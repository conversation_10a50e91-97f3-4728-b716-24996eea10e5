package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.trade.req.ThirdActivityRecordDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import com.holderzone.saas.store.trade.repository.feign.ThirdActivityClientService;
import com.holderzone.saas.store.trade.service.IThirdActivityRecordService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.GrouponCalculateUtil;
import com.holderzone.saas.store.trade.utils.caculate.PriceCalculationUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年07月14日 15:52
 * @description 团购代金券处理
 */
@Slf4j
@Component
@AllArgsConstructor
public class GrouponVoucherDiscountHandler extends DiscountHandler {

    private final IThirdActivityRecordService thirdActivityRecordService;

    private final ThirdActivityClientService thirdActivityClientService;

    /**
     * 团购验券-代金券
     * 先团购,再第三方
     */
    @Override
    void dealDiscount(DiscountContext context) {
        DineinOrderDetailRespDTO orderDetailRespDTO = context.getOrderDetailRespDTO();
        Map<Integer, DiscountDO> discountTypeMap = context.getDiscountTypeMap();
        List<DineInItemDTO> allItems = context.getAllItems();
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = context.getDiscountFeeDetailDTOS();
        // 美团团购
        GrouponCalculateUtil.handleGroupon(orderDetailRespDTO, false, discountTypeMap, allItems,
                discountFeeDetailDTOS, GroupBuyTypeEnum.MEI_TUAN, type());
        // 抖音团购
        GrouponCalculateUtil.handleGroupon(orderDetailRespDTO, false, discountTypeMap, allItems,
                discountFeeDetailDTOS, GroupBuyTypeEnum.DOU_YIN, DiscountTypeEnum.DOU_YIN_GROUPON.getCode());
        // 支付宝团购
        GrouponCalculateUtil.handleGroupon(orderDetailRespDTO, false, discountTypeMap, allItems,
                discountFeeDetailDTOS, GroupBuyTypeEnum.ALIPAY, DiscountTypeEnum.ALIPAY_GROUPON.getCode());
        // 农行团购
        GrouponCalculateUtil.handleGroupon(orderDetailRespDTO, false, discountTypeMap, allItems,
                discountFeeDetailDTOS, GroupBuyTypeEnum.ABC, DiscountTypeEnum.ABC_GROUPON.getCode());
        // 第三方平台活动
        handleThirdActivity(context);
    }

    /**
     * 第三方活动处理
     */
    private void handleThirdActivity(DiscountContext context) {
        String orderGuid = String.valueOf(context.getOrderDO().getGuid());
        DineinOrderDetailRespDTO orderDetailRespDTO = context.getOrderDetailRespDTO();
        Map<Integer, DiscountDO> discountTypeMap = context.getDiscountTypeMap();
        List<DineInItemDTO> allItems = context.getAllItems();
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = context.getDiscountFeeDetailDTOS();

        Map<Integer, DiscountFeeDetailDTO> feeDetailDTOMap = discountFeeDetailDTOS.stream()
                .collect(Collectors.toMap(DiscountFeeDetailDTO::getDiscountType, Function.identity(), (v1, v2) -> v1));
        DiscountFeeDetailDTO thirdActivityDiscount = feeDetailDTOMap.get(DiscountTypeEnum.THIRD_ACTIVITY.getCode());
        boolean isNotExist = ObjectUtils.isEmpty(thirdActivityDiscount);
        if (isNotExist) {
            DiscountDO grouponDiscountDO = discountTypeMap.get(DiscountTypeEnum.THIRD_ACTIVITY.getCode());
            thirdActivityDiscount = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(grouponDiscountDO);
            thirdActivityDiscount.setDiscountFee(BigDecimal.ZERO);
        }

        List<ThirdActivityRecordDTO> thirdActivityRecordDTOList = getThirdActivityRecordDTOList(orderDetailRespDTO, orderGuid);

        orderDetailRespDTO.setThirdActivityMaxFee(orderDetailRespDTO.getOrderSurplusFee());
        BigDecimal discountFee = calculateThirdActivity(orderDetailRespDTO, thirdActivityRecordDTOList);
        context.setThirdActivityDiscountFee(discountFee);
        if (BigDecimalUtil.greaterThanZero(discountFee)) {
            thirdActivityDiscount.setDiscountFee(discountFee);
            orderDetailRespDTO.setOrderSurplusFee(orderDetailRespDTO.getOrderSurplusFee().subtract(discountFee));
            orderDetailRespDTO.setGrouponFee(orderDetailRespDTO.getGrouponFee().add(discountFee));
            // 第三方活动的优惠按比例设置商品的折扣总价
            AmountCalculationUtil.dealWithAllItems(allItems, discountFee);
            // 商品实付金额分摊
            Map<String, BigDecimal> itemDiscountPriceMap = PriceCalculationUtils.calculationItemDiscountPrice(discountFee, allItems);
            log.info("订单上第三方活动优惠分摊:{}", JacksonUtils.writeValueAsString(itemDiscountPriceMap));
            Map<String, DineInItemDTO> itemDTOMap = allItems.stream()
                    .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
            // 更新商品实付金额
            itemDiscountPriceMap.forEach((guid, itemDiscountFee) -> {
                DineInItemDTO dineInItemDTO = itemDTOMap.get(guid);
                if (!ObjectUtils.isEmpty(dineInItemDTO)) {
                    dineInItemDTO.setDiscountTotalPrice(dineInItemDTO.getDiscountTotalPrice().subtract(itemDiscountFee));
                }
            });
            log.info("[团购非套餐券]第三方活动总扣减金额：{}，订单剩余金额：{}", thirdActivityDiscount.getDiscountFee(),
                    orderDetailRespDTO.getOrderSurplusFee());
        }
        if (isNotExist) {
            discountFeeDetailDTOS.add(thirdActivityDiscount);
        }
    }

    private List<ThirdActivityRecordDTO> getThirdActivityRecordDTOList(DineinOrderDetailRespDTO orderDetailRespDTO, String orderGuid) {
        boolean isDiscountActivity = true;
        List<ThirdActivityRecordDTO> thirdActivityRecordDTOList = thirdActivityRecordService.listThirdActivityByOrderGuid(orderGuid);
        log.info("[Voucher]订单下使用的活动={}", JacksonUtils.writeValueAsString(thirdActivityRecordDTOList));
        if (!CollectionUtils.isEmpty(thirdActivityRecordDTOList)) {
            List<String> guidList = thirdActivityRecordDTOList.stream()
                    .map(ThirdActivityRecordDTO::getActivityGuid)
                    .collect(Collectors.toList());
            List<ThirdActivityRespDTO> thirdActivityList = thirdActivityClientService.inProcessListByGuid(guidList);
            log.info("[Voucher]可用活动列表={}", JacksonUtils.writeValueAsString(thirdActivityList));
            if (!CollectionUtils.isEmpty(thirdActivityList)) {
                Map<String, ThirdActivityRespDTO> activityMap = thirdActivityList.stream()
                        .collect(Collectors.toMap(ThirdActivityRespDTO::getGuid, Function.identity(), (dto1, dto2) -> dto2));
                for (ThirdActivityRecordDTO rec : thirdActivityRecordDTOList) {
                    ThirdActivityRespDTO thirdActivityDTO = activityMap.get(rec.getActivityGuid());
                    if (ObjectUtils.isEmpty(thirdActivityDTO)) {
                        continue;
                    }
                    AmountCalculationUtil.handleActivityDeductionFee(rec, thirdActivityDTO);
                    if (0 == thirdActivityDTO.getIsActivityShare() && isDiscountActivity) {
                        isDiscountActivity = false;
                    }
                }
            }
        }
        orderDetailRespDTO.setIsDiscountActivity(isDiscountActivity);
        return thirdActivityRecordDTOList;
    }

    private static BigDecimal calculateThirdActivity(DineinOrderDetailRespDTO orderDetailRespDTO,
                                                     List<ThirdActivityRecordDTO> thirdActivityRecordDTOList) {
        List<GrouponListRespDTO> grouponDTOList = orderDetailRespDTO.getGrouponListRespDTOS();
        // 未绑定
        Set<String> activityGuidSet = grouponDTOList.stream()
                .map(GrouponListRespDTO::getActivityGuid)
                .filter(a -> !StringUtils.isEmpty(a))
                .collect(Collectors.toSet());
        return thirdActivityRecordDTOList.stream()
                .filter(t -> !activityGuidSet.contains(t.getActivityGuid()))
                .map(ThirdActivityRecordDTO::getDeductionFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.GROUPON.getCode();
    }
}
