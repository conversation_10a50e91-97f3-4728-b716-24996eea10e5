package com.holderzone.saas.store.trade.entity.read;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> R
 * @date 2020/11/26 18:12
 * @description
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class OrderReadDetailsDO {
    /**
     * 全局唯一主键
     */
    private Long guid;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
    /**
     * 结算时间
     */
    private LocalDateTime checkoutTime;
    /**
     * 桌台名称(区域+桌台code)
     */
    private String diningTableName;
    /**
     * 订单号(前端显示用，门店内唯一，格式************)
     */
    private String orderNo;
    /**
     * 订单金额（商品总额+附加费）
     */
    private BigDecimal orderFee;
    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 服务员Guid
     */
    private String waiterGuid;
    /**
     *      服务员类型
     *      CALL_WAITER(1, "喊客员"),
     *     SERVE_WAITER(2, "服务员"),
     *     PASS_DISHES_WAITER(3, "传菜员"),
     *     TIDY_WAITER(4, "收台员"),
     *     MOP_WAITER(5, "洗碗员");
     */
    private Integer waiterType;
    /**
     * 服务员名称
     */
    private String waiterName;
    /**
     * 服务员编号
     */
    private String waiterNo;
    /**
     * 订单类型（0：正餐 1：快餐）
     */
    private Integer tradeMode;
}
