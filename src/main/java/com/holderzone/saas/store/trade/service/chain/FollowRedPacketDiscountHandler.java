package com.holderzone.saas.store.trade.service.chain;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.member.pay.RedPacketAmountFeeQuery;
import com.holderzone.saas.store.dto.member.pay.RedPacketOrderDTO;
import com.holderzone.saas.store.dto.member.pay.RedPacketSettleAccountsVO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import com.holderzone.saas.store.trade.entity.enums.RecoveryTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.repository.interfaces.DiscountService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.HttpUtil;
import com.holderzone.saas.store.trade.utils.RedisKeyUtil;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2023-06-26
 * @description
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class FollowRedPacketDiscountHandler extends DiscountHandler{

    @Value("${member.center.hostUrl}")
    private String memberCenterHostUrl;

    private final RedisHelper redisHelper;

    private final DiscountService discountService;
    @Override
    void dealDiscount(DiscountContext context) {
        // 16.随行红包优惠（营销活动）
        BigDecimal followRedPacketFee = BigDecimal.ZERO;
        if (Objects.equals(RecoveryTypeEnum.GENERAL.getCode(), context.getOrderDO().getRecoveryType())) {
            log.info("计算获取随行红包优惠信息 orderGuid={}", context.getOrderDO().getGuid());
            RedPacketAmountFeeQuery query = new RedPacketAmountFeeQuery();
            query.setOperSubjectGuid(UserContextUtils.get().getOperSubjectGuid());
            List<RedPacketOrderDTO> orderDTOList = new ArrayList<>();
            if (Objects.equals(UpperStateEnum.GENERAL.getCode(), context.getOrderDO().getUpperState())) {
                RedPacketOrderDTO redPacketOrderDTO = new RedPacketOrderDTO();
                redPacketOrderDTO.setOrderNumber(String.valueOf(context.getOrderDO().getGuid()));
                redPacketOrderDTO.setOrderAmount(context.getOrderDO().getOrderFee());
                orderDTOList.add(redPacketOrderDTO);
            } else {
                // 主单
                RedPacketOrderDTO redPacketOrderDTO = new RedPacketOrderDTO();
                redPacketOrderDTO.setOrderNumber(String.valueOf(context.getOrderDO().getGuid()));
                redPacketOrderDTO.setOrderAmount(context.getOrderDO().getOrderFee());
                orderDTOList.add(redPacketOrderDTO);
                // 子单
                List<RedPacketOrderDTO> list = new ArrayList<>();
                context.getSubOrderDOS().forEach(sub -> {
                    RedPacketOrderDTO packetOrderDTO = new RedPacketOrderDTO();
                    packetOrderDTO.setOrderNumber(String.valueOf(sub.getGuid()));
                    packetOrderDTO.setOrderAmount(sub.getOrderFee());
                    list.add(packetOrderDTO);
                });
                orderDTOList.addAll(list);
            }
            query.setOrderNumbers(orderDTOList);
            String result = "";
            try {
                String httpRequestUrl = String.format("%s/hsa_follow/red_packet_settle_accounts", memberCenterHostUrl).intern();
                log.info("计算获取随行红包优惠信息 httpRequestUrl={}", httpRequestUrl);
                Map<String, String> headerMap = new HashMap<>();
                headerMap.put("source", "2");
                result = HttpUtil.doPostJsonHeader(httpRequestUrl, JacksonUtils.writeValueAsString(query), headerMap);
                log.info("计算获取随行红包优惠信息 result={}", result);
            } catch (IOException e) {
                log.error("计算获取随行红包优惠信息异常", e);
            }

            if (StringUtils.isNotBlank(result)) {
                followRedPacketFee = dealResult(followRedPacketFee,result,context);
            }
        } else {
            DiscountDO followRedPacketDiscount = discountService.getFollowRedPacketDiscount(context.getOrderDetailRespDTO().getGuid());
            if (!ObjectUtils.isEmpty(followRedPacketDiscount)) {
                followRedPacketFee = followRedPacketDiscount.getDiscountFee();
            }
        }
        followRedPacketFee = dealWithDiscountFee(context.getOrderDetailRespDTO().getOrderSurplusFee(), followRedPacketFee);
        log.info("实际随行红包优惠金额={}", followRedPacketFee);
        buildFollowRedPacketFee(context,followRedPacketFee);

    }

    private void buildFollowRedPacketFee(DiscountContext context,BigDecimal followRedPacketFee) {
        if (!BigDecimalUtil.greaterThanZero(followRedPacketFee)) {
            return;
        }
        context.getOrderDetailRespDTO().setFollowRedPacketFee(followRedPacketFee);
        context.getOrderDetailRespDTO().setIsFollowRedPacket(context.getOrderDO().getIsFollowRedPacket());
        DiscountFeeDetailDTO redPacket = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(context.getDiscountTypeMap().get
                (type()));
        if (context.getOrderDO().getIsFollowRedPacket()) {
            redPacket.setDiscountFee(followRedPacketFee);
            context.getOrderDetailRespDTO().setOrderSurplusFee(context.getOrderDetailRespDTO().getOrderSurplusFee().subtract(
                    redPacket.getDiscountFee()));
        } else {
            redPacket.setDiscountFee(BigDecimal.ZERO);
        }
        context.getOrderDetailRespDTO().setOrderSurplusFee(context.getOrderDetailRespDTO().getOrderSurplusFee().subtract(
                redPacket.getDiscountFee()));
        log.info("16.随行红包优惠（营销活动）计算后订单剩余金额：{}，优惠金额：{}", context.getOrderDetailRespDTO().getOrderSurplusFee(),
                redPacket.getDiscountFee());
        context.getDiscountFeeDetailDTOS().add(redPacket);

    }

    private BigDecimal dealResult(BigDecimal followRedPacketFee, String result, DiscountContext context) {
        JSONObject resultJson = JSONObject.parseObject(result);
        String data = resultJson.getString("data");
        if (StringUtils.isBlank(data)) {
            return followRedPacketFee;
        }
        List<RedPacketSettleAccountsVO> redPacketList = JSONObject.parseArray(data, RedPacketSettleAccountsVO.class);
        if (CollectionUtils.isEmpty(redPacketList)) {
            return followRedPacketFee;
        }
        // 随行红包优惠信息存到redis
        String redPacketInfoKey = RedisKeyUtil.getRedPacketInfoKey(String.valueOf(context.getOrderDO().getGuid()));
        if (redisHelper.hasKey(redPacketInfoKey)) {
            redisHelper.delete(redPacketInfoKey);
        }
        redisHelper.setEx(redPacketInfoKey, data, 3, TimeUnit.DAYS);

        followRedPacketFee = redPacketList.stream()
                .map(RedPacketSettleAccountsVO::getRedPacketSettleAccount)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        log.info("计算获取到的随行红包优惠金额={}", followRedPacketFee);

        return followRedPacketFee;
    }

    @Override
    Integer type() {
        return DiscountTypeEnum.FOLLOW_RED_PACKET.getCode();
    }
}
