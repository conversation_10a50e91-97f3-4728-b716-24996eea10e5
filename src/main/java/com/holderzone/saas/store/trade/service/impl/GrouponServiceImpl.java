package com.holderzone.saas.store.trade.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityRespDTO;
import com.holderzone.saas.store.dto.order.request.groupon.GrouponReqDTO;
import com.holderzone.saas.store.dto.order.request.groupon.GrouponSingleDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponTradeDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import com.holderzone.saas.store.dto.trade.GrouponOrderDTO;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import com.holderzone.saas.store.trade.entity.domain.GrouponDO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import com.holderzone.saas.store.trade.entity.domain.ThirdActivityRecordDO;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.mapper.OrderMapper;
import com.holderzone.saas.store.trade.repository.feign.GroupBuyClientService;
import com.holderzone.saas.store.trade.repository.feign.GroupClientService;
import com.holderzone.saas.store.trade.repository.feign.ThirdActivityClientService;
import com.holderzone.saas.store.trade.repository.interfaces.DiscountService;
import com.holderzone.saas.store.trade.repository.interfaces.GrouponMpService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderItemService;
import com.holderzone.saas.store.trade.service.GrouponService;
import com.holderzone.saas.store.trade.repository.interfaces.GrouponTradeDetailService;
import com.holderzone.saas.store.trade.service.IThirdActivityRecordService;
import com.holderzone.saas.store.trade.service.converter.GrouponConverter;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GrouponServiceImpl
 * @date 2019/01/28 17:28
 * @description
 * @program holder-saas-store-trade
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GrouponServiceImpl implements GrouponService {

    private final DynamicHelper dynamicHelper;

    private final DiscountService discountService;

    private final GrouponMpService grouponMpService;

    private final GrouponTradeDetailService grouponTradeDetailService;

    private final OrderMapper orderMapper;

    private final IThirdActivityRecordService thirdActivityRecordService;

    private final OrderItemService orderItemService;

    private final GroupBuyClientService groupBuyClientService;

    private final GroupClientService groupClientService;

    private final ThirdActivityClientService thirdActivityClientService;

    private OrderTransform orderTransform = OrderTransform.INSTANCE;

    private static final ScheduledExecutorService EXECUTOR_SERVICE = Executors.newScheduledThreadPool(10, new ThreadFactoryBuilder()
            .setNameFormat("团购结算信息异步线程-%d")
            .build());

    @Override
    public List<GrouponListRespDTO> listByOrderGuid(String orderGuid, Integer grouponType) {
        List<GrouponDO> grouponList = grouponMpService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isEmpty(grouponList)) {
            return Lists.newArrayList();
        }
        if (Objects.nonNull(grouponType)) {
            grouponList = grouponList.stream()
                    .filter(e -> Objects.nonNull(e.getGrouponType()) && e.getGrouponType().equals(grouponType))
                    .collect(Collectors.toList());
            if (GroupBuyTypeEnum.MEI_TUAN.getCode() == grouponType || GroupBuyTypeEnum.DOU_YIN.getCode() == grouponType
                    || GroupBuyTypeEnum.ALIPAY.getCode() == grouponType) {
                // 查询第三方活动是否存在
                Set<String> activityGuidSet = grouponList.stream()
                        .map(GrouponDO::getActivityGuid)
                        .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(activityGuidSet)) {
                    List<ThirdActivityRespDTO> activityList = thirdActivityClientService.inProcessListByGuid(new ArrayList<>(activityGuidSet));
                    log.info("当前生效的第三方活动列表:{}", JacksonUtils.writeValueAsString(activityList));
                    List<String> activityGuids = activityList.stream().map(ThirdActivityRespDTO::getGuid).collect(Collectors.toList());
                    grouponList.forEach(e -> {
                        if (StringUtils.isNotBlank(e.getActivityGuid()) && !activityGuids.contains(e.getActivityGuid())) {
                            e.setActivityGuid(null);
                        }
                    });
                }
                grouponList = grouponList.stream()
                        .filter(e -> StringUtils.isEmpty(e.getActivityGuid()))
                        .collect(Collectors.toList());
            }
        }
        log.info("订单中使用的券码:{}", JacksonUtils.writeValueAsString(grouponList));
        return orderTransform.orderDOListGrouponListRespDTOList(grouponList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<GrouponListRespDTO> verify(GrouponReqDTO grouponReqDTO) {
        // 校验参数
        checkParam(grouponReqDTO);
        // 校验订单
        checkOrder(grouponReqDTO);
        // 校验第三方活动记录
        thirdActivityRecordService.checkRecord(grouponReqDTO);
        // 构建验券请求参数
        CouPonReqDTO couPonReqDTO = buildVerifyCouponParam(grouponReqDTO);
        List<GroupVerifyDTO> groupVerifyResult = groupClientService.verifyCoupon(couPonReqDTO);
        log.info("订单{}验券成功返回结果:{}", grouponReqDTO.getOrderGuid(), JacksonUtils.writeValueAsString(groupVerifyResult));
        if (CollectionUtils.isEmpty(groupVerifyResult)) {
            return Lists.newArrayList();
        }
        // 成功的集合
        List<String> successCodes = groupVerifyResult.stream().map(GroupVerifyDTO::getCode).collect(Collectors.toList());
        Map<String, GrouponSingleDTO> singleGrouponMap = Maps.newHashMap();
        if(CollUtil.isNotEmpty((grouponReqDTO.getGrouponList()))){
            singleGrouponMap = grouponReqDTO.getGrouponList()
                    .stream()
                    .collect(Collectors.toMap(GrouponSingleDTO::getCouponCode, Function.identity(), (key1, key2) -> key2));
        }
        // 更新成功的验券列表
        grouponReqDTO.setCouponCodeList(successCodes);
        grouponReqDTO.setDeductionAmount(grouponReqDTO.getDealValue());
        List<GrouponReqDTO> grouponReqDTOList = Lists.newArrayList();
        for (GroupVerifyDTO groupVerifyDTO : groupVerifyResult) {
            GrouponReqDTO innerGrouponReqDTO = new GrouponReqDTO();
            BeanUtils.copyProperties(grouponReqDTO, innerGrouponReqDTO);
            if (StringUtils.isNotEmpty(groupVerifyDTO.getVerifyCode())) {
                //抖音券需分别设置每张券的购买金额等
                resetCouponInfo(innerGrouponReqDTO, singleGrouponMap.get(groupVerifyDTO.getVerifyCode()));
            }
            innerGrouponReqDTO.setCouponCode(groupVerifyDTO.getCode());
            innerGrouponReqDTO.setVerifyId(groupVerifyDTO.getVerifyId());
            innerGrouponReqDTO.setCertificateId(groupVerifyDTO.getCertificateId());
            innerGrouponReqDTO.setReceiptChannel(groupVerifyDTO.getReceiptChannel());
            innerGrouponReqDTO.setMaitonConsumeDTO(groupVerifyDTO.getMaitonConsumeDTO());
            grouponReqDTOList.add(innerGrouponReqDTO);
        }
        // 保存到 groupon表
        saveBatchGroupon(grouponReqDTOList);
        // 处理discount表
        discountService.handleDiscount(grouponReqDTO.getOrderGuid(), grouponReqDTO.getGroupBuyType(), grouponReqDTO.getDeductionAmount());
        // 保存第三方活动记录
        thirdActivityRecordService.saveRecord(grouponReqDTO);
        // 查询db
        List<GrouponDO> grouponList = grouponMpService.listByOrderGuid(grouponReqDTO.getOrderGuid());
        // 异步更新团购结算信息
        createTradeDetail(couPonReqDTO, groupVerifyResult);
        return orderTransform.grouponDOS2GrouponListRespDTOS(grouponList);
    }

    private void resetCouponInfo(GrouponReqDTO innerGrouponReqDTO, GrouponSingleDTO grouponSingleDTO) {
        if(grouponSingleDTO == null || grouponSingleDTO.getCouponCode() == null){
            return;
        }
        innerGrouponReqDTO.setCouponBuyPrice(grouponSingleDTO.getCouponBuyPrice());
        innerGrouponReqDTO.setDealValue(grouponSingleDTO.getDealValue());
    }

    /**
     * 校验订单
     */
    private void checkOrder(GrouponReqDTO grouponReqDTO) {
        // 如果是并台情况且该guid为子桌id，替换为主桌guid
        String orderGuid = grouponReqDTO.getOrderGuid();
        OrderDO orderDO = orderMapper.selectOne(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getGuid, orderGuid)
                .eq(OrderDO::getIsDelete, Boolean.FALSE)
        );
        if (Objects.isNull(orderDO)) {
            throw new BusinessException("订单不存在，请刷新后再试");
        }
        if (StateEnum.SUCCESS.getCode() == orderDO.getState()) {
            throw new BusinessException("订单已支付，请刷新订单后再试");
        }
        if (Objects.equals(UpperStateEnum.SUB.getCode(), orderDO.getUpperState())) {
            orderGuid = String.valueOf(orderDO.getMainOrderGuid());
        }
        grouponReqDTO.setOrderGuid(orderGuid);
        grouponReqDTO.setOrderFee(orderDO.getOrderFee());
    }

    /**
     * 校验请求参数
     */
    private void checkParam(GrouponReqDTO grouponReqDTO) {
        if (Objects.isNull(grouponReqDTO.getDealId())) {
            throw new BusinessException("项目ID不能为空");
        }
        if (Objects.isNull(grouponReqDTO.getDealTitle())) {
            throw new BusinessException("项目标题不能为空");
        }
        if (Objects.isNull(grouponReqDTO.getDealValue())) {
            throw new BusinessException("券面值不能为空");
        }
        if (Objects.isNull(grouponReqDTO.getCouponType())) {
            throw new BusinessException("券类型不能为空");
        }
    }

    private CouPonReqDTO buildVerifyCouponParam(GrouponReqDTO grouponReqDTO) {
        CouPonReqDTO couPonReqDTO = new CouPonReqDTO();
        couPonReqDTO.setCount(grouponReqDTO.getCount());
        couPonReqDTO.setCouponCode(grouponReqDTO.getCouponCode());
        couPonReqDTO.setCouponCodeList(grouponReqDTO.getCouponCodeList());
        couPonReqDTO.setErpId(UserContextUtils.getStoreGuid());
        couPonReqDTO.setErpName(UserContextUtils.getStoreName());
        couPonReqDTO.setErpOrderId(grouponReqDTO.getOrderGuid());
        couPonReqDTO.setGroupBuyType(grouponReqDTO.getGroupBuyType());
        couPonReqDTO.setOrderId(grouponReqDTO.getOrderId());
        couPonReqDTO.setUserId(grouponReqDTO.getUserId());
        couPonReqDTO.setOrderFee(grouponReqDTO.getOrderFee());
        return couPonReqDTO;
    }

    @Override
    public void saveBatchGroupon(List<GrouponReqDTO> grouponReqDTOList) {
        List<GrouponDO> grouponList = Lists.newArrayList();
        for (GrouponReqDTO grouponReqDTO : grouponReqDTOList) {
            GrouponDO grouponDO = new GrouponDO();
            grouponDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HST_GROUPON));
            grouponDO.setAmount(grouponReqDTO.getDealValue());
            grouponDO.setCode(grouponReqDTO.getCouponCode());
            if (grouponReqDTO.getDealTitle().length() <= 49) {
                grouponDO.setName(grouponReqDTO.getDealTitle());
            } else {
                grouponDO.setName(grouponReqDTO.getDealTitle().substring(0, 49));
            }
            grouponDO.setUserId(grouponReqDTO.getUserId());
            grouponDO.setOrderGuid(Long.valueOf(grouponReqDTO.getOrderGuid()));
            grouponDO.setStaffGuid(UserContextUtils.getUserGuid());
            grouponDO.setStaffName(UserContextUtils.getUserName());
            grouponDO.setStoreGuid(UserContextUtils.getStoreGuid());
            grouponDO.setStoreName(UserContextUtils.getStoreName());
            grouponDO.setThirdCode(String.valueOf(grouponReqDTO.getDealId()));
            grouponDO.setGrouponType(grouponReqDTO.getGroupBuyType());
            grouponDO.setCouponType(grouponReqDTO.getCouponType());
            grouponDO.setCouponBuyPrice(grouponReqDTO.getCouponBuyPrice());
            grouponDO.setDeductionAmount(Objects.nonNull(grouponReqDTO.getDeductionAmount()) ?
                    grouponReqDTO.getDeductionAmount() : grouponReqDTO.getDealValue());
            grouponDO.setActivityGuid(grouponReqDTO.getActivityGuid());
            grouponDO.setVerifyId(grouponReqDTO.getVerifyId());
            grouponDO.setCertificateId(grouponReqDTO.getCertificateId());
            grouponDO.setReceiptChannel(grouponReqDTO.getReceiptChannel());
            grouponDO.setMaitonConsumeInfo(Objects.nonNull(grouponReqDTO.getMaitonConsumeDTO()) ?
                    JacksonUtils.writeValueAsString(grouponReqDTO.getMaitonConsumeDTO()) : null);
            grouponList.add(grouponDO);
        }
        grouponMpService.saveBatch(grouponList);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokeBatchOrder(List<String> orderGuids) {
        log.info("并台撤销团购券入参:{}", JacksonUtils.writeValueAsString(orderGuids));
        if (CollectionUtils.isEmpty(orderGuids)) {
            return;
        }
        for (String orderGuid : orderGuids) {
            // 查询订单验券列表
            List<GrouponDO> grouponList = grouponMpService.listByOrderGuid(orderGuid);
            for (GrouponDO groupon : grouponList) {
                GrouponReqDTO innerRevokeReq = new GrouponReqDTO();
                innerRevokeReq.setCouponCode(groupon.getCode());
                innerRevokeReq.setOrderGuid(orderGuid);
                try {
                    //撤销平台的团购券
                    GrouponDO grouponDO = revokeGroupon(innerRevokeReq.getCouponCode(), orderGuid);
                    if (ObjectUtil.isNotNull(grouponDO)) {
                        innerRevokeReq.setActivityGuid(grouponDO.getActivityGuid());
                        // 若是套餐券需要清空订单商品表上的团购字段
                        orderItemService.clearGroupBuyFlag(Long.valueOf(orderGuid), grouponDO.getCode());
                        //若存在第三方活动
                        revokeThirdActivity(innerRevokeReq, orderGuid, grouponDO);
                    }
                } catch (Exception e) {
                    log.error("撤销券失败,orderGuid:{}, 券码:{}, e:{}", orderGuid, innerRevokeReq.getCouponCode(), e.getMessage());
                }
            }
        }
        // 最后再清除一次第三方活动记录表
        thirdActivityRecordService.revokeThirdActivityRecord(orderGuids);
    }

    /**
     * 撤销团购券
     *
     * @param revokeReq 撤销参数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void revoke(GrouponReqDTO revokeReq) {
        log.info("撤销团购券入参:{}", JacksonUtils.writeValueAsString(revokeReq));
        //是否是并台
        String orderGuid = checkCombineOrder(revokeReq.getOrderGuid());
        //撤销平台的团购券
        GrouponDO grouponDO = revokeGroupon(revokeReq.getCouponCode(), orderGuid);
        if (ObjectUtil.isNotNull(grouponDO)) {
            revokeReq.setActivityGuid(grouponDO.getActivityGuid());
        }
        if (StringUtils.isEmpty(revokeReq.getActivityGuid())) {
            return;
        }
        // 若是套餐券需要清空订单商品表上的团购字段
        orderItemService.clearGroupBuyFlag(Long.valueOf(orderGuid), grouponDO.getCode());
        //若存在第三方活动
        revokeThirdActivity(revokeReq, orderGuid, grouponDO);
    }

    @Override
    public GrouponDO revokeGroupon(String couponCode, String orderGuid) {
        List<GrouponDO> grouponDOList = grouponMpService.listByCodeAndOrderGuid(couponCode, orderGuid);
        if (!org.springframework.util.CollectionUtils.isEmpty(grouponDOList)) {
            grouponDOList.forEach(grouponDO -> {
                if (grouponDO != null) {
                    //去撤销团购券
                    GroupVerifyDTO groupVerifyDTO = GrouponConverter.fromGroupRecord(grouponDO);
                    log.info("调用第三方撤销验券参数:{}", JacksonUtils.writeValueAsString(groupVerifyDTO));
                    MtDelCouponRespDTO mtDelCouponRespDTO = groupClientService.revokeCoupon(groupVerifyDTO);
                    if (Objects.isNull(mtDelCouponRespDTO)) {
                        throw new BusinessException("撤销验券失败");
                    }
                    if (mtDelCouponRespDTO.getResult() != 0) {
                        throw new BusinessException(mtDelCouponRespDTO.getMessage());
                    }
                    //撤销已验券记录
                    clearGroupBuyRecord(grouponDO);
                }
            });
        }
        cancelTradeDetail(orderGuid, couponCode);
        return grouponDOList.get(0);
    }

    private void clearGroupBuyRecord(GrouponDO grouponDO) {
        grouponMpService.removeById(grouponDO.getGuid());
    }

    private void revokeThirdActivity(GrouponReqDTO grouponReq, String orderGuid, GrouponDO grouponDO) {
        // 撤销第三方活动
        ThirdActivityRecordDO thirdActivityRecordDO = thirdActivityRecordService.getOne(new LambdaQueryWrapper<ThirdActivityRecordDO>()
                .eq(ThirdActivityRecordDO::getOrderGuid, orderGuid)
                .eq(ThirdActivityRecordDO::getActivityGuid, grouponReq.getActivityGuid())
                .eq(ThirdActivityRecordDO::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (ObjectUtils.isEmpty(thirdActivityRecordDO)) {
            log.warn("订单下没有第三方活动,orderGuid={},activityGuid={}", orderGuid, grouponReq.getActivityGuid());
            return;
        }
        if (!Objects.equals(0, thirdActivityRecordDO.getRuleType())) {
            log.warn("第三方活动类型不对,thirdActivityRecordDO={}", JacksonUtils.writeValueAsString(thirdActivityRecordDO));
            return;
        }
        List<String> codeList = new ArrayList<>(Arrays.asList(thirdActivityRecordDO.getThirdActivityCodes().split(",")));
        codeList.removeIf(code -> Objects.equals(grouponReq.getCouponCode(), code));
        if (CollectionUtils.isEmpty(codeList)) {
            thirdActivityRecordService.removeById(thirdActivityRecordDO);
        } else {
            String codesStr = codeList.stream().map(String::valueOf).collect(Collectors.joining(","));
            thirdActivityRecordDO.setThirdActivityCodes(codesStr);
            thirdActivityRecordService.updateById(thirdActivityRecordDO);
        }
        List<ThirdActivityRespDTO> thirdActivityList = thirdActivityClientService.listByGuid(Collections.singletonList(grouponReq.getActivityGuid()));
        log.info("处理折扣信息,thirdActivityList={}", JacksonUtils.writeValueAsString(thirdActivityList));
        if (CollectionUtils.isEmpty(thirdActivityList)) {
            log.warn("未查询到第三方活动,activityGuid={}", grouponReq.getActivityGuid());
            throw new BusinessException("未查询到第三方活动");
        }
        // 处理折扣信息
        DiscountDO grouponDiscount = discountService.getThirdActivityDiscount(orderGuid);
        if (ObjectUtils.isEmpty(grouponDiscount)) {
            return;
        }
        BigDecimal discountFee = grouponDiscount.getDiscountFee();
        if (BigDecimal.ZERO.equals(discountFee)) {
            return;
        }

        ThirdActivityRespDTO thirdActivityDTO = thirdActivityList.get(0);
        BigDecimal couponFee = thirdActivityDTO.getCouponFee();
        // 第三方抵扣面值可能为空
        if (ObjectUtil.isNotNull(grouponDO) && couponFee == null) {
            couponFee = grouponDO.getDeductionAmount();
        }

        discountFee = discountFee.subtract(BigDecimalUtil.nonNullValue(couponFee));
        if (BigDecimalUtil.lessThanZero(discountFee)) {
            discountFee = BigDecimal.ZERO;
        }
        grouponDiscount.setDiscountFee(discountFee);
        discountService.updateById(grouponDiscount);
        // 若是套餐券需要清空订单商品表上的团购字段
        orderItemService.clearGroupBuyFlag(Long.valueOf(orderGuid), grouponReq.getCouponCode());
    }

    private String checkCombineOrder(String orderGuid) {
        // 如果是并台情况且该guid为子桌id，替换为主桌guid
        OrderDO orderDO = orderMapper.selectOne(new LambdaQueryWrapper<OrderDO>()
                .eq(OrderDO::getGuid, orderGuid)
                .eq(OrderDO::getIsDelete, Boolean.FALSE)
        );
        if (!ObjectUtils.isEmpty(orderDO) && Objects.equals(UpperStateEnum.SUB.getCode(), orderDO.getUpperState())) {
            orderGuid = String.valueOf(orderDO.getMainOrderGuid());
        }
        return orderGuid;
    }

    @Override
    public void createTradeDetail(CouPonReqDTO grouponReqDTO, List<GroupVerifyDTO> results) {
        // 目前只对接了美团团购结算明细
        if (GroupBuyTypeEnum.MEI_TUAN.getCode() != grouponReqDTO.getGroupBuyType()) {
            return;
        }
        // 更新验券券码集合
        updateCouPonReqCouponCodeList(grouponReqDTO, results);
        CouPonReqDTO couPonReqDTO = new CouPonReqDTO();
        BeanUtils.copyProperties(grouponReqDTO, couPonReqDTO);
        String jsonStr = UserContextUtils.getJsonStr();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        EXECUTOR_SERVICE.schedule(() -> {
            // 切换数据源
            UserContextUtils.put(jsonStr);
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            // 如果是单张
            if (CollectionUtils.isEmpty(couPonReqDTO.getCouponCodeList())) {
                couPonReqDTO.setCouponCodeList(Lists.newArrayList(couPonReqDTO.getCouponCode()));
            }
            couPonReqDTO.setReceiptChannel(results.get(0).getReceiptChannel());
            log.info("当前UserContext:{}", jsonStr);
            for (String couponCode : couPonReqDTO.getCouponCodeList()) {
                couPonReqDTO.setCouponCode(couponCode);
                MtCouponTradeDetailRespDTO mtCouponTradeDetailRespDTO = groupBuyClientService.queryGroupTradeDetail(couPonReqDTO);
                log.info("查询团购结算信息:{}", JacksonUtils.writeValueAsString(mtCouponTradeDetailRespDTO));
                grouponTradeDetailService.create(mtCouponTradeDetailRespDTO);
            }
        }, 10, TimeUnit.SECONDS);
    }


    /**
     * 更新验券券码集合
     */
    private void updateCouPonReqCouponCodeList(CouPonReqDTO couPonReqDTO, List<GroupVerifyDTO> results) {
        if (CollectionUtils.isEmpty(results)) {
            return;
        }
        List<String> couponCodeList = results.stream().map(GroupVerifyDTO::getCode)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        couPonReqDTO.setCouponCodeList(couponCodeList);
    }

    @Override
    public void cancelTradeDetail(String orderGuid, String couponCode) {
        String jsonStr = UserContextUtils.getJsonStr();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        EXECUTOR_SERVICE.schedule(() -> {
            // 切换数据源
            UserContextUtils.put(jsonStr);
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            log.info("撤销团购结算信息,券码：{}", couponCode);
            grouponTradeDetailService.cancel(orderGuid, couponCode);
        }, 10, TimeUnit.SECONDS);
    }

    @Override
    public void handleTradeDetailHistory(String enterpriseGuid, Long guid) {
        dynamicHelper.changeDatasource(enterpriseGuid);
        UserContextUtils.putErp(enterpriseGuid);
        List<GrouponDO> groupons = grouponMpService.listForGeGuid(guid);
        if (CollectionUtils.isEmpty(groupons)) {
            return;
        }
        log.info("groupons条数:{}", groupons.size());
        for (GrouponDO groupon : groupons) {
            CouPonReqDTO couPonReqDTO = new CouPonReqDTO();
            couPonReqDTO.setCouponCode(groupon.getCode());
            couPonReqDTO.setErpId(groupon.getStoreGuid());
            couPonReqDTO.setErpName(groupon.getStoreName());
            couPonReqDTO.setStoreGuid(groupon.getStoreGuid());
            couPonReqDTO.setErpOrderId(String.valueOf(groupon.getOrderGuid()));
            MtCouponTradeDetailRespDTO mtCouponTradeDetailRespDTO = groupBuyClientService.queryGroupTradeDetail(couPonReqDTO);
            grouponTradeDetailService.create(mtCouponTradeDetailRespDTO);
        }
        log.info("groupons执行完成:{}", groupons.size());
    }

    @Override
    public List<String> hasThirdActivityOrderGuids(List<String> orderGuids) {
        log.info("批量查询订单使用第三方平台活动入参:{}", JacksonUtils.writeValueAsString(orderGuids));
        if (CollectionUtils.isEmpty(orderGuids)) {
            return Lists.newArrayList();
        }
        List<String> hasThirdActivityOrderGuids = Lists.newArrayList();
        // 查询团购验券使用记录
        List<GrouponDO> grouponList = grouponMpService.listByOrderGuids(orderGuids);
        List<String> grouponOrderGuids = grouponList.stream()
                .map(e -> String.valueOf(e.getOrderGuid()))
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(grouponOrderGuids)) {
            hasThirdActivityOrderGuids.addAll(grouponOrderGuids);
        }
        // 查询第三方平台活动记录
        List<ThirdActivityRecordDO> thirdActivityRecordList = thirdActivityRecordService.listByOrderGuids(orderGuids);
        List<String> thirdActivityOrderGuids = thirdActivityRecordList.stream()
                .map(ThirdActivityRecordDO::getOrderGuid)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(thirdActivityOrderGuids)) {
            hasThirdActivityOrderGuids.addAll(thirdActivityOrderGuids);
        }
        return hasThirdActivityOrderGuids;
    }

    @Override
    public List<GrouponOrderDTO> useGrouponTotalAmountByOrderGuids(List<String> orderGuids) {
        return grouponMpService.useGrouponTotalAmountByOrderGuids(orderGuids);
    }

}
