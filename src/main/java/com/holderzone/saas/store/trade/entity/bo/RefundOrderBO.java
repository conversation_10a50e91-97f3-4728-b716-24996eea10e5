package com.holderzone.saas.store.trade.entity.bo;

import com.holderzone.saas.store.trade.entity.domain.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 订单退款 业务对象
 */
@Data
public class RefundOrderBO implements Serializable {

    private static final long serialVersionUID = -2142460175962519494L;

    /**
     * 原单
     */
    private OrderDO originalOrder;

    /**
     * 退款单
     */
    private OrderDO order;

    /**
     * 退款单扩展
     */
    private OrderExtendsDO orderExtends;

    /**
     * 退款单商品明细
     */
    private List<OrderItemDO> orderItemList;

    /**
     * 退款单商品扩展明细
     */
    private List<OrderItemExtendsDO> orderItemExtendsList;

    /**
     * 退款单商品换菜明细
     */
    private List<OrderItemChangesDO> orderItemChangesList;

    /**
     * 退款单记录
     */
    private List<OrderItemRecordDO> orderItemRecordList;

    /**
     * 附加费明细
     */
    private List<AppendFeeDO> appendFeeList;

    /**
     * 赠菜明细
     */
    private List<FreeReturnItemDO> freeItemList;

    /**
     * 退款退菜明细
     */
    private List<FreeReturnItemDO> refundItemList;

    /**
     * 属性明细
     */
    private List<ItemAttrDO> itemAttrList;

    /**
     * 换菜属性明细
     */
    private List<ItemAttrDO> changeItemAttrList;

    /**
     * 退款单交易明细
     */
    private List<TransactionRecordDO> transactionRecordList;

    /**
     * 退款单多笔聚合支付交易明细
     */
    private List<MultipleTransactionRecordDO> multipleTransactionRecordList;

    /**
     * 退款记录
     */
    private OrderRefundRecordDO orderRefundRecord;

    /**
     * 获取退款单的guid
     */
    public Long getRefundOrderGuid() {
        return this.order.getGuid();
    }

    /**
     * 获取退款单的营业日时间
     */
    public LocalDate getRefundBusinessDay() {
        return this.order.getBusinessDay();
    }

}
