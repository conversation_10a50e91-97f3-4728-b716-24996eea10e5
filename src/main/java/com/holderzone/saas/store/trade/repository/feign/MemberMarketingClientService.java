package com.holderzone.saas.store.trade.repository.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.response.MemberPortrayalDetailsDTO;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ConsumptionGiftDetailDTO;
import com.holderzone.holder.saas.member.wechat.dto.activitie.MemberConsumptionGiftDTO;
import com.holderzone.saas.store.dto.marketing.specials.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 新会员 营销中心
 **/
@Component
@FeignClient(name = "holder-member-marketing", fallbackFactory = MemberMarketingClientService.ServiceFallBack.class, url = "${member.marketing.host}")
public interface MemberMarketingClientService {

    /**
     * 计算消费赠送处理
     * @param memberConsumptionGiftQO memberConsumptionGiftQO
     */
    @PostMapping("/marketing/consumption/activity/deal/consumption/gift")
    void dealConsumptionGift(@RequestBody MemberConsumptionGiftDTO memberConsumptionGiftQO);

    /**
     * 查询订单优惠
     * @param orderNumber
     * @return
     */
    @GetMapping("/marketing/consumption/activity/get/order/gift")
    ConsumptionGiftDetailDTO getOrderGift(@RequestParam("orderNumber") String orderNumber);

    /**
     * 撤销送礼
     * @param orderNumber 订单号
     */
    @GetMapping("/marketing/consumption/activity/revocation/consumption/gift")
    void revocationConsumptionGift(@RequestParam(value = "orderNumber") String orderNumber);

    /**
     * 营销活动下单
     */
    @PostMapping("/marketing/unite/activity/pay")
    void uniteActivityOrderPay(@RequestBody UniteActivityOrderPayDTO payDTO);

    /**
     * 营销活动退款
     */
    @PostMapping("/marketing/unite/activity/refund")
    void uniteActivityOrderRefund(@RequestBody UniteActivityOrderRefundDTO refundDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MemberMarketingClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public MemberMarketingClientService create(Throwable throwable) {

            return new MemberMarketingClientService() {
                @Override
                public void dealConsumptionGift(MemberConsumptionGiftDTO memberConsumptionGiftQO) {
                    log.info(HYSTRIX_PATTERN, "方法：dealConsumptionGift， 调用会员营销服务失败, 请求入参，volumeCode:{}, throwable :{}"
                            , JacksonUtils.writeValueAsString(memberConsumptionGiftQO), throwable);
                    throw new BusinessException("计算消费有礼赠送处理失败");
                }

                @Override
                public ConsumptionGiftDetailDTO getOrderGift(String orderNumber) {
                    log.info(HYSTRIX_PATTERN, "方法：getOrderGift， 调用会员营销服务失败, 请求入参，volumeCode:{}, throwable :{}"
                            , orderNumber, throwable);
                    throw new BusinessException("查询订单优惠处理失败");
                }

                @Override
                public void revocationConsumptionGift(String orderNumber) {
                    log.info(HYSTRIX_PATTERN, "方法：revocationConsumptionGift， 调用会员营销服务失败, 请求入参，volumeCode:{}, throwable :{}"
                            , orderNumber, throwable);
                    throw new BusinessException("撤销送礼处理失败");
                }

                @Override
                public void uniteActivityOrderPay(UniteActivityOrderPayDTO payDTO) {
                    log.info(HYSTRIX_PATTERN, "方法：uniteActivityOrderPay， 调用会员营销服务失败, 请求入参，reqDTO:{}, throwable :{}"
                            , JacksonUtils.writeValueAsString(payDTO), throwable);
                    throw new BusinessException("营销活动下单失败");
                }

                @Override
                public void uniteActivityOrderRefund(UniteActivityOrderRefundDTO refundDTO) {
                    log.info(HYSTRIX_PATTERN, "方法：uniteActivityOrderRefund， 调用会员营销服务失败, 请求入参，reqDTO:{}, throwable :{}"
                            , JacksonUtils.writeValueAsString(refundDTO), throwable);
                    throw new BusinessException("营销活动退款失败");
                }

            };
        }
    }
}
