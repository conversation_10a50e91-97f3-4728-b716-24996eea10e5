package com.holderzone.saas.store.trade.repository.impls;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityDetailsRespDTO;
import com.holderzone.saas.store.dto.journaling.req.SaleCountReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SaleCountRespDTO;
import com.holderzone.saas.store.dto.order.request.item.TransferItemReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderItemQuery;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderItemRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.OrderItemChangeFlagEnum;
import com.holderzone.saas.store.enums.order.OrderStateEnum;
import com.holderzone.saas.store.trade.entity.constant.OrderRedisConstant;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.mapper.OrderItemMapper;
import com.holderzone.saas.store.trade.mapper.OrderMapper;
import com.holderzone.saas.store.trade.mapper.PadOrderMapper;
import com.holderzone.saas.store.trade.repository.feign.ThirdActivityClientService;
import com.holderzone.saas.store.trade.repository.interfaces.*;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.ItemUtil;
import com.holderzone.saas.store.trade.utils.RedisKeyUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 订单商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderItemServiceImpl extends ServiceImpl<OrderItemMapper, OrderItemDO> implements OrderItemService {

    private final RedisHelper redisHelper;

    private final OrderMapper orderMapper;

    private final PadOrderMapper padOrderMapper;

    private final TransactionRecordService transactionRecordService;

    private final ItemAttrService itemAttrService;

    private final ThirdActivityClientService thirdActivityClientService;

    private final DiscountService discountService;

    private final OrderItemExtendsService orderItemExtendsService;

    @Override
    public List<OrderItemDO> listByOrderGuid(Long orderGuid) {
        return list(new LambdaQueryWrapper<OrderItemDO>().eq(OrderItemDO::getOrderGuid, orderGuid).orderByDesc
                (OrderItemDO::getGmtCreate));

    }

    @Override
    public List<OrderItemDO> listByOrderGuidWithOutIsDel(Long orderGuid) {
        return list(new LambdaQueryWrapper<OrderItemDO>().eq(OrderItemDO::getOrderGuid, orderGuid)
                .eq(OrderItemDO::getIsDelete, false)
                .orderByDesc(OrderItemDO::getGmtCreate));

    }

    /**
     * 从缓存获取订单商品
     *
     * @param orderGuid  订单guid
     * @param deviceType 订单来源，pad有特殊处理，可能为空
     * @return 订单商品
     */
    @Override
    public List<OrderItemDO> listByOrderGuidWithCache(Long orderGuid, Integer deviceType) {
        String redisKey = RedisKeyUtil.getHstOrderItemKey(String.valueOf(orderGuid));
        Boolean hasKey = redisHelper.hasKey(redisKey);
        if (hasKey) {
            List<OrderItemDO> orderItemDOS = JSON.parseArray(redisHelper.get(redisKey), OrderItemDO.class);
            log.info("获取菜品信息-缓存获取：{}", orderItemDOS);
            return orderItemDOS;
        }
        List<OrderItemDO> orderItemDOS = list(new LambdaQueryWrapper<OrderItemDO>()
                .eq(OrderItemDO::getIsDelete, false)
                .eq(OrderItemDO::getOrderGuid, orderGuid)
                .orderByDesc(OrderItemDO::getGmtCreate).orderByAsc(OrderItemDO::getGuid));
        log.info("OrderItemDOdb查询日志，orderGuid={},orderItemDOGuidList={}", orderGuid,
                orderItemDOS.stream().map(OrderItemDO::getGuid).collect(Collectors.toList()));
        if (CollectionUtil.isEmpty(orderItemDOS)) {
            return Collections.emptyList();
        }
        if (deviceType != null && Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), deviceType)) {
            // pad点餐
            orderItemDOS = padOrderHandler(orderGuid, orderItemDOS);
        }
        if (CollectionUtils.isEmpty(orderItemDOS)) {
            return orderItemDOS;
        }
        // 查询菜品扩展表
        List<Long> orderItemGuidList = orderItemDOS.stream().map(OrderItemDO::getGuid).collect(Collectors.toList());
        List<OrderItemExtendsDO> orderItemExtendsList = orderItemExtendsService.listByIds(orderItemGuidList);
        Map<Long, OrderItemExtendsDO> orderItemExtendsMap = orderItemExtendsList.stream()
                .collect(Collectors.toMap(OrderItemExtendsDO::getGuid, Function.identity(), (key1, key2) -> key2));
        for (OrderItemDO orderItemDO : orderItemDOS) {
            OrderItemExtendsDO orderItemExtendsDO = orderItemExtendsMap.get(orderItemDO.getGuid());
            orderItemDO.setChangeFlag(OrderItemChangeFlagEnum.UN_CHANGE.getCode());
            if (Objects.isNull(orderItemExtendsDO)) {
                continue;
            }
            if (Objects.equals(orderItemExtendsDO.getChangeFlag(), OrderItemChangeFlagEnum.CHANGE.getCode())) {
                orderItemDO.setChangeFlag(OrderItemChangeFlagEnum.CHANGE.getCode());
            }
            orderItemDO.setIsKitchen(orderItemExtendsDO.getIsKitchen());
            orderItemDO.setTransferFlag(orderItemExtendsDO.getTransferFlag());
            orderItemDO.setRefundPrice(orderItemExtendsDO.getRefundPrice());
        }
        log.info("获取菜品信息-db获取：{}", orderItemDOS);
        // 设置缓存
        redisHelper.setEx(redisKey, JSON.toJSONString(orderItemDOS), OrderRedisConstant.ORDER_MINUTES_TIMEOUT,
                TimeUnit.MINUTES);
        return orderItemDOS;
    }

    private List<OrderItemDO> padOrderHandler(Long orderGuid, List<OrderItemDO> orderItemDOS) {
        List<String> padOrderGuidList = orderItemDOS.stream().map(OrderItemDO::getPadOrderGuid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(padOrderGuidList)) {
            log.warn("pad下单数据为空 orderGuid={}", orderGuid);
            return Collections.emptyList();
        }
        List<PadOrderDO> padOrderDOList = padOrderMapper.selectList(new LambdaQueryWrapper<PadOrderDO>()
                .in(PadOrderDO::getGuid, padOrderGuidList)
                .eq(PadOrderDO::getOrderState, OrderStateEnum.ACCEPT_ORDER.getCode())
                .eq(PadOrderDO::getIsDelete, BooleanEnum.FALSE.getCode()));
        log.info("PadOrderDOdb查询,Q-padOrderGuidList={},R-padOrderDOGuidList={}", padOrderGuidList,
                padOrderDOList.stream().map(PadOrderDO::getGuid).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(padOrderDOList)) {
            log.warn("pad下单数据为空 orderGuid={}", orderGuid);
            return Collections.emptyList();
        }
        // 通过主单id查询所有的订单信息
        List<String> combineGuidList = padOrderDOList.stream()
                .map(PadOrderDO::getCombineOrderGuid)
                .filter(s -> !ObjectUtils.isEmpty(s))
                .distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(combineGuidList)) {
            // 主单的所有子单信息
            List<PadOrderDO> combinePadOrderDOList = padOrderMapper.selectList(new LambdaQueryWrapper<PadOrderDO>()
                    .in(PadOrderDO::getCombineOrderGuid, combineGuidList)
                    .eq(PadOrderDO::getOrderState, OrderStateEnum.ACCEPT_ORDER.getCode())
                    .eq(PadOrderDO::getIsDelete, BooleanEnum.FALSE.getCode())
            );
            if (CollectionUtils.isEmpty(combinePadOrderDOList)) {
                log.error("异常情况：主单存在，子单不存在 combineGuidList={}", combineGuidList);
                return Collections.emptyList();
            }
            padOrderDOList.clear();
            padOrderDOList.addAll(combinePadOrderDOList);
        }
        List<Long> padOrderAllGuidList = padOrderDOList.stream()
                .map(PadOrderDO::getGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(padOrderAllGuidList)) {
            log.error("没有pad订单 padOrderGuidList={}", padOrderGuidList);
            return Collections.emptyList();
        }
        orderItemDOS.removeIf(item -> !padOrderAllGuidList.contains(Long.parseLong(item.getPadOrderGuid())));
        return orderItemDOS;
    }

    @Override
    public boolean saveBatchWithDeleteCache(Collection<OrderItemDO> entityList, String orderGuid) {
        String redisKey = RedisKeyUtil.getHstOrderItemKey(orderGuid);
        boolean update = saveBatch(entityList);
        log.info("保存商品并删除缓存，redisKey={}，saveBatch={}", redisKey, update);
        if (update) {
            redisHelper.delete(redisKey);
        }
        return update;
    }

    @Override
    public boolean updateBatchByIdWithDeleteCache(Collection<OrderItemDO> entityList, String orderGuid) {
        boolean update = updateBatchById(entityList);
        if (update) {
            //有并台反结账时，子单的商品缓存无法被清除，改用orderItem中的orderGuid作为redisKey来清除缓存（Bug16953）
            entityList.forEach(v -> {
                String redisKey = RedisKeyUtil.getHstOrderItemKey(String.valueOf(v.getOrderGuid()));
                if (redisHelper.hasKey(redisKey)) {
                    redisHelper.delete(redisKey);
                }
            });
        }
        return update;
    }

    @Override
    public boolean removeByIdsWithDeleteCache(Collection<? extends Serializable> idList, String orderGuid) {
        if (CollectionUtil.isEmpty(idList)) {
            return Boolean.TRUE;
        }
        String redisKey = RedisKeyUtil.getHstOrderItemKey(orderGuid);
        boolean update = removeByIds(idList);
        if (update) {
            redisHelper.delete(redisKey);
        }
        return update;
    }

    @Override
    public List<OrderItemDO> listByOrderGuidWithOutIsPay(Long orderGuid) {
        return list(new LambdaQueryWrapper<OrderItemDO>().eq(OrderItemDO::getOrderGuid, orderGuid).ne
                (OrderItemDO::getIsPay, 1));
    }

    @Override
    public List<OrderItemDO> listByOrderGuidWithOutSub(Long orderGuid) {
        return list(new LambdaQueryWrapper<OrderItemDO>().eq(OrderItemDO::getOrderGuid, orderGuid).eq
                (OrderItemDO::getParentItemGuid, 0));
    }

    @Override
    public Boolean hasCurrentItem(Long orderGuid) {
        LambdaQueryWrapper<OrderItemDO> queryWrapper = new LambdaQueryWrapper<OrderItemDO>()
                .eq(OrderItemDO::getOrderGuid, orderGuid).eq(OrderItemDO::getParentItemGuid, 0);
        queryWrapper.and(i -> i.gt(OrderItemDO::getCurrentCount, 0).or().gt(OrderItemDO::getFreeCount, 0));
        List<OrderItemDO> orderItemDOS = list(queryWrapper);
        return !CollectionUtil.isEmpty(orderItemDOS);
    }

    @Override
    public Boolean hasItem(Long orderGuid) {
        LambdaQueryWrapper<OrderItemDO> queryWrapper = new LambdaQueryWrapper<OrderItemDO>()
                .eq(OrderItemDO::getOrderGuid, orderGuid);
        int count = count(queryWrapper);
        return count > 0;
    }

    @Override
    public List<OrderItemDO> listByMainItemGuid(Long itemGuid) {
        return list(new LambdaQueryWrapper<OrderItemDO>()
                .eq(OrderItemDO::getParentItemGuid, itemGuid));
    }

    @Override
    public List<OrderItemDO> listByMainItemGuids(Collection<? extends Serializable> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<OrderItemDO>()
                .in(OrderItemDO::getParentItemGuid, idList));
    }

    @Override
    public void deleteByOrderGuid(String orderGuid) {
        remove(new LambdaQueryWrapper<OrderItemDO>().eq(OrderItemDO::getOrderGuid, orderGuid).ne
                (OrderItemDO::getIsPay, 1));
    }

    @Override
    public void deleteByOrderGuids(List<String> orderGuids) {
        if (CollectionUtil.isEmpty(orderGuids)) {
            return;
        }
        remove(new LambdaQueryWrapper<OrderItemDO>().in(OrderItemDO::getOrderGuid, orderGuids));
    }

    @Override
    public List<OrderItemDO> listByIdsWithLock(Collection<? extends Serializable> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<OrderItemDO>().in(OrderItemDO::getGuid, idList).last("for update"));
    }

    @Override
    public List<OrderItemDO> listByOrderLists(List<String> orderLists) {
        if (CollectionUtil.isEmpty(orderLists)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<OrderItemDO>().in(OrderItemDO::getOrderGuid, orderLists));
    }

    /**
     * 调整单-查询订单商品
     * 标记商品是否调整过
     *
     * @param query 订单guid，门店，交易模式
     * @return 订单商品信息列表
     */
    @Override
    public AdjustByOrderRespDTO listOrderItem(AdjustByOrderItemQuery query) {
        String orderGuid = query.getOrderGuid();
        if (StringUtils.isEmpty(orderGuid)) {
            throw new BusinessException("订单guid不能为空");
        }

        // 订单信息
        OrderDO orderDO = orderMapper.selectById(orderGuid);
        if (ObjectUtils.isEmpty(orderDO)) {
            log.warn("未查询到订单信息 orderGuid={}", orderGuid);
            throw new BusinessException("订单不存在");
        }
        AdjustByOrderRespDTO adjustByOrderRespDTO = OrderTransform.INSTANCE.orderDO2DineAdjustOrder(orderDO);

        // 第三方活动处理
        List<DiscountDO> discountDOS = discountService.listByOrderGuid(orderGuid);
        if (CollectionUtils.isNotEmpty(discountDOS)) {
            Map<Integer, DiscountDO> discountTypeMap = CollectionUtil.toMap(discountDOS, "discountType");
            DiscountDO thirdActivityDiscount = discountTypeMap.get(DiscountTypeEnum.THIRD_ACTIVITY.getCode());
            if (!ObjectUtils.isEmpty(thirdActivityDiscount) &&
                    BigDecimalUtil.greaterThanZero(thirdActivityDiscount.getDiscountFee())) {
                List<ThirdActivityDetailsRespDTO> detailList = thirdActivityClientService.detailByOrderGuid(orderGuid);
                adjustByOrderRespDTO.setDetailList(detailList);
                adjustByOrderRespDTO.setActuallyPayFee(adjustByOrderRespDTO.getActuallyPayFee()
                        .add(thirdActivityDiscount.getDiscountFee())
                        .subtract(adjustByOrderRespDTO.getExcessAmount()));
            }
        }


        // 订单商品信息
        String mainOrderGuid = orderGuid;
        if (orderDO.getUpperState().equals(UpperStateEnum.SUB.getCode())) {
            mainOrderGuid = String.valueOf(orderDO.getMainOrderGuid());
        }
        List<OrderDO> orderDOList = orderMapper.selectList(new LambdaQueryWrapper<OrderDO>()
                .select(OrderDO::getGuid)
                .eq(OrderDO::getMainOrderGuid, mainOrderGuid)
                .eq(OrderDO::getIsDelete, Boolean.FALSE)
                .eq(OrderDO::getUpperState, UpperStateEnum.SUB.getCode()));
        List<Long> guidList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(orderDOList)) {
            guidList = orderDOList.stream().map(OrderDO::getGuid).collect(Collectors.toList());
        }
        guidList.add(Long.valueOf(orderGuid));

        List<OrderItemDO> orderItemDOList = this.list(new LambdaQueryWrapper<OrderItemDO>()
                .eq(OrderItemDO::getIsDelete, Boolean.FALSE)
                .in(OrderItemDO::getOrderGuid, guidList));

        if (!CollectionUtils.isEmpty(orderItemDOList)) {
            // 属性信息
            List<ItemAttrDO> itemAttrDOList = itemAttrService.findByOrderGuid(orderGuid);
            Map<Long, List<ItemAttrDO>> orderItemAttrMap = itemAttrDOList.stream()
                    .collect(Collectors.groupingBy(ItemAttrDO::getOrderItemGuid));
            Map<Long, List<OrderItemDO>> subItemMap = orderItemDOList.stream()
                    .collect(Collectors.groupingBy(OrderItemDO::getParentItemGuid));

            List<AdjustByOrderItemRespDTO> itemList = new ArrayList<>();
            for (OrderItemDO item : orderItemDOList) {
                // 跳过套餐的子商品
                if (!ObjectUtils.isEmpty(item.getParentItemGuid()) && !Objects.equals(0L, item.getParentItemGuid())) {
                    continue;
                }

                // 结账前已经退菜的商品，调整单选择商品弹窗中，不显示
                BigDecimal count = item.getCurrentCount().add(item.getFreeCount());
                if (count.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                //套餐
                BigDecimal attrTotal = BigDecimal.ZERO;
                BigDecimal addPriceTotal = BigDecimal.ZERO;
                if (ItemUtil.isGroup(item.getItemType())) {
                    List<OrderItemDO> subOrderItemDOS = subItemMap.get(item.getGuid());
                    for (OrderItemDO subOrderItemDO : subOrderItemDOS) {
                        if (BigDecimalUtil.greaterThanZero(subOrderItemDO.getAddPrice())) {
                            addPriceTotal = addPriceTotal.add(subOrderItemDO.getAddPrice()
                                    .multiply(subOrderItemDO.getCurrentCount()));
                        }

                        if (!ObjectUtils.isEmpty(subOrderItemDO.getHasAttr()) && Objects.equals(1, subOrderItemDO.getHasAttr())) {
                            List<ItemAttrDO> itemAttrDOS = orderItemAttrMap.get(subOrderItemDO.getGuid());
                            if (CollectionUtil.isEmpty(itemAttrDOS)) {
                                log.warn("未查询到属性，这里应该有属性 itemGuid={}", subOrderItemDO.getGuid());
                                continue;
                            }
                            for (ItemAttrDO itemAttrDO : itemAttrDOS) {
                                BigDecimal subAttrTotal = itemAttrDO.getAttrPrice().multiply(subOrderItemDO.getCurrentCount());
                                // 处理套餐称重
                                if (!subOrderItemDO.getItemType().equals(ItemTypeEnum.WEIGH.getCode())) {
                                    subAttrTotal = subAttrTotal.multiply(subOrderItemDO.getPackageDefaultCount());
                                }
                                attrTotal = attrTotal.add(subAttrTotal);
                            }
                        }
                    }
                }

                // 属性加价
                if (!ObjectUtils.isEmpty(item.getHasAttr()) && Objects.equals(1, item.getHasAttr())) {
                    List<ItemAttrDO> itemAttrDOS = orderItemAttrMap.get(item.getGuid());
                    if (CollectionUtil.isNotEmpty(itemAttrDOS)) {
                        attrTotal = attrTotal.add(itemAttrDOS.stream()
                                .map(ItemAttrDO::getAttrPrice)
                                .reduce(BigDecimal::add)
                                .orElse(BigDecimal.ZERO));
                    }
                }

                //计算普通菜品价格小计
                BigDecimal currentPrice = item.getPrice().multiply(count);
                // 商品小计
                BigDecimal totalPrice;
                if (Objects.equals(ItemTypeEnum.GROUP.getCode(), item.getItemType())) {
                    totalPrice = (item.getPrice().add(attrTotal.add(addPriceTotal)).multiply(count));
                } else {
                    if (BigDecimalUtil.greaterThanZero(currentPrice) || BigDecimalUtil.equelZero(currentPrice)) {
                        totalPrice = BigDecimalUtil.setScale2(currentPrice.add(attrTotal.multiply(count)));
                    } else {
                        totalPrice = BigDecimal.ZERO;
                    }
                }

                AdjustByOrderItemRespDTO adjustByOrderItemRespDTO = new AdjustByOrderItemRespDTO();
                if (item.getGuid() != null) {
                    adjustByOrderItemRespDTO.setOrderItemGuid(String.valueOf(item.getGuid()));
                }
                adjustByOrderItemRespDTO.setItemGuid(item.getItemGuid());
                adjustByOrderItemRespDTO.setItemName(item.getItemName());
                adjustByOrderItemRespDTO.setItemType(item.getItemType());
                adjustByOrderItemRespDTO.setSkuGuid(item.getSkuGuid());
                adjustByOrderItemRespDTO.setSkuName(item.getSkuName());
                adjustByOrderItemRespDTO.setPrice(item.getPrice());
                adjustByOrderItemRespDTO.setCurrentCount(count);
                adjustByOrderItemRespDTO.setUnit(item.getUnit());
                adjustByOrderItemRespDTO.setIsAdjustItem(item.getIsAdjustItem());
                adjustByOrderItemRespDTO.setOtherAddPrice(attrTotal.add(addPriceTotal));
                adjustByOrderItemRespDTO.setTotalPrice(totalPrice);
                itemList.add(adjustByOrderItemRespDTO);

            }
            adjustByOrderRespDTO.setOrderItemList(itemList);
        }

        // 支付方式信息
        List<TransactionRecordDO> transactionRecordDOS = transactionRecordService.listByOrderGuid(orderGuid);
        if (!CollectionUtils.isEmpty(transactionRecordDOS)) {
            List<ActuallyPayFeeDetailDTO> list = OrderTransform.INSTANCE.transactionRecordDOS2ActuallyPayFeeDetailDTOS(
                    transactionRecordDOS);
            adjustByOrderRespDTO.setPayDetailList(list);
        }
        return adjustByOrderRespDTO;
    }

    @Override
    public void updateBatchIsAdjustItemIsTrue(List<Long> guids) {
        if (CollectionUtils.isEmpty(guids)) {
            return;
        }
        baseMapper.updateBatchIsAdjustItemIsTrue(guids);
    }

    @Override
    public void clearGroupBuyFlag(Long orderGuid, String couponCode) {
        if (Objects.isNull(orderGuid) || StringUtils.isEmpty(couponCode)) {
            return;
        }
        List<OrderItemDO> orderItemDOS = list(new LambdaQueryWrapper<OrderItemDO>()
                .eq(OrderItemDO::getOrderGuid, orderGuid)
                .eq(OrderItemDO::getCouponCode, couponCode)
                .eq(OrderItemDO::getIsDelete, Boolean.FALSE)
        );
        if (CollectionUtils.isNotEmpty(orderItemDOS)) {
            this.revokeGroupon(orderItemDOS);
        }
        baseMapper.clearGroupBuyFlag(orderGuid, couponCode);
        // 清除订单明细缓存
        String redisKey = RedisKeyUtil.getHstOrderItemKey(String.valueOf(orderGuid));
        redisHelper.delete(redisKey);
    }

    @Override
    public void batchClearGroupBuyFlag(Long orderGuid, List<String> orderItemGuids) {
        if (CollectionUtils.isEmpty(orderItemGuids)) {
            return;
        }
        List<OrderItemDO> orderItemDOS = list(new LambdaQueryWrapper<OrderItemDO>()
                .eq(OrderItemDO::getOrderGuid, orderGuid)
                .in(OrderItemDO::getGuid, orderItemGuids)
                .eq(OrderItemDO::getIsDelete, Boolean.FALSE)
        );
        List<OrderItemDO> couponOrderItemDOS = orderItemDOS.stream()
                .filter(orderItemDO -> StringUtils.isEmpty(orderItemDO.getCouponCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(couponOrderItemDOS)) {
            this.revokeGroupon(orderItemDOS);
        }
        baseMapper.batchClearGroupBuyFlag(orderItemGuids);
        // 清除订单明细缓存
        String redisKey = RedisKeyUtil.getHstOrderItemKey(String.valueOf(orderGuid));
        redisHelper.delete(redisKey);
    }

    // 验券加购撤销用券后将价格改为原价
    private void revokeGroupon(List<OrderItemDO> orderItemDOS) {
        List<Long> orderItemGuidList = orderItemDOS.stream()
                .map(OrderItemDO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        // key:orderItemGuid
        Map<Long, OrderItemExtendsDO> orderItemExtendsMap = orderItemExtendsService.list(new LambdaQueryWrapper<OrderItemExtendsDO>()
                        .in(OrderItemExtendsDO::getGuid, orderItemGuidList)
                        .eq(OrderItemExtendsDO::getIsDelete, Boolean.FALSE)
                        .eq(OrderItemExtendsDO::getIsUseCouponPrice, 1))
                .stream()
                .collect(Collectors.toMap(OrderItemExtendsDO::getGuid, Function.identity(), (oldValue, newValue) -> newValue));
        if (MapUtils.isEmpty(orderItemExtendsMap)) {
            return;
        }
        Set<OrderItemDO> updateOrderItems = new HashSet<>();
        Set<OrderItemExtendsDO> updateOrderItemExtends = new HashSet<>();
        Set<ItemAttrDO> updateItemAttrs = new HashSet<>();
        // key:orderItemGuid
        Map<Long, List<ItemAttrDO>> itemAttrDOMap = itemAttrService.list(new LambdaQueryWrapper<ItemAttrDO>()
                        .in(ItemAttrDO::getOrderItemGuid, orderItemGuidList)
                        .eq(ItemAttrDO::getIsDelete, Boolean.FALSE))
                .stream()
                .collect(Collectors.groupingBy(ItemAttrDO::getOrderItemGuid));
        for (OrderItemDO orderItemDO : orderItemDOS) {
            OrderItemExtendsDO orderItemExtendsDO = orderItemExtendsMap.get(orderItemDO.getGuid());
            if (Objects.isNull(orderItemExtendsDO)) {
                continue;
            }
            if (Objects.nonNull(orderItemExtendsDO.getBeforeCouponPrice())
                || Objects.nonNull(orderItemExtendsDO.getBeforeCouponAddPrice())) {
                if (Objects.nonNull(orderItemExtendsDO.getBeforeCouponPrice())) {
                    orderItemDO.setPrice(orderItemExtendsDO.getBeforeCouponPrice());
                    orderItemExtendsDO.setBeforeCouponPrice(null);
                }
                if (Objects.nonNull(orderItemExtendsDO.getBeforeCouponAddPrice())) {
                    orderItemDO.setAddPrice(orderItemExtendsDO.getBeforeCouponAddPrice());
                    orderItemExtendsDO.setBeforeCouponAddPrice(null);
                }
                orderItemExtendsDO.setIsUseCouponPrice(Boolean.FALSE);
                updateOrderItemExtends.add(orderItemExtendsDO);
                updateOrderItems.add(orderItemDO);
            }
            if (CollectionUtils.isNotEmpty(itemAttrDOMap.get(orderItemDO.getGuid()))) {
                for (ItemAttrDO itemAttrDO : itemAttrDOMap.get(orderItemDO.getGuid())) {
                    if (Objects.nonNull(itemAttrDO.getBeforeCouponAttrPrice())) {
                        itemAttrDO.setAttrPrice(itemAttrDO.getBeforeCouponAttrPrice());
                        itemAttrDO.setBeforeCouponAttrPrice(null);
                        updateItemAttrs.add(itemAttrDO);
                    }
                }
            }
            // 套餐处理
            orderPackageSubgroupHandler(orderItemDO, updateOrderItems, updateOrderItemExtends, updateItemAttrs);
        }
        if (CollectionUtils.isNotEmpty(updateOrderItems)) {
            updateBatchById(updateOrderItems);
        }
        if (CollectionUtils.isNotEmpty(updateItemAttrs)) {
            itemAttrService.updateBatchById(updateItemAttrs);
        }
        if (CollectionUtils.isNotEmpty(updateOrderItemExtends)) {
            orderItemExtendsService.updateBatchById(updateOrderItemExtends);
        }
    }

    private void orderPackageSubgroupHandler(OrderItemDO orderItemDO, Set <OrderItemDO> updateOrderItems,
                                             Set<OrderItemExtendsDO> updateOrderItemExtends,
                                             Set<ItemAttrDO> updateItemAttrs) {
        List<OrderItemDO> subOrderItemDOS = list(new LambdaQueryWrapper<OrderItemDO>()
                .in(OrderItemDO::getParentItemGuid, orderItemDO.getGuid())
                .eq(OrderItemDO::getIsDelete, Boolean.FALSE)
        );
        if (CollectionUtils.isEmpty(subOrderItemDOS)) {
            return;
        }
        List<Long> subOrderItemGuidList = subOrderItemDOS.stream()
                .map(OrderItemDO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        // key:orderItemGuid
        Map<Long, OrderItemExtendsDO> subOrderItemExtendsMap = orderItemExtendsService.list(new LambdaQueryWrapper<OrderItemExtendsDO>()
                        .in(OrderItemExtendsDO::getGuid, subOrderItemGuidList)
                        .eq(OrderItemExtendsDO::getIsDelete, Boolean.FALSE))
                .stream()
                .collect(Collectors.toMap(OrderItemExtendsDO::getGuid, Function.identity(), (oldValue, newValue) -> newValue));
        // key:orderItemGuid
        Map<Long, List<ItemAttrDO>> itemAttrMap = itemAttrService.list(new LambdaQueryWrapper<ItemAttrDO>()
                        .in(ItemAttrDO::getOrderItemGuid, subOrderItemGuidList)
                        .eq(ItemAttrDO::getIsDelete, Boolean.FALSE)).stream()
                .collect(Collectors.groupingBy(ItemAttrDO::getOrderItemGuid));
        BigDecimal mainOrderItemAttrTotal = orderItemDO.getAttrTotal();
        for (OrderItemDO subOrderItemDO : subOrderItemDOS) {
            BigDecimal subOrderItemAttrTotal = subOrderItemDO.getAttrTotal();
            List<ItemAttrDO> itemAttrDOList = itemAttrMap.get(subOrderItemDO.getGuid());
            if (CollectionUtils.isNotEmpty(itemAttrDOList)) {
                for (ItemAttrDO itemAttrDO : itemAttrDOList) {
                    if (Objects.nonNull(itemAttrDO.getBeforeCouponAttrPrice())) {
                        itemAttrDO.setAttrPrice(itemAttrDO.getBeforeCouponAttrPrice());
                        itemAttrDO.setBeforeCouponAttrPrice(null);
                        subOrderItemAttrTotal = subOrderItemAttrTotal.add(itemAttrDO.getAttrPrice());
                        updateItemAttrs.add(itemAttrDO);
                    }
                }
            }
            OrderItemExtendsDO subOrderItemExtendsDO = subOrderItemExtendsMap.get(subOrderItemDO.getGuid());
            if (Objects.nonNull(subOrderItemExtendsDO) && Objects.nonNull(subOrderItemExtendsDO.getBeforeCouponAddPrice())) {
                subOrderItemDO.setAddPrice(subOrderItemExtendsDO.getBeforeCouponAddPrice());
                subOrderItemExtendsDO.setBeforeCouponAddPrice(null);
                updateOrderItemExtends.add(subOrderItemExtendsDO);
            }
            subOrderItemDO.setAttrTotal(subOrderItemAttrTotal);
            updateOrderItems.add(subOrderItemDO);
            // 属性数量
            BigDecimal subAttrNum = subOrderItemDO.getCurrentCount();
            if (!subOrderItemDO.getItemType().equals(ItemTypeEnum.WEIGH.getCode()) &&
                    BigDecimalUtil.greaterThanZero(subOrderItemDO.getPackageDefaultCount())) {
                subAttrNum = subAttrNum.multiply(subOrderItemDO.getPackageDefaultCount());
            }
            // 属性总价 = 套餐数量 * 子项数量 * 默认份数
            mainOrderItemAttrTotal = mainOrderItemAttrTotal.add(subOrderItemAttrTotal.multiply(subAttrNum));
        }
        orderItemDO.setAttrTotal(mainOrderItemAttrTotal);
        updateOrderItems.add(orderItemDO);
    }

    @Override
    public List<SaleCountRespDTO> screenSaleType(SaleCountReqDTO saleCountReqDTO) {
        return baseMapper.screenSaleType(saleCountReqDTO);
    }

    @Override
    public List<SaleCountRespDTO> screenSaleItem(SaleCountReqDTO saleCountReqDTO) {
        return baseMapper.screenSaleItem(saleCountReqDTO);
    }

    @Override
    public void updateRefundCount(Long orderGuid, Long orderItemGuid, BigDecimal refundCount, BigDecimal freeRefundCount) {
        UpdateWrapper<OrderItemDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(OrderItemDO::getGuid, orderItemGuid);
        uw.setSql("refund_count = refund_count + " + refundCount.toPlainString());
        uw.setSql("free_refund_count = free_refund_count + " + freeRefundCount.toPlainString());
        if (update(uw)) {
            String redisKey = RedisKeyUtil.getHstOrderItemKey(String.valueOf(orderGuid));
            redisHelper.delete(redisKey);
        }
    }

    @Override
    public OrderItemDO getByGuidContainsDel(Long orderItemGuid) {
        return baseMapper.getByGuidContainsDel(orderItemGuid);
    }

    @Override
    public List<OrderItemDO> listByMainItemGuidContainsDel(Long orderItemGuid) {
        return baseMapper.listByMainItemGuidContainsDel(orderItemGuid);
    }

    @Override
    public void restoreOrderItem(List<OrderItemDO> restoreOrderItemList) {
        if (CollectionUtil.isEmpty(restoreOrderItemList)) {
            return;
        }
        baseMapper.restoreOrderItem(restoreOrderItemList);
    }

    @Override
    public List<OrderItemDO> listAllByGuid(Set<String> guids) {
        List<OrderItemDO> orderItemDOList = baseMapper.selectBatchIds(guids);
        if (org.springframework.util.CollectionUtils.isEmpty(orderItemDOList)) {
            return orderItemDOList;
        }
        List<Long> parentItemGuidList = orderItemDOList.stream()
                .filter(item -> item.getItemType() == ItemTypeEnum.GROUP.getCode())
                .map(OrderItemDO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        if (org.springframework.util.CollectionUtils.isEmpty(parentItemGuidList)) {
            return orderItemDOList;
        }
        List<OrderItemDO> subItemDOList = this.listByMainItemGuids(parentItemGuidList);
        return Stream.of(orderItemDOList, subItemDOList)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

}
