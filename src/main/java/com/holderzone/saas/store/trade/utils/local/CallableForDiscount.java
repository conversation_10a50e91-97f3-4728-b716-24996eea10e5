package com.holderzone.saas.store.trade.utils.local;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.saas.store.dto.trade.DiscountDTO;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import com.holderzone.saas.store.trade.repository.interfaces.DiscountService;
import com.holderzone.saas.store.trade.transform.LocalTransform;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

@Slf4j
public class CallableForDiscount implements Callable<List<DiscountDTO>> {

    //入参
    private List<String> orderGuidLists;

    private DiscountService discountService;

    private LocalTransform localTransform;

    List<DiscountDTO> data = new ArrayList<>();

    private final CountDownLatch latch;

    private UserContext userContext;


    public CallableForDiscount(List<String> orderGuidLists, DiscountService discountService,
                               LocalTransform localTransform, CountDownLatch latch, UserContext userContext) {
        this.orderGuidLists = orderGuidLists;
        this.discountService = discountService;
        this.localTransform = localTransform;
        this.latch = latch;
        this.userContext = userContext;
    }

    @Override
    public List<DiscountDTO> call() throws Exception {
        try {
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            log.info("查询优惠入参：{}", orderGuidLists);
            List<DiscountDO> discountLists = discountService.listByOrderGuids(orderGuidLists);
            data = localTransform.discountDOS2DiscountDTOS(discountLists);
            log.info("查询优惠结束");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            latch.countDown();
        }
        return data;
    }
}
