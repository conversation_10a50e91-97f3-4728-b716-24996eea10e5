package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.enums.VolumeTypeEnum;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.context.DiscountContext;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.caculate.PriceCalculationUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-06-26
 * @description
 */
@Component
@Slf4j
@AllArgsConstructor
public class MemberGrouponDiscountHandler extends DiscountHandler {
    private final PriceCalculationUtils priceCalculationUtils;

    @Override
    void dealDiscount(DiscountContext context) {
        if (context.isRejectDiscount()) {
            return;
        }
        // ============================== 8.代金券（营销活动） START ===================================
        if (context.getVolumeCodeType() == VolumeTypeEnum.CASH_COUPON.getCcCode()) {
            List<RequestDishInfo> dishInfoDTOList = priceCalculationUtils.dealwithVolume(context);
            DiscountDO memberGrouponDO = context.getDiscountTypeMap().get(type());
            DiscountFeeDetailDTO memberGroupon = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(memberGrouponDO);
            if (CollectionUtil.isNotEmpty(dishInfoDTOList)) {
                log.info("3.会员优惠券最终菜品：{}", JacksonUtils.writeValueAsString(dishInfoDTOList));
                priceCalculationUtils.dealwithVolumeByDiscount(context, dishInfoDTOList);
                singleItemDiscount(context.getDineInItemDTOMap(), memberGroupon, dishInfoDTOList);
            } else {
                memberGroupon.setDiscountFee(BigDecimal.ZERO);
            }
            context.getDiscountFeeDetailDTOS().add(memberGroupon);
            context.getOrderDetailRespDTO().setOrderSurplusFee(context.getOrderDetailRespDTO().getOrderSurplusFee().subtract(memberGroupon
                    .getDiscountFee()));
            log.info("3.会员优惠券计算后订单剩余金额：{}，优惠金额：{}，会员优惠券计算后菜品：{}", context.getOrderDetailRespDTO().getOrderSurplusFee(),
                    memberGroupon.getDiscountFee(), JacksonUtils.writeValueAsString(context.getDineInItemDTOMap()));
        }
        // ================================= 代金券（营销活动） END =============================================
        if (context.getVolumeCodeType() != VolumeTypeEnum.CASH_COUPON.getCcCode() && context.getVolumeCodeType() !=
                VolumeTypeEnum.PRODUCT_COUPON.getCcCode()) {
            DiscountDO memberGrouponDO = context.getDiscountTypeMap().get(type());
            DiscountFeeDetailDTO memberGroupon = OrderTransform.INSTANCE.discountDO2DiscountFeeDetailDTO(memberGrouponDO);
            memberGroupon.setDiscountFee(BigDecimal.ZERO);
            context.getDiscountFeeDetailDTOS().add(memberGroupon);
        }

    }

    @Override
    Integer type() {
        return DiscountTypeEnum.MEMBER_GROUPON.getCode();
    }
}
