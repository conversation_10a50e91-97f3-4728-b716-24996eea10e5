package com.holderzone.saas.store.trade.service.inner.interfaces;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.OrderDetailPushMqDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;

/**
 * {@link AsyncTradeService}
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/16 13:56
 */
public interface AsyncTradeService {

    void asyncCheckOutCall(BaseDTO baseDTO, OrderDO orderDO, DineinOrderDetailRespDTO orderDetail);

    void asyncPushOrder(OrderDO orderDO, UserContext userContext);

    boolean printCheckOutCall(BaseDTO baseDTO, OrderDO orderDO, DineinOrderDetailRespDTO orderDetail);

    OrderDetailPushMqDTO getOrderDetailPushMqDTO(String orderGuid);

    OrderDetailPushMqDTO getOrderDetailPushMqDTO(OrderDO orderDO);

}