package com.holderzone.saas.store.trade.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.OrderItemChangeFlagEnum;
import com.holderzone.saas.store.enums.order.OrderItemChangeNodeEnum;
import com.holderzone.saas.store.trade.bo.OrderPackageSubgroupBO;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.entity.enums.RecoveryTypeEnum;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.repository.feign.StoreClientService;
import com.holderzone.saas.store.trade.repository.interfaces.ItemAttrService;
import com.holderzone.saas.store.trade.repository.interfaces.OrderItemChangesService;
import com.holderzone.saas.store.trade.service.PackageSubgroupChangesService;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant.*;

/**
 * 套餐换菜
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PackageSubgroupChangesServiceImpl implements PackageSubgroupChangesService {

    private final StoreClientService storeClientService;

    private final ItemAttrService itemAttrService;

    private final OrderItemChangesService orderItemChangesService;

    private final DynamicHelper dynamicHelper;

    @Override
    public void changes(OrderPackageSubgroupBO packageSubgroupBiz) {
        // 换菜明细
        List<SubDineInItemDTO> subDineInItemList = packageSubgroupBiz.getChangeSubDineInItemList();
        List<SubDineInItemDTO> hasOriginalItemList = subDineInItemList.stream()
                .filter(e -> Objects.nonNull(e.getOriginalItem()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hasOriginalItemList)) {
            // 没有换菜
            return;
        }
        // 更新商品明细
        updateOrderItemInfo(packageSubgroupBiz, OrderItemChangeFlagEnum.CHANGE.getCode());
        // 查询营业时间
        if (Objects.isNull(packageSubgroupBiz.getBusinessDay())) {
            BusinessDateReqDTO reqDTO = new BusinessDateReqDTO();
            reqDTO.setStoreGuidList(Lists.newArrayList(UserContextUtils.getStoreGuid()));
            reqDTO.setQueryDateTime(LocalDateTime.now());
            LocalDate businessDay = storeClientService.queryBusinessDay(reqDTO);
            packageSubgroupBiz.setBusinessDay(businessDay);
        }
        // 换菜明细
        addOrderItemChangesDOList(packageSubgroupBiz, hasOriginalItemList);
    }


    @Override
    public void cancel(OrderPackageSubgroupBO packageSubgroupBiz) {
        List<SubDineInItemDTO> subDineInItemList = packageSubgroupBiz.getChangeSubDineInItemList();
        if (CollectionUtils.isEmpty(subDineInItemList)) {
            return;
        }
        // 更新商品明细
        updateOrderItemInfo(packageSubgroupBiz, OrderItemChangeFlagEnum.UN_CHANGE.getCode());
        // 查询营业时间
        BusinessDateReqDTO reqDTO = new BusinessDateReqDTO();
        reqDTO.setStoreGuidList(Lists.newArrayList(UserContextUtils.getStoreGuid()));
        reqDTO.setQueryDateTime(LocalDateTime.now());
        LocalDate businessDay = storeClientService.queryBusinessDay(reqDTO);
        packageSubgroupBiz.setBusinessDay(businessDay);
        // 换菜明细
        addOrderItemCancelChangesDOList(packageSubgroupBiz);
        // 撤销标识
        packageSubgroupBiz.getOrderItemChangesList().forEach(e -> e.setCancelFlag(BooleanEnum.TRUE.getCode()));
    }

    @Override
    public void addChangeOrderItemRecordDOList(OrderDO orderDO, List<OrderItemChangesDO> orderItemChangesDOList,
                                               List<OrderItemRecordDO> itemRecordSave) {
        if (CollectionUtils.isEmpty(orderItemChangesDOList)) {
            return;
        }
        // 换菜记录
        List<OrderItemRecordDO> itemRecordList = Lists.newArrayList();
        List<Long> orderItemRecordGuidList = dynamicHelper.generateGuids(ORDER_ITEM_RECORD, orderItemChangesDOList.size());
        for (OrderItemChangesDO orderItemChangesDO : orderItemChangesDOList) {
            OrderItemRecordDO itemRecordDO = OrderTransform.INSTANCE.orderItemChangesDO2OrderItemRecordDO(orderItemChangesDO);
            itemRecordDO.setGuid(orderItemRecordGuidList.remove(0));
            itemRecordDO.setType(1);
            itemRecordDO.setLog("【点菜】:套餐换餐");
            if (orderDO.getRecoveryType().equals(RecoveryTypeEnum.NEW.getCode())) {
                itemRecordDO.setIsFromRecovery(1);
            }
            itemRecordList.add(itemRecordDO);
        }
        itemRecordSave.addAll(itemRecordList);
    }

    @Override
    public void addOriginalItemInfo(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        List<DineInItemDTO> dineInItemDTOList = dineinOrderDetailRespDTO.getDineInItemDTOS();
        List<ReturnItemDTO> returnItemDTOList = dineinOrderDetailRespDTO.getReturnItemDTOS();
        if (CollectionUtils.isEmpty(dineInItemDTOList) && CollectionUtils.isEmpty(returnItemDTOList)) {
            return;
        }
        List<DineinOrderDetailRespDTO> subOrderDetails = dineinOrderDetailRespDTO.getSubOrderDetails();
        if (CollectionUtils.isNotEmpty(subOrderDetails)) {
            subOrderDetails.forEach(this::addOriginalItemInfo);
        }
        // 过滤出套餐子商品换菜的商品
        List<SubDineInItemDTO> changeSubDineInItemList = dineInItemDTOList.stream()
                .filter(e -> org.apache.commons.collections.CollectionUtils.isNotEmpty(e.getPackageSubgroupDTOS()))
                .flatMap(e -> e.getPackageSubgroupDTOS().stream())
                .filter(e -> org.apache.commons.collections.CollectionUtils.isNotEmpty(e.getSubDineInItemDTOS()))
                .flatMap(e -> e.getSubDineInItemDTOS().stream())
                .filter(e -> Objects.equals(OrderItemChangeFlagEnum.CHANGE.getCode(), e.getChangeFlag()))
                .collect(Collectors.toList());
        List<SubDineInItemDTO> returnChangeSubDineInItemList = returnItemDTOList.stream()
                .map(ReturnItemDTO::getDineInItemDTO)
                .filter(e -> org.apache.commons.collections.CollectionUtils.isNotEmpty(e.getPackageSubgroupDTOS()))
                .flatMap(e -> e.getPackageSubgroupDTOS().stream())
                .filter(e -> org.apache.commons.collections.CollectionUtils.isNotEmpty(e.getSubDineInItemDTOS()))
                .flatMap(e -> e.getSubDineInItemDTOS().stream())
                .filter(e -> Objects.equals(OrderItemChangeFlagEnum.CHANGE.getCode(), e.getChangeFlag()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(returnChangeSubDineInItemList)) {
            changeSubDineInItemList.addAll(returnChangeSubDineInItemList);
        }
        if (CollectionUtils.isEmpty(changeSubDineInItemList)) {
            return;
        }
        addOriginalItemInfo(Long.valueOf(dineinOrderDetailRespDTO.getGuid()), changeSubDineInItemList);
    }


    @Override
    public void addOriginalItemInfo(Long orderGuid, List<SubDineInItemDTO> changeSubDineInItemList) {
        if (CollectionUtils.isEmpty(changeSubDineInItemList)) {
            return;
        }
        changeSubDineInItemList = changeSubDineInItemList.stream()
                .filter(e -> Objects.equals(OrderItemChangeFlagEnum.CHANGE.getCode(), e.getChangeFlag()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(changeSubDineInItemList)) {
            return;
        }
        log.info("查询换菜信息,changeSubDineInItemList:{}", JacksonUtils.writeValueAsString(changeSubDineInItemList));
        // 查询换菜信息
        List<Long> changeSubDineInItemGuidList = changeSubDineInItemList.stream()
                .map(SubDineInItemDTO::getGuid)
                .map(Long::valueOf)
                .distinct()
                .collect(Collectors.toList());
        List<OrderItemChangesDO> orderItemChangesList = orderItemChangesService.listByOrderGuid(orderGuid);
        List<OrderItemChangesDO> existOrderItemChangesList = orderItemChangesList.stream()
                .filter(e -> changeSubDineInItemGuidList.contains(e.getOrderItemGuid()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existOrderItemChangesList)) {
            log.warn("套餐子菜换菜商品查询为空, changeSubDineInItemGuidList:{}", JacksonUtils.writeValueAsString(changeSubDineInItemGuidList));
            return;
        }
        Map<String, OrderItemChangesDO> orderItemChangesGroupByBatchNumberMap = orderItemChangesList.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getChangeBatchNumber()) && Objects.isNull(e.getOriginalOrderItemGuid()))
                .collect(Collectors.toMap(OrderItemChangesDO::getChangeBatchNumber, Function.identity(), (key1, key2) -> key1));
        Map<Long, OrderItemChangesDO> orderItemChangesByOrderItemGuidMap = orderItemChangesList.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getChangeBatchNumber()))
                .collect(Collectors.toMap(OrderItemChangesDO::getOrderItemGuid, Function.identity(), (key1, key2) -> key1));
        List<Long> orderItemChangesGuidList = orderItemChangesList.stream().map(OrderItemChangesDO::getOrderItemGuid).collect(Collectors.toList());
        // 查询原菜品属性
        List<ItemAttrDO> itemAttrDOList = itemAttrService.listByItemGuids(orderItemChangesGuidList);
        Map<Long, List<ItemAttrDO>> itemAttrMap = itemAttrDOList.stream()
                .collect(Collectors.groupingBy(ItemAttrDO::getOrderItemGuid));
        for (SubDineInItemDTO subDineInItemDTO : changeSubDineInItemList) {
            OrderItemChangesDO changesDO = orderItemChangesByOrderItemGuidMap.get(Long.valueOf(subDineInItemDTO.getGuid()));
            if (Objects.nonNull(changesDO)) {
                subDineInItemDTO.setChangeBatchNumber(changesDO.getChangeBatchNumber());
                OrderItemChangesDO orderItemChangesDO = orderItemChangesGroupByBatchNumberMap.get(changesDO.getChangeBatchNumber());
                if (Objects.nonNull(orderItemChangesDO)) {
                    SubDineInItemDTO originalSubDineInItemDTO = OrderTransform.INSTANCE.orderItemChangesDO2SubDineInItemDTO(orderItemChangesDO);
                    // 原菜的属性组
                    addChangeItemAttrList(orderItemChangesDO, originalSubDineInItemDTO, itemAttrMap);
                    subDineInItemDTO.setOriginalItem(originalSubDineInItemDTO);
                }
            }
        }
    }


    /**
     * 增加换菜商品的属性列表
     */
    private void addChangeItemAttrList(OrderItemChangesDO orderItemChanges, SubDineInItemDTO changeSubDineInItemDTO,
                                       Map<Long, List<ItemAttrDO>> itemAttrMap) {
        if (orderItemChanges.getHasAttr() != null && orderItemChanges.getHasAttr() == 1) {
            List<ItemAttrDO> itemAttrList = itemAttrMap.get(orderItemChanges.getOrderItemGuid());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(itemAttrList)) {
                return;
            }
            List<ItemAttrDTO> itemAttrDTOS = OrderTransform.INSTANCE.itemAttrDOS2itemAttrDTOS(itemAttrList);
            changeSubDineInItemDTO.setItemAttrDTOS(itemAttrDTOS);
            BigDecimal attrTotal = BigDecimal.ZERO;
            for (ItemAttrDO itemAttrDO : itemAttrList) {
                attrTotal = attrTotal.add(itemAttrDO.getAttrPrice());
            }
            changeSubDineInItemDTO.setSingleItemAttrTotal(attrTotal);
        }
    }

    /**
     * 更新商品明细
     */
    private void updateOrderItemInfo(OrderPackageSubgroupBO packageSubgroupBiz, Integer changeFlag) {
        Map<String, SubDineInItemDTO> hasOriginalItemMap = packageSubgroupBiz.getChangeSubDineInItemList().stream()
                .filter(e -> Objects.nonNull(e.getOriginalItem()))
                .collect(Collectors.toMap(SubDineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        List<OrderItemExtendsDO> orderItemExtendsDOList = packageSubgroupBiz.getOrderItemExtendsDOS();
        // 换菜标识
        orderItemExtendsDOList.forEach(e -> {
            if (hasOriginalItemMap.containsKey(String.valueOf(e.getGuid()))) {
                e.setGmtModified(LocalDateTime.now());
                e.setChangeFlag(changeFlag);
            }
        });
        // 原菜品明细更新 (数量)
        List<OrderItemDO> orderItemDOList = packageSubgroupBiz.getOrderItemDOS();
        Map<String, SubDineInItemDTO> originalItemMap = packageSubgroupBiz.getChangeSubDineInItemList().stream()
                .map(SubDineInItemDTO::getOriginalItem)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(SubDineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        Map<String, SubDineInItemDTO> originalExistItemMap = packageSubgroupBiz.getChangeSubDineInItemList().stream()
                .collect(Collectors.toMap(SubDineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        for (OrderItemDO itemDO : orderItemDOList) {
            if (OrderItemChangeFlagEnum.CHANGE.getCode() == changeFlag) {
                // 换菜
                if (!originalItemMap.containsKey(String.valueOf(itemDO.getGuid()))) {
                    return;
                }
                SubDineInItemDTO originalItem = originalItemMap.get(String.valueOf(itemDO.getGuid()));
                itemDO.setCurrentCount(itemDO.getCurrentCount().subtract(originalItem.getCurrentCount()));
            } else {
                // 撤销
                if (!originalExistItemMap.containsKey(String.valueOf(itemDO.getGuid()))) {
                    return;
                }
                if (Boolean.TRUE.equals(packageSubgroupBiz.getAddOrderItemFlag())) {
                    return;
                }
                SubDineInItemDTO originalExistItem = originalExistItemMap.get(String.valueOf(itemDO.getGuid()));
                itemDO.setCurrentCount(itemDO.getCurrentCount().add(originalExistItem.getCurrentCount()));
            }
        }
    }

    /**
     * 构建换菜明细
     */
    private void addOrderItemChangesDOList(OrderPackageSubgroupBO packageSubgroupBiz, List<SubDineInItemDTO> hasOriginalItemList) {
        // 菜品信息
        Map<String, ItemInfoRespDTO> itemInfoRespMap = packageSubgroupBiz.getItemInfoRespDTOMap();
        // 换菜明细
        List<OrderItemChangesDO> orderItemChangesList = Lists.newArrayList();
        // 换菜明细中的原菜明细
        Map<String, SubDineInItemDTO> subDineInItemByChangeBatchNumberMap = hasOriginalItemList.stream()
                .map(SubDineInItemDTO::getOriginalItem)
                .filter(e -> StringUtils.isNotEmpty(e.getChangeBatchNumber()))
                .collect(Collectors.toMap(SubDineInItemDTO::getChangeBatchNumber, Function.identity(), (key1, key2) -> key1));
        // 换菜明细中的明细
        List<Long> changeSubOrderItemGuids = dynamicHelper.generateGuids(HST_ORDER_ITEM,
                (long) hasOriginalItemList.size() + subDineInItemByChangeBatchNumberMap.size());
        for (SubDineInItemDTO subDineInItemDTO : hasOriginalItemList) {
            // 原菜
            SubDineInItemDTO originalSubDineInItemDTO = subDineInItemDTO.getOriginalItem();
            // 换菜指向的原明细
            OrderItemChangesDO originalOrderItemChangesDO = getOriginalOrderItemChangesDO(subDineInItemDTO.getChangeBatchNumber(), orderItemChangesList);
            if (Objects.isNull(originalOrderItemChangesDO)) {
                ItemInfoRespDTO originalItemInfoResp = itemInfoRespMap.getOrDefault(originalSubDineInItemDTO.getItemGuid(), new ItemInfoRespDTO());
                // sku info
                SkuInfoRespDTO originalskuInfoRespDTO = Optional.ofNullable(originalItemInfoResp.getSkuList()).orElse(Lists.newArrayList())
                        .stream()
                        .filter(e -> originalSubDineInItemDTO.getSkuGuid().equals(e.getSkuGuid()))
                        .findFirst()
                        .orElse(null);
                // 获取此批次原菜
                SubDineInItemDTO originalSubDineInItem = getOriginalSubDineInItemDTO(originalSubDineInItemDTO, packageSubgroupBiz.getChangeSubDineInItemList());
                if (Objects.nonNull(originalSubDineInItem)) {
                    originalSubDineInItemDTO.setGuid(originalSubDineInItem.getGuid());
                }
                if (StringUtils.isEmpty(originalSubDineInItemDTO.getGuid())) {
                    originalSubDineInItemDTO.setGuid(String.valueOf(dynamicHelper.generateGuid(HST_ORDER_ITEM)));
                }
                originalOrderItemChangesDO = subDineInItemToOrderItemChangesDO(packageSubgroupBiz, changeSubOrderItemGuids,
                        null, originalSubDineInItemDTO, originalskuInfoRespDTO);
                orderItemChangesList.add(originalOrderItemChangesDO);
            } else {
                originalSubDineInItemDTO.setGuid(String.valueOf(originalOrderItemChangesDO.getOrderItemGuid()));
            }
            originalSubDineInItemDTO.setChangeOrderItemGuid(String.valueOf(originalOrderItemChangesDO.getGuid()));

            // 新菜
            ItemInfoRespDTO newItemInfoResp = itemInfoRespMap.getOrDefault(subDineInItemDTO.getItemGuid(), new ItemInfoRespDTO());
            // sku info
            SkuInfoRespDTO newSkuInfoRespDTO = Optional.ofNullable(newItemInfoResp.getSkuList()).orElse(Lists.newArrayList())
                    .stream()
                    .filter(e -> subDineInItemDTO.getSkuGuid().equals(e.getSkuGuid()))
                    .findFirst()
                    .orElse(null);
            OrderItemChangesDO newOrderItemChangesDO = subDineInItemToOrderItemChangesDO(packageSubgroupBiz, changeSubOrderItemGuids,
                    originalOrderItemChangesDO.getGuid(), subDineInItemDTO, newSkuInfoRespDTO);
            subDineInItemDTO.setChangeOrderItemGuid(String.valueOf(newOrderItemChangesDO.getGuid()));
            orderItemChangesList.add(newOrderItemChangesDO);
        }
        log.info("套餐换菜明细:{}", JacksonUtils.writeValueAsString(orderItemChangesList));
        packageSubgroupBiz.getOrderItemChangesList().addAll(orderItemChangesList);
    }

    /**
     * 构建撤销换菜明细
     */
    private void addOrderItemCancelChangesDOList(OrderPackageSubgroupBO packageSubgroupBiz) {
        // 菜品信息
        Map<String, ItemInfoRespDTO> itemInfoRespMap = packageSubgroupBiz.getItemInfoRespDTOMap();
        // 换菜明细
        List<OrderItemChangesDO> orderItemChangesList = Lists.newArrayList();
        List<SubDineInItemDTO> originalSubDineInItemList = packageSubgroupBiz.getSubDineInItemList();
        // 换菜明细中的明细
        List<Long> changeSubOrderItemGuids = dynamicHelper.generateGuids(HST_ORDER_ITEM,
                (long) originalSubDineInItemList.size() * 2);
        // 原菜
        for (SubDineInItemDTO originalSubDineInItemDTO : originalSubDineInItemList) {
            // 原菜 sku info
            SkuInfoRespDTO originalskuInfoRespDTO = getSkuInfoRespDTO(itemInfoRespMap, originalSubDineInItemDTO);
            if (StringUtils.isEmpty(originalSubDineInItemDTO.getGuid())) {
                originalSubDineInItemDTO.setGuid(String.valueOf(dynamicHelper.generateGuid(HST_ORDER_ITEM)));
            }
            // 构建原菜的换菜明细
            OrderItemChangesDO originalOrderItemChangesDO = subDineInItemToOrderItemChangesDO(packageSubgroupBiz,
                    changeSubOrderItemGuids, null, originalSubDineInItemDTO, originalskuInfoRespDTO);
            orderItemChangesList.add(originalOrderItemChangesDO);
            originalSubDineInItemDTO.setChangeOrderItemGuid(String.valueOf(originalOrderItemChangesDO.getGuid()));
        }
        // 新菜
        List<SubDineInItemDTO> changeSubDineInItemList = packageSubgroupBiz.getChangeSubDineInItemList();
        SubDineInItemDTO changeSubDineInItem = changeSubDineInItemList.get(0);
        // 新菜 sku info
        SkuInfoRespDTO changeskuInfoRespDTO = getSkuInfoRespDTO(itemInfoRespMap, changeSubDineInItem);
        for (SubDineInItemDTO originalSubDineInItemDTO : originalSubDineInItemList) {
            // 构建新菜的换菜明细
            OrderItemChangesDO newOrderItemChangesDO = subDineInItemToOrderItemChangesDO(packageSubgroupBiz, changeSubOrderItemGuids,
                    Long.valueOf(originalSubDineInItemDTO.getChangeOrderItemGuid()), changeSubDineInItem, changeskuInfoRespDTO);
            changeSubDineInItem.setChangeOrderItemGuid(String.valueOf(newOrderItemChangesDO.getGuid()));
            orderItemChangesList.add(newOrderItemChangesDO);
        }
        log.info("套餐换菜明细:{}", JacksonUtils.writeValueAsString(orderItemChangesList));
        packageSubgroupBiz.getOrderItemChangesList().addAll(orderItemChangesList);
    }

    /**
     * 获取商品信息
     */
    private SkuInfoRespDTO getSkuInfoRespDTO(Map<String, ItemInfoRespDTO> itemInfoRespMap, SubDineInItemDTO subDineInItemDTO) {
        // sku info
        ItemInfoRespDTO changeItemInfoResp = itemInfoRespMap.getOrDefault(subDineInItemDTO.getItemGuid(), new ItemInfoRespDTO());
        return Optional.ofNullable(changeItemInfoResp.getSkuList()).orElse(Lists.newArrayList())
                .stream()
                .filter(e -> subDineInItemDTO.getSkuGuid().equals(e.getSkuGuid()))
                .findFirst()
                .orElse(null);
    }

    /**
     * subDineInItemDTO 转 OrderItemChangesDO
     */
    private OrderItemChangesDO subDineInItemToOrderItemChangesDO(OrderPackageSubgroupBO packageSubgroupBiz,
                                                                 List<Long> changeSubOrderItemGuids,
                                                                 Long originalOrderItemGuid,
                                                                 SubDineInItemDTO subDineInItemDTO,
                                                                 SkuInfoRespDTO skuInfoRespDTO) {
        OrderItemChangesDO orderItemChangesDO = OrderTransform.INSTANCE.subDineInItemDTO2OrderItemChangesDO(subDineInItemDTO);
        orderItemChangesDO.setGuid(changeSubOrderItemGuids.remove(0));
        orderItemChangesDO.setOrderGuid(packageSubgroupBiz.getOrderDO().getGuid());
        orderItemChangesDO.setOrderItemGuid(Long.valueOf(subDineInItemDTO.getGuid()));
        orderItemChangesDO.setOriginalOrderItemGuid(originalOrderItemGuid);
        orderItemChangesDO.setGmtCreate(LocalDateTime.now());
        if (ObjectUtils.isEmpty(orderItemChangesDO.getPrice())) {
            orderItemChangesDO.setPrice(BigDecimal.ZERO);
        }
        orderItemChangesDO.setFreeCount(BigDecimal.ZERO);
        orderItemChangesDO.setReturnCount(BigDecimal.ZERO);
        orderItemChangesDO.setChangeBatchNumber(subDineInItemDTO.getChangeBatchNumber());
        orderItemChangesDO.setBusinessDay(packageSubgroupBiz.getBusinessDay());
        orderItemChangesDO.setStoreGuid(UserContextUtils.getStoreGuid());
        orderItemChangesDO.setStoreName(UserContextUtils.getStoreName());
        orderItemChangesDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
        orderItemChangesDO.setCreateStaffName(UserContextUtils.getUserName());
        orderItemChangesDO.setChangeNode(OrderItemChangeNodeEnum.ADD_ITEM_BEFORE.getNode());
        if (Boolean.FALSE.equals(packageSubgroupBiz.getAddItemFlag())) {
            orderItemChangesDO.setChangeNode(OrderItemChangeNodeEnum.ADD_ITEM_AFTER.getNode());
        }
        orderItemChangesDO.setAddPrice(BigDecimal.ZERO);
        if (BigDecimalUtil.greaterThanZero(subDineInItemDTO.getAddPrice())) {
            orderItemChangesDO.setAddPrice(subDineInItemDTO.getAddPrice());
        }
        if (Objects.nonNull(skuInfoRespDTO)) {
            orderItemChangesDO.setAccountingPrice(skuInfoRespDTO.getAccountingPrice());
        }
        // 属性
        List<ItemAttrDTO> itemAttrList = subDineInItemDTO.getItemAttrDTOS();
        orderItemChangesDO.setHasAttr(0);
        if (!CollectionUtils.isEmpty(itemAttrList)) {
            BigDecimal attrTotal = BigDecimal.ZERO;
            for (ItemAttrDTO itemAttrDTO : itemAttrList) {
                attrTotal = attrTotal.add(itemAttrDTO.getAttrPrice()
                        .multiply(BigDecimal.valueOf(itemAttrDTO.getNum())));
            }
            orderItemChangesDO.setHasAttr(1);
            orderItemChangesDO.setAttrTotal(attrTotal);
        }
        return orderItemChangesDO;
    }

    /**
     * 获取此批次原菜
     */
    private SubDineInItemDTO getOriginalSubDineInItemDTO(SubDineInItemDTO originalSubDineInItemDTO, List<SubDineInItemDTO> hasOriginalItemList) {
        String skuGuid = originalSubDineInItemDTO.getSkuGuid();
        return hasOriginalItemList.stream().filter(e -> Objects.equals(skuGuid, e.getSkuGuid())
                        && Objects.isNull(e.getOriginalItem()))
                .findFirst().orElse(null);
    }

    /**
     * 获取原 商品换菜明细guid
     */
    private OrderItemChangesDO getOriginalOrderItemChangesDO(String changeBatchNumber, List<OrderItemChangesDO> orderItemChangesList) {
        return orderItemChangesList.stream()
                .filter(e -> Objects.isNull(e.getOriginalOrderItemGuid()) && Objects.equals(changeBatchNumber, e.getChangeBatchNumber()))
                .findFirst()
                .orElse(null);
    }
}
