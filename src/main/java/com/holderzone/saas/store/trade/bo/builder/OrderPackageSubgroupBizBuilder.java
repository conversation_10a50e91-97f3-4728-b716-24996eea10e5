package com.holderzone.saas.store.trade.bo.builder;

import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import com.holderzone.saas.store.trade.bo.OrderPackageSubgroupBO;


/**
 * 订单加菜 - 套餐biz builder
 */
public class OrderPackageSubgroupBizBuilder {

    private OrderPackageSubgroupBizBuilder() {

    }

    public static OrderPackageSubgroupBO build() {
        OrderPackageSubgroupBO orderPackageSubgroupBO = new OrderPackageSubgroupBO();
        orderPackageSubgroupBO.setItemInfoRespDTOMap(Maps.newHashMap());
        orderPackageSubgroupBO.setItemAttrDOS(Lists.newArrayList());
        orderPackageSubgroupBO.setItemRecordSave(Lists.newArrayList());
        orderPackageSubgroupBO.setOrderItemDOS(Lists.newArrayList());
        orderPackageSubgroupBO.setRestoreOrderItemDOS(Lists.newArrayList());
        orderPackageSubgroupBO.setRemoveOrderItemDOS(Lists.newArrayList());
        orderPackageSubgroupBO.setOrderItemExtendsDOS(Lists.newArrayList());
        orderPackageSubgroupBO.setOrderItemChangesList(Lists.newArrayList());
        orderPackageSubgroupBO.setAddChangeBatchNumber(Lists.newArrayList());
        return orderPackageSubgroupBO;
    }
}
