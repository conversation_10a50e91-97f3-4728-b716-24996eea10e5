package com.holderzone.saas.store.trade.entity.domain;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 订单会员多卡支付
 */
@Data
@TableName("hst_order_multi_member")
public class OrderMultiMember {

    /**
     * 全局唯一主键
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 会员卡guid
     */
    private String memberCardGuid;

    /**
     * 会员卡号
     */
    private String memberCardNum;

    /**
     * 会员电话
     */
    private String memberPhone;

    /**
     * 会员名字
     */
    private String memberName;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 登录方式 0:扫码 1:手机号
     */
    private Integer loginType;


}
