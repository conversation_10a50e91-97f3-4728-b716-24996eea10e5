package com.holderzone.saas.store.trade.service.chain;

import com.holderzone.saas.store.trade.context.DiscountContext;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @create 2023-06-25
 * @description
 */
@Component
@AllArgsConstructor
public class DiscountChain {

    private final FreeDiscountHandler freeDiscountHandler;

    private final ThirdActivityDiscountHandler thirdActivityDiscountHandler;

    private final GrouponDiscountHandler grouponDiscountHandler;

    private final GrouponVoucherDiscountHandler grouponVoucherDiscountHandler;

    private final ActivityDiscountHandler activityDiscountHandler;

    private final NthActivityDiscountHandler nthActivityDiscountHandler;

    private final FollowRedPacketDiscountHandler followRedPacketDiscountHandler;

    private final GoodsGrouponDiscountHandler goodsGrouponDiscountHandler;

    private final MemberDiscountHandler memberDiscountHandler;

    private final  MemberGrouponDiscountHandler memberGrouponDiscountHandler;

    private final PointsDeductionDiscountHandler pointsDeductionDiscountHandler;

    private final SystemDiscountHandler systemDiscountHandler;

    private final SingleMemberDiscountHandler singleMemberDiscountHandler;

    private final SingleDiscountHandler singleDiscountHandler;

    private final WholeDiscountHandler wholeDiscountHandler;

    private final ConcessionalDiscountHandler concessionalDiscountHandler;

    private final MaitonGrouponDiscountHandler maitonGrouponDiscountHandler;

    @PostConstruct
    public void init(){
        // 1.团购验券-套餐券
        grouponDiscountHandler.setNextDiscountHandler(freeDiscountHandler);
        // 2.菜品赠送优惠
        freeDiscountHandler.setNextDiscountHandler(goodsGrouponDiscountHandler);
        // 3.商品券
        goodsGrouponDiscountHandler.setNextDiscountHandler(singleMemberDiscountHandler);
        // 4.会员价
        singleMemberDiscountHandler.setNextDiscountHandler(singleDiscountHandler);
        // 5.单品改价
        // 6.单品折扣
        singleDiscountHandler.setNextDiscountHandler(grouponVoucherDiscountHandler);
        // 7.团购验券-代金券
        grouponVoucherDiscountHandler.setNextDiscountHandler(memberGrouponDiscountHandler);
        // 8.代金券
        memberGrouponDiscountHandler.setNextDiscountHandler(memberDiscountHandler);
        // 9.会员折扣
        memberDiscountHandler.setNextDiscountHandler(nthActivityDiscountHandler);
        // 第N份优惠活动
        nthActivityDiscountHandler.setNextDiscountHandler(activityDiscountHandler);
        //10.满减，11.满折
        activityDiscountHandler.setNextDiscountHandler(wholeDiscountHandler);
        // 12.整单折扣
        wholeDiscountHandler.setNextDiscountHandler(pointsDeductionDiscountHandler);
        // 13.积分抵扣
        pointsDeductionDiscountHandler.setNextDiscountHandler(systemDiscountHandler);
        // 14.系统省零
        systemDiscountHandler.setNextDiscountHandler(concessionalDiscountHandler);
        // 15.整单让价
        // 16.随行红包优惠
        concessionalDiscountHandler.setNextDiscountHandler(followRedPacketDiscountHandler);
        // 美团一键买单
        followRedPacketDiscountHandler.setNextDiscountHandler(maitonGrouponDiscountHandler);
    }

    public void doDiscount(DiscountContext context){
        grouponDiscountHandler.doDiscount(context);
    }
}
