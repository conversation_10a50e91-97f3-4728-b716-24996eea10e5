package com.holderzone.saas.store.trade.async.print.util;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.print.content.PrintCoTableCbDTO;
import com.holderzone.saas.store.dto.print.content.PrintPreCoTableCbDTO;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import com.holderzone.saas.store.trade.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.trade.transform.PrintTransform;
import com.holderzone.saas.store.trade.utils.AmountCalculationUtil;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import com.holderzone.saas.store.trade.utils.ItemUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/09/23 11:31
 */
@Slf4j
public class PrintUtils {

    private PrintTransform printTransform = PrintTransform.INSTANCE;

    private static BinaryOperator<DineInItemDTO> mergeFunction = (a, b) -> {
        b.setCurrentCount(b.getCurrentCount().add(a.getCurrentCount()));
        b.setFreeCount(b.getFreeCount().add(a.getFreeCount()));
        b.setReturnCount(b.getReturnCount().add(a.getReturnCount()));
        b.setDiscountTotalPrice(Optional.ofNullable(b.getDiscountTotalPrice()).orElse(b.getItemPrice())
                .add(Optional.ofNullable(a.getDiscountTotalPrice()).orElse(a.getItemPrice())));
        return b;
    };

    /**
     * 菜品列表
     */
    List<PrintItemRecord> extractItemRecords(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, boolean
            isReturn, boolean isMerge) {
        List<DineInItemDTO> dineInItemDTOS = dineinOrderDetailRespDTO.getDineInItemDTOS();
        if (dineInItemDTOS == null) {
            return Collections.emptyList();
        }
        Stream<DineInItemDTO> dineInItemDTOStream = isMerge ?
                dineInItemDTOS.stream()
                        .collect(Collectors.groupingBy(DineInItemDTO::getPriceAndGuidGroupByKey, LinkedHashMap::new
                                , Collectors.toList()))
                        .values().stream()
                        .map(this::reduceDininItem)
                        .flatMap(Collection::stream)
                : dineInItemDTOS.stream();
        return dineInItemDTOStream.flatMap(dineInItemDTO -> {
            List<PrintItemRecord> printItemRecords = new ArrayList<>(2);
            if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getCurrentCount())) {
                printItemRecords.add(newPrintItemRecord(dineInItemDTO, dineInItemDTO.getCurrentCount(), false));
            }
            if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getFreeCount()) && !isReturn) {
                printItemRecords.add(newPrintItemRecord(dineInItemDTO, dineInItemDTO.getFreeCount(), true));
            }
            return printItemRecords.stream();
        }).collect(Collectors.toList());
    }

    private BigDecimal getDiscountAfterTotal(List<PrintItemRecord> itemRecordList) {
        List<PrintItemRecord> printItemRecords = Optional.ofNullable(itemRecordList).orElse(Lists.newArrayList());
        return printItemRecords.stream()
                .map(PrintItemRecord::getItemDiscountAfterPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * @param dineinitemdtos 同样价格的商品,@see {@link DineInItemDTO#getPriceAndGuidGroupByKey()}
     * @return 整理后的结果
     */
    private List<DineInItemDTO> reduceDininItem(List<DineInItemDTO> dineinitemdtos) {
        //  改价类型 ---> 商品
        Map<Integer, List<DineInItemDTO>> typeToItemList = dineinitemdtos.stream().collect(Collectors.groupingBy(DineInItemDTO::getPriceChangeType));
        List<DineInItemDTO> dtoss = new ArrayList<>();
        //  未改价  直接合并
        if (!CollectionUtil.isEmpty(typeToItemList.get(0))) {
            DineInItemDTO dineInItemDtoNoChange = typeToItemList.get(0).stream().reduce(mergeFunction).orElse(null);
            dtoss.add(dineInItemDtoNoChange);
        }
        //单品改价  根据price合并
        if (!CollectionUtil.isEmpty(typeToItemList.get(1))) {
            List<DineInItemDTO> collectChangePrice = typeToItemList.get(1).stream()
                    .collect(Collectors.groupingBy(DineInItemDTO::getPrice, Collectors.reducing(mergeFunction)))
                    .values().stream().map(Optional::get).collect(Collectors.toList());
            dtoss.addAll(collectChangePrice);
        }
        //单品折扣  根据折扣合并
        if (!CollectionUtil.isEmpty(typeToItemList.get(2))) {
            List<DineInItemDTO> collectReduce = typeToItemList.get(2).stream()
                    .collect(Collectors.groupingBy(DineInItemDTO::getDiscountPercent, Collectors.reducing(mergeFunction)))
                    .values().stream().map(Optional::get).collect(Collectors.toList());
            dtoss.addAll(collectReduce);
        }
        return dtoss;
    }


    /**
     * 菜品列表
     */
    List<PrintItemRecord> extractItemRecords(DineinOrderDetailRespDTO dineinOrderDetailRespDTO, boolean
            isReturn) {
        return Optional.ofNullable(dineinOrderDetailRespDTO.getDineInItemDTOS())
                .map(dineInItemDtos -> dineInItemDtos.stream().flatMap(dineInItemDTO -> {
                    List<PrintItemRecord> printItemRecords = new ArrayList<>(2);
                    if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getCurrentCount())) {
                        printItemRecords.add(newPrintItemRecord(dineInItemDTO, dineInItemDTO.getCurrentCount(), false));
                    }
                    if (BigDecimalUtil.greaterThanZero(dineInItemDTO.getFreeCount()) && !isReturn) {
                        printItemRecords.add(newPrintItemRecord(dineInItemDTO, dineInItemDTO.getFreeCount(), true));
                    }
                    return printItemRecords.stream();
                }).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    /**
     * 列表每一项
     */
    private PrintItemRecord newPrintItemRecord(DineInItemDTO dineInItemDTO, BigDecimal number, boolean asGift) {
        PrintItemRecord printItemRecord = turn2PrintItemRecord(dineInItemDTO, asGift, false);
        printItemRecord.setNumber(number);
        if (dineInItemDTO.getItemType() == ItemTypeEnum.GROUP.getCode() || dineInItemDTO.getItemType() ==
                ItemTypeEnum.TEAMMEAL.getCode()) {
            printItemRecord.setPropertyPrice(BigDecimal.ZERO);
            if (!CollectionUtil.isEmpty(dineInItemDTO.getPackageSubgroupDTOS())) {
                printItemRecord.setSubItemRecords(getSubItemRecords(dineInItemDTO, asGift));
            }
        }
        return printItemRecord;
    }

    /**
     * 子项
     */
    private List<PrintItemRecord> getSubItemRecords(DineInItemDTO dineInItemDTO, boolean asGift) {
        return dineInItemDTO.getPackageSubgroupDTOS().stream()
                .map(PackageSubgroupDTO::getSubDineInItemDTOS)
                .filter(subDineInItemDtos -> !CollectionUtils.isEmpty(subDineInItemDtos))
                .flatMap(Collection::stream)
                .map(subDineInItemDTO -> {
                    DineInItemDTO dineinitemdtofromsub = printTransform.createDineInItemDTOFromSub(subDineInItemDTO);
                    PrintItemRecord subPrintItemRecord = turn2PrintItemRecord(dineinitemdtofromsub, asGift, true);
                    subPrintItemRecord.setPkgCnt(Optional.ofNullable(subDineInItemDTO.getPackageDefaultCount())
                            .orElse(BigDecimal.ONE));
                    subPrintItemRecord.setNumber(subDineInItemDTO.getCurrentCount());
                    subPrintItemRecord.setIngredientPrice(subDineInItemDTO.getAddPrice());
                    return subPrintItemRecord;
                })
                .collect(Collectors.toList());
    }

    /**
     * 附加費
     */
    private BigDecimal requirePropertyPrice(BigDecimal singleItemAttrTotal, List<ItemAttrDTO> itemAttrList) {
        if (singleItemAttrTotal != null) {
            return singleItemAttrTotal;
        }
        return Optional.ofNullable(itemAttrList).map(attrDtos -> attrDtos.stream()
                        .map(itemAttrDTO -> itemAttrDTO.getAttrPrice().multiply(BigDecimal.valueOf(itemAttrDTO.getNum())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                .orElse(BigDecimal.ZERO);
    }

    /**
     * 附加费属性
     */
    private String getItemProperty(List<ItemAttrDTO> attrdtolist) {
        if (CollectionUtils.isEmpty(attrdtolist)) {
            return null;
        }
        return attrdtolist.stream().map(itemAttrDTO -> itemAttrDTO.getNum() == 1 ?
                        itemAttrDTO.getAttrName() : itemAttrDTO.getAttrName() + "x" + itemAttrDTO.getNum())
                .collect(Collectors.joining("，"));
    }

    /**
     * 转换
     */
    private PrintItemRecord turn2PrintItemRecord(DineInItemDTO dineInItemDTO, boolean asGift, boolean issub) {
        BigDecimal price = dineInItemDTO.getPrice();
        if (!issub && (asGift || Objects.equals(dineInItemDTO.getPriceChangeType(), 2))) {
            price = dineInItemDTO.getOriginalPrice();
        }
        return new PrintItemRecord().setOrderItemGuid(dineInItemDTO.getGuid())
                .setOriginalOrderItemGuid(dineInItemDTO.getOriginalOrderItemGuid())
                .setItemGuid(dineInItemDTO.getItemGuid())
                .setUnit(dineInItemDTO.getUnit())
                .setItemName(ItemTypeEnum.SKU.getCode() == dineInItemDTO.getItemType() ? dineInItemDTO.getItemName()
                        + "(" + dineInItemDTO.getSkuName() + ")" : dineInItemDTO.getItemName())
                .setItemTypeGuid(dineInItemDTO.getItemTypeGuid())
                .setItemTypeName(dineInItemDTO.getItemTypeName())
                .setPrice(price)
                .setItemDiscountAfterPrice(getItemDiscountAfterPrice(dineInItemDTO, asGift))
                .setAsPackage(!issub && ItemUtil.isGroup(dineInItemDTO.getItemType()))
                .setAsWeight(Integer.valueOf(ItemTypeEnum.WEIGH.getCode()).equals(dineInItemDTO.getItemType()))
                .setAsGift(asGift)
                .setIngredientPrice(BigDecimal.ZERO)
                .setRemark(dineInItemDTO.getRemark())
                .setProperty(getItemProperty(dineInItemDTO.getItemAttrDTOS()))
                .setPropertyPrice(requirePropertyPrice(dineInItemDTO.getSingleItemAttrTotal(), dineInItemDTO
                        .getItemAttrDTOS()))
                .setHasAttr(dineInItemDTO.getHasAttr())
                .setSingleItemAttrTotal(dineInItemDTO.getSingleItemAttrTotal())
                .setSingleAddPriceTotal(dineInItemDTO.getSingleAddPriceTotal())
                .setItemState(dineInItemDTO.getItemState())
                .setActualPrice(Objects.nonNull(dineInItemDTO.getMemberPrice()) ? dineInItemDTO.getMemberPrice().multiply(dineInItemDTO.getCurrentCount()) : null)
                .setActualType(2);
    }

    private BigDecimal getItemDiscountAfterPrice(DineInItemDTO dineInItemDTO, boolean asGift) {
        if (asGift) {
            return BigDecimal.ZERO;
        }
        if (dineInItemDTO.getPriceChangeType() != null && dineInItemDTO.getPriceChangeType() == 2) {
            // 折扣需要补偿价格
            BigDecimal reduicePrice = dineInItemDTO.getOriginalPrice()
                    .multiply(AmountCalculationUtil.ITEM_DISCOUNT_PERCENT_BASE.subtract(new BigDecimal
                                    (dineInItemDTO.getDiscountPercent()))
                            .divide(AmountCalculationUtil.ITEM_DISCOUNT_PERCENT_BASE, 2, RoundingMode.HALF_DOWN))
                    .multiply(dineInItemDTO.getCurrentCount());
            dineInItemDTO.setItemPrice(dineInItemDTO.getItemPrice().subtract(reduicePrice).setScale(2, RoundingMode.HALF_UP));
        }
        // 验券加商品 : 优惠价 = 原价
        if (StringUtils.isNotEmpty(dineInItemDTO.getCouponCode())) {
            return dineInItemDTO.getItemPrice();
        }
        if (Objects.isNull(dineInItemDTO.getDiscountTotalPrice())) {
            return dineInItemDTO.getItemPrice();
        }
        return dineInItemDTO.getDiscountTotalPrice();
    }


    /**
     * Item价格的和
     */
    BigDecimal extractItemPriceTotal(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        log.info("计算商品合计:{}", JacksonUtils.writeValueAsString(dineinOrderDetailRespDTO));
        BigDecimal itemTotal = BigDecimal.ZERO;
        for (DineInItemDTO itemDTO : dineinOrderDetailRespDTO.getDineInItemDTOS()) {
            if (ItemTypeEnum.WEIGH.getCode() != itemDTO.getItemType()) {
                itemTotal = itemTotal.add(itemDTO.getBeforeItemPrice());
            } else {
                itemTotal = itemTotal.add(itemDTO.getCurrentCount().multiply(itemDTO.getPrice())).add(itemDTO.getSingleItemAttrTotal());
            }
            log.info("计算商品合计:商品={},itemTotal={}", itemDTO.getItemName(), itemTotal);
            List<FreeItemDTO> freeItemList = itemDTO.getFreeItemDTOS();
            if (CollectionUtils.isEmpty(freeItemList)) {
                continue;
            }
            if (ItemTypeEnum.WEIGH.getCode() != itemDTO.getItemType()) {
                itemTotal = itemTotal.add(freeItemList.stream().map(FreeItemDTO::getItemPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
            } else {
                itemTotal = itemTotal.add(freeItemList.stream().map(e -> e.getCount().multiply(e.getPrice())).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        }
        return itemTotal;
    }

    /**
     * 获取附加费
     */
    protected List<AdditionalCharge> getAppendFee(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        if (!BigDecimalUtil.greaterThanZero(dineinOrderDetailRespDTO.getAppendFee())) {
            return Collections.emptyList();
        }
        List<AppendFeeDetailDTO> appendFeeDetailDTOS = dineinOrderDetailRespDTO.getOrderFeeDetailDTO()
                .getAppendFeeDetailDTOS();
        if (CollectionUtils.isEmpty(appendFeeDetailDTOS)) {
            return Collections.emptyList();
        }
        return appendFeeDetailDTOS.stream()
                .map(appendFeeDetailDTO -> new AdditionalCharge(appendFeeDetailDTO.getName(),
                        appendFeeDetailDTO.getAmount(), appendFeeDetailDTO.getUnitPrice()))
                .collect(Collectors.toList());
    }

    /**
     * 优惠列表
     */
    List<ReduceRecord> extractReduceRecordList(DineinOrderDetailRespDTO dineinOrderDetailRespDTO) {
        return Optional.ofNullable(dineinOrderDetailRespDTO.getDiscountFeeDetailDTOS())
                .map(discountFeeDetailDTOS -> discountFeeDetailDTOS.stream()
                        .map(discountFeeDetailDTO -> {
                            ReduceRecord reduceRecord = new ReduceRecord();
                            String discountName = discountFeeDetailDTO.getDiscountName();
                            if (discountFeeDetailDTO.getDiscountType().equals(DiscountTypeEnum.WHOLE.getCode())) {
                                discountName = discountName + "[" + discountFeeDetailDTO.getDiscount() + "]折";
                            }
                            reduceRecord.setName(discountName);
                            reduceRecord.setAmount(discountFeeDetailDTO.getDiscountFee());
                            return reduceRecord;
                        })
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    /**
     * 支付记录
     */
    List<PayRecord> requirePayRecordList(DineinOrderDetailRespDTO order) {

        List<PayRecord> payRecords = Optional.ofNullable(order.getActuallyPayFeeDetailDTOS())
                .map(actuallyPayFeeDetailDTOS1 -> actuallyPayFeeDetailDTOS1.stream()
                        .map(actuallyPayFeeDetailDTO -> {
                            PayRecord payRecord = new PayRecord();
                            payRecord.setPayName(actuallyPayFeeDetailDTO.getPaymentTypeName());
                            BigDecimal amount = actuallyPayFeeDetailDTO.getAmount();
                            if (actuallyPayFeeDetailDTO.getPaymentType().equals(PaymentTypeEnum.CASH.getCode()) &&
                                    BigDecimalUtil.greaterThanZero(order.getChangeFee())) {
                                amount = amount.add(order.getChangeFee());
                            }
                            payRecord.setAmount(amount);
                            //金额为负时标记为退款
                            if (BigDecimalUtil.lessThanZero(amount)) {
                                payRecord.setPayName("退款(" + actuallyPayFeeDetailDTO.getPaymentTypeName() + ")");
                            }
                            return payRecord;
                        })
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
        return payRecords;
    }

    /**
     * 设置桌台信息
     */
    private void setTableInfo(PrintPreCoTableCbDTO.PrintTableDTO printTableDTO, DineinOrderDetailRespDTO
            dineinOrderDetailRespDTO) {
        printTableDTO.setTotal(extractItemPriceTotal(dineinOrderDetailRespDTO));
        printTableDTO.setPrintItemRecordList(extractItemRecords(dineinOrderDetailRespDTO, false, true));
        printTableDTO.setDiscountAfterTotal(getDiscountAfterTotal(printTableDTO.getPrintItemRecordList()));
        printTableDTO.setOrderRemark(dineinOrderDetailRespDTO.getRemark());
        if (dineinOrderDetailRespDTO.getTradeMode().equals(TradeModeEnum.DINE.getMode())) {
            printTableDTO.setMarkNo(dineinOrderDetailRespDTO.getDiningTableName());
        } else {
            printTableDTO.setMarkNo(dineinOrderDetailRespDTO.getMark());
        }
        printTableDTO.setOrderNo(dineinOrderDetailRespDTO.getOrderNo());
        printTableDTO.setPersonNumber(dineinOrderDetailRespDTO.getGuestCount());
        printTableDTO.setOpenTableTime(DateTimeUtils.localDateTime2Mills(dineinOrderDetailRespDTO.getGmtCreate()));
    }

    List<PrintCoTableCbDTO.PrintTableDTO> getPrintTableOfCheckout(DineinOrderDetailRespDTO mainOrder) {
        List<PrintCoTableCbDTO.PrintTableDTO> printTableDTOS = new ArrayList<>();
        PrintCoTableCbDTO.PrintTableDTO printTableDTO = new PrintCoTableCbDTO.PrintTableDTO();
        setTableInfo(printTableDTO, mainOrder);
        printTableDTO.setCheckOutTime(DateTimeUtils.localDateTime2Mills(mainOrder.getCheckoutTime()));

        printTableDTOS.add(printTableDTO);
        if (!CollectionUtil.isEmpty(mainOrder.getSubOrderDetails())) {
            for (DineinOrderDetailRespDTO subOrder : mainOrder.getSubOrderDetails()) {
                printTableDTO = new PrintCoTableCbDTO.PrintTableDTO();
                setTableInfo(printTableDTO, subOrder);
                printTableDTO.setCheckOutTime(DateTimeUtils.localDateTime2Mills(mainOrder.getCheckoutTime()));
                printTableDTOS.add(printTableDTO);
            }
        }
        return printTableDTOS;
    }

    /**
     * 设置打印体
     */
    List<PrintPreCoTableCbDTO.PrintTableDTO> getPrintTableOfPreCheckout(DineinOrderDetailRespDTO
                                                                                dineinOrderDetailRespDTO) {
        List<PrintPreCoTableCbDTO.PrintTableDTO> printTableDTOS = new ArrayList<>();
        PrintPreCoTableCbDTO.PrintTableDTO printTableDTO = new PrintPreCoTableCbDTO.PrintTableDTO();
        setTableInfo(printTableDTO, dineinOrderDetailRespDTO);
        printTableDTOS.add(printTableDTO);
        if (!CollectionUtil.isEmpty(dineinOrderDetailRespDTO.getSubOrderDetails())) {
            for (DineinOrderDetailRespDTO subOrderDetail : dineinOrderDetailRespDTO.getSubOrderDetails()) {
                printTableDTO = new PrintPreCoTableCbDTO.PrintTableDTO();
                setTableInfo(printTableDTO, subOrderDetail);
                printTableDTOS.add(printTableDTO);
            }
        }
        return printTableDTOS;
    }


}