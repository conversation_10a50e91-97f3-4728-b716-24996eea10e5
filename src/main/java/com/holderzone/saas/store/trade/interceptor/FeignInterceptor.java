package com.holderzone.saas.store.trade.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FeignInterceptor
 * @date 2018/09/13 16:19
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Slf4j
@Configuration
public class FeignInterceptor implements RequestInterceptor {
    public static final String LANGUAGE = "language";
    @Override
    public void apply(RequestTemplate template) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return;
        }

        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        template.header(USER_INFO, request.getHeader(USER_INFO));
        template.header(LANGUAGE, request.getHeader(LANGUAGE));
    }
}
