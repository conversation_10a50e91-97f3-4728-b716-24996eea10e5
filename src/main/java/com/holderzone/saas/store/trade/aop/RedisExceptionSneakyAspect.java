package com.holderzone.saas.store.trade.aop;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;

@Aspect
@Component
@Slf4j
public class RedisExceptionSneakyAspect {

    @Pointcut("@annotation(com.holderzone.saas.store.trade.anno.ExceptionSneaky)")
    public void sneaky() {
    }

    @Around("sneaky()")
    public Object whetherPay(ProceedingJoinPoint point) {
        try {
            return point.proceed();
        } catch (Throwable e) {
            log.error("redis方法异常",e);
        }
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        Class<?> returnType = method.getReturnType();
        if(returnType.isAssignableFrom(List.class)){
            return Collections.emptyList();
        }
        if(returnType.isAssignableFrom(Boolean.class)){
            return Boolean.FALSE;
        }
        return null;
    }
}
