package com.holderzone.saas.store.trade.mapper;

import com.holderzone.saas.store.dto.business.manage.HandoverPayDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessDailyMapper
 * @date 2019/02/12 17:37
 * @description 营业日报
 * @program holder-saas-store-trade
 */
public interface BusinessDailyMapper {
    List<ItemRespDTO> freeReturn(@Param("dto") DailyReqDTO request, @Param("type") int type);

    List<ItemRespDTO> goods(@Param("dto") DailyReqDTO request);

    //原来的商品销售统计接口(goods)就是将套餐与单品分开查询后union在一起，
    //现在需要增加将套餐与单品分开查询（22818），直接拆分为查询单品的singleGoods和查询套餐的packageGoods两个方法

    List<ItemRespDTO> singleGoods(@Param("dto") DailyReqDTO request);

    List<ItemRespDTO> packageGoods(@Param("dto") DailyReqDTO request);

    List<AttrItemRespDTO> attr(@Param("dto") DailyReqDTO request);

    List<ItemRespDTO> classify(@Param("dto") DailyReqDTO request);

    List<DiningTypeRespDTO> diningType(@Param("dto") DailyReqDTO request);

    MemberConsumeRespDTO memberConsume(@Param("dto") DailyReqDTO request);

    List<GatherRespDTO> gather(@Param("dto") DailyReqDTO request);

    OverviewRespDTO overview(@Param("dto") DailyReqDTO request);

    /**
     * 收款方式分组统计
     *
     * @param request
     * @return
     */
    List<AmountItemDTO> paymentTypeCount(@Param("dto") DailyReqDTO request);

    /**
     * 优惠方式分组统计
     *
     * @param request
     * @return
     */
    List<AmountItemDTO> discountTypeCount(@Param("dto") DailyReqDTO request);

    /**
     * 销售收入统计
     *
     * @param request
     * @return
     */
    BigDecimal[] consumerAmount(@Param("dto") DailyReqDTO request);

    /**
     * 销售收入(团购验券)统计
     *
     * @param request
     * @return
     */
    List<AmountItemDTO> consumerGrouponAmount(@Param("dto") DailyReqDTO request);

    /**
     * 余出金额统计
     */
    BigDecimal calExcessAmount(@Param("dto") DailyReqDTO request);

    HandoverPayDTO handover(@Param("dto") HandoverPayQueryDTO request);

    /**
     * 查询收银员信息
     */
    List<String> getCheckoutStaffs(@Param("dto") DailyReqDTO request);

    /**
     * 查询退款收银员信息
     */
    List<String> getRefundCheckoutStaffs(@Param("dto") DailyReqDTO request);

    /**
     * 查询美团团购商家预计金额
     */
    BigDecimal getMtGrouponEstimatedAmount(@Param("dto") DailyReqDTO request);

    /**
     * 退款统计报表
     */
    List<RefundAmountDTO> refund(@Param("dto") DailyReqDTO request);

    /**
     * 营业概况统计新增
     */
    List<OverviewStatisticRespDTO> overviewStatistic(@Param("dto") DailyReqDTO request);
}
