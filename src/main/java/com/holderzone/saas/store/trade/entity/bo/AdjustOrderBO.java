package com.holderzone.saas.store.trade.entity.bo;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.trade.entity.domain.AdjustOrderDO;
import com.holderzone.saas.store.trade.entity.domain.AdjustOrderDetailsDO;
import com.holderzone.saas.store.trade.entity.domain.ItemAttrDO;
import com.holderzone.saas.store.trade.entity.domain.OrderDO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 调整单业务对象
 */
@Data
public class AdjustOrderBO implements Serializable {

    private static final long serialVersionUID = -2754775077640497488L;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单营业日
     */
    private LocalDate businessDay;

    /**
     * 调整单
     */
    private AdjustOrderDO order;

    /**
     * 调整单明细
     */
    private List<AdjustOrderDetailsDO> details;

    /**
     * 属性
     */
    private List<ItemAttrDO> attrList;

    /**
     * 订单明细guids
     */
    private List<Long> orderItemGuidList;

    /**
     * 原订单需要调整的商品明细
     */
    private List<DineInItemDTO> orderItemDTOList;

    /**
     * 调整后的商品明细
     */
    private List<DineInItemDTO> adjustOrderItemDTOList;

    /**
     * 堂食原订单信息
     */
    private OrderDO originalOrder;

    /**
     * 外卖原订单信息
     */
    private TakeoutOrderDTO takeoutOrderDetail;
}
