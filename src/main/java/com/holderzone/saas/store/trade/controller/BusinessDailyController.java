package com.holderzone.saas.store.trade.controller;

import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;
import com.holderzone.saas.store.trade.service.BusinessDailyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessDailyController
 * @date 2019/02/12 14:53
 * @description 营业日报接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/daily")
@Api(description = "营业日报接口")
@Slf4j
public class BusinessDailyController {

    private BusinessDailyService businessDailyService;

    @Autowired
    public BusinessDailyController(BusinessDailyService businessDailyService) {
        this.businessDailyService = businessDailyService;
    }

    @ApiOperation(value = "营业概况", notes = "营业概况")
    @PostMapping("overview")
    public OverviewRespDTO overview(@RequestBody @Valid DailyReqDTO request) {
        return businessDailyService.overview(request);
    }

    @ApiOperation(value = "收款统计", notes = "收款统计")
    @PostMapping("gather")
    public List<GatherRespDTO> gather(@RequestBody @Valid DailyReqDTO request) {
        return businessDailyService.gather(request);
    }

    @ApiOperation(value = "会员消费统计", notes = "会员消费统计")
    @PostMapping("member_consumer")
    public MemberConsumeRespDTO memberConsume(@RequestBody @Valid DailyReqDTO request) {
        return businessDailyService.memberConsume(request);
    }

    @ApiOperation(value = "用餐类型统计", notes = "用餐类型统计")
    @PostMapping("dining_type")
    public List<DiningTypeRespDTO> diningType(@RequestBody @Valid DailyReqDTO request) {
        return businessDailyService.diningType(request);
    }

    @ApiOperation(value = "退款统计报表", notes = "退款统计报表")
    @PostMapping("refund")
    public RefundRespDTO refund(@RequestBody @Valid DailyReqDTO request) {
        return businessDailyService.refund(request);
    }

    @ApiOperation(value = "分类销售统计", notes = "分类销售统计")
    @PostMapping("classify")
    public List<ItemRespDTO> classify(@RequestBody @Valid DailyReqDTO request) {
        return businessDailyService.classify(request);
    }

    @ApiOperation(value = "商品销售统计", notes = "商品销售统计")
    @PostMapping("goods")
    public List<ItemRespDTO> goods(@RequestBody @Valid DailyReqDTO request) {
        return businessDailyService.goods(request);
    }

    @ApiOperation(value = "属性销售统计", notes = "属性销售统计")
    @PostMapping("attr")
    public List<AttrItemRespDTO> attr(@RequestBody @Valid DailyReqDTO request) {
        return businessDailyService.attr(request);
    }

    @ApiOperation(value = "退菜统计", notes = "退菜统计")
    @PostMapping("return_vegetables")
    public List<ItemRespDTO> returnVegetables(@RequestBody @Valid DailyReqDTO request) {
        return businessDailyService.freeReturn(request, 1);
    }

    @ApiOperation(value = "赠菜统计", notes = "赠菜统计")
    @PostMapping("dish_giving")
    public List<ItemRespDTO> dishGiving(@RequestBody @Valid DailyReqDTO request) {
        return businessDailyService.freeReturn(request, 2);
    }


}