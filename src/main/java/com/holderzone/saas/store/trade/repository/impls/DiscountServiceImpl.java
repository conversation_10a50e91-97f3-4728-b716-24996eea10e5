package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.domain.DiscountDO;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.mapper.DiscountMapper;
import com.holderzone.saas.store.trade.repository.interfaces.DiscountService;
import com.holderzone.saas.store.trade.utils.BigDecimalUtil;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 订单优惠记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Service
@AllArgsConstructor
@Slf4j
public class DiscountServiceImpl extends ServiceImpl<DiscountMapper, DiscountDO> implements DiscountService {

    private final DynamicHelper dynamicHelper;

    @Override
    public List<DiscountDO> listByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<DiscountDO>().eq(DiscountDO::getOrderGuid, orderGuid));

    }

    @Override
    public List<DiscountDO> listByOrderGuids(List<String> orderGuid) {
        if (CollectionUtil.isEmpty(orderGuid)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<DiscountDO>().in(DiscountDO::getOrderGuid, orderGuid));
    }

    @Override
    public void removeByOrderGuids(List<Long> subOrderGuids) {
        if (CollectionUtil.isEmpty(subOrderGuids)) {
            return;
        }
        remove(new LambdaQueryWrapper<DiscountDO>().in(DiscountDO::getOrderGuid, subOrderGuids));
    }

    @Override
    public void deleteByOrderGuids(List<String> orderGuids) {
        if (CollectionUtil.isEmpty(orderGuids)) {
            return;
        }
        remove(new LambdaQueryWrapper<DiscountDO>().in(DiscountDO::getOrderGuid, orderGuids));
    }

    @Override
    public DiscountDO getGrouponDiscount(String orderGuid) {
        return getOne(new LambdaQueryWrapper<DiscountDO>().eq(DiscountDO::getOrderGuid, orderGuid).eq
                (DiscountDO::getDiscountType, DiscountTypeEnum.GROUPON.getCode()));
    }

    @Override
    public DiscountDO getGrouponDiscountByType(String orderGuid, Integer groupType) {
        return getOne(new LambdaQueryWrapper<DiscountDO>().eq(DiscountDO::getOrderGuid, orderGuid).eq
                (DiscountDO::getDiscountType, groupType));
    }

    @Override
    public DiscountDO getThirdActivityDiscount(String orderGuid) {
        return getOne(new LambdaQueryWrapper<DiscountDO>()
                .eq(DiscountDO::getOrderGuid, orderGuid)
                .eq(DiscountDO::getDiscountType, DiscountTypeEnum.THIRD_ACTIVITY.getCode()));
    }

    @Override
    public DiscountDO getFollowRedPacketDiscount(String orderGuid) {
        return getOne(new LambdaQueryWrapper<DiscountDO>().eq(DiscountDO::getOrderGuid, orderGuid).eq
                (DiscountDO::getDiscountType, DiscountTypeEnum.FOLLOW_RED_PACKET.getCode()));
    }

    @Override
    public List<DiscountDO> getGrouponDiscount(List<Long> orderGuids) {
        if (CollectionUtil.isEmpty(orderGuids)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<DiscountDO>().in(DiscountDO::getOrderGuid, orderGuids)
                .in(DiscountDO::getDiscountType, Lists.newArrayList(DiscountTypeEnum.GROUPON.getCode(),
                        DiscountTypeEnum.DOU_YIN_GROUPON.getCode(), DiscountTypeEnum.ALIPAY_GROUPON.getCode(),DiscountTypeEnum.ABC_GROUPON.getCode())));
    }

    @Override
    public List<DiscountDO> getUseDiscountListByOrderGuid(String orderGuid) {
        if (StringUtils.isEmpty(orderGuid)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<DiscountDO>().eq(DiscountDO::getOrderGuid, orderGuid).ne(DiscountDO::getDiscountFee, BigDecimal.ZERO));
    }

    @Override
    public List<DiscountDO> listHasGrouponCompleteOrder(LocalDateTime startTime, String orderGuid) {
        return baseMapper.listHasGrouponCompleteOrder(startTime, orderGuid);
    }

    @Override
    public void handleDiscount(String orderGuid, Integer discountType, BigDecimal deductionAmount) {
        // fixBug:解决没进入结账页面没生成优惠信息，进行初始化
        if (!BigDecimalUtil.greaterThanZero(deductionAmount)) {
            return;
        }
        DiscountDO grouponDiscountDO = getGrouponDiscountByType(orderGuid, discountType);
        if (ObjectUtils.isEmpty(grouponDiscountDO)) {
            Long grouponDiscountGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_DISCOUNT);
            DiscountDO discountDO = new DiscountDO();
            discountDO.setGuid(grouponDiscountGuid);
            DiscountTypeEnum discountTypeEnum = DiscountTypeEnum.get(discountType);
            discountDO.setDiscountType(discountTypeEnum.getCode());
            discountDO.setDiscountName(discountTypeEnum.getDesc());
            discountDO.setStaffGuid(UserContextUtils.getUserGuid());
            discountDO.setStaffName(UserContextUtils.getUserName());
            discountDO.setStoreGuid(UserContextUtils.getStoreGuid());
            discountDO.setStoreName(UserContextUtils.getStoreName());
            discountDO.setOrderGuid(Long.valueOf(orderGuid));
            discountDO.setDiscountFee(deductionAmount);
            this.save(discountDO);
        } else {
            grouponDiscountDO.setDiscountFee(grouponDiscountDO.getDiscountFee().add(deductionAmount));
            this.updateById(grouponDiscountDO);
        }
    }

    @Override
    public List<AmountItemDTO> handoverDiscountType(HandoverPayQueryDTO handoverPayQueryDTO) {
        return baseMapper.handoverDiscountType(handoverPayQueryDTO);
    }
}
