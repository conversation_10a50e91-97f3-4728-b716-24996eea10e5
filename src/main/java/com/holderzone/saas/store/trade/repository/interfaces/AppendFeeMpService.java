package com.holderzone.saas.store.trade.repository.interfaces;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.trade.entity.domain.AppendFeeDO;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 附加费记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-22
 */
public interface AppendFeeMpService extends IService<AppendFeeDO> {

    List<AppendFeeDO> listByOrderGuid(Serializable orderGuid);

    List<AppendFeeDO> listByOrderGuids(Collection<? extends Serializable> idList);

    void updateRefundAmountByOrderGuid(Serializable orderGuid, AppendFeeDO appendFeeDO);

    void deleteByOrderGuid(Serializable orderGuid);

    void deleteByOrderGuids(List<String> orderGuids);
}
