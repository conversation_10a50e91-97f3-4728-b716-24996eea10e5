package com.holderzone.saas.store.trade.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.OrderTableBillDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.OrderTableDTO;
import com.holderzone.saas.store.dto.trade.OrderTableGuestDTO;
import com.holderzone.saas.store.dto.trade.OrderTableVO;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.trade.entity.enums.TradeModeEnum;
import com.holderzone.saas.store.trade.entity.enums.UpperStateEnum;
import com.holderzone.saas.store.trade.repository.feign.MessageClientService;
import com.holderzone.saas.store.trade.repository.feign.TableClientService;
import com.holderzone.saas.store.trade.service.CombineOrderService;
import com.holderzone.saas.store.trade.service.DineInService;
import com.holderzone.saas.store.trade.service.OrderDetailService;
import com.holderzone.saas.store.trade.service.OrderTableBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderTableBillServiceImpl implements OrderTableBillService {

    @Resource
    private OrderDetailService orderDetailService;

    @Resource
    private TableClientService tableClientService;

    @Resource
    private DineInService dineInService;

    @Resource
    private CombineOrderService combineOrderService;

    @Resource
    private MessageClientService bizMsgRpcClient;

    /**
     * 手动清台处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderTableBillDTO dealEnableHandleClose(BillPayReqDTO billPayReqDTO) {
        log.info("手动清台处理billPayReqDTO参数：{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        //查询订单
        OrderDTO orderDO = orderDetailService.findByOrderGuid(billPayReqDTO.getOrderGuid());
        log.info("订单信息：{}", JacksonUtils.writeValueAsString(orderDO));
        if (orderDO.getTradeMode() != TradeModeEnum.DINEIN.getCode()) {
            return null;
        }
        // 正餐
        if (UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState())
                && !StringUtils.isEmpty(orderDO.getMainOrderGuid())
                && !"0".equals(orderDO.getMainOrderGuid())) {
            billPayReqDTO.setMainOrderGuid(orderDO.getMainOrderGuid());
        }
        OrderTableBillDTO dto = new OrderTableBillDTO();
        dto.setTableGuid(orderDO.getDiningTableGuid());
        OrderTableDTO orderTableDTO = orderDetailService.findTableByOrderGuid(billPayReqDTO.getOrderGuid());
        log.info("手动更新桌台参数：{}", JacksonUtils.writeValueAsString(orderTableDTO));
        //转台
        OrderTableVO orderTableVO = tableClientService.updateTableOrder(orderTableDTO);
        log.info("手动更新桌台返参：{}", JacksonUtils.writeValueAsString(orderTableVO));
        if (Objects.nonNull(orderTableVO)) {
            dto.setOrderTableType(orderTableVO.getOrderTableType());
            //单台订单
            if (orderTableVO.getOrderTableType() == 0) {
                singleOrder(billPayReqDTO, orderTableVO);
                dto.setOrderGuid(orderTableVO.getOrderGuid());
            } else if (orderTableVO.getOrderTableType() == 1) {
                //联台订单
                associatedOrder(billPayReqDTO, orderTableVO, dto);
            } else {
                //并台订单
                combineOrder(billPayReqDTO, orderTableVO, dto, orderDO);
                dto.setOrderTableGuestDTOS(orderTableVO.getOrderTableGuestDTOS());
            }
            //桌台更新通知
            List<String> tableGuidList = orderTableVO.getOrderTableGuestDTOS().stream().map(OrderTableGuestDTO::getTableGuid).collect(Collectors.toList());
            sendMsg(billPayReqDTO, JacksonUtils.writeValueAsString(tableGuidList));
            log.info("手动清台处理返回参数：{}", JacksonUtils.writeValueAsString(dto));
            return dto;
        }
        return null;
    }

    public void sendMsg(BaseDTO baseDTO, String content) {
        log.info("发送桌位状态变化通知");
        BusinessMessageDTO messageDTO = BusinessMessageDTO.builder()
                .subject("桌位状态变化通知")
                .messageType(BusinessMsgTypeEnum.TABLE_STATE_MSG_TYPE.getId())
                .detailMessageType(BusinessMsgTypeEnum.TABLE_CHANGED.getId())
                .content(content)
                .platform("2")
                .storeGuid(baseDTO.getStoreGuid())
                .storeName(baseDTO.getStoreName())
                .build();
        bizMsgRpcClient.notifyMsg(messageDTO);
    }

    private void combineOrder(BillPayReqDTO billPayReqDTO,
                              OrderTableVO orderTableVO,
                              OrderTableBillDTO dto,
                              OrderDTO orderDO) {
        List<TableInfoDTO> tableInfoDTOS = Lists.newArrayList();
        TableOrderCombineDTO tableOrderCombineDTO = new TableOrderCombineDTO();
        BeanUtils.copyProperties(billPayReqDTO, tableOrderCombineDTO);
        for (OrderTableGuestDTO tableGuestDTO : orderTableVO.getOrderTableGuestDTOS()) {
            CreateDineInOrderReqDTO createDineInOrderReqDTO =
                    getCreateDineInOrderReqDTO(billPayReqDTO, tableGuestDTO.getOrderGuid(), tableGuestDTO);

            if (orderDO.getDiningTableGuid().equals(tableGuestDTO.getTableGuid())) {
                log.info("当前主台新订单：{}", tableGuestDTO.getOrderGuid());
                dto.setOrderGuid(tableGuestDTO.getOrderGuid());
            }

            TableInfoDTO tableInfoDTO = new TableInfoDTO();
            BeanUtils.copyProperties(billPayReqDTO, tableInfoDTO);
            tableInfoDTO.setOrderGuid(tableGuestDTO.getOrderGuid());
            tableInfoDTO.setFastFood(false);
            tableInfoDTO.setTableGuid(tableGuestDTO.getTableGuid());
            tableInfoDTO.setAreaName(tableInfoDTO.getAreaName());
            tableInfoDTO.setAreaGuid(tableInfoDTO.getAreaGuid());
            tableInfoDTO.setTableName(tableGuestDTO.getDiningTableName());
            if (tableGuestDTO.getIsMain() == 1) {
                tableOrderCombineDTO.setMainOrderGuid(tableGuestDTO.getOrderGuid());
                tableOrderCombineDTO.setMainTableGuid(tableGuestDTO.getTableGuid());
            }else {
                tableInfoDTOS.add(tableInfoDTO);
            }
            dineInService.createOrder(createDineInOrderReqDTO);
        }

        //通知订单服务桌台已经并桌
        tableOrderCombineDTO.setTableInfoDTOS(tableInfoDTOS);
        tableOrderCombineDTO.setIgnoreVerifySameOrderFlag(true);
        log.info("并桌请求参数：{}", JacksonUtils.writeValueAsString(tableOrderCombineDTO));
        combineOrderService.combine(tableOrderCombineDTO);
    }

    private void associatedOrder(BillPayReqDTO billPayReqDTO, OrderTableVO orderTableVO, OrderTableBillDTO dto) {
        // 获取主台信息
        OrderTableGuestDTO mainTableOrderDTO = orderTableVO.getOrderTableGuestDTOS().stream()
                .filter(in -> in.getIsMain() == 1)
                .collect(Collectors.toList()).get(0);
        dto.setOrderGuid(orderTableVO.getOrderGuid());
        CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        BeanUtils.copyProperties(billPayReqDTO, createDineInOrderReqDTO);
        createDineInOrderReqDTO.setGuid(orderTableVO.getOrderGuid());
        createDineInOrderReqDTO.setOriginalOrderGuid(billPayReqDTO.getOrderGuid());
        if (!StringUtils.isEmpty(billPayReqDTO.getMainOrderGuid())) {
            createDineInOrderReqDTO.setOriginalOrderGuid(billPayReqDTO.getMainOrderGuid());
        }
        createDineInOrderReqDTO.setAssociatedFlag(true);
        createDineInOrderReqDTO.setAssociatedSn(String.valueOf(mainTableOrderDTO.getAssociatedTimes()));
        createDineInOrderReqDTO.setAreaName(mainTableOrderDTO.getAreaName());
        createDineInOrderReqDTO.setDiningTableGuid(mainTableOrderDTO.getTableGuid());
        createDineInOrderReqDTO.setDiningTableName(mainTableOrderDTO.getDiningTableName());
        createDineInOrderReqDTO.setGuestCount(mainTableOrderDTO.getGuestCount());
        createDineInOrderReqDTO.setAreaGuid(mainTableOrderDTO.getAreaGuid());
        // 联台桌台列表
        Map<String, String> associatedTableMap = orderTableVO.getOrderTableGuestDTOS()
                .stream().collect(Collectors.toMap(OrderTableGuestDTO::getTableGuid,
                        i -> i.getAreaName() + "-" + i.getDiningTableName()));
        createDineInOrderReqDTO.setAssociatedTableGuids(Lists.newArrayList());
        createDineInOrderReqDTO.setAssociatedTableNames(Lists.newArrayList());

        List<String> tableGuids = orderTableVO.getOrderTableGuestDTOS()
                .stream().map(OrderTableGuestDTO::getTableGuid).collect(Collectors.toList());

        for (String tableGuid : tableGuids) {
            createDineInOrderReqDTO.getAssociatedTableGuids().add(tableGuid);
            createDineInOrderReqDTO.getAssociatedTableNames().add(associatedTableMap.get(tableGuid));
        }
        log.info("联台下单入参:{}", JacksonUtils.writeValueAsString(createDineInOrderReqDTO));
        dineInService.createOrder(createDineInOrderReqDTO);
    }

    private void singleOrder(BillPayReqDTO billPayReqDTO, OrderTableVO orderTableVO) {
        OrderTableGuestDTO orderTableGuest = orderTableVO.getOrderTableGuestDTOS().get(0);
        CreateDineInOrderReqDTO createDineInOrderReqDTO = getCreateDineInOrderReqDTO(billPayReqDTO, orderTableVO.getOrderGuid(), orderTableGuest);
        dineInService.createOrder(createDineInOrderReqDTO);
    }

    private static CreateDineInOrderReqDTO getCreateDineInOrderReqDTO(BillPayReqDTO billPayReqDTO,
                                                                      String orderGuid,
                                                                      OrderTableGuestDTO orderTableGuest) {
        CreateDineInOrderReqDTO createDineInOrderReqDTO = new CreateDineInOrderReqDTO();
        BeanUtils.copyProperties(billPayReqDTO, createDineInOrderReqDTO);
        createDineInOrderReqDTO.setGuid(orderGuid);
        createDineInOrderReqDTO.setOriginalOrderGuid(billPayReqDTO.getOrderGuid());
        if (!StringUtils.isEmpty(billPayReqDTO.getMainOrderGuid())) {
            createDineInOrderReqDTO.setOriginalOrderGuid(billPayReqDTO.getMainOrderGuid());
        }
        createDineInOrderReqDTO.setDiningTableGuid(orderTableGuest.getTableGuid());
        createDineInOrderReqDTO.setDiningTableName(orderTableGuest.getDiningTableName());
        createDineInOrderReqDTO.setAreaName(orderTableGuest.getAreaName());
        createDineInOrderReqDTO.setAreaGuid(orderTableGuest.getAreaGuid());
        createDineInOrderReqDTO.setGuestCount(orderTableGuest.getGuestCount());
        createDineInOrderReqDTO.setReserveOrder(false);
        return createDineInOrderReqDTO;
    }
}
