package com.holderzone.saas.store.trade.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.saas.store.trade.entity.domain.OrderWaiterDO;
import com.holderzone.saas.store.trade.entity.query.OrderWaiterQueryDetails;
import com.holderzone.saas.store.trade.entity.read.OrderReadTotalDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;

/**
 * <AUTHOR> R
 * @date 2020/11/18 17:56
 * @description
 */
public interface OrderWaiterMapper extends BaseMapper<OrderWaiterDO> {
    /***
     *  根据订单guid 更新 服务员所服务订单金额
     * @param orderGuid 订单guid
     * @param orderFee 订单金额
     * @param storeGuid 服务门店Guid
     * @param storeName 服务门店
     * @return 返回结果
     */
    @Update("UPDATE hst_order_waiter SET order_fee = #{orderFee},store_guid = #{storeGuid},store_name = #{storeName} WHERE order_guid = #{orderGuid}")
    int updateOrderFeeByOrderGuid(String orderGuid, BigDecimal orderFee, String storeGuid, String storeName);

    /***
     *  分页统计服务员服务总数（未统计 无操作员项）
     * @param iPage 分页
     * @param orderWaiterQueryDetails 查询条件
     * @return
     */
    IPage<OrderReadTotalDO> pageQueryOrderWaiterTotal(IPage<OrderReadTotalDO> iPage, @Param("orderWaiterQueryDetails") OrderWaiterQueryDetails orderWaiterQueryDetails);
}