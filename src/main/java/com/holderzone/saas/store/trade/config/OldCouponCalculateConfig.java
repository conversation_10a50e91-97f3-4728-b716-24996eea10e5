package com.holderzone.saas.store.trade.config;

import com.holderzone.framework.util.DateTimeUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.Objects;


/**
 * 老版本团购券计算
 */
@Data
@RefreshScope
@Configuration
public class OldCouponCalculateConfig {

    /**
     * 计算时间 以这个时间节点区分 老订单构建订单详情时 走之前的逻辑
     */
    @Value("${coupon.old_calculate_max_time}")
    private String oldCalculateMaxTime;

    public LocalDateTime getOldCalculateMaxDateTime() {
        if (StringUtils.isEmpty(oldCalculateMaxTime)) {
            return null;
        }
        return DateTimeUtils.string2LocalDateTime(oldCalculateMaxTime);
    }

    public boolean isBefore(LocalDateTime checkoutTime) {
        if (Objects.isNull(checkoutTime)) {
            return false;
        }
        LocalDateTime oldCalculateMaxDateTime = getOldCalculateMaxDateTime();
        if (Objects.isNull(oldCalculateMaxDateTime)) {
            return false;
        }
        return checkoutTime.isBefore(oldCalculateMaxDateTime);
    }
}