package com.holderzone.saas.store.trade.repository.impls;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.saas.store.trade.entity.domain.ItemAttrDO;
import com.holderzone.saas.store.trade.mapper.ItemAttrMapper;
import com.holderzone.saas.store.trade.repository.interfaces.ItemAttrService;
import com.holderzone.saas.store.trade.utils.CollectionUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 商品属性 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-22
 */
@Service
public class ItemAttrServiceImpl extends ServiceImpl<ItemAttrMapper, ItemAttrDO> implements ItemAttrService {


    @Override
    public List<ItemAttrDO> listByItemGuids(Collection<? extends Serializable> itemGuids) {
        if (CollectionUtil.isEmpty(itemGuids)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<ItemAttrDO>().in(ItemAttrDO::getOrderItemGuid, itemGuids));
    }

    @Override
    public void deleteByOrderGuid(String orderGuid) {
        remove(new LambdaQueryWrapper<ItemAttrDO>().eq(ItemAttrDO::getOrderGuid, orderGuid));
    }

    @Override
    public void removeByItemGuids(Collection<? extends Serializable> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return;
        }
        remove(new LambdaQueryWrapper<ItemAttrDO>().in(ItemAttrDO::getOrderItemGuid, idList));
    }

    @Override
    public List<ItemAttrDO> findByOrderGuid(String orderGuid) {
        if (StringUtils.isEmpty(orderGuid)) {
            return Lists.newArrayList();
        }
        return list(new LambdaQueryWrapper<ItemAttrDO>().eq(ItemAttrDO::getOrderGuid, orderGuid));
    }

    @Override
    public void deleteByOrderGuids(List<String> orderGuids) {
        if (CollectionUtil.isEmpty(orderGuids)) {
            return;
        }
        remove(new LambdaQueryWrapper<ItemAttrDO>().in(ItemAttrDO::getOrderGuid, orderGuids));
    }
}
