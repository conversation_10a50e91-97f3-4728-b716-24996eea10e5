package com.holderzone.saas.store.trade.service;

import com.holderzone.saas.store.dto.member.pay.RedPacketSettleAccountsVO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.trade.req.PadPayInfoReqDTO;
import com.holderzone.saas.store.trade.entity.domain.OrderAbnormalRecordDO;
import com.holderzone.saas.store.trade.entity.dto.BillPayInfoDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RedisService
 * @date 2018/12/27 11:12
 * @description redis Service
 * @program holder-saas-store-table
 */
public interface RedisService {

    void putTradeVersion(String orderGuid);

    void batchPutTradeVersion(List<String> orderGuids);

    void putAbnormalOrderRecord(String orderGuid, OrderAbnormalRecordDO orderAbnormalRecordDO);

    OrderAbnormalRecordDO getAbnormalOrderRecord(String orderGuid);

    void putBillPayInfo(String orderGuid, BillPayInfoDTO billPayInfoDTO);

    BillPayInfoDTO getBillPayInfo(String orderGuid);

    void deleteBillPayInfo(String orderGuid);

    void putOrderPaymentInitiateMethod(String orderGuid, String paymentInitiateMethod);

    String getOrderPaymentInitiateMethod(String orderGuid);

    void deleteOrderPaymentInitiateMethod(String orderGuid);

    void putOrderPayGuid(String orderGuid, String payGuid);

    String getOrderPayGuid(String orderGuid);

    void deleteOrderPayGuid(String orderGuid);

    /**
     * 加菜商品信息存redis
     *
     * @param key           订单guid+下单表guid，以:分割
     * @param createDineDTO 加菜商品信息
     */
    void putPadOrderAddItemInfo(String key, CreateDineInOrderReqDTO createDineDTO);

    /**
     * 取加菜商品信息
     *
     * @param key 订单guid+下单表guid，以:分割
     * @return 加菜商品信息
     */
    CreateDineInOrderReqDTO getPadOrderAddItemInfo(String key);

    /**
     * 保存pad支付信息到缓存
     *
     * @param orderGuid        订单guid
     * @param padPayInfoReqDTO pad支付信息
     */
    void putPadPayInfo(String orderGuid, PadPayInfoReqDTO padPayInfoReqDTO);

    /**
     * 获取缓存pad支付信息
     *
     * @param orderGuid 订单guid
     * @return pad支付信息
     */
    PadPayInfoReqDTO getPadPayInfo(String orderGuid);

    /**
     * 删除缓存pad支付信息
     *
     * @param orderGuid 订单guid
     */
    void deletePadPayInfo(String orderGuid);

    /**
     * 保存pad转台信息
     *
     * @param redisKey 键
     * @param value    值
     */
    void putPadTurnInfo(String redisKey, String value);

    /**
     * 获取pad转台信息
     *
     * @param redisKey 键
     * @return 值
     */
    String getPadTurnInfo(String redisKey);

    /**
     * 删除pad转台信息
     *
     * @param redisKey 键
     */
    void deletePadTurnInfo(String redisKey);

    /**
     * 获取随行红包信息
     *
     * @param redPacketInfoKey 随行红包信息key
     * @return 随行红包信息
     */
    List<RedPacketSettleAccountsVO> getRedPacketInfo(String redPacketInfoKey);

    /**
     * 删除随行红包信息
     *
     * @param redisKey 随行红包信息key
     */
    void deleteRedPacketInfo(String redisKey);

    /**
     * 获取订单金额
     * 加菜退菜时会存一分到缓存，没有则用数据库的
     *
     * @param orderGuids 订单guid列表
     * @return 订单金额Map
     */
    Map<String, BigDecimal> batchGetOrderFee(List<String> orderGuids);

    void deleteAdvancePay(String orderGUID);
}
