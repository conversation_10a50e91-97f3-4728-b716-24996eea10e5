package com.holderzone.saas.store.trade.repository.external;

import com.holderzone.saas.store.dto.ludou.LudouMemberDTO;
import com.holderzone.saas.store.dto.ludou.LudouMemberPayRespDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;

import java.math.BigDecimal;

/**
 * 麓豆对接
 */
public interface LudouMemberExternalService {

    /**
     * 查询麓豆会员信息
     */
    LudouMemberDTO query(String phone, String ludouUserId);

    /**
     * 麓豆支付
     */
    LudouMemberPayRespDTO pay(BillPayReqDTO billPayReqDTO);

    /**
     * 麓豆退款
     */
    Boolean refund(String orderGuid, String ludouOrderGuid,
                   BigDecimal actuallyPayFee, BigDecimal ludouPayFee,
                   String remark);
}
