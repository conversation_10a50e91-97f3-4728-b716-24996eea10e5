package com.holderzone.saas.store.trade.controller;

import com.holderzone.saas.store.dto.order.OrderWechatDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayLockReqDTO;
import com.holderzone.saas.store.dto.order.request.face.WeChatPayReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.service.CalculateService;
import com.holderzone.saas.store.trade.service.OrderLockService;
import com.holderzone.saas.store.trade.service.WeChatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WeChatOrderController
 * @date 2019/01/04 8:53
 * @description 微信订单接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/we_chat")
@Api(description = "微信订单接口")
@Slf4j
@AllArgsConstructor
public class WeChatOrderController {

    private final WeChatService weChatService;

    private final OrderLockService orderLockService;

    private final CalculateService calculateService;
    private final RedisHelper redisHelper;


    @ApiOperation(value = "批量获取订单详情", notes = "批量获取订单详情")
    @PostMapping("/get_order_detail_list")
    public List<DineinOrderDetailRespDTO> getOrderDetailList(@RequestBody SingleListDTO singleListDTO) {
        return weChatService.getOrderDetailList(singleListDTO);
    }

    @ApiOperation(value = "微信批量获取订单详情", notes = "微信批量获取订单详情")
    @PostMapping("/get_order_details")
    public List<DineinOrderDetailRespDTO> getOrderDetails(@RequestBody SingleListDTO singleListDTO) {
        return weChatService.getOrderDetails(singleListDTO);
    }

    @ApiOperation(value = "获取微信订单详情", notes = "获取微信订单详情")
    @PostMapping("/get_order")
    public OrderWechatDTO getOrder(@RequestBody String orderGuid) {
        return weChatService.getOrder(orderGuid);
    }

    @ApiOperation(value = "获取微信订单是否有菜品", notes = "获取微信订单是否有菜品")
    @PostMapping("/has_item")
    public Boolean hasItem(@RequestBody Long orderGuid) {
        log.info("orderGuid={}", orderGuid);
        return weChatService.hasItem(orderGuid);
    }


    @ApiOperation(value = "微信支付回调", notes = "微信支付回调")
    @PostMapping("/pay")
    public Boolean pay(@RequestBody WeChatPayReqDTO weChatPayReqDTO) {
        log.info("收到微信支付回调 weChatPayReqDTO={}", weChatPayReqDTO);
        String lockKey = "weChatCallback:";
        boolean lockSuccess = false;
        try {
            lockKey = lockKey + weChatPayReqDTO.getPayGuid();
            lockSuccess = redisHelper.setNxEx(lockKey, "1", 60);
            if (!lockSuccess) {
                log.info("重复回调");
                return Boolean.FALSE;
            }
            return weChatService.pay(weChatPayReqDTO);
        } finally {
            if (lockSuccess) {
                redisHelper.delete(lockKey);
            }
        }
    }

    @ApiOperation(value = "微信支付前校验", notes = "微信支付前校验")
    @PostMapping("/prepay")
    public Boolean prepay(@RequestBody WeChatPayLockReqDTO WeChatPayLockReqDTO) {
        return orderLockService.tryLock(WeChatPayLockReqDTO.getOrderGuid());
    }

    @ApiOperation(value = "微信校验优惠券", notes = "微信校验优惠券")
    @PostMapping("/check_volume")
    public DineinOrderDetailRespDTO checkVolume(@RequestBody BillCalculateReqDTO billCalculateReqDTO) {
        return calculateService.checkVolume(billCalculateReqDTO);
    }


}
