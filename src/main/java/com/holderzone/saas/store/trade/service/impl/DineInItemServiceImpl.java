package com.holderzone.saas.store.trade.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingQueryDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateResultRespDTO;
import com.holderzone.saas.store.dto.kds.req.ItemBatchRefundReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemCallUpReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemRefundReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemUrgeReqDTO;
import com.holderzone.saas.store.dto.kds.resp.KitchenItemRespDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.common.*;
import com.holderzone.saas.store.dto.order.request.dinein.CancelFreeItemReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.PriceChangeItemReqDTO;
import com.holderzone.saas.store.dto.order.request.groupon.GrouponReqDTO;
import com.holderzone.saas.store.dto.order.request.item.*;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.print.content.PrintOrderItemDTO;
import com.holderzone.saas.store.dto.table.TableStatusChangeDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import com.holderzone.saas.store.enums.business.DataSettingEnum;
import com.holderzone.saas.store.enums.business.DinnerCouponItemPriceSettingEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.enums.order.OrderStateEnum;
import com.holderzone.saas.store.enums.table.TableStatusChangeEnum;
import com.holderzone.saas.store.enums.trade.StateEnum;
import com.holderzone.saas.store.trade.bo.OrderPackageSubgroupBO;
import com.holderzone.saas.store.trade.bo.ReturnOrFreeItemBO;
import com.holderzone.saas.store.trade.bo.TransferItemBO;
import com.holderzone.saas.store.trade.bo.builder.OrderPackageSubgroupBizBuilder;
import com.holderzone.saas.store.trade.bo.builder.ReturnOrFreeItemBizBuilder;
import com.holderzone.saas.store.trade.builder.TransferItemBizBuilder;
import com.holderzone.saas.store.trade.entity.bo.BatchAddItemBO;
import com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.trade.entity.constant.OrderRedisConstant;
import com.holderzone.saas.store.trade.entity.domain.*;
import com.holderzone.saas.store.trade.entity.enums.*;
import com.holderzone.saas.store.trade.helper.BillVerifyHelper;
import com.holderzone.saas.store.trade.helper.DynamicHelper;
import com.holderzone.saas.store.trade.helper.MessagePushHelper;
import com.holderzone.saas.store.trade.helper.RedisHelper;
import com.holderzone.saas.store.trade.mapper.OrderItemMapper;
import com.holderzone.saas.store.trade.mapper.PadOrderMapper;
import com.holderzone.saas.store.trade.repository.feign.*;
import com.holderzone.saas.store.trade.repository.impls.ItemAttrServiceImpl;
import com.holderzone.saas.store.trade.repository.interfaces.*;
import com.holderzone.saas.store.trade.service.*;
import com.holderzone.saas.store.trade.service.mq.KdsMqService;
import com.holderzone.saas.store.trade.transform.LocalTransform;
import com.holderzone.saas.store.trade.transform.OrderTransform;
import com.holderzone.saas.store.trade.utils.*;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.trade.entity.constant.GuidKeyConstant.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className
 * @date 2018/09/04 16:10
 * @description //TODO
 * @program holder-saas-store-order
 */
@Service
@Log4j2
public class DineInItemServiceImpl extends ServiceImpl<OrderItemMapper, OrderItemDO> implements DineInItemService {

    public static final String ORDER_ITEM_GUID = "orderItemGuid";
    public static final String NOT_CALL_UP_TOO = "已叫起菜品不能再次叫起";

    private final OrderService orderService;

    private final OrderSubsidiaryService orderSubsidiaryService;

    private final OrderItemService orderItemService;

    private final OrderItemExtendsService orderItemExtendsService;

    private final OrderItemRecordService orderItemRecordService;

    private final ItemAttrService itemAttrService;

    private final FreeReturnItemService freeReturnItemService;

    private final DynamicHelper dynamicHelper;

    private final RedisHelper redisHelper;

    private final KdsService kdsService;

    private final TableClientService tableClientService;

    private final DineInPrintService dineInPrintService;

    private final DineInService dineInService;

    private final TableService tableService;

    private final ItemClientService itemClientService;

    private final KdsClientService kdsClientService;

    private final KdsMqService kdsMqService;

    private final MessagePushHelper messagePushHelper;

    @Resource
    private PadOrderMapper padOrderMapper;

    @Resource(name = "checkOutThreadPool")
    private ExecutorService checkOutThreadPool;

    private OrderTransform orderTransform = OrderTransform.INSTANCE;

    private final BusinessMessageService businessMessageService;

    private final OrganizationClientService organizationClientService;

    private final BusinessDataSettingService businessDataSettingService;

    @Autowired
    private GroupClientService groupClientService;

    @Autowired
    private GrouponService grouponService;

    @Autowired
    private IThirdActivityRecordService thirdActivityRecordService;

    @Autowired
    private DiscountService discountService;

    @Autowired
    private OrderItemChangesService orderItemChangesService;

    @Autowired
    private StoreClientService storeClientService;

    @Autowired
    private PackageSubgroupChangesService packageSubgroupChangesService;

    @Resource
    private AppendFeeService appendFeeService;

    /**
     * 商品标签 0 ：否 1：是
     */
    private static final Integer ITEM_TAG = 1;

    private static final String NEW = "新品";
    private static final String SIGN = "招牌";
    private static final String BESTSELLER = "热销";
    private static final String DO_NOT_PLACE_ORDER = "库存不足不可下单";
    /**
     * pad商家点餐
     */
    public static final Integer MERCHANT_ORDER = 0;

    /**
     * pad用户自主点餐
     */
    public static final Integer USER_ONESELF_ORDER = 1;

    private static final ScheduledExecutorService UNDO_COUPON_EXECUTOR_SERVICE = Executors.newScheduledThreadPool(5, new ThreadFactoryBuilder()
            .setNameFormat("再次撤销团购验券异步线程-%d")
            .build());

    @Autowired
    public DineInItemServiceImpl(OrderService orderService, OrderSubsidiaryService orderSubsidiaryService,
                                 OrderItemService orderItemService,
                                 OrderItemExtendsService orderItemExtendsService, DynamicHelper dynamicHelper,
                                 RedisHelper redisHelper, ItemAttrServiceImpl itemAttrService,
                                 FreeReturnItemService freeReturnItemService, KdsService kdsService,
                                 TableClientService tableClientService, DineInPrintService dineInPrintService,
                                 DineInService dineInService, TableService tableService,
                                 ItemClientService itemClientService, KdsClientService kdsClientService,
                                 KdsMqService kdsMqService, OrderItemRecordService orderItemRecordService,
                                 MessagePushHelper messagePushHelper,
                                 BusinessMessageService businessMessageService,
                                 OrganizationClientService organizationClientService,
                                 BusinessDataSettingService businessDataSettingService) {
        this.orderService = orderService;
        this.orderSubsidiaryService = orderSubsidiaryService;
        this.orderItemService = orderItemService;
        this.orderItemExtendsService = orderItemExtendsService;
        this.dynamicHelper = dynamicHelper;
        this.redisHelper = redisHelper;
        this.itemAttrService = itemAttrService;
        this.kdsService = kdsService;
        this.freeReturnItemService = freeReturnItemService;
        this.tableClientService = tableClientService;
        this.dineInPrintService = dineInPrintService;
        this.dineInService = dineInService;
        this.tableService = tableService;
        this.itemClientService = itemClientService;
        this.kdsClientService = kdsClientService;
        this.kdsMqService = kdsMqService;
        this.orderItemRecordService = orderItemRecordService;
        this.messagePushHelper = messagePushHelper;
        this.businessMessageService = businessMessageService;
        this.organizationClientService = organizationClientService;
        this.businessDataSettingService = businessDataSettingService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EstimateItemRespDTO addItems(CreateDineInOrderReqDTO createDineInOrderReqDTO, Boolean isFastFood) {
        List<DineInItemDTO> dineInItemDTOS = getDineInItemDTOS(createDineInOrderReqDTO);
        Long orderGuid = Long.valueOf(createDineInOrderReqDTO.getGuid());
        List<DineInItemDTO> useCouponItem = dineInItemDTOS.stream()
                .filter(e -> Objects.nonNull(e.getCouponInfo())).collect(Collectors.toList());
        if (!Boolean.TRUE.equals(createDineInOrderReqDTO.getContinueCheckGrouponFlag())
                && !CollectionUtils.isEmpty(useCouponItem)) {
            // 验券加商品， 先校验
            grouponDoCheck(orderGuid, useCouponItem);
        }
        try {
            EstimateItemRespDTO estimateItemRespDTO = batchAddItems(createDineInOrderReqDTO, isFastFood);
            // 加菜后处理
            afterAddItemsHandler(createDineInOrderReqDTO, estimateItemRespDTO, dineInItemDTOS);
            return estimateItemRespDTO;
        } catch (Exception e) {
            log.error("商品下单失败,e:", e);
            if (!Boolean.TRUE.equals(createDineInOrderReqDTO.getContinueCheckGrouponFlag())) {
                // 撤销用券
                revokeGrouponByItem(orderGuid, useCouponItem);
            }
            throw new BusinessException(e.getMessage());
        }
    }


    /**
     * 加菜后处理
     */
    private void afterAddItemsHandler(CreateDineInOrderReqDTO createDineInOrderReqDTO,
                                      EstimateItemRespDTO estimateItemRespDTO,
                                      List<DineInItemDTO> dineInItemList) {
        List<DineInItemDTO> useCouponItem = dineInItemList.stream()
                .filter(e -> Objects.nonNull(e.getCouponInfo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(useCouponItem)) {
            return;
        }
        if (!Boolean.TRUE.equals(createDineInOrderReqDTO.getContinueCheckGrouponFlag())
                && Boolean.FALSE.equals(estimateItemRespDTO.getResult())) {
            Long orderGuid = Long.valueOf(createDineInOrderReqDTO.getGuid());
            log.error("加购商品失败,estimateItemRespDTO:{}", JacksonUtils.writeValueAsString(estimateItemRespDTO));
            // 撤销用券
            revokeGrouponByItem(orderGuid, useCouponItem);
        }
        if (Boolean.TRUE.equals(createDineInOrderReqDTO.getContinueCheckGrouponFlag())) {
            for (DineInItemDTO itemDTO : useCouponItem) {
                // 构建验券请求参数
                CouPonReqDTO couPonReqDTO = buildCouPonReqDTO(Long.valueOf(createDineInOrderReqDTO.getGuid()),
                        Lists.newArrayList(itemDTO));
                // 查询团购验券结算信息
                grouponService.createTradeDetail(couPonReqDTO, Lists.newArrayList(itemDTO.getGroupVerify()));
            }
        }
    }

    @Override
    @Transactional
    public EstimateItemRespDTO batchAddItems(CreateDineInOrderReqDTO createDineInOrderReqDTO, Boolean isFastFood) {
        String storeGuid = createDineInOrderReqDTO.getStoreGuid();
        EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        //微信新增的菜品记录微信点菜人id（目前只有新增菜品，微信无赠菜，退菜操作）
        String userWxPublicOpenId = createDineInOrderReqDTO.getUserWxPublicOpenId();
        BatchAddItemBO addItemBO = new BatchAddItemBO();
        //同一批次新增的菜品创建时间统一
        LocalDateTime now = LocalDateTime.now();
        addItemBO.setNow(now);
        Long orderGuid = Long.valueOf(createDineInOrderReqDTO.getGuid());
        addItemBO.setOrderGuid(orderGuid);

        List<OrderItemDO> orderItemDOS = new ArrayList<>();
        addItemBO.setOrderItemDOS(orderItemDOS);
        List<OrderItemExtendsDO> orderItemExtendsDOS = new ArrayList<>();
        addItemBO.setOrderItemExtendsDOS(orderItemExtendsDOS);
        List<ItemAttrDO> itemAttrDOS = new ArrayList<>();
        addItemBO.setItemAttrDOS(itemAttrDOS);
        List<FreeReturnItemDO> freeReturnItemDOS = new ArrayList<>();
        addItemBO.setFreeReturnItemDOS(freeReturnItemDOS);
        List<OrderItemChangesDO> orderItemChangesList = Lists.newArrayList();
        addItemBO.setOrderItemChangesList(orderItemChangesList);
        //
        List<DineInItemDTO> dineInItemDTOS = getDineInItemDTOS(createDineInOrderReqDTO);
        log.info("dineInItemDTOS:{}", JacksonUtils.writeValueAsString(dineInItemDTOS));

        //由于前端的code值不对，后端直接查一遍
        checkItemCode(dineInItemDTOS);
        log.info("update code dineInItemDTOS:{}", JacksonUtils.writeValueAsString(dineInItemDTOS));

        OrderDO orderDO = orderService.getByIdWithLock(orderGuid);
        addItemBO.setOrderDO(orderDO);
        // 校验订单
        verifyOrderForAddItem(orderDO);
        // 估清校验
        if (Boolean.FALSE.equals(isFastFood)) {
            EstimateResultRespDTO estimateResult = itemClientService.estimate(createDineInOrderReqDTO.getDineInItemDTOS());
            if (Boolean.FALSE.equals(estimateResult.getSuccess())) {
                log.info("正餐估清校验");
                estimateItemRespDTO.setResult(Boolean.FALSE);
                estimateItemRespDTO.setEstimate(Boolean.TRUE);
                estimateItemRespDTO.setEstimateInfo(estimateResult.getErrorMsg());
                estimateItemRespDTO.setEstimateSkuGuids(estimateResult.getSkuGuids());
                return estimateItemRespDTO;
            }
        }

        Long padOrderGuid = getPadOrderGuid(createDineInOrderReqDTO, orderDO, storeGuid, orderGuid);

        padSetRemark(createDineInOrderReqDTO, orderDO);

        setMemberPhone(createDineInOrderReqDTO, orderDO);
        // 查询下单商品信息
        Map<String, ItemInfoRespDTO> itemInfoRespDTOMap = queryItemInfoRespDTO(storeGuid, dineInItemDTOS);
        addItemBO.setItemInfoRespDTOMap(itemInfoRespDTOMap);
        List<OrderItemRecordDO> itemRecordSave = new ArrayList<>();
        addItemBO.setItemRecordSave(itemRecordSave);
        // 是否使用商品购买价
        boolean isUseBuyPrice = businessDataSettingService.queryIsUseCouponBuyPrice(createDineInOrderReqDTO.getStoreGuid());
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            // 必须是验券的商品
            boolean isUseBuyPriceAndCoupon = isUseBuyPrice && Objects.nonNull(dineInItemDTO.getCouponInfo());
            OrderItemExtendsDO extendsDO = new OrderItemExtendsDO();
            handleFieldCompatible(dineInItemDTO, extendsDO, isUseBuyPriceAndCoupon);
            OrderItemDO orderItemDO = orderTransform.dineInItemDTO2OrderItemDO(dineInItemDTO);
            Long orderItemGuid = dynamicHelper.generateGuid(HST_ORDER_ITEM);
            extendsDO.setWxBatch(orderItemDO.getWxBatch());
            orderItemDO.setGmtCreate(now);
            setUserWxPublicOpenId(userWxPublicOpenId, orderItemDO, extendsDO);
            setPadOrderGuid(padOrderGuid, orderItemDO, extendsDO);
            orderItemDO.setGuid(orderItemGuid);
            extendsDO.setGuid(orderItemGuid);
            orderItemDO.setOrderGuid(orderGuid);
            extendsDO.setOrderGuid(orderGuid);
            orderItemDO.setIsPay(0);
            orderItemDO.setCurrentCount(dineInItemDTO.getCurrentCount());
            orderItemDO.setFreeCount(BigDecimal.ZERO);
            orderItemDO.setReturnCount(BigDecimal.ZERO);
            orderItemDO.setCreateStaffName(UserContextUtils.getUserName());
            orderItemDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
            bindCouponCode(orderItemDO, dineInItemDTO);

            // 记录核算价
            ItemInfoRespDTO itemInfoRespDTO = itemInfoRespDTOMap.get(dineInItemDTO.getItemGuid());
            SkuInfoRespDTO skuInfoRespDTO = getSkuInfoRespDTO(itemInfoRespDTO, dineInItemDTO.getSkuGuid());
            setCostPrice(skuInfoRespDTO, orderItemDO, extendsDO);

            //设置商品积分抵扣值和积分
            orderItemDO.setPointMoney(dineInItemDTO.getPointMoney());
            orderItemDO.setPointValue(dineInItemDTO.getPointValue());

            orderItemDO.setIsKitchen(dineInItemDTO.getIsKitchen());
            extendsDO.setIsKitchen(dineInItemDTO.getIsKitchen());

            //组装商品标签
            assembleItemLabel(itemInfoRespDTO, orderItemDO);
            orderItemDOS.add(orderItemDO);
            orderItemExtendsDOS.add(extendsDO);


            addItemRecordSave(dineInItemDTO, orderItemDO, itemRecordSave);

            dineInItemDTO.setGuid(String.valueOf(orderItemGuid));
            //
            BigDecimal itemPrice = dineInItemDTO.getCurrentCount().multiply(dineInItemDTO.getPrice());

            orderDO.setOrderFee(orderDO.getOrderFee().add(itemPrice));


            //处理未下单赠送
            handleNotOrderFreeGive(dineInItemDTO, orderItemDO, skuInfoRespDTO, addItemBO);

            // 商品属性
            orderFeeAddAttrTotalPrice(dineInItemDTO, orderItemGuid, orderItemDO, addItemBO, isUseBuyPriceAndCoupon);

            // 商品套餐处理
            orderPackageSubgroupHandler(dineInItemDTO, orderItemDO, extendsDO, addItemBO, isUseBuyPriceAndCoupon);
        }

        orderDO.setOrderFeeForCombine(orderDO.getOrderFee());

        // 保存验券记录
        saveGroupon(orderDO.getGuid(), dineInItemDTOS, isUseBuyPrice);

        // 保存商品点菜(换菜记录)
        packageSubgroupChangesService.addChangeOrderItemRecordDOList(orderDO, orderItemChangesList, itemRecordSave);

        if (!ObjectUtils.isEmpty(itemRecordSave)) {
            //保存到菜品流水表中
            orderItemRecordService.saveBatchWithDeleteCache(itemRecordSave);
        }

        if (!CollectionUtils.isEmpty(itemAttrDOS)) {
            itemAttrService.saveOrUpdateBatch(itemAttrDOS);
        }

        if (!CollectionUtils.isEmpty(freeReturnItemDOS)) {
            freeReturnItemService.saveBatch(freeReturnItemDOS);
        }
        //处理预点餐逻辑
        handlePreOrderItem(createDineInOrderReqDTO);
        // 保存套餐换菜明细
        savePackageSubgroupChangeItemList(orderItemChangesList);

        estimateItemRespDTO.setResult(Boolean.TRUE);

        orderService.updateById(orderDO);

        // 更新多单结账的主单
        updateOrderFeeForSameOrder(orderDO);

        boolean isMerchant = judgeStoreDeviceOnPadOrderType(createDineInOrderReqDTO);
        if (!isMerchant && !ObjectUtils.isEmpty(createDineInOrderReqDTO.getDeviceType()) &&
                Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), createDineInOrderReqDTO.getDeviceType())) {
            // pad在下单的时候已经入库,只删除对应的缓存
            String redisKey = RedisKeyUtil.getHstOrderItemKey(String.valueOf(orderDO.getGuid()));
            redisHelper.delete(redisKey);
        } else {
            orderItemService.saveBatchWithDeleteCache(orderItemDOS, String.valueOf(orderDO.getGuid()));
            orderItemExtendsService.saveBatch(orderItemExtendsDOS);
        }

        // 加菜成功后的异步操作
        addAfterItemAsyn(createDineInOrderReqDTO, isFastFood, addItemBO);

        // 将加菜后的订单金额存入缓存
        redisHelper.setEx(OrderRedisConstant.REDIS_ORDER_FEE + orderDO.getGuid(),
                String.valueOf(orderDO.getOrderFee()), 30, TimeUnit.SECONDS);

        // 如果订单是pad来源，加菜后发送接单消息
        boolean isPad = judgePad(createDineInOrderReqDTO, orderDO);
        if (isPad) {
            sendMsgToPad(createDineInOrderReqDTO);
        }

        return estimateItemRespDTO;
    }

    /**
     * 更新多单结账的主单
     */
    private void updateOrderFeeForSameOrder(OrderDO orderDO) {
        if (!Objects.equals(UpperStateEnum.SAME_SUB.getCode(), orderDO.getUpperState())) {
            return;
        }
        Long guid = orderDO.getGuid();
        Long mainOrderGuid = orderDO.getMainOrderGuid();
        if (Objects.nonNull(mainOrderGuid) && !Objects.equals(0L, mainOrderGuid)) {
            guid = mainOrderGuid;
        }
        orderService.updateSameOrderFeeForCombine(guid);
    }

    /**
     * 加菜校验订单
     */
    private void verifyOrderForAddItem(OrderDO orderDO) {
        if (!OrderUtil.unfinished(orderDO)) {
            throw new ParameterException("订单状态不允许加菜");
        }
        verifyOrderForSameOrder(orderDO);
    }

    /**
     * 校验多单结账 订单
     */
    private void verifyOrderForSameOrder(OrderDO orderDO) {
        if (Objects.equals(UpperStateEnum.SAME_SUB.getCode(), orderDO.getUpperState())
                && Objects.equals(StateEnum.SUB_SUCCESS.getCode(), orderDO.getState())) {
            throw new ParameterException("请刷新桌台后重试");
        }
    }

    private void handlePreOrderItem(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        if (createDineInOrderReqDTO.isReserveOrder()) {
            OrderSubsidiaryDO orderSubsidiaryDO = new OrderSubsidiaryDO();
            orderSubsidiaryDO.setGuid(Long.valueOf(createDineInOrderReqDTO.getGuid()));
            orderSubsidiaryDO.setReserveOrderState(2);
            orderSubsidiaryService.updateById(orderSubsidiaryDO);
        }
    }

    private void addAfterItemAsyn(CreateDineInOrderReqDTO createDineInOrderReqDTO,
                                  Boolean isFastFood,
                                  BatchAddItemBO addItemBO) {
        // 清楚删除标记的明细
        addItemBO.getOrderItemDOS().removeIf(e -> Objects.nonNull(e.getIsDelete()) && Boolean.TRUE.equals(e.getIsDelete()));
        if (Boolean.FALSE.equals(isFastFood)) {
            //加菜成功后的异步操作
            UserContext userContext = UserContextUtils.get();
            checkOutThreadPool.execute(() -> {
                UserContextUtils.put(userContext);
                if (StringUtils.isEmpty(createDineInOrderReqDTO.getUserGuid())) {
                    createDineInOrderReqDTO.setUserGuid("100000");
                }
                if (StringUtils.isEmpty(createDineInOrderReqDTO.getUserName())) {
                    createDineInOrderReqDTO.setUserName("默认操作员");
                }
                EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
                addItemAsync(createDineInOrderReqDTO, addItemBO);
                EnterpriseIdentifier.remove();
            });
        }
    }

    /**
     * 商品属性
     */
    private void orderFeeAddAttrTotalPrice(DineInItemDTO dineInItemDTO, Long orderItemGuid,
                                           OrderItemDO orderItemDO, BatchAddItemBO addItemBO, boolean isUseBuyPriceAndCoupon) {
        if (Objects.isNull(orderItemDO.getAttrTotal())) {
            orderItemDO.setAttrTotal(BigDecimal.ZERO);
        }
        if (CollectionUtil.isEmpty(dineInItemDTO.getItemAttrDTOS())) {
            return;
        }
        LocalDateTime now = addItemBO.getNow();
        Long orderGuid = addItemBO.getOrderGuid();
        OrderDO orderDO = addItemBO.getOrderDO();
        BigDecimal attrTotalPrice = BigDecimal.ZERO;
        Long itemAttrGuid = dynamicHelper.generateGuid(ITEM_ATTR_GUID);
        List<Long> itemAttrGuidList = dynamicHelper.generateGuids(ITEM_ATTR_GUID, dineInItemDTO.getItemAttrDTOS().size());
        for (int j = 0; j < dineInItemDTO.getItemAttrDTOS().size(); j++) {
            ItemAttrDTO itemAttrDTO = dineInItemDTO.getItemAttrDTOS().get(j);
            ItemAttrDO itemAttrDO = orderTransform.itemAttrDTO2ItemAttrDO(itemAttrDTO);
            if (isUseBuyPriceAndCoupon) {
                itemAttrDO.setBeforeCouponAttrPrice(itemAttrDO.getAttrPrice());
                itemAttrDTO.setAttrPrice(BigDecimal.ZERO);
                itemAttrDO.setAttrPrice(BigDecimal.ZERO);
            }
            itemAttrDO.setOrderItemGuid(orderItemGuid);
            itemAttrDO.setGuid(itemAttrGuidList.get(j));
            itemAttrDO.setGmtCreate(now);
            itemAttrDO.setGmtModified(now);
            itemAttrDO.setOrderGuid(orderGuid);
            itemAttrDO.setStoreGuid(UserContextUtils.getStoreGuid());
            itemAttrDO.setStoreName(UserContextUtils.getStoreName());
            itemAttrDTO.setGuid(String.valueOf(itemAttrGuid));
            addItemBO.getItemAttrDOS().add(itemAttrDO);
            // 暂时没有属性数量
            BigDecimal attrPrice = getAttrPrice(dineInItemDTO, itemAttrDTO);
            attrTotalPrice = attrTotalPrice.add(attrPrice);
        }
        // 有属性
        orderItemDO.setHasAttr(1);
        // 订单金额 + 属性价
        orderDO.setOrderFee(orderDO.getOrderFee().add(attrTotalPrice));
    }


    private void orderPackageSubgroupHandler(DineInItemDTO dineInItemDTO,
                                             OrderItemDO orderItemDO,
                                             OrderItemExtendsDO extendsDO,
                                             BatchAddItemBO addItemBO,
                                             boolean isUseBuyPriceAndCoupon) {
        if (Boolean.FALSE.equals(ItemUtil.isGroup(dineInItemDTO.getItemType()))) {
            return;
        }
        // 菜品为套餐时
        OrderDO orderDO = addItemBO.getOrderDO();
        List<OrderItemRecordDO> itemRecordSave = addItemBO.getItemRecordSave();
        List<ItemAttrDO> itemAttrDOS = addItemBO.getItemAttrDOS();
        List<OrderItemDO> orderItemDOS = addItemBO.getOrderItemDOS();
        Map<String, ItemInfoRespDTO> itemInfoRespDTOMap = addItemBO.getItemInfoRespDTOMap();
        List<OrderItemExtendsDO> orderItemExtendsDOS = addItemBO.getOrderItemExtendsDOS();
        List<OrderItemChangesDO> orderItemChangesList = addItemBO.getOrderItemChangesList();
        if (Objects.isNull(orderItemDO.getAddPrice())) {
            orderItemDO.setAddPrice(BigDecimal.ZERO);
        }
        if (Objects.isNull(orderItemDO.getAttrTotal())) {
            orderItemDO.setAttrTotal(BigDecimal.ZERO);
        }
        // biz
        OrderPackageSubgroupBO packageSubgroupBiz = OrderPackageSubgroupBizBuilder.build();
        packageSubgroupBiz.setDineInItemDTO(dineInItemDTO);
        packageSubgroupBiz.setItemInfoRespDTOMap(itemInfoRespDTOMap);
        packageSubgroupBiz.setItemAttrDOS(itemAttrDOS);
        packageSubgroupBiz.setItemRecordSave(itemRecordSave);
        packageSubgroupBiz.setOrderItemDOS(orderItemDOS);
        packageSubgroupBiz.setOrderItemExtendsDOS(orderItemExtendsDOS);
        packageSubgroupBiz.setOrderItemChangesList(orderItemChangesList);
        packageSubgroupBiz.setOrderDO(orderDO);
        packageSubgroupBiz.setExtendsDO(extendsDO);
        packageSubgroupBiz.setAddItemFlag(true);
        // 套餐子项处理
        overlayAttrTotalPrice(packageSubgroupBiz, isUseBuyPriceAndCoupon);
        // 套餐子项属性加之和 套餐的hasAttr标识新版本，新版本直接取attrTotal和addPrice计算
        orderItemDO.setHasAttr(BooleanEnum.TRUE.getCode());
        dineInItemDTO.setHasAttr(BooleanEnum.TRUE.getCode());
        orderItemDO.setAttrTotal(dineInItemDTO.getSingleItemAttrTotal());
        if (isUseBuyPriceAndCoupon) {
            extendsDO.setBeforeCouponAddPrice(dineInItemDTO.getSingleAddPriceTotal());
            orderItemDO.setAddPrice(BigDecimal.ZERO);
        } else {
            orderItemDO.setAddPrice(dineInItemDTO.getSingleAddPriceTotal());
        }
        // 数量
        BigDecimal itemNum = (dineInItemDTO.getCurrentCount().add(dineInItemDTO.getFreeCount() == null
                ? BigDecimal.ZERO : dineInItemDTO.getFreeCount()));
        BigDecimal attrTotalPrice = orderItemDO.getAttrTotal().multiply(itemNum);
        BigDecimal addTotalPrice = orderItemDO.getAddPrice().multiply(itemNum);
        // 订单金额 + 套餐子项属性总价 + 套餐加价总价
        orderDO.setOrderFee(orderDO.getOrderFee().add(addTotalPrice).add(attrTotalPrice));
    }


    private void handleNotOrderFreeGive(DineInItemDTO dineInItemDTO,
                                        OrderItemDO orderItemDO,
                                        SkuInfoRespDTO skuInfoRespDTO,
                                        BatchAddItemBO addItemBO) {
        LocalDateTime now = addItemBO.getNow();
        Long orderGuid = addItemBO.getOrderGuid();
        OrderDO orderDO = addItemBO.getOrderDO();
        List<FreeReturnItemDO> freeReturnItemDOS = addItemBO.getFreeReturnItemDOS();
        List<OrderItemRecordDO> itemRecordSave = addItemBO.getItemRecordSave();
        List<FreeItemDTO> freeItemDTOS = dineInItemDTO.getFreeItemDTOS();
        if (!CollectionUtils.isEmpty(freeItemDTOS)) {
            //记录未下单赠送数量
            BigDecimal freeSum = BigDecimal.ZERO;
            OrderItemRecordDO recordFreeDO = new OrderItemRecordDO();

            for (FreeItemDTO freeItemDTO : freeItemDTOS) {
                FreeReturnItemDO freeReturnItemDO = getFreeReturnItemDO(freeItemDTO, now, orderItemDO, orderGuid);
                freeSum = freeSum.add(freeItemDTO.getCount());
                BigDecimal freeItemPrice = freeItemDTO.getCount().multiply(dineInItemDTO.getOriginalPrice());
                orderDO.setOrderFee(orderDO.getOrderFee().add(freeItemPrice));
                freeReturnItemDOS.add(freeReturnItemDO);
                recordFreeDO = orderTransform.freeReturnItemDO2OrderItemRecordDO(freeReturnItemDO);
                Long recordGuid2 = dynamicHelper.generateGuid(ORDER_ITEM_RECORD);
                recordFreeDO.setGuid(recordGuid2);
                recordFreeDO.setOrderItemGuid(String.valueOf(orderItemDO.getGuid()));
            }
            setRecordFreeDO(dineInItemDTO, orderDO, recordFreeDO, freeSum);

            OrderItemRecordDO recordCurrentDO = getRecordCurrentDO(dineInItemDTO, recordFreeDO, freeSum, orderItemDO);
            // 记录核算价
            if (!ObjectUtils.isEmpty(skuInfoRespDTO)) {
                recordFreeDO.setAccountingPrice(skuInfoRespDTO.getAccountingPrice());
                recordCurrentDO.setAccountingPrice(skuInfoRespDTO.getAccountingPrice());
            }

            itemRecordSave.add(recordFreeDO);
            itemRecordSave.add(recordCurrentDO);
        }
    }

    private void setUserWxPublicOpenId(String userWxPublicOpenId,
                                       OrderItemDO orderItemDO,
                                       OrderItemExtendsDO extendsDO) {
        if (StringUtils.isNotEmpty(userWxPublicOpenId)) {
            orderItemDO.setUserWxPublicOpenId(userWxPublicOpenId);
            extendsDO.setUserWxPublicOpenId(userWxPublicOpenId);
        }
    }

    private void setPadOrderGuid(Long padOrderGuid,
                                 OrderItemDO orderItemDO,
                                 OrderItemExtendsDO extendsDO) {
        if (!ObjectUtils.isEmpty(padOrderGuid)) {
            orderItemDO.setPadOrderGuid(String.valueOf(padOrderGuid));
            extendsDO.setPadOrderGuid(String.valueOf(padOrderGuid));
        }
    }

    private SkuInfoRespDTO getSkuInfoRespDTO(ItemInfoRespDTO itemInfoRespDTO,
                                             String dineInItemDTO) {
        SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        if (!ObjectUtils.isEmpty(itemInfoRespDTO)) {
            Map<String, SkuInfoRespDTO> skuInfoRespDTOMap = itemInfoRespDTO.getSkuList().stream()
                    .collect(Collectors.toMap(SkuInfoRespDTO::getSkuGuid, Function.identity()));
            skuInfoRespDTO = skuInfoRespDTOMap.get(dineInItemDTO);

        }
        return skuInfoRespDTO;
    }

    private void setCostPrice(SkuInfoRespDTO skuInfoRespDTO,
                              OrderItemDO orderItemDO,
                              OrderItemExtendsDO extendsDO) {
        if (!ObjectUtils.isEmpty(skuInfoRespDTO)) {
            orderItemDO.setAccountingPrice(skuInfoRespDTO.getAccountingPrice());
            orderItemDO.setSmallPicture(skuInfoRespDTO.getPictureUrl());
            if (BigDecimalUtil.greaterThanZero(skuInfoRespDTO.getCostPrice())) {
                extendsDO.setCostPrice(skuInfoRespDTO.getCostPrice());
            }
        }
    }

    private void addItemRecordSave(DineInItemDTO dineInItemDTO,
                                   OrderItemDO orderItemDO,
                                   List<OrderItemRecordDO> itemRecordSave) {
        OrderItemRecordDO orderItemRecordDO = orderTransform.orderItemDO2OrderItemRecordDO(orderItemDO);
        Long recordGuid1 = dynamicHelper.generateGuid(ORDER_ITEM_RECORD);
        orderItemRecordDO.setItemType(dineInItemDTO.getItemType());
        orderItemRecordDO.setGuid(recordGuid1);
        orderItemRecordDO.setSkuGuid(dineInItemDTO.getSkuGuid());
        orderItemRecordDO.setSkuName(dineInItemDTO.getSkuName());
        orderItemRecordDO.setType(1);
        orderItemRecordDO.setLog("【点菜】:点菜");
        orderItemRecordDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
        orderItemRecordDO.setCreateStaffName(UserContextUtils.getUserName());

        if (BigDecimalUtil.greaterThanZero(orderItemRecordDO.getCurrentCount())) {
            itemRecordSave.add(orderItemRecordDO);
        }
    }

    private void padSetRemark(CreateDineInOrderReqDTO createDineInOrderReqDTO,
                              OrderDO orderDO) {
        boolean fromPad = judgeFromPad(createDineInOrderReqDTO, orderDO);
        if (fromPad) {
            orderDO.setRemark(createDineInOrderReqDTO.getRemark());
        }
    }

    private void setMemberPhone(CreateDineInOrderReqDTO createDineInOrderReqDTO,
                                OrderDO orderDO) {
        boolean memberPhoneIsEmpty = StringUtils.isEmpty(orderDO.getMemberPhone()) ||
                "0".equals(orderDO.getMemberPhone());
        if (memberPhoneIsEmpty && !StringUtils.isEmpty(createDineInOrderReqDTO.getUserWxPublicOpenId())
                && createDineInOrderReqDTO.getUserWxPublicOpenId().length() != 19) {
            orderDO.setMemberPhone(createDineInOrderReqDTO.getUserWxPublicOpenId());
        }
    }

    @NotNull
    private List<DineInItemDTO> getDineInItemDTOS(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        List<DineInItemDTO> dineInItemDTOS = createDineInOrderReqDTO.getDineInItemDTOS();
        if (CollectionUtils.isEmpty(dineInItemDTOS)) {
            throw new ParameterException("菜品信息不能为空");
        }
        // 属性总价清0
        dineInItemDTOS.forEach(e -> e.setSingleItemAttrTotal(BigDecimal.ZERO));
        return dineInItemDTOS;
    }

    private void assembleItemLabel(ItemInfoRespDTO itemInfoRespDTO,
                                   OrderItemDO orderItemDO) {
        if (ObjectUtil.isNotNull(itemInfoRespDTO)) {
            StringBuilder str = new StringBuilder();
            //新品
            if (ITEM_TAG.equals(itemInfoRespDTO.getIsNew())) {
                str.append(NEW).append("、");
            }
            //招牌
            if (ITEM_TAG.equals(itemInfoRespDTO.getIsSign())) {
                str.append(SIGN).append("、");
            }
            //招牌
            if (ITEM_TAG.equals(itemInfoRespDTO.getIsBestseller())) {
                str.append(BESTSELLER).append("、");
            }
            if (StringUtils.isNotBlank(str)) {
                String resultStr = str.substring(0, str.length() - 1);
                orderItemDO.setTag(resultStr);
            }
        }
    }

    @NotNull
    private BigDecimal getAttrPrice(DineInItemDTO dineInItemDTO,
                                    ItemAttrDTO itemAttrDTO) {
        BigDecimal attrPrice;
        if (dineInItemDTO.getItemType().equals(ItemTypeEnum.WEIGH.getCode())) {
            attrPrice = itemAttrDTO.getAttrPrice().multiply(BigDecimal.ONE);
        } else {
            attrPrice = itemAttrDTO.getAttrPrice().multiply(dineInItemDTO.getCurrentCount().add
                    (dineInItemDTO.getFreeCount() == null ? BigDecimal.ZERO : dineInItemDTO.getFreeCount
                            ()));
        }
        return attrPrice;
    }

    private void handleFieldCompatible(DineInItemDTO dineInItemDTO, OrderItemExtendsDO extendsDO, boolean isUseBuyPriceAndCoupon) {
        if (dineInItemDTO.getPriceChangeType() != null && dineInItemDTO.getPriceChangeType() == 2) {
            dineInItemDTO.setPrice(dineInItemDTO.getOriginalPrice());
        }
        //安卓兼容以前字段
        if (dineInItemDTO.getPriceChangeType() == null || dineInItemDTO.getOriginalPrice() == null) {
            dineInItemDTO.setOriginalPrice(dineInItemDTO.getPrice());
        }
        if (isUseBuyPriceAndCoupon && Objects.nonNull(dineInItemDTO.getCouponInfo())) {
            log.info("购买券信息：{}", JacksonUtils.writeValueAsString(dineInItemDTO.getCouponInfo()));
            extendsDO.setBeforeCouponPrice(dineInItemDTO.getPrice());
            extendsDO.setIsUseCouponPrice(Boolean.TRUE);
            dineInItemDTO.setPrice(BigDecimal.valueOf(dineInItemDTO.getCouponInfo().getCouponBuyPrice()));
        }
    }

    @Nullable
    private Long getPadOrderGuid(CreateDineInOrderReqDTO createDineInOrderReqDTO,
                                 OrderDO orderDO,
                                 String storeGuid,
                                 Long orderGuid) {
        Long padOrderGuid = null;

        // 其他设备加菜处理：如果订单来源是pad，入参来源不是pad，初始化pad下单表，下单已接单状态
        boolean isPad = judgePad(createDineInOrderReqDTO, orderDO);

        // 判断门店pad配置
        boolean isMerchant = judgeStoreDeviceOnPadOrderType(createDineInOrderReqDTO);

        if (isPad || isMerchant) {
            PadOrderDO padOrderDO = new PadOrderDO();
            padOrderGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_PAD_ORDER);
            padOrderDO.setGuid(padOrderGuid);
            getPadOrderDO(createDineInOrderReqDTO, padOrderDO, orderDO, storeGuid);

            handleCombineOrder(orderDO, padOrderDO, orderGuid);
            padOrderMapper.insert(padOrderDO);
        }
        return padOrderGuid;
    }

    private void getPadOrderDO(CreateDineInOrderReqDTO createDineInOrderReqDTO,
                               PadOrderDO padOrderDO,
                               OrderDO orderDO,
                               String storeGuid) {
        padOrderDO.setOrderGuid(createDineInOrderReqDTO.getGuid());
        List<PadOrderDO> padOrderDOList = padOrderMapper.selectList(new LambdaQueryWrapper<PadOrderDO>()
                .eq(PadOrderDO::getOrderGuid, createDineInOrderReqDTO.getGuid()));
        padOrderDO.setPadBatch(padOrderDOList.size() + 1);
        BigDecimal totalPrice = createDineInOrderReqDTO.getDineInItemDTOS().stream()
                .map(DineInItemDTO::getPrice)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        padOrderDO.setTotalPrice(totalPrice);
        padOrderDO.setActualGuestsNo(orderDO.getGuestCount());
        padOrderDO.setOrderState(OrderStateEnum.ACCEPT_ORDER.getCode());
        padOrderDO.setDenialReason("");
        padOrderDO.setTradeMode(TradeModeEnum.DINEIN.getCode());
        padOrderDO.setStoreGuid(storeGuid);
        padOrderDO.setRemark(createDineInOrderReqDTO.getRemark());
        padOrderDO.setItemCount(createDineInOrderReqDTO.getDineInItemDTOS().size());
        padOrderDO.setOrderSource(createDineInOrderReqDTO.getDeviceType());
        padOrderDO.setDiningTableGuid(createDineInOrderReqDTO.getDiningTableGuid());
        padOrderDO.setTableCode(createDineInOrderReqDTO.getDiningTableName());
        padOrderDO.setAreaGuid(createDineInOrderReqDTO.getAreaGuid());
        padOrderDO.setAreaName(createDineInOrderReqDTO.getAreaName());
    }

    private void handleCombineOrder(OrderDO orderDO,
                                    PadOrderDO padOrderDO,
                                    Long orderGuid) {
        if (Objects.equals(UpperStateEnum.SUB.getCode(), orderDO.getUpperState())) {
            // 下单的是子桌
            Long mainOrderGuid = orderDO.getMainOrderGuid();
            padOrderDO.setCombineOrderGuid(String.valueOf(mainOrderGuid));

            // 并台情况下推送需要推送所有pad设备消息
            List<OrderDO> orderDOList = orderService.list(new LambdaQueryWrapper<OrderDO>()
                    .eq(OrderDO::getMainOrderGuid, mainOrderGuid));
            OrderDO mainOrderDO = orderService.getById(mainOrderGuid);
            orderDOList.add(mainOrderDO);
            List<String> tableGuidList = orderDOList.stream()
                    .map(OrderDO::getDiningTableGuid)
                    .distinct()
                    .collect(Collectors.toList());
            combineSubmitOrderPushMsg(orderDO, tableGuidList);
        } else if (Objects.equals(UpperStateEnum.MAIN.getCode(), orderDO.getUpperState())) {
            // 是主单
            List<OrderDO> subList = orderService.list(new LambdaQueryWrapper<OrderDO>()
                    .eq(OrderDO::getMainOrderGuid, orderGuid));
            if (!com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(subList)) {
                // 下单的是主桌
                padOrderDO.setCombineOrderGuid(String.valueOf(orderGuid));

                // 并台情况下推送需要推送所有pad设备消息
                subList.add(orderDO);
                List<String> tableGuidList = subList.stream()
                        .map(OrderDO::getDiningTableGuid)
                        .distinct()
                        .collect(Collectors.toList());
                combineSubmitOrderPushMsg(orderDO, tableGuidList);
            }
        }
    }

    private void overlayAttrTotalPrice(OrderPackageSubgroupBO packageSubgroupBiz, boolean isUseBuyPriceAndCoupon) {
        DineInItemDTO dineInItemDTO = packageSubgroupBiz.getDineInItemDTO();
        List<PackageSubgroupDTO> packageSubgroupDTOS = dineInItemDTO.getPackageSubgroupDTOS();
        for (PackageSubgroupDTO packageSubgroupDTO : packageSubgroupDTOS) {
            List<SubDineInItemDTO> subDineInItemDTOS = packageSubgroupDTO.getSubDineInItemDTOS();
            for (SubDineInItemDTO subDineInItemDTO : subDineInItemDTOS) {
                packageSubgroupBiz.setPackageSubgroupDTO(packageSubgroupDTO);
                packageSubgroupBiz.setSubDineInItemDTO(subDineInItemDTO);
                // 套餐子项处理
                addPackageSubgroup(packageSubgroupBiz, isUseBuyPriceAndCoupon);
            }
            // 套餐换菜
            packageSubgroupBiz.setChangeSubDineInItemList(subDineInItemDTOS);
            packageSubgroupChangesService.changes(packageSubgroupBiz);
        }
    }

    private void setRecordFreeDO(DineInItemDTO dineInItemDTO,
                                 OrderDO orderDO,
                                 OrderItemRecordDO recordFreeDO,
                                 BigDecimal freeSum) {
        if (orderDO.getRecoveryType().equals(RecoveryTypeEnum.NEW.getCode())) {
            recordFreeDO.setIsFromRecovery(1);
        }
        recordFreeDO.setFreeCount(freeSum);
        recordFreeDO.setItemType(dineInItemDTO.getItemType());
        recordFreeDO.setType(2);
        recordFreeDO.setSkuGuid(dineInItemDTO.getSkuGuid());
        recordFreeDO.setSkuName(dineInItemDTO.getSkuName());
        recordFreeDO.setItemTypeName(dineInItemDTO.getItemTypeName());
        recordFreeDO.setItemTypeGuid(dineInItemDTO.getItemTypeGuid());
        recordFreeDO.setUnit(dineInItemDTO.getUnit());
        recordFreeDO.setLog("【赠菜】:未下单赠送");
        recordFreeDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
        recordFreeDO.setCreateStaffName(UserContextUtils.getUserName());
    }

    @NotNull
    private OrderItemRecordDO getRecordCurrentDO(DineInItemDTO dineInItemDTO,
                                                 OrderItemRecordDO recordFreeDO,
                                                 BigDecimal freeSum,
                                                 OrderItemDO orderItemDO) {
        OrderItemRecordDO recordCurrentDO;
        String jsonStr = JacksonUtils.writeValueAsString(recordFreeDO);
        recordCurrentDO = JacksonUtils.toObject(OrderItemRecordDO.class, jsonStr);
        Long recordGuid3 = dynamicHelper.generateGuid(ORDER_ITEM_RECORD);
        recordCurrentDO.setGuid(recordGuid3);
        recordCurrentDO.setItemTypeGuid(dineInItemDTO.getItemTypeGuid());
        recordCurrentDO.setItemTypeName(dineInItemDTO.getItemTypeName());
        recordCurrentDO.setSkuGuid(dineInItemDTO.getSkuGuid());
        recordCurrentDO.setSkuName(dineInItemDTO.getSkuName());
        recordCurrentDO.setUnit(dineInItemDTO.getUnit());
        recordCurrentDO.setCurrentCount(freeSum);
        recordCurrentDO.setItemType(dineInItemDTO.getItemType());
        recordCurrentDO.setType(1);
        recordCurrentDO.setOrderItemGuid(String.valueOf(orderItemDO.getGuid()));
        recordCurrentDO.setLog("【点菜】:未下单赠送加入点菜");
        return recordCurrentDO;
    }

    @NotNull
    private FreeReturnItemDO getFreeReturnItemDO(FreeItemDTO freeItemDTO,
                                                 LocalDateTime now,
                                                 OrderItemDO orderItemDO,
                                                 Long orderGuid) {
        FreeReturnItemDO freeReturnItemDO = new FreeReturnItemDO();
        freeReturnItemDO.setGmtCreate(now);
        freeReturnItemDO.setStaffName(UserContextUtils.getUserName());
        freeReturnItemDO.setStaffGuid(UserContextUtils.getUserGuid());
        freeReturnItemDO.setGuid(dynamicHelper.generateGuid(HST_ORDER_ITEM));
        freeReturnItemDO.setOrderItemGuid(orderItemDO.getGuid());
        freeReturnItemDO.setOrderGuid(orderGuid);
        freeReturnItemDO.setCount(freeItemDTO.getCount());
        freeReturnItemDO.setReason(freeItemDTO.getReason());
        freeReturnItemDO.setStoreGuid(UserContextUtils.getStoreGuid());
        freeReturnItemDO.setStoreName(UserContextUtils.getStoreName());
        freeReturnItemDO.setType(FreeReturnTypeEnum.FREE.getCode());
        freeReturnItemDO.setItemGuid(orderItemDO.getItemGuid());
        freeReturnItemDO.setItemName(orderItemDO.getItemName());
        freeReturnItemDO.setPrice(orderItemDO.getOriginalPrice());
        //未下单赠送不可能是已划
        freeReturnItemDO.setServeCount(BigDecimal.ZERO);
        freeReturnItemDO.setItemState(freeItemDTO.getItemState());
        orderItemDO.setFreeCount(orderItemDO.getFreeCount().add(freeItemDTO.getCount()));
        return freeReturnItemDO;
    }

    private boolean judgeFromPad(CreateDineInOrderReqDTO createDineInOrderReqDTO, OrderDO orderDO) {
        return Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), orderDO.getDeviceType()) &&
                Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), createDineInOrderReqDTO.getDeviceType());
    }

    private boolean judgePad(CreateDineInOrderReqDTO createDineInOrderReqDTO, OrderDO orderDO) {
        return Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), orderDO.getDeviceType()) &&
                !Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), createDineInOrderReqDTO.getDeviceType());
    }

    private boolean judgeStoreDeviceOnPadOrderType(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        boolean isMerchant = false;
        // 查询当前设备绑定的信息
        StoreDeviceDTO storeDeviceDTO = organizationClientService.queryDeviceByDeviceId(createDineInOrderReqDTO.getDeviceId());
        log.warn("查询当前设备绑定的信息 storeDeviceDTOList={}", JacksonUtils.writeValueAsString(storeDeviceDTO));
        if (ObjectUtils.isEmpty(storeDeviceDTO)) {
            log.error("当前设备绑定的信息为空 deviceId={}", createDineInOrderReqDTO.getDeviceId());
        } else {
            isMerchant = !ObjectUtils.isEmpty(storeDeviceDTO.getPadOrderType()) &&
                    Objects.equals(MERCHANT_ORDER, storeDeviceDTO.getPadOrderType());
        }
        return isMerchant;
    }

    private void checkItemCode(List<DineInItemDTO> dineInItemDTOS) {
        Set<String> skuGuidList = dineInItemDTOS.stream()
                .map(DineInItemDTO::getSkuGuid).collect(Collectors.toSet());
        final Map<String, String> codeMap = itemClientService.getCode(Lists.newArrayList(skuGuidList));
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            if (ObjectUtils.isEmpty(codeMap) || codeMap.isEmpty()) {
                break;
            }
            String skuGuid = dineInItemDTO.getSkuGuid();
            String code = codeMap.get(skuGuid);
            if (StringUtils.isNotBlank(code)) {
                dineInItemDTO.setCode(code);
            }
        }
    }

    /**
     * 保存套餐换菜明细
     */
    private void savePackageSubgroupChangeItemList(List<OrderItemChangesDO> orderItemChangesList) {
        if (CollectionUtil.isEmpty(orderItemChangesList)) {
            return;
        }
        orderItemChangesService.saveOrUpdateBatch(orderItemChangesList);
    }

    /**
     * 查询下单商品信息
     */
    private Map<String, ItemInfoRespDTO> queryItemInfoRespDTO(String storeGuid, List<DineInItemDTO> dineInItemDTOS) {
        // 查询下单商品信息
        Set<String> itemGuidList = dineInItemDTOS.stream()
                .map(DineInItemDTO::getItemGuid)
                .collect(Collectors.toSet());
        // 所有子菜品
        List<SubDineInItemDTO> subDineInItemList = dineInItemDTOS.stream()
                .filter(p -> ItemUtil.isGroup(p.getItemType()))
                .flatMap(p -> p.getPackageSubgroupDTOS().stream())
                .flatMap(g -> g.getSubDineInItemDTOS().stream()).collect(Collectors.toList());
        for (SubDineInItemDTO subDineInItemDTO : subDineInItemList) {
            itemGuidList.add(subDineInItemDTO.getItemGuid());
            // 原菜
            SubDineInItemDTO originalItem = subDineInItemDTO.getOriginalItem();
            if (Objects.nonNull(originalItem)) {
                itemGuidList.add(originalItem.getItemGuid());
            }
        }
        return queryItemInfoByStoreGuidAndItemGuids(storeGuid, new ArrayList<>(itemGuidList));
    }

    /**
     * 查询门店当前模式的菜品信息
     */
    private Map<String, ItemInfoRespDTO> queryItemInfoByStoreGuidAndItemGuids(String storeGuid, List<String> itemGuidList) {
        ItemStringListDTO listDTO = new ItemStringListDTO();
        listDTO.setStoreGuid(storeGuid);
        listDTO.setDataList(new ArrayList<>(itemGuidList));
        List<ItemInfoRespDTO> itemInfoRespDTOS = itemClientService.listItemInfoBySalesModel(listDTO);
        Map<String, ItemInfoRespDTO> itemInfoRespDTOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(itemInfoRespDTOS)) {
            itemInfoRespDTOMap = itemInfoRespDTOS.stream().collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, e -> e));
        }
        return itemInfoRespDTOMap;
    }


    private void appendCostPrice(OrderItemExtendsDO extendsDO, BigDecimal costPrice) {
        if (BigDecimalUtil.greaterThanZero(costPrice)) {
            BigDecimal orderItemCostPrice = extendsDO.getCostPrice();
            if (Objects.isNull(orderItemCostPrice)) {
                orderItemCostPrice = BigDecimal.ZERO;
            }
            extendsDO.setCostPrice(orderItemCostPrice.add(costPrice));
        }
    }

    /**
     * 添加套餐子商品
     */
    private void addPackageSubgroup(OrderPackageSubgroupBO packageSubgroupBiz, boolean isUseBuyPriceAndCoupon) {
        LocalDateTime now = LocalDateTime.now();
        DineInItemDTO dineInItemDTO = packageSubgroupBiz.getDineInItemDTO();
        Map<String, ItemInfoRespDTO> itemInfoRespDTOMap = packageSubgroupBiz.getItemInfoRespDTOMap();
        SubDineInItemDTO subDineInItemDTO = packageSubgroupBiz.getSubDineInItemDTO();
        PackageSubgroupDTO packageSubgroupDTO = packageSubgroupBiz.getPackageSubgroupDTO();
        OrderDO orderDO = packageSubgroupBiz.getOrderDO();
        List<OrderItemDO> orderItemDOS = packageSubgroupBiz.getOrderItemDOS();
        List<OrderItemExtendsDO> orderItemExtendsDOS = packageSubgroupBiz.getOrderItemExtendsDOS();
        OrderItemExtendsDO extendsDO = packageSubgroupBiz.getExtendsDO();
        if (StringUtils.isEmpty(subDineInItemDTO.getItemTypeGuid())) {
            throw new ParameterException("菜品分类不能为空");
        }
        if (Objects.isNull(dineInItemDTO.getSingleAddPriceTotal())) {
            dineInItemDTO.setSingleAddPriceTotal(BigDecimal.ZERO);
        }
        if (Objects.isNull(dineInItemDTO.getSingleItemAttrTotal())) {
            dineInItemDTO.setSingleItemAttrTotal(BigDecimal.ZERO);
        }
        // 子菜品核算价
        ItemInfoRespDTO subItemInfoRespDTO = itemInfoRespDTOMap.get(subDineInItemDTO.getItemGuid());
        SkuInfoRespDTO subSkuInfoRespDTO = getSkuInfoRespDTO(subItemInfoRespDTO, subDineInItemDTO.getSkuGuid());
        OrderItemDO subOrderItemDO = orderTransform.subDineInItemDTO2OrderItemDO(subDineInItemDTO);
        Long subOrderItemGuid = dynamicHelper.generateGuid(HST_ORDER_ITEM);
        subOrderItemDO.setGuid(subOrderItemGuid);
        subDineInItemDTO.setGuid(String.valueOf(subOrderItemGuid));
        subOrderItemDO.setIsPay(0);
        subOrderItemDO.setGmtCreate(now);
        if (StringUtils.isNotEmpty(extendsDO.getUserWxPublicOpenId())) {
            subOrderItemDO.setUserWxPublicOpenId(extendsDO.getUserWxPublicOpenId());
        }
        if (ObjectUtils.isEmpty(subOrderItemDO.getPrice())) {
            subOrderItemDO.setPrice(BigDecimal.ZERO);
        }
        subOrderItemDO.setOrderGuid(orderDO.getGuid());
        subOrderItemDO.setCurrentCount(subDineInItemDTO.getCurrentCount());
        subOrderItemDO.setFreeCount(BigDecimal.ZERO);
        subOrderItemDO.setReturnCount(BigDecimal.ZERO);
        subOrderItemDO.setPackageDefaultCount(subDineInItemDTO.getPackageDefaultCount());
        subOrderItemDO.setParentItemGuid(Long.valueOf(dineInItemDTO.getGuid()));
        subOrderItemDO.setSubgroupGuid(packageSubgroupDTO.getSubgroupGuid());
        subOrderItemDO.setSubgroupName(packageSubgroupDTO.getSubgroupName());
        subOrderItemDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
        subOrderItemDO.setCreateStaffName(UserContextUtils.getUserName());
        if (!ObjectUtils.isEmpty(extendsDO.getPadOrderGuid())) {
            subOrderItemDO.setPadOrderGuid(extendsDO.getPadOrderGuid());
        }
        if (BigDecimalUtil.greaterThanZero(subDineInItemDTO.getAddPrice())) {
            subOrderItemDO.setAddPrice(subDineInItemDTO.getAddPrice());
        }
        // 获取加价
        BigDecimal addPrice = getAddPrice(packageSubgroupBiz);
        dineInItemDTO.setSingleAddPriceTotal(dineInItemDTO.getSingleAddPriceTotal().add(addPrice));
        OrderItemExtendsDO orderItemExtendsDO = new OrderItemExtendsDO();
        if (!ObjectUtils.isEmpty(subSkuInfoRespDTO)) {
            subOrderItemDO.setAccountingPrice(subSkuInfoRespDTO.getAccountingPrice());
            appendCostPrice(extendsDO, subSkuInfoRespDTO.getCostPrice());
            orderItemExtendsDO.setCostPrice(subSkuInfoRespDTO.getCostPrice());
        }
        BeanUtils.copyProperties(subOrderItemDO, orderItemExtendsDO);
        if (isUseBuyPriceAndCoupon) {
            orderItemExtendsDO.setBeforeCouponAddPrice(subOrderItemDO.getAddPrice());
            subOrderItemDO.setAddPrice(BigDecimal.ZERO);
        }
        orderItemExtendsDOS.add(orderItemExtendsDO);
        orderItemDOS.add(subOrderItemDO);
        // 换菜之前的菜品保存
        addDeletePackageSubgroupOrderItem(packageSubgroupBiz);
        // 套餐子项 添加记录
        addPackageSubgroupOrderItemRecord(packageSubgroupBiz, subOrderItemDO, subSkuInfoRespDTO);
        // 属性价格
        BigDecimal attrTotalPrice = addAttrTotalPrice(packageSubgroupBiz, subOrderItemGuid, subOrderItemDO, isUseBuyPriceAndCoupon);
        dineInItemDTO.setSingleItemAttrTotal(dineInItemDTO.getSingleItemAttrTotal().add(attrTotalPrice));
        // + changeBatchNumber 避免重复计算原商品
        addChangeBatchNumber(packageSubgroupBiz);
    }

    /**
     * 换菜之前的菜品保存
     */
    private void addDeletePackageSubgroupOrderItem(OrderPackageSubgroupBO packageSubgroupBiz) {
        Map<String, ItemInfoRespDTO> itemInfoRespDTOMap = packageSubgroupBiz.getItemInfoRespDTOMap();
        SubDineInItemDTO subDineInItemDTO = packageSubgroupBiz.getSubDineInItemDTO();
        SubDineInItemDTO originalItem = subDineInItemDTO.getOriginalItem();
        if (Objects.isNull(originalItem)) {
            return;
        }
        List<String> addChangeBatchNumber = packageSubgroupBiz.getAddChangeBatchNumber();
        if (addChangeBatchNumber.contains(originalItem.getChangeBatchNumber())) {
            return;
        }
        DineInItemDTO dineInItemDTO = packageSubgroupBiz.getDineInItemDTO();
        PackageSubgroupDTO packageSubgroupDTO = packageSubgroupBiz.getPackageSubgroupDTO();
        OrderDO orderDO = packageSubgroupBiz.getOrderDO();
        List<OrderItemDO> orderItemDOS = packageSubgroupBiz.getOrderItemDOS();
        List<OrderItemExtendsDO> orderItemExtendsDOS = packageSubgroupBiz.getOrderItemExtendsDOS();
        OrderItemExtendsDO extendsDO = packageSubgroupBiz.getExtendsDO();
        ItemInfoRespDTO subItemInfoRespDTO = itemInfoRespDTOMap.get(originalItem.getItemGuid());
        SkuInfoRespDTO subSkuInfoRespDTO = getSkuInfoRespDTO(subItemInfoRespDTO, originalItem.getSkuGuid());
        OrderItemDO subOriginalItemOrderItemDO = orderTransform.subDineInItemDTO2OrderItemDO(originalItem);
        Long subOrderItemGuid = dynamicHelper.generateGuid(HST_ORDER_ITEM);
        subOriginalItemOrderItemDO.setGuid(subOrderItemGuid);
        originalItem.setGuid(String.valueOf(subOrderItemGuid));
        subOriginalItemOrderItemDO.setIsPay(0);
        subOriginalItemOrderItemDO.setIsDelete(true);
        subOriginalItemOrderItemDO.setGmtCreate(LocalDateTime.now());
        if (StringUtils.isNotEmpty(extendsDO.getUserWxPublicOpenId())) {
            subOriginalItemOrderItemDO.setUserWxPublicOpenId(extendsDO.getUserWxPublicOpenId());
        }
        if (ObjectUtils.isEmpty(subOriginalItemOrderItemDO.getPrice())) {
            subOriginalItemOrderItemDO.setPrice(BigDecimal.ZERO);
        }
        subOriginalItemOrderItemDO.setOrderGuid(orderDO.getGuid());
        subOriginalItemOrderItemDO.setCurrentCount(originalItem.getCurrentCount());
        subOriginalItemOrderItemDO.setFreeCount(BigDecimal.ZERO);
        subOriginalItemOrderItemDO.setReturnCount(BigDecimal.ZERO);
        subOriginalItemOrderItemDO.setPackageDefaultCount(originalItem.getPackageDefaultCount());
        subOriginalItemOrderItemDO.setParentItemGuid(Long.valueOf(dineInItemDTO.getGuid()));
        subOriginalItemOrderItemDO.setSubgroupGuid(packageSubgroupDTO.getSubgroupGuid());
        subOriginalItemOrderItemDO.setSubgroupName(packageSubgroupDTO.getSubgroupName());
        subOriginalItemOrderItemDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
        subOriginalItemOrderItemDO.setCreateStaffName(UserContextUtils.getUserName());
        if (!ObjectUtils.isEmpty(extendsDO.getPadOrderGuid())) {
            subOriginalItemOrderItemDO.setPadOrderGuid(extendsDO.getPadOrderGuid());
        }
        if (BigDecimalUtil.greaterThanZero(originalItem.getAddPrice())) {
            subOriginalItemOrderItemDO.setAddPrice(originalItem.getAddPrice());
        }
        if (!ObjectUtils.isEmpty(subSkuInfoRespDTO)) {
            subOriginalItemOrderItemDO.setAccountingPrice(subSkuInfoRespDTO.getAccountingPrice());
            appendCostPrice(extendsDO, subSkuInfoRespDTO.getCostPrice());
        }
        OrderItemExtendsDO orderItemExtendsDO = new OrderItemExtendsDO();
        BeanUtils.copyProperties(subOriginalItemOrderItemDO, orderItemExtendsDO);
        orderItemExtendsDOS.add(orderItemExtendsDO);
        orderItemDOS.add(subOriginalItemOrderItemDO);
        // 属性
        List<ItemAttrDTO> itemAttrDTOList = originalItem.getItemAttrDTOS();
        if (!CollectionUtils.isEmpty(itemAttrDTOList)) {
            List<ItemAttrDO> itemAttrDOS = packageSubgroupBiz.getItemAttrDOS();
            // 属性总价
            BigDecimal subAttrTotalPrice = BigDecimal.ZERO;
            List<Long> itemAttrGuidList = dynamicHelper.generateGuids(ITEM_ATTR_GUID, itemAttrDTOList.size());
            for (int l = 0; l < itemAttrDTOList.size(); l++) {
                ItemAttrDTO itemAttrDTO = itemAttrDTOList.get(l);
                ItemAttrDO itemAttrDO = orderTransform.itemAttrDTO2ItemAttrDO(itemAttrDTO);
                itemAttrDO.setOrderItemGuid(subOrderItemGuid);
                itemAttrDO.setGmtCreate(LocalDateTime.now());
                itemAttrDO.setGuid(itemAttrGuidList.get(l));
                itemAttrDO.setOrderGuid(orderDO.getGuid());
                itemAttrDO.setStoreGuid(UserContextUtils.getStoreGuid());
                itemAttrDO.setStoreName(UserContextUtils.getStoreName());
                itemAttrDTO.setGuid(String.valueOf(itemAttrDO.getGuid()));
                itemAttrDOS.add(itemAttrDO);
                subAttrTotalPrice = subAttrTotalPrice.add(itemAttrDTO.getAttrPrice());
                subOriginalItemOrderItemDO.setHasAttr(1);
            }
            // 单份商品的属性价
            subOriginalItemOrderItemDO.setAttrTotal(subAttrTotalPrice);
        }
    }

    /**
     * 套餐子项 添加记录
     */
    private void addPackageSubgroupOrderItemRecord(OrderPackageSubgroupBO packageSubgroupBiz,
                                                   OrderItemDO subOrderItemDO, SkuInfoRespDTO subSkuInfoRespDTO) {
        OrderDO orderDO = packageSubgroupBiz.getOrderDO();
        List<OrderItemRecordDO> itemRecordSave = packageSubgroupBiz.getItemRecordSave();
        DineInItemDTO dineInItemDTO = packageSubgroupBiz.getDineInItemDTO();

        OrderItemRecordDO itemRecordDO = orderTransform.orderItemDO2OrderItemRecordDO(subOrderItemDO);
        Long recordGuid = dynamicHelper.generateGuid(ORDER_ITEM_RECORD);
        itemRecordDO.setGuid(recordGuid);
        itemRecordDO.setType(1);
        itemRecordDO.setSkuName(dineInItemDTO.getSkuName());
        itemRecordDO.setSkuGuid(dineInItemDTO.getSkuGuid());
        itemRecordDO.setItemType(dineInItemDTO.getItemType());
        itemRecordDO.setItemTypeGuid(dineInItemDTO.getItemTypeGuid());
        itemRecordDO.setItemTypeName(dineInItemDTO.getItemTypeName());
        itemRecordDO.setUnit(dineInItemDTO.getUnit());
        itemRecordDO.setLog("【点菜】:子菜");
        if (orderDO.getRecoveryType().equals(RecoveryTypeEnum.NEW.getCode())) {
            itemRecordDO.setIsFromRecovery(1);
        }
        if (!ObjectUtils.isEmpty(subSkuInfoRespDTO)) {
            itemRecordDO.setAccountingPrice(subSkuInfoRespDTO.getAccountingPrice());
        }
        itemRecordSave.add(itemRecordDO);
    }

    /**
     * + changeBatchNumber 避免重复计算原商品
     */
    private void addChangeBatchNumber(OrderPackageSubgroupBO packageSubgroupBiz) {
        SubDineInItemDTO subDineInItemDTO = packageSubgroupBiz.getSubDineInItemDTO();
        SubDineInItemDTO originalItem = subDineInItemDTO.getOriginalItem();
        if (Objects.isNull(originalItem)) {
            return;
        }
        packageSubgroupBiz.getAddChangeBatchNumber().add(originalItem.getChangeBatchNumber());
    }

    /**
     * 获取套餐加价
     */
    private BigDecimal getAddPrice(OrderPackageSubgroupBO packageSubgroupBiz) {
        SubDineInItemDTO subDineInItemDTO = packageSubgroupBiz.getSubDineInItemDTO();
        SubDineInItemDTO originalItem = subDineInItemDTO.getOriginalItem();
        if (Objects.isNull(originalItem)) {
            return Optional.ofNullable(subDineInItemDTO.getAddPrice()).orElse(BigDecimal.ZERO).multiply(subDineInItemDTO.getCurrentCount());
        }
        List<String> addChangeBatchNumberList = packageSubgroupBiz.getAddChangeBatchNumber();
        if (addChangeBatchNumberList.contains(originalItem.getChangeBatchNumber())) {
            return BigDecimal.ZERO;
        }
        return Optional.ofNullable(originalItem.getAddPrice()).orElse(BigDecimal.ZERO).multiply(originalItem.getCurrentCount());
    }


    /**
     * 属性加价
     */
    private BigDecimal addAttrTotalPrice(OrderPackageSubgroupBO packageSubgroupBiz, Long subOrderItemGuid,
                                         OrderItemDO subOrderItemDO, boolean isUseBuyPriceAndCoupon) {
        SubDineInItemDTO subDineInItemDTO = packageSubgroupBiz.getSubDineInItemDTO();
        List<ItemAttrDTO> itemAttrDTOList = subDineInItemDTO.getItemAttrDTOS();
        BigDecimal subAttrTotalPrice = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(itemAttrDTOList)) {
            OrderDO orderDO = packageSubgroupBiz.getOrderDO();
            List<ItemAttrDO> itemAttrDOS = packageSubgroupBiz.getItemAttrDOS();
            // 属性总价
            List<Long> itemAttrGuidList = dynamicHelper.generateGuids(ITEM_ATTR_GUID, itemAttrDTOList.size());
            for (int l = 0; l < itemAttrDTOList.size(); l++) {
                ItemAttrDTO itemAttrDTO = itemAttrDTOList.get(l);
                ItemAttrDO itemAttrDO = orderTransform.itemAttrDTO2ItemAttrDO(itemAttrDTO);
                itemAttrDO.setOrderItemGuid(subOrderItemGuid);
                itemAttrDO.setGmtCreate(LocalDateTime.now());
                itemAttrDO.setGuid(itemAttrGuidList.get(l));
                itemAttrDO.setOrderGuid(orderDO.getGuid());
                itemAttrDO.setStoreGuid(UserContextUtils.getStoreGuid());
                itemAttrDO.setStoreName(UserContextUtils.getStoreName());
                itemAttrDTO.setGuid(String.valueOf(itemAttrDO.getGuid()));
                if (isUseBuyPriceAndCoupon) {
                    itemAttrDO.setBeforeCouponAttrPrice(itemAttrDO.getAttrPrice());
                    itemAttrDO.setAttrPrice(BigDecimal.ZERO);
                } else {
                    subAttrTotalPrice = subAttrTotalPrice.add(itemAttrDTO.getAttrPrice());
                }
                itemAttrDOS.add(itemAttrDO);
                subOrderItemDO.setHasAttr(1);
            }
            // 单份商品的属性价
            subOrderItemDO.setAttrTotal(subAttrTotalPrice);
        }
        // 属性总价计算
        SubDineInItemDTO originalItem = subDineInItemDTO.getOriginalItem();
        if (Objects.isNull(originalItem)) {
            // 属性数量
            BigDecimal subAttrNum = subDineInItemDTO.getCurrentCount();
            if (!subDineInItemDTO.getItemType().equals(ItemTypeEnum.WEIGH.getCode()) &&
                    BigDecimalUtil.greaterThanZero(subDineInItemDTO.getPackageDefaultCount())) {
                subAttrNum = subAttrNum.multiply(subDineInItemDTO.getPackageDefaultCount());
            }
            // 属性总价 = 套餐数量 * 子项数量 * 默认份数
            return subAttrTotalPrice.multiply(subAttrNum);
        }
        // 存在换菜，则使用原商品中的属性组计算价格
        if (packageSubgroupBiz.getAddChangeBatchNumber().contains(originalItem.getChangeBatchNumber())) {
            return BigDecimal.ZERO;
        }
        List<ItemAttrDTO> originalItemAttrDTOList = originalItem.getItemAttrDTOS();
        BigDecimal originalSubAttrTotalPrice = originalItemAttrDTOList.stream().map(ItemAttrDTO::getAttrPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 属性数量
        BigDecimal subAttrNum = originalItem.getCurrentCount();
        if (!originalItem.getItemType().equals(ItemTypeEnum.WEIGH.getCode()) &&
                BigDecimalUtil.greaterThanZero(originalItem.getPackageDefaultCount())) {
            subAttrNum = subAttrNum.multiply(originalItem.getPackageDefaultCount());
        }
        // 属性总价 = 套餐数量 * 子项数量 * 默认份数
        return originalSubAttrTotalPrice.multiply(subAttrNum);
    }

    /**
     * 发送消息给pad
     *
     * @param createDineInOrderReqDTO 入参信息
     */
    private void sendMsgToPad(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        BusinessMessageDTO payMessageDTO = new BusinessMessageDTO();
        payMessageDTO.setSubject(BusinessMsgTypeEnum.ACCEPT_ORDER.getName());
        payMessageDTO.setContent("一体机接单");
        payMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        payMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.ACCEPT_ORDER.getId());
        payMessageDTO.setPlatform("2");
        // 查询门店桌台对应设备信息
        PadOrderTypeReqDTO reqDTO = new PadOrderTypeReqDTO();
        reqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        reqDTO.setTableGuid(createDineInOrderReqDTO.getDiningTableGuid());
        StoreDeviceDTO storeDevice = organizationClientService.queryDeviceByStoreTable(reqDTO);
        if (ObjectUtils.isEmpty(storeDevice)) {
            log.warn("未查询到设备信息 reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
            return;
        }
        payMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + storeDevice.getDeviceNo());
        payMessageDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        payMessageDTO.setStoreName(UserContextUtils.getStoreName());
        log.info("接单 payMessageDTO={}", JacksonUtils.writeValueAsString(payMessageDTO));
        businessMessageService.msg(payMessageDTO);
    }

    /**
     * 并台一体机加菜推送消息
     *
     * @param orderDO       门店guid,门店名字
     * @param tableGuidList 桌台guid列表
     */
    private void combineSubmitOrderPushMsg(OrderDO orderDO, List<String> tableGuidList) {
        String storeGuid = orderDO.getStoreGuid();
        BusinessMessageDTO mainTableMessageDTO = new BusinessMessageDTO();
        mainTableMessageDTO.setSubject(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getName());
        mainTableMessageDTO.setContent("并台一体机加菜消息");
        mainTableMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        mainTableMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getId());
        mainTableMessageDTO.setPlatform("2");
        mainTableMessageDTO.setStoreGuid(storeGuid);
        mainTableMessageDTO.setStoreName(orderDO.getStoreName());

        // 查询子桌设备号
        PadOrderTypeReqDTO reqDTO = new PadOrderTypeReqDTO();
        reqDTO.setStoreGuid(storeGuid);
        reqDTO.setTableGuidList(tableGuidList);
        List<StoreDeviceDTO> storeDeviceDTOList = organizationClientService.listDeviceByStoreTable(reqDTO);
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(storeDeviceDTOList)) {
            log.error("门店桌台对应设备信息列表为空 reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
            return;
        }
        storeDeviceDTOList.forEach(device -> {
            mainTableMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + device.getDeviceNo());
            log.info("并台一体机加菜消息 mainTableMessageDTO={}", JacksonUtils.writeValueAsString(mainTableMessageDTO));
            businessMessageService.msg(mainTableMessageDTO);
        });
    }

    private void addItemAsync(CreateDineInOrderReqDTO createDineInOrderReqDTO,
                              BatchAddItemBO addItemBO) {
        OrderDO orderDO = addItemBO.getOrderDO();
        // 点菜单
        List<DineInItemDTO> printDineInItemDTOS2 = AmountCalculationUtil.buildItem(addItemBO.getOrderItemDOS(),
                addItemBO.getItemAttrDOS(), addItemBO.getFreeReturnItemDOS());
        //后厨只打印即起的菜品
        List<DineInItemDTO> backDineInItemDTOS = printDineInItemDTOS2.stream().filter(dineInItemDTO -> dineInItemDTO
                .getItemState() == ItemStateEnum.GENERAL.getCode()).collect(Collectors.toList());

        // 挂起单数据
        List<DineInItemDTO> hangUpDineInItemDTOS = printDineInItemDTOS2.stream().filter(dineInItemDTO -> dineInItemDTO
                .getItemState() == ItemStateEnum.HANG_UP.getCode()).collect(Collectors.toList());

        for (DineInItemDTO backDineInItemDTO : backDineInItemDTOS) {
            List<FreeItemDTO> freeItemDTOS = backDineInItemDTO.getFreeItemDTOS();
            if (CollectionUtil.isNotEmpty(freeItemDTOS)) {
                backDineInItemDTO.setFreeItemDTOS(freeItemDTOS.stream().filter(freeItemDTO -> freeItemDTO
                        .getItemState() == ItemStateEnum.GENERAL.getCode()).collect(Collectors.toList()));
            }
        }
        if (createDineInOrderReqDTO.getPrint() == 1) {
            DineinOrderDetailRespDTO dineinOrderDetailRespDTO = orderTransform.orderDO2DineinOrderDetailRespDTO
                    (orderDO);
            dineinOrderDetailRespDTO.setDineInItemDTOS(printDineInItemDTOS2);
            // 打印挂起单
            if (CollectionUtil.isNotEmpty(hangUpDineInItemDTOS)) {
                dineInPrintService.printOrderItem(createDineInOrderReqDTO, orderDO, hangUpDineInItemDTOS,
                        PrintOrderItemDTO.ItemInvoiceTypeEnum.HANG_UP.getType());
            }
            //kds加菜
            kdsService.prepare(dineinOrderDetailRespDTO, createDineInOrderReqDTO);
            printOrderItem(createDineInOrderReqDTO, backDineInItemDTOS, orderDO);
            dineInPrintService.printLabelItem(createDineInOrderReqDTO, orderDO, backDineInItemDTOS);
        }

        // 菜品清单
        // 附加费详情
        OrderFeeDetailDTO orderFeeDetailDTO = null;
        if (BigDecimalUtil.greaterThanZero(orderDO.getAppendFee())) {
            orderFeeDetailDTO = new OrderFeeDetailDTO();
            SingleDataDTO singleDataDTO = new SingleDataDTO();
            singleDataDTO.setData(String.valueOf(orderDO.getGuid()));
            orderFeeDetailDTO.setAppendFeeDetailDTOS(appendFeeService.appendFeeList(singleDataDTO));
        }
        dineInPrintService.printItemDetail(createDineInOrderReqDTO, orderDO, orderFeeDetailDTO, printDineInItemDTOS2);

        // 更新桌台状态
        TableStatusChangeDTO tableStatusChangeDTO = orderTransform.createDineInOrderReqDTO2TableStatusChangeDTO
                (createDineInOrderReqDTO);
        tableStatusChangeDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));
        tableStatusChangeDTO.setTableGuid(orderDO.getDiningTableGuid());
        tableStatusChangeDTO.setTableStatusChange(TableStatusChangeEnum.ORDER_DISH_CHANGE.getId());
        tableStatusChangeDTO.setEnableManualClear(UpperStateEnum.SAME_ORDER_STATE.contains(orderDO.getUpperState()) ?
                BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
        tableClientService.tableStatusChange(tableStatusChangeDTO);
    }

    private void printOrderItem(CreateDineInOrderReqDTO createDineInOrderReqDTO,
                                List<DineInItemDTO> backDineInItemDTOS,
                                OrderDO orderDO) {
        if (CollectionUtil.isNotEmpty(backDineInItemDTOS)) {
            List<DineInItemDTO> backDineInItemList = backDineInItemDTOS.stream()
                    .filter(DineInItemDTO::getIsKitchen)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(backDineInItemList)) {
                log.info("没有需要打厨的商品");
                return;
            }
            dineInPrintService.printOrderItem(createDineInOrderReqDTO, orderDO, backDineInItemList, null);
        }
    }

    @Override
    @Transactional
    public BatchItemReturnOrFreeReqDTO returnOrFreeItem(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO,
                                                        Boolean isReturn, Boolean isCheck) {

        List<OrderItemDO> returnEstimateList = new ArrayList<>();
        List<OrderItemDO> returnPrintList = new ArrayList<>();

        OrderDO orderDO = handleReturnOrFreeItem(batchItemReturnOrFreeReqDTO, isReturn, isCheck, returnPrintList, returnEstimateList);

        //退货时修改订单金额
        if (orderDO != null) {
            DineinOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(String.valueOf(orderDO.getGuid()));
            BigDecimal orderFee = AmountCalculationUtil.getOrderFee(orderDetail.getDineInItemDTOS(), BigDecimal.ZERO);
            if (BigDecimalUtil.greaterThanZero(orderDO.getAppendFee())) {
                orderFee = orderFee.add(orderDO.getAppendFee());
            }
            orderDO.setOrderFee(orderFee);
            orderDO.setOrderFeeForCombine(orderFee);
            orderService.updateById(orderDO);
            // 更新多单结账的主单
            updateOrderFeeForSameOrder(orderDO);
        }


        if (Boolean.TRUE.equals(isReturn) && !batchItemReturnOrFreeReqDTO.isFastFood()) {
            //更新桌台状态
            tableService.returnChange(batchItemReturnOrFreeReqDTO, orderDO);
        }

        //打印退菜单
        if (Boolean.TRUE.equals(isReturn)) {
            DineinOrderDetailRespDTO dineinOrderDetailRespDTO = orderTransform.orderDO2DineinOrderDetailRespDTO
                    (orderDO);
            List<DineInItemDTO> returnEstimateDineInItemDTOS = AmountCalculationUtil.buildItem(returnEstimateList,
                    Collections.emptyList(), Collections.emptyList());
            List<DineInItemDTO> returnPrintDineInItemDTOS = AmountCalculationUtil.buildItem(returnPrintList,
                    Collections.emptyList(), Collections.emptyList());
            dineinOrderDetailRespDTO.setDineInItemDTOS(returnPrintDineInItemDTOS);
            dineInPrintService.printRefundItem(batchItemReturnOrFreeReqDTO, dineinOrderDetailRespDTO);
            dineinOrderDetailRespDTO.setDineInItemDTOS(returnEstimateDineInItemDTOS);
            //快餐反结账已经退过不重复退
            if (!dineinOrderDetailRespDTO.getTradeMode().equals(TradeModeEnum.FAST.getCode())) {
                itemClientService.returnEstimate(returnEstimateDineInItemDTOS);
            }


        }

        // 将退菜后的订单金额存入缓存
        if (orderDO != null) {
            redisHelper.setEx(OrderRedisConstant.REDIS_ORDER_FEE + orderDO.getGuid(),
                    String.valueOf(orderDO.getOrderFee()), 30, TimeUnit.SECONDS);
        }

        return batchItemReturnOrFreeReqDTO;
    }

    private OrderDO handleReturnOrFreeItem(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO,
                                           Boolean isReturn, Boolean isCheck,
                                           List<OrderItemDO> returnPrintList, List<OrderItemDO> returnEstimateList) {
        // 构建
        ReturnOrFreeItemBO biz = buildReturnOrFreeItemBiz(batchItemReturnOrFreeReqDTO, returnPrintList, returnEstimateList, isReturn, isCheck);
        log.info("赠送/退菜信息：{}", JacksonUtils.writeValueAsString(biz));
        if (CollectionUtil.isNotEmpty(biz.getSaveOrderItemDOS())) {
            orderItemService.saveBatch(biz.getSaveOrderItemDOS());
        }
        if (CollectionUtil.isNotEmpty(biz.getSaveOrderItemExtendDOS())) {
            orderItemExtendsService.saveOrUpdateBatch(biz.getSaveOrderItemExtendDOS());
        }
        if (CollectionUtil.isNotEmpty(biz.getItemUpdateList())) {
            orderItemService.updateBatchByIdWithDeleteCache(biz.getItemUpdateList(), String.valueOf(biz.getOrderDO().getGuid()));
        }
        if (CollectionUtil.isNotEmpty(biz.getSaveList())) {
            freeReturnItemService.saveBatch(biz.getSaveList());
        }
        if (CollectionUtil.isNotEmpty(biz.getUpdateList())) {
            freeReturnItemService.updateBatchById(biz.getUpdateList());
        }
        if (CollectionUtil.isNotEmpty(biz.getSaveItemAttrDOS())) {
            itemAttrService.saveOrUpdateBatch(biz.getSaveItemAttrDOS());
        }
        if (CollectionUtil.isNotEmpty(biz.getSaveOrderItemChangesDOS())) {
            orderItemChangesService.saveOrUpdateBatch(biz.getSaveOrderItemChangesDOS());
        }
        // 撤销验券
        grouponUndo(biz.getItemUpdateList());
        return biz.getOrderDO();
    }


    private ReturnOrFreeItemBO buildReturnOrFreeItemBiz(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO,
                                                        List<OrderItemDO> returnPrintList, List<OrderItemDO> returnEstimateList,
                                                        Boolean isReturn, Boolean isCheck) {
        ReturnOrFreeItemBO biz = ReturnOrFreeItemBizBuilder.build();
        biz.setReturnPrintList(returnPrintList);
        biz.setReturnEstimateList(returnEstimateList);
        biz.setIsCheck(isCheck);
        biz.setIsReturn(isReturn);

        List<BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq> itemReturnOrFreeReqs = batchItemReturnOrFreeReqDTO.getItemReturnOrFreeReqs();
        Map<String, BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq> returnOrFreeReqMap = CollectionUtil.toMap(itemReturnOrFreeReqs, ORDER_ITEM_GUID);
        List<OrderItemDO> orderItemDOS = orderItemService.listByIdsWithLock(returnOrFreeReqMap.keySet());
        Map<Long, OrderItemDO> itemDOMap = CollectionUtil.toMap(orderItemDOS, "guid");
        biz.setItemDOMap(itemDOMap);

        ItemBatchRefundReqDTO reqDTO = new ItemBatchRefundReqDTO();
        List<ItemRefundReqDTO> itemRefundList = itemReturnOrFreeReqs.stream().map(i -> {
            ItemRefundReqDTO refundReqDTO = new ItemRefundReqDTO();
            refundReqDTO.setOrderItemGuid(i.getOrderItemGuid());
            refundReqDTO.setNumber(i.getItemReturnOrFreeReqDTOS().get(0).getCount().intValue());
            return refundReqDTO;
        }).collect(Collectors.toList());
        reqDTO.setItemRefundList(itemRefundList);
        List<KitchenItemRespDTO> respDTOList = kdsClientService.queryByOrderItem(reqDTO);
        Map<String, Boolean> outDinnerMap = respDTOList.stream()
                .collect(Collectors.toMap(KitchenItemRespDTO::getOrderItemGuid, KitchenItemRespDTO::getIsOutDinner));
        biz.setOutDinnerMap(outDinnerMap);

        for (BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq itemReturnOrFreeReq : itemReturnOrFreeReqs) {
            itemReturnOrFreeReqInnerHandler(biz, itemReturnOrFreeReq, batchItemReturnOrFreeReqDTO);
        }
        return biz;
    }

    private void itemReturnOrFreeReqInnerHandler(ReturnOrFreeItemBO biz,
                                                 BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq itemReturnOrFreeReq,
                                                 BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO) {
        List<OrderItemDO> itemUpdateList = biz.getItemUpdateList();
        Boolean isCheck = biz.getIsCheck();

        biz.setAdd(false);
        OrderItemDO orderItemDO = biz.getItemDOMap().get(Long.valueOf(itemReturnOrFreeReq.getOrderItemGuid()));
        OrderDO orderDO = biz.getOrderDO();
        if (orderDO == null) {
            orderDO = orderService.getByIdWithLock(orderItemDO.getOrderGuid());
            biz.setOrderDO(orderDO);
        }
        if (Boolean.TRUE.equals(isCheck) && !OrderUtil.unfinished(orderDO)) {
            throw new ParameterException("订单状态异常");
        }
        // 校验多单结账 订单
        verifyOrderForSameOrder(orderDO);
        OrderItemDO returnPrint = orderTransform.orderItemDO2OrderItemDO(orderItemDO);

        List<ItemReturnOrFreeReqDTO> itemReturnOrFreeReqDTOS = itemReturnOrFreeReq.getItemReturnOrFreeReqDTOS();
        Map<String, ItemReturnOrFreeReqDTO> freeReqDTOMap = CollectionUtil.toMap(itemReturnOrFreeReqDTOS,
                "freeGuid");

        Map<Long, FreeReturnItemDO> freeReturnItemDOMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(freeReqDTOMap)) {
            List<FreeReturnItemDO> freeReturnItemDOS = new ArrayList<>(freeReturnItemService.listByIds(new
                    ArrayList<>(freeReqDTOMap.keySet())));
            freeReturnItemDOMap = CollectionUtil.toMap(freeReturnItemDOS, "guid");
        }
        biz.setFreeReturnItemDOMap(freeReturnItemDOMap);

        handlePriceChange(biz, itemReturnOrFreeReqDTOS, orderItemDO);

        for (ItemReturnOrFreeReqDTO itemReturnOrFreeReqDTO : itemReturnOrFreeReqDTOS) {
            handleItemReturnOrFree(biz, batchItemReturnOrFreeReqDTO, itemReturnOrFreeReqDTO, orderItemDO, returnPrint);
        }
        orderItemDO.setGmtModified(null);
        if (BigDecimalUtil.lessThanZero(orderItemDO.getCurrentCount()) || BigDecimalUtil.lessThanZero(orderItemDO
                .getFreeCount())) {
            throw new ParameterException("操作数量超出限制");
        }
        itemUpdateList.add(orderItemDO);
    }

    private void handleItemReturnOrFree(ReturnOrFreeItemBO biz,
                                        BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO,
                                        ItemReturnOrFreeReqDTO itemReturnOrFreeReqDTO,
                                        OrderItemDO orderItemDO,
                                        OrderItemDO returnPrint) {
        Boolean isReturn = biz.getIsReturn();
        OrderDO orderDO = biz.getOrderDO();
        List<FreeReturnItemDO> saveList = biz.getSaveList();
        List<OrderItemDO> returnPrintList = biz.getReturnPrintList();
        List<OrderItemDO> returnEstimateList = biz.getReturnEstimateList();
        OrderItemDO saveOrderItemDO = biz.getSaveOrderItemDO();

        BigDecimal count = itemReturnOrFreeReqDTO.getCount();
        // 退菜数量不会很多，暂时不改为批量生成
        Long guid = dynamicHelper.generateGuid(FREE_RETURN_ITEM);
        itemReturnOrFreeReqDTO.setGuid(String.valueOf(guid));
        FreeReturnItemDO freeReturnItemDO = orderTransform.orderItemDO2freeReturnItemDO(orderItemDO);
        freeReturnItemDO.setStaffName(UserContextUtils.getUserName());
        freeReturnItemDO.setStaffGuid(UserContextUtils.getUserGuid());
        freeReturnItemDO.setGuid(guid);
        Long orderItemGuid = biz.isAdd() ? saveOrderItemDO.getGuid() : orderItemDO.getGuid();
        freeReturnItemDO.setOrderItemGuid(orderItemGuid);
        freeReturnItemDO.setOrderGuid(orderItemDO.getOrderGuid());
        freeReturnItemDO.setCount(count);
        freeReturnItemDO.setReason(itemReturnOrFreeReqDTO.getReason());
        freeReturnItemDO.setStoreGuid(UserContextUtils.getStoreGuid());
        freeReturnItemDO.setStoreName(UserContextUtils.getStoreName());
        freeReturnItemDO.setItemGuid(orderItemDO.getItemGuid());
        freeReturnItemDO.setItemName(orderItemDO.getItemName());
        freeReturnItemDO.setPrice(Boolean.TRUE.equals(isReturn) ? orderItemDO.getPrice() : BigDecimalUtil.nonNullValue(orderItemDO.getOriginalPrice()));
        freeReturnItemDO.setType(Boolean.TRUE.equals(isReturn) ? FreeReturnTypeEnum.RETURN.getCode() : FreeReturnTypeEnum.FREE
                .getCode());
        freeReturnItemDO.setIsFree(itemReturnOrFreeReqDTO.getIsFree());
        freeReturnItemDO.setAuthStaffGuid(batchItemReturnOrFreeReqDTO.getAuthStaffGuid());
        freeReturnItemDO.setAuthStaffName(batchItemReturnOrFreeReqDTO.getAuthStaffName());
        freeReturnItemDO.setAuthStaffPicture(batchItemReturnOrFreeReqDTO.getAuthStaffPicture());
        // 操作节点相关
        freeReturnItemDO.setOrderState(orderDO.getState());
        freeReturnItemDO.setRecoveryType(orderDO.getRecoveryType());
        Boolean isOutDinner = biz.getOutDinnerMap().get(String.valueOf(orderItemGuid));
        if (ObjectUtils.isEmpty(isOutDinner)) {
            isOutDinner = Boolean.TRUE;
        }
        freeReturnItemDO.setIsOutDinner(isOutDinner);

        BigDecimal actuallyRefundFee = BigDecimal.ZERO;
        if (BigDecimalUtil.greaterThanZero(orderItemDO.getCurrentCount()) &&
                BigDecimalUtil.greaterThanZero(orderItemDO.getDiscountTotalPrice())) {
            actuallyRefundFee = orderItemDO.getDiscountTotalPrice()
                    .divide(orderItemDO.getCurrentCount(), 2, RoundingMode.HALF_DOWN).multiply(count);
        }
        freeReturnItemDO.setActuallyRefundFee(actuallyRefundFee);
        if (Boolean.TRUE.equals(isReturn)) {
            // 保存退菜记录
            saveReturnItemRecord(biz, itemReturnOrFreeReqDTO, orderItemDO, freeReturnItemDO);
        } else {
            // 保存赠菜记录
            saveFreeItemRecord(biz, itemReturnOrFreeReqDTO, orderItemDO, saveOrderItemDO, freeReturnItemDO, biz.isAdd());
        }
        saveList.add(freeReturnItemDO);
        //暂存退菜数量
        returnPrint.setCurrentCount(count);
        //挂起菜品退菜时不打印退菜单
        if (!returnPrint.getItemState().equals(ItemStateEnum.HANG_UP.getCode())) {
            returnPrintList.add(returnPrint);
        }
        returnEstimateList.add(returnPrint);
        if (Boolean.TRUE.equals(ItemUtil.isGroup(returnPrint.getItemType()))) {
            List<OrderItemDO> listByMainItemGuid = orderItemService.listByMainItemGuid(returnPrint.getGuid());
            returnPrintList.addAll(listByMainItemGuid);
            returnEstimateList.addAll(listByMainItemGuid);
        }
    }

    private void handlePriceChange(ReturnOrFreeItemBO biz,
                                   List<ItemReturnOrFreeReqDTO> itemReturnOrFreeReqDTOS,
                                   OrderItemDO orderItemDO) {
        List<OrderItemDO> saveOrderItemDOS = biz.getSaveOrderItemDOS();
        List<ItemAttrDO> saveItemAttrDOS = biz.getSaveItemAttrDOS();
        List<OrderItemExtendsDO> saveOrderItemExtendDOS = biz.getSaveOrderItemExtendDOS();
        List<OrderItemChangesDO> saveOrderItemChangesDOS = biz.getSaveOrderItemChangesDOS();
        OrderDO orderDO = biz.getOrderDO();
        OrderItemDO saveOrderItemDO = null;
        BigDecimal allItemCount = itemReturnOrFreeReqDTOS.stream().map(ItemReturnOrFreeReqDTO::getCount).reduce
                (BigDecimal::add).orElse(BigDecimal.ZERO);
        log.info("赠菜或退菜的数量是{}，总数是{}", allItemCount, orderItemDO.getCurrentCount());
        if (Boolean.FALSE.equals(biz.getIsReturn())) {
            if (allItemCount.compareTo(orderItemDO.getCurrentCount()) < 0) {
                biz.setAdd(true);
                //拆分成一个新的orderItem
                Long itemguid = dynamicHelper.generateGuid(HST_ORDER_ITEM);
                saveOrderItemDO = JacksonUtils.toObject(OrderItemDO.class, JacksonUtils
                        .writeValueAsString(orderItemDO));
                saveOrderItemDO.setGuid(itemguid);
                //===================== 制空，使用系统默认值 =====================
                saveOrderItemDO.setGmtCreate(null);
                saveOrderItemDO.setGmtModified(null);
                saveOrderItemDO.setCurrentCount(BigDecimal.ZERO);
                saveOrderItemDO.setFreeCount(BigDecimal.ZERO);
                saveOrderItemDO.setReturnCount(BigDecimal.ZERO);
                saveOrderItemDO.setPrice(orderItemDO.getOriginalPrice());
                saveOrderItemDO.setPriceChangeType(0);
                saveOrderItemDOS.add(saveOrderItemDO);
                //套餐
                handleGroupItem(orderItemDO, saveOrderItemDO, saveOrderItemDOS, saveItemAttrDOS,
                        saveOrderItemExtendDOS, saveOrderItemChangesDOS);
            } else {
                orderItemDO.setPrice(orderItemDO.getOriginalPrice());
                orderItemDO.setPriceChangeType(0);
            }
            if (Objects.nonNull(orderItemDO.getPriceChangeType())) {
                if (orderItemDO.getPriceChangeType() == 1) {
                    orderDO.setOrderFee(orderDO.getOrderFee().add(orderItemDO.getOriginalPrice().subtract(orderItemDO
                            .getPrice()).multiply(allItemCount)));
                } else if (orderItemDO.getPriceChangeType() == 2) {
                    orderDO.setOrderFee(orderDO.getOrderFee().add(orderItemDO.getOriginalPrice()
                            .multiply(new BigDecimal(1000).subtract(new BigDecimal(orderItemDO.getDiscountPercent()))
                                    .divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP)
                            ).multiply(allItemCount)));
                }
            }
        }
        biz.setSaveOrderItemDO(saveOrderItemDO);
    }

    private void handleGroupItem(OrderItemDO orderItemDO, OrderItemDO saveOrderItemDO,
                                 List<OrderItemDO> saveOrderItemDOS, List<ItemAttrDO> saveItemAttrDOS,
                                 List<OrderItemExtendsDO> saveOrderItemExtendDOS,
                                 List<OrderItemChangesDO> saveOrderItemChangesDOS) {
        if (Boolean.FALSE.equals(ItemUtil.isGroup(saveOrderItemDO.getItemType()))) {
            return;
        }
        // 订单商品明细
        Map<Long, Long> subOrderItemGuidMap = copySubOrderItem(saveOrderItemDO, orderItemDO, saveOrderItemDOS);
        List<Long> subOrderItemGuidList = new ArrayList<>(subOrderItemGuidMap.keySet());
        // 订单商品明细扩展表
        copySubOrderItemExtends(subOrderItemGuidMap, orderItemDO, saveOrderItemExtendDOS);
        // 子项商品关联的属性
        copySubOrderItemAttr(subOrderItemGuidMap, subOrderItemGuidList, saveItemAttrDOS);
        // 订单商品换菜
        copySubOrderItemChanges(subOrderItemGuidMap, orderItemDO, subOrderItemGuidList, saveOrderItemChangesDOS);
    }

    /**
     * 保存退菜记录
     */
    private void saveReturnItemRecord(ReturnOrFreeItemBO biz, ItemReturnOrFreeReqDTO itemReturnOrFreeReqDTO,
                                      OrderItemDO orderItemDO, FreeReturnItemDO freeReturnItemDO) {
        BigDecimal count = itemReturnOrFreeReqDTO.getCount();
        // 保存到菜品流水表中
        OrderItemRecordDO orderItemRecordDO1 = OrderTransform.INSTANCE.orderItemDO2OrderItemRecordDO(orderItemDO);
        Long recordGuid = dynamicHelper.generateGuid(ORDER_ITEM_RECORD);
        orderItemRecordDO1.setGuid(recordGuid);
        if (biz.getOrderDO().getRecoveryType().equals(RecoveryTypeEnum.NEW.getCode())) {
            orderItemRecordDO1.setIsFromRecovery(1);
        }
        orderItemRecordDO1.setReturnCount(count);
        orderItemRecordDO1.setType(3);
        orderItemRecordDO1.setLog("【退菜】:正常退菜，增加退菜数");
        orderItemRecordService.saveBatchWithDeleteCache(orderItemRecordDO1);

        //增加退菜数
        orderItemDO.setReturnCount(orderItemDO.getReturnCount().add(count));
        //退货为赠送
        if (itemReturnOrFreeReqDTO.getIsFree() != null && itemReturnOrFreeReqDTO.getIsFree() == 1) {
            // 减少赠送数量
            // 保存到菜品流水表中
            OrderItemRecordDO orderItemRecordDO2 = OrderTransform.INSTANCE.orderItemDO2OrderItemRecordDO(orderItemDO);
            Long recordGuid2 = dynamicHelper.generateGuid(ORDER_ITEM_RECORD);
            orderItemRecordDO2.setGuid(recordGuid2);
            if (biz.getOrderDO().getRecoveryType().equals(RecoveryTypeEnum.NEW.getCode())) {
                orderItemRecordDO2.setIsFromRecovery(1);
            }
            orderItemRecordDO2.setFreeCount(count.negate());
            orderItemRecordDO2.setType(2);
            orderItemRecordDO2.setLog("【退菜】:退货为赠送，减少赠送数量");
            orderItemRecordService.saveBatchWithDeleteCache(orderItemRecordDO2);

            orderItemDO.setFreeCount(orderItemDO.getFreeCount().subtract(count));
            FreeReturnItemDO freeReturnItemServiceById = biz.getFreeReturnItemDOMap().get(Long.valueOf
                    (itemReturnOrFreeReqDTO.getFreeGuid()));
            freeReturnItemServiceById.setCount(freeReturnItemServiceById.getCount().subtract(count));
            biz.getUpdateList().add(freeReturnItemServiceById);
            freeReturnItemDO.setItemState(freeReturnItemServiceById.getItemState());
        } else {
            orderItemDO.setCurrentCount(orderItemDO.getCurrentCount().subtract(count));
        }
    }

    /**
     * 保存赠送记录
     */
    private void saveFreeItemRecord(ReturnOrFreeItemBO biz, ItemReturnOrFreeReqDTO itemReturnOrFreeReqDTO,
                                    OrderItemDO orderItemDO, OrderItemDO saveOrderItemDO, FreeReturnItemDO freeReturnItemDO,
                                    boolean isadd) {
        BigDecimal count = itemReturnOrFreeReqDTO.getCount();
        // 保存到菜品流水表中
        OrderItemRecordDO orderItemRecordDO2 = OrderTransform.INSTANCE.orderItemDO2OrderItemRecordDO(orderItemDO);
        Long recordGuid2 = dynamicHelper.generateGuid(ORDER_ITEM_RECORD);
        orderItemRecordDO2.setGuid(recordGuid2);
        if (biz.getOrderDO().getRecoveryType().equals(RecoveryTypeEnum.NEW.getCode())) {
            orderItemRecordDO2.setIsFromRecovery(1);
        }
        orderItemRecordDO2.setFreeCount(count);
        orderItemRecordDO2.setType(2);
        orderItemRecordDO2.setLog("【赠菜】:赠菜是加菜，增加当前点菜数量，增加赠菜数量");
        orderItemRecordService.saveBatchWithDeleteCache(orderItemRecordDO2);
        if (isadd) {
            saveOrderItemDO.setFreeCount(saveOrderItemDO.getFreeCount().add(count));
        } else {
            orderItemDO.setFreeCount(orderItemDO.getFreeCount().add(count));
        }
        //判断赠送是否被划菜 后期改为数量之后只有前端传入
        if (itemReturnOrFreeReqDTO.getIsServe() != null && itemReturnOrFreeReqDTO.getIsServe() == 1) {
            freeReturnItemDO.setServeCount(freeReturnItemDO.getCount());
            freeReturnItemDO.setItemState(ItemStateEnum.SERVED.getCode());
        } else {
            freeReturnItemDO.setServeCount(BigDecimal.ZERO);
        }
        //赠送加了，点菜就不用加在菜品流水表了
        orderItemDO.setCurrentCount(orderItemDO.getCurrentCount().subtract(count));
    }

    @Override
    @Transactional
    public Boolean cancelFree(CancelFreeItemReqDTO cancelFreeItemReqDTO) {
        List<OrderItemDO> updateOrderItemDOS = new ArrayList<>();
        List<OrderItemDO> saveOrderItemDOS = new ArrayList<>();
        List<String> removeIds = new ArrayList<>();
        List<String> freeItemGuids = cancelFreeItemReqDTO.getFreeItemGuids();
        String orderGuid = "";
        List<FreeReturnItemDO> freeReturnItemDOS = new ArrayList<>(freeReturnItemService.listByIds(freeItemGuids));
        if (CollectionUtil.isNotEmpty(freeReturnItemDOS)) {
            Map<Long, FreeReturnItemDO> freeReturnItemDOMap = CollectionUtil.toMap(freeReturnItemDOS, "guid");
            Map<Long, FreeReturnItemDO> freeReturnItemDOMapByOrderItemGuid = CollectionUtil.toMap(freeReturnItemDOS,
                    ORDER_ITEM_GUID);
            List<OrderItemDO> orderItemDOS = new ArrayList<>(orderItemService.listByIds(new ArrayList<>
                    (freeReturnItemDOMapByOrderItemGuid.keySet())));
            Map<Long, OrderItemDO> orderItemDOMap = CollectionUtil.toMap(orderItemDOS, "guid");
            for (String freeItemGuid : freeItemGuids) {
                FreeReturnItemDO freeReturnItemDO = freeReturnItemDOMap.get(Long.valueOf(freeItemGuid));
                OrderItemDO orderItemDO = orderItemDOMap.get(freeReturnItemDO.getOrderItemGuid());
                orderItemDO.setFreeCount(orderItemDO.getFreeCount().subtract(freeReturnItemDO.getCount()));

                /**
                 * 保存到菜品流水表中
                 */
                OrderItemRecordDO orderItemRecordDO2 = OrderTransform.INSTANCE.orderItemDO2OrderItemRecordDO(orderItemDO);
                Long recordGuid = dynamicHelper.generateGuid(ORDER_ITEM_RECORD);
                orderItemRecordDO2.setGuid(recordGuid);
                OrderDO orderDO = orderService.getByIdWithCache(String.valueOf(orderItemDO.getOrderGuid()));
                if (orderDO.getRecoveryType().equals(RecoveryTypeEnum.NEW.getCode())) {
                    orderItemRecordDO2.setIsFromRecovery(1);
                }
                orderItemRecordDO2.setFreeCount(freeReturnItemDO.getCount().negate());
                orderItemRecordDO2.setType(2);
                orderItemRecordDO2.setLog("【赠菜】:取消赠菜，减少赠菜数量");
                orderItemRecordService.saveBatchWithDeleteCache(orderItemRecordDO2);

                removeIds.add(freeItemGuid);
                boolean add = !Objects.equals(freeReturnItemDO.getItemState(), orderItemDO.getItemState());
                //状态不一致时拆分菜品
                if (add) {
                    OrderItemDO saveOrderItemDO = JacksonUtils.toObject(OrderItemDO.class, JacksonUtils
                            .writeValueAsString(orderItemDO));
                    Long guid = dynamicHelper.generateGuid(HST_ORDER_ITEM);
                    saveOrderItemDO.setGuid(guid);
                    saveOrderItemDO.setCurrentCount(freeReturnItemDO.getCount());
                    saveOrderItemDO.setFreeCount(BigDecimal.ZERO);
                    saveOrderItemDO.setReturnCount(BigDecimal.ZERO);
                    saveOrderItemDO.setItemState(freeReturnItemDO.getItemState());
                    saveOrderItemDO.setUrgeNum(freeReturnItemDO.getUrgeNum());
                    saveOrderItemDOS.add(saveOrderItemDO);
                    //套餐
                    if (ItemUtil.isGroup(saveOrderItemDO.getItemType())) {
                        List<OrderItemDO> subOrderItemDOS = orderItemService.listByMainItemGuid(orderItemDO.getGuid());
                        for (OrderItemDO subOrderItemDO : subOrderItemDOS) {
                            subOrderItemDO.setGuid(dynamicHelper.generateGuid(HST_ORDER_ITEM));
                            subOrderItemDO.setParentItemGuid(guid);
                            subOrderItemDO.setItemState(freeReturnItemDO.getItemState());
                            saveOrderItemDOS.add(subOrderItemDO);
                        }
                    }
                } else {
                    orderItemDO.setCurrentCount(orderItemDO.getCurrentCount().add(freeReturnItemDO.getCount()));
                    orderItemDO.setUrgeNum(freeReturnItemDO.getUrgeNum());
                }
                updateOrderItemDOS.add(orderItemDO);
                orderGuid = String.valueOf(orderItemDO.getOrderGuid());
            }
            if (CollectionUtil.isNotEmpty(removeIds)) {
                freeReturnItemService.removeByIds(removeIds);
            }
            orderItemService.updateBatchByIdWithDeleteCache(updateOrderItemDOS, orderGuid);
            orderItemService.saveBatchWithDeleteCache(saveOrderItemDOS, orderGuid);
        }

        return Boolean.TRUE;
    }

    @Override
    public Boolean batchServeItem(ServeItemReqDTO serveItemReqDTO) {
        List<ServeItemReqDTO.ServeItem> serveItems = serveItemReqDTO.getServeItems();
        OrderDO orderDO = orderService.getByIdWithLock(serveItemReqDTO.getOrderGuid());

        if (!orderDO.getState().equals(StateEnum.READY.getCode())) {
            throw new ParameterException("订单状态异常");
        }
        updateItem(serveItems, orderDO);

        return Boolean.TRUE;
    }

    private void updateItem(List<ServeItemReqDTO.ServeItem> serveItems,
                            OrderDO orderDO) {
        List<OrderItemDO> orderItemDOS = new ArrayList<>();
        List<FreeReturnItemDO> freeReturnItemDOS = new ArrayList<>();
        for (ServeItemReqDTO.ServeItem serveItem : serveItems) {
            //第一期只支持全划，所以只标记状态，之后可能调整为单个划菜
            handleOrderItem(serveItem, orderItemDOS);
            //赠菜默认划菜数量等与赠送数量（全划）
            handleFreeReturnItem(serveItem, freeReturnItemDOS);

        }
        if (!CollectionUtils.isEmpty(freeReturnItemDOS)) {
            freeReturnItemService.updateBatchById(freeReturnItemDOS);
        }
        if (!CollectionUtils.isEmpty(orderItemDOS)) {
            orderItemService.updateBatchByIdWithDeleteCache(orderItemDOS, String.valueOf(orderDO.getGuid()));
        }
    }

    private void handleOrderItem(ServeItemReqDTO.ServeItem serveItem,
                                 List<OrderItemDO> orderItemDOS) {
        if (serveItem.getType() == 1) {
            OrderItemDO orderItemDO = orderItemService.getById(serveItem.getGuid());
            if (orderItemDO.getItemState().equals(ItemStateEnum.SERVED.getCode())) {
                throw new ParameterException("已划商品不能再划");
            }
            orderItemDO.setGuid(Long.valueOf(serveItem.getGuid()));
            orderItemDO.setItemState(ItemStateEnum.SERVED.getCode());
            orderItemDOS.add(orderItemDO);
        }
    }

    private void handleFreeReturnItem(ServeItemReqDTO.ServeItem serveItem,
                                      List<FreeReturnItemDO> freeReturnItemDOS) {
        if (serveItem.getType() == 2) {
            FreeReturnItemDO freeReturnItemDO = new FreeReturnItemDO();
            FreeReturnItemDO byId = freeReturnItemService.getById(serveItem.getGuid());
            if (byId.getItemState().equals(ItemStateEnum.SERVED.getCode())) {
                throw new ParameterException("已划商品不能再划");
            }
            freeReturnItemDO.setGuid(Long.valueOf(serveItem.getGuid()));
            freeReturnItemDO.setServeCount(byId.getCount());
            freeReturnItemDO.setItemState(ItemStateEnum.SERVED.getCode());
            freeReturnItemDOS.add(freeReturnItemDO);
        }
    }

    @Override
    public Boolean batchUrgeItem(UpdateItemStateReqDTO updateItemStateReqDTO) {
        List<UpdateItemStateReqDTO.OrderItem> orderItems = updateItemStateReqDTO.getOrderItems();
        List<String> orderItemGuids = new ArrayList<>();
        List<String> freeOrderItemGuids = new ArrayList<>();
        ItemUrgeReqDTO itemUrgeReqDTO = new ItemUrgeReqDTO();
        List<String> kdsItemGuids = new ArrayList<>();
        itemUrgeReqDTO.setOrderItemGuidList(kdsItemGuids);
        setOrderItemGuid(orderItems, orderItemGuids, freeOrderItemGuids);
        OrderDO orderDO = orderService.getById(updateItemStateReqDTO.getOrderGuid());
        List<DineInItemDTO> urgeDineInItemDTOS = new ArrayList<>();
        updateOrderItem(updateItemStateReqDTO, orderItemGuids, kdsItemGuids, orderDO, urgeDineInItemDTOS);
        updateFreeReturnItem(freeOrderItemGuids, kdsItemGuids, urgeDineInItemDTOS);
        dineInPrintService.printOrderItem(updateItemStateReqDTO, orderDO, urgeDineInItemDTOS, PrintOrderItemDTO
                .ItemInvoiceTypeEnum.REMIND.getType());
        kdsMqService.urge(itemUrgeReqDTO);
        return Boolean.TRUE;

    }

    private void updateFreeReturnItem(List<String> freeOrderItemGuids,
                                      List<String> kdsItemGuids,
                                      List<DineInItemDTO> urgeDineInItemDTOS) {
        if (CollectionUtil.isNotEmpty(freeOrderItemGuids)) {
            List<FreeReturnItemDO> freeReturnItemDOS = new ArrayList<>(freeReturnItemService.listByIds
                    (freeOrderItemGuids));

            Map<Long, List<FreeReturnItemDO>> orderItemGuid = CollectionUtil.toListMap(freeReturnItemDOS,
                    ORDER_ITEM_GUID);
            ArrayList<Long> longs = new ArrayList<>(orderItemGuid.keySet());
            for (Long orderGuid : longs) {
                kdsItemGuids.add(String.valueOf(orderGuid));
            }
            List<OrderItemDO> orderItemDOS = new ArrayList<>(orderItemService.listByIds(orderItemGuid.keySet()));
            kdsAddSubItemGuid(orderItemGuid, orderItemDOS, kdsItemGuids);
            setZeroByCurrentCount(orderItemDOS);
            List<ItemAttrDO> itemAttrDOS = itemAttrService.listByItemGuids(orderItemGuid.keySet());


            for (FreeReturnItemDO freeReturnItemDO : freeReturnItemDOS) {
                freeReturnItemDO.setUrgeNum(freeReturnItemDO.getUrgeNum() + 1);
            }
            freeReturnItemService.updateBatchById(freeReturnItemDOS);
            urgeDineInItemDTOS.addAll(AmountCalculationUtil.buildItem(orderItemDOS, itemAttrDOS,
                    freeReturnItemDOS));

        }
    }

    private void updateOrderItem(UpdateItemStateReqDTO updateItemStateReqDTO,
                                 List<String> orderItemGuids,
                                 List<String> kdsItemGuids,
                                 OrderDO orderDO,
                                 List<DineInItemDTO> urgeDineInItemDTOS) {
        if (CollectionUtil.isNotEmpty(orderItemGuids)) {
            List<OrderItemDO> orderItemDOS = new ArrayList<>(orderItemService.listByIdsWithLock(orderItemGuids));
            // kds商品处理
            kdsAddOrderItemGuid(orderItemDOS, kdsItemGuids);
            // 子菜加入kds
            kdsAddMainItemGuid(orderItemDOS, kdsItemGuids);

            List<ItemAttrDO> itemAttrDOS = itemAttrService.listByItemGuids(orderItemGuids);
            List<FreeReturnItemDO> freeReturnItemDOS = freeReturnItemService.listFreeByOrderGuid(updateItemStateReqDTO
                    .getOrderGuid());
            for (OrderItemDO orderItemDO : orderItemDOS) {
                orderItemDO.setUrgeNum(orderItemDO.getUrgeNum() + 1);
            }
            orderItemService.updateBatchByIdWithDeleteCache(orderItemDOS, String.valueOf(orderDO.getGuid()));
            urgeDineInItemDTOS.addAll(AmountCalculationUtil.buildItem(orderItemDOS, itemAttrDOS,
                    freeReturnItemDOS));
        }
    }

    private void kdsAddSubItemGuid(Map<Long, List<FreeReturnItemDO>> orderItemGuid,
                                   List<OrderItemDO> orderItemDOS,
                                   List<String> kdsItemGuids) {
        List<OrderItemDO> subOrderItemDOS = orderItemService.listByMainItemGuids(orderItemGuid.keySet());
        if (CollectionUtil.isNotEmpty(subOrderItemDOS)) {
            orderItemDOS.addAll(subOrderItemDOS);
            Map<Long, OrderItemDO> subOrderItemGuid = CollectionUtil.toMap(subOrderItemDOS,
                    "guid");
            ArrayList<Long> longArrayList = new ArrayList<>(subOrderItemGuid.keySet());
            for (Long orderGuid : longArrayList) {
                kdsItemGuids.add(String.valueOf(orderGuid));
            }
        }
    }

    private void setZeroByCurrentCount(List<OrderItemDO> orderItemDOS) {
        for (OrderItemDO orderItemDO : orderItemDOS) {
            //子菜品没有赠送数量，退菜数量的概念，用作打印显示
            if (orderItemDO.getParentItemGuid() != null && orderItemDO.getParentItemGuid() > 0) {
                continue;
            }
            //打印时赠送菜品需要取赠送数量，如果CurrentCount大于零打印构建代码会取CurrentCount
            orderItemDO.setCurrentCount(BigDecimal.ZERO);
        }
    }

    private void setOrderItemGuid(List<UpdateItemStateReqDTO.OrderItem> orderItems,
                                  List<String> orderItemGuids,
                                  List<String> freeOrderItemGuids) {
        for (UpdateItemStateReqDTO.OrderItem orderItem : orderItems) {
            if (orderItem.getType() == 1) {
                orderItemGuids.add(orderItem.getGuid());
            }
            if (orderItem.getType() == 2) {
                freeOrderItemGuids.add(orderItem.getGuid());
            }
        }
    }

    /**
     * kds商品处理
     */
    private void kdsAddOrderItemGuid(List<OrderItemDO> orderItemDOS, List<String> kdsItemGuids) {
        for (OrderItemDO orderItem : orderItemDOS) {
            if (Objects.equals(ItemTypeEnum.GROUP.getCode(), orderItem.getItemType())) {
                List<OrderItemDO> subItemList = orderItemService.list(new LambdaQueryWrapper<OrderItemDO>()
                        .eq(OrderItemDO::getGuid, orderItem.getParentItemGuid()));
                List<String> list = subItemList.stream()
                        .map(i -> String.valueOf(i.getGuid()))
                        .collect(Collectors.toList());
                kdsItemGuids.addAll(list);
                continue;
            }
            kdsItemGuids.add(String.valueOf(orderItem.getGuid()));
        }
    }

    /**
     * 子菜加入kds
     */
    private void kdsAddMainItemGuid(List<OrderItemDO> orderItemDOS, List<String> kdsItemGuids) {
        Map<Long, OrderItemDO> orderItemGuid = CollectionUtil.toMap(orderItemDOS, "guid");
        List<OrderItemDO> subOrderItemDOS = orderItemService.listByMainItemGuids(new ArrayList<>(orderItemGuid
                .keySet()));
        if (CollectionUtil.isNotEmpty(subOrderItemDOS)) {
            orderItemDOS.addAll(subOrderItemDOS);
            Map<Long, OrderItemDO> subOrderItemGuid = CollectionUtil.toMap(subOrderItemDOS,
                    "guid");
            ArrayList<Long> longs = new ArrayList<>(subOrderItemGuid.keySet());
            for (Long orderGuid : longs) {
                kdsItemGuids.add(String.valueOf(orderGuid));
            }
        }
    }

    @Override
    public Boolean callUpItem(UpdateItemStateReqDTO updateItemStateReqDTO) {
        List<UpdateItemStateReqDTO.OrderItem> orderItems = updateItemStateReqDTO.getOrderItems();
        List<String> orderItemGuids = new ArrayList<>();
        List<String> freeOrderItemGuids = new ArrayList<>();
        ItemCallUpReqDTO itemCallUpReqDTO = new ItemCallUpReqDTO();
        List<String> kdsItemGuids = new ArrayList<>();
        itemCallUpReqDTO.setOrderItemGuidList(kdsItemGuids);
        if (ObjectUtils.isEmpty(updateItemStateReqDTO.getIsCallAll())) {
            itemCallUpReqDTO.setIsCallAll(Boolean.FALSE);
        } else {
            itemCallUpReqDTO.setIsCallAll(updateItemStateReqDTO.getIsCallAll());
        }
        setOrderItemGuid(orderItems, orderItemGuids, freeOrderItemGuids);
        List<DineInItemDTO> callUpDineInItemDTOS = new ArrayList<>();
        OrderDO orderDO = orderService.getById(updateItemStateReqDTO.getOrderGuid());
        itemCallUpReqDTO.setDiningTableName(orderDO.getDiningTableName());
        if (CollectionUtil.isNotEmpty(orderItemGuids)) {
            List<OrderItemDO> orderItemDOS = new ArrayList<>(orderItemService.listByIdsWithLock(orderItemGuids));
            // kds商品处理
            for (OrderItemDO orderItem : orderItemDOS) {
                //若是套餐商品或者宴会套餐
                if (isParentItem(orderItem.getItemType())) {
                    List<OrderItemDO> subItemList = orderItemService.list(new LambdaQueryWrapper<OrderItemDO>()
                            .eq(OrderItemDO::getParentItemGuid, orderItem.getGuid()));
                    List<String> list = subItemList.stream()
                            .map(i -> String.valueOf(i.getGuid()))
                            .collect(Collectors.toList());
                    kdsItemGuids.addAll(list);
                    continue;
                }
                kdsItemGuids.add(String.valueOf(orderItem.getGuid()));
            }
            Map<Long, OrderItemDO> orderItemGuid = CollectionUtil.toMap(orderItemDOS, "guid");
            List<OrderItemDO> subOrderItemDOS = orderItemService.listByMainItemGuids(new ArrayList<>(orderItemGuid
                    .keySet()));
            if (CollectionUtil.isNotEmpty(subOrderItemDOS)) {
                orderItemDOS.addAll(subOrderItemDOS);
            }
            List<ItemAttrDO> itemAttrDOS = itemAttrService.listByItemGuids(orderItemGuids);
            List<FreeReturnItemDO> freeReturnItemDOS = freeReturnItemService.listFreeByOrderGuid(updateItemStateReqDTO
                    .getOrderGuid());
            for (OrderItemDO orderItemDO : orderItemDOS) {
                if (orderItemDO.getItemState().equals(ItemStateEnum.CALL_UP.getCode())) {
                    throw new ParameterException(NOT_CALL_UP_TOO);
                }
                orderItemDO.setItemState(ItemStateEnum.CALL_UP.getCode());
            }
            orderItemService.updateBatchByIdWithDeleteCache(orderItemDOS, String.valueOf(orderDO.getGuid()));
            callUpDineInItemDTOS.addAll(AmountCalculationUtil.buildItem(orderItemDOS, itemAttrDOS,
                    freeReturnItemDOS));
        }
        if (CollectionUtil.isNotEmpty(freeOrderItemGuids)) {
            List<FreeReturnItemDO> freeReturnItemDOS = new ArrayList<>(freeReturnItemService.listByIds
                    (freeOrderItemGuids));

            Map<Long, List<FreeReturnItemDO>> orderItemGuid = CollectionUtil.toListMap(freeReturnItemDOS,
                    ORDER_ITEM_GUID);
            List<OrderItemDO> orderItemDOS = new ArrayList<>(orderItemService.listByIds(orderItemGuid.keySet()));
            List<OrderItemDO> subOrderItemDOS = orderItemService.listByMainItemGuids(new ArrayList<>(orderItemGuid
                    .keySet()));
            if (CollectionUtil.isNotEmpty(subOrderItemDOS)) {
                orderItemDOS.addAll(subOrderItemDOS);
            }
            for (OrderItemDO orderItemDO : orderItemDOS) {
                orderItemDO.setCurrentCount(BigDecimal.ZERO);
                if (orderItemDO.getItemState().equals(ItemStateEnum.CALL_UP.getCode())) {
                    throw new ParameterException(NOT_CALL_UP_TOO);
                }
                orderItemDO.setItemState(ItemStateEnum.CALL_UP.getCode());
                kdsItemGuids.add(String.valueOf(orderItemDO.getGuid()));
            }
            List<ItemAttrDO> itemAttrDOS = itemAttrService.listByItemGuids(orderItemGuid.keySet());


            for (FreeReturnItemDO freeReturnItemDO : freeReturnItemDOS) {
                if (freeReturnItemDO.getItemState().equals(ItemStateEnum.CALL_UP.getCode())) {
                    throw new ParameterException(NOT_CALL_UP_TOO);
                }
                freeReturnItemDO.setItemState(ItemStateEnum.CALL_UP.getCode());
            }
            freeReturnItemService.updateBatchById(freeReturnItemDOS);
            callUpDineInItemDTOS.addAll(AmountCalculationUtil.buildItem(orderItemDOS, itemAttrDOS,
                    freeReturnItemDOS));
        }
        dineInPrintService.printOrderItem(updateItemStateReqDTO, orderDO, callUpDineInItemDTOS, PrintOrderItemDTO
                .ItemInvoiceTypeEnum.CALL_UP.getType());
        dineInPrintService.printLabelItem(updateItemStateReqDTO, orderDO, callUpDineInItemDTOS);
        kdsMqService.call(itemCallUpReqDTO);
        return Boolean.TRUE;

    }

    private boolean isParentItem(Integer itemType) {
      return Objects.equals(ItemTypeEnum.GROUP.getCode(), itemType) ||  Objects.equals(ItemTypeEnum.TEAMMEAL.getCode(), itemType);
    }


    /**
     * 没改价
     *
     * @param priceChangeItemReqDTO
     * @return
     */
    @Override
    @Transactional
    public Boolean changePrice(PriceChangeItemReqDTO priceChangeItemReqDTO) {
        OrderItemDO orderItemDO = orderTransform.priceChangeItemReqDTO2OrderItemDO(priceChangeItemReqDTO);
        String redisKey = RedisKeyUtil.getHstOrderItemKey(priceChangeItemReqDTO.getOrderGuid());
        boolean update = orderItemService.updateById(orderItemDO);
        if (update) {
            redisHelper.delete(redisKey);
        }
        return update;
    }

    /**
     * 根据pad下单guid查询下单商品
     *
     * @param padOrderGuid pad下单guid
     * @return 商品列表
     */
    @Override
    public List<DineInItemDTO> queryItemByPadGuid(String padOrderGuid) {
        if (ObjectUtils.isEmpty(padOrderGuid)) {
            throw new BusinessException("pad下单guid不能为空");
        }
        List<OrderItemDO> orderItemDOList = this.list(new LambdaQueryWrapper<OrderItemDO>()
                .eq(OrderItemDO::getPadOrderGuid, padOrderGuid)
                .eq(OrderItemDO::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        List<DineInItemDTO> dineInItemDTOList = LocalTransform.INSTANCE.orderItemDO2DineInItemDTO(orderItemDOList);

        // 套餐子商品
        Map<Long, List<OrderItemDO>> subItemMap = orderItemDOList.stream()
                .filter(item -> !Objects.equals(0L, item.getParentItemGuid()))
                .collect(Collectors.groupingBy(OrderItemDO::getParentItemGuid));

        // 删除套餐子商品
        dineInItemDTOList.removeIf(item -> !Objects.equals(0L, item.getParentItemGuid()));
        dineInItemDTOList.forEach(item -> {
            if (Objects.equals(1, item.getItemType())) {

                List<OrderItemDO> itemDTOList = subItemMap.get(Long.valueOf(item.getGuid()));
                if (CollectionUtils.isEmpty(itemDTOList)) {
                    log.info("套餐子商品为空 ItemGuid={}", item.getItemGuid());
                    return;
                }

                Map<String, List<OrderItemDO>> subgroupMap = itemDTOList.stream()
                        .collect(Collectors.groupingBy(OrderItemDO::getSubgroupGuid));

                List<String> subgroupGuidList = itemDTOList.stream()
                        .map(OrderItemDO::getSubgroupGuid)
                        .distinct()
                        .collect(Collectors.toList());

                List<PackageSubgroupDTO> packageSubgroupDTOS = new ArrayList<>();
                subgroupGuidList.forEach(sub -> {
                    PackageSubgroupDTO subgroupDTO = new PackageSubgroupDTO();
                    subgroupDTO.setSubgroupGuid(sub);
                    List<OrderItemDO> itemDOList = subgroupMap.get(sub);
                    subgroupDTO.setSubgroupName(itemDOList.get(0).getSubgroupName());
                    List<SubDineInItemDTO> subDineInItemDTOList =
                            LocalTransform.INSTANCE.orderItemDOList2subDineInItemDTOList(itemDOList);
                    subgroupDTO.setSubDineInItemDTOS(subDineInItemDTOList);
                    packageSubgroupDTOS.add(subgroupDTO);
                });
                item.setPackageSubgroupDTOS(packageSubgroupDTOS);
            }
        });

        return dineInItemDTOList;
    }

    /**
     * 根据订单guid查询订单商品
     *
     * @param orderGuid 订单guid
     * @return 定单商品
     */
    @Override
    public List<DineInItemDTO> queryItemByOrderGuid(String orderGuid) {
        if (StringUtils.isEmpty(orderGuid)) {
            throw new BusinessException("订单guid为空");
        }
        List<OrderItemDO> orderItemDOList = this.list(new LambdaQueryWrapper<OrderItemDO>()
                .eq(OrderItemDO::getOrderGuid, orderGuid)
                .eq(OrderItemDO::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        return LocalTransform.INSTANCE.orderItemDO2DineInItemDTO(orderItemDOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EstimateItemRespDTO changeGroupItem(ChangeGroupItemReqDTO changeGroupItemReqDTO) {
        OrderPackageSubgroupBO packageSubgroupBiz = buildPackageSubgroupChangeBiz(changeGroupItemReqDTO);
        log.info("套餐换菜处理,packageSubgroupBiz:{}", JacksonUtils.writeValueAsString(packageSubgroupBiz));
        // 商品估清
        EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(true);
        // 估清校验
        estimateItemRespDTO = changeDineInItemEstimate(packageSubgroupBiz.getChangeSubDineInItemList(), packageSubgroupBiz.getPkgOrderItemDO());
        if (Boolean.FALSE.equals(estimateItemRespDTO.getResult())) {
            return estimateItemRespDTO;
        }
        // 套餐子项换菜处理
        packageSubgroupChangesService.changes(packageSubgroupBiz);
        // 换菜商品记录
        packageSubgroupChangesService.addChangeOrderItemRecordDOList(packageSubgroupBiz.getOrderDO(),
                packageSubgroupBiz.getOrderItemChangesList(), packageSubgroupBiz.getItemRecordSave());
        // 套餐换菜持久化处理
        persistenceOrderChangeItem(packageSubgroupBiz);
        // 套餐子菜换菜 后置处理
        changeGroupItemAfter(changeGroupItemReqDTO, packageSubgroupBiz, false);
        return estimateItemRespDTO;
    }


    /**
     * 校验是否已换菜
     */
    private void verifyOrderItemChanged(String orderItemGuid) {
        OrderItemExtendsDO orderItemExtendsDO = orderItemExtendsService.getById(orderItemGuid);
        if (Objects.nonNull(orderItemExtendsDO) && Objects.equals(orderItemExtendsDO.getChangeFlag(), BooleanEnum.TRUE.getCode())) {
            throw new BusinessException("请先撤销再换菜");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public EstimateItemRespDTO cancelChangeGroupItem(ChangeGroupItemReqDTO changeGroupItemReqDTO) {
        // 构建撤销换菜 biz
        OrderPackageSubgroupBO packageSubgroupBiz = buildCancelPackageSubgroupChangeBiz(changeGroupItemReqDTO);
        log.info("套餐换菜撤销处理,packageSubgroupBiz:{}", JacksonUtils.writeValueAsString(packageSubgroupBiz));
        // 商品估清
        EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(true);
        // 估清校验
        estimateItemRespDTO = changeDineInItemEstimate(packageSubgroupBiz.getChangeSubDineInItemList(), packageSubgroupBiz.getPkgOrderItemDO());
        if (Boolean.FALSE.equals(estimateItemRespDTO.getResult())) {
            return estimateItemRespDTO;
        }
        // 套餐子项换菜处理
        packageSubgroupChangesService.cancel(packageSubgroupBiz);
        // 套餐换菜持久化处理
        persistenceOrderChangeItem(packageSubgroupBiz);
        // 套餐子菜换菜 后置处理
        changeGroupItemAfter(changeGroupItemReqDTO, packageSubgroupBiz, true);
        return estimateItemRespDTO;
    }

    @Override
    public List<DineInItemDTO> queryItemByGuid(SingleListDTO req) {
        if (CollectionUtils.isEmpty(req.getList())) {
            throw new BusinessException("订单商品guid列表为空");
        }
        List<OrderItemDO> orderItemDOList = this.list(new LambdaQueryWrapper<OrderItemDO>()
                .in(OrderItemDO::getGuid, req.getList())
                .eq(OrderItemDO::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        return LocalTransform.INSTANCE.orderItemDO2DineInItemDTO(orderItemDOList);
    }

    /**
     * 转菜
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transferItem(TransferItemReqDTO transferReq) {
        // 查询原单
        OrderDO oldOrderDO = orderService.getById(transferReq.getOldOrderGuid());

        // 查询新单
        OrderDO newOrderDO = orderService.getById(transferReq.getNewOrderGuid());
        List<OrderItemDO> oldOrderItemDOList = orderItemService.listAllByGuid(transferReq.getOldOrderItemGuids());
        Set<String> oldOrderItemGuids = oldOrderItemDOList.stream()
                .map(OrderItemDO::getGuid)
                .map(String::valueOf)
                .collect(Collectors.toSet());
        List<OrderItemExtendsDO> oldItemExtendsDOList = orderItemExtendsService.listAllByGuid(oldOrderItemGuids);
        List<OrderItemChangesDO> orderItemChangesList = orderItemChangesService.listByOrderItemGuidList(oldOrderItemGuids);

        // 转菜校验
        BillVerifyHelper.verifyTransferItem(transferReq, oldOrderDO, newOrderDO, oldOrderItemDOList);
        List<ItemAttrDO> oldItemAttrDOList = itemAttrService.findByOrderGuid(transferReq.getOldOrderGuid());
        List<OrderItemRecordDO> oldItemRecordDOList = orderItemRecordService.listByItemGuids(transferReq.getOldOrderItemGuids());

        // 构建转菜
        TransferItemBO transferItemBO = TransferItemBizBuilder.buildTransferItemBiz(transferReq, newOrderDO, oldOrderItemDOList,
                oldItemAttrDOList, oldItemRecordDOList, oldItemExtendsDOList, orderItemChangesList);
        log.info("构建transferItemBO完成：{}", JacksonUtils.writeValueAsString(transferItemBO));
        List<OrderItemDO> newOrderItemDOList = transferItemBO.getNewOrderItemDOList();
        List<ItemAttrDO> newItemAttrDOList = transferItemBO.getNewItemAttrDOList();
        List<OrderItemRecordDO> newItemRecordDOList = transferItemBO.getNewItemRecordDOList();
        List<OrderItemExtendsDO> newItemExtendsDOList = transferItemBO.getNewItemExtendsDOList();
        List<OrderItemChangesDO> updateItemChangesList = transferItemBO.getUpdateItemChangesList();
        BigDecimal orderItemFee = TransferItemBizBuilder.calculateOrderItemFee(newItemAttrDOList, newOrderItemDOList);
        newOrderDO.setOrderFee(newOrderDO.getOrderFee().add(orderItemFee));
        TransferItemBizBuilder.setOrderFeeForCombine(newOrderDO, orderItemFee);
        // 新订单
        orderItemService.saveBatch(newOrderItemDOList);
        orderService.updateById(newOrderDO);
        itemAttrService.saveBatch(newItemAttrDOList);
        orderItemRecordService.saveBatch(newItemRecordDOList);
        if (!CollectionUtils.isEmpty(newItemExtendsDOList)) {
            orderItemExtendsService.saveBatch(newItemExtendsDOList);
        }
        if (!CollectionUtils.isEmpty(updateItemChangesList)) {
            orderItemChangesService.saveBatch(updateItemChangesList);
        }

        transferItemBO.setOldOrderDO(oldOrderDO);
        List<OrderItemDO> updateOrderItemDOList = TransferItemBizBuilder.handleOldOrderData(oldOrderItemDOList, transferItemBO);
        // 原订单
        orderItemService.updateBatchById(updateOrderItemDOList);
        OrderDO orderDO = new OrderDO();
        orderDO.setGuid(oldOrderDO.getGuid());
        orderDO.setOrderFee(oldOrderDO.getOrderFee().subtract(orderItemFee));
        orderDO.setOrderFeeForCombine(oldOrderDO.getOrderFeeForCombine().subtract(orderItemFee));
        orderService.updateById(orderDO);
        List<String> deleteItemGuidList = oldOrderItemDOList.stream()
                .filter(item -> Boolean.TRUE.equals(item.getIsDelete()))
                .map(OrderItemDO::getGuid)
                .map(String::valueOf)
                .distinct()
                .collect(Collectors.toList());
        orderItemService.removeByIdsWithDeleteCache(deleteItemGuidList, String.valueOf(oldOrderDO.getGuid()));
        orderItemRecordService.deleteByItemGuids(deleteItemGuidList);

        // 异步处理
        transferItemAsyn(transferReq, newOrderDO, oldOrderDO, transferItemBO, orderDO);
    }

    /**
     * 转菜异步处理
     */
    private void transferItemAsyn(TransferItemReqDTO transferReq,
                                  OrderDO newOrderDO,
                                  OrderDO oldOrderDO,
                                  TransferItemBO transferItemBO,
                                  OrderDO orderDO) {
        List<OrderItemDO> newOrderItemDOList = transferItemBO.getNewOrderItemDOList();
        List<ItemAttrDO> newItemAttrDOList = transferItemBO.getNewItemAttrDOList();
        UserContext userContext = UserContextUtils.get();
        checkOutThreadPool.execute(() -> {
            UserContextUtils.put(userContext);
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());

            DineinOrderDetailRespDTO newOrderDetailRespDTO = OrderTransform.INSTANCE.orderDO2DineinOrderDetailRespDTO(newOrderDO);
            newOrderDetailRespDTO.setOperatorName(userContext.getUserName());
            LocalDateTime transferTime = LocalDateTime.now();
            newOrderDetailRespDTO.setTransferTime(transferTime);
            DineinOrderDetailRespDTO oldOrderDetailRespDTO = OrderTransform.INSTANCE.orderDO2DineinOrderDetailRespDTO(oldOrderDO);
            // kds转菜
            try {
                kdsService.transferItem(transferReq, newOrderDetailRespDTO, transferItemBO.getOld2NewMap());
            } catch (Exception e) {
                log.error("通知kds失败,e:", e);
            }
            // 打印转菜单
            List<DineInItemDTO> newDineInItemList = AmountCalculationUtil.buildItem(newOrderItemDOList,
                    newItemAttrDOList, Lists.newArrayList());
            newOrderDetailRespDTO.setDineInItemDTOS(newDineInItemList);
            dineInPrintService.printTransferItem(transferReq, newOrderDetailRespDTO, oldOrderDetailRespDTO);
            // 更新新桌台状态
            TableStatusChangeDTO tableStatusChangeDTO = orderTransform.transferReq2TableStatusChangeDTO(transferReq);
            tableStatusChangeDTO.setOrderGuid(String.valueOf(newOrderDO.getGuid()));
            tableStatusChangeDTO.setTableGuid(newOrderDO.getDiningTableGuid());
            tableStatusChangeDTO.setTableStatusChange(TableStatusChangeEnum.ORDER_DISH_CHANGE.getId());
            tableClientService.tableStatusChange(tableStatusChangeDTO);

            // 更新旧桌台状态
            tableStatusChangeDTO.setOrderGuid(String.valueOf(oldOrderDO.getGuid()));
            tableStatusChangeDTO.setTableGuid(oldOrderDO.getDiningTableGuid());
            if (orderItemService.hasCurrentItem(oldOrderDO.getGuid())) {
                tableStatusChangeDTO.setTableStatusChange(TableStatusChangeEnum.ORDER_DISH_CHANGE.getId());
            } else {
                tableStatusChangeDTO.setTableStatusChange(TableStatusChangeEnum.ORDER_DISH_REFUND_ALL.getId());
            }
            tableClientService.tableStatusChange(tableStatusChangeDTO);

            // 将加菜后的订单金额存入缓存
            redisHelper.setEx(OrderRedisConstant.REDIS_ORDER_FEE + newOrderDO.getGuid(),
                    String.valueOf(newOrderDO.getOrderFee()), 30, TimeUnit.SECONDS);
            redisHelper.setEx(OrderRedisConstant.REDIS_ORDER_FEE + orderDO.getGuid(),
                    String.valueOf(orderDO.getOrderFee()), 30, TimeUnit.SECONDS);

            // 桌台刷新推送
            messagePushHelper.transferItemMsgPush(transferItemBO);
            EnterpriseIdentifier.remove();
        });
    }

    /**
     * 套餐换菜持久化处理
     */
    private void persistenceOrderChangeItem(OrderPackageSubgroupBO packageSubgroupBiz) {
        log.info("套餐换菜持久化处理:{}", JacksonUtils.writeValueAsString(packageSubgroupBiz));
        // 保存换菜明细
        savePackageSubgroupChangeItemList(packageSubgroupBiz.getOrderItemChangesList());
        List<ItemAttrDO> itemAttrList = packageSubgroupBiz.getItemAttrDOS();
        if (CollectionUtil.isNotEmpty(itemAttrList)) {
            List<Long> orderItemGuidList = itemAttrList.stream()
                    .map(ItemAttrDO::getOrderItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            itemAttrService.removeByItemGuids(orderItemGuidList);
            itemAttrService.saveOrUpdateBatch(itemAttrList);
        }
        List<OrderItemExtendsDO> orderItemExtendsList = packageSubgroupBiz.getOrderItemExtendsDOS();
        if (CollectionUtil.isNotEmpty(orderItemExtendsList)) {
            orderItemExtendsService.saveOrUpdateBatch(orderItemExtendsList);
        }
        List<OrderItemRecordDO> itemRecordList = packageSubgroupBiz.getItemRecordSave();
        if (CollectionUtil.isNotEmpty(itemRecordList)) {
            orderItemRecordService.saveOrUpdateBatch(itemRecordList);
        }
        List<OrderItemDO> removeOrderItemList = packageSubgroupBiz.getRemoveOrderItemDOS();
        if (CollectionUtil.isNotEmpty(removeOrderItemList)) {
            List<Long> removeOrderItemGuidList = removeOrderItemList.stream().map(OrderItemDO::getGuid).distinct().collect(Collectors.toList());
            orderItemService.removeByIdsWithDeleteCache(removeOrderItemGuidList, String.valueOf(packageSubgroupBiz.getOrderDO().getGuid()));
            orderItemExtendsService.removeByIds(removeOrderItemGuidList);
            packageSubgroupBiz.getOrderItemDOS().removeIf(e -> removeOrderItemGuidList.contains(e.getGuid()));
        }
        List<OrderItemDO> restoreOrderItemList = packageSubgroupBiz.getRestoreOrderItemDOS();
        if (CollectionUtil.isNotEmpty(restoreOrderItemList)) {
            List<Long> restoreOrderItemGuidList = restoreOrderItemList.stream().map(OrderItemDO::getGuid).distinct().collect(Collectors.toList());
            orderItemService.restoreOrderItem(restoreOrderItemList);
            orderItemExtendsService.restoreOrderItemExtends(restoreOrderItemGuidList);
            packageSubgroupBiz.getOrderItemDOS().removeIf(e -> restoreOrderItemGuidList.contains(e.getGuid()));
        }
        List<OrderItemDO> orderItemList = packageSubgroupBiz.getOrderItemDOS();
        if (CollectionUtil.isNotEmpty(orderItemList)) {
            // 直接替换订单商品明细
            orderItemService.saveOrUpdateBatch(orderItemList);
        }
        // 退估清
        returnOriginalDineInItemEstimate(packageSubgroupBiz.getSubDineInItemList(), packageSubgroupBiz.getPkgOrderItemDO());
    }

    /**
     * 构建换菜 biz
     */
    private OrderPackageSubgroupBO buildPackageSubgroupChangeBiz(ChangeGroupItemReqDTO changeGroupItemReqDTO) {
        // 新菜
        List<SubDineInItemDTO> subDineInItemList = changeGroupItemReqDTO.getSubDineInItemList();
        if (CollectionUtils.isEmpty(subDineInItemList)) {
            throw new BusinessException("套餐换菜信息为空");
        }
        SubDineInItemDTO originalItem = subDineInItemList.stream()
                .map(SubDineInItemDTO::getOriginalItem)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
        if (Objects.isNull(originalItem)) {
            throw new BusinessException("套餐换菜原菜信息为空");
        }
        List<SubDineInItemDTO> originalItemList = Lists.newArrayList(originalItem);
        // 查询order, orderItemExtend
        OrderDO orderDO = orderService.getById(changeGroupItemReqDTO.getOrderGuid());
        OrderItemDO orderItemDO = orderItemService.getById(changeGroupItemReqDTO.getOrderItemGuid());
        if (Objects.isNull(orderDO) || Objects.isNull(orderItemDO)) {
            throw new BusinessException("订单信息有误");
        }
        // 校验是否已换菜
        verifyOrderItemChanged(changeGroupItemReqDTO.getOrderItemGuid());
        // 填充换菜批次号
        addOrderItemChangeBatchNumber(changeGroupItemReqDTO);
        // 查询套餐信息
        OrderItemDO pkgOrderItemDO = orderItemService.getById(orderItemDO.getParentItemGuid());
        // 套餐子项明细 增加套餐上的商品状态
        subDineInItemDTOAddItemState(pkgOrderItemDO, originalItemList, subDineInItemList);
        // 查询菜品
        List<String> queryItemGuidList = Lists.newArrayList(orderItemDO.getItemGuid());
        // 换菜后itemGuidList
        List<String> changeItemGuidList = subDineInItemList.stream()
                .map(SubDineInItemDTO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(changeItemGuidList)) {
            queryItemGuidList.addAll(changeItemGuidList);
        }
        Map<String, ItemInfoRespDTO> itemInfoRespMap =
                queryItemInfoByStoreGuidAndItemGuids(changeGroupItemReqDTO.getStoreGuid(), queryItemGuidList);
        OrderPackageSubgroupBO packageSubgroupBiz = OrderPackageSubgroupBizBuilder.build();
        packageSubgroupBiz.setOrderDO(orderDO);
        packageSubgroupBiz.setOrderItemDOS(Lists.newArrayList(orderItemDO));
        packageSubgroupBiz.setPkgOrderItemDO(pkgOrderItemDO);
        packageSubgroupBiz.setSubDineInItemList(originalItemList);
        packageSubgroupBiz.setChangeSubDineInItemList(subDineInItemList);
        packageSubgroupBiz.setItemInfoRespDTOMap(itemInfoRespMap);
        packageSubgroupBiz.setAddItemFlag(false);
        OrderItemExtendsDO orderItemExtendsDO = new OrderItemExtendsDO();
        BeanUtils.copyProperties(orderItemDO, orderItemExtendsDO);
        packageSubgroupBiz.setOrderItemExtendsDOS(Lists.newArrayList(orderItemExtendsDO));
        // 是否需要删除原商品明细
        if (originalItem.getCurrentCount().compareTo(orderItemDO.getCurrentCount()) >= 0) {
            packageSubgroupBiz.setRemoveOrderItemDOS(Lists.newArrayList(orderItemDO));
        }
        packageSubgroupBiz.setItemRecordSave(Lists.newArrayList());
        // 换菜后菜品处理
        for (SubDineInItemDTO subDineInItemDTO : subDineInItemList) {
            addOrderItemForChange(packageSubgroupBiz, orderItemDO, subDineInItemDTO, null);
        }
        return packageSubgroupBiz;
    }


    /**
     * 填充换菜批次号
     */
    private void addOrderItemChangeBatchNumber(ChangeGroupItemReqDTO changeGroupItemReqDTO) {
        List<SubDineInItemDTO> subDineInItemList = changeGroupItemReqDTO.getSubDineInItemList();
        String changeBatchNumber = String.valueOf(System.currentTimeMillis());
        for (SubDineInItemDTO subDineInItemDTO : subDineInItemList) {
            subDineInItemDTO.setChangeBatchNumber(changeBatchNumber);
            SubDineInItemDTO originalItem = subDineInItemDTO.getOriginalItem();
            if (Objects.isNull(originalItem)) {
                continue;
            }
            originalItem.setChangeBatchNumber(changeBatchNumber);
        }
    }


    /**
     * 换菜后菜品处理
     */
    private void addOrderItemForChange(OrderPackageSubgroupBO packageSubgroupBiz, OrderItemDO originalOrderItemDO,
                                       SubDineInItemDTO subDineInItemDTO, Long subOrderItemGuid) {
        ItemInfoRespDTO subItemInfoRespDTO = packageSubgroupBiz.getItemInfoRespDTOMap().get(subDineInItemDTO.getItemGuid());
        SkuInfoRespDTO subSkuInfoRespDTO = getSkuInfoRespDTO(subItemInfoRespDTO, subDineInItemDTO.getSkuGuid());
        // 换菜后商品明细
        OrderItemDO subOrderItemDO = OrderTransform.INSTANCE.subDineInItemDTO2OrderItemDO(subDineInItemDTO);
        if (Objects.isNull(subOrderItemGuid)) {
            subOrderItemGuid = dynamicHelper.generateGuid(HST_ORDER_ITEM);
        }
        subOrderItemDO.setGuid(subOrderItemGuid);
        subDineInItemDTO.setGuid(String.valueOf(subOrderItemGuid));
        subOrderItemDO.setOriginalOrderItemGuid(originalOrderItemDO.getOriginalOrderItemGuid());
        subOrderItemDO.setOrderGuid(originalOrderItemDO.getOrderGuid());
        subOrderItemDO.setParentItemGuid(originalOrderItemDO.getParentItemGuid());
        subOrderItemDO.setSubgroupGuid(originalOrderItemDO.getSubgroupGuid());
        subOrderItemDO.setSubgroupName(originalOrderItemDO.getSubgroupName());
        subOrderItemDO.setGmtCreate(LocalDateTime.now());
        subOrderItemDO.setGmtModified(subOrderItemDO.getGmtCreate());
        subOrderItemDO.setCurrentCount(subDineInItemDTO.getCurrentCount());
        subOrderItemDO.setFreeCount(BigDecimal.ZERO);
        subOrderItemDO.setReturnCount(BigDecimal.ZERO);
        subOrderItemDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
        subOrderItemDO.setCreateStaffName(UserContextUtils.getUserName());
        if (BigDecimalUtil.greaterThanZero(subDineInItemDTO.getAddPrice())) {
            subOrderItemDO.setAddPrice(subDineInItemDTO.getAddPrice());
        }
        OrderItemExtendsDO orderItemExtendsDO = new OrderItemExtendsDO();
        BeanUtils.copyProperties(subOrderItemDO, orderItemExtendsDO);
        if (!ObjectUtils.isEmpty(subSkuInfoRespDTO)) {
            subOrderItemDO.setAccountingPrice(subSkuInfoRespDTO.getAccountingPrice());
            appendCostPrice(orderItemExtendsDO, subSkuInfoRespDTO.getCostPrice());
        }
        // 商品属性
        List<ItemAttrDO> itemAttrDOList = buildItemAttrList(originalOrderItemDO.getOrderGuid(), subDineInItemDTO);
        if (!CollectionUtils.isEmpty(itemAttrDOList)) {
            subOrderItemDO.setHasAttr(1);
            packageSubgroupBiz.getItemAttrDOS().addAll(itemAttrDOList);
        }
        packageSubgroupBiz.getOrderItemDOS().add(subOrderItemDO);
        packageSubgroupBiz.getOrderItemExtendsDOS().add(orderItemExtendsDO);
    }

    /**
     * 构建商品属性
     */
    private List<ItemAttrDO> buildItemAttrList(Long orderGuid, SubDineInItemDTO subDineInItemDTO) {
        List<ItemAttrDO> itemAttrList = Lists.newArrayList();
        List<ItemAttrDTO> itemAttrDTOList = subDineInItemDTO.getItemAttrDTOS();
        if (CollectionUtils.isEmpty(itemAttrDTOList)) {
            return itemAttrList;
        }
        List<Long> itemAttrGuids = dynamicHelper.generateGuids(ITEM_ATTR_GUID, itemAttrDTOList.size());
        for (ItemAttrDTO itemAttrDTO : itemAttrDTOList) {
            ItemAttrDO itemAttrDO = OrderTransform.INSTANCE.itemAttrDTO2ItemAttrDO(itemAttrDTO);
            itemAttrDO.setOrderItemGuid(Long.valueOf(subDineInItemDTO.getGuid()));
            itemAttrDO.setGmtCreate(LocalDateTime.now());
            itemAttrDO.setGuid(itemAttrGuids.remove(0));
            itemAttrDO.setOrderGuid(orderGuid);
            itemAttrDO.setStoreGuid(UserContextUtils.getStoreGuid());
            itemAttrDO.setStoreName(UserContextUtils.getStoreName());
            itemAttrList.add(itemAttrDO);
        }
        return itemAttrList;
    }

    /**
     * 构建撤销换菜 biz
     */
    private OrderPackageSubgroupBO buildCancelPackageSubgroupChangeBiz(ChangeGroupItemReqDTO changeGroupItemReqDTO) {
        // 查询order, orderItem
        OrderDO orderDO = orderService.getById(changeGroupItemReqDTO.getOrderGuid());
        if (Objects.isNull(orderDO)) {
            throw new BusinessException("订单信息有误");
        }
        // 查询换菜信息
        List<OrderItemChangesDO> originalItemByBatchNumberList = orderItemChangesService.getChangeItemByBatchNumbers(orderDO.getGuid(),
                Lists.newArrayList(changeGroupItemReqDTO.getChangeBatchNumber()));
        if (CollectionUtils.isEmpty(originalItemByBatchNumberList)) {
            throw new BusinessException("换菜信息不存在，请联系管理员");
        }
        List<Long> orderItemGuidList = originalItemByBatchNumberList.stream()
                .map(OrderItemChangesDO::getOrderItemGuid)
                .distinct()
                .collect(Collectors.toList());
        // 查询商品明细 orderItem
        List<OrderItemDO> orderItemDOList = orderItemService.listByIds(orderItemGuidList);
        List<OrderItemExtendsDO> orderItemExtendsDOList = orderItemDOList.stream().map(e -> {
            OrderItemExtendsDO orderItemExtendsDO = new OrderItemExtendsDO();
            BeanUtils.copyProperties(e, orderItemExtendsDO);
            return orderItemExtendsDO;
        }).collect(Collectors.toList());
        // 原菜 -> 新菜
        OrderItemChangesDO originalChangeItem = originalItemByBatchNumberList.stream()
                .filter(e -> Objects.isNull(e.getOriginalOrderItemGuid()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(originalChangeItem)) {
            throw new BusinessException("换菜异常，请联系管理员");
        }
        SubDineInItemDTO originalSubDineInItemDTO = OrderTransform.INSTANCE.orderItemChangesDO2SubDineInItemDTO(originalChangeItem);
        // 查询关联属性
        subDineInItemDTOAddItemAttrDTO(originalChangeItem.getOrderItemGuid(), originalSubDineInItemDTO);
        List<SubDineInItemDTO> subDineInItemList = Lists.newArrayList(originalSubDineInItemDTO);
        // 新菜 -> 原菜
        List<OrderItemChangesDO> changeItemList = originalItemByBatchNumberList.stream()
                .filter(e -> Objects.nonNull(e.getOriginalOrderItemGuid()))
                .collect(Collectors.toList());
        List<SubDineInItemDTO> originalItemList = changeItemList.stream().map(e -> {
            SubDineInItemDTO changeSubDineInItemDTO = OrderTransform.INSTANCE.orderItemChangesDO2SubDineInItemDTO(e);
            // 查询关联属性
            subDineInItemDTOAddItemAttrDTO(e.getOrderItemGuid(), changeSubDineInItemDTO);
            return changeSubDineInItemDTO;
        }).collect(Collectors.toList());
        // 增加批次号
        subDineInItemDTOAddChangeBatchNumber(subDineInItemList, originalItemList);
        // 设置到对应的新菜上
        subDineInItemList.forEach(e -> e.setOriginalItem(originalItemList.get(0)));
        // 查询套餐信息
        OrderItemDO pkgOrderItemDO = orderItemService.getById(orderItemDOList.get(0).getParentItemGuid());
        // 套餐子项明细 增加套餐上的商品状态
        subDineInItemDTOAddItemState(pkgOrderItemDO, originalItemList, subDineInItemList);
        OrderPackageSubgroupBO packageSubgroupBiz = OrderPackageSubgroupBizBuilder.build();
        packageSubgroupBiz.setOrderDO(orderDO);
        packageSubgroupBiz.setOrderItemDOS(orderItemDOList);
        packageSubgroupBiz.setPkgOrderItemDO(pkgOrderItemDO);
        // 换前
        packageSubgroupBiz.setSubDineInItemList(originalItemList);
        // 换后
        packageSubgroupBiz.setChangeSubDineInItemList(subDineInItemList);
        packageSubgroupBiz.setAddItemFlag(false);
        packageSubgroupBiz.setOrderItemExtendsDOS(orderItemExtendsDOList);
        packageSubgroupBiz.setItemRecordSave(Lists.newArrayList());
        // 需要删除原商品明细
        List<OrderItemDO> originalOrderItemList = buildRemoveOrderItemDOList(originalItemList, orderItemDOList);
        packageSubgroupBiz.setRemoveOrderItemDOS(originalOrderItemList);
        // 换菜后菜品处理
        // 如果原菜品还存在，则修改原菜品数量，否则新增明细
        OrderItemDO originalOrderItemDO = orderItemDOList.stream()
                .filter(e -> e.getGuid().equals(originalChangeItem.getOrderItemGuid()))
                .findFirst()
                .orElse(null);
        packageSubgroupBiz.setAddOrderItemFlag(false);
        if (Objects.isNull(originalOrderItemDO)) {
            packageSubgroupBiz.setAddOrderItemFlag(true);
            // 撤销套餐换菜 构建原菜品明细
            cancelChangeForAddOrderItemDO(packageSubgroupBiz, originalChangeItem);
        }
        return packageSubgroupBiz;
    }

    /**
     * 撤销套餐换菜 构建原菜品明细
     * 1. 原菜品明细存在，则直接修改删除状态或者更新数量
     * 2. 原菜品明细不存在，则需要构建新的菜品明细
     */
    private void cancelChangeForAddOrderItemDO(OrderPackageSubgroupBO packageSubgroupBiz, OrderItemChangesDO originalChangeItem) {
        // 查询被删除的原商品明细
        OrderItemDO orderItemDel = orderItemService.getByGuidContainsDel(originalChangeItem.getOrderItemGuid());
        if (Objects.isNull(orderItemDel)) {
            List<OrderItemDO> orderItemDOList = packageSubgroupBiz.getOrderItemDOS();
            List<SubDineInItemDTO> changeSubDineInItemList = packageSubgroupBiz.getChangeSubDineInItemList();
            addOrderItemForChange(packageSubgroupBiz, orderItemDOList.get(0), changeSubDineInItemList.get(0), originalChangeItem.getOrderItemGuid());
        } else {
            // 更新数量
            orderItemDel.setCurrentCount(originalChangeItem.getCurrentCount());
            orderItemDel.setIsDelete(false);
            packageSubgroupBiz.getOrderItemDOS().add(orderItemDel);
            packageSubgroupBiz.getRestoreOrderItemDOS().add(orderItemDel);
        }
    }

    /**
     * 查询商品关联属性
     */
    private void subDineInItemDTOAddItemAttrDTO(Long originalOrderItemGuid, SubDineInItemDTO originalSubDineInItemDTO) {
        List<ItemAttrDO> itemAttrDOList = itemAttrService.listByItemGuids(Lists.newArrayList(originalOrderItemGuid));
        List<ItemAttrDTO> itemAttrDTOList = orderTransform.itemAttrDOS2itemAttrDTOS(itemAttrDOList);
        originalSubDineInItemDTO.setItemAttrDTOS(itemAttrDTOList);
    }

    /**
     * 增加批次号
     */
    private void subDineInItemDTOAddChangeBatchNumber(List<SubDineInItemDTO> subDineInItemList, List<SubDineInItemDTO> originalItemList) {
        String changeBatchNumber = String.valueOf(System.currentTimeMillis());
        subDineInItemList.forEach(e ->
                // 批次号更新
                e.setChangeBatchNumber(changeBatchNumber)
        );
        originalItemList.forEach(e ->
                // 批次号更新
                e.setChangeBatchNumber(changeBatchNumber)
        );
    }

    /**
     * 构建需要删除的订单商品明细
     */
    private List<OrderItemDO> buildRemoveOrderItemDOList(List<SubDineInItemDTO> originalItemList, List<OrderItemDO> orderItemDOList) {
        // 需要删除原商品明细
        List<String> originalItemGuidList = originalItemList.stream()
                .map(SubDineInItemDTO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        return orderItemDOList.stream()
                .filter(e -> originalItemGuidList.contains(String.valueOf(e.getGuid())))
                .collect(Collectors.toList());
    }


    /**
     * 套餐子项明细 增加套餐上的商品状态
     */
    private void subDineInItemDTOAddItemState(OrderItemDO pkgOrderItemDO, List<SubDineInItemDTO> originalItemList,
                                              List<SubDineInItemDTO> subDineInItemList) {
        originalItemList.forEach(e -> e.setItemState(pkgOrderItemDO.getItemState()));
        subDineInItemList.forEach(e -> e.setItemState(pkgOrderItemDO.getItemState()));
    }

    /**
     * 换菜商品估清校验
     */
    private EstimateItemRespDTO changeDineInItemEstimate(List<SubDineInItemDTO> subDineInItemList, OrderItemDO pkgOrderItemDO) {
        EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        estimateItemRespDTO.setResult(true);
        List<DineInItemDTO> dineInItemList = subDineInItemList.stream().map(e -> {
            DineInItemDTO changeDineInItemDTO = OrderTransform.INSTANCE.subDineInItemDTO2DineInItemDTO(e);
            // 商品数量 = 套餐数量 * 套餐子项数量 * 套餐内商品默认数量
            BigDecimal totalCount = pkgOrderItemDO.getCurrentCount().add(pkgOrderItemDO.getFreeCount());
            BigDecimal itemStock = totalCount.multiply(e.getCurrentCount()).multiply(e.getPackageDefaultCount());
            changeDineInItemDTO.setCurrentCount(itemStock);
            return changeDineInItemDTO;
        }).collect(Collectors.toList());
        log.info("套餐换菜/撤销换菜估清入参:{}", JacksonUtils.writeValueAsString(dineInItemList));
        EstimateResultRespDTO estimateResult = itemClientService.estimate(dineInItemList);
        if (Boolean.FALSE.equals(estimateResult.getSuccess())) {
            log.error("正餐估清校验失败, estimateResult:{}", JacksonUtils.writeValueAsString(estimateResult));
            estimateItemRespDTO.setResult(Boolean.FALSE);
            estimateItemRespDTO.setEstimate(Boolean.TRUE);
            estimateItemRespDTO.setEstimateInfo(estimateResult.getErrorMsg());
            estimateItemRespDTO.setEstimateSkuGuids(estimateResult.getSkuGuids());
            return estimateItemRespDTO;
        }
        return estimateItemRespDTO;
    }

    /**
     * 原商品退估清
     */
    private void returnOriginalDineInItemEstimate(List<SubDineInItemDTO> subDineInItemList, OrderItemDO pkgOrderItemDO) {
        if (CollectionUtils.isEmpty(subDineInItemList)) {
            return;
        }
        List<DineInItemDTO> dineInItemDTOList = subDineInItemList.stream().map(e -> {
            DineInItemDTO dineInItemDTO = OrderTransform.INSTANCE.subDineInItemDTO2DineInItemDTO(e);
            dineInItemDTO.setGmtCreate(LocalDateTime.now());
            // 商品数量 = 套餐数量 * 套餐子项数量 * 套餐内商品默认数量
            BigDecimal itemStock = pkgOrderItemDO.getCurrentCount()
                    .multiply(e.getCurrentCount())
                    .multiply(e.getPackageDefaultCount());
            dineInItemDTO.setCurrentCount(itemStock);
            return dineInItemDTO;
        }).collect(Collectors.toList());
        log.info("套餐换菜,商品撤销估清入参:{}", JacksonUtils.writeValueAsString(dineInItemDTOList));
        itemClientService.returnEstimate(dineInItemDTOList);
    }

    /**
     * 套餐子菜换菜 后置处理
     */
    private void changeGroupItemAfter(ChangeGroupItemReqDTO changeGroupItemReqDTO, OrderPackageSubgroupBO packageSubgroupBiz, boolean cancelFlag) {
        UserContext userContext = UserContextUtils.get();
        checkOutThreadPool.execute(() -> {
            // 转换order
            UserContextUtils.put(userContext);
            EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
            // 订单
            OrderDO orderDO = packageSubgroupBiz.getOrderDO();
            DineinOrderDetailRespDTO orderDetailRespDTO = OrderTransform.INSTANCE.orderDO2DineinOrderDetailRespDTO(orderDO);
            // 打印换菜单
            dineInPrintService.printChangeItem(changeGroupItemReqDTO, orderDetailRespDTO, cancelFlag,
                    packageSubgroupBiz.getSubDineInItemList(), packageSubgroupBiz.getChangeSubDineInItemList(),
                    packageSubgroupBiz.getPkgOrderItemDO());
            EnterpriseIdentifier.remove();
        });
    }


    /**
     * 团购验券
     * 属于加菜中 执行验券，和第三方活动强关联
     * 一次只验一种类型的券
     */
    public void grouponDoCheck(Long orderGuid, List<DineInItemDTO> dineInItemList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(dineInItemList)) {
            return;
        }
        // 构建验券请求参数
        CouPonReqDTO couPonReqDTO = buildCouPonReqDTO(orderGuid, dineInItemList);
        // 校验参数
        // 校验第三方活动记录
        GrouponReqDTO grouponReqDTO = buildThirdActivityRecord(orderGuid, dineInItemList, couPonReqDTO);
        thirdActivityRecordService.checkRecord(grouponReqDTO);
        // 执行验券
        List<GroupVerifyDTO> results = null;
        try {
            results = groupClientService.verifyCoupon(couPonReqDTO);
            log.info("执行团购验券完成,results:{}", JacksonUtils.writeValueAsString(results));
            // 验券成功
            for (int i = 0; i < dineInItemList.size(); i++) {
                if (i >= results.size()) {
                    continue;
                }
                dineInItemList.get(i).setGroupVerify(results.get(i));
            }
        } catch (Exception e) {
            log.error("验券失败,请求参数:{},e:{}", JacksonUtils.writeValueAsString(couPonReqDTO), e.getMessage());
            // 撤销验券
            revokeGroupon(results);
            throw new BusinessException(e.getMessage());
        }
        if (results.size() != dineInItemList.size()) {
            log.error("验券失败,验券数量和成功返回数量不一致:{}", JacksonUtils.writeValueAsString(couPonReqDTO));
            // 撤销验券
            revokeGrouponByItem(orderGuid, dineInItemList);
            throw new BusinessException("验券失败，请重新进入验券页面再次验券");
        }
        // 查询团购验券结算信息
        grouponService.createTradeDetail(couPonReqDTO, results);
    }

    /**
     * 构建校验第三方活动请求参数
     */
    private GrouponReqDTO buildThirdActivityRecord(Long orderGuid, List<DineInItemDTO> dineInItemList, CouPonReqDTO couPonReqDTO) {
        MtCouponPreRespDTO couponInfo = dineInItemList.get(0).getCouponInfo();
        GrouponReqDTO grouponReqDTO = new GrouponReqDTO();
        grouponReqDTO.setOrderGuid(String.valueOf(orderGuid));
        grouponReqDTO.setActivityGuid(couponInfo.getActivityGuid());
        grouponReqDTO.setCouponCodeList(couPonReqDTO.getCouponCodeList());
        grouponReqDTO.setCouponCode(couPonReqDTO.getCouponCode());
        grouponReqDTO.setGroupBuyType(couPonReqDTO.getGroupBuyType());
        grouponReqDTO.setCount(dineInItemList.size());
        return grouponReqDTO;
    }

    /**
     * 构建验券请求参数
     */
    private CouPonReqDTO buildCouPonReqDTO(Long orderGuid, List<DineInItemDTO> dineInItemList) {
        MtCouponPreRespDTO couponInfo = dineInItemList.get(0).getCouponInfo();
        Integer groupBuyType = couponInfo.getGroupBuyType();
        String couponCode = couponInfo.getCouponCode();
        CouPonReqDTO couPonReqDTO = new CouPonReqDTO();
        couPonReqDTO.setErpId(UserContextUtils.getStoreGuid());
        couPonReqDTO.setErpName(UserContextUtils.getStoreName());
        couPonReqDTO.setErpOrderId(String.valueOf(orderGuid));
        couPonReqDTO.setGroupBuyType(groupBuyType);
        if (GroupBuyTypeEnum.MEI_TUAN.getCode() == groupBuyType) {
            couPonReqDTO.setCouponCode(couponCode);
        }
        couPonReqDTO.setCount(dineInItemList.size());
        couPonReqDTO.setUserId(couponInfo.getUserId());
        couPonReqDTO.setOrderId(couponInfo.getOrderId());
        List<String> couponCodes = dineInItemList.stream()
                .map(DineInItemDTO::getCouponInfo)
                .map(MtCouponPreRespDTO::getCouponCode)
                .collect(Collectors.toList());
        couPonReqDTO.setCouponCodeList(couponCodes);
        return couPonReqDTO;
    }

    /**
     * 商品绑定券码
     */
    private void bindCouponCode(OrderItemDO orderItemDO, DineInItemDTO dineInItemDTO) {
        GroupVerifyDTO groupVerify = dineInItemDTO.getGroupVerify();
        MtCouponPreRespDTO couponInfo = dineInItemDTO.getCouponInfo();
        if (Objects.isNull(groupVerify) || Objects.isNull(couponInfo)) {
            return;
        }
        orderItemDO.setGrouponType(couponInfo.getGroupBuyType());
        orderItemDO.setCouponCode(groupVerify.getCode());
    }

    /**
     * 保存团购验券相关记录
     */
    public void saveGroupon(Long orderGuid, List<DineInItemDTO> dineInItemList, boolean isUseBuyPrice) {
        List<DineInItemDTO> useGrouponItemList = dineInItemList.stream().filter(e -> Objects.nonNull(e.getCouponInfo())).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(useGrouponItemList)) {
            return;
        }
        List<GrouponReqDTO> grouponReqDTOList = Lists.newArrayList();
        for (DineInItemDTO dineInItemDTO : useGrouponItemList) {
            MtCouponPreRespDTO innerCouponInfo = dineInItemDTO.getCouponInfo();
            GroupVerifyDTO groupVerify = dineInItemDTO.getGroupVerify();
            GrouponReqDTO grouponReqDTO = new GrouponReqDTO();
            grouponReqDTO.setOrderGuid(String.valueOf(orderGuid));
            grouponReqDTO.setDealId(innerCouponInfo.getDealId());
            grouponReqDTO.setDealTitle(innerCouponInfo.getDealTitle());
            grouponReqDTO.setDealValue(BigDecimal.valueOf(innerCouponInfo.getDealValue()));
            grouponReqDTO.setCouponCode(groupVerify.getCode());
            grouponReqDTO.setDeductionAmount(getDeductionAmount(dineInItemDTO, isUseBuyPrice));
            grouponReqDTO.setCouponType(innerCouponInfo.getCouponType());
            grouponReqDTO.setVerifyId(groupVerify.getVerifyId());
            grouponReqDTO.setCertificateId(groupVerify.getCertificateId());
            grouponReqDTO.setUserId(innerCouponInfo.getUserId());
            grouponReqDTO.setGroupBuyType(innerCouponInfo.getGroupBuyType());
            grouponReqDTO.setCouponBuyPrice(BigDecimal.valueOf(innerCouponInfo.getCouponBuyPrice()));
            grouponReqDTOList.add(grouponReqDTO);
            if (StringUtils.isNotBlank(innerCouponInfo.getActivityGuid())) {
                // 第三方活动记录入库
                grouponReqDTO.setActivityGuid(innerCouponInfo.getActivityGuid());
                thirdActivityRecordService.saveRecord(grouponReqDTO);
            }
            // 团购优惠入库
            discountService.handleDiscount(String.valueOf(orderGuid), grouponReqDTO.getGroupBuyType(), grouponReqDTO.getDeductionAmount());
        }
        // 团购验券记录入库
        grouponService.saveBatchGroupon(grouponReqDTOList);
    }


    private BigDecimal getDeductionAmount(DineInItemDTO dineInItemDTO, boolean isUseBuyPrice) {
        BigDecimal itemPrice = dineInItemDTO.getCurrentCount().multiply(dineInItemDTO.getPrice());
        if (isUseBuyPrice) {
            return itemPrice;
        }
        itemPrice = itemPrice.add(getTotalItemAttrPrice(dineInItemDTO.getItemAttrDTOS()));
        if (!CollectionUtils.isEmpty(dineInItemDTO.getPackageSubgroupDTOS())) {
            List<PackageSubgroupDTO> packageSubgroupList = dineInItemDTO.getPackageSubgroupDTOS();
            for (PackageSubgroupDTO packageSubgroupDTO : packageSubgroupList) {
                if (!CollectionUtils.isEmpty(packageSubgroupDTO.getSubDineInItemDTOS())) {
                    List<SubDineInItemDTO> subDineInItemList = packageSubgroupDTO.getSubDineInItemDTOS();
                    for (SubDineInItemDTO subDineInItemDTO : subDineInItemList) {
                        itemPrice = itemPrice.add(subDineInItemDTO.getAddPrice().multiply(subDineInItemDTO.getCurrentCount()));
                        itemPrice = itemPrice.add(getTotalItemAttrPrice(subDineInItemDTO.getItemAttrDTOS()));
                    }
                }
            }
        }
        return itemPrice;
    }

    private BigDecimal getTotalItemAttrPrice(List<ItemAttrDTO> itemAttrList) {
        BigDecimal itemPrice = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(itemAttrList)) {
            return itemPrice;
        }
        for (ItemAttrDTO itemAttr : itemAttrList) {
            itemPrice = itemPrice.add(itemAttr.getAttrPrice().multiply(BigDecimal.valueOf(itemAttr.getNum())));
        }
        return itemPrice;
    }


    /**
     * 退菜撤销用券
     */
    public void grouponUndo(List<OrderItemDO> orderItemList) {
        List<OrderItemDO> useGrouponItemList = orderItemList.stream().filter(e -> StringUtils.isNotBlank(e.getCouponCode())).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(useGrouponItemList)) {
            return;
        }
        // 查询使用
        OrderItemDO orderItemDO = orderItemList.get(0);
        List<String> couponCodes = useGrouponItemList.stream().map(OrderItemDO::getCouponCode).collect(Collectors.toList());
        for (String couponCode : couponCodes) {
            GrouponReqDTO reqDTO = new GrouponReqDTO();
            reqDTO.setCouponCode(couponCode);
            reqDTO.setOrderGuid(String.valueOf(orderItemDO.getOrderGuid()));
            grouponService.revoke(reqDTO);
            log.info("退菜撤销成功,reqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
            // 取消团购验券结算信息
            grouponService.cancelTradeDetail(String.valueOf(orderItemDO.getOrderGuid()), couponCode);
        }
        // 退菜完成
        List<String> orderItemGuids = orderItemList.stream().map(OrderItemDO::getGuid)
                .map(String::valueOf).collect(Collectors.toList());
        orderItemService.batchClearGroupBuyFlag(orderItemDO.getOrderGuid(), orderItemGuids);
    }


    /**
     * 仅仅撤销券
     */
    private void revokeGrouponByItem(Long orderGuid, List<DineInItemDTO> dineInItemList) {
        List<DineInItemDTO> revokeItemList = dineInItemList.stream().filter(e -> Objects.nonNull(e.getGroupVerify())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(revokeItemList)) {
            return;
        }
        List<GroupVerifyDTO> revokeList = revokeItemList.stream().map(DineInItemDTO::getGroupVerify).collect(Collectors.toList());
        for (GroupVerifyDTO groupVerifyDTO : revokeList) {
            // 去撤销团购券
            groupVerifyDTO.setErpId(UserContextUtils.getStoreGuid());
            groupVerifyDTO.setErpName(UserContextUtils.getStoreName());
            groupVerifyDTO.setStoreGuid(UserContextUtils.getStoreGuid());
            log.info("验券加商品调用第三方撤销验券参数:{}", JacksonUtils.writeValueAsString(groupVerifyDTO));
            MtDelCouponRespDTO undoResult = groupClientService.revokeCoupon(groupVerifyDTO);
            log.info("验券加商品调用第三方撤销验券返回参数,undoResult:{}", JacksonUtils.writeValueAsString(undoResult));
            if (Objects.isNull(undoResult) || undoResult.getResult() != 0) {
                UNDO_COUPON_EXECUTOR_SERVICE.schedule(() -> {
                    log.info("验券加商品再次调用第三方撤销验券,groupVerifyDTO:{}", JacksonUtils.writeValueAsString(groupVerifyDTO));
                    groupClientService.revokeCoupon(groupVerifyDTO);
                }, 6, TimeUnit.SECONDS);
            }
            // 取消团购验券结算信息
            grouponService.cancelTradeDetail(String.valueOf(orderGuid), groupVerifyDTO.getCode());
        }
    }


    private void revokeGroupon(List<GroupVerifyDTO> revokeList) {
        if (CollectionUtils.isEmpty(revokeList)) {
            return;
        }
        for (GroupVerifyDTO groupVerifyDTO : revokeList) {
            //去撤销团购券
            groupVerifyDTO.setErpId(UserContextUtils.getStoreGuid());
            groupVerifyDTO.setErpName(UserContextUtils.getStoreName());
            groupVerifyDTO.setStoreGuid(UserContextUtils.getStoreGuid());
            log.warn("验券加商品异常调用第三方撤销验券参数:{}", JacksonUtils.writeValueAsString(groupVerifyDTO));
            MtDelCouponRespDTO undoResult = groupClientService.revokeCoupon(groupVerifyDTO);
            log.warn("验券加商品异常调用第三方撤销验券返回参数,undoResult:{}", JacksonUtils.writeValueAsString(undoResult));
            if (Objects.isNull(undoResult) || undoResult.getResult() != 0) {
                UNDO_COUPON_EXECUTOR_SERVICE.schedule(() -> {
                    log.warn("验券加商品异常再次调用第三方撤销验券,groupVerifyDTO:{}", JacksonUtils.writeValueAsString(groupVerifyDTO));
                    groupClientService.revokeCoupon(groupVerifyDTO);
                }, 6, TimeUnit.SECONDS);
            }
        }
    }

    private Map<Long, Long> copySubOrderItem(OrderItemDO saveOrderItemDO, OrderItemDO orderItemDO,
                                             List<OrderItemDO> saveOrderItemDOS) {
        List<OrderItemDO> subOrderItemDOS = orderItemService.listByMainItemGuidContainsDel(orderItemDO.getGuid());
        List<Long> orderItemDOGuidList = dynamicHelper.generateGuids(HST_ORDER_ITEM, subOrderItemDOS.size());
        Map<Long, Long> subOrderItemGuidMap = Maps.newHashMap();
        for (OrderItemDO subOrderItemDO : subOrderItemDOS) {
            Long originalGuid = subOrderItemDO.getGuid();
            subOrderItemDO.setGuid(orderItemDOGuidList.remove(0));
            subOrderItemDO.setParentItemGuid(saveOrderItemDO.getGuid());
            subOrderItemDO.setItemState(saveOrderItemDO.getItemState());
            subOrderItemDO.setGmtCreate(null);
            subOrderItemDO.setGmtModified(null);
            saveOrderItemDOS.add(subOrderItemDO);
            subOrderItemGuidMap.put(originalGuid, subOrderItemDO.getGuid());
        }
        return subOrderItemGuidMap;
    }

    private void copySubOrderItemExtends(Map<Long, Long> subOrderItemGuidMap,
                                         OrderItemDO orderItemDO,
                                         List<OrderItemExtendsDO> saveOrderItemExtendDOS) {
        List<OrderItemExtendsDO> orderItemExtendsDOS = orderItemExtendsService.listByOrderGuid(orderItemDO.getOrderGuid());
        if (CollectionUtil.isEmpty(orderItemExtendsDOS)) {
            return;
        }
        for (OrderItemExtendsDO orderItemExtendsDO : orderItemExtendsDOS) {
            Long newGuid = subOrderItemGuidMap.get(orderItemExtendsDO.getGuid());
            if (Objects.isNull(newGuid)) {
                continue;
            }
            orderItemExtendsDO.setGuid(newGuid);
            orderItemExtendsDO.setGmtCreate(null);
            orderItemExtendsDO.setGmtModified(null);
            saveOrderItemExtendDOS.add(orderItemExtendsDO);
        }
    }


    private void copySubOrderItemAttr(Map<Long, Long> subOrderItemGuidMap,
                                      List<Long> subOrderItemGuidList,
                                      List<ItemAttrDO> saveItemAttrDOS) {
        List<ItemAttrDO> itemAttrList = itemAttrService.listByItemGuids(subOrderItemGuidList);
        if (CollectionUtil.isEmpty(itemAttrList)) {
            return;
        }
        List<Long> itemAttrGuidList = dynamicHelper.generateGuids(ITEM_ATTR_GUID, itemAttrList.size());
        // copy
        for (ItemAttrDO itemAttrDO : itemAttrList) {
            itemAttrDO.setGuid(itemAttrGuidList.remove(0));
            itemAttrDO.setOrderItemGuid(subOrderItemGuidMap.getOrDefault(itemAttrDO.getOrderItemGuid(), itemAttrDO.getOrderItemGuid()));
            itemAttrDO.setGmtCreate(null);
            itemAttrDO.setGmtModified(null);
            saveItemAttrDOS.add(itemAttrDO);
        }
    }

    private void copySubOrderItemChanges(Map<Long, Long> subOrderItemGuidMap,
                                         OrderItemDO orderItemDO,
                                         List<Long> subOrderItemGuidList,
                                         List<OrderItemChangesDO> saveOrderItemChangesDOS) {
        List<OrderItemChangesDO> orderItemChangesList = orderItemChangesService.listByOrderItemGuidList(orderItemDO.getOrderGuid(), subOrderItemGuidList);
        if (CollectionUtil.isEmpty(orderItemChangesList)) {
            return;
        }
        log.info("套餐换菜处理赠送, 历史换菜明细:{}", JacksonUtils.writeValueAsString(orderItemChangesList));
        orderItemChangesList = orderItemChangesList.stream()
                .filter(e -> subOrderItemGuidMap.containsKey(e.getOrderItemGuid()) || Objects.isNull(e.getOriginalOrderItemGuid()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(orderItemChangesList)) {
            return;
        }
        List<Long> orderItemChangesGuidList = dynamicHelper.generateGuids(ITEM_CHANGE_GUID, orderItemChangesList.size());
        // copy
        Map<Long, Long> orderItemChangesGuidMap = Maps.newHashMap();
        Map<String, String> orderItemChangesBatchNumberMap = Maps.newHashMap();
        for (OrderItemChangesDO orderItemChangesDO : orderItemChangesList) {
            orderItemChangesGuidMap.put(orderItemChangesDO.getGuid(), orderItemChangesGuidList.remove(0));
            orderItemChangesBatchNumberMap.put(orderItemChangesDO.getChangeBatchNumber(), String.valueOf(System.currentTimeMillis()));
        }
        for (OrderItemChangesDO orderItemChangesDO : orderItemChangesList) {
            orderItemChangesDO.setGuid(orderItemChangesGuidMap.get(orderItemChangesDO.getGuid()));
            orderItemChangesDO.setOrderItemGuid(subOrderItemGuidMap.getOrDefault(orderItemChangesDO.getOrderItemGuid(),
                    orderItemChangesDO.getOrderItemGuid()));
            if (Objects.nonNull(orderItemChangesDO.getOriginalOrderItemGuid())) {
                orderItemChangesDO.setOriginalOrderItemGuid(orderItemChangesGuidMap.get(orderItemChangesDO.getOriginalOrderItemGuid()));
            }
            orderItemChangesDO.setChangeBatchNumber(orderItemChangesBatchNumberMap.get(orderItemChangesDO.getChangeBatchNumber()));
            orderItemChangesDO.setGmtCreate(null);
            orderItemChangesDO.setGmtModified(null);
            saveOrderItemChangesDOS.add(orderItemChangesDO);
        }
    }
}
