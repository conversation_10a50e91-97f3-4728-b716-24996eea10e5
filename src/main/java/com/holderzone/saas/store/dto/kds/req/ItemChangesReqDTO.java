package com.holderzone.saas.store.dto.kds.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ItemChangesReqDTO extends BaseDTO {

    private static final long serialVersionUID = -4247230675662759853L;

    @NotBlank(message = "订单Guid不得为空")
    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @NotBlank(message = "订单号不得为空")
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @NotBlank(message = "区域guid不得为空")
    @ApiModelProperty(value = "区域guid")
    private String areaGuid;

    @NotNull(message = "交易模式不得为空")
    @Min(value = 0, message = "交易模式：0=正餐，1=快餐，2=外卖")
    @Max(value = 2, message = "交易模式：0=正餐，1=快餐，2=外卖")
    @ApiModelProperty(value = "交易模式：0=正餐，1=快餐，2=外卖")
    private Integer tradeMode;

    @ApiModelProperty(value = "桌台guid")
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台号")
    private String diningTableName;

    @ApiModelProperty(value = "创建人")
    private String createStaffName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "原菜品")
    private KdsItemDTO originalKdsItem;

    @ApiModelProperty(value = "更换菜品")
    private KdsItemDTO changesKdsItem;

    @ApiModelProperty(value = "是否撤销套餐换菜")
    private Boolean cancelFlag;

    @ApiModelProperty(value = "原菜品")
    private List<KdsItemDTO> originalKdsItemList;

    @ApiModelProperty(value = "更换菜品")
    private List<KdsItemDTO> changesKdsItemList;

    /**
     * 商品信息
     */
    private List<SkuInfoRespDTO> skus;
}
