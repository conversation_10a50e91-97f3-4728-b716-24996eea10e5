package com.holderzone.saas.store.dto.item.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

@Data
public class ItemBatchUpdateDTO {

    @ApiModelProperty("是否推荐 0否 1是")
    private Integer isRecommend;

    @ApiModelProperty("是否加入整单折扣(0：否，1：是)")
    private Integer isWholeDiscount;

    @ApiModelProperty(value = "是否上架POS机（0：否，1：是）")
    private Integer isJoinPos;

    @ApiModelProperty("商品关联的分类GUID")
    private String typeGuid;

    @ApiModelProperty(value = "是否上架一体机（0：否，1：是）")
    private Integer isJoinAio;

    @ApiModelProperty("是否上架  0否 1是")
    private Integer isRack;

    @ApiModelProperty(value = "是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;

    @ApiModelProperty(value = "是否参与小程序商城（0：否，1：是）")
    private Integer isJoinMiniAppMall;

    @ApiModelProperty("审核状态")
    private Integer auditStatus;

    @ApiModelProperty(value = "是否参与小程序外卖（0：否，1：是）")
    private Integer isJoinMiniAppTakeaway;

    @ApiModelProperty(value = "是否支持堂食（0：否，1：是）")
    private Integer isJoinStore;
    @ApiModelProperty(value = "商品guid列表", required = true)
    @Size(min = 1, message = "商品guid列表不能为空")
    private List<String> itemList;
}
