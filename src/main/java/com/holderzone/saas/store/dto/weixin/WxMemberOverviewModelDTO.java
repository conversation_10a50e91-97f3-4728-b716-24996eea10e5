package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Api("会员中心主页子模块")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class WxMemberOverviewModelDTO {

	@ApiModelProperty(value = "1:我的券，2：我的排队，3.我的预订，4.我的寄存，5：我的就餐")
	private Integer modelId;

	@ApiModelProperty(value = "模块名字")
	private String modelName;

	@ApiModelProperty(value = "模块数量")
	private Integer modelCount;
}
