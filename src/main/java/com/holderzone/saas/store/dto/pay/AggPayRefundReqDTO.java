package com.holderzone.saas.store.dto.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayRefundReqDTO
 * @date 2019/03/14 10:37
 * @description 聚合支付退款请求实体
 * @program holder-saas-store-dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AggPayRefundReqDTO {

    /**
     * 退款的商户的订单号
     */
    @ApiModelProperty(value = "商户发起退款对应的订单号")
    private String orderGUID;
    /**
     * 退款类型（是）
     * 0 全款，1 部分
     */
    @ApiModelProperty(value = "退款类型。0:全款，1:部分")
    private Integer refundType;
    /**
     * 退款金额，单位分（是）
     */
    @ApiModelProperty(value = "退款金额不能为空，单位分")
    private BigDecimal refundFee;
    /**
     * 退款理由（是）
     */
    @ApiModelProperty(value = "退款的理由，必填")
    private String reason;

    @ApiModelProperty(value = "商户透传字段")
    private String attachData;

    @ApiModelProperty(value = "支付唯一标示")
    private String payGUID;

    @ApiModelProperty(value = "商户唯一标识，appId")
    protected String appId;

    @ApiModelProperty(value = "发起请求的时间")
    protected Long timestamp;

    @ApiModelProperty(value = "开发者ID")
    protected String developerId;

    @ApiModelProperty(value = "签名")
    protected String signature;

}
