package com.holderzone.saas.store.dto.trade;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品属性
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class ItemAttrDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private String guid;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    private Integer isDelete;

    /**
     * 订单guid
     */
    private String orderGuid;

    /**
     * 订单商品guid
     */
    private String orderItemGuid;

    /**
     * 属性guid
     */
    private String attrGuid;

    /**
     * 属性名称
     */
    private String attrName;

    /**
     * 属性组guid
     */
    private String attrGroupGuid;

    /**
     * 属性组名称
     */
    private String attrGroupName;

    /**
     * 属性价格
     */
    private BigDecimal attrPrice;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;


}
