package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxBrandAuthRespDTO
 * @date 2019/03/01 17:27
 * @description 商户后台品牌公众号绑定RespDTO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("商户后台品牌公众号绑定")
public class WxBrandAuthRespDTO {

    @ApiModelProperty("品牌GUID")
    @NonNull
    private String brandGuid;

    @ApiModelProperty("品牌名称")
    private String brandName;

    @ApiModelProperty("公众号名字")
    private String nickName;

    @ApiModelProperty("公众号类型")
    private String mpType;

    @ApiModelProperty("公众号APPID")
    private String mpAppId;

    @ApiModelProperty("公众号权限集合")
    private List<Integer> permissions;

    @ApiModelProperty("公众号二维码地址")
    private String qrcodeUrl;

    @ApiModelProperty("该品牌是否已绑定公众号（0未绑定，1已绑定）")
    private Integer alreadyBand;

    @ApiModelProperty("多会员体系guid")
    private String multiMemberGuid;

    @ApiModelProperty("多会员体系名称")
    private String multiMemberName;
}
