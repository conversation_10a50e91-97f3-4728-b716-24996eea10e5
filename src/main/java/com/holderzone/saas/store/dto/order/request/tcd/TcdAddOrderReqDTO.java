package com.holderzone.saas.store.dto.order.request.tcd;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TcdAddOrderReqDTO
 * @date 2018/09/08 14:24
 * @description //通吃岛创建订单入参
 * @program holder-saas-store-dto
 */
@Data
public class TcdAddOrderReqDTO implements Serializable {

    private static final long serialVersionUID = -4332755485264393339L;

    @ApiModelProperty(value = "企业guid", hidden = true)
    @NotEmpty
    private String enterpriseGuid;

    @ApiModelProperty(value = "企业名称", hidden = true)
    private String enterpriseName;

    @ApiModelProperty(value = "门店guid", hidden = true)
    private String storeGuid;

    @ApiModelProperty(value = "门店名称", hidden = true)
    private String storeName;

    @ApiModelProperty(value = "用户账号", hidden = true)
    private String account;

    @ApiModelProperty(value = "用户guid", hidden = true)
    private String userGuid;

    @ApiModelProperty(value = "用户名称", hidden = true)
    private String userName;

    @ApiModelProperty(value = "订单的guid")
    private String guid;

    @ApiModelProperty(value = "设备类型")
    private Integer deviceType;

    @ApiModelProperty(value = "订单金额（商品总额+附加费）")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "附加费")
    private BigDecimal appendFee;

    @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "附加费")
    private List<AppendFeeDetailDTO> appendFeeDetailDTOS;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态")
    private Integer state;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "结算时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTime;

    @ApiModelProperty(value = "创建人")
    private String createStaffName;

    @ApiModelProperty(value = "结账人")
    private String checkoutStaffName;

    @ApiModelProperty(value = "牌号")
    private String mark;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "桌台guid")
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台名字")
    private String diningTableName;

    @ApiModelProperty(value = "就餐人数")
    private Integer guestCount;

    @ApiModelProperty(value = "商品")
    private List<DineInItemDTO> dineInItemDTOS;

}
