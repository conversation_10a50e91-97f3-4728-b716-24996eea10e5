package com.holderzone.saas.store.dto.takeaway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutShopBindReqDTO
 * @date 2018/09/25 9:07
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@ApiModel
@NoArgsConstructor
public class TakeoutOwnItemReqDTO {

    @ApiModelProperty(value = "appId（用于自营外卖)")
    private String appId;

    @ApiModelProperty(value = "sign")
    private String sign;

    @ApiModelProperty(value = "门店code")
    private String StoreCode;
}
