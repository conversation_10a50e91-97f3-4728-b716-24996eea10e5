package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxInQueueReqDTO
 * @date 2019/05/16 15:01
 * @description 微信排队取号请求入参
 * @program holder-saas-store
 */
@Data
@ApiModel("微信排队取号请求入参")
@AllArgsConstructor
@NoArgsConstructor
public class WxInQueueReqDTO {

    private WxStoreConsumerDTO wxStoreConsumerDTO;

    @Min(value = 0, message = "非法的人数输入")
    @Max(value = 127, message = "非法的人数输入")
    @ApiModelProperty(value = "人数", required = true)
    private Byte peopleNumber;

    private String storeGuid;
}
