package com.holderzone.saas.store.dto.config.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateConfigReqDTO
 * @date 2019/05/15 18:00
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
@ApiModel(value = "门店重置置满配置结果DTO")
@Builder
public class EstimateConfigRespDTO {

    /**
     * guid
     */
    @ApiModelProperty(value = "业务主键")
    private String guid;

    /**
     * 商户guid
     */
    @ApiModelProperty(value = "商户guid")
    private String enterpriseGuid;

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;


    /**
     * 重置时间  hh:mm:ss
     */
    @ApiModelProperty(value = "重置时间  24小时格式： hh:mm")
    @Builder.Default
    private String resetTime = "05:00";
}
