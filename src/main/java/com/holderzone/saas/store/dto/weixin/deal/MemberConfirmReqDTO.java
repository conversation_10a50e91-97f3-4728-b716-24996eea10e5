package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@ApiModel("会员卡或者优惠券选中")
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Data
public class MemberConfirmReqDTO {

	@ApiModelProperty(value = "1:会员卡，2：优惠券",required = true)
	private Integer type;

	@ApiModelProperty("会员持卡GUID")
	private String memberInfoCardGuid;

	@ApiModelProperty("true使用积分，false，不使用积分")
	private Boolean integralUck;

	@ApiModelProperty(value = "优惠券code")
	private String volumeCode;
}
