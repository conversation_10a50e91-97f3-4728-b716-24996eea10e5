package com.holderzone.saas.store.dto.order.response.dinein;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReturnItemDTO
 * @date 2019/01/17 14:44
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class FreeItemDTO {

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "数量")
    private BigDecimal count;

    @ApiModelProperty(value = "操作人guid")
    private String staffGuid;

    @ApiModelProperty(value = "操作人名字")
    private String staffName;

    @ApiModelProperty(value = "订单商品guid")
    private String guid;

    @ApiModelProperty(value = "商品")
    private DineInItemDTO dineInItemDTO;
}
