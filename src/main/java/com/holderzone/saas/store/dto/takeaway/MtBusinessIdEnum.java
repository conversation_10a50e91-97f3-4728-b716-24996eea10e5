package com.holderzone.saas.store.dto.takeaway;

public enum MtBusinessIdEnum {

    TUAN_GOU(1, "团购"),

    TAKEOUT(2, "外卖"),

    SHAN_HUI(3, "闪惠"),

    PAYMENT(5, "支付"),

    BOOK(7, "预定"),

    MEMBER(8, "全渠道会员"),

    BRAND_MEMBER(15, "大众点评 - 到餐品牌会员卡")
    ;

    /**
     * 类型
     */
    private int type;

    /**
     * 描述
     */
    private String desc;

    MtBusinessIdEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static MtBusinessIdEnum ofType(int type) {
        for (MtBusinessIdEnum value : MtBusinessIdEnum.values()) {
            if (type == value.type) {
                return value;
            }
        }
        throw new IllegalArgumentException("不存在业务类型businessId[" + type + "]");
    }
}
