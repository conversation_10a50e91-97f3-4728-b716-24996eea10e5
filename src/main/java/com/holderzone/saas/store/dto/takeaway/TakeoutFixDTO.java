package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class TakeoutFixDTO implements Serializable {

    private static final long serialVersionUID = 3670997254410704553L;

    /**
     * 修复记录请求DTO
     */
    @ApiModelProperty("修复记录请求DTO")
    @Valid
    private TakeoutFixRecordDTO recordReqDTO;

    /**
     * 修复明细请求DTO
     */
    @ApiModelProperty("修复明细请求DTO")
    @Valid
    // @NotEmpty(message = "修复数据不能为空")
    private List<TakeoutFixItemDTO> itemReqDTOList;

    /**
     * 是否提交记录
     * 提交记录：1  立即修复：0
     */
    @ApiModelProperty("是否提交记录")
    @NotNull(message = "是否提交不能为空")
    private Boolean commitFlag;

    /**
     * 是否修复商品绑定关系
     * 0-否 1-是
     */
    @ApiModelProperty("是否修复商品绑定关系")
    @NotNull(message = "是否修复商品绑定关系不能为空")
    private Boolean fixBindFlag;

}
