package com.holderzone.saas.store.dto.member.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberPayRecordReqDTO
 * @date 2018/09/29 9:20
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class MemberPayRecordReqDTO extends  BasePageDTO{
    @ApiModelProperty(value = "会员Guid",required = true)
    @NotBlank(message = "会员Guid不能为空")
    private  String memberGuid;
    @ApiModelProperty(value ="查询类型0充值1支付2退回",required = true)
    private  Byte type;
    @ApiModelProperty(value = "交易流水号,对应账户充值页面按交易流水号")
    private  String sequenceNo;
    @ApiModelProperty(value = "交易开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime tradingTimeBegin;
    @ApiModelProperty(value = "交易截至时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime tradingTimeEnd;
    @ApiModelProperty(value = "订单号,对应订单支付页面按订单号")
    private String orderNo;

}
