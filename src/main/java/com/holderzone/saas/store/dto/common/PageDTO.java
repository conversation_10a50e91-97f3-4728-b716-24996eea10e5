package com.holderzone.saas.store.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel("分页查询对象DTO")
public class PageDTO implements Serializable {

    private static final long serialVersionUID = -4307925127548322621L;

    /**
     * 当前页数
     */
    @ApiModelProperty(value = "当前页数")
    private long currentPage = 1;

    /**
     * 每页显示条数
     */
    @ApiModelProperty(value = "每页显示条数")
    private long pageSize = 10;
}
