package com.holderzone.saas.store.dto.business.queue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueuedTypeDO
 * @date 2018/07/27 下午4:44
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class QueuedTypeDTO {

    /**
     * 门店guid
     */
    @ApiModelProperty("门店guid")
    private String storeGuid;

    /**
     * 队列guid
     */
    @ApiModelProperty("队列guid")
    private String queuedTypeGuid;

    /**
     * 队列名称
     */
    @ApiModelProperty("队列名称")
    private String name;

    /**
     * 队列编号
     */
    @ApiModelProperty("队列编号")
    private String code;

    /**
     * 就餐人数左区间
     */
    @ApiModelProperty("就餐人数左区间")
    private Integer leftInterval;

    /**
     * 就餐人数右区间
     */
    @ApiModelProperty("就餐人数右区间")
    private Integer rightInterval;

    /**
     * 是否已启用
     * 0=未启用
     * 1=已启用
     */
    @ApiModelProperty("是否已启用。0=未启用，1=已启用")
    private Integer enable;

    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    @ApiModelProperty("是否已删除。0=未删除，1=已删除")
    private Integer deleted;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime gmtModified;
}
