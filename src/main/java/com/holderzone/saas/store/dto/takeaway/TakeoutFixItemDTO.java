package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class TakeoutFixItemDTO implements Serializable {

    private static final long serialVersionUID = 3670997254410704553L;

    private Long id;

    @ApiModelProperty("门店guid")
    @NotBlank(message = "门店guid不能为空")
    private String storeGuid;

    @ApiModelProperty("门店名字")
    @NotBlank(message = "门店名字不能为空")
    private String storeName;

    /**
     * 订单来源：0=美团，1=饿了么 6=赚餐自营外卖
     *
     * @see com.holderzone.saas.store.dto.takeaway.OrderType.TakeoutSubType
     */
    @ApiModelProperty("订单来源：0=美团，1=饿了么 6=赚餐自营外卖")
    @NotNull(message = "订单来源不能为空")
    private Integer orderSubType;

    @ApiModelProperty("外卖商品名称")
    @NotBlank(message = "外卖商品名称不能为空")
    private String takeoutItemName;

    @ApiModelProperty("外卖方商品唯一标识")
    @NotBlank(message = "外卖方商品唯一标识不能为空")
    private String thirdSkuId;

    @ApiModelProperty("门店商品guid")
    @NotBlank(message = "门店商品guid不能为空")
    private String erpItemGuid;

    @ApiModelProperty("门店商品规格guid")
    @NotBlank(message = "门店商品规格guid不能为空")
    private String erpItemSkuGuid;

    @ApiModelProperty("门店商品名称")
    @NotBlank(message = "门店商品名称不能为空")
    private String erpItemName;

    @ApiModelProperty("门店商品单价")
    @NotNull(message = "门店商品单价不能为空")
    private BigDecimal erpItemPrice;

    @ApiModelProperty("门店商品核算单价")
    private BigDecimal takeawayAccountingPrice;

    @ApiModelProperty("门店映射数量")
    @NotNull(message = "门店映射数量不能为空")
    private BigDecimal erpItemCount;

    @ApiModelProperty("修复条数")
    @NotNull(message = "修复条数不能为空")
    private Integer takeoutOrderCount;
}
