package com.holderzone.saas.store.dto.weixin;

import com.holderzone.saas.store.dto.weixin.common.WxStoreInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreConfigDTO
 * @date 2019/05/17 15:17
 * @description 门店微信信息及功能对象
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("门店微信信息及功能对象")
public class WxStoreConfigDTO extends WxStoreInfoDTO {

    @ApiModelProperty("微信排队功能配置")
    private WxStoreQueueConfigDTO wxStoreQueueConfigDTO;
}
