package com.holderzone.saas.store.dto.reserve;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/9/27
 * @description 查询时间范围内待处理订单
 */
@Data
@ApiModel(value = "查询时间范围内待处理订单")
@EqualsAndHashCode(callSuper = false)
public class CommitStatisticsReqDTO implements Serializable {

    private static final long serialVersionUID = 7268055063328089692L;

    @ApiModelProperty(value = "开始日期")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束日期")
    private LocalDate endDate;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;
}
