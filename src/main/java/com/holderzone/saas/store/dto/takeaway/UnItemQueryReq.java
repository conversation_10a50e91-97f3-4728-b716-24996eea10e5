package com.holderzone.saas.store.dto.takeaway;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class UnItemQueryReq extends BasePageDTO implements Serializable {

    private static final long serialVersionUID = 2605105818892085689L;

    /**
     * 查询关键字
     */
    private String keywords;

    /**
     * 门店guids
     */
    private List<String> storeGuids;

    @NotNull(message = "外卖类型不得为空")
    @Min(value = 0, message = "外卖类型(0：美团，1：饿了么,2:自营,3:赚餐,4:抖音,5:京东)")
    @Max(value = 5, message = "外卖类型(0：美团，1：饿了么,2:自营,3:赚餐,4:抖音,5:京东)")
    @ApiModelProperty(value = "外卖类型(0：美团，1：饿了么,2:自营,3:赚餐,4:抖音,5:京东)", required = true)
    private Integer takeoutType;

    @ApiModelProperty(value = "是否绑定")
    private Integer bindingFlag;

    /**
     * 品牌guid
     */
    private String brandGuid;
}
