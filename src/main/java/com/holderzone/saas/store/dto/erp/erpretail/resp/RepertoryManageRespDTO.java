package com.holderzone.saas.store.dto.erp.erpretail.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel("出入库的用于添加或者更新的实体")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepertoryManageRespDTO {

    @ApiModelProperty("出入库实体的标识")
    private String guid;

    @ApiModelProperty("单据名称，由后端根据invoiceType自动匹配生成")
    private String invoiceName;

    @ApiModelProperty("单据日期")
    private String invoiceMakeTime;

    @ApiModelProperty("单据状态，1：完成，0：作废")
    private int status;

    @ApiModelProperty("商品总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("应付金额")
    private BigDecimal shouldPayOrReceive;
}
