package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentDTO
 * @date 2018/08/10 10:46
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentDTO extends FrontedRequestJHPayDTO {

    @ApiModelProperty(value = "业务主键，如果有必须传入，对于聚合支付反结账情况")
    private String paymentGuid;

    @ApiModelProperty(value = "操作员工guid")
    private String operationStaffGuid;

    @ApiModelProperty(value = "操作员工姓名")
    private String operationStaffName;

    @NotNull
    @ApiModelProperty(value = "支付方式类型:0:现金支付，1:聚合支付，2:银行卡支付，3:会员卡支付 3+n:其他支付方式")
    private Integer paymentType;

    @ApiModelProperty(value = "如果是银行卡支付或者其他支付方式，需要传入银行卡流水号")
    private String thirdOrderNo;

    @ApiModelProperty(value = "如果会员登录，需要传入此参数")
    private String memberGuid;

    @ApiModelProperty(value = "如果会员登录，需要传入此参数")
    private String telPhoneNo;

    @NotBlank
    @ApiModelProperty(value = "支付方式名称")
    private String paymentTypeName;

    @ApiModelProperty(value = "如果是会员支付，需要传入会员密码！！！")
    private String password;

    @ApiModelProperty(value = "交易类型 0:订单正常结算，1:会员充值")
    private Integer tradingType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "会员memberName")
    private String memberName;

    @ApiModelProperty(value = "找零")
    private BigDecimal cashReturn;

}
