package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SubItemSkuSynRespDTO
 * @date 2019/01/03 下午3:41
 * @description //套餐分组下的子商品实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class SubItemSkuSynRespDTO {
    @ApiModelProperty(value = "规格Guid", required = true)
    private String skuGuid;

    @ApiModelProperty(value = "maybe null,规格名称")
    private String skuName = "";

    @ApiModelProperty(value = "商品图片")
    private String pictureUrl = "";

    @ApiModelProperty(value = "规格的商品GUID", required = true)
    private String itemGuid;

    @ApiModelProperty(value = "规格的商品名称", required = true)
    private String itemName;

    @ApiModelProperty(value = "maybe null,规格的编码")
    private String code;

    @ApiModelProperty(value = "规格关联的商品的分类GUID", required = true)
    private String typeGuid;

    @ApiModelProperty(value = "规格关联的商品的分类名称", required = true)
    private String typeName;

    @ApiModelProperty(value = "商品类型(注：套餐子商品中不包括套餐)：1.套餐（不称重，无规格），2.规格商品（单商品，不称重），3.称重商品（单商品，称重），4.单品  5.宴会套餐", required = true)
    private Integer itemType;

    @ApiModelProperty(value = "商品单位", required = true)
    private String unit;

    @ApiModelProperty(value = "商品元素中的数量", required = true)
    private BigDecimal itemNum;

    @ApiModelProperty(value = "商品加价", required = true)
    private BigDecimal addPrice;

    @ApiModelProperty(value = "子商品售价", required = true)
    private BigDecimal salePrice;

    @ApiModelProperty(value = "成本价格")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "商品毛利率")
    private BigDecimal itemGrossMargin;

    @ApiModelProperty(value = "属性组状态:0：无属性; 1:有属性; 2:有必选属性组", required = true)
    private Integer hasAttr;

    @ApiModelProperty(value = "默认勾选数量，1：默认勾选一个，0,默认不勾选", required = true)
    private Integer defaultNum;

    @ApiModelProperty(value = "是否可重复选择，0:否,1:是", required = true)
    private Integer isRepeat;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "单品备注")
    private String remark;

    @ApiModelProperty(value = "可能为空集合，该子商品所含属性集合")
    private List<AttrGroupSynRespDTO> attrGroupList;

    @ApiModelProperty(value = "是否估清 1:否 2:是")
    private Integer isSoldOut = 1;

    @ApiModelProperty(value = "是否参与微信点餐（0：否，1：是）")
    private Integer isJoinWeChat;

    /**
     * 当前剩余数量
     */
    @ApiModelProperty(value = "当前剩余数量")
    private BigDecimal residueQuantity = BigDecimal.ZERO;
}
