package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Api("会员支付")
public class WxPayWayDetailsDTO {

	@ApiModelProperty("支付方式,0:微信支付，1：会员卡余额")
	private Integer payWay;

	@ApiModelProperty(value = "会员卡名字")
	private String cardName;

	@ApiModelProperty("支付方式")
	private String payWayName;

	@ApiModelProperty("余额")
	private BigDecimal balance;

	@ApiModelProperty("0：可用，1：不可用")
	private Integer enable;

	@ApiModelProperty("支付金额")
	private BigDecimal payAmount;
}
