package com.holderzone.saas.store.dto.queue;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableConfirmDTO
 * @date 2019/03/30 15:31
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
public class TableConfirmDTO extends ItemGuidDTO {
    @ApiModelProperty(value = "订单的guid")
    private String guid;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "桌台guid", required = true)
    @LockField
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台名字", required = true)
    @NotBlank(message = "桌台名字不能为空")
    private String diningTableName;

    @ApiModelProperty(value = "桌台区域GUID", required = true)
    private String areaGuid;

    @ApiModelProperty(value = "桌台区域名称", required = true)
    @NotBlank(message = "桌台区域名称不能为空")
    private String areaName;

    @ApiModelProperty(value = "是否打印后厨（0：否，1：是）")
    private int print;

    @ApiModelProperty(value = "商品")
    private List<DineInItemDTO> dineInItemDTOS;
}