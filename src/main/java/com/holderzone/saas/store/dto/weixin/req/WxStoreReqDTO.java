package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreReqDTO
 * @date 2019/02/19 11:52
 * @description 微信门店配置请求DTO
 * @program holder-saas-store-weixin
 */
@Data
@ApiModel(value = "单个门店请求DTO")
@AllArgsConstructor
@NoArgsConstructor
@RequiredArgsConstructor
@Builder
@Accessors(chain = true)
public class WxStoreReqDTO implements Serializable {

    private static final long serialVersionUID = 844258735723282891L;

    @ApiModelProperty(value = "guid")
    String guid;

    @ApiModelProperty(value = "门店Guid")
    @NotNull
	@NonNull
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "当前操作类型：0点餐，1排队")
    private Integer type;

    @Override
    public String toString() {
        return "WxStoreReqDTO{" +
                "storeGuid='" + storeGuid + '\'' +
                ", storeName='" + storeName + '\'' +
                '}';
    }
}
