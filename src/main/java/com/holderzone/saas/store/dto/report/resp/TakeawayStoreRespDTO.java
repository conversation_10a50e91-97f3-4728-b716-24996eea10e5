package com.holderzone.saas.store.dto.report.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayStoreRespDTO
 * @date 2018/12/05 16:57
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
public class TakeawayStoreRespDTO implements Serializable {

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "时间")
    private String time;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "完成订单数")
    private Integer finishedOrderCount;

    @ApiModelProperty(value = "完成订单金额")
    private BigDecimal finishedTotal;

    @ApiModelProperty(value = "退款订单数")
    private Integer refundOrderCount;

    @ApiModelProperty(value = "退款订单金额")
    private BigDecimal refundTotal;


    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getFinishedOrderCount() {
        return finishedOrderCount;
    }

    public void setFinishedOrderCount(Integer finishedOrderCount) {
        this.finishedOrderCount = finishedOrderCount;
    }

    public BigDecimal getFinishedTotal() {
        return finishedTotal;
    }

    public void setFinishedTotal(BigDecimal finishedTotal) {
        this.finishedTotal = finishedTotal;
    }

    public Integer getRefundOrderCount() {
        return refundOrderCount;
    }

    public void setRefundOrderCount(Integer refundOrderCount) {
        this.refundOrderCount = refundOrderCount;
    }

    public BigDecimal getRefundTotal() {
        return refundTotal;
    }

    public void setRefundTotal(BigDecimal refundTotal) {
        this.refundTotal = refundTotal;
    }

}
