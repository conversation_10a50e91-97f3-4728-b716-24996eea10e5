package com.holderzone.saas.store.dto.organization;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@ApiModel("门店附加信息DTO")
@Data
@Accessors(chain = true)
public class StoreAttachInfoDTO implements Serializable {

    @ApiModelProperty(value = "门店guid",required = true)
    @NotBlank(message = "门店guid不能为空")
    private String storeGuid;
    @ApiModelProperty("聚合支付账号")
    private String uatAppId;
    @ApiModelProperty("聚合支付密钥")
    private String uatSecret;
}
