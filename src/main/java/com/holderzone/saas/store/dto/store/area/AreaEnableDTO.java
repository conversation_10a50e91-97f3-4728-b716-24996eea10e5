package com.holderzone.saas.store.dto.store.area;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AreaDO
 * @date 2018/07/23 下午12:52
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AreaEnableDTO implements Serializable {

    private static final long serialVersionUID = -8583851590641597245L;

    /**
     * 区域guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "区域guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "区域guid", required = true)
    private String areaGuid;

    /**
     * 是否已启用
     * 0=未启用
     * 1=已启用
     */
    @NotNull
    @Min(value = 0, message = "是否启用不得为空，范围[0-1]，0=未启用，1=已启用")
    @Max(value = 1, message = "是否启用不得为空，范围[0-1]，0=未启用，1=已启用")
    @ApiModelProperty(value = "是否已启用。0=未启用，1=已启用。", required = true)
    private Integer enable;
}
