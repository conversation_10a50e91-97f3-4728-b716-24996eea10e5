/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:StoredCashFormatDTO.java
 * Date:2019-12-5
 * Author:terry
 */

package com.holderzone.saas.store.dto.print.format;

import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class StoredCashFormatDTO extends FormatDTO {

    @NotNull(message = "门店名称不能为空")
    @ApiModelProperty(value = "门店名称", required = true)
    private FormatMetadata storeName;

    @NotNull(message = "单据类型不能为空")
    @ApiModelProperty(value = "单据类型", required = true)
    private FormatMetadata invoiceName;

    @NotNull(message = "交易流水号不能为空")
    @ApiModelProperty(value = "交易流水号", required = true)
    private FormatMetadata serialNumber;

    @NotNull(message = "充值卡号不能为空")
    @ApiModelProperty(value = "充值卡号", required = true)
    private FormatMetadata cardNo;

    @NotNull(message = "充值金额不能为空")
    @ApiModelProperty(value = "充值金额", required = true)
    private FormatMetadata recharge;

    @NotNull(message = "支付方式不能为空")
    @ApiModelProperty(value = "支付方式", required = true)
    private FormatMetadata payType;

    @NotNull(message = "赠送金额不能为空")
    @ApiModelProperty(value = "赠送金额", required = true)
    private FormatMetadata presented;

    @NotNull(message = "赠送积分不能为空")
    @ApiModelProperty(value = "赠送积分", required = true)
    private FormatMetadata presentedIntegral;

    @NotNull(message = "当前余额不能为空")
    @ApiModelProperty(value = "当前余额", required = true)
    private FormatMetadata currentCash;

    @NotNull(message = "当前积分不能为空")
    @ApiModelProperty(value = "当前积分", required = true)
    private FormatMetadata integration;

    @NotNull(message = "会员姓名不能为空")
    @ApiModelProperty(value = "会员姓名", required = true)
    private FormatMetadata memberName;

    @NotNull(message = "手机号不能为空")
    @ApiModelProperty(value = "会员手机号", required = true)
    private FormatMetadata memberPhone;

    @NotNull(message = "充值时间不能为空")
    @ApiModelProperty(value = "充值时间", required = true)
    private FormatMetadata rechargeTime;

    @NotNull(message = "操作员不能为空")
    @ApiModelProperty(value = "操作员", required = true)
    private FormatMetadata operator;

    @NotNull(message = "打印时间不能为空")
    @ApiModelProperty(value = "打印时间", required = true)
    private FormatMetadata printTime;

    @Override
    public void applyDefault() {
        super.applyDefault();
        if (storeName == null) {
            storeName = new FormatMetadata(2, 1, false);
        }
        if (invoiceName == null) {
            invoiceName = new FormatMetadata(2, 1, false);
        }
        if (serialNumber == null) {
            serialNumber = new FormatMetadata(1, 0, false);
        }
        if (cardNo == null) {
            cardNo = new FormatMetadata(1, 0, false);
        }
        if (recharge == null) {
            recharge = new FormatMetadata(1, 0, false);
        }
        if (payType == null) {
            payType = new FormatMetadata(1, 0, false, false);
        }
        if (presented == null) {
            presented = new FormatMetadata(1, 0, false);
        }
        if (presentedIntegral == null) {
            presentedIntegral = new FormatMetadata(1, 0, false);
        }
        if (currentCash == null) {
            currentCash = new FormatMetadata(1, 0, false);
        }
        if (integration == null) {
            integration = new FormatMetadata(1, 0, false);
        }
        if (memberName == null) {
            memberName = new FormatMetadata(1, 0, false, false);
        }
        if (memberPhone == null) {
            memberPhone = new FormatMetadata(1, 0, false, false);
        }
        if (rechargeTime == null) {
            rechargeTime = new FormatMetadata(1, 0, false);
        }
        if (operator == null) {
            operator = new FormatMetadata(1, 0, false);
        }
        if (printTime == null) {
            printTime = new FormatMetadata(1, 2, false);
        }
    }
}
