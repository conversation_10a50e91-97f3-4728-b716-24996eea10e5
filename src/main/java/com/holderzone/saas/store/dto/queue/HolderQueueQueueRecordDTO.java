package com.holderzone.saas.store.dto.queue;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HolderQueueQueueRecordDTO
 * @date 2019/05/16 10:05
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HolderQueueQueueRecordDTO {
    private HolderQueueDTO queue;
    private HolderQueueItemDetailDTO queueRecord;
}