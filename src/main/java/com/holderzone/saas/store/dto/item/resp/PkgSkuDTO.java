package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/30
 * @description 套餐规格
 */
@Data
@ApiModel(value = "套餐规格")
@EqualsAndHashCode(callSuper = false)
public class PkgSkuDTO implements Serializable {

    private static final long serialVersionUID = -1451140371543200001L;

    @ApiModelProperty(value = "套餐规格guid")
    private String skuGuid;

    @ApiModelProperty(value = "子项规格guid")
    private String subSkuGuid;

}
