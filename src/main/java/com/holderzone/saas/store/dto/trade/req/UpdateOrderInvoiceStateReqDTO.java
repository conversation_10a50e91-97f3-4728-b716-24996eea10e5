package com.holderzone.saas.store.dto.trade.req;

import com.holderzone.saas.store.enums.trade.OrderInvoiceStateEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/10
 * @description 更新订单开票状态
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "更新订单开票状态")
public class UpdateOrderInvoiceStateReqDTO implements Serializable {

    private static final long serialVersionUID = -8342774105810144537L;

    /**
     * 订单流水号(全局唯一)
     */
    private String orderNo;

    /**
     * 订单状态
     * 【paas】 -1 未绑定门店，-2 百旺返回错误或无数据
     * 【百旺】0待审核，1开票中，2开具成功，3开具失败，4作废已申请，5 作废中 6已作废，7作废失败，8已开未生成PDF（税控电票），9 已审核 ，10 待扫码，11 已追回
     * 1004 需要登录，1003 需要认证
     * @see OrderInvoiceStateEnum
     */
    private String status;

    /**
     * 发票号码
     * 开票失败，返回为空
     */
    private String invoiceNo;

}
