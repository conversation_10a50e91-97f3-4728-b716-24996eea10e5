package com.holderzone.saas.store.dto.member.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/17 11:32
 * @className:
 */
@Data
@ApiModel("订单计算随行红包参数")
@Accessors(chain = true)
public class RedPacketAmountFeeQuery implements Serializable {

    private static final long serialVersionUID = -2148630032852254201L;

    @ApiModelProperty("运营主体")
    private String operSubjectGuid;

    @ApiModelProperty("订单guid列表")
    private List<RedPacketOrderDTO> orderNumbers;
}
