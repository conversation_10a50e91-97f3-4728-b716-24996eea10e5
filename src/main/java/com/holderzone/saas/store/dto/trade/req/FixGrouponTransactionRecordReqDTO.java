package com.holderzone.saas.store.dto.trade.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class FixGrouponTransactionRecordReqDTO implements Serializable {

    private static final long serialVersionUID = 237969085326580656L;

    private String enterpriseGuid;

    private String orderGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

}
