package com.holderzone.saas.store.dto.reserve;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "ReserveReportDataDTO", description = "预订菜品统计明细")
@Accessors(chain = true)
@NoArgsConstructor
public class ReserveReportDataDTO {

    /**
     * 分类guid
     */
    @ApiModelProperty(value = "分类guid")
    private String itemTypeGuid;

    /**
     * 分类名
     */
    @ApiModelProperty(value = "分类名称")
    private String itemTypeName;

    /**
     * 分类数量
     */
    @ApiModelProperty(value = "分类数量")
    private Double itemTypeNum;

    /**
     * 分类小计
     */
    @ApiModelProperty(value = "分类小计")
    private BigDecimal itemTypePrice;

    /**
     * 分类下菜品小计
     */
    @ApiModelProperty(value = "分类菜品明细")
    private List<ReserveItemDTO> reserveItemDTOList;

}
