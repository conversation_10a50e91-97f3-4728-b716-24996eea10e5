package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.item.common.MchntItemBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemGroupMealSaveReqDTO
 * @date 2019/12/06 下午5:39
 * @description //宴会/团餐保存DTO
 * @program holder
 */
@Data
@ApiModel(value = "宴会/团餐保存DTO")
public class ItemGroupMealSaveReqDTO extends MchntItemBaseDTO {

    @ApiModelProperty(value = "该团购套餐GUID,新增时不传")
    private String itemGuid;

    @ApiModelProperty(value = "商品名称")
    @Size(max = 40)
    @NotEmpty
    private String name;

    @ApiModelProperty(value = "pinyin")
    private String pinyin;

    @ApiModelProperty(value = "商品类型：（0：门店自己创建的商品，1：品牌自己创建的商品,2:被推送过来的商品）")
    private Integer itemFrom;

    @ApiModelProperty(value = "商品类型：1.套餐（不称重，无规格），2.规格商品 ，3.称重商品（单商品，称重） 4.单品  5：团队套餐")
    @NotNull
    private Integer itemType;

    @ApiModelProperty(value = "排序")
    @Max(99999999)
    private Integer sort;

    @ApiModelProperty(value = "商品描述")
    @Size(max = 200)
    private String description;

    @ApiModelProperty(value = "团餐套餐规格")
    private SkuSaveReqDTO sku;

    @ApiModelProperty(value = "团餐下子菜规格")
    private List<GroupMealSkuSaveReqDTO> skuList;


}
