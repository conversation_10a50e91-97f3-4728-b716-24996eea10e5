package com.holderzone.saas.store.dto.print.content.nested;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class MultiMemberPayRecord implements Serializable {

    private static final long serialVersionUID = -8951585514851962893L;

    /**
     * 卡支付余额
     */
    private BigDecimal memberCardBalance;

    /**
     * 本次消费实充金额
     */
    private BigDecimal rechargeAmount;

    /**
     * 本次消费赠送金额
     */
    private BigDecimal giftAmount;

    /**
     * 本次消费补贴余额
     */
    private BigDecimal subsidyAmount;

    /**
     * 会员卡号
     */
    private String memberCardNum;

}
