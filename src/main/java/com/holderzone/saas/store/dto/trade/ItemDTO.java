package com.holderzone.saas.store.dto.trade;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单商品
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class ItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private String guid;

    /**
     * 反结账原菜品guid
     */
    private Long originalOrderItemGuid;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    private Integer isDelete;

    /**
     * 订单guid
     */
    private String orderGuid;

    /**
     * 用户微信公众号openId
     */
    private String userWxPublicOpenId;

    /**
     * 微信菜品批次
     */
    private String wxBatch;

    /**
     * 商品guid
     */
    private String itemGuid;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品编号
     */
    private String code;

    /**
     * 商品类别guid
     */
    private String itemTypeGuid;

    /**
     * 商品类别名称
     */
    private String itemTypeName;

    /**
     * 套餐分组guid
     */
    private String subgroupGuid;

    /**
     * 套餐分组名字
     */
    private String subgroupName;

    /**
     * 商品的规格
     */
    private String skuGuid;

    /**
     * 规格名称
     */
    private String skuName;

    /**
     * sku价格
     */
    private BigDecimal price;

    /**
     * 会员价格
     */
    private BigDecimal memberPrice;
    /**
     * 商品原价
     */
    private BigDecimal originalPrice;
    /**
     * 折扣
     */
    private Integer discountPercent;
    /**
     * 菜品优惠合计
     */
    private BigDecimal totalDiscountFee;

    /**
     * 属性总价
     */
//    private BigDecimal attrTotal;

    /**
     * 是否有属性（0：否，1：是）
     */
    private Integer hasAttr;

    /**
     * 套餐主项guid
     */
    private String parentItemGuid;

    /**
     * 商品类型(1.套餐主项，2.规格，3.称重，4.单品 )
     */
    private Integer itemType;

    /**
     * 商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂 ，8.已上菜 )
     */
    private Integer itemState;

    /**
     * 催品次数
     */
    private Integer urgeNum;

    /**
     * 当前数量（不包括赠送，不要重复减赠送折扣）
     */
    private BigDecimal currentCount;

    /**
     * 赠送数量（销售统计=当前+赠送）
     */
    private BigDecimal freeCount;

    /**
     * 退货数量（赠送变为退之后不计入销售和增菜统计）
     */
    private BigDecimal returnCount;

    /**
     * 套餐预设数量
     */
    private BigDecimal packageDefaultCount;

    /**
     * 子项加价
     */
    private BigDecimal addPrice;

    /**
     * 计数单位
     */
    private String unit;

    /**
     * 是否0份售卖（0：否，1：是）
     */
    private Integer isZero;

    /**
     * 是否反结账已支付（0：否，1：是）
     */
    private Integer isPay;

    /**
     * 是否参与整单折扣(0：否，1：是)
     */
    private Integer isWholeDiscount;

    /**
     * 是否参与会员折扣（0：否，1：是）
     */
    private Integer isMemberDiscount;

    /**
     * 是否改价（0：未改价 默认，1：单品改价  2: 单品折扣）  默认为0
     */
    private Integer priceChangeType;
    /**
     * 商品备注
     */
    private String remark;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 创建操作人guid
     */
    private String createStaffGuid;

    /**
     * 创建操作人guid
     */
    private String createStaffName;
}
