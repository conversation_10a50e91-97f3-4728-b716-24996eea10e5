package com.holderzone.saas.store.dto.trade;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> R
 * @date 2020/12/15 17:07
 * @description
 */
@Data
@ApiModel(value = "挂账还款记录分页查询DTO")
public class DebtUnitRecordPageRespDTO {
    /**
     * 全局唯一主键
     */
    @ApiModelProperty(value = "主键")
    private String guid;
    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    /**
     * 单位联系人
     */
    @ApiModelProperty(value = "单位联系人")
    private String unitContactName;

    /**
     * 单位联系电话
     */
    @ApiModelProperty(value = "单位联系电话")
    private String unitContactTel;

    @ApiModelProperty(value = "单位Guid")
    private String unitGuid;

    @ApiModelProperty(value = "单位编码")
    private String unitCode;
    /**
     * 还款票据编号
     */
    @ApiModelProperty(value = "还款票据编号")
    private String debtInvoiceCode;
    /**
     * 挂账金额
     */
    @ApiModelProperty(value = "挂账金额")
    private BigDecimal debtFee;
    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式（0：现金，1：支付宝，2：微信，3：银行转账，4：支票）")
    private Integer paymentType;
    /**
     * 还款金额
     */
    @ApiModelProperty(value = "还款金额")
    private BigDecimal repaymentFee;
    /**
     * 是否还款（默认0，未还款，1已还款）
     */
    @ApiModelProperty(value = "是否还款（默认不传查全部！0:未还款，1:已还款）")
    private Integer repaymentStatus;
    /**
     * 操作人name
     */
    @ApiModelProperty(value = "操作人name")
    private String createStaffName;
    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;
    /**
     * 整单备注
     */
    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "凭证")
    private List<String> evidenceList;

    @ApiModelProperty(value = "还款批次号")
    private String repaymentBatchNumber;

    /**
     * 会员GUID
     */
    @ApiModelProperty(value = "会员GUID")
    private String memberInfoGuid;

    /**
     * 消费记录GUID
     */
    @ApiModelProperty(value = "消费记录GUID")
    private String memberConsumptionGuid;

    /**
     * 会员卡guid
     */
    @ApiModelProperty(value = "会员卡guid")
    private String memberCardGuid;

    @ApiModelProperty(value = "会员手机号")
    private String memberPhone;

    /**
     * 会员名字
     */
    @ApiModelProperty(value = "会员名字")
    private String memberName;
}
