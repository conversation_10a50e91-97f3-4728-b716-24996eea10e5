package com.holderzone.saas.store.dto.weixin.deal;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@Builder
@ApiModel("商品套餐分组下的商品")
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown=true)
public class ItemInfoSubSkuDTO {

	@ApiModelProperty(value = "商品id")
	private String itemGuid;
	@ApiModelProperty(value = "商品名称")
	private String itemName;
	@ApiModelProperty(value = "可能是空,商品编码")
	private String code;
	@ApiModelProperty(value = "商品分类id")
	private String itemTypeGuid;
	@ApiModelProperty(value = "商品类型名称")
	private String itemTypeName;
	@ApiModelProperty(value = "商品类型2.多规格，3.称重，4.单品。")
	private Integer itemType;
	@ApiModelProperty(value = "商品单位")
	private String unit;
	@ApiModelProperty(value = "规格Guid")
	private String skuGuid;
	@ApiModelProperty(value = "可能是空,规格名称")
	private String skuName = StringUtils.EMPTY;
	@ApiModelProperty(value = "商品图片")
	private String pictureUrl = StringUtils.EMPTY;
	@ApiModelProperty(value = "商品预售数：例：4，用户每加1份，实际加4份")
	private BigDecimal itemNum;
	@ApiModelProperty(value = "商品价格")
	private BigDecimal addPrice;
	@ApiModelProperty(value = "子商品售价", required = true)
	private BigDecimal salePrice;
	@ApiModelProperty(value = "是否可重复选择，0:否,1:是")
	@Builder.Default
	private Integer isRepeat = 0;
	@ApiModelProperty(value = "默认勾选数量，1：默认勾选一个，0,默认不勾选", required = true)
	private Integer defaultNum;
	@ApiModelProperty(value = "可能为空集合，该子商品所含属性集合")
	private List<ItemInfoAttrGroupDTO> attrGroupList;
	@Builder.Default
	@ApiModelProperty(value = "是否估清 1:否 2:是")
	private Integer isSoldOut = 1;
	@ApiModelProperty(value = "当前剩余数量")
	private BigDecimal residueQuantity=new BigDecimal("-1");
	@ApiModelProperty(value = "排序",hidden = true)
	private Integer sort;

	
}
