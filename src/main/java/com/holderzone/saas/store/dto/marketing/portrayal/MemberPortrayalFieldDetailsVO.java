package com.holderzone.saas.store.dto.marketing.portrayal;

import com.holderzone.saas.store.enums.marketing.portrayal.FieldTypeEnum;
import com.holderzone.saas.store.enums.marketing.portrayal.MemberPortrayalFieldEnum;
import com.holderzone.saas.store.enums.marketing.portrayal.StatisticalPeriodEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/12/23
 * @description 会员画像字段详情
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "会员画像字段详情")
public class MemberPortrayalFieldDetailsVO implements Serializable {

    private static final long serialVersionUID = 3888185296633572056L;

    /**
     * 字段名
     *
     * @see MemberPortrayalFieldEnum
     */
    @ApiModelProperty(value = "字段名")
    private String field;

    /**
     * 字段名称
     *
     * @see MemberPortrayalFieldEnum
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    /**
     * 字段类型 1基础信息 2消费信息 3充值信息
     *
     * @see FieldTypeEnum
     */
    @ApiModelProperty(value = "字段类型 1基础信息 2消费信息 3充值信息")
    private Integer fieldType;

    /**
     * 字段序号
     */
    @ApiModelProperty(value = "字段序号")
    private Integer fieldSort;

    /**
     * 统计周期 1基础信息 2消费信息 3充值信息
     *
     * @see StatisticalPeriodEnum
     */
    @ApiModelProperty(value = "统计周期 1注册至今 2最近一段时间 3今年 4本月")
    private Integer statisticalPeriod;

    /**
     * 最近N天
     */
    @ApiModelProperty(value = "最近N天")
    private Integer recentDays;

    /**
     * 是否选中金额限制 0否 1是
     */
    @ApiModelProperty(value = "是否选中金额限制 0否 1是")
    private Integer isSelectAmountLimit;

    /**
     * 订单金额不足n元不统计
     */
    @ApiModelProperty(value = "订单金额不足n元不统计")
    private BigDecimal amountLimit;

    /**
     * 自定义人群
     */
    @ApiModelProperty(value = "自定义人群")
    private List<CustomizeLabelDetails> customizeLabelList;

}
