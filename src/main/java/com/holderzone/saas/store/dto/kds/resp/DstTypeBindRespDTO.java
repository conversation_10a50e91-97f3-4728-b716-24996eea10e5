package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class DstTypeBindRespDTO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    @ApiModelProperty(value = "分类Guid")
    private String typeGuid;

    @ApiModelProperty(value = "分类名称")
    private String typeName;

    @ApiModelProperty(value = "商品列表")
    List<DstItemBindRespDTO> itemList;
}
