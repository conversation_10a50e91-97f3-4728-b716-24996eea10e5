package com.holderzone.saas.store.dto.order;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.request.TakeAwayBillPayCreateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderQuery
 * @date 2018-07-24 17:28:39
 * @description
 * @program holder-saas-store-order
 */
@Data
@NoArgsConstructor
@ApiModel
public class OrderCreateDTO extends BaseDTO {

    private static final long serialVersionUID = -5172096053497254811L;

    @ApiModelProperty(value = "订单的guid", hidden = true, required = true)
    private String orderGuid;

    @ApiModelProperty(value = "原订单guid")
    private String originalOrderGuid;

    @ApiModelProperty(value = "原反结账uuid")
    private String recoveryUuid;

    @ApiModelProperty(value = "反结账原因")
    private String recoveryReason;

    @ApiModelProperty(value = "账单guid")
    private String billGuid;

    @ApiModelProperty(value = "外卖订单的guid")
    private String takeawayOrderGuid;

    @ApiModelProperty(value = "挂单redisKey")
    private String hangOrderKey;

//    @ApiModelProperty(value = "门店guid", required = true)
//    @NotBlank(message = "门店guid不能为空")
//    private String storeGuid;
//
//    @ApiModelProperty(value = "门店名称", required = true)
//    @NotBlank(message = "门店名称不能为空")
//    private String storeName;

    @ApiModelProperty(value = "操作员guid", required = true)
//    @NotBlank(message = "操作员guid不能为空")
    private String createStaffGuid;

    @ApiModelProperty(value = "操作员名字", required = true)
//    @NotBlank(message = "操作员名字不能为空")
    private String createStaffName;

    @ApiModelProperty(value = "会员guid（也可以返回结算结果时更新）")
    private String memberGuid;

    @ApiModelProperty(value = "会员电话")
    private String memberPhone;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "牌号")
    private String mark;

    @ApiModelProperty(value = "是否自动牌号（0:否，1：是）")
    private Integer autoMark;

    @ApiModelProperty(value = "附加费")
    private BigDecimal surcharge;

    @ApiModelProperty(value = "订单价格")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "桌台guid（仅堂食）")
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台名字（仅堂食）")
    private String diningTableName;

    @ApiModelProperty(value = "交易模式：0:堂食,1:快餐,2:外卖", required = true)
    private int tradeMode;

    @ApiModelProperty(value = "交易模式：0:堂食,1:快餐,2:外卖")
    private String tradeModeName;

//    @ApiModelProperty(value = "订单来源：一体机（1）、POS设备（2）、M1（3）、平板点餐（4）、微信公众号（5）、美团外卖（6）饿了么外卖（7）", required = true)
//    private Integer orderSource;

    @ApiModelProperty(value = "订单来源前缀")
    private String preOrderSource;

    @ApiModelProperty(value = "就餐人数（仅堂食）")
    private int guestCount;

    @ApiModelProperty(value = "流水号", hidden = true, required = true)
    private String serialnumber;

//    @ApiModelProperty(value = "营业日", required = true)
//    @NotNull(message = "营业日不能为空")
//    private LocalDate businessDayStamp;

    @ApiModelProperty(value = "班次")
    private String shiftTime;

//    @ApiModelProperty(value = "设备型号", required = true)
//    private String deviceType;

    @ApiModelProperty(value = "外卖账单信息")
    private TakeAwayBillPayCreateDTO takeAwayBillPayCreateDTO;

    @ApiModelProperty(value = "菜品guid")
    private List<OrderDish> orderDishes;

    @Data
    public static class OrderDish implements Serializable{

        private static final long serialVersionUID = 3160900632055331648L;

        /**
         * 订单菜品guid
         */
        private String orderDishGuid;

        /**
         * 是否新建
         */
        private Boolean isAdd;

        /**
         * 菜品guid
         */
        private String dishGuid;

        @ApiModelProperty(value = "主菜品业务id")
        private String appMainDishId;

        /**
         * 菜品名称
         */
        private String dishName;

        @ApiModelProperty(value = "菜品类型guid")
        private String dishTypeGuid;

        @ApiModelProperty(value = "菜品类型名称")
        private String dishTypeName;

        /**
         * 菜品编号
         */
        private String code;

        @ApiModelProperty(value = "是否已经下单")
        private Integer isPay;

        /**
         * 菜品名称
         */
        private String takeawayGroupDish;

        /**
         * 套餐分组guid
         */
        private String subgroupGuid;

        /**
         * 套餐分组名字
         */
        private String subgroupName;

        /**
         * 套餐主项guid
         */
        private String parentDishGuid;

        /**
         * 菜品的套餐类型(0：单规格、1：多规格、2：套餐主项、3：套餐子项、4：称重菜品 )
         */
        private Byte packageDishType;

        /**
         * 菜品的规格
         */
        private String skuGuid;

        /**
         * 规格名称
         */
        private String skuName;

        @ApiModelProperty(value = "sku价格")
        private BigDecimal price;

        @ApiModelProperty(value = "会员价格")
        private BigDecimal memberPrice;

        @ApiModelProperty(value = "数量")
        private BigDecimal orderCount;

        @ApiModelProperty(value = "套餐预设数量")
        private Integer packageDefaultCount;

        @ApiModelProperty(value = "退菜数量")
        private Integer returnCount;

        @ApiModelProperty(value = "退菜原因")
        private String returnReason;

        @ApiModelProperty(value = "是否赠送(0:非赠送、1：赠送)")
        private Byte isGift;

        /**
         * 计数单位
         */
        private String unit;

        /**
         * 计数单位code(DishUnitEnum)
         */
        private Integer unitCode;

        /**
         * 属性（加料）（只给外卖有描述没有价格）
         */
        private String skuPropertys;

        /**
         * 属性总价
         */
        private BigDecimal skuPropertyTotal;

        /**
         * 是否参与会员权益折扣
         */
        private byte joinMemberDiscount;

        /**
         * 是否参与整单折扣
         */
        private byte joinWholeDiscount;

        /**
         * 菜品备注
         */
        private String remark;

        private List<OrderDish> orderDishes;

        private List<SkuProperty> skuProperties;
    }

    @Data
    public static class SkuProperty implements Serializable{

        private static final long serialVersionUID = 8652620618040482858L;

        @ApiModelProperty(value = "订单属性guid")
        private String skuPropertyGuid;

        /**
         * 属性guid
         */
        private String propertyGuid;

        /**
         * 属性名称
         */
        private String propertyName;

        /**
         * 属性组名称
         */
        private String propertyGroupName;

        /**
         * 属性价格
         */
        private BigDecimal propertyPrice;

        /**
         * 属性数量
         */
        private Integer num;
    }

}
