package com.holderzone.saas.store.dto.member.activity;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.enums.GroupBuyTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum ThirdActivityTypeEnum {

    /**
     * 美团
     */
    MT("MT", "美团团购", 1),

    /**
     * 大众点评
     */
    DZ("DZ", "大众点评", 4),

    /**
     * 抖音
     */
    DY("DY", "抖音团购", 2),

    /**
     * 赚餐
     */
    ZC("ZC", "赚餐", 5),

    /**
     * 支付宝
     */
    ZF("ZF", "支付宝", 3),

    /**
     * 聚卡慧
     */
    ABC("ABC", "农行团购", 7),

    /**
     * 其他
     */
    OTHER("OTHER", "其他", 6);

    private final String thirdType;

    private final String thirdName;

    private final Integer sort;

    public static boolean dockCompleted(String type) {
        ThirdActivityTypeEnum typeEnum = ThirdActivityTypeEnum.getNameByType(type);
        return DOCK_COMPLETED_LIST.contains(typeEnum);
    }

    public final static List<ThirdActivityTypeEnum> DOCK_COMPLETED_LIST = Lists.newArrayList(MT, DY, ZF,ABC);

    public static ThirdActivityTypeEnum getNameByType(String thirdType) {
        return Arrays.stream(ThirdActivityTypeEnum.values()).filter(p -> p.getThirdType().equals(thirdType))
                .findFirst().orElseThrow(() -> new BusinessException("不存在当前活动类型"));
    }


    public static String transferThirdType(Integer groupType) {
        GroupBuyTypeEnum groupBuyTypeEnum = GroupBuyTypeEnum.groupBuyType(groupType);
        switch (groupBuyTypeEnum) {
            case MEI_TUAN:
                return ThirdActivityTypeEnum.MT.getThirdType();
            case DOU_YIN:
                return ThirdActivityTypeEnum.DY.getThirdType();
            case DA_ZHONG_DIAN_PIN:
                return ThirdActivityTypeEnum.DZ.getThirdType();
            case ZHUAN_CAN:
                return ThirdActivityTypeEnum.ZC.getThirdType();
            case ALIPAY:
                return ThirdActivityTypeEnum.ZF.getThirdType();
            case ABC:
                return ThirdActivityTypeEnum.ABC.getThirdType();
            default:
                return ThirdActivityTypeEnum.OTHER.getThirdType();
        }
    }


    public static Integer transferGroupBuyType(String thirdType) {
        ThirdActivityTypeEnum nameByType = ThirdActivityTypeEnum.getNameByType(thirdType);
        switch (nameByType) {
            case MT:
                return GroupBuyTypeEnum.MEI_TUAN.getCode();
            case DY:
                return GroupBuyTypeEnum.DOU_YIN.getCode();
            case DZ:
                return GroupBuyTypeEnum.DA_ZHONG_DIAN_PIN.getCode();
            case ZC:
                return GroupBuyTypeEnum.ZHUAN_CAN.getCode();
            case ZF:
                return GroupBuyTypeEnum.ALIPAY.getCode();
            case ABC:
                return GroupBuyTypeEnum.ABC.getCode();
            case OTHER:
                return GroupBuyTypeEnum.OTHER.getCode();
            default:
                return null;
        }
    }

}
