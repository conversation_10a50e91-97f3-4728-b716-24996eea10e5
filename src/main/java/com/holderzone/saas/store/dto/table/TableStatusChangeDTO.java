package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableCheckOutDTO
 * @date 2019/01/08 9:17
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class TableStatusChangeDTO extends BaseDTO {

    @ApiModelProperty("订单号")
    private String orderGuid;

    @ApiModelProperty("桌台guid")
    private String tableGuid;

    @ApiModelProperty("桌台状态变化枚举")
    private Integer tableStatusChange;

    @ApiModelProperty("是否正餐手动清台")
    private Integer enableManualClear;

    @ApiModelProperty("是否结清")
    private Boolean checkoutSuccessFlag;
}
