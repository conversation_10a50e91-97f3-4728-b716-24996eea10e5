package com.holderzone.saas.store.dto.kds.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class KitchenItemDTO implements Serializable {

    private static final long serialVersionUID = -4265897662401930888L;

    @NotBlank(message = "唯一标识不能为空")
    @ApiModelProperty(value = "唯一标识", required = true)
    private String guid;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @Nullable
    @ApiModelProperty(value = "制作点堂口Guid")
    private String pointGuid;

    @NotBlank(message = "制作点堂口设备ID不能为空", groups = Prd.class)
    @ApiModelProperty(value = "制作点堂口设备ID", required = true)
    private String prdDeviceId;

    @NotBlank(message = "出堂口设备ID不能为空", groups = Dst.class)
    @ApiModelProperty(value = "出堂口设备ID", required = true)
    private String dstDeviceId;

    @Nullable
    @ApiModelProperty(value = "屏幕显示分类\n" +
            "0b00001111\n" +
            "低0位 外卖 1\n" +
            "低1位 快餐 2\n" +
            "低2位 正餐 4\n" +
            "低3位 全部 8\n" +
            "<p>\n" +
            "tradeMode\n" +
            "0：正餐\n" +
            "1：快餐\n" +
            "<p>\n" +
            "映射关系如下\n" +
            "tradeMode 0 -> displayType 4\n" +
            "tradeMode 1 -> displayType 2\n" +
            "tradeMode 2 -> displayType 1")
    private Integer displayType;

    @NotBlank(message = "订单Guid不能为空")
    @ApiModelProperty(value = "订单Guid\n" +
            "并台情况下，订单拆单显示", required = true)
    private String orderGuid;

    @NotBlank(message = "订单描述不能为空")
    @ApiModelProperty(value = "正餐：正餐\n" +
            "快餐：快餐\n" +
            "外卖：饿了么|美团", required = true)
    private String orderDesc;

    @NotBlank(message = "订单号不能为空")
    @ApiModelProperty(value = "正餐：订单号\n" +
            "快餐：订单号\n" +
            "外卖：订单号", required = true)
    private String orderNumber;

    @NotBlank(message = "订单流水号不能为空")
    @ApiModelProperty(value = "正餐：桌台号\n" +
            "快餐：牌号\n" +
            "外卖：日流水号", required = true)
    private String orderSerialNo;

    @Nullable
    @ApiModelProperty(value = "整单备注")
    private String orderRemark;

    @NotBlank(message = "区域Guid不能为空")
    @ApiModelProperty(value = "区域Guid", required = true)
    private String areaGuid;

    @Nullable
    @ApiModelProperty(value = "桌台Guid")
    private String tableGuid;

    @Nullable
    @ApiModelProperty(value = "订单下商品唯一Guid，\n" +
            "不同于guid\n" +
            "不同于itemGuid\n" +
            "该字段标识唯一的一次菜品(批量)操作\n" +
            "<p>\n" +
            "比如：白菜x10，则\n" +
            "guid表示这个1份的标识\n" +
            "itemGuid表示这个item类型的标识\n" +
            "orderItemGuid表示这个10份的标识")
    private String orderItemGuid;

    @NotBlank(message = "商品Guid不能为空")
    @ApiModelProperty(value = "商品Guid", required = true)
    private String itemGuid;

    @NotBlank(message = "商品名称不能为空")
    @ApiModelProperty(value = "商品名称", required = true)
    private String itemName;

    @NotBlank(message = "skuGuid不能为空")
    @ApiModelProperty(value = "skuGuid", required = true)
    private String skuGuid;

    @Nullable
    @ApiModelProperty(value = "sku名称")
    private String skuName;

    @Nullable
    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @Nullable
    @ApiModelProperty(value = "sku单位")
    private String skuUnit;

    @NotNull(message = "是否是称重商品不能为空")
    @ApiModelProperty(value = "是否是称重商品", required = true)
    private Boolean isWeight;

    @NotNull(message = "当前商品数量不能为空")
    @ApiModelProperty(value = "当前商品数量\n" +
            "称重       具体重量\n" +
            "非称重     具体份数", required = true)
    private BigDecimal currentCount;

    @Deprecated
    @ApiModelProperty(value = "已退商品数量")
    private BigDecimal returnCount;

    @NotNull(message = "商品属性MD5不能为空")
    @ApiModelProperty(value = "商品属性MD5，ItemAttrMd5BO转Json转Md5", required = true)
    private String itemAttrMd5;

    @Nullable
    @ApiModelProperty(value = "菜品备注")
    private String itemRemark;

    @Nullable
    @ApiModelProperty(value = "超时阈值\n" +
            "单位：分钟")
    private Integer timeout;

    @Deprecated
    @ApiModelProperty(value = "催促次数")
    private Integer urgedTimes;

    @NotNull(message = "商品状态不能为空")
    @ApiModelProperty(value = "商品状态\n" +
            "1.即起\n" +
            "2.挂起\n" +
            "3.叫起\n" +
            "8.已上菜(已划菜)\n" +
            "9.预定\n" +
            "99.退菜", required = true)
    private Integer itemState;

    @NotNull(message = "厨房商品状态不能为空")
    @ApiModelProperty(value = "厨房商品状态\n" +
            "4.待制作\n" +
            "5.制作中\n" +
            "6.待出堂\n" +
            "7.已出堂", required = true)
    private Integer kitchenState;

    @Nullable
    @ApiModelProperty(value = "准备时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime prepareTime;

    @Nullable
    @ApiModelProperty(value = "挂起时间（同准备时间，区别是item进厨房时状态是挂起）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime hangUpTime;

    @Nullable
    @ApiModelProperty(value = "叫起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime callUpTime;

    @Nullable
    @ApiModelProperty(value = "催菜时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime urgedTime;

    @Nullable
    @ApiModelProperty(value = "制作人Guid")
    private String cookStaffGuid;

    @Nullable
    @ApiModelProperty(value = "制作人姓名")
    private String cookStaffName;

    @Nullable
    @ApiModelProperty(value = "制作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime cookTime;

    @Nullable
    @ApiModelProperty(value = "完成人Guid")
    private String completeStaffGuid;

    @Nullable
    @ApiModelProperty(value = "完成人姓名")
    private String completeStaffName;

    @Nullable
    @ApiModelProperty(value = "完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime completeTime;

    @Nullable
    @ApiModelProperty(value = "出堂人Guid")
    private String distributeStaffGuid;

    @Nullable
    @ApiModelProperty(value = "出堂人姓名")
    private String distributeStaffName;

    @Nullable
    @ApiModelProperty(value = "出堂时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime distributeTime;

    @Nullable
    @ApiModelProperty(value = "撤销出堂人Guid")
    private String cancelDstStaffGuid;

    @Nullable
    @ApiModelProperty(value = "撤销出堂人姓名")
    private String cancelDstStaffName;

    @Nullable
    @ApiModelProperty(value = "撤销出堂时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime cancelDstTime;

    public interface Prd extends Default {
    }

    public interface Dst extends Default {
    }
}
