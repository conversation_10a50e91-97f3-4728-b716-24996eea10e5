package com.holderzone.saas.store.dto.item.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeSingleDTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class TypeSingleDTO extends ItemLogDTO {
    private static final long serialVersionUID = 1727528918478527413L;
    @ApiModelProperty(value = "最主要的业务请求参数")
    @NotEmpty
    private String data;

    @ApiModelProperty(value = "商品分类guidList")
    private List<String> itemTypeList;

    @ApiModelProperty(value = "销售模式 2 菜谱 1 普通")
    private Integer isModel;

    @ApiModelProperty(value = "查询关键字")
    private String keywords;

    @ApiModelProperty(value = "商品查询类型：0 全部类型，1 宴会套餐")
    private Integer itemQueryType;

    /**
     * 规则类型 0显示批次 1菜品汇总
     */
    @ApiModelProperty(value = "规则类型 0显示批次 1菜品汇总")
    private Integer ruleType;

    /**
     * kds显示规则guid
     */
    @ApiModelProperty(value = "显示规则guid")
    private String ruleGuid;

    /**
     * 菜谱方案Guid
     */
    @ApiModelProperty(value = "菜谱方案Guid")
    private String planGuid;

    @ApiModelProperty(value = "商品guidList")
    private List<String> itemList;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty("用户标识")
    private String wxtoken;

    @ApiModelProperty("skuGuidList")
    private List<String> skuGuids;
}
