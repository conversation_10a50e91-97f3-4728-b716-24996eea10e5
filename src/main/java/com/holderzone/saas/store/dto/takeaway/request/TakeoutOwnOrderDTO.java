package com.holderzone.saas.store.dto.takeaway.request;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class TakeoutOwnOrderDTO {

    @ApiModelProperty(value = "门店编号")
    public String ShopNo;

    @ApiModelProperty(value = "第三方订单ID")
    public String OriginId;

    @ApiModelProperty(value = "蜂鸟（1:即时单，3:预约单）  美团（0:即时单，1:预约单） 达达可不填")
    public int OrderType;

    @ApiModelProperty(value = "下单时间")
    public long OrderTime;

    @ApiModelProperty(value = "期望送达时间")
    public long ExpectDeliveryTime;

    @ApiModelProperty(value = "订单金额")
    public BigDecimal CargoPrice;

    @ApiModelProperty(value = "订单总重量（kg）")
    public double OrderWeight;

    @ApiModelProperty(value = "用户备注")
    public String OrderRemark;

    @ApiModelProperty(value = "发票抬头")
    public String InvoiceTitle;

    @ApiModelProperty(value = "是否需要代收费用 1:是 0:否(代收金额，非运费)")
    public int IsPrepay;

    @ApiModelProperty(value = "需要代收 是 代收金额")
    public double RequirePayment;

    @ApiModelProperty(value = "收货人姓名")
    public String ReceiverName;

    @ApiModelProperty(value = "收货人地址")
    public String ReceiverAddress;

    @ApiModelProperty(value = "收货人地址维度")
    public String ReceiverLat;

    @ApiModelProperty(value = "收货人地址经度")
    public String ReceiverLng;

    @ApiModelProperty(value = "蜂鸟 是 收货人经纬度来源（1:腾讯地图, 2:百度地图, 3:高德地图）")
    public int PositionSource;

    @ApiModelProperty(value = "收货人手机号")
    public String ReceiverPhone;

    @ApiModelProperty(value = "收货人座机号")
    public String ReceiverTel;

    @ApiModelProperty(value = "蜂鸟、美团 是 商品原价")
    public double GoodsPrice;

    @ApiModelProperty(value = "商户Code")
    public String StoreCode;

    @ApiModelProperty(value = "运费")
    public double Freight;

    @ApiModelProperty(value = "商户Code")
    public String Flag;

    @ApiModelProperty(value = "商户编号")
    public String SourceId;

    @ApiModelProperty(value = "门店名称")
    public String ShopName;

    @ApiModelProperty(value = "餐盒费")
    public double BoxFee;

    @ApiModelProperty(value = "第三方商家自己订单当日流水号")
    public int DayIndex;

    @ApiModelProperty(value = "第三方商家自己订单当日流水号")
    public String ViewOrderId;

    @ApiModelProperty(value = "活动减免金额")
    public double ActivityMoney;

    @ApiModelProperty(value = "商家负责的活动减免金额")
    public double ShopActivityMoney;

    @ApiModelProperty(value = "预约到达时间（格式：yyyy-MM-dd HH:mm:ss）")
    public String ReachTime;

    @ApiModelProperty(value = "取餐类型：默认传0 (0:普通取餐（骑手送餐）;1:到店自取)")
    public int PickType;

    @ApiModelProperty(value = "开票类型0不开1个人2企业")
    public int InvoiceType;

    @ApiModelProperty(value = "InvoiceType为2必传 企业税号")
    public String TaxpayerId;

    @ApiModelProperty(value = "商品")
    private List<FoodListDTO> foodList;

}
