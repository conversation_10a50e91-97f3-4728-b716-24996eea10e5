package com.holderzone.saas.store.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel(value = "门店收入排名")
@NoArgsConstructor
@AllArgsConstructor
public class IndexStoreSortDTO {
    @ApiModelProperty(value = "门店名称")
    private String storeName;
    @ApiModelProperty(value = "营业总收入（元）")
    private double turnover;
    @ApiModelProperty(value = "排序")
    private int sort;

    public IndexStoreSortDTO(String storeName, double turnover) {
        this.storeName = storeName;
        this.turnover = turnover;
    }
}
