package com.holderzone.saas.store.dto.report;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class IndexQuery {
    @ApiModelProperty(value = "0:营业日 1:自然日")
    private Integer timeType = 1;
    @ApiModelProperty(value = "门店guid")
    protected List<String> storeGuids;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startTime;
    @ApiModelProperty(value = "结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "企业guid")
    protected String enterpriseGuid;

    @ApiModelProperty(value = "当前页数")
    private int currentPage = 1;
    @ApiModelProperty(value = "每页显示条数")
    private int pageSize = 10;
    @ApiModelProperty(value = "排序，0：降序 1：升序")
    private int sort = 0;

}
