package com.holderzone.saas.store.dto.report.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 估清报表返回DTO
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "估清报表返回", description = "估清报表返回")
public class EstimateReportRespDTO {

    @ApiModelProperty("估清guid")
    private String guid;

    @ApiModelProperty(value = "品牌")
    private String brandName;

    @ApiModelProperty("估清类型 1：售完下架 2：长期估清")
    private Integer estimateType;

    @ApiModelProperty(value = "估清时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime estimateTime;

    @ApiModelProperty(value = "估清操作员")
    private String estimateOperatorName;

    @ApiModelProperty(value = "估清设备类型")
    private String estimateDeviceType;

    @ApiModelProperty("解估类型 1：未解估 2：自动解估 3：手动解估")
    private Integer cancelEstimateType;

    @ApiModelProperty(value = "解估时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime cancelEstimateTime;

    @ApiModelProperty(value = "解估操作员")
    private String cancelEstimateOperatorName;

    @ApiModelProperty(value = "解估设备类型")
    private String cancelEstimateDeviceType;

    @ApiModelProperty(value = "估清时长 解估时间-估清时间，天/时/分")
    private String estimatedDuration;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "操作类型")
    private String opType;

    @ApiModelProperty(value = "门店")
    private String storeName;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime opTime;

    @ApiModelProperty(value = "操作日志操作员")
    private String opLogOperatorName;

    @ApiModelProperty(value = "操作日志设备类型")
    private Integer opLogDeviceType;
}
