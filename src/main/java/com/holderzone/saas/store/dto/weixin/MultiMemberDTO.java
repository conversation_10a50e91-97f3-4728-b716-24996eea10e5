package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/8/4 9:48
 * @description
 */
@Data
public class MultiMemberDTO {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("关联企业id")
    private String enterpriseGuid;
    @ApiModelProperty("多会员体系的guid")
    private String multiMemberGuid;
    @ApiModelProperty("多会员体系的名称")
    private String multiMemberName;
    @ApiModelProperty("是否可用,true为可用")
    private Boolean enabled;
}
