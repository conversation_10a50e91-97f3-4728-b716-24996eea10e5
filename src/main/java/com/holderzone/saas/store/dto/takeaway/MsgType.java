package com.holderzone.saas.store.dto.takeaway;

/**
 * fixme 命名体现出enum
 */
public enum MsgType {

    MT_BIND_SET("1003", ""),

    MT_TUAN_GOU_BIND_SET("1004", ""),

    NEW_ORDER("1010", "新订单"),

    STATUS_CHANGE("1110", "更改状态"),

    REMIND_ORDER("1020", "提醒订单"),

    CANCEL_ORDER("1030", "取消订单"),

    REJECT_REFUND("1031", "拒绝取消订单"),

    REJECT_ORDER("2010", "拒单"),

    CONFIRM_ORDER("2011", "确认接单"),

    REPLY_REMIND_ORDER("2020", "回复提醒订单"),

    REJECT_CANCEL_ORDER("2030", "不同意退单"),

    CONFIRM_CANCEL_ORDER("2031", "同意退单"),

    CANCEL_ORDER_DTO("2041", "DTO退单"),

    QUERY_DISH("3010", "查询菜品"),

    UNKNOWN("4000", ""),

    MT_MEMBER_CARD("15201", "会员卡消息"),

    MT_MEMBER_CONSUMPTION("15202", "会员卡积分"),

    MT_MEMBER_NEW("15101", "会员联名卡查询新客"),

    ;

    /**
     * 类型
     */
    private String type;

    /**
     * 描述
     */
    private String desc;

    MsgType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public static MsgType ofType(String type) {
        for (MsgType msgType : values()) {
            if (msgType.type.equalsIgnoreCase(type)) {
                return msgType;
            }
        }
        throw new IllegalArgumentException("无效的MsgType[" + type + "]");
    }
}
