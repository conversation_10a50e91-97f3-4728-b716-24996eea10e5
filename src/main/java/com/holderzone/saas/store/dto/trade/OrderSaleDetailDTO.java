package com.holderzone.saas.store.dto.trade;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/6/1 13:48
 * @description 订单详情（report销售明细）
 */
@Data
public class OrderSaleDetailDTO {

    /**
     * 订单guid
     */
    private Long guid;

    /**     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 订单号(前端显示用，门店内唯一，格式************)
     */
    private String orderNo;

    /**
     * 交易模式(0：正餐，1：快餐)
     */
    private Integer tradeMode;

    /**
     * 设备类型(订单来源BaseDeviceTypeEnum)
     */
    private Integer deviceType;

    /**
     * 客人数
     */
    private Integer guestCount;

    /**
     * 营业日
     */
    private LocalDate businessDay;

    /**
     * 桌台guid
     */
    private String diningTableGuid;

    /**
     * 桌台名称(区域+桌台code)
     */
    private String diningTableName;

    /**
     * 是否虚拟台(0:否，1:是)
     */
    private Integer virtualTable;

    /**
     * 作废原因
     */
    private String cancelReason;

    /**
     * 整单备注
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String remark;

    /**
     * 快餐牌号
     */
    private String mark;

    /**
     * 预结单打印次数
     */
    private Integer printPreBillNum;

    /**
     * 订单金额（商品总额+附加费）
     */
    private BigDecimal orderFee;

    /**
     * 附加费
     */
    private BigDecimal appendFee;

    /**
     * 找零（收款-应收金额）
     */
    private BigDecimal changeFee;

    /**
     * 实收金额=订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））
     * 应收金额=订单金额-优惠金额-订金(押金)
     */
    private BigDecimal actuallyPayFee;

    /**
     * 预付金（反结账原单聚合支付转入）
     */
    private BigDecimal prepayFee;

    /**
     * 定金
     */
    private BigDecimal reserveFee;

    /**
     * 1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废
     */
    private Integer state;
    /**
     * 订单反结账类型1：普通单 2：原单 3：新单 4：退单
     */
    private Integer recoveryType;

    /**
     * 0:无并单，1:主单， 2:子单
     */
    private Integer upperState;

    /**
     * 主单guid
     */
    private Long mainOrderGuid;

    /**
     * 预定guid
     */
    private String reserveGuid;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 会员卡guid
     */
    private String memberCardGuid;

    /**
     * 会员支付id，反结账传给会员系统
     */
    private String memberConsumptionGuid;

    /**
     * 会员电话
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String memberPhone;

    /**
     * 会员名字
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String memberName;

    /**
     * 用户微信公众号openId
     */
    private String userWxPublicOpenId;

    /**
     * 反结账原单的guid
     */
    private Long originalOrderGuid;

    /**
     * 反结账id(多次反结账所有原单和新单此id相同)
     */
    private Long recoveryId;

    /**
     * 计算是是否使用会员价（1：使用，0：不使用）
     */
    private Integer calculateByMemberPrice;
    /**
     * 反结账原因
     */
    private String recoveryReason;

    /**
     * 反结账设备类型（BaseDeviceTypeEnum）
     */
    private Integer recoveryDeviceType;

    /**
     * 结账设备类型（BaseDeviceTypeEnum）
     */
    private Integer checkoutDeviceType;

    /**
     * 取消订单设备类型（BaseDeviceTypeEnum）
     */
    private Integer cancelDeviceType;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 开台时间
     */
    private LocalDateTime checkinTime;

    /**
     * 结算时间
     */
    private LocalDateTime checkoutTime;

    /**
     * 作废时间
     */
    private LocalDateTime cancelTime;

    /**
     * 创建操作人guid
     */
    private String createStaffGuid;

    /**
     * 创建操作人name
     */
    private String createStaffName;

    /**
     * 反结账操作人guid
     */
    private String recoveryStaffGuid;

    /**
     * 反结账操作人name
     */
    private String recoveryStaffName;

    /**
     * 结账操作人guid
     */
    private String checkoutStaffGuid;

    /**
     * 结账操作人name
     */
    private String checkoutStaffName;

    /**
     * 作废订单操作人guid
     */
    private String cancelStaffGuid;

    /**
     * 作废订单操作人name
     */
    private String cancelStaffName;

}
