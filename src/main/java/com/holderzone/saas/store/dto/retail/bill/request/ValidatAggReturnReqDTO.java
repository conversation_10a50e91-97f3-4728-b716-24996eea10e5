package com.holderzone.saas.store.dto.retail.bill.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className JhQueryReqDTO
 * @date 2018/08/14 11:45
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class ValidatAggReturnReqDTO extends BaseDTO {


    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal amount;


}