package com.holderzone.saas.store.dto.report.resp;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DebtTotalStatisticsDTO {

    @ApiModelProperty(value = "挂账金额")
    public BigDecimal debtAmount;

    @ApiModelProperty(value = "还款金额")
    public BigDecimal repaymentAmount;

    @ApiModelProperty(value = "未还款金额")
    public BigDecimal unRepaymentAmount;

    /**
     * 总条数
     */
    private int total;

    public static DebtTotalStatisticsDTO buildEmpty() {
        return new DebtTotalStatisticsDTO(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, 0);
    }

}
