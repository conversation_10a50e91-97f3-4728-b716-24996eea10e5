package com.holderzone.saas.store.dto.trade.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description pad重新绑定返回实体
 * @date 2021/9/24 11:08
 * @className: PadRebindRespDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "pad重新绑定返回实体")
public class PadRebindRespDTO implements Serializable {

    private static final long serialVersionUID = 7407629043226735843L;

    /**
     * 就餐人数
     */
    @ApiModelProperty("就餐人数")
    private Integer actualGuestsNo;

    /**
     * 交易订单号
     */
    @ApiModelProperty("订单guid")
    private String orderGuid;
}
