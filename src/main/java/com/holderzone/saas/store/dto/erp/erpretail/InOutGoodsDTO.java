package com.holderzone.saas.store.dto.erp.erpretail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

@Data
@ApiModel("出入库商品明细")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InOutGoodsDTO {

    @ApiModelProperty("商品Guid")
    @NotEmpty(message = "商品Guid不得为空")
    private String goodsGuid;

    @ApiModelProperty("商品Code")
    private String goodsCode;

    @ApiModelProperty("商品名称")
    @NotEmpty(message = "商品名称不得为空")
    private String goodsName;

    @ApiModelProperty("商品条码")
    private String barCode;

    @ApiModelProperty("拼音简码")
    private String pinyin;

    @ApiModelProperty("商品图片地址")
    private String goodsImage;

    @ApiModelProperty("安全库存")
    private BigDecimal safeNum;

    @ApiModelProperty("出入库数量")
    @NotEmpty(message = "出入库数量不得为空")
    private BigDecimal count;

    @ApiModelProperty("单位Guid")
    private String unitGuid;

    @ApiModelProperty("单位名称")
    @NotEmpty(message = "单位名称不得为空")
    private String unitName;

    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    @ApiModelProperty("总价")
    private BigDecimal totalAmount;

    @ApiModelProperty("商品所属分类guid")
    @NotEmpty(message = "商品所属分类guid不得为空")
    private String goodsClassifyGuid;

    @ApiModelProperty("商品所属分类名称")
    @NotEmpty(message = "商品所属分类名称不得为空")
    private String goodsClassifyName;

    @ApiModelProperty("是否开启库存，0：否，1：是")
    @NotEmpty(message = "是否开启库存不能为空")
    private Integer isOpenStock;

    @ApiModelProperty("1.套餐（不称重，无规格），2.规格商品（不称重），3.称重商品（单商品，称重） 4:单品\")")
    private int itemType;

}
