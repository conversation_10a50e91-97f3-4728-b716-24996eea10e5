package com.holderzone.saas.store.dto.item.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishPkgSubgroupDTO
 * @date 2018/07/25 下午6:39
 * @description //快餐相关DTO
 * @program holder-saas-store-dish
 */
@Data
@ApiModel(value = "套餐分组向外传输对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DishPkgSubgroupRespDTO extends BaseDTO {
    private static final long serialVersionUID = -1414592911317322300L;

    @ApiModelProperty("套餐分组的唯一标识")
    private String subgroupGuid;
    @ApiModelProperty(value = "分组NAME")
    private String name;

    @ApiModelProperty(value = "当前分组中的菜品的可选择数量（菜品可重复被选）：0：顾客不可选择，其余值：最大可选菜品次数")
    private Integer pickNum;

    @ApiModelProperty(value = "套餐分组中包含的可选规格集合")
    private List<DishPkgSubgroupSkuRespDTO> dishList;

}
