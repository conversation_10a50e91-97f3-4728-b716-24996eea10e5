package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreAuthByTypeReqDTO
 * @date 2018/09/25 9:08
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StoreAuthByTypeReqDTO extends BaseDTO {

    @ApiModelProperty(value = "外卖类型(外卖类型 1：美团，2：饿了么)")
    private Integer takeoutType;

    @ApiModelProperty(value = "绑定状态(0：未绑定，1:绑定，2：全部)")
    private Integer bindingStatus;

    @ApiModelProperty(value = "查询字段")
    private String queryString;
}
