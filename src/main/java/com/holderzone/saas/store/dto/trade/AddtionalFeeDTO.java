package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AddtionalFeeDTO
 * @date 2018/08/01 15:09
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class AddtionalFeeDTO extends BaseBillDTO {

    /**
     * 业务主键
     * 第一次请求不用传，以后折扣，附加费相关必须传
     */
    @ApiModelProperty(value = "业务主键")
    private String billAddtionalGuid;

    /**
     * 该附加费配置的guid
     */
    @ApiModelProperty(value = "附加费guid")
    private String addtionalGuid;

    /**
     * 附加费配置名
     */
    @ApiModelProperty(value = "附加费name")
    private String addtionalCostName;

    /**
     * 附加费费用值
     */
    @ApiModelProperty(value = "附加费值")
    private BigDecimal addtionalCostFee;

    /**
     * 该附加费数量
     */
    @ApiModelProperty(value = "附加费数量")
    private Integer addtionalCount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
