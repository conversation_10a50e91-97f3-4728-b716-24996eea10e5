package com.holderzone.saas.store.dto.queue;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueListDTO
 * @date 2019/09/19 10:49
 * @description 微信排队记录列表DTO
 * @program holder-saas-store
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WxQueueListDTO {

    @ApiModelProperty("排队guid")
    private String guid;

    @ApiModelProperty("用户id,这里是openId")
    private String userGuid;

    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("门店名字")
    private String storeName;

    @ApiModelProperty("品牌名字")
    private String brandName;

    @ApiModelProperty("品牌guid")
    private String brandGuid;

    @ApiModelProperty("周几")
    private String weekDate;

    @ApiModelProperty(value = "日期")
    private LocalDate date;

    @ApiModelProperty(value = "时间  格式：HH:mm:ss")
    private String time;

    @ApiModelProperty("状态 0:队列中,1:过号,2:叫号中,3:已就餐,4:已取消")
    private Byte status;

    @ApiModelProperty("排队取消时间，只有状态为4的时候有值")
    private LocalDateTime cancelTime;

    public void initDate(LocalDateTime dateTime) {
        this.date = dateTime.toLocalDate();
        this.time = dateTime.toLocalTime().toString();
        this.weekDate = WeekEnum.getDescByCode(this.date.getDayOfWeek().getValue());
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    private enum WeekEnum {
        MONDAY(1, "周一"),
        TUESDAY(2, "周二"),
        WEDNESDAY(3, "周三"),
        THURSDAY(4, "周四"),
        FRIDAY(5, "周五"),
        SATURDAY(6, "周六"),
        SUNDAY(7, "周日"),
        DEFAULT(0, "未知");

        private int code;

        private String desc;

        public static String getDescByCode(int code) {
            return Arrays.asList(WeekEnum.values()).stream().filter(e -> Objects.equals(e.getCode(), code)).findFirst().orElse(DEFAULT).getDesc();
        }
    }

}
