package com.holderzone.saas.store.dto.business.brand;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 品牌配置查询DTO
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "品牌配置查询DTO")
public class BrandConfigQueryDTO extends BasePageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @ApiModelProperty(value = "全局唯一主键")
    private Long guid;

    /**
     * 品牌guid
     */
    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;
}
