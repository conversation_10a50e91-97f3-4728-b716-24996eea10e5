package com.holderzone.saas.store.dto.order;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderUpdateDTO
 * @date 2018/08/02 下午 17:36
 * @description
 * @program holder-saas-store-dto
 */
@Data
@NoArgsConstructor
public class OrderUpdateDTO extends BaseDTO{

    /**
     * 业务主键
     */
    private String orderGuid;

    /**
     * 反结账uuid
     */
    private String recoveryUuid;

    /**
     * 反结账原因
     */
    private String recoveryReason;

    /**
     * 作废原因
     */
    private String invalidReason;

    /**
     * 整单备注
     */
    private String remark;

    /**
     * 牌号
     */
    private String mark;

    /**
     * 附加费
     */
    private BigDecimal surcharge;

    /**
     * 反结账设备类型
     */
    private Integer recoveryDeviceType;

    /**
     * 0:无子单，1:主单， 2:子单
     */
    private Byte upperState;

    /**
     * 主单guid
     */
    private String mainOrderGuid;

    /**
     * 账单guid
     */
    private String fundBillGuid;

    /**
     * 账单流水号
     */
    private String billSequenceNo;

    /**
     * 状态(0：未完成， 1：已完成， 2：已作废 ，3：已退款，4：反结账状态)
     */
    private Integer state;

    /**
     * 客人数
     */
    private Integer guestCount;

    /**
     * 更新操作人guid
     */
    private String updateStaffGuid;

    /**
     * 更新操作人name
     */
    private String updateStaffName;

    /**
     * 取消订单操作人guid
     */
    private String cancelStaffGuid;

    /**
     * 取消订单操作人guid
     */
    private String cancelStaffName;

    /**
     * 取消时间
     */
    private LocalDateTime cancelTimestamp;
}
