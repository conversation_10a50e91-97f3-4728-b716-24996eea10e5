package com.holderzone.saas.store.dto.order.response.groupon;

import com.holderzone.saas.store.dto.takeaway.response.MtMaitonConsumeDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2023-06-21
 * @description 团购验券返回信息
 */
@Data
public class GroupVerifyDTO implements Serializable {

    private static final long serialVersionUID = 4843054068064941454L;

    /**
     * 团购类型 GroupBuyTypeEnum
     */
    private Integer groupBuyType;

    /**
     * 验券成功得券码
     */
    private String code;

    /**
     * 代表券码一次核销的标识（抖音撤销时需要）
     */
    private String verifyId;

    /**
     * 代表一张券码的标识（抖音撤销时需要）
     */
    private String certificateId;

    private String erpId;

    private String erpName;

    private String storeGuid;

    /**
     * 第三方erp订单号
     */
    private String erpOrderId;

    /**
     * 凭证归属支付宝用户id
     */
    private String userId;

    /**
     * 券码请求code
     */
    private String verifyCode;

    /**
     * 券码渠道 买单:1004
     */
    private Integer receiptChannel;

    /**
     * 一键买单券信息
     */
    private MtMaitonConsumeDTO maitonConsumeDTO;

}
