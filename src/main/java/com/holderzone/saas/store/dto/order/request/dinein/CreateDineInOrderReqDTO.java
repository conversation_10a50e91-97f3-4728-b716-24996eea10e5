package com.holderzone.saas.store.dto.order.request.dinein;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.table.TablePlaceDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CreateDineInOrderReqDTO
 * @date 2019/01/04 9:50
 * @description 创建堂食订单
 * @program holder-saas-store-dto
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class CreateDineInOrderReqDTO extends BaseDTO {

    private static final long serialVersionUID = -1281286881279843330L;

    @ApiModelProperty(value = "订单的guid")
    @LockField
    @OrderLockField
    private String guid;

    @ApiModelProperty(value = "用户微信公众号openId")
    private String userWxPublicOpenId;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "桌台guid")
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台名字")
    @NotBlank(message = "桌台名字不能为空")
    private String diningTableName;

    @ApiModelProperty(value = "联台桌台guids")
    private List<String> associatedTableGuids;

    @ApiModelProperty(value = "联台桌台guids")
    private List<String> associatedTableNames;

    @ApiModelProperty(value = "桌台区域名称")
    @NotBlank(message = "桌台区域名称不能为空")
    private String areaName;

    @ApiModelProperty(value = "就餐人数")
    private int guestCount;

    @ApiModelProperty(value = "是否打印后厨（0：否，1：是）")
    private int print;

    @ApiModelProperty(value = "商品")
    private List<DineInItemDTO> dineInItemDTOS;

    @ApiModelProperty(value = "AreaGuid")
    private String areaGuid;

    @ApiModelProperty(value = "是否预点餐")
    private boolean reserveOrder;

    @ApiModelProperty(value = "防重复提交，唯一ID")
    private String addGoodsBatchUUID;

    /**
     *  如果满足以下场景时，那么在 batchAddItems(批量添加商品时不受估清影响)
     *  订单-接单：1 （后续可添加）
     */
    private Integer estimateType;

    /**
     * 是否跳过验券
     */
    private Boolean continueCheckGrouponFlag;

    /**
     * 是否联台单
     */
    private Boolean associatedFlag;

    /**
     * 联台单编号
     */
    private String associatedSn;

    @ApiModelProperty(value = "桌台名称列表")
    private List<TablePlaceDTO> tables;

    /**
     * 原订单guid
     */
    private String originalOrderGuid;

}
