package com.holderzone.saas.store.dto.pay;

import com.holderzone.framework.util.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AggPayStatisticsDTO implements Serializable {

    private static final long serialVersionUID = -651886682637480241L;

    @ApiModelProperty("支付记录")
    private Page<AggPayRecordDTO> page;

    @ApiModelProperty("合计总金额")
    private BigDecimal totalAmount;
}
