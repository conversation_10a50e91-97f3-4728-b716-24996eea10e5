package com.holderzone.saas.store.dto.takeaway;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class UnDiscount implements Serializable {

    private static final long serialVersionUID = -1621424324275021203L;

    /**
     * 优惠活动名称
     */
    private String discountName;

    /**
     * 备注说明
     */
    private String discountRemark;

    /**
     * 活动类型
     */
    private int type;

    /**
     * 此优惠活动总金额 enterpriseDiscount+platformDiscount+otherDiscount
     */
    private BigDecimal totalDiscount;

    /**
     * 商家承担的折扣部分
     */
    private BigDecimal enterpriseDiscount;

    /**
     * 外卖平台承担的折扣部分
     */
    private BigDecimal platformDiscount;

    /**
     * 第三方承担的部分
     */
    private BigDecimal otherDiscount;
}
