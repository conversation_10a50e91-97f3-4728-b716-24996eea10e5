package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/16 15:12
 * @description
 */
@Data
@ApiModel("微信第三方用户信息查询请求参数")
public class WxQueryThirdPartUserInfoReqDTO {

    @ApiModelProperty(value = "第三方openId")
    private String openId;

    @ApiModelProperty(value = "手机号")
    private String tel;

    @ApiModelProperty(value = "用户来源：翼惠天下:13")
    private Integer source;

    @ApiModelProperty(value = "密码")
    private String password;
}
