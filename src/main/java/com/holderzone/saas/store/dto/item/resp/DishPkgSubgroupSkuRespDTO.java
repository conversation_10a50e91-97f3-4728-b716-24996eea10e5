package com.holderzone.saas.store.dto.item.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishPkgSubgroupSkuDTO
 * @date 2018/07/31 下午6:09
 * @description //快餐相关DTO
 * @program holder-saas-store-dish
 */
@Data
@NoArgsConstructor
@ApiModel(value = "套餐分组中的菜品")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder(toBuilder = true)
@AllArgsConstructor
public class DishPkgSubgroupSkuRespDTO implements Serializable {
    private static final long serialVersionUID = -5049466584861909332L;

    @ApiModelProperty(value = "规格Guid")
    private String skuGuid;

    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty(value = "售价")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "规格关联的菜品GUID")
    private String dishGuid;

    @ApiModelProperty(value = "规格关联的菜品名称")
    private String dishName;

    @ApiModelProperty(value = "规格关联的菜品编码")
    private String code;

    @ApiModelProperty(value = "规格关联的菜品的分类GUID")
    private String typeGuid;

    @ApiModelProperty(value = "菜品类型(注：套餐子菜品中不包括套餐)：1.套餐（不称重，无规格），2.规格菜品（单菜品，不称重），3.称重菜品（单菜品，称重），4.单品。")
    private Byte dishType;

    @ApiModelProperty(value = "菜品单位")
    private String unit;

    @ApiModelProperty(value = "菜品元素中的数量")
    private BigDecimal dishNum;

    @ApiModelProperty(value = "此菜品是否有属性:0 否 1 是")
    private Byte hasProperty;
}
