package com.holderzone.saas.store.dto.business.reserve;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordDO
 * @date 2018/07/30 下午2:51
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class ReserveRecordOthersDTO {

    /**
     * 门店guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    /**
     * 桌台guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "桌台guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "桌台guid", required = true)
    private String tableGuid;

    /**
     * 营业日日期
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "营业日日期", required = true)
    private LocalDate businessDay;

    /**
     * 预订时段guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "预订时段guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "预订时段guid", required = true)
    private String reservePeriodGuid;
}
