package com.holderzone.saas.store.dto.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * {@link AggPayReserveResultDTO}
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/25 下午2:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AggPayReserveResultDTO {
    private String attachData;

    private String code;

    private String msg;

    private String signature;

    @ApiModelProperty(value = "订单状态：待支付-0，支付中-1，支付成功-2，支付失败-3，退款-4，已关闭-5，撤销-6，待支付-10")
    private String paySt;
}
