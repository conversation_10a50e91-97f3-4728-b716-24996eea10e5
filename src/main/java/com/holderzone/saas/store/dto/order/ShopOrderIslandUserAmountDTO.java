package com.holderzone.saas.store.dto.order;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 收益余额支付
 */
@Data
public class ShopOrderIslandUserAmountDTO implements Serializable {

    private static final long serialVersionUID = 2575531712696207747L;

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;


    /**
     * 赚餐 userId
     */
    private Long weAppUserId;

    /**
     * 会员手机号
     */
    private String phone;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 会员卡guid
     */
    private String memberInfoCardGuid;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 订单guid
     */
    private String orderGuid;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 微信订单guid
     */
    private String orderRecordGuid;

    /**
     * 商品列表
     */
    private List<InnerSkuDTO> skuList;

    @Data
    public static class InnerSkuDTO implements Serializable {

        private static final long serialVersionUID = 3093885676204257010L;

        private String name;

        private String num;

    }
}
