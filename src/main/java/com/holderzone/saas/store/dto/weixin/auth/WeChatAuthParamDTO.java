package com.holderzone.saas.store.dto.weixin.auth;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 微信授权参数
 * @date 2021/9/6 16:09
 * @className: WeChatAuthParamRespDTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "微信授权参数")
public class WeChatAuthParamDTO implements Serializable {

    private static final long serialVersionUID = 1134907801136116441L;

    /**
     * 应用唯一标识
     */
    @ApiModelProperty("应用唯一标识")
    private String appId;

    /**
     * 应用授权作用域，拥有多个作用域用逗号（,）分隔，APP 所拥有的 scope
     */
    @ApiModelProperty("应用授权作用域")
    private String scope;

    /**
     * 一个随机的尽量不重复的字符串，用来使得每次的 signature 不同
     */
    @ApiModelProperty("随机字符串")
    private String noncestr;

    /**
     * 时间戳
     */
    @ApiModelProperty("时间戳")
    private String timestamp;

    /**
     * 签名
     */
    @ApiModelProperty("签名")
    private String signature;

    /**
     * 授权流程的回调接口
     */
    @ApiModelProperty("授权流程的回调接口")
    private String listener;
}
