package com.holderzone.saas.store.dto.order.request.daily;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DailyReqDTO
 * @date 2019/02/12 16:49
 * @description 营业日报查询请求参数
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@EqualsAndHashCode(callSuper = true)
public class DailyReqDTO extends BaseDTO {

    private static final long serialVersionUID = -3509957219129064931L;

    @NotBlank(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始时间，格式yyyy-MM-dd")
    private String beginTime;

    @NotBlank(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间，格式yyyy-MM-dd")
    private String endTime;

    /**
     * @see com.holderzone.saas.store.enums.order.DailyReqQueryTypeEnum
     */
    @ApiModelProperty(value = "查询类型 0或者null：全部, 1:堂食 2：外卖")
    private Integer queryType;

    @Nullable
    @ApiModelProperty(value = "排序字段（UI图从左至右） 1：第一个排序字段 2：第二个排序字段 3：第三个排序字段")
    private Integer orderItem;

    @Nullable
    @ApiModelProperty(value = "排序方式 1正序 2倒序")
    private Integer orderType;

    @ApiModelProperty(value = "商品类型", example = "商品销售统计新增筛选条件，0 单品，1 套餐")
    private Integer itemType;

    @ApiModelProperty(value = "是否会员")
    private Integer isMember;

    @ApiModelProperty(value = "结账操作人guid")
    private List<String> checkoutStaffGuids;

    @ApiModelProperty(value = "门店guid")
    private List<String> storeGuids;

    @ApiModelProperty(value = "是否为打印时查询")
    private Integer isPrint;

    @ApiModelProperty(value = "分类Guid")
    private List<String> typeGuidList;

    @ApiModelProperty(value = "隐藏平台数据")
    private List<Integer> reportHideSubTypes;

}