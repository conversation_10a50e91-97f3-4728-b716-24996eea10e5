package com.holderzone.saas.store.dto.trade;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeBatchDTO
 * @date 2019/06/04 16:22
 * @description
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PaymentTypeBatchDTO {
    private String storeGuid;

    private List<PaymentTypeDTO> paymentTypeDTOList;
}
