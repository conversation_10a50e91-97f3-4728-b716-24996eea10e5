package com.holderzone.saas.store.dto.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className ValidateRespDTO
 * @date 18-9-25 下午2:13
 * @description android端登陆验证返回信息DTO
 * @program holder-saas-store-staff
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ValidateRespDTO implements Serializable {

    private static final long serialVersionUID = 8580366670248572999L;

    @ApiModelProperty(value = "门店编号")
    private String storeNo;

    @ApiModelProperty(value = "用户guid")
    private String userGuid;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "用户编号")
    private String userAccount;

    @ApiModelProperty(value = "手机号")
    private String phoneNumber;

    @ApiModelProperty(value = "是否人脸录入")
    private Boolean isInputFace;

    @ApiModelProperty(value = "设备guid（云端生成）")
    private String deviceId;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名")
    private String storeName;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty(value = "企业名")
    private String enterpriseName;

    @ApiModelProperty(value = "有效起始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime validStartTime;

    @ApiModelProperty(value = "有效截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime validEndTime;

    @ApiModelProperty(value = "产品有效起始时间")
    private Long productStartTime;

    @ApiModelProperty(value = "产品有效截止时间")
    private Long productEndTime;

    @ApiModelProperty(value = "多会员体系的guid")
    private String multiMemberGuid;

    @ApiModelProperty(value = "多会员体系的名称")
    private String multiMemberName;

    @ApiModelProperty(value = "体系状态，默认启用，true为启用 false为禁用")
    private Boolean multiMemberStatus;

    @ApiModelProperty(value = "经营类型编码")
    private String manageTypeCode;

    @ApiModelProperty("经营终端会员登录方式")
    public List<Integer> memberLoginType;

    @ApiModelProperty("会员支付校验方式")
    public List<Integer> memberLoginCheckType;

    @ApiModelProperty(value = "是否开启就餐会员画像")
    private Boolean memberPortrayal;

    @ApiModelProperty(value = "副屏显示内容 1同主屏一致 2自定义")
    private Integer displayType;

}
