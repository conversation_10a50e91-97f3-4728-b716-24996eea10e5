package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 外卖列表返回数据
 * @date 2022/3/18
 */
@Data
@NoArgsConstructor
public class TakeoutOrderListDTO {

    @ApiModelProperty(value = "配送方式(0:自配送；1：一城飞客；2：专送；3：众包)")
    private Integer deliveryType;

    @ApiModelProperty(value = "外卖订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "订单状态 -1：已取消，0：待处理，10：已接单/待配送，20：订单配送，100：已完成")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单异常状态 " +
            "0：初始状态，" +
            "10：用户申请“取消订单”，11：处理“同意取消订单”中，12：处理“不同意取消订单”中，" +
            "20：用户申请“退单”，21：处理“同意退单”中，22：处理“不同意退单”中")
    private Integer orderTagStatus;

    @ApiModelProperty(value = "订单子类.OrderType=0：0=美团  1=饿了么  2=百度  3=京东 4=滴滴  5=自营（自营外卖平台） 6=赚餐")
    private Integer orderSubType;

    @ApiModelProperty(value = "外卖订单号")
    private String orderId;

    @ApiModelProperty(value = "日流水")
    private String orderDaySn;

    @ApiModelProperty(value = "是否是预定单：0=否，1=是")
    private Boolean reserve;

    @ApiModelProperty(value = "备注")
    private String orderRemark;

    @ApiModelProperty(value = "是否是催单")
    private Boolean reminded;

    @ApiModelProperty(value = "菜品数量")
    private BigDecimal itemCount;

    @ApiModelProperty(value = "顾客姓名")
    private String customerName;

    @ApiModelProperty(value = "顾客手机号")
    private String customerPhone;

    @ApiModelProperty(value = "顾客地址")
    private String customerAddress;

    @ApiModelProperty(value = "顾客实际支付金额")
    private BigDecimal customerActualPay;

    @ApiModelProperty(value = "总价,包括：菜品(原价)+餐盒+配送")
    private BigDecimal total;

    @ApiModelProperty(value = "订单创建（下单）时间戳")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "预订单生效/激活时间,非预订单，同下单时间")
    private LocalDateTime activeTime;

    @ApiModelProperty(value = "接单时间")
    private LocalDateTime acceptTime;

    @ApiModelProperty(value = "退单请求时间")
    private LocalDateTime refundReqTime;

    @ApiModelProperty(value = "订单完成时间")
    private LocalDateTime completeTime;

    @ApiModelProperty(value = "配送时间要求  格式化好的时间字符串")
    private String estimateDeliveredTimeString;

    @ApiModelProperty(value = "取消订单请求时间")
    private LocalDateTime cancelReqTime;

    @ApiModelProperty(value = "赚餐外卖订单状态")
    /**
     * PENDING--待处理;
     * COOKING---出餐中;
     * TAKE_A_SINGLE---取单中;
     * DISTRIBUTION----配送中;
     * FINISH-已完成;
     * CANCELLED---已取消;
     * PERSON_PENDING---配送员待接单(已出餐);
     * INVALID---失效
     */
    private String orderStatusTcd;

    @ApiModelProperty(value = "订单取消时间")
    private LocalDateTime cancelTime;

    @ApiModelProperty(value = "期望送达时间  0=无时间要求  >0=具体要求的送达时间")
    private LocalDateTime estimateDeliveredTime;

    @ApiModelProperty(value = "配送时间，同取餐时间")
    private LocalDateTime deliveryTime;

    @ApiModelProperty(value = "核销码")
    private String writeOffCode;

    @ApiModelProperty(value = "赚餐外卖类型,NETWORK=线上配送；PICK_UP=自提")
    private String orderTypeTcd;

    @ApiModelProperty(value = "隐私号")
    private String privacyPhone;
}
