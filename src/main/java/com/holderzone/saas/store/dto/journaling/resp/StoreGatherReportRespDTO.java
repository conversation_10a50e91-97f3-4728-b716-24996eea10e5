package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreGatherReportRespDTO
 * @date 2019/05/29 10:59
 * @description 门店汇总respDTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel("门店汇总列表实体")
public class StoreGatherReportRespDTO implements Serializable {

    private static final long serialVersionUID = -7797621541091220451L;
    @ApiModelProperty(value = "日期")
    private String date;
    @ApiModelProperty(value = "品牌名称")
    private String brandName;
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;
    @ApiModelProperty(value = "门店名称")
    private String storeName;
    @ApiModelProperty(value = "应收金额")
    private BigDecimal orderFee;
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountFee;
    @ApiModelProperty(value = "实收金额")
    private BigDecimal actuallyPayFee;
    @ApiModelProperty(value = "订单数")
    private Integer orderCount;
    @ApiModelProperty(value = "单均")
    private BigDecimal orderAverageFee;
    @ApiModelProperty(value = "客流量")
    private Integer guestCount;
    @ApiModelProperty(value = "客均")
    private BigDecimal guestAverageFee;

    @ApiModelProperty(value = "平均用餐时长（分）")
    private Integer orderAverageTime;

    @ApiModelProperty(value = "门店对应的orderGuid列表",hidden = true)
    private List<String> orderGuids;

    @ApiModelProperty(value = "系统省零",hidden = true)
    private BigDecimal savingZero;

    @ApiModelProperty(value = "会员折扣",hidden = true)
    private BigDecimal memberDiscount;

    @ApiModelProperty(value = "会员优惠券",hidden = true)
    private BigDecimal memberCoupons;

    @ApiModelProperty(value = "菜品赠送",hidden = true)
    private BigDecimal itemPresent;

    @ApiModelProperty(value = "整单让价",hidden = true)
    private BigDecimal fullSinglePrice;

    @ApiModelProperty(value = "团购券",hidden = true)
    private BigDecimal groupCoupons;

    @ApiModelProperty(value = "整单折扣",hidden = true)
    private BigDecimal wholeDiscount;

    @ApiModelProperty(value = "其他优惠",hidden = true)
    private BigDecimal otherDiscount;
    @ApiModelProperty(value = "门店编码")
    private String storeCode;

}
