package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BatchImportItemRespDTO
 * @date 2019/07/30 15:47
 * @description //TODO 批量导入商品返回结果DTO
 * @program holder-saas-aggregation-app
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "Q3阶段优化：批量导入商品返回结果")
@Data
public class BatchImportItemRespDTO {

    @ApiModelProperty(value = "验证及保存状态 0：上传成功  1：保存失败 2：验证失败 ")
    private  Integer flag ;

    @ApiModelProperty(value = "数据集 保存成功时结果为空  保存和验证失败有数据 ")
    private List<ItemBatchImportTempRespDTO> list;
}
