package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/7/24 17:58
 * @description  微信公众号绑定运营主体请求参数
 */
@Data
public class WxOperSubjectBrandReqDTO {
    /***
     * 品牌guid
     */
    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;
    /***
     * 是否是联盟主体
     */
    @ApiModelProperty(value = "是否是联盟主体")
    private Boolean isAlliance;
    /***
     * 运营主体Guid
     */
    @ApiModelProperty(value = "运营主体Guid")
    private String operSubjectGuid;
}
