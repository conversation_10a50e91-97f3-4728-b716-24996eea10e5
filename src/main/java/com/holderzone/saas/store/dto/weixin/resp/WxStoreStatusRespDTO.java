package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreStatusRespDTO
 * @date 2019/02/21 18:35
 * @description 微信门店业务状态DTO
 * @program holder-saas-store-weixin
 */
@Data
@ApiModel(value = "微信门店业务状态DTO")
public class WxStoreStatusRespDTO {
    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "门店所属品牌名字集合")
    private List<String> brandNameList;

    @ApiModelProperty(value = "门店是否开启微信线上功能(0关闭，1开启)")
    private Integer isOpened;

    @ApiModelProperty(value = "堂食扫码点餐是否开启（0关闭，1开启）")
    private Integer forHereStatus;

    @ApiModelProperty(value = "排队扫码点餐是否开启（0关闭，1开启）")
    private Integer queueUpStatus;

    @ApiModelProperty(value = "外卖扫码点餐时否开启（0关闭，1开启）（一期不做）")
    private Integer takeawayStatus;

    @ApiModelProperty(value = "预定功能是否开启（0关闭，1开启）")
    private Integer bookingStatus;
}