package com.holderzone.saas.store.dto.print.template.printable;

import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@Accessors(chain = true)
public class ReverseText implements Serializable {

    private static final long serialVersionUID = 111622080154251370L;

    @ApiModelProperty(value = "打印文本")
    private Text text;

    @ApiModelProperty(value = "文本对齐方式")
    private Text.Align align = Text.Align.Left;

    public ReverseText(Text text) {
        this.text = text;
    }

    public ReverseText(Text text, Text.Align align) {
        this.text = text;
        this.align = align;
    }

    public ReverseText(String text) {
        this(new Text(text));
    }

    public ReverseText(String text, Font font) {
        this(new Text(text, font));
    }

    public ReverseText(String text, Font font, Text.Align align) {
        this(new Text(text, font), align);
    }
}
