package com.holderzone.saas.store.dto.takeaway.jd;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 京东秒送系统参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BusinessOrderDTO extends CommonSystemDTO{

    private String billId;

    private String statusId;

    private String timestamp;

    private String orderId;

    private String businessTag;

    private String remark;

    private OrderStatusEnum status;

    public void initStatus(){
        this.status = OrderStatusEnum.getOrderStatusEnumByJdCode(this.statusId);
    }

}
