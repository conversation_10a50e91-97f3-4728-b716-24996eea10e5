package com.holderzone.saas.store.dto.order.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeAwayBillPayCreateDTO
 * @date 2018/09/27 15:09
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class TakeAwayBillPayCreateDTO implements Serializable {

    private String orderGuid;

    /**
     * 总价
     */
    private BigDecimal total;

    /**
     * 实收
     */
    private BigDecimal amount;

    /**
     * 折扣
     */
    private BigDecimal discountTotal;

    private String staffGuid;

    private String staffName;
    /**
     * 外卖交易流水号
     */
    private String orderSn;

    /**
     * 美团外卖 -2 饿了么外卖-1
     */
    private String paymentType;

    /**
     * 结算时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkTime;

    /**
     * 订单来源，0:一体机，1:pos设备，2 m1 ,3:平板点餐，4:微信公众号，5:美团外卖，6:饿了么外卖
     */
    private Integer orderSource;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate businessDay;

    private String storeGuid;

    private String storeName;


}
