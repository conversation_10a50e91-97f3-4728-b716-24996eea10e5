package com.holderzone.saas.store.dto.report.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DiscountDetailRespDTO
 * @date 2018/09/29 19:26
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class DiscountDetailRespDTO implements Serializable {

    @ApiModelProperty(value = "订单号")
    private String orderGuid;

    @ApiModelProperty(value = "折扣类型")
    private String discountName;

    @ApiModelProperty(value = "折扣金额")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "折扣时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime discountTime;

    @ApiModelProperty(value = "门店名字")
    private String storeName;

    @ApiModelProperty(value = "销售模式")
    private String tradeMode;

    @ApiModelProperty(value = "员工guid")
    private String staffGuid;

    @ApiModelProperty(value = "员工name")
    private String staffName;

    @ApiModelProperty(value = "员工name")
    private String staffNameAndGuid;

    public String getStaffNameAndGuid() {
        return this.staffName + "/" + this.staffGuid;
    }
}
