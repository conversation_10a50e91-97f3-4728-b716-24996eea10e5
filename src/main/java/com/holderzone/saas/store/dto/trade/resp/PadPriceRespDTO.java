package com.holderzone.saas.store.dto.trade.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description pad价格实体
 * @date 2021/9/2 11:21
 * @className: PadPriceRespDTO
 */
@NoArgsConstructor
@Accessors(chain = true)
@Data
@ApiModel(value = "pad价格实体")
public class PadPriceRespDTO implements Serializable {

    private static final long serialVersionUID = 3646391587923604153L;

    @ApiModelProperty("购物车商品总价")
    private BigDecimal originPrice;

    @ApiModelProperty("购物车会员优惠总价")
    private BigDecimal memberPrice;

    public static PadPriceRespDTO returnEmpty() {
        return new PadPriceRespDTO().setOriginPrice(BigDecimal.ZERO).setMemberPrice(BigDecimal.ZERO);
    }

    public PadPriceRespDTO(BigDecimal originPrice, BigDecimal memberPrice) {
        this.originPrice = originPrice;
        this.memberPrice = memberPrice;
    }
}
