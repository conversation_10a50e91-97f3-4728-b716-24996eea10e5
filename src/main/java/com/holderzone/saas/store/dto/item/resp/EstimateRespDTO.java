package com.holderzone.saas.store.dto.item.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateRespDTO
 * @date 2019/05/07 15:31
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EstimateRespDTO implements Serializable {

    private Long id;

    private String guid;

    /**
     * 商品sku guid
     */
    private String skuGuid;

    /**
     * 是否禁售 0:否 1:是
     */
    private Integer isSoldOut;

    /**
     * 是否限量  0：否  1：是
     */
    private Integer isTheLimit;

    /**
     * 限量数量
     */
    private Integer limitQuantity;

    /**
     * 当前剩余数量
     */
    private Integer residueQuantity;

    /**
     * 提醒阈值
     */
    private Integer reminderThreshold;

    /**
     * 是否次日置满  0：否  1：是
     */
    private Integer isItReset;

    /**
     * 门店guid
     */
    private String storeGuid;


}
