package com.holderzone.saas.store.dto.report.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DiscountQueryDTO
 * @date 2018/09/28 19:28
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class DiscountQueryDTO extends BaseQueryDTO {

    @ApiModelProperty(value = "-2 美团折扣 -1 饿了么折扣 ，0-会员折扣，1-优惠券整单折扣，2-优惠券菜品折扣，3-整单折扣，4-菜品赠送，5-会员积分，6-单品折扣,7-系统省零，8-收款改价")
    private String discountType;

    @ApiModelProperty(value = "订单号")
    private String orderGuid;

    @ApiModelProperty(value = "订单来源 0:全部 ，一体机点餐下单（1）、POS设备（2）、M1（3）、平板点餐（4）、微信公众号（5）、美团外卖（6）饿了么外卖（7）")
    private int orderSource = 0;

}
