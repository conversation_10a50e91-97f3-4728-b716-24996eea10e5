package com.holderzone.saas.store.dto.takeaway.request;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class FoodListDTO {

    @ApiModelProperty(value = "菜品名称")
    public String FoodName;

    @ApiModelProperty(value = "价格")
    public double FoodPrice;


    @ApiModelProperty(value = "数量")
    public int FoodCount;
}
