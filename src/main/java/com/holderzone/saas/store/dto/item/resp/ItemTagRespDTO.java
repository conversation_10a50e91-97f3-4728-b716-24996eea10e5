package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ItemTagRespDTO {
    private String guid;
    @ApiModelProperty("门店guid")
    private String storeGuid;
    @ApiModelProperty("标签名称")
    private String tagName;
    @ApiModelProperty("图片链接")
    private String pictureUrl;
    @ApiModelProperty("排序")
    private Integer sort;
    @ApiModelProperty("标签分组")
    private Integer tagGroup;
}
