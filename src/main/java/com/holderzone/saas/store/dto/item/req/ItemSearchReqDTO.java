package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ItemSearchReqDTO {

    @ApiModelProperty(value = "门店Guid", required = true)
    private String storeGuid;

    @ApiModelProperty("是否推荐")
    private Integer isRecommend;

    @ApiModelProperty("是否上架 0否 1是")
    private Integer isRack;

    @ApiModelProperty(value = "是否上架一体机（0：否，1：是）")
    private Integer isJoinAio;

    @ApiModelProperty(value = "是否上架POS机（0：否，1：是）")
    private Integer isJoinPos;

    @ApiModelProperty(value = "是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;

    @ApiModelProperty("商品Guid列表")
    private List<String> itemGuidList;
}
