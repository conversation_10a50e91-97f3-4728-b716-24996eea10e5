package com.holderzone.saas.store.dto.hw;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveDateDTO
 * @date 2019/04/26 18:29
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "预定请求")
public class ReserveIntervalDTO extends ReserveDateDTO {

    @NotNull(message = "预定区间不得为空")
    @ApiModelProperty(value = "预定区间：0=中午，1=晚上", required = true)
    private Integer reserveInterval;
}