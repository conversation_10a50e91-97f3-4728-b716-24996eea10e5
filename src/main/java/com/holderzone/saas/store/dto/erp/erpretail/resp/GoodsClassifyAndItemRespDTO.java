package com.holderzone.saas.store.dto.erp.erpretail.resp;

import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("商品分类及分类下的商品")
public class GoodsClassifyAndItemRespDTO {

    @ApiModelProperty("商品所属分类guid")
    private String goodsClassifyGuid;

    @ApiModelProperty("商品所属分类名称")
    private String goodsClassifyName;

    @ApiModelProperty("该分类下对应的菜品")
    List<InOutGoodsDTO> goodsList;
}
