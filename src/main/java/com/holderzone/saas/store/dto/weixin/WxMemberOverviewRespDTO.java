package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Api("会员中心主页")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WxMemberOverviewRespDTO {

	@ApiModelProperty(value = "true：绑定手机号")
	private boolean bindingPhoneNum;

	@ApiModelProperty(value = "手机号码")
	private String phoneNum;

	@ApiModelProperty(value = "所有子模块")
	private List<WxMemberOverviewModelDTO> wxMemberOverviewModelDTOS;

	@ApiModelProperty(value = "卡列表")
	private WxMemberCenterCardRespDTO wxMemberCenterCardRespDTO;

	@ApiModelProperty("会员guid")
	private String memberInfoGuid;
}
