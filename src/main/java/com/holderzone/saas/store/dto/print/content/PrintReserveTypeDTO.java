package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.nested.PrintReserveType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;


/**
 * 外卖单
 *
 * <AUTHOR>
 * @date 2018/09/19 16:36
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PrintReserveTypeDTO extends PrintDTO {


    @NotNull(message = "合计数量不能为空")
    @ApiModelProperty(value = "合计数量", required = true)
    private Double totalNum;

    @Valid
    @Nullable
    @ApiModelProperty(value = "分类列表")
    private List<PrintReserveType> typeRecordList;


}
