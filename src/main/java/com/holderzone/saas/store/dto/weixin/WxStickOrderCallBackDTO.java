package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickOrderCallBackDTO
 * @date 2019/03/27 17:57
 * @description 微信桌贴购买回调DTO
 * @program holder-saas-store
 */
@ApiModel("微信桌贴购买回调DTO")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class WxStickOrderCallBackDTO {

    @ApiModelProperty("enterpriseGuid")
    @NotBlank
    private String enterpriseGuid;

    @ApiModelProperty("payGuid")
    @NotBlank
    private String payGuid;

    @ApiModelProperty("支付状态")
    @NotBlank
    private String paySt;

    @ApiModelProperty("模板guid集合")
    @NotEmpty
    private List<String> modelGuidList;
}
