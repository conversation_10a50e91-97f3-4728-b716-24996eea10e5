package com.holderzone.saas.store.dto.marketing.specials;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 营销活动下单请求
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class UniteActivityOrderPayDTO implements Serializable {

    private static final long serialVersionUID = 5528866350378721845L;

    /**
     * 活动列表
     */
    private List<InnerActivity> activities;

    @NotBlank(message = "来源门店不能为空")
    private String storeName;

    /**
     * 订单时间
     */
    @NotNull(message = "订单时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime orderTime;

    @NotBlank(message = "订单guid不能为空")
    private String orderGuid;

    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    /**
     * 订单类型
     *
     * @see com.holderzone.saas.store.enums.marketing.OrderTypeEnum
     */
    @NotNull(message = "订单类型不能为空")
    private Integer orderType;

    /**
     * 订单金额
     */
    @NotNull(message = "订单类型不能为空")
    private BigDecimal orderFee;

    /**
     * 订单优惠金额
     */
    @NotNull(message = "订单优惠金额不能为空")
    private BigDecimal orderDiscountFee;

    /**
     * 订单实收金额
     */
    @NotNull(message = "订单实收金额不能为空")
    private BigDecimal orderActuallyFee;

    /**
     * 订单来源
     *
     * @see com.holderzone.saas.store.enums.marketing.OrderSourceEnum
     */
    @NotNull(message = "订单来源不能为空")
    private Integer orderSource;

    /**
     * 下单用户
     */
    private String memberPhone;

    /**
     * 手机号
     */
    private String memberName;

    @Data
    public static class InnerActivity implements Serializable {

        @NotNull(message = "活动类型不能为空")
        private Integer activityType;

        /**
         * 活动guid
         */
        @NotBlank(message = "活动guid不能为空")
        private String activityGuid;
    }

    private List<Integer> activityTypes;

    /**
     * 活动guid
     */
    private String activityGuid;
}
