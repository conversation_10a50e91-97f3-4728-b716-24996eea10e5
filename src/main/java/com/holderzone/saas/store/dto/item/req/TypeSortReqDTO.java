package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeSortReqDTO
 * @date 2019/11/03 下午5:40
 * @description //批量更新分类排序请求实体
 * @program holder
 */

@Data
@ApiModel(value = "批量更新分类排序请求实体")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TypeSortReqDTO {

    @NotNull
    @ApiModelProperty(value = "分类guid")
    private String guid;

    @ApiModelProperty(value = "分类名称")
    private String name;

    @NotNull
    @ApiModelProperty(value = "分类排序权重值 越小越靠前")
    private Integer sort;

    @ApiModelProperty(value = "批量更新分类下商品排序集合")
    private List<ItemSortReqDTO> itemList;

    @ApiModelProperty(value = "商品guid集合")
    private List<String> itemGuidList;
}
