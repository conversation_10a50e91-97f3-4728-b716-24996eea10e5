package com.holderzone.saas.store.dto.journaling.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DailySaleDetailRespDTO
 * @date 2019/10/25 14:50
 * @description
 * @program holder-saas-store
 */
@Data
@ApiModel("营业日报销售统计请求入参")
public class DailySaleDetailReqDTO extends JournalStoreAppBaseReqDTO {
    @ApiModelProperty("1/分类销售， 2/商品销售，3/退货统计，4/赠送统计")
    private Integer reqType;
}
