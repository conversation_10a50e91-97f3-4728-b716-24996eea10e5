package com.holderzone.saas.store.dto.invoice;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@ApiModel(description = "生成认证二维码")
@Data
public class RequestAuthenticationDTO implements Serializable {

    @ApiModelProperty(value = "门店")
    private String storeGuid;

    @ApiModelProperty(value = "订单流水号")
    private String orderNo;

    @ApiModelProperty(value = "1 税务app（默认） 2 个税app")
    private Integer taxType;

    @ApiModelProperty(value = "登录账号")
    private String account;
}
