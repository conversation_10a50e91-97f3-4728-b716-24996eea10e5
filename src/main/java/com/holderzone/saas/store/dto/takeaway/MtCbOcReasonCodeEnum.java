package com.holderzone.saas.store.dto.takeaway;

/**
 * 美团发送取消消息中的值
 * <p>
 * https://developer.meituan.com/openapi#7.6
 */
public enum MtCbOcReasonCodeEnum {

    超时未确认("1001", "系统取消，超时未确认"),

    在线支付订单30分钟未支付("1002", "系统取消，在线支付订单30分钟未支付"),

    在线支付中取消("1101", "用户取消，在线支付中取消"),

    商家确认前取消("1102", "用户取消，商家确认前取消"),

    用户退款取消("1103", "用户取消，用户退款取消"),

    用户下错单("1201", "客服取消，用户下错单"),

    用户测试("1202", "客服取消，用户测试"),

    重复订单("1203", "客服取消，重复订单"),

    其他原因("1204", "客服取消，其他原因"),

    未知原因("1301", "其他原因");

    private String code;

    private String desc;

    MtCbOcReasonCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static MtCbOcReasonCodeEnum ofCode(String code) {
        for (MtCbOcReasonCodeEnum value : values()) {
            if (value.code.equalsIgnoreCase(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效的reasonCode[" + code + "]");
    }
}
