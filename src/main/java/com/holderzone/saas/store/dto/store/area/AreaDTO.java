package com.holderzone.saas.store.dto.store.area;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AreaDO
 * @date 2018/07/23 下午12:52
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AreaDTO implements Serializable {

    private static final long serialVersionUID = -3720029598274524679L;

    /**
     * 区域guid
     */
    @ApiModelProperty(value = "门店名称")
    private String areaGuid;

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店名称")
    private String storeGuid;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "门店名称")
    private String name;

    /**
     * 区域排序
     */
    @ApiModelProperty(value = "门店名称")
    private Integer sort;

    /**
     * 是否已启用
     * 0=未启用
     * 1=已启用
     */
    @ApiModelProperty(value = "是否已启用。0=未启用，1=已启用。")
    private Integer enable;

    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    @ApiModelProperty(value = "是否已删除。0=未删除，1=已删除。")
    private Integer deleted;

    /**
     * 该区域下桌台数量
     */
    @ApiModelProperty(value = "该区域下桌台数量")
    private Integer tableCount;

    /**
     * 该区域下订单/预定记录数量
     */
    @ApiModelProperty(value = "该区域下订单/预定记录数量")
    private Integer orderCount;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 该区域下的桌台列表
     */
    @ApiModelProperty(value = "该区域下的桌台列表")
    private List<TableDTO> tables;
}
