package com.holderzone.saas.store.dto.journaling.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SingleSaleDetailReqDTO
 * @date 2019/10/30 17:30
 * @description 商超版商品销售入参
 * @program holder-saas-store
 */
@Data
@ApiModel("商超版商品销售入参")
public class SingleSaleDetailReqDTO extends JournalWebBaseReqDTO {
    @ApiModelProperty("分类GUID")
    private String typeGuid;

    private Integer start;

    @ApiModelProperty(value = "操作人guid集合")
    private List<String> createStaffGuidList;

    public int getStart() {
        return (int) ((this.getCurrentPage() <= 0 ? 1 : this.getCurrentPage() - 1) * this.getPageSize());
    }
}
