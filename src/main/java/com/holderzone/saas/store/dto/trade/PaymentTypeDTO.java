package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeDTO
 * @date 2018/08/27 9:17
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class PaymentTypeDTO implements Serializable {

    @ApiModelProperty(value = "storeGuid")
    private String storeGuid;

    @ApiModelProperty(value = "storeName")
    private String storeName;

    @ApiModelProperty(value = "支付方式guid")
    private String paymentTypeGuid;

    @ApiModelProperty(value = "支付方式name")
    private String paymentTypeName;

    @ApiModelProperty(value = "0:现金支付，1:聚合支付，2:银行卡支付，3:会员卡支付（选择系统默认时为必填项）,11:食堂电子卡,12:食堂实体卡")
    private Integer paymentType;

    @ApiModelProperty(value = "0-启用，1-禁用")
    private Integer state;

    @ApiModelProperty(value = "门店信息")
    private List<StoreDTO> storeDTOS;

    @ApiModelProperty(value = "排序，自增排序")
    private Integer sorting;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "appId")
    private String appId;

    @ApiModelProperty(value = "appSecretKey")
    private String appSecretKey;

    @ApiModelProperty(value = "0:全部，1:默认，2:自定义")
    private Integer source;

    @ApiModelProperty(value = "支付模式，0:先登录后支付，1:直接支付")
    private Integer paymentMode;

    @ApiModelProperty(value = "是否支持多卡支付")
    private Integer paymentMultiCard;

    @ApiModelProperty(value = "父类支付类型guid")
    private String parentPaymentTypeGuid;

    @ApiModelProperty("子菜单")
    private List<PaymentTypeDTO> children;

    @ApiModelProperty(value = "收款分流：0关闭 1开启")
    private Integer paymentShunt;

    @ApiModelProperty("聚合支付配置信息")
    private List<PaymentInfoDTO> jhPayInfoList;

    private String empId;

}
