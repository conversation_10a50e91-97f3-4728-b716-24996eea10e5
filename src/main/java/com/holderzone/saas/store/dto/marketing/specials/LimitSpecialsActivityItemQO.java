package com.holderzone.saas.store.dto.marketing.specials;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/28
 * @description 限时特价活动商品查询
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "限时特价活动商品查询")
public class LimitSpecialsActivityItemQO implements Serializable {

    private static final long serialVersionUID = -3344795346015372923L;

    @ApiModelProperty(value = "运营主体guid")
    private String operSubjectGuid;

    @ApiModelProperty("会员GUID")
    private String memberInfoGuid;

    @ApiModelProperty("是否注册")
    private Integer isRegister;

    @ApiModelProperty("商品guid")
    private List<String> itemGuidList;

    /**
     * 指定等级guid（按等级筛选存在）
     */
    private String gradeGuidListStr;

    /**
     * 标签guid（按标签筛选存在）
     */
    private String labelGuidListStr;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 适用场景 0:正餐 1：快餐
     */
    @ApiModelProperty(value = "适用场景 0:正餐 1：快餐")
    private Integer applyBusiness;

}
