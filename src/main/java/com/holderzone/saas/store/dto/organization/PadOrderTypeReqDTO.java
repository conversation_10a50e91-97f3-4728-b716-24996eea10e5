package com.holderzone.saas.store.dto.organization;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className PadOrderTypeReqDTO
 * @date 21-8-2 下午5:25
 * @description pad点餐设置点餐模式请求dto
 */
@ApiModel("pad点餐查询点餐模式返回dto")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PadOrderTypeReqDTO {

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "点餐类型(0:商家点餐 1:用户自主点餐)")
    private Integer padOrderType;

    @ApiModelProperty(value = "桌台guid")
    private String tableGuid;

    @ApiModelProperty(value = "设备号")
    private String deviceNo;

    @ApiModelProperty(value = "桌台guid列表")
    private List<String> tableGuidList;

}
