package com.holderzone.saas.store.dto.order.response.daily;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OverviewRespDTO
 * @date 2019/02/15 14:51
 * @description 营业日报-营业概况
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class OverviewRespDTO {

    @ApiModelProperty(value = "收银员")
    private List<String> checkoutStaffs;
    @ApiModelProperty(value = "订单数")
    private Integer orderCount;
    @ApiModelProperty(value = "客流量")
    private Integer guestCount;
    @ApiModelProperty(value = "销售额")
    private BigDecimal consumerAmount;
    @ApiModelProperty(value = "销售收入")
    private BigDecimal gatherAmount;
    @ApiModelProperty(value = "销售收入预计应得金额")
    private BigDecimal estimatedAmount;
    @ApiModelProperty(value = "销售收入项")
    private List<AmountItemDTO> gatherItems;
    @ApiModelProperty(value = "优惠总额")
    private BigDecimal discountAmount;
    @ApiModelProperty(value = "优惠项")
    private List<AmountItemDTO> discountItems;
    @ApiModelProperty(value = "美团团购商家预计应得金额")
    private BigDecimal mtGrouponEstimatedAmount;

    @ApiModelProperty(value = "毛利润")
    private BigDecimal grossProfitAmount;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costAmount;

    @ApiModelProperty(value = "正餐客流量")
    private Integer dineInGuestCount;

    @ApiModelProperty(value = "上座率")
    private String occupancyRatePercent;

    @ApiModelProperty(value = "开台率")
    private String openTableRatePercent;

    @ApiModelProperty(value = "翻台率")
    private String flipTableRatePercent;

    @ApiModelProperty(value = "平均用餐时间，单位分钟")
    private Integer avgDineInTime;

    @ApiModelProperty("会员充值金额")
    private BigDecimal rechargeMoney;

    @ApiModelProperty(value = "退款总额")
    private BigDecimal refundAmount;
}