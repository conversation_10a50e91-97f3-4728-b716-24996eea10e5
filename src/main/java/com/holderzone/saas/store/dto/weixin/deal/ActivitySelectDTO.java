package com.holderzone.saas.store.dto.weixin.deal;

import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/27
 * @description 活动选择
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "活动选择")
public class ActivitySelectDTO implements Serializable {

    private static final long serialVersionUID = -8978898086856781726L;

    @ApiModelProperty(value = "营销活动guid，如果为字符串0则为取消，如果传null  表示不变")
    private String activityGuid;

    /**
     * @see DiscountTypeEnum
     */
    @ApiModelProperty("活动类型 16限时特价 12满减满折")
    private Integer activityType;

}
