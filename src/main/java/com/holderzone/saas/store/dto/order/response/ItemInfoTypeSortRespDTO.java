package com.holderzone.saas.store.dto.order.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 排序值
 */
@Data
@ApiModel(value = "商品详情及分类信息返回实体")
public class ItemInfoTypeSortRespDTO {

//    @ApiModelProperty(value = "分类信息返回集合")
//    private List<TypeWebRespDTO> typeList;


    /**
     * sort
     */
    @ApiModelProperty(value = "sort")
    private Integer sort;


    /**
     * typeGUID
     */
    @ApiModelProperty(value = "typeSort")
    private Integer typeSort;



}
