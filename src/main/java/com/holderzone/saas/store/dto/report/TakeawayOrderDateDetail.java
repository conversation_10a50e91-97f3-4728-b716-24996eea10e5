package com.holderzone.saas.store.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2018/12/05 上午 11:27
 * @description
 */
@ApiModel
public class TakeawayOrderDateDetail {

    @ApiModelProperty("订单总额指标")
    private TakeawayOrderDateIndicator orderAmountTotal;

    @ApiModelProperty("订单数量指标")
    private TakeawayOrderDateIndicator orderCount;

    @ApiModelProperty("订单均单价指标")
    private TakeawayOrderDateIndicator orderAvgAmount;

    @ApiModelProperty("退款总额指标")
    private TakeawayOrderDateIndicator refundAmountTotal;

    @ApiModelProperty("退款数量")
    private TakeawayOrderDateIndicator refundCount;

    public TakeawayOrderDateIndicator getOrderAmountTotal() {
        return orderAmountTotal;
    }

    public void setOrderAmountTotal(TakeawayOrderDateIndicator orderAmountTotal) {
        this.orderAmountTotal = orderAmountTotal;
    }

    public TakeawayOrderDateIndicator getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(TakeawayOrderDateIndicator orderCount) {
        this.orderCount = orderCount;
    }

    public TakeawayOrderDateIndicator getOrderAvgAmount() {
        return orderAvgAmount;
    }

    public void setOrderAvgAmount(TakeawayOrderDateIndicator orderAvgAmount) {
        this.orderAvgAmount = orderAvgAmount;
    }

    public TakeawayOrderDateIndicator getRefundAmountTotal() {
        return refundAmountTotal;
    }

    public void setRefundAmountTotal(TakeawayOrderDateIndicator refundAmountTotal) {
        this.refundAmountTotal = refundAmountTotal;
    }

    public TakeawayOrderDateIndicator getRefundCount() {
        return refundCount;
    }

    public void setRefundCount(TakeawayOrderDateIndicator refundCount) {
        this.refundCount = refundCount;
    }
}
