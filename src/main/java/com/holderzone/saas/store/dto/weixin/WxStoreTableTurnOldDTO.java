package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WxStoreTableTurnOldDTO {
	@ApiModelProperty("旧桌台guid")
	private String tableGuid;

	@ApiModelProperty("旧桌台code")
	private String tableCode;

	@ApiModelProperty("旧桌台所出区域guid")
	private String areaGuid;

	@ApiModelProperty("旧桌台所出区域名称")
	private String areaName;

}
