package com.holderzone.saas.store.dto.erp.erpretail.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@Data
@NoArgsConstructor
@ApiModel("盘点概览信息查看")
public class InventoryOverviewReqDTO extends BasePageDTO {

    @ApiModelProperty("查询条件，盘点开始日期")
    @NotEmpty(message = "盘点开始日期不得为空")
    private String startDate;

    @ApiModelProperty("查询条件，盘点结束日期")
    @NotEmpty(message = "盘点结束日期不得为空")
    private String endDate;

    @ApiModelProperty("查询条件，盘点类型：0:全部，1，日盘，2：周盘，3：月盘")
    @NotEmpty(message = "盘点类型 不得为空")
    private String type;

    @ApiModelProperty("查询条件，盘点单状态：0:全部，1，已完成，2：已作废")
    @NotEmpty(message = "盘点单状态 不得为空")
    private String orderStatus;

    @ApiModelProperty("查询条件，盘点人或盘点单号")
    private String operatorOrOrderNo;

    @JsonIgnore
    private String startDateTime;

    @JsonIgnore
    private String endDateTime;
}
