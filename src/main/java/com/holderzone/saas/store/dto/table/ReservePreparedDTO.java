package com.holderzone.saas.store.dto.table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReservePreparedDTO
 * @date 2019/05/30 14:21
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReservePreparedDTO {
    private List<String> add;
    private List<String> del;
}