package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class OrderAbnormalRecordRespDTO {

    @ApiModelProperty(value = "发起支付时间")
    private LocalDateTime checkoutTime;

    @ApiModelProperty(value = "订单号(前端显示用，门店内唯一，格式************)")
    private String orderNo;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "用餐方式id 0.堂食 1.快餐")
    private Integer diningMethodId;

    @ApiModelProperty(value = "用餐方式名称")
    private String diningMethodName;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "支付方式id 1.微信 2.支付宝")
    private Integer paymentMethodId;

    @ApiModelProperty(value = "支付方式名称")
    private String paymentMethodName;

    @ApiModelProperty(value = "支付状态 1：成功 2：失败 3：异常")
    private Integer paymentStatu;

    @ApiModelProperty(value = "支付guid")
    private String payGuid;

    @ApiModelProperty(value = "支付结果：0：失败,1:成功,2：无结果")
    private Integer payResult;
}
