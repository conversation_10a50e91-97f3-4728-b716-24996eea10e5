package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class ItemConfBatchUpdateReqDTO implements Serializable {

    private static final long serialVersionUID = 801750484571895107L;

    @NotEmpty(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @Valid
    @NotEmpty(message = "商品配置列表")
    @ApiModelProperty(value = "商品配置列表")
    private List<ItemConfigUpdateReqDTO> itemConfigs;
}
