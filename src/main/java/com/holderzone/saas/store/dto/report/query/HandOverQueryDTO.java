package com.holderzone.saas.store.dto.report.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandOverQueryDTO
 * @date 2018/10/08 11:24
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class HandOverQueryDTO extends BaseQueryDTO {

    @ApiModelProperty(value = "交接班状态 -1:全部 0:未交班 1:已交班")
    private int state = -1;

    @ApiModelProperty(value = "开班人姓名")
    private String name;

}
