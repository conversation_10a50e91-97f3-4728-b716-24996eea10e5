package com.holderzone.saas.store.dto.member.grade;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountValidityDTO
 * @date 2018/08/29 下午3:28
 * @description //会员有效期
 * @program holder-saas-config-center
 */
public class AccountValidityDTO extends BaseDTO{

    @ApiModelProperty(value = "会员有效期类型 0：永久有效，1:时间限制",required = true)
    private Byte expiryType;

    @ApiModelProperty(value = "有效期年份，有时间限制时必传")
    private int year;

    public Byte getExpiryType() {
        return expiryType;
    }

    public void setExpiryType(Byte expiryType) {
        this.expiryType = expiryType;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }
}
