package com.holderzone.saas.store.dto.weixin.member;

import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Api("订单验证")
@Data
@Builder
public class WxCalculateConfirmDTO {

	private DineinOrderDetailRespDTO dineinOrderDetailRespDTO;

	private String errorMsg;

}
