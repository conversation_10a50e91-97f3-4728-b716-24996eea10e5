package com.holderzone.saas.store.dto.table;

import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableOrderDTO
 * @date 2019/01/02 17:07
 * @description 主要用于安卓端使用
 * @program holder-saas-store-dto
 */
@ApiModel
@AllArgsConstructor
@Data
@NoArgsConstructor
public class TableOrderDTO extends BaseDTO {

    @ApiModelProperty("业务主键")
    private String tableGuid;

    private String areaName;

    @ApiModelProperty("区域guid")
    private String areaGuid;

    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty("桌号")
    private String tableCode;

    @ApiModelProperty("主单号，如果有主单号，表示拼桌，如果主单号和订单号相同表示是主桌")
    private String mainOrderGuid;

    @ApiModelProperty("订单号")
    private String orderGuid;

    @ApiModelProperty("桌位座位数")
    private Integer seats;

    @ApiModelProperty("实际就餐人数")
    private Integer actualGuestsNo;

    @ApiModelProperty("桌台状态 -1=暂停使用;0=空闲;1=占用;2=预定;3=待清台")
    private Integer status;

    @ApiModelProperty("排序")
    private Integer sort;
    @ApiModelProperty("桌台详情状态 10=占用空台;11=占用锁定;12=占用并台;20=预定未锁定;21=预定已锁定")
    private Set<Integer> subStatus;

    @ApiModelProperty("开台时间")
    private LocalDateTime openTableTime;

    @ApiModelProperty("当前时间")
    private LocalDateTime currentTime = DateTimeUtils.now();

    @ApiModelProperty("并卓次数")
    private Integer combineTimes;

    @ApiModelProperty("联台次数")
    private Integer associatedTimes;

    @ApiModelProperty("是否打印预结单 0:未打印，1:打印")
    private Integer printPreBillNum;

    @ApiModelProperty("锁定的设备号")
    private String lockDeviceId;

    @ApiModelProperty("会员手机号")
    private String memberPhone;

    /**
     * 会员guid
     */
    @ApiModelProperty("会员guid")
    private String memberGuid;

}
