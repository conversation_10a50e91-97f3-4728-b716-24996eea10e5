package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableTagDTO
 * @date 2019/12/04 16:24
 * @description //TODO
 * @program IdeaProjects
 */
@Data
public class TableTagDTO extends BaseDTO {

    @ApiModelProperty("guid")
    private String guid;

    @ApiModelProperty("标签名称")
    @NotBlank(message = "标签名称不能为空")
    @Length(max = 5)
    private String tagName;

    @ApiModelProperty("排序")
    private Integer sort;


}