package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentSettleDTO
 * @date 2018/08/29 9:44
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class PaymentSettleDTO extends BaseBillDTO {

    @ApiModelProperty(value = "支付单集合")
    private List<PaymentDTO> paymentDTOS;

}
