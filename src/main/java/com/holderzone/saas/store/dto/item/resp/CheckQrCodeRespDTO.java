package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description 二维码过期校验返回实体
 * @date 2021/6/11 16:20
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "二维码过期校验返回实体")
public class CheckQrCodeRespDTO {

    @ApiModelProperty(value = "菜谱是否删除")
    private Boolean isDeletePlan;

    @ApiModelProperty(value = "是否过期")
    private Boolean isExpired;
}
