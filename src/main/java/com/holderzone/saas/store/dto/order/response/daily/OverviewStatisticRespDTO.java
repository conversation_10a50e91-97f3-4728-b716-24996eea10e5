package com.holderzone.saas.store.dto.order.response.daily;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/6/3
 * @since 1.8
 */
@Data
@ApiModel
public class OverviewStatisticRespDTO {

    @ApiModelProperty(value = "订单guid")
    private String guid;

    @ApiModelProperty(value = "主订单guid")
    private String mainOrderGuid;

    @ApiModelProperty(value = "客人数")
    private Integer guestCount;

    @ApiModelProperty(value = "入座时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "离座时间")
    private LocalDateTime checkoutTime;

    @ApiModelProperty(value = "是否联台单")
    private String associatedFlag;

    @ApiModelProperty(value = "联台单桌台列表")
    private String associatedTableGuids;
}
