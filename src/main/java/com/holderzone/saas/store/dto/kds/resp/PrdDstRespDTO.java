package com.holderzone.saas.store.dto.kds.resp;

import com.holderzone.framework.util.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PrdDstRespDTO implements Serializable {

    private static final long serialVersionUID = -1158994112223699540L;

    @ApiModelProperty("堂口列表")
    private List<PrdPointDTO> points;

    @ApiModelProperty("分组列表")
    private Page<DstGroupItemDTO> groups;

    @ApiModelProperty("商品列表")
    private Page<PrdDstItemDTO> items;

    @ApiModelProperty("订单列表")
    private Page<PrdDstOrderDTO> orders;
}
