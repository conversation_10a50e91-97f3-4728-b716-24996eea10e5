package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @description 点餐菜品请求实体
 * @date 2021/8/17 16:40
 */
@Data
@ApiModel(description = "点餐菜品请求实体")
public class OrderItemReqDTO {

    @ApiModelProperty("key:规格Guid（字符串）- value:下单数量（整数），不会有小数")
    private Map<String, Integer> skuAndQuantityMap;

    @ApiModelProperty("门店guid")
    private String storeGuid;
}
