package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@ApiModel(description = "掌控者智慧门店管理系统对接-菜品规格")
@Data
public class TcdDishSku {

    @ApiModelProperty(value = "菜品规格Id(平台方)")
    private Long id;

    @ApiModelProperty(value = "菜品Id(平台方)")
    private String dishId;

    @ApiModelProperty(value = "掌控者菜品规格Id(erp方)")
    private String holderDishSkuId;

    @ApiModelProperty(value = "掌控者实际菜品规格Id(erp方)")
    private String holderActualDishSkuId;

    @ApiModelProperty(value = "掌控者菜品Id(erp方)")
    private String holderDishId;

    @ApiModelProperty(value = "规格名称")
    private String spec;

    @ApiModelProperty(value = "菜品价格")
    private Float price;

    @ApiModelProperty(value = "菜品库存")
    private Double stock;

}
