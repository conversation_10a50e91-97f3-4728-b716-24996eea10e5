package com.holderzone.saas.store.dto.weixin.deal;

import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class UnMemberMessageDTO {

	private DineinOrderDetailRespDTO orderDetails;
	private String orderRecordGuid;
	private String openId;
}
