package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Accessors(chain = true)
@ApiModel("用户权限保存请求实体")
public class UserAuthoritySaveDTO implements Serializable {

    @ApiModelProperty(value = "被授权人guid")
    private String userGuid;

    @ApiModelProperty(value = "要保存的权限")
    private List<UserAuthorityDTO> userAuthorityDTOList;

    public UserAuthoritySaveDTO() {
    }

    public UserAuthoritySaveDTO(String userGuid, List<UserAuthorityDTO> userAuthorityDTOList) {
        this.userGuid = userGuid;
        this.userAuthorityDTOList = userAuthorityDTOList;
    }

    public UserAuthoritySaveDTO(List<UserAuthorityDTO> userAuthorityDTOList) {
        this.userAuthorityDTOList = userAuthorityDTOList;
    }

    public List<UserAuthorityDTO> getUserAuthorityDTOList() {
        return userAuthorityDTOList;
    }

    public void setUserAuthorityDTOList(List<UserAuthorityDTO> userAuthorityDTOList) {
        this.userAuthorityDTOList = userAuthorityDTOList;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof UserAuthoritySaveDTO)) return false;
        UserAuthoritySaveDTO that = (UserAuthoritySaveDTO) o;
        return Objects.equals(getUserAuthorityDTOList(), that.getUserAuthorityDTOList());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getUserAuthorityDTOList());
    }

    @Override
    public String toString() {
        return "UserAuthoritySaveDTO{" +
                "userGuid='" + userGuid + '\'' +
                ", userAuthorityDTOList=" + userAuthorityDTOList +
                '}';
    }
}
