package com.holderzone.saas.store.dto.item.req.price;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @BelongsProject: holder-saas-store-dto
 * @BelongsPackage: com.holderzone.saas.store.dto.item.req.price
 * @Author: li ao
 * @CreateTime: 2024-03-27  10:05
 * @Description:
 * @Version: 1.0
 */
@Data
@ApiModel(value = "商品对应的菜谱对应的分类")
public class PlanPriceItemTypeDTO {
    @ApiModelProperty(value = "分类id")
    private Long id;
    @ApiModelProperty(value = "分类名称")
    private String name;
}
