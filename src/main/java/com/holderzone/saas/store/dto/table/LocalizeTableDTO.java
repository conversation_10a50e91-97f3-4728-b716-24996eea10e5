package com.holderzone.saas.store.dto.table;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LocalizeTableDTO
 * @date 2019/11/13 15:12
 * @description //TODO
 * @program IdeaProjects
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LocalizeTableDTO implements Serializable {

    @ApiModelProperty("tableGuid")
    private String tableGuid;

    @ApiModelProperty("主单号")
    private String mainOrderGuid;

    @ApiModelProperty("订单号")
    private String orderGuid;

    /**
     * 桌台状态
     * -1=暂停使用
     * 0=空闲
     * 1=占用
     * 2=预定
     * 3=待清台
     */
    @ApiModelProperty("状态")
    private Integer status;

    /**
     * 10=占用空台
     * 11=占用锁定
     * 12=占用并台
     * 20=预定未锁定
     * 21=预定已锁定
     */
    @ApiModelProperty("子状态")
    private String subStatus;

    @ApiModelProperty("并台次数")
    private Integer combineTimes;

    @ApiModelProperty("开台时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime openTableTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime gmtModified;


}