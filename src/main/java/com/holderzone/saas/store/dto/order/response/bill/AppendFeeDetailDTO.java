package com.holderzone.saas.store.dto.order.response.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderFeeDetailDTO
 * @date 2019/01/28 11:16
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class AppendFeeDetailDTO implements Serializable {

    private static final long serialVersionUID = 6431318034577495868L;

    private Long guid;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "收费方式。0=按人，1=按桌。")
    private Integer type;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "退款数量")
    private BigDecimal refundCount;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 自定义金额退款分摊金额
     */
    @ApiModelProperty(value = "自定义金额退款分摊金额")
    private BigDecimal refundShareAmount;
}
