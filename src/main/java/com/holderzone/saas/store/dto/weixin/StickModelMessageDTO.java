package com.holderzone.saas.store.dto.weixin;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StickModelMessageDTO
 * @date 2019/03/15 14:18
 * @description 推送消息DTO
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StickModelMessageDTO {
    String messageType;

    String enterpriseGuid;

    List<WxTableStickDTO> message;
}
