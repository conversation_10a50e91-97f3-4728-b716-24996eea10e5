package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RetailBusinessDataRespDTO
 * @date 2019/11/04 16:53
 * @description
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RetailBusinessDataRespDTO extends BusinessDataRespDTO {

    @ApiModelProperty(value = "销售额")
    private BigDecimal totalFee;
    @ApiModelProperty(value = "客单价")
    private BigDecimal averagePrice;
    @ApiModelProperty(value = "退货订单数")
    private Integer refundOrderCount;
    @ApiModelProperty(value = "退货金额")
    private BigDecimal refundAmount;

    public static RetailBusinessDataRespDTO INSTANCE() {
        RetailBusinessDataRespDTO retailBusinessDataRespDTO = new RetailBusinessDataRespDTO(BigDecimal.ZERO, BigDecimal.ZERO, 0, BigDecimal.ZERO);
        retailBusinessDataRespDTO.setBusinessFee(BigDecimal.ZERO);
        retailBusinessDataRespDTO.setDiscountFee(BigDecimal.ZERO);
        retailBusinessDataRespDTO.setGuestCount(0);
        retailBusinessDataRespDTO.setOrderCount(0);
        return retailBusinessDataRespDTO;
    }
}
