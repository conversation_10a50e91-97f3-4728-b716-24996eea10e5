package com.holderzone.saas.store.dto.kds.resp;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> R
 * @date 2021/1/28 15:42
 * @description
 */
@Data
public class DisplayRuleItemRespDTO {

    /**
     * 全局唯一主键
     */
    private String guid;

    /**
     * 显示状态 0延迟显示 1分批显示
     */
    private Integer displayState;
    /**
     * 延迟时间
     */
    private Integer delayTime;
    /**
     * 批次
     */
    private Integer batch;

    /**
     * 是否全部门店
     */
    private Boolean isAllStore;

    /**
     * 规则类型 0显示规则 1菜品汇总
     */
    private Integer ruleType;
    /**
     * 生效状态 生效状态 0延迟生效 1立即生效
     */
    private Integer effectiveState;
    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 显示规则guid
     */
    private String ruleGuid;
    /**
     * 商品guid
     */
    private String itemGuid;
    /**
     * 商品名称
     */
    private String itemName;
    /**
     * 序号
     */
    private Integer sort;
}
