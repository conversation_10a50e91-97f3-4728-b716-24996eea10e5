package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@ApiModel(value = "接收前端单参数请求对应的pojo")
@EqualsAndHashCode(callSuper = true)
public class SingleDataPageDTO extends PageDTO {

    private static final long serialVersionUID = -3818182013879498606L;

    @ApiModelProperty(value = "业务请求参数")
    private String data;
}
