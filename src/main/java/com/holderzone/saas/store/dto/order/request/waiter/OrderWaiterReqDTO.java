package com.holderzone.saas.store.dto.order.request.waiter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> R
 * @date 2020/11/19 9:57
 * @description
 */
@Data
@ApiModel(value = "订单服务员记录请求参数DTO")
public class OrderWaiterReqDTO {
    @ApiModelProperty(value = "订单的guid", required = true)
    private String orderGuid;
    @ApiModelProperty(value = "服务员", required = true)
    private List<OrderWaiterInfoDTO> orderWaiterInfoDTOList;
}
