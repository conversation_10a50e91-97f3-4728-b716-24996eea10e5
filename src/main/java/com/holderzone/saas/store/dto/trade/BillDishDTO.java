package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.order.common.SkuPropertyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillDishDTO
 * @date 2018/07/26 13:42
 * @description 结算账单菜品实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class BillDishDTO extends BaseBillDTO implements Serializable {

    /**
     * 账单菜品GUID
     */
    @ApiModelProperty(value = "账单guid")
    private String billDishGuid;


    @ApiModelProperty(value = "菜品类型guid")
    private String dishTypeGuid;

    @ApiModelProperty(value = "菜品类型名称")
    private String dishTypeName;

    /**
     * 菜品Guid
     */
    @ApiModelProperty(value = "菜品Guid")
    private String dishGuid;

    /**
     * 菜品名称
     */
    @ApiModelProperty(value = "菜品名称")
    private String dishName;

    /**
     * 菜品的规格
     */
    @ApiModelProperty(value = "菜品的规格")
    private String skuGuid;

    /**
     * 规格名称
     */
    @ApiModelProperty(value = "规格名称")
    private String skuName;

    /**
     * 计数单位
     */
    @ApiModelProperty(value = "计数单位")
    private String unit;

    /**
     * 计数单位code(DishUnitEnum)
     */
    @ApiModelProperty(value = "计数单位code(DishUnitEnum)")
    private Integer unitCode;

    /**
     * 菜品套餐类型 0:单规格、1：多规格、2：套餐主项 、3：套餐子项 4：称重
     */
    @ApiModelProperty(value = "菜品套餐类型(0:单规格、1：多规格、2：套餐主项 、3：套餐子项 4：称重")
    private Integer packageDishType;

    /**
     * 如果是套餐子项，表示本套餐的guid
     */
    @ApiModelProperty(value = "如果是套餐子项，表示本套餐的guid")
    private String parentDishGuid;

    /**
     * 账单菜品数量
     */
    @ApiModelProperty(value = "账单菜品数量")
    private BigDecimal orderCount;

    /**
     * 价格
     */
    @ApiModelProperty(value = "价格")
    private BigDecimal price;
    /**
     * 会员价
     */
    @ApiModelProperty(value = "会员价")
    private BigDecimal memberPrice;

    /**
     * 做法总额
     */
    @ApiModelProperty(value = "做法总额")
    private BigDecimal practiceTotal;

    /**
     * 是否赠送 0:否1：是
     */
    @ApiModelProperty(value = "是否赠送 0:否1：是")
    private Byte gift;

    /**
     * 是否参与整单折扣 0:否1：是
     */
    @ApiModelProperty(value = "是否参与整单折扣 0:否1：是")
    private Byte joinWholeDiscount;

    /**
     * 是否参与会员折扣 0:否1：是
     */
    @ApiModelProperty(value = "是否参与会员折扣 0:否1：是")
    private Byte joinMemberDiscount;

    /**
     * 创建下单时间
     */
    @ApiModelProperty(value = "创建下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStamp;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 做法明细
     */
    @ApiModelProperty(value = "做法明细")
    private List<BillDishPracticeDTO> dishPractices;

    /**
     * 套餐子项
     */
    @ApiModelProperty(value = "套餐子项")
    private List<BillDishDTO> childBillDishes;

    /**
     * 菜品附加属性
     */
    @ApiModelProperty(value = "菜品附加属性")
    private List<BillDishSkuPropertyDTO> skuProperties;

    @ApiModelProperty(value = "sku总和")
    private BigDecimal skuPropertyTotal;
}
