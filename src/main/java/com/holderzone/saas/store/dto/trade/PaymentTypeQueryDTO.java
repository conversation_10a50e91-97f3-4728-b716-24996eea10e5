package com.holderzone.saas.store.dto.trade;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeQueryDTO
 * @date 2018/09/14 10:03
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class PaymentTypeQueryDTO extends BaseDTO {

    @ApiModelProperty(value = "guid")
    private String storeGuid;

    @ApiModelProperty(value = "0 全部 1 默认 2 自定义")
    private Integer source;

    /**
     * @see com.holderzone.saas.store.enums.order.TradeModeEnum
     */
    @ApiModelProperty(value = "订单模式")
    private Integer tradeMode;

}
