package com.holderzone.saas.store.dto.order.request.waiter;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> R
 * @date 2020/11/30 14:27
 * @description
 */
@Data
@ApiModel(value = "订单服务员详情分页查询返回参数DTO")
public class OrderWaiterPageDetailsRespDTO {
    @ApiModelProperty(value = "开台时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "结账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTime;

    @ApiModelProperty(value = "桌台名称")
    private String diningTableName;

    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "订单金额")
    private String orderFee;

    @ApiModelProperty(value = "服务员编号")
    private String waiterNo;

    @ApiModelProperty(value = "服务员名称")
    private String waiterName;

    @ApiModelProperty(value = "服务员类型")
    private Integer waiterType;

    @ApiModelProperty(value = "门店名称")
    private String storeName;
}
