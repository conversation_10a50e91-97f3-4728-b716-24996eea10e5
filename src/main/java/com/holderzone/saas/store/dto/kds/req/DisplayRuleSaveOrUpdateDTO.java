package com.holderzone.saas.store.dto.kds.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.kds.resp.DisplayItemRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayStoreRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 新增和更新实体
 * @date 2021/2/1 10:23
 */

@Data
public class DisplayRuleSaveOrUpdateDTO {

    /**
     * 规则guid
     */
    @ApiModelProperty(value = "规则guid")
    private String ruleGuid;

    /**
     * 显示状态 0延迟显示 1分批显示
     */
    @ApiModelProperty(value = "显示状态 0延迟显示 1分批显示")
    private Integer displayState;

    /**
     * 延迟时间
     */
    @ApiModelProperty(value = "延迟时间")
    private Integer delayTime;

    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    private Integer batch;

    /**
     * 生效状态 0延迟生效 1立即生效
     */
    @ApiModelProperty(value = "生效状态 0延迟生效 1立即生效")
    private Integer effectiveState;

    /**
     * 生效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "生效时间")
    private LocalDateTime effectiveTime;

    /**
     * 是否全部门店
     */
    @ApiModelProperty(value = "是否全部门店")
    private Boolean isAllStore;

    /**
     * 规则类型 0显示批次 1菜品汇总
     */
    @ApiModelProperty(value = "规则类型 0显示批次 1菜品汇总")
    private Integer ruleType;

    /**
     * 门店列表
     */
    @ApiModelProperty(value = "门店列表")
    private List<DisplayStoreRespDTO> storeList;

    /**
     * 商品列表
     */
    @ApiModelProperty(value = "商品列表")
    private List<DisplayItemRespDTO> itemList;

    /**
     * 品牌guid
     */
    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

}
