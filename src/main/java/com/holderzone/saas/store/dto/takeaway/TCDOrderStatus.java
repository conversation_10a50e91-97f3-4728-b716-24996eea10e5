package com.holderzone.saas.store.dto.takeaway;

/**
 * 订单状态:
 * PENDING         --->1 待处理(外卖订单)、未使用（到店消费）、待发货（快寄配送到家）,
 * COOKING         --->2 出餐中（外卖订单）、已发货（快寄配送到家）,
 * PERSON_PENDING  --->3 配送员待接单（外卖订单）
 * TAKE_A_SINGLE   --->4 取单中（外卖订单）,
 * DISTRIBUTION    --->5 配送中（外卖订单）,
 * FINISH          --->6 已完成（外卖订单、到店消费、快寄配送到家）,
 * CANCELLED       --->7 已取消（外卖订单、到店消费、快寄配送到家）,
 * INVALID         --->8 失效
 *
 * <AUTHOR>
 * @Description 订单状态
 * @time 2017年7月25日 下午4:43:05
 */
public interface TCDOrderStatus {

    /**
     * 待处理(外卖订单)、未使用（到店消费）、待发货（快寄配送到家）,
     */
    String PENDING = "PENDING";

    /**
     * 出餐中（外卖订单）、已发货（快寄配送到家）,
     */
    String COOKING = "COOKING";

    /**
     * 配送员待接单（外卖订单）
     */
    String PERSON_PENDING = "PERSON_PENDING";

    /**
     * 取单中（外卖订单）,
     */
    String TAKE_A_SINGLE = "TAKE_A_SINGLE";

    /**
     * 配送中（外卖订单）,
     */
    String DISTRIBUTION = "DISTRIBUTION";

    /**
     * 已完成（外卖订单、到店消费、快寄配送到家）,
     */
    String FINISH = "FINISH";

    /**
     * 已取消（外卖订单、到店消费、快寄配送到家）,
     */
    String CANCELLED = "CANCELLED";

    /**
     * 失效
     */
    String INVALID = "INVALID";
}
