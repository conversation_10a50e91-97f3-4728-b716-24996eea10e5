package com.holderzone.saas.store.dto.log.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LogRespDTO
 * @date 2018/09/27 上午10:21
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "日志返回给前端对象")
public class LogRespDTO implements Serializable {
    @ApiModelProperty(value = "该条日志唯一标识")
    private String logGuid;
    @ApiModelProperty(value = "操作时间")
    private String operationTime;
    @ApiModelProperty(value = "操作模块")
    private String module;
    @ApiModelProperty(value = "操作子功能")
    private String button;
    @ApiModelProperty(value = "操作对象")
    private String[] target;
    @ApiModelProperty(value = "操作类型")
    private String method;
    @ApiModelProperty(value = "操作人姓名")
    private String createBy;
    @ApiModelProperty(value = "日志详情")
    private String logContent;
    @ApiModelProperty(value = "操作平台")
    private String platform;
    @ApiModelProperty("门店")
    private String store;
}
