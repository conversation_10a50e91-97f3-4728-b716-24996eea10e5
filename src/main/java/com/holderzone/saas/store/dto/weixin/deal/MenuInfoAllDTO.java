package com.holderzone.saas.store.dto.weixin.deal;

import com.holderzone.saas.store.dto.weixin.resp.StoreActivityRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel("微信：门店配置，商品，分组")
public class MenuInfoAllDTO {

    /**
     * 售卖模式
     */
    @ApiModelProperty(value = "1普通模式,2菜谱模式")
    private Integer salesModel;

    /**
     * 菜谱售卖模式中，存在价格方案GUID
     */
    @ApiModelProperty(value = "价格方案GUID")
    private String pricePlanGuid;

    private List<ItemInfoDTO> itemInfoDTOS;

    private List<ItemTypeDTO> itemTypeDTOS;

    List<UserMemberCardCacheDTO> cardList;

    private MenuInfoConfigDTO menuInfoConfigDTO;

    @ApiModelProperty(value = "订单id")
    private String orderGuid;

    @ApiModelProperty("是否跳转到订单详情 1:跳转,0:不跳转")
    private Integer isJump = 0;

    @ApiModelProperty("是否是会员")
    private Boolean isLogin;

    @ApiModelProperty("是否存在附加费")
    private Boolean surchargeFlag;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "点餐人数")
    private Integer guestCount;

    /***
     *  二维码类型:0普通二维码，1带参数二维码
     */
    @ApiModelProperty(value = "二维码类型:0普通二维码，1带参数二维码")
    private Integer qrCodeType;

    @ApiModelProperty(value = "门店活动列表")
    private List<StoreActivityRespDTO> storeActivityList;

}
