package com.holderzone.saas.store.dto.log.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessLogRecordDTO
 * @date 2018/12/05 14:24
 * @description //TODO
 */
@Data
public class BusinessLogRecordDTO {

    //操作人
    @NotBlank(message = "创建人不能为空")
    private String createBy;
    //操作时间
    @NotNull(message = "操作时间不能为空")
    private LocalDateTime operationTime;
    //操作对象(json数组)

    private String target;
    //操作方法（增删改)
    @NotNull(message = "方法不能为空")
    private String method;
    //操作模块(门店菜品，菜品库）
    private String module;
    //操作子功能(编辑，移动，新建等）
    private String button;
    //平台(商户后台等）
    @NotNull(message = "平台信息")
    private String platform;
    @NotBlank(message = "日志内容不能为空")
    private String logContent;

    private String storeGuid;

    private String storeName;
    private String params;
    private Integer recordType;
    @NotBlank(message = "请求路径不能为空")
    private String requestUri;

}
