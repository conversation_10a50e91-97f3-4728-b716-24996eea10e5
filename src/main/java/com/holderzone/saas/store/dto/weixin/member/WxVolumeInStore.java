package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Api("门店下的优惠券")
@Data
public class WxVolumeInStore extends WxProductStoreDTO{

	@ApiModelProperty("0:全部商品,1:部分商品")
	private Integer supportAll;

	@ApiModelProperty("支持的商品列表")
	private List<WxVolumeProductRespDTO> wxVolumeProductRespDTOS;
}
