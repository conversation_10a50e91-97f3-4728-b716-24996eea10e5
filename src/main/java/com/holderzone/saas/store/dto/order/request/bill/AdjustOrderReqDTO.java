package com.holderzone.saas.store.dto.order.request.bill;


import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "调整单请求参数对象")
public class AdjustOrderReqDTO extends BaseDTO {

    @ApiModelProperty(value = "订单guid")
    @OrderLockField
    @NotNull(message = "订单guid不能为空")
    private String orderGuid;

    @ApiModelProperty(value = "订单商品集合")
    private List<DineInItemDTO> dineInItemDTOList;

    @ApiModelProperty(value = "线下退款金额")
    private BigDecimal refundFee;

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "调整单设备类型",example = "3")
    private Integer recoveryDeviceType;

    @ApiModelProperty(value = "调整单类型,支持快餐、正餐、反结账新订单")
    private String tradeMode;

    @ApiModelProperty(value = "操作人员名称")
    private String operatorName;


}