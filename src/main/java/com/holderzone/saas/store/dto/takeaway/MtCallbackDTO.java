package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 此处的ePoiId字段需要注意
 * <p>
 * 1. 传输上
 * meituan->merchant 是requestParam填充
 * merchant->producer 是requestBody序列化
 * producer->process 是requestBody反序列化
 * <p>
 * ePoiId不做特殊处理的话：
 * <p>
 * requestParam填充没问题。
 * <p>
 * merchant->producer会将ePoiId字段序列化成epoiId；
 * producer->process会从epoiId反序列化到ePoiId字段中；
 * 传输上没问题，但是日志打印时就会给人歧义。所以使用JsonAutoDetect处理。
 * <p>
 * 2. checkSignature时有问题：
 * Bean2Map工具类会将ePoiId字段解析承EPoiId，所以验证签名前需要替换字符串EPoiId为ePoiId。
 */
@Data
@ApiModel
@NoArgsConstructor
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY,
        getterVisibility = JsonAutoDetect.Visibility.NONE)
public class MtCallbackDTO implements Serializable {

    private static final long serialVersionUID = 2108474328072905524L;

    @ApiModelProperty("ERP厂商入驻新美大餐饮平台得到的唯一身份表示")
    @NotBlank(message = "开发者身份不得为空")
    private Integer developerId;

    @ApiModelProperty("数字签名，签名方式与菜品类接口计算签名一致")
    @NotBlank(message = "数字签名不得为空")
    private String sign;

    @ApiModelProperty("门店绑定的授权token，将来的门店业务操作必须要传")
    @NotBlank(message = "门店绑定的授权token不得为空", groups = Bind.class)
    private String appAuthToken;

    @ApiModelProperty("业务类型, 1团购、2外卖、3闪惠、5支付、7预定、8全渠道会员")
    @NotBlank(message = "业务类型不得为空", groups = {Bind.class, Unbind.class})
    private String businessId;

    @ApiModelProperty("美团门店id")
    @NotBlank(message = "美团门店id不得为空", groups = Bind.class)
    private String poiId;

    @ApiModelProperty("美团门店名称")
    @NotBlank(message = "美团门店名称不得为空", groups = Bind.class)
    private String poiName;

    @ApiModelProperty("门店绑定的时间戳")
    @NotNull(message = "门店绑定的时间戳不得为空", groups = {PrivacyDegrade.class, Bind.class, Unbind.class})
    private Long timestamp;

    @ApiModelProperty("门店绑定时，传入的ERP厂商分配给门店的唯一标识 最大长度100")
    @NotBlank(message = "ERP厂商唯一标识不得为空",
            groups = {OrderCreate.class, OrderCancel.class, OrderRefund.class, OrderConfirm.class,
                    OrderFinish.class, OrderShippingStatus.class, OrderPartRefund.class,
                    Bind.class, Unbind.class})
    private String ePoiId;

    @ApiModelProperty("订单详情信息")
    @NotBlank(message = "订单详情不得为空", groups = {OrderCreate.class, OrderConfirm.class, OrderFinish.class})
    private String order;

    @ApiModelProperty("订单取消信息")
    @NotBlank(message = "订单取消信息不得为空", groups = OrderCancel.class)
    private String orderCancel;

    @ApiModelProperty("退款消息")
    @NotBlank(message = "退款消息不得为空", groups = OrderRefund.class)
    private String orderRefund;

    @ApiModelProperty("订单配送信息")
    @NotBlank(message = "订单配送信息不得为空", groups = OrderShippingStatus.class)
    private String shippingStatus;

    @ApiModelProperty("门店状态信息")
    @NotBlank(message = "门店状态信息不得为空", groups = OrderPoiStatus.class)
    private String poiStatus;

    @ApiModelProperty("部分退款信息")
    @NotBlank(message = "部分退款信息不得为空", groups = OrderPartRefund.class)
    private String partOrderRefund;

    @ApiModelProperty("订单结算信息详情数据")
    private String tradeDetail;

    public interface OrderCreate {
    }

    public interface OrderCancel {
    }

    public interface OrderRefund {
    }

    public interface OrderConfirm {
    }

    public interface OrderFinish {
    }

    public interface OrderShippingStatus {
    }

    public interface OrderPoiStatus {
    }

    public interface OrderPartRefund {
    }

    public interface PrivacyDegrade {
    }

    public interface Bind {
    }

    public interface Unbind {
    }
}
