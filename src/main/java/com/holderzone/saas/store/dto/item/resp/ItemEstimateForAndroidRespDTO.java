package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemEstimateForAndroidDTO
 * @date 2019/05/16 14:15
 * @description //TODO 安卓菜品估清同步
 * @program holder-saas-aggregation-app
 */
@Data
@Builder
@ApiModel(value = "安卓菜品估清同步查询返回DTO")
@AllArgsConstructor
@NoArgsConstructor
public class ItemEstimateForAndroidRespDTO {

    /**
     *sku 业务主键
     */
    @ApiModelProperty(value = "sku 业务主键")
    private String skuGuid;

    /**
     * 是否估清 1：否  2：是
     */
    @Builder.Default
    @ApiModelProperty(value = "是否估清 1:否 2:是")
    private Integer isSoldOut = 1;

    /**
     * 当前剩余数量
     */
    @Builder.Default
    @ApiModelProperty(value = "当前剩余数量")
    private BigDecimal residueQuantity = BigDecimal.ZERO;

    /**
     * 提醒阈值
     */
    @Builder.Default
    @ApiModelProperty(value = "提醒阈值")
    private BigDecimal reminderThreshold = BigDecimal.TEN;


}
