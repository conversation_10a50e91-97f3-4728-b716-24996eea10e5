package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 微信订单信息实体
 * @date 2022/3/29 14:28
 * @className: WechatOrderInfoDTO
 */
@Data
@ApiModel("微信订单信息实体")
@Accessors(chain = true)
public class WechatOrderInfoDTO implements Serializable {

    private static final long serialVersionUID = 2849907708477869576L;

    @ApiModelProperty("微信订单金额（接单&未接单）")
    private BigDecimal wechatOrderFee;

    /**
     * 微信订单状态（来自订单表的状态）： 1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废
     */
    @ApiModelProperty("微信订单状态")
    private Integer orderState;

    /**
     * 下单来源 0：PC服务端,1：PC平板,2：小店通,3：一体机,4：POS机,5：云平板,6：点菜宝(M1),
     * 7：PV1(带刷卡的点菜宝),9：厨房显示系统,10: 取餐屏,12：微信 15通吃岛  16 翼惠天下
     */
    @ApiModelProperty("下单来源")
    private Integer orderSource;

    @ApiModelProperty(value = "trade的订单guid")
    private String orderGuid;
}
