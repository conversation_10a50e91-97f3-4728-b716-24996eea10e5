package com.holderzone.saas.store.dto.user.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;

@Accessors(chain = true)
public class PermissionsReqDTO {

    @ApiModelProperty("员工guid")
    private String userGuid;

    @ApiModelProperty("终端code")
    private String terminalCode;

    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("资源code")
    private String sourceCode;

    @ApiModelProperty("权限来源：0，自己创建；1，来自角色列表")
    private Integer sourceFrom;

    @ApiModelProperty("员工guid列表")
    private List<String> userGuids;

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public Integer getSourceFrom() {
        return sourceFrom;
    }

    public void setSourceFrom(Integer sourceFrom) {
        this.sourceFrom = sourceFrom;
    }

    public PermissionsReqDTO() {
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getTerminalCode() {
        return terminalCode;
    }

    public void setTerminalCode(String terminalCode) {
        this.terminalCode = terminalCode;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public List<String> getUserGuids() {
        return userGuids;
    }

    public void setUserGuids(List<String> userGuids) {
        this.userGuids = userGuids;
    }

    @Override
    public String toString() {
        return "PermissionsReqDTO{" +
                "userGuid='" + userGuid + '\'' +
                ", terminalCode='" + terminalCode + '\'' +
                ", storeGuid='" + storeGuid + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PermissionsReqDTO)) return false;
        PermissionsReqDTO that = (PermissionsReqDTO) o;
        return Objects.equals(getUserGuid(), that.getUserGuid()) && Objects.equals(getTerminalCode(), that.getTerminalCode()) && Objects.equals(getStoreGuid(), that.getStoreGuid());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getUserGuid(), getTerminalCode(), getStoreGuid());
    }
}
