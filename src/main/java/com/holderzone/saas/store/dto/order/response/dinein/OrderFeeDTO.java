package com.holderzone.saas.store.dto.order.response.dinein;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderFeeDTO
 * @date 2019/01/17 16:57
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class OrderFeeDTO {

    /**
     * 订单金额（商品总额+附加费）
     */
    private BigDecimal orderFee;

    /**
     * 附加费
     */
    private BigDecimal appendFee;

    /**
     * 实收金额（订单金额-折扣金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））
     */
    private BigDecimal actuallyPayFee;

    /**
     * 应收金额（实收金额-订金（押金），只结账前用）
     */
    private BigDecimal shouldPayFee;

    /**
     * 找零（收款-应收金额）
     */
    private BigDecimal changeFee;

    /**
     * 预付金（反结账原单聚合支付转入）
     */
    private BigDecimal prepayFee;

    /**
     * 定金
     */
    private BigDecimal reserveFee;

}
