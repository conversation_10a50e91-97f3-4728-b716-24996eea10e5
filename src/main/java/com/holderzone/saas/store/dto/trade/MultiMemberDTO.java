package com.holderzone.saas.store.dto.trade;

import com.mos.secure.ext.annotations.DesensitizationProp;
import com.mos.secure.ext.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 多张会员卡支付信息
 *
 * <AUTHOR>
 * @date 2025/7/8
 * @since 1.8
 */
@Data
public class MultiMemberDTO {

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "会员名称")
    @DesensitizationProp(SensitiveTypeEnum.CHINESE_NAME)
    private String memberName;

    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    @ApiModelProperty(value = "会员电话")
    @DesensitizationProp(SensitiveTypeEnum.MOBILE_PHONE)
    private String memberPhone;
}
