package com.holderzone.saas.store.dto.report;

import java.math.BigDecimal;

/**
 * 菜品销售统计返回实体
 * <AUTHOR>
 * @date 2018/09/27 下午 15:53
 * @description
 */
public class OrderDishDTO {

    /**
     * 菜品编码
     */
    private String dishCode;

    /**
     * 菜品名称
     */
    private String dishName;

    /**
     * 菜品类型
     */
    private String dishType;

    /**
     * 是否套餐
     */
    private Integer packageDish;

    /**
     * 菜品数量
     */
    private BigDecimal dishCount;

    /**
     * 菜品金额
     */
    private BigDecimal dishAmount;

    /**
     * 菜品金额占比
     */
    private Double dishAmountRatio;

    public Double getDishAmountRatio() {
        return dishAmountRatio;
    }

    public void setDishAmountRatio(Double dishAmountRatio) {
        this.dishAmountRatio = dishAmountRatio;
    }

    public String getDishCode() {
        return dishCode;
    }

    public void setDishCode(String dishCode) {
        this.dishCode = dishCode;
    }

    public String getDishName() {
        return dishName;
    }

    public void setDishName(String dishName) {
        this.dishName = dishName;
    }

    public String getDishType() {
        return dishType;
    }

    public void setDishType(String dishType) {
        this.dishType = dishType;
    }

    public Integer getPackageDish() {
        return packageDish;
    }

    public void setPackageDish(Integer packageDish) {
        this.packageDish = packageDish;
    }

    public BigDecimal getDishCount() {
        return dishCount;
    }

    public void setDishCount(BigDecimal dishCount) {
        this.dishCount = dishCount;
    }

    public BigDecimal getDishAmount() {
        return dishAmount;
    }

    public void setDishAmount(BigDecimal dishAmount) {
        this.dishAmount = dishAmount;
    }
}
