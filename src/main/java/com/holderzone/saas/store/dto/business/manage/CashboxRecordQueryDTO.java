package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CashboxRecordDO
 * @date 2018/07/29 下午5:42
 * @description 钱箱记录查询DTO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class CashboxRecordQueryDTO implements Serializable {

    private static final long serialVersionUID = 7128129943181174181L;

    /**
     * 钱箱记录guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "钱箱记录guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "钱箱记录guid", required = true)
    private String cashboxRecordGuid;
}
