package com.holderzone.saas.store.dto.report.openapi;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.open.BaseReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 销售明细查询入参
 */
@Data
public class SaleDetailQueryDTO extends BaseReq {

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "用于分页查询的游标，由上一次调用返回，首次调用可不填")
    private Long cursor;

    @ApiModelProperty(value = "返回的最大记录数，整型，最大值1000，默认值500，超过最大值时取最大值")
    private Integer limit;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "请求来源")
    private String requestSource;
}
