package com.holderzone.saas.store.dto.config.req;

import com.holderzone.saas.store.enums.business.PrintItemOrderEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;


/**
 * 修改商品打印顺序配置
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PrintItemOrderConfigReq implements Serializable {

    private static final long serialVersionUID = 4641346640564252884L;

    @NotBlank(message = "门店guid不能为空")
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 打印小票(点菜单、菜品清单)商品顺序
     * asc
     * desc
     *
     * @see PrintItemOrderEnum
     */
    @NotBlank(message = "顺序不能为空")
    private String printItemOrder;

    private String deviceId;

    private Integer autoPrint;

}
