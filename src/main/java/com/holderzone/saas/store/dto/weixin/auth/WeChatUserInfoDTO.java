package com.holderzone.saas.store.dto.weixin.auth;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 微信用户信息
 * @date 2021/9/6 19:57
 * @className: WeChatUserInfoDTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "微信用户信息")
public class WeChatUserInfoDTO implements Serializable {

    private static final long serialVersionUID = -5579201630565285674L;

    /**
     * openid
     */
    @ApiModelProperty("openid")
    private String openid;

    /**
     * 昵称
     */
    @ApiModelProperty("昵称")
    private String nickname;

    /**
     * 性别
     */
    @ApiModelProperty("性别")
    private String sex;

    /**
     * 语言
     */
    @ApiModelProperty("语言")
    private String language;


    /**
     * 城市
     */
    @ApiModelProperty("城市")
    private String city;


    /**
     * 省会
     */
    @ApiModelProperty("省会")
    private String province;


    /**
     * 国家
     */
    @ApiModelProperty("国家")
    private String country;


    /**
     * 头像url
     */
    @ApiModelProperty("头像url")
    private String headimgurl;

    /**
     * unionid
     */
    @ApiModelProperty("unionid")
    private String unionid;
}
