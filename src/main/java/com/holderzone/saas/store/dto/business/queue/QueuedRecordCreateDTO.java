package com.holderzone.saas.store.dto.business.queue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueuedRecordCreateDTO
 * @date 2018/07/27 下午6:29
 * @description 新增
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class QueuedRecordCreateDTO {

    /**
     * 门店guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店名称不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    /**
     * 创建人guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "创建人guid，且不得超过45个字符")
    @ApiModelProperty(value = "创建人guid", required = true)
    private String createUserGuid;

    /**
     * 客人数
     */
    @NotNull
    @Min(value = 1, message = "客人数不得为空，且必须大于1")
    @ApiModelProperty(value = "客人数", required = true)
    private Integer customerCount;

    /**
     * 客人手机号
     */
    @ApiModelProperty("客人手机号，可以为空")
    private String customerTel;
}
