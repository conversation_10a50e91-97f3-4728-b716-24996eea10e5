package com.holderzone.saas.store.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.trade.AddtionalFeeDTO;
import com.holderzone.saas.store.dto.trade.BaseBillDTO;
import com.holderzone.saas.store.dto.trade.BillDiscountDTO;
import com.holderzone.saas.store.dto.trade.PayQueryRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillRespDTO
 * @date 2018/09/08 15:28
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class BillRespDTO extends BaseBillDTO {

    /**
     * 创建账单时间
     */
    @ApiModelProperty(value = "创建账单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTimestamp;

    @ApiModelProperty(value = "结账员工guid")
    private String checkoutStaffGuid;

    @ApiModelProperty(value = "结账员工name")
    private String checkoutStaffName;

    /**
     * 结算完成时间
     */
    @ApiModelProperty(value = "结算完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutedTimestamp;

    /**
     * 反结账员工信息
     */
    @ApiModelProperty(value = "反结账员工信息")
    private String recoveryStaffGuid;

    @ApiModelProperty(value = "反结账员工信息")
    private String recoveryStaffName;

    @ApiModelProperty(value = "账单来源：一体机点餐下单（1）、POS设备（2）、M1（3）、平板点餐（4）、微信公众号（5）、美团外卖（6）饿了么外卖（7）", required = true)
    private String billSourceName;

    /**
     * 状态(10：待支付  0：富民支付中  1：支付中(已请求上游） 2：支付成功 3：支付失败 4：退款  5：已关闭  7：反结账状态 -1 删除)
     */
    @ApiModelProperty(value = " 状态(10：待支付  0：富民支付中  1：支付中(已请求上游） 2：支付成功 3：支付失败 4：退款  5：已关闭  7：反结账状态 -1 删除)")
    private Integer state;

    /**
     * 反结账UUID
     */
    @ApiModelProperty(value = "反结账UUID")
    private String recoveryUuid;

    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    @ApiModelProperty(value = "会员电话")
    private String telPhoneNo;

    /**
     * 折扣详情
     */
    @ApiModelProperty(value = "折扣详情")
    private List<BillDiscountDTO> billDiscountDTOList;

    /**
     * 附加费合计
     */
    @ApiModelProperty(value = "附加费合计")
    private List<AddtionalFeeDTO> addtionalFeeDOList;

    @ApiModelProperty(value = "支付详情")
    List<PayQueryRespDTO> payQueryRespDTOS;

    /**
     * 总折扣=couponsFee+itemTotalDiscountFee+memberDiscountFee（或者wholeOrderDiscountFee）+memberScoreDiscountFee+modifyDirectlyFee
     */
    @ApiModelProperty(value = "总折扣")
    private BigDecimal totalDiscountFee;

    /**
     * 消费总计=菜品信息中所有菜品单价*数量+各个菜品做法附加费
     */
    @ApiModelProperty(value = "消费总计")
    private BigDecimal consumerTotalFee;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    private BigDecimal shouldPayFee;
    /**
     * 实付金额
     */
    @ApiModelProperty(value = "实付金额")
    private BigDecimal actuallyPayFee;

    /**
     * 附加费合计
     */
    @ApiModelProperty(value = "附加费合计")
    private BigDecimal addtionalFee;

    /**
     * 优惠券折扣费总计
     */
    @ApiModelProperty(value = "优惠券折扣费总计")
    private BigDecimal couponsFee;
    /**
     * 单品折扣费总计
     */
    @ApiModelProperty(value = "单品折扣费总计")
    private BigDecimal itemTotalDiscountFee;
    /**
     * 会员折扣费用
     */
    @ApiModelProperty(value = "会员折扣费用")
    private BigDecimal memberDiscountFee;
    /**
     * 整单折扣费用
     */
    @ApiModelProperty(value = "整单折扣费用")
    private BigDecimal wholeOrderDiscountFee;
    /**
     * 会员积分折扣
     */
    @ApiModelProperty(value = "会员积分折扣")
    private BigDecimal memberScoreDiscountFee;
    /**
     * 直接修改价格费用(最后一步计算，之前任何价格有改动，该项都清零)
     */
    @ApiModelProperty(value = "直接修改价格费用")
    private BigDecimal modifyDirectlyFee;

    /**
     * 菜品优惠券折扣
     */
    @ApiModelProperty(value = "菜品优惠券折扣")
    private BigDecimal dishCouponsFee;

    /**
     * 赠送菜品折扣
     */
    @ApiModelProperty(value = "赠送菜品折扣")
    private BigDecimal presentDishFee;

    /**
     * 退款金额
     */
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundFee;

}
