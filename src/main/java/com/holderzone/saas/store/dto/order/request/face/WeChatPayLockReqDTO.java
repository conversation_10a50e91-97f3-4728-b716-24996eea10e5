package com.holderzone.saas.store.dto.order.request.face;

import com.holderzone.saas.store.dto.table.anno.LockField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FacePayCompensateReqDTO
 * @date 2019/04/08 13:56
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class WeChatPayLockReqDTO {

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "version")
    @LockField
    private Integer version;



}
