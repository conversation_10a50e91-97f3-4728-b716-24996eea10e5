package com.holderzone.saas.store.dto.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MsgRespDTO
 * @date 2018/09/25 10:23
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class MsgRespDTO implements Serializable {

    @ApiModelProperty(value = "消息guid")
    private String messageGuid;

    @ApiModelProperty(value = "消息标题")
    private String subject;

    @ApiModelProperty(value = "消息体")
    private String content;

    @ApiModelProperty(value = "推送的消息类型,由推送方决定")
    private Integer messageType;

    @ApiModelProperty(value = "平台，1:云端平台,2:其他业务平台")
    private String platform;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名")
    private String storeName;

    @ApiModelProperty(value = "状态 0:未读，1:已读")
    private Integer state;

    @ApiModelProperty(value = "当前时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime currentTime;

}
