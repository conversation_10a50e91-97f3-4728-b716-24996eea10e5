package com.holderzone.saas.store.dto.invoice.ipass;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@ApiModel(description = "生成订单二维码")
@Data
public class IpassGenerateOrderInvoiceDTO implements Serializable {

    @ApiModelProperty(value = "门店id")
    private String storeId;

    @ApiModelProperty(value = "订单流水号")
    private String orderNo;

    @ApiModelProperty(value = "回调地址")
    private String callbackUrl;

    @ApiModelProperty(value = "用户账号")
    private String account;

    @ApiModelProperty(value = "电子税务局登录账号（dlzh）对应开票员的姓名")
    private String kpr;

    @ApiModelProperty(value = "申请人，一般与kpr相同")
    private String sqr;

    @ApiModelProperty(value = "数电专票传01、数电普票传02")
    private String fplxdm;

    @ApiModelProperty(value = "价税合计（订单金额）")
    private String jshj;

    @ApiModelProperty(value = "合计金额，不含税金额")
    private String hjje;

    @ApiModelProperty(value = "合计税额，不含税税额")
    private String hjse;

    @ApiModelProperty(value = "订单明细")
    private List<IpassOrderItemsDTO> orderItems;
}
