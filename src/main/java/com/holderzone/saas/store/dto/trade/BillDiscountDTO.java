package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillDiscountDTO
 * @date 2018/07/26 14:00
 * @description 折扣实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class BillDiscountDTO implements Serializable {

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    /**
     * 门店Guid
     */
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    /**
     * 门店名
     */
    @ApiModelProperty(value = "门店名")
    private String storeName;

    /**
     * 账单折扣GUID
     */
    @ApiModelProperty(value = "折扣guid，如果更新必须传入！")
    private String billDiscountGuid;

    /**
     * 账单业务主键GUID
     */
    @ApiModelProperty(value = "账单guid", required = true)
    private String billGuid;


    @ApiModelProperty(value = "折扣类别，0-会员折扣，1-优惠券整单折扣，2-优惠券菜品折扣，3-整单折扣，4-菜品赠送，5-会员积分，6-单品折扣,7-系统省零，8-收款改价")
    private Integer discountType;

    @ApiModelProperty(value = "折扣name")
    private String discountName;

    /**
     * 单品折扣或者菜品赠送时候 菜品GUID
     */
    @ApiModelProperty(value = "单品折扣时候或者菜品赠送，菜品guid")
    private String dishGuid;

    /**
     * 单品折扣或者菜品赠送时候 菜品名称
     */
    @ApiModelProperty(value = "单品折扣时候或者菜品赠送，菜品name")
    private String dishName;

    /**
     * 优惠券GUID
     */
    @ApiModelProperty(value = "优惠券guid")
    private String couponsGuid;

    /**
     * 折扣金额
     */
    @ApiModelProperty(value = "折扣金额")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "折扣打几折，对应整单折扣或者会员折扣类似")
    private BigDecimal discount;

    /**
     * 会员积分消耗的积分
     */
    @ApiModelProperty(value = "会员积分消耗的积分")
    private BigDecimal memberCardCostScore;

    /**
     * 会员积分折扣需要传，会员guid
     */
    @ApiModelProperty(value = "如果会员登录，传入此值表示享受会员折扣（后期会员积分折扣也需要传入此值）")
    private String memberGuid;

    @ApiModelProperty(value = "折扣人guid")
    private String operationStaffGuid;

    @ApiModelProperty(value = "折扣人姓名")
    private String operationStaffName;

    @ApiModelProperty(value = "订单来源")
    private Integer orderSource;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "0:堂食，1快餐，2：外卖")
    private Integer tradeMode;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate businessDaystamp;

    /**
     *  如果是单品折扣，关联对应账单菜品
     */
    private String billDishGuid;


}
