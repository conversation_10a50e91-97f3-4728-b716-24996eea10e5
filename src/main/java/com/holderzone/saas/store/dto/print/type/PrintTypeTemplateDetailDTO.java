package com.holderzone.saas.store.dto.print.type;

import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/22
 * @description 打印分类模版详情
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "打印分类模版详情", description = "打印分类模版详情")
public class PrintTypeTemplateDetailDTO implements Serializable {

    private static final long serialVersionUID = 2081781606284722771L;

    @ApiModelProperty(value = "模版Guid")
    private String guid;

    @ApiModelProperty(value = "模版名称")
    private String name;

    @ApiModelProperty(value = "分类列表")
    private List<TemplateTypeVO> typeList;

    /**
     * @see InvoiceTypeEnum
     */
    @ApiModelProperty(value = "应用单据-InvoiceTypeEnum")
    private List<String> invoiceType;

    @ApiModelProperty(value = "是否全部门店 0：否,1:是")
    private Boolean isAllStore;

    @ApiModelProperty(value = "门店列表")
    private List<TemplateStoreVO> storeList;
}
