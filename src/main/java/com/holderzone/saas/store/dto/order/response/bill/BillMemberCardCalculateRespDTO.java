package com.holderzone.saas.store.dto.order.response.bill;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class BillMemberCardCalculateRespDTO implements Serializable {

    private static final long serialVersionUID = -8951585514851962893L;

    /**
     * 麓豆会员id
     */
    private String ludouMemberGuid;

    /**
     * 麓豆会员手机号
     */
    private String ludouMemberPhone;

    /**
     * 麓豆会员名称
     */
    private String ludouMemberName;

    /**
     * 麓豆会员余额
     */
    private BigDecimal ludouMemberBalance;
}
