package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/8/28 15:08
 * @description
 */
@ApiModel("微信品牌配置信息相应DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxSubjectRespDTO {
    @ApiModelProperty("是否为联盟")
    private Boolean isAlliance;
    @ApiModelProperty("运营主体guid")
    private String operSubjectGuid;
    @ApiModelProperty("运营主体是否禁用")
    private Boolean multiMemberStatus;
}
