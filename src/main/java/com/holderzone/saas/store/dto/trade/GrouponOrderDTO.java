package com.holderzone.saas.store.dto.trade;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 团购订单
 */
@Data
public class GrouponOrderDTO implements Serializable {

    private static final long serialVersionUID = 4642939612235796240L;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 订单号(前端显示用，门店内唯一，格式************)
     */
    private String orderNo;

    /**
     * 券码
     */
    private String couponCode;

    /**
     * 券平台
     *
     * @see com.holderzone.saas.store.enums.GroupBuyTypeEnum
     */
    private Integer grouponType;


    private BigDecimal amount;

    /**
     * 券渠道
     *
     * @see com.holderzone.saas.store.enums.order.GrouponReceiptChannelEnum
     */
    private Integer receiptChannel;

    /**
     * 一键买单信息
     *
     * @see com.holderzone.saas.store.dto.takeaway.response.MtMaitonConsumeDTO
     */
    private String maitonConsumeInfo;

}
