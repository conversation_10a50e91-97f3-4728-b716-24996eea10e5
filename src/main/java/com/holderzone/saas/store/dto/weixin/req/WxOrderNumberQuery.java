package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 查询session中的consumer信息
 * @date 2021/7/2 14:52
 */
@Data
@ApiModel("查询session中的consumer信息")
public class WxOrderNumberQuery {

    @ApiModelProperty("企业Guid")
    private String enterpriseGuid;

    @ApiModelProperty("门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "桌台guid")
    private String diningTableGuid;

    @ApiModelProperty(value = "区域guid")
    private String areaGuid;

    @ApiModelProperty("点餐人数,存的时候必传")
    private Integer orderNumber;

    private String openId;

    /**
     * 交易模式(0：正餐，1：快餐)
     */
    private Integer tradeMode;
}
