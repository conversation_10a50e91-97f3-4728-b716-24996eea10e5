package com.holderzone.saas.store.dto.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.framework.util.DateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderBaseInfoDTO
 * @date 2018/09/28 18:20
 * @description 订单基本信息DTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class OrderBaseInfoDTO implements Serializable {

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    @ApiModelProperty(value = "门店名称")
    private String storeName;
    @ApiModelProperty(value = "销售类型，对应订单表：tradeMode 交易模式")
    private Integer tradeMode;
    @ApiModelProperty("客人数")
    private Integer guestCount;
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;
    @ApiModelProperty("创建人guid")
    private String createStaffGuid;
    @ApiModelProperty("创建人姓名")
    private String createStaffName;
    @ApiModelProperty(value = "开台时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkInTimestamp;
    @ApiModelProperty(value = "结账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTimestamp;
    @ApiModelProperty("牌号")
    private String mark;
    @ApiModelProperty("会员手机号")
    private String member_phone;
    @ApiModelProperty("会员姓名")
    private String member_name;
    @ApiModelProperty("就餐时长")
    private LocalTime timeDuration;


    public void makeTimeDuration(){
        if(this.checkInTimestamp != null && this.checkoutTimestamp != null){
            long longDuration = DateTimeUtils.localDateTime2Mills(this.checkoutTimestamp) - DateTimeUtils.localDateTime2Mills(this.checkInTimestamp);
            this.timeDuration = DateTimeUtils.mills2LocalDateTime(longDuration).toLocalTime();
        }else{
            this.timeDuration = null;
        }
    }
}
