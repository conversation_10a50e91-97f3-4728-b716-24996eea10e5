package com.holderzone.saas.store.dto.order.request.member;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@ApiModel(description = "优惠兑换码结果")
@Data
public class IslandRedeemResultDTO implements Serializable {


    @ApiModelProperty(value = "失败原因")
    private String causeMessage;

    @ApiModelProperty(value = "兑换是否成功 1 成功 0 失败")
    private String resultState;
}
