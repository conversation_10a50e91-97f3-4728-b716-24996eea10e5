package com.holderzone.saas.store.dto.member.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description 实体卡校验实体
 * @date 2021/5/17 14:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("实体卡校验实体")
public class PhysicalCardCheckDTO {

    @ApiModelProperty("实体卡卡号")
    private String physicalCardNum;

    @ApiModelProperty("实体卡手机号")
    private String physicalCardPhoneNum;

    @ApiModelProperty("实体卡卡号列表")
    private List<String> activateCardList;
}
