package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateMenuSubitemReqDTO
 * @date 2019/05/23 15:32
 * @description //TODO 新增、更新模板菜单请求DTO
 * @program holder-saas-aggregation-app
 */
@Data
@ApiModel(value = "新增、更新模板菜单DTO")
public class ItemTemplateMenuSubitemReqDTO {

    /**
     * 时间周期模式  1：按时段  2：按星期  3：周期+时段  默认 1
     */
    @ApiModelProperty(value = "时间周期模式  1：按时段  2：按星期  3：周期+时段 ")
    private Integer periodicMode;


    /**
     * 是否全时段 1:否 2：是
     */
    @ApiModelProperty(value = "是否全时段 1:否 2：是")
    private Integer isItFullTime;

    /**
     * 商品模板guid
     */
    @ApiModelProperty(value = "商品模板guid")
    private String templateGuid;

    /**
     * 商品模板-菜单guid
     */
    @ApiModelProperty(value = "商品模板-菜单guid 新增不需要传")
    private String menuGuid;

    /**
     * 商品模板-菜单-菜品集合
     */
    @ApiModelProperty(value = "商品模板-菜单-菜品集合")
    private List<ItemMenuSubItemReqDTO> itemMenuSubItemReqDTOS;

    /**
     * 商品模板-菜单-保存执行时间集合
     */
    @ApiModelProperty(value = "商品模板-菜单-保存执行时间集合")
    private ItmeTemplateMenuValidityReqDTO itmeTemplateMenuValidityReqDTO;

    @ApiModelProperty(value = "菜单逻辑删除 0：否 1：是")
    private Integer isDelete = 0;


}
