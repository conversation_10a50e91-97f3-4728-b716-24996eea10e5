package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GroupMealSkuSaveReqDTO
 * @date 2019/12/06 下午6:02
 * @description //团购套餐规格保存DTO
 * @program holder
 */

@Data
@ApiModel(value = "团购套餐规格保存DTO")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GroupMealSkuSaveReqDTO implements Serializable {

    @ApiModelProperty(value = "关系表主键guid,新增时不传")
    private String guid;

    @ApiModelProperty(value = "团餐子项商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "团餐子项规格GUID")
    private String skuGuid;

    @ApiModelProperty(value = "数量")
    @NotEmpty
    @Size(min = 1, max = 999999)
    private BigDecimal num;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "排序，越小越靠前")
    private Integer sort;
}
