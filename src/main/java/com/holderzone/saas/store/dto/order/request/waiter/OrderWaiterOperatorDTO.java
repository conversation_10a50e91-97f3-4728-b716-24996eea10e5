package com.holderzone.saas.store.dto.order.request.waiter;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> R
 * @date 2021/1/13 15:37
 * @description
 */
@Data
public class OrderWaiterOperatorDTO {
    @ApiModelProperty(value = "运算符：0-->等于,1-->大于,-1-->小于,2-->大于等于,-2-->小于等于")
    private Integer operator;
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderFee;
}
