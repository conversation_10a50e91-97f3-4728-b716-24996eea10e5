package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("预订单")
@Accessors(chain=true)
public class PreOrderRespDTO {

	@ApiModelProperty(value = "true：输入人数键盘，false，不需要键盘")
	private Boolean enableKeyboard=true;

	@ApiModelProperty(value = "门店名称")
	private String storeName;

	@ApiModelProperty(value = "品牌名称")
	private String brandName;

	@ApiModelProperty(value = "区域名称")
	private String areaName;

	@ApiModelProperty(value = "桌台名称")
	private String tableCode;

	@ApiModelProperty(value = "整单备注")
	private String preOrderRemark= StringUtils.EMPTY;

	@ApiModelProperty(value = "预订单总价")
	private BigDecimal originPreOrderPrice;

	@ApiModelProperty(value = "true:有会员价，false：没有")
	private Boolean enablePreferentialPrice=false;

	@ApiModelProperty(value = "是否登录会员，true:表示订单总价金色加v")
	private Boolean isLogin;

	@ApiModelProperty(value = "点餐人数")
	private Integer guestCount;

	@ApiModelProperty(value = "预订单优惠价")
	private BigDecimal preferentialPreOrderPrice;

    @ApiModelProperty(value = "附加费总额")
    private BigDecimal appendFee;

	private List<PreOrderPersonDTO> preOrderPersonDTOS;
}
