package com.holderzone.saas.store.dto.trade;

import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/24 14:37
 * @description
 */
@Data
@ApiModel("订单推送MQ消息对象")
public class OrderDetailPushMqDTO {
    @ApiModelProperty(value = "企业GUID")
    private String enterpriseGuid;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "微信小程序/支付宝小程序 用户id")
    private String openId;

    @ApiModelProperty(value = "运营主体")
    private String operSubjectGuid;

    @ApiModelProperty(value = "微信订单guid")
    private String orderRecordGuid;

    @ApiModelProperty(value = "微信订单支付倒计时")
    private Long secondsBetween;

    @ApiModelProperty(value = "订单主数据")
    private OrderDTO orderDTO;

    @ApiModelProperty(value = "优惠合计")
    private BigDecimal discountCombined;

    @ApiModelProperty(value = "付款金额合计")
    private BigDecimal payWayDetailsCount;

    @ApiModelProperty(value = "菜品数据")
    private List<OrderItemDTO> orderItemDTOList;

    @ApiModelProperty(value = "优惠信息")
    private List<DiscountDTO> discountInfoList;

    @ApiModelProperty(value = "附加费")
    private List<AppendFeeDetailDTO> appendFeeDetailList;

    @ApiModelProperty(value = "付款金额明细")
    private List<OrderDetailTransactionRecordRespDTO> payWayDetails;
}
