package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxAuthorizeReqDTO
 * @date 2019/02/23 16:10
 * @description 微信授权请求DTO
 * @program holder-saas-store-weixin
 */
@Data
@ApiModel(value = "微信授权请求DTO")
public class WxAuthorizeReqDTO {

    @ApiModelProperty(value = "code作为换取access_token的票据，每次用户授权带上的code将不一样，code只能使用一次，5分钟未被使用自动过期。", required = true)
    private String code;

    @ApiModelProperty(value = "重定向后会带上state参数，开发者可以填写a-zA-Z0-9的参数值，最多128字节")
    private String state;

    @ApiModelProperty(value = "穿透字段", required = true)
    private String eventKey;

    @ApiModelProperty(value = "appId")
    private String appId;

    @ApiModelProperty(value = "微信token")
    private String weixinToken;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty(value = "wxqicode表guid")
    private String wxqrCodeGuid;
    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;
    @ApiModelProperty(value = "消息发送的时间戳", required = false)
    private Long currentTime;
    @ApiModelProperty("类型：0 微信 1 KbzPay")
    private Integer type;

    @ApiModelProperty("缓存token: 直接获取KbzPay的kbzpay_token")
    private String accessToken;

    @ApiModelProperty("语言类型")
    private String lang;

    /**
     * 跳转类型
     *
     * @see com.holderzone.saas.store.enums.weixin.WxMenuTypeEnum
     */
    private String menuType;

    /**
     * 会员guid
     */
    private String memberInfoGuid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    @ApiModelProperty("是否再次授权")
    private Boolean isAgain;

    @ApiModelProperty("外部openId")
    private String thirdOpenId;

    @ApiModelProperty("外部appId")
    private String thirdAppId;
}
