package com.holderzone.saas.store.dto.journaling.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("老板助手-销售统计请求入参")
public class SaleCountReqDTO {

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate startDate;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate endDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "开始营业日期时间 yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "开始营业日期时间不能为空")
    protected LocalDateTime businessStartDateTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "营业结束日期时间 yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "营业结束日期时间不能为空")
    protected LocalDateTime businessEndDateTime;

    @ApiModelProperty(value = "门店guid")
    private List<String> storeGuidList;

    @ApiModelProperty(value = "统计类型(1:商品 2:分类)")
    private String type;

    @ApiModelProperty(value = "一次数据加载时间节点时间戳")
    private long dataEndTime;

    @ApiModelProperty(value = "页码数")
    private long currentPage;

    @ApiModelProperty(value = "每页条数")
    private long pageSize;

}
