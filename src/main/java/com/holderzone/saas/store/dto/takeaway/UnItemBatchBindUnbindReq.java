package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/1
 * @description 批量绑定
 */
@Data
public class UnItemBatchBindUnbindReq implements Serializable {

    private static final long serialVersionUID = -2431190008724794553L;

    @ApiModelProperty("批量绑定商品列表")
    private List<UnItemBindUnbindReq> bindUnbindReqList;

    @NotBlank(message = "ERP方商品GUID不得为空")
    @ApiModelProperty(value = "ERP方商品GUID")
    private String itemGuid;

    @NotBlank(message = "ERP方商品规格GUID不得为空")
    @ApiModelProperty(value = "ERP方商品规格GUID")
    private String skuGuid;

    @NotNull(message = "映射数量不得为空")
    @ApiModelProperty(value = "映射数量")
    private Integer unItemCountMapper;

    @Min(value = 0, message = "外卖类型(0：美团，1：饿了么，2：掌控者,3:赚餐,4:抖音,5:京东)")
    @Max(value = 5, message = "外卖类型(0：美团，1：饿了么，2：掌控者,3:赚餐,4:抖音,5:京东)")
    @NotNull(message = "商品映射类型不得为空")
    @ApiModelProperty(value = "商品映射类型：0=美团，1=饿了么，2=掌控者,3=赚餐,4:抖音,5:京东", required = true)
    private Integer takeoutType;

    @NotBlank(message = "品牌GUID不得为空")
    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

}
