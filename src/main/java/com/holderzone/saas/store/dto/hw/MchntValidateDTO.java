package com.holderzone.saas.store.dto.hw;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MchntValidateDTO
 * @date 2019/05/16 11:26
 * @description //TODO
 * @program holder-saas-store-hw
 */
@Data
@ApiModel("查询商家是否开通智能预定服务")
public class MchntValidateDTO {

    @NotEmpty(message = "商家电话号码不得为空")
    @ApiModelProperty(value = "商家电话号码", required = true)
    private String merchantPhone;
}