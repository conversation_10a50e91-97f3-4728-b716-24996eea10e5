package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreAttrGroupRespDTO
 * @date 2019/3/15
 */
@Data
@ApiModel("微信门店菜品属性组")
public class WxStoreAttrGroupRespDTO {

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;
    @ApiModelProperty(value = "商品属性组GUID", required = true)
    private String attrGroupGuid;
    @ApiModelProperty(value = "属性组名称", required = true)
    private String name;
    @ApiModelProperty(value = "是否必选:0 否 1 是", required = true)
    private Integer isRequired;
    @ApiModelProperty(value = "是否多选:0 否 1 是", required = true)
    private Integer isMultiChoice;
    @ApiModelProperty(value = "maybe null,图标地址")
    private String iconUrl;
    @ApiModelProperty(value = "是否需要展示价格，1：是，0,否", required = true)
    private Integer showPrice;
    @ApiModelProperty(value = "属性详情集合", required = true)
    private List<WxStoreAttrRespDTO> attrList;
}
