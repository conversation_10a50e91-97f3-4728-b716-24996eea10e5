package com.holderzone.saas.store.dto.marketing.specials;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/6/4
 * @description 标签 会员 等级
 */
@Data
@Accessors(chain = true)
public class ConsumptionGiftDetailsDTO implements Serializable {

    private static final long serialVersionUID = 7535139146158456242L;

    /**
     * 条件序号 1、2、3
     */
    @ApiModelProperty(value = "条件序号")
    private Integer serialNumber;

    /**
     * guidList
     */
    private List<String> guidList;

}
