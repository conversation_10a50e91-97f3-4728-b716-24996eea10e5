package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class DstItemQueryReqDTO implements Serializable {

    private static final long serialVersionUID = -2782247301702252131L;

    @NotEmpty(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @NotEmpty(message = "设备ID不得为空")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @ApiModelProperty(value = "分组guid")
    private String groupGuid;
}
