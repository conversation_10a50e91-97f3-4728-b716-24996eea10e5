package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("购物车展示")
public class WeChatH5PayReqDTO {

    @ApiModelProperty(value = "订单id", required = true)
    private String orderGuid;

    @ApiModelProperty(value = "微信回调页面地址", required = true)
    private String outNotifyUrl;

    @ApiModelProperty(value = "0：PC服务端,1：PC平板,2：小店通,3：一体机,4：POS机,5：云平板,6：点菜宝(M1)," +
            "7：PV1(带刷卡的点菜宝),9：厨房显示系统,10: 取餐屏,12：微信 15通吃岛"
    )
    private Integer deviceType;

    @ApiModelProperty(value = "支付金额", required = true)
    private BigDecimal payAmount;

    @ApiModelProperty(value = "是否使用会员优惠")
    private Boolean useMemberDiscountFlag;

    @ApiModelProperty(value = "小程序appId(小程序支付需要appId)")
    private String appId;

    @ApiModelProperty(value = "客户端ip(农行渠道需要参数)")
    private String clientIp;

    @ApiModelProperty(value = "是否二次支付(第一次支付取消或者失败，在订单详情页面支付)")
    private Boolean secondPayFlag;

    @ApiModelProperty("外部openId")
    private String thirdOpenId;

    @ApiModelProperty("外部appId")
    private String thirdAppId;
}
