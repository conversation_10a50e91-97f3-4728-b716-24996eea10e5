package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPictureConfigDTO
 * @date 2019/08/19 10:07
 * @description 门店设备副屏图片
 * @program holder-saas-store
 */
@Data
@ApiModel("门店设备副屏图片")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScreenPictureConfigDTO {

    @ApiModelProperty(value = "图片集合")
    List<ScreenPicDTO> screenPicDTOList;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名")
    private String storeName;

    @ApiModelProperty(value = "设备类型 0：PC服务端,1：PC平板,2：小店通,3：一体机,4：POS机,5：云平板,6：点菜宝(M1),7：PV1(带刷卡的点菜宝),9：厨房显示系统,14: 取餐屏,12：微信")
    private Integer deviceType;

    @ApiModelProperty(value = "图片类型 1-->副屏图片，2-->副屏点餐图片 3副屏显示设置")
    private Integer picType;

    @ApiModelProperty(value = "切换时间")
    private Integer changeMills;

    @ApiModelProperty(value = "picTimeGuid")
    private String picTimeGuid;

    @ApiModelProperty(value = "副屏显示内容 1同主屏一致 2自定义")
    private Integer displayType;
}
