package com.holderzone.saas.store.dto.weixin;

import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreAccountDTO
 * @date 2019/4/18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxStoreAccountDTO {

    private DineinOrderDetailRespDTO dineinOrderDetailRespDTO;

    private WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO;
}
