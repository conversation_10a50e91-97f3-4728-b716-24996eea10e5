package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.saas.store.dto.weixin.common.WxStoreInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueConfigDTO
 * @date 2019/05/09 18:46
 * @description 门店微信排队配置详情响应DTO
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("门店微信排队配置详情响应DTO")
public class WxQueueConfigDTO extends WxStoreInfoDTO {

    @ApiModelProperty("guid")
    private String guid;

    @ApiModelProperty("是否开启线上排队（0关闭，1开启）（0暂停排队，1开启排队）")
    private Integer isQueueOpen;

    @ApiModelProperty("排队最远距离")
    private BigDecimal maxDistant;

    @ApiModelProperty("是否开启距离限制（0关闭，1开启）")
    private Integer distantConstraint;

    @ApiModelProperty("排队附加费（不做）")
    private BigDecimal queueFee;

    @ApiModelProperty("是否可以排队预点单（0关闭，1开启。不做）")
    private Integer couldQueueOrder;

    @ApiModelProperty("当前修改是否只是更改状态")
    private Boolean isOnlyState;
}