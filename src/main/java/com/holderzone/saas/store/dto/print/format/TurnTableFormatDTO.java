/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:TurnTableFormatDTO.java
 * Date:2019-12-5
 * Author:terry
 */

package com.holderzone.saas.store.dto.print.format;

import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class TurnTableFormatDTO extends FormatDTO {

    @Nullable
    @ApiModelProperty(value = "门店名称", required = true)
    private FormatMetadata storeName;

    @NotNull(message = "单据类型不能为空")
    @ApiModelProperty(value = "单据类型", required = true)
    private FormatMetadata invoiceName;

    @NotNull(message = "转台时间不能为空")
    @ApiModelProperty(value = "转台时间", required = true)
    private FormatMetadata turnTime;

    @NotNull(message = "原桌位不能为空")
    @ApiModelProperty(value = "原桌位", required = true)
    private FormatMetadata oriTable;

    @NotNull(message = "新桌位不能为空")
    @ApiModelProperty(value = "新桌位", required = true)
    private FormatMetadata newTable;

    @NotNull(message = "操作员不能为空")
    @ApiModelProperty(value = "操作员", required = true)
    private FormatMetadata operator;

    @NotNull(message = "打印时间不能为空")
    @ApiModelProperty(value = "打印时间", required = true)
    private FormatMetadata printTime;

    @Override
    public void applyDefault() {
        super.applyDefault();
        if (storeName == null) {
            storeName = new FormatMetadata(2, 1, false);
        }
        if (invoiceName == null) {
            invoiceName = new FormatMetadata(2, 1, false);
        }
        if (turnTime == null) {
            turnTime = new FormatMetadata(1, 0, false);
        }
        if (oriTable == null) {
            oriTable = new FormatMetadata(2, 1, false);
        }
        if (newTable == null) {
            newTable = new FormatMetadata(2, 1, false);
        }
        if (operator == null) {
            operator = new FormatMetadata(1, 0, false);
        }
        if (printTime == null) {
            printTime = new FormatMetadata(1, 2, false);
        }
    }
}
