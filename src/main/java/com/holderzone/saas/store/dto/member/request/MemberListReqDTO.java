package com.holderzone.saas.store.dto.member.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberListReqDTO
 * @date 2018/09/25 18:23
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class MemberListReqDTO extends BasePageDTO implements Serializable{

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value="注册开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
   @JsonSerialize(using = LocalDateTimeSerializer.class)
   @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreateBegin;

    @ApiModelProperty(value="注册结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreateEnd;



    @ApiModelProperty(value = "生日开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate  birthdayBegin;

    @ApiModelProperty(value = "生日结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthdayEnd;


    @ApiModelProperty(value = "性别,0男1女2保密全部传-1")
    private Integer sex;

    @ApiModelProperty(value="查询类型,1会员手机2会员姓名")
    private Integer searchType;


    @ApiModelProperty(value = "主要搜索条件内容")
    private String searchMainValue;
    @ApiModelProperty(value = "会员等级主键")
    private String memberGradeGuid;

    @ApiModelProperty(value = "总消费次数起点")
    private Integer totalConsumeNumBegin;

    @ApiModelProperty(value = "总消费次数终点")
    private Integer totalConsumeNumEnd;

    @ApiModelProperty(value ="总充值次数终点")
    private Integer totalPrepaidNumBegin;

    @ApiModelProperty(value ="总充值次数终点")
    private Integer totalPrepaidNumEnd;



    @ApiModelProperty(value = "总充值金额起点")
    private BigDecimal totalPrepaidFeeBegin;

 @ApiModelProperty(value = "总充值金额终点")
 private BigDecimal totalPrepaidFeeEnd;


 @ApiModelProperty(value = "总消费金额起点")
 private BigDecimal totalConsumeFeeBegin;

 @ApiModelProperty(value = "总消费次数终点")
 private BigDecimal totalConsumeFeeEnd;
}
