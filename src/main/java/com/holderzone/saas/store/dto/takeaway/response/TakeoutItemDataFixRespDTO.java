package com.holderzone.saas.store.dto.takeaway.response;

import com.holderzone.saas.store.dto.item.resp.MappingRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 外卖商品数据修复返回实体
 * @date 2022/5/12 14:10
 * @className: TakeoutItemDataFixRespDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "外卖商品数据修复返回实体")
public class TakeoutItemDataFixRespDTO implements Serializable {

    private static final long serialVersionUID = 4324845578505641705L;

    /**
     * 门店GUID
     */
    @ApiModelProperty("门店GUID")
    private String storeGuid;

    /**
     * 门店名
     */
    @ApiModelProperty("门店名")
    private String storeName;

    /**
     * 订单来源：0=美团，1=饿了么，6=赚餐自营外卖
     *
     * @see com.holderzone.saas.store.dto.takeaway.OrderType.TakeoutSubType
     */
    @ApiModelProperty("订单来源")
    private Integer orderSubType;

    /**
     * 商品名称：外卖名称+规格名称
     */
    @ApiModelProperty("商品名称：外卖名称+规格名称")
    private String takeoutItemName;

    /**
     * 外卖商品编号
     */
    @ApiModelProperty("外卖商品编号")
    private String thirdSkuId;

    /**
     * 门店映射数量
     */
    @ApiModelProperty("门店映射数量")
    private BigDecimal erpItemCount = BigDecimal.ONE;

    /**
     * 异常订单数
     */
    @ApiModelProperty("异常订单数")
    private Integer takeoutOrderCount;

}
