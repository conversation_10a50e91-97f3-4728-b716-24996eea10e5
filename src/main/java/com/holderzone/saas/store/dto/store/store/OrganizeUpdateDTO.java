package com.holderzone.saas.store.dto.store.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreCreateDTO
 * @date 2018/07/23 下午4:27
 * @description 修改组织DTO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class OrganizeUpdateDTO implements Serializable {

    private static final long serialVersionUID = -6640566635361659783L;

    /**
     * 组织guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "组织guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "组织guid", required = true)
    private String organizationGuid;

    /**
     * 组织类型。1=品牌，2=区域，3=门店。
     */
    @NotNull
    @Min(value = 1, message = "是否启用不得为空，且范围是[1-3]，1=品牌，2=区域，3=门店")
    @Max(value = 3, message = "是否启用不得为空，且范围是[1-3]，1=品牌，2=区域，3=门店")
    @ApiModelProperty(value = "组织类型。1=品牌，2=区域，3=门店。", required = true)
    private Integer organizationType;

    /**
     * 启用/禁用
     */
    @ApiModelProperty(value = "是否已启用。0=未启用，1=已启用。默认1。", required = true)
    private Integer enable;

    /**
     * 父级组织的guid
     */
    @NotBlank
    @Size(min = 1, max = 45, message = "父级组织的guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "父级组织的guid", required = true)
    private String parentOrganizationGuid;

    /**
     * 父级组织名称
     */
    @NotBlank
    @Size(min = 1, max = 45, message = "父级组织名称不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "父级组织名称", required = true)
    private String parentOrganizationName;

    /**
     * 组织名称
     */
    @NotNull
    @Size(min = 1, max = 45, message = "组织名称不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "组织名称", required = true)
    private String name;

    /**
     * 组织类型为门店条件下的门店面积
     */
    @Min(value = 0, message = "门店面积范围[0-65535]")
    @Max(value = 65535, message = "门店面积范围[0-65535]")
    @ApiModelProperty(value = "门店面积")
    private Integer area;

    /**
     * 组织电话
     */
    @Size(min = 1, max = 45, message = "组织电话不得超过45个字符")
    @ApiModelProperty(value = "组织电话")
    private String tel;

    /**
     * 省份编码
     */
    @Min(value = 0, message = "省份编码[0-16777215]")
    @Max(value = 16777215, message = "省份编码[0-16777215]")
    @ApiModelProperty(value = "省份编码")
    private Integer provinceCode;

    /**
     * 省份名称
     */
    @Size(min = 1, max = 45, message = "省份名称不得超过45个字符")
    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    /**
     * 城市编码
     */
    @Min(value = 0, message = "城市编码[0-16777215]")
    @Max(value = 16777215, message = "城市编码[0-16777215]")
    @ApiModelProperty(value = "城市编码")
    private Integer cityCode;

    /**
     * 城市名称
     */
    @Size(min = 1, max = 45, message = "城市名称不得超过45个字符")
    @ApiModelProperty(value = "城市名称")
    private String cityName;

    /**
     * 区县编码
     */
    @Min(value = 0, message = "区县编码[0-16777215]")
    @Max(value = 16777215, message = "区县编码[0-16777215]")
    @ApiModelProperty(value = "区县编码")
    private Integer countyCode;

    /**
     * 区县名称
     */
    @Size(min = 1, max = 45, message = "区县名称不得超过45个字符")
    @ApiModelProperty(value = "区县名称")
    private String countyName;

    /**
     * 详细地址
     */
    @Size(min = 1, max = 250, message = "详细地址不得超过250个字符")
    @ApiModelProperty(value = "详细地址")
    private String addressDetail;

    /**
     * 纬度
     */
    @Size(min = 1, max = 12, message = "纬度不得超过12个字符")
    @ApiModelProperty(value = "纬度")
    private String latitude;

    /**
     * 经度
     */
    @Size(min = 1, max = 12, message = "经度不得超过12个字符")
    @ApiModelProperty(value = "经度")
    private String longitude;
}
