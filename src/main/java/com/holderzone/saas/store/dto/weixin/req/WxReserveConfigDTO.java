package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxReserveConfigRespDTO
 * @date 2019/12/16 17:23
 * @description
 * @program holder-saas-store
 */
@Data
@ApiModel("微信预定配置DTO")
public class WxReserveConfigDTO {
    @ApiModelProperty("guid")
    private String guid;

    @ApiModelProperty("门店guid")
    @NotEmpty(message = "门店GUID不能为空")
    private String storeGuid;

    @ApiModelProperty("门店名字")
    private String storeName;

    @ApiModelProperty("是否开启预定")
    private Boolean status;

    @ApiModelProperty("预定类型（0：按天， 1：按小时）")
    private Integer reserveType;

    @ApiModelProperty("提前多少天，reserveType == 0 时，取这个值")
    private Integer daysBefore;

    @ApiModelProperty("提前多少小时，reserveType == 1 时，取这个值")
    private Double hoursBefore;

    @ApiModelProperty("允许预定时间范围/天")
    private Integer daysRange;

    @ApiModelProperty("预定范围：0：按区域，1：按桌台")
    private String reserveArea;
}
