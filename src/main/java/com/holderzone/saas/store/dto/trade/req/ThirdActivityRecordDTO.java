package com.holderzone.saas.store.dto.trade.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 第三方活动使用记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(description = "第三方活动使用记录")
public class ThirdActivityRecordDTO implements Serializable {

    private static final long serialVersionUID = -3178055245477132517L;

    @ApiModelProperty(value = "第三方活动记录guid，更新必传")
    private String guid;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "第三方活动guid")
    private String activityGuid;

    @ApiModelProperty(value = "规则类型 0-金额扣减 1-买单优惠")
    private Integer ruleType;

    @ApiModelProperty(value = "第三方活动券码")
    private List<String> thirdActivityCodeList;

    @ApiModelProperty(value = "参与活动的金额")
    private BigDecimal joinFee;

    @ApiModelProperty(value = "不参与活动金额")
    private BigDecimal notJoinFee;

    @ApiModelProperty(value = "扣减金额")
    private BigDecimal deductionFee;

    @ApiModelProperty(value = "顾客购买金额或者抵扣金额")
    private BigDecimal costFee;

    @ApiModelProperty(value = "券面值")
    private BigDecimal couponFee;

    @ApiModelProperty(example = "是否与其他平台活动共享 0-否 1-是")
    private Byte isActivityShare;

    @ApiModelProperty(example = "是否与其他活动类型共享 0-否 1-是")
    private Byte isThirdShare;

    @ApiModelProperty(example = "单笔订单使用上限")
    private Integer useLimit;

    @ApiModelProperty("三方平台类型 MT-美团; DZ-大众点评; DY-抖音; ZC-赚餐; OTHER-其他")
    private String thirdType;

}
