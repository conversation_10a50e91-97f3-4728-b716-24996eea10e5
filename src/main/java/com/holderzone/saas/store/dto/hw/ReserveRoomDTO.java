package com.holderzone.saas.store.dto.hw;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveDateDTO
 * @date 2019/04/26 18:29
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "预定请求")
public class ReserveRoomDTO {

    @NotEmpty(message = "商户电话号码不得为空")
    @ApiModelProperty(value = "商户电话号码", required = true)
    private String merchantPhone;

    @Nullable
    @ApiModelProperty(value = "预定日期：如 20190516")
    private String reserveDate;

    @Nullable
    @ApiModelProperty(value = "预定区间：0=中午，1=晚上")
    private String reserveInterval;

    @Nullable
    @ApiModelProperty(value = "预定时间：如 19:00")
    private String reserveTime;

    @Nullable
    @ApiModelProperty(value = "就餐人数：1-99")
    private String peopleTotal;
}