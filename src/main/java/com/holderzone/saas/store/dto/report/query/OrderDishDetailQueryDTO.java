package com.holderzone.saas.store.dto.report.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2018/09/28 上午 11:11
 * @description
 */
@ApiModel("订单菜品明细")
public class OrderDishDetailQueryDTO extends BaseQueryDTO {


    @ApiModelProperty("菜品类型Guid")
    private String dishTypeGuid;

    @ApiModelProperty("是否套餐")
    private Integer packageDish;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("菜品编号或者名称搜索框")
    private String searchContent;

    @ApiModelProperty("是否赠送(0):销售报表(1)")
    private Integer gift;

    public Integer getGift() {
        return gift;
    }

    public void setGift(Integer gift) {
        this.gift = gift;
    }

    public String getDishTypeGuid() {
        return dishTypeGuid;
    }

    public void setDishTypeGuid(String dishTypeGuid) {
        this.dishTypeGuid = dishTypeGuid;
    }

    public Integer getPackageDish() {
        return packageDish;
    }

    public void setPackageDish(Integer packageDish) {
        this.packageDish = packageDish;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSearchContent() {
        return searchContent;
    }

    public void setSearchContent(String searchContent) {
        this.searchContent = searchContent;
    }
}
