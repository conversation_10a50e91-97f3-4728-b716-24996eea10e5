package com.holderzone.saas.store.dto.config.req;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/18
 * @description 门店出餐设置请求
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "出餐配置返回", description = "出餐配置返回")
public class StoreFinishFoodConfigReq implements Serializable {

    private static final long serialVersionUID = -3948912680093319322L;

    /**
     * 备餐时间
     * 必填，正整数，0＜备餐时间小于等于<999，不填就默认关闭
     */
//    @NotNull(message = "备餐时间不能为空")
    @Min(value = 0, message = "备餐时间不能为负数")
    @Max(value = 999, message = "备餐时间最多999")
    @ApiModelProperty(value = "备餐时间")
    private Integer prepTime;

//    @NotNull(message = "快餐出餐语音开关不能为空")
    @ApiModelProperty(value = "快餐出餐语音开关：0关闭 1开启")
    private Integer finishFoodVoiceSwitch;

    @NotNull(message = "门店guid不能为空")
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

}
