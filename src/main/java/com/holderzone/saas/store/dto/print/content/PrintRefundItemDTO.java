package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.dto.print.content.base.TradeModeAware;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import static com.holderzone.saas.store.dto.print.util.PrintMockUtils.*;

/**
 * 退菜单
 *
 * <AUTHOR>
 * @date 2018/09/19 16:16
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("退菜单")
public class PrintRefundItemDTO extends PrintBaseItemDTO implements TradeModeAware, PrintDataMockito {

    @Nullable
    @ApiModelProperty(value = "外卖平台名称", notes = "外卖打印时该值不能为空")
    private String markName;

    @NotBlank(message = "桌位或号牌或外卖平台名称不能为空")
    @ApiModelProperty(value = "桌位或号牌或外卖平台名称（正餐'桌位，快餐'牌号'，外卖'平台名''）", required = true)
    private String markNo;

    @NotBlank(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    private String orderNo;

    @NotNull(message = "就餐人数不得为空")
    @ApiModelProperty(value = "就餐人数", required = true)
    private Integer personNumber;

    @NotNull(message = "下单时间")
    @ApiModelProperty(value = "下单时间", required = true)
    private Long orderTime;

    /**
     * @see com.holderzone.saas.store.enums.print.TradeModeEnum
     */
    @NotNull(message = "交易模式不得为空")
    @ApiModelProperty(value = "交易模式", required = true)
    private Integer tradeMode;

    @Override
    public void applyMock() {
        super.applyMock();
        setMarkNo(mockMarkNo());
        setOrderNo(mockOrderNo());
        setPersonNumber(mockPersonNumber());
        setOrderTime(mockOpenTableTime());
        setTradeMode(0);
    }
}
