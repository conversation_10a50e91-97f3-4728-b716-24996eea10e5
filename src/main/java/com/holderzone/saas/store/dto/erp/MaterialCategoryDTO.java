package com.holderzone.saas.store.dto.erp;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.erp.util.Add;
import com.holderzone.saas.store.dto.erp.util.Update;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2019/04/26 11:41
 */
public class MaterialCategoryDTO extends BaseDTO {

    @NotEmpty(message = "分类GUID不能为空", groups = Update.class)
    private String guid;
    @ApiModelProperty("分类名称")
    @NotEmpty(message = "分类名称不能为空", groups = Add.class)
    private String name;
    @ApiModelProperty("仓库GUID")
    private String warehouseGuid;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getName() {
        return name;
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }

    public void setName(String name) {
        this.name = name;
    }

}
