package com.holderzone.saas.store.dto.kds.req;

import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class KdsPrinterPageReqDTO extends PageDTO {

    private static final long serialVersionUID = -2773042054434843563L;

    @NotEmpty(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @NotEmpty(message = "设备Guid不得为空")
    @ApiModelProperty(value = "设备Guid")
    private String deviceId;
}
