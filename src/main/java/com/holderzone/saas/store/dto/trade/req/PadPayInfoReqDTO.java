package com.holderzone.saas.store.dto.trade.req;

import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description pad支付信息
 * @date 2021/9/9 15:38
 * @className: PadPayInfoReqDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "pad支付信息")
public class PadPayInfoReqDTO implements Serializable {

    private static final long serialVersionUID = 6478906038714118548L;

    /**
     * 交易订单号
     */
    @ApiModelProperty("订单guid")
    private String orderGuid;

    /**
     * 消费总额
     * 订单金额（商品总额+附加费）
     */
    @ApiModelProperty("消费总额")
    private BigDecimal orderTotalFee;

    /**
     * 会员优惠
     */
    @ApiModelProperty("会员优惠")
    private BigDecimal memberDiscountFee;

    /**
     * 优惠券code list
     */
    @ApiModelProperty(value = "优惠券code list")
    private List<String> volumeCodes;

    /**
     * 卡券优惠金额
     */
    @ApiModelProperty(value = "卡券优惠金额")
    private BigDecimal volumeFee;

    /**
     * 需付金额
     * 即实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））
     */
    @ApiModelProperty("需付金额")
    private BigDecimal actuallyPayFee;

    /**
     * 是否计算积分1：计算，2：不计算
     * 最开始看错了，注意这个字段，小心被坑
     */
    @ApiModelProperty(value = "是否计算积分1：计算，2：不计算")
    private Integer useIntegral;

    /**
     * 积分抵扣金额
     */
    @ApiModelProperty(value = "积分抵扣金额")
    private BigDecimal integralDeductedAmount;

    /**
     * 当前的优惠信息
     */
    @ApiModelProperty(value = "当前的优惠信息")
    private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

    @ApiModelProperty(value = "附加费")
    private BigDecimal appendFee;

    @ApiModelProperty("用户昵称")
    private String nickName;

    @ApiModelProperty("平台会员GUID")
    private String memberInfoGuid;

    @ApiModelProperty(value = "会员卡guid")
    private String memberInfoCardGuid;

    @ApiModelProperty(value = "0：PC服务端,1：PC平板,2：小店通,3：一体机,4：POS机,5：云平板,6：点菜宝(M1)," +
            "7：PV1(带刷卡的点菜宝),9：厨房显示系统,10: 取餐屏,12：微信 15通吃岛")
    private Integer deviceType;

    @ApiModelProperty("运营主体Guid")
    private String operSubjectGuid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty(value = "version")
    private Integer version;

    @ApiModelProperty(value = "设备id")
    private String deviceId;
}
