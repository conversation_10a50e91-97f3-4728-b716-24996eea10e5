package com.holderzone.saas.store.dto.order.response.daily;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GatherRespDTO
 * @date 2019/02/15 14:46
 * @description 营业日报-收款统计
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class GatherRespDTO {
    @ApiModelProperty(value = "收款方式编码")
    private Integer gatherCode;
    @ApiModelProperty(value = "收款方式")
    private String gatherName;
    @ApiModelProperty(value = "销售收入")
    private BigDecimal consumerAmount;
    @ApiModelProperty(value = "超出金额，销售收入内")
    private BigDecimal excessAmount;
    @ApiModelProperty(value = "充值收入")
    private BigDecimal prepaidAmount;
    @ApiModelProperty(value = "预订收入")
    private BigDecimal reserveAmount;
    @ApiModelProperty(value = "合计")
    private BigDecimal totalAmount;
    @ApiModelProperty(value = "是否合计 0否 1是")
    private int isTotal = 0;
    @ApiModelProperty(value = "聚合支付对应的支付方式")
    private Integer payPowerId;
    @ApiModelProperty(value = "明细")
    private List<InnerDetails> innerDetails;
    @Data
    public static class InnerDetails {
        @ApiModelProperty(value = "收款方式")
        private String gatherName;
        @ApiModelProperty(value = "销售收入")
        private BigDecimal consumerAmount;
    }

    public BigDecimal getTotalAmount() {
        return Optional.ofNullable(consumerAmount).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(prepaidAmount).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(reserveAmount).orElse(BigDecimal.ZERO));
    }

}