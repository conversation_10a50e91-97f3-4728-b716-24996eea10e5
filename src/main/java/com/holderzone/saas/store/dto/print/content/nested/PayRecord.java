package com.holderzone.saas.store.dto.print.content.nested;

import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayRecord
 * @date 2018/07/25 16:11
 * @description 支付方式
 * @program holder-saas-store-print
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "支付方式记录")
public class PayRecord implements Serializable {

    private static final long serialVersionUID = -1851067545250063984L;

    @NotBlank(message = "支付方式名称不能为空")
    @ApiModelProperty(value = "支付方式名称")
    private String payName;

    @NotNull(message = "支付金额不能为空")
    @ApiModelProperty(value = "支付金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "支付金额")
    private String amountStr;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "支付流水号")
    private String paySerialNumber;

    @ApiModelProperty(value = "余出")
    private BigDecimal excessAmount;

    @ApiModelProperty(value = "余出")
    private String excessAmountStr;

    @ApiModelProperty(value = "商家预计应得金额")
    private BigDecimal estimatedAmount;

    @ApiModelProperty(value = "商家预计应得金额")
    private String estimatedAmountStr;

    @ApiModelProperty(value = "订单笔数")
    private Long orderCount;

    @ApiModelProperty(value = "订单笔数")
    private String orderCountStr;

    @ApiModelProperty(value = "团购券张数")
    private Long grouponCount;

    @ApiModelProperty(value = "团购券张数")
    private String grouponCountStr;

    @ApiModelProperty(value = "是否团购")
    private Boolean isGroupon;

    @ApiModelProperty(value = "使用明细")
    private List<AmountItemDTO.InnerDetails> innerDetails;

    @Data
    public static class InnerDetails {
        @ApiModelProperty("明细")
        private String name;
        @ApiModelProperty("统计金额")
        private BigDecimal amount;
        @ApiModelProperty(value = "优惠金额")
        private BigDecimal discountAmount;
        @ApiModelProperty(value = "订单笔数")
        private Long orderCount;
        @ApiModelProperty(value = "订单笔数")
        private String orderCountStr;
        @ApiModelProperty(value = "团购券张数")
        private Long grouponCount;
        @ApiModelProperty(value = "团购券张数")
        private String grouponCountStr;
        @ApiModelProperty(value = "使用明细")
        private List<AmountItemDTO.InnerDetails> innerDetails;
    }

    public PayRecord(String payName, BigDecimal amount, String paySerialNumber) {
        this.payName = payName;
        this.amount = amount;
        this.paySerialNumber = paySerialNumber;
    }
}
