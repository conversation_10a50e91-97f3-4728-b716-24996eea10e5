package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> R
 * @date 2020/12/22 14:11
 * @description
 */
@Data
@ApiModel(value = "系统默认支付方式DTO")
public class PaymentTypeEnumDTO {
    @ApiModelProperty(value = "支付方式name")
    private String paymentTypeName;

    @ApiModelProperty(value = "0:现金支付，1:聚合支付，2:银行卡支付，3:会员卡支付")
    private Integer paymentType;
}
