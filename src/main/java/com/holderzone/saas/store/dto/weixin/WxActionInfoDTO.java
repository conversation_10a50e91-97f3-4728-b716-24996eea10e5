package com.holderzone.saas.store.dto.weixin;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxActionInfoDTO
 * @date 2019/03/20 10:29
 * @description 微信二维码详细信息请求参数
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("微信二维码详细信息请求参数")
public class WxActionInfoDTO {

    @ApiModelProperty("微信二维码场景值参数")
    @JsonProperty(value = "scene")
    private WxScene wxScene;
}
