package com.holderzone.saas.store.dto.kds.req;

import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.constraints.*;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PrdItemStatusReqDTO extends PageDTO {

    private static final long serialVersionUID = -4247230675662759853L;

    @NotBlank(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @NotBlank(message = "设备ID不得为空")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @Nullable
    @ApiModelProperty(value = "堂口Guid")
    private String pointGuid;

    @NotNull(message = "屏幕显示分类不得为空")
    @Pattern(regexp = "^[1248]$")
    @ApiModelProperty(value = "屏幕显示分类：外卖=1，快餐=2，正餐=4，全部=8")
    private String displayType;

    @NotNull(message = "显示模式不得为空")
    @Min(value = 0, message = "显示模式" +
            "制作点位显示模式：0=堂口模式，1=菜品汇总模式，2=订单模式，3=复合模式,4=混合模式1*6,5=混合-订单模式1*5,6=混合-汇总模式1*10")
    @Max(value = 6, message = "显示模式" +
            "制作点位显示模式：0=堂口模式，1=菜品汇总模式，2=订单模式，3=复合模式,4=混合模式1*6,5=混合-订单模式1*5,6=混合-汇总模式1*10")
    @ApiModelProperty(value = "显示模式" +
            "制作点位显示模式：0=堂口模式，1=菜品汇总模式，2=订单模式，3=复合模式,4=混合模式1*6,5=混合-订单模式1*5,6=混合-汇总模式1*10")
    private Integer displayMode;

    @Nullable
    @ApiModelProperty(value = "查询模式：0=按菜品汇总，1=按订单")
    private Integer queryModeForPoint;

    @NotNull(message = "厨房商品状态不得为空")
    @Min(value = 4, message = "厨房商品状态：4=待制作，5=制作中")
    @Max(value = 5, message = "厨房商品状态：4=待制作，5=制作中")
    @ApiModelProperty(value = "厨房商品状态：4=待制作，5=制作中")
    private Integer kitchenState;

    @ApiModelProperty(value = "查询关键字")
    private String keywords;

    @ApiModelProperty(value = "菜品展示类型 0：汇总中展示，订单中不展示；1：汇总和订单中都展示")
    private Integer itemDisplayType;

    /**
     * 是否允许重复
     */
    @ApiModelProperty(value = "菜品是否允许重复显示")
    private Boolean allowRepeatFlag;
}
