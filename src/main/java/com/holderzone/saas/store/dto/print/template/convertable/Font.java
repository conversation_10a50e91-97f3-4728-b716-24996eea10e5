package com.holderzone.saas.store.dto.print.template.convertable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 字体样式，作用于打印文本
 * 包括字体大小、字体粗体、字体下划线
 *
 * <AUTHOR>
 * @date 2018/12/15 14:30
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class Font implements Serializable {

    /**
     * 小号字体，无加粗，无下划线
     */
    public static final Font SMALL = new Font(Size.SMALL, false, false);

    /**
     * 小号字体，加粗，无下划线
     */
    public static final Font SMALL_BOLD = new Font(Size.SMALL, true, false);

    /**
     * 1x2字体，不加粗，无下划线
     */
    public static final Font SMALL_NORMAL_THIN = new Font(Size.SMALL_NORMAL_THIN, false, false);

    /**
     * 1x2字体，加粗，无下划线
     */
    public static final Font SMALL_NORMAL_THIN_BOLD = new Font(Size.SMALL_NORMAL_THIN, true, false);

    /**
     * 正常字体，不加粗，无下划线
     */
    public static final Font NORMAL = new Font(Size.NORMAL, false, false);

    /**
     * 正常字体，加粗，无下划线
     */
    public static final Font NORMAL_BOLD = new Font(Size.NORMAL, true, false);

    /**
     * 大号字体，不加粗，无下划线
     */
    public static final Font BIG = new Font(Size.BIG, false, false);

    /**
     * 大号字体，加粗，无下划线
     */
    public static final Font BIG_BOLD = new Font(Size.BIG, true, false);

    private static final long serialVersionUID = 7277348152651495946L;

    @ApiModelProperty(value = "字体大小")
    private Size size;

    @ApiModelProperty(value = "是否粗体")
    private boolean bold;

    @ApiModelProperty(value = "是否下划线")
    private boolean underline;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Size {

        public static final Size SMALL = new Size(1, 1);

        public static final Size SMALL_NORMAL_THIN = new Size(1, 2);

        public static final Size NORMAL = new Size(2, 2);

        public static final Size BIG = new Size(3, 3);

        @ApiModelProperty(value = "横坐标放大倍数")
        private int xm;

        @ApiModelProperty(value = "纵坐标放大倍数")
        private int ym;
    }
}
