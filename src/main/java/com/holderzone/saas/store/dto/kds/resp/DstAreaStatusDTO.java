package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class DstAreaStatusDTO implements Serializable {

    private static final long serialVersionUID = -1158994112223699540L;

    @ApiModelProperty(value = "区域Guid")
    private String areaGuid;

    @ApiModelProperty(value = "区域Guid")
    private String areaName;

    @ApiModelProperty(value = "是否被当前设备绑定")
    private Boolean isBoundBySelf;

    @ApiModelProperty(value = "是否被其他设备绑定")
    private Boolean isBoundByOthers;
}
