package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/27
 * @description 营销活动信息返回
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "活动选择")
public class MarketingActivityInfoRespDTO implements Serializable {

    private static final long serialVersionUID = -7738555743048820455L;

    @ApiModelProperty("营销活动guid")
    private String activityGuid;

    @ApiModelProperty("活动标题")
    private String activityName;

    /**
     * @see DiscountTypeEnum
     */
    @ApiModelProperty("活动类型 16限时特价 12满减满折")
    private Integer activityType;

    @ApiModelProperty("活动规则")
    private String activityRule;

    @ApiModelProperty("活动是否可用")
    private boolean useAble;

    @ApiModelProperty("活动不可用原因")
    private String unUseReason;

    @ApiModelProperty("优惠金额")
    private BigDecimal discountPrice;

    @ApiModelProperty("活动商品规格优惠金额")
    private Map<String, BigDecimal> skuDiscountPriceMap = new HashMap<>();

}
