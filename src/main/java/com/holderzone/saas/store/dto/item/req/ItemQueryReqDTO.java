package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishQueryReqDTO
 * @date 2019/01/11 下午2:25
 * @description //web端商品列表查询实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class ItemQueryReqDTO extends PageDTO {
    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "分类GUID;若查询全部分类，则传空字符串")
    private String typeGuid;

    @ApiModelProperty(value = "是否上架：-1:全部;0：否;1:是")
    private Integer isRack;

    @ApiModelProperty(value = "是否上架一体机（0：否，1：是）")
    private Integer isJoinAio;

    @ApiModelProperty(value = "是否上架POS机（0：否，1：是）")
    private Integer isJoinPos;

    @ApiModelProperty(value = "是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;

    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "查询模式， 默认null：非团餐查询  1：团餐查询")
    private Integer model;

    @ApiModelProperty(value = "是否加入整单折扣(0：否，1：是)")
    private Integer isWholeDiscount;

    @ApiModelProperty(value = "商品编号(小程序端需要此值用来做item的模糊查询)")
    private String code;

    @ApiModelProperty(value = "商品类型列表 对应Item的itemType字段 商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品 5.团餐")
    private List<Integer> itemTypeList;

    @ApiModelProperty(value = "搜索条件，code或则商品名称")
    private String searchKey;

    /**
     * 菜谱方案guid
     */
    @ApiModelProperty(value = "方案guid")
    private String planGuid;

    @ApiModelProperty(value = "方案guids")
    private List<String> planGuidList;

    @ApiModelProperty(value = "需要查询的商品guid")
    private List<String> itemGuidList;

    @ApiModelProperty(value = "需要过滤的商品guid")
    private List<String> filterItemList;

    @ApiModelProperty(value = "方案分页的商品guid")
    private List<String> planItemList;

    @ApiModelProperty(value = "需要过滤的规格guid")
    private List<String> filterSkuList;

    @ApiModelProperty(value = "品牌guidList")
    private List<String> brandGuidList;

}
