package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishPropertyValueReqDTO
 * @date 2019/01/08 上午11:36
 * @description //新增商品时属性值的实体
 * @program holder-saas-store-dto
 */
@Data
public class ItemAttrReqDTO implements Serializable {
    /**
     * 商品与属性组关联实体的唯一标识
     */
    private String itemAttrGroupGuid;
    @ApiModelProperty(value = "属性详情与商品和属性组关联实体的关联实体GUID")

    private String attrItemAttrGroupGuid;
    @ApiModelProperty(value = "商品属性值GUID", required = true)
    @NotNull
    private String attrGuid;

    @ApiModelProperty(value = "是否默认，1：是，0,否", required = true)
    @NotNull
    private Integer isDefault;
}
