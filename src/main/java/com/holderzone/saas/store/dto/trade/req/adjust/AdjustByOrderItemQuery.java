package com.holderzone.saas.store.dto.trade.req.adjust;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 调整单-订单商品列表查询实体
 * @date 2022/1/18 15:35
 * @className: AdjustByOrderItemQuery
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "调整单-订单商品列表查询实体")
public class AdjustByOrderItemQuery implements Serializable {

    private static final long serialVersionUID = 5701246933383362803L;

    @ApiModelProperty(value = "交易模式(0：正餐，1：快餐 2外卖)")
    private Integer tradeMode;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;
}
