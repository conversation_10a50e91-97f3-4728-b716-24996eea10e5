package com.holderzone.saas.store.dto.print;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.util.List;

/**
 * 打印结果推送DTO
 *
 * <AUTHOR>
 * @date 2018/10/26 10:08
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "打印记录请求实体")
public class PrintRecordReqDTO extends BaseDTO {

    public static final Integer STATUS_INITIAL = 0;

    public static final Integer STATUS_SUCCEED = 1;

    public static final Integer STATUS_FAILED = 2;

    @ApiModelProperty(value = "消息id")
    private String msgId;

    @NotBlank(message = "设备ID不能为空", groups = ListRecord.class)
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @NotBlank(message = "打印记录GUID不能为空", groups = {
            UpdateRecord.class,
            DeleteRecord.class
    })
    @ApiModelProperty(value = "打印记录GUID")
    private String recordGuid;

    @JsonProperty(value = "status")
    @NotNull(message = "状态不能为空", groups = {
            UpdateRecord.class,
            ListRecord.class
    })
    @Min(value = 0, groups = ListRecord.class)
    @Min(value = 1, groups = UpdateRecord.class)
    @Max(value = 2, groups = {UpdateRecord.class, ListRecord.class})
    @ApiModelProperty(value = "打印状态:1=成功,2=失败")
    private Integer printStatus;

    @ApiModelProperty(value = "打印状态消息详情")
    private String printStatusMsg;

    @JsonProperty(value = "recordGuidList")
    @NotEmpty(message = "打印记录GUID列表不能为空", groups = BatchDeleteRecord.class)
    @ApiModelProperty(value = "打印记录列表")
    private List<String> arrayOfRecordGuid;

    public interface UpdateRecord {

    }

    public interface ListRecord {

    }

    public interface DeleteRecord {

    }

    public interface BatchDeleteRecord {

    }
}
