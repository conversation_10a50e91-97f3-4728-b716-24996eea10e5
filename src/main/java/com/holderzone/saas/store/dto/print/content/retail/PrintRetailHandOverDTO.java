package com.holderzone.saas.store.dto.print.content.retail;

import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.content.nested.InOutRecord;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 交接单
 *
 * <AUTHOR>
 * @date 2018/09/19 17:29
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("交接单")
public class PrintRetailHandOverDTO extends PrintDTO {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private String storeName;

    @NotBlank(message = "交接员工姓名不能为空")
    @ApiModelProperty(value = "交接员工", required = true)
    private String staffName;

    @NotBlank(message = "时长不能为空")
    @ApiModelProperty(value = "时长", required = true)
    private String duration;

    @NotBlank(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始时间", required = true)
    private String beginTime;

    @NotBlank(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间", required = true)
    private String overTime;

    @NotNull(message = "当班营业额不能为空")
    @ApiModelProperty(value = "当班营业额", required = true)
    private BigDecimal dutyAmount;

    @Valid
    @ApiModelProperty(value = "各项收入列表")
    private List<PayRecord> payRecordList;

    @Valid
    @ApiModelProperty(value = "销售收入")
    private List<InOutRecord> inOutRecordList;
}
