package com.holderzone.saas.store.dto.weixin.deal;

import com.holderzone.saas.store.dto.weixin.member.MemberDiscountDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("优惠明细")
@Data
public class ConcessionTotalRespDTO {
	private List<MemberDiscountDTO> memberDiscountDTOS= Collections.emptyList();

	@ApiModelProperty("优惠合计")
	private BigDecimal discountAmount;

	@ApiModelProperty(value = "true：有选中的会员卡或优惠券，false：没有")
	private Boolean uck=false;

	@ApiModelProperty(value = "1:启用积分抵扣，0：未开启")
	private Integer enableIntegral=0;

	@ApiModelProperty(value = "积分")
	private Integer integral=0;

	@ApiModelProperty(value = "积分抵扣金额")
	private BigDecimal integralFee=BigDecimal.ZERO;

	@ApiModelProperty(value = "1：积分选中，0：未选中")
	private Integer uckIntegral=0;
}
