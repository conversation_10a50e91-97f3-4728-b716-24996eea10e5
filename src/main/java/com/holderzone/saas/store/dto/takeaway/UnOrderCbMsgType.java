package com.holderzone.saas.store.dto.takeaway;

/**
 * UnOrder回调消息类型，融合不同外卖平台的消息为一致的消息定义
 */
public class UnOrderCbMsgType {

    private UnOrderCbMsgType() {
    }

    /**
     * 订单生效
     */
    public static final int ORDER_ACTIVATED = 1;

    /**
     * 订单已确认
     */
    public static final int ORDER_CONFIRMED = 2;

    /**
     * 订单配送中
     */
    public static final int ORDER_SHIPPING = 3;

    /**
     * 订单配送完成
     */
    public static final int ORDER_SHIP_SUCCEED = 4;

    /**
     * 订单已完成
     */
    public static final int ORDER_FINISHED = 5;

    /**
     * 订单已取消：商家确认前取消
     */
    public static final int ORDER_CANCELED_BY_MCHNT_BEFORE_CONFIRMATION = 6;

    /**
     * 订单已取消：除商家确认前取消
     */
    public static final int ORDER_CANCELED = 7;

    /**
     * 订单已催单
     */
    public static final int ORDER_URGED = 11;

    /**
     * 申请取消单
     */
    public static final int ORDER_CANCEL_REQ = 20;

    /**
     * 申请取消取消单
     */
    public static final int ORDER_CANCEL_CANCEL_REQ = 21;

    /**
     * 商户同意取消单
     */
    public static final int ORDER_CANCEL_AGREED = 22;

    /**
     * 商户不同意取消单
     */
    public static final int ORDER_CANCEL_DISAGREED = 23;

    /**
     * 客服仲裁取消订单有效
     */
    public static final int ORDER_CANCEL_ARBITRATION_EFFECTIVE = 24;

    /**
     * 申请退单
     */
    public static final int ORDER_REFUND_REQ = 30;

    /**
     * 申请取消退单
     */
    public static final int ORDER_CANCEL_REFUND_REQ = 31;

    /**
     * 商户同意退单
     */
    public static final int ORDER_REFUND_AGREED = 32;

    /**
     * 商户不同意退单
     */
    public static final int ORDER_REFUND_DISAGREED = 33;

    /**
     * 客服仲裁退单有效
     */
    public static final int ORDER_REFUND_ARBITRATION_EFFECTIVE = 34;

    /**
     * 自营外卖订单保存
     */
    public static final int ORDER_OWN_SAVE = 60;

    /**
     * 订单被骑手接单
     */
    public static final int ORDER_SHIPPING_DISTRIBUTE = 61;

    /**
     * 一城飞客发配送单失败
     */
    public static final int ORDER_DELIVERY_ERROR = 62;

    /**
     * 赚餐外卖出餐成功
     */
    public static final int ORDER_DINING_OUT_TCD = 63;
}
