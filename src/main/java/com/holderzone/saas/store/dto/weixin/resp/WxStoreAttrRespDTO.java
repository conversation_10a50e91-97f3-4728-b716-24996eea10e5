package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreAttrRespDTO
 * @date 2019/3/15
 */
@Data
@ApiModel("微信门店菜品属性")
public class WxStoreAttrRespDTO {

    @ApiModelProperty(value = "商品属性组guid")
    private String attrGroupGuid;

    @ApiModelProperty(value = "商品属性详情GUID", required = true)
    private String attrGuid;

    @ApiModelProperty(value = "属性详情名称", required = true)
    private String name;

    @ApiModelProperty(value = "价格", required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "是否选中该属性，1：是，0：否")
    private Integer userck = 0;
}
