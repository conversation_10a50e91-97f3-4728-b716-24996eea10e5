package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> R
 * @date 2021/1/11 16:35
 * @description
 */
@Data
@ApiModel("微信卡片消息信DTO")
public class WxCardMessageReqDTO {
    @ApiModelProperty(value = "主键")
    private String guid;

    @ApiModelProperty(value = "订单id")
    private String orderGuid;

//    @ApiModelProperty(value="最小有效期")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
//    private LocalDateTime minValidTime;
//
//    @ApiModelProperty(value="最大有效期")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
//    private LocalDateTime maxValidTime;
}
