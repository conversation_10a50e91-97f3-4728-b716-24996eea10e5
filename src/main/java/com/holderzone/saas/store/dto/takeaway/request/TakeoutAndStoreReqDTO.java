package com.holderzone.saas.store.dto.takeaway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 外卖和商品
 * @date 2022/5/18 14:41
 * @className: TakeoutAndStoreReqDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "外卖和商品")
public class TakeoutAndStoreReqDTO implements Serializable {

    private static final long serialVersionUID = -1578221467277826020L;

    /**
     * 外卖商品编号列表
     */
    @ApiModelProperty("外卖商品编号列表")
    private String takeoutItemNumber;

    @ApiModelProperty("门店guid列表")
    private String storeGuid;
}
