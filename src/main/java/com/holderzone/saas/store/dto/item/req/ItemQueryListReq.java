package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 门店一体机等终端 桌台菜品查询请求实体
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class ItemQueryListReq {

    @ApiModelProperty(value = "门店guid", required = true)
    @NotBlank(message = "门店Guid不能为空")
    private String storeGuid;

    @ApiModelProperty(value = "适用场景（1商城;2外卖;3堂食，用英文逗号分隔）")
    private String sceneCode;

    @ApiModelProperty(value = "是否上架，0 全部，1 只返回上架的,默认只返回上架的")
    private Integer isRack = 1;

    @ApiModelProperty(value = "是否上架一体机（0：否，1：是）")
    private Integer isJoinAio;

    @ApiModelProperty(value = "是否上架POS机（0：否，1：是）")
    private Integer isJoinPos;

    @ApiModelProperty(value = "是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;

    @ApiModelProperty("运营主体Guid")
    private String operSubjectGuid;
    //商品guid
    private String itemGuid;
}
