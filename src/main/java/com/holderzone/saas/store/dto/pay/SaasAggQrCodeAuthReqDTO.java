package com.holderzone.saas.store.dto.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 18-6-6
 */
@Data
@ApiModel
public class SaasAggQrCodeAuthReqDTO {

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;
}
