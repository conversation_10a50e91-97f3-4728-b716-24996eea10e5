package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.saas.store.dto.weixin.WxStoreCityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreCityListRespDTO
 * @date 2019/05/18 18:33
 * @description 城市列表DTO
 * @program holder-saas-store
 */
@Data
@ApiModel("城市列表DTO")
public class WxStoreCityListRespDTO {

    List<WxStoreCityDTO> cityList;

    List<WxStoreCityDTO> otherCities;

    @ApiModelProperty("该品牌下所有城市门店数量")
    Integer totalCount;
}
