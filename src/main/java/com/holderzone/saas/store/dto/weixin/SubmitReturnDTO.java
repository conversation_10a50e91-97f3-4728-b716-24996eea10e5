package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "微信下单返回")
public class SubmitReturnDTO {

	@ApiModelProperty(value = "0:成功，-1：估清")
	private Integer errorCode=0;

	@ApiModelProperty(value = "错误返回")
	private String errorMsg;

	@ApiModelProperty(value = "订单guid")
	private String orderGuid;
}
