package com.holderzone.saas.store.dto.item.req.price;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 批量编辑新增商品基础菜品基础信息
 * @date 2021/10/9
 */
@Data
public class PlanPriceAddBaseDTO {

    @ApiModelProperty(value = "方案GUID")
    private String planGuid;

    @ApiModelProperty(value = "方案名称")
    private String planName;

    @ApiModelProperty(value = "商品分类guid")
    private String typeGuid;

    @ApiModelProperty(value = "商品分类名称")
    private String typeName;
}
