package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Api("返回会员卡列表")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxMemberCardRespDTO {

	@ApiModelProperty(value = "0：正常，1：不正常")
	private Integer result;

	private List<WxMemberCardDTO> wxMemberCardDTOS;

	private String errorMsg;

}
