package com.holderzone.saas.store.dto.order.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SkuPropertyDTO
 * @date 2018/09/14 15:50
 * @description 菜品属性
 * @program holder-saas-store-order
 */
@Data

public class SkuPropertyDTO {

    @ApiModelProperty(value = "订单属性guid")
    private String skuPropertyGuid;

    @ApiModelProperty(value = "属性guid", required = true)
    private String propertyDishGuid;

    @ApiModelProperty(value = "属性名称")
    private String propertyDishName;

    @ApiModelProperty(value = "属性组名称")
    private String propertyGroupName;

    @ApiModelProperty(value = "属性价格")
    private BigDecimal propertyPrice;

    @ApiModelProperty(value = "属性数量", required = true)
    private Integer num;

}
