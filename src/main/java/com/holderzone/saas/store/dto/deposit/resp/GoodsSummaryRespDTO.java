package com.holderzone.saas.store.dto.deposit.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class GoodsSummaryRespDTO implements Serializable {

    private static final long serialVersionUID = -2773042054434843563L;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "库存总量")
    private int sum;

}