package com.holderzone.saas.store.dto.map;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GeoCodeDTO
 * @date 2019/05/18 17:15
 * @description 地理编码DTO
 * @program holder-saas-store
 */
@Data
@ApiModel("地理编码DTO")
@AllArgsConstructor
@NoArgsConstructor
public class GeoCodeDTO {

    /**
     * 结构化地址信息
     */
    @JsonProperty(value = "formatted_address")
    private String formattedAddress;

    /**
     * 国家
     */
    private String country;

    /**
     * 地址所在的省份名
     * 此处需要注意的是，中国的四大直辖市也算作省级单位。
     */
    private String province;

    /**
     * 地址所在的城市名
     */
    private List<String> city;

    /**
     * 城市编码
     */
    @JsonProperty(value = "citycode")
    private String cityCode;

    /**
     * 地址所在的区
     */
    private List<String> district;

    /**
     * 地址所在的乡镇
     */
    private List<String> township;

    /**
     * 社区信息列表
     */
    private NeighborhoodDTO neighborhood;

    /**
     * 楼信息列表
     */
    private BuildingDTO building;

    /**
     * 区域编码
     */
    @JsonProperty(value = "adcode")
    private String adCode;

    /**
     * 街道
     */
    private List<String> street;

    /**
     * 门牌
     */
    private List<String> number;

    /**
     * 坐标点
     */
    private String location;

    /**
     * 匹配级别
     */
    private String level;


    public void setCity(Object city) {
        if (city instanceof String)
            this.city = Arrays.asList(String.valueOf(city));
        else
            this.city = (List<String>) city;
    }

    public void setDistrict(Object district) {
        if (district instanceof String)
            this.district = Arrays.asList(String.valueOf(district));
        else
            this.district = (List<String>) district;
    }

    public void setNumber(Object number) {
        if (number instanceof String) {
            this.number = Arrays.asList(String.valueOf(number));
        } else {
            this.number = (List<String>) number;
        }
    }
    public void setStreet(Object street) {
        if (street instanceof String) {
            this.street = Arrays.asList(String.valueOf(street));
        } else {
            this.street = (List<String>) street;
        }
    }
    public void setTownship(Object township) {
        if (township instanceof String) {
            this.township = Arrays.asList(String.valueOf(township));
        } else {
            this.township = (List<String>) township;
        }
    }
}


