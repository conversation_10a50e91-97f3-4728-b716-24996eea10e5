package com.holderzone.saas.store.dto.order.request.dinein;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 修改订单实体
 * @date 2022/2/28 15:24
 * @className:
 */
@Data
@ApiModel(value = "修改订单实体")
public class UpdateOrderReqDTO implements Serializable {

    private static final long serialVersionUID = 890716394758979706L;

    @ApiModelProperty(value = "状态")
    private Boolean state;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;
}
