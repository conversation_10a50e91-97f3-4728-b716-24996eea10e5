package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class TradeTakeoutInfoDTO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    @ApiModelProperty(value = "平台名称")
    private String platformName;

    @ApiModelProperty(value = "平台订单号")
    private String platformNumber;

    @ApiModelProperty(value = "平台日流水号")
    private String platformSerialNo;

    @ApiModelProperty(value = "是否自动接单")
    private Boolean isAutoAccept;
}
