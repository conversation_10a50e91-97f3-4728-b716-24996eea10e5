package com.holderzone.saas.store.dto.business;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemDiscountReqDTO
 * @date 2019/11/01 17:23
 * @description 商户后台系统省零请求入参
 * @program holder-saas-store
 */
@Data
@ApiModel("商户后台系统省零请求入参")
public class SystemDiscountReqDTO {

    @ApiModelProperty(value = "storeGuid")
    private String storeGuid;

    @ApiModelProperty(value = "storeName")
    private String storeName;

    @ApiModelProperty(value = "系统省零Guid")
    private String systemDiscountGuid;

    @ApiModelProperty(value = "折扣临界值")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "省零方式 0不省零,1：四舍五入，2：只舍不入，3：只入不舍")
    private Integer roundType;

    @ApiModelProperty(value = "保留到，0:保留到元，1:保留到角")
    private Integer scale;

    @ApiModelProperty(value = "0:启用，1:禁用")
    private Integer state;

}
