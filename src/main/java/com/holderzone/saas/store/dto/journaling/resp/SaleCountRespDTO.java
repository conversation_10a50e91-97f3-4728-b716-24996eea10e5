package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("老板助手-销售统计")
public class SaleCountRespDTO {

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "商品名")
    private String itemName;

    @ApiModelProperty(value = "分类名")
    private String typeName;

    @ApiModelProperty(value = "商品类型（1.套餐主项，2.规格，3.称重，4.单品 ）")
    private Integer itemType;

    @ApiModelProperty(value = "销量")
    private Double saleNumber;

    @ApiModelProperty(value = "销售额")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "销售额占比")
    private double percent;

    @ApiModelProperty(value = "记数单位")
    private String unit;

    @ApiModelProperty(value = "规格guid")
    private String skuGuid;

    @ApiModelProperty(value = "商品类别guid")
    private String itemTypeGuid;
}
