package com.holderzone.saas.store.dto.reserve;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/11
 * @description 通知支付
 */
@Data
@ApiModel(value = "通知支付")
@EqualsAndHashCode(callSuper = false)
public class NotifyPayReqDTO implements Serializable {

    private static final long serialVersionUID = 5561479232093249195L;

    @ApiModelProperty(value = "预定guid")
    private String reserveGuid;

    @ApiModelProperty(value = "会员电话")
    private String memberPhone;

}
