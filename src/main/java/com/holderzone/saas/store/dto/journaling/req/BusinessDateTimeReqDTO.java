package com.holderzone.saas.store.dto.journaling.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("报表-营业日期时间请求入参")
public class BusinessDateTimeReqDTO {

    @ApiModelProperty(value = "传入日期类型list，1：今天，7：近7天，30：近30天")
    @NotNull(message = "传入日期类型不为空")
    private List<Integer> dateTypes;

    @ApiModelProperty(value = "门店guid list")
    @NotNull(message = "传入门店guid list不为空")
    private List<String> storeGuids;
}
