package com.holderzone.saas.store.dto.order.common;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.weixin.MinPriceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderItemDTO
 * @date 2018/09/14 15:48
 * @description 订单商品DTO
 * @program holder-saas-store-trade
 */
@Data
@Accessors(chain = true)
public class DineInItemDTO implements Serializable {

    static SerializeConfig config = new SerializeConfig();

    static {
        config.addFilter(DineInItemDTO.class, new SimplePropertyPreFilter("itemGuid", "skuList", "attrGroupList", "subgroupList"));
    }

    private static final DateTimeFormatter UTC_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
    private static final DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final long serialVersionUID = 8618058957722064841L;

    @ApiModelProperty(value = "订单商品guid")
    private String guid;
    @ApiModelProperty(value = "订单guid")
    private String orderGuid;
    @ApiModelProperty(value = "反结账原菜品guid")
    private Long originalOrderItemGuid;

    @ApiModelProperty(value = "调整商品数量")
    private BigDecimal adjustNumber;

    @ApiModelProperty(value = "调整商品回退库存数量")
    private BigDecimal rollbackCount;

    @ApiModelProperty(value = "创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "用户微信公众号openId")
    private String userWxPublicOpenId;

    @ApiModelProperty(value = "微信菜品批次")
    private String wxBatch;

    @ApiModelProperty(value = "商品guid", required = true)
    private String itemGuid;

    @ApiModelProperty(value = "商品名称", required = true)
    private String itemName;

    @ApiModelProperty(value = "商品编号", required = true)
    private String code;

    @ApiModelProperty(value = "商品类型(1.套餐主项，2.规格，3.称重，4.单品 5.团餐主项)", required = true)
    private Integer itemType;

    @ApiModelProperty(value = "商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂，8.已上菜(已划菜) ，9.预定)")
    private Integer itemState;

    @ApiModelProperty(value = "商品分类guid", required = true)
    private String itemTypeGuid;

    @ApiModelProperty(value = "商品分类名称", required = true)
    private String itemTypeName;

    @ApiModelProperty(value = "规格guid", required = true)
    private String skuGuid;

    @ApiModelProperty(value = "规格名称", required = true)
    private String skuName;

    @ApiModelProperty(value = "sku价格", required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "商品价格小计", required = true)
    private BigDecimal itemPrice;

    @ApiModelProperty(value = "商品毛利率")
    private BigDecimal itemGrossMargin;

    @ApiModelProperty(value = "商品优惠金额", required = true)
    private BigDecimal totalDiscountFee;

    @ApiModelProperty(value = "会员价格")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "属性总价")
    private BigDecimal singleItemAttrTotal;

    @ApiModelProperty(value = "加价总价")
    private BigDecimal singleAddPriceTotal;

    @ApiModelProperty(value = "催品次数", required = true)
    private Integer urgeNum;

    @ApiModelProperty(value = "数量", required = true)
    private BigDecimal currentCount;

    @ApiModelProperty(value = "赠送数量")
    private BigDecimal freeCount;

    @ApiModelProperty(value = "退货数量")
    private BigDecimal returnCount;

    @ApiModelProperty(value = "退款数量")
    private BigDecimal refundCount;

    @ApiModelProperty(value = "计数单位", required = true)
    private String unit;

    @ApiModelProperty(value = "是否已经下单(0:否、1：是)", required = true)
    private Integer isPay;

    @ApiModelProperty(value = "是否参与会员权益折扣", required = true)
    private Integer isMemberDiscount;

    @ApiModelProperty(value = "参与了单品券的数量", required = true)
    private BigDecimal isGoodsReduceDiscount;

    @ApiModelProperty(value = "是否参与整单折扣", required = true)
    private Integer isWholeDiscount;

    @ApiModelProperty(value = "是否用会员价格计算了", required = true)
    private Integer isCaculatByMemberPrice;

    /**
     * see {@link com.holderzone.saas.store.enums.order.ItemPriceChangeEnum}
     */
    @ApiModelProperty(value = "改价类型（0-未改价，1-已改价，2-商品折扣）", required = false)
    private Integer priceChangeType;

    @ApiModelProperty(value = "套餐主项guid")
    private Long parentItemGuid;

    @ApiModelProperty(value = "原价", required = false)
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "折扣百分比（700代表7折）", required = false)
    private Integer discountPercent;

    @ApiModelProperty(value = "菜品upc")
    private String itemUpc;

    @ApiModelProperty(value = "单个菜品优惠券优惠金额（保留4位小数返回）")
    private BigDecimal couponDiscount;

    @ApiModelProperty(value = "单个菜品会员折扣优惠金额（保留4位小数返回）")
    private BigDecimal memberDiscount;

    @ApiModelProperty(value = "商品备注")
    private String remark;

    @ApiModelProperty(value = "起买数")
    private BigDecimal minOrderNum;

    @ApiModelProperty(value = "不能改变的itemprice--辅助字段")
    private BigDecimal beforeItemPrice;

    @ApiModelProperty(value = "创建操作人")
    private String createStaffName;

    @ApiModelProperty(value = "套餐分组")
    private List<PackageSubgroupDTO> packageSubgroupDTOS;

    @ApiModelProperty(value = "赠送商品")
    private List<FreeItemDTO> freeItemDTOS;

    @ApiModelProperty(value = "商品属性")
    private List<ItemAttrDTO> itemAttrDTOS;

    @ApiModelProperty("商品标签：离线订单上传使用")
    private String tag;

    @ApiModelProperty("积分兑换值")
    private Integer pointValue;

    @ApiModelProperty("金额兑换值")
    private BigDecimal pointMoney;

    @ApiModelProperty("是否调整过商品，0：否  1：是")
    private Boolean isAdjustItem;

    @ApiModelProperty("调整类型(0.数量调整，1.菜品更换)")
    private Integer adjustType;

    @ApiModelProperty("外卖映射门店商品数量")
    private BigDecimal mappingCount;

    @ApiModelProperty("会员价优惠金额")
    private BigDecimal memberPreferential;

    @ApiModelProperty("商品券抵扣优惠金额")
    private BigDecimal ticketPreferential;

    @ApiModelProperty("满折活动商品优惠金额")
    private BigDecimal discountPreferential;

    @ApiModelProperty("外卖调整单商品核算价")
    private BigDecimal takeawayAccountingPrice;

    @ApiModelProperty("团购平台")
    private Integer grouponType;

    @ApiModelProperty("商品券券码")
    private String couponCode;

    @ApiModelProperty("使用商品券码时间")
    private LocalDateTime useCouponTime;

    @ApiModelProperty("商品券信息")
    private MtCouponPreRespDTO couponInfo;

    @ApiModelProperty("代金券信息")
    private List<MtCouponPreRespDTO> couponInfos;

    @ApiModelProperty("验券返回信息")
    private GroupVerifyDTO groupVerify;

    @ApiModelProperty(value = "最终商品优惠单价")
    private BigDecimal discountPrice;

    @ApiModelProperty(value = "最终商品实付金额")
    private BigDecimal discountTotalPrice;

    @ApiModelProperty(value = "团购实付金额")
    private BigDecimal grouponDiscountTotalPrice;

    @ApiModelProperty(value = "限时特价总金额")
    private BigDecimal specialsTotalPrice;

    @ApiModelProperty(value = "限时特价优惠的金额（原价-单品券金额-限时特价总金额-限时特价总金额中的会员优惠金额）")
    private BigDecimal specialsDiscountPrice;

    @ApiModelProperty(value = "限时特价金额")
    private BigDecimal specialsPrice;

    @ApiModelProperty(value = "限时特价总金额中的会员优惠金额")
    private BigDecimal specialsMemberPrice;

    @ApiModelProperty(value = "被限时特价覆盖的价格")
    private BigDecimal limitPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "参与限时特价的数量")
    private BigDecimal joinSpecialsCount;

    @ApiModelProperty(value = "参与后单个商品的优惠金额")
    private BigDecimal specialsSingleDistinctPrice;

    @ApiModelProperty(value = "商品总价")
    private BigDecimal totalPrice;

    // ----参数传递所需
    @ApiModelProperty(value = "商品图,列表小图")
    private String pictureUrl = StringUtils.EMPTY;

    private String specString;

    @ApiModelProperty(value = "true：有会员价,false：没有")
    private Boolean enablePreferentialPrice = false;

    @ApiModelProperty(value = "商品原价")
    private BigDecimal showPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "商品会员价")
    private BigDecimal showMemberPrice = BigDecimal.ZERO;

    @ApiModelProperty("最低价")
    private BigDecimal minPrice;

    /**
     * 最低价类型
     *
     * @see MinPriceTypeEnum
     */
    @ApiModelProperty("最低价类型 0原价 1会员价 2会员折扣 3限时特价")
    private Integer minPriceType;

    @ApiModelProperty(value = "限时特价活动guid")
    private String specialsActivityGuid;

    /**
     * 列表小图
     */
    private String smallPicture;

    /**
     * 非套餐商品： 是否有属性
     * 套餐商品： 是否使用套餐上的属性价和加价
     */
    private Integer hasAttr;

    private List<ItemInfoSkuDTO> skuList;
    private List<ItemInfoAttrGroupDTO> attrGroupList;
    private List<ItemInfoSubgroupDTO> subgroupList;

    /**
     * 父实体GUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的实体，则该字段为品牌库对应的实体GUID。
     */
    protected String parentGuid;

    // ----参数传递所需

    /**
     * 是否打厨
     * 默认ture
     */
    @ApiModelProperty(value = "是否打厨")
    private Boolean isKitchen = Boolean.TRUE;

    @ApiModelProperty(value = "是否转菜")
    private Boolean transferFlag = Boolean.TRUE;

    /**
     * 自定义退款金额分摊
     */
    @ApiModelProperty(value = "自定义退款金额分摊")
    private BigDecimal refundPrice;

    //辅助字段  不序列化 也不转化为json
    // 套餐内单位商品的属性加价
    @JsonIgnore
    private transient BigDecimal singleAddPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "快餐验券加购商品是否已验券")
    private Boolean fastFoodCouponVerified;

    //目前beforeItemPrice，ItemPrice是一样的  ItemPrice可能会改   BeforeItemPrice不会  可以用这个追溯以前的ItemPrice
    @JsonIgnore
    public void setItemPiceAndBeforeItemPrice(BigDecimal itemPrice) {
        this.itemPrice = itemPrice;
        this.beforeItemPrice = itemPrice;
    }

    @JsonIgnore
    public int getPriceAndGuidGroupByKey() {
        StringBuilder key = new StringBuilder(getItemGuid() + getSkuGuid() + getOriginalPrice().toString());
        //商品属性不为空
        if (CollectionUtils.isNotEmpty(itemAttrDTOS)) {
            for (ItemAttrDTO itemAttrDTO : itemAttrDTOS) {
                key.append(StringUtils.defaultString(itemAttrDTO.getAttrGuid()));
            }
        }
        //套餐分组不为空
        if (CollectionUtils.isNotEmpty(packageSubgroupDTOS)) {
            for (PackageSubgroupDTO subgroupDTO : packageSubgroupDTOS) {
                key.append(subgroupDTO.getSubgroupGuid());
                //遍历套餐子项
                for (SubDineInItemDTO itemDTO : subgroupDTO.getSubDineInItemDTOS()) {
                    key.append(itemDTO.getGuid()).append(itemDTO.getPrice());
                    //子项商品属性不为空
                    if (CollectionUtils.isNotEmpty(itemDTO.getItemAttrDTOS())) {
                        for (ItemAttrDTO itemAttrDTO : itemDTO.getItemAttrDTOS()) {
                            key.append(itemAttrDTO.getGuid());
                        }
                    }
                }
            }
        }
        return key.toString().hashCode();
    }

}
