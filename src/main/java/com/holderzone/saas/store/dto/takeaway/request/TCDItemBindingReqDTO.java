package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.takeaway.response.TcdDishSku;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@ApiModel
@EqualsAndHashCode(callSuper = true)
public class TCDItemBindingReqDTO extends BaseDTO {

    @ApiModelProperty(value = "绑定token")
    private String token;

    @ApiModelProperty(value = "菜品操作类型（0：绑定、更新 1：解绑）")
    private Integer operateType;

    @ApiModelProperty(value = "菜品列表")
    private List<TcdDishSku> dishSkus;

}
