package com.holderzone.saas.store.dto.member.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberConsumeReqDTO
 * @date 2018/09/29 9:54
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class MemberConsumeReqDTO extends BasePageDTO {

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "0:营业日 1:自然日")
    private int timeType;

    @ApiModelProperty(value = "门店guid")
    private List<String> storeGuids;

    @ApiModelProperty(value = "-1 全部 0:正餐，1:快餐")
    private int tradeMode = -1;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

}
