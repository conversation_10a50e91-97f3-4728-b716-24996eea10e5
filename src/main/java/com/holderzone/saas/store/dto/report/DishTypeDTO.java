package com.holderzone.saas.store.dto.report;

/**
 * <AUTHOR>
 * @date 2018/09/29 下午 17:17
 * @description
 */
public class DishTypeDTO {

    private Boolean delete;
    private String typeGuid;
    private String parentGuid;
    private Boolean parentType;
    private String name;
    private String sort;
    private String brandGuid;
    private String dishType;

    public Boolean getDelete() {
        return delete;
    }

    public void setDelete(Boolean delete) {
        this.delete = delete;
    }

    public String getTypeGuid() {
        return typeGuid;
    }

    public void setTypeGuid(String typeGuid) {
        this.typeGuid = typeGuid;
    }

    public String getParentGuid() {
        return parentGuid;
    }

    public void setParentGuid(String parentGuid) {
        this.parentGuid = parentGuid;
    }

    public Boolean getParentType() {
        return parentType;
    }

    public void setParentType(Boolean parentType) {
        this.parentType = parentType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getBrandGuid() {
        return brandGuid;
    }

    public void setBrandGuid(String brandGuid) {
        this.brandGuid = brandGuid;
    }

    public String getDishType() {
        return dishType;
    }

    public void setDishType(String dishType) {
        this.dishType = dishType;
    }
}
