package com.holderzone.saas.store.dto.store.table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableDO
 * @date 2018/07/24 上午9:55
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class TableStatusUpdateDTO implements Serializable {

    /**
     * 桌台guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "桌台guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "桌台guid", required = true)
    private String tableGuid;

    /**
     * 订单guid
     */
    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    /**
     * 桌台占用
     * 0=未占用
     * 1=已占用
     */
    @ApiModelProperty(value = "桌台占用，0=未占用，1=已占用")
    private Integer status;
}
