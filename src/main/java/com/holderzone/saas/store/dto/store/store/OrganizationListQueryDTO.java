package com.holderzone.saas.store.dto.store.store;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className OrganizationListQueryDTO
 * @date 18-9-18 上午10:32
 * @description 组织查询对象实体
 * @program holder-saas-store-dto
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationListQueryDTO implements Serializable {
    private static final long serialVersionUID = -226464449356874108L;

    /**
     * 组织guid数组
     */
    @ApiModelProperty("组织guid数组")
    private List<String> organizeGuidList;
}
