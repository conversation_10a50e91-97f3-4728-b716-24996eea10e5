package com.holderzone.saas.store.dto.print.template.printable;

import com.holderzone.saas.store.dto.print.template.convertable.Text;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 表格
 * 表格从第2列开始，每列之前都会增加一个空格位，也就是说这些列的实际可用宽度会少一个字条位置。
 * 如：{10,10,10}的列宽设置，而列的实际可用字符位置数为：{10,9,9}
 * 输出完成后自动换新行
 *
 * <AUTHOR>
 * @date 2018/12/15 15:13
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TableRow implements Serializable {

    private static final long serialVersionUID = 8895564055610722407L;

    @ApiModelProperty(value = "列内容")
    private List<Text> columns;

    @ApiModelProperty(value = "每个单元格的宽度（字符数），中文算两个，英文算一个")
    private List<Integer> columnWidths;

    @ApiModelProperty(value = "是否靠右打印，默认为靠左打印")
    private List<Boolean> alignRights;

    @ApiModelProperty(value = "是否单元格内换行")
    private boolean lineFeedInnerCell = false;
}
