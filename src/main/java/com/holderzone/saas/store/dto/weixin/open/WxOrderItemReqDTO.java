package com.holderzone.saas.store.dto.weixin.open;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/10
 * @description 微信订单商品
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "微信订单商品", description = "微信订单商品")
public class WxOrderItemReqDTO implements Serializable {

    private static final long serialVersionUID = -7139580319249680221L;

    @ApiModelProperty(value = "商品Guid")
    private String itemGuid;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "SkuGuid")
    private String skuGuid;

    @ApiModelProperty(value = "SKU名称")
    private String skuName;

    @ApiModelProperty(value = "数量")
    private Integer number;

}
