package com.holderzone.saas.store.dto.order.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderItemDTO
 * @date 2018/09/14 15:48
 * @description 订单商品子项DTO
 * @program holder-saas-store-order
 */
@Data
public class SubDineInItemDTO {

    @ApiModelProperty(value = "订单商品guid")
    private String guid;

    @ApiModelProperty(value = "反结账原菜品guid")
    private Long originalOrderItemGuid;

    @ApiModelProperty(value = "商品guid", required = true)
    private String itemGuid;

    @ApiModelProperty(value = "商品名称", required = true)
    private String itemName;

    private Integer itemState;

    @ApiModelProperty(value = "商品编号", required = true)
    private String code;

    @ApiModelProperty(value = "商品类型(1.套餐主项，2.规格，3.称重，4.单品 )", required = true)
    private Integer itemType;

    @ApiModelProperty(value = "商品分类guid", required = true)
    private String itemTypeGuid;

    @ApiModelProperty(value = "商品分类名称", required = true)
    private String itemTypeName;

    @ApiModelProperty(value = "规格guid", required = true)
    private String skuGuid;

    @ApiModelProperty(value = "规格名称", required = true)
    private String skuName;

    @ApiModelProperty(value = "sku价格", required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "数量", required = true)
    private BigDecimal currentCount;

    @ApiModelProperty(value = "套餐预设数量", required = true)
    private BigDecimal packageDefaultCount;

    @ApiModelProperty(value = "属性总价")
    private BigDecimal singleItemAttrTotal;

    @ApiModelProperty(value = "套餐子项加价")
    private BigDecimal addPrice;

    @ApiModelProperty(value = "计数单位", required = true)
    private String unit;

    @ApiModelProperty(value = "商品备注")
    private String remark;

    /**
     * pad点餐里明确没有这玩意
     */
    @ApiModelProperty(value = "商品属性")
    private List<ItemAttrDTO> itemAttrDTOS;

    @ApiModelProperty(value = "是否套餐子菜换菜")
    private Integer changeFlag;

    @ApiModelProperty(value = "是否首次套餐子菜换菜")
    private Boolean firstChangeFlag;

    @ApiModelProperty(value = "换菜菜品")
    private SubDineInItemDTO changeDishes;

    @ApiModelProperty(value = "换菜批次号")
    private String changeBatchNumber;

    @ApiModelProperty(value = "换菜明细guid")
    private String changeOrderItemGuid;

    @ApiModelProperty(value = "换菜原菜")
    private SubDineInItemDTO originalItem;

    @ApiModelProperty(value = "是否撤销套餐子菜换菜")
    private Integer cancelFlag;

    @ApiModelProperty(value = "是否转菜")
    private Boolean transferFlag;

}
