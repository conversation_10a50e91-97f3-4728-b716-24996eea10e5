package com.holderzone.saas.store.dto.trade;

import com.holderzone.saas.store.dto.order.OrderMultiMemberPayDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 商户后台/数据报表/订单统计/明细  支付方式明细
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
@Data
public class OrderDetailTransactionRecordRespDTO {

    @ApiModelProperty(value = "支付方式名称")
    private String paymentTypeName;

    @ApiModelProperty(value = "支付方式")
    private Integer paymentType;

    @ApiModelProperty(value = "交易金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "订单会员多卡支付信息")
    private List<OrderMultiMemberPayDTO> multiMemberPays;

}
