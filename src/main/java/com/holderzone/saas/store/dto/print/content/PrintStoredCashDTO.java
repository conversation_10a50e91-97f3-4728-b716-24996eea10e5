package com.holderzone.saas.store.dto.print.content;

import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

import static com.holderzone.saas.store.dto.print.util.PrintMockUtils.*;

/**
 * 储值单
 *
 * <AUTHOR>
 * @date 2018/09/19 14:43
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "储值单")
public class PrintStoredCashDTO extends PrintDTO implements PrintDataMockito {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private String storeName;

    @NotBlank(message = "交易流水号不能为空")
    @ApiModelProperty(value = "交易流水号", required = true)
    private String serialNumber;

    @NotNull(message = "充值金额不能为空")
    @ApiModelProperty(value = "充值金额", required = true)
    private BigDecimal recharge;

    @NotNull(message = "赠送金额不能为空")
    @ApiModelProperty(value = "赠送金额", required = true)
    private BigDecimal presented;

    @NotNull(message = "赠送积分不能为空")
    @ApiModelProperty(value = "赠送积分", required = true)
    private BigDecimal presentedIntegral;

    @NotNull(message = "到账金额不能为空")
    @ApiModelProperty(value = "到账金额", required = true)
    private BigDecimal arrival;

    @NotBlank(message = "充值方式不能为空")
    @ApiModelProperty(value = "充值方式", required = true)
    private String payWay;

    @NotBlank(message = "充值卡号不能为空")
    @ApiModelProperty(value = "充值卡号", required = true)
    private String cardNo;

    @NotNull(message = "当前金额不能为空")
    @ApiModelProperty(value = "当前金额", required = true)
    private BigDecimal currentCash;

    @NotNull(message = "当前积分不能为空")
    @ApiModelProperty(value = "当前积分", required = true)
    private String integration;

    @NotNull(message = "充值时间不能为空")
    @ApiModelProperty(value = "充值时间", required = true)
    private Long rechargeTime;

    @ApiModelProperty(value = "门店地址", notes = "如果未设置门店地址，该值为空")
    private String storeAddress;

    @ApiModelProperty(value = "门店电话", notes = "如果未设置门店电话，该值为空")
    private String tel;

    @ApiModelProperty(value = "会员姓名")
    private String memberName;

    @ApiModelProperty(value = "会员手机号")
    private String memberPhone;

    @Override
    public void applyMock() {
        super.applyMock();
        setStoreName(mockStoreName());
        setSerialNumber("0123456789");
        setRecharge(BigDecimal.ONE);
        setPresented(BigDecimal.ONE);
        setPresentedIntegral(BigDecimal.ONE);
        setArrival(BigDecimal.ONE);
        setPayWay("现金支付");
        setCardNo("123456");
        setMemberName(mockMemberName());
        setMemberPhone(mockMemberPhone());
        setCurrentCash(BigDecimal.ONE);
        setIntegration("123");
        setRechargeTime(DateTimeUtils.nowMillis());
        setStoreAddress(mockStoreAddress());
        setTel(mockStoreTel());
    }
}
