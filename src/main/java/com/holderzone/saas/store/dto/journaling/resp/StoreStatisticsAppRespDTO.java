package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreStatisticsAppRespDTO
 * @date 2019/06/03 10:36
 * @description app报表-门店统计respDTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class StoreStatisticsAppRespDTO implements Serializable {

    private static final long serialVersionUID = 7094801479835129404L;

    @ApiModelProperty(value = "一次数据加载时间节点时间戳")
    private long dataEndTime;
    @ApiModelProperty(value = "门店统计数据列表")
    private List<StoreStatisticsDetailDTO> statisticsDetailDTOS;

    public static StoreStatisticsAppRespDTO filterZero(StoreStatisticsAppRespDTO storeStatisticsAppRespDTO) {
        List<StoreStatisticsDetailDTO> statisticsDetailDTOS = storeStatisticsAppRespDTO.getStatisticsDetailDTOS();
        if (!CollectionUtils.isEmpty(statisticsDetailDTOS)) {
            List<StoreStatisticsDetailDTO> respList = statisticsDetailDTOS.stream().filter(e -> e.getBusinessFee().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            storeStatisticsAppRespDTO.setStatisticsDetailDTOS(respList);
        }
        return storeStatisticsAppRespDTO;
    }

    @Data
    @ApiModel
    public static class StoreStatisticsDetailDTO implements Serializable {
        @ApiModelProperty(value = "门店guid")
        private String storeGuid;
        @ApiModelProperty(value = "门店名称")
        private String storeName;
        @ApiModelProperty(value = "营业额")
        private BigDecimal businessFee;
        @ApiModelProperty(value = "订单数")
        private Integer orderCount;
    }

}
