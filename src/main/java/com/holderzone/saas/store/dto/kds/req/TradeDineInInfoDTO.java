package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class TradeDineInInfoDTO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    @ApiModelProperty(value = "区域Guid")
    private String areaGuid;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "桌台Guid")
    private String tableGuid;

    @ApiModelProperty(value = "桌台名称")
    private String tableName;

    @ApiModelProperty(value = "订单流水号")
    private String orderNo;

    @ApiModelProperty(value = "是否联台单")
    private Boolean associatedFlag;

    @ApiModelProperty(value = "联台单编号")
    private String associatedSn;

    @ApiModelProperty(value = "联台单桌台列表")
    private List<String> associatedTableGuids;

    @ApiModelProperty(value = "联台桌台名称")
    private List<String> associatedTableNames;
}
