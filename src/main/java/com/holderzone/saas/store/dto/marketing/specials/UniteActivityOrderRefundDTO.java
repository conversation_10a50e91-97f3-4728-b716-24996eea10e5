package com.holderzone.saas.store.dto.marketing.specials;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;


/**
 * 营销活动退款请求
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class UniteActivityOrderRefundDTO implements Serializable {

    private static final long serialVersionUID = 5528866350378721845L;

    @NotEmpty(message = "活动类型不能为空")
    private List<Integer> activityTypes;

    @NotBlank(message = "订单guid不能为空")
    private String orderGuid;

}
