package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutShopBindReqDTO
 * @date 2018/09/25 9:07
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@ApiModel
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TakeoutItemBindReqDTO extends BaseDTO {

    @Min(value = 1, message = "外卖类型(1：美团，2：饿了么)")
    @Max(value = 2, message = "外卖类型(1：美团，2：饿了么)")
    @ApiModelProperty(value = "外卖类型(1：美团，2：饿了么)")
    private Integer takeoutType;
}
