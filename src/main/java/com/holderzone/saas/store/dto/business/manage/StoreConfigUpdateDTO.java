package com.holderzone.saas.store.dto.business.manage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.holderzone.saas.store.enums.business.PrintItemOrderEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreConfigDO
 * @date 2018/07/29 下午4:05
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class StoreConfigUpdateDTO implements Serializable {

    private static final long serialVersionUID = -3106463042960831628L;

    /**
     * 门店guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    /**
     * 平板下单是否启用密码
     * 0=未启用
     * 1=已启用
     */
    @ApiModelProperty("平板下单是否启用密码：0=未启用，1=已启用")
    private Integer enablePadPwd;

    /**
     * 是否启用划菜
     * 0=未启用
     * 1=已启用
     */
    @ApiModelProperty("是否启用划菜：0=未启用，1=已启用")
    private Integer enableMarkDish;

    /**
     * 是否启用会员价
     * 0=未启用
     * 1=已启用
     */
    @ApiModelProperty("是否启用会员价：0=未启用，1=已启用")
    private Integer enableMemPrice;

    /**
     * 是否启用交接班
     * 0=未启用
     * 1=已启用
     */
    @ApiModelProperty("是否启用交接班：0=未启用，1=已启用")
    private Integer enableHandover;

    /**
     * 微信点餐模式
     * 0=堂食
     * 1=快餐
     */
    @ApiModelProperty("微信点餐模式：0=堂食，1=快餐")
    private Integer wechatOrderMode;

    /**
     * 流水号模式
     * 0=自增
     * 1=随机
     */
    @ApiModelProperty("流水号模式：0=自增，1=随机")
    private Integer serialNumberMode;

    /**
     * 门店营业开始时间（00:00:00-23:59:59）
     */
    @ApiModelProperty("门店营业开始时间（00:00:00-23:59:59）")
    @JsonFormat(pattern = "HH:mm:ss")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime businessStartTime;

    /**
     * 门店营业结束时间（00:00:00-23:59:59）
     */
    @ApiModelProperty("门店营业结束时间（00:00:00-23:59:59）")
    @JsonFormat(pattern = "HH:mm:ss")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime businessEndTime;

    /**
     * 是否启用自动号牌
     * 0=不启用
     * 1=启用
     */
    @ApiModelProperty("是否启用自动号牌：0=不启用，1=启用")
    private Integer enableAutoMark;

    @ApiModelProperty(value = "初始号牌")
    private String initMark;

    @ApiModelProperty(value = "备餐时间")
    private Integer prepTime;

    @ApiModelProperty(value = "快餐出餐语音开关：0关闭 1开启")
    private Integer finishFoodVoiceSwitch;

    /**
     * 打印小票(点菜单、菜品清单)商品顺序
     * asc
     * desc
     *
     * @see PrintItemOrderEnum
     */
    @NotBlank(message = "顺序不能为空")
    private String printItemOrder;

    /**
     * 手动清台：0关闭 1开启
     */
    @ApiModelProperty(value = "手动清台：0关闭 1开启")
    private Integer enableHandleClose;

    /**
     * 同一订单多次使用聚合支付：0关闭 1开启
     */
    @ApiModelProperty(value = "同一订单多次使用聚合支付：0关闭 1开启")
    private Integer enableMultiplePay;

    /**
     * 正餐支持换高价值或低价值的菜 0不支持 1支持
     */
    @ApiModelProperty(value = "正餐支持换高价值或低价值的菜 0不支持 1支持")
    private Integer enableDineInChangeDiffValue;

    /**
     * 快餐餐支持换高价值或低价值的菜 0不支持 1支持
     */
    @ApiModelProperty(value = "快餐餐支持换高价值或低价值的菜 0不支持 1支持")
    private Integer enableFastChangeDiffValue;

    /**
     * 结束号牌
     */
    @ApiModelProperty(value = "结束号牌")
    private String endMark;
}
