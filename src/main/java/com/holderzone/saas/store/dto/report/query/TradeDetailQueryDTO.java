package com.holderzone.saas.store.dto.report.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.common.PageDTO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 结算明细查询DTO
 */
@Data
public class TradeDetailQueryDTO extends PageDTO {

    private static final long serialVersionUID = -1690820536319722992L;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "开始时间不能为空")
    private LocalDate startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "结束时间不能为空")
    private LocalDate endTime;

    /**
     * 品牌guid
     */
    private String brandGuid;

    /**
     * 门店guid
     */
    private List<String> storeGuids;

    /**
     * 券来源 0:美团 1:饿了么
     */
    private Integer couponSource;

    /**
     * 来源 0:美团 1:饿了么
     */
    private Integer takeoutOrderType;

    /**
     * 企业guid
     */
    private String enterpriseGuid;
}
