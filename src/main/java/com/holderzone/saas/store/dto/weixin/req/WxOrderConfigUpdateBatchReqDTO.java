package com.holderzone.saas.store.dto.weixin.req;

import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxOrderConfigUpdateBatchReqDTO
 * @date 2019/02/22 14:46
 * @description 微信门店配置批量修改reqDTO
 * @program holder-saas-store-weixin
 **/

@Data
@ApiModel(value = "微信门店配置批量修改入参")
public class WxOrderConfigUpdateBatchReqDTO {
    @ApiModelProperty("微信点餐门店配置")
    WxOrderConfigDTO wxOrderConfigDTO;
    @ApiModelProperty(value = "storeGuid")
    @NotNull
    private List<String> storeGuidList;
}
