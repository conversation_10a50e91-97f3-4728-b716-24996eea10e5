package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("商品标签")
public class ItemInfoTagDTO {

	@ApiModelProperty(value = "guid")
	private String id;
	@ApiModelProperty(value = "标签名称")
	private String name;
}
