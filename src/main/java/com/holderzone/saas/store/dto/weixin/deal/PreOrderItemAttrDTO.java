package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("预订单商品项属性")
@Accessors(chain=true)
public class PreOrderItemAttrDTO {

	@ApiModelProperty(value = "属性名称")
	private String attrName;

	@ApiModelProperty(value = "属性加价")
	private BigDecimal attrPrice;
}
