package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
public class MtCbPoiStatusDetail implements Serializable {

    private static final long serialVersionUID = -7404986172916493983L;

    /**
     * ERP方门店ID，最大长度100
     */
    private String ePoiId;

    /**
     * 门店状态
     */
    private String poiStatus;

    /**
     * 操作方
     */
    private String operateUser;

    /**
     * 状态变更原因
     */
    private String reason;

    /**
     * 发生时间【单位秒】
     */
    private String operateTime;
}
