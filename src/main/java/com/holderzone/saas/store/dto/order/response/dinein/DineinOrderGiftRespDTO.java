package com.holderzone.saas.store.dto.order.response.dinein;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.mos.secure.ext.annotations.DesensitizationProp;
import com.mos.secure.ext.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class DineinOrderGiftRespDTO {

    @ApiModelProperty(value = "订单的guid")
    private String guid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "预点餐信息")
    private List<DineInItemDTO> reserveItems;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "反结账原单的guid")
    private Long originalOrderGuid;

    @ApiModelProperty(value = "0:无并单，1:主单， 2:子单")
    private Integer upperState;

    @ApiModelProperty(value = "主单单号")
    private String mainOrderNo;

    @ApiModelProperty(value = "主单guid")
    private String mainOrderGuid;

    @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "订单来源：PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7",
            required = true)
    private String deviceTypeName;

    @ApiModelProperty(value = "交易模式：交易模式(0：正餐，1：快餐)", required = true)
    private String tradeModeName;

    @ApiModelProperty(value = "交易模式：交易模式(0：正餐，1：快餐)", required = true)
    private Integer tradeMode;

    @ApiModelProperty(value = "订单来源：PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7",
            required = true)
    private Integer deviceType;

    @ApiModelProperty(value = "结账来源：PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7",
            required = true)
    private String checkoutDeviceTypeName;

    @ApiModelProperty(value = "订单金额（商品总额+附加费）")
    private BigDecimal orderFee;

    /**
     * {@link com.holderzone.saas.store.enums.order.DiscountTypeEnum}
     */
    @ApiModelProperty(value = "优惠金额明细（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
    private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

    @ApiModelProperty(value = "团购券总优惠金额")
    private BigDecimal grouponFee;

    @ApiModelProperty(value = "团购券明细")
    private List<GrouponListRespDTO> grouponListRespDTOS;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "状态(1：未结账， 2：已结账， 3：已退款，4：已作废)")
    private Integer state;

    @ApiModelProperty(value = "状态(1：未结账， 2：已结账， 3：已退款，4：已作废)")
    private String stateName;

    @ApiModelProperty(value = "反结账id")
    private String recoveryId;

    @ApiModelProperty(value = "订单反结账类型1：普通单 2：原单 3：新单 4：退单 ")
    private Integer recoveryType;

    @ApiModelProperty(value = "营业日")
    private LocalDate businessDay;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "结算时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTime;

    @ApiModelProperty(value = "创建人")
    private String createStaffName;

    @ApiModelProperty(value = "结账人")
    private String checkoutStaffName;

    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    @DesensitizationProp(SensitiveTypeEnum.MOBILE_PHONE)
    @ApiModelProperty(value = "会员电话")
    private String memberPhone;

    @ApiModelProperty(value = "会员姓名")
    private String memberName;

    @ApiModelProperty(value = " 会员支付id")
    private String memberConsumptionGuid;
}
