package com.holderzone.saas.store.dto.order.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AutoMarkReqDTO
 * @date 2018/10/23 10:43
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class AutoMarkReqDTO extends BaseDTO {

    @ApiModelProperty(value = "自动号牌（0：否，1：是）")
    private Integer autoMark;

    @ApiModelProperty(value = "初始号牌")
    private String initMark;

    @ApiModelProperty(value = "结束号牌")
    private String endMark;
}
