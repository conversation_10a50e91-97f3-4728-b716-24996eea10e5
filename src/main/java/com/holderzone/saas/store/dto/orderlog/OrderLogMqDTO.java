package com.holderzone.saas.store.dto.orderlog;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderLogMqDTO
 * @date 2018/10/09 15:55
 * @description //TODO
 * @program holder-saas-store-log
 */
@Data
public class OrderLogMqDTO {

    /**
     * @see com.holderzone.saas.store.enums.order.logOperationTypeEnum
     */
    @ApiModelProperty(value = "操作类型（logOperationTypeEnum）")
    @NotNull(message = "操作类型不能为空")
    private Integer operationType;

    @ApiModelProperty(value = "订单号")
    @NotBlank(message = "订单号不能为空")
    private String orderGuid;

    @ApiModelProperty(value = "操作来源DeviceTypeEnum")
    @NotNull(message = "订单来源不能为空")
    private Integer orderSource;

    @ApiModelProperty(value = "交易模式(0：正餐 1：快餐  2：外卖 )")
    @NotNull(message = "交易模式不能为空")
    private Integer tradeMode;

    @ApiModelProperty(value = "操作时间")
    @NotNull(message = "操作时间不能为空")
    private LocalDateTime operationTime;

    @ApiModelProperty(value = "操作人guid")
    @NotBlank(message = "操作人Guid不能为空")
    private String operationStaffGuid;

    @ApiModelProperty(value = "操作人姓名")
    @NotBlank(message = "操作人姓名不能为空")
    private String operationStaffName;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "日志详情（json）")
    @NotBlank(message = "日志内容不能为空")
    private String detail;

    @ApiModelProperty(value = "订单状态")
    private Integer state;

}
