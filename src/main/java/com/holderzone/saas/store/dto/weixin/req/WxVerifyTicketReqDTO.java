package com.holderzone.saas.store.dto.weixin.req;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxVerifyTicketReqDTO
 * @date 2019/02/23 11:40
 * @description 微信推送ticketDTO
 * @program holder-saas-store-weixin
 */
@Data
@XmlAccessorType(value = XmlAccessType.FIELD)
@XmlRootElement(name = "xml")
public class WxVerifyTicketReqDTO {

    @XmlElement(name = "AppId")
    private String appId;

    @XmlElement(name = "CreateTime")
    private Long createTime;

    @XmlElement(name = "InfoType")
    private String infoType;

    @XmlElement(name = "ComponentVerifyTicket")
    private String componentVerifyTicket;
}
