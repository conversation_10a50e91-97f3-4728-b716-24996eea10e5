package com.holderzone.saas.store.dto.order.response.bill;

import com.holderzone.saas.store.dto.order.OrderMultiMemberPayDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ActuallyPayFeeDetailDTO
 * @date 2019/01/28 11:17
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class ActuallyPayFeeDetailDTO implements Serializable {

    private static final long serialVersionUID = -3673553729222578571L;

    @ApiModelProperty(value = "全局唯一主键")
    private String guid;

    @ApiModelProperty(value = "银行流水号")
    private String bankTransactionId;

    @ApiModelProperty(value = "人脸支付和通联银行卡支付安卓加的随机3位数字")
    private String faceCode;

    @ApiModelProperty(value = "交易金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "交易类型，1:正常支付转入，2:会员充值转入，3:预付金转入，4:定金转入，5:预付金使用，6:退单退款，7:会员余额退款，8:预付金退款，9:定金退款，10:正常支付退款")
    private Integer tradingType;

    /**
     * @see com.holderzone.saas.store.enums.PaymentTypeEnum
     */
    @ApiModelProperty(value = "支付方式 1：现金支付，2：聚合支付，3：银行卡支付，4:会员卡支付，5:人脸支付，6:通吃岛支付，7:预定金支付，10：其他支付方式")
    private Integer paymentType;

    @ApiModelProperty(value = "支付方式名")
    private String paymentTypeName;

    @ApiModelProperty(value = "团购是否可退款")
    private Boolean grouponRefundableFlag;

    @ApiModelProperty(value = "是否多次聚合支付")
    private Boolean isMultipleAggPay;

    @ApiModelProperty(value = "交易记录guid (多笔聚合支付关联的 transactionRecord表的guid)")
    private String transactionRecordGuid;

    @ApiModelProperty(value = "退款交易记录guid (多笔聚合支付关联的 transactionRecord表的guid)")
    private String refundTransactionRecordGuid;

    @ApiModelProperty(value = "会员多卡支付信息")
    private List<OrderMultiMemberPayDTO> multiMemberPays;

    @ApiModelProperty(value = "聚合支付对应的支付方式")
    private Integer payPowerId;
}
