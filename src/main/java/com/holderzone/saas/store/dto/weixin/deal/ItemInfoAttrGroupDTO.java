package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("微信商品属性组")
public class ItemInfoAttrGroupDTO {
	@ApiModelProperty(value = "guid")
	private String attrGroupGuid;
	@ApiModelProperty(value = "属性组名称")
	private String name;
	@ApiModelProperty(value = "是否必选")
	@Builder.Default
	private Integer isRequired=0;
	@Builder.Default
	@ApiModelProperty(value = "是否多选:0 否 1 是")
	private Integer isMultiChoice=0;
	@ApiModelProperty(value = "属性详情集合")
	private List<ItemInfoAttrDTO> attrList;
	@ApiModelProperty(value = "是否需要展示价格，1：是，0,否")
	private Integer showPrice;
}
