package com.holderzone.saas.store.dto.orderlog.response;

import com.holderzone.saas.store.dto.orderlog.detail.OrderLogDTO;
import com.holderzone.saas.store.dto.orderlog.detail.PaymentLogDTO;
import com.holderzone.saas.store.dto.orderlog.detail.RefundLogDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderLogDetailRespDTO
 * @date 2018/10/09 14:56  普通订单出参
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class OrderLogDetailRespDTO {
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "订单号")
    private String orderGuid;

    @ApiModelProperty(value = "门店Guid")
    private String StoreGuid;

    @ApiModelProperty(value = "门店名称")
    private String StoreName;

    @ApiModelProperty(value = "日志详情")
    private List<OrderLogDTO> orderLogDTOS;

    @ApiModelProperty(value = "退款详情")
    private List<RefundLogDTO> refundLogDTOS;

    @ApiModelProperty(value = "支付详情")
    private PaymentLogDTO paymentLogDTO;




}
