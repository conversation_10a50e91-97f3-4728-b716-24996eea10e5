package com.holderzone.saas.store.dto.print.type;

import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/22
 * @description 打印分类模版展示
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "打印分类模版展示", description = "打印分类模版展示")
public class PrintTypeTemplateVO implements Serializable {

    private static final long serialVersionUID = -7888674587918870684L;

    @ApiModelProperty(value = "模版Guid")
    private String guid;

    @ApiModelProperty(value = "模版名称")
    private String name;

    @ApiModelProperty(value = "适用门店")
    private String storeNames;

    /**
     * @see InvoiceTypeEnum
     */
    @ApiModelProperty(value = "应用单据-InvoiceTypeEnum")
    private String invoiceType;

    @ApiModelProperty(value = "是否启用 true false")
    private Boolean isEnable;

}
