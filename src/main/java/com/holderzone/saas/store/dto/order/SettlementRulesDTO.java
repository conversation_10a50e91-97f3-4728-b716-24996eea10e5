package com.holderzone.saas.store.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SettlementRulesDTO
 * @date 2019/10/10 16:40
 * @description //TODO
 * @program IdeaProjects
 */
@Data
@NoArgsConstructor
@ApiModel
public class SettlementRulesDTO {

    private Integer id;

    @ApiModelProperty(value = "序号")
    private Integer sequenceNumber;

    /**
     * 优惠方式名称
     */
    @ApiModelProperty(value = "优惠方式名称")
    private String offerName;

    /**
     * 折扣描述
     */
    @ApiModelProperty(value = "折扣描述")
    private String offerDesc;

}