package com.holderzone.saas.store.dto.weixin.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseHsmProductDetail;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Api("优惠券详情")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxMemberInfoVolumeDetailsRespDTO {
//	@ApiModelProperty(value = "会员持卷GUID",hidden = true)
//	private String memberVolumeGuid;
//	@ApiModelProperty(value = "卷模板GUID",hidden = true)
//	private String volumeInfoGuid;
//	@ApiModelProperty("卷名称")
	private String volumeInfoName;
//	@ApiModelProperty(value = "卷简短描述（代金卷50元，无门槛，…）",hidden = true)
//	private String volumeSimpleDesc;
	@ApiModelProperty("开始有效期")
	@JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
	private LocalDateTime volumeStartDate;
	@ApiModelProperty("结束有效期")
	@JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
	private LocalDateTime volumeEndDate;
	@ApiModelProperty(value = "卷码(未生效时，不返回卷码)",hidden = true)
	private String volumeCode;
	@ApiModelProperty("券码条形码")
	private String volumeQrCode;
	@ApiModelProperty("会员拥有卷状态,0未使用，1，已使用，2，未生效，3，已过期，4已作废,5不可使用（仅点餐）")
	private Integer volumeState;
//	@ApiModelProperty("卷模板的状态,0未过期(发送中),1已过期(保留状态),2停止发放,3作废")
//	private Integer volumeInfoState;
	@ApiModelProperty("卷优惠金额")
	private BigDecimal volumeMoney;
	@ApiModelProperty("卷类型，0代金卷,1折扣卷,2兑换卷,3商品券")
	private Integer volumeType;
	@ApiModelProperty(value = "使用门槛满,0无限制，1有限制",hidden = true)
	private Integer useThreshold;
	@ApiModelProperty(value = "满多少可用",hidden = true)
	private BigDecimal useThresholdFull;
	@ApiModelProperty(value = "每次可使用数量",hidden = true)
	private String mayUseNum;
	@ApiModelProperty(value = "券简短说明")
	private List<String> volumeDescList;
	@ApiModelProperty("优惠券使用说明")
	private String description;
	@ApiModelProperty("是否单独使用0:无限制，1:仅限原价购买")
	private Integer isUseAlone;
	@ApiModelProperty("优惠券适用门店及商品列表")
	private List<WxApplicableProductStores> wxApplicableProductStores;

	@ApiModelProperty("门店名称")
	private String storeName;
	@ApiModelProperty("是否支持全部菜品 0全部 1部分")
	private int supportAll;
	@ApiModelProperty("优惠券支持门店下的菜品详细")
	private List<ResponseHsmProductDetail> productDetailRespDTOS;
}
