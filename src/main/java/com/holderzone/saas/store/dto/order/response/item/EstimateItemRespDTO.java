package com.holderzone.saas.store.dto.order.response.item;

import com.holderzone.saas.store.dto.order.response.OrderTableBillDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateItemRespDTO
 * @date 2019/01/28 17:32
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class EstimateItemRespDTO {

    @ApiModelProperty(value = "结果")
    private Boolean result;

    @ApiModelProperty(value = "是否估清失败")
    private Boolean estimate;

    @ApiModelProperty(value = "失败原因")
    private String errorMsg;

    @ApiModelProperty(value = "返回估清商品提示信息")
    private String estimateInfo;

    @ApiModelProperty(value = "已估清商品规格id")
    private List<String> estimateSkuGuids;

    @ApiModelProperty(value = "快餐返回orderGuid")
    private String orderGuid;

    @ApiModelProperty(value = "快餐小程序下单计算后")
    private DineinOrderDetailRespDTO calculate;
}
