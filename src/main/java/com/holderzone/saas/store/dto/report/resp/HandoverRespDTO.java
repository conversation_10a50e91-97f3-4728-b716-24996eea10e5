package com.holderzone.saas.store.dto.report.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverRespDTO
 * @date 2018/10/08 11:45
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class HandoverRespDTO implements Serializable {

    @ApiModelProperty(value = "交接班记录guid")
    private String handoverRecordGuid;

    @ApiModelProperty(value = "门店名")
    private String storeName;

    @ApiModelProperty(value = "系统编号")
    private String terminalId;

    @ApiModelProperty(value = "开班人guid")
    private String createUserGuid;

    @ApiModelProperty(value = "开班人姓名")
    private String createUserName;

    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "接班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime handoverTime;

    @ApiModelProperty(value = "销售总收入")
    private BigDecimal paymentMoney;

    @ApiModelProperty(value = "销售订单完成数")
    private Integer paymentCount;

    @ApiModelProperty(value = "储值总收入")
    private BigDecimal memberChargeMoney;

    @ApiModelProperty(value = "储值订单完成数")
    private Integer chargedCount;

    @ApiModelProperty(value = "当班营业额")
    private BigDecimal businessIncoming;

    @ApiModelProperty(value = "交接班状态")
    private String state;

    @ApiModelProperty(value = "当班时长")
    private String duration;

    @ApiModelProperty(value = "开班人姓名/账号")
    private String createNameAndGuid;

    public String getCreateNameAndGuid() {
        return this.createUserName+"/"+this.createUserGuid;
    }

    public String getDuration() {
        if (null == this.handoverTime || null == this.createTime) {
            return null;
        }
        Duration between = Duration.between(this.createTime, this.handoverTime);
        long betweenTimes = between.toMillis();
        betweenTimes = betweenTimes / 1000;
        BigDecimal hour = BigDecimal.valueOf(betweenTimes).divide(BigDecimal.valueOf(3600), 0, RoundingMode.DOWN);
        betweenTimes = betweenTimes % 3600;
        BigDecimal minute = BigDecimal.valueOf(betweenTimes).divide(BigDecimal.valueOf(60), 0, RoundingMode.DOWN);
        BigDecimal seconds = BigDecimal.valueOf(betweenTimes % 60);
        return getStr(":", hour, minute, seconds);
    }

    private String getStr(String regex, Object... parms) {
        StringBuilder format = new StringBuilder("%s");
        for (int i = 0; i < parms.length - 1; ++i) {
            format.append(regex).append("%s");
        }
        return String.format(format.toString(), parms);
    }

}
