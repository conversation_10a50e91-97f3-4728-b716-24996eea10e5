package com.holderzone.saas.store.dto.erp;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/09 上午 10:28
 * @description
 */
@ApiModel("盘点单查询实体")
public class CheckoutDocumentQueryDTO extends BasePageDTO {

    @ApiModelProperty("仓库guid")
    private List<String> warehouseGuidList;

    @ApiModelProperty("盘点开始日期")
    private Date startDate;

    @ApiModelProperty("盘点结束日期")
    private Date endDate;

    @ApiModelProperty("盘点类型")
    private Integer type;

    @ApiModelProperty("搜索框内容")
    private String searchContent;

    public List<String> getWarehouseGuidList() {
        return warehouseGuidList;
    }

    public void setWarehouseGuidList(List<String> warehouseGuidList) {
        this.warehouseGuidList = warehouseGuidList;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSearchContent() {
        return searchContent;
    }

    public void setSearchContent(String searchContent) {
        this.searchContent = searchContent;
    }
}
