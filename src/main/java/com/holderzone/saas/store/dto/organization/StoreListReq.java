package com.holderzone.saas.store.dto.organization;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className QueryBrandDTO
 * @date 19-1-3 上午10:38
 * @description
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("门店列表查询")
public class StoreListReq {

    @ApiModelProperty(value = "门店guid集合")
    private List<String> storeGuidList;

    @ApiModelProperty(value = "省或市或区编码集合")
    private List<String> regionCodeList;

    @ApiModelProperty(value = "品牌guid集合")
    private List<String> brandGuidList;

}
