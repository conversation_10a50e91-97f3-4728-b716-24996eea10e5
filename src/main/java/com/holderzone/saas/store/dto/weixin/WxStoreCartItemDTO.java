package com.holderzone.saas.store.dto.weixin;

import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @describle 商品项
 * @athor zhouxinwen
 * @dateTime 2019/3/1
 */
@Data
@ApiModel("商品项")
public class WxStoreCartItemDTO {
    @ApiModelProperty(value = "商品guid", required = true)
    private String itemGuid;
    @ApiModelProperty(value = "商品名称", required = true)
    private String itemName;
    @ApiModelProperty(value = "商品编号", required = true)
    private String code;

    @ApiModelProperty(value = "商品类型(1.套餐主项，2.规格，3.称重，4.单品 )", required = true)
    private Integer itemType;

    @ApiModelProperty(value = "商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂 ，8.已上菜(已划菜) )")
    private Integer itemState;
    @ApiModelProperty(value = "商品分类guid", required = true)
    private String itemTypeGuid;
    @ApiModelProperty(value = "商品分类名称", required = true)
    private String itemTypeName;
    @ApiModelProperty(value = "规格guid", required = true)
    private String skuGuid;

    @ApiModelProperty(value = "规格名称", required = true)
    private String skuName;
    @ApiModelProperty(value = "sku价格", required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "商品价格小计", required = true)
    private BigDecimal itemPrice;

    @ApiModelProperty(value = "属性总价")
    private BigDecimal singleItemAttrTotal;

    @ApiModelProperty(value = "数量", required = true)
    private BigDecimal currentCount;

    @ApiModelProperty(value = "赠送数量")
    private BigDecimal freeCount;

    @ApiModelProperty(value = "退货数量")
    private BigDecimal returnCount;

    @ApiModelProperty(value = "计数单位", required = true)
    private String unit;

    @ApiModelProperty(value = "是否已经下单(0:否、1：是)", required = true)
    private Integer isPay;

    @ApiModelProperty(value = "是否参与会员权益折扣", required = true)
    private Integer isMemberDiscount;

    @ApiModelProperty(value = "是否参与整单折扣", required = true)
    private Integer isWholeDiscount;

    @ApiModelProperty(value = "商品备注")
    private String remark;

    @ApiModelProperty(value = "套餐分组")
    private List<PackageSubgroupDTO> packageSubgroupDTOS;

    @ApiModelProperty(value = "赠送商品")
    private List<FreeItemDTO> freeItemDTOS;

    @ApiModelProperty(value = "商品属性")
    private List<ItemAttrDTO> itemAttrDTOS;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        WxStoreCartItemDTO that = (WxStoreCartItemDTO) o;
        return Objects.equals(itemGuid, that.itemGuid) &&
                Objects.equals(skuGuid, that.skuGuid) &&
                Objects.equals(remark, that.remark);
    }

    @Override
    public int hashCode() {

        return Objects.hash(super.hashCode(), itemGuid, skuGuid, itemAttrDTOS);
    }
}
