package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class ShopCartItemAddRespDTO {

	@ApiModelProperty(value = "0：表示成功，1：失败")
	private Integer code = 0;

	@ApiModelProperty(value = "错误信息，没有表示成功")
	private String errorMsg = StringUtils.EMPTY;

	@ApiModelProperty(value = "商品最大数量")
	private BigDecimal itemMaxNum;

	public static ShopCartItemAddRespDTO addFailed(String errorMsg) {
		return new ShopCartItemAddRespDTO().setCode(1).setErrorMsg(errorMsg);
	}
}
