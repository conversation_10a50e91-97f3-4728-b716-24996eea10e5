package com.holderzone.saas.store.dto.report.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
public class ReportQueryVO implements Serializable {

    private static final long serialVersionUID = 2942494682295252169L;

    /**
     * 当前页数
     */
    @ApiModelProperty(value = "当前页数")
    private Integer currentPage = 1;

    /**
     * 每页显示条数
     */
    @ApiModelProperty(value = "每页显示条数")
    private Integer pageSize = 10;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "开始时间不能为空")
    private LocalDate startTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "结束时间不能为空")
    private LocalDate endTime;

    private String enterpriseGuid;

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty("门店集合，品牌查询需传品牌下门店集合")
    private List<String> storeGuids;

    @ApiModelProperty(value = "商品类型(1.套餐主项，2.规格，3.称重，4.单品，5.团餐主项 ) 不传：全部")
    private Integer goodsType;

    @ApiModelProperty(value = "商品分类 根据后端返回而定")
    private String goodsCategories;

    @ApiModelProperty(value = "就餐类型 0：正餐 1：快餐 2：外卖 不传：全部")
    private Integer cateringType;

    private String itemName;

    /**
     * 操作节点 1反结账 2已结账 3未结账 4未出餐
     */
    @ApiModelProperty(value = "操作节点 1反结账 2已结账 3未结账 4未出餐")
    public Integer operatorNode;

    @ApiModelProperty(value = "挂账还款状态 0:未还款 1：还款")
    private Integer repaymentStatus;

    @ApiModelProperty(value = "挂账单位guid")
    private String debtUnitGuid;
}
