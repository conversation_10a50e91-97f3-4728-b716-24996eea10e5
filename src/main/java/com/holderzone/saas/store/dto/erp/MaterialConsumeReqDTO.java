package com.holderzone.saas.store.dto.erp;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/11/14 20:10
 */
@Data
public class MaterialConsumeReqDTO extends BasePageDTO {

    @ApiModelProperty("仓库GUID")
    private String warehouseGuid;

    @ApiModelProperty("开始日期")
    private String startDate;

    @ApiModelProperty("结束日期")
    private String endDate;

    @ApiModelProperty("分类Guid")
    private String classifyGuid;

    @ApiModelProperty("物料名称或编码")
    private String nameOrCode;

    @ApiModelProperty("单据类型")
    private Integer type;
}
