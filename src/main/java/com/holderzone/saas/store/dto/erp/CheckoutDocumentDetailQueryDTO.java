package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/08 下午 14:39
 * @description
 */
@ApiModel(value = "查询物料信息")
@Data
public class CheckoutDocumentDetailQueryDTO {

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "仓库guid")
    private String warehouseGuid;

    @ApiModelProperty(value = "原料guid列表")
    private List<String> materialGuidList;

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public List<String> getMaterialGuidList() {
        return materialGuidList;
    }

    public void setMaterialGuidList(List<String> materialGuidList) {
        this.materialGuidList = materialGuidList;
    }
}
