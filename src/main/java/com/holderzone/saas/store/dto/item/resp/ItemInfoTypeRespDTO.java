package com.holderzone.saas.store.dto.item.resp;

import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.weixin.deal.MenuInfoAllDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 商品详情及分类信息返回实体
 * @date 2021/4/14 14:24
 */
@Data
@ApiModel(value = "商品详情及分类信息返回实体")
public class ItemInfoTypeRespDTO {

//    @ApiModelProperty(value = "分类信息返回集合")
//    private List<TypeWebRespDTO> typeList;


    /**
     * typeGUID
     */
    @ApiModelProperty(value = "分类GUID（唯一标识）")
    private String typeGuid;

    /**
     * 菜单类别名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 品牌GUID
     */
    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    /**
     * 门店GUID
     */
    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 是否启用（0：否，1：是）
     */
    @ApiModelProperty(value = "是否启用（0：否，1：是）")
    private Integer isEnable;

    /**
     * icon地址
     */
    @ApiModelProperty(value = "icon地址")
    private String iconUrl;

    /**
     * 关联的商品数量
     */
    @ApiModelProperty(value = "关联的商品数量")
    private Integer itemNum;

    /**
     * 上架商品数量
     */
    @ApiModelProperty(value = "上架商品数量")
    private Integer rackItemNum;

    /**
     * 下架商品数量
     */
    @ApiModelProperty(value = "下架商品数量")
    private Integer unRackItemNum;

    @ApiModelProperty(value = "type类型：（0：门店自己创建的type，1：品牌自己创建的type,2:被推送过来的type）")
    private Integer typeFrom;

    /**
     * 菜谱方案GUID
     */
    @ApiModelProperty(value = "菜谱方案GUID")
    private String pricePlanGuid;

    /**
     * 菜谱分类图片类型(0小图,1竖图,2整屏)
     */
    @ApiModelProperty(value = "菜谱分类图片类型(0小图,1竖图,2整屏)")
    private Integer menuClassifyPictureType;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "是否必点")
    private Integer isMustPoint;

}
