package com.holderzone.saas.store.dto.weixin.req;

import com.holderzone.saas.store.dto.weixin.deal.ItemInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 删除购物车商品请求入参
 * @date 2021/6/22 16:16
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("删除购物车商品请求入参")
public class ClearCartItemReqDTO {

    @ApiModelProperty("删除商品实体列表，只能由前端传")
    private List<ItemInfoDTO> infoDTOList;
}
