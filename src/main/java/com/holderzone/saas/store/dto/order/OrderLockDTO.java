package com.holderzone.saas.store.dto.order;

import com.holderzone.saas.store.dto.order.common.BaseOrderDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderLockDto
 * @date 2019/09/12 11:06
 * @description //TODO
 * @program IdeaProjects
 */
@Data
@NoArgsConstructor
public class OrderLockDTO extends BaseOrderDTO {

    @ApiModelProperty(value = "锁的超时时间")
    private Long timeout;

}