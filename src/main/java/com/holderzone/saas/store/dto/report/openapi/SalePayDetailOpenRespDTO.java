package com.holderzone.saas.store.dto.report.openapi;

import lombok.Data;

import java.io.Serializable;

/**
 * 销售支付明细响应
 */
@Data
public class SalePayDetailOpenRespDTO implements Serializable {

    private static final long serialVersionUID = -1025167689243809355L;

    /**
     * 序号
     */
    private Integer index;

    /**
     * 支付方式
     */
    private String payWayCode;

    /**
     * 支付金额
     */
    private Long amount;

    /**
     * 支付流水号, 收单系统唯一性标识
     */
    private String bankTransactionId;
}
