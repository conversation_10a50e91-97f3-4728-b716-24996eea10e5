package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class TakeoutOrderForceIndexDTO {

    @ApiModelProperty(value = "门店标识")
    private String storeGuid;

    @ApiModelProperty(value = "门店标识")
    private List<String> businessDays;
}
