package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("微信品牌我的订单")
@AllArgsConstructor
@NoArgsConstructor
public class WxBrandUserOrderDTO {

	private List<WxBrandUserOrderTitleGroup> wxBrandUserOrderTitleGroupList;

	private List<WxBrandUserOrderItemDTO> wxBrandUserOrderItemDTOList;
}
