package com.holderzone.saas.store.dto.item.resp.price;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 商品的分类
 * @date 2021/11/3 18:22
 * @className: PlanPriceAllTypeRespDTO
 */
@Data
@ApiModel(value = "所有为已启用、即将启用、暂不启用菜谱的所有商品")
public class PlanPriceAllTypeRespDTO {

    @ApiModelProperty(value = "分类guid")
    private String typeGuid;

    @ApiModelProperty(value = "分类名称")
    private String typeName;

    @ApiModelProperty(value = "分类下商品列表")
    private List<PlanPriceAllItemRespDTO> itemList;
}
