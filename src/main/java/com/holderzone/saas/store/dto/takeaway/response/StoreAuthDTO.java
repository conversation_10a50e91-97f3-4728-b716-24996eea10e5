package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreAuthDTO
 * @date 2018/09/25 9:07
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class StoreAuthDTO implements Serializable {

    @ApiModelProperty(value = "外卖类型：0=美团，1=饿了么")
    private Integer takeoutType;

    @ApiModelProperty(value = "平台名")
    private String platformName;

    @ApiModelProperty(value = "门店编号")
    private String storeNumber;

    @NotBlank(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "外卖门店id")
    private String shopId;

    @ApiModelProperty(value = "外卖门店名称")
    private String shopName;

    @ApiModelProperty(value = "绑定状态(0：未绑定，1:绑定，2：全部)")
    private Integer bindingStatus;

    @ApiModelProperty(value = "配送类型(0：自配送，1:饿了么/美团一城飞客 2:饿了么/美团专送，3：饿了么/美团众包)")
    private Integer deliveryType;
}
