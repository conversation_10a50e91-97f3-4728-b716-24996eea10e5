package com.holderzone.saas.store.dto.member.pay;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员快速收款
 */
@Data
public class MemberQuickPayDTO {

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 订单guid
     */
    private String orderGuid;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 手机号
     */
    private String phoneNum;

    /**
     * 收款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 退款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime refundTime;
}
