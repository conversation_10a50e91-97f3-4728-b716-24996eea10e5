package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 商品bom配置提交入参
 *
 * <AUTHOR>
 * @date 2019/05/16 17:19
 */
@ApiModel(value = "商品bom配置提交入参")
public class GoodsBomConfigDTO {

    @NotEmpty(message = "商品GUID不能为空")
    @ApiModelProperty("商品GUID不能为空")
    private String goodsGuid;

    @NotEmpty(message = "商品sku不能为空")
    @ApiModelProperty("商品sku不能为空")
    private String goodsSku;

    @ApiModelProperty("商品列表")
    private List<GoodsBomDTO> bomList;

    public String getGoodsGuid() {
        return goodsGuid;
    }

    public void setGoodsGuid(String goodsGuid) {
        this.goodsGuid = goodsGuid;
    }

    public String getGoodsSku() {
        return goodsSku;
    }

    public void setGoodsSku(String goodsSku) {
        this.goodsSku = goodsSku;
    }

    public List<GoodsBomDTO> getBomList() {
        return bomList;
    }

    public void setBomList(List<GoodsBomDTO> bomList) {
        this.bomList = bomList;
    }
}
