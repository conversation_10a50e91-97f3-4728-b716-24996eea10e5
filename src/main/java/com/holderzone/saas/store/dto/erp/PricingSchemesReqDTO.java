package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @className PricingSchemesReqDTO
 * @date 2019-04-27 10:31:52
 * @description
 * @program holder-saas-store-dto
 */
public class PricingSchemesReqDTO {

    @ApiModelProperty(value = "主键guid")
    private String guid;
    @ApiModelProperty(value = "供应商guid")
    private String suppliersGuid;
    @ApiModelProperty(value = "物料guid")
    private String materialGuid;
    @ApiModelProperty(value = "协议单价")
    private BigDecimal dealPrice;
    @ApiModelProperty(value = "单位")
    private String dealUnit;
    @ApiModelProperty(value = "状态(0:停止供应，1:正常供应)")
    private Integer enabled;

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getSuppliersGuid() {
        return suppliersGuid;
    }

    public void setSuppliersGuid(String suppliersGuid) {
        this.suppliersGuid = suppliersGuid;
    }

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public BigDecimal getDealPrice() {
        return dealPrice;
    }

    public void setDealPrice(BigDecimal dealPrice) {
        this.dealPrice = dealPrice;
    }

    public String getDealUnit() {
        return dealUnit;
    }

    public void setDealUnit(String dealUnit) {
        this.dealUnit = dealUnit;
    }
}
