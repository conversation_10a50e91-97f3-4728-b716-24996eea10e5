package com.holderzone.saas.store.dto.item.req;


import com.holderzone.saas.store.dto.item.resp.AttrGroupWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubgroupWebRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class SingleShoppingCart {

    @ApiModelProperty(value = "商品唯一标识")
    private String itemGuid;

    @ApiModelProperty(value = "是否开启库存(0：否，1：是)")
    private Integer isOpenStock;

    @ApiModelProperty(value = "当日库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "总库存")
    private BigDecimal totalStock;

    @ApiModelProperty(value = "外卖打包费（冗余小程序端字段）")
    private BigDecimal takeawayPackageFee;

    @ApiModelProperty(value = "商城打包费（冗余小程序端字段）")
    private BigDecimal mallPackageFee;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "拼音简码")
    private String pinyin;

    @ApiModelProperty(value = "别名")
    private String nameAbbr;

    @ApiModelProperty(value = "规格guid")
    private String skuGuid;

    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty("是否次日置满  1：否  2：是")
    private Integer isItReset;

    @ApiModelProperty("外卖价格")
    @DecimalMin(value = "0")
    @DecimalMax(value = "99999.99")
    private BigDecimal takeawayPrice;

    @ApiModelProperty("外卖会员价格")
    @DecimalMin(value = "0")
    @DecimalMax(value = "99999.99")
    private BigDecimal takeawayMemberPrice;
    @ApiModelProperty(value = "售卖价格")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "成本价格")
    @DecimalMin(value = "0.01")
    @DecimalMax(value = "99999.99")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "会员价格")
    @DecimalMin(value = "0.01")
    @DecimalMax(value = "99999.99")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "划线价格")
    @DecimalMin(value = "0.01")
    @DecimalMax(value = "99999.99")
    private BigDecimal virtualPrice;

    @ApiModelProperty(value = "是否上架")
    private Integer isRack;

    @ApiModelProperty(value = "是否上架一体机（0：否，1：是）")
    private Integer isJoinAio;

    @ApiModelProperty(value = "是否上架POS机（0：否，1：是）")
    private Integer isJoinPos;

    @ApiModelProperty(value = "是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;

    @ApiModelProperty(value = "计数单位")
    private String unit;

    @ApiModelProperty(value = "计数单位编码")
    private String unitCode;

    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "父商品GUID")
    private String parentGuid;

    @ApiModelProperty(value = "商品类型:1.套餐（不称重，无规格），2.规格商品，3.称重商品，4.单品 ，5:。团餐")
    private Integer itemType;

    @ApiModelProperty(value = "是否售罄:0 否 1 是")
    private Integer isSoldOut;

    @ApiModelProperty(value = "属性组状态:0：无属性; 1:有属性; 2:有必选属性组")
    private Integer hasAttr;

    @ApiModelProperty(value = "排序号")
    private Integer sort;

    @ApiModelProperty(value = "商品描述")
    private String description;

    @ApiModelProperty(value = "商品主图路径")
    private String pictureUrl;

    @ApiModelProperty(value = "是否热销：对应快餐用的字段为为：isRecommend：0：否，1：是")
    private Integer isBestseller;

    @ApiModelProperty(value = "是否新品：快餐无对应字段：0：否，1：是")
    private Integer isNew;

    @ApiModelProperty(value = "是否是招牌：0：否，1：是")
    private Integer isSign;

    @ApiModelProperty(value = "是否是固定套餐，0：否，1：是")
    private Integer isFixPkg;

    @ApiModelProperty(value = "是否参与小程序商城（冗余小程序端字段）")
    private Integer isJoinMiniAppMall;

    @ApiModelProperty(value = "是否参与小程序外卖（冗余小程序端字段）")
    private Integer isJoinMiniAppTakeaway;

    @ApiModelProperty(value = " 是否支持堂食（冗余小程序端字段）")
    private Integer isJoinStore;

    @ApiModelProperty(value = "商品编码（冗余小程序端字段）")
    private String code;

    @ApiModelProperty(value = "审核状态（冗余小程序端字段）")
    private Integer auditStatus;

    @ApiModelProperty(value = "图文详情（冗余小程序端字段）")
    private String remarkDetail;

    @ApiModelProperty(value = "视频url json数组（冗余小程序端字段）")
    private String videoUrls;

    @ApiModelProperty(value = "好评次数（冗余小程序端字段）")
    private Integer upCount;

    @ApiModelProperty(value = "差评次数（冗余小程序端字段）")
    private Integer downCount;

    @ApiModelProperty(value = "一级属性集合")
    private List<AttrGroupWebRespDTO> attrGroupList;

    @ApiModelProperty(value = "分组集合")
    private List<SubgroupWebRespDTO> subgroupList;

}
