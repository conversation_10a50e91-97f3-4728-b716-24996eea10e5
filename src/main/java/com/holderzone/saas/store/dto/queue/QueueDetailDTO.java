package com.holderzone.saas.store.dto.queue;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueDetailDTO
 * @date 2019/05/10 13:57
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
public class QueueDetailDTO {
    private String guid;
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;
    @ApiModelProperty(value = "门店名")
    private String storeName;
    @NotEmpty
    @ApiModelProperty(value = "队列名称",required = true)
    private String name;
    @NotEmpty
    @ApiModelProperty(value = "队列编码",required = true)
    private String code;
    @Min(1)
    @Max(127)
    @ApiModelProperty(value = "最大人数",required = true)
    private Byte min;
    @Min(1)
    @Max(127)
    @ApiModelProperty(value = "最小人数",required = true)
    private Byte max;
    private Integer index;
    private List<TableDTO> tables = Collections.emptyList();
}