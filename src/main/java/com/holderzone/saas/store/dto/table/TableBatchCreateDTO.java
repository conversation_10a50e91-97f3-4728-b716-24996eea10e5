package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.enums.table.BatchTableEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableBatchCreateDTO
 * @date 2019/01/03 9:37
 * @description 批量创建桌台dto
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class TableBatchCreateDTO extends BaseDTO {

    @NotBlank
    @ApiModelProperty("门店guid")
    private String storeGuid;

    @NotBlank
    @ApiModelProperty("门店name")
    private String storeName;

    @NotNull
    @ApiModelProperty("区域guid")
    private String areaGuid;

    @NotBlank
    @ApiModelProperty("区域name")
    private String areaName;

    @ApiModelProperty("固定首字母")
    private String fixedFirstWord;

    @ApiModelProperty("生成类型")
    private BatchTableEnum batchTableEnum;

    @ApiModelProperty("开始数字")
    @NotNull
    private Integer startNum;

    @ApiModelProperty("数量")
    @NotNull
    private Integer total;

    @ApiModelProperty("每桌人数")
    @NotNull
    private Integer seatsPerTable;

    @ApiModelProperty("标签列表")
    @Size(max = 5)
    private List<String> tagList;
}
