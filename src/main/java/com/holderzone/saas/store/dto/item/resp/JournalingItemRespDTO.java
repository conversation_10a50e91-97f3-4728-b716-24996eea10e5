package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className JournalingTypeRespDTO
 * @date 2019/09/03 上午11:30
 * @description // 报表大屏商品支持
 * @program holder-saas-store-dto
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "报表大屏商品支持")
public class JournalingItemRespDTO implements Serializable {

    @ApiModelProperty(value = "商品/分类guid")
    private  String guid;

    @ApiModelProperty(value = "名称")
    private  String name;


    @ApiModelProperty(value = "子分类guid集合")
    List<String> subGuids;


    @ApiModelProperty(value = "来源  0:门店自建  2：品牌推送")
    private  Integer itemFrom;
}
