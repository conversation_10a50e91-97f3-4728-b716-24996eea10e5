package com.holderzone.saas.store.dto.item.resp;

import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import lombok.Data;

@Data
public class TypeGroupKeyRes {

    private String guid;

    private String name;

    public static TypeGroupKeyRes of(PrintItemRecord printItemRecord) {
        TypeGroupKeyRes typeGroupKey = new TypeGroupKeyRes();
        typeGroupKey.setGuid(printItemRecord.getItemTypeGuid());
        typeGroupKey.setName(printItemRecord.getItemTypeName());
        return typeGroupKey;
    }
}
