package com.holderzone.saas.store.dto.item.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTMenuReqDTO
 * @date 2019/05/23 14:27
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ItemTMenuReqDTO {

    /**
     * 业务主键
     */
    private String guid;

    /**
     * 外键 商品模板guid
     */
    private String templateGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否逻辑删除 0：否  1：是
     */
    @Builder.Default
    private Integer isDelete = 0;

}
