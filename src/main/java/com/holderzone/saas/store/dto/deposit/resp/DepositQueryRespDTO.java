package com.holderzone.saas.store.dto.deposit.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class DepositQueryRespDTO implements Serializable {

    private static final long serialVersionUID = -2773042054434843563L;

    @ApiModelProperty(value = "寄存guid")
    private String guid;

    @ApiModelProperty(value = "寄存单号")
    private String depositOrderId;

    @ApiModelProperty(value = "头像")
    private String headPortrait;

    @ApiModelProperty(value = "姓名")
    private String customerName;

    @ApiModelProperty(value = "联系方式")
    private String phoneNum;

    @ApiModelProperty(value = "寄存数量")
    private int depositNum;

    @ApiModelProperty(value = "剩余数量")
    private int residueNum;

//    @ApiModelProperty(value = "商品集合")
//    private List<GoodsRespDTO> goodsList;

    @ApiModelProperty(value = "过期时间")
    private String expireTime;

    @ApiModelProperty(value = "存入时间")
    private String saveTime;


}
