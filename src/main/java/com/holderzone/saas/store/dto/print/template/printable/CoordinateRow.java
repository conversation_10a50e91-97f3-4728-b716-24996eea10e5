package com.holderzone.saas.store.dto.print.template.printable;

import com.holderzone.saas.store.dto.print.template.convertable.Text;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 指定X坐标、Y坐标的文本行
 *
 * <AUTHOR>
 * @date 18-12-15 14:30
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class CoordinateRow implements Serializable {

    private static final long serialVersionUID = -456023279192386658L;

    @ApiModelProperty(value = "该行的坐标文字列表")
    private List<CoordinateText> coordinateTextList;

    public CoordinateRow addCoordinateText(String text, int xc, int yc) {
        return this.addCoordinateText(new CoordinateText(new Text(text), xc, yc));
    }

    public CoordinateRow addCoordinateText(CoordinateText coordinateText) {
        if (null == this.coordinateTextList) {
            this.coordinateTextList = new ArrayList<>();
        }
        this.coordinateTextList.add(coordinateText);
        return this;
    }

    @Data
    @NoArgsConstructor
    public static class CoordinateText {

        @ApiModelProperty(value = "打印文本")
        private Text text;

        @ApiModelProperty(value = "X坐标")
        private int xc;

        @ApiModelProperty(value = "Y坐标")
        private int yc;

        @ApiModelProperty(value = "对齐方式")
        private Text.Align align = Text.Align.Left;

        public CoordinateText(Text text, int xc, int yc) {
            this.text = text;
            this.xc = xc;
            this.yc = yc;
        }

        public CoordinateText(Text text, int xc, int yc, Text.Align align) {
            this.text = text;
            this.xc = xc;
            this.yc = yc;
            this.align = align;
        }
    }
}