package com.holderzone.saas.store.dto.member.request;

import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UpdateMemberReqDTO
 * @date 2018/09/17 14:51
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class UpdateMemberReqDTO extends BaseMemberDTO{

    @ApiModelProperty(value = "名字")
    @NotNull(message = "名字不能为空")
    private String name;

    @ApiModelProperty(value = "性别 0：女，1:男，2：保密 必传",required = true)
    @NotNull(message = "性别不能为空")
    private Byte sex;

    @ApiModelProperty(value = "生日,必传",required = true)
    @NotNull(message = "生日不能为空")
    private LocalDate birthday;

}
