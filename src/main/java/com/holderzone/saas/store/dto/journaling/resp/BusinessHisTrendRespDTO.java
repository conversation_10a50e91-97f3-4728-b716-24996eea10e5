package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessHisTrendRespDTO
 * @date 2019/05/29 16:33
 * @description 报表-营业概况-历史趋势
 * @program holder-saas-store-dto
 */
@Data
@ApiModel("报表-经营概况-历史趋势")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessHisTrendRespDTO implements Serializable {
    private static final long serialVersionUID = 6792882265451674443L;

    @ApiModelProperty(value = "日期数据")
    private List<String> dateData;
    @ApiModelProperty(value = "对应日期的营业额数据")
    private List<BigDecimal> businessFeeTrend;
    @ApiModelProperty(value = "对应日期的订单数")
    private List<Integer> orderCountTrend;
    @ApiModelProperty(value = "对应日期的客流量")
    private List<Integer> guestCountTrend;
    @ApiModelProperty(value = "对应日期的客单价")
    private List<BigDecimal> averagePrice;
}
