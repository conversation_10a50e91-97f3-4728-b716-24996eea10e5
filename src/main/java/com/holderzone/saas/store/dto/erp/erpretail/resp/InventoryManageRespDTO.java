package com.holderzone.saas.store.dto.erp.erpretail.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("盘点管理返回实体")
public class InventoryManageRespDTO {

    @ApiModelProperty("单据编号")
    private String invoiceNo;

    @ApiModelProperty("票据类型")
    private String invoiceName;

    @ApiModelProperty("盘点日期")
    private String inventoryDate;

    @ApiModelProperty("盘点人员")
    private String operator;

    @ApiModelProperty("单据状态")
    private int status;

}
