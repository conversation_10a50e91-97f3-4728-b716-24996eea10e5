package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ItemSkuListReqDTO extends PageDTO {

    @ApiModelProperty(value = "门店guid")
    @NotBlank(message = "门店Guid不能为空")
    private String storeGuid;

    @ApiModelProperty("商品guid")
    private String itemGuid;

    @ApiModelProperty("类型guid")
    private String typeGuid;

    @ApiModelProperty(value = "是否开启库存（0：否，1：是）")
    private Integer isOpenStock;

    @ApiModelProperty("是否参与小程序商城（0：否，1：是） ")
    private Integer isJoinMiniAppMall;

    @ApiModelProperty("是否参与小程序外卖（0：否，1：是） ")
    private Integer isJoinMiniAppTakeaway;

    @ApiModelProperty("是否支持堂食（0：否，1：是） ")
    private Integer isJoinStore;
}
