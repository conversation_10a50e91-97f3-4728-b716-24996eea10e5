package com.holderzone.saas.store.dto.weixin.deal;

import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseClientMarketActivity;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "活动选择")
public class MarketActivityDTO implements Serializable {

    private static final long serialVersionUID = 2405456291437993010L;

    @ApiModelProperty("营销活动guid")
    private String activityGuid;

    @ApiModelProperty("营销活动名称")
    private String activityName;

    @ApiModelProperty("菜品信息")
    private List<RequestDishInfo> dishInfoDTOList;

    @ApiModelProperty("营销活动总优惠金额")
    private BigDecimal discountMoney;

    @ApiModelProperty("营销活动列表")
    private List<ResponseClientMarketActivity> activitiesList;

}
