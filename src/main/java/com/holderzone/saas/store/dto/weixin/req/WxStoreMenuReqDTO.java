package com.holderzone.saas.store.dto.weixin.req;

import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel(value = "微信菜单入参")
@NoArgsConstructor
@AllArgsConstructor
public class WxStoreMenuReqDTO {

	private String enterpriseGuid;

	private String storeGuid;

	@ApiModelProperty(value = "1：扫码进入 ")
	private Integer wxPermission;

	private WxStoreConsumerDTO wxStoreConsumerDTO;
}
