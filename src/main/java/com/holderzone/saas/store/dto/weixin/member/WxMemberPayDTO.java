package com.holderzone.saas.store.dto.weixin.member;

import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Api("会员支付")
public class WxMemberPayDTO {

	@ApiModelProperty("企业guid")
	private String enterpriseGuid;

	@ApiModelProperty("门店guid")
	private String storeGuid;

	@ApiModelProperty("桌台guid")
	private String tableGuid;

	@ApiModelProperty("用户opId")
	private String openId;

	@ApiModelProperty("用户昵称")
	private String nickName;

	@ApiModelProperty("用户微信头像")
	private String headImgUrl;

	@ApiModelProperty(value = "会员密码")
	private String memberPassWord;

	@ApiModelProperty(value = "门店名字")
	private String storeName;

	private WxStoreConsumerDTO wxStoreConsumerDTO;
}
