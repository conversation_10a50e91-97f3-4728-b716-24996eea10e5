package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @className WarehouseReqDTO
 * @date 2019-04-24 15:29:09
 * @description
 * @program holder-saas-store-dto
 */
public class WarehouseReqDTO implements Serializable {

    private static final long serialVersionUID = 3990685863840271598L;
    @ApiModelProperty(value = "主键guid")
    private String guid;
    @ApiModelProperty(value = "仓库名称")
    private String name;
    @ApiModelProperty(value = "仓库编号")
    private String code;
    @ApiModelProperty(value = "仓库地址")
    private String addr;
    @ApiModelProperty(value = "负责人")
    private String pic;
    @ApiModelProperty(value = "电话")
    private String tel;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "类型(0:企业库，1:门店库，2:其他)")
    private Integer type;
    @ApiModelProperty(value = "门店或企业guid")
    private String foreignKey;
    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getAddr() {
        return addr;
    }

    public void setAddr(String addr) {
        this.addr = addr;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(String foreignKey) {
        this.foreignKey = foreignKey;
    }

    public String getEnterpriseGuid() {
        return enterpriseGuid;
    }

    public void setEnterpriseGuid(String enterpriseGuid) {
        this.enterpriseGuid = enterpriseGuid;
    }
}
