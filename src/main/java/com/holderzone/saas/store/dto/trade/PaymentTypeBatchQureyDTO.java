package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeBatchQureyDTO
 * @date 2019/06/04 16:20
 * @description
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量查询所有门店的支付方式请求入参")
public class PaymentTypeBatchQureyDTO {
    List<PaymentTypeQueryDTO> paymentTypeQueryDTOList;
}
