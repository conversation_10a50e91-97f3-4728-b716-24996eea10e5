package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 商户后台/数据报表/订单统计 列表查询结果DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
@Data
public class BusinessOrderStatisticsRespDTO {

    @ApiModelProperty(value = "订单表唯一标识")
    private String guid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "就餐类型")
    private String tradeMode;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态")
    private String orderState;

    @ApiModelProperty(value = "订单来源")
    private String orderSource;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "结账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkoutTime;

    private String diningTableName;

    @ApiModelProperty(value = "桌台guid")
    private String diningTableGuid;

    @ApiModelProperty(value = "订单金额")
    private String orderFeeForCombine;

    @ApiModelProperty(value = "实收金额")
    private String actuallyPayFee;

    @ApiModelProperty(value = "支付方式")
    private String payWay;

    @ApiModelProperty(value = "收银员")
    private String cashier;

    @ApiModelProperty(value = "是否并单:为空或0：无并单,1：并单")
    private Integer isMerge;

    @ApiModelProperty(value = "为2 存在退单信息")
    private Integer recoveryType;

    @ApiModelProperty(value = "退款订单guid")
    private String refundOrderGuid;

    /**
     * 开票结果 1成功 0失败
     */
    @ApiModelProperty(value = "开票结果")
    private Boolean isInvoice;

    @ApiModelProperty(value = "订单总团购实付金额")
    private BigDecimal totalCouponBuyPrice;

    @ApiModelProperty(value = "订单商品信息")
    private List<Item> itemList;

    @Data
    public static class Item {

        /**
         * 商品名称
         */
        private String itemName;

        /**
         * 商品规格
         */
        private String itemSku;

        /**
         * 商品分类
         */
        private String itemType;

        /**
         * 商品数量
         */
        private BigDecimal itemNumber;

        /**
         * 单价
         */
        private BigDecimal itemPrice;

        /**
         * 属性加价
         */
        private BigDecimal itemAttrPrice;

        /**
         * 小计
         */
        private BigDecimal itemAmount;

        /**
         * 商品优惠
         */
        private BigDecimal itemReduce;

        /**
         * 实付金额
         */
        private BigDecimal itemPayFee;

        /**
         * 团购
         */
        private String itemGroupon;

        /**
         * 下单时间
         */
        private LocalDateTime createTime;

        /**
         * 点单员
         */
        private String createStaffName;

        /**
         * 商品备注
         */
        private String itemRemark;
    }
}
