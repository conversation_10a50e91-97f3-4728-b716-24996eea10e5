package com.holderzone.saas.store.dto.takeaway;

public enum OrderCallEnum {
    ALREADY_CALL(0, "已拨打"),

    NOT_CALL(1, "未拨打"),

    ALREADY_CONNECT(2, "已接通"),

    NOT_CONNECT(3, "未接通");

    /**
     * 类型
     */
    private int type;

    /**
     * 描述
     */
    private String desc;

    OrderCallEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderCallEnum ofType(int type) {
        for (OrderCallEnum value : OrderCallEnum.values()) {
            if (type == value.type) {
                return value;
            }
        }
        throw new IllegalArgumentException("不存在业务类型businessId[" + type + "]");
    }
}
