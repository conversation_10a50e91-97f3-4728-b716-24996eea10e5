package com.holderzone.saas.store.dto.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberRechargeDTO
 * @date 2018/08/21 13:51
 * @description 会员充值DTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class MemberRechargeDTO extends MemberJHRechargeDTO {

    @ApiModelProperty(value = "会员guid必传",required = true)
    private String memberGuid;

    @ApiModelProperty(value = "会员电话")
    private String telPhoneNo;

    @ApiModelProperty(value = "会员name")
    private String memberName;

    @ApiModelProperty(value = "订单号")
    private String orderGuid;

    @NotNull
    @ApiModelProperty(value = "充值类型 0:现金，1:聚合支付，2:银行卡支付，3:会员卡支付")
    private Integer paymentType;

    @NotBlank
    @ApiModelProperty(value = "充值name")
    private String paymentName;

    @ApiModelProperty(value = "银行卡流水号")
    private String bankTransactionId;

    @ApiModelProperty(value = "如果是银行卡支付或者其他支付方式，需要传入银行卡流水号")
    private String thirdOrderNo;

    @ApiModelProperty(value = "营业日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate businessDay;

}
