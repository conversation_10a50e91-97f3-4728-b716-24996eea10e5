package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishSynRespDTO
 * @date 2019/01/03 下午2:10
 * @description //套餐详情返回实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "套餐详情返回实体")
public class ItemPkgInfoRespDTO implements Serializable {

    @ApiModelProperty(value = "商品唯一标识")
    private String itemGuid;
    @ApiModelProperty(value = "分类GUID")
    private String typeGuid;
    @ApiModelProperty(value = "商品来源（0：门店自己创建的商品，1：品牌自己创建的商品,2:被推送过来的商品）")
    private Integer itemFrom;
    @ApiModelProperty(value = "商品类型:1.套餐（不称重，无规格），2.规格商品（单商品，不称重），3.称重商品（单商品，称重），4.单品")
    private Integer itemType;

    @ApiModelProperty(value = "是否售罄:0 否 1 是")
    private Integer isSoldOut;

    @ApiModelProperty(value = "是否有属性:0 否 1 是")
    private Integer hasAttr;

    @ApiModelProperty(value = "商品名称")
    private String name;
    @ApiModelProperty(value = "拼音简码")
    private String pinyin;
    @ApiModelProperty(value = "别名")
    private String nameAbbr;
    @ApiModelProperty(value = "排序号")
    private Integer sort;
    @ApiModelProperty(value = "商品描述")
    private String description;

    @ApiModelProperty(value = "商品主图路径")
    // todo 安桌端的商品图片是一个还是两个？
    private String pictureUrl;

    @ApiModelProperty(value = "是否热销：对应快餐用的字段为为：isRecommend：0：否，1：是")
    private Integer isBestseller;
    @ApiModelProperty(value = "是否新品：快餐无对应字段：0：否，1：是")
    private Integer isNew;
    @ApiModelProperty(value = "是否是招牌：0：否，1：是")
    private Integer isSign;
    @ApiModelProperty(value = "是否是固定套餐，0：否，1：是")
    private Integer isFixPkg;
    @ApiModelProperty(value = "规格集合")
    private List<SkuSynRespDTO> skuList;
    @ApiModelProperty(value = "分组集合")
    private List<SubgroupSynRespDTO> subgroupList;

}
