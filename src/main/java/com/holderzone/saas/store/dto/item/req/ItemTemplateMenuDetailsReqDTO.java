package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateMenuDetailsReqDTO
 * @date 2019/06/03 10:42
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
@ApiModel(value = "商品模板菜单详情请求DTO")
public class ItemTemplateMenuDetailsReqDTO {


    @ApiModelProperty(value = "菜单guid")
    private String guid;

    @ApiModelProperty(value = "菜品类型guid")
    private String typeGuid;

    @ApiModelProperty(value = "查询关键字")
    private String keywords;

}
