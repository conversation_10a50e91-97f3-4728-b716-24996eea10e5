package com.holderzone.saas.store.dto.journaling.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/8/28 15:23
 */
@Data
@ApiModel("报表-折线图显示")
public class SaleStatisticsByHoursReqDTO extends JournalAppBaseReqDTO {

    @ApiModelProperty(value = "企业guid")
    String enterpriseGuid;

}
