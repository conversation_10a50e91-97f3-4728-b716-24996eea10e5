package com.holderzone.saas.store.dto.erp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/07 下午 18:40
 * @description
 */
@ApiModel("盘点单添加或者更新实体")
public class CheckoutDocumentAddOrUpdateDTO extends BaseDTO {

    @ApiModelProperty("盘点单实体唯一标识")
    private String guid;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "Asia/Shanghai")
    private Date gmtCreate;

    @ApiModelProperty("纸质编号")
    private String code;

    @ApiModelProperty("仓库guid")
    private String warehouseGuid;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("单据门店guid")
    private String documentStoreGuid;

    @ApiModelProperty("盘点类型(0：日盘，1：周盘， 2：月盘)")
    private Integer type;

    @ApiModelProperty("经办人guid")
    private String operatorGuid;

    @ApiModelProperty("经办人名称")
    private String operatorName;

    @ApiModelProperty("盘点时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "Asia/Shanghai")
    private Date documentDate;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否锁盘")
    private Boolean lock;

    @ApiModelProperty("盘点单明细")
    private List<CheckoutDocumentDetailAddOrUpdateDTO> detailList;

    public Boolean getLock() {
        return lock;
    }

    public void setLock(Boolean lock) {
        this.lock = lock;
    }

    public String getDocumentStoreGuid() {
        return documentStoreGuid;
    }

    public void setDocumentStoreGuid(String documentStoreGuid) {
        this.documentStoreGuid = documentStoreGuid;
    }

    public List<CheckoutDocumentDetailAddOrUpdateDTO> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<CheckoutDocumentDetailAddOrUpdateDTO> detailList) {
        this.detailList = detailList;
    }

    public Date getDocumentDate() {
        return documentDate;
    }

    public void setDocumentDate(Date documentDate) {
        this.documentDate = documentDate;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOperatorGuid() {
        return operatorGuid;
    }

    public void setOperatorGuid(String operatorGuid) {
        this.operatorGuid = operatorGuid;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
