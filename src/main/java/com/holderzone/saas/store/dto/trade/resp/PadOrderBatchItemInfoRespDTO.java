package com.holderzone.saas.store.dto.trade.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description pad点餐批次商品信息
 * @date 2021/9/1 10:37
 * @className: PadOrderBatchItemInfoRespDTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "pad点餐批次商品信息")
public class PadOrderBatchItemInfoRespDTO implements Serializable {

    private static final long serialVersionUID = -1371485745630497209L;

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品类型(1.套餐主项，2.规格，3.称重，4.单品 5.团餐主项)")
    private Integer itemType;

    /**
     * 组装的规格和属性的字符串
     * 单规格无属性商品直接返回空
     */
    @ApiModelProperty(value = "组装的规格和属性的字符串")
    private String skuAttrDescString;

    @ApiModelProperty(value = "规格guid", required = true)
    private String skuGuid;

    @ApiModelProperty(value = "sku价格", required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "会员价格")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "数量", required = true)
    private BigDecimal currentCount;

    /**
     * 属性总价
     */
    @ApiModelProperty(value = "属性总价")
    private BigDecimal attrTotal;

    /**
     * 小图
     */
    @ApiModelProperty(value = "商品图片地址，小图")
    private String smallPicture;
}
