package com.holderzone.saas.store.dto.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PageQuery
 * @date 2018/07/23 下午5:55
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Deprecated//建议分页BasePageDTO
public class PageQuery {

    /**
     * 偏移index
     */
    private Long offsetIndex;

    /**
     * 分页size
     */
    private Long pageSize;
}
