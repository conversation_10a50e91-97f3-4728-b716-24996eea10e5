package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/15
 * @description 支付宝授权查询返回
 */
@Data
public class AlipayAuthRespDTO {

    @ApiModelProperty(value = "企业GUID")
    private String enterpriseGuid;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    @ApiModelProperty(value = "支付宝商户appid")
    private String appId;

    @ApiModelProperty(value = "应用授权Token")
    private String appAuthToken;

    @ApiModelProperty(value = "'应用公钥'")
    private String applyPublicKey;

    @ApiModelProperty(value = "'应用私钥'")
    private String applyPrivateKey;

    @ApiModelProperty(value = "'支付宝公钥'")
    private String aliPublicKey;

    @ApiModelProperty(value = "支付宝接口加密方式")
    private String aes;

}
