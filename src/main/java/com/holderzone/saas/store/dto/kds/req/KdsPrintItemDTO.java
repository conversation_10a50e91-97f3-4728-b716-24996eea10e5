package com.holderzone.saas.store.dto.kds.req;

import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 打印公共基础类
 *
 * <AUTHOR>
 * @date 2018/09/18 19:32
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "制作单、出堂单单")
public class KdsPrintItemDTO extends KdsPrintDTO {

    @Valid
    @NotEmpty(message = "商品列表不能为空")
    @ApiModelProperty(value = "商品列表")
    private List<PrdDstItemDTO> itemRecordList;
}
