package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Api("会员卡或优惠券")
@Data
@Builder
public class CardAndVolumeDTO {

	private List<MemberDiscountDTO> memberDiscountDTOS;

	@ApiModelProperty("优惠合计")
	private BigDecimal discountAmount;

	@ApiModelProperty("0:正常，1：需要更新，2：订单信息发生变化 ")
	private Integer result;

	private String errorMsg;
}
