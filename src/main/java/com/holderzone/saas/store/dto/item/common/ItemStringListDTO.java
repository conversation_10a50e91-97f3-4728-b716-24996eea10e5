package com.holderzone.saas.store.dto.item.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishSingleDTO
 * @date 2018/11/30 上午10:07
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@Accessors(chain = true)
public class ItemStringListDTO extends ItemLogDTO {

    private static final long serialVersionUID = 1727528918478527413L;

    @ApiModelProperty(value = "最主要的业务请求参数")
    private List<String> dataList;

    @ApiModelProperty(value = "商品guidList")
    private List<String> itemList;

    @ApiModelProperty(value = "记录id")
    private Long recordId;
}
