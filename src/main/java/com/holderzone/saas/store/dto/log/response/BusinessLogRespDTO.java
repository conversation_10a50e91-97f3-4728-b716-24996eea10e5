package com.holderzone.saas.store.dto.log.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessLogRespDTO
 * @date 2018/12/07 10:12
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class BusinessLogRespDTO {
    @ApiModelProperty(value = "业务日志Guid")
    private String businessLogGuid;
    @ApiModelProperty(value = "操作对象")
    private List<String> target;
    @ApiModelProperty(value = "平台")
    private String platform;
    @ApiModelProperty(value = "操作子功能")
    private String button;
    @ApiModelProperty(value = "操作门店")
    private String store;
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;
    @ApiModelProperty(value = "日志详情内容")
    private String logContent;
    @ApiModelProperty(value = "操作类型")
    private String method;
    @ApiModelProperty(value = "操作模块")
    private String module;
    @ApiModelProperty(value = "操作人")
    private String createBy;
}
