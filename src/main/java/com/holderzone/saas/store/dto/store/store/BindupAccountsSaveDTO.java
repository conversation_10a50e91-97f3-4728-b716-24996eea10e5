package com.holderzone.saas.store.dto.store.store;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 保存传递参数
 * <AUTHOR>
 */
@Data
public class BindupAccountsSaveDTO implements Serializable {

    /**
     * 是否强制扎帐
     */
    @NotNull
    @Min(value = 0, message = "是否强制扎帐：0=不强制，1=强制")
    @Max(value = 1, message = "是否强制扎帐：0=不强制，1=强制")
    @ApiModelProperty(value = "是否强制扎帐  1：否  2：是")
    private Integer isBuAccounts;

    /**
     *是否展示金额
     */
    @NotNull
    @Min(value = 0, message = "是否展示金额：0=不显示，1=显示")
    @Max(value = 1, message = "是否展示金额：0=不显示，1=显示")
    @ApiModelProperty(value = "是否展示现金  1：不显示  2：显示")
    private Integer isShowCash;


    /**
     * 多人交接班 0 否 1是
     */
    @NotNull
    @Min(value = 0, message = "是否多人交接班：0 否 1是")
    @Max(value = 1, message = "是否多人交接班：0 否 1是")
    @ApiModelProperty(value = "是否多人交接班 0 否 1是")
    private Integer isMultiHandover;

    /**
     * 品牌guid
     */
    @NotNull(message = "品牌guid不为空")
    private String brandGuid;

}
