package com.holderzone.saas.store.dto.trade.req.adjust;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 调整外卖单请求DTO
 */
@Data
@NoArgsConstructor
public class AdjustTakeoutOrderReqDTO extends BasePageDTO {

    private static final long serialVersionUID = -2566037494096470408L;

    /**
     * 外卖订单guid
     */
    @NotNull(message = "外卖订单guid不能为空")
    private String orderGuid;

    /**
     * 外卖订单明细guids
     */
    @NotEmpty(message = "外卖订单明细guids不能为空")
    private List<String> orderItemGuids;

    public AdjustTakeoutOrderReqDTO(String orderGuid, List<String> orderItemGuids) {
        this.orderGuid = orderGuid;
        this.orderItemGuids = orderItemGuids;
    }
}
