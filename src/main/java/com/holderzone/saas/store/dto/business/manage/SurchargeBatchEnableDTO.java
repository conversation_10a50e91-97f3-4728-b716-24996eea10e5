package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class SurchargeBatchEnableDTO implements Serializable {

    private static final long serialVersionUID = -2630904057455048727L;

    @NotEmpty(message = "附加费Guid列表不得为空")
    @ApiModelProperty(value = "附加费Guid列表", required = true)
    private List<String> surchargeGuidList;

    @NotNull(message = "是否启用不得为空")
    @ApiModelProperty(value = "是否启用：0=停用，1=启用", required = true)
    private Boolean isEnable;
}
