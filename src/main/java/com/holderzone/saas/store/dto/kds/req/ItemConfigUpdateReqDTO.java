package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class ItemConfigUpdateReqDTO implements Serializable {

    private static final long serialVersionUID = 801750484571895107L;

    @NotEmpty(message = "商品Guid不得为空")
    @ApiModelProperty(value = "商品Guid")
    private String itemGuid;

    @NotEmpty(message = "规格Id不得为空")
    @ApiModelProperty(value = "规格Id")
    private String skuGuid;

    @Min(value = 5, message = "超时时间范围：5-120")
    @Max(value = 120, message = "超时时间范围：5-120")
    @NotNull(message = "超时时间不得为空")
    @ApiModelProperty(value = "超时时间：分钟")
    private Integer timeout;

    @Min(value = 1, message = "最大制作份数范围：1-99")
    @Max(value = 999, message = "最大制作份数范围：1-999")
    @NotNull(message = "最大制作份数不得为空")
    @ApiModelProperty(value = "最大制作份数")
    private Integer maxCopies;

    // fixme 打开注释
    @Min(value = 0, message = "制作模式范围：0-1，0=按订单显示，1=按汇总显示")
    @Max(value = 1, message = "制作模式范围：0-1，0=按订单显示，1=按汇总显示")
//    @NotNull(message = "制作模式范围不得为空")
    @ApiModelProperty(value = "最大制作份数")
    private Integer displayType;
}
