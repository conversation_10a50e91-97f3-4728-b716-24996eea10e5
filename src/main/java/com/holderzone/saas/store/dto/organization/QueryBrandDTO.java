package com.holderzone.saas.store.dto.organization;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueryBrandDTO
 * @date 2019/07/22 14:59
 * @description 品牌查询入参
 * @program holder-saas-store
 */
@Data
@ApiModel("品牌查询入参")
@AllArgsConstructor
@NoArgsConstructor
public class QueryBrandDTO extends BaseDTO {

    @ApiModelProperty("品牌guid")
    private String brandGuid;

    @ApiModelProperty("品牌名字")
    private String brandName;
}
