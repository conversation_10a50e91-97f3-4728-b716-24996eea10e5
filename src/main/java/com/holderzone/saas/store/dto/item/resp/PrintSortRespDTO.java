package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeRespDTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "分类的查询结果实体")
public class PrintSortRespDTO implements Serializable {

    private List<TypeRespDTO> typeRespDTOList;

    private List<ItemPrintTypeRespDTO> itemTypeRespDTOList;
}
