package com.holderzone.saas.store.dto.table;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveOpenTableDTO
 * @date 2019/05/31 15:41
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PadAreaDTO {

    @ApiModelProperty("区域guid")
    private String areaGuid;

    @ApiModelProperty("区域名")
    private String areaName;

    @ApiModelProperty(value = "未绑定的桌台集合")
    private List<TableInfoDTO> tableIDTOList;
}
