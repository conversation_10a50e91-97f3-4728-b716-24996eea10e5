package com.holderzone.saas.store.dto.trade.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description pad下单表
 * @date 2021/8/26 15:58
 * @className: PadOrderRespDTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PadOrderRespDTO implements Serializable {

    private static final long serialVersionUID = -8946201401177740514L;

    /**
     * 全局唯一主键
     */
    @ApiModelProperty(value = "业务主键")
    private Long guid;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 交易订单号
     */
    @ApiModelProperty("订单guid")
    private String orderGuid;

    /**
     * pad点餐批次
     */
    @ApiModelProperty("pad点餐批次")
    private Integer padBatch;

    /**
     * 消费合计
     */
    @ApiModelProperty(value = "消费合计")
    private BigDecimal totalPrice;

    /**
     * 就餐人数
     */
    @ApiModelProperty("就餐人数")
    private Integer actualGuestsNo;

    /**
     * 区域guid
     */
    @ApiModelProperty(value = "区域guid")
    private String areaGuid;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaName;

    /**
     * 桌台guid
     */
    @ApiModelProperty(value = "桌台guid")
    private String diningTableGuid;

    /**
     * 桌台号
     */
    @ApiModelProperty(value = "桌台号")
    private String tableCode;

    /**
     * see{@link com.holderzone.saas.store.enums.order.OrderStateEnum}
     * 下单状态 0:待处理，1：接单，2：拒单，3：未结帐，4：已结账,5:已做废, 8订单已失效,9.已退菜
     */
    @ApiModelProperty("下单状态")
    private Integer orderState;

    /**
     * 拒单原因
     */
    @ApiModelProperty("拒单原因")
    private String denialReason;

    /**
     * 用餐类型，0：正餐：1：快餐
     */
    @ApiModelProperty("用餐类型，0：正餐：1：快餐")
    private Integer tradeMode;

    /**
     * 门店guid
     */
    @ApiModelProperty("门店guid")
    private String storeGuid;

    /**
     * 整单备注
     */
    @ApiModelProperty("整单备注")
    private String remark;

    /**
     * 商品数量
     */
    @ApiModelProperty("商品数量")
    private Integer itemCount;

    /**
     * 操作人员guid
     */
    @ApiModelProperty("操作人员guid")
    private String operationGuid;

    /**
     * 操作人员名字
     */
    @ApiModelProperty("操作人员名字")
    private String operationName;

    /**
     * 并桌订单的id
     */
    @ApiModelProperty("并桌订单的id")
    private String combineOrderGuid;

    /**
     * 订单来源
     */
    @ApiModelProperty("订单来源")
    private Integer orderSource;

}
