package com.holderzone.saas.store.dto.print;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(description = "打印机关联的桌台")
public class PrinterTableDTO {

    @ApiModelProperty(value = "桌台GUID")
    private String tableGuid;

    @ApiModelProperty(value = "桌台名称")
    private String tableName;
}
