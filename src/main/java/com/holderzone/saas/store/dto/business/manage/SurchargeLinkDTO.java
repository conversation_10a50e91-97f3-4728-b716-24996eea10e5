package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(description = "附加费业务对象")
public class SurchargeLinkDTO implements Serializable {

    private static final long serialVersionUID = -3810430351672213557L;

    @ApiModelProperty(value = "附加费Guid")
    private String surchargeGuid;

    /**
     * 订单guid
     */
    private String orderGuid;

    @ApiModelProperty(value = "区域Guid")
    private String areaGuid;

    @ApiModelProperty(value = "附加费名称")
    private String name;

    @ApiModelProperty(value = "附加费金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "附加费类型：0=按人，1=按桌")
    private Integer type;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 场景：0=正餐，1=快餐
     */
    @ApiModelProperty(value = "场景：0=正餐，1=快餐")
    private String tradeMode;

    /**
     * 有效时间
     */
    @ApiModelProperty(value = "有效时间")
    private Integer effectiveTime;

    public String getUniqueKey() {
        return this.type + this.name + this.unitPrice;
    }
}
