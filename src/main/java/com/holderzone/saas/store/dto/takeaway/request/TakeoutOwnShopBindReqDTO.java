package com.holderzone.saas.store.dto.takeaway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutShopBindReqDTO
 * @date 2018/09/25 9:07
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@ApiModel
@NoArgsConstructor
public class TakeoutOwnShopBindReqDTO {

    @ApiModelProperty(value = "手机号（用于自营外卖)")
    private String tel;

    @ApiModelProperty(value = "门店号（用于自营外卖)")
    private String storeCode;

    @ApiModelProperty(value = "密码（用于自营外卖)")
    private String passWord;

    @ApiModelProperty(value = "appId（用于自营外卖)")
    private String appId;

    @ApiModelProperty(value = "appSecret（用于自营外卖)")
    private String appSecret;

    @ApiModelProperty(value = "thirdStoreCode（用于自营外卖)")
    private String thirdStoreCode;

    @ApiModelProperty(value = "sign")
    private String sign;
}
