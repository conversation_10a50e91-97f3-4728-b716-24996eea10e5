package com.holderzone.saas.store.dto.reserve;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class ReserveReportParamDTO {

    @NotBlank(message = "开始时间不得为空")
    @ApiModelProperty(value = "开始时间", required = true)
    private String startTime;

    @NotNull(message = "下单时间不能为空")
    @ApiModelProperty(value = "下单时间", required = true)
    private String endTime;

    @NotNull(message = "分类名不能为空")
    @ApiModelProperty(value = "分类名", example = "1-单品，2-套餐", required = true)
    private Integer type;

    @NotNull(message = "设备号不能为空")
    @ApiModelProperty(value = "设备号", required = true)
    private String deviceId;

    @NotNull(message = "门店guid不能为空")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

}
