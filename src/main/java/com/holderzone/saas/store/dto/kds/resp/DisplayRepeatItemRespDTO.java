package com.holderzone.saas.store.dto.kds.resp;

import com.holderzone.saas.store.dto.organization.StoreDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * KDS菜品绑定配置
 */
@Data
public class DisplayRepeatItemRespDTO implements Serializable {

    private static final long serialVersionUID = -8993349661482406553L;

    @ApiModelProperty(value = "销售模式: 1 普通模式 2 菜谱方案")
    private Integer salesModel;

    /**
     * 是否允许重复
     */
    private Boolean allowRepeatFlag;

    /**
     * 是否全部门店
     */
    private Boolean allStoreFlag;

    /**
     * 门店列表
     */
    private List<String> storeGuids;

    /**
     * 门店列表信息
     */
    private List<StoreDTO> storeList;
}
