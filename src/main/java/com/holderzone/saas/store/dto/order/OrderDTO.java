package com.holderzone.saas.store.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.trade.AddtionalFeeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderDTO
 * @date 2018-07-25 17:15:31
 * @description
 * @program holder-saas-store-order
 */
@Data
@NoArgsConstructor
public class OrderDTO implements Serializable {

    private static final long serialVersionUID = -6648969016820776028L;

    @ApiModelProperty(value = "结账人")
    private String checkoutStaffGuid;

    @ApiModelProperty(value = "结账人")
    private String checkoutStaffName;

    @ApiModelProperty(value = "订单的guid", hidden = true, required = true)
    private String orderGuid;

    @ApiModelProperty(value = "如果账单更新，需要传入")
    private String billGuid;

    @ApiModelProperty(value = "门店guid", required = true)
    @NotBlank(message = "门店guid不能为空")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称", required = true)
    @NotBlank(message = "门店名称不能为空")
    private String storeName;

    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    @ApiModelProperty(value = "会员消费guid")
    private String memberConsumptionGuid;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "牌号")
    private String mark;

//    @ApiModelProperty(value = "附加费")
//    private BigDecimal surcharge;

    @ApiModelProperty(value = "桌台guid（仅堂食）")
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台名字（仅堂食）")
    private String diningTableName;

    @ApiModelProperty(value = "交易模式：0:堂食,1:快餐,2:外卖", required = true)
    private int tradeMode;

    @ApiModelProperty(value = "订单来源：一体机点餐下单（1）、POS设备（2）、M1（3）、平板点餐（4）、微信公众号（5）、美团外卖（6）饿了么外卖（7）", required = true)
    private Integer orderSource;

    @ApiModelProperty(value = "就餐人数（仅堂食）")
    private int guestCount;

    //    @ApiModelProperty(value = "附加费集合")
//    private List<AddtionalFeeDTO> addtionalFeeDTOS;

    /**
     * 是否需要更新账单信息
     */
//    private Boolean updateBill;

    /**
     * 主单guid
     */
//    private String mainOrderGuid;

    /**
     * 是否是主单 0：无子单，1：主单，2：子单
     */
//    private Integer upperState;

//    @ApiModelProperty(value = "班次")
//    private String shiftTime;

    @ApiModelProperty(value = "菜品guid")
    private List<OrderDish> orderDishes;

    @Data
    public static class OrderDish{

        /**
         * 订单菜品guid
         */
        private String orderDishGuid;

        /**
         * 创建时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime gmtCreate;

        /**
         * 菜品guid
         */
        private String dishGuid;

        /**
         * 菜品名称
         */
        private String dishName;

        /**
         * 套餐主项guid
         */
        private String parentDishGuid;

        /**
         * 菜品的套餐类型(0：单规格、1：多规格、2：套餐主项、3：套餐子项、4：称重菜品 )
         */
        private Byte packageDishType;

        /**
         * 菜品的规格
         */
        private String skuGuid;

        /**
         * 规格名称
         */
        private String skuName;

        /**
         * sku价格
         */
        private BigDecimal price;

        /**
         * 会员价格
         */
        private BigDecimal memberPrice;

        /**
         * 数量
         */
        private BigDecimal orderCount;

        /**
         * 计数单位
         */
        private String unit;

        /**
         * 计数单位code(DishUnitEnum)
         */
        private Integer unitCode;

        /**
         * 属性总价
         */
        private BigDecimal skuPropertyTotal;

        /**
         * 是否赠送(0:非赠送、1：赠送)
         */
        private Byte isGift;

        /**
         * 是否参与权益折扣
         */
        private Byte joinMemberDiscount;

        /**
         * 是否参与整单折扣
         */
        private Byte joinWholeDiscount;

        /**
         * 菜品备注1
         */
        private String remark;

        private List<SkuProperty> skuProperties;
    }

    @Data
    public static class SkuProperty{

        /**
         * 主键
         */
        private Long id;

        /**
         * 创建时间
         */
        private LocalDateTime gmtCreate;

        /**
         * 修改时间
         */
        private LocalDateTime gmtModified;

        /**
         * 是否删除 0：false,1:true
         */
        private Boolean isDelete;

        /**
         * skuPropertyGuid
         */
        private String skuPropertyGuid;

        /**
         * 订单菜品guid
         */
        private String orderDishGuid;

        /**
         * 属性guid
         */
        private String propertyGuid;

        /**
         * 属性名称
         */
        private String propertyName;

        /**
         * 属性价格
         */
        private BigDecimal propertyPrice;

        /**
         * 数量
         */
        private Integer num;

        /**
         * 门店guid
         */
        private String storeGuid;

        /**
         * 门店名称
         */
        private String storeName;
    }


}
