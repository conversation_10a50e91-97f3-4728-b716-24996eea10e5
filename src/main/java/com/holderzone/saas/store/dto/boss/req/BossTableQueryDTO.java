package com.holderzone.saas.store.dto.boss.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/22
 * @description 老板助手桌台查询
 */
@Data
@ApiModel(value = "老板助手桌台信息")
@EqualsAndHashCode(callSuper = false)
public class BossTableQueryDTO implements Serializable {

    private static final long serialVersionUID = 1635025462404554833L;

    @NotEmpty(message = "门店GUID不能为空")
    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("区域guid")
    private String areaGuid;

    @ApiModelProperty("桌台Guid列表")
    private List<String> tableGuidList;

    @ApiModelProperty("企业guid")
    private String enterpriseGuid;

}
