package com.holderzone.saas.store.dto.print.content;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 挂账还款单
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PrintDebtRepaymentDTO extends PrintDTO {

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "单位联系人")
    private String unitContactName;

    @ApiModelProperty(value = "单位联系电话")
    private String unitContactTel;

    @ApiModelProperty(value = "还款记录")
    @Size(min = 1)
    private List<InnerRepaymentRecord> recordList;

    @ApiModelProperty(value = "结账方式")
    private String paymentTypeName;

    @ApiModelProperty(value = "还款合计")
    private BigDecimal repaymentTotalFee;

    @ApiModelProperty(value = "操作员")
    private String updateStaffName;

    @ApiModelProperty(value = "还款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime gmtModified;

    /**
     * 会员电话
     */
    @ApiModelProperty(value = "会员电话")
    private String memberPhone;

    /**
     * 会员名字
     */
    @ApiModelProperty(value = "会员名字")
    private String memberName;


    /**
     * 会员余额
     */
    @ApiModelProperty(value = "会员余额")
    private BigDecimal memberMoney;

    @Data
    public static class InnerRepaymentRecord implements Serializable {

        private static final long serialVersionUID = 2351967816470160154L;

        @ApiModelProperty(value = "挂账时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
        private LocalDateTime gmtCreate;

        @ApiModelProperty(value = "桌台")
        private String diningTableName;

        @ApiModelProperty(value = "还款金额")
        private BigDecimal repaymentFee;

    }

    @Override
    public void applyMock() {
        super.applyMock();
    }
}
