package com.holderzone.saas.store.dto.report.openapi.shiyuanhui;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 销售商品明细响应
 */
@Data
public class SaleProductDetailShiYuanHuiRespDTO implements Serializable {

    private static final long serialVersionUID = -1025167689243809355L;

    /**
     * 序号
     */
    @JsonProperty("Index")
    private Integer index;

    /**
     * 订单guid
     */
    @JsonIgnore
    private String orderGuid;

    /**
     * 下菜时间
     */
    @JsonIgnore
    private LocalDateTime gmtCreate;

    /**
     * 商品唯一标识
     */
    @JsonProperty("ProductCode")
    private String skuGuid;

    /**
     * 商品名称
     */
    @JsonProperty("ProductName")
    private String itemName;

    /**
     * 商品分类编码
     */
    @JsonProperty("ProductTypeCode")
    private String itemTypeGuid;

    /**
     * 商品分类名称
     */
    @JsonProperty("ProductTypeName")
    private String itemTypeName;

    /**
     * 商品单价
     */
    @JsonProperty("UnitPrice")
    private Long price;

    /**
     * 商品数量
     */
    @JsonProperty("ProductNum")
    private Long currentCount;

    /**
     * 商品总价
     */
    @JsonProperty("OldAmount")
    private Long totalPrice;

    /**
     * 优惠金额
     */
    @JsonProperty("PreferentialAmount")
    private Long discountFee;

    /**
     * 实收金额
     */
    @JsonProperty("SaleAmount")
    private Long actuallyPayFee;


}
