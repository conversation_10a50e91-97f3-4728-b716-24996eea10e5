package com.holderzone.saas.store.dto.report.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("商品报表-总计")
public class GoodsSalesTotalDTO {

    @ApiModelProperty(value = "总销量")
    private BigDecimal totalSalesVolume;

    @ApiModelProperty(value = "总价格")
    private BigDecimal totalReceivedSumPrice;

    @ApiModelProperty(value = "总商品实付金额")
    private BigDecimal totalDiscountPrice;

    @ApiModelProperty(value = "总商品堂食实付金额")
    private BigDecimal totalDineInDiscountPrice;

    @ApiModelProperty(value = "毛利润")
    private BigDecimal totalGrossProfitAmount;
}
