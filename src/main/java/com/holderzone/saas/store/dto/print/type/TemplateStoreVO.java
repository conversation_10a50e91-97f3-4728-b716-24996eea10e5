package com.holderzone.saas.store.dto.print.type;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/23
 * @description 模版门店展示
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "模版门店展示", description = "模版门店展示")
public class TemplateStoreVO implements Serializable {

    private static final long serialVersionUID = 6170756590705614929L;

    @ApiModelProperty(value = "'门店guid'")
    private String guid;

    @ApiModelProperty(value = "门店名称")
    private String name;

}
