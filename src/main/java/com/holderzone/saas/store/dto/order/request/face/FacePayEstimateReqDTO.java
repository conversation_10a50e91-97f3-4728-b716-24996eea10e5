package com.holderzone.saas.store.dto.order.request.face;

import com.holderzone.saas.store.dto.common.SingleDataDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FacePayEstimateReqDTO
 * @date 2019/07/22 16:28
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "人脸支付请求dto")
public class FacePayEstimateReqDTO extends SingleDataDTO{

    @ApiModelProperty(value="使用的积分")
    private Integer useIntegral;

    @ApiModelProperty(value="积分抵扣了多少钱")
    private BigDecimal integralDiscountMoney;
}
