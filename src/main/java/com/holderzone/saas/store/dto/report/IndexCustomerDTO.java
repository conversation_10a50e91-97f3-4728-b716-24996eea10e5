package com.holderzone.saas.store.dto.report;


import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel(value = "顾客概览")
@NoArgsConstructor
public class IndexCustomerDTO {

    @ApiModelProperty(value = "顾客总数（位)")
    private long customerCount;
    @ApiModelProperty(value = "消费顾客-会员属性")
    private List<ReportBaseDTO> customerTypeList = Lists.newArrayList();
    @ApiModelProperty(value = "消费顾客-销售类型")
    private List<ReportBaseDTO> sellTypeList = Lists.newArrayList();
}
