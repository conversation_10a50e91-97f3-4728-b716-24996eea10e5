package com.holderzone.saas.store.dto.item.req.price;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/9/29
 */
@Data
public class PlanPriceEditItemDTO {

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "分类名称")
    private String typeName;

    @ApiModelProperty(value = "图片List")
    private List<PlanPricePictureEditDTO> pictureList;

    @ApiModelProperty(value = "售卖名称List")
    private List<PlanPriceSaleNameEditDTO> saleNameList;

    @ApiModelProperty(value = "售价List")
    private List<PlanPriceSaleOrMemberPriceEditDTO> salePriceList;

    @ApiModelProperty(value = "会员价List")
    private List<PlanPriceSaleOrMemberPriceEditDTO> memberPriceList;

    @ApiModelProperty(value = "堂食核算价List")
    private List<PlanPriceSaleOrMemberPriceEditDTO> accountingPriceList;

    @ApiModelProperty(value = "外卖核算价List")
    private List<PlanPriceSaleOrMemberPriceEditDTO> takeawayAccountingPriceList;

    @ApiModelProperty(value = "划线价List")
    private List<PlanPriceSaleOrMemberPriceEditDTO> linePriceList;

    @ApiModelProperty(value = "描述List")
    private List<PlanPriceDescriptionEditDTO> descriptionList;

    @ApiModelProperty(value = "排序List")
    private List<PlanPriceSortEditDTO> sortList;

    @ApiModelProperty(value = "分类名称List")
    private List<PlanPriceTypeEditDTO> typeList;

    @ApiModelProperty(value = "分类排序List")
    private List<PlanPriceTypeEditDTO> typeSortList;

    @ApiModelProperty(value = "英文简介List")
    private List<PlanPriceEnglishBriefEditDTO> englishBriefList;

    @ApiModelProperty(value = "英文配料描述List")
    private List<PlanPriceEnglishIngredientsEditDTO> englishIngredientsDescList;

    @ApiModelProperty(value = "商品分类list")
    private List<PlanPriceSaleNameEditDTO> typeNameList;

}
