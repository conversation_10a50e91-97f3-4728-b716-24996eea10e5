package com.holderzone.saas.store.dto.business.manage;


import com.holderzone.framework.util.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AdditionalFeeDO
 * @date 2018/08/02 下午3:00
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SurchargeListDTO extends Page {

    private static final long serialVersionUID = 8091374849599304216L;

    @NotBlank(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid", required = true)
    private String storeGuid;

    @Min(value = 0, message = "收费方式：0=按人，1=按桌")
    @Max(value = 1, message = "收费方式：0=按人，1=按桌")
    @ApiModelProperty(value = "收费方式。0=按人，1=按桌。")
    private Integer type;
}

