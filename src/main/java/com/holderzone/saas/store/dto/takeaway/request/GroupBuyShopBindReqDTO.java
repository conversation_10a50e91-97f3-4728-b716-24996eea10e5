package com.holderzone.saas.store.dto.takeaway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
@ApiModel
@NoArgsConstructor
@EqualsAndHashCode
public class GroupBuyShopBindReqDTO {

    @NotNull
    @Min(value = 1, message = "团购类型(1：美团，2：饿了么)")
    @Max(value = 2, message = "团购类型(1：美团，2：饿了么)")
    @ApiModelProperty(value = "团购类型(1：美团，2：饿了么)")
    private Integer takeoutType;

    @NotNull
    @Min(value = 0, message = "操作绑定的状态(1：绑定操作，0，解除绑定操作)")
    @Max(value = 1, message = "操作绑定的状态(1：绑定操作，0，解除绑定操作)")
    @ApiModelProperty(value = "操作绑定的状态(1：绑定操作，0，解除绑定操作)")
    private Integer bindingStatus;


    @ApiModelProperty(value = "门店guid", hidden = true)
    private String storeGuid;
}
