package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Api("会员选择确认")
public class WxPrepayConfirmReqDTO {

	@ApiModelProperty(value = "企业guid",required = true)
	private String enterpriseGuid;

	@ApiModelProperty(value = "品牌guid",required = true)
	private String brandGuid;

	@ApiModelProperty(value = "门店guid",required = true)
	private String storeGuid;

	@ApiModelProperty(value = "桌台guid",required = true)
	private String tableGuid;

	@ApiModelProperty(value = "用户guid",required = true)
	private String openId;

	@ApiModelProperty("卡GUID")
	private String cardGuid;

	@ApiModelProperty("卡名称")
	private String cardName;

	@ApiModelProperty("会员持卡GUID")
	private String memberInfoCardGuid;

	@ApiModelProperty("卡体系GUID")
	private String systemManagementGuid;

	@ApiModelProperty("会员卡积分")
	private Integer memberIntegral;

	@ApiModelProperty(value = "优惠券code")
	private String volumeCode;

}
