package com.holderzone.saas.store.dto.trade.resp;

import com.holderzone.saas.store.dto.common.BaseRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description pad二维码返回
 * @date 2021/9/13 14:28
 * @className: PadQrCodeRespDTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel("pad二维码返回")
public class PadQrCodeRespDTO extends BaseRespDTO {

    private static final long serialVersionUID = -1123392258221799573L;

    @ApiModelProperty(value = "pad二维码")
    private byte[] padQrCode;
}
