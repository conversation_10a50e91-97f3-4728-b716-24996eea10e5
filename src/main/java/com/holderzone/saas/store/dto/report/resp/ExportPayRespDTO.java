package com.holderzone.saas.store.dto.report.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExportPayRespDTO
 * @date 2019/12/27 18:05
 * @description 支付流水统计报表到处对象
 * @program holder-saas-store
 */
@Data
public class ExportPayRespDTO<T> {
    private ExportRespDTO<T> exportRespDTO;

    @ApiModelProperty("消费金额小计")
    private String consumeSubtotal;

    @ApiModelProperty("储值金额小计")
    private String storedAmountSubtotal;

    @ApiModelProperty("预定金额小计")
    private String preOrderAmountSubtotal;

    @ApiModelProperty("退款金额小计")
    private String refundSubtotal;

    @ApiModelProperty("收支金额小计")
    private String inoutSubtotal;

    @ApiModelProperty("消费、储值、预定金额总计")
    private String cspTotal;

    @ApiModelProperty("退款金额总计")
    private String refundTotal;

    @ApiModelProperty("收支金额总计")
    private String inoutTotal;

}
