package com.holderzone.saas.store.dto.takeaway.response;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * https://developer.meituan.com/openapi#5.3
 */
@Data
@ApiModel
@NoArgsConstructor
/**
 * 验券前准备，调用coupon/prepare后返回的实体类。
 */
public class MtCouponPreRespDTO implements Serializable {

    private static final long serialVersionUID = -7698626920142162994L;

    @ApiModelProperty(value = "最大可验证张数")
    private int count;

    @ApiModelProperty(value = "券购买价")
    private double couponBuyPrice;

    @ApiModelProperty(value = "券码")
    private String couponCode;

    @ApiModelProperty(value = "是否代金券")
    private Boolean isVoucher;

    @ApiModelProperty(value = "券有效期")
    private String couponEndTime;

    @ApiModelProperty(value = "项目开始时间")
    private String dealBeginTime;

    @ApiModelProperty(value = "项目ID")
    private int dealId;

    @ApiModelProperty(value = "套餐类券码对应套餐详细")
    private String dealMenu;

    @ApiModelProperty(value = "团购券价格")
    private double dealPrice;

    @ApiModelProperty(value = "项目名称")
    private String dealTitle;

    @ApiModelProperty(value = "券面值")
    private double dealValue;

    @ApiModelProperty(value = "返回消息")
    private String message;

    @ApiModelProperty(value = "最小消费张数")
    private int minConsume;

    @ApiModelProperty(value = "操作状态")
    private int result;

    @ApiModelProperty(value = "开店宝设置的团购名字段（仅套餐）")
    private String rawTitle;

    /**
     * @see com.holderzone.saas.store.enums.trade.CouponTypeEnum
     */
    @ApiModelProperty(value = "券类型type=1团餐券; type=2代金券; type=3次卡")
    private Integer couponType;

    @ApiModelProperty(value = "团购类型")
    private Integer groupBuyType;

    @ApiModelProperty(value = "券码渠道 买单:1004")
    private Integer receiptChannel;

    @ApiModelProperty(value = "一键买单券信息")
    private MtMaitonConsumeDTO maitonConsumeDTO;

    /**
     * 支付宝平台侧商品ID，是支付宝平台侧商品的唯一标识，后续与平台交互，需要使用该 ID，建议持久化
     */
    private String itemId;

    /**
     * 抖音团购id
     */
    private String skuId;

    /**
     * 绑定商品id
     */
    private String itemGuid;

    /**
     * 绑定商品id
     */
    private String skuGuid;

    private Long orderItemGuid;

    /**
     * 关联的第三方活动字段
     */
    @ApiModelProperty(value = "更变后的第三方活动guid")
    private String newActivityGuid;

    @ApiModelProperty(value = "第三方活动guid")
    private String activityGuid;

    @ApiModelProperty(example = "是否与其他活动类型共享 0-否 1-是")
    private Byte isThirdShare;

    @ApiModelProperty(example = "是否与其他平台活动共享 0-否 1-是")
    private Byte isActivityShare;

    @ApiModelProperty(example = "单笔订单使用上限")
    private Integer useLimit;

    /**
     * 抵扣金额
     */
    private BigDecimal deductionAmount;

    /**
     * 凭证归属支付宝用户id
     */
    private String userId;

    /**
     * 购买商品的订单id，核销接口使用
     */
    private String orderId;

    /**
     * 预验券时间
     * 小程序预验券使用
     */
    private String preQueryTime;

    public List<List<MtDealRespMenu>> getDealMenus() {
        if (StringUtils.isEmpty(this.getDealMenu())) {
            return Lists.newArrayList();
        }
        List<Object> objectList = JacksonUtils.toObjectList(Object.class, this.getDealMenu());
        return objectList.stream()
                .map(e -> JacksonUtils.toObjectList(MtDealRespMenu.class, JacksonUtils.writeValueAsString(e)))
                .collect(Collectors.toList());
    }

}
