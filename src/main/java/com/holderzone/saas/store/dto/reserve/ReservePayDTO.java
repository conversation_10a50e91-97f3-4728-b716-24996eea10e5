package com.holderzone.saas.store.dto.reserve;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.saas.store.dto.common.BaseDTO;
import lombok.Data;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;

/**
 * {@link ReservePayDTO}
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/31 14:52
 */
@Data
public class ReservePayDTO extends BaseDTO {

    String payName;

    Integer payType;

    BigDecimal payAmount;

    Integer payCount;

    @JsonIgnore
    public Pair<Integer, String> getMapKeyPair() {
        return Pair.of(payType, payName);
    }
}