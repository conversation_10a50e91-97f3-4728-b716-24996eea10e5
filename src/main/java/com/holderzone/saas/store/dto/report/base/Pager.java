package com.holderzone.saas.store.dto.report.base;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 通用分页对象
 */
@Setter
@Getter
public class Pager implements Serializable {
    private static final long serialVersionUID = 2L;

    private int pageNo;

    private int pageSize;

    private int totalCount;

    private int totalPages;

    public Pager() {
        this.pageNo = 1;
        this.pageSize = 2;
        this.totalCount = 0;
        this.totalPages = 1;
    }

    public Pager(int pageNo, int pageSize, int totalCount) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.totalCount = totalCount;
        this.calculateTotalPages();
    }

    public void calculateTotalPages() {
        if (this.totalCount <= 0 || this.pageSize <= 0) {
            this.totalPages = 1;
        } else {
            Double ceil = Math.ceil(this.totalCount * 1.0 / this.pageSize);
            this.totalPages = ceil.intValue();
        }
    }
}
