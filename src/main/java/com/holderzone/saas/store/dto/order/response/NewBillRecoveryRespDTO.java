package com.holderzone.saas.store.dto.order.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className NewBillRecoveryRespDTO
 * @date 2018/11/28 10:24
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class NewBillRecoveryRespDTO {

    @ApiModelProperty(value = "对应账单guid")
    private String billGuid;

    @ApiModelProperty(value = "退款方式")
    private String refundWay;


}
