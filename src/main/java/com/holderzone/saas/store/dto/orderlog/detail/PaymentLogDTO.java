package com.holderzone.saas.store.dto.orderlog.detail;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentLogDTO
 * @date 2018/10/09 20:06
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class PaymentLogDTO {

    @ApiModelProperty(value = "操作类型（logOperationTypeEnum）")
    private Integer operationType;

    @ApiModelProperty(value = "操作类型name（logOperationTypeEnum）")
    private String operationTypeName;

    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;

    @ApiModelProperty(value = "操作人")
    private String operationStaffName;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "应收金额")
    private BigDecimal receivableAmount;

    @ApiModelProperty(value = "支付方式")
    private String payType;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "优惠金额")
    private List<DiscountAmount> discountAmounts;

    @Data
    public static class DiscountAmount {

        @ApiModelProperty(value = "优惠名")
        private String name;

        @ApiModelProperty(value = "优惠金额")
        private BigDecimal amount;
    }
}
