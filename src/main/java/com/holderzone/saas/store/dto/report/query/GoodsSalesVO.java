package com.holderzone.saas.store.dto.report.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
public class GoodsSalesVO {
    @ApiModelProperty(value = "页数")
    private Integer currentPage;
    @ApiModelProperty(value = "条数")
    private Integer pageSize;
    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull
    private LocalDate startTime;
    @ApiModelProperty(value = "结束时间", required = true)
    @NotNull
    private LocalDate endTime;
    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;
    @ApiModelProperty(value = "门店guid")
    private List<String> storeGuid;
    @ApiModelProperty(value = "商品分类")
    private String goodsCategories;
    @ApiModelProperty(value = "商品名称")
    private String goodsNames;
    @ApiModelProperty(value = "就餐类型 0-正餐，1-快餐，2-外卖")
    private Integer cateringType;
    @ApiModelProperty(value = "商品guid")
    private String itemGuid;
    @ApiModelProperty(value = "商品sku_guid")
    private String skuGuid;
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 是否是导出
     */
    private boolean isExport;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    /**
     * 商品销量 1 ，商品分类 2
     */
    private Integer type = 2;

    /**
     * 1.套餐 2.普通,
     */
    private Integer itemType;

    /**
     * 总订单数
     */
    private Long totalOrderCount;

}
