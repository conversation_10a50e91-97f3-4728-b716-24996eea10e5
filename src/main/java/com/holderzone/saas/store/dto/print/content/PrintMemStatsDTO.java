package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("会员消费统计单")
public class PrintMemStatsDTO extends PrintDTO {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private String storeName;

    @NotNull(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始时间", required = true)
    private Long startTime;

    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间", required = true)
    private Long endTime;

    @NotNull(message = "消费单数不能为空")
    @ApiModelProperty(value = "消费单数", required = true)
    private Long consumeQuantity;

    @NotNull(message = "消费金额不能为空")
    @ApiModelProperty(value = "消费金额", required = true)
    private BigDecimal consumeTotal;

    @NotNull(message = "充值单数不能为空")
    @ApiModelProperty(value = "充值单数", required = true)
    private Long rechargeQuantity;

    @NotNull(message = "充值金额不能为空")
    @ApiModelProperty(value = "充值金额", required = true)
    private BigDecimal rechargeTotal;

    @NotNull(message = "充值赠送不能为空")
    @ApiModelProperty(value = "充值赠送", required = true)
    private BigDecimal rechargeGift;

    @NotNull(message = "充值收入不能为空")
    @ApiModelProperty(value = "充值收入", required = true)
    private BigDecimal rechargeIncome;

    @Nullable
    @ApiModelProperty(value = "充值方式收入列表", notes = "充值方式收入列表可以为空（不显示）")
    private List<PayRecord> rechargeDetail;
}
