package com.holderzone.saas.store.dto.trade.resp.adjust;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 调整单-订单商品信息实体
 * @date 2022/1/18 15:58
 * @className: AdjustByOrderItemRespDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "调整单-订单商品信息实体")
public class AdjustByOrderItemRespDTO implements Serializable {

    private static final long serialVersionUID = 6813503331210826033L;

    private Long guid;

    /**
     * 订单商品guid
     */
    @ApiModelProperty(value = "订单商品guid")
    private String orderItemGuid;

    /**
     * 商品guid
     */
    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 商品类型(1.套餐主项，2.规格，3.称重，4.单品 5.团餐主项)
     */
    @ApiModelProperty(value = "商品类型(1.套餐主项，2.规格，3.称重，4.单品 5.团餐主项)")
    private Integer itemType;

    /**
     * 商品的规格
     */
    @ApiModelProperty(value = "商品的规格")
    private String skuGuid;

    /**
     * 规格名称
     */
    @ApiModelProperty(value = "规格名称")
    private String skuName;

    /**
     * 属性名称
     */
    @ApiModelProperty(value = "属性名称")
    private String propertyName;

    /**
     * sku价格
     */
    @ApiModelProperty(value = "sku价格")
    private BigDecimal price;

    /**
     * 当前数量（不包括赠送，不要重复减赠送折扣）
     */
    @ApiModelProperty(value = "当前数量（不包括赠送，不要重复减赠送折扣）")
    private BigDecimal currentCount;

    /**
     * 商品小计
     */
    @ApiModelProperty(value = "商品小计，包含属性加价、子项加价等等")
    private BigDecimal totalPrice;

    /**
     * 其它加价，包含属性加价、子项加价等等
     */
    @ApiModelProperty(value = "其它加价，包含属性加价、子项加价等等")
    private BigDecimal otherAddPrice;

    /**
     * 计数单位
     */
    @ApiModelProperty(value = "计数单位")
    private String unit;

    /**
     * 是否调整过商品，0：否  1：是
     */
    @ApiModelProperty(value = "是否调整过商品，0：否  1：是")
    private Boolean isAdjustItem;

    @ApiModelProperty("调整类型(0.数量调整，1.菜品更换)")
    private Integer adjustType;

    @ApiModelProperty("绑定商品guid")
    private String erpItemSkuGuid;

    @ApiModelProperty("套餐主项guid")
    private Long parentItemGuid;

    @ApiModelProperty("套餐预设数量")
    private BigDecimal packageDefaultCount;

    /**
     * 调整单明细
     */
    @ApiModelProperty(value = "对应商品的调整明细")
    private List<DineInItemDTO> adjustItemList;
}
