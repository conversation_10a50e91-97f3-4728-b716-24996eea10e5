package com.holderzone.saas.store.dto.pay;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SaasAggRefundDTO
 * @date 2019/03/15 13:53
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class SaasAggRefundDTO extends BaseDTO {

    @ApiModelProperty(value = "退款实体")
    private AggRefundReqDTO aggRefundReqDTO;

    @ApiModelProperty(value = "是否是快速收款")
    private Boolean isQuickReceipt = false;

    @ApiModelProperty(value = "交易中心服务回调地址，比如:http://holder-saas-cloud-enterprise/enterprise/find 这个回调地址是saas拿到支付轮询结果的回调")
    private String saasCallBackUrl;
}
