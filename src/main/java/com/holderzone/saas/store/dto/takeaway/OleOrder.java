package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class OleOrder implements Serializable {
    private String address;
    @JsonFormat(
            locale = "zh",
            timezone = "GMT+8"
    )
    private Date createdAt;
    @JsonFormat(
            locale = "zh",
            timezone = "GMT+8"
    )
    private Date activeAt;
    private double deliverFee;
    private double merchantDeliverySubsidy;
    private double vipDeliveryFeeDiscount;
    @JsonFormat(
            locale = "zh",
            timezone = "GMT+8"
    )
    private Date deliverTime;
    private String description;
    private String invoice;
    private boolean book;
    private boolean onlinePaid;
    private String id;
    private List<String> phoneList;
    private long shopId;
    private String openId;
    private String shopName;
    private int daySn;
    private int userId;
    private String userIdStr;
    private double totalPrice;
    private double originalPrice;
    private String consignee;
    private String deliveryGeo;
    private String deliveryPoiAddress;
    private boolean invoiced;
    private double income;
    private double serviceRate;
    private double serviceFee;
    private double hongbao;
    private double packageFee;
    private double activityTotal;
    private double shopPart;
    private double elemePart;
    private boolean downgraded;
    @JsonFormat(locale = "zh", timezone = "GMT+8")
    private Date secretPhoneExpireTime;
    private String taxpayerId;
    private double coldBoxFee;
    private String cancelOrderDescription;
    @JsonFormat(
            locale = "zh",
            timezone = "GMT+8"
    )
    private Date cancelOrderCreatedAt;
    private boolean baiduWaimai;
    private List<String> consigneePhones;
    @JsonFormat(
            locale = "zh",
            timezone = "GMT+8"
    )
    private Date confirmCookingTime;
    private int orderBusinessType;
    @JsonFormat(
            locale = "zh",
            timezone = "GMT+8"
    )
    private Date pickUpTime;
    private int pickUpNumber;
    private double specUserPart;
    private Boolean isBusinessOrder;
    private Boolean pinTuanOrder;
    private String extraJson;
}
