package com.holderzone.saas.store.dto.retail.dinein;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PriceChangeItemReqDTO
 * @date 2019/10/08 18:03
 * @description //TODO
 * @program IdeaProjects
 */
@Data
public class PriceChangeItemReqDTO extends BaseDTO {

    @OrderLockField
    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "itemGuid")
    @NotNull
    private String guid;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "折扣千分比（700代表7折）", required = false)
    @Max(value = 1000)
    @Min(value = 1)
    private Integer discountPercent;

    @ApiModelProperty(value = "改价类型（0-未改价，1-已改价，2-商品折扣）", required = false)
    @NotNull
    private Integer priceChangeType;

}