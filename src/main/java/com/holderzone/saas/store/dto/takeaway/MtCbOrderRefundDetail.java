package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
public class MtCbOrderRefundDetail implements Serializable {

    private static final long serialVersionUID = -679815362770312480L;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 退款消息类型
     */
    private String notifyType;

    /**
     * 退款理由
     */
    private String reason;
}
