package com.holderzone.saas.store.dto.member;

import com.holderzone.saas.store.dto.trade.BaseBillDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberLoginRespDTO
 * @date 2018/08/06 11:38
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MemberLoginRespDTO extends BaseBillDTO {

    @ApiModelProperty(value = "电话号")
    private String telPhoneNo;

    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    @ApiModelProperty(value = "会员名字")
    private String name;

    @ApiModelProperty(value = "密码")
    private String password;

    /**
     * 会员卡余额
     */
    @ApiModelProperty(value = "会员卡余额")
    private BigDecimal remaining;

    /**
     * 该会员可用积分
     */
    @ApiModelProperty(value = "该会员可用积分")
    private Integer couldUseScore;

    /**
     * 是否开启积分抵现 0：关闭，1:开启
     */
    @ApiModelProperty(value = "是否开启积分抵现 0：关闭，1:开启")
    private Byte isOpen;

    /**
     * 消费积分单位
     */
    @ApiModelProperty(value = "消费积分单位")
    private Integer consumeIntegralUnit;

    /**
     * 单笔订单最大消费积分
     */
    @ApiModelProperty(value = "单笔订单最大消费积分")
    private Integer consumeIntegralMax;

    /**
     * 消费积分金额单位
     */
    @ApiModelProperty(value = "消费积分金额单位")
    private BigDecimal consumeFeeUnit;

    /**
     * 消费积分的最小金额
     */
    @ApiModelProperty(value = "消费积分的最小金额")
    private BigDecimal consumeFeeMin;

    /**
     * 会员权益折扣
     */
    @ApiModelProperty(value = "会员权益折扣")
    private BigDecimal memberDiscount;

}
