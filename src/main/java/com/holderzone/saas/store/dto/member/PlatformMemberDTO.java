package com.holderzone.saas.store.dto.member;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2023-08-30
 * @description
 */
@Data
public class PlatformMemberDTO {

    private String phone;

    private String sourceName;

    private String enterpriseGuid;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 平台类型0美团
     */
    private Integer type;

    /**
     * 支付宝 userId
     */
    private String userId;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 头像图片地址
     */
    private String avatar;

    /**
     * 身份认证信息
     */
    private String identityAuthentication;
}
