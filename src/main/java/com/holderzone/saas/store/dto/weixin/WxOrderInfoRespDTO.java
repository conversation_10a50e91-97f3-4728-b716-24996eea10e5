package com.holderzone.saas.store.dto.weixin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 订单信息
 */
@Data
public class WxOrderInfoRespDTO implements Serializable {

    private static final long serialVersionUID = -7296236571760374411L;

    /**
     * 订单guid
     */
    private String guid;

    /**
     * 订单号(前端显示用，门店内唯一，格式************)
     */
    private String orderNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 交易模式(0：正餐，1：快餐)
     *
     * @see TradeModeEnum
     */
    private Integer tradeMode;

    /**
     * 设备类型(订单来源 BaseDeviceTypeEnum)
     */
    private Integer deviceType;

    /**
     * 客人数
     */
    private Integer guestCount;

    /**
     * 桌台guid
     */
    private String diningTableGuid;

    /**
     * 桌台名称(区域+桌台code)
     */
    private String diningTableName;

    /**
     * 作废原因
     */
    private String cancelReason;

    /**
     * 整单备注
     */
    private String remark;

    /**
     * 快餐牌号
     */
    private String mark;

    /**
     * 订单金额（商品总额+附加费）
     */
    private BigDecimal orderFee;

    /**
     * 附加费
     */
    private BigDecimal appendFee;

    /**
     * 实收金额=订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））
     * 应收金额=订单金额-优惠金额-订金(押金)
     */
    private BigDecimal actuallyPayFee;

    /**
     * 优惠金额
     */
    private BigDecimal discountFee;

    /**
     * 1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废 7: 反结账
     */
    private Integer state;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 取消订单设备类型（BaseDeviceTypeEnum）
     */
    private Integer cancelDeviceType;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 商品明细
     */
    private List<InnerItem> orderItemList;

    @Data
    public static class InnerItem implements Serializable {

        private static final long serialVersionUID = 8369790441305747868L;

        @ApiModelProperty(value = "商品名称")
        private String itemName;

        @ApiModelProperty(value = "规格名称")
        private String skuName;

        /**
         * 列表小图
         */
        private String smallPicture;
    }
}
