package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AcceptOrRefuseOrderRespDTO
 * @date 2018/10/15 14:34
 * @description
 * @program holder-saas-store-dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AcceptOrRefuseOrderRespDTO implements Serializable {

    private static final long serialVersionUID = 7155386865663731879L;

    @ApiModelProperty(value = "操作是否成功，1：成功，0：失败")
    private Integer isOperation;

    @ApiModelProperty(value = "失败原因")
    private String errorMessage;
}
