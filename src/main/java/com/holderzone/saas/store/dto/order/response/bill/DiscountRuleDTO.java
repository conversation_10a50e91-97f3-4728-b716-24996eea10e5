package com.holderzone.saas.store.dto.order.response.bill;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/16
 * @description 折扣规则信息
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "限时特价商品")
public class DiscountRuleDTO implements Serializable {

    private static final long serialVersionUID = -8476307848345781427L;

    @ApiModelProperty(value = "活动guid")
    private String activityGuid;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**
     * 为参与活动客户打标签
     */
    @ApiModelProperty(value = "为参与活动客户打标签")
    private List<String> labelGuidList;

    /**
     * 券购买金额
     */
    private BigDecimal couponBuyTotalPrice;

}
