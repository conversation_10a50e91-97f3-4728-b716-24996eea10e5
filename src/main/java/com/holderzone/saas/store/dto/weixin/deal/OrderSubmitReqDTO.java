package com.holderzone.saas.store.dto.weixin.deal;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OrderSubmitReqDTO {
    @ApiModelProperty(value = "就餐人数")
    private Integer userCount;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "0：PC服务端,1：PC平板,2：小店通,3：一体机,4：POS机,5：云平板,6：点菜宝(M1)," +
            "7：PV1(带刷卡的点菜宝),9：厨房显示系统,10: 取餐屏,12：微信 15通吃岛 16翼惠天下")
    /***见 DeviceTypeEnum*/
    private Integer deviceType;

    @ApiModelProperty(value = "是否使用会员优惠")
    private Boolean useMemberDiscountFlag;

    @ApiModelProperty(value = "优惠券code list")
    private List<String> volumeCodes;

    @ApiModelProperty(value = "营销活动guid")
    private String activityGuid;

    @ApiModelProperty(value = "会员持卡guid")
    private String memberCardGuid;

    @ApiModelProperty(value = "附加费明细")
    private List<PreOrderSurchargeDTO> surchargeList;

    @ApiModelProperty(value = "优惠明细")
    private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

    @ApiModelProperty(value = "营销活动所选列表")
    private List<ActivitySelectDTO> activitySelectList;

    @ApiModelProperty(value = "购物车商品列表")
    private List<ItemInfoDTO> shopCartItemList;

    @ApiModelProperty(value = "购物车信息")
    private List<ShopCartItemReqDTO> shopCartItemReqDTOS;

    @ApiModelProperty(value = "商品计算结果信息")
    private List<DineInItemDTO> calculateItemResultList;

}
