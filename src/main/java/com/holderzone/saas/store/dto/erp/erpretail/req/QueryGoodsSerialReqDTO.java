package com.holderzone.saas.store.dto.erp.erpretail.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@Data
@EqualsAndHashCode
public class QueryGoodsSerialReqDTO extends BasePageDTO {

    @ApiModelProperty(value = "开始日期")
    @NotEmpty(message = "startDate 不得为空")
    private String startDate;

    @ApiModelProperty(value = "结束日期")
    @NotEmpty(message = "endDate 不得为空")
    private String endDate;

    @ApiModelProperty(value = "票据类型")
    private int invoiceType;

    @ApiModelProperty(value = "商品Guid")
    @NotEmpty(message = "商品Guid不得为空")
    private String goodsGuid;

    @JsonIgnore
    private String startDateTime;

    @JsonIgnore
    private String endDateTime;

}
