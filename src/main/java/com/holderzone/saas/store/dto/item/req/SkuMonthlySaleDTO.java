package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class SkuMonthlySaleDTO {

    @ApiModelProperty(value = "规格GUID", required = true)
    @NotBlank(message = "规格guid不能为空")
    private String guid;

    @ApiModelProperty(value = "月销售量，不能小于1", required = true)
    @NotNull(message = "月销售量不能为空")
    @Min(value = 1, message = "月销售量不能小于1")
    private Integer monthlySale;
}
