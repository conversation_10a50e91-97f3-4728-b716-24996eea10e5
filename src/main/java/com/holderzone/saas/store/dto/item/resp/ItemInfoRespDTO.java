package com.holderzone.saas.store.dto.item.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishSynRespDTO
 * @date 2019/01/03 下午2:10
 * @description //商品详情返回实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "商品详情返回实体")
public class ItemInfoRespDTO implements Serializable {

    @ApiModelProperty(value = "商品唯一标识")
    private String itemGuid;

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "父商品GUID")
    private String parentGuid;

    @ApiModelProperty(value = "分类GUID")
    private String typeGuid;

    @ApiModelProperty(value = "分类名称")
    private String typeName;

    /**
     * 分类排序
     */
    @ApiModelProperty(value = "分类排序")
    private Integer typeSort;

    @ApiModelProperty(value = "商品来源（0：门店自己创建的商品，1：品牌自己创建的商品,2:被推送过来的商品）")
    private Integer itemFrom;

    @ApiModelProperty(value = "商品类型:1.套餐（不称重，无规格），2.规格商品，3.称重商品，4.单品 ，5:。团餐")
    private Integer itemType;

    @ApiModelProperty(value = "是否售罄:0 否 1 是")
    private Integer isSoldOut;

    @ApiModelProperty(value = "属性组状态:0：无属性; 1:有属性; 2:有必选属性组")
    private Integer hasAttr;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "拼音简码")
    private String pinyin;

    @ApiModelProperty(value = "别名")
    private String nameAbbr;

    @ApiModelProperty(value = "排序号")
    private Integer sort;

    @ApiModelProperty(value = "商品描述")
    private String description = StringUtils.EMPTY;

    /**
     * 英文简述
     */
    @ApiModelProperty(value = "英文简述")
    private String englishBrief = StringUtils.EMPTY;

    /**
     * 英文配料描述
     */
    @ApiModelProperty(value = "英文配料描述")
    private String englishIngredientsDesc = StringUtils.EMPTY;

    @ApiModelProperty(value = "maybe null,商品图片地址,列表小图")
    private String pictureUrl = StringUtils.EMPTY;

    /**
     * 图片路径数组json,这里对应新一期加入的“列表大图”
     */
    @ApiModelProperty(value = "商品图片地址，列表大图")
    private String bigPictureUrl = StringUtils.EMPTY;

    /**
     * 图片路径数组json,这里对应新一期加入的“详情大图”
     */
    @ApiModelProperty(value = "商品图片地址，详情大图")
    private String detailBigPictureUrl = StringUtils.EMPTY;

    @ApiModelProperty(value = "是否热销：对应快餐用的字段为为：isRecommend：0：否，1：是")
    private Integer isBestseller;

    @ApiModelProperty(value = "是否新品：快餐无对应字段：0：否，1：是")
    private Integer isNew;

    @ApiModelProperty(value = "是否是招牌：0：否，1：是")
    private Integer isSign;

    @ApiModelProperty(value = "是否推荐：0：否，1：是")
    private Integer isRecommend;

    @ApiModelProperty(value = "是否是固定套餐，0：否，1：是")
    private Integer isFixPkg;

    @ApiModelProperty(value = "企业guid（冗余小程序端字段）")
    private String enterpriseGuid;

    @ApiModelProperty(value = "商品编码（冗余小程序端字段）")
    private String code;

    @ApiModelProperty(value = "审核状态（冗余小程序端字段）")
    private Integer auditStatus;

    @ApiModelProperty(value = "图文详情（冗余小程序端字段）")
    private String remarkDetail;

    @ApiModelProperty(value = "视频url json数组（冗余小程序端字段）")
    private String videoUrls;

    @ApiModelProperty(value = "好评次数（冗余小程序端字段）")
    private Integer upCount;

    @ApiModelProperty(value = "差评次数（冗余小程序端字段）")
    private Integer downCount;

    @ApiModelProperty(value = "规格集合")
    private List<SkuInfoRespDTO> skuList;

    @ApiModelProperty(value = "一级属性集合")
    private List<AttrGroupWebRespDTO> attrGroupList;

    @ApiModelProperty(value = "分组集合")
    private List<SubgroupWebRespDTO> subgroupList;

    @ApiModelProperty(value = "门店集合")
    private List<StoreDTO> substoreList;

    // 赚餐调用微信那边有的字段
    @ApiModelProperty("商品原价")
    private BigDecimal showPrice = BigDecimal.ZERO;

    @ApiModelProperty("商品会员价")
    private BigDecimal showMemberPrice = BigDecimal.ZERO;

    @ApiModelProperty("显示单位")
    private String showUnit;

    @ApiModelProperty("起卖数")
    private BigDecimal minOrderNum;

    @ApiModelProperty(value = "规格GUID")
    private String itemSkuGuid;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;
}
