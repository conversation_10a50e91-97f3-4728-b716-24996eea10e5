package com.holderzone.saas.store.dto.order.request.waiter;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.framework.util.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> R
 * @date 2020/12/9 12:08
 * @description
 */
@Data
@ApiModel(value = "订单服务员详情分页查询请求参数DTO----后台补录")
public class OrderWaiterMakeUpPageReqDTO extends Page {
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "开始日期时间 yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "开始日期时间不能为空")
    private LocalDateTime startDateTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "营业结束日期时间 yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "结束日期时间不能为空")
    private LocalDateTime endDateTime;

    @NotNull(message = "查询类型")
    @ApiModelProperty(value = "查询类型(1, 开台时间),(2, 结账时间)")
    private Integer timeType;

    @ApiModelProperty(value = "门店Guid集合")
    private List<String> storeGuidList;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单金额前置运算条件")
    private OrderWaiterOperatorDTO frontOrderOperator;

    @ApiModelProperty(value = "订单金额后置运算条件")
    private OrderWaiterOperatorDTO rearOrderOperator;

    @ApiModelProperty(value = "桌台Guid集合")
    private List<String> tableGuidList;
}
