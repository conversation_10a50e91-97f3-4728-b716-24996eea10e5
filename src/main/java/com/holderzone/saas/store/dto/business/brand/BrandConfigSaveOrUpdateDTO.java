package com.holderzone.saas.store.dto.business.brand;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 品牌配置保存或更新DTO
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "品牌配置保存或更新DTO")
public class BrandConfigSaveOrUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键（更新时必填）
     */
    @ApiModelProperty(value = "全局唯一主键（更新时必填）")
    private Long guid;

    /**
     * 品牌guid
     */
    @NotBlank(message = "品牌guid不能为空")
    @ApiModelProperty(value = "品牌guid", required = true)
    private String brandGuid;

    /**
     * 反结账时效限制
     */
    @ApiModelProperty(value = "反结账时效限制")
    private Integer recoveryTimeLimit;

    /**
     * 反结账时效限制单位，0：小时 1：天
     */
    @ApiModelProperty(value = "反结账时效限制单位，0：小时 1：天")
    private Integer recoveryTimeLimitUnit;

    /**
     * 退款时效限制
     */
    @ApiModelProperty(value = "退款时效限制")
    private Integer refundTimeLimit;

    /**
     * 退款时效限制单位，0：小时 1：天
     */
    @ApiModelProperty(value = "退款时效限制单位，0：小时 1：天")
    private Integer refundTimeLimitUnit;
}
