package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("微信品牌我的订单分组")
@AllArgsConstructor
@NoArgsConstructor
public class WxBrandUserOrderTitleGroup {

	@ApiModelProperty(value = "订单数量")
	private Integer orderNum;

	@ApiModelProperty(value="状态名称")
	private String orderName;

	//0:待确认，3:已取消，5:待支付，6：已完成 )
	@ApiModelProperty(value = "状态(0:待确认，3:已取消，1:待支付，2：已完成 )")
	private Integer orderState;
}
