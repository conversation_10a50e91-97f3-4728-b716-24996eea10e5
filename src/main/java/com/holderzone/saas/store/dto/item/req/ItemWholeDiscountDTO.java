package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.item.common.ItemLogDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemRackDTO
 * @date 2019/02/26 上午9:47
 * @description // 商品的批量整单折扣传输对象
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "商品的批量整单折扣传输对象")
public class ItemWholeDiscountDTO extends ItemLogDTO {
    @ApiModelProperty(value = "选中商品的GUID集合")
    @NotEmpty
    private List<String> itemGuidList;

    @ApiModelProperty(value = "是否加入整单折扣(0：否，1：是)")
    @NotNull
    private Integer wholeDiscountState;
}
