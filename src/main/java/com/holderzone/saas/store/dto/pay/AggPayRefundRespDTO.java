package com.holderzone.saas.store.dto.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayRefundRespDTO
 * @date 2019/03/14 10:38
 * @description 聚合支付退款实体
 * @program holder-saas-store-dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AggPayRefundRespDTO {

    /**
     * 响应码
     * 成功=10000
     */
    @ApiModelProperty(value = "响应码，2.0版本，10000和20045都表示退款成功")
    private String code;

    /**
     * 响应信息
     */
    @ApiModelProperty(value = "响应信息")
    private String msg;

    /**
     * 退款商户的订单号（是）
     * 对应orderHolderNo
     */
    @ApiModelProperty(value = "退款商户的订单号（是），对应orderHolderNo")
    private String mchntOrderNo;

    /**
     * 平台退款号，富民生成（是）
     */
    @ApiModelProperty(value = "平台退款号，富民生成（是）")
    private String refOrderNo;

    /**
     * 退款金额单位分（是）
     */
    @ApiModelProperty(value = "退款金额单位分（是）")
    private String refundFee;

    /**
     * 签名（是）
     */
    @ApiModelProperty(value = "签名（是）")
    private String signature;

    /**
     * 退款状态，直接退款银行不会返回，查询才会返回
     */
    @ApiModelProperty(value = "退款状态，直接退款银行不会返回，查询才会返回")
    private String state;

    /**
     * 商户透传字段
     */
    @ApiModelProperty(value = "附加数据，透传字段")
    private String attachData;


}
