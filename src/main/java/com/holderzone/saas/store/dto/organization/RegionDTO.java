package com.holderzone.saas.store.dto.organization;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className AreaDTO
 * @date 19-1-3 上午10:45
 * @description 省市区DTO（根据省市区查询的query实体可继承该类）
 * @program holder-saas-store-staff
 */
@Data
@ApiModel("省市区DTO")
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class RegionDTO {

    @ApiModelProperty(value = "区域编码")
    private String adcode;

    @ApiModelProperty(value = "区域名称")
    private String name;

    @ApiModelProperty(value = "区域是否可选择")
    private Boolean isCheckable;

    @ApiModelProperty(value = "区域下级")
    private List<RegionDTO> children;

    public RegionDTO(String code, String name, List<RegionDTO> children) {
        this.adcode = code;
        this.name = name;
        this.children = children;
    }
}
