package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class DstBindAreaReqDTO implements Serializable {

    private static final long serialVersionUID = -1158994112223699540L;

    @NotEmpty(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @NotEmpty(message = "设备ID不得为空")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @Nullable
    @ApiModelProperty(value = "已选中区域列表")
    private List<String> selectedAreaGuidList;

    @ApiModelProperty(value = "快餐是否被选择")
    private Boolean isSnackSelected;

    @ApiModelProperty(value = "外卖是否被选择")
    private Boolean isTakeoutSelected;
}
