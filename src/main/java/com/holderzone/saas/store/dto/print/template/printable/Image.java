package com.holderzone.saas.store.dto.print.template.printable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 打印点位图片，即，将要打印的图片的位图存储进来，默认传入进来的是24位图（RGB）
 * 输出完成后自动换新行
 *
 * <AUTHOR>
 * @date 2018/09/27 14:55
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class Image implements Serializable {

    private static final long serialVersionUID = 5084216636078983783L;

    @ApiModelProperty(value = "宽度(像素)")
    private int width = 0;

    @ApiModelProperty(value = "高度(像素)")
    private int height = 0;

    @ApiModelProperty(value = "位图数据")
    private int[] pixels;

    @ApiModelProperty(value = "上下文间距(像素)")
    private int margin = 0;

    @ApiModelProperty(value = "图片链接")
    private String url;

    public Image(String url) {
        this.url = url;
    }

    public Image(int width, int height, int[] pixels) {
        this.width = width;
        this.height = height;
        this.pixels = pixels;
    }

    /**
     * 缩放图形
     *
     * @param width  缩放后的宽度
     * @param height 缩放后的高度
     */
    public void scale(int width, int height) {
        double widthRate, heightRate;
        widthRate = (double) getWidth() / width;
        heightRate = (double) getHeight() / height;
        int[] srcBytes = getPixels();
        int[] destBytes = new int[width * height];
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int i = toInt(y * heightRate) * toInt(width * widthRate) + toInt(x * widthRate);
                destBytes[y * width + x] = srcBytes[i];
            }
        }
        this.width = width;
        this.height = height;
        this.pixels = destBytes;
    }

    /**
     * 缩放图形
     *
     * @param width 缩放后的宽度
     */
    public void scaleWidth(int width) {
        scale(width, (int) (((double) width / getWidth()) * getHeight()));
    }

    private static int toInt(double n) {
        return (int) n;
    }
}
