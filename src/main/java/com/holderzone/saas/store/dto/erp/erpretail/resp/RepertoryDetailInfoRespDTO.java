package com.holderzone.saas.store.dto.erp.erpretail.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel("出入库的用于添加或者更新的实体")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepertoryDetailInfoRespDTO {

    @ApiModelProperty("出入库实体的标识")
    private String guid;

    @ApiModelProperty("单据名称，由后端根据invoiceType自动匹配生成")
    private String invoiceName;

    @ApiModelProperty("经办人名称")
    private String operatorName;

    @ApiModelProperty("单据日期")
    private String invoiceMakeTime;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("应付金额")
    private BigDecimal shouldPayOrReceive;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("商品总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("制单时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty("制单人")
    private String invoiceMaker;

    @ApiModelProperty("商品列表")
    private List<InOutGoodsDTO> detailList;
}
