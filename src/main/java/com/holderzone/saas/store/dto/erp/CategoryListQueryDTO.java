package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 分类关联的物料查询DTO
 *
 * <AUTHOR>
 * @date 2019/05/15 11:03
 */
@ApiModel(value = "物料分类dto")
public class CategoryListQueryDTO {

    @ApiModelProperty(value = "仓库Guid")
    private String warehouseGuid;

    @ApiModelProperty(value = "searchConditions")
    private String searchConditions;

    @ApiModelProperty(value = "storeGuid")
    private String storeGuid;

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }

    public String getSearchConditions() {
        return searchConditions;
    }

    public void setSearchConditions(String searchConditions) {
        this.searchConditions = searchConditions;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }
}
