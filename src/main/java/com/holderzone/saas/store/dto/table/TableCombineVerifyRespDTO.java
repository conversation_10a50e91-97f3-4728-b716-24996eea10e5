package com.holderzone.saas.store.dto.table;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


/**
 * 并台校验提示
 * 子单使用第三方平台活动/团购验券
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TableCombineVerifyRespDTO implements Serializable {

    private static final long serialVersionUID = 3691035271735014843L;

    @ApiModelProperty("是否需要提示")
    private Boolean needTips;

    @ApiModelProperty("提示内容")
    private String tips;

    @ApiModelProperty("注意")
    private String notice;
}