package com.holderzone.saas.store.dto.deposit.req;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
public class DepositQueryReqForWebDTO extends BasePageDTO {

    private static final long serialVersionUID = -2773042054434843563L;

    @ApiModelProperty(value = "门店Guid")
    @NotNull(message = "门店Guid不能为空")
    private String storeGuid;

    @ApiModelProperty(value = "查询条件")
    private String condition;
}
