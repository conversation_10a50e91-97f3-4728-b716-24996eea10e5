package com.holderzone.saas.store.dto.takeaway.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 外卖商品数据修复请求实体
 * @date 2022/5/12 14:15
 * @className: TakeoutItemDataFixReqDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "外卖商品数据修复请求实体")
public class TakeoutItemDataFixReqDTO implements Serializable {

    private static final long serialVersionUID = 941441873134602965L;

    @NotNull(message = "开始日期时间不能为空")
    @ApiModelProperty("时间筛选-开始日期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startDateTime;

    @NotNull(message = "结束日期时间不能为空")
    @ApiModelProperty("时间筛选-结束日期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endDateTime;

    /**
     * 外卖商品编号列表
     */
    @ApiModelProperty("外卖商品编号列表")
    private List<TakeoutAndStoreReqDTO> takeoutAndStore;

    @ApiModelProperty(value = "外卖商品编号列表", hidden = true)
    private List<String> spliceList;

    @ApiModelProperty(value = "顺序类型：0=正序，1=倒叙")
    private Integer sequenceType;
}
