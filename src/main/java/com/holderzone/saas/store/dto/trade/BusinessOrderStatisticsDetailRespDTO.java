package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 商户后台/数据报表/订单统计 列表查询结果DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
@Data
public class BusinessOrderStatisticsDetailRespDTO {

    @ApiModelProperty(value = "订单表唯一标识")
    private String guid;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态")
    private Integer state;

    @ApiModelProperty(value = "订单状态")
    private String orderState;

    @ApiModelProperty(value = "预定单guid")
    private String reserveGuid;

    @ApiModelProperty(value = "开台时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "订单来源")
    private String orderSource;

    @ApiModelProperty(value = "就餐类型")
    private String tradeMode;

    @ApiModelProperty(value = "结账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkoutTime;

    @ApiModelProperty(value = "就餐人数")
    private String guestCount;

    @ApiModelProperty(value = "桌台信息/牌号")
    private String diningTableName;

    @ApiModelProperty(value = "会员姓名")
    private String memberName;

    @ApiModelProperty(value = "会员电话")
    private String memberPhone;

    @ApiModelProperty(value = "开台人")
    private String createStaffName;

    @ApiModelProperty(value = "结账人")
    private String checkoutStaffName;

    @ApiModelProperty(value = "结账操作来源")
    private String checkoutSource;

    @ApiModelProperty(value = "订单金额")
    private String orderFee;

    @ApiModelProperty(value = "应收金额")
    private String accountsReceivableAmount;

    @ApiModelProperty(value = "实收金额")
    private String actuallyPayFee;

    @ApiModelProperty(value = "优惠合计")
    private BigDecimal discountCombined;

    @ApiModelProperty(value = "优惠信息")
    private List<DiscountDTO> discountInfoList;

    @ApiModelProperty(value = "付款金额合计")
    private BigDecimal payWayDetailsCount;

    /**
     * 退款金额
     */
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    /**
     * 总团购实际支付金额
     */
    @ApiModelProperty(value = "总团购实际支付金额")
    private BigDecimal totalCouponBuyPrice;

    @ApiModelProperty(value = "付款金额明细")
    private List<OrderDetailTransactionRecordRespDTO> payWayDetails;

    @ApiModelProperty(value = "商品信息")
    private List<DineInItemDTO> itemInfo;

    @ApiModelProperty(value = "退货商品信息")
    private List<ReturnItemDTO> returnItemInfoList;

    @ApiModelProperty(value = "是否是反结账订单，页面效果为是否存在新/原单 0否 1是")
    private String hasAntiSettlement;

    @ApiModelProperty(value = "原/新单guid，hasAntiSettlement=1返回")
    private String newOrderGuid;

    @ApiModelProperty(value = "是否存在退款信息 0否 1是")
    private String hasRefund;

    @ApiModelProperty(value = "退款信息")
    private List<RefundTransactionRecordDetailRespDTO> refundDetails;

    @ApiModelProperty(value = "是否是并单 0否 1是")
    private String hasMainOrder;

    @ApiModelProperty(value = "0:无并单，1:主单， 2:子单")
    private Integer upperState;

    @ApiModelProperty(value = "并单下拉信息，hasMainOrder=1返回")
    private List<MergeOrderDetailRespDTO> mergeOrderDetails;

    @ApiModelProperty(value = "附加费")
    private List<AppendFeeDetailDTO> appendFeeDetailList;

    @ApiModelProperty(value = "挂账信息")
    private DebtUnitRecordPageRespDTO debtUnitRecord;

    /**
     * 开票结果 1成功 0失败
     */
    @ApiModelProperty(value = "开票结果")
    private Boolean isInvoice;

    /**
     * 发票号码
     */
    @ApiModelProperty(value = "发票号码")
    private String invoiceCode;

    @ApiModelProperty(value = "是否联台单")
    private Boolean associatedFlag;

    @ApiModelProperty(value = "联台单编号")
    private String associatedSn;

    @ApiModelProperty(value = "联台单桌台列表")
    private List<String> associatedTableGuids;

    @ApiModelProperty(value = "联台单桌台列表")
    private List<String> associatedTableNames;

    @ApiModelProperty(value = "麓豆会员电话")
    private String ludouMemberPhone;

    @ApiModelProperty(value = "麓豆会员名字")
    private String ludouMemberName;

    @ApiModelProperty(value = "作废原因")
    private String cancelReason;

    @ApiModelProperty(value = "多单结账信息")
    private List<BusinessOrderStatisticsDetailRespDTO> otherOrderDetails;

}
