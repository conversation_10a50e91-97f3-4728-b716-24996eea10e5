package com.holderzone.saas.store.dto.kds.req;

import com.holderzone.saas.store.dto.order.request.item.TransferItemReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.OrderItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2025/1/8
 * @description kds转菜
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "kds转菜")
public class KdsItemTransferReqDTO implements Serializable {

    private static final long serialVersionUID = 4381609611136368507L;

    @ApiModelProperty(value = "转菜请求")
    private TransferItemReqDTO transferReq;

    @ApiModelProperty(value = "新订单详情")
    private DineinOrderDetailRespDTO orderDetailRespDTO;

    @ApiModelProperty(value = "新旧对照map")
    private HashMap<Long, OrderItemDTO> old2NewMap;

}
