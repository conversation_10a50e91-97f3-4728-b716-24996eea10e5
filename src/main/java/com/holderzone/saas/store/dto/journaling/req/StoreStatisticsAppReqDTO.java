package com.holderzone.saas.store.dto.journaling.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreStatisticsAppReqDTO
 * @date 2019/06/03 10:40
 * @description app报表-门店统计repDTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class StoreStatisticsAppReqDTO extends JournalAppBaseReqDTO {
    private static final long serialVersionUID = -1766006517366421L;
    @ApiModelProperty(value = "页码数")
    private long currentPage;
    @ApiModelProperty(value = "每页条数")
    private long pageSize;
    @ApiModelProperty(value = "排序规则，营业额：0 ，订单数：1")
    private int sortRule;
    @ApiModelProperty(value = "一次数据加载时间节点时间戳")
    private long dataEndTime;

    private Boolean isMch = false;
}
