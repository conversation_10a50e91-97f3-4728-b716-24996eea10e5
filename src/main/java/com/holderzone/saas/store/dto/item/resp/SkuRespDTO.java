package com.holderzone.saas.store.dto.item.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SkuDTO
 * @date 2018/08/23 上午11:20
 * @description //快餐相关DTO
 * @program holder-saas-store-dto
 */
@ApiModel(value = "菜品的sku")
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SkuRespDTO implements Serializable {
    private static final long serialVersionUID = -7595630908370021431L;

    @ApiModelProperty(value = "typeGUID")
    private String typeGuid;

    @ApiModelProperty(value = "skuGUID")
    private String skuGuid;

    @ApiModelProperty(value = "upc")
    private String upc;

    @ApiModelProperty(value = "库存")
    private Integer stock;

    @ApiModelProperty(value = "规格名称")
    @Size(max = 16, message = "规格名称不能超过16位")
    private String name;

    @ApiModelProperty(value = "说明")
    private String spec;

    @ApiModelProperty("是否上下架")
    private Integer isRack;

    @ApiModelProperty(value = "是否上架一体机（0：否，1：是）")
    private Integer isJoinAio;

    @ApiModelProperty(value = "是否上架POS机（0：否，1：是）")
    private Integer isJoinPos;

    @ApiModelProperty(value = "是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;

    @ApiModelProperty(value = "售卖价格")
    @DecimalMax(value = "999999", message = "价格不能大于999999元")
    private BigDecimal salePrice;

    @NotNull(message = "参考价不能为空")
    @DecimalMax(value = "999999", message = "价格不能大于999999元")
    @ApiModelProperty(value = "参考价格")
    private BigDecimal referencePrice;
    @ApiModelProperty(value = "是否删除0：否，1：是")
    private Integer isDelete;
    @ApiModelProperty(value = "注：仅商户后台使用。是否修改售卖价：0：未修改;1:已修改")
    private Integer updateSalePrice;
    @ApiModelProperty(value = "会员价格")
    private BigDecimal memberPrice;
    @ApiModelProperty(value = "是否上架Kds（0：否，1：是）")
    private Integer isJoinKds;

    /**
     * 商品规格单位
     */
    @ApiModelProperty(value = "商品规格单位")
    private String unit;

    /**
     * 核算价
     */
    @ApiModelProperty("堂食核算价")
    private BigDecimal accountingPrice;

    @ApiModelProperty("外卖核算价")
    private BigDecimal takeawayAccountingPrice;

    /**
     * 划线价
     */
    @ApiModelProperty("划线价")
    private BigDecimal linePrice;

    /**
     * 菜谱方案商品guid
     */
    @ApiModelProperty(value = "菜谱方案商品guid")
    private String planItemGuid;

    /***
     * 是否售完下架 0:上架 1：售完下架 2：立即下架（直接删除）
     */
    @ApiModelProperty(value = "是否售完下架 0:已上架 1：售完下架 2：立即下架（直接删除） 3：即将下架 4：已下架（售完商品的下架） ")
    private Integer isSoldOut;
}
