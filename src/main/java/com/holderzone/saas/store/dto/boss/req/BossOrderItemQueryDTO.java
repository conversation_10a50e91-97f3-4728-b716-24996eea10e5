package com.holderzone.saas.store.dto.boss.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/22
 * @description 老板助手订单商品查询
 */
@Data
@ApiModel(value = "老板助手订单商品查询")
@EqualsAndHashCode(callSuper = false)
public class BossOrderItemQueryDTO implements Serializable {

    private static final long serialVersionUID = 9045824807527379812L;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

}
