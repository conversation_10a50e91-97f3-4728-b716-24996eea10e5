package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemMenuSubItemReqDTO
 * @date 2019/05/23 11:49
 * @description //TODO 商品模板菜单子项
 * @program holder-saas-aggregation-app
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "商品模板菜单子项")
public class ItemMenuSubItemReqDTO {

    /**
     * 业务主键
     */
    @ApiModelProperty(value = "业务主键 新增不需要传 ")
    private String guid;

    /**
     * 外键 商品菜单guid
     */
    @ApiModelProperty(value = "商品菜单guid 新增不需要传")
    private String itemMenuGuid;

    /**
     * 商品sku
     */
    @ApiModelProperty(value = "商品sku")
    private String skuGuid;

    /**
     * 模板价格
     */
    @ApiModelProperty(value = "模板价格")
    private BigDecimal price;


    /**
     * 是否逻辑删除 0：否  1：是
     */
    @Builder.Default
    @ApiModelProperty(value = "是否逻辑删除 0：否  1：是 不需要传")
    private Integer isDelete = 0;
}
