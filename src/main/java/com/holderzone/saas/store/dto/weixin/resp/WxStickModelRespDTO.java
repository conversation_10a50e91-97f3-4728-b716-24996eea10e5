package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.resource.common.dto.validate.Add;
import com.holderzone.resource.common.dto.validate.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickModelRespDTO
 * @date 2019/03/16 10:19
 * @description 微信桌贴库响应DTO
 * @program holder-saas-store
 */
@ApiModel(value = "微信桌贴库响应DTO", description = "微信桌贴库响应DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxStickModelRespDTO {
    @ApiModelProperty("唯一标识")
    @NotEmpty(message = "GUID不能为空", groups = Update.class)
    private String guid;
    @ApiModelProperty("桌贴名字")
    @NotEmpty(message = "桌贴名字不能为空", groups = {Add.class, Update.class})
    private String name;
    @ApiModelProperty("原价")
    @NotNull(message = "原价不能为空", groups = {Add.class, Update.class})
    private BigDecimal originalPrice;
    @ApiModelProperty("售价")
    @NotNull(message = "售价不能为空", groups = {Add.class, Update.class})
    private BigDecimal price;
    @ApiModelProperty("缩略图")
    private String previewImg;
    @ApiModelProperty("是否已经购买，0未购买，1已购买")
    private Integer isBought;
}
