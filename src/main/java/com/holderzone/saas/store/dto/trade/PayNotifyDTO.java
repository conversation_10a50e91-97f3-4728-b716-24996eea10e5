package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayNotifyDTO
 * @date 2018/10/10 14:59
 * @description
 * @program holder-saas-store-dto
 */

@Data
public class PayNotifyDTO implements Serializable {

    /**
     * 支付方式类型
     */
    @ApiModelProperty(value = "支付方式类型 支付方式-2美团支付 ，-1 饿了么支付， 0:现金支付 ，1:聚合支付 ,2：银行卡支付，3：会员卡支付，3+n：其他支付方式")
    private Integer paymentType;

    /**
     * 支付方式名字
     */
    @ApiModelProperty(value = "如果是自定义支付，需要传入支付名字")
    private String paymentTypeName;

    /**
     * 当会员登录时候需要，或者会员充值、支付需要
     */
    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    /**
     * 当会员充值相关需要
     */
    @ApiModelProperty(value = "会员电话")
    private String telPhoneNo;

    /**
     * 会员名字
     */
    @ApiModelProperty(value = "会员名字")
    private String memberName;

}
