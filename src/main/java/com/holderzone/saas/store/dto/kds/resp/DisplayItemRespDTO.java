package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2021/1/28 14:13
 */
@Data
public class DisplayItemRespDTO {

    /**
     * 商品guid
     */
    @ApiModelProperty(value = "商品guid")
    private String itemGuid;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;
    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private Integer sort;
    /**
     * 规则类型 0显示批次 1菜品汇总
     */
    @ApiModelProperty(value = "规则类型 0显示批次 1菜品汇总")
    private Integer ruleType;

    /**
     * 菜谱方案商品名称
     */
    @ApiModelProperty(value = "菜谱方案商品名称")
    private String planItemName;


    @ApiModelProperty("商品id(productGuid,productSpecGuid)")
    private String guid;

    private String productGuid;

    private String productName;

    private String productSpecGuid;

    private String productSpecName;
}
