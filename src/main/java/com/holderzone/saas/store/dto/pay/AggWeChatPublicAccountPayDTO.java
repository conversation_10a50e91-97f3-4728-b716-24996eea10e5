package com.holderzone.saas.store.dto.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggWeChatPublicAccountPayDTO
 * @date 2019/03/16 10:34
 * @description
 * @program holder-saas-store-dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AggWeChatPublicAccountPayDTO {

    @ApiModelProperty(value = "订单总金额", required = true)
    private BigDecimal amount;

    @ApiModelProperty(value = "商户自己的订单编号", required = true)
    private String orderGUID;

    @ApiModelProperty(value = "本次支付的唯一标示", required = true)
    private String payGUID;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "订单描述")
    private String body;

    @ApiModelProperty(value = "外部通知的回调地址")
    private String outNotifyUrl;

    @ApiModelProperty(value = "门店名")
    private String storeName;

    @ApiModelProperty(value = "商户名")
    private String enterpriseName;

    @ApiModelProperty(value = "商户唯一标识，appId,交易中心自己组装")
    private String appId;

    @ApiModelProperty(value = "商户名称")
    private String mchntName;

    @ApiModelProperty(value = "appSecret")
    private String appSecret;

    @ApiModelProperty(value = "跳转url")
    private String redirectUrl;

    @ApiModelProperty(value = "透传字段")
    private String attachData;
}
