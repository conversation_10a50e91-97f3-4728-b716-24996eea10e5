package com.holderzone.saas.store.dto.order;

import com.aimilin.annotation.ExlColumn;
import lombok.Data;

import java.io.Serializable;

/**
 * 复制订单导入信息
 *
 * <AUTHOR>
 * @date 2021/12/21 10:51
 */
@Data
public class OrderUploadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单guid
     */
    @ExlColumn("*订单guid")
    private String orderGuid;
    /**
     * 门店guid
     */
    @ExlColumn("导入门店guid")
    private String storeGuid;
}
