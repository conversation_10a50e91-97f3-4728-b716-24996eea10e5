package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @description Pad点餐商品列表返回实体
 * @date 2021/7/29 11:02
 */
@Data
@ApiModel(value = "Pad点餐商品列表返回实体")
public class PadItemRespDTO {

    @ApiModelProperty(value = "商品唯一标识", required = true)
    private String itemGuid;

    @ApiModelProperty(value = "父商品GUID")
    private String parentGuid;

    @ApiModelProperty(value = "分类GUID", required = true)
    private String typeGuid;

    @ApiModelProperty(value = "分类名称", required = true)
    private String itemTypeName;

    @ApiModelProperty(value = "商品类型:1.套餐（不称重，无规格），2.规格商品（单商品，不称重），3.称重商品（单商品，称重）,4,单品  5:团餐", required = true)
    private Integer itemType;

    @ApiModelProperty(value = "maybe null,是否售罄:0 否 1 是")
    private Integer isSoldOut;

    @ApiModelProperty(value = "属性组状态:0：无属性; 1:有属性; 2:有必选属性组" +
            "若该商品为套餐，则含义为（与商品数据库存储数据不同）：若子商品中有一个有必选属性，则该字段=2,若所有子商品均无属性，则该字段=0,其余=1", required = true)
    private Integer hasAttr;

    @ApiModelProperty(value = "商品名称", required = true)
    @Size(max = 40)
    private String name;
    @ApiModelProperty(value = "拼音简码", required = true)
    private String pinyin;

    @ApiModelProperty(value = "maybe null,别名", required = true)
    private String nameAbbr;

    @ApiModelProperty(value = "排序号", required = true)
    private Integer sort;

    @ApiModelProperty(value = "maybe null,商品描述")
    private String description = StringUtils.EMPTY;

    /**
     * 英文简述，菜谱模式才有，普通模式则为空
     */
    @ApiModelProperty(value = "英文简述，菜谱模式才有，普通模式不展示")
    private String englishBrief = StringUtils.EMPTY;

    /**
     * 英文配料描述
     */
    @ApiModelProperty(value = "英文配料描述，菜谱模式才有，普通模式不展示")
    private String englishIngredientsDesc = StringUtils.EMPTY;

    /**
     * 小图
     */
    @ApiModelProperty(value = "商品图片地址，小图")
    private String smallPicture;

    /**
     * 大图
     */
    @ApiModelProperty(value = "商品图片地址，大图")
    private String bigPicture;

    /**
     * 竖图
     */
    @ApiModelProperty(value = "商品图片地址，竖图")
    private String verticalPicture;

    /**
     * 详情大图list
     */
    @ApiModelProperty(value = "商品图片地址，详情大图list")
    private List<String> detailPictureList;

    @ApiModelProperty(value = "规格集合", required = true)
    private List<PadSkuRespDTO> skuList;

    @ApiModelProperty(value = "可能为空集合，一级属性集合")
    private List<AttrGroupSynRespDTO> attrGroupList;

    @ApiModelProperty(value = "可能为空集合，分组集合")
    private List<SubgroupSynRespDTO> subgroupList;
}
