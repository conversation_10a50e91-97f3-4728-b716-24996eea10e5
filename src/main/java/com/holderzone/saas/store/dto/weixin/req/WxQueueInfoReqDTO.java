package com.holderzone.saas.store.dto.weixin.req;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueInfoReqDTO
 * @date 2019/05/14 9:59
 * @description 微信排队详情请求DTO
 * @program holder-saas-store
 */
@Data
@ApiModel("微信排队详情请求DTO")
@AllArgsConstructor
@NoArgsConstructor
public class WxQueueInfoReqDTO extends BaseDTO {
    @ApiModelProperty("排队guid")
    private String queueGuid;
}
