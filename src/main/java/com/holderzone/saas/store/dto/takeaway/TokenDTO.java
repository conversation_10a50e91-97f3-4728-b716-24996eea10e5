package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TokenDTO
 * @date 2018/09/25 9:18
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class TokenDTO implements Serializable {

    @ApiModelProperty(value = "token值")
    private String token;
}
