package com.holderzone.saas.store.dto.order.request.bill;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BilMemberCardCalculateReqDTO
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class BilMemberCardCalculateReqDTO extends BaseDTO {

    private static final long serialVersionUID = -8951585514851962893L;


    /**
     * 会员手机号码或所持卡号
     */
    @ApiModelProperty(value = "会员手机号码或所持卡号",required = true)
    private String phoneNumOrCardNum;

    /**
     * 门店GUID
     */
    @ApiModelProperty(value = "门店GUID",required = true)
    private String storeGuid;

    /**
     * 企业guid
     */
    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    /**
     * 是否只查询当前门店的卡
     */
    @ApiModelProperty(value = "是否只查询当前门店的卡")
    private int isCurrentStoreCard;

    /**
     * 过期校验guid
     */
    @ApiModelProperty(value = "过期校验guid")
    private String checkExpireGuid;

    @ApiModelProperty(value = "登录方式 0-扫码 1-手机号录入")
    private Integer loginType;

    @ApiModelProperty(value = "操作模块 1正餐 2快餐 3会员")
    private Integer moduleType;

    @ApiModelProperty(value = "交易模式(0：正餐，1：快餐)")
    private Integer tradeMode;

    @ApiModelProperty(value = "是否校验登录权限")
    private Boolean isCheckLoginPermissions;


    /** 订单计算参数 */

    @ApiModelProperty(value = "订单guid")
    @OrderLockField
    private String orderGuid;

    @ApiModelProperty(value = "是否使用会员优惠")
    private Boolean useMemberDiscountFlag;

    @ApiModelProperty(value = "是否会员折扣")
    private Boolean isMemberDiscount = Boolean.TRUE;

    @ApiModelProperty(value = "1：会员登陆，2：会员登出，-1：不操作会员")
    private Integer memberLogin;

    @ApiModelProperty(value = "是否计算积分1：计算，2：不计算")
    private Integer memberIntegral;

    @ApiModelProperty(value = "是否是积分商城0：不是，1：是")
    private Integer memberIntegralStore;

    @ApiModelProperty(value = "会员卡guid")
    private String memberInfoCardGuid;

    @ApiModelProperty(value = "会员电话")
    private String memberPhone;

    @ApiModelProperty(value = "整单折扣")
    private BigDecimal wholeDiscount;

    @ApiModelProperty(value = "整单让价")
    private BigDecimal concessional;

    @ApiModelProperty(value = "优惠券code")
    private String volumeCode;

    @ApiModelProperty(value = "优惠券code list")
    private List<String> volumeCodes;

    /**
     * {@link com.holderzone.holder.saas.member.enums.volume.VolumeTypeEnum}
     */
    @ApiModelProperty(value = "优惠券type")
    private Integer volumeType;

    @ApiModelProperty(value = "1：验券，2：撤销，3：查询（不验券，只给微信用）")
    private Integer verify;

    @ApiModelProperty(value = "营销活动guid，如果为字符串0则为取消，如果传null  表示不变")
    private String activityGuid;

    @ApiModelProperty(value = "是否需要详情")
    private Boolean isNeedActivityList;

    @ApiModelProperty(value = "第三方活动信息列表")
    private List<String> thirdActivityDTOList;

    @ApiModelProperty(value = "是否整单折扣")
    private Boolean isWholeDiscount;

}
