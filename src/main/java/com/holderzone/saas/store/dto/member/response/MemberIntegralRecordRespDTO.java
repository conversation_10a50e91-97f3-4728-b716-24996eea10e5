package com.holderzone.saas.store.dto.member.response;

import io.swagger.annotations.ApiModelProperty;
import javafx.scene.chart.ValueAxis;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberIntegralRecordRespDTO
 * @date 2018/09/29 18:21
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data

public class MemberIntegralRecordRespDTO {
    @ApiModelProperty(value = "订单号")
    private  String orderNo;
    /**
     * 交易时间
     */
    @ApiModelProperty(value = "交易时间")
    private LocalDateTime transactionTime;

    /**
     * 剩余积分
     */
    @ApiModelProperty(value = "剩余积分")
    private Integer residualIntegral;

    /**
     * 交易积分
     */
    @ApiModelProperty(value = "交易积分")
    private Integer transactionIntegral;

    /**
     * 交易类型 （IntegralTransactionTypeEnum.code）
     */
    @ApiModelProperty(value = "交易类型编码")
    private Integer type;


    /**
     * 交易类型 （IntegralTransactionTypeEnum.code）
     */
    @ApiModelProperty(value = "交易类型描述")
    private String typeDesc;




    /**
     * 业务主键
     */
    @ApiModelProperty(value = "会员Guid",required = true)
    private String memberGuid;

    /**
     * 操作人guid
     */
    @ApiModelProperty(value = "操作人Guid")
    private String staffGuid;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String staffName;

    @ApiModelProperty
    private LocalDate businessDay;

}
