package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/7/4
 * @description 限时特价计算
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "限时特价计算")
public class SpecialsActivityAmountDTO {

    @ApiModelProperty(value = "限时特价总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "限时特价优惠的金额（原价-单品券金额-限时特价总金额-限时特价总金额中的会员优惠金额）")
    private BigDecimal discountPrice;

    @ApiModelProperty(value = "限时特价金额")
    private BigDecimal specialsPrice;

    @ApiModelProperty(value = "限时特价总金额中的会员优惠金额")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "被限时特价覆盖的价格")
    private BigDecimal limitPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "参与限时特价的数量")
    private BigDecimal joinSpecialsCount;

    @ApiModelProperty(value = "参与后单个商品的优惠金额")
    private BigDecimal specialsSingleDistinctPrice;
}
