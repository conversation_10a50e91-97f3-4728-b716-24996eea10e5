package com.holderzone.saas.store.dto.report.query;

import com.holderzone.saas.store.dto.report.eum.DateTimeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/12/05 上午 10:46
 * @description
 */
@ApiModel
public class TakeawayIndexQueryDTO extends BaseQueryDTO {

    @ApiModelProperty(value = "订单来源", notes = "0:美团,1:饿了么")
    private List<Integer> orderSource;

    @ApiModelProperty(value = "统计维度", notes = "DAY:天,HOUR:时,WEEK:周,MONTH:月")
    private DateTimeEnum dateTime;

    @ApiModelProperty(value = "菜品销售排名依据 0:销售数量，1:销售总额")
    private Integer dishSorted = 0;

    @ApiModelProperty(value = "门店销售排名排名依据 0:完成订单数，1:完成订单额，2:退款订单数，3:退款金额")
    private Integer storeSalesSorted = 0;


    public Integer getDishSorted() {
        return dishSorted;
    }

    public void setDishSorted(Integer dishSorted) {
        this.dishSorted = dishSorted;
    }

    public Integer getStoreSalesSorted() {
        return storeSalesSorted;
    }

    public void setStoreSalesSorted(Integer storeSalesSorted) {
        this.storeSalesSorted = storeSalesSorted;
    }

    public List<Integer> getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(List<Integer> orderSource) {
        this.orderSource = orderSource;
    }

    public DateTimeEnum getDateTime() {
        return dateTime;
    }

    public void setDateTime(DateTimeEnum dateTime) {
        this.dateTime = dateTime;
    }
}
