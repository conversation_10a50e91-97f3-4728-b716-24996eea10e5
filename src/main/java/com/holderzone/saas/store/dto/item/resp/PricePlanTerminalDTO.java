package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 正在进行中的价格方案 所请求的终端实体
 *
 * <AUTHOR>
 * @date 2021/3/17 17:48
 * @description
 */
@ApiModel(value = "正在进行中的价格方案 所请求的终端实体DTO")
@Data
public class PricePlanTerminalDTO {

    @ApiModelProperty(value = "isJoinMiniAppMall")
    private Integer isJoinMiniAppMall;

    @ApiModelProperty(value = "isJoinMiniAppTakeaway")
    private Integer isJoinMiniAppTakeaway;

    @ApiModelProperty(value = "isJoinStore")
    private Integer isJoinStore;

    @ApiModelProperty(value = "isJoinAio")
    private Integer isJoinAio;

    @ApiModelProperty(value = "isJoinPos")
    private Integer isJoinPos;

    @ApiModelProperty(value = "isJoinPad")
    private Integer isJoinPad;

    @ApiModelProperty(value = "isJoinBuffet")
    private Integer isJoinBuffet;
}
