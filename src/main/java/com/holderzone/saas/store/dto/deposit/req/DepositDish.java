package com.holderzone.saas.store.dto.deposit.req;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 寄存商品信息
 *
 * <AUTHOR>
 * @date 2021/6/3 18:30
 */
@Data
public class DepositDish {
    /**
     * 寄存商品skuGuid
     */
    private String skuId;
    /**
     * 寄存商品数量
     */
    private BigDecimal skuCount;
    /**
     * 0 入库 1 出库
     */
    private Integer type;

    private Integer itemType;

    private String itemName;

    private String skuName;

    private String typeName;

    private BigDecimal price;

    private String unit;
}
