package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 * @version 1.0
 * @className PadPictureRespDTO
 * @date 2021/7/21
 * @description pad点餐图片返回DTO
 * @program holder-saas-store-dto
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "pad点餐图片返回DTO")
public class PadPictureRespDTO {

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "小图")
    private String smallPicture;

    @ApiModelProperty(value = "商品小图")
    private String itemSmallPicture;

    @ApiModelProperty(value = "方案小图")
    private String planSmallPicture;

    @ApiModelProperty(value = "大图")
    private String bigPicture;

    @ApiModelProperty(value = "竖图")
    private String verticalPicture;

    @ApiModelProperty(value = "详情大图")
    private String detailPicture;

    @ApiModelProperty(value = "pad菜谱图片大小类型")
    private Integer menuClassifyPictureType;

    @ApiModelProperty(value = "排序")
    private Integer sort;
}
