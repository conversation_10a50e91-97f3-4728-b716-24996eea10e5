package com.holderzone.saas.store.dto.weixin.req;

import com.holderzone.saas.store.dto.weixin.WxStickDownloadTableDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickDownloadReqDTO
 * @date 2019/03/18 15:00
 * @description 微信桌贴下载请求入参
 * @program holder-saas-store
 */
@ApiModel("微信桌贴下载请求入参")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class WxStickDownloadReqDTO {

    @ApiModelProperty(value = "下载key", notes = "下载轮询时必传字段，用于查询当前需下载的桌贴包是否已生成成功")
    private String downloadKey;

    @ApiModelProperty("二维码类型:0普通二维码，1带参数二维码 10 kbz二维码")
    private Integer qrCodeType;

    @ApiModelProperty("桌贴guid")
    private String stickGuid;

    @ApiModelProperty("门店名字")
    private String storeName;

    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("品牌guid")
    private String brandGuid;

    @ApiModelProperty("桌位集合")
    private List<WxStickDownloadTableDTO> tableList;
}
