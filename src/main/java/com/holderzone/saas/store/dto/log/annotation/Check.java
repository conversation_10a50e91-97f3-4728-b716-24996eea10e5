package com.holderzone.saas.store.dto.log.annotation;


import com.holderzone.saas.store.dto.log.validate.ParamConstraintValidated;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义验证器注解
 *
 * @Athor forewei
 * @Email <EMAIL>
 * @Date 2019/12/09
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ParamConstraintValidated.class)
public @interface Check {
    /**
     * 合法的参数值数组
     **/
    String[] paramValues();

    /**
     * 提示信息
     **/
    String message() default "参数值非法";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};


}
