package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("微信商品属性")
public class ItemInfoAttrDTO {
	@ApiModelProperty(value = "属性组id",hidden = true)
	private String attrGroupGuid;
	@ApiModelProperty(value = "guid")
	private String attrGuid;
	@ApiModelProperty(value = "属性详情名称")
	private String name;
	@ApiModelProperty(value = "价格")
	private BigDecimal price;
	@ApiModelProperty(value = "是否选中，1：是，0,否")
	@Builder.Default
	private Integer uck=0;
}
