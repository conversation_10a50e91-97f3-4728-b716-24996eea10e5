package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description Pad点餐分类列表返回实体
 * @date 2021/7/29 11:00
 */
@Data
@ApiModel(value = "Pad点餐分类列表返回实体")
public class PadTypeRespDTO {

    @ApiModelProperty(value = "分类Guid")
    private String typeGuid;

    @ApiModelProperty(value = "商品分类名称")
    private String name;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 商品集合
     */
    @ApiModelProperty(value = "可能为空集合，商品集合")
    private List<PadItemRespDTO> itemList;

    /**
     * pad分类图片大小类型(0小图,1竖图,2整屏)
     * PadTypePictureEnum
     */
    @ApiModelProperty(value = "pad分类图片大小类型(0小图,1竖图,2整屏)")
    private Integer menuClassifyPictureType;
}
