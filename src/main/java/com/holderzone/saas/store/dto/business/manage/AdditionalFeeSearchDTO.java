package com.holderzone.saas.store.dto.business.manage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class AdditionalFeeSearchDTO {

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 区域guid
     */
    @ApiModelProperty(value = "区域guid")
    private List<String> areaGuid;

    /**
     * 附加费guid
     */
    @ApiModelProperty(value = "附加费guid")
    private String additionalFeeGuid;


    /**
     * 附加费名
     */
    @ApiModelProperty(value = "附加费名")
    private String name;

    /**
     * 附加费金额
     */
    @ApiModelProperty(value = "附加费金额")
    private BigDecimal amount;

    /**
     * 附加费排序
     */
    @ApiModelProperty(value = "附加费备注")
    private Integer sort;

    /**
     * 附加费备注
     */
    @ApiModelProperty(value = "附加费备注")
    private String remark;

    /**
     * 是否已启用
     * 0=未启用
     * 1=已启用
     */
    @ApiModelProperty(value = "是否已启用：0=未启用，1=已启用")
    private Integer enable;

    /**
     * 是否删除
     * 0=未删除
     * 1=已删除
     */
    @ApiModelProperty(value = "是否已删除：0=未删除，1=已删除")
    private Integer deleted;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 附加费收费方式
     * 0=按人
     * 1=按桌
     */
    @NotNull
    @ApiModelProperty(value = "收费方式。0=按人，1=按桌。", required = true)
    private Integer feeType;
}
