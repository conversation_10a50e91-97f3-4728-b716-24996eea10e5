package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/17 11:07
 * @description
 */
@Data
@ApiModel("微信第三方用户信息返回参数")
public class WxThirdPartUserInfoRespDTO {
    @ApiModelProperty(value = "主键")
    private String guid;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "第三方openId")
    private String openId;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "用户来源：易慧天下:13 ")
    private Integer source;

    @ApiModelProperty(value = "密码")
    private String password;
}
