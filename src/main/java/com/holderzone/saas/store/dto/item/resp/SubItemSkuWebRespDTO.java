package com.holderzone.saas.store.dto.item.resp;

import com.holderzone.saas.store.dto.weixin.deal.ItemInfoAttrGroupDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.DecimalMax;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SubItemSkuSynRespDTO
 * @date 2019/01/03 下午3:41
 * @description //套餐分组下的子商品实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class SubItemSkuWebRespDTO {
    @ApiModelProperty(value = "sku与分组关联实体GUID")
    private String skuSubgroupGuid;
    @ApiModelProperty(value = "规格Guid", required = true)
    private String skuGuid;

    @ApiModelProperty(value = "商品及规格组合名称")
    private String name;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "规格名称")
    private String skuName = StringUtils.EMPTY;

    @ApiModelProperty(value = "该规格售卖价格")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "商品图片")
    private String pictureUrl = StringUtils.EMPTY;

    @ApiModelProperty(value = "规格关联的商品的分类GUID")
    private String typeGuid;

    @ApiModelProperty(value = "规格关联的商品的分类名称")
    private String typeName;

    @ApiModelProperty("maybe null,规格的编码")
    private String code;

    @ApiModelProperty(value = "规格的商品GUID", required = true)
    private String itemGuid;

    @ApiModelProperty(value = "商品类型(注：套餐子商品中不包括套餐)：1.套餐（不称重，无规格），2.规格商品（单商品，不称重），3.称重商品（单商品，称重），4.单品。", required = true)
    private Integer itemType;

    @ApiModelProperty(value = "商品单位", required = true)
    private String unit;

    @ApiModelProperty(value = "商品元素中的数量", required = true)
    private BigDecimal itemNum;

    @ApiModelProperty(value = "商品加价", required = true)
    private BigDecimal addPrice;

    @ApiModelProperty(value = "属性组状态:0：无属性; 1:有属性; 2:有必选属性组", required = true)
    private Integer hasAttr;

    @ApiModelProperty(value = "是否默认勾选，1：是，0,否", required = true)
    private Integer isDefault;

    @ApiModelProperty(value = "是否可重复选择，0:否,1:是", required = true)
    private Integer isRepeat;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "默认勾选数量，1：默认勾选一个，0,默认不勾选")
    private Integer defaultNum = 1;

    @ApiModelProperty("可能为空集合，该子商品所含属性集合")
    private List<AttrGroupWebRespDTO> attrGroupList;
}
