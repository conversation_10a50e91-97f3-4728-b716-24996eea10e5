package com.holderzone.saas.store.dto.weixin;

import com.holderzone.saas.store.dto.weixin.resp.WxStoreItemRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("微信门店预订单")
public class WxStoreAdvanceOrderDTO {

	private WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO;

	private List<WxStoreItemRespDTO> itemList;

}
