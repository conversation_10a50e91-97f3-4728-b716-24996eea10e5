package com.holderzone.saas.store.dto.weixin.deal;

import com.holderzone.saas.store.enums.weixin.ActivityTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/6/12
 * @description 活动规则描述
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "活动规则描述")
public class ActivityRuleDescDTO implements Serializable {

    private static final long serialVersionUID = 1802511816052648651L;

    /**
     * 活动guid
     */
    @ApiModelProperty(value = "活动guid")
    private String activityGuid;

    /**
     * @see ActivityTypeEnum
     */
    @ApiModelProperty("活动类型 1限时特价 2满减 3满折")
    private Integer activityType;

    @ApiModelProperty(value = "活动规则描述")
    private String activityRuleDesc;

    /**
     * 活动规则
     * 共享互斥关系 0-互斥 1-共享
     */
    @ApiModelProperty(value = "活动规则 共享互斥关系 0-互斥 1-共享")
    private Integer relationRule;

}
