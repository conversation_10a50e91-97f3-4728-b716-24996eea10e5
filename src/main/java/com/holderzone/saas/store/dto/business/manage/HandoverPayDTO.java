package com.holderzone.saas.store.dto.business.manage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className HandoverPayDTO
 * @date 18-9-11 下午7:27
 * @description 指定时间（班次）内的收入信息DTO
 * @program holder-saas-store-dto
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class HandoverPayDTO implements Serializable {

    private static final long serialVersionUID = 167773187287817039L;

    @ApiModelProperty("操作人guid")
    private String userGuid;

    @ApiModelProperty(value = "操作人name")
    private String userName;

    @ApiModelProperty(value = "已结账订单数")
    private Integer checkedCount;

    @ApiModelProperty(value = "充值订单数")
    private Integer chargedCount;

    @ApiModelProperty(value = "销售收入")
    private BigDecimal saleIncoming;

    @ApiModelProperty(value = "充值收入")
    private BigDecimal chargeIncoming;

    @ApiModelProperty(value = "退货订单数")
    private Integer refundCount;

    @ApiModelProperty(value = "退货金额")
    private BigDecimal refundMoney;

    @ApiModelProperty(value = "营业额 = 销售收入+充值收入-会员卡消费金额")
    private BigDecimal businessIncoming;

    @ApiModelProperty(value = "收入详情，key是支付方式类型，value是合计总额")
    private Map<Integer, BigDecimal> incomingDetail;

    @ApiModelProperty(value = "收入详情，key是支付方式名称，value是合计总额")
    private Map<String, BigDecimal> incomingDetailStr;

    @ApiModelProperty("收银笔数")
    private Integer paymentCount;

    @ApiModelProperty("收入金额（包括现金及其他支付方式）")
    private BigDecimal paymentMoney;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间（开班时间）")
    private LocalDateTime gmtCreate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("修改时间（交班时间）")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "充值详情，key是支付方式名称，value是合计总额")
    private Map<String, BigDecimal> chargeDetailStr;

    @ApiModelProperty(value = "充值金额中现金支付合计")
    private BigDecimal chargeCash;

    @ApiModelProperty(value = "销售额中现金支付合计")
    private BigDecimal saleCash;

    @ApiModelProperty(value = "销售收入支付方式详情（带排序）")
    private List<AmountItemDTO> incomingDetailList;

    @ApiModelProperty(value = "优惠方式详情（带排序）")
    private List<AmountItemDTO> discountDetailList;

    @ApiModelProperty(value = "充值收入支付方式详情（带排序）")
    private List<AmountItemDTO> chargeDetailList;

    @ApiModelProperty(value = "还款金额（还挂账金额汇总）")
    private BigDecimal repaymentFeeTotal;

    @ApiModelProperty(value = "还款单数")
    private Integer repaymentFeeCount;

    @ApiModelProperty(value = "还款支付方式（带排序）")
    private List<AmountItemDTO> repaymentList;

    @ApiModelProperty(value = "还款详情，key是支付方式名称，value是合计总额")
    private Map<String, BigDecimal> repaymentStr;

    @ApiModelProperty(value = "客流量")
    private Integer traffic;

    @ApiModelProperty(value = "总餐位数")
    private Integer totalSeats;

    @ApiModelProperty(value = "上座率")
    private String occupancyRatePercent;

    @ApiModelProperty(value = "桌台使用次数")
    private Integer tableUseCount;

    @ApiModelProperty(value = "总桌台数")
    private Integer tableCount;

    @ApiModelProperty(value = "开台率")
    private String openTableRatePercent;

    @ApiModelProperty(value = "翻台率")
    private String flipTableRatePercent;

    @ApiModelProperty(value = "总用餐时间，单位分钟")
    private Long totalDineInTime;

    @ApiModelProperty(value = "平均用餐时间，单位分钟")
    private Integer avgDineInTime;

    @ApiModelProperty(value = "销售（已结总金额）")
    private BigDecimal saleAmount;

    @ApiModelProperty(value = "优惠总额")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "退款总额")
    private BigDecimal refundAmount;
}
