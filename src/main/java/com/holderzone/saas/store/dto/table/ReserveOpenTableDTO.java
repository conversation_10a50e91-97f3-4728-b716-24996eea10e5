package com.holderzone.saas.store.dto.table;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveOpenTableDTO
 * @date 2019/05/31 15:41
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReserveOpenTableDTO {
    private List<String> orderGuids;
    private List<String> fails;
    private List<String> noExistTables;
    private Boolean success;
    private String mainOrderGuid;
    private String mainTableGuid;
}