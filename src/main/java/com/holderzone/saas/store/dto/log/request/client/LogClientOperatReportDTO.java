package com.holderzone.saas.store.dto.log.request.client;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Athor forewei
 * @Email <EMAIL>
 * @Date 2019/12/4
 */
@ApiModel(value = "客户端操作日志上报入参")
public class LogClientOperatReportDTO extends BaseLogOperatReportDTO{


    @ApiModelProperty("企业guid")
    private String enterpriseGuid;

    @ApiModelProperty("企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty("门店名称")
    private String storeName;


    @ApiModelProperty("操作描述")
    private String remark;


    @ApiModelProperty("终端编号")
    private String terminalNo;


    @ApiModelProperty("操作人")
    private String createBy;


    @ApiModelProperty(" 日志内容")
    private String logContent;


    @ApiModelProperty("耗时")
    private  Long runTime;

    @ApiModelProperty("扩充字段1")
    private String ext1;

    @ApiModelProperty("扩充字段2")
    private String ext2;

    @ApiModelProperty("扩充字段3")
    private String ext3;


    public String getEnterpriseGuid() {
        return enterpriseGuid;
    }

    public void setEnterpriseGuid(String enterpriseGuid) {
        this.enterpriseGuid = enterpriseGuid;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getTerminalNo() {
        return terminalNo;
    }

    public void setTerminalNo(String terminalNo) {
        this.terminalNo = terminalNo;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getLogContent() {
        return logContent;
    }

    public void setLogContent(String logContent) {
        this.logContent = logContent;
    }

    public Long getRunTime() {
        return runTime;
    }

    public void setRunTime(Long runTime) {
        this.runTime = runTime;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
