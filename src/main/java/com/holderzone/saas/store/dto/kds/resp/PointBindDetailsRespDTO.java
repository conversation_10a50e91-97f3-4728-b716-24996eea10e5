package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class PointBindDetailsRespDTO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    @ApiModelProperty(value = "商品数量")
    private Integer boundItemCount;

    @ApiModelProperty(value = "商品列表")
    List<PointTypeBindRespDTO> pointTypeBindList;
}
