package com.holderzone.saas.store.dto.item.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel(value = "商品类别返回实体")
public class ItemPrintTypeRespDTO {
    private static final long serialVersionUID = 4475958094205222757L;

    @ApiModelProperty(value = "商品GUID")
    private String itemGuid;

    @ApiModelProperty(value = "商品类型：（0：门店自己创建的商品，1：品牌自己创建的商品,2:被推送过来的商品）")
    private Integer itemFrom;
    @ApiModelProperty(value = "商品类型：1.套餐（不称重，无规格），2.规格商品（单商品，不称重），3.称重商品（单商品，称重），4.单品。")
    private Integer itemType;
    @ApiModelProperty(value = "是否参与推荐：0：否，1：是")
    private Integer isNew;
    @ApiModelProperty(value = "是否热销：0：否,1:是")
    private Integer isBestseller;

    @ApiModelProperty(value = "排序")
    private Integer sort;
    @ApiModelProperty(value = "上架门店数")
    private Integer rackStoreNum;
    @ApiModelProperty(value = "是否关联套餐（0：否，1：是）")
    private Integer isWithPkg;
    @ApiModelProperty(value = "可能为空集合，标签集合,举例：[{\"id\":\"isBestseller\",\"name\":\"热销\"},{\"id\":\"isNew\",\"name\":\"新品\"},{\"id\":\"isSign\",\"name\":\"招牌\"}]")
    private List<TagRespDTO> tagList;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "企业guid（冗余小程序端字段）")
    private String enterpriseGuid;
}
