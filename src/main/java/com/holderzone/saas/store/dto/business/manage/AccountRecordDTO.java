package com.holderzone.saas.store.dto.business.manage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordDO
 * @date 2018/07/28 上午10:01
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class AccountRecordDTO {

    /**
     * 门店guid
     */
    @ApiModelProperty("门店guid")
    private String storeGuid;

    /**
     * 营业日guid
     */
    @ApiModelProperty("营业日guid")
    private String accountRecordGuid;

    /**
     * 创建人guid
     */
    @ApiModelProperty("创建人guid")
    private String createUserGuid;

    /**
     * 创建人名字
     */
    @ApiModelProperty("创建人名字")
    private String createUserName;

    /**
     * 扎帐人guid
     */
    @ApiModelProperty("扎帐人guid")
    private String confirmUserGuid;

    /**
     * 扎帐人名字
     */
    @ApiModelProperty("扎帐人名字")
    private String confirmUserName;

    /**
     * 打印人guid
     */
    @ApiModelProperty("打印人guid")
    private String printUserGuid;

    /**
     * 打印人名字
     */
    @ApiModelProperty("打印人名字")
    private String printUserName;

    /**
     * 扎帐状态
     * 0=未扎帐
     * 1=已扎帐
     */
    @ApiModelProperty("扎帐状态。0=未扎帐，1=已扎帐")
    private Integer status;

    /**
     * 是否已出库
     * 0=未出库
     * 1=已出库
     */
    @ApiModelProperty("是否已出库。0=未出库，1=已出库")
    private Integer outbound;

    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    @ApiModelProperty("是否已删除。0=未删除，1=已删除")
    private Integer deleted;

    /**
     * 营业日
     */
    @ApiModelProperty("营业日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate businessDay;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime gmtModified;

    /**
     * 扎帐时间
     */
    @ApiModelProperty("扎帐时间")
    private LocalDateTime gmtConfirmed;
}
