package com.holderzone.saas.store.dto;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreMerchantDineInItemDTO
 * @date 2019/4/15
 */
@Data
@ApiModel("商户接单商品详情")
public class WxStoreMerchantDineInItemDTO {

	@ApiModelProperty(value = "keyguid",required = true)
	private String guid;

	private List<DineInItemDTO> dineInItemDTOS;

	@ApiModelProperty(value = "商品总数")
	private Integer itemCount;

	@ApiModelProperty(value = "订单金额明细（商品总额+附加费）")
	private List<OrderFeeDetailDTO> orderFeeDetailDTOS;

	@ApiModelProperty(value = "实收金额明细（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
	private List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS;

	@ApiModelProperty(value = "优惠金额明细（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
	private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

	@ApiModelProperty(value = "退货信息")
	private List<ReturnItemDTO> returnItemDTOS;
}
