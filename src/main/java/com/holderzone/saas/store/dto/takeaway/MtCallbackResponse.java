package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class MtCallbackResponse implements Serializable {

    private static final long serialVersionUID = 2728158125453745059L;

    /**
     * 厂商接收后需立即返回{“data”.“OK”}
     * 如4秒内不返回或返回数据格式错误，平台会重新推送并触发短信报警
     */
    private String data;

    private Integer code;

    private String message;

    public MtCallbackResponse(String data) {
        this.data = data;
    }

    public MtCallbackResponse(Integer code) {
        this.code = code;
    }

    /**
     * 官网是大写的 OK
     */
    public static MtCallbackResponse OK = new MtCallbackResponse("OK");

    /**
     * 官网是小写的 success
     */
    public static MtCallbackResponse SUCCESS = new MtCallbackResponse("success");

    /**
     * 签名错误
     */
    public static MtCallbackResponse SIGNATURE_ERROR = new MtCallbackResponse("sign_error");

    /**
     * 未知错误
     */
    public static MtCallbackResponse UNKNOWN_ERROR = new MtCallbackResponse("unknown_error");

    /**
     * 业务授权解绑回调需要返回code
     */
    public static MtCallbackResponse UNBIND_AUTHORIZATION_SUCCESS = new MtCallbackResponse(0);

    public static MtCallbackResponse UNBIND_AUTHORIZATION_FAIL = new MtCallbackResponse(1);

    public static MtCallbackResponse buildBusinessSuccess(String data){
        return new MtCallbackResponse(data,0,"success");
    }

    public static MtCallbackResponse buildBusinessFail(String message){
        MtCallbackResponse mtCallbackResponse = new MtCallbackResponse();
        mtCallbackResponse.setCode(1);
        mtCallbackResponse.setMessage(message);
        return mtCallbackResponse;
    }
}
