package com.holderzone.saas.store.dto.print.type;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TemplateItemQO implements Serializable {

    private static final long serialVersionUID = 1452641517698871918L;

    @ApiModelProperty(value = "'商品guid'")
    private String itemGuid;

    private String productSpecGuid;

}
