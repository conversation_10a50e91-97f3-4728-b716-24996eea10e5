package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("云后台-企业列表")
public class EnterpriseCountRespDTO {

    @ApiModelProperty(value = "订单数")
    private long dealNum;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal dealPrice;

    public EnterpriseCountRespDTO() {
        this.dealNum = 0;
        this.dealPrice = new BigDecimal(0);
    }
}
