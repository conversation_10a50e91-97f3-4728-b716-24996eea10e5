package com.holderzone.saas.store.dto.weixin.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfoVolumeDetails;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Api("优惠券详情")
@Data
@Builder
public class WxVolumeCodeDTO {
    @ApiModelProperty("会员持卷GUID")
    private String memberVolumeGuid;
    @ApiModelProperty("优惠券详情")
    private ResponseMemberInfoVolumeDetails MemberInfoVolumeDetailsRespDTO;
    @ApiModelProperty("卷模板GUID")
    private String volumeInfoGuid;
    @ApiModelProperty("卷名称")
    private String volumeInfoName;
    @ApiModelProperty("优惠券状态，0可使用的，1已失效的")
    private Integer mayUseVolume;

    @ApiModelProperty("开始有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime volumeStartDate;

    @ApiModelProperty("结束有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime volumeEndDate;

    @ApiModelProperty("会员拥有卷状态,-2置灰未生效,-1置灰生效,0未使用，1，已使用，2，未生效，3，已过期，4已作废")
    private Integer volumeState;
    @ApiModelProperty("卷模板的状态,0未过期(发送中),1已过期(保留状态),2停止发放,3作废")
    private Integer volumeInfoState;
    @ApiModelProperty("卷优惠金额")
    private BigDecimal volumeMoney;
    @ApiModelProperty("卷类型，0代金卷,1折扣卷,2兑换卷")
    private Integer volumeType;
    @ApiModelProperty("使用门槛满,0无限制，1有限制")
    private Integer useThreshold;
    @ApiModelProperty("满多少可用")
    private BigDecimal useThresholdFull;
    @ApiModelProperty("1：选中，0：非选中")
    private Integer uck = 0;
    @ApiModelProperty("优惠券码")
    private String volumeCode;
}

