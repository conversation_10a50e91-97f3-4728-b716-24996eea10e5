package com.holderzone.saas.store.dto.print.raw;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class PrinterItemRawDTO implements Serializable {

    private static final long serialVersionUID = 4495132042816105577L;

    /**
     * 唯一标识
     * todo 告诉DaoDao该Sql列由link_guid变为guid了
     */
    private String guid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 打印机guid
     */
    private String printerGuid;

    /**
     * 菜品guid
     */
    private String itemGuid;

    /**
     * 菜品名称
     */
    private String itemName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}