package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商户后台/数据报表/订单统计/明细信息 退款信息
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
@Data
public class RefundTransactionRecordDetailRespDTO implements Serializable {

    private static final long serialVersionUID = 6401526266952401252L;

    @ApiModelProperty(value = "退款编号")
    private String orderNo;

    @ApiModelProperty(value = "退款金额")
    private String amount;

    @ApiModelProperty(value = "退款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime refundTime;

    @ApiModelProperty(value = "支付方式")
    private String paymentTypeName;

    @ApiModelProperty(value = "退款操作人")
    private String staffName;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;
}
