package com.holderzone.saas.store.dto.takeaway.response;

import lombok.Data;

import java.io.Serializable;


/**
 * 团购订单结算明细
 */
@Data
public class MtCouponTradeDetailRespDTO implements Serializable {

    private static final long serialVersionUID = -651691893156216326L;

    /**
     * 业务系统门店GUID
     */
    private String storeGuid;

    /**
     * 美团门店GUID
     */
    private String mtStoreGuid;

    /**
     * 美团门店名字
     */
    private String mtStoreName;

    /**
     * 商家促销金额
     */
    private Double bizCost;

    /**
     * 券进价，即美团从商家获取的团购券结算批发价，也是开店宝中的“顾客实付”-“服务费”。如果商家有另外的促销（指从开店宝发起的促销），
     * 则美团与商家的结算价格为结算批发价-商家促销金额；如无另外的促销，结算批发价即作为美团与商家的结算价格
     */
    private Double buyPrice;

    /**
     * 券购买价，即用户在购买团购券时所需付的价格，这里指剔除商家促销金额后的价格，即实时价格
     */
    private Double couponBuyPrice;

    /**
     * 券码
     */
    private String couponCode;

    /**
     * 项目ID
     */
    private Integer dealId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 券面值，即人们常说的市场价
     */
    private Double dealValue;

    /**
     * 商家预计应得金额
     */
    private Double due;

    /**
     * 验券时间
     */
    private Long useTime;

    /**
     * 是否量贩：0：不是，1：是
     */
    private Integer volume;

    /**
     * 量贩项目的单张券原价（普通券单张券原价与项目总价相同）
     */
    private Double singleValue;

    /**
     * 券信息
     */
    private MtCouponDetailRespDTO couponDetail;

}
