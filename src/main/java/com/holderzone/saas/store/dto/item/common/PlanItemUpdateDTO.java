package com.holderzone.saas.store.dto.item.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> R
 * @date 2021/3/10 17:04
 * @description
 */
@ApiModel(value = "价格方案中菜品数据更新对象")
@Data
public class PlanItemUpdateDTO {
    @ApiModelProperty(value = "菜谱方案菜谱关系Guid")
    private String planItemGuid;

    @ApiModelProperty(value = "菜品方案guid")
    private String planGuid;

    @ApiModelProperty(value = "商品sku")
    private String itemGuid;

    @ApiModelProperty(value = "商品排序")
    private Integer sort;

    @ApiModelProperty(value = "菜谱方案商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品售价")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "商品会员价")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "商品描述")
    private String description;

    @ApiModelProperty(value = "图片路径数组json")
    private String pictureUrl;
}
