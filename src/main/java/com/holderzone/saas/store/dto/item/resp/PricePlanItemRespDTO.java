package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 方案菜品查询返回
 * @date 2021/6/1 18:07
 */
@ApiModel("方案菜品查询返回")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class PricePlanItemRespDTO {

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "商品名")
    private String itemName;

    @ApiModelProperty(value = "分类guid")
    private String typeGuid;

    @ApiModelProperty(value = "商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品 5.团餐。")
    private Integer itemType;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    private String description;

    /**
     * 图片路径数组json
     */
    @ApiModelProperty(value = "图片路径数组json")
    private String pictureUrl;

    @ApiModelProperty(value = "是否是最新编辑")
    private Boolean newUpdateFlag;

    /**
     * 起卖数(非称重即为整数，称重即为小数)
     */
    @ApiModelProperty(value = "起卖数(非称重即为整数，称重即为小数)")
    private BigDecimal minOrderNum;

    @ApiModelProperty(value = "方案销售价")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "商品原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "方案会员价")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "规格信息--预览菜谱不需要这个")
    private List<PricePlanSkuRespDTO> skuList;
}
