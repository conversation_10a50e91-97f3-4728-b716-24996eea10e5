package com.holderzone.saas.store.dto.report.resp;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ReturnItemDTO implements Serializable {

    private static final long serialVersionUID = 8295434747256392583L;

    @ApiModelProperty(value = "品牌")
    public String brandName;

    public String brandGuid;

    @ApiModelProperty(value = "门店")
    public String storeName;

    public String storeGuid;

    @ApiModelProperty(value = "商品名称")
    public String goodsName;

    @ApiModelProperty(value = "商品guid")
    public String goodsGuid;

    @ApiModelProperty(value = "规格guid")
    public String skuGuid;

    @ApiModelProperty(value = "商品分类")
    public String goodsCategories;

    @ApiModelProperty(value = "退货数量")
    public BigDecimal returnQuantity;

    @ApiModelProperty(value = "退货金额")
    public BigDecimal refund;

    @ApiModelProperty(value = "退货率(%)")
    public String refundRate;
}
