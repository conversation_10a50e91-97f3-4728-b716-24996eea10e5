package com.holderzone.saas.store.dto.item.resp;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemEstimateForAndroidDTO
 * @date 2019/05/16 14:15
 * @description //TODO 安卓菜品估清同步
 * @program holder-saas-aggregation-app
 */
@Data
@ApiModel(value = "安卓菜品估清同步查询DTO")
public class ItemEstimateForAndroidDTO {

    /**
     *sku 业务主键
     */
    @ApiModelProperty(value = "sku 业务主键")
    private String skuGuid;

    /**
     * 是否禁售 1:否 2:是
     */
    @ApiModelProperty(value = "是否禁售 1:否 2:是")
    private Integer isSoldOut = 1;

    /**
     * 是否限量  1：否  2：是
     */
    @ApiModelProperty(value = "是否限量  1：否  2：是")
    private Integer isTheLimit = 1;

    /**
     * 当前剩余数量
     */
    @ApiModelProperty(value = "当前剩余数量")
    private BigDecimal residueQuantity = BigDecimal.ZERO;

    /**
     * 提醒阈值
     */
    @ApiModelProperty(value = "提醒阈值")
    private BigDecimal reminderThreshold = BigDecimal.TEN;


}
