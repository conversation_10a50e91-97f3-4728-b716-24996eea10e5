package com.holderzone.saas.store.dto.business.manage.sync;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AdditionalFeeDO
 * @date 2018/08/02 下午3:00
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "附加费")
public class SurchargeRawDTO implements Serializable {

    @ApiModelProperty(value = "自增id")
    private Long id;

    @ApiModelProperty(value = "附加费guid")
    private String surchargeGuid;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "附加费名称")
    private String name;

    @ApiModelProperty(value = "附加费金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "费用类型（0：按人；1：按桌）")
    private Integer type;

    @ApiModelProperty(value = "是否已启用（0=未启用；1=已启用）")
    private Boolean isEnable;

    @ApiModelProperty(value = "是否已删除（0=未删除；1=已删除）")
    private Boolean isDeleted;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;
}
