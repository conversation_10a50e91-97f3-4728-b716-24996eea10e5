package com.holderzone.saas.store.dto.report.resp;

import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.report.OrderBaseInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderDetailRespDTO
 * @date 2018/09/28 10:33
 * @description 订单详情DTO，包含基本信息，菜品信息，结算信息，优惠信息，支付信息
 * @program holder-saas-store-report
 */
@Data
@ApiModel
public class OrderTransDetailReportRespDTO implements Serializable {

    @ApiModelProperty(value = "订单基本信息")
    private OrderBaseInfoDTO orderBaseInfoDTO;

    @ApiModelProperty(value = "收银员账号")
    private String operationStaffGuid;
    @ApiModelProperty(value = "收银员姓名")
    private String operationStaffName;
    @ApiModelProperty("外卖平台")
    private String takeawayPlat;
    @ApiModelProperty("外卖接单时间")
    private LocalDateTime takeawayReciveOrderTime;

    @ApiModelProperty("菜品合计")
    private BigDecimal totalDishFee;
    @ApiModelProperty("菜品列表，可能包含主、子订单的菜品")
    private List<OrderDishInfo>orderDishs;

    @ApiModelProperty("结算信息")
    private List<OrderCheckOutInfo> OrderCheckOutInfo;

    @ApiModelProperty("优惠合计")
    private BigDecimal totalDiscountFee;
    @ApiModelProperty("优惠列表信息")
    private List<OrderDiscountInfo>orderDiscounts;

    @ApiModelProperty("实收金额")
    private BigDecimal actuallyPayFee;
    @ApiModelProperty("支付列表信息")
    private List<OrderPayInfo>orderPayments;


    @Data
    public static class OrderDishInfo{

        @ApiModelProperty("订单编号")
        private String orderGuid;
        @ApiModelProperty("菜品编号")
        private String code;
        @ApiModelProperty("菜品名称")
        private String dishName;
        @ApiModelProperty("规格")
        private String skuName;
        @ApiModelProperty("菜品售卖价")
        private BigDecimal price;
        @ApiModelProperty("属性小计")
        private BigDecimal skuPropertyTotal;
        @ApiModelProperty("数量")
        private BigDecimal orderCount;
        @ApiModelProperty("菜品小计")
        private BigDecimal dishSubTotal;
        public void calculateDishSubTotal(){
            if(this.skuPropertyTotal != null){
                this.dishSubTotal = this.price.add(this.skuPropertyTotal).multiply(this.orderCount);
            }else{
                this.dishSubTotal = this.price.multiply(this.orderCount);
            }
        }
    }

    @Data
    public static class OrderCheckOutInfo{
        @ApiModelProperty("项目名称")
        private String projectName;
        @ApiModelProperty("子项名称")
        private String subProjectName;
        @ApiModelProperty("金额")
        private BigDecimal price;

    }

    @Data
    public static class OrderDiscountInfo{
        @ApiModelProperty("项目名称")
        private String discountProName;
        @ApiModelProperty("金额")
        private BigDecimal discountFee;
        @ApiModelProperty("备注")
        private String remark;
    }


    @Data
    public static class OrderPayInfo{
        @ApiModelProperty("支付方式")
        private Integer paymentType;
        @ApiModelProperty("支付金额")
        private BigDecimal amount;
        @ApiModelProperty("优惠金额")
        private BigDecimal discountFee;
        @ApiModelProperty("第三方支付流水号")
        private String thirdOrderNo;
        @ApiModelProperty("备注")
        private String remark;

    }
}
