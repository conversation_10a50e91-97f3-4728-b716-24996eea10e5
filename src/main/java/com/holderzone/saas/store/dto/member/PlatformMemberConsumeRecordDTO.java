package com.holderzone.saas.store.dto.member;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023-08-30
 * @description
 */
@Data
public class PlatformMemberConsumeRecordDTO implements Serializable {

    private static final long serialVersionUID = -4259983891213482465L;

    /**
     * USER_POINT_EVENT：会员积分事件
     */
    private String eventType;

    /**
     * 具体信息
     */
    private EventDetail eventDetail;

    @Data
    public static class EventDetail implements Serializable {

        private static final long serialVersionUID = 6686875451233065981L;

        /**
         * 用户id
         */
        private String userOpenId;

        /**
         * 手机号
         * 通过 https://api-open-cater.meituan.com/jmcard/members/query获取
         */
        private String phone;

        /**
         * 企业guid
         * 通过查询授权获取
         */
        private String enterpriseGuid;

        /**
         * 运营主体guid
         * 通过查询授权获取
         */
        private String operSubjectGuid;

        /**
         * MEI_TUAN ：美团
         * DIAN_PING：点评
         */
        private String platformType;

        /**
         * 积分数据
         */
        private PointItem pointInfo;


        @Data
        public static class PointItem implements Serializable {

            private static final long serialVersionUID = -92066928586322223L;

            /**
             * 积分唯一流水id
             */
            private String pointItemId;

            /**
             * 业务类型
             * MAIDAN（买单）,TUAN（团购）
             */
            private String bizType;

            /**
             * 业务产品id
             * 产品id。团购dealid，买单id等
             */
            private String productId;

            /**
             * 业务产品名称
             * 如deal 名称
             */
            private String productName;

            /**
             * 积分操作类型
             * ADD（增加积分），REDUCE（扣减积分）
             */
            private String opType;

            /**
             * 促销类型
             * NEW_GIFT（新人礼）
             */
            private String promoType;

            /**
             * 用户支付金额
             * 精确到分，单位：元
             */
            private BigDecimal money;

            /**
             * 交易时间
             * 精确到秒，格式:"YYYY-MM-DD hh:mm:ss"
             */
            private String businessTime;

            /**
             * 核销关联ID
             * 用于关联一次核销的唯一编号。「核销」和「撤销核销/已消费退」的编号一致
             */
            private String consumeAssociationId;
        }
    }

}
