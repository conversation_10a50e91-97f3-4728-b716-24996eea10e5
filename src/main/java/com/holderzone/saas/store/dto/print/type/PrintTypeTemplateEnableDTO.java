package com.holderzone.saas.store.dto.print.type;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2024/1/23
 * @description 启用/禁用请求
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "启用/禁用请求", description = "启用/禁用请求")
public class PrintTypeTemplateEnableDTO implements Serializable {

    private static final long serialVersionUID = 2597145910956480302L;

    @NotBlank(message = "模版Guid不得为空")
    @ApiModelProperty(value = "模版Guid", required = true)
    private String templateGuid;

    @NotNull(message = "是否启用不得为空")
    @ApiModelProperty(value = "是否启用：0=禁用，1=启用。", required = true)
    private Boolean isEnable;

}
