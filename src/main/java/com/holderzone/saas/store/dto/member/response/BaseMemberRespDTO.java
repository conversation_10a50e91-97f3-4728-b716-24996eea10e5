package com.holderzone.saas.store.dto.member.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseMemberRespDTO
 * @date 2018/09/16 下午9:43
 * @description //TODO
 * @program holder-saas-config-center
 */
@Data
public class BaseMemberRespDTO {

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "业务主键")
    private String memberGuid;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "性别 0：女，1:男，2：保密")
    private Byte sex;

    @ApiModelProperty(value = "会员账号")
    private String accountNumber;

    @ApiModelProperty(value = "生日")
    private LocalDate birthday;

    @ApiModelProperty(value = "会员等级名称")
    private String memberGradeName;

    @ApiModelProperty(value = "会员电子卡号")
    private String cardNum;

    @ApiModelProperty(value = "剩余积分")
    private Integer residualIntegral;

    @ApiModelProperty(value = "账户余额")
    private BigDecimal balance;

    @ApiModelProperty(value = "充值规则 0：不可充值，1:定额充值，2：不定额充值")
    private Byte prepaidRule;

    @ApiModelProperty(value = "定额充值额度（JsonArray）")
    private List prepaidLimit;
}
