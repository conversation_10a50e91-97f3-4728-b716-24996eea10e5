package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.base.TradeModeAware;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 称重标签单
 *
 * <AUTHOR>
 * @date 2018/09/19 16:23
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "称重标签单")
public class PrintItemLabelDTO extends PrintBaseItemDTO implements TradeModeAware {

    @NotBlank(message = "商品名称")
    @ApiModelProperty(value = "商品名称", required = true)
    private String itemName;

    @NotBlank(message = "编号不能为空")
    @ApiModelProperty(value = "编号", required = true)
    private String serialNumber;

    @NotNull(message = "数量")
    @ApiModelProperty(value = "数量", required = true)
    private BigDecimal currentCount;

    @NotNull(message = "单位")
    @ApiModelProperty(value = "单位", required = true)
    private String unit;

    @NotNull(message = "价格")
    @ApiModelProperty(value = "价格", required = true)
    private BigDecimal itemPrice;
}
