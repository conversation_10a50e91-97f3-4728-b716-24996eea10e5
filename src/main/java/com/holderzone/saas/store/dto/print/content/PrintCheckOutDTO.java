package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.dto.print.content.base.TradeModeAware;
import com.holderzone.saas.store.dto.print.content.nested.MultiMemberPayRecord;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

import static com.holderzone.saas.store.dto.print.util.PrintMockUtils.*;

/**
 * 结账单
 *
 * <AUTHOR>
 * @date 2018/09/19 9:27
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("结账单")
public class PrintCheckOutDTO extends PrintPreCheckoutDTO implements PrintDataMockito, TradeModeAware {

    @NotNull(message = "结帐时间不能为空")
    @ApiModelProperty(value = "结帐时间", required = true)
    private Long checkOutTime;

    @Valid
    @NotEmpty(message = "支付方式不能为空")
    @ApiModelProperty(value = "支付方式", required = true)
    private List<PayRecord> payRecordList;

    @NotNull(message = "找零不能为空")
    @ApiModelProperty(value = "找零", required = true)
    private BigDecimal changedPay;

    @NotNull(message = "实付金额不能为空")
    @ApiModelProperty(value = "实付金额", required = true)
    private BigDecimal actuallyPay;

    @NotNull(message = "交易模式不得为空")
    @ApiModelProperty(value = "交易模式", required = true)
    private Integer tradeMode;

    @ApiModelProperty(value = "会员姓名", required = true)
    private String memberName;

    @ApiModelProperty(value = "会员手机号", required = true)
    private String memberPhone;

    @ApiModelProperty(value = "支付卡余额")
    private BigDecimal memberCardBalance;

    @ApiModelProperty(value = "会员本次消费实充金额")
    private BigDecimal memberRechargeAmount;

    @ApiModelProperty(value = "会员本次消费赠送金额")
    private BigDecimal memberGiftAmount;

    @ApiModelProperty(value = "会员本次消费补贴金额")
    private BigDecimal memberSubsidyAmount;

    @ApiModelProperty(value = "会员多卡/单卡支付信息")
    private List<MultiMemberPayRecord> multiMemberPayRecords;

    @ApiModelProperty(value = "挂账单位名称")
    private String debtUnitName;

    @ApiModelProperty(value = "挂账联系人")
    private String debtContactName;

    @ApiModelProperty(value = "挂账单位电话")
    private String debtContactTel;

    @ApiModelProperty(value = "多单结账其他子单信息")
    private List<PrintCheckOutDTO> otherPrintCheckOuts;

    @ApiModelProperty(value = "开票二维码", required = true)
    private String invoiceCode;

    @Override
    public void applyMock() {
        super.applyMock();
        setCheckOutTime(mockCheckoutTime());
        setChangedPay(mockPayChanged());
        setActuallyPay(mockActuallyPay());
        setPayRecordList(mockPayRecords());
        setTradeMode(0);
        setMemberName(mockMemberName());
        setMemberPhone(mockMemberPhone());
        setMultiMemberPayRecords(mockMemberPayAmountRecords());
        setDebtUnitName(mockDebtUnitName());
        setDebtContactName(mockDebtContactName());
        setDebtContactTel(mockDebtContactTel());
    }
}
