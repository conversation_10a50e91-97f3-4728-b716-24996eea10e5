package com.holderzone.saas.store.dto.report.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/6
 * @description 销售明细查询
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "销售明细查询", description = "销售明细查询")
public class SalesDetailQO implements Serializable {

    private static final long serialVersionUID = 715607663254027031L;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @ApiModelProperty(value = "结账时间")
    @NotNull(message = "结账时间不能为空")
    private LocalDate startTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @ApiModelProperty(value = "结账时间")
    @NotNull(message = "结账时间不能为空")
    private LocalDate endTime;

    @ApiModelProperty(value = "门店guid")
    private List<String> storeGuidList;

    @ApiModelProperty(value = "商品类型（0.全部 1.套餐商品，2.普通商品，3.称重商品，4.宴会套餐）")
    private Integer itemType;

    @ApiModelProperty(value = "订单操作（0：全部， 1：点菜， 2：赠菜，3：退菜）")
    private Integer state;

    @ApiModelProperty(value = "页数")
    private Integer currentPage;

    @ApiModelProperty(value = "条数")
    private Integer pageSize;
}
