package com.holderzone.saas.store.dto.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggRefundPollingRespDTO
 * @date 2019/03/15 15:03
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class AggRefundPollingRespDTO extends BaseCallBackDTO{

    /**
     * 应答码 10000代表成功，其他参考文档（是）
     */
    @ApiModelProperty(value = "应答码 10000代表成功，其他参考文档（是）")
    private String code;

    /**
     * 应答信息（是）
     */
    @ApiModelProperty(value = "应答信息")
    private String msg;

    /**
     * 商户订单号，透传字段（否）
     * 等于orderHolderNo
     */
    @ApiModelProperty(value = "商户订单号，透传字段（否），等于orderHolderNo")
    private String mchntOrderNo;

    /**
     * 第三方平台订单号，透传（否）
     */
    @ApiModelProperty(value = "第三方平台订单号，透传（否）")
    private String orderNo;

    /**
     * 商户ID（是）
     */
    @ApiModelProperty(value = "商户ID（是）")
    private String mchntId;

    /**
     * 签名（是）
     */
    @ApiModelProperty(value = "签名（是）")
    private String signature;

    /**
     * 坑爹银行返回的是字符串，不是json对象，不能直接转为集合。先用字符串接受
     */
    @ApiModelProperty(value = "坑爹银行返回的是字符串，不是json对象，不能直接转为集合。先用字符串接受")
    private String refundOrderList;

    /**
     * 商户透传字段
     */
    @ApiModelProperty(value = "商户透传字段")
    private String attachData;

    @ApiModelProperty(value = "退款详情")
    private List<AggRefundDetailRespDTO> refundOrderDetial;

    public static AggRefundPollingRespDTO errorResp(String code, String msg) {
        AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode(code);
        aggRefundPollingRespDTO.setMsg(msg);
        return aggRefundPollingRespDTO;
    }

    public static AggRefundPollingRespDTO errorResp(String code, String msg, String attachData) {
        AggRefundPollingRespDTO aggRefundPollingRespDTO = errorResp(code, msg);
        aggRefundPollingRespDTO.setAttachData(attachData);
        return aggRefundPollingRespDTO;
    }

}
