package com.holderzone.saas.store.dto.reserve;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/4
 * @description 更新订单预定信息
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "更新订单预定信息")
public class UpdateOrderReserveReqDTO implements Serializable {

    private static final long serialVersionUID = -7739035031421813301L;

    @ApiModelProperty("订单guid")
    private String orderGuid;

    @ApiModelProperty("预定guid")
    private String reserveGuid;

    @ApiModelProperty("预付金额")
    private BigDecimal reserveAmount;

    @ApiModelProperty("门店guid")
    private String storeGuid;

}
