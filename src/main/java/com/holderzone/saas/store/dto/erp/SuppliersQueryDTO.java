package com.holderzone.saas.store.dto.erp;

import com.holderzone.saas.store.dto.common.BasePageDTO;

/**
 * <AUTHOR>
 * @className SuppliersQueryDTO
 * @date 2019-04-26 14:25:41
 * @description
 * @program holder-saas-store-dto
 */
public class SuppliersQueryDTO extends BasePageDTO {

    private String searchConditions;
    private String foreignKey;
    private Integer enabled = -1;

    public String getSearchConditions() {
        return searchConditions;
    }

    public void setSearchConditions(String searchConditions) {
        this.searchConditions = searchConditions;
    }

    public String getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(String foreignKey) {
        this.foreignKey = foreignKey;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }
}
