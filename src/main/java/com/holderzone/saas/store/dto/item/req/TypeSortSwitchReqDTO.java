package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeSortReqDTO
 * @date 2020年7月25日
 * @description //交换分类排序
 * @program holder
 */

@Data
@ApiModel(value = "交换分类排序")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TypeSortSwitchReqDTO {

    @NotBlank(message = "源分类Guid不能为空")
    @ApiModelProperty(value = "源分类Guid")
    private String sourceTypeGuid;

    @NotBlank(message = "目标分类Guid不能为空")
    @ApiModelProperty(value = "目标分类Guid")
    private String targetTypeGuid;


}
