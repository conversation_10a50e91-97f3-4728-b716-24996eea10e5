package com.holderzone.saas.store.dto.weixin.req;

import com.holderzone.saas.store.dto.weixin.WxStoreCartItemDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;

@Data
@ApiModel("用户与商品项请求对象")
public class WxStoreConsumerCartItemReqDTO {
    @Valid
    @ApiModelProperty(value = "顾客封装对象", required = true)
    private WxStoreConsumerDTO wxStoreConsumerDTO;

    @Valid
    @ApiModelProperty(value = "商品项封装对象", required = true)
    private WxStoreCartItemDTO wxStoreCartItemDTO;
}
