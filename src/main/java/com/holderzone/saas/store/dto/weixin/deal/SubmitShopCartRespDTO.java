package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(value = "提交购物车返回")
@Accessors(chain = true)
public class SubmitShopCartRespDTO {

	@ApiModelProperty(value = "0：成功，1：失败")
	private Integer code=0;

	@ApiModelProperty(value = "错误信息")
	private String errorMsg;

	@ApiModelProperty(value = "购物车")
	private ShopCartRespDTO shopCartRespDTO;

	public static SubmitShopCartRespDTO noItem(){
		return new SubmitShopCartRespDTO().setCode(1).setErrorMsg("没有商品，无法添加购物车");
	}

	public static SubmitShopCartRespDTO estimate(String errorMsg) {
		return new SubmitShopCartRespDTO().setCode(1).setErrorMsg(errorMsg);
	}

	public static SubmitShopCartRespDTO closeStore(){
		return new SubmitShopCartRespDTO().setCode(1).setErrorMsg("门店正在休息中，无法提交购物车");
	}
}
