package com.holderzone.saas.store.dto.item.resp;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SkuSynRespDTO
 * @date 2019/01/03 下午2:57
 * @description //安桌端同步商品规格的返回实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@JsonIgnoreProperties(ignoreUnknown = true)
public class SkuSynRespDTO implements Serializable {
    private static final long serialVersionUID = 1567286740443940790L;

    private Long id;

    private String itemGuid;
    @ApiModelProperty(value = "规格GUID", required = true)
    private String skuGuid;

    @ApiModelProperty(value = "maybe null,upc")
    private String upc;

    @ApiModelProperty(value = "maybe null,当日库存")
    private Integer stock;

    @ApiModelProperty(value = "是否开启库存（0：否 ， 1：是）")
    private Integer isOpenStock;

    @ApiModelProperty(value = "maybe null,规格名称")
    private String name;

    @ApiModelProperty(value = "售卖价格", required = true)
    private BigDecimal salePrice;

    @ApiModelProperty(value = "成本价格")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "商品毛利率")
    private BigDecimal itemGrossMargin;

    @ApiModelProperty(value = "会员价格")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "外卖价格")
    private BigDecimal takeawayPrice;

    @ApiModelProperty(value = "外卖会员价格")
    private BigDecimal takeawayMemberPrice;

    @ApiModelProperty(value = "外卖打包费（冗余小程序端字段）")
    private BigDecimal takeawayPackageFee;

    @ApiModelProperty(value = "商城打包费（冗余小程序端字段）")
    private BigDecimal mallPackageFee;

    @ApiModelProperty(value = "是否加入整单折扣(0：否，1：是)", required = true)
    private Integer isWholeDiscount;

    @ApiModelProperty(value = "是否参与会员折扣（0：否，1：是）", required = true)
    private Integer isMemberDiscount;

    @ApiModelProperty(value = "起卖数(非称重即为整数，称重即为小数)", required = true)
    private BigDecimal minOrderNum;

    @ApiModelProperty(value = "计数单位", required = true)
    private String unit;

    @ApiModelProperty(value = "maybe null,编号")
    private String code;

    @ApiModelProperty(value = "是否参与微信点餐（0：否，1：是）", required = true)
    private Integer isJoinWeChat;

    @ApiModelProperty(value = "是否参与小程序商城（冗余小程序端字段）")
    private Integer isJoinMiniAppMall;

    @ApiModelProperty(value = "是否参与小程序外卖（冗余小程序端字段）")
    private Integer isJoinMiniAppTakeaway;

    @ApiModelProperty(value = "是否支持堂食（冗余小程序端字段）")
    private Integer isJoinStore;

    @ApiModelProperty(value = "小程序端折扣（冗余小程序端字段）")
    private double discount;

    @ApiModelProperty(value = "月销售量（冗余小程序端字段）")
    private Integer monthlySale;

    @ApiModelProperty(value = "sku（冗余小程序端字段）")
    private String sku;

    @ApiModelProperty(value = "是否上下架")
    private Integer isRack;

    @ApiModelProperty(value = "是否上架一体机（0：否，1：是）")
    private Integer isJoinAio;

    @ApiModelProperty(value = "是否上架POS机（0：否，1：是）")
    private Integer isJoinPos;

    @ApiModelProperty(value = "是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;

    /**
     * 核算价
     */
    @ApiModelProperty("堂食核算价")
    private BigDecimal accountingPrice;

    @ApiModelProperty("外卖核算价")
    private BigDecimal takeawayAccountingPrice;


    /**
     * 划线价
     */
    @ApiModelProperty("划线价")
    private BigDecimal linePrice;

    /**
     * 积分兑换值
     */
    @ApiModelProperty(value = "积分兑换值")
    private Integer integralConversionValue;

    /**
     * 金额兑换值
     */
    @ApiModelProperty(value = "金额兑换值")
    private BigDecimal integralConversionMoney;

    /**
     * 品牌库对应的skuGuid：如果是自己创建的内容，则此字段为skuGuid，如果是被推送过来的商品，则该字段为原skuGuid。
     */
    @ApiModelProperty(value = "品牌库对应的skuGuid：如果是自己创建的内容，则此字段为skuGuid，如果是被推送过来的商品，则该字段为原skuGuid")
    private String parentGuid;
}
