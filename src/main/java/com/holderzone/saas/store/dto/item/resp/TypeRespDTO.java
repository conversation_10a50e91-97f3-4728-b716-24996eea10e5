package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeRespDTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "分类的查询结果实体")
public class TypeRespDTO implements Serializable {
    /**
     * typeGUID
     */
    @ApiModelProperty(value = "分类GUID（唯一标识）")
    private String typeGuid;

    /**
     * 菜单类别名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 门店GUID
     */
    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 是否启用（0：否，1：是）
     */
    @ApiModelProperty(value = "是否启用（0：否，1：是）")
    private Integer isEnable;

    /**
     * 下架商品数量
     */
    @ApiModelProperty(value = "下架商品数量")
    private Integer unRackItemNum;

    @ApiModelProperty(value = "type类型：（0：门店自己创建的type，1：品牌自己创建的type,2:被推送过来的type）")
    private Integer typeFrom;

    /**
     * 菜谱方案GUID
     */
    @ApiModelProperty(value = "菜谱方案GUID")
    private String pricePlanGuid;
}
