package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Api("优惠券适用门店")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class WxApplicableProductStores {

	@ApiModelProperty("门店名称")
	private String storeName;

	@ApiModelProperty("0:全部商品,1:部分商品")
	private Integer allSupport;

	@ApiModelProperty("门店下商品集合")
	private List<String> productNameList;
}
