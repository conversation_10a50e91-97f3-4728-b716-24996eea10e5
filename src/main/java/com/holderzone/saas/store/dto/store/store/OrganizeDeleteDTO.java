package com.holderzone.saas.store.dto.store.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreCreateDTO
 * @date 2018/07/23 下午4:27
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class OrganizeDeleteDTO implements Serializable {

    private static final long serialVersionUID = -1734600081817198072L;

    /**
     * 组织guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "组织guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "组织guid", required = true)
    private String organizationGuid;

    /**
     * 父级组织的guid
     */
    @NotBlank
    @Size(min = 1, max = 45, message = "父级组织的guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "父级组织的guid", required = true)
    private String parentOrganizationGuid;

    /**
     * 组织类型。1=品牌，2=区域，3=门店。
     */
    @NotNull
    @Min(value = 1, message = "是否启用不得为空，且范围是[1-3]，1=品牌，2=区域，3=门店")
    @Max(value = 3, message = "是否启用不得为空，且范围是[1-3]，1=品牌，2=区域，3=门店")
    @ApiModelProperty(value = "组织类型。1=品牌，2=区域，3=门店。", required = true)
    private Integer organizationType;
}
