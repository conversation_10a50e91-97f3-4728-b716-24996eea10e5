package com.holderzone.saas.store.dto.trade.req;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description pad下单请求实体
 * @date 2021/8/20 10:47
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("pad下单请求实体")
public class PadOrderPlacementReqDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -3020371676679249509L;

    @ApiModelProperty(value = "订单的guid")
    private String orderGuid;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "桌台guid")
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台名字")
    @NotBlank(message = "桌台名字不能为空")
    private String diningTableName;

    @ApiModelProperty(value = "AreaGuid")
    private String areaGuid;

    @ApiModelProperty(value = "桌台区域名称")
    @NotBlank(message = "桌台区域名称不能为空")
    private String areaName;

    @ApiModelProperty(value = "就餐人数")
    private Integer guestCount;

    @ApiModelProperty("订单总价")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "商品")
    private List<PadDineInItemDTO> dineInItemDTOList;

}
