package com.holderzone.saas.store.dto.config.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateConfigReqDTO
 * @date 2019/05/15 18:00
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
@ApiModel(value = "云呼电话DTO")
public class ReservePhoneReqDTO {

    @Nullable
    @ApiModelProperty(value = "唯一Guid")
    private String guid;

    @NotBlank(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @Nullable
    @ApiModelProperty(value = "电话号码")
    private String phone;
}
