package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @className PadPictureDTO
 * @date 2021/7/21
 * @description 属性值保存DTO
 * @program hsi_item_pad_picture
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "pad点餐图片请求DTO")
public class PadPictureDTO extends PageDTO {

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "菜品guid")
    private String itemGuid;

    @ApiModelProperty(value = "分类guid")
    private String typeGuid;

    @ApiModelProperty(value = "菜谱方案guid")
    private String planGuid;

    @ApiModelProperty(value = "小图")
    private String smallPicture;

    @ApiModelProperty(value = "大图")
    private String bigPicture;

    @ApiModelProperty(value = "竖图")
    private String verticalPicture;

    @ApiModelProperty(value = "详情大图")
    private String detailPicture;

    @ApiModelProperty(value = "菜品名字搜索")
    private String keywords;

    @ApiModelProperty(value = "门店")
    private String storeGuid;

    @ApiModelProperty(value = "分类guid列表,用于查询的字段")
    private List<String> typeGuidList;
}
