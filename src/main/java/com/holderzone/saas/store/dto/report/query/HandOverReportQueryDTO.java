package com.holderzone.saas.store.dto.report.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class HandOverReportQueryDTO implements Serializable {

    private static final long serialVersionUID = 677425935060880408L;

    @ApiModelProperty(value = "交接班状态 -1:全部 0:未交班 1:已交班")
    private Integer state = -1;

    @NotBlank(message = "门店guid不能为空")
    @ApiModelProperty("门店guid")
    private String storeGuid;

    @NotNull(message = "查询开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("查询开始时间")
    private LocalDateTime businessStartDateTime;

    @NotNull(message = "查询结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("查询结束时间")
    private LocalDateTime businessEndDateTime;

    @ApiModelProperty(value = "操作人guids")
    private List<String> userGuids;
}
