package com.holderzone.saas.store.dto.table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableCombineRespDTO
 * @date 2019/11/26 10:46
 * @description //TODO
 * @program IdeaProjects
 */
@Data
public class TableCombineRespDTO {

    @ApiModelProperty("并台失败的桌台号")
    private List<String> failTableArray;

    @ApiModelProperty("成功的集合")
    private Map<String,String> successResult;
}