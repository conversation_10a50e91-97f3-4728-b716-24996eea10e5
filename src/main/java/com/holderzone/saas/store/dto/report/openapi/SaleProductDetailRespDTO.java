package com.holderzone.saas.store.dto.report.openapi;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 销售商品明细响应
 */
@Data
public class SaleProductDetailRespDTO implements Serializable {

    private static final long serialVersionUID = -1025167689243809355L;

    /**
     * 序号
     */
    private Integer index;

    /**
     * 订单guid
     */
    @JsonIgnore
    private String orderGuid;

    /**
     * 下菜时间
     */
    @JsonIgnore
    private LocalDateTime gmtCreate;

    /**
     * 商品唯一标识
     */
    private String skuGuid;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品分类编码
     */
    private String itemTypeGuid;

    /**
     * 商品分类名称
     */
    private String itemTypeName;

    /**
     * 商品单价
     */
    private Long price;

    /**
     * 商品数量
     */
    private Long currentCount;

    /**
     * 商品总价
     */
    private Long totalPrice;

    /**
     * 优惠金额
     */
    private Long discountFee;

    /**
     * 实收金额
     */
    private Long actuallyPayFee;


}
