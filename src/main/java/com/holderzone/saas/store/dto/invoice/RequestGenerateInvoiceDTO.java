package com.holderzone.saas.store.dto.invoice;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@ApiModel(description = "生成开票")
@Data
public class RequestGenerateInvoiceDTO implements Serializable {

    private static final long serialVersionUID = -8223007489421054806L;

    @ApiModelProperty(value = "account")
    private String account;

    @ApiModelProperty(value = "accountName")
    private String accountName;

    @ApiModelProperty(value = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "门店")
    private String storeGuid;

    @ApiModelProperty(value = "开票金额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "回调地址")
    private String callbackUrl;

}
