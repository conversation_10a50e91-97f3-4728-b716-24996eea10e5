package com.holderzone.saas.store.dto.report.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayDetailRespDTO
 * @date 2018/09/29 16:49
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class PayDetailRespDTO implements Serializable {

    @ApiModelProperty(value = "订单号")
    private String orderGuid;

    @ApiModelProperty(value = " 支付方式name")
    private String paymentName;

    @ApiModelProperty(value = "第三方交易流水")
    private String thirdOrderNo;

    @ApiModelProperty(value = "交易流水")
    private String billGuid;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "结账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkTime;

    @ApiModelProperty(value = "门店名字")
    private String storeName;

    @ApiModelProperty(value = "销售类型")
    private String tradeMode;

    @ApiModelProperty(value = "员工guid")
    private String staffGuid;

    @ApiModelProperty(value = "员工Name")
    private String staffName;

    @ApiModelProperty(value = "员工姓名/账号")
    private String staffNameAndGuid;

    public String getStaffNameAndGuid() {
        return this.staffName + "/" + this.staffGuid;
    }

}
