package com.holderzone.saas.store.dto.order.response.dinein;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReturnItemDTO
 * @date 2019/01/17 14:44
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class ReturnItemDTO implements Serializable {

    private static final long serialVersionUID = 7890656522084036729L;

    @ApiModelProperty(value = "itemGuid")
    private String guid;

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "标记退货是否为赠送（0：否，1：是）")
    private Integer isFree;

    @ApiModelProperty(value = "数量")
    private BigDecimal count;

    @ApiModelProperty(value = "退货价格小计")
    private BigDecimal itemPrice;

    @ApiModelProperty(value = "商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂 ，8.已上菜 )")
    private Integer itemState;

    @ApiModelProperty(value = "操作人guid")
    private String staffGuid;

    @ApiModelProperty(value = "操作人名字")
    private String staffName;

    @ApiModelProperty(value = "商品")
    private DineInItemDTO dineInItemDTO;

    //辅助字段
    @JsonIgnore
    private transient String orderItemGuid;
}
