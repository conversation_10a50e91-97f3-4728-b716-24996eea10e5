package com.holderzone.saas.store.dto.order.response;

import com.holderzone.saas.store.dto.trade.OrderTableGuestDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OrderTableBillDTO implements Serializable {

    private static final long serialVersionUID = -2546600589223154067L;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "桌台guid")
    private String tableGuid;

    /**
     * 桌台信息
     */
    private List<OrderTableGuestDTO> orderTableGuestDTOS;


    /**
     * 0 单台 1 联台 2 并台
     */
    private Integer orderTableType;
}
