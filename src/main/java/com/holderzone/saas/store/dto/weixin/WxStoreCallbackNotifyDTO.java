package com.holderzone.saas.store.dto.weixin;

import com.holderzone.saas.store.dto.pay.SaasNotifyDTO;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreCallbackNotifyDTO
 * @date 2019/4/22
 */
@Data
@AllArgsConstructor
public class WxStoreCallbackNotifyDTO {

	String storeGuid;
	String openId;
	String allianceId;

	SaasNotifyDTO saasNotifyDTO;
}
