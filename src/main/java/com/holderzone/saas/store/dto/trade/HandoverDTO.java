package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverDTO
 * @date 2019/04/08 14:47
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
public class HandoverDTO {

    @ApiModelProperty("操作人guid")
    private String userGuid;

    @ApiModelProperty(value = "操作人name")
    private String userName;

    @ApiModelProperty(value = "店铺Guid")
    private String storeGuid;

    @ApiModelProperty(value = "付款类型")
    private Integer paymentType;

    @ApiModelProperty(value = "付款类型名称")
    private String paymentName;

    @ApiModelProperty(value = "付款类型消费总金额数")
    private BigDecimal amount;

    @ApiModelProperty(value = "超出金额")
    private BigDecimal excessAmount;

    @ApiModelProperty(value = "付款类型总订单数")
    private Integer sumItemOrder;

}
