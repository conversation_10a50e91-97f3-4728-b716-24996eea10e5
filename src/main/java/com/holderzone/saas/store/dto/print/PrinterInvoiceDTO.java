package com.holderzone.saas.store.dto.print;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("打印机所关联的单据")
public class PrinterInvoiceDTO {

    @ApiModelProperty(value = "单据类型")
    private Integer invoiceType;

    @ApiModelProperty(value = "单据名称")
    private String invoiceName;

    @ApiModelProperty(value = "打印次数； 新增必传； 修改选传", example = "1")
    private Integer printCount;
}
