package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;

@Data
@ApiModel("商品条码")
@EqualsAndHashCode(callSuper = true)
public class ItemBarCodeReqDTO extends BaseDTO {

    @NotBlank(message = "商品guid不能为空")
    @ApiModelProperty(value = "商品guid", required = true)
    private String guid;

    @NotBlank(message = "商品名称不能为空")
    @ApiModelProperty(value = "商品名称", required = true)
    private String itemName;

    @ApiModelProperty(value = "商品名称", required = true)
    private String skuName;

    @NotNull(message = "数量不能为空")
    @ApiModelProperty(value = "数量", required = true)
    private BigDecimal currentCount;

    @NotNull(message = "小计不能为空")
    @ApiModelProperty(value = "小计", required = true)
    private BigDecimal itemPrice;
}
