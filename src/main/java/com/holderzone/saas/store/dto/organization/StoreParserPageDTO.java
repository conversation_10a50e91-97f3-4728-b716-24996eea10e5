package com.holderzone.saas.store.dto.organization;

import com.holderzone.framework.util.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className QueryBrandDTO
 * @date 19-1-3 上午10:38
 * @description
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("门店列表及门店条件匹配DTO")
public class StoreParserPageDTO extends Page {

    @ApiModelProperty(value = "门店guid集合")
    private List<String> storeGuidList;

    @ApiModelProperty(value = "省或市或区编码集合")
    private List<String> regionCodeList;

    @ApiModelProperty(value = "品牌guid集合")
    private List<String> brandGuidList;

    @ApiModelProperty(value = "组织guid集合")
    private List<String> organizationGuidList;

    /**
     * 规则类型 0显示批次 1菜品汇总
     */
    @ApiModelProperty(value = "规则类型 0显示批次 1菜品汇总")
    private Integer ruleType;

    /**
     * kds显示规则guid
     */
    @ApiModelProperty(value = "显示规则guid")
    private String ruleGuid;
}
