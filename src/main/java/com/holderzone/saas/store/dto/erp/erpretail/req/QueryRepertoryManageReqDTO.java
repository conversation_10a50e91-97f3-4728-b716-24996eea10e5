package com.holderzone.saas.store.dto.erp.erpretail.req;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/10/24 下午 14:32
 * @description
 */
@ApiModel("查询库存管理请求实体")
@Data
@EqualsAndHashCode
public class QueryRepertoryManageReqDTO extends BasePageDTO {

    @ApiModelProperty("单据状态,0:全部，1：已完成，2：已作废")
    @NotEmpty(message = "单据状态不能为空")
    private String status;

    @ApiModelProperty("单据类型")
    @NotNull(message = "单据类型不能为空")
    private int invoiceType;

    @ApiModelProperty("单据编号")
    private String guid;

    @ApiModelProperty("查询出库/入库。0：入库，1：出库")
    @NotNull(message = "出入库类型不得为空")
    private int inOut;

}
