package com.holderzone.saas.store.dto.trade.resp.adjust;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 调整单-外卖订单实体
 * @date 2022/1/17 16:57
 * @className: AdjustByTakeawayOrderRespDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "调整单-外卖订单实体")
public class AdjustByTakeawayOrderRespDTO implements Serializable {

    private static final long serialVersionUID = 1176917647035404881L;

    @ApiModelProperty(value = "订单来源：0=美团  1=饿了么  2=百度  3=京东 4=滴滴  5=自营（自营外卖平台） 6=赚餐")
    private Integer orderSubType;

    @ApiModelProperty(value = "订单创建（下单）时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "餐号")
    private String orderDaySn;

    /**
     * 顾客手机号，字符串数组，线上有多个手机号的情况
     */
    @ApiModelProperty(value = "顾客手机号，字符串数组，线上有多个手机号的情况")
    private String customerPhone;

    @ApiModelProperty(value = "隐私号", hidden = true)
    private String privacyPhone;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "订单状态 -1：已取消，0：待处理，10：已接单/待配送，20：订单配送，100：已完成")
    private Integer orderStatus;

    /**
     * 订单guid
     */
    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "订单编号")
    private String orderViewId;

    @ApiModelProperty(value = "是否调整过定单")
    private Integer adjustState;
}
