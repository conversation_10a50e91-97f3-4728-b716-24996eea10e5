package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@ApiModel("查询赚餐商品返回")
@Accessors(chain = true)
public class TcdItemMappingQueryRespDTO extends TcdCommonRespDTO {

    @ApiModelProperty(value = "菜品列表")
    private List<TcdItemMappingRespDTO> data;

}
