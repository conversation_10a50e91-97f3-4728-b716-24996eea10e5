package com.holderzone.saas.store.dto.order.request.item;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/19
 * @description 更新订单商品信息
 */
@Data
public class UpdateOrderItemInfoDTO implements Serializable {

    private static final long serialVersionUID = 8680115420106582204L;

    @ApiModelProperty("订单guid")
    private String orderGuid;

    private List<DineInItemDTO> allItems;
}
