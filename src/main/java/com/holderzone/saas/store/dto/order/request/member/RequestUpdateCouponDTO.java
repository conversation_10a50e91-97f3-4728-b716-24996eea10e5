package com.holderzone.saas.store.dto.order.request.member;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * A DTO for the {@link com.hpkj.island.domain.IslandRedeemCodeActive} entity.
 */
@ApiModel(description = "优惠更新关联关系")
@Data
public class RequestUpdateCouponDTO implements Serializable {
    private String enterpriseGuid;

    private String operSubjectGuid;

    private List<String> volumeCode;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "手机号")
    private String phoneNum;

    @ApiModelProperty(value = "memberInfoGuid")
    private String memberInfoGuid;

    @ApiModelProperty(value = "会员名称")
    private String memberName;

    @ApiModelProperty(value = "来源")
    private Integer source;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "是否登录 1 是 0 否")
    private Boolean loginType;
}
