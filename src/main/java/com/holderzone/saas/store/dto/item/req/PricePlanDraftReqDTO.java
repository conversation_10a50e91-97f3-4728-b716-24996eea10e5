package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> R
 * @date 2021/3/16 10:39
 * @description
 */
@ApiModel(value = "价格方案删除草稿入参")
@Data
public class PricePlanDraftReqDTO {

    @ApiModelProperty(value = "所属用户guid")
    private String userGuid;

    /**
     * 品牌guid
     */
    private String brandGuid;
}
