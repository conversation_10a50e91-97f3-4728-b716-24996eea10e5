package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.mos.secure.ext.annotations.DesensitizationProp;
import com.mos.secure.ext.enums.SensitiveTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 订单信息
 */
@Data
public class OrderInfoRespDTO implements Serializable {

    private static final long serialVersionUID = -7296236571760374411L;

    /**
     * 订单guid
     */
    private String guid;

    /**
     * 商品明细
     */
    private List<DineInItemDTO> itemList;

    /**
     * 附加费明细
     */
    private List<SurchargeLinkDTO> surchargeLinkList;

    /**
     * 优惠明细
     */
    private List<DiscountFeeDetailDTO> discountFeeDetailList;

    /**
     * 退款明细
     */
    private List<RefundTransactionRecordDetailRespDTO> refundDetailList;

    /**
     * 订单号(前端显示用，门店内唯一，格式************)
     */
    private String orderNo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 交易模式(0：正餐，1：快餐)
     *
     * @see TradeModeEnum
     */
    private Integer tradeMode;

    /**
     * 设备类型(订单来源 BaseDeviceTypeEnum)
     */
    private Integer deviceType;

    /**
     * 客人数
     */
    private Integer guestCount;

    /**
     * 营业日
     */
    private LocalDate businessDay;

    /**
     * 桌台guid
     */
    private String diningTableGuid;

    /**
     * 桌台名称(区域+桌台code)
     */
    private String diningTableName;

    /**
     * 是否虚拟台(0:否，1:是)
     */
    private Integer virtualTable;

    /**
     * 作废原因
     */
    private String cancelReason;

    /**
     * 整单备注
     */
    private String remark;

    /**
     * 快餐牌号
     */
    private String mark;

    /**
     * 订单金额（商品总额+附加费）
     */
    private BigDecimal orderFee;

    /**
     * 附加费
     */
    private BigDecimal appendFee;

    /**
     * 实收金额=订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））
     * 应收金额=订单金额-优惠金额-订金(押金)
     */
    private BigDecimal actuallyPayFee;

    /**
     * 优惠金额
     */
    private BigDecimal discountFee;

    /**
     * 预付金（反结账原单聚合支付转入）
     */
    private BigDecimal prepayFee;

    /**
     * 定金
     */
    private BigDecimal reserveFee;

    /**
     * 1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废 7: 反结账
     */
    private Integer state;

    /**
     * 订单反结账类型1：普通单 2：原单 3：新单 4：退单
     */
    private Integer recoveryType;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 会员电话
     */
    @DesensitizationProp(SensitiveTypeEnum.MOBILE_PHONE)
    private String memberPhone;

    /**
     * 会员名字
     */
    @DesensitizationProp(SensitiveTypeEnum.CHINESE_NAME)
    private String memberName;

    /**
     * 结账设备类型（BaseDeviceTypeEnum）
     */
    private Integer checkoutDeviceType;

    /**
     * 取消订单设备类型（BaseDeviceTypeEnum）
     */
    private Integer cancelDeviceType;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 是否为调整单 0：不是调整单  1：是调整单
     */
    private Integer adjustState;

    /**
     * 退款订单guid
     */
    private Long refundOrderGuid;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 超出金额
     */
    private BigDecimal excessAmount;

    /**
     * 结账时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTime;

    /**
     * 多张会员卡支付信息
     */
    private List<MultiMemberDTO> multiMemberList;
}
