package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.framework.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class UnMappedItem implements Serializable {

    private static final long serialVersionUID = -2043569989584336271L;

    @ApiModelProperty("平台方商品ID")
    private String unItemId;

    @ApiModelProperty("平台方商品名称")
    private String unItemName;

    @ApiModelProperty("平台方商品规格")
    private String unItemSkuId;

    @ApiModelProperty("平台方商品规格名称")
    private String unItemSkuName;

    @ApiModelProperty("平台方商品名称拼接规格名称")
    private String unItemNameWithSku;

    @ApiModelProperty("平台方商品规格单位名称")
    private String unItemSkuUnit;

    @ApiModelProperty("平台方商品分类ID")
    private String unItemTypeId;

    @ApiModelProperty("平台方商品分类名称")
    private String unItemTypeName;

    @ApiModelProperty("平台方商品抵扣商品库商品数量")
    private Integer unItemCountMapper;

    @ApiModelProperty("ERP商品ID")
    private String erpItemId;

    @ApiModelProperty("ERP商品名称")
    private String erpItemName;

    @ApiModelProperty("ERP商品是否上架(0：否，1：是)")
    private Integer erpItemIsRack;

    @ApiModelProperty("ERP商品规格ID")
    private String erpItemSkuId;

    @ApiModelProperty("ERP商品规格名称")
    private String erpItemSkuName;

    @ApiModelProperty("ERP商品名称拼接规格名称")
    private String erpItemNameWithSku;

    @ApiModelProperty("ERP商品规格单位名称")
    private String erpItemSkuUnit;

    @ApiModelProperty("ERP商品分类ID")
    private String erpItemTypeId;

    @ApiModelProperty("ERP商品分类名称")
    private String erpItemTypeName;

    @ApiModelProperty("ERP门店guid")
    private String erpStoreGuid;

    @ApiModelProperty("ERP门店名称")
    private String erpStoreName;

    @ApiModelProperty(value = "所属品牌的name")
    private String belongBrandName;

    @ApiModelProperty("透传字段")
    private String extendValue;

    @ApiModelProperty("拓展信息记录ID")
    private Long extendInfoId;

    @ApiModelProperty("kds是否绑定")
    private Boolean erpItemIsKdsBindItem;

    @ApiModelProperty("美团方下单时传递的skuId")
    private String mtSkuId;

    @ApiModelProperty("是否有效绑定")
    private Boolean realBindFlag;

    /**
     * 业务标识，1-拼好饭
     */
    private int businessIdentify;

    @JsonIgnore
    public Integer getBindingFlag() {
        return StringUtils.isEmpty(erpItemSkuId) ? -1 : 1;
    }

    @JsonIgnore
    public Integer getRealBindingFlag() {
        return (Objects.nonNull(realBindFlag) && !realBindFlag) ? -1 : 1;
    }

    @JsonIgnore
    public Integer getSortHashCode() {
        return (erpStoreName + unItemTypeId).hashCode();
    }
}
