package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WxStoreTableTurnDTO {

	private WxStoreTableTurnNewDTO wxStoreTableTurnNewDTO;

	private WxStoreTableTurnOldDTO wxStoreTableTurnOldDTO;

}
