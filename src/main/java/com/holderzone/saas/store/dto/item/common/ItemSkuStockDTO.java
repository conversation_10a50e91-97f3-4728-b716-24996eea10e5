package com.holderzone.saas.store.dto.item.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishSingleDTO
 * @date 2018/11/30 上午10:07
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ItemSkuStockDTO {
    private static final long serialVersionUID = 1727528918478527413L;
    /**
     * 商品sku guid
     */
    @NotNull(message = "商品sku不能为空")
    @ApiModelProperty(value = "商品skuGuid", required = true)
    private String skuGuid;

    /**
     * 商品sku guid
     */
    @NotNull(message = "库存数量不能为空")
    @Min(value = 0, message = "不能为负数")
    @ApiModelProperty(value = "库存数量，不能为负数", required = true)
    private BigDecimal stock;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;
}
