package com.holderzone.saas.store.dto.takeaway;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2023-08-23
 * @description 美团回调接口公共参数
 */
@Data
public class MtCallbackCommonDTO {

    @NotBlank(message = "时间戳不能为空")
    private Long timestamp;

    @NotBlank(message = "数字签名不得为空")
    private String sign;

    @NotBlank(message = "开发者身份不得为空")
    private Long developerId;

    @NotBlank(message = "业务id不得为空")
    private Integer businessId;

    /**
     * 业务实体id
     */
    private String opBizCode;

    @NotBlank(message = "消息id不得为空")
    private String msgId;

}
