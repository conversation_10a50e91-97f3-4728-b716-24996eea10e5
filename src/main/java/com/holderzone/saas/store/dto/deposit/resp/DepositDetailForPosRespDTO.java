package com.holderzone.saas.store.dto.deposit.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class DepositDetailForPosRespDTO implements Serializable {

    private static final long serialVersionUID = 8424514095125620853L;

    @ApiModelProperty(value = "寄存单号")
    private String depositOrderId;


    @ApiModelProperty(value = "存入时间")
    private String saveTime;

    @ApiModelProperty(value = "头像地址")
    private String headPortrait;

    @ApiModelProperty(value = "存入人姓名")
    private String customerName;

    @ApiModelProperty(value = "存入人电话号码")
    private String phoneNum;

    @ApiModelProperty(value = "存入的商品列表")
    private List<GoodsRespDTO> goodsRespDTOS;

    @ApiModelProperty(value = "寄存备注")
    private String remark;


}
