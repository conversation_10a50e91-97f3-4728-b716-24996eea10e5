package com.holderzone.saas.store.dto.report;

/**
 * <AUTHOR>
 * @date 2018/10/16 上午 11:38
 * @description
 */
public class OrderDishDetailReportDTO {

    /**
     * 菜品编号
     */
    private String dishCode;

    /**
     * 菜品名称
     */
    private String dishName;

    /**
     * 菜品类型
     */
    private String dishType;

    /**
     * 是否套餐
     */
    private String packageDish;

    /**
     * 售卖价
     */
    private String salePrice;

    /**
     * 菜品总量
     */
    private String dishCount;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 结账时间
     */
    private String checkoutTimestamp;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 销售类型
     */
    private String saleType;

    /**
     * 结算员工
     */
    private String checkoutStaffName;

    public String getDishCode() {
        return dishCode;
    }

    public void setDishCode(String dishCode) {
        this.dishCode = dishCode;
    }

    public String getDishName() {
        return dishName;
    }

    public void setDishName(String dishName) {
        this.dishName = dishName;
    }

    public String getDishType() {
        return dishType;
    }

    public void setDishType(String dishType) {
        this.dishType = dishType;
    }

    public String getPackageDish() {
        return packageDish;
    }

    public void setPackageDish(String packageDish) {
        this.packageDish = packageDish;
    }

    public String getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(String salePrice) {
        this.salePrice = salePrice;
    }

    public String getDishCount() {
        return dishCount;
    }

    public void setDishCount(String dishCount) {
        this.dishCount = dishCount;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCheckoutTimestamp() {
        return checkoutTimestamp;
    }

    public void setCheckoutTimestamp(String checkoutTimestamp) {
        this.checkoutTimestamp = checkoutTimestamp;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getSaleType() {
        return saleType;
    }

    public void setSaleType(String saleType) {
        this.saleType = saleType;
    }

    public String getCheckoutStaffName() {
        return checkoutStaffName;
    }

    public void setCheckoutStaffName(String checkoutStaffName) {
        this.checkoutStaffName = checkoutStaffName;
    }
}
