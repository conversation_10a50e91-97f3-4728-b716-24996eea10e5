package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Api("验券优惠")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class MemberDiscountDTO {

	@ApiModelProperty(value = "折扣方式名字")
	private String discountName;

	@ApiModelProperty(value = "1-会员卡折扣，7-会员优惠券,8-积分抵扣")
	private Integer discountType;

	@ApiModelProperty(value = "折扣总额")
	private BigDecimal discountFee;

}