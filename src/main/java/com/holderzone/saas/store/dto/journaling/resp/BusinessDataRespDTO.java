package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessDataRespDTO
 * @date 2019/05/29 15:43
 * @description 报表-营业概况-营业数据
 * @program holder-saas-store-dto
 */
@Data
@ApiModel("报表-经营概况-营业数据")
@AllArgsConstructor
@NoArgsConstructor
public class BusinessDataRespDTO implements Serializable {
    private static final long serialVersionUID = 8739444481868442208L;
    public static BusinessDataRespDTO DEFAULT = new BusinessDataRespDTO(BigDecimal.ZERO, 0, 0, BigDecimal.ZERO, BigDecimal.ZERO, 0);
    @ApiModelProperty(value = "营业额")
    private BigDecimal businessFee;
    @ApiModelProperty(value = "订单数")
    private Integer orderCount;
    @ApiModelProperty(value = "客流量")
    private Integer guestCount;
    @ApiModelProperty(value = "优惠合计")
    private BigDecimal discountFee;

    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "总平均用餐时长（分）")
    private Integer orderAverageTimeTotal;

    public static BusinessDataRespDTO INSTANCE() {
        return new BusinessDataRespDTO(BigDecimal.ZERO, 0, 0, BigDecimal.ZERO, BigDecimal.ZERO, 0);
    }
}
