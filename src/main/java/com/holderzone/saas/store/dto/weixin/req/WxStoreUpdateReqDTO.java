package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreUpdateReqDTO
 * @date 2019/02/21 9:28
 * @description 微信门店配置修改请求DTO
 * @program holder-saas-store-weixin
 */
@Data
@ApiModel(value = "微信门店配置修改请求参数")
public class WxStoreUpdateReqDTO {
    @ApiModelProperty(value = "storeGuid")
    @NotNull
    private String storeGuid;

    /**
     * 是否营业（0关闭，1开启）
     */
    @ApiModelProperty(value = "是否营业（0关闭，1开启）")
    private Integer isOrderOpen;

    /**
     * 点餐模式（0正餐，1快餐）
     */
    @ApiModelProperty(value = "点餐模式（0正餐，1快餐）")
    private Integer orderModel;

    /**
     * 接单模式（0手动接单，1首单手动，加菜自动，2无需确认（仅适用于快餐））
     */
    @ApiModelProperty(value = "接单模式（0所有订单均需手动确认，1首单需手动确认，加菜无需确认，2无需确认（仅适用于快餐模式））")
    private Integer takingModel;

    /**
     * 是否开启线上买单（0关闭，1开启（快餐只能选择开启））
     */
    @ApiModelProperty(value = "是否开启线上买单（0关闭，1开启（快餐只能选择开启））")
    private Integer isOnlinePayed;

    /**
     * 结账模式（0线上支付后，需店员手动结账和清台，1线上支付后自动结账，需店员手动清台，2，自动结账（仅支持快餐））
     */
    @ApiModelProperty(value = "结账模式（0线上支付后，需店员手动结账和清台，1线上支付后自动结账，需店员手动清台，2，自动结账（仅支持快餐））")
    private Integer settleModel;

    @ApiModelProperty(value = "顾客是否可点称重商品（0顾客不可点，1顾客可点）")
    private Integer isWeighingOrdered;

    @ApiModelProperty(value = "是否开启单品备注（0关闭，1开启）")
    private Integer isRemarked;

    @ApiModelProperty(value = "跳转页面类型（0点餐页，1门店主页）")
    private Integer urlType;

    @ApiModelProperty(value = "菜单样式（0大图，1小图）")
    private Integer menuType;

    @ApiModelProperty(value = "已勾选的标签名字集合")
    private String tagNames;

    @ApiModelProperty(value = "已勾选的支付方式名字集合")
    private String payWayNames;

    @ApiModelProperty(value = "是否被配置（0未配置，1已配置），如果门店未同步，或门店未配置，将无法改变门店微信营业状态")
    private Integer isConfigured;

    @ApiModelProperty(value = "是否编辑开关")
    private Integer isSwitch;
}
