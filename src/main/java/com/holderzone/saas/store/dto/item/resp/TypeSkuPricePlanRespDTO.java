package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeSkuPricePlanRespDTO
 * @date 2021/04/05 下午2:16
 * @description //菜谱模式商品数据
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "菜谱模式商品数据")
public class TypeSkuPricePlanRespDTO implements Serializable {

    @ApiModelProperty(value = "菜谱guid")
    private String pricePlanGuidArray;

    @ApiModelProperty(value = "规格集合")
    private List<TypeSkuRespDTO> typeSkuRespDTOList;


}
