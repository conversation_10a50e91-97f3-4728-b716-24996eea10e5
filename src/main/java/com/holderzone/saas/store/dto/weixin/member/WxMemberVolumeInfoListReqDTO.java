package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Api("会员优惠券列表入参")
public class WxMemberVolumeInfoListReqDTO {

	@ApiModelProperty("平台会员GUID")
	private String memberInfoGuid;
	@ApiModelProperty("卷分类，-1全部类型，0代金卷,1折扣卷,2兑换卷")
	private Integer volumeType;
	@ApiModelProperty("企业GUID")
	private String enterpriseGuid;
	@ApiModelProperty("品牌GUID")
	private String brandGuid;
	@ApiModelProperty("优惠券状态，0可使用的，1已失效的")
	private Integer mayUseVolume;
	@ApiModelProperty(value = "门店id")
	private String storeGuid;

}
