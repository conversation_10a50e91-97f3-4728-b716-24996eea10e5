package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "方案菜品分页查询入参")
@Data
public class PricePlanItemPageReqDTO extends PageDTO {

    @ApiModelProperty(value = "方案guid")
    private String planGuid;

    @ApiModelProperty(value = "分类guid，选择全部分类时为空")
    private String typeGuid;

    @ApiModelProperty(value = "查询商品名或商品spu（guid）")
    private String itemNameOrCode;
}
