package com.holderzone.saas.store.dto.kds.req;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 菜品分组 请求DTO
 */
@Data
public class ItemGroupReqDTO implements Serializable {

    private static final long serialVersionUID = -8993349661482406553L;

    /**
     * 分组guid
     */
    private String groupGuid;

    /**
     * 分组名称
     */
    @NotBlank(message = "分组名称不能为空")
    private String name;

    /**
     * 选择商品列表
     */
    @Valid
    @NotEmpty(message = "选择商品不能为空")
    private List<InnerSku> skus;

    @Data
    public static class InnerSku implements Serializable {

        private static final long serialVersionUID = 727347819505157801L;

        @NotBlank(message = "item guid")
        private String itemGuid;

        @NotBlank(message = "sku guid")
        private String skuGuid;
    }
}
