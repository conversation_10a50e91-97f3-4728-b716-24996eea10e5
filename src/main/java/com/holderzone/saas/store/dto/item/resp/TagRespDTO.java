package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TagRespDTO
 * @date 2019/01/23 上午10:37
 * @description //商品的标签返回实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "标签返回实体")
public class TagRespDTO implements Serializable {

    private static final long serialVersionUID = -7362801988210071630L;

    @ApiModelProperty(value = "标签显示名称", required = true)
    private String name;

    @ApiModelProperty(value = "字段值，之后标签可以自由配置了就是GUID", required = true)
    private String id;
}
