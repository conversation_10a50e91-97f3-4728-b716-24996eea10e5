package com.holderzone.saas.store.dto.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@ApiModel("企业可分配角色以及该用户可分配角色DTO")
@Accessors(chain = true)
public class UserRoleDistDTO {

    @ApiModelProperty(value = "角色GUID")
    private String guid;

    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty(value = "角色是否已勾选")
    private Boolean isChecked;

    public UserRoleDistDTO(String guid, String name) {
        this.guid = guid;
        this.name = name;
    }

    public UserRoleDistDTO(String guid, String name, Boolean isChecked) {
        this.guid = guid;
        this.name = name;
        this.isChecked = isChecked;
    }

    @JsonIgnore
    public static UserRoleDistDTO checked(String guid, String name) {
        return new UserRoleDistDTO(guid, name, Boolean.TRUE);
    }

    @JsonIgnore
    public static UserRoleDistDTO unChecked(String guid, String name) {
        return new UserRoleDistDTO(guid, name, Boolean.FALSE);
    }

    @JsonIgnore
    public static UserRoleDistDTO checkedIgnore(String guid, String name) {
        return new UserRoleDistDTO(guid, name);
    }
}
