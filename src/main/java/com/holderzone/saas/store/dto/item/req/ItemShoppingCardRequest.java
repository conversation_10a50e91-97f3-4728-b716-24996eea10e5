package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class ItemShoppingCardRequest {

    @ApiModelProperty(value = "企业guid", required = true)
    @NotBlank(message = "企业Guid不能为空")
    private String enterpriseGuid;

    @ApiModelProperty(value = "门店guid", required = true)
    @NotBlank(message = "门店Guid不能为空")
    private String storeGuid;

    @ApiModelProperty(value = "规格guid", required = true)
    @NotEmpty(message = "规格Guid不能为空")
    @Size
    private List<String> skuGuids;

}
