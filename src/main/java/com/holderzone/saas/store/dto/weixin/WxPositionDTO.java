package com.holderzone.saas.store.dto.weixin;

import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxPositionDTO
 * @date 2019/05/17 15:34
 * @description
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("获取门店列表请求参数")
public class WxPositionDTO extends WxPortalReqDTO{

    @ApiModelProperty("当前用户经度")
    @NotNull
    private BigDecimal longitude;

    @ApiModelProperty("当前用户纬度")
    @NotNull
    private BigDecimal latitude;

    @ApiModelProperty("城市code")
    private String cityCode;

    @ApiModelProperty("城市名字")
    private String cityName;
}
