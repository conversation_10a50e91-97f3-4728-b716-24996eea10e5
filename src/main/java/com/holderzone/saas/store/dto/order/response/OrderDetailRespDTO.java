package com.holderzone.saas.store.dto.order.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.order.BillRespDTO;
import com.holderzone.saas.store.dto.order.OrderDTO;
import com.holderzone.saas.store.dto.order.common.OrderDishDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderDetailRespDTO
 * @date 2018/09/08 14:24
 * @description //安卓订单详情
 * @program holder-saas-store-order
 */
@Data
public class OrderDetailRespDTO implements Serializable {

    private static final long serialVersionUID = -7464647213587627304L;

    @ApiModelProperty(value = "订单来源：一体机点餐下单（1）、POS设备（2）、M1（3）、平板点餐（4）、微信公众号（5）、美团外卖（6）饿了么外卖（7）", required = true)
    private String orderSourceName;

    @ApiModelProperty(value = "账单来源：一体机点餐下单（1）、POS设备（2）、M1（3）、平板点餐（4）、微信公众号（5）、美团外卖（6）饿了么外卖（7）", required = true)
    private String billSourceName;

    @ApiModelProperty(value = "交易模式：0:堂食,1:快餐,2:外卖", required = true)
    private String tradeModeName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "设备类型（OrderDeviceTypeEnum）")
    private String deviceTypeName;

    @ApiModelProperty(value = "状态(0：未完成， 1：已完成， 2：已作废 ，3：已退款，4：反结账状态)")
    private Integer state;

    @ApiModelProperty(value = "开台时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkinTimestamp;

    @ApiModelProperty(value = "结算时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTimestamp;

    @ApiModelProperty(value = "取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime cancelTimestamp;


    @ApiModelProperty(value = "创建订单人")
    private String createStaffName;

    @ApiModelProperty(value = "结账人")
    private String checkoutStaffName;

    @ApiModelProperty(value = "订单的guid", required = true)
    private String orderGuid;

    @ApiModelProperty(value = "外卖订单的guid")
    private String takeawayOrderGuid;

    @ApiModelProperty(value = "账单Guid,如果账单更新，需要传入")
    private String fundBillGuid;

    @ApiModelProperty(value = "门店guid", required = true)
    @NotBlank(message = "门店guid不能为空")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称", required = true)
    @NotBlank(message = "门店名称不能为空")
    private String storeName;

    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "牌号")
    private String mark;

//    @ApiModelProperty(value = "附加费")
//    private BigDecimal surcharge;

    @ApiModelProperty(value = "桌台guid（仅堂食）")
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台名字（仅堂食）")
    private String diningTableName;

    @ApiModelProperty(value = "交易模式：0:堂食,1:快餐,2:外卖", required = true)
    private int tradeMode;

    @ApiModelProperty(value = "订单来源：一体机点餐下单（1）、POS设备（2）、M1（3）、平板点餐（4）、微信公众号（5）、美团外卖（6）饿了么外卖（7）", required = true)
    private Integer orderSource;

    @ApiModelProperty(value = "就餐人数（仅堂食）")
    private int guestCount;

    @ApiModelProperty(value = "支付信息")
    private BillRespDTO billRespDTO;

    @ApiModelProperty(value = "反结账信息")
    private RecoveryInfo recoveryInfo;

    @ApiModelProperty(value = "作废订单信息")
    private CancelInfo cancelInfo;

    @ApiModelProperty(value = "退菜信息")
    private List<ReturnDish> returnDish;

    @ApiModelProperty(value = "菜品")
    private List<OrderDishDTO> orderDishes;

    @Data
    public static class ReturnDish {

        @ApiModelProperty(value = "退菜原因")
        private String reason;

        @ApiModelProperty(value = "订单菜品guid")
        private String orderDishGuid;

        @ApiModelProperty(value = "菜品总价小计")
        private BigDecimal orderDishTotal;

        @ApiModelProperty(value = "退菜菜品guid")
        private String dishGuid;

        @ApiModelProperty(value = "退菜菜品名称")
        private String dishName;

        @ApiModelProperty(value = "退菜菜品编号")
        private String code;

        @ApiModelProperty(value = "是否赠送(0:非赠送、1：赠送)")
        private Byte gift;

        @ApiModelProperty(value = "退菜套餐主项guid")
        private String parentDishGuid;

        @ApiModelProperty(value = "退菜菜品的套餐类型(0：单规格、1：多规格、2：套餐主项、3：套餐子项、4：称重菜品 )")
        private Byte packageDishType;

        @ApiModelProperty(value = "退菜菜品的规格")
        private String skuGuid;

        @ApiModelProperty(value = "退菜规格名称")
        private String skuName;

        @ApiModelProperty(value = "退菜sku价格")
        private BigDecimal price;

        @ApiModelProperty(value = "退菜数量")
        private BigDecimal returnCount;

        @ApiModelProperty(value = "计数单位")
        private String unit;

        @ApiModelProperty(value = "计数单位code")
        private Integer unitCode;

        @ApiModelProperty(value = "属性总价")
        private BigDecimal skuPropertyTotal;

        @ApiModelProperty(value = "菜品备注")
        private String remark;

        @ApiModelProperty(value = "菜品属性")
        private List<OrderDTO.SkuProperty> skuProperties;
    }

    @Data
    public static class RecoveryInfo {

        @ApiModelProperty(value = "反结账原因")
        private String recoveryReason;

        /**
         * 反结账UUID
         */
        @ApiModelProperty(value = "反结账UUID")
        private String recoveryUuid;

        @ApiModelProperty(value = "反结账更新操作人guid")
        private String updateStaffGuid;

        @ApiModelProperty(value = "反结账更新操作人名称")
        private String updateStaffName;

        @ApiModelProperty(value = "反结账设备类型")
        private Integer recoveryDeviceType;

        @ApiModelProperty(value = "反结账次数")
        private Integer recoveryNum;

        @ApiModelProperty(value = "反结账金额")
        private BigDecimal recoveryFee;

        @ApiModelProperty(value = "反结账时间")
        private LocalDateTime gmtModified;
    }

    @Data
    public static class CancelInfo {

        @ApiModelProperty(value = "作废原因")
        private String invalidReason;

        @ApiModelProperty(value = "作废更新操作人guid")
        private String cancelStaffGuid;

        @ApiModelProperty(value = "作废更新操作人名称")
        private String cancelStaffName;

        @ApiModelProperty(value = "作废设备类型")
        private Integer cancelDeviceType;

        @ApiModelProperty(value = "作废设备名称")
        private String cancelDeviceName;

        @ApiModelProperty(value = "退款金额")
        private BigDecimal cancelFee;

        @ApiModelProperty(value = "退款方式")
        private String refundWay;

        @ApiModelProperty(value = "作废时间")
        private LocalDateTime cancelTimestamp;

        @ApiModelProperty(value = "退款时间")
        private LocalDateTime refundTimestamp;
    }


}
