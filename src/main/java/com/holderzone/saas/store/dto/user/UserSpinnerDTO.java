package com.holderzone.saas.store.dto.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.OrgGeneralDTO;
import com.holderzone.saas.store.dto.organization.RegionDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("用户数据权限过滤条件DTO")
public class UserSpinnerDTO implements Serializable {

    private static final long serialVersionUID = -7020396307936047417L;

    @ApiModelProperty("用户门店权限过滤条件")
    private List<StoreDTO> arrayOfStoreDTO;

    @ApiModelProperty("用户组织权限过滤条件")
    private List<OrgGeneralDTO> arrayOfOrgDTO;

    @ApiModelProperty("用户品牌权限过滤条件")
    private List<BrandDTO> arrayOfBrandDTO;

    @ApiModelProperty("用户区域权限过滤条件")
    private List<RegionDTO> arrayOfRegionDTO;

    public UserSpinnerDTO(List<OrgGeneralDTO> arrayOfOrgDTO, List<BrandDTO> arrayOfBrandDTO,
                          List<RegionDTO> arrayOfRegionDTO) {
        this.arrayOfStoreDTO = Collections.emptyList();
        this.arrayOfOrgDTO = arrayOfOrgDTO;
        this.arrayOfBrandDTO = arrayOfBrandDTO;
        this.arrayOfRegionDTO = arrayOfRegionDTO;
    }

    public UserSpinnerDTO(List<StoreDTO> arrayOfStoreDTO, List<OrgGeneralDTO> arrayOfOrgDTO,
                          List<BrandDTO> arrayOfBrandDTO, List<RegionDTO> arrayOfRegionDTO) {
        this.arrayOfStoreDTO = arrayOfStoreDTO;
        this.arrayOfOrgDTO = arrayOfOrgDTO;
        this.arrayOfBrandDTO = arrayOfBrandDTO;
        this.arrayOfRegionDTO = arrayOfRegionDTO;
    }

    @JsonIgnore
    public static UserSpinnerDTO empty() {
        return new UserSpinnerDTO()
                .setArrayOfStoreDTO(Collections.emptyList())
                .setArrayOfOrgDTO(Collections.emptyList())
                .setArrayOfBrandDTO(Collections.emptyList())
                .setArrayOfRegionDTO(Collections.emptyList());
    }
}
