package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/5
 * @description 菜品显示顺序配置
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "菜品显示顺序配置")
public class DisplayRuleItemSortDTO implements Serializable {

    private static final long serialVersionUID = 632483552806733723L;

    /**
     * 规则guid
     */
    @ApiModelProperty(value = "规则guid")
    private String guid;

    /**
     * 品牌guid
     */
    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    /**
     * 加菜显示顺序 0按下单时间依次展示 1突出展示
     */
    @ApiModelProperty(value = "加菜显示顺序 0按下单时间依次展示 1突出展示")
    private Integer itemSortType;

    /**
     * 加菜展示方式 0加菜全部突出展示 1按照设定时间突出显示
     */
    @ApiModelProperty(value = "加菜展示方式 0加菜全部突出展示 1按照设定时间突出显示")
    private Integer itemDisplayType;

    /**
     * 加菜突出显示时间设置
     * 距第一次下单时间%s分钟后的新加菜品突出显示
     */
    @ApiModelProperty(value = "加菜突出显示时间设置 距第一次下单时间%s分钟后的新加菜品突出显示")
    private Integer itemIntervalTime;

}
