package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class OrderTableReqDTO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    @NotBlank(message = "订单Guid不得为空")
    @ApiModelProperty("订单Guid")
    private String orderGuid;

    @NotBlank(message = "区域Guid不得为空")
    @ApiModelProperty(value = "区域Guid")
    private String areaGuid;

    @NotBlank(message = "原桌台Guid不得为空")
    @ApiModelProperty(value = "原桌台Guid")
    private String oriTableGuid;

    @NotBlank(message = "新桌台Guid不得为空")
    @ApiModelProperty(value = "新桌台Guid")
    private String newTableGuid;

    @NotBlank(message = "新桌台名称不得为空")
    @ApiModelProperty(value = "新桌台名称")
    private String newTableName;

}
