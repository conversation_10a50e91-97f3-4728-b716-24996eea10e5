package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class DeviceConfUpdateReqDTO implements Serializable {

    private static final long serialVersionUID = -2782247301702252131L;

    @NotEmpty(message = "设备Guid不得为空")
    @ApiModelProperty(value = "设备Guid")
    private String deviceId;

    @NotNull(message = "显示模式不得为空", groups = BasicConfig.class)
    @Min(value = 0, message = "显示模式" +
            "制作点位显示模式：0=堂口模式，1=菜品汇总模式，2=订单模式，3=订单1*6,4=混合订单模式1*6" +
            "出堂点位显示模式：0=单菜品模式，1=菜品汇总模式，2=订单模式，3=订单1*6,4=混合订单模式1*6")
    @Max(value = 4, message = "显示模式" +
            "制作点位显示模式：0=堂口模式，1=菜品汇总模式，2=订单模式，3=订单1*6,4=混合订单模式1*6" +
            "出堂点位显示模式：0=单菜品模式，1=菜品汇总模式，2=订单模式，3=订单1*6,4=混合订单模式1*6")
    @ApiModelProperty(value = "显示模式" +
            "制作点位显示模式：0=堂口模式，1=菜品汇总模式，2=订单模式，3=订单1*6,4=混合订单模式1*6" +
            "出堂点位显示模式：0=单菜品模式，1=菜品汇总模式，2=订单模式，3=订单1*6,4=混合订单模式1*6")
    private Integer displayMode;

    @Valid
    @NotNull(message = "制作点高级配置不得为空", groups = AdvancedConfig.class)
    @ApiModelProperty(value = "制作点高级配置")
    private DevicePrdConfDTO devicePrdConfDTO;

    @Valid
    @NotNull(message = "出堂口高级配置不得为空", groups = AdvancedConfig.class)
    @ApiModelProperty(value = "出堂口高级配置")
    private DeviceDstConfDTO deviceDstConfDTO;

    public interface BasicConfig {
    }

    public interface AdvancedConfig {
    }
}
