package com.holderzone.saas.store.dto.trade;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/6/1 15:10
 * @description 订单属性
 */
@Data
public class ItemAttrDetailDTO {
    /**
     * 全局唯一主键
     */
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    private Boolean isDelete;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 订单商品guid
     */
    private Long orderItemGuid;

    /**
     * 属性guid
     */
    private String attrGuid;

    /**
     * 属性名称
     */
    private String attrName;

    /**
     * 属性组guid
     */
    private String attrGroupGuid;

    /**
     * 属性组名称
     */
    private String attrGroupName;

    /**
     * 属性价格
     */
    private BigDecimal attrPrice;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;
}
