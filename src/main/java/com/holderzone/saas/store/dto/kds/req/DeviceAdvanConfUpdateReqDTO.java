package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class DeviceAdvanConfUpdateReqDTO implements Serializable {

    private static final long serialVersionUID = -2782247301702252131L;

    @NotEmpty(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @NotEmpty(message = "设备ID不得为空")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @Valid
    @NotNull(message = "制作点高级配置不得为空", groups = ProductionConfig.class)
    @ApiModelProperty(value = "制作点高级配置")
    private DevicePrdConfDTO devicePrdConfDTO;

    @Valid
    @NotNull(message = "出堂口高级配置不得为空", groups = DistributeConfig.class)
    @ApiModelProperty(value = "出堂口高级配置")
    private DeviceDstConfDTO deviceDstConfDTO;

    public interface ProductionConfig {
    }

    public interface DistributeConfig {
    }
}
