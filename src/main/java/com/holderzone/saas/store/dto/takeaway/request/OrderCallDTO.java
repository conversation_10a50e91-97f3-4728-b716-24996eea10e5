package com.holderzone.saas.store.dto.takeaway.request;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * hst_takeout_call_record
 *
 * <AUTHOR>
@Data
@Accessors(chain = true)
public class OrderCallDTO implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 唯一标识
     */
    private String guid;

    /**
     * oder主键
     */
    private String orderGuid;

    /**
     * 订单子类
     * OrderType=0：0=美团  1=饿了么  2=百度  3=京东
     * OrderType=1：0=扫码订单  1=微信预订单
     */
    private Integer orderSubType;

    /**
     * 门店名字
     */
    private String storeName;

    /**
     * 数据批次
     */
    private String batch;

    /**
     * 送达类型 0 超时 1 正常
     */
    private Integer sendStatus;

    /**
     * 呼叫状态 0 已拨打、1 未拨打、2 已接通、3 未接通
     */
    private Integer callStatus;

    /**
     * 呼叫失败 message
     */
    private String message;

    /**
     * 商户标识
     */
    private String enterpriseGuid;

    /**
     * 门店标识
     */
    private String storeGuid;

    /**
     * 外卖订单ID
     */
    private String orderId;

    /**
     * 顾客隐私手机号
     */
    private String phone;

    /**
     * 订单创建时间
     */
    private LocalDateTime createTime;

    /**
     * 期望送达时间：转时间戳后，若0=立即配送，若大于0=具体要求的送达时间
     */
    private LocalDateTime estimateDeliveredTime;

    /**
     * 实际送达时间
     */
    private LocalDateTime deliveredTime;

    /**
     * 订单完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;
}