package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;

@Data
@ApiModel("微信订单完成后的通知入参")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class WxMemberTradeNotifyReqDTO {

    /*
    userInfoDTO.getEnterpriseGuid().wxMemberPayDTO.getOpenId()
			//wxMemberPayDTO.getStoreGuid()wxMemberPayDTO.getStoreName()
			//calculate.getActuallyPayFee().setScale(2, BigDecimal.ROUND_HALF_UP
			//UserContextUtils.getStoreGuid()
			//billPayReqDTO.getOrderGuid()
			  prepay.getMemberInfoCardGuid()
			  prepay.getBrandGuid()
     */

    String enterpriseGuid;
    String openId;
    String storeGuid;
    String storeName;
    //calculate.getActuallyPayFee()
    @ApiModelProperty(value = "支付金额")
    BigDecimal actuallyPayFee;
    @ApiModelProperty(value = "微信订单的guid")
    String orderGuid;

    String memberInfoCardGuid;
    String brandGuid;
}
