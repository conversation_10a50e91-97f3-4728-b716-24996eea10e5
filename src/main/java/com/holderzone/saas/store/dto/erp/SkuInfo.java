package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/05/17 下午 14:17
 * @description
 */
@ApiModel("sku的用量实体")
public class SkuInfo{

    @ApiModelProperty("sku的guid")
    private String skuGuid;

    @ApiModelProperty("用量")
    private BigDecimal count;

    public SkuInfo() {
    }

    public SkuInfo(String skuGuid, BigDecimal count) {
        this.skuGuid = skuGuid;
        this.count = count;
    }


    public String getSkuGuid() {
        return skuGuid;
    }

    public void setSkuGuid(String skuGuid) {
        this.skuGuid = skuGuid;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }
}