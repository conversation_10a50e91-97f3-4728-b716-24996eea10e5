package com.holderzone.saas.store.dto.kds.resp;

import com.holderzone.saas.store.dto.print.template.PrintRow;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/07/26 11:12
 * @description 打印转码后传输对象
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(description = "打印单实体")
public class KdsPrintOrderDTO implements Serializable {

    private static final long serialVersionUID = -2298992933118311673L;

    @ApiModelProperty(value = "打印标识", required = true)
    private String printKey;

    @ApiModelProperty(value = "打印机IP地址", required = true)
    private String printerIp;

    @ApiModelProperty(value = "打印机Port", required = true)
    private Integer printerPort;

    @ApiModelProperty(value = "打印次数", required = true)
    private Integer printTimes;

    @ApiModelProperty(value = "打印机类型; 可选参数: 0/本机; 1/网络打印机; 2/usb打印机", required = true)
    private Integer printerType;

    @ApiModelProperty(value = "页张大小", required = true)
    private Integer pageSize;

    @ApiModelProperty(value = "打印行集合", required = true)
    private List<PrintRow> printRows;
}
