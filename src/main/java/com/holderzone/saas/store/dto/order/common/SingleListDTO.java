package com.holderzone.saas.store.dto.order.common;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0
 * @className SingleDataDTO
 * @date 2018/09/15 下午4:52
 * @description 接受前端单参数使用的DTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "接收前端单参数请求对应的pojo")
@EqualsAndHashCode(callSuper = true)
public class SingleListDTO extends BaseDTO {

    private static final long serialVersionUID = -4997463513943512986L;

    @ApiModelProperty(value = "业务请求参数")
    private List<String> list;
}
