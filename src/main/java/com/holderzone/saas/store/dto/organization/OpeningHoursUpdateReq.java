package com.holderzone.saas.store.dto.organization;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OpeningHoursUpdateReq {
    @ApiModelProperty(value = "企业guid",required = true)
    private String enterpriseGuid;
    @ApiModelProperty(value = "门店guid",required = true)
    private String storeGuid;
    @ApiModelProperty(value = "门店营业时间 （注意格式，且保证不同时间段之间不存在交集）",required = true,example = "7:00-9:00,11:30-19:00")
    private String openingHours;
}
