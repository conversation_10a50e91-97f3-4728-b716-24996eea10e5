package com.holderzone.saas.store.dto.report.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ReserveReportQueryVO extends ReportQueryVO {

    private static final long serialVersionUID = 2942494682295252169L;

    @ApiModelProperty(value = "预定状态")
    public List<Integer> reserveStates;

    /**
     * 是否逾期
     */
    private Boolean isDelay;

    @ApiModelProperty("来源 15小程序预定 3一体机预定 31桌台预付 ?pos预定")
    private Integer deviceType;

}
