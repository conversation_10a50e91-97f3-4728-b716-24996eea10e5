package com.holderzone.saas.store.dto.invoice;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@ApiModel(description = "开票校验结果")
@Data
public class ResponseVerifyInvoiceDTO implements Serializable {


    @ApiModelProperty(value = "失败原因")
    private Integer messageCode;

    @ApiModelProperty(value = "是否校验通过 1 是 0 否")
    private Integer verifyCode;

    /**
     * 电子税务局注册人姓名
     */
    private String electronicTaxpayerName;

    /**
     * 电子税务局注册手机号
     */
    private String electronicTaxpayerPhone;
}
