package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreMerchantOrderRespDTO
 * @date 2019/4/9
 */
@ApiModel("微信门店商户接单返回")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxStoreMerchantOrderRespDTO {
	List<WxStoreMerchantOrderDTO> wxStoreMerchantOrderDTOS;
}
