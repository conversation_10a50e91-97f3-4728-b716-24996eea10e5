package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/06/01 16:35
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "价格方案导入菜品查询可导入菜品入参")
public class PricePlanItemAddQueryReqDTO extends BasePageDTO {

    @ApiModelProperty(value = "分类guid")
    private String typeGuid;

    @ApiModelProperty(value = "查询关键字")
    private String keywords;

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "方案guid")
    private String planGuid;

}
