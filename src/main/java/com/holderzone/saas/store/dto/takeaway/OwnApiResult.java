package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class OwnApiResult {

    @ApiModelProperty(value = "响应返回码，参考接口返回码 (0成功 -1失败 默认失败)")
    private Integer code;

    @ApiModelProperty(value = "响应状态(成功为success,失败为fail)")
    private String status;

    @ApiModelProperty(value = "响应描述")
    private String msg;

    @ApiModelProperty(value = "响应结果")
    private Object result;

    @ApiModelProperty(value = "错误编码(和code一致)")
    private Integer errorCode;

}
