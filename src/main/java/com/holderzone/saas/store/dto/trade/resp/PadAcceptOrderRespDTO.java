package com.holderzone.saas.store.dto.trade.resp;

import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description
 * @date 2021/8/26 10:58
 * @className:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class PadAcceptOrderRespDTO {

    private String enterpriseGuid = "";

    private String storeGuid = "";

    private String diningTableGuid = "";

    private String tableCode = "";

    private String areaName = "";

    private String storeName = "";

    private String brandName = "";

    private EstimateItemRespDTO estimateItemRespDTO;

    private String errorMsg = "";

    public static PadAcceptOrderRespDTO estimate() {
        return new PadAcceptOrderRespDTO().setErrorMsg("商品已沽清");
    }

    public static PadAcceptOrderRespDTO outDate() {
        return new PadAcceptOrderRespDTO().setErrorMsg("订单长时间未处理，请刷新");
    }

    public static PadAcceptOrderRespDTO tableError() {
        return new PadAcceptOrderRespDTO().setErrorMsg("桌台无法开台，不能接单");
    }
}