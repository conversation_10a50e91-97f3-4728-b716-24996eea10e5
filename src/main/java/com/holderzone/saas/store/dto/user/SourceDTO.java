package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className SourceDTO
 * @date 19-2-13 上午11:51
 * @description 商户后台服务资源DTO
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("服务资源DTO")
public class SourceDTO {
    @ApiModelProperty("服务资源名")
    private String sourceName;

    @ApiModelProperty("服务资源guid")
    private String sourceGuid;

    @ApiModelProperty("服务资源code")
    private String sourceCode;

    @ApiModelProperty("服务资源url")
    private String sourceUrl;

    @ApiModelProperty("是否已存在")
    private Boolean isChecked;
}
