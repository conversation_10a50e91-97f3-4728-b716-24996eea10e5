package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseBillQuery
 * @date 2018/09/15 15:11
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class BaseBillQuery extends BaseDTO {

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    protected Long beginTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    protected Long endTime;

    @ApiModelProperty(value = "当前页数")
    protected int currentPage = 1;

    @ApiModelProperty(value = "每页多少数据")
    protected int pageSize = 10;

    public int getIndex() {
        return (this.currentPage <= 0 ? 1 : this.currentPage - 1) * this.pageSize;
    }

}
