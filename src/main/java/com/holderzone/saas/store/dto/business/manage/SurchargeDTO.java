package com.holderzone.saas.store.dto.business.manage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AdditionalFeeDO
 * @date 2018/08/02 下午3:00
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(description = "附加费实体")
public class SurchargeDTO implements Serializable {

    private static final long serialVersionUID = -3810430351672213557L;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "附加费guid")
    private String surchargeGuid;

    @ApiModelProperty(value = "附加费名")
    private String name;

    @ApiModelProperty(value = "附加费金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "收费方式。0=按人，1=按桌。")
    private Integer type;

    @ApiModelProperty(value = "是否已启用：0=未启用，1=已启用")
    private Boolean isEnable;

    @ApiModelProperty(value = "是否已删除：0=未删除，1=已删除")
    private Boolean isDeleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "关联区域")
    private List<SurchargeAreaDTO> areaList;

    private String tradeMode;

    @NotEmpty(message = "场景")
    @ApiModelProperty(value = "场景：0=正餐，1=快餐", required = true)
    private List<String> tradeModes;

    @ApiModelProperty(value = "有效时间")
    private Integer effectiveTime;
}
