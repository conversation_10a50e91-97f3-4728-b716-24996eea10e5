package com.holderzone.saas.store.dto.store.store;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className chirdOrganization
 * @date 18-8-30 下午4:38
 * @description 组织上下级关系DTO
 * @program holder-saas-store-store
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ChildOrganization {

    /**
     * 组织编码
     */
    @ApiModelProperty("组织编码")
    private Long id;

    /**
     * 组织名称
     */
    @ApiModelProperty("组织名称")
    private String name;

    /**
     * 组织guid
     */
    @ApiModelProperty("组织guid")
    private String organizationGuid;

    /**
     * 组织类型。0=集团（云端存储），1=品牌，2=区域，3=门店。
     */
    @ApiModelProperty("组织类型。0=集团，1=品牌，2=区域，3=门店。")
    private Integer organizationType;

    /**
     * 父级组织的guid
     */
    @ApiModelProperty("父级组织的guid")
    private String parentOrganizationGuid;

    /**
     * 父级组织名称
     */
    @ApiModelProperty("父级组织名称")
    private String parentOrganizationName;

    /**
     * 下级List
     */
    @ApiModelProperty("下级组织List")
    private List<ChildOrganization> childOrganizations;
}
