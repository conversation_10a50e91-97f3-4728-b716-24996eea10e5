package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Api("会员卡或优惠券")
@Data
@Builder
public class WxVolumeCodeRespDTO {

	@ApiModelProperty("所有优惠券信息")
	private List<WxVolumeCodeDTO> memberVolumeList;

	@ApiModelProperty(value = "0：正常，1：不正常")
	private Integer result;

	@ApiModelProperty(value = "错误信息")
	private String errorMsg;

}
