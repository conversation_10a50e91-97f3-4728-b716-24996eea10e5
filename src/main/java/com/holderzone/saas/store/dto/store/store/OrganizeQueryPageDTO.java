package com.holderzone.saas.store.dto.store.store;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreQueryPageDTO
 * @date 2018/07/23 下午5:23
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class OrganizeQueryPageDTO extends BasePageDTO implements Serializable {

    private static final long serialVersionUID = -1928341295207214147L;

    /**
     * 组织guid
     */
    @ApiModelProperty(value = "组织guid")
    private String organizationGuid;

    /**
     * 组织名称/组织编码
     */
    @ApiModelProperty(value = "组织名称/组织编码")
    private String nameOrId;

    /**
     * 创建组织的用户guid
     */
    @ApiModelProperty(value = "创建组织的用户guid")
    private String createUserGuid;

    @ApiModelProperty(value = "组织类型")
    private Integer organizationType;
}
