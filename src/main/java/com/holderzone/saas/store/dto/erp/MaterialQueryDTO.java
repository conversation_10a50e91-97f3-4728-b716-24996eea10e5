package com.holderzone.saas.store.dto.erp;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/25 14:59
 */
public class MaterialQueryDTO extends BasePageDTO {

    private String warehouseGuid;
    private String category;
    @ApiModelProperty(value = "状态:0/禁用;1/启用",example = "1")
    private Integer state;
    private String storeGuid;
    private List<String> materialGuidList;
    @ApiModelProperty(value = "查询条件")
    private String searchConditions;
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }

    public List<String> getMaterialGuidList() {
        return materialGuidList;
    }

    public void setMaterialGuidList(List<String> materialGuidList) {
        this.materialGuidList = materialGuidList;
    }

    public String getSearchConditions() {
        return searchConditions;
    }

    public void setSearchConditions(String searchConditions) {
        this.searchConditions = searchConditions;
    }
}
