package com.holderzone.saas.store.dto.order.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.common.OrderDishDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CreatOrderReqDTO
 * @date 2018/09/14 15:33
 * @description 创建订单入参
 * @program holder-saas-store-order
 */
@Data
public class CreatOrderReqDTO extends BaseDTO{

//    @ApiModelProperty(value = "会员guid（也可以返回结算结果时更新）")
//    private String memberGuid;
//
//    @ApiModelProperty(value = "会员电话")
//    private String memberPhone;

    //    @ApiModelProperty(value = "附加费")
//    private BigDecimal surcharge;

    @ApiModelProperty(value = "订单的guid", required = true)
    private String orderGuid;

    @ApiModelProperty(value = "账单guid")
    private String billGuid;

    @ApiModelProperty(value = "整单备注" )
    private String remark;

    @ApiModelProperty(value = "牌号")
    private String mark;

    @ApiModelProperty(value = "是否自动牌号（0:否，1：是）")
    private Integer autoMark;

    @ApiModelProperty(value = "客人数")
    private Integer guestCount;

    @ApiModelProperty(value = "交易模式：0:堂食,1:快餐,2:外卖", required = true)
    @NotNull(message = "交易模式不能为空")
    private Integer tradeMode;

    @ApiModelProperty(value = "订单来源：一体机点餐下单（1）、POS设备（2）、M1（3）、平板点餐（4）、微信公众号（5）、美团外卖（6）饿了么外卖（7）", required = true)
    private Integer orderSource;

    @ApiModelProperty(value = "菜品", required = true)
    private List<OrderDishDTO> orderDishes;



}
