package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class DeviceBasicConfUpdateReqDTO implements Serializable {

    private static final long serialVersionUID = -2782247301702252131L;

    @NotEmpty(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @NotEmpty(message = "设备ID不得为空")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @NotNull(message = "显示模式不得为空")
    @Min(value = 0, message = "显示模式" +
            "制作点位显示模式：0=堂口模式，1=菜品汇总模式，2=订单模式1*4,3=订单模式1*6" +
            "出堂点位显示模式：0=单菜品模式，1=菜品汇总模式，2=订单模式1*4,3=订单模式1*6")
    @Max(value = 7, message = "显示模式" +
            "制作点位显示模式：0=堂口模式，1=菜品汇总模式，2=订单模式,3=订单模式1*6" +
            "出堂点位显示模式：0=单菜品模式，1=菜品汇总模式，2=订单模式,3=订单模式1*6")
    @ApiModelProperty(value = "显示模式" +
            "制作点位显示模式：0=堂口模式，1=菜品汇总模式，2=订单模式,3=订单模式1*6,4=混合订单模式1*6" +
            "出堂点位显示模式：0=单菜品模式，1=菜品汇总模式，2=订单模式,3=订单模式1*6,4=混合订单模式1*6")
    private Integer displayMode;

    @ApiModelProperty(value = "菜品展示类型 0：汇总中展示，订单中不展示；1：汇总和订单中都展示")
    private Integer itemDisplayType;
}
