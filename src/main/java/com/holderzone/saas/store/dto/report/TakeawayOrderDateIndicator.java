package com.holderzone.saas.store.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/12/06 上午 11:02
 * @description
 */
@ApiModel
public class TakeawayOrderDateIndicator {

    @ApiModelProperty("时间段")
    private List<String> dateTimeList;

    @ApiModelProperty("美团")
    private List<BigDecimal> meituanList;

    @ApiModelProperty("饿了么")
    private List<BigDecimal> eleList;

    @ApiModelProperty("总计")
    private List<BigDecimal> totalList;


    public List<BigDecimal> getTotalList() {
        return totalList;
    }

    public void setTotalList(List<BigDecimal> totalList) {
        this.totalList = totalList;
    }

    public List<String> getDateTimeList() {
        return dateTimeList;
    }

    public void setDateTimeList(List<String> dateTimeList) {
        this.dateTimeList = dateTimeList;
    }

    public List<BigDecimal> getMeituanList() {
        return meituanList;
    }

    public void setMeituanList(List<BigDecimal> meituanList) {
        this.meituanList = meituanList;
    }

    public List<BigDecimal> getEleList() {
        return eleList;
    }

    public void setEleList(List<BigDecimal> eleList) {
        this.eleList = eleList;
    }
}
