package com.holderzone.saas.store.dto.item.resp.price;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 价格方案编辑商品规格
 * @date 2021/9/28
 */
@Data
public class PlanPriceEditItemSkuDTO {

    @ApiModelProperty(value = "规格guid")
    private String skuGuid;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "规格")
    private String skuName;

    @ApiModelProperty(value = "销售价")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "会员价")
    private BigDecimal memberPrice;

    @ApiModelProperty("堂食核算价")
    private BigDecimal accountingPrice;

    @ApiModelProperty("外卖核算价")
    private BigDecimal takeawayAccountingPrice;
}
