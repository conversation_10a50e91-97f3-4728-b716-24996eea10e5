package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class DstBindItemReqDTO implements Serializable {

    private static final long serialVersionUID = -1158994112223699540L;

    @NotEmpty(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @NotEmpty(message = "设备ID不得为空")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @Valid
    @Nullable
    @ApiModelProperty(value = "绑定商品列表")
    private List<PrdDstItemBindDTO> bindItemSkuList;

    @Nullable
    @ApiModelProperty(value = "需解绑Sku列表")
    private List<String> unbindSkuGuidList;

    @ApiModelProperty(value = "是否绑定菜品分组")
    private Boolean bindingItemGroupFlag;

    @ApiModelProperty(value = "绑定菜品分组列表")
    private List<String> bindingItemGroups;
}
