package com.holderzone.saas.store.dto.report;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 反结账订单详情信息
 */
@Data
@ApiModel
@NoArgsConstructor
public class OrderRecoveryDetailDTO {

    @ApiModelProperty(notes = "基本信息,集合为0的元素为原订单")
    private List<OrderRecoveryDTO> orderRecoveryDTOList = Lists.newArrayList();

    /**
     * 结算信息
     */
    @Data
    @ApiModel(description = "结算Model")
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Balance {

        public static final String ORDER_MONEY_NAME = "订单金额";
        public static final String ORDER_DIS_NAME = "优惠金额";
        public static final String ORDER_TRUE_MONEY_NAME = "实收金额";

        /**
         * 项目名称
         */
        @ApiModelProperty(notes = "项目名称")
        private String item;

        /**
         * 金额（元）
         */
        @ApiModelProperty(notes = "金额（元）")
        private BigDecimal money;
    }


    /**
     * 优惠信息
     */
    @Data
    @ApiModel(description = "优惠Model")
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Discounts {

        public static final String TYPE_MEITUAN = "美团折扣";
        public static final String TYPE_ELEME = "美团折扣";
        public static final String TYPE_VIP = "会员折扣";
        public static final String TYPE_COUPON_ALL = "优惠券整单折扣";
        public static final String TYPE_COUPON_DISH = "优惠券菜品折扣";
        public static final String TYPE_ALL = "整单折扣";
        public static final String TYPE_DISH_DONATE = "菜品赠送";
        public static final String TYPE_VIP_BONUS = "会员积分";
        public static final String TYPE_SINGLE = "单品折扣";
        public static final String TYPE_SYS = "系统省零";
        public static final String TYPE_CHANGE = "整单让价";


        public static final Map<Integer, String> TYPEMAP = Maps.newHashMap();

        static {
            TYPEMAP.put(-2, TYPE_MEITUAN);
            TYPEMAP.put(-1, TYPE_ELEME);
            TYPEMAP.put(0, TYPE_VIP);
            TYPEMAP.put(1, TYPE_COUPON_ALL);
            TYPEMAP.put(2, TYPE_COUPON_DISH);
            TYPEMAP.put(3, TYPE_ALL);
            TYPEMAP.put(4, TYPE_DISH_DONATE);
            TYPEMAP.put(5, TYPE_VIP_BONUS);
            TYPEMAP.put(6, TYPE_SINGLE);
            TYPEMAP.put(7, TYPE_SYS);
            TYPEMAP.put(8, TYPE_CHANGE);
        }

        /**
         * 项目名称
         */
        @ApiModelProperty(notes = "项目名称")
        private String item;

        /**
         * 金额（元）
         */
        @ApiModelProperty(notes = "金额（元）")
        private BigDecimal money;

        /**
         * 备注
         */
        @ApiModelProperty(notes = "备注")
        private String remark;
    }


    /**
     * 支付信息
     */
    @Data
    @ApiModel(description = "支付Model")
    @NoArgsConstructor
    public static class Pay {

        public static final String PAY_MEITUAN = "美团支付";
        public static final String PAY_ELEME = "饿了么支付";
        public static final String PAY_MONEY = "现金支付";
        public static final String PAY_EGGS = "聚合支付";
        public static final String PAY_CARD_BANK = "银行卡支付";
        public static final String PAY_CARD_VIP = "会员卡支付";
        public static final String PAY_OTHER = "其他支付方式";


        public static final String getPayType(int payType) {
            switch (payType) {
                case -2:
                    return PAY_MEITUAN;
                case -1:
                    return PAY_ELEME;
                case 0:
                    return PAY_MONEY;
                case 1:
                    return PAY_EGGS;
                case 2:
                    return PAY_CARD_BANK;
                case 3:
                    return PAY_CARD_VIP;
                default:
                    return PAY_OTHER;

            }
        }

        /**
         * 支付方式
         */
        @ApiModelProperty(notes = "支付方式")
        private String payType;

        /**
         * 支付金额（元）
         */
        @ApiModelProperty(notes = "支付金额（元）")
        private BigDecimal money;

        /**
         * 优惠金额
         */
        @ApiModelProperty(notes = "优惠金额")
        private BigDecimal discountsMoney;

        /**
         * 第三方支付流水号
         */
        @ApiModelProperty(notes = "第三方支付流水号")
        private String outTradeNo;

        /**
         * 备注
         */
        @ApiModelProperty(notes = "备注")
        private String remark;
    }

    /**
     * 菜品
     */
    @Data
    @ApiModel(description = "菜品Model")
    @NoArgsConstructor
    public static class Dishes {

        @ApiModelProperty("订单编号")
        private String orderGuid;

        /**
         * 菜品编号
         */
        @ApiModelProperty(notes = "菜品编号")
        private String code;

        /**
         * 菜品名称
         */
        @ApiModelProperty(notes = "菜品名称")
        private String dishesName;

        /**
         * 单位
         */
        @ApiModelProperty(notes = "单位")
        private String unit;

        /**
         * 售卖价（元）
         */
        @ApiModelProperty(notes = "售卖价（元）")
        private BigDecimal price;

        /**
         * 数量
         */
        @ApiModelProperty(notes = "数量")
        private BigDecimal num;

        /**
         * 做法小计（元）
         */
        @ApiModelProperty(notes = "做法小计（元）")
        private BigDecimal doPrice;

        /**
         * 小计（元）
         */
        @ApiModelProperty(notes = "小计（元）")
        private BigDecimal totalMoney;

        /**
         * 规格名称
         */
        private String skuName;
        /**
         * 套餐子项
         */
        @ApiModelProperty(notes = "套餐子项")
        private List<SubDishe> subDishes;

        public void calculateTotalMoney() {
            if (this.doPrice != null) {
                this.totalMoney = this.price.add(this.doPrice).multiply(this.num);
            } else {
                this.totalMoney = this.price.multiply(this.num);
            }
        }
    }

    /**
     * 菜品
     */
    @Data
    @ApiModel(description = "子菜品Model")
    @NoArgsConstructor
    public static class SubDishe {

        @ApiModelProperty("订单编号")
        private String orderGuid;

        /**
         * 菜品编号
         */
        @ApiModelProperty(notes = "菜品编号")
        private String code;

        /**
         * 菜品名称
         */
        @ApiModelProperty(notes = "菜品名称")
        private String dishesName;

        /**
         * 单位
         */
        @ApiModelProperty(notes = "单位")
        private String unit;

        /**
         * 售卖价（元）
         */
        @ApiModelProperty(notes = "售卖价（元）")
        private BigDecimal price;

        /**
         * 数量
         */
        @ApiModelProperty(notes = "数量")
        private BigDecimal num;

        /**
         * 做法小计（元）
         */
        @ApiModelProperty(notes = "做法小计（元）")
        private BigDecimal doPrice;

        /**
         * 小计（元）
         */
        @ApiModelProperty(notes = "小计（元）")
        private BigDecimal totalMoney;

        /**
         * 规格名称
         */
        private String skuName;

        public void calculateTotalMoney() {
            if (this.doPrice != null) {
                this.totalMoney = this.price.add(this.doPrice).multiply(this.num);
            } else {
                this.totalMoney = this.price.multiply(this.num);
            }
        }
    }


    @Data
    @ApiModel(description = "菜品Model 包含主、子菜品")
    @NoArgsConstructor
    public static class AllDishes {
        @ApiModelProperty(notes = "订单Guid")
        private String orderGuid;
        @ApiModelProperty(notes = "订单菜品信息")
        private List<Dishes> dishes;
    }
}
