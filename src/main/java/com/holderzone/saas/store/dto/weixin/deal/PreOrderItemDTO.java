package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Data
@ApiModel("预订单商品项")
public class PreOrderItemDTO {

	@ApiModelProperty(value = "商品项名称")
	private String itemName;

	@ApiModelProperty(value = "商品类型:1.套餐，2.多规格，3.称重,4,单品,5,固定套餐")
	private Integer itemType;

	@ApiModelProperty(value = "商品图")
	private String pictureUrl;

	@ApiModelProperty(value = "规格名称")
	private String skuName;

	@ApiModelProperty(value = "计数单位")
	private String unit;

	@ApiModelProperty(value = "商品选择数量")
	private BigDecimal itemCount;

	@ApiModelProperty(value = "商品总价")
	private BigDecimal itemPrice;

	@ApiModelProperty(value = "true:有会员价，false：没有")
	private Boolean enablePreferentialPrice=false;

	@ApiModelProperty(value = "商品优惠价")
	private BigDecimal itemPreferentialPrice;

	private List<PreOrderSubItemDTO> preOrderSubItemDTOS;

	private List<PreOrderItemAttrDTO> preOrderItemAttrDTOList;
}
