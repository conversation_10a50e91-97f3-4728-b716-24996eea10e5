package com.holderzone.saas.store.dto.takeaway.request;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class CustomersDTO {

    @NotBlank(message = "姓名不得为空")
    @ApiModelProperty(value = "姓名")
    public String Name;

    @NotBlank(message = "电话不得为空")
    @ApiModelProperty(value = "电话")
    public String Phone;

    @ApiModelProperty(value = "微信Id")
    public String OpenId;

    @NotBlank(message = "客户地址不得为空")
    @ApiModelProperty(value = "客户地址")
    public String Address;

    @ApiModelProperty(value = "门牌号")
    public String HouseNumbe;

    @NotBlank(message = "下单时间不得为空")
    @ApiModelProperty(value = "下单时间")
    private String CreateTime;

    @ApiModelProperty(value = "预计送达时间或取餐时间")
    private String TakeMealTime;

    @NotNull(message = "经度不得为空")
    @ApiModelProperty(value = "经度")
    public BigDecimal Longitude;

    @NotNull(message = "纬度不得为空")
    @ApiModelProperty(value = "纬度")
    public BigDecimal Latitude;

}
