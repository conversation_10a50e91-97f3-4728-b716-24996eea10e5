package com.holderzone.saas.store.dto.weixin.cp;

import lombok.Data;

import java.io.Serializable;

/**
 * 赚餐门店 二维码参数
 */
@Data
public class StoreQrCodeDTO implements Serializable {

    private static final long serialVersionUID = -2401410161678976568L;


    private String enterpriseGuid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 桌台guid
     */
    private String diningTableGuid;

    /**
     * appId
     */
    private String appId;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 跳转地址
     */
    private String jumpUrl;
}
