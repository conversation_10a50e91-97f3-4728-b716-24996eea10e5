package com.holderzone.saas.store.dto.retail.dinein;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CreateDineInOrderReqDTO
 * @date 2019/01/04 9:50
 * @description 创建堂食订单
 * @program holder-saas-store-dto
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class RetailRemarkReqDTO extends BaseDTO {

    private static final long serialVersionUID = -1281286881279843330L;

    @ApiModelProperty(value = "订单的guid")
    @LockField
    @OrderLockField
    private String guid;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "商品")
    private List<DineInItemDTO> dineInItemDTOS;

}
