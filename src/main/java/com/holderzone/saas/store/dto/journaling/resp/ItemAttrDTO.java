package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "报表-属性加价DTO")
public class ItemAttrDTO {

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "order_item_guid")
    private String orderItemGuid;

    @ApiModelProperty(value = "order_guid")
    private String orderGuid;

    @ApiModelProperty(value = "store_guid")
    private String storeGuid;

    @ApiModelProperty(value = "数量")
    private double num;

    @ApiModelProperty(value = "属性价格")
    private double attrPrice;

    @ApiModelProperty(value = "总价")
    private double totalPrice;
}
