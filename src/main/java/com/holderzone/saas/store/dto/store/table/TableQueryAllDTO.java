package com.holderzone.saas.store.dto.store.table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableQueryAllDTO
 * @date 2018/07/24 上午11:39
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class TableQueryAllDTO implements Serializable {

    private static final long serialVersionUID = 1314706140400974461L;

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 区域guid
     */
    @ApiModelProperty(value = "区域guid，为空则查询所有区域")
    private String areaGuid;

    /**
     * 桌台名称/桌台编号
     */
    @ApiModelProperty(value = "桌台名称/桌台编号，为空则查询所有名称/编号")
    private String nameOrCode;
}
