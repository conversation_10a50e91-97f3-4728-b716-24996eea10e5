package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/26
 * @since 1.8
 */
@Data
@ApiModel("出入库单物料导入实体")
public class InOutDocumentMaterialImportDTO {

    @ApiModelProperty("仓库Guid")
    private String warehouseGuid;

    @ApiModelProperty("门店Guid")
    private String storeGuid;

    @ApiModelProperty("供应商Guid")
    private String supplierGuid;

    @ApiModelProperty("已经添加的物料Guid列表，用英文逗号隔开")
    private String materialGuidList;

    @ApiModelProperty("出入库单物料详情导入实体列表")
    List<InOutDocumentMaterialDetailImportDTO> inOutDocumentMaterialDetailImportDTOList;
}
