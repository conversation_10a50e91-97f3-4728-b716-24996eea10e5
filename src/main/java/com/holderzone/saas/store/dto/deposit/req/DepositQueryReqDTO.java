package com.holderzone.saas.store.dto.deposit.req;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
public class DepositQueryReqDTO extends BasePageDTO {

    private static final long serialVersionUID = -2773042054434843563L;

    @ApiModelProperty(value = "门店Guid")
    @NotNull(message = "门店Guid不能为空")
    private String storeGuid;

    @ApiModelProperty(value = "查询条件")
    @NotNull(message = "查询条件不能为空")
    private String condition;

    @ApiModelProperty(value = "phoneGuid 作为查询条件，后端使用字段，前端不用管")
    private String phoneGuid;

    @ApiModelProperty(value = "wxGuid 作为查询条件，后端使用字段，前端不用管")
    private String wxGuid;

}
