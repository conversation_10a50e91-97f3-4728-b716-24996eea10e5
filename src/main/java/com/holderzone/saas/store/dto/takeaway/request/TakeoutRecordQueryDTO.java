package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 修复审核列表查询DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "修复审核列表查询DTO")
public class TakeoutRecordQueryDTO extends BasePageDTO implements Serializable {

    private static final long serialVersionUID = -1662477501604534237L;
}
