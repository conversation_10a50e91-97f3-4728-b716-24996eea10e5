package com.holderzone.saas.store.dto.deposit.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class QueryDepositDetailReqDTO implements Serializable {

    private static final long serialVersionUID = -2773042054434843563L;

    @ApiModelProperty(value = "寄存记录Guid")
    @NotNull(message = "寄存记录Guid不得为空")
    private String depositGuid;

}
