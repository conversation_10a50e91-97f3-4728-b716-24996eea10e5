package com.holderzone.saas.store.dto.report.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/25
 * @description 云收款统计返回
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "云收款统计查询", description = "云收款统计查询")
public class CloudPayConstituteRespDTO implements Serializable {

    private static final long serialVersionUID = 1631066300966166516L;

    @ApiModelProperty(value = "收款code")
    private Integer payCode;

    @ApiModelProperty(value = "收款方式")
    private String payMethod;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalAmount;

}
