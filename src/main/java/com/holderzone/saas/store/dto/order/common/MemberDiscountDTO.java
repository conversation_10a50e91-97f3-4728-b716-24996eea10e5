package com.holderzone.saas.store.dto.order.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberDiscountDTO
 * @date 2019/05/21 14:30
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class MemberDiscountDTO {

    @ApiModelProperty(value = "商品(并单时为所有订单商品)")
    private List<DineInItemDTO> dineInItemDTOS;

    @ApiModelProperty(value = "优惠券id（入参）")
    private List<String> couponIds;

    @ApiModelProperty(value = "打几折（入参）")
    private BigDecimal memberDiscount;

    @ApiModelProperty(value = "优惠券优惠金额合计（保留2位小数返回）")
    private BigDecimal totalCouponDiscount;

    @ApiModelProperty(value = "会员折扣优惠金额合计（保留2位小数返回）")
    private BigDecimal totalMemberDiscount;


}
