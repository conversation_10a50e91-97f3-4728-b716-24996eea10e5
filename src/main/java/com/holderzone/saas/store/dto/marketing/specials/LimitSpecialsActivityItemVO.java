package com.holderzone.saas.store.dto.marketing.specials;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.holderzone.saas.store.enums.marketing.ApplyBusinessTypeEnum;
import com.holderzone.saas.store.enums.marketing.DataUnitEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/5/28
 * @description 限时特价活动商品返回
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "限时特价活动商品返回")
public class LimitSpecialsActivityItemVO implements Serializable {

    private static final long serialVersionUID = -7503721853573933952L;

    /**
     * 商品id
     */
    private String commodityId;

    /**
     * 商品编码
     */
    private String commodityCode;

    /**
     * 特价类型 1打折 2减价 3指定价格
     */
    private Integer specialsType;

    /**
     * 特价数额
     */
    private BigDecimal specialsNumber;

    /**
     * 优惠限购
     * 为空表示不限制
     */
    private Integer limitNumber;

    /**
     * 商品使用数量
     * 为空表示不限制
     */
    private Integer itemUseNumber;

    /**
     * 活动guid
     */
    @ApiModelProperty(value = "活动guid")
    private String activityGuid;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private String activityCode;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    private LocalDateTime endTime;

    /**
     * 活动状态
     * 0-未发布 1-已发布 2-已暂停
     * ActivityStateEnum
     */
    @ApiModelProperty(value = "活动状态 0-未发布 1-已发布 2-已暂停")
    private Integer state;

    /**
     * 是否限制活动时段 0-否 1-是
     */
    @ApiModelProperty(value = "是否限制活动时段 0-否 1-是")
    private Integer isLimitPeriod;

    /**
     * 限制时段类型 -1:自定义 0：日 1：周 2：月 3：年
     *
     * @see DataUnitEnum
     */
    @ApiModelProperty(value = "限制时段类型 -1:自定义 0：日 1：周 2：月 3：年")
    @TableField(strategy = FieldStrategy.IGNORED)
    private Integer limitPeriodType;

    /**
     * 限制时段限制类型json
     *
     * @see EffectiveTimeVO
     */
    @ApiModelProperty(value = "限制时段限制类型json")
    @TableField(strategy = FieldStrategy.IGNORED)
    private String limitPeriodJson;

    /**
     * 活动规则
     * 共享互斥关系 0-互斥 1-共享
     */
    @ApiModelProperty(value = "活动规则 共享互斥关系 0-互斥 1-共享")
    private Integer relationRule;

    /**
     * 适用场景 0:全部业务 1：部分业务
     *
     * @see ApplyBusinessTypeEnum
     */
    @ApiModelProperty(value = "适用场景 0:全部业务 1：部分业务")
    private Integer applyBusiness;

    /**
     * 适用场景json
     * 多个用逗号隔开
     */
    @ApiModelProperty(value = "适用场景json")
    private String applyBusinessJson;

}
