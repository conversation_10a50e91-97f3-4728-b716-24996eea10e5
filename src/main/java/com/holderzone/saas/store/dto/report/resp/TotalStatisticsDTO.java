package com.holderzone.saas.store.dto.report.resp;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TotalStatisticsDTO {

    public String brandGuid;

    public String storeGuid;

    @ApiModelProperty(hidden = true)
    public String goodsGuid;

    @ApiModelProperty(value = "总数量")
    public BigDecimal totalQuantity;

    @ApiModelProperty(value = "总金额")
    public BigDecimal totalMoney;

    @ApiModelProperty(value = "实退金额")
    public BigDecimal actuallyRefundFee;

    public static TotalStatisticsDTO DEFAULT = new TotalStatisticsDTO("", "", "",
            BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);

}
