package com.holderzone.saas.store.dto.member.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 *<AUTHOR>
 *@version 1.0
 *@className HandoverReqDTO
 *@date 2019/04/08 17:12
 *@description //TODO
 *@program holder-saas-aggregation-app
 */
@Data
public class HandoverReqDTO {

    @ApiModelProperty(value = "门店guid", hidden = true) private String storeGuid;
    @NotBlank(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始时间，格式yyyy-MM-dd")
    private String beginTime;
    @NotBlank(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间，格式yyyy-MM-dd")
    private String endTime;
    @ApiModelProperty(value = "用户guid", hidden = true)
    private String staffGuid;


    public String getBeginTime() {
        if(beginTime.length()==10){
            return beginTime+" 00:00:00";
        }else {
            return beginTime;
        }
    }
    public String getEndTime() {
        if(endTime.length()==10){
            return endTime+" 23:59:59";
        }else {
            return endTime;
        }
    }
}
