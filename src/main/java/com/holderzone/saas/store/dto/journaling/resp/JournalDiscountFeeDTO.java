package com.holderzone.saas.store.dto.journaling.resp;

import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className journalDiscountFeeDTO
 * @date 2019/06/10 15:18
 * @description 优惠详细信息
 * @program holder-saas-store-dto
 */
@Data
public class JournalDiscountFeeDTO extends DiscountFeeDetailDTO implements Serializable {

    private static final long serialVersionUID = 3649606809050810194L;
    @ApiModelProperty(value = "订单guid")
    private String orderGuid;
}
