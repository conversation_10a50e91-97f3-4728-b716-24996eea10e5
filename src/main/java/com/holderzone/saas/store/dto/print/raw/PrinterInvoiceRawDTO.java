package com.holderzone.saas.store.dto.print.raw;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class PrinterInvoiceRawDTO implements Serializable {

    private static final long serialVersionUID = 4355151640787064236L;

    /**
     * 唯一标识
     */
    private String guid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 打印机guid
     */
    private String printerGuid;

    /**
     * 票据类型
     */
    private Integer invoiceType;

    /**
     * 票据名称
     */
    private String invoiceName;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;
}