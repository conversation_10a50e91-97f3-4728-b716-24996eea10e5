package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreCityDTO
 * @date 2019/05/18 18:31
 * @description 微信门店城市DTO
 * @program holder-saas-store
 */
@Data
@ApiModel("微信门店城市DTO")
public class WxStoreCityDTO {

    @ApiModelProperty("城市名字")
    private String cityName;

    @ApiModelProperty("城市code")
    private String code;

    @ApiModelProperty("门店数量")
    private Integer count;
}
