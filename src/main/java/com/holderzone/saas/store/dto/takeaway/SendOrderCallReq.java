package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class SendOrderCallReq implements Serializable {

    @NotBlank(message = "推送记录guid不能为空")
    @ApiModelProperty(value = "推送记录guid", required = true)
    private String guid;

    @ApiModelProperty("执行时间戳")
    private LocalDateTime longTime;

    /**
     * oder主键
     */
    private String orderGuid;

    /**
     * 顾客隐私手机号
     */
    private String phone;

    /**
     * 数据批次
     */
    private String batch;

    /**
     * 商户标识
     */
    private String enterpriseGuid;
}
