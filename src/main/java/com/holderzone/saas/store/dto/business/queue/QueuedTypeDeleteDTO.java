package com.holderzone.saas.store.dto.business.queue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueuedTypeCreateDTO
 * @date 2018/07/27 下午4:35
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class QueuedTypeDeleteDTO {

    /**
     * 队列guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "队列guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "队列guid", required = true)
    private String queuedTypeGuid;
}
