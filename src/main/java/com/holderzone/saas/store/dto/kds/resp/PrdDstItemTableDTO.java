package com.holderzone.saas.store.dto.kds.resp;

import com.holderzone.saas.store.enums.kds.KdsItemStateEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class PrdDstItemTableDTO implements Serializable {

    private static final long serialVersionUID = 4223797150524466678L;

    @NotNull(message = "屏幕显示类型不得为空")
    @ApiModelProperty(value = "屏幕显示类型", required = true)
    private Integer displayType;

    @NotBlank(message = "订单Guid不得为空")
    @ApiModelProperty(value = "订单Guid", required = true)
    private String orderGuid;

    @NotBlank(message = "订单描述不得为空")
    @ApiModelProperty(value = "根据交易模式：桌台名、快餐、饿了么|美团", required = true)
    private String orderDesc;

    @NotBlank(message = "订单号不得为空")
    @ApiModelProperty(value = "根据交易模式：订单号、订单号、订单号", required = true)
    private String orderNumber;

    @NotBlank(message = "订单流水号不得为空")
    @ApiModelProperty(value = "根据交易模式：桌台名、牌号、外卖日流水号", required = true)
    private String orderSerialNo;

    @NotBlank(message = "厨房菜品Guid不得为空")
    @ApiModelProperty(value = "厨房菜品Guid", required = true)
    private String kitchenItemGuid;

    @NotBlank(message = "区域Guid不得为空")
    @ApiModelProperty(value = "区域Guid", required = true)
    private String areaGuid;

    @NotNull(message = "是否已催单不得为空")
    @ApiModelProperty(value = "是否已催单", required = true)
    private Boolean isUrged;

    @NotNull(message = "是否已挂起不得为空")
    @ApiModelProperty(value = "是否已挂起", required = true)
    private Boolean isHanged;

    @ApiModelProperty(value = "是否加菜")
    private Boolean isAddItem;

    /**
     * 根据 KitchenItemDTO 生成 PrdDstItemTableDTO
     *
     * @param kitchenItemDTO
     * @return
     */
    public static PrdDstItemTableDTO ofKitchenItemDTO(KitchenItemDTO kitchenItemDTO) {
        PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemTableDTO.setDisplayType(kitchenItemDTO.getDisplayType());
        prdDstItemTableDTO.setOrderGuid(kitchenItemDTO.getOrderGuid());
        prdDstItemTableDTO.setOrderDesc(kitchenItemDTO.getOrderDesc());
        prdDstItemTableDTO.setOrderNumber(kitchenItemDTO.getOrderNumber());
        prdDstItemTableDTO.setOrderSerialNo(kitchenItemDTO.getOrderSerialNo());
        prdDstItemTableDTO.setKitchenItemGuid(kitchenItemDTO.getGuid());
        prdDstItemTableDTO.setAreaGuid(kitchenItemDTO.getAreaGuid());
        prdDstItemTableDTO.setIsUrged(null != kitchenItemDTO.getUrgedTime());
        prdDstItemTableDTO.setIsHanged(KdsItemStateEnum.HANG_UP.getCode() == kitchenItemDTO.getItemState());
        return prdDstItemTableDTO;
    }
}