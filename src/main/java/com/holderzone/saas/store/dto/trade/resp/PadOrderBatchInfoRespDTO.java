package com.holderzone.saas.store.dto.trade.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description pad点餐批次信息
 * @date 2021/9/1 10:31
 * @className: PadOrderBatchInfoRespDTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "pad点餐批次信息")
public class PadOrderBatchInfoRespDTO implements Serializable {

    private static final long serialVersionUID = 3972640527700693342L;

    /**
     * pad下单guid
     */
    @ApiModelProperty(value = "pad下单guid")
    private Long padOrderGuid;

    /**
     * pad点餐批次
     */
    @ApiModelProperty("pad点餐批次")
    private Integer padBatch;

    /**
     * 消费合计：普通价和会员价
     */
    @ApiModelProperty(value = "消费合计：普通价和会员价")
    private PadPriceRespDTO priceRespDTO;

    /**
     * see{@link com.holderzone.saas.store.enums.order.OrderStateEnum}
     * 下单状态 0:待处理，1：接单，2：拒单，3：未结帐，4：已结账,5:已做废, 8订单已失效,9.已退菜
     */
    @ApiModelProperty("下单状态")
    private Integer orderState;

    /**
     * pad点餐批次商品信息
     */
    @ApiModelProperty(value = "pad点餐批次商品信息")
    private List<PadOrderBatchItemInfoRespDTO> batchItemInfoList;
}
