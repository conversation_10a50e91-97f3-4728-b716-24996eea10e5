package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("微信商品SKU")
public class ItemInfoSkuDTO {

	@ApiModelProperty(value = "规格GUID")
	private String skuGuid;
	@ApiModelProperty(value = "品牌规格GUID")
	private String parentGuid;
	@ApiModelProperty(value = "规格名称")
	private String name;
	@ApiModelProperty(value = "计数单位")
	private String unit;
	@ApiModelProperty(value = "sku编号")
	private String code;
	@ApiModelProperty(value = "售价")
	private BigDecimal salePrice;
	@ApiModelProperty(value = "true:有会员价")
	private Boolean enablePreferentialPrice=false;
	@ApiModelProperty(value = "会员价格")
	private BigDecimal memberPrice=BigDecimal.ZERO;

	/**
	 * 核算价
	 */
	@ApiModelProperty("核算价")
	private BigDecimal accountingPrice;


	/**
	 * 划线价
	 */
	@ApiModelProperty("划线价")
	private BigDecimal linePrice;

	@ApiModelProperty(value = "起卖数(非称重即为整数，称重即为小数)")
	private BigDecimal minOrderNum;
	@ApiModelProperty(value = "是否加入整单折扣(0：否，1：是)")
	private Integer isWholeDiscount;
	@ApiModelProperty(value = "是否参与会员折扣（0：否，1：是")
	private Integer isMemberDiscount;
	@ApiModelProperty(value = "1表示选中，0非选中")
	private Integer uck=0;
	@ApiModelProperty(value = "是否估清 1:否 2:是")
	private Integer isSoldOut = 1;
	@ApiModelProperty(value = "当前剩余数量")
	private BigDecimal residueQuantity=new BigDecimal("-1");

	@ApiModelProperty("满减活动规则描述")
	private List<String> fullReductionStrList;
	@ApiModelProperty("满折活动规则描述")
	private List<String> fullDiscountStrList;

	/**
	 * 活动规则描述
	 * 顺序：1限时特价 2满减 3满折
	 */
	@ApiModelProperty("活动规则描述")
	private List<ActivityRuleDescDTO> activityRuleDescList;

}
