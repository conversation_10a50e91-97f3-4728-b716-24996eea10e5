package com.holderzone.saas.store.dto.takeaway.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class HolderAuthDTO {

    private static final long serialVersionUID = 5466838236697202547L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 唯一标识
     */
    private String guid;

    /**
     * 商户GUID
     */
    private String enterpriseGuid;

    /**
     * 门店GUID，即ePoiId
     */
    private String storeGuid;

    /**
     * 饿了么商户ID
     */
    private Long userId;

    /**
     * 饿了么商户名称
     */
    private String userName;

    /**
     * 商户编码
     */
    private String code;

    /**
     * 访问token
     */
    private String accessToken;

    /**
     * 刷新token
     */
    private String refreshToken;


    /**
     * token生效时间
     */
    private LocalDateTime activeTime;

    /**
     * token有效时间，单位秒
     */
    private Long expires;

    /**
     * token过期时间
     */
    private LocalDateTime expireTime;


    /**
     * token生效时间
     */
    private LocalDateTime refreshActiveTime;

    /**
     * token有效时间，单位秒
     */
    private Long refreshExpires;

    /**
     * token过期时间
     */
    private LocalDateTime refreshExpireTime;


    /**
     * 逻辑删除：0=未删除，1=已删除
     */
    private Boolean deleted;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;


}
