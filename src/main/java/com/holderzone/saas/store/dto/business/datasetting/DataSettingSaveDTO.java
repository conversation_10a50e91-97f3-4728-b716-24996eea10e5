package com.holderzone.saas.store.dto.business.datasetting;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/8
 * @since 1.8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel
public class DataSettingSaveDTO {

    @ApiModelProperty(value = "品牌的guid")
    @NotNull
    private String brandGuid;

    @ApiModelProperty(value = "数据取值设置的集合")
    private List<DataSettingDTO> dataSettingDTOList;
}
