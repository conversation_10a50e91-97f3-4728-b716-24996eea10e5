package com.holderzone.saas.store.dto.erp;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.erp.util.Add;
import com.holderzone.saas.store.dto.erp.util.Update;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2019/04/25 14:59
 */
public class MaterialDTO extends BaseDTO {
    @ApiModelProperty("GUID")
    @NotEmpty(message = "物料GUID不能为空", groups = Update.class)
    private String guid;
    @ApiModelProperty("企业GUID")
    private String enterpriseGuid;
    @ApiModelProperty("门店GUID")
    private String storeGuid;

    @ApiModelProperty("仓库GUID")
    private String warehouseGuid;

    @ApiModelProperty("物品性质")
    private String property;
    @ApiModelProperty("名称")
    @NotEmpty(message = "物料名称不能为空")
    @Size(max = 40, min = 1)
    private String name;
    @ApiModelProperty("简称")
    @Size(max = 40, min = 1, groups = {Update.class, Add.class},message = "简码长度必须介于1-40之间")
    private String simpleName;
    @ApiModelProperty("单位")
    @NotEmpty(message = "主单位不能为空")
    private String unit;
    @ApiModelProperty("单位名称")
    private String unitName;
    @ApiModelProperty("辅助单位")
    private String auxiliaryUnit;
    @ApiModelProperty("辅助单位名称")
    private String auxiliaryUnitName;
    @ApiModelProperty("最低库存")
    private BigDecimal lowestStock;
    @ApiModelProperty("实时库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "入库单价")
    private BigDecimal inUnitPrice;

    @ApiModelProperty("所属分类GUID")
    private String category;

    @ApiModelProperty("所属分类名称")
    private String categoryName;

    @ApiModelProperty(value = "类型 0:物资,1/原料")
    @NotEmpty(message = "类型不能为空", groups = Add.class)
    private String type;
    @ApiModelProperty("编码")
    @NotEmpty(message = "编码不能为空", groups = Add.class)
    @Size(min = 6, max = 6)
    private String code;
    @ApiModelProperty("条码")
    private String barCode;
    @ApiModelProperty("规格")
    private String specs;
    @ApiModelProperty(value = "换算的主单位", example = "0")
    private Long conversionMain;
    @ApiModelProperty(value = "换算的辅单位", example = "0")
    private Long conversionAuxiliary;
    @ApiModelProperty(value = "是否启用:0/禁用;1/启用", example = "1")
    private Integer enabled;
    @ApiModelProperty(value = "是否删除:0/否;1/是", example = "0")
    private Integer deleted;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;
    @ApiModelProperty("备注")
    private String remark;

    public BigDecimal getInUnitPrice() {
        return inUnitPrice;
    }

    public void setInUnitPrice(BigDecimal inUnitPrice) {
        this.inUnitPrice = inUnitPrice;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getEnterpriseGuid() {
        return enterpriseGuid;
    }

    public void setEnterpriseGuid(String enterpriseGuid) {
        this.enterpriseGuid = enterpriseGuid;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSimpleName() {
        return simpleName;
    }

    public void setSimpleName(String simpleName) {
        this.simpleName = simpleName;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getAuxiliaryUnit() {
        return auxiliaryUnit;
    }

    public void setAuxiliaryUnit(String auxiliaryUnit) {
        this.auxiliaryUnit = auxiliaryUnit;
    }

    public BigDecimal getLowestStock() {
        return lowestStock;
    }

    public void setLowestStock(BigDecimal lowestStock) {
        this.lowestStock = lowestStock;
    }

    public BigDecimal getStock() {
        return stock;
    }

    public void setStock(BigDecimal stock) {
        this.stock = stock;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getSpecs() {
        return specs;
    }

    public void setSpecs(String specs) {
        this.specs = specs;
    }

    public Long getConversionMain() {
        return conversionMain;
    }

    public void setConversionMain(Long conversionMain) {
        this.conversionMain = conversionMain;
    }

    public Long getConversionAuxiliary() {
        return conversionAuxiliary;
    }

    public void setConversionAuxiliary(Long conversionAuxiliary) {
        this.conversionAuxiliary = conversionAuxiliary;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getAuxiliaryUnitName() {
        return auxiliaryUnitName;
    }

    public void setAuxiliaryUnitName(String auxiliaryUnitName) {
        this.auxiliaryUnitName = auxiliaryUnitName;
    }
}
