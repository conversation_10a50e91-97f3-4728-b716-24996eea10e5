package com.holderzone.saas.store.dto.business.datasetting;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/8
 * @since 1.8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel
public class DataSettingQueryDTO {

    @ApiModelProperty(value = "品牌的guid")
    private String brandGuid;

    @ApiModelProperty(value = "门店的guid")
    private String storeGuid;

    @ApiModelProperty(value = "数据取值的类型（1：正餐点餐页验券加购商品取值）")
    private List<Integer> dataSettingTypeList;
}
