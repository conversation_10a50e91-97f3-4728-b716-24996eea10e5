package com.holderzone.saas.store.dto.order.request.bill;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className JhQueryReqDTO
 * @date 2018/08/14 11:45
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class RecoveryReqDTO extends BaseDTO {


    @ApiModelProperty(value = "订单guid")
    @OrderLockField
    private String orderGuid;

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "授权人guid")
    private String authStaffGuid;

    @ApiModelProperty(value = "授权人名称")
    private String authStaffName;

    @ApiModelProperty(value = "授权人图片")
    private String authStaffPicture;

    @ApiModelProperty(value = "反结账设备类型")
    private Integer recoveryDeviceType;

    @ApiModelProperty(value = "是否人脸支付")
    private boolean facePay;

    @ApiModelProperty("是否是食堂消费")
    private Boolean canteenPay;

    @ApiModelProperty("若是食堂消费判断用了实体卡还是电子卡，1电子卡2实体卡")
    private Integer canteenPayType;

    @ApiModelProperty("是否退菜")
    private Boolean returnItemFlag;
}