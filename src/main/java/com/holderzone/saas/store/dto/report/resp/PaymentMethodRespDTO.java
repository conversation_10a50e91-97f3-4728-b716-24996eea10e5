package com.holderzone.saas.store.dto.report.resp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.holderzone.saas.store.util.BigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/2/23
 * @description 收款方式返回信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "收款方式返回信息", description = "收款方式返回信息")
public class PaymentMethodRespDTO implements Serializable {

    private static final long serialVersionUID = 8612515557779691719L;

    @ApiModelProperty(value = "收款方式")
    private String payMethod;

    @JsonSerialize(using = BigDecimalSerialize.class)
    @ApiModelProperty(value = "销售收入")
    private BigDecimal salesRevenue;

    @ApiModelProperty(value = "销售收入占比")
    private String salesRevenueProportion;

    @JsonSerialize(using = BigDecimalSerialize.class)
    @ApiModelProperty(value = "会员充值收入")
    private BigDecimal memberRecharge;

    @JsonSerialize(using = BigDecimalSerialize.class)
    @ApiModelProperty(value = "预定定金")
    private BigDecimal deposit;

    @JsonSerialize(using = BigDecimalSerialize.class)
    @ApiModelProperty(value = "合计")
    private BigDecimal total;

    @JsonSerialize(using = BigDecimalSerialize.class)
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceCharge;

    @JsonSerialize(using = BigDecimalSerialize.class)
    @ApiModelProperty(value = "预计实收")
    private BigDecimal actualIncome;
}
