package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 方案与门店绑定关系返回对象
 * @date 2020/11/16 10:14
 */
@ApiModel(value = "方案与门店绑定关系返回对象")
@Data
public class PricePlanBingStoreRespDTO {

    @ApiModelProperty(value = "方案与门店绑定GUID")
    private String guid;

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "方案GUID")
    private String planGuid;

    @ApiModelProperty(value = "关联门店GUID")
    private String storeGuid;
}
