package com.holderzone.saas.store.dto.report.openapi.shiyuanhui;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 销售明细订单列表
 */
@Data
public class SaleDetailShiYuanHuiRespDTO implements Serializable {

    private static final long serialVersionUID = -1025167689243809355L;

    /**
     * 订单guid
     */
    @JsonIgnore
    private String orderGuid;

    /**
     * 门店guid
     */
    @JsonProperty("StoreId")
    private String storeGuId;

    /**
     * 门店名称
     */
    @JsonProperty("StoreName")
    private String storeName;

    @JsonProperty("PosNO")
    private String posNO;

    /**
     * 结账人
     */
    @JsonProperty("Cashier")
    private String checkoutStaffName;

    /**
     * 结账时间
     */
    @JsonProperty("BizDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkoutTime;

    /**
     * 销售金额
     */
    @JsonProperty("SaleAmount")
    private Long orderFee;

    /**
     * 销售笔数
     */
    @JsonProperty("SaleCount")
    private Integer saleCount;

    /**
     * 优惠金额
     */
    @JsonProperty("PreferentialAmount")
    private Long discountFee;

    /**
     * 实收金额
     */
    @JsonProperty("FundsAmount")
    private Long actuallyPayFee;

    /**
     * 小票号
     * 订单号
     */
    @JsonProperty("ReceiptCode")
    private String orderNo;

    /**
     * 原订单号，退货传
     */
    @JsonProperty("OriginalReceiptCode")
    private String originalOrderNo;

    /**
     * 交易类型:0销售,1退货
     */
    @JsonProperty("ReceiptType")
    private Integer receiptType;

    /**
     * 订单备注
     */
    @JsonProperty("Note")
    private String remark;

    /**
     * 最后修改时间
     */
    @JsonProperty("UpdateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    /**
     * 商品明细
     */
    @JsonProperty("ProductDetail")
    private List<SaleProductDetailShiYuanHuiRespDTO> productDetails;

    /**
     * 支付明细
     */
    @JsonProperty("PayDetail")
    private List<SalePayDetailShiYuanHuiRespDTO> payDetails;

}
