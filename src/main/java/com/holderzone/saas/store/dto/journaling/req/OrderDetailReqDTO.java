package com.holderzone.saas.store.dto.journaling.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderDetailFeignReqDTO
 * @date 2019/05/29 10:35
 * @description 报表-订单明细feign调用请求入参
 * @program holder-saas-store
 */
@Data
@ApiModel(value = "报表-订单明细feign调用请求入参")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderDetailReqDTO extends JournalWebBaseReqDTO {

    @ApiModelProperty(value = "餐饮版订单状态（1：未结账， 2：已结账， 3：已退款，4：已作废）， 商超订单状态（4 已完成， 5 已退款")
    private Integer state;

    @ApiModelProperty(value = "用餐类型（0：正餐，1：快餐），不传查全部 商超版不传")
    private Integer tradeMode;

    @ApiModelProperty(value = "start")
    private Integer start;

    @ApiModelProperty(value = "操作人guid集合")
    private List<String> createStaffGuidList;

    public int getStart() {
        return (int) ((this.getCurrentPage() <= 0 ? 1 : this.getCurrentPage() - 1) * this.getPageSize());
    }
}
