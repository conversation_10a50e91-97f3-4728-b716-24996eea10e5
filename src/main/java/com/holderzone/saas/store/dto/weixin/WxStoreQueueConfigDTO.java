package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreQueueConfigDTO
 * @date 2019/05/17 20:36
 * @description 门店微信排队功能配置信息DTO
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("门店微信排队功能配置信息DTO")
public class WxStoreQueueConfigDTO {
    @ApiModelProperty("是否展示排队按钮，true 是， false 否")
    private Boolean showQueueButton;

    @ApiModelProperty("是否可排队，true可排队，false暂停排队")
    private Boolean couldQueue;

    @ApiModelProperty("是否需要排队，true 需排队，false 无需排队")
    private Boolean needQueue;

    @ApiModelProperty("最大取号距离，若distant>maxDistant 显示超过取号距离")
    private Integer maxDistant;

    @ApiModelProperty(value = "是否开启距离限制，true开启，false关闭", notes = "关闭时最大取号距离字段无效")
    private Boolean distantConstraint;
}
