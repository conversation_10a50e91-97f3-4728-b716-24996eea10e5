package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateForManualReqDTO
 * @date 2019/05/16 16:30
 * @description 安卓手动修改估清商品状态请求DTO
 * @program holder-saas-aggregation-app
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "安卓手动估清商品状态请求DTO")
public class EstimateForManualReqDTO extends BaseDTO {

    private static final long serialVersionUID = 8842785991231598134L;

    @NotNull(message = "商品Guid不能为空")
    @ApiModelProperty(value = "商品 GUID")
    private String itemGuid;

    /**
     * 是否禁售 1:否 2:是
     */
    @ApiModelProperty(value = "是否禁售 1:否 2:是")
    private Integer isSoldOut;

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 估清商品skuGuid的集合
     */
    private List<String> skuGuidList;

    /**
     * 商品类型
     * 1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品 5.团餐。
     */
    private Integer itemType;
}
