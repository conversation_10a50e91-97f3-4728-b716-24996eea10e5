package com.holderzone.saas.store.dto.erp;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @className SuppliersReconciliationQueryDTO
 * @date 2019-05-10 11:06:41
 * @description
 * @program holder-saas-store-dto
 */
public class SuppliersReconciliationQueryDTO extends BasePageDTO {

    @ApiModelProperty(value = "企业或者门店guid")
    private String guid;
    @ApiModelProperty("供应商Guid")
    private String supplierGuid;
    @ApiModelProperty("对账单状态")
    private Integer status = -1;
    @ApiModelProperty(value = "开始时间")
    private Long startDate;
    @ApiModelProperty(value = "结束时间")
    private Long endDate;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getSupplierGuid() {
        return supplierGuid;
    }

    public void setSupplierGuid(String supplierGuid) {
        this.supplierGuid = supplierGuid;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getStartDate() {
        return startDate;
    }

    public void setStartDate(Long startDate) {
        this.startDate = startDate;
    }

    public Long getEndDate() {
        return endDate;
    }

    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }
}
