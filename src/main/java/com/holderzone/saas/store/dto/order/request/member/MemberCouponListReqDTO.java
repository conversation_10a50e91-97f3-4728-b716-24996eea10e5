package com.holderzone.saas.store.dto.order.request.member;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberCouponListReqDTO
 * @date 2019/12/26 11:52
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class MemberCouponListReqDTO extends BaseDTO {

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;
    @ApiModelProperty(value = "会员guid", required = true)
    private String memberInfoGuid;
    @ApiModelProperty(value = "会员卡guid")
    private String memberInfoCardGuid;



    @ApiModelProperty("已勾选的优惠券码")
    private List<String> volumeCodes;


}
