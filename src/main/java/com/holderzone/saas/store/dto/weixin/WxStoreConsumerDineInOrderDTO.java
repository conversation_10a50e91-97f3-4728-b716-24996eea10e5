package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.LinkedList;
import java.util.List;

@Data
@ApiModel("微信用户订单")
public class WxStoreConsumerDineInOrderDTO {

    @ApiModelProperty(value = "订单号")
    private String guid;

    @ApiModelProperty(value = "用户备注")
    private String remark;

    @ApiModelProperty(value = "是否是快餐，快餐：1，正餐：0", required = true)
    private Integer isSnack = 0;

    @NotBlank
    @ApiModelProperty(value = "用户当前所在公众号唯一标识", required = true)
    private String openId;
    @ApiModelProperty("用户昵称")
    private String nickName;
    @ApiModelProperty("用户微信头像")
    private String headImgUrl;
    @ApiModelProperty("用户性别,0：女,1:男，2:无法识别")
    private int sex;

    @ApiModelProperty("用户所在国家")
    private String country;
    @ApiModelProperty("用户所在省份")
    private String province;
    @ApiModelProperty("用户所在城市")
    private String city;
    @ApiModelProperty(value = "设备类型:PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7")
    private Integer deviceType;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @NotBlank
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "用户guid")
    private String userGuid;

    @ApiModelProperty(value = "桌台guid", required = true)
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台号", required = true)
    private String tableCode;
    @ApiModelProperty(value = "桌台名字", required = true)
    private String diningTableName;
    @ApiModelProperty(value = "桌台所在区域名称", required = true)
    private String areaName;

    @ApiModelProperty(value = "就餐人数")
    private int guestCount;

    @ApiModelProperty(value = "菜品集合")
    private List<WxStoreCartItemDTO> wxStoreCartItemDTOS = new LinkedList<>();

}
