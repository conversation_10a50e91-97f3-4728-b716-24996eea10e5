package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreUserOrder
 * @date 2019/3/27
 */
@Data
@ApiModel("微信门店我的订单")
public class WxStoreUserOrderDTO {

	private List<WxStoreDineinOrderDetailsRespDTO> orders;

	private WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO;
}
