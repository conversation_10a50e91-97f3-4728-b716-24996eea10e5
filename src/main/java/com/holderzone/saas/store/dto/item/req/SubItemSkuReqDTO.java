package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SubItemSkuSynRespDTO
 * @date 2019/01/03 下午3:41
 * @description //套餐分组下的子商品实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "套餐分组下的子商品实体")
public class SubItemSkuReqDTO {

    @ApiModelProperty(value = "sku与分组关联实体GUID")
    private String skuSubgroupGuid;

    @ApiModelProperty(value = "规格Guid", required = true)
    @NotEmpty
    private String skuGuid;

    @ApiModelProperty(value = "规格的商品GUID", required = true)
    @NotEmpty
    private String itemGuid;

    @ApiModelProperty(value = "商品元素中的数量", required = true)
    @NotEmpty
    @DecimalMin("0.001")
    @DecimalMax("99.999")
    private BigDecimal itemNum;

    @ApiModelProperty(value = "商品加价", required = true)
    @NotEmpty
    private BigDecimal addPrice;

    @ApiModelProperty(value = "是否默认勾选，1：是，0,否", required = true)
    @NotEmpty
    private Integer isDefault;

    @ApiModelProperty(value = "是否可重复选择，0:否,1:是", required = true)
    @NotEmpty
    private Integer isRepeat;

    @ApiModelProperty(value = "该规格在该分组内的排序", required = true)
    @NotEmpty
    private Integer sort;
}
