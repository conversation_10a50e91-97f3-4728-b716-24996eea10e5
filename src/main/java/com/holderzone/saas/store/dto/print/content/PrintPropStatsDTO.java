package com.holderzone.saas.store.dto.print.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("属性销售统计单")
public class PrintPropStatsDTO extends PrintStatsDTO {

    @Valid
    @NotEmpty(message = "属性组列表不能为空")
    @ApiModelProperty(value = "属性组列表", required = true)
    private List<PropGroup> propGroupList;

    @Data
    public static class PropGroup {

        @NotBlank(message = "属性组名称不能为空")
        @ApiModelProperty(value = "属性组名称", required = true)
        private String name;

        @Valid
        @NotEmpty(message = "属性列表不能为空")
        @ApiModelProperty(value = "属性列表", required = true)
        private List<PropItem> propList;
    }

    @Data
    public static class PropItem {

        @NotBlank(message = "属性名称不能为空")
        @ApiModelProperty(value = "属性名称", required = true)
        private String name;

        @NotNull(message = "属性销售数量不能为空")
        @ApiModelProperty(value = "属性销售数量", required = true)
        private Long quantity;

        @ApiModelProperty(value = "属性销售数量", required = true)
        private String quantityStr;

        @NotNull(message = "属性销售金额不能为空")
        @ApiModelProperty(value = "属性销售金额", required = true)
        private BigDecimal money;

        @ApiModelProperty(value = "属性销售金额", required = true)
        private String moneyStr;
    }
}
