package com.holderzone.saas.store.dto.report.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 估清报表查询VO
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "估清报表查询", description = "估清报表查询")
public class EstimateReportQueryVO {

    /**
     * 当前页数
     */
    @ApiModelProperty(value = "当前页数")
    private long currentPage = 1;

    /**
     * 每页显示条数
     */
    @ApiModelProperty(value = "每页显示条数")
    private long pageSize = 10;

    /**
     * 企业guid
     */
    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @NotNull
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate startTime;

    @NotNull
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate endTime;

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty("门店集合，品牌查询需传品牌下门店集合")
    private List<String> storeGuidList;

    @ApiModelProperty("解估类型 1：未解估 2：自动解估 3：手动解估")
    private Integer cancelEstimateType;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "估清guid列表")
    private List<String> guidList;
}
