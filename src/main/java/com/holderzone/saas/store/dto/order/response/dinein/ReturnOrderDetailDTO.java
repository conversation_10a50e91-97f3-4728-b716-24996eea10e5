package com.holderzone.saas.store.dto.order.response.dinein;

import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReturnOrderDetailDTO
 * @date 2019/01/17 14:16
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class ReturnOrderDetailDTO {

    @ApiModelProperty(value = "反结账原因")
    private String recoveryReason;

    @ApiModelProperty(value = "反结账操作人guid")
    private String recoveryStaffGuid;

    @ApiModelProperty(value = "反结账操作人名称")
    private String recoveryStaffName;

    @ApiModelProperty(value = "反结账设备类型")
    private Integer recoveryDeviceType;

    @ApiModelProperty(value = "反结账设备类型")
    private String recoveryDeviceTypeName;

    @ApiModelProperty(value = "反结账次数")
    private Integer recoveryNum;

    @ApiModelProperty(value = "反结账金额")
    private BigDecimal recoveryFee;

    @ApiModelProperty(value = "反结账时间")
    private LocalDateTime recoveryTime;

    @ApiModelProperty(value = "退单退款明细")
    private List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS;
}
