package com.holderzone.saas.store.dto.order.response.dinein;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 退单记录
 */
@Data
public class RefundOrderRecordDTO implements Serializable {

    private static final long serialVersionUID = 3433739665054528786L;

    @ApiModelProperty(value = "退款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "退款类型 0原路 1线下")
    private Integer refundType;

    @ApiModelProperty(value = "退款明细")
    private String refundDetails;

    @ApiModelProperty(value = "退款类型 退款/反结账")
    private String refundSource;

    @ApiModelProperty(value = "操作员")
    private String createStaffName;

    @ApiModelProperty(value = "授权员")
    private String authStaffName;

    @ApiModelProperty(value = "操作来源")
    private Integer deviceType;

    @ApiModelProperty(value = "操作来源")
    private String deviceTypeName;

    @ApiModelProperty(value = "退款原因")
    private String refundReason;

}
