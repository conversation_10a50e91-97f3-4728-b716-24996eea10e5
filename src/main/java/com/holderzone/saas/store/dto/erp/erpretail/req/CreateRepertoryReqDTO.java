package com.holderzone.saas.store.dto.erp.erpretail.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel("出入库的用于添加或者更新的实体")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreateRepertoryReqDTO extends BaseDTO {

    @ApiModelProperty("单据类型(1:采购入库，2：顾客退货，3:销售出库，4:盘盈入库, 5:盘亏出库, 6:期初入库，7:其他入库，8：退货出库，9：其他出库)")
    @NotNull(message = "单据类型不得为空")
    private int invoiceType;

    @ApiModelProperty("出入库类型(0:入库，1：出库)")
    private Integer inOut;

    @ApiModelProperty("经办人Guid")
    private String operatorGuid;

    @ApiModelProperty("经办人名称")
    private String operatorName;

    @ApiModelProperty("单据日期")
    private String invoiceMakeTime;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("应付金额")
    private BigDecimal shouldPayOrReceive;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("商品列表")
    private List<InOutGoodsDTO> detailList;

    @ApiModelProperty("商品总金额")
    private BigDecimal totalAmount;
}
