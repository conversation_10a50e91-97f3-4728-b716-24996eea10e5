package com.holderzone.saas.store.dto.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UserInfoDTO
 * @date 2018/09/13 15:49
 * @description
 * @program holder-saas-store-dto
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoDTO implements Serializable {

    /**
     * 企业GUID
     */
    private String enterpriseGuid;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 企业编码
     */
    private String enterpriseNo;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店编码
     */
    private String storeNo;

    /**
     * 用户GUID
     */
    private String userGuid;

    /**
     * 用户名称
     */
    @JsonProperty(value = "name")
    private String userName;

    /**
     * 用户帐号
     */
    private String account;

    /**
     * 用户电话
     */
    private String tel;

    /**
     * 联盟id
     */
    private String allianceId;

    /**
     * 来源设备类型
     */
    private String source;
}
