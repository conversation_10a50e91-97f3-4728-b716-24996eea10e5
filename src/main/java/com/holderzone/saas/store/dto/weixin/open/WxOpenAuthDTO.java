package com.holderzone.saas.store.dto.weixin.open;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("微信开发平台公众号授权回调DTO")
@AllArgsConstructor
@NoArgsConstructor
public class WxOpenAuthDTO {

    @ApiModelProperty("授权码")
    private String auth_code;

    @ApiModelProperty("过期时间")
    private Long expires_in;

    @ApiModelProperty("品牌guid")
    private String brandGuid;

    @ApiModelProperty("企业guid")
    private String enterpriseGuid;
}
