package com.holderzone.saas.store.dto.weixin.deal.mq;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("商品和类型")
@Accessors(chain = true)
public class ItemUpdateMQDTO {

	List<String> storeGuidList;

	private String enterpriseGuid;
}
