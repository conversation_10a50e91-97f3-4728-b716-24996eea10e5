package com.holderzone.saas.store.dto.trade.resp;

import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description pad订单详情信息返回实体
 * @date 2021/8/31 17:44
 * @className: PadOrderInfoRespDTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "pad订单详情信息返回实体")
public class PadOrderInfoRespDTO implements Serializable {

    private static final long serialVersionUID = 1907700780487275985L;

    /**
     * 交易订单号
     */
    @ApiModelProperty("订单guid")
    private String orderGuid;

    /**
     * 1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废 7: 反结账
     */
    @ApiModelProperty("订单状态 1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废 7: 反结账")
    private Integer state;

    /**
     * 就餐人数
     */
    @ApiModelProperty("就餐人数")
    private Integer actualGuestsNo;

    /**
     * 订单总价
     */
    @ApiModelProperty(value = "订单总价")
    private PadPriceRespDTO orderTotalPrice;

    /**
     * 是否开启线上买单（0关闭，1开启（快餐只能选择开启））
     */
    @ApiModelProperty(value = "是否开启线上买单（0关闭，1开启（快餐只能选择开启））")
    private Integer isOnlinePayed;

    /**
     * 附加费列表
     */
    @ApiModelProperty(value = "附加费列表")
    private List<AppendFeeDetailDTO> appendFeeList;

    /**
     * pad点餐批次信息
     */
    @ApiModelProperty(value = "pad点餐批次信息")
    private List<PadOrderBatchInfoRespDTO> batchInfoList;
}
