package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseTableDTO
 * @date 2019/01/21 15:33
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class BaseTableDTO extends BaseDTO {

    private static final long serialVersionUID = -8762350676510705153L;

    @ApiModelProperty(value = "桌台guid")
    private String tableGuid;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "是否快餐")
    private boolean fastFood;

}
