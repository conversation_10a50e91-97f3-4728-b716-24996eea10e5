package com.holderzone.saas.store.dto.order.request;

import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.trade.OrderTableGuestDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrderTableBillReqDTO extends CancelOrderReqDTO implements Serializable {

    private static final long serialVersionUID = -2546600589223154067L;

    /**
     * 桌台信息
     */
    private List<OrderTableGuestDTO> orderTableGuestDTOS;


    /**
     * 0 单台 1 联台 2 并台
     */
    private Integer orderTableType;
}
