package com.holderzone.saas.store.dto.boss.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(value = "老板助手商品查询")
@EqualsAndHashCode(callSuper = false)
public class BossItemQueryDTO implements Serializable {

    private static final long serialVersionUID = -9137583595933031613L;

    @NotEmpty(message = "分类GUID不能为空")
    @ApiModelProperty(value = "分类GUID;若查询全部分类，则传空字符串")
    private String typeGuid;

    @NotNull
    @ApiModelProperty(value = "是否上架：-1:全部;0：否;1:是")
    private Integer isRack;

    @NotEmpty(message = "门店GUID不能为空")
    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;
}
