package com.holderzone.saas.store.dto.marketing.portrayal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2024/12/23
 * @description 自定义人群详情
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "自定义人群详情")
public class CustomizeLabelDetails implements Serializable {

    private static final long serialVersionUID = 3579276047952366064L;

    @ApiModelProperty(value = "自定义人群名称")
    private String labelName;

    @ApiModelProperty(value = "包含开始次数")
    private Integer includeStartNum;

    @ApiModelProperty(value = "包含结束次数")
    private Integer includeEndNum;

}
