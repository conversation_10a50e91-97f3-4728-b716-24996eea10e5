package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
public class MtCbOrderPartRefundDetail implements Serializable {

    private static final long serialVersionUID = -5508732371719877674L;

    /**
     * 部分退款菜品信息
     */
    private String food;

    /**
     * 部分退款金额
     */
    private String money;

    /**
     * 通知类型
     * part：商家/用户发起部分退款 agree：商家同意部分退款 reject ：商家拒绝部分退款
     */
    private String notifyType;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 部分退款原因
     */
    private String reason;

    /**
     * https://developer.meituan.com/openapi#7.5.8
     */
    @Data
    public static class FoodBean implements Serializable {

        private static final long serialVersionUID = -6135205393831597060L;

        /**
         * ERP端菜品id
         */
        private String app_food_code;

        /**
         * 餐盒总个数
         */
        private long box_num = 1;

        /**
         * 餐盒费，单价
         * 单位：元
         */
        private double box_price;

        /**
         * 菜品份数
         */
        private long count = 0;

        /**
         * 菜品名
         */
        private String food_name;

        /**
         * 菜品原价
         * 单位：元
         */
        private double food_price;

        /**
         * 原菜品小计?
         * 单位：元
         */
        private double origin_food_price;

        /**
         * 菜品退还价格?
         * 单位：元
         */
        private double refund_price;

        /**
         * ERP端菜品skuId, 对应菜品映射中的eDishSkuCode
         */
        private String sku_id;

        /**
         * 菜品规格
         */
        private String spec;

        /**
         * 菜品属性，多个属性用英文逗号隔开
         */
        private String food_property;

        /**
         * 美团 skuId
         */
        private String mt_sku_id;
    }
}
