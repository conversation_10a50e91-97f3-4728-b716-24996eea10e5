package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class KdsAttrGroupDTO implements Serializable {

    private static final long serialVersionUID = 8948933316030590201L;

    @NotBlank(message = "属性组Guid不得为空")
    @ApiModelProperty(value = "属性组Guid", required = true)
    private String groupGuid;

    @NotBlank(message = "属性组名称不得为空")
    @ApiModelProperty(value = "属性组名称", required = true)
    private String groupName;

    @Valid
    @NotEmpty(message = "属性列表不得为空")
    @ApiModelProperty(value = "属性列表", required = true)
    private List<KdsItemAttrDTO> attrs;
}