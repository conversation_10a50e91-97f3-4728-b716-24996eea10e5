package com.holderzone.saas.store.dto.order.request.bill;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className JhQueryReqDTO
 * @date 2018/08/14 11:45
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class JhQueryReqDTO extends BaseDTO {


    @ApiModelProperty(value = "支付guid，如果有，聚合支付轮询参数")
    private String payGuid;

    @ApiModelProperty(value = "聚合支付订单guid，如果有，聚合支付轮询参数")
    private String orderGuid;

    @ApiModelProperty(value = "订单金额（商品总额+附加费）")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private BigDecimal actuallyPayFee;


}