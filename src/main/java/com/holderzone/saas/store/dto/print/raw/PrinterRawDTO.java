package com.holderzone.saas.store.dto.print.raw;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class PrinterRawDTO implements Serializable {

    private static final long serialVersionUID = -75342253696516107L;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 设备编号
     */
    private String deviceId;

    /**
     * 打印机guid
     */
    private String printerGuid;

    /**
     * 打印机名称
     */
    private String printerName;

    /**
     * 打印业务类型; 参数: 0/前台打印; 1后厨打印; 2/标签打印
     */
    private Integer businessType;

    /**
     * 打印机类型; 可选参数: 0/本机打印机; 1/网络打印机; 2/usb打印机
     */
    private Integer printerType;

    /**
     * 打印机ip
     */
    private String printerIp;

    /**
     * 打印端口
     */
    private Integer printerPort;

    /**
     * 打印次数
     */
    private Integer printCount;

    /**
     * 打印纸张类型; 参数: 80; 58; 40X30; 30X20
     */
    private String printPage;

    /**
     * 打印方式(切纸方式);  参数: 0/整单; 1/一菜一单; 2/一种类型一单; 3/一份数量一单; 默认0/整单
     */
    private Integer printCut;

    /**
     * 是否是主机：0=否，1=是
     */
    private Boolean isMaster;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录更新时间
     */
    private LocalDateTime gmtModified;
}