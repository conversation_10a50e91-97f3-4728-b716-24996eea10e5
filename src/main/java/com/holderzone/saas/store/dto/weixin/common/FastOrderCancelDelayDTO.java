package com.holderzone.saas.store.dto.weixin.common;

import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

@Data
@ApiModel("快餐超时取消")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FastOrderCancelDelayDTO implements Delayed {

	private int remainingTime;

	private String orderGuid;

	private WxStoreConsumerDTO wxStoreConsumerDTO;

	@Override
	public long getDelay(TimeUnit unit) {
		return this.remainingTime - System.currentTimeMillis();
	}

	@Override
	public int compareTo(Delayed o) {
		return this == o ? 0 : this.getDelay(TimeUnit.MILLISECONDS) - o.getDelay(TimeUnit.MILLISECONDS) > 0 ? 1 : -1;
	}
}
