package com.holderzone.saas.store.dto.item.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel(value = "价格方案中菜品数据对象")
@Data
public class PlanItemDTO {

    @ApiModelProperty(value = "菜谱商品Guid更新时传")
    private String planItemGuid;

    @ApiModelProperty(value = "菜品guid")
    private String itemGuid;

    @ApiModelProperty(value = "商品类型")
    private Integer itemType;

    @ApiModelProperty(value = "商品排序")
    private Integer sort;

    @ApiModelProperty(value = "菜品方案guid")
    private String planGuid;

    @ApiModelProperty(value = "菜谱方案商品名称")
    private String planItemName;

    @ApiModelProperty(value = "商品售价")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "商品会员价")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "商品描述")
    private String description;

    @ApiModelProperty(value = "图片路径数组json")
    private String pictureUrl;

    @ApiModelProperty(value = "商品分类Guid")
    private String typeGuid;

    @ApiModelProperty(value = "商品分类名称")
    private String typeName;

    @ApiModelProperty(value = "是否售完下架 0:已上架 1：售完下架 2：立即下架（直接删除） 3：即将下架 4：已下架（售完商品的下架） ")
    private Integer isSoldOut;
}
