package com.holderzone.saas.store.dto.print.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("商品销售,退菜,赠菜统计单")
public class PrintItemStatsDTO extends PrintStatsDTO {

    @Valid
    @NotEmpty(message = "商品销售列表不能为空")
    @ApiModelProperty(value = "商品销售列表", required = true)
    private List<PrintItemStatsDTO.Item> itemList;

    @Data
    public static class Item {

        @NotBlank(message = "该商品名称不能为空")
        @ApiModelProperty(value = "该商品名称", required = true)
        private String name;

        @NotNull(message = "该商品销售数量不能为空")
        @ApiModelProperty(value = "该商品销售数量", required = true)
        private BigDecimal quantity;

        @NotNull(message = "该商品销售金额不能为空")
        @ApiModelProperty(value = "该商品销售金额", required = true)
        private BigDecimal money;

        @ApiModelProperty(value = "该商品销售数量(字符串)", required = true)
        private String quantityStr;

        @ApiModelProperty(value = "该商品销售金额(字符串)", required = true)
        private String moneyStr;

        @NotNull(message = "该商品是否为称重商品不能为空")
        @ApiModelProperty(value = "该商品是否为称重商品", required = true)
        private Boolean isWeight;

        @NotNull(message = "该商品是否为套餐商品不能为空")
        @ApiModelProperty(value = "该商品是否为套餐商品", required = true)
        private Boolean isPackage;
    }
}
