package com.holderzone.saas.store.dto.takeaway.response;

import com.holderzone.saas.store.dto.takeaway.MtResponseDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
public class MtDoCheckRespDTO {

    /**
     * 美团执行验券返回参数
     */
    private MtCouponDoCheckRespDTO data;

    /**
     * 美团响应失败结果
     */
    private MtResponseDTO.ErrorDetail error;
}
