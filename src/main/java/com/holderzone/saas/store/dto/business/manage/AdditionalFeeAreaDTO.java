package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class AdditionalFeeAreaDTO {

    /**
     * guid
     */
    @ApiModelProperty(value = "guid")
    private String guid;

    /**
     * 附加费guid
     */
    @ApiModelProperty(value = "附加费guid")
    private String additionalFeeGuid;

    /**
     * 区域guid
     */
    @ApiModelProperty(value = "区域guid")
    private String areaGuid;

}
