package com.holderzone.saas.store.dto.business.group;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.exception.GroupBuyException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023-06-19
 * @description
 */
@Data
public class StoreBindDTO {

    @NotNull(message = "团购类型不能为空")
    private Integer groupBuyType;

    /**
     * 外部账号id
     */
    private String accountId;

    /**
     * 外部门店id
     */
    private String poiId;

    /**
     * 业务系统门店guid
     */
    @NotNull(message = "门店guid不能为空")
    private String storeGuid;

    public void verify(){
        if(StringUtils.isEmpty(poiId)){
            throw new GroupBuyException("参数信息有误");
        }
    }
}
