package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className RoleDataDTO
 * @date 19-2-13 上午11:45
 * @description 角色权限DTO
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("角色权限DTO")
public class TerminalDTO {
    @ApiModelProperty("终端名称")
    private String terminalName;

    @ApiModelProperty("终端guid")
    private String terminalGuid;

    @ApiModelProperty("终端code")
    private String terminalCode;

    @ApiModelProperty("是否已有")
    private Boolean isChecked;
}
