package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickDownloadTableDTO
 * @date 2019/03/18 15:01
 * @description 微信桌贴下载桌位DTO
 * @program holder-saas-store
 */

@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("微信桌贴下载桌位DTO")
public class WxStickDownloadTableDTO {

    @ApiModelProperty("区域guid")
    private String areaGuid;

    @ApiModelProperty("区域名字")
    private String areaName;

    @ApiModelProperty("桌贴guid")
    private String tableGuid;

    @ApiModelProperty("桌贴名字")
    private String tableName;
}
