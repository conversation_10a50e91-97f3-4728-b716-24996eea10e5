package com.holderzone.saas.store.dto.member;

import com.holderzone.saas.store.dto.trade.BaseBillDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberLoginDTO
 * @date 2018/08/06 11:14
 * @description 会员登录接口请求实体
 * @program holder-saas-store-dto
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MemberLoginDTO extends BaseBillDTO {

    private String memberGuid;
    @ApiModelProperty(value = "手机号或者卡号必传")
    private String phone;

    public static MemberLoginDTO build() {
        return new MemberLoginDTO();
    }

    public MemberLoginDTO addMemberGuid(String memberGuid) {
        this.setMemberGuid(Optional.ofNullable(memberGuid).orElse(""));
        return this;
    }

}
