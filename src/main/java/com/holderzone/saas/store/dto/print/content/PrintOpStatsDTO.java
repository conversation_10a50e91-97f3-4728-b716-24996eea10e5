package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("营业概况单")
public class PrintOpStatsDTO extends PrintDTO {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private String storeName;

    @NotNull(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始时间", required = true)
    private Long startTime;

    @NotNull(message = "收银员不能为空")
    @ApiModelProperty(value = "收银员")
    private String checkoutStaffs;

    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间", required = true)
    private Long endTime;

    @NotNull(message = "订单数不能为空")
    @ApiModelProperty(value = "订单数", required = true)
    private Long orderQuantity;

    @ApiModelProperty(value = "订单数", required = true)
    private String orderQuantityStr;

    @NotNull(message = "客流量不能为空")
    @ApiModelProperty(value = "客流量", required = true)
    private Long customerNumber;

    @ApiModelProperty(value = "客流量", required = true)
    private String customerNumberStr;

    @NotNull(message = "销售额不能为空")
    @ApiModelProperty(value = "销售额", required = true)
    private BigDecimal salesTotal;

    @ApiModelProperty(value = "销售额", required = true)
    private String salesTotalStr;

    @NotNull(message = "销售收入不能为空")
    @ApiModelProperty(value = "销售收入", required = true)
    private BigDecimal salesIncome;

    @ApiModelProperty(value = "销售收入", required = true)
    private String salesIncomeStr;

    @Nullable
    @ApiModelProperty(value = "各支付方式销售收入列表")
    private List<PayRecord> salesDetail;

    /**
     * 预计应得金额
     */
    @ApiModelProperty(value = "预计应得金额", required = true)
    private BigDecimal estimatedAmount;

    @ApiModelProperty(value = "预计应得金额", required = true)
    private String estimatedAmountStr;

    @NotNull(message = "优惠总额不能为空")
    @ApiModelProperty(value = "优惠总额", required = true)
    private BigDecimal reduceTotal;

    @ApiModelProperty(value = "优惠总额", required = true)
    private String reduceTotalStr;

    @Nullable
    @ApiModelProperty(value = "优惠列表")
    private List<ReduceRecord> reduceDetail;

    @ApiModelProperty(value = "正餐客流量", required = true)
    private Long dineInCustomerNumber;

    @ApiModelProperty(value = "正餐客流量", required = true)
    private String dineInCustomerNumberStr;

    @ApiModelProperty(value = "上座率")
    private String occupancyRatePercent;

    @ApiModelProperty(value = "开台率")
    private String openTableRatePercent;

    @ApiModelProperty(value = "翻台率")
    private String flipTableRatePercent;

    @ApiModelProperty(value = "平均用餐时间，单位分钟")
    private Long avgDineInTime;

    @ApiModelProperty(value = "平均用餐时间，单位分钟")
    private String avgDineInTimeStr;

    @ApiModelProperty("会员充值金额")
    private BigDecimal rechargeMoney;

    @ApiModelProperty("会员充值金额")
    private String rechargeMoneyStr;

    @ApiModelProperty(value = "退款总额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "退款总额")
    private String refundAmountStr;
}
