package com.holderzone.saas.store.dto.trade.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.saas.store.dto.kds.resp.KdsAttrGroupDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class TransferItemDetailsDTO implements Serializable {

    private static final long serialVersionUID = -598628414756531621L;

    @NotBlank(message = "订单Guid不得为空")
    @ApiModelProperty(value = "订单商品Guid")
    private String orderItemGuid;

    @NotBlank(message = "商品Guid不得为空")
    @ApiModelProperty(value = "商品Guid")
    private String itemGuid;

    @NotBlank(message = "商品名称不得为空")
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @NotBlank(message = "SkuGuid不得为空")
    @ApiModelProperty(value = "SkuGuid")
    private String skuGuid;

    @Nullable
    @ApiModelProperty(value = "SKU名称")
    private String skuName;

    @Nullable
    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @NotNull(message = "商品状态不得为空")
    @ApiModelProperty(value = "商品状态：1.即起、2.挂起、3.叫起")
    private Integer itemState;

    @NotNull(message = "是否为称重商品不得为空")
    @ApiModelProperty(value = "是否为称重商品")
    private Boolean isWeight;

    @NotNull(message = "份数不得为空")
    @ApiModelProperty(value = "份数：称重商品时最多3位小数，非称重商品时为整数")
    private BigDecimal currentCount;

    @Nullable
    @ApiModelProperty(value = "单位")
    private String skuUnit;

    @Nullable
    @ApiModelProperty(value = "商品备注")
    private String itemRemark;

    @Valid
    @Nullable
    @ApiModelProperty(value = "属性组")
    private List<KdsAttrGroupDTO> attrGroup;

    @JsonIgnore
    @ApiModelProperty(value = "订单备注")
    private String orderRemark;

    @ApiModelProperty(value = "套餐子菜列表")
    private List<TransferItemDetailsDTO> subItemList;

}
