package com.holderzone.saas.store.dto.order.request.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RequestCheckVolumeRedeem {

    private String enterpriseGuid;

    private String operSubjectGuid;

    @ApiModelProperty(value = "手机号")
    private String phoneNum;

    @ApiModelProperty(value = "memberInfoGuid")
    private String memberInfoGuid;

    @ApiModelProperty(value = "会员名称")
    private String memberName;

    @ApiModelProperty(value = "兑换码")
    private String redeemCodeVal;

    @ApiModelProperty(value = "来源")
    private Integer source;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;



    @ApiModelProperty(value = "所属劵的会员guid", notes = "验劵一次后会返回，之后再验其它劵需传")
    private String belongsMemberInfoGuid;

}
