package com.holderzone.saas.store.dto.weixin.req;

import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.weixin.member.WxMemberPayDTO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

@Data
@ApiModel("微信商家接单入参")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxMemberModeNotifyReqDTO implements Delayed{

	private long remainingTime;

	private UserInfoDTO userInfoDTO;

	private WxMemberPayDTO wxMemberPayDTO;

	private WxPrepayReqDTO prepay;

	private DineinOrderDetailRespDTO calculate;

	private BillPayReqDTO billPayReqDTO;

	private WxMemberTradeNotifyReqDTO wxMemberTradeNotifyReqDTO;
	//private user


	@Override
	public long getDelay(TimeUnit unit) {
		return this.remainingTime - System.currentTimeMillis();
	}


	@Override
	public int compareTo(Delayed o) {
		if (this == o) {
			return 0;
		}
		return this.getDelay(TimeUnit.MILLISECONDS) - o.getDelay(TimeUnit.MILLISECONDS) > 0 ? 1 : -1;
	}
}
