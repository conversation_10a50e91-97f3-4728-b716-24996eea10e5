package com.holderzone.saas.store.dto.takeaway.jd;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum OrderStatusEnum {

    ACTIVATED(1,"41000","订单生效"),

    CONFIRMED(2,"32000","订单已确认"),

    SHIPPING(3,"33040","订单配送中"),

    SHIP_SUCCEED(4,"33060","订单配送完成"),

    FINISHED(5,"90000","订单已完成"),

    CANCELED_BEFORE_CONFIRMED(6,"20020","商家确认前取消"),

    CANCELED(7,"20021","除商家确认前取消"),

    CANCEL_REQ(20,"20030","用户申请取消单"),

    CANCEL_AGREED(22,"20031","商户同意取消单"),

    CANCEL_DISAGREED(23,"20032","商户不同意取消单"),

    REFUND_REQ(30,"10","创建售后单申请消息"),

    REFUND_AGREED(32,"32","退款成功"),

    REFUND_DISAGREED(33,"33","退款失败"),

    ;
    private int systemCode;

    private String jdCode;

    private String description;

    public static OrderStatusEnum getOrderStatusEnumByJdCode(String jdCode) {
        for (OrderStatusEnum orderStatusEnum : OrderStatusEnum.values()) {
            if (orderStatusEnum.jdCode.equals(jdCode)) {
                return orderStatusEnum;
            }
        }
        return null;
    }
}
