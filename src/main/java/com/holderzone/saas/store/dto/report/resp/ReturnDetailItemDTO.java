package com.holderzone.saas.store.dto.report.resp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ReturnDetailItemDTO implements Serializable {

    private static final long serialVersionUID = 8295434747256392583L;

    /**
     * 门店名称
     */
    public String storeName;

    /**
     * 门店名称
     */
    public String orderNo;

    /**
     * 订单类型
     */
    public String cateringType;

    /**
     * 规格guid
     */
    public String skuGuid;

    /**
     * 商品名称
     */
    public String goodsName;

    /**
     * 商品分类
     */
    public String goodsCategories;

    /**
     * 退菜数量
     */
    public BigDecimal returnQuantity;

    /**
     * 退菜金额
     */
    public BigDecimal refund;

    /**
     * 退菜时间
     */
    public String returnTime;

    /**
     * 下单时间
     */
    public String orderTime;

    /**
     * 退菜原因
     */
    public String reason;

    /**
     * 退菜操作人
     */
    public String staff;

    /**
     * 操作节点
     */
    public String operatorNode;

    /**
     * 桌台
     */
    public String table;

    /**
     * 售卖单价
     */
    public BigDecimal salePrice;

    /**
     * 实退金额
     */
    public BigDecimal actuallyRefundFee;

    /**
     * 授权人员
     */
    public String authorityUser;

}
