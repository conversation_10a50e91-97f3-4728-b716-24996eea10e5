package com.holderzone.saas.store.dto.order.request.item;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 转菜商品详情
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "转菜商品详情")
public class TransferItemDetailsReqDTO implements Serializable {

    private static final long serialVersionUID = -4998051281866392581L;

    @ApiModelProperty(value = "订单商品guid")
    private String orderItemGuid;

    @ApiModelProperty(value = "转菜数量")
    private BigDecimal transferCount;

    @ApiModelProperty(value = "商品类型(1.套餐主项，2.规格，3.称重，4.单品 5.团餐主项)")
    private Integer orderItemType;

}
