package com.holderzone.saas.store.dto.weixin.open;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/10
 * @description 微信消息发送请求
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "微信消息发送请求", description = "微信消息发送请求")
public class WxSendMessageReqDTO implements Serializable {

    private static final long serialVersionUID = -3479985161310424681L;

    @ApiModelProperty(value = "订单及商品列表")
    private List<WxOrderReqDTO> orderList;

}
