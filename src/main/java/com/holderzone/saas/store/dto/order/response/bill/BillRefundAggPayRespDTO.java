package com.holderzone.saas.store.dto.order.response.bill;

import com.holderzone.saas.store.dto.order.BillMultipleRefundAggPayDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BillRefundAggPayRespDTO implements Serializable {

    private static final long serialVersionUID = -7811238638009099409L;

    /**
     * 退款结果
     */
    private List<BillMultipleRefundAggPayDTO> refundAggPays;
}
