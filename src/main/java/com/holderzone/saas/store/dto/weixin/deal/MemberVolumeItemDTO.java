package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApiModel("优惠券")
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Data
public class MemberVolumeItemDTO {
	@ApiModelProperty("会员持卷GUID")
	private String memberVolumeGuid;
	@ApiModelProperty("券模板GUID")
	private String volumeInfoGuid;
	@ApiModelProperty("券名称")
	private String volumeInfoName;
	@ApiModelProperty("开始有效期")
	private LocalDateTime volumeStartDate;
	@ApiModelProperty("结束有效期")
	private LocalDateTime volumeEndDate;
	@ApiModelProperty("会员拥有券状态,0未使用，1，已使用，2，未生效，3，已过期，4已作废")
	private Integer volumeState;
	@ApiModelProperty("券优惠金额")
	private BigDecimal volumeMoney;
	@ApiModelProperty("券类型，0代金券,1折扣券,2兑换券,3商品券")
	private Integer volumeType;
	@ApiModelProperty("使用门槛满,0无限制，1有限制")
	private Integer useThreshold;
	@ApiModelProperty("满多少可用")
	private BigDecimal useThresholdFull;
	@ApiModelProperty("0:无限制，1:仅限原价购买")
	private Integer whetherOriginPrice=0;
	@ApiModelProperty("优惠券码")
	private String volumeCode;
	@ApiModelProperty("1:表示选中，0：表示未选中")
	private Integer uck=0;

	@ApiModelProperty(value = "true:可用，false：不可用")
	private Boolean enable=true;
	@ApiModelProperty(value = "不可用原因",hidden = true)
	private String tip;

}
