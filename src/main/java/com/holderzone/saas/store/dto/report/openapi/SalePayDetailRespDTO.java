package com.holderzone.saas.store.dto.report.openapi;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 销售支付明细响应
 */
@Data
public class SalePayDetailRespDTO implements Serializable {

    private static final long serialVersionUID = -1025167689243809355L;

    /**
     * 序号
     */
    private Integer index;

    /**
     * 订单guid
     */
    @JsonIgnore
    private String orderGuid;

    /**
     * 支付时间
     */
    @JsonIgnore
    private LocalDateTime gmtCreate;

    /**
     * 支付类型
     */
    private Integer paymentType;

    /**
     * 支付类型名称
     */
    private String paymentTypeName;

    /**
     * 支付方式
     */
    private String payWayCode;

    /**
     * 支付金额
     */
    private Long amount;

    /**
     * 支付流水号, 收单系统唯一性标识
     */
    private String bankTransactionId;
}
