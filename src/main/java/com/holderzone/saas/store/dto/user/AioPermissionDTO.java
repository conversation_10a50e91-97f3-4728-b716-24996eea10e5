package com.holderzone.saas.store.dto.user;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.enums.AioPermissionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className AioPermissionDTO
 * @date 18-11-14 下午6:32
 * @description 一体机权限dto
 * @program holder-saas-store-dto
 */
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@ApiModel
public class AioPermissionDTO {

    /**
     * 一体机权限code，对应AioPermissionEnum
     */
    @ApiModelProperty(value = "一体机权限code")
    private Integer permissionCode;

    /**
     * 一体机权限名称
     */
    @ApiModelProperty(value = "一体机权限名称")
    private String permissionName;

    /**
     * 权限备注：
     * 整单折扣-最高可打x折
     * 整单让价-最高可让x元
     */
    @ApiModelProperty(value = "权限备注，整单折扣-最高可打x折，整单让价-最高可让x元")
    private BigDecimal remark;

    /**
     * 校验
     *
     * @param dtoList dtoList
     */
    public static void validate(List<AioPermissionDTO> dtoList) {
        List<AioPermissionDTO> list = dtoList.stream().filter(p -> p.getPermissionCode().equals(AioPermissionEnum.ZHENG_DAN_ZHE_KOU.getCode())
                || p.getPermissionCode().equals(AioPermissionEnum.ZHENG_DAN_RANG_JIA.getCode())).collect(Collectors.toList());
        if (list.size() > 0) {
            list.forEach(p -> {
                if (p.getPermissionCode().equals(AioPermissionEnum.ZHENG_DAN_ZHE_KOU.getCode())) {
                    if (p.getRemark() == null) {
                        throw new BusinessException("整单折扣下最高打折数不能为空");
                    } else if (p.getRemark().compareTo(BigDecimal.TEN) >= 0 || p.getRemark().compareTo(BigDecimal.ZERO) < 0) {
                        throw new BusinessException("整单折扣下最高打折数范围只能为0-9.99，当前打折数为：" + p.getRemark());
                    }
                }
                if (p.getPermissionCode().equals(AioPermissionEnum.ZHENG_DAN_RANG_JIA.getCode())) {
                    if (p.getRemark() == null) {
                        throw new BusinessException("整单让价下最高让价不能为空");
                    } else if (p.getRemark().compareTo(BigDecimal.ZERO) < 0 || p.getRemark().compareTo(BigDecimal.valueOf(1000000)) >= 0) {
                        throw new BusinessException("整单让价下最高让价范围只能为0-999999.99，当前最高让价为：" + p.getRemark());
                    }
                }
            });
        }
    }
}
