package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AdditionalFeeDO
 * @date 2018/08/02 下午3:00
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SurchargeQueryDTO implements Serializable {

    private static final long serialVersionUID = -7894384364235228828L;

    @NotBlank(message = "附加费Guid不得为空")
    @ApiModelProperty(value = "附加费Guid", required = true)
    private String surchargeGuid;
}
