package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description 微信的预订单集合详情返回
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreAdvanceOrderPriceDTO
 * @date 2019/3/25
 */
@Data
@ApiModel("微信预订单返回")
public class WxStoreAdvanceOrderPriceDTO {

	@ApiModelProperty(value = "预订单guid")
	private String advanceGuid;

	@ApiModelProperty(value="商户订单guid,商家接单后，guid不为空，则视为加菜")
	private String tradeOrderGuid;

	private List<WxStoreAdvanceOrderDTO> wxStoreAdvanceOrderDTOS;

	@ApiModelProperty(value = "预订单总价格")
	private BigDecimal totalPrice;

	@ApiModelProperty(value = "整单备注")
	private String totalRemark;

	@ApiModelProperty(value = "门店名")
	private String storeName;

	@ApiModelProperty(value = "区域名")
	private String areaName;

	@ApiModelProperty(value = "是否是快餐,1快餐，0：正餐",required = true)
	private Integer orderModel;



}
