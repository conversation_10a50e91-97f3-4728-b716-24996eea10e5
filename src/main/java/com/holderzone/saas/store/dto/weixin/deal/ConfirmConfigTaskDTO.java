package com.holderzone.saas.store.dto.weixin.deal;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 门店自动接单和确认配置
 * @date 2021/4/14
 */
@Data
@Builder
public class ConfirmConfigTaskDTO implements Serializable {

    /**
     * 配置模式1所有订单均确认，2首单确认
     */
    private Integer takingModel;

    /**
     * 订单记录guid
     * pad传的是orderGuid
     */
    private String orderRecordGuid;

    /**
     * guid加菜唯一主键
     */
    private String merchantGuid;

    /**
     * 下单人微信OpenId
     */
    private String openId;

    /**
     * 下单人
     */
    private String nickName;

    /**
     * 就餐人数
     */
    private Integer actualGuestsNo;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 业务主键
     */
    private String guid;

    /**
     * 是否是自动确认
     */
    private Boolean autoConfirm;

    /**
     * 是否是确认提示
     */
    private Boolean confirmPrompt;

    /**
     * 企业GUID
     */
    private String enterpriseGuid;

    /**
     * 主体GUID
     */
    private String operSubjectGuid;

    private String source;

    /**
     * 用户下单后每隔多久进行提示（分钟，不填写默认提示一次）
     */
    private Long confirmPromptTime;

    /**
     * 用户GUID
     */
    private String userGuid;

    /**
     * 用户名称
     */
    private String userName;

    private String account;

    private String storeName;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 整单备注
     */
    private String remark;

}
