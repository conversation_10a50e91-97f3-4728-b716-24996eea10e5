package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxSendShortMsgReqDTO
 * @date 2019/03/07 14:27
 * @description 微信发送公众号解绑请求参数
 * @program holder-saas-store
 */
@Data
@ApiModel("微信发送公众号解绑请求参数")
@AllArgsConstructor
@NoArgsConstructor
public class WxSendShortMsgReqDTO {

    @ApiModelProperty("用户guid")
    @NotEmpty(message = "用户guid不能为空")
    private String userGuid;

    @ApiModelProperty("用户姓名")
    @NotEmpty(message = "用户姓名不能为空")
    private String userName;

    @ApiModelProperty("用户手机号")
    @NotEmpty(message = "用户手机号不能为空")
    private String tel;
}
