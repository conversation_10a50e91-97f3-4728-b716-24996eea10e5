package com.holderzone.saas.store.dto.erp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/08 下午 14:15
 * @description
 */
@ApiModel("结算单查询实体")
public class CheckoutDocumentSelectDTO {

    @ApiModelProperty("盘点单实体唯一标识")
    private String guid;

    @ApiModelProperty("制单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    private Date gmtCreate;

    @ApiModelProperty("纸质编号")
    private String code;

    @ApiModelProperty("仓库guid")
    private String warehouseGuid;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("单据门店guid")
    private String documentStoreGuid;

    @ApiModelProperty("单据门店名称")
    private String documentStoreName;

    @ApiModelProperty("盘点类型(0：日盘，1：周盘， 2：月盘)")
    private Integer type;

    @ApiModelProperty("经办人guid")
    private String operatorGuid;

    @ApiModelProperty("经办人名称")
    private String operatorName;

    @ApiModelProperty("盘点时间")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="Asia/Shanghai")
    private Date documentDate;

    @ApiModelProperty("状态(0:未提交，1：已提交)")
    private Integer status;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否锁盘")
    private Boolean lock;

    @ApiModelProperty("制单人名称")
    private String createStaffName;

    @ApiModelProperty("盘点单明细")
    private List<CheckoutDocumentDetailSelectDTO> detailList;


    public String getCreateStaffName() {
        return createStaffName;
    }

    public void setCreateStaffName(String createStaffName) {
        this.createStaffName = createStaffName;
    }

    public String getDocumentStoreGuid() {
        return documentStoreGuid;
    }

    public void setDocumentStoreGuid(String documentStoreGuid) {
        this.documentStoreGuid = documentStoreGuid;
    }

    public String getDocumentStoreName() {
        return documentStoreName;
    }

    public void setDocumentStoreName(String documentStoreName) {
        this.documentStoreName = documentStoreName;
    }

    public List<CheckoutDocumentDetailSelectDTO> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<CheckoutDocumentDetailSelectDTO> detailList) {
        this.detailList = detailList;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOperatorGuid() {
        return operatorGuid;
    }

    public void setOperatorGuid(String operatorGuid) {
        this.operatorGuid = operatorGuid;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Date getDocumentDate() {
        return documentDate;
    }

    public void setDocumentDate(Date documentDate) {
        this.documentDate = documentDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Boolean getLock() {
        return lock;
    }

    public void setLock(Boolean lock) {
        this.lock = lock;
    }
}
