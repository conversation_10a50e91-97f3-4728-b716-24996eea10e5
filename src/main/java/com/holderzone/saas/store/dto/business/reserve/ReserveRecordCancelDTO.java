package com.holderzone.saas.store.dto.business.reserve;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordDO
 * @date 2018/07/30 下午2:51
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReserveRecordCancelDTO {

    /**
     * 预订记录guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "预订记录guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "预订记录guid", required = true)
    private String reserveRecordGuid;

    /**
     * 撤销人guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "撤销人guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "撤销人guid", required = true)
    private String cancelUserGuid;

    /**
     * 撤销人名字
     */
    @NotNull
    @Size(min = 1, max = 45, message = "撤销人名字不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "撤销人名字", required = true)
    private String cancelUserName;
}
