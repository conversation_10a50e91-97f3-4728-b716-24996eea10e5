package com.holderzone.saas.store.dto.order.request.item;

import com.holderzone.saas.store.dto.table.BaseTableDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UpdateItemStateReqDTO
 * @date 2019/01/24 17:26
 * @description
 * @program holder-saas-store-dto
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UpdateItemStateReqDTO extends BaseTableDTO{

    private static final long serialVersionUID = -7549769448472688573L;

    @ApiModelProperty(value = "订单商品")
    private List<UpdateItemStateReqDTO.OrderItem> orderItems;

    @ApiModelProperty(value = "是否叫起整单")
    private Boolean isCallAll;

    @Data
    public static class OrderItem {

        @ApiModelProperty(value = "商品或赠送商品guid")
        private String guid;

        @ApiModelProperty(value = "划菜类型（1：商品，2：赠送）")
        private int type;

    }
}
