package com.holderzone.saas.store.dto.item.req.price;

import com.holderzone.saas.store.dto.item.common.PlanPriceEditBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description 图片
 * @date 2021/9/29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanPricePictureEditDTO extends PlanPriceEditBaseDTO {

    private String pictureUrl;

    @ApiModelProperty(value = "商品图片地址，列表大图")
    private String bigPictureUrl;

    /**
     * 图片路径数组json,这里对应新一期加入的“详情大图”
     */
    @ApiModelProperty(value = "商品图片地址，详情大图")
    private String detailBigPictureUrl;
}
