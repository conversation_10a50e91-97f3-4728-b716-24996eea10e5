package com.holderzone.saas.store.dto.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("用户数据条件匹配规则实体")
public class UserDataCondRuleDTO {

    @ApiModelProperty(value = "请求值、响应值：用户所管理组织（组织GUID，组织名称）")
    private List<UserDataDTO> userOrgData;

    @ApiModelProperty(value = "请求值、响应值：用户所管理品牌（品牌GUID，品牌名称）")
    private List<UserDataDTO> userBrandData;

    @ApiModelProperty(value = "请求值、响应值：用户所管理区域（区域GUID为((区)市)省Code拼接，区域名称为((区)市)省名称拼接）")
    private List<UserRegionDTO> userRegionData;

    @JsonIgnore
    public static UserDataCondRuleDTO empty() {
        return new UserDataCondRuleDTO()
                .setUserOrgData(Collections.emptyList())
                .setUserBrandData(Collections.emptyList())
                .setUserRegionData(Collections.emptyList());
    }
}
