package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickShopCartRemoveDTO
 * @date 2019/03/18 10:35
 * @description 微信桌贴购物车删除DTO
 * @program holder-saas-store
 */
@ApiModel("微信桌贴购物车删除DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxStickShopCartRemoveDTO {

    @ApiModelProperty("需移除的商品guid集合")
    private List<String> guidList;

    @ApiModelProperty("是否清空购物车，0否，1是")
    private Integer isClear;
}
