package com.holderzone.saas.store.dto.store.store;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreDTO
 * @date 2018/07/23 下午5:06
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
@Accessors(chain = true)
public class OrganizeDTO implements Serializable {

    private static final long serialVersionUID = 7290219444344348603L;

    /**
     * 组织编码
     */
    @ApiModelProperty(value = "组织编码")
    private Long id;

    /**
     * 组织guid
     */
    @ApiModelProperty(value = "组织guid")
    private String organizationGuid;

    /**
     * 组织类型
     */
    @ApiModelProperty(value = "组织类型。1=品牌，2=区域，3=门店。")
    private Integer organizationType;

    /**
     * 父级组织的guid
     */
    @ApiModelProperty(value = "父级组织的guid")
    private String parentOrganizationGuid;

    /**
     * 父级组织名称
     */
    @ApiModelProperty(value = "父级组织名称")
    private String parentOrganizationName;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String name;

    /**
     * 组织为门店条件下的门店面积
     */
    @ApiModelProperty(value = "组织为门店条件下的门店面积")
    private Integer area;

    /**
     * 组织电话
     */
    @ApiModelProperty(value = "组织电话")
    private String tel;

    /**
     * 省份编码
     */
    @ApiModelProperty(value = "省份编码")
    private Integer provinceCode;

    /**
     * 省份名称
     */
    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    /**
     * 城市编码
     */
    @ApiModelProperty(value = "城市编码")
    private Integer cityCode;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String cityName;

    /**
     * 区县编码
     */
    @ApiModelProperty(value = "区县编码")
    private Integer countyCode;

    /**
     * 区县名称
     */
    @ApiModelProperty(value = "区县名称")
    private String countyName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String addressDetail;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String latitude;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String longitude;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 是否已启用
     * 0=未启用
     * 1=已启用
     */
    @ApiModelProperty(value = "是否已启用。0=未启用，1=已启用。")
    private Integer enable;

    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    @ApiModelProperty(value = "是否已删除。0=未删除，1=已删除。")
    private Integer deleted;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间，格式yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间，格式yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 创建组织的用户guid
     */
    @ApiModelProperty(value = "创建组织的用户guid")
    private String createUserGuid;
}
