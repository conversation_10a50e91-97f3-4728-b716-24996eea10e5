package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CartCheckResult {

    @ApiModelProperty(value = "规格guid")
    private String skuGuid;

    @ApiModelProperty(value = "是否成功")
    private boolean isSuccess;

    @ApiModelProperty(value = "校验详情")
    private String errorMsg;

}
