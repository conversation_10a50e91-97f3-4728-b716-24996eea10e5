package com.holderzone.saas.store.dto.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemMessageDTO
 * @date 2018/9/4 17:32
 * @description 发到mq的推送消息dto
 * @program framework-resource-service
 */
@ApiModel
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class SystemMessageDTO {

    @ApiModelProperty(value = "消息等级")
    private String level;

    @ApiModelProperty(value = "消息guid")
    private String messageGuid;

    @ApiModelProperty(value = "消息主题")
    private String subject;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "平台")
    private String platform;

    @ApiModelProperty(value = "系统消息推送至哪些门店")
    private List<EnterpriseDTO> enterpriseList;

}
