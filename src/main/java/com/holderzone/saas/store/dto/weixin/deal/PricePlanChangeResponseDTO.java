package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("微信：价格方案变动信息")
public class PricePlanChangeResponseDTO {

    /**
     * 变动说明
     */
    @ApiModelProperty(value = "变动说明")
    private String changeMessage;

    @ApiModelProperty("变动类型，0切换菜谱；1菜谱变化(当前进行中的菜谱有变化)；2没有变化(依然在当前进行中的菜谱)；3菜谱方案有效，但没有进行中的菜谱；4普通模式")
    private Integer changeType;

    @ApiModelProperty(value = "菜谱GUID")
    private String planGiud;

}
