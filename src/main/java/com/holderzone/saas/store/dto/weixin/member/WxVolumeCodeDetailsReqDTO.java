package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Api("优惠券详情")
@Data
@Builder
public class WxVolumeCodeDetailsReqDTO {

	@ApiModelProperty("企业guid")
	private	String enterpriseGuid;

	@ApiModelProperty("品牌guid")
	private String brandGuid;

	@ApiModelProperty("门店guid")
	private String storeGuid;

	@ApiModelProperty("卡券guid")
	private String memberVolumeGuid;
}
