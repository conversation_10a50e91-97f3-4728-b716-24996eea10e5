package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.weixin.deal.ItemInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PadShoppingCarReqDTO
 * @date 2021/7/pad点餐加入购物车dto
 * @description 属性值保存DTO
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "pad点餐加入购物车dto")
public class PadShoppingCarReqDTO {

    @ApiModelProperty(
            required = true
    )
    @NotNull(
            message = "商品必传"
    )
    private ItemInfoDTO itemInfoDTO;


    @ApiModelProperty(
            required = true
    )
    @NotNull(
            message = "订单guid必传"
    )
    private String orderGuid;

    @ApiModelProperty(
            value = "商品修改数量",
            required = true
    )
    @NotNull(
            message = "加入购物车，数量必传"
    )
    private BigDecimal modifyNum;

    @ApiModelProperty(
            value = "门店guid",
            required = true
    )
    @NotNull(
            message = "门店guid必传"
    )
    private String storeGuid;
}
