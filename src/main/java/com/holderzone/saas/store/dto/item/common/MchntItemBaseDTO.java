package com.holderzone.saas.store.dto.item.common;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MchntDishBaseDTO
 * @date 2018/10/11 下午8:05
 * @description //商户后台商品基础DTO
 * @program holder-saas-store-dto
 */
@Data
public class MchntItemBaseDTO extends BaseDTO {
    private static final long serialVersionUID = 302055149501598943L;

    /**
     * 修改字段描述的同时务必要同步修改枚举中的定义 {@link com.holderzone.saas.store.enums.item.ModuleEntranceEnum}
     */
    @ApiModelProperty(value = "快餐含义为：1：从商品库入口进入,2：从门店商品库入口进入,4:外卖入口;" +
            "正餐参数改为：0：从门店商品库入口进入,1：从商品库入口进入，2.推送,4:外卖入口;5：菜谱方案进入", notes = "从哪个操作模块请求")
    private Integer from;
}
