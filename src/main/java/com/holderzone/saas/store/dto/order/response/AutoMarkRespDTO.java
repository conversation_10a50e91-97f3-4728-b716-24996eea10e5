package com.holderzone.saas.store.dto.order.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 快餐自动号牌 返回
 */
@Data
public class AutoMarkRespDTO implements Serializable {

    private static final long serialVersionUID = -1057808482381728624L;

    @ApiModelProperty(value = "初始号牌")
    private String initMark;

    @ApiModelProperty(value = "自动号牌（0：否，1：是）")
    private Integer autoMark;

    @ApiModelProperty(value = "结束号牌")
    private String endMark;
}
