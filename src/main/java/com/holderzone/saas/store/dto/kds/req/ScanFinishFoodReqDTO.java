package com.holderzone.saas.store.dto.kds.req;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/20
 * @description 扫码出餐请求
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "扫码出餐请求", description = "扫码出餐请求")
public class ScanFinishFoodReqDTO extends BaseDTO {

    private static final long serialVersionUID = -1472324898038999702L;

    @NotBlank(message = "出餐码不能为空")
    @ApiModelProperty(value = "出餐码")
    private String orderNo;

}
