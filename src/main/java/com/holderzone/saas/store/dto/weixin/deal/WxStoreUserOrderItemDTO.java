package com.holderzone.saas.store.dto.weixin.deal;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel("我的订单商品项")
public class WxStoreUserOrderItemDTO {

	@ApiModelProperty(value = "订单guid")
	private String guid;

	@ApiModelProperty(value = "门店名")
	private String storeName;

	@ApiModelProperty(value = "品牌名")
	private String brandName;

	@ApiModelProperty(value = "品牌图片")
	private String logUrl;

	@ApiModelProperty(value = "0：正餐，1：快餐")
	private Integer tradeMode;

	@ApiModelProperty(value = "金额")
	private BigDecimal actuallyPayFee;


	@ApiModelProperty(value = "是否有会员价:1有：0：没有")
	private Integer useDisCount = 0;

	@ApiModelProperty(value = "优惠金额")
	private BigDecimal disCountFee;

	@ApiModelProperty(value = "原始金额")
	private BigDecimal originalFee;

	@ApiModelProperty(value = "状态(0:待确认，1待支付，2:已完成，3:已取消 )")
	private Integer state;

	@ApiModelProperty(value = "结算时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime checkoutTime;

	@ApiModelProperty(value = "商品名称")
	private List<String> itemNames;

}
