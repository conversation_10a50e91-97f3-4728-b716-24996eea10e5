package com.holderzone.saas.store.dto.order.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PackageSubgroupDTO
 * @date 2019/01/04 10:32
 * @description 套餐分组对象
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class FreeItemDTO implements Serializable {

    private static final long serialVersionUID = -4494021919876153491L;

    @ApiModelProperty(value = "guid", required = true)
    private String guid;

    @ApiModelProperty(value = "调整反结账赠送的商品时，加的调整数字段")
    private BigDecimal adjustNumber;

    @ApiModelProperty(value = "赠送原因", required = true)
    private String reason;

    @ApiModelProperty(value = "赠送数量", required = true)
    private BigDecimal count;

    @ApiModelProperty(value = "催品次数", required = true)
    private Integer urgeNum;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "商品价格小计", required = true)
    private BigDecimal itemPrice;

    @ApiModelProperty(value = "商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂 ，8.已上菜(已划菜) )", required = true)
    private Integer itemState;

    @ApiModelProperty(value = "赠菜退菜数量")
    private BigDecimal refundCount;

    @ApiModelProperty(value = "订单明细guid")
    private Long orderItemGuid;

    @ApiModelProperty(value = "优惠单价")
    private BigDecimal discountPrice;

    @ApiModelProperty(value = "优惠总价")
    private BigDecimal discountTotalPrice;
}
