package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

@NoArgsConstructor
@AllArgsConstructor
@RequiredArgsConstructor
@Accessors
@Builder
@Data
@Api("会员选择确认")
public class WxPrepayConfirmRespDTO {

	private String errorMsg;

	@NonNull
	@ApiModelProperty(value = "0:正常，1：无法选中")
	private Integer result;
}
