package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description Pad点餐规格列表返回实体
 * @date 2021/7/29 12:01
 */
@Data
@ApiModel(value = "Pad点餐规格列表返回实体")
public class PadSkuRespDTO {

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "规格GUID", required = true)
    private String skuGuid;

    @ApiModelProperty(value = "maybe null,当日库存")
    private Integer stock;

    @ApiModelProperty(value = "是否开启库存（0：否 ， 1：是）")
    private Integer isOpenStock;

    @ApiModelProperty(value = "maybe null,规格名称")
    private String name;

    @ApiModelProperty(value = "售卖价格", required = true)
    private BigDecimal salePrice;

    @ApiModelProperty(value = "成本价格")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "商品毛利率")
    private BigDecimal itemGrossMargin;

    @ApiModelProperty(value = "会员价格")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "是否加入整单折扣(0：否，1：是)", required = true)
    private Integer isWholeDiscount;

    @ApiModelProperty(value = "是否参与会员折扣（0：否，1：是）", required = true)
    private Integer isMemberDiscount;

    @ApiModelProperty(value = "起卖数(非称重即为整数，称重即为小数)", required = true)
    private BigDecimal minOrderNum;

    @ApiModelProperty(value = "计数单位", required = true)
    private String unit;

    @ApiModelProperty(value = "maybe null,编号")
    private String code;

    @ApiModelProperty(value = "是否上下架")
    private Integer isRack;

    @ApiModelProperty(value = "是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;

    /**
     * 品牌库对应的skuGuid：如果是自己创建的内容，则此字段为skuGuid，如果是被推送过来的商品，则该字段为原skuGuid。
     */
    @ApiModelProperty(value = "品牌库对应的skuGuid：如果是自己创建的内容，则此字段为skuGuid，如果是被推送过来的商品，则该字段为原skuGuid")
    private String parentGuid;
}
