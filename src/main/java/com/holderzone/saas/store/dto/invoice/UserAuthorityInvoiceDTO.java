package com.holderzone.saas.store.dto.invoice;

import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Accessors(chain = true)
public class UserAuthorityInvoiceDTO implements Serializable {


    /**
     * guid
     */
    private String guid;

    /**
     * 被授权人guid，员工guid
     */
    private String userGuid;

    /**
     * 资源code
     */
    private String sourceCode;

    /**
     * 电子税务局注册人姓名
     */
    private String electronicTaxpayerName;

    /**
     * 电子税务局注册手机号
     */
    private String electronicTaxpayerPhone;

    /**
     * 授权码
     */
    private String authorityCode;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 授权人guid
     */
    private String authorizerGuid;

    /**
     * 授权人账户
     */
    private String authorizerAccount;

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public void setAuthorityCode(String authorityCode) {
        this.authorityCode = authorityCode;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public void setAuthorizerGuid(String authorizerGuid) {
        this.authorizerGuid = authorizerGuid;
    }

    public void setAuthorizerAccount(String authorizerAccount) {
        this.authorizerAccount = authorizerAccount;
    }

    public void setElectronicTaxpayerName(String electronicTaxpayerName) {
        this.electronicTaxpayerName = electronicTaxpayerName;
    }

    public void setElectronicTaxpayerPhone(String electronicTaxpayerPhone) {
        this.electronicTaxpayerPhone = electronicTaxpayerPhone;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    @Override
    public String toString() {
        return "UserAuthorityInvoiceDTO{" +
                "guid='" + guid + '\'' +
                ", userGuid='" + userGuid + '\'' +
                ", sourceCode='" + sourceCode + '\'' +
                ", authorityCode='" + authorityCode + '\'' +
                ", isDelete=" + isDelete +
                ", authorizerGuid='" + authorizerGuid + '\'' +
                ", authorizerAccount='" + authorizerAccount + '\'' +
                ", electronicTaxpayerName='" + electronicTaxpayerName + '\'' +
                ", electronicTaxpayerPhone='" + electronicTaxpayerPhone + '\'' +
                ", gmtCreate=" + gmtCreate +
                ", gmtModified=" + gmtModified +
                '}';
    }
}
