package com.holderzone.saas.store.dto.kds.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.saas.store.dto.kds.ItemComparable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class PrdDstOrderDTO  implements ItemComparable, Serializable {

    private static final long serialVersionUID = -2240162461066846742L;

    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @ApiModelProperty(value = "订单类型：外卖1，快餐2，正餐4")
    private Integer orderType;

    @ApiModelProperty(value = "订单类型描述：正餐-正餐，快餐-快餐，外卖-外卖")
    private String orderDesc;

    @ApiModelProperty(value = "订单号：正餐-订单号，快餐-订单号，外卖-订单号")
    private String orderNumber;

    @ApiModelProperty(value = "订单流水号：正餐-桌台名，快餐-牌号，外卖-日流水号")
    private String orderSerialNo;

    @ApiModelProperty(value = "订单备注")
    private String orderRemark;

    @ApiModelProperty(value = "商品列表")
    private List<PrdDstItemDTO> items;

    /******************排序字段如下******************/

    @JsonIgnore
    @ApiModelProperty(value = "催菜时间")
    private LocalDateTime urgedTime;

    @JsonIgnore
    @ApiModelProperty(value = "叫起时间")
    private LocalDateTime callUpTime;

    @JsonIgnore
    @ApiModelProperty(value = "准备时间")
    private LocalDateTime prepareTime;

    @JsonIgnore
    @ApiModelProperty(value = "挂起时间")
    private LocalDateTime hangUpTime;

    /**
     * 加菜时间
     */
    private LocalDateTime addItemTime;

    /**
     * 首次加菜时间
     */
    private LocalDateTime firstAddItemTime;

    @Override
    @JsonIgnore
    public LocalDateTime getUrgedTimeForSort() {
        return urgedTime;
    }

    @Override
    public Integer getAddItemTimeForSort() {
        if (null == addItemTime || null == firstAddItemTime || addItemTime.isAfter(firstAddItemTime)) {
            return 0;
        }
        return 1;
    }

    @Override
    @JsonIgnore
    public LocalDateTime getCallUpTimeForSort() {
        return callUpTime;
    }

    @Override
    @JsonIgnore
    public LocalDateTime getPrepareTimeForSort() {
        return prepareTime;
    }

    @Override
    @JsonIgnore
    public LocalDateTime getHangUpTimeForSort() {
        return hangUpTime;
    }

    @Override
    @JsonIgnore
    public Integer getSort() {
        return null;
    }

    @Override
    @JsonIgnore
    public Integer getBatch() {
        return null;
    }

}
