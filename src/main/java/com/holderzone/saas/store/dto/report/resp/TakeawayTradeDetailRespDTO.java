package com.holderzone.saas.store.dto.report.resp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 团购订单结算明细
 */
@Data
public class TakeawayTradeDetailRespDTO implements Serializable {

    private static final long serialVersionUID = -1667368987764335434L;

    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 账单日期
     */
    private String orderCreateDate;

    /**
     * 订单来源 0：美团 1：饿了么 6:赚餐
     */
    private Integer takeoutOrderType;

    /**
     * 订单状态
     * 1 用户已提交订单,2 可推送到APP方平台也可推送到商家,3 商家已收到,4 商家已确认,8 已完成,9 已取消
     */
    private Integer status;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 商品金额=菜品原价＋餐盒费＋赠品原价（铺货模式赠品原值为0）,保留2为小数
     */
    private BigDecimal foodAmount;

    /**
     * 配送费,保留2位小数
     */
    private BigDecimal shippingAmount;

    /**
     * 打包袋价格
     */
    private BigDecimal packageBagMoney;

    /**
     * 顾客实际支付
     */
    private BigDecimal userPayTotalAmount;

    /**
     * 支付类型（1:货到付款；2:在线支付）
     */
    private Integer payType;

    /**
     * 结算金额(商家收入)
     */
    private BigDecimal settleAmount;


    private String orderDaySn;

    private String orderCreateTime;

    private String orderCompleteTime;

    /**
     * 顾客单买单送费
     */
    private BigDecimal singleIncreaseAmount;

    /**
     * 顾客支付给商家
     */
    private BigDecimal phfPayTotalAmountForPoi;

    /**
     * 总活动款
     */
    private BigDecimal totalActivityAmount;

    /**
     * 活动成本明细
     */
    private String activityDetails;

    /**
     * 抽佣金额
     */
    private BigDecimal commisionAmount;

    /**
     * 平台活动补贴
     */
    private BigDecimal platformPayForPoiAmount;

    /**
     * 商家费用合计
     */
    private BigDecimal totalMerchantFees;

    /**
     * 美团应付给商家
     */
    private BigDecimal offlineOrderSkPayAmount;

    /**
     * 配送方式
     */
    private String shippingType;
}
