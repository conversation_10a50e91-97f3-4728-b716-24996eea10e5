package com.holderzone.saas.store.dto.kds.resp;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class ProductionPointRespDTO implements Serializable {

    private static final long serialVersionUID = 7519855724435157205L;

    /**
     * 堂口Guid
     */
    private String guid;

    /**
     * 堂口名称
     */
    private String name;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 设备Guid
     */
    private String deviceId;

    /**
     * 已绑定商品数量
     */
    private Integer boundItemCount;
}
