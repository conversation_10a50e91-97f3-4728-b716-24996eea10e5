package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Data
@ApiModel("0元支付响应")
public class ZeroPayResultRespDTO {

	@ApiModelProperty(value = "0:成功,1失败")
	private Integer result;

	@ApiModelProperty(value = "失败原因")
	private String errorMsg;

	public static ZeroPayResultRespDTO success() {
		return new ZeroPayResultRespDTO(0, null);
	}

	public static ZeroPayResultRespDTO changeFailed(){
		return new ZeroPayResultRespDTO(1, "订单详情发生变化");
	}

	public static ZeroPayResultRespDTO payFailed(){
		return new ZeroPayResultRespDTO(1, "支付失败");
	}

	public static ZeroPayResultRespDTO soldOut(){
		return new ZeroPayResultRespDTO(1, "商品售尽");
	}

	public static ZeroPayResultRespDTO itemSynchronize(){
		return new ZeroPayResultRespDTO(1, "商品没有同步，无法使用会员支付");
	}

	public static ZeroPayResultRespDTO payAmountFailed(){
		return new ZeroPayResultRespDTO(1, "支付异常，请刷新后重试");
	}
}
