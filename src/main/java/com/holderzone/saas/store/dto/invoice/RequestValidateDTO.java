package com.holderzone.saas.store.dto.invoice;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@ApiModel(description = "开票校验")
@Data
public class RequestValidateDTO implements Serializable {

    @ApiModelProperty(value = "userGuid")
    private String userGuid;

    @ApiModelProperty(value = "account")
    private String account;

    @ApiModelProperty(value = "accountName")
    private String accountName;

    @ApiModelProperty(value = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "门店")
    private String storeGuid;

    @ApiModelProperty(value = "开票金额")
    private BigDecimal orderAmount;

    private String sms;
}
