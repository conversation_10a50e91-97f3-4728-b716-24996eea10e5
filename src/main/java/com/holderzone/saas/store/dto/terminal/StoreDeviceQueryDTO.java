package com.holderzone.saas.store.dto.terminal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className StoreTreminalQueryDTO
 * @date 18-9-6 上午10:23
 * @description 设备管理查询DTO
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class StoreDeviceQueryDTO implements Serializable {

    private static final long serialVersionUID = -1331083160455095942L;

    /**
     * 门店guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    /**
     * 设备类型
     * PC服务端- 0
     * PC平板- 1
     * 小店通- 2
     * 一体机- 3
     * POS机- 4
     * 云平板- 5
     * 点菜宝(M1)- 6
     * PV1(带刷卡的点菜宝)- 7
     */
    @ApiModelProperty(value = "PC服务端- 0、PC平板- 1、小店通- 2、一体机- 3、POS机- 4、云平板- 5、" +
            "点菜宝(M1)- 6、PV1(带刷卡的点菜宝)- 7")
    private Integer deviceType;
}
