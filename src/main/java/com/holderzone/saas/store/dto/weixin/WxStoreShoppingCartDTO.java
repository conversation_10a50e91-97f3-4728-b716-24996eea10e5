package com.holderzone.saas.store.dto.weixin;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.List;

/**
 * 	@describle	购物车传输对象
 * 	@athor		zhouxinwen
 * 	@dateTime	2019/3/1
 */
@ApiModel("购物车对象")
@Data
public class WxStoreShoppingCartDTO{

	@ApiModelProperty(value = "购物车唯一标识")
	private String guid;

	@ApiModelProperty(value = "用户备注")
	private String remark;

	@ApiModelProperty(value="是否是快餐，快餐：1，正餐：0",required = true)
	private Integer isSnack;
	@NotBlank
	@ApiModelProperty(value = "桌台guid",required=true)
	private String diningTableGuid;

	@NotBlank
	@ApiModelProperty(value = "桌台号", required = true)
	private String tableCode;
	@NotBlank
	@ApiModelProperty(value = "桌台名字",required=true)
	private String diningTableName;
	@NotBlank
	@ApiModelProperty(value = "桌台所在区域名称",required=true)
	private String areaName;

	@ApiModelProperty(value = "就餐人数",required = true)
	private int guestCount;

	@ApiModelProperty(value = "菜品集合")
	private List<WxStoreCartItemDTO> wxStoreCartItemDTOS=new LinkedList<>();

	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@ApiModelProperty(value="创建时间")
	private LocalDateTime gmtCreate;
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@ApiModelProperty(value="修改时间")
	private LocalDateTime gmtUpdate;
}
