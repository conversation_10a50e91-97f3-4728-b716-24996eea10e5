package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemDiscountDTO
 * @date 2018/08/15 18:07
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class SystemDiscountDTO implements Serializable {

    @ApiModelProperty(value = "storeName")
    private String storeName;

    @ApiModelProperty(value = "storeGuid")
    private String storeGuid;

    @ApiModelProperty(value = "批量新增的门店guid")
    private List<StoreDTO> storeDTOS;

    @ApiModelProperty(value = "系统省零Guid")
    private String systemDiscountGuid;

    @ApiModelProperty(value = "折扣临界值")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "省零方式 0不省零,1：四舍五入，2：只舍不入，3：只入不舍")
    private Integer roundType;

    @ApiModelProperty(value = "保留到，0:保留到元，1:保留到角")
    private Integer scale;

    @ApiModelProperty(value = "0:启用，1:禁用")
    private Integer state;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

}
