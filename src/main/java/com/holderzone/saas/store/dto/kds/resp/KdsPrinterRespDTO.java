package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class KdsPrinterRespDTO implements Serializable {

    private static final long serialVersionUID = -2773042054434843563L;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "设备Guid")
    private String deviceId;

    @ApiModelProperty(value = "打印机Guid")
    private String printerGuid;

    @ApiModelProperty(value = "打印机名称")
    private String printerName;

    @ApiModelProperty(value = "IP地址")
    private String printerIp;

    @ApiModelProperty(value = "端口：0-65535，9100")
    private Integer printerPort;

    @ApiModelProperty(value = "纸张宽度：只支持58、80")
    private Integer pageSize;

    @ApiModelProperty(value = "是否由当前设备绑定")
    private Boolean isBoundBySelf;
}
