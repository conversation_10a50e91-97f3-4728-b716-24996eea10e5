package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.concurrent.TimeUnit;

@ApiModel
@Data
public class ItemRedisReqDTO implements Serializable {

    @ApiModelProperty("存入的值（不能空）")
    private String redisValue;

    @ApiModelProperty("存入的key")
    private String redisKey;

    @ApiModelProperty("过期时间")
    private Long redisTime;

    @ApiModelProperty("时间单位")
    private TimeUnit timeUnit;
}
