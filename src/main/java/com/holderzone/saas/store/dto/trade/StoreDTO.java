package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreDTO
 * @date 2018/09/13 14:18
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class StoreDTO implements Serializable {

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店name")
    private String storeName;

    @ApiModelProperty(value = "门店经营类型")
    private String mchntTypeCode;

}
