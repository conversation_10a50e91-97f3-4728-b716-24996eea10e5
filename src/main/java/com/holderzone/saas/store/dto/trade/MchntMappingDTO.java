package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MchntMappingDTO
 * @date 2018/08/13 10:11
 * @description
 * @program holder-saas-store-dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MchntMappingDTO {

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id")
    private String appId;

    /**
     * 应用密钥
     */
    @ApiModelProperty(value = "应用密钥")
    private String appSecret;


}
