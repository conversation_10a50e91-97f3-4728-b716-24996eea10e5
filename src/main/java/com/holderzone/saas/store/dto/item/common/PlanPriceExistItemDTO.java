package com.holderzone.saas.store.dto.item.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 价格方案已存在的商品信息
 * @date 2021/10/11
 */
@Data
public class PlanPriceExistItemDTO {

    private String planGuid;

    private String planName;

    private Integer status;

    private Integer sellTimeType;

    private String brandGuid;

    private List<String> itemGuidList;

    @ApiModelProperty(value = "起始时间")
    private LocalTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalTime endTime;

}
