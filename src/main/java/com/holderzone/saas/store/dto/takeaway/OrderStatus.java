package com.holderzone.saas.store.dto.takeaway;

/**
 * 待处理
 * 待配送（已接单）（有催单标记）
 * 配送中（有催单标记）
 * 已送达（已完成）
 * 已取消（已退款）
 * 退款中
 *
 * <AUTHOR>
 * @Description 订单状态
 * @time 2017年7月25日 下午4:43:05
 */
public interface OrderStatus {

    /**
     * 订单已取消
     */
    int CANCELED = -1;

    /**
     * 订单待处理
     */
    int TO_DO = 0;

    /**
     * 订单已接单/待配送
     */
    int TO_SHIP = 10;

    /**
     * 订单配送中
     */
    int SHIPPING = 20;

    /**
     * 订单配送完成
     */
    int SHIP_FINISHED = 30;

    /**
     * 订单已完成
     */
    int FINISHED = 100;
}
