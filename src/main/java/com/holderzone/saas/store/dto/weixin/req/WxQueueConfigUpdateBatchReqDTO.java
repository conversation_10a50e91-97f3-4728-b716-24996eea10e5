package com.holderzone.saas.store.dto.weixin.req;

import com.holderzone.saas.store.dto.weixin.resp.WxQueueConfigDTO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueConfigUpdateBatchReqDTO
 * @date 2019/05/10 11:45
 * @description 门店微信线上排队配置修改请求入参
 * @program holder-saas-store
 */
@ApiModel("门店微信线上排队配置修改请求入参")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxQueueConfigUpdateBatchReqDTO {
    private List<String> storeGuidList;

    private WxQueueConfigDTO wxQueueConfigDTO;
}
