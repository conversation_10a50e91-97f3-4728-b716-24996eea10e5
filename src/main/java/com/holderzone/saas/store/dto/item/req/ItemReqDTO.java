package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemSaveReqDTO
 * @date 2019/01/08 上午9:53
 * @description //正餐商品修改的请求实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel("商品请求实体")
@EqualsAndHashCode(callSuper = true)
public class ItemReqDTO extends ItemStringListDTO {

    @ApiModelProperty(value = "商品SPU编号")
    private String spu;

    @ApiModelProperty(value = "商品GUID")
    private String itemGuid;

    @ApiModelProperty(value = "分类GUID")
    @NotEmpty
    private String typeGuid;

    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "商品类型：（0：门店自己创建的商品，1：品牌自己创建的商品,2:被推送过来的商品）")
    private Integer itemFrom;

    @ApiModelProperty(value = "商品类型：1.套餐（不称重，无规格），2.规格商品（不称重），3.称重商品（单商品，称重） 4:单品")
    /**
     *    单品包括在规格商品中
     */
    @NotNull
    private Integer itemType;

    @ApiModelProperty(value = "是否有属性:0 否 1 是")
    private Integer hasAttr;

    @ApiModelProperty(value = "商品名称")
    @NotEmpty
    @Size(max = 40)
    private String name;

    @ApiModelProperty(value = "拼音简码")
    @NotEmpty
    @Size(max = 40)
    private String pinyin;

    @ApiModelProperty(value = "商品名称简写（别名）")
    @Size(max = 40)
    private String nameAbbr;

    @ApiModelProperty(value = "排序")
    @Max(99999999)
    private Integer sort;

    @ApiModelProperty(value = "商品主图路径")
    private String pictureUrl;

    @ApiModelProperty(value = "是否热销：0：否，1：是")
    @NotNull
    private Integer isBestseller;

    @ApiModelProperty(value = "是否新品：0：否，1：是")
    @NotNull
    private Integer isNew;

    @ApiModelProperty(value = "是否是招牌：0：否，1：是")
    @NotNull
    private Integer isSign;

    @ApiModelProperty(value = "是否推荐：0：否，1：是")
    private Integer isRecommend;

    @ApiModelProperty(value = "商品描述")
    @Size(max = 200)
    private String description;

    @ApiModelProperty(value = "规格集合")
    @NotEmpty
    private List<@Valid SkuSaveReqDTO> skuList;

    @ApiModelProperty(value = "商品属性集合")
    @Size(max = 20, message = "最多可配置10组属性，请调整")
    private List<@Valid ItemAttrGroupReqDTO> attrGroupList;

    @ApiModelProperty(value = "更新版本 1：商超版  null：餐饮版")
    private Integer flag;

    @ApiModelProperty(value = "企业guid（冗余小程序端字段）")
    private String enterpriseGuid;

    @ApiModelProperty(value = "商品编码（冗余小程序端字段）")
    private String code;

    @ApiModelProperty(value = "审核状态（冗余小程序端字段）")
    private Integer auditStatus;

    @ApiModelProperty(value = "图文详情（冗余小程序端字段）")
    private String remarkDetail;

    @ApiModelProperty(value = "视频url json数组（冗余小程序端字段）")
    private String videoUrls;

    @ApiModelProperty(value = "好评次数（冗余小程序端字段）")
    private Integer upCount;

    @ApiModelProperty(value = "差评次数（冗余小程序端字段）")
    private Integer downCount;

    @ApiModelProperty(value = "配送类型 1同城配送;2快递运输（冗余小程序端字段）")
    private Integer mallDelivery;

    @ApiModelProperty(value = "打印机guid")
    private String printerGuid;

    @ApiModelProperty(value = "员工guid; 必传", required = true)
    private String userGuid;

    @ApiModelProperty(value = "分类名称")
    private String typeName;

    @ApiModelProperty(value = "打印机guid列表")
    private List<String> printerGuidList;
}
