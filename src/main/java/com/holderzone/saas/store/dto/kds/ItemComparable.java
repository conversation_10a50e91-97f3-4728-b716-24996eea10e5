package com.holderzone.saas.store.dto.kds;

import java.time.LocalDateTime;
import java.util.Comparator;

import static java.util.Comparator.*;

public interface ItemComparable extends Comparable<ItemComparable> {

    LocalDateTime getUrgedTimeForSort();

    Integer getAddItemTimeForSort();

    LocalDateTime getCallUpTimeForSort();

    LocalDateTime getPrepareTimeForSort();

    LocalDateTime getHangUpTimeForSort();

    Integer getSort();

    Integer getBatch();

    @Override
    default int compareTo(ItemComparable o) {
        Comparator<ItemComparable> comparator =
                comparing(ItemComparable::getUrgedTimeForSort, nullsLast(naturalOrder()))
                        .thenComparing(ItemComparable::getAddItemTimeForSort, nullsLast(naturalOrder()))
                        .thenComparing(ItemComparable::getCallUpTimeForSort, nullsLast(naturalOrder()))
                        .thenComparing(ItemComparable::getSort, nullsLast(naturalOrder()))
                        .thenComparing(ItemComparable::getBatch, nullsLast(naturalOrder()))
                        .thenComparing(ItemComparable::getPrepareTimeForSort, nullsLast(naturalOrder()))
                        .thenComparing(ItemComparable::getHangUpTimeForSort, nullsLast(naturalOrder()));
        return comparator.compare(this, o);
    }

    default int compareTo2(ItemComparable o) {
        Comparator<ItemComparable> comparator =
                comparing(ItemComparable::getUrgedTimeForSort, nullsLast(naturalOrder()))
                        .thenComparing(ItemComparable::getAddItemTimeForSort, nullsLast(naturalOrder()))
                        .thenComparing(ItemComparable::getCallUpTimeForSort, nullsLast(naturalOrder()))
                        .thenComparing(ItemComparable::getPrepareTimeForSort, nullsLast(naturalOrder()))
                        .thenComparing(ItemComparable::getHangUpTimeForSort, nullsLast(naturalOrder()));
        return comparator.compare(this, o);
    }
}
