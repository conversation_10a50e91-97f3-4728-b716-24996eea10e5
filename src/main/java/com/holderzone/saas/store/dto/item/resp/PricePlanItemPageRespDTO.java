package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description 方案菜品分页查询返回
 * @date 2021/6/1 11:24
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "方案菜品分页查询返回")
public class PricePlanItemPageRespDTO {

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "商品名")
    private String name;

    @ApiModelProperty(value = "分类名")
    private String typeName;

    @ApiModelProperty(value = "方案商品名称")
    private String planItemName;

    @ApiModelProperty(value = "商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品 5.团餐。")
    private Integer itemType;

    @ApiModelProperty(value = "规格信息")
    private List<PricePlanSkuRespDTO> skuList;

}