package com.holderzone.saas.store.dto.journaling.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RecoveryOrderRespDTO
 * @date 2019/06/03 11:20
 * @description 反结账订单响应DTO
 * @program holder-saas-store
 */
@ApiModel(value = "反结账订单响应DTO")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class RecoveryOrderRespDTO implements Serializable {

    private static final long serialVersionUID = 1061467628122317323L;

    @ApiModelProperty(value = "反结账订单guid")
    private String recoveryOrderGuid;

    @ApiModelProperty(value = "反结账订单号")
    private String recoveryOrderNo;

    @ApiModelProperty(value = "反结账订单状态")
    private String stateName;

    @ApiModelProperty(value = "反结账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime recoveryTime;

    @ApiModelProperty(value = "反结账金额")
    private BigDecimal recoveryFee;

    @ApiModelProperty(value = "反结账实付金额")
    private BigDecimal actuallyFee;

    @ApiModelProperty(value = "原单号")
    private String originalOrderNo;

    @ApiModelProperty(value = "原单金额")
    private BigDecimal originalFee;

    @ApiModelProperty(value = "原单实付金额")
    private BigDecimal originalActuallyFee;

    @ApiModelProperty(value = "反结账操作人")
    private String recoveryOperation;

    @ApiModelProperty(value = "反结账原因")
    private String recoveryReason;

    @ApiModelProperty(value = "反结账作废原因")
    private String cancelReason;

    @ApiModelProperty(value = "反结账作废操作者")
    private String cancelOperation;
}
