package com.holderzone.saas.store.dto.user;

import com.holderzone.resource.common.dto.data.ServerDTO;
import com.holderzone.resource.common.dto.product.MenuDTO;
import com.holderzone.resource.common.dto.product.ModuleDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className TerminalDTO
 * @date 18-9-17 下午5:41
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ModuleSourceDTO extends ModuleDTO {
    /**
     * 关联的资源服务列表
     */
    @ApiModelProperty(value = "服务列表,必传（终端类型为0/app有效）")
    private List<ServerDTO> serverList;

    /**
     * 关联的菜单列表
     */
    @ApiModelProperty(value = "菜单列表（终端类型为1/web有效）")
    private List<MenuDTO> menuDTOList;
}

