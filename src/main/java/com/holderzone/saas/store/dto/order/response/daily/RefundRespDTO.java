package com.holderzone.saas.store.dto.order.response.daily;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 营业日报-退款款统计
 *
 * <AUTHOR>
 * @date 2025/5/13
 * @since 1.8
 */
@Data
@ApiModel
public class RefundRespDTO {

    @ApiModelProperty(value = "收银员")
    private List<String> checkoutStaffs;

    @ApiModelProperty(value = "退款统计")
    private List<RefundAmountDTO> refundAmounts;
}
