package com.holderzone.saas.store.dto.item.req;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateRecoverReqDTO
 * @date 2019/05/08 14:23
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
public class EstimateRecoverReqDTO {

    /**
     * 配置表guid
     */
    private String guid;

    /**
     * 估清自动置满时间  时间格式 hh:mm:ss
     */
    private String recoveryTime;

    /**
     * 是否启用  0：启用  1：不启用
     */
    private Integer isEnable;

    /**
     * 门店guid
     */
    private String storeGuid;
}
