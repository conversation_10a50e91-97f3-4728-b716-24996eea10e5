package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class DstAreaRespDTO implements Serializable {

    private static final long serialVersionUID = -1158994112223699540L;

    @ApiModelProperty(value = "区域列表")
    private List<DstAreaStatusDTO> areaList;

    @ApiModelProperty(value = "快餐状态")
    private DstAreaStatusDTO snackStatus;

    @ApiModelProperty(value = "外卖状态")
    private DstAreaStatusDTO takeoutStatus;
}
