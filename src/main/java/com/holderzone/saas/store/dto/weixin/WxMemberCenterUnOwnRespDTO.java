package com.holderzone.saas.store.dto.weixin;

import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListUnowned;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Api("未开通卡列表")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class WxMemberCenterUnOwnRespDTO {

	@ApiModelProperty("会员未开通的会员卡")
	private List<ResponseMemberCardListUnowned> cardList;
}
