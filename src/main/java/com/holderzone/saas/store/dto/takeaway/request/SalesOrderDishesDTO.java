package com.holderzone.saas.store.dto.takeaway.request;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class SalesOrderDishesDTO {

    @NotNull(message = "订单id不得为空")
    @ApiModelProperty(value = "订单id")
    public Integer SalesOrderId;

    @NotNull(message = "菜品id不得为空")
    @ApiModelProperty(value = "菜品id(下单必传用：于打印机匹配需打印的菜品)")
    public Integer DishesId;

    @NotNull(message = "菜品类型Id不得为空")
    @ApiModelProperty(value = "菜品类型Id")
    public Integer DishesTypeId;

    @NotBlank(message = "菜品名称必传不得为空")
    @ApiModelProperty(value = "菜品名称必传")
    public String DishesName;

    @NotNull(message = "菜品规格id不得为空")
    @ApiModelProperty(value = "菜品规格id")
    public Integer DishesSpecId;

    @NotNull(message = "数量不得为空")
    @ApiModelProperty(value = "数量")
    public Integer Number;

    @NotNull(message = "餐盒费不得为空")
    @ApiModelProperty(value = "餐盒费")
    public BigDecimal MealsFee;

    @NotNull(message = "单价不得为空")
    @ApiModelProperty(value = "单价")
    public BigDecimal Price;

    @NotNull(message = "合计金额不得为空")
    @ApiModelProperty(value = "合计金额")
    public BigDecimal Amount;

    @ApiModelProperty(value = "备注(例如:加糖，加辣)")
    public String Remark;

    @ApiModelProperty(value = "第三方菜品ID")
    public String ThirdSkuId;

}
