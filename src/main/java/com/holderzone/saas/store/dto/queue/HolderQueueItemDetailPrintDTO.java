package com.holderzone.saas.store.dto.queue;

import com.holderzone.saas.store.dto.print.content.PrintDTO;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HolderQueueItemDetailPrintDTO
 * @date 2019/03/28 14:09
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
@Deprecated
@Accessors(chain = true)
public class HolderQueueItemDetailPrintDTO extends PrintDTO {
    private HolderQueueItemDetailDTO detail;
    private String qrCode;
}