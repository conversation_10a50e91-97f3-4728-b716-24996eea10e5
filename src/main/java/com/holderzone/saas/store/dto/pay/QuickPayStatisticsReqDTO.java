package com.holderzone.saas.store.dto.pay;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/6
 * @description 快速收款统计请求
 */
@Data
@ApiModel(value = "快速收款统计请求")
@EqualsAndHashCode(callSuper = false)
public class QuickPayStatisticsReqDTO implements Serializable {

    private static final long serialVersionUID = 5432657548355776440L;

    /**
     * 企业guid
     */
    @ApiModelProperty("企业guid")
    private String enterpriseGuid;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "开始日期时间")
    protected LocalDateTime startDateTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "结束日期时间")
    protected LocalDateTime endDateTime;

    @ApiModelProperty(value = "门店guid列表")
    private List<String> storeGuidList;

}
