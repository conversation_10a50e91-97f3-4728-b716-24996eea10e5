package com.holderzone.saas.store.dto.order.request.item;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/7
 * @description 换菜请求
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "换菜请求")
public class TransferItemReqDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -7446445661293329654L;

    @ApiModelProperty(value = "订单商品guid")
    private List<TransferItemDetailsReqDTO> itemDetailsDTOList;

    @OrderLockField
    @ApiModelProperty(value = "原订单guid")
    private String oldOrderGuid;

    @ApiModelProperty(value = "新订单guid")
    private String newOrderGuid;

    @JsonIgnore
    public Map<String, BigDecimal> getTransferItemMap() {
        return this.itemDetailsDTOList.stream()
                .collect(Collectors.toMap(TransferItemDetailsReqDTO::getOrderItemGuid, TransferItemDetailsReqDTO::getTransferCount));
    }

    @JsonIgnore
    public Set<String> getOldOrderItemGuids() {
        return getTransferItemMap().keySet();
    }
}
