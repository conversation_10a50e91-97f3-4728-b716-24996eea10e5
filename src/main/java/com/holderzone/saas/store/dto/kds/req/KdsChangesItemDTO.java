package com.holderzone.saas.store.dto.kds.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * kds换菜单
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KdsChangesItemDTO extends BaseDTO {

    private static final long serialVersionUID = -6416994103627068329L;

    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "桌台号")
    private String diningTableName;

    @ApiModelProperty(value = "创建人")
    private String createStaffName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "是否撤销套餐换菜")
    private Boolean cancelFlag;

    @ApiModelProperty(value = "原菜品")
    private KdsItemDTO originalKdsItem;

    @ApiModelProperty(value = "原菜品")
    private List<KdsItemDTO> originalKdsItemList;

    @ApiModelProperty(value = "更换菜品")
    private KdsItemDTO changesKdsItem;

    @ApiModelProperty(value = "更换菜品")
    private List<KdsItemDTO> changesKdsItemList;

    @ApiModelProperty(value = "打印设备")
    private Set<String> deviceIds;

    @ApiModelProperty(value = "打印设备绑定菜品map")
    private Map<String, List<String>> deviceItemMap;
}
