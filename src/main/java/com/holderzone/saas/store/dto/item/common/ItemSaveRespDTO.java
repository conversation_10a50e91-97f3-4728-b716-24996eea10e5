package com.holderzone.saas.store.dto.item.common;

import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 保存商品返回实体
 * @date 2021/1/19 11:18
 */
public class ItemSaveRespDTO {

    /**
     * 商品guid
     */
   private String itemGuid;

    /**
     * 推送所用guid
     */
   private List<String> insertSkuGuidList;

    public String getItemGuid() {
        return itemGuid;
    }

    public void setItemGuid(String itemGuid) {
        this.itemGuid = itemGuid;
    }

    public List<String> getInsertSkuGuidList() {
        return insertSkuGuidList;
    }

    public void setInsertSkuGuidList(List<String> insertSkuGuidList) {
        this.insertSkuGuidList = insertSkuGuidList;
    }

    public ItemSaveRespDTO() {
    }

    public ItemSaveRespDTO(String itemGuid, List<String> insertSkuGuidList) {
        this.itemGuid = itemGuid;
        this.insertSkuGuidList = insertSkuGuidList;
    }

    public ItemSaveRespDTO(String itemGuid) {
        this.itemGuid = itemGuid;
    }

    public ItemSaveRespDTO(List<String> insertSkuGuidList) {
        this.insertSkuGuidList = insertSkuGuidList;
    }

    @Override
    public String toString() {
        return "ItemSaveRespDTO{" +
                "itemGuid='" + itemGuid + '\'' +
                ", insertSkuGuidList=" + insertSkuGuidList +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ItemSaveRespDTO)) {
            return false;
        }
        ItemSaveRespDTO that = (ItemSaveRespDTO) o;
        return Objects.equals(getItemGuid(), that.getItemGuid()) && Objects.equals(getInsertSkuGuidList(), that.getInsertSkuGuidList());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getItemGuid(), getInsertSkuGuidList());
    }
}
