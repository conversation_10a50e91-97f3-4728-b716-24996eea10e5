package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className JHConfigDTO
 * @date 2018/09/13 19:58
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class JHConfigDTO implements Serializable {

    @NotBlank
    @ApiModelProperty(value = "支付平台商户号")
    private String appId;

    @NotBlank
    @ApiModelProperty(value = "支付平台商户key")
    private String appSecretKey;

    @NotBlank
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

}
