package com.holderzone.saas.store.dto.kds.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * 菜品分组 列表返回DTO
 */
@Data
public class ItemGroupRespDTO implements Serializable {

    private static final long serialVersionUID = -8993349661482406553L;

    /**
     * 分组guid
     */
    private String guid;

    /**
     * 是否当前设备已绑定
     */
    private Boolean currentBindFlag;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 商品数量
     */
    private Long itemCount;

}
