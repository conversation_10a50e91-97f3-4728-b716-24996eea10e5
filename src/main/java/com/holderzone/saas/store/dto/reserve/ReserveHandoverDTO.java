package com.holderzone.saas.store.dto.reserve;

import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "ReserveHandoverDTO", description = "交接班预订金统计")
@Accessors(chain = true)
public class ReserveHandoverDTO {

    @ApiModelProperty(value = "预订单数")
    private Integer reserveCount;

    @ApiModelProperty(value = "预订金总额")
    private BigDecimal reserveAmount;

    @ApiModelProperty(value = "预订金中现金支付总额")
    private BigDecimal reserveCashAmount;

    @ApiModelProperty(value = "预订金支付方式明细")
    private Map<String, BigDecimal> reservePayDetailMap;

    @ApiModelProperty(value = "预订金支付方式明细（带排序）")
    private List<AmountItemDTO> reservePayDetailList;

}
