package com.holderzone.saas.store.dto.trade;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <p>
 * 商户后台/数据报表/订单统计 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BusinessOrderStatisticsQueryDTO extends BasePageDTO {

    @ApiModelProperty(value = "营业时间开始时间,格式:yyyy-MM-dd,必传", required = true)
    @NotEmpty(message = "营业时间开始时间不可为空")
    private String startTime;

    @ApiModelProperty(value = "营业时间结束时间,格式:yyyy-MM-dd,必传", required = true)
    @NotEmpty(message = "营业时间结束时间不可为空")
    private String endTime;

    @ApiModelProperty(value = "就餐类型:0：正餐,1：快餐,查询全部制空字段")
    private Integer state;

    @ApiModelProperty(value = "顾客类型:0：会员,1：非会员,查询全部制空字段")
    private Integer customerType;

    /**
     * order表state 1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废  7：反结账
     * 1:未结账[ 1, 2, 3 ]
     * 2:已结账[ 4 ]
     * 3:反结账[ 7 ]
     * 4:退款[ 5 ]
     * 5:已作废[ 6 ]
     */
    @ApiModelProperty(value = "订单状态:1：未结账,2：已结账,3：反结账,4:退款,5:已作废,查询全部制空字段")
    private Integer orderState;

    @ApiModelProperty(value = "订单号:考虑页面响应效率,仅支持全匹配")
    private String orderNo;

    @ApiModelProperty(value = "快速搜索值")
    private String searchKey;

    /**
     * device_type [ 12, 15 ] 对应 12
     */
    @ApiModelProperty(value = "店内订单来源:1：安卓一体机,2：POS,3：微信扫码点餐,4：PAD,5：自助点餐机,6：翼慧天下,7：点菜宝(M1),8：PV1(带刷卡的点菜宝)")
    private Integer orderSource;

    @ApiModelProperty(value = "门店集合，品牌查询需传品牌下门店集合")
    private List<String> storeGuidList;

    @ApiModelProperty(value = "结账操作人guid")
    private List<String> checkoutStaffGuids;

    private List<String> diningTableGuids;

    /**
     * 开具发票 1是 0否
     */
    @ApiModelProperty(value = "开具发票")
    private Integer isInvoice;

    @ApiModelProperty(value = "支付方式列表")
    private List<String> paymentTypeList;

    @ApiModelProperty(value = "支付方式是否包含金额")
    private Boolean paymentTypeContainsFee;

    @ApiModelProperty(value = "团购支付方式列表")
    private List<Integer> grouponTypeList;

    @ApiModelProperty(hidden = true)
    public int getIndex() {
        return ((getCurrentPage() == null || getCurrentPage() <= 0 ? 1 : getCurrentPage()) - 1) * (getPageSize() == null ? 20 : getPageSize());
    }

    /**
     * 如果有对StateEnum定义的code修改，请同步修改这里
     * 根据前端下拉选择 对应订单状态。 未结账状态 包含 1：待支付 2：支付中 3：支付失败
     */
    @ApiModelProperty(hidden = true)
    public String getOrderQueryInState() {
        //===================== 支付状态的枚举在trade =====================
        String inSql = "";
        if (this.orderState != null) {
            switch (this.orderState) {
                case 1:
                    inSql = "1, 2, 3, 12";
                    break;
                case 2:
                    inSql = "4";
                    break;
                case 3:
                    inSql = "7";
                    break;
                case 4:
                    inSql = "5";
                    break;
                case 5:
                    inSql = "6";
                    break;
                default:
                    break;
            }
        }
        return inSql;
    }

    /**
     * 根据前端订单来源下拉 对应订单来源设备类型
     */
    @ApiModelProperty(hidden = true)
    public String getDeviceTypeInSql() {
        StringBuilder inSql = new StringBuilder();
        if (this.orderSource != null) {
            switch (this.orderSource) {
                case 1:
                    inSql.append(BaseDeviceTypeEnum.All_IN_ONE.getCode());
                    break;
                case 2:
                    inSql.append(BaseDeviceTypeEnum.POS.getCode());
                    break;
                case 3:
                    inSql.append(BaseDeviceTypeEnum.WECHAT.getCode())
                            .append(",").append(BaseDeviceTypeEnum.TCD.getCode())
                            .append(",").append(BaseDeviceTypeEnum.WECHAT_MINI.getCode())
                            .append(",").append(BaseDeviceTypeEnum.ALI.getCode());
                    break;
                case 4:
                    inSql.append(BaseDeviceTypeEnum.CLOUD_PANEL.getCode());
                    break;
                case 5:
                    inSql.append(BaseDeviceTypeEnum.SELF.getCode());
                    break;
                case 6:
                    inSql.append(BaseDeviceTypeEnum.YHTX.getCode());
                    break;
                case 7:
                    inSql.append(BaseDeviceTypeEnum.M1.getCode());
                    break;
                case 8:
                    inSql.append(BaseDeviceTypeEnum.PV1.getCode());
                    break;
                default:
                    break;
            }
        }
        return inSql.toString();
    }

}
