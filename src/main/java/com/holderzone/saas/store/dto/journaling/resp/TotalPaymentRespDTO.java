package com.holderzone.saas.store.dto.journaling.resp;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TotalPaymentRespDTO
 * @date 2019/06/03 10:28
 * @description 支付统计响应DTO
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@ApiModel(value = "支付统计响应DTO")
public class TotalPaymentRespDTO {

    public static TotalPaymentRespDTO DEFAULT = new TotalPaymentRespDTO(
            BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP),
            BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP),
            BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP),
            Lists.newArrayList());
    @ApiModelProperty(value = "支付合计")
    private BigDecimal totalFee;

    @ApiModelProperty(value = "退款合计")
    private BigDecimal totalRefundFee;

    @ApiModelProperty(value = "实付金额合计")
    private BigDecimal totalActuallyFee;

    private List<PaymentReportRespDTO> paymentReportList;
}
