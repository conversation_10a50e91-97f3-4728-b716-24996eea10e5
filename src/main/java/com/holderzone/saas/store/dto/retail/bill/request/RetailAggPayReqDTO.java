package com.holderzone.saas.store.dto.retail.bill.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.retail.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillCalculateReqDTO
 * @date 2019/01/28 17:32
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class RetailAggPayReqDTO extends BaseDTO {

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "支付金额", required = true)
    private BigDecimal amount;

    @ApiModelProperty(value = "是否最后一次支付", required = true)
    private Boolean last;

    @ApiModelProperty(value = "发起条码支付的授权码，当微信、支付宝条码支付时候，必填！！", required = true)
    private String authCode;

    @ApiModelProperty(value = "订单金额（商品总额+附加费）")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "使用的积分")
    private Integer useIntegral;

    @ApiModelProperty(value = "积分抵扣了多少钱")
    private BigDecimal integralDiscountMoney;

    @ApiModelProperty(value = "当前的优惠信息")
    private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

    @ApiModelProperty(value = "version")
    @LockField
    private Integer version;


}
