package com.holderzone.saas.store.dto.reserve;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "ReserveReportTotalDataDTO", description = "预订菜品统计合计")
@Accessors(chain = true)
public class ReserveReportTotalDataDTO {

    @ApiModelProperty(value = "合计数量")
    private Double totalNum;

    @ApiModelProperty(value = "合计小计")
    private BigDecimal totalItemPrice;

    @ApiModelProperty(value = "分类明细")
    private List<ReserveReportDataDTO> reportDataList;
}
