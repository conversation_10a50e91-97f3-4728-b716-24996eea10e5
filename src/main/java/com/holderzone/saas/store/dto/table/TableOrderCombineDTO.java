package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableOrderCombineDTO
 * @date 2019/01/25 9:55
 * @description
 * @program holder-saas-store-dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TableOrderCombineDTO extends BaseDTO {

    private static final long serialVersionUID = -1929531857417756241L;

    @ApiModelProperty("增量并/拆单（为空时拆主单）")
    private List<TableInfoDTO> tableInfoDTOS;

    @OrderLockField
    private String mainOrderGuid;

    @LockField
    private String mainTableGuid;

    @ApiModelProperty("是否忽略校验同一桌台")
    private Boolean ignoreVerifySameOrderFlag;

}
