package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("微信排队取号请求入参")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxDineCurrentDetailsDTO {

	@ApiModelProperty(value = "订单id")
	private String orderGuid;

	@ApiModelProperty(value = "订单状态")
	private Integer orderState;

	@ApiModelProperty(value = "接单批次guid")
	private String merchantGuid;
}
