package com.holderzone.saas.store.dto.takeaway;

public interface OrderTagStatus {

    /**
     * 初始状态
     */
    int NORMAL = 0;

    /**
     * 用户申请“取消订单”
     */
    int CANCEL_REQ = 10;

    /**
     * 处理“同意取消订单”中
     */
    int TX_AGREE_CANCEL = 11;

    /**
     * 处理“不同意取消订单”中
     */
    int TX_DISAGREE_CANCEL = 12;

    /**
     * 用户申请“退单”
     */
    int REFUND_REQ = 20;

    /**
     * 处理“同意退单”中
     */
    int TX_AGREE_REFUND = 21;

    /**
     * 处理“不同意退单”中
     */
    int TX_DISAGREE_REFUND = 22;

    /**
     * 订单操作-订单出餐
     */
    int ORDER_OPERATE_PREPARED = 100;
}
