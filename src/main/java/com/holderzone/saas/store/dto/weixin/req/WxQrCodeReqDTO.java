package com.holderzone.saas.store.dto.weixin.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.saas.store.dto.weixin.WxActionInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQrCodeReqDTO
 * @date 2019/03/20 10:25
 * @description 调用微信获取带参数二维码api请求参数
 * @program holder-saas-store-weixin
 */

@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("调用微信获取带参数二维码api请求参数")
public class WxQrCodeReqDTO {

    @ApiModelProperty(value = "该二维码有效时间", notes = "以秒为单位。 最大不超过2592000（即30天），此字段如果不填，则默认有效期为30秒。")
    @JsonProperty(value = "expire_seconds")
    private Integer expireSeconds;

    @ApiModelProperty(value = "二维码类型", notes = "QR_SCENE为临时的整型参数值，QR_STR_SCENE为临时的字符串参数值，QR_LIMIT_SCENE为永久的整型参数值，QR_LIMIT_STR_SCENE为永久的字符串参数值")
    @JsonProperty(value = "action_name")
    private String actionName;

    @ApiModelProperty(value = "二维码详细信息")
    @JsonProperty(value = "action_info")
    private WxActionInfoDTO wxActionInfoDTO;
}
