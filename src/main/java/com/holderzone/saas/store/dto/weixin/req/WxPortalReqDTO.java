package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxPortalReqDTO
 * @date 2019/04/03 15:23
 * @description 微信所有通过msgKey获取信息的请求DTO
 * @program holder-saas-store
 */
@ApiModel("微信用户信息获取请求DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxPortalReqDTO {

    @NotEmpty(message = "enterpriseGuid 不能为空")
    @ApiModelProperty("企业guid")
    private String enterpriseGuid;

    @ApiModelProperty("appId")
    private String appId;

    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "msgKey", notes = "主要用于微信回调时接收参数")
    private String msgKey;

    @ApiModelProperty(value = "调用jsSdk时所在url", notes = "仅在调用获取jsSdk时需要,需传全路径（包括http://）")
    private String url;

    @ApiModelProperty("当前请求类型，0：登陆，1：绑定")
    private Integer reqType;

    @ApiModelProperty("会员guid")
    private String memberInfoGuid;

    @ApiModelProperty("运营主体guid")
    private String operSubjectGuid;

    @ApiModelProperty("外部openId")
    private String thirdOpenId;

    @ApiModelProperty("外部appId")
    private String thirdAppId;

}
