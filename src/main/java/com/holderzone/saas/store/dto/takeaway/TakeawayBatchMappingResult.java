package com.holderzone.saas.store.dto.takeaway;

import com.holderzone.framework.util.Page;
import lombok.Data;

import java.io.Serializable;

/**
 * 外卖批量绑定映射 返回
 */
@Data
public class TakeawayBatchMappingResult implements Serializable {

    private static final long serialVersionUID = 3317394050719834772L;

    /**
     * 未绑定条数
     */
    private Long unBindCount;

    /**
     * 已关联 但门店不存在的商品条数
     */
    private Long lossBindCount;

    /**
     * 商品列表 - 分页
     */
    private Page<UnMappedItem> pageInfo;

    public static TakeawayBatchMappingResult buildEmptyResult(Integer currentPage, Integer pageSize) {
        TakeawayBatchMappingResult result = new TakeawayBatchMappingResult();
        result.setUnBindCount(0L);
        result.setLossBindCount(0L);
        result.setPageInfo(new Page<>(currentPage, pageSize));
        return result;
    }

    public static TakeawayBatchMappingResult buildSuccessResult(Long unBindCount, Long lossBindCount, Page<UnMappedItem> pageInfo) {
        TakeawayBatchMappingResult result = new TakeawayBatchMappingResult();
        result.setUnBindCount(unBindCount);
        result.setLossBindCount(lossBindCount);
        result.setPageInfo(pageInfo);
        return result;
    }

}
