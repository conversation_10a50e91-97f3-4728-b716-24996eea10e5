package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PadShoppingCarRespDTO
 * @date 2021/7/29
 * @description pad点餐购物车返回dto
 * @program holder-saas-store-dto
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "pad点餐购物车返回dto")
public class PadShoppingCarRespDTO {

    /**
     * 订单的guid
     */
    @ApiModelProperty("订单的guid")
    private String orderGuid;

    /**
     * 已选种类数量
     */
    @ApiModelProperty(value = "已选种类数量")
    private Integer kindCount;

    /**
     * 总共商品数量
     */
    @ApiModelProperty(value = "总共商品数量")
    private Integer itemCount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 总金额
     */
    @ApiModelProperty(value = "总金额")
    private BigInteger totalMoney;

    /**
     * 商品集合
     */
    @ApiModelProperty(value = "可能为空集合，商品集合")
    private List<PadItemRespDTO> itemList;

}
