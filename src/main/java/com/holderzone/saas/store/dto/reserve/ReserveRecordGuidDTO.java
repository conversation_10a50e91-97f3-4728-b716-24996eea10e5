package com.holderzone.saas.store.dto.reserve;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordGuidDTO
 * @date 2019/04/27 10:06
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@ApiModel
@NoArgsConstructor
public class ReserveRecordGuidDTO extends BaseDTO {

    private static final long serialVersionUID = -3808727786444744062L;

    @ApiModelProperty("预定业务主键")
    private String guid;

    @ApiModelProperty("撤销预定原因")
    private String reason;

    @ApiModelProperty("支付guid")
    private String payGuid;

    @ApiModelProperty("桌台信息")
    private Collection<TableDTO> tables;

    @ApiModelProperty("菜品信息")
    private Collection<DineInItemDTO> items;

    /**
     * 订单类型 0预订 1预付金
     */
    @ApiModelProperty("订单类型 0预订 1预付金")
    private Integer orderType;

    @ApiModelProperty("订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "主单guid")
    private String mainOrderGuid;

    /**
     * 退款金额
     */
    @ApiModelProperty("退款金额")
    private BigDecimal reserveRefundAmount;

    @ApiModelProperty("预定金额")
    private BigDecimal reserveAmount;

    public ReserveRecordGuidDTO(String guid){
        this.guid = guid;
    }

    public ReserveRecordGuidDTO(String guid,String reason){
        this.guid = guid;
        this.reason = reason;
    }

}