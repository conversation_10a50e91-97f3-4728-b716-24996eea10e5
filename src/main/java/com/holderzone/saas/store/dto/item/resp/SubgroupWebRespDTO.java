package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SubgroupSynRespDTO
 * @date 2019/01/03 下午3:30
 * @description //网页端套餐详情的分组返回实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class SubgroupWebRespDTO implements Serializable {
    @ApiModelProperty(value = "商品GUID")
    private String itemGuid;
    @ApiModelProperty(value = "套餐分组的唯一标识",required = true)
    private String subgroupGuid;
    @ApiModelProperty(value = "分组NAME",required = true)
    private String name;

    @ApiModelProperty(value = "是否是固定分组：0：否，1：是")
    @NotNull
    private Integer isFixSubgroup;

    @ApiModelProperty(value = "当前分组中的商品的可选择数量（商品可重复被选）：0：顾客不可选择，其余值：最大可选商品次数",required = true)
    private Integer pickNum;

    @ApiModelProperty(value = "排序")
    private Integer sort;
    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;
    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;
    @ApiModelProperty(value = "subgroup来源（0：门店自己创建的subgroup，1：品牌自己创建的subgroup,2:被推送过来的subgroup）")
    private Integer subgroupFrom;
    @ApiModelProperty(value = "父分组GUID")
    private String parentGuid;

    @ApiModelProperty(value = "套餐分组中包含的可选规格集合",required = true)
    private List<SubItemSkuWebRespDTO> subItemSkuList;
}
