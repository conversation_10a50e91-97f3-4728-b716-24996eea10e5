package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/5
 * @description 菜品显示顺序配置
 */
@Data
@ApiModel(description = "菜品显示顺序配置")
public class DisplayRuleItemSortQueryDTO implements Serializable {

    private static final long serialVersionUID = 7939869832914724903L;

    /**
     * 品牌guid
     */
    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

}
