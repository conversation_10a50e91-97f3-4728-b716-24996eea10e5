package com.holderzone.saas.store.dto.business.queue;

import com.holderzone.saas.store.dto.trade.req.record.MemberOperateRecordReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/10/19
 * @description
 */
@Data
public class MemberOperateRecordPushMqDTO implements Serializable {

    private static final long serialVersionUID = -8982560422301923372L;

    @ApiModelProperty(value = "会员操作记录请求体")
    private MemberOperateRecordReqDTO operateRecordReqDTO;

    @ApiModelProperty(value = "操作员guid")
    private String userContextJson;
}
