package com.holderzone.saas.store.dto.weixin.deal;

import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ItemCouponInfoDTO implements Serializable {

    private static final long serialVersionUID = -2339728731940007174L;

    @ApiModelProperty(value = "团购券信息")
    private MtCouponPreRespDTO couponPreRespDTO;

    @ApiModelProperty("验券返回信息")
    private GroupVerifyDTO groupVerify;

}
