package com.holderzone.saas.store.dto.item.resp;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateRecoveryRespDTO
 * @date 2019/05/08 15:20
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class EstimateRecoveryRespDTO {

    /**
     * guid
     */
    private String guid;

    /**
     * 商户guid
     */
    private String enterpriseGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 配置类型
     */
    private Integer dicCode;

    /**
     * 配置类型名称
     */
    private String dicName;

    /**
     * 配置项value ，多个值可自定义或存json字符串
     */
    private String dictValue;

    /**
     * 是否启用  0：启用  1：不启用
     */
    private Integer isEnable;

}
