package com.holderzone.saas.store.dto.kds.req;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ItemPrepareReqDTO extends BaseDTO {

    private static final long serialVersionUID = -4247230675662759853L;

    @NotBlank(message = "订单Guid不得为空")
    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @NotNull(message = "交易模式不得为空")
    @Min(value = 0, message = "交易模式：0=正餐，1=快餐，2=外卖")
    @Max(value = 2, message = "交易模式：0=正餐，1=快餐，2=外卖")
    @ApiModelProperty(value = "交易模式：0=正餐，1=快餐，2=外卖")
    private Integer tradeMode;

    @Nullable
    @ApiModelProperty(value = "整单备注")
    private String orderRemark;

    @Nullable
    @ApiModelProperty(value = "正餐独有信息")
    private TradeDineInInfoDTO tradeDineInInfoDTO;

    @Nullable
    @ApiModelProperty(value = "快餐独有信息")
    private TradeSnackInfoDTO tradeSnackInfoDTO;

    @Nullable
    @ApiModelProperty(value = "外卖独有信息")
    private TradeTakeoutInfoDTO tradeTakeoutInfoDTO;

    @Valid
    @NotEmpty(message = "商品列表不得为空")
    @ApiModelProperty(value = "商品列表")
    private List<KdsItemDTO> items;

    /**
     * 商品信息
     */
    private List<SkuInfoRespDTO> skus;
}
