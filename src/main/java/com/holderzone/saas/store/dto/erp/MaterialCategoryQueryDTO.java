package com.holderzone.saas.store.dto.erp;

import com.holderzone.saas.store.dto.common.BasePageDTO;

/**物料分类分页查询DTO
 * <AUTHOR>
 * @date 2019/04/26 11:41
 */
public class MaterialCategoryQueryDTO extends BasePageDTO {
    private String warehouseGuid;
    private String searchConditions;
    private String storeGuid;
    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }

    public String getSearchConditions() {
        return searchConditions;
    }

    public void setSearchConditions(String searchConditions) {
        this.searchConditions = searchConditions;
    }

    @Override
    public String getStoreGuid() {
        return storeGuid;
    }

    @Override
    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }
}
