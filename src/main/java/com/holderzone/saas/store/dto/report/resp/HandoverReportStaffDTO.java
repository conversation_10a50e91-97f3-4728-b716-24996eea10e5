package com.holderzone.saas.store.dto.report.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class HandoverReportStaffDTO implements Serializable {

    @ApiModelProperty(value = "员工Guid")
    private String userGuid;

    @ApiModelProperty(value = "员工名")
    private String userName;

    @ApiModelProperty(value = "统计")
    private String statistical;
}
