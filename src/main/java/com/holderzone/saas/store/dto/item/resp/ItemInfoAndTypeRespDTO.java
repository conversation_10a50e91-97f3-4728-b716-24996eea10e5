package com.holderzone.saas.store.dto.item.resp;

import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.weixin.deal.MenuInfoAllDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 商品详情及分类信息返回实体
 * @date 2021/4/14 14:24
 */
@Data
@ApiModel(value = "商品详情及分类信息返回实体")
public class ItemInfoAndTypeRespDTO {

    @ApiModelProperty(value = "分类信息返回集合")
    private List<TypeWebRespDTO> typeList;

    @ApiModelProperty(value = "商品详情返回集合")
    private List<ItemInfoRespDTO> itemInfoList;

    @ApiModelProperty(value = "门店商品、分类、配置实体")
    private MenuInfoAllDTO menuInfoAllDTO;

    private List<PrintItemRecord> printItemRecords;

    private List<ItemInfoRespDTO> itemInfoRespDTOS;

    private List<String> itemList;

}
