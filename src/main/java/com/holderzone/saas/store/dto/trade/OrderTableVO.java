package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 订单
 * </p>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class OrderTableVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 0 单台 1 联台 2 并台
     */
    private Integer orderTableType;

    /**
     * 桌台信息
     */
    private List<OrderTableGuestDTO> orderTableGuestDTOS;

    private String orderGuid;
}