package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutShopBindRespDTO
 * @date 2018/09/25 9:07
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@ApiModel
@NoArgsConstructor
public class TakeoutOwnRespDTO implements Serializable {

    private static final long serialVersionUID = 6784968432096220916L;

    @ApiModelProperty(value = "结果代码")
    private String code;

    @ApiModelProperty(value = "详情信息")
    private String message;

    @ApiModelProperty(value = "data")
    private TakeoutOwnBindRespDTO data;

}
