package com.holderzone.saas.store.dto.takeaway.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreAuthByTypeReqDTO
 * @date 2018/09/25 9:08
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@NoArgsConstructor
public class StoreAuthByStoreReqDTO implements Serializable {

    private static final long serialVersionUID = 3848969351137744321L;

    @NotBlank(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    /**
     * 品牌guid
     */
    private String brandGuid;

    /**
     * 外部门店id
     */
    private String poiId;

    /**
     * saas门店开通的业务类型
     */
    private Byte businessId;
}
