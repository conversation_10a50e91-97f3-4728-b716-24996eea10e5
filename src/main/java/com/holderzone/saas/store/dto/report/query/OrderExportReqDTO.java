package com.holderzone.saas.store.dto.report.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/4/28 14:37
 * @description
 */
@ApiModel("订单数据导出请DTO")
@Data
public class OrderExportReqDTO {
    @ApiModelProperty("导出类型")
    private Integer exportType;
    @ApiModelProperty("导出json")
    private String paramString;
//    @ApiModelProperty("导出订单id")
//    private List<String> orderIds;
}
