package com.holderzone.saas.store.dto.report.resp;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 退款明细
 */
@Data
public class RefundDetailDTO implements Serializable {

    private static final long serialVersionUID = -4374335766385207822L;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 结账时间
     */
    private String checkOutTime;

    /***
     * 退款时间
     */
    private String refundTime;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * @see com.holderzone.saas.store.enums.order.TradeModeEnum
     * 就餐类型
     */
    private Integer tradeMode;

    /**
     * 退款类型 退款 or 反结账
     */
    private Integer type;

    /**
     * 区域 + 桌台号
     */
    private String diningTableName;

    /**
     * 订单实收金额
     */
    private BigDecimal actuallyPayFee;

    /**
     * 实退金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 退款操作员
     */
    private String createStaffName;

    /**
     * 退款授权员
     */
    private String authStaffName;

    /**
     * 退款方式
     */
    private String refundDetails;

    /**
     * 会员号
     */
    private String memberPhone;
}
