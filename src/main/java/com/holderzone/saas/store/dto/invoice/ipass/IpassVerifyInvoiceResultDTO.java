package com.holderzone.saas.store.dto.invoice.ipass;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@ApiModel(description = "开票校验结果")
@Data
public class IpassVerifyInvoiceResultDTO implements Serializable {


    @ApiModelProperty(value = "0：未登录，1：已登录")
    private Integer isTaxLogin;

    @ApiModelProperty(value = "0：未认证，1：已认证")
    private Integer isTaxAuth;

    @ApiModelProperty(value = "企业在税务局，剩余授信额度")
    private String sysxed;
}
