package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PollingJHPayDTO
 * @date 2018/08/15 10:45
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class PollingJHPayDTO {

    @ApiModelProperty(value = "商户订单GUID")
    private String orderGUID;

    @ApiModelProperty(value = "商户透传数据")
    private String attachData;

    @ApiModelProperty(value = "平台退款单号，如果有这个参数，只会返回一条记录")
    private String refundNo;

    @ApiModelProperty(value = "支付唯一标示")
    private String payGUID;

    @ApiModelProperty(value = "商户唯一标识，appId")
    private String appId;

    @ApiModelProperty(value = "发起请求的时间")
    private Long timestamp;

    @ApiModelProperty(value = "开发者ID")
    private String developerId;

    @ApiModelProperty(value = "签名")
    private String signature;


    public PollingJHPayDTO() {
    }

    public PollingJHPayDTO(Builder builder) {
        this.appId = builder.appId;
        this.attachData = builder.attachData;
        this.developerId = builder.developerId;
        this.orderGUID = builder.orderGUID;
        this.payGUID = builder.payGUID;
        this.refundNo = builder.refundNo;
        this.signature = builder.signature;
        this.timestamp = builder.timestamp;
    }

    @Data
    public static class Builder {

        private String orderGUID;

        private String attachData;

        private String refundNo;

        private String payGUID;

        private String appId;

        private Long timestamp;

        private String developerId;

        private String signature;

        public static Builder getBuilder() {
            return new Builder();
        }

        public Builder orderGuid(String orderGUID) {
            this.setOrderGUID(orderGUID);
            return this;
        }

        public Builder attachData(String attachData) {
            this.setAttachData(attachData);
            return this;
        }

        public Builder refundNo(String refundNo) {
            this.refundNo = refundNo;
            return this;
        }

        public Builder payGUID(String payGUID) {
            this.payGUID = payGUID;
            return this;
        }

        public Builder appId(String appId) {
            this.appId = appId;
            return this;
        }

        public Builder timestamp(Long timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        public Builder developerId(String developerId) {
            this.developerId = developerId;
            return this;
        }

        public Builder signature(String signature) {
            this.signature = signature;
            return this;
        }


        public PollingJHPayDTO build() {
            return new PollingJHPayDTO(this);
        }
    }

}
