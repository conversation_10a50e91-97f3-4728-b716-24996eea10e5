package com.holderzone.saas.store.dto.print.content.nested;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintItemRecord
 * @date 2018/07/25 11:13
 * @description 商品相关
 * @program holder-saas-store-print
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(description = "菜品记录")
public class PrintItemRecord implements Serializable {

    private static final long serialVersionUID = -2851990564619429954L;

    /**
     * 菜品SKU
     */
    private String unItemSku;

    /**
     * 无用，待整理
     */
    @ApiModelProperty(value = "日流水")
    private String daySn;

    @ApiModelProperty(value = "订单菜品Guid")
    private String orderItemGuid;

    @ApiModelProperty(value = "反结账原菜品guid")
    private Long originalOrderItemGuid;

    @NotBlank(message = "itemGuid不能为空")
    @ApiModelProperty(value = "菜品Guid", required = true)
    private String itemGuid;

    @ApiModelProperty(value = "skuGuid")
    private String skuGuid;

    @NotBlank(message = "菜品名字不能为空")
    @ApiModelProperty(value = "菜品名字", required = true)
    private String itemName;

    @NotBlank(message = "菜品分类Guid不能为空")
    @ApiModelProperty(value = "菜品分类guid", required = true)
    private String itemTypeGuid;

    @Nullable
    @ApiModelProperty(value = "菜品分类名字，菜品清单、预结单、结账单必传")
    private String itemTypeName;

    /**
     * 无用，待整理
     */
    @ApiModelProperty(value = "当前分类总计")
    private BigDecimal itemTypeTotal;

    /**
     * 前台单：不为空
     * 后厨单：未使用
     * 标签单：不为空
     */
    @Nullable
    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @Nullable
    @ApiModelProperty(value = "套餐子项预设数量")
    private BigDecimal pkgCnt;

    /**
     * 普通商品、规格商品、称重商品：商品数量
     * <p>
     * 套餐商品：商品数量
     * 套餐商品之成分-普通商品：成分选择数量
     * 套餐商品之成分-规格商品：成分选择数量
     * 套餐商品之成分-称重商品：成分选择数量
     */
    @NotNull(message = "菜品数量不得为空")
    @ApiModelProperty(value = "数量。如果是套餐商品的成分，该数量为成分选择数量")
    private BigDecimal number;

    @ApiModelProperty(value = "映射后平台商品数量")
    private BigDecimal actualNumber;

    /**
     * 只有点菜单、退菜单要用该字段，且不为空
     */
    @Nullable
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 只有结帐单要用该字段，且不为空
     */
    @Deprecated
    @ApiModelProperty(value = "小计")
    private BigDecimal subtotal;

    /**
     * 1.折扣价
     * 2.会员价
     */
    @ApiModelProperty(value = "单品购买优惠")
    private Integer actualType;

    @ApiModelProperty(value = "会员价/折扣价")
    private BigDecimal actualPrice;

    @ApiModelProperty(value = "优惠后价")
    private BigDecimal itemDiscountAfterPrice;

    @NotNull(message = "是否称重不得为空")
    @ApiModelProperty(value = "是否称重")
    private Boolean asWeight;

    @NotNull(message = "是否套餐不得为空")
    @ApiModelProperty(value = "是否套餐")
    private Boolean asPackage;

    /**
     * 前台单：不得为空
     * 后厨单：可以为空
     * 标签单：未使用
     */
    @Nullable
    @ApiModelProperty(value = "是否赠送")
    private Boolean asGift;

    @Valid
    @ApiModelProperty(value = "或者套餐成分列表")
    private List<PrintItemRecord> subItemRecords;

    @Nullable
    @ApiModelProperty(value = "备注")
    private String remark;

    @Nullable
    @ApiModelProperty(value = "属性(以逗号隔开)")
    private String property;

    /**
     * 目前只有结帐单用了该字段，且该字段可以为空
     * <p>
     * 普通商品、规格商品、称重商品、套餐商品的套餐成分 才可能有属性加价，套餐商品本身无属性加价
     * <p>
     * 普通商品、规格商品、称重商品：属性加价为“该商品属性加价”
     * 套餐商品的套餐成分：属性加价为“成分配置数量 * sum(该套餐成分属性加价)”
     */
    @Nullable
    @ApiModelProperty(value = "属性加价。如果是套餐商品的套餐成分，那么属性加价=成分配置数量*sum(该套餐成分属性加价)")
    private BigDecimal propertyPrice;

    /**
     * 只有套餐商品的套餐成分才可能有成分加价
     * <p>
     * 套餐商品的套餐成分：成分加价为“该套餐成分成分加价”
     */
    @Nullable
    @ApiModelProperty(value = "成分加价")
    private BigDecimal ingredientPrice;

    /**
     * 打印服务内部，套餐菜品使用
     */
    private BigDecimal itemTotal;

    @ApiModelProperty(value = "商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂，8.已上菜(已划菜) ，9.预定)")
    private Integer itemState;

    /**
     * 所属口袋 0=1号口袋  1=2号口袋 依次类推
     */
    private Integer cartId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否改变了分类
     */
    private Boolean isChange = Boolean.FALSE;

    /**
     * 非套餐商品： 是否有属性
     * 套餐商品： 是否使用套餐上的属性价和加价
     */
    private Integer hasAttr;

    @ApiModelProperty(value = "属性总价")
    private BigDecimal singleItemAttrTotal;

    @ApiModelProperty(value = "加价总价")
    private BigDecimal singleAddPriceTotal;

    /**
     * 单据类型
     */
    private Integer invoiceType;

    /**
     * 是否为需要去重的套餐数据
     */
    private Boolean isDuplicatePackage;

    /**
     * 小计价格是否显示0
     */
    private Boolean isPriceShowZero;
}
