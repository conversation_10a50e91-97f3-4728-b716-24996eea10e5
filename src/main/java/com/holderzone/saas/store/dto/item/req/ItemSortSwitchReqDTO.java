package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemSortReqDTO
 * @date 2020年7月25日
 * @description //交换分类排序
 * @program holder
 */

@Data
@ApiModel(value = "商品排序交换")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemSortSwitchReqDTO {

    @NotBlank(message = "源商品Guid不能为空")
    @ApiModelProperty(value = "源分类Guid")
    private String sourceItemGuid;

    @NotBlank(message = "目标商品Guid不能为空")
    @ApiModelProperty(value = "目标分类Guid")
    private String targetItemGuid;


}
