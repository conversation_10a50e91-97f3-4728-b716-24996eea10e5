package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 外卖商品异常数据返回实体
 * @date 2022/5/7 15:23
 * @className: TakeoutItemAbnormalDataRespDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "外卖商品异常数据返回实体")
public class TakeoutItemAbnormalDataRespDTO implements Serializable {

    private static final long serialVersionUID = 906820354119819268L;

    /**
     * 门店GUID
     */
    @ApiModelProperty("门店GUID")
    private String storeGuid;

    /**
     * 门店名
     */
    @ApiModelProperty("门店名")
    private String storeName;

    /**
     * 订单来源：0=美团，1=饿了么，6=赚餐自营外卖
     *
     * @see com.holderzone.saas.store.dto.takeaway.OrderType.TakeoutSubType
     */
    @ApiModelProperty("订单来源")
    private Integer orderType;

    /**
     * 商品名称：外卖名称+规格名称
     */
    @ApiModelProperty("商品名称：外卖名称+规格名称")
    private String itemName;

    /**
     * 外卖商品编号
     */
    @ApiModelProperty("外卖商品编号")
    private String takeoutItemNumber;

    /**
     * 异常订单数
     */
    @ApiModelProperty("异常订单数")
    private Integer abnormalOrderNum;

    /**
     * 销售金额
     */
    @ApiModelProperty("销售金额")
    private BigDecimal totalPrice;

}
