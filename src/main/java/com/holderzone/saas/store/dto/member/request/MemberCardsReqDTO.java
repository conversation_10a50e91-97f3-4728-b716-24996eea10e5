package com.holderzone.saas.store.dto.member.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberCardsReqDTO
 * @date 2018/09/28 13:54
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
@ApiModel
public class MemberCardsReqDTO {

    @ApiModelProperty(value = "会员guid,必传",required = true)
    @NotNull(message = "会员guid不能为空")
    private String memberGuid;
    @ApiModelProperty(value = "会员卡号")
    private  String num;
}
