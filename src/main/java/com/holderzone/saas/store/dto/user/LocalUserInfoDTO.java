package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LocalUserInfoDTO
 * @date 2019/10/09 18:54
 * @description 本地化方案用户信息DTO（包含模板权限及资源权限）
 * @program holder-saas-store
 */
@Data
@ApiModel("本地化方案用户信息DTO（包含模板权限及资源权限）")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LocalUserInfoDTO {

    @ApiModelProperty(value = "userGuid")
    private String userGuid;

    @ApiModelProperty(value = "企业编号")
    private String enterpriseNo;

    @ApiModelProperty(value = "用户账号")
    private String account;

    @ApiModelProperty(value = "用户密码，加密后")
    private String password;

    @ApiModelProperty(value = "用户名称")
    private String name;

    @ApiModelProperty(value = "用户电话")
    private String phone;

    @ApiModelProperty(value = "整单折扣阈值")
    private BigDecimal discountThreshold;

    @ApiModelProperty(value = "整单让价阈值")
    private BigDecimal allowanceThreshold;

    @ApiModelProperty(value = "单品折扣阈值")
    private BigDecimal productDiscountThreshold;

    @ApiModelProperty(value = "退款金额差值阈值")
    private BigDecimal refundThreshold;

    @ApiModelProperty(value = "操作权限资源集合")
    private List<MenuSourceDTO> menuSourceDTOList;

    @ApiModelProperty(value = "模块权限资源集合")
    private List<MenuSourceDTO> moduleSourceDTOList;
}
