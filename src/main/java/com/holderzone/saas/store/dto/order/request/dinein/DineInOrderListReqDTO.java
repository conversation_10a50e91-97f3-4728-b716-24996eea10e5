package com.holderzone.saas.store.dto.order.request.dinein;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderListReqDTO
 * @date 2018/09/14 16:01
 * @description 订单列表入参
 * @program holder-saas-store-order
 */
@Data
public class DineInOrderListReqDTO extends BasePageDTO {

    private static final long serialVersionUID = -4199190124950049171L;

    @ApiModelProperty(value = "状态(0：未结账， 1：已结账， 2：已作废/退单")
    @NotNull
    private Integer state;

    @ApiModelProperty(value = "状态(0：未结账， 1：已结账， 2：已作废/退单")
    private List<Integer> states;

    @ApiModelProperty(value = "是否小程序订单")
    private Boolean miniAppFlag;

    @ApiModelProperty(value = "订单反结账类型1：普通单 2：原单 3：新单 4：退单")
    private List<Integer> recoveryTypes;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "桌号，牌号，订单号，手机号")
    private String searchKey;

    @ApiModelProperty(value = "交易模式(0：正餐，1：快餐)")
    private Integer tradeMode;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "会员guid")
    private String memberInfoGuid;

    @ApiModelProperty(value = "会员openId")
    private String openId;

    @ApiModelProperty("0默认,1下单时间正序,2下单时间倒序,3订单金额正序,4订单金额倒序,5结账时间正序,6结账时间倒序,7实收金额正序,8实收金额倒序")
    private Integer sortType = 0;

    @ApiModelProperty(value = "支付方式查询")
    private List<String> paymentTypeList;

}
