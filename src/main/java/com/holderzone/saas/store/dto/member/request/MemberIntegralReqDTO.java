package com.holderzone.saas.store.dto.member.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberIntegralReqDTO
 * @date 2018/09/29 17:02
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class MemberIntegralReqDTO extends BasePageDTO{
    @ApiModelProperty(value = "日期类型0营业日 1自然日",required = true)
    private Integer dateType;
    @ApiModelProperty(value = "日期区间开始时间",required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timeBegin;
    @ApiModelProperty(value = "日期区间结束时间",required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timeEnd;

    @ApiModelProperty(value ="全部-1,充值1,消费2,3消费抵现,4反结账退回,5反结账扣减",required = true)
    private Integer integralType;

    @ApiModelProperty(value = "订单号")
    private String orderNo;
    @ApiModelProperty(value = "会员Guid",required = true)
    @NotBlank(message = "会员Guid不能为空")
    private String memberGuid;


}
