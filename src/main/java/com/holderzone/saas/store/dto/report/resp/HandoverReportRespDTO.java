package com.holderzone.saas.store.dto.report.resp;

import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class HandoverReportRespDTO implements Serializable {

    private static final long serialVersionUID = 8373453166302893453L;

    @ApiModelProperty(value = "统计类型")
    private String statisticalType;

    @ApiModelProperty(value = "统计")
    private String statistical;

    @ApiModelProperty(value = "员工统计")
    private List<HandoverReportStaffDTO> staffStatisticals;

    @ApiModelProperty(value = "序号")
    private Integer sort;
}
