package com.holderzone.saas.store.dto.business.queue;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueuedRecordDTO
 * @date 2018/07/27 下午6:16
 * @description 排队记录DTO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueuedRecordDTO {

    /**
     * 门店guid
     */
    @ApiModelProperty("门店guid")
    private String storeGuid;

    /**
     * 排队类型guid
     */
    @ApiModelProperty("排队类型guid")
    private String queuedTypeGuid;

    /**
     * 排队记录guid
     */
    @ApiModelProperty("排队记录guid")
    private String queuedRecordGuid;

    /**
     * 创建人guid
     */
    @ApiModelProperty("创建人guid")
    private String createUserGuid;

    /**
     * 完成人guid
     */
    @ApiModelProperty("完成人guid")
    private String finishUserGuid;

    /**
     * 排队记录编号
     * 由Queue的code拼接QueuedRecord的seed而成
     */
    @ApiModelProperty("排队记录编号")
    private String code;

    /**
     * 本记录的种子号
     */
    @ApiModelProperty("本记录的种子号")
    private Integer seed;

    /**
     * 客人数
     */
    @ApiModelProperty("客人数")
    private Integer customerCount;

    /**
     * 客人手机号
     */
    @ApiModelProperty("客人手机号")
    private String customerTel;

    /**
     * 排队状态
     * 0=排号中
     * 1=完成
     * 2=过号
     */
    @ApiModelProperty("排队状态：0=排号中，1=完成，2=过号")
    private Integer status;

    /**
     * 叫号次数
     */
    @ApiModelProperty("叫号次数")
    private Integer callTimes;

    /**
     * 该排队队列的前面等待人数
     */
    @ApiModelProperty("该排队队列的前面等待人数")
    private Integer waitingCount;

    /**
     * 已等待时间
     * 单位：分钟
     */
    @ApiModelProperty("已等待时间，单位：分钟")
    private Long waitingTime;

    /**
     * 当前排号位置
     */
    @ApiModelProperty("当前排号位置")
    private Long waitingIndex;

    /**
     * 二维码链接
     */
    @ApiModelProperty("二维码链接")
    private String qrCodeUrl;

    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    @ApiModelProperty("是否已删除：0=未删除，1=已删除")
    private Integer deleted;

    /**
     * 营业日日期
     */
    @ApiModelProperty("营业日日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate businessDay;

    /**
     * 完成时间
     */
    @ApiModelProperty("完成时间")
    private LocalDateTime gmtFinished;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime gmtModified;
}
