package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Api("优惠券适用的门店")
@Data
@Builder
public class WxProductStoreDTO {
	@ApiModelProperty("门店GUID")
	private String storeGuid;
	@ApiModelProperty("门店key，充值用")
	private String storeKey;
	@ApiModelProperty("门店名称")
	private String storeName;
}
