package com.holderzone.saas.store.dto.zhuancan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/23
 * @description 赚餐叫号通知
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "赚餐叫号通知", description = "赚餐叫号通知")
public class ZhuanCanCallMessageDTO implements Serializable {

    private static final long serialVersionUID = 1557248289653063253L;

    @ApiModelProperty(value = "赚餐订单列表")
    private List<ZhuanCanOrderMessageDTO> zhuanCanOrderList;

}
