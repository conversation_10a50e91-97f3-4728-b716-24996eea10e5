package com.holderzone.saas.store.dto.order.response;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderListRespDTO
 * @date 2018/09/07 19:49
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class OrderListRespDTO extends BasePageDTO{

    /**
     * 状态(0：未完成， 1：已完成， 2：已作废 ，3：已退款，4：反结账状态)
     */
    @ApiModelProperty(value = "状态(0：未完成， 1：已完成， 2：已作废 ，3：已退款，4：反结账状态)")
    private Integer state;

    /**
     * 状态(0：未完成， 1：已完成， 2：已作废 ，3：已退款，4：反结账状态)
     */
    @ApiModelProperty(value = "状态(0：未完成， 1：已完成， 2：已作废 ，3：已退款，4：反结账状态)")
    private String stateName;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderFee;

    /**
     * 订单guid
     */
    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime creatTime;

    /**
     * 牌号
     */
    @ApiModelProperty(value = "牌号")
    private String mark;

//    /**
//     * 订单号
//     */
//    @ApiModelProperty(value = "订单号")
//    private String orderNo;

    /**
     * 创建操作人名字
     */
    @ApiModelProperty(value = "创建操作人名字")
    private String createStaffName;

    /**
     * 结账操作人名字
     */
    @ApiModelProperty(value = "结账操作人名字")
    private String checkoutStaffName;
}
