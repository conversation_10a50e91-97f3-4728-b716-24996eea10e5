package com.holderzone.saas.store.dto.print.format;

public enum ImagePixelEnum {

    MIN_58(0, 128, 128, 500 * 1000L),

    REC_58(1, 256, 256, 500 * 1000L),

    MAX_58(2, 384, 384, 500 * 1000L),

    MIN_80(3, 192, 192, 500 * 1000L),

    REC_80(4, 384, 384, 500 * 1000L),

    MAX_80(5, 576, 576, 500 * 1000L),

    ;

    private int type;

    private int width;

    private int height;

    private long size;

    ImagePixelEnum(int type, int width, int height, long size) {
        this.type = type;
        this.width = width;
        this.height = height;
        this.size = size;
    }

    public int getType() {
        return type;
    }

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    public long getSize() {
        return size;
    }
}
