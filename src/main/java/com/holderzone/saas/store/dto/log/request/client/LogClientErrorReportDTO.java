package com.holderzone.saas.store.dto.log.request.client;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * @Athor forewei
 * @Email <EMAIL>
 * @Date 2019/12/5
 */
@ApiModel(value = "客户端错误日志上报入参")
public class LogClientErrorReportDTO {

    @ApiModelProperty("平台  （收银系统|990  支付平台|80  MDM平台|40  ERP平台|50 小程序平台|60  会员平台|30）")
    @NotNull
    private String platform;

    @ApiModelProperty("终端类型  99003|一体机  99004|pos系列  99005|pad 99006|点菜宝  99009|kds 99014|TV 99011|自助点餐机")
    @NotNull
    private String module;

    @ApiModelProperty("企业guid")
    private String enterpriseGuid;

    @ApiModelProperty("企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty("门店名称")
    private String storeName;


    @ApiModelProperty("终端编号")
    private String terminalNo;

    @ApiModelProperty("操作人")
    private String createBy;

    @ApiModelProperty("日志产生时间")
    private String logdate;

    @ApiModelProperty("错误堆栈信息")
    private String message;


    @ApiModelProperty("扩充字段1")
    private String ext1;

    @ApiModelProperty("扩充字段2")
    private String ext2;

    @ApiModelProperty("扩充字段3")
    private String ext3;


    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getEnterpriseGuid() {
        return enterpriseGuid;
    }

    public void setEnterpriseGuid(String enterpriseGuid) {
        this.enterpriseGuid = enterpriseGuid;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getTerminalNo() {
        return terminalNo;
    }

    public void setTerminalNo(String terminalNo) {
        this.terminalNo = terminalNo;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getLogdate() {
        return logdate;
    }

    public void setLogdate(String logdate) {
        this.logdate = logdate;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }
}
