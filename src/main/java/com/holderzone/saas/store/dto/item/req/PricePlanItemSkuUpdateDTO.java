package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PricePlanItemSkuUpdateDTO {

    @ApiModelProperty(value = "方案菜品guid")
    private String planItemGuid;

    @ApiModelProperty(value = "原价，按百分比计算时需要传原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "方案售价")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "方案会员价")
    private BigDecimal memberPrice;
}
