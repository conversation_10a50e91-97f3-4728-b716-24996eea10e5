package com.holderzone.saas.store.dto.takeaway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutShopBindReqDTO
 * @date 2018/09/25 9:07
 * @description
 * @program holder-saas-store-takeaway
 */

@Data
@ApiModel
@NoArgsConstructor
public class TakeoutTCDKnightAcceptDTO {

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "商铺token")
    private String token;

    @ApiModelProperty(value = "拒绝原因")
    private String refuseReason;

    @ApiModelProperty(value = "拒绝时间")
    private LocalDateTime refuseTime;

    @ApiModelProperty(value = "配送员名称")
    private String deliveryPersonName;

    @ApiModelProperty(value = "配送员电话")
    private String deliveryPersonPhone;
}
