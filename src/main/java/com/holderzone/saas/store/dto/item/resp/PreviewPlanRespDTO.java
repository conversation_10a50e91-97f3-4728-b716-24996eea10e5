package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description 预览菜谱方案返回实体
 * @date 2021/5/31 17:45
 */
@ApiModel(value = "预览菜谱方案返回实体")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class PreviewPlanRespDTO {

    @ApiModelProperty(value = "方案guid")
    private String planGuid;

    @ApiModelProperty(value = "方案名")
    private String planName;

    @ApiModelProperty(value = "二维码")
    private String qrCode;

    @ApiModelProperty(value = "分享地址")
    private String shareUrl;

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    /**
     * 品牌logo（oss下载地址）
     */
    @ApiModelProperty(value = "品牌logo（oss下载地址）")
    private String brandLogoUrl;

    @ApiModelProperty(value = "菜谱方案商品分类返回实体")
    private List<PricePlanItemTypeRespDTO> planItemTypeRespDTOList;

}
