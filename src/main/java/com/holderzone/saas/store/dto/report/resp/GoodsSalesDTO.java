package com.holderzone.saas.store.dto.report.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("商品报表-销售报表")
public class GoodsSalesDTO {
    @ApiModelProperty(value = "品牌")
    private String brandName;
    @ApiModelProperty(value = "门店")
    private String storeName;
    private String storeGuid;
    @ApiModelProperty(value = "商品名称")
    private String goodsName;
    @ApiModelProperty(value = "商品分类")
    private String goodsCategories;
    @ApiModelProperty(value = "商品分类guid")
    private String goodsCategoriesGuid;
    @ApiModelProperty(value = "售价")
    private BigDecimal sellingPrise;
    @ApiModelProperty(value = "销量")
    private BigDecimal salesVolume;
    @ApiModelProperty(value = "属性加价")
    private BigDecimal propertyExtraPrice;
    @ApiModelProperty(value = "销售金额")
    private BigDecimal actualReceivedPrice;
    @ApiModelProperty(value = "商品优惠价")
    private BigDecimal discountPrice;
    @ApiModelProperty(value = "点单率")
    private String chosenRate;
    @ApiModelProperty(value = "商品guid")
    private String itemGuid;
    private String tradeMode;
    private String skuGuid;

    @ApiModelProperty(value = "毛利润")
    private BigDecimal grossProfitAmount;

}
