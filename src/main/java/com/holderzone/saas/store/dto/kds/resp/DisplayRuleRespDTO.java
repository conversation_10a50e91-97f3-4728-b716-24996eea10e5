package com.holderzone.saas.store.dto.kds.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description DisplayRuleRespDTO
 * @date 2021/1/28 15:10
 */
@Data
public class DisplayRuleRespDTO {

    /**
     * 显示规则guid
     */
    @ApiModelProperty(value = "显示规则guid")
    private String guid;

    /**
     * 显示状态 0延迟显示 1分批显示
     */
    @ApiModelProperty(value = "显示状态 0延迟显示 1分批显示")
    private Integer displayState;
    /**
     * 延迟时间
     */
    @ApiModelProperty(value = "延迟时间")
    private Integer delayTime;
    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    private Integer batch;

    /**
     * 是否全部门店
     */
    @ApiModelProperty(value = "是否全部门店")
    private Boolean isAllStore;

    /**
     * 规则类型 0显示批次 1菜品汇总
     */
    @ApiModelProperty(value = "规则类型 0显示批次 1菜品汇总")
    private Integer ruleType;
    /**
     * 生效状态 生效状态 0延迟生效 1立即生效
     */
    @ApiModelProperty(value = "生效状态 生效状态 0延迟生效 1立即生效")
    private Integer effectiveState;
    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime effectiveTime;
    /**
     * 门店列表
     */
    @ApiModelProperty(value = "门店列表")
    private List<DisplayStoreRespDTO> storeList;

    /**
     * 商品列表
     */
    @ApiModelProperty(value = "商品列表")
    private List<DisplayItemRespDTO> itemList;

    /**
     * 规则序号
     */
    @ApiModelProperty(value = "规则序号")
    private Integer ruleSort;
}
