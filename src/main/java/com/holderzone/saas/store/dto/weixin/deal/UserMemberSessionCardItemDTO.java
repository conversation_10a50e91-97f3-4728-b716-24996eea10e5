package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Data
@ApiModel("会员卡项缓存")
public class UserMemberSessionCardItemDTO {

	@ApiModelProperty("会员卡二维码")
	private String cardQRCode;
	@ApiModelProperty(value = "卡GUID",hidden = true)
	private String cardGuid;
	@ApiModelProperty("会员持卡GUID")
	private String memberInfoCardGuid;
	@ApiModelProperty(value = "卡体系GUID",hidden = true)
	private String systemManagementGuid;
	@ApiModelProperty(value = "会员拥有体系GUID",hidden = true)
	private String enterpriseMemberInfoSystemGuid;
	@ApiModelProperty("卡名称")
	private String cardName;
	@ApiModelProperty("卡LOGO,本期无")
	private String cardLogo;
	@ApiModelProperty("卡片图片路径")
	private String cardIcon;
	@ApiModelProperty("卡颜色")
	private String cardColour;
	@ApiModelProperty("有效期类型，0永久有效，2固定时间")
	private Integer validityType;
	@ApiModelProperty("开始有效期")
	private String cardStartDate;
	@ApiModelProperty("结束有效期")
	private String cardEndDate;
	@ApiModelProperty("卡类型,0成长型(默认卡),1荣誉型,2付费型,3实体卡")
	private Integer cardType;
	@ApiModelProperty("卡片编号")
	private String systemManagementCardNum;
	@ApiModelProperty("卡余额")
	private BigDecimal cardMoney;
	@ApiModelProperty("卡赠送余额")
	private BigDecimal giftMoney;
	@ApiModelProperty(value = "积分")
	private String cardIntegral;
	@ApiModelProperty(value = "卡成长值",hidden = true)
	private String cardGrowthValue;
	@ApiModelProperty("卡等级GUID")
	private String cardLevelGuid;
	@ApiModelProperty("卡等级名字")
	private String cardLevelName;
	@ApiModelProperty("卡等级编号,V-(n)")
	private Integer cardLevelNum;
	@ApiModelProperty("等级图标路径")
	private String levelIcon;
	@ApiModelProperty(value = "会员持卡是否默认卡 0不是，1是",hidden = true)
	private Integer isDefaultCard;
	@ApiModelProperty(hidden = true)
	private LocalDateTime gmtCreate;
	@ApiModelProperty(hidden = true)
	private LocalDateTime gmtModified;
	@ApiModelProperty(value = "1:选中，0：未选中")
	private Integer uck=0;

	public boolean compareTo(UserMemberSessionCardItemDTO o2) {
		return this.getIsDefaultCard() == 1
				? o2.getIsDefaultCard() == 1 && this.getGmtCreate().compareTo(o2.getGmtCreate()) < 0
				: o2.getIsDefaultCard() == 1 || this.getGmtCreate().compareTo(o2.getGmtCreate()) < 0;
	}
}
