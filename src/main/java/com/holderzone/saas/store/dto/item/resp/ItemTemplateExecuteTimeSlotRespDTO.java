package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateMenuTimeReqDTO
 * @date 2019/05/24 10:19
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "模板菜单执行时间段")
public class ItemTemplateExecuteTimeSlotRespDTO {

    /**
     * 执行开始时间
     */
    @ApiModelProperty(value = "执行开始时间  24小时格式：HH：ss")
    private String  startTime ;

    /**
     * 执行结束时间
     */
    @ApiModelProperty(value = "执行结束时间  24小时格式：HH：ss")
    private  String  endTime;
}

