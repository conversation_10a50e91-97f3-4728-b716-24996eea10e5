package com.holderzone.saas.store.dto.member.pay;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberPayDTO
 * @date 2019/02/28 14:02
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class MemberPayDTO extends BaseDTO{

    private String orderGuid;

    private String storeGuid;

    private String storeName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tradingTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate businessDay;

    //消费金额
    private BigDecimal amount;

    private String memberGuid;

    private String telPhoneNo;

    @ApiModelProperty(value = "0:代表充值 1:代表消费 2:代表退款")
    private Integer type;

    @ApiModelProperty(value = "是否成功")
    private Boolean flag;

    private String operationStaffGuid;

    private String operationStaffName;

    @ApiModelProperty(value = "交易流水号")
    private String sequenceNo;

    @ApiModelProperty(value = "充值类型 0：现金，1:聚合支付，2：刷卡")
    private Byte prepaidType;

    @ApiModelProperty(value = "充值类型name")
    private String prepaidTypeName;

    /**
     * 消费类型
     */
    @ApiModelProperty(value = "0:代表会员卡消费 1:代表非会员卡消费")
    private Integer consumeType;

    @ApiModelProperty(value = "是否使用积分")
    private Boolean useIntegral;

    @ApiModelProperty(value = "消费多少积分（正数）")
    private Integer integral;

    private String enterpriseGuid;
}
