package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ChargeDTO
 * @date 2018/09/17 11:56
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class ChargeDTO implements Serializable {

    @ApiModelProperty(value = "规格guid")
    private String chargeGuid;

    @ApiModelProperty(value = "产品guid")
    private String productGuid;

    @ApiModelProperty(value = "消费类型")
    private String chargeType;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "单位")
    private int unit;

    @ApiModelProperty(value = "数量")
    private int unitNumber;

    @ApiModelProperty(value = "折扣")
    private BigDecimal concessionalRate;

}
