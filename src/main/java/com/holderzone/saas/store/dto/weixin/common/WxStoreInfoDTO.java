package com.holderzone.saas.store.dto.weixin.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreInfoDTO
 * @date 2019/05/09 18:50
 * @description 门店微信配置公共属性DTO
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("门店微信配置公共属性DTO")
public class WxStoreInfoDTO {

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty("联系电话")
    private String tel;

    @ApiModelProperty("门店地址")
    private String address;

    @ApiModelProperty("门店是否营业")
    private Boolean isOpened=true;

    @ApiModelProperty("用户距当前门店的距离")
    private Integer distant;

    @ApiModelProperty("门店经度")
    private BigDecimal longitude;

    @ApiModelProperty("门店纬度")
    private BigDecimal latitude;

    @ApiModelProperty(value = "门店所属品牌名字集合")
    private List<String> brandNameList;
}
