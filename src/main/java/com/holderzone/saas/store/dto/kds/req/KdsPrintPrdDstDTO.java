package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 打印公共基础类
 *
 * <AUTHOR>
 * @date 2018/09/18 19:32
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "制作单")
public class KdsPrintPrdDstDTO extends KdsPrintItemDTO {

    @ApiModelProperty(value = "根据交易模式：桌台名、快餐、饿了么|美团")
    private String orderDesc;

    @ApiModelProperty(value = "根据交易模式：订单号、订单号、订单号")
    private String orderNumber;

    @ApiModelProperty(value = "根据交易模式：桌台名、牌号、外卖日流水号")
    private String orderSerialNo;
}
