package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @description 聚合支付账户设置入参
 * @date 2021/11/23 16:50
 * @className: PaymentAccountReqDTO
 */
@Data
public class PaymentAccountReqDTO {

    @ApiModelProperty(value = "支付方式guid", required = true)
    private String paymentTypeGuid;

    @ApiModelProperty("聚合支付配置信息")
    private List<PaymentInfoDTO> jhPayInfoList;

    @ApiModelProperty(value = "收款分流：0关闭 1开启", required = true)
    private Integer paymentShunt;

    @ApiModelProperty(value = "门店guid", required = true)
    @NotBlank(message = "门店guid不能为空")
    private String storeGuid;

    @ApiModelProperty("要删除的支付方式guid列表")
    private List<String> toDeleteList;
}
