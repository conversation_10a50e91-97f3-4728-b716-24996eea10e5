package com.holderzone.saas.store.dto.report.resp;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

public class PageExt<T> {

    private long currentPage;
    private long pageSize;
    private long totalCount;

    @ApiModelProperty("消费金额小计")
    private String consumeSubtotal;

    @ApiModelProperty("储值金额小计")
    private String storedAmountSubtotal;

    @ApiModelProperty("预定金额小计")
    private String preOrderAmountSubtotal;

    @ApiModelProperty("退款金额小计")
    private String refundSubtotal;

    @ApiModelProperty("收支金额小计")
    private String inoutSubtotal;

    @ApiModelProperty("消费、储值、预定金额总计")
    private String cspTotal;

    @ApiModelProperty("退款金额总计")
    private String refundTotal;

    @ApiModelProperty("收支金额总计")
    private String inoutTotal;

    @ApiModelProperty("消费金额小计")
    private List<T> data;

    public PageExt() {
        this.currentPage = 1L;
        this.pageSize = 10L;
        this.data = new ArrayList();
    }

    public PageExt(long currentPage, long pageSize, long totalCount, List<T> data) {
        this(currentPage, pageSize, totalCount);
        this.data = data;
    }

    public PageExt(long currentPage, long pageSize, long totalCount) {
        this.currentPage = 1L;
        this.pageSize = 10L;
        this.data = new ArrayList();
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        this.totalCount = totalCount;
    }

    public PageExt(long currentPage, long pageSize, List<T> data) {
        this(currentPage, pageSize);
        this.data = data;
    }

    public PageExt(long currentPage, long pageSize) {
        this.currentPage = 1L;
        this.pageSize = 10L;
        this.data = new ArrayList();
        this.currentPage = currentPage;
        this.pageSize = pageSize;
    }

    public long getCurrentPage() {
        return this.currentPage;
    }

    public void setCurrentPage(long currentPage) {
        this.currentPage = currentPage;
    }

    public long getPageSize() {
        return this.pageSize;
    }

    public void setPageSize(long pageSize) {
        this.pageSize = pageSize;
    }

    public long getTotalCount() {
        return this.totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public List<T> getData() {
        return this.data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public String getConsumeSubtotal() {
        return consumeSubtotal;
    }

    public void setConsumeSubtotal(String consumeSubtotal) {
        this.consumeSubtotal = consumeSubtotal;
    }

    public String getStoredAmountSubtotal() {
        return storedAmountSubtotal;
    }

    public void setStoredAmountSubtotal(String storedAmountSubtotal) {
        this.storedAmountSubtotal = storedAmountSubtotal;
    }

    public String getPreOrderAmountSubtotal() {
        return preOrderAmountSubtotal;
    }

    public void setPreOrderAmountSubtotal(String preOrderAmountSubtotal) {
        this.preOrderAmountSubtotal = preOrderAmountSubtotal;
    }

    public String getRefundSubtotal() {
        return refundSubtotal;
    }

    public void setRefundSubtotal(String refundSubtotal) {
        this.refundSubtotal = refundSubtotal;
    }

    public String getInoutSubtotal() {
        return inoutSubtotal;
    }

    public void setInoutSubtotal(String inoutSubtotal) {
        this.inoutSubtotal = inoutSubtotal;
    }

    public String getCspTotal() {
        return cspTotal;
    }

    public void setCspTotal(String cspTotal) {
        this.cspTotal = cspTotal;
    }

    public String getRefundTotal() {
        return refundTotal;
    }

    public void setRefundTotal(String refundTotal) {
        this.refundTotal = refundTotal;
    }

    public String getInoutTotal() {
        return inoutTotal;
    }

    public void setInoutTotal(String inoutTotal) {
        this.inoutTotal = inoutTotal;
    }
}

