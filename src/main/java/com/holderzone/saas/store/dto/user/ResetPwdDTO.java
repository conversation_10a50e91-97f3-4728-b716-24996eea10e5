package com.holderzone.saas.store.dto.user;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UpdatePwdDTO
 * @date 18-11-2 下午3:22
 * @description 修改密码DTO
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "重置密码请求实体", description = "重置当前登录员工密码时该实体可以为空")
public class ResetPwdDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 9201687964798437361L;

    @ApiModelProperty(value = "用户GUID（重置下属员工密码时不能为空）")
    private String guid;
}
