package com.holderzone.saas.store.dto.retail.item;

import com.holderzone.saas.store.dto.order.request.item.ItemReturnOrFreeReqDTO;
import com.holderzone.saas.store.dto.table.BaseTableDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BatchItemReturnOrFreeReqDTO
 * @date 2019/01/23 11:27
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class BatchItemReturnOrFreeReqDTO extends BaseTableDTO {

    @ApiModelProperty(value = "退货赠送菜品信息")
    private List<ItemReturnOrFreeReq> itemReturnOrFreeReqs;


    @Data
    public static class ItemReturnOrFreeReq {

        @ApiModelProperty(value = "订单商品guid")
        private String orderItemGuid;

        @ApiModelProperty(value = "赠/退记录")
        private List<ItemReturnOrFreeReqDTO> itemReturnOrFreeReqDTOS;

    }


}
