package com.holderzone.saas.store.dto.member.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreBaseRespDTO
 * @date 2018/09/29 15:34
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class StoreBaseRespDTO {
    @ApiModelProperty(value = "门店GUID")
    private String StoreGuid;
    @ApiModelProperty(value = "门店名称")
    private String StoreName;
}
