package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseClientMarketActivity;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.weixin.deal.ActivitySelectDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 小程序订单结算 返回DTO
 */
@Data
public class CalculateOrderRespDTO implements Serializable {

    private static final long serialVersionUID = -3101581851274634862L;

    @ApiModelProperty(value = "是否使用会员优惠")
    private Boolean useMemberDiscountFlag;

    @ApiModelProperty(value = "是否有会员价")
    private Boolean hasMemberPrice;

    @ApiModelProperty(value = "是否有会员折扣")
    private Boolean hasMemberDiscount;

    @ApiModelProperty(value = "会员折扣")
    private BigDecimal memberDiscount;

    @ApiModelProperty(value = "不可使用会员优惠(互斥)")
    private Boolean unAbleMemberDiscountFlag;

    @ApiModelProperty(value = "不可使用会员优惠(互斥)原因")
    private String unAbleMemberDiscountReason;

    @ApiModelProperty(value = "订单剩余可以优惠金额")
    private BigDecimal orderSurplusFee;

    @ApiModelProperty(value = "订单剩余可以优惠金额(跳过限时特价)")
    private BigDecimal orderSurplusFeeBySkipSpecials;

    @ApiModelProperty(value = "订单金额（商品总额+附加费）")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "附加费")
    private BigDecimal appendFee;

    @ApiModelProperty(value = "优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "营销活动所选列表")
    private List<ActivitySelectDTO> activitySelectList;

    @ApiModelProperty("营销活动列表")
    private List<MarketingActivityInfoRespDTO> activityInfoList;

    @ApiModelProperty(value = "错误原因")
    private String tip;

    @ApiModelProperty(value = "优惠明细")
    private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

    @ApiModelProperty(value = "附加费明细")
    private List<SurchargeLinkDTO> appendFeeDetailDTOS;

    @ApiModelProperty(value = "商品明细")
    private List<DineInItemDTO> dineInItemList;

}
