package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY,
        getterVisibility = JsonAutoDetect.Visibility.NONE)
public class UnItemBindUnbindReq extends UnItemBaseMapReq {

    private static final long serialVersionUID = -5711573937851844989L;

    @NotBlank(message = "ERP门店id不得为空")
    @ApiModelProperty(value = "ERP门店id", required = true)
    private String storeGuid;

    @Min(value = 0, message = "外卖类型(0：美团，1：饿了么，2：掌控者,3:赚餐,4:抖音,5:京东)")
    @Max(value = 5, message = "外卖类型(0：美团，1：饿了么，2：掌控者,3:赚餐,4:抖音,5:京东)")
    @NotNull(message = "商品映射类型不得为空")
    @ApiModelProperty(value = "商品映射类型：0=美团，1=饿了么，2=掌控者,3=赚餐,4:抖音,5:京东", required = true)
    private Integer takeoutType;

    @ApiModelProperty("美团方下单时传递的skuId")
    private String mtSkuId;
}
