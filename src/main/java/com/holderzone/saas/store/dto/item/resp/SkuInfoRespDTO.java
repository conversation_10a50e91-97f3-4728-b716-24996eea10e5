package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SkuSynRespDTO
 * @date 2019/01/03 下午2:57
 * @description //商品规格详情的返回实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "商品规格详情的返回实体")
public class SkuInfoRespDTO implements Serializable {
    private static final long serialVersionUID = 1567286740443940790L;

    private Long id;

    @ApiModelProperty(value = "分类GUID")
    private String typeGuid;

    @ApiModelProperty(value = "item分类GUID")
    private String itemTypeGuid;

    @ApiModelProperty(value = "商品GUID")
    private String itemGuid;

    @ApiModelProperty(value = "规格GUID")
    private String skuGuid;

    @ApiModelProperty(value = "upc")
    private String upc;

    @ApiModelProperty(value = "当日库存")
    private Integer stock;

    @ApiModelProperty("是否次日置满  1：否  2：是")
    private Integer isItReset;
    @ApiModelProperty(value = "成本价格")
    @DecimalMin(value = "0.01")
    @DecimalMax(value = "99999.99")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "会员价格")
    @DecimalMin(value = "0.01")
    @DecimalMax(value = "99999.99")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "规格名称")
    private String name;

    @ApiModelProperty(value = "售卖价格")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "外卖价格")
    private BigDecimal takeawayPrice;

    @ApiModelProperty(value = "外卖会员价格")
    private BigDecimal takeawayMemberPrice;

    @ApiModelProperty(value = "是否加入整单折扣(0：否，1：是)")
    private Integer isWholeDiscount;

    @ApiModelProperty(value = "是否参与会员折扣（0：否，1：是）")
    private Integer isMemberDiscount;

    @ApiModelProperty(value = "起卖数(非称重即为整数，称重即为小数)")
    private BigDecimal minOrderNum;

    @ApiModelProperty(value = "计数单位")
    private String unit;

    @ApiModelProperty(value = "计数单位编码")
    private String unitCode;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "安全库存")
    private BigDecimal safeStock;

    @ApiModelProperty(value = "当前总库存")
    private BigDecimal totalStock;

    @ApiModelProperty(value = "是否上架")
    private Integer isRack;

    @ApiModelProperty(value = "是否上架一体机（0：否，1：是）")
    private Integer isJoinAio;

    @ApiModelProperty(value = "是否上架POS机（0：否，1：是）")
    private Integer isJoinPos;

    @ApiModelProperty(value = "是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;

    @ApiModelProperty(value = "是否参与自助点餐机（0：否，1：是）")
    private Integer isJoinBuffet;

    @ApiModelProperty(value = "是否参与微信点餐（0：否，1：是）")
    private Integer isJoinWeChat;

    @ApiModelProperty(value = "是否参与美团外卖（0：否，1：是）")
    private Integer isJoinMt;

    @ApiModelProperty(value = "是否参与饿了么外卖（0：否，1：是）")
    private Integer isJoinElm;

    @ApiModelProperty(value = "是否开启库存（0：否，1：是）")
    private Integer isOpenStock;

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "sku来源（0：门店自己创建的sku，1：品牌自己创建的sku,2:被推送过来的sku）")
    private Integer skuFrom;

    @ApiModelProperty(value = "父规格GUID")
    private String parentGuid;
    @ApiModelProperty(value = "是否参与小程序商城（0：否，1：是） （冗余小程序端字段）")
    private Integer isJoinMiniAppMall;
    @ApiModelProperty(value = "是否参与小程序外卖（0：否，1：是） （冗余小程序端字段）")
    private Integer isJoinMiniAppTakeaway;
    @ApiModelProperty(value = "是否支持堂食（0：否，1：是） （冗余小程序端字段）")
    private Integer isJoinStore;

    @ApiModelProperty(value = "是否推荐（冗余小程序端字段）")
    private Integer isRecommend;

    @ApiModelProperty(value = "虚拟价格（冗余小程序端字段）")
    private BigDecimal virtualPrice;

    @ApiModelProperty(value = "销售总量（冗余小程序端字段）")
    private Integer totalSale;

    @ApiModelProperty(value = "月销售量（冗余小程序端字段）")
    private Integer monthlySale;

    @ApiModelProperty(value = "商品名称（冗余小程序端字段）")
    private String itemName;

    /**
     * 商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品 5.团餐。
     */
    @ApiModelProperty(value = "商品类型")
    private Integer itemType;

    @ApiModelProperty(value = "商品分类名称（冗余小程序端字段）")
    private String typeName;

    @ApiModelProperty(value = "商品主图路径")
    private String pictureUrl;

    @ApiModelProperty(value = "外卖打包费（冗余小程序端字段）")
    private BigDecimal takeawayPackageFee;

    @ApiModelProperty(value = "商城打包费（冗余小程序端字段）")
    private BigDecimal mallPackageFee;

    @ApiModelProperty(value = "sku（冗余小程序端字段）")
    private String sku;

    @ApiModelProperty(value = "是否上架Kds（0：否，1：是）")
    private Integer isJoinKds;

    /**
     * 核算价
     */
    @ApiModelProperty("堂食核算价")
    private BigDecimal accountingPrice;

    @ApiModelProperty("外卖核算价")
    private BigDecimal takeawayAccountingPrice;

    /**
     * 是否套餐
     */
    @ApiModelProperty("是否套餐")
    private Boolean isPkgItem;

    // 赚餐扫码点餐对接所需字段
    @ApiModelProperty(value = "1表示选中，0非选中")
    private Integer uck = 0;

    @ApiModelProperty("true:有会员价")
    private Boolean enablePreferentialPrice = false;

    @ApiModelProperty("是否估清 1:否 2:是")
    private Integer isSoldOut;

    @ApiModelProperty("当前剩余数量")
    private BigDecimal residueQuantity;

    @ApiModelProperty("套餐信息")
    private List<SkuInfoPkgDTO> listPkg;
}
