package com.holderzone.saas.store.dto.report.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 挂账明细列表
 */
@Data
public class DebtUnitRecordDTO implements Serializable {

    private static final long serialVersionUID = -7268651757454110822L;

    /**
     * 全局唯一主键
     */
    @ApiModelProperty(value = "主键")
    private String guid;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    /**
     * 还款票据编号
     */
    @ApiModelProperty(value = "还款票据编号")
    private String debtInvoiceCode;

    /**
     * 挂账门店
     */
    @ApiModelProperty(value = "挂账门店")
    private String storeName;

    /**
     * 是否还款（默认0，未还款，1已还款）
     */
    @ApiModelProperty(value = "是否还款（默认不传查全部！0:未还款，1:已还款）")
    private Integer repaymentStatus;

    /**
     * 挂账金额
     */
    @ApiModelProperty(value = "挂账金额")
    private BigDecimal debtFee;

    /**
     * 还款金额
     */
    @ApiModelProperty(value = "还款金额")
    private BigDecimal repaymentFee;

    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式（0：现金，1：支付宝，2：微信，3：银行转账，4：支票）")
    private Integer paymentType;

    /**
     * 挂账时间
     */
    @ApiModelProperty(value = "挂账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 还款时间
     */
    @ApiModelProperty(value = "还款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 挂账操作人
     */
    @ApiModelProperty(value = "挂账操作人")
    private String createStaffName;

    /**
     * 还款操作人
     */
    @ApiModelProperty(value = "还款操作人")
    private String updateStaffName;
}
