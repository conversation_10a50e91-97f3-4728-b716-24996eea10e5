package com.holderzone.saas.store.dto.pay;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayNotifyDTO
 * @date 2019/03/18 16:29
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class AggPayNotifyDTO {

    private String code;

    private String msg;

    private BigDecimal amount;

    /**
     * 平台订单号（是）
     */
    private String orderNo;
    /**
     * 商户订单号（是）
     * 同orderHolderNo
     */
    private String orderHolderNo;
    /**
     * 商户GUID
     */
    private String orderGUID;

    /**
     * 商户订单支付唯一标示
     */
    private String payGUID;

    /**
     * 支付功能id（是）
     */
    private String payPowerId;
    /**
     * 二维码连接，支付宝扫码，微信扫码返回（否）
     */
    private String code_url;
    /**
     * 支付宝授权链接（否）
     * payPowerId=3或者payPowerId=17时候，商户未传openId的时候返回。
     * 然后点击进入支付宝授权
     */
    private String authUrl;
    /**
     * 上游订单号（否）
     * 条码支付成功时候返回，
     * 当 payPowerId= 3 或 payPowerId=17 时，商户下单时传 openId参数时有返回
     */
    private String bankTransactionId;
    /**
     * 唤起支付的参数（否）
     * json格式
     */
    private String prepay_info;
    /**
     * 微信H5接口支付链接（否）
     */
    private String mweb_url;
    /**
     * 下单日期（否）
     * 条码支付成功时候有返回（支付宝，微信），创建订单时间：yyyyMMdd
     */
    private String orderDt;
    /**
     * 订单手续费（否）
     * 条码支付成功时候返回（微信，支付宝），该笔订单所扣费用
     */
    private String fee;
    /**
     * 支付完成时间（否）
     * 条码支付成功时候有返回：yyyyMMddHHmmss
     */
    private String paidTime;
    /**
     * 支付用户信息（否）
     * 条码支付成功时候有返回，该笔订单支付用户信息
     */
    private String openId;
    /**
     * 商户订单描述（否）
     * 条码支付成功时候返回
     */
    private String body;
    /**
     * 商户订单标题（否）
     * 条码支付成功时候返回
     */
    private String subject;
    /**
     * 支付结果（否）
     * 条码支付成功时候返回  2：支付成功
     */
    private String paySt;

    /**
     * 签名（否）
     */
    private String signature;

    /**
     * 京东pc网，h5网页支付返回（否）
     */
    private String jdResult;

    /**
     * 给商户原路返回的data
     */
    private String attachData;


}
