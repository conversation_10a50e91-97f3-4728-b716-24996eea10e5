package com.holderzone.saas.store.dto.store.store;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className StoreAllInfoDTO
 * @date 18-9-10 下午3:19
 * @description 门店信息（包括营业时间、外卖接单配置等）相关DTO
 * @program holder-saas-store-store
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class StoreAllInfoDTO extends OrganizeDTO {

    private static final long serialVersionUID = -3438707772453407428L;

    /**
     * 门店营业开始时间（00:00:00-23:59:59）
     */
    @ApiModelProperty("门店营业开始时间（00:00:00-23:59:59）")
    @JsonFormat(pattern = "HH:mm:ss")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime businessStartTime;

    /**
     * 门店营业结束时间（00:00:00-23:59:59）
     */
    @ApiModelProperty("门店营业结束时间（00:00:00-23:59:59）")
    @JsonFormat(pattern = "HH:mm:ss")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime businessEndTime;

    /**
     * 外卖接单配置是否自动（1=自动接单，0=手动接单）
     */
    @ApiModelProperty("外卖接单配置是否自动（1=自动接单，0=手动接单）")
    private Integer auto;

    /**
     * 根据storeGuid获取存入Redis中的key名
     *
     * @param storeGuid 门店guid
     * @return redis key
     */
    public static String getRedisKey(String storeGuid) {
        return "organization:storeInfo:" + storeGuid;
    }
}
