package com.holderzone.saas.store.dto.retail.bill.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillCalculateReqDTO
 * @date 2019/01/28 17:32
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class RetailCalculateReqDTO extends BaseDTO {

    @ApiModelProperty(value = "订单guid")
    @OrderLockField
    private String orderGuid;

    @ApiModelProperty(value = "1：会员登陆，2：会员登出，-1：不操作会员")
    private Integer memberLogin;

    @ApiModelProperty(value = "是否计算积分1：计算，2：不计算")
    private Integer memberIntegral;

    @ApiModelProperty(value = "会员卡guid")
    private String memberInfoCardGuid;

    @ApiModelProperty(value = "会员电话")
    private String memberPhone;

    @ApiModelProperty(value = "整单折扣")
    private BigDecimal wholeDiscount;

    @ApiModelProperty(value = "整单让价")
    private BigDecimal concessional;

    @ApiModelProperty(value = "1：验券，2：撤销，3：查询（不验券，只给微信用）")
    private Integer verify;


}
