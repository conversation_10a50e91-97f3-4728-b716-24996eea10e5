package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> R
 * @date 2021/3/18 12:25
 * @description
 */
@Data
public class TypePricePlanReqDTO {

    @ApiModelProperty("typeGuid")
    private String typeGuid;

    @ApiModelProperty("商品分类名称")
    @NotBlank(
            message = "商品分类名称不能为空"
    )
    @Size(min = 1, max = 20)
    private String name;

    @ApiModelProperty("排序，新增单个分类时非必填，但拖动排序时必填")
    @Min(value = 1)
    private Integer sort;

    @ApiModelProperty("删除状态0：false,1:true")
    private Integer isDelete;

    @ApiModelProperty(value = "描述")
    @Size(max = 50)
    private String description;

    /**
     * 菜谱分类图片类型
     */
    @ApiModelProperty(value = "菜谱分类图片类型(0小图,1竖图,2整屏)")
    private Integer menuClassifyPictureType;

    /**
     * 编辑后的分类名称 (批量编辑菜谱 - 编辑分类)
     */
    private String updateAfterName;

}
