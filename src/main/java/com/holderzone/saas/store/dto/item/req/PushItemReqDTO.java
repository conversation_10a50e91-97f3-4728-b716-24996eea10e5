package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.item.common.ItemLogDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PushItemReqDTO extends ItemLogDTO {

    @ApiModelProperty(value = "被推送的规格GUID集合")
    private List<String> skuGuidList;

    private List<String> storeGuidList;

    @ApiModelProperty(value = "是否更新子商品")
    private Boolean isUpdateSubItem;
}