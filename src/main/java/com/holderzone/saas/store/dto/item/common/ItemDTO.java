package com.holderzone.saas.store.dto.item.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


@Data
@NoArgsConstructor
public class ItemDTO {

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "更改时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    @ApiModelProperty(value = "商品GUID")
    private String guid;

    @ApiModelProperty(value = "企业GUID")
    private String enterpriseGuid;

    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "商品关联的分类GUID")
    private String typeGuid;

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品 5.团餐。")
    private Integer itemType;

    @ApiModelProperty(value = "是否售罄:0 否 1 是")
    private Integer isSoldOut;

    @ApiModelProperty(value = "属性组状态:0：无属性; 1:有属性; 2:有必选属性组(如果是套餐，则该字段=0)")
    private Integer hasAttr;

    @ApiModelProperty(value = "拼音简码")
    private String pinyin;

    @ApiModelProperty(value = "商品名称简写")
    private String nameAbbr;

    @ApiModelProperty(value = "商品描述")
    private String description;

    @ApiModelProperty(value = "图片路径数组json")
    private String pictureUrl;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "是否产生订单：0：否，1：是。")
    private Integer inOrder;

    @ApiModelProperty(value = "是否是新品（0：否，1：是）(新品)")
    private Integer isNew;

    @ApiModelProperty(value = "是否热销：0：否，1：是")
    private Integer isBestseller;

    @ApiModelProperty(value = "是否是招牌：0：否，1：是")
    private Integer isSign;

    @ApiModelProperty(value = "是否因重复而改名：0：否，1：是")
    private Integer nameChange;

    @ApiModelProperty(value = "图文详情（冗余小程序端字段）")
    private String remarkDetail;

    @ApiModelProperty(value = "配送类型 1同城配送;2快递运输（冗余小程序端字段）")
    private Integer mallDelivery;

    @ApiModelProperty(value = "code（冗余小程序端字段）")
    private String code;

    @ApiModelProperty(value = "好评数（冗余小程序端字段）")
    private Integer upCount;

    @ApiModelProperty(value = "差评数（冗余小程序端字段）")
    private Integer downCount;

    @ApiModelProperty(value = "审核状态（冗余小程序端字段）")
    private Integer auditStatus;

    @ApiModelProperty(value = "视频url json数组（冗余小程序端字段）")
    private String videoUrls;

    @ApiModelProperty(value = "是否推荐（冗余小程序端字段） 0否 1是")
    private Integer isRecommend;

    @ApiModelProperty(value = "是否启用（冗余小程序端字段） 0否 1是")
    private Integer isEnabled;

    @ApiModelProperty(value = "分类名")
    private String typeName;

    @ApiModelProperty(value = "名称")
    protected String name;

}
