package com.holderzone.saas.store.dto.order.response.daily;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/14
 * @since 1.8
 */
@Data
@ApiModel
public class RefundAmountDTO {

    @ApiModelProperty(value = "退款方式编码 1 正餐部分退款 2 正餐反结账 3 快销部分退款 4 快销反结账 5 合计")
    private Integer refundCode;

    @ApiModelProperty(value = "退款方式名称")
    private String refundName;

    @ApiModelProperty(value = "退款笔数")
    private Integer refundOrderCount;

    @ApiModelProperty(value = "退款总金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "是否合计项,0否 1是")
    private int isTotal = 0;
}
