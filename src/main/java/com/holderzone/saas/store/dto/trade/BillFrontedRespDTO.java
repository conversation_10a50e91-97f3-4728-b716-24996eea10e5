package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.sun.scenario.effect.impl.prism.PrRenderInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillFrontedRespDTO
 * @date 2018/09/10 15:10
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class BillFrontedRespDTO extends BaseBillDTO {

    /**
     * 客人数
     */
    @ApiModelProperty(value = "客人数量")
    private Integer guestCount;

    /**
     * 桌台Guid
     */
    @ApiModelProperty(value = "桌台Guid")
    private String diningTableGuid;

    /**
     * 桌台名称
     */
    @ApiModelProperty(value = "桌台名称")
    private String diningTableName;

    /**
     * 交易类型(0：堂食 1：快销  2：外卖 )
     */
    @ApiModelProperty(value = "交易类型(0：堂食 1：快销  2：外卖 )")
    private Integer tradeMode;

    /**
     * 会员guid
     */
    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    /**
     * 会员电话
     */
    @ApiModelProperty(value = "会员电话")
    private String telPhoneNo;

    @ApiModelProperty(value = "会员名字")
    private String memberName;

    @ApiModelProperty(value = "会员余额")
    private BigDecimal remaining;

    @ApiModelProperty("该会员可用积分")
    private Integer couldUseScore;

    /**
     * 创建账单时间
     */
    @ApiModelProperty(value = "创建账单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTimestamp;

    @ApiModelProperty(value = "结账员工guid")
    private String checkoutStaffGuid;

    @ApiModelProperty(value = "结账员工name")
    private String checkoutStaffName;
    /**
     * 结算完成时间
     */
    @ApiModelProperty(value = "结算完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutedTimestamp;


    @ApiModelProperty(value = "营业日时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate businessDaystamp;

    /**
     * 反结账员工信息
     */
    @ApiModelProperty(value = "反结账员工信息guid")
    private String recoveryStaffGuid;

    @ApiModelProperty(value = "反结账员工信息name")
    private String recoveryStaffName;
    /**
     * 状态 10：待支付  0：富民支付中  1：支付中(已请求上游） 2：支付成功 3：支付失败 4：退款  5：已关闭  7：反结账状态 -1 删除
     */
    @ApiModelProperty(value = "状态(10：待支付  0：富民支付中  1：支付中(已请求上游） 2：支付成功 3：支付失败 4：退款  5：已关闭  6 撤销 7：反结账状态 -1 删除")
    private Integer state;
    /**
     * 反结账UUID
     */
    @ApiModelProperty(value = "反结账UUID")
    private String recoveryUuid;

    /**
     * 折扣详情
     */
    @ApiModelProperty(value = "折扣详情")
    private List<BillDiscountDTO> billDiscountDTOList;

    /**
     * 附加费合计
     */
    @ApiModelProperty(value = "附加费详情")
    private List<AddtionalFeeDTO> addtionalFeeDOList;

    /**
     * 总折扣=couponsFee+itemTotalDiscountFee+memberDiscountFee（或者wholeOrderDiscountFee）+memberScoreDiscountFee+modifyDirectlyFee
     */
    @ApiModelProperty(value = "附加费合计")
    private BigDecimal totalDiscountFee;

    @ApiModelProperty(value = "消费总计=附加费+商品总额")
    private BigDecimal consumerTotalFee;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    private BigDecimal shouldPayFee;
    /**
     * 实付金额
     */
    @ApiModelProperty(value = "实付金额")
    private BigDecimal actuallyPayFee;

    /**
     * 附加费合计
     */
    @ApiModelProperty(value = "附加费合计")
    private BigDecimal addtionalFee;

    /**
     * 优惠券折扣费总计
     */
    @ApiModelProperty(value = "优惠券折扣费总计")
    private BigDecimal couponsFee;

    /**
     * 单品折扣费总计
     */
    @ApiModelProperty(value = "单品折扣费总计")
    private BigDecimal itemTotalDiscountFee;

    /**
     * 会员折扣费用
     */
    @ApiModelProperty(value = "会员折扣费用")
    private BigDecimal memberDiscountFee;
    /**
     * 整单折扣费用
     */
    @ApiModelProperty(value = "整单折扣费用")
    private BigDecimal wholeOrderDiscountFee;
    /**
     * 会员积分折扣
     */
    @ApiModelProperty(value = "会员积分折扣")
    private BigDecimal memberScoreDiscountFee;
    /**
     * 直接修改价格费用(最后一步计算，之前任何价格有改动，该项都清零)
     */
    @ApiModelProperty(value = "直接修改价格费用")
    private BigDecimal modifyDirectlyFee;

    /**
     * 菜品优惠券折扣
     */
    @ApiModelProperty(value = "菜品优惠券折扣")
    private BigDecimal dishCouponsFee;

    /**
     * 赠送菜品折扣
     */
    @ApiModelProperty(value = "赠送菜品折扣")
    private BigDecimal presentDishFee;

    /**
     * 菜品信息
     */
    @ApiModelProperty(value = "菜品信息，以订单号分组，考虑并单、拆单情况")
    private Map<String, List<BillDishDTO>> billDishDoPerDiningTable;

}
