package com.holderzone.saas.store.dto.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayPollingDTO
 * @date 2019/03/14 10:32
 * @description 轮询聚合支付实体
 * @program holder-saas-store-dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AggPayPollingDTO {

    @ApiModelProperty(value = "商户订单GUID")
    private String orderGUID;

    @ApiModelProperty(value = "商户透传数据")
    private String attachData;

    @ApiModelProperty(value = "平台退款单号，如果有这个参数，只会返回一条记录")
    private String refundNo;

    @ApiModelProperty(value = "支付唯一标示")
    private String payGUID;

    @ApiModelProperty(value = "商户唯一标识，appId")
    private String appId;

    @ApiModelProperty(value = "发起请求的时间")
    private Long timestamp;

    @ApiModelProperty(value = "开发者ID")
    private String developerId;

    @ApiModelProperty(value = "签名")
    private String signature;

}
