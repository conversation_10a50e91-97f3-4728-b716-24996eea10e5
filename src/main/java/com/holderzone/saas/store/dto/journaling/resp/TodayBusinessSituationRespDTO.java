package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TodayBusinessSituationRespDTO
 * @date 2019/06/04 10:29
 * @description app报表-今日营业概况respDTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class TodayBusinessSituationRespDTO implements Serializable {

    private static final long serialVersionUID = -2854428737293430677L;
    @ApiModelProperty(value = "营业额(已结)")
    private BigDecimal businessFee;
    @ApiModelProperty(value = "订单数(已结)")
    private Integer orderCount;
    @ApiModelProperty(value = "营业额(未结)")
    private BigDecimal unsettledBusinessFee;
    @ApiModelProperty(value = "订单数(未结)")
    private Integer unsettledOrderCount;
    @ApiModelProperty(value = "昨日营业额")
    private BigDecimal yesterdayBusinessFee;
    @ApiModelProperty(value = "昨日订单数")
    private Integer yesterdayOrderCount;
    @ApiModelProperty(value = "内部使用字段",hidden = true)
    private int model;

    {
        this.businessFee = BigDecimal.ZERO;
        this.orderCount = BigDecimal.ZERO.intValue();
        this.unsettledBusinessFee = BigDecimal.ZERO;
        this.unsettledOrderCount = BigDecimal.ZERO.intValue();
        this.yesterdayBusinessFee = BigDecimal.ZERO;
        this.yesterdayOrderCount = BigDecimal.ZERO.intValue();
    }
}
