package com.holderzone.saas.store.dto.order.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.request.TakeAwayBillPayCreateDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderCreatRespDTO
 * @date 2018/09/04 16:46
 * @description
 * @program holder-saas-store-order
 */
@Data
public class OrderCreatRespDTO extends BaseDTO{

    @ApiModelProperty(value = "反结账uuid")
    private String recoveryUuid;

    @ApiModelProperty(value = "结账人")
    private String checkoutStaffGuid;

    @ApiModelProperty(value = "结账人")
    private String checkoutStaffName;

    @ApiModelProperty(value = "订单的guid", required = true)
    private String orderGuid;

    @ApiModelProperty(value = "如果账单更新，需要传入")
    private String billGuid;

    @ApiModelProperty(value = "门店guid", required = true)
    @NotBlank(message = "门店guid不能为空")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称", required = true)
    @NotBlank(message = "门店名称不能为空")
    private String storeName;

    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "牌号")
    private String mark;

    @ApiModelProperty(value = "营业日")
    private LocalDate businessDay;

    @ApiModelProperty(value = "桌台guid（仅堂食）")
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台名字（仅堂食）")
    private String diningTableName;

    @ApiModelProperty(value = "交易模式：0:堂食,1:快餐,2:外卖", required = true)
    private int tradeMode;

    @ApiModelProperty(value = "订单来源：一体机点餐下单（1）、POS设备（2）、M1（3）、平板点餐（4）、微信公众号（5）、美团外卖（6）饿了么外卖（7）", required = true)
    private Integer orderSource;

    @ApiModelProperty(value = "就餐人数（仅堂食）")
    private int guestCount;

    @ApiModelProperty(value = "外卖账单信息")
    private TakeAwayBillPayCreateDTO takeAwayBillPayCreateDTO;

    @ApiModelProperty(value = "菜品guid")
    private List<OrderCreatRespDTO.OrderDish> orderDishes;

    @Data
    public static class OrderDish {

        /**
         * 订单菜品guid
         */
        @ApiModelProperty(value = "订单菜品guid")
        private String orderDishGuid;

        /**
         * 创建时间
         */
        @ApiModelProperty(value = "创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime gmtCreate;

        /**
         * 菜品guid
         */
        @ApiModelProperty(value = "菜品guid")
        private String dishGuid;

        /**
         * 菜品名称
         */
        @ApiModelProperty(value = "菜品名称")
        private String dishName;

        @ApiModelProperty(value = "是否已经下单")
        private Integer isPay;

        /**
         * 菜品编号
         */
        @ApiModelProperty(value = "菜品编号")
        private String code;

        @ApiModelProperty(value = "菜品类型guid")
        private String dishTypeGuid;

        @ApiModelProperty(value = "菜品类型名称")
        private String dishTypeName;

        @ApiModelProperty(value = "主菜品业务id")
        private String appMainDishId;

        /**
         * 套餐主项guid
         */
        @ApiModelProperty(value = "套餐主项orderDishGuid")
        private String parentDishGuid;

        /**
         * 菜品的套餐类型(0：单规格、1：多规格、2：套餐主项、3：套餐子项、4：称重菜品 )
         */
        @ApiModelProperty(value = "菜品的套餐类型(0：单规格、1：多规格、2：套餐主项、3：套餐子项、4：称重菜品 )")
        private Byte packageDishType;

        /**
         * 菜品的规格
         */
        @ApiModelProperty(value = "菜品的规格")
        private String skuGuid;

        /**
         * 规格名称
         */
        @ApiModelProperty(value = "规格名称")
        private String skuName;

        /**
         * sku价格
         */
        @ApiModelProperty(value = "sku价格")
        private BigDecimal price;

        /**
         * 会员价格
         */
        @ApiModelProperty(value = "会员价格")
        private BigDecimal memberPrice;

        /**
         * 数量
         */
        @ApiModelProperty(value = "数量")
        private BigDecimal orderCount;

        /**
         * 计数单位
         */
        @ApiModelProperty(value = "计数单位")
        private String unit;

        /**
         * 计数单位code(DishUnitEnum)
         */
        @ApiModelProperty(value = "计数单位code(DishUnitEnum)")
        private Integer unitCode;

        /**
         * 属性总价
         */
        @ApiModelProperty(value = "属性总价")
        private BigDecimal skuPropertyTotal;

        /**
         * 是否赠送(0:非赠送、1：赠送)
         */
        @ApiModelProperty(value = "是否赠送(0:非赠送、1：赠送)")
        private Byte isGift;

        /**
         * 是否参与权益折扣
         */
        @ApiModelProperty(value = "是否参与权益折扣")
        private Byte joinMemberDiscount;

        /**
         * 是否参与整单折扣
         */
        @ApiModelProperty(value = "是否参与整单折扣")
        private Byte joinWholeDiscount;

        /**
         * 菜品备注
         */
        @ApiModelProperty(value = "菜品备注")
        private String remark;

        private List<OrderCreatRespDTO.SkuProperty> skuProperties;
    }

    @Data
    public static class SkuProperty {

        /**
         * 订单菜品guid
         */
        private String orderDishGuid;

        @ApiModelProperty(value = "订单属性guid")
        private String skuPropertyGuid;

        @ApiModelProperty(value = "属性guid", required = true)
        private String propertyDishGuid;

        /**
         * 属性guid
         */
        @ApiModelProperty(value = "属性guid")
        private String propertyGuid;

        @ApiModelProperty(value = "属性名称")
        private String propertyDishName;

        /**
         * 属性名称
         */
        @ApiModelProperty(value = "属性名称")
        private String propertyName;

        /**
         * 属性组名称
         */
        private String propertyGroupName;

        /**
         * 属性价格
         */
        @ApiModelProperty(value = "属性价格")
        private BigDecimal propertyPrice;

        @ApiModelProperty(value = "属性数量")
        private Integer num;

    }
}
