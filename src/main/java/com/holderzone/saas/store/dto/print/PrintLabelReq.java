package com.holderzone.saas.store.dto.print;

import com.holderzone.saas.store.dto.common.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 重打印标签请求
 */
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PrintLabelReq extends BaseDTO {

    @NotNull(message = "订单Guid不能为空")
    private String orderGuid;

    /**
     * 可打印商品集合
     */
    @NotEmpty
    @Valid
    private List<InnerPrintLabelDetailsReq> printItemList;

    @Data
    public static class InnerPrintLabelDetailsReq implements Serializable {

        private static final long serialVersionUID = -2759574848238765454L;

        @NotNull(message = "订单商品明细guid不能为空")
        private String orderItemGuid;

        @NotNull(message = "打印数量不能为空")
        private BigDecimal count;
    }
}
