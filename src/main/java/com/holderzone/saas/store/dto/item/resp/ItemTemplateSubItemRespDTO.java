package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateSubItemRespDTO
 * @date 2019/06/01 16:27
 * @description //TODO 商品模板新增菜单获取全部sku商品列表
 * @program holder-saas-aggregation-app
 */
@Data
@ApiModel(value = "sku商品列表")
public class ItemTemplateSubItemRespDTO {
    @ApiModelProperty(value = "商品guid")
    private String guid;
    @ApiModelProperty(value = "sku guid")
    private String skuGuid;
    @ApiModelProperty(value = "商品名称")
    private String name;
    @ApiModelProperty(value = "商品sku名称")
    private String skuName;
    @ApiModelProperty(value = "单位")
    private String unit;
    @ApiModelProperty(value = "商品分类guid")
    private String typeGuid;
    @ApiModelProperty(value = "商品分类名称")
    private String typeName;
    @ApiModelProperty(value = "商品sku售价")
    private BigDecimal salePrice;
    @ApiModelProperty(value = "商品类型")
    private Integer itemType;
}
