package com.holderzone.saas.store.dto.invoice.ipass;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@ApiModel(description = "生成认证二维码")
@Data
public class IpassAuthenticationResultUrlDTO implements Serializable {

    @ApiModelProperty(value = "发送短信手机号")
    private String storeId;

    @ApiModelProperty(value = "订单流水号")
    private String orderNo;

    @ApiModelProperty(value = "业务类型代码")
    private String ywlxdm;

    @ApiModelProperty(value = "登录账号")
    private String dlzh;

    @ApiModelProperty(value = "如果ewmlx是2 返回的是base64文件流")
    private String ewm;

    @ApiModelProperty(value = "纳税人识别号")
    private String nsrsbh;

    @ApiModelProperty(value = "实人认证id")
    private String rzid;

    @ApiModelProperty(value =
            "状态" +
            "0成功" +
            "-1失败" +
            "1004 需要登录")
    private String status;

    @ApiModelProperty(value = "异常原因 如果status为-1 会返回具体失败原因")
    private String ycyy;
}
