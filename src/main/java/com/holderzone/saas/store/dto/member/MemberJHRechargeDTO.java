package com.holderzone.saas.store.dto.member;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberJHRechargeDTO
 * @date 2018/08/21 14:09
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class MemberJHRechargeDTO extends BaseDTO {

    /**
     * 商品名称（是）
     */
    @ApiModelProperty("本次订单交易的商品名称！！")
    private String goodsName;

    /**
     * 订单描述（是）
     */
    @ApiModelProperty(value = "对应订单的描述！！")
    private String body;

    /**
     * 支付功能ID（是）
     */
    @ApiModelProperty(value = "详细见文档")
    private String payPowerId;

    /**
     * 订单附加描述（否）
     */
    @ApiModelProperty(value = "对应订单附加描述！!")
    private String description;

    /**
     * 附加数据（否）
     */
    @ApiModelProperty(value = "对应订单附加数据！！")
    private String extra;

    /**
     * 商户分发渠道（否）
     * 商户自定义来标记该笔订单属于的渠道方（支付平台会记录该标记，但不做任何处理）
     */
    @ApiModelProperty(value = "商户自定义来标记该笔订单属于的渠道方（支付平台会记录该标记，但不做任何处理）")
    private String cpChannel;

    /**
     * 默认币种（否）
     * RMB
     */
    @ApiModelProperty(value = "交易币种，默认RMB")
    private String currency = "RMB";

    /**
     * 发起条码支付的授权码
     */
    @ApiModelProperty(value = "发起条码支付的授权码，当微信、支付宝条码支付时候，必填！！")
    private String authCode;

    /**
     * 设备终端号（否）
     * 发起条码支付时候必填
     */
    @ApiModelProperty(value = "设备终端号,微信、支付宝条码支付时候，此参数必填！！")
    private String terminalId;

    /**
     * 商户公众号APPID（否）
     */
    @ApiModelProperty(value = "商户公众号APPID！！")
    private String subAppId;

    /**
     * 用户在微信公众号或者微信小程序下的唯一标识（否）
     */
    @ApiModelProperty(value = "用户在微信公众号或者微信小程序下的唯一标识！！")
    private String subOpenId;

    /**
     * 场景信息，商户对接微信h5时候必填（否）
     */
    @ApiModelProperty(value = "场景信息，商户对接微信h5时候必填！！")
    private String sceneInfo;

    /**
     * 用户在京东、支付宝平台唯一 id（否）
     * （支付宝固定二维码、京东支付用到）
     * 如果不传，则返回授权地址authUrl
     */
    @ApiModelProperty(value = "用户在京东、支付宝平台唯一 id，（支付宝固定二维码、京东支付用到，如果不传，则返回授权地址authUrl）！！")
    private String openId;

    @ApiModelProperty(value = "签名")
    private String signature;

    @ApiModelProperty(value = "支付金额必传",required = true)
    private BigDecimal amount;

}
