package com.holderzone.saas.store.dto.business.manage.sync;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 计算附加费实体
 * @date 2022/3/25 16:36
 * @className: SurchargeCalculateDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "计算附加费实体")
public class SurchargeCalculateDTO implements Serializable {

    private static final long serialVersionUID = -9198817353958202687L;

    /**
     * 客人数量
     */
    @ApiModelProperty(value = "客人数量", required = true)
    private Integer guestCount;

    @ApiModelProperty(value = "桌台Guid", required = true)
    private String tableGuid;

    @ApiModelProperty(value = "区域Guid")
    private String areaGuid;

    @ApiModelProperty(value = "企业GUID", required = true)
    private String enterpriseGuid;

    /**
     * 场景：0=正餐，1=快餐
     */
    @ApiModelProperty(value = "场景：0=正餐，1=快餐")
    private Integer tradeMode;
}
