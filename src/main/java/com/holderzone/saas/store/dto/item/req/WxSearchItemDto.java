package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Setter;
import org.springframework.util.StringUtils;

/**
 * 微信扫描点餐搜索商品dto
 *
 * <AUTHOR>
 * @date 2021/2/24 14:53
 */
@Setter
public class WxSearchItemDto extends BasePageDTO {

    @ApiModelProperty(value = "搜索关键字")
    private String keywords;

    /**
     * 关键字拆分模糊搜索
     *
     * @return 关键字
     */
    public String getKeywords() {
        if (!StringUtils.isEmpty(keywords)) {
            StringBuilder result = new StringBuilder("%");
            String[] words = keywords.split("");
            for (String word : words) {
                result.append(word).append("%");
            }
            return result.toString();
        }
        return keywords;
    }
}
