package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className AuthorityDO
 * @date 20-12-18 上午10:32
 * @description hss_authority DO
 * @program holder-saas-store-staff
 */

@Accessors(chain = true)
@ApiModel("用户权限保存请求实体")
public class AuthorityDTO implements Serializable {

    /**
     * guid
     */
    @ApiModelProperty(value = "权限id")
    private Integer id;

    /**
     * 终端code
     */
    @ApiModelProperty(value = "终端code")
    private String terminalCode;

    /**
     * 终端名称
     */
    @ApiModelProperty(value = "终端名称")
    private String terminalName;

    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String sourceName;

    /**
     * 资源code
     */
    @ApiModelProperty(value = "资源code")
    private String sourceCode;

    /**
     * 资源url
     */
    @ApiModelProperty(value = "资源url")
    private String sourceUrl;


    public AuthorityDTO(Integer id, String terminalCode, String terminalName, String sourceName, String sourceCode, String sourceUrl) {
        this.id = id;
        this.terminalCode = terminalCode;
        this.terminalName = terminalName;
        this.sourceName = sourceName;
        this.sourceCode = sourceCode;
        this.sourceUrl = sourceUrl;
    }

    public AuthorityDTO(String terminalCode, String terminalName, String sourceName, String sourceCode, String sourceUrl) {
        this.terminalCode = terminalCode;
        this.terminalName = terminalName;
        this.sourceName = sourceName;
        this.sourceCode = sourceCode;
        this.sourceUrl = sourceUrl;
    }

    public AuthorityDTO(String terminalName, String sourceName, String sourceCode, String sourceUrl) {
        this.terminalName = terminalName;
        this.sourceName = sourceName;
        this.sourceCode = sourceCode;
        this.sourceUrl = sourceUrl;
    }

    public AuthorityDTO(String sourceName, String sourceCode, String sourceUrl) {
        this.sourceName = sourceName;
        this.sourceCode = sourceCode;
        this.sourceUrl = sourceUrl;
    }

    public AuthorityDTO(String sourceCode, String sourceUrl) {
        this.sourceCode = sourceCode;
        this.sourceUrl = sourceUrl;
    }


    public AuthorityDTO(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTerminalCode() {
        return terminalCode;
    }

    public void setTerminalCode(String terminalCode) {
        this.terminalCode = terminalCode;
    }

    public String getTerminalName() {
        return terminalName;
    }

    public void setTerminalName(String terminalName) {
        this.terminalName = terminalName;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getSourceUrl() {
        return sourceUrl;
    }

    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }

    public AuthorityDTO() {

    }

}
