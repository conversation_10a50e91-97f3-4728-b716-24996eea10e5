package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 订单
 * </p>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class OrderTableDTO extends OrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 0 单台 1 联台 2 并台
     */
    private Integer orderTableType;

    /**
     * 桌台guid
     */
    private List<OrderTableGuestDTO> orderTableGuestDTOS;

    /**
     * 联台下桌台guid
     */
    private List<String> tableGuidList;

    /**
     * 主台
     */
    private String tableGuid;

    /**
     * 联台人数
     */
    private Integer guestCount;
}