package com.holderzone.saas.store.dto.report.eum;

import com.holderzone.saas.store.dto.report.OrderDishDetailReportDTO;
import com.holderzone.saas.store.dto.report.OrderDishReportDTO;
import com.holderzone.saas.store.dto.report.OrderDishTypeInfoReportDTO;
import com.holderzone.saas.store.dto.report.OrderRecoveryDTO;
import com.holderzone.saas.store.dto.report.query.*;
import com.holderzone.saas.store.dto.report.resp.*;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExportTypeEnum
 * @date 2018/10/11 10:07
 * @description
 * @program holder-saas-store-dto
 */
public enum ExportTypeEnum {

    DISCOUNT_TYPE(0, DiscountRespDTO.DiscountResp.class, DiscountQueryDTO.class, "discountName:优惠方式名称,amount:优惠金额(元)","优惠方式统计报表"),

    DISCOUNT_TYPE_DETAIL(1, DiscountDetailRespDTO.class, DiscountQueryDTO.class, "orderGuid:订单号,discountName:优惠方式名称,discountFee:优惠金额(元),discountTime:折扣时间,storeName:门店名称,tradeMode:销售类型," +
            "staffName:员工姓名","优惠方式明细报表"),

    PAY_TYPE(2, PaymentTypeRespDTO.PaymentTypeResp.class, PayTypeCountQueryDTO.class, "paymentName:支付方式名称,amount:支付金额(元)","支付方式报表"),

    PAY_TYPE_DETAIL(3, PayDetailRespDTO.class, PayTypeCountQueryDTO.class, "orderGuid:订单号,paymentName:支付方式名称,billGuid:交易流水号,thirdOrderNo:第三方交易流水号,amount:支付金额(元)," +
            "checkTime:结账时间,storeName:门店名称,tradeMode:销售类型,staffName:员工姓名","支付方式详情报表"),

    HANDOVER(4, HandoverRespDTO.class, HandOverQueryDTO.class, "storeName:门店名称,terminalId:系统设备编号,createNameAndGuid:开班人姓名/账号,gmtCreate:开班时间,handoverTime:交班时间,duration:当班时长," +
            "businessIncoming:当班营业额,state:交班状态","交接班报表"),

    ORDER_TRANS_TYPE(5, OrderTransRecordRespDTO.class, OrderTransRecordQueryDTO.class, "orderGuid:订单号,storeName:门店名称,tradeMode:销售类型,checkoutTimestamp:结账时间,orderFee:订单金额（元）," +
            "totalDiscountFee:优惠金额（元）,actuallyPayFee:实付金额（元）,operationStaffName:收银员姓名","订单交易记录报表"),

    ORDER_CANCEL_TYPE(6, OrderTransRecordRespDTO.class, OrderTransRecordQueryDTO.class, "orderGuid:订单号,storeName:门店名称,tradeMode:销售类型,cancelTimestamp:作废时间,orderFee:订单金额（元）," +
            "totalDiscountFee:优惠金额（元）,actuallyPayFee:实付金额（元）,cancelStaffName:作废员工姓名","订单作废记录报表"),


    SALE_DISH(7, OrderDishReportDTO.class, OrderDishQueryDTO.class,
            "dishCode:菜品编号,dishName:菜品名称,dishType:菜品分类,packageDish:菜品类型,dishCount:销售数量,dishAmount:销售金额,dishAmountRatio:销售额占比","菜品销售报表"),
    GIFT_DISH(8, OrderDishReportDTO.class, OrderDishQueryDTO.class,
            "dishCode:菜品编号,dishName:菜品名称,dishType:菜品分类,packageDish:菜品类型,dishCount:赠送数量,dishAmount:赠送金额,dishAmountRatio:金额占比","菜品赠送报表"),
    SALE_DISH_DETAIL(9, OrderDishDetailReportDTO.class, OrderDishDetailQueryDTO.class,
            "dishCode:菜品编号,dishName:菜品名称,dishType:菜品分类,packageDish:菜品类型,salePrice:售卖价,dishCount:销售总量," +
                    "orderNo:订单号,checkoutTimestamp:结账时间,storeName:门店名称,saleType:销售类型,checkoutStaffName:员工姓名","菜品销售明细报表"),
    GIFT_DISH_DETAIL(10, OrderDishDetailReportDTO.class, OrderDishDetailQueryDTO.class,
            "dishCode:菜品编号,dishName:菜品名称,dishType:菜品分类,packageDish:菜品类型,salePrice:售卖价,dishCount:赠送数量," +
                    "orderNo:订单号,checkoutTimestamp:结账时间,storeName:门店名称,saleType:销售类型,checkoutStaffName:员工姓名","菜品赠送明细报表"),
    SALE_DISH_TYPE_INFO(11, OrderDishTypeInfoReportDTO.class, OrderDishTypeInfoQueryDTO.class,
            "dishType:菜品分类,saleAmount:销售总额,saleAmountRatio:销售额占比","菜品销售分类统计报表"),
    SALE_DISH_RETURN(12, OrderDishReportDTO.class, OrderDishQueryDTO.class,
            "dishCode:菜品编号,dishName:菜品名称,dishType:菜品分类,packageDish:菜品类型,dishCount:退菜总量,dishAmount:退菜总额,dishAmountRatio:金额占比","菜品退菜报表"),
    SALE_DISH_RETURN_DETAIL(13, OrderDishDetailReportDTO.class, OrderDishDetailQueryDTO.class,
            "dishCode:菜品编号,dishName:菜品名称,dishType:菜品分类,packageDish:菜品类型,salePrice:售卖价,dishCount:退菜数量," +
                    "orderNo:订单号,checkoutTimestamp:结账时间,storeName:门店名称,saleType:销售类型,checkoutStaffName:员工姓名","菜品退菜明细报表"),
    ORDER_RECOVERY_TYPE(14, OrderRecoveryDTO.class, OrderRecoveryQueryDTO.class, "orderGuid:订单号,storeName:门店名称,tradeMode:销售类型,checkoutTimestamp:反结账时间,operationStaffName:反结账员工姓名","反结账订单报表");


    private Integer type;

    private Class<?> resultClzz;

    private Class<?> queryClzz;

    private String head;

    private String name;

    ExportTypeEnum(Integer type, Class<?> resultClzz, Class<?> queryClzz, String head,String name) {
        this.type = type;
        this.resultClzz = resultClzz;
        this.queryClzz = queryClzz;
        this.head = head;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public Class<?> getResultClzz() {
        return resultClzz;
    }

    public void setResultClzz(Class<?> resultClzz) {
        this.resultClzz = resultClzz;
    }

    public Class<?> getQueryClzz() {
        return queryClzz;
    }

    public void setQueryClzz(Class<?> queryClzz) {
        this.queryClzz = queryClzz;
    }

    public static String getHeadByType(Integer type) {
        return Arrays.stream(ExportTypeEnum.values())
                .filter(exportTypeEnum -> Objects.equals(type, exportTypeEnum.type))
                .findFirst()
                .map(ExportTypeEnum::getHead)
                .orElse(null);
    }

    public static Class<?> getResultClzzByType(Integer type) {
        return Arrays.stream(ExportTypeEnum.values())
                .filter(exportTypeEnum -> Objects.equals(type, exportTypeEnum.type))
                .findFirst()
                .map(ExportTypeEnum::getResultClzz)
                .orElse(null);
    }

    public static Class<?> getQueryClzzByType(Integer type) {
        return Arrays.stream(ExportTypeEnum.values())
                .filter(exportTypeEnum -> Objects.equals(type, exportTypeEnum.type))
                .findFirst()
                .map(ExportTypeEnum::getQueryClzz)
                .orElse(null);
    }

}
