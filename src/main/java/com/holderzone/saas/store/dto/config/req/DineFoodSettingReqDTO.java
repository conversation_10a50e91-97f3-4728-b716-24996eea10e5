package com.holderzone.saas.store.dto.config.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/13
 * @description 正餐配置请求
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "正餐配置请求")
public class DineFoodSettingReqDTO implements Serializable {

    private static final long serialVersionUID = 6510713452343836351L;

    @NotBlank(message = "门店guid不能为空")
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 手动清台：0关闭 1开启
     */
    @ApiModelProperty(value = "手动清台：0关闭 1开启")
    private Integer enableHandleClose;

    /**
     * 同一订单多次使用聚合支付：0关闭 1开启
     */
    @ApiModelProperty(value = "同一订单多次使用聚合支付：0关闭 1开启")
    private Integer enableMultiplePay;

    /**
     * 正餐支持换高价值或低价值的菜 0不支持 1支持
     */
    @ApiModelProperty(value = "正餐支持换高价值或低价值的菜 0不支持 1支持")
    private Integer enableDineInChangeDiffValue;

    /**
     * 快餐餐支持换高价值或低价值的菜 0不支持 1支持
     */
    @ApiModelProperty(value = "快餐餐支持换高价值或低价值的菜 0不支持 1支持")
    private Integer enableFastChangeDiffValue;
}
