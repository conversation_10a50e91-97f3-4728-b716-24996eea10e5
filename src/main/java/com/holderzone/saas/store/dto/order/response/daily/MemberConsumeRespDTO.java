package com.holderzone.saas.store.dto.order.response.daily;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberConsumeRespDTO
 * @date 2019/02/15 14:36
 * @description 营业日报-会员消费统计
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class MemberConsumeRespDTO {
    @ApiModelProperty(value = "消费单数")
    private Integer consumerCount;
    @ApiModelProperty(value = "消费金额")
    private BigDecimal consumerAmount;
    @ApiModelProperty(value = "充值单数")
    private Integer prepaidCount;
    @ApiModelProperty(value = "充值金额")
    private BigDecimal prepaidAmount;
    @ApiModelProperty(value = "充值赠送")
    private BigDecimal prepaidGiveAmount;
    @ApiModelProperty(value = "充值收入")
    private BigDecimal prepaidTakeInAmount;
    @ApiModelProperty(value = "充值收入项")
    private List<AmountItemDTO> prepaidItems;
}