package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "价格方案列表查询请求对象")
@Data
public class PricePlanPageReqDTO extends PageDTO {

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "方案名称或方案guid")
    private String nameOrGuid;

    @ApiModelProperty(value = "方案状态-0未启用 1已启用 2暂不启用 3永久停用 4即将启用")
    private Integer status;

    @ApiModelProperty(value = "需排除菜谱方案Guid")
    private List<String> filterGuids;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "包含的菜谱方案Guid")
    private List<String> includeGuids;
}
