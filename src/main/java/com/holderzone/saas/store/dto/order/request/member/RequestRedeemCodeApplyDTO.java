package com.holderzone.saas.store.dto.order.request.member;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description = "优惠兑换码活动表")
@Data
public class RequestRedeemCodeApplyDTO implements Serializable {

    private String enterpriseGuid;

    private String operSubjectGuid;

    @ApiModelProperty(value = "手机号")
    private String phoneNum;

    @ApiModelProperty(value = "memberInfoGuid")
    private String memberInfoGuid;

    @ApiModelProperty(value = "会员名称")
    private String memberName;

    @ApiModelProperty(value = "兑换码")
    private String redeemCodeVal;

    @ApiModelProperty(value = "来源")
    private Integer source;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;
}
