package com.holderzone.saas.store.dto.invoice.ipass;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@ApiModel(description = "生成订单商品明细")
@Data
public class IpassOrderItemsDTO implements Serializable {

    @ApiModelProperty(value = "单价（含税）")
    private String dj;

    @ApiModelProperty(value = "金额（含税）")
    private String je;

    @ApiModelProperty(value = "税率标识\n" +
            "0 是正常税率\n" +
            "1是免税\n" +
            "2是不征税\n" +
            "3普通零税率")
    private String lslbs;

    @ApiModelProperty(value = "税额 保留2位小数")
    private String se;

    @ApiModelProperty(value = "数量 保留6位小数")
    private String sl;

    @ApiModelProperty(value = "税率 最多3位小数")
    private String slv;

    @ApiModelProperty(value = "商品编码 对应百旺，核销子系统-系统管理-商品管理中内容 eg: 3070401000000000000")
    private String spbm;

    @ApiModelProperty(value = "商品名称 eg: 餐饮服务")
    private String spmc;
}
