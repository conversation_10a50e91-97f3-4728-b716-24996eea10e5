package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableBasicQueryDTO
 * @date 2019/01/03 9:21
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class TableBasicQueryDTO extends BaseDTO {

    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("区域guid")
    private String areaGuid;

    @ApiModelProperty("桌台信息集合")
    private List<String> tableGuidList;

    @ApiModelProperty("是否查询桌台")
    private Boolean queryTableFlag;
}
