package com.holderzone.saas.store.dto.print.content;

import com.holderzone.holder.saas.member.terminal.dto.member.response.MemberPortrayalDetailsDTO;
import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.dto.print.content.base.TradeModeAware;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

import static com.holderzone.saas.store.dto.print.util.PrintMockUtils.*;

/**
 * 菜品清单
 *
 * <AUTHOR>
 * @date 2018/09/19 15:28
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("菜品清单")
public class PrintItemDetailDTO extends PrintBaseItemDTO implements TradeModeAware, PrintDataMockito {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名")
    private String storeName;

    @Nullable
    @ApiModelProperty(value = "整单备注")
    private String orderRemark;

    @NotBlank(message = "桌位或牌号不能为空")
    @ApiModelProperty(value = "桌位或牌号（号牌名称：正餐'桌位：'，快餐'牌号：'）", required = true)
    private String markNo;

    @NotBlank(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    private String orderNo;

    @NotNull(message = "就餐人数不得为空")
    @ApiModelProperty(value = "就餐人数", required = true)
    private Integer personNumber;

    @NotNull(message = "开台时间不能为空")
    @ApiModelProperty(value = "开台时间", required = true)
    private Long openTableTime;

    @NotNull(message = "商品总额不能为空")
    @ApiModelProperty(value = "商品总额", required = true)
    private BigDecimal total;

    @ApiModelProperty(value = "商品折后总额", required = true)
    private BigDecimal discountAfterTotal;

    @ApiModelProperty(value = "会员画像", required = true)
    private MemberPortrayalDetailsDTO memberPortrayalDTO;

    @Valid
    @Nullable
    @ApiModelProperty(value = "附加费", required = true)
    private List<AdditionalCharge> additionalChargeList;

    @Override
    public void applyMock() {
        super.applyMock();
        setStoreName(mockStoreName());
        setOrderRemark(mockOrderRemark());
        setMarkNo(mockMarkNo());
        setOrderNo(mockOrderNo());
        setPersonNumber(mockPersonNumber());
        setOpenTableTime(mockOpenTableTime());
        setTotal(mockTotal());
        setMemberPortrayalDTO(mockMemberPortrayalDTO());
        setAdditionalChargeList(mockAdditionalChargeList());
    }
}
