package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtMaitonConsumeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@ApiModel
public class CouponDelReqDTO extends BaseDTO {


    /**
     * 券码
     */
    @NotNull
    @ApiModelProperty(value = "券码,12位纯数字")
    private String couponCode;

    /**
     * 商家登录ERP账号ID
     */
    @NotBlank
    private String erpId;

    /**
     * 商家登录erp账号名称
     */
    @NotBlank
    private String erpName;

    /**
     * 券码渠道 买单:1004
     */
    private Integer receiptChannel;

    /**
     * 一键买单券信息
     */
    private MtMaitonConsumeDTO maitonConsumeDTO;

}
