package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreAuthorizationDTO
 * @date 2018/09/15 16:04
 * @description
 * @program holder-saas-store-dto
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class StoreAuthorizationDTO implements Serializable {

    @ApiModelProperty(value = "erp企业id")
    private String erpEnterpriseGuid;

    @ApiModelProperty(value = "erp门店id")
    private String erpStoreId;

    @ApiModelProperty(value = "erp门店名称")
    private String erpStoreName;

    @ApiModelProperty(value = "erp门店编号")
    private String StoreNumber;

    @ApiModelProperty(value = "外卖门店id")
    private String takeawayStoreId;

    @ApiModelProperty(value = "外卖门店名称")
    private String takeawayStoreName;

    @ApiModelProperty(value = "绑定状态 1：已绑定，0：未绑定")
    private Integer bindingStatus;

    @ApiModelProperty(value = "外卖类型 1：美团，2：饿了么")
    private Integer takeawayType;

    @ApiModelProperty(value = "查询字段")
    private String queryString;
}
