/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:TakeoutTcdRespDTO.java
 * Date:2020-3-2
 * Author:terry
 */

package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020-03-02 下午2:06
 */
@Data
public class TakeoutTcdRespDTO extends TcdCommonRespDTO {
    private static final long serialVersionUID = 6784968432096220916L;

    @ApiModelProperty(value = "data")
    private TakeoutOwnBindRespDTO data;

}
