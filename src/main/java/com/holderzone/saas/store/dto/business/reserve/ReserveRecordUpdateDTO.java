package com.holderzone.saas.store.dto.business.reserve;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordDO
 * @date 2018/07/30 下午2:51
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class ReserveRecordUpdateDTO {

    /**
     * 预订记录guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "预订记录guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "预订记录guid", required = true)
    private String reserveRecordGuid;

    /**
     * 预订时段guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "预订时段guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "预订时段guid", required = true)
    private String reservePeriodGuid;

    /**
     * 客人名字
     */
    @NotNull
    @Size(min = 1, max = 45, message = "客人名字不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "客人名字", required = true)
    private String guestName;

    /**
     * 客人性别
     * 0=女性
     * 1=男性
     */
    @NotNull
    @Min(value = 0, message = "客人性别：0=女性，1=男性")
    @Max(value = 1, message = "客人性别：0=女性，1=男性")
    @ApiModelProperty(value = "客人性别", required = true)
    private Integer guestGender;

    /**
     * 客人电话
     */
    @NotNull
    @Size(min = 1, max = 45, message = "客人电话不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "客人电话", required = true)
    private String guestTel;

    /**
     * 客人备注
     */
    @ApiModelProperty(value = "客人备注")
    private String guestRemark;

    /**
     * 客人数量
     */
    @NotNull
    @ApiModelProperty(value = "客人数量", required = true)
    private Integer guestCount;

    /**
     * 客人抵达时间
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "客人抵达时间", required = true)
    private LocalDateTime arrivalTime;

    /**
     * 预订保留时间
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "预订保留时间", required = true)
    private LocalDateTime deadlineTime;

    /**
     * 营业日日期
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "营业日日期", required = true)
    private LocalDate businessDay;
}
