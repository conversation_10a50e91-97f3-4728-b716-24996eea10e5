package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 用于菜谱方案导入商品展示
 * @date 2021/3/22 17:02
 */
@Data
@ApiModel(value = "用于菜谱方案导入商品展示")
public class TypeItemRespDTO implements Serializable {

    private static final long serialVersionUID = 5566325425916932142L;

    @ApiModelProperty(value = "分类GUID")
    private String typeGuid;

    @ApiModelProperty(value = "分类名称")
    private String name;

    @ApiModelProperty(value = "商品集合")
    private List<ItemWebRespDTO> itemWebRespDTOList;

}
