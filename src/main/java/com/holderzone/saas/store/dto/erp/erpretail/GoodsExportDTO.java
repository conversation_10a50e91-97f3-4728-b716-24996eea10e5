package com.holderzone.saas.store.dto.erp.erpretail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

@Data
@ApiModel("出入库商品明细")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoodsExportDTO {

    @ApiModelProperty("商品guid")
    private String goodsGuid;

    @ApiModelProperty("安全库存")
    private BigDecimal safeNum;

    @ApiModelProperty("出入库数量")
    @NotEmpty(message = "出入库数量不得为空")
    private BigDecimal count;
}
