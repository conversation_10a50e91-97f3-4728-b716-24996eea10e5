package com.holderzone.saas.store.dto.takeaway;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class UnRefundItem implements Serializable {

    private static final long serialVersionUID = 4916810505687055138L;

    /**
     * 菜品名称
     */
    private String itemName;

    /**
     * 单位
     */
    private String itemUnit;

    /**
     * 单价
     */
    private BigDecimal itemPrice;

    /**
     * 菜品数量
     */
    private BigDecimal itemCount;

    /**
     * 平台商品实际数量
     */
    private BigDecimal actualItemCount;

    /**
     * 菜品小计
     */
    private BigDecimal itemTotal;

    /**
     * 菜品规格 多元素使用"",""分割开
     */
    private String itemSpec;

    /**
     * 特殊属性 多元素使用"",""分割开
     */
    private String itemProperty;

    /**
     * 餐盒单价
     */
    private BigDecimal boxPrice;

    /**
     * 餐盒数量
     */
    private BigDecimal boxCount;

    /**
     * 餐盒小计
     */
    private BigDecimal boxTotal;

    /**
     * 结算类型 0=普通消费菜品  1=赠送菜品
     */
    private Integer settleType;

    /**
     * 三方唯一标识
     */
    private String thirdSkuId;

    /**
     * 退款金额
     */
    private BigDecimal refundPrice;
}
