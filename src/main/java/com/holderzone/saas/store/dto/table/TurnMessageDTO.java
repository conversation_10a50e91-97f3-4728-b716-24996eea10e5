package com.holderzone.saas.store.dto.table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 转台消息实体
 * @date 2021/9/23 22:22
 * @className: public class TurnMessageDTO {
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "转台消息实体")
@Accessors(chain = true)
public class TurnMessageDTO implements Serializable {

    private static final long serialVersionUID = 6997131387815908066L;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String content;

    /**
     * 转台数据key
     * table_turn:旧桌台:新桌台
     */
    @ApiModelProperty(value = "转台数据key")
    private String redisKey;

    /**
     * 就餐人数
     */
    @ApiModelProperty("就餐人数")
    private Integer actualGuestsNo;

    /**
     * 交易订单号
     */
    @ApiModelProperty("订单guid")
    private String orderGuid;
}
