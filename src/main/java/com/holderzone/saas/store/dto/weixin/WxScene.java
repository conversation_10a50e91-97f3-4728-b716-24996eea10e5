package com.holderzone.saas.store.dto.weixin;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxScene
 * @date 2019/03/20 10:30
 * @description 微信二维码场景值参数
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("微信二维码场景值参数")
public class WxScene {

    @ApiModelProperty(value = "场景值ID", notes = "临时二维码时为32位非0整型，永久二维码时最大值为100000（目前参数只支持1--100000）")
    @JsonProperty(value = "scene_id")
    private String sceneId;

    @ApiModelProperty(value = "场景值ID（字符串形式的ID）", notes = "字符串类型，长度限制为1到64")
    @JsonProperty(value = "scene_str")
    private String sceneStr;
}
