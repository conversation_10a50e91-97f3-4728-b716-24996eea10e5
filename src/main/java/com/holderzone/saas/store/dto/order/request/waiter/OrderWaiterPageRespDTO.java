package com.holderzone.saas.store.dto.order.request.waiter;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> R
 * @date 2020/11/26 16:57
 * @description
 */
@Data
@ApiModel(value = "订单服务员分页查询返回参数DTO")
public class OrderWaiterPageRespDTO {
    @ApiModelProperty(value = "开台时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "桌台名称")
    private String diningTableName;

    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @ApiModelProperty("是否录入服务员（true：已录入；false：未录入）")
    private Boolean ifInputWaiter;
}
