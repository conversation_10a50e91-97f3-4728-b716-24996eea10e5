package com.holderzone.saas.store.dto.trade;

import com.holderzone.saas.store.dto.order.local.FreeReturnItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel
@Data
public class LocalDTO {

    @ApiModelProperty(value = "未结账订单")
    private List<OrderDTO> orderList;

    @ApiModelProperty(value = "菜品")
    private List<ItemDTO> orderItemList;

    @ApiModelProperty(value = "属性")
    private List<ItemAttrDTO> attrList;

    @ApiModelProperty(value = "赠送")
    private List<FreeReturnItemDTO> freeLists;

    @ApiModelProperty(value = "优惠")
    private List<DiscountDTO> discountList;

    @ApiModelProperty(value = "交易记录")
    private List<TransactionRecordDTO> tranList;

    @ApiModelProperty(value = "附加费")
    private List<AppendFeeDTO> appendFeeList;
}
