package com.holderzone.saas.store.dto.report;

/**
 * <AUTHOR>
 * @date 2018/10/16 上午 11:37
 * @description
 */
public class OrderDishReportDTO {

    /**
     * 菜品编码
     */
    private String dishCode;

    /**
     * 菜品名称
     */
    private String dishName;

    /**
     * 菜品类型
     */
    private String dishType;

    /**
     * 是否套餐
     */
    private String packageDish;

    /**
     * 菜品数量
     */
    private String dishCount;

    /**
     * 菜品金额
     */
    private String dishAmount;

    /**
     * 菜品金额占比
     */
    private String dishAmountRatio;

    public String getDishCode() {
        return dishCode;
    }

    public void setDishCode(String dishCode) {
        this.dishCode = dishCode;
    }

    public String getDishName() {
        return dishName;
    }

    public void setDishName(String dishName) {
        this.dishName = dishName;
    }

    public String getDishType() {
        return dishType;
    }

    public void setDishType(String dishType) {
        this.dishType = dishType;
    }

    public String getPackageDish() {
        return packageDish;
    }

    public void setPackageDish(String packageDish) {
        this.packageDish = packageDish;
    }

    public String getDishCount() {
        return dishCount;
    }

    public void setDishCount(String dishCount) {
        this.dishCount = dishCount;
    }

    public String getDishAmount() {
        return dishAmount;
    }

    public void setDishAmount(String dishAmount) {
        this.dishAmount = dishAmount;
    }

    public String getDishAmountRatio() {
        return dishAmountRatio;
    }

    public void setDishAmountRatio(String dishAmountRatio) {
        this.dishAmountRatio = dishAmountRatio;
    }
}
