package com.holderzone.saas.store.dto.map;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BuildingDTO
 * @date 2019/05/18 17:22
 * @description 楼信息DTO
 * @program holder-saas-store
 */
@Data
public class BuildingDTO {

    @JsonProperty(value = "name")
    private List<String> names;

    @JsonProperty(value = "type")
    private List<String> types;
}
