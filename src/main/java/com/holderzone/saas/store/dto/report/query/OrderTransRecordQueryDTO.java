package com.holderzone.saas.store.dto.report.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderTransRecordQueryDTO
 * @date 2018/09/27 13:50
 * @description 订单交易记录列表查询参数DTO
 * @program holder-saas-store-report
 */
@Data
@ApiModel
public class OrderTransRecordQueryDTO extends BaseQueryDTO{

    @ApiModelProperty(value = "订单来源 对应订单表字段 device_type设备类型 -1:全部 0: 1:   ")
    private int device_type;
    @ApiModelProperty(value = "订单号")
    private String order_no;
    @ApiModelProperty(value = "订单状态 0：未完成， 1：已完成， 2：已作废 ，3：已退款，4：反结账状态")
    private int state;

}
