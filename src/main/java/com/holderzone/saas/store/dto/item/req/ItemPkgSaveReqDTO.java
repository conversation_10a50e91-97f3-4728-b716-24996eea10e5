package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemSaveReqDTO
 * @date 2019/01/08 上午9:53
 * @description //正餐商品套餐新增的请求实体
 * @program holder-saas-store-dto
 */
@Data
public class ItemPkgSaveReqDTO extends ItemStringListDTO {

    @ApiModelProperty(value = "该套餐GUID")
    private String itemGuid;

    @ApiModelProperty(value = "分类GUID")
    @NotEmpty
    private String typeGuid;

    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "商品类型：（0：门店自己创建的商品，1：品牌自己创建的商品,2:被推送过来的商品）")
    private Integer itemFrom;

    @ApiModelProperty(value = "商品类型：1.套餐（不称重，无规格），2.规格商品 ，3.称重商品（单商品，称重） 4.单品  5：团队套餐")
    @NotNull
    private Integer itemType;

    @ApiModelProperty(value = "是否有属性:0 否 1 是")
    private Integer hasAttr;

    @ApiModelProperty(value = "商品名称")
    @Size(max = 40)
    @NotEmpty
    private String name;

    @ApiModelProperty(value = "拼音简码")
    @NotEmpty
    @Size(max = 40)
    private String pinyin;

    @ApiModelProperty(value = "商品名称简写（别名）")
    @Size(max = 40)
    private String nameAbbr;

    @ApiModelProperty(value = "排序")
    @Max(99999999)
    private Integer sort;

    @ApiModelProperty(value = "商品主图路径")
    private String pictureUrl;

    @ApiModelProperty(value = "是否热销：0：否，1：是")
    @NotNull
    private Integer isBestseller;

    @ApiModelProperty(value = "是否新品：0：否，1：是")
    @NotNull
    private Integer isNew;

    @ApiModelProperty(value = "是否是招牌：0：否，1：是")
    @NotNull
    private Integer isSign;

    @ApiModelProperty(value = "商品描述")
    @Size(max = 200)
    private String description;

    @ApiModelProperty(value = "规格集合")
    @NotEmpty
    private List<@Valid SkuSaveReqDTO> skuList;
    @ApiModelProperty(value = "商品属性集合")
    @Size(max = 10,message = "最多可配置10组分组，请调整")
    @NotEmpty
    private List<@Valid SubgroupReqDTO> subgroupList;
}
