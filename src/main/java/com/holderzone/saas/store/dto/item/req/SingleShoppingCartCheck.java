package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class SingleShoppingCartCheck {

    @ApiModelProperty(value = "规格guid")
    private String skuGuid;

    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty(value = "售卖价格")
    @DecimalMin(value = "0.01")
    @DecimalMax(value = "99999.99")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "会员价格")
    @DecimalMin(value = "0.01")
    @DecimalMax(value = "99999.99")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "外卖价格")
    @DecimalMin(value = "0.01")
    @DecimalMax(value = "99999.99")
    private BigDecimal takeawayPrice;

    @ApiModelProperty(value = "外卖会员价格")
    @DecimalMin(value = "0.01")
    @DecimalMax(value = "99999.99")
    private BigDecimal takeawayMemberPrice;

    @ApiModelProperty(value = "划线价格")
    @DecimalMin(value = "0.01")
    @DecimalMax(value = "99999.99")
    private BigDecimal virtualPrice;

    @ApiModelProperty(value = "外卖打包费（冗余小程序端字段）")
    private BigDecimal takeawayPackageFee;

    @ApiModelProperty(value = "当前规格剩余库存（冗余小程序端字段）")
    private BigDecimal stock;

}
