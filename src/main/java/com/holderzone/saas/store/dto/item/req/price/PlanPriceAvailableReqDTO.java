package com.holderzone.saas.store.dto.item.req.price;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 批量编辑新增商品查询可用的价格方案
 * @date 2021/9/28
 */
@Data
@ApiModel(value = "批量编辑新增商品查询可用的价格方案")
public class PlanPriceAvailableReqDTO{

    @ApiModelProperty(value = "品牌guid")
    @NotNull(message = "品牌guid不能为空")
    private String brandGuid;

    @ApiModelProperty(value = "排除已选择的价格方案列表guid")
    private List<String> planGuidList;

    @ApiModelProperty(value = "选择的商品列表guid")
    private List<String> itemGuidList;

    @ApiModelProperty(value = "菜谱名称")
    private String planName;
}
