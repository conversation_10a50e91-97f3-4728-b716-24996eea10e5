package com.holderzone.saas.store.dto.queue;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HolderQueueGuidListDTO
 * @date 2019/04/01 16:48
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
public class HolderQueueGuidListDTO extends BaseDTO {
    @ApiModelProperty("清空的queueGuid集合")
    private List<String> guids;
}