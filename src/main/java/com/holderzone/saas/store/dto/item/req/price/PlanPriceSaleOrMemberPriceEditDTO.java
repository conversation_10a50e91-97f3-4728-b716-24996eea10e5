package com.holderzone.saas.store.dto.item.req.price;

import com.holderzone.saas.store.dto.item.common.PlanPriceEditBaseDTO;
import com.holderzone.saas.store.dto.item.common.PlanPriceEditPriceBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/9/29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanPriceSaleOrMemberPriceEditDTO extends PlanPriceEditBaseDTO {

    private List<PlanPriceEditPriceBaseDTO> skuPriceList;
}
