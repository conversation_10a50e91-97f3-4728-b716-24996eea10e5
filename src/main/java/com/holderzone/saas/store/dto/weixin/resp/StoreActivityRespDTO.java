package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/30
 * @description 门店活动
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "门店活动")
public class StoreActivityRespDTO implements Serializable {

    private static final long serialVersionUID = -6904629707871751629L;

    @ApiModelProperty(value = "小标签")
    private String label;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "活动描述列表")
    private List<String> activityDescList;

}
