package com.holderzone.saas.store.dto.hw;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveLaunchDTO
 * @date 2019/04/26 18:29
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "预定请求")
public class ReserveDateTimeDTO {

    @NotEmpty(message = "商户电话号码不得为空")
    @ApiModelProperty(value = "商户电话号码", required = true)
    private String merchantPhone;

    @Nullable
    @ApiModelProperty(value = "就餐人数：1-99", required = true)
    private String peopleTotal;

    @Nullable
    @ApiModelProperty(value = "桌台类型：0=大厅，1=包房", required = true)
    private String roomType;

    @NotNull(message = "预定日期不得为空")
    @JsonFormat(pattern = "yyyyMMdd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @ApiModelProperty(value = "预定日期：如 20190516", required = true)
    private LocalDate reserveDate;

    @NotNull(message = "预定时间不得为空")
    @JsonFormat(pattern = "HH:mm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    @ApiModelProperty(value = "预定时间：如 19:00", required = true)
    private LocalTime reserveTime;
}