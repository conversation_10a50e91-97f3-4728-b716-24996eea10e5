package com.holderzone.saas.store.dto.trade;

import com.holderzone.framework.util.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> R
 * @date 2020/12/15 16:14
 * @description
 */
@ApiModel(value = "挂账还款记录分页返回DTO")
public class DebtUnitRecordPageReqDTO extends Page {

    @ApiModelProperty(value = "单位Guid")
    @NotBlank(message = "单位Guid不能为空")
    private String unitGuid;

    @ApiModelProperty(value = "是否还款（默认0，未还款，1已还款）")
    private Integer repaymentStatus;

    public Integer getRepaymentStatus() {
        return repaymentStatus;
    }

    public void setRepaymentStatus(Integer repaymentStatus) {
        this.repaymentStatus = repaymentStatus;
    }

    public String getUnitGuid() {
        return unitGuid;
    }

    public void setUnitGuid(String unitGuid) {
        this.unitGuid = unitGuid;
    }
}
