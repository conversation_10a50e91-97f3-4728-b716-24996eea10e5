package com.holderzone.saas.store.dto.table;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AreaDTO
 * @date 2018/12/27 10:38
 * @description
 * @program holder-saas-store-dto
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@ApiModel
public class AreaDTO extends BaseDTO implements Serializable {

    @ApiModelProperty("业务主键")
    private String guid;

    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("门店name")
    private String storeName;

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("排序字段")
    private Integer sort;

    @ApiModelProperty("0-启用，1-禁用")
    private Integer enable;

    /**
     * 是否删除
     */
    @ApiModelProperty("0-未删除，1-删除")
    private Integer deleted;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime openTableTime;

    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime gmtModified;


}
