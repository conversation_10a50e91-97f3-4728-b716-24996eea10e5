package com.holderzone.saas.store.dto.report.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DiscountRespDTO
 * @date 2018/09/28 19:30
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class DiscountRespDTO implements Serializable {

    @ApiModelProperty(value = "结果集")
    private List<DiscountResp> result;

    @Data
    @ApiModel
    public static class DiscountResp implements Serializable {

        @ApiModelProperty(value = "折扣方式name")
        private String discountName;

        @ApiModelProperty(value = "折扣金额")
        private BigDecimal amount;
    }

}
