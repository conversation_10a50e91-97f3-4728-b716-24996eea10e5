package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * 实体说民https://developer.meituan.com/openapi#7.5.1
 * <p>
 * caution : 哈哈
 * cityId : 132
 * ctime : 12341234
 * daySeq : 1
 * estimateDeliveredTime : 124124123
 * detail : [{"app_food_code":"1","food_name":"狗不理","sku_id":"1","quantity":6,"price":100,"box_num":2,"box_price":1,"unit":"份","food_discount":0.8,"spec":"大份","food_property":"中辣,微甜","cart_id":0}]
 * ePoiId : erp-poi
 * extras : [{"act_detail_id":10,"reduce_fee":2.5,"mt_charge":1.5,"poi_charge":1.5,"remark":"满10元减2.5元","type":2,"avg_send_time":5.5},{"reduce_fee":5,"remark":"新用户立减5元","type":1,"avg_send_time":1},{"rider_fee":10}]
 * favorites : false
 * invoiced : 1
 * invoiceTitle : XXX公司
 * taxpayerId : 91110108562144110X
 * isFavorites : false
 * isPoiFirstOrder : false
 * isThirdShipping : 0
 * latitude : 234.12341234
 * longitude : 534.12341234
 * logisticsCode : 1002
 * orderId : 12341234
 * orderIdView : 12343412344
 * originalPrice : 25
 * payType : 2
 * pickType : 0
 * poiAddress : 望京-研发园
 * poiFirstOrder : false
 * poiName : 门店名称
 * poiPhone : 18610543723
 * poiReceiveDetail :
 * recipientAddress : 望京-西小区-8号楼5层
 * recipientName : X先生
 * recipientPhone : 18610543723
 * backupRecipientPhone : 17642323521_7121
 * shipperPhone : 18610543723
 * shippingFee : 5
 * status : 1
 * total : 20
 * utime : *********
 */
@Data
public class MtCbOrderDetail implements Serializable {

    private static final long serialVersionUID = 1447188842029145369L;

    /**
     * 备注
     */
    private String caution;

    /**
     * 城市Id
     */
    private long cityId;

    /**
     * 订单创建时间
     * 单位：秒
     */
    private long ctime;

    /**
     * 门店当天的订单流水号
     */
    private String daySeq;

    /**
     * 用户预计送达时间，“立即送达”时为0
     * 单位：秒
     */
    private long deliveryTime;

    /**
     * 餐具
     * 99：用户不需要餐具
     * 0：用户没有选择餐具
     * 1-10：用户选择的用餐人数；
     * -10：10人以上用餐；
     * 88：用户需要餐具；
     */
    private int dinnersNumber;

    /**
     * 三方的门店Id
     */
    private String ePoiId;

    /**
     * 用户是否收藏此门店
     */
    private boolean favorites;

    /**
     * 是否需要发票
     */
    private int hasInvoiced;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 发票税号
     */
    private String taxpayerId;

    /**
     * 用户是否收藏此门店
     */
    private boolean isFavorites;

    /**
     * 用户是否第一次在此门店点餐
     */
    private boolean isPoiFirstOrder;

    /**
     * 是否第三方配送
     */
    private int isThirdShipping;

    /**
     * 订餐地址纬度
     */
    private double latitude;

    /**
     * 订餐地址经度
     */
    private double longitude;

    /**
     * 配送方式码
     */
    private String logisticsCode;

    /**
     * 订单Id
     */
    private long orderId;

    /**
     * 订单展示Id
     */
    private long orderIdView;

    /**
     * 订单原价
     */
    private double originalPrice;

    /**
     * 支付类型
     * 1：货到付款；2：在线支付
     */
    private int payType;

    /**
     * 取餐类型
     * 0：普通取餐；1：到店取餐 该信息默认不推送，如有需求可联系开放平台工作人员开通
     */
    private int pickType;

    /**
     * 门店地址
     */
    private String poiAddress;

    /**
     * 用户是否第一次在此门店点餐
     */
    private boolean poiFirstOrder;

    /**
     * 门店ID
     */
    private Long poiId;

    /**
     * 门店名称
     */
    private String poiName;

    /**
     * 商家电话
     */
    private String poiPhone;

    /**
     * 商家对账详情
     */
    private String poiReceiveDetail;

    /**
     * 收货人地址
     */
    private String recipientAddress;

    /**
     * 隐私地址
     * 此字段不会包含recipientAddress字段中@#后面的值
     */
    private String recipientAddressDesensitization;

    /**
     * 收货人姓名
     */
    private String recipientName;

    /**
     * 收货人电话
     */
    private String recipientPhone;

    /**
     * 备份隐私号
     */
    private String backupRecipientPhone;

    /**
     * 配送员电话
     */
    private String shipperPhone;

    /**
     * 配送费
     */
    private double shippingFee;

    /**
     * 订单状态
     */
    private int status;

    /**
     * 订单总价：用户实际支付金额
     */
    private double total;

    /**
     * 订单更新时间
     * 单位：秒
     */
    private long utime;

    /**
     * detail
     */
    private String detail;

    /**
     * extras
     */
    private String extras;

    /**
     * actOrderChargeByMt : [{"comment":"美团配送减3.0元","feeTypeDesc":"活动款","feeTypeId":10019,"moneyCent":300}]
     * actOrderChargeByPoi : [{"comment":"美团配送减3.0元","feeTypeDesc":"活动款","feeTypeId":10019,"moneyCent":0}]
     * foodShareFeeChargeByPoi : 390
     * logisticsFee : 300
     * onlinePayment : 2000
     * wmPoiReceiveCent : 1610
     */
    @Data
    @ApiModel
    @NoArgsConstructor
    public static class PoiReceiveDetailBean implements Serializable {

        private static final long serialVersionUID = -7468766479192569376L;

        /**
         * 菜品分成
         */
        private long foodShareFeeChargeByPoi;

        /**
         * 用户实际支付配送费 (分)
         */
        private long logisticsFee;

        /**
         * 在线支付款 (分)
         */
        private long onlinePayment;

        /**
         * 商家应收款（分）
         */
        private long wmPoiReceiveCent;

        /**
         * 美团承担明细
         */
        private List<ActOrderChargeBean> actOrderChargeByMt;

        /**
         * 商家承担明细
         */
        private List<ActOrderChargeBean> actOrderChargeByPoi;

        /**
         * comment : 美团配送减3.0元
         * feeTypeDesc : 活动款
         * feeTypeId : 10019
         * moneyCent : 300
         */
        @Data
        @ApiModel
        @NoArgsConstructor
        public static class ActOrderChargeBean implements Serializable {

            private static final long serialVersionUID = -4847929179803041004L;

            /**
             * 备注
             */
            private String comment;

            /**
             * 明细费用类型描述
             */
            private String feeTypeDesc;

            /**
             * 明细费用类型Id
             */
            private int feeTypeId;

            /**
             * 明细金额（分）
             */
            private long moneyCent;
        }
    }

    /**
     * app_food_code : 1
     * food_name : 狗不理
     * sku_id : 1
     * quantity : 6
     * price : 100
     * box_num : 2
     * box_price : 1
     * unit : 份
     * food_discount : 0.8
     * spec : 大份
     * food_property : 中辣,微甜
     * cart_id : 0
     */
    @Data
    public static class DetailBean implements Serializable {

        private static final long serialVersionUID = 7253512481677283320L;

        /**
         * ERP端菜品id
         */
        private String app_food_code;

        /**
         * 菜品名
         */
        private String food_name;

        /**
         * ERP端菜品skuId, 对应菜品映射中的eDishSkuCode
         */
        private String sku_id;

        /**
         * 美团方skuId
         */
        private String mt_sku_id;

        /**
         * 菜品份数
         */
        private long quantity = 0;

        /**
         * 菜品原价
         * 单位：元
         */
        private double price;

        /**
         * 餐盒总个数
         */
        private long box_num = 1;

        /**
         * 餐盒费，单价
         * 单位：元
         */
        private double box_price;

        /**
         * 单位
         */
        private String unit;

        /**
         * 真实价格
         */
        private double actual_price;

        /**
         * 菜品折扣，只是美团商家、APP方配送的门店才会设置，默认为1。
         */
        private double food_discount = 0.00;

        /**
         * 菜品规格
         */
        private String spec;

        /**
         * 菜品属性，多个属性用英文逗号隔开
         */
        private String food_property;

        /**
         * 商品所在的口袋，0为1号口袋，1为2号口袋，以此类推
         */
        private int cart_id = 0;

        private double single_increase_amount;

        private double original_price;

        private boolean isSharpShooter;

        private String mt_spu_id;

        private String mt_tag_id;

        private String detail_id;

        /**
         * 套餐子项详情
         */
        private List<SubDetailBean> sub_detail_list;
    }

    @Data
    public static class SubDetailBean implements Serializable {

        private static final long serialVersionUID = 7584504536957383152L;

        /**
         * ERP端菜品id
         */
        private String app_food_code;

        /**
         * 菜品名
         */
        private String food_name;

        /**
         * ERP端菜品skuId, 对应菜品映射中的eDishSkuCode
         */
        private String sku_id;

        /**
         * 美团方skuId
         */
        private String mt_sku_id;

        /**
         * 菜品份数
         */
        private long quantity = 0;

        /**
         * 菜品原价
         * 单位：元
         */
        private double price;

        /**
         * 餐盒总个数
         */
        private long box_num = 1;

        /**
         * 餐盒费，单价
         * 单位：元
         */
        private double box_price;

        /**
         * 单位
         */
        private String unit;

        /**
         * 真实价格
         */
        private double actual_price;

        /**
         * 菜品规格
         */
        private String spec;

        /**
         * 菜品属性，多个属性用英文逗号隔开
         */
        private String food_property;

        private String detail_id;

        private String mt_spu_id;

        private String mt_tag_id;

        private double original_price;

    }

    /**
     * act_detail_id : 10
     * reduce_fee : 2.5
     * mt_charge : 1.5
     * poi_charge : 1.5
     * remark : 满10元减2.5元
     * type : 2
     * avg_send_time : 5.5
     * rider_fee : 10
     */
    @Data
    public static class ExtraBean implements Serializable {

        private static final long serialVersionUID = 358750138706788718L;

        /**
         * 活动id
         */
        private int act_detail_id;

        /**
         * 活动优惠金额，是美团承担活动费用和商户承担活动费用的总和
         */
        private double reduce_fee;

        /**
         * 优惠金额中美团承担的部分
         */
        private double mt_charge;

        /**
         * 优惠金额中商家承担的部分
         */
        private double poi_charge;

        /**
         * 优惠说明
         */
        private String remark;

        /**
         * 活动类型
         */
        private int type;

        /**
         * 餐厅平均送餐时间，单位为秒
         */
        private float avg_send_time;

        /**
         * 骑手应付款，只对美团配送线上支付线下结算的商家有效
         */
        private double rider_fee;
    }
}
