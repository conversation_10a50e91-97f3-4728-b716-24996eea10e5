package com.holderzone.saas.store.dto.takeaway.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 外卖商品异常数据请求实体
 * @date 2022/5/7 14:11
 * @className: TakeoutItemAbnormalDataReqDTO
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "外卖商品异常数据请求实体")
public class TakeoutItemAbnormalDataReqDTO extends BasePageDTO implements Serializable {

    private static final long serialVersionUID = -1130703550451074979L;

    @NotNull(message = "开始日期时间不能为空")
    @ApiModelProperty("时间筛选-开始日期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startDateTime;

    @NotNull(message = "结束日期时间不能为空")
    @ApiModelProperty("时间筛选-结束日期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endDateTime;

    /**
     * 订单来源：0=美团，1=饿了么，6=赚餐自营外卖
     *
     * @see com.holderzone.saas.store.dto.takeaway.OrderType.TakeoutSubType
     */
    @ApiModelProperty(value = "订单来源：0=美团，1=饿了么，6=赚餐自营外卖")
    private Integer orderType;

    /**
     * 门店GUID列表
     */
    @ApiModelProperty("门店GUID列表")
    private List<String> storeGuidList;

    /**
     * 品牌GUID
     */
    @ApiModelProperty("品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "搜索关键字：商品名称/商品编号")
    private String keywords;

    @ApiModelProperty(value = "顺序类型：0=正序，1=倒叙")
    private Integer sequenceType;

    /**
     * 已审核id列表
     */
    @ApiModelProperty("已审核id列表")
    private List<String> hadCheckIdList;
}
