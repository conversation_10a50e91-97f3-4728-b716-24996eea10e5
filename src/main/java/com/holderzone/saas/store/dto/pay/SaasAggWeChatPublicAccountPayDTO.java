package com.holderzone.saas.store.dto.pay;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SaasAggWeChatPublicAccountPayDTO
 * @date 2019/03/16 10:38
 * @description
 * @program holder-saas-store-dto
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel
public class SaasAggWeChatPublicAccountPayDTO extends BaseDTO {

    @ApiModelProperty("微信公众号支付实体")
    private AggWeChatPublicAccountPayDTO publicAccountPayDTO;

    @ApiModelProperty(value = "交易中心服务回调地址，比如:http://holder-saas-cloud-enterprise/enterprise/find 这个回调地址是saas拿到支付轮询结果的回调")
    private String saasCallBackUrl;

}
