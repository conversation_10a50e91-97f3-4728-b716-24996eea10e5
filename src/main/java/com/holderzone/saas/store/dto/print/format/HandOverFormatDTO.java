/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:HandOverFormatDTO.java
 * Date:2019-12-5
 * Author:terry
 */

package com.holderzone.saas.store.dto.print.format;

import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Deprecated
@EqualsAndHashCode(callSuper = true)
public class HandOverFormatDTO extends FormatDTO {

    @ApiModelProperty(value = "门店名称", required = true)
    private FormatMetadata storeName;

    @ApiModelProperty(value = "单据类型", required = true)
    private FormatMetadata invoiceName;

    @ApiModelProperty(value = "交接员工", required = true)
    private FormatMetadata staffName;

    @ApiModelProperty(value = "时长", required = true)
    private FormatMetadata duration;

    @ApiModelProperty(value = "开始时间", required = true)
    private FormatMetadata beginTime;

    @ApiModelProperty(value = "结束时间", required = true)
    private FormatMetadata overTime;

    @ApiModelProperty(value = "当班营业额", required = true)
    private FormatMetadata dutyAmount;

    @ApiModelProperty(value = "收入列表", required = true)
    private FormatMetadata payRecordList;

    @ApiModelProperty(value = "销售收入", required = true)
    private FormatMetadata saleIncome;

    @ApiModelProperty(value = "充值收入", required = true)
    private FormatMetadata rechargeIncome;

    @ApiModelProperty(value = "注意", required = true)
    private FormatMetadata caution;

    @ApiModelProperty(value = "操作员", required = true)
    private FormatMetadata operator;

    @ApiModelProperty(value = "打印时间", required = true)
    private FormatMetadata printTime;
}
