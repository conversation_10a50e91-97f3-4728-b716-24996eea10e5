package com.holderzone.saas.store.dto.common;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseBillDTO
 * @date 2018/07/13 上午11:03
 * @description
 * @program holder-saas-store-dto
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseDTO implements Serializable {

    private static final long serialVersionUID = -2537027369402350110L;

    /**
     * @see com.holderzone.saas.store.enums.BaseDeviceTypeEnum
     */
    @ApiModelProperty(value = "0：PC服务端,1：PC平板,2：小店通,3：一体机,4：POS机,5：云平板,6：点菜宝(M1)," +
            "7：PV1(带刷卡的点菜宝),9：厨房显示系统,10: 取餐屏,12：微信 15通吃岛",
            hidden = true)
    private Integer deviceType;

    @ApiModelProperty(value = "设备id", hidden = true)
    private String deviceId;

    @ApiModelProperty(value = "设备编号", hidden = true)
    private String deviceNo;

    @ApiModelProperty(value = "企业guid", hidden = true)
    private String enterpriseGuid;

    @ApiModelProperty(value = "企业名称", hidden = true)
    private String enterpriseName;

    @ApiModelProperty(value = "门店guid", hidden = true)
    private String storeGuid;

    @ApiModelProperty(value = "门店名称", hidden = true)
    private String storeName;

    @ApiModelProperty(value = "经度", hidden = true)
    private String longitude;

    @ApiModelProperty(value = "纬度", hidden = true)
    private String latitude;

    @ApiModelProperty(value = "用户guid", hidden = true)
    private String userGuid;

    @ApiModelProperty(value = "用户名称", hidden = true)
    private String userName;

    @ApiModelProperty(value = "登录者帐号", hidden = true)
    private String account;

    @ApiModelProperty(value = "发起请求时间戳", hidden = true)
    private Long requestTimestamp;

    @ApiModelProperty("运营主体Guid")
    private String operSubjectGuid;

    @ApiModelProperty(value = "实收金额")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty("是否打印开票二维码小票 1 是 null或0不管")
    private Integer isInvoiceCode;

    @ApiModelProperty("开票注册人账号")
    private String invoicePhone;

    @ApiModelProperty(value = "accountName")
    private String accountName;

    @ApiModelProperty("打印开票二维码小票")
    private String invoiceCode;

    /**
     * 过时字段
     */
    @ApiModelProperty("是否走手动清台逻辑：0否 1是")
    private Integer isHandleClose;

    @ApiModelProperty("是否走手动清台0否 1是")
    private Integer closeTableFlag;

    @ApiModelProperty(value = "请求过程耗时", hidden = true)
    public Long getElapsedTime() {
        if (requestTimestamp != null) {
            return System.currentTimeMillis() - requestTimestamp;
        }
        return null;

    }
}
