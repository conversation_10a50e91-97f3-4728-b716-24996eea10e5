package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OpenTableDTO
 * @date 2019/01/04 16:11
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class OpenTableByOrderDTO extends BaseDTO {

    @ApiModelProperty(value = "订单号", hidden = true)
    private String orderGuid;
}
