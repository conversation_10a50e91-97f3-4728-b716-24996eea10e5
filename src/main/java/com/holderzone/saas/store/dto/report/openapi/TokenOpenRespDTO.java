package com.holderzone.saas.store.dto.report.openapi;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * token
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TokenOpenRespDTO implements Serializable {

    private static final long serialVersionUID = -1025167689243809355L;

    /**
     * 请求token
     */
    private String token;

}
