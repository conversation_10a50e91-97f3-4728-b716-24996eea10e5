package com.holderzone.saas.store.dto.print;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AreaPrinterDTO
 * @date 2018/9/18 10:21
 * @description 区域-打印机dto
 * @program holder-saas-store-dto
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(description = "打印机关联的区域")
public class PrinterAreaDTO {

    @ApiModelProperty(value = "区域GUID")
    private String areaGuid;

    @ApiModelProperty(value = "区域名称")
    private String areaName;
}
