package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AppInitializeReqDTO
 * @date 2018/09/21 10:45
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AppInitializeReqDTO extends BaseDTO {

    @ApiModelProperty(value = "外卖平台订单id")
    private String orderId;

    @ApiModelProperty(value = "订单服务订单guid")
    private String orderGuid;
}
