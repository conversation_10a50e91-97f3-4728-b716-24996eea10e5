package com.holderzone.saas.store.dto.order.request.dinein;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CreateDineInOrderReqDTO
 * @date 2019/01/04 9:50
 * @description 创建堂食订单
 * @program holder-saas-store-dto
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class CreateGroupMealReqDTO extends BaseDTO {

    private static final long serialVersionUID = 1049826977636839470L;

    @ApiModelProperty(value = "订单的guid")
    @OrderLockField
    private List<String> guid;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "就餐人数")
    private int guestCount;

    @ApiModelProperty(value = "牌号")
    private String mark;

    @ApiModelProperty(value = "用户微信公众号openId")
    private String userWxPublicOpenId;

    @ApiModelProperty(value = "微信桌台号")
    private String weixinTableCode;

    @ApiModelProperty(value = "是否自动牌号（0:否，1：是）")
    private Integer autoMark;

    @ApiModelProperty(value = "商品")
    private List<DineInItemDTO> dineInItemDTOS;
}
