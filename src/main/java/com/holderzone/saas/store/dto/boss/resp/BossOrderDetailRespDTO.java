package com.holderzone.saas.store.dto.boss.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.AdjustOrderRecordDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.*;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.mos.secure.ext.annotations.DesensitizationProp;
import com.mos.secure.ext.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/23
 * @description 老板助手订单信息
 */
@Data
@ApiModel(value = "老板助手桌台信息")
@EqualsAndHashCode(callSuper = false)
public class BossOrderDetailRespDTO implements Serializable {

    private static final long serialVersionUID = -1585815976082285553L;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "0:无并单，1:主单， 2:子单")
    private Integer upperState;

    @ApiModelProperty(value = "主单单号")
    private String mainOrderNo;

    @ApiModelProperty(value = "主单guid")
    private String mainOrderGuid;

    @ApiModelProperty(value = "已退货商品")
    private List<ReturnItemDTO> returnItemDTOS;

    @ApiModelProperty(value = "已下单商品")
    private List<DineInItemDTO> dineInItemDTOS;

    @ApiModelProperty(value = "反结账id")
    private String recoveryId;

    @ApiModelProperty(value = "订单反结账类型1：普通单 2：原单 3：新单 4：退单 ")
    private Integer recoveryType;

    @ApiModelProperty(value = "订单金额（商品总额+附加费）")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "附加费")
    private BigDecimal appendFee;

    @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "找零（收款-应收金额）")
    private BigDecimal changeFee;

    @ApiModelProperty(value = "优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "预定金")
    private BigDecimal reserveFee;

    @ApiModelProperty("预定金退款金额")
    private BigDecimal reserveRefundAmount;

    @ApiModelProperty(value = "预定guid")
    private String reserveGuid;

    @ApiModelProperty("预定备注")
    private String reserveRemark;

    @ApiModelProperty(value = "团购券总优惠金额")
    private BigDecimal grouponFee;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "桌台guid")
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台名字")
    private String diningTableName;

    @ApiModelProperty(value = "是否虚拟台(0:否，1:是)")
    private Integer virtualTable;

    @ApiModelProperty(value = "就餐人数")
    private Integer guestCount;

    @ApiModelProperty(value = "优惠活动guid")
    private String activityGuid;

    @ApiModelProperty(value = "订单当前关联的会员卡guid")
    private String memberCardGuid;

    @ApiModelProperty(value = "可退金额 (所有支付方式 + 团购验券)")
    private BigDecimal refundAbleFee;

    @ApiModelProperty(value = "可退金额 (团购验券)")
    private BigDecimal grouponRefundAbleFee;

    @ApiModelProperty(value = "是否在结账页面团购验券")
    private Boolean useWholeGrouponFlag;

}
