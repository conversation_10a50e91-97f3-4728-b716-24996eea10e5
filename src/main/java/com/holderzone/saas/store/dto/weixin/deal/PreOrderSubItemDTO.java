package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Data
@ApiModel("预订单套餐商品项")
public class PreOrderSubItemDTO {

	@ApiModelProperty("套餐商品名称")
	private String itemName;

	@ApiModelProperty("套餐商品数量")
	private Integer currentCount;

	@ApiModelProperty("套餐商品加价")
	private BigDecimal itemAddPrice;

	private List<PreOrderItemAttrDTO> preOrderItemAttrDTOList;
}
