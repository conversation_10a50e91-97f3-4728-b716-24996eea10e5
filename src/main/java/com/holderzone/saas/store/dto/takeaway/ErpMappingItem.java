package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ErpMappingItem implements Serializable {

    private static final long serialVersionUID = 7234749022045156470L;

    @ApiModelProperty("ERP方商品GUID")
    private String erpItemGuid;

    @ApiModelProperty("ERP方商品名称(拼接规格)")
    private String erpItemName;

    @ApiModelProperty("ERP方商品规格ID")
    private String erpItemSkuId;

    @ApiModelProperty("平台方商品规格ID：不为空表示已绑定，与当前操作的unItemSkuId一致则表示商品应该默认选中")
    private String unItemSkuId;

    @ApiModelProperty("门店商品单价")
    private BigDecimal erpItemPrice;

    @ApiModelProperty(value = "菜谱售卖名称")
    private String planItemName;

    @ApiModelProperty(value = "外卖核算价")
    private BigDecimal takeawayAccountingPrice;
}