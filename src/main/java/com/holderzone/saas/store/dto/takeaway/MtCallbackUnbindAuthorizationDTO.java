package com.holderzone.saas.store.dto.takeaway;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2023-08-23
 * @description 美团业务授权码回调参数
 */
@Data
public class MtCallbackUnbindAuthorizationDTO {

    /**
     * 消息类型，解除授权消息为510001
     */
    @NotBlank(message = "消息类型不能为空")
    private String msgType;

    @NotBlank(message = "数字签名不得为空")
    private String sign;

    @NotBlank(message = "开发者身份不得为空")
    private String developerId;

    @NotBlank(message = "解除授权消息体不得为空")
    private String message;

    private String msgId;

    private String timestamp;
}
