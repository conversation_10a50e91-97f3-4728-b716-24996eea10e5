package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("预订单人物")
@Accessors(chain = true)
public class PreOrderPersonDTO {

    @ApiModelProperty(value = "用户id")
    private String openid;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty("用户微信头像")
    private String headImgUrl;

    @ApiModelProperty(value = "是否登录会员,true登录，false：不是会员或没登录")
    private Boolean loginMember;

    @ApiModelProperty(value = "商品明细")
    private List<PreOrderItemDTO> preOrderItemDTOS;

    @ApiModelProperty(value = "附加费明细")
    private List<PreOrderSurchargeDTO> preOrderSurchargeList;
}

