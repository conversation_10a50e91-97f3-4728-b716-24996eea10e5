package com.holderzone.saas.store.dto.print.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 外卖单
 *
 * <AUTHOR>
 * @date 2018/09/19 16:36
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "预点餐统计单")
public class PrintReserveDTO extends PrintReserveTypeDTO {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private String storeName;

    @NotBlank(message = "开始时间不得为空")
    @ApiModelProperty(value = "开始时间", required = true)
    private String startTime;

    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间", required = true)
    private String endTime;

    @NotNull(message = "单据类型不能为空")
    @ApiModelProperty(value = "单据类型", required = true)
    private Integer type;



}
