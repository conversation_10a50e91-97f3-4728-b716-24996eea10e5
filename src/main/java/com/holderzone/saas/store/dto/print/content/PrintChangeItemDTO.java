package com.holderzone.saas.store.dto.print.content;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.kds.req.KdsItemDTO;
import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.dto.print.content.base.TradeModeAware;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 换菜单
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PrintChangeItemDTO extends PrintBaseItemDTO implements TradeModeAware, PrintDataMockito {


    @NotBlank(message = "订单Guid不得为空")
    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @NotBlank(message = "订单号不得为空")
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @NotBlank(message = "区域guid不得为空")
    @ApiModelProperty(value = "区域guid")
    private String areaGuid;

    @NotNull(message = "交易模式不得为空")
    @Min(value = 0, message = "交易模式：0=正餐，1=快餐，2=外卖")
    @Max(value = 2, message = "交易模式：0=正餐，1=快餐，2=外卖")
    @ApiModelProperty(value = "交易模式：0=正餐，1=快餐，2=外卖")
    private Integer tradeMode;

    @ApiModelProperty(value = "是否撤销换菜")
    private Boolean cancelFlag;

    @ApiModelProperty(value = "桌台号")
    private String diningTableName;

    @ApiModelProperty(value = "创建人")
    private String createStaffName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "原菜品")
    private KdsItemDTO originalKdsItem;

    @ApiModelProperty(value = "更换菜品")
    private KdsItemDTO changesKdsItem;

    @ApiModelProperty(value = "绑定的商品")
    private List<String> arrayOfItemGuid;

    @ApiModelProperty(value = "原菜品")
    private List<KdsItemDTO> originalKdsItemList;

    @ApiModelProperty(value = "更换菜品")
    private List<KdsItemDTO> changesKdsItemList;

    @Override
    public void applyMock() {
        super.applyMock();
    }
}
