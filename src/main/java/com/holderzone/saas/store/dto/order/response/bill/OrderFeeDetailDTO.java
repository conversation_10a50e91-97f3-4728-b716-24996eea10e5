package com.holderzone.saas.store.dto.order.response.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderFeeDetailDTO
 * @date 2019/01/28 11:16
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class OrderFeeDetailDTO {

    @ApiModelProperty(value = "商品总额")
    private BigDecimal itemTotalFee;

    @ApiModelProperty(value = "附加费")
    private List<AppendFeeDetailDTO> appendFeeDetailDTOS;

}
