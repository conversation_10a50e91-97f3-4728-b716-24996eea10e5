package com.holderzone.saas.store.dto.retail.bill.constant;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 18-5-16
 */
public enum PayPowerId {

    /**
     * pc或者其他终端上展示二维码,用户支付宝钱包扫一扫即可支付。
     * 商户需要在页面上行轮询支付查询接口来确定支付状态。
     */
    ALI_SCAN_CODE("1", "支付宝扫码支付"),

    /**
     * 商户收银员用扫码设备(扫码枪)扫描用户支付宝钱包的付款条码/二维码,直接进行扣款
     */
    ALI_BAR_CODE("2", "支付宝条码支付"),

    /**
     * 支付宝服务窗页面使用此接口调起支付宝支付。
     */
    ALI_BAR_CODE_2("3", "支付宝条码支付(支付宝一码多付、支付宝 jsapi 支付)"),

    /**
     * pc 或者其他终端上展示二维码,用户微信扫一扫即可支付。
     * 商户需要在页面上行轮询支付查询接口来确定支付状态。
     */
    WX_SCAN_CODE_ONLINE("4", "微信扫码支付(线上)"),

    /**
     * 商户收银员用扫码设备(扫码枪)扫描用户微信客户端的付款条码/二维码,直接进行扣款
     */
    WX_BAR_CODE_ONLINE("5", "微信条码支付(线上)"),

    /**
     * 商户产品在微信内进行推广和支付。
     */
    WX_PUB_NUM_ONLINE("6", "微信公众号支付(线上)"),

    /**
     * 商户把安卓或 IOS 的sdk 封装到 app 内。具体对接步骤查看微信官方文档,地址:https://pay.weixin.qq.com/wiki/doc/api/app/app.php?chapter=8_5
     */
    WX_APP_ONLINE("7", "微信app支付(线上)"),

    /**
     * pc 或者其他终端上展示二维码,用户微信扫一扫即可支付。商户需要在页面上行轮询支付查询接口来确定支付状态。
     */
    WX_SCAN_CODE_OFFLINE("8", "微信扫码支付(线下)"),

    /**
     * 商户收银员用扫码设备(扫码枪)扫描用户微信客户端的付款条码/二维码,直接进行扣款
     */
    WX_BAR_CODE_OFFLINE("9", "微信条码支付(线下)"),

    /**
     * 商户产品在微信内进行推广和支付
     */
    WX_PUB_NUM_OFFLINE("10", "微信公众号支付(线下)(微信固定二维码支付)"),

    /**
     * 商户产品在微信外网站进行推广和支付。也可封装到 app 内。
     */
    WX_H5_ONLINE("12", "微信 h5 支付(线上)"),

    /**
     * pc 或者其他终端上展示二维码,用户使用手机 qq 扫一扫即可支付。商户需要在页面上行轮询支付查询接口来确定支付状态。
     */
    QQ_SCAN_CODE("14", "qq扫码支付"),

    /**
     * 商户收银员用扫码设备(扫码枪)扫描用户qq 客户端的付款条码/二维码,直接进行扣款
     */
    QQ_BAR_CODE("15", "qq条码支付"),

    /**
     * 商户产品在 qq 内进行推广和支付,富民在 qq的支付授权目录:http://payol.cqfmbank.com/
     */
    QQ_PUB_NUM("16", "qq 公众号支付(qq 固定二维码支付)"),

    /**
     * 商户生成固定二维码, 用户使用支付宝扫一 扫进入输入金额页面,输入金额后提交进行 支付。
     */
    ALI_FIXED_QR("17", "支付宝固定二维码支付"),

    /**
     * 商户生成固定二维码,用户使用微信扫一扫 进入输入金额页面,输入金额后提交进行支付。
     */
    WX_FIXED_QR_ONLINE("18", "微信固定二维码支付(线上)"),

    /**
     * 商户生成固定二维码,用户使用微信扫一扫进入输入金额页面,输入金额后提交进行支付。
     */
    WX_FIXED_QR_OFFLINE("19", "微信固定二维码支付(线下)"),

    /**
     * 商户生成固定二维码, 用户使用 qq 钱包扫一扫进入输入金额页面,输入金额后提交进行支付。
     */
    QQ_FIXED_QR("20", "qq 固定二维码支付"),

    /**
     * 用户向商户展示京东金融等 APP 的付款码,商户扫描用户的付款码进行扣款
     */
    JD_BAR_CODE("21", "京东条码支付"),

    /**
     * 用户通过扫码进行支付
     */
    JD_SCAN_CODE("22", "京东扫码支付"),

    /**
     * 用户要使用 h5 页面来调用,借是只能用借记卡的意思
     */
    JD_H5("23", "京东h5支付(借)"),

    /**
     * 用户要使用 h5 页面来调用,借、贷是可以用借记卡和贷记卡的意思
     */
    JD_H5_2("24", "京东h5支付(借、贷)"),

    /**
     * 商户把安卓或 IOS 的sdk 封装到 app 内。具体对接步骤查看 qq 官方文档,地址:https://qpay.qq.com/qpaywiki/showdocument.php?pid=38&docid=165
     */
    QQ_APP("25", "QQ的app支付"),

    /**
     * 商户生成固定二维码,用户使用京东钱包扫一扫进入输入金额页面,输入金额后提交进行支付。
     */
    JD_FIXED_QR("28", "京东固定二维码支付"),

    /**
     * 微信小程序支付(线上)", "微信小程序内进行支付
     */
    WX_MINI_PROGRAM_ONLINE("29", "微信小程序支付(线上)"),

    /**
     * 微信小程序支付(线下)", "微信小程序内进行支付
     */
    WX_MINI_PROGRAM_OFFLINE("30", "微信小程序支付(线下)"),

    /**
     * 微信银联条码支付(线上)
     */
    YL_WX_BAR_CODE_ONLINE("40", "微信银联条码支付(线上)"),

    /**
     * 银联微信扫码支付(线上)
     */
    YL_WX_SCAN_CODE_ONLINE("41", "银联微信扫码支付(线上)"),
    /**
     * 银联(微信)公众号支付（线上）
     */
    YL_WX_PUBLIC_NO_ONLINE("42", "银联(微信)公众号支付（线上）"),
    /**
     * 银联(微信)H5支付（线上）
     */
    YL_WX_H5_ONLINE("43", "银联(微信)H5支付（线上）"),
    /**
     * 银联(微信)APP支付（线上）
     */
    YL_WX_APP_ONLINE("44", "银联(微信)APP支付（线上）"),
    /**
     * 银联(微信)小程序支付（线上）
     */
    YL_WX_SMALL_PROGRAM_ONLINE("45", "银联(微信)小程序支付（线上）"),
    /**
     * 银联(微信)条码支付（线下）
     */
    YL_WX_BAR_CODE("50", "微信"),
    /**
     * 银联(微信)扫码支付（线下)
     */
    YL_WX_SCAN_CODE("51", "微信"),
    /**
     * 银联(微信)公众号支付（线下）
     */
    YL_WX_PUBLIC_NO("52", "银联(微信)公众号支付（线下）"),
    /**
     * 银联(微信)小程序支付（线下）
     */
    YL_WX_SMALL_PROGRAM("53", "银联(微信)小程序支付（线下）"),

    /**
     * 银联支付宝扫码支付
     */
    TL_AL_SCAN_CODE("31", "支付宝"),

    /**
     * 银联支付宝条码支付
     */
    YL_ALI_BAR_CODE("32", "支付宝"),

    /**
     * 翼支付条码支付
     */
    BEST_BAR_CODE("71", "翼支付"),

    /**
     * 翼支付扫码支付
     */
    BEST_QR_CODE("72", "翼支付"),

    /**
     * 数字货币
     */
    DIGITAL_CURRENCY("100", "数字货币");

    /**
     * 字段值
     */
    private String id;

    /**
     * 支付功能名称
     */
    private String name;

    /**
     * @param id
     * @param name
     */
    PayPowerId(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameById(String id) {
        return Arrays.stream(PayPowerId.values()).filter(payPowerId -> payPowerId.getId().equals(id)).findFirst().map(PayPowerId::getName).orElse(null);
    }
}
