package com.holderzone.saas.store.dto.trade;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 附加费记录
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class AppendFeeDTO implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private String guid;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    private Integer isDelete;

    /**
     * 订单guid
     */
    private String orderGuid;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 名字
     */
    private String name;

    /**
     * 收费方式。0=按人，1=按桌。
     */
    private Integer type;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 区域guid
     */
    private String areaGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 操作人guid
     */
    private String staffGuid;

    /**
     * 操作人姓名
     */
    private String staffName;


}
