package com.holderzone.saas.store.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 聚合支付多次支付退款记录
 */
@Data
public class BillMultipleRefundAggPayDTO implements Serializable {

    private static final long serialVersionUID = 8387381208736999283L;

    @ApiModelProperty(value = "多次支付唯一主键")
    private String guid;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal amount;

    /**
     * 非前端传递
     */
    @ApiModelProperty(value = "当前订单guid")
    private String orderGuid;

    /**
     * 非前端传递
     */
    @ApiModelProperty(value = "交易记录guid")
    private String transactionRecordGuid;

    /**
     * 非前端传递
     */
    @ApiModelProperty(value = "反结账原订单支付payGuid")
    private String originalPayGuid;

    /**
     * 非前端传递
     */
    @ApiModelProperty(value = "反结账原订单支付orderGuid")
    private String originalOrderGuid;

    /**
     * 非前端传递
     */
    @ApiModelProperty(value = "退款类型。0:全款，1:部分")
    private Integer refundType;

    /**
     * 非前端传递
     */
    @ApiModelProperty(value = "是否退款成功")
    private Boolean successFlag;

    /**
     * 非前端传递
     */
    @ApiModelProperty(value = "退款失败原因")
    private String errorMsg;

}
