package com.holderzone.saas.store.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.order.common.BaseOrderDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CancelOrderDTO
 * @date 2018-07-31 14:08:03
 * @description
 * @program holder-saas-store-order
 */
@Data
public class CancelOrderDTO extends BaseOrderDTO {

    @ApiModelProperty(value = "作废原因", required = true)
    private String cancelReason;

    /**
     * 营业日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate businessDay;

}
