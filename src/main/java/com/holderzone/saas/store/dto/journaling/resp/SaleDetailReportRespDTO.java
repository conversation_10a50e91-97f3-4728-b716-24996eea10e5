package com.holderzone.saas.store.dto.journaling.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderDetailReportRespDTO
 * @date 2019/05/28 16:42
 * @description 报表-订单明细响应DTO
 * @program holder-saas-store
 */
@Data
@ApiModel(value = "报表-销售明细响应DTO")
public class SaleDetailReportRespDTO {

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "order_guid")
    private String orderGuid;

    @ApiModelProperty(value = "套餐主项guid")
    private String parentItemGuid;

    @ApiModelProperty(value = "门店名")
    private String storeName;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品分类")
    private String itemTypeName;

    @ApiModelProperty(value = "商品类型（1.套餐主项，2.规格，3.称重，4.单品 ）")
    private Integer itemType;

    @ApiModelProperty(value = "商品类型（1.套餐主项，2.规格，3.称重，4.单品 ）")
    private String typeName;

    @ApiModelProperty(value = "操作（0：全部， 1：点菜， 2：赠菜，3：退菜）")
    private Integer status;

    @ApiModelProperty(value = "操作（0：全部， 1：点菜， 2：赠菜，3：退菜）")
    private String statusName;

    @ApiModelProperty(value = "点餐数量")
    private Double currentNumber;

    @ApiModelProperty(value = "赠菜数量")
    private Double feeNumber;

    @ApiModelProperty(value = "退菜数量")
    private Double returnNumber;

    @ApiModelProperty(value = "总数量")
    private Double totalNumber;

    @ApiModelProperty(value = "记数单位")
    private String unit;

    @ApiModelProperty(value = "price")
    private double price;

    @ApiModelProperty(value = "套餐默认数量")
    private double packageDefaultCount;

    @ApiModelProperty(value = "商品总额")
    private BigDecimal costTotal;

    @ApiModelProperty(value = "属性加价")
    private BigDecimal attrPrice;

    @ApiModelProperty(value = "操作者")
    private String staffName;

    //@ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
   // private BigDecimal realTotal;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "结账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTime;

    @ApiModelProperty(value = "是否是反结账之后的操作（1:是）")
    private Integer isFromRecovery;
    @ApiModelProperty(value = "商品id")
    private String skuGuid;
    @ApiModelProperty(value = "订单商品Guid")
    private String orderItemGuid;
}
