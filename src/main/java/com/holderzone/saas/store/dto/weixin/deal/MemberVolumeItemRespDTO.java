package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("点餐优惠券列表")
@Data
public class MemberVolumeItemRespDTO {

	private List<MemberVolumeItemDTO> memberVolumeItemDTOS;

	@ApiModelProperty(value = "1:表示有选中，0：没有选中")
	private Integer uck;
}
