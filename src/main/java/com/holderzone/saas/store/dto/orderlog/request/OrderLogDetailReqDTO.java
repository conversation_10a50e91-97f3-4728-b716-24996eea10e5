package com.holderzone.saas.store.dto.orderlog.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.order.BillRespDTO;
import com.holderzone.saas.store.dto.order.OrderDTO;
import com.holderzone.saas.store.dto.order.common.OrderDishDTO;
import com.holderzone.saas.store.dto.order.response.OrderDetailRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderLogDetailReqDTO
 * @date 2018/10/09 14:55
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data

//快餐：已完成或者已作废 对应快餐对象
//外卖  对应外卖对象
//快餐:反结账  对应反结账对象
public class OrderLogDetailReqDTO {
    @ApiModelProperty(value = "订单号",required = true)
    private String orderGuid;
    @ApiModelProperty(value = "交易模式编码：0:堂食,1:快餐,2:外卖,")
    private Integer tradeMode;
    @ApiModelProperty(value = "订单状态(0：未完成， 1：已完成， 2：已作废 ，3：已退款，4：反结账状态")
    private Integer state;
}
