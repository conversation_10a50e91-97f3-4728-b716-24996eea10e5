package com.holderzone.saas.store.dto.member.request;

import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ModifiPassWdReqDTO
 * @date 2018/09/17 18:03
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class ModifiPassWdReqDTO extends BaseMemberDTO{

    @ApiModelProperty(value = "旧密码，必传",required = true)
    private String oldPassword;

    @ApiModelProperty(value = "新密码，必传")
    private String newPassword;

}
