package com.holderzone.saas.store.dto.order.response.daily;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 *<AUTHOR>
 *@version 1.0
 *@className BaseItemRespDTO
 *@date 2019/04/16 10:10
 *@description //TODO
 *@program holder-saas-aggregation-app
 */
@ApiModel
@Data
public class BaseItemRespDTO {

    @ApiModelProperty(value = "商品份数")
    private BigDecimal num;

    @ApiModelProperty(value = "商品类型")
    private Integer itemType;

    @ApiModelProperty(value = "order_item主键guid")
    private String orderItemGuid;

}
