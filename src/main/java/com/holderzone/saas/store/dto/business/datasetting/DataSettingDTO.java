package com.holderzone.saas.store.dto.business.datasetting;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/7
 * @since 1.8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel
public class DataSettingDTO {

    @ApiModelProperty(value = "数据取值设置的guid")
    private Long guid;

    @ApiModelProperty(value = "品牌的guid")
    private String brandGuid;

    @ApiModelProperty(value = "品牌的名称")
    private String brandName;

    @ApiModelProperty(value = "数据取值的类型（1：正餐点餐页验券加购商品取值）")
    @NotNull
    private Integer dataSettingType;

    @ApiModelProperty(value = "每个数据取值类型下的code（1：正餐点餐页验券加购商品取值：1：商品原价 2：商品购买价）")
    @NotNull
    private Integer dataSettingCode;
}
