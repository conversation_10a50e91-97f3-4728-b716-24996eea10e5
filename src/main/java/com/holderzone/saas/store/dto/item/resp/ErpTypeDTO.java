package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ErpTypeDTO implements Serializable {

    private static final long serialVersionUID = 8017709063480242375L;

    @ApiModelProperty(value = "分类Guid")
    private String guid;

    @ApiModelProperty(value = "分类名称（商品规格）")
    private String name;
}
