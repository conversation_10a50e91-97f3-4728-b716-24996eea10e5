package com.holderzone.saas.store.dto.item.resp.estimate;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 安卓估清商品返回DTO
 * @date 2022/4/12 11:15
 * @className: EstimateForAndroidRespDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "安卓估清商品返回DTO")
public class EstimateForAndroidRespDTO implements Serializable {

    private static final long serialVersionUID = -4516327278327059963L;

    @ApiModelProperty(value = "估清规格信息列表")
    private List<EstimateItemRespDTO> itemRespList = Lists.newArrayList();

    @ApiModelProperty(value = "恢复售卖时间")
    private String resumeSaleTime;
}
