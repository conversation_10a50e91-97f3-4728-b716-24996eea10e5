package com.holderzone.saas.store.dto.user;

import com.holderzone.resource.common.dto.extension.BaseDictionaryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className UserBaseDTO
 * @date 19-2-11 下午3:00
 * @description
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("用户基础信息")
public class UserBaseDTO {
    @ApiModelProperty("用户guid")
    private String userGuid;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("商户号")
    private String enterpriseNo;

    @ApiModelProperty("商户名")
    private String enterpriseName;

    @ApiModelProperty("商户guid")
    private String enterpriseGuid;

    @ApiModelProperty("登陆用户手机号")
    private String tel;

    /**
     * SINGLE,单店
     * CHAIN,连锁
     * PLATFORM,平台
     */
    @ApiModelProperty("经营模式")
    private String managementModel;

    @ApiModelProperty("注册方式")
    private String regType;

    @ApiModelProperty("是否一体化")
    private Boolean integrateFlag;

    private BaseDictionaryDTO baseDictionaryDTO;
}
