package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @description 出堂配置商品
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "出餐配置返回", description = "出餐配置返回")
public class DistributeItemDTO implements Serializable {

    private static final long serialVersionUID = -816529127671236199L;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "设备Guid")
    private String deviceId;

    @ApiModelProperty(value = "商品Guid")
    private String itemGuid;

    @ApiModelProperty(value = "规格Id")
    private String skuGuid;

}
