package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SurchargeAreaDTO implements Serializable {

    private static final long serialVersionUID = 894917371588191683L;

    @ApiModelProperty(value = "区域Guid")
    private String guid;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "是否被选中")
    private Boolean isSelected;
}
