package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class ItemRefundReqDTO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    @NotBlank(message = "订单商品Guid不得为空")
    @ApiModelProperty("订单商品Guid")
    private String orderItemGuid;

    @NotNull(message = "是否为称重商品不得为空")
    @ApiModelProperty(value = "是否为称重商品")
    private Boolean isWeight;

    @NotNull(message = "商品数量不得为空")
    @ApiModelProperty(value = "商品数量")
    private Integer number;
}
