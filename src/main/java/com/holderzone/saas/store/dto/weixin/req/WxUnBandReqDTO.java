package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxUnBandReqDTO
 * @date 2019/03/07 15:08
 * @description 微信解绑请求DTO
 * @program holder-saas-store
 */
@ApiModel("微信解绑请求DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxUnBandReqDTO {

    @ApiModelProperty("品牌guid")
    @NotEmpty(message = "品牌guid不能为空")
    String brandGuid;

    @ApiModelProperty("短信key")
    @NotEmpty(message = "短信key值不能为空")
    String messageKey;

    @ApiModelProperty("短信验证码")
    @NotEmpty(message = "短信验证码不能为空")
    String code;
}
