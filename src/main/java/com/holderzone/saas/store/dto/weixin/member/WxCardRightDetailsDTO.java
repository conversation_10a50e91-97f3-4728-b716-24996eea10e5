package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Api("返回会员卡权益数组")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxCardRightDetailsDTO {

	@ApiModelProperty("权益GUID")
	private String rightsGuid;
	@ApiModelProperty("权益名称")
	private String rightsName;
	@ApiModelProperty("获取条件")
	private String gainCondition;
	@ApiModelProperty("优惠内容")
	private String preferentialContent;
	@ApiModelProperty("优惠说明")
	private String preferentialDesc;
}
