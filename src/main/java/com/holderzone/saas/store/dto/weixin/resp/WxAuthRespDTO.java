package com.holderzone.saas.store.dto.weixin.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxAuthRespDTO
 * @date 2019/03/06 14:10
 * @description 微信授权列表返回前端DTO， 包含预授权跳转页面url
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("微信授权列表，包含预授权跳转url")
public class WxAuthRespDTO {

    @ApiModelProperty("品牌公众号绑定列表")
    @JsonProperty(value = "data")
    private List<WxBrandAuthRespDTO> wxBrandAuthRespDTOList;

}
