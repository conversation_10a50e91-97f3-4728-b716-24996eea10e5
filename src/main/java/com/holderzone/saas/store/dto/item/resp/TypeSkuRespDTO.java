package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeSkuRespDTO
 * @date 2019/01/24 下午2:16
 * @description //用于套餐新建时分组内商品规格列表显示返回实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "用于套餐新建时分组内商品规格列表显示返回实体")
public class TypeSkuRespDTO implements Serializable {
    @ApiModelProperty(value = "分类GUID")
    private String typeGuid;
    @ApiModelProperty(value = "分类名称")
    private String name;
    @ApiModelProperty(value = "规格集合")
    private List<SkuNameRespDTO> skuNameRespDTOList;

    @ApiModel
    @Data
    public static class SkuNameRespDTO {
        @ApiModelProperty(value = "规格GUID")
        private String skuGuid;
        @ApiModelProperty(value = "商品GUID")
        private String itemGuid;
        @ApiModelProperty(value = "带商品名的规格名称")
        private String name;
        @ApiModelProperty(value = "商品类型:2.规格商品（单商品，不称重），3.称重商品（单商品，称重）")
        private Integer itemType;
        @ApiModelProperty(value = "规格单位")
        private String unit;
        @ApiModelProperty(value = "销售价")
        private BigDecimal salePrice;
        @ApiModelProperty(value = "成本价")
        private BigDecimal saleCostPrice;
        @ApiModelProperty(value = "毛利率")
        private BigDecimal grossMargin;
        @ApiModelProperty("核算价")
        private BigDecimal accountingPrice;
        @ApiModelProperty("商品图片数组")
        private String pictureUrl;
        /**
         * 品牌库对应的SKUGUID：如果是自己创建的内容，则此字段为skuGuid，如果是被推送过来的商品，则该字段为原skuGUID。
         */
        @ApiModelProperty("品牌库对应的SKUGUID")
        private String parentGuid;
    }

}
