package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@ApiModel("优惠券列表请求入参")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class VolumePageReqDTO {

	@ApiModelProperty(value = "订单id",required = true)
	@NotBlank(message = "订单id不能为空")
	private String orderGuid;
}
