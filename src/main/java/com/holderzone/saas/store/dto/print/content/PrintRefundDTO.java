package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.dto.print.content.base.TradeModeAware;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.enums.print.RefundPrintTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

import static com.holderzone.saas.store.dto.print.util.PrintMockUtils.*;

/**
 * 餐饮退款单
 *
 * <AUTHOR>
 * @date 2025/07/01
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("餐饮退款单")
public class PrintRefundDTO extends PrintBaseItemDTO implements TradeModeAware, PrintDataMockito {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private String storeName;

    @Nullable
    @ApiModelProperty(value = "外卖平台名称", notes = "外卖打印时该值不能为空")
    private String markName;

    @NotBlank(message = "桌位或号牌或外卖平台名称不能为空")
    @ApiModelProperty(value = "桌位或号牌或外卖平台名称（正餐'桌位，快餐'牌号'，外卖'平台名''）", required = true)
    private String markNo;

    @NotBlank(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    private String orderNo;

    @NotNull(message = "退款时间不能为空")
    @ApiModelProperty(value = "退款时间", required = true)
    private Long refundTime;

    @NotNull(message = "退款金额不能为空")
    @ApiModelProperty(value = "退款金额", required = true)
    private BigDecimal refundAmount;

    @NotBlank(message = "退款方式不能为空")
    @ApiModelProperty(value = "退款方式", required = true)
    private String refundMethod;

    @ApiModelProperty(value = "退款方式类型")
    private String refundMethodType;

    @Nullable
    @ApiModelProperty(value = "退款原因")
    private String refundReason;

    @NotNull(message = "操作时间不能为空")
    @ApiModelProperty(value = "操作时间", required = true)
    private Long operationTime;

    @ApiModelProperty(value = "类型，0-退款，1-反结账", required = true)
    private RefundPrintTypeEnum operationType;

    @ApiModelProperty(value = "附加费")
    private List<AdditionalCharge> additionalChargeList;

    @Override
    public void applyMock() {
        super.applyMock();
        setStoreName(mockStoreName());
        setMarkNo(mockMarkNo());
        setOrderNo(mockOrderNo());
        setRefundTime(mockRefundTime());
        setRefundAmount(mockRefundAmount());
        setRefundMethod(mockRefundMethod());
        setRefundReason(mockRefundReason());
        setOperatorStaffName(mockOperatorStaffName());
        setOperationTime(mockOperationTime());
        setTradeMode(0);
    }
}
