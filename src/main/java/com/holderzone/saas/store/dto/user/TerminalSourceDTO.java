package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className TerminalSourceDTO
 * @date 19-2-15 上午11:46
 * @description
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("角色授权保存实体")
public class TerminalSourceDTO {
    @ApiModelProperty("角色guid")
    private String roleGuid;

    @ApiModelProperty("终端guid")
    private String terminalGuid;

    @ApiModelProperty("终端code")
    private String terminalCode;

    @ApiModelProperty("终端名")
    private String terminalName;

    @ApiModelProperty("最底级菜单列表，赋值时可存guid字段")
    private List<MenuDTO> menuDTOList;
}
