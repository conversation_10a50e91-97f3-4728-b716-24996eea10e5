package com.holderzone.saas.store.dto.organization;

import com.holderzone.saas.store.dto.table.PadAreaDTO;
import com.holderzone.saas.store.dto.table.TableInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className PadOrderTypeRespDTO
 * @date 21-8-2 下午5:25
 * @description pad点餐查询点餐模式返回dto
 */
@ApiModel("pad点餐查询点餐模式返回dto")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PadOrderTypeRespDTO {

    @ApiModelProperty(value = "点餐类型(0:商家点餐 1:用户自主点餐)")
    private Integer padOrderType;

    @ApiModelProperty(value = "门店下未绑定桌台区域信息")
    private List<PadAreaDTO> padAreaDTOList;

    @ApiModelProperty(value = "已绑定桌台信息")
    private TableInfoDTO bindingTable;

    /***
     * 运营主体Guid
     */
    @ApiModelProperty(value = "运营主体Guid")
    private String operSubjectGuid;

    @ApiModelProperty("企业guid")
    private String enterpriseGuid;
}
