package com.holderzone.saas.store.dto.business.manage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.user.UserOrgDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
@ApiModel(value = "HandoverPayNewDTO", description = "交接班统计收入返回对象")
public class HandoverPayNewDTO {

    @ApiModelProperty(value = "是否多人交接班 0 否 1 是")
    private Integer isMultiHandover;

    private String storeGuid;

    private String storeName;

    private String terminalId;

    private String deviceId;

    private String handoverRecordGuid;

    private Integer status;

    @ApiModelProperty(value = "应上缴现金")
    private BigDecimal handOnCash;

    @ApiModelProperty(value = "实上缴现金")
    private BigDecimal realHandOnCash;

    @ApiModelProperty(value = "销售单数")
    private Integer saleCount;

    @ApiModelProperty(value = "销售总额")
    private BigDecimal saleIncoming;

    @ApiModelProperty(value = "销售详情，key是支付方式名称，value是合计总额")
    private Map<String, BigDecimal> saleIncomingDetail;

    @ApiModelProperty(value = "储值单数")
    private Integer rechargeCount;

    @ApiModelProperty(value = "储值总额")
    private BigDecimal rechargeIncoming;

    @ApiModelProperty(value = "储值详情")
    private Map<String, BigDecimal> rechargeIncomingDetail;

    @ApiModelProperty(value = "预订单数")
    private Integer reserveCount;

    @ApiModelProperty(value = "预订总额")
    private BigDecimal reserveIncoming;

    @ApiModelProperty(value = "预订详情")
    private Map<String, BigDecimal> reserveIncomingDetail;

    @ApiModelProperty(value = "超出金额")
    private BigDecimal excessAmount;

    @ApiModelProperty(value = "当班员工姓名")
    private String userName;

    @ApiModelProperty(value = "当班员工guid")
    private String userGuid;

    @ApiModelProperty(value = "当班员工姓名")
    private String printUserName;

    @ApiModelProperty(value = "交接负责人姓名")
    private String handoverName;

    @ApiModelProperty(value = "当班员工列表")
    private List<UserOrgDTO> userOrgDTOList;

    @ApiModelProperty(value = "选择当班员工guids")
    private List<String> userGuids;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "销售收入支付方式详情（带排序）")
    private List<AmountItemDTO> incomingDetailList;

    @ApiModelProperty(value = "优惠方式详情（带排序）")
    private List<AmountItemDTO> discountDetailList;

    @ApiModelProperty(value = "充值收入支付方式详情（带排序）")
    private List<AmountItemDTO> chargeDetailList;

    @ApiModelProperty(value = "预订金支付方式明细（带排序）")
    private List<AmountItemDTO> reservePayDetailList;

    @ApiModelProperty(value = "还款金额（还挂账金额汇总）")
    private BigDecimal repaymentFeeTotal;

    @ApiModelProperty(value = "还款单数")
    private Integer repaymentFeeCount;

    @ApiModelProperty(value = "还款支付方式（带排序）")
    private List<AmountItemDTO> repaymentList;

    @ApiModelProperty(value = "还款详情，key是支付方式名称，value是合计总额")
    private Map<String, BigDecimal> repaymentStr;

    @ApiModelProperty(value = "客流量")
    private Integer traffic;

    @ApiModelProperty(value = "总餐位数")
    private Integer totalSeats;

    @ApiModelProperty(value = "上座率")
    private String occupancyRatePercent;

    @ApiModelProperty(value = "桌台使用次数")
    private Integer tableUseCount;

    @ApiModelProperty(value = "总桌台数")
    private Integer tableCount;

    @ApiModelProperty(value = "开台率")
    private String openTableRatePercent;

    @ApiModelProperty(value = "翻台率")
    private String flipTableRatePercent;

    @ApiModelProperty(value = "总用餐时间，单位分钟")
    private Long totalDineInTime;

    @ApiModelProperty(value = "平均用餐时间，单位分钟")
    private Integer avgDineInTime;

    @ApiModelProperty(value = "销售（已结总金额）")
    private BigDecimal saleAmount;

    @ApiModelProperty(value = "优惠总额")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "退款总额")
    private BigDecimal refundAmount;
}
