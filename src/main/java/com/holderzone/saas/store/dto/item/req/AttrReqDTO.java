package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.item.common.MchntItemBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PropertyValueReqDTO
 * @date 2019/01/04 下午6:01
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class AttrReqDTO extends MchntItemBaseDTO {

    @ApiModelProperty(value = "关联的属性组的GUID")
    @NotEmpty
    private String attrGroupGuid;

    @ApiModelProperty(value = "属性值名称")
    @NotEmpty
    @Size(max = 20)
    private String name;

    @ApiModelProperty(value = "加价")
    @DecimalMin("0.00")
    @DecimalMax("99.99")
    private BigDecimal price;

    @ApiModelProperty(value = "选取的适用分类的GUID集合")
    private List<String> typeGuidList;
}
