package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import com.sun.javafx.font.PrismFontFactory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TurnTableDTO
 * @date 2019/01/31 17:21
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
@NoArgsConstructor
public class TurnTableDTO extends BaseDTO {

    @ApiModelProperty("旧桌台guid")
    private String originTableGuid;

    @ApiModelProperty("旧桌台code")
    private String originTableCode;

    @ApiModelProperty("旧桌台所出区域guid")
    private String originTableAreaGuid;

    @ApiModelProperty("旧桌台所出区域名称")
    private String originTableAreaName;

    @ApiModelProperty("新桌台所属区域名称")
    private String newTableAreaName;

    @ApiModelProperty("新桌台所属区域guid")
    private String newTableAreaGuid;

    @LockField
    @ApiModelProperty("转到的新桌台guid")
    private String newTableGuid;

    @ApiModelProperty("转到的新桌台code")
    private String newTableCode;


}
