package com.holderzone.saas.store.dto.item.resp;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel(value = "商超库存扣减结果返回实体")
@NoArgsConstructor
@AllArgsConstructor
public class ItemStockResp {

    @ApiModelProperty(value = "返回code（10000:成功;10001:失败）")
    private String code;

    @ApiModelProperty(value = "接口信息")
    private String message;
}
