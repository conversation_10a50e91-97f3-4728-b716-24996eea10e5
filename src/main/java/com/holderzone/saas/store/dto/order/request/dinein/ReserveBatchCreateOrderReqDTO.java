package com.holderzone.saas.store.dto.order.request.dinein;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveBatchCreateOrderReqDTO
 * @date 2019/01/04 9:50
 * @description 预定到点批量开台
 * @program holder-saas-store-dto
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class ReserveBatchCreateOrderReqDTO extends BaseDTO {

    private static final long serialVersionUID = -6036752362190343902L;

    @ApiModelProperty(value = "预订guid")
    private String reserveGuid;

    @ApiModelProperty(value = "主单guid")
    private String mainOrderGuid;

    @ApiModelProperty(value = "是否有预点餐")
    private Boolean containDish;

    @ApiModelProperty(value = "定金")
    private BigDecimal reserveFee;

    @ApiModelProperty(value = "订单桌台信息")
    private List<CreateDineInOrderReqDTO> createDineInOrderReqDTOS;

    @ApiModelProperty(value = "预订电话")
    private String reservePhone;
}
