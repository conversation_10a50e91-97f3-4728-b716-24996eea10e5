package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel("用户能管理门店、组织、区域、品牌传输实体")
public class UserRegionDTO implements Serializable {

    private static final long serialVersionUID = -6377459199355435627L;

    @ApiModelProperty("条件GUID")
    private String adcode;

    @ApiModelProperty("条件名称")
    private String name;

    @ApiModelProperty("条件是否被删除，被删除的话前端显示时需要标记该条件，并对该条件进行修改后提交")
    private Boolean isDeleted;
}
