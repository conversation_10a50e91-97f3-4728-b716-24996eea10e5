package com.holderzone.saas.store.dto.queue;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HolderQueueQueryDTO
 * @date 2019/03/27 17:15
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
public class HolderQueueQueryDTO {
    @ApiModelProperty("队列元素guid")
    private String guid;
    @ApiModelProperty("门店guid")
    private String storeGuid;
    @ApiModelProperty("队列guid")
    private String queueGuid;
    @ApiModelProperty("状态")
    private Byte status;
}