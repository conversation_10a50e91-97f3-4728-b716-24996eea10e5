package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Api("优惠券适用的门店列表")
@Data
@Builder
public class WxVolumeStoreAndProductRespDTO {
	@ApiModelProperty("优惠券Guid")
	private String volumeGuid;
	@ApiModelProperty("优惠券支持门店")
	private List<WxVolumeInStore> volumeInStoreList;
}
