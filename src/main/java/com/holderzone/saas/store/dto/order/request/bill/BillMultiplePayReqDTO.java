package com.holderzone.saas.store.dto.order.request.bill;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/14
 * @description 多次支付
 */
@Data
@ApiModel(value = "多次支付")
@EqualsAndHashCode(callSuper = false)
public class BillMultiplePayReqDTO extends BillAggPayReqDTO implements Serializable {

    private static final long serialVersionUID = 3376540300282443607L;

    @ApiModelProperty(value = "是否聚合支付 0否 1是")
    private Boolean enableAggPay;

    @ApiModelProperty(value = "是否组合支付 0否 1是")
    private Boolean enableCombinePay;

}
