package com.holderzone.saas.store.dto.weixin.deal;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.holderzone.saas.store.dto.item.resp.ItemTypeListDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GroupVerifyDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.enums.weixin.MinPriceTypeEnum;
import com.holderzone.saas.store.enums.weixin.PreferentialTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("微信商品")
public class ItemInfoDTO {
    static SerializeConfig config = new SerializeConfig();

    static {
        config.addFilter(ItemInfoDTO.class, new SimplePropertyPreFilter("itemGuid", "skuList", "attrGroupList", "subgroupList", "couponPreRespDTO"));
        config.addFilter(ItemInfoSkuDTO.class, new SimplePropertyPreFilter("skuGuid", "uck"));
        config.addFilter(ItemInfoAttrGroupDTO.class, new SimplePropertyPreFilter("attrGroupGuid", "attrList", "attrGuid", "uck"));
        config.addFilter(ItemInfoSubgroupDTO.class, new SimplePropertyPreFilter("subgroupGuid", "subItemSkuList", "pickNum"));
        config.addFilter(ItemInfoSubSkuDTO.class, new SimplePropertyPreFilter("skuGuid", "itemGuid", "defaultNum", "attrGroupList"));
        config.addFilter(MtCouponPreRespDTO.class, new SimplePropertyPreFilter("couponCode", "preQueryTime"));
    }

    @ApiModelProperty(value = "商品id")
    private String itemGuid;
    @ApiModelProperty(value = "父商品GUID")
    private String parentGuid;
    @ApiModelProperty(value = "分类GUID")
    private String typeGuid;
    @ApiModelProperty(value = "分类名称")
    private String typeName;
    @ApiModelProperty(value = "商品类型:1.套餐，2.多规格，3.称重,4,单品,5,固定套餐")
    private Integer itemType;
    @ApiModelProperty(value = "商品原价")
    private BigDecimal showPrice = BigDecimal.ZERO;
    @ApiModelProperty(value = "true：有会员价,false：没有")
    private Boolean enablePreferentialPrice = false;
    @ApiModelProperty(value = "商品会员价")
    private BigDecimal showMemberPrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "划线价")
    private BigDecimal showLinePrice = BigDecimal.ZERO;

    @ApiModelProperty(value = "显示单位")
    private String showUnit;
    @ApiModelProperty(value = "起卖数")
    private BigDecimal minOrderNum;
    @ApiModelProperty(value = "会员折扣规则")
    private String showMemberRule = StringUtils.EMPTY;
    @ApiModelProperty(value = "商品名称")
    private String name;
    @ApiModelProperty(value = "拼音简码")
    private String pinyin;
    @ApiModelProperty(value = "别名")
    private String nameAbbr;
    @ApiModelProperty(value = "商品描述")
    private String description;

    /**
     * 英文简述
     */
    @ApiModelProperty(value = "英文简述")
    private String englishBrief = StringUtils.EMPTY;

    /**
     * 英文配料描述
     */
    @ApiModelProperty(value = "英文配料描述")
    private String englishIngredientsDesc = StringUtils.EMPTY;

    @ApiModelProperty(value = "商品图,列表小图")
    private String pictureUrl = StringUtils.EMPTY;

    /**
     * 图片路径数组json,这里对应新一期加入的“列表大图”
     */
    @ApiModelProperty(value = "商品图片地址，列表大图")
    private String bigPictureUrl = StringUtils.EMPTY;

    /**
     * 图片路径数组json,这里对应新一期加入的“详情大图”
     */
    @ApiModelProperty(value = "商品图片地址，详情大图")
    private String detailBigPictureUrl = StringUtils.EMPTY;

    @ApiModelProperty(value = "是否参与整单折扣(0：否，1：是)", hidden = true)
    private Integer isWholeDiscount;
    @ApiModelProperty(value = "是否参与会员折扣（0：否，1：是）")
    private Integer isMemberDiscount;
    @ApiModelProperty("true代表，显示最小金额起:起卖数*最小价格")
    private Boolean minSale = false;
    @ApiModelProperty(value = "估清:1 否 2是")
    private Integer isSoldOut = 1;
    @ApiModelProperty(value = "当前剩余数量")
    private BigDecimal residueQuantity = new BigDecimal("-1");

    private List<ItemInfoTagDTO> tagList;

    @ApiModelProperty(value = "商品最低价")
    private Integer isBestseller;

    @ApiModelProperty(value = "是否参与推荐：0：否，1：是")
    private Integer isNew;

    @ApiModelProperty(value = "是否是招牌：0：否,1:是")
    private Integer isSign;

    private List<ItemInfoSkuDTO> skuList;
    private List<ItemInfoAttrGroupDTO> attrGroupList;
    private List<ItemInfoSubgroupDTO> subgroupList;
    @ApiModelProperty(value = "用户选中的商品数量,称重可能是小数")
    private BigDecimal currentCount = BigDecimal.ZERO;
    @ApiModelProperty(value = "排序号", hidden = true)
    private Integer sort;
    @ApiModelProperty(value = "是否存在估清sku")
    private Boolean existEstimateFlag;

    @ApiModelProperty("满减活动规则描述")
    private List<String> fullReductionStrList;

    @ApiModelProperty("满折活动规则描述")
    private List<String> fullDiscountStrList;

    @ApiModelProperty(value = "最终优惠价")
    private BigDecimal preferentialPrice;

    @ApiModelProperty(value = "折扣价")
    private BigDecimal discountPrice;

    // --->2024.06重构后扫码点餐相关字段

    @ApiModelProperty(value = "划线价")
    private BigDecimal linePrice;

    @ApiModelProperty("原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "会员价格")
    private BigDecimal memberPrice;

    @ApiModelProperty("最低价")
    private BigDecimal minPrice;

    /**
     * 最低价类型
     *
     * @see MinPriceTypeEnum
     */
    @ApiModelProperty("最低价类型 0原价 1会员价 2会员折扣 3限时特价")
    private Integer minPriceType;

    /**
     * 会员折扣比例
     */
    @ApiModelProperty("会员折扣比例")
    private BigDecimal discountValue;

    /**
     * 活动规则描述
     * 顺序：1限时特价 2满减 3满折
     */
    @ApiModelProperty("活动规则描述")
    private List<ActivityRuleDescDTO> activityRuleDescList;

    // 以下字段用于前端计算所需

    /**
     * 优惠类型 1会员价 2会员折扣 3限时特价
     *
     * @see PreferentialTypeEnum
     */
    @ApiModelProperty("优惠类型 1会员价 2会员折扣 3限时特价")
    private List<Integer> preferentialTypeList = new ArrayList<>();

    @ApiModelProperty("商品营销活动")
    private ItemMarketingActivityDTO marketingActivityDTO;

    /**
     * 包含数量
     */
    @ApiModelProperty(value = "包含数量")
    private Integer containNumber;

    @ApiModelProperty("限时特价活动优惠金额")
    private BigDecimal specialsDiscountPrice;

    @ApiModelProperty(value = "限时特价总金额中的会员优惠金额")
    private BigDecimal specialsMemberPrice;

    @ApiModelProperty(value = "参与后单个商品的优惠金额")
    private BigDecimal specialsSingleDistinctPrice;

    @ApiModelProperty(value = "团购券信息")
    private MtCouponPreRespDTO couponPreRespDTO;

    @ApiModelProperty("验券返回信息")
    private GroupVerifyDTO groupVerify;

    /**
     * 商品分类列表
     */
    @ApiModelProperty(value = "商品分类列表")
    private List<ItemTypeListDTO> typeList;

    // <---2024.06重构后扫码点餐相关字段

    public static String getUniqueKey(ItemInfoDTO itemInfoDTO) {
        return DigestUtils.md5DigestAsHex(JSONObject.toJSONBytes(itemInfoDTO, config));
    }

    public ItemInfoDTO(String itemGuid, String parentGuid, String typeGuid, String typeName, Integer itemType, BigDecimal showPrice, Boolean enablePreferentialPrice, BigDecimal showMemberPrice, BigDecimal showLinePrice, String showUnit, BigDecimal minOrderNum, String showMemberRule, String name, String pinyin, String nameAbbr, String description, String englishBrief, String englishIngredientsDesc, String pictureUrl, String bigPictureUrl, String detailBigPictureUrl, Integer isWholeDiscount, Integer isMemberDiscount, Boolean minSale, Integer isSoldOut, BigDecimal residueQuantity, List<ItemInfoTagDTO> tagList, Integer isBestseller, Integer isNew, Integer isSign, List<ItemInfoSkuDTO> skuList, List<ItemInfoAttrGroupDTO> attrGroupList, List<ItemInfoSubgroupDTO> subgroupList, BigDecimal currentCount, Integer sort, Boolean existEstimateFlag, List<String> fullReductionStrList, List<String> fullDiscountStrList, BigDecimal preferentialPrice, BigDecimal discountPrice, BigDecimal linePrice, BigDecimal originalPrice, BigDecimal memberPrice, BigDecimal minPrice, Integer minPriceType, BigDecimal discountValue, List<ActivityRuleDescDTO> activityRuleDescList, List<Integer> preferentialTypeList, ItemMarketingActivityDTO marketingActivityDTO, Integer containNumber, BigDecimal specialsDiscountPrice, BigDecimal specialsMemberPrice, BigDecimal specialsSingleDistinctPrice, MtCouponPreRespDTO couponPreRespDTO, GroupVerifyDTO groupVerify) {
        this.itemGuid = itemGuid;
        this.parentGuid = parentGuid;
        this.typeGuid = typeGuid;
        this.typeName = typeName;
        this.itemType = itemType;
        this.showPrice = showPrice;
        this.enablePreferentialPrice = enablePreferentialPrice;
        this.showMemberPrice = showMemberPrice;
        this.showLinePrice = showLinePrice;
        this.showUnit = showUnit;
        this.minOrderNum = minOrderNum;
        this.showMemberRule = showMemberRule;
        this.name = name;
        this.pinyin = pinyin;
        this.nameAbbr = nameAbbr;
        this.description = description;
        this.englishBrief = englishBrief;
        this.englishIngredientsDesc = englishIngredientsDesc;
        this.pictureUrl = pictureUrl;
        this.bigPictureUrl = bigPictureUrl;
        this.detailBigPictureUrl = detailBigPictureUrl;
        this.isWholeDiscount = isWholeDiscount;
        this.isMemberDiscount = isMemberDiscount;
        this.minSale = minSale;
        this.isSoldOut = isSoldOut;
        this.residueQuantity = residueQuantity;
        this.tagList = tagList;
        this.isBestseller = isBestseller;
        this.isNew = isNew;
        this.isSign = isSign;
        this.skuList = skuList;
        this.attrGroupList = attrGroupList;
        this.subgroupList = subgroupList;
        this.currentCount = currentCount;
        this.sort = sort;
        this.existEstimateFlag = existEstimateFlag;
        this.fullReductionStrList = fullReductionStrList;
        this.fullDiscountStrList = fullDiscountStrList;
        this.preferentialPrice = preferentialPrice;
        this.discountPrice = discountPrice;
        this.linePrice = linePrice;
        this.originalPrice = originalPrice;
        this.memberPrice = memberPrice;
        this.minPrice = minPrice;
        this.minPriceType = minPriceType;
        this.discountValue = discountValue;
        this.activityRuleDescList = activityRuleDescList;
        this.preferentialTypeList = preferentialTypeList;
        this.marketingActivityDTO = marketingActivityDTO;
        this.containNumber = containNumber;
        this.specialsDiscountPrice = specialsDiscountPrice;
        this.specialsMemberPrice = specialsMemberPrice;
        this.specialsSingleDistinctPrice = specialsSingleDistinctPrice;
        this.couponPreRespDTO = couponPreRespDTO;
        this.groupVerify = groupVerify;
    }
}
