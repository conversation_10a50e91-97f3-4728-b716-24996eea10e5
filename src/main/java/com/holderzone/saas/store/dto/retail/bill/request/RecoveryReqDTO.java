package com.holderzone.saas.store.dto.retail.bill.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className JhQueryReqDTO
 * @date 2018/08/14 11:45
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class RecoveryReqDTO extends BaseDTO {


    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "反结账设备类型")
    private Integer recoveryDeviceType;

    @ApiModelProperty(value = "是否人脸支付")
    private boolean facePay;


}