package com.holderzone.saas.store.dto.member.response;

import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 登录返回实体
 * @date 2021/8/9 10:01
 */
@Data
public class PadLoginMemberRespDTO {

    /**
     * 登录状态
     *
     * @see com.holderzone.saas.store.enums.member.LoginStateEnum
     */
    @ApiModelProperty("0密码错误 1登录成功 2手机号未注册 3验证码错误 4验证码已失效 5微信未绑定手机号 6微信授权失败")
    private Integer loginState;

    @ApiModelProperty("查询某门店所属体系下的卡列表的返回值DTO")
    private ResponseMemberAndCardInfoDTO memberAndCardInfoDTO;

    @Override
    public String toString() {
        return "PadLoginMemberRespDTO={" +
                "loginState=" + loginState +
                ", memberAndCardInfoDTO=" + memberAndCardInfoDTO +
                '}';
    }
}
