package com.holderzone.saas.store.dto.reserve;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HWReserveRecordDTO
 * @date 2019/04/26 18:29
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@Accessors(chain = true)
public class HWReserveRecordDTO extends KeyDTO{
    private String merchantPhone;
    private String storeGuid;
    private String customerPhone;
    private Integer peopleTotal;
    private Byte tableType;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime reserveTime;

}