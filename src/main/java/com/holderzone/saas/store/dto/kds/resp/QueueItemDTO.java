package com.holderzone.saas.store.dto.kds.resp;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class QueueItemDTO {

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 设备Guid
     */
    private String deviceGuid;

    /**
     * 订单Guid
     */
    private String orderGuid;

    /**
     * 订单流水号
     */
    private String orderNo;

    /**
     * 订单交易模式
     */
    private Integer orderMode;

    /**
     * 订单描述
     */
    private String orderDesc;

    /**
     * 队列Item状态：0=等待中，1=待取餐，2=已取餐
     */
    private Integer status;

    /**
     * 进入等待队列时间
     */
    private Date inTime;

    /**
     * 进入出堂队列时间
     */
    private Date dstTime;

    /**
     * 出堂队列过期时间
     */
    private Date dstExpireTime;
}