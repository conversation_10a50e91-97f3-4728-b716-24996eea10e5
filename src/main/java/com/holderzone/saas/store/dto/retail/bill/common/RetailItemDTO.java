package com.holderzone.saas.store.dto.retail.bill.common;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderItemDTO
 * @date 2018/09/14 15:48
 * @description 订单商品DTO
 * @program holder-saas-store-trade
 */
@Data
public class RetailItemDTO {
    private static final DateTimeFormatter UTC_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
    private static final DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @ApiModelProperty(value = "订单商品guid")
    private String guid;
    @ApiModelProperty(value = "反结账原菜品guid")
    private Long originalOrderItemGuid;

    @ApiModelProperty(value = "创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "用户微信公众号openId")
    private String userWxPublicOpenId;

    @ApiModelProperty(value = "微信菜品批次")
    private String wxBatch;

    @ApiModelProperty(value = "商品guid", required = true)
    private String itemGuid;

    @ApiModelProperty(value = "商品名称", required = true)
    private String itemName;

    @ApiModelProperty(value = "商品编号", required = true)
    private String code;

    @ApiModelProperty(value = "商品类型(1.套餐主项，2.规格，3.称重，4.单品 )", required = true)
    private Integer itemType;

    @ApiModelProperty(value = "商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂，8.已上菜(已划菜) ，9.预定)")
    private Integer itemState;

    @ApiModelProperty(value = "商品分类guid", required = true)
    private String itemTypeGuid;

    @ApiModelProperty(value = "商品分类名称", required = true)
    private String itemTypeName;

    @ApiModelProperty(value = "规格guid", required = true)
    private String skuGuid;

    @ApiModelProperty(value = "规格名称", required = true)
    private String skuName;

    @ApiModelProperty(value = "sku价格", required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "商品价格小计", required = true)
    private BigDecimal itemPrice;

    @ApiModelProperty(value = "最終的单价", required = true)
    private BigDecimal finalSinglePrice;

    @ApiModelProperty(value = "单品折扣改价会员价后sku价格（前端展示）", required = true)
    private BigDecimal changePrice;

    @ApiModelProperty(value = "单品折扣改价会员价后商品价格小计（前端展示）", required = true)
    private BigDecimal changeItemPrice;

    @ApiModelProperty(value = "商品优惠金额", required = true)
    private BigDecimal totalDiscountFee;

    @ApiModelProperty(value = "会员价格")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "属性总价")
    private BigDecimal singleItemAttrTotal;

    @ApiModelProperty(value = "不改的总价")
    private BigDecimal beforeItemPrice;

    @ApiModelProperty(value = "催品次数", required = true)
    private Integer urgeNum;

    @ApiModelProperty(value = "数量", required = true)
    private BigDecimal currentCount;

    @ApiModelProperty(value = "赠送数量")
    private BigDecimal freeCount;

    @ApiModelProperty(value = "退货数量")
    private BigDecimal returnCount;

    @ApiModelProperty(value = "计数单位", required = true)
    private String unit;

    @ApiModelProperty(value = "是否已经下单(0:否、1：是)", required = true)
    private Integer isPay;

    @ApiModelProperty(value = "是否参与会员权益折扣", required = true)
    private Integer isMemberDiscount;

    @ApiModelProperty(value = "参与了单品券的数量", required = true)
    private BigDecimal isGoodsReduceDiscount;

    @ApiModelProperty(value = "是否参与整单折扣", required = true)
    private Integer isWholeDiscount;

    @ApiModelProperty(value = "是否用会员价格计算了", required = true)
    private Integer isCaculatByMemberPrice;
    /**
     * see {@link com.holderzone.saas.store.enums.order.ItemPriceChangeEnum}
     */
    @ApiModelProperty(value = "改价类型（0-未改价，1-已改价，2-商品折扣）", required = false)
    private Integer priceChangeType;

    @ApiModelProperty(value = "原价", required = false)
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "折扣百分比（700代表7折）", required = false)
    private Integer discountPercent;

    @ApiModelProperty(value = "单个菜品优惠券优惠金额（保留4位小数返回）")
    private BigDecimal couponDiscount;

    @ApiModelProperty(value = "单个菜品会员折扣优惠金额（保留4位小数返回）")
    private BigDecimal memberDiscount;

    @ApiModelProperty(value = "商品备注")
    private String remark;

    @ApiModelProperty(value = "upc")
    private String itemUpc;

    @ApiModelProperty(value = "起买数")
    private BigDecimal minOrderNum;

    @ApiModelProperty(value = "套餐分组")
    private List<PackageSubgroupDTO> packageSubgroupDTOS;

    @ApiModelProperty(value = "赠送商品")
    private List<FreeItemDTO> freeItemDTOS;

    @ApiModelProperty(value = "商品属性")
    private List<ItemAttrDTO> itemAttrDTOS;

}
