package com.holderzone.saas.store.dto.weixin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreTradeOrderDetailsDTO
 * @date 2019/3/20
 */
@Data
@ApiModel(value = "微信订单列表")
public class WxStoreTradeOrderDetailsDTO {

	@ApiModelProperty(value = "主桌为0")
	private Integer mainTable=1;

	@ApiModelProperty(value = "订单的guid")
	private String guid;

	@ApiModelProperty(value = "微信订单guid")
	private String wxGuid;

	@ApiModelProperty(value = "当前订单批次的guid")
	private String batchId;

	@ApiModelProperty(value = "桌台guid")
	private String tableGuid;

	@ApiModelProperty(value = "订单号")
	private String orderNo;

	@ApiModelProperty(value = "桌台号")
	private String tableCode;

	@ApiModelProperty(value = "取餐号")
	private String mark;

	@ApiModelProperty(value = "整单备注")
	private String remark;

	@ApiModelProperty(value = "商品")
	private List<DineInItemDTO> dineInItemDTOS;

	@ApiModelProperty(value = "退货信息")
	private List<ReturnItemDTO> returnItemDTOS;

	@ApiModelProperty(value = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	private LocalDateTime gmtCreate;

	@ApiModelProperty(value = "结算时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime checkoutTime;

	@ApiModelProperty(value = "预付金")
	private BigDecimal prepayFee;

	@ApiModelProperty(value = "消费合计")
	private BigDecimal totalPrice;

	@ApiModelProperty(value = "是否是快餐,1快餐，0：正餐",required = true)
	private Integer orderModel;

	@ApiModelProperty(value = "就餐人数")
	private Integer guestCount;

	@ApiModelProperty(value="订单状态,0待确认，1已下单，2已支付，3已取消，4，已退菜，5待支付，6已完成")
	private Integer state;

	@ApiModelProperty(value = "订单金额（商品总额+附加费）")
	private BigDecimal orderFee;

	@ApiModelProperty(value = "订单金额明细（商品总额+附加费）")
	private OrderFeeDetailDTO orderFeeDetailDTO;


	@ApiModelProperty(value = "找零（收款-应收金额）")
	private BigDecimal changeFee;

	@ApiModelProperty(value = "优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
	private BigDecimal discountFee;


	@ApiModelProperty(value = "订单金额明细（商品总额+附加费）")
	private List<OrderFeeDetailDTO> orderFeeDetailDTOS;

	@ApiModelProperty(value = "实收金额明细（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
	private List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS;

	@ApiModelProperty(value = "优惠金额明细（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
	private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

	WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO;


	@ApiModelProperty(value = "附加费总计")
	private BigDecimal appendFee = BigDecimal.ZERO;

	@ApiModelProperty(value = "应付金额=合计消费-折扣")
	private BigDecimal payableAmount = BigDecimal.ZERO;

	@ApiModelProperty(value = "消费合计=商品金额+附加费")
	private BigDecimal totalConsumption = BigDecimal.ZERO;

	@ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
	private BigDecimal actuallyPayFee = BigDecimal.ZERO;
}
