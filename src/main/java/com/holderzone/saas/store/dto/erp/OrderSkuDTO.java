package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/17 下午 14:11
 * @description
 */
@ApiModel("下订单或者退订单时用于出入库的sku列表")
public class OrderSkuDTO {

    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("订单guid")
    private String orderId;

    @ApiModelProperty("sku列表")
    private List<SkuInfo> skuList;

    @ApiModelProperty("操作人guid")
    private String operatorGuid;

    @ApiModelProperty("操作人名称")
    private String operatorName;

    @ApiModelProperty("企业guid")
    private String enterpriseGuid;

    @ApiModelProperty("是否出库，反结账入库 为false")
    public Boolean getOut() {
        return isOut;
    }

    /**
     * change case： 反结账重新打印订单导致重复出库
     * 解决方案，添加字段，标识出库，默认为出库，如果传false，表示反结账了 要恢复库存
     * 2019-10-9  【wuhedong】
     */
    @ApiModelProperty("是否出库，默认为出库")
    private Boolean isOut = true;

    public void setOut(Boolean out) {
        this.isOut = out;
    }

    public String getEnterpriseGuid() {
        return enterpriseGuid;
    }

    public void setEnterpriseGuid(String enterpriseGuid) {
        this.enterpriseGuid = enterpriseGuid;
    }

    public String getOperatorGuid() {
        return operatorGuid;
    }

    public void setOperatorGuid(String operatorGuid) {
        this.operatorGuid = operatorGuid;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public List<SkuInfo> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<SkuInfo> skuList) {
        this.skuList = skuList;
    }
}


