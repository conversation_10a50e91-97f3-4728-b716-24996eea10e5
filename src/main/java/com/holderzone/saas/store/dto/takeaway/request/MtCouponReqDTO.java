package com.holderzone.saas.store.dto.takeaway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
public class MtCouponReqDTO {

    @ApiModelProperty(value = "storeGuid，必须")
    private String storeGuid;

    @ApiModelProperty(value = "认领门店返回的token【一店一token】，非必须")
    private String appAuthToken;

    @ApiModelProperty(value = "交互数据的编码【建议UTF-8】，必须")
    private String charset;

    @ApiModelProperty(value = "当前请求的时间戳【单位是秒】，必须")
    private long timestamp;

    @ApiModelProperty(value = "接口版本【默认是1】，非必须")
    private String version;

    @ApiModelProperty(value = "请求的数字签名，必须")
    private String sign;

    @ApiModelProperty(value = "开发者id，必须")
    private int developerId;

    @ApiModelProperty(value = "券码，12位纯数字，必须")
    private String couponCode;

    @ApiModelProperty(value = "数量，小于100的整数，必须")
    private int count;

    @ApiModelProperty(value = "32位字符串")
    private String erpId;

    @ApiModelProperty(value = "32位字符串")
    private String erpName;

    @ApiModelProperty(value = "32位字符串")
    private String erpOrderId;

    @ApiModelProperty(value = "券码渠道 买单:1004")
    private Integer receiptChannel;

}
