package com.holderzone.saas.store.dto.order.response.daily;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DailyDiningTypeDTO
 * @date 2019/02/15 10:40
 * @description 营业日报-用餐类型
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class DiningTypeRespDTO {
    @ApiModelProperty(value = "用餐类型编码 1正餐 2快餐 3外卖")
    private Integer typeCode;
    @ApiModelProperty(value = "用餐类型名称")
    private String typeName;
    @ApiModelProperty(value = "订单数")
    private Integer orderCount;
    @ApiModelProperty(value = "消费人数")
    private Integer guestCount;
    @ApiModelProperty(value = "销售收入")
    private BigDecimal amount;
    @ApiModelProperty(value = "单均消费")
    private BigDecimal orderPrice;
    @ApiModelProperty(value = "人均消费")
    private BigDecimal guestPrice;
    @ApiModelProperty(value = "外卖统计")
    List<DiningTypeRespDTO> subDiningTypes;
    @ApiModelProperty(value = "是否合计项,0否 1是")
    private int isTotal = 0;

}