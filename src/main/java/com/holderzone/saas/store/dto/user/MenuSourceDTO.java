package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className MenuSourceDTO
 * @date 19-2-10 上午10:52
 * @description 菜单-资源DTO
 * @program holder-saas-store-staff
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class MenuSourceDTO {

    @ApiModelProperty("模块名称")
    private String moduleName;

    @ApiModelProperty("资源guid")
    private String sourceGuid;

    @ApiModelProperty("资源名称")
    private String sourceName;

    @ApiModelProperty("资源code")
    private String sourceCode;

    @ApiModelProperty("资源请求地址")
    private String sourceUrl;

    @ApiModelProperty("是否可以提升权限")
    private Boolean isElevatedPrivileges;

    @ApiModelProperty("权限类型 0正常权限 1人脸授权 2授权码授权")
    private Integer type;
}
