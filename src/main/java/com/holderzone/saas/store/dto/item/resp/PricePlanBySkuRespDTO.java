package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description ..
 * @date 2021/1/15 15:44
 */
@Data
public class PricePlanBySkuRespDTO {

    /**
     * 品牌GUID
     */
    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    /**
     * 方案GUID
     */
    @ApiModelProperty(value = "方案GUID")
    private String planGuid;

    /**
     * 菜品GUID
     */
    @ApiModelProperty(value = "菜品GUID")
    private String itemGuid;

    /**
     * 菜品规格GUID
     */
    @ApiModelProperty(value = "菜品规格GUID")
    private String skuGuid;

    /**
     * 方案销售价格
     */
    @ApiModelProperty(value = "方案销售价格")
    private BigDecimal salePrice;

    /**
     * 方案会员价格
     */
    @ApiModelProperty(value = "方案会员价格")
    private BigDecimal memberPrice;

    /**
     * 是否套餐
     */
    @ApiModelProperty(value = "是否套餐")
    private Boolean isPkgItem;

    @ApiModelProperty(value = "方案名")
    private String planName;
}
