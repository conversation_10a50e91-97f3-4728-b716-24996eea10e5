package com.holderzone.saas.store.dto.erp;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/07 下午 14:32
 * @description
 */
@ApiModel("出入库单查询实体")
public class InOutDocumentQueryDTO extends BasePageDTO {

    @ApiModelProperty("仓库guid")
    private List<String> warehouseGuidList;

    @ApiModelProperty("单据提交状态")
    private Integer status;

    @ApiModelProperty("单据类型")
    private Integer type;

    @ApiModelProperty("单据出入库类型")
    private Integer inOutType;

    @ApiModelProperty("供应商Guid")
    private String supplierGuid;

    @ApiModelProperty("单据编号")
    private String guid;

    @ApiModelProperty("单据开始日期")
    private Date startDate;

    @ApiModelProperty("单据结束日期")
    private Date endDate;

    public List<String> getWarehouseGuidList() {
        return warehouseGuidList;
    }

    public void setWarehouseGuidList(List<String> warehouseGuidList) {
        this.warehouseGuidList = warehouseGuidList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getInOutType() {
        return inOutType;
    }

    public void setInOutType(Integer inOutType) {
        this.inOutType = inOutType;
    }

    public String getSupplierGuid() {
        return supplierGuid;
    }

    public void setSupplierGuid(String supplierGuid) {
        this.supplierGuid = supplierGuid;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
}
