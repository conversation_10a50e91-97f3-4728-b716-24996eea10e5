package com.holderzone.saas.store.dto.weixin.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxCommonReqDTO
 * @date 2019/02/23 15:46
 * @description 微信推送公共报文格式请求DTO
 * @program holder-saas-store-weixin
 */
@Data
@ApiModel("微信推送公共报文格式请求DTO")
public class WxCommonReqDTO implements Serializable {

    @ApiModelProperty(value = "签名字段")
    @JsonProperty(value = "signature")
    private String signature;

    @ApiModelProperty(value = "随机字符串")
    @JsonProperty(value = "echostr")
    private String echostr;

    @ApiModelProperty(value = "时间戳")
    @JsonProperty(value = "timestamp")
    private String timestamp;

    @ApiModelProperty(value = "随机数")
    @JsonProperty(value = "nonce")
    private String nonce;

    @ApiModelProperty(value = "加密类型")
    @JsonProperty(value = "encrypt_type")
    private String encrypt_type;

    @ApiModelProperty("消息签名")
    @JsonProperty(value = "msg_signature")
    private String msg_signature;

    @ApiModelProperty("openId")
    @JsonProperty(value = "openid")
    private String openid;

    @ApiModelProperty("body参数")
    private String xml;
}
