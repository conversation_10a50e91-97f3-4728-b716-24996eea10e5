package com.holderzone.saas.store.dto.trade.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 支付方式实体
 * @date 2022/1/19 14:19
 * @className: PaymentDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "支付方式实体")
public class PaymentDTO implements Serializable {

    private static final long serialVersionUID = 4110897270644084480L;

    @ApiModelProperty(value = "交易金额")
    private BigDecimal amount;

    /**
     * @see com.holderzone.saas.store.enums.PaymentTypeEnum
     */
    @ApiModelProperty(value = "支付方式 1:现金支付 ，2:聚合支付 ,3：银行卡支付，4：会员卡支付，5：人脸支付，10：其他支付方式")
    @Min(value = 1, message = "支付方式最少为1")
    private Integer paymentType;

    @ApiModelProperty(value = "支付方式名")
    @NotNull(message = "支付方式不能为空")
    private String paymentTypeName;
}
