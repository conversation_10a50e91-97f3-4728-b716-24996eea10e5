package com.holderzone.saas.store.dto.report.openapi.shiyuanhui;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 销售支付明细响应
 */
@Data
public class SalePayDetailShiYuanHuiRespDTO implements Serializable {

    private static final long serialVersionUID = -1025167689243809355L;

    /**
     * 序号
     */
    @JsonProperty("Index")
    private Integer index;

    /**
     * 订单guid
     */
    @JsonIgnore
    private String orderGuid;

    /**
     * 支付时间
     */
    @JsonIgnore
    private LocalDateTime gmtCreate;

    /**
     * 支付类型
     */
    @JsonIgnore
    private Integer paymentType;

    /**
     * 支付类型名称
     */
    @JsonIgnore
    private String paymentTypeName;

    /**
     * 支付方式
     * 支付方式编码: x1001为现金，其他由调用方提供
     */
    @JsonProperty("PayWayCode")
    private String payWayCode;

    /**
     * 支付金额
     */
    @JsonProperty("PayAmount")
    private Long amount;

    /**
     * 支付流水号, 收单系统唯一性标识
     */
    @JsonProperty("BizCode")
    private String bankTransactionId;
}
