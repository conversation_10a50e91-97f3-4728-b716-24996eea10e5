package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class DstItemBindRespDTO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    @ApiModelProperty(value = "设备Guid")
    private String deviceId;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "商品Guid")
    private String itemGuid;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "菜谱售卖名称")
    private String planItemName;

    @ApiModelProperty(value = "商品拼音")
    private String pinyin;

    @ApiModelProperty(value = "是否被当前设备绑定")
    private Boolean isBoundBySelf;

    @ApiModelProperty(value = "是否被其他设备绑定")
    private Boolean isBoundByOthers;

    @ApiModelProperty(value = "规格列表")
    private List<DstSkuBindRespDTO> skus;
}
