package com.holderzone.saas.store.dto.print.format.metadata;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotNull;

/**
 * example:
 * type=0,title=宣传语,text=欢迎您！
 * 那么：
 * 一体机上：    宣传语      欢迎您
 * 打印单据上：       欢迎您
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomMetadata extends FormatMetadata {

    /**
     * 0=文本
     * 1=图片
     * 2=条码
     * 3=二维码
     */
    @NotNull(message = "自定义类型不能为空")
    @ApiModelProperty(value = "自定义类型")
    private Integer type;

    /**
     * 标题
     */
    @Nullable
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * type=0 文本内容
     * type=1 图片URL
     * type=2 条码文本
     * type=3 二维码文本
     */
    @NotNull(message = "正文不能为空")
    @ApiModelProperty(value = "正文")
    private String text;
}
