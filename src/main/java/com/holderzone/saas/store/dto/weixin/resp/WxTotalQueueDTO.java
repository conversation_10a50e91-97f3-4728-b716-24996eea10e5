package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.saas.store.dto.queue.HolderQueueDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxTotalQueueDTO
 * @date 2019/05/17 16:27
 * @description 微信排队总览信息DTO
 * @program holder-saas-store
 */
@ApiModel("微信排队总览信息DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxTotalQueueDTO {
    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("门店名字")
    private String storeName;

    @ApiModelProperty("品牌guid")
    private String brandGuid;

    @ApiModelProperty("品牌名字")
    private String brandName;

    @ApiModelProperty("品牌Logo")
    private String brandLogo;

    @ApiModelProperty("门店排队桌位信息")
    private List<HolderQueueDTO> holderQueueDTOList;

    public String getStoreGuid() {
        return storeGuid;
    }

    public WxTotalQueueDTO setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
        return this;
    }

    public String getStoreName() {
        return storeName;
    }

    public WxTotalQueueDTO setStoreName(String storeName) {
        this.storeName = storeName;
        return this;
    }

    public String getBrandGuid() {
        return brandGuid;
    }

    public WxTotalQueueDTO setBrandGuid(String brandGuid) {
        this.brandGuid = brandGuid;
        return this;
    }

    public String getBrandName() {
        return brandName;
    }

    public WxTotalQueueDTO setBrandName(String brandName) {
        this.brandName = brandName;
        return this;
    }

    public String getBrandLogo() {
        return brandLogo;
    }

    public WxTotalQueueDTO setBrandLogo(String brandLogo) {
        this.brandLogo = brandLogo;
        return this;
    }

    public List<HolderQueueDTO> getHolderQueueDTOList() {
        return holderQueueDTOList;
    }

    public WxTotalQueueDTO setHolderQueueDTOList(List<HolderQueueDTO> holderQueueDTOList) {
        this.holderQueueDTOList = holderQueueDTOList;
        return this;
    }
}
