package com.holderzone.saas.store.dto.item.resp;


import com.holderzone.saas.store.enums.zhuancan.CatSourceType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeSynRespDTO
 * @date 2019/01/03 下午2:11
 * @description //安桌分类同步的返回DTO
 * @program holder-saas-store-dto
 */
@Data
public class TypeSynRespDTO implements Serializable {

    private static final long serialVersionUID = 8699718152178767966L;

    @ApiModelProperty(value = "分类Guid")
    private String typeGuid;

    @ApiModelProperty(value = "商品分类名称")
    private String name;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "maybe null,图标的URL")
    private String iconUrl;

    @ApiModelProperty(value = "maybe null,描述")
    private String description;

    @ApiModelProperty(value = "分类来源")
    private CatSourceType catSourceType;

    /**
     * pad分类图片大小类型(0小图,1竖图,2整屏)
     * PadTypePictureEnum
     */
    @ApiModelProperty(value = "pad分类图片大小类型(0小图,1竖图,2整屏)")
    private Integer menuClassifyPictureType;

    @ApiModelProperty(value = "父级分类Guid")
    private String parentGuid;

    @ApiModelProperty(value = "是否必点")
    private Integer isMustPoint;

}
