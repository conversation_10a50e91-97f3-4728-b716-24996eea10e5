package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.saas.store.dto.pay.AggPayRespDTO;
import com.holderzone.saas.store.dto.pay.KbzPayStartDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxPayRespDTO
 * @date 2019/04/24 14:12
 * @description 微信支付响应DTO
 * @program holder-saas-store
 */
@ApiModel("微信支付响应DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class WxPayRespDTO {

    @ApiModelProperty("支付url")
    private String payUrl;

    @ApiModelProperty("是否可支付:1可支付,0不可支付,2,已支付跳到订单详情")
    private Integer couldPay;

    @ApiModelProperty("错误信息")
    private String errorMsg;

    @ApiModelProperty("聚合支付预下单resp")
    private AggPayRespDTO result;

    @ApiModelProperty("kbz发起支付实体")
    private KbzPayStartDTO kbzPayStartDTO;

    public static WxPayRespDTO changeFailed() {
        return new WxPayRespDTO().setCouldPay(2).setErrorMsg("订单详情发生变化");
    }

    public static WxPayRespDTO payAmountFailed() {
        return new WxPayRespDTO().setCouldPay(2).setErrorMsg("支付异常，请刷新后重试");
    }

    public static WxPayRespDTO prohibit() {
        return new WxPayRespDTO().setCouldPay(0).setErrorMsg("暂未开启线上支付，请联系商家");
    }

    public static WxPayRespDTO payFailed(Integer code, String message) {
        return new WxPayRespDTO().setCouldPay(code).setErrorMsg(message);
    }
}
