package com.holderzone.saas.store.dto.report.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2018/10/10 下午 13:57
 * @description
 */
@ApiModel("订单菜品分类统计查询实体")
public class OrderDishTypeInfoQueryDTO extends BaseQueryDTO {

    @ApiModelProperty("菜品分类")
    private Integer level;

    @ApiModelProperty("排序字段")
    private String sortField;

    @ApiModelProperty("倒叙(0)或者顺序(1)")
    private Integer asc;

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public Integer getAsc() {
        return asc;
    }

    public void setAsc(Integer asc) {
        this.asc = asc;
    }
}
