package com.holderzone.saas.store.dto.order.request.item;

import com.holderzone.saas.store.dto.table.BaseTableDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ServeItemReqDTO
 * @date 2019/01/24 17:26
 * @description //TODO
 * @program holder-saas-store-dto
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ServeItemReqDTO extends BaseTableDTO {

    private static final long serialVersionUID = 15461151683272511L;

    @ApiModelProperty(value = "划菜商品")
    private List<ServeItem> serveItems;

    @Data
    public static class ServeItem {

        @ApiModelProperty(value = "商品或赠送商品guid")
        private String guid;

        @ApiModelProperty(value = "划菜类型（1：商品，2：赠送）")
        private int type;

    }
}
