package com.holderzone.saas.store.dto.store.store;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
public class BindupAccountsTips implements Serializable {

    /**
     * 是否提示信息  0 否 1 是
     */
    private int tips;

    /**
     *  营业时间
     */
    private LocalDate currTime;

    /**
     * 是否展示现金： false: 不展示    true:展示
     */
    private Boolean isShowCash;

    /**
     * 能否开台 false: 不能开台   true:能开台
     */
    private Boolean canOpenTable;
}
