package com.holderzone.saas.store.dto.print.content.nested;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayRecord
 * @date 2018/07/25 16:11
 * @description 支付方式
 * @program holder-saas-store-print
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "支付方式记录")
public class PayDetailRecord implements Serializable {

    private static final long serialVersionUID = -1851067545250063984L;

    @NotBlank(message = "支付方式名称不能为空")
    @ApiModelProperty(value = "支付方式名称")
    private String payName;

    @NotNull(message = "销售金额不能为空")
    @ApiModelProperty(value = "销售金额")
    private BigDecimal salesAmount;

    @NotNull(message = "退货金额不能为空")
    @ApiModelProperty(value = "退货金额")
    private BigDecimal refundAmount;

    @NotNull(message = "实收金额不能为空")
    @ApiModelProperty(value = "实收金额")
    private BigDecimal actuallyAmount;
}
