package com.holderzone.saas.store.dto.queue;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HolderQueueDTO
 * @date 2019/03/27 17:00
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
@Accessors(chain = true)
public class HolderQueueDTO {
    private String guid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "品牌logo（oss下载地址）")
    private String logoUrl;

    @ApiModelProperty(value = "是否开启回复队列")
    private Boolean isEnableRecovery;

    @ApiModelProperty(value = "恢复至队列的位数,请填写1,2,3")
    private Integer recoveryNum ;

    @ApiModelProperty(value = "门店名称（创建、更新时不能为空）", required = true)
    private String storeName;

    @ApiModelProperty(value = "队列名称",required = true)
    @Size(min = 1,max = 5,message = "")
    private String name;
    @NotEmpty
    @ApiModelProperty(value = "队列编码",required = true)
    private String code;
    @Min(1)
    @Max(99)
    @ApiModelProperty(value = "最大人数",required = true)
    private Byte min;
    @ApiModelProperty(value = "最小人数",required = true)
    private Byte max;

    private Integer size;

    public static class DEFAULT_LESS{
        public static HolderQueueDTO less(String storeGuid){
            return new HolderQueueDTO().setName("小桌").setMin((byte)1).setMax((byte)4).setCode("A").setStoreGuid(storeGuid).setSize(0);
        }
    }
    public static class DEFAULT_MID{
        public static HolderQueueDTO mid(String storeGuid){
            return new HolderQueueDTO().setName("大桌").setMin((byte)5).setMax((byte)8).setCode("B").setStoreGuid(storeGuid).setSize(0);
        }
    }
}