package com.holderzone.saas.store.dto.print.format.metadata;

import com.holderzone.saas.store.dto.print.template.convertable.Font;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class FormatMetadata implements Serializable {

    private static final long serialVersionUID = 788429040407113L;

    /**
     * 1=一倍字体
     * 2=二倍字体
     * 3=三倍字体
     * 4=四倍字体
     * ...
     */
    private int size = 1;

    /**
     * X轴放大倍数
     */
    private int xm = 1;

    /**
     * Y轴放大倍数
     */
    private int ym = 1;

    /**
     * 暂不启用
     * <p>
     * 0=左对齐
     * 1=居中对齐
     * 2=右对齐
     * 3=两端对齐
     */
    private int align = 0;

    /**
     * 是否加粗
     */
    private boolean bold = false;

    /**
     * 暂不启用
     * <p>
     * 是否加下划线
     */
    private boolean underline = false;

    /**
     * 是否启用
     */
    private boolean enable = true;

    public FormatMetadata(int size, int align, boolean bold) {
        this.xm = size;
        this.ym = size;
        this.align = align;
        this.bold = bold;
    }

    public FormatMetadata(int size, int align, boolean bold, boolean enable) {
        this.xm = size;
        this.ym = size;
        this.align = align;
        this.bold = bold;
        this.enable = enable;
    }

    public FormatMetadata(int xm, int ym, int align, boolean bold) {
        this.xm = xm;
        this.ym = ym;
        this.align = align;
        this.bold = bold;
    }

    public FormatMetadata(int xm, int ym, int align, boolean bold, boolean enable) {
        this.xm = xm;
        this.ym = ym;
        this.align = align;
        this.bold = bold;
        this.enable = enable;
    }

    public FormatMetadata setSize(int size) {
        this.size = size;
        this.xm = size;
        this.ym = size;
        return this;
    }

    public Font toFont() {
        return new Font(new Font.Size(xm, ym), bold, underline);
    }
}
