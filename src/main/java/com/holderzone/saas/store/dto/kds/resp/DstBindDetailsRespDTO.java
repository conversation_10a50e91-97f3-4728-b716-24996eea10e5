package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class DstBindDetailsRespDTO implements Serializable {

    private static final long serialVersionUID = -1882498373765351638L;

    @ApiModelProperty(value = "绑定关系预览")
    private DstBindStatusRespDTO dstBindStatus;

    @ApiModelProperty(value = "绑定商品列表")
    private List<DstTypeBindRespDTO> dstTypeBindList;
}
