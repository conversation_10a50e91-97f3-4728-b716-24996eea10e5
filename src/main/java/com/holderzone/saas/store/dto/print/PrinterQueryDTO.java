package com.holderzone.saas.store.dto.print;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrinterQueryDTO
 * @date 2018/9/18 16:40
 * @description 查询打印机dto
 * @program holder-saas-store-dto
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@ApiModel(description = "打印机查询条件dto")
public class PrinterQueryDTO {

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "打印业务类型: 1/前台打印; 2后厨打印; 3标签打印;")
    private Integer businessType;

    @ApiModelProperty(value = "T1设备编号")
    private String deviceId;

    /**
     * 票据类型
     */
    @ApiModelProperty(value = "票据类型")
    private Integer invoiceType;
}
