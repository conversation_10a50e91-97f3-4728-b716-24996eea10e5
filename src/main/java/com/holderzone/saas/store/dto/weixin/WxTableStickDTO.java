package com.holderzone.saas.store.dto.weixin;

import com.holderzone.resource.common.dto.validate.Add;
import com.holderzone.resource.common.dto.validate.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/02/18 18:00
 */
@ApiModel(value = "桌贴对象", description = "桌贴传输DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxTableStickDTO {

    @ApiModelProperty("唯一标识")
    @NotEmpty(message = "GUID不能为空", groups = Update.class)
    private String guid;
    @ApiModelProperty("所属模板分类GUID")
    @NotEmpty(message = "所属分类不能为空", groups = {Add.class, Update.class})
    private String category;
    @ApiModelProperty("原价")
    @NotNull(message = "原价不能为空", groups = {Add.class, Update.class})
    private BigDecimal originalPrice;
    @ApiModelProperty("售价")
    @NotNull(message = "售价不能为空", groups = {Add.class, Update.class})
    private BigDecimal price;
    @ApiModelProperty("是否推荐:0/否,1/是")
    @NotEmpty(message = "是否推荐不能为空", groups = {Add.class, Update.class})
    private String isRecommended;
    @ApiModelProperty("桌贴名字")
    @NotEmpty(message = "桌贴名字不能为空", groups = {Add.class, Update.class})
    private String name;
    @ApiModelProperty("背景颜色")
    @NotEmpty(message = "背景颜色必填", groups = {Add.class, Update.class})
    private String backColor;
    @ApiModelProperty("背景宽度")
    @Min(message = "宽度必须大于0", value = 1, groups = {Add.class, Update.class})
    private Double backWidth;
    @ApiModelProperty("背景高度")
    @Min(message = "高度必须大于0", value = 1, groups = {Add.class, Update.class})
    private Double backHeight;
    @ApiModelProperty("单位:0/毫米,1/厘米,2/像素,3/英寸")
    private String unit;
    @ApiModelProperty("背景图片，包含背景颜色")
    @NotBlank
    private String bgUrl;
    @ApiModelProperty("背景图片")
    private String backImage;
    @ApiModelProperty("背景图展示策略:0/原图,1/高100%,2/宽100%,3/压缩")
    private String backStrategy;
    @ApiModelProperty("图片元素")
    private String elementImage;
    @ApiModelProperty("logo图片")
    private String elementLogo;
    @ApiModelProperty("logo是否可用:0/否,1/是")
    private String logoSwitch;
    @ApiModelProperty("控制桌贴编辑时打开log，0关闭，1打开")
    private Integer isLogoShow;
    @ApiModelProperty("形状元素")
    private String elementShape;
    @ApiModelProperty("门店名称")
    private String storeNameText;
    @ApiModelProperty("门店名称的字体颜色")
    private String storeNameTextColor;
    @ApiModelProperty("门店描述")
    private String storeDescText;
    @ApiModelProperty("门店描述的字体颜色")
    private String storeDescTextColor;
    @ApiModelProperty("二维码描述")
    private String qrCodeText;
    @ApiModelProperty("二维码描述的字体颜色")
    private String qrCodeTextColor;
    @ApiModelProperty("wifi描述")
    private String wifiText;
    @ApiModelProperty("wifi描述的字体颜色")
    private String wifiTextColor;
    @ApiModelProperty("wifi密码")
    private String wifiPassword;
    @ApiModelProperty("wifi密码字体颜色")
    private String wifiPasswordColor;
    @ApiModelProperty("赞助商字体的颜色")
    private String sponsorsTextColor;
    @ApiModelProperty("赞助商名字")
    private String sponsorsText;
    @ApiModelProperty("区域是否显示:0/否,1/是")
    private String areaShow;
    @ApiModelProperty("区域名称")
    private String areaText;
    @ApiModelProperty("区域名称的字体颜色")
    private String areaTextColor;
    @ApiModelProperty("桌号是否显示:0/否,1/是")
    private String tableNumberShow;
    @ApiModelProperty("桌号名称")
    private String tableNumberText;
    @ApiModelProperty("桌号名称的字体颜色")
    private String tableNumberTextColor;
    @ApiModelProperty("二维码")
    private String qrCode;
    @ApiModelProperty("创建者GUID")
    private String createStaffGuid;
    @ApiModelProperty("更新者GUID")
    private String updateStaffGuid;
    @ApiModelProperty("是否启用/上架:0/否,1/是")
    private String isEnable;
    @ApiModelProperty("是否删除")
    private String isDelete;
    @ApiModelProperty("创建时间")
    private Long createTime;
    @ApiModelProperty("更新时间")
    private Long updateTime;
    @ApiModelProperty("分类名称")
    private String categoryName;
    @ApiModelProperty("缩略图")
    private String previewImg;
    @ApiModelProperty("是否是模板，仅用于业务层操作")
    private Integer isModel;
}
