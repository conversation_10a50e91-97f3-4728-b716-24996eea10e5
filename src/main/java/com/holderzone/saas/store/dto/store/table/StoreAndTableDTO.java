package com.holderzone.saas.store.dto.store.table;

import com.holderzone.saas.store.dto.table.TableBasicDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description 门店和桌台DTO
 * @date 2022/3/15 17:10
 * @className: StoreAndTableDTO
 */
@Data
@ApiModel("门店和桌台DTO")
@Accessors(chain = true)
public class StoreAndTableDTO {

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "桌台列表")
    private List<String> tableGuidList;
}
