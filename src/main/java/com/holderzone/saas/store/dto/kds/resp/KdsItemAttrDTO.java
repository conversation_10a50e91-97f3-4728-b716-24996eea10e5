package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class KdsItemAttrDTO implements Serializable {

    private static final long serialVersionUID = 8948933316030590201L;

    @NotBlank(message = "属性Guid不得为空")
    @ApiModelProperty(value = "属性Guid", required = true)
    private String attrGuid;

    @NotBlank(message = "属性名称不得为空")
    @ApiModelProperty(value = "属性名称", required = true)
    private String attrName;

    @NotNull(message = "属性数量不得为空")
    @ApiModelProperty(value = "属性数量", required = true)
    private Integer attrNumber;
}