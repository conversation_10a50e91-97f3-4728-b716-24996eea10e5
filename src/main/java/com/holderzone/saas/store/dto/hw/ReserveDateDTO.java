package com.holderzone.saas.store.dto.hw;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveDateDTO
 * @date 2019/04/26 18:29
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "预定请求")
public class ReserveDateDTO {

    @NotEmpty(message = "商户电话号码不得为空")
    @ApiModelProperty(value = "商户电话号码", required = true)
    private String merchantPhone;

    @NotNull(message = "预定日期不得为空")
    @JsonFormat(pattern = "yyyyMMdd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @ApiModelProperty(value = "预定日期：如 20190516", required = true)
    private LocalDate reserveDate;

    @Nullable
    @ApiModelProperty(value = "桌台类型：0=大厅，1=包房")
    private String roomType;

    @Nullable
    @ApiModelProperty(value = "就餐人数：1-99")
    private String peopleTotal;

}