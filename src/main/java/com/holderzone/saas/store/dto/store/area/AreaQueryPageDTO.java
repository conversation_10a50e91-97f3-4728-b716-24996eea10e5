package com.holderzone.saas.store.dto.store.area;

import com.holderzone.saas.store.dto.common.PageQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreQueryPageDTO
 * @date 2018/07/23 下午5:23
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class AreaQueryPageDTO extends PageQueryDTO implements Serializable {

    private static final long serialVersionUID = 2425386300416013820L;

    /**
     * 门店guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;
}
