package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/20
 * @description kds菜品查询
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "kds菜品查询", description = "kds菜品查询")
public class PrdDstItemQueryDTO implements Serializable {

    private static final long serialVersionUID = -6052098912825826685L;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "本次订单商品")
    private List<String> orderItemGuidList;

}
