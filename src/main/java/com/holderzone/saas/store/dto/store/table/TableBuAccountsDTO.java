package com.holderzone.saas.store.dto.store.table;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 *
 * 表扎帐提示消息
 * <AUTHOR>
 */
@Data
public class TableBuAccountsDTO implements Serializable {

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 用户guid
     */
    private String userGuid;

    /**
     * 用户名称
     */
    private String userName;

    /**
     *  桌台信息
     */
    private List<TableStatusDTO> tableStatusDTOS;
}
