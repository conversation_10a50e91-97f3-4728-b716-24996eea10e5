package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
public class MtResponseDTO {

    private static final String STR_OK = "OK";

    public static MtResponseDTO OK = new MtResponseDTO(STR_OK);

    /**
     * 美团响应成功结果
     */
    private String data;

    /**
     * 美团响应失败结果
     */
    private ErrorDetail error;

    public MtResponseDTO(String data) {
        this.data = data;
    }

    public boolean isOK() {
        return STR_OK.equalsIgnoreCase(data);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ErrorDetail implements Serializable {

        private static final long serialVersionUID = 5137562078692852835L;

        /**
         * 错误码
         */
        private String code;

        /**
         * 错误类型
         */
        private String error_type;

        /**
         * 错误详情
         */
        private String message;
    }
}
