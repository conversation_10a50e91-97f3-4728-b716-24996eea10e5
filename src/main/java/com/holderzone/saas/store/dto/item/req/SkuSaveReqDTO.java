package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SkuSaveReqDTO
 * @date 2019/01/08 上午10:08
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class SkuSaveReqDTO implements Serializable {


    private String itemGuid;

    @ApiModelProperty(value = "规格GUID")
    private String skuGuid;

    @ApiModelProperty(value = "规格名称")
    @Size(max = 20)
    private String name;

    @ApiModelProperty(value = "售卖价格")
    @NotNull
    @DecimalMin(value = "0")
    @DecimalMax(value = "99999.99")
    private BigDecimal salePrice;

    @ApiModelProperty("外卖价格")
    @DecimalMin(value = "0")
    @DecimalMax(value = "99999.99")
    private BigDecimal takeawayPrice;

    @ApiModelProperty("外卖会员价格")
    @DecimalMin(value = "0")
    @DecimalMax(value = "99999.99")
    private BigDecimal takeawayMemberPrice;

    @ApiModelProperty(value = "成本价格")
    @DecimalMin(value = "0")
    @DecimalMax(value = "99999.99")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "会员价格")
    @DecimalMin(value = "0")
    @DecimalMax(value = "99999.99")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "计数单位")
    @NotEmpty
    @Size(min = 1, max = 10)
    private String unit;

    @ApiModelProperty(value = "计数单位code(DishUnitEnum)")
    private Integer unitCode;

    @ApiModelProperty(value = "是否参与会员折扣（0：否，1：是）")
    private Integer isMemberDiscount;

    @ApiModelProperty(value = "是否加入整单折扣(0：否，1：是)")
    private Integer isWholeDiscount;

    @ApiModelProperty(value = "是否上架(0：否，1：是)")
    private Integer isRack;

    @ApiModelProperty(value = "是否上架一体机（0：否，1：是）")
    private Integer isJoinAio;

    @ApiModelProperty(value = "是否上架POS机（0：否，1：是）")
    private Integer isJoinPos;

    @ApiModelProperty(value = "是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "商品条码upc")
    private String upc;

    @ApiModelProperty(value = "起卖数(非称重即为整数，称重即为小数)")
    @NotNull
    @DecimalMin(value = "0.001")
    @DecimalMax(value = "999.999")
    private BigDecimal minOrderNum;

    @ApiModelProperty(value = "当日库存")
    private BigDecimal dailyStock;

    @ApiModelProperty(value = "期初库存")
    private BigDecimal totalStock;

    @ApiModelProperty(value = "安全库存")
    private BigDecimal safeStock;

    @ApiModelProperty(value = "美团sku")
    private String mtSku;

    @ApiModelProperty(value = "饿了么sku")
    private String elmSku;

    @ApiModelProperty(value = "是否参与自助点餐机（0：否，1：是）")
    private Integer isJoinBuffet;

    @ApiModelProperty(value = "是否参与微信点餐（0：否，1：是）")
    private Integer isJoinWeChat;

    @ApiModelProperty(value = "是否参与美团外卖（0：否，1：是）")
    private Integer isJoinMt;

    @ApiModelProperty(value = "是否参与饿了么外卖（0：否，1：是）")
    private Integer isJoinElm;

    @ApiModelProperty(value = "是否开启库存(0：否，1：是)")
    private Integer isOpenStock;

    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "sku类型：（0：门店自己创建的sku，1：品牌自己创建的sku,2:被推送过来的sku）")
    private Integer skuFrom;

    @ApiModelProperty(value = "是否参与小程序商城（冗余小程序端字段）")
    private Integer isJoinMiniAppMall;

    @ApiModelProperty(value = "是否参与小程序外卖（冗余小程序端字段）")
    private Integer isJoinMiniAppTakeaway;

    @ApiModelProperty(value = " 是否支持堂食（冗余小程序端字段）")
    private Integer isJoinStore;

    @ApiModelProperty(value = "销售总量（冗余小程序端字段）")
    private Integer totalSale;

    @ApiModelProperty(value = "月销售量（冗余小程序端字段）")
    private Integer monthlySale;

    @ApiModelProperty(value = "外卖打包费（冗余小程序端字段）")
    private BigDecimal takeawayPackageFee;

    @ApiModelProperty(value = "商城打包费（冗余小程序端字段）")
    private BigDecimal mallPackageFee;

    @ApiModelProperty(value = "是否自动置满库存（冗余小程序端字段）")
    private Boolean isFull;

    @ApiModelProperty(value = "置满时间（冗余小程序端字段）")
    private Integer fullTime;

    @ApiModelProperty(value = "小程序端折扣（冗余小程序端字段）")
    private Double discount;

    @ApiModelProperty(value = "sku（冗余小程序端字段）")
    private String sku;

    @ApiModelProperty(value = "是否上架Kds（0：否，1：是）")
    private Integer isJoinKds;

    /**
     * 核算价
     */
    @ApiModelProperty("堂食核算价")
    private BigDecimal accountingPrice;

    @ApiModelProperty("外卖核算价")
    private BigDecimal takeawayAccountingPrice;
}
