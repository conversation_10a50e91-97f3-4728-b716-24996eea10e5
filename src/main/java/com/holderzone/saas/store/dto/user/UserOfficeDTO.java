package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("用户职位DTO")
public class UserOfficeDTO implements Serializable {

    private static final long serialVersionUID = 7369581449362446243L;

    @ApiModelProperty(value = "请求值、响应值：用户职位编码")
    private Integer code;

    @ApiModelProperty(value = "响应值：用户职位名称")
    private String name;
}
