package com.holderzone.saas.store.dto.business.manage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.user.UserOrgDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className HandoverPayQueryDTO
 * @date 18-9-11 下午7:46
 * @description 指定时间（班次）内的收入信息查询DTO
 * @program holder-saas-store-dto
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class HandoverPayQueryDTO {

    /**
     * 门店guid
     */
    @NotBlank
    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "操作人guid")
    private String userGuid;

    @ApiModelProperty(value = "操作人name")
    private String userName;

    /**
     * 创建时间（开班时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间（开班时间）")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间（交班时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("修改时间（交班时间）")
    private LocalDateTime gmtModified;

    /**
     * 员工账号
     */
    private String account;

    @ApiModelProperty(value = "操作人guids")
    private List<String> userGuids;

    @ApiModelProperty(value = "当班员工集合")
    private List<UserOrgDTO> userOrgDTOList;
}
