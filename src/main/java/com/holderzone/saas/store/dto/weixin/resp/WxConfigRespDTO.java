package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxConfigRespDTO
 * @date 2019/05/17 16:12
 * @description 微信品牌配置信息相应DTO
 * @program holder-saas-store
 */
@ApiModel("微信品牌配置信息相应DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxConfigRespDTO {
    private WxCommonReqDTO wxCommonReqDTO;
    @ApiModelProperty("品牌名字")
    private String brandName;
    @ApiModelProperty("appId")
    private String appId;
    @ApiModelProperty("企业guid")
    private String enterpriseGuid;
}
