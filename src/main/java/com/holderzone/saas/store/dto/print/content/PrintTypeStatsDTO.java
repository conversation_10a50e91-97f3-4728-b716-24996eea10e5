package com.holderzone.saas.store.dto.print.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("分类销售统计单")
public class PrintTypeStatsDTO extends PrintStatsDTO {

    @Valid
    @NotEmpty(message = "分类销售列表不能为空")
    @ApiModelProperty(value = "分类销售列表", required = true)
    private List<ItemType> itemTypeList;

    @Data
    public static class ItemType {

        @NotBlank(message = "该分类名称不能为空")
        @ApiModelProperty(value = "该分类名称", required = true)
        private String name;

        @NotNull(message = "该分类销售数量不能为空")
        @ApiModelProperty(value = "该分类销售数量", required = true)
        private BigDecimal quantity;

        @ApiModelProperty(value = "该分类销售数量", required = true)
        private String quantityStr;

        @NotNull(message = "该分类销售金额不能为空")
        @ApiModelProperty(value = "该分类销售金额", required = true)
        private BigDecimal money;

        @ApiModelProperty(value = "该分类销售金额", required = true)
        private String moneyStr;

        @NotNull(message = "该分类销售实付金额不能为空")
        @ApiModelProperty(value = "该分类实付金额", required = true)
        private BigDecimal payMoney;

        @ApiModelProperty(value = "该分类实付金额", required = true)
        private String payMoneyStr;
    }
}
