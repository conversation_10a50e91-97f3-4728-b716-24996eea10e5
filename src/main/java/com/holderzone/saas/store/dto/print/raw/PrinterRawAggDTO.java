package com.holderzone.saas.store.dto.print.raw;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PrinterRawAggDTO implements Serializable {

    private List<PrinterRawDTO> printerList;

    private List<PrinterItemRawDTO> printerItemList;

    private List<PrinterAreaRawDTO> printerAreaList;

    private List<PrinterTableRawDTO> printerTableList;

    private List<PrinterInvoiceRawDTO> printerInvoiceList;

    private List<PrinterFormatRawDTO> printerFormatList;
}
