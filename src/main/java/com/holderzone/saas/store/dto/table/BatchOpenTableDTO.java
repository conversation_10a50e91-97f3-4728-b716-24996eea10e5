package com.holderzone.saas.store.dto.table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * {@link BatchOpenTableDTO}
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/17 11:07
 */
@Data
public class BatchOpenTableDTO {

    @ApiModelProperty("预订桌台信息")
    List<OpenTableDTO> openTableDTOS;


    @ApiModelProperty("预订Guid")
    private String reserveGuid;

    @ApiModelProperty("是否包含菜品")
    private Boolean containDish;

    @ApiModelProperty()
    private BigDecimal reserveAmount;

    @ApiModelProperty("预订手机")
    private String reservePhone;

}