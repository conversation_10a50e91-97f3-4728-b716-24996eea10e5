package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PropertySynSecondRespDTO
 * @date 2019/01/03 下午3:23
 * @description //安桌同步属性详情的返回实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class AttrWebRespDTO implements Serializable {
    private static final long serialVersionUID = 5589200904023786169L;
    @ApiModelProperty(value = "属性详情与商品和属性组关联实体的关联实体GUID",required = true)
    private String attrItemAttrGroupGuid;
    @ApiModelProperty(value = "商品属性详情GUID",required = true)
    private String attrGuid;

    @ApiModelProperty(value = "属性详情名称",required = true)
    private String name;

    @ApiModelProperty(value = "价格",required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "是否默认，1：是，0,否",required = true)
    private Integer isDefault;
    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;
    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;
    @ApiModelProperty(value = "attr来源（0：门店自己创建的attr，1：品牌自己创建的attr,2:被推送过来的attr）")
    private Integer attrFrom;
    @ApiModelProperty(value = "父属性GUID")
    private String parentGuid;

    @ApiModelProperty("是否选中，1：是，0,否")
    private Integer uck;
}
