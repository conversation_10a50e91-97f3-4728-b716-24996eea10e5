package com.holderzone.saas.store.dto.member.response;

import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.saas.store.dto.common.BaseRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description 绑定和登录返回
 * @date 2021/9/9 19:43
 * @className: BindAndLoginRespDTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel("绑定和登录返回")
public class BindAndLoginRespDTO extends BaseRespDTO {

    private static final long serialVersionUID = -535669316555165851L;

    @ApiModelProperty("查询某门店所属体系下的卡列表的返回值DTO")
    private ResponseMemberAndCardInfoDTO memberAndCardInfoDTO;

}
