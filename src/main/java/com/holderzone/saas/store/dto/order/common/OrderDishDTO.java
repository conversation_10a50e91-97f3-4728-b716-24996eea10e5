package com.holderzone.saas.store.dto.order.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderDishDTO
 * @date 2018/09/14 15:48
 * @description 订单菜品DTO
 * @program holder-saas-store-order
 */
@Data
public class OrderDishDTO {

    @ApiModelProperty(value = "订单菜品guid", required = true)
    private String orderDishGuid;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "菜品guid", required = true)
    private String dishGuid;

    @ApiModelProperty(value = "主菜品业务id")
    private String appMainDishId;

    @ApiModelProperty(value = "菜品名称")
    private String dishName;

    @ApiModelProperty(value = "菜品编号")
    private String code;

    @ApiModelProperty(value = "套餐主项的id（orderDishGuid）", required = true)
    private String parentDishGuid;

    @ApiModelProperty(value = "分组GUID", required = true)
    private String subgroupGuid;

    @ApiModelProperty(value = "菜品的套餐类型(0：单规格、1：多规格、2：套餐主项、3：套餐子项、4：称重菜品 )", required = true)
    private Byte packageDishType;

    @ApiModelProperty(value = "菜品的规格", required = true)
    private String skuGuid;

    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty(value = "sku价格")
    private BigDecimal price;

    @ApiModelProperty(value = "会员价格")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "数量", required = true)
    private BigDecimal orderCount;

    @ApiModelProperty(value = "套餐预设数量")
    private Integer packageDefaultCount;

    @ApiModelProperty(value = "app子项点菜数量")
    private Integer appChildDishCount;

    @ApiModelProperty(value = "退菜数量", required = true)
    private Integer returnCount;

    @ApiModelProperty(value = "退菜原因", required = true)
    private String returnReason;

    @ApiModelProperty(value = "计数单位")
    private String unit;

    @ApiModelProperty(value = "计数单位code")
    private Integer unitCode;

    @ApiModelProperty(value = "属性总价")
    private BigDecimal skuPropertyTotal;

    @ApiModelProperty(value = "菜品总价小计")
    private BigDecimal orderDishTotal;

    @ApiModelProperty(value = "是否赠送(0:非赠送、1：赠送)", required = true)
    private Byte isGift;

    @ApiModelProperty(value = "是否已经下单(0:否、1：是)", required = true)
    private Integer isPay;

    @ApiModelProperty(value = "是否参与权益折扣")
    private Byte joinMemberDiscount;

    @ApiModelProperty(value = "是否参与整单折扣")
    private Byte joinWholeDiscount;

    @ApiModelProperty(value = "菜品备注", required = true)
    private String remark;

    @ApiModelProperty(value = "菜品属性")
    private List<SkuPropertyDTO> skuProperties;
}
