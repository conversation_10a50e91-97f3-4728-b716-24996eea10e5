package com.holderzone.saas.store.dto.journaling.resp;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TotalSaleRespDTO
 * @date 2019/10/25 17:25
 * @description 销售统计返回参数
 * @program holder-saas-store
 */
@ApiModel("销售统计返回参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TotalSaleRespDTO {

    public static TotalSaleRespDTO DEFAULT = new TotalSaleRespDTO(0.000, BigDecimal.ZERO, Lists.newArrayList());

    @ApiModelProperty("数量合计")
    private Double totalCount;

    @ApiModelProperty("金额合计")
    private BigDecimal totalFee;

    private List<ItemCountDetailDTO> itemCountDetailDTOS;
}
