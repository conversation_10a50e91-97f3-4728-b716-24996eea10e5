package com.holderzone.saas.store.dto.journaling.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;

@Data
@ApiModel("支付流水统计返回参数")
public class PaySerialStatisticsRespDTO {

    @ApiModelProperty("支付方式Code")
    private Integer payWayCode;

    @ApiModelProperty("支付方式")
    private String payWay;

    @ApiModelProperty("消费金额")
    private BigDecimal consumeAmount = BigDecimal.ZERO;

    @ApiModelProperty("消费次数")
    private int consumeCount = 0;

    @ApiModelProperty("储值金额")
    private BigDecimal storedAmount = BigDecimal.ZERO;

    @ApiModelProperty("储值比数")
    private int storedCount = 0;

    @ApiModelProperty("预定金额")
    private BigDecimal preOrderAmount = BigDecimal.ZERO;

    @ApiModelProperty("预定次数")
    private int preOrderCount = 0;

    @ApiModelProperty("退款金额")
    private BigDecimal refundAmount = BigDecimal.ZERO;

    @ApiModelProperty("退款次数")
    private int refundCount = 0;

    @ApiModelProperty("实际收支")
    private BigDecimal actualInOut = BigDecimal.ZERO;

    @JsonIgnore
    public Pair<Integer, String> getMapKeyPair() {
        return Pair.of(payWayCode, payWay);
    }
}
