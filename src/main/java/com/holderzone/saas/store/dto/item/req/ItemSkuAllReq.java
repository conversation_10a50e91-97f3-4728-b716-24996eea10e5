package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ItemSkuAllReq {
    @ApiModelProperty(value = "门店Guid")
    @NotBlank(message = "门店guid不能为空")
    private String storeGuid;

    /**
     * 是否参与小程序商城（0：否，1：是） （冗余小程序端字段）
     */
    private Integer isJoinMiniAppMall;

    /**
     * 是否参与小程序外卖（0：否，1：是） （冗余小程序端字段）
     */
    private Integer isJoinMiniAppTakeaway;

    /**
     * 是否支持堂食（0：否，1：是） （冗余小程序端字段）
     */
    private Integer isJoinStore;
}
