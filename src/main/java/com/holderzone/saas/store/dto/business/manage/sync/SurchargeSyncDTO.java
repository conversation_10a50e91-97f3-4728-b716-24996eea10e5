package com.holderzone.saas.store.dto.business.manage.sync;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AdditionalFeeDO
 * @date 2018/08/02 下午3:00
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "附加费同步")
public class SurchargeSyncDTO {

    private static final long serialVersionUID = 8091374849599304216L;

    @NotBlank(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid", required = true)
    private String storeGuid;
}

