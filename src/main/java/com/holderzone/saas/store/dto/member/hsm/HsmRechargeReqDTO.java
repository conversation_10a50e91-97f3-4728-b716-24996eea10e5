package com.holderzone.saas.store.dto.member.hsm;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2019/6/19 16:04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "HsmRechargeReqDTO", description = "充值reqDTO")
public class HsmRechargeReqDTO extends BaseDTO {

    @ApiModelProperty(value = "支付guid")
    private String payGuid;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "会员持卡guid")
    private String memberInfoCardGuid;

    @DecimalMin(value = "0.01")
    @ApiModelProperty(value = "充值金额", required = true)
    private BigDecimal rechargeMoney;

    @ApiModelProperty(value = "品牌GUID", required = true)
    private String brandGuid;

    @ApiModelProperty(value = "品牌名称", required = true)
    private String brandName;

    @ApiModelProperty(value = "充值所在门店所在的地址")
    private String storeAddress;

    @ApiModelProperty(value = "充值所在门店所在的地址")
    private String storeTelephone;

    @ApiModelProperty(value = "微信公众号外部地址")
    private String outNotifyUrl;

    @NotNull
    @ApiModelProperty(value = "支付方式，现金 0，聚合 1，银行卡 2，会员 3，扫脸 4，自定义 4+n")
    private Integer payWay;

    @ApiModelProperty(value = "支付方式名字")
    private String payName;

    @ApiModelProperty("支付码")
    private String payCode;

    @NotNull
    @ApiModelProperty("订单（充值）来源，0微信，1一体机,2POS")
    private Integer orderSource;

    @ApiModelProperty("充值/下单时间")
    private Date orderTime;

    /**
     * 联盟id
     */
    @ApiModelProperty("联盟id")
    private String allianceId;

    @ApiModelProperty(value = "食堂卡支付")
    private Boolean canteenCard;

    @ApiModelProperty(value = "msgKey")
    private String msgKey;

    @ApiModelProperty("外部openId")
    private String thirdOpenId;

    @ApiModelProperty("外部appId")
    private String thirdAppId;
}
