package com.holderzone.saas.store.dto.report.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderTransRecordRespDTO
 * @date 2018/09/27 14:22
 * @description 订单交易记录列表数据DTO
 * @program holder-saas-store-report
 */
@Data
@ApiModel(value = "订单交易记录信息")
public class OrderTransRecordRespDTO implements Serializable {

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    @ApiModelProperty(value = "门店名称")
    private String storeName;
    @ApiModelProperty(value = "销售类型，对应订单表：tradeMode 交易模式")
    private String tradeMode;
    @ApiModelProperty(value = "结账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTimestamp;
    @ApiModelProperty(value = "订单取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime cancelTimestamp;
    @ApiModelProperty("订单取消操作人guid")
    private String cancelStaffGuid;
    @ApiModelProperty("订单取消操作人姓名")
    private String cancelStaffName;
    @ApiModelProperty(value = "作废人员姓名/账户")
    private String cancelStaffNameAndGuid;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderFee;
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal totalDiscountFee;
    @ApiModelProperty(value = "实收金额")
    private BigDecimal actuallyPayFee;
    @ApiModelProperty(value = "收银员账号")
    private String operationStaffGuid;
    @ApiModelProperty(value = "收银员姓名")
    private String operationStaffName;
    @ApiModelProperty(value = "收银员姓名/账户")
    private String operationStaffNameAndGuid;

    @ApiModelProperty(value = "是否反结账账单")
    private String recoveryState;
    @ApiModelProperty(value = "反结账uuid")
    private String recoveryUuid;



}
