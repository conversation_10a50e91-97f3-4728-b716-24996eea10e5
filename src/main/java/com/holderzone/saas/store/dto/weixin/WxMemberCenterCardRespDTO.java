package com.holderzone.saas.store.dto.weixin;


import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListOwned;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Api("会员中心卡")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class WxMemberCenterCardRespDTO {

	private List<ResponseMemberCardListOwned> memberCardList;

	@ApiModelProperty(value = "true:有未开通的卡，false：没有未开通的卡")
	private boolean ownCard;

}
