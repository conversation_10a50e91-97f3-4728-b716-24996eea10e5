package com.holderzone.saas.store.dto.print.template.printable;

import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 分隔符，不同于Line，其中间部分可打印文本
 * eg:
 * ----------分隔符----------
 * <p>
 * 输出完成后自动换新行
 *
 * <AUTHOR>
 * @date 18-12-15 14:36
 */
@Data
@ApiModel
@AllArgsConstructor
@Accessors
public class Separator implements Serializable {

    private static final long serialVersionUID = 2477337640051919016L;

    @ApiModelProperty(value = "打印文本")
    private Text text;

    @ApiModelProperty(value = "位于该行打印文本左右两侧的上下文本分隔符，默认是小号不加粗的\"-\"")
    private Text separator = new Text("-");

    @ApiModelProperty(value = "分割线占缩短倍数，如：1=代表占满剩余空间，2=代表占满剩余空间的一半")
    private Integer ratio = 1;

    public Separator() {
        this("");
    }

    public Separator(String text) {
        this(new Text(text, Font.SMALL_BOLD));
    }

    public Separator(Text text) {
        this.text = text;
    }

    public Separator(String text, Integer ratio) {
        this(new Text(text, Font.SMALL_BOLD), ratio);
    }

    public Separator(Text text, Integer ratio) {
        this.text = text;
        this.ratio = ratio;
    }
}
