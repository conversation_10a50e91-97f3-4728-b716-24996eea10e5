package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickOrderRespDTO
 * @date 2019/03/15 16:40
 * @description 微信桌贴模板购买响应DTO
 * @program holder-saas-store
 */
@Data
@ApiModel(value = "微信桌贴模板购买响应DTO")
public class WxStickOrderRespDTO {

    @ApiModelProperty(value = "返回10000表示请求成功")
    private String code;

    @ApiModelProperty(value = "msg信息")
    private String msg;

    @ApiModelProperty(value = "轮询支付结果需要传入")
    private String payGuid;

    @ApiModelProperty(value = "轮询支付结果需要传入")
    private String paymentGuid;

    @ApiModelProperty(value = "二维码链接地址")
    private String codeUrl;

    @ApiModelProperty(value = "2:支付成功，3:支付失败，5：购买失败，10:支付中")
    private String paySt;

    @ApiModelProperty(value = "0：购买中，1:购买成功，2购买失败")
    private Integer buyStatus;

    @ApiModelProperty(value = "模板guid集合")
    private List<String> guidList;

}
