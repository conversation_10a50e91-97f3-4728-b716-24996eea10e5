package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PollingJHPayRespDTO
 * @date 2018/08/15 11:33
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class PollingJHPayRespDTO implements Serializable {

    private static final long serialVersionUID = 6887826567450887459L;

    /**
     * 响应码
     * 成功=10000
     */
    @ApiModelProperty(value = "响应码，成功=10000")
    private String code;

    /**
     * 响应信息
     */
    @ApiModelProperty(value = "响应信息")
    private String msg;

    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    /**
     * 主题
     */
    @ApiModelProperty(value = "主题")
    private String subject;

    /**
     * body
     */
    @ApiModelProperty(value = "body")
    private String body;

    /**
     * 我们自己平台的订单id
     */
    @ApiModelProperty(value = "聚合支付平台订单号")
    private String orderHolderNo;

    /**
     * 支付功能id
     */
    @ApiModelProperty(value = "支付功能id")
    private String payPowerId;

    /**
     * 银行订单编号
     */
    @ApiModelProperty(value = "银行订单编号")
    private String orderNo;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "待支付-0，支付中-1，支付成功-2，支付失败-3，退款-4，已关闭-5，撤销-6，待支付-10")
    private String paySt;

    /**
     * 银行交易流水号
     */
    @ApiModelProperty(value = "银行交易流水号")
    private String bankTransactionId;

    /**
     * 商户自己订单号
     */
    @ApiModelProperty(value = "商户自己订单号")
    private String orderGUID;

    /**
     * 本次支付唯一标识
     */
    @ApiModelProperty(value = "本次支付唯一标识")
    private String payGUID;


    /**
     * 订单创建时间（是）
     * 格式： yyyyMMdd
     */
    @ApiModelProperty(value = "订单创建时间")
    private String orderDt;

    /**
     * 支付完成时间（否）
     * 格式： yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "支付完成时间")
    private String timePaid;

    /**
     * 订单附加描述（否）
     */
    @ApiModelProperty(value = "订单附加描述")
    private String description;

    /**
     * 该笔订单所扣费用，单位分（是）
     */
    @ApiModelProperty(value = "该笔订单所扣费用，单位分")
    private String fee;

    /**
     * 商户下单时所传（否）
     */
    @ApiModelProperty(value = "商户下单时所传")
    private String extra;

    /**
     * 平台订单创建时间（否）
     * 商户下单的时间，格式：
     * yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "平台订单创建时间")
    private String created;

    /**
     * 支付渠道Id（是）
     */
    @ApiModelProperty(value = "支付渠道Id")
    private String payChannelId;

    /**
     * 给商户透传字段
     */
    @ApiModelProperty(value = "附加数据，透传字段")
    private String attachData;

    @ApiModelProperty(value = "二维码链接")
    private String codeUrl;

    @ApiModelProperty(value = "微信公众号或者小程序，支付唤起参数")
    private String prepayInfo;
    /**
     * 签名
     */
    @ApiModelProperty(value = "签名")
    private String signature;

    public PollingJHPayRespDTO() {
    }

    public PollingJHPayRespDTO(Builder builder) {
        this.code = builder.code;
        this.msg = builder.msg;
        this.paySt = builder.paySt;
        this.orderGUID = builder.orderGUID;
        this.payGUID = builder.payGUID;
    }

    public static class Builder {

        private String code;

        private String msg;

        private String paySt;

        private String orderGUID;

        private String payGUID;

        public static Builder Builder() {
            return new Builder();
        }

        public Builder code(String code) {

            this.code = code;
            return this;
        }

        public Builder msg(String msg) {
            this.msg = msg;
            return this;
        }

        public Builder orderGUID(String orderGUID) {
            this.orderGUID = orderGUID;
            return this;
        }

        public Builder payGUID(String payGUID) {
            this.payGUID = payGUID;
            return this;
        }

        public Builder paySt(String paySt) {
            this.paySt = paySt;
            return this;
        }

        public PollingJHPayRespDTO build() {
            return new PollingJHPayRespDTO(this);
        }
    }

}
