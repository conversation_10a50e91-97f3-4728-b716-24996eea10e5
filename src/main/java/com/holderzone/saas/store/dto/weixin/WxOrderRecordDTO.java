package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WxOrderRecordDTO {

	private String guid;

	private String orderGuid;

	private String merchantGuid;

	private String userRecordGuid;

	private String brandGuid;

	private String storeGuid;

	private String areaGuid;

	private String tableGuid;

	private LocalDateTime gmtCreate;

	private LocalDateTime gmtModified;

	private Integer orderMode;

	private String logUrl;

	private Integer orderState;

	private String orderStateName;

	private BigDecimal actuallyPayFee;
}
