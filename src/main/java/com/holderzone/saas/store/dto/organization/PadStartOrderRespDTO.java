package com.holderzone.saas.store.dto.organization;

import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className PadStartOrderRespDTO
 * @date 21-8-2 下午5:25
 * @description pad开始点餐返回dto
 */
@ApiModel("pad开始点餐返回dto")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PadStartOrderRespDTO {

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 品牌logo
     */
    @ApiModelProperty(value = "品牌logo")
    private String brandLogoUrl;

    /**
     * 微信点餐门店配置列表响应DTO
     */
    @ApiModelProperty(value = "微信点餐门店配置列表响应DTO")
    private WxOrderConfigDTO wxOrderConfigDTO;

    @ApiModelProperty(value = "能否使用积分")
    private Boolean canUseIntegral;

}
