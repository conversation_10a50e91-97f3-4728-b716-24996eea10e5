package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintTicketRespDTO
 * @date 2018/10/15 16:47
 * @description
 * @program holder-saas-store-dto
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PrintTicketRespDTO {

    @ApiModelProperty(value = "外卖订单来源")
    private String takeawayType;

    @ApiModelProperty(value = "第三方平台订单号")
    private String orderId;

    @ApiModelProperty(value = "门店名称")
    private String storeGuid;

    @ApiModelProperty(value = "是否在线支付  1=在线支付  0=线下付款")
    private Integer isOnlinePay;

    @ApiModelProperty(value = "送餐时间")
    private LocalDateTime sendMealTime;

    @ApiModelProperty(value = "下单时间")
    private LocalDateTime createOrderTime;

    @ApiModelProperty(value = "门店名称")
    private String orderName;

    @ApiModelProperty(value = "备注")
    private String remake;

    @ApiModelProperty(value = "商品总额")
    private BigDecimal dishesTotal;

    @ApiModelProperty(value = "总价,包括：菜品(原价)+餐盒+配送")
    private BigDecimal total;

    @ApiModelProperty(value = "餐盒费")
    private BigDecimal packageFeeTotal;

    @ApiModelProperty(value = "配送费")
    private BigDecimal shippingFeeTotal;

    @ApiModelProperty(value = "顾客实际支付金额")
    private BigDecimal customerActualPay;

    @ApiModelProperty(value = "顾客姓名")
    private String customerName;

    @ApiModelProperty(value = "顾客地址")
    private String customerAddress;

    @ApiModelProperty(value = "顾客手机号")
    private String customerPhone;

    @ApiModelProperty(value = "操作员")
    private String stafName;


    @ApiModelProperty(value = "商品")
    private List<PrintTicketRespDTO.OrderDish> orderDishes;

    public String getTakeawayType() {
        return takeawayType;
    }

    public void setTakeawayType(String takeawayType) {
        this.takeawayType = takeawayType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public Integer getIsOnlinePay() {
        return isOnlinePay;
    }

    public void setIsOnlinePay(Integer isOnlinePay) {
        this.isOnlinePay = isOnlinePay;
    }

    public LocalDateTime getSendMealTime() {
        return sendMealTime;
    }

    public void setSendMealTime(LocalDateTime sendMealTime) {
        this.sendMealTime = sendMealTime;
    }

    public LocalDateTime getCreateOrderTime() {
        return createOrderTime;
    }

    public void setCreateOrderTime(LocalDateTime createOrderTime) {
        this.createOrderTime = createOrderTime;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public String getRemake() {
        return remake;
    }

    public void setRemake(String remake) {
        this.remake = remake;
    }

    public BigDecimal getDishesTotal() {
        return dishesTotal;
    }

    public void setDishesTotal(BigDecimal dishesTotal) {
        this.dishesTotal = dishesTotal;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public BigDecimal getPackageFeeTotal() {
        return packageFeeTotal;
    }

    public void setPackageFeeTotal(BigDecimal packageFeeTotal) {
        this.packageFeeTotal = packageFeeTotal;
    }

    public BigDecimal getShippingFeeTotal() {
        return shippingFeeTotal;
    }

    public void setShippingFeeTotal(BigDecimal shippingFeeTotal) {
        this.shippingFeeTotal = shippingFeeTotal;
    }

    public BigDecimal getCustomerActualPay() {
        return customerActualPay;
    }

    public void setCustomerActualPay(BigDecimal customerActualPay) {
        this.customerActualPay = customerActualPay;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerAddress() {
        return customerAddress;
    }

    public void setCustomerAddress(String customerAddress) {
        this.customerAddress = customerAddress;
    }

    public String getCustomerPhone() {
        return customerPhone;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    public String getStafName() {
        return stafName;
    }

    public void setStafName(String stafName) {
        this.stafName = stafName;
    }

    public List<OrderDish> getOrderDishes() {
        return orderDishes;
    }

    public void setOrderDishes(List<OrderDish> orderDishes) {
        this.orderDishes = orderDishes;
    }

    public List<OrderDiscount> getOrderDiscountList() {
        return orderDiscountList;
    }

    public void setOrderDiscountList(List<OrderDiscount> orderDiscountList) {
        this.orderDiscountList = orderDiscountList;
    }

    public static class OrderDish implements Serializable {

        @ApiModelProperty(value = "商品名称")
        private String dishesName;

        @ApiModelProperty(value = "菜品规格")
        private String dishesSpecs;

        @ApiModelProperty(value = "菜品数量")
        private BigDecimal dishesQuantity;

        @ApiModelProperty(value = "单价")
        private BigDecimal dishesPrice;

        @ApiModelProperty(value = "菜品小计")
        private BigDecimal dishesSubTotal;

        @ApiModelProperty(value = "特殊属性（多元素使用“，”分割开）")
        private String dishesProperty;

        public String getDishesName() {
            return dishesName;
        }

        public void setDishesName(String dishesName) {
            this.dishesName = dishesName;
        }

        public String getDishesSpecs() {
            return dishesSpecs;
        }

        public void setDishesSpecs(String dishesSpecs) {
            this.dishesSpecs = dishesSpecs;
        }

        public BigDecimal getDishesQuantity() {
            return dishesQuantity;
        }

        public void setDishesQuantity(BigDecimal dishesQuantity) {
            this.dishesQuantity = dishesQuantity;
        }

        public BigDecimal getDishesPrice() {
            return dishesPrice;
        }

        public void setDishesPrice(BigDecimal dishesPrice) {
            this.dishesPrice = dishesPrice;
        }

        public BigDecimal getDishesSubTotal() {
            return dishesSubTotal;
        }

        public void setDishesSubTotal(BigDecimal dishesSubTotal) {
            this.dishesSubTotal = dishesSubTotal;
        }

        public String getDishesProperty() {
            return dishesProperty;
        }

        public void setDishesProperty(String dishesProperty) {
            this.dishesProperty = dishesProperty;
        }
    }


    @ApiModelProperty(value = "订单优惠活动数据")
    private List<PrintTicketRespDTO.OrderDiscount> orderDiscountList;


    public static class OrderDiscount{


        @ApiModelProperty(value = "此优惠活动总金额")
        private BigDecimal total;

        @ApiModelProperty(value = "商家承担的折扣部分")
        private BigDecimal enterpriseDiscount;

        @ApiModelProperty(value = "外卖平台承担的折扣部分")
        private BigDecimal platformTotal;

        @ApiModelProperty(value = "第三方承担的部分")
        private BigDecimal otherTotal;

        public BigDecimal getTotal() {
            return total;
        }

        public void setTotal(BigDecimal total) {
            this.total = total;
        }

        public BigDecimal getEnterpriseDiscount() {
            return enterpriseDiscount;
        }

        public void setEnterpriseDiscount(BigDecimal enterpriseDiscount) {
            this.enterpriseDiscount = enterpriseDiscount;
        }

        public BigDecimal getPlatformTotal() {
            return platformTotal;
        }

        public void setPlatformTotal(BigDecimal platformTotal) {
            this.platformTotal = platformTotal;
        }

        public BigDecimal getOtherTotal() {
            return otherTotal;
        }

        public void setOtherTotal(BigDecimal otherTotal) {
            this.otherTotal = otherTotal;
        }
    }

}
