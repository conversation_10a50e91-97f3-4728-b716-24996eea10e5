package com.holderzone.saas.store.dto.order.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderSourceRespDTO
 * @date 2018/10/09 11:55
 * @description //TODO
 * @program holder-saas-store-member
 */

public class OrderSourceRespDTO {
  @ApiModelProperty(value = "订单来源code")
  private Integer code;
  @ApiModelProperty(value = "订单来源描述")
    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
