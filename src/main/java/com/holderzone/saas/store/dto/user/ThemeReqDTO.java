package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className RoleDTO
 * @date 19-1-15 下午2:08
 * @description 角色DTO
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@ApiModel("产品主题DTO")
public class ThemeReqDTO implements Serializable {

    private static final long serialVersionUID = 7941342064924506403L;

    @Nullable
    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @NotBlank(message = "终端编码不能为空")
    @ApiModelProperty(value = "终端编码", required = true)
    private String terminalCode;
}
