package com.holderzone.saas.store.dto.trade;

import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


/**
 * 订单信息
 */
@Data
public class OrderInfoBatchDTO implements Serializable {

    private static final long serialVersionUID = -7296236571760374411L;

    /**
     * 商品信息
     */
    private Map<String, List<DineInItemDTO>> itemMap;

    /**
     * 附加费
     */
    private Map<String, List<SurchargeLinkDTO>> surchargeLinkMap;

    /**
     * 优惠信息
     */
    private Map<String, List<DiscountFeeDetailDTO>> discountFeeDetailMap;

    /**
     * 退款信息
     */
    private Map<String, List<RefundTransactionRecordDetailRespDTO>> refundDetailMap;

    /**
     * 退款信息
     */
    private Map<String, List<MultiMemberDTO>> multiMemberDTOMap;

    /**
     * 订单信息
     */
    Map<String, DineinOrderDetailRespDTO> orderDetailMap;
}
