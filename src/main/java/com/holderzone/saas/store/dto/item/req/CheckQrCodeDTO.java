package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 二维码过期校验请求实体
 * @date 2021/6/3 15:36
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "二维码过期校验请求实体")
public class CheckQrCodeDTO {

    @NotNull
    @ApiModelProperty(value = "方案guid")
    private String planGuid;

    @NotNull
    @ApiModelProperty(value = "二维码guid")
    private String qrCodeGuid;

    @ApiModelProperty("企业guid,头部不传这里必传")
    private String enterpriseGuid;
}
