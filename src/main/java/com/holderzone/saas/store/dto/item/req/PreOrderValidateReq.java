package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 门店一体机等终端 预点餐菜谱方案验证
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class PreOrderValidateReq {

    @ApiModelProperty(value = "门店guid", required = true)
    @NotBlank(message = "门店Guid不能为空")
    private String storeGuid;

}
