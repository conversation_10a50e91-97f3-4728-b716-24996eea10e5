package com.holderzone.saas.store.dto.user;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RoleTerminal
 * @date 18-9-17 下午5:32
 * @description 权限相关DTO
 * @program holder-saas-aggregation-merchant
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RoleTerminalDTO {

    @ApiModelProperty(value = "终端GUID,必传", required = true)
    private String terminalGuid;

    @ApiModelProperty(value = "终端名称", required = true)
    private String terminalName;

    @ApiModelProperty(value = "终端类型 0/app 1/web", required = true)
    private String type;

    @ApiModelProperty(value = "模块列表", required = true)
    private List<ModuleSourceDTO> moduleSourceDTOList;
}
