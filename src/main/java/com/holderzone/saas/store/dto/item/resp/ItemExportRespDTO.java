package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishSynRespDTO
 * @date 2021-05-07 18:31:11
 * @description //商品导出实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "商品导出实体")
public class ItemExportRespDTO implements Serializable {

    @ApiModelProperty(value = "商品GUID")
    private String itemGuid;
    @ApiModelProperty(value = "分类名称")
    private String typeName;
    @ApiModelProperty(value = "商品名称")
    private String name;
    @ApiModelProperty(value = "商品类型")
    private String itemType;
    @ApiModelProperty(value = "商品单位")
    private String skuUnit;
    @ApiModelProperty(value = "售价")
    private BigDecimal skuSalePrice;
    /**
     * 不要输出成本价
     */
    @Deprecated
    @ApiModelProperty(value = "成本价")
    private BigDecimal skuCostPrice;
    @ApiModelProperty(value = "是否整单折扣")
    private String isWholeDiscount;
    @ApiModelProperty(value = "货号")
    private String skuCode;
    @ApiModelProperty(value = "条码")
    private String skuUpc;
    @ApiModelProperty(value = "是否开启库存")
    private String isOpenStock;
    @ApiModelProperty(value = "安全库存")
    private BigDecimal safeNum;
    @ApiModelProperty(value = "库存数量")
    private BigDecimal remainRepertoryNum;
}
