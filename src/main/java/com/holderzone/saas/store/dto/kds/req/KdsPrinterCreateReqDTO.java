package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class KdsPrinterCreateReqDTO implements Serializable {

    private static final long serialVersionUID = -2773042054434843563L;

    @NotEmpty(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @NotNull(message = "打印机名称不得为空")
    @Size(max = 40, message = "打印机名称不得超过40个字符")
    @ApiModelProperty(value = "打印机名称")
    private String printerName;

    @NotEmpty(message = "IP地址不得为空")
    @ApiModelProperty(value = "IP地址")
    private String printerIp;

    @NotNull(message = "端口不得为空，推荐9100")
    @Min(value = 0, message = "端口：0-65535，推荐9100")
    @Max(value = 65535, message = "端口：0-65535，推荐9100")
    @ApiModelProperty(value = "端口：9100")
    private Integer printerPort;

    @NotNull(message = "纸张宽度不得为空")
    @Pattern(regexp = "^(58|80)$", message = "纸张宽度：只支持58、80")
    @ApiModelProperty(value = "纸张宽度：只支持58、80")
    private String pageSize;
}
