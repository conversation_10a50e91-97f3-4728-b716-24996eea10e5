package com.holderzone.saas.store.dto.takeaway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
public class SalesUpdateDTO {

    @ApiModelProperty(value = "小程序端订单ID")
    public long OrderID;

    @ApiModelProperty(value = "订单状态(0待支付;1支付完成;2订单完成）")
    public int OrderStatus;

    @ApiModelProperty(value = "配送方式")
    private int distributionType;

    @ApiModelProperty(value = "门店guid", hidden = true)
    private String storeGuid;

}

