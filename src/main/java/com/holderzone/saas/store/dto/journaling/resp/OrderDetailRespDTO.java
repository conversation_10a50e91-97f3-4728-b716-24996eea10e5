package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/5/8 15:39
 * @description
 */
@Data
@ApiModel(value = "报表-订单明细返回DTO")
public class OrderDetailRespDTO extends OrderDetailReportRespDTO{
    @ApiModelProperty(value = "支付方式key-value")
    private Map<String,String> paymentTypeMap;
}
