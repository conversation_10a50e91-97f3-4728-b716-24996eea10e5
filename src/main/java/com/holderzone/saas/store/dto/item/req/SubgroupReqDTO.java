package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SubgroupSynRespDTO
 * @date 2019/01/03 下午3:30
 * @description //安桌同步套餐的分组返回实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "套餐的分组新增的请求实体")
public class SubgroupReqDTO implements Serializable {

    @ApiModelProperty(value = "套餐分组的唯一标识")
    private String subgroupGuid;

    @ApiModelProperty(value = "分组NAME")
    @Size(max = 20)
    private String name;

    @ApiModelProperty(value = "是否是固定分组：0：否，1：是")
    @NotNull
    private Integer isFixSubgroup;

    @ApiModelProperty(value = "当前分组中的商品的可选择数量（商品可重复被选）：0：顾客不可选择，其余值：最大可选商品次数")
    @Max(value = 99)
    private Integer pickNum;

    @ApiModelProperty(value = "该分组在该套餐内的排序")
    private Integer sort;
    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;
    @ApiModelProperty(value = "subgroup类型：（0：门店自己创建的subgroup，1：品牌自己创建的subgroup,2:被推送过来的subgroup）")
    private Integer subgroupFrom;

    @ApiModelProperty(value = "套餐分组中包含的可选规格集合")
    @NotEmpty(message = "分组下至少包含一个商品规格")
    private List<SubItemSkuReqDTO> subItemSkuList;
}
