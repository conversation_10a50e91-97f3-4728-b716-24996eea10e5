package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 预订单附加费项
 */
@Data
public class PreOrderSurchargeDTO implements Serializable {

    private static final long serialVersionUID = -3013770694365212375L;

    @ApiModelProperty(value = "附加费guid")
    private String surchargeGuid;

    @ApiModelProperty(value = "区域guid")
    private String areaGuid;

    @ApiModelProperty(value = "附加费名称")
    private String name;

    @ApiModelProperty(value = "附加费单价")
    private BigDecimal amount;

    @ApiModelProperty(value = "附加费数量")
    private Integer count;

    @ApiModelProperty(value = "附加费小计")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "附加费类型：0=按人，1=按桌")
    private Integer type;

    @ApiModelProperty(value = "场景：0=正餐，1=快餐")
    private String tradeMode;

    @ApiModelProperty(value = "有效时间")
    private Integer effectiveTime;

}
