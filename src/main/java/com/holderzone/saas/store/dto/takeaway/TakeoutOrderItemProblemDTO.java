package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 外卖修复
 * </p>
 */
@Data
public class TakeoutOrderItemProblemDTO {

    @ApiModelProperty(value = "订单商品明细id")
    private String id;

    private String storeGuid;

    private String itemSku;

    private Integer orderSubType;


    /**
     * 三方唯一标识
     */
    private String thirdSkuId;

    /**
     * 门店商品规格guid
     */
    private String erpItemSkuGuid;

    /**
     * 门店商品名称
     */
    private String erpItemName;

    /**
     * 门店商品单价
     */
    private BigDecimal erpItemPrice;


}
