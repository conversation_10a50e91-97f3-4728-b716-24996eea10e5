package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemExcelTemplateReqDTO
 * @date 2019/2/19 17:16
 * @description 批量导入商品DTO
 * @program holder-saas-store-business
 */
@ApiModel("批量导入商品DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ItemExcelTemplateReqDTO {

    @ApiModelProperty("分类名称")
    private String typeName;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("商品简称 非必填")
    private String nameAbbr;

    @ApiModelProperty(value = "商品类型", notes = "商品类型:2:普通(计数)  3：称重（计重）")
    private String itemType;

    @ApiModelProperty(value = "起卖数", notes = "起卖数(非称重即为整数，如个，称重即为小数，如KG)")
    private BigDecimal minOrderNum;

    @ApiModelProperty(value = "单位", notes = "单位(非称重根据情况而定，如个，称重为KG)")
    private String unit;

    @ApiModelProperty(value = "销售价")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "是否参与整单折扣 0：否 1：是")
    private Integer isWholeDiscount;

    @ApiModelProperty(value = "餐饮版专用字段：会员价")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "餐饮版专用字段：是否上架掌控者设备 0：否  1:是")
    private Integer isRack;

    @ApiModelProperty(value = "是否上架一体机（0：否，1：是）")
    private Integer isJoinAio;

    @ApiModelProperty(value = "是否上架POS机（0：否，1：是）")
    private Integer isJoinPos;

    @ApiModelProperty(value = "是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;

    @ApiModelProperty(value = "餐饮版专用字段：是否上架微信点餐 0：否 1:是")
    private Integer isJoinWechat;

    @ApiModelProperty(value = "餐饮版专用字段：是否上架自助点餐机 0：否 1：是")
    private Integer isJoinBuffet;

    @ApiModelProperty(value = "商超专用字段：货号")
    private String code;

    @ApiModelProperty(value = "商超专用字段：商品条码")
    private String upc;

    @ApiModelProperty(value = "商超专用字段：库存是否开启 0：否 1：是")
    private Integer isOpenStock;

    @ApiModelProperty("商超专用字段：安全库存")
    private BigDecimal safeStock;

    @ApiModelProperty("商超专用字段：库存数量")
    private BigDecimal totalStock;

    @ApiModelProperty("餐饮版专用字段: 商品图片地址")
    private String pictureUrl;


}
