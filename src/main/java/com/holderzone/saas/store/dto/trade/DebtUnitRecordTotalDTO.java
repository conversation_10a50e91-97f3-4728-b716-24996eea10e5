package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR> R
 * @date 2020/12/15 18:53
 * @description
 */
@ApiModel(value = "挂账汇总DTO")
public class DebtUnitRecordTotalDTO extends DebtUnitDropdownListDTO{
    @ApiModelProperty(value = "可用金额")
    private BigDecimal availableFeeTotal;

    @ApiModelProperty(value = "信用额度")
    private BigDecimal creditLimitTotal;

    @ApiModelProperty(value = "未还款挂账金额")
    private BigDecimal debtFeeTotal;

    public BigDecimal getAvailableFeeTotal() {
        return availableFeeTotal;
    }

    public void setAvailableFeeTotal(BigDecimal availableFeeTotal) {
        this.availableFeeTotal = availableFeeTotal;
    }

    public BigDecimal getCreditLimitTotal() {
        return creditLimitTotal;
    }

    public void setCreditLimitTotal(BigDecimal creditLimitTotal) {
        this.creditLimitTotal = creditLimitTotal;
    }

    public BigDecimal getDebtFeeTotal() {
        return debtFeeTotal;
    }

    public void setDebtFeeTotal(BigDecimal debtFeeTotal) {
        this.debtFeeTotal = debtFeeTotal;
    }

    @Override
    public String toString() {
        return "DebtUnitRecordTotalDTO{" +
                "availableFeeTotal=" + availableFeeTotal +
                ", creditLimitTotal=" + creditLimitTotal +
                ", debtFeeTotal=" + debtFeeTotal +
                '}';
    }
}
