package com.holderzone.saas.store.dto.order.inside;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderTableInfoDTO
 * @date 2019/01/16 15:14
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderTableInfoDTO {

    /**
     * 订单全局唯一主键
     */
    private String guid;

    /**
     * 交易模式(0：正餐，1：快餐)
     */
    private Integer tradeMode;

    /**
     * 订单金额（商品总额+附加费）
     */

    private BigDecimal orderFee;

    /**
     * 1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废
     */
    private Integer state;

    /**
     * 1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废
     */
    private String stateName;

    /**
     * 预结单打印次数
     */
    private Integer printPreBillNum;

    /**
     * 客人数
     */
    private Integer guestCount;

    /**
     * 0:无并单，1:主单， 2:子单
     */
    private Integer upperState;

    /**
     * 主单guid
     */
    private String mainOrderGuid;

    /**
     * 并桌金额（只用于并桌情况加载桌台页面的金额展示）
     */
    private BigDecimal orderFeeForCombine;

    /**
     * 会员电话
     */
    private String memberPhone;

    /**
     * 会员guid
     */
    private String memberGuid;


    public OrderTableInfoDTO(BigDecimal orderFee, Integer printPreBillNum, Integer state) {
        this.orderFee = orderFee;
        this.printPreBillNum = printPreBillNum;
        this.state = state;
    }
}
