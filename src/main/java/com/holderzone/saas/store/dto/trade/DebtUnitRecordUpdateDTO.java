package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR> R
 * @date 2020/12/17 10:05
 * @description
 */
@ApiModel(value = "挂账还款更新基础DTO")
public class DebtUnitRecordUpdateDTO {

    @ApiModelProperty(value = "主键")
    private String guid;

    @ApiModelProperty(value = "还款金额")
    private BigDecimal repaymentFee;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public BigDecimal getRepaymentFee() {
        return repaymentFee;
    }

    public void setRepaymentFee(BigDecimal repaymentFee) {
        this.repaymentFee = repaymentFee;
    }
}
