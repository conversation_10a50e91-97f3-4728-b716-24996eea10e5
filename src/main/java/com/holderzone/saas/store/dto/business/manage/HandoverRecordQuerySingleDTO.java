package com.holderzone.saas.store.dto.business.manage;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverRecordCreateDTO
 * @date 2018/07/29 上午11:39
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class HandoverRecordQuerySingleDTO extends BaseDTO {
    private static final long serialVersionUID = -3952836273481696291L;

    /**
     * 交接班guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "交接班guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "交接班guid", required = true)
    private String handoverRecordGuid;
}
