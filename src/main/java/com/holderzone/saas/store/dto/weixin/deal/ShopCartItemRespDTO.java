package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@ApiModel("购物车商品项")
@Builder
@Data
@Accessors(chain = true)
public class ShopCartItemRespDTO {

	@ApiModelProperty(value = "商品id")
	private String itemGuid;

	@ApiModelProperty(value = "商品项名称")
	private String itemName;

	@ApiModelProperty(value = "类型id")
	private Integer itemTypeGuid;

	@ApiModelProperty(value = "商品类型:1.套餐，2.多规格，3.称重,4,单品,5,固定套餐")
	private Integer itemType;

	@ApiModelProperty(value = "规格id")
	private Integer skuGuid;

	@ApiModelProperty(value = "规格名称")
	private String skuName;

	@ApiModelProperty(value = "sku价格",hidden = true)
	private BigDecimal skuPrice;

	@ApiModelProperty(value = "起卖数")
	private BigDecimal minOrderNum;

	@ApiModelProperty(value = "true：有会员价")
	private Boolean enablePreferentialPrice;

	@ApiModelProperty(value = "会员价",hidden = true)
	private BigDecimal memberPrice;

	@ApiModelProperty(value = "图片")
	private String pictureUrl;

	@ApiModelProperty(value = "计数单位")
	private String unit;

	@ApiModelProperty(value = "商品选择数量")
	private BigDecimal currentCount;

	@ApiModelProperty(value = "商品小计")
	private BigDecimal itemPrice;

	@ApiModelProperty(value = "商品优惠价")
	private BigDecimal itemPreferentialPrice;

	@ApiModelProperty(value = "属性组列表")
	private List<ShopAttrDTO> attrList;

	@ApiModelProperty(value = "是否估清 1:否 2:是")
	@Builder.Default
	private Integer isSoldOut = 1;
}
