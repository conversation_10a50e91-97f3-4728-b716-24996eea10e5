package com.holderzone.saas.store.dto.business.manage;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022年11月25日 下午5:24
 * @description 交接班历史数据处理实体
 */
@Data
public class HandoverHistoryHandleDTO implements Serializable {

    private static final long serialVersionUID = 8964061819413412024L;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty("门店guid")
    private String storeGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("查询开始时间")
    private LocalDateTime businessStartDateTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("查询结束时间")
    private LocalDateTime businessEndDateTime;

    @ApiModelProperty("查询限制数量")
    private Integer count;

    @ApiModelProperty("是否新开班")
    private Boolean newHandover;
}
