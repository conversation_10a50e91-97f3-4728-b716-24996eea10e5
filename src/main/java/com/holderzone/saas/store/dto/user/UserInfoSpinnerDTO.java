package com.holderzone.saas.store.dto.user;

import com.holderzone.saas.store.dto.common.UserInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UserInfoSpinnerDTO
 * @date 2019/06/05 10:17
 * @description
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@ApiModel(value = "用户信息spinner")
public class UserInfoSpinnerDTO {

    @ApiModelProperty(value = "用户spinner")
    private UserSpinnerDTO userSpinnerDTO;

    @ApiModelProperty(value = "用户信息")
    private UserInfoDTO userInfoDTO;

    @ApiModelProperty(value = "用户角色列表")
    private List<RoleDTO> roleDTOList;
}
