package com.holderzone.saas.store.dto.deposit.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class MessageRemindReqDTO implements Serializable {

    private static final long serialVersionUID = -2773042054434843563L;


    @ApiModelProperty(value = "门店guid")
    @NotNull(message = "门店guid不得为空")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    @NotNull(message = "门店名称不得为空")
    private String storeName;

    @ApiModelProperty(value = "寄存提醒，0：不提醒，1:提醒")
    @NotNull(message = "寄存提醒不得为空")
    private int depositRemind;

    @ApiModelProperty(value = "取出商品提醒，0：不提醒，1:提醒")
    @NotNull(message = "取出商品提醒不得为空")
    private int getRemind;

    @ApiModelProperty(value = "过期提醒，0：不提醒，1:提醒")
    @NotNull(message = "过期提醒不得为空")
    private int expireRemind;

    @ApiModelProperty(value = "提前天数")
    private int advanceDays;

}
