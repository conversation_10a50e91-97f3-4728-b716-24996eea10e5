package com.holderzone.saas.store.dto.member.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberPayRecordRespDTO
 * @date 2018/09/29 9:28
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class MemberPayRecordRespDTO{


    /**
     * 交易流水号
     */
    @ApiModelProperty(value = "交易流水号")
    private String sequenceNo;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    /**
     * 交易时间
     */
    @ApiModelProperty(value = "交易时间")
    private LocalDateTime transactionTime;

    /**
     * 交易金额
     */
    @ApiModelProperty(value = "交易金额")
    private BigDecimal fee;


    /**
     * 操作人guid
     */
    @ApiModelProperty(value = "操作人/收银员Guid")
    private String staffGuid;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人/收银员姓名")
    private String staffName;



}
