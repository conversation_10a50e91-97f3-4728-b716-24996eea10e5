package com.holderzone.saas.store.dto.order.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.bytebuddy.asm.Advice;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderLogListReqDTO
 * @date 2018/10/08 20:47
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class OrderLogListReqDTO extends BasePageDTO {
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startTime;
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "订单来源 0:一体机，1:美团外卖，2:饿了么外卖，全部-1",required = true)
    private Byte orderSource;
    @ApiModelProperty(value = "订单号")
    private String orderNum;

    @ApiModelProperty(value = "门店Guid",required = true)
    private List<String> storeGuids;

    @ApiModelProperty(value = "交易模式：0:堂食,1:快餐,2:外卖,全部-1", required = true)
    @NotNull(message = "交易模式不能为空")
    private Integer tradeMode;

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Byte getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(Byte orderSource) {
        this.orderSource = orderSource;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }



    public Integer getTradeMode() {
        return tradeMode;
    }

    public List<String> getStoreGuids() {
        return storeGuids;
    }

    public void setStoreGuids(List<String> storeGuids) {
        this.storeGuids = storeGuids;
    }

    public void setTradeMode(Integer tradeMode) {
        this.tradeMode = tradeMode;
    }
}
