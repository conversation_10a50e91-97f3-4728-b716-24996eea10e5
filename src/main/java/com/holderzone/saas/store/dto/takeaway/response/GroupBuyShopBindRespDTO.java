package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class GroupBuyShopBindRespDTO implements Serializable {


    private static final long serialVersionUID = 6784968432096220916L;

    @ApiModelProperty(value = "门店解绑/绑定地址")
    private String url;

}
