package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class OrderRemarkReqDTO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    @NotBlank(message = "订单Guid不得为空")
    @ApiModelProperty("订单Guid")
    private String orderGuid;

    @NotNull(message = "订单备注不得为空")
    @ApiModelProperty("订单备注")
    private String orderRemark;
}
