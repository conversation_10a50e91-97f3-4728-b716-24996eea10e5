package com.holderzone.saas.store.dto.kds.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 更换换菜单
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class KdsPrintChangesItemDTO extends KdsPrintItemDTO {

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "桌台号")
    private String diningTableName;

    @ApiModelProperty(value = "创建人")
    private String createStaffName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "是否撤销套餐换菜")
    private Boolean cancelFlag;

    @ApiModelProperty(value = "原菜品")
    private KdsItemDTO originalKdsItem;

    @ApiModelProperty(value = "更换菜品")
    private List<KdsItemDTO> originalKdsItemList;

    @ApiModelProperty(value = "更换菜品")
    private KdsItemDTO changesKdsItem;

    @ApiModelProperty(value = "更换菜品")
    private List<KdsItemDTO> changesKdsItemList;

    @ApiModelProperty(value = "绑定的商品")
    private List<String> arrayOfItemGuid;
}
