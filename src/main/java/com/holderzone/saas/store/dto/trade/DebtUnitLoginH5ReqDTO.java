package com.holderzone.saas.store.dto.trade;

import com.holderzone.framework.util.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 查询H5页面挂账还款记录请求参数
 *
 * <AUTHOR>
 * @since 2020-12-22
 */
@ApiModel(value = "查询H5页面挂账还款记录登录请求参数")
public class DebtUnitLoginH5ReqDTO extends Page {

    /**
     * 单位编码
     */
    @ApiModelProperty(value = "单位联系电话")
    private String contactTel;

    /**
     * 查询密码
     */
    @ApiModelProperty(value = "查询密码，登录时必传")
    private String password;

    /**
     * 企业guid
     */
    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty(value = "是否还款（默认0，未还款，1已还款）")
    private Integer repaymentStatus;

    public Integer getRepaymentStatus() {
        return repaymentStatus;
    }

    public void setRepaymentStatus(Integer repaymentStatus) {
        this.repaymentStatus = repaymentStatus;
    }

    public String getContactTel() {
        return contactTel;
    }

    public void setContactTel(String contactTel) {
        this.contactTel = contactTel;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getEnterpriseGuid() {
        return enterpriseGuid;
    }

    public void setEnterpriseGuid(String enterpriseGuid) {
        this.enterpriseGuid = enterpriseGuid;
    }
}
