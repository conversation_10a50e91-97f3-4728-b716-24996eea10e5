package com.holderzone.saas.store.dto.takeaway.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 商户后台/数据报表/订单统计 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
@Data
public class BusinessTakeoutOrderRespDTO {

    @ApiModelProperty(value = "订单唯一标识")
    private String guid;

    @ApiModelProperty(value = "订单唯一标识")
    private String storeName;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态")
    private String orderState;

    @ApiModelProperty(value = "订单来源")
    private String orderSource;

    @ApiModelProperty(value = "接单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acceptTime;

    @ApiModelProperty(value = "接单员")
    private String acceptStaffName;

    @ApiModelProperty(value = "姓名")
    private String customerName;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal itemCount;

    @ApiModelProperty(value = "联系电话")
    private String customerPhone;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal total;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal customerActualPay;


}
