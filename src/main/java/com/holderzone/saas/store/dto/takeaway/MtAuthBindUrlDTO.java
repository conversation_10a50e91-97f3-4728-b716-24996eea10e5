package com.holderzone.saas.store.dto.takeaway;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2023-08-29
 * @description 美团第三方授权绑定参数
 */
@Data
public class MtAuthBindUrlDTO {

    @NotNull(message = "业务类型id不得为空")
    private Integer businessId;

    /**
     * 业务类型名称
     */
    private String businessName;

    /**
     * 会员主体guid
     */
    private String operSubjectGuid;

    /**
     * 1绑定或者0解绑
     */
    @NotNull(message = "授权状态不能为空")
    private Integer status;

    public boolean isBind() {
        return status == 1;
    }

    public boolean isUnBind() {
        return status == 0;
    }
}
