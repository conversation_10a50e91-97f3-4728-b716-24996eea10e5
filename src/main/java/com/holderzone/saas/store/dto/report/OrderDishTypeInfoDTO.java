package com.holderzone.saas.store.dto.report;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2018/10/10 下午 14:01
 * @description
 */
public class OrderDishTypeInfoDTO {

    /**
     * 菜品类型
     */
    private String dishType;

    /**
     * 销售金额
     */
    private BigDecimal saleAmount;

    /**
     * 销售额占比
     */
    private Double saleAmountRatio;

    public String getDishType() {
        return dishType;
    }

    public void setDishType(String dishType) {
        this.dishType = dishType;
    }

    public BigDecimal getSaleAmount() {
        return saleAmount;
    }

    public void setSaleAmount(BigDecimal saleAmount) {
        this.saleAmount = saleAmount;
    }

    public Double getSaleAmountRatio() {
        return saleAmountRatio;
    }

    public void setSaleAmountRatio(Double saleAmountRatio) {
        this.saleAmountRatio = saleAmountRatio;
    }
}
