package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY,
        getterVisibility = JsonAutoDetect.Visibility.NONE)
public class MtRespItemSku implements Serializable {

    @ApiModelProperty("美团方菜品sku id")
    private String dishSkuId;

    @ApiModelProperty("菜品sku名称，通常跟菜品dishName一致")
    private String dishSkuName;

    @ApiModelProperty("ERP方菜品sku id")
    private String eDishSkuCode;

    @ApiModelProperty("价格")
    private String price;

    @ApiModelProperty("sku规格")
    private String spec;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("美团方下单时传递的skuId")
    private String mtSkuId;
}
