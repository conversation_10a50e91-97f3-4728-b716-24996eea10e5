package com.holderzone.saas.store.dto.log.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LogListReqDTO
 * @date 2018/09/25 9:18
 * @description 日志列表入参
 * @program holder-saas-store-order
 */
@Data
public class LogListReqDTO extends BasePageDTO{

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startDate;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endDate;

    @ApiModelProperty(value = "操作方式:-1=删除,0=查询,1=增加,2=修改")
    private String method;

    @ApiModelProperty(value = "模块")
    private String module;

    @ApiModelProperty(value = "日志创建者")
    private String createBy;

    @ApiModelProperty(value = "平台类型:商家后台3，收银系统4")
    private String platform;
    @ApiModelProperty(value = "pageSize")
    private Integer pageSize;
    @ApiModelProperty(value = "pageIndex")
    private Integer pageIndex;
}
