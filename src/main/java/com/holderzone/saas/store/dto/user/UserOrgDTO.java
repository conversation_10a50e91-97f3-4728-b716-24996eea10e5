package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("用户所属组织DTO")
public class UserOrgDTO implements Serializable {

    private static final long serialVersionUID = -8588985280307290674L;

    @ApiModelProperty(value = "请求值、响应值：用户所属组织GUID")
    private String guid;

    @ApiModelProperty(value = "响应值：用户所属组织名称")
    private String name;

    @ApiModelProperty(value = "响应值：用户所属组织树，将根节点到当前选择节点的组织名称，以英文短横线进行拼接，如：市场部-直销团队")
    private String orgNameTreeJoined;
}
