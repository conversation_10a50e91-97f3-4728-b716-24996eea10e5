package com.holderzone.saas.store.dto.retail.dinein;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderListReqDTO
 * @date 2018/09/14 16:01
 * @description 订单列表入参
 * @program holder-saas-store-order
 */
@Data
public class RetailOrderListReqDTO extends BasePageDTO {

    @ApiModelProperty(value = "状态(0：未结账， 1：已结账， 2：已作废/退单")
    @NotNull
    private Integer state;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "商品名称/编码")
    private String goodsName;

    @ApiModelProperty(value = "收银人选")
    private List<String> staffGuid;

    @ApiModelProperty(value = "支付方式")
    private List<String> payWay;

    /**
     *  @see {@link com.holderzone.saas.store.retail.entity.enums.OrderTypeEnum}
     */
    @ApiModelProperty(value = "订单类型，1为原单  2为退单")
    private Integer orderType;


}
