package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * https://developer.meituan.com/openapi#5.4
 */

@Data
@ApiModel
@NoArgsConstructor
/**
 * 执行验券后，调用coupon/consume接口返回数据的实体类。
 */
public class MtCouponDoCheckRespDTO {

    @ApiModelProperty(value = "订单号")
    private String orderId;

    @ApiModelProperty(value = "验证券码数组")
    private List<String> couponCodes;

    @ApiModelProperty(value = "项目名称")
    private String dealTitle;

    @ApiModelProperty(value = "券面值")
    private double dealValue;

    @ApiModelProperty(value = "项目ID")
    private int dealId;

    @ApiModelProperty(value = "返回消息")
    private String message;

    @ApiModelProperty(value = "美团门店ID")
    private String poiid;

    @ApiModelProperty(value = "操作状态")
    private int result;

    @ApiModelProperty(value = "开店宝设置的团购名字段（仅套餐）")
    private String rawTitle;

    @ApiModelProperty(value = "是否代金券,true代表代金券,false代表套餐券")
    private Boolean isVoucher;

    @ApiModelProperty(value = "券码渠道 买单:1004")
    private Integer receiptChannel;

    @ApiModelProperty(value = "一键买单券信息")
    private MtMaitonConsumeDTO maitonConsumeDTO;
}
