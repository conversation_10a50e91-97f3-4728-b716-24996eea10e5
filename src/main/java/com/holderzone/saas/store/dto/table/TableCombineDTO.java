package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableCombineDTO
 * @date 2019/01/07 9:42
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class TableCombineDTO extends BaseDTO {

    @ApiModelProperty("桌位信息")
    private List<String> tableGuidList;

    @ApiModelProperty("并桌次数")
    private Integer combineTimes;

    @ApiModelProperty("主桌订单号")
    private String mainOrderGuid;

    @ApiModelProperty("主桌桌台号")
    @LockField
    private String mainTableGuid;

}
