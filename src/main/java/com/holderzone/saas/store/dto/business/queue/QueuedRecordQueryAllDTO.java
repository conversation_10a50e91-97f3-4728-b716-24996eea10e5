package com.holderzone.saas.store.dto.business.queue;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueuedRecordCreateDTO
 * @date 2018/07/27 下午6:29
 * @description 查询当前营业日所有排队记录
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueuedRecordQueryAllDTO {

    /**
     * 门店guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店名称不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    /**
     * 排队类型guid
     */
    @ApiModelProperty("排队类型guid，为空则查询所有类型")
    private String queuedTypeGuid;
}
