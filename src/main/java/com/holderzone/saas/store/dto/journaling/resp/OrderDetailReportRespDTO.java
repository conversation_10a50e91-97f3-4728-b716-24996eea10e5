package com.holderzone.saas.store.dto.journaling.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderDetailReportRespDTO
 * @date 2019/05/28 16:42
 * @description 报表-订单明细响应DTO
 * @program holder-saas-store
 */
@Data
@ApiModel(value = "报表-订单明细响应DTO")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderDetailReportRespDTO {

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "状态(1：未结账， 2：已结账， 3：已退款，4：已作废)")
    private String stateName;

    @ApiModelProperty(value = "用餐类型(0：正餐，1：快餐)")
    private String tradeModeName;

    @ApiModelProperty(value = "就餐人数")
    private Integer guestCount;

    @ApiModelProperty(value = "订单金额（商品总额+附加费）")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "附加费")
    private BigDecimal appendFee;

    @ApiModelProperty(value = "商品总额")
    private BigDecimal itemTotalFee;

    @ApiModelProperty(value = "优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "系统省零")
    private BigDecimal savingZero;

    @ApiModelProperty(value = "会员折扣")
    private BigDecimal memberDiscount;

    @ApiModelProperty(value = "会员优惠券")
    private BigDecimal memberCoupons;

    @ApiModelProperty(value = "菜品赠送")
    private BigDecimal itemPresent;

    @ApiModelProperty(value = "整单让价")
    private BigDecimal fullSinglePrice;

    @ApiModelProperty(value = "团购券")
    private BigDecimal groupCoupons;

    @ApiModelProperty(value = "整单折扣")
    private BigDecimal wholeDiscount;

    @ApiModelProperty(value = "积分抵扣")
    private BigDecimal points;

    @ApiModelProperty(value = "会员价")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "单品折扣")
    private BigDecimal singleDiscount;

    @ApiModelProperty(value = "商品券")
    private BigDecimal goodsGrouper;

    @ApiModelProperty(value = "营销活动")
    private BigDecimal campaign;

    @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "门店名字")
    private String storeName;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "支付方式")
    private List<String> paymentTypeNames;

    @ApiModelProperty(value = "操作人账号")
    private String operationAccount;

    @ApiModelProperty(value = "订单来源")
    private String deviceTypeName;

    private String paymentTypes;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkOutTime;

    {
        this.savingZero = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        this.memberDiscount = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        this.memberCoupons = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        this.itemPresent = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        this.fullSinglePrice = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        this.groupCoupons = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        this.wholeDiscount = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        this.points = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        this.memberPrice = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        this.singleDiscount = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
        this.goodsGrouper = BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP);
    }
}
