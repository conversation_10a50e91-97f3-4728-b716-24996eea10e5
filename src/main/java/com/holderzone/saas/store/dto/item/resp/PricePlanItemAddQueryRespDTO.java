package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/06/01 16:27
 */
@Data
@ApiModel(value = "sku商品列表")
public class PricePlanItemAddQueryRespDTO {

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "商品分类guid")
    private String typeGuid;

    @ApiModelProperty(value = "商品分类名称")
    private String typeName;

    @ApiModelProperty(value = "商品类型")
    private Integer itemType;

    @ApiModelProperty(value = "是否已被选择", example = "是则无法勾选，价格方案导入菜品新增")
    private Boolean isSelected;
}
