package com.holderzone.saas.store.dto.pay;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class AggPayRecordDTO implements Serializable {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "支付功能的id：2=支付宝条码， 9=微信条码支付")
    private String payPowerId;

    @ApiModelProperty(value = "通道名称")
    private String payPowerName;

    @ApiModelProperty(value = "通道ID")
    private String payChannelId;

    @ApiModelProperty(value = "商户自己订单号")
    private String orderGUID;

    @ApiModelProperty(value = "本次支付唯一标识")
    private String payGUID;

    @ApiModelProperty(value = "金额（元）")
    private BigDecimal amount;

    @ApiModelProperty(value = "聚合支付平台订单号")
    private String orderHolderNo;

    @ApiModelProperty(value = "银行订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单支付状态：支付成功=2，支付失败=3，已退款=4")
    private String paySt;

    @ApiModelProperty(value = "银行交易流水号")
    private String bankTransactionId;

    @ApiModelProperty(value = "手机号")
    private String phoneNum;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "结帐时间")
    private LocalDateTime gmtTimePaid;

    @ApiModelProperty(value = "退款单号")
    private String refOrderNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "退款时间")
    private LocalDateTime gmtRefund;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;
}
