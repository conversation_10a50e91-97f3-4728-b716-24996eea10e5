package com.holderzone.saas.store.dto.print.content.retail;

import com.holderzone.saas.store.dto.print.content.PrintBaseItemDTO;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 结账单
 *
 * <AUTHOR>
 * @date 2018/09/19 9:27
 */
@Data
@NoArgsConstructor
@ApiModel("结账单")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PrintRetailCheckOutDTO extends PrintBaseItemDTO {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名")
    private String storeName;

    @Nullable
    @ApiModelProperty(value = "整单备注")
    private String orderRemark;

    @NotBlank(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    private String orderNo;

    @NotNull(message = "开台时间不能为空")
    @ApiModelProperty(value = "开台时间", required = true)
    private Long openTableTime;

    @NotNull(message = "商品总额不能为空")
    @ApiModelProperty(value = "商品总额", required = true)
    private BigDecimal total;

    @Valid
    @Nullable
    @ApiModelProperty(value = "优惠折扣", required = true)
    private List<ReduceRecord> reduceRecordList;

    @NotNull(message = "应付金额不能为空")
    @ApiModelProperty(value = "应付金额", required = true)
    private BigDecimal payAble;

    @Nullable
    @ApiModelProperty(value = "门店地址", notes = "如果未设置门店地址，该值为空")
    private String storeAddress;

    @Nullable
    @ApiModelProperty(value = "门店电话", notes = "如果未设置门店电话，该值为空")
    private String tel;

    @NotNull(message = "结帐时间不能为空")
    @ApiModelProperty(value = "结帐时间", required = true)
    private Long checkOutTime;

    @Valid
    @NotEmpty(message = "支付方式不能为空")
    @ApiModelProperty(value = "支付方式", required = true)
    private List<PayRecord> payRecordList;

    @NotNull(message = "找零不能为空")
    @ApiModelProperty(value = "找零", required = true)
    private BigDecimal changedPay;

    @NotNull(message = "实付金额不能为空")
    @ApiModelProperty(value = "实付金额", required = true)
    private BigDecimal actuallyPay;
}
