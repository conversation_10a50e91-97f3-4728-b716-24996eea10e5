package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Api("会员中心主页")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class WxMemberOverviewReqDTO {

	@ApiModelProperty("0:登录，1：登出")
	private Integer whetherLogin;

	private WxStoreConsumerDTO wxStoreConsumerDTO;
}
