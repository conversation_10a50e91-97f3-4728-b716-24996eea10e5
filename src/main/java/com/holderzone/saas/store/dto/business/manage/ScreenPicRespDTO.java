package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPicRespDTO
 * @date 2018/09/12 17:53
 * @description //TODO
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class ScreenPicRespDTO implements Serializable {

    @ApiModelProperty(value = "oss图片下载路径")
    private String ossUrl;

    @ApiModelProperty(value = "oss图片下载路径")
    private Integer changeMils;

    @ApiModelProperty(value = "guid")
    private String screenPictureGuid;

}
