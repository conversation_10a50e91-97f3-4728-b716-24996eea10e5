package com.holderzone.saas.store.dto.print;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishPrinterDTO
 * @date 2018/7/25 10:11
 * @description 菜品-打印机 dto
 * @program holder-saas-store-print
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("打印机关联的菜品")
public class PrinterItemDTO {

    @ApiModelProperty(value = "菜品GUID", required = true)
    private String itemGuid;

    @ApiModelProperty(value = "菜品名称")
    private String itemName;
}
