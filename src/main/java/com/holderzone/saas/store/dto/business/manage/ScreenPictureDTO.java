package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPictureDTO
 * @date 2018/09/06 17:43
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class ScreenPictureDTO implements Serializable {

    @ApiModelProperty(value = "guid")
    private String screenPictureGuid;

    @ApiModelProperty(value = "切换时间")
    private Integer changeMills;

    @ApiModelProperty(value = "图片下载路径")
    private String ossUrl;

    @ApiModelProperty(value = "门店guid")
    private String selectStoreGuid;

    @ApiModelProperty(value = "门店name")
    private String storeName;

    @ApiModelProperty(value = "图片的基础信息")
    private PictureInfoDTO pictureInfoDTO;

}
