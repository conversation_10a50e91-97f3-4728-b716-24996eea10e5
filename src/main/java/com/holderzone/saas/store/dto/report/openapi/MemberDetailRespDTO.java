package com.holderzone.saas.store.dto.report.openapi;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员信息
 */
@Data
public class MemberDetailRespDTO implements Serializable {

    private static final long serialVersionUID = -1025167689243809355L;

    /**
     * 会员GUID
     */
    private String guid;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 手机号
     */
    private String phoneNum;

}
