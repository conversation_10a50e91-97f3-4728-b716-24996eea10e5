package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年11月25日 下午6:05
 * @description 交接班查询中间对象
 */
@Data
public class HandOverQueryBO implements Serializable {

    private static final long serialVersionUID = -4045145278701492777L;

    @ApiModelProperty(value = "交接班状态 -1:全部 0:未交班 1:已交班")
    private Integer state = -1;

    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("查询开始时间")
    private LocalDateTime businessStartDateTime;

    @ApiModelProperty("查询结束时间")
    private LocalDateTime businessEndDateTime;

    @ApiModelProperty(value = "操作人guids")
    private List<String> userGuids;

    @ApiModelProperty("查询限制数量")
    private Integer count;
}
