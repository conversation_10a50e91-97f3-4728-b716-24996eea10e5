package com.holderzone.saas.store.dto.business.manage;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AddtionalFeeConfigDTO
 * @date 2018/08/01 14:16
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class AddtionalFeeConfigDTO implements Serializable {

    /**
     * 业务主键
     */
    private String addtionalGUID;
    /**
     * @description 附加费配置名
     */
    private String addtionalCostName;
    /**
     * @description 附加费费用值
     */
    private Integer addtionalCostFee;
    /**
     * 序号
     */
    private Integer serialNumber;
    /**
     * @description 0-启用，1-禁用
     */
    private Integer state;
    /**
     * 备注
     */
    private String remark;

}
