package com.holderzone.saas.store.dto.deposit.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class ModifyRemindReqDTO implements Serializable {

    private static final long serialVersionUID = -2773042054434843563L;


    @ApiModelProperty(value = "门店guid")
    @NotNull(message = "门店guid不得为空")
    private String storeGuid;
}
