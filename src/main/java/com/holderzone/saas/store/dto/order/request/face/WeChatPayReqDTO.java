package com.holderzone.saas.store.dto.order.request.face;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FacePayCompensateReqDTO
 * @date 2019/04/08 13:56
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WeChatPayReqDTO extends BaseDTO {

    @ApiModelProperty(value = "订单guid")
    @NotNull
    private String orderGuid;

    @ApiModelProperty(value = "支付记录guid")
    @NotNull
    private String payGuid;

    @ApiModelProperty(value = "支付功能id")
    private String payPowerId;

    @ApiModelProperty(value = "是否成功")
    @NotNull
    private boolean success;

    @ApiModelProperty(value = "交易金额")
    @NotNull
    private BigDecimal amount;

    @ApiModelProperty(value = "订单金额（商品总额+附加费）")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "优惠信息")
    private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    /**
     * 交易流水号
     */
    private String bankTransactionId;

}
