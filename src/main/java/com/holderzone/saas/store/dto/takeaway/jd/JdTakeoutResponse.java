package com.holderzone.saas.store.dto.takeaway.jd;

import com.holderzone.framework.response.ResultEnum;
import lombok.Data;

@Data
public class JdTakeoutResponse<T> {

    private int code;

    private String msg;

    private T data;

    public static JdTakeoutResponse<String> buildEmptySuccess(){
        JdTakeoutResponse<String> response = new JdTakeoutResponse<>();
        response.setCode(ResultEnum.SUCCESS.getResultCode());
        response.setMsg("success");
        response.setData("");
        return response;
    }

}
