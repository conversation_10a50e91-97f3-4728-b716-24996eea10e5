package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.item.common.ItemLogDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemRackDTO
 * @date 2019/02/26 上午9:47
 * @description // 商品批量上下架实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "商品的单个或批量上下架传输对象")
public class ItemRackDTO extends ItemLogDTO {

    @ApiModelProperty(value = "选中商品的GUID集合,单个商品的上下架就装一个商品的GUID到集合中")
    @NotEmpty
    private List<String> itemGuidList;

    @ApiModelProperty(value = "上下架状态 0 下架 1 上架")
    @NotNull
    private Integer rackState;

    @ApiModelProperty("是否加入整单折扣(0：否，1：是)")
    private Integer isWholeDiscount;

    @ApiModelProperty(value = "是否参与小程序商城（0：否，1：是）")
    private Integer isJoinMiniAppMall;

    @ApiModelProperty(value = "是否参与小程序外卖（0：否，1：是）")
    private Integer isJoinMiniAppTakeaway;

    @ApiModelProperty(value = "是否支持堂食（0：否，1：是）")
    private Integer isJoinStore;

    @ApiModelProperty(value = "是否上架一体机（0：否，1：是）")
    private Integer isJoinAio;

    @ApiModelProperty(value = "是否上架POS机（0：否，1：是）")
    private Integer isJoinPos;

    @ApiModelProperty(value = "是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;
}
