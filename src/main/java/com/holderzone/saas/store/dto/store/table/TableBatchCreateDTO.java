package com.holderzone.saas.store.dto.store.table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableDO
 * @date 2018/07/24 上午9:55
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class TableBatchCreateDTO implements Serializable {

    private static final long serialVersionUID = 4190124830932557499L;

    /**
     * 门店guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    /**
     * 区域guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "区域guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "区域guid", required = true)
    private String areaGuid;

    /**
     * 区域名称
     */
    @NotNull
    @Size(min = 1, max = 45, message = "区域名称不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "区域名称", required = true)
    private String areaName;

    /**
     * 桌台名称前缀
     */
    @NotNull
    @Size(min = 1, max = 45, message = "桌台名称前缀不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "桌台名称前缀", required = true)
    private String namePrefix;

    /**
     * 起始桌台编号
     */
    @NotNull
    @Pattern(regexp = "^\\d{1,8}$", message = "起始桌台编号不得为空，且不得超过8个字符")
    @ApiModelProperty(value = "起始桌台编号", required = true)
    private String codeBegin;

    /**
     * 结束桌台编号
     */
    @NotNull
    @Pattern(regexp = "^\\d{1,8}$", message = "起始桌台编号不得为空，且不得超过8个字符")
    @ApiModelProperty(value = "结束桌台编号", required = true)
    private String codeEnd;

    /**
     * 桌台座位数
     */
    @NotNull
    @Min(value = 1, message = "桌台座位数[1-255]")
    @Max(value = 255, message = "桌台座位数[1-255]")
    @ApiModelProperty(value = "桌台座位数", required = true)
    private Integer seats;

    /**
     * 是否已启用
     * 0=未启用
     * 1=已启用
     * 默认1
     */
    @NotNull
    @Min(value = 0, message = "是否启用不得为空，且0=未启用，1=已启用")
    @Max(value = 1, message = "是否启用不得为空，且0=未启用，1=已启用")
    @ApiModelProperty(value = "是否已启用。0=未启用，1=已启用。默认1。", required = true)
    private Integer enable;
}
