package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/29
 * @description 销售分类返回
 */
@Data
@ApiModel(value = "销售分类返回")
@EqualsAndHashCode(callSuper = false)
public class SaleTypeRespDTO implements Serializable {

    private static final long serialVersionUID = 7055261144111003515L;

    @ApiModelProperty(value = "分类Guid")
    private List<String> typeGuidList;

    @ApiModelProperty(value = "商品分类名称")
    private String name;

}
