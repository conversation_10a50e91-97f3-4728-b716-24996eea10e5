package com.holderzone.saas.store.dto.takeaway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
public class TakeoutOwnOrderCancelDTO {

    @ApiModelProperty(value = "第三方订单ID")
    public String orderId;

    @ApiModelProperty(value = "取消原因ID")
    public Integer cancelReasonId;

    @ApiModelProperty(value = "取消原因")
    public String cancelReason;

    @ApiModelProperty(value = "商户编号（创建商户账号分配的编号） 达达测试环境默认为：73753 一城飞客(不需要传)")
    public String sourceId;

}
