package com.holderzone.saas.store.dto.user;

import com.holderzone.saas.store.enums.user.LoginSourceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Deprecated
@NoArgsConstructor
@ApiModel(description = "用户登录DTO")
public class UserLoginDTO implements Serializable {

    private static final long serialVersionUID = 6937348412454976273L;

    @ApiModelProperty(value = "用户名", required = true)
    private String username;

    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @ApiModelProperty("门店编号：一体机登录时必传，Web端登录不传")
    private String storeNo;

//    @JsonProperty("enterpriseUidOrRegTel")
//    @ApiModelProperty("企业GUID或者注册电话号码,web登录时必传")
    @ApiModelProperty("企业编号：Web登录时必传，一体机登录不传")
    private String enterpriseNo;

    /**
     * @see LoginSourceEnum
     */
//    @ApiModelProperty("登录来源:0=web，3=aio，4=pos，5=pad，6=m1，7=p/v1")
//    private String source;
    @ApiModelProperty("登录来源:0=web，10=aio，11=aio_p，12=aio_p_f，20=pos，21=pos_p，22=pos_p_f，30=pad，40=m1")
    private Integer source;

    //    @JsonProperty("vid")
    @ApiModelProperty("验证码id")
    private String captchaId;

    //    @JsonProperty("vCode")
    @ApiModelProperty("验证码code")
    private String captchaCode;

}
