package com.holderzone.saas.store.dto.user;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UpdatePwdDTO
 * @date 18-11-2 下午3:22
 * @description 修改密码DTO
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("更新密码请求实体")
public class UpdatePwdDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -6146751840487015315L;

    @ApiModelProperty(value = "用户GUID（更新下属员工时不能为空）")
    private String guid;

    @Size(min = 6, max = 20, message = "密码必须介于6到20位之间")
    @ApiModelProperty(value = "原密码（更新自己时不能为空，长度介于6到20位之间）")
    private String oldPwd;

    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 20, message = "新密码必须为6位数字")
    @ApiModelProperty(value = "新密码(更新自己及下属员工时不能为空，长度介于6到20位之间)", required = true)
    private String newPwd;
}
