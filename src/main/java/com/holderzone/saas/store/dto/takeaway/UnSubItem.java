package com.holderzone.saas.store.dto.takeaway;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/2/25
 * @description 平台子菜
 */
@Data
@NoArgsConstructor
public class UnSubItem implements Serializable {

    private static final long serialVersionUID = -8089148817676301837L;

    /**
     * ERP端菜品id
     */
    private String appFoodCode;

    /**
     * 菜品名
     */
    private String foodName;

    /**
     * ERP端菜品skuId, 对应菜品映射中的eDishSkuCode
     */
    private String skuId;

    /**
     * 美团方skuId
     */
    private String mtSkuId;

    /**
     * 菜品份数
     */
    private long quantity = 0;

    /**
     * 菜品原价
     * 单位：元
     */
    private double price;

    /**
     * 餐盒总个数
     */
    private long boxNum = 1;

    /**
     * 餐盒费，单价
     * 单位：元
     */
    private double boxPrice;

    /**
     * 单位
     */
    private String unit;

    /**
     * 真实价格
     */
    private double actualPrice;

    /**
     * 菜品规格
     */
    private String spec;

    /**
     * 菜品属性，多个属性用英文逗号隔开
     */
    private String foodProperty;

    private String detailId;

    private String mtSpuId;

    private String mtTagId;

    private double originalPrice;

}
