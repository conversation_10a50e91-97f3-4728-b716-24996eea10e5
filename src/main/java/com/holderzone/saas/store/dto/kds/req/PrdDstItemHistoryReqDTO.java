package com.holderzone.saas.store.dto.kds.req;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PrdDstItemHistoryReqDTO extends BasePageDTO {

    private static final long serialVersionUID = -4247230675662759853L;

    @Nullable
    @ApiModelProperty(value = "精确查找：桌台号")
    private String tableName;

    @Nullable
    @ApiModelProperty(value = "精确查找：订单号")
    private String orderNumber;

    @Nullable
    @ApiModelProperty(value = "模糊查找：菜品名、规格名；精确查找：规格编号")
    private String itemSearchKey;

    @ApiModelProperty(value = "是否允许重复绑定菜品")
    private Boolean allowRepeatFlag;
}
