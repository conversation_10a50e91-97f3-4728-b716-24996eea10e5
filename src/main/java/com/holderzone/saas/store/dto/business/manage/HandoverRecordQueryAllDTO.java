package com.holderzone.saas.store.dto.business.manage;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className
 * @date 2018/07/29 下午12:34
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HandoverRecordQueryAllDTO extends BasePageDTO {
    private static final long serialVersionUID = 8091374849599304216L;

    /**
     * 设备固件编号
     */
    @ApiModelProperty("设备固件编号")
    private String terminalId;

    /**
     * 交接班状态
     */
    @ApiModelProperty("交接班状态")
    private Integer status;

    /**
     * 查询开始时间
     */
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private LocalDateTime startDateTime;

    /**
     * 转换数据库中的偏移index
     *
     * @return 分页偏移量
     */
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    public Integer getOffsetIndex() {
        return this.getPageSize() * (this.getCurrentPage() - 1);
    }
}
