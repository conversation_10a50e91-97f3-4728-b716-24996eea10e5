package com.holderzone.saas.store.dto.queue;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableDTO
 * @date 2019/05/09 11:30
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
@Builder
public class TableDTO {
    private String tableGuid;
    private String tableName;
    private String areaGuid;
    private String areaName;
    private Integer seats;
    private Boolean effective;
}