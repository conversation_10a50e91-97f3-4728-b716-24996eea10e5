package com.holderzone.saas.store.dto.order.request.bill;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.BillMultipleRefundAggPayDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 聚合支付部分退款入参
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BillMultipleRefundAggPayReqDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 3376540300282443607L;

    @ApiModelProperty(value = "订单guid")
    @OrderLockField
    private String orderGuid;

    @ApiModelProperty(value = "version")
    @LockField
    private Integer version;

    @ApiModelProperty(value = "退款明细")
    private List<BillMultipleRefundAggPayDTO> refundAggPays;
}
