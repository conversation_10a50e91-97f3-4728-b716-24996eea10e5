package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishSynRespDTO
 * @date 2019/01/03 下午2:10
 * @description //安桌同步商品的商品返回实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class ItemSynRespDTO implements Serializable {

    @ApiModelProperty(value = "商品唯一标识", required = true)
    private String itemGuid;
    @ApiModelProperty(value = "父商品GUID")
    private String parentGuid;
    @ApiModelProperty(value = "分类GUID", required = true)
    private String typeGuid;
    @ApiModelProperty(value = "分类名称", required = true)
    private String typeName;
    @ApiModelProperty(value = "商品类型:1.套餐（不称重，无规格），2.规格商品（单商品，不称重），3.称重商品（单商品，称重）,4,单品  5:团餐", required = true)
    private Integer itemType;

    @ApiModelProperty(value = "maybe null,是否售罄:0 否 1 是")
    private Integer isSoldOut;

    @ApiModelProperty(value = "属性组状态:0：无属性; 1:有属性; 2:有必选属性组" +
            "若该商品为套餐，则含义为（与商品数据库存储数据不同）：若子商品中有一个有必选属性，则该字段=2,若所有子商品均无属性，则该字段=0,其余=1", required = true)
    private Integer hasAttr;

    @ApiModelProperty(value = "商品名称", required = true)
    @Size(max = 40)
    private String name;
    @ApiModelProperty(value = "拼音简码", required = true)
    private String pinyin;

    @ApiModelProperty(value = "maybe null,别名", required = true)
    private String nameAbbr;

    @ApiModelProperty(value = "排序号", required = true)
    private Integer sort;

    @ApiModelProperty(value = "maybe null,商品描述")
    private String description = StringUtils.EMPTY;

    /**
     * 英文简述
     */
    @ApiModelProperty(value = "英文简述")
    private String englishBrief = StringUtils.EMPTY;

    /**
     * 英文配料描述
     */
    @ApiModelProperty(value = "英文配料描述")
    private String englishIngredientsDesc = StringUtils.EMPTY;

    @ApiModelProperty(value = "maybe null,商品图片地址,列表小图")
    private String pictureUrl = StringUtils.EMPTY;

    /**
     * 图片路径数组json,这里对应新一期加入的“列表大图”
     */
    @ApiModelProperty(value = "商品图片地址，列表大图")
    private String bigPictureUrl = StringUtils.EMPTY;

    /**
     * 图片路径数组json,这里对应新一期加入的“详情大图”
     */
    @ApiModelProperty(value = "商品图片地址，详情大图")
    private String detailBigPictureUrl = StringUtils.EMPTY;

    @ApiModelProperty(value = "商品最低价")
    private Integer isBestseller;

    @ApiModelProperty(value = "是否参与推荐：0：否，1：是")
    private Integer isNew;

    @ApiModelProperty(value = "是否是招牌：0：否,1:是")
    private Integer isSign;

    @ApiModelProperty(value = "是否推荐：0：否，1：是")
    private Integer isRecommend;

    @ApiModelProperty(value = "可能为空集合，标签集合,举例：[{\"id\":\"isBestseller\",\"name\":\"热销\"},{\"id\":\"isNew\",\"name\":\"新品\"},{\"id\":\"isSign\",\"name\":\"招牌\"}]")
    private List<TagRespDTO> tagList;

    @ApiModelProperty(value = "是否是固定套餐，0：否，1：是", required = true)
    private Integer isFixPkg;

    @ApiModelProperty(value = "规格集合", required = true)
    private List<SkuSynRespDTO> skuList;

    @ApiModelProperty(value = "可能为空集合，一级属性集合")
    private List<AttrGroupSynRespDTO> attrGroupList;

    @ApiModelProperty(value = "可能为空集合，分组集合")
    private List<SubgroupSynRespDTO> subgroupList;

    // 老pad所需字段
    /**
     * 小图
     */
    @ApiModelProperty(value = "商品图片地址，小图")
    private String smallPicture;

    /**
     * 大图
     */
    @ApiModelProperty(value = "商品图片地址，大图")
    private String bigPicture;

    /**
     * 竖图
     */
    @ApiModelProperty(value = "商品图片地址，竖图")
    private String verticalPicture;

    /**
     * 详情大图
     */
    @ApiModelProperty(value = "商品图片地址，详情大图")
    private String detailPicture;

    /**
     * 商品分类列表
     */
    @ApiModelProperty(value = "商品分类列表")
    private List<ItemTypeListDTO> typeList;
}
