package com.holderzone.saas.store.dto.erp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/05/10 上午 10:38
 * @description
 */
public class InOutDocumentFolwDetailSelectDTO {

    @ApiModelProperty("出入库明细唯一标识")
    private String guid;

    @ApiModelProperty("关联单据")
    private String documentGuid;

    @ApiModelProperty("物料Guid")
    private String materialGuid;

    @ApiModelProperty("物料Code")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("单据类型")
    private Integer type;

    @ApiModelProperty("单据出入库类型")
    private Integer inOutType;

    @ApiModelProperty("单据日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="Asia/Shanghai")
    private Date documentDate;

    @ApiModelProperty(hidden = true)
    private BigDecimal count;

    @ApiModelProperty("变化量")
    private String modifiedCount;

    @ApiModelProperty("单位guid")
    private String unitGuid;

    @ApiModelProperty("单位")
    private String unitName;

    public Integer getInOutType() {
        return inOutType;
    }

    public void setInOutType(Integer inOutType) {
        this.inOutType = inOutType;
    }

    public String getModifiedCount() {
        return modifiedCount;
    }

    public void setModifiedCount(String modifiedCount) {
        this.modifiedCount = modifiedCount;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getDocumentGuid() {
        return documentGuid;
    }

    public void setDocumentGuid(String documentGuid) {
        this.documentGuid = documentGuid;
    }

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getUnitGuid() {
        return unitGuid;
    }

    public void setUnitGuid(String unitGuid) {
        this.unitGuid = unitGuid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Date getDocumentDate() {
        return documentDate;
    }

    public void setDocumentDate(Date documentDate) {
        this.documentDate = documentDate;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }
}
