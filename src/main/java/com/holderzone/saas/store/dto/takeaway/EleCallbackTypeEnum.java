package com.holderzone.saas.store.dto.takeaway;

/**
 * https://nest-fe.faas.ele.me/openapi/documents/callback
 */
@SuppressWarnings("NonAsciiCharacters")
public enum EleCallbackTypeEnum {

    订单生效("订单生效", "订单状态扭转", 10, "订单生效，店铺可以看到新订单", "订单消息"),
    商户接单("商户接单", "订单状态扭转", 12, "商户已经接单", "订单状态变更消息"),
    订单被取消("订单被取消", "订单状态扭转", 14, "订单被取消（接单前）", "订单状态变更消息"),
    订单置为无效("订单置为无效", "订单状态扭转", 15, "订单置为无效（接单后）", "订单状态变更消息"),
    订单强制无效("订单强制无效", "订单状态扭转", 17, "订单强制无效（商家主动取消已接受订单、用户1分钟内取消）", "订单状态变更消息"),
    订单完结("订单完结", "订单状态扭转", 18, "订单完结", "订单状态变更消息"),
    用户申请取消单("用户申请取消单", "取消单流程", 20, "下单用户申请取消", "取消单消息"),
    用户取消取消单申请("用户取消取消单申请", "取消单流程", 21, "用户取消取消订单", "取消单消息"),
    商户拒绝取消单("商户拒绝取消单", "取消单流程", 22, "商户拒绝取消订单", "取消单消息"),
    商户同意取消单("商户同意取消单", "取消单流程", 23, "商户同意取消订单", "取消单消息"),
    用户申请仲裁取消单("用户申请仲裁取消单", "取消单流程", 24, "用户申请仲裁", "取消单消息"),
    客服仲裁取消单申请有效("客服仲裁取消单申请有效", "取消单流程", 25, "客服仲裁取消单申请有效", "取消单消息"),
    客服仲裁取消单申请无效("客服仲裁取消单申请无效", "取消单流程", 26, "客服仲裁取消单申请无效", "取消单消息"),
    用户申请退单("用户申请退单", "退单流程", 30, "用户申请退单", "退单消息"),
    用户取消退单("用户取消退单", "退单流程", 31, "用户取消退单", "退单消息"),
    商户拒绝退单("商户拒绝退单", "退单流程", 32, "商户拒绝退单", "退单消息"),
    商户同意退单("商户同意退单", "退单流程", 33, "商户同意退单", "退单消息"),
    用户申请仲裁("用户申请仲裁", "退单流程", 34, "用户申请仲裁", "退单消息"),
    客服仲裁退单有效("客服仲裁退单有效", "退单流程", 35, "客服仲裁退单有效", "退单消息"),
    客服仲裁退单无效("客服仲裁退单无效", "退单流程", 36, "客服仲裁退单无效", "退单消息"),
    用户催单("用户催单", "催单流程", 45, "用户催单", "催单消息"),
    订单待分配配送商("订单待分配配送商", "运单追踪", 51, "待分配配送商", "运单状态变更消息"),
    订单待分配配送员("订单待分配配送员", "运单追踪", 52, "待分配配送员", "运单状态变更消息"),
    配送员取餐中("配送员取餐中", "运单追踪", 53, "已分配给配送员，配送员取餐中", "运单状态变更消息"),
    配送员已到店("配送员已到店", "运单追踪", 54, "配送员已经到店", "运单状态变更消息"),
    配送员配送中("配送员配送中", "运单追踪", 55, "配送员已取餐，配送中", "运单状态变更消息"),
    配送成功("配送成功", "运单追踪", 56, "配送成功", "运单状态变更消息"),
    配送取消_商户取消("配送取消，商户取消", "运单追踪", 57, "配送取消，商户取消", "运单状态变更消息"),
    配送取消_用户取消("配送取消，用户取消", "运单追踪", 58, "", "运单状态变更消息"),
    配送取消_物流系统取消("配送取消，物流系统取消", "运单追踪", 59, "配送取消", "运单状态变更消息"),
    配送失败_呼叫配送晚("配送失败，呼叫配送晚", "运单追踪", 60, "配送失败", "运单状态变更消息"),
    配送失败_餐厅出餐问题("配送失败，餐厅出餐问题", "运单追踪", 61, "配送失败", "运单状态变更消息"),
    配送失败_商户中断配送("配送失败，商户中断配送", "运单追踪", 62, "配送失败", "运单状态变更消息"),
    配送失败_用户不接电话("配送失败，用户不接电话", "运单追踪", 63, "配送失败", "运单状态变更消息"),
    配送失败_用户退单("配送失败，用户退单", "运单追踪", 64, "配送失败", "运单状态变更消息"),
    配送失败_用户地址错误("配送失败，用户地址错误", "运单追踪", 65, "配送失败", "运单状态变更消息"),
    配送失败_超出服务范围("配送失败，超出服务范围", "运单追踪", 66, "配送失败", "运单状态变更消息"),
    配送失败_骑手标记异常("配送失败，骑手标记异常", "运单追踪", 67, "配送失败", "运单状态变更消息"),
    配送失败_系统自动标记异常("配送失败，系统自动标记异常", "运单追踪", 68, "配送失败", "运单状态变更消息"),
    配送失败_其他异常("配送失败，其他异常", "运单追踪", 69, "配送失败", "运单状态变更消息"),
    配送失败_超时标记异常("配送失败，超时标记异常", "运单追踪", 70, "配送失败", "运单状态变更消息"),
    自行配送("自行配送", "运单追踪", 71, "自行配送", "运单状态变更消息"),
    不再配送("不再配送", "运单追踪", 72, "不再配送", "运单状态变更消息"),
    物流拒单_仅支持在线支付("物流拒单，仅支持在线支付", "运单追踪", 73, "仅支持在线支付", "运单状态变更消息"),
    物流拒单_超出服务范围("物流拒单，超出服务范围", "运单追踪", 74, "超出服务范围", "运单状态变更消息"),
    物流拒单_请求配送晚("物流拒单，请求配送晚  ", "运单追踪", 75, "请求配送过晚", "运单状态变更消息"),
    物流拒单_系统异常("物流拒单，系统异常", "运单追踪", 76, "系统异常", "运单状态变更消息"),
    店铺营业状态通知("店铺营业状态通知", "店铺管理", 91, "店铺营业状态变化", "店铺状态变更消息"),
    店铺信息修改通知("店铺信息修改通知", "店铺管理", 93, "店铺基本信息变更消息", "店铺信息变更消息"),
    应用授权解除通知("应用授权解除通知", "授权管理", 100, "授权状态变更消息", "授权状态变更消息"),
    服务市场订单订购通知("服务市场订单订购通知", "服务订单管理", 105, "服务市场订单状态扭转消息", "服务市场订购消息"),
    NULL("null", "null", -1, "null", "null");

    /**
     * 名称
     */
    private String name;

    /**
     * 分类
     */
    private String category;

    /**
     * Type值
     */
    private int type;

    /**
     * 说明
     */
    private String description;

    /**
     * 消息结构体
     */
    private String structure;

    EleCallbackTypeEnum(String name, String category, int type, String description, String structure) {
        this.name = name;
        this.category = category;
        this.type = type;
        this.description = description;
        this.structure = structure;
    }

    public String getName() {
        return name;
    }

    public String getCategory() {
        return category;
    }

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public String getStructure() {
        return structure;
    }

    public static EleCallbackTypeEnum ofType(int type) {
        EleCallbackTypeEnum[] values = EleCallbackTypeEnum.values();
        for (EleCallbackTypeEnum value : values) {
            if (type == value.type) {
                return value;
            }
        }
        return EleCallbackTypeEnum.NULL;
    }
}
