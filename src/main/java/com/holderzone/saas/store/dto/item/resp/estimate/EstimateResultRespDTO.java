package com.holderzone.saas.store.dto.item.resp.estimate;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 校验估清商品返回结果
 */
@Data
@ApiModel(value = "校验估清商品返回结果DTO")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EstimateResultRespDTO implements Serializable {

    private static final long serialVersionUID = -7204649193528405112L;

    /**
     * 是否校验通过
     */
    private Boolean success;

    /**
     * 检验失败商品，以逗号分割拼接字符串返回
     */
    private String errorMsg;

    /**
     * 校验不通过的skuGuids
     */
    private List<String> skuGuids;

}
