package com.holderzone.saas.store.dto.order.response.dinein;

import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.enums.print.RefundPrintTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 退款单打印传输对象
 *
 * <AUTHOR>
 * @date 2025/7/3 16:34
 */
@Data
public class RefundOrderPrintDTO {

    /**
     * 退款时间
     */
    private Long refundTime;

    /**
     * 退款时间
     */
    private BigDecimal refundAmount;

    /**
     * 退款支付方式
     */
    private String paymentTypeName;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 退款方式类型
     */
    private String refundType;

    /**
     * 操作时间
     */
    private Long operationTime;

    /**
     * 调用方法类型，0-退款，1-反结账
     */
    private RefundPrintTypeEnum typeEnum;

    /**
     * 附加费
     */
    private List<AdditionalCharge> additionalChargeList;
}
