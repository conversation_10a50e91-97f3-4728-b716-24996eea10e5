package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EleBindingUrlDTO
 * @date 2018/09/20 17:53
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EleBindingUrlDTO implements Serializable {

    private static final long serialVersionUID = -7820935209083353029L;

    @ApiModelProperty(value = "门店id")
    @NotNull(message = "门店id不能为空")
    private String storeId;

    @ApiModelProperty(value = "外卖类型")
    @NotNull(message = "外卖类型不能为空")
    private Integer takeawayType;

    @ApiModelProperty(value = "授权url")
    private String bindingUrl;
}
