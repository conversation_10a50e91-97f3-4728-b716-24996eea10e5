package com.holderzone.saas.store.dto.weixin.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @description 
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreDineinOrderDetailsRespDTO
 * @date 2019/3/19
 */
@Data
@ApiModel("微信门店：我的订单")
public class WxStoreDineinOrderDetailsRespDTO {

	@ApiModelProperty(value = "订单的guid")
	private String guid;

	@ApiModelProperty(value = "品牌图片")
	private String logUrl;

	@ApiModelProperty(value = "门店名")
	private String storeName;

	@ApiModelProperty(value = "交易模式：交易模式(0：正餐，1：快餐)", required = true)
	private Integer tradeMode;

	@ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
	private BigDecimal actuallyPayFee;

	@ApiModelProperty(value = "订单号")
	private String orderNo;

	@ApiModelProperty(value = "状态(0:待确认，1：未结账， 2：已结账， 3：已退款，4：已作废)")
	private Integer state;

	@ApiModelProperty(value = "结算时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime checkoutTime;

	@ApiModelProperty(value = "结算时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime gmtCreate;

	@ApiModelProperty(value="商品名称")
	private List<String> itemNames;

//	@ApiModelProperty(value = "商品")
//	private List<DineInItemDTO> dineInItemDTOS;
}
