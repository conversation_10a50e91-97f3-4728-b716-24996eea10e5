package com.holderzone.saas.store.dto.pay;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggRefundDetailRespDTO
 * @date 2019/03/15 15:04
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class AggRefundDetailRespDTO {

    /**
     * 单笔退款号（是）
     * 富民银行生成的退款订单号
     */
    private String refOrderno;
    /**
     * 原订单号（是）
     */
    private String orderNo;
    /**
     * 退款金额，单位分（是）
     */
    private Integer refundFee;
    /**
     * 退款类型 0：全款，1：部分
     */
    private Integer refundType;
    /**
     * 退款订单生成时间（是）
     * yyyyMMddHHmmss
     */
    private String refCreateDate;
    /**
     * 退款成功时间（否）
     * yyyyMMdd HHmmss（注意空格）
     */
    private String refDate;
    /**
     * 退款状态（是）
     * 0 待退款， 1 成功， 2 失败， 3 退款中
     */
    private String status;
    /**
     * 退款原因（否）
     * 当初输入的原因
     */
    private String reason;
    /**
     * 商户退款订单号（否）
     * 自定义的等于refundNo
     */
    private String refMchntOrderNo;
    /**
     * 单次退款对应的签名（是）
     */
    private String signature;

    /**
     * 银行分配的商户编号
     */
    private String mchntId;

}
