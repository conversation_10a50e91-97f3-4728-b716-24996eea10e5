package com.holderzone.saas.store.dto.takeaway;

public enum DeliveryChangeEnum {

    已分配骑手(0, "已分配骑手"),

    已处理(1, "已处理"),

    商家已配餐(10, "商家已配餐"),

    骑手取餐(22, "已分配骑手取餐"),

    已取餐(23, "已取餐-配送途中"),

    同意退款(100, "同意退款"),

    拒绝退款(101, "拒绝退款"),

    已完成(254, "已完成"),

    已关闭(255, "已关闭");

    private int code;

    private String desc;

    DeliveryChangeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
