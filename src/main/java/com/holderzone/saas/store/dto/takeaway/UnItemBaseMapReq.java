package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@NoArgsConstructor
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY,
        getterVisibility = JsonAutoDetect.Visibility.NONE)
public class UnItemBaseMapReq implements Serializable {

    private static final long serialVersionUID = -5711573937851844989L;

    @NotBlank(message = "平台方商品ID不得为空")
    @ApiModelProperty(value = "平台方商品ID", required = true)
    private String unItemId;

    @NotBlank(message = "平台方商品规格ID不得为空")
    @ApiModelProperty(value = "平台方商品规格ID", required = true)
    private String unItemSkuId;

    @NotBlank(message = "平台方商品分类ID不得为空")
    @ApiModelProperty(value = "平台方商品分类ID", required = true)
    private String unItemTypeId;

    @ApiModelProperty(value = "扩展字段，透传", required = true)
    private String extendValue;

    @NotBlank(message = "ERP方商品GUID不得为空")
    @ApiModelProperty(value = "ERP方商品GUID", required = true)
    private String erpItemGuid;

    @NotBlank(message = "ERP方商品规格GUID不得为空")
    @ApiModelProperty(value = "ERP方商品规格GUID", required = true)
    private String erpItemSkuId;

    @ApiModelProperty(value = "商品实际规格GUID")
    private String actualErpItemSkuId;

    @ApiModelProperty(value = "映射数量")
    private Integer unItemCountMapper;

    /**
     * 业务标识，1-拼好饭
     */
    private int businessIdentify;
}
