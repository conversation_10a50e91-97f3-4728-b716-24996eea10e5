package com.holderzone.saas.store.dto.queue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreQueueDTO
 * @date 2019/05/09 16:59
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class StoreQueueDTO {
    @ApiModelProperty("队列信息")
    private List<HolderQueueDetailDTO> queues;
    @ApiModelProperty("是否开启手动重置队列")
    private Boolean isEnableManualReset;
    @ApiModelProperty("是否允许直接就餐")
    private Boolean isEnableEat;
    @ApiModelProperty("是否允许恢复至队列")
    private Boolean isEnableRecovery;
}