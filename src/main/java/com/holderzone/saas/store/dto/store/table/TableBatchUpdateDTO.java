package com.holderzone.saas.store.dto.store.table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableDO
 * @date 2018/07/24 上午9:55
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class TableBatchUpdateDTO implements Serializable {

    private static final long serialVersionUID = 815314635970498322L;

    /**
     * 桌台guid
     */
    @NotEmpty(message = "桌台guid列表不得为空，且桌台guid不得超过45个字符")
    @ApiModelProperty(value = "桌台guid列表", required = true)
    private List<String> tableGuidList;

    /**
     * 区域guid
     */
    @ApiModelProperty(value = "区域guid")
    private String areaGuid;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaName;

    /**
     * 桌台座位数
     */
    @ApiModelProperty(value = "桌台座位数")
    private Integer seats;

    /**
     * 是否已启用
     * 0=未启用
     * 1=已启用
     * 默认1
     */
    @ApiModelProperty(value = "是否已启用。0=未启用，1=已启用。默认1。")
    private Integer enable;
}
