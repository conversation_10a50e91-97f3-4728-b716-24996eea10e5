package com.holderzone.saas.store.dto.store.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreCreateDTO
 * @date 2018/07/23 下午4:27
 * @description 组织启用/禁用DTO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class OrganizeEnableDTO implements Serializable {

    private static final long serialVersionUID = 6849600812309037212L;

    /**
     * 组织guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "组织guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "组织guid", required = true)
    private String organizationGuid;

    /**
     * 组织类型。1=品牌，2=区域，3=门店。
     */
    @NotNull
    @Min(value = 1, message = "是否启用不得为空，且范围是[1-3]，1=品牌，2=区域，3=门店")
    @Max(value = 3, message = "是否启用不得为空，且范围是[1-3]，1=品牌，2=区域，3=门店")
    @ApiModelProperty(value = "组织类型。1=品牌，2=区域，3=门店。", required = true)
    private Integer organizationType;

    /**
     * 是否已启用
     * 0=未启用
     * 1=已启用
     */
    @NotNull
    @Min(value = 0, message = "是否启用不得为空，且范围是[0-1]，0=未启用，1=已启用")
    @Max(value = 1, message = "是否启用不得为空，且范围是[0-1]，0=未启用，1=已启用")
    @ApiModelProperty(value = "是否已启用。0=未启用，1=已启用。", required = true)
    private Integer enable;
}
