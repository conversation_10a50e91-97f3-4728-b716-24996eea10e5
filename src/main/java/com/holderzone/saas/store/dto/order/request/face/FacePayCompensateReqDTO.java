package com.holderzone.saas.store.dto.order.request.face;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FacePayCompensateReqDTO
 * @date 2019/04/08 13:56
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class FacePayCompensateReqDTO extends BaseDTO {

    @ApiModelProperty(value = "退款记录的guid")
    @NotNull
    private String guid;

    @ApiModelProperty(value = "订单guid")
    @NotNull
    private String orderGuid;

    @ApiModelProperty(value = "是否成功")
    @NotNull
    private boolean success;


}
