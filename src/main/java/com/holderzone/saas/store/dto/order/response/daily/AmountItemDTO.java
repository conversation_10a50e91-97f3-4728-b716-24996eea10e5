package com.holderzone.saas.store.dto.order.response.daily;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AmountItemDTO
 * @date 2019/02/15 14:31
 * @description 日报统计金额项
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@Accessors(chain = true)
public class AmountItemDTO {
    @ApiModelProperty(value = "编码")
    private Integer code;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;
    @ApiModelProperty(value = "数量")
    private Integer count;
    @ApiModelProperty(value = "超出金额，第三方活动使用")
    private BigDecimal excessAmount;
    @ApiModelProperty(value = "顺序")
    private Integer sort;
    @ApiModelProperty(value = "使用明细")
    private List<InnerDetails> innerDetails;
    @ApiModelProperty(value = "商家预计应得金额")
    private BigDecimal estimatedAmount;
    @ApiModelProperty(value = "订单笔数")
    private Integer orderCount;
    @ApiModelProperty(value = "优惠订单笔数")
    private Integer discountOrderCount;
    @ApiModelProperty(value = "团购券张数")
    private Integer grouponCount;
    @ApiModelProperty(value = "是否团购")
    private Boolean isGroupon;
    @ApiModelProperty(value = "聚合支付对应的支付方式")
    private Integer payPowerId;
    @Data
    public static class InnerDetails {
        @ApiModelProperty("明细")
        private String name;
        @ApiModelProperty("统计金额")
        private BigDecimal amount;
        @ApiModelProperty(value = "优惠金额")
        private BigDecimal discountAmount;
        @ApiModelProperty("使用数量")
        private Integer count;
        @ApiModelProperty(value = "订单笔数")
        private Integer orderCount;
        @ApiModelProperty(value = "优惠订单笔数")
        private Integer discountOrderCount;
        @ApiModelProperty(value = "团购券张数")
        private Integer grouponCount;
        @ApiModelProperty(value = "是否团购")
        private Boolean isGroupon;
        @ApiModelProperty(value = "使用明细")
        private List<InnerDetails> innerDetails;
    }


    public AmountItemDTO() {

    }

    public AmountItemDTO(Integer code, String name, BigDecimal amount, BigDecimal excessAmount) {
        this.code = code;
        this.name = name;
        this.amount = amount;
        this.excessAmount = excessAmount;
    }
}