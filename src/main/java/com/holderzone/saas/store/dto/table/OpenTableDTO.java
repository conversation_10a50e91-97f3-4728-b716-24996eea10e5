package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OpenTableDTO
 * @date 2019/01/04 16:11
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class OpenTableDTO extends BaseDTO {

    @ApiModelProperty("桌台guid")
    @NotBlank
    private String tableGuid;

    @ApiModelProperty("桌台号")
    @NotBlank
    private String tableCode;

    @ApiModelProperty("区域名")
    private String areaName;

    @ApiModelProperty(value = "订单号", hidden = true)
    private String orderGuid;

    @NotNull
    @ApiModelProperty(value = "实际就餐人数")
    private Integer actualGuestsNo;

    @ApiModelProperty("区域guid")
    private String areaGuid;

    @ApiModelProperty("门店编号")
    private String storeNo;

    /**
     * 设备号
     */
    @ApiModelProperty("设备号")
    private String deviceNo;
}
