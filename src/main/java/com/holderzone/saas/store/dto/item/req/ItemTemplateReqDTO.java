package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateReqDTO
 * @date 2019/05/23 10:28
 * @description //TODO 商品模板请求DTO
 * @program holder-saas-aggregation-app
 */
@Data
@ApiModel(value = "商品模板")
public class ItemTemplateReqDTO {

    /**
     * 业务主键
     */
    @ApiModelProperty(value = "商品模板guid")
    private String guid;

    /**
     * 外键 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String templateName;

    /**
     * 模板描述
     */
    @ApiModelProperty(value = "模板详细描述")
    private String description;

    /**
     * 开始有效期
     */
    @ApiModelProperty(value = "开始有效期  24小时制：yyyy-mm-dd HH:mm:ss")
    private String effectiveStartTime;

    /**
     * 结束有效期
     */
    @ApiModelProperty(value = "结束有效期 24小时制：yyyy-mm-dd HH:mm:ss")
    private String effectiveEndTime;

    /**
     * 是否激活 1：激活 2：冻结
     */
    @ApiModelProperty(value = "是否激活 1：激活 2：冻结")
    private Integer isItActivated = 1;

    /**
     * 周期模式  1：按时段  2：按星期
     */
    @ApiModelProperty(value = "周期模式  1：按时段  2：按星期")
    private Integer periodicMode;

    /**
     * 是否逻辑删除 0：否  1：是
     */
    @ApiModelProperty(value = "是否逻辑删除 0：否  1：是    删除时传入1")
    private Integer isDelete = 0;

}
