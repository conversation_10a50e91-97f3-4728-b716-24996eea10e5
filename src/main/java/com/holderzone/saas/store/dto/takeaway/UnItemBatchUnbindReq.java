package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY,
        getterVisibility = JsonAutoDetect.Visibility.NONE)
public class UnItemBatchUnbindReq implements Serializable {

    private static final long serialVersionUID = -5711573937851844989L;

    @NotBlank(message = "ERP门店id不得为空")
    @ApiModelProperty(value = "ERP门店id", required = true)
    private String storeGuid;

    //@Min(value = 0, message = "外卖类型(0：美团，1：饿了么,2:掌控者)")
    //@Max(value = 3, message = "外卖类型(0：美团，1：饿了么,2:掌控者)")
    @NotNull(message = "商品映射类型不得为空")
    @ApiModelProperty(value = "商品映射类型：0=美团，1=饿了么", required = true)
    private Integer takeoutType;

    @Valid
    @NotEmpty(message = "解绑商品列表不得为空")
    @ApiModelProperty(value = "解绑商品列表", required = true)
    List<UnItemBaseMapReq> unItemUnbindList;

    @ApiModelProperty(value = "绑定：true 解绑: false")
    private Boolean bindFlag;
}
