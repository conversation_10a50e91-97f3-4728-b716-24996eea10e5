package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class UnMappedType implements Serializable {

    @ApiModelProperty("平台方商品分类ID")
    private String unItemTypeId;

    @ApiModelProperty("平台方商品分类名称")
    private String unItemTypeName;

    @ApiModelProperty("平台方该分类下的商品列表")
    private List<UnMappedItem> unItemList;

    /**
     * 业务标识1-拼好饭
     */
    private int businessIdentify;
}
