package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillDishSkuPropertyDTO
 * @date 2018/10/08 20:56
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class BillDishSkuPropertyDTO implements Serializable {

    /**
     * 业务主键
     */
    @ApiModelProperty(value = "业务主键")
    private String skuPropertyGuid;

    /**
     * 关联账单菜品外键
     */
    @ApiModelProperty(value = "关联账单菜品外键")
    private String billDishGuid;

    /**
     * 属性guid
     */
    @ApiModelProperty(value = "属性guid")
    private String propertyGuid;

    /**
     * 属性名称
     */
    @ApiModelProperty(value = "属性名称")
    private String propertyName;

    /**
     * 属性数量
     */
    @ApiModelProperty(value = "属性数量")
    private Integer num;

    /**
     * 属性价格
     */
    @ApiModelProperty(value = "属性价格")
    private BigDecimal propertyPrice;




}
