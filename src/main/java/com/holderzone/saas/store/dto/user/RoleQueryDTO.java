package com.holderzone.saas.store.dto.user;

import com.holderzone.framework.util.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RoleQueryDTO
 * @date 19-1-15 下午2:58
 * @description 查询角色列表DTO
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@ApiModel("角色列表查询DTO")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class RoleQueryDTO extends Page {
    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("分页偏移量")
    private long offsetIndex;

    /**
     * mybatis拼接limit参数需要
     *
     * @return 分页偏移量
     */
    public long getOffsetIndex() {
        return (this.getCurrentPage() -1) * this.getPageSize();
    }
}
