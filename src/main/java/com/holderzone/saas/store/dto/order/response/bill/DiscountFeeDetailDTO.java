package com.holderzone.saas.store.dto.order.response.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DiscountFeeDetailDTO
 * @date 2019/01/28 11:17
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class DiscountFeeDetailDTO implements Serializable {

    private static final long serialVersionUID = -1304266424392351000L;

    @ApiModelProperty(value = "折扣guid")
    private String guid;

    /**
     * 订单guid
     */
    private String orderGuid;

    @ApiModelProperty(value = "折扣方式名字")
    private String discountName;

    @ApiModelProperty(value = "活动guid")
    private String activityGuid;

    /**
     * {@link com.holderzone.saas.store.enums.order.DiscountTypeEnum}
     */
    @ApiModelProperty(value = "1-会员折扣，2-整单折扣，3-整单让价,4-系统省零，5-赠送优惠，6-团购验券，7-会员优惠券,8-积分抵扣")
    private Integer discountType;

    @ApiModelProperty(value = "折扣总额")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "打几折（整单折扣和会员折扣）")
    private BigDecimal discount;

    /**
     * @see DiscountRuleDTO
     */
    @ApiModelProperty(value = "折扣规则：是否计算积分1：计算，2：不计算")
    private String rule;

    @ApiModelProperty(value = "折扣内容")
    private String content;

    @ApiModelProperty(value = "抵扣积分数量")
    private Integer integral;


}
