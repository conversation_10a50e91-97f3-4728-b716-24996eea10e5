package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.item.common.ItemLogDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeReqDTO
 * @date 2019/01/03 下午4:55
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class TypeReqDTO extends ItemLogDTO {

    @ApiModelProperty("typeGuid")
    private String typeGuid;

    @ApiModelProperty("商品分类名称")
    @NotBlank(
            message = "商品分类名称不能为空"
    )
    @Size(min = 1, max = 40)
    private String name;

    @ApiModelProperty("排序，新增单个分类时非必填，但拖动排序时必填")
    @Min(value = 1)
    private Integer sort;

    @ApiModelProperty("删除状态")
    private Integer isDelete;

    @ApiModelProperty("品牌GUID")
    private String brandGuid;

    @ApiModelProperty("门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "描述")
    @Size(max = 50)
    private String description;

    @ApiModelProperty(value = "是否启用（0：否，1：是）")
    @NotNull
    @Min(0)
    @Max(1)
    private Integer isEnable;

    @ApiModelProperty(value = "图标地址")
    private String iconUrl;

    @ApiModelProperty(value = "菜谱方案Guid")
    private String pricePlanGuid;

    @ApiModelProperty(value = "父分类Guid")
    private String parentGuid;

    /**
     * 菜谱分类图片类型
     */
    @ApiModelProperty(value = "菜谱分类图片类型(0小图,1竖图,2整屏)")
    private Integer menuClassifyPictureType;

    @ApiModelProperty(value = "是否必点")
    private Integer isMustPoint;
}
