package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
public class MtCbOrderCancelDetail implements Serializable {

    private static final long serialVersionUID = -2286232887801650510L;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 取消原因类型
     */
    private String reasonCode;

    /**
     * 取消原因描述
     *
     * @see MtOcReasonCodeEnum
     */
    private String reason;
}
