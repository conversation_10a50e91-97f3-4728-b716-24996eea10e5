package com.holderzone.saas.store.dto.trade;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/28
 * @since 1.8
 */
@Data
public class HandoverOrderOptimizationDTO {

    /**
     * 订单guid
     */
    private String guid;

    /**
     * 主订单guid
     */
    private String mainOrderGuid;

    /**
     * 订单金额
     */
    private BigDecimal orderFee;

    /**
     * 订单upper state
     */
    private Integer UpperState;

    /**
     * 交易模式(0：正餐，1：快餐)
     */
    private Integer tradeMode;

    /**
     * 客人数
     */
    private Integer guestCount;

    /**
     * 入座时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 离座时间
     */
    private LocalDateTime checkoutTime;

    /**
     * 是否联台单
     */
    private String associatedFlag;

    /**
     * 联台单桌台列表
     */
    private String associatedTableGuids;
}
