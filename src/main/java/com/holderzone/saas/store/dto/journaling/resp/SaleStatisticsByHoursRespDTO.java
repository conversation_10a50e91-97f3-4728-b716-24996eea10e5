package com.holderzone.saas.store.dto.journaling.resp;


import cn.hutool.core.date.DateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.map.LinkedMap;

import javax.swing.text.DateFormatter;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019/8/28 16:11
 */
@ApiModel(value = "时间序列查询返回值")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SaleStatisticsByHoursRespDTO {

    @ApiModelProperty(value = "今天企业销售按小时分组数据列表")
    private List<StoreSaleItemDTO> todaySaleStatisticsData;

    @ApiModelProperty(value = "昨天企业销售按小时分组数据列表")
    private List<StoreSaleItemDTO> yesterdaySaleStatisticsData;

    @ApiModelProperty(value = "每天企业销售按小时分组数据列表")
    private Map<String, List<StoreSaleItemDTO>> saleStatisticsData;

    public SaleStatisticsByHoursRespDTO(List<StoreSaleItemDTO> todaySaleStatisticsData, List<StoreSaleItemDTO> yesterdaySaleStatisticsData) {
        this.todaySaleStatisticsData = todaySaleStatisticsData;
        this.yesterdaySaleStatisticsData = yesterdaySaleStatisticsData;
    }
}
