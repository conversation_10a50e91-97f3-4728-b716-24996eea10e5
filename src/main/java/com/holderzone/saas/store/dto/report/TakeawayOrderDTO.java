package com.holderzone.saas.store.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2018/12/05 上午 10:45
 * @description
 */
@ApiModel
public class TakeawayOrderDTO {

    @ApiModelProperty("订单总额")
    private TakeawayPlatformIndicator orderAmountTotal;
    @ApiModelProperty("订单数量")
    private TakeawayPlatformIndicator orderCount;
    @ApiModelProperty("均单收入")
    private TakeawayPlatformIndicator orderAvgAmount;
    @ApiModelProperty("退款总额")
    private TakeawayPlatformIndicator refundAmountTotal;
    @ApiModelProperty("退款订单数")
    private TakeawayPlatformIndicator refundCount;

    public TakeawayPlatformIndicator getOrderAmountTotal() {
        return orderAmountTotal;
    }

    public void setOrderAmountTotal(TakeawayPlatformIndicator orderAmountTotal) {
        this.orderAmountTotal = orderAmountTotal;
    }

    public TakeawayPlatformIndicator getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(TakeawayPlatformIndicator orderCount) {
        this.orderCount = orderCount;
    }

    public TakeawayPlatformIndicator getOrderAvgAmount() {
        return orderAvgAmount;
    }

    public void setOrderAvgAmount(TakeawayPlatformIndicator orderAvgAmount) {
        this.orderAvgAmount = orderAvgAmount;
    }

    public TakeawayPlatformIndicator getRefundAmountTotal() {
        return refundAmountTotal;
    }

    public void setRefundAmountTotal(TakeawayPlatformIndicator refundAmountTotal) {
        this.refundAmountTotal = refundAmountTotal;
    }

    public TakeawayPlatformIndicator getRefundCount() {
        return refundCount;
    }

    public void setRefundCount(TakeawayPlatformIndicator refundCount) {
        this.refundCount = refundCount;
    }
}
