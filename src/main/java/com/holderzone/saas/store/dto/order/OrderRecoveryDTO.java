package com.holderzone.saas.store.dto.order;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.response.OrderCreatRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRecoveryDTO
 * @date 2018/09/05 14:33
 * @description
 * @program holder-saas-store-order
 */
@Data
public class OrderRecoveryDTO extends BaseDTO {

    @ApiModelProperty(value = "新订单对象")
    private OrderCreatRespDTO orderCreatRespDTO;

    @ApiModelProperty(value = "反结账员工guid")
    private String recoveryStaffGuid;

    @ApiModelProperty(value = "反结账员工name")
    private String recoveryStaffName;

}
