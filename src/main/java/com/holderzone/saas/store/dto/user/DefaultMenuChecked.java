package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className DefaultMenuChecked
 * @date 19-2-21 下午3:20
 * @description 角色授权部分，根据前端要求构造的实体
 * @program holder-saas-store-staff
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class DefaultMenuChecked {

    @ApiModelProperty("菜单下存在已选中的资源，提取该菜单的guid组成的集合（前端需要）")
    private List<String> defaultCheckedMenuList;

    @ApiModelProperty("菜单树，包括最底层菜单的资源信息")
    private List<MenuDTO> menuDTOList;
}
