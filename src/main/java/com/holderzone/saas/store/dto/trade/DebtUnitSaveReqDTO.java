package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 挂账单位保存入参
 *
 * <AUTHOR>
 * @since 2020-12-16
 */
@ApiModel(value = "挂账单位保存DTO")
public class DebtUnitSaveReqDTO {

    /**
     * 单位guid
     */
    @ApiModelProperty(value = "单位guid，编辑时传")
    private String unitGuid;

    /**
     * 单位代码
     */
    @ApiModelProperty(value = "单位代码")
    @NotBlank(message = "单位代码不能为空")
    private String code;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    @NotBlank(message = "单位名称不能为空")
    private String name;

    /**
     * 查询密码
     */
    @ApiModelProperty(value = "查询密码")
    @NotBlank(message = "查询密码不能为空")
    private String password;

    /**
     * 信用额度
     */
    @ApiModelProperty(value = "信用额度")
    @NotNull(message = "信用额度不能为空")
    private BigDecimal creditLimit;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    @NotBlank(message = "联系人不能为空")
    private String contactName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @NotBlank(message = "联系电话不能为空")
    private String contactTel;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public BigDecimal getCreditLimit() {
        return creditLimit;
    }

    public void setCreditLimit(BigDecimal creditLimit) {
        this.creditLimit = creditLimit;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactTel() {
        return contactTel;
    }

    public void setContactTel(String contactTel) {
        this.contactTel = contactTel;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUnitGuid() {
        return unitGuid;
    }

    public void setUnitGuid(String unitGuid) {
        this.unitGuid = unitGuid;
    }

}
