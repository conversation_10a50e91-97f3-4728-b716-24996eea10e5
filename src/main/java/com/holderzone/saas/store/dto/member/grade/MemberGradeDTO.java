package com.holderzone.saas.store.dto.member.grade;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberGradeDTO
 * @date 2018/08/29 下午4:16
 * @description //会员等级
 * @program holder-saas-config-center
 */
@Data
public class MemberGradeDTO extends BaseDTO {
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;
    /**
     * 业务主键
     */
    @ApiModelProperty(value = "业务主键")
    private String memberGradeGuid;

    /**
     * 等级名称
     */
    @ApiModelProperty(value = "等级名称,必传",required = true)
    private String name;

    /**
     * 成为该等级需要的积分
     */
    @ApiModelProperty(value = "成为该等级需要的积分,必传",required = true)
    @Min(0)
    private Integer needIntegral;

    /**
     * 权益折扣
     */
    @ApiModelProperty(value = "权益折扣,必传",required = true)
    private BigDecimal discount;

    /**
     * 是否默认 0：否，1:是
     */
    @ApiModelProperty(value = "是否默认 0：否，1:是",required = true)
    private Byte isDefault;

    /**
     * 充值规则 0：不可充值，1:定额充值，2：不定额充值
     */
    @ApiModelProperty(value = "充值规则 0：不可充值，1:定额充值，2：不定额充值",required = true)
    private Byte prepaidRule;

    /**
     * 定额充值额度（JsonArray）
     */
    @ApiModelProperty(value = "定额充值额度(JsonArray)，如果是定额充值则必传")
    private List<String> prepaidLimit;

    /**
     * 等级样式 0：纯色，1:图片
     */
    @ApiModelProperty(value = "等级样式 0：纯色，1:图片",required = true)
    private Byte style;

    /**
     * 纯色样式 （MemberGradeColorEnum）
     */
    @ApiModelProperty(value = "纯色样式编码")
    private Integer color;


    @ApiModelProperty(value ="颜色样式RGBA编码,卡片样式纯色时候必传")
    private String colorRGBA;
    /**
     * 图片链接
     */
    @ApiModelProperty(value = "图片链接,卡片样式图案时必传")
    private String picture;

    private List<IntegralRuleDTO> integralRuleDTOS;



    @Data
    public static class IntegralRuleDTO {

        /**
         * 业务主键
         */
        @ApiModelProperty(value = "业务主键")
        private String integralRuleGuid;

        /**
         * 规则类型 0：获取规则，1:消费规则
         */
        @ApiModelProperty(value = "规则类型 0：获取规则，1:消费规则",required = true)
        private Byte type;

        /**
         * 是否开启 0：关闭，1:开启
         */
        @ApiModelProperty(value = "是否开启 0：关闭，1:开启",required = true)
        private Byte isOpen;

        /**
         * 使用余额是否累计积分 0：否，1:是
         */
        @ApiModelProperty(value = "使用余额是否累计积分 0：否，1:是",required = true)
        private Byte allowBalance;

        /**
         * 会员等级的业务主键
         */
        @ApiModelProperty(value = "会员等级的业务主键")
        private String memberGradeGuid;

        /**
         * 获取规则类型（GetIntegralRuleTypeEnum）
         */
        @ApiModelProperty(value = "获取规则类型（1, 充值积分, 2, 消费积分）",required = true)
        private Integer getType;

        @ApiModelProperty(value = "获取规则类型名称")
        private String getTypeName;

        /**
         * 消费规则类型（ConsumeIntegralRuleTypeEnum）
         */
        @ApiModelProperty(value = "消费规则类型（3, 消费抵现）")
        private Integer consumeType;

        @ApiModelProperty(value = "消费规则类型名称")
        private String consumeTypeName;

        /**
         * 获取积分单位
         */
        @ApiModelProperty(value = "获取积分单位",required = true)
        @Min(value = 0)
        private Integer getIntegralUnit;

        /**
         * 获取积分金额单位
         */
        @ApiModelProperty(value = "获取积分金额单位",required = true)
        @Min(value = 0)
        private BigDecimal getFeeUnit;

        /**
         * 消费积分单位
         */
        @ApiModelProperty(value = "消费积分单位",required = true)
        @Min(value = 0)
        private Integer consumeIntegralUnit;

        /**
         * 单笔订单最大消费积分
         */
        @ApiModelProperty(value = "单笔订单最大消费积分",required = true)
        @Min(value = 0)
        private Integer consumeIntegralMax;

        /**
         * 消费积分金额单位
         */
        @ApiModelProperty(value = "消费积分金额单位",required = true)
        @Min(value = 0)
        private BigDecimal consumeFeeUnit;

        /**
         * 消费积分的最小金额
         */
        @ApiModelProperty(value = "消费积分的最小金额",required = true)
        @Min(value = 0)
        private BigDecimal consumeFeeMin;
    }
}
