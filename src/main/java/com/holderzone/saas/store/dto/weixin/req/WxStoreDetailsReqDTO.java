package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description 
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreDetailsReqDTO
 * @date 2019/5/27
 */
@Data
@ApiModel(value = "订单详情与买单页面共用")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxStoreDetailsReqDTO {
	
	@ApiModelProperty(value = "订单详情或买单页面Guid")
	private String orderGuid;

	@ApiModelProperty(value = "企业id",required = true)
	private String enterpriseGuid;
	
	@ApiModelProperty(value = "0:表示订单详情,1:表示买单",required = true)
	private Integer pageNum;

	@ApiModelProperty(value = "用户id,用来解锁",required = true)
	private String openId;

	@ApiModelProperty(value = "0:正餐，1：快餐",required = true)
	private Integer tradeMode;
	
}
