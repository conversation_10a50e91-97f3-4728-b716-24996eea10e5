package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "掌控者智慧门店管理系统对接-菜品")
@Data
public class TcdDish {

    @ApiModelProperty(value = "菜品Id")
    private Long id;

    @ApiModelProperty(value = "菜品名称")
    private String name;

    @ApiModelProperty(value = "掌控者菜品Id")
    private String holderDishId;

    @ApiModelProperty(value = "菜品规格列表")
    private List<TcdDishSku> skus;

}
