package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemBatchRefundReqDTO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    @Valid
    @NotEmpty(message = "待退的订单商品列表不得为空")
    @ApiModelProperty("待退的订单商品列表")
    private List<ItemRefundReqDTO> itemRefundList;
}
