package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeItemListDTO
 * @date 2019/03/01 下午5:22
 * @description // 获取打印列表同步数据实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "分类和商品的列表实体")
public class TypeItemListDTO implements Serializable {
    @ApiModelProperty(value = "GUID")
    private String guid;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "是否被使用")
    private Boolean used = Boolean.FALSE;

    @ApiModelProperty(value = "商品集合")
    private List<TypeItemListDTO> itemList;
}
