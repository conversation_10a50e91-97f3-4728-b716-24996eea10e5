package com.holderzone.saas.store.dto.order.inside;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderTableInfoDTO
 * @date 2019/01/16 15:14
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class OrderGuidsDTO {

    public OrderGuidsDTO() {

    }

    public OrderGuidsDTO(List<String> orderGuids) {
        this.orderGuids = orderGuids;
    }

    private List<String> orderGuids;

    private String orderGuid;

    private String enterpriseGuid;

}
