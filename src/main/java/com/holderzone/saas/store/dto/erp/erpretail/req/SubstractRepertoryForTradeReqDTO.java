package com.holderzone.saas.store.dto.erp.erpretail.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("销售出库请求实体")
@Data
public class SubstractRepertoryForTradeReqDTO {

    @ApiModelProperty("单据类型(1:采购入库，2：顾客退货，3:销售出库，4:盘盈入库, 5:盘亏出库, 6:期初入库，7:其他入库，8：退货出库，9：其他出库)")
    @NotNull(message = "单据类型不得为空")
    private int invoiceType;

    @ApiModelProperty("订单编号")
    @NotEmpty(message = "订单编号不得为空")
    private String invoiceNo;

    @ApiModelProperty("商品列表")
    private List<SubstractGoodsReqDTO> detailList;

}
