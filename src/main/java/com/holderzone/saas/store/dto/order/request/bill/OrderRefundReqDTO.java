package com.holderzone.saas.store.dto.order.request.bill;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.trade.TransactionRecordDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 订单退款DTO
 */
@Data
public class OrderRefundReqDTO extends BaseDTO {

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "退款方式 0原路退回 1线下退款")
    private Integer refundType;

    @ApiModelProperty(value = "退款原因")
    private String refundReason;

    @ApiModelProperty(value = "退款总金额")
    @NotNull(message = "退款金额不能为空")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "授权人guid")
    private String authStaffGuid;

    @ApiModelProperty(value = "授权人名称")
    private String authStaffName;

    @ApiModelProperty("授权截图")
    private String picture;

    @ApiModelProperty(value = "退款商品明细")
    private List<DineInItemDTO> dineInItemDTOS;

    @ApiModelProperty(value = "退款方式")
    private List<ActuallyPayFeeDetailDTO> refundTypeList;

    @ApiModelProperty(value = "退款订单guid (非前端传)")
    private Long refundOrderGuid;

    @ApiModelProperty(value = "原支付记录 (非前端传)")
    private List<TransactionRecordDTO> transactionRecordList;

    @ApiModelProperty(value = "原支付记录 (非前端传)")
    private List<TransactionRecordDTO> allTransactionRecordList;

    @ApiModelProperty(value = "原多笔聚合支付记录 (非前端传)")
    private List<TransactionRecordDTO> multipleTransactionRecordList;

    @ApiModelProperty(value = "当前部分退的券抵扣金额 (非前端传)")
    private BigDecimal currentCouponDeductionAmount;

    @ApiModelProperty(value = "当前部分退的券实际购买金额 (非前端传)")
    private BigDecimal currentCouponBuyAmount;

    @ApiModelProperty(value = "营业日 (非前端传)")
    private LocalDate businessDay;

    @ApiModelProperty(value = "是否单个商品 (非前端传)")
    private Boolean singleItemFlag;

    @ApiModelProperty(value = "预定单guid (非前端传)")
    private String reserveGuid;
}