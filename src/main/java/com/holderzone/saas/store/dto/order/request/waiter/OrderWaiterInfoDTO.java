package com.holderzone.saas.store.dto.order.request.waiter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> R
 * @date 2020/11/19 9:54
 * @description
 */
@Data
@ApiModel(value = "服务员类型DTO")
public class OrderWaiterInfoDTO {
    @ApiModelProperty(value = "服务员Guid", required = true)
    private String waiterGuid;
    @ApiModelProperty(value = "服务员类型(0, 无操作员),(1, 喊客员),(2, 服务员),(3, 传菜员),(4, 收台员),(5, 洗碗员)", required = true)
    private Integer waiterType;
    @ApiModelProperty(value = "服务员名称", required = true)
    private String waiterName;
    @ApiModelProperty(value = "服务员编号", required = true)
    private String waiterNo;
}
