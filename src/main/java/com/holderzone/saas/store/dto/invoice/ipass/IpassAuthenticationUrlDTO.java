package com.holderzone.saas.store.dto.invoice.ipass;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@ApiModel(description = "生成认证二维码")
@Data
public class IpassAuthenticationUrlDTO implements Serializable {

    @ApiModelProperty(value = "发送短信手机号")
    private String storeId;

    @ApiModelProperty(value = "订单流水号")
    private String orderNo;

    @ApiModelProperty(value = "1税务app（默认） 2个税app")
    private String ewmlx;

    @ApiModelProperty(value = "登录账号")
    private String account;
}
