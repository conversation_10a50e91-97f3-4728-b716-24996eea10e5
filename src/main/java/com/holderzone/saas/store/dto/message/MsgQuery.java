package com.holderzone.saas.store.dto.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MsgQuery
 * @date 2018/09/25 10:19
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class MsgQuery extends BaseMsgQuery {

    @ApiModelProperty(value = "推送的消息类型，0:小铃铛，1:业务消息，2:系统消息")
    private Integer messageType;

    @ApiModelProperty(value = "状态 0:未读，1:已读")
    private Integer state;

    @ApiModelProperty(value = "messageGuid")
    private String messageGuid;

    public int getIndex() {
        return (this.currentPage <= 0 ? 1 : this.currentPage - 1) * this.pageSize;
    }


}
