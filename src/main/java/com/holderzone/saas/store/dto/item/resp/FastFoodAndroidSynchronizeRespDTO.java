package com.holderzone.saas.store.dto.item.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FastFoodAndroidSynchronizeRespDTO
 * @date 2019/03/02 下午4:55
 * @description //快餐的安卓端同步商品实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "返回给移动端的快餐商品信息DTO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FastFoodAndroidSynchronizeRespDTO implements Serializable {
    @ApiModelProperty(value = "商品集合")
    private List<DishRespDTO> dishList;
    @ApiModelProperty(value = "分类集合")
    private List<TypeSynRespDTO> typeList;
}
