package com.holderzone.saas.store.dto.report.resp;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("商品报表-返回类型")
public class GoodsSalesStatisticDTO {

    @ApiModelProperty(value = "分页和列表")
    private PageInfo<GoodsSalesDTO> pageInfo;

    @ApiModelProperty(value = "销量与金额")
    private GoodsSalesTotalDTO data;
}
