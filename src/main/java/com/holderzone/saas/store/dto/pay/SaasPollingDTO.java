package com.holderzone.saas.store.dto.pay;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SaasPollingDTO
 * @date 2019/03/18 17:52
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class SaasPollingDTO extends BaseDTO {

    @ApiModelProperty("订单号")
    private String orderGuid;

    @ApiModelProperty("支付guid")
    private String payGuid;

}
