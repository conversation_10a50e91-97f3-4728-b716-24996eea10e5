package com.holderzone.saas.store.dto.member.hsm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillCalculateReqDTO
 * @date 2019/01/28 17:32
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class HsmAggPayRespDTO {

    @ApiModelProperty(value = "支付guid，如果有，聚合支付轮询参数")
    private String payGuid;

    @ApiModelProperty(value = "聚合支付订单guid，如果有，聚合支付轮询参数")
    private String orderGuid;

    @ApiModelProperty(value = "结果")
    private Boolean result;
}
