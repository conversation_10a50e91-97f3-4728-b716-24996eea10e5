package com.holderzone.saas.store.dto.order.response.dinein;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityDetailsRespDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.MemberPortrayalDetailsDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseClientActivitiesList;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseIntegralOffset;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.member.MemberInfoRespDTO;
import com.holderzone.saas.store.dto.order.OrderMultiMemberDTO;
import com.holderzone.saas.store.dto.order.OrderMultiMemberPayDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.AdjustOrderRecordDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillMemberCardCalculateRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.mos.secure.ext.annotations.DesensitizationProp;
import com.mos.secure.ext.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineinOrderDetailRespDTO
 * @date 2019/01/16 14:30
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class DineinOrderDetailRespDTO {

    @ApiModelProperty(value = "订单的guid")
    private String guid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "预点餐信息")
    private List<DineInItemDTO> reserveItems;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "反结账原单的guid")
    private Long originalOrderGuid;

    @ApiModelProperty(value = "0:无并单，1:主单， 2:子单")
    private Integer upperState;

    @ApiModelProperty(value = "主单单号")
    private String mainOrderNo;

    @ApiModelProperty(value = "主单guid")
    private String mainOrderGuid;

    @ApiModelProperty(value = "订单来源：PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7",
            required = true)
    private String deviceTypeName;

    @ApiModelProperty(value = "交易模式：交易模式(0：正餐，1：快餐)", required = true)
    private String tradeModeName;

    @ApiModelProperty(value = "交易模式：交易模式(0：正餐，1：快餐)", required = true)
    private Integer tradeMode;

    /**
     * @see com.holderzone.saas.store.enums.BaseDeviceTypeEnum
     */
    @ApiModelProperty(value = "订单来源：PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7",
            required = true)
    private Integer deviceType;

    @ApiModelProperty(value = "结账来源：PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7",
            required = true)
    private String checkoutDeviceTypeName;

    @ApiModelProperty(value = "订单金额（商品总额+附加费）")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "第三方活动可以使用金额")
    private BigDecimal thirdActivityMaxFee;

    @ApiModelProperty(value = "订单剩余可以优惠金额")
    private BigDecimal orderSurplusFee;

    @ApiModelProperty(value = "附加费")
    private BigDecimal appendFee;

    @ApiModelProperty(value = "附加费优惠金额")
    private BigDecimal appendDiscountFee;

    @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "应付金额", required = true)
    private BigDecimal payAble;

    @ApiModelProperty(value = "找零（收款-应收金额）")
    private BigDecimal changeFee;

    @ApiModelProperty(value = "优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "预付金")
    private BigDecimal prepayFee;

    @ApiModelProperty(value = "预定金")
    private BigDecimal reserveFee;

    @ApiModelProperty("预定金退款金额")
    private BigDecimal reserveRefundAmount;

    @ApiModelProperty(value = "预定guid")
    private String reserveGuid;

    /**
     * 订单类型 0预订 1预付金
     */
    @ApiModelProperty("订单类型 0预订 1预付金")
    private Integer orderType = 0;

    @ApiModelProperty("预定备注")
    private String reserveRemark;

    @ApiModelProperty(value = "预定来源")
    private Integer reserveDeviceType;

    /**
     * {@link PaymentTypeEnum}
     */
    @ApiModelProperty("预定金支付方式,1-现金，2-聚合支付，2银行卡支付，10，其他支付方式")
    private Integer reservePaymentType;

    @ApiModelProperty("预定金支付方式名称")
    private String reservePaymentTypeName;

    @ApiModelProperty(value = "订单金额明细（商品总额+附加费）")
    private OrderFeeDetailDTO orderFeeDetailDTO;

    @ApiModelProperty(value = "实收金额明细（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS;


    /**
     * {@link com.holderzone.saas.store.enums.order.DiscountTypeEnum}
     */
    @ApiModelProperty(value = "优惠金额明细（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
    private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

    @ApiModelProperty(value = "团购券总优惠金额")
    private BigDecimal grouponFee;

    @ApiModelProperty(value = "团购券明细")
    private List<GrouponListRespDTO> grouponListRespDTOS;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "状态(1：未结账， 2：已结账， 3：已退款，4：已作废)")
    private Integer state;

    @ApiModelProperty(value = "状态(1：未结账， 2：已结账， 3：已退款，4：已作废)")
    private String stateName;

    @ApiModelProperty(value = "反结账id")
    private String recoveryId;

    @ApiModelProperty(value = "订单反结账类型1：普通单 2：原单 3：新单 4：退单 ")
    private Integer recoveryType;

    @ApiModelProperty(value = "营业日")
    private LocalDate businessDay;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "结算时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTime;

    @ApiModelProperty(value = "创建人")
    private String createStaffName;

    @ApiModelProperty(value = "结账人")
    private String checkoutStaffName;

    /**
     * 用户微信公众号openId
     */
    private String userWxPublicOpenId;

    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    @DesensitizationProp(SensitiveTypeEnum.MOBILE_PHONE)
    @ApiModelProperty(value = "会员电话")
    private String memberPhone;

    @ApiModelProperty(value = "会员姓名")
    private String memberName;

    @ApiModelProperty(value = " 会员支付id")
    private String memberConsumptionGuid;

    @ApiModelProperty(value = "支付卡余额")
    private BigDecimal memberCardBalance;

    @ApiModelProperty(value = "会员本次消费实充金额")
    private BigDecimal memberRechargeAmount;

    @ApiModelProperty(value = "会员本次消费赠送金额")
    private BigDecimal memberGiftAmount;

    @ApiModelProperty(value = "会员本次消费补贴金额")
    private BigDecimal memberSubsidyAmount;

    @ApiModelProperty(value = "多卡/单卡支付返回信息")
    private List<OrderMultiMemberPayDTO> multiMemberPays;

    @ApiModelProperty(value = "牌号")
    private String mark;

    @ApiModelProperty(value = "预结单打印次数")
    private Integer printPreBillNum;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "桌台guid")
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台名字")
    private String diningTableName;

    @ApiModelProperty(value = "是否虚拟台(0:否，1:是)")
    private Integer virtualTable;

    @ApiModelProperty(value = "就餐人数")
    private Integer guestCount;

    @ApiModelProperty(value = "退单信息")
    private ReturnOrderDetailDTO returnOrderDetailDTO;

    @ApiModelProperty(value = "作废订单信息")
    private CancelDetailDTO cancelDetailDTO;

    @ApiModelProperty(value = "退款信息")
    private RefundOrderDetailDTO refundOrderDetailDTO;

    @ApiModelProperty(value = "退单记录")
    private List<RefundOrderRecordDTO> refundOrderRecordList;

    @ApiModelProperty(value = "退货信息")
    private List<ReturnItemDTO> returnItemDTOS;

    @ApiModelProperty(value = "商品")
    private List<DineInItemDTO> dineInItemDTOS;

    @ApiModelProperty(value = "子单订单详情")
    private List<DineinOrderDetailRespDTO> subOrderDetails;

    @ApiModelProperty(value = "关联订单详情")
    private List<DineinOrderDetailRespDTO> otherOrderDetails;

    @ApiModelProperty(value = "会员详情")
    private MemberInfoRespDTO memberInfoRespDTO;

    @ApiModelProperty(value = "新会员详情")
    private ResponseMemberAndCardInfoDTO memberInfoAndCard;

    @ApiModelProperty(value = "会员积分详情")
    private ResponseIntegralOffset integralOffsetResultRespDTO;

    @ApiModelProperty(value = "订单多卡支付信息")
    private List<OrderMultiMemberDTO> orderMultiMembers;

    @ApiModelProperty(value = "优惠活动guid")
    private String activityGuid;

    @ApiModelProperty(value = "第N份优惠活动guid")
    private String nthActivityGuid;

    @ApiModelProperty(value = "订单当前关联的会员卡guid")
    private String memberCardGuid;

    @ApiModelProperty(value = "当前会员卡是否用了会员价，0-没有，1-有")
    private Integer singleItemUsedMemeberPrice;

    @ApiModelProperty(value = "错误原因")
    private String tip;

    @ApiModelProperty(value = "version")
    private Integer version;

    @ApiModelProperty("营销活动列表")
    List<ResponseClientActivitiesList> activityInfos;

    @ApiModelProperty("可否操作订单")
    private Boolean canOperated;

    @ApiModelProperty("是否录入服务员（true：已录入；false：未录入）")
    private Boolean ifInputWaiter;

    @ApiModelProperty("是否是积分商城0：不是，1：是")
    private Integer memberIntegralStore;

    @ApiModelProperty(value = "积分使用方式 0不使用 1积分抵现 2积分兑换")
    private Integer integralType;

    @ApiModelProperty(value = "使用的积分")
    private Integer useIntegral;

    @ApiModelProperty(value = "调整商品记录信息")
    private AdjustOrderRecordDTO adjustOrderRecordDTO;

    @ApiModelProperty(value = "是否用会员价格计算了")
    private Integer calculateByMemberPrice;

    @ApiModelProperty(value = "是否调整单，0：不是调整订单 1：是调整单")
    private Integer adjustState;

    @ApiModelProperty("能否使用优惠活动（true：能；false：否）")
    private Boolean isDiscountActivity;

    /**
     * 超出金额
     * 目前只有第三方活动会有这玩意
     */
    @ApiModelProperty(value = "超出金额")
    private BigDecimal excessAmount;

    @ApiModelProperty(value = "第三方活动详情")
    private List<ThirdActivityDetailsRespDTO> detailList;

    @ApiModelProperty(value = "随行红包金额")
    private BigDecimal followRedPacketFee;

    @ApiModelProperty(value = "是否使用随行红包")
    private Boolean isFollowRedPacket;

    private String payQrCode;

    @ApiModelProperty(value = "退款订单guid")
    private Long refundOrderGuid;

    @ApiModelProperty(value = "可退金额 (所有支付方式 + 团购验券)")
    private BigDecimal refundAbleFee;

    @ApiModelProperty(value = "可退金额 (团购验券)")
    private BigDecimal grouponRefundAbleFee;

    @ApiModelProperty(value = "是否单个商品")
    private Boolean singleItemFlag;

    @ApiModelProperty(value = "是否在结账页面团购验券")
    private Boolean useWholeGrouponFlag;

    @ApiModelProperty(value = "附加费信息")
    private List<SurchargeLinkDTO> surchargeLinkList;

    @ApiModelProperty("开票过滤团购券码")
    private List<String> invoiceGrouponCode;

    @ApiModelProperty("会员优惠差异金额")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "登录方式 0-扫码 1-手机号录入")
    private Integer loginType;

    @ApiModelProperty(value = "是否撤销套餐换菜")
    private Boolean changeCancelFlag;

    @ApiModelProperty(value = "换菜前商品")
    private List<DineInItemDTO> originalDineInItemList;

    @ApiModelProperty(value = "换菜后商品")
    private List<DineInItemDTO> changeDineInItemList;

    /**
     * 挂账信息
     */
    @ApiModelProperty(value = "挂账单位名称")
    private String debtUnitName;

    @ApiModelProperty(value = "挂账联系人")
    private String debtContactName;

    @ApiModelProperty(value = "挂账单位电话")
    private String debtContactTel;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "是否联台单")
    private Boolean associatedFlag;

    @ApiModelProperty(value = "联台单编号")
    private String associatedSn;

    @ApiModelProperty(value = "联台单桌台列表")
    private List<String> associatedTableGuids;

    @ApiModelProperty(value = "联台单桌台列表")
    private List<String> associatedTableNames;

    @ApiModelProperty(value = "会员画像")
    private MemberPortrayalDetailsDTO memberPortrayalDTO;

    @ApiModelProperty(value = "更换时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime transferTime;

    @ApiModelProperty(value = "操作人")
    private String operatorName;

    /**
     * 是否多次支付
     */
    @ApiModelProperty(value = "是否多次支付")
    private Boolean isMultipleAggPay;

    @ApiModelProperty(value = "多次支付总金额")
    private BigDecimal multiplePayFee;

    @ApiModelProperty(value = "是否多次实际支付")
    private Boolean isMultipleActualAggPay;

    @ApiModelProperty(value = "当前桌台的订单guid")
    private String currentTableOrderGuid;

    @ApiModelProperty(value = "是否结账不清台")
    private Boolean checkoutUnCloseTableFlag;

    @ApiModelProperty(value = "麓豆会员id")
    private String ludouMemberGuid;

    @ApiModelProperty(value = "麓豆会员电话")
    private String ludouMemberPhone;

    @ApiModelProperty(value = "麓豆会员名字")
    private String ludouMemberName;

    @ApiModelProperty(value = "麓豆会员详情")
    private BillMemberCardCalculateRespDTO ludouMemberDTO;

    /**
     * 总团购实际支付金额
     */
    private BigDecimal totalCouponBuyPrice;

    @ApiModelProperty(value = "退款打印需要的参数")
    private RefundOrderPrintDTO refundOrderPrintDTO;

    public Optional<List<DineInItemDTO>> optionalDineInItemDTOS() {
        return Optional.ofNullable(dineInItemDTOS);
    }
}
