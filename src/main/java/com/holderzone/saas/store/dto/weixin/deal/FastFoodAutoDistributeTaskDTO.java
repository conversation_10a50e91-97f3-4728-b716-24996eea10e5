package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @description 快餐自动出餐延迟队列
 */
@Data
@Builder
public class FastFoodAutoDistributeTaskDTO implements Serializable {

    private static final long serialVersionUID = -6542676434008863329L;

    @ApiModelProperty(value = "企业GUID")
    private String enterpriseGuid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "订单牌号")
    private String orderDesc;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

}
