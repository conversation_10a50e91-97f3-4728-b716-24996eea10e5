package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@Data
@Api("支付方式")
public class PayWayRespDTO {

	@ApiModelProperty("0:成功，1:失败")
	private Integer result=0;

	@ApiModelProperty("失败原因")
	private String errorMsg;

	@ApiModelProperty(value = "是否有会员支付：1：有，0：无")
	private Integer supportMember=0;

	@ApiModelProperty(value = "会员卡是否可用，1：行，0：不行")
	private Integer enableMember=0;

	@ApiModelProperty(value = "会员卡余额")
	private BigDecimal memberBalance;

	@ApiModelProperty(value = "原价")
	private BigDecimal originAmount;

	@ApiModelProperty(value = "是否有优惠价:1有，0无")
	private Integer supportPreference=0;

	@ApiModelProperty(value = "优惠价")
	private BigDecimal payAmount;

	@ApiModelProperty(value ="会员卡名称")
	private String cardName;

	public static PayWayRespDTO changeFailed(){
		return new PayWayRespDTO().setResult(1).setErrorMsg("订单详情发生变化");
	}
}
