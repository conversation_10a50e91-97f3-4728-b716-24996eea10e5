package com.holderzone.saas.store.dto.print.content;

import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import static com.holderzone.saas.store.dto.print.util.PrintMockUtils.mockStoreName;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "转台单")
public class PrintTurnTableDTO extends PrintDTO implements PrintDataMockito {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private String storeName;

    @NotBlank(message = "原桌位名称不得为空")
    @ApiModelProperty(value = "原桌位", required = true)
    private String srcTableName;

    @NotBlank(message = "新桌位名称不得为空")
    @ApiModelProperty(value = "新桌位", required = true)
    private String destTableName;

    @NotNull(message = "转台时间不得为空")
    @ApiModelProperty(value = "转台时间", required = true)
    private Long turnTime;

    @Override
    public void applyMock() {
        super.applyMock();
        setStoreName(mockStoreName());
        setSrcTableName("测试桌台1");
        setDestTableName("测试桌台2");
        setTurnTime(DateTimeUtils.nowMillis());
    }
}
