package com.holderzone.saas.store.dto.print.template.convertable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 打印文本
 * 包括文本字符串，文本字体
 *
 * <AUTHOR>
 * @date 2018/12/15 14:30
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class Text implements Serializable {

    /**
     * 空文字
     */
    public static final Text EMPTY = new Text("");

    /**
     * 空白文字
     */
    public static final Text BLANK = new Text(" ");

    /**
     * 虚线文字
     */
    public static final Text DOT = new Text("-");

    private static final long serialVersionUID = -5152892642655651236L;

    @ApiModelProperty(value = "文本字符串")
    private String text;

    @ApiModelProperty(value = "文本字体")
    private Font font = Font.SMALL;

    public Text(String text) {
        this.text = text;
    }

    /**
     * 文本对齐方式
     */
    public enum Align {

        /**
         * 左对齐
         */
        Left,

        /**
         * 居中对齐
         */
        Center,

        /**
         * 右对齐
         */
        Right
    }
}
