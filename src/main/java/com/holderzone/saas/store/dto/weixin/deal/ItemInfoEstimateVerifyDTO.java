package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("商品估清校验")
public class ItemInfoEstimateVerifyDTO {

	private String itemName;

	private String skuGuid;

	private String skuName;

	private String unit;

	private Boolean isSoldOut = false;

	private BigDecimal remainNum;


}
