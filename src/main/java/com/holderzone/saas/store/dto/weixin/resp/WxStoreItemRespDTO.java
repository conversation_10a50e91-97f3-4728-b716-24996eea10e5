package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.saas.store.dto.item.resp.SubgroupSynRespDTO;
import com.holderzone.saas.store.dto.item.resp.TagRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreItemRespDTO
 * @date 2019/2/25
 */
@ApiModel("微信门店菜品")
@Data
public class WxStoreItemRespDTO {

    @ApiModelProperty(value = "商品唯一标识", required = true)
    private String itemGuid;
    @ApiModelProperty(value = "分类GUID", required = true)
    private String typeGuid;
    @ApiModelProperty(value = "分类名称", required = true)
    private String typeName;
    @ApiModelProperty(value = "商品类型:1.套餐（不称重，无规格），2.规格商品（单商品，不称重），3.称重商品（单商品，称重）,4,单品", required = true)
    private Integer itemType;
    @ApiModelProperty(value = "maybe null,是否售罄:0 否 1 是")
    private Integer isSoldOut=0;
    @ApiModelProperty(value = "属性组状态:0：无属性; 1:有属性; 2:有必选属性组" +
            "若该商品为套餐，则含义为（与商品数据库存储数据不同）：若子商品中有一个有必选属性，则该字段=2,若所有子商品均无属性，则该字段=0,其余=1", required = true)
    private Integer hasAttr;
    @ApiModelProperty(value = "商品名称", required = true)
    @Size(max = 40)
    private String name;
    @ApiModelProperty(value = "拼音简码", required = true)
    private String pinyin;
    @ApiModelProperty(value = "maybe null,别名", required = true)
    private String nameAbbr;
    @ApiModelProperty(value = "排序号", required = true)
    private Integer sort;
    @ApiModelProperty(value = "maybe null,商品描述")
    private String description;
    @ApiModelProperty(value = "maybe null,商品主图路径")
    private String pictureUrl;
    @ApiModelProperty(value = "是否是热销")
    private Integer isBestseller;
    @ApiModelProperty(value = "是否是新品")
    private Integer isNew;
    @ApiModelProperty(value = "是否是招牌")
    private Integer isSign;
    @ApiModelProperty(value = "可能为空集合，标签集合,举例：[{\"id\":\"isBestseller\",\"name\":\"热销\"},{\"id\":\"isNew\",\"name\":\"新品\"},{\"id\":\"isSign\",\"name\":\"招牌\"}]")
    private List<TagRespDTO> tagList;
    @ApiModelProperty(value = "是否是固定套餐，0：否，1：是", required = true)
    private Integer isFixPkg;
	//修改字段
    @ApiModelProperty(value = "规格集合", required = true)
    private List<WxStoreSkuRespDTO> skuList;

    @ApiModelProperty(value = "可能为空集合，分组集合")
    private List<SubgroupSynRespDTO> subgroupList;
    //修改字段
    @ApiModelProperty(value = "可能为空集合，一级属性集合")
    private List<WxStoreAttrGroupRespDTO> attrGroupList;

    //新增字段
	@ApiModelProperty(value="商品选中数量")
    private BigDecimal currentCount;

	//新增字段
	@ApiModelProperty(value="单品备注")
	private String remark;

	//新增字段
	@ApiModelProperty(value="商品价格=选中(规格+属性)")
	private BigDecimal itemPrice;
}
