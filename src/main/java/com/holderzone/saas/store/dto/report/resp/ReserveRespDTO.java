package com.holderzone.saas.store.dto.report.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.reserve.ReserveDeviceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 预定明细
 */
@Data
public class ReserveRespDTO implements Serializable {

    private static final long serialVersionUID = -1667368987764335434L;

    @ApiModelProperty("预定单guid")
    private String guid;

    @ApiModelProperty(value = "门店guid", required = true)
    @JsonIgnore
    private String storeGuid;

    @ApiModelProperty("门店名称")
    private String storeName;

    /**
     * @see ReserveDeviceTypeEnum
     */
    @ApiModelProperty("来源")
    private Integer deviceType;

    @ApiModelProperty(value = "预定人数", required = true)
    private Integer number;

    @ApiModelProperty("预定单状态")
    private String state;

    @ApiModelProperty("预定单状态")
    private String stateName;

    @ApiModelProperty(value = "预订人姓名", required = true)
    private String name;

    @ApiModelProperty(value = "预订人手机号", required = true)
    private String phone;

    @ApiModelProperty("预定区域")
    @JsonIgnore
    private String area;

    private String tables;

    @ApiModelProperty("预定区域")
    private String areaName;

    /**
     * {@link PaymentTypeEnum}
     */
    @ApiModelProperty("预定金支付方式,1-现金，2-聚合支付，2银行卡支付，10，其他支付方式")
    private Integer paymentType;

    @ApiModelProperty("预定金支付方式名称")
    private String paymentTypeName;

    @ApiModelProperty("预定金金额")
    private BigDecimal reserveAmount;

    @ApiModelProperty("0. 女,1. 男")
    private Byte gender;

    @ApiModelProperty("预定备注")
    private String remark;

    @ApiModelProperty("预定开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime reserveStartTime;

    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty("是否预期")
    private Boolean isDelay;

    @ApiModelProperty("预付金回退")
    private BigDecimal refundAmount;

}
