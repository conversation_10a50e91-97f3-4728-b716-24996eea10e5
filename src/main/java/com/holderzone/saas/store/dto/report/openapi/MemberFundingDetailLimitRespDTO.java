package com.holderzone.saas.store.dto.report.openapi;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.StringUtils;
import lombok.Data;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;
import java.util.List;

/**
 * 会员资金变动明细响应
 */
@Data
public class MemberFundingDetailLimitRespDTO implements Serializable {

    private static final long serialVersionUID = -1025167689243809355L;

    /**
     * 列表数据
     */
    private List<MemberFundingDetailRespDTO> list;

    /**
     * 分页游标
     */
    private String nextCursor;

    public static MemberFundingDetailLimitRespDTO buildEmpty() {
        MemberFundingDetailLimitRespDTO respDTO = new MemberFundingDetailLimitRespDTO();
        respDTO.setList(Lists.newArrayList());
        respDTO.setNextCursor(Strings.EMPTY);
        return respDTO;
    }

    public static MemberFundingDetailLimitRespDTO buildResult(List<MemberFundingDetailRespDTO> list, String nextCursor) {
        MemberFundingDetailLimitRespDTO respDTO = new MemberFundingDetailLimitRespDTO();
        respDTO.setList(list);
        respDTO.setNextCursor(Strings.EMPTY);
        if (!StringUtils.isEmpty(nextCursor)) {
            respDTO.setNextCursor(nextCursor);
        }
        return respDTO;
    }
}
