package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class PrdDstItemBindDTO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    @NotEmpty(message = "商品Guid不得为空")
    @ApiModelProperty(value = "商品Guid")
    private String itemGuid;

    @NotEmpty(message = "规格Id不得为空")
    @ApiModelProperty(value = "规格Id")
    private String skuGuid;
}
