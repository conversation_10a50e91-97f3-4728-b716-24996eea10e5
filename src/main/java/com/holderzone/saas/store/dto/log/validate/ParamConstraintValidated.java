package com.holderzone.saas.store.dto.log.validate;


import com.holderzone.saas.store.dto.log.annotation.Check;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.List;

/**
 * 入参验证
 *
 * @Athor forewei
 * @Email <EMAIL>
 * @Date 2019/12/09
 */
public class ParamConstraintValidated implements ConstraintValidator<Check, Object> {

    /**
     * 合法的参数值，从注解中获取
     */
    private List<String> paramValues;

    public void initialize(Check constraint) {
        //初始化时获取注解上的值
        paramValues = Arrays.asList(constraint.paramValues());
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (paramValues.contains(value)) {
            return true;
        }
        //不在指定的参数列表中
        return false;
    }
}
