package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class OrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    private String guid;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    private Integer isDelete;

    /**
     * 订单号(前端显示用，门店内唯一，格式************)
     */
    private String orderNo;

    /**
     * 交易模式(0：正餐，1：快餐)
     *
     * @see TradeModeEnum
     */
    private Integer tradeMode;

    /**
     * 设备类型(订单来源 BaseDeviceTypeEnum)
     */
    private Integer deviceType;

    /**
     * 客人数
     */
    private Integer guestCount;

    /**
     * 营业日
     */
    private LocalDate businessDay;

    /**
     * 桌台guid
     */
    private String diningTableGuid;

    /**
     * 桌台名称(区域+桌台code)
     */
    private String diningTableName;

    /**
     * 是否虚拟台(0:否，1:是)
     */
    private Integer virtualTable;

    /**
     * 作废原因
     */
    private String cancelReason;

    /**
     * 整单备注
     */
    private String remark;

    /**
     * 快餐牌号
     */
    private String mark;

    /**
     * 预结单打印次数
     */
    private Integer printPreBillNum;

    /**
     * 订单金额（商品总额+附加费）
     */
    private BigDecimal orderFee;

    /**
     * 附加费
     */
    private BigDecimal appendFee;

    /**
     * 找零（收款-应收金额）
     */
    private BigDecimal changeFee;

    /**
     * 实收金额=订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））
     * 应收金额=订单金额-优惠金额-订金(押金)
     */
    private BigDecimal actuallyPayFee;

    /**
     * 预付金（反结账原单聚合支付转入）
     */
    private BigDecimal prepayFee;

    /**
     * 定金
     */
    private BigDecimal reserveFee;

    /**
     * 1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废
     */
    private Integer state;


    /**
     * 订单反结账类型1：普通单 2：原单 3：新单 4：退单
     */
    private Integer recoveryType;

    /**
     * 0:无并单，1:主单， 2:子单
     */
    private Integer upperState;

    /**
     * 主单guid
     */
    private String mainOrderGuid;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 会员卡guid
     */
    private String memberCardGuid;

    /**
     * 会员支付id，反结账传给会员系统
     */
    private String memberConsumptionGuid;

    /**
     * 会员电话
     */
    private String memberPhone;

    /**
     * 会员名字
     */
    private String memberName;

    /**
     * 用户微信公众号openId
     */
    private String userWxPublicOpenId;

    /**
     * 微信点餐二维码
     */
    private String qrcode;

    /**
     * 反结账原单的guid
     */
    private Long originalOrderGuid;

    /**
     * 反结账id(多次反结账所有原单和新单此id相同)
     */
    private Long recoveryId;

    /**
     * 计算用会员价 1为用了
     */
    private Integer caculatByMemberPrice;
    /**
     * 反结账原因
     */
    private String recoveryReason;

    /**
     * 反结账设备类型（BaseDeviceTypeEnum）
     */
    private Integer recoveryDeviceType;

    /**
     * 结账设备类型（BaseDeviceTypeEnum）
     */
    private Integer checkoutDeviceType;

    /**
     * 取消订单设备类型（BaseDeviceTypeEnum）
     */
    private Integer cancelDeviceType;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店联系人电话
     */
    private String storeContactTel;

    /**
     * 本地化上传时间
     */
    @ApiModelProperty(value = "本地化上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkinTime;

    /**
     * 结算时间
     */
    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTime;

    /**
     * 作废时间
     */
    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime cancelTime;

    /**
     * 创建操作人guid
     */
    private String createStaffGuid;

    /**
     * 创建操作人name
     */
    private String createStaffName;

    /**
     * 反结账操作人guid
     */
    private String recoveryStaffGuid;

    /**
     * 反结账操作人name
     */
    private String recoveryStaffName;

    /**
     * 结账操作人guid
     */
    private String checkoutStaffGuid;

    /**
     * 结账操作人name
     */
    private String checkoutStaffName;

    /**
     * 作废订单操作人guid
     */
    private String cancelStaffGuid;

    /**
     * 作废订单操作人name
     */
    private String cancelStaffName;

    /**
     * 订单来源  -2 : 服务器已完成订单 ，-1 : 服务器生成订单未修改,1 : 为本地创建，2为服务器生成订单本地已修改
     */
    private Integer orderSource;

    /**
     * 本地化上传Id
     */
    private String localUploadId;

    /**
     * 聚合支付帐号appId
     */
    @ApiModelProperty(value = "聚合支付帐号appId")
    private String paymentAppId;
}