package com.holderzone.saas.store.dto.report.base;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用的返回消息
 */
@Setter
@Getter
@ToString
public class Message<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private int code;
    private String msg;
    private List<T> list;
    private Map<String, Object> data;
    private Pager pager;

    public Message(int code, String msg, List<T> list, Map<String, Object> data) {
        this.code = code;
        this.msg = msg;
        this.list = list;
        this.data = data;
        this.pager = new Pager();
    }

    public Message(int code, String msg, List<T> list, Map<String, Object> data, Pager pager) {
        this.code = code;
        this.msg = msg;
        this.list = list;
        this.data = data;
    }


    public Message() {
        this.code = 0;
        this.msg = "success!";
        this.list = new ArrayList<>();
        this.data = new HashMap<>(1);
        this.pager = new Pager();
    }

    public Message(List<T> list, Map<String, Object> data, Pager pager) {
        this(0, "success!", list, data, pager);
    }


    public static Message getInstance() {
        return new Message();
    }

    public static Message getLoginInstance() {
        return new Message(0, "success!", new ArrayList<>(), new HashMap<>(), null);
    }

    public static Message getInstance(String msg) {
        Message message = new Message();
        message.setMsg(msg);
        return message;
    }
}
