package com.holderzone.saas.store.dto.weixin.req;


import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel(value="微信h5支付入参")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WxH5PayReqDTO {

	private WxStoreConsumerDTO wxStoreConsumerDTO;

	@ApiModelProperty(value = "微信回调页面地址",required = true)
	private String outNotifyUrl;

	@ApiModelProperty(value = "订单id：不传")
	private String orderGuid;
}
