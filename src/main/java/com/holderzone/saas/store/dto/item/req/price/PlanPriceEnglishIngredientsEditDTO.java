package com.holderzone.saas.store.dto.item.req.price;

import com.holderzone.saas.store.dto.item.common.PlanPriceEditBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 英文配料描述
 * @date 2021/9/29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlanPriceEnglishIngredientsEditDTO extends PlanPriceEditBaseDTO {

    private String englishIngredientsDesc;
}
