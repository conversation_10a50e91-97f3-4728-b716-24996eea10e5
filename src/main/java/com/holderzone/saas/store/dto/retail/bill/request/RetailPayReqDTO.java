package com.holderzone.saas.store.dto.retail.bill.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.retail.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillCalculateReqDTO
 * @date 2019/01/28 17:32
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class RetailPayReqDTO extends BaseDTO {

    @ApiModelProperty(value = "订单guid")

    private String orderGuid;

    @ApiModelProperty(value = "订单金额（商品总额+附加费）")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "附加费")
    private BigDecimal appendFee;

    @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "找零")
    private BigDecimal changeFee;

    @ApiModelProperty(value = "当前的优惠信息")
    private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

    @ApiModelProperty(value = "支付信息")
    @NotEmpty(message = "支付信息不能为空")
    private List<Payment> payments;

    @ApiModelProperty(value = "是否快餐")
    private boolean fastFood;

    @ApiModelProperty(value = "会员密码")
    private String memberPassWord;

    @ApiModelProperty(value = "会员卡guid")
    private String memberInfoCardGuid;

    @ApiModelProperty("员消费记录GUID，如果已经上传过一次订单信息，再次计算其它卡优惠时，只需要传此GUID(菜品没有做变化的情况下)")
    private String memberConsumptionGuid;

    @ApiModelProperty(value = "使用的积分")
    private Integer useIntegral;

    @ApiModelProperty(value = "积分抵扣了多少钱")
    private BigDecimal integralDiscountMoney;

    @ApiModelProperty(value = "version")
    @LockField
    private Integer version;

    @Data
    public static class Payment {

        @ApiModelProperty(value = "交易金额")
        private BigDecimal amount;

        @ApiModelProperty(value = "支付方式 1:现金支付 ，2:聚合支付 ,3：银行卡支付，4：会员卡支付，5：人脸支付，10：其他支付方式")
        @Min(value = 1, message = "支付方式最少为1")
        private Integer paymentType;

        @ApiModelProperty(value = "支付方式名")
        @NotNull(message = "支付方式不能为空")
        private String paymentTypeName;

        @ApiModelProperty(value = "银行流水号")
        private String bankTransactionId;

        @ApiModelProperty(value = "人脸支付失败时安卓自己随机加的3位数字")
        private String faceCode;

    }


}
