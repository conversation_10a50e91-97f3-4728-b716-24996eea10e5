package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Api("优惠券适用的商品")
@Data
@Builder
public class WxVolumeProductRespDTO {
	private String guid;
	private String productKey;
	private String enterpriseGuid;
	private String brandGuid;
	private String storeGuid;
	private Integer dataSource;
	private String productName;
	private String nameAbbr;
	private String classificationCode;
	private String classificationName;
	private String namePinyin;
	private Integer productType;
	private String productSpecName;
	private String productSpecKey;
	private String productSpecGuid;
	private BigDecimal productPrice;
	private String pictureUrlJson;
	private String description;
	private Integer isEnable;
	private LocalDateTime gmtCreate;
	private LocalDateTime gmtModified;
	private String productUnit;
}
