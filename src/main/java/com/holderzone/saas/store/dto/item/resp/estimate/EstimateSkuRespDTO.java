package com.holderzone.saas.store.dto.item.resp.estimate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 估清商品规格返回DTO
 * @date 2022/4/12 11:28
 * @className: EstimateSkuRespDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "估清商品规格返回DTO")
public class EstimateSkuRespDTO implements Serializable {

    private static final long serialVersionUID = 5601110449487122974L;

    /**
     * 商品sku guid
     */
    @NotNull(message = "商品sku不能为空")
    @ApiModelProperty(value = "商品规格Guid")
    private String skuGuid;

    @ApiModelProperty(value = "商品规格名称")
    private String skuName;

    /**
     * 设置数量
     * 限量数量
     * 即设置估清时录入的数量
     */
    @ApiModelProperty(value = "设置数量")
    private BigDecimal limitQuantity;

    /**
     * 当前剩余数量
     */
    @ApiModelProperty(value = "当前剩余数量")
    private BigDecimal residueQuantity;

    /**
     * 是否长期估清（未勾选次日自动移除列表恢复售卖）
     * 1：否  2：是
     */
    @ApiModelProperty(value = "是否长期估清 1：否  2：是")
    private Integer isForeverEstimate;

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    /**
     * 估清修改时间
     */
    @ApiModelProperty(value = "估清修改时间", hidden = true)
    private LocalDateTime estimateGmtModified;
}
