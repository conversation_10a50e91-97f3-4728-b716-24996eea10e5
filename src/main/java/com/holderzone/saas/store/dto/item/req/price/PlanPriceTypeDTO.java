package com.holderzone.saas.store.dto.item.req.price;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @BelongsProject: holder-saas-store-dto
 * @BelongsPackage: com.holderzone.saas.store.dto.item.req.price
 * @Author: li ao
 * @CreateTime: 2024-03-27  11:44
 * @Description:
 * @Version: 1.0
 */
@Data
@ApiModel(value = "分类列表")
public class PlanPriceTypeDTO {

    @ApiModelProperty(value = "GUID")
    private String guid;
    @ApiModelProperty(value = "排序")
    private Integer sort;
    @ApiModelProperty(value = "是否启用（0：否，1：是）")
    private Integer isEnable;
    @ApiModelProperty(value = "名称")
    protected String name;
    @ApiModelProperty(value = "菜谱方案GUID")
    private String pricePlanGuid;

}
