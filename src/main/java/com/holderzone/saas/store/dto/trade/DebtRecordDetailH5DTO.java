package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * H5页面挂账还款记录详情
 *
 * <AUTHOR>
 * @since 2020-12-22
 */
@ApiModel(value = "H5页面挂账还款记录详情")
public class DebtRecordDetailH5DTO {

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    /**
     * 挂账金额
     */
    @ApiModelProperty(value = "挂账金额")
    private BigDecimal debtAmount;

    /**
     * 挂账日期
     */
    @ApiModelProperty(value = "挂账日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime debtDate;

    /**
     * 还款金额
     */
    @ApiModelProperty(value = "还款金额")
    private BigDecimal repaymentAmount;

    /**
     * 是否还款（默认0，未还款，1已还款）
     */
    @ApiModelProperty(value = "是否还款（默认不传查全部！0:未还款，1:已还款）")
    private Integer repaymentStatus;



    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public BigDecimal getDebtAmount() {
        return debtAmount;
    }

    public void setDebtAmount(BigDecimal debtAmount) {
        this.debtAmount = debtAmount;
    }

    public LocalDateTime getDebtDate() {
        return debtDate;
    }

    public void setDebtDate(LocalDateTime debtDate) {
        this.debtDate = debtDate;
    }

    public BigDecimal getRepaymentAmount() {
        return repaymentAmount;
    }

    public void setRepaymentAmount(BigDecimal repaymentAmount) {
        this.repaymentAmount = repaymentAmount;
    }

    public Integer getRepaymentStatus() {
        return repaymentStatus;
    }

    public void setRepaymentStatus(Integer repaymentStatus) {
        this.repaymentStatus = repaymentStatus;
    }
}
