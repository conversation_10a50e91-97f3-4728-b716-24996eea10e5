package com.holderzone.saas.store.dto.report.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2018/09/27 下午 15:31
 * @description
 */
@ApiModel("菜品销售或者赠送统计查询实体")
public class OrderDishQueryDTO extends BaseQueryDTO {


    @ApiModelProperty("菜品类型Guid")
    private String dishTypeGuid;

    @ApiModelProperty("是否套餐")
    private Integer packageDish;

    @ApiModelProperty("搜索框内容")
    private String searchContent;

    @ApiModelProperty("排序字段")
    private String sortField;

    @ApiModelProperty("倒叙(0)或者顺序(1)")
    private Integer asc;

    @ApiModelProperty("赠送报表(1);销售报表(0)")
    private Integer gift;

    public String getDishTypeGuid() {
        return dishTypeGuid;
    }

    public void setDishTypeGuid(String dishTypeGuid) {
        this.dishTypeGuid = dishTypeGuid;
    }

    public Integer getPackageDish() {
        return packageDish;
    }

    public void setPackageDish(Integer packageDish) {
        this.packageDish = packageDish;
    }

    public String getSearchContent() {
        return searchContent;
    }

    public void setSearchContent(String searchContent) {
        this.searchContent = searchContent;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public Integer getAsc() {
        return asc;
    }

    public void setAsc(Integer asc) {
        this.asc = asc;
    }

    public Integer getGift() {
        return gift;
    }

    public void setGift(Integer gift) {
        this.gift = gift;
    }
}
