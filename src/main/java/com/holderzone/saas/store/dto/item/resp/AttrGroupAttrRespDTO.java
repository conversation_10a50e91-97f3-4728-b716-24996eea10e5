package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PropertyGroupPropertyValueRespDTO
 * @date 2019/01/07 下午7:39
 * @description //属性组和属性详情列表的返回DTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "属性列表返回实体")
public class AttrGroupAttrRespDTO implements Serializable {
    @ApiModelProperty(value = "属性组GUID")
    private String attrGroupGuid;

    @ApiModelProperty(value = "所属品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "所属门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "属性组名称")
    private String name;

    @ApiModelProperty(value = "属性组排序")
    private Integer sort;

    @ApiModelProperty(value = "对属性组的描述")
    private String description;

    @ApiModelProperty(value = "是否启用：0：否;1:是")
    private Integer isEnable;

    @ApiModelProperty(value = "图标路径")
    private String iconUrl;

    @ApiModelProperty(value = "是否必选:0 否 1 是")
    private Integer isRequired;

    @ApiModelProperty(value = "是否支持多选:0 否 1 是")
    private Integer isMultiChoice;

    @ApiModelProperty(value = "是否有默认选项:0 否 1 是")
    private Integer withDefault;

    @ApiModelProperty(value = "属性值集合")
    private List<AttrRespDTO> attrList;

    @ApiModelProperty(value = "属性组来源:0/门店自建，1/品牌自建，2被推送")
    private Integer attrGroupFrom;
}
