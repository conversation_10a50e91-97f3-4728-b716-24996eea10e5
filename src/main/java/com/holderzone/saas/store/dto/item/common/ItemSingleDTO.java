package com.holderzone.saas.store.dto.item.common;

import com.holderzone.saas.store.enums.common.BooleanEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishSingleDTO
 * @date 2018/11/30 上午10:07
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class ItemSingleDTO extends ItemLogDTO {
    private static final long serialVersionUID = 1727528918478527413L;
    @ApiModelProperty(value = "最主要的业务请求参数")
    @NotEmpty
    private String data;

    @ApiModelProperty(value = "查询关键字")
    private String keywords;

    @ApiModelProperty(value = "初始化数据时 0：门店  1：品牌")
    private Integer model;

    @ApiModelProperty(value = "商品查询类型：0 全部类型，1 宴会套餐")
    private Integer itemQueryType;

    /**
     * 规则类型 0显示批次 1菜品汇总
     */
    @ApiModelProperty(value = "规则类型 0显示批次 1菜品汇总")
    private Integer ruleType;

    /**
     * kds显示规则guid
     */
    @ApiModelProperty(value = "显示规则guid")
    private String ruleGuid;

    /**
     * 菜谱方案Guid
     */
    @ApiModelProperty(value = "菜谱方案Guid")
    private String planGuid;

    @ApiModelProperty(value = "商品guidList")
    private List<String> itemList;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店Guids")
    private List<String> storeGuids;

    @ApiModelProperty("用户标识")
    private String wxtoken;

    @ApiModelProperty("skuGuidList")
    private List<String> skuGuids;

    @ApiModelProperty(value = "需要过滤的guid")
    private List<String> filterGuidList;

    @ApiModelProperty(value = "是否单店模式")
    private Boolean singleFlag;

    @ApiModelProperty(value = "是否使用限时特价")
    private Integer h5flag = BooleanEnum.FALSE.getCode();

    @ApiModelProperty(value = "是否使用第N份优惠")
    private Integer nthFlag = BooleanEnum.FALSE.getCode();

    @ApiModelProperty(value = "商品分类guid")
    private String typeGuid;
}
