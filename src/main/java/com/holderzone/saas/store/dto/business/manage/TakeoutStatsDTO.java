package com.holderzone.saas.store.dto.business.manage;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class TakeoutStatsDTO implements Serializable {

    private static final long serialVersionUID = -7805108685912694528L;

    @ApiModelProperty(value = "客人数")
    private Integer customerCount;

    @ApiModelProperty(value = "订单数")
    private Integer orderCount;

    @ApiModelProperty(value = "订单数map，不同类型订单统计订单数")
    private Map<Integer, Long> orderCountMap;

    @ApiModelProperty(value = "销售收入")
    private BigDecimal salesIncoming;

    @ApiModelProperty(value = "成本价")
    private BigDecimal costAmount;

    @ApiModelProperty(value = "订单数详情")
    private Map<String, Integer> orderCountDetail;

    @ApiModelProperty(value = "销售收入详情")
    private Map<String, BigDecimal> salesIncomingDetail;

    @ApiModelProperty(value = "单均消费详情")
    private Map<String, BigDecimal> salesIncomingAvgDetail;

    @ApiModelProperty(value = "优惠总额详情")
    private Map<String, BigDecimal> discountAmountDetail;

    @ApiModelProperty(value = "收款合计详情")
    private Map<String, BigDecimal> collectAmountDetail;

    @ApiModelProperty(value = "商家收款详情")
    private Map<String, BigDecimal> shopAmountDetail;

    @ApiModelProperty(value = "优惠订单数详情")
    private Map<String, Integer> discountOrderCountDetail;

    @ApiModelProperty(value = "退款总额")
    private BigDecimal refundAmount;

    @JsonIgnore
    public static TakeoutStatsDTO empty() {
        return new TakeoutStatsDTO()
                .setCustomerCount(0)
                .setOrderCount(0)
                .setCostAmount(BigDecimal.ZERO)
                .setOrderCountDetail(Collections.emptyMap())
                .setSalesIncomingDetail(Collections.emptyMap())
                .setSalesIncomingAvgDetail(Collections.emptyMap())
                .setDiscountAmountDetail(Collections.emptyMap())
                .setCollectAmountDetail(Collections.emptyMap())
                .setShopAmountDetail(Collections.emptyMap())
                .setDiscountOrderCountDetail(Collections.emptyMap())
                .setRefundAmount(BigDecimal.ZERO);
    }

    @JsonIgnore
    public static TakeoutStatsDTO mock() {
        Map<String, Integer> orderCountDetail = new HashMap<>();
        orderCountDetail.put("美团外卖", 1);
        orderCountDetail.put("饿了么外卖", 1);
        Map<String, BigDecimal> salesIncomingDetail = new HashMap<>();
        salesIncomingDetail.put("美团外卖", BigDecimal.ONE);
        salesIncomingDetail.put("饿了么外卖", BigDecimal.ONE);
        Map<String, BigDecimal> salesIncomingAvgDetail = new HashMap<>();
        salesIncomingAvgDetail.put("美团外卖", BigDecimal.ONE);
        salesIncomingAvgDetail.put("饿了么外卖", BigDecimal.ONE);
        Map<String, BigDecimal> discountAmountDetail = new HashMap<>();
        discountAmountDetail.put("美团外卖", BigDecimal.ONE);
        discountAmountDetail.put("饿了么外卖", BigDecimal.ONE);
        Map<String, BigDecimal> collectAmountDetail = new HashMap<>();
        collectAmountDetail.put("美团外卖", BigDecimal.valueOf(2));
        collectAmountDetail.put("饿了么外卖", BigDecimal.valueOf(2));
        Map<String, Integer> discountOrderCountDetail = new HashMap<>();
        discountOrderCountDetail.put("美团外卖优惠", 1);
        discountOrderCountDetail.put("饿了么外卖优惠", 1);
        return new TakeoutStatsDTO()
                .setCustomerCount(0)
                .setOrderCount(0)
                .setCostAmount(BigDecimal.ZERO)
                .setOrderCountDetail(orderCountDetail)
                .setSalesIncomingDetail(salesIncomingDetail)
                .setSalesIncomingAvgDetail(salesIncomingAvgDetail)
                .setDiscountAmountDetail(discountAmountDetail)
                .setCollectAmountDetail(collectAmountDetail)
                .setDiscountOrderCountDetail(discountOrderCountDetail)
                .setRefundAmount(BigDecimal.ZERO);
    }
}
