package com.holderzone.saas.store.dto.print.content.nested;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayRecord
 * @date 2018/07/25 16:11
 * @description 支付方式
 * @program holder-saas-store-print
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "收支汇总")
@Builder
public class InOutRecord implements Serializable {

    private static final long serialVersionUID = -1851067545250063984L;

    @NotBlank(message = "支付方式名称不能为空")
    @ApiModelProperty(value = "支付方式名称", required = true)
    private String name;

    @NotNull
    @ApiModelProperty(value = "订单数", required = true)
    private Long number;

    @NotNull
    @ApiModelProperty(value = "金额", required = true)
    private BigDecimal money;
}
