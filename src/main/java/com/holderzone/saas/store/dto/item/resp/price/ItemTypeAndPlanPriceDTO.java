package com.holderzone.saas.store.dto.item.resp.price;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class ItemTypeAndPlanPriceDTO {

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "图片路径数组json")
    private String pictureUrl = StringUtils.EMPTY;

    @ApiModelProperty(value = "商品描述")
    private String description;

    @ApiModelProperty(value = "英文简述")
    private String englishBrief;

    @ApiModelProperty(value = "英文配料描述")
    private String englishIngredientsDesc;

    @ApiModelProperty(value = "商品规格信息")
    private List<PlanPriceEditItemSkuDTO> itemSkuList;

    @ApiModelProperty(value = "价格方案信息")
    private List<PlanPriceEditDTO> planPriceList;
}
