package com.holderzone.saas.store.dto.open;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-07-05
 * @description 订单分页列表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderPageReq extends BaseReq{

    private Integer currentPage = 1;

    private Integer pageSize = 20;

    private String startTime;

    private String endTime;

    private String searchKey;

    private Integer orderState;

    private Integer state;

    private List<String> storeGuidList;

    private List<String> diningTableGuids;
}
