package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AdditionalFeeDO
 * @date 2018/08/02 下午3:00
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SurchargeUpdateDTO {

    @NotBlank(message = "附加费Guid不得为空")
    @ApiModelProperty(value = "附加费Guid", required = true)
    private String surchargeGuid;

    @NotBlank(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid", required = true)
    private String storeGuid;

    @NotNull(message = "附加费名称不得为空")
    @Size(min = 1, max = 10, message = "附加费名称不得超过10个字符")
    @ApiModelProperty(value = "附加费名称", required = true)
    private String name;

    @NotNull(message = "附加费金额不得为空，范围：[0.01 ，99999.99]")
    @ApiModelProperty(value = "附加费金额：最多2位小数；[0.01 ，99999.99]", required = true)
    private BigDecimal amount;

    @NotNull(message = "收费方式：0=按人，1=按桌")
    @Min(value = 0, message = "收费方式：0=按人，1=按桌")
    @Max(value = 1, message = "收费方式：0=按人，1=按桌")
    @ApiModelProperty(value = "收费方式：0=按人，1=按桌。", required = true)
    private Integer type;

    @NotEmpty(message = "场景")
    @ApiModelProperty(value = "场景：0=正餐，1=快餐", required = true)
    private List<String> tradeModes;

    @ApiModelProperty(value = "有效时间")
    private Integer effectiveTime;

    @ApiModelProperty(value = "区域Guid列表", required = true)
    private List<String> areaGuidList;
}
