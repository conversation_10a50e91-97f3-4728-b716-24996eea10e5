package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutOrderDTO
 * @date 2018/09/08 10:18
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TakeoutOrderDTO extends BasePageDTO {

    private static final long serialVersionUID = 3670997254410704553L;

    @ApiModelProperty(value = "店铺id")
    private Long shopId;

    @ApiModelProperty(value = "店铺名称")
    private String shopName;


    @NotEmpty(message = "外卖单标识不得为空", groups = {
            AcceptOrderGroup.class,
            CancelOrderGroup.class,
            AgreeCancelOrderGroup.class,
            DisagreeCancelOrderGroup.class,
            AgreeRefundOrderGroup.class,
            DisagreeRefundOrderGroup.class,
            GetOrderDetailGroup.class,
    })
    @ApiModelProperty(value = "外卖单标识")
    private String orderGuid;

    @ApiModelProperty(value = "订单状态 -1：已取消，0：待处理，10：已接单/待配送，20：订单配送，100：已完成")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单异常状态 " +
            "0：初始状态，" +
            "10：用户申请“取消订单”，11：处理“同意取消订单”中，12：处理“不同意取消订单”中，" +
            "20：用户申请“退单”，21：处理“同意退单”中，22：处理“不同意退单”中")
    private Integer orderTagStatus;

    @ApiModelProperty(value = "订单是否异常状态")
    private Boolean exInitStatus;

    @ApiModelProperty(value = "订单类型,0=外卖订单  1=微信订单  2=其他订单")
    private Integer orderType;

    @ApiModelProperty(value = "赚餐外卖类型,NETWORK=线上配送；PICK_UP=自提")
    private String orderTypeTcd;

    @ApiModelProperty(value = "订单子类.OrderType=0：0=美团  1=饿了么  2=百度  3=京东 4=滴滴  5=自营（自营外卖平台） 6=赚餐")
    private Integer orderSubType;

    @ApiModelProperty(value = "赚餐外卖订单状态")
    /**
     * PENDING--待处理;
     * COOKING---出餐中;
     * TAKE_A_SINGLE---取单中;
     * DISTRIBUTION----配送中;
     * FINISH-已完成;
     * CANCELLED---已取消;
     * PERSON_PENDING---配送员待接单(已出餐);
     * INVALID---失效
     */
    private String orderStatusTcd;

    @ApiModelProperty(value = "核销码")
    private String writeOffCode;

    @ApiModelProperty(value = "商户名字")
    private String enterpriseName;

    @ApiModelProperty(value = "门店名字")
    private String storeName;

    @ApiModelProperty(value = "商家标识")
    private String enterpriseGuid;

    @ApiModelProperty(value = "门店标识")
    private String storeGuid;

    @ApiModelProperty(value = "外卖订单号")
    private String orderId;

    @ApiModelProperty(value = "外卖订单流水号")
    private String orderViewId;

    @ApiModelProperty(value = "日流水")
    private String orderDaySn;

    @ApiModelProperty(value = "备注")
    private String orderRemark;

    @ApiModelProperty(value = "是否是预订单")
    private Boolean reserve;

    @ApiModelProperty(value = "是否是催单")
    private Boolean reminded;

    @ApiModelProperty(value = "催单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime remindTime;

    @ApiModelProperty(value = "菜品数量")
    private BigDecimal itemCount;

    @ApiModelProperty(value = "顾客姓名")
    private String customerName;

    @ApiModelProperty(value = "顾客手机号")
    private String customerPhone;

    @ApiModelProperty(value = "隐私号")
    private String privacyPhone;

    @ApiModelProperty(value = "顾客地址")
    private String customerAddress;

//    /**
//     * 此字段不会包含recipientAddress字段中@#后面的值
//     */
//    @NotBlank(message = "隐私地址不能为空")
//    @ApiModelProperty(value = "隐私地址", required = true)
//    private String recipientAddressDesensitization;

    @ApiModelProperty(value = "用餐人数")
    private Integer customerNumber;

    @ApiModelProperty(value = "首单用户 0=否 1=是")
    private Boolean firstOrder;

    @ApiModelProperty(value = "配送地址纬度")
    private String shipLatitude;

    @ApiModelProperty(value = "配送地址经度")
    private String shipLongitude;

    @ApiModelProperty(value = "配送员姓名")
    private String shipperName;

    @ApiModelProperty(value = "配送员手机号")
    private String shipperPhone;

    @ApiModelProperty(value = "配送方式")
    private int distributionType;

    @ApiModelProperty(value = "是否三方托运  0=否  1=是  -1=未知")
    private Boolean thirdShipper;

    @ApiModelProperty(value = "是否有发票 0=无发票  1=有发票")
    private Boolean invoiced;

    @ApiModelProperty(value = "发票类型 0=个人 1=企业  -1=未知")
    private Integer invoiceType;

    @ApiModelProperty(value = "发票抬头")
    private String invoiceTitle;

    @ApiModelProperty(value = "纳税人身份证明")
    private String taxpayerId;

    @ApiModelProperty(value = "总价,包括：菜品(原价)+餐盒+配送")
    private BigDecimal total;

    @ApiModelProperty(value = "配送费")
    private BigDecimal shipTotal;

    @ApiModelProperty(value = "菜品消费合计(不含餐盒费)")
    private BigDecimal itemTotal;

    @ApiModelProperty(value = "餐盒费")
    private BigDecimal packageTotal;

    @ApiModelProperty(value = "折扣合计 EnterpriseDiscount+PlatformDiscount+OtherDiscount")
    private BigDecimal discountTotal;

    @ApiModelProperty(value = "商家承担的折扣部分")
    private BigDecimal enterpriseDiscount;

    @ApiModelProperty(value = "商外卖平台承担的折扣部分")
    private BigDecimal platformDiscount;

    @ApiModelProperty(value = "其他折扣")
    private BigDecimal otherDiscount;

    @ApiModelProperty(value = "服务费率(平台抽成比例)0.15=15%")
    private BigDecimal serviceFeeRate;

    @ApiModelProperty(value = "服务费(平台抽成费用)")
    private BigDecimal serviceFee;

    @ApiModelProperty(value = "顾客实际支付金额")
    private BigDecimal customerActualPay;

    @ApiModelProperty(value = "顾客退款菜品")
    private String customerRefundDish;

    @ApiModelProperty(value = "顾客退款金额")
    private BigDecimal customerRefund;

    @ApiModelProperty(value = "门店收款,顾客实际支付的金额扣除服务费(平台抽成)考虑扣除配送费?")
    private BigDecimal shopTotal;

    @ApiModelProperty(value = "是否在线支付  1=在线支付  0=线下付款")
    private Boolean onlinePay;

    @ApiModelProperty(value = "订单创建（下单）时间戳")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "预订单生效/激活时间,非预订单，同下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime activeTime;

    @ApiModelProperty(value = "接单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime acceptTime;

    @ApiModelProperty(value = "接单员工GUID")
    private String acceptStaffGuid;

    @ApiModelProperty(value = "接单员工名字")
    private String acceptStaffName;

    @ApiModelProperty(value = "接单设备id")
    private String acceptDeviceId;

    @ApiModelProperty(value = "接单设备type")
    private Integer acceptDeviceType;

    @ApiModelProperty(value = "期望送达时间  0=无时间要求  >0=具体要求的送达时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime estimateDeliveredTime;

    @ApiModelProperty(value = "配送时间要求  格式化好的时间字符串")
    private String estimateDeliveredTimeString;

    @ApiModelProperty(value = "配送方式(0:自配送；1：一城飞客；2：专送；3：众包)")
    private int deliveryType;

    @ApiModelProperty(value = "配送时间，同取餐时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime deliveryTime;

    @ApiModelProperty(value = "实际送达时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime deliveredTime;

    @ApiModelProperty(value = "取消订单请求时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime cancelReqTime;

    @ApiModelProperty(value = "取消订单请求原因")
    private String cancelReqReason;

    @ApiModelProperty(value = "取消订单请求原因code（抖音使用）")
    private Long cancelReqReasonCode;

    @ApiModelProperty(value = "取消订单请求超时时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime cancelReqExpireTime;

    @ApiModelProperty(value = "取消订单请求回复时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime cancelReplyTime;

    @ApiModelProperty(value = "订单取消类型是否是拒单")
    private Boolean cancelAsReject;

    @NotEmpty(message = "退单请求回复消息体不得为空", groups = DisagreeCancelOrderGroup.class)
    @ApiModelProperty(value = "取消订单请求回复消息体")
    private String cancelReplyMessage;

    @ApiModelProperty(value = "取消订单请求回复员工GUID")
    private String cancelReplyStaffGuid;

    @ApiModelProperty(value = "取消订单请求回复员工名字")
    private String cancelReplyStaffName;

    @ApiModelProperty(value = "退单请求时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime refundReqTime;

    @ApiModelProperty(value = "退单请求原因")
    private String refundReqReason;

    @ApiModelProperty(value = "退单请求超时时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime refundReqExpireTime;

    @ApiModelProperty(value = "退单请求回复时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime refundReplyTime;

    @NotEmpty(message = "退单请求回复消息体不得为空", groups = DisagreeRefundOrderGroup.class)
    @ApiModelProperty(value = "退单请求回复消息体")
    private String refundReplyMessage;

    @ApiModelProperty(value = "退单请求回复员工GUID")
    private String refundReplyStaffGuid;

    @ApiModelProperty(value = "退单请求回复员工名字")
    private String refundReplyStaffName;

    @ApiModelProperty(value = "是否（部分）退款成功：0=未退款或未成功，1=成功")
    private String refundSuccess;

    @ApiModelProperty(value = "是否发起退款")
    private Boolean refundedFlag;

    @ApiModelProperty(value = "订单取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime cancelTime;

    @NotEmpty(message = "取消订单请求原因不得为空", groups = CancelOrderGroup.class)
    @ApiModelProperty(value = "订单取消原因")
    private String cancelReason;

    @ApiModelProperty(value = "取消订单员工GUID")
    private String cancelStaffGuid;

    @ApiModelProperty(value = "取消订单员工名字")
    private String cancelStaffName;

    @ApiModelProperty(value = "订单完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime completeTime;

    @ApiModelProperty(value = "订单营业日")
    private LocalDate businessDay;

    @ApiModelProperty(value = "是否调整")
    private Integer adjustState;


    @ApiModelProperty(value = "订单商品明细")
    private List<OrderRemindDTO> arrayOfRemind;

    @ApiModelProperty(value = "订单商品明细")
    private List<OrderItemDTO> arrayOfItem;

    @ApiModelProperty(value = "订单优惠活动数据")
    private List<OrderDiscountDTO> arrayOfDiscount;

    @ApiModelProperty(value = "订单优惠活动数据")
    private List<OrderLogDTO> arrayOfLog;

    @ApiModelProperty(value = "打印类型 1-外卖单，2-后厨点菜单")
    private Integer printType;

    @Data
    @ApiModel
    @NoArgsConstructor
    public static class OrderRemindDTO implements Serializable {

        private static final long serialVersionUID = 2560652386455710951L;

        @NotEmpty(message = "催单GUID不得为空")
        @ApiModelProperty(value = "催单GUID")
        private String remindGuid;

        @NotEmpty(message = "催单回复内容不得为空")
        @ApiModelProperty(value = "催单回复内容")
        private String replyContent;
    }


    @Data
    @ApiModel
    @NoArgsConstructor
    public static class OrderItemDTO implements Serializable {

        private static final long serialVersionUID = -1020490925057612069L;

        @ApiModelProperty(value = "门店guid")
        private String storeGuid;

        @ApiModelProperty(value = "门店名")
        private String storeName;

        @ApiModelProperty(value = "外卖订单guid")
        private String orderGuid;

        @ApiModelProperty(value = "菜品SKU")
        private String itemSku;

        @ApiModelProperty(value = "菜品GUID")
        private String itemGuid;

        @ApiModelProperty(value = "菜品名称")
        private String itemName;

        @ApiModelProperty(value = "菜品单价")
        private String itemCode;

        @ApiModelProperty(value = "单位")
        private String itemUnit;

        @ApiModelProperty(value = "单价")
        private BigDecimal itemPrice;

        @ApiModelProperty(value = "菜品数量")
        private BigDecimal itemCount;

        @ApiModelProperty(value = "菜品小计")
        private BigDecimal itemTotal;

        @ApiModelProperty(value = "菜品规格")
        private String itemSpec;

        @ApiModelProperty(value = "特殊属性")
        private String itemProperty;

        @ApiModelProperty(value = "餐盒单价")
        private BigDecimal boxPrice;

        @ApiModelProperty(value = "餐盒数量")
        private BigDecimal boxCount;

        @ApiModelProperty(value = "餐盒小计")
        private BigDecimal boxTotal;

        @ApiModelProperty(value = "所属口袋 0=1号口袋  1=2号口袋")
        private Integer cartId;

        @ApiModelProperty(value = "折扣掉的金额单价")
        private BigDecimal discountPrice;

        @ApiModelProperty(value = "菜品折扣比例 1=无折扣，0.85=8.5折扣")
        private BigDecimal discountRatio;

        @ApiModelProperty(value = "折扣掉的金额合计")
        private BigDecimal discountTotal;

        @ApiModelProperty(value = "订单完成时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime completeTime;

        @ApiModelProperty(value = "订单营业日")
        private LocalDate businessDay;

        @ApiModelProperty(value = "结算类型 0=普通消费菜品  1=赠送菜品")
        private Integer settleType;

        @ApiModelProperty(value = "平台实际商品数量（外卖商品有商品映射时改字段值与上面那个字段值不一致）")
        private BigDecimal actualItemCount;

        @ApiModelProperty(value = "门店商品规格guid")
        private String erpItemSkuGuid;

        @ApiModelProperty(value = "绑定门店的商品信息")
        private SkuTakeawayInfoRespDTO skuTakeawayInfoRespDTO;
    }

    @Data
    @ApiModel
    @NoArgsConstructor
    public static class OrderDiscountDTO implements Serializable {

        private static final long serialVersionUID = -4268787374110110272L;

        @ApiModelProperty(value = "外卖订单优惠guid")
        private String discountGuid;

        @ApiModelProperty(value = "外卖订单guid")
        private String orderGuid;

        @ApiModelProperty(value = "优惠活动名称")
        private String discountName;

        @ApiModelProperty(value = "备注说明")
        private String discountRemark;

        @ApiModelProperty(value = "此优惠活动总金额 EnterpriseDiscount+PlatformTotal+OtherTotal")
        private BigDecimal totalDiscount;

        @ApiModelProperty(value = "商家承担的折扣部分")
        private BigDecimal enterpriseDiscount;

        @ApiModelProperty(value = "外卖平台承担的折扣部分")
        private BigDecimal platformDiscount;

        @ApiModelProperty(value = "第三方承担的部分")
        private BigDecimal otherDiscount;
    }

    @Data
    @ApiModel
    @NoArgsConstructor
    public static class OrderLogDTO implements Serializable {

        private static final long serialVersionUID = -5248851369793967559L;

        @ApiModelProperty(value = "订单GUID")
        private String orderGuid;

        @ApiModelProperty(value = "外卖订单ID")
        private String orderId;

        @ApiModelProperty(value = "操作时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime time;

        @ApiModelProperty(value = "操作人")
        private String operator;

        @ApiModelProperty(value = "操作描述")
        private String description;

        @ApiModelProperty(value = "元数据title")
        private String title;

        @ApiModelProperty(value = "元数据body")
        private String body;

        @ApiModelProperty(value = "是否在商户后台显示")
        private Boolean showInWebPage;

        @ApiModelProperty(value = "是否在终端(AIO)显示")
        private Boolean showInEndpoint;

        @ApiModelProperty(value = "记录创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime gmtCreate;

        @ApiModelProperty(value = "记录修改时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime gmtModified;
    }

    public interface AcceptOrderGroup {
    }

    public interface CancelOrderGroup {
    }

    public interface AgreeCancelOrderGroup {
    }

    public interface DisagreeCancelOrderGroup {
    }

    public interface AgreeRefundOrderGroup {
    }

    public interface DisagreeRefundOrderGroup {
    }

    public interface ListOrderGroup {
    }

    public interface GetOrderDetailGroup {
    }
}
