package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel
public class EleCallbackResponse {

    public static EleCallbackResponse SUCCESS = new EleCallbackResponse();

    public static EleCallbackResponse PARAMETER_ERROR = new EleCallbackResponse(201, "参数错误");

    public static EleCallbackResponse SIGNATURE_ERROR = new EleCallbackResponse(202, "签名错误");

    public static EleCallbackResponse UNKNOWN_ERROR = new EleCallbackResponse(299, "未知错误");

    private int code;

    private String message;

    public EleCallbackResponse() {
        code = 200;
        message = "ok";
    }

    public EleCallbackResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
