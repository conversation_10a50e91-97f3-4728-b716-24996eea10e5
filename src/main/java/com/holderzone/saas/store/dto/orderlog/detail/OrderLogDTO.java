package com.holderzone.saas.store.dto.orderlog.detail;

import com.holderzone.saas.store.dto.order.common.OrderDishDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderLogDTO
 * @date 2018/10/09 17:59
 * @description //
 * @program holder-saas-store-order
 */
@Data
public class OrderLogDTO {

    @ApiModelProperty(value = "操作类型（logOperationTypeEnum）")
    private Integer operationType;

    @ApiModelProperty(value = "操作类型name（logOperationTypeEnum）")
    private String operationTypeName;

    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;

    @ApiModelProperty(value = "操作人")
    private String operationStaffName;

    @ApiModelProperty(value = "设备类型:PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7 ")
    private Integer deviceType;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "作废原因")
    private String cancelReason;

    @ApiModelProperty(value = "反结账原因")
    private String recoveryReason;

    @ApiModelProperty(value = "菜品")
    private List<OrderDishDTO> orderDishes;

}
