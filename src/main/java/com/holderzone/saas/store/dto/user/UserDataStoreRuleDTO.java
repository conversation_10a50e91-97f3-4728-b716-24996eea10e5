package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("用户数据门店规则实体")
public class UserDataStoreRuleDTO {

    @ApiModelProperty(value = "请求值、响应值：用户所管理门店（门店GUID，门店名称）")
    private List<UserDataDTO> userStoreData;
}

