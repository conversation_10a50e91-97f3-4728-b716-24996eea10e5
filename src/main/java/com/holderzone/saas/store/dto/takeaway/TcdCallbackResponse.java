package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel
public class TcdCallbackResponse {

    public static TcdCallbackResponse SUCCESS = new TcdCallbackResponse();

    public static TcdCallbackResponse PARAMETER_ERROR = new TcdCallbackResponse(201, "参数错误");

    public static TcdCallbackResponse SIGNATURE_ERROR = new TcdCallbackResponse(202, "签名错误");

    public static TcdCallbackResponse UNKNOWN_ERROR = new TcdCallbackResponse(299, "未知错误");

    private int code;

    private String message;

    public TcdCallbackResponse() {
        code = 200;
        message = "ok";
    }

    public TcdCallbackResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
