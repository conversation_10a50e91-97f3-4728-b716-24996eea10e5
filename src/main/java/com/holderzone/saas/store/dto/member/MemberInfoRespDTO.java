package com.holderzone.saas.store.dto.member;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberInfoRespDTO
 * @date 2018/09/10 15:36
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class MemberInfoRespDTO implements Serializable {

    @ApiModelProperty(value = "会员名字")
    private String name;

    @ApiModelProperty(value = "电话号")
    private String telPhoneNo;

    @ApiModelProperty(value = "该会员可用积分")
    private Integer couldUseScore;

    @ApiModelProperty(value = "会员卡余额")
    private BigDecimal remaining;

    @ApiModelProperty(value = "会员卡guid")
    private String memberGuid;

}
