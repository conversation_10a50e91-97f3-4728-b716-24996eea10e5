package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className UserAuthorityDO
 * @date 20-12-18 上午10:32
 * @description hss_user_authority DO
 * @program holder-saas-store-staff
 */

@Accessors(chain = true)
@ApiModel("用户权限保存请求实体")
@Data
public class UserAuthorityDTO implements Serializable {

    private static final long serialVersionUID = 5829162238319212183L;

    /**
     * guid
     */
    @ApiModelProperty(value = "权限拥有Guid")
    private String guid;

    /**
     *  被授权人guid
     */
    @ApiModelProperty(value = "被授权人guid")
    private String userGuid;

    /**
     * 权限guid
    */
    @ApiModelProperty(value = "权限sourceCode（关联权限名称表的sourceCode）")
    private String sourceCode;

    /**
     * 授权码
     */
    @ApiModelProperty(value = "授权码")
    private String authorityCode;

    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    /**
     * 授权人guid
     */
    @ApiModelProperty(value = "授权人guid")
    private String authorizerGuid;

    /**
     * 授权人账户
     */
    @ApiModelProperty(value = "授权人账户")
    private String authorizerAccount;

    /**
     * 是否人脸录入
     */
    @ApiModelProperty(value = "是否人脸录入")
    private Boolean isInputFace;

    /**
     * 电子税务局注册人姓名
     */
    @ApiModelProperty(value = "电子税务局注册人姓名")
    private String electronicTaxpayerName;

    /**
     * 电子税务局注册手机号
     */
    @ApiModelProperty(value = "电子税务局注册手机号")
    private String electronicTaxpayerPhone;

    public Boolean getInputFace() {
        return isInputFace;
    }

    public void setInputFace(Boolean inputFace) {
        isInputFace = inputFace;
    }

    public String getAuthorizerGuid() {
        return authorizerGuid;
    }

    public void setAuthorizerGuid(String authorizerGuid) {
        this.authorizerGuid = authorizerGuid;
    }

    public String getAuthorizerAccount() {
        return authorizerAccount;
    }

    public void setAuthorizerAccount(String authorizerAccount) {
        this.authorizerAccount = authorizerAccount;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getAuthorityCode() {
        return authorityCode;
    }

    public void setAuthorityCode(String authorityCode) {
        this.authorityCode = authorityCode;
    }


    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public UserAuthorityDTO(String guid, String userGuid, String storeGuid, String sourceCode, String authorityCode) {
        this.guid = guid;
        this.userGuid = userGuid;
        this.sourceCode = sourceCode;
        this.authorityCode = authorityCode;
    }

    public UserAuthorityDTO(String userGuid, String storeGuid, String sourceCode, String authorityCode) {
        this.userGuid = userGuid;
        this.sourceCode = sourceCode;
        this.authorityCode = authorityCode;
    }

    public UserAuthorityDTO(String sourceCode, String authorityCode) {
        this.sourceCode = sourceCode;
        this.authorityCode = authorityCode;
    }

    public UserAuthorityDTO(String authorityCode) {
        this.authorityCode = authorityCode;
    }

    public UserAuthorityDTO() {

    }

}
