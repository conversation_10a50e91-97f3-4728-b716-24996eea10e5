package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MchntValidateDTO
 * @date 2018/09/14 10:45
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class MchntValidateDTO implements Serializable {

    @ApiModelProperty(value = "appId")
    private String appId;

    @ApiModelProperty(value = "appSecretKey")
    private String appSecretKey;

}
