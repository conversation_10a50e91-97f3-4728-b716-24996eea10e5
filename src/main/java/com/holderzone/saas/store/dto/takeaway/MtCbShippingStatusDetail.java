package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
public class MtCbShippingStatusDetail implements Serializable {

    private static final long serialVersionUID = 1486835413702304310L;

    /**
     * 骑手电话
     */
    private String dispatcherMobile;

    /**
     * 骑手姓名
     */
    private String dispatcherName;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 配送状态
     */
    private String shippingStatus;

    /**
     * 发生时间【单位秒】
     */
    private long time;
}
