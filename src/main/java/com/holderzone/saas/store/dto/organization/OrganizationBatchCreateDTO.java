package com.holderzone.saas.store.dto.organization;

import com.holderzone.resource.common.dto.holder.organization.HolderOrganizationResultDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class OrganizationBatchCreateDTO implements Serializable {

    private static final long serialVersionUID = -6079164707784423128L;

    /**
     * 企业guid
     */
    // @NotNull(message = "企业guid不能为空")
    // private String enterpriseGuid;

    @ApiModelProperty("同步范围 0-全部 1-部分")
    private Integer syncArea;

    /**
     * 组织机构列表
     */
    // @NotEmpty(message = "组织机构不能为空")
    private List<HolderOrganizationResultDTO> holderOrganizationResultList;

}
