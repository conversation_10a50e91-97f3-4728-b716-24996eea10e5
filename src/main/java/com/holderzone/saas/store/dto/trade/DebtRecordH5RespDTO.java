package com.holderzone.saas.store.dto.trade;

import com.holderzone.framework.util.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * H5页面查询单位还款记录返回DTO
 *
 * <AUTHOR>
 * @since 2020-12-22
 */
@ApiModel(value = "H5页面查询单位还款记录返回DTO")
public class DebtRecordH5RespDTO {

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String name;

    /**
     * 单位代码
     */
    @ApiModelProperty(value = "单位Code")
    private String code;

    /**
     * 可用额度
     */
    @ApiModelProperty(value = "可用额度")
    private BigDecimal creditLimitLeft;

    /**
     * 总额度
     */
    @ApiModelProperty(value = "总额度")
    private BigDecimal creditLimit;

    /**
     * 挂账金额
     */
    @ApiModelProperty(value = "挂账金额（同时也是合计中的挂账金额）")
    private BigDecimal debtAmount;

    /**
     * 还款金额
     */
    @ApiModelProperty(value = "还款金额（同时也是合计中的还款金额）")
    private BigDecimal repaymentAmount;

    /**
     * 还款记录详情分页
     */
    @ApiModelProperty(value = "还款记录详情分页")
    private Page<DebtRecordDetailH5DTO>  recordDetails;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public BigDecimal getCreditLimitLeft() {
        return creditLimitLeft;
    }

    public void setCreditLimitLeft(BigDecimal creditLimitLeft) {
        this.creditLimitLeft = creditLimitLeft;
    }

    public BigDecimal getCreditLimit() {
        return creditLimit;
    }

    public void setCreditLimit(BigDecimal creditLimit) {
        this.creditLimit = creditLimit;
    }

    public BigDecimal getDebtAmount() {
        return debtAmount;
    }

    public void setDebtAmount(BigDecimal debtAmount) {
        this.debtAmount = debtAmount;
    }

    public BigDecimal getRepaymentAmount() {
        return repaymentAmount;
    }

    public void setRepaymentAmount(BigDecimal repaymentAmount) {
        this.repaymentAmount = repaymentAmount;
    }

    public Page<DebtRecordDetailH5DTO> getRecordDetails() {
        return recordDetails;
    }

    public void setRecordDetails(Page<DebtRecordDetailH5DTO> recordDetails) {
        this.recordDetails = recordDetails;
    }
}
