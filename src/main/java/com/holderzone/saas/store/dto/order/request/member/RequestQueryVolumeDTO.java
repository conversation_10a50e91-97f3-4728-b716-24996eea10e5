package com.holderzone.saas.store.dto.order.request.member;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("查询优惠券列表")
public class RequestQueryVolumeDTO extends BaseDTO {

    /**
     * memberInfoGuid
     */
    private String memberInfoGuid;

    /**
     * orderGuid
     */
    private String orderGuid;

    /**
     * orderNumber
     */
    private String orderNumber;

    @ApiModelProperty(value = "会员卡guid")
    private String memberInfoCardGuid;

    @ApiModelProperty("已勾选的优惠券码")
    private List<String> volumeCodes;
}
