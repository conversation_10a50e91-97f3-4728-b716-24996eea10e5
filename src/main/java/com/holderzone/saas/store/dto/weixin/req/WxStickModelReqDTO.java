package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickModelReqDTO
 * @date 2019/03/12 14:22
 * @description 微信桌贴模板库请求入参
 * @program holder-saas-store
 */
@ApiModel(value = "微信桌贴模板库请求入参", description = "微信桌贴模板库请求入参")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxStickModelReqDTO {

    @ApiModelProperty("模板guid")
    private List<String> list;

    @ApiModelProperty("是否推荐:0/否,1/是")
    private Integer isRecommended;

    @ApiModelProperty("所属模板分类GUID")
    private String category;

}
