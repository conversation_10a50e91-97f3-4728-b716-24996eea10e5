package com.holderzone.saas.store.dto.erp.erpretail.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("创建盘点单")
public class InventoryDetailRespDTO {

    @ApiModelProperty("单据编号")
    private String invoiceNo;

    @ApiModelProperty("日期")
    private String date;

    @ApiModelProperty("票据类型")
    private String invoiceName;

    @ApiModelProperty("经办人")
    private String operator;

    @ApiModelProperty("制单时间")
    private String invoiceTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("制单人")
    private String invoiceMaker;

    @ApiModelProperty("需要盘点的商品列表")
    private List<GoodsSumInfoRespDTO> goodsList;

}
