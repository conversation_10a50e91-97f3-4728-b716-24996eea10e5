package com.holderzone.saas.store.dto.order.common;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SalesOrderDishese
 * @date 2019/11/29 14:50
 * @description //TODO
 * @program IdeaProjects
 */
@Data
@NoArgsConstructor
public class SalesOrderDisheseBO {


    public SalesOrderDisheseBO(String storeDishesId, BigDecimal price, BigDecimal checkCount, BigDecimal practiceSubTotal,
                               String dishesName, String dishesCode, String checkUnit, Integer packageDishes) {
        this.storeDishesId = storeDishesId;
        this.price = price;
        this.checkCount = checkCount;
        this.practiceSubTotal = practiceSubTotal;
        this.dishesName = dishesName;
        this.dishesCode = dishesCode;
        this.checkUnit = checkUnit;
        this.packageDishes = packageDishes;

    }

    /**
     * 菜品skuid
     */
    private String storeDishesId;

    /**
     * 企业级菜品skuid
     */
    private String dishesId;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 商品价格小计
     * 优惠前
     */
    private BigDecimal itemPrice;

    /**
     * 最终商品优惠总价
     */
    private BigDecimal discountTotalPrice;

    /**
     * 结算数量(上菜完成数量，结算金额依此数量)
     */
    private BigDecimal checkCount;

    /**
     * 金额小计
     */
    private BigDecimal practiceSubTotal;

    private String dishesName;

    private String dishesErpName;

    private String dishesSkuName;

    private String dishesCode;

    private String dishTypeName;

    private String checkUnit;

    /**
     * 是否套餐主项（1是 0否）
     */
    private Integer packageDishes;
    /**
     * 是否子项
     */
    private Integer subItemFlag;
    /**
     * 父类Id
     */
    private String parentDishesId;

}
