package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ItemSkuSearchRespDTO {
    @ApiModelProperty(value = "skuGuid",required = true)
    private String skuGuid;
    @ApiModelProperty("sku名称")
    private String skuName;
    @ApiModelProperty(value = "商品名称")
    private String itemName;
    @ApiModelProperty(value = "售卖价格")
    private BigDecimal salePrice;
    @ApiModelProperty(value = "会员价格")
    private BigDecimal memberPrice;
    @ApiModelProperty(value = "外卖价格")
    private BigDecimal takeawayPrice;
    @ApiModelProperty(value = "外卖会员价格")
    private BigDecimal takeawayMemberPrice;
    @ApiModelProperty("单位")
    private String unit;
    @ApiModelProperty("商品Guid")
    private String itemGuid;
    @ApiModelProperty("商品分类Guid")
    private String typeGuid;
    @ApiModelProperty(value = "商品分类名称（冗余小程序端字段）")
    private String typeName;
    @ApiModelProperty(value = "商品主图路径")
    private String pictureUrl;
    @ApiModelProperty(value = "是否被删除")
    private String isDelete;

}
