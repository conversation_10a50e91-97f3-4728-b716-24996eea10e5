package com.holderzone.saas.store.dto.store.area;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreQueryPageDTO
 * @date 2018/07/23 下午5:23
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class AreaQuerySingleDTO implements Serializable {

    private static final long serialVersionUID = -4366026157915732535L;

    /**
     * 桌台区域guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "桌台区域guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "桌台区域guid", required = true)
    private String areaGuid;

    /**
     * 是否查询已删除的门店区域
     */
    @ApiModelProperty(value = "是否查询已删除的门店区域。0(or null)=不查询，1=查询。")
    private Integer deleted;
}
