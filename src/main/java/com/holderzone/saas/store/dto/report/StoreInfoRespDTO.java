package com.holderzone.saas.store.dto.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreInfoRespDTO
 * @date 2018/09/27 11:04
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class StoreInfoRespDTO implements Serializable {

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店name")
    private String storeName;

    @ApiModelProperty(value = "营业日配置起始时间")
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "营业日配置截止时间")
    private LocalDateTime endTime;

}
