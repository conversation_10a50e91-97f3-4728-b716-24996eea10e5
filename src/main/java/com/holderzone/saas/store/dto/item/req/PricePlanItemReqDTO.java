package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> R
 * @date 2021/3/15 15:38
 * @description
 */
@ApiModel(value = "价格方案商品批量操作入参")
@Data
public class PricePlanItemReqDTO {
    @ApiModelProperty(value = "方案guid")
    private String planGuid;

    @ApiModelProperty(value = "是否售完下架 0:上架 1：售完下架 2：立即下架（直接删除） 3：即将下架 ")
    private Integer isSoldOut;

    @ApiModelProperty(value = "菜谱商品guid")
    private List<String> itemGuidList;

    @ApiModelProperty(value = "分类Guid")
    private String typeGuid;
}
