package com.holderzone.saas.store.dto.member.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 一体机基本设置返回
 * @date 2021/6/16 16:18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("一体机基本设置返回")
public class CanteenBasicSetupRespDTO implements Serializable {

    @ApiModelProperty("余额规则guid,编辑时不为空")
    private String balanceRuleGuid;

    @ApiModelProperty("余额扣款顺序 例[2,1,3]")
    private List<Integer> deductionBalanceOrder;

    @ApiModelProperty("是否启用组合支付")
    private Integer isEnableCombinationPayment;
}
