package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import net.bytebuddy.implementation.bind.annotation.Default;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateMerchantConfigRespDTO
 * @date 2019/05/08 10:19
 * @description //TODO 商户后台 - 估清设置 - 自动估清列表
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "估清-自动估清列表")
public class EstimateMerchantConfigRespDTO {


    @ApiModelProperty(value = "业务主键 guid")
    private String guid;

    /**
     * sku guid
     */
    @ApiModelProperty(value = "sku guid")
    private String skuGuid;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 规格名称
     */
    @ApiModelProperty(value = "规格名称")
    private String skuName;

    /**
     * 分类guid
     */
    @ApiModelProperty(value = "分类guid")
    private String typeGuid;
    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String typeName;
    /**
     * 菜品类型
     */
    @ApiModelProperty(value = "菜品类型")
    private Integer itemType;
    /**
     * 菜品类型名称
     */
    @ApiModelProperty(value = "菜品类型名称")
    private String itemTypeName;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;
    /**
     * 当前时间节点是否限量是否限量  0：否  1：是
     */
    @ApiModelProperty(value = "当前时间节点是否限量  1：否  2：是")
    private Integer isTheLimit = 1;

    /**
     * 下个时间节点是否限量是否限量  0：否  1：是
     */
    @ApiModelProperty(value = "下个时间节点是否限量  1：否  2：是")
    private Integer isTheLimitReset = 1;

    /**
     * 限量数量
     */
    @ApiModelProperty(value = "限量数量")
    private BigDecimal limitQuantity = BigDecimal.TEN;
    /**
     * 提醒阈值
     */
    @ApiModelProperty(value = "提醒阈值")
    private BigDecimal reminderThreshold = BigDecimal.TEN;

    /**
     * 当前剩余数量
     */
    @ApiModelProperty(value = "当前剩余数量")
    private BigDecimal residueQuantity = new BigDecimal(-1);

    /**
     * 是否次日置满  0：否  1：是
     */
    @ApiModelProperty(value = "是否次日置满  1：否  2：是")
    private Integer isItReset = 1;

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 以下都是冗余给小程序
     * 商品guid
     */
    @ApiModelProperty(value = "商品guid")
    private String itemGuid;
    @ApiModelProperty("折扣")
    private Double discount;
    @ApiModelProperty("售卖价格")
    private BigDecimal salePrice;
    @ApiModelProperty(value = "虚拟价格（冗余小程序端字段）")
    private BigDecimal virtualPrice;
    @ApiModelProperty(value = "商品图片地址")
    private String pictureUrl;
    @ApiModelProperty(value = "起卖数(非称重即为整数，称重即为小数)")
    private BigDecimal minOrderNum;
    @ApiModelProperty(value = "会员价格")
    private BigDecimal memberPrice;

}
