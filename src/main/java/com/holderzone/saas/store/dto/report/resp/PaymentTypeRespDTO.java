package com.holderzone.saas.store.dto.report.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeRespDTO
 * @date 2018/09/28 15:10
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class PaymentTypeRespDTO implements Serializable {

    @ApiModelProperty(value = "结果集")
    private List<PaymentTypeResp> result;

    @Data
    @ApiModel
    public static class PaymentTypeResp {

        @ApiModelProperty(value = "支付方式name")
        private String paymentName;

        @ApiModelProperty(value = "支付金额")
        private BigDecimal amount;

    }


}
