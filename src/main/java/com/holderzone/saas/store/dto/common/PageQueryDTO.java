package com.holderzone.saas.store.dto.common;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PageQueryDTO
 * @date 2018/07/23 下午5:52
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
@Deprecated//建议分页BasePageDTO
public class PageQueryDTO {

    /**
     * 分页页码
     */
    @NotNull
    @Min(value = 1, message = "分页页码不得为空，且必须大于1")
    @ApiModelProperty(value = "分页页码", required = true)
    private Long pageIndex;

    /**
     * 分页size
     */
    @NotNull
    @Min(value = 1, message = "分页size不得为空，且必须大于1")
    @ApiModelProperty(value = "分页size", required = true)
    private Long pageSize;


    /**
     * 装换位数据库中的偏移index
     *
     * @return
     */
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    public Long getOffsetIndex() {
        return pageSize * (pageIndex - 1);
    }
}
