package com.holderzone.saas.store.dto.journaling.eum;

import com.holderzone.framework.exception.unchecked.BusinessException;

import java.util.Arrays;

public enum SaleDetailTypeEnum {

    SET(1, "套餐"),
    NORMAL1(2, "普通商品"),
    WEIGHT(3, "称重商品"),
    NORMAL2(4, "普通商品"),
    BANQUET_ITEM(5, "宴会套餐");
    private Integer code;
    private String name;

    SaleDetailTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNames(Integer code) {
        return Arrays.stream(SaleDetailTypeEnum.values()).filter(p -> p.getCode().equals(code))
                .findFirst().orElseThrow(() -> new BusinessException("不存在当前code对应的name")).getName();
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
