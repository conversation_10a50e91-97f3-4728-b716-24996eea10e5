package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Api("会员优惠券列表")
public class WxMemberVolumeInfoListRespDTO {
	@ApiModelProperty("可使用优惠券数量")
	private Integer mayUseVolumeNum;
	@ApiModelProperty("已失效优惠券数量")
	private Integer notUseVolumeNum;
	@ApiModelProperty("所有优惠券信息")
	private List<WxMemberVolumeInfoRespDTO> memberVolumeList;
}
