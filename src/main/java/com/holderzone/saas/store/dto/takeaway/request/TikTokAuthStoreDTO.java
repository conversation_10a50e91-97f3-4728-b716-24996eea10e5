package com.holderzone.saas.store.dto.takeaway.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 抖音外卖
 */
@Data
@NoArgsConstructor
public class TikTokAuthStoreDTO implements Serializable {

    private static final long serialVersionUID = 3670997254410704553L;

    @ApiModelProperty(value = "抖音授权店铺透传参数")
    private String state;

    @ApiModelProperty(value = "抖音授权店铺code")
    private String code;
}
