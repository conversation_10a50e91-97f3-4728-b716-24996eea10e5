package com.holderzone.saas.store.dto.config.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ConfigReqQueryDTO
 * @date 2019/05/15 11:19
 * @description //TODO  门店相关配置查询DTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "配置逆向查询DTO")
public class ConfigReverseQueryDTO {

    @ApiModelProperty(value = "类型")
    private Integer dicCode;

    @ApiModelProperty(value = "配置值")
    private String dicValue;
}
