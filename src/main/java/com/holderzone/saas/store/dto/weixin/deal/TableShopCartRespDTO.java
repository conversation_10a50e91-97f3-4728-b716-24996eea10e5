package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("购物车展示")
public class TableShopCartRespDTO {
	@ApiModelProperty(value = "商品id")
	private String itemGuid;
	@ApiModelProperty(value = "分类GUID")
	private String typeGuid;
	@ApiModelProperty(value = "分类名称")
	private String typeName;
	@ApiModelProperty(value = "商品类型:1.套餐，2.多规格，3.称重,4,单品,5,固定套餐")
	private Integer itemType;
	@ApiModelProperty(value = "估清:0 否 1 是,仅适用于单品与称重，套餐与多规格以具体哪种sku估清为准")
	@Builder.Default
	private Integer isSoldOut=1;
	@ApiModelProperty(value = "商品展示价格")
	@Builder.Default
	private BigDecimal showPrice=BigDecimal.ZERO;
	@Builder.Default
	@ApiModelProperty(value = "商品展示会员价")
	private BigDecimal showMemberPrice = BigDecimal.ZERO;
	@ApiModelProperty(value = "商品名称")
	private String name;
	@ApiModelProperty(value = "拼音简码")
	private String pinyin;
	@ApiModelProperty(value = "别名")
	private String nameAbbr;
	@ApiModelProperty(value = "商品描述")
	private String description;
	@ApiModelProperty(value = "商品图")
	private String pictureUrl;
	private List<ItemInfoTagDTO> tagList;
	private List<ItemInfoSkuDTO> skuList;
	private List<ItemInfoAttrGroupDTO> attrGroupList;
	private List<ItemInfoSubgroupDTO> subgroupList;

	@ApiModelProperty(value = "用户选中的商品数量,称重可能是小数")
	private BigDecimal userCount = BigDecimal.ZERO;
}
