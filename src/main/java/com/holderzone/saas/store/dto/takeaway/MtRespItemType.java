package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY,
        getterVisibility = JsonAutoDetect.Visibility.NONE)
public class MtRespItemType implements Serializable {

    @ApiModelProperty("ERP门店id")
    private String ePoiId;

    @ApiModelProperty("菜品分类名称")
    private String name;

    @ApiModelProperty("分类顺序")
    private Integer sequence;
}
