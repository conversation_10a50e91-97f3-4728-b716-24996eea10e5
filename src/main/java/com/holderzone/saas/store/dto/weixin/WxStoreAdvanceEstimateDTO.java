package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("选好了沽清")
public class WxStoreAdvanceEstimateDTO {

	@ApiModelProperty(value="true：存在沽清商品,false,不存在")
	private Boolean estimateResult;

	@ApiModelProperty(value = "估清商品结果")
	List<WxStoreEstimateItem> wxStoreEstimateItemList;
}
