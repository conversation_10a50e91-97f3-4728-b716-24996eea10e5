package com.holderzone.saas.store.dto.takeaway;

/**
 * https://nest-fe.faas.ele.me/openapi/documents/callback
 */
@SuppressWarnings("NonAsciiCharacters")
public enum OwnTypeEnum {

    /**
     * "订单状态(0待支付;1支付完成;2订单完成）")
     */

    待支付("待支付", "订单状态扭转", 0, "订单待支付", "订单消息"),
    支付完成("支付完成", "订单状态扭转", 1, "订单支付完成", "订单消息"),
    订单完成("订单完成", "订单状态扭转", 2, "订单完成", "订单消息"),
    已取消("已取消", "订单状态扭转", 3, "已取消", "订单消息"),
    已退款("已退款", "订单状态扭转", 4, "已退款", "订单消息"),
    支付失败("支付失败", "订单状态扭转", 5, "支付失败", "订单消息"),
    待取单("待取单", "订单状态扭转", 6, "待取单", "订单消息"),
    制作中("制作中", "订单状态扭转", 7, "制作中", "订单消息"),
    待配送("待配送", "订单状态扭转", 8, "待配送", "订单消息"),
    配送中("配送中", "订单状态扭转", 9, "配送中", "订单消息"),
    已拒单("已拒单", "订单状态扭转", 10, "已拒单", "订单消息"),

    NULL("null", "null", -1, "null", "null");

    /**
     * 名称
     */
    private String name;

    /**
     * 分类
     */
    private String category;

    /**
     * Type值
     */
    private int type;

    /**
     * 说明
     */
    private String description;

    /**
     * 消息结构体
     */
    private String structure;

    OwnTypeEnum(String name, String category, int type, String description, String structure) {
        this.name = name;
        this.category = category;
        this.type = type;
        this.description = description;
        this.structure = structure;
    }

    public String getName() {
        return name;
    }

    public String getCategory() {
        return category;
    }

    public int getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public String getStructure() {
        return structure;
    }

    public static OwnTypeEnum ofType(int type) {
        OwnTypeEnum[] values = OwnTypeEnum.values();
        for (OwnTypeEnum value : values) {
            if (type == value.type) {
                return value;
            }
        }
        return OwnTypeEnum.NULL;
    }

    public enum StructType {

        订单消息(10, 10),

        订单状态变更消息(12, 18),

        取消单消息(20, 26),

        退单消息(30, 36),

        催单消息(45, 45),

        运单状态变更消息(51, 76),

        店铺状态变更消息(91, 93),

        授权状态变更消息(100, 100),

        服务市场订购消息(105, 105),

        未知(-1, -1);

        private int begin;

        private int end;

        StructType(int begin, int end) {
            this.begin = begin;
            this.end = end;
        }

        public int getBegin() {
            return begin;
        }

        public int getEnd() {
            return end;
        }

        public static StructType ofType(int type) {
            for (StructType structType : values()) {
                if (structType.begin <= type && type <= structType.end) {
                    return structType;
                }
            }
            return 未知;
        }
    }
}
