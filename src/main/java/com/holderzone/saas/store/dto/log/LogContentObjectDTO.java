package com.holderzone.saas.store.dto.log;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LogContentObjectDTO
 * @date 2018/09/30 下午2:32
 * @description //TODO
 * @program holder-saas-aggregation-merchant
 */
@Data
@Deprecated
public class LogContentObjectDTO {
    @ApiModelProperty(value = "操作子功能")
    private String button;

    @ApiModelProperty(value = "操作对象")
    private List<String> operationTarget;

    @ApiModelProperty(value = "详情")
    private Object tdata;
}
