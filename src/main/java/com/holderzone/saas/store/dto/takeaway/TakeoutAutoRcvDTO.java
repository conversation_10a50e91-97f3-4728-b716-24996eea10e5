package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutAutoRcvDTO
 * @date 2018/09/08 16:10
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TakeoutAutoRcvDTO extends BaseDTO {

    @NotNull(message = "是否自动接单", groups = SetAuto.class)
    @ApiModelProperty(value = "是否自动接单")
    @JsonProperty("isAutomaticAcceptOrder")
    private Boolean autoOrder;

    public interface SetAuto {
    }

    public interface GetAuto {
    }
}
