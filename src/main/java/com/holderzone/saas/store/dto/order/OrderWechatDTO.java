package com.holderzone.saas.store.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderDTO
 * @date 2018-07-25 17:15:31
 * @description
 * @program holder-saas-store-order
 */
@Data
@NoArgsConstructor
public class OrderWechatDTO implements Serializable {

    private static final long serialVersionUID = -6648969016820776028L;

    @ApiModelProperty(value = "结账人")
    private String checkoutStaffGuid;

    @ApiModelProperty(value = "结账人")
    private String checkoutStaffName;

    @ApiModelProperty(value = "订单的guid", hidden = true, required = true)
    private String orderGuid;

    @ApiModelProperty(value = "门店guid", required = true)
    @NotBlank(message = "门店guid不能为空")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称", required = true)
    @NotBlank(message = "门店名称不能为空")
    private String storeName;

    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "牌号")
    private String mark;

    @ApiModelProperty(value = "桌台guid（仅堂食）")
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台名字（仅堂食）")
    private String diningTableName;

    @ApiModelProperty(value = "交易模式：0:堂食,1:快餐,2:外卖", required = true)
    private int tradeMode;

    @ApiModelProperty(value = "就餐人数（仅堂食）")
    private int guestCount;

    /**
     * 是否是主单 0：无子单，1：主单，2：子单
     */

    /**
     * 1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废
     */
    private Integer state;

    /**
     * 设备类型(订单来源 BaseDeviceTypeEnum)
     */
    private Integer deviceType;

}
