package com.holderzone.saas.store.dto.weixin;

import com.holderzone.saas.store.dto.common.BaseRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreMerchantOperationDTO
 * @date 2019/4/18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class WxStoreMerchantOperationDTO {

    private String enterpriseGuid = "";

    private String storeGuid = "";

    private String diningTableGuid = "";

    private String tableCode = "";

    private String areaName = "";

    private String storeName = "";

    private String brandName = "";

    private EstimateItemRespDTO estimateItemRespDTO;

    private String errorMsg = "";

    /**
     * 构造提示消息
     *
     * @param errorMsg 提示信息
     * @return 只有提示信息的返回
     */
    public static WxStoreMerchantOperationDTO constMsg(String errorMsg) {
        return new WxStoreMerchantOperationDTO().setErrorMsg(errorMsg);
    }

    public static WxStoreMerchantOperationDTO estimate() {
        return new WxStoreMerchantOperationDTO().setErrorMsg("商品已沽清");
    }

    public static WxStoreMerchantOperationDTO noItem() {
        return new WxStoreMerchantOperationDTO().setErrorMsg("订单未获取到商品");
    }

    public static WxStoreMerchantOperationDTO outDate() {
        return new WxStoreMerchantOperationDTO().setErrorMsg("订单长时间未处理，请刷新");
    }

    public static WxStoreMerchantOperationDTO tableError() {
        return new WxStoreMerchantOperationDTO().setErrorMsg("桌台无法开台，不能接单");
    }
}
