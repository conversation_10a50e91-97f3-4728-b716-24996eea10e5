package com.holderzone.saas.store.dto.trade.resp;

import com.holderzone.saas.store.dto.item.common.ItemPadCalculateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description pad下单返回实体
 * @date 2021/8/20 10:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("pad下单返回实体")
public class PadOrderPlacementRespDTO {

    @ApiModelProperty("订单guid")
    private String orderGuid;

    @ApiModelProperty("失败原因")
    private String errorMsg;

    @ApiModelProperty(value = "true：表示有商品估清")
    private Boolean hasErrorItem = false;

    @ApiModelProperty(value = "商品估清名称列表")
    private List<ItemPadCalculateDTO> badSkuGuidList;

    public static PadOrderPlacementRespDTO unCombineMasterDevice() {
        return new PadOrderPlacementRespDTO().setErrorMsg("当前门店暂未绑定主机");
    }

    public static PadOrderPlacementRespDTO submitFailed() {
        return new PadOrderPlacementRespDTO().setErrorMsg("下单失败");
    }

    public static PadOrderPlacementRespDTO errorItem(List<ItemPadCalculateDTO> badSkuGuidList) {
        return new PadOrderPlacementRespDTO().setHasErrorItem(true).setBadSkuGuidList(badSkuGuidList);
    }

    public static PadOrderPlacementRespDTO estimateFaild(String errorMsg) {
        return new PadOrderPlacementRespDTO().setErrorMsg(errorMsg).setHasErrorItem(true);
    }

    public static PadOrderPlacementRespDTO upperLimit(String errorMsg) {
        return new PadOrderPlacementRespDTO().setErrorMsg(errorMsg);
    }

    public static PadOrderPlacementRespDTO unSet() {
        return new PadOrderPlacementRespDTO().setErrorMsg("门店暂未配置");
    }

    public static PadOrderPlacementRespDTO modelSetError() {
        return new PadOrderPlacementRespDTO().setErrorMsg("门店点餐模式配置错误");
    }

    public static PadOrderPlacementRespDTO noItem() {
        return new PadOrderPlacementRespDTO().setErrorMsg("已经下单，购物车没有商品");
    }
}
