package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GroupMealSubgroupWebRespDTO
 * @date 2019/12/07 上午10:54
 * @description //团餐套餐分组DTO
 * @program holder
 */

@ApiModel(value = "团餐套餐分组返回实体")
@Data
public class GroupMealSubgroupWebRespDTO extends SubgroupWebRespDTO {

    @ApiModelProperty(value = "该分类下已选商品N道")
    private Integer typeSkuQuantity;

    @ApiModelProperty(value = "该分类下已选商品N份")
    private Integer typeSkuNUmber;

    @ApiModelProperty(value = "该分类下已选商品销售价合计")
    private BigDecimal typeSkuSumPrice;

    @ApiModelProperty(value = "该分类下已选商品总毛利润（销售价-成本价）")
    private BigDecimal typeSkuSumCostPrice;

    @ApiModelProperty(value = "团餐分组中包含的可选规格集合",required = true)
    private List<GroupMealSubItemSkuWebRespDTO> groupMealSubItemSkuList;

}
