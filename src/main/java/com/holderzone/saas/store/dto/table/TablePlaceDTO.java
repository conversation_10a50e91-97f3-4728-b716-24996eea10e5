package com.holderzone.saas.store.dto.table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/27
 * @description 桌台位置
 */
@Data
public class TablePlaceDTO implements Serializable {

    private static final long serialVersionUID = 2508510262173282120L;

    /**
     * 区域guid
     */
    @ApiModelProperty("区域guid")
    private String areaGuid;

    /**
     * 区域名称
     */
    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("桌位guid")
    private String guid;

    @ApiModelProperty("桌位名称")
    private String name;
}
