package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY,
        getterVisibility = JsonAutoDetect.Visibility.NONE)
public class MtReqItemMapping implements Serializable {

    @ApiModelProperty("美团菜品id")
    private String dishId;

    @ApiModelProperty("ERP方菜品id")
    private String eDishCode;

    @ApiModelProperty("SKU映射。如果没有sku维度的信息，此数据结构允许为空")
    private List<MtReqSkuMapping> waiMaiDishSkuMappings;
}
