package com.holderzone.saas.store.dto.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggRefundReqDTO
 * @date 2019/03/15 13:51
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class AggRefundReqDTO {

    @ApiModelProperty(value = "支付唯一标示", required = true)
    private String payGUID;

    @ApiModelProperty(value = "商户发起退款对应的订单号", required = true)
    private String orderGUID;

    @ApiModelProperty(value = "退款类型。0:全款，1:部分，必填", required = true)
    private Integer refundType;

    @ApiModelProperty(value = "退款金额不能为空，单位元，必填", required = true)
    private BigDecimal refundFee;

    @ApiModelProperty(value = "退款的理由，必填", required = true)
    private String reason;

    @ApiModelProperty(value = "商户透传字段")
    private String attachData;

    @ApiModelProperty(value = "商户唯一标识，appId")
    private String appId;

    @ApiModelProperty(value = "发起请求的时间")
    private Long timestamp;

    @ApiModelProperty(value = "开发者ID")
    private String developerId;

    @ApiModelProperty(value = "签名")
    private String signature;

}
