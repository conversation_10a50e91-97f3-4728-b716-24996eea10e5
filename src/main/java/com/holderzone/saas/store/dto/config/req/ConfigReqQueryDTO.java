package com.holderzone.saas.store.dto.config.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ConfigReqQueryDTO
 * @date 2019/05/15 11:19
 * @description //TODO  门店相关配置查询DTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "门店相关配置查询DTO")
public class ConfigReqQueryDTO {

    /**
     * 商户guid
     */
    @ApiModelProperty(value = "商户guid")
    private String enterpriseGuid;
    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 配置类型
     */
    @ApiModelProperty(value = "配置类型,不传")
    private Integer dicCode;

}
