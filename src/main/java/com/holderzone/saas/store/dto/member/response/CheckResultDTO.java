package com.holderzone.saas.store.dto.member.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description 校验返回实体
 * @date 2021/5/17 15:03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("校验返回实体")
public class CheckResultDTO {

    @ApiModelProperty("校验的结果 ture成功 false失败")
    private Boolean checkResult;

    @ApiModelProperty("提示信息")
    private String abnormalPrompt;

    @ApiModelProperty("运营主体会员GUID")
    private String memberInfoGuid;
}
