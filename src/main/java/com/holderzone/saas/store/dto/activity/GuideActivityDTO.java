package com.holderzone.saas.store.dto.activity;

import lombok.Data;

import java.io.Serializable;

@Data
public class GuideActivityDTO implements Serializable {

    private String guid;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 引导前提
     */
    private String guideBefore;

    /**
     * 引导添加到
     */
    private String guideAfter;

    /**
     * 活动状态
     */
    private Integer status;

    /**
     * 自动标签
     */
    private String autoLabel;

    /**
     * 是否跳过 0：否 1：是
     */
    private Integer skipFlag;

    /**
     * 跳过文案
     */
    private String skipText;

    /**
     * 引导类型 0：文字 1：海报
     */
    private Integer guideType;

    /**
     * 引导文字
     */
    private String guideText;

    /**
     * 引导海报
     */
    private String guidePoster;

    /**
     * 目标类型 0:小程序 1:网页
     */
    private Integer targetType;

    /**
     * 网页链接
     */
    private String targetUrl;

    /**
     * 小程序路径
     */
    private String targetRelation;

    /**
     * 目标小程序卡片标题
     */
    private String targetTitle;

    /**
     * 目标小程序封面
     */
    private String targetCover;

    /**
     * 员工码尺寸
     */
    private String qrCodeSize;

    /**
     * 企微活码
     */
    private String qrCode;

    /**
     * 跳转地址
     */
    private String jumpUrl;

}
