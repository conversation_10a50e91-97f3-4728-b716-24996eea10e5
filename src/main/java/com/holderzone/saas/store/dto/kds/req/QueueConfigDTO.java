package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.groups.Default;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class QueueConfigDTO {

    /**
     * 设备GUID
     */
    @NotEmpty(message = "设备GUID不能为空", groups = {Query.class, Update.class})
    @ApiModelProperty(value = "设备GUID")
    private String guid;

    /**
     * 门店GUID
     */
    @NotEmpty(message = "唯一GUID不能为空", groups = {Query.class, Update.class})
    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    /**
     * 队列来源：2=掌控者点餐系统（默认），1=手动输入
     */
    @ApiModelProperty(value = "队列来源")
    private Integer source;

    /**
     * 队列数据包括：4=掌控者点餐设备（默认），2=外卖，1=移动点餐
     */
    @ApiModelProperty(value = "队列数据")
    private Integer content;

    /**
     * 队列信息：2=等餐队列，1=取餐队列，3（默认）
     */
    @ApiModelProperty(value = "队列信息")
    private Integer layout;

    /**
     * 取餐呼叫：2=KDS出堂（默认），1=手动输入
     */
    @ApiModelProperty(value = "取餐呼叫")
    private Integer callMode;

    /**
     * 取餐确认：2=自动确认（默认），1=手动输入
     */
    @ApiModelProperty(value = "取餐确认")
    private Integer confirmMode;

    /**
     * 确认等待时间：4=30秒（默认），5=1分钟，6=1分钟，7=2分钟，8=3分钟，9=5分钟
     */
    @ApiModelProperty(value = "确认等待时间")
    private Integer confirmTtlLevel;

    /**
     * 叫号语音：当前只支持KDS播放取餐呼叫语音，不支持切换到电视等其他设备
     * true（默认）
     */
    @ApiModelProperty(value = "叫号语音")
    private Boolean isCallUpEnable;

    /**
     * 声音模板
     */
    @ApiModelProperty(value = "声音模板")
    private String voiceTemplate;

    /**
     * 播放次数：1次，2次，3次（默认）
     */
    @ApiModelProperty(value = "播放次数")
    private Integer playTimes;

    /**
     * 播放间隔：1秒，3秒，5秒（默认），10秒
     */
    @ApiModelProperty(value = "播放间隔")
    private Integer playInterval;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    public interface Query extends Default {

    }

    public interface Update extends Default {

    }
}