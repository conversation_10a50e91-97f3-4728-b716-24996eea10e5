package com.holderzone.saas.store.dto.order.request.dinein;

import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/10
 **/
@Data
@ApiModel
public class BatchFastOfflineOrderResp {

    /**
     * 成功记录
     */
    private List<FastFoodRespDTO> successList;

    @Data
    @Accessors(chain = true)
    public static class FastFoodRespDTO {

        @ApiModelProperty(value = "订单的guid")
        @OrderLockField
        private String guid;

        @ApiModelProperty("订单编号：前端传递,做唯一标识 orderNo = storeNo + yyyymmdd + 4位AtomicLong")
        private String orderNo;
    }

}
