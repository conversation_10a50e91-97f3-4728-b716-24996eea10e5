package com.holderzone.saas.store.dto.item.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishListDTO
 * @date 2018/07/18 上午10:32
 * @description //快餐相关DTO
 * @program holder-saas-store-menu
 */
@Data
@ApiModel(value = "菜品的列表DTO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DishRespDTO implements Serializable {
    private static final long serialVersionUID = -2645893295664374154L;
    @ApiModelProperty(value = "菜品唯一标识")
    private String dishGuid;
    @ApiModelProperty(value = "分类GUID")
    private String typeGuid;
    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;
    @ApiModelProperty(value = "菜品来源：（0：门店自己创建的菜品，1：品牌自己创建的菜品,2:被推送过来的菜品）")
    private Byte dishFrom;
    @ApiModelProperty(value = "菜品类型：场景1：商户后台使用：1.套餐（不称重，无规格），3.称重菜品（单菜品，称重）,5 .普通。" +
            "场景2：app端获取菜品集合使用：1.套餐（不称重，无规格），2.规格菜品（单菜品，不称重），3.称重菜品（单菜品，称重），4.单品")
    private Byte dishType;

    @ApiModelProperty(value = "菜品类型：场景1：商户后台使用：2.多规格菜品（单菜品，不称重），4.单品。" +
            "场景2：app端使用：值为空")
    private Byte isMultiSku;

    @ApiModelProperty(value = "是否售罄:0 否 1 是")
    private Byte isSoldOut;

    @ApiModelProperty(value = "是否上架:0 否 1 是")
    private Byte isRack;

    @ApiModelProperty(value = "是否有属性:0 否 1 是")
    private Byte hasProperty;
    @ApiModelProperty(value = "菜品编号")
    private String code;
    @ApiModelProperty(value = "菜品名称")
    private String name;
    @ApiModelProperty(value = "拼音简码")
    private String pinyin;
    @ApiModelProperty(value = "菜品简称")
    private String nameAbbr;
    @ApiModelProperty(value = "排序号")
    private Integer sort;

    @ApiModelProperty(value = "起卖数(非称重即为整数，称重即为小数)")
    private BigDecimal minOrderNum;

    @ApiModelProperty(value = "计数单位")
    private String unit;

    @ApiModelProperty(value = "计数单位code(DishUnitEnum)")
    private Integer unitCode;

    @ApiModelProperty(value = "菜品描述")
    private String desc;

    @ApiModelProperty(value = "菜品主图路径")
    private String pictureUrl;
    @ApiModelProperty(value = "是否加入整单折扣(0：否，1：是)")
    private Byte isWholeDiscount;

    @ApiModelProperty(value = "是否参与会员折扣（0：否，1：是）")
    private Byte isMemberDiscount;
    @ApiModelProperty(value = "关联的菜品GUID")
    private String parentGuid;
    @ApiModelProperty(value = "是否参与推荐：0：否，1：是")
    private Byte isRecommend;
    @ApiModelProperty(value = "是否已经被删除0：未被删除，1：已被删除")
    private Byte isDelete;
    @ApiModelProperty(value = "规格集合")
    private List<SkuRespDTO> skuDTOS;
    @ApiModelProperty(value = "套餐所含分组集合")
    private List<DishPkgSubgroupRespDTO> dishPkgSubgroupList;
}
