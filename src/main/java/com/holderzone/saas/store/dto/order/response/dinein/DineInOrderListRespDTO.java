package com.holderzone.saas.store.dto.order.response.dinein;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.enums.trade.StateEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderListRespDTO
 * @date 2018/09/07 19:49
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class DineInOrderListRespDTO extends BasePageDTO{

    private static final long serialVersionUID = -7296236571760374411L;

    @ApiModelProperty(value = "状态(未结账，已结账，已作废, 退单")
    private Integer state;

    @ApiModelProperty(value = "状态(未结账，已结账，已作废, 退单")
    private String stateName;

    @ApiModelProperty(value = "订单反结账类型1：普通单 2：原单 3：新单 4：退单")
    private Integer recoveryType;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "订单guid")
    private String guid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "快餐牌号")
    private String mark;

    @ApiModelProperty(value = "桌台名称(区域+桌台code)")
    private String diningTableName;

    @ApiModelProperty(value = "创建操作人名字")
    private String createStaffName;

    @ApiModelProperty(value = "结账操作人名字")
    private String checkoutStaffName;

    @ApiModelProperty(value = "是否调整单，0：不是调整单  1：是调整单")
    private Integer adjustState;

    private Integer upperState;

    @ApiModelProperty(value = "结算时间")
    private LocalDateTime checkoutTime;

    @ApiModelProperty(value = "是否有退款单")
    private Boolean hasRefundOrderFlag;

    @ApiModelProperty(value = "退款单guid")
    private String refundOrderGuid;

    /**
     * 支付方式名
     */
    @ApiModelProperty(value = "支付方式名")
    private String paymentTypeName;

}
