package com.holderzone.saas.store.dto.erp.erpretail.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

@Data
@ApiModel("商品库存汇总信息")
public class GoodsSumInfoRespDTO {

    @ApiModelProperty("商品Guid")
    @NotEmpty(message = "商品Guid不得为空")
    private String goodsGuid;

    @ApiModelProperty("商品Code")
    private String goodsCode;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商品分类")
    private String goodsClassifyName;

    @ApiModelProperty("库存数量")
    private BigDecimal count;

    @ApiModelProperty("安全库存")
    private BigDecimal safeNum;

    @ApiModelProperty("单位Guid")
    private String unitGuid;

    @ApiModelProperty("单位名称")
    private String unitName;

    @ApiModelProperty("盘点数量")
    @NotEmpty(message = "盘点数量不得为空")
    private BigDecimal inventoryCount;


}
