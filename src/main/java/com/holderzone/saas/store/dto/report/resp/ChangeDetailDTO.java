package com.holderzone.saas.store.dto.report.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 换菜明细
 */
@Data
public class ChangeDetailDTO implements Serializable {

    private static final long serialVersionUID = -4374335766385207822L;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 是否撤销
     */
    private Integer cancelFlag;

    /**
     * 换菜节点
     */
    private String changeNode;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 换菜时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime changeTime;

    /**
     * 套餐名称
     */
    private String subgroupItemName;

    /**
     * 原子菜品
     */
    private String originalItemName;

    /**
     * 原子菜品单价
     */
    private String originalItemPrice;

    /**
     * 原子菜品数量
     */
    private String originalItemCount;

    /**
     * 原子菜品合计
     */
    private BigDecimal originalItemTotalPrice;

    /**
     * 更换菜品
     */
    private String changeItemName;

    /**
     * 更换菜品单价
     */
    private String changeItemPrice;

    /**
     * 更换菜品数量
     */
    private String changeItemCount;

    /**
     * 更换菜品价格
     */
    private BigDecimal changeItemTotalPrice;

    /**
     * 总差额
     */
    private BigDecimal changePrice;

    /**
     * 操作员
     */
    private String createStaffName;
}
