package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "套餐详情返回实体")
public class SkuInfoPkgDTO implements Serializable {

    @ApiModelProperty(value = "商品唯一标识")
    private String itemGuid;

    @ApiModelProperty(value = "套餐商品父标识")
    private String parentGuid;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal itemNum;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品规格标识")
    private String skuGuid;

    @ApiModelProperty(value = "计数单位")
    private String unit;

    @ApiModelProperty(value = "maybe null,规格名称")
    private String skuName;

    @ApiModelProperty(value = "maybe null,编号")
    private String code;

    @ApiModelProperty(value = "售卖价格", required = true)
    private BigDecimal salePrice;

    @ApiModelProperty("核算价")
    private BigDecimal accountingPrice;

    @ApiModelProperty("外卖核算价")
    private BigDecimal takeawayAccountingPrice;
}
