package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@ApiModel(value = "接收前端单参数请求对应的pojo")
@EqualsAndHashCode(callSuper = true)
public class ErpTypeItemQueryDTO extends PageDTO {

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "分类Guid")
    private String typeGuid;
}
