package com.holderzone.saas.store.dto.trade.resp.adjust;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 调整单-正餐/快餐订单实体
 * @date 2022/1/18 14:42
 * @className: AdjustByDineAndFastOrderRespDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "调整单-正餐/快餐订单实体")
public class AdjustByDineAndFastOrderRespDTO implements Serializable {

    private static final long serialVersionUID = -201329164205989586L;

    @ApiModelProperty(value = "状态(1未结账，2已结账，3已作废, 4退单")
    private Integer state;

    @ApiModelProperty(value = "状态(1未结账，2已结账，3已作废, 4退单")
    private String stateName;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "下单时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "快餐牌号")
    private String mark;

    @ApiModelProperty(value = "桌台名称(区域+桌台code)")
    private String diningTableName;

    @ApiModelProperty(value = "结账操作人名字")
    private String checkoutStaffName;

    @ApiModelProperty(value = "是否调整过定单，0：否  1：是")
    private Integer adjustState;
}
