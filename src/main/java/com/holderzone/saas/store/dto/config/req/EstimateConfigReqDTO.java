package com.holderzone.saas.store.dto.config.req;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateConfigReqDTO
 * @date 2019/05/15 18:00
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
@ApiModel(value = "门店配置估清置满时间DTO")
public class EstimateConfigReqDTO {

    /**
     * guid
     */
    @ApiModelProperty(value = "业务主键")
    private String guid;


    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;


    /**
     * 重置时间  hh:mm:ss
     */
    @ApiModelProperty(value = "重置时间  24小时格式： hh:mm")
    private String resetTime;
}
