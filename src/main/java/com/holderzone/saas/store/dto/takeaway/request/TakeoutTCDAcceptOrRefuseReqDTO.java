package com.holderzone.saas.store.dto.takeaway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutShopBindReqDTO
 * @date 2018/09/25 9:07
 * @description
 * @program holder-saas-store-takeaway
 */

/**
 * {
 * 	"deliveryPersonName": "",
 * 	"deliveryPersonPhone": "",
 * 	"orderSn": "",
 * 	"orderState": "",
 * 	"refuseReason": "",
 * 	"token": ""
 * }
 */
@Data
@ApiModel
@NoArgsConstructor
public class TakeoutTCDAcceptOrRefuseReqDTO {

    @ApiModelProperty(value = "订单号(赚餐外卖平台)")
    private String orderSn;

    @ApiModelProperty(value = "外卖配送员姓名")
    private String deliveryPersonName;

    @ApiModelProperty(value = "外卖配送员电话")
    private String deliveryPersonPhone;

    /**
     * 订单状态:
     * PENDING         ---> 待处理(外卖订单)、未使用（到店消费）、待发货（快寄配送到家）,
     * COOKING         ---> 出餐中（外卖订单）、已发货（快寄配送到家）,
     * PERSON_PENDING  ---> 配送员待接单（外卖订单）
     * TAKE_A_SINGLE   ---> 取单中（外卖订单）,
     * DISTRIBUTION    ---> 配送中（外卖订单）,
     * FINISH          ---> 已完成（外卖订单、到店消费、快寄配送到家）,
     * CANCELLED       ---> 已取消（外卖订单、到店消费、快寄配送到家）,
     * INVALID         ---> 失效
     */
    @ApiModelProperty(value = "订单状态")
    private String orderState;

    @ApiModelProperty(value = "取消原因")
    private String refuseReason;

    @ApiModelProperty(value = "拒绝时间")
    private LocalDateTime refuseTime;

    @ApiModelProperty(value = "token")
    private String token;

}
