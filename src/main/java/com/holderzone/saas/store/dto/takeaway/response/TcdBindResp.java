package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class TcdBindResp {

    @ApiModelProperty(value = "店铺id")
    private String id;

    @ApiModelProperty(value = "店铺地址")
    private String address;

    @ApiModelProperty(value = "绑定状态，0-未绑定 1-已绑定")
    private Integer bindStatus;

    @ApiModelProperty(value = "店铺名称")
    private String name;

    @ApiModelProperty(value = "店铺电话")
    private String phone;

    @ApiModelProperty(value = "绑定token")
    private String token;

    @ApiModelProperty(value = "掌控者门店ID")
    private String holderMerchantId;

    @ApiModelProperty(value = "赚餐平台ID")
    private String platformId;
}
