package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/05/07
 * @description 支付方式支付模式
 */
@Data
@ApiModel
public class PaymentTypeModeDTO implements Serializable {


    @ApiModelProperty(value = "支付方式guid")
    private String paymentTypeGuid;

    @ApiModelProperty(value = "storeGuid")
    private String storeGuid;

    @ApiModelProperty(value = "支付模式，0:先登录后支付，1:直接支付")
    private Integer paymentMode;

    @ApiModelProperty(value = "是否支持多卡支付")
    private Integer paymentMultiCard;

}
