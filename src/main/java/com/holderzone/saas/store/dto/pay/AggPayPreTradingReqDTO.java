package com.holderzone.saas.store.dto.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @version 1.0
 * @className PreTradingReqDTO
 * @date 2019/03/14 10:11
 * @description 请求聚合支付实体
 * @program holder-saas-store-dto
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AggPayPreTradingReqDTO {

    @ApiModelProperty(value = "支付金额", required = true)
    private BigDecimal amount;

    @ApiModelProperty(value = "商户自己的订单编号！！", required = true)
    private String orderGUID;

    @ApiModelProperty(value = "本次支付唯一标示", required = true)
    private String payGUID;

    @ApiModelProperty(value = "本次订单交易的商品名称！！", required = true)
    private String goodsName;

    @ApiModelProperty(value = "对应订单的描述！！", required = true)
    private String body;

    @ApiModelProperty(value = "支付功能的id; " +
            "31:支付宝二维码支付(客户扫我们代理商返回给的二维码)、" +
            "2:支付宝条码支付(扫码枪)、3:支付宝公众号支付、" +
            "51:微信二维码支付、9:微信条码支付、10:微信公众号支付。105:kbzPay" +
            "扫码枪不用传", required = true)
    private String payPowerId;

    @ApiModelProperty(value = "签约的协议id")
    private String agreementNo;

    @ApiModelProperty(value = "对应订单附加描述！!")
    private String description;

    @ApiModelProperty(value = "对应订单附加数据！！")
    private String extra;

    @ApiModelProperty(value = "商户分发渠道，商户自定义来标记该笔订单属于的渠道方（支付平台会记录该标记，但不做任何处理）")
    private String cpChannel;

    @ApiModelProperty(value = "交易币种，默认RMB")
    private String currency = "RMB";

    @ApiModelProperty(value = "发起条码支付的授权码，当微信、支付宝条码支付时候，必填！！")
    private String authCode;

    @ApiModelProperty(value = "设备终端号,微信、支付宝条码支付时候，此参数必填！！")
    private String terminalId;

    @ApiModelProperty(value = "商户公众号APPID！！")
    private String subAppId;

    @ApiModelProperty(value = "用户在微信公众号或者微信小程序下的唯一标识！！")
    private String subOpenId;

    @ApiModelProperty(value = "场景信息，商户对接微信h5时候必填！！")
    private String sceneInfo;

    @ApiModelProperty(value = "用户在京东、支付宝平台唯一 id，（支付宝固定二维码、京东支付用到，如果不传，则返回授权地址authUrl）！！")
    private String openId;

    @ApiModelProperty(value = "商户传来的额外数据，不做处理，透传。")
    private String attachData;

    @ApiModelProperty(value = "商户名")
    private String enterpriseName;

    @ApiModelProperty(value = "门店名称，必须传", required = true)
    private String storeName;

    @ApiModelProperty(value = "聚合支付回调的地址，调用方自定义，这个回调地址是聚合支付直接回调地址")
    private String outNotifyUrl;

    @ApiModelProperty(value = "支付成功跳转地址，用于主扫，支付成功后银行跳转")
    private String callBackUrl;

    @ApiModelProperty(value = "商户唯一标识，appId,云端配置有，交易中心组装")
    private String appId;

    @ApiModelProperty(value = "发起请求的时间", required = true)
    private Long timestamp;

    @ApiModelProperty(value = "开发者ID，交易中心组装", required = true)
    private String developerId;

    @ApiModelProperty(value = "签名，交易中心组装")
    private String signature;

    /**
     * 商户账户号
     */
    private String merchantUserId;


    @ApiModelProperty(value = "客户端ip")
    private String clientIp;
}
