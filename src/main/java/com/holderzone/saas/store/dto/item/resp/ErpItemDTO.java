package com.holderzone.saas.store.dto.item.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ErpItemDTO implements Serializable {

    private static final long serialVersionUID = 8017709063480242375L;

    @ApiModelProperty(value = "商品Guid")
    private String guid;

    @ApiModelProperty(value = "商品名称（商品规格）")
    private String name;

    @ApiModelProperty(value = "商品规格ID")
    private String skuId;

    @ApiModelProperty(value = "商品图片")
    private String icon;

    @JsonIgnore
    @ApiModelProperty(value = "排序字段")
    private Integer sort;

    @JsonIgnore
    @ApiModelProperty(value = "item主键，辅助排序")
    private Long id;
}
