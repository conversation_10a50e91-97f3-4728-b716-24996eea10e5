package com.holderzone.saas.store.dto.organization;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreBizDTO
 * @date 2019/08/14 10:40
 * @description 门店营页信息DTO
 * @program holder-saas-store
 */
@Data
@ApiModel("门店营页信息DTO")
public class StoreBizDTO {
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "详细地址")
    private String addressDetail;

    @ApiModelProperty(value = "联系人电话")
    private String contactTel;

    @ApiModelProperty(value = "营业开始时间", required = true)
    @JsonFormat(pattern = "HH:mm:ss")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime businessStart;

    @ApiModelProperty(value = "营业结束时间", required = true)
    @JsonFormat(pattern = "HH:mm:ss")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime businessEnd;
}
