package com.holderzone.saas.store.dto.takeaway.jd;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Assert;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopBindReqDTO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

@Data
public class CallBackDTO {

    @JSONField(name = "expires_in")
    private String expiresIn;

    private String time;

    private String uid;

    private String token;

    @JSONField(name = "user_nick")
    private String userNick;

    private String venderId;

    /**
     * 应用key
     */
    private String appKey;

    /**
     * 应用secret
     */
    @JSONField(name = "appsecert")
    private String appSecret;

    /**
     * 自定义参数
     */
    private String invokeId;


    @JsonIgnore
    public String getEnterpriseGuid(){
        if(StringUtils.isEmpty(invokeId)){
            return null;
        }
        String[] split;
        try {
            split = URLDecoder.decode(invokeId,"GBK").split("/");
        } catch (UnsupportedEncodingException e) {
            throw new BusinessException("透传字段参数错误");
        }
        Assert.valid(2 == split.length, "透传字段参数错误");
        return split[0];
    }

    @JsonIgnore
    public String getBrandGuid() {
        if(StringUtils.isEmpty(invokeId)){
            return null;
        }
        String[] split;
        try {
            split = URLDecoder.decode(invokeId,"GBK").split("/");
        } catch (UnsupportedEncodingException e) {
            throw new BusinessException("透传字段参数错误");
        }
        Assert.valid(2 == split.length, "透传字段参数错误");
        return split[1];
    }

    public static String getStateUsingGuid(TakeoutShopBindReqDTO takeoutShopBindReqDTO) {
        return takeoutShopBindReqDTO.getEnterpriseGuid() + "/" + takeoutShopBindReqDTO.getBrandGuid();
    }
}
