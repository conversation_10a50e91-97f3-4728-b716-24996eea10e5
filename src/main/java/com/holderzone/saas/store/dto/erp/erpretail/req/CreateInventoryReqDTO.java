package com.holderzone.saas.store.dto.erp.erpretail.req;

import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSumInfoRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("创建盘点单")
public class CreateInventoryReqDTO {

    @ApiModelProperty("票据类型")
    @NotEmpty(message = "票据类型不得为空")
    private String invoiceType;

    @ApiModelProperty("盘点人员")
    private String operator;

    @ApiModelProperty("盘点日期")
    private String inventoryDate;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("需要盘点的商品列表")
    private List<GoodsSumInfoRespDTO> goodsList;

}
