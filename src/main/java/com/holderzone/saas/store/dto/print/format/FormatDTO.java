package com.holderzone.saas.store.dto.print.format;

import com.holderzone.saas.store.dto.print.content.base.FormatDefaults;
import com.holderzone.saas.store.dto.print.format.metadata.CustomMetadata;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel("打印模板")
public class FormatDTO implements FormatDefaults, Serializable {

    private static final long serialVersionUID = 6179378383566481468L;

    @Nullable
    @ApiModelProperty(value = "唯一标识")
    private String guid;

    @NotBlank(message = "模板名称不能为空")
    @ApiModelProperty(value = "模板名称")
    private String name;

    @NotBlank(message = "门店GUID不能为空")
    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @NotNull(message = "票据类型不能为空")
    @ApiModelProperty(value = "票据类型")
    private Integer invoiceType;

    @Nullable
    @ApiModelProperty(value = "纸张宽度", notes = "58/80，修改格式时不传，测试格式时必传")
    private Integer pageSize;

    @Nullable
    @ApiModelProperty(value = "设备ID", notes = "修改格式时不传，测试格式时必传")
    private String deviceId;

    @Valid
    @Nullable
    @ApiModelProperty(value = "页眉列表")
    private List<CustomMetadata> headers;

    @Valid
    @Nullable
    @ApiModelProperty(value = "页脚列表")
    private List<CustomMetadata> footers;

    @Nullable
    @ApiModelProperty(value = "是否启用")
    private Boolean isEnable;

    @Override
    public void applyDefault() {
        if (this.name == null) {
            this.name = "默认模板";
        }
        if (this.pageSize == null) {
            this.pageSize = 80;
        }
        if (this.headers == null) {
            this.headers = Collections.emptyList();
        }
        if (this.footers == null) {
            this.footers = Collections.emptyList();
        }
    }
}
