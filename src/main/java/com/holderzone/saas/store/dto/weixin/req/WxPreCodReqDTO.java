package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxPreCodReqDTO
 * @date 2019/03/06 10:53
 * @description 微信获取预授权码请求参数
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("微信获取预授权码请求参数")
public class WxPreCodReqDTO {

    @ApiModelProperty("品牌guid")
    @NotEmpty(message = "品牌Guid不能为空")
    private String brandGuid;

    @ApiModelProperty("企业guid")
    @NotEmpty(message = "企业Guid不能为空")
    private String enterpriseGuid;

    @ApiModelProperty("appId")
    @NotEmpty(message = "appId不能为空")
    private String appId;

    @ApiModelProperty("当前请求类型，0：登陆，1：绑定")
    private Integer reqType;

    @ApiModelProperty("会员guid")
    private String memberInfoGuid;

    @ApiModelProperty("运营主体guid")
    private String operSubjectGuid;
}
