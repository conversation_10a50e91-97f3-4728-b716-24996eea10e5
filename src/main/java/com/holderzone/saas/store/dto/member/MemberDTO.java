package com.holderzone.saas.store.dto.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/8/21.
 */
@Data
public class MemberDTO extends BaseDTO{

    @ApiModelProperty(value = "业务主键")
    private String memberGuid;

    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "性别 0：女，1:男，2：保密")
    private Byte sex;

    @ApiModelProperty(value = "会员账号")
    private String accountNumber;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "创建时间")
    private LocalDate birthday;

    @ApiModelProperty(value = "会员有效期（开始日期）")
    private LocalDate startDate;

    @ApiModelProperty(value = "会员有效期（截止日期）")
    private LocalDate expiryDate;

    @ApiModelProperty(value = "最近登录日期")
    private LocalDateTime recentlyLoginDate;

    @ApiModelProperty(value = "最近消费日期")
    private LocalDateTime recentlyConsumeDate;

    @ApiModelProperty(value = "会员等级的业务主键")
    private String memberGradeGuid;

    @ApiModelProperty(value = "累计消费次数")
    private Integer totalConsumeNum;

    @ApiModelProperty(value = "累计充值次数")
    private Integer totalPrepaidNum;

    @ApiModelProperty(value = "累计积分")
    private Integer totalIntegral;

    @ApiModelProperty(value = "剩余积分")
    private Integer residualIntegral;

    @ApiModelProperty(value = "累计消费金额（全部消费）")
    private BigDecimal totalConsumeFee;

    @ApiModelProperty(value = "累计支付金额（余额支付）")
    private BigDecimal totalPayFee;

    @ApiModelProperty(value = "累计充值金额")
    private BigDecimal totalPrepaidFee;

    @ApiModelProperty(value = "累计赠送金额")
    private BigDecimal totalPresentFee;

    @ApiModelProperty(value = "账户余额")
    private BigDecimal balance;

    @ApiModelProperty(value = "操作人guid")
    private String staffGuid;

    @ApiModelProperty(value = "操作人姓名")
    private String staffName;
}
