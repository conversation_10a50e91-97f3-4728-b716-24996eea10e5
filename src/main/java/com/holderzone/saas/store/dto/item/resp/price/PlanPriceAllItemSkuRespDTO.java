package com.holderzone.saas.store.dto.item.resp.price;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 所有为已启用、即将启用、暂不启用菜谱的所有商品规格信息
 * @date 2021/9/28
 */
@Data
@ApiModel(value = "所有为已启用、即将启用、暂不启用菜谱的所有商品")
public class PlanPriceAllItemSkuRespDTO {

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "规格guid")
    private String skuGuid;

    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty(value = "商品规格名称")
    private String itemSkuName;
}
