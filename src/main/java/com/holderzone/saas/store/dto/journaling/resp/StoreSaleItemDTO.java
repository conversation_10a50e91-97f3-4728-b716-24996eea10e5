package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/8/29 11:39
 */
@ApiModel(value = "销售额返回值")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StoreSaleItemDTO {

    @ApiModelProperty(value = "时间段字符串")
    private String time;

    @ApiModelProperty(value = "价格")
    private BigDecimal sumActuallyPayFee;
}
