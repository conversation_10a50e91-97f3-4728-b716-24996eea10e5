package com.holderzone.saas.store.dto.print.deprecate;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/07/26 11:12
 * @description 打印转码后传输对象
 */
@Deprecated
public class PrintContentDTO {

    private String enterpriseInfoGUID;

    /**
     * 打印的唯一id
     */
    private String printKey;

    /**
     * 打印行集合
     */
    private List<PrintRowDTO> ArrayOfPrintRowE;

    private String StoreGUID;

    private Integer printerTypeCode;

    private Integer onLocal;

    private String ip;

    private String port;

    /**
     * 打印次数
     */
    private Integer printTimes;

    /**
     * 页张大小
     */
    private String paper;

    public static Builder builder(){
        return new Builder(new PrintContentDTO());
    }
    public static class Builder{

        PrintContentDTO printContent;
        public Builder(PrintContentDTO printContent) {
            this.printContent = printContent;
        }
        public Builder enterpriseInfoGUID(String enterpriseInfoGUID) {
            this.printContent.setEnterpriseInfoGUID(enterpriseInfoGUID);
            return this;
        }

        public Builder printKey(String printKey) {
            this.printContent.setPrintKey(printKey);
            return this;
        }

        public Builder arrayOfPrintRowE(List<PrintRowDTO> arrayOfPrintRowE) {
            this.printContent.setArrayOfPrintRowE(arrayOfPrintRowE);
            return this;
        }

        public Builder storeGUID(String storeGUID) {
            this.printContent.setStoreGUID(storeGUID);
            return this;
        }

        public Builder printerTypeCode(Integer printerTypeCode) {
            this.printContent.setPrinterTypeCode(printerTypeCode);
            return this;
        }

        public Builder onLocal(Integer onLocal) {
            this.printContent.setOnLocal(onLocal);
            return this;
        }

        public Builder ip(String ip) {
            this.printContent.setIp(ip);
            return this;
        }

        public Builder port(String port) {
            this.printContent.setPort(port);
            return this;
        }

        public Builder printTimes(Integer printTimes) {
            this.printContent.setPrintTimes(printTimes);
            return this;
        }

        public Builder paper(String paper) {
            this.printContent.setPaper(paper);
            return this;
        }
        public PrintContentDTO build(){
            return this.printContent;
        }
    }

    public String getEnterpriseInfoGUID() {
        return enterpriseInfoGUID;
    }

    public void setEnterpriseInfoGUID(String enterpriseInfoGUID) {
        this.enterpriseInfoGUID = enterpriseInfoGUID;
    }

    public String getPrintKey() {
        return printKey;
    }

    public void setPrintKey(String printKey) {
        this.printKey = printKey;
    }

    public List<PrintRowDTO> getArrayOfPrintRowE() {
        return ArrayOfPrintRowE;
    }

    public void setArrayOfPrintRowE(List<PrintRowDTO> arrayOfPrintRowE) {
        ArrayOfPrintRowE = arrayOfPrintRowE;
    }

    public String getStoreGUID() {
        return StoreGUID;
    }

    public void setStoreGUID(String storeGUID) {
        StoreGUID = storeGUID;
    }

    public Integer getPrinterTypeCode() {
        return printerTypeCode;
    }

    public void setPrinterTypeCode(Integer printerTypeCode) {
        this.printerTypeCode = printerTypeCode;
    }

    public Integer getOnLocal() {
        return onLocal;
    }

    public void setOnLocal(Integer onLocal) {
        this.onLocal = onLocal;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public Integer getPrintTimes() {
        return printTimes;
    }

    public void setPrintTimes(Integer printTimes) {
        this.printTimes = printTimes;
    }

    public String getPaper() {
        return paper;
    }

    public void setPaper(String paper) {
        this.paper = paper;
    }

}
