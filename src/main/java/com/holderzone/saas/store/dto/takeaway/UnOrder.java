package com.holderzone.saas.store.dto.takeaway;

import com.holderzone.saas.store.dto.takeaway.request.FoodListDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class UnOrder implements Serializable {

    private static final long serialVersionUID = 8684397685270796408L;

    /**
     * 订单状态
     * 0：待处理
     * -1：已取消
     * 10：已接单
     * 20：配送中
     * 100：完成
     * -200：已退单
     *
     * @see com.holderzone.saas.store.dto.takeaway.OrderStatus
     */
    private Integer orderStatus;
    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 外卖门店ID（抖音）
     */
    private Long subStoreId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * UnOrder融合的CallbackMsgType
     *
     * @see UnOrderCbMsgType
     */
    private int cbMsgType = -1;

    /**
     * 回复消息类型
     *
     * @see UnOrderReplyMsgType
     */
    private int replyMsgType = -1;

    /**
     * 订单类型, 0=外卖订单  1=微信订单  2=其他订单
     */
    private int orderType = -1;

    /**
     * 订单子类.OrderType2=0：0=美团  1=饿了么  2=百度  3=京东 5=自营 6=通吃岛 ; OrderType2=1：0=扫码订单  1=微信预订单
     */
    private int orderSubType = -1;

    /**
     * 通吃岛专用标识
     */
    private String orderStatusTcd;

    /**
     * 核销码
     */
    private String writeOffCode;

    /**
     * 通吃岛外卖订单类型
     */
    private String orderTypeTcd;

    /**
     * 商家标识
     */
    private String enterpriseGuid;

    /**
     * 门店标识
     */
    private String storeGuid;

    /**
     * 是否打印
     */
    private Boolean print;

    /**
     * 门店名称，如果有
     */
    private String storeName;

    /**
     * 外卖订单ID，数据交换标识
     */
    private String orderId;

    /**
     * 外卖订单流水号，小票打印使用，默认同OrderID(美团对OrderID、OrderSN有区分)
     */
    private String orderViewId;

    /**
     * 日流水
     */
    private String orderDaySn;

    /**
     * 备注
     */
    private String orderRemark;

    /**
     * 是否预订单 0=否 1=是
     */
    private Boolean reserve;

    /**
     * 是否第三方配送 0=否 1=是
     */
    private int isThirdShipping;

    /**
     * 顾客姓名
     */
    private String customerName;

    /**
     * 顾客电话多个电话:存放List<String> 的json字符串
     */
    private String customerPhone;

    /**
     * 顾客多个隐私号：存放List<String> 的json字符串
     */
    private String privacyPhone;

    /**
     * 顾客地址
     */
    private String customerAddress;

    /**
     * 隐私地址
     * 此字段不会包含recipientAddress字段中@#后面的值
     */
    private String recipientAddressDesensitization;

    /**
     * 真实地址
     */
    private String realRecipientAddress;

    /**
     * 用餐人数
     */
    private Integer customerNumber;

    /**
     * 首单用户 0=否 1=是 -1=未知
     */
    private Boolean firstOrder;

    /**
     * 配送地址纬度
     */
    private String shipLatitude;

    /**
     * 配送地址经度
     */
    private String shipLongitude;

    /**
     * 配送员ID
     */
    private String shipperId;

    /**
     * 配送员姓名
     */
    private String shipperName;

    /**
     * 配送员电话
     */
    private String shipperPhone;

    /**
     * 配送时间（同取餐时间）
     */
    private LocalDateTime shipStartTime;

    /**
     * 是否三方托运  0=否  1=是  -1=未知
     */
    private Boolean thirdShipper;

    /**
     * 是否有发票 0=无发票  1=有发票
     */
    private Boolean invoiced;

    /**
     * 发票类型 0=个人 1=企业  -1=未知
     */
    private Integer invoiceType;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 纳税人身份证明
     */
    private String taxpayerId;

    /**
     * 总价,包括：菜品(原价)+餐盒+配送
     */
    private BigDecimal total;

    /**
     * 配送费
     */
    private BigDecimal shipTotal;

    /**
     * 菜品消费合计(不含餐盒费)
     */
    private BigDecimal itemTotal;

    /**
     * 餐盒费
     */
    private BigDecimal packageTotal;

    /**
     * 餐具
     */
    private int dinnersNumber;

    /**
     * 折扣合计 EnterpriseDiscount+PlatformDiscount+OtherDiscount
     */
    private BigDecimal discountTotal;

    /**
     * 商家承担的折扣部分
     */
    private BigDecimal enterpriseDiscount;

    /**
     * 外卖平台承担的折扣部分
     */
    private BigDecimal platformDiscount;

    /**
     * 其他折扣
     */
    private BigDecimal otherDiscount;

    /**
     * 服务费率(平台抽成比例)0.15=15%
     */
    private BigDecimal serviceFeeRate;

    /**
     * 服务费(平台抽成费用)
     */
    private BigDecimal serviceFee;

    /**
     * 顾客实际支付金额
     */
    private BigDecimal customerActualPay;

    /**
     * 是否是部分退单
     */
    private Boolean part;

    /**
     * 顾客退款金额
     */
    private BigDecimal customerRefund;

    /**
     * 顾客退款菜品
     */
    private String customerRefundItem;

    /**
     * 门店收款,顾客实际支付的金额扣除服务费(平台抽成)考虑扣除配送费?
     */
    private BigDecimal shopTotal;

    /**
     * 是否在线支付  1=在线支付  0=线下付款
     */
    private Boolean onlinePay;


    /**
     * 订单创建（下单）时间戳
     */
    private LocalDateTime createTime;

    /**
     * 预订单生效/激活时间,非预订单，同下单时间
     */
    private LocalDateTime activeTime;

    /**
     * 配送时间要求  0=无时间要求  >0=具体要求的配送时间
     */
    private LocalDateTime estimateDeliveredTime;

    /**
     * 接单时间
     */
    private LocalDateTime acceptTime;

    /**
     * 配送时间（同取餐时间）
     */
    private LocalDateTime deliveryTime;

    /**
     * 实际送达时间
     */
    private LocalDateTime deliveredTime;

    /**
     * 取消订单请求时间
     */
    private LocalDateTime cancelReqTime;

    /**
     * 取消订单请求原因
     */
    private String cancelReqReason;

    /**
     * 取消订单请求回复消息体
     */
    private String cancelReplyMessage;

    /**
     * 退单请求时间
     */
    private LocalDateTime refundReqTime;

    /**
     * 退单请求原因
     */
    private String refundReqReason;

    /**
     * 退单请求回复消息体
     */
    private String refundReplyMessage;

    /**
     * 订单取消时间
     */
    private LocalDateTime cancelTime;

    /**
     * 取消订单原因
     */
    private String cancelReason;

    /**
     * 取消订单原因 code
     * (抖音需传)
     */
    private Long cancelReasonCode;

    /**
     * 取消订单角色名
     */
    private String cancelRoleName;

    /**
     * 订单完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 订单商品明细
     */
    private List<UnItem> arrayOfUnItem;

    /**
     * 订单反馈信息
     */
    private List<UnRemind> arrayOfUnRemind;

    /**
     * 订单折扣明细
     */
    private List<UnDiscount> arrayOfUnDiscount;

    /**
     * 退菜明细
     */
    private List<UnRefundItem> arrayOfUnRefundItem;

    /**
     * 自营外卖配送方式
     */
    private int distributionType;


    /**
     * erp新订单 回复 2010 拒单， 2011 确认接单
     * erp退单 回复 2030 不同意退单， 2031 同意退单
     * <p>
     * 已过时，使用replyMsgType代理，需要Android前端同步修改
     *
     * @see com.holderzone.saas.store.dto.takeaway.MsgType
     */
    @Deprecated
    private String msgType;

    /**
     * 外卖平台原始的状态值
     */
    @Deprecated
    private String originalOrderStatus;

    /**
     * 商家标识
     */
    @Deprecated
    private String enterpriseId;

    /**
     * 门店标识
     */
    @Deprecated
    private String storeId;

    /**
     * 不同意取消订单/退单原因
     */
    @Deprecated
    private String disagreeReason;

    /**
     * 订单状态最后更新时间
     */
    @Deprecated
    private LocalDateTime updateTime;

    /**
     * 饿了么token标识商户id
     */
    @Deprecated
    private String eleUserId;


    private List<FoodListDTO> foodLists;

    /**
     * fixme 提取到RetryDTO
     * 一城飞客发起配送专用
     */
    private Integer curRetryTime;

    private Integer maxRetryTimes;

    private Integer delayTimeLevel;

    private Boolean isEnableBackOff;

    /**
     * 物流单号
     */
    private String thirdCarrierId;

    /**
     * 订单入库guid
     */
    private String orderGuid;

    /**
     * 0普通订单1美团拼好饭订单
     */
    private int businessType;

    /**
     * 满赠说明
     */
    private String fullGiftRemark;

    /**
     * 京东外卖授权到品牌 需要传递品牌guid
     */
    private String brandGuid;

    /**
     * 京东外卖操作人
     */
    private String operateStaffName;

}
