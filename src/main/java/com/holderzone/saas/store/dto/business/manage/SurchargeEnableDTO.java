package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class SurchargeEnableDTO implements Serializable {

    private static final long serialVersionUID = -2630904057455048727L;

    @NotBlank(message = "附加费Guid不得为空")
    @ApiModelProperty(value = "附加费Guid", required = true)
    private String surchargeGuid;

    @NotNull(message = "是否启用不得为空")
    @ApiModelProperty(value = "是否启用：0=停用，1=启用。", required = true)
    private Boolean isEnable;
}
