package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@ApiModel("会员卡或者优惠券选中")
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Data
public class UserMemberSessionDTO {

	@ApiModelProperty("门店guid")
	private String storeGuid;

	@ApiModelProperty("用户id")
	private String openId;

	@ApiModelProperty("用户id")
	private String userGuid;

	@ApiModelProperty("是否登录")
	private Boolean isLogin = false;

	@ApiModelProperty("昵称")
	private String nickName;

	@ApiModelProperty(value = "会员guid")
	private String memberInfoGuid;

	@ApiModelProperty("卡的guid")
	private String cardGuid;

	@ApiModelProperty("会员持卡GUID,-1:不使用，0：请选择")
	private String memberInfoCardGuid;

	@ApiModelProperty("卡体系GUID")
	private String systemManagementGuid;

	@ApiModelProperty(value = "是否计算积分1：计算，0：不计算")
	private Integer memberIntegral;

	@ApiModelProperty(value = "是否支持会员价")
	private Boolean whetherSupportMemberPrice = false;

	@ApiModelProperty(value = "优惠券code,-1：不使用，0：请选择，")
	private String volumeCode;

	@ApiModelProperty(value = "true:已验券，false：未验券")
	private Boolean volumeVerify=false;

	@ApiModelProperty(value = "应付金额")
	private BigDecimal payAmount;

	@ApiModelProperty(value = "非会员金额")
	private BigDecimal unMemberFee;

	/***
	 * 运营主体GUID
	 */
	@ApiModelProperty(value = "运营主体GUID")
	private String operSubjectGuid;
	//private Map<String,>
}
