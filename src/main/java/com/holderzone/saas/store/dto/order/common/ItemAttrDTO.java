package com.holderzone.saas.store.dto.order.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SkuPropertyDTO
 * @date 2018/09/14 15:50
 * @description 菜品属性
 * @program holder-saas-store-order
 */
@Data
public class ItemAttrDTO implements Serializable {

    private static final long serialVersionUID = 7223455979658792272L;

    @ApiModelProperty(value = "商品属性guid")
    private String guid;

    @ApiModelProperty(value = "属性guid", required = true)
    private String attrGuid;

    @ApiModelProperty(value = "属性名称", required = true)
    private String attrName;

    @ApiModelProperty(value = "属性组名称", required = true)
    private String attrGroupName;

    @ApiModelProperty(value = "属性组guid", required = true)
    private String attrGroupGuid;

    @ApiModelProperty(value = "属性价格", required = true)
    private BigDecimal attrPrice;

    @ApiModelProperty(value = "属性数量", required = true)
    private Integer num;

}
