package com.holderzone.saas.store.dto.takeaway;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class EleCbUnbindDetail implements Serializable {

    private static final long serialVersionUID = 8790066845208350662L;

    /**
     * 店铺ID
     */
    private String shopId;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 解除授权时间
     */
    private LocalDateTime relieveOAuthTime;
}
