package com.holderzone.saas.store.dto.print.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/6/9
 * @since 1.8
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "预付金单")
public class PrintReservePayDTO extends PrintDTO {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private String storeName;

    @ApiModelProperty("预定时间")
    @NotNull(message = "预定时间不能为空")
    private String confirmTime;

    @ApiModelProperty(value = "收银员")
    private String checkoutStaffs;

    @ApiModelProperty(value = "就餐时间")
    private String reserveStartTime;

    @ApiModelProperty(value = "就餐人数")
    private Integer number;

    @ApiModelProperty(value = "预订桌台")
    private String tableName;

    @ApiModelProperty(value = "预订人")
    private String name;

    @ApiModelProperty("0女 1男")
    private Byte gender;

    @ApiModelProperty(value = "预留手机号")
    private String phone;

    @ApiModelProperty("预付金额")
    private BigDecimal reserveAmount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("支付方式")
    private String paymentTypeName;

    @ApiModelProperty("支付时间")
    private String paymentTime;
}
