package com.holderzone.saas.store.dto.takeaway.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 商户后台/数据报表/订单统计 列表查询结果DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
@Data
public class BusinessTakeoutOrderDetailRespDTO {

    @ApiModelProperty(value = "订单表唯一标识")
    private String guid;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态")
    private String orderState;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acceptTime;

    @ApiModelProperty(value = "订单来源")
    private String orderSource;

    @ApiModelProperty(value = "送达时间")
    private String deliveryTime;

    @ApiModelProperty(value = "下单时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal total;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal customerActualPay;

    @ApiModelProperty(value = "姓名")
    private String customerName;

    @ApiModelProperty(value = "联系电话")
    private String phoneNo;

    @ApiModelProperty(value = "送餐地址")
    private String customerAddress;

    @ApiModelProperty(value = "备注")
    private String orderRemark;

    @ApiModelProperty(value = "商品信息")
    private List<BusinessTakeoutOrderItemRespDTO> itemInfoList;

    @ApiModelProperty(value = "退款金额")
    private String customerRefund;

    @ApiModelProperty(value = "退款菜品")
    private String customerRefundItem;

    @ApiModelProperty(value = "退款申请时间")
    private LocalDateTime refundReqTime;

    @ApiModelProperty(value = "退款原因")
    private String refundReqReason;

    @ApiModelProperty(value = "退款处理时间")
    private LocalDateTime refundReplyTime;

    @ApiModelProperty(value = "退款操作员")
    private String refundReplyStaffName;

    @ApiModelProperty(value = "是否有退款信息")
    private Boolean refundSuccess;

    @ApiModelProperty(value = "退款结果描述：0=未(部分)退款，1=已(部分)退款")
    private String refundStateDesc;

}

