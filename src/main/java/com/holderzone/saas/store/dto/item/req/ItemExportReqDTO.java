package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishSynRespDTO
 * @date 2019/01/03 下午2:10
 * @description //商品详情返回实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "商品导出条件")
public class ItemExportReqDTO{

    @NotBlank(message = "品牌GUID不能为空！")
    @ApiModelProperty("品牌GUID")
    private String brandGuid;

    @NotBlank(message = "门店GUID不能为空！")
    @ApiModelProperty("门店GUID")
    private String storeGuid;

    @ApiModelProperty("分类GUID")
    private String typeGuid;
}
