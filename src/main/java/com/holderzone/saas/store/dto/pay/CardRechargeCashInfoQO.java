package com.holderzone.saas.store.dto.pay;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 储值单充值信息
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class CardRechargeCashInfoQO extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "订单号", required = true)
    private String orderNumber;
}
