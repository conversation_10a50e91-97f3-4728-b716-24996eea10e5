package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandOverRespDTO
 * @date 2019/10/29 15:07
 * @description
 * @program holder-saas-store
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "交接班-当班详情")
public class HandOverRespDTO {
    @ApiModelProperty(value = "当班详情-数据统计")
    RetailBusinessDataRespDTO businessDataRespDTO;

    @ApiModelProperty(value = "当班详情-支付方式统计")
    List<PaymentReportRespDTO> paymentReportRespDTOS;
}
