package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("微信：价格方案变动请求信息")
public class PricePlanChangeRequestDTO {

    /**
     * 客户端菜谱模式下，菜品规格guid
     */
    @ApiModelProperty(value = "客户端菜谱模式下，菜品规格guid")
    private List<String> skuGuidList;

    @ApiModelProperty("菜谱GUID")
    private String pricePlanGuid;

    @ApiModelProperty("门店GUID，可以不用传")
    private String storeGuid;
}
