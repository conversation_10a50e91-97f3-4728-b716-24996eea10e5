package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.saas.store.dto.weixin.WxStoreTradeOrderDetailsDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreTradeDetailsGroupDTO
 * @date 2019/5/27
 */
@ApiModel(value = "订单以桌台分组")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class WxStoreTradeDetailsGroupDTO {

	@ApiModelProperty(value = "桌台id")
	private String tableGuid;

	@ApiModelProperty(value = "桌台编码")
	private String tableCode;

	@ApiModelProperty(value = "区域")
	private String areaName;

	@ApiModelProperty(value = "0：表示主桌，1：表示次桌")
	private Integer mainTable=1;

	private List<WxStoreTradeOrderDetailsDTO> wxStoreTradeOrderDetailsDTOS;
}
