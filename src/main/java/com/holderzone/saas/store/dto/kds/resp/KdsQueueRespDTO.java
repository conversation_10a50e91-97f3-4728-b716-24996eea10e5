package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class KdsQueueRespDTO implements Serializable {

    private static final long serialVersionUID = -1158994112223699540L;

    @ApiModelProperty(value = "等待队列")
    private List<QueueItemDTO> waitingQueue;

    @ApiModelProperty(value = "取餐队列")
    private List<QueueItemDTO> takeMealQueue;
}
