package com.holderzone.saas.store.dto.item.req.price;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 编辑分类
 * @date 2024/3/26
 */
@Data
@ApiModel(value = "编辑分类model")
public class PlanPriceEditHsiTypeDTO {

    @ApiModelProperty(value = "推送类型：1-立即推送，2-按时间推送")
    private Integer pushType;

    @ApiModelProperty(value = "推送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pushDate;

    @ApiModelProperty(value = "编辑分类信息")
    private List<PlanPriceEditHsiTypeItemDTO> updateTypeItems;

}
