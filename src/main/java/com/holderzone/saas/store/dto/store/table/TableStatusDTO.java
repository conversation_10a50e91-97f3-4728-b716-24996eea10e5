package com.holderzone.saas.store.dto.store.table;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 验证门店信息内容
 * <AUTHOR>
 * @version 1.0
 * @className TableDO
 * @date 2018/07/24 上午9:55
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TableStatusDTO implements Serializable {

    @ApiModelProperty("开台时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime openTableTime;

    @ApiModelProperty("桌台状态 -1=暂停使用;0=空闲;1=占用;2=预定;3=待清台")
    private Integer status;

    @ApiModelProperty("该桌台该扎账时间")
    private LocalDate tableBindUpAccounts;

}
