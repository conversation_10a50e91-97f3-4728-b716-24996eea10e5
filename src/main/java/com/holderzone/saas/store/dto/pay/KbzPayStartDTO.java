package com.holderzone.saas.store.dto.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description kbz发起支付实体
 * @date 2021/8/11 18:28
 */
@ApiModel("kbz发起支付实体")
@Data
@Accessors(chain = true)
public class KbzPayStartDTO {

    @ApiModelProperty("拉起支付流程的预支付交易ID")
    private String prePayId;

    @ApiModelProperty("订单详情。订单信息拼接成的字符串")
    private String orderInfo;

    @ApiModelProperty("orderInfo 签名")
    private String sign;

}
