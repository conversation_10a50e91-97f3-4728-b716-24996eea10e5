package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@ApiModel("购物车")
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Data
@Builder
public class ShopCartRespDTO {

	private List<ItemInfoDTO> itemInfoDTOS;

	@ApiModelProperty(value = "所在区域",hidden = true)
	private String areaName;

	@ApiModelProperty(value = "桌台号",hidden = true)
	private String tableCode;

	@ApiModelProperty(value = "是否是会员")
	private Boolean isLogin=false;

	@ApiModelProperty(value = "原价")
	private BigDecimal originPrice;

	@ApiModelProperty(value = "是否有优惠价")
	private Boolean enablePreferentialPrice=false;

	@ApiModelProperty(value = "优惠价")
	private BigDecimal preferentialPrice;

	@ApiModelProperty(value = "商品种数")
	private Integer speciesCount;

	@ApiModelProperty(value = "商品件数")
	private BigDecimal pieceCount;

	@ApiModelProperty(value = "折扣价")
	private BigDecimal discountPrice;

}
