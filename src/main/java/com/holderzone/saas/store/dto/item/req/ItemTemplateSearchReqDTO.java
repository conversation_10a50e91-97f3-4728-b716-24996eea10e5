package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateSearchReqDTO
 * @date 2019/05/30 10:10
 * @description //TODO 条件查询门店销售模板列表
 * @program holder-saas-aggregation-app
 */
@Data
@ApiModel(value = "查询模板列表")
public class ItemTemplateSearchReqDTO extends BasePageDTO {

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "激活状态 1：激活 2：冻结")
    private Integer itActivated;

    @ApiModelProperty(value = "执行开始时间 24小时制 yyyy-mm-dd")
    private String startTime;

    @ApiModelProperty(value = "执行结束时间 24小时制 yyyy-mm-dd")
    private String endTime;

    @ApiModelProperty(value = "当前状态 1：已结束 2：未开始  3：进行中")
    private Integer status;

    @ApiModelProperty(value = "搜索模板名称关键字")
    private String keywords;


}
