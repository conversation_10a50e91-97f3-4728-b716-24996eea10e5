package com.holderzone.saas.store.dto.pay.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RefundState
 * @date 2019/03/14 10:58
 * @description 退款请求状态
 * @program holder-saas-store-dto
 */
public enum AggRefundStateEnum {

    REFUNDING("0", "待退款"),

    REFUND_SUCCESS("1", "退款成功"),

    REFUND_FAILURE("2", "退款失败"),

    REFUND_PROCESSED("3", "退款中");

    private String state;

    private String desc;

    AggRefundStateEnum(String state, String desc) {
        this.state = state;
        this.desc = desc;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
