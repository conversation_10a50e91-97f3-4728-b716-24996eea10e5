package com.holderzone.saas.store.dto.print.type;

import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/26
 * @description 模版门店查询
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "模版门店查询", description = "模版门店查询")
public class TemplateStoreQO implements Serializable {

    private static final long serialVersionUID = 9183811886008533748L;

    @NotBlank(message = "品牌guid不能为空")
    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "查询关键字")
    private String keywords;

    @ApiModelProperty(value = "模版guid")
    private String templateGuid;

    /**
     * @see InvoiceTypeEnum
     */
    @ApiModelProperty(value = "应用单据-InvoiceTypeEnum")
    @NotEmpty(message = "请选择应用单据")
    private List<String> invoiceType;
}
