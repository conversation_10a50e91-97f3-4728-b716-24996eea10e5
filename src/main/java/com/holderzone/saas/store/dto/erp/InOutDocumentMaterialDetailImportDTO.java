package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/26
 * @since 1.8
 */
@Data
@ApiModel("出入库单物料详情导入实体")
public class InOutDocumentMaterialDetailImportDTO {

    @ApiModelProperty("编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("入库数量（1~999999）")
    private String count;

    @ApiModelProperty("入库单位")
    private String unitName;

    @ApiModelProperty("入库单价（0~999999.99）")
    private String unitPrice;
}
