package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/30 下午 13:54
 * @description
 */
@ApiModel("仓库物料信息的查询实体")
public class InOutDocumentDetailQueryDTO {

    @ApiModelProperty("仓库Guid")
    private String warehouseGuid;

    @ApiModelProperty("门店Guid")
    private String storeGuid;

    @ApiModelProperty("供应商Guid")
    private String supplierGuid;

    @ApiModelProperty("物料Guid的集合")
    private List<String> materialGuidList;

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }

    public String getSupplierGuid() {
        return supplierGuid;
    }

    public void setSupplierGuid(String supplierGuid) {
        this.supplierGuid = supplierGuid;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public List<String> getMaterialGuidList() {
        return materialGuidList;
    }

    public void setMaterialGuidList(List<String> materialGuidList) {
        this.materialGuidList = materialGuidList;
    }
}
