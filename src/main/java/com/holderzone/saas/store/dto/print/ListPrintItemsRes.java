package com.holderzone.saas.store.dto.print;


import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 查询指定订单已绑定标签商品返回
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ListPrintItemsRes {
    /**
     * 订单Guid
     */
    private String orderGuid;


    /**
     * 可打印商品集合
     */
    private List<PrintItem> printItemList;

    public ListPrintItemsRes(DineinOrderDetailRespDTO orderDetail, List<DineInItemDTO> orderItemList) {
        this.orderGuid=orderDetail.getGuid();

        List<PrintItem> printItemList = new ArrayList<>();
        for (DineInItemDTO dineInItemDTO : orderItemList) {
            PrintItem printItem = new PrintItem(
                    dineInItemDTO.getGuid(),
                    dineInItemDTO.getItemGuid(),
                    dineInItemDTO.getItemName(),
                    dineInItemDTO.getItemType().toString());

            printItemList.add(printItem);
        }

        this.printItemList= printItemList;
    }


    /**
     * 打印商品信息
     */
   @Data
   @AllArgsConstructor
   @NoArgsConstructor
    public static class PrintItem {
        /**
         * 订单商品guid
         */
        private String guid;
        /**
         * 商品Guid
         */
        private String itemGuid;
        /**
         * 商品名称
         */
        private String itemName;
        /**
         * 商品类型: 1-套餐主项 2-规格 3-称重  4-单品  5-团餐主项
         */
        private String itemType;
    }


}

