package com.holderzone.saas.store.dto.map;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MapCommonRespDTO
 * @date 2019/05/18 17:02
 * @description 高德地图公共响应属性DTO
 * @program holder-saas-store
 */
@ApiModel("高德地图公共响应属性DTO")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MapCommonRespDTO {

    /**
     * 状态码：0请求失败，1请求成功
     */
    private Integer status;

    /**
     * 响应描述
     * infoCode == 10000是描述为OK
     */
    private String info;

    /**
     * 响应code（10000为成功）
     */
    @JsonProperty(value = "infocode")
    private Integer infoCode;



}
