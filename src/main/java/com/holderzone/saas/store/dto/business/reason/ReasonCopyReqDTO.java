package com.holderzone.saas.store.dto.business.reason;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 原因copy请求入参
 * @date 2020/12/1 14:58
 */
@Data
@ApiModel("原因copy请求入参")
public class ReasonCopyReqDTO {

    @ApiModelProperty(value = "原因实体列表")
    private List<ReasonDTO> reasonDTOList;

    @ApiModelProperty(value = "门店Guid列表")
    private List<String> storeGuidList;
}
