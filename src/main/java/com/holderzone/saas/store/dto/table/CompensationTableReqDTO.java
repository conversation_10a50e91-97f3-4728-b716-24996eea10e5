package com.holderzone.saas.store.dto.table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/23
 * @description 桌台状态补偿
 */
@Data
@ApiModel(value = "桌台状态补偿")
@EqualsAndHashCode(callSuper = false)
public class CompensationTableReqDTO implements Serializable {

    private static final long serialVersionUID = 4861226891447608414L;

    @ApiModelProperty("桌台状态补偿")
    private List<CompensationTableDTO> list;

    @ApiModelProperty("企业guid")
    private String enterpriseGuid;

    public CompensationTableReqDTO() {
    }

    public CompensationTableReqDTO(List<CompensationTableDTO> list, String enterpriseGuid) {
        this.list = list;
        this.enterpriseGuid = enterpriseGuid;
    }
}
