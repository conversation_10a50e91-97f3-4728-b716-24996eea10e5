package com.holderzone.saas.store.dto.print.format.compress;

import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;

public final class CompressUtils {

    private static final int ENABLE_BIT = 1;
    private static final int ENABLE_MASK = 0b1;

    private static final int UNDERLINE_BIT = 1;
    private static final int UNDERLINE_MASK = 0b1;

    private static final int BOLD_BIT = 1;
    private static final int BOLD_MASK = 0b1;

    private static final int ALIGN_BIT = 2;
    private static final int ALIGN_MASK = 0b11;

    private static final int SIZE_BIT = 2;
    private static final int SIZE_MASK = 0b11;

    /**
     * 0x00_00_0_0_0
     * 从低位开始
     * 是否启用：0=不启用，1=启用
     * 是否加下划线：0=不加下划线 ，1=加下划线
     * 是否加粗：0=不加粗，1=加粗
     * 对齐方式：00=左对齐，01=居中对齐，10=右对齐，11=两端对齐
     * 字体倍数：00=一倍字体，01=二倍字体，10=三倍字体，11=四倍字体
     *
     * @param formatMetadata
     * @return
     */
    public static int format2Binary(FormatMetadata formatMetadata) {
        int size = formatMetadata.getSize() - 1;
        int align = formatMetadata.getAlign();
        int bold = formatMetadata.isBold() ? 1 : 0;
        int underline = formatMetadata.isUnderline() ? 1 : 0;
        int enable = formatMetadata.isEnable() ? 1 : 0;
        return (((((((size << ALIGN_BIT) + align) << BOLD_BIT) + bold) << UNDERLINE_BIT) + underline) << ENABLE_BIT) + enable;
    }

    public static FormatMetadata binary2Format(int binary) {
        FormatMetadata formatMetadata = new FormatMetadata();
        formatMetadata.setEnable((binary & ENABLE_MASK) == 1);
        binary >>= ENABLE_BIT;
        formatMetadata.setUnderline((binary & UNDERLINE_MASK) == 1);
        binary >>= UNDERLINE_BIT;
        formatMetadata.setBold((binary & BOLD_MASK) == 1);
        binary >>= BOLD_BIT;
        formatMetadata.setAlign(binary & ALIGN_MASK);
        binary >>= ALIGN_BIT;
        formatMetadata.setSize((binary & SIZE_MASK) + 1);
        return formatMetadata;
    }
}
