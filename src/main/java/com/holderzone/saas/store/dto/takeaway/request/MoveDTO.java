package com.holderzone.saas.store.dto.takeaway.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 数据迁移实体
 * @date 2022/5/25 19:33
 * @className: MoveDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "数据迁移实体")
public class MoveDTO implements Serializable {

    private static final long serialVersionUID = -5497888163531555565L;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty(value = "执行数量")
    private Integer executeCount;

    @ApiModelProperty("时间筛选-开始日期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startDateTime;

    @ApiModelProperty("时间筛选-结束日期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endDateTime;
}
