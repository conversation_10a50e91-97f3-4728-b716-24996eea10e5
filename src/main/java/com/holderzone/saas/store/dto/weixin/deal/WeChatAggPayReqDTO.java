package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("购物车展示")
public class WeChatAggPayReqDTO{

	@ApiModelProperty(value = "订单id",required = true)
	private String orderGuid;

	@ApiModelProperty(value = "是否最后一次支付", required = true)
	private Boolean last;

	@ApiModelProperty(value = "0：PC服务端,1：PC平板,2：小店通,3：一体机,4：POS机,5：云平板,6：点菜宝(M1)," +
			"7：PV1(带刷卡的点菜宝),9：厨房显示系统,10: 取餐屏,12：微信 15通吃岛"
			)
	private Integer deviceType;

	/**
	 * 支付功能ID（是）
	 * @see com.holderzone.saas.store.dto.trade.constant.PayPowerId
	 */
	@ApiModelProperty(value = "详细见文档")
	private String payPowerId;

}
