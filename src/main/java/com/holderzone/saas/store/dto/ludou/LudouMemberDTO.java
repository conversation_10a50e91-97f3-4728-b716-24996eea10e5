package com.holderzone.saas.store.dto.ludou;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 麓豆会员信息
 */
@Data
public class LudouMemberDTO implements Serializable {

    private static final long serialVersionUID = -2806992746246547783L;

    /**
     * 麓豆会员id
     */
    private String userId;

    /**
     * 麓豆会员名称
     */
    private String nickName;

    /**
     * 麓豆会员手机号
     */
    private String phone;

    /**
     * 麓豆会员余额
     */
    private BigDecimal usableAmount;
}
