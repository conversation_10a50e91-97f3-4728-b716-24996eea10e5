package com.holderzone.saas.store.dto.ludou;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 麓豆会员退款 请求
 */
@Data
@Builder
public class LudouMemberRefundDTO implements Serializable {

    private static final long serialVersionUID = -2806992746246547783L;

    /**
     * 麓豆退款金额（目前仅支持全退，只要 ludouAmount > 0，则执行全退）
     */
    private BigDecimal ludouAmount;

    /**
     * 总退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 交易编号 （麓豆平台的订单id）
     */
    private String tradeNo;

    /**
     * 外部交易编号 (order表的guid)
     */
    private String outTradeNo;
}
