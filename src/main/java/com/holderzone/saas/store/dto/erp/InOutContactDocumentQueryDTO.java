package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2019/05/10 上午 9:45
 * @description
 */
@ApiModel("查询关联单据查询实体")
public class InOutContactDocumentQueryDTO {

    @ApiModelProperty("供应商guid")
    private String supplierGuid;

    @ApiModelProperty("仓库guid")
    private String warehouseGuid;

    @ApiModelProperty("单据guid")
    private String documentGuid;

    public String getSupplierGuid() {
        return supplierGuid;
    }

    public void setSupplierGuid(String supplierGuid) {
        this.supplierGuid = supplierGuid;
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }

    public String getDocumentGuid() {
        return documentGuid;
    }

    public void setDocumentGuid(String documentGuid) {
        this.documentGuid = documentGuid;
    }
}
