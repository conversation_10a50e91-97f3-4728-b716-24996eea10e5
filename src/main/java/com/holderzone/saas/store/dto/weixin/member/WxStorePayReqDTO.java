package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Api("微信端支付方式")
public class WxStorePayReqDTO {

	@ApiModelProperty(value = "企业guid",required = true)
	private String enterpriseGuid;

	@ApiModelProperty(value = "品牌guid",required = true)
	private String brandGuid;

	@ApiModelProperty(value = "门店guid",required = true)
	private String storeGuid;

	@ApiModelProperty(value = "桌台guid",required = true)
	private String tableGuid;

	@ApiModelProperty(value = "用户guid",required = true)
	private String openId;


}
