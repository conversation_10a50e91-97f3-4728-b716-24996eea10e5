package com.holderzone.saas.store.dto.journaling.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className JournalStoreAppBaseReqDTO
 * @date 2019/10/25 13:48
 * @description 门店报表请求基础参数
 * @program holder-saas-store
 */
@Data
public class JournalStoreAppBaseReqDTO {

    private static final long serialVersionUID = -165725793274767946L;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
//    @NotNull(message = "开始时间不能为空")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
//    @NotNull(message = "结束时间不能为空")
    private LocalDate endDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "开始营业日期时间 yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "开始营业日期时间不能为空")
    protected LocalDateTime businessStartDateTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "营业结束日期时间 yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "营业结束日期时间不能为空")
    protected LocalDateTime businessEndDateTime;

    @ApiModelProperty(value = "交接班员工guid")
    private String createStaffGuid;

    @ApiModelProperty(value = "交接班查询开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime staffTime;

    @ApiModelProperty(value = "是否是交接班查询")
    private Boolean isHandOver = Boolean.FALSE;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "金额排序，0/不排序， 1/从小到大排序， 2/从大到小排序")
    private Integer amountSort;

    @ApiModelProperty(value = "金额排序，0/不排序， 1/从小到大排序， 2/从大到小排序")
    private Integer countSort;

    /*public void setStartDate(LocalDate startDate) {
        this.startDate = Optional.ofNullable(startDate).orElse(LocalDate.now());
    }

    public void setEndDate() {
        this.endDate = Optional.ofNullable(endDate).orElse(LocalDate.now());
    }*/
}
