package com.holderzone.saas.store.dto.item.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineItemWebRespDTO
 * @date 2019/01/11 上午11:30
 * @description //web端商品列表获取实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class ItemWebRespDTO implements Serializable {

    private static final long serialVersionUID = 4475958094205222757L;

    @ApiModelProperty(value = "商品GUID")
    private String itemGuid;
    /**
     * 父实体GUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的实体，则该字段为品牌库对应的实体GUID。
     */
    @ApiModelProperty(value = "父实体GUID")
    protected String parentGuid;
    @ApiModelProperty(value = "商品名称")
    private String name;
    @ApiModelProperty(value = "商品编号")
    private String code;
    @ApiModelProperty(value = "商品分类guid")
    private String typeGuid;
    @ApiModelProperty(value = "商品分类名称")
    private String typeName;

    @ApiModelProperty(value = "商品图片地址,列表小图")
    private String pictureUrl = StringUtils.EMPTY;

    /**
     * 图片路径数组json,这里对应新一期加入的“列表大图”
     */
    @ApiModelProperty(value = "商品图片地址，列表大图")
    private String bigPictureUrl = StringUtils.EMPTY;

    /**
     * 图片路径数组json,这里对应新一期加入的“详情大图”
     */
    @ApiModelProperty(value = "商品图片地址，详情大图")
    private String detailBigPictureUrl = StringUtils.EMPTY;

    @ApiModelProperty(value = "商品最低价")
    private BigDecimal lowestPrice;

    @ApiModelProperty(value = "商品规格上架情况：0：否,1:是")
    private Integer isRack;

    @ApiModelProperty(value = "是否上架一体机（0：否，1：是）")
    private Integer isJoinAio;

    @ApiModelProperty(value = "是否上架POS机（0：否，1：是）")
    private Integer isJoinPos;

    @ApiModelProperty(value = "是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;

    @ApiModelProperty(value = "商品类型：（0：门店自己创建的商品，1：品牌自己创建的商品,2:被推送过来的商品）")
    private Integer itemFrom;
    @ApiModelProperty(value = "商品类型：1.套餐（不称重，无规格），2.规格商品（单商品，不称重），3.称重商品（单商品，称重），4.单品。")
    private Integer itemType;
    @ApiModelProperty(value = "是否参与推荐：0：否，1：是")
    private Integer isNew;
    @ApiModelProperty(value = "是否热销：0：否,1:是")
    private Integer isBestseller;
    @ApiModelProperty(value = "是否推荐：0：否，1：是")
    private Integer isRecommend;
    @ApiModelProperty(value = "是否是招牌：0：否,1:是")
    private Integer isSign;
    @ApiModelProperty(value = "是否有加入整单折扣的规格(0：否，1：是)")
    private Integer isWholeDiscount;
    @ApiModelProperty(value = "是否有参与会员折扣的规格（0：否，1：是）")
    private Integer isMemberDiscount;
    @ApiModelProperty(value = "排序")
    private Integer sort;
    @ApiModelProperty(value = "上架门店数")
    private Integer rackStoreNum;
    @ApiModelProperty(value = "是否关联套餐（0：否，1：是）")
    private Integer isWithPkg;
    @ApiModelProperty(value = "可能为空集合，标签集合,举例：[{\"id\":\"isBestseller\",\"name\":\"热销\"},{\"id\":\"isNew\",\"name\":\"新品\"},{\"id\":\"isSign\",\"name\":\"招牌\"}]")
    private List<TagRespDTO> tagList;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "企业guid（冗余小程序端字段）")
    private String enterpriseGuid;

    @ApiModelProperty(value = "审核状态（冗余小程序端字段）")
    private Integer auditStatus;

    @ApiModelProperty(value = "图文详情（冗余小程序端字段）")
    private String remarkDetail;

    @ApiModelProperty(value = "视频url json数组（冗余小程序端字段）")
    private String videoUrls;

    @ApiModelProperty(value = "好评次数（冗余小程序端字段）")
    private Integer upCount;

    @ApiModelProperty(value = "差评次数（冗余小程序端字段）")
    private Integer downCount;

    @ApiModelProperty(value = "是否参与小程序商城（冗余小程序端字段）")
    private Integer isJoinMiniAppMall;

    @ApiModelProperty(value = "是否参与小程序外卖（冗余小程序端字段）")
    private Integer isJoinMiniAppTakeaway;

    @ApiModelProperty(value = " 是否支持堂食（冗余小程序端字段）")
    private Integer isJoinStore;
    @ApiModelProperty(value = "规格列表")
    private List<SkuRespDTO> skuList;
    @ApiModelProperty(value = "属性组列表，可能为空集合，一级属性集合")
    private List<AttrGroupWebRespDTO> attrGroupList;

    /**
     * 方案GUID
     */
    private String planGuid;

    /**
     * 方案销售价格
     */
    private BigDecimal salePrice;

    /**
     * sku 最小售价
     */
    private BigDecimal minSalePrice;

    /**
     * sku 最大售价
     */
    private BigDecimal maxSalePrice;

    /**
     * 方案会员价格
     */
    private BigDecimal memberPrice;

    /***
     * 是否售完下架 0:上架 1：售完下架 2：立即下架（直接删除）
     */
    @ApiModelProperty(value = "是否售完下架 0:已上架 1：售完下架 2：立即下架（直接删除） 3：即将下架 4：已下架（售完商品的下架） ")
    private Integer isSoldOut;

    /**
     * 商品描述
     */
    @ApiModelProperty(value = "商品描述")
    private String description;

    /**
     * 菜谱方案商品名称
     */
    @Size(min = 1, max = 40, message = "请输入1-40个字符")
    @ApiModelProperty(value = "菜谱方案商品名称")
    private String planItemName;

    /**
     * 菜品规格GUID
     */
    @ApiModelProperty(value = "菜品规格GUID")
    private String skuGuid;

    /**
     * 菜谱方案商品guid
     */
    @ApiModelProperty(value = "菜谱方案商品guid")
    private String planItemGuid;

    /**
     * 是否最新编辑,前端就不驼峰,我只能适配
     */
    @ApiModelProperty(value = "是否最新编辑")
    private Boolean newupdateflag;

    /**
     * 英文简述
     */
    @ApiModelProperty(value = "英文简述")
    private String englishBrief;

    /**
     * 英文配料描述
     */
    @ApiModelProperty(value = "英文配料描述")
    private String englishIngredientsDesc;

    /**
     * 是否点击
     * 老板助手使用
     */
    @ApiModelProperty(value = "是否点击")
    private Boolean isClick = Boolean.FALSE;

    /**
     * 核算价
     */
    @ApiModelProperty("堂食核算价")
    private BigDecimal accountingPrice;

    @ApiModelProperty("外卖核算价")
    private BigDecimal takeawayAccountingPrice;

    /**
     * 划线价
     */
    @ApiModelProperty(value = "划线价")
    private BigDecimal linePrice;

    /**
     * 商品分类列表
     */
    @ApiModelProperty(value = "商品分类列表")
    private List<ItemTypeListDTO> typeList;

    /**
     * 商品分类列表JSON串
     */
    @ApiModelProperty(value = "商品分类列表JSON串")
    private String typeListJson;
}
