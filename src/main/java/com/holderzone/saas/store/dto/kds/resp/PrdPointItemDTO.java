package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

@ApiModel("kds绑定菜品返回实体")
public class PrdPointItemDTO {

    /**
     * 门店Guid
     */
    @ApiModelProperty("门店Guid")
    private String storeGuid;

    /**
     * 设备Guid
     */
    @ApiModelProperty("设备Guid")
    private String deviceId;

    /**
     * 堂口Guid
     */
    @ApiModelProperty("堂口Guid")
    private String pointGuid;

    /**
     * 商品Guid
     */
    @ApiModelProperty("商品Guid")
    private String itemGuid;

    /**
     * 规格Id
     */
    @ApiModelProperty("规格Id")
    private String skuGuid;

    /**
     * 规格编码
     */
    @ApiModelProperty("规格编码")
    private String skuCode;

    public PrdPointItemDTO() {
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getPointGuid() {
        return pointGuid;
    }

    public void setPointGuid(String pointGuid) {
        this.pointGuid = pointGuid;
    }

    public String getItemGuid() {
        return itemGuid;
    }

    public void setItemGuid(String itemGuid) {
        this.itemGuid = itemGuid;
    }

    public String getSkuGuid() {
        return skuGuid;
    }

    public void setSkuGuid(String skuGuid) {
        this.skuGuid = skuGuid;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PrdPointItemDTO)) {
            return false;
        }
        PrdPointItemDTO that = (PrdPointItemDTO) o;
        return Objects.equals(getStoreGuid(), that.getStoreGuid()) && Objects.equals(getDeviceId(), that.getDeviceId()) && Objects.equals(getPointGuid(), that.getPointGuid()) && Objects.equals(getItemGuid(), that.getItemGuid()) && Objects.equals(getSkuGuid(), that.getSkuGuid()) && Objects.equals(getSkuCode(), that.getSkuCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getStoreGuid(), getDeviceId(), getPointGuid(), getItemGuid(), getSkuGuid(), getSkuCode());
    }

    @Override
    public String toString() {
        return "PrdPointItemDTO{" +
                "storeGuid='" + storeGuid + '\'' +
                ", deviceId='" + deviceId + '\'' +
                ", pointGuid='" + pointGuid + '\'' +
                ", itemGuid='" + itemGuid + '\'' +
                ", skuGuid='" + skuGuid + '\'' +
                ", skuCode='" + skuCode + '\'' +
                '}';
    }
}
