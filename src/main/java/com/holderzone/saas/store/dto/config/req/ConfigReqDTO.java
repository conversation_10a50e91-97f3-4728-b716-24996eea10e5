package com.holderzone.saas.store.dto.config.req;

import com.holderzone.saas.store.enums.common.ConfigEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ConfigRespDTO
 * @date 2019/05/14 17:53
 * @description //TODO common config  配置请求DTO
 * @program holder-saas-stroe-dto
 */
@Data
@ApiModel(value = "门店相关配置请求DTO")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfigReqDTO implements Serializable {

    /**
     * guid
     */
    @ApiModelProperty(value = "业务主键")
    private String guid;

    /**
     * 商户guid
     */
    @ApiModelProperty(value = "商户guid")
    private String enterpriseGuid;

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 配置类型
     */
    @ApiModelProperty(value = "配置类型")
    private Integer dicCode;

    /**
     * 配置类型名称
     */
    @ApiModelProperty(value = "配置类型名称")
    private String dicName;

    /**
     * 配置项value ，多个值可自定义或存json字符串
     */
    @ApiModelProperty(value = "配置项value ，多个值可自定义或存json字符串")
    private String dictValue;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    /**
     * 是否启用  0：启用  1：不启用
     */
    @ApiModelProperty(value = "是否启用  0：启用  1：不启用")
    private Integer isEnable;

    public static ConfigReqDTO init(String storeGuid) {
        return ConfigReqDTO.builder()
                .dicCode(ConfigEnum.ESTIMATE_RECOVERY_TIME.getCode())
                .dicName(ConfigEnum.ESTIMATE_RECOVERY_TIME.getDesc())
                .dictValue("05:00").storeGuid(storeGuid)
                .build();
    }


}
