package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutShopBindReqDTO
 * @date 2018/09/25 9:07
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@ApiModel
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TakeoutShopOwnUnBindReqDTO extends BaseDTO {

    @ApiModelProperty(value = "手机号（用于自营外卖)")
    private String tel;

    @ApiModelProperty(value = "门店号（用于自营外卖)")
    private String storeCode;

    @ApiModelProperty(value = "密码（用于自营外卖)")
    private String passWord;

}
