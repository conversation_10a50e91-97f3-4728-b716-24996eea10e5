package com.holderzone.saas.store.dto.mdm;

import com.holderzone.framework.util.DateTimeUtils;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class MDMResult<T> {

    private int code;

    private String message;

    private T data;

    private long now;

    public static <T> MDMResult<T> success() {
        return success(null);
    }

    public static <T> MDMResult<T> success(T t) {
        return new MDMResult<T>().setCode(200).setMessage("操作成功").setData(t).setNow(DateTimeUtils.nowMillis());
    }

    public static <T> MDMResult<T> failure() {
        return new MDMResult<T>().setCode(500).setMessage("操作失败").setData(null).setNow(DateTimeUtils.nowMillis());
    }
}
