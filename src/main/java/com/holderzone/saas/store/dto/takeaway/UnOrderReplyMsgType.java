package com.holderzone.saas.store.dto.takeaway;

public class UnOrderReplyMsgType {

    private UnOrderReplyMsgType() {
    }

    /**
     * 确认接单
     */
    public static final int CONFIRM_ORDER = 1;

    /**
     * 取消订单
     */
    public static final int CANCEL_ORDER = 2;

    /**
     * 同意取消订单
     */
    public static final int AGREE_CANCEL_ORDER = 3;

    /**
     * 不同意取消订单
     */
    public static final int DISAGREE_CANCEL_ORDER = 4;

    /**
     * 同意退单
     */
    public static final int AGREE_REFUND_ORDER = 5;

    /**
     * 不同意退单
     */
    public static final int DISAGREE_REFUND_ORDER = 6;

    /**
     * 回复催单
     */
    public static final int REPLY_URGE_ORDER = 7;


    /**
     * 商家发起配送
     */
    public static final int START_DELIVERY_ORDER = 8;

    /**
     * 商家取消
     */
    public static final int CANCEL_DELIVERY_ORDER = 9;


    /**
     * 回复骑手接单
     */
    public static final int DELIVERY_KNIGHT_ACCEPT = 101;

    /**
     * 回复骑手到店
     */
    @Deprecated
    public static final int DELIVERY_KNIGHT_REACH_SHOP = 102;

    /**
     * 回复商家已送出
     */
    public static final int DELIVERY_START = 103;

    /**
     * 回复商家已取消
     */
    public static final int DELIVERY_CANCEL = 104;

    /**
     * 回复商家已送达
     */
    public static final int DELIVERY_COMPLETE = 105;

    /**
     * 骑手实时定位
     */
    public static final int DELIVERY_KNIGHT_LOCATION = 106;
}
