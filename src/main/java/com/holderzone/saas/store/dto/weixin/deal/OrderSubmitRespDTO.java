package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("下单返回")
public class OrderSubmitRespDTO {

    @ApiModelProperty("订单id")
    private String orderRecordGuid;

    @ApiModelProperty("失败原因")
    private String errorMsg;

    @ApiModelProperty(value = "true：表示有商品估清")
    private Boolean estimate = false;

    @ApiModelProperty(value = "商品估清名称列表")
    private List<String> estimateItemNames;

    @ApiModelProperty("订单guid")
    private String orderGuid;

    /**
     * 订单来源 BaseDeviceTypeEnum
     */
    @ApiModelProperty("订单来源")
    private Integer deviceType;

    @ApiModelProperty("优惠提示")
    private String discountTips;

    public static OrderSubmitRespDTO unCombineMasterDevice() {
        return new OrderSubmitRespDTO().setErrorMsg("当前门店暂未绑定主机");
    }

    public static OrderSubmitRespDTO submitFailed() {
        return new OrderSubmitRespDTO().setErrorMsg("下单失败");
    }

    public static OrderSubmitRespDTO submitFailed(String errorMsg) {
        return new OrderSubmitRespDTO().setErrorMsg("下单失败" + ":" + errorMsg);
    }

    public static OrderSubmitRespDTO submitDiscountFailed(String tips) {
        return new OrderSubmitRespDTO().setDiscountTips(tips);
    }

    public static OrderSubmitRespDTO estimateFaild(List<String> estimateItemNames) {
        return new OrderSubmitRespDTO().setErrorMsg("商品估清").setEstimate(true).setEstimateItemNames(estimateItemNames);
    }

    public static OrderSubmitRespDTO estimateFaild(String errorMsg) {
        return new OrderSubmitRespDTO().setErrorMsg(errorMsg).setEstimate(true);
    }

    public static OrderSubmitRespDTO upperLimit(String errorMsg) {
        return new OrderSubmitRespDTO().setErrorMsg(errorMsg);
    }

    public static OrderSubmitRespDTO unSet() {
        return new OrderSubmitRespDTO().setErrorMsg("门店暂未配置");
    }

    public static OrderSubmitRespDTO noItem() {
        return new OrderSubmitRespDTO().setErrorMsg("已经下单，购物车没有商品");
    }

    public static OrderSubmitRespDTO tableError() {
        return new OrderSubmitRespDTO().setErrorMsg("桌台开台失败，无法玩游戏");
    }
}
