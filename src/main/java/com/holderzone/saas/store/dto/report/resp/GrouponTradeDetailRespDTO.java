package com.holderzone.saas.store.dto.report.resp;

import com.holderzone.saas.store.enums.report.CouponSourceEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 团购订单结算明细
 */
@Data
public class GrouponTradeDetailRespDTO implements Serializable {

    private static final long serialVersionUID = -1667368987764335434L;

    /**
     * @see CouponSourceEnum
     * 券来源 0:美团 1：抖音
     */
    private Integer source;

    /**
     * 券码
     */
    private String couponCode;

    /**
     * 券码使用时间
     */
    private String couponUseTime;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 三方门店id
     */
    private String thirdStoreGuid;

    /**
     * 三方门店名字
     */
    private String thirdStoreName;

    /**
     * 业务系统订单单号
     */
    private String orderNo;

    /**
     * 项目名称
     */
    private String dealTitle;

    /**
     * 项目id
     */
    private String dealId;

    /**
     * 市场价(券面值)
     */
    private BigDecimal dealValue;

    /**
     * 商家促销金额
     */
    private String bizCost;

    /**
     * 团购券码购买价格
     */
    private BigDecimal couponBuyPrice;

    /**
     * 商家预计应得金额
     */
    private String due;

    /**
     * 是否量贩：0：不是，1：是
     */
    private Integer volume;

    /**
     * 量贩项目的单张券原价（普通券单张券原价与项目总价相同）
     */
    private String singleValue;

    /**
     * 券售价金额
     */
    private BigDecimal couponSellPrice;
}
