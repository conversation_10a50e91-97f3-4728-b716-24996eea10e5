package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickJHPayRespDTO
 * @date 2019/03/15 17:28
 * @description 微信桌贴聚合支付响应DTO
 * @program holder-saas-store
 */
@Data
@ApiModel(value = "微信桌贴聚合支付响应DTO")
@AllArgsConstructor
@NoArgsConstructor
public class WxStickJHPayRespDTO {
    private String attachData;

    private String code;

    private String msg;

    private String result;
}
