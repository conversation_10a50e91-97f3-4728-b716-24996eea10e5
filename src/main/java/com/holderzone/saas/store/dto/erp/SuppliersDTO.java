package com.holderzone.saas.store.dto.erp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @className SuppliersReqDTO
 * @date 2019-04-26 10:59:23
 * @description
 * @program holder-saas-store-dto
 */
public class SuppliersDTO implements Serializable {

    private static final long serialVersionUID = -2519917742999881934L;
    @ApiModelProperty(value = "主键guid")
    private String guid;
    @ApiModelProperty(value = "供应商名称")
    private String name;
    @ApiModelProperty(value = "单位电话")
    private String officeTel;
    @ApiModelProperty(value = "联系人")
    private String contactName;
    @ApiModelProperty(value = "联系人电话")
    private String contactTel;
    @ApiModelProperty(value = "地址")
    private String addr;
    @ApiModelProperty(value = "结算周期")
    private Integer settlementInterval;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "供应商关联的门店或其企业")
    private String foreignKey;
    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;
    @ApiModelProperty(value = "状态(0:已禁用，1:启用)")
    private Integer enabled;
    @ApiModelProperty(value = "0:正常，1:已删除")
    private Integer deleted;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOfficeTel() {
        return officeTel;
    }

    public void setOfficeTel(String officeTel) {
        this.officeTel = officeTel;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactTel() {
        return contactTel;
    }

    public void setContactTel(String contactTel) {
        this.contactTel = contactTel;
    }

    public String getAddr() {
        return addr;
    }

    public void setAddr(String addr) {
        this.addr = addr;
    }

    public Integer getSettlementInterval() {
        return settlementInterval;
    }

    public void setSettlementInterval(Integer settlementInterval) {
        this.settlementInterval = settlementInterval;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(String foreignKey) {
        this.foreignKey = foreignKey;
    }

    public String getEnterpriseGuid() {
        return enterpriseGuid;
    }

    public void setEnterpriseGuid(String enterpriseGuid) {
        this.enterpriseGuid = enterpriseGuid;
    }
}
