package com.holderzone.saas.store.dto.takeaway;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class TcdSyncItemMappingDTO implements Serializable {

    private static final long serialVersionUID = 5907441892010358378L;

    /**
     * 企业id
     */
    @NotNull(message = "企业id不能未空")
    private String enterpriseGuid;

    /**
     * 被同步的门店
     */
    private String storeGuid;

    /**
     * 同步门店
     */
    private List<TcdStoreMapping> storeMappingList;


    @Data
    public static class TcdStoreMapping implements Serializable {

        private static final long serialVersionUID = 543374860604855568L;

        /**
         * 同步的门店
         */
        private String storeGuid;

        /**
         * 同步商品明细
         */
        @NotEmpty
        private List<TcdItemMapping> itemMappingList;

        @Data
        public static class TcdItemMapping implements Serializable {

            private static final long serialVersionUID = -679175718266220377L;

            /**
             * 原赚餐商品id
             */
            private String sourceUnItemSkuId;

            /**
             * 现赚餐商品id
             */
            private String targetUnItemSkuId;

            /**
             * 被同步的赚餐商品id
             */
            private String syncUnItemSkuId;
        }

    }


}
