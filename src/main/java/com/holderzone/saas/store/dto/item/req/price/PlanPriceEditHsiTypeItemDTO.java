package com.holderzone.saas.store.dto.item.req.price;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 编辑分类
 * @date 2024/3/26
 */
@Data
public class PlanPriceEditHsiTypeItemDTO {

    @ApiModelProperty(value = "分类id")
    private String id;

    @ApiModelProperty(value = "分类名称")
    private String name;


    @ApiModelProperty(value = "编辑排序List")
    private List<PlanPriceSortEditDTO> sortList;

    @ApiModelProperty(value = "编辑分类名称")
    private List<PlanPriceSaleNameEditDTO> saleNameList;

}
