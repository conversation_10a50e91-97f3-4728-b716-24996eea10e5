package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PreOrderValidateRespDTO
 * @date 2019/01/07 下午7:39
 * @description 一体机预点餐菜谱方案验证返回实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "一体机预点餐菜谱方案验证返回实体")
public class PreOrderValidateRespDTO implements Serializable {

    @ApiModelProperty(value = "验证结果说明")
    private String desc;

}
