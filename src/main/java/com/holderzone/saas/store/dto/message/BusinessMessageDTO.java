package com.holderzone.saas.store.dto.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessMessageDTO
 * @date 2018/09/04 11:15
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessMessageDTO implements Serializable {

    private String messageGuid;

    @ApiModelProperty(value = "消息标题")
    private String subject;

    @ApiModelProperty(value = "消息体")
    private String content;

    @ApiModelProperty(value = "推送的消息类型 0:小铃铛，1:业务消息，2:系统消息")
    private Integer messageType;

    @ApiModelProperty(value = "消息详情类型 11:订单下单 12:订单结账 13:微信服务 14：")
    private Integer detailMessageType;

    @ApiModelProperty(value = "平台，1:云端平台,2:其他业务平台")
    private String platform;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名")
    private String storeName;

    @ApiModelProperty(value = "状态 0:未读，1:已读")
    private String state;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime pushTime;

    @ApiModelProperty(value = "自由拼接的消息类型")
    private String messageTypeStr;

}
