package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 打印公共基础类
 *
 * <AUTHOR>
 * @date 2018/09/18 19:32
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(description = "打印请求基础类")
public class PrintDTO implements PrintDataMockito, Serializable {

    private static final long serialVersionUID = -5442220773853902934L;

    @NotNull(message = "打印票据类型")
    @ApiModelProperty(value = "打印票据类型", required = true)
    private Integer invoiceType;

    @NotBlank(message = "企业GUID不得为空")
    @ApiModelProperty(value = "企业GUID", required = true)
    private String enterpriseGuid;

    @NotBlank(message = "门店GUID不能为空")
    @ApiModelProperty(value = "门店GUID", required = true)
    private String storeGuid;

    @NotBlank(message = "printUid不能为空")
    @ApiModelProperty(value = "打印UID", required = true)
    private String printUid;

    @Nullable
    @ApiModelProperty(value = "打印区域，正餐、微信点餐模式下该值不能为空",
            notes = "PrintRecordServiceImpl#assertAreaGuidByTradeMode方法已做校验")
    private String areaGuid;

    @Nullable
    @ApiModelProperty(value = "打印区域桌台，正餐、微信点餐模式下该值不能为空",
            notes = "PrintRecordServiceImpl#assertAreaGuidByTradeMode方法已做校验")
    private String tableGuid;

    @Nullable
    @ApiModelProperty(value = "交易模式", required = true)
    private Integer tradeMode;

    @NotBlank(message = "操作人GUID不能为空")
    @ApiModelProperty(value = "操作人GUID", required = true)
    private String operatorStaffGuid;

    @NotBlank(message = "操作人姓名不能为空")
    @ApiModelProperty(value = "操作人姓名", required = true)
    private String operatorStaffName;

    @NotNull(message = "打印时间不能为空")
    @ApiModelProperty(value = "打印时间", required = true)
    private Long createTime;

    @NotBlank(message = "设备编号不能为空")
    @ApiModelProperty(value = "设备编号", required = true)
    private String deviceId;

    @NotNull(message = "打印来源不能为空")
    @ApiModelProperty(value = "打印来源", required = true)
    private PrintSourceEnum printSourceEnum;

    @ApiModelProperty(value = "指定打印设备")
    private Integer appointPrinterType;

    @ApiModelProperty(value = "子单据类型：0=点菜单，1=叫起单，2=催菜单，3=挂起单", notes = "整单备注可以为空")
    private Integer itemInvoiceType;

    @ApiModelProperty(value = "开票二维码", required = true)
    private String invoiceCode;

    @ApiModelProperty(value = "开票金额", required = true)
    private String invoiceAmount;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    /**
     * 手动打印标识
     */
    private Boolean manualPrint = false;

    @ApiModelProperty(value = "打印价格 0不打印 1打印")
    private Boolean printPriceType;

    /**
     * 是否部分退款
     */
    @Nullable
    private Boolean partRefundFlag;

    @Override
    public void applyMock() {
        setOperatorStaffName("管理员");
    }
}
