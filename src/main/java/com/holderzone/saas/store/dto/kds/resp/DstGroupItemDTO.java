package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 出堂口菜品分组
 */
@Data
@NoArgsConstructor
public class DstGroupItemDTO implements Serializable {

    private static final long serialVersionUID = -2240162461066846742L;

    @ApiModelProperty("分组guid")
    private String groupGuid;

    @ApiModelProperty("分组名称")
    private String groupName;

    @ApiModelProperty("商品列表")
    private List<PrdDstItemDTO> items;
}
