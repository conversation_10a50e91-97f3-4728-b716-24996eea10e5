package com.holderzone.saas.store.dto.takeaway;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 催单实体
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class UnRemind implements Serializable {

    private static final long serialVersionUID = 2560652386455710951L;

    /**
     * 商户GUID
     */
    private String enterpriseGuid;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 外卖单ID
     */
    private String orderId;

    /**
     * 催单消息标识，催单ID
     */
    private String remindId;

    /**
     * 回复内容,拒单原因或者催单回复内容
     */
    private String replyContent;

    /**
     * 催单时间
     */
    private LocalDateTime remindTime;
}
