package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OrderMustPointCheckDTO implements Serializable {

    @ApiModelProperty(value = "购物车商品列表")
    private List<ItemInfoDTO> shopCartItemList;

    @ApiModelProperty("门店Guid")
    private String storeGuid;
}
