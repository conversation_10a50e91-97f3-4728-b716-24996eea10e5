package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Accessors(chain = true)
@ApiModel("用户权限查询返回实体")
public class UserAuthorityDataDTO implements Serializable {

    private static final long serialVersionUID = -5609254499610900868L;

    @ApiModelProperty(value = "权限名称列表")
    private List<AuthorityDTO> authorityDTOS;

    @ApiModelProperty(value = "请求值、响应值：用户可分配的角色id，逗号分割")
    private List<UserAuthorityDTO> userAuthorityDTOS;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public List<AuthorityDTO> getAuthorityDTOS() {
        return authorityDTOS;
    }

    public void setAuthorityDTOS(List<AuthorityDTO> authorityDTOS) {
        this.authorityDTOS = authorityDTOS;
    }

    public List<UserAuthorityDTO> getUserAuthorityDTOS() {
        return userAuthorityDTOS;
    }

    public void setUserAuthorityDTOS(List<UserAuthorityDTO> userAuthorityDTOS) {
        this.userAuthorityDTOS = userAuthorityDTOS;
    }

    public UserAuthorityDataDTO(List<AuthorityDTO> authorityDTOS, List<UserAuthorityDTO> userAuthorityDTOS) {
        this.authorityDTOS = authorityDTOS;
        this.userAuthorityDTOS = userAuthorityDTOS;
    }

    public UserAuthorityDataDTO(List<UserAuthorityDTO> userAuthorityDTOS) {
        this.userAuthorityDTOS = userAuthorityDTOS;
    }

    public UserAuthorityDataDTO() {
    }
}
