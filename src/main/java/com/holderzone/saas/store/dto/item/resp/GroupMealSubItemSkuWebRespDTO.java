package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GroupMealSubItemSkuWebRespDTO
 * @date 2019/12/07 上午11:16
 * @description //团餐分类下子菜详情返回实体
 * @program holder
 */

@Data
@ApiModel(value = "团餐分类下子菜详情返回实体")
public class GroupMealSubItemSkuWebRespDTO extends  SubItemSkuWebRespDTO{

    @ApiModelProperty(value = "毛利率")
    private BigDecimal grossMargin;

    @ApiModelProperty(value = "单商品总数量销售价")
    private BigDecimal price;

    @ApiModelProperty(value = "单商品总数量成本价")
    private BigDecimal costPrice;

    @ApiModelProperty(value = "单商品销售价")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "单商品销售价")
    private BigDecimal saleCostPrice;

}
