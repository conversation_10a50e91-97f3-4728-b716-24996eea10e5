package com.holderzone.saas.store.dto.retail.dinein;

import com.holderzone.saas.store.dto.retail.bill.common.RetailItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReturnItemDTO
 * @date 2019/01/17 14:44
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class ReturnItemDTO {

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "标记退货是否为赠送（0：否，1：是）")
    private Integer isFree;

    @ApiModelProperty(value = "数量")
    private BigDecimal count;

    @ApiModelProperty(value = "退货价格小计")
    private BigDecimal itemPrice;

    @ApiModelProperty(value = "商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂 ，8.已上菜 )")
    private Integer itemState;

    @ApiModelProperty(value = "操作人guid")
    private String staffGuid;

    @ApiModelProperty(value = "操作人名字")
    private String staffName;

    @ApiModelProperty(value = "商品")
    private RetailItemDTO retailItemDTO;
}
