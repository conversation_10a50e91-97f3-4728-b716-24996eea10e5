package com.holderzone.saas.store.dto.member.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 随行红包订单信息
 * @date 2022/3/29 11:48
 * @className: RedPacketOrderDTO
 */
@Data
@ApiModel("随行红包订单信息")
@Accessors(chain = true)
public class RedPacketOrderDTO implements Serializable {

    private static final long serialVersionUID = -3855804047351703662L;

    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty("订单号")
    private String orderNumber;
}
