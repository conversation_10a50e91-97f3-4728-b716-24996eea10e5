package com.holderzone.saas.store.dto.print.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 排队单
 *
 * <AUTHOR>
 * @date 2019/05/07 11:48
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("排队单")
public class PrintQueueDTO extends PrintDTO {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名")
    private String storeName;

    @NotNull(message = "取号时间不能为空")
    @ApiModelProperty(value = "取号时间", required = true)
    private Long queueTime;

    @NotNull(message = "就餐人数不得为空")
    @ApiModelProperty(value = "就餐人数", required = true)
    private Integer personNumber;

    @NotBlank(message = "排队号码不能为空")
    @ApiModelProperty(value = "排队号码")
    private String queueNumber;

    @NotNull(message = "需等待桌台数不能为空")
    @ApiModelProperty(value = "需等待桌台数", required = true)
    private Long waitNumber;

    @Nullable
    @ApiModelProperty(value = "排队二维码链接")
    private String queueUrl;

    @Nullable
    @ApiModelProperty(value = "营销信息", notes = "当前迭代暂未使用")
    private String marketingInfo;

    @Nullable
    @ApiModelProperty(value = "门店电话", notes = "如果未设置门店电话，该值为空")
    private String tel;
}
