package com.holderzone.saas.store.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * group统计返回对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ApiModel("group统计返回对象")
public class CountItemDTO implements Serializable {

    private static final long serialVersionUID = -4307925127548322622L;

    /**
     * 分类键
     */
    @ApiModelProperty(value = "分类键")
    private String item;

    /**
     * 条数
     */
    @ApiModelProperty(value = "条数")
    private Integer num;
}
