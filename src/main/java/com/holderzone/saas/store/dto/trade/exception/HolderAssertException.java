package com.holderzone.saas.store.dto.trade.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HolderAssertException
 * @date 2019/12/13 11:24
 * @description //TODO
 * @program IdeaProjects
 */
public class HolderAssertException extends RuntimeException{

    /**
     * 异常message
     */
    private String message;

    /**
     * 异常编码
     */
    private String code;

    /**
     * 模块
     */
    private String model;


    public HolderAssertException(String code,String message){
        super(message);
        this.message = message;
        this.code = code;
        this.model = "HOULDER-TRADE";
    }

}