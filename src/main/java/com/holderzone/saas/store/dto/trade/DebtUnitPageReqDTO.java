package com.holderzone.saas.store.dto.trade;

import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 挂账单位分页查询入参
 *
 * <AUTHOR>
 * @since 2020-12-16
 */
@ApiModel(value = "挂账单位分页查询入参DTO")
public class DebtUnitPageReqDTO extends PageDTO {

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

}
