package com.holderzone.saas.store.dto.print;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 查询打印机可关联菜品
 *
 * <AUTHOR>
 * @date 2021/4/11 13:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "查询打印机可关联菜品")
public class PrintItemReqDto extends BaseDTO {

    @ApiModelProperty(value = "打印机已关联菜品Guid")
    private List<String> itemGuids;
}
