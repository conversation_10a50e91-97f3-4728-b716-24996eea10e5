package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class SendCallRespDTO implements Serializable {

    @ApiModelProperty(value = "statusCode", required = true)
    private Integer statusCode;

    @ApiModelProperty("message")
    private SendCallMessageRespDTO message;

    @ApiModelProperty("data")
    private SendCallDataRespDTO data;
}
