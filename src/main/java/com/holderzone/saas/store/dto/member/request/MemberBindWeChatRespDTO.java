package com.holderzone.saas.store.dto.member.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 会员绑定微信实体
 * @date 2021/9/7 10:53
 * @className: MemberBindWeChatRespDTO
 */
@Data
@Accessors(chain = true)
@ApiModel("会员绑定微信实体")
public class MemberBindWeChatRespDTO implements Serializable {

    private static final long serialVersionUID = -1944573317468695090L;

    @ApiModelProperty("手机号")
    private String phoneNum;

    @ApiModelProperty("短信验证码")
    private String smsCode;

    @ApiModelProperty("微信授权码")
    private String authCode;
}
