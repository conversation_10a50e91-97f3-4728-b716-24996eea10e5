package com.holderzone.saas.store.dto.trade.req.adjust;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 调整单详情查询DTO
 */
@Data
@Accessors(chain = true)
public class AdjustOrderQueryDTO extends BasePageDTO {

    private static final long serialVersionUID = -2566037494096470408L;

    @ApiModelProperty("订单guid")
    private Long orderGuid;

    @ApiModelProperty("调整单guid")
    private Long adjustOrderGuid;

    @ApiModelProperty("订单明细guids")
    private List<Long> orderItemGuids;

    @ApiModelProperty("订单guids")
    private List<Long> orderGuids;

}
