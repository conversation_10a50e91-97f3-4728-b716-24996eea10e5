package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class CouPonPreReqDTO extends BaseDTO {

    private static final long serialVersionUID = 1549956790243616158L;

    /**
     * 券码
     */
    @NotNull
    @ApiModelProperty(value = "券码,12位纯数字")
    private String couponCode;

    /**
     * 第三方erp订单号
     */
    private String erpOrderId;

    @ApiModelProperty(value = "第三方活动guid")
    private String activityGuid;

    @ApiModelProperty(value = "已使用活动guid列表")
    private List<String> usedGuidList;

    /**
     * 是否使用其他类型活动
     */
    @ApiModelProperty(value = "是否使用其他类型活动")
    private Boolean hasUseActivity;

    /**
     * GroupBuyTypeEnum
     */
    @ApiModelProperty(value = "团购类型")
    @NotNull(message = "请更新到最新版本")
    private Integer groupBuyType;

    /**
     * 抖音/支付宝团购券扫码获取的参数
     */
    private String encryptedData;

    /**
     * 是否启用跨单核销。 该值默认为false
     * 用户多次下单购买相同商品，当cross_order为true时，传入任一订单二维码信息可以获取用户多次购买该商品的所有有效券码信息
     */
    private Boolean crossOrder;
}
