package com.holderzone.saas.store.dto.config.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/20
 * @since 1.8
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "收银设置返回")
public class CashSettingRespDTO {

    /**
     * 手动清台：0关闭 1开启
     */
    @ApiModelProperty(value = "手动清台：0关闭 1开启")
    private Integer enableHandleClose;

    /**
     * 同一订单多次使用聚合支付：0关闭 1开启
     */
    @ApiModelProperty(value = "同一订单多次使用聚合支付：0关闭 1开启")
    private Integer enableMultiplePay;

    /**
     * 正餐支持换高价值或低价值的菜 0不支持 1支持
     */
    @ApiModelProperty(value = "正餐支持换高价值或低价值的菜 0不支持 1支持")
    private Integer enableDineInChangeDiffValue;

    /**
     * 快餐餐支持换高价值或低价值的菜 0不支持 1支持
     */
    @ApiModelProperty(value = "快餐餐支持换高价值或低价值的菜 0不支持 1支持")
    private Integer enableFastChangeDiffValue;

    @ApiModelProperty(value = "初始号牌")
    private String initMark;

    @ApiModelProperty(value = "自动号牌（0：否，1：是）")
    private Integer autoMark;
}
