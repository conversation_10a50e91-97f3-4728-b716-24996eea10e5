package com.holderzone.saas.store.dto.item.resp;

import com.holderzone.framework.util.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplatesRespDTO
 * @date 2019/05/30 10:40
 * @description //TODO 门店销售模板列表
 * @program holder-saas-aggregation-app
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "门店销售模板列表")
public class ItemTemplatesRespDTO {

    @ApiModelProperty(value = "当前门店使用的模板名称")
    private String currentTemplateName;

    @ApiModelProperty(value = "分页模板列表")
    private Page<ItemTemplateRespDTO> templateRespDTOS;

}
