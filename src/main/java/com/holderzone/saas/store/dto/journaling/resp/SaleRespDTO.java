package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
@Data
@ApiModel("老板助手-销售统计请求返回")
public class SaleRespDTO {


    private static final long serialVersionUID = 7094801479835129404L;

    @ApiModelProperty(value = "一次数据加载时间节点时间戳")
    private long dataEndTime;

    @ApiModelProperty(value = "门店统计数据列表")
    private List<SaleCountRespDTO> statisticsDetailDTOS;
}
