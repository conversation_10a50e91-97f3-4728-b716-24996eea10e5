package com.holderzone.saas.store.dto.weixin.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.AppendFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreTradeOrderDetailsRespDTO
 * @date 2019/3/27
 */
@Data
@ApiModel("微信正餐返回订单详情")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WxStoreTradeOrderDetailsRespDTO {

	@ApiModelProperty(value = "0并桌，1：非并桌")
	private Integer combine = 1;

	private List<WxStoreTradeDetailsGroupDTO> wxStoreTradeDetailsGroupDTOS;

	@ApiModelProperty(value = "订单号")
	private String orderNo="";

	@ApiModelProperty("取餐号")
	private String mark = "";

	@ApiModelProperty(value = "桌台名字")
	private String tableCode;

	@ApiModelProperty(value = "交易模式：交易模式(0：正餐，1：快餐)")
	private Integer tradeMode;

	@ApiModelProperty(value = "订单详情的消费合计")
	private BigDecimal totalPrice = BigDecimal.ZERO;

	@ApiModelProperty(value = "订单金额（商品总额+附加费）")
	private BigDecimal orderFee = BigDecimal.ZERO;

	@ApiModelProperty(value = "找零（收款-应收金额）")
	private BigDecimal changeFee = BigDecimal.ZERO;

	@ApiModelProperty(value = "优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
	private BigDecimal discountFee = BigDecimal.ZERO;

	@ApiModelProperty(value = "订单金额1.0")
	private List<OrderFeeDetailDTO> orderFeeDetailDTOS = Collections.emptyList();

	@ApiModelProperty(value = "实收金额明细（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
	private List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS = Collections.emptyList();

	@ApiModelProperty(value = "优惠金额明细（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
	private List<DiscountFeeDetailDTO> discountFeeDetailDTOS = Collections.emptyList();

	@ApiModelProperty(value = "下单时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime gmtCreate;

	@ApiModelProperty(value = "结算时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime checkoutTime;

	@ApiModelProperty(value = "桌台名字")
	private String diningTableName;

	@ApiModelProperty(value = "消费人数")
	private Integer guestCount;

	@ApiModelProperty(value="订单状态,0:提交，1：确认，2：结账 ,3:已取消")
	private Integer orderState;

	@ApiModelProperty(value = "桌台所在区域名称")
	private String areaName;

	@ApiModelProperty(value = "门店名称")
	private String storeName;

	@ApiModelProperty(value = "品牌名称")
	private String brandName;

	@ApiModelProperty(value = "支付方式")
	private String payWay="在线支付";

	@ApiModelProperty(value = "错误")
	private String errorMsg;

	@ApiModelProperty(value = "附加费")
	private List<AppendFeeDetailDTO> appendFeeDetailDTOS;

	@ApiModelProperty(value = "附加费总计")
	private BigDecimal appendFee = BigDecimal.ZERO;

	@ApiModelProperty(value = "应付金额=合计消费-折扣")
	private BigDecimal payableAmount = BigDecimal.ZERO;

	@ApiModelProperty(value = "消费合计=商品金额+附加费")
	private BigDecimal totalConsumption = BigDecimal.ZERO;

	@ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
	private BigDecimal actuallyPayFee = BigDecimal.ZERO;

	/**
	 * 以下是会员q3i2增加
	 */

	@ApiModelProperty(value = "非会员小计")
	private BigDecimal unMemberTotalAmount;

	@ApiModelProperty(value = "省零")
	private BigDecimal saveZero;

	@ApiModelProperty(value = "1:不使用,2：唯一优惠 3：请选择")
	private Integer memberCardStatus;

	@ApiModelProperty(value = "1:不使用,2：唯一优惠 3：请选择")
	private Integer memberVolumeStatus;

	@ApiModelProperty(value = "会员卡优惠")
	private BigDecimal memberCardFee;

	@ApiModelProperty(value = "优惠券优惠")
	private BigDecimal memberVolumeFee;

	@ApiModelProperty("会员卡积分")
	private Integer cardIntegral;

	@ApiModelProperty(value = "积分优惠")
	private BigDecimal integralFee;

	/**
	 * 页面穿透字段
	 */
	@ApiModelProperty("卡GUID")
	private String cardGuid;

	@ApiModelProperty("卡名称")
	private String cardName;

	@ApiModelProperty("会员持卡GUID")
	private String memberInfoCardGuid;

	@ApiModelProperty("卡体系GUID")
	private String systemManagementGuid;

	@ApiModelProperty("会员卡积分")
	private Integer memberIntegral;

	@ApiModelProperty(value = "优惠券code")
	private String volumeCode;

}
