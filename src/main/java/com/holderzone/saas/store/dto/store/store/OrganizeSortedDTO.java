package com.holderzone.saas.store.dto.store.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreCreateDTO
 * @date 2018/07/23 下午4:27
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class OrganizeSortedDTO implements Serializable {

    private static final long serialVersionUID = -6925937527355198643L;

    /**
     * 组织guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "组织guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "组织guid", required = true)
    private String organizationGuid;

    /**
     * 组织排序
     */
    @NotNull
    @Min(value = 0, message = "门店排序范围[1-16777215]")
    @Max(value = 16777215, message = "门店排序范围[1-16777215]")
    @ApiModelProperty(value = "门店排序，范围：1-16777215", required = true)
    private Integer sort;
}
