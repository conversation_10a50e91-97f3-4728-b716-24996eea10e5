package com.holderzone.saas.store.dto.business.brand;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 品牌配置DTO
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BrandConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @ApiModelProperty(value = "全局唯一主键")
    private Long guid;

    /**
     * 品牌guid
     */
    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    /**
     * 反结账时效限制
     */
    @ApiModelProperty(value = "反结账时效限制")
    private Integer recoveryTimeLimit;

    /**
     * 反结账时效限制单位，0：小时 1：天
     */
    @ApiModelProperty(value = "反结账时效限制单位，0：小时 1：天")
    private Integer recoveryTimeLimitUnit;

    /**
     * 退款时效限制
     */
    @ApiModelProperty(value = "退款时效限制")
    private Integer refundTimeLimit;

    /**
     * 退款时效限制单位，0：小时 1：天
     */
    @ApiModelProperty(value = "退款时效限制单位，0：小时 1：天")
    private Integer refundTimeLimitUnit;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;
}
