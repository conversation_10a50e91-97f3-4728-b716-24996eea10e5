package com.holderzone.saas.store.dto.weixin.req;

import com.holderzone.framework.util.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStorePageReqDTO
 * @date 2019/02/15 14:06
 * @description 微信点餐门店信息同步请求DTO
 * @program holder-saas-store-weixin
 */
@Data
@ApiModel(value = "微信点餐门店信息请求DTO，不传默认查询所有")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxStorePageReqDTO extends Page implements Serializable {

    private static final long serialVersionUID = -643642932376619235L;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;
}
