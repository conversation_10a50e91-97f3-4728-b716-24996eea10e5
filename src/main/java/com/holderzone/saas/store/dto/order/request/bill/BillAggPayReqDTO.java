package com.holderzone.saas.store.dto.order.request.bill;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillCalculateReqDTO
 * @date 2019/01/28 17:32
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class BillAggPayReqDTO extends BaseDTO {

    @ApiModelProperty(value = "订单guid")
    @OrderLockField
    private String orderGuid;

    @ApiModelProperty(value = "支付金额", required = true)
    private BigDecimal amount;

    /**
     * 注意：
     * 目前看此字段意思为是否组合支付, 跟之前的注释有区别，并非标识最后一次支付
     * 当组合支付时，安卓端传参为 false
     * 如果需要使用本订单最后一次发起支付标识，请使用checkoutSuccessFlag
     */
    @ApiModelProperty(value = "是否最后一次支付", required = true)
    private Boolean last;

    @ApiModelProperty(value = "是否最后结账")
    private Boolean checkoutSuccessFlag;

    @ApiModelProperty(value = "发起条码支付的授权码，当微信、支付宝条码支付时候，必填！！", required = true)
    private String authCode;

    @ApiModelProperty(value = "订单金额（商品总额+附加费）")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "附加费")
    private BigDecimal appendFee;

    @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "使用的积分")
    private Integer useIntegral;

    @ApiModelProperty(value = "是否是积分商城0：不是，1：是")
    private Integer memberIntegralStore;

    @ApiModelProperty(value = "积分抵扣了多少钱")
    private BigDecimal integralDiscountMoney;

    @ApiModelProperty(value = "当前的优惠信息")
    private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

    @ApiModelProperty(value = "version")
    @LockField
    private Integer version;

    @ApiModelProperty("其他支付参数")
    private BillPayReqDTO billPayReqDTO;

    /**
     *
     */
    @ApiModelProperty(value = "支付功能的id; " +
            "31:支付宝二维码支付(客户扫我们代理商返回给的二维码)、" +
            "2:支付宝条码支付(扫码枪)、3:支付宝公众号支付、" +
            "51:微信二维码支付、9:微信条码支付、10:微信公众号支付。105:kbzPay" +
            "扫码枪不用传")
    private String payPowerId;

    @ApiModelProperty(value = "会员卡guid")
    private String memberInfoCardGuid;

    @ApiModelProperty(value = "会员guid")
    private String memberInfoGuid;

    /**
     * 主动扫描1
     */
    private Integer activeScan;

    @ApiModelProperty(value = "支付guid")
    private Long payGuid;

    @ApiModelProperty(value = "多次支付guid")
    private Long multipleRecordGuid;

}
