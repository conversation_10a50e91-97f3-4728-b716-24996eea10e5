package com.holderzone.saas.store.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@Data
public class OrderMultiMemberPayDTO implements Serializable {

    private static final long serialVersionUID = -8951585514851962893L;

    @ApiModelProperty("卡支付余额")
    private BigDecimal memberCardBalance;

    @ApiModelProperty("本次消费实充金额")
    private BigDecimal rechargeAmount;

    @ApiModelProperty("本次消费赠送金额")
    private BigDecimal giftAmount;

    @ApiModelProperty("本次消费补贴余额")
    private BigDecimal subsidyAmount;

    @ApiModelProperty("会员卡号")
    private String memberCardNum;

    @ApiModelProperty("本次支付总金额 统计")
    private BigDecimal amount;
}
