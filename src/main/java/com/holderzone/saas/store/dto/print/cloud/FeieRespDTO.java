package com.holderzone.saas.store.dto.print.cloud;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/26
 * @description 飞蛾返回
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "飞蛾返回", description = "飞蛾返回")
public class FeieRespDTO implements Serializable {

    private static final long serialVersionUID = 3829333068233245993L;

    @ApiModelProperty(value = "返回提示")
    private String msg;

    @ApiModelProperty(value = "返回code")
    private Integer ret;

    @ApiModelProperty(value = "成功/失败数据")
    private DataMsg data;

    @ApiModelProperty(value = "服务器执行时间")
    private Integer serverExecutedTime;

    @Data
    public static class DataMsg {

        @ApiModelProperty(value = "添加成功的返回值")
        private List<String> ok;

        @ApiModelProperty(value = "添加失败的返回值")
        private List<String> no;

        @ApiModelProperty(value = "机型 0：58小票机，1：80小票机，2：标签机，3：出餐宝")
        private Integer model;

        @ApiModelProperty(value = "状态 0：离线 1：在线，工作状态正常 2：在线，工作状态不正常")
        private Integer status;

        @ApiModelProperty(value = "printlogo")
        private String printlogo;

    }

}
