package com.holderzone.saas.store.dto.kds.req;

import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DstItemStatusReqDTO extends PageDTO {

    private static final long serialVersionUID = -4247230675662759853L;

    @NotBlank(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @NotBlank(message = "设备ID不得为空")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @NotNull(message = "屏幕显示分类不得为空")
    @Pattern(regexp = "^[1248]$")
    @ApiModelProperty(value = "屏幕显示分类：外卖=1，快餐=2，正餐=4，全部=8")
    private String displayType;

    @NotNull(message = "显示模式不得为空")
    @Min(value = 0, message = "显示模式" +
            "出堂点位显示模式：0=单菜品模式，1=菜品汇总模式，2=订单模式，3=复合模式,4=混合模式1*6,5=混合-订单模式1*5,6=混合-汇总模式1*10, 7=菜品分组汇总模式")
    @Max(value = 7, message = "显示模式" +
            "出堂点位显示模式：0=单菜品模式，1=菜品汇总模式，2=订单模式，3=复合模式,4=混合模式1*6,5=混合-订单模式1*5,6=混合-汇总模式1*10, 7=菜品分组汇总模式")
    @ApiModelProperty(value = "显示模式" +
            "出堂点位显示模式：0=单菜品模式，1=菜品汇总模式，2=订单模式，3=复合模式,4=混合模式1*6,5=混合-订单模式1*5,6=混合-汇总模式1*10, 7=菜品分组汇总模式")
    private Integer displayMode;

    @ApiModelProperty(value = "查询关键字")
    private String keywords;

    @ApiModelProperty(value = "菜品展示类型 0：汇总中展示，订单中不展示；1：汇总和订单中都展示")
    private Integer itemDisplayType;

    /**
     * 是否允许重复
     */
    @ApiModelProperty(value = "菜品是否允许重复显示")
    private Boolean allowRepeatFlag;

}
