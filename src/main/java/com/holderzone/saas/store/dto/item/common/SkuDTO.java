package com.holderzone.saas.store.dto.item.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class SkuDTO {

    @ApiModelProperty(value = "规格GUID")
    private String guid;

    @ApiModelProperty(value = "企业GUID")
    private String enterpriseGuid;

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "关联的门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "商品GUID")
    private String itemGuid;

    @ApiModelProperty(value = "类型GUID 冗余小程序字段")
    private String typeGuid;

    @ApiModelProperty(value = "upc商品条码")
    private String upc;

    @ApiModelProperty(value = "当日库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "总库存")
    private BigDecimal totalStock;

    @ApiModelProperty(value = "规格名称(固定规格名称为空字符串\"\")")
    private String name;

    @ApiModelProperty(value = "编号")
    private String code;

    @ApiModelProperty(value = "售卖价格")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "成本价格")
    private BigDecimal  costPrice;

    @ApiModelProperty(value = "会员价格")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "虚拟价格（冗余小程序端字段）")
    private BigDecimal virtualPrice;

    @ApiModelProperty(value = "商品规格单位")
    private String unit;

    @ApiModelProperty(value = "称重单位对应的编号")
    private Integer unitCode;

    @ApiModelProperty(value = "起卖数(非称重即为整数，称重即为小数)")
    private BigDecimal minOrderNum;

    @ApiModelProperty(value = "是否参与会员折扣（0：否，1：是）")
    private Integer isMemberDiscount;

    @ApiModelProperty(value = "是否加入整单折扣(0：否，1：是)")
    private Integer isWholeDiscount;

    @ApiModelProperty(value = "是否上架(0：否，1：是)")
    private Integer isRack;

    @ApiModelProperty(value = "是否开启库存（0：否 ， 1：是）")
    private Integer isOpenStock;

    @ApiModelProperty(value = "是否上架POS机（0：否，1：是）")
    private Integer isJoinPos;

    @ApiModelProperty(value = "是否上架一体机（0：否，1：是）")
    private Integer isJoinAio;

    @ApiModelProperty(value = "是否参与自助点餐机（0：否，1：是）")
    private Integer isJoinBuffet;

    @ApiModelProperty(value = "是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;

    @ApiModelProperty(value = "是否参与美团外卖（0：否，1：是）")
    private Integer isJoinMt;

    @ApiModelProperty(value = "是否参与微信点餐（0：否，1：是）")
    private Integer isJoinWeChat;

    @ApiModelProperty(value = "是否参与小程序商城（0：否，1：是） （冗余小程序端字段）")
    private Integer isJoinMiniAppMall;

    @ApiModelProperty(value = "是否参与饿了么外卖（0：否，1：是）")
    private Integer isJoinElm;

    @ApiModelProperty(value = "是否支持堂食（0：否，1：是） （冗余小程序端字段）")
    private Integer isJoinStore;

    @ApiModelProperty(value = "是否参与小程序外卖（0：否，1：是） （冗余小程序端字段）")
    private Integer isJoinMiniAppTakeaway;

    @ApiModelProperty(value = "饿了么sku")
    private String elmSku;

    @ApiModelProperty(value = "美团sku")
    private String mtSku;

    @ApiModelProperty(value = "品牌库对应的SKUGUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的商品，则该字段为原skuGUID。")
    private String parentGuid;

    @ApiModelProperty(value = "规格来源（0：门店自己创建的规格，1：品牌自己创建的规格,2:被推送过来的规格）")
    private Integer skuFrom;

    @ApiModelProperty(value = "外卖打包费（冗余小程序端字段）")
    private BigDecimal takeawayPackageFee;

    @ApiModelProperty(value = "商城打包费（冗余小程序端字段）")
    private BigDecimal mallPackageFee;

    @ApiModelProperty(value = "当前规格起售数量（冗余小程序端字段）")
    private Integer minSale;

    @ApiModelProperty(value = "当前规格当日最大库存（冗余小程序端字段）")
    private Integer maxQuantity;

    @ApiModelProperty(value = "是否自动置满库存（冗余小程序端字段）")
    private Boolean isFull;

    @ApiModelProperty(value = "置满时间（冗余小程序端字段）")
    private Integer fullTime;

    @ApiModelProperty(value = "小程序端折扣（冗余小程序端字段）")
    private Double discount;


    @ApiModelProperty(value = "月销售量（冗余小程序端字段）")
    private Integer monthlySale;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "更改时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;
}
