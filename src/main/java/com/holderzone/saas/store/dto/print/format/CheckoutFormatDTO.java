/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:CheckoutFormatDTO.java
 * Date:2019-12-5
 * Author:terry
 */

package com.holderzone.saas.store.dto.print.format;

import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CheckoutFormatDTO extends FormatDTO {

    @NotNull(message = "门店名称不能为空")
    @ApiModelProperty(value = "门店名称", required = true)
    private FormatMetadata storeName;

    @NotNull(message = "单据类型不能为空")
    @ApiModelProperty(value = "单据类型", required = true)
    private FormatMetadata invoiceName;

    @NotNull(message = "整单备注不能为空")
    @ApiModelProperty(value = "整单备注", required = true)
    private FormatMetadata orderRemark;

    @NotNull(message = "牌号/桌位不能为空")
    @ApiModelProperty(value = "牌号/桌位", required = true)
    private FormatMetadata markNo;

    @NotNull(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    private FormatMetadata orderNo;

    @NotNull(message = "就餐人数不能为空")
    @ApiModelProperty(value = "就餐人数", required = true)
    private FormatMetadata personNumber;

    @NotNull(message = "开台/下单时间不能为空")
    @ApiModelProperty(value = "开台/下单时间", required = true)
    private FormatMetadata openTableTime;

    @NotNull(message = "结帐时间不能为空")
    @ApiModelProperty(value = "结帐时间", required = true)
    private FormatMetadata checkoutTime;

    @NotNull(message = "商品组件不能为空")
    @ApiModelProperty(value = "商品组件", required = true)
    private FormatMetadata itemLayout;

    @NotNull(message = "商品单价不能为空")
    @ApiModelProperty(value = "商品单价", required = true)
    private FormatMetadata itemPrice;

    @NotNull(message = "商品小计不能为空")
    @ApiModelProperty(value = "商品小计", required = true)
    private FormatMetadata itemTotal;

    @ApiModelProperty(value = "商品折后小计", required = true)
    private FormatMetadata itemDiscountAfterTotal;

    @NotNull(message = "分类小计不能为空")
    @ApiModelProperty(value = "分类小计", required = true)
    private FormatMetadata typeTotal;

    @NotNull(message = "商品属性不能为空")
    @ApiModelProperty(value = "商品属性", required = true)
    private FormatMetadata itemProperty;

    @NotNull(message = "商品备注不能为空")
    @ApiModelProperty(value = "商品备注", required = true)
    private FormatMetadata itemRemark;

    @NotNull(message = "商品总计不能为空")
    @ApiModelProperty(value = "商品总计", required = true)
    private FormatMetadata itemSumTotal;

    @NotNull(message = "附加费明细不能为空")
    @ApiModelProperty(value = "附加费明细", required = true)
    private FormatMetadata additionalCharge;

    @NotNull(message = "附加费合计不能为空")
    @ApiModelProperty(value = "附加费合计", required = true)
    private FormatMetadata additionalChargeTotal;

    @NotNull(message = "优惠明细不能为空")
    @ApiModelProperty(value = "优惠明细", required = true)
    private FormatMetadata reduceRecord;

    @NotNull(message = "优惠合计不能为空")
    @ApiModelProperty(value = "优惠合计", required = true)
    private FormatMetadata reduceRecordTotal;

    @NotNull(message = "应付金额不能为空")
    @ApiModelProperty(value = "应付金额", required = true)
    private FormatMetadata payableMoney;

    @NotNull(message = "实付明细不能为空")
    @ApiModelProperty(value = "实付明细", required = true)
    private FormatMetadata paidMoneyRecord;

    @NotNull(message = "找零不能为空")
    @ApiModelProperty(value = "找零", required = true)
    private FormatMetadata paidMoneyChanged;

    @NotNull(message = "实付金额不能为空")
    @ApiModelProperty(value = "实付金额", required = true)
    private FormatMetadata actuallyPayMoney;

    @NotNull(message = "会员姓名")
    @ApiModelProperty(value = "会员姓名", required = true)
    private FormatMetadata memberName;

    @NotNull(message = "手机号不能为空")
    @ApiModelProperty(value = "会员手机号", required = true)
    private FormatMetadata memberPhone;

    @NotNull(message = "支付卡余额")
    @ApiModelProperty(value = "支付卡余额", required = true)
    private FormatMetadata memberCardBalance;

    @ApiModelProperty(value = "本次消费余额类型", required = true)
    private FormatMetadata memberCardBalanceType;

    @NotNull(message = "挂账单位名称")
    @ApiModelProperty(value = "挂账单位名称", required = true)
    private FormatMetadata debtUnitName;

    @NotNull(message = "挂账联系人")
    @ApiModelProperty(value = "挂账联系人", required = true)
    private FormatMetadata debtContactName;

    @NotNull(message = "挂账单位电话")
    @ApiModelProperty(value = "挂账单位电话", required = true)
    private FormatMetadata debtContactTel;

    @NotNull(message = "操作员不能为空")
    @ApiModelProperty(value = "操作员", required = true)
    private FormatMetadata operator;

    @NotNull(message = "打印时间不能为空")
    @ApiModelProperty(value = "打印时间", required = true)
    private FormatMetadata printTime;

    @ApiModelProperty(value = "开票二维码", required = true)
    private FormatMetadata invoiceQrCode;

    @ApiModelProperty(value = "扫码开票", required = true)
    private FormatMetadata invoiceCodeName;

    @ApiModelProperty(value = "扫码提示语句", required = true)
    private FormatMetadata invoiceMarkedWords;

    @ApiModelProperty(value = "开票金额", required = true)
    private FormatMetadata invoiceAmount;

    @ApiModelProperty(value = "会员优惠差异金额", required = true)
    private FormatMetadata originalPrice;

    @ApiModelProperty(value = "套餐子菜品显示制作数量", required = true)
    private FormatMetadata showMakeNum;

    @NotNull(message = "年月日不能为空")
    @ApiModelProperty(value = "年月日", required = true)
    private FormatMetadata yearMonthDay;

    @ApiModelProperty(value = "赠送菜品金额", required = true)
    private FormatMetadata reduceDishAmount;

    @Override
    public void applyDefault() {
        super.applyDefault();
        defaultOrderFormatMetadata();
        defaultItemFormatMetadata();
        defaultOrderFeeFormatMetadata();
        defaultMemberFormatMetadata();
        defaultInvoiceFormatMetadata();
        defaultDebtFormatMetadata();
    }

    private void defaultOrderFormatMetadata() {
        if (storeName == null) {
            storeName = new FormatMetadata(2, 1, false);
        }
        if (invoiceName == null) {
            invoiceName = new FormatMetadata(2, 1, false);
        }
        if (orderRemark == null) {
            orderRemark = new FormatMetadata(2, 1, false, false);
        }
        if (markNo == null) {
            markNo = new FormatMetadata(1, 0, false);
        }
        if (orderNo == null) {
            orderNo = new FormatMetadata(1, 0, false);
        }
        if (personNumber == null) {
            personNumber = new FormatMetadata(1, 2, false);
        }
        if (openTableTime == null) {
            openTableTime = new FormatMetadata(1, 0, false);
        }
        if (checkoutTime == null) {
            checkoutTime = new FormatMetadata(1, 2, false);
        }
        if (operator == null) {
            operator = new FormatMetadata(1, 0, false);
        }
        if (printTime == null) {
            printTime = new FormatMetadata(1, 2, false);
        }
        if (yearMonthDay == null) {
            yearMonthDay = new FormatMetadata(0, 0, false, false);
        }
    }

    private void defaultItemFormatMetadata() {
        if (itemLayout == null) {
            itemLayout = new FormatMetadata(1, 2, 0, false);
        }
        if (itemPrice == null) {
            itemPrice = new FormatMetadata(1, 0, false);
        }
        if (itemTotal == null) {
            itemTotal = new FormatMetadata(1, 0, false);
        }
        if (itemDiscountAfterTotal == null) {
            itemDiscountAfterTotal = new FormatMetadata(1, 0, false, false);
        }
        if (typeTotal == null) {
            typeTotal = new FormatMetadata(1, 0, false, false);
        }
        if (itemProperty == null) {
            itemProperty = new FormatMetadata(1, 0, false);
        }
        if (itemRemark == null) {
            itemRemark = new FormatMetadata(1, 0, false);
        }
        if (itemSumTotal == null) {
            itemSumTotal = new FormatMetadata(1, 0, false);
        }
    }


    private void defaultOrderFeeFormatMetadata() {
        if (additionalCharge == null) {
            additionalCharge = new FormatMetadata(1, 3, false);
        }
        if (additionalChargeTotal == null) {
            additionalChargeTotal = new FormatMetadata(1, 3, false);
        }
        if (reduceRecord == null) {
            reduceRecord = new FormatMetadata(1, 3, false);
        }
        if (reduceRecordTotal == null) {
            reduceRecordTotal = new FormatMetadata(1, 3, false);
        }
        if (reduceDishAmount == null) {
            reduceDishAmount = new FormatMetadata(1, 3, false, true);
        }
        if (payableMoney == null) {
            payableMoney = new FormatMetadata(2, 0, false);
        }
        if (paidMoneyRecord == null) {
            paidMoneyRecord = new FormatMetadata(1, 3, false);
        }
        if (paidMoneyChanged == null) {
            paidMoneyChanged = new FormatMetadata(1, 3, false);
        }
        if (actuallyPayMoney == null) {
            actuallyPayMoney = new FormatMetadata(2, 0, false);
        }
    }

    private void defaultMemberFormatMetadata() {
        if (memberName == null) {
            memberName = new FormatMetadata(1, 0, false, false);
        }
        if (memberPhone == null) {
            memberPhone = new FormatMetadata(1, 0, false, false);
        }
        if (memberCardBalance == null) {
            memberCardBalance = new FormatMetadata(1, 0, false, false);
        }
        if (memberCardBalanceType == null) {
            memberCardBalanceType = new FormatMetadata(1, 0, false, false);
        }
    }

    private void defaultDebtFormatMetadata() {
        if (debtUnitName == null) {
            debtUnitName = new FormatMetadata(1, 0, false, false);
        }
        if (debtContactName == null) {
            debtContactName = new FormatMetadata(1, 0, false, false);
        }
        if (debtContactTel == null) {
            debtContactTel = new FormatMetadata(1, 0, false, false);
        }
    }

    private void defaultInvoiceFormatMetadata() {
        if (invoiceQrCode == null) {
            invoiceQrCode = new FormatMetadata(1, 1, false);
        }
        if (invoiceCodeName == null) {
            invoiceCodeName = new FormatMetadata(1, 1, true);
        }
        if (invoiceMarkedWords == null) {
            invoiceMarkedWords = new FormatMetadata(1, 1, true);
        }
        if (originalPrice == null) {
            originalPrice = new FormatMetadata(1, 3, false);
        }
        if (invoiceAmount == null) {
            invoiceAmount = new FormatMetadata(1, 1, true);
        }
        if (showMakeNum == null) {
            showMakeNum = new FormatMetadata(1, 1, false, false);
        }
    }
}
