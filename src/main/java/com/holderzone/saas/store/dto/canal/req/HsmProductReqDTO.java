package com.holderzone.saas.store.dto.canal.req;

import com.holderzone.saas.store.constant.member.MemberSyncConstant;
import com.holderzone.saas.store.enums.canal.MemberSynEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ProductListReqDTO
 * @date 2019/06/27 18:21
 * @description //TODO 批量同步商品请求DTO
 * @program holder-saas-aggregation-app
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HsmProductReqDTO {

    /**
     * 同步操作类型::同步功能使用
     */
    @ApiModelProperty(value = "同步操作类型::同步功能使用")
    private MemberSynEnum sycEnum;

    /**
     * 商品信息GUID
     */
    @ApiModelProperty(value = "商品信息GUID")
    private String guid;

    /**
     * 外部商品主键
     */
    @NotNull(message = "外部商品主键为空")
    @ApiModelProperty(value = "外部商品主键", required = true)
    private String productKey;

    /**
     * 联盟ID
     */
    @NotNull(message = "联盟ID为空")
    @ApiModelProperty(value = "联盟ID", required = true)
    private String allianceid;

    /**
     * 企业GUID
     */
    @ApiModelProperty(value = "企业GUID",hidden = true)
    private String enterpriseGuid;

    /**
     * 外部商家主键
     */
    @NotNull(message = "外部商家主键为空")
    @ApiModelProperty(value = "外部商家主键", required = true)
    private String enterpriseKey;

    /**
     * 品牌GUID
     */
    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    /**
     * 外部商家对应的品牌主键
     */
    @ApiModelProperty(value = "外部商家对应的品牌主键")
    private String brandKey;

    /**
     * 门店GUID
     */
    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    /**
     * 外部门店主键
     */
    @NotNull(message = "外部门店主键为空")
    @ApiModelProperty(value = "外部门店主键", required = true)
    private String storeKey;

    /**
     * 商品名称
     */
    @NotNull(message = "商品名称为空")
    @ApiModelProperty(value = "商品名称", required = true)
    private String productName;

    /**
     * 商品分类Guid
     */
    @ApiModelProperty(value = "商品分类Guid")
    private String classificationGuid;
    /**
     * 商品分类编码
     */
    @ApiModelProperty(value = "商品分类编码")
    private String classificationCode;

    /**
     * 商品分类名称
     */
    @ApiModelProperty(value = "商品分类名称")
    private String classificationName;

    /**
     * 商品编号
     */
    @ApiModelProperty(value = "商品编号")
    private String productNum;

    /**
     * 商品类型,0套餐，1单品
     */
    @ApiModelProperty(value = "商品类型 1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品。")
    private Integer productType;

    @ApiModelProperty(value = "商品规格名称")
    private String productSpecName;

    @ApiModelProperty(value = "商品规格Key")
    private String productSpecKey;

    @ApiModelProperty(value = "商品规格Guid")
    private String productSpecGuid;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal productPrice;

    /**
     * 图片路径数组json
     */
    @ApiModelProperty(value = "图片路径")
    private String pictureUrlJson;

    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 是否热销\新品\招牌 0普通 1新品 2热销 3招牌 4新品+热销 5新品+招牌 6热销+招牌 7新品+热销+招牌
     */
    @ApiModelProperty(value = "是否属于热销\\新品\\招牌=> [0普通 1新品 2热销 3招牌 4新品+热销 5新品+招牌 6热销+招牌 7新品+热销+招牌]")
    private int isHot;
    /**
     * 是否可以微信点餐
     */
    @ApiModelProperty(value = "是否可以微信点餐 0否 1是")
    private int isJoinWeChat;

    /**
     *  状态,0禁用,1启用
     */
    @ApiModelProperty(value = "状态,0禁用,1启用")
    @NotNull(message = "启用状态不能为空")
    private int isEnable;

    @ApiModelProperty(value = "状态,0禁用,1启用")
    @NotNull(message = "单位不能为空")
    private  String  productUnit;

}
