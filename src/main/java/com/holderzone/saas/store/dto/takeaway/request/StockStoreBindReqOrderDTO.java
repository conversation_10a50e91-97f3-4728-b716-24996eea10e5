package com.holderzone.saas.store.dto.takeaway.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StockStoreBindReqOrderDTO implements Serializable {

    private static final long serialVersionUID = -7820935209083353029L;

    @ApiModelProperty(value = "门店guid")
    @NotNull
    private String storeGuid;

    @ApiModelProperty(value = "库存绑定门店")
    @NotNull
    private String branchStoreGuid;

    @ApiModelProperty(value = "订单编号")
    @NotNull
    private String orderId;
}
