package com.holderzone.saas.store.dto.order.request.waiter;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.framework.util.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR> R
 * @date 2020/11/26 9:32
 * @description
 */
@Data
@ApiModel(value = "订单服务员分页查询请求参数DTO")
public class OrderWaiterPageReqDTO extends Page {
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "开始日期时间 yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "开始日期时间不能为空")
    private LocalDateTime startDateTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "营业结束日期时间 yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "结束日期时间不能为空")
    private LocalDateTime endDateTime;

    @NotNull(message = "状态不能为空")
    @ApiModelProperty(value = "订单录入服务员状态（未录入：true 查询全部：false）默认查询未录入：true")
    private Boolean inputStatus;
}
