package com.holderzone.saas.store.dto.map;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GeoCodeRespDTO
 * @date 2019/05/18 17:12
 * @description 地理编码响应DTO
 * @program holder-saas-store
 */
@Data
@ApiModel("地理编码响应DTO")
@AllArgsConstructor
@NoArgsConstructor
public class GeoCodeRespDTO extends MapCommonRespDTO {

    @JsonProperty(value = "geocodes")
    private List<GeoCodeDTO> geoCodeDTOList;
}
