package com.holderzone.saas.store.dto.journaling.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessSituationReqDTO
 * @date 2019/05/29 15:35
 * @description 营业概况requestDTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class BusinessSituationReqDTO extends JournalAppBaseReqDTO {

    private static final long serialVersionUID = 2439494246229750106L;

    @ApiModelProperty(value = "请求来源，0：商户后台，1：app")
    private String dataSource;

    @ApiModelProperty(value = "操作人guid集合")
    private List<String> createStaffGuidList;
}
