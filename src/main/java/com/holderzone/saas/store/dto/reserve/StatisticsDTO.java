package com.holderzone.saas.store.dto.reserve;

import com.holderzone.saas.store.dto.common.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PhoneDTO
 * @date 2019/04/29 17:35
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsDTO extends BaseDTO {

    private Long total;

    private Long act;

    private Long cancle;
}