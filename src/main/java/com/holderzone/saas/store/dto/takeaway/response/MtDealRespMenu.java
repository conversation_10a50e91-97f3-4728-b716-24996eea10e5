package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
public class MtDealRespMenu implements Serializable {

    @ApiModelProperty(value = "字段内容")
    private String content;

    @ApiModelProperty(value = "数量/规格")
    private String price;

    @ApiModelProperty(value = "单价")
    private String specification;

    @ApiModelProperty(value = "小计")
    private String total;

    @ApiModelProperty(value = "区别表头和行。0代表表头，128代表行")
    private String type;

    @ApiModelProperty(value = "notDishes")
    private String notDishes;

    @ApiModelProperty(value = "Images")
    private String images;

    @ApiModelProperty(value = "desc")
    private String desc;

}
