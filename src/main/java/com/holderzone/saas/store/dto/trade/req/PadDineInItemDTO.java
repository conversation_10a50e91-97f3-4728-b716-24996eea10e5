package com.holderzone.saas.store.dto.trade.req;

import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PadDineInItemDTO
 * @date 2021/8/23 18:02
 * @description 订单商品DTO
 */
@Data
@Accessors(chain = true)
public class PadDineInItemDTO implements Serializable {

    private static final long serialVersionUID = 6370995831714048630L;

    @ApiModelProperty(value = "商品guid", required = true)
    private String itemGuid;

    @ApiModelProperty(value = "商品名称", required = true)
    private String itemName;

    @ApiModelProperty(value = "商品类型(1.套餐主项，2.规格，3.称重，4.单品 5.团餐主项)", required = true)
    private Integer itemType;

    @ApiModelProperty(value = "商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂，8.已上菜(已划菜) ，9.预定)")
    private Integer itemState=1;

    @ApiModelProperty(value = "商品分类guid", required = true)
    private String itemTypeGuid;

    @ApiModelProperty(value = "商品分类名称", required = true)
    private String itemTypeName;

    @ApiModelProperty(value = "规格guid", required = true)
    private String skuGuid;

    @ApiModelProperty(value = "规格名称", required = true)
    private String skuName;

    @ApiModelProperty(value = "sku价格", required = true)
    private BigDecimal price;

    @ApiModelProperty(value = "会员价格")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "数量", required = true)
    private BigDecimal currentCount;

    @ApiModelProperty(value = "计数单位", required = true)
    private String unit;

    @ApiModelProperty(value = "套餐主项guid")
    private Long parentItemGuid;

    @ApiModelProperty(value = "起买数")
    private BigDecimal minOrderNum;

    @ApiModelProperty(value = "套餐分组")
    private List<PackageSubgroupDTO> packageSubgroupDTOS;

    @ApiModelProperty(value = "商品属性")
    private List<ItemAttrDTO> itemAttrDTOS;

    /**
     * 小图
     */
    @ApiModelProperty(value = "商品图片地址，小图")
    private String smallPicture;

    /**
     * see {@link com.holderzone.saas.store.enums.order.ItemPriceChangeEnum}
     */
    @ApiModelProperty(value = "改价类型（0-未改价，1-已改价，2-商品折扣）", required = false)
    private Integer priceChangeType;

}
