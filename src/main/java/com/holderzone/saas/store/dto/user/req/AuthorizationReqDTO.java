package com.holderzone.saas.store.dto.user.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@Accessors(chain = true)
public class AuthorizationReqDTO {

    @NotBlank
    @ApiModelProperty("资源code")
    private String sourceCode;

    @NotBlank
    @ApiModelProperty("权限code")
    private String authorityCode;

    @ApiModelProperty("被授权人guid")
    private String userGuid;

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public AuthorizationReqDTO() {
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getAuthorityCode() {
        return authorityCode;
    }

    public void setAuthorityCode(String authorityCode) {
        this.authorityCode = authorityCode;
    }

    @Override
    public String toString() {
        return "AuthorizationReqDTO{" +
                "sourceCode='" + sourceCode + '\'' +
                ", authorityCode='" + authorityCode + '\'' +
                '}';
    }
}
