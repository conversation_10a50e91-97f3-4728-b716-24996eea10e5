package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TagPageQueryDTO
 * @date 2019/12/05 14:35
 * @description //TODO
 * @program IdeaProjects
 */
@Data
public class TableTagPageQueryDTO extends PageDTO {

    @ApiModelProperty("门店 GUID ")
    @NotBlank
    private String storeGuid;

}