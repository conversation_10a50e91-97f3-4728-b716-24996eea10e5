package com.holderzone.saas.store.dto.user.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 员工简要信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2023/9/7
 * @since 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserBriefDTO {

    @ApiModelProperty(value = "员工guid")
    private String userGuid;

    @ApiModelProperty(value = "员工姓名")
    private String userName;

    @ApiModelProperty(value = "员工账号")
    private String account;

    public UserBriefDTO(String userGuid, String userName) {
        this.userGuid = userGuid;
        this.userName = userName;
    }
}
