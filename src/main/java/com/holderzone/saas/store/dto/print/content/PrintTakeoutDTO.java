package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

import static com.holderzone.saas.store.dto.print.util.PrintMockUtils.*;

/**
 * 外卖单
 *
 * <AUTHOR>
 * @date 2018/09/19 16:36
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "外卖单")
public class PrintTakeoutDTO extends PrintBaseItemDTO implements PrintDataMockito {

    @NotBlank(message = "平台名称不能为空")
    @ApiModelProperty(value = "平台名称", required = true)
    private String platform;

    @NotBlank(message = "外卖平台原单号不能为空")
    @ApiModelProperty(value = "外卖平台原单号", required = true)
    private String platformOrder;

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private String storeName;

    @ApiModelProperty(value = "是否取消", required = true)
    private Boolean cancelFlag;

    @NotBlank(message = "支付状态描述不得为空")
    @ApiModelProperty(value = "支付状态描述", required = true)
    private String payMsg;

    @NotBlank(message = "期望送达时间不得为空")
    @ApiModelProperty(value = "期望送达时间", required = true)
    private String expectTime;

    @NotNull(message = "下单时间不能为空")
    @ApiModelProperty(value = "下单时间", required = true)
    private Long orderTime;

    @NotBlank(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    private String orderNo;

    @ApiModelProperty(value = "是否是预定单：0=否，1=是", required = true)
    private Boolean reserve;

    @ApiModelProperty(value = "出餐码")
    private String foodFinishBarCode;

    @Nullable
    @ApiModelProperty(value = "备注", notes = "可以没有备注，因此该值可以为空")
    private String remark;

    @NotNull(message = "商品总额不能为空")
    @ApiModelProperty(value = "商品总额", required = true)
    private BigDecimal itemTotalPrice;

    @Valid
    @Nullable
    @ApiModelProperty(value = "附加费")
    private List<AdditionalCharge> additionalChargeList;

    @Valid
    @Nullable
    @ApiModelProperty(value = "优惠折扣")
    private List<ReduceRecord> reduceRecordList;

    @NotNull(message = "原价")
    @ApiModelProperty(value = "原价", required = true)
    private BigDecimal originalPrice;

    @NotNull(message = "实付金额")
    @ApiModelProperty(value = "实付金额", required = true)
    private BigDecimal actuallyPay;

    @NotBlank(message = "客户姓名不能为空")
    @ApiModelProperty(value = "客户姓名", required = true)
    private String receiverName;

    @ApiModelProperty(value = "客户电话", required = true)
    private String receiverTel;

    @ApiModelProperty(value = "客户隐私号", required = true)
    private String privacyPhone;

    @NotBlank(message = "客户地址不能为空")
    @ApiModelProperty(value = "客户地址", required = true)
    private String receiverAddress;

    /**
     * 此字段不会包含recipientAddress字段中@#后面的值
     */
    // @NotBlank(message = "隐私地址不能为空")
    @ApiModelProperty(value = "隐私地址", required = true)
    private String recipientAddressDesensitization;

    @NotNull(message = "是否是异常单不得为空")
    @ApiModelProperty(value = "是否是异常单", required = true)
    private Boolean abnormal;

    @ApiModelProperty(value = "是否需要发票(0=不需要发票，1=需要发票)")
    private Boolean invoiced;

    @ApiModelProperty(value = "发票抬头")
    private String invoiceTitle;

    @ApiModelProperty(value = "纳税人身份证明")
    private String taxpayerId;

    @ApiModelProperty(value = "是否需要餐具")
    private String dinnersNumber;

    @ApiModelProperty(value = "是否自动接单")
    private Boolean isAutoAccept;

    @ApiModelProperty(value = "满赠信息")
    private String fullGiftRemark;

    @Override
    public void applyMock() {
        super.applyMock();
        setPlatform(MOCK_TAKEOUT_PLATFORM);
        setPlatformOrder(MOCK_TAKEOUT_PLATFORM_ORDER);
        setStoreName(mockStoreName());
        setPayMsg(MOCK_TAKEOUT_PAY_MSG);
        setExpectTime(MOCK_TAKEOUT_EXPECT_TIME);
        setOrderTime(mockCheckoutTime());
        setOrderNo(mockOrderNo());
        setFoodFinishBarCode(mockFoodFinishBarCode());
        setRemark(MOCK_TAKEOUT_REMARK);
        setItemRecordList(mockTakeoutItemRecords());
        setItemTotalPrice(MOCK_TAKEOUT_ITEM_TOTAL_PRICE);
        setAdditionalChargeList(mockTakeoutAdditionalChargeList());
        setReduceRecordList(mockTakeoutReduceRecordList());
        setOriginalPrice(MOCK_TAKEOUT_ORIGINAL_PRICE);
        setActuallyPay(MOCK_TAKEOUT_ACTUALLY_PAY);
        setReceiverName(MOCK_TAKEOUT_RECEIVER_NAME);
        setReceiverTel(MOCK_TAKEOUT_RECEIVER_TEL);
        setPrivacyPhone(MOCK_TAKEOUT_RECEIVER_TEL);
        setReceiverAddress(MOCK_TAKEOUT_RECEIVER_ADDRESS);
        setAbnormal(false);
        setInvoiced(false);
        setIsAutoAccept(true);
        setFullGiftRemark(MOCK_TAKEOUT_FULL_GIFT);
    }
}
