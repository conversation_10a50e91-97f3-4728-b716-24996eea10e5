package com.holderzone.saas.store.dto.report;


import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "收入概览")
@NoArgsConstructor
public class IndexInComeOverviewDTO {

    @ApiModelProperty(value = "营业总收入（元）")

    private BigDecimal totalMoney;

    @ApiModelProperty(value = "营业总收入构成")
    private List<ReportBaseDTO> businessList = Lists.newArrayList();
    @ApiModelProperty(value = "销售总收入构成-顾客类型（元）")
    private List<ReportBaseDTO> sellList = Lists.newArrayList();
    @ApiModelProperty(value = "营业总收入构成")
    private List<ReportBaseDTO> sellTypeList = Lists.newArrayList();
}
