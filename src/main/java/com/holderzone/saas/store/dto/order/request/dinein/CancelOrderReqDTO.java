package com.holderzone.saas.store.dto.order.request.dinein;

import com.holderzone.saas.store.dto.table.BaseTableDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CancelOrderReqDTO
 * @date 2019/01/21 15:36
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class CancelOrderReqDTO extends BaseTableDTO {

    @ApiModelProperty(value = "作废原因")
    private String reason;

    @ApiModelProperty(value = "是否桌台")
    private boolean table;

    @ApiModelProperty(value = "是否快餐")
    private boolean fastFood;

    @ApiModelProperty(value = "插入计算作废数据")
    private Integer isCalc;

    @ApiModelProperty(value = "是否结账整单（用于多单结账，作废最后一笔单子）")
    private Boolean checkoutOrderFlag;

}
