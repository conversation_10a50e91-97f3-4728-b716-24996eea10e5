package com.holderzone.saas.store.dto.order.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderLogListRespDTO
 * @date 2018/10/08 20:55
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class OrderLogListRespDTO {
    @ApiModelProperty(value = "订单号")
    private String orderNum;
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;
    @ApiModelProperty(value = "门店名称")
    private String storeName;
    @ApiModelProperty(value = "交易模式编码：0:堂食,1:快餐,2:外卖,")

    private Integer tradeMode;
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;


    @ApiModelProperty(value = "交易模式名称")

    private String tradeModeDesc;

    @ApiModelProperty(value = "订单日志业务主键")
    private String orderLogGuid;

    @ApiModelProperty(value = "状态(0：未完成， 1：已完成， 2：已作废 ，3：已退款，4：反结账状态")
    private Integer state;

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getTradeMode() {
        return tradeMode;
    }

    public void setTradeMode(Integer tradeMode) {
        this.tradeMode = tradeMode;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getTradeModeDesc() {
        return tradeModeDesc;
    }

    public void setTradeModeDesc(String tradeModeDesc) {
        this.tradeModeDesc = tradeModeDesc;
    }

    public String getOrderLogGuid() {
        return orderLogGuid;
    }

    public void setOrderLogGuid(String orderLogGuid) {
        this.orderLogGuid = orderLogGuid;
    }
}
