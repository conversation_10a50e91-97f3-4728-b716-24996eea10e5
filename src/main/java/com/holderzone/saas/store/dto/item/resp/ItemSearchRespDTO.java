package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ItemSearchRespDTO {

    @ApiModelProperty(value = "商品Guid",required = true)
    private String guid;

    @ApiModelProperty("商品名称")
    private String name;

    @ApiModelProperty("商品备注")
    private String remark;

    @ApiModelProperty("图文详情")
    private String remarkDetail;

    @ApiModelProperty("商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品 5.团餐。")
    private Integer itemType;

    @ApiModelProperty("好评数")
    private Integer upCount;

    @ApiModelProperty("差评数")
    private Integer downCount;

    @ApiModelProperty("会员价格")
    private BigDecimal memberPrice;

    @ApiModelProperty("虚拟价格")
    private BigDecimal virtualPrice;

    @ApiModelProperty("售卖价格")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "商品主图路径")
    private String pictureUrl;

    @ApiModelProperty(value = "视频url json数组")
    private String videoUrls;
}
