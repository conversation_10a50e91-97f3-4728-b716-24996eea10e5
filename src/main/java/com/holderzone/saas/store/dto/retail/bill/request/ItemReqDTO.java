package com.holderzone.saas.store.dto.retail.bill.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemReqDTO
 * @date 2019/11/02 16:55
 * @description //TODO
 * @program IdeaProjects
 */
@Data
public class ItemReqDTO {
    @ApiModelProperty(value = "itemGuid传订单中的itemGuid")
    private Long itemGuid;

    @ApiModelProperty(value = "退货数量")
    private BigDecimal count;

    @ApiModelProperty(value = "是否是免费商品")
    private Boolean isFree;
}