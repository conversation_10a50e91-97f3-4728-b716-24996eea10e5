package com.holderzone.saas.store.dto.trade;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单优惠记录
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class DiscountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private String guid;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    private Integer isDelete;

    /**
     * 订单guid
     */
    private String orderGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 订单商品guid
     */
    private String orderItemGuid;

    /**
     * 优惠券guid
     */
    private String couponsGuid;

    /**
     * 折扣方式名字
     */
    private String discountName;

    /**
     * {@link DiscountTypeEnum}
     * '1-会员折扣，2-整单折扣，3-整单让价,4-系统省零，5-赠送优惠，6-团购验券，7-会员优惠券，8-积分抵扣'
     */
    private Integer discountType;

    /**
     * 打几折（整单折扣和会员折扣）
     */
    private BigDecimal discount;

    /**
     * 系统省零规则（DiscountRuleBO）
     */
    private String rule;

    /**
     * 折扣总额
     */
    private BigDecimal discountFee;

    /**
     * 折扣状态 0表示正常，1表示是反结账折扣
     */
    private Integer discountState;

    /**
     * 操作人guid
     */
    private String staffGuid;

    /**
     * 操作人姓名
     */
    private String staffName;


}
