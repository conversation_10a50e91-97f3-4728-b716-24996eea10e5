package com.holderzone.saas.store.dto.journaling.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreGatherReportReqDTO
 * @date 2019/05/29 10:47
 * @description 门店汇总requestDTO
 * @program holder-saas-store-dto
 */
@Data @ApiModel("门店汇总requestDTO")
public class StoreGatherReportReqDTO extends JournalWebBaseReqDTO {

    private static final long serialVersionUID = 3450917072571289186L;

    @ApiModelProperty(value = "品牌guid列表")
    private List<String> brandGuids;

    @ApiModelProperty(value = "mysql find_in_set使用")
    private String storeGuidStr;
}
