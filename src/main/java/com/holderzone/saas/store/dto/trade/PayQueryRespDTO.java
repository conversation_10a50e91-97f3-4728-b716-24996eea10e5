package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayQueryRespDTO
 * @date 2018/09/10 11:10
 * @description 查询返回支付单详情实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class PayQueryRespDTO extends BaseBillDTO{

    /**
     * 支付的业务主键（对应聚合支付的orderGUID）
     */
    @ApiModelProperty(value = "支付的业务主键")
    private String paymentGuid;

    @ApiModelProperty(value = "对应聚合支付的orderGUID")
    private String jhOrderGuid;

    /**
     * 对应聚合支付的payGUID
     */
    @ApiModelProperty(value = "对应聚合支付的payGUID")
    private String payGuid;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal amount;
    /**
     * 第三方单号
     */
    @ApiModelProperty(value = "第三方单号，如果是银行卡支付或者其他支付方式，对应输入的流水号")
    private String thirdOrderNo;

    /**
     * 银行流水号
     */
    @ApiModelProperty(value = "银行流水号")
    private String bankTransactionId;

    /**
     * 折扣金额
     */
    @ApiModelProperty(value = "折扣金额")
    private BigDecimal discountFee;

    /**
     * 支付方式类型
     */
    @ApiModelProperty(value = "支付方式类型 0:现金支付 ，1:聚合支付 ，2:银行卡支付，3：会员卡支付")
    private Integer paymentType;

    /**
     * 支付方式名字
     */
    @ApiModelProperty(value = "如果是自定义支付，需要传入支付名字")
    private String paymentTypeName;

    @ApiModelProperty(value = " 0:订单正常结算，1:会员充值")
    private Integer tradingType;

    /**
     * 交易完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime paidTimestamp;

    /**
     * 支付单创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTimestamp;

    /**
     * 当会员登录时候需要，或者会员充值、支付需要
     */
    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    /**
     * 当会员充值相关需要
     */
    @ApiModelProperty(value = "会员电话")
    private String telPhoneNo;

    /**
     * 操作员工GUID
     */
    @ApiModelProperty(value = "操作员工GUID")
    private String operationStaffGuid;

    /**
     * 操作员工名字
     */
    @ApiModelProperty(value = "操作员工名字")
    private String operationStaffName;


    @ApiModelProperty(value = "10：待支付  0：富民支付中  1：支付中(已请求上游） 2：支付成功 3：支付失败 4：退款  5：已关闭  6 撤销 7：反结账状态 -1 删除")
    private Integer state;

    /**
     * 二维码链接
     */
    @ApiModelProperty(value = "二维码链接")
    private String codeUrl;

    /**
     * 备注
     */
    private String remark;

    @ApiModelProperty("本次订单交易的商品名称！！")
    private String goodsName;

    @ApiModelProperty(value = "对应订单的描述！！")
    private String body;

    @ApiModelProperty(value = "支付功能的id; 1:支付宝二维码支付(客户扫我们代理商返回给的二维码)、2:支付宝条码支付(扫码枪)、3:支付宝公众号支付、" +
            "8:微信二维码支付、9:微信条码支付、10:微信公众号支付")
    private String payPowerId;

    @ApiModelProperty(value = "聚合支付支付功能id对应的name")
    private String payPowerName;

    @ApiModelProperty(value = "对应订单附加描述！!")
    private String description;

    @ApiModelProperty(value = "对应订单附加数据！！")
    private String extra;

    /**
     * 商户分发渠道（否）
     * 商户自定义来标记该笔订单属于的渠道方（支付平台会记录该标记，但不做任何处理）
     */
    @ApiModelProperty(value = "商户自定义来标记该笔订单属于的渠道方（支付平台会记录该标记，但不做任何处理）")
    private String cpChannel;

    /**
     * 设备终端号（否）
     * 发起条码支付时候必填
     */
    @ApiModelProperty(value = "设备终端号,微信、支付宝条码支付时候，此参数必填！！")
    private String terminalId;

}
