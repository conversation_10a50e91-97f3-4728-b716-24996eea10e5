package com.holderzone.saas.store.dto.kds.req;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.enums.kds.ScanFinishFoodTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ItemStateTransReqDTO extends BaseDTO {

    private static final long serialVersionUID = -1158994112223699540L;

    @Nullable
    @ApiModelProperty(value = "查询模式：0=按菜品汇总，1=按订单")
    private Integer queryModeForPoint;

    /**
     * 订单类型
     * @see ScanFinishFoodTypeEnum
     */
    @ApiModelProperty(value = "订单类型")
    private Integer orderDifference;

    @ApiModelProperty(value = "订单牌号")
    private String orderDesc;

    @Valid
    @NotEmpty(message = "商品列表不得为空")
    @ApiModelProperty(value = "商品列表", required = true)
    private List<PrdDstItemDTO> prdDstItemList;
}
