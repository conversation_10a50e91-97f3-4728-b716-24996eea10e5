package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 外卖模块设置
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TakeoutConfigDTO extends BaseDTO {

    @NotNull(message = "是否自动接单", groups = SetAuto.class)
    @ApiModelProperty(value = "是否自动接单")
    @JsonProperty("isAutomaticAcceptOrder")
    private Boolean autoOrder;

    /**
     * 是否打印外卖取消小票：0=否，1=是
     */
    @ApiModelProperty(value = "是否打印外卖取消小票")
    @JsonProperty("isPrintCancelOrder")
    private Boolean printCancelOrder;

    public interface SetAuto {
    }

    public interface GetAuto {
    }
}
