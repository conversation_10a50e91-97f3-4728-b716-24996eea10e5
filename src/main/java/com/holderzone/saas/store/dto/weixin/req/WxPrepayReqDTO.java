package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("买单支付入参")
@Builder
public class WxPrepayReqDTO {

	@ApiModelProperty(required = true,value = "企业guid")
	private String enterpriseGuid;

	@ApiModelProperty(required = true,value = "品牌guid")
	private String brandGuid;

	@ApiModelProperty(required = true,value = "门店guid")
	private String storeGuid;

	@ApiModelProperty(required = true,value = "桌台guid")
	private String tableGuid;

	@ApiModelProperty(required = true,value = "用户id")
	private String openId;

	@ApiModelProperty(value = "支付金额",required = true)
	private BigDecimal payAmount;

	@ApiModelProperty("会员卡guid")
	private String memberCardGuid;

	@ApiModelProperty("卡的guid")
	private String cardGuid;

	@ApiModelProperty("会员持卡GUID")
	private String memberInfoCardGuid;

	@ApiModelProperty("卡体系GUID")
	private String systemManagementGuid;

	@ApiModelProperty(value = "是否计算积分1：计算，2：不计算")
	private Integer memberIntegral;

	@ApiModelProperty(value = "优惠券code")
	private String volumeCode;

	@ApiModelProperty(value = "订单id")
	private String orderGuid;

	@ApiModelProperty(value = "类型")
	private Integer mode;

	@ApiModelProperty(value = "")
	private BigDecimal orderFee;

	@ApiModelProperty(value = "会员guid")
	private String memberInfoGuid;

	@ApiModelProperty(value = "团购券优惠")
	private BigDecimal purchaseGroupFee;

	private Integer version;
}
