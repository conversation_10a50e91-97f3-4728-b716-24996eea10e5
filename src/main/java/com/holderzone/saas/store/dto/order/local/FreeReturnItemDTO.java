package com.holderzone.saas.store.dto.order.local;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 赠送/退货记录
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class FreeReturnItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    private String guid;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "开班时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    private Integer isDelete;

    /**
     * 订单商品guid
     */
    private String orderItemGuid;

    /**
     * 商品guid
     */
    private String itemGuid;

    /**
     * 商品类型(1.套餐主项，2.规格，3.称重，4.单品 )
     */
    private Integer itemType;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品的规格
     */
    private String skuGuid;

    /**
     * 规格名称
     */
    private String skuName;

    /**
     * sku价格
     */
    private BigDecimal price;

    /**
     * 菜品优惠合计
     */
    private BigDecimal totalDiscountFee;

    /**
     * 订单guid（反结账取新生成的）
     */
    private String orderGuid;

    /**
     * 类型（1：退货，2：赠送）
     */
    private Integer type;

    /**
     * 催品次数
     */
    private Integer urgeNum;

    /**
     * 商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂，8.已上菜(已划菜) ，9.预定)
     */
    private Integer itemState;

    /**
     * 标记退货是否为赠送（0：否，1：是）
     */
    private Integer isFree;

    /**
     * 原因
     */
    private String reason;

    /**
     * 数量
     */
    private BigDecimal count;

    /**
     * 赠送划菜数量
     */
    private BigDecimal serveCount;

    /**
     * 操作人guid
     */
    private String staffGuid;

    /**
     * 操作人名字
     */
    private String staffName;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;


}
