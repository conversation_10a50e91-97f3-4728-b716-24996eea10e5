package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateMenuAllSubItemReqDTO
 * @date 2019/06/01 16:35
 * @description //TODO 模板菜单添加sku商品按条件获取sku商品
 * @program holder-saas-aggregation-app
 */
@Data
@ApiModel(value = "模板菜单添加sku商品按条件获取sku商品DTO")
public class ItemTemplateMenuAllSubItemReqDTO extends BasePageDTO {

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 类型guid
     */
    @ApiModelProperty(value = "分类guid")
    private String typeGuid;

    /**
     * 查询关键字
     */
    @ApiModelProperty(value = "查询关键字")
    private String keywords;

}
