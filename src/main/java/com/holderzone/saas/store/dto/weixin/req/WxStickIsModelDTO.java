package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickIsModelDTO
 * @date 2019/03/16 17:13
 * @description 微信桌贴是否是模板请求参数
 * @program holder-saas-store
 */
@ApiModel("微信桌贴是否是模板请求参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxStickIsModelDTO {

    @ApiModelProperty
    private String guid;

    @ApiModelProperty
    private Integer isModel;

}
