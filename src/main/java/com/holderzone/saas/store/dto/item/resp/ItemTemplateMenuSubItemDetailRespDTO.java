package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateMenuSubItemDetailRespDTO
 * @date 2019/06/03 11:41
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@ApiModel(value = "商品模板-菜单-菜品详细信息")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ItemTemplateMenuSubItemDetailRespDTO {

    /**
     * 菜单菜品guid
     */
    @ApiModelProperty(value = "业务主键guid")
    private String  guid;
    /**
     * 菜品名称
     */
     @ApiModelProperty(value = "菜品名称")
    private String  name;
    /**
     * 菜品sku guid
     */
     @ApiModelProperty(value = "菜品sku guid")
    private String  skuGuid;

    /**
     * 菜品sku名称
     */
     @ApiModelProperty(value = "菜品sku名称")
    private String  skuName;
    /**
     * 菜品单位
     */
     @ApiModelProperty(value = "菜品单位")
    private String  unit;


    /**
     * 菜品sku guid
     */
    @ApiModelProperty(value = "菜品分类guid")
    private String  typeGuid;

    /**
     * 菜品类型名称
     */
     @ApiModelProperty(value = "菜品类型名称")
    private String  typeName;
    /**
     * 菜品原价
     */
     @ApiModelProperty(value = "菜品原价")
    private BigDecimal salePrice;
    /**
     * 菜品售价
     */
     @ApiModelProperty(value = "菜品售价")
    private BigDecimal  price;

     /**
      * 是否逻辑删除
      */
    @ApiModelProperty(value = "是否逻辑删除 0：否 1：是")
    private BigDecimal  isDetail;

}
