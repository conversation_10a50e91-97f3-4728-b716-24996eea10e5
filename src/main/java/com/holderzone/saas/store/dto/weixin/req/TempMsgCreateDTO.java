package com.holderzone.saas.store.dto.weixin.req;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TempMsgCreateDTO
 * @date 2019/05/14 17:38
 * @description 微信消息模板创建DTO
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "微信消息模板创建DTO")
public class TempMsgCreateDTO extends BaseDTO {
    @ApiModelProperty("需创建消息模板的公众号appId")
    private String appId;
}
