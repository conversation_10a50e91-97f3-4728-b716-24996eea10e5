package com.holderzone.saas.store.dto.deposit.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class GoodsRespDTO implements Serializable {

    private static final long serialVersionUID = 8424514095125620853L;

    @ApiModelProperty(value = "商品guid")
    @NotNull(message = "商品guid不得为空")
    private String guid;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品规格名称")
    private String skuName;

    private String skuGuid;

    @ApiModelProperty(value = "单位")
    private String goodsUnit;

    @ApiModelProperty(value = "存储位置")
    private String storePosition;

    @ApiModelProperty(value = "过期时间 格式 2019-12-12，不传值，表示永久有效")
    private String expireTime;

    @ApiModelProperty(value = "剩余天数，由后台计算之后返回给前端，前端不需要传递此参数到后端，该字段暂未使用")
    private int residueDay;

    @ApiModelProperty(value = "寄存数量，在存入商品时传入该参数")
    private int depositNum;

    @ApiModelProperty(value = "剩余数量，由后端计算值返回给前端")
    private int residueNum;

    @ApiModelProperty(value = "取出数量，在取出寄存记录时传递")
    private int takeOutNum;
}
