package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @className CategoryDTO
 * @date 2019-04-27 16:50:00
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel(value = "分类dto")
public class CategoryDTO implements Serializable {

    private static final long serialVersionUID = 6525433130023935005L;

    @ApiModelProperty(value = "分类guid")
    private String categoryGuid;

    @ApiModelProperty(value = "分类名")
    private String categoryName;

    @ApiModelProperty(value = "原材料列表")
    private List<MaterialDTO> materialDTOList;

    public String getCategoryGuid() {
        return categoryGuid;
    }

    public void setCategoryGuid(String categoryGuid) {
        this.categoryGuid = categoryGuid;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public List<MaterialDTO> getMaterialDTOList() {
        return materialDTOList;
    }

    public void setMaterialDTOList(List<MaterialDTO> materialDTOList) {
        this.materialDTOList = materialDTOList;
    }
}
