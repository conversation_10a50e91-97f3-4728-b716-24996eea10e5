package com.holderzone.saas.store.dto.takeaway;

/**
 * 商家取消订单时填写的值
 *
 * https://developer.meituan.com/openapi#7.6
 */
public enum MtOcReasonCodeEnum {

    商家超时接单("2001", "商家超时接单【商家取消时填写】"),

    非顾客原因修改订单("2002", "非顾客原因修改订单"),

    非客户原因取消订单("2003", "非客户原因取消订单"),

    配送延迟("2004", "配送延迟"),

    售后投诉("2005", "售后投诉"),

    用户要求取消("2006", "用户要求取消"),

    其他原因("2007", "其他原因（未传code，默认为此）"),

    店铺太忙("2008", "店铺太忙"),

    商品已售完("2009", "商品已售完"),

    地址无法配送("2010", "地址无法配送"),

    店铺已打烊("2011", "店铺已打烊"),

    联系不上用户("2012", "联系不上用户"),

    重复订单("2013", "重复订单"),

    配送员取餐慢("2014", "配送员取餐慢"),

    配送员送餐慢("2015", "配送员送餐慢"),

    配送员丢餐少餐餐洒("2016", "配送员丢餐、少餐、餐洒");

    private String code;

    private String desc;

    MtOcReasonCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static MtOcReasonCodeEnum ofCode(String code) {
        for (MtOcReasonCodeEnum value : values()) {
            if (value.code.equalsIgnoreCase(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效的reasonCode[" + code + "]");
    }
}
