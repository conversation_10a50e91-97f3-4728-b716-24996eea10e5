package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WebSocketMessageDTO
 * @date 2019/5/20
 */
@ApiModel
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WebSocketMessageDTO<T> {
	@ApiModelProperty(value = "接收类型")
	private Integer type;

	@ApiModelProperty(value = "跳转页面")
	private Integer isJump=0;

	@ApiModelProperty(value = "推送内容")
	private T content;

	private Integer errorCode;

	private String errorMsg;
}
