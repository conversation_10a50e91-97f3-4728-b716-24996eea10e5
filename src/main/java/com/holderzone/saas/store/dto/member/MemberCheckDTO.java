package com.holderzone.saas.store.dto.member;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberCheckDTO
 * @date 2018/08/23 上午9:10
 * @description //TODO
 * @program holder-saas-config-center
 */
public class MemberCheckDTO {

    @NotBlank
    private String password;

    @NotBlank
    private String phone;

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
