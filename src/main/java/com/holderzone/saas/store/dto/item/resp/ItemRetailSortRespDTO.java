package com.holderzone.saas.store.dto.item.resp;

import com.holderzone.saas.store.dto.item.req.TypeSortReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemRetailSortReqDTO
 * @date 2019/10/29 下午5:29
 * @description //商超排序
 * @program holder
 */


@ApiModel(value = "商超分类排序返回dto")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemRetailSortRespDTO {

    @NotNull(message = "分类列表不可为空")
    @ApiModelProperty(value = "分类及其分类下商品排序集合")
    private List<TypeSortRespDTO> typeList;
}
