package com.holderzone.saas.store.dto.orderlog.detail;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RefundLogDTO
 * @date 2018/10/09 20:07
 * @description
 * @program holder-saas-store-order
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundLogDTO implements Serializable {

    @ApiModelProperty(value = "操作类型（logOperationTypeEnum）")
    private Integer operationType;

    @ApiModelProperty(value = "操作类型name（logOperationTypeEnum）")
    private String operationTypeName;

    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;

    @ApiModelProperty(value = "支付方式")
    private String paymentMode;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "备注")
    private String remark;
}
