package com.holderzone.saas.store.dto.report;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className IndexOrderOverviewDto
 * @date 2018/10/11 17:35
 * @description 报表首页 订单概览 DTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "订单概览")
public class IndexOrderOverviewDto {

    @ApiModelProperty(value = "订单总数（笔）")
    private BigDecimal orderCounts;
    @ApiModelProperty(value = "订单构成-交易状态（笔）")
    List<ReportBaseDTO> order = Lists.newArrayList();
//    private Order order;
    @ApiModelProperty(value = "交易成功订单构成-销售类型（笔）")
    List<ReportBaseDTO> transSuccessOrder = Lists.newArrayList();
//    private TransOrder TransSuccessOrder;
    @ApiModelProperty(value = "作废取消订单构成-销售类型（笔）")
    List<ReportBaseDTO> transCancelOrder = Lists.newArrayList();
//    private TransOrder TransCancelOrder;
    @ApiModelProperty(value = "多线程查询返回对象类型")
    private int model;

    @Data
    public static class Order{
        @ApiModelProperty(value = "交易成功订单数")
        private BigDecimal transOrderCounts;
        @ApiModelProperty(value = "交易成功订单所占百分比")
        private String transOrderPercent;
        @ApiModelProperty(value = "作废订单数")
        private BigDecimal cancelOrderCounts;
        @ApiModelProperty(value = "作废订单所占百分比")
        private String cancelOrderPercent;

    }

    @Data
    public static class TransOrder{
        @ApiModelProperty(value = "正餐订单")
        private BigDecimal dinner;
        @ApiModelProperty(value = "正餐订单所占百分比")
        private String  dinnerPercent;
        @ApiModelProperty(value = "快餐订单")
        private BigDecimal fast;
        @ApiModelProperty(value = "快餐订单所占百分比")
        private String fastPercent;
        @ApiModelProperty(value = "外卖订单")
        private BigDecimal takeAway;
        @ApiModelProperty(value = "外卖订单所占百分比")
        private String takeAwayPercent;

    }

}
