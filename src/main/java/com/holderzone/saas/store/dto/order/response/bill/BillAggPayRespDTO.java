package com.holderzone.saas.store.dto.order.response.bill;

import com.holderzone.saas.store.dto.order.response.OrderTableBillDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillCalculateReqDTO
 * @date 2019/01/28 17:32
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class BillAggPayRespDTO {

    @ApiModelProperty(value = "支付guid，如果有，聚合支付轮询参数")
    private String payGuid;

    @ApiModelProperty(value = "聚合支付订单guid，如果有，聚合支付轮询参数")
    private String jhOrderGuid;

    @ApiModelProperty(value = "结果")
    private Boolean result;

    @ApiModelProperty(value = "是否估清失败")
    private Boolean estimate;

    @ApiModelProperty(value = "返回估清商品提示信息")
    private String estimateInfo;

    @ApiModelProperty(value = "已估清商品规格id")
    private List<String> estimateSkuGuids;
}
