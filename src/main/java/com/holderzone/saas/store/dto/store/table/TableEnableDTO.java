package com.holderzone.saas.store.dto.store.table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableDO
 * @date 2018/07/24 上午9:55
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class TableEnableDTO implements Serializable {

    private static final long serialVersionUID = -167146931686951867L;

    /**
     * 桌台guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "桌台guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "桌台guid", required = true)
    private String tableGuid;

    /**
     * 是否已启用
     * 0=未启用
     * 1=已启用
     * 默认1
     */
    @NotNull
    @Min(value = 0, message = "是否启用不得为空，且0=未启用，1=已启用")
    @Max(value = 1, message = "是否启用不得为空，且0=未启用，1=已启用")
    @ApiModelProperty(value = "是否已启用。0=未启用，1=已启用。默认1。", required = true)
    private Integer enable;
}
