package com.holderzone.saas.store.dto.print.deprecate;

import java.util.ArrayList;
import java.util.List;

/**
 * 单个打印行
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/07/26 11:16
 * @description 打印行抽象实体
 */
@Deprecated
public class PrintRowDTO {

    /**
     * x放大倍数
     */
    private String xm;

    /**
     * y放大倍数
     */
    private String ym;

    /**
     * 对齐
     */
    private String align;

    /**
     * 加粗
     */
    private Boolean bold;

    /**
     * 字体类型
     */
    private String fontFamily;

    /**
     * 媒体类型
     */
    private String contentType;

    /**
     * 内容
     */
    private String content;

    private List<PrintEDTO> printEList = new ArrayList<PrintEDTO>();

    public String getXm() {
        return xm;
    }

    public void setXm(String xm) {
        this.xm = xm;
    }

    public String getYm() {
        return ym;
    }

    public void setYm(String ym) {
        this.ym = ym;
    }

    public String getAlign() {
        return align;
    }

    public void setAlign(String align) {
        this.align = align;
    }

    public String getFontFamily() {
        return fontFamily;
    }

    public void setFontFamily(String fontFamily) {
        this.fontFamily = fontFamily;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    /**
     * 添加打印元素
     */
    public void addPrintE(PrintEDTO printE) {
        this.printEList.add(printE);
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        PrintRowDTO printRow;

        public Builder() {
            this.printRow = new PrintRowDTO();
        }

        public Builder setXm(String xm) {
            this.printRow.setXm(xm);
            return this;
        }

        public Builder setYm(String ym) {
            this.printRow.setYm(ym);
            return this;
        }

        public Builder setAlign(String align) {
            this.printRow.setAlign(align);
            return this;
        }

        public Builder setBlod(Boolean bold) {
            this.printRow.setBold(bold);
            return this;
        }

        public Builder setFontFamily(String fontFamily) {
            this.printRow.setFontFamily(fontFamily);
            return this;
        }

        public Builder setContentType(String contentType) {
            this.printRow.setContentType(contentType);
            return this;
        }

        public Builder setContent(String content) {
            this.printRow.setContent(content);
            return this;
        }

        public PrintRowDTO build() {
            return this.printRow;
        }
    }

    public List<PrintEDTO> getPrintEList() {
        return printEList;
    }

    public void setPrintEList(List<PrintEDTO> printEList) {
        this.printEList = printEList;
    }

    public Boolean getBold() {
        return bold;
    }

    public void setBold(Boolean bold) {
        this.bold = bold;
    }
}
