package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillDiscountRequestDTO
 * @date 2018/08/01 16:26
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class BillDiscountRequestDTO extends BaseBillDTO implements Serializable {

    /**
     * 折扣集合
     */
    @ApiModelProperty(value = "折扣集合")
    private List<BillDiscountDTO> billDiscountDTOList;

    /**
     * 附加费
     */
    @ApiModelProperty(value = "附加费集合")
    private List<AddtionalFeeDTO> addtionalFeeConfigDTOS;

}
