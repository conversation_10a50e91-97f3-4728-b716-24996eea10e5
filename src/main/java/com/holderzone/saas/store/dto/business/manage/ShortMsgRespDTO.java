package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ShortMsgRespDTO
 * @date 2018/09/17 15:40
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class ShortMsgRespDTO implements Serializable {

    @ApiModelProperty(value = "返回10000表示请求成功")
    private String code;

    @ApiModelProperty(value = "msg信息")
    private String msg;

    @ApiModelProperty(value = "轮询支付结果需要传入")
    private String payGuid;

    @ApiModelProperty(value = "轮询支付结果需要传入")
    private String paymentGuid;

    @ApiModelProperty(value = "二维码链接地址")
    private String codeUrl;

    @ApiModelProperty(value = "2:支付成功，3:支付失败，10:支付中")
    private String paySt;

}
