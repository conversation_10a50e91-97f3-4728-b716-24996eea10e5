package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateMenusRespDTO
 * @date 2019/05/30 18:41
 * @description //TODO 商品模板菜单列表
 * @program holder-saas-aggregation-app
 */
@Data
@ApiModel(value = "商品模板菜单列表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ItemTemplateMenusRespDTO {

    @ApiModelProperty(value = "模板guid")
    private  String templateGuid;
    @ApiModelProperty(value = "模板菜单guid")
    private  String guid;
    @ApiModelProperty(value = "模板菜单执行时间集合")
    private List<String> executeTimes;
    @ApiModelProperty(value = "是否逻辑删除  删除时传1")
    private  String isDelate;

}
