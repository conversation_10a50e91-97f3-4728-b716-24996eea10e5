package com.holderzone.saas.store.dto.erp.erpretail.req;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

@Data
@EqualsAndHashCode
public class QueryGoodsSumInfoReqDTO extends BasePageDTO {

    @ApiModelProperty(value = "商品分类")
    private String goodsClassifyGuid;

    @ApiModelProperty(value = "名称或编号")
    private String nameOrCode;

    @ApiModelProperty(value = "门店Guid")
    @NotEmpty(message = "门店Guid不得为空")
    private String storeGuid;


}
