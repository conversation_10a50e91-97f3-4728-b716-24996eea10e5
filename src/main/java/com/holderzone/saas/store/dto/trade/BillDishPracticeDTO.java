package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillDishPracticeDTO
 * @date 2018/07/26 17:37
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class BillDishPracticeDTO implements Serializable {

    /**
     * 账单菜品做法Guid
     */
    @ApiModelProperty(value = "账单菜品做法Guid")
    private String billDishPracticeGuid;

    /**
     * 账单菜品GUID
     */
    @ApiModelProperty(value = "账单菜品GUID")
    private String billDishGuid;

    /**
     * 做法GUID
     */
    @ApiModelProperty(value = "做法GUID")
    private String practiceGuid;

    /**
     * 做法名称
     */
    @ApiModelProperty(value = "做法名称")
    private String practiceName;

    /**
     * 做法总额
     */
    @ApiModelProperty(value = "做法总额")
    private BigDecimal practicePrice;

}
