package com.holderzone.saas.store.dto.map;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DistantDTO
 * @date 2019/05/18 17:34
 * @description 距离DTO
 * @program holder-saas-store
 */
@Data
public class DistantDTO {

    @JsonProperty(value = "origin_id")
    private String originId;

    @JsonProperty(value = "dest_id")
    private String destId;

    private String distance;

    private String duration;

    private String info;

    private String code;
}
