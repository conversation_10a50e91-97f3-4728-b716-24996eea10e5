package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@ApiModel("微信用户")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxUserRecordDTO {

	private String guid;

	private String openId;

	private String nickName;

	private String headImgUrl;

	private Integer sex;

	private String country;

	private String province;

	private String city;

	private String phone;

	private LocalDateTime gmtCreate;

	private LocalDateTime gmtModified;

	private Boolean isLogin;
}
