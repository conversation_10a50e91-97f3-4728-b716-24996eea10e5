package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class PrdPointItemQueryReqDTO implements Serializable {

    private static final long serialVersionUID = -2782247301702252131L;

    @NotEmpty(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @NotNull(message = "设备ID不得为空")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @NotNull(message = "堂口Guid不得为空")
    @ApiModelProperty(value = "堂口Guid")
    private String pointGuid;

    @Nullable
    @ApiModelProperty(value = "搜索关键字")
    private String searchKey;

    @ApiModelProperty(value = "菜品分组guid")
    private String groupGuid;
}
