package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreStatusUpdateReqDTO
 * @date 2019/05/09 15:44
 * @description 门店微信功能状态修改请求DTO
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("门店微信功能状态修改请求DTO")
public class WxStoreStatusUpdateReqDTO {
    @ApiModelProperty("guid")
    private String guid;

    @ApiModelProperty("门店是否开启微信所有功能")
    private Integer isOpened;

    @ApiModelProperty("微信点餐功能状态（0关闭，1开启）")
    private Integer forHereStatus;

    @ApiModelProperty("微信排队功能状态（0关闭，1开启）")
    private Integer queueUpStatus;

    @ApiModelProperty("微信预定功能状态（0关闭，1开启）")
    private Integer bookingStatus;

    @ApiModelProperty("微信外卖功能状态（0关闭，1开启）")
    private Integer takeawayStatus;
}
