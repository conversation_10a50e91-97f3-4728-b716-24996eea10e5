package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Api("会员卡或优惠券")
@Data
@Builder
public class WxVolumeCodeReqDTO {

	@ApiModelProperty(value = "企业guid")
	private String enterpriseGuid;

	@ApiModelProperty(value = "品牌guid")
	private String brandGuid;

	@ApiModelProperty(value = "门店guid")
	private String storeGuid;

	@ApiModelProperty(value = "用户guid")
	private String openId;
}
