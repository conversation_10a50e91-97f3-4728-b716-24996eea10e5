package com.holderzone.saas.store.dto.print.type;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/23
 * @description 模版商品展示
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "模版商品展示", description = "模版商品展示")
public class TemplateItemVO implements Serializable {

    private static final long serialVersionUID = 1452641517698871918L;

    @ApiModelProperty(value = "'商品guid'")
    private String itemGuid;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "分类guid")
    private String typeGuid;



    /**
     * 菜谱方案商品名称
     */
    @ApiModelProperty(value = "菜谱方案商品名称")
    private String planItemName;


    @ApiModelProperty("商品id(productGuid,productSpecGuid)")
    private String guid;

    private String productGuid;

    private String productName;

    private String productSpecGuid;

    private String productSpecName;
}
