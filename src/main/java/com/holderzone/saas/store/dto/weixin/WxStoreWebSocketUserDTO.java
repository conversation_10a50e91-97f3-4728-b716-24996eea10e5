package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreWebSocketUserDTO
 * @date 2019/3/22
 */
@Data
@ApiModel(value = "保存websocket会话")
public class WxStoreWebSocketUserDTO {

	@ApiModelProperty(value = "sessionid")
	private String sid;

	@ApiModelProperty(value = "用户信息")
	private WxStoreConsumerDTO wxStoreConsumerDTO;
}
