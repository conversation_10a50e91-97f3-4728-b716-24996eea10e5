package com.holderzone.saas.store.dto.takeaway;

/**
 * 订单退款消息推送的type
 * <p>
 * https://developer.meituan.com/openapi#7.6
 */
public enum MtCbOrNotifyTypeEnum {

    APPLY("apply", "发起退款"),

    AGREE("agree", "确认退款"),

    REJECT("reject", "驳回退款"),

    CANCEL_REFUND("cancelRefund", "用户取消退款申请"),

    CANCEL_REFUND_COMPLAINT("cancelRefundComplaint", "取消退款申诉");

    private String type;

    private String desc;

    MtCbOrNotifyTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MtCbOrNotifyTypeEnum ofType(String type) {
        for (MtCbOrNotifyTypeEnum value : values()) {
            if (type.equalsIgnoreCase(value.type)) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效的notifyType[" + type + "]");
    }
}
