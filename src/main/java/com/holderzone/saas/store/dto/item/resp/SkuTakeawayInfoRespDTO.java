package com.holderzone.saas.store.dto.item.resp;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SkuTakeawayInfoRespDTO
 * @date 2018/09/29 下午4:17
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class SkuTakeawayInfoRespDTO extends BaseDTO {
    @ApiModelProperty(value = "skuGuid")
    private String skuGuid;
    @ApiModelProperty(value = "parentSkuGuid")
    private String parentSkuGuid;
    @ApiModelProperty(value = "商品类型：1.套餐（不称重，无规格），2.多规格商品（单商品，不称重），3.称重商品（单商品，称重），4.单品。")
    private Integer itemType;
    @ApiModelProperty(value = "规格名称（规格商品才有）")
    private String skuName;
    @ApiModelProperty(value = "商品名称")
    private String itemName;
    /**
     * 售卖价格
     */
    @ApiModelProperty(value = "售卖价格")
    private BigDecimal salePrice;
    /**
     * 商品GUID
     */
    @ApiModelProperty(value = "商品GUID")
    private String itemGuid;
    @ApiModelProperty(value = "分类GUID")
    private String typeGuid;
    @ApiModelProperty(value = "分类名称")
    private String typeName;
    @ApiModelProperty(value = "规格编号")
    private String code;
    @ApiModelProperty(value = "规格单位")
    private String unit;

    @ApiModelProperty(value = "是否上架")
    private Integer isRack;

    @ApiModelProperty("外卖核算价")
    private BigDecimal takeawayAccountingPrice;

    @ApiModelProperty("堂食核算价")
    private BigDecimal accountingPrice;



    @ApiModelProperty(value = "分组集合")
    private List<SubgroupWebRespDTO> subgroupList;
}
