package com.holderzone.saas.store.dto.business.reason;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReasonTypeDO
 * @date 2019/08/15 14:49
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ReasonTypeDTO implements Serializable {

    private static final long serialVersionUID = -4145834403382353878L;

    @ApiModelProperty(value = "原因guid")
    private String reasonTypeGuid;

    /**
     * 原因类型
     */
    @ApiModelProperty(value = "原因类型code")
    private String reasonType;

    /**
     * 原因类型
     */
    @ApiModelProperty(value = "原因类型")
    private Integer reasonTypeCode;


}