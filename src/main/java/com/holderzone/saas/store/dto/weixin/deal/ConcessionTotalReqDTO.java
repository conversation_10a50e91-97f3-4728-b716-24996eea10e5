package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("优惠明细入参")
@Data
public class ConcessionTotalReqDTO {

	@ApiModelProperty(value = "1:会员卡，2：优惠券",required = true)
	private Integer type;

	@ApiModelProperty(value = "会员卡id")
	private String memberInfoCardGuid;

	@ApiModelProperty(value = "是否计算积分1：计算，0：不计算")
	private Integer enableIntegral;

	@ApiModelProperty(value = "优惠券码")
	private String volumeCode;

	@ApiModelProperty(value = "订单id",required = true)
	@NotBlank(message = "订单id必传")
	private String orderGuid;
}
