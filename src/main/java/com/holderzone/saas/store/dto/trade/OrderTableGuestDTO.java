package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 订单
 * </p>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class OrderTableGuestDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 桌台guid
     */
    private String tableGuid;

    /**
     * 人数
     */
    private Integer guestCount;

    private String orderGuid;

    private String diningTableName;

    private String areaName;

    /**
     * 区域guid
     */
    private String areaGuid;

    /**
     * 是否主台
     */
    private Integer isMain;

    /**
     * 联台编号
     */
    private Integer associatedTimes;

}