package com.holderzone.saas.store.dto.print.content.nested;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 折扣优惠信息
 *
 * <AUTHOR>
 * @version 1.0
 * @className Reduce
 * @date 2018/08/01 10:49
 * @description 折扣dto
 * @program holder-saas-store-dto
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "优惠折扣记录")
public class ReduceRecord implements Serializable {

    private static final long serialVersionUID = 2560920447796754131L;

    @NotBlank(message = "折扣名称不能为空")
    @ApiModelProperty(value = "折扣名称")
    private String name;

    @NotNull(message = "折扣金额不能为空")
    @ApiModelProperty(value = "折扣金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "订单笔数")
    private Long orderCount;

    @ApiModelProperty(value = "订单笔数")
    private String orderCountStr;

    public ReduceRecord(String name, BigDecimal amount) {
        this.name = name;
        this.amount = amount;
    }
}
