package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

import static com.holderzone.saas.store.dto.print.util.PrintMockUtils.*;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("预结单")
public class PrintPreCheckoutDTO extends PrintItemDetailDTO implements PrintDataMockito {

    @Valid
    @Nullable
    @ApiModelProperty(value = "附加费", required = true)
    private List<AdditionalCharge> additionalChargeList;

    @Valid
    @Nullable
    @ApiModelProperty(value = "优惠折扣", required = true)
    private List<ReduceRecord> reduceRecordList;

    @NotNull(message = "应付金额不能为空")
    @ApiModelProperty(value = "应付金额", required = true)
    private BigDecimal payAble;

    @ApiModelProperty(value = "支付方式", required = true)
    private List<PayRecord> payRecordList;

    @ApiModelProperty(value = "实付金额", required = true)
    private BigDecimal actuallyPay;

    @ApiModelProperty(value = "找零", required = true)
    private BigDecimal changedPay;

    @Nullable
    @ApiModelProperty(value = "门店地址", notes = "如果未设置门店地址，该值为空")
    private String storeAddress;

    @Nullable
    @ApiModelProperty(value = "门店电话", notes = "如果未设置门店电话，该值为空")
    private String tel;

    @Nullable
    @ApiModelProperty(value = "买单二维码", notes = "如果未设置买单二维码，该值为空")
    private String payQrCode;

    @ApiModelProperty("会员优惠差异金额")
    private BigDecimal originalPrice;

    @Override
    public void applyMock() {
        super.applyMock();
        setAdditionalChargeList(mockAdditionalChargeList());
        setReduceRecordList(mockReduceRecordList());
        setPayAble(mockPayable());
        setStoreAddress(mockStoreAddress());
        setTel(mockStoreTel());
    }
}
