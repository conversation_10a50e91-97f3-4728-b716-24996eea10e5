package com.holderzone.saas.store.dto.retail.bill.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReturnGoodReqDto
 * @date 2019/10/25 16:23
 * @description //TODO
 * @program IdeaProjects
 */
@Data
public class ReturnItemReqDTO extends BaseDTO {

    @NotNull
    @ApiModelProperty(value = "订单GUID")
    private String orderGuid;

    @ApiModelProperty(value = "退货列表")
    List<ItemReqDTO> itemsReqDTOS;

    @ApiModelProperty(value = "退货原因")
    private String reason;

    @NotNull
    @Max(9_0000_0000)
    @ApiModelProperty(value = "应退金额")
    private BigDecimal shouldReturnAmount;

    @NotNull
    @Max(9_0000_0000)
    @ApiModelProperty(value = "实退金额")
    private BigDecimal actualReturnAmount;

}