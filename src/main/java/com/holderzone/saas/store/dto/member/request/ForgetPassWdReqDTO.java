package com.holderzone.saas.store.dto.member.request;

import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ModifiPassWdReqDTO
 * @date 2018/09/17 18:03
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class ForgetPassWdReqDTO extends BaseMemberDTO{

    @ApiModelProperty(value = "验证码,必传")
    @NotNull(message = "验证码不能为空")
    private String verificationCode;

    @ApiModelProperty(value = "新密码,必传")
    @NotNull(message = "新密码不能为空")
    private String newPassword;

}
