package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.base.TradeModeAware;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 标签单
 *
 * <AUTHOR>
 * @date 2018/09/19 16:23
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "标签单")
public class PrintLabelDTO extends PrintBaseItemDTO implements TradeModeAware {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private String storeName;

    @NotBlank(message = "编号不能为空")
    @ApiModelProperty(value = "编号", required = true)
    private String serialNumber;

    @Nullable
    @ApiModelProperty(value = "门店电话", notes = "如果未设置门店电话，该值为空")
    private String tel;

    @NotNull(message = "交易模式不得为空")
    @ApiModelProperty(value = "交易模式", required = true)
    private Integer tradeMode;

    /**
     * todo 有赋值但未使用，建议删除
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 当前序号
     * 该字段是打印服务内部生成，使用方无需设置
     */
    private int currentNo;

    /**
     * 总数
     * 该字段是打印服务内部生成(根据切纸类型，该值不同)，使用方无需设置
     */
    private int totalNo;
}
