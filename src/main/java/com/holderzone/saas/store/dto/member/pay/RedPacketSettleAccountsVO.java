package com.holderzone.saas.store.dto.member.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 红包订单结算返回
 * @date 2022/3/17 12:00
 * @className: RedPacketSettleAccountsVO
 */
@Data
@ApiModel("红包订单结算返回")
@Accessors(chain = true)
public class RedPacketSettleAccountsVO implements Serializable {

    private static final long serialVersionUID = 6978528305791455078L;

    @ApiModelProperty("红包金额")
    private BigDecimal redPacketSettleAccount;

    @ApiModelProperty("订单编号")
    private String orderNumber;

    /**
     * 活动guid
     */
    @ApiModelProperty("活动guid")
    private String activityGuid;
}
