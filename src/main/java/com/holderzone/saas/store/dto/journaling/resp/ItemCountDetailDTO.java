package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemCountDetailDTO
 * @date 2019/10/25 17:18
 * @description 营业日报商品相关DTO
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("营业日报商品相关返回参数")
public class ItemCountDetailDTO {

    @ApiModelProperty("guid")
    private String guid;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("数量")
    private Double count;

    @ApiModelProperty("金额")
    private BigDecimal amount;
}
