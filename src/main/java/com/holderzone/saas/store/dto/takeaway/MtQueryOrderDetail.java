package com.holderzone.saas.store.dto.takeaway;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * cTime : 1476703162
 * caution :
 * cityId : 110100
 * deliveryTime : 0
 * ePoiId : 38336878361472
 * hasInvoiced : 1
 * invoiceTitle : 北京三块在线科技有限公司
 * taxpayerId : 91110108562144110X
 * isThirdShipping : 0
 * latitude : 39.980164
 * longitude : 116.485792
 * logisticsCancelTime : 0
 * logisticsCode : 2002
 * logisticsCompletedTime : 1476756192
 * logisticsConfirmTime : 1476753421
 * logisticsDispatcherMobile : 13910951589
 * logisticsDispatcherName : 夏洪洋
 * logisticsFetchTime : 1476754250
 * logisticsId : 101
 * logisticsName : 快送
 * logisticsSendTime : 1476753166
 * logisticsStatus : 40
 * orderCompletedTime : 1476705543
 * orderConfirmTime : 1476705059
 * orderId : 2341012653
 * orderIdView : 4229622801966816
 * orderSendTime : 1476703176
 * originalPrice : 48
 * payType : 2
 * poiAddress : 将台路芳园里3号楼北侧（和睦家医院旁）
 * poiId : 422962
 * poiName : 祥锦香苑饺子屋（将台路店）
 * poiPhone : 010-84561359
 * recipientAddress : 银河湾(万红西街) (2号楼1门505)@#北京市朝阳区酒仙桥街道万红西街9号院
 * recipientName : 李(先生)
 * recipientPhone : 13910780281
 * backupRecipientPhone : 17642323521_7121
 * shippERPhone : 18301181810
 * shippingFee : 5
 * status : 8
 * total : 42
 * uTime : 1476706282
 * daySeq : 1
 * dinnersNumber : 2
 * pickType : 0
 */
@Data
@NoArgsConstructor
public class MtQueryOrderDetail implements Serializable {

    private static final long serialVersionUID = -7907515681905869366L;

    @JsonProperty("cTime")
    private long ctime;
    private String caution;
    private long cityId;
    private long deliveryTime;
    private String detail;
    private String ePoiId;
    private String extras;
    private int hasInvoiced;
    private String invoiceTitle;
    private String taxpayerId;
    private int isThirdShipping;
    private double latitude;
    private double longitude;
    private String poiReceiveDetail;
    private long logisticsCancelTime;
    private String logisticsCode;
    private long logisticsCompletedTime;
    private long logisticsConfirmTime;
    private String logisticsDispatcherMobile;
    private String logisticsDispatcherName;
    private long logisticsFetchTime;
    private int logisticsId;
    private String logisticsName;
    private long logisticsSendTime;
    private int logisticsStatus;
    private long orderCompletedTime;
    private long orderConfirmTime;
    private long orderId;
    private long orderIdView;
    private long orderSendTime;
    private double originalPrice;
    private int payType;
    private String poiAddress;
    private long poiId;
    private String poiName;
    private String poiPhone;
    private String recipientAddress;
    private String recipientName;
    private String recipientPhone;
    private String backupRecipientPhone;
    private String shipperPhone;
    private double shippingFee;
    private int status;
    private double total;
    @JsonProperty("uTime")
    private long utime;
    private String daySeq;
    private int dinnersNumber;
    private int pickType;
}
