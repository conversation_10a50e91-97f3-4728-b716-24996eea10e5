package com.holderzone.saas.store.dto.order.response.daily;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AttrItemRespDTO
 * @date 2019/02/20 16:12
 * @description
 * @program holder-saas-store-trade
 */
@Data
@ApiModel
public class AttrItemRespDTO extends ItemRespDTO {
    @ApiModelProperty(value = "属性组guid")
    private String attrGroupGuid;
    @ApiModelProperty(value = "属性组名称")
    private String attrGroupName;
    @ApiModelProperty(value = "属性")
    private List<AttrItemRespDTO> attrs;
}