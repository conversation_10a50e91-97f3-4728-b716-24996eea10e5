package com.holderzone.saas.store.dto.organization;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreEnterpriseDTO
 * @date 2019/10/24 17:01
 * @description 门店级企业信息DTO
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StoreEnterpriseDTO {
    StoreDTO storeDTO;
    BrandDTO brandDTO;
}
