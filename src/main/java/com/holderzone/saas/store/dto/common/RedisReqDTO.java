package com.holderzone.saas.store.dto.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description redis请求实体
 * @date 2021/9/9 10:12
 * @className: RedisReqDTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel("redis请求实体")
public class RedisReqDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -1361598913978666770L;

    @ApiModelProperty("存入的值（不能空）")
    private String redisValue;

    @ApiModelProperty("存入的key")
    private String redisKey;

    @ApiModelProperty("过期时间")
    private Long redisTime;

    @ApiModelProperty("时间单位")
    private TimeUnit timeUnit;
}