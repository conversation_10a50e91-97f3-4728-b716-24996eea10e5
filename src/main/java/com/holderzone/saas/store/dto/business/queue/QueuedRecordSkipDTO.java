package com.holderzone.saas.store.dto.business.queue;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueuedRecordCreateDTO
 * @date 2018/07/27 下午6:29
 * @description 过号
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueuedRecordSkipDTO {

    /**
     * 排队记录guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "排队记录guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "排队记录guid", required = true)
    private String queuedRecordGuid;
}
