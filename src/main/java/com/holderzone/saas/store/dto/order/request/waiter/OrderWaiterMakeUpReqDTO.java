package com.holderzone.saas.store.dto.order.request.waiter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> R
 * @date 2020/12/9 11:38
 * @description
 */
@Data
@ApiModel(value = "订单服务员记录请求参数DTO----后台补录")
public class OrderWaiterMakeUpReqDTO {
    @ApiModelProperty(value = "订单的guid", required = true)
    private List<String> orderGuidList;
    @ApiModelProperty(value = "服务员", required = true)
    private List<OrderWaiterInfoDTO> orderWaiterInfoDTOList;
}
