package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ShortMsgConfigDTO
 * @date 2018/09/18 17:43
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class ShortMsgConfigDTO implements Serializable {

    @ApiModelProperty(value = "业务主键guid")
    private String appreciateGuid;

    @ApiModelProperty(value = "消费后发送短信0:禁用，1:启用")
    @NotNull(message = "消费后发送短信不能为空")
    private int afterConsume;

    @ApiModelProperty(value = "充值成功后发送短信0:禁用，1:启用")
    @NotNull(message = "使用积分发送短信不能为空")
    private int afterCharge;

    @ApiModelProperty(value = "企业guid")
    @NotBlank(message = "商户guid不能为空")
    private String enterpriseGuid;

    @ApiModelProperty(value = "剩余短信条数")
    private int residueCount;
}
