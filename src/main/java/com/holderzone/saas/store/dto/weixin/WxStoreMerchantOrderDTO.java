package com.holderzone.saas.store.dto.weixin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreMerchantOrderDTO
 * @date 2019/4/9
 */
@ApiModel("微信商户接单")
@Data
public class WxStoreMerchantOrderDTO {

    @ApiModelProperty(value = "业务主键")
    private String guid;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "消费合计")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "下单人微信OpenId")
    private String openId;

    @ApiModelProperty(value = "下单人")
    private String nickName;

    @ApiModelProperty(value = "桌台guid")
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台号")
    private String tableCode;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "0:待处理，1：接单，2：拒单，3：未结帐，4：已结账,5:已做废, 8订单已失效,9.已退菜")
    private Integer orderState;

    @ApiModelProperty(value = "0：正餐，1：快餐 ")
    private Integer tradeMode;

    @ApiModelProperty(value = "点餐类型")
    private String tradeModeName;

    @ApiModelProperty(value = "消息来源,0: 微信 1:pad 2:支付宝")
    private Integer msgSource;

    @ApiModelProperty(value = "消息来源名字")
    private String msgSourceName;

    @ApiModelProperty(value = "商品总数")
    private Integer itemCount;

    @ApiModelProperty(value = "已点商品集合")
    private List<DineInItemDTO> dineInItemDTOList;

    @ApiModelProperty(value = "就餐人数")
    private Integer actualGuestsNo;
    /**
     * 新增字段
     */
    @ApiModelProperty(value = "总订单guid")
    private String tradeGuid;
    @ApiModelProperty(value = "订单号")
    private String orderNo;
    @ApiModelProperty(value = "取餐号")
    private String mark;
    @ApiModelProperty(value = "整单备注")
    private String remark;
    @ApiModelProperty(value = "结账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTime;

    @ApiModelProperty(value = "区域id")
    private String areaGuid;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "1表示存在，0表示不存在")
    private Integer currentOrder;

    @ApiModelProperty(value = "操作来源")
    private Integer operationSource;

    @ApiModelProperty(value = "操作来源名字")
    private String operationSourceName;

    @ApiModelProperty(value = "操作人员名字")
    private String operationName;

    @ApiModelProperty(value = "拒单原因")
    private String denialReason;

    @ApiModelProperty(value = "下单头像")
    private String HeadImgUrl;

    @ApiModelProperty(value = "是否会员")
    private Integer isMemberDiscount = 0;

    @ApiModelProperty(value = "是否折扣")
    private Integer isWholeDiscount = 0;

    @ApiModelProperty(value = "trade订单ID")
    private String orderGuid;


    @ApiModelProperty(value = "微信同一批次的guid")
    private String orderRecordGuid;

    @ApiModelProperty(value = "并桌后的订单id")
    private String combine;

    @ApiModelProperty(value = "用户手机号")
    private String phoneNum;

    /**
     * pad点餐批次
     */
    @ApiModelProperty("pad点餐批次")
    private Integer padBatch;

    /***
     * 下单来源 0：PC服务端,1：PC平板,2：小店通,3：一体机,4：POS机,5：云平板,6：点菜宝(M1),
     *  7：PV1(带刷卡的点菜宝),9：厨房显示系统,10: 取餐屏,12：微信 15通吃岛  16 翼惠天下
     * 下单源
     */
    @ApiModelProperty("下单来源")
    private Integer orderSource;
}
