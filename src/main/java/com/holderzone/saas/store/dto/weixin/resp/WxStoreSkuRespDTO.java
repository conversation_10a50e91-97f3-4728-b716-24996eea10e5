package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("微信门店商品规格")
public class WxStoreSkuRespDTO {

	@ApiModelProperty(value="商品guid",required=true)
	private String itemGuid;

	@ApiModelProperty(value = "规格GUID",required = true)
	private String skuGuid;

	@ApiModelProperty(value = "maybe null,upc")
	private String upc;

	@ApiModelProperty(value = "maybe null,当日库存")
	private Integer stock;

	@ApiModelProperty(value = "maybe null,规格名称")
	private String name;

	@ApiModelProperty(value = "售卖价格",required = true)
	private BigDecimal salePrice;

	@ApiModelProperty(value = "是否加入整单折扣(0：否，1：是)",required = true)
	private Integer isWholeDiscount;

	@ApiModelProperty(value = "是否参与会员折扣（0：否，1：是）",required = true)
	private Integer isMemberDiscount;

	@ApiModelProperty(value = "起卖数(非称重即为整数，称重即为小数)",required = true)
	private BigDecimal minOrderNum;

	@ApiModelProperty(value = "计数单位",required = true)
	private String unit;

	@ApiModelProperty(value = "maybe null,编号")
	private String code;

	//新增字段
	@ApiModelProperty(value="是否选中，1：选中，0：不选中")
	private Integer userck;

	/**
	 * 估清
	 */
	/**
	 * 是否估清 1：否  2：是
	 */
	@ApiModelProperty(value = "是否估清 1:否 2:是")
	private Integer isSoldOut = 1;

	/**
	 * 当前剩余数量
	 */
	@ApiModelProperty(value = "当前剩余数量")
	private BigDecimal residueQuantity = new BigDecimal("-1");

	/**
	 * 提醒阈值
	 */
	@ApiModelProperty(value = "提醒阈值")
	private BigDecimal reminderThreshold = BigDecimal.TEN;
}
