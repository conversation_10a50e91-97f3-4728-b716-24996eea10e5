package com.holderzone.saas.store.dto.order.request.groupon;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import com.holderzone.saas.store.dto.takeaway.response.MtMaitonConsumeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GrouponListRespDTO
 * @date 2019/01/28 17:32
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class GrouponReqDTO extends BaseDTO {

    @ApiModelProperty(value = "订单guid")
    @OrderLockField
    private String orderGuid;

    @ApiModelProperty(value = "券购买价")
    private BigDecimal couponBuyPrice;

    @NotNull
    @ApiModelProperty(value = "券码,12位纯数字")
    private String couponCode;

    @ApiModelProperty(value = "批量验券")
    private List<String> couponCodeList;

    @NotNull
    @Min(value = 0, message = "小于100的整数")
    @Max(value = 100, message = "小于100的整数")
    @ApiModelProperty(value = "数量")
    private Integer count;

    @ApiModelProperty(value = "第三方活动guid")
    private String activityGuid;

    /**
     * GroupBuyTypeEnum
     */
    @ApiModelProperty(value = "团购类型")
    @NotNull
    private Integer groupBuyType;


    @ApiModelProperty(value = "项目名称")
    private String dealTitle;

    @ApiModelProperty(value = "券面值")
    private BigDecimal dealValue;

    @ApiModelProperty(value = "项目ID")
    private Integer dealId;

    /**
     * 券类型
     * 团购类型（type=1团餐券; type=2代金券; type=3次卡）
     *
     * @see com.holderzone.saas.store.enums.trade.CouponTypeEnum
     */
    private Integer couponType;

    /**
     * 抵扣金额
     */
    private BigDecimal deductionAmount;

    /**
     * 关联的商品guid
     */
    private String skuGuid;

    /**
     * 代表券码一次核销的标识（抖音撤销时需要）
     */
    private String verifyId;

    /**
     * 代表一张券码的标识（抖音撤销时需要）
     */
    private String certificateId;

    /**
     * 凭证归属支付宝用户id
     */
    private String userId;

    /**
     * 购买商品的订单id，核销接口使用
     */
    private String orderId;

    /**
     * 订单金额
     */
    private BigDecimal orderFee;

    /**
     * 验券集合，用做抖音验券记录
     */
    private List<GrouponSingleDTO> grouponList;

    /**
     * 券码渠道 买单:1004
     */
    private Integer receiptChannel;

    /**
     * 一键买单券信息
     */
    private MtMaitonConsumeDTO maitonConsumeDTO;

}
