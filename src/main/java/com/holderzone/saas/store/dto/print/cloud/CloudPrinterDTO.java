package com.holderzone.saas.store.dto.print.cloud;

import com.holderzone.saas.store.dto.print.PrinterDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/26
 * @description 云打印机
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "云打印机", description = "云打印机")
public class CloudPrinterDTO extends PrinterDTO implements Serializable {

    private static final long serialVersionUID = -1737792018874738749L;

    @ApiModelProperty(value = "设备用途")
    private List<DeviceUseDTO> deviceUse;

}
