package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("获取门店列表请求参数")
@Builder
@RequiredArgsConstructor
@Accessors(chain = true)
public class WxPrepayRespDTO {

	@ApiModelProperty(value = "0：成功，1：失败")
	@NonNull
	private Integer result;

	@ApiModelProperty(value = "失败原因")
	private String errorMsg;

	public static WxPrepayRespDTO changeFailed(){
		return new WxPrepayRespDTO(1, "订单详情发生变化");
	}

	public static WxPrepayRespDTO emptySuccess(){
		return new WxPrepayRespDTO(0);
	}

}
