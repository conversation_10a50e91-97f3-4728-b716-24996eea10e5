package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SalesVolumeRespDTO
 * @date 2019/12/25 18:28
 * @description 商品销量统计返回参数
 * @program holder-saas-store
 */
@Data
@ApiModel("商品销量统计返回参数")
public class SalesVolumeRespDTO {

    @ApiModelProperty("商品名字")
    private String itemName;

    @ApiModelProperty("分类名字")
    private String typeName;

    @ApiModelProperty("商品类型名字")
    private String itemTypeName;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("订单数量")
    private Double orderCount;

    @ApiModelProperty("商品销量")
    private Double salesVolume;

    @ApiModelProperty("退菜量")
    private Double refundCount;

    @ApiModelProperty("赠送量")
    private Double freeCount;

    @ApiModelProperty("销售额")
    private BigDecimal salesAmount;

    @ApiModelProperty("商品实付金额")
    private BigDecimal discountPrice;

    @ApiModelProperty("商品堂食实付金额")
    private BigDecimal dineInDiscountPrice;

    @ApiModelProperty("销售额占比")
    private Double salesProportion;

    @ApiModelProperty("堂食收入占比")
    private Double salesDineInProportion;

    @ApiModelProperty("点单率")
    private Double spotRate;

    @ApiModelProperty(value = "毛利润")
    private BigDecimal grossProfitAmount;

    private String storeGuid;

    private String storeName;

    private String brandGuid;

    private String brandName;
}
