package com.holderzone.saas.store.dto.trade.req.record;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/10/17
 * @description 会员操作记录请求
 */
@Data
public class MemberOperateRecordReqDTO implements Serializable {

    private static final long serialVersionUID = -4240309972907495219L;

    @ApiModelProperty(value = "设备类型 BaseDeviceTypeEnum 3一体机 4pos机")
    private Integer deviceType;

    @ApiModelProperty(value = "登录方式 0-扫码 1-手机号录入")
    private Integer loginType;

    @ApiModelProperty(value = "操作模块 1正餐 2快餐 3会员 4挂账")
    private Integer moduleType;

    @ApiModelProperty(value = "交易模式(0：正餐，1：快餐)")
    private Integer tradeMode;

    @ApiModelProperty(value = "操作员guid")
    private String operatorGuid;

    @ApiModelProperty(value = "会员手机号")
    private String phoneNum;

    @ApiModelProperty(value = "操作员名称")
    private String operatorName;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;
}
