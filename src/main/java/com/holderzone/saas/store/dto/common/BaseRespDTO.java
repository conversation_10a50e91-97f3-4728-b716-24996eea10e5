package com.holderzone.saas.store.dto.common;

import com.holderzone.saas.store.enums.common.ResultStateEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 返回公共实体
 * @date 2021/8/6 10:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("返回公共实体")
public class BaseRespDTO implements Serializable {

    private static final long serialVersionUID = -4292297844105201112L;

    @ApiModelProperty("返回guid，maybe null")
    private String returnGuid;

    /**
     * 结果状态 0成功
     *
     * @see com.holderzone.saas.store.enums.common.ResultStateEnum
     */
    @ApiModelProperty("结果状态 0成功 1失败")
    private Integer resultState;

    @ApiModelProperty("提示信息，成功为空")
    private String promptMsg;

    /**
     * 构造提示消息
     *
     * @param promptMsg 提示信息
     * @return 只有提示信息的返回
     */
    public static BaseRespDTO constMsg(String promptMsg) {
        return new BaseRespDTO().setPromptMsg(promptMsg).setResultState(ResultStateEnum.FAIL.getCode());
    }

    /**
     * 构造提示消息
     *
     * @param promptMsg   提示信息
     * @param resultState 结果状态
     * @return 只有提示信息的返回
     */
    public static BaseRespDTO constMsgAndState(String promptMsg, Integer resultState) {
        return new BaseRespDTO().setPromptMsg(promptMsg).setResultState(resultState);
    }

    /**
     * 构造成功返回结果
     *
     * @return 成功状态
     */
    public static BaseRespDTO constSuccess() {
        return new BaseRespDTO().setResultState(ResultStateEnum.SUCCESS.getCode());
    }

    /**
     * 请求参数为空的返回构造
     *
     * @return 只有提示信息的返回
     */
    public static BaseRespDTO constReqMsg() {
        return new BaseRespDTO().setPromptMsg("请求参数为空").setResultState(ResultStateEnum.FAIL.getCode());
    }
}
