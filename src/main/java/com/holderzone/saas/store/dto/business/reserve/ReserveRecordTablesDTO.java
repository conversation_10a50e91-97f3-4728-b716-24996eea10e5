package com.holderzone.saas.store.dto.business.reserve;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordDO
 * @date 2018/07/30 下午2:51
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class ReserveRecordTablesDTO {

    /**
     * 门店guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    /**
     * 预订时段guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "预订时段guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "预订时段guid", required = true)
    private String reservePeriodGuid;

    /**
     * 营业日日期
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "营业日日期", required = true)
    private LocalDate businessDay;

    /**
     * 状态过滤集合
     */
    @ApiModelProperty(value = "状态过滤集合，null/empty查询全部名，否则根据给定条件查询", required = true)
    private List<Integer> statusCollection;
}
