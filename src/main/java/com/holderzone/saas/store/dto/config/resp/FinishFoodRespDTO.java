package com.holderzone.saas.store.dto.config.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/19
 * @description 出餐配置返回
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "出餐配置返回", description = "出餐配置返回")
public class FinishFoodRespDTO implements Serializable {

    private static final long serialVersionUID = 7285247089861640419L;

    @ApiModelProperty(value = "备餐时间")
    private Integer prepTime;

    @ApiModelProperty(value = "快餐出餐语音开关：0关闭 1开启")
    private Integer finishFoodVoiceSwitch;

}
