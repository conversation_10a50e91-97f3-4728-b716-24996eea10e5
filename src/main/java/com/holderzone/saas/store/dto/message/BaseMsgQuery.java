package com.holderzone.saas.store.dto.message;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseMsgQuery
 * @date 2018/09/25 10:18
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public abstract class BaseMsgQuery implements Serializable{

    @ApiModelProperty(value = "当前页数")
    protected int currentPage = 1;

    @ApiModelProperty(value = "每页多少数据")
    protected int pageSize = 10;

    @ApiModelProperty(value = "门店Guid")
    protected String storeGuid;

    protected LocalDateTime beginTime;

    protected LocalDateTime endTime;

}
