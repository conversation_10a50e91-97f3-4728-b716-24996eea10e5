package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeWebRespDTO
 * @date 2019/01/04 下午5:14
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "分类的查询结果实体")
public class TypeWebRespDTO implements Serializable {
    /**
     * typeGUID
     */
    @ApiModelProperty(value = "分类GUID（唯一标识）")
    private String typeGuid;

    /**
     * 菜单类别名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 品牌GUID
     */
    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    /**
     * 门店GUID
     */
    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 是否启用（0：否，1：是）
     */
    @ApiModelProperty(value = "是否启用（0：否，1：是）")
    private Integer isEnable;

    /**
     * icon地址
     */
    @ApiModelProperty(value = "icon地址")
    private String iconUrl;

    /**
     * 关联的商品数量
     */
    @ApiModelProperty(value = "关联的商品数量")
    private Integer itemNum;

    /**
     * 上架商品数量
     */
    @ApiModelProperty(value = "上架商品数量")
    private Integer rackItemNum;

    /**
     * 下架商品数量
     */
    @ApiModelProperty(value = "下架商品数量")
    private Integer unRackItemNum;

    @ApiModelProperty(value = "type类型：（0：门店自己创建的type，1：品牌自己创建的type,2:被推送过来的type）")
    private Integer typeFrom;

    /**
     * 菜谱方案GUID
     */
    @ApiModelProperty(value = "菜谱方案GUID")
    private String pricePlanGuid;

    /**
     * 菜谱分类图片类型(0小图,1竖图,2整屏)
     */
    @ApiModelProperty(value = "菜谱分类图片类型(0小图,1竖图,2整屏)")
    private Integer menuClassifyPictureType;

    /**
     * 父实体GUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的实体，则该字段为品牌库对应的实体GUID。
     */
    private String parentGuid;

    @ApiModelProperty(value = "是否必点")
    private Integer isMustPoint;

}
