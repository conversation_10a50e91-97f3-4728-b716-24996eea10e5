package com.holderzone.saas.store.dto.reserve;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableTypeEnum
 * @date 2019/06/05 14:51
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public enum TableTypeEnum {

    HALL(Byte.valueOf("0"), "大厅"),

    COMPARTMENT(Byte.valueOf("1"), "包间|包房|包厢"),

    ;

    private Byte value;

    private String name;

    TableTypeEnum(Byte value, String name) {
        this.value = value;
        this.name = name;
    }

    public Byte getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static TableTypeEnum getByValue(Number value) {
        return Arrays.stream(values()).filter(e -> value.byteValue() == e.getValue()).findFirst().orElse(null);
    }
}