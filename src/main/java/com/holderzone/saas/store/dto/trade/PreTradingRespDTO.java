package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className JhQueryReqDTO
 * @date 2018/08/14 11:45
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class PreTradingRespDTO implements Serializable {

    private static final long serialVersionUID = 674632042244417856L;

    /**
     * 响应码
     * 成功=10000
     */
    @ApiModelProperty(value = "响应码，成功=10000")
    private String code;

    /**
     * 响应信息
     */
    @ApiModelProperty(value = "响应信息")
    private String msg;

    /**
     * 结果
     */
    @ApiModelProperty(value = "结果")
    private String result;

    /**
     * 附加数据
     */
    @ApiModelProperty(value = "附加数据")
    private String attachData;

    @ApiModelProperty(value = "支付guid，如果有，聚合支付轮询参数")
    private String payGuid;

    @ApiModelProperty(value = "聚合支付订单guid，如果有，聚合支付轮询参数")
    private String jhOrderGuid;


    public PreTradingRespDTO(Builder builder) {
        this.code = builder.code;
        this.msg = builder.msg;
        this.attachData = builder.attachData;
        this.result = builder.result;
    }
    public PreTradingRespDTO(){}

    public static class Builder {

        private String code;

        private String msg;

        private String result;

        private String attachData;

        public static Builder builder() {
            return new Builder();
        }

        public Builder code(String code) {
            this.code = code;
            return this;
        }

        public Builder msg(String msg) {
            this.msg = msg;
            return this;
        }

        public Builder result(String result) {
            this.result = result;
            return this;
        }

        public Builder attachData(String attachData) {
            this.attachData = attachData;
            return this;
        }


        public PreTradingRespDTO build() {
            return new PreTradingRespDTO(this);
        }
    }
}
