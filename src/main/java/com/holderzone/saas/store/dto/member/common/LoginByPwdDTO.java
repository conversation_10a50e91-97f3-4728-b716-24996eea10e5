package com.holderzone.saas.store.dto.member.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 密码登录请求实体
 * @date 2021/8/6 16:48
 */
@ApiModel("密码登录请求实体")
@Data
public class LoginByPwdDTO {

    @ApiModelProperty("手机号")
    private String phoneNum;

    @ApiModelProperty("登录密码")
    private String payPwd;

    @Override
    public String toString() {
        return "LoginByPwdDTO={" +
                "phoneNum='" + phoneNum + '\'' +
                ", payPwd='" + payPwd + '\'' +
                '}';
    }
}
