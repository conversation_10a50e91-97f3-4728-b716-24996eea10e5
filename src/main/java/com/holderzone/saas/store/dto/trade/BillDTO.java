package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillDTO
 * @date 2018/07/26 13:45
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class BillDTO extends BaseBillDTO {

    /**
     * 订单guid
     */
    private String orderGuid;

    /**
     * 客人数
     */
    private Integer guestCount;

    /**
     * 桌台Guid
     */
    private String diningTableGuid;

    /**
     * 桌台名称
     */
    private String diningTableName;

    /**
     * 交易类型(0：堂食 1：快销  2：外卖 )
     */
    private Integer tradeMode;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 会员卡Guid
     */
    private String memberCardGuid;
    /**
     * 是否使用会员卡
     */
    private Boolean useMemberCard;
    /**
     * 营业日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate businessDayStamp;
    /**
     * 开台时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime openTableTimestamp;
    /**
     * 开始结算时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkoutTimestamp;
    private String checkoutStaffGuid;
    private String checkoutStaffName;

    /**
     * 状态(0：未完成 1：已完成 2：已取消、3：反结账状态)
     */
    private Integer state;

    @ApiModelProperty(value = "订单来源，0:一体机，1:pos设备，2:平板点餐，3:微信公众号，4:美团外卖，5:饿了么外卖")
    private Integer orderSource;

    /**
     * 反结账UUID
     */
    private String recoveryUuid;
    /**
     * 会员权益折扣（打几折  如：90%）
     */
    private BigDecimal memberPercentage;
    /**
     * 菜品信息
     */
    private List<BillDishDTO> billDishDTOList;

    @ApiModelProperty(value = "附加费集合")
    private List<AddtionalFeeDTO> addtionalFeeDTOS;

}
