package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentRespDTO
 * @date 2018/08/31 16:17
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentRespDTO implements Serializable {

    @ApiModelProperty(value = "支付响应实体，返回10000表示成功，如果是聚合支付标示会话成功，不代表支付成")
    private String code;

    @ApiModelProperty(value = "msg")
    private String msg;

    @ApiModelProperty(value = "业务主键")
    private String paymentGuid;

    @ApiModelProperty(value = "如果是聚合支付，会返回对应支付单payGuid，轮询需要传入")
    private String payGuid;

    @ApiModelProperty(value = "如果是聚合支付，jhPayGuid，轮询需要传入")
    private String jhPayGuid;

    public static PaymentRespDTO errorPaymentResp(String code, String msg) {

        PaymentRespDTO paymentRespDTO = new PaymentRespDTO();
        paymentRespDTO.setCode(code);
        paymentRespDTO.setMsg(msg);
        return paymentRespDTO;

    }
    public static PaymentRespDTO successPaymentResp(String code, String msg) {

        PaymentRespDTO paymentRespDTO = new PaymentRespDTO();
        paymentRespDTO.setCode(code);
        paymentRespDTO.setMsg(msg);
        return paymentRespDTO;

    }
}
