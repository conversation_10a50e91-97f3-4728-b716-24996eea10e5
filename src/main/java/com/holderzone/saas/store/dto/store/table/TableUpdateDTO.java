package com.holderzone.saas.store.dto.store.table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableDO
 * @date 2018/07/24 上午9:55
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class TableUpdateDTO implements Serializable {

    private static final long serialVersionUID = 7754799897607079964L;

    /**
     * 桌台guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "桌台guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "桌台guid", required = true)
    private String tableGuid;

    /**
     * 区域guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "区域guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "区域guid", required = true)
    private String areaGuid;

    /**
     * 区域名称
     */
    @NotNull
    @Size(min = 1, max = 45, message = "区域名称不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "区域名称", required = true)
    private String areaName;

    /**
     * 桌台名称
     */
    @NotNull
    @Size(min = 1, max = 45, message = "桌台名称不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "桌台名称", required = true)
    private String name;

    /**
     * 桌台编号
     */
    @NotNull
    @Pattern(regexp = "^\\d{1,8}$", message = "桌台编号不得为空，且不得超过8个数字")
    @ApiModelProperty(value = "桌台编号", required = true)
    private String code;

    /**
     * 桌台座位数
     */
    @NotNull
    @Min(value = 1, message = "桌台座位数[1-255]")
    @Max(value = 255, message = "桌台座位数[1-255]")
    @ApiModelProperty(value = "桌台座位数", required = true)
    private Integer seats;

    /**
     * 是否已启用
     * 0=未启用
     * 1=已启用
     * 默认1
     */
    @NotNull
    @Min(value = 0, message = "是否启用不得为空，且0=未启用，1=已启用")
    @Max(value = 1, message = "是否启用不得为空，且0=未启用，1=已启用")
    @ApiModelProperty(value = "是否已启用。0=未启用，1=已启用。默认1。", required = true)
    private Integer enable;
}
