package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 该DTO类的字段几乎是OMessage的字段定义
 * 不使用OMessage而是用该DTO的原因是：添加注释、添加Validator验证等
 */
@Data
@ApiModel
@NoArgsConstructor
public class EleCallbackDTO implements Serializable {

    private static final long serialVersionUID = 3972049868601452558L;
}
