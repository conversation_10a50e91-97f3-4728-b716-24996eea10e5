package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Api("返回会员卡")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxMemberCardListReqDTO {
	@ApiModelProperty("平台会员GUID")
	private String openId;
	@ApiModelProperty("企业GUID")
	private String enterpriseGuid;
	@ApiModelProperty("品牌GUID，精确到品牌就需要传，只要企业级别可为空")
	private String brandGuid;
	@ApiModelProperty("门店guid")
	private String storeGuid;
}
