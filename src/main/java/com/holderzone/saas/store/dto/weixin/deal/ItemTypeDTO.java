package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Api("商品类型")
@Accessors(chain = true)
public class ItemTypeDTO {
	@ApiModelProperty(value = "分类Guid")
	private String typeGuid;
	@ApiModelProperty(value = "0:标签，1：分类")
	private Integer isType;
	@ApiModelProperty(value = "分类或标签名称")
	private String name;
	@ApiModelProperty(value = "排序",hidden = true)
	private Integer sort;
	@ApiModelProperty(value = "可能空，图标的URL")
	private String iconUrl;
	@ApiModelProperty(value = "可能空，描述")
	private String description;

	@ApiModelProperty(value = "是否必点")
	private Integer isMustPoint;


}
