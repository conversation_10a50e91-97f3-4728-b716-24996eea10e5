package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "mdm商品返回实体")
public class SelectItemDTO implements Serializable {
    private static final long serialVersionUID = -1118396942531553674L;
    @ApiModelProperty("主键")
    private String guid;
    @ApiModelProperty("第三方平台唯一标识")
    private String thirdNo;
    @ApiModelProperty("商品名称")
    private String name;
    @ApiModelProperty("商品关联的分类GUID")
    private String typeGuid;
    @ApiModelProperty("门店GUID")
    private String storeGuid;
    @ApiModelProperty("品牌GUID")
    private String brandGuid;
    @ApiModelProperty("商品来源（0：门店，1：品牌）")
    private Integer itemFrom;
    @ApiModelProperty("商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品")
    private Integer itemType;
    @ApiModelProperty("商品图片,图片路径数组json")
    private String pictureUrl;
    @ApiModelProperty("拼音简码")
    private String pinyin;
    @ApiModelProperty("商品编码")
    private Byte itemCode;
    @ApiModelProperty("商品条形码")
    private String itemBar;
    @ApiModelProperty("别名")
    private String alias;
    @ApiModelProperty("商品描述")
    private String description;
    @ApiModelProperty("参考价")
    private BigDecimal price;
    @ApiModelProperty("计价单位")
    private String unit;
    @ApiModelProperty("企业Guid")
    private String enterpriseGuid;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("商品的sku集合")
    private List<SelectItemSkuDTO> itemSkuDOList;

}