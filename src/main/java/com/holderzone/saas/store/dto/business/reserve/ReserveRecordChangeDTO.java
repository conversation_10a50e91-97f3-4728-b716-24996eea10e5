package com.holderzone.saas.store.dto.business.reserve;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordDO
 * @date 2018/07/30 下午2:51
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReserveRecordChangeDTO {

    /**
     * 预订记录guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "预订记录guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "预订记录guid", required = true)
    private String reserveRecordGuid;

    /**
     * 桌台区域guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "桌台区域guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "桌台区域guid", required = true)
    private String areaGuid;

    /**
     * 桌台区域名
     */
    @NotNull
    @Size(min = 1, max = 45, message = "桌台区域名不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "桌台区域名", required = true)
    private String areaName;

    /**
     * 桌台guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "桌台guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "桌台guid", required = true)
    private String tableGuid;

    /**
     * 桌台名
     */
    @NotNull
    @Size(min = 1, max = 45, message = "桌台名不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "桌台名", required = true)
    private String tableName;
}
