package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class ItemSkuMonthlySaleIncDTO {

    @ApiModelProperty(value = "规格集合", required = true)
    @NotEmpty(message = "规格集合不能为空")
    private List<@Valid SkuMonthlySaleDTO> skuList;
}
