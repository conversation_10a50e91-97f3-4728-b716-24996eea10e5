package com.holderzone.saas.store.dto.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayRespDTO
 * @date 2019/03/14 10:28
 * @description 聚合支付预下单resp
 * @program holder-saas-store-dto
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class AggPayRespDTO {

    @ApiModelProperty(value = "支付响应实体，返回10000表示成功，如果是聚合支付标示会话成功，不代表支付成")
    private String code;

    @ApiModelProperty(value = "msg")
    private String msg;

    @ApiModelProperty("结果")
    private String result;

    @ApiModelProperty(value = "如果是聚合支付，会返回对应支付单payGuid，轮询需要传入")
    private String payGuid;

    @ApiModelProperty(value = "如果是聚合支付，会返回订单号orderGuid，轮询需要传入")
    private String orderGuid;

    @ApiModelProperty(value = "聚合支付帐号guid")
    private String paymentAppId;

    public static AggPayRespDTO errorPaymentResp(String code, String msg) {

        AggPayRespDTO paymentRespDTO = new AggPayRespDTO();
        paymentRespDTO.setCode(code);
        paymentRespDTO.setMsg(msg);
        return paymentRespDTO;

    }

    public static AggPayRespDTO successPaymentResp(String code, String msg) {

        AggPayRespDTO paymentRespDTO = new AggPayRespDTO();
        paymentRespDTO.setCode(code);
        paymentRespDTO.setMsg(msg);
        return paymentRespDTO;

    }

}
