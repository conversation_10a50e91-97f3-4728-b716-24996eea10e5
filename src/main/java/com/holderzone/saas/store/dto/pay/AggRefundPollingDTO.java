package com.holderzone.saas.store.dto.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggRefundPollingDTO
 * @date 2019/03/15 14:52
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class AggRefundPollingDTO {

    @ApiModelProperty(value = "商户订单GUID")
    private String orderGUID;

    @ApiModelProperty(value = "商户透传数据")
    private String attachData;

    @ApiModelProperty(value = "平台退款单号，如果有这个参数，只会返回一条记录")
    private String refundNo;

    @ApiModelProperty(value = "支付唯一标示")
    private String payGUID;

    @ApiModelProperty(value = "商户唯一标识，appId")
    private String appId;

    @ApiModelProperty(value = "发起请求的时间")
    private Long timestamp;

    @ApiModelProperty(value = "开发者ID")
    private String developerId;

    @ApiModelProperty(value = "签名")
    private String signature;


}
