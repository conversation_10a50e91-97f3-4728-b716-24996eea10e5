package com.holderzone.saas.store.dto.takeaway.response;

import lombok.Data;

import java.util.List;

@Data
public class MtMaitonConsumeDTO {

    /**
     * 整单：用户消费总金额
     */
    private String totalOriginalPrice;

    /**
     * 券码：总抵扣券金额
     */
    private String totalVoucherOriginalPrice;

    /**
     * 券码：总商家实收
     */
    private String totalVoucherBuyPrice;

    /**
     * 整单：商家总实收
     */
    private String totalBuyPrice;

    /**
     * 买单：买单尾款金额
     */
    private String maitonPayPrice;

    /**
     * 券码：抵扣券数量
     */
    private Integer voucherNum;

    /**
     * 券码：抵扣券信息
     */
    private List<MtMaitonPromotionDTO> maitonPromotionDTOList;

    /**
     * 买单：买单订单号
     */
    private String maitonOrderId;
}
