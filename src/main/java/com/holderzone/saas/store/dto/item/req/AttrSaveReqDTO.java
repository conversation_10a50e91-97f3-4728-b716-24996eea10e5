package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.item.common.MchntItemBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AttrSaveReqDTO
 * @date 2019/2/12 17:48
 * @description 属性值保存DTO
 * @program holder-saas-store-business
 */
@ApiModel("属性值保存DTO")
@Data
public class AttrSaveReqDTO extends MchntItemBaseDTO {

    @ApiModelProperty("属性值guid")
    @NotBlank(message = "attrGuid不能为空")
    private String attrGuid;

    @ApiModelProperty("属性值名称")
    @Size(max = 20)
    private String name;

    @ApiModelProperty(value = "加价")
    @DecimalMin("0.00")
    @DecimalMax("99.99")
    private BigDecimal price;

    @ApiModelProperty(value = "选取的适用分类的GUID集合")
    private List<String> typeGuidList;
}
