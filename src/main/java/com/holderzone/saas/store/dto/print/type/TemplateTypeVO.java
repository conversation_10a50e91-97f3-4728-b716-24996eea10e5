package com.holderzone.saas.store.dto.print.type;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/23
 * @description 模版分类展示
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "模版分类展示", description = "模版分类展示")
public class TemplateTypeVO implements Serializable {

    private static final long serialVersionUID = -292225122119531364L;

    @ApiModelProperty(value = "'分类guid'")
    private String typeGuid;

    @ApiModelProperty(value = "分类名称")
    private String typeName;

    @ApiModelProperty(value = "商品列表")
    private List<TemplateItemVO> itemList;
}
