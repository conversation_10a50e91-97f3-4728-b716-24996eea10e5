package com.holderzone.saas.store.dto.queue;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class QueueWechatDTO {

    @ApiModelProperty("品牌guid")
    @NotEmpty(message = "品牌信息不能为空")
    private String brandGuid;

    @ApiModelProperty("用户guid")
    @NotEmpty(message = "用户信息不能为空")
    private String userGuid;

	@ApiModelProperty("状态 0:队列中,1:过号,2:叫号中,3:以就餐,4:已取消")
    private Byte status;

    private String enterpriseGuid;
}
