package com.holderzone.saas.store.dto.retail.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DiscountFeeDetailDTO
 * @date 2019/01/28 11:17
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class DiscountFeeDetailDTO {

    @ApiModelProperty(value = "折扣guid")
    private String guid;

    @ApiModelProperty(value = "折扣方式名字")
    private String discountName;

    /**
     * {@link com.holderzone.saas.store.trade.entity.enums.DiscountTypeEnum}
     */
    @ApiModelProperty(value = "1-会员折扣，2-整单折扣，3-整单让价,4-系统省零，5-赠送优惠，6-团购验券，7-会员优惠券,8-积分抵扣")
    private Integer discountType;

    @ApiModelProperty(value = "折扣总额")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "打几折（整单折扣和会员折扣）")
    private BigDecimal discount;

    @ApiModelProperty(value = "折扣规则：是否计算积分1：计算，2：不计算")
    private String rule;


}
