package com.holderzone.saas.store.dto.member.request;

import com.holderzone.holder.saas.member.wechat.dto.member.RequestMemberBasic;
import com.holderzone.saas.store.dto.member.common.LoginByPwdDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description pad登陆请求实体
 * @date 2021/8/6 16:04
 */
@ApiModel("pad登陆请求实体")
@Data
public class PadLoginMemberReqDTO {

    @ApiModelProperty("登录方式 1短信验证码登录 2帐号密码登录 3第三方授权登录")
    private Integer loginType;

    @ApiModelProperty("短信验证码登录请求实体")
    private RequestMemberBasic memberBasic;

    @ApiModelProperty("帐号密码登录请求实体")
    private LoginByPwdDTO loginByPwdDTO;

    @ApiModelProperty("微信授权码")
    private String authCode;

    /**
     * 运营主体guid
     */
    @ApiModelProperty("运营主体guid")
    private String operSubjectGuid;

    @Override
    public String toString() {
        return "PadLoginMemberReqDTO={" +
                "loginType=" + loginType +
                ", memberBasic=" + memberBasic +
                ", loginByPwdDTO=" + loginByPwdDTO +
                '}';
    }
}
