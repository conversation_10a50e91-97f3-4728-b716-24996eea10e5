package com.holderzone.saas.store.dto.weixin.deal;

import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRight;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@ApiModel("会员卡")
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Data
public class MemberCardItemDTO {
	@ApiModelProperty("会员卡二维码")
	private String cardQRCode;
	@ApiModelProperty("卡GUID（包括副卡）")
	private String cardGuid;
	@ApiModelProperty("会员持卡GUID")
	private String memberInfoCardGuid;
	@ApiModelProperty("卡体系GUID")
	private String systemManagementGuid;
	@ApiModelProperty("会员拥有体系GUID")
	private String enterpriseMemberInfoSystemGuid;
	@ApiModelProperty("卡名称")
	private String cardName;
	@ApiModelProperty("卡LOGO,本期无")
	private String cardLogo;
	@ApiModelProperty("卡片图片路径")
	private String cardIcon;
	@ApiModelProperty("卡颜色")
	private String cardColour;
	@ApiModelProperty("有效期类型，0永久有效，2固定时间")
	private Integer validityType;
	@ApiModelProperty("开始有效期")
	private String cardStartDate;
	@ApiModelProperty("结束有效期")
	private String cardEndDate;
	@ApiModelProperty("卡类型,0成长型(默认卡),1荣誉型,2付费型,3实体卡")
	private Integer cardType;
	@ApiModelProperty("卡片编号")
	private String systemManagementCardNum;
	@ApiModelProperty("卡余额")
	private BigDecimal cardMoney;
	@ApiModelProperty("卡赠送余额")
	private BigDecimal giftMoney;
	@ApiModelProperty("积分")
	private String cardIntegral;
	@ApiModelProperty("卡成长值")
	private String cardGrowthValue;
	@ApiModelProperty("卡等级GUID")
	private String cardLevelGuid;
	@ApiModelProperty("卡等级名字")
	private String cardLevelName;
	@ApiModelProperty("卡等级编号,V-(n)")
	private Integer cardLevelNum;
	@ApiModelProperty("等级图标路径")
	private String levelIcon;
	@ApiModelProperty(value = "1：表示选中，0：表示未选中")
	private Integer uck=0;
	@ApiModelProperty(value = "1:表示当前会员卡，1选中积分，0：表示未选中")
	private Integer uckIntegral=1;
	@ApiModelProperty(value = "1:有权益数组，0：没有")
	private Integer uckCardRight=0;
	@ApiModelProperty(value = "权益数组")
	private List<ResponseCardRight> cardRights;
}
