package com.holderzone.saas.store.dto.journaling.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.lang.NonNull;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DailyPrintReqDTO
 * @date 2019/10/31 下午6:53
 * @description //营业日报打印类型请求入参
 * @program holder
 */

@Data
@ApiModel(value = "营业日报打印类型请求入参")
public class DailyPrintReqDTO extends  JournalStoreAppBaseReqDTO{

    @Valid
    @NotEmpty(message = "打印入参 type 不能为空  1：分类销售统计  2：商品销售统计  3：退货统计  4：赠送统计  5：营业概况 ")
    @ApiModelProperty(value = "打印入参  1：分类销售统计  2：商品销售统计  3：退货统计  4：赠送统计  5：营业概况  ")
    private Integer type;

    @Valid
    @NotEmpty(message = "打印入参 设备号deviceId 不能为空 ")
    @ApiModelProperty(value = "设备号")
    private  String deviceId;
}
