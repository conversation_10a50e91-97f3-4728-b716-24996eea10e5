package com.holderzone.saas.store.dto.takeaway.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
@NoArgsConstructor
public class TakeoutItemMappingReqDTO implements Serializable {

    private static final long serialVersionUID = 6529834582976862827L;

    @NotNull(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid", required = true)
    private String storeGuid;

    /**
     * 品牌guid
     */
    private String brandGuid;

    @NotNull(message = "外卖类型不得为空")
    @Min(value = 0, message = "外卖类型(0：美团，1：饿了么,2:自营,3:赚餐,4:抖音)")
    @Max(value = 5, message = "外卖类型(0：美团，1：饿了么,2:自营,3:赚餐,4:抖音)")
    @ApiModelProperty(value = "外卖类型(0：美团，1：饿了么,2:自营,3:赚餐,4:抖音)", required = true)
    private Integer takeoutType;
}
