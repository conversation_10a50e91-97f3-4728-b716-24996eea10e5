package com.holderzone.saas.store.dto.order.request.item;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemReturnReqDTO
 * @date 2019/01/23 11:27
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class ItemReturnOrFreeReqDTO extends BaseDTO{

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "数量")
    private BigDecimal count;

    @ApiModelProperty(value = "标记退货是否为赠送（0：否，1：是）")
    private Integer isFree;

    @ApiModelProperty(value = "标记赠送/退货是否被划菜（0：否，1：是）")
    private Integer isServe;

    @ApiModelProperty(value = "guid", required = true)
    private String guid;

    @ApiModelProperty(value = "退货的商品之前的赠送记录guid", required = true)
    private String freeGuid;


}
