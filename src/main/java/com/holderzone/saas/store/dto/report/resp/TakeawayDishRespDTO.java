package com.holderzone.saas.store.dto.report.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeAwayDishRespDTO
 * @date 2018/12/05 11:18
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
public class TakeawayDishRespDTO implements Serializable {

    @ApiModelProperty(value = "排序字段")
    private Integer sort;

    @ApiModelProperty(value = "菜品名称")
    private String dishName;

    @ApiModelProperty(value = "销售数量")
    private Integer salesCount;

    @ApiModelProperty(value = "销售总额")
    private BigDecimal salesTotal;

    @ApiModelProperty(value = "菜品来源 0-美团 1-饿了么")
    private String dishSource;


    public String getDishSource() {
        return dishSource;
    }

    public void setDishSource(String dishSource) {
        this.dishSource = dishSource;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getDishName() {
        return dishName;
    }

    public void setDishName(String dishName) {
        this.dishName = dishName;
    }

    public Integer getSalesCount() {
        return salesCount;
    }

    public void setSalesCount(Integer salesCount) {
        this.salesCount = salesCount;
    }

    public BigDecimal getSalesTotal() {
        return salesTotal;
    }

    public void setSalesTotal(BigDecimal salesTotal) {
        this.salesTotal = salesTotal;
    }
}
