package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program holder-saas-store-dto
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@ApiModel
public class OpenTableOrderDTO extends BaseDTO {

    @ApiModelProperty("桌台guid(并台时存在)")
    private List<String> tableGuidList;

    @ApiModelProperty("订单guid")
    private String orderGuid;

    @ApiModelProperty("门店guid")
    private String storeGuid;

}
