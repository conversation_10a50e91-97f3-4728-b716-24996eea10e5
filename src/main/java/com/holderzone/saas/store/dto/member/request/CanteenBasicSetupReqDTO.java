package com.holderzone.saas.store.dto.member.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 一体机基本设置入参
 * @date 2021/6/16 15:50
 */

@Data
@Accessors(chain = true)
@ApiModel("一体机基本设置入参")
@NoArgsConstructor
public class CanteenBasicSetupReqDTO implements Serializable {

    private static final long serialVersionUID = -8813613825281330932L;

    @ApiModelProperty("是否启用组合支付")
    @NonNull
    private Integer isEnableCombinationPayment;

    /**
     * 运营主体Guid
     */
    @NonNull
    @ApiModelProperty(value = "运营主体Guid")
    private String operSubjectGuid;

    /**
     * 企业guid
     */
    @NonNull
    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty("余额规则guid,编辑时不为空")
    private String balanceRuleGuid;

    @ApiModelProperty("余额扣款顺序 例[2,1,3]")
    private List<Integer> deductionBalanceOrder;

}
