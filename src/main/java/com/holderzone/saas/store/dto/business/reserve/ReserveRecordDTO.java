package com.holderzone.saas.store.dto.business.reserve;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveRecordDO
 * @date 2018/07/30 下午2:51
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class ReserveRecordDTO {

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 订单guid
     */
    @ApiModelProperty(value = "订单guid")
    private String orderGuid;


    /**
     * 桌台区域guid
     */
    @ApiModelProperty(value = "桌台区域guid")
    private String areaGuid;

    /**
     * 桌台区域名字
     */
    @ApiModelProperty(value = "桌台区域名字")
    private String areaName;

    /**
     * 桌台guid
     */
    @ApiModelProperty(value = "桌台guid")
    private String tableGuid;

    /**
     * 桌台名字
     */
    @ApiModelProperty(value = "桌台名字")
    private String tableName;

    /**
     * 预订记录guid
     */
    @ApiModelProperty(value = "预订记录guid")
    private String reserveRecordGuid;

    /**
     * 预订时段guid
     */
    @ApiModelProperty(value = "预订时段guid")
    private String reservePeriodGuid;

    /**
     * 创建人guid
     */
    @ApiModelProperty(value = "创建人guid")
    private String createUserGuid;

    /**
     * 创建人名字
     */
    @ApiModelProperty(value = "创建人名字")
    private String createUserName;

    /**
     * 完成人guid
     */
    @ApiModelProperty(value = "完成人guid")
    private String confirmUserGuid;

    /**
     * 完成人名字
     */
    @ApiModelProperty(value = "完成人名字")
    private String confirmUserName;

    /**
     * 撤销人guid
     */
    @ApiModelProperty(value = "撤销人guid")
    private String cancelUserGuid;

    /**
     * 撤销人名字
     */
    @ApiModelProperty(value = "撤销人名字")
    private String cancelUserName;

    /**
     * 客人名字
     */
    @ApiModelProperty(value = "客人名字")
    private String guestName;

    /**
     * 客人性别
     * 0=女性
     * 1=男性
     */
    @ApiModelProperty(value = "客人性别")
    private Integer guestGender;

    /**
     * 客人电话
     */
    @ApiModelProperty(value = "客人电话")
    private String guestTel;

    /**
     * 客人备注
     */
    @ApiModelProperty(value = "客人备注")
    private String guestRemark;

    /**
     * 客人数量
     */
    @ApiModelProperty(value = "客人数量")
    private Integer guestCount;

    /**
     * 客人抵达时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "客人抵达时间")
    private LocalDateTime arrivalTime;

    /**
     * 预订保留时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "预订保留时间")
    private LocalDateTime deadlineTime;

    /**
     * 短信发送次数
     */
    @ApiModelProperty(value = "短信发送次数")
    private Integer smsSentTimes;

    /**
     * 预订状态
     * 0=预订中
     * 1=完成
     * 2=超时
     * 3=作废
     */
    @ApiModelProperty(value = "预订状态：0=预订中，1=完成，2=超时，3=作废")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;

    /**
     * 营业日日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "营业日日期")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate businessDay;
}
