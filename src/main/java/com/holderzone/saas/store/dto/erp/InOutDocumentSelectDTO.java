package com.holderzone.saas.store.dto.erp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/30 下午 13:49
 * @description
 */
@ApiModel
public class InOutDocumentSelectDTO {

    @ApiModelProperty("出入库实体的标识")
    private String guid;

    @ApiModelProperty("供应商Guid")
    private String supplierGuid;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("关联单据(出库单关联的单据)")
    private String contactDocumentGuid;

    @ApiModelProperty("关联单据(入库单关联的单据)")
    private List<String> contactDocumentGuidList;

    @ApiModelProperty("纸质编码")
    private String code;

    @ApiModelProperty("仓库guid")
    private String warehouseGuid;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("单据门店guid")
    private String documentStoreGuid;

    @ApiModelProperty("单据门店名称")
    private String documentStoreName;

    @ApiModelProperty("单据日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="Asia/Shanghai")
    private Date documentDate;

    @ApiModelProperty("单据类型")
    private Integer type;

    @ApiModelProperty("物料总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("应付金额")
    private BigDecimal shouldPayAmount;

    @ApiModelProperty("经办人Guid")
    private String operatorGuid;

    @ApiModelProperty("经办人名称")
    private String operatorName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("单据状态")
    private Integer status;

    @ApiModelProperty("结算类型")
    private Integer settleStatus;

    @ApiModelProperty("单据出入库类型")
    private Integer inOutType;

    @ApiModelProperty("制单人名称")
    private String createStaffName;

    @ApiModelProperty("制单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="Asia/Shanghai")
    private Date gmtCreate;

    @ApiModelProperty("物料明细")
    private List<InOutDocumentDetailSelectDTO> detailList;

    public String getDocumentStoreGuid() {
        return documentStoreGuid;
    }

    public void setDocumentStoreGuid(String documentStoreGuid) {
        this.documentStoreGuid = documentStoreGuid;
    }

    public String getDocumentStoreName() {
        return documentStoreName;
    }

    public void setDocumentStoreName(String documentStoreName) {
        this.documentStoreName = documentStoreName;
    }

    public Integer getSettleStatus() {
        return settleStatus;
    }

    public void setSettleStatus(Integer settleStatus) {
        this.settleStatus = settleStatus;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getCreateStaffName() {
        return createStaffName;
    }

    public void setCreateStaffName(String createStaffName) {
        this.createStaffName = createStaffName;
    }

    public List<String> getContactDocumentGuidList() {
        return contactDocumentGuidList;
    }

    public void setContactDocumentGuidList(List<String> contactDocumentGuidList) {
        this.contactDocumentGuidList = contactDocumentGuidList;
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getInOutType() {
        return inOutType;
    }

    public void setInOutType(Integer inOutType) {
        this.inOutType = inOutType;
    }

    public List<InOutDocumentDetailSelectDTO> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<InOutDocumentDetailSelectDTO> detailList) {
        this.detailList = detailList;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getSupplierGuid() {
        return supplierGuid;
    }

    public void setSupplierGuid(String supplierGuid) {
        this.supplierGuid = supplierGuid;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getContactDocumentGuid() {
        return contactDocumentGuid;
    }

    public void setContactDocumentGuid(String contactDocumentGuid) {
        this.contactDocumentGuid = contactDocumentGuid;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Date getDocumentDate() {
        return documentDate;
    }

    public void setDocumentDate(Date documentDate) {
        this.documentDate = documentDate;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getShouldPayAmount() {
        return shouldPayAmount;
    }

    public void setShouldPayAmount(BigDecimal shouldPayAmount) {
        this.shouldPayAmount = shouldPayAmount;
    }

    public String getOperatorGuid() {
        return operatorGuid;
    }

    public void setOperatorGuid(String operatorGuid) {
        this.operatorGuid = operatorGuid;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
