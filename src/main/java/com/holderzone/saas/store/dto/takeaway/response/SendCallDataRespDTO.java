package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class SendCallDataRespDTO implements Serializable {

    @ApiModelProperty(value = "access_token", required = true)
    private String access_token;

    @ApiModelProperty(value = "expires_in", required = true)
    private Integer expires_in;
}
