package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WeChatCancelPayReqDTO {

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "作废原因")
    private String reason;

    @ApiModelProperty(value = "是否快餐")
    private Boolean fastFood;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty(value = "0：PC服务端,1：PC平板,2：小店通,3：一体机,4：POS机,5：云平板,6：点菜宝(M1)," +
            "7：PV1(带刷卡的点菜宝),9：厨房显示系统,10: 取餐屏,12：微信 15通吃岛"
    )
    private Integer deviceType;
}
