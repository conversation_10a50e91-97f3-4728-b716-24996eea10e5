package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class KdsPrinterDeleteReqDTO implements Serializable {

    private static final long serialVersionUID = -2773042054434843563L;

    @NotEmpty(message = "打印机Guid不得为空")
    @ApiModelProperty(value = "打印机Guid")
    private String printerGuid;
}
