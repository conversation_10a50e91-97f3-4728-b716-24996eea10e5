package com.holderzone.saas.store.dto.user;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className BaseQuery
 * @date 18-9-13 上午10:12
 * @description
 * @program holder-saas-store-dto
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public abstract class BaseQuery {
    protected int sortType;
    protected String searchConditions;
    protected int pageSize;
    protected int pageIndex;
    protected String orderBy;
    protected int count;
    private Long startDate;
    private Long endDate;
}
