package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
public class MtCallbackSettlementDTO implements Serializable {

    private static final long serialVersionUID = 2108474328072905524L;

    @ApiModelProperty("ERP厂商入驻新美大餐饮平台得到的唯一身份表示")
    @NotBlank(message = "开发者身份不得为空")
    private Integer developerId;

    @ApiModelProperty("数字签名，签名方式与菜品类接口计算签名一致")
    @NotBlank(message = "数字签名不得为空")
    private String sign;

    private String epoiId;

    @ApiModelProperty("订单结算信息详情数据")
    private String tradeDetail;
}
