package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.framework.util.Assert;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutShopBindReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * https://nest-fe.faas.ele.me/openapi/documents/isvoauth
 */
@Data
@ApiModel
@NoArgsConstructor
public class EleCallbackBindDTO {

    @ApiModelProperty(value = "授权码", notes = "授权码")
    private String code;

    @ApiModelProperty(value = "透传字段", notes = "透传字段")
    private String state;

    @ApiModelProperty(value = "授权失败信息", notes = "授权失败信息")
    private String error;

    @JsonProperty("error_description")
    @ApiModelProperty(value = "授权失败的详细原因描述", notes = "授权失败的详细原因描述")
    private String errorDescription;

    @JsonIgnore
    public String getEnterpriseGuid() {
        String[] split = state.split("/");
        Assert.valid(2 == split.length, "透传字段参数错误");
        return split[0];
    }

    @JsonIgnore
    public String getStoreGuid() {
        String[] split = state.split("/");
        Assert.valid(2 == split.length, "透传字段参数错误");
        return split[1];
    }

    public static String getStateUsingGuid(TakeoutShopBindReqDTO takeoutShopBindReqDTO) {
        return takeoutShopBindReqDTO.getEnterpriseGuid() + "/" + takeoutShopBindReqDTO.getStoreGuid();
    }
}
