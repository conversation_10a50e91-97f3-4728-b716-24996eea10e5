package com.holderzone.saas.store.dto.table.trade;

import com.holderzone.saas.store.dto.table.BaseTableDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseTableDTO
 * @date 2019/01/21 15:33
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class TradeTableDTO extends BaseTableDTO {

    @ApiModelProperty(value = "桌台名称")
    private String tableName;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty(value = "区域guid")
    private String areaGuid;
}
