package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.item.common.ItemLogDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemSortUpdateReqDTO
 * @date 2019/2/11 14:43
 * @description 商品排序修改DTO
 * @program holder-saas-store-business
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("菜品排序修改DTO")
@Data
public class ItemSortUpdateReqDTO extends ItemLogDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("菜品guid")
    @NotBlank(message = "菜品guid不能为空")
    private String itemGuid;

    @Min(value = 1, message = "序号最小为1")
    @ApiModelProperty("序号")
    private Integer sort;

    @ApiModelProperty("分类guid")
    private String typeGuid;
}
