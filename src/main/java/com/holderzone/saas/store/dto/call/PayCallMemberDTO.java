package com.holderzone.saas.store.dto.call;

import com.holderzone.saas.store.dto.member.pay.RedPacketOrderSettleCallBackQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 支付通知会员实体
 * @date 2022/3/17 10:19
 * @className: PayCallMemberDTO
 */
@Data
@ApiModel("支付通知会员实体")
@Accessors(chain = true)
public class PayCallMemberDTO implements Serializable {

    private static final long serialVersionUID = -5102645067093467460L;

    @ApiModelProperty("运营主体")
    private String operSubjectGuid;

    @ApiModelProperty("点餐来源")
    private Integer orderSource;

    @ApiModelProperty("消费时间")
    private LocalDateTime consumptionTime;

    @ApiModelProperty("门店Guid")
    private String storeGuid;

    @ApiModelProperty("门店名称")
    private String storeName;

    @ApiModelProperty("订单结算回调红包参数")
    private List<RedPacketOrderSettleCallBackQO> redPacketOrderSettleCallBackQOS;


}
