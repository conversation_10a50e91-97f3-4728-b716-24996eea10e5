package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SubgroupSynRespDTO
 * @date 2019/01/03 下午3:30
 * @description //安桌同步套餐的分组返回实体,与获取套餐详情实体共用
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class SubgroupSynRespDTO implements Serializable {

    private static final long serialVersionUID = -7973837074144582990L;

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "套餐分组的唯一标识", required = true)
    private String subgroupGuid;

    @ApiModelProperty(value = "分组NAME")
    private String name;

    @ApiModelProperty(value = "当前分组中的商品的可选择数量（商品可重复被选）：0：顾客不可选择，其余值：最大可选商品次数", required = true)
    private Integer pickNum;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "套餐分组中包含的可选规格集合", required = true)
    private List<SubItemSkuSynRespDTO> subItemSkuList;
}
