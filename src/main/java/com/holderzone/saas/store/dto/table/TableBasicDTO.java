package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableBasicDTO
 * @date 2019/01/02 17:07
 * @description
 * @program holder-saas-store-dto
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@ApiModel
public class TableBasicDTO extends BaseDTO {

    @ApiModelProperty("业务主键")
    private String guid;

    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("门店名称")
    private String storeName;

    /**
     * 区域guid
     */
    @ApiModelProperty("区域guid")
    private String areaGuid;

    /**
     * 区域名称
     */
    @ApiModelProperty("区域名称")
    private String areaName;

    /**
     * 桌台编号
     */
    @ApiModelProperty("桌台编号")
    private String tableCode;

    /**
     * 桌台座位数
     */
    @ApiModelProperty("桌台座位数")
    private Integer seats;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 是否已启用
     * 0=已启用
     * 1=未启用
     * 默认启用
     */
    @ApiModelProperty("0-已启用，1-未启用")
    private Integer enable;

    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    @ApiModelProperty("0-未删除，1-已删除")
    private Integer deleted;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty("标签列表")
    private List<String> tagList;

    @ApiModelProperty("标签集合")
    private List<TableTagDTO> tableTagDTOS;

}
