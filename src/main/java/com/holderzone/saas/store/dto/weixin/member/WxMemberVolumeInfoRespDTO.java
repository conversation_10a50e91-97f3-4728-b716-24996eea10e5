package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Api("优惠券")
public class WxMemberVolumeInfoRespDTO {
	@ApiModelProperty("会员持卷GUID")
	private String memberVolumeGuid;
	@ApiModelProperty("卷模板GUID")
	private String volumeInfoGuid;
	@ApiModelProperty("券码")
	private String volumeCode;
	@ApiModelProperty("卷名称")
	private String volumeInfoName;
	@ApiModelProperty("优惠券状态，0可使用的，1已失效的")
	private Integer mayUseVolume;
	@ApiModelProperty("开始有效期")
	private LocalDateTime volumeStartDate;
	@ApiModelProperty("结束有效期")
	private LocalDateTime volumeEndDate;
	@ApiModelProperty("会员拥有卷状态,0未使用，1，已使用，2，未生效，3，已过期，4已作废")
	private Integer volumeState;
//	@ApiModelProperty("卷模板的状态,0未过期(发送中),1已过期(保留状态),2停止发放,3作废")
//	private Integer volumeInfoState;
	@ApiModelProperty("卷优惠金额")
	private BigDecimal volumeMoney;
	@ApiModelProperty("卷类型，0代金卷,1折扣卷,2兑换卷,3商品券")
	private Integer volumeType;
	@ApiModelProperty("卷名称")
	private String volumeTypeName;
	@ApiModelProperty("使用门槛满,0无限制，1有限制")
	private Integer useThreshold;
	@ApiModelProperty("满多少可用")
	private BigDecimal useThresholdFull;
	@ApiModelProperty("0:无限制，1:仅限原价购买")
	private Integer whetherOriginPrice;
}
