package com.holderzone.saas.store.dto.report.openapi;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员资金变动明细订单列表
 */
@Data
public class MemberFundingDetailRespDTO implements Serializable {

    private static final long serialVersionUID = -1025167689243809355L;

    /**
     * 资金变动明细id
     */
    private String guid;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 手机号
     */
    private String phoneNum;

    /**
     * 会员GUID
     */
    @JsonIgnore
    private String memberInfoGuid;

    /**
     * 会员持卡GUID
     */
    @JsonIgnore
    private String memberInfoCardGuid;

    /**
     * 会员卡等级名称
     */
    private String cardLevelName;

    /**
     * 卡编号
     */
    private String cardNum;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 实充发生额
     */
    private BigDecimal rechargeAmount;

    /**
     * 赠送发生额
     */
    private BigDecimal giftAmount;

    /**
     * 补贴发生额
     */
    private BigDecimal subsidyAmount;

    /**
     * 充值方式
     */
    private String payName;

    /**
     * 变动时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 变动类型
     * (CASE mf.amount_source_type
     * WHEN 0 THEN
     * '后台调整'
     * WHEN 1  THEN '充值'
     * WHEN 2  THEN '反结账'
     * WHEN 3  THEN '消费'
     * WHEN 4  THEN '后台导入'
     * WHEN 5 THEN '退款'
     * WHEN 6 THEN '赠送'
     * WHEN 7 THEN '补贴活动'
     * WHEN 8 THEN '补贴金过期'
     * WHEN 9 THEN '退卡'
     * END) AS type,
     */
    private Integer amountSourceType;

    /**
     * 操作人
     */
    private String operatorAccountName;
}
