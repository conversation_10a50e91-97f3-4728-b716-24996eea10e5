package com.holderzone.saas.store.dto.orderlog.response;

import com.holderzone.saas.store.dto.orderlog.detail.TakeawayLogDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayOrderLogDetailRespDTO
 * @date 2018/10/09 15:02
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class TakeawayOrderLogDetailRespDTO {
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "订单号")
    private String orderGuid;

    @ApiModelProperty(value = "门店Guid")
    private String StoreGuid;

    @ApiModelProperty(value = "门店名称")
    private String StoreName;
    @ApiModelProperty(value = "菜品列表")
    private List<TakeawayLogDTO> takeawayLogDTOS;

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getOrderGuid() {
        return orderGuid;
    }

    public void setOrderGuid(String orderGuid) {
        this.orderGuid = orderGuid;
    }

    public String getStoreGuid() {
        return StoreGuid;
    }

    public void setStoreGuid(String storeGuid) {
        StoreGuid = storeGuid;
    }

    public String getStoreName() {
        return StoreName;
    }

    public void setStoreName(String storeName) {
        StoreName = storeName;
    }

    public List<TakeawayLogDTO> getTakeawayLogDTOS() {
        return takeawayLogDTOS;
    }

    public void setTakeawayLogDTOS(List<TakeawayLogDTO> takeawayLogDTOS) {
        this.takeawayLogDTOS = takeawayLogDTOS;
    }
}
