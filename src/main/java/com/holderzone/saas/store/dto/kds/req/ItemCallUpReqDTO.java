package com.holderzone.saas.store.dto.kds.req;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class ItemCallUpReqDTO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    @NotEmpty(message = "订单商品Guid列表不得为空")
    @ApiModelProperty(value = "订单商品Guid列表")
    private List<String> orderItemGuidList;

    @ApiModelProperty(value = "是否叫起整单")
    private Boolean isCallAll;

    /**
     * 桌台名称(区域+桌台code)
     */
    @ApiModelProperty(value = "桌台名称(区域+桌台code)")
    private String diningTableName;

}
