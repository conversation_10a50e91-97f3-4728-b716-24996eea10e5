package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PropertySynFirstRespDTO
 * @date 2019/01/03 下午3:13
 * @description //安桌同步商品一级属性的返回实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class AttrGroupWebRespDTO implements Serializable {

    @ApiModelProperty(value = "商品GUID")
    private String itemGuid;
    @ApiModelProperty(value = "商品与属性组关联实体的唯一标识")
    private String itemAttrGroupGuid;
    @ApiModelProperty(value = "商品属性组GUID",required = true)
    private String attrGroupGuid;
    @ApiModelProperty(value = "属性组名称",required = true)
    private String name;
    @ApiModelProperty(value = "是否必选:0 否 1 是",required = true)
    private Integer isRequired;

    @ApiModelProperty(value = "是否多选:0 否 1 是",required = true)
    private Integer isMultiChoice;
    @ApiModelProperty(value = "maybe null,图标地址")
    private String iconUrl;
    @ApiModelProperty(value = "是否有默认属性，1：是，0,否",required = true)
    private Integer withDefault;
    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;
    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;
    @ApiModelProperty(value = "attrGroup来源（0：门店自己创建的attrGroup，1：品牌自己创建的attrGroup,2:被推送过来的attrGroup）")
    private Integer attrGroupFrom;
    @ApiModelProperty(value = "父属性组GUID")
    private String parentGuid;
    @ApiModelProperty(value = "属性详情集合",required = true)
    private List<AttrWebRespDTO> attrList;
}
