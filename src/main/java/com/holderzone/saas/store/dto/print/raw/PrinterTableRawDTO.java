package com.holderzone.saas.store.dto.print.raw;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class PrinterTableRawDTO implements Serializable {

    private static final long serialVersionUID = -7004841021433658238L;

    /**
     * 唯一标识
     */
    private String guid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 打印机guid
     */
    private String printerGuid;

    /**
     * 桌台guid
     */
    private String tableGuid;

    /**
     * 桌台名称
     */
    private String tableName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}