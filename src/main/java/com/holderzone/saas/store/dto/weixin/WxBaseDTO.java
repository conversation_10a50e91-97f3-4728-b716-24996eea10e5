package com.holderzone.saas.store.dto.weixin;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxBaseDTO
 * @date 2019/03/22 16:33
 * @description 微信组织相关BaseDTO
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("微信组织相关BaseDTO")
public class WxBaseDTO extends BaseDTO {
    @ApiModelProperty("品牌guid")
    private String brandGuid;

    @ApiModelProperty("品牌名字")
    private String brandName;
}
