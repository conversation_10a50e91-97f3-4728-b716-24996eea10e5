package com.holderzone.saas.store.dto.trade.req;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description pad下单一体机接单请求实体
 * @date 2021/8/24 11:51
 * @className: PadAcceptOrderReqDTO
 */
@Data
@ApiModel("pad下单一体机接单请求实体")
@AllArgsConstructor
@NoArgsConstructor
public class PadAcceptOrderReqDTO {

    @ApiModelProperty(value = "当前订单guid", required = true)
    private String orderGuid;

    @ApiModelProperty(value = "0:待处理，1：接单，2：拒单，3：未结帐，4：已结账,5:已做废", required = true)
    private Integer orderState;

    @ApiModelProperty(value = "拒绝原因")
    private String denialReason;

    @ApiModelProperty(value = "桌台Guid")
    private String tableGuid;

    @ApiModelProperty(value = "桌台号")
    private String tableName;

    @ApiModelProperty(value = "开台人数")
    private Integer actualGuestsNo;

    @ApiModelProperty(value = "区域名字")
    private String areaName;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "已接单的菜品集合", required = true)
    private List<DineInItemDTO> dineInItemDTOList;
}
