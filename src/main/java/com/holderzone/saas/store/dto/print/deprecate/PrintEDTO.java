package com.holderzone.saas.store.dto.print.deprecate;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 打印
 *
 * <AUTHOR>
 * @date 2018/09/30 14:40
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@Deprecated
public class PrintEDTO implements Serializable {

    private static final long serialVersionUID = -1573818487622287403L;

    /**
     * Key-Value模式：Key
     */
    protected String key;

    /**
     * Key-Value模式：Value
     */
    protected String value;

    /**
     * 对齐方式：0=左对齐，1=两端对齐
     */
    protected String alignEdges;

    /**
     * 文字“-”模拟“点”
     */
    protected String dot;

    /**
     * 坐标模式：横坐标
     * 距行首x+y*256点位,每8个点位=1mm
     */
    protected int xpoint;

    /**
     * 坐标模式：纵坐标
     */
    protected int ypoint;

    /**
     * 打印内容
     */
    protected String content;

    public static PrintEDTO coordinateRow(int xpoint, int ypoint, String content) {
        return new CoordinateRow(xpoint, ypoint, content);
    }

    public static PrintEDTO separator(String dot, String content) {
        return new Separator(dot, content);
    }

    public static PrintEDTO keyValue(String key, String value, String alignEdge) {
        return new KeyValue(key, value, alignEdge);
    }

    public static PrintEDTO section(String content) {
        return new Section(content);
    }

    /**
     * 按坐标打印
     */
    static class CoordinateRow extends PrintEDTO {
        public CoordinateRow(int x, int y, String text) {
            this.xpoint = x;
            this.ypoint = y;
            this.content = text;
        }
    }

    /**
     * 虚线行中间带文本格式
     */
    static class Separator extends PrintEDTO {
        public Separator(String dots, String text) {
            this.dot = dots;
            this.content = text;
        }
    }

    /**
     * 键值对两端对齐格式
     */
    static class KeyValue extends PrintEDTO {
        public KeyValue(String left, String right, String alignEdge) {
            this.key = left;
            this.value = right;
            this.alignEdges = alignEdge;
        }
    }

    /**
     * 普通段落文本
     */
    static class Section extends PrintEDTO implements Serializable {
        public Section(String content) {
            this.content = content;
        }
    }
}
