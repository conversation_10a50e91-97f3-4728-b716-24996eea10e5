package com.holderzone.saas.store.dto.reserve;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;


/**
 * 预定 待处理统计
 */
@Data
public class ReserveCommitStaticsDTO {

    /**
     * 待处理订单总数
     */
    private Long total;

    /**
     * 近30日统计
     */
    private List<InnerDay> days;

    /**
     * 每日统计
     */
    @Data
    public static class InnerDay {

        /**
         * 时间
         */
        private LocalDate time;

        /**
         * 待处理订单数量
         */
        private Long total;
    }
}
