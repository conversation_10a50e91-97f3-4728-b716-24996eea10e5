package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel
public class MtDelCouponRespDTO implements Serializable {


    @ApiModelProperty(value = "操作状态(0:成功)")
    private int result;

    @ApiModelProperty(value = "返回值信息")
    private String message;

    public MtDelCouponRespDTO() {

    }

    public MtDelCouponRespDTO(Integer result, String message) {
        this.result = result;
        this.message = message;
    }

    public static MtDelCouponRespDTO buildSuccess() {
        return new MtDelCouponRespDTO(0, "");
    }

    public static MtDelCouponRespDTO buildError(String message) {
        return new MtDelCouponRespDTO(-1, message);
    }

}
