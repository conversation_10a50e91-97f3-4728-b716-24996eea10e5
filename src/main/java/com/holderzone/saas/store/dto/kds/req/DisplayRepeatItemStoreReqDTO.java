package com.holderzone.saas.store.dto.kds.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 菜品重复绑定适用门店 请求DTO
 */
@Data
public class DisplayRepeatItemStoreReqDTO implements Serializable {

    private static final long serialVersionUID = -8993349661482406553L;

    /**
     * 品牌guid
     */
    @NotBlank(message = "品牌不能为空")
    private String brandGuid;

    /**
     * 是否允许重复
     */
    @NotNull(message = "KDS菜品绑定方式不能为空")
    private Boolean allowRepeatFlag;

    /**
     * 是否全部门店
     */
    @NotNull(message = "是否全部门店不能为空")
    private Boolean allStoreFlag;

    /**
     * 门店列表
     */
    private List<String> storeGuids;
}
