package com.holderzone.saas.store.dto.report.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayTypeCountQueryDTO
 * @date 2018/09/28 15:08
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class PayTypeCountQueryDTO extends BaseQueryDTO {

    @ApiModelProperty(value = "支付类型  支付方式-2美团支付 ，-1 饿了么支付， 0:现金支付 ，1:聚合支付 ,2：银行卡支付，3：会员卡支付，3+n：其他支付方式")
    private String paymentType;

    @ApiModelProperty(value = "订单号")
    private String orderGuid;

    @ApiModelProperty(value = "订单来源 0:全部 ，一体机点餐下单（1）、POS设备（2）、M1（3）、平板点餐（4）、微信公众号（5）、美团外卖（6）饿了么外卖（7）")
    private int orderSource = 0;

}
