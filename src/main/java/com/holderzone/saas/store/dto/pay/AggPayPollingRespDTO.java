package com.holderzone.saas.store.dto.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayPollingRespDTO
 * @date 2019/03/14 10:30
 * @description 聚合支付轮询resp
 * @program holder-saas-store-dto
 */
@Data
public class AggPayPollingRespDTO extends BaseCallBackDTO {

    @ApiModelProperty(value = "响应码，成功=10000")
    private String code;

    @ApiModelProperty(value = "响应信息")
    private String msg;

    @ApiModelProperty(value = "金额（分）")
    private BigDecimal amount;

    @ApiModelProperty(value = "主题")
    private String subject;

    @ApiModelProperty(value = "body")
    private String body;

    @ApiModelProperty(value = "聚合支付平台订单号")
    private String orderHolderNo;

    @ApiModelProperty(value = "支付功能id")
    private String payPowerId;

    @ApiModelProperty(value = "银行订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态：待支付-0，支付中-1，支付成功-2，支付失败-3，退款-4，已关闭-5，撤销-6，待支付-10")
    private String paySt;

    @ApiModelProperty(value = "银行交易流水号")
    private String bankTransactionId;

    @ApiModelProperty(value = "商户自己订单号")
    private String orderGUID;

    @ApiModelProperty(value = "本次支付唯一标识")
    private String payGUID;

    @ApiModelProperty(value = "订单创建时间（是）： yyyyMMdd")
    private String orderDt;

    @ApiModelProperty(value = "支付完成时间（否）： yyyy-MM-dd HH:mm:ss")
    private String timePaid;

    @ApiModelProperty(value = "订单附加描述（否）")
    private String description;

    @ApiModelProperty(value = "该笔订单所扣费用，单位分（是）")
    private String fee;

    @ApiModelProperty(value = "商户下单时所传（否）")
    private String extra;

    @ApiModelProperty(value = "平台订单创建时间：yyyy-MM-dd HH:mm:ss")
    private String created;

    @ApiModelProperty(value = "支付渠道Id")
    private String payChannelId;

    @ApiModelProperty(value = "附加数据，透传字段")
    private String attachData;

    @ApiModelProperty(value = "二维码链接")
    private String codeUrl;

    @ApiModelProperty(value = "微信公众号或者小程序，支付唤起参数")
    private String prepayInfo;

    @ApiModelProperty(value = "签名")
    private String signature;

    @ApiModelProperty(value = "是否最後一次支付")
    private Boolean isLast;

    @ApiModelProperty(value = "是否最后结账")
    private Boolean checkoutSuccessFlag;

    @ApiModelProperty("结账状态 4：结账成功 5：结账失败")
    private Integer billPleaseState;

    /**
     * 银行流水号
     */
    private String bankOrderNo;

    public static AggPayPollingRespDTO errorResp(String code, String msg) {
        AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode(code);
        pollingRespDTO.setMsg(msg);
        return pollingRespDTO;
    }

    public static AggPayPollingRespDTO errorResp(String code, String msg, String attachData) {
        AggPayPollingRespDTO pollingRespDTO = errorResp(code, msg);
        pollingRespDTO.setAttachData(attachData);
        return pollingRespDTO;
    }
}
