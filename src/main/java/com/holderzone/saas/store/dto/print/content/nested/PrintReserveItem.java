package com.holderzone.saas.store.dto.print.content.nested;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintItemRecord
 * @date 2018/07/25 11:13
 * @description 商品相关
 * @program holder-saas-store-print
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(description = "菜品记录")
public class PrintReserveItem implements Serializable {

    private static final long serialVersionUID = -2851990564619429954L;

    @ApiModelProperty(value = "菜品Guid", required = true)
    private String itemGuid;

    @NotBlank(message = "菜品名字不能为空")
    @ApiModelProperty(value = "菜品名字", required = true)
    private String itemName;

    @NotNull(message = "菜品数量不得为空")
    @ApiModelProperty(value = "数量。如果是套餐商品的成分，该数量为成分选择数量")
    private Double number;


}
