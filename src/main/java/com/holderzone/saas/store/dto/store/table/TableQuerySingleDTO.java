package com.holderzone.saas.store.dto.store.table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableQueryAllDTO
 * @date 2018/07/24 上午11:39
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class TableQuerySingleDTO implements Serializable {

    private static final long serialVersionUID = 2313101587181553644L;

    /**
     * 桌台guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "桌台guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String tableGuid;

    /**
     * 是否查询已删除的门店区域
     */
    @ApiModelProperty(value = "是否查询已删除的门店区域。0(or null)=不查询，1=查询。")
    private Integer deleted;
}
