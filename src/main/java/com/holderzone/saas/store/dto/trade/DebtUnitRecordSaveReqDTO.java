package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> R
 * @date 2020/12/16 15:40
 * @description
 */
@ApiModel(value = "挂账保存请求DTO")
@Data
public class DebtUnitRecordSaveReqDTO {

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "单位Guid")
    private String unitGuid;

    @ApiModelProperty(value = "单位编码")
    private String unitCode;

    @ApiModelProperty(value = "还款票据编号")
    private String debtInvoiceCode;

    @ApiModelProperty(value = "挂账金额")
    private BigDecimal debtFee;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "变更类型(反结账后需要更新挂账信息：1 新增订单为：2)")
    private Integer changeType;

    @ApiModelProperty(value = "退款guid")
    private String refundOrderGuid;
}
