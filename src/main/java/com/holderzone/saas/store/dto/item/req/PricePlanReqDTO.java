package com.holderzone.saas.store.dto.item.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.item.common.PlanItemDTO;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@ApiModel(value = "价格方案保存入参")
@Data
public class PricePlanReqDTO {

    @ApiModelProperty(value = "方案保存类型0-草稿1-保存数据库")
    private Integer planType;

    @ApiModelProperty(value = "方案guid（编辑时传）")
    private String guid;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "所属品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "说明")
    private String description;

    /**
     * 方案状态-0 未启用 1 已启用 2暂不启用 3永久停用 4即将启用
     * @see com.holderzone.saas.store.item.entity.enums.PricePlanStatusEnum
     */
    @ApiModelProperty(value = "方案状态-0 未启用 1 已启用 2暂不启用 3永久停用 4即将启用")
    private Integer status;

    @ApiModelProperty(value = "起始时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalTime startTime;

    @ApiModelProperty(value = "结束时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalTime endTime;

    @ApiModelProperty(value = "售卖类型-0 默认（全时段）-1特殊时段")
    private Integer sellTimeType;

    @ApiModelProperty(value = "推送时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pushDate;

    @ApiModelProperty(value = "菜谱生效时间 1立即生效 2按时间")
    private Integer pushType;

    @ApiModelProperty(value = "新增方案菜品详情")
    private List<PlanItemDTO> planItemDTOList;

    @ApiModelProperty(value = "门店guid集合")
    private List<String> storeGuidList;

    @ApiModelProperty(value = "菜谱分类集合")
    private List<TypePricePlanReqDTO> typeList;

    @ApiModelProperty(value = "方案保存信息")
    private List<ItemWebRespDTO> itemWebRespDTOList;

    @ApiModelProperty(value = "门店集合")
    private List<StoreDTO> storeDTOList;

    private Boolean batchEdit;
}
