package com.holderzone.saas.store.dto.item.resp;

import com.holderzone.saas.store.dto.organization.StoreDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GroupMealItemDetailRespDTO
 * @date 2019/12/07 上午10:49
 * @description //团餐详情DTO
 * @program holder-saas-store-item
 */
@ApiModel(value = "团餐详情DTO")
@Data
public class GroupMealItemDetailRespDTO {

    @ApiModelProperty(value = "已选商品N道")
    private Integer sumSkuQuantity; //已选商品N道

    @ApiModelProperty(value = "已选商品N份")
    private Integer sumSkuNUmber;  //已选商品N份

    @ApiModelProperty(value = "总毛利率")
    private BigDecimal grossMargin;

    @ApiModelProperty(value = "团餐分组")
    private List<GroupMealSubgroupWebRespDTO> groupMealSubgroupList;

    //extends ItemInfoRespDTO 解决问题：同一DTO转换成PO和DO，但是PO继承于DO，模糊两可的转换。
    @ApiModelProperty(value = "商品唯一标识")
    private String itemGuid;

    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "父商品GUID")
    private String parentGuid;

    @ApiModelProperty(value = "商品来源（0：门店自己创建的商品，1：品牌自己创建的商品,2:被推送过来的商品）")
    private Integer itemFrom;

    @ApiModelProperty(value = "分类GUID")
    private String typeGuid;

    @ApiModelProperty(value = "商品类型:1.套餐（不称重，无规格），2.规格商品，3.称重商品，4.单品 ，5:。团餐")
    private Integer itemType;

    @ApiModelProperty(value = "属性组状态:0：无属性; 1:有属性; 2:有必选属性组")
    private Integer hasAttr;

    @ApiModelProperty(value = "是否售罄:0 否 1 是")
    private Integer isSoldOut;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "别名")
    private String nameAbbr;

    @ApiModelProperty(value = "拼音简码")
    private String pinyin;

    @ApiModelProperty(value = "排序号")
    private Integer sort;

    @ApiModelProperty(value = "商品主图路径")
    private String pictureUrl;

    @ApiModelProperty(value = "商品描述")
    private String description;

    @ApiModelProperty(value = "是否热销：对应快餐用的字段为为：isRecommend：0：否，1：是")
    private Integer isBestseller;

    @ApiModelProperty(value = "是否是招牌：0：否，1：是")
    private Integer isSign;

    @ApiModelProperty(value = "是否新品：快餐无对应字段：0：否，1：是")
    private Integer isNew;

    @ApiModelProperty(value = "是否是固定套餐，0：否，1：是")
    private Integer isFixPkg;

    @ApiModelProperty(value = "商品编码（冗余小程序端字段）")
    private String code;

    @ApiModelProperty(value = "企业guid（冗余小程序端字段）")
    private String enterpriseGuid;

    @ApiModelProperty(value = "审核状态（冗余小程序端字段）")
    private Integer auditStatus;

    @ApiModelProperty(value = "视频url json数组（冗余小程序端字段）")
    private String videoUrls;

    @ApiModelProperty(value = "图文详情（冗余小程序端字段）")
    private String remarkDetail;

    @ApiModelProperty(value = "好评次数（冗余小程序端字段）")
    private Integer upCount;

    @ApiModelProperty(value = "规格集合")
    private List<SkuInfoRespDTO> skuList;

    @ApiModelProperty(value = "差评次数（冗余小程序端字段）")
    private Integer downCount;

    @ApiModelProperty(value = "一级属性集合")
    private List<AttrGroupWebRespDTO> attrGroupList;

    @ApiModelProperty(value = "门店集合")
    private List<StoreDTO> substoreList;

    @ApiModelProperty(value = "分组集合")
    private List<SubgroupWebRespDTO> subgroupList;
}
