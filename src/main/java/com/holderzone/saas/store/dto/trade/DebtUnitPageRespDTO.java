package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 分页查询挂账单位返参
 *
 * <AUTHOR>
 * @since 2020-12-16
 */
@ApiModel(value = "挂账单位分页返回DTO")
public class DebtUnitPageRespDTO {

    /**
     * 单位主表guid
     */
    @ApiModelProperty(value = "单位guid")
    private String unitGuid;

    /**
     * 单位代码
     */
    @ApiModelProperty(value = "单位代码")
    private String code;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    private String name;

    /**
     * 单位额度
     */
    @ApiModelProperty(value = "单位额度")
    private BigDecimal creditLimit;

    /**
     * 单位可用额度
     */
    @ApiModelProperty(value = "单位可用额度")
    private BigDecimal creditLimitLeft;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 查询密码
     */
    @ApiModelProperty(value = "查询密码")
    private String password;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String contactTel;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;

    public String getUnitGuid() {
        return unitGuid;
    }

    public void setUnitGuid(String unitGuid) {
        this.unitGuid = unitGuid;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getCreditLimit() {
        return creditLimit;
    }

    public void setCreditLimit(BigDecimal creditLimit) {
        this.creditLimit = creditLimit;
    }

    public BigDecimal getCreditLimitLeft() {
        return creditLimitLeft;
    }

    public void setCreditLimitLeft(BigDecimal creditLimitLeft) {
        this.creditLimitLeft = creditLimitLeft;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactTel() {
        return contactTel;
    }

    public void setContactTel(String contactTel) {
        this.contactTel = contactTel;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}
