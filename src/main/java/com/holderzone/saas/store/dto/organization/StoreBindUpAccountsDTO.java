package com.holderzone.saas.store.dto.organization;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 扎帐业务传参实体类
 * <AUTHOR>
 */
@Data
public class StoreBindUpAccountsDTO implements Serializable {

    /**
     * 修改门店guid集合
     */
    List<String> storeList;

    /**
     * 是否强制扎帐
     */
    Integer isBuAccounts;

    /**
     * 是否展示现金
     */
    Integer isShowCash;

    /**
     * 能够开台
     */
    Integer canOpenTable;

    @ApiModelProperty(value = "是否多人交接班 0 否 1 是")
    private Integer isMultiHandover;
}
