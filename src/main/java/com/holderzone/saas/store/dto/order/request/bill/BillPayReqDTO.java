package com.holderzone.saas.store.dto.order.request.bill;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.OrderMultiMemberDTO;
import com.holderzone.saas.store.dto.order.response.bill.BillMemberCardCalculateRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillCalculateReqDTO
 * @date 2019/01/28 17:32
 * @description 支付入参
 * @program holder-saas-store-dto
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BillPayReqDTO extends BaseDTO {

    private static final long serialVersionUID = -2546600589223154067L;

    @ApiModelProperty(value = "订单guid")
    @OrderLockField
    private String orderGuid;

    @ApiModelProperty(value = "消费时间")
    private LocalDateTime consumptionTime;

    @ApiModelProperty(value = "订单金额（商品总额+附加费）")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "附加费")
    private BigDecimal appendFee;

    @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "找零")
    private BigDecimal changeFee;

    @ApiModelProperty(value = "当前的优惠信息")
    private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

    @ApiModelProperty(value = "支付信息")
    @NotEmpty(message = "支付信息不能为空")
    private List<Payment> payments;

    @ApiModelProperty(value = "是否快餐")
    private boolean fastFood;

    @ApiModelProperty(value = "会员密码")
    private String memberPassWord;

    @ApiModelProperty(value = "是否需要密码")
    private Boolean needPassword = Boolean.TRUE;

    @ApiModelProperty(value = "会员卡guid")
    private String memberInfoCardGuid;

    @ApiModelProperty(value = "会员guid")
    private String memberInfoGuid;

    @ApiModelProperty("员消费记录GUID，如果已经上传过一次订单信息，再次计算其它卡优惠时，只需要传此GUID(菜品没有做变化的情况下)")
    private String memberConsumptionGuid;

    @ApiModelProperty(value = "是否是积分商城0：不是，1：是")
    private Integer memberIntegralStore;

    @ApiModelProperty(value = "麓豆会员信息")
    private BillMemberCardCalculateRespDTO ludouMemberDTO;

    @ApiModelProperty(value = "使用的积分")
    private Integer useIntegral;

    @ApiModelProperty(value = "积分抵扣了多少钱")
    private BigDecimal integralDiscountMoney;

    @ApiModelProperty(value = "主单guid")
    private String mainOrderGuid;

    @ApiModelProperty(value = "桌台guid")
    private String tableGuid;

    @ApiModelProperty(value = "version")
    @LockField
    private Integer version;

    @Data
    public static class Payment {

        private String guid;

        @ApiModelProperty(value = "交易金额")
        private BigDecimal amount;

        /**
         * @see com.holderzone.saas.store.enums.PaymentTypeEnum
         */
        @ApiModelProperty(value = "支付方式 1:现金支付 ，2:聚合支付 ,3：银行卡支付，4：会员卡支付，5：人脸支付，10：其他支付方式")
        @Min(value = 1, message = "支付方式最少为1")
        private Integer paymentType;

        @ApiModelProperty(value = "支付方式名")
        @NotNull(message = "支付方式不能为空")
        private String paymentTypeName;

        @ApiModelProperty(value = "银行流水号")
        private String bankTransactionId;

        @ApiModelProperty(value = "人脸支付失败时安卓自己随机加的3位数字")
        private String faceCode;

        @ApiModelProperty(value = "多会员支付")
        private List<OrderMultiMemberDTO> multiMembers;
    }

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "单位Guid")
    private String unitGuid;

    @ApiModelProperty(value = "单位编码")
    private String unitCode;

    @ApiModelProperty(value = "挂账单位联系人")
    private String unitContactName;

    @ApiModelProperty(value = "挂账单位电话")
    private String unitContactTel;

    @ApiModelProperty("是否是食堂消费")
    private Boolean canteenPay = false;

    @ApiModelProperty(value = "食堂卡余额：离线订单每一单都要记录，用于对比")
    private BigDecimal cardMoney;

    @ApiModelProperty(value = "食堂卡赠送金额:离线订单每一单都要记录，用于对比")
    private BigDecimal cardGiftMoney;

    private String openId;

    @ApiModelProperty("是否打印开票二维码小票 1 是 null或0不管")
    private Integer isInvoiceCode;

    @ApiModelProperty("开票注册人账号")
    private String invoicePhone;

    @ApiModelProperty(value = "accountName")
    private String accountName;

    @ApiModelProperty(value = "当前桌台的订单guid")
    private String currentTableOrderGuid;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "是否聚合支付")
    private Boolean aggPayFlag;

    @ApiModelProperty(value = "是否回调支付")
    private Boolean callbackPayFlag;

    /**
     * 是否pad下单
     */
    public boolean padOrderFlag() {
        return Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), this.getDeviceType());
    }

    /**
     * 是否人脸支付
     */
    public boolean facePayFlag() {
        return this.getPayments().stream().anyMatch(e -> PaymentTypeEnum.FACE.getCode() == e.getPaymentType());
    }

    /**
     * 是否挂账支付
     */
    public boolean debtPayFlag() {
        return this.getPayments().stream().anyMatch(e -> PaymentTypeEnum.DEBT_PAY.getCode() == e.getPaymentType());
    }
}
