package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class PrdPointItemBindReqDTO implements Serializable {

    private static final long serialVersionUID = -2773042054434843563L;

    @NotEmpty(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @NotEmpty(message = "设备ID不得为空")
    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @NotNull(message = "堂口Guid不得为空")
    @ApiModelProperty(value = "堂口Guid")
    private String pointGuid;

    @ApiModelProperty(value = "绑定商品列表")
    private List<PrdDstItemBindDTO> bindingItems;

    @ApiModelProperty(value = "是否绑定菜品分组")
    private Boolean bindingItemGroupFlag;

    @ApiModelProperty(value = "绑定菜品分组列表")
    private List<String> bindingItemGroups;
}
