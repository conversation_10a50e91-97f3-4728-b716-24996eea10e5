package com.holderzone.saas.store.dto.takeaway;

/**
 * 订单部分退款消息推送的type
 * <p>
 * https://developer.meituan.com/openapi#7.5.8
 */
public enum MtCbOprNotifyTypeEnum {

    PART("part", "商家/用户发起部分退款"),

    AGREE("agree", "商家同意部分退款"),

    REJECT("reject", "商家拒绝部分退款");

    private String type;

    private String desc;

    MtCbOprNotifyTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MtCbOprNotifyTypeEnum ofType(String type) {
        for (MtCbOprNotifyTypeEnum value : values()) {
            if (type.equalsIgnoreCase(value.type)) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效的notifyType[" + type + "]");
    }
}
