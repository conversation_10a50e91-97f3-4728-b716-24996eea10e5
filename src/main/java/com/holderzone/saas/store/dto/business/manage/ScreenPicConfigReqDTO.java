package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPicConfigReqDTO
 * @date 2019/08/19 18:51
 * @description 门店设备副屏图片请求入参
 * @program holder-saas-store
 */
@Data
@ApiModel("门店设备副屏图片请求入参")
public class ScreenPicConfigReqDTO {

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "设备类型:0：PC服务端,1：PC平板,2：小店通,3：一体机,4：POS机,5：云平板,6：点菜宝(M1),7：PV1(带刷卡的点菜宝),9：厨房显示系统,14: 取餐屏,12：微信", notes = "")
    private Integer deviceType;

    @ApiModelProperty(value = "图片类型 1-->满屏图片，2-->半屏图片 3副屏显示设置")
    private Integer picType;
}
