package com.holderzone.saas.store.dto.queue;

import com.holderzone.resource.common.dto.validate.Update;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HolderQueueDTO
 * @date 2019/03/27 17:00
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
@ApiModel("排队元素信息")
public class HolderQueueItemDTO extends BaseDTO {
    @ApiModelProperty("业务主键")
    @NotBlank(message = "排队记录guid不能为空",groups = Update.class)
    private String guid;
    @NotBlank(message = "请选择排队的门店")
    @ApiModelProperty("门店guid")
    private String storeGuid;
    @ApiModelProperty("联系人姓名")
    private String contact;
    @ApiModelProperty("手机号")
    private String phone;
    @ApiModelProperty("性别 男:true, 女:false")
    private Boolean gender = true;
    private String remark;
    @Min(value = 0,message = "非法的人数输入")
    @Max(value = 127 ,message = "非法的人数输入")
    @ApiModelProperty(value = "人数",required = true)
    private Byte peopleNumber;

}