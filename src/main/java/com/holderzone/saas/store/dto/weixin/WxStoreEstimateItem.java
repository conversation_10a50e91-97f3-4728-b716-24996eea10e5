package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("估清结果商品")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WxStoreEstimateItem {

	@ApiModelProperty(value = "规格id")
	private String skuGuid;

	@ApiModelProperty(value = "规格名字")
	private String skuName = "";

	@ApiModelProperty(value = "商品名字")
	private String itemName;

	@ApiModelProperty(value = "商品guid")
	private String itemGuid;
}
