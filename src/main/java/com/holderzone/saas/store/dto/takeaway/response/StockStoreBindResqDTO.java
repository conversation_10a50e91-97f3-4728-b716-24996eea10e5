package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StockStoreBindResqDTO implements Serializable {

    private static final long serialVersionUID = -7820935209083353029L;

    @ApiModelProperty(value = "门店guid")
    @NotNull(message = "门店guid不能为空")
    private String storeGuid;

    @ApiModelProperty(value = "库存绑定门店")
    @NotNull(message = "库存绑定门店guid不能为空")
    private String branchStoreGuid;

    @ApiModelProperty(value = "库存绑定门店名称")
    private String branchStoreName;
}
