package com.holderzone.saas.store.dto.business.manage;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023年09月07日 11:10
 * @description 附加费条件查询
 */
@Data
public class SurchargeConditionQuery implements Serializable{

    private static final long serialVersionUID = 5050657339141700952L;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 费用类型
     * 0：按人
     * 1：按桌
     */
    private Integer type;

    /**
     * 场景：0=正餐，1=快餐
     */
    private Integer tradeMode;

    /**
     * 区域guid
     */
    private String areaGuid;
}
