package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutShopBindReqDTO
 * @date 2018/09/25 9:07
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@ApiModel
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TakeoutShopOwnItemBindReqDTO extends BaseDTO {

    @ApiModelProperty(value = "id")
    private List<BindInfo> GoodsList;

    @ApiModelProperty(value = "门店guid")
    private String StoreCode;

}
