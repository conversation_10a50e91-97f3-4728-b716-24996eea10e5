package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 价格方案门店控制列表查询返回对象
 * @date 2020/10/29 17:13
 */
@ApiModel(value = "价格方案门店控制列表查询返回对象")
@Data
public class PricePlanStoreRespDTO {

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "有无其他价格方案占用")
    private Boolean hasPricePlan;

    @ApiModelProperty(value = "是否当前价格方案")
    private Boolean isCurrent;

    @ApiModelProperty(value = "是否所属当前用户")
    private Boolean isCurrentUser;
}
