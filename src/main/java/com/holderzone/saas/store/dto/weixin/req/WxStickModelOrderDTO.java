package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickModelOrderDTO
 * @date 2019/03/15 16:19
 * @description 微信桌贴模板下单DTO
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "微信桌贴模板下单DTO")
public class WxStickModelOrderDTO implements Serializable {

    private static final long serialVersionUID = 6150956895403789086L;

    @ApiModelProperty(value = "企业guid")
    @NotBlank(message = "企业guid不能为空")
    private String enterpriseGuid;

    @ApiModelProperty(value = "模板guid集合")
    @NotEmpty(message = "模板guid集合不能为空")
    private List<String> tableStickGuidList;

    @ApiModelProperty(value = "数量")
    @Min(value = 1, message = "产品数量最小为1")
    private int orderCount;

    @ApiModelProperty(value = "终端类型")
    @NotBlank(message = "终端类型不能为空")
    private String terminal;

    @ApiModelProperty(value = "付款方式")
    @NotBlank(message = "付款方式不能为空")
    private String payType;

    @ApiModelProperty(value = "操作人账号")
    private String staffAccount;

    @ApiModelProperty(value = "操作人guid")
    @NotBlank(message = "操作人guid不能为空")
    private String staffGuid;

    @ApiModelProperty(value = "操作人姓名")
    @NotBlank(message = "操作人姓名不能为空")
    private String staffName;

    @ApiModelProperty(value = "上次订单单号")
    private String lastPayGuid;
}
