package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("微信门店配置")
public class MenuInfoConfigDTO {

    @ApiModelProperty(value = "点餐模式（0正餐，1快餐）")
    private Integer orderModel;

    @ApiModelProperty(value = "顾客是否可点称重商品（0顾客可点，1顾客不可点）")
    private Integer isWeighingOrdered;

    @ApiModelProperty(value = "菜单样式（0大图，1小图）")
    private Integer menuType;

    @ApiModelProperty(value = "1：开启点餐，0：关闭点餐")
    private Integer isOrderOpen;

    @ApiModelProperty(value = "0:停业，1：开业")
    private Integer isOpened;

    @ApiModelProperty(value = "是否游客模式 （0关闭，1开启）")
    private Integer guestFlag;

    @ApiModelProperty(value = "支持团购平台")
    private List<String> supportGrouponTypes;

    @ApiModelProperty(value = "色系")
    private String styleColor;

    @ApiModelProperty(value = "是否自动带入会员详细地址")
    private Integer autoInMemberInfoFlag;

}
