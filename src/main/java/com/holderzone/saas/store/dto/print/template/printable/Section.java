package com.holderzone.saas.store.dto.print.template.printable;

import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 打印文字块，每个文字块是一个段落
 *
 * <AUTHOR>
 * @date 2018/12/15 14:41
 */
@Data
@ApiModel
@NoArgsConstructor
@Accessors(chain = true)
public class Section implements Serializable {

    private static final long serialVersionUID = 8079930315772352147L;

    @ApiModelProperty(value = "打印文本列表")
    private List<Text> texts;

    @ApiModelProperty(value = "文本对齐方式")
    private Text.Align align = Text.Align.Left;

    public Section(String text) {
        if (this.texts == null) {
            this.texts = new ArrayList<>();
        }
        this.texts.add(new Text(text));
    }

    public Section(Text text) {
        if (this.texts == null) {
            this.texts = new ArrayList<>();
        }
        this.texts.add(text);
    }

    public Section(String text, Font font) {
        if (this.texts == null) {
            this.texts = new ArrayList<>();
        }
        this.texts.add(new Text(text, font));
    }

    public Section(Text text, Text.Align align) {
        this(text);
        this.align = align;
    }

    public Section(String text, Text.Align align) {
        this(text);
        this.align = align;
    }

    public Section(String text, Font font, Text.Align align) {
        this(text, font);
        this.align = align;
    }

    public Section setAlign(Text.Align align) {
        this.align = align;
        return this;
    }

    public Section addText(String text) {
        return this.addText(text, Font.SMALL);
    }

    public Section addText(String text, Font font) {
        return this.addText(new Text(text, font));
    }

    public Section addText(Text text) {
        if (this.texts == null) {
            this.texts = new ArrayList<>();
        }
        this.texts.add(text);
        return this;
    }
}
