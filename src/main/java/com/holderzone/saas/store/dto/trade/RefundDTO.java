package com.holderzone.saas.store.dto.trade;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RefundDTO
 * @date 2018/08/29 18:11
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class RefundDTO extends BaseBillDTO {

    @ApiModelProperty(value = "支付单guid")
    private String paymentGuid;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundFee;

    @ApiModelProperty(value = "操作员工guid")
    private String operationStaffGuid;

    @ApiModelProperty(value = "操作员工name")
    private String operationStaffName;

    @ApiModelProperty(value = "退款的理由，必填")
    private String reason;

    @ApiModelProperty(value = "退款类型。0:全款，1:部分")
    private Integer refundType;

}
