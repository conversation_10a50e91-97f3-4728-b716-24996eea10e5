package com.holderzone.saas.store.dto.takeaway.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TcdAddOrderReqDTO
 * @date 2018/09/08 14:24
 * @description //赚餐创建订单入参
 * @program holder-saas-store-dto
 */
@Data
public class TakeoutTcdOrderReqDTO implements Serializable {

    public interface Created extends Default {
    }

    public interface Confirmed extends Default {
    }

    public interface Canceled extends Default {
    }

    public interface Shipping extends Default {
    }

    public interface Finished extends Default {
    }

    private static final long serialVersionUID = -4332755485264393339L;

    /****************商户区*****************/

    @ApiModelProperty(value = "商户ID，如：1938")
    private Long merchantId;

    @ApiModelProperty(value = "商户编号，如：4991336190014521")
    private Long merchantNo;

    @ApiModelProperty(value = "商户用户编号，如：1182")
    private Long merchantUserId;

    @ApiModelProperty(value = "品牌id，如：383")
    private Long brandId;

    @NotBlank(message = "企业guid不得为空")
    @ApiModelProperty(value = "企业guid", required = true)
    private String enterpriseGuid;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @ApiModelProperty(value = "是否打印")
    private Boolean print;

    @NotBlank(message = "不得为空")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;
    /**
     * PENDING--待处理;
     * COOKING---出餐中;
     * TAKE_A_SINGLE---取单中;
     * DISTRIBUTION----配送中;
     * FINISH-已完成;
     * CANCELLED---已取消;
     * PERSON_PENDING---配送员待接单(已出餐);
     * INVALID---失效
     */
    @ApiModelProperty(value = "赚餐外卖订单状态")
    private String orderStatusTcd;

    @ApiModelProperty(value = "核销码")
    private String writeOffCode;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /****************XXX区*****************/

    @NotBlank(message = "订单编号不得为空")
    @ApiModelProperty(value = "订单编号，如：200302150505469635", required = true)
    private String orderSn;

    @NotNull(message = "订单每日排序号码不得为空")
    @ApiModelProperty(value = "订单每日排序号码")
    private Integer dayCount;

    @NotNull(message = "订单状态不得为空")
    @ApiModelProperty(value = "订单状态", required = true)
    private OrderState orderState;

    @ApiModelProperty(value = "外卖，如：NETWORK;PICK_UP")
    private String orderType;

    @ApiModelProperty(value = "平台id，如：142")
    private Long platformId;

    @ApiModelProperty(value = "优惠券id，如：142")
    private String couponId;

    @ApiModelProperty(value = "配送区域，在自配送时需传入该字段，如：1")
    private Integer rangeCode;

    @ApiModelProperty(value = "配送费用，在自配送时需传入该字段，如：0")
    private BigDecimal sendPrice;

    @ApiModelProperty(value = "支付方式，实际上全市线上支付，如：" +
            "1 支付宝 2 微信 3 银行卡 4余额支付  " +
            "5.免支付 6 掌控者会员卡支付 10.掌控者聚合支付")
    private Integer payWay;

    /****************金额区*****************/

    @NotNull(message = "订单总价不得为空", groups = {Created.class, Confirmed.class})
    @ApiModelProperty(value = "订单总价，包括：菜品(原价) + 餐盒费用 + 配送费用，新订单不得为空", required = true)
    private BigDecimal totalAmount;

    @NotNull(message = "菜品(原价)不得为空", groups = {Created.class, Confirmed.class})
    @ApiModelProperty(value = "菜品(原价)，新订单不得为空", required = true)
    private BigDecimal productFee;

    @NotNull(message = "餐盒费用不得为空", groups = {Created.class, Confirmed.class})
    @ApiModelProperty(value = "餐盒费用，新订单不得为空", required = true)
    private BigDecimal packFee;

    @NotNull(message = "配送费用不得为空", groups = {Created.class, Confirmed.class})
    @ApiModelProperty(value = "配送费用，新订单不得为空", required = true)
    private BigDecimal shippingFee;

    @NotNull(message = "用户实际支付不得为空", groups = {Created.class, Confirmed.class})
    @ApiModelProperty(value = "用户实际支付，包括：订单总价 - 优惠，新订单不得为空", required = true)
    private BigDecimal amount;

    /****************优惠区：红包 优惠券  满减活动，优惠总额 = 满减金额 + 优惠券减免*****************/

    @Nullable
    @ApiModelProperty(value = "满减金额")
    private BigDecimal fullReduction;

    @Nullable
    @ApiModelProperty(value = "优惠券减免")
    private BigDecimal couponFee;

    @Nullable
    @ApiModelProperty(value = "同fullRedcution，不使用")
    private BigDecimal discountFee;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal platformDiscount;

    @ApiModelProperty(value = "会员优惠金额")
    private BigDecimal otherDiscount;

    /****************配送区****************/

    @JsonProperty("sendDeliveryState")
    @ApiModelProperty(value = "分发状态")
    private DeliveryState deliveryState;

    @ApiModelProperty(value = "分发时间，时间戳（秒）")
    private Integer sendDeliveryTime;

    @ApiModelProperty(value = "配送员名称", required = true)
    private String personName;

    @ApiModelProperty(value = "配送员电话", required = true)
    private String personPhone;

    /****************取消区****************/

    @Nullable
    @ApiModelProperty(value = "哪一端发起的退款：1=用户发起退款，2=商家发起的退款")
    private Integer whoRefunds;

    @Nullable
    @ApiModelProperty(value = "申请退款原因")
    private String requestRefundsReason;

    /****************嵌套区****************/

    @JsonProperty("shopOrderExtendedInfoDTO")
    @ApiModelProperty(value = "联系信息，新订单不得为空", required = true)
    @NotNull(message = "联系信息不得为空", groups = {Created.class, Confirmed.class})
    private OrderExtended orderExtended;

    @Nullable
    @ApiModelProperty(value = "优惠活动")
    @JsonProperty("fullReductionActivity")
    private ReduceActivity reduceActivity;

    @Valid
    @NotEmpty(message = "商品信息不得为空", groups = {Created.class, Confirmed.class})
    @ApiModelProperty(value = "商品信息，新订单不得为空", required = true)
    @JsonProperty("shopOrderProductInfoDTOS")
    private List<OrderProduct> orderProductList;

    public enum DeliveryState {

        /**
         * 未配送
         */
        UNSEND,

        /**
         * 已配送
         */
        SENDED,

        ;
    }

    public enum OrderState {

        /**
         * 待处理(外卖订单)、未使用（到店消费）、待发货（快寄配送到家）
         */
        PENDING,

        /**
         * 出餐中（外卖订单）、已发货（快寄配送到家）
         */
        COOKING,

        /**
         * 取单中（外卖订单）
         */
        TAKE_A_SINGLE,

        /**
         * 配送中（外卖订单）
         */
        DISTRIBUTION,

        /**
         * 配送中（外卖订单）
         */
        REFUND,

        /**
         * 已完成（外卖订单、到店消费、快寄配送到家）
         */
        FINISH,

        /**
         * 已取消（外卖订单、到店消费、快寄配送到家）
         */
        CANCELLED,

        /**
         * 配送员待接单（外卖订单）
         */
        PERSON_PENDING,

        /**
         * 失效
         */
        INVALID,
    }

    @Data
    public static class OrderExtended {

        @NotEmpty(message = "客户地址不得为空", groups = {Created.class, Confirmed.class})
        @ApiModelProperty(value = "客户地址，新订单不得为空", required = true)
        private String customerAddress;

        @ApiModelProperty(value = "客户性别，新订单不得为空")
        private String customerGender;

        @NotEmpty(message = "客户姓名不得为空", groups = {Created.class, Confirmed.class})
        @ApiModelProperty(value = "客户姓名，新订单不得为空", required = true)
        private String customerName;

        @NotEmpty(message = "客户电话不得为空", groups = {Created.class, Confirmed.class})
        @ApiModelProperty(value = "客户电话，新订单不得为空", required = true)
        private String customerPhone;

        @NotEmpty(message = "客户坐标不得为空", groups = {Created.class, Confirmed.class})
        @ApiModelProperty(value = "客户坐标，如：104.0367,30.62434，新订单不得为空")
        private String customerCoordinate;

        @ApiModelProperty(value = "商户地址，如：天府广场")
        private String merchantAddress;

        @ApiModelProperty(value = "商户地址，如：104.065861,30.657401")
        private String merchantCoordinate;

        @ApiModelProperty(value = "商户距离，如：0")
        private BigDecimal merchantDistance;

        @ApiModelProperty(value = "商户名称，如：虾霸天天府广场店")
        private String merchantName;

        @ApiModelProperty(value = "商户电话，如：15800000258")
        private String merchantPhone;

        @ApiModelProperty(value = "订单送达，如：立即送餐")
        private String orderSend;

        @ApiModelProperty(value = "null，如：")
        private Long receiveId;

        @ApiModelProperty(value = "订单标识，如：")
        private String orderRemark;

        @ApiModelProperty(value = "订单备注，如：")
        private String orderMark;
    }

    @Data
    public static class OrderProduct {

        /**
         * 商品类型（菜品（商品）销售类型）
         * <p>
         * 1  快递到家
         * 2  外卖点餐
         * 4  到店消费
         * 8  外卖自提
         * 16 外卖套餐
         * 32 线上兑换
         * 64 扫码点餐
         */
        @ApiModelProperty(value = "商品类型，如：2")
        private Integer productType;

        @ApiModelProperty(value = "活动ID，如：")
        private Long activityId;

        @ApiModelProperty(value = "活动类型，如：")
        private Integer activityType;

        @ApiModelProperty(value = "配送费用，如：12.1")
        private BigDecimal shippingFee;

        @ApiModelProperty(value = "打包费，如：12.1")
        private BigDecimal packFee;

        @ApiModelProperty(value = "产品名称，如：回锅小炒肉")
        private String productName;

        @ApiModelProperty(value = "规格id，如：1882")
        private String skuId;

        @NotNull(message = "数量不得为空", groups = {Created.class, Confirmed.class})
        @ApiModelProperty(value = "数量，新订单不得为空")
        private Integer productNum;

        @NotNull(message = "菜品小计不得为空", groups = {Created.class, Confirmed.class})
        @ApiModelProperty(value = "菜品小计，新订单不得为空")
        private BigDecimal productPrice;

        @ApiModelProperty(value = "，如：")
        private String imageUrl;

        @NotEmpty(message = "商品GUID不得为空", groups = {Created.class, Confirmed.class})
        @ApiModelProperty(value = "商品GUID，新订单不得为空", required = true)
        private String itemGuid;

        @NotEmpty(message = "商品信息不得为空", groups = {Created.class, Confirmed.class})
        @ApiModelProperty(value = "商品名称，新订单不得为空", required = true)
        private String itemName;

        @Valid
        @NotNull(message = "商品规格不得为空", groups = {Created.class, Confirmed.class})
        @ApiModelProperty(value = "商品规格，新订单不得为空", required = true)
        private ProductSpec spec;

        @ApiModelProperty(value = "会员价")
        private BigDecimal memberPrice;

        @ApiModelProperty(value = "商品GUID，新订单不得为空", required = true)
        private List<SelfSupportDishPropertyTemp> selfSupportDishPropertyTempList;

    }

    @Data
    public static class ProductSpec {

        @NotEmpty(message = "商品信息不得为空", groups = {Created.class, Confirmed.class})
        @ApiModelProperty(value = "规格名称，新订单不得为空", required = true)
        private String skuGuid;

        @Nullable
        @ApiModelProperty(value = "规格名称，新订单有值就传")
        private String skuName;

        @ApiModelProperty(value = "规格名称，如：回锅小炒肉")
        private String name;

        @ApiModelProperty(value = "计数单位")
        private String unit;

        @ApiModelProperty(value = "打包盒数量，如：0")
        private Integer packNum;

        @ApiModelProperty(value = "打包盒价格，如：0")
        private BigDecimal packPrice;

        @NotNull(message = "规格价格不得为空", groups = {Created.class, Confirmed.class})
        @ApiModelProperty(value = "规格价格，新订单不得为空", required = true)
        private BigDecimal price;

        @ApiModelProperty(value = "餐盒，如：0")
        private BigDecimal boxPrice;

        @ApiModelProperty(value = "是否免餐盒费，如：0")
        private Integer freeBoxPrice;
    }

    @Data
    public static class ReduceActivity {

        @Valid
        @JsonProperty("fullReductionlist")
        @ApiModelProperty(value = "优惠列表")
        @NotEmpty(message = "优惠列表不得为空", groups = {Created.class, Confirmed.class})
        private List<Reduction> reductionList;

        //      "id": 78,
        //		"userId": 1182,
        //		"brandId": null,
        //		"merchantId": 1938,
        //		"merchantName": "虾霸天天府广场店",
        //		"brandName": null,
        //		"addTime": "2020-02-26T07:36:20.000Z",
        //		"offerActivity": false,
        //		"shareActivity": false,
        //		"distributionOrder": false,

        //		"businessType": null,
        //		"activityType": 2,
        //		"activityShopType": null,
        //		"openActivity": true,
        //		"platformId": 142
    }

    @Data
    public static class Reduction {

        @ApiModelProperty(value = "XX金额，如：0")
        private BigDecimal fullMoney;

        @NotNull(message = "优惠金额不得为空", groups = {Created.class, Confirmed.class})
        @ApiModelProperty(value = "优惠金额，如：0", required = true)
        private BigDecimal cutMoney;

        @ApiModelProperty(value = "不知道啥，如：0")
        private String merchantNameInfos;
    }
}
