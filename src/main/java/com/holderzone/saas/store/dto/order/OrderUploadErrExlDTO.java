package com.holderzone.saas.store.dto.order;

import com.aimilin.annotation.ExlColumn;
import com.aimilin.annotation.ExlSheet;
import lombok.Data;

import java.io.Serializable;

/**
 * 复制订单错误信息
 *
 * <AUTHOR>
 * @date 2021/12/22 11:51
 */
@Data
@ExlSheet("未导入订单信息")
public class OrderUploadErrExlDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExlColumn(value = "订单GUID", index = 1)
    private String orderGuid;

    @ExlColumn(value = "错误原因", index = 2)
    private String errorMessage = "订单不存在或改订单存在并台信息";

}
