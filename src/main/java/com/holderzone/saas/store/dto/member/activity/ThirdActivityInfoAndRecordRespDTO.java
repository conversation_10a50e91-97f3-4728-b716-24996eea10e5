package com.holderzone.saas.store.dto.member.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 第三方优惠活动详情信息以及使用记录
 * @date 2021/12/9 18:08
 * @className:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(description = "第三方优惠活动详情信息以及使用记录")
public class ThirdActivityInfoAndRecordRespDTO implements Serializable {

    private static final long serialVersionUID = -1905181930778548602L;

    @ApiModelProperty(example = "guid")
    private String guid;

    @ApiModelProperty("三方平台类型 MT-美团; DZ-大众点评; DY-抖音; ZC-赚餐; OTHER-其他")
    private String thirdType;

    @ApiModelProperty(example = "三方平台类型名称")
    private String thirdName;

    @ApiModelProperty(example = "活动名称")
    private String activityName;

    @ApiModelProperty("三方平台活动编号")
    private String thirdCode;

    /**
     * 活动规则
     */
    @ApiModelProperty(example = "规则类型 0-金额扣减 1-买单优惠")
    private Byte ruleType;

    @ApiModelProperty(example = "券面值")
    private BigDecimal couponFee;

    @ApiModelProperty(example = "券客户购买金额")
    private BigDecimal couponCostFee;

    @ApiModelProperty(example = "选择类型 0-折扣 1-满减")
    private Byte discountType;

    @ApiModelProperty(example = "折扣")
    private BigDecimal discount;

    @ApiModelProperty(example = "每满多少元")
    private BigDecimal fullFee;

    @ApiModelProperty(example = "减多少元")
    private BigDecimal reduceFee;

    @ApiModelProperty(example = "最多参与金额")
    private BigDecimal limitFee;

    /**
     * 使用规则
     */
    @ApiModelProperty(example = "单笔订单使用上限")
    private Integer useLimit;

    @ApiModelProperty(example = "是否与其他平台活动共享 0-否 1-是")
    private Byte isActivityShare;

    @ApiModelProperty(example = "是否与其他活动类型共享 0-否 1-是")
    private Byte isThirdShare;

    @ApiModelProperty(value = "第三方活动券码，可能为空")
    private List<String> thirdActivityCodeList;

    @ApiModelProperty(value = "参与活动的金额，可能为空")
    private BigDecimal joinFee;

    @ApiModelProperty(value = "不参与活动金额，可能为空")
    private BigDecimal notJoinFee;

    @Data
    public static class StoreInfo {

        @ApiModelProperty("门店guid")
        private String guid;

        @ApiModelProperty("门店名称")
        private String storeName;

        public StoreInfo(String guid, String storeName) {
            this.guid = guid;
            this.storeName = storeName;
        }
    }

    @Data
    public static class ReduceRule {

        @ApiModelProperty("券面值")
        private BigDecimal couponFee;

        @ApiModelProperty("券客户购买金额")
        private BigDecimal couponCostFee;
    }

    @Data
    public static class DiscountRule {

        @ApiModelProperty("选择类型 0-折扣 1-满减")
        private Byte discountType;

        @ApiModelProperty("折扣")
        private BigDecimal discount;

        @ApiModelProperty("每满多少元")
        private BigDecimal fullFee;

        @ApiModelProperty("减多少元")
        private BigDecimal reduceFee;

        @ApiModelProperty("最多参与金额")
        private BigDecimal limitFee;
    }

    @ApiModelProperty(value = "抵扣金额")
    private BigDecimal deductionAmount;

}
