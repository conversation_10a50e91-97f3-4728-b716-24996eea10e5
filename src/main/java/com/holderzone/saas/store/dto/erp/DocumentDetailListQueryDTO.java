package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/16
 * @description 查询出入库单详情列表
 */
@Data
@ApiModel(value = "查询出入库单详情列表")
@EqualsAndHashCode(callSuper = false)
public class DocumentDetailListQueryDTO implements Serializable {

    private static final long serialVersionUID = 7848864380441474471L;

    @ApiModelProperty("单据guid列表")
    private List<String> documentGuidList;

}
