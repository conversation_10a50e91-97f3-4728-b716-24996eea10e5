package com.holderzone.saas.store.dto.order.request.bill;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "调整订单记录DTO")
public class AdjustOrderRecordDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    private String guid;

    /**
     * 订单guid
     */
    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    /**
     * 操作员guid
     */
    @ApiModelProperty(value = "操作员guid")
    private String operatorGuid;

    @ApiModelProperty(value = "调整订单原因")
    private String reason;

    /**
     * 操作员名称
     */
    @ApiModelProperty(value = "操作员名称")
    private String operatorName;

    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime operationTime;

}
