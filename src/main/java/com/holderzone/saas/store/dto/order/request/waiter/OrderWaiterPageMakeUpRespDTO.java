package com.holderzone.saas.store.dto.order.request.waiter;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> R
 * @date 2020/12/9 11:16
 * @description
 */
@Data
@ApiModel(value = "订单服务员分页查询返回参数DTO---后台补录")
public class OrderWaiterPageMakeUpRespDTO {
    @ApiModelProperty(value = "开台时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "结账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTime;

    @ApiModelProperty(value = "桌台名称")
    private String diningTableName;

    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "订单金额")
    private String orderFee;

    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "喊客员")
    private OrderWaiterInfoDTO callWaiter;

    @ApiModelProperty(value = "服务员")
    private OrderWaiterInfoDTO serveWaiter;

    @ApiModelProperty(value = "传菜员")
    private OrderWaiterInfoDTO passDishesWaiter;

    @ApiModelProperty(value = "收台员")
    private OrderWaiterInfoDTO tidyWaiter;

    @ApiModelProperty(value = "洗碗员")
    private OrderWaiterInfoDTO mopWaiter;
}
