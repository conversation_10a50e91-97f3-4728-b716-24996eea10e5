package com.holderzone.saas.store.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel(value = "报表基础模型")
@NoArgsConstructor
@AllArgsConstructor
public class ReportBaseDTO {

    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "值")
    private Object value;
    @ApiModelProperty(value = "百分比")
    private double percent;

    public ReportBaseDTO(String name) {
        this.name = name;
    }

    public ReportBaseDTO(String name, Object value) {
        this.name = name;
        this.value = value;
    }
}
