package com.holderzone.saas.store.dto.weixin.deal;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class VolumeOrderQueryDTO implements Serializable {

    private static final long serialVersionUID = 2681969638533473972L;

    @ApiModelProperty(value = "营销活动guid")
    private String activityGuid;

    @ApiModelProperty(value = "优惠券code list")
    private List<String> volumeCodes;

    @ApiModelProperty(value = "是否使用会员优惠")
    private Boolean useMemberDiscountFlag;

    @ApiModelProperty(value = "商品明细")
    @NotEmpty(message = "商品明细不能为空")
    private List<DineInItemDTO> dineInItemList;

    @ApiModelProperty(value = "营销活动所选列表")
    private List<ActivitySelectDTO> activitySelectList;

    /**
     * 是否首次进入
     */
    @ApiModelProperty(value = "是否首次进入")
    private Boolean isFirst;

    @ApiModelProperty(value = "是否使用限时特价")
    private Boolean useSpecialsActivityFlag;

}
