package com.holderzone.saas.store.dto.erp.erpretail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class SaleSkuSumDTO {

    @ApiModelProperty(value = "sevenDaySkuSum")
    private int sevenDaySkuSum;

    @ApiModelProperty(value = "thirtyDaySkuSum")
    private int thirtyDaySkuSum;

}
