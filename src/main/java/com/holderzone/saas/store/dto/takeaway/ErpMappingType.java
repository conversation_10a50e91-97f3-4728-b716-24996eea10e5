package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ErpMappingType implements Serializable {

    private static final long serialVersionUID = 7768530381749848051L;

    @ApiModelProperty("ERP方商品分类ID")
    private String erpItemTypeId;

    @ApiModelProperty("ERP方商品分类名称")
    private String erpItemTypeName;

    @ApiModelProperty("ERP方商品分类下的商品")
    private List<ErpMappingItem> erpItemList;
}
