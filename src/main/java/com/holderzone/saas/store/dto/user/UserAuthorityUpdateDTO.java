package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Accessors(chain = true)
@ApiModel("用户权限保存请求实体")
public class UserAuthorityUpdateDTO implements Serializable {

    @ApiModelProperty(value = "是否退菜")
    private Integer isReturn;

    @ApiModelProperty(value = "是否挂账")
    private Integer isDebt;

    @ApiModelProperty(value = "权限名称列表")
    private List<AuthorityDTO> authorityDTOS;

    @ApiModelProperty(value = "权限拥有列表")
    private List<UserAuthorityDTO> userAuthorityDTOS;

    public List<AuthorityDTO> getAuthorityDTOS() {
        return authorityDTOS;
    }

    public void setAuthorityDTOS(List<AuthorityDTO> authorityDTOS) {
        this.authorityDTOS = authorityDTOS;
    }

    public List<UserAuthorityDTO> getUserAuthorityDTOS() {
        return userAuthorityDTOS;
    }

    public Integer getIsReturn() {
        return isReturn;
    }

    public void setIsReturn(Integer isReturn) {
        this.isReturn = isReturn;
    }

    public Integer getIsDebt() {
        return isDebt;
    }

    public void setIsDebt(Integer isDebt) {
        this.isDebt = isDebt;
    }

    public void setUserAuthorityDTOS(List<UserAuthorityDTO> userAuthorityDTOS) {
        this.userAuthorityDTOS = userAuthorityDTOS;
    }

    public void setUserAuthorityDTO(List<UserAuthorityDTO> userAuthorityDTOS) {
        this.userAuthorityDTOS = userAuthorityDTOS;
    }

    public UserAuthorityUpdateDTO(List<AuthorityDTO> authorityDTOS, List<UserAuthorityDTO> userAuthorityDTOS) {
        this.authorityDTOS = authorityDTOS;
        this.userAuthorityDTOS = userAuthorityDTOS;
    }

    public UserAuthorityUpdateDTO(List<AuthorityDTO> authorityDTOS) {
        this.authorityDTOS = authorityDTOS;
    }

    public UserAuthorityUpdateDTO() {

    }
}
