package com.holderzone.saas.store.dto.deposit.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class OperationQueryRespDTO implements Serializable {

    private static final long serialVersionUID = -2773042054434843563L;

    @ApiModelProperty(value = "操作时间")
    private String operationTime;

    @ApiModelProperty(value = "操作方式：0：存入，1：取出")
    private int operationWay;

    @ApiModelProperty(value = "操作人")
    private String operator;

    @ApiModelProperty(value = "商品集合")
    private List<GoodsSimpleRespDTO> goodsList;

    @ApiModelProperty(value = "寄存备注")
    private String remark;

}
