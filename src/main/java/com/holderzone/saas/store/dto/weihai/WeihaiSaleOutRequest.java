package com.holderzone.saas.store.dto.weihai;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 微海销售出库API请求实体
 */
@Data
public class WeihaiSaleOutRequest {

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 账单类型：堂食、店内销售、美团、外卖、饿了么、到店等
     */
    private String billingType;

    /**
     * 营业日期: yyyy-MM-dd
     */
    private String businessDate;

    /**
     * 桌台名称
     */
    private String deskName;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 流水金额
     */
    private BigDecimal flowAmount;

    /**
     * 是否调整单据
     */
    private Integer isAdjustOrder;

    /**
     * 乐才门店Id
     */
    private String lcDepartId;

    /**
     * 下单时间: yyyy-MM-dd HH:mm:ss
     */
    private String openTime;

    /**
     * 账单号
     */
    private String orderNo;

    /**
     * 实收金额
     */
    private BigDecimal paidAmount;

    /**
     * 客流: 就餐的人数，默认为: 传1
     */
    private String passengerFlow;

    /**
     * 商品明细列表
     */
    private List<ProductDetail> productList;

    /**
     * 商品明细
     */
    @Data
    public static class ProductDetail {

        /**
         * 关联加料
         */
        private List<AddedMaterial> addedMaterial;

        /**
         * 商品编码
         */
        private String foodCode;

        /**
         * 商品优惠金额
         */
        private BigDecimal foodDiscountAmount;

        /**
         * 商品收入金额
         */
        private BigDecimal foodIncomeAmount;

        /**
         * 商品名称
         */
        private String foodName;

        /**
         * 商品金额
         */
        private BigDecimal foodOriginalAmount;

        /**
         * 商品分类
         */
        private String foodStyle;

        /**
         * 是否调整单据
         */
        private Integer isAdjustOrder;

        /**
         * 是否成本管控 0否 1是
         */
        private Integer isCost;

        /**
         * 乐才门店Id
         */
        private String lcDepartId;

        /**
         * 订单编号
         */
        private String orderNo;

        /**
         * 售价
         */
        private String price;

        /**
         * 销售数量
         */
        private String salesQuantity;

        /**
         * 规格
         */
        private String specification;

    }

    /**
     * 加料信息
     */
    @Data
    public static class AddedMaterial {

        /**
         * 加料商品编码
         */
        private String foodCode;

        /**
         * 加料名称
         */
        private String foodName;

        /**
         * 是否成本管控 0否 1是
         */
        private Integer isCost;

        /**
         * 加料数量
         */
        private String salesQuantity;

        /**
         * 加料规格
         */
        private String specification;

    }
}