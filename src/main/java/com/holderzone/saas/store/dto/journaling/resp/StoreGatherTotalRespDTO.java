package com.holderzone.saas.store.dto.journaling.resp;

import com.holderzone.framework.util.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/5/9 18:23
 * @description
 */
@Data
@ApiModel("门店汇总列表返回DTO")
public class StoreGatherTotalRespDTO {
    @ApiModelProperty(value = "总应收金额")
    private BigDecimal orderFeeTotal = BigDecimal.ZERO;
    @ApiModelProperty(value = "总优惠金额")
    private BigDecimal discountFeeTotal = BigDecimal.ZERO;
    @ApiModelProperty(value = "总实收金额")
    private BigDecimal actuallyPayFeeTotal = BigDecimal.ZERO;
    @ApiModelProperty(value = "总客流量")
    private Integer guestCountTotal = 0;
    @ApiModelProperty(value = "总订单数")
    private Integer orderCountTotal = 0;

    @ApiModelProperty(value = "总单均")
    private BigDecimal orderAverageFeeTotal = BigDecimal.ZERO;

    @ApiModelProperty(value = "总客均")
    private BigDecimal guestAverageFeeTotal = BigDecimal.ZERO;

    @ApiModelProperty(value = "总平均用餐时长（分）")
    private Integer orderAverageTimeTotal = 0;

    @ApiModelProperty(value = "分页数据")
    private Page<StoreGatherReportRespDTO> respDataPage;
}
