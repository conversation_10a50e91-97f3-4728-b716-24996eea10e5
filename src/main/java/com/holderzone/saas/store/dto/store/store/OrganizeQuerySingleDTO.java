package com.holderzone.saas.store.dto.store.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreQueryPageDTO
 * @date 2018/07/23 下午5:23
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class OrganizeQuerySingleDTO implements Serializable {

    private static final long serialVersionUID = 2243187305975043954L;

    /**
     * 组织guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "组织guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "组织guid", required = true)
    private String organizationGuid;

    /**
     * 是否查询已删除的组织
     */
    @ApiModelProperty(value = "是否查询已删除的组织。0(or null)=不查询，1=查询。")
    private Integer deleted;

    /**
     * 创建组织的用户guid
     */
    @ApiModelProperty(value = "创建组织的用户guid")
    private String createUserGuid;
}
