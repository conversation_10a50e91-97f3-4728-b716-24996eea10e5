package com.holderzone.saas.store.dto.report.resp;

import com.holderzone.saas.store.dto.trade.constant.PaymentType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverDetailRespDTO
 * @date 2018/10/08 17:37
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class HandoverDetailRespDTO implements Serializable {

    @ApiModelProperty(value = "支付方式统计结果集")
    private List<HandoverDetail> result;

    @ApiModel
    @Data
    public static class HandoverDetail {

        @ApiModelProperty(value = "支付方式type")
        private Integer paymentType;

        @ApiModelProperty(value = "支付方式name")
        private String paymentName;

        @ApiModelProperty(value = "支付金额")
        private BigDecimal amount;

    }

}
