package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@Accessors(chain = true)
public class TakeoutOrderStatisticsRespDTO implements Serializable {

    private static final long serialVersionUID = -8362647607588364201L;

    @ApiModelProperty("待处理订单数量")
    private Integer unOrderCount;

    @ApiModelProperty("异常状态为非初始状态数量")
    private Integer exOrderCount;

    @ApiModelProperty("待配送订单数量")
    private Integer unShippingCunt;

    @ApiModelProperty("配送中订单数量")
    private Integer shippingCount;

    @ApiModelProperty("已完成订单数量")
    private Integer successCount;

    @ApiModelProperty("已取消订单数量")
    private Integer cancelCount;
}
