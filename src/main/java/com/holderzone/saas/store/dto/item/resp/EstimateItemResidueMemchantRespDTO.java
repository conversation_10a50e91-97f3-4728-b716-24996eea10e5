package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateItemResidueMemchantRespDTO
 * @date 2019/05/08 11:06
 * @description //TODO  商户后台 - 估清设置 - 剩余可售数列表
 * @program holder-saas-store-dto
 */
@Data
@NoArgsConstructor
@ApiModel(value = "估清-剩余可售列表")
public class EstimateItemResidueMemchantRespDTO {

    /**
     * 估清业务主键 guid
     */
    @ApiModelProperty(value = "估清业务主键 guid")
    private String guid;
    /**
     * sku guid
     */
    @ApiModelProperty(value = "sku guid")
    private String skuGuid;

    /**
     * 规格名称
     */
    @ApiModelProperty(value = "规格名称")
    private String skuName;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    /**
     * 分类guid
     */
    @ApiModelProperty(value = "分类guid")
    private String typeGuid;
    /**
     * 分类名称
     */
    @ApiModelProperty(value = "分类名称")
    private String typeName;
    /**
     * 菜品类型
     */
    @ApiModelProperty(value = "菜品类型")
    private Integer itemType;
    /**
     * 菜品类型名称
     */
    @ApiModelProperty(value = "菜品类型名称")
    private String itemTypeName;

    /**
     * 当前剩余数量
     */
    @ApiModelProperty(value = "当前剩余数量")
    private BigDecimal residueQuantity = new BigDecimal(-1);

    /**
     * 单位
     */
    @ApiModelProperty(value = " 单位")
    private String unit;

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;
}
