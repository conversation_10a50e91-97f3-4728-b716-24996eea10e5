package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ShortMsgPollingRespDTO
 * @date 2018/09/17 17:54
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class ShortMsgPollingRespDTO implements Serializable {

    @ApiModelProperty(value = "响应码，成功=10000")
    private String code;

    @ApiModelProperty(value = "响应信息")
    private String msg;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "主题")
    private String subject;

    @ApiModelProperty(value = "body")
    private String body;

    @ApiModelProperty(value = "聚合支付平台订单号")
    private String orderHolderNo;

    @ApiModelProperty(value = "支付功能id")
    private String payPowerId;

    @ApiModelProperty(value = "银行订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态")
    private String paySt;

    @ApiModelProperty(value = "银行交易流水号")
    private String bankTransactionId;

    @ApiModelProperty(value = "商户自己订单号")
    private String orderGUID;

    @ApiModelProperty(value = "本次支付唯一标识")
    private String payGUID;

    /**
     * 订单创建时间（是）
     * 格式： yyyyMMdd
     */
    @ApiModelProperty(value = "订单创建时间")
    private String orderDt;

    /**
     * 支付完成时间（否）
     * 格式： yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "支付完成时间")
    private String timePaid;

    @ApiModelProperty(value = "订单附加描述")
    private String description;

    @ApiModelProperty(value = "该笔订单所扣费用，单位分")
    private String fee;

    @ApiModelProperty(value = "商户下单时所传")
    private String extra;

    /**
     * 平台订单创建时间（否）
     * 商户下单的时间，格式：
     * yyyy-MM-dd HH:mm:ss
     */
    @ApiModelProperty(value = "平台订单创建时间")
    private String created;

    @ApiModelProperty(value = "支付渠道Id")
    private String payChannelId;

    @ApiModelProperty(value = "附加数据，透传字段")
    private String attachData;

    @ApiModelProperty(value = "二维码链接")
    private String codeUrl;

    @ApiModelProperty(value = "微信公众号或者小程序，支付唤起参数")
    private String prepayInfo;

    @ApiModelProperty(value = "签名")
    private String signature;
}
