package com.holderzone.saas.store.dto.report.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderDetailQueryDTO
 * @date 2018/10/17 18:21
 * @description 订单详情查询条件DTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class OrderDetailQueryDTO {

    @ApiModelProperty(value = "订单Guid")
    private String  orderGuid;
    @ApiModelProperty(value = "反结账订单Guid")
    private String  recoveryUuid;
}
