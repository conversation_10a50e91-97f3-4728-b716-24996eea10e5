package com.holderzone.saas.store.dto.user;

import com.holderzone.saas.store.enums.user.LoginSourceEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Deprecated
@NoArgsConstructor
@ApiModel("用户登出DTO")
public class UserLogoutDTO implements Serializable {

    private static final long serialVersionUID = -3586900417130787058L;

    @ApiModelProperty("Token字符串")
    private String token;

    /**
     * @see LoginSourceEnum
     */
    @ApiModelProperty("登录来源")
//    private String source;
    private Integer source;
}
