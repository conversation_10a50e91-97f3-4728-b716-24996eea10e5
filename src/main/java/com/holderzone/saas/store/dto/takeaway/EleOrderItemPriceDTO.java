package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 饿了么订单商品明细价格
 */
@Data
public class EleOrderItemPriceDTO implements Serializable {

    private static final long serialVersionUID = -7820935209083353029L;

    @ApiModelProperty(value = "商品id")
    private String extendCode;

    @ApiModelProperty(value = "商品总价")
    private Double total;

    @ApiModelProperty(value = "数量")
    private Integer quantity;

    @ApiModelProperty(value = "用户侧价格")
    private Double userPrice;

}
