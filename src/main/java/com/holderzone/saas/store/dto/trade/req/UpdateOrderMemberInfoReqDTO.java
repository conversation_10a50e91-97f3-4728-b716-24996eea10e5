package com.holderzone.saas.store.dto.trade.req;

import lombok.Data;

import java.io.Serializable;


/**
 * 更新订单会员信息
 */
@Data
public class UpdateOrderMemberInfoReqDTO implements Serializable {

    private static final long serialVersionUID = -7739035031421813301L;

    /**
     * 订单guid
     */
    private String orderGuid;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 会员持卡guid
     */
    private String memberCardGuid;

    /**
     * 会员手机号
     */
    private String memberPhone;

    /**
     * 会员名称
     */
    private String memberName;

}
