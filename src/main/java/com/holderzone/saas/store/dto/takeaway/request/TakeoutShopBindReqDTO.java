package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutShopBindReqDTO
 * @date 2018/09/25 9:07
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@ApiModel
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class TakeoutShopBindReqDTO extends BaseDTO {


    @NotNull
    @Min(value = 1, message = "外卖类型(1：美团，2：饿了么,3:自营外卖,4:抖音外卖,5:京东外卖)")
    @Max(value = 5, message = "外卖类型(1：美团，2：饿了么,3:自营外卖,4:抖音外卖,5:京东外卖)")
    @ApiModelProperty(value = "外卖类型(1：美团，2：饿了么,3:自营外卖,4:抖音外卖,5:京东外卖)")
    private Integer takeoutType = 4;

    @NotNull
    @Min(value = 0, message = "操作绑定的状态(1：绑定操作，0，解除绑定操作)")
    @Max(value = 1, message = "操作绑定的状态(1：绑定操作，0，解除绑定操作)")
    @ApiModelProperty(value = "操作绑定的状态(1：绑定操作，0，解除绑定操作)")
    private Integer bindingStatus;

    /**
     * 品牌guid，京东外卖需传递
     */
    private String brandGuid;
}
