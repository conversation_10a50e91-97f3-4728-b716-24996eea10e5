package com.holderzone.saas.store.dto.print.template.printable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 二维码
 *
 * <AUTHOR>
 * @date 2018/09/27 14:55
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class QrCode implements Serializable {

    private static final long serialVersionUID = -4333352987557771164L;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "宽度(像素)")
    private int width = 320;

    @ApiModelProperty(value = "上下文间距(像素)")
    private int margin = 24;

    public QrCode(String content) {
        this.content = content;
    }
}
