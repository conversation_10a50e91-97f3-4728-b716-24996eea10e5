package com.holderzone.saas.store.dto.deposit.req;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 寄存商品同步erp库存请求参数
 *
 * <AUTHOR>
 * @date 2021/6/3 18:23
 */
@Data
public class DepositErpSyncDTO {
    /**
     * 门店GUID
     */
    private String storeId;
    /**
     * 是否入库 true 入库  false 出库
     */
    private Boolean isDeposit;
    /**
     * 寄存记录GUID
     */
    private String thirdNo;
    /**
     * 操作人guid
     */
    private String userGuid;
    /**
     * 操作人名称
     */
    private String username;
    /**
     * 寄存商品信息
     */
    private List<DepositDish> depositDishes;
    /**
     * 业务类型 1 调整单   0 寄存
     */
    private int businessType;
    /**
     * 原订单guid
     */
    private String orderGuid;
    /**
     * 营业时间
     */
    private LocalDate businessDate;
    /**
     * 原订单下单时间
     */
    private LocalDateTime orderCreateTime;

    /**
     * 桌台名称(区域+桌台code)
     */
    private String diningTableName;

    /**
     * 订单金额（商品总额+附加费）
     */
    private BigDecimal orderFee;

    /**
     * 实收金额=订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））
     * 应收金额=订单金额-优惠金额-订金(押金)
     */
    private BigDecimal actuallyPayFee;

    /**
     * 客人数
     */
    private Integer guestCount;

    /**
     * 是否外卖
     */
    private Boolean takeawayFlag;
}
