package com.holderzone.saas.store.dto.erp.erpretail;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@ApiModel("库存汇总信息查看")
public class RepertorySumDTO {

    @ApiModelProperty("库存总量")
    private BigDecimal repertorySum;

    @ApiModelProperty("sku总量")
    private int skuSum;

    @ApiModelProperty("7日动销")
    private String sevenDaySkuSum;


    @ApiModelProperty("30日动销")
    private String thirtyDaySkuSum;

}
