package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "方案菜品删除入参")
public class PricePlanItemRemoveReqDTO {

    @ApiModelProperty(value = "方案guid")
    private String planGuid;

    @ApiModelProperty(value = "方案菜品guid")
    private List<String> itemGuidList;
}
