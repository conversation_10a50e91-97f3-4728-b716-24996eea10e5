package com.holderzone.saas.store.dto.queue;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemGuidDTO
 * @date 2019/03/28 14:05
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Setter
@Getter
public class ItemGuidDTO extends BaseDTO {
    @ApiModelProperty(value = "item guid" ,required = true)
    private String itemGuid;

    @ApiModelProperty("品牌guid")
    @NotEmpty(message = "品牌信息不能为空")
    private String brandGuid;

    private String enterpriseGuid;
}