package com.holderzone.saas.store.dto.item.req.price;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 批量下架保存入参实体
 * @date 2021/11/4 12:20
 * @className: public class PlanPriceSoldOutReqDTO {
 */
@Data
@ApiModel(value = "批量下架保存入参实体")
public class PlanPriceSoldOutReqDTO {

    @ApiModelProperty(value = "推送类型：1-立即推送，2-按时间推送")
    private Integer pushType;

    @ApiModelProperty(value = "推送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pushDate;

    @ApiModelProperty(value = "下架方式：1-售完下架，2-立即下架（直接删除）")
    private Integer soldOutType;

    @ApiModelProperty(value = "商品规格GuidList")
    private List<String> skuGuidList;

    @ApiModelProperty(value = "方案GuidList")
    private List<String> planGuidList;

}
