package com.holderzone.saas.store.dto.journaling.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SalesVolumeReqDTO
 * @date 2019/12/25 16:29
 * @description 商品销量统计请求入参
 * @program holder-saas-store
 */
@Data
@ApiModel("商品销量统计请求入参")
public class SalesVolumeReqDTO extends JournalWebBaseReqDTO {

    @ApiModelProperty("商品类型:1、套餐，3、称重商品，4、单品、5团餐")
    private Integer itemType;

    @ApiModelProperty("分类GUID")
    private String typeGuid;

    @ApiModelProperty("商品名字")
    private String itemName;

    @ApiModelProperty("0:不排序,1:升序,2:降序")
    private Integer sort;

    @ApiModelProperty("按什么排序：")
    private String sortName;

    @ApiModelProperty(value = "就餐类型 0-正餐，1-快餐，2-外卖")
    private Integer cateringType;

    @ApiModelProperty(value = "套餐guid")
    private String pkgItemGuid;

    @ApiModelProperty(value = "是否根据门店分组")
    private Boolean groupByStoreFlag;

    @ApiModelProperty(value = "总订单数")
    private Long totalOrderCount;

    @ApiModelProperty(value = "总销售额")
    private BigDecimal totalSalesAmount;

    @ApiModelProperty(value = "总商品堂食实付金额")
    private BigDecimal totalDineInDiscountPrice;
}
