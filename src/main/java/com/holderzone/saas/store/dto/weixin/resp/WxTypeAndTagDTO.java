package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.saas.store.dto.item.resp.TypeSynRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxTypeAndTagDTO
 * @date 2019/2/22 17:48
 * @description 微信点餐标签和分类
 * @package com.holderzone.saas.store.dto.weixin.resp
 */
@Data
@ApiModel("门店标签和分类")
public class WxTypeAndTagDTO extends TypeSynRespDTO {

    @ApiModelProperty(value = "是否是商品分类,1:分类，0：标签")
    private Integer isType;

    @ApiModelProperty(value = "如果是标签，是否开启该标签，1：是，0：否")
    private Byte isOpened;
}
