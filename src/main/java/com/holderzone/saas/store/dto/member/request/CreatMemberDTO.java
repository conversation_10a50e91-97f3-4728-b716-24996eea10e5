package com.holderzone.saas.store.dto.member.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CreatMemberDTO
 * @date 2018/09/18 9:08
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class CreatMemberDTO extends BaseDTO{

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "手机号必川",required = true)
    private String phone;

    @ApiModelProperty(value = "性别 0：女，1:男，2：保密")
    private Byte sex;

    @ApiModelProperty(value = "密码,必传",required = true)
    private String password;

    @ApiModelProperty(value = "生日必传",required = true)
    private LocalDate birthday;

    @ApiModelProperty(value = "实体卡号")
    private String num;
}
