package com.holderzone.saas.store.dto.canal.req;

import com.holderzone.saas.store.enums.canal.MemberSynEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * <p>
 * 组织表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-16
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "HsmStoreReqDTO", description = "门店信息")
public class HsmStoreReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 同步操作类型::同步功能使用
     */
    @ApiModelProperty(value = "同步操作类型::同步功能使用")
    private MemberSynEnum sycEnum;

    /**
     * 门店的Guid
     */
    @ApiModelProperty(value = "门店主键")
    private String guid;


    /**
     * 外部门店主键
     */
    @ApiModelProperty(value = "外部门店主键",required = true)
    private String storeKey;

    /**
     * 商家信息GUID
     */
    @ApiModelProperty(value = "会员平台商家信息GUID")
    private String enterpriseGuid;

    /**
     * 外部商家主键
     */
    @ApiModelProperty(value = "外部商家主键",required = true)
    private String enterpriseKey;

    /**
     *  联盟ID
     */
    @ApiModelProperty(value = "联盟ID",required = true)
    private String allianceid;

    /**
     * 品牌信息GUID
     */
    @ApiModelProperty(value = "品牌信息GUID")
    private String brandGuid;

    /**
     * 外部商家对应的品牌主键
     */
    @ApiModelProperty(value = "外部商家对应的品牌主键")
    private String brandKey;

    /**
     * 门店编码
     */
    @ApiModelProperty(value = "门店编码")
    private String storeCode;

    /**
     * 门店类型,0加盟店,1直营店
     */
    @ApiModelProperty(value = "门店类型,0加盟店,1直营店",required = true)
    private Integer storeType;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 联系人名字
     */
    @ApiModelProperty(value = "联系人名字",required = true)
    private String contactName;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话",required = true)
    private String contactTel;

    /**
     * 门店图标
     */
    @ApiModelProperty(value = "门店图标")
    private String storeIcon;

    /**
     * 省编码
     */
    @ApiModelProperty(value = "省编码",required = true)
    private String provinceCode;

    /**
     * 省名字
     */
    @ApiModelProperty(value = "省名字")
    private String provinceName;

    /**
     * 城市编码
     */
    @ApiModelProperty(value = "城市编码",required = true)
    private String cityCode;

    /**
     * 城市名字
     */
    @ApiModelProperty(value = "城市名字",required = true)
    private String cityName;

    /**
     * 区编码
     */
    @ApiModelProperty(value = "区编码",required = true)
    private String areaCode;

    /**
     * 区名字
     */
    @ApiModelProperty(value = "区名字",required = true)
    private String areaName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址",required = true)
    private String addressDetail;

    /**
     * 门店描述
     */
    @ApiModelProperty(value = "门店描述")
    private String description;

    /**
     * 门店开始营业时间
     */
    @ApiModelProperty(value = "门店开始营业时间")
    private LocalTime businessStart;

    /**
     * 门店营业结束时间
     */
    @ApiModelProperty(value = "门店营业结束时间")
    private LocalTime businessEnd;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String latitude;

    /**
     *  状态,0禁用,1启用
     */
    @ApiModelProperty(value = "状态,0禁用,1启用")
    private int isEnable;
}
