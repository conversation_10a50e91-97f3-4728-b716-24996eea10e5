package com.holderzone.saas.store.dto.print.type;

import com.holderzone.saas.store.dto.kds.req.KdsPrinterBindUnbindReqDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/22
 * @description 打印分类模版创建/更新
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "打印分类模版创建/更新", description = "打印分类模版创建/更新")
public class PrintTypeTemplateDTO implements Serializable {

    private static final long serialVersionUID = 1796010772175593839L;

    @NotEmpty(message = "模版Guid不能为空", groups = PrintTypeTemplateDTO.Modify.class)
    @ApiModelProperty(value = "模版Guid")
    private String guid;

    @ApiModelProperty(value = "品牌guid")
    @NotBlank(message = "品牌guid不能为空", groups = PrintTypeTemplateDTO.Create.class)
    private String brandGuid;

    @ApiModelProperty(value = "模版名称")
    @NotBlank(message = "请填写模版名称")
    @Length(max = 20, message = "模版名称长度不能超过20")
    private String name;

    @Valid
    @ApiModelProperty(value = "分类列表")
    @NotEmpty(message = "分类不能为空")
    @Size(max = 99, message = "最多可以添加99个分类")
    private List<TemplateTypeDTO> typeDTOList;

    /**
     * @see InvoiceTypeEnum
     */
    @ApiModelProperty(value = "应用单据-InvoiceTypeEnum")
    @NotEmpty(message = "请选择应用单据")
    private List<String> invoiceType;

    @ApiModelProperty(value = "是否全部门店 0：否,1:是")
    @NotNull(message = "请选择是否全部门店")
    private Boolean isAllStore;

    @ApiModelProperty(value = "门店guid列表")
    private List<String> storeGuidList;

    public interface Create {

    }

    public interface Modify {

    }

}
