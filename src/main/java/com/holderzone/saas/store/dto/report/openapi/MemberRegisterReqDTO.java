package com.holderzone.saas.store.dto.report.openapi;

import com.holderzone.saas.store.dto.open.BaseReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 注册会员入参
 */
@Data
public class MemberRegisterReqDTO extends BaseReq {

    @NotBlank(message = "企业不能为空")
    private String enterpriseGuid;

    @NotBlank(message = "运营主体不能为空")
    private String operSubjectGuid;

    @NotBlank(message = "电话号码不能为空")
    @ApiModelProperty(name = "phoneNum", value = "会员电话号码", required = true)
    private String phoneNum;

    @ApiModelProperty(value = "会员注册来源,0后台添加,1POS机注册,2一体机注册,3后台导入，微网站(21微信扫码点餐，24微信注册.25微信C端后台注册)，微信小程序(51和惠多，52翼惠天下，53赚餐)", required = true)
    @NotNull(message = "会员注册来源不能为空")
    private Integer sourceType;

    @ApiModelProperty(value = "微信昵称", required = true)
    @NotEmpty(message = "会员昵称不能为空")
    private String nickName;

    @ApiModelProperty(value = "所属单位")
    private String workName;

    @ApiModelProperty(value = "联系地址")
    private String contactAddress;

    @ApiModelProperty("成长值")
    private Integer growthValue;
}
