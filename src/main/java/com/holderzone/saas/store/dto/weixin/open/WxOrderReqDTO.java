package com.holderzone.saas.store.dto.weixin.open;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/10
 * @description 微信订单
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "微信订单商品", description = "微信订单商品")
public class WxOrderReqDTO implements Serializable {

    private static final long serialVersionUID = -5077684506340317791L;

    @ApiModelProperty(value = "订单号")
    private String orderGuid;

    @ApiModelProperty(value = "订单已出餐商品列表")
    private List<WxOrderItemReqDTO> orderItemList;

}
