package com.holderzone.saas.store.dto.queue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.text.DecimalFormat;
import java.text.Format;
import java.text.NumberFormat;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HolderQueueItemDetailDTO
 * @date 2019/03/27 17:08
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
@Accessors(chain = true)
@ApiModel("排队元素信息")
public class HolderQueueItemDetailDTO extends HolderQueueItemDTO {
    @ApiModelProperty("队列guid")
    private String queueGuid;
    @ApiModelProperty("队列编号")
    private String queueCode;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("序号")
    private String code;
    @ApiModelProperty("状态 0:队列中,1:过号,2:叫号中,3:以就餐,4:已取消")
    private Byte status;
    @ApiModelProperty("叫号次数")
    private Integer callTimes = 0;
    @ApiModelProperty("前面有多好个号")
     private Integer before;
    @ApiModelProperty("当前叫号")
     private Integer currentOrder;
    @ApiModelProperty("排队开始时间")
    private LocalDateTime startTime;
    @ApiModelProperty("排队结束时间")
    private LocalDateTime endTime = LocalDateTime.now();
    @ApiModelProperty("桌台名称")
    private String tableName;
    @ApiModelProperty("桌台guid")
    private String tableGuid;
    @ApiModelProperty("区域名称")
    private String areaName;
    @ApiModelProperty("区域guid")
    private String areaGuid;
    @ApiModelProperty("编号")
    public String getOrder(){
        return getQueueCode()+getCode();
    }
}