package com.holderzone.saas.store.dto.order.request.bill;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillCalculateReqDTO
 * @date 2019/01/28 17:32
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class BillCalculateReqDTO extends BaseDTO {

    private static final long serialVersionUID = -8951585514851962893L;

    @ApiModelProperty(value = "订单guid")
    @OrderLockField
    private String orderGuid;

    @ApiModelProperty(value = "是否使用会员优惠")
    private Boolean useMemberDiscountFlag;

    @ApiModelProperty(value = "是否会员折扣")
    private Boolean isMemberDiscount = Boolean.TRUE;

    @ApiModelProperty(value = "1：会员登陆，2：会员登出，-1：不操作会员")
    private Integer memberLogin;

    @ApiModelProperty(value = "登录方式 0-扫码 1-手机号录入")
    private Integer loginType;

    @ApiModelProperty(value = "是否计算积分1：计算，2：不计算")
    private Integer memberIntegral;

    @ApiModelProperty(value = "是否是积分商城0：不是，1：是")
    private Integer memberIntegralStore;

    @ApiModelProperty(value = "会员卡guid")
    private String memberInfoCardGuid;

    @ApiModelProperty(value = "会员卡号")
    private String memberCardNum;

    @ApiModelProperty(value = "会员卡类型，0主卡，1权益卡，2联盟权益卡")
    private Integer cardType;

    @ApiModelProperty(value = "会员电话")
    private String memberPhone;

    @ApiModelProperty(value = "整单折扣")
    private BigDecimal wholeDiscount;

    @ApiModelProperty(value = "整单让价")
    private BigDecimal concessional;

    @ApiModelProperty(value = "优惠券code")
    private String volumeCode;

    @ApiModelProperty(value = "优惠券code list")
    private List<String> volumeCodes;

    /**
     * {@link com.holderzone.holder.saas.member.enums.volume.VolumeTypeEnum}
     */
    @ApiModelProperty(value = "优惠券type")
    private Integer volumeType;

    @ApiModelProperty(value = "1：验券，2：撤销，3：查询（不验券，只给微信用）")
    private Integer verify;

    @ApiModelProperty(value = "满减满折活动guid，如果为字符串0则为取消，如果传null  表示不变")
    private String activityGuid;

    @ApiModelProperty(value = "第N份优惠活动guid")
    private String nthActivityGuid;

    @ApiModelProperty(value = "是否需要详情")
    private Boolean isNeedActivityList;

    @ApiModelProperty(value = "是否查询 查询则不需要保存记录")
    private Boolean unNeedPersistence;

    @ApiModelProperty(value = "第三方活动信息列表")
    private List<String> thirdActivityDTOList;

    @ApiModelProperty(value = "是否整单折扣")
    private Boolean isWholeDiscount;

    @ApiModelProperty(value = "memberInfoGuid")
    private String memberInfoGuid;
}
