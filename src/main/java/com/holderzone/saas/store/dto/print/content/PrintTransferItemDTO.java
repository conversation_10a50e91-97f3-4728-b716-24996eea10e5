package com.holderzone.saas.store.dto.print.content;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.dto.print.content.base.TradeModeAware;
import com.holderzone.saas.store.dto.trade.req.TransferItemDetailsDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 转菜单
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PrintTransferItemDTO extends PrintBaseItemDTO implements TradeModeAware, PrintDataMockito {

    private static final long serialVersionUID = -7462642609416274020L;

    @NotBlank(message = "订单Guid不得为空")
    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @NotBlank(message = "订单号不得为空")
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "原桌台号")
    private String oldDiningTableName;

    @ApiModelProperty(value = "新桌台号")
    private String newDiningTableName;

    @ApiModelProperty(value = "操作人")
    private String operatorName;

    @ApiModelProperty(value = "更换时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime transferTime;

    @ApiModelProperty(value = "绑定的商品")
    private List<String> arrayOfItemGuid;

    @ApiModelProperty(value = "转移菜品")
    private List<TransferItemDetailsDTO> transferItemList;

    @Override
    public void applyMock() {
        super.applyMock();
    }
}
