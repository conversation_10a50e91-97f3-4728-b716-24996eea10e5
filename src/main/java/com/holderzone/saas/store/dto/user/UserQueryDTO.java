package com.holderzone.saas.store.dto.user;

import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "员工查询实体")
public class UserQueryDTO extends PageDTO implements Serializable {

    private static final long serialVersionUID = -7861944983104651453L;

    @ApiModelProperty(value = "广义组织Guid列表")
    private List<String> genericOrgGuids;

    @ApiModelProperty(value = "搜索关键字：姓名、手机号、员工帐号")
    private String searchKey;

    @ApiModelProperty(value = "创建人GUID", hidden = true)
    private String createStaffGuid;

    @ApiModelProperty(value = "是否启用")
    private Boolean isEnable;

    /**
     * 是否返回password：1是 其他否
     */
    private Integer showPassword;

    @ApiModelProperty(value = "按设备类型过滤掉不能登录的成员。登录设备类型：PC服务端- 0、PC平板- 1、小店通- 2、一体机- 3、POS机- 4、云平板- 5、" +
            "点菜宝(M1)- 6、PV1(带刷卡的点菜宝)- 7" +
            "\n 备注：登陆验证必传，与baseDTO中的设备类型不同")
    private Integer deviceTypeCode;
}
