package com.holderzone.saas.store.dto.item.common;

import com.holderzone.framework.log.busines.Platform;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemLogDTO
 * @date 2019/03/01 上午11:16
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class ItemLogDTO extends MchntItemBaseDTO {
    /**
     * 操作对象
     */
    private List<String> operationTarget;
    /**
     * 操作子功能
     */
    private String button;

    /**
     * 模块
     */
    private String module;
    /**
     * 请求路径
     */
    private String requestUri;
    /**
     * MERCHANTBACK("商家后台", "3"),
     *     CASHIERSYSTEM("收银系统", "4");
     */
    private Platform platform;
}
