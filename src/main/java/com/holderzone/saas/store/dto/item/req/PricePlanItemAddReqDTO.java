package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.item.common.PlanItemDTO;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(value = "价格方案菜品保存入参")
@Data
public class PricePlanItemAddReqDTO {

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "方案guid")
    private String planGuid;

    @ApiModelProperty(value = "方案菜品详情")
    private List<PlanItemDTO> planItemDTOList;

    @ApiModelProperty(value = "方案保存信息")
    private List<ItemWebRespDTO> itemWebRespDTOList;
}
