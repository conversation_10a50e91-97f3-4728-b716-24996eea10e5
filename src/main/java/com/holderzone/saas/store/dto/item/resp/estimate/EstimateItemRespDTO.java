package com.holderzone.saas.store.dto.item.resp.estimate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 估清商品返回DTO
 * @date 2022/4/13 10:05
 * @className: EstimateItemRespDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "估清商品返回DTO")
public class EstimateItemRespDTO implements Serializable {

    private static final long serialVersionUID = 6942762650058341351L;

    @NotNull(message = "商品Guid不能为空")
    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "估清规格信息列表")
    private List<EstimateSkuRespDTO> skuRespList;

    /**
     * 估清修改时间
     */
    @ApiModelProperty(value = "估清修改时间", hidden = true)
    private LocalDateTime estimateGmtModified;
}
