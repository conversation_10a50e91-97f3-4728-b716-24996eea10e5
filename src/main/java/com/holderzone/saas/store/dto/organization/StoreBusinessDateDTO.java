package com.holderzone.saas.store.dto.organization;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.Objects;

@ApiModel("营业时间段DTO")
public class StoreBusinessDateDTO {

    @ApiModelProperty(value = "营业开始时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime businessStart;

    @ApiModelProperty(value = "营业结束时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime businessEnd;

    public StoreBusinessDateDTO() {

    }

    public StoreBusinessDateDTO(LocalDateTime businessStart, LocalDateTime businessEnd) {
        this.businessStart = businessStart;
        this.businessEnd = businessEnd;
    }

    public LocalDateTime getBusinessStart() {
        return businessStart;
    }

    public void setBusinessStart(LocalDateTime businessStart) {
        this.businessStart = businessStart;
    }

    public LocalDateTime getBusinessEnd() {
        return businessEnd;
    }

    public void setBusinessEnd(LocalDateTime businessEnd) {
        this.businessEnd = businessEnd;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        StoreBusinessDateDTO that = (StoreBusinessDateDTO) o;
        return businessStart.equals(that.businessStart) &&
                businessEnd.equals(that.businessEnd);
    }

    @Override
    public int hashCode() {
        return Objects.hash(businessStart, businessEnd);
    }
}
