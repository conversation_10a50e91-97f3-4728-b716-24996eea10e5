package com.holderzone.saas.store.dto.store.table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.business.reserve.ReserveRecordDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableDO
 * @date 2018/07/24 上午9:55
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class TableDTO implements Serializable {

    private static final long serialVersionUID = -6941480460180084977L;

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 区域guid
     */
    @ApiModelProperty(value = "区域guid")
    private String areaGuid;

    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaName;

    /**
     * 桌台guid
     */
    @ApiModelProperty(value = "桌台guid")
    private String tableGuid;

    /**
     * 桌台名称
     */
    @ApiModelProperty(value = "桌台名称")
    private String name;

    /**
     * 桌台编号
     */
    @ApiModelProperty(value = "桌台编号")
    private String code;

    /**
     * 桌台座位数
     */
    @ApiModelProperty(value = "桌台座位数")
    private Integer seats;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 是否已启用
     * 0=未启用
     * 1=已启用
     * 默认1
     */
    @ApiModelProperty(value = "是否已启用。0=未启用，1=已启用。")
    private Integer enable;

    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    @ApiModelProperty(value = "是否已删除。0=未删除，1=已删除。")
    private Integer deleted;

    /**
     * 是否已占用
     * 0=未占用
     * 1=已占用
     * 默认0
     * 占用时：不允许 禁用/删除
     */
    @ApiModelProperty(value = "是否已占用。0=未占用，1=已占用。")
    private Integer status;

    /**
     * 订单guid
     */
    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    /**
     * 预定记录实体
     */
    @ApiModelProperty(value = "预定记录实体")
    private ReserveRecordDTO reserveRecordDTO;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "主单单号")
    private String mainOrderGuid;

    /**
     * 客人数
     */
    @ApiModelProperty(value = "客人数")
    private Integer guestCount;

    /**
     * 是否接单：只要接了一个单都是接单
     */
    @ApiModelProperty(value = "是否接单：只要接了一个单都是接单")
    private Boolean isAcceptOrder;

}
