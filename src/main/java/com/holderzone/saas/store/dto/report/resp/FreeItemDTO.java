package com.holderzone.saas.store.dto.report.resp;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class FreeItemDTO implements Serializable {

    private static final long serialVersionUID = 2282082665228611899L;

    @ApiModelProperty(value = "品牌")
    public String brandName;

    @ApiModelProperty(value = "门店")
    public String storeName;

    @ApiModelProperty(value = "商品名称")
    public String goodsName;

    @ApiModelProperty(value = "商品规格")
    public String skuGuid;

    @ApiModelProperty(value = "商品guid")
    public String goodsGuid;

    @ApiModelProperty(value = "商品分类")
    public String goodsCategories;

    @ApiModelProperty(value = "赠送数量")
    public BigDecimal givenQuantity;

    @ApiModelProperty(value = "赠送金额")
    public BigDecimal givenRefund;

    @ApiModelProperty(value = "赠送率")
    public String givenRate;
}
