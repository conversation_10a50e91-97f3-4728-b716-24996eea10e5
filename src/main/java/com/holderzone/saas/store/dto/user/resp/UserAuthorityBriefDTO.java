package com.holderzone.saas.store.dto.user.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 员工授权简要信息
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @since 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAuthorityBriefDTO {

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "员工guid")
    private String userGuid;

    @ApiModelProperty(value = "员工姓名")
    private String userName;

    @ApiModelProperty(value = "员工账号")
    private String account;
}
