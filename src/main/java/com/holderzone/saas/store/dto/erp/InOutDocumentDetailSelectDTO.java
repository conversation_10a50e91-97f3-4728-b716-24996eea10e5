package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/30 下午 13:44
 * @description
 */
@ApiModel
public class InOutDocumentDetailSelectDTO {

    @ApiModelProperty("出入库明细唯一标识")
    private String guid;

    @ApiModelProperty("主单据Guid")
    private String documentGuid;

    @ApiModelProperty("物料Guid")
    private String materialGuid;

    @ApiModelProperty("物料Code")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("库存量")
    private BigDecimal stock;

    @ApiModelProperty("出入库数量")
    private BigDecimal count;

    @ApiModelProperty("单位Guid")
    private String unitGuid;

    @ApiModelProperty("单位名称")
    private String unitName;

    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    @ApiModelProperty("总价")
    private BigDecimal totalAmount;

    @ApiModelProperty("退货数量")
    private BigDecimal returnCount;

    @ApiModelProperty("物料单位的集合")
    private List<InOutDocumentMaterialUnitDTO> materialUnitList;

    public BigDecimal getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(BigDecimal returnCount) {
        this.returnCount = returnCount;
    }

    public List<InOutDocumentMaterialUnitDTO> getMaterialUnitList() {
        return materialUnitList;
    }

    public void setMaterialUnitList(List<InOutDocumentMaterialUnitDTO> materialUnitList) {
        this.materialUnitList = materialUnitList;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getDocumentGuid() {
        return documentGuid;
    }

    public void setDocumentGuid(String documentGuid) {
        this.documentGuid = documentGuid;
    }

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public BigDecimal getStock() {
        return stock;
    }

    public void setStock(BigDecimal stock) {
        this.stock = stock;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public String getUnitGuid() {
        return unitGuid;
    }

    public void setUnitGuid(String unitGuid) {
        this.unitGuid = unitGuid;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
}
