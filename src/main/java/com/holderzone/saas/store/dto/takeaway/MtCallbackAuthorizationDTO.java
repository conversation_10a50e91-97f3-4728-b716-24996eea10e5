package com.holderzone.saas.store.dto.takeaway;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create 2023-08-23
 * @description 美团业务授权码回调参数
 */
@Data
public class MtCallbackAuthorizationDTO {

    /**
     * 授权码，有效期为10分钟
     */
    @NotBlank(message = "授权码不能为空")
    private String code;

    @NotBlank(message = "数字签名不得为空")
    private String sign;

    @NotBlank(message = "开发者身份不得为空")
    private Long developerId;

    @NotBlank(message = "业务类型id不得为空")
    private Integer businessId;

    /**
     * 自定义回传参数
     */
    private String state;
}
