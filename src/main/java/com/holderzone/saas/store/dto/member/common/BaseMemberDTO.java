package com.holderzone.saas.store.dto.member.common;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseMemberDTO
 * @date 2018/09/17 13:56
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class BaseMemberDTO extends BaseDTO{

    @ApiModelProperty(value = "会员guid,修改会员资料,修改密码,忘记密码时，查询会员详情必传")
    private String memberGuid;

    @ApiModelProperty(value = "手机号或者卡号,根据卡号或手机号获取会员信息时必传")
    private String phone;



}
