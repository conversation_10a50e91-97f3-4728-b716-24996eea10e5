package com.holderzone.saas.store.dto.print.format.retail;

import com.holderzone.saas.store.dto.print.format.FormatDTO;
import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class RetailTakeoutFormatDTO extends FormatDTO {

    @NotNull(message = "平台不能为空")
    @ApiModelProperty(value = "平台", required = true)
    private FormatMetadata platform;

    @NotNull(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private FormatMetadata storeName;

    @NotNull(message = "支付消息不能为空")
    @ApiModelProperty(value = "支付消息", required = true)
    private FormatMetadata payMsg;

    @NotNull(message = "异常单概览不能为空")
    @ApiModelProperty(value = "异常单概览", required = true)
    private FormatMetadata abnormalMsg;

    @NotNull(message = "异常单详情不能为空")
    @ApiModelProperty(value = "异常单详情", required = true)
    private FormatMetadata abnormalDetail;

    @NotNull(message = "期望送达时间不能为空")
    @ApiModelProperty(value = "期望送达时间", required = true)
    private FormatMetadata expectTime;

    @NotNull(message = "下单时间不能为空")
    @ApiModelProperty(value = "下单时间", required = true)
    private FormatMetadata orderTime;

    @NotNull(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    private FormatMetadata orderNo;

    @NotNull(message = "备注不能为空")
    @ApiModelProperty(value = "备注", required = true)
    private FormatMetadata remark;

    @NotNull(message = "商品组件不能为空")
    @ApiModelProperty(value = "商品组件", required = true)
    private FormatMetadata itemLayout;

    @NotNull(message = "商品总计不能为空")
    @ApiModelProperty(value = "商品总计", required = true)
    private FormatMetadata itemSumTotal;

    @NotNull(message = "附加费明细不能为空")
    @ApiModelProperty(value = "附加费明细", required = true)
    private FormatMetadata additionalCharge;

    @NotNull(message = "折扣不能为空")
    @ApiModelProperty(value = "折扣", required = true)
    private FormatMetadata discount;

    @NotNull(message = "原价不能为空")
    @ApiModelProperty(value = "原价", required = true)
    private FormatMetadata originalPrice;

    @NotNull(message = "实付不能为空")
    @ApiModelProperty(value = "实付", required = true)
    private FormatMetadata actuallyPay;

    @NotNull(message = "客户名字不能为空")
    @ApiModelProperty(value = "客户名字", required = true)
    private FormatMetadata receiverName;

    @NotNull(message = "客户电话不能为空")
    @ApiModelProperty(value = "客户电话", required = true)
    private FormatMetadata receiverTel;

    @NotNull(message = "客户地址不能为空")
    @ApiModelProperty(value = "客户地址", required = true)
    private FormatMetadata receiverAddress;

    @NotNull(message = "操作员不能为空")
    @ApiModelProperty(value = "操作员", required = true)
    private FormatMetadata operator;

    @NotNull(message = "打印时间不能为空")
    @ApiModelProperty(value = "打印时间", required = true)
    private FormatMetadata printTime;

    @Override
    public void applyDefault() {
        super.applyDefault();
        if (platform == null) {
            platform = new FormatMetadata(2, 1, true);
        }
        if (storeName == null) {
            storeName = new FormatMetadata(1, 1, false);
        }
        if (payMsg == null) {
            payMsg = new FormatMetadata(2, 1, true);
        }
        if (abnormalMsg == null) {
            abnormalMsg = new FormatMetadata(2, 1, true);
        }
        if (abnormalDetail == null) {
            abnormalDetail = new FormatMetadata(1, 0, false);
        }
        if (expectTime == null) {
            expectTime = new FormatMetadata(1, 0, false);
        }
        if (orderTime == null) {
            orderTime = new FormatMetadata(1, 0, false);
        }
        if (orderNo == null) {
            orderNo = new FormatMetadata(1, 0, false);
        }
        if (remark == null) {
            remark = new FormatMetadata(2, 0, true);
        }
        if (itemLayout == null) {
            itemLayout = new FormatMetadata(1, 2, 0, false);
        }
        if (itemSumTotal == null) {
            itemSumTotal = new FormatMetadata(1, 3, false);
        }
        if (additionalCharge == null) {
            additionalCharge = new FormatMetadata(1, 0, false);
        }
        if (discount == null) {
            discount = new FormatMetadata(1, 0, false);
        }
        if (originalPrice == null) {
            originalPrice = new FormatMetadata(1, 2, false);
        }
        if (actuallyPay == null) {
            actuallyPay = new FormatMetadata(2, 2, true);
        }
        if (receiverName == null) {
            receiverName = new FormatMetadata(2, 0, true);
        }
        if (receiverTel == null) {
            receiverTel = new FormatMetadata(2, 0, true);
        }
        if (receiverAddress == null) {
            receiverAddress = new FormatMetadata(2, 0, true);
        }
        if (operator == null) {
            operator = new FormatMetadata(1, 0, false);
        }
        if (printTime == null) {
            printTime = new FormatMetadata(1, 0, false);
        }
    }
}
