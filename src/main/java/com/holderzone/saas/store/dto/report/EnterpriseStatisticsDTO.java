package com.holderzone.saas.store.dto.report;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseStatisticsDTO {


    @ApiModelProperty(notes = "订单成交总数")
    private int num;
    @ApiModelProperty(notes = "金额总数")
    private double money;
}
