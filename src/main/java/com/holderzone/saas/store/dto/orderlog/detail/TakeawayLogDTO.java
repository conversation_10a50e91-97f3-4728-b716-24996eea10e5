package com.holderzone.saas.store.dto.orderlog.detail;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayLogDTO
 * @date 2018/09/27 20:43
 * @description
 * @program holder-saas-store-dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TakeawayLogDTO {

    @ApiModelProperty(value = "操作类型（logOperationTypeEnum）")
    private Integer operationType;

    @ApiModelProperty(value = "操作类型描述")
    private String operationTypeDesc;

    @ApiModelProperty(value = "操作人guid")
    private String operationStaffGuid;

    @ApiModelProperty(value = "操作人姓名")
    private String operationStaffName;

    @ApiModelProperty(value = "操作时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "菜品详情")
    private List<Dish> dishesList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Dish implements Serializable {

        @ApiModelProperty(value = "菜品名称")
        private String dishName;

        @ApiModelProperty(value = "菜品编号")
        private String dishNumber;

        @ApiModelProperty(value = "单位")
        private String unit;

        @ApiModelProperty(value = "数量")
        private Integer count;

        @ApiModelProperty(value = "售卖价")
        private BigDecimal price;

        @ApiModelProperty(value = "属性")
        private String attribute;
    }
}
