package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.item.resp.CartCheckResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


@Data
@NoArgsConstructor
@Accessors(chain = true)
public class ItemShoppingCartCheckResponse {

    @ApiModelProperty(value = "错误码", required = true)
    private Integer code;

    @ApiModelProperty(value = "错误信息", required = true)
    private String message;

    @ApiModelProperty(value = "错误列表")
    List<CartCheckResult> data;

}
