package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Deprecated
@NoArgsConstructor
@ApiModel("用户Token解析结果信息")
public class UserTokenDTO implements Serializable {

    @ApiModelProperty("企业GUID")
    private String enterpriseGuid;

    @ApiModelProperty("门店编号")
    private String storeNo;

    @ApiModelProperty("用户GUID")
    private String userGuid;
}
