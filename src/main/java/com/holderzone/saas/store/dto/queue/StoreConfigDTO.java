package com.holderzone.saas.store.dto.queue;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.holderzone.framework.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreConfigDTO
 * @date 2019/05/09 18:29
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
@Accessors(chain = true)
public class StoreConfigDTO {
    private String guid;
    @ApiModelProperty(value = "门店guid",required = true)
    @NotEmpty(message = "请选择门店")
    private String storeGuid;
    private Boolean isEnableEat ;
    private Boolean isEnableRecovery;
    @Pattern(regexp = "[1,2,3]",message = "恢复至队列的位数,请填写1,2,3")
    private String recoveryNum ;
    private Boolean isEnableManualReset;
    private Boolean isEnableAutoReset;

    @ApiModelProperty("预定开始时间")
    @JsonFormat(pattern = "HH:mm")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime autoResetTiming;


    public Integer getRecoveryNum() {
        if(StringUtils.isEmpty(recoveryNum)){
            return null;
        }
        return Integer.valueOf(recoveryNum);
    }

    public static class DEFAULT{
        public static StoreConfigDTO build(String storeGuid){
            StoreConfigDTO storeConfigDTO = new StoreConfigDTO();
            storeConfigDTO.isEnableEat =true ;
            storeConfigDTO.isEnableRecovery = true;
            storeConfigDTO.isEnableManualReset = false;
            storeConfigDTO.isEnableAutoReset = true;
            storeConfigDTO.isEnableRecovery = true;
            storeConfigDTO.recoveryNum = "3";
            storeConfigDTO.isEnableAutoReset  = true;
            storeConfigDTO.autoResetTiming  = LocalTime.of(5,0);
            storeConfigDTO.storeGuid = storeGuid;
            return storeConfigDTO;
        }
    }
}