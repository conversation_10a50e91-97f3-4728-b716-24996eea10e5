package com.holderzone.saas.store.dto.user;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RolePermissionDTO
 * @date 18-11-15 上午10:50
 * @description
 * @program holder-saas-store-staff
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RolePermissionDTO extends RoleTerminalDTO {
    private List<AioPermissionDTO> permissionDTOList;
}
