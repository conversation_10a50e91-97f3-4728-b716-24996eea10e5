package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateMerchantReqDTO
 * @date 2019/05/08 10:44
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "估清商品")
public class EstimateMerchantReqDTO extends BasePageDTO {

    /**
     * 分类guid
     */
    @ApiModelProperty(value = "分类guid")
    private  String sortGuid;

    /**
     * 类型guid
     */
    @ApiModelProperty(value = "类型guid")
    private Integer  typGuid;

    /**
     * 是否限量  1：否  2：是
     */
    @ApiModelProperty(value = "是否限量  1：否  2：是")
    private Integer isTheLimit;

    /**
     * 查询关键字
     */
    @ApiModelProperty(value = "查询关键字")
    private  String  keywords;

    /**
     * 门店guid列表
     */
    @ApiModelProperty(value = "门店guid列表 格式为list<String>")
    private List<String> storeGuids;

}
