package com.holderzone.saas.store.dto.marketing.portrayal;

import com.holderzone.saas.store.enums.marketing.portrayal.ApplySettingEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/12/23
 * @description 会员画像详情
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "会员画像详情")
public class MemberPortrayalDetailsVO implements Serializable {

    private static final long serialVersionUID = -1308870162645840954L;

    /**
     * 画像guid
     */
    @ApiModelProperty(value = "画像guid")
    private String portrayalGuid;

    /**
     * 应用设置 1餐饮云一体机端（正餐） 2餐饮云POS端（正餐）
     *
     * @see ApplySettingEnum
     */
    @ApiModelProperty(value = "应用设置")
    private List<Integer> applySetting;

    @ApiModelProperty(value = "基础信息字段")
    private List<MemberPortrayalFieldDetailsVO> basicsFieldList;

    @ApiModelProperty(value = "消费信息字段")
    private List<MemberPortrayalFieldDetailsVO> consumeFieldList;

    @ApiModelProperty(value = "充值信息字段")
    private List<MemberPortrayalFieldDetailsVO> rechargeFieldList;

}
