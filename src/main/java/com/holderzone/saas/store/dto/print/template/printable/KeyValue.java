package com.holderzone.saas.store.dto.print.template.printable;

import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 指定X坐标、Y坐标的文本行
 *
 * <AUTHOR>
 * @date 18-12-15 14:30
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class KeyValue implements Serializable {

    private static final long serialVersionUID = 8871510031009869743L;

    @ApiModelProperty(value = "靠左的文本")
    private Text key;

    @ApiModelProperty(value = "靠右的文本")
    private Text value;

    @ApiModelProperty(value = "是否靠两端排列(即对齐两端Edge)")
    private boolean alignEdges = true;

    public KeyValue(String key, String value) {
        this.key = new Text(key);
        this.value = new Text(value);
    }

    public KeyValue(Text key, Text value) {
        this.key = key;
        this.value = value;
    }

    public KeyValue(String key, String value, boolean alignEdges) {
        this.key = new Text(key);
        this.value = new Text(value);
        this.alignEdges = alignEdges;
    }

    public KeyValue setKeyString(String key) {
        return setKeyString(key, Font.SMALL);
    }

    public KeyValue setKeyString(String key, Font font) {
        this.key = new Text(key, font);
        return this;
    }

    public KeyValue setValueString(String value) {
        return this.setValueString(value, Font.SMALL);
    }

    public KeyValue setValueString(String value, Font font) {
        this.value = new Text(value, font);
        return this;
    }
}