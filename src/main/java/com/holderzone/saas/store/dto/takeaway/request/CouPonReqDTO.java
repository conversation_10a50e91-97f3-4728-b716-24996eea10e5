package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class CouPonReqDTO extends BaseDTO {


    /**
     * 券码
     */
    @NotNull
    @ApiModelProperty(value = "券码,12位纯数字")
    private String couponCode;

    /**
     * 数量
     */
    @NotNull
    @Min(value = 0, message = "小于100的整数")
    @Max(value = 100, message = "小于100的整数")
    @ApiModelProperty(value = "数量")
    private Integer count;

    /**
     * 商家登录ERP账号ID
     */
    @NotBlank
    private String erpId;

    /**
     * 商家登录erp账号名称
     */
    @NotBlank
    private String erpName;

    /**
     * 第三方erp订单号
     */
    @NotBlank
    private String erpOrderId;

    /**
     * 抖音批量验券
     */
    private List<String> couponCodeList;

    /**
     * GroupBuyTypeEnum
     */
    @ApiModelProperty(value = "团购类型")
    @NotNull
    private Integer groupBuyType;

    /**
     * 凭证归属支付宝用户id
     */
    private String userId;

    /**
     * 购买商品的订单id，核销接口使用
     */
    private String orderId;

    /**
     * 平台核销门店id
     */
    private String shopId;

    /**
     * 订单金额
     */
    private BigDecimal orderFee;

    /**
     * 券码渠道 买单:1004
     */
    private Integer receiptChannel;
}
