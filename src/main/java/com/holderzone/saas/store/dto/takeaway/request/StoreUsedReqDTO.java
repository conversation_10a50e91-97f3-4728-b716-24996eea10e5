package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreAuthByTypeReqDTO
 * @date 2018/09/25 9:08
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class StoreUsedReqDTO extends BaseDTO {
}
