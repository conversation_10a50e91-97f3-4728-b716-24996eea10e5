package com.holderzone.saas.store.dto.item.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.item.common.PricePlanStoreBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 价格方案列表查询返回对象
 * @date 2021/5/31 17:56
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "价格方案列表查询返回对象")
@Data
@Accessors(chain = true)
public class PricePlanRespDTO {

    @ApiModelProperty(value = "方案guid")
    private String guid;

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "方案名")
    private String name;

    @ApiModelProperty(value = "方案状态-0未启用 1已启用 2暂不启用 3永久停用 4即将启用")
    private Integer status;

    @ApiModelProperty(value = "方案状态名")
    private String statusName;

    @ApiModelProperty(value = "说明")
    private String description;

    @ApiModelProperty(value = "商品数")
    private Integer itemNum;

    @ApiModelProperty(value = "使用门店数")
    private Integer storeNum;

    @ApiModelProperty(value = "方案编号")
    private String planCode;

    @ApiModelProperty(value = "起始时间")
    private LocalTime startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalTime endTime;

    @ApiModelProperty(value = "生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "即将生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime instantlyEffectiveTime;

    @ApiModelProperty(value = "售卖类型-0 默认（全时段）-1特殊时段")
    private Integer sellTimeType;

    @ApiModelProperty(value = "菜谱生效时间 1立即生效 2按时间")
    private Integer pushType;

    @ApiModelProperty(value = "推送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime pushDate;

    @ApiModelProperty(value = "门店名称列表")
    private List<PricePlanStoreBaseDTO> storeList;

    @ApiModelProperty(value = "最新编辑时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updateTime;
}
