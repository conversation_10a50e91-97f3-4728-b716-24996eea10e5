package com.holderzone.saas.store.dto.print.type;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/23
 * @description 模版分类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "模版分类", description = "模版分类")
public class TemplateTypeDTO implements Serializable {

    private static final long serialVersionUID = -6822851406669486260L;

    @ApiModelProperty(value = "'分类guid'")
    private String guid;

    @ApiModelProperty(value = "分类名称")
    @Length(max = 20, message = "分类名称长度不能超过20")
    @NotBlank(message = "请填写分类名称")
    private String name;

    @ApiModelProperty(value = "商品guid列表")
    private List<TemplateItemQO> itemGuidList;

}
