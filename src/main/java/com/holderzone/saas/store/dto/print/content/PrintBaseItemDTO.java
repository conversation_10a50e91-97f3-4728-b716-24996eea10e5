package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.util.PrintMockUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/09/26 20:07
 * 在单据的基础信息上面，添加菜品记录
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PrintBaseItemDTO extends PrintDTO implements PrintDataMockito {

    @Valid
    @Nullable
    @ApiModelProperty(value = "商品列表，后厨单中商品列表不得为空，其他情况允许为空")
    private List<PrintItemRecord> itemRecordList;

    @Override
    public void applyMock() {
        super.applyMock();
        PrintMockUtils.setMockItemRecords(this);
    }
}
