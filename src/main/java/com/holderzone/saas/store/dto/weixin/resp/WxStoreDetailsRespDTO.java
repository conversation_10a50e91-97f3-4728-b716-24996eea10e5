package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreDetailsRespDTO
 * @date 2019/5/27
 */
@Data
@ApiModel("微信订单详情")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxStoreDetailsRespDTO {

	@ApiModelProperty(value = "0:并桌，1：非并桌 ")
	private Integer combine;

	@ApiModelProperty(value = "0:正餐,1:快餐")
	private Integer tradeMode;

	private Integer pageNum;

	private WxStoreTradeOrderDetailsRespDTO wxStoreTradeOrderDetailsRespDTO;
}
