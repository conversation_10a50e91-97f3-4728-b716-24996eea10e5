package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("商品销量统计返回参数")
public class SalesVolumeStoreStrRespDTO {

    @ApiModelProperty("品牌")
    private String brandName;

    @ApiModelProperty("门店")
    private String storeName;

    @ApiModelProperty("商品名字")
    private String itemName;

    @ApiModelProperty("分类名字")
    private String typeName;

    @ApiModelProperty("商品类型名字")
    private String itemTypeName;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("订单数量")
    private Double orderCount;

    @ApiModelProperty("商品销量")
    private Double salesVolume;

    @ApiModelProperty("退菜量")
    private Double refundCount;

    @ApiModelProperty("赠送量")
    private Double freeCount;

    @ApiModelProperty("销售额")
    private BigDecimal salesAmount;

    @ApiModelProperty("实付金额")
    private String discountPrice;

    @ApiModelProperty(value = "毛利润")
    private BigDecimal grossProfitAmount;

    @ApiModelProperty("销售额占比")
    private String salesProportion;

    @ApiModelProperty("点单率")
    private String spotRate;

    @ApiModelProperty("堂食收入金额")
    private String dineInDiscountPrice;

    @ApiModelProperty("堂食收入占比")
    private String salesDineInProportion;
}
