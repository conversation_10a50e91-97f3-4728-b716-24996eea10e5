package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreListDTO
 * @date 2019/05/17 20:29
 * @description 微信门店列表DTO(同一品牌)
 * @program holder-saas-store
 */
@ApiModel("同一品牌下微信门店功能及信息列表")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WxStoreListDTO {

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty("品牌guid")
    private String brandGuid;

    @ApiModelProperty("品牌名字")
    private String brandName;

    @ApiModelProperty("品牌logo")
    private String brandLogoUrl;

    private List<WxStoreConfigDTO> storeConfigDTOS;

    @ApiModelProperty("门店数量")
    private Integer storeCount;
}
