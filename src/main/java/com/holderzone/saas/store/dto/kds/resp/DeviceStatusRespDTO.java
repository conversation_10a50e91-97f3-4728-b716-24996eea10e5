package com.holderzone.saas.store.dto.kds.resp;

import com.holderzone.saas.store.dto.kds.req.DeviceDstConfDTO;
import com.holderzone.saas.store.dto.kds.req.DevicePrdConfDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class DeviceStatusRespDTO implements Serializable {

    private static final long serialVersionUID = -1158994112223699540L;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @ApiModelProperty(value = "设备名称")
    private String name;

    @ApiModelProperty(value = "堂口模式不得为空")
    private Integer pointMode;

    @ApiModelProperty(value = "显示模式" +
            "制作点位显示模式：0=堂口模式，1=菜品汇总模式，2=订单模式，3=订单1*6,4=混合订单模式1*6" +
            "出堂点位显示模式：0=单菜品模式，1=菜品汇总模式，2=订单模式，3=订单1*6,4=混合订单模式1*6")
    private Integer displayMode;

    @ApiModelProperty(value = "菜品展示类型 0：汇总中展示，订单中不展示；1：汇总和订单中都展示")
    private Integer itemDisplayType;

    @ApiModelProperty(value = "是否已完成初始化")
    private Boolean isInitialized;

    @ApiModelProperty(value = "制作点高级配置")
    private DevicePrdConfDTO devicePrdConfDTO;

    @ApiModelProperty(value = "出堂口高级配置")
    private DeviceDstConfDTO deviceDstConfDTO;

    @ApiModelProperty(value = "是否允许重复绑定菜品")
    private Boolean allowRepeatFlag;

    @ApiModelProperty(value = "销售模式: 1 普通模式 2 菜谱方案")
    private Integer salesModel;
}
