package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BIllDishDiscountDTO
 * @date 2018/08/06 9:12
 * @description 菜品折扣DTO
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class BIllDishDiscountDTO {

    /**
     * 菜品折扣相关类型，4:菜品赠送，6：单品折扣，9：消费核单
     */
    private Integer dishDiscountType;
    /**
     * 折扣金额，
     */
    @ApiModelProperty(value = "折扣金额，对应直接减价或者单品折扣")
    private BigDecimal discountFee;

    /**
     * 对某个菜品操作的账单菜品guid
     */
    private String billDishGuid;

    /**
     * 消费核单修改菜品数量
     */
    private Integer changedCount;
    /**
     * 菜品操作员工guid
     */
    private String dishOperationStaffGuid;
    /**
     * 菜品操作员工姓名
     */
    private String dishOperationStaffName;

    /**
     * 是否赠送
     */
    private Boolean gift;
}
