package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SpinnerReqDTO
 * @date 2019/06/03 16:49
 * @description spinner查询请求入参
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "spinner查询请求入参")
public class SpinnerReqDTO {

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "组织guid")
    private String orgGuid;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;
}
