package com.holderzone.saas.store.dto.takeaway.response;

import com.holderzone.saas.store.dto.takeaway.ErpMappingType;
import com.holderzone.saas.store.dto.takeaway.UnMappedType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@Accessors(chain = true)
public class TakeoutOwnItemMappingRespDTO implements Serializable {

    private static final long serialVersionUID = -721251260893883904L;

    @ApiModelProperty("规格id")
    private long id;

    @ApiModelProperty("规格名称")
    private String name;

    @ApiModelProperty("价格")
    private BigDecimal price;

    @ApiModelProperty("菜品id")
    private long goodsId;

    @ApiModelProperty("菜品单位")
    private String unit;

    @ApiModelProperty("已绑定菜品对应id")
    private String ThirdSkuId;

    @ApiModelProperty("菜品名称")
    private String goodsName;

    @ApiModelProperty("菜品类型id")
    private long goodsTypeId;

    @ApiModelProperty("菜品类型名称")
    private String goodsTypeName;

    @ApiModelProperty("菜品url")
    private String goodsUrl;


}
