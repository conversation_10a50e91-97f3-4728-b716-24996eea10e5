package com.holderzone.saas.store.dto.print.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("用餐类型统计单")
public class PrintTradeStatsDTO extends PrintDTO {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private String storeName;

    @NotNull(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始时间", required = true)
    private Long startTime;

    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间", required = true)
    private Long endTime;

    @NotNull(message = "订单数不能为空")
    @ApiModelProperty(value = "订单数", required = true)
    private Long orderQuantity;

    @ApiModelProperty(value = "订单数", required = true)
    private String orderQuantityStr;

    @NotNull(message = "消费人数不能为空")
    @ApiModelProperty(value = "消费人数", required = true)
    private Long customerNumber;

    @ApiModelProperty(value = "消费人数", required = true)
    private String customerNumberStr;

    @NotNull(message = "销售收入不能为空")
    @ApiModelProperty(value = "销售收入", required = true)
    private BigDecimal salesIncome;

    @ApiModelProperty(value = "销售收入", required = true)
    private String salesIncomeStr;

    @ApiModelProperty(value = "单均消费", required = true)
    private String orderAvgConsumeStr;

    @Nullable
    @ApiModelProperty(value = "正餐统计", required = true)
    private DineSnackTradeStats dineMode;

    @Nullable
    @ApiModelProperty(value = "快餐统计", required = true)
    private DineSnackTradeStats snackMode;

    @ApiModelProperty(value = "类型名称", required = true)
    private String takeoutTypeName;

    @Nullable
    @ApiModelProperty(value = "外卖统计")
    private List<TakeoutTradeStats> takeoutMode;

    @Data
    public static class TradeStats {

        @ApiModelProperty(value = "类型名称", required = true)
        private String typeName;

        @NotNull(message = "订单数不能为空")
        @ApiModelProperty(value = "订单数", required = true)
        private Long orderQuantity;

        @ApiModelProperty(value = "订单数", required = true)
        private String orderQuantityStr;

        @NotNull(message = "销售收入不能为空")
        @ApiModelProperty(value = "销售收入", required = true)
        private BigDecimal salesIncome;

        @ApiModelProperty(value = "销售收入", required = true)
        private String salesIncomeStr;

        @NotNull(message = "单均消费不能为空")
        @ApiModelProperty(value = "单均消费", required = true)
        private BigDecimal orderAvgConsume;

        @ApiModelProperty(value = "单均消费", required = true)
        private String orderAvgConsumeStr;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class DineSnackTradeStats extends TradeStats {



        @NotNull(message = "消费人数不能为空")
        @ApiModelProperty(value = "消费人数", required = true)
        private Long customerNumber;

        @ApiModelProperty(value = "消费人数", required = true)
        private String customerNumberStr;

        @NotNull(message = "人均消费不能为空")
        @ApiModelProperty(value = "人均消费", required = true)
        private BigDecimal customerAvgConsume;

        @ApiModelProperty(value = "人均消费", required = true)
        private String customerAvgConsumeStr;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class TakeoutTradeStats extends TradeStats {

        @NotBlank(message = "外卖平台名称不能为空")
        @ApiModelProperty(value = "外卖平台名称", required = true)
        private String platformName;
    }
}
