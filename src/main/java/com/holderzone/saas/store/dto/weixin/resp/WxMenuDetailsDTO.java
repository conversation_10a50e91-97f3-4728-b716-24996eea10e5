package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxMenuDetailsDTO
 * @date 2019/2/22 17:49
 * @description 微信点餐菜品显示DTO
 * @package com.holderzone.saas.store.dto.weixin.resp
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("门店菜单及商品信息")
public class WxMenuDetailsDTO {
    /**
     * 商品集合
     */
    @ApiModelProperty(value = "微信商品集合")
    private List<WxStoreItemRespDTO> itemList;

    /**
     * 分类集合
     */
    @ApiModelProperty(value = "微信门店商品分类与标签集合")
    private List<WxTypeAndTagDTO> typeList;
    /**
     * 配置详情
     */
    @ApiModelProperty("微信门店后台配置详情 ")
    private WxStoreConfigRespDTO wxStoreConfigRespDTO;

	@ApiModelProperty("是否跳转到订单详情 1:跳转")
    private Integer isJump=0;

	@ApiModelProperty("0显示")
	private Integer first=1;

	@ApiModelProperty("订单id")
	private String orderGuid;
}
