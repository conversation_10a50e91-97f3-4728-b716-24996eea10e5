package com.holderzone.saas.store.dto.order.request.dinein;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量保存快餐订单
 * <AUTHOR>
 */
@Data
@ApiModel
public class BatchCreateFastFoodReqDTO implements Serializable {

    private static final long serialVersionUID = -6767696846789986773L;
    @ApiModelProperty("订单列表")
    private List<FastFoodReqDTO> foodReqDTOList;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class FastFoodReqDTO extends CreateFastFoodReqDTO {

        @ApiModelProperty("创建操作人guid")
        private String staffGuid;

        @ApiModelProperty("创建操作人name")
        private String staffName;

        @ApiModelProperty("订单状态：1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废 7: 反结账")
        private Integer state;

        @ApiModelProperty("订单编号：前端传递,做唯一标识 orderNo = storeNo + yyyymmdd + 4位AtomicLong")
        private String orderNo;

        @ApiModelProperty("订单创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime orderTime;

        @ApiModelProperty("营业日")
        private LocalDate businessDay;

        //================================ 结账时计算账单金额和获取订单详情 参数 =================================

        @ApiModelProperty(value = "1：会员登陆，2：会员登出，-1：不操作会员")
        private Integer memberLogin;

        @ApiModelProperty(value = "是否计算积分1：计算，2：不计算")
        private Integer memberIntegral;

        @ApiModelProperty(value = "会员电话")
        private String memberPhone;

        @ApiModelProperty(value = "整单折扣")
        private BigDecimal wholeDiscount;

        @ApiModelProperty(value = "整单让价")
        private BigDecimal concessional;

        @ApiModelProperty(value = "优惠券code")
        private String volumeCode;

        @ApiModelProperty(value = "优惠券code list")
        private List<String> volumeCodes;

        /**
         * {@link com.holderzone.holder.saas.member.enums.volume.VolumeTypeEnum}
         */
        @ApiModelProperty(value = "优惠券type")
        private Integer volumeType;

        @ApiModelProperty(value = "1：验券，2：撤销，3：查询（不验券，只给微信用）")
        private Integer verify;

        @ApiModelProperty(value = "营销活动guid，如果为字符串0则为取消，如果传null  表示不变")
        private String activityGuid;

        @ApiModelProperty(value = "是否需要详情")
        private Boolean isNeedActivityList;

        //================================ 支付订单金额 参数 =================================

        @ApiModelProperty(value = "消费时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime consumptionTime;

        @ApiModelProperty(value = "订单金额（商品总额+附加费）")
        private BigDecimal orderFee;

        @ApiModelProperty(value = "附加费")
        private BigDecimal appendFee;

        @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
        private BigDecimal actuallyPayFee;

        @ApiModelProperty(value = "优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
        private BigDecimal discountFee;

        @ApiModelProperty(value = "找零")
        private BigDecimal changeFee;

        @ApiModelProperty(value = "当前的优惠信息")
        private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

        @ApiModelProperty(value = "支付信息")
        @NotEmpty(message = "支付信息不能为空")
        private List<BillPayReqDTO.Payment> payments;

        @ApiModelProperty(value = "是否快餐")
        private boolean fastFood;

        @ApiModelProperty(value = "会员密码")
        private String memberPassWord;

        @ApiModelProperty(value = "会员卡guid")
        private String memberInfoCardGuid;

        @ApiModelProperty(value = "会员guid")
        private String memberInfoGuid;

        @ApiModelProperty("员消费记录GUID，如果已经上传过一次订单信息，再次计算其它卡优惠时，只需要传此GUID(菜品没有做变化的情况下)")
        private String memberConsumptionGuid;

        @ApiModelProperty(value = "使用的积分")
        private Integer useIntegral;

        @ApiModelProperty(value = "积分抵扣了多少钱")
        private BigDecimal integralDiscountMoney;

        @ApiModelProperty(value = "version")
        @LockField
        private Integer version;

        @ApiModelProperty(value = "单位名称")
        private String unitName;

        @ApiModelProperty(value = "单位Guid")
        private String unitGuid;

        @ApiModelProperty(value = "单位编码")
        private String unitCode;

        @ApiModelProperty("是否是食堂消费")
        private Boolean canteenPay = false;

        @ApiModelProperty(value = "食堂卡余额：离线订单每一单都要记录，用于对比")
        private BigDecimal cardMoney;

        @ApiModelProperty(value = "食堂卡赠送金额:离线订单每一单都要记录，用于对比")
        private BigDecimal cardGiftMoney;
    }

}
