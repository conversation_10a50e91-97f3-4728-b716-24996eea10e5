package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("必点分类校验")
public class CheckMustItemRespDTO {

    @ApiModelProperty("是否必点")
    private Boolean isCheckMust;

    @ApiModelProperty("分类guid")
    private String typeGuid;
}
