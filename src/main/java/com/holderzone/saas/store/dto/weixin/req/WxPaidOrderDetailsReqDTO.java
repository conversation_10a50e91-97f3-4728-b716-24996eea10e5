package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("微信结账后订单详情")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxPaidOrderDetailsReqDTO {

	private String enterpriseGuid;

	private String storeGuid;

	private String openId;

	private String orderGuid;
}
