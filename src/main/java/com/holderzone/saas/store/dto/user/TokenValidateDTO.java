package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Deprecated
@NoArgsConstructor
@ApiModel("Token验证实体")
public class TokenValidateDTO implements Serializable {

    private static final long serialVersionUID = 5949717225665424449L;

    @ApiModelProperty("Token字符串")
    private String token;

    @ApiModelProperty("登录来源")
    private String source;
}
