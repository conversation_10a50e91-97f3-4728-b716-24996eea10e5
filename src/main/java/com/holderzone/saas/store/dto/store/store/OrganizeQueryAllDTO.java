package com.holderzone.saas.store.dto.store.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreQueryPageDTO
 * @date 2018/07/23 下午5:23
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class OrganizeQueryAllDTO implements Serializable {

    private static final long serialVersionUID = 8346999943405837471L;

    /**
     * 组织guid
     */
    @ApiModelProperty(value = "组织guid")
    private String organizationGuid;

    /**
     * 组织名称或编码
     */
    @ApiModelProperty(value = "组织名称/组织编码")
    private String nameOrId;

    /**
     * 创建门店的用户guid
     */
    @ApiModelProperty(value = "创建门店的用户guid")
    private String createUserGuid;
}
