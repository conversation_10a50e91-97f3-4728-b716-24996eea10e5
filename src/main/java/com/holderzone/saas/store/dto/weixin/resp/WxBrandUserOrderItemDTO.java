package com.holderzone.saas.store.dto.weixin.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("微信品牌我的订单项")
@AllArgsConstructor
@NoArgsConstructor
public class WxBrandUserOrderItemDTO {

	@ApiModelProperty(value="订单guid")
	private String tradeOrderGuid;

	@ApiModelProperty(value = "品牌图片")
	private String logUrl;

	private WxStoreConsumerDTO wxStoreConsumerDTO;

	@ApiModelProperty(value = "交易模式：交易模式(0：正餐，1：快餐)", required = true)
	private Integer tradeMode;

	@ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
	private BigDecimal actuallyPayFee;

	@ApiModelProperty(value = "状态(0:待确认，3:已取消，5:待支付，6：已完成 )")
	//前端4种状态 0待确认(待接单,快餐不存在)，1已接单，2已完成，3已取消
	private Integer state;

	@ApiModelProperty(value = "结算时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime checkoutTime;

	@ApiModelProperty(value="商品名称")
	private List<String> itemNames;

}
