package com.holderzone.saas.store.dto.takeaway;

import com.holderzone.saas.store.dto.takeaway.MtRespItem;
import com.holderzone.saas.store.dto.takeaway.MtResponseDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
public class MtRespItemDTO {

    /**
     * 美团商品列表
     */
    private List<MtRespItem> data;

    /**
     * 美团响应失败结果
     */
    private MtResponseDTO.ErrorDetail error;
}
