package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Api("会员卡或优惠券")
@Data
@Builder
public class CardAndVolumeDiscountReqDTO {

	@ApiModelProperty(value = "企业id")
	private String enterpriseGuid;

	@ApiModelProperty(value = "品牌id")
	private String brandGuid;

	@ApiModelProperty(value = "门店guid")
	private String storeGuid;

	@ApiModelProperty(value = "桌台id")
	private String tableGuid;

	@ApiModelProperty(value = "用户id")
	private String openId;

	@ApiModelProperty(value = "优惠券code")
	private String volumeCode;

	@ApiModelProperty(value = "会员卡guid")
	private String memberInfoCardGuid;

	@ApiModelProperty(value = "会员卡积分")
	private Integer memberIntegral;

	@ApiModelProperty(value = "优惠券名称")
	private String volumeName;

	@ApiModelProperty(value = "0:会员卡，1：优惠券")
	private Integer discountType;
}
