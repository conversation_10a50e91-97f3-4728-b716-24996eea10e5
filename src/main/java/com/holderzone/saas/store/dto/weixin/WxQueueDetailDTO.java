package com.holderzone.saas.store.dto.weixin;

import com.holderzone.saas.store.dto.queue.HolderQueueQueueRecordDTO;
import com.holderzone.saas.store.dto.queue.StoreConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueDetailDTO
 * @date 2019/05/16 10:04
 * @description 微信上展示的排队详情DTO
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("微信上展示的排队详情DTO")
@Builder
public class WxQueueDetailDTO extends HolderQueueQueueRecordDTO {

    @ApiModelProperty("品牌guid")
    private String brandGuid;

    @ApiModelProperty("品牌名字")
    private String brandName;

    @ApiModelProperty("品牌LogoUrl")
    private String brandLogo;

    @ApiModelProperty("排队配置")
    private StoreConfigDTO storeConfigDTO;

	@ApiModelProperty(value = "-1表示错误，0：正常")
	private Integer code=0;



}
