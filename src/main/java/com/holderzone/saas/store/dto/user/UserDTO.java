package com.holderzone.saas.store.dto.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.resource.common.dto.validate.Add;
import com.holderzone.saas.store.dto.organization.OrgGeneralDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("用户请求响应实体")
public class UserDTO implements Serializable {

    private static final long serialVersionUID = -5609254499610900868L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "用户GUID（更新、查询、启用(禁用)、删除时不能为空）")
    @NotBlank(message = "用户GUID不能为空", groups = {Update.class, Query.class,
            Enable.class, Disable.class, EnableOrDisable.class, Delete.class})
    private String guid;

    /**
     * todo 删除@JsonProperty("mchntNo")
     */
    @JsonProperty("mchntNo")
    @ApiModelProperty(value = "商户号）")
//    @NotBlank(message = "商户号不能为空", groups = Create.class)
//    @Pattern(regexp = "^\\d{8}$", groups = Create.class, message = "商户号必须是8位数字")
    private String enterpriseNo;

    @ApiModelProperty(value = "员工账号（创建时不能为空）")
    @NotBlank(message = "员工账号不能为空", groups = Create.class)
    @Pattern(regexp = "^\\d{3,6}$", groups = Create.class, message = "员工账号必须是3-6位数字")
    private String account;

    @ApiModelProperty(value = "员工密码（创建时不能为空")
    @NotBlank(message = "员工密码不能为空", groups = Create.class)
    @Size(min = 6, max = 20, groups = Create.class, message = "员工密码必须介于6到20位之间")
    private String password;

    @ApiModelProperty(value = "授权码")
    private String authCode;

    @ApiModelProperty(value = "姓名（创建时不能为空）")
    @NotBlank(message = "姓名不能为空", groups = Create.class)
    private String name;

    @ApiModelProperty(value = "手机号（创建时不能为空）")
    @NotBlank(message = "手机号不能为空", groups = Create.class)
    private String phone;

    @ApiModelProperty(value = "员工身份证号码")
    private String idCardNo;

    @ApiModelProperty(value = "员工身份证地址")
    private String idCardAddress;

    @ApiModelProperty(value = "员工居住地址")
    private String address;

    @ApiModelProperty(value = "员工生日")
    private LocalDateTime birthday;

    @ApiModelProperty(value = "员工入职时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime onBoardingTime;

    @NotEmpty(message = "用户角色列表不能为空", groups = {Create.class, Update.class})
    @ApiModelProperty(value = "用户角色列表")
    private List<RoleDTO> userRoles;

    @ApiModelProperty(value = "组织机构id")
    private String orgGuid;

    @ApiModelProperty(value = "请求值、响应值：用户所属组织")
    private UserOrgDTO userOrg;

    @ApiModelProperty(value = "响应值：企业的整个组织树")
    private List<OrgGeneralDTO> entireOrgTree;

    @ApiModelProperty(value = "请求值、响应值：用户职位")
    private UserOfficeDTO userOffice;

    @ApiModelProperty(value = "请求值、响应值：用户所管理门店规则")
    private UserDataStoreRuleDTO userDataStoreRule;

    @ApiModelProperty(value = "请求值、响应值：用户所管理门店条件匹配规则")
    private List<UserDataCondRuleDTO> userDataCondRuleList;

    @ApiModelProperty(value = "请求值、响应值：整单折扣阈值，范围0-10，最多两位小数")
    private BigDecimal discountThreshold;

    @ApiModelProperty(value = "请求值、响应值：整单让价阈值，最多两位小数")
    private BigDecimal allowanceThreshold;

    @ApiModelProperty(value = "请求值、响应值：单品折扣阈值，范围0-10， 最多两位小数")
    private BigDecimal productDiscountThreshold;

    @ApiModelProperty(value = "请求值、响应值：退款金额差值阈值，范围0-10， 最多两位小数")
    private BigDecimal refundThreshold;

    @ApiModelProperty(value = "请求值、响应值：用户可分配的角色id，逗号分割")
    private List<UserRoleDistDTO> userRolesDistributable;

    @JsonIgnore
    @ApiModelProperty(value = "创建人GUID")
    private String createStaffGuid;

    @JsonIgnore
    @ApiModelProperty(value = "更新人GUID")
    private String updateStaffGuid;

    @ApiModelProperty(value = "是否启用：0=禁用，1=启用（启用、禁用时不能为空）")
    @NotNull(message = "是否启用不能为空", groups = {Create.class, EnableOrDisable.class})
    private Boolean isEnable;

    @ApiModelProperty(value = "是否删除：0=未删除，1=已删除")
    private Boolean isDeleted;

    @ApiModelProperty(value = "是否为操作员：0=否，1=是")
    private Boolean isWaiter;

    @ApiModelProperty(value = "记录创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "记录修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "单门店企业创建用户时传入")
    private String storeGuid;

    /**
     * @see com.holderzone.resource.common.enums.RegTypeEnum
     */
    @ApiModelProperty(value = "注册来源，跟随企业注册来源")
    private String regType;

    @ApiModelProperty(value = "手机号列表")
    private List<String> phoneList;

    @ApiModelProperty(value = "企业Guid")
    private String enterpriseGuid;

    /**
     * 是否一体化
     */
    @ApiModelProperty(value = "是否一体化")
    private Boolean integrateFlag;

    @ApiModelProperty(value = "错误返回信息")
    private String errorMsg;

    /**
     * 人脸识别码
     */
    @ApiModelProperty(value = "人脸识别码")
    private String faceCode;

    /**
     * 微海同步员工id
     */
    private String weihaiId;

    /**
     * 接收系统短信通知：0=不接收，1=接收
     */
    @ApiModelProperty(value = "接收系统短信通知：0=不接收，1=接收")
    private Boolean isReceive;

    public interface Create {

    }

    public interface Update {

    }

    public interface Query {

    }

    public interface Enable {

    }

    public interface Disable {

    }

    public interface EnableOrDisable {

    }

    public interface Delete {

    }
}
