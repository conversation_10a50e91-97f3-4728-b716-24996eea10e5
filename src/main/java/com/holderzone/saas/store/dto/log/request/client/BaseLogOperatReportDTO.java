package com.holderzone.saas.store.dto.log.request.client;


import com.holderzone.saas.store.dto.log.annotation.Check;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * @Athor forewei
 * @Email <EMAIL>
 * @Date 2019/12/4
 */
public class BaseLogOperatReportDTO {

    @ApiModelProperty("平台  （收银系统|990  支付平台|80  MDM平台|40  ERP平台|50 小程序平台|60  会员平台|30）")
    @NotNull
    private String platform;

    @ApiModelProperty("终端类型  99003|一体机  99004|pos系列  99005|pad 99006|点菜宝  99009|kds 99014|TV 99011|自助点餐机")
    @NotNull
    private String module;

    @ApiModelProperty("请求地址")
    private String requestUri;

    @ApiModelProperty("入参")
    private String params;

    @ApiModelProperty("反参")
    private String returnParams;

    @ApiModelProperty("请求ip")
    private String remoteAddr;

    @ApiModelProperty("操作方式   -1|删除  0|查询  1|增加  2|修改")
    @Check(paramValues = {"0", "1", "2", "-1"})
    private String method;


    @ApiModelProperty("发起操作日志记录的时间")
    private String operationTime;

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getRequestUri() {
        return requestUri;
    }

    public void setRequestUri(String requestUri) {
        this.requestUri = requestUri;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getReturnParams() {
        return returnParams;
    }

    public void setReturnParams(String returnParams) {
        this.returnParams = returnParams;
    }

    public String getRemoteAddr() {
        return remoteAddr;
    }

    public void setRemoteAddr(String remoteAddr) {
        this.remoteAddr = remoteAddr;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(String operationTime) {
        this.operationTime = operationTime;
    }
}
