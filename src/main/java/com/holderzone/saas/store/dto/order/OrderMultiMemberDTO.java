package com.holderzone.saas.store.dto.order;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.anno.OrderLockField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class OrderMultiMemberDTO extends BaseDTO {

    private static final long serialVersionUID = -8951585514851962893L;

    @ApiModelProperty(value = "订单guid")
    @OrderLockField
    private String orderGuid;

    @ApiModelProperty(value = "会员名称")
    private String memberName;

    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    @ApiModelProperty(value = "会员卡guid")
    private String memberInfoCardGuid;

    @ApiModelProperty(value = "会员卡号")
    private String memberCardNum;

    @ApiModelProperty(value = "会员电话")
    private String memberPhone;

    @ApiModelProperty(value = "登录方式 0-扫码 1-手机号录入")
    private Integer loginType;

    /**
     * @see com.holderzone.saas.store.enums.member.MemberLoginEnum
     */
    @ApiModelProperty(value = "1：会员登陆，2：会员登出")
    private Integer memberLogin;

    /**
     * 支付参数
     */
    @ApiModelProperty(value = "会员密码")
    private String memberPassWord;

    /**
     * 支付参数
     */
    @ApiModelProperty(value = "是否需要密码")
    private Boolean needPassword = Boolean.TRUE;

    /**
     * 支付参数
     */
    @ApiModelProperty(value = "卡支付金额")
    private BigDecimal amount;

    /**
     * 反参
     */
    @ApiModelProperty(value = "卡余额")
    private BigDecimal cardMoney;

    /**
     * 反参
     */
    @ApiModelProperty("冻结金额")
    private BigDecimal freezeMoney;

    /**
     * 反参
     */
    @ApiModelProperty(value = " 积分")
    private Integer cardIntegral;


}
