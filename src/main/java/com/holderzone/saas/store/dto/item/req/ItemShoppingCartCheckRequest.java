package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


@Data
@NoArgsConstructor
public class ItemShoppingCartCheckRequest {


    @ApiModelProperty(value = "门店guid", required = true)
    @NotBlank(message = "门店Guid不能为空")
    private String storeGuid;

    @ApiModelProperty(value = "是否为外卖", required = true)
    @NotNull(message = "是否为外卖不能为空")
    private Boolean isTakeaway;

    @ApiModelProperty(value = "购物车列表")
    List<SingleShoppingCartCheck> data;

}
