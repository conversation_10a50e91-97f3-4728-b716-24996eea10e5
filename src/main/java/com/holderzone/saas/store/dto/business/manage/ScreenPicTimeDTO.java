package com.holderzone.saas.store.dto.business.manage;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPicTimeDTO
 * @date 2018/11/27 9:13
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class ScreenPicTimeDTO extends BaseDTO {

    @ApiModelProperty(value = "业务主键")
    private String picTimeGuid;

    @ApiModelProperty(value = "切换时间")
    private Integer changeMills;

    @ApiModelProperty(value = "图片类型")
    private Integer picType;

    @ApiModelProperty(value = "选着的guid")
    private String selectStoreGuid;

}
