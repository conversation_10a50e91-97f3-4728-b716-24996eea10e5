package com.holderzone.saas.store.dto.marketing.specials;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/5/28
 * @description
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class EffectiveTimeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<String> type;

    private List<String> value;
}
