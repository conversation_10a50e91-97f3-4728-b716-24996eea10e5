package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LocalUserReqDTO
 * @date 2019/10/10 11:39
 * @description 员工信息本地化请求DTO
 * @program holder-saas-store
 */

@Data
@ApiModel("员工信息本地化请求DTO")
public class LocalUserReqDTO {

    @NotEmpty(message = "门店guid不能为空")
    @ApiModelProperty("门店Guid")
    String storeGuid;

    @NotEmpty(message = "终端Code不能为空")
    @ApiModelProperty("终端Code")
    String terminalCode;
}
