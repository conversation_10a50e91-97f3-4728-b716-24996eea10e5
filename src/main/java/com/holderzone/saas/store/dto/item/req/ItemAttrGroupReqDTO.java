package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DishPropertyGroupReqDTO
 * @date 2019/01/08 上午11:28
 * @description //新增商品时商品属性组的实体
 * @program holder-saas-store-dto
 */
@Data
public class ItemAttrGroupReqDTO implements Serializable {

    @ApiModelProperty(value = "商品Guid")
    private String itemGuid;

    @ApiModelProperty(value = "商品与属性组关联实体的唯一标识")
    private String itemAttrGroupGuid;

    @ApiModelProperty(value = "属性组GUID", required = true)
    @NotNull
    private String attrGroupGuid;

    @ApiModelProperty(value = "是否必选:0 否 1 是", required = true)
    @NotNull
    private Integer isRequired;

    @ApiModelProperty(value = "是否多选:0 否 1 是", required = true)
    @NotNull
    private Integer isMultiChoice;

    @ApiModelProperty(value = "是否有默认选项:0 否 1 是")
    private Integer withDefault;

    @ApiModelProperty(value = "商品属性值集合", required = true)
    @NotNull
    @Size(max = 50, message = "每个属性组最多可配置50个属性，请调整")
    private List<@Valid ItemAttrReqDTO> attrList;
}
