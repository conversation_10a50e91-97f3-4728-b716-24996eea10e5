package com.holderzone.saas.store.dto.file;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FileDeleteDTO
 * @date 2018/10/22 下午2:27
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class FileDeleteDTO extends BaseDTO {
    @ApiModelProperty(value = "被删除文件在OSS上的存储路径")
    private String fileUrl;
    private Integer type;
}
