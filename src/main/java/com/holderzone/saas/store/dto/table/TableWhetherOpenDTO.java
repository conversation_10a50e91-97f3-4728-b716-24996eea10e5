package com.holderzone.saas.store.dto.table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableWhetherOpenDTO
 * @date 2019/03/05 15:46
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class TableWhetherOpenDTO extends BaseTableDTO {

    @ApiModelProperty("新桌台code")
    private String newTableCode;

    @ApiModelProperty("新桌台区域名")
    private String newTableAreaName;

    @ApiModelProperty("是否开台成功")
    private boolean whetherOpened;


}
