package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class PointSkuBindRespDTO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    @ApiModelProperty(value = "设备Guid")
    private String deviceId;

    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "堂口Guid")
    private String pointGuid;

    @ApiModelProperty(value = "堂口名称")
    private String pointName;

    @ApiModelProperty(value = "规格Id")
    private String skuGuid;

    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty(value = "规格编码")
    private String skuCode;

    @ApiModelProperty(value = "超时时间：分钟")
    private Integer timeout;

    @ApiModelProperty(value = "最大制作份数")
    private Integer maxCopies;

    @ApiModelProperty(value = "制作显示模式")
    private Integer displayType;

    @ApiModelProperty(value = "是否被当前设备绑定")
    private Boolean isBoundBySelf;

    @ApiModelProperty(value = "是否被其他设备绑定")
    private Boolean isBoundByOthers;
}
