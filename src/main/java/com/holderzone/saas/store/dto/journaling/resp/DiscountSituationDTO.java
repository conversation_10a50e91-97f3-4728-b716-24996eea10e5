package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DiscountSituationDTO
 * @date 2019/05/30 11:20
 * @description 优惠统计DTO
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("优惠统计")
public class DiscountSituationDTO {

    @ApiModelProperty(value = "系统省零")
    private BigDecimal savingZero;

    @ApiModelProperty(value = "会员折扣")
    private BigDecimal memberDiscount;

    @ApiModelProperty(value = "会员优惠券")
    private BigDecimal memberCoupons;

    @ApiModelProperty(value = "菜品赠送")
    private BigDecimal itemPresent;

    @ApiModelProperty(value = "整单让价")
    private BigDecimal fullSinglePrice;

    @ApiModelProperty(value = "团购券")
    private BigDecimal groupCoupons;

    @ApiModelProperty(value = "整单折扣")
    private BigDecimal wholeDiscount;

    @ApiModelProperty(value = "积分抵扣")
    private BigDecimal points;

    @ApiModelProperty(value = "会员价")
    private BigDecimal memberPrice;

    @ApiModelProperty(value = "单品折扣")
    private BigDecimal singleDiscount;

    @ApiModelProperty(value = "商品券")
    private BigDecimal goodsGrouper;

    @ApiModelProperty(value = "营销活动")
    private BigDecimal campaign;

    public static DiscountSituationDTO DEFAULT() {
        return DiscountSituationDTO.builder()
                .savingZero(BigDecimal.ZERO)
                .memberDiscount(BigDecimal.ZERO)
                .memberCoupons(BigDecimal.ZERO)
                .itemPresent(BigDecimal.ZERO)
                .fullSinglePrice(BigDecimal.ZERO)
                .groupCoupons(BigDecimal.ZERO)
                .wholeDiscount(BigDecimal.ZERO)
                .points(BigDecimal.ZERO)
                .memberPrice(BigDecimal.ZERO)
                .singleDiscount(BigDecimal.ZERO)
                .goodsGrouper(BigDecimal.ZERO)
                .campaign(BigDecimal.ZERO)
                .build();
    }
}
