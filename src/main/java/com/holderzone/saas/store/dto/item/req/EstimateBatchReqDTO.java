package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateReqDTO
 * @date 2019/05/07 14:44
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class EstimateBatchReqDTO implements Serializable {

    /**
     * 商品sku guid
     */
    @NotNull(message = "商品sku不能为空")
    @Size(min = 1,message = "商品sku不能为空")
    @ApiModelProperty(value = "商品sku不能为空")
    private List<String> skuGuidList;

    /**
     * 是否禁售 1:否 2:是
     */
    @ApiModelProperty(value = "是否禁售 1:否 2:是")
    private Integer isSoldOut;

    /**
     * 当前时间节点是否限量  1：否  2：是
     */
    @ApiModelProperty(value = "当前时间节点是否限量  1：否  2：是")
    private Integer isTheLimit;

    /**
     * 下个时间节点是否限量  1：否  2：是
     */
    @ApiModelProperty(value = "下个时间节点是否限量  1：否  2：是")
    private Integer isTheLimitReset;


    /**
     * 限量数量
     */
    @ApiModelProperty(value = "限量数量")
    private BigDecimal limitQuantity;

    /**
     * 当前剩余数量
     */
    @ApiModelProperty(value = "当前剩余数量")
    private BigDecimal residueQuantity;

    /**
     * 提醒阈值
     */
    @ApiModelProperty(value = "提醒阈值")
    private BigDecimal reminderThreshold;

    /**
     * 是否次日置满  1：否  2：是
     */
    @ApiModelProperty(value = "是否次日置满  1：否  2：是")
    private Integer isItReset ;

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;


}
