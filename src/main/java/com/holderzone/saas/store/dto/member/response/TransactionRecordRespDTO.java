package com.holderzone.saas.store.dto.member.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TransactionRecordRespDTO
 * @date 2018/09/18 16:26
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class TransactionRecordRespDTO {

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "交易时间")
    private LocalDateTime transactionTime;

    @ApiModelProperty(value = "交易金额")
    private BigDecimal fee;

    @ApiModelProperty(value = "交易类型 0：充值，1:支付")
    private Byte type;

    @ApiModelProperty(value = "余额")
    private BigDecimal balance;
    @ApiModelProperty(value = "交易流水号")
    private String sequenceNo;

}
