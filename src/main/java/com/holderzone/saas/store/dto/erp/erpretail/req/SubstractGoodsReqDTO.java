package com.holderzone.saas.store.dto.erp.erpretail.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("售卖出库请求实体")
public class SubstractGoodsReqDTO {

    @ApiModelProperty("商品Guid")
    private String goodsGuid;

    @ApiModelProperty("出入库数量")
    private BigDecimal count;

    @ApiModelProperty("商品单价")
    private BigDecimal unitPrice;

}
