package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class TakeoutDeliveryChange {

    @ApiModelProperty(value = "一城飞客接口标识")
    private String Action;
    /************ 飞客action:SendOrderState *************/

    @ApiModelProperty(value = "订单编号（第三方商家自己的订单编号）")
    public String OrderId;

    /**
     * 1为已处理
     * 10 商家已配餐
     * 22为已分配骑手，正在取餐路上
     * 23为已取餐，配送途中
     * 100为同意退款
     * 101为拒绝退款
     * 254为已完成
     * 255为已关闭
     */
    @ApiModelProperty(value = "订单状态")
    public int OrderState;

    @ApiModelProperty(value = "原因")
    public String Reason;

    @ApiModelProperty(value = "配送距离（单位：米）")
    public int Distance;

    /******** 以下为骑手信息 飞客action:SendWLUserInfo、SendWLUserLocation *******/

    @ApiModelProperty(value = "骑手姓名")
    public String UserName;

    @ApiModelProperty(value = "骑手性别（0为女，1为男）")
    public String UserSex;

    @ApiModelProperty(value = "骑手手机号码")
    public String UserPhone;

    @ApiModelProperty(value = "骑手是否认证了身份证（0为没有认证，1为已认证）")
    public String IsIDCard;

    @ApiModelProperty(value = "骑手照片链接（完整的Url链接）")
    public String UserPhoto;

    @ApiModelProperty(value = "纬度")
    public String Lat;

    @ApiModelProperty(value = "经度")
    public String Lng;

    @ApiModelProperty(value = "骑手位置URL")
    public String PositionUrl;
}
