package com.holderzone.saas.store.dto.business.manage;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverRecordCreateDTO
 * @date 2018/07/29 上午11:39
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class HandoverRecordConfirmDTO extends BaseDTO {
    private static final long serialVersionUID = 9181918823285037338L;

    /**
     * 设备固件编号
     */
    @ApiModelProperty("设备固件编号")
    private String terminalId;

    @ApiModelProperty("实上缴金额")
    private BigDecimal realHandOnCash;

    @ApiModelProperty(value = "用户guid列表")
    private List<String> userGuidList;

    @ApiModelProperty(value = "是否多人交接班 0 否 1 是")
    private Integer isMultiHandover;
}
