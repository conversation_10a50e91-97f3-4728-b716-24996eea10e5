package com.holderzone.saas.store.dto.retail.dinein;

import com.holderzone.saas.store.dto.table.BaseTableDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CancelFreeItemReqDTO
 * @date 2019/01/31 11:40
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class CancelFreeItemReqDTO extends BaseTableDTO {

    @ApiModelProperty(value = "赠送商品guid")
    private List<String> freeItemGuids;

}
