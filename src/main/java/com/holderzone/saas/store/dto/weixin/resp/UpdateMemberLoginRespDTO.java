package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("修改会员登录状态返回")
@AllArgsConstructor
@NoArgsConstructor
public class UpdateMemberLoginRespDTO {

	@ApiModelProperty("登录状态：true登录，false登出")
	private Boolean loginState;
}
