package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class UnItem implements Serializable {

    private static final long serialVersionUID = 4916810505687055138L;

    /**
     * 菜品SKU
     */
    private String itemSku;

    /**
     * 菜品单价
     */
    private String itemCode;

    /**
     * 菜品名称
     */
    private String itemName;

    /**
     * 单位
     */
    private String itemUnit;

    /**
     * 单价
     */
    private BigDecimal itemPrice;

    /**
     * 菜品数量
     */
    private BigDecimal itemCount;

    /**
     * 平台商品实际数量
     */
    private BigDecimal actualItemCount;

    /**
     * 菜品小计
     */
    private BigDecimal itemTotal;

    /**
     * 菜品规格 多元素使用"",""分割开
     */
    private String itemSpec;

    /**
     * 特殊属性 多元素使用"",""分割开
     */
    private String itemProperty;

    /**
     * 餐盒单价
     */
    private BigDecimal boxPrice;

    /**
     * 餐盒数量
     */
    private BigDecimal boxCount;

    /**
     * 餐盒小计
     */
    private BigDecimal boxTotal;

    /**
     * 所属口袋 0=1号口袋  1=2号口袋
     */
    private Integer cartId;

    /**
     * 真实价格
     */
    private BigDecimal actualPrice;

    /**
     * 折扣掉的金额单价
     */
    private BigDecimal discountPrice;

    /**
     * 菜品折扣比例 1=无折扣，0.85=8.5折扣
     */
    private BigDecimal discountRatio;

    /**
     * 折扣掉的金额合计
     */
    private BigDecimal discountTotal;

    /**
     * 结算类型 0=普通消费菜品  1=赠送菜品
     */
    private Integer settleType;

    /**
     * 平台方商品规格ID
     */
    private String unItemSkuId;

    /**
     * 三方唯一标识
     */
    private String thirdSkuId;

    /**
     * 父级菜品单价
     */
    private String parentItemCode;

    /**
     * 父级菜品SKU
     */
    private String parentItemSku;

    /**
     * 套餐子项详情
     */
    private List<UnItem> subItemList;

}
