package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LocalizeTableReqDTO
 * @date 2019/11/13 15:06
 * @description //TODO
 * @program IdeaProjects
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LocalizeTableOrderReqDTO extends BaseDTO {

    List<LocalizeTableDTO> localTableOrderList;

}