package com.holderzone.saas.store.dto.organization;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className OrgGeneralDTO
 * @date 19-1-25 上午11:02
 * @description 通用存储企业、组织、门店DTO
 * @program holder-saas-store-staff
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@ApiModel("通用存储企业、组织、门店DTO")
public class OrgGeneralDTO {

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "类型，0-企业，1-组织，2-门店")
    private Integer type;

    @Deprecated
    @ApiModelProperty(value = "是否可勾选， false-否，true-是")
    private Boolean isCheckable;

    @ApiModelProperty(value = "下级组织")
    private List<OrgGeneralDTO> children;
}
