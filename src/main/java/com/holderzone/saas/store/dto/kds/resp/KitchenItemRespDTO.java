package com.holderzone.saas.store.dto.kds.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/10/27
 * @description
 */
@Data
public class KitchenItemRespDTO implements Serializable {

    private static final long serialVersionUID = 2929117990370273581L;

    /**
     * 是否出堂
     */
    private Boolean isOutDinner;

    /**
     * 订单商品guid
     */
    private String orderItemGuid;
}
