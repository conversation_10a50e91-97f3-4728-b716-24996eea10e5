package com.holderzone.saas.store.dto.print.content.nested;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 退款商品信息
 *
 * <AUTHOR>
 * @date 2025/07/01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "退款商品信息")
public class RefundItem implements Serializable {

    private static final long serialVersionUID = -1234567890123456789L;

    @NotBlank(message = "商品名称不能为空")
    @ApiModelProperty(value = "商品名称", required = true)
    private String itemName;

    @NotNull(message = "单价不能为空")
    @ApiModelProperty(value = "单价", required = true)
    private BigDecimal unitPrice;

    @NotNull(message = "退款数量不能为空")
    @ApiModelProperty(value = "退款数量", required = true)
    private Integer quantity;

    @NotNull(message = "小计不能为空")
    @ApiModelProperty(value = "小计", required = true)
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "商品属性")
    private String property;

    public RefundItem(String itemName, BigDecimal unitPrice, Integer quantity, BigDecimal totalPrice) {
        this.itemName = itemName;
        this.unitPrice = unitPrice;
        this.quantity = quantity;
        this.totalPrice = totalPrice;
    }
}
