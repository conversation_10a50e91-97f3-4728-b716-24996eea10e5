package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemSortRespDTO
 * @date 2019/10/23 上午11:18
 * @description //商超门店商品排序返回实体
 * @program holder-saas-store-dto
 */

@Data
@ApiModel(value = "商超门店商品排序返回实体" )
@NoArgsConstructor
@AllArgsConstructor
public class ItemSortRespDTO {

    @ApiModelProperty(value = "商品id")
    private  Integer id;
    @ApiModelProperty(value = "商品guid")
    private  String  guid;
    @ApiModelProperty(value = "商品名称")
    private  String  name;
    @ApiModelProperty(value = "商品排序权重值 越小越靠前")
    private  Integer sort;
}
