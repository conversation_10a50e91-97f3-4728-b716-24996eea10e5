package com.holderzone.saas.store.dto.item.req.price;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 批量编辑新增商品传递得数据
 * @date 2021/9/28
 */
@Data
@ApiModel(value = "批量编辑新增商品传递得数据")
public class PlanPriceAddReqDTO {

    @ApiModelProperty(value = "推送类型：1-立即推送，2-按时间推送")
    private Integer pushType;

    @ApiModelProperty(value = "推送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pushDate;

    @ApiModelProperty(value = "商品信息")
    private List<ItemWebRespDTO> itemWebRespDTOList;

    @ApiModelProperty(value = "菜谱信息")
    private List<PlanPriceAddBaseDTO> planPriceAddBaseList;

}
