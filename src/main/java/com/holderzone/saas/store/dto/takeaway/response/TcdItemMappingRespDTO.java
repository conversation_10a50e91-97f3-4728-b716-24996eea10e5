package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@ApiModel("查询赚餐商品返回")
@Accessors(chain = true)
public class TcdItemMappingRespDTO {

    @ApiModelProperty(value = "组Id")
    private Long id;

    @ApiModelProperty(value = "组名称")
    private String name;

    @ApiModelProperty(value = "菜品列表")
    private List<TcdDish> dishes;

}
