package com.holderzone.saas.store.dto.print.template.printable;

import com.holderzone.saas.store.dto.print.template.convertable.Text;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class Line implements Serializable {

    private static final long serialVersionUID = 6379505798729152863L;

    @ApiModelProperty(value = "线高(像素)")
    private int height = 1;

    @ApiModelProperty(value = "是否为虚线")
    private boolean dotted = false;

    @ApiModelProperty(value = "是否以文本符号替换图片，默认true，即以文本方式打印")
    private boolean useSymbol = true;

    @ApiModelProperty(value = "文本符号")
    private Text symbol = Text.DOT;
}
