package com.holderzone.saas.store.dto.marketing.specials;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.enums.marketing.ApplyBusinessTypeEnum;
import com.holderzone.saas.store.enums.marketing.DataUnitEnum;
import com.holderzone.saas.store.enums.marketing.RechargeGiftTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/4
 * @description 限时特价活动详情返回
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "限时特价活动详情返回", description = "限时特价活动详情返回")
public class LimitSpecialsActivityDetailsVO implements Serializable {

    private static final long serialVersionUID = 8703620733617395226L;

    /**
     * 活动guid
     */
    @ApiModelProperty(value = "活动guid")
    private String guid;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String name;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    /**
     * 是否限制活动时段 0-否 1-是
     */
    @ApiModelProperty(value = "是否限制活动时段 0-否 1-是")
    private Integer isLimitPeriod;

    /**
     * 限制时段类型 -1:自定义 0：日 1：周 2：月 3：年
     *
     * @see DataUnitEnum
     */
    @ApiModelProperty(value = "限制时段类型 -1:自定义 0：日 1：周 2：月 3：年")
    private Integer equitiesTimeLimitedType;

    /**
     * 限制时段限制类型json
     *
     * @see EffectiveTimeVO
     */
    @ApiModelProperty(value = "限制时段限制类型json")
    private String equitiesTimeLimitedJson;

    /**
     * 活动标签
     * 为参与活动客户打标签
     * labelGuidJson
     */
    @ApiModelProperty(value = "活动标签")
    private List<String> labelGuidList;

    /**
     * 活动规则
     * 共享互斥关系 0-互斥 1-共享
     */
    @ApiModelProperty(value = "活动规则 共享互斥关系 0-互斥 1-共享")
    private Integer relationRule;

    /**
     * 适用场景 0:全部业务 1：部分业务
     *
     * @see ApplyBusinessTypeEnum
     */
    @ApiModelProperty(value = "适用场景 0:全部业务 1：部分业务")
    private Integer applyBusiness;

    /**
     * 适用场景
     * applyBusinessJson
     */
    @ApiModelProperty(value = "适用场景")
    private List<String> applyBusinessList;

    /**
     * 是否适用于所有门店 1:全部门店；0:部分门店(外关联表)
     */
    @ApiModelProperty(value = "是否适用于所有门店 1:全部门店；0:部分门店(外关联表)")
    private Integer isAllStore;

    /**
     * 适用门店
     */
    @ApiModelProperty(value = "适用门店")
    private List<LimitSpecialsActivityStoreDTO> storeDTOList;

    /**
     * 适用人群类型 0不限制 1所有注册会员 2指定人群
     *
     * @see RechargeGiftTypeEnum
     */
    @ApiModelProperty(value = "适用人群类型 0不限制 1所有注册会员 2指定人群")
    private Integer groupType;

    /**
     * 指定标签
     */
    @ApiModelProperty(value = "指定标签")
    private ConsumptionGiftDetailsDTO labelDTO;

    /**
     * 指定等级
     */
    @ApiModelProperty(value = "指定等级")
    private ConsumptionGiftDetailsDTO gradeDTO;

    /**
     * 指定会员
     */
    @ApiModelProperty(value = "指定会员")
    private ConsumptionGiftDetailsDTO memberDTO;

    @ApiModelProperty(value = "活动商品")
    private List<LimitSpecialsActivityItemDTO> itemDTOList;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    @ApiModelProperty("活动优惠金额")
    private BigDecimal discountPrice;

    @ApiModelProperty("活动金额")
    private BigDecimal specialsPrice;

}
