package com.holderzone.saas.store.dto.takeaway;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY,
        getterVisibility = JsonAutoDetect.Visibility.NONE)
public class MtRespItem implements Serializable {

    @ApiModelProperty("美团方菜品分类名")
    private String categoryName;

    @ApiModelProperty("美团方菜品id")
    private String dishId;

    @ApiModelProperty("美团方菜品名")
    private String dishName;

    @ApiModelProperty("ERP方菜品id")
    private String eDishCode;

    @ApiModelProperty("sku基础信息")
    private List<MtRespItemSku> waiMaiDishSkuBases;
}
