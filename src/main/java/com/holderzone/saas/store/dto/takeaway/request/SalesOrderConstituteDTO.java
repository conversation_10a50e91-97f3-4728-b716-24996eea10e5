package com.holderzone.saas.store.dto.takeaway.request;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class SalesOrderConstituteDTO {

    @NotNull(message = "折扣类型外键Id不得为空")
    @ApiModelProperty(value = "折扣类型外键Id")
    public Integer Id;

    @NotNull(message = "折扣类型(0=活动折扣;1=整单让价;2=优惠券折扣;3=餐盒费;4=配送费;5=新人立减)不得为空")
    @ApiModelProperty(value = "折扣类型(0=活动折扣;1=整单让价;2=优惠券折扣;3=餐盒费;4=配送费;5=新人立减)")
    public Integer DiscountType;

    @NotNull(message = "折扣类型（1折扣优惠;2附加费)不得为空")
    @ApiModelProperty(value = "折扣类型（1折扣优惠;2附加费)")
    public Integer DataType;

    @NotNull(message = "折扣金额(折扣;附加费)不得为空")
    @ApiModelProperty(value = "折扣金额(折扣;附加费)")
    public BigDecimal DiscountAmount;

    @NotBlank(message = "折扣名称不得为空")
    @ApiModelProperty(value = "折扣名称")
    public String DiscountName;

}
