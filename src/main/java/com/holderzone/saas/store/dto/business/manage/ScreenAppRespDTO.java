package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenAppRespDTO
 * @date 2018/11/16 13:52
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScreenAppRespDTO implements Serializable {

    @ApiModelProperty(value = "oss图片下载路径")
    private String ossUrl;

    @ApiModelProperty(value = "切换时间")
    private Integer changeMils;

    @ApiModelProperty(value = "guid")
    private String screenPictureGuid;

    @ApiModelProperty(value = "高")
    private Integer height;

    @ApiModelProperty(value = "宽")
    private Integer weight;

    @ApiModelProperty(value = "是否是第一张")
    private boolean asFirst;

    @ApiModelProperty(value = "像素类型 1920*1080-->1; 800*800-->2;1024*600-->3;635*600-->4 ")
    private Integer pxType;

    @ApiModelProperty(value = "图片类型 1-->副屏图片，2-->副屏点餐图片")
    private Integer picType;

}
