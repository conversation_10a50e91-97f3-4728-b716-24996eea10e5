package com.holderzone.saas.store.dto.report;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2018/09/28 上午 10:34
 * @description
 */
public class OrderDishDetailDTO {

    /**
     * 菜品编号
     */
    private String dishCode;

    /**
     * 菜品名称
     */
    private String dishName;

    /**
     * 菜品类型
     */
    private String dishType;

    /**
     * 是否套餐
     */
    private Integer packageDish;

    /**
     * 售卖价
     */
    private BigDecimal salePrice;

    /**
     * 菜品总量
     */
    private BigDecimal dishCount;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 销售类型
     */
    private Integer saleType;

    /**
     * 结算员工
     */
    private String checkoutStaffName;

    /**
     * 结账时间
     */
    private Long checkoutTimestamp;

    /**
     * 退菜员工
     */
    private String returnStaffName;

    /**
     * 退菜时间
     */
    private Long returnTimestamp;

    public String getDishCode() {
        return dishCode;
    }

    public void setDishCode(String dishCode) {
        this.dishCode = dishCode;
    }

    public String getDishName() {
        return dishName;
    }

    public void setDishName(String dishName) {
        this.dishName = dishName;
    }

    public String getDishType() {
        return dishType;
    }

    public void setDishType(String dishType) {
        this.dishType = dishType;
    }

    public Integer getPackageDish() {
        return packageDish;
    }

    public void setPackageDish(Integer packageDish) {
        this.packageDish = packageDish;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public BigDecimal getDishCount() {
        return dishCount;
    }

    public void setDishCount(BigDecimal dishCount) {
        this.dishCount = dishCount;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getCheckoutTimestamp() {
        return checkoutTimestamp;
    }

    public void setCheckoutTimestamp(Long checkoutTimestamp) {
        this.checkoutTimestamp = checkoutTimestamp;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getSaleType() {
        return saleType;
    }

    public void setSaleType(Integer saleType) {
        this.saleType = saleType;
    }

    public String getCheckoutStaffName() {
        return checkoutStaffName;
    }

    public void setCheckoutStaffName(String checkoutStaffName) {
        this.checkoutStaffName = checkoutStaffName;
    }

    public String getReturnStaffName() {
        return returnStaffName;
    }

    public void setReturnStaffName(String returnStaffName) {
        this.returnStaffName = returnStaffName;
    }

    public Long getReturnTimestamp() {
        return returnTimestamp;
    }

    public void setReturnTimestamp(Long returnTimestamp) {
        this.returnTimestamp = returnTimestamp;
    }
}
