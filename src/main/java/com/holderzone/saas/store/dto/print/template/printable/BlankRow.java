package com.holderzone.saas.store.dto.print.template.printable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 前进多行，输出指定数量的空白行
 *
 * <AUTHOR>
 * @date 2018/12/15 15:01
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class BlankRow implements Serializable {

    private static final long serialVersionUID = -8870024165979648952L;

    @ApiModelProperty(value = "前进行数")
    private int lineNumber = 1;
}
