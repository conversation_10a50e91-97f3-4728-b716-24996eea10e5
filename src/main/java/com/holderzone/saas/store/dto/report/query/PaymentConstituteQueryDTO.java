package com.holderzone.saas.store.dto.report.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/23
 * @description 收款构成查询
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "收款构成查询", description = "收款构成查询")
public class PaymentConstituteQueryDTO extends PageDTO {

    private static final long serialVersionUID = 9152097566936267719L;

    /**
     * 企业guid
     */
    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @NotNull
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate startTime;

    @NotNull
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate endTime;

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "展示类型 1按汇总展示 2按单店展示 3按单日单店展示")
    private Integer showType;

    @ApiModelProperty("门店集合，品牌查询需传品牌下门店集合")
    private List<String> storeGuidList;
}
