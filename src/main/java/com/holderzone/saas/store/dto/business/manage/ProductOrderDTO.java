package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ProductOrderDTO
 * @date 2018/09/17 13:51
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class ProductOrderDTO implements Serializable {

    @ApiModelProperty(value = "企业guid")
    @NotBlank(message = "企业guid不能为空")
    private String enterpriseGuid;

    @ApiModelProperty(value = "产品guid")
    @NotBlank(message = "产品guid不能为空")
    private String productGuid;

    @ApiModelProperty(value = "数量")
    @Min(value = 1, message = "产品数量最小为1")
    private Integer orderCount;

    @ApiModelProperty(value = "产品类型（规格）")
    @NotBlank(message = "产品规格guid不能为空")
    private String chargeGuid;

    @ApiModelProperty(value = "终端类型")
    private String terminal;

    @ApiModelProperty(value = "付款方式 微信:51 支付宝:1")
    @NotNull(message = "付款方式不能为空")
    private Integer payType;

    @ApiModelProperty(value = "操作人账号")
    @NotBlank(message = "操作人账号不能为空")
    private String staffAccount;

    @ApiModelProperty(value = "操作人guid")
    @NotBlank(message = "操作人guid不能为空")
    private String staffGuid;

    @ApiModelProperty(value = "操作人name")
    @NotBlank(message = "操作人name不能为空")
    private String staffName;

}

