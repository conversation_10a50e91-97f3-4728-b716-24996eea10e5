package com.holderzone.saas.store.dto.pay;

import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/11/6
 * @description 快速收款统计返回
 */
@Data
@ApiModel(value = "快速收款统计返回")
@EqualsAndHashCode(callSuper = false)
public class QuickPayStatisticsRespDTO implements Serializable {

    private static final long serialVersionUID = 7043909304857427934L;

    /**
     * @see PayPowerId
     * 支付功能的id：
     * -1: 会员余额支付
     * 31：支付宝二维码支付(客户扫我们代理商返回给的二维码)
     * 2 ：支付宝条码支付(扫码枪)
     * 3 ：支付宝公众号支付
     * 51：微信二维码支付
     * 9 ：微信条码支付
     * 10：微信公众号支付
     */
    @ApiModelProperty(value = "支付功能id")
    private String payPowerId;

    /**
     * 门店Guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "金额总计")
    private BigDecimal amountTotal;

    @ApiModelProperty(value = "订单数")
    private Integer orderCount;

    /**
     * 客人数
     * pos快速收银一笔订单算1人
     */
    @ApiModelProperty(value = "客人数")
    private Integer guestCount;

    @ApiModelProperty(value = "支付日期")
    private LocalDate payDate;

}
