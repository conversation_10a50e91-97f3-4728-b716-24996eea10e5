package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 商户后台/数据报表/订单统计 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
@Data
public class BusinessTakeoutOrderItemRespDTO {

    @ApiModelProperty(value = "订单唯一标识")
    private String guid;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "商品单位")
    private String itemUnit;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal itemCount;

    @ApiModelProperty(value = "商品单价")
    private BigDecimal itemPrice;

}
