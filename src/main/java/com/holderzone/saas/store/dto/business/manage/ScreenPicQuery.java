package com.holderzone.saas.store.dto.business.manage;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPicQuery
 * @date 2018/11/19 16:10
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class ScreenPicQuery extends BaseDTO {

    @ApiModelProperty(value = "图片类型")
    private Integer picType;

    @ApiModelProperty(value = "像素类型")
    private Integer pxType;

    @ApiModelProperty(value = "门店guid")
    private String selectStoreGuid;

}
