package com.holderzone.saas.store.dto.pay;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SaasAggPayDTO
 * @date 2019/03/14 10:31
 * @description 调用交易中心服务dto
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class SaasAggPayDTO extends BaseDTO {

    @ApiModelProperty(value = "请求聚合支付实体")
    private AggPayPreTradingReqDTO reqDTO;

    @ApiModelProperty(value = "是否为快速收款")
    private Boolean isQuickReceipt = false;

    private Boolean isLast;

    @ApiModelProperty(value = "是否最后结账")
    private Boolean checkoutSuccessFlag;

    @ApiModelProperty(value = "交易中心服务回调地址，" +
            "比如:http://holder-saas-cloud-enterprise/enterprise/find " +
            "这个回调地址是saas拿到支付轮询结果的回调")
    private String saasCallBackUrl;

    @ApiModelProperty(value = "支付成功跳转地址，用于主扫，支付成功后银行跳转")
    private String callBackUrl;
}
