package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 交接单
 *
 * <AUTHOR>
 * @date 2018/09/19 17:29
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("交接单")
public class PrintHandOverNewDTO extends PrintDTO {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private String storeName;

    @NotBlank(message = "交接员工姓名不能为空")
    @ApiModelProperty(value = "交接员工", required = true)
    private String staffName;

    @ApiModelProperty(value = "是否多人交接班")
    private Integer isMultiHandover;

    @ApiModelProperty(value = "交接负责人姓名")
    private String handoverName;

    @NotBlank(message = "时长不能为空")
    @ApiModelProperty(value = "时长", required = true)
    private String duration;

    @NotBlank(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始时间", required = true)
    private String beginTime;

    @NotBlank(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间", required = true)
    private String overTime;

    @ApiModelProperty(value = "应上缴现金", required = true)
    private BigDecimal handOnCash;

    @ApiModelProperty(value = "实上缴现金", required = true)
    private BigDecimal realHandOnCash;

    @NotNull(message = "销售（已结）订单数不能为空")
    @ApiModelProperty(value = "销售（已结）订单数", required = true)
    private Integer saleCount;

    @NotNull(message = "销售收入不能为空")
    @ApiModelProperty(value = "销售收入", required = true)
    private BigDecimal saleIncome;

    @Valid
    @ApiModelProperty(value = "销售支付列表", required = true)
    private List<PayRecord> salePayRecordList;

    @NotNull(message = "储值订单数不能为空")
    @ApiModelProperty(value = "储值订单数", required = true)
    private Integer rechargeCount;

    @NotNull(message = "充值收入不能为空")
    @ApiModelProperty(value = "充值收入", required = true)
    private BigDecimal rechargeIncome;

    @ApiModelProperty(value = "优惠方式列表", required = true)
    private List<PayRecord> discountRecordList;

    @Valid
    @ApiModelProperty(value = "储值支付列表", required = true)
    private List<PayRecord> rechargePayRecordList;

    @NotNull(message = "预订订单数不能为空")
    @ApiModelProperty(value = "预订订单数", required = true)
    private Integer reserveCount;

    @NotNull(message = "预订金收入不能为空")
    @ApiModelProperty(value = "预订金收入", required = true)
    private BigDecimal reserveIncome;

    @Valid
    @ApiModelProperty(value = "预订支付列表", required = true)
    private List<PayRecord> reservePayRecordList;

    @ApiModelProperty(value = "还款金额（还挂账金额汇总）")
    private BigDecimal repaymentFeeTotal;

    @ApiModelProperty(value = "还款单数")
    private Integer repaymentFeeCount;

    @Valid
    @ApiModelProperty(value = "还款支付列表")
    private List<PayRecord> repaymentList;

    @ApiModelProperty(value = "客流量")
    private Integer traffic;

    @ApiModelProperty(value = "上座率")
    private String occupancyRatePercent;

    @ApiModelProperty(value = "开台率")
    private String openTableRatePercent;

    @ApiModelProperty(value = "翻台率")
    private String flipTableRatePercent;

    @ApiModelProperty(value = "平均消费时间，单位分钟")
    private Integer avgDineInTime;

    @ApiModelProperty(value = "销售（已结总金额）")
    private BigDecimal saleAmount;

    @ApiModelProperty(value = "优惠总额")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "退款总额")
    private BigDecimal refundAmount;
}
