package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.nested.PayRecordStats;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("收款统计单")
public class PrintReceiptStatsDTO extends PrintDTO {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private String storeName;

    @NotNull(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始时间", required = true)
    private Long startTime;

    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间", required = true)
    private Long endTime;

    @NotNull(message = "收款合计不能为空")
    @ApiModelProperty(value = "收款合计", required = true)
    private BigDecimal receiptTotal;

    @Nullable
    @ApiModelProperty(value = "收款列表", notes = "收款列表可以为空（不显示）")
    private List<PayRecord> receiptDetail;

    @Nullable
    @ApiModelProperty(value = "收款方式", notes = "收款方式列表可以为空（不显示）")
    private List<PayRecordStats> receiptStatsDetail;
}
