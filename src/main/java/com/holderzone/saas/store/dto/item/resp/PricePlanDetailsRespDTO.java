package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> R
 * @date 2021/3/11 15:21
 * @description
 */
@ApiModel(value = "价格方案编辑详情dto")
@Data
public class PricePlanDetailsRespDTO {
    @ApiModelProperty(value = "基础信息")
    private PricePlanRespDTO pricePlanRespDTO;

    @ApiModelProperty(value = "分类信息")
    private List<TypeWebRespDTO> typeWebRespDTO;

    @ApiModelProperty(value = "门店信息")
    private List<PricePlanStoreRespDTO> storeRespDTOList;
}
