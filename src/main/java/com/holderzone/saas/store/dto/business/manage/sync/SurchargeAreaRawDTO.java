package com.holderzone.saas.store.dto.business.manage.sync;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "附加费区域")
public class SurchargeAreaRawDTO implements Serializable {

    @ApiModelProperty(value = "自增id")
    private Long id;

    @ApiModelProperty(value = "唯一Guid")
    private String guid;

    @ApiModelProperty(value = "附加费Guid")
    private String surchargeGuid;

    @ApiModelProperty(value = "区域Guid")
    private String areaGuid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;
}
