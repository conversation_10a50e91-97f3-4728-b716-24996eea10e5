package com.holderzone.saas.store.dto.erp.erpretail.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("商品流水返回实体")
public class GoodsSerialRespDTO {

    @ApiModelProperty("业务名称")
    private String invoiceName;

    @ApiModelProperty("业务类型")
    private Integer invoiceType;

    @ApiModelProperty("变化量")
    private BigDecimal changeNum;

    @ApiModelProperty("单位")
    private String unitName;

    @ApiModelProperty("业务时间")
    private String invoiceTime;

    @ApiModelProperty("关联单号")
    private String invoiceNo;

    @ApiModelProperty("安全库存")
    private BigDecimal safeNum;

    @ApiModelProperty("单据是否被作废")
    private boolean isInvalid;

}
