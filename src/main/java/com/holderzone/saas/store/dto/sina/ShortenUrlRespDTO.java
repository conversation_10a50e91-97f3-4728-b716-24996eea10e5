package com.holderzone.saas.store.dto.sina;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ShortenUrlRespDTO
 * @date 2019/04/04 14:29
 * @description 新浪短链接转换DTO
 * @program holder-saas-store
 */
@Data
@ApiModel("新浪短链接转换DTO")
@AllArgsConstructor
@NoArgsConstructor
public class ShortenUrlRespDTO {

    @ApiModelProperty("短链接")
    @JsonProperty("url_short")
    private String urlShort;

    @ApiModelProperty("原链接")
    @JsonProperty("url_long")
    private String urlLong;

    @ApiModelProperty("type")
    private String type;
}
