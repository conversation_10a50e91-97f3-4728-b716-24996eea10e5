package com.holderzone.saas.store.dto.report.openapi;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.open.BaseReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 查询会员资金变动明细入参
 */
@Data
public class MemberFundingDetailQueryDTO extends BaseReq {

    @NotBlank(message = "企业不能为空")
    private String enterpriseGuid;

    @NotBlank(message = "运营主体不能为空")
    private String operSubjectGuid;

    @ApiModelProperty(value = "变动时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "用于分页查询的游标，由上一次调用返回，首次调用可不填")
    private Long cursor;

    @ApiModelProperty(value = "返回的最大记录数，整型，最大值1000，默认值500，超过最大值时取最大值")
    private Integer limit;

    @ApiModelProperty(value = "请求来源")
    private String requestSource;
}
