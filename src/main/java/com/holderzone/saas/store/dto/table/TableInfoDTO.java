package com.holderzone.saas.store.dto.table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableInfoDTO
 * @date 2019/01/16 14:46
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel("桌台信息dto")
@Accessors(chain = true)
@Data
public class TableInfoDTO extends BaseTableDTO {

    @ApiModelProperty(value = "桌台名称")
    private String tableName;

    /**
     * 区域guid
     */
    @ApiModelProperty("区域guid")
    private String areaGuid;

    /**
     * 区域名称
     */
    @ApiModelProperty("区域名称")
    private String areaName;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;

    /**
     * 是否绑定
     */
    @ApiModelProperty("是否绑定")
    private Boolean isBind;
}
