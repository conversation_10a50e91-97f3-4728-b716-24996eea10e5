package com.holderzone.saas.store.dto.kds.req;


import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 查询实体
 * @date 2021/1/28 11:30
 */
@ApiModel(description = "查询实体")
@Data
@EqualsAndHashCode(callSuper = true)
public class DisplayRuleQueryDTO extends PageDTO {

    /**
     * 规则guid
     */
    @ApiModelProperty(value = "规则guid")
    private String ruleGuid;

    /**
     * 规则类型 0显示批次 1菜品汇总
     */
    @ApiModelProperty(value = "规则类型 0显示批次 1菜品汇总")
    private Integer ruleType;

    /**
     * 品牌guid
     */
    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;
}
