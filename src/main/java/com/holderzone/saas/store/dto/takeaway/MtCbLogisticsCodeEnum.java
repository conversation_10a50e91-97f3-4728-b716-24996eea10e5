package com.holderzone.saas.store.dto.takeaway;

/**
 * 配送方式
 * <p>
 * https://developer.meituan.com/openapi#7.6
 */
public enum MtCbLogisticsCodeEnum {

    商家自配送("0000", "商家自配送"),

    美团专送加盟("1001", "美团专送-加盟"),

    美团专送自建("1002", "美团专送-自建"),

    美团配送众包("1003", "美团配送-众包"),

    美团专送城市代理("1004", "美团专送-城市代理"),

    快送("2002", "快送"),

    全城送("2010", "全城送"),

    混合送即美团专送美团快送("3001", "混合送，即美团专送+美团快送，使用方式相同"),

    未知("9999999", "未知配送方式");

    private String code;

    private String desc;

    MtCbLogisticsCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static MtCbLogisticsCodeEnum ofCode(String code) {
        for (MtCbLogisticsCodeEnum value : values()) {
            if (value.code.equalsIgnoreCase(code)) {
                return value;
            }
        }
        // throw new IllegalArgumentException("无效的logisticsCode[" + code + "]");
        return 未知;
    }
}
