package com.holderzone.saas.store.dto.member.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 订单结算回调红包参数
 * @date 2022/3/17 10:46
 * @className: RedPacketOrderSettleCallBackQO
 */
@Data
@ApiModel("订单结算回调红包参数")
@Accessors(chain = true)
public class RedPacketOrderSettleCallBackQO implements Serializable {

    private static final long serialVersionUID = 5775323997185428311L;

    @ApiModelProperty("订单guid")
    private String orderNum;

    /**
     * 订单号(前端显示用，门店内唯一，格式************)
     */
    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("订单应付金额")
    private BigDecimal orderAmount;

    @ApiModelProperty("订单实付金额")
    private BigDecimal orderPaidAmount;

    @ApiModelProperty("红包金额")
    private BigDecimal redPacketAmount;

    @ApiModelProperty("父级订单号")
    private String parentOrderNumber;

    @ApiModelProperty("父级订单编号")
    private String parentOrderNo;

    /**
     * 活动guid
     */
    @ApiModelProperty("活动guid")
    private String activityGuid;

    /**
     * 红包实际使用金额
     */
    @ApiModelProperty("红包实际使用金额")
    private BigDecimal redPacketActualAmount;

}
