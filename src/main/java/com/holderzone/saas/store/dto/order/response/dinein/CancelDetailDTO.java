package com.holderzone.saas.store.dto.order.response.dinein;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CancelDetailDTO
 * @date 2019/01/17 14:33
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
public class CancelDetailDTO {

    @ApiModelProperty(value = "作废原因")
    private String cancelReason;

    @ApiModelProperty(value = "作废操作人guid")
    private String cancelStaffGuid;

    @ApiModelProperty(value = "作废操作人名称")
    private String cancelStaffName;

    @ApiModelProperty(value = "作废设备类型")
    private Integer cancelDeviceType;

    @ApiModelProperty(value = "作废设备名称")
    private String cancelDeviceName;

    @ApiModelProperty(value = "作废时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime cancelTime;

    @ApiModelProperty(value = "作废订单退款明细")
    private List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS;
}
