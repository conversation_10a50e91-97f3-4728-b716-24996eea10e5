package com.holderzone.saas.store.dto.journaling.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RecoveryOrderReqDTO
 * @date 2019/06/03 11:36
 * @description 反结账订单列表请求入参
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "反结账订单列表请求入参")
public class RecoveryOrderReqDTO extends JournalAppBaseReqDTO {

    @ApiModelProperty(value = "上次查询最后一条订单的guid")
    private String lastGuid;

    @ApiModelProperty(value = "上次查询最后一条订单的创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime lastGmtCreate;

    @ApiModelProperty(value = "请求条数")
    private Integer reqCount;
}
