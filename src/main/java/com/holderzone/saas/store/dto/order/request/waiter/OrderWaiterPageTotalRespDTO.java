package com.holderzone.saas.store.dto.order.request.waiter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> R
 * @date 2020/12/2 14:55
 * @description
 */
@Data
@ApiModel(value = "订单服务员汇总分页查询返回参数DTO")
public class OrderWaiterPageTotalRespDTO {

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @ApiModelProperty(value = "服务员编号")
    private String waiterNo;

    @ApiModelProperty(value = "服务员名称")
    private String waiterName;

    @ApiModelProperty(value = "服务员类型")
    private Integer waiterType;

    @ApiModelProperty(value = "少于30")
    private Integer orLessCount;

    @ApiModelProperty(value = "30-100之间")
    private Integer betweenCount;

    @ApiModelProperty(value = "100以上")
    private Integer theAboveCount;
}
