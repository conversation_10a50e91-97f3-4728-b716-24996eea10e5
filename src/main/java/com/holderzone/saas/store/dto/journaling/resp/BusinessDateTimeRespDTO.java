package com.holderzone.saas.store.dto.journaling.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("报表-营业日期时间")
public class BusinessDateTimeRespDTO {

    @ApiModelProperty(value = "开始营业日期时间集合，顺序为：今天，近7天，近30天")
    private List<LocalDateTime> businessStartDateTimes;

    @ApiModelProperty(value = "结束营业日期时间，都是当日期时间")
    private LocalDateTime businessEndDateTime;
}
