package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 打印公共基础类
 *
 * <AUTHOR>
 * @date 2018/09/18 19:32
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(description = "打印请求基础类")
public class KdsPrintDTO implements Serializable {

    private static final long serialVersionUID = -5442220773853902934L;

    @NotNull(message = "打印票据类型")
    @ApiModelProperty(value = "打印票据类型", required = true)
    private Integer invoiceType;

    @NotBlank(message = "企业GUID不得为空")
    @ApiModelProperty(value = "企业GUID", required = true)
    private String enterpriseGuid;

    @NotBlank(message = "门店GUID不能为空")
    @ApiModelProperty(value = "门店GUID", required = true)
    private String storeGuid;

    @NotBlank(message = "printUid不能为空")
    @ApiModelProperty(value = "打印UID", required = true)
    private String printUid;

    @NotBlank(message = "操作人GUID不能为空")
    @ApiModelProperty(value = "操作人GUID", required = true)
    private String operatorStaffGuid;

    @NotBlank(message = "操作人姓名不能为空")
    @ApiModelProperty(value = "操作人姓名", required = true)
    private String operatorStaffName;

    @NotBlank(message = "操作人帐号不能为空")
    @ApiModelProperty(value = "操作人帐号", required = true)
    private String operatorStaffAccount;

    @NotNull(message = "打印时间不能为空")
    @ApiModelProperty(value = "打印时间", required = true)
    private LocalDateTime createTime;

    @NotBlank(message = "设备编号不能为空")
    @ApiModelProperty(value = "设备编号", required = true)
    private String deviceId;
}
