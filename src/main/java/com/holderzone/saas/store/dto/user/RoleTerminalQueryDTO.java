package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RoleTerminalQueryDTO
 * @date 19-2-15 下午1:58
 * @description
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("角色终端查询DTO")
public class RoleTerminalQueryDTO {
    @ApiModelProperty("角色guid")
    private String roleGuid;

    @ApiModelProperty("终端guid")
    private String terminalGuid;
}
