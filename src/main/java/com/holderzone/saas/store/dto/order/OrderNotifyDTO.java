package com.holderzone.saas.store.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderNotifyDTO
 * @date 2018/09/01 17:22
 * @description
 * @program holder-saas-store-dto
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderNotifyDTO implements Serializable {

    private String orderGuid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tradingTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate businessDay;

    @ApiModelProperty(value = "交易金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "10：待支付  0：富民支付中  1：支付中(已请求上游） 2：支付成功 3：支付失败 4：退款  5：已关闭  6 撤销 7：反结账状态 -1 删除")
    private Integer state;

    private String operationStaffGuid;

    private String operationStaffName;

    @ApiModelProperty(value = "交易流水号")
    private String sequenceNo;

    @ApiModelProperty(value = "支付方式")
    private Integer prepaidType;

    @ApiModelProperty(value = "支付方式name")
    private String prepaidName;

    @ApiModelProperty(value = "0:代表充值 1:代表消费 2:代表退款")
    private Integer type;

    private String enterpriseGuid;

    @ApiModelProperty(value = "会员名字")
    private String name;

    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    @ApiModelProperty(value = "会员电话")
    private String phone;

}
