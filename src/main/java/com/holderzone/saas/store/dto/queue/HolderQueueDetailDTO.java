package com.holderzone.saas.store.dto.queue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HolderQueueDetailDTO
 * @date 2019/03/28 18:23
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel("队列信息")
public class HolderQueueDetailDTO extends HolderQueueDTO{
    @ApiModelProperty("元素集合")
    private List<HolderQueueItemDetailDTO> items;
}