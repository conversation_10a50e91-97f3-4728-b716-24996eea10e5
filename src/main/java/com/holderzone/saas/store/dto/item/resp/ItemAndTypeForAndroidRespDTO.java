package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemAndTypeForAndroidRespDTO
 * @date 2019/01/03 下午2:00
 * @description //安卓端同步商品接口返回实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class ItemAndTypeForAndroidRespDTO implements Serializable {
    private static final long serialVersionUID = -9184965694661322344L;
    /**
     * 商品集合
     */
    @ApiModelProperty(value = "可能为空集合，商品集合")
    private List<ItemSynRespDTO> itemList;

    /**
     * 分类集合
     */
    @ApiModelProperty(value = "可能为空集合，分类集合")
    private List<TypeSynRespDTO> typeList;

    /**
     * 售卖模式
     */
    @ApiModelProperty(value = "1普通模式,2菜谱模式")
    private Integer salesModel;

    /**
     * 菜谱售卖模式中，存在价格方案GUID
     */
    @ApiModelProperty(value = "价格方案GUID")
    private String pricePlanGuid;
}
