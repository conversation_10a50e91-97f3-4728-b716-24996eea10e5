package com.holderzone.saas.store.dto.order.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PackageSubgroupDTO
 * @date 2019/01/04 10:32
 * @description 套餐分组对象
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
@Accessors(chain = true)
@NoArgsConstructor
public class PackageSubgroupDTO implements Serializable {

    private static final long serialVersionUID = -8860934935403009377L;

    @ApiModelProperty(value = "分组guid", required = true)
    private String subgroupGuid;

    @ApiModelProperty(value = "分组名称", required = true)
    private String subgroupName;

    @ApiModelProperty(value = "套餐子项", required = true)
    private List<SubDineInItemDTO> subDineInItemDTOS;

    public PackageSubgroupDTO(String subgroupGuid, String subgroupName) {
        this.subgroupGuid = subgroupGuid;
        this.subgroupGuid = subgroupName;
    }
}
