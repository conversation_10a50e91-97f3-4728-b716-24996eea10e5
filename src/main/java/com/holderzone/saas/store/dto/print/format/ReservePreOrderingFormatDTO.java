package com.holderzone.saas.store.dto.print.format;

import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class ReservePreOrderingFormatDTO extends FormatDTO {

    @NotNull(message = "门店名称不能为空")
    @ApiModelProperty(value = "门店名称", required = true)
    private FormatMetadata storeName;

    @NotNull(message = "单据类型不能为空")
    @ApiModelProperty(value = "单据类型", required = true)
    private FormatMetadata invoiceName;

    @NotNull(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始时间", required = true)
    private FormatMetadata beginTime;

    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间", required = true)
    private FormatMetadata endTime;


    @Override
    public void applyDefault() {
        super.applyDefault();
        if (storeName == null) {
            storeName = new FormatMetadata(2, 1, false);
        }
        if (invoiceName == null) {
            invoiceName = new FormatMetadata(2, 1, false);
        }

    }
}
