package com.holderzone.saas.store.dto.store.store;


import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 扎帐信息
 * <AUTHOR>
 */
@Data
public class BindupAccountsDTO implements Serializable {

    private Long id;

    /**
     * 扎帐时间
     */
    private LocalDateTime bindupAccounts;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 用户id
     */
    private String userGuid;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 错误码
     */
    private Long code;

    /**
     * 信息
     */
    private String message;

}