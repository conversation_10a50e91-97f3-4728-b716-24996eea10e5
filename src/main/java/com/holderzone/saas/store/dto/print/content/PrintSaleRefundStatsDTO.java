package com.holderzone.saas.store.dto.print.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/14
 * @since 1.8
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("退款统计单")
public class PrintSaleRefundStatsDTO extends PrintDTO {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private String storeName;

    @NotNull(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始时间", required = true)
    private Long startTime;

    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间", required = true)
    private Long endTime;

    @NotNull(message = "收银员不能为空")
    @ApiModelProperty(value = "收银员")
    private String checkoutStaffs;

    @Nullable
    @ApiModelProperty(value = "正餐部分退款", required = true)
    private SaleRefundStats dineinPartRefund;

    @Nullable
    @ApiModelProperty(value = "正餐反结账", required = true)
    private SaleRefundStats dineinRecovery;

    @Nullable
    @ApiModelProperty(value = "快销部分退款", required = true)
    private SaleRefundStats fastPartRefund;

    @Nullable
    @ApiModelProperty(value = "快销反结账", required = true)
    private SaleRefundStats fastRecovery;

    @Nullable
    @ApiModelProperty(value = "合计", required = true)
    private SaleRefundStats total;

    @Data
    public static class SaleRefundStats {

        @ApiModelProperty(value = "退款方式名称", required = true)
        private String refundName;

        @NotNull(message = "退款笔数不能为空")
        @ApiModelProperty(value = "退款笔数", required = true)
        private Integer refundOrderCount;

        @ApiModelProperty(value = "退款笔数", required = true)
        private String refundOrderCountStr;

        @NotNull(message = "退款总金额不能为空")
        @ApiModelProperty(value = "退款总金额", required = true)
        private BigDecimal refundAmount;

        @ApiModelProperty(value = "退款总金额", required = true)
        private String refundAmountStr;
    }
}
