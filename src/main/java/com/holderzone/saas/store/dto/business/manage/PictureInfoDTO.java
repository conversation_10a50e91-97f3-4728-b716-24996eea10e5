package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PictureInfoDTO
 * @date 2018/11/16 14:30
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class PictureInfoDTO implements Serializable {

    @ApiModelProperty(value = "像素类型 1920*1080-->1; 800*800-->2;1024*600-->3;635*600-->4 ")
    private Integer pxType;

    @ApiModelProperty(value = "图片类型 1-->副屏图片，2-->副屏点餐图片")
    private Integer picType;


}
