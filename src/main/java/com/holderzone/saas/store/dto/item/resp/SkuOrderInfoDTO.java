package com.holderzone.saas.store.dto.item.resp;

import com.holderzone.saas.store.dto.common.BaseDTO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillDishDTO
 * @date 2018/07/17 下午2:07
 * @description //菜品传输对象
 * @program holder-saas-store-menu
 */
@Data
public class SkuOrderInfoDTO extends BaseDTO {
    private static final long serialVersionUID = -1007804775685846100L;
    /**
     * skuGUID
     */
    private String skuGuid;

    /**
     * 规格名称
     */
    private String skuName;
    /**
     * 商品GUID
     */
    private String itemGuid;
    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 菜品分类GUID
     */
    private String typeGuid;

    /**
     * 菜品分类名称
     */
    private String typeName;

    /**
     * 商品类型：1.套餐（不称重，无规格），2.规格商品（非单品，不称重），3.称重商品（单菜品，称重），4.单品。
     */
    private Integer itemType;

    /**
     * 规格编号
     */
    private String code;

    /**
     * 计数单位
     */
    private String unit;

    /**
     * 计数单位code(ItemUnitEnum)
     */
    private Integer unitCode;

    /**
     * 售卖价格
     */
    private BigDecimal salePrice;

}

