package com.holderzone.saas.store.dto.trade.resp.adjust;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 调整单详情
 */
@Data
@Accessors(chain = true)
public class AdjustOrderDetailRespDTO implements Serializable {

    private static final long serialVersionUID = -900312572049799255L;

    @ApiModelProperty("调整单guid")
    private Long guid;

    @ApiModelProperty("订单guid")
    private Long orderGuid;

    @ApiModelProperty("交易模式(0：正餐，1：快餐，3：外卖)")
    private Integer tradeMode;

    @ApiModelProperty("调整单号")
    private String adjustNo;

    @ApiModelProperty("调整金额")
    private BigDecimal adjustPrice;

    @ApiModelProperty("调整原因")
    private String reason;

    @ApiModelProperty("调整单操作员")
    private String createStaffName;

    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty("商品明细")
    private List<DineInItemDTO> adjustItemList;
}
