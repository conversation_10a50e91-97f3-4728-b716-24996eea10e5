package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class DstSkuBindRespDTO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    @ApiModelProperty(value = "商品Guid")
    private String itemGuid;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "规格Id")
    private String skuGuid;

    @ApiModelProperty(value = "规格名称")
    private String skuName;

    @ApiModelProperty(value = "规格编码")
    private String skuCode;

    @ApiModelProperty(value = "菜品绑定设备区域")
    private List<DstAreaBindRespDTO> areas;

    @ApiModelProperty(value = "超时时间：分钟")
    private Integer timeout;

    @ApiModelProperty(value = "是否被当前设备绑定")
    private Boolean isBoundBySelf;

    @ApiModelProperty(value = "是否被其他设备绑定")
    private Boolean isBoundByOthers;
}
