package com.holderzone.saas.store.dto.terminal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className StoreTerminalDTO
 * @date 18-9-6 上午10:37
 * @description 门店-设备DTO
 * @program holder-saas-store-dto
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
@Accessors(chain = true)
public class StoreDeviceDTO implements Serializable {

    private static final long serialVersionUID = 2998556334602497964L;

    /**
     * 关联的企业guid
     */
    @ApiModelProperty(value = "关联的企业guid")
    private String enterpriseGuid;

    /**
     * 门店编号
     */
    @ApiModelProperty(value = "门店编号")
    private String storeNo;

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 厂商设备编号
     */
    @ApiModelProperty(value = "厂商设备编号")
    private String deviceNo;

    /**
     * 系统设备编号
     */
    @ApiModelProperty(value = "系统设备编号（云端生成）")
    private String deviceGuid;

    /**
     * 设备是否绑定
     */
    @ApiModelProperty(value = "设备是否绑定")
    private Boolean binding;

    /**
     * 设备是否注册
     */
    @ApiModelProperty(value = "设备是否已在云端录入")
    private Boolean register;

    /**
     * 设备类型
     * PC服务端- 0
     * PC平板- 1
     * 小店通- 2
     * 一体机- 3
     * POS机- 4
     * 云平板- 5
     * 点菜宝(M1)- 6
     * PV1(带刷卡的点菜宝)- 7
     * 厨房显示系统- 9
     * 厨房取餐屏- 10
     */
    @ApiModelProperty(value = "0=PC服务端，1=PC平板，2=小店通，3=一体机，4=POS机，5=云平板，" +
            "6=点菜宝(M1)，7=PV1(带刷卡的点菜宝)，9=厨房显示系统，10=厨房取餐屏")
    private Integer deviceType;

    /**
     * 设备类型名称
     */
    @ApiModelProperty(value = "设备类型名称")
    private String deviceName;

    /**
     * 打印设备排序（仅设备类型为一体机有效，默认为0。）
     */
    @ApiModelProperty(value = "打印设备排序（仅设备类型为一体机有效，默认为0。）")
    private Integer sort;

    /**
     * 创建人guid
     */
    @ApiModelProperty(value = "创建人guid")
    private String createUserGuid;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;

    /**
     * 设备解绑时间
     */
    @ApiModelProperty(value = "设备解绑时间")
    private LocalDateTime gmtUnbind;

    /**
     * 桌台号guid
     */
    @ApiModelProperty(value = "桌台号guid")
    private String tableGuid;

    /**
     * pad点餐类型(0:商家点餐 1:用户自主点餐)
     */
    @ApiModelProperty(value = "pad点餐类型(0:商家点餐 1:用户自主点餐)")
    private Integer padOrderType;
}
