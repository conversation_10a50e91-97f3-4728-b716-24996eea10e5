package com.holderzone.saas.store.dto.weixin.deal;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel(value = "加菜入参")
public class ShopCartItemReqDTO {

	@ApiModelProperty(required = true)
	@NotNull(message="商品必传")
	private ItemInfoDTO itemInfoDTO;

	@ApiModelProperty(value = "true：代表加减，-1减,小于起卖数删除，0：不处理，正数代表加数量，false:0：删除，正数代表直接修改数量,小于起卖数删除",required = true)
	@NotNull(message = "购物车加菜类型必传")
	private Boolean changeType;

	@ApiModelProperty(value = "商品修改数量",required = true)
	@NotNull(message = "加入购物车，数量必传")
	private BigDecimal modifyNum;

	@ApiModelProperty(value = "用户id",hidden = true)
	private String openid;

	@ApiModelProperty(value = "昵称",hidden = true)
	private String nickName;

	@ApiModelProperty(value = "用户微信头像",hidden = true)
	private String headImgUrl;

	@ApiModelProperty(value = "购物车修改时间",hidden = true)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime gmtCreate;
}
