package com.holderzone.saas.store.dto.reserve;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/19
 * @description 预订反结账
 */
@Data
@ApiModel(value = "预订反结账")
@EqualsAndHashCode(callSuper = false)
public class ReserveRecoveryDTO implements Serializable {

    private static final long serialVersionUID = 1392310445586749055L;

    @ApiModelProperty(value = "预定guid")
    private String reserveGuid;

    @ApiModelProperty(value = "预定guid")
    private String newReserveGuid;

    @ApiModelProperty(value = "原单guid")
    private String orderGuid;

    @ApiModelProperty(value = "新单guid")
    private String newOrderGuid;

}
