package com.holderzone.saas.store.dto.print.format;

import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 餐饮退款单格式
 *
 * <AUTHOR>
 * @date 2025/07/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RefundFormatDTO extends FormatDTO {

    @NotNull(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private FormatMetadata storeName;

    @NotNull(message = "单据类型不能为空")
    @ApiModelProperty(value = "单据类型", required = true)
    private FormatMetadata invoiceName;

    @NotNull(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    private FormatMetadata orderNo;

    @NotNull(message = "牌号不能为空")
    @ApiModelProperty(value = "牌号", required = true)
    private FormatMetadata markNo;

    @NotNull(message = "退款时间不能为空")
    @ApiModelProperty(value = "退款时间", required = true)
    private FormatMetadata refundTime;

    @NotNull(message = "商品名称不能为空")
    @ApiModelProperty(value = "商品名称", required = true)
    private FormatMetadata itemName;

    @NotNull(message = "单价不能为空")
    @ApiModelProperty(value = "单价", required = true)
    private FormatMetadata itemPrice;

    @NotNull(message = "退款数量不能为空")
    @ApiModelProperty(value = "退款数量", required = true)
    private FormatMetadata itemNumber;

    @NotNull(message = "小计不能为空")
    @ApiModelProperty(value = "小计", required = true)
    private FormatMetadata itemTotal;

    @NotNull(message = "商品组件不能为空")
    @ApiModelProperty(value = "商品组件", required = true)
    private FormatMetadata itemLayout;

    @NotNull(message = "商品属性不能为空")
    @ApiModelProperty(value = "商品属性", required = true)
    private FormatMetadata itemProperty;

    @NotNull(message = "附加费明细不能为空")
    @ApiModelProperty(value = "附加费明细", required = true)
    private FormatMetadata additionalCharge;

    @NotNull(message = "附加费合计不能为空")
    @ApiModelProperty(value = "附加费合计", required = true)
    private FormatMetadata additionalChargeTotal;

    @NotNull(message = "退款金额不能为空")
    @ApiModelProperty(value = "退款金额", required = true)
    private FormatMetadata refundAmount;

    @NotNull(message = "退款方式不能为空")
    @ApiModelProperty(value = "退款方式", required = true)
    private FormatMetadata refundMethod;

    @NotNull(message = "退款原因不能为空")
    @ApiModelProperty(value = "退款原因", required = true)
    private FormatMetadata refundReason;

    @NotNull(message = "操作员不能为空")
    @ApiModelProperty(value = "操作员", required = true)
    private FormatMetadata operator;

    @NotNull(message = "操作时间不能为空")
    @ApiModelProperty(value = "操作时间", required = true)
    private FormatMetadata operationTime;

    @NotNull(message = "打印时间不能为空")
    @ApiModelProperty(value = "打印时间", required = true)
    private FormatMetadata printTime;

    @Override
    public void applyDefault() {
        super.applyDefault();
        
        if (storeName == null) {
            storeName = new FormatMetadata(2, 1, false); // 1px*1px
        }
        if (invoiceName == null) {
            invoiceName = new FormatMetadata(2, 1, false); // 2px*2px
        }
        if (orderNo == null) {
            orderNo = new FormatMetadata(1, 1, false); // 1px*1px
        }
        if (markNo == null) {
            markNo = new FormatMetadata(1, 1, false); // 1px*1px
        }
        if (refundTime == null) {
            refundTime = new FormatMetadata(1, 1, false); // 1px*1px
        }
        if (itemName == null) {
            itemName = new FormatMetadata(1, 1, false); // 标题1px*1px
        }
        if (itemPrice == null) {
            itemPrice = new FormatMetadata(1, 2, false); // 商品信息1px*2px
        }
        if (itemNumber == null) {
            itemNumber = new FormatMetadata(1, 2, false); // 商品信息1px*2px
        }
        if (itemTotal == null) {
            itemTotal = new FormatMetadata(1, 2, false); // 商品信息1px*2px
        }
        if (itemLayout == null) {
            itemLayout = new FormatMetadata(1, 2, 0, false);
        }
        if (itemProperty == null) {
            itemProperty = new FormatMetadata(1, 0, false); // 属性1px*1px
        }
        if (additionalCharge == null) {
            additionalCharge = new FormatMetadata(1, 3, false);
        }
        if (additionalChargeTotal == null) {
            additionalChargeTotal = new FormatMetadata(1, 3, false);
        }
        if (refundAmount == null) {
            refundAmount = new FormatMetadata(2, 2, true); // 退款金额2px*2px
        }
        if (refundMethod == null) {
            refundMethod = new FormatMetadata(2, 2, true); // 退款方式2px*2px
        }
        if (refundReason == null) {
            refundReason = new FormatMetadata(1, 0, false);
        }
        if (operator == null) {
            operator = new FormatMetadata(1, 1, false); // 1px*1px
        }
        if (operationTime == null) {
            operationTime = new FormatMetadata(1, 1, false); // 1px*1px
        }
        if (printTime == null) {
            printTime = new FormatMetadata(1, 1, false); // 1px*1px
        }
    }
}
