package com.holderzone.saas.store.dto.item.resp;

import com.holderzone.saas.store.dto.takeaway.ErpMappingItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 门店商品返回实体
 * @date 2022/5/17 16:05
 * @className: StoreItemListRespDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "门店商品返回实体")
public class StoreItemListRespDTO implements Serializable {

    private static final long serialVersionUID = 5178503166349560956L;

    @ApiModelProperty("门店GUID")
    private String storeGuid;

    @ApiModelProperty("门店商品列表")
    private List<ErpMappingItem> erpMappingItemList;
}
