package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "mdm商品规格返回实体")
public class SelectItemSkuDTO implements Serializable {
    private static final long serialVersionUID = 4657179005772741650L;
    @ApiModelProperty("创建时间")
    private Date gmtCreate;
    @ApiModelProperty("修改时间")
    private Date gmtModified;
    @ApiModelProperty("MDM itemSku的主键")
    private String guid;
    @ApiModelProperty("第三方创建itemSku时的主键")
    private String thirdNo;
    @ApiModelProperty("规格名称")
    private String name;
    @ApiModelProperty("商品GUID")
    private String itemGuid;
    @ApiModelProperty("品牌GUID")
    private String brandGuid;
    @ApiModelProperty("关联的门店GUID")
    private String storeGuid;
    @ApiModelProperty("upc商品条码")
    private String upc;
    @ApiModelProperty("编号")
    private String code;
    @ApiModelProperty("售卖价格")
    private BigDecimal salePrice;
    @ApiModelProperty("商品规格单位")
    private String unit;
    @ApiModelProperty("规格来源")
    private Integer skuFrom;
}