package com.holderzone.saas.store.dto.print.cloud;

import com.holderzone.saas.store.dto.print.PrinterItemDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/27
 * @description 设备用途
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "云打印机", description = "云打印机")
public class DeviceUseDTO implements Serializable {

    private static final long serialVersionUID = -2056502305524241052L;

    @ApiModelProperty(value = "小票类型 1后厨点菜单 2结账单")
    private Integer invoiceType;

    @ApiModelProperty(value = "打印次数； 新增必传； 修改选传", example = "1")
    private Integer printCount;

    @ApiModelProperty(value = "出单商品")
    private List<PrinterItemDTO> arrayOfItemDTO;

}
