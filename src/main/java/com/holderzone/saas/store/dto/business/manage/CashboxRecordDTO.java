package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CashboxRecordDO
 * @date 2018/07/29 下午5:42
 * @description 钱箱DTO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CashboxRecordDTO {

    /**
     * 门店guid
     */
    @ApiModelProperty("门店guid")
    private String storeGuid;

    /**
     * 交接班guid
     */
    @ApiModelProperty("交接班guid")
    private String handoverRecordGuid;

    /**
     * 钱箱记录guid
     */
    @ApiModelProperty("钱箱记录guid")
    private String cashboxRecordGuid;

    /**
     * 用户guid
     */
    @ApiModelProperty("用户guid")
    private String userGuid;

    /**
     * 用户名字
     */
    @ApiModelProperty("用户名字")
    private String userName;

    /**
     * 操作类型
     * 0=存入
     * 1=取出
     */
    @ApiModelProperty("操作类型：0=存入，1=取出")
    private Integer operationType;

    /**
     * 来源
     * 0=开钱箱存取款
     * 1=收银存取款
     */
    @ApiModelProperty("来源：0=开钱箱存取款，1=收银存取款")
    private Integer sourceType;

    /**
     * 现金
     */
    @ApiModelProperty("现金")
    private BigDecimal money;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime gmtModified;
}
