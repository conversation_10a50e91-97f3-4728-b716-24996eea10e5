package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

import static com.holderzone.saas.store.dto.print.util.PrintMockUtils.*;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("并台预结单")
public class PrintPreCoTableCbDTO extends PrintDTO implements PrintDataMockito {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名")
    private String storeName;

    @Valid
    @NotEmpty(message = "并台订单列表不得为空")
    @ApiModelProperty(value = "并台订单列表", required = true)
    private List<PrintPreCoTableCbDTO.PrintTableDTO> tableOrderList;

    @NotNull(message = "并台合计金额不能为空")
    @ApiModelProperty(value = "并台合计金额", required = true)
    private BigDecimal tableTotal;

    @Valid
    @Nullable
    @ApiModelProperty(value = "附加费", required = true)
    private List<AdditionalCharge> additionalChargeList;

    @Valid
    @Nullable
    @ApiModelProperty(value = "优惠折扣", required = true)
    private List<ReduceRecord> reduceRecordList;

    @NotNull(message = "应付金额不能为空")
    @ApiModelProperty(value = "应付金额", required = true)
    private BigDecimal payAble;

    @Nullable
    @ApiModelProperty(value = "门店地址", notes = "如果未设置门店地址，该值为空")
    private String storeAddress;

    @Nullable
    @ApiModelProperty(value = "门店电话", notes = "如果未设置门店电话，该值为空")
    private String tel;

    @Nullable
    @ApiModelProperty(value = "买单二维码", notes = "如果未设置买单二维码，该值为空")
    private String payQrCode;

    @Override
    public void applyMock() {
        super.applyMock();
        setStoreName(mockStoreName());
        setTableOrderList(mockPreTableOrders());
        setTableTotal(mockTotal());
        setAdditionalChargeList(mockAdditionalChargeList());
        setReduceRecordList(mockReduceRecordList());
        setPayAble(mockPayable());
        setStoreAddress(mockStoreAddress());
        setTel(mockStoreTel());
    }

    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    @ApiModel("并台订单")
    public static class PrintTableDTO {

        @Valid
        @Nullable
        @ApiModelProperty(value = "商品列表，后厨单中商品列表不得为空，其他情况允许为空")
        private List<PrintItemRecord> printItemRecordList;

        @Nullable
        @ApiModelProperty(value = "整单备注")
        private String orderRemark;

        @NotBlank(message = "桌位或牌号不能为空")
        @ApiModelProperty(value = "桌位或牌号（号牌名称：正餐'桌位：'，快餐'牌号：'）", required = true)
        private String markNo;

        @NotBlank(message = "订单号不能为空")
        @ApiModelProperty(value = "订单号", required = true)
        private String orderNo;

        @NotNull(message = "就餐人数不得为空")
        @ApiModelProperty(value = "就餐人数", required = true)
        private Integer personNumber;

        @NotNull(message = "开台时间不能为空")
        @ApiModelProperty(value = "开台时间", required = true)
        private Long openTableTime;

        @NotNull(message = "商品总额不能为空")
        @ApiModelProperty(value = "商品总额", required = true)
        private BigDecimal total;

        @ApiModelProperty(value = "商品折后总额", required = true)
        private BigDecimal discountAfterTotal;
    }
}
