package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PropertyValueRespDTO
 * @date 2019/01/07 下午7:43
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel(value = "属性值返回实体")
public class AttrRespDTO implements Serializable {
    @ApiModelProperty(value = "属性组GUID")
    private String attrGroupGuid;
    @ApiModelProperty(value = "属性值GUID")
    private String attrGuid;

    @ApiModelProperty(value = "属性名称")
    private String name;

    @ApiModelProperty(value = "加价")
    private BigDecimal price;

    @ApiModelProperty(value = "是否是默认选中项，0：否;1:是")
    private Integer isDefault;

    @ApiModelProperty(value = "关联的分类集合")
    private List<TypeWebRespDTO> typeList;

    @ApiModelProperty(value = "属性值来源:0/门店自建，1/品牌自建，2被推送")
    private Integer attrFrom;

    /**
     * 是否点击
     * 老板助手使用
     */
    @ApiModelProperty(value = "是否点击")
    private Boolean isClick = Boolean.FALSE;
}
