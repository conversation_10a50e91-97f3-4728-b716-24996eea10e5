package com.holderzone.saas.store.dto.canal.req;

import com.holderzone.saas.store.enums.canal.MemberSynEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <p>
 * 品牌表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "HsmBrandReqDTO", description = "品牌信息")
public class HsmBrandReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 同步操作类型::同步功能使用
     */
    @ApiModelProperty(value = "同步操作类型::同步功能使用")
    private MemberSynEnum sycEnum;

    /**
     * 品牌信息GUID
     */
    @ApiModelProperty(value = "品牌信息GUID")
    private String guid;

    /**
     * 外部商家对应的品牌主键
     */


    @ApiModelProperty(value = "外部商家对应的品牌主键",required = true)
    private String brandKey;

    /**
     * 联盟ID
     */


    @ApiModelProperty(value = "联盟ID",required = true)
    private String allianceid;

    /**
     * 企业GUID
     */
    @ApiModelProperty(value = "会员平台返回的企业GUID")
    private String enterpriseGuid;


    /**
     * 外部商家主键
     */

    @ApiModelProperty(value = "外部商家主键",required = true)
    private String enterpriseKey;

    /**
     * 品牌名称
     */

    @ApiModelProperty(value = "品牌名称",required = true)
    private String brandName;

    /**
     * 品牌LOGO
     */

    @ApiModelProperty(value = "品牌LOGO")
    private String brandLogo;

    /**
     * 联系人名字
     */
    @ApiModelProperty(value = "联系人名字")
    private String contactName;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String contactTel;

    /**
     *  状态,0禁用,1启用
     */
    @ApiModelProperty(value = "状态,0禁用,1启用")
    private int isEnable;
}
