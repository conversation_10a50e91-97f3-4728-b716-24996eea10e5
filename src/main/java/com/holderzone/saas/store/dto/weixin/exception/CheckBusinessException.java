package com.holderzone.saas.store.dto.weixin.exception;

public class CheckBusinessException extends Exception{

	/**
	 * 
	 */
	private static final long serialVersionUID = 6705598161107463466L;
	private int code = 0;
	public CheckBusinessException() {
		super();
	}
	public CheckBusinessException(String message) {
		super(message);
	}
	public CheckBusinessException(int code,String message) {
		super(message);
		this.code=code;
	}
	public int getCode() {
		return code;
	}
	public void setCode(int code) {
		this.code = code;
		
	}
	public static void main(String[] args) {
		
	}
	
}
