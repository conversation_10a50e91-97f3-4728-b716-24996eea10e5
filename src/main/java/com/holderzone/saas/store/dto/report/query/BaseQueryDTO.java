package com.holderzone.saas.store.dto.report.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseQueryDTO
 * @date 2018/09/26 15:34
 * @description
 * @program holder-saas-report
 */
@ApiModel
@Data
public class BaseQueryDTO<T> implements Serializable {

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "Asia/Shanghai")
    protected LocalDateTime beginTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    protected LocalDateTime endTime;

    @ApiModelProperty(value = "门店guid")
    protected List<String> storeGuids;

    @ApiModelProperty(value = "企业guid")
    protected String enterpriseGuid;

    @ApiModelProperty(value = "模式 -1:全部 0:堂食 1:快餐  2:外卖")
    protected int tradeMode = -1;

    @ApiModelProperty(value = "0:营业日 1:自然日")
    protected int timeType;

    @ApiModelProperty(value = "当前页数")
    protected int currentPage = 1;

    @ApiModelProperty(value = "每页多少数据")
    protected int pageSize = 10;

    @ApiModelProperty(value = "当前登录用户guid")
    protected String userGuid;

    protected Integer type;

    public int makeStart() {
        return (this.currentPage <= 0 ? 1 : this.currentPage - 1) * this.pageSize;
    }

}
