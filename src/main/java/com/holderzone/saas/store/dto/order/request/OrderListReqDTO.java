package com.holderzone.saas.store.dto.order.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderListReqDTO
 * @date 2018/09/14 16:01
 * @description 订单列表入参
 * @program holder-saas-store-order
 */
@Data
public class OrderListReqDTO extends BasePageDTO{

    @ApiModelProperty(value = "状态(0：未完成， 1：已完成， 2：已作废 ，3：已退款，4：反结账状态)")
    private Integer state;

    @ApiModelProperty(value = "交易模式(0：正餐 1：快餐  2：外卖 )")
    private Integer tradeMode;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime beginTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "桌号，牌号，订单号")
    private String searchKey;

}
