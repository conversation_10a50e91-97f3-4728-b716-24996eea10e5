package com.holderzone.saas.store.dto.call;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 转台通知会员实体
 * @date 2022/3/15 19:36
 * @className: TurnCallMemberDTO
 */
@Data
@ApiModel("转台通知会员实体")
@Accessors(chain = true)
public class TurnCallMemberDTO implements Serializable {

    private static final long serialVersionUID = 8718119078358964088L;

    @ApiModelProperty("订单guid")
    private String orderNumber;

    @ApiModelProperty("门店Guid")
    private String storeGuid;

    @ApiModelProperty("桌台Guid")
    private String tableGuid;

    @ApiModelProperty("点餐来源")
    private Integer orderSource;

    @ApiModelProperty("订单状态 1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废")
    private Integer orderState;

    @ApiModelProperty("运营主体")
    private String operSubjectGuid;
}
