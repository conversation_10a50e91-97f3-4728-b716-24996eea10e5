package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Accessors(chain = true)
@ApiModel("会员支付")
public class MemberPayReusltRespDTO {
	@ApiModelProperty("0：成功，1：订单已经处理,2:密码错误,3:商品卖完了")
	private Integer result;

	@ApiModelProperty("失败原因")
	private String errorMsg;

	public static MemberPayReusltRespDTO success() {
		return new MemberPayReusltRespDTO(0, null);
	}

	public static MemberPayReusltRespDTO changeFailed(){
		return new MemberPayReusltRespDTO(1, "订单详情发生变化");
	}

	public static MemberPayReusltRespDTO changeMessageFailed(String message){
		return new MemberPayReusltRespDTO(1, message);
	}

	public static MemberPayReusltRespDTO errorPasswd(){
		return new MemberPayReusltRespDTO(2, "密码错误");
	}

	public static MemberPayReusltRespDTO memberCardDisable(){
		return new MemberPayReusltRespDTO(3, "该会员卡已禁用");
	}

	public static MemberPayReusltRespDTO memberCardFrozen(){
		return new MemberPayReusltRespDTO(3, "会员卡已被冻结");
	}


	public static MemberPayReusltRespDTO soldOut() {
		return new MemberPayReusltRespDTO(3, "商品卖完了");
	}

	public static MemberPayReusltRespDTO itemSynchronize(){
		return new MemberPayReusltRespDTO(3, "商品没有同步，无法使用会员支付");
	}

	public static MemberPayReusltRespDTO payFailed(){
		return new MemberPayReusltRespDTO(3, "支付失败");
	}

	public static MemberPayReusltRespDTO payBalanceFailed(){
		return new MemberPayReusltRespDTO(3, "余额不足");
	}

	public static MemberPayReusltRespDTO payAmountFailed() {
		return new MemberPayReusltRespDTO(3, "支付异常，请刷新后重试");
	}
}
