package com.holderzone.saas.store.dto.trade.resp.adjust;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.holder.saas.member.terminal.dto.activity.ThirdActivityDetailsRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 调整单-订单信息实体
 * @date 2022/1/18 17:12
 * @className: AdjustByOrderRespDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "调整单-订单信息实体")
public class AdjustByOrderRespDTO implements Serializable {

    private static final long serialVersionUID = -900312572049799255L;

    /**
     * 实收金额=订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））
     * 应收金额=订单金额-优惠金额-订金(押金)
     */
    @ApiModelProperty(value = "实收金额")
    private BigDecimal actuallyPayFee;

    /**
     * 超出金额
     * 目前只有第三方活动会有这玩意
     */
    private BigDecimal excessAmount;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "结账操作人名字")
    private String checkoutStaffName;

    /**
     * 交易模式(0：正餐，1：快餐，2外卖)
     */
    @ApiModelProperty(value = "交易模式(0：正餐，1：快餐，2外卖)")
    private Integer tradeMode;

    @ApiModelProperty(value = "订单商品列表")
    private List<AdjustByOrderItemRespDTO> orderItemList;

    @ApiModelProperty(value = "支付详情列表")
    private List<ActuallyPayFeeDetailDTO> payDetailList;

    /**
     * 调整单信息
     */
    @ApiModelProperty("调整单号")
    private String adjustNo;

    @ApiModelProperty("调整金额")
    private BigDecimal adjustPrice;

    @ApiModelProperty("调整原因")
    private String reason;

    @ApiModelProperty("调整单操作员")
    private String createStaffName;

    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "第三方活动详情")
    private List<ThirdActivityDetailsRespDTO> detailList;

    @ApiModelProperty("权限判断时间, 普通订单为结账时间，外卖为接单时间")
    private LocalDateTime operateTime;
}
