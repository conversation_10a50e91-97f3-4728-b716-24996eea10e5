package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DisplayStoreRespDTO {

    /**
     * 全局唯一主键
     */
    @ApiModelProperty(value = "全局唯一主键")
    private String guid;
    /**
     * 显示规则guid
     */
    @ApiModelProperty(value = "显示规则guid")
    private String ruleGuid;
    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;
    /**
     * 门店名字
     */
    @ApiModelProperty(value = "门店名字")
    private String storeName;
    /**
     * 规则类型 0显示批次 1菜品汇总
     */
    @ApiModelProperty(value = "规则类型 0显示批次 1菜品汇总")
    private Integer ruleType;
}
