/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:TakeoutFormatDTO.java
 * Date:2019-12-5
 * Author:terry
 */

package com.holderzone.saas.store.dto.print.format;

import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class TakeoutFormatDTO extends FormatDTO {

    @NotNull(message = "平台不能为空")
    @ApiModelProperty(value = "平台", required = true)
    private FormatMetadata platform;

    @NotNull(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名", required = true)
    private FormatMetadata storeName;

    @NotNull(message = "支付消息不能为空")
    @ApiModelProperty(value = "支付消息", required = true)
    private FormatMetadata payMsg;

    @NotNull(message = "期望送达时间不能为空")
    @ApiModelProperty(value = "期望送达时间", required = true)
    private FormatMetadata expectTime;

    @NotNull(message = "下单时间不能为空")
    @ApiModelProperty(value = "下单时间", required = true)
    private FormatMetadata orderTime;

    @NotNull(message = "备注不能为空")
    @ApiModelProperty(value = "备注", required = true)
    private FormatMetadata remark;

    @NotNull(message = "商品组件不能为空")
    @ApiModelProperty(value = "商品组件", required = true)
    private FormatMetadata itemLayout;

    @NotNull(message = "商品单价不能为空")
    @ApiModelProperty(value = "商品单价", required = true)
    private FormatMetadata itemPrice;

    @NotNull(message = "商品小计不能为空")
    @ApiModelProperty(value = "商品小计", required = true)
    private FormatMetadata itemTotal;

    @NotNull(message = "分类小计不能为空")
    @ApiModelProperty(value = "分类小计", required = true)
    private FormatMetadata typeTotal;

    @NotNull(message = "商品总计不能为空")
    @ApiModelProperty(value = "商品总计", required = true)
    private FormatMetadata itemSumTotal;

    @NotNull(message = "餐盒费合计不能为空")
    @ApiModelProperty(value = "餐盒费合计", required = true)
    private FormatMetadata packageTotal;

    @NotNull(message = "配送费合计不能为空")
    @ApiModelProperty(value = "配送费合计", required = true)
    private FormatMetadata shipTotal;

    @NotNull(message = "折扣不能为空")
    @ApiModelProperty(value = "折扣", required = true)
    private FormatMetadata discount;

    @NotNull(message = "原价不能为空")
    @ApiModelProperty(value = "原价", required = true)
    private FormatMetadata originalPrice;

    @NotNull(message = "实付不能为空")
    @ApiModelProperty(value = "实付", required = true)
    private FormatMetadata actuallyPay;

    @NotNull(message = "顾客信息不能为空")
    @ApiModelProperty(value = "顾客信息", required = true)
    private FormatMetadata customerInfo;

    @NotNull(message = "操作员不能为空")
    @ApiModelProperty(value = "操作员", required = true)
    private FormatMetadata operator;

    @NotNull(message = "打印时间不能为空")
    @ApiModelProperty(value = "打印时间", required = true)
    private FormatMetadata printTime;

    @NotNull(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    private FormatMetadata orderNo;

    @NotNull(message = "条形码不能为空")
    @ApiModelProperty(value = "条形码", required = true)
    private FormatMetadata orderNoBarCode;

    @NotNull(message = "出餐码不能为空")
    @ApiModelProperty(value = "出餐码", required = true)
    private FormatMetadata foodFinishBarCode;

    @NotNull(message = "套餐内容不能为空")
    @ApiModelProperty(value = "套餐内容", required = true)
    private FormatMetadata packageContent;

    @Override
    public void applyDefault() {
        super.applyDefault();
        if (platform == null) {
            platform = new FormatMetadata(2, 2, 1, true);
        }
        if (storeName == null) {
            storeName = new FormatMetadata(1, 1, 1, false);
        }
        if (payMsg == null) {
            payMsg = new FormatMetadata(1, 1, 1, true);
        }
        if (expectTime == null) {
            expectTime = new FormatMetadata(1, 2, 0, false);
        }
        if (orderTime == null) {
            orderTime = new FormatMetadata(1, 1, 0, false);
        }
        if (remark == null) {
            remark = new FormatMetadata(1, 2, 0, false);
        }
        if (itemLayout == null) {
            itemLayout = new FormatMetadata(1, 2, 0, false);
        }
        if (itemPrice == null) {
            itemPrice = new FormatMetadata(1, 2, 0, false);
        }
        if (typeTotal == null) {
            typeTotal = new FormatMetadata(1, 2, 0, false, false);
        }
        if (itemTotal == null) {
            itemTotal = new FormatMetadata(1, 2, 0, false);
        }
        if (itemSumTotal == null) {
            itemSumTotal = new FormatMetadata(1, 1, 3, false);
        }
        if (packageTotal == null) {
            packageTotal = new FormatMetadata(1, 1, 3, false);
        }
        if (shipTotal == null) {
            shipTotal = new FormatMetadata(1, 1, 3, false);
        }
        if (discount == null) {
            discount = new FormatMetadata(1, 1, 0, false);
        }
        if (originalPrice == null) {
            originalPrice = new FormatMetadata(1, 1, 0, false);
        }
        if (actuallyPay == null) {
            actuallyPay = new FormatMetadata(1, 1, 0, false);
        }
        if (customerInfo == null) {
            customerInfo = new FormatMetadata(1, 1, 0, false);
        }
        if (operator == null) {
            operator = new FormatMetadata(1, 1, 0, false);
        }
        if (printTime == null) {
            printTime = new FormatMetadata(1, 1, 0, false);
        }
        if (orderNo == null) {
            orderNo = new FormatMetadata(1, 1, 1, false);
        }
        if (orderNoBarCode == null) {
            orderNoBarCode = new FormatMetadata(1, 1, 1, false);
        }
        if (foodFinishBarCode == null) {
            foodFinishBarCode = new FormatMetadata(1, 1, 1, false, false);
        }
        if (packageContent == null) {
            packageContent = new FormatMetadata(1, 1, 1, false, false);
        }
    }
}
