package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverRecordCreateDTO
 * @date 2018/07/29 上午11:39
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
@Accessors(chain = true)
public class HandoverRecordCreateDTO {

    /**
     * 门店guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    /**
     * 门店名称
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店名称不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店名称", required = true)
    private String storeName;

    /**
     * 创建人(接班人)guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "接班人guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "创建人(接班人)guid", required = true)
    private String createUserGuid;

    /**
     * 创建人(接班人)名字
     */
    @NotNull
    @Size(min = 1, max = 45, message = "接班人名字不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "创建人(接班人)名字", required = true)
    private String createUserName;

    /**
     * 设备id
     */
    @NotNull
    @Size(min = 1, max = 45, message = "设备id不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "设备id", required = true)
    private String terminalId;
}
