package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreAdvanceConsumerReqDTO
 * @date 2019/4/9
 */
@Data
@ApiModel(value="微信门店顾客下单信息")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WxStoreAdvanceConsumerReqDTO {

	@ApiModelProperty(value = "0：查数据，1：删除用户，2：整单备注,3:建立连接")
	private Integer advanceType;

	@ApiModelProperty(value="订单guid")
	private String tradeOrderGuid;

	private WxStoreConsumerDTO wxStoreConsumerDTO;

	@ApiModelProperty(value = "就餐人数", required = true)
	private Integer userCount;

	@ApiModelProperty(value = "整单备注")
	private String orderRemark;

	@ApiModelProperty(value = "是否是快餐,1快餐，0：正餐",required = true)
	private Integer orderModel;

	@ApiModelProperty(value = "当不为空时，表示这时在请求别人的预订单")
	private String nopenId;

	@ApiModelProperty(value = "1：订单详情，2：买单页面")
	private Integer pageNum;

	@ApiModelProperty(value = "pagGuid")
	private String payGuid;

	@ApiModelProperty(value = "是否计算积分1：计算，2：不计算")
	private Integer memberIntegral;

}
