package com.holderzone.saas.store.dto.journaling.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.framework.util.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderDetailReportReqDTO
 * @date 2019/05/28 17:38
 * @description 报表-订单明细请求入参
 * @program holder-saas-store
 */
@Data
@ApiModel("报表-销售明细请求入参")
public class SaleDetailReportReqDTO extends Page {

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate startDate;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate endDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "开始营业日期时间 yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "开始营业日期时间不能为空")
    protected LocalDateTime businessStartDateTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(value = "营业结束日期时间 yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "营业结束日期时间不能为空")
    protected LocalDateTime businessEndDateTime;

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "门店guid")
    private List<String> storeGuid;

    @ApiModelProperty(value = "商品分类guid")
    private String itemTypeGuid;

    @ApiModelProperty(value = "商品类型（1.套餐主项，2.规格，3.称重，4.单品 ）")
    private List<Integer> itemType;

    @ApiModelProperty(value = "订单操作（0：全部， 1：点菜， 2：赠菜，3：退菜）")
    private Integer state;

    @ApiModelProperty(value = "start")
    private Integer start;

    public int getStart() {
        return (int) ((this.getCurrentPage() <= 0 ? 1 : this.getCurrentPage() - 1) * this.getPageSize());
    }

}
