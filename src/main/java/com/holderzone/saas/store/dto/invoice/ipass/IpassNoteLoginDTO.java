package com.holderzone.saas.store.dto.invoice.ipass;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@ApiModel(description = "Ipass开票校验请求")
@Data
public class IpassNoteLoginDTO implements Serializable {


    @ApiModelProperty(value = "门店guid")
    private String storeId;

    @ApiModelProperty(value = "注册人账号")
    private String account;

    private String sms;
}
