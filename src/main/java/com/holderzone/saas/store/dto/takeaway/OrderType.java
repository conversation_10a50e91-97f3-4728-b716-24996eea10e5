package com.holderzone.saas.store.dto.takeaway;

public enum OrderType {

    TAKEOUT_ORDER(0, "外卖订单"),

    WECHAT_ORDER(1, "微信订单"),

    OTHER_ORDER(2, "其它订单"),

    PAD_ORDER(3, "PAD订单"),

    ALI_ORDER(4, "支付宝订单"),
    ;

    /**
     * 类型
     */
    private int type;

    /**
     * 描述
     */
    private String desc;

    OrderType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static OrderType ofType(int type) {
        for (OrderType orderType : values()) {
            if (orderType.type == type) {
                return orderType;
            }
        }
        throw new IllegalArgumentException("无效的OrderType[" + type + "]");
    }

    public enum TakeoutSubType {

        MT_TAKEOUT(0, "美团外卖", "美团"),

        ELE_TAKEOUT(1, "饿了么外卖", "饿了么"),

        BD_TAKEOUT(2, "百度外卖", "百度"),

        JD_TAKEOUT(3, "京东外卖", "京东"),

        DD_TAKEOUT(4, "滴滴外卖", "滴滴"),

        OWN_TAKEOUT(5, "自营外卖", "自营外卖"),

        TCD_TAKEOUT(6, "赚餐自营外卖", "赚餐自营外卖"),

        TIKTOK_TAKEOUT(7, "抖音外卖", "抖音"),

        ;

        /**
         * 类型
         */
        private int type;

        /**
         * 描述
         */
        private String desc;

        /**
         * 简要描述
         */
        private String source;

        TakeoutSubType(int type, String desc, String source) {
            this.type = type;
            this.desc = desc;
            this.source = source;
        }

        public int getType() {
            return type;
        }

        public String getDesc() {
            return desc;
        }

        public String getSource() {
            return source;
        }

        public Integer getTypeInteger() {
            return type;
        }

        public static TakeoutSubType ofType(int type) {
            for (TakeoutSubType takeoutSubType : values()) {
                if (takeoutSubType.type == type) {
                    return takeoutSubType;
                }
            }
            throw new IllegalArgumentException("无效的TakeoutSubType[" + type + "]");
        }

        public static TakeoutSubType ofDesc(String desc) {
            for (TakeoutSubType takeoutSubType : values()) {
                if (takeoutSubType.desc.equals(desc)) {
                    return takeoutSubType;
                }
            }
            return null;
        }
    }

    public enum WechatSubType {

        WECHAT_SCAN_CODE(0, "微信扫码"),

        WECHAT_RESERVE(1, "微信预定单");

        /**
         * 类型
         */
        private int type;

        /**
         * 描述
         */
        private String desc;

        WechatSubType(int type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public int getType() {
            return type;
        }

        public String getDesc() {
            return desc;
        }

        public static WechatSubType ofType(int type) {
            for (WechatSubType wechatSubType : values()) {
                if (wechatSubType.type == type) {
                    return wechatSubType;
                }
            }
            throw new IllegalArgumentException("无效的WechatSubType[" + type + "]");
        }
    }
}
