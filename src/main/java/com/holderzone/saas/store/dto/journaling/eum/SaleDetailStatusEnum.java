package com.holderzone.saas.store.dto.journaling.eum;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.journaling.req.StoreGatherReportReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.StoreGatherReportRespDTO;
import com.holderzone.saas.store.enums.item.WeekEnum;

import java.util.Arrays;

public enum SaleDetailStatusEnum {

    ORDER(1, "点菜"),
    FREE(2, "赠菜"),
    RETURN(3, "退菜");

    private Integer code;
    private String name;

    SaleDetailStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNames(Integer code) {
        return Arrays.stream(SaleDetailStatusEnum.values()).filter(p -> p.getCode().equals(code))
                .findFirst().orElseThrow(() -> new BusinessException("不存在当前code对应的name")).getName();
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
