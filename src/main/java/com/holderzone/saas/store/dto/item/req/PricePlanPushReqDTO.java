package com.holderzone.saas.store.dto.item.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "价格方案推送入参")
public class PricePlanPushReqDTO {

    @ApiModelProperty(value = "方案guid")
    private String planGuid;

    @ApiModelProperty(value = "推送类型：1-立即推送，2-按时间推送")
    private Integer pushType;

    @ApiModelProperty(value = "推送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pushDate;

    /***
     *  售卖类型-0 默认（全时段）-1特殊时段
     */
    @ApiModelProperty(value = "售卖类型-0 默认（全时段）-1特殊时段")
    private Integer sellTimeType;
}
