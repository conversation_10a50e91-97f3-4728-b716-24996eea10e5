package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemSortReqDTO
 * @date 2019/10/23 下午4:21
 * @description //批量更新商品排序请求实体
 * @program holder
 */

@Data
@ApiModel(value = "批量更新商品排序请求实体")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemSortReqDTO {

    @NotNull
    @ApiModelProperty(value = "商品id")
    private Integer id;

    @NotNull
    @ApiModelProperty(value = "商品guid")
    private String guid;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @NotNull
    @ApiModelProperty(value = "商品排序权重值 越小越靠前")
    private Integer sort;

}
