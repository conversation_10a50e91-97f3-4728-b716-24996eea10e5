package com.holderzone.saas.store.dto.deposit.req;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
public class OperationHistoryQueryReqDTO extends BasePageDTO {

    private static final long serialVersionUID = -2773042054434843563L;

    @ApiModelProperty(value = "寄存记录Guid")
    @NotNull(message = "寄存记录Guid不得为空")
    private String depositGuid;
}