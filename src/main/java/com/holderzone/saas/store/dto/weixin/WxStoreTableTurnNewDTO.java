package com.holderzone.saas.store.dto.weixin;

import com.holderzone.saas.store.dto.table.anno.LockField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WxStoreTableTurnNewDTO {
	@ApiModelProperty("新桌台所属区域名称")
	private String areaName;

	@ApiModelProperty("新桌台所属区域guid")
	private String areaGuid;

	@ApiModelProperty("转到的新桌台guid")
	private String tableGuid;

	@ApiModelProperty("转到的新桌台code")
	private String tableCode;
}
