package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description 预览菜谱方案请求实体
 * @date 2021/6/2 18:03
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "预览菜谱方案请求实体")
public class PreviewPlanReqDTO {

    @NotNull
    @ApiModelProperty(value = "方案guid")
    private String planGuid;

    @NotNull
    @ApiModelProperty(value = "浏览模式 1全部 2最新编辑 ")
    private Integer browseType;

    @ApiModelProperty("企业guid,头部不传这里必传")
    private String enterpriseGuid;
}
