package com.holderzone.saas.store.dto.kds.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.saas.store.dto.kds.req.KdsPrintDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 打印记录DTO
 *
 * <AUTHOR>
 * @date 2018/10/09 9:55
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(description = "打印记录实体")
public class KdsPrintRecordDTO implements Serializable {

    @ApiModelProperty(value = "订单号")
    private String recordUid;

    @ApiModelProperty(value = "打印记录GUID")
    private String recordGuid;

    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "生成该打印记录的设备ID")
    private String deviceId;

    @JsonProperty(value = "typeCode")
    @ApiModelProperty(value = "打印业务类型:1/点菜单;2/预接单;3/结账单;4/储值单;5/菜品清单;6/退菜单;7/标签单;88/外卖单;99/交接单")
    private Integer invoiceType;

    @ApiModelProperty(value = "打印机GUID")
    private String printerGuid;

    @ApiModelProperty(value = "打印状态:0/打印中;1/打印成功;2/失败")
    private Integer printStatus;

    @ApiModelProperty(value = "打印原始请求实体")
    private KdsPrintDTO printContent;

    @ApiModelProperty(value = "创建记录员工GUID")
    private String createStaffGuid;

    @JsonProperty(value = "createTime")
    private LocalDateTime gmtCreate;

    @JsonProperty(value = "updateTime")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "号牌名称")
    private String markName;

    @ApiModelProperty(value = "号牌数字")
    private String markNo;

    @JsonProperty(value = "ip")
    @ApiModelProperty(value = "打印机ip")
    private String printerIp;

    @JsonProperty(value = "printName")
    @ApiModelProperty(value = "打印机名称")
    private String printerName;
}
