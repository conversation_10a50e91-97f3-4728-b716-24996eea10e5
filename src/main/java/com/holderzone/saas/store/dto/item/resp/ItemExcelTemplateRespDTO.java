package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemExcelTemplateRespDTO
 * @date 2019/1/28 14:55
 * @description 门店商品导出模板
 * @program holder-saas-store-business
 */
@Data
@ApiModel(description = "门店商品导出模板")
public class ItemExcelTemplateRespDTO {
    @ApiModelProperty("分类名称")
    private String typeName;

    @ApiModelProperty("菜品名称")
    private String itemName;

    @ApiModelProperty("菜品简称")
    private String nameAbbr;

    @ApiModelProperty(value = "商品类型", notes = "商品类型:1.套餐（不称重，无规格），2.规格商品（单商品，不称重），3.称重商品（单商品，称重），4.单品")
    private String itemType;

    @ApiModelProperty("规格名称")
    private String skuName;

    @ApiModelProperty("SKU")
    private String sku;

    @ApiModelProperty("SKU简码")
    private String skuCode;

    @ApiModelProperty("计价单位")
    private String unit;

    @ApiModelProperty(value = "起卖数", notes = "起卖数(非称重即为整数，如个，称重即为小数，如KG)")
    private BigDecimal minOrderNum;

    @ApiModelProperty("销售价")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "是否加入整单折扣", notes = "是否加入整单折扣(0：否，1：是)")
    private String isWholeDiscount;

    @ApiModelProperty("商品图片地址")
    private  String pictureUrl;

}
