package com.holderzone.saas.store.dto.store.area;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AreaDO
 * @date 2018/07/23 下午12:52
 * @description //TODO
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AreaUpdateDTO implements Serializable {

    private static final long serialVersionUID = 3055165143532107036L;

    /**
     * 区域guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "区域guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "区域guid", required = true)
    private String areaGuid;

    /**
     * 区域名称
     */
    @NotNull
    @Size(min = 1, max = 45, message = "区域名称不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "区域名称", required = true)
    private String name;
}
