package com.holderzone.saas.store.dto.business.manage.sync;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel(value = "附加费同步接口")
public class SurchargeAggDTO {

    @ApiModelProperty(value = "附加费列表")
    private List<SurchargeRawDTO> surchargeList;

    @ApiModelProperty(value = "附加费区域列表")
    private List<SurchargeAreaRawDTO> surchargeAreaList;

    public static SurchargeAggDTO empty() {
        SurchargeAggDTO surchargeAggDTO = new SurchargeAggDTO();
        surchargeAggDTO.setSurchargeList(Collections.emptyList());
        surchargeAggDTO.setSurchargeAreaList(Collections.emptyList());
        return surchargeAggDTO;
    }

    public static SurchargeAggDTO full(List<SurchargeRawDTO> surchargeList, List<SurchargeAreaRawDTO> surchargeAreaList) {
        SurchargeAggDTO surchargeAggDTO = new SurchargeAggDTO();
        surchargeAggDTO.setSurchargeList(surchargeList);
        surchargeAggDTO.setSurchargeAreaList(surchargeAreaList);
        return surchargeAggDTO;
    }
}
