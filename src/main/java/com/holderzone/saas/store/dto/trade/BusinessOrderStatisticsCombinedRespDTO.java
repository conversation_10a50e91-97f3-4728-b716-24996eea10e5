package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 商户后台/数据报表/订单统计 列表查询结果DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
@Data
public class BusinessOrderStatisticsCombinedRespDTO {

    @ApiModelProperty(value = "订单数")
    private String orderCount;

    @ApiModelProperty(value = "金额合计")
    private String aggregateAmount;

}
