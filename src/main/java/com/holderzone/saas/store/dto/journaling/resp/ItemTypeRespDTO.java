package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("商品分类")
public class ItemTypeRespDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    private Integer isDelete;

    private String guid;

    /**
     * 品牌GUID
     */
    private String brandGuid;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否启用（0：否，1：是）
     */
    private Integer isEnable;

    /**
     * 分类描述
     */
    private String description;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 图标
     */
    private String iconUrl;

    /**
     * 关联该分类的商品数
     */
    private Integer itemNum;

    /**
     * 是否因重复而改名：0：否，1：是
     */
    private Integer nameChange;

    /**
     * 分类来源（0：门店自己创建的分类，1：品牌自己创建的分类,2:被推送过来的分类）
     */
    private Integer typeFrom;

}
