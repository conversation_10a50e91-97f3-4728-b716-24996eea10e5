package com.holderzone.saas.store.dto.item.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintItemTypeDTO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class PrintItemTypeDTO extends ItemLogDTO {

    @ApiModelProperty(value = "查询关键字")
    private String keywords;

    @ApiModelProperty(value = "菜谱方案Guid")
    private String planGuid;

    @ApiModelProperty(value = "商品guidList")
    private List<String> itemList;

    @ApiModelProperty(value = "查询模式， 默认null：非团餐查询  1：团餐查询")
    private Integer model;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "商品编号(小程序端需要此值用来做item的模糊查询)")
    private String code;

    @ApiModelProperty(value = "搜索条件，code或则商品名称")
    private String searchKey;

    @ApiModelProperty(value = "是否上架：-1:全部;0：否;1:是")
    private Integer isRack;

    @ApiModelProperty(value = "分类GUID;若查询全部分类，则传空字符串")
    private List<String> typeGuid;
}
