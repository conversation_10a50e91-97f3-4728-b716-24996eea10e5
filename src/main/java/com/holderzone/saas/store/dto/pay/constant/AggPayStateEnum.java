package com.holderzone.saas.store.dto.pay.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayState
 * @date 2019/03/14 10:42
 * @description 支付请求订单状态
 * @program holder-saas-store-dto
 */
public enum AggPayStateEnum {

    /**
     * 聚合支付待支付
     */
    READY("10", "待支付"),

    /**
     * 富民支付中，代表失败（暂时，银行问题）
     */
    FMREADY("0", "富民支付中"),

    /**
     * 支付中(已请求上游)
     */
    PENDING("1", "支付中(已请求上游)"),

    /**
     * 支付成功
     */
    SUCCESS("2", "支付成功"),

    /**
     * 支付失败
     */
    FAILURE("3", "支付失败"),

    /**
     * 退款
     */
    REFUNDED("4", "退款"),

    /**
     * 已关闭
     */
    CLOSED("5", "已关闭"),

    /**
     * 撤销
     */
    CANCELED("6", "撤销");

    private String id;
    private String desc;

    AggPayStateEnum(String id, String desc) {
        this.id = id;
        this.desc = desc;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 是否支付流程走完
     * @return
     */
    public static boolean isFinish(String code){
        return code.equals(SUCCESS.getId()) || code.equals(FAILURE.getId()) || code.equals(CLOSED.getId());
    }
}
