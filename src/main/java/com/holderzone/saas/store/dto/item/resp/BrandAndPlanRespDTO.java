package com.holderzone.saas.store.dto.item.resp;

import com.holderzone.saas.store.dto.organization.BrandDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description 返回实体
 * @date 2021/3/25 12:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class BrandAndPlanRespDTO {

    @ApiModelProperty(value = "品牌实体")
    private BrandDTO brandDTO;

    @ApiModelProperty(value = "菜谱方案List")
    private List<PricePlanRespDTO> planDTOList;

}
