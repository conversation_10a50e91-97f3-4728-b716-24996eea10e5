package com.holderzone.saas.store.dto.print;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 查询指定订单已绑定标签商品请求
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ListPrintItemReq {
    @NotNull(message = "订单guid不能为空")
    private String orderGuid;

    @NotNull(message = "门店guid不能为空")
    private String storeGuid;

    @NotNull(message = "设备Id不能为空")
    private String deviceId;
}
