package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description Pad点餐商品列表返回实体
 * @date 2021/7/29 10:37
 */
@Data
@ApiModel(value = "Pad点餐商品列表返回实体")
public class PadTypeItemRespDTO {

    /**
     * 分类集合
     */
    @ApiModelProperty(value = "可能为空集合，分类集合")
    private List<PadTypeRespDTO> typeList;

}
