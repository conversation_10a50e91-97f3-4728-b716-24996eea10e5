package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/17
 * @description
 */
@Data
@ApiModel(value = "查询出入库单详情列表")
@EqualsAndHashCode(callSuper = false)
public class MaterialBySupplierQueryDTO implements Serializable {

    private static final long serialVersionUID = -4187817696591664187L;

    @ApiModelProperty("供应商Guid")
    private String supplierGuid;

    @ApiModelProperty("提交状态(0:未提交，1：已提交)")
    private String status;

    @ApiModelProperty("出入库类型(0:入库，1：出库)")
    private String inOutType;

}
