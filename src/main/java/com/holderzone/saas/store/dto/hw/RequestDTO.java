package com.holderzone.saas.store.dto.hw;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RequestDTO
 * @date 2019/05/24 11:53
 * @description //TODO
 * @program holder-saas-store-hw
 */
@Data
@ApiModel(description = "请求类")
public class RequestDTO<T> {

    @Valid
    @NotNull(message = "业务实体不得为空")
    @ApiModelProperty(value = "业务实体", required = true)
    private T params;
}