package com.holderzone.saas.store.dto.deposit.req;

import com.holderzone.saas.store.dto.deposit.resp.GoodsRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class DepositGetReqDTO implements Serializable {

    private static final long serialVersionUID = -2773042054434843563L;

    @ApiModelProperty(value = "寄存记录Guid")
    @NotNull(message = "寄存记录Guid不得为空")
    private String depositGuid;

    @ApiModelProperty(value = "用户Guid")
    @NotNull(message = "用户guid不得为空")
    private String userGuid;

    @ApiModelProperty(value = "商品列表")
    @NotNull(message = "商品列表不得为空")
    private List<GoodsRespDTO> goodsList;

    @ApiModelProperty(value = "备注")
    private String remark;

}