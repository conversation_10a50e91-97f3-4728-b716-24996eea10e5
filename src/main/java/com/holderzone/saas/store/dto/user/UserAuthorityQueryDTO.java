package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Accessors(chain = true)
@ApiModel("用户权限查询请求实体")
public class UserAuthorityQueryDTO implements Serializable {

    private static final long serialVersionUID = -5609254499610900868L;

    @ApiModelProperty(value = "被授权人guid")
    private String userGuid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public UserAuthorityQueryDTO(String userGuid, String storeGuid) {
        this.userGuid = userGuid;
        this.storeGuid = storeGuid;
    }

    public UserAuthorityQueryDTO(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public UserAuthorityQueryDTO() {

    }
}
