package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SurchargeDeleteDTO implements Serializable {

    private static final long serialVersionUID = -1449515380764802512L;

    @NotBlank(message = "附加费Guid不得为空")
    @ApiModelProperty(value = "附加费Guid", required = true)
    private String surchargeGuid;
}
