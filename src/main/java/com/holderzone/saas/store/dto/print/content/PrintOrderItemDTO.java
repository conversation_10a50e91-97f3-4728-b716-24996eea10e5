package com.holderzone.saas.store.dto.print.content;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.dto.print.content.base.TradeModeAware;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import static com.holderzone.saas.store.dto.print.util.PrintMockUtils.*;

/**
 * 点菜单
 *
 * <AUTHOR>
 * @date 2018/09/19 15:44
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "点菜单")
public class PrintOrderItemDTO extends PrintRefundItemDTO implements TradeModeAware, PrintDataMockito {

    @Nullable
    @ApiModelProperty(value = "预计送达时间(外卖)", notes = "仅外卖使用")
    private String estimateDeliveredTimeString;

    @Nullable
    @ApiModelProperty(value = "整单备注", notes = "整单备注可以为空")
    private String remark;

    @ApiModelProperty(value = "是否自动接单")
    private Boolean isAutoAccept;

    @ApiModelProperty(value = "子单据类型：0=点菜单，1=叫起单，2=催菜单，3=挂起单", notes = "整单备注可以为空")
    private Integer itemInvoiceType = ItemInvoiceTypeEnum.ORDER.getType();

    @ApiModelProperty(value = "出餐码")
    private String foodFinishBarCode;

    @ApiModelProperty(value = "单据类型")
    private String orderTypeName;

    /**
     * 1表示美团拼好饭订单
     */
    private int takeoutBusinessType;

    @Override
    public void applyMock() {
        super.applyMock();
        setEstimateDeliveredTimeString("立即送达");
        setRemark(mockOrderRemark());
        setMarkNo(mockMarkNo());
        setOrderNo(mockOrderNo());
        setFoodFinishBarCode(mockFoodFinishBarCode());
        setPersonNumber(mockPersonNumber());
        setOrderTime(mockOpenTableTime());
        setTradeMode(0);
        setOrderTypeName("饿了么外卖");
    }

    public enum ItemInvoiceTypeEnum {

        ORDER(0, "点菜单"),

        CALL_UP(1, "叫起单"),

        REMIND(2, "催菜单"),

        HANG_UP(3, "挂起单"),
        ;

        private Integer type;

        private String name;

        ItemInvoiceTypeEnum(Integer type, String name) {
            this.type = type;
            this.name = name;
        }

        public Integer getType() {
            return type;
        }

        public String getName() {
            return name;
        }

        public static ItemInvoiceTypeEnum ofType(Integer type) {
            for (ItemInvoiceTypeEnum invoiceTypeEnum : ItemInvoiceTypeEnum.values()) {
                if (invoiceTypeEnum.getType().equals(type)) {
                    return invoiceTypeEnum;
                }
            }
            throw new BusinessException("不支持的菜品票据子类型：" + type);
        }

        public static String getNameByType(Integer type) {
            return ofType(type).getName();
        }
    }
}
