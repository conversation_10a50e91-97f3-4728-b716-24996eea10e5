package com.holderzone.saas.store.dto.kds.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.saas.store.dto.kds.ItemComparable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class PrdDstItemDTO implements ItemComparable, Serializable {

    private static final long serialVersionUID = 4223797150524466678L;

    @NotBlank(message = "商品Guid不得为空")
    @ApiModelProperty(value = "商品Guid", required = true)
    private String itemGuid;

    @NotBlank(message = "商品名称不得为空")
    @ApiModelProperty(value = "商品名称", required = true)
    private String itemName;

    @NotBlank(message = "SkuGuid不得为空")
    @ApiModelProperty(value = "SkuGuid", required = true)
    private String skuGuid;

    @Nullable
    @ApiModelProperty(value = "SKU名称")
    private String skuName;

    @NotNull(message = "是否是称重商品不得为空")
    @ApiModelProperty(value = "是否是称重商品", required = true)
    private Boolean isWeight;

    @NotNull(message = "数量不得为空")
    @DecimalMin(value = "0.000", message = "数量不得小于0")
    @ApiModelProperty(value = "份数：称重商品时最多3位小数，非称重商品时为整数", required = true)
    private BigDecimal currentCount;

    @Nullable
    @ApiModelProperty(value = "单位")
    private String skuUnit;

    @Valid
    @Nullable
    @ApiModelProperty(value = "属性组")
    private List<KdsAttrGroupDTO> attrGroup;

    @Nullable
    @ApiModelProperty(value = "商品备注")
    private String itemRemark;

    @Nullable
    @ApiModelProperty(value = "整单备注")
    private String orderRemark;

    @NotBlank(message = "商品属性MD5不得为空")
    @ApiModelProperty(value = "商品属性MD5", required = true)
    private String itemAttrMd5;

    @Nullable
    @ApiModelProperty(value = "当前等待时间")
    private Integer waitTime;

    @Nullable
    @ApiModelProperty(value = "超时时间")
    private Integer timeout;

    @Nullable
    @ApiModelProperty(value = "是否烹饪中")
    private Boolean isCooking;

    @NotNull(message = "已催商品数量不得为空")
    @ApiModelProperty(value = "已催商品数量", required = true)
    private Integer urgedItemNumber;

    @NotNull(message = "挂起商品数量不得为空")
    @ApiModelProperty(value = "挂起商品数量", required = true)
    private Integer hangedItemNumber;

    @Valid
    @Nullable
    @ApiModelProperty(value = "厨房称重菜品")
    private PrdDstItemTableDTO weightKitchenItem;

    @Valid
    @Nullable
    @ApiModelProperty(value = "厨房菜品列表")
    private List<PrdDstItemTableDTO> kitchenItemList;

    /******************排序字段如下******************/

    @JsonIgnore
    @ApiModelProperty(value = "催菜时间")
    private LocalDateTime urgedTime;

    @JsonIgnore
    @ApiModelProperty(value = "叫起时间")
    private LocalDateTime callUpTime;

    @JsonIgnore
    @ApiModelProperty(value = "准备时间")
    private LocalDateTime prepareTime;

    @JsonIgnore
    @ApiModelProperty(value = "挂起时间")
    private LocalDateTime hangUpTime;

    @JsonIgnore
    @ApiModelProperty(value = "拆分后的index")
    private Integer splitIndex;

    /**************铺平字段如下，仅打印使用**************/

    @ApiModelProperty(value = "屏幕显示类型")
    private Integer displayType;

    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @ApiModelProperty(value = "根据交易模式：桌台名、快餐、饿了么|美团")
    private String orderDesc;

    @ApiModelProperty(value = "根据交易模式：订单号、订单号、订单号")
    private String orderNumber;

    @ApiModelProperty(value = "根据交易模式：桌台名、牌号、外卖日流水号")
    private String orderSerialNo;

    @ApiModelProperty(value = "厨房菜品Guid，仅打印使用")
    private String kitchenItemGuid;

    @ApiModelProperty(value = "区域Guid，仅打印使用")
    private String areaGuid;

    @ApiModelProperty(value = "是否已催单，仅打印使用")
    private Boolean isUrged;

    @ApiModelProperty(value = "是否已挂起，仅打印使用")
    private Boolean isHanged;

    @ApiModelProperty(value = "批次")
    private Integer batch;

    @ApiModelProperty(value = "菜品序号")
    private Integer sort;

    @ApiModelProperty(value = "规则类型 0 显示规则 1菜品汇总")
    private Integer displayRuleType;

    @ApiModelProperty(value = "显示时间")
    private LocalDateTime  displayTime;

    @ApiModelProperty(value = "原商品全称")
    private String originalItemSkuName;

    /**
     * 加菜批次
     * 0表示首次点单，不算加菜
     */
    private Integer addItemBatch;

    /**
     * 首次加菜时间
     */
    private LocalDateTime firstAddItemTime;

    /**
     * 加菜时间
     */
    private LocalDateTime addItemTime;

    @ApiModelProperty(value = "是否加菜")
    private Boolean isAddItem;

    public void copyItemTableInfo(PrdDstItemTableDTO prdDstItemTableDTO) {
        this.displayType = prdDstItemTableDTO.getDisplayType();
        this.orderGuid = prdDstItemTableDTO.getOrderGuid();
        this.orderDesc = prdDstItemTableDTO.getOrderDesc();
        this.orderNumber = prdDstItemTableDTO.getOrderNumber();
        this.orderSerialNo = prdDstItemTableDTO.getOrderSerialNo();
        this.kitchenItemGuid = prdDstItemTableDTO.getKitchenItemGuid();
        this.areaGuid = prdDstItemTableDTO.getAreaGuid();
        this.isUrged = prdDstItemTableDTO.getIsUrged();
        this.isHanged = prdDstItemTableDTO.getIsHanged();
    }

    @Override
    @JsonIgnore
    public LocalDateTime getUrgedTimeForSort() {
        return urgedTime;
    }

    @Override
    public Integer getAddItemTimeForSort() {
        if (null != isAddItem && isAddItem) {
            return 0;
        }
        return 1;
    }

    @Override
    @JsonIgnore
    public LocalDateTime getCallUpTimeForSort() {
        return callUpTime;
    }

    @Override
    @JsonIgnore
    public LocalDateTime getPrepareTimeForSort() {
        return prepareTime;
    }

    @Override
    @JsonIgnore
    public LocalDateTime getHangUpTimeForSort() {
        return hangUpTime;
    }


}