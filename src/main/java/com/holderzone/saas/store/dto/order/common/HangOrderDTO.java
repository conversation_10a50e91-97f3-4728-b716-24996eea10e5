package com.holderzone.saas.store.dto.order.common;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HangOrderDTO
 * @date 2018/10/17 10:08
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class HangOrderDTO extends BaseDTO{

    @ApiModelProperty(value = "挂单的key，逗号分隔")
    private String hangOrderKey;

    @ApiModelProperty(value = "storeGuid")
    private String storeGuid;

    @ApiModelProperty(value = "挂起订单")
    private String order;
}
