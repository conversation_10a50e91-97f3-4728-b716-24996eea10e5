package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class ItemUrgeReqDTO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    @NotEmpty(message = "订单商品Guid列表不得为空")
    @ApiModelProperty(value = "订单商品Guid列表")
    private List<String> orderItemGuidList;
}
