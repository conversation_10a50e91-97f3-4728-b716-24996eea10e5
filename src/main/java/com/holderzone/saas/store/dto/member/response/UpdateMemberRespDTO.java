package com.holderzone.saas.store.dto.member.response;

import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UpdateMemberRespDTO
 * @date 2018/10/29 17:07
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class UpdateMemberRespDTO extends BaseMemberDTO {
    @ApiModelProperty(value = "名字")

    private String name;

    @ApiModelProperty(value = "性别 0：女，1:男，2：保密")

    private Byte sex;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @ApiModelProperty(value = "生日")

    private LocalDate birthday;
}
