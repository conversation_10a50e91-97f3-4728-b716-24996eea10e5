package com.holderzone.saas.store.dto.print.template.printable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 标签单 条形码(Code128编码)
 * 输出完成后自动换新行
 *
 * <AUTHOR>
 * @date 2018/12/15 14:58
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class LabelBarCode implements Serializable {

    private static final long serialVersionUID = -4844434663928737450L;

    @ApiModelProperty(value = "条码内容")
    private String content;

    @ApiModelProperty(value = "条码高度(像素)")
    private int height = 90;

    @ApiModelProperty(value = "上下文间距(像素)")
    private int left = 50;

    public LabelBarCode(String content) {
        this.content = content;
    }
}
