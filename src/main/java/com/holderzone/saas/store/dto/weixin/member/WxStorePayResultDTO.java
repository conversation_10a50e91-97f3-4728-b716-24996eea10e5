package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@Api("支付结果")
public class WxStorePayResultDTO {

	@ApiModelProperty("0：成功，1：订单已经处理,2:密码错误")
	private Integer result;

	@ApiModelProperty("失败原因")
	private String errorMsg;

}
