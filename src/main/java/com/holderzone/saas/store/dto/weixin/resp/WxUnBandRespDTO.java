package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxUnBandRespEnum
 * @date 2019/03/07 16:21
 * @description 微信公众号解绑返回参数
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("微信公众号解绑返回参数")
public class WxUnBandRespDTO {

    @ApiModelProperty("返回code（0为成功，其他均为失败）")
    private Integer respCode;

    @ApiModelProperty("返回描述")
    private String respMessage;
}
