package com.holderzone.saas.store.dto.user.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.util.Objects;

@Accessors(chain = true)
public class PermissionsRespDTO {

    @ApiModelProperty("终端code")
    private String terminalCode;

    @ApiModelProperty("终端名称")
    private String terminalName;

    @ApiModelProperty("资源名称")
    private String sourceName;

    @ApiModelProperty("资源code")
    private String sourceCode;

    @ApiModelProperty("资源url")
    private String sourceUrl;

    @ApiModelProperty("用户guid")
    private String userGuid;

    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("授权人guid")
    private String authorizerGuid;

    @ApiModelProperty("权限code")
    private String authorityCode;

    @ApiModelProperty("权限来源：0，自己创建；1，来自角色列表")
    private Integer sourceFrom;

    @ApiModelProperty("授权人账户")
    private String authorizerAccount;

    @ApiModelProperty("是否有任何用户勾选该权限")
    private Boolean anyoneAuthorized;

    public String getAuthorizerGuid() {
        return authorizerGuid;
    }

    public void setAuthorizerGuid(String authorizerGuid) {
        this.authorizerGuid = authorizerGuid;
    }

    public String getAuthorizerAccount() {
        return authorizerAccount;
    }

    public void setAuthorizerAccount(String authorizerAccount) {
        this.authorizerAccount = authorizerAccount;
    }

    public Integer getSourceFrom() {
        return sourceFrom;
    }

    public void setSourceFrom(Integer sourceFrom) {
        this.sourceFrom = sourceFrom;
    }

    public PermissionsRespDTO() {
    }

    public String getTerminalCode() {
        return terminalCode;
    }

    public void setTerminalCode(String terminalCode) {
        this.terminalCode = terminalCode;
    }

    public String getTerminalName() {
        return terminalName;
    }

    public void setTerminalName(String terminalName) {
        this.terminalName = terminalName;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getSourceUrl() {
        return sourceUrl;
    }

    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public String getAuthorityCode() {
        return authorityCode;
    }

    public void setAuthorityCode(String authorityCode) {
        this.authorityCode = authorityCode;
    }

    public Boolean getAnyoneAuthorized() {
        return anyoneAuthorized;
    }

    public void setAnyoneAuthorized(Boolean anyoneAuthorized) {
        this.anyoneAuthorized = anyoneAuthorized;
    }

    @Override
    public String toString() {
        return "PermissionsRespDTO{" +
                "terminalCode='" + terminalCode + '\'' +
                ", terminalName='" + terminalName + '\'' +
                ", sourceName='" + sourceName + '\'' +
                ", sourceCode='" + sourceCode + '\'' +
                ", sourceUrl='" + sourceUrl + '\'' +
                ", userGuid='" + userGuid + '\'' +
                ", storeGuid='" + storeGuid + '\'' +
                ", authorityCode='" + authorityCode + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PermissionsRespDTO)) return false;
        PermissionsRespDTO that = (PermissionsRespDTO) o;
        return Objects.equals(getTerminalCode(), that.getTerminalCode()) && Objects.equals(getTerminalName(), that.getTerminalName()) && Objects.equals(getSourceName(), that.getSourceName()) && Objects.equals(getSourceCode(), that.getSourceCode()) && Objects.equals(getSourceUrl(), that.getSourceUrl()) && Objects.equals(getUserGuid(), that.getUserGuid()) && Objects.equals(getStoreGuid(), that.getStoreGuid()) && Objects.equals(getAuthorizerGuid(), that.getAuthorizerGuid()) && Objects.equals(getAuthorityCode(), that.getAuthorityCode()) && Objects.equals(getSourceFrom(), that.getSourceFrom()) && Objects.equals(getAuthorizerAccount(), that.getAuthorizerAccount())
                && Objects.equals(getAnyoneAuthorized(), that.getAnyoneAuthorized());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getTerminalCode(), getTerminalName(), getSourceName(), getSourceCode(), getSourceUrl(), getUserGuid(), getStoreGuid(), getAuthorizerGuid(), getAuthorityCode(), getSourceFrom(), getAuthorizerAccount(), getAnyoneAuthorized());
    }
}
