package com.holderzone.saas.store.dto.trade.resp.adjust;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 调整单单实体
 * @date 2022/1/21 20:22
 * @className: AdjustOrderRespDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "调整单单实体")
public class AdjustOrderRespDTO implements Serializable {

    private static final long serialVersionUID = -4944247925226739524L;

    @ApiModelProperty("调整单id")
    private Long id;

    @ApiModelProperty("调整单guid")
    private Long guid;

    /**
     * 调整单号
     */
    @ApiModelProperty("调整单号")
    private String adjustNo;

    /**
     * 调整时间
     */
    @ApiModelProperty("调整时间")
    private LocalDateTime gmtCreate;

    /**
     * 调整商品数量
     */
    @ApiModelProperty("调整商品数量")
    private Integer adjustCount;

    /**
     * 调整金额
     */
    @ApiModelProperty("调整金额")
    private BigDecimal adjustPrice;

    /**
     * 订单号(前端显示用，门店内唯一，格式************)
     */
    @ApiModelProperty("订单号")
    private String orderNo;

    /**
     * 交易模式(0：正餐，1：快餐，3：外卖)
     */
    @ApiModelProperty("交易模式(0：正餐，1：快餐，3：外卖)")
    private Integer tradeMode;

    /**
     * 操作人姓名
     */
    @ApiModelProperty("操作人姓名")
    private String createStaffName;
}
