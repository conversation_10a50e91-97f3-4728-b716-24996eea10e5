package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentReportRespDTO
 * @date 2019/06/03 10:24
 * @description 支付统计响应DTO
 * @program holder-saas-store
 */
@ApiModel(value = "支付方式统计DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PaymentReportRespDTO implements Serializable {

    private static final long serialVersionUID = 6142612267820006238L;

    @ApiModelProperty(value = "支付方式名字")
    private String payWayName;

    @ApiModelProperty(value = "该支付方式合计金额")
    private BigDecimal payFee;

    @ApiModelProperty(value = "退货金额")
    private BigDecimal refundFee;

    @ApiModelProperty(value = "实收金额 = payFee - refundFee")
    private BigDecimal actuallyFee;

}
