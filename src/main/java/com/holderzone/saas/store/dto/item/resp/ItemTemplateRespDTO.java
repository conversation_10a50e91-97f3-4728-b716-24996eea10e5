package com.holderzone.saas.store.dto.item.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateRespDTO
 * @date 2019/05/30 10:48
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@ApiModel(value = "模板信息")
@Data
public class ItemTemplateRespDTO {

    @ApiModelProperty(value = "商品模板guid")
    private String guid;
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;
    @ApiModelProperty(value = "模板名称")
    private String templateName;
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime effectiveStartTime;
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime effectiveEndTime;
    @ApiModelProperty(value = "模板当前状态 1：已结束 2：未开始  3：进行中" )
    private Integer currentStatus;
    @ApiModelProperty(value = "模板激活状态 1：激活 2：冻结")
    private Integer isItActivated;
    @ApiModelProperty(value = "周期模式 1：按时段  2：按星期 ")
    private Integer periodicMode;
    @ApiModelProperty(value = "详细描述")
    private String description;
    @ApiModelProperty(value = "是否逻辑删除 0：否  1：是")
    private Integer isDelete;


}
