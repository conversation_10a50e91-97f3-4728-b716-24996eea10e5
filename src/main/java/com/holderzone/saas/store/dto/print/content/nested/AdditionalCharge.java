package com.holderzone.saas.store.dto.print.content.nested;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 附加费
 *
 * <AUTHOR>
 * @date 2018/09/19 13:42
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "附加费")
public class AdditionalCharge implements Serializable {

    private static final long serialVersionUID = -2459653144406000497L;

    @NotBlank(message = "附加费名称不能为空")
    @ApiModelProperty(value = "附加费名称")
    private String chargeName;

    @NotNull(message = "附加费金额不能为空")
    @ApiModelProperty(value = "附加费金额")
    private BigDecimal chargeValue;

    // @NotNull(message = "附加费单价不能为空")
    @ApiModelProperty(value = "附加费单价")
    private BigDecimal unitValue;

    @ApiModelProperty(value = "数量")
    private BigDecimal count;

    public AdditionalCharge(String chargeName, BigDecimal chargeValue, BigDecimal unitValue) {
        this.chargeName = chargeName;
        this.chargeValue = chargeValue;
        this.unitValue = unitValue;
    }


}
