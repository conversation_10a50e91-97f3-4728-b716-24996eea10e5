package com.holderzone.saas.store.dto.terminal;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className TerminalDTO
 * @date 18-9-13 下午3:29
 * @description
 * @program holder-saas-store-dto
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TerminalInfoDTO {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty(value = "终端名称")
    private String terminalName;

    @ApiModelProperty("终端guid")
    private String terminalGuid;

    @ApiModelProperty(value = "终端类型",notes = "0/app 1/web; 前端勾选动态布局就传1")
    private String type;
}
