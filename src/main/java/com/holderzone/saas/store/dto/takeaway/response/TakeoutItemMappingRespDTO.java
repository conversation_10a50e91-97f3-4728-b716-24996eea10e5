package com.holderzone.saas.store.dto.takeaway.response;

import com.holderzone.saas.store.dto.takeaway.ErpMappingType;
import com.holderzone.saas.store.dto.takeaway.UnMappedType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
@NoArgsConstructor
@Accessors(chain = true)
public class TakeoutItemMappingRespDTO implements Serializable {

    private static final long serialVersionUID = -721251260893883904L;

    @ApiModelProperty("平台方分类列表")
    private List<UnMappedType> unItemTypeList;

    @ApiModelProperty("ERP方分类列表")
    private List<ErpMappingType> erpItemTypeList;

    @ApiModelProperty("未关联商品数")
    private Long unBindCount;

    @ApiModelProperty("已关联商品在门店当前菜谱中不存在商品数")
    private Long invalidBindCount;

    /**
     * 销售模式: 1 普通模式 2 菜谱方案
     */
    @ApiModelProperty(value = "销售模式: 1 普通模式 2 菜谱方案")
    private Integer salesModel;

}
