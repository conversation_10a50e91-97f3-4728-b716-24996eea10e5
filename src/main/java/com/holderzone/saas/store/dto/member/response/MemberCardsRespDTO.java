package com.holderzone.saas.store.dto.member.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberCardsRespDTO
 * @date 2018/09/28 13:56
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class MemberCardsRespDTO implements Serializable{
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "开卡时间")
    private LocalDateTime gmtCreate;


    /**
     * 业务主键
     */
    @ApiModelProperty(value = "业务主键")
    private String memberGuid;

    @ApiModelProperty(value = "会员卡号")
    private String num;

   @ApiModelProperty(value = "会员卡类型 0：电子卡，1:实体卡")
    private Byte type;
}
