package com.holderzone.saas.store.dto.business.manage;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className HandoverPayDetailDTO
 * @date 18-10-8 下午2:56
 * @description 营业中心-交班-班次下的支付详情实体
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HandoverPayDetailDTO {
    /**
     * 交接班记录guid
     */
    private String handoverRecordGuid;

    /**
     * 设备id
     */
    private String terminalId;

    /**
     * 支付方式
     */
    private String payType;

    /**
     * 支付方式名称
     */
    private String payTypeName;

    /**
     * 支付金额
     */
    private BigDecimal payMoney;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("修改时间")
    private LocalDateTime gmtModified;
}
