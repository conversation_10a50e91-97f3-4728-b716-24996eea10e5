package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxCouldEditStoreDTO
 * @date 2019/03/11 18:03
 * @description 微信可配置门店DTO
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("微信可配置门店DTO")
public class WxCouldEditStoreDTO {
    @ApiModelProperty("门店guid")
    private String storeGuid;

    @ApiModelProperty("门店名字")
    private String storeName;

    @ApiModelProperty("品牌名字")
    private List<String> brandNames;
}
