package com.holderzone.saas.store.dto.order.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RecoveryOrderReqDTO
 * @date 2018/09/14 16:02
 * @description 反结账订单入参
 * @program holder-saas-store-order
 */
@Data
public class RecoveryOrderReqDTO extends BaseDTO {

//    @ApiModelProperty(value = "反结账新建订单对象")
//    private CreatOrderReqDTO creatOrderReqDTO;

    @ApiModelProperty(value = "原订单guid")
    private String originalOrderGuid;

    @ApiModelProperty(value = "原账单guid")
    private String originalBillGuid;

    @ApiModelProperty(value = "原反结账uuid")
    private String recoveryUuid;

    @ApiModelProperty(value = "反结账原因")
    private String recoveryReason;

}
