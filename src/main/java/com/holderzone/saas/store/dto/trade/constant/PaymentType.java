package com.holderzone.saas.store.dto.trade.constant;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentType
 * @date 2018/08/09 17:49
 * @description
 * @program holder-saas-store-trading-center
 */
public enum PaymentType {

    JD(-3,"京东外卖支付"),

    MEI_TUAN(-2,"美团外卖支付"),

    E_LE_MA(-1,"饿了么外卖支付"),

    CASH_PAY(0, "现金支付"),

    JH_PAY(1, "聚合支付"),

    BANK_CARD_PAY(2, "银行卡支付"),

    MEMBER_CARD_PAY(3, "会员卡支付");

    private Integer id;

    private String name;

    PaymentType(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameById(Integer id) {
        return Arrays.stream(PaymentType.values())
                .filter(paymentType -> Objects.equals(paymentType.id, id))
                .map(paymentType -> paymentType.name)
                .findFirst()
                .orElse(null);
    }

}
