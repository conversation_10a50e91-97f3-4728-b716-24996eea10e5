package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateMenuDetailRespDTO
 * @date 2019/06/03 11:09
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@ApiModel(value = "商品模板-菜单详情返回DTO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ItemTemplateMenuDetailRespDTO {

    /**
     * 菜单guid
     */
    @ApiModelProperty(value = "菜单guid")
    private String  menuGuid;

    /**
     * 是否全时段 1：否 2：是
     */
    @ApiModelProperty(value = "是否全时段 1：否 2：是")
    private Integer  isItFullTime;

    /**
     * 执行时间guid
     */
    @ApiModelProperty(value = "执行时间guid")
    private  String timeGuid;

    /**
     * 菜单执行时间模式 1：时间段 2：星期  3：混合（时间段+星期）
     */
    @ApiModelProperty(value = "菜单执行时间模式 1：时间段 2：星期  3：混合（时间段+星期）")
    private Integer  periodicMode;

    /**
     * 执行星期集合 周日-周六 ：0-6
     */
    @ApiModelProperty(value = "执行星期集合 周日-周六 ：0-6 ")
    private List<Integer> weeks;

    /**
     * 执行时间段集合
     */
    @ApiModelProperty(value = "执行时间段集合")
    private  List<ItemTemplateExecuteTimeSlotRespDTO>  times;

    /**
     * 菜品集合
     */
    @ApiModelProperty(value = "菜单-菜品集合")
    private  List<ItemTemplateMenuSubItemDetailRespDTO> subItems;
}
