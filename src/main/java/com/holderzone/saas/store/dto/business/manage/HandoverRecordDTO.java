package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverRecordDO
 * @date 2018/07/29 上午11:17
 * @description 营业中心-交接班相关实体
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class  HandoverRecordDTO {

    /**
     * 门店guid
     */
    @ApiModelProperty("门店guid")
    private String storeGuid;

    /**
     * 门店名称
     */
    @ApiModelProperty("门店名称")
    private String storeName;

    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private String terminalId;

    /**
     * 交接班记录guid
     */
    @ApiModelProperty("交接班记录guid")
    private String handoverRecordGuid;

    /**
     * 创建人(接班人)guid
     */
    @ApiModelProperty("创建人(接班人)guid")
    private String createGuid;

    /**
     * 创建人(接班人)名字
     */
    @ApiModelProperty("创建人(接班人)名字")
    private String createName;

    /**
     * 当班销售收入（销售收入）
     */
    @ApiModelProperty("当班销售收入（销售收入）")
    private BigDecimal paymentMoney;

    /**
     * 当班储值收入（充值收入）
     */
    @ApiModelProperty("当班储值收入（充值收入）")
    private BigDecimal memberChargeMoney;

    /**
     * 营业额（销售收入+充值收入-会员卡消费金额）
     */
    @ApiModelProperty("营业额（销售收入+充值收入-会员卡消费金额）")
    private BigDecimal businessInComing;

    /**
     * 确认人(交班人)guid
     */
    @ApiModelProperty("确认人(交班人)guid")
    private String confirmUserGuid;

    /**
     * 确认人(交班人)名字
     */
    @ApiModelProperty("确认人(交班人)名字")
    private String confirmUserName;

    /**
     * 交接班状态
     * 0=已接班(未交班)
     * 1=已交班
     */
    @ApiModelProperty("交接班状态：0=已接班(未交班)，1=已交班")
    private Integer status;

    /**
     * 已结账订单数
     */
    @ApiModelProperty("已结账订单数")
    private Integer checkedCount;

    /**
     * 充值订单数
     */
    @ApiModelProperty("充值订单数")
    private Integer chargedCount;

    /**
     * 收银笔数
     */
    @ApiModelProperty("收银笔数")
    private Integer paymentCount;

    /**
     * 现金收银
     */
    @ApiModelProperty("现金收银")
    private BigDecimal collectMoney;

    /**
     * 现金周转
     */
    @ApiModelProperty("现金周转")
    private BigDecimal flowMoney;

    /**
     * 系统结算
     */
    @ApiModelProperty("系统结算")
    private BigDecimal sysBalance;

    /**
     * 差额
     */
    @ApiModelProperty("差额")
    private BigDecimal differenceMoney;

    /**
     * 创建时间（开班时间）
     */
    @ApiModelProperty("创建时间（开班时间）")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间（交班时间）
     */
    @ApiModelProperty("修改时间（交班时间）")
    private LocalDateTime gmtModified;

    /**
     *
     */
    @ApiModelProperty("退货订单数")
    private Integer refundCount;

    /**
     * 退货金额
     */
    @ApiModelProperty("退货金额")
    private BigDecimal refundMoney;

    /**
     * 该班次下的支付详情
     */
    @ApiModelProperty("该班次下的支付详情")
    private List<HandoverPayDetailDTO> payDetailDTOList;

    /**
     * 应上缴现金
     */
    @ApiModelProperty("应上缴现金")
    private BigDecimal handonCash;

    /**
     * 实际上缴现金
     */
    @ApiModelProperty("实上缴现金")
    private BigDecimal realHandonCash;
}
