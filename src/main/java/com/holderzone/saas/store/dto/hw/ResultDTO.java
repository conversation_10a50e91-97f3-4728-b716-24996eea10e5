package com.holderzone.saas.store.dto.hw;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ResultDTO
 * @date 2019/05/16 11:24
 * @description //TODO
 * @program holder-saas-store-hw
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "响应类")
public class ResultDTO<T> {

    @ApiModelProperty(value = "系统状态码：0=成功，1=失败", required = true)
    private String returnCode;

    @ApiModelProperty(value = "业务状态码：详细编码见文档定义", required = true)
    private String resultCode;

    @ApiModelProperty(value = "系统状态描述", required = true)
    private String resultMessage;

    @ApiModelProperty(value = "业务实体：根据业务入参会不一样，后续会输出详细业务对应入参")
    private T params;

    public ResultDTO(String resultCode, String resultMessage) {
        this.returnCode = "0";
        this.resultCode = resultCode;
        this.resultMessage = resultMessage;
    }

    public ResultDTO(String resultCode, String resultMessage, T params) {
        this.resultCode = resultCode;
        this.resultMessage = resultMessage;
        this.params = params;
    }

    public static ResultDTO success(String resultCode, String resultMsg) {
        ResultDTO result = new ResultDTO<>();
        result.setReturnCode("0");
        result.setResultCode(resultCode);
        result.setResultMessage(resultMsg);
        return result;
    }

    public static ResultDTO failure(String resultCode, String resultMsg) {
        ResultDTO result = new ResultDTO<>();
        result.setReturnCode("1");
        result.setResultCode(resultCode);
        result.setResultMessage(resultMsg);
        return result;
    }
}