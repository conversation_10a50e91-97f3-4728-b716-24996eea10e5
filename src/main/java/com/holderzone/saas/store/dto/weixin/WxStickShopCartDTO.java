package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickShopCartDTO
 * @date 2019/03/12 11:48
 * @description 微信桌贴购物车DTO
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("微信桌贴购物车DTO")
public class WxStickShopCartDTO {

    /**
     * 桌贴模板guid
     */
    @ApiModelProperty("桌贴模板guid")
    @NotEmpty(message = "模板guid不能为空")
    private String modelGuid;

    /**
     * 桌贴模板名字
     */
    @ApiModelProperty("桌贴模板名字")
    private String modelName;

    /**
     * 模板缩略图
     */
    @ApiModelProperty("模板缩略图")
    private String previewImg;

    /**
     * 售价
     */
    @ApiModelProperty("售价")
    private BigDecimal price;
}
