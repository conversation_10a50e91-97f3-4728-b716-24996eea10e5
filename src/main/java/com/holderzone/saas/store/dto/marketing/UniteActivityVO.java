package com.holderzone.saas.store.dto.marketing;

import com.holderzone.saas.store.dto.marketing.nth.NthActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import com.holderzone.saas.store.dto.member.pay.RedPacketSettleAccountsVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/4
 * @description 营销活动统一返回
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "营销活动统一返回")
public class UniteActivityVO implements Serializable {

    private static final long serialVersionUID = 5714455061106914939L;

    @ApiModelProperty(value = "限时折扣活动信息")
    private List<LimitSpecialsActivityDetailsVO> limitSpecialsList;

    @ApiModelProperty(value = "随行红包活动信息")
    private List<RedPacketSettleAccountsVO> redPacketList;

    @ApiModelProperty(value = "第N份优惠活动信息")
    private List<NthActivityDetailsVO> nthActivityList;

}
