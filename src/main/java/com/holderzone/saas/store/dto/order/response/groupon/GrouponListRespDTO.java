package com.holderzone.saas.store.dto.order.response.groupon;

import com.holderzone.saas.store.dto.takeaway.response.MtMaitonConsumeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GrouponListRespDTO
 * @date 2019/01/28 17:32
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class GrouponListRespDTO {

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "券码")
    private String code;

    @ApiModelProperty(value = "券类型")
    private Integer couponType;

    @ApiModelProperty(value = "券平台")
    private Integer grouponType;

    @ApiModelProperty(value = "抵扣金额")
    private BigDecimal deductionAmount;

    @ApiModelProperty(value = "关联的第三方活动guid")
    private String activityGuid;

    @ApiModelProperty(value = "顾客实际购买金额")
    private BigDecimal couponBuyPrice;

    /**
     * @see com.holderzone.saas.store.enums.order.GrouponReceiptChannelEnum
     */
    @ApiModelProperty(value = "券码渠道 买单:1004, 团购: 1000")
    private Integer receiptChannel;

    @ApiModelProperty(value = "一键买单消费信息")
    private MtMaitonConsumeDTO maitonConsumeInfo;

}
