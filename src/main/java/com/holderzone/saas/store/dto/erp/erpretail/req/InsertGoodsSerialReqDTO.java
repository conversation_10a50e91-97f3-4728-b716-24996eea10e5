package com.holderzone.saas.store.dto.erp.erpretail.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("插入商品流水请求实体")
public class InsertGoodsSerialReqDTO {

    @ApiModelProperty("商品Guid")
    private String goodsGuid;

    @ApiModelProperty("业务类型")
    private int invoiceType;

    @ApiModelProperty("变化量")
    private BigDecimal changeNum;

    @ApiModelProperty("单位名称")
    private String unitName;

    @ApiModelProperty("关联单号")
    private String invoiceNo;

}
