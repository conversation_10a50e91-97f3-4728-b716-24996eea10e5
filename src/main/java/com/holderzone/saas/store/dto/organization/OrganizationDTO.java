package com.holderzone.saas.store.dto.organization;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className OrganizationDTO
 * @date 19-1-3 下午4:45
 * @description 组织DTO
 * @program holder-saas-store-organization
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@ApiModel("组织机构DTO")
public class OrganizationDTO {

    @NotNull(message = "更新时组织guid不能为空", groups = Update.class)
    @ApiModelProperty(value = "组织guid")
    private String guid;

    @ApiModelProperty(value = "MDM 生成的uuid")
    private String uuid;

    @NotNull(message = "组织名称不能为空")
    @ApiModelProperty(value = "组织名称", required = true)
    private String name;

    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @ApiModelProperty(value = "联系人电话")
    private String contactTel;

    @ApiModelProperty(value = "上级组织id（由企业guid到直属上级的guid组成，逗号隔开）", required = true)
    @NotBlank(message = "上级组织id不能为空")
    private String parentIds;

    @ApiModelProperty(value = "省份code")
    private String provinceCode;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "城市code")
    private String cityCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "区县code")
    private String countyCode;

    @ApiModelProperty(value = "区县名称")
    private String countyName;

    @ApiModelProperty(value = "详细地址")
    private String addressDetail;

    @ApiModelProperty(value = "组织描述")
    private String description;

    @ApiModelProperty(value = "创建人guid")
    private String createUserGuid;

    @ApiModelProperty(value = "修改人guid")
    private String modifiedUserGuid;

    @ApiModelProperty(value = "是否删除（默认为0-未删除）")
    private Boolean isDeleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "子级组织")
    private List<OrganizationDTO> childOrganizationDTOList;

    @ApiModelProperty(value = "关联门店信息列表")
    private List<StoreDTO> storeList;

    @ApiModelProperty(value = "微海供应链id")
    private String weihaiId;

    public interface Update {
    }
}
