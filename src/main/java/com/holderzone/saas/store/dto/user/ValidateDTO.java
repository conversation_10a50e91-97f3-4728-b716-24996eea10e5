package com.holderzone.saas.store.dto.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className ValidateDTO
 * @date 18-9-11 下午3:58
 * @description Android端校验DTO
 * @program holder-saas-store-staff
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ValidateDTO extends BaseDTO {
    private static final long serialVersionUID = 3287283448969119594L;

    @ApiModelProperty("门店编码（7位）")
    private String storeNo;

    /**
     * 设备类型
     * PC服务端- 0
     * PC平板- 1
     * 小店通- 2
     * 一体机- 3
     * POS机- 4
     * 云平板- 5
     * 点菜宝(M1)- 6
     * PV1(带刷卡的点菜宝)- 7
     */
    @ApiModelProperty(value = "PC服务端- 0、PC平板- 1、小店通- 2、一体机- 3、POS机- 4、云平板- 5、" +
            "点菜宝(M1)- 6、PV1(带刷卡的点菜宝)- 7" +
            "\n 备注：登陆验证必传，与baseDTO中的设备类型不同")
    private Integer deviceTypeCode;

    /**
     * 设备与门店是否绑定（0=否，1=是。）
     */
    @ApiModelProperty("设备与门店是否绑定（0=否，1=是。）")
    private Boolean binding;

}
