package com.holderzone.saas.store.dto.weixin.resp;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStickDownloadRespDTO
 * @date 2019/03/19 13:51
 * @description 微信桌贴下载响应DTO
 * @program holder-saas-store
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("微信桌贴下载响应DTO")
public class WxStickDownloadRespDTO {
    private String fileName;

    private byte[] fileByte;

    private String exception;
}
