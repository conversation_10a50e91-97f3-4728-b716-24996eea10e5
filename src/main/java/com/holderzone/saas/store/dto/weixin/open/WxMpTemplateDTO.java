package com.holderzone.saas.store.dto.weixin.open;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/13
 * @description 消息模版
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "消息模版", description = "消息模版")
public class WxMpTemplateDTO implements Serializable {

    private static final long serialVersionUID = 3356153982969712316L;

    /**
     * 短Id
     */
    @ApiModelProperty(value = "短Id")
    private String shortId;

    /**
     * 消息模板对应的微信公众号
     */
    @ApiModelProperty(value = "消息模板对应的微信公众号")
    private String appId;

    /**
     * 消息模板在微信的ID，调用消息模板时必需，与公众号绑定
     */
    @ApiModelProperty(value = "消息模板在微信的ID，调用消息模板时必需，与公众号绑定")
    private String templateId;

    /**
     * 品牌guid
     */
    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    /**
     * 消息模板类型，0：排队消息模板，1：会员余额消费消息模板
     */
    @ApiModelProperty(value = "消息模板类型，0：排队消息模板，1：会员余额消费消息模板")
    private Integer type;

}
