package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemBatchImportReqDTO
 * @date 2019/07/31 13:50
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
@ApiModel(value = "Q3阶段：商品导入优化p2阶段验证保存商品")
public class ItemBatchImportReqDTO {

    @ApiModelProperty(value = "品牌guid 或者 门店guid")
    private String guid;

    @ApiModelProperty(value = "商品集合")
    private List<ItemExcelTemplateReqDTO> itemExcelTemplateReqDTOS;

}
