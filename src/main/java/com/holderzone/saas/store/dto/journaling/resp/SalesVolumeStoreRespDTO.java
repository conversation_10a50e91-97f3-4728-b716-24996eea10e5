package com.holderzone.saas.store.dto.journaling.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 门店商品销量统计 按门店统计
 */
@Data
public class SalesVolumeStoreRespDTO implements Serializable {

    private static final long serialVersionUID = 4196595790424705511L;

    private String storeGuid;

    private String storeName;

    private String brandGuid;

    private String brandName;

    private List<SalesVolumeRespDTO> items;

}
