/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:PreCheckoutFormatDTO.java
 * Date:2019-12-5
 * Author:terry
 */

package com.holderzone.saas.store.dto.print.format;

import com.holderzone.saas.store.dto.print.format.metadata.FormatMetadata;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class PreCheckoutFormatDTO extends FormatDTO {

    @NotNull(message = "门店名称不能为空")
    @ApiModelProperty(value = "门店名称", required = true)
    private FormatMetadata storeName;

    @NotNull(message = "单据类型不能为空")
    @ApiModelProperty(value = "单据类型", required = true)
    private FormatMetadata invoiceName;

    @NotNull(message = "整单备注不能为空")
    @ApiModelProperty(value = "整单备注", required = true)
    private FormatMetadata orderRemark;

    @NotNull(message = "桌位不能为空")
    @ApiModelProperty(value = "桌位", required = true)
    private FormatMetadata markNo;

    @NotNull(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", required = true)
    private FormatMetadata orderNo;

    @NotNull(message = "就餐人数不能为空")
    @ApiModelProperty(value = "就餐人数", required = true)
    private FormatMetadata personNumber;

    @NotNull(message = "开台/下单时间不能为空")
    @ApiModelProperty(value = "开台/下单时间", required = true)
    private FormatMetadata openTableTime;

    @NotNull(message = "商品组件不能为空")
    @ApiModelProperty(value = "商品组件", required = true)
    private FormatMetadata itemLayout;

    @NotNull(message = "商品单价不能为空")
    @ApiModelProperty(value = "商品单价", required = true)
    private FormatMetadata itemPrice;

    @NotNull(message = "商品小计不能为空")
    @ApiModelProperty(value = "商品小计", required = true)
    private FormatMetadata itemTotal;

    @ApiModelProperty(value = "商品折后小计", required = true)
    private FormatMetadata itemDiscountAfterTotal;

    @NotNull(message = "分类小计不能为空")
    @ApiModelProperty(value = "分类小计", required = true)
    private FormatMetadata typeTotal;

    @NotNull(message = "商品属性不能为空")
    @ApiModelProperty(value = "商品属性", required = true)
    private FormatMetadata itemProperty;

    @NotNull(message = "商品备注不能为空")
    @ApiModelProperty(value = "商品备注", required = true)
    private FormatMetadata itemRemark;

    @NotNull(message = "商品总计不能为空")
    @ApiModelProperty(value = "商品总计", required = true)
    private FormatMetadata itemSumTotal;

    @NotNull(message = "附加费明细不能为空")
    @ApiModelProperty(value = "附加费明细", required = true)
    private FormatMetadata additionalCharge;

    @NotNull(message = "附加费合计不能为空")
    @ApiModelProperty(value = "附加费合计", required = true)
    private FormatMetadata additionalChargeTotal;

    @NotNull(message = "优惠明细不能为空")
    @ApiModelProperty(value = "优惠明细", required = true)
    private FormatMetadata reduceRecord;

    @NotNull(message = "优惠合计不能为空")
    @ApiModelProperty(value = "优惠合计", required = true)
    private FormatMetadata reduceRecordTotal;

    @NotNull(message = "应付金额不能为空")
    @ApiModelProperty(value = "应付金额", required = true)
    private FormatMetadata payableMoney;

    @NotNull(message = "操作员不能为空")
    @ApiModelProperty(value = "操作员", required = true)
    private FormatMetadata operator;

    @NotNull(message = "打印时间不能为空")
    @ApiModelProperty(value = "打印时间", required = true)
    private FormatMetadata printTime;

    @NotNull(message = "买单二维码不能为空")
    @ApiModelProperty(value = "买单二维码", required = true)
    private FormatMetadata payQrCode;

    @NotNull(message = "年月日不能为空")
    @ApiModelProperty(value = "年月日", required = true)
    private FormatMetadata yearMonthDay;

    @NotNull(message = "赠送菜品金额")
    @ApiModelProperty(value = "赠送菜品金额", required = true)
    private FormatMetadata reduceDishAmount;

    @Override
    public void applyDefault() {
        super.applyDefault();
        defaultOrderFormatMetadata();
        defaultItemFormatMetadata();
    }

    private void defaultItemFormatMetadata() {
        if (itemLayout == null) {
            itemLayout = new FormatMetadata(1, 2, 0, false);
        }
        if (itemPrice == null) {
            itemPrice = new FormatMetadata(1, 0, false);
        }
        if (itemTotal == null) {
            itemTotal = new FormatMetadata(1, 0, false);
        }
        if (itemDiscountAfterTotal == null) {
            itemDiscountAfterTotal = new FormatMetadata(1, 0, false, false);
        }
        if (typeTotal == null) {
            typeTotal = new FormatMetadata(1, 0, false, false);
        }
        if (itemProperty == null) {
            itemProperty = new FormatMetadata(1, 0, false);
        }
        if (itemRemark == null) {
            itemRemark = new FormatMetadata(1, 0, false);
        }
        if (itemSumTotal == null) {
            itemSumTotal = new FormatMetadata(1, 0, false);
        }
        if (yearMonthDay == null) {
            yearMonthDay = new FormatMetadata(0, 0, false, false);
        }
    }

    private void defaultOrderFormatMetadata() {
        if (storeName == null) {
            storeName = new FormatMetadata(2, 1, false);
        }
        if (invoiceName == null) {
            invoiceName = new FormatMetadata(2, 1, false);
        }
        if (orderRemark == null) {
            orderRemark = new FormatMetadata(1, 1, false, false);
        }
        if (markNo == null) {
            markNo = new FormatMetadata(1, 0, false);
        }
        if (orderNo == null) {
            orderNo = new FormatMetadata(1, 0, false);
        }
        if (personNumber == null) {
            personNumber = new FormatMetadata(1, 2, false);
        }
        if (openTableTime == null) {
            openTableTime = new FormatMetadata(1, 0, false);
        }
        if (additionalCharge == null) {
            additionalCharge = new FormatMetadata(1, 3, false);
        }
        if (additionalChargeTotal == null) {
            additionalChargeTotal = new FormatMetadata(1, 3, false);
        }
        if (reduceRecord == null) {
            reduceRecord = new FormatMetadata(1, 3, false);
        }
        if (reduceRecordTotal == null) {
            reduceRecordTotal = new FormatMetadata(1, 3, false);
        }
        if (reduceDishAmount == null) {
            reduceDishAmount = new FormatMetadata(1, 3, false, true);
        }
        if (payableMoney == null) {
            payableMoney = new FormatMetadata(2, 0, false);
        }
        if (operator == null) {
            operator = new FormatMetadata(1, 0, false);
        }
        if (printTime == null) {
            printTime = new FormatMetadata(1, 2, false);
        }
        if (payQrCode == null) {
            payQrCode = new FormatMetadata(1, 1, false, false);
        }
    }
}
