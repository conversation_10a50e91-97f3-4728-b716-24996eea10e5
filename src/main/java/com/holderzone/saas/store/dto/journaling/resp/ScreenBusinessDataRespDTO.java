package com.holderzone.saas.store.dto.journaling.resp;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenBusinessDataRespDTO
 * @date 2019/08/27 16:50
 * @description 大屏营业统计响应参数
 * @program holder-saas-store
 */
@ApiModel("大屏营业统计响应参数")
@Data
public class ScreenBusinessDataRespDTO extends BusinessDataRespDTO {
    private Integer storeCount;

    private BigDecimal averagePrice;

    public static ScreenBusinessDataRespDTO getInit(ScreenBusinessDataRespDTO screenBusinessDataRespDTO, Integer storeCount) {
        screenBusinessDataRespDTO.setBusinessFee(screenBusinessDataRespDTO.getActuallyPayFee()
                .setScale(0, BigDecimal.ROUND_HALF_UP));
        screenBusinessDataRespDTO.setAveragePrice(Objects.equals(0, screenBusinessDataRespDTO.getOrderCount()) ?
                BigDecimal.ZERO : screenBusinessDataRespDTO.getActuallyPayFee()
                .divide(new BigDecimal(screenBusinessDataRespDTO.getOrderCount()), 2, BigDecimal.ROUND_HALF_UP));
        screenBusinessDataRespDTO.setStoreCount(storeCount);
        return screenBusinessDataRespDTO;
    }
}
