package com.holderzone.saas.store.dto.trade;

import com.holderzone.saas.store.dto.user.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentQuery
 * @date 2018/09/15 15:10
 * @description
 * @program holder-saas-store-dto
 */
@Data
@Builder
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class PaymentQuery extends BaseBillQuery {

    /**
     * 账单业务主键
     */
    @ApiModelProperty(value = "账单业务主键")
    private String billGuid;

    @ApiModelProperty(value = "如果是聚合支付传，支付唯一标识")
    private String payGuid;

    @ApiModelProperty(value = "聚合支付orderGuid")
    private String jhOrderGuid;

    @ApiModelProperty(value = "支付单业务主键")
    private String paymentGuid;


}

