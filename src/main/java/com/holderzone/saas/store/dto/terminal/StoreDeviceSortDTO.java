package com.holderzone.saas.store.dto.terminal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className StoreTerminalSortDTo
 * @date 18-9-6 上午11:35
 * @description 门店-设备排序DTO
 * @program holder-saas-store-dto
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class StoreDeviceSortDTO implements Serializable {

    private static final long serialVersionUID = 8637273725247072856L;

    /**
     * 门店guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    /**
     * 厂商设备编号
     */
    @NotNull
    @Size(min = 1, max = 45, message = "厂商设备编号不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "厂商设备编号", required = true)
    private String deviceNo;

    /**
     * 系统设备编号
     */
    @NotNull
    @ApiModelProperty(value = "系统设备编号", required = true)
    private String deviceGuid;

    /**
     * 设备类型
     * PC服务端- 0
     * PC平板- 1
     * 小店通- 2
     * 一体机- 3
     * POS机- 4
     * 云平板- 5
     * 点菜宝(M1)- 6
     * PV1(带刷卡的点菜宝)- 7
     */
    @NotNull
    @Min(value = 1, message = "设备类型最小为1")
    @ApiModelProperty(value = "PC服务端- 0、PC平板- 1、小店通- 2、一体机- 3、POS机- 4、云平板- 5、" +
            "点菜宝(M1)- 6、PV1(带刷卡的点菜宝)- 7")
    private Integer deviceType;

    /**
     * 设备排序
     */
    @NotNull
    @Min(value = 0, message = "设备排序范围[1-32767]")
    @Max(value = 16777215, message = "设备排序范围[1-32767]")
    @ApiModelProperty(value = "设备排序，范围：1-16777215", required = true)
    private Integer sort;
}
