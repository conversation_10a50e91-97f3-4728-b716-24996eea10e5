package com.holderzone.saas.store.dto.takeaway.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutShopBindRespDTO
 * @date 2018/09/25 9:07
 * @description
 * @program holder-saas-store-takeaway
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class OwnDistributionDTO implements Serializable {

    private static final long serialVersionUID = 6784968432096220916L;

    @ApiModelProperty(value = "配送方式code")
    private int Type;

    @ApiModelProperty(value = "配送方式名称")
    private String TypeName;

}
