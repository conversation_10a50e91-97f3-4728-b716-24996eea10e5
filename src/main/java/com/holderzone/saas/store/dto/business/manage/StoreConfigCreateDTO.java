package com.holderzone.saas.store.dto.business.manage;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreConfigDO
 * @date 2018/07/29 下午4:05
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class StoreConfigCreateDTO implements Serializable {

    private static final long serialVersionUID = 5822616871941581904L;

    /**
     * 门店guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    /**
     * 门店名称
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店名称不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店名称", required = true)
    private String storeName;

    /**
     * 平板下单是否启用密码
     * 0=未启用
     * 1=已启用
     */
    @ApiModelProperty("平板下单是否启用密码：0=未启用，1=已启用")
    private Integer enablePadPwd;

    /**
     * 是否启用划菜
     * 0=未启用
     * 1=已启用
     */
    @ApiModelProperty("是否启用划菜：0=未启用，1=已启用")
    private Integer enableMarkDish;

    /**
     * 是否启用会员价
     * 0=未启用
     * 1=已启用
     */
    @ApiModelProperty("是否启用会员价：0=未启用，1=已启用")
    private Integer enableMemPrice;

    /**
     * 是否启用交接班
     * 0=未启用
     * 1=已启用
     */
    @ApiModelProperty("是否启用交接班：0=未启用，1=已启用")
    private Integer enableHandover;

    /**
     * 微信点餐模式
     * 0=堂食
     * 1=快餐
     */
    @ApiModelProperty("微信点餐模式：0=堂食，1=快餐")
    private Integer wechatOrderMode;

    /**
     * 流水号模式
     * 0=自增
     * 1=随机
     */
    @ApiModelProperty("流水号模式：0=自增，1=随机")
    private Integer serialNumberMode;

    /**
     * 门店营业开始时间（00:00:00-23:59:59）
     */
    @ApiModelProperty("门店营业开始时间（00:00:00-23:59:59）")
    @JsonFormat(pattern = "HH:mm:ss")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime businessStartTime;

    /**
     * 门店营业结束时间（00:00:00-23:59:59）
     */
    @ApiModelProperty("门店营业结束时间（00:00:00-23:59:59）")
    @JsonFormat(pattern = "HH:mm:ss")
    @JsonSerialize(using = LocalTimeSerializer.class)
    @JsonDeserialize(using = LocalTimeDeserializer.class)
    private LocalTime businessEndTime;
}
