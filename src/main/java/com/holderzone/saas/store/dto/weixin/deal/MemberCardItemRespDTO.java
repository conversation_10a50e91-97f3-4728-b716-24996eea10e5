package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("点餐会员卡列表")
@Data
public class MemberCardItemRespDTO {

	private List<MemberCardItemDTO> memberCardItemDTOS;

	@ApiModelProperty(value = "1:表示有选中，0：没有选中")
	private Integer uck;
}
