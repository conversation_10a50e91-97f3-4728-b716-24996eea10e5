package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @className PricingSchemesReqDTO
 * @date 2019-04-27 10:31:52
 * @description
 * @program holder-saas-store-dto
 */
public class PricingReqDTO implements Serializable {

    private static final long serialVersionUID = 7990914773803139903L;
    @ApiModelProperty(value = "供应商guid")
    private String suppliersGuid;
    @ApiModelProperty(value = "物料报价列表")
    private List<PricingSchemesReqDTO> pricingSchemesList;
    @ApiModelProperty(value = "需要删除物料报价信息")
    private List<String> deleteList;

    public String getSuppliersGuid() {
        return suppliersGuid;
    }

    public void setSuppliersGuid(String suppliersGuid) {
        this.suppliersGuid = suppliersGuid;
    }

    public List<PricingSchemesReqDTO> getPricingSchemesList() {
        return pricingSchemesList;
    }

    public void setPricingSchemesList(List<PricingSchemesReqDTO> pricingSchemesList) {
        this.pricingSchemesList = pricingSchemesList;
    }

    public List<String> getDeleteList() {
        return deleteList;
    }

    public void setDeleteList(List<String> deleteList) {
        this.deleteList = deleteList;
    }
}
