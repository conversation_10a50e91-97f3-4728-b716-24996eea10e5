package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("商品套餐分组")
public class ItemInfoSubgroupDTO {

	@ApiModelProperty(value = "guid")
	private String subgroupGuid;
	@ApiModelProperty(value = "分组名称")
	private String name;
	@ApiModelProperty(value = "当前分组中商品的可选择数量（商品可重复被选）：0：顾客不可选，其余值：最大可选商品次数")
	private Integer pickNum;
	@ApiModelProperty(value = "排序",hidden = true)
	private Integer sort;
	@ApiModelProperty(value = "true:当前分组全部售完")
	private Boolean allSoldOut = false;
	private List<ItemInfoSubSkuDTO> subItemSkuList;
}
