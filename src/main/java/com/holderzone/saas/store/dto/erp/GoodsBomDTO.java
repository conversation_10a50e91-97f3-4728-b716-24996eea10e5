package com.holderzone.saas.store.dto.erp;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/04/29 13:57
 */
public class GoodsBomDTO extends BaseDTO {

    @ApiModelProperty("商品GUID")
    @NotEmpty(message = "商品GUID不能为空")
    private String goodsGuid;

    @ApiModelProperty("商品SKU")
    @NotEmpty(message = "商品SKU不能为空")
    private String goodsSku;

    @ApiModelProperty("物料GUID")
    @NotEmpty(message = "物料GUID不能为空")
    private String materialGuid;

    @ApiModelProperty("使用量")
    @NotNull(message = "使用量不能为空")
    private BigDecimal usage;

    @ApiModelProperty("单位")
    @NotEmpty(message = "单位不能为空")
    private String unit;

    @ApiModelProperty("单位名")
    private String unitName;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("物料种类数量")
    private Integer materialCategoryCount;

    @ApiModelProperty("辅助单位")
    private String auxiliaryUnit;

    @ApiModelProperty("辅助单位名")
    private String auxiliaryUnitName;

    public String getGoodsGuid() {
        return goodsGuid;
    }

    public void setGoodsGuid(String goodsGuid) {
        this.goodsGuid = goodsGuid;
    }

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public BigDecimal getUsage() {
        return usage;
    }

    public void setUsage(BigDecimal usage) {
        this.usage = usage;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getGoodsSku() {
        return goodsSku;
    }

    public void setGoodsSku(String goodsSku) {
        this.goodsSku = goodsSku;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public Integer getMaterialCategoryCount() {
        return materialCategoryCount;
    }

    public void setMaterialCategoryCount(Integer materialCategoryCount) {
        this.materialCategoryCount = materialCategoryCount;
    }

    public String getAuxiliaryUnit() {
        return auxiliaryUnit;
    }

    public void setAuxiliaryUnit(String auxiliaryUnit) {
        this.auxiliaryUnit = auxiliaryUnit;
    }

    public String getAuxiliaryUnitName() {
        return auxiliaryUnitName;
    }

    public void setAuxiliaryUnitName(String auxiliaryUnitName) {
        this.auxiliaryUnitName = auxiliaryUnitName;
    }
}
