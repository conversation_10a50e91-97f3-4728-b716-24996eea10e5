package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BatchImportGetItemsReqDTO
 * @date 2019/07/30 10:16
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "批量上传商品前，根据flag 获取商品列表，验证是否存在同名商品")
public class BatchImportGetItemsReqDTO {

    @ApiModelProperty(value = "商品名称集合")
    private List<String> itemNames;

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "状态标识  0：查询门店商品  1：查询品牌商品")
    private Integer flag;
}
