package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description 菜谱方案商品分类返回实体
 * @date 2021/6/1 18:10
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "菜谱方案商品分类返回实体")
public class PricePlanItemTypeRespDTO {

    @ApiModelProperty(value = "分类guid")
    private String typeGuid;

    @ApiModelProperty(value = "分类名")
    private String typeName;

    @ApiModelProperty(value = "菜谱方案商品分类返回实体")
    private List<PricePlanItemRespDTO> planItemRespDTOList;
}
