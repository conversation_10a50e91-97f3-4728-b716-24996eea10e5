package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class DstBindStatusRespDTO implements Serializable {

    private static final long serialVersionUID = -1158994112223699540L;

    @ApiModelProperty(value = "已绑定区域数量")
    private Integer boundAreaCount;

    @ApiModelProperty(value = "快餐是否被绑定")
    private Boolean isSnackBound;

    @ApiModelProperty(value = "外卖是否被绑定")
    private Boolean isTakeoutBound;

    @ApiModelProperty(value = "已绑定商品数量")
    private Integer boundItemCount;
}
