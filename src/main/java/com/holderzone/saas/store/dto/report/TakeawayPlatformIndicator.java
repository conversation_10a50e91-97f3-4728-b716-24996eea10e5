package com.holderzone.saas.store.dto.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2018/12/05 下午 17:42
 * @description
 */
@ApiModel
public class TakeawayPlatformIndicator {


    @ApiModelProperty("美团指标")
    private BigDecimal meituan;

    @ApiModelProperty("ele么指标")
    private BigDecimal ele;

    @ApiModelProperty("合计")
    private BigDecimal total;


    public BigDecimal getMeituan() {
        return meituan;
    }

    public void setMeituan(BigDecimal meituan) {
        this.meituan = meituan;
    }

    public BigDecimal getEle() {
        return ele;
    }

    public void setEle(BigDecimal ele) {
        this.ele = ele;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }
}
