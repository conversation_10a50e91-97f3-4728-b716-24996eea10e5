package com.holderzone.saas.store.dto.report.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/3/6
 * @description 销售明细返回
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "销售明细返回", description = "销售明细返回")
public class SalesDetailRespDTO implements Serializable {

    private static final long serialVersionUID = 7197642519214252742L;

    @ApiModelProperty(value = "商品名称：商品+规格")
    private String itemName;

    @ApiModelProperty(value = "商品分类")
    private String itemTypeName;

    /**
     * 商品类型
     * 1.套餐主项，2.规格，3.称重，4.单品
     */
    @ApiModelProperty(value = "商品类型")
    private String itemType;

    /**
     * 操作
     * 1：点菜， 2：赠菜，3：退菜
     */
    @ApiModelProperty(value = "操作")
    private String operation;

    /**
     * 点餐数量
     * 赠菜数量
     * 退菜数量
     */
    @ApiModelProperty(value = "点餐数量")
    private Double orderNumber;

    @ApiModelProperty(value = "记数单位")
    private String unit;

    @ApiModelProperty(value = "金额")
    private BigDecimal price;

    @ApiModelProperty(value = "属性加价")
    private BigDecimal attrPrice;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "结账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTime;

}
