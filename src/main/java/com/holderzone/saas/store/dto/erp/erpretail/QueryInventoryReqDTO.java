package com.holderzone.saas.store.dto.erp.erpretail;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@ApiModel("查询盘点单相关信息")
public class QueryInventoryReqDTO extends BasePageDTO {

    @ApiModelProperty("盘点单Guid")
    private String inventoryGuid;

    @ApiModelProperty("查询条件，盘点开始日期")
    @Nullable
    private String startDate;

    @ApiModelProperty("查询条件，盘点结束日期")
    private String endDate;

    @ApiModelProperty("查询条件，盘点类型：0:全部，1，日盘，2：周盘，3：月盘")
    private String type;

    @ApiModelProperty("查询条件，盘点单状态：0，已完成，1：已作废")
    private String orderStatus;

    @ApiModelProperty("查询条件，盘点人或盘点单号")
    private String operatorOrOrderNo;
}
