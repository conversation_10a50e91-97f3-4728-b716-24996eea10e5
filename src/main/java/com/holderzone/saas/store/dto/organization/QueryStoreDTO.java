package com.holderzone.saas.store.dto.organization;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className QueryBrandDTO
 * @date 19-1-3 上午10:38
 * @description
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("门店查询DTO，分页查询时页码从1开始")
public class QueryStoreDTO extends StoreParserPageDTO {

    @ApiModelProperty(value = "状态，0-禁用，1-启用 ")
    private Integer isEnable;

    @ApiModelProperty(value = "门店名称")
    private String storeName;


}
