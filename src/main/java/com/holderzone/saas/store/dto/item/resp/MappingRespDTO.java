package com.holderzone.saas.store.dto.item.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MappingDTO
 * @date 2018/09/04 下午4:50
 * @description //美团菜品映射菜品库返回DTO
 * @program holder-saas-store-dto
 */
// todo 序列化字段有问题
@Data
public class MappingRespDTO extends BaseDTO {

    private static final long serialVersionUID = -8042420177112361065L;
    /**
     * skuGUID
     */
    private String eDishSkuCode;

    /**
     * skuCode
     */
    private String skuCode;

    private String eDishSkuName;

    private String eDishSkuUnit;
    /**
     * dishGUID
     */
    private String eDishCode;

    private String eDishName;

    private String pinyin;

    /**
     * 名称
     */
    private String dishNameWithSpec;
    /**
     * 菜品分类名称
     */
    private String categoryName;
    /**
     * 菜品分类GUID
     */
    private String categoryId;

    /**
     * 品牌库对应的SKUGUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的商品，则该字段为原skuGUID。
     */
    private String parentSkuGuid;

    /**
     * 品牌库对应的ITEMGUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的商品，则该字段为原itemGUID。
     */
    private String parentItemGuid;

    @ApiModelProperty("是否上架(0：否，1：是)")
    private Integer isRack;

    /**
     * 当前销售模式下 是否支持可选
     */
    private Integer isChoice;

    @ApiModelProperty("门店商品单价")
    private BigDecimal erpItemPrice;

    @ApiModelProperty(value = "菜谱售卖名称")
    private String planItemName;

    @ApiModelProperty("外卖核算价")
    private BigDecimal takeawayAccountingPrice;

    @ApiModelProperty("堂食核算价")
    private BigDecimal accountingPrice;

    public String getPlanItemName() {
        return planItemName;
    }

    public void setPlanItemName(String planItemName) {
        this.planItemName = planItemName;
    }

    public BigDecimal getErpItemPrice() {
        return erpItemPrice;
    }

    public void setErpItemPrice(BigDecimal erpItemPrice) {
        this.erpItemPrice = erpItemPrice;
    }

    public Integer getIsRack() {
        return isRack;
    }

    public void setIsRack(Integer isRack) {
        this.isRack = isRack;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    @JsonProperty("eDishSkuCode")
    public String geteDishSkuCode() {
        return eDishSkuCode;
    }

    public void seteDishSkuCode(String eDishSkuCode) {
        this.eDishSkuCode = eDishSkuCode;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    @JsonProperty("eDishSkuName")
    public String geteDishSkuName() {
        return eDishSkuName;
    }

    public void seteDishSkuName(String eDishSkuName) {
        this.eDishSkuName = eDishSkuName;
    }

    @JsonProperty("eDishSkuUnit")
    public String geteDishSkuUnit() {
        return eDishSkuUnit;
    }

    public void seteDishSkuUnit(String eDishSkuUnit) {
        this.eDishSkuUnit = eDishSkuUnit;
    }

    @JsonProperty("eDishCode")
    public String geteDishCode() {
        return eDishCode;
    }

    public void seteDishCode(String eDishCode) {
        this.eDishCode = eDishCode;
    }

    @JsonProperty("eDishName")
    public String geteDishName() {
        return eDishName;
    }

    public void seteDishName(String eDishName) {
        this.eDishName = eDishName;
    }

    public String getDishNameWithSpec() {
        return dishNameWithSpec;
    }

    public void setDishNameWithSpec(String dishNameWithSpec) {
        this.dishNameWithSpec = dishNameWithSpec;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getParentSkuGuid() {
        return parentSkuGuid;
    }

    public void setParentSkuGuid(String parentSkuGuid) {
        this.parentSkuGuid = parentSkuGuid;
    }

    public Integer getIsChoice() {
        return isChoice;
    }

    public void setIsChoice(Integer isChoice) {
        this.isChoice = isChoice;
    }
}
