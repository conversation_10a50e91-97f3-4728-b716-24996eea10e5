package com.holderzone.saas.store.dto.order.common;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PushOrderBillsVo
 * @date 2019/11/29 14:45
 * @description //TODO
 * @program IdeaProjects
 */
@Data
public class PushOrderBillsBO {

    /**
     * 销售订单Id
     */
    private String salesOrderId;

    /**
     * 销售订单流水号
     */
    private String serialNumber;

    /**
     * 门店Id
     */
    private String storeId;

    /**
     * 下单时间
     */
    private LocalDateTime createTime;

    /**
     * 客人数
     */
    private Integer guestCount;

    /**
     * 桌台名称(区域+桌台code)
     */
    private String diningTableName;

    /**
     * 消费合计
     */
    private BigDecimal consumeTotal;

    /**
     * 交易模式{0=堂食模式 1=快销 2 外卖}
     */
    private Integer tradeMode;

    /**
     * 外卖平台 美团/饿了么
     */
    private String takeaway;

    private String checkStaffId;

    private String checkOutStaffName;

    private LocalDate businessDay;

    /**
     * 结算金额(应收)
     */
    private BigDecimal checkTotal;

    /**
     * 支付折扣合计
     */
    private BigDecimal paymentDiscountTotal;

    /**
     * 账单附加费用合计
     */
    private BigDecimal additionalFeesTotal;

    /**
     * 反结账
     */
    private Boolean isRecovery;

    private String EnterpriseId;

    private String BranchStoreGuid;

    private String BranchStoreName;

    private List<SalesOrderDisheseBO> salesOrderDisheses;

}