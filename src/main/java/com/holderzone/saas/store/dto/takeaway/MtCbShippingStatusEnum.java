package com.holderzone.saas.store.dto.takeaway;

/**
 * 配送状态
 * <p>
 * https://developer.meituan.com/openapi#7.6
 */
public enum MtCbShippingStatusEnum {

    配送单发往配送("0", "配送单发往配送"),

    等待骑手接单("5", "已经分配骑手，等待骑手接单"),

    骑手已接单("10", "配送单已确认(骑手接单)"),

    骑手已到店("15", "骑手已到店"),

    骑手已取餐("20", "骑手已取餐"),

    骑手已送达("40", "骑手已送达"),

    配送单已取消("100", "配送单已取消");

    private String status;

    private String desc;

    MtCbShippingStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static MtCbShippingStatusEnum ofStatus(String status) {
        for (MtCbShippingStatusEnum value : values()) {
            if (value.status.equalsIgnoreCase(status)) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效的shippingStatus[" + status + "]");
    }
}
