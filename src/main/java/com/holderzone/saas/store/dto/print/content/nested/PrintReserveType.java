package com.holderzone.saas.store.dto.print.content.nested;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintItemRecord
 * @date 2018/07/25 11:13
 * @description 商品相关
 * @program holder-saas-store-print
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@ApiModel(description = "菜品记录")
public class PrintReserveType implements Serializable {

    private static final long serialVersionUID = -2851990564619429954L;

    @Valid
    @Nullable
    @ApiModelProperty(value = "分类guid")
    private String itemTypeGuid;

    @Valid
    @Nullable
    @ApiModelProperty(value = "分类名称")
    private String itemTypeName;

    @Valid
    @Nullable
    @ApiModelProperty(value = "分类数量")
    private Double itemTypeNum;

    @Valid
    @Nullable
    @ApiModelProperty(value = "商品列表，后厨单中商品列表不得为空，其他情况允许为空")
    private List<PrintReserveItem> itemRecordList;

}
