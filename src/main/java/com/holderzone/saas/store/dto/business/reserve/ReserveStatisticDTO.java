package com.holderzone.saas.store.dto.business.reserve;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveStatisticDTO
 * @date 2018/07/31 下午7:46
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReserveStatisticDTO {

    /**
     * 总记录条数
     */
    private Integer totalCount;

    /**
     * 已到达记录条数
     */
    private Integer arrivedCount;

    /**
     * 作废记录条数
     */
    private Integer invalidCount;

    /**
     * 未抵达记录条数
     */
    private Integer notArrivedCount;

    /**
     * 超时记录条数
     */
    private Integer timeoutCount;

    /**
     * 预定记录列表
     */
    private List<ReserveRecordDTO> reserveRecords;

    /**
     * 返回默认空值对象
     *
     * @return
     */
    public static ReserveStatisticDTO empty() {
        ReserveStatisticDTO reserveStatisticDTO = new ReserveStatisticDTO();
        reserveStatisticDTO.setTotalCount(0);
        reserveStatisticDTO.setArrivedCount(0);
        reserveStatisticDTO.setInvalidCount(0);
        reserveStatisticDTO.setNotArrivedCount(0);
        reserveStatisticDTO.setTimeoutCount(0);
        reserveStatisticDTO.setReserveRecords(Collections.emptyList());
        return reserveStatisticDTO;
    }

    /**
     * 返回数据填充对象
     *
     * @param statusCollection
     * @param reserveRecordDTOS
     * @return
     */
    public static ReserveStatisticDTO of(List<Integer> statusCollection, List<ReserveRecordDTO> reserveRecordDTOS) {
        int arrivedCount = 0;
        int invalidCount = 0;
        int notArrivedCount = 0;
        int timeoutCount = 0;
        for (Integer status : statusCollection) {
            switch (status) {
                case 0:
                    notArrivedCount++;
                    break;
                case 1:
                    arrivedCount++;
                    break;
                case 2:
                    timeoutCount++;
                    break;
                case 3:
                    invalidCount++;
                    break;
                default:
                    break;
            }
        }
        ReserveStatisticDTO reserveStatisticDTO = new ReserveStatisticDTO();
        reserveStatisticDTO.setTotalCount(statusCollection.size());
        reserveStatisticDTO.setNotArrivedCount(notArrivedCount);
        reserveStatisticDTO.setArrivedCount(arrivedCount);
        reserveStatisticDTO.setTimeoutCount(timeoutCount);
        reserveStatisticDTO.setInvalidCount(invalidCount);
        reserveStatisticDTO.setReserveRecords(reserveRecordDTOS);
        return reserveStatisticDTO;
    }


}
