package com.holderzone.saas.store.dto.trade;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseBillDTO
 * @date 2018/08/01 16:33
 * @description 基础DTO
 * @program holder-saas-store-dto
 */
@Data
public abstract class BaseBillDTO extends BaseDTO implements Serializable {
    /**
     * 关联账单guid
     */
    @ApiModelProperty(value = "对应账单guid")
    protected String billGuid;

    @ApiModelProperty(value = "对应订单guid")
    protected String orderGuid;

}
