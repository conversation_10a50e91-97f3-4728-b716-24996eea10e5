package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.saas.store.dto.weixin.common.WxStoreInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxOrderConfigDTO
 * @date 2019/02/14 11:07
 * @description 微信点餐门店配置响应DTO
 * @program holder-saas-store-weixin
 */
@Data
@ApiModel(value = "微信点餐门店配置列表响应DTO")
public class WxOrderConfigDTO extends WxStoreInfoDTO {

    @ApiModelProperty("guid")
    private String guid;
    /**
     * 是否开启微信点餐（0关闭，1开启）
     */
    private Integer isOrderOpen;

    /**
     * 点餐模式（0正餐，1快餐）
     */
    @ApiModelProperty(value = "点餐模式（0正餐，1快餐）")
    private Integer orderModel;

    /**
     * 接单模式（0手动接单，1首单手动，加菜自动，2无需确认（仅适用于快餐））
     */
//    @ApiModelProperty(value = "接单模式（0所有订单均需手动确认，1首单需手动确认，加菜无需确认，2无需确认（仅适用于快餐模式））")
    @ApiModelProperty(value = "接单模式（0所有订单均需确认，1首单需确认")
    private Integer takingModel;

    /**
     * 是否开启线上买单（0关闭，1开启（快餐只能选择开启））
     */
    @ApiModelProperty(value = "是否开启线上买单（0关闭，1开启（快餐只能选择开启））")
    private Integer isOnlinePayed;

    @ApiModelProperty(value = "顾客是否可点称重商品（0顾客可点，1顾客不可点）")
    private Integer isWeighingOrdered;

    @ApiModelProperty(value = "是否开启单品备注（0关闭，1开启）")
    private Integer isRemarked;

    @ApiModelProperty(value = "跳转页面类型（0点餐页，1门店主页）")
    private Integer urlType;

    @ApiModelProperty(value = "菜单样式（0大图，1小图）")
    private Integer menuType;

    @ApiModelProperty(value = "标签列表")
    private String tagNames;

    @ApiModelProperty(value = "自动确认（0关闭1开启）")
    private Integer autoConfirm;

    @ApiModelProperty(value = "自动确认时间（分钟，自动确认接单时间）")
    private Long autoConfirmTime;

    @ApiModelProperty(value = "用户下单后每隔多久进行提示（分钟，不填写默认提示一次）")
    private Long confirmPromptTime;

    private Boolean isOnlyState;

    /**
     * pad点餐主题类型(0深色主题,1浅色主题)
     */
    @ApiModelProperty(value = "pad点餐主题类型(0深色主题,1浅色主题)")
    private Integer subjectType;

    /**
     * pad点餐背景图
     */
    @ApiModelProperty(value = "pad点餐背景图")
    private String padBackgroundUrl;


    /**
     * 是否结账不清台
     */
    @ApiModelProperty(value = "是否结账不清台")
    private Integer isCheckoutUnCloseTable;

    @ApiModelProperty(value = "是否游客模式 （0关闭，1开启）")
    private Integer guestFlag;

    /**
     * 支付团购平台
     */
    private List<String> supportGrouponTypes;

    @ApiModelProperty(value = "色系")
    private String styleColor;

    @ApiModelProperty(value = "是否自动带入会员详细地址")
    private Integer autoInMemberInfoFlag;

}
