package com.holderzone.saas.store.dto.takeaway;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel
public class OwnCallbackResponse {

    public static OwnCallbackResponse SUCCESS = new OwnCallbackResponse();

    public static OwnCallbackResponse PARAMETER_ERROR = new OwnCallbackResponse(201, "查询不到该条数据");

    public static OwnCallbackResponse SIGNATURE_ERROR = new OwnCallbackResponse(202, "签名错误");

    public static OwnCallbackResponse DO_ERROR = new OwnCallbackResponse(203, "执行错误");

    public static OwnCallbackResponse GO_SHIPPING_ERROR = new OwnCallbackResponse(204, "商家发起配送错误");

    public static OwnCallbackResponse DONE_SHIPPING_ERROR = new OwnCallbackResponse(205, "商家完成配送错误");

    public static OwnCallbackResponse CANCEL_SHIPPING_ERROR = new OwnCallbackResponse(206, "商家取消配送错误");

    private int code;

    private String message;

    public OwnCallbackResponse() {
        code = 10000;
        message = "ok";
    }

    public OwnCallbackResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
