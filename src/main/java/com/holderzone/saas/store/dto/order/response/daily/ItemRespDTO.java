package com.holderzone.saas.store.dto.order.response.daily;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTypeRespDTO
 * @date 2019/02/12 16:57
 * @description 营业日报 基础条目
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class ItemRespDTO  extends  BaseItemRespDTO {

    @ApiModelProperty(value = "唯一标识")
    private String guid;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "赠送数量")
    private BigDecimal freeNum;

    @ApiModelProperty(value = "实际销售数量 = 收款数量+赠送数量")
    private BigDecimal quantum;

    @ApiModelProperty(value = "金额，收款数量*单价")
    private BigDecimal amount;

    @ApiModelProperty(value = "商品优惠价")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "是否合计项,0否 1是")
    private int isTotal = 0;

    @ApiModelProperty(value = "是否含有子项,0否 1是")
    private int hasSubInfo = 0;

    @ApiModelProperty(value = "子项")
    private List<ItemRespDTO> subs;

    @ApiModelProperty(value = "属性名称")
    private String skuName;

    private BigDecimal dinnerQuantum;

    private BigDecimal dinnerAmount;

    private BigDecimal dinnerDiscountAmount;

    private BigDecimal takeoutQuantum;

    private BigDecimal takeoutAmount;

    private BigDecimal takeoutDiscountAmount;

    private BigDecimal takeoutUnitPrice;

}