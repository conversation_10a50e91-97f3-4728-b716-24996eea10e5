package com.holderzone.saas.store.dto.takeaway;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class MtQueryOrderIdDetail implements Serializable {

    private static final long serialVersionUID = 3449305765331654286L;

    /**
     * 该条记录的偏移量
     */
    private Long offsetId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * ERP门店id
     */
    private String epoiId;
}