package com.holderzone.saas.store.dto.trade;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.enums.DebtPaymentTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR> R
 * @date 2020/12/16 9:36
 * @description
 */
@ApiModel(value = "挂账还款更新请求DTO")
public class DebtUnitRecordUpdateReqDTO extends BaseDTO {
    @ApiModelProperty(value = "单位Guid")
    private String unitGuid;

    @ApiModelProperty(value = "更新数据集合")
    private List<DebtUnitRecordUpdateDTO> updateList;

    /**
     * @see DebtPaymentTypeEnum
     */
    @ApiModelProperty(value = "结算方式（0：现金，1：支付宝，2：微信，3：银行转账，4：支票）")
    private Integer paymentType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "凭证")
    private List<String> evidenceList;

    @ApiModelProperty(value = "还款批次号")
    private String repaymentBatchNumber;

    @ApiModelProperty(value = "会员密码")
    private String memberPassWord;

    @ApiModelProperty(value = "是否需要密码")
    private Boolean needPassword = Boolean.TRUE;

    @ApiModelProperty(value = "会员卡guid")
    private String memberInfoCardGuid;

    @ApiModelProperty(value = "会员guid")
    private String memberInfoGuid;

    @ApiModelProperty(value = "会员电话")
    private String memberPhone;

    @ApiModelProperty(value = "会员名字")
    private String memberName;

    public String getMemberPhone() {
        return memberPhone;
    }

    public void setMemberPhone(String memberPhone) {
        this.memberPhone = memberPhone;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getMemberPassWord() {
        return memberPassWord;
    }

    public void setMemberPassWord(String memberPassWord) {
        this.memberPassWord = memberPassWord;
    }

    public Boolean getNeedPassword() {
        return needPassword;
    }

    public void setNeedPassword(Boolean needPassword) {
        this.needPassword = needPassword;
    }

    public String getMemberInfoCardGuid() {
        return memberInfoCardGuid;
    }

    public void setMemberInfoCardGuid(String memberInfoCardGuid) {
        this.memberInfoCardGuid = memberInfoCardGuid;
    }

    public String getMemberInfoGuid() {
        return memberInfoGuid;
    }

    public void setMemberInfoGuid(String memberInfoGuid) {
        this.memberInfoGuid = memberInfoGuid;
    }

    public String getUnitGuid() {
        return unitGuid;
    }

    public void setUnitGuid(String unitGuid) {
        this.unitGuid = unitGuid;
    }

    public List<DebtUnitRecordUpdateDTO> getUpdateList() {
        return updateList;
    }

    public void setUpdateList(List<DebtUnitRecordUpdateDTO> updateList) {
        this.updateList = updateList;
    }

    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<String> getEvidenceList() {
        return evidenceList;
    }

    public void setEvidenceList(List<String> evidenceList) {
        this.evidenceList = evidenceList;
    }

    public String getRepaymentBatchNumber() {
        return repaymentBatchNumber;
    }

    public void setRepaymentBatchNumber(String repaymentBatchNumber) {
        this.repaymentBatchNumber = repaymentBatchNumber;
    }
}
