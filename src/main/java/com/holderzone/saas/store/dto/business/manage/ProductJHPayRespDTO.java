package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ProductJHPayRespDTO
 * @date 2018/09/17 14:38
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class ProductJHPayRespDTO implements Serializable {

    @ApiModelProperty("响应码，成功=10000")
    private String code;

    @ApiModelProperty("响应信息")
    private String msg;

    @ApiModelProperty("结果")
    private String result;

    @ApiModelProperty("附加数据")
    private String attachData;

}
