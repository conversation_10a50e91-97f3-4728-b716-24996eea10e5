package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxInQueueRespDTO
 * @date 2019/05/20 11:05
 * @description 微信排队响应DTO
 * @program holder-saas-store
 */
@Data
@ApiModel("微信排队响应DTO")
@AllArgsConstructor
@NoArgsConstructor
public class WxInQueueRespDTO extends WxPortalReqDTO {

    @ApiModelProperty("响应code")
    private Integer respCode;

    @ApiModelProperty("响应信息")
    private String respMsg;
}
