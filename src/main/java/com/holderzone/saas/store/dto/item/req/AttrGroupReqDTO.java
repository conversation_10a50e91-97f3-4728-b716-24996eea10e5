package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.item.common.MchntItemBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PropertyGroupReqDTO
 * @date 2019/01/04 下午5:53
 * @description //正餐属性组保存的实体
 * @program holder-saas-store-dto
 */
@Data
public class AttrGroupReqDTO extends MchntItemBaseDTO {

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "属性组名称")
    @NotEmpty
    @Size(max = 20)
    private String name;

    @ApiModelProperty(value = "排序")
    @Max(99999999)
    private Integer sort;

    @ApiModelProperty(value = "描述")
    @Size(max = 50)
    private String description;

    @ApiModelProperty(value = "是否启用；0：否，1：是")
    @NotNull
    private Integer isEnable;
    @ApiModelProperty(value = "是否必选:0 否 1 是")
    @NotNull
    private Integer isRequired;

    @ApiModelProperty(value = "是否支持多选:0 否 1 是")
    @NotNull
    private Integer isMultiChoice;

    @ApiModelProperty(value = "默认选择属性GUID集合")
    private List<String> defaultAttrGuidList;
}
