package com.holderzone.saas.store.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BasePageDTO
 * @date 2018/08/24 上午11:04
 * @description //TODO
 * @program holder-saas-config-center
 */
@Data
@NoArgsConstructor
public class BasePageDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 1799659859507251057L;

    /**
     * 当前页数
     */
    @ApiModelProperty(value = "当前页数")
    private Integer currentPage = 1;
    /**
     * 每页显示条数
     */
    @ApiModelProperty(value = "每页显示条数")
    private Integer pageSize = 20;

    @ApiModelProperty(value = "分页时最大Id")
    private Long maxId;

}
