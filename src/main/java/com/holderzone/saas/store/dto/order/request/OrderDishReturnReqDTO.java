package com.holderzone.saas.store.dto.order.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderDishReturnReqDTO
 * @date 2018/10/16 11:55
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class OrderDishReturnReqDTO extends BaseDTO {

    @ApiModelProperty(value = "orderGuid")
    public String orderGuid;

    @ApiModelProperty(value = "billGuid")
    public String billGuid;

    @ApiModelProperty(value = "orderDishGuid")
    public String orderDishGuid;

    @ApiModelProperty(value = "退菜数量")
    public BigDecimal returnCount;

    @ApiModelProperty(value = "退菜原因")
    public String returnReason;
}
