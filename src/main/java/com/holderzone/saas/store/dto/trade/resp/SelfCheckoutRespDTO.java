package com.holderzone.saas.store.dto.trade.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 自助结账返回实体
 * @date 2021/9/9 15:38
 * @className: SelfCheckoutRespDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "自助结账返回实体")
public class SelfCheckoutRespDTO implements Serializable {

    private static final long serialVersionUID = 5524693598806799470L;

    @ApiModelProperty(value = "订单金额（商品总额+附加费）")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private BigDecimal actuallyPayFee;
}
