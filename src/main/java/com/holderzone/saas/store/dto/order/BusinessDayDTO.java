package com.holderzone.saas.store.dto.order;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @date 2022年11月03日 上午9:47
 * @description 营业日查询/返回实体
 */
@Data
public class BusinessDayDTO implements Serializable {

    private static final long serialVersionUID = 2864204852872622206L;

    private String guid;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 门店营业开始时间
     */
    private String orderNo;

    /**
     * 营业日
     */
    private LocalTime businessStart;

    /**
     * 门店营业结束时间
     */
    private LocalTime businessEnd;


    private LocalDateTime gmtCreate;
}
