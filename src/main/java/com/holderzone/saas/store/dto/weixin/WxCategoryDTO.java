package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxCategoryDTO
 * @date 2019/03/05 15:09
 * @description 微信桌贴分类DTO
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WxCategoryDTO {

    private String storeGuid;

    private String enterpriseGuid;

    private String messageType;

    @ApiModelProperty("唯一标识")
    private String guid;
    @ApiModelProperty("分类名称")
    private String categoryName;
    @ApiModelProperty("排序")
    @NotEmpty
    @Min(value = 1)
    private Integer categorySort;
    @ApiModelProperty("数量")
    private Long count;

    private String isDelete;

}
