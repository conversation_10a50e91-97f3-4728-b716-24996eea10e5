package com.holderzone.saas.store.dto.weixin.member;

import com.holderzone.holder.saas.member.wechat.dto.card.ResponseCardRight;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Api("返回会员卡")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxMemberCardDTO {
	@ApiModelProperty("会员卡二维码")
	private String cardQRCode;
	@ApiModelProperty("卡GUID（包括副卡）")
	private String cardGuid;
	@ApiModelProperty("会员持卡GUID")
	private String memberInfoCardGuid;
	@ApiModelProperty("卡体系GUID")
	private String systemManagementGuid;
	@ApiModelProperty("会员拥有体系GUID")
	private String enterpriseMemberInfoSystemGuid;
	@ApiModelProperty("卡名称")
	private String cardName;
	@ApiModelProperty("卡LOGO,本期无")
	private String cardLogo;
	@ApiModelProperty("卡片图片路径")
	private String cardIcon;
	@ApiModelProperty("卡颜色")
	private String cardColour;
	@ApiModelProperty("有效期类型，0永久有效，2固定时间")
	private Integer validityType=0;
	@ApiModelProperty("开始有效期")
	private String cardStartDate;
	@ApiModelProperty("结束有效期")
	private String cardEndDate;
	@ApiModelProperty("卡类型,0成长型(默认卡),1荣誉型,2付费型,3实体卡")
	private Integer cardType;
	@ApiModelProperty("卡片编号")
	private String systemManagementCardNum;
	@ApiModelProperty("卡余额")
	private BigDecimal cardMoney;
	@ApiModelProperty("卡赠送余额")
	private BigDecimal giftMoney;
	@ApiModelProperty("积分")
	private String cardIntegral;
	@ApiModelProperty("卡成长值")
	private String cardGrowthValue;
	@ApiModelProperty("卡等级GUID")
	private String cardLevelGuid;
	@ApiModelProperty("卡等级名字")
	private String cardLevelName;
	@ApiModelProperty("卡等级编号,V-(n)")
	private Integer cardLevelNum=0;
	@ApiModelProperty("等级图标路径")
	private String levelIcon;
	@ApiModelProperty("权益汇总")
	private List<ResponseCardRight> cardRightDetailsRespDTOS;
	@ApiModelProperty("是否选中，1：选中，2：不选中")
	private Integer uck=0;
}
