package com.holderzone.saas.store.dto.marketing;

import com.holderzone.saas.store.dto.member.pay.RedPacketOrderDTO;
import com.holderzone.saas.store.enums.marketing.QueryTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/4
 * @description 营销活动统一查询
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "营销活动统一查询")
public class UniteActivityQO implements Serializable {

    private static final long serialVersionUID = 675233801797198681L;

    @NotEmpty(message = "运营主体guid不能为空")
    @ApiModelProperty(value = "运营主体guid")
    private String operSubjectGuid;

    /**
     * 查询类型
     *
     * @see QueryTypeEnum
     */
    @NotNull(message = "查询类型不能为空")
    @ApiModelProperty("查询类型 1限时特价活动 2随行红包")
    private Integer queryType;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty("用餐类型:0正餐，1快餐")
    private Integer orderState;

    @ApiModelProperty(value = "下单时间")
    private LocalDateTime orderDate;

    @ApiModelProperty("订单列表")
    private List<RedPacketOrderDTO> orderNumbers;

}
