package com.holderzone.saas.store.dto.print.content.nested;

import com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayRecord
 * @date 2018/07/25 16:11
 * @description 支付方式
 * @program holder-saas-store-print
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "收款方式统计")
public class PayRecordStats implements Serializable {

    private static final long serialVersionUID = -1851067545250063984L;

    @NotBlank(message = "支付方式名称不能为空")
    @ApiModelProperty(value = "支付方式名称", required = true)
    private String payName;

    @Nullable
    @ApiModelProperty(value = "销售收入", required = true)
    private BigDecimal salesIncome;

    @ApiModelProperty(value = "销售收入", required = true)
    private String salesIncomeStr;

    @Nullable
    @ApiModelProperty(value = "充值收入", required = true)
    private BigDecimal rechargeIncome;

    @ApiModelProperty(value = "充值收入", required = true)
    private String rechargeIncomeStr;

    @Nullable
    @ApiModelProperty(value = "预订收入", required = true)
    private BigDecimal reserveIncome;

    @ApiModelProperty(value = "预订收入", required = true)
    private String reserveIncomeStr;

    @ApiModelProperty(value = "余出金额")
    private BigDecimal excessAmount;

    @ApiModelProperty(value = "余出金额")
    private String excessAmountStr;

    @ApiModelProperty(value = "明细")
    private List<GatherRespDTO.InnerDetails> innerDetails;
}
