package com.holderzone.saas.store.dto.report.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/23
 * @description 收款构成返回
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "收款构成返回", description = "收款构成返回")
public class PaymentConstituteRespDTO implements Serializable {

    private static final long serialVersionUID = -333510143372402623L;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "营业时间")
    private LocalDate businessDate;

    @ApiModelProperty(value = "收款方式")
    private List<PaymentMethodRespDTO> paymentList = new ArrayList<>();

}
