package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPicDTO
 * @date 2019/08/19 16:35
 * @description 新副屏图片DTO
 * @program holder-saas-store
 */
@Data
@ApiModel(value = "新副屏图片DTO")
public class ScreenPicDTO {

    @ApiModelProperty(value = "guid")
    private String screenPictureGuid;

    @ApiModelProperty(value = "图片下载路径")
    private String ossUrl;

    @ApiModelProperty(value = "像素类型 1-->1080*1920,3-->1024*600,4-->635*600,2-->800*800,5-->1920*1080,")
    private Integer pxType;

    @ApiModelProperty(value = "图片名字")
    private String name;
}
