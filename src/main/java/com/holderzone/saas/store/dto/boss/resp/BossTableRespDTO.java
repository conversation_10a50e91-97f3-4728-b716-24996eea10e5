package com.holderzone.saas.store.dto.boss.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.framework.util.DateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/10/22
 * @description 老板助手桌台信息
 */
@Data
@ApiModel(value = "老板助手桌台信息")
@EqualsAndHashCode(callSuper = false)
public class BossTableRespDTO implements Serializable {

    private static final long serialVersionUID = -7746917916210669937L;

    @ApiModelProperty("桌台guid")
    private String tableGuid;

    @ApiModelProperty("桌台名称")
    private String tableCode;

    @ApiModelProperty("区域guid")
    private String areaGuid;

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("订单金额")
    private BigDecimal orderAmount;

    @ApiModelProperty("主单号，如果有主单号，表示拼桌，如果主单号和订单号相同表示是主桌")
    private String mainOrderGuid;

    @ApiModelProperty("订单号")
    private String orderGuid;

    @ApiModelProperty("桌位座位数")
    private Integer seats;

    @ApiModelProperty("实际就餐人数")
    private Integer actualGuestsNo;

    @ApiModelProperty("桌台状态 -1=暂停使用;0=空闲;1=占用;2=预定;3=待清台")
    private Integer status;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("桌台详情状态 10=占用空台;11=占用锁定;12=占用并台;20=预定未锁定;21=预定已锁定")
    private Set<Integer> subStatus;

    @ApiModelProperty("开台时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime openTableTime;

    @ApiModelProperty("当前时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime currentTime = DateTimeUtils.now();

    @ApiModelProperty(value = "门店guid", hidden = true)
    private String storeGuid;

    @ApiModelProperty(value = "门店名称", hidden = true)
    private String storeName;

    @ApiModelProperty("联台次数")
    private Integer associatedTimes;

    @ApiModelProperty("并卓次数")
    private Integer combineTimes;

}
