package com.holderzone.saas.store.dto.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MsgInfoRespDTO
 * @date 2018/09/25 10:20
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class MsgInfoRespDTO implements Serializable {

    @ApiModelProperty(value = "消息guid")
    private String messageGuid;

    @ApiModelProperty(value = "消息标题")
    private String subject;

    @ApiModelProperty(value = "state")
    private Integer state;

    @ApiModelProperty(value = "content")
    private String content;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "消息创建时间")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "当前时间")
    private LocalDateTime currentTime;

}
