package com.holderzone.saas.store.dto.deposit.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class GoodsSimpleRespDTO implements Serializable {

    private static final long serialVersionUID = 8424514095125620853L;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品规格名称")
    private String skuName;

    @ApiModelProperty(value = "剩余数量")
    private int residueNum;

    @ApiModelProperty(value = "这次历史操作操作的数据量")
    private int operatorNum;
}
