package com.holderzone.saas.store.dto.report.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * <AUTHOR>
 * @date 2024/2/27
 * @description 收款构成查询结果
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "收款构成查询结果", description = "收款构成查询结果")
public class PaymentConstituteDTO implements Serializable {

    private static final long serialVersionUID = 8612515557779691719L;

    @ApiModelProperty(value = "营业时间")
    public LocalDate businessDate;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "收款code")
    private Integer paymentType;

    @ApiModelProperty(value = "收款方式")
    private String payMethod;

    @ApiModelProperty(value = "销售收入")
    private BigDecimal salesRevenue;

    @ApiModelProperty(value = "会员充值收入")
    private BigDecimal memberRecharge;

    @ApiModelProperty(value = "预定定金")
    private BigDecimal deposit;

    @ApiModelProperty(value = "合计")
    private BigDecimal total;

    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceCharge;

    @ApiModelProperty(value = "预计实收")
    private BigDecimal actualIncome;

    @ApiModelProperty(value = "集合支付id")
    private String payPowerId;
}
