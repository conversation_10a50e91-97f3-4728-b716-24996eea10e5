package com.holderzone.saas.store.dto.retail.dinein;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderListRespDTO
 * @date 2018/09/07 19:49
 * @description //TODO
 * @program holder-saas-store-order
 */
@Data
public class RetailOrderListRespDTO extends BasePageDTO {

    @ApiModelProperty(value = "状态(未结账，已结账，已作废, 退单")
    private Integer state;

    @ApiModelProperty(value = "状态(未结账，已结账，已作废, 退单")
    private String stateName;

    @ApiModelProperty(value = "订单类型1：普通单 2：退单")
    private Integer orderType;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "订单guid")
    private String guid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "创建操作人guid")
    private String createStaffGuid;

    @ApiModelProperty(value = "创建操作人名字")
    private String createStaffName;

    @ApiModelProperty(value = "结账操作人guid")
    private String checkoutStaffGuid;

    @ApiModelProperty(value = "结账操作人名字")
    private String checkoutStaffName;

    @ApiModelProperty(value = "校驗時間")
    private LocalDateTime checkoutTime;
}
