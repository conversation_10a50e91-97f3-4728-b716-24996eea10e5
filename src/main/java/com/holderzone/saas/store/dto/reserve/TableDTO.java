package com.holderzone.saas.store.dto.reserve;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class TableDTO {

    @ApiModelProperty("桌台guid")
    private String guid;

    @ApiModelProperty("桌台名称")
    private String name;

    @ApiModelProperty("区域guid")
    private String areaGuid;

    @ApiModelProperty("区域名称")
    private String areaName;

    private transient Integer state = 0;
}