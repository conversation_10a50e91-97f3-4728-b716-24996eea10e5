package com.holderzone.saas.store.dto.table;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.anno.LockField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class OpenAssociatedTableDTO extends BaseDTO {

    private static final long serialVersionUID = -3646445554626334200L;

    @NotNull
    @ApiModelProperty(value = "实际就餐人数")
    private Integer actualGuestsNo;

    @ApiModelProperty("主桌桌台guid")
    @LockField
    @NotBlank
    private String mainTableGuid;

    @ApiModelProperty("桌台guid集合")
    @NotEmpty
    private List<String> tableGuids;
}
