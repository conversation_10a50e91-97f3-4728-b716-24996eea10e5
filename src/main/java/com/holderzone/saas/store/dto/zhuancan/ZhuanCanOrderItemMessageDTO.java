package com.holderzone.saas.store.dto.zhuancan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/4/24
 * @description 赚餐订单商品
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "赚餐订单商品", description = "赚餐订单商品")
public class ZhuanCanOrderItemMessageDTO implements Serializable {

    private static final long serialVersionUID = 6053475597592687704L;

    @ApiModelProperty(value = "商品Guid")
    private String itemGuid;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "SkuGuid")
    private String skuGuid;

    @ApiModelProperty(value = "SKU名称")
    private String skuName;

    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @ApiModelProperty(value = "数量")
    private Integer number;

}
