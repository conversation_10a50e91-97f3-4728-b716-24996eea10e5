package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@ApiModel
@EqualsAndHashCode(callSuper = true)
public class TCDDeliverySyncReqDTO extends BaseDTO {

    @ApiModelProperty(value = "店铺id")
    private String id;

    @ApiModelProperty(value = "赚餐平台ID")
    private String platformId;

    @ApiModelProperty(value = "店铺名称")
    private String name;

    @ApiModelProperty(value = "掌控者门店ID")
    private String holderMerchantId;

    @ApiModelProperty(value = "绑定状态，0-未绑定 1-已绑定")
    private Integer bindStatus;

    @ApiModelProperty(value = "绑定token")
    private String token;

    @ApiModelProperty(value = "配送类型(0：自配送，1:饿了么/美团一城飞客 2:饿了么/美团专送，3：饿了么/美团众包)")
    private Integer deliveryType;

}
