package com.holderzone.saas.store.dto.trade;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class OrderAbnormalRecordReqDTO extends BaseDTO implements Serializable {

    @ApiModelProperty("订单guid")
    private String orderGuid;

    @ApiModelProperty("桌台guid")
    private String tableGuid;

    @ApiModelProperty("支付guid")
    private String payGuid;

    @ApiModelProperty("其他支付参数")
    private BillPayReqDTO billPayReqDTO;
}
