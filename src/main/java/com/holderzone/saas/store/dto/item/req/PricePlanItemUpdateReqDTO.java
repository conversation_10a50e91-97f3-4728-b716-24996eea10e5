package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(value = "方案菜品更新入参")
public class PricePlanItemUpdateReqDTO {

    @ApiModelProperty(value = "批量调整百分比")
    private Integer percentNum;

    @ApiModelProperty(value = "批量调整固定值")
    private BigDecimal fixedPrice;

    @ApiModelProperty(value = "更新菜品集合")
    private List<PricePlanItemUpdateDTO> itemList;
}
