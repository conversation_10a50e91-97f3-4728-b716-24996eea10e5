package com.holderzone.saas.store.dto.item.common;

import com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 价格方案批量编辑
 * @date 2021/9/29
 */
@Data
public class PlanPriceEditPriceBaseDTO {

    @ApiModelProperty(value = "规格guid")
    private String skuGuid;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "规格")
    private String skuName;

    @ApiModelProperty(value = "会员价或者售价")
    private BigDecimal price;
}
