package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 查询商品spu对应商品信息请求参数
 *
 * <AUTHOR>
 * @date 2021/4/11 15:10
 */
@Data
@ApiModel(description = "查询商品spu对应商品信息请求参数")
public class ItemSpuReqDTO {

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "商品Guid")
    private List<String> itemGuids;
}
