package com.holderzone.saas.store.dto.trade.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 记录第三方活动使用信息入参
 * @date 2021/12/9 16:31
 * @className: RecordThirdActivityInfoReqDTO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(description = "记录第三方活动使用信息入参")
public class RecordThirdActivityInfoReqDTO implements Serializable {

    private static final long serialVersionUID = -8972363827321785832L;

    @ApiModelProperty(value = "第三方活动使用记录列表")
    private List<ThirdActivityRecordDTO> thirdActivityRecordList;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;
}
