package com.holderzone.saas.store.dto.weixin.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Api("优惠券详情入参")
@Data
public class WxVolumeDetailReqDTO {

	@ApiModelProperty(value="企业guid")
	private	String enterpriseGuid;

	@ApiModelProperty("卡券guid")
	private String memberVolumeGuid;

	@ApiModelProperty("卡券信息guid")
	private String volumeInfoGuid;
}
