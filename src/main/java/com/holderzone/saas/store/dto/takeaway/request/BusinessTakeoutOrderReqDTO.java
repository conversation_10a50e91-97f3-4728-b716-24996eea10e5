package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <p>
 * 商户后台/数据报表/订单统计 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BusinessTakeoutOrderReqDTO extends BasePageDTO {

    @ApiModelProperty(value = "营业时间开始时间,格式:yyyy-MM-dd,必传", required = true)
    @NotEmpty(message = "营业时间开始时间不可为空")
    private String startTime;

    @ApiModelProperty(value = "营业时间结束时间,格式:yyyy-MM-dd,必传", required = true)
    @NotEmpty(message = "营业时间结束时间不可为空")
    private String endTime;

    @ApiModelProperty(value = "订单状态注意符号也需要传:-1：已取消,0：待处理,10：待配送,20：配送中,100：已完成,-2：异常单,查询全部制空字段")
    private Integer orderState;

    @ApiModelProperty(value = "手机号")
    private String phoneNo;

    @ApiModelProperty(value = "外卖订单来源:0：美团,1：饿了么,5：自营外卖,6：通吃岛外卖")
    private Integer orderSource;

    @ApiModelProperty(value = "门店集合，品牌查询需传品牌下门店集合")
    private List<String> storeGuidList;

    @ApiModelProperty(hidden = true)
    public int getIndex() {
        return ((getCurrentPage() == null || getCurrentPage() <= 0 ? 1 : getCurrentPage()) - 1) * (getPageSize() == null ? 20 : getPageSize());
    }

}
