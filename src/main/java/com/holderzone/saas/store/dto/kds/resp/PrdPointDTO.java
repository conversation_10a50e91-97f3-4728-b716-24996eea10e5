package com.holderzone.saas.store.dto.kds.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class PrdPointDTO implements Serializable {

    private static final long serialVersionUID = -2240162461066846742L;

    @ApiModelProperty("堂口名称")
    private String pointName;

    @ApiModelProperty("商品列表")
    private List<PrdDstItemDTO> items;

    @ApiModelProperty("订单列表")
    private List<PrdDstOrderDTO> orders;
}
