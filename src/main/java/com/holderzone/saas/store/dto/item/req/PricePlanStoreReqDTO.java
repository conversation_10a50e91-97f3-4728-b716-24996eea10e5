package com.holderzone.saas.store.dto.item.req;

import com.holderzone.saas.store.dto.organization.StoreParserPageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 价格方案门店控制列表查询请求对象
 * @date 2020/10/29 17:13
 */
@ApiModel(value = "价格方案门店控制列表查询请求对象")
@Data
public class PricePlanStoreReqDTO extends StoreParserPageDTO {

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "价格方案Guid")
    private String planGuid;
}
