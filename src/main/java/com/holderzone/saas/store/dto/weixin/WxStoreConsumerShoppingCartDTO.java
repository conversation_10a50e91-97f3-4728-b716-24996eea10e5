package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;

@Data
@ApiModel("用户与购物车管理信息")
public class WxStoreConsumerShoppingCartDTO {

    @Valid
    @ApiModelProperty("购物车信息")
    private WxStoreShoppingCartDTO wxStoreShoppingCartDTO;

    @Valid
    @ApiModelProperty("用户信息")
    private WxStoreConsumerDTO wxStoreConsumerDTO;
}
