package com.holderzone.saas.store.dto.pay;

import com.holderzone.saas.store.dto.trade.BaseInfo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SaasNotifyDTO
 * @date 2019/03/21 14:00
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class SaasNotifyDTO {

    private AggPayPollingRespDTO aggPayPollingRespDTO;

    private AggRefundPollingRespDTO aggRefundPollingRespDTO;

    private BaseInfo baseInfo;


}
