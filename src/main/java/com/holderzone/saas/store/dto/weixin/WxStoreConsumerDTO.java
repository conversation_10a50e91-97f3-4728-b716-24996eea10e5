package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@ApiModel("微信点餐用户信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxStoreConsumerDTO {

    @ApiModelProperty("这tm就是userRecord的guid")
    private String consumerGuid;
    @NotBlank
    @ApiModelProperty(value = "用户当前所在公众号唯一标识", required = true)
    private String openId;
    @ApiModelProperty(value = "用户在所有公众号的唯一标识")
    private String unionId;
    @ApiModelProperty("用户昵称")
    private String nickName;
    @ApiModelProperty("用户微信头像")
    private String headImgUrl;
    @ApiModelProperty("用户性别,0：女,1:男，2:无法识别")
    private int sex;
    @ApiModelProperty("用户所在国家")
    private String country;
    @ApiModelProperty("用户所在省份")
    private String province;
    @ApiModelProperty("用户所在城市")
    private String city;

    private String workName;
    private String contactAddress;
    @Deprecated
    @ApiModelProperty("用户手机")
    private String phone;
    @ApiModelProperty(value = "设备类型:PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7")
    private Integer deviceType;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "企业guid")
    private String enterpriseGuid;

    @ApiModelProperty(value = "企业名称")
    private String enterpriseName;

    @NotBlank
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "用户guid")
    private String userGuid;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "登录者帐号")
    private String account;

    @ApiModelProperty(value = "点餐人数")
    private Integer guestCount;

    @NotBlank
    @ApiModelProperty(value = "桌台guid", required = true)
    private String diningTableGuid;

    @NotBlank
    @ApiModelProperty(value = "桌台号", required = true)
    private String tableCode;
    @NotBlank
    @ApiModelProperty(value = "桌台名字", required = true)
    private String diningTableName;

    @ApiModelProperty(value = "区域guid", required = true)
    private String areaGuid;

    @NotBlank
    @ApiModelProperty(value = "桌台所在区域名称", required = true)
    private String areaName;

    @ApiModelProperty(value = "品牌名字")
    private String brandName;

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "品牌图片")
    private String brandLogo;
    
    @ApiModelProperty(value = "是否登录会员")
    private Boolean isLogin;

    @ApiModelProperty("会员guid")
    private String memberInfoGuid;

    @ApiModelProperty(value = "手机号码")
    private String phoneNum;

    @ApiModelProperty("是否为联盟")
    private Boolean isAlliance;
    @ApiModelProperty("运营主体guid")
    private String operSubjectGuid;
    @ApiModelProperty("运营主体是否禁用")
    private Boolean multiMemberStatus;
    {
        this.brandGuid = "";
        this.brandLogo = "";
        this.brandName = "";
    }
}
