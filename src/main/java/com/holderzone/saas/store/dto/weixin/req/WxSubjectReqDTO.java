package com.holderzone.saas.store.dto.weixin.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2020/8/28 15:21
 * @description
 */
@ApiModel("微信主体获取请求DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WxSubjectReqDTO {
    @NotEmpty(message = "enterpriseGuid 不能为空")
    @ApiModelProperty("企业guid")
    private String enterpriseGuid;

    @ApiModelProperty("appId")
    private String appId;
}
