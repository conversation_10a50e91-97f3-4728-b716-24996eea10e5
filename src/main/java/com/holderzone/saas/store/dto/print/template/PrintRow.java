package com.holderzone.saas.store.dto.print.template;

import com.holderzone.saas.store.dto.print.template.printable.*;
import com.holderzone.saas.store.enums.print.ContentTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 适配行，兼容所有Printable，方便传输时序列化/反序列化
 *
 * <AUTHOR>
 * @date 18-12-15 14:30
 */
@Data
@ApiModel
@NoArgsConstructor
@Accessors(chain = true)
public class PrintRow implements Serializable {

    private static final long serialVersionUID = -1489496746791236939L;

    /**
     * 媒体类型
     *
     * @see ContentTypeEnum
     */
    @ApiModelProperty(value = "媒体类型", required = true)
    private String contentType;

    @ApiModelProperty(value = "条码")
    private BarCode barCode;

    @ApiModelProperty(value = "标签条码")
    private LabelBarCode labelBarCode;

    @ApiModelProperty(value = "空白行")
    private BlankRow blankRow;

    @ApiModelProperty(value = "坐标行")
    private CoordinateRow coordinateRow;

    @ApiModelProperty(value = "图片")
    private Image image;

    @ApiModelProperty(value = "键值对")
    private KeyValue keyValue;

    @ApiModelProperty(value = "简单横线")
    private Line line;

    @ApiModelProperty(value = "二维码")
    private QrCode qrCode;

    @ApiModelProperty(value = "文本反白")
    private ReverseText reverseText;

    @ApiModelProperty(value = "段落")
    private Section section;

    @ApiModelProperty(value = "分隔符")
    private Separator separator;

    @ApiModelProperty(value = "单元格")
    private Table table;

    @ApiModelProperty(value = "单元格行")
    private TableRow tableRow;
}
