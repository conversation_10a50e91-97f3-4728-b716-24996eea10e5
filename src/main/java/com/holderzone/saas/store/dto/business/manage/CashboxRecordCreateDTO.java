package com.holderzone.saas.store.dto.business.manage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CashboxRecordDO
 * @date 2018/07/29 下午5:42
 * @description 创建钱箱记录DTO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
@Builder
public class CashboxRecordCreateDTO implements Serializable {

    private static final long serialVersionUID = 6463703431084935271L;

    /**
     * 门店guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    /**
     * 用户guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "用户guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "用户guid", required = true)
    private String userGuid;

    /**
     * 用户名字
     */
    @NotNull
    @Size(min = 1, max = 45, message = "用户名字不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "用户名字", required = true)
    private String userName;

    /**
     * 操作类型
     * 0=存入
     * 1=取出
     */
    @NotNull
    @Min(value = 0, message = "操作类型[0-1]，0=存入，1=取出")
    @Max(value = 1, message = "操作类型[0-1]，0=存入，1=取出")
    @ApiModelProperty(value = "操作类型：0=存入，1=取出", required = true)
    private Integer operationType;

    /**
     * 来源类型
     */
    @NotNull
    @Min(value = 0, message = "来源类型[0-1]，0=开钱箱存取款，1=收银存取款")
    @Max(value = 1, message = "来源类型[0-1]，0=开钱箱存取款，1=收银存取款")
    @ApiModelProperty(value = "来源类型：0=开钱箱存取款，1=收银存取款", required = true)
    private Integer sourceType;

    /**
     * 现金
     */
    @NotNull
    @ApiModelProperty(value = "现金", required = true)
    private BigDecimal money;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 设备id
     */
    @NotNull
    @Size(min = 1, max = 45, message = "设备id不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "设备id", required = true)
    private String terminalId;

    private String enterpriseGuid;
}
