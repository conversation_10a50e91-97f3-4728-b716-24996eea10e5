package com.holderzone.saas.store.dto.weixin.resp;

import com.holderzone.saas.store.dto.weixin.common.WxStoreInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@ApiModel("门店配置")
@Data
public class WxStoreConfigRespDTO extends WxStoreInfoDTO {

    /**
     * 是否营业（0关闭，1开启）
     */
    @ApiModelProperty(value = "点餐功能是否开启（0已暂停点餐，1开启）")
    private Integer isOrderOpen = 1;

    /**
     * 点餐模式（0正餐，1快餐）
     */
    @ApiModelProperty(value = "点餐模式（0正餐，1快餐）")
    private Integer orderModel = 0;

    /**
     * 接单模式（0手动接单，1首单手动，加菜自动，2无需确认（仅适用于快餐））
     */
    @ApiModelProperty(value = "接单模式（0所有订单均需手动确认，1首单需手动确认，加菜无需确认，2无需确认（仅适用于快餐模式））")
    private Integer takingModel = 1;

    /**
     * 是否开启线上买单（0关闭，1开启（快餐只能选择开启））
     */
    @ApiModelProperty(value = "是否开启线上买单（0关闭，1开启（快餐只能选择开启））")
    private Integer isOnlinePayed = 1;

    /**
     * 支付方式List
     */
    @ApiModelProperty(value = "支付方式List")
    private String payWayNames;

    /**
     * 结账模式（0线上支付后，需店员手动结账和清台，1线上支付后自动结账，需店员手动清台，2，自动结账（仅支持快餐））
     */
    @ApiModelProperty(value = "结账模式（0线上支付后，需店员手动结账和清台，1线上支付后自动结账，需店员手动清台，2，自动结账（仅支持快餐））")
    private Integer settleModel = 0;

    @ApiModelProperty(value = "顾客是否可点称重商品（0顾客不可点，1顾客可点）")
    private Integer isWeighingOrdered = 0;

    @ApiModelProperty(value = "是否开启单品备注（0关闭，1开启）")
    private Integer isRemarked = 1;

    @ApiModelProperty(value = "跳转页面类型（0点餐页，1门店主页）")
    private Integer urlType = 0;

    @ApiModelProperty(value = "菜单样式（0大图，1小图）")
    private Integer menuType = 0;


}
