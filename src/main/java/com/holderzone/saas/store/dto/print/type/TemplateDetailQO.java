package com.holderzone.saas.store.dto.print.type;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/29
 * @description 分类模版详情查询
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "分类模版详情查询", description = "分类模版详情查询")
public class TemplateDetailQO implements Serializable {

    private static final long serialVersionUID = 2315334115319583888L;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "打印票据类型")
    private Integer invoiceType;

    @ApiModelProperty(value = "商品guid")
    private List<String> itemGuidList;
}
