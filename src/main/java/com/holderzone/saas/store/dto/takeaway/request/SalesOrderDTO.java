package com.holderzone.saas.store.dto.takeaway.request;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class SalesOrderDTO {

    @ApiModelProperty(value = "用户名")
    public String UserName;

    @NotNull(message = "门店编号不得为空")
    @ApiModelProperty(value = "门店编号")
    public Integer StoreId;

    @NotNull(message = "小程序端订单ID不得为空")
    @ApiModelProperty(value = "小程序端订单ID")
    public long OrderId;

    @NotBlank(message = "门店Guid不得为空")
    @ApiModelProperty(value = "门店Guid")
    public String StoreGuid;

    @ApiModelProperty(value = "微信授权Code")
    public String Code;

    @ApiModelProperty(value = "当前请求用户OpenID")
    public String OpenId;

    @ApiModelProperty(value = "桌台id")
    public Integer StoreTableId;

    @NotBlank(message = "流水号不得为空")
    @ApiModelProperty(value = "流水号")
    public String SerialNumber;

    @NotBlank(message = "取单号不得为空")
    @ApiModelProperty(value = "取单号")
    public String TakeAwayNum;

    @NotNull(message = "客人数不得为空")
    @ApiModelProperty(value = "客人数")
    public Integer GuestCount;

    @ApiModelProperty(value = "结账时间")
    public String CheckOutTime;

    @NotNull(message = "营业额(最终应付金额)不得为空")
    @ApiModelProperty(value = "营业额(最终应付金额)")
    public BigDecimal ConsumeAmount;

    @NotNull(message = "附加费(餐盒费+配送费)不得为空")
    @ApiModelProperty(value = "附加费(餐盒费+配送费)")
    public BigDecimal Additional;

    @NotNull(message = "配送费不得为空")
    @ApiModelProperty(value = "配送费")
    public BigDecimal DeliverAmount;

    @NotNull(message = "收款金额不得为空")
    @ApiModelProperty(value = "收款金额")
    public BigDecimal PayAmount;

    @NotNull(message = "菜品总价不得为空")
    @ApiModelProperty(value = "菜品总价")
    public BigDecimal GoodsAmount;

    @NotNull(message = "折扣金额不得为空")
    @ApiModelProperty(value = "折扣金额")
    public BigDecimal DiscountAmount;

    @ApiModelProperty(value = "退款金额")
    public BigDecimal RefundAmount;

    @ApiModelProperty(value = "订单备注")
    public String Remark;

    @NotNull(message = "订单状态(0待支付;1支付完成;2订单完成)不得为空")
    @ApiModelProperty(value = "订单状态(0待支付;1支付完成;2订单完成)")
    public Integer OrderStatus;

    @NotNull(message = "取餐方式(0堂食;1自取;2外送)不得为空")
    @ApiModelProperty(value = "取餐方式(0堂食;1自取;2外送)")
    public Integer WayEatType;

    @NotNull(message = "订单来源（0微信小程序点餐、外卖……默认微信小程序点餐)不得为空")
    @ApiModelProperty(value = "订单来源（0微信小程序点餐、外卖……默认微信小程序点餐)")
    public Integer SourceType;

    @ApiModelProperty(value = "分享码")
    public String ShareCode;

    @NotNull(message = "客户Id(用于外卖)不得为空")
    @ApiModelProperty(value = "客户Id(用于外卖)")
    public Integer CustomersId;

    @ApiModelProperty(value = "用于模板消息formId")
    public String FormId;

    @NotNull(message = "是否新客不得为空")
    @ApiModelProperty(value = "是否新客")
    public Boolean IsNewGuest;

    @NotBlank(message = "下单时间不得为空")
    @ApiModelProperty(value = "下单时间")
    private String CreateTime;

    @ApiModelProperty(value = "客户信息")
    public CustomersDTO CustomersDTO;

    @ApiModelProperty(value = "账单菜品明细列表")
    public List<SalesOrderGoodsDTO> SalesOrderGoods;

    @ApiModelProperty(value = "账单折扣明细列表")
    public List<SalesOrderConstituteDTO> SalesOrderConstituteDTOs;

}

