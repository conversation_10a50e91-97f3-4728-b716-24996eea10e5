package com.holderzone.saas.store.dto.takeaway.request;

import com.holderzone.saas.store.enums.takeaway.NotifyTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/15
 * @description 通知支付宝入参
 */
@Data
public class NotifyAliPayAuthReqDTO {

    @ApiModelProperty(value = "企业GUID")
    private String enterpriseGuid;

    @ApiModelProperty(value = "运营主体GUID")
    private String operSubjectGuid;

    @ApiModelProperty(value = "支付宝商户appid")
    private String appId;

    @ApiModelProperty(value = "应用授权Token")
    private String appAuthToken;

    /**
     * 通知类型 1授权 2取消授权
     *
     * @see NotifyTypeEnum
     */
    @ApiModelProperty(value = "通知类型 1授权 2取消授权")
    private Integer notifyType;

}
