package com.holderzone.saas.store.dto.item.req.estimate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 估清商品规格请求DTO
 * @date 2022/4/12 10:17
 * @className: EstimateSkuReqDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "估清商品规格请求DTO")
public class EstimateSkuReqDTO implements Serializable {

    private static final long serialVersionUID = 7914476302531572288L;

    /**
     * 商品sku guid
     */
    @NotNull(message = "商品sku不能为空")
    @ApiModelProperty(value = "商品规格Guid")
    private String skuGuid;

    /**
     * 设置数量
     * 同限量数量
     * 即设置估清时录入的数量
     */
    @NotNull(message = "设置数量不能为空")
    @ApiModelProperty(value = "设置数量")
    private BigDecimal limitQuantity;

    /**
     * 是否长期估清（未勾选次日自动移除列表恢复售卖）
     * 1：否  2：是
     */
    @NotNull(message = "是否长期估清不能为空")
    @ApiModelProperty(value = "是否长期估清 1：否  2：是")
    private Integer isForeverEstimate;

}
