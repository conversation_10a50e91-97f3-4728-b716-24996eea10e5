package com.holderzone.saas.store.dto.weixin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @className WxSocketDistributionDTO
 * @date 2019/5/20
 */
@Data
@ApiModel("mq向websocket需要传输的对象")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WxSocketDistributionDTO {

	@ApiModelProperty(value = "分发类型:0(向桌台每个用户分发，需要tableGuid),1(用于快餐分发，需要openId),2(向桌台具体某个用户分发，需要tableGuid,openId)" +
			"3.桌台分发除了当前用户，需要tableGuid，openGuid")
	private Integer distribution;

	@ApiModelProperty(value = "tableGuid")
	private String tableGuid;

	@ApiModelProperty(value = "用户id")
	private String openId;

	@ApiModelProperty(value = "需要推送的对象")
	private String content;
}
