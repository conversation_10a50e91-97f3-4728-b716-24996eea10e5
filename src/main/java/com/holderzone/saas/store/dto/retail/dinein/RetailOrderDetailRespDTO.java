package com.holderzone.saas.store.dto.retail.dinein;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.holder.saas.member.terminal.dto.retail.ResponseMemberAndCardInfo;
import com.holderzone.holder.saas.member.terminal.dto.retail.ResponseRetailIntegralCompute;
import com.holderzone.saas.store.dto.member.MemberInfoRespDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.OrderFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnOrderDetailDTO;
import com.holderzone.saas.store.dto.retail.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.retail.bill.common.RetailItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineinOrderDetailRespDTO
 * @date 2019/01/16 14:30
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class RetailOrderDetailRespDTO {

    @ApiModelProperty(value = "订单的guid")
    private String guid;

    @ApiModelProperty(value = "订单来源：PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7",
            required = true)
    private String deviceTypeName;


    @ApiModelProperty(value = "订单来源：PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7",
            required = true)
    private Integer deviceType;

    @ApiModelProperty(value = "结账来源：PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7",
            required = true)
    private String checkoutDeviceTypeName;

    @ApiModelProperty(value = "订单金额（商品总额+附加费）")
    private BigDecimal orderFee;

    @ApiModelProperty(value = "订单剩余可以优惠金额")
    private BigDecimal orderSurplusFee;


    @ApiModelProperty(value = "实收金额（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "找零（收款-应收金额）")
    private BigDecimal changeFee;

    @ApiModelProperty(value = "优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
    private BigDecimal discountFee;


    @ApiModelProperty(value = "订单金额明细（商品总额+附加费）")
    private OrderFeeDetailDTO orderFeeDetailDTO;

    @ApiModelProperty(value = "实收金额明细（订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠）））")
    private List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS;

    @ApiModelProperty(value = "优惠金额明细（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））")
    private List<DiscountFeeDetailDTO> discountFeeDetailDTOS;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "状态(1：未结账， 2：已结账， 3：已退款，4：已作废)")
    private Integer state;

    @ApiModelProperty(value = "状态(1：未结账， 2：已结账， 3：已退款，4：已作废)")
    private String stateName;

    @ApiModelProperty(value = "订单类型：1.普通单 2：退单")
    private Integer orderType;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "结算时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTime;

    @ApiModelProperty(value = "创建人")
    private String createStaffName;

    @ApiModelProperty(value = "结账人")
    private String checkoutStaffName;

    @ApiModelProperty(value = "会员guid")
    private String memberGuid;

    @ApiModelProperty(value = " 会员支付id")
    private String memberConsumptionGuid;

    @ApiModelProperty(value = "整单备注")
    private String remark;

    @ApiModelProperty(value = "退单信息")
    private ReturnOrderDetailDTO returnOrderDetailDTO;

    @ApiModelProperty(value = "退货信息")
    private List<ReturnItemDTO> returnItemDTOS;

    @ApiModelProperty(value = "商品")
    private List<RetailItemDTO> dineInItemDTOS;

    @ApiModelProperty(value = "会员详情")
    private MemberInfoRespDTO memberInfoRespDTO;

    @ApiModelProperty(value = "新会员详情")
    private ResponseMemberAndCardInfo memberInfoAndCard;

    @ApiModelProperty(value = "零售会员详情")
    private ResponseMemberAndCardInfo retailMemberInfoAndCard;

    @ApiModelProperty(value = "会员积分详情")
    private ResponseRetailIntegralCompute integralOffsetResultRespDTO;

    @ApiModelProperty(value = "订单当前关联的会员卡guid")
    private String memberCardGuid;

    @ApiModelProperty(value = "订单当前关联的会员卡编号")
    private String memberCardNum;

    @ApiModelProperty(value = "当前会员卡是否用了会员价，0-没有，1-有")
    private Integer singleItemUsedMemeberPrice;

    @ApiModelProperty(value = "错误原因")
    private String tip;

    @ApiModelProperty(value = "version")
    private Integer version;

    public Optional<List<RetailItemDTO>> optionalDineInItemDTOS() {
        return Optional.ofNullable(dineInItemDTOS);
    }
}
