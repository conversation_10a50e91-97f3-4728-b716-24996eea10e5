package com.holderzone.saas.store.dto.business.queue;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueuedTypeCreateDTO
 * @date 2018/07/27 下午4:35
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class QueuedTypeCreateDTO {

    /**
     * 门店guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    /**
     * 队列名称
     */
    @NotNull
    @Size(min = 1, max = 45, message = "队列名称不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "队列名称", required = true)
    private String name;

    /**
     * 队列编号
     */
    @NotEmpty
    @ApiModelProperty(value = "队列编号", required = true)
    private String code;

    /**
     * 就餐人数左区间
     */
    @NotNull
    @Min(value = 1, message = "就餐人数左区间范围[1-99]")
    @Max(value = 99, message = "就餐人数左区间范围[1-99]")
    @ApiModelProperty(value = "就餐人数左区间范围[1-99]", required = true)
    private Integer leftInterval;

    /**
     * 就餐人数右区间
     */
    @NotNull
    @Min(value = 1, message = "就餐人数右区间范围[1-99]")
    @Max(value = 99, message = "就餐人数右区间范围[1-99]")
    @ApiModelProperty(value = "就餐人数右区间范围[1-99]", required = true)
    private Integer rightInterval;
}
