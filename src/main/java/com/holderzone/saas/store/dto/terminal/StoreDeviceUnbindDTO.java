package com.holderzone.saas.store.dto.terminal;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className StoreTerminalUnbindDTO
 * @date 18-9-6 下午2:04
 * @description 门店设备-解绑DTO
 * @program holder-saas-store-dto
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class StoreDeviceUnbindDTO implements Serializable {

    private static final long serialVersionUID = 2789305336518963941L;

    /**
     * 门店guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "门店guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "门店guid", required = true)
    private String storeGuid;

    /**
     * 厂商设备编号
     */
    @NotNull
    @Size(min = 1, max = 45, message = "厂商设备编号不得为空，且不得超过45个字符")
    private String deviceNo;

    @NotNull
    @ApiModelProperty(value = "系统设备编号", required = true)
    private String deviceGuid;

    /**
     * 解绑人guid（修改人）
     */
    @ApiModelProperty(value = "解绑人（修改人）guid")
    private String unbindUserGuid;

    /**
     * 解绑人姓名
     */
    @ApiModelProperty(value = "解绑人姓名")
    private String unbindUserName;

    /**
     * 解绑时间
     */
    @NotNull
    @ApiModelProperty(value = "解绑时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtUnbindTime;

    @ApiModelProperty("是否来自云端")
    private Boolean fromCloud = Boolean.FALSE;
}
