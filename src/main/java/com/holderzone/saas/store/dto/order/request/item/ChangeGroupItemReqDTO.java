package com.holderzone.saas.store.dto.order.request.item;

import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.table.BaseTableDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 换菜请求DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ChangeGroupItemReqDTO extends BaseTableDTO {

    private static final long serialVersionUID = -7549769448472688573L;

    @Deprecated
    private SubDineInItemDTO subDineInItemDTO;

    private List<SubDineInItemDTO> subDineInItemList;

    @ApiModelProperty(value = "是否撤销")
    private boolean isCancel;

    @ApiModelProperty(value = "订单商品guid")
    private String orderItemGuid;

    @ApiModelProperty(value = "换菜批次号")
    private String changeBatchNumber;

}
