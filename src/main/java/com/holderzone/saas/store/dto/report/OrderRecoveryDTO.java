package com.holderzone.saas.store.dto.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.google.common.collect.Lists;
import com.holderzone.framework.util.DateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;


/**
 * 反结账
 */
@Data
@ApiModel
@NoArgsConstructor
public class OrderRecoveryDTO implements Serializable {

    @ApiModelProperty(notes = "菜品信息")
    private List<OrderRecoveryDetailDTO.AllDishes> allDishes = Lists.newArrayList();

    @ApiModelProperty(notes = "支付信息")
    private List<OrderRecoveryDetailDTO.Pay> pays = Lists.newArrayList();

    @ApiModelProperty(notes = "结算信息")
    private List<OrderRecoveryDetailDTO.Balance> balances = Lists.newArrayList();

    @ApiModelProperty(notes = "优惠信息")
    private List<OrderRecoveryDetailDTO.Discounts> discounts = Lists.newArrayList();

    /**
     * 订单号
     */
    @ApiModelProperty(notes = "订单号")
    private String orderNo = "";

    /**
     * 门店名称
     */
    @ApiModelProperty(notes = "门店名称")
    private String storeName = "";

    /**
     * 销售类型
     */
    @ApiModelProperty(notes = "销售类型")
    private String tradeMode = "";

    /**
     * 反结账时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @ApiModelProperty(notes = "反结账时间")
    private LocalDateTime recoveryTime;

    /**
     * 员工姓名
     */
    @ApiModelProperty(notes = "员工姓名")
    private String employeeName = "";
    /**
     * 账号
     */
    @ApiModelProperty(notes = "账号")
    private String employeeAccount = "'";

    /**
     * 牌号
     */
    @ApiModelProperty(notes = "牌号")
    private String mark = "";

    /**
     * 就餐人数
     */
    @ApiModelProperty(notes = "就餐人数")
    private int guestCount;

    @ApiModelProperty(value = "反结账员工姓名姓名/账户")
    private String recoveryNameAndGuid;

    /**
     * 结账时间
     */
    @ApiModelProperty(notes = "结账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutTimestamp;

    /**
     * 收银员
     */
    @ApiModelProperty(notes = "收银员")
    private String checkoutStaffName;

    /**
     * 创建人
     */
    @ApiModelProperty(notes = "创建人")
    private String creatorName;

    /**
     * 订单guid
     */
    @ApiModelProperty(notes = "订单guid")
    private String orderGuid;

    /**
     * 反结账ID
     */
    @ApiModelProperty(notes = "反结账ID")
    private String recoveryId;


    /*
        wudl add 2018-10-12
     */

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;


    @ApiModelProperty(value = "开台时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkInTimestamp;

    @ApiModelProperty("会员手机号")
    private String memberPhone;

    @ApiModelProperty("会员姓名")
    private String memberName;

    @ApiModelProperty("就餐时长")
    private String timeDuration;

    @ApiModelProperty(value = "收银员账号")
    private String operationStaffGuid;

    @ApiModelProperty(value = "收银员姓名")
    private String operationStaffName;

    @ApiModelProperty("外卖平台")
    private String takeawayPlat;

    @ApiModelProperty("外卖接单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime takeawayReciveOrderTime;

    @ApiModelProperty("菜品合计")
    private BigDecimal totalDishFee;

    @ApiModelProperty("优惠合计")
    private BigDecimal totalDiscountFee;

    @ApiModelProperty("实收金额")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty("原单号: 快餐：订单牌号，外卖：外卖平台订单号，正餐暂时没有原单号")
    private String originalNo;



    public void makeTimeDuration() {
        if (this.checkInTimestamp != null && this.checkoutTimestamp != null) {
            long longDuration = DateTimeUtils.localDateTime2Mills(this.checkoutTimestamp) - DateTimeUtils.localDateTime2Mills(this.checkInTimestamp);
            this.timeDuration = DateTimeUtils.mills2LocalDateTime(longDuration).toLocalTime().toString();
        }
    }
}
