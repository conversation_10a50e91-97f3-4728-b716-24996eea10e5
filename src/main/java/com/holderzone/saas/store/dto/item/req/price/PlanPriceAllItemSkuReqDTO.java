package com.holderzone.saas.store.dto.item.req.price;

import com.holderzone.saas.store.dto.common.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 查询品牌下所有价格方案得菜品
 * @date 2021/9/28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "查询所有为已启用、即将启用、暂不启用菜谱的所有商品规格")
public class PlanPriceAllItemSkuReqDTO extends PageDTO {

    private static final long serialVersionUID = 8031304796852560000L;

    @ApiModelProperty(value = "品牌guid")
    @NotNull(message = "品牌guid不能为空")
    private String brandGuid;

    @ApiModelProperty(value = "搜索条件，商品名称")
    private String searchKey;

    @ApiModelProperty(value = "排除已选择的商品规格guid")
    private List<String> excludeSkuGuidList;
}
