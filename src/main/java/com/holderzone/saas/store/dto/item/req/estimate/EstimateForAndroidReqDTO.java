package com.holderzone.saas.store.dto.item.req.estimate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 安卓手动估清商品请求DTO
 * @date 2022/4/12 10:04
 * @className: EstimateForAndroidReqDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "安卓手动估清商品请求DTO")
public class EstimateForAndroidReqDTO implements Serializable {

    private static final long serialVersionUID = 2267227421922146198L;

    @NotBlank(message = "商品Guid不能为空")
    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "估清规格信息列表")
    private List<EstimateSkuReqDTO> skuReqList;

    @NotBlank(message = "门店guid不能为空")
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @NotBlank(message = "设备id不能为空")
    @ApiModelProperty(value = "设备id")
    private String deviceId;
}
