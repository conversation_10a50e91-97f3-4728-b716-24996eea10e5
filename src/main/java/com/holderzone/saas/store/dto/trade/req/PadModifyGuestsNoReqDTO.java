package com.holderzone.saas.store.dto.trade.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 修改就餐人数请求实体
 * @date 2021/9/7 18:27
 * @className: PadModifyGuestsNoReqDTO
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "修改就餐人数请求实体")
public class PadModifyGuestsNoReqDTO implements Serializable {

    private static final long serialVersionUID = 7407679043222435883L;

    /**
     * 就餐人数
     */
    @ApiModelProperty("就餐人数")
    private Integer actualGuestsNo;

    /**
     * 交易订单号
     */
    @ApiModelProperty("订单guid")
    private String orderGuid;
}
