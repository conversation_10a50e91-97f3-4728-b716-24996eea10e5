package com.holderzone.saas.store.dto.order.response.daily;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("属性销售统计(聚合结果)")
public class PropStatsDTO {

    @ApiModelProperty(value = "属性组列表")
    private List<PropGroup> propGroupList;

    @ApiModelProperty(value = "全部数量合计")
    private long totalQuantity;

    @ApiModelProperty(value = "全部金额合计")
    private BigDecimal totalMoney;
    @Data
    public static class PropGroup {

        @ApiModelProperty(value = "属性组名称")
        private String name;

        @ApiModelProperty(value = "属性列表")
        private List<PropItem> propList;

        @ApiModelProperty(value = "属性组数量合计")
        private long groupQuantity;
        
        @ApiModelProperty(value = "属性组金额合计")
        private BigDecimal groupMoney;

    }

    @Data
    public static class PropItem {

        @ApiModelProperty(value = "属性名称")
        private String name;

        @ApiModelProperty(value = "属性销售数量")
        private Long quantity;

        @ApiModelProperty(value = "单价")
        private BigDecimal unitPrice;

        @ApiModelProperty(value = "属性销售金额")
        private BigDecimal money;

        @ApiModelProperty(value = "属性列表")
        private List<PropItem> propList;
    }
}
