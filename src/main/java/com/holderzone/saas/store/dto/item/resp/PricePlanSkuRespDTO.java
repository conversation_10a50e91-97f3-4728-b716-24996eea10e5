package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 方案菜品分页查询规格返回
 * @date 2021/6/1 11:25
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "方案菜品分页查询规格返回")
public class PricePlanSkuRespDTO {

    @ApiModelProperty(value = "方案菜品guid")
    private String planItemGuid;

    @ApiModelProperty(value = "商品编码")
    private String code;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "规格")
    private String skuName;

    @ApiModelProperty(value = "规格")
    private String skuGuid;

    @ApiModelProperty(value = "商品原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "方案销售价")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "方案会员价")
    private BigDecimal memberPrice;

    @ApiModelProperty("堂食核算价")
    private BigDecimal accountingPrice;

    @ApiModelProperty("外卖核算价")
    private BigDecimal takeawayAccountingPrice;

    @ApiModelProperty("划线价")
    private BigDecimal linePrice;

    @ApiModelProperty("是否售完下架 0:上架 1：售完下架 2：立即下架（直接删除） 3：即将下架 ")
    private Integer isSoldOut;

    @ApiModelProperty(value = "商品描述")
    private String description;

    @ApiModelProperty(value = "图片路径数组json")
    private String pictureUrl;

    /**
     * 图片路径数组json,这里对应新一期加入的“列表大图”
     */
    @ApiModelProperty(value = "商品图片地址，列表大图")
    private String bigPictureUrl;

    /**
     * 图片路径数组json,这里对应新一期加入的“详情大图”
     */
    @ApiModelProperty(value = "商品图片地址，详情大图")
    private String detailBigPictureUrl;

    /**
     * 起卖数(非称重即为整数，称重即为小数)
     */
    @ApiModelProperty(value = "起卖数(非称重即为整数，称重即为小数)")
    private BigDecimal minOrderNum;

    /**
     * 是否套餐
     */
    private Boolean isPkgItem;

    @ApiModelProperty(value = "商品分类guid")
    private String typeGuid;
    @ApiModelProperty(value = "商品分类名称")
    private String typeName;
    @ApiModelProperty(value = "商品排序")
    private Integer sort;
    @ApiModelProperty(value = "菜谱方案商品名称")
    private String planItemName;

    private String storeGuid;

    /**
     * 商品分类列表JSON串
     */
    @ApiModelProperty(value = "商品分类列表JSON串")
    private String typeListJson;
}
