package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MenuDO
 * @date 19-1-22 下午5:10
 * @description
 * @program holder-saas-store-staff
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class MenuDTO {
    @ApiModelProperty("菜单guid")
    private String menuGuid;

    @ApiModelProperty("菜单名称")
    private String menuName;

    @ApiModelProperty("菜单地址")
    private String menuUrl;

    @ApiModelProperty("菜单排序值")
    private Long menuSort;

    @ApiModelProperty("上级菜单ids（逗号分割，最顶级为终端guid）")
    private String parentIds;

    @ApiModelProperty("菜单图标")
    private String menuIcon;

    @ApiModelProperty("模块guid")
    private String moduleGuid;

    @ApiModelProperty("跳转页标题")
    private String pageTitle;

    @ApiModelProperty("跳转页url")
    private String pageUrl;

    @ApiModelProperty("是否显示（默认为1-显示，0-不显示）")
    private Boolean isEnable;

    @ApiModelProperty("下级菜单")
    private List<MenuDTO> menus;

    @ApiModelProperty("该菜单下的服务资源信息，只有最下级的菜单中服务资源才不为空")
    private List<SourceDTO> sourceDTOList;

    @ApiModelProperty("该菜单下已选中的资源服务信息（前端需要）")
    private List<String> isCheckedList;
}
