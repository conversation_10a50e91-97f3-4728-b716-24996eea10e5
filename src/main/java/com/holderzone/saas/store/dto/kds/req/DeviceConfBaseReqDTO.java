package com.holderzone.saas.store.dto.kds.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class DeviceConfBaseReqDTO implements Serializable {

    private static final long serialVersionUID = -2782247301702252131L;

    @NotEmpty(message = "设备Guid不得为空")
    @ApiModelProperty(value = "设备Guid")
    private String deviceId;
}
