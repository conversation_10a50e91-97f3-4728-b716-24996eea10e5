package com.holderzone.saas.store.dto.erp;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;

/**
 * 库存查询DTO
 *
 * <AUTHOR>
 * @date 2019/05/10 11:18
 */
public class StockQueryDTO extends BasePageDTO {
    @ApiModelProperty("分类GUID")
    private String category;
    @ApiModelProperty("搜索条件")
    private String searchConditions;
    @ApiModelProperty("仓库GUID")
    private String warehouseGuid;

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSearchConditions() {
        return searchConditions;
    }

    public void setSearchConditions(String searchConditions) {
        this.searchConditions = searchConditions;
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }
}
