package com.holderzone.saas.store.dto.member.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 兑换积分查询实体
 * @date 2021/7/16 10:05
 */
@ApiModel("兑换积分查询实体")
@Data
public class ConversionIntegralQuery {

    @ApiModelProperty("商品GuidList")
    private List<String> itemGuidList;

    @ApiModelProperty("规格GuidList")
    private List<String> skuGuidList;

    @ApiModelProperty("门店guid")
    private String storeGuid;
}
