package com.holderzone.saas.store.dto.report.openapi;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 销售明细订单列表
 */
@Data
public class SaleDetailRespDTO implements Serializable {

    private static final long serialVersionUID = -1025167689243809355L;

    /**
     * 订单guid
     */
    @JsonIgnore
    private String orderGuid;

    /**
     * 门店guid
     */
    private String storeGuId;

    /**
     * 门店名称
     */
    private String storeName;

    private String posNO;

    /**
     * 结账人
     */
    private String checkoutStaffName;

    /**
     * 结账时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkoutTime;

    /**
     * 销售金额
     */
    private Long orderFee;

    /**
     * 销售笔数
     */
    private Integer saleCount;

    /**
     * 优惠金额
     */
    private Long discountFee;

    /**
     * 实收金额
     */
    private Long actuallyPayFee;

    /**
     * 小票号
     * 订单号
     */
    private String orderNo;

    /**
     * 原订单号，退货传
     */
    private String originalOrderNo;

    /**
     * 交易类型:0销售,1退货
     */
    private Integer receiptType;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 会员手机号
     */
    private String memberPhone;

    /**
     * 设备类型
     *
     * @see com.holderzone.saas.store.enums.BaseDeviceTypeEnum
     */
    private Integer deviceType;

    /**
     * 下单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime gmtModified;


    /**
     * 商品明细
     */
    private List<SaleProductDetailRespDTO> productDetails;

    /**
     * 支付明细
     */
    private List<SalePayDetailRespDTO> payDetails;

}
