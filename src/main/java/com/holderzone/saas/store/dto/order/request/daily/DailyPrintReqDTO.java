package com.holderzone.saas.store.dto.order.request.daily;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DailyPrintReqDTO
 * @date 2019/02/19 10:40
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class DailyPrintReqDTO extends DailyReqDTO {
    @ApiModelProperty(value = "日报类型:1营业概况 2收款统计 3会员消费统计 4用餐类型统计 5分类销售统计 6商品销售统计 7属性销售统计 8退菜统计 9赠菜统计 10会员充值统计（拆分） 11会员消费统计（拆分）12退款统计")
    @NotNull(message = "日报类型不能为空")
    private Integer type;
}