package com.holderzone.saas.store.dto.trade;

import com.holderzone.saas.store.dto.user.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillQuery
 * @date 2018/09/15 15:16
 * @description
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class BillQuery extends BaseBillQuery {

    /**
     * 账单业务主键
     */
    @ApiModelProperty(value = "账单业务主键")
    private String billGuid;

    /**
     * 桌台guid
     */
    @ApiModelProperty(value = "桌台guid")
    private String diningTableGuid;

    /**
     * 交易模式 0：堂食 1：快销  2：外卖
     */
    @ApiModelProperty(value = "交易模式，0：堂食 1：快销  2：外卖")
    private Integer tradeMode;

    /**
     * 反结账员工guid
     */
    @ApiModelProperty(value = "反结账员工guid")
    private String recoveryStaffGuid;

    /**
     * 状态
     */
    @ApiModelProperty(value = "账单状态")
    private Integer state;
    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;
    /**
     * 反结账guid
     */
    @ApiModelProperty(value = "反结账guid")
    private String recoveryUuid;
    /**
     * 时间查询类型
     */
    @ApiModelProperty(value = "0:创建时间，1:结算完成时间")
    private Integer billQueryTimeType;

}
