package com.holderzone.saas.store.dto.trade.req.adjust;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 创建调整单DTO
 */
@Data
public class AdjustOrderReqDTO extends BaseDTO {

    private static final long serialVersionUID = -2566037494096470408L;

    @NotNull(message = "设备类型不能为空")
    @ApiModelProperty(value = "调整单设备类型")
    private Integer deviceType;

    @NotNull(message = "交易模式不能为空")
    @ApiModelProperty(value = "交易模式(0：正餐，1：快餐，2：外卖)")
    private Integer tradeMode;

    @NotNull(message = "订单guid不能为空")
    @ApiModelProperty("订单guid")
    private Long orderGuid;

    @NotNull(message = "调整金额不能为空")
    @ApiModelProperty("调整金额")
    private BigDecimal adjustPrice;

    @NotBlank(message = "调整原因不能为空")
    @ApiModelProperty("调整原因")
    private String reason;

    @NotNull(message = "调整单金额不能为空")
    @ApiModelProperty("调整后金额")
    private BigDecimal orderFee;

    @Valid
    @NotEmpty(message = "调整商品明细不能为空")
    @ApiModelProperty(value = "调整商品明细")
    private List<AdjustOrderItem> orderItemList;


    @Data
    public static class AdjustOrderItem implements Serializable {

        private static final long serialVersionUID = -3934794280227863839L;

        @NotNull(message = "订单商品明细guid不能为空")
        @ApiModelProperty("订单商品明细guid")
        private String orderItemGuid;

        @NotNull(message = "调整类型不能为空")
        @ApiModelProperty("调整类型(0.数量调整，1.菜品更换)")
        private Integer adjustType;

        @ApiModelProperty("调整数量(当前调整为数量调整时，该字段有值)")
        private BigDecimal adjustCount;

        @ApiModelProperty("菜品更换的商品明细(当前调整为菜品更换时，该集合有值)")
        private List<DineInItemDTO> itemList;
    }

    /**
     * 冗余字段，不由前端传，由预处理请求获取
     */
    @ApiModelProperty("订单流水号")
    private String orderNo;

    @ApiModelProperty("营业日")
    private LocalDate businessDay;

    @ApiModelProperty("订单状态(1：未结账， 2：已结账， 3：已退款，4：已作废)")
    private Integer state;

    @ApiModelProperty(value = "订单商品信息")
    private List<DineInItemDTO> dineInItemDTOS;

    @ApiModelProperty(value = "外卖订单信息(冗余)")
    private TakeoutOrderDTO takeoutOrderDetail;

}
