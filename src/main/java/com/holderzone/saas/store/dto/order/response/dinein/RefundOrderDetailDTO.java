package com.holderzone.saas.store.dto.order.response.dinein;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 退款信息
 */
@Data
public class RefundOrderDetailDTO implements Serializable {

    private static final long serialVersionUID = 6623841514000761571L;

    @ApiModelProperty(value = "退款订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "退款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime refundTime;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "退款方式")
    private String refundTypes;

    @ApiModelProperty(value = "是否全部退款")
    private Boolean residueFlag;

}
