package com.holderzone.saas.store.dto.user.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023年07月26日 12:26
 * @description 员工人脸信息
 */
@Data
public class UserFaceDTO implements Serializable {

    private static final long serialVersionUID = 8622381478811177358L;

    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 员工guid
     */
    @ApiModelProperty(value = "员工guid")
    private String userGuid;

    @ApiModelProperty(value = "员工姓名")
    private String userName;

    @ApiModelProperty(value = "员工账号")
    private String account;

    /**
     * 人脸识别码
     */
    @ApiModelProperty(value = "人脸识别码")
    private String faceCode;

    /**
     * 是否人脸录入
     */
    @ApiModelProperty(value = "是否人脸录入")
    private Boolean isInputFace;
}
