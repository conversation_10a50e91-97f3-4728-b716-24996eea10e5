package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Api("会员卡列表")
@Accessors(chain = true)
public class MemberCardListItemDTO {

	@ApiModelProperty("会员持卡GUID")
	private String memberInfoCardGuid;
	@ApiModelProperty("卡名称")
	private String cardName;
	@ApiModelProperty("卡LOGO")
	private String cardLogo;
	@ApiModelProperty("卡片图片路径")
	private String cardIcon;
	@ApiModelProperty("卡颜色")
	private String cardColour;
	@ApiModelProperty("有效期类型，0永久有效，2固定时间")
	private Integer validityType;
	@ApiModelProperty("开始有效期")
	private String cardStartDate;
	@ApiModelProperty("结束有效期")
	private String cardEndDate;
	@ApiModelProperty("卡类型,0成长型(默认卡),1荣誉型,2付费型,3实体卡")
	private Integer cardType;
	@ApiModelProperty("卡余额")
	private BigDecimal cardMoney;
	@ApiModelProperty("积分")
	private String cardIntegral;
	@ApiModelProperty("卡成长值")
	private String cardGrowthValue;
	@ApiModelProperty("卡等级GUID")
	private String cardLevelGuid;
	@ApiModelProperty("卡等级名字")
	private String cardLevelName;
	@ApiModelProperty("卡等级编号,V-(n)")
	private Integer cardLevelNum;
	@ApiModelProperty("等级图标路径")
	private String levelIcon;
	@ApiModelProperty(value = "1:选中")
	private Integer uck=0;
}
