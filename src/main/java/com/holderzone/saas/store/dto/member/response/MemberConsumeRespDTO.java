package com.holderzone.saas.store.dto.member.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberConsumeRespDTO
 * @date 2018/09/29 9:42
 * @description
 * @program holder-saas-store-dto
 */
@ApiModel
@Data
public class MemberConsumeRespDTO implements Serializable {


    @ApiModelProperty(value = "结算员工name")
    private String checkoutStaffName;


    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    /**
     * 结算完成时间
     */
    @ApiModelProperty(value = "结算完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime checkoutedTimestamp;

    /**
     * 总折扣=couponsFee+itemTotalDiscountFee+memberDiscountFee（或者wholeOrderDiscountFee）+memberScoreDiscountFee+modifyDirectlyFee
     */
    @ApiModelProperty(value = "总折扣")
    private BigDecimal totalDiscountFee;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应收金额")
    private BigDecimal shouldPayFee;

    /**
     * 实付金额
     */
    @ApiModelProperty(value = "实收金额")
    private BigDecimal actuallyPayFee;

    @ApiModelProperty(value = "-1 全部 0:正餐，1:快餐，2:外卖")
    private Integer tradeMode;

    @ApiModelProperty(value = "门店name")
    private String storeName;

}
