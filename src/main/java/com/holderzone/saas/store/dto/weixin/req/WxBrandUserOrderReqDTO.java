package com.holderzone.saas.store.dto.weixin.req;

import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("我的就餐查询请求入参")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WxBrandUserOrderReqDTO {

	private WxStoreConsumerDTO wxStoreConsumerDTO;

	@ApiModelProperty(value = "就餐类型,不传表示查询所有")
	private Integer orderState;
}
