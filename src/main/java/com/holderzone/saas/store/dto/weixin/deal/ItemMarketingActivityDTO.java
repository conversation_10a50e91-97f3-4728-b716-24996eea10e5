package com.holderzone.saas.store.dto.weixin.deal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/26
 * @description 商品营销活动
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "商品营销活动")
public class ItemMarketingActivityDTO implements Serializable {

    private static final long serialVersionUID = 5183106158168157006L;

    /**
     * 特价类型 1打折 2减价 3指定价格
     */
    @ApiModelProperty(value = "特价类型 1打折 2减价 3指定价格")
    private Integer specialsType;

    /**
     * 特价数额
     */
    @ApiModelProperty(value = "特价数额")
    private BigDecimal specialsNumber;

    /**
     * 优惠限购
     * 为空表示不限制
     */
    @ApiModelProperty(value = "优惠限购,为空表示不限制")
    private Integer limitNumber;

}
