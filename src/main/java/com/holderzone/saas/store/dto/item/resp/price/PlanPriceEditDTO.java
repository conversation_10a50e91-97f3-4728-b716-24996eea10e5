package com.holderzone.saas.store.dto.item.resp.price;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalTime;

/**
 * <AUTHOR>
 * @description
 * @date 2021/9/28
 */
@Data
public class PlanPriceEditDTO {

    @ApiModelProperty(value = "方案GUID")
    private String planGuid;

    @ApiModelProperty(value = "方案名称")
    private String planName;

    /**
     * 方案状态-0 未启用 1 已启用 2暂不启用 3永久停用 4即将启用
     *
     */
    private Integer status;

    /***
     *  售卖类型-0 默认（全时段）-1特殊时段
     */
    private Integer sellTimeType;

    private String brandGuid;

    /***
     *  起始时间
     */
    private LocalTime startTime;
    /***
     *  结束时间
     */
    private LocalTime endTime;

    /**
     * 分类guid
     */
    private String typeGuid;
}
