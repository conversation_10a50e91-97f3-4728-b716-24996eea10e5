package com.holderzone.saas.store.dto.item.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> R
 * @date 2021/3/12 14:32
 * @description
 */
@ApiModel(value = "门店关系校验返回对象")
@Data
public class PricePlanStoreCheckRespDTO {
    @ApiModelProperty(value = "关联门店GUID")
    private String storeGuid;
    @ApiModelProperty(value = "是否默认")
    private Boolean isDefault;
    @ApiModelProperty(value = "是否重叠")
    private Boolean isOverlapping;
}
