package com.holderzone.saas.store.dto.weixin.open;

import com.holderzone.saas.store.dto.weixin.req.WxCommonReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxMessageHandleReqDTO
 * @date 2019/03/26 17:48
 * @description 微信消息处理请求DTO
 * @program holder-saas-store
 */
@ApiModel("微信消息处理请求DTO")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class WxMessageHandleReqDTO {

    @ApiModelProperty("请求body，xml格式")
    private String body;

    @ApiModelProperty("该消息对应的公众号appId")
    private String appId;

    @ApiModelProperty("消息公共属性")
    private WxCommonReqDTO wxCommonReqDTO;
}
