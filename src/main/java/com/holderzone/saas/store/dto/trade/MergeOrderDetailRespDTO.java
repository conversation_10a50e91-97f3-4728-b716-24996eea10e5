package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 商户后台/数据报表/订单统计/明细 并单信息DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/27
 */
@Data
public class MergeOrderDetailRespDTO {

    @ApiModelProperty(value = "订单GUID")
    private String guid;

    @ApiModelProperty(value = "并单状态 0:无子单，1:主单， 2:子单")
    private Integer upperState;

    @ApiModelProperty(value = "桌台名称")
    private String diningTableName;

}