package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseItemSingleDTO
 * @date 2021/4/13 上午10:07
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class BaseItemSingleDTO {

    private static final long serialVersionUID = 1727528918478527413L;

    @ApiModelProperty(value = "最主要的业务请求参数")
    private String data;

    @ApiModelProperty(value = "最主要的业务请求参数批量版")
    private List<String> dataList;

    @ApiModelProperty(value = "查询关键字")
    private String keywords;

    @ApiModelProperty(value = "初始化数据时 0：门店  1：品牌")
    private Integer model;

    @ApiModelProperty(value = "商品查询类型：0 全部类型，1 宴会套餐")
    private Integer itemQueryType;
}
