package com.holderzone.saas.store.dto.ludou;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 麓豆会员支付 请求
 */
@Data
@Builder
public class LudouMemberPayDTO implements Serializable {

    private static final long serialVersionUID = -2806992746246547783L;

    /**
     * 设备编号
     */
    private String deviceNo;

    /**
     * 麓豆支付金额
     */
    private BigDecimal ludouAmount;

    /**
     * 总支付金额
     */
    private BigDecimal totalAmount;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 交易编号
     */
    private String outTradeNo;

    /**
     * 用户ID
     */
    private String userId;
}
