package com.holderzone.saas.store.dto.print.content;

import com.holderzone.saas.store.dto.print.content.base.PrintDataMockito;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.dto.print.content.nested.MultiMemberPayRecord;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

import static com.holderzone.saas.store.dto.print.util.PrintMockUtils.*;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("并台结帐单单")
public class PrintCoTableCbDTO extends PrintDTO implements PrintDataMockito {

    @NotBlank(message = "门店名不能为空")
    @ApiModelProperty(value = "门店名")
    private String storeName;

    @Valid
    @NotEmpty(message = "并台订单列表不得为空")
    @ApiModelProperty(value = "并台订单列表", required = true)
    private List<PrintTableDTO> tableOrderList;

    @NotNull(message = "并台合计金额不能为空")
    @ApiModelProperty(value = "并台合计金额", required = true)
    private BigDecimal tableTotal;

    @Valid
    @Nullable
    @ApiModelProperty(value = "附加费", required = true)
    private List<AdditionalCharge> additionalChargeList;

    @Valid
    @Nullable
    @ApiModelProperty(value = "优惠折扣", required = true)
    private List<ReduceRecord> reduceRecordList;

    @NotNull(message = "应付金额不能为空")
    @ApiModelProperty(value = "应付金额", required = true)
    private BigDecimal payAble;

    @Valid
    @NotEmpty(message = "支付方式不能为空")
    @ApiModelProperty(value = "支付方式", required = true)
    private List<PayRecord> payRecordList;

    @NotNull(message = "找零不能为空")
    @ApiModelProperty(value = "找零", required = true)
    private BigDecimal changedPay;

    @NotNull(message = "实付金额不能为空")
    @ApiModelProperty(value = "实付金额", required = true)
    private BigDecimal actuallyPay;

    @Nullable
    @ApiModelProperty(value = "门店地址", notes = "如果未设置门店地址，该值为空")
    private String storeAddress;

    @Nullable
    @ApiModelProperty(value = "门店电话", notes = "如果未设置门店电话，该值为空")
    private String tel;

    @ApiModelProperty(value = "会员姓名")
    private String memberName;

    @ApiModelProperty(value = "会员手机号")
    private String memberPhone;

    @ApiModelProperty(value = "支付卡余额")
    private BigDecimal memberCardBalance;

    @ApiModelProperty(value = "会员本次消费实充金额")
    private BigDecimal memberRechargeAmount;

    @ApiModelProperty(value = "会员本次消费赠送金额")
    private BigDecimal memberGiftAmount;

    @ApiModelProperty(value = "会员本次消费补贴金额")
    private BigDecimal memberSubsidyAmount;

    @ApiModelProperty(value = "会员多卡/单卡支付信息")
    private List<MultiMemberPayRecord> multiMemberPayRecords;

    /**
     * 挂账信息
     */
    @ApiModelProperty(value = "挂账单位名称")
    private String debtUnitName;

    @ApiModelProperty(value = "挂账联系人")
    private String debtContactName;

    @ApiModelProperty(value = "挂账单位电话")
    private String debtContactTel;

    @ApiModelProperty(value = "多单结账其他子单信息")
    private List<PrintCoTableCbDTO> otherPrintCoTableCbs;

    @Override
    public void applyMock() {
        super.applyMock();
        setStoreName(mockStoreName());
        setTableOrderList(mockNowTableOrders());
        setTableTotal(mockTotal());
        setAdditionalChargeList(mockAdditionalChargeList());
        setReduceRecordList(mockReduceRecordList());
        setPayAble(mockPayable());
        setChangedPay(mockPayChanged());
        setActuallyPay(mockActuallyPay());
        setPayRecordList(mockPayRecords());
        setStoreAddress(mockStoreAddress());
        setTel(mockStoreTel());
        setMemberName(mockMemberName());
        setMemberPhone(mockMemberPhone());
        setMultiMemberPayRecords(mockMemberPayAmountRecords());
        setDebtUnitName(mockDebtUnitName());
        setDebtContactName(mockDebtContactName());
        setDebtContactTel(mockDebtContactTel());
    }

    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    @ApiModel("并台订单")
    public static class PrintTableDTO extends PrintPreCoTableCbDTO.PrintTableDTO {

        @NotNull(message = "结帐时间不能为空")
        @ApiModelProperty(value = "结帐时间", required = true)
        private Long checkOutTime;
    }
}
