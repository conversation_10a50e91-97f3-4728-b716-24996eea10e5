package com.holderzone.saas.store.dto.weixin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreMerchantOrderReqDTO
 * @date 2019/4/9
 */
@ApiModel("微信接单请求入参")
@Data
public class WxStoreMerchantOrderReqDTO {

    /**
     * 这玩意是下单表的guid
     */
    @ApiModelProperty(value = "订单唯一标识，处理订单必须")
    private String guid;

    @ApiModelProperty(value = "0:待处理，1：接单，2：拒单，3：未结帐，4：已结账,5:已做废")
    private Integer orderState;

    @ApiModelProperty(value = "0：正餐，1：快餐")
    private Integer tradeMode;

    @ApiModelProperty(value = "桌号，牌号，订单号，手机号")
    private String tableCode;

    @ApiModelProperty(value = "起始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    @ApiModelProperty(value = "上次查询的guid", notes = "用于滚动查询，刷新不传，上拉加载时传上次查询的最后一条记录的guid")
    private String lastGuid;

    @ApiModelProperty(value = "总数量大小", required = true)
    private Integer count;

}
