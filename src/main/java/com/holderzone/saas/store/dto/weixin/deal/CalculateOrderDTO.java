package com.holderzone.saas.store.dto.weixin.deal;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class CalculateOrderDTO implements Serializable {

    private static final long serialVersionUID = 2681969638533473972L;

    /**
     * @see com.holderzone.saas.store.enums.BaseDeviceTypeEnum
     */
    @ApiModelProperty(value = "0：PC服务端,1：PC平板,2：小店通,3：一体机,4：POS机,5：云平板,6：点菜宝(M1)," +
            "7：PV1(带刷卡的点菜宝),9：厨房显示系统,10: 取餐屏,12：微信 15通吃岛",
            hidden = true)
    private Integer deviceType;

    /**
     * 是否使用会员优惠
     */
    @ApiModelProperty(value = "是否使用会员优惠")
    private Boolean useMemberDiscountFlag;

    /**
     * 是否使用积分
     */
    @ApiModelProperty(value = "是否使用积分")
    private Boolean memberIntegralStore;

    @ApiModelProperty(value = "会员持卡guid")
    private String memberInfoCardGuid;

    @ApiModelProperty(value = "优惠券code list")
    private List<String> volumeCodes;

    @ApiModelProperty(value = "营销活动所选列表")
    private List<ActivitySelectDTO> activitySelectList;

    @ApiModelProperty(value = "商品明细")
    @NotEmpty(message = "商品明细不能为空")
    private List<DineInItemDTO> dineInItemList;

    /**
     * 是否首次进入
     */
    @ApiModelProperty(value = "是否首次进入")
    private Boolean isFirst;

    @ApiModelProperty(value = "是否替换")
    private Boolean isReplace;

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "1：验券，2：撤销，3：查询（不验券，只给微信用）")
    private Integer verify;

}
