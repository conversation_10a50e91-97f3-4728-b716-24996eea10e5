package com.holderzone.saas.store.dto.marketing.nth;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 第N份优惠商品
 */
@Data
public class NthActivityItemDTO implements Serializable {

    private static final long serialVersionUID = -1828523635280753827L;

    /**
     * 商品id
     */
    @ApiModelProperty(value = "商品id")
    private String commodityId;

    /**
     * 活动guid
     */
    @ApiModelProperty(value = "活动guid")
    private String activityGuid;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    /**
     * 商品类型
     */
    @ApiModelProperty(value = "商品类型")
    private Integer commodityType;

    /**
     * 特价类型
     */
    @ApiModelProperty(value = "特价类型 1打折 2减价 3指定价格")
    private Integer specialsType;

    /**
     * 商品渠道
     */
    @ApiModelProperty(value = "商品渠道")
    private String channel;

    /**
     * 商品是否存在 0否 1是
     */
    @ApiModelProperty(value = "商品是否存在 0否 1是")
    private Integer isExist;

}
