package com.holderzone.saas.store.dto.erp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/29 上午 11:02
 * @description
 */
@ApiModel("出入库的用于添加或者更新的实体")
public class InOutDocumentAddOrUpdateDTO extends BaseDTO {

    @ApiModelProperty("出入库实体的标识")
    private String guid;

    @ApiModelProperty("供应商Guid")
    private String supplierGuid;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("关联单据")
    private String contactDocumentGuid;

    @ApiModelProperty("纸质编码")
    private String code;

    @ApiModelProperty("仓库Guid")
    private String warehouseGuid;

    @ApiModelProperty("仓库名称")
    private String warehouseName;

    @ApiModelProperty("单据门店guid")
    private String documentStoreGuid;

    @ApiModelProperty("单据日期")
    @JsonFormat(pattern = "yyyy-MM-dd" , timezone = "Asia/Shanghai")
    private Date documentDate;

    @ApiModelProperty("单据类型(0:采购入库，1：其他入库，2:盘盈入库单，10:销售出库, 11:退货出库, 12:其他出库，13:盘亏出库单)")
    private Integer type;

    @ApiModelProperty("物料总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("应付金额")
    private BigDecimal shouldPayAmount;

    @ApiModelProperty("经办人Guid")
    private String operatorGuid;

    @ApiModelProperty("经办人名称")
    private String operatorName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("单据明细")
    private List<InOutDocumentDetailAddOrUpdateDTO> detailList;

    @ApiModelProperty("出入库类型(0:入库，1：出库)")
    private Integer inOutType;

    public String getDocumentStoreGuid() {
        return documentStoreGuid;
    }

    public void setDocumentStoreGuid(String documentStoreGuid) {
        this.documentStoreGuid = documentStoreGuid;
    }

    public String getContactDocumentGuid() {
        return contactDocumentGuid;
    }

    public void setContactDocumentGuid(String contactDocumentGuid) {
        this.contactDocumentGuid = contactDocumentGuid;
    }

    public Integer getInOutType() {
        return inOutType;
    }

    public void setInOutType(Integer inOutType) {
        this.inOutType = inOutType;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public List<InOutDocumentDetailAddOrUpdateDTO> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<InOutDocumentDetailAddOrUpdateDTO> detailList) {
        this.detailList = detailList;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getSupplierGuid() {
        return supplierGuid;
    }

    public void setSupplierGuid(String supplierGuid) {
        this.supplierGuid = supplierGuid;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }

    public Date getDocumentDate() {
        return documentDate;
    }

    public void setDocumentDate(Date documentDate) {
        this.documentDate = documentDate;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getShouldPayAmount() {
        return shouldPayAmount;
    }

    public void setShouldPayAmount(BigDecimal shouldPayAmount) {
        this.shouldPayAmount = shouldPayAmount;
    }

    public String getOperatorGuid() {
        return operatorGuid;
    }

    public void setOperatorGuid(String operatorGuid) {
        this.operatorGuid = operatorGuid;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
