package com.holderzone.saas.store.dto.zhuancan;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/23
 * @description 赚餐订单消息
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "赚餐订单消息", description = "赚餐订单消息")
public class ZhuanCanOrderMessageDTO implements Serializable {

    private static final long serialVersionUID = -272308289820372568L;

    @ApiModelProperty(value = "赚餐订单号")
    private String zhuanCanOrderSn;

    @ApiModelProperty(value = "订单已出餐商品列表")
    private List<ZhuanCanOrderItemMessageDTO> orderItemList;

}
