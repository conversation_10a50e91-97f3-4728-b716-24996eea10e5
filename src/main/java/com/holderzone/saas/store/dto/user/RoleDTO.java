package com.holderzone.saas.store.dto.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className RoleDTO
 * @date 19-1-15 下午2:08
 * @description 角色DTO
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("角色DTO")
public class RoleDTO implements Serializable {

    private static final long serialVersionUID = 7941342064924506403L;

    @NotBlank(message = "更新时角色guid不能为空", groups = RoleDTO.Update.class)
    @ApiModelProperty(value = "角色guid（更新时必传）")
    private String guid;

    @NotBlank(message = "角色名称不能为空", groups = {RoleDTO.Update.class, RoleDTO.Create.class})
    @ApiModelProperty(value = "角色名称", required = true)
    private String name;

    @ApiModelProperty(value = "是否启用，1-已启用，0-未启用")
    private Boolean isEnable;

    @ApiModelProperty(value = "是否删除，1-已删除，0-未删除")
    private Boolean isDeleted;

    @ApiModelProperty(value = "创建人guid")
    private String createStaffGuid;

    @ApiModelProperty(value = "更新人guid")
    private String modifiedStaffGuid;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "角色可用的登陆方式")
    private List<String> enableLoginSourceList;

    public interface Update {};

    public interface Create {};
}
