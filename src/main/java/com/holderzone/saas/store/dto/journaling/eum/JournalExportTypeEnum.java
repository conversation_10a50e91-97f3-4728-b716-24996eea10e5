package com.holderzone.saas.store.dto.journaling.eum;

import com.holderzone.saas.store.dto.journaling.req.*;
import com.holderzone.saas.store.dto.journaling.resp.*;
import com.holderzone.saas.store.dto.report.query.HandOverReportQueryDTO;
import com.holderzone.saas.store.dto.report.resp.HandoverReportRespDTO;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className JournalExportTypeEnum
 * @date 2019/06/06 9:59
 * @description 商户后台1.2报表导出类型
 * @program holder-saas-store-dto
 */
public enum JournalExportTypeEnum {


    STORE_GATHER_TYPE(0, StoreGatherReportRespDTO.class, StoreGatherReportReqDTO.class,
            "date:日期," +
                    "storeCode:门店编号," +
                    "storeName:门店名称," +
                    "orderFee:应收（应收）," +
                    "discountFee:优惠金额（元）," +
                    "savingZero:系统省零（元）," +
                    "memberDiscount:会员折扣（元）," +
                    "memberCoupons:会员优惠券（元）," +
                    "itemPresent:菜品赠送（元）," +
                    "fullSinglePrice:整单让价（元）," +
                    "groupCoupons:团购券（元）," +
                    "wholeDiscount:整单折扣（元）," +
                    "actuallyPayFee:实收（元）," +
                    "orderCount:订单数," +
                    "orderAverageFee:单均（元）," +
                    "guestCount:客流量," +
                    "guestAverageFee:客均（元）", "门店汇总报表"),
    ORDER_DETAIL_TYPE(1, OrderDetailRespDTO.class, OrderDetailReqDTO.class,
            "orderNo:订单号," +
                    "stateName:订单状态," +
                    "tradeModeName:用餐类型," +
                    "guestCount:就餐人数," +
                    "itemTotalFee:商品金额," +
                    "appendFee:附加费," +
                    "orderFee:订单金额," +
                    "savingZero:系统省零," +
                    "memberDiscount:会员折扣," +
                    "memberCoupons:会员优惠券," +
                    "itemPresent:菜品赠送," +
                    "fullSinglePrice:整单让价," +
                    "groupCoupons:团购券," +
                    "wholeDiscount:整单折扣," +
                    "points:积分抵扣," +
                    "memberPrice:会员价," +
                    "singleDiscount:单品折扣," +
                    "goodsGrouper:商品券," +
                    "campaign:营销活动," +
                    "paymentTypes:付款方式," +
                    "actuallyPayFee:实收," +
                    "deviceTypeName:订单来源," +
                    "operationAccount:操作人," +
                    "storeName:门店," +
                    "gmtCreate:下单时间,"+
                    "checkOutTime:结账时间", "订单明细统计报表"),
    SALE_DETAIL_TYPE(2, SaleDetailReportRespDTO.class, SaleDetailReportReqDTO.class,
            "storeName:门店名," +
                    "orderNo:订单号," +
                    "itemName:商品名称," +
                    "itemTypeName:商品分类," +
                    "typeName:商品类型," +
                    "statusName:操作," +
                    "currentNumber:点餐数量," +
                    "feeNumber:赠菜数量," +
                    "returnNumber:退菜数量," +
                    "unit:记数单位," +
                    "costTotal:商品总额," +
                    "attrPrice:属性加价," +
                    "createTime:下单时间," +
                    "checkoutTime:结账时间", "销售明细统计报表"),

    RETAIL_ORDER_DETAIL_TYPE(3, OrderDetailReportRespDTO.class, OrderDetailReqDTO.class,
            "orderNo:订单号," +
                    "stateName:订单状态," +
                    "itemTotalFee:商品金额," +
                    "orderFee:订单金额," +
                    "itemPresent:商品赠送," +
                    "singleDiscount:单品折扣," +
                    "wholeDiscount:整单折扣," +
                    "points:积分抵扣," +
                    "savingZero:系统省零," +
                    "fullSinglePrice:整单让价," +
                    "actuallyPayFee:实收," +
                    "paymentTypes:付款方式," +
                    "deviceTypeName:订单来源," +
                    "operationAccount:操作人," +
                    "checkOutTime:时间", "订单记录统计报表"),
    RETAIL_SALE_DETAIL_TYPE(4, SaleDetailReportRespDTO.class, SingleSaleDetailReqDTO.class,
            "itemName:商品名称," +
                    "itemTypeName:商品分类," +
                    "totalNumber:数量," +
                    "unit:单位," +
                    "costTotal:金额," +
                    "orderNo:订单号," +
                    "staffName:操作者," +
                    "checkoutTime:时间", "销售统计报表"),
    SALES_VOLUME_TYPE(5, SalesVolumeStrRespDTO.class, SalesVolumeReqDTO.class,
            "itemName:商品名称," +
                    "typeName:商品分类," +
                    "itemTypeName:商品类型," +
                    "unit:单位," +
                    "salesVolume:商品销量," +
                    "refundCount:退菜量," +
                    "freeCount:赠送量," +
                    "salesAmount:销售额（元）," +
                    "discountPrice:实付金额," +
                    "grossProfitAmount:毛利润," +
                    "salesProportion:销售额占比（%）," +
                    "spotRate:点单率（%）," +
                    "dineInDiscountPrice:堂食收入金额," +
                    "salesDineInProportion:堂食收入占比（%）",
            "门店商品销量统计报表"),
    PAY_SERIAL_TYPE(6, PaySerialStatisticsRespDTO.class, PaySerialStatisticsReqDTO.class,
            "payWay:支付方式," +
                    "consumeAmount:消费," +
//                    "consumeCount:消费笔数," +
                    "storedAmount:储值," +
//                    "storedCount:储值笔数," +
                    "preOrderAmount:预订," +
//                    "preOrderCount:预订笔数," +
//                    "refundAmount:退款金额," +
//                    "refundCount:退款笔数," +
                    "actualInOut:合计,",
            "支付方式统计报表"),
    HANDOVER_TYPE(7, HandoverReportRespDTO.class, HandOverReportQueryDTO.class,
            "statisticalType:统计类型," +
                    "statistical:统计,",
            "交接班统计报表"),
    SALES_STORE_VOLUME_TYPE(8, SalesVolumeStoreStrRespDTO.class, SalesVolumeReqDTO.class,
            "brandName:品牌," +
                    "storeName:门店," +
                    "itemName:商品名称," +
                    "typeName:商品分类," +
                    "itemTypeName:商品类型," +
                    "unit:单位," +
                    "salesVolume:商品销量," +
                    "refundCount:退菜量," +
                    "freeCount:赠送量," +
                    "salesAmount:销售额（元）," +
                    "discountPrice:实付金额," +
                    "grossProfitAmount:毛利润,"+
                    "salesProportion:销售额占比（%）," +
                    "spotRate:点单率（%）," +
                    "dineInDiscountPrice:堂食收入金额," +
                    "salesDineInProportion:堂食收入占比（%）",
            "门店商品销量统计报表"),

    ;

    private Integer type;

    private Class<?> resultClzz;

    private Class<?> queryClzz;

    private String head;

    private String name;

    JournalExportTypeEnum(Integer type, Class<?> resultClzz, Class<?> queryClzz, String head, String name) {
        this.type = type;
        this.resultClzz = resultClzz;
        this.queryClzz = queryClzz;
        this.head = head;
        this.name = name;
    }

    public static String getHeadByType(Integer type) {
        return Arrays.stream(JournalExportTypeEnum.values())
                .filter(exportTypeEnum -> Objects.equals(type, exportTypeEnum.type))
                .findFirst()
                .map(JournalExportTypeEnum::getHead)
                .orElse(null);
    }

    public static Class<?> getResultClzzByType(Integer type) {
        return Arrays.stream(JournalExportTypeEnum.values())
                .filter(exportTypeEnum -> Objects.equals(type, exportTypeEnum.type))
                .findFirst()
                .map(JournalExportTypeEnum::getResultClzz)
                .orElse(null);
    }

    public static Class<?> getQueryClzzByType(Integer type) {
        return Arrays.stream(JournalExportTypeEnum.values())
                .filter(exportTypeEnum -> Objects.equals(type, exportTypeEnum.type))
                .findFirst()
                .map(JournalExportTypeEnum::getQueryClzz)
                .orElse(null);
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Class<?> getResultClzz() {
        return resultClzz;
    }

    public void setResultClzz(Class<?> resultClzz) {
        this.resultClzz = resultClzz;
    }

    public Class<?> getQueryClzz() {
        return queryClzz;
    }

    public void setQueryClzz(Class<?> queryClzz) {
        this.queryClzz = queryClzz;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static JournalExportTypeEnum getEnum(int type) {
        for (JournalExportTypeEnum typeEnum : JournalExportTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
