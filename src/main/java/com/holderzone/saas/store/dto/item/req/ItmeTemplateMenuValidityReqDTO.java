package com.holderzone.saas.store.dto.item.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItmeTemplateMenuValidityReqDTO
 * @date 2019/05/23 16:53
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "菜单执行时间")
public class ItmeTemplateMenuValidityReqDTO {

    /**
     * 业务主键
     */
    @ApiModelProperty(value = "业务主键")
    private String guid;

    /**
     * 外键 商品菜单guid
     */
    @ApiModelProperty(value = "商品菜单guid 新增不需要传")
    private String itemMenuGuid;

    /**
     *
     */
    @ApiModelProperty(value = "执行星期集合  周一到周末 0-6")
    private List<Integer> weeks;


    /**
     * 星期执行数量 ，前端无需传入
     */
    @ApiModelProperty(value = "星期执行数量 ，无需传入")
    private Integer weeksQuantum;

    /**
     *
     */
    @ApiModelProperty(value = "时间段 存时间段集合")
    private List<ItemTemplateMenuTimeReqDTO> times;

    /**
     * 星期有效时间段个数 ，前端无需传入
     */
    @ApiModelProperty(value = "星期有效时间段数量 ，无需传入")
    private Integer timesQuantum;

    /**
     * 是否逻辑删除 0：否  1：是
     */
    @Builder.Default
    @ApiModelProperty(value = "是否逻辑删除 0：否  1：是 新增不需要传")
    private Integer isDelete = 0;

}
