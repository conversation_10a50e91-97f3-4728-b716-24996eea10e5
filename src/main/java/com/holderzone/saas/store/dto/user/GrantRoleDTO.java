package com.holderzone.saas.store.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className GantRoleDTO
 * @date 18-9-28 上午9:56
 * @description 角色授权DTO
 * @program holder-saas-store-staff
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GrantRoleDTO {
    @ApiModelProperty("角色ID")
    private String roleGuid;

    @ApiModelProperty("角色code")
    private String roleCode;

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("角色描述")
    private String roleDesc;

    @ApiModelProperty("是否启用")
    private String isEnabled;

    @ApiModelProperty("终端-模块-权限列表")
    private List<RolePermissionDTO> roleTerminalDTOList;
}
