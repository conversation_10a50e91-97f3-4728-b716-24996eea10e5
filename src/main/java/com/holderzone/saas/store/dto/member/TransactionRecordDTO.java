package com.holderzone.saas.store.dto.member;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TransactionRecordDTO
 * @date 2018/08/25 下午5:05
 * @description //交易记录
 * @program holder-saas-config-center
 */
@Data
public class TransactionRecordDTO extends BasePageDTO{
    @ApiModelProperty(value = "会员Guid,必传",required = true)
    private String memberGuid;

    private String phone;

    @ApiModelProperty(value = "交易类型 0：充值，1:支付,必传",required = true)
    private Integer type;

}
