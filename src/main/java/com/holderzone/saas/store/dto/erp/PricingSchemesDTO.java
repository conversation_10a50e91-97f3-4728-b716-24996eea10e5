package com.holderzone.saas.store.dto.erp;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @className PricingSchemesDO
 * @date 2019-04-27 10:54:23
 * @description
 * @program holder-saas-store-erp
 */
public class PricingSchemesDTO {

    @ApiModelProperty(value = "主键guid")
    private String guid;
    @ApiModelProperty(value = "物料名称")
    private String materialName;
    @ApiModelProperty(value = "供应商guid")
    private String suppliersGuid;
    @ApiModelProperty(value = "物料guid")
    private String materialGuid;
    @ApiModelProperty(value = "协议单价")
    private BigDecimal dealPrice;
    @ApiModelProperty(value = "单位")
    private String dealUnit;
    @ApiModelProperty(value = "状态(0:停止供应，1:正常供应)")
    private Integer enabled;
    @ApiModelProperty(value = "0:正常，1:已删除")
    private Integer deleted;
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime gmtModified;
    @ApiModelProperty(value = "单位列表")
    private List<MaterialUnitDTO> unitList;

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public List<MaterialUnitDTO> getUnitList() {
        return unitList;
    }

    public void setUnitList(List<MaterialUnitDTO> unitList) {
        this.unitList = unitList;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getSuppliersGuid() {
        return suppliersGuid;
    }

    public void setSuppliersGuid(String suppliersGuid) {
        this.suppliersGuid = suppliersGuid;
    }

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public BigDecimal getDealPrice() {
        return dealPrice;
    }

    public void setDealPrice(BigDecimal dealPrice) {
        this.dealPrice = dealPrice;
    }

    public String getDealUnit() {
        return dealUnit;
    }

    public void setDealUnit(String dealUnit) {
        this.dealUnit = dealUnit;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }
}
