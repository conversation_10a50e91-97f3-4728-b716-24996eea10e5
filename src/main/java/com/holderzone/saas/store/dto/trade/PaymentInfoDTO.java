package com.holderzone.saas.store.dto.trade;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentInfoDTO
 * @date 2018/09/13 20:25
 * @description
 * @program holder-saas-store-dto
 */
@Data
public class PaymentInfoDTO implements Serializable {

    private static final long serialVersionUID = -4909200808408051333L;

    @ApiModelProperty(value = "聚合支付帐号guid", hidden = true)
    private String paymentInfoGuid;

    @ApiModelProperty(value = "商户guid", hidden = true)
    @NotBlank(message = "商户guid不能为空")
    private String enterpriseGuid;

    @ApiModelProperty(value = "门店guid", hidden = true)
    @NotBlank(message = "门店guid不能为空")
    private String storeGuid;

    @ApiModelProperty(value = "应用id", required = true)
    @NotBlank(message = "应用id不能为空")
    private String appId;

    @ApiModelProperty(value = "应用密钥", required = true)
    @NotBlank(message = "应用密钥不能为空")
    private String appSecret;

    /**
     * 账户名称
     */
    @ApiModelProperty(value = "账户名称")
    private String accountName;

    /**
     * 是否默认账户：0否 1是
     */
    @ApiModelProperty(value = "是否默认账户：0否 1是")
    private Integer isDefaultAccount;

    /**
     * 分流规则（以逗号分割）
     */
    @ApiModelProperty(value = "分流规则（以逗号分割）", hidden = true)
    private String diversionRules;

    /**
     * 分流规则数组
     */
    @ApiModelProperty(value = "分流规则数组")
    private List<Integer> diversionRule;

    /**
     * 聚合支付账号店员号
     */
    private String empId;
}
