package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.saas.store.kds.entity.domain.DisplayRepeatItemStoreDO;
import com.holderzone.saas.store.kds.mapper.DisplayRepeatItemStoreMapper;
import com.holderzone.saas.store.kds.service.DisplayRepeatItemStoreService;
import com.holderzone.saas.store.kds.utils.SnowFlakeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class DisplayRepeatItemStoreServiceImpl extends ServiceImpl<DisplayRepeatItemStoreMapper, DisplayRepeatItemStoreDO>
        implements DisplayRepeatItemStoreService {

    @Override
    public List<String> storeGuidListByBrandGuid(String brandGuid) {
        List<DisplayRepeatItemStoreDO> list = list(new QueryWrapper<DisplayRepeatItemStoreDO>()
                .lambda()
                .eq(DisplayRepeatItemStoreDO::getBrandGuid, brandGuid)
                .select(DisplayRepeatItemStoreDO::getStoreGuid));
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(DisplayRepeatItemStoreDO::getStoreGuid).distinct().collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void save(String brandGuid, List<String> storeGuids) {
        // 先删除
        removeByBrandGuid(brandGuid);
        if (CollectionUtils.isEmpty(storeGuids)) {
            return;
        }
        // 再添加
        List<DisplayRepeatItemStoreDO> displayRepeatItemStoreList = storeGuids.stream().map(storeGuid -> {
            DisplayRepeatItemStoreDO displayRepeatItemStoreDO = new DisplayRepeatItemStoreDO();
            displayRepeatItemStoreDO.setGuid(String.valueOf(SnowFlakeUtil.getInstance().nextId()));
            displayRepeatItemStoreDO.setBrandGuid(brandGuid);
            displayRepeatItemStoreDO.setStoreGuid(storeGuid);
            return displayRepeatItemStoreDO;
        }).collect(Collectors.toList());
        saveBatch(displayRepeatItemStoreList);
    }

    @Override
    public void removeByBrandGuid(String brandGuid) {
        remove(new UpdateWrapper<DisplayRepeatItemStoreDO>().lambda()
                .eq(DisplayRepeatItemStoreDO::getBrandGuid, brandGuid)
        );
    }
}