package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.kds.entity.domain.BindItemGroupDO;
import com.holderzone.saas.store.kds.mapper.BindItemGroupMapper;
import com.holderzone.saas.store.kds.service.BindItemGroupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class BindItemGroupServiceImpl extends ServiceImpl<BindItemGroupMapper, BindItemGroupDO>
        implements BindItemGroupService {

    @Override
    public BindItemGroupDO getByName(String storeGuid, String name) {
        QueryWrapper<BindItemGroupDO> qw = new QueryWrapper<>();
        qw.lambda().eq(BindItemGroupDO::getStoreGuid, storeGuid);
        qw.lambda().eq(BindItemGroupDO::getName, name);
        return getOne(qw);
    }

    @Override
    public List<BindItemGroupDO> listByStoreGuid(String storeGuid) {
        QueryWrapper<BindItemGroupDO> qw = new QueryWrapper<>();
        qw.lambda().eq(BindItemGroupDO::getStoreGuid, storeGuid);
        return list(qw);
    }
}
