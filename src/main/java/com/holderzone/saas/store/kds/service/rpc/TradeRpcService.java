package com.holderzone.saas.store.kds.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.item.req.ItemBarCodeReqDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = TradeRpcService.TradeItemFallBack.class)
public interface TradeRpcService {

    /**
     * 打印商品标签单
     */
    @PostMapping("/item/print/barCode")
    void printItemBarCode(@RequestBody ItemBarCodeReqDTO itemBarCodeReqDTO);

    @ApiOperation(value = "根据guid查询订单商品")
    @PostMapping("/order_item/query_item_by_guid")
    List<DineInItemDTO> queryItemByGuid(@RequestBody SingleListDTO req);

    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @GetMapping("/order_detail/find_by_order_guid")
    OrderDTO findByOrderGuid(@RequestParam("orderGuid") String orderGuid);

    /**
     * 根据订单guid查询订单商品
     *
     * @param orderGuid 订单guid
     * @return 订单商品
     */
    @ApiOperation(value = "根据订单guid查询定单商品")
    @GetMapping("/order_item/query_item_by_order_guid")
    List<DineInItemDTO> queryItemByOrderGuid(@RequestParam("orderGuid") String orderGuid);

    @Component
    @Slf4j
    class TradeItemFallBack implements FallbackFactory<TradeRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TradeRpcService create(Throwable throwable) {
            return new TradeRpcService() {

                @Override
                public void printItemBarCode(ItemBarCodeReqDTO itemBarCodeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "printItemBarCode", JacksonUtils.writeValueAsString(itemBarCodeReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<DineInItemDTO> queryItemByGuid(SingleListDTO req) {
                    log.error(HYSTRIX_PATTERN, "queryItemByGuid", JacksonUtils.writeValueAsString(req),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public OrderDTO findByOrderGuid(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "findByOrderGuid", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<DineInItemDTO> queryItemByOrderGuid(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "queryItemByOrderGuid", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }
}
