package com.holderzone.saas.store.kds.utils;

import org.slf4j.MDC;

public class TraceIdUtils {

    private TraceIdUtils() {
    }

    private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<>();

    public static final String TRACE_ID_KEY = "traceId";

    public static void setTraceId(String traceId) {
        THREAD_LOCAL.set(traceId);
        MDC.put(TRACE_ID_KEY, traceId);
    }

    public static String getTraceId() {
        return THREAD_LOCAL.get();
    }

    public static void clear() {
        THREAD_LOCAL.remove();
        MDC.clear();
    }

}
