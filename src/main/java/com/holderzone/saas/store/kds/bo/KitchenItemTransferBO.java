package com.holderzone.saas.store.kds.bo;

import com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class KitchenItemTransferBO implements Serializable {

    private static final long serialVersionUID = 3230292162403526943L;

    private List<KitchenItemDO> kitchenItemDOList;

    private List<KitchenItemAttrDO> kitchenItemAttrList;

}
