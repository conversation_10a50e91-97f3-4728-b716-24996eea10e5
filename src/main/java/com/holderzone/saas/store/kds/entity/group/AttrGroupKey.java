package com.holderzone.saas.store.kds.entity.group;

import com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO;
import lombok.Data;

import java.util.Objects;

@Data
public class AttrGroupKey {

    /**
     * 属性组Guid
     */
    private String groupGuid;

    /**
     * 属性组名称
     */
    private String groupName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AttrGroupKey that = (AttrGroupKey) o;
        return Objects.equals(groupGuid, that.groupGuid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(groupGuid);
    }

    public static AttrGroupKey of(KitchenItemAttrDO kitchenItemAttrDO) {
        AttrGroupKey attrGroupKey = new AttrGroupKey();
        attrGroupKey.setGroupGuid(kitchenItemAttrDO.getGroupGuid());
        attrGroupKey.setGroupName(kitchenItemAttrDO.getGroupName());
        return attrGroupKey;
    }
}
