package com.holderzone.saas.store.kds.bo;

import com.holderzone.saas.store.dto.kds.resp.DisplayRuleItemRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemDO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
public class KitchenItemPrepareBO implements Serializable {

    private static final long serialVersionUID = 3230292162403526943L;

    private LocalDateTime prepareTime;

    private Boolean isNeedNotification;

    private List<KitchenItemDO> kitchenItemList;

    private List<KitchenItemAttrDO> kitchenItemAttrList;

    /**
     * 设备ID -> 设备
     */
    private Map<String, DeviceConfigDO> deviceConfigInSqlMap;

    /**
     * 商品显示规则
     */
    private Map<String, DisplayRuleItemRespDTO> kitchenItemDisplayRuleMap;
}
