package com.holderzone.saas.store.kds.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemDeviceDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface KitchenItemDeviceMapper extends BaseMapper<KitchenItemDeviceDO> {

    List<KitchenItemDeviceDO> limitListByDeviceId(@Param("deviceId") String deviceId);

    void updateBatchKitchenItemGuid(@Param("list") List<KitchenItemDeviceDO> list);
}
