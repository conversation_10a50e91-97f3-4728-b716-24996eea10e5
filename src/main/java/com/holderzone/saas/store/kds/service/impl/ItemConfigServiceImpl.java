package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.dto.kds.req.ItemConfBatchUpdateReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemConfigUpdateReqDTO;
import com.holderzone.saas.store.kds.entity.domain.ItemConfigDO;
import com.holderzone.saas.store.kds.mapper.ItemConfigMapper;
import com.holderzone.saas.store.kds.mapstruct.DeviceConfigMapstruct;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import com.holderzone.saas.store.kds.service.ItemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机单据管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class ItemConfigServiceImpl extends ServiceImpl<ItemConfigMapper, ItemConfigDO> implements ItemConfigService {

    private final DistributedIdService distributedIdService;

    private final DeviceConfigMapstruct deviceConfigMapstruct;

    @Autowired
    public ItemConfigServiceImpl(DistributedIdService distributedIdService, DeviceConfigMapstruct deviceConfigMapstruct) {
        this.distributedIdService = distributedIdService;
        this.deviceConfigMapstruct = deviceConfigMapstruct;
    }

    @Override
    public void insertOrUpdateBatch(ItemConfBatchUpdateReqDTO itemConfBatchUpdateReqDTO) {
        String storeGuid = itemConfBatchUpdateReqDTO.getStoreGuid();
        List<ItemConfigUpdateReqDTO> itemConfigs = itemConfBatchUpdateReqDTO.getItemConfigs();
        List<String> skuGuidList = itemConfigs.stream()
                .map(ItemConfigUpdateReqDTO::getSkuGuid)
                .collect(Collectors.toList());
        List<ItemConfigDO> itemConfigInSql = queryBatchBySkuGuid(skuGuidList);
        List<String> existedSkuGuidList = itemConfigInSql.stream()
                .map(ItemConfigDO::getSkuGuid).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(existedSkuGuidList)) {
            ItemConfigDO itemConfigDO = new ItemConfigDO();
            itemConfigDO.setStoreGuid(storeGuid);
            itemConfigDO.setTimeout(itemConfigs.get(0).getTimeout());
            itemConfigDO.setMaxCopies(itemConfigs.get(0).getMaxCopies());
            itemConfigDO.setDisplayType(itemConfigs.get(0).getDisplayType());
            update(itemConfigDO, new LambdaQueryWrapper<ItemConfigDO>().in(ItemConfigDO::getSkuGuid, existedSkuGuidList));
        }
        itemConfigs.removeIf(req -> existedSkuGuidList.contains(req.getSkuGuid()));
        if (!CollectionUtils.isEmpty(itemConfigs)) {
            List<String> guids = distributedIdService.nextBatchPointItemGuid(itemConfigs.size());
            saveBatch(itemConfigs.stream()
                    .map(deviceConfigMapstruct::fromItemConfigUpdateReq)
                    .peek(prdPointItemDO -> {
                        prdPointItemDO.setStoreGuid(storeGuid);
                        prdPointItemDO.setGuid(guids.remove(guids.size() - 1));
                    })
                    .collect(Collectors.toList()));
        }
    }

    @Override
    public List<ItemConfigDO> queryBatchBySkuGuid(List<String> skuGudList) {
        if (CollectionUtils.isEmpty(skuGudList)) return Collections.emptyList();
        return list(new LambdaQueryWrapper<ItemConfigDO>().in(ItemConfigDO::getSkuGuid, skuGudList));
    }

    @Override
    public List<ItemConfigDO> queryBatchByStoreGuid(String storeGuid) {
        return list(new LambdaQueryWrapper<ItemConfigDO>().eq(ItemConfigDO::getStoreGuid, storeGuid));
    }
}
