package com.holderzone.saas.store.kds.entity.bo;

import com.holderzone.framework.util.Assert;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.PageDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsAttrGroupDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsItemAttrDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemTableDTO;
import com.holderzone.saas.store.kds.entity.domain.ItemConfigDO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO;
import com.holderzone.saas.store.kds.entity.group.AttrGroupKey;
import com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO;
import com.holderzone.saas.store.kds.utils.DeepCloneUtils;
import com.holderzone.saas.store.kds.utils.PageAdapter;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

public class BaseRespBO {

    protected <T> Page<T> convert2Page(PageDTO pageDTO, List<T> orderResult) {
        long currentPage = Math.max(1, pageDTO.getCurrentPage());
        long pageSize = pageDTO.getPageSize();
        int fromIndex = (int) ((currentPage - 1) * pageSize);
        int endIndex = (int) (fromIndex + pageSize);
        List<T> resultList = fromIndex > orderResult.size()
                ? Collections.emptyList()
                : orderResult.subList(fromIndex, Math.min(endIndex, orderResult.size()));
        PageAdapter<T> objectPageAdapter = new PageAdapter<>();
        objectPageAdapter.setCurrent(currentPage);
        objectPageAdapter.setSize(pageSize);
        objectPageAdapter.setTotal(orderResult.size());
        objectPageAdapter.setRecords(resultList);
        return objectPageAdapter;
    }

    protected <T> Page<T> reasonable2Page(PageDTO pageDTO, List<T> orderResult) {
        long pageSize = pageDTO.getPageSize();
        long currentPage = Math.max(1, Math.min(pageDTO.getCurrentPage(), orderResult.size() / pageSize + 1));
        int fromIndex = (int) ((currentPage - 1) * pageSize);
        int endIndex = (int) Math.min(fromIndex + pageSize, orderResult.size());
        PageAdapter<T> objectPageAdapter = new PageAdapter<>();
        objectPageAdapter.setCurrent(currentPage);
        objectPageAdapter.setSize(pageSize);
        objectPageAdapter.setTotal(orderResult.size());
        objectPageAdapter.setRecords(orderResult.subList(fromIndex, endIndex));
        return objectPageAdapter;
    }

    protected PrdDstItemDTO getPrdDstItemDTO(KitchenItemReadDO item) {
        PrdDstItemDTO prdDstItemDTO = readDoToItemResp(item);
        prdDstItemDTO.setTimeout(Optional.ofNullable(item.getItemConfig())
                .map(ItemConfigDO::getTimeout).orElse(ItemConfigDO.defaultConfig().getTimeout()));
        prdDstItemDTO.setAttrGroup(item.getAttrs().stream()
                .collect(Collectors.groupingBy(AttrGroupKey::of))
                .entrySet().stream()
                .map(this::getKdsAttrGroupDTO)
                .collect(Collectors.toList()));
        return prdDstItemDTO;
    }

    protected KdsAttrGroupDTO getKdsAttrGroupDTO(Map.Entry<AttrGroupKey, List<KitchenItemAttrDO>> attrEntry) {
        AttrGroupKey groupInfo = attrEntry.getKey();
        KdsAttrGroupDTO kdsAttrGroupDTO = new KdsAttrGroupDTO();
        kdsAttrGroupDTO.setGroupGuid(groupInfo.getGroupGuid());
        kdsAttrGroupDTO.setGroupName(groupInfo.getGroupName());
        kdsAttrGroupDTO.setAttrs(attrEntry.getValue().stream()
                .map(this::getKdsItemAttrDTO)
                .collect(Collectors.toList()));
        return kdsAttrGroupDTO;
    }

    protected KdsItemAttrDTO getKdsItemAttrDTO(KitchenItemAttrDO attr) {
        KdsItemAttrDTO kdsItemAttrDTO = new KdsItemAttrDTO();
        kdsItemAttrDTO.setAttrGuid(attr.getAttrGuid());
        kdsItemAttrDTO.setAttrName(attr.getAttrName());
        kdsItemAttrDTO.setAttrNumber(attr.getAttrNumber());
        return kdsItemAttrDTO;
    }

    private PrdDstItemDTO readDoToItemResp(KitchenItemReadDO kitchenItemReadDO) {
        PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setItemGuid(kitchenItemReadDO.getItemGuid());
        prdDstItemDTO.setItemName(kitchenItemReadDO.getItemName());
        prdDstItemDTO.setSkuGuid(kitchenItemReadDO.getSkuGuid());
        prdDstItemDTO.setSkuName(kitchenItemReadDO.getSkuName());
        prdDstItemDTO.setIsWeight(kitchenItemReadDO.getIsWeight());
        prdDstItemDTO.setCurrentCount(kitchenItemReadDO.getCurrentCount());
        prdDstItemDTO.setSkuUnit(kitchenItemReadDO.getSkuUnit());
        prdDstItemDTO.setItemRemark(kitchenItemReadDO.getItemRemark());
        prdDstItemDTO.setOrderRemark(kitchenItemReadDO.getOrderRemark());
        prdDstItemDTO.setItemAttrMd5(kitchenItemReadDO.getItemAttrMd5());
        prdDstItemDTO.setTimeout(kitchenItemReadDO.getTimeout());
        prdDstItemDTO.setUrgedTime(kitchenItemReadDO.getUrgedTime());
        prdDstItemDTO.setCallUpTime(kitchenItemReadDO.getCallUpTime());
        prdDstItemDTO.setPrepareTime(kitchenItemReadDO.getPrepareTime());
        prdDstItemDTO.setHangUpTime(kitchenItemReadDO.getHangUpTime());
        prdDstItemDTO.setAreaGuid(kitchenItemReadDO.getAreaGuid());
        prdDstItemDTO.setBatch(kitchenItemReadDO.getBatch());
        prdDstItemDTO.setDisplayRuleType(kitchenItemReadDO.getDisplayRuleType());
        prdDstItemDTO.setSort(kitchenItemReadDO.getSort());
        prdDstItemDTO.setOriginalItemSkuName(kitchenItemReadDO.getOriginalItemSkuName());
        prdDstItemDTO.setAddItemBatch(kitchenItemReadDO.getAddItemBatch());
        prdDstItemDTO.setFirstAddItemTime(kitchenItemReadDO.getFirstAddItemTime());
        prdDstItemDTO.setAddItemTime(kitchenItemReadDO.getAddItemTime());
        prdDstItemDTO.setIsAddItem(Boolean.FALSE);
        return prdDstItemDTO;
    }

    protected PrdDstItemDTO calItemBatchInfo(int splitIndex, PrdDstItemDTO prdDstItemDTO, List<KitchenItemReadDO> orderItem) {
        PrdDstItemDTO itemResult = (PrdDstItemDTO) DeepCloneUtils.cloneObject(prdDstItemDTO);
        int urgedItemNumber = 0;
        int hangedItemNumber = 0;
        boolean isCooking = false;
        LocalDateTime urgedTimeOfBatch = null;
        LocalDateTime callUpTimeOfBatch = null;
        LocalDateTime prepareTimeOfBatch = null;
        LocalDateTime hangUpTimeOfBatch = null;
        List<PrdDstItemTableDTO> kitchenItemList = new ArrayList<>();
        for (KitchenItemReadDO kitchenItemReadDO : orderItem) {
            Assert.valid(!kitchenItemReadDO.getIsWeight(), "数据处理错误，称重商品未正确处理");
            // 可制作guid列表
            PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
            kitchenItemList.add(prdDstItemTableDTO);
            prdDstItemTableDTO.setDisplayType(kitchenItemReadDO.getDisplayType());
            prdDstItemTableDTO.setOrderGuid(kitchenItemReadDO.getOrderGuid());
            prdDstItemTableDTO.setOrderDesc(kitchenItemReadDO.getOrderDesc());
            prdDstItemTableDTO.setOrderNumber(kitchenItemReadDO.getOrderNumber());
            prdDstItemTableDTO.setOrderSerialNo(kitchenItemReadDO.getOrderSerialNo());
            prdDstItemTableDTO.setIsUrged(false);
            prdDstItemTableDTO.setIsHanged(false);
            prdDstItemTableDTO.setKitchenItemGuid(kitchenItemReadDO.getGuid());
            prdDstItemTableDTO.setAreaGuid(kitchenItemReadDO.getAreaGuid());
            // 是否在制作
            if (!isCooking && kitchenItemReadDO.getKitchenState() > 4) {
                isCooking = true;
            }
            prdDstItemTableDTO.setIsAddItem(true);
            // 催菜
            LocalDateTime urgedTimeOfItem = kitchenItemReadDO.getUrgedTime();
            if (urgedTimeOfItem != null) {
                urgedItemNumber++;
                prdDstItemTableDTO.setIsUrged(true);
                if (urgedTimeOfBatch == null) {
                    urgedTimeOfBatch = urgedTimeOfItem;
                } else {
                    if (urgedTimeOfItem.compareTo(urgedTimeOfBatch) < 0) {
                        urgedTimeOfBatch = urgedTimeOfItem;
                    }
                }
            }
            // 叫起
            if (1 == kitchenItemReadDO.getItemState()) {
                LocalDateTime prepareTimeOfItem = kitchenItemReadDO.getPrepareTime();
                if (prepareTimeOfItem != null) {
                    if (prepareTimeOfBatch == null) {
                        prepareTimeOfBatch = prepareTimeOfItem;
                    } else {
                        if (prepareTimeOfItem.compareTo(prepareTimeOfBatch) < 0) {
                            prepareTimeOfBatch = prepareTimeOfItem;
                        }
                    }
                }
            }
            // 叫起
            else if (3 == kitchenItemReadDO.getItemState()) {
                LocalDateTime callUpTimeOfItem = kitchenItemReadDO.getCallUpTime();
                if (callUpTimeOfItem != null) {
                    if (callUpTimeOfBatch == null) {
                        callUpTimeOfBatch = callUpTimeOfItem;
                    } else {
                        if (callUpTimeOfItem.compareTo(callUpTimeOfBatch) < 0) {
                            callUpTimeOfBatch = callUpTimeOfItem;
                        }
                    }
                }
                LocalDateTime hangUpTimeOfItem = kitchenItemReadDO.getHangUpTime();
                if (hangUpTimeOfItem != null) {
                    if (hangUpTimeOfBatch == null) {
                        hangUpTimeOfBatch = hangUpTimeOfItem;
                    } else {
                        if (hangUpTimeOfItem.compareTo(hangUpTimeOfBatch) < 0) {
                            hangUpTimeOfBatch = hangUpTimeOfItem;
                        }
                    }
                }
            }
            // 挂起
            else {
                hangedItemNumber++;
                LocalDateTime hangUpTimeOfItem = kitchenItemReadDO.getHangUpTime();
                if (hangUpTimeOfItem != null) {
                    prdDstItemTableDTO.setIsHanged(true);
                    if (hangUpTimeOfBatch == null) {
                        hangUpTimeOfBatch = hangUpTimeOfItem;
                    } else {
                        if (hangUpTimeOfItem.compareTo(hangUpTimeOfBatch) < 0) {
                            hangUpTimeOfBatch = hangUpTimeOfItem;
                        }
                    }
                }
            }
        }
        // 显示字段
        LocalDateTime beginTime = prepareTimeOfBatch != null ? prepareTimeOfBatch
                : hangUpTimeOfBatch != null ? hangUpTimeOfBatch : DateTimeUtils.now();
        itemResult.setWaitTime((int) Duration.between(beginTime, LocalDateTime.now()).toMinutes());
        itemResult.setIsCooking(isCooking);
        itemResult.setUrgedItemNumber(urgedItemNumber);
        itemResult.setHangedItemNumber(hangedItemNumber);
        itemResult.setKitchenItemList(kitchenItemList);
        itemResult.setCurrentCount(BigDecimal.valueOf(kitchenItemList.size()));
        // 排序字段
        itemResult.setUrgedTime(urgedTimeOfBatch);
        itemResult.setCallUpTime(callUpTimeOfBatch);
        itemResult.setPrepareTime(prepareTimeOfBatch);
        itemResult.setHangUpTime(hangUpTimeOfBatch);
        itemResult.setSplitIndex(splitIndex);
        return itemResult;
    }

    protected PrdDstItemDTO calItemWeightInfo(PrdDstItemDTO prdDstItemDTO,
                                              KitchenItemReadDO kitchenItemReadDO) {
        Assert.valid(kitchenItemReadDO.getIsWeight(), "数据处理错误，非称重商品未正确处理");
        PrdDstItemDTO itemResult = (PrdDstItemDTO) DeepCloneUtils.cloneObject(prdDstItemDTO);
        // 显示字段
        LocalDateTime beginTime = kitchenItemReadDO.getPrepareTime() != null ? kitchenItemReadDO.getPrepareTime()
                : kitchenItemReadDO.getHangUpTime() != null ? kitchenItemReadDO.getHangUpTime() : DateTimeUtils.now();
        itemResult.setWaitTime((int) Duration.between(beginTime, LocalDateTime.now()).toMinutes());
        itemResult.setIsCooking(kitchenItemReadDO.getKitchenState() > 4);
        itemResult.setUrgedItemNumber(kitchenItemReadDO.getUrgedTime() != null ? 1 : 0);
        itemResult.setHangedItemNumber(2 == kitchenItemReadDO.getItemState() ? 1 : 0);
        itemResult.setCurrentCount(kitchenItemReadDO.getCurrentCount());
        PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemTableDTO.setDisplayType(kitchenItemReadDO.getDisplayType());
        prdDstItemTableDTO.setOrderGuid(kitchenItemReadDO.getOrderGuid());
        prdDstItemTableDTO.setOrderDesc(kitchenItemReadDO.getOrderDesc());
        prdDstItemTableDTO.setOrderNumber(kitchenItemReadDO.getOrderNumber());
        prdDstItemTableDTO.setOrderSerialNo(kitchenItemReadDO.getOrderSerialNo());
        prdDstItemTableDTO.setKitchenItemGuid(kitchenItemReadDO.getGuid());
        prdDstItemTableDTO.setAreaGuid(kitchenItemReadDO.getAreaGuid());
        prdDstItemTableDTO.setIsUrged(kitchenItemReadDO.getUrgedTime() != null);
        prdDstItemTableDTO.setIsHanged(kitchenItemReadDO.getHangUpTime() != null);
        itemResult.setWeightKitchenItem(prdDstItemTableDTO);
        // 排序字段
        itemResult.setUrgedTime(kitchenItemReadDO.getUrgedTime());
        itemResult.setCallUpTime(kitchenItemReadDO.getCallUpTime());
        itemResult.setPrepareTime(kitchenItemReadDO.getPrepareTime());
        itemResult.setHangUpTime(kitchenItemReadDO.getHangUpTime());
        itemResult.setSplitIndex(0);
        return itemResult;
    }
}
