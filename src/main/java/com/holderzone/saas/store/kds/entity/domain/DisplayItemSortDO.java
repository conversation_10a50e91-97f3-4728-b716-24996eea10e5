package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 菜品显示顺序配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
@Data
@Accessors(chain = true)
@TableName("hsk_display_item_sort")
public class DisplayItemSortDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableId(value = "guid", type = IdType.INPUT)
    @ApiModelProperty(value = "全局唯一主键")
    private String guid;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    @TableLogic
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    @ApiModelProperty(value = "加菜显示顺序 0按下单时间依次展示 1突出展示")
    private Integer itemSortType;

    @ApiModelProperty(value = "加菜展示方式 0加菜全部突出展示 1按照设定时间突出显示")
    private Integer itemDisplayType;

    @ApiModelProperty(value = "加菜突出显示时间设置 距第一次下单时间%s分钟后的新加菜品突出显示")
    private Integer itemIntervalTime;


}
