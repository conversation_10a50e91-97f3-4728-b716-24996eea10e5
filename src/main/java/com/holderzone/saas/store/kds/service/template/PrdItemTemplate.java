package com.holderzone.saas.store.kds.service.template;

import com.holderzone.saas.store.dto.kds.req.KdsPrintItemDTO;
import com.holderzone.saas.store.dto.kds.req.KdsPrintPrdDstDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.kds.service.print.KdsInvoiceTypeEnum;
import com.holderzone.saas.store.kds.utils.DigitalUtils;
import com.holderzone.saas.store.kds.utils.print.PrintRowUtils;
import com.holderzone.saas.store.util.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
@Slf4j
public class PrdItemTemplate extends BaseItemTemplate<KdsPrintPrdDstDTO> {
    private static final int NUMBER_SIZE = 10;

    private static final String NUMBER_PREFIX="BATCH_DISHES_";

    @Override
    public List<PrintRow> getPrintRows() {
        List<PrintRow> printRows = new ArrayList<>();

        // 单据名称
        PrintRowUtils.add(printRows, new Section()
                .addText(LocaleUtil.getMessage("production_order_invoice_header"), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));
        //添加批次信息

        KdsPrintItemDTO printDTO = getPrintDTO();
        KdsInvoiceTypeEnum invoiceType = KdsInvoiceTypeEnum.ofType(printDTO.getInvoiceType());
        if(CollectionUtils.isEmpty(printDTO.getItemRecordList())){
            log.error("制作单菜品数量为零");
        }
        if(invoiceType.getType().equals(KdsInvoiceTypeEnum.PRD_ITEM.getType())&& printDTO.getItemRecordList().size()>0){
            PrdDstItemDTO dstItemDTO =  printDTO.getItemRecordList().get(0);
            if(dstItemDTO.getBatch()!=null && dstItemDTO.getBatch()>0 && dstItemDTO.getBatch()<NUMBER_SIZE){
                String batchMessage = LocaleUtil.getMessage(NUMBER_PREFIX + dstItemDTO.getBatch());
                PrintRowUtils.add(printRows, new Section()
                        .addText(batchMessage, Font.NORMAL)
                        .setAlign(Text.Align.Center));
            }
        }
        PrintRowUtils.add(printRows, new Separator());

        printRows.addAll(super.getPrintRows());

        return printRows;
    }

    @Override
    public String getPrintFailedMsg() {
        KdsPrintPrdDstDTO printDTO = getPrintDTO();
        return "制作单" + DigitalUtils.humanize(printDTO.getOrderNumber()) + "打印失败";
    }
}
