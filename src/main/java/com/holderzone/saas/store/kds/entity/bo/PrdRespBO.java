package com.holderzone.saas.store.kds.entity.bo;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.PageDTO;
import com.holderzone.saas.store.dto.kds.ItemComparable;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleItemSortDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstOrderDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdPointDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.entity.domain.ItemConfigDO;
import com.holderzone.saas.store.kds.entity.group.OrderGroupKey;
import com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO;
import com.holderzone.saas.store.kds.entity.read.PointItemReadDO;
import com.holderzone.saas.store.kds.utils.SplitUtils;
import lombok.Builder;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Data
@Builder
public class PrdRespBO extends BaseRespBO {

    /**
     * 是否为订单模式
     */
    private Boolean isOrderMode;

    /**
     * 是否显示所有模式的商品
     */
    private Boolean isAllType;

    /**
     * 制作点高级配置
     */
    private DeviceConfigDO deviceConfig;

    /**
     * 菜品显示顺序配置
     */
    private DisplayRuleItemSortDTO itemSortDTO;

    public List<PrdPointDTO> getPrdDstPointList(List<PointItemReadDO> pointItemReadInSql) {
        return pointItemReadInSql.parallelStream()
                .map(this::getPrdDstPointDTO)
                .collect(Collectors.toList());
    }

    private PrdPointDTO getPrdDstPointDTO(PointItemReadDO pointItemReadDO) {
        PrdPointDTO prdPointDTO = new PrdPointDTO();
        prdPointDTO.setPointName(pointItemReadDO.getPointName());
        if (isOrderMode) {
            if (CollectionUtils.isEmpty(pointItemReadDO.getItems())) {
                prdPointDTO.setOrders(Collections.emptyList());
            } else {
                prdPointDTO.setOrders(getPrdDstOrderList(pointItemReadDO.getItems()));
            }
        } else {
            if (CollectionUtils.isEmpty(pointItemReadDO.getItems())) {
                prdPointDTO.setItems(Collections.emptyList());
            } else {
                prdPointDTO.setItems(getOrderPrdDstItemList(pointItemReadDO.getItems()));
            }
        }
        return prdPointDTO;
    }

    public Page<PrdDstOrderDTO> getPrdDstOrderPage(PageDTO pageDTO, List<KitchenItemReadDO> itemInfo) {
        return reasonable2Page(pageDTO, getPrdDstOrderList(itemInfo));
    }

    public List<PrdDstOrderDTO> getPrdDstOrderList(List<KitchenItemReadDO> items) {
        return items.parallelStream()
                .collect(Collectors.groupingBy(OrderGroupKey::of))
                .entrySet().parallelStream()
                .map(this::getPrdDstOrderDTO)
                .filter(Objects::nonNull)
                .sorted(ItemComparable::compareTo)
                .collect(Collectors.toList());
    }

    private PrdDstOrderDTO getPrdDstOrderDTO(Map.Entry<OrderGroupKey, List<KitchenItemReadDO>> orderEntry) {
        OrderGroupKey orderInfo = orderEntry.getKey();
        List<KitchenItemReadDO> itemInfo = orderEntry.getValue();
        PrdDstOrderDTO prdDstOrderDTO = new PrdDstOrderDTO();
        prdDstOrderDTO.setOrderGuid(orderInfo.getOrderGuid());
        prdDstOrderDTO.setOrderType(orderInfo.getDisplayType());
        prdDstOrderDTO.setOrderDesc(orderInfo.getOrderDesc());
        prdDstOrderDTO.setOrderNumber(orderInfo.getOrderNumber());
        prdDstOrderDTO.setOrderSerialNo(orderInfo.getOrderSerialNo());
        prdDstOrderDTO.setOrderRemark(orderInfo.getOrderRemark());
        List<PrdDstItemDTO> prdDstItemList = getPrdDstItemList(itemInfo);
        if (CollectionUtils.isEmpty(prdDstItemList)) {
            return null;
        }
        prdDstOrderDTO.setItems(prdDstItemList);
        PrdDstItemDTO prdDstItemDTO = prdDstItemList.get(0);
        prdDstOrderDTO.setUrgedTime(prdDstItemDTO.getUrgedTime());
        prdDstOrderDTO.setCallUpTime(prdDstItemDTO.getCallUpTime());
        prdDstOrderDTO.setPrepareTime(prdDstItemDTO.getPrepareTime());
        prdDstOrderDTO.setHangUpTime(prdDstItemDTO.getHangUpTime());
        prdDstOrderDTO.setAddItemTime(prdDstItemDTO.getAddItemTime());
        return prdDstOrderDTO;
    }

    public Page<PrdDstItemDTO> getPrdDstItemPage(PageDTO pageDTO, List<KitchenItemReadDO> itemInfo) {
        return reasonable2Page(pageDTO, getPrdDstItemList(itemInfo));
    }

    public List<PrdDstItemDTO> getPrdDstItemList(List<KitchenItemReadDO> itemInfo) {
        Function<KitchenItemReadDO, String> classifier = isOrderMode
                ? KitchenItemReadDO::getOrderItemGuid : KitchenItemReadDO::getItemAttrMd5;
        AtomicInteger globalIndex = new AtomicInteger(0);
        return itemInfo.parallelStream()
                .collect(Collectors.groupingBy(classifier))
                .values().parallelStream().flatMap(orderItems -> {
                    // 移除模式不匹配的商品
                    orderItems.removeIf(o -> !o.isTypeMatched(isAllType, isOrderMode));
                    if (orderItems.isEmpty()) return Stream.empty();

                    // 全局排序
                    orderItems.sort(ItemComparable::compareTo);
                    // 菜品基础信息
                    KitchenItemReadDO kitchenItemReadDO = orderItems.get(0);

                    PrdDstItemDTO prdDstItemDTO = getPrdDstItemDTO(kitchenItemReadDO);
                    setIsAddItem(prdDstItemDTO);
                    // 称重商品
                    if (kitchenItemReadDO.getIsWeight()) {
                        return orderItems.stream()
                                .filter(o -> deviceConfig.getIsShowHangedItem()
                                        || o.isNormalState())
                                .map(o -> calItemWeightInfo(prdDstItemDTO, o));
                    }
                    // 非称重商品
                    Integer maxCopies = Optional.ofNullable(kitchenItemReadDO.getItemConfig())
                            .map(ItemConfigDO::getMaxCopies).orElse(ItemConfigDO.defaultConfig().getMaxCopies());
                    if (isOrderMode || !deviceConfig.getIsDisplayByMaxCopied()) {
                        maxCopies = -1;
                    }
                    if (!deviceConfig.getIsShowHangedItem()) {
                        List<KitchenItemReadDO> normalItems = orderItems.stream()
                                .filter(KitchenItemReadDO::isNormalState)
                                .collect(Collectors.toList());
                        List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(normalItems, maxCopies);
                        return batchItems.stream().map(batchItem -> calItemBatchInfo(
                                globalIndex.getAndIncrement(), prdDstItemDTO, batchItem
                        ));
                    }
                    if (deviceConfig.getIsProduceHangedItem()) {
                        List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(orderItems, maxCopies);
                        return batchItems.stream().map(batchItem -> calItemBatchInfo(
                                globalIndex.getAndIncrement(), prdDstItemDTO, batchItem
                        ));
                    }
                    List<KitchenItemReadDO> normalItems = orderItems.stream()
                            .filter(KitchenItemReadDO::isNormalState)
                            .collect(Collectors.toList());
                    if (normalItems.isEmpty()) {
                        return Stream.of(calItemBatchInfo(0, prdDstItemDTO, orderItems))
                                .peek(prdDstItemDTO1 -> {
                                    prdDstItemDTO1.setCurrentCount(BigDecimal.ZERO);
                                    prdDstItemDTO1.setKitchenItemList(Collections.emptyList());
                                });
                    }
                    List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(normalItems, maxCopies);
                    return IntStream.range(0, batchItems.size())
                            .mapToObj(index -> {
                                PrdDstItemDTO prdDstItemDTO1 = calItemBatchInfo(
                                        globalIndex.getAndIncrement(), prdDstItemDTO, batchItems.get(index)
                                );
                                if (index == batchItems.size() - 1) {
                                    prdDstItemDTO1.setHangedItemNumber(orderItems.size() - normalItems.size());
                                }
                                return prdDstItemDTO1;
                            });
                })
                .sorted(ItemComparable::compareTo)
                .collect(Collectors.toList());
    }

    /**
     * 与getPrdDstItemList再排序规则上不一样
     * 为了避免影响到其他逻辑
     *
     * @param itemInfo
     * @return
     */
    public List<PrdDstItemDTO> getOrderPrdDstItemList(List<KitchenItemReadDO> itemInfo) {
        boolean itemSort = ObjectUtils.isEmpty(itemSortDTO) || itemSortDTO.getItemSortType() == BooleanEnum.FALSE.getCode();
        Function<KitchenItemReadDO, String> classifier = isOrderMode
                ? KitchenItemReadDO::getOrderItemGuid : (itemSort ? KitchenItemReadDO::getItemAttrMd5 : KitchenItemReadDO::getUniqueSkuKey2);
        AtomicInteger globalIndex = new AtomicInteger(0);
        return itemInfo.parallelStream()
                .collect(Collectors.groupingBy(classifier))
                .values().parallelStream().flatMap(orderItems -> {
                    // 移除模式不匹配的商品
                    orderItems.removeIf(o -> !o.isTypeMatched(isAllType, isOrderMode));
                    if (orderItems.isEmpty()) return Stream.empty();

                    // 全局排序
                    orderItems.sort(ItemComparable::compareTo);
                    // 菜品基础信息
                    KitchenItemReadDO kitchenItemReadDO = orderItems.get(0);

                    PrdDstItemDTO prdDstItemDTO = getPrdDstItemDTO(kitchenItemReadDO);
                    setIsAddItem(prdDstItemDTO);
                    // 称重商品
                    if (kitchenItemReadDO.getIsWeight()) {
                        return orderItems.stream()
                                .filter(o -> deviceConfig.getIsShowHangedItem()
                                        || o.isNormalState())
                                .map(o -> calItemWeightInfo(prdDstItemDTO, o));
                    }
                    // 非称重商品
                    Integer maxCopies = Optional.ofNullable(kitchenItemReadDO.getItemConfig())
                            .map(ItemConfigDO::getMaxCopies).orElse(ItemConfigDO.defaultConfig().getMaxCopies());
                    if (isOrderMode || !deviceConfig.getIsDisplayByMaxCopied()) {
                        maxCopies = -1;
                    }
                    if (!deviceConfig.getIsShowHangedItem()) {
                        List<KitchenItemReadDO> normalItems = orderItems.stream()
                                .filter(KitchenItemReadDO::isNormalState)
                                .collect(Collectors.toList());
                        List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(normalItems, maxCopies);
                        return batchItems.stream().map(batchItem -> calItemBatchInfo(
                                globalIndex.getAndIncrement(), prdDstItemDTO, batchItem
                        ));
                    }
                    if (deviceConfig.getIsProduceHangedItem()) {
                        List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(orderItems, maxCopies);
                        return batchItems.stream().map(batchItem -> calItemBatchInfo(
                                globalIndex.getAndIncrement(), prdDstItemDTO, batchItem
                        ));
                    }
                    List<KitchenItemReadDO> normalItems = orderItems.stream()
                            .filter(KitchenItemReadDO::isNormalState)
                            .collect(Collectors.toList());
                    if (normalItems.isEmpty()) {
                        return Stream.of(calItemBatchInfo(0, prdDstItemDTO, orderItems))
                                .peek(prdDstItemDTO1 -> {
                                    prdDstItemDTO1.setCurrentCount(BigDecimal.ZERO);
                                    prdDstItemDTO1.setKitchenItemList(Collections.emptyList());
                                });
                    }
                    List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(normalItems, maxCopies);
                    return IntStream.range(0, batchItems.size())
                            .mapToObj(index -> {
                                PrdDstItemDTO prdDstItemDTO1 = calItemBatchInfo(
                                        globalIndex.getAndIncrement(), prdDstItemDTO, batchItems.get(index)
                                );
                                if (index == batchItems.size() - 1) {
                                    prdDstItemDTO1.setHangedItemNumber(orderItems.size() - normalItems.size());
                                }
                                return prdDstItemDTO1;
                            });
                })
                .sorted(ItemComparable::compareTo2)
                .collect(Collectors.toList());
    }

    private void setIsAddItem(PrdDstItemDTO prdDstItemDTO) {
        if (!ObjectUtils.isEmpty(prdDstItemDTO.getAddItemBatch()) && prdDstItemDTO.getAddItemBatch() > 0
                && (!ObjectUtils.isEmpty(itemSortDTO) && itemSortDTO.getItemSortType().equals(BooleanEnum.TRUE.getCode()))) {
            if (itemSortDTO.getItemDisplayType().equals(BooleanEnum.TRUE.getCode())) {
                Duration duration = Duration.between(prdDstItemDTO.getFirstAddItemTime(), LocalDateTime.now());
                long minutes = duration.toMinutes();
                if (minutes > itemSortDTO.getItemIntervalTime()) {
                    prdDstItemDTO.setIsAddItem(true);
                }
            } else {
                prdDstItemDTO.setIsAddItem(true);
            }

        }
    }

    public List<PrdDstItemDTO> getPrdDstItemListNew(List<KitchenItemReadDO> itemInfo) {
        boolean itemSort = ObjectUtils.isEmpty(itemSortDTO) || itemSortDTO.getItemSortType() == BooleanEnum.FALSE.getCode();
        Function<KitchenItemReadDO, String> classifier = isOrderMode
                ? KitchenItemReadDO::getOrderItemGuid : (itemSort ? KitchenItemReadDO::getItemAttrMd5 : KitchenItemReadDO::getUniqueSkuKey2);
        AtomicInteger globalIndex = new AtomicInteger(0);
        return itemInfo.parallelStream()
                .collect(Collectors.groupingBy(classifier))
                .values().parallelStream().flatMap(List::stream)
                .collect(Collectors.groupingBy((itemSort ? KitchenItemReadDO::getItemAttrMd5 : KitchenItemReadDO::getUniqueSkuKey2)))
                .values().parallelStream().flatMap(orderItems -> {
                    // 移除模式不匹配的商品
                    orderItems.removeIf(o -> !o.isTypeMatched(isAllType, isOrderMode));
                    if (orderItems.isEmpty()) return Stream.empty();

                    // 全局排序
                    orderItems.sort(ItemComparable::compareTo);
                    // 菜品基础信息
                    KitchenItemReadDO kitchenItemReadDO = orderItems.get(0);

                    PrdDstItemDTO prdDstItemDTO = getPrdDstItemDTO(kitchenItemReadDO);
                    setIsAddItem(prdDstItemDTO);
                    // 称重商品
                    if (kitchenItemReadDO.getIsWeight()) {
                        return orderItems.stream().map(o -> calItemWeightInfo(prdDstItemDTO, o));
                    }
                    // 非称重商品
                    Integer maxCopies = Optional.ofNullable(kitchenItemReadDO.getItemConfig())
                            .map(ItemConfigDO::getMaxCopies).orElse(ItemConfigDO.defaultConfig().getMaxCopies());
                    if (isOrderMode || !deviceConfig.getIsDisplayByMaxCopied()) {
                        maxCopies = -1;
                    }
                    if (!deviceConfig.getIsShowHangedItem()) {
                        List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(orderItems, maxCopies);
                        return batchItems.stream().map(batchItem -> calItemBatchInfo(
                                globalIndex.getAndIncrement(), prdDstItemDTO, batchItem
                        ));
                    }
                    if (deviceConfig.getIsProduceHangedItem()) {
                        List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(orderItems, maxCopies);
                        return batchItems.stream().map(batchItem -> calItemBatchInfo(
                                globalIndex.getAndIncrement(), prdDstItemDTO, batchItem
                        ));
                    }
                    List<KitchenItemReadDO> normalItems = orderItems.stream()
                            .filter(KitchenItemReadDO::isNormalState)
                            .collect(Collectors.toList());
                    if (normalItems.isEmpty()) {
                        return Stream.of(calItemBatchInfo(0, prdDstItemDTO, orderItems))
                                .peek(prdDstItemDTO1 -> {
                                    prdDstItemDTO1.setCurrentCount(BigDecimal.ZERO);
                                    prdDstItemDTO1.setKitchenItemList(Collections.emptyList());
                                });
                    }
                    List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(normalItems, maxCopies);
                    return IntStream.range(0, batchItems.size())
                            .mapToObj(index -> {
                                PrdDstItemDTO prdDstItemDTO1 = calItemBatchInfo(
                                        globalIndex.getAndIncrement(), prdDstItemDTO, batchItems.get(index)
                                );
                                if (index == batchItems.size() - 1) {
                                    prdDstItemDTO1.setHangedItemNumber(orderItems.size() - normalItems.size());
                                }
                                return prdDstItemDTO1;
                            });
                })
                .sorted(ItemComparable::compareTo)
                .collect(Collectors.toList());
    }

    public List<PrdDstItemDTO> getSummaryPrdDstItemList(List<KitchenItemReadDO> itemInfo) {
        boolean itemSort = ObjectUtils.isEmpty(itemSortDTO) || itemSortDTO.getItemSortType() == BooleanEnum.FALSE.getCode();
        AtomicInteger globalIndex = new AtomicInteger(0);
        return itemInfo.stream()
                .collect(Collectors.groupingBy((itemSort ? KitchenItemReadDO::getUniqueSkuKey : KitchenItemReadDO::getUniqueSkuKey3)))
                .values().stream().flatMap(orderItems -> {
                    // 移除模式不匹配的商品
                    orderItems.removeIf(o -> !o.isTypeMatched(isAllType, isOrderMode));
                    if (orderItems.isEmpty()) return Stream.empty();

                    // 全局排序
                    orderItems.sort(ItemComparable::compareTo);
                    // 菜品基础信息
                    KitchenItemReadDO kitchenItemReadDO = orderItems.get(0);

                    PrdDstItemDTO prdDstItemDTO = getPrdDstItemDTO(kitchenItemReadDO);
                    setIsAddItem(prdDstItemDTO);
                    // 称重商品
                    if (kitchenItemReadDO.getIsWeight()) {
                        return orderItems.stream().map(o -> calItemWeightInfo(prdDstItemDTO, o));
                    }
                    // 非称重商品
                    Integer maxCopies = Optional.ofNullable(kitchenItemReadDO.getItemConfig())
                            .map(ItemConfigDO::getMaxCopies).orElse(ItemConfigDO.defaultConfig().getMaxCopies());
                    if (isOrderMode || !deviceConfig.getIsDisplayByMaxCopied()) {
                        maxCopies = -1;
                    }
                    if (!deviceConfig.getIsShowHangedItem()) {
                        List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(orderItems, maxCopies);
                        return batchItems.stream().map(batchItem -> calItemBatchInfo(
                                globalIndex.getAndIncrement(), prdDstItemDTO, batchItem
                        ));
                    }
                    if (deviceConfig.getIsProduceHangedItem()) {
                        List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(orderItems, maxCopies);
                        return batchItems.stream().map(batchItem -> calItemBatchInfo(
                                globalIndex.getAndIncrement(), prdDstItemDTO, batchItem
                        ));
                    }
                    List<KitchenItemReadDO> normalItems = orderItems.stream()
                            .filter(KitchenItemReadDO::isNormalState)
                            .collect(Collectors.toList());
                    if (normalItems.isEmpty()) {
                        return Stream.of(calItemBatchInfo(0, prdDstItemDTO, orderItems))
                                .peek(prdDstItemDTO1 -> {
                                    prdDstItemDTO1.setCurrentCount(BigDecimal.ZERO);
                                    prdDstItemDTO1.setKitchenItemList(Collections.emptyList());
                                });
                    }
                    List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(normalItems, maxCopies);
                    return IntStream.range(0, batchItems.size())
                            .mapToObj(index -> {
                                PrdDstItemDTO prdDstItemDTO1 = calItemBatchInfo(
                                        globalIndex.getAndIncrement(), prdDstItemDTO, batchItems.get(index)
                                );
                                if (index == batchItems.size() - 1) {
                                    prdDstItemDTO1.setHangedItemNumber(orderItems.size() - normalItems.size());
                                }
                                return prdDstItemDTO1;
                            });
                })
                .sorted(ItemComparable::compareTo2)
                .collect(Collectors.toList());
    }
}
