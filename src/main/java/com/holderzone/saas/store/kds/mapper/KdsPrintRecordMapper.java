package com.holderzone.saas.store.kds.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.kds.entity.domain.KdsPrintRecordDO;
import com.holderzone.saas.store.kds.entity.query.PrintRecordQuery;
import com.holderzone.saas.store.kds.entity.read.KdsPrintRecordReadDO;

import java.util.List;

/**
 * KDS打印记录 Mapper 接口
 *
 * <AUTHOR>
 * @since 2019-03-29
 */
public interface KdsPrintRecordMapper extends BaseMapper<KdsPrintRecordDO> {

    /**
     * 根据打印记录Guid查询该条打印记录，包括其路由的打印机
     *
     * @param printRecordQuery
     * @return
     */
    KdsPrintRecordReadDO queryByRecordGuid(PrintRecordQuery printRecordQuery);

    /**
     * 根据打印记录Guid列表查询多条打印记录，包括其路由的打印机
     *
     * @param printRecordQuery
     * @return
     */
    List<KdsPrintRecordReadDO> queryInRecordGuid(PrintRecordQuery printRecordQuery);
}
