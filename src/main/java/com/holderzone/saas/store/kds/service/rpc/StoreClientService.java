package com.holderzone.saas.store.kds.service.rpc;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreClientService
 * @date 2018/09/30 11:41
 * @description
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = StoreClientService.FallBack.class)
public interface StoreClientService {

    @PostMapping("store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 根据品牌guid查询品牌信息
     *
     * @param brandGuid 品牌guid
     * @return 品牌信息
     */
    @ApiOperation("根据品牌guid查询品牌信息")
    @PostMapping("/brand/query_brand_by_guid")
    BrandDTO queryBrandByGuid(@RequestParam("brandGuid") String brandGuid);

    /**
     * 根据门店guid查询门店关联的品牌信息
     *
     * @param storeGuid 门店guid
     * @return 品牌信息
     */
    @ApiOperation(value = "根据门店guid查询门店关联的品牌信息", notes = "若门店未关联到品牌则返回为null，后期一个门店可关联多个品牌")
    @RequestMapping("/store/query_brand_by_storeguid")
    BrandDTO queryBrandByStoreGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 根据传入的guid数组查询门店列表（返回扁平结构、只包含guid和name两列）
     *
     * @param storeGuidList 门店guid集合
     * @return 门店列表（扁平结构、只包含guid和name两列）
     */
    @ApiOperation("根据传入的guid数组查询组织列表")
    @PostMapping("store//query_store_by_idlist")
    List<StoreDTO> queryStoreByIdList(@ApiParam("门店guid集合") @RequestBody List<String> storeGuidList);

    @Component
    class FallBack implements FallbackFactory<StoreClientService> {
        private static final Logger logger = LoggerFactory.getLogger(StoreClientService.FallBack.class);

        @Override
        public StoreClientService create(Throwable throwable) {
            return new StoreClientService() {

                @Override
                public StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid) {
                    logger.error("获取营业日异常e={}", throwable.getMessage());
                    throw new ParameterException("获取营业日失败!");
                }

                @Override
                public BrandDTO queryBrandByGuid(String brandGuid) {
                    logger.error("queryBrandByGuid异常e={}", throwable.getMessage());
                    throw new ParameterException("获取门店信息失败!");
                }

                @Override
                public BrandDTO queryBrandByStoreGuid(String storeGuid) {
                    logger.error("获取品牌信息异常e={}", throwable.getMessage());
                    throw new ParameterException("获取品牌信息失败!");
                }

                @Override
                public List<StoreDTO> queryStoreByIdList(List<String> storeGuidList) {
                    logger.error("获取门店信息异常e={}", throwable.getMessage());
                    throw new ParameterException("获取门店信息失败!");
                }
            };
        }
    }
}
