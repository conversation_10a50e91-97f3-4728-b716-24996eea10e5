package com.holderzone.saas.store.kds.service.print;


import com.holderzone.saas.store.kds.entity.read.KdsPrintRecordReadDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ContentCacheService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface KdsPrintCacheService {

    void save(List<KdsPrintRecordReadDO> arrayOfPrintRecordReadDO);

    KdsPrintRecordReadDO popSingle(String recordGuid);

    List<KdsPrintRecordReadDO> popBatch(List<String> arrayOfRecordGuid);

    boolean hasMsgId(String msgId, String recordGuid, List<String> arrayOfRecordGuid);

    void saveMsgId(String msgId, String recordGuid, List<String> arrayOfRecordGuid);
}
