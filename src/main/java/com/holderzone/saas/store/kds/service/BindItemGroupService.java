package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.kds.entity.domain.BindItemGroupDO;

import java.util.List;


public interface BindItemGroupService extends IService<BindItemGroupDO> {

    BindItemGroupDO getByName(String storeGuid, String name);

    List<BindItemGroupDO> listByStoreGuid(String storeGuid);

}
