package com.holderzone.saas.store.kds.service.print.impl;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.kds.entity.read.KdsPrintRecordReadDO;
import com.holderzone.saas.store.kds.service.print.KdsPrintCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ContentCacheServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印单缓存实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class KdsPrintCacheServiceImpl implements KdsPrintCacheService {

    private static final long EXPIRE_TIME = 5;

    private static final TimeUnit EXPIRE_UNIT = TimeUnit.MINUTES;

    private static final String PRINT_MSG_KEY = "PRINT_KDS:MSG_ID";

    private final RedisTemplate<String, Object> redisTemplateObject;

    private final RedisTemplate<String, KdsPrintRecordReadDO> redisTemplateKdsPrintRecord;

    private final RedisTemplate<String, List<KdsPrintRecordReadDO>> redisTemplateListKdsPrintRecord;

    @Value("${batch-push.enable:true}")
    private boolean batchPushEnable;

    @Autowired
    public KdsPrintCacheServiceImpl(RedisTemplate<String, Object> redisTemplateObject,
                                    RedisTemplate<String, KdsPrintRecordReadDO> redisTemplateKdsPrintRecord,
                                    RedisTemplate<String, List<KdsPrintRecordReadDO>> redisTemplateListKdsPrintRecord) {
        this.redisTemplateObject = redisTemplateObject;
        this.redisTemplateKdsPrintRecord = redisTemplateKdsPrintRecord;
        this.redisTemplateListKdsPrintRecord = redisTemplateListKdsPrintRecord;
    }

    @Override
    public void save(List<KdsPrintRecordReadDO> arrayOfPrintRecordReadDO) {
        if (batchPushEnable) {
            List<String> arrayOfRecordGuid = arrayOfPrintRecordReadDO.stream()
                    .map(KdsPrintRecordReadDO::getGuid)
                    .collect(Collectors.toList());
            redisTemplateListKdsPrintRecord.opsForValue()
                    .set(keyForBatch(arrayOfRecordGuid), arrayOfPrintRecordReadDO, EXPIRE_TIME, EXPIRE_UNIT);
        } else {
            for (KdsPrintRecordReadDO printRecordReadDO : arrayOfPrintRecordReadDO) {
                String recordGuid = printRecordReadDO.getGuid();
                redisTemplateKdsPrintRecord.opsForValue()
                        .set(keyForSingle(recordGuid), printRecordReadDO, EXPIRE_TIME, EXPIRE_UNIT);
            }
        }
    }

    @Override
    public KdsPrintRecordReadDO popSingle(String recordGuid) {
        String key = keyForSingle(recordGuid);
        KdsPrintRecordReadDO printRecordReadDO = redisTemplateKdsPrintRecord.opsForValue().get(key);
        redisTemplateKdsPrintRecord.delete(key);
        return printRecordReadDO;
    }

    @Override
    public List<KdsPrintRecordReadDO> popBatch(List<String> arrayOfRecordGuid) {
        String key = keyForBatch(arrayOfRecordGuid);
        List<KdsPrintRecordReadDO> printRecordReadDO = redisTemplateListKdsPrintRecord.opsForValue().get(key);
        redisTemplateListKdsPrintRecord.delete(key);
        return printRecordReadDO;
    }

    private String keyForSingle(String recordGuid) {
        return "kds_print:content:" + recordGuid;
    }

    private String keyForBatch(List<String> arrayOfRecordGuid) {
        Collections.sort(arrayOfRecordGuid);
        return "kds_print:content:" + Objects.hash(arrayOfRecordGuid);
    }

    @Override
    public boolean hasMsgId(String msgId, String recordGuid, List<String> arrayOfRecordGuid) {
        if (StringUtils.isEmpty(msgId)) {
            return false;
        }
        if (StringUtils.isEmpty(recordGuid) && CollectionUtils.isEmpty(arrayOfRecordGuid)) {
            return false;
        }
        String key;
        if (StringUtils.isNotEmpty(recordGuid)) {
            key = PRINT_MSG_KEY + msgId + ":" + recordGuid;
        } else {
            Collections.sort(arrayOfRecordGuid);
            key = PRINT_MSG_KEY + msgId + ":" + Objects.hash(arrayOfRecordGuid);
        }
        return Boolean.TRUE.equals(redisTemplateObject.hasKey(key));
    }

    @Override
    public void saveMsgId(String msgId, String recordGuid, List<String> arrayOfRecordGuid) {
        if (StringUtils.isEmpty(msgId)) {
            return;
        }
        if (StringUtils.isNotEmpty(recordGuid)) {
            redisTemplateObject.opsForValue().set(PRINT_MSG_KEY + msgId + ":" + recordGuid, recordGuid, 1, TimeUnit.DAYS);
        }
        if (CollectionUtils.isNotEmpty(arrayOfRecordGuid)) {
            Collections.sort(arrayOfRecordGuid);
            redisTemplateObject.opsForValue().set(PRINT_MSG_KEY + msgId + ":" + Objects.hash(arrayOfRecordGuid),
                    JacksonUtils.writeValueAsString(arrayOfRecordGuid), 1, TimeUnit.DAYS);
        }
    }
}
