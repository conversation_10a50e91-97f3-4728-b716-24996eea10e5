package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 菜品重复绑定适用门店
 */
@Data
@TableName("hsk_display_repeat_item_store")
public class DisplayRepeatItemStoreDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 全局唯一主键
     */
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 品牌guid
     */
    private String brandGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

}
