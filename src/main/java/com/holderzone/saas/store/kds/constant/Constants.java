package com.holderzone.saas.store.kds.constant;

public final class Constants {

    public static final int ITEM_TIMEOUT = 20;

    public static final int ITEM_MAX_COPIES = 3;

    public static final int ITEM_DISPLAY_TYPE = 0;

    /**
     * 当没有规则时，修改为订单模式，设置为999
     */
    public static final int ITEM_NO_RULE_BATCH=999;
    /**
     * 当时延迟商品，设置批次为1000
     */
    public static final int ITEM_DELAYED_BATCH=1000;
    /**
     * 当时延迟商品，设置批次为1000
     */
    public static final int ITEM_DELAYED_URGE_BATCH=-1;
    public static final int ITEM_BATCH_MIN=1;
    public static final int ITEM_BATCH_MAX=100;

    public static final String RULE_TYPE_CANNOT_BE_EMPTY = "规则类型不能为空";

    public static final String RULE_GUID_CANNOT_BE_EMPTY = "规则guid不能为空";

    public static final String BRAND_GUID_CANNOT_BE_EMPTY = "品牌guid不能为空";

    public static final String QUERY_ERROR_BY_ITEM = "菜品查询异常";

}
