package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.entity.enums.KdsTradeModeEnum;
import com.holderzone.saas.store.kds.service.KdsNotificationService;
import com.holderzone.saas.store.kds.service.KdsStatusPushService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;

@Slf4j
@Service
@AllArgsConstructor
public class KdsStatusPushServiceImpl implements KdsStatusPushService {

    private static final String KDS_BUSINESS_TYPE = "kds_status";

    private static final String KDS_VOICE_MSG = "kds_voice_msg";

    private final KdsNotificationService kdsNotificationService;


    @Override
    public void statusChanged(int tradeMode, Collection<DeviceConfigDO> deviceConfigInSql) {
        KdsTradeModeEnum kdsTradeModeEnum = KdsTradeModeEnum.ofCode(tradeMode);
        UserContext userContext = UserContextUtils.get();
        String voiceMsg = kdsTradeModeEnum.getVoice();
        deviceConfigInSql.stream()
                .filter(deviceConfigDO -> {
                    switch (kdsTradeModeEnum) {
                        case DINE_IN:
                            return deviceConfigDO.getIsDineInOrderNotice();
                        case SNACK:
                            return deviceConfigDO.getIsSnackOrderNotice();
                        case TAKEOUT:
                            return deviceConfigDO.getIsTakeoutOrderNotice();
                        default:
                            return false;
                    }
                })
                .forEach(deviceConfigDO -> statusChanged(
                        userContext.getEnterpriseGuid(),
                        userContext.getStoreGuid(), deviceConfigDO.getGuid(), voiceMsg
                ));
    }

    @Override
    public void statusChanged(String enterpriseGuid, String storeGuid, String deviceId, String voiceMsg) {
        kdsNotificationService.sendMessage(enterpriseGuid, storeGuid, deviceId, KDS_BUSINESS_TYPE, voiceMsg);
    }

    /**
     * 语音播报
     */
    @Override
    public void voiceBroadcast(String enterpriseGuid, String storeGuid, String deviceId, String voiceMsg) {
        kdsNotificationService.sendMessage(enterpriseGuid, storeGuid, deviceId, KDS_VOICE_MSG, voiceMsg);
    }

}
