package com.holderzone.saas.store.kds.mapstruct;

import com.holderzone.saas.store.dto.kds.req.DisplayRuleSaveOrUpdateDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayItemRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayRuleRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayStoreRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DisplayItemDO;
import com.holderzone.saas.store.kds.entity.domain.DisplayRuleDO;
import com.holderzone.saas.store.kds.entity.domain.DisplayStoreDO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring")
public interface DisplayRuleMapstruct {

    DisplayStoreRespDTO toStoreResp(DisplayStoreDO displayStoreDO);

    DisplayItemRespDTO toItemResp(DisplayItemDO displayItemDO);

    List<DisplayStoreRespDTO> toStoreRespList(List<DisplayStoreDO> displayStoreDO);

    List<DisplayItemRespDTO> toItemRespList(List<DisplayItemDO> displayItemDO);

    DisplayStoreDO toStoreDO(DisplayStoreRespDTO storeRespDTO);

    List<DisplayStoreDO> toStoreDOList(List<DisplayStoreRespDTO> storeList);

    DisplayItemDO toItemDO(DisplayItemRespDTO itemRespDTO);

    List<DisplayItemDO> toItemDOList(List<DisplayItemRespDTO> itemList);

    DisplayRuleRespDTO toRuleRespDTO(DisplayRuleDO ruleDO);

    List<DisplayRuleRespDTO> toRuleRespDTOList(List<DisplayRuleDO> ruleDOList);

    DisplayRuleDO ruleRespToRuleDO(DisplayRuleRespDTO ruleRespDTO);

    List<DisplayRuleDO> ruleRespListtoRuleDOList(List<DisplayRuleRespDTO> respDTOList);

    DisplayRuleDO ruleReqToRuleDO(DisplayRuleSaveOrUpdateDTO ruleReqDTO);
}
