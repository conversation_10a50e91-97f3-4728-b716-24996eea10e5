package com.holderzone.saas.store.kds.entity.group;

import com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO;
import lombok.Data;

import java.util.Objects;

@Data
public class OrderGroupKey {


    /**
     * 订单Guid
     * 并台情况下，订单拆单显示
     */
    private String orderGuid;

    /**
     * 正餐：桌台名称
     * 快餐：快餐
     * 外卖：饿了么|美团
     */
    private String orderDesc;

    /**
     * 正餐：订单号
     * 快餐：订单号
     * 外卖：订单号
     */
    private String orderNumber;

    /**
     * 正餐：桌台名
     * 快餐：牌号
     * 外卖：日流水号
     */
    private String orderSerialNo;

    /**
     * 整单备注
     */
    private String orderRemark;

    /**
     * 区域Guid
     */
    private String areaGuid;

    /**
     * 桌台Guid
     */
    private String tableGuid;

    /**
     * 显示模式
     */
    private Integer displayType;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OrderGroupKey that = (OrderGroupKey) o;
        return Objects.equals(orderGuid, that.orderGuid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(orderGuid);
    }

    public static OrderGroupKey of(KitchenItemReadDO kitchenItemReadDO) {
        OrderGroupKey orderGroupKey = new OrderGroupKey();
        orderGroupKey.setOrderGuid(kitchenItemReadDO.getOrderGuid());
        orderGroupKey.setOrderDesc(kitchenItemReadDO.getOrderDesc());
        orderGroupKey.setOrderNumber(kitchenItemReadDO.getOrderNumber());
        orderGroupKey.setOrderSerialNo(kitchenItemReadDO.getOrderSerialNo());
        orderGroupKey.setOrderRemark(kitchenItemReadDO.getOrderRemark());
        orderGroupKey.setAreaGuid(kitchenItemReadDO.getAreaGuid());
        orderGroupKey.setTableGuid(kitchenItemReadDO.getTableGuid());
        orderGroupKey.setDisplayType(kitchenItemReadDO.getDisplayType());
        return orderGroupKey;
    }
}
