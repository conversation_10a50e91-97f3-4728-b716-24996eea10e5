package com.holderzone.saas.store.kds.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * web app configure
 *
 * <AUTHOR>
 * @date 17-12-5
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //一体机实现多语言切换
        MyLocaleChangeInterceptor myLocaleChangeInterceptor = new MyLocaleChangeInterceptor();
        registry.addInterceptor(myLocaleChangeInterceptor);
    }
}
