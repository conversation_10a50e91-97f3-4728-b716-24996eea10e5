package com.holderzone.saas.store.kds.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.KdsPrintRecordReqDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsPrintOrderDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsPrintRecordDTO;
import com.holderzone.saas.store.kds.service.print.KdsPrintRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Api("KDS设备接口")
@RequestMapping("/kds_print_record")
public class KdsPrintRecordController {

    private final KdsPrintRecordService kdsPrintRecordService;

    @Autowired
    public KdsPrintRecordController(KdsPrintRecordService kdsPrintRecordService) {
        this.kdsPrintRecordService = kdsPrintRecordService;
    }

    @PostMapping("/get_order")
    @ApiOperation("打印内容获取接口")
    public List<KdsPrintOrderDTO> getPrinterOrder(@RequestBody @Validated KdsPrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("打印内容获取接口入参：{}", JacksonUtils.writeValueAsString(printRecordReqDTO));
        }
        return kdsPrintRecordService.getPrintOrder(printRecordReqDTO);
    }

    @PostMapping("/list")
    @ApiOperation("获取打印结果的列表，入参status: 0=打印中，1=成功，2=失败")
    public List<KdsPrintRecordDTO> listRecord(@Validated(KdsPrintRecordReqDTO.ListRecord.class)
                                              @RequestBody KdsPrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("获取打印结果的列表入参：{}", JacksonUtils.writeValueAsString(printRecordReqDTO));
        }
        return kdsPrintRecordService.listRecord(printRecordReqDTO);
    }

    @PostMapping("/update_status")
    @ApiOperation("打印结果更新")
    public void updateStatus(@Validated(KdsPrintRecordReqDTO.UpdateRecord.class)
                             @RequestBody KdsPrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("打印结果更新入参：{}", JacksonUtils.writeValueAsString(printRecordReqDTO));
        }
        kdsPrintRecordService.updatePrintResult(printRecordReqDTO);
    }

    @PostMapping("/delete")
    @ApiOperation("删除单条打印记录")
    public void deleteRecord(@Validated(KdsPrintRecordReqDTO.DeleteRecord.class)
                             @RequestBody KdsPrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("删除单条打印记录入参：{}", JacksonUtils.writeValueAsString(printRecordReqDTO));
        }
        kdsPrintRecordService.deleteRecord(printRecordReqDTO);
    }

    @PostMapping("/batch_delete")
    @ApiOperation("批量删除失败列表")
    public void batchDeleteRecord(@Validated(KdsPrintRecordReqDTO.BatchDeleteRecord.class)
                                  @RequestBody KdsPrintRecordReqDTO printRecordReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("批量删除失败列表入参：printStatusDTO={}", JacksonUtils.writeValueAsString(printRecordReqDTO));
        }
        kdsPrintRecordService.batchDeleteRecord(printRecordReqDTO.getArrayOfRecordGuid());
    }
}

