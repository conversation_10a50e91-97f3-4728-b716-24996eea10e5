package com.holderzone.saas.store.kds.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlHelper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Functions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.*;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import com.holderzone.saas.store.dto.order.request.item.TransferItemDetailsReqDTO;
import com.holderzone.saas.store.dto.order.request.item.TransferItemReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.OrderItemDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.item.ItemTypeEnum;
import com.holderzone.saas.store.enums.kds.KdsItemStateEnum;
import com.holderzone.saas.store.enums.kds.ScanFinishFoodTypeEnum;
import com.holderzone.saas.store.kds.aop.RequireNotification;
import com.holderzone.saas.store.kds.bo.KitchenItemChangesBO;
import com.holderzone.saas.store.kds.bo.KitchenItemPrepareBO;
import com.holderzone.saas.store.kds.constant.Constants;
import com.holderzone.saas.store.kds.constant.PointModeConstants;
import com.holderzone.saas.store.kds.entity.bo.*;
import com.holderzone.saas.store.kds.entity.domain.*;
import com.holderzone.saas.store.kds.entity.dto.KitchenItemDelayNotificationDTO;
import com.holderzone.saas.store.kds.entity.enums.*;
import com.holderzone.saas.store.kds.entity.query.ItemQuery;
import com.holderzone.saas.store.kds.entity.read.GroupBindItemReadDO;
import com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO;
import com.holderzone.saas.store.kds.entity.read.PointItemReadDO;
import com.holderzone.saas.store.kds.handler.KitchenItemHandlerFactory;
import com.holderzone.saas.store.kds.mapper.DeviceBindItemGroupMapper;
import com.holderzone.saas.store.kds.mapper.KitchenItemMapper;
import com.holderzone.saas.store.kds.mapstruct.DisplayRuleMapstruct;
import com.holderzone.saas.store.kds.mapstruct.KitchenItemMapstruct;
import com.holderzone.saas.store.kds.service.*;
import com.holderzone.saas.store.kds.service.print.KdsInvoiceTypeEnum;
import com.holderzone.saas.store.kds.service.print.KdsPrintRecordService;
import com.holderzone.saas.store.kds.service.rpc.ItemRpcService;
import com.holderzone.saas.store.kds.service.rpc.TableRpcService;
import com.holderzone.saas.store.kds.service.rpc.TradeRpcService;
import com.holderzone.saas.store.kds.utils.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机单据管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
@SuppressWarnings("unchecked")
@AllArgsConstructor
public class KitchenItemServiceImpl extends ServiceImpl<KitchenItemMapper, KitchenItemDO> implements KitchenItemService {

    private final DeviceConfigService deviceConfigService;

    private final ProductionPointService productionPointService;

    private final PrdPointItemService prdPointItemService;

    private final KitchenItemAttrService kitchenItemAttrService;

    private final DistributeItemService distributeItemService;

    private final DistributeAreaService distributeAreaService;

    private final KdsPrintRecordService kdsPrintRecordService;

    private final KitchenItemMapstruct kitchenItemMapstruct;

    private final DistributedIdService distributedIdService;

    private final KdsStatusPushService kdsStatusPushService;

    private final QueueItemService queueItemService;

    private final DisplayStoreService displayStoreService;

    private final DisplayRuleService displayRuleService;

    private final DisplayItemService displayItemService;

    private final DisplayRuleMapstruct displayRuleMapstruct;

    private final KdsNotificationService kdsNotificationService;

    private final ItemRpcService itemRpcService;

    private final TradeRpcService tradeRpcService;

    private final TableRpcService tableRpcService;

    private final KitchenItemMapper kitchenItemMapper;

    private final KitchenAssociatedOrderService kitchenAssociatedOrderService;

    private final KitchenItemDelayNotificationService kitchenItemDelayNotificationService;

    private final DisplayItemSortService displayItemSortService;

    private final DisplayRepeatItemService displayRepeatItemService;

    private final KitchenItemDeviceService kitchenItemDeviceService;

    private final KitchenItemHandlerFactory kitchenItemHandlerFactory;

    private final DeviceBindItemGroupMapper deviceBindItemGroupMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void prepare(ItemPrepareReqDTO itemPrepareReqDTO) {
        List<KdsItemDTO> kdsItemDTOS = itemPrepareReqDTO.getItems();
        // 查询KDS菜品绑定配置
        Boolean allowRepeatFlag = displayRepeatItemService.queryAllowRepeatFlag(itemPrepareReqDTO.getStoreGuid());
        // prepare
        KitchenItemPrepareBO biz = kitchenItemHandlerFactory.create(allowRepeatFlag).prepare(itemPrepareReqDTO);
        log.info("prepare biz:{}", JacksonUtils.writeValueAsString(biz));
        List<KitchenItemDO> kitchenItem2Insert = biz.getKitchenItemList();
        List<KitchenItemAttrDO> kitchenItemAttr2Insert = biz.getKitchenItemAttrList();
        Boolean isNeedNotification = biz.getIsNeedNotification();
        LocalDateTime prepareTime = biz.getPrepareTime();
        Map<String, DeviceConfigDO> deviceConfigInSqlMap = biz.getDeviceConfigInSqlMap();
        Map<String, DisplayRuleItemRespDTO> kitchenItemDisplayRuleMap = biz.getKitchenItemDisplayRuleMap();

        // 打印未匹配菜品日志，保存数据
        List<String> skuGuidMatched = kitchenItem2Insert.stream()
                .map(KitchenItemDO::getSkuGuid).collect(Collectors.toList());
        logWarnMsgOfUnMatchedItems(kdsItemDTOS, skuGuidMatched);
        boolean prdEmpty = false;
        boolean dstEmpty = false;
        for (KitchenItemDO kitchenItemDO : kitchenItem2Insert) {
            if (!prdEmpty && StringUtils.isEmpty(kitchenItemDO.getPrdDeviceId())) {
                prdEmpty = true;
            }
            if (!dstEmpty && StringUtils.isEmpty(kitchenItemDO.getDstDeviceId())) {
                dstEmpty = true;
            }
            if (prdEmpty && dstEmpty) {
                break;
            }
        }
        if (prdEmpty || dstEmpty || Boolean.TRUE.equals(isNeedNotification)) {
            needNotification(itemPrepareReqDTO, isNeedNotification, prdEmpty, dstEmpty);
        }
        if (skuGuidMatched.isEmpty()) {
            log.warn("所有菜品未绑定");
            return;
        }
        //  保存kds 商品
        List<String> backupItemGuids = saveKitchenItem(itemPrepareReqDTO, prepareTime, kitchenItem2Insert,
                kitchenItemAttr2Insert, kitchenItemDisplayRuleMap);
        if (CollectionUtils.isEmpty(backupItemGuids)) {
            return;
        }
        // 自动打印
        autoPrint(itemPrepareReqDTO.getOrderGuid(), backupItemGuids, deviceConfigInSqlMap, false);
        // 语音、状态变更推送
        preparePushVoiceAndChangedStatus(itemPrepareReqDTO.getTradeMode(), itemPrepareReqDTO.getStoreGuid(), deviceConfigInSqlMap);
        // 入取餐屏队列
        queueItemService.inPreparedQueue(AsyncTask.wrapper(itemPrepareReqDTO));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changes(ItemChangesReqDTO itemChangesReqDTO) {
        // 查询KDS菜品绑定配置
        Boolean allowRepeatFlag = displayRepeatItemService.queryAllowRepeatFlag(itemChangesReqDTO.getStoreGuid());
        // changes
        KitchenItemChangesBO biz = kitchenItemHandlerFactory.create(allowRepeatFlag).changes(itemChangesReqDTO);
        log.info("changes KitchenItemChangesBO:{}", JacksonUtils.writeValueAsString(biz));
        // 持久化批量保存数据入库
        saveBatchChangeKitchenItem(itemChangesReqDTO, biz);
        Map<String, DeviceConfigDO> deviceConfigInSqlMap = biz.getDeviceConfigInSqlMap();
        // 语音、状态变更推送
        preparePushVoiceAndChangedStatus(itemChangesReqDTO.getTradeMode(), itemChangesReqDTO.getStoreGuid(), deviceConfigInSqlMap);
        // 未绑定商品提醒
        List<KitchenItemDO> changeKitchenItemDOList = biz.getChangeKitchenItemDOList();
        List<String> deviceToPush = getDeviceToPush(changeKitchenItemDOList, itemChangesReqDTO.getStoreGuid());
        if (CollectionUtils.isNotEmpty(deviceToPush)) {
            sendReminderMessages(deviceToPush);
        }
        // 打印换菜单
        kdsPrintRecordService.printChangesItem(biz.getKdsChangesItemDTO());
    }

    private void saveBatchChangeKitchenItem(ItemChangesReqDTO itemChangesReqDTO, KitchenItemChangesBO biz) {
        List<KitchenItemDO> changeKitchenItemDOList = biz.getChangeKitchenItemDOList();
        if (CollectionUtils.isNotEmpty(changeKitchenItemDOList)) {
            log.info("[套餐换菜]changeKitchenItemDOList={}", JacksonUtils.writeValueAsString(changeKitchenItemDOList));
            saveBatch(changeKitchenItemDOList);
            saveBatchKitchenItemDeviceDOList(changeKitchenItemDOList);
        }
        List<KitchenItemDO> needChangeKitchenItemDOList = biz.getNeedChangeKitchenItemDOList();
        if (CollectionUtils.isNotEmpty(needChangeKitchenItemDOList)) {
            log.info("[套餐换菜]needChangeKitchenItemDOList={}", JacksonUtils.writeValueAsString(needChangeKitchenItemDOList));
            removeByIds(needChangeKitchenItemDOList.stream().map(KitchenItemDO::getId).collect(Collectors.toList()));
            // 删除关联
            List<String> needChangeKitchenItemGuids = needChangeKitchenItemDOList.stream()
                    .map(KitchenItemDO::getGuid)
                    .distinct()
                    .collect(Collectors.toList());
            kitchenItemDeviceService.removeBatchByKitchenItemGuids(needChangeKitchenItemGuids);
        }
        List<String> originalOrderItemGuidList = itemChangesReqDTO.getOriginalKdsItemList()
                .stream()
                .map(KdsItemDTO::getOrderItemGuid)
                .distinct()
                .collect(Collectors.toList());
        // 先删除原来order_item_guid的属性，再存入新的属性
        kitchenItemAttrService.remove(new LambdaQueryWrapper<KitchenItemAttrDO>()
                .in(KitchenItemAttrDO::getOrderItemGuid, originalOrderItemGuidList));
        // 属性
        List<KitchenItemAttrDO> kitchenItemAttrList = biz.getKitchenItemAttrList();
        if (CollectionUtils.isNotEmpty(kitchenItemAttrList)) {
            List<String> attrGuidList = distributedIdService.nextBatchKitchenAttrGuid(kitchenItemAttrList.size());
            kitchenItemAttrList.forEach(itemAttrDO -> itemAttrDO.setGuid(attrGuidList.remove(attrGuidList.size() - 1)));
            kitchenItemAttrService.saveBatch(kitchenItemAttrList);
        }
    }


    private void needNotification(ItemPrepareReqDTO itemPrepareReqDTO, boolean isNeedNotification,
                                  boolean prdEmpty, boolean dstEmpty) {
        List<DeviceConfigDO> deviceConfigList = deviceConfigService.listDeviceOfStore(itemPrepareReqDTO.getStoreGuid());
        List<String> deviceToPush = new ArrayList<>();
        deviceConfigList = deviceConfigList.stream().filter(e -> Objects.nonNull(e.getPointMode())).collect(Collectors.toList());
        for (DeviceConfigDO configDO : deviceConfigList) {
            if (isNeedNotification) {
                deviceToPush.add(configDO.getGuid());
                continue;
            }
            if (prdEmpty && PointModeEnum.ofMode(configDO.getPointMode()) == PointModeEnum.PRODUCTION) {
                deviceToPush.add(configDO.getGuid());
            }
            if (dstEmpty && PointModeEnum.ofMode(configDO.getPointMode()) == PointModeEnum.DISTRIBUTE) {
                deviceToPush.add(configDO.getGuid());
            }
        }
        log.info("推送sendReminderMessages：{}", JacksonUtils.writeValueAsString(deviceToPush));
        sendReminderMessages(deviceToPush);
    }

    /**
     * 保存kds 商品
     */
    private List<String> saveKitchenItem(ItemPrepareReqDTO itemPrepareReqDTO, LocalDateTime prepareTime,
                                         List<KitchenItemDO> kitchenItem2Insert, List<KitchenItemAttrDO> kitchenItemAttr2Insert,
                                         Map<String, DisplayRuleItemRespDTO> kitchenItemDisplayRuleMap) {
        List<String> itemGuids = distributedIdService.nextBatchKitchenItemGuid(kitchenItem2Insert.size());
        List<String> backupItemGuids = new ArrayList<>(itemGuids);
        kitchenItem2Insert.forEach(kitchenItemDO -> kitchenItemDO.setGuid(itemGuids.remove(itemGuids.size() - 1)));
        //延迟菜品逻辑
        List<String> delayedKitchenItemGuidList = new ArrayList<>();
        Map<Integer, List<KitchenItemDO>> delayedItemDTOMap = new HashMap<>();
        kitchenItem2Insert.forEach(s -> {
            DisplayRuleItemRespDTO ruleDTO = kitchenItemDisplayRuleMap.get(s.getItemGuid());
            if (ruleDTO == null) {
                return;
            }
            if (ruleDTO.getRuleType() == -1) {
                return;
            }
            if (ruleDTO.getDelayTime() == null) {
                return;
            }
            List<KitchenItemDO> kitchenItemGuidList = delayedItemDTOMap.getOrDefault(ruleDTO.getDelayTime(), new ArrayList<>());
            kitchenItemGuidList.add(s);
            delayedItemDTOMap.put(ruleDTO.getDelayTime(), kitchenItemGuidList);
            delayedKitchenItemGuidList.add(s.getGuid());
        });
        if (!CollectionUtils.isEmpty(delayedKitchenItemGuidList)) {
            backupItemGuids.removeIf(delayedKitchenItemGuidList::contains);
            delayedItemDTOMap.forEach((delay, delayedItemDTOList) -> {
                List<String> delayDeviceGuidList = delayedItemDTOList.stream().map(KitchenItemDO::getPrdDeviceId)
                        .filter(StringUtils::hasText).collect(Collectors.toList());
                KitchenItemDelayNotificationDTO dto = new KitchenItemDelayNotificationDTO();
                dto.setDelayTimeLeft(delay);
                dto.setDelayTimeTotal(delay);
                dto.setStoreGuid(UserContextUtils.getStoreGuid());
                dto.setOrderGuid(itemPrepareReqDTO.getOrderGuid());
                dto.setKitchenItemGuidList(backupItemGuids);
                dto.setDeviceGuidList(delayDeviceGuidList);
                kitchenItemDelayNotificationService.sendMessage(dto);
            });

        } else {
            kitchenItem2Insert.forEach(k -> {
                k.setPrepareTime(prepareTime);
                k.setDisplayTime(null);
                k.setDelayTimeMinutes(null);
            });
        }
        this.saveBatch(kitchenItem2Insert);
        // 批量保存菜品显示设备绑定关系
        saveBatchKitchenItemDeviceDOList(kitchenItem2Insert);
        // 如果是联台单
        TradeDineInInfoDTO tradeDineInInfoDTO = itemPrepareReqDTO.getTradeDineInInfoDTO();
        if (KdsTradeModeEnum.DINE_IN.getCode() == itemPrepareReqDTO.getTradeMode()
                && Objects.nonNull(tradeDineInInfoDTO) && Boolean.TRUE.equals(tradeDineInInfoDTO.getAssociatedFlag())) {
            KitchenAssociatedOrderDO associatedOrderDO = new KitchenAssociatedOrderDO();
            associatedOrderDO.setGuid(String.valueOf(SnowFlakeUtil.getInstance().nextId()));
            associatedOrderDO.setOrderGuid(itemPrepareReqDTO.getOrderGuid());
            associatedOrderDO.setAssociatedFlag(tradeDineInInfoDTO.getAssociatedFlag());
            associatedOrderDO.setAssociatedSn(tradeDineInInfoDTO.getAssociatedSn());
            associatedOrderDO.setAssociatedTableGuids(Objects.nonNull(tradeDineInInfoDTO.getAssociatedTableGuids()) ?
                    JacksonUtils.writeValueAsString(tradeDineInInfoDTO.getAssociatedTableGuids()) : null);
            associatedOrderDO.setAssociatedTableNames(Objects.nonNull(tradeDineInInfoDTO.getAssociatedTableNames()) ?
                    JacksonUtils.writeValueAsString(tradeDineInInfoDTO.getAssociatedTableNames()) : null);
            kitchenAssociatedOrderService.create(associatedOrderDO);
        }
        // 批量生成attrGuid
        if (!kitchenItemAttr2Insert.isEmpty()) {
            List<String> attrGuids = distributedIdService.nextBatchKitchenAttrGuid(kitchenItemAttr2Insert.size());
            kitchenItemAttr2Insert.forEach(kitchenItemAttrDO -> kitchenItemAttrDO.setGuid(attrGuids.remove(attrGuids.size() - 1)));
            kitchenItemAttrService.saveBatch(kitchenItemAttr2Insert);
        }
        return backupItemGuids;
    }

    /**
     * 批量保存菜品显示设备绑定关系
     */
    private void saveBatchKitchenItemDeviceDOList(List<KitchenItemDO> kitchenItem2Insert) {
        List<KitchenItemDeviceDO> kitchenItemDeviceDOList = buildKitchenItemDeviceList(kitchenItem2Insert);
        if (CollectionUtils.isNotEmpty(kitchenItemDeviceDOList)) {
            kitchenItemDeviceService.saveBatch(kitchenItemDeviceDOList);
        }
    }


    private List<KitchenItemDeviceDO> buildKitchenItemDeviceList(List<KitchenItemDO> kitchenItem2Insert) {
        List<KitchenItemDeviceDO> kitchenItemDeviceList = Lists.newArrayList();
        for (KitchenItemDO kitchenItemDO : kitchenItem2Insert) {
            if (CollectionUtils.isEmpty(kitchenItemDO.getPrdPointDevices()) && CollectionUtils.isEmpty(kitchenItemDO.getDstPointDevices())) {
                continue;
            }
            // 制作口
            List<KitchenItemDeviceDO> prdPointDevices = Optional.ofNullable(kitchenItemDO.getPrdPointDevices()).orElse(Lists.newArrayList());
            for (KitchenItemDeviceDO prdPointDevice : prdPointDevices) {
                KitchenItemDeviceDO kitchenItemDeviceDO = new KitchenItemDeviceDO();
                BeanUtils.copyProperties(prdPointDevice, kitchenItemDeviceDO);
                kitchenItemDeviceDO.setGuid(String.valueOf(SnowFlakeUtil.getInstance().nextId()));
                kitchenItemDeviceDO.setKitchenItemGuid(kitchenItemDO.getGuid());
                kitchenItemDeviceList.add(kitchenItemDeviceDO);
            }
            // 出堂口
            List<KitchenItemDeviceDO> dstPointDevices = Optional.ofNullable(kitchenItemDO.getDstPointDevices()).orElse(Lists.newArrayList());
            for (KitchenItemDeviceDO dstPointDevice : dstPointDevices) {
                KitchenItemDeviceDO kitchenItemDeviceDO = new KitchenItemDeviceDO();
                BeanUtils.copyProperties(dstPointDevice, kitchenItemDeviceDO);
                kitchenItemDeviceDO.setGuid(String.valueOf(SnowFlakeUtil.getInstance().nextId()));
                kitchenItemDeviceDO.setKitchenItemGuid(kitchenItemDO.getGuid());
                kitchenItemDeviceList.add(kitchenItemDeviceDO);
            }
        }
        return kitchenItemDeviceList;
    }

    @NotNull
    private List<String> getDeviceToPush(List<KitchenItemDO> updateKitchenItemDOList, String storeGuid) {
        boolean prdEmpty = false;
        boolean dstEmpty = false;
        for (KitchenItemDO kitchenItemDO : updateKitchenItemDOList) {
            if (!prdEmpty && StringUtils.isEmpty(kitchenItemDO.getPrdDeviceId())) {
                prdEmpty = true;
            }
            if (!dstEmpty && StringUtils.isEmpty(kitchenItemDO.getDstDeviceId())) {
                dstEmpty = true;
            }
            if (prdEmpty && dstEmpty) {
                break;
            }
        }
        return getDeviceToPush(storeGuid, prdEmpty, dstEmpty);
    }

    @NotNull
    private List<String> getDeviceToPush(String storeGuid, boolean prdEmpty, boolean dstEmpty) {
        List<DeviceConfigDO> deviceConfigList = deviceConfigService.listDeviceOfStore(storeGuid);
        List<String> deviceToPush = new ArrayList<>();
        for (DeviceConfigDO configDO : deviceConfigList) {
            if (configDO.getPointMode() == null) {
                continue;
            }
            if (prdEmpty && PointModeEnum.ofMode(configDO.getPointMode()) == PointModeEnum.PRODUCTION) {
                deviceToPush.add(configDO.getGuid());
            }
            if (dstEmpty && PointModeEnum.ofMode(configDO.getPointMode()) == PointModeEnum.DISTRIBUTE) {
                deviceToPush.add(configDO.getGuid());
            }
        }
        return deviceToPush;
    }

    private void sendReminderMessages(List<String> deviceToPush) {
        deviceToPush.forEach(deviceGuid -> {
            kdsNotificationService.sendMessage(
                    UserContextUtils.getEnterpriseGuid(),
                    UserContextUtils.getStoreGuid(),
                    deviceGuid,
                    "kds_notification",
                    "您好，系统检测到订单中存在未绑定任何KDS终端的商品，避免做漏菜品，请先进行绑定。"
            );
        });
    }

    /**
     * 查询制作点菜品
     *
     * @param prdItemStatusReqDTO 入参
     * @return 制作点菜品列表
     */
    @Override
    public PrdDstRespDTO queryPrdStatus(PrdItemStatusReqDTO prdItemStatusReqDTO) {
        String storeGuid = prdItemStatusReqDTO.getStoreGuid();
        String deviceId = prdItemStatusReqDTO.getDeviceId();
        DeviceConfigDO deviceConfigInSql = deviceConfigService.queryPrdDeviceByGuid(storeGuid, deviceId);
        // 构造返回体
        PrdDstRespDTO prdDstRespDTO = new PrdDstRespDTO();

        // 菜品显示顺序配置
        DisplayRuleItemSortQueryDTO queryDTO = new DisplayRuleItemSortQueryDTO();
        queryDTO.setStoreGuid(storeGuid);
        DisplayRuleItemSortDTO itemSortDTO = displayItemSortService.queryItemSortRuleByStore(queryDTO);

        // 查询设备信息，即该设备作为制作点位或出堂口时设备的相关信息
        PrdRespBO.PrdRespBOBuilder builder = PrdRespBO.builder()
                .deviceConfig(deviceConfigInSql)
                .itemSortDTO(itemSortDTO);

        Integer displayMode = prdItemStatusReqDTO.getDisplayMode();
        switch (displayMode) {
            case PointModeConstants.PRD_POINT:
                prdPoint(prdItemStatusReqDTO, builder, prdDstRespDTO);
                break;
            case PointModeConstants.PRD_SUMMARY_ITEM:
                prdSummaryItem(prdItemStatusReqDTO, deviceConfigInSql, builder, prdDstRespDTO, DisplayRuleType.OTHER);
                break;
            case PointModeConstants.PRD_ORDER:
            case PointModeConstants.PRD_SUMMARY_ORDER:
                prdOrder(prdItemStatusReqDTO, deviceConfigInSql, builder, prdDstRespDTO, DisplayRuleType.OTHER);
                break;
            case PointModeConstants.PRD_BLEND_MODE:
                prdBlendMode(prdItemStatusReqDTO, deviceConfigInSql, builder, prdDstRespDTO);
                break;
            case PointModeConstants.BLEND_MODE_ORDER:
                prdBlendModePage(prdItemStatusReqDTO, deviceConfigInSql, builder, prdDstRespDTO);
                break;
            case PointModeConstants.BLEND_MODE_SUMMARY:
                prdItemStatusReqDTO.setPageSize(10);
                prdSummaryItem(prdItemStatusReqDTO, deviceConfigInSql, builder, prdDstRespDTO, DisplayRuleType.SUMMERY);
                break;
            default:
                prdALL(prdItemStatusReqDTO, builder, prdDstRespDTO);
        }
        return prdDstRespDTO;
    }

    /**
     * 制作口：混合模式
     * 该模式只查询第一页，由后端控制并写死
     *
     * @param prdItemStatusReqDTO 入参
     * @param deviceConfigInSql   该设备配置信息
     * @param builder             构建器
     * @param prdDstRespDTO       返回体
     */
    private void prdBlendMode(PrdItemStatusReqDTO prdItemStatusReqDTO, DeviceConfigDO deviceConfigInSql,
                              PrdRespBO.PrdRespBOBuilder builder, PrdDstRespDTO prdDstRespDTO) {
        prdItemStatusReqDTO.setCurrentPage(1);
        prdItemStatusReqDTO.setPageSize(5);
        int displayRuleType = DisplayRuleType.BATCH;
        if (Objects.equals(ItemDisplayTypeEnum.ALL_SHOW.getType(), prdItemStatusReqDTO.getItemDisplayType())) {
            displayRuleType = DisplayRuleType.SUMMERY;
        }
        prdOrder(prdItemStatusReqDTO, deviceConfigInSql, builder, prdDstRespDTO, displayRuleType);
        prdItemStatusReqDTO.setCurrentPage(1);
        prdItemStatusReqDTO.setPageSize(10);
        prdSummaryItem(prdItemStatusReqDTO, deviceConfigInSql, builder, prdDstRespDTO, DisplayRuleType.SUMMERY);
    }

    private void prdBlendModePage(PrdItemStatusReqDTO prdItemStatusReqDTO, DeviceConfigDO deviceConfigInSql,
                                  PrdRespBO.PrdRespBOBuilder builder, PrdDstRespDTO prdDstRespDTO) {
        int displayRuleType = DisplayRuleType.BATCH;
        if (Objects.equals(ItemDisplayTypeEnum.ALL_SHOW.getType(), prdItemStatusReqDTO.getItemDisplayType())) {
            displayRuleType = DisplayRuleType.SUMMERY;
        }
        prdOrder(prdItemStatusReqDTO, deviceConfigInSql, builder, prdDstRespDTO, displayRuleType);
    }

    /**
     * 最后这个不知道是什么模式，传入的页数是最大值，先暂不处理
     *
     * @param prdItemStatusReqDTO 入参
     * @param builder             返回构造器
     * @param prdDstRespDTO       返回体
     */
    private void prdALL(PrdItemStatusReqDTO prdItemStatusReqDTO, PrdRespBO.PrdRespBOBuilder builder,
                        PrdDstRespDTO prdDstRespDTO) {
        List<KitchenItemReadDO> kitchenItemReadInSql =
                KitchenItemUtils.filterByDisplayTime(baseMapper.queryPrdItem(ItemQuery.of(prdItemStatusReqDTO)));
        long currentPage = prdItemStatusReqDTO.getCurrentPage();
        long pageSize = prdItemStatusReqDTO.getPageSize();
        prdItemStatusReqDTO.setCurrentPage(1);
        prdItemStatusReqDTO.setPageSize(Long.MAX_VALUE);
        PrdRespBO prdRespBO = builder.isOrderMode(false).isAllType(false).build();
        prdDstRespDTO.setItems(prdRespBO.getPrdDstItemPage(prdItemStatusReqDTO, kitchenItemReadInSql));
        prdItemStatusReqDTO.setCurrentPage(currentPage);
        prdItemStatusReqDTO.setPageSize(pageSize);
        prdRespBO = builder.isOrderMode(true).isAllType(false).build();
        prdDstRespDTO.setOrders(prdRespBO.getPrdDstOrderPage(prdItemStatusReqDTO, kitchenItemReadInSql));
    }

    /**
     * 制作点: 订单模式 / 混合-订单模式1*5
     *
     * @param prdItemStatusReqDTO 入参
     * @param deviceConfigInSql   该设备配置信息
     * @param builder             返回构造器
     * @param prdDstRespDTO       返回体
     */
    private void prdOrder(PrdItemStatusReqDTO prdItemStatusReqDTO, DeviceConfigDO deviceConfigInSql,
                          PrdRespBO.PrdRespBOBuilder builder, PrdDstRespDTO prdDstRespDTO, int displayRuleType) {
        PrdRespBO prdRespBO = builder.isOrderMode(true).isAllType(true).build();
        prdItemStatusReqDTO.setPointGuid(null);
        // 查询订单
        PageAdapter<PrdDstOrderDTO> page = new PageAdapter<>(prdItemStatusReqDTO.getCurrentPage(),
                prdItemStatusReqDTO.getPageSize());
        ItemQuery query = ItemQuery.of(prdItemStatusReqDTO);
        query.setDisplayRuleType(displayRuleType);
        query.setIsShowHangedItem(!deviceConfigInSql.getIsShowHangedItem());
        IPage<PrdDstOrderDTO> prdItem = baseMapper.queryPrdDstOrderAndItem(page, query);
        log.info("订单模式 prdItem={}", JacksonUtils.writeValueAsString(prdItem));
        List<PrdDstOrderDTO> records = prdItem.getRecords();

        // 查询订单商品
        List<String> orderGuidList = records.stream()
                .map(PrdDstOrderDTO::getOrderGuid)
                .collect(Collectors.toList());
        query.setOrderGuidList(orderGuidList);
        List<KitchenItemReadDO> doList = baseMapper.queryPrdItemByOrder(query);
        if (CollectionUtils.isEmpty(doList)) {
            log.error("kds商品为空");
            prdDstRespDTO.setOrders(new PageAdapter<>(page, records));
            return;
        }
        Map<String, List<KitchenItemReadDO>> kitchenItemMap = doList.stream()
                .collect(Collectors.groupingBy(KitchenItemReadDO::getOrderGuid));

        records.forEach(order -> {
            List<KitchenItemReadDO> kitchenItemList = kitchenItemMap.get(order.getOrderGuid());
            if (CollectionUtils.isEmpty(kitchenItemList)) {
                // 原则上不会走到这里面，为了以防万一加上日志，方便排查
                log.error("订单对应商品为空 orderGuid={}", order.getOrderGuid());
            }
            order.setItems(prdRespBO.getPrdDstItemListNew(KitchenItemUtils.setAddItemBatch(kitchenItemList, prdRespBO.getItemSortDTO())));
        });
        prdDstRespDTO.setOrders(new PageAdapter<>(page, records));
    }

    /**
     * 制作点: 菜品汇总模式 / 混合-订单模式1*5
     *
     * @param prdItemStatusReqDTO 入参
     * @param deviceConfigInSql   该设备配置信息
     * @param builder             返回构造器
     * @param displayRuleType     汇总:1
     * @param prdDstRespDTO       返回体
     */
    private void prdSummaryItem(PrdItemStatusReqDTO prdItemStatusReqDTO, DeviceConfigDO deviceConfigInSql,
                                PrdRespBO.PrdRespBOBuilder builder, PrdDstRespDTO prdDstRespDTO, int displayRuleType) {
        PrdRespBO prdRespBO = builder.isOrderMode(false).isAllType(true).build();
        PageAdapter<KitchenItemReadDO> page = new PageAdapter<>(prdItemStatusReqDTO.getCurrentPage(),
                prdItemStatusReqDTO.getPageSize());
        ItemQuery query = ItemQuery.of(prdItemStatusReqDTO);
        query.setDisplayRuleType(displayRuleType);
        query.setIsShowHangedItem(!deviceConfigInSql.getIsShowHangedItem());
        setItemSort(prdRespBO.getItemSortDTO(), query);
        log.info("[prd][汇总][queryPrdItemGroup]query={}", JacksonUtils.writeValueAsString(query));
        IPage<KitchenItemReadDO> itemGroupPage = baseMapper.queryPrdItemGroup(page, query);
        log.info("[prd][汇总][itemGroupPage]={}", JacksonUtils.writeValueAsString(itemGroupPage));
        List<KitchenItemReadDO> records = itemGroupPage.getRecords();
        List<PrdDstItemDTO> summaryPrdDstItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(records)) {
            // 查询菜品明细
            Set<String> skuGuidSet = records.stream().map(KitchenItemReadDO::getUniqueSkuKey).collect(Collectors.toSet());
            query.setSkuGuidList(new ArrayList<>(skuGuidSet));
            log.info("[prd][汇总][queryPrdItemBySku]query={}", JacksonUtils.writeValueAsString(query));
            List<KitchenItemReadDO> itemGroupBySkuList = baseMapper.queryPrdItemBySku(query);
            log.info("[prd][汇总][itemGroupBySkuList]={}", JacksonUtils.writeValueAsString(itemGroupBySkuList));
            summaryPrdDstItemList = prdRespBO.getSummaryPrdDstItemList(KitchenItemUtils.setAddItemBatch(itemGroupBySkuList, prdRespBO.getItemSortDTO()));
        }
        prdDstRespDTO.setItems(new PageAdapter<>(page, summaryPrdDstItemList));
    }

    private void setItemSort(DisplayRuleItemSortDTO itemSortDTO, ItemQuery query) {
        if (!ObjectUtils.isEmpty(itemSortDTO)) {
            query.setItemSortType(itemSortDTO.getItemSortType());
            if (itemSortDTO.getItemDisplayType() == null || itemSortDTO.getItemDisplayType().equals(BooleanEnum.FALSE.getCode())) {
                query.setItemIntervalTime(null);
            } else {
                query.setItemIntervalTime(itemSortDTO.getItemIntervalTime());
            }
        }
    }

    /**
     * 制作点: 堂口模式
     *
     * @param prdItemStatusReqDTO 入参
     * @param builder             返回构造器
     */
    private void prdPoint(PrdItemStatusReqDTO prdItemStatusReqDTO, PrdRespBO.PrdRespBOBuilder builder,
                          PrdDstRespDTO prdDstRespDTO) {
        String storeGuid = prdItemStatusReqDTO.getStoreGuid();
        String deviceId = prdItemStatusReqDTO.getDeviceId();
        boolean isOrderMode = Integer.valueOf(1).equals(prdItemStatusReqDTO.getQueryModeForPoint());
        PrdRespBO prdRespBO = builder.isOrderMode(isOrderMode).isAllType(true).build();
        List<PointItemReadDO> pointItemReadInSql = baseMapper.queryPrdPointItem(ItemQuery.of(prdItemStatusReqDTO));
        pointItemReadInSql = productionPointService.fillEmptyPoint(
                storeGuid, deviceId, pointItemReadInSql);
        for (PointItemReadDO pointItemReadDO : pointItemReadInSql) {
            pointItemReadDO.setItems(KitchenItemUtils.filterByDisplayTime(pointItemReadDO.getItems()));
            pointItemReadDO.setItems(KitchenItemUtils.setAddItemBatch(pointItemReadDO.getItems(), prdRespBO.getItemSortDTO()));
        }
        prdDstRespDTO.setPoints(prdRespBO.getPrdDstPointList(pointItemReadInSql));
    }

    /**
     * 查询出堂口菜品
     *
     * @param dstItemStatusReqDTO 入参
     * @return 出堂口菜品列表
     */
    @Override
    public PrdDstRespDTO queryDstStatus(DstItemStatusReqDTO dstItemStatusReqDTO) {
        String storeGuid = dstItemStatusReqDTO.getStoreGuid();
        String deviceId = dstItemStatusReqDTO.getDeviceId();
        deviceConfigService.assertThatDeviceExists(storeGuid, deviceId);
        // 构建返回值
        PrdDstRespDTO prdDstRespDTO = new PrdDstRespDTO();

        // 菜品显示顺序配置
        DisplayRuleItemSortQueryDTO queryDTO = new DisplayRuleItemSortQueryDTO();
        queryDTO.setStoreGuid(storeGuid);
        DisplayRuleItemSortDTO itemSortDTO = displayItemSortService.queryItemSortRuleByStore(queryDTO);

        // 查询数据，略
        DstRespBO.DstRespBOBuilder builder = DstRespBO.builder().itemSortDTO(itemSortDTO);

        Integer displayMode = dstItemStatusReqDTO.getDisplayMode();
        switch (displayMode) {
            case PointModeConstants.DST_SINGLE_ITEM:
                dstSingleItem(dstItemStatusReqDTO, builder, prdDstRespDTO);
                break;
            case PointModeConstants.DST_SUMMARY_ITEM:
                dstSummaryItem(dstItemStatusReqDTO, builder, prdDstRespDTO, DisplayRuleType.OTHER);
                break;
            case PointModeConstants.DST_ORDER:
            case PointModeConstants.DST_SUMMARY_ORDER:
                dstOrder(dstItemStatusReqDTO, builder, prdDstRespDTO, DisplayRuleType.OTHER);
                break;
            case PointModeConstants.DST_BLEND_MODE:
                dstBlendMode(dstItemStatusReqDTO, builder, prdDstRespDTO);
                break;
            case PointModeConstants.BLEND_MODE_ORDER:
                dstBlendModePage(dstItemStatusReqDTO, builder, prdDstRespDTO);
                break;
            case PointModeConstants.BLEND_MODE_SUMMARY:
                dstItemStatusReqDTO.setPageSize(10);
                dstSummaryItem(dstItemStatusReqDTO, builder, prdDstRespDTO, DisplayRuleType.SUMMERY);
                break;
            case PointModeConstants.GROUP_MODE_SUMMARY:
                dstBindItemGroup(dstItemStatusReqDTO, builder, prdDstRespDTO);
                break;
            default:
                dstAll(dstItemStatusReqDTO, builder, prdDstRespDTO);
        }
        return prdDstRespDTO;
    }

    /**
     * 出堂口: 混合模式
     * 该模式只查询第一页，由后端控制并写死
     *
     * @param dstItemStatusReqDTO 入参
     * @param builder             构建器
     */
    private void dstBlendMode(DstItemStatusReqDTO dstItemStatusReqDTO, DstRespBO.DstRespBOBuilder builder,
                              PrdDstRespDTO prdDstRespDTO) {
        dstItemStatusReqDTO.setCurrentPage(1);
        dstItemStatusReqDTO.setPageSize(5);
        int displayRuleType = DisplayRuleType.BATCH;
        if (Objects.equals(ItemDisplayTypeEnum.ALL_SHOW.getType(), dstItemStatusReqDTO.getItemDisplayType())) {
            displayRuleType = DisplayRuleType.SUMMERY;
        }
        dstOrder(dstItemStatusReqDTO, builder, prdDstRespDTO, displayRuleType);
        dstItemStatusReqDTO.setCurrentPage(1);
        dstItemStatusReqDTO.setPageSize(10);
        dstSummaryItem(dstItemStatusReqDTO, builder, prdDstRespDTO, DisplayRuleType.SUMMERY);
    }

    /**
     * 出堂口: 混合模式
     *
     * @param dstItemStatusReqDTO 入参
     * @param builder             构建器
     */
    private void dstBlendModePage(DstItemStatusReqDTO dstItemStatusReqDTO, DstRespBO.DstRespBOBuilder builder,
                                  PrdDstRespDTO prdDstRespDTO) {
        int displayRuleType = DisplayRuleType.BATCH;
        if (Objects.equals(ItemDisplayTypeEnum.ALL_SHOW.getType(), dstItemStatusReqDTO.getItemDisplayType())) {
            displayRuleType = DisplayRuleType.SUMMERY;
        }
        dstOrder(dstItemStatusReqDTO, builder, prdDstRespDTO, displayRuleType);
    }

    /**
     * 最后这个不知道是什么模式，传入的页数是最大值，先保留原逻辑
     *
     * @param dstItemStatusReqDTO 入参
     * @param builder             返回构造器
     */
    private void dstAll(DstItemStatusReqDTO dstItemStatusReqDTO, DstRespBO.DstRespBOBuilder builder,
                        PrdDstRespDTO prdDstRespDTO) {
        String storeGuid = dstItemStatusReqDTO.getStoreGuid();
        ItemQuery itemQuery = ItemQuery.of(storeGuid, dstItemStatusReqDTO);
        List<KitchenItemReadDO> kitchenItemReadInSql =
                KitchenItemUtils.filterByDisplayTime(Integer.valueOf(8).equals(itemQuery.getDisplayType())
                        ? baseMapper.queryDstItemAll(itemQuery)
                        : baseMapper.queryDstItem(itemQuery));
        long currentPage = dstItemStatusReqDTO.getCurrentPage();
        long pageSize = dstItemStatusReqDTO.getPageSize();
        dstItemStatusReqDTO.setCurrentPage(1);
        dstItemStatusReqDTO.setPageSize(Long.MAX_VALUE);
        DstRespBO build = builder.isSingleMode(false).isSummaryMode(true).isAllType(false).build();
        prdDstRespDTO.setItems(build.getDstItemPage(dstItemStatusReqDTO, kitchenItemReadInSql));
        dstItemStatusReqDTO.setCurrentPage(currentPage);
        dstItemStatusReqDTO.setPageSize(pageSize);
        build = builder.isSingleMode(false).isSummaryMode(false).isAllType(false).build();
        prdDstRespDTO.setOrders(build.getDstOrderPage(dstItemStatusReqDTO, kitchenItemReadInSql));
    }

    /**
     * 出堂口: 菜品汇总模式
     * 混合-汇总模式1*10
     *
     * @param dstItemStatusReqDTO 入参
     * @param builder             构造器
     * @param displayRuleType     汇总
     */
    private void dstSummaryItem(DstItemStatusReqDTO dstItemStatusReqDTO, DstRespBO.DstRespBOBuilder builder,
                                PrdDstRespDTO prdDstRespDTO, int displayRuleType) {
        String storeGuid = dstItemStatusReqDTO.getStoreGuid();
        ItemQuery itemQuery = ItemQuery.of(storeGuid, dstItemStatusReqDTO);
        itemQuery.setDisplayRuleType(displayRuleType);
        DstRespBO build = builder.isSingleMode(false).isSummaryMode(true).isAllType(true).build();
        setItemSort(build.getItemSortDTO(), itemQuery);
        PageAdapter<KitchenItemReadDO> page = new PageAdapter<>(dstItemStatusReqDTO.getCurrentPage(), dstItemStatusReqDTO.getPageSize());
        IPage<KitchenItemReadDO> summaryItemPage = baseMapper.pageDstItemGroup(page, itemQuery);
        log.info("dst汇总-itemGroupPage={}", JacksonUtils.writeValueAsString(summaryItemPage));
        List<KitchenItemReadDO> records = summaryItemPage.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            // 查询菜品明细
            Set<String> skuGuidSet = records.stream().map(KitchenItemReadDO::getUniqueSkuKey).collect(Collectors.toSet());
            itemQuery.setSkuGuidList(new ArrayList<>(skuGuidSet));
            List<KitchenItemReadDO> itemGroupBySkuList = baseMapper.queryDstItemBySku(itemQuery);
            log.info("dst汇总-itemGroupBySkuList={}", JacksonUtils.writeValueAsString(itemGroupBySkuList));
            summaryItemPage.setRecords(KitchenItemUtils.setAddItemBatch(itemGroupBySkuList, build.getItemSortDTO()));
        }
        prdDstRespDTO.setItems(new PageAdapter<>(page, build.getSummaryDstItemListNew(summaryItemPage.getRecords())));
    }

    /**
     * 出堂口: 菜品分组汇总模式
     *
     * @param dstItemStatusReqDTO 入参
     * @param builder             返回构造器
     */
    private void dstBindItemGroup(DstItemStatusReqDTO dstItemStatusReqDTO, DstRespBO.DstRespBOBuilder builder,
                                  PrdDstRespDTO prdDstRespDTO) {
        DstRespBO dstRespBO = builder.isSummaryMode(true).isSingleMode(false).isAllType(true).build();
        ItemQuery itemQuery = ItemQuery.of(dstItemStatusReqDTO);
        // 分页查询菜品分组列表
        PageAdapter<GroupBindItemReadDO> page = new PageAdapter<>(dstItemStatusReqDTO.getCurrentPage(),
                dstItemStatusReqDTO.getPageSize());
        IPage<GroupBindItemReadDO> groupBindItemReadDOIPage = deviceBindItemGroupMapper.pageBindGroup(page, itemQuery);
        List<GroupBindItemReadDO> records = groupBindItemReadDOIPage.getRecords();
        log.info("菜品分组列表:{}", JacksonUtils.writeValueAsString(records));
        List<GroupBindItemReadDO> groupBindItemReadInSql = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(records)) {
            List<String> groupGuids = records.stream()
                    .map(GroupBindItemReadDO::getGuid)
                    .distinct()
                    .collect(Collectors.toList());
            itemQuery.setGroupGuids(groupGuids);
            // 查询商品数据
            groupBindItemReadInSql = baseMapper.queryDstGroupBindItem(itemQuery);
            for (GroupBindItemReadDO groupBindItemReadDO : groupBindItemReadInSql) {
                groupBindItemReadDO.setItems(KitchenItemUtils.filterByDisplayTime(groupBindItemReadDO.getItems()));
                groupBindItemReadDO.setItems(KitchenItemUtils.setAddItemBatch(groupBindItemReadDO.getItems(), dstRespBO.getItemSortDTO()));
            }
        }
        prdDstRespDTO.setGroups(new PageAdapter<>(page, dstRespBO.getDstBindGroupItemList(records, groupBindItemReadInSql)));
    }

    /**
     * 出堂口: 单菜品模式
     *
     * @param dstItemStatusReqDTO 入参
     * @param builder             构造器
     */
    private void dstSingleItem(DstItemStatusReqDTO dstItemStatusReqDTO, DstRespBO.DstRespBOBuilder builder,
                               PrdDstRespDTO prdDstRespDTO) {
        String storeGuid = dstItemStatusReqDTO.getStoreGuid();
        ItemQuery itemQuery = ItemQuery.of(storeGuid, dstItemStatusReqDTO);
        DstRespBO build = builder.isSingleMode(true).isSummaryMode(false).isAllType(true).build();
        DisplayRuleItemSortDTO itemSortDTO = build.getItemSortDTO();
        if (!ObjectUtils.isEmpty(itemSortDTO)) {
            itemQuery.setItemSortType(itemSortDTO.getItemSortType());
        }
        PageAdapter<KitchenItemReadDO> page = new PageAdapter<>(dstItemStatusReqDTO.getCurrentPage(),
                dstItemStatusReqDTO.getPageSize());
        IPage<KitchenItemReadDO> singleItemPage = baseMapper.pageDstItemAll(page, itemQuery);
        log.info("dst单菜-singleItemPage={}", JacksonUtils.writeValueAsString(singleItemPage));
        List<KitchenItemReadDO> records = singleItemPage.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            // 查询菜品明细
            Set<String> orderItemGuidSet = records.stream().map(KitchenItemReadDO::getGuid).collect(Collectors.toSet());
            itemQuery.setKitchenItemGuidList(new ArrayList<>(orderItemGuidSet));
            List<KitchenItemReadDO> itemGroupBySkuList = baseMapper.queryDstItemBySku(itemQuery);
            log.info("dst单菜-itemGroupBySkuList={}", JacksonUtils.writeValueAsString(itemGroupBySkuList));
            singleItemPage.setRecords(itemGroupBySkuList);
        }
        prdDstRespDTO.setItems(new PageAdapter<>(page, build.getSummaryDstItemListNew(singleItemPage.getRecords())));
    }

    /**
     * 出堂口: 订单模式1*4
     * 出堂口: 订单模式1*6
     * 混合-订单模式1*5
     *
     * @param dstItemStatusReqDTO 入参
     * @param builder             构建器
     * @param displayRuleType     批次
     */
    private void dstOrder(DstItemStatusReqDTO dstItemStatusReqDTO, DstRespBO.DstRespBOBuilder builder,
                          PrdDstRespDTO prdDstRespDTO, int displayRuleType) {
        String storeGuid = dstItemStatusReqDTO.getStoreGuid();
        ItemQuery itemQuery = ItemQuery.of(storeGuid, dstItemStatusReqDTO);
        DstRespBO build = builder.isSingleMode(false).isSummaryMode(false).isAllType(true).build();
        itemQuery.setDisplayRuleType(displayRuleType);

        // 查询订单
        PageAdapter<PrdDstOrderDTO> page = new PageAdapter<>(dstItemStatusReqDTO.getCurrentPage(),
                dstItemStatusReqDTO.getPageSize());
        IPage<PrdDstOrderDTO> prdItem = baseMapper.pageDstOrderItem(page, itemQuery);
        log.info("出堂 订单模式 prdItem={}", JacksonUtils.writeValueAsString(prdItem));
        List<PrdDstOrderDTO> records = prdItem.getRecords();

        // 查询订单商品
        List<String> orderGuidList = records.stream()
                .map(PrdDstOrderDTO::getOrderGuid)
                .distinct()
                .collect(Collectors.toList());
        itemQuery.setOrderGuidList(orderGuidList);
        List<KitchenItemReadDO> doList = baseMapper.queryDstItemByOrder(itemQuery);
        if (CollectionUtils.isEmpty(doList)) {
            log.error("kds商品为空");
            prdDstRespDTO.setOrders(new PageAdapter<>(page, records));
            return;
        }
        Map<String, List<KitchenItemReadDO>> kitchenItemMap = doList.stream()
                .collect(Collectors.groupingBy(KitchenItemReadDO::getOrderGuid));

        for (PrdDstOrderDTO order : records) {
            List<KitchenItemReadDO> kitchenItemList = kitchenItemMap.get(order.getOrderGuid());
            if (CollectionUtils.isEmpty(kitchenItemList)) {
                // 原则上不会走到这里面，为了以防万一加上日志，方便排查
                log.error("订单对应商品为空 orderGuid={}", order.getOrderGuid());
            }
            order.setItems(build.getDstItemListNew(KitchenItemUtils.setAddItemBatch(kitchenItemList, build.getItemSortDTO())));
        }
        prdDstRespDTO.setOrders(new PageAdapter<>(page, records));
    }

    @Override
    @RequireNotification(assertResult = true)
    public boolean call(ItemCallUpReqDTO itemCallUpReqDTO) {
        if (org.springframework.util.CollectionUtils.isEmpty(itemCallUpReqDTO.getOrderItemGuidList())) {
            log.info("订单商品Guid列表不得为空");
            return false;
        }

        KitchenItemDO kitchenItemDO = new KitchenItemDO();
        kitchenItemDO.setItemState(KdsItemStateEnum.CALL_UP.getCode());
        kitchenItemDO.setCallUpTime(DateTimeUtils.now());
        kitchenItemDO.setOrderSortTime(kitchenItemDO.getCallUpTime());
        boolean update = this.update(kitchenItemDO, new LambdaQueryWrapper<KitchenItemDO>()
                .eq(KitchenItemDO::getItemState, KdsItemStateEnum.HANG_UP.getCode())
                .in(KitchenItemDO::getOrderItemGuid, itemCallUpReqDTO.getOrderItemGuidList())
        );

        // 叫起语音播报
        sendCallUpVoiceMsg(itemCallUpReqDTO);

        return update;
    }

    private void sendCallUpVoiceMsg(ItemCallUpReqDTO itemCallUpReqDTO) {
        // 查询菜品绑定设备
        String storeGuid = UserContextUtils.getStoreGuid();
        List<String> deviceIdList = new ArrayList<>();
        List<KitchenItemDO> oldKitchenItemDOList = this.list(new LambdaQueryWrapper<KitchenItemDO>()
                .in(KitchenItemDO::getOrderItemGuid, itemCallUpReqDTO.getOrderItemGuidList()));
        List<String> skuGuidList = oldKitchenItemDOList.stream()
                .map(KitchenItemDO::getSkuGuid)
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());

        SingleDataDTO dataDTO = new SingleDataDTO();
        dataDTO.setDatas(skuGuidList);
        dataDTO.setData(storeGuid);

        // 查询制作设备
        List<PrdPointItemDTO> prdPointItemDTOList = prdPointItemService.queryPrdPointBySku(dataDTO);
        if (!org.springframework.util.CollectionUtils.isEmpty(prdPointItemDTOList)) {
            List<String> prdPointDeviceIdList = prdPointItemDTOList.stream()
                    .map(PrdPointItemDTO::getDeviceId)
                    .filter(StringUtils::hasText)
                    .distinct()
                    .collect(Collectors.toList());
            deviceIdList.addAll(prdPointDeviceIdList);
        }

        // 查询出堂设备
        List<DistributeItemDTO> distributeItemDTOList = distributeItemService.queryDistributeItemBySku(dataDTO);
        if (!org.springframework.util.CollectionUtils.isEmpty(distributeItemDTOList)) {
            List<String> distributeDeviceIdList = distributeItemDTOList.stream()
                    .map(DistributeItemDTO::getDeviceId)
                    .filter(StringUtils::hasText)
                    .distinct()
                    .collect(Collectors.toList());
            deviceIdList.addAll(distributeDeviceIdList);
        }

        // 发送语音消息
        String voiceMsg = getCallUpVoiceMsg(itemCallUpReqDTO);
        if (!org.springframework.util.CollectionUtils.isEmpty(deviceIdList)) {
            deviceIdList.forEach(deviceId -> {
                kdsStatusPushService.voiceBroadcast(UserContextUtils.getEnterpriseGuid(), storeGuid, deviceId, voiceMsg);
            });
        }
    }

    private String getCallUpVoiceMsg(ItemCallUpReqDTO itemCallUpReqDTO) {
        if (StringUtils.isEmpty(itemCallUpReqDTO.getDiningTableName())) {
            List<KitchenItemDO> oldKitchenItemDOList = this.list(new LambdaQueryWrapper<KitchenItemDO>()
                    .in(KitchenItemDO::getOrderItemGuid, itemCallUpReqDTO.getOrderItemGuidList()));
            if (org.springframework.util.CollectionUtils.isEmpty(oldKitchenItemDOList)) {
                return Constants.QUERY_ERROR_BY_ITEM;
            }
            OrderDTO orderDTO = tradeRpcService.findByOrderGuid(oldKitchenItemDOList.get(0).getOrderGuid());
            if (ObjectUtils.isEmpty(orderDTO)) {
                return Constants.QUERY_ERROR_BY_ITEM;
            }
            itemCallUpReqDTO.setDiningTableName(orderDTO.getDiningTableName());
        }
        StringBuilder voiceMsg = new StringBuilder(itemCallUpReqDTO.getDiningTableName());
        if (itemCallUpReqDTO.getIsCallAll()) {
            // 桌台号+整单+叫起走菜
            return voiceMsg + "整单叫起走菜";
        }

        // 有单独接口调用，所以只能自己去查，不能由业务传递
        SingleListDTO listDTO = new SingleListDTO();
        listDTO.setList(itemCallUpReqDTO.getOrderItemGuidList());
        List<DineInItemDTO> inItemDTOList = tradeRpcService.queryItemByGuid(listDTO);
        if (org.springframework.util.CollectionUtils.isEmpty(inItemDTOList)) {
            return Constants.QUERY_ERROR_BY_ITEM;
        }
        if (itemCallUpReqDTO.getOrderItemGuidList().size() > 1) {
            // 桌台号+菜品所属分类+叫起走菜
            List<String> itemTypeNameList = inItemDTOList.stream()
                    .map(DineInItemDTO::getItemTypeName)
                    .filter(StringUtils::hasText)
                    .distinct()
                    .collect(Collectors.toList());
            String itemTypeNameStr = arrayConvert(itemTypeNameList);
            voiceMsg.append(itemTypeNameStr);
            voiceMsg.append("叫起走菜");
        } else {
            // 桌台号+菜品名称+叫起走菜
            voiceMsg.append(inItemDTOList.get(0).getItemName());
            voiceMsg.append("叫起走菜");
        }
        return voiceMsg.toString();
    }

    public String arrayConvert(List<String> list) {
        if (CollUtil.isEmpty(list)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        int size = list.size() - 1;
        for (int i = 0; i <= size; i++) {
            String string = list.get(i);
            if (i != size) {
                sb.append(string).append("、");
            } else {
                sb.append(string);
            }
        }
        return sb.toString();
    }

    @Override
    @RequireNotification(assertResult = true)
    @Transactional(rollbackFor = Exception.class)
    public boolean urge(ItemUrgeReqDTO itemUrgeReqDTO) {
        if (CollectionUtils.isEmpty(itemUrgeReqDTO.getOrderItemGuidList())) {
            return false;
        }

        KitchenItemDO kitchenItemDO = new KitchenItemDO();
        kitchenItemDO.setUrgedTime(DateTimeUtils.now());
        kitchenItemDO.setBatch(Constants.ITEM_DELAYED_URGE_BATCH);
        LambdaQueryWrapper<KitchenItemDO> lambdaQueryWrapper = new LambdaQueryWrapper<KitchenItemDO>()
                .in(KitchenItemDO::getOrderItemGuid, itemUrgeReqDTO.getOrderItemGuidList());

        List<KitchenItemDO> kitchenItemList = this.baseMapper.selectList(lambdaQueryWrapper).stream()
                .filter(s -> {
                    if (s.getUrgedTime() != null) {
                        return false;
                    }
                    if (s.getDisplayTime() == null) {
                        return false;
                    }
                    return s.getDisplayTime().isAfter(LocalDateTime.now());
                }).collect(Collectors.toList());
        Map<String, List<KitchenItemDO>> kitchenItemMap =
                kitchenItemList
                        .stream()
                        .collect(Collectors.groupingBy(KitchenItemDO::getOrderGuid));
        this.update(kitchenItemDO, lambdaQueryWrapper);

        kitchenItemDO.setPrepareTime(kitchenItemDO.getUrgedTime());
        kitchenItemDO.setOrderSortTime(kitchenItemDO.getUrgedTime());

        List<String> needUpdateItemGuidList = kitchenItemList.stream().map(KitchenItemDO::getGuid).collect(Collectors.toList());
        lambdaQueryWrapper.in(KitchenItemDO::getGuid, needUpdateItemGuidList);
        this.update(kitchenItemDO, lambdaQueryWrapper);

        kitchenItemMap.forEach((orderGuid, kitchenItemByOrderList) -> {
            if (kitchenItemByOrderList.isEmpty()) {
                return;
            }
            String storeGuid = kitchenItemByOrderList.get(0).getStoreGuid();
            List<String> kitchenItemGuidList = kitchenItemByOrderList.stream().map(KitchenItemDO::getGuid).collect(Collectors.toList());
            List<String> deviceGuidList = kitchenItemByOrderList.stream().map(KitchenItemDO::getPrdDeviceId).collect(Collectors.toList());
            autoPrint(orderGuid, storeGuid, kitchenItemGuidList, deviceGuidList, true);
        });

        return true;
    }

    @Override
    @RequireNotification(assertResult = true)
    public boolean remark(OrderRemarkReqDTO orderRemarkReqDTO) {
        String orderRemark = orderRemarkReqDTO.getOrderRemark();
        String orderGuid = orderRemarkReqDTO.getOrderGuid();
        List<KitchenItemReadDO> kitchenItemInSql = baseMapper.queryOrderItem(ItemQuery.of(orderGuid));
        if (CollectionUtils.isEmpty(kitchenItemInSql)) {
            return false;
        }
        List<KitchenItemDO> kitchenItemsToUpdate = kitchenItemInSql.stream()
                .map(kitchenItemReadDO -> {
                    KitchenItemDO kitchenItem2Update = new KitchenItemDO();
                    kitchenItem2Update.setOrderItemGuid(kitchenItemReadDO.getOrderItemGuid());
                    kitchenItem2Update.setOrderRemark(orderRemark);
                    kitchenItem2Update.setItemAttrMd5(ItemMd5Utils.calItemMd5(orderRemark, kitchenItemReadDO));
                    return kitchenItem2Update;
                })
                .collect(Collectors.toList());
        return SqlHelper.retBool(baseMapper.updateOrderRemark(kitchenItemsToUpdate));
    }

    @Override
    @RequireNotification(assertResult = true)
    public boolean changeTable(OrderTableReqDTO orderTableReqDTO) {
        KitchenItemDO kitchenItemDO = new KitchenItemDO();
        kitchenItemDO.setAreaGuid(orderTableReqDTO.getAreaGuid());
        kitchenItemDO.setTableGuid(orderTableReqDTO.getNewTableGuid());
        kitchenItemDO.setOrderSerialNo(orderTableReqDTO.getAreaGuid() + "-" + orderTableReqDTO.getNewTableName());
        return this.update(kitchenItemDO, new LambdaQueryWrapper<KitchenItemDO>()
                .eq(KitchenItemDO::getOrderGuid, orderTableReqDTO.getOrderGuid())
                .eq(KitchenItemDO::getTableGuid, orderTableReqDTO.getOriTableGuid()));
    }

    @Override
    @RequireNotification(assertResult = true)
    public boolean refund(ItemBatchRefundReqDTO itemBatchRefundReqDTO) {
        Map<String, ItemRefundReqDTO> orderItemMap = itemBatchRefundReqDTO.getItemRefundList().stream()
                .collect(Collectors.groupingBy(ItemRefundReqDTO::getOrderItemGuid))
                .values().stream()
                .map(items -> items.stream()
                        .reduce((pre, next) -> {
                            pre.setIsWeight(pre.getIsWeight() && next.getIsWeight());
                            pre.setNumber(pre.getNumber() + next.getNumber());
                            return pre;
                        }).orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ItemRefundReqDTO::getOrderItemGuid, Functions.identity()));
        List<String> orderItemGuidList = itemBatchRefundReqDTO.getItemRefundList().stream()
                .map(ItemRefundReqDTO::getOrderItemGuid).collect(Collectors.toList());
        List<KitchenItemDO> itemsInSql = this.list(new LambdaQueryWrapper<KitchenItemDO>()
                .select(KitchenItemDO::getId,
                        KitchenItemDO::getOrderGuid,
                        KitchenItemDO::getDisplayType,
                        KitchenItemDO::getKitchenState,
                        KitchenItemDO::getOrderItemGuid)
                .in(KitchenItemDO::getOrderItemGuid, orderItemGuidList)
                .ne(KitchenItemDO::getItemState, KdsItemStateEnum.REFUNDED.getCode())
                .orderByAsc(KitchenItemDO::getKitchenState));
        if (itemsInSql.isEmpty()) {
            return false;
        }
        log.info("kds退菜：itemsInSql:{}", JacksonUtils.writeValueAsString(itemsInSql));
        List<KitchenItemDO> itemsToUpdate = itemsInSql.stream()
                .collect(Collectors.groupingBy(KitchenItemDO::getOrderItemGuid))
                .entrySet().stream()
                .flatMap(entry -> {
                    entry.getValue().sort(Comparator.comparing(
                            KitchenItemDO::getKitchenState, Comparator.naturalOrder()
                    ));
                    ItemRefundReqDTO itemRefundReqDTO = orderItemMap.get(entry.getKey());
                    Assert.notNull(itemRefundReqDTO.getNumber(), "商品数量不得为空");
                    Assert.isTrue(itemRefundReqDTO.getNumber() > 0, "商品数量不得为0");
                    if (itemRefundReqDTO.getNumber() > entry.getValue().size()) {
                        log.error("商品[orderItemGuid:{}]数量{}大于可退数量{}",
                                itemRefundReqDTO.getOrderItemGuid(),
                                itemRefundReqDTO.getNumber(), entry.getValue().size());
                        throw new IllegalArgumentException("商品数量大于可退数量");
                    }
                    return entry.getValue().subList(0, itemRefundReqDTO.getNumber())
                            .stream()
                            .map(kitchenItemDO -> {
                                KitchenItemDO kitchenItemToRefund = new KitchenItemDO();
                                kitchenItemToRefund.setId(kitchenItemDO.getId());
                                kitchenItemToRefund.setItemState(KdsItemStateEnum.REFUNDED.getCode());
                                return kitchenItemToRefund;
                            })
                            .collect(Collectors.toList()).stream();
                })
                .collect(Collectors.toList());
        log.info("kds退菜：itemsToUpdate:{}", JacksonUtils.writeValueAsString(itemsToUpdate));
        if (CollectionUtils.isEmpty(itemsToUpdate)) {
            return false;
        }
        updateBatchById(itemsToUpdate);
        queueItemService.outPrepareQueue(AsyncTask.wrapper(itemsInSql.get(0)));
        return true;
    }

    @Override
    @RequireNotification
    public void cooking(ItemStateTransReqDTO itemStateTransReqDTO) {
        stateTrans(itemStateTransReqDTO.getPrdDstItemList(), KdsKitchenStateEnum.TO_PRD, KdsKitchenStateEnum.COOKING);

        if (isNeedToPrintAfterFiltering(itemStateTransReqDTO, queryPrintConfig(itemStateTransReqDTO))) {

            DeviceConfigDO deviceConfig = deviceConfigService.queryPrdDeviceByGuid(
                    itemStateTransReqDTO.getStoreGuid(), itemStateTransReqDTO.getDeviceId());

            if (DisplayModeEnum.PRD_SUMMARY_ITEM_ORDER.getDisplayMode().equals(deviceConfig.getDisplayMode())) {
                ItemStateTransReqDTO reqDTO = new ItemStateTransReqDTO();
                reqDTO.setDeviceId(deviceConfig.getGuid());

                List<PrdDstItemDTO> items2Print = new ArrayList<>();
                List<PrdDstItemDTO> orderItems2Print = new ArrayList();
                itemStateTransReqDTO.getPrdDstItemList().forEach(s -> {
                    if (s.getDisplayRuleType() == null || DisplayRuleType.OTHER == s.getDisplayRuleType()) {
                        s.setDisplayRuleType(DisplayRuleType.BATCH);
                    }
                    if (s.getDisplayRuleType() == DisplayRuleType.SUMMERY) {
                        items2Print.add(s);
                    }
                    if (s.getDisplayRuleType() == DisplayRuleType.BATCH) {
                        orderItems2Print.add(s);
                    }
                });
                if (!items2Print.isEmpty()) {
                    reqDTO.setPrdDstItemList(items2Print);
                    kdsPrintRecordService.manualPrintPrdItem(reqDTO, false);
                }
                if (!orderItems2Print.isEmpty()) {
                    reqDTO.setPrdDstItemList(orderItems2Print);
                    kdsPrintRecordService.manualPrintPrdItem(reqDTO, true);
                }
                return;
            }
            boolean isOrderModeInPoint = !Integer.valueOf(0).equals(itemStateTransReqDTO.getQueryModeForPoint());
            boolean isPrintPerOrder = deviceConfig.shouldPrintPerOrder(isOrderModeInPoint);
            kdsPrintRecordService.manualPrintPrdItem(itemStateTransReqDTO, isPrintPerOrder);
        }
    }

    @Override
    @RequireNotification
    public void complete(ItemStateTransReqDTO itemStateTransReqDTO) {
        stateTrans(itemStateTransReqDTO.getPrdDstItemList(), KdsKitchenStateEnum.COOKING, KdsKitchenStateEnum.TO_DST);
    }

    @Override
    @RequireNotification
    public void cancelComplete(ItemStateTransReqDTO itemStateTransReqDTO) {
        List<Integer> stateList = Lists.newArrayList(5, 6);
        stateTransCancelComplete(itemStateTransReqDTO.getPrdDstItemList(), stateList, KdsKitchenStateEnum.TO_PRD);
    }

    @Override
    @RequireNotification
    public List<PrdDstItemDTO> distribute(ItemStateTransReqDTO itemStateTransReqDTO) {
        stateTrans(itemStateTransReqDTO.getPrdDstItemList(), KdsKitchenStateEnum.NULL, KdsKitchenStateEnum.FINISHED);

        log.info("itemStateTransReqDTO={}", JacksonUtils.writeValueAsString(itemStateTransReqDTO));
        ItemStateTransReqDTO objCloned = (ItemStateTransReqDTO) DeepCloneUtils.cloneObject(itemStateTransReqDTO);
        log.info("objCloned={}", JacksonUtils.writeValueAsString(objCloned));
        queueItemService.inDistributedQueue(AsyncTask.wrapper(objCloned));

        DeviceConfigDO deviceConfigInSql = deviceConfigService.queryDstDispatchAsPrintByGuid(
                itemStateTransReqDTO.getStoreGuid(), itemStateTransReqDTO.getDeviceId());
        if (deviceConfigInSql.getIsDispatchAsPrint()) {
            kdsPrintRecordService.manualPrintDstItem(itemStateTransReqDTO);
        }
        objCloned.getPrdDstItemList().forEach(i -> {
            if (i.getIsWeight()) {
                return;
            }
            if (CollectionUtils.isEmpty(i.getKitchenItemList())) {
                return;
            }
            if (i.getKitchenItemList().size() <= i.getCurrentCount().intValue()) {
                return;
            }
            i.setKitchenItemList(i.getKitchenItemList().subList(0, i.getCurrentCount().intValue()));
        });
        return objCloned.getPrdDstItemList();
    }

    @Override
    @RequireNotification
    @Transactional(rollbackFor = Exception.class)
    public void cancelDistribute(ItemCancelDstReqDTO itemCancelDstReqDTO) {
        KitchenItemDO kitchenItemInSql = getOne(new LambdaQueryWrapper<KitchenItemDO>()
                .select(KitchenItemDO::getOrderGuid,
                        KitchenItemDO::getDisplayType,
                        KitchenItemDO::getCookTime,
                        KitchenItemDO::getDistributeTime,
                        KitchenItemDO::getDistributeStaffGuid,
                        KitchenItemDO::getDistributeStaffName)
                .eq(KitchenItemDO::getGuid, itemCancelDstReqDTO.getKitchenItemGuid())
                .eq(KitchenItemDO::getKitchenState, KdsKitchenStateEnum.FINISHED.getCode()));
        if (kitchenItemInSql == null) {
            return;
        }
        KitchenItemDO kitchenItemDO = new KitchenItemDO();
        kitchenItemDO.setGuid(itemCancelDstReqDTO.getKitchenItemGuid());
        kitchenItemDO.setKitchenState(KdsKitchenStateEnum.TO_DST.getCode());
        if (kitchenItemInSql.getCookTime() == null) {
            kitchenItemDO.setCookTime(kitchenItemInSql.getDistributeTime());
            kitchenItemDO.setCookStaffGuid(kitchenItemInSql.getDistributeStaffGuid());
            kitchenItemDO.setCookStaffName(kitchenItemInSql.getDistributeStaffName());
        }
        kitchenItemDO.setCancelDstTime(DateTimeUtils.now());
        kitchenItemDO.setCancelDstStaffGuid(UserContextUtils.getUserGuid());
        kitchenItemDO.setCancelDstStaffName(UserContextUtils.getUserName());
        baseMapper.cancelDistribute(kitchenItemDO);

        kitchenItemInSql.setGuid(itemCancelDstReqDTO.getKitchenItemGuid());
        queueItemService.backPreparedQueue(AsyncTask.wrapper(kitchenItemInSql));
    }

    @Override
    public Page<KitchenItemDTO> prdHistory(PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO) {
        List<String> kitchenItemGuidList = Lists.newArrayList();
        if (Boolean.TRUE.equals(prdDstItemHistoryReqDTO.getAllowRepeatFlag())) {
            // 查询设备绑定菜品记录
            List<KitchenItemDeviceDO> kitchenItemDeviceList = kitchenItemDeviceService
                    .limitListByDeviceId(prdDstItemHistoryReqDTO.getDeviceId());
            kitchenItemGuidList = kitchenItemDeviceList.stream()
                    .map(KitchenItemDeviceDO::getKitchenItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(kitchenItemGuidList)) {
                return new PageAdapter<>(prdDstItemHistoryReqDTO.getCurrentPage(), prdDstItemHistoryReqDTO.getPageSize());
            }
        }
        IPage<KitchenItemDO> page = page(new PageAdapter<>(prdDstItemHistoryReqDTO),
                historySearchWrapper(prdDstItemHistoryReqDTO)
                        .in(Boolean.TRUE.equals(prdDstItemHistoryReqDTO.getAllowRepeatFlag()),
                                KitchenItemDO::getGuid, kitchenItemGuidList)
                        .eq(KitchenItemDO::getStoreGuid, prdDstItemHistoryReqDTO.getStoreGuid())
                        .eq(!Boolean.TRUE.equals(prdDstItemHistoryReqDTO.getAllowRepeatFlag()),
                                KitchenItemDO::getPrdDeviceId, prdDstItemHistoryReqDTO.getDeviceId())
                        .ge(KitchenItemDO::getKitchenState, KdsKitchenStateEnum.COOKING.getCode())
                        .orderByDesc(KitchenItemDO::getCookTime)
        );
        return new PageAdapter<>(page, kitchenItemMapstruct.toKitchenItemDTO(page.getRecords()));
    }

    @Override
    public Page<KitchenItemDTO> dstHistory(PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO) {
        List<String> kitchenItemGuidList = Lists.newArrayList();
        if (Boolean.TRUE.equals(prdDstItemHistoryReqDTO.getAllowRepeatFlag())) {
            // 查询设备绑定菜品记录
            List<KitchenItemDeviceDO> kitchenItemDeviceList = kitchenItemDeviceService
                    .limitListByDeviceId(prdDstItemHistoryReqDTO.getDeviceId());
            kitchenItemGuidList = kitchenItemDeviceList.stream()
                    .map(KitchenItemDeviceDO::getKitchenItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(kitchenItemGuidList)) {
                return new PageAdapter<>(prdDstItemHistoryReqDTO.getCurrentPage(), prdDstItemHistoryReqDTO.getPageSize());
            }
        }
        IPage<KitchenItemDO> page = page(new PageAdapter<>(prdDstItemHistoryReqDTO),
                historySearchWrapper(prdDstItemHistoryReqDTO)
                        .in(Boolean.TRUE.equals(prdDstItemHistoryReqDTO.getAllowRepeatFlag()),
                                KitchenItemDO::getGuid, kitchenItemGuidList)
                        .eq(KitchenItemDO::getStoreGuid, prdDstItemHistoryReqDTO.getStoreGuid())
                        .eq(!Boolean.TRUE.equals(prdDstItemHistoryReqDTO.getAllowRepeatFlag()),
                                KitchenItemDO::getDstDeviceId, prdDstItemHistoryReqDTO.getDeviceId())
                        .eq(KitchenItemDO::getKitchenState, KdsKitchenStateEnum.FINISHED.getCode())
                        .orderByDesc(KitchenItemDO::getDistributeTime)
        );
        return new PageAdapter<>(page, kitchenItemMapstruct.toKitchenItemDTO(page.getRecords()));
    }

    @Override
    public void prdPrintAgain(KitchenItemDTO kitchenItemDTO) {
        printAgain(kitchenItemDTO, KdsInvoiceTypeEnum.PRD_ITEM);
    }

    @Override
    public void dstPrintAgain(KitchenItemDTO kitchenItemDTO) {
        printAgain(kitchenItemDTO, KdsInvoiceTypeEnum.DST_ITEM);
    }

    private String logWarnMsgOfUnMatchedItems(List<KdsItemDTO> kdsItemDTOS, List<String> skuGuidMatched) {
        List<KdsItemDTO> kdsItemsUnMatched = kdsItemDTOS.stream()
                .filter(kdsItemDTO -> !skuGuidMatched.contains(kdsItemDTO.getSkuGuid()))
                .collect(Collectors.toList());
        if (!kdsItemsUnMatched.isEmpty()) {
            String unbindMsg = kdsItemsUnMatched.stream()
                    .map(kdsItemDTO -> {
                        String skuName = StringUtils.hasText(kdsItemDTO.getSkuName())
                                ? "(" + kdsItemDTO.getSkuName() + ")" : "";
                        return kdsItemDTO.getItemName() + skuName + ":" + kdsItemDTO.getSkuGuid();
                    })
                    .collect(Collectors.joining(",", "[", "]"));
            log.warn("以下菜品未绑定：{}", unbindMsg);
            return unbindMsg;
        }
        return "";
    }


    @Override
    public void autoPrint(String orderGuid,
                          String storeGuid,
                          List<String> kitchenItemGuidList,
                          List<String> deviceGuidList) {
        autoPrint(orderGuid, storeGuid, kitchenItemGuidList, deviceGuidList, false);
    }

    @Override
    public void autoPrint(String orderGuid,
                          String storeGuid,
                          List<String> kitchenItemGuidList,
                          List<String> deviceGuidList, boolean isUrge) {
        Map<String, DeviceConfigDO> deviceConfigInSqlMap = deviceConfigService.listPrdDeviceByGuid(storeGuid, deviceGuidList);
        autoPrint(orderGuid, kitchenItemGuidList, deviceConfigInSqlMap, isUrge);
    }

    @Override
    public List<KitchenItemRespDTO> queryByOrderItem(ItemBatchRefundReqDTO query) {
        List<String> orderItemGuidList = query.getItemRefundList().stream()
                .map(ItemRefundReqDTO::getOrderItemGuid)
                .collect(Collectors.toList());
        List<KitchenItemDO> itemsInSql = this.list(new LambdaQueryWrapper<KitchenItemDO>()
                .select(KitchenItemDO::getId,
                        KitchenItemDO::getOrderGuid,
                        KitchenItemDO::getDisplayType,
                        KitchenItemDO::getItemState,
                        KitchenItemDO::getKitchenState,
                        KitchenItemDO::getOrderItemGuid)
                .in(KitchenItemDO::getOrderItemGuid, orderItemGuidList)
                .ne(KitchenItemDO::getItemState, KdsItemStateEnum.REFUNDED.getCode())
                .orderByAsc(KitchenItemDO::getKitchenState));

        List<KitchenItemRespDTO> respDTOList = new ArrayList<>();
        Map<String, Integer> refundNumberMap = query.getItemRefundList().stream()
                .collect(Collectors.toMap(ItemRefundReqDTO::getOrderItemGuid, ItemRefundReqDTO::getNumber));
        Map<String, List<KitchenItemDO>> kitchenItemMap = itemsInSql.stream()
                .collect(Collectors.groupingBy(KitchenItemDO::getOrderItemGuid));
        for (Map.Entry<String, List<KitchenItemDO>> entry : kitchenItemMap.entrySet()) {
            List<KitchenItemDO> kitchenItemList = entry.getValue();
            Integer number = refundNumberMap.get(entry.getKey());
            if (number > kitchenItemList.size()) {
                number = kitchenItemList.size();
            }
            kitchenItemList = kitchenItemList.subList(0, number);
            Set<Integer> kitchenStateSet = kitchenItemList.stream()
                    .map(KitchenItemDO::getKitchenState)
                    .collect(Collectors.toSet());
            KitchenItemRespDTO respDTO = new KitchenItemRespDTO();
            respDTO.setOrderItemGuid(entry.getKey());
            respDTO.setIsOutDinner(kitchenStateSet.contains(KdsKitchenStateEnum.FINISHED.getCode()));
            respDTOList.add(respDTO);
        }
        return respDTOList;
    }

    private void autoPrint(String orderGuid,
                           List<String> backupItemGuids,
                           Map<String, DeviceConfigDO> deviceConfigInSqlMap, boolean isUrge) {
        List<DeviceConfigDO> autoPrintDeviceList = deviceConfigInSqlMap.values().stream()
                .filter(DeviceConfigDO::getIsPrintAutomatic)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(autoPrintDeviceList) && !CollectionUtils.isEmpty(backupItemGuids)) {
            List<String> autoPrintDeviceIdList = autoPrintDeviceList.stream()
                    .map(BaseDO::getGuid).collect(Collectors.toList());
            List<KitchenItemReadDO> kitchenItemReadInSql = baseMapper.queryPrepareItem(
                    ItemQuery.of(orderGuid, autoPrintDeviceIdList, backupItemGuids)
            );
            Map<String, Boolean> devicePrintModeMap = autoPrintDeviceList.stream()
                    .collect(Collectors.toMap(BaseDO::getGuid, DeviceConfigDO::shouldPrintPerOrder));
            Map<String, List<KitchenItemReadDO>> deviceKitchenItemMap = kitchenItemReadInSql.stream()
                    .filter(k -> k.getUrgedTime() == null || isUrge)
                    .collect(Collectors.groupingBy(KitchenItemReadDO::getPrdDeviceId));
            for (Map.Entry<String, List<KitchenItemReadDO>> entry : deviceKitchenItemMap.entrySet()) {
                String deviceId = entry.getKey();
                DeviceConfigDO deviceConfig = deviceConfigInSqlMap.get(deviceId);
                PrdRespBO prdRespBO = PrdRespBO.builder()
                        .deviceConfig(deviceConfig)
                        .isAllType(true).isOrderMode(true).build();

                if (DisplayModeEnum.PRD_SUMMARY_ITEM_ORDER.getDisplayMode().equals(deviceConfig.getDisplayMode())) {
                    printByMixMode(deviceConfig, entry.getValue());
                    return;
                }

                List<PrdDstItemDTO> prdDstItemList = prdRespBO.getPrdDstItemList(entry.getValue());
                kdsPrintRecordService.autoPrintPrdItem(deviceId, prdDstItemList, devicePrintModeMap.getOrDefault(deviceId, false));
            }
        }
    }

    private void printByMixMode(DeviceConfigDO deviceConfig, List<KitchenItemReadDO> kitchenItemReadInSql) {
        PrdRespBO prdRespBO = PrdRespBO.builder()
                .deviceConfig(deviceConfig)
                .isAllType(true).isOrderMode(true).build();
        ItemStateTransReqDTO reqDTO = new ItemStateTransReqDTO();
        reqDTO.setDeviceId(deviceConfig.getGuid());

        List<KitchenItemReadDO> items = KitchenItemUtils.filterByDisplayType(kitchenItemReadInSql, DisplayRuleType.SUMMERY);
        if (!CollectionUtils.isEmpty(items)) {
            List<PrdDstItemDTO> items2Print = prdRespBO.getPrdDstItemList(items);
            reqDTO.setPrdDstItemList(items2Print);
            kdsPrintRecordService.manualPrintPrdItem(reqDTO, false);
        }

        List<KitchenItemReadDO> orderItems = KitchenItemUtils.filterByDisplayType(kitchenItemReadInSql, DisplayRuleType.BATCH);
        if (CollectionUtils.isEmpty(orderItems)) {
            return;
        }
        List<PrdDstItemDTO> items2Print = prdRespBO.getPrdDstItemList(orderItems);
        reqDTO.setPrdDstItemList(items2Print);
        kdsPrintRecordService.manualPrintPrdItem(reqDTO, true);
    }


    private void preparePushVoiceAndChangedStatus(Integer tradeMode, String storeGuid,
                                                  Map<String, DeviceConfigDO> deviceConfigInSqlMap
    ) {
        // 语音推送
        kdsStatusPushService.statusChanged(tradeMode, deviceConfigInSqlMap.values());
        // 状态变更推送
        List<String> deviceToPush = deviceConfigService.listDeviceOfStore(storeGuid).stream()
                .map(BaseDO::getGuid).collect(Collectors.toList());
        List<String> devicePushed = deviceConfigInSqlMap.values().stream()
                .map(BaseDO::getGuid).collect(Collectors.toList());
        deviceToPush.removeAll(devicePushed);
        UserContext userContext = UserContextUtils.get();
        String enterpriseGuid = userContext.getEnterpriseGuid();
        for (String deviceId : deviceToPush) {
            kdsStatusPushService.statusChanged(enterpriseGuid, storeGuid, deviceId, "");
        }
    }

    private void stateTrans(List<PrdDstItemDTO> prdDstItemList, KdsKitchenStateEnum oldState, KdsKitchenStateEnum newState) {
        // fixme 暂时遍历处理，后续需批量处理，提升性能
        log.info("撤销状态：{}===>撤销菜品参数：{}", newState, JacksonUtils.writeValueAsString(prdDstItemList));
        Iterator<PrdDstItemDTO> iterator = prdDstItemList.iterator();
        while (iterator.hasNext()) {
            PrdDstItemDTO prdDstItemDTO = iterator.next();
            if (prdDstItemDTO.getIsWeight()) {
                PrdDstItemTableDTO weightKitchenItem = prdDstItemDTO.getWeightKitchenItem();
                Assert.notNull(weightKitchenItem, "称重商品不得为空");
                LambdaQueryWrapper<KitchenItemDO> wrapper = new LambdaQueryWrapper<KitchenItemDO>()
                        .eq(KitchenItemDO::getGuid, weightKitchenItem.getKitchenItemGuid())
                        .eq(oldState.getCode() > KdsKitchenStateEnum.NULL.getCode(),
                                KitchenItemDO::getKitchenState, oldState.getCode())
                        .ne(KdsKitchenStateEnum.FINISHED.equals(newState),
                                KitchenItemDO::getKitchenState, KdsKitchenStateEnum.FINISHED.getCode());
                KitchenItemDO kitchenItemInSql = this.getOne(wrapper);
                if (kitchenItemInSql == null) {
                    iterator.remove();
                    continue;
                }
                BigDecimal subtract = kitchenItemInSql.getCurrentCount().subtract(prdDstItemDTO.getCurrentCount());
                if (subtract.compareTo(BigDecimal.ZERO) <= 0) {
                    // 更新商品状态
                    KitchenItemDO kitchenItemDO = new KitchenItemDO();
                    stateTrans(kitchenItemDO, oldState, newState);
                    if (!this.update(kitchenItemDO, wrapper)) {
                        iterator.remove();
                    }
                    continue;
                }
                // 更新商品数量
                KitchenItemDO kitchenItemDO = new KitchenItemDO();
                kitchenItemDO.setCurrentCount(subtract);
                this.update(kitchenItemDO, wrapper);
                // 裂变商品为2个商品
                KitchenItemDO weightItemCloned = (KitchenItemDO) DeepCloneUtils.cloneObject(kitchenItemInSql);
                weightItemCloned.setGuid(distributedIdService.nextKitchenItemGuid());
                weightItemCloned.setCurrentCount(prdDstItemDTO.getCurrentCount());
                stateTrans(weightItemCloned, oldState, newState);
                this.save(weightItemCloned);
                continue;
            }
            BigDecimal cookingCount = prdDstItemDTO.getCurrentCount();
            if (cookingCount.intValue() == 0) {
                iterator.remove();
                String name = prdDstItemDTO.getItemName();
                if (StringUtils.hasText(prdDstItemDTO.getSkuName())) {
                    name = name + "[" + prdDstItemDTO.getSkuName() + "]";
                }
                log.warn("挂起菜品，跳过：itemGuid={}, skuGuid={}, name={}",
                        prdDstItemDTO.getItemGuid(), prdDstItemDTO.getSkuGuid(), name);
                continue;
            }
            List<PrdDstItemTableDTO> kitchenItemList = prdDstItemDTO.getKitchenItemList();
            Assert.notNull(kitchenItemList, "商品列表不得为空");
            Assert.notEmpty(kitchenItemList, "商品列表不得为空");
            Assert.isTrue(cookingCount.intValue() <= kitchenItemList.size(), "商品数量不得大于列表Size");
            int cookingCounter = 0;
            List<String> kitchenItemGuidList = kitchenItemList.stream()
                    .map(PrdDstItemTableDTO::getKitchenItemGuid)
                    .collect(Collectors.toList());
            List<String> toDstList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(kitchenItemGuidList)) {
                List<KitchenItemDO> kitchenItemDOList = this.list(new LambdaQueryWrapper<KitchenItemDO>()
                        .and(q -> q.eq(KitchenItemDO::getKitchenState, KdsKitchenStateEnum.COOKING.getCode())
                                .or()
                                .eq(KitchenItemDO::getKitchenState, KdsKitchenStateEnum.TO_DST.getCode()))
                        .in(KitchenItemDO::getGuid, kitchenItemGuidList));
                if (CollectionUtils.isNotEmpty(kitchenItemDOList)) {
                    toDstList = kitchenItemDOList.stream()
                            .map(KitchenItemDO::getGuid)
                            .collect(Collectors.toList());
                }
            }
            Iterator<PrdDstItemTableDTO> innerIterator = kitchenItemList.iterator();
            if (CollectionUtils.isNotEmpty(toDstList)) {
                List<String> finalToDstList = toDstList;
                List<PrdDstItemTableDTO> beforeList = kitchenItemList.stream()
                        .filter(o1 -> finalToDstList.contains(o1.getKitchenItemGuid()))
                        .collect(Collectors.toList());
                kitchenItemList.removeAll(beforeList);
                beforeList.addAll(kitchenItemList);
                innerIterator = beforeList.iterator();
                kitchenItemList.addAll(beforeList);
            }
            while (innerIterator.hasNext() && cookingCounter++ < cookingCount.intValue()) {
                PrdDstItemTableDTO prdDstItemTableDTO = innerIterator.next();
                KitchenItemDO kitchenItemDO = new KitchenItemDO();
                stateTrans(kitchenItemDO, oldState, newState);
                log.info("撤销菜品组装菜品状态：{}", JacksonUtils.writeValueAsString(kitchenItemDO));
                if (!this.update(kitchenItemDO, new LambdaQueryWrapper<KitchenItemDO>()
                        .eq(KitchenItemDO::getGuid, prdDstItemTableDTO.getKitchenItemGuid())
                        .eq(oldState.getCode() > KdsKitchenStateEnum.NULL.getCode(),
                                KitchenItemDO::getKitchenState, oldState.getCode())
                        .ne(KdsKitchenStateEnum.FINISHED.equals(newState),
                                KitchenItemDO::getKitchenState, KdsKitchenStateEnum.FINISHED.getCode()))) {
                    innerIterator.remove();
                }
            }
            if (kitchenItemList.isEmpty()) {
                iterator.remove();
            }
        }
    }

    private void stateTransCancelComplete(List<PrdDstItemDTO> prdDstItemList, List<Integer> stateList, KdsKitchenStateEnum newState) {
        // fixme 暂时遍历处理，后续需批量处理，提升性能
        Iterator<PrdDstItemDTO> iterator = prdDstItemList.iterator();
        while (iterator.hasNext()) {
            PrdDstItemDTO prdDstItemDTO = iterator.next();
            if (prdDstItemDTO.getIsWeight()) {
                PrdDstItemTableDTO weightKitchenItem = prdDstItemDTO.getWeightKitchenItem();
                Assert.notNull(weightKitchenItem, "称重商品不得为空");
                LambdaQueryWrapper<KitchenItemDO> wrapper = new LambdaQueryWrapper<KitchenItemDO>()
                        .eq(KitchenItemDO::getGuid, weightKitchenItem.getKitchenItemGuid())
                        .in(KitchenItemDO::getKitchenState, stateList)
                        .ne(KdsKitchenStateEnum.FINISHED.equals(newState),
                                KitchenItemDO::getKitchenState, KdsKitchenStateEnum.FINISHED.getCode());
                KitchenItemDO kitchenItemInSql = this.getOne(wrapper);
                if (kitchenItemInSql == null) {
                    iterator.remove();
                    continue;
                }
                BigDecimal subtract = kitchenItemInSql.getCurrentCount().subtract(prdDstItemDTO.getCurrentCount());
                if (subtract.compareTo(BigDecimal.ZERO) <= 0) {
                    // 更新商品状态
                    KitchenItemDO kitchenItemDO = new KitchenItemDO();
//                    stateTrans(kitchenItemDO,KdsKitchenStateEnum.ofCode(kitchenItemDO.getKitchenState()) , newState);
                    kitchenItemDO.setKitchenState(newState.getCode());
                    kitchenItemDO.setCompleteTime(DateTimeUtils.now());
                    kitchenItemDO.setCompleteStaffGuid(UserContextUtils.getUserGuid());
                    kitchenItemDO.setCompleteStaffName(UserContextUtils.getUserName());
                    if (!this.update(kitchenItemDO, wrapper)) {
                        iterator.remove();
                    }
                    continue;
                }
                // 更新商品数量
                KitchenItemDO kitchenItemDO = new KitchenItemDO();
                kitchenItemDO.setCurrentCount(subtract);
                this.update(kitchenItemDO, wrapper);
                // 裂变商品为2个商品
                KitchenItemDO weightItemCloned = (KitchenItemDO) DeepCloneUtils.cloneObject(kitchenItemInSql);
                weightItemCloned.setGuid(distributedIdService.nextKitchenItemGuid());
                weightItemCloned.setCurrentCount(prdDstItemDTO.getCurrentCount());
                stateTrans(weightItemCloned, KdsKitchenStateEnum.ofCode(weightItemCloned.getKitchenState()), newState);
                this.save(weightItemCloned);
                continue;
            }
            BigDecimal cookingCount = prdDstItemDTO.getCurrentCount();
            if (cookingCount.intValue() == 0) {
                iterator.remove();
                String name = prdDstItemDTO.getItemName();
                if (StringUtils.hasText(prdDstItemDTO.getSkuName())) {
                    name = name + "[" + prdDstItemDTO.getSkuName() + "]";
                }
                log.warn("挂起菜品，跳过：itemGuid={}, skuGuid={}, name={}",
                        prdDstItemDTO.getItemGuid(), prdDstItemDTO.getSkuGuid(), name);
                continue;
            }
            List<PrdDstItemTableDTO> kitchenItemList = prdDstItemDTO.getKitchenItemList();
            Assert.notNull(kitchenItemList, "商品列表不得为空");
            Assert.notEmpty(kitchenItemList, "商品列表不得为空");
            Assert.isTrue(cookingCount.intValue() <= kitchenItemList.size(), "商品数量不得大于列表Size");
            Iterator<PrdDstItemTableDTO> innerIterator = kitchenItemList.iterator();
            int cookingCounter = 0;
            while (innerIterator.hasNext() && cookingCounter++ < cookingCount.intValue()) {
                PrdDstItemTableDTO prdDstItemTableDTO = innerIterator.next();
                KitchenItemDO kitchenItemDO = new KitchenItemDO();
                kitchenItemDO.setKitchenState(newState.getCode());
                kitchenItemDO.setCompleteTime(DateTimeUtils.now());
                kitchenItemDO.setCompleteStaffGuid(UserContextUtils.getUserGuid());
                kitchenItemDO.setCompleteStaffName(UserContextUtils.getUserName());
                if (!this.update(kitchenItemDO, new LambdaQueryWrapper<KitchenItemDO>()
                        .eq(KitchenItemDO::getGuid, prdDstItemTableDTO.getKitchenItemGuid())
                        .in(KitchenItemDO::getKitchenState, stateList)
                        .ne(KdsKitchenStateEnum.FINISHED.equals(newState),
                                KitchenItemDO::getKitchenState, KdsKitchenStateEnum.FINISHED.getCode()))) {
                    innerIterator.remove();
                }
            }
            if (kitchenItemList.isEmpty()) {
                iterator.remove();
            }
        }
    }

    private void stateTrans(KitchenItemDO kitchenItemDO, KdsKitchenStateEnum oldState, KdsKitchenStateEnum newState) {
        kitchenItemDO.setKitchenState(newState.getCode());
        switch (oldState) {
            case TO_PRD:
                kitchenItemDO.setCookTime(DateTimeUtils.now());
                kitchenItemDO.setCookStaffGuid(UserContextUtils.getUserGuid());
                kitchenItemDO.setCookStaffName(UserContextUtils.getUserName());
                break;
            case COOKING:
                kitchenItemDO.setCompleteTime(DateTimeUtils.now());
                kitchenItemDO.setCompleteStaffGuid(UserContextUtils.getUserGuid());
                kitchenItemDO.setCompleteStaffName(UserContextUtils.getUserName());
                break;
            case NULL:
            case TO_DST:
                kitchenItemDO.setDistributeTime(DateTimeUtils.now());
                kitchenItemDO.setDistributeStaffGuid(UserContextUtils.getUserGuid());
                kitchenItemDO.setDistributeStaffName(UserContextUtils.getUserName());
                break;
            case FINISHED:
                break;
        }
    }

    private Map<String, Boolean> queryPrintConfig(ItemStateTransReqDTO itemStateTransReqDTO) {
        if (CollectionUtils.isEmpty(itemStateTransReqDTO.getPrdDstItemList())) {
            return Collections.emptyMap();
        }
        List<String> kitchenItemGuidList = itemStateTransReqDTO.getPrdDstItemList().stream()
                .flatMap(prdDstItemDTO -> {
                    if (prdDstItemDTO.getIsWeight()) {
                        PrdDstItemTableDTO weightKitchenItem = Objects.requireNonNull(prdDstItemDTO.getWeightKitchenItem());
                        return Stream.of(weightKitchenItem.getKitchenItemGuid());
                    }
                    List<PrdDstItemTableDTO> kitchenItemList = Objects.requireNonNull(prdDstItemDTO.getKitchenItemList());
                    return kitchenItemList.stream().map(PrdDstItemTableDTO::getKitchenItemGuid);
                })
                .collect(Collectors.toList());
        List<KitchenItemDO> result = list(new LambdaQueryWrapper<KitchenItemDO>()
                .select(KitchenItemDO::getGuid,
                        KitchenItemDO::getIsPrintAutomatic)
                .in(KitchenItemDO::getGuid, kitchenItemGuidList));
        return result.stream().collect(Collectors.toMap(KitchenItemDO::getGuid, KitchenItemDO::getIsPrintAutomatic));
    }

    private boolean isNeedToPrintAfterFiltering(ItemStateTransReqDTO itemStateTransReqDTO, Map<String, Boolean> printStatus) {
        if (MapUtils.isEmpty(printStatus)) {
            return false;
        }
        List<PrdDstItemDTO> collect = itemStateTransReqDTO.getPrdDstItemList().stream()
                .filter(prdDstItemDTO -> {
                    if (prdDstItemDTO.getIsWeight()) {
                        PrdDstItemTableDTO weightKitchenItem = Objects.requireNonNull(prdDstItemDTO.getWeightKitchenItem());
                        return !printStatus.getOrDefault(weightKitchenItem.getKitchenItemGuid(), false);
                    }
                    List<PrdDstItemTableDTO> kitchenItemList = Objects.requireNonNull(prdDstItemDTO.getKitchenItemList());
                    List<PrdDstItemTableDTO> kitchenItemsNeedPrint = kitchenItemList.stream()
                            .filter(prdDstItemTableDTO ->
                                    !printStatus.getOrDefault(prdDstItemTableDTO.getKitchenItemGuid(), false))
                            .collect(Collectors.toList());
                    prdDstItemDTO.setKitchenItemList(kitchenItemsNeedPrint);
                    return !kitchenItemsNeedPrint.isEmpty();
                })
                .collect(Collectors.toList());
        itemStateTransReqDTO.setPrdDstItemList(collect);
        return !CollectionUtils.isEmpty(collect);
    }

    private void printAgain(KitchenItemDTO kitchenItemDTO, KdsInvoiceTypeEnum kdsInvoiceTypeEnum) {
        PrdDstItemDTO prdDstItemDTO = kitchenItemMapstruct.kitchenToPrdDstItem(kitchenItemDTO);
        PrdDstItemTableDTO prdDstItemTableDTO = PrdDstItemTableDTO.ofKitchenItemDTO(kitchenItemDTO);
        if (kitchenItemDTO.getIsWeight()) {
            prdDstItemDTO.setWeightKitchenItem(prdDstItemTableDTO);
        } else {
            prdDstItemDTO.setKitchenItemList(Collections.singletonList(prdDstItemTableDTO));
        }
        String deviceId = KdsInvoiceTypeEnum.PRD_ITEM == kdsInvoiceTypeEnum
                ? kitchenItemDTO.getPrdDeviceId() : kitchenItemDTO.getDstDeviceId();
        kdsPrintRecordService.printSingleItem(deviceId, prdDstItemDTO, kdsInvoiceTypeEnum);
    }

    /**
     * fixme
     *
     * @param prdDstItemHistoryReqDTO
     * @return
     */
    private LambdaQueryWrapper<KitchenItemDO> historySearchWrapper(PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO) {
        return new LambdaQueryWrapper<KitchenItemDO>()
                .nested(StringUtils.hasText(prdDstItemHistoryReqDTO.getTableName()), wrapper -> wrapper
                        .eq(KitchenItemDO::getDisplayType, 4)
                        .like(KitchenItemDO::getOrderSerialNo, prdDstItemHistoryReqDTO.getTableName())
                )
                .like(StringUtils.hasText(prdDstItemHistoryReqDTO.getOrderNumber()),
                        KitchenItemDO::getOrderNumber, prdDstItemHistoryReqDTO.getOrderNumber())
                .nested(StringUtils.hasText(prdDstItemHistoryReqDTO.getItemSearchKey()), wrapper -> wrapper
                        .like(KitchenItemDO::getItemName, prdDstItemHistoryReqDTO.getItemSearchKey())
                        .or()
                        .like(KitchenItemDO::getSkuName, prdDstItemHistoryReqDTO.getItemSearchKey())
                        .or()
                        .like(KitchenItemDO::getSkuCode, prdDstItemHistoryReqDTO.getItemSearchKey())
                )
                .between(KitchenItemDO::getItemState, KdsItemStateEnum.PREPARE.getCode(), KdsItemStateEnum.CALL_UP.getCode());
    }

    @Override
    public List<PrdDstItemDTO> queryByOrder(PrdDstItemQueryDTO query) {
        List<KitchenItemDO> kitchenItemDOList = this.list(new LambdaQueryWrapper<KitchenItemDO>()
                .eq(StringUtils.hasText(query.getOrderGuid()), KitchenItemDO::getOrderGuid, query.getOrderGuid())
                .in(CollectionUtils.isNotEmpty(query.getOrderItemGuidList()), KitchenItemDO::getOrderItemGuid, query.getOrderItemGuidList())
                .ne(KitchenItemDO::getItemState, KdsItemStateEnum.REFUNDED.getCode())
                .ne(KitchenItemDO::getKitchenState, KdsKitchenStateEnum.FINISHED.getCode())
                .orderByAsc(KitchenItemDO::getKitchenState));

        List<PrdDstItemDTO> respDTOList = new ArrayList<>();
        kitchenItemDOList.forEach(kitchenItemDO -> {
            PrdDstItemDTO prdDstItemDTO = getPrdDstItemDTO(kitchenItemDO);
            respDTOList.add(prdDstItemDTO);
        });
        return respDTOList;
    }

    @Override
    public void batchDistribute(List<ItemStateTransReqDTO> reqDTOList) {
        reqDTOList.forEach(reqDTO -> {
            // 菜谱出堂
            this.distribute(reqDTO);

            // 发送语音消息
            DeviceQueryReqDTO deviceQueryReqDTO = new DeviceQueryReqDTO(reqDTO.getStoreGuid(), reqDTO.getDeviceId());
            DstBindStatusRespDTO dstConfig = distributeAreaService.queryAreaBindingPreview(deviceQueryReqDTO);
            String voiceMsg = getTakeFoodVoiceMsg(reqDTO, dstConfig);
            kdsStatusPushService.voiceBroadcast(reqDTO.getEnterpriseGuid(), reqDTO.getStoreGuid(), reqDTO.getDeviceId(), voiceMsg);

            // 刷新状态
            kdsStatusPushService.statusChanged(reqDTO.getEnterpriseGuid(), reqDTO.getStoreGuid(), reqDTO.getDeviceId(), "");
        });
    }


    @Override
    public void transfer(KdsItemTransferReqDTO transferReqDTO) {
        TransferItemReqDTO transferReq = transferReqDTO.getTransferReq();
        HashMap<Long, OrderItemDTO> old2NewMap = transferReqDTO.getOld2NewMap();
        DineinOrderDetailRespDTO orderDetailRespDTO = transferReqDTO.getOrderDetailRespDTO();

        List<TransferItemDetailsReqDTO> itemDetailsDTOList = transferReq.getItemDetailsDTOList();
        Map<String, BigDecimal> transferItemMap = itemDetailsDTOList.stream()
                .collect(Collectors.toMap(TransferItemDetailsReqDTO::getOrderItemGuid, TransferItemDetailsReqDTO::getTransferCount, (v1, v2) -> v1));
        List<KitchenItemDO> kitchenItemDOList = this.list(new LambdaQueryWrapper<KitchenItemDO>()
                .eq(KitchenItemDO::getOrderGuid, transferReq.getOldOrderGuid())
                .in(KitchenItemDO::getOrderItemGuid, transferItemMap.keySet()));
        Map<String, List<KitchenItemDO>> itemNumMap = kitchenItemDOList.stream()
                .collect(Collectors.groupingBy(KitchenItemDO::getOrderItemGuid));
        TableDTO tableDTO = tableRpcService.getTableByGuid(orderDetailRespDTO.getDiningTableGuid());
        log.info("[根据桌台guid查桌台详情]tableDTO={}", JacksonUtils.writeValueAsString(tableDTO));
        if (ObjectUtils.isEmpty(tableDTO)) {
            log.warn("桌台查询失败 diningTableGuid={}", orderDetailRespDTO.getDiningTableGuid());
            return;
        }
        List<KitchenItemDO> newKitchenItemDOList = new ArrayList<>();
        List<Long> oldKitchenItemidList = new ArrayList<>();
        Map<String, String> kitchenItemMappingMap = Maps.newHashMap();
        itemNumMap.forEach((orderItemGuid, kitchenItemDOS) -> {
            OrderItemDTO orderItemDTO = old2NewMap.get(Long.valueOf(orderItemGuid));
            log.info("[异常排查]orderItemDTO={}", JacksonUtils.writeValueAsString(orderItemDTO));
            if (ObjectUtils.isEmpty(orderItemDTO)) {
                return;
            }

            BigDecimal transferCount = orderItemDTO.getCurrentCount();
            if (Objects.equals(orderItemDTO.getItemType(), ItemTypeEnum.WEIGH.getCode())) {
                // 称重商品
                transferCount = BigDecimal.ONE;
            }
            List<KitchenItemDO> subKitchenItemList = kitchenItemDOS.subList(0,
                    Integer.parseInt(transferCount.stripTrailingZeros().toPlainString()));

            List<String> kitchenItemGuidList = distributedIdService.nextBatchKitchenItemGuid(subKitchenItemList.size());
            subKitchenItemList.forEach(kitchenItemDO -> {
                oldKitchenItemidList.add(kitchenItemDO.getId());
                KitchenItemDO newKitchenItemDO = new KitchenItemDO();
                BeanUtils.copyProperties(kitchenItemDO, newKitchenItemDO);
                newKitchenItemDO.setOrderGuid(transferReq.getNewOrderGuid());
                newKitchenItemDO.setOrderItemGuid(String.valueOf(orderItemDTO.getGuid()));
                newKitchenItemDO.setOrderNumber(orderDetailRespDTO.getOrderNo());
                newKitchenItemDO.setTableGuid(orderDetailRespDTO.getDiningTableGuid());
                newKitchenItemDO.setAreaGuid(tableDTO.getAreaGuid());
                newKitchenItemDO.setOrderSerialNo(tableDTO.getAreaName() + "-" + tableDTO.getCode());
                newKitchenItemDO.setId(null);
                newKitchenItemDO.setGuid(kitchenItemGuidList.remove(0));
                newKitchenItemDO.setGmtCreate(null);
                newKitchenItemDO.setGmtModified(null);
                kitchenItemMappingMap.put(kitchenItemDO.getGuid(), newKitchenItemDO.getGuid());
                newKitchenItemDOList.add(newKitchenItemDO);
            });
        });
        this.saveBatch(newKitchenItemDOList);
        if (CollectionUtils.isNotEmpty(oldKitchenItemidList)) {
            kitchenItemMapper.deleteBatchIds(oldKitchenItemidList);
            updateBatchKitchenItemGuid(kitchenItemMappingMap);
        }
        List<KitchenItemAttrDO> oldItemAttrDOList = kitchenItemAttrService.listByOrderItemGuids(transferItemMap.keySet());
        if (!org.springframework.util.CollectionUtils.isEmpty(oldItemAttrDOList)) {
            List<String> kitchenItemAttrGuidList = distributedIdService.nextBatchKitchenItemGuid(oldItemAttrDOList.size());
            List<KitchenItemAttrDO> newItemAttrDOList = new ArrayList<>();
            oldItemAttrDOList.forEach(itemAttrDO -> {
                OrderItemDTO orderItemDTO = old2NewMap.get(Long.valueOf(itemAttrDO.getOrderItemGuid()));
                if (ObjectUtils.isEmpty(orderItemDTO)) {
                    return;
                }
                KitchenItemAttrDO attrDO = new KitchenItemAttrDO();
                BeanUtils.copyProperties(itemAttrDO, attrDO);
                attrDO.setGuid(kitchenItemAttrGuidList.remove(0));
                attrDO.setOrderItemGuid(String.valueOf(orderItemDTO.getGuid()));
                newItemAttrDOList.add(attrDO);
            });
            kitchenItemAttrService.saveBatch(newItemAttrDOList);
        }

        // 刷新状态
        List<String> deviceIdSet = buildTransferKitchenItemDeviceIds(newKitchenItemDOList);
        deviceIdSet.forEach(deviceId -> kdsStatusPushService.statusChanged(transferReq.getEnterpriseGuid(),
                transferReq.getStoreGuid(), deviceId, ""));
    }

    private void updateBatchKitchenItemGuid(Map<String, String> kitchenItemMappingMap) {
        List<KitchenItemDeviceDO> kitchenItemDeviceDOList = Lists.newArrayList();
        for (Map.Entry<String, String> entry : kitchenItemMappingMap.entrySet()) {
            KitchenItemDeviceDO kitchenItemDeviceDO = new KitchenItemDeviceDO();
            kitchenItemDeviceDO.setOriginalKitchenItemGuid(entry.getKey());
            kitchenItemDeviceDO.setKitchenItemGuid(entry.getValue());
            kitchenItemDeviceDOList.add(kitchenItemDeviceDO);
        }
        kitchenItemDeviceService.updateBatchKitchenItemGuid(kitchenItemDeviceDOList);
    }

    private List<String> buildTransferKitchenItemDeviceIds(List<KitchenItemDO> newKitchenItemDOList) {
        Set<String> deviceIdSet = Sets.newHashSet();
        for (KitchenItemDO kitchenItemDO : newKitchenItemDOList) {
            deviceIdSet.add(kitchenItemDO.getPrdDeviceId());
            deviceIdSet.add(kitchenItemDO.getDstDeviceId());
        }
        List<String> kitchenItemGuids = newKitchenItemDOList.stream()
                .map(KitchenItemDO::getGuid)
                .collect(Collectors.toList());
        List<KitchenItemDeviceDO> kitchenItemDeviceList = kitchenItemDeviceService.listDeviceIdByKitchenItemGuids(kitchenItemGuids);
        Set<String> kitchenItemDeviceIds = kitchenItemDeviceList.stream()
                .map(KitchenItemDeviceDO::getDeviceId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(kitchenItemDeviceIds)) {
            deviceIdSet.addAll(kitchenItemDeviceIds);
        }
        deviceIdSet.remove(null);
        deviceIdSet.remove(Strings.EMPTY);
        log.info("transfer Kitchen Item DeviceIds:{}", JacksonUtils.writeValueAsString(deviceIdSet));
        return new ArrayList<>(deviceIdSet);
    }

    private String getTakeFoodVoiceMsg(ItemStateTransReqDTO reqDTO, DstBindStatusRespDTO dstConfig) {
        String voiceMsg = "";
        if (Objects.equals(ScanFinishFoodTypeEnum.FAST.getCode(), reqDTO.getOrderDifference())
                && Boolean.TRUE.equals(dstConfig.getIsSnackBound())) {
            voiceMsg = "请" + reqDTO.getOrderDesc() + "前来取餐";
        }
        if (Objects.equals(ScanFinishFoodTypeEnum.TAKEAWAY.getCode(), reqDTO.getOrderDifference())
                && Boolean.TRUE.equals(dstConfig.getIsTakeoutBound())) {
            voiceMsg = "请外卖订单" + reqDTO.getOrderDesc() + "前来取餐";
        }
        return voiceMsg;
    }

    private PrdDstItemDTO getPrdDstItemDTO(KitchenItemDO kitchenItemDO) {
        PrdDstItemDTO prdDstItemDTO = new PrdDstItemDTO();
        prdDstItemDTO.setCurrentCount(kitchenItemDO.getCurrentCount());
        prdDstItemDTO.setIsWeight(kitchenItemDO.getIsWeight());
        prdDstItemDTO.setItemAttrMd5(kitchenItemDO.getItemAttrMd5());
        prdDstItemDTO.setItemGuid(kitchenItemDO.getItemGuid());
        prdDstItemDTO.setItemName(kitchenItemDO.getItemName());
        prdDstItemDTO.setItemRemark(kitchenItemDO.getItemRemark());
        prdDstItemDTO.setSkuGuid(kitchenItemDO.getSkuGuid());
        prdDstItemDTO.setSkuName(kitchenItemDO.getSkuName());
        prdDstItemDTO.setSkuUnit(kitchenItemDO.getSkuUnit());
        prdDstItemDTO.setOrderRemark(kitchenItemDO.getOrderRemark());
        prdDstItemDTO.setUrgedItemNumber(0);
        prdDstItemDTO.setHangedItemNumber(0);
        prdDstItemDTO.setDisplayType(kitchenItemDO.getDisplayType());
        prdDstItemDTO.setIsUrged(Objects.nonNull(kitchenItemDO.getUrgedTime()));
        prdDstItemDTO.setIsHanged(Objects.equals(KdsItemStateEnum.HANG_UP.getCode(), kitchenItemDO.getItemState()));
        prdDstItemDTO.setUrgedTime(kitchenItemDO.getUrgedTime());
        prdDstItemDTO.setCallUpTime(kitchenItemDO.getCallUpTime());
        prdDstItemDTO.setPrepareTime(kitchenItemDO.getPrepareTime());
        prdDstItemDTO.setHangUpTime(kitchenItemDO.getHangUpTime());
        prdDstItemDTO.setOrderGuid(kitchenItemDO.getOrderGuid());
        prdDstItemDTO.setOrderDesc(kitchenItemDO.getOrderDesc());
        prdDstItemDTO.setOrderNumber(kitchenItemDO.getOrderNumber());
        prdDstItemDTO.setOrderSerialNo(kitchenItemDO.getOrderSerialNo());
        prdDstItemDTO.setBatch(kitchenItemDO.getBatch());
        prdDstItemDTO.setSort(kitchenItemDO.getSort());

        PrdDstItemTableDTO prdDstItemTableDTO = getPrdDstItemTableDTO(kitchenItemDO);
        if (kitchenItemDO.getIsWeight()) {
            prdDstItemDTO.setWeightKitchenItem(prdDstItemTableDTO);
        } else {
            prdDstItemDTO.setKitchenItemList(Lists.newArrayList(prdDstItemTableDTO));
        }
        return prdDstItemDTO;
    }

    private PrdDstItemTableDTO getPrdDstItemTableDTO(KitchenItemDO kitchenItemDO) {
        PrdDstItemTableDTO prdDstItemTableDTO = new PrdDstItemTableDTO();
        prdDstItemTableDTO.setDisplayType(kitchenItemDO.getDisplayType());
        prdDstItemTableDTO.setOrderGuid(kitchenItemDO.getOrderGuid());
        prdDstItemTableDTO.setOrderDesc(kitchenItemDO.getOrderDesc());
        prdDstItemTableDTO.setOrderNumber(kitchenItemDO.getOrderNumber());
        prdDstItemTableDTO.setOrderSerialNo(kitchenItemDO.getOrderSerialNo());
        prdDstItemTableDTO.setKitchenItemGuid(kitchenItemDO.getGuid());
        prdDstItemTableDTO.setAreaGuid("snack_area_guid");
        prdDstItemTableDTO.setIsUrged(Objects.nonNull(kitchenItemDO.getUrgedTime()));
        prdDstItemTableDTO.setIsHanged(Objects.equals(KdsItemStateEnum.HANG_UP.getCode(), kitchenItemDO.getItemState()));
        return prdDstItemTableDTO;
    }

}
