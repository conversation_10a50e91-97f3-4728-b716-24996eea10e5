package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.framework.base.dto.message.*;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.kds.service.KdsNotificationService;
import com.holderzone.saas.store.kds.service.rpc.MsgRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutorService;

@Slf4j
@Service
public class KdsNotificationServiceImpl implements KdsNotificationService {


    private final ExecutorService executorService;

    private final MsgRpcService msgRpcService;

    @Autowired
    public KdsNotificationServiceImpl(ExecutorService executorService, MsgRpcService msgRpcService) {
        this.executorService = executorService;
        this.msgRpcService = msgRpcService;
    }

    @Override
    public void sendMessage(String enterpriseGuid, String storeGuid, String deviceId,String topic, String message) {
        executorService.execute(() -> {
            PushMessageDTO pushMessageDTO = new PushMessageDTO();
            pushMessageDTO.setTopicType(TopicType.BUSINESS);
            BusinessMessage businessMessage = new BusinessMessage();
            businessMessage.setEnterpriseGuid(enterpriseGuid);
            businessMessage.setStoreGuid(storeGuid);
            businessMessage.setBusinessType(getTopic(topic,deviceId));
            pushMessageDTO.setBusinessMessage(businessMessage);
            pushMessageDTO.setData(message);
            MessageDTO messageDTO = new MessageDTO();
            messageDTO.setMessageType(MessageType.PUSH);
            messageDTO.setPushMessage(pushMessageDTO);
            msgRpcService.sendPrintMessage(messageDTO);
            log.info("消息推送完毕，deviceId：{}，voiceMsg：{}", deviceId, message);
        });
    }

    private String getTopic(String topic ,String deviceId) {
        if (!StringUtils.hasText(deviceId)) {
            return topic;
        }
        return topic + ":" + deviceId;
    }
}
