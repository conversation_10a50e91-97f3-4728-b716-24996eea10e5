package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleQueryDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleSaveOrUpdateDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayRuleRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DisplayRuleDO;

/**
 * <AUTHOR>
 * @description 显示规则表
 * @date 2021/1/29 14:39
 */
public interface DisplayRuleService extends IService<DisplayRuleDO> {

    /**
     * 保存规则
     *
     * @param reqDTO DisplayRuleReqDTO
     * @return Boolean
     */
    Boolean saveRule(DisplayRuleSaveOrUpdateDTO reqDTO);

    /**
     * 显示批次列表
     *
     * @param reqDTO 当前页数,每页显示条数，必传规则类型 0显示批次 1菜品汇总
     * @return Page<DisplayRuleRespDTO>
     */
    Page<DisplayRuleRespDTO> batchList(DisplayRuleQueryDTO reqDTO);

    /**
     * 根据ruleGuid删除单个规则
     *
     * @param reqDTO DisplayRuleReqDTO
     * @return Boolean
     */
    Boolean deleteRule(DisplayRuleQueryDTO reqDTO);

    /**
     * 根据ruleGuid查询单个规则信息
     *
     * @param reqDTO DisplayRuleReqDTO
     * @return DisplayRuleRespDTO
     */
    DisplayRuleRespDTO getRule(DisplayRuleQueryDTO reqDTO);

    /**
     * 更新规则
     *
     * @param reqDTO DisplayRuleReqDTO
     * @return Boolean
     */
    Boolean updateRule(DisplayRuleSaveOrUpdateDTO reqDTO);

    Boolean queryHasAllStore(DisplayRuleQueryDTO reqDTO);
}

