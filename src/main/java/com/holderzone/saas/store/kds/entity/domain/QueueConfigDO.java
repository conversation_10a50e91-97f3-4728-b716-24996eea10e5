package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Queue;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "hsk_queue_config")
public class QueueConfigDO {

    /**
     * 主键ID
     */
     @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一GUID
     */
    @TableField(value = "guid")
    private String guid;

    /**
     * 门店GUID
     */
    @TableField(value = "store_guid")
    private String storeGuid;

    /**
     * 队列来源：2=掌控者点餐系统（默认），1=手动输入
     */
    @TableField(value = "source")
    private Integer source;

    /**
     * 队列数据包括：4=掌控者点餐设备（默认），2=外卖，1=移动点餐
     */
    @TableField(value = "content")
    private Integer content;

    /**
     * 队列信息：2=等餐队列，1=取餐队列，3（默认）
     */
    @TableField(value = "layout")
    private Integer layout;

    /**
     * 取餐呼叫：2=KDS出堂（默认），1=手动输入
     */
    @TableField(value = "call_mode")
    private Integer callMode;

    /**
     * 取餐确认：2=自动确认（默认），1=手动输入
     */
    @TableField(value = "confirm_mode")
    private Integer confirmMode;

    /**
     * 确认等待时间：
     * 4=30秒（默认）
     * 5=1分钟
     * 6=2分钟
     * 7=3分钟
     * 8=4分钟
     * 9=5分钟
     */
    @TableField(value = "confirm_ttl_level")
    private Integer confirmTtlLevel;

    /**
     * 叫号语音：当前只支持KDS播放取餐呼叫语音，不支持切换到电视等其他设备
     * true（默认）
     */
    @TableField(value = "is_call_up_enable")
    private Boolean isCallUpEnable;

    /**
     * 声音模板
     */
    @TableField(value = "voice_template")
    private String voiceTemplate;

    /**
     * 播放次数：1次，2次，3次（默认）
     */
    @TableField(value = "play_times")
    private Integer playTimes;

    /**
     * 播放间隔：1秒，3秒，5秒（默认），10秒
     */
    @TableField(value = "play_interval")
    private Integer playInterval;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private LocalDateTime gmtModified;

    public static final String COL_GUID = "guid";

    public static final String COL_SOURCE = "source";

    public static final String COL_CONTENT = "content";

    public static final String COL_LAYOUT = "layout";

    public static final String COL_CALL_MODE = "call_mode";

    public static final String COL_CONFIRM_MODE = "confirm_mode";

    public static final String COL_CONFIRM_TTL = "confirm_ttl";

    public static final String COL_IS_CALL_UP_ENABLE = "is_call_up_enable";

    public static final String COL_VOICE_TEMPLATE = "voice_template";

    public static final String COL_PLAY_TIMES = "play_times";

    public static final String COL_PLAY_INTERVAL = "play_interval";

    public static final String COL_GMT_CREATE = "gmt_create";

    public static final String COL_GMT_MODIFIED = "gmt_modified";

    public static QueueConfigDO defaultConfig(String storeGuid, String deviceGuid) {
        QueueConfigDO queueConfigDO = new QueueConfigDO();
        queueConfigDO.setGuid(deviceGuid);
        queueConfigDO.setStoreGuid(storeGuid);
        queueConfigDO.setSource(2);
        queueConfigDO.setContent(4);
        queueConfigDO.setLayout(3);
        queueConfigDO.setCallMode(2);
        queueConfigDO.setConfirmMode(2);
        queueConfigDO.setConfirmTtlLevel(4);
        queueConfigDO.setIsCallUpEnable(true);
        queueConfigDO.setVoiceTemplate(null);
        queueConfigDO.setPlayTimes(3);
        queueConfigDO.setPlayInterval(5);
        return queueConfigDO;
    }
}