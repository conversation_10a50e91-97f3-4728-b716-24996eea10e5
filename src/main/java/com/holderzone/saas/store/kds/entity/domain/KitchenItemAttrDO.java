package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hsk_kitchen_item_attr")
public class KitchenItemAttrDO extends BaseDO {

    /**
     * 订单商品Guid
     */
    private String orderItemGuid;

    /**
     * 属性组Guid
     */
    private String groupGuid;

    /**
     * 属性组名称
     */
    private String groupName;

    /**
     * 属性Guid
     */
    private String attrGuid;

    /**
     * 属性名称
     */
    private String attrName;

    /**
     * 属性数量
     */
    private Integer attrNumber;
}
