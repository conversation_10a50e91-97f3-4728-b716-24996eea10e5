package com.holderzone.saas.store.kds.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleItemSortDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleItemSortQueryDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleQueryDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleSaveOrUpdateDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayRuleRespDTO;
import com.holderzone.saas.store.kds.service.DisplayRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @description 显示规则接口
 * @date 2021/1/27 16:46
 */
@Slf4j
@AllArgsConstructor
@RestController
@Api(value = "显示规则接口")
@RequestMapping("/display_rule")
public class DisplayRuleController {

    private final DisplayRuleService displayRuleService;

    /**
     * 保存规则
     *
     * @param reqDTO DisplayRuleReqDTO
     * @return Boolean
     */
    @ApiOperation("保存规则")
    @PostMapping("/save_rule")
    public Boolean saveRule(@RequestBody DisplayRuleSaveOrUpdateDTO reqDTO) {
        log.info("保存规则入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return displayRuleService.saveRule(reqDTO);
    }

    /**
     * 显示批次列表
     *
     * @param reqDTO 当前页数,每页显示条数，必传规则类型 0显示批次 1菜品汇总,品牌guid
     * @return Page<DisplayRuleRespDTO>
     */
    @ApiOperation(value = "显示批次列表", notes = "可传当前页数,每页显示条数，必传规则类型 0显示批次 1菜品汇总")
    @PostMapping("/rule_list")
    public Page<DisplayRuleRespDTO> batchList(@RequestBody DisplayRuleQueryDTO reqDTO) {
        log.info("显示批次列表入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return displayRuleService.batchList(reqDTO);
    }

    /**
     * 根据ruleGuid删除单个规则
     *
     * @param reqDTO DisplayRuleReqDTO
     * @return Boolean
     */
    @ApiOperation("删除规则")
    @PostMapping("/delete_rule")
    public Boolean deleteRule(@RequestBody DisplayRuleQueryDTO reqDTO) {
        log.info("删除规则入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return displayRuleService.deleteRule(reqDTO);
    }

    /**
     * 根据ruleGuid查询单个规则信息
     *
     * @param reqDTO DisplayRuleReqDTO
     * @return DisplayRuleRespDTO
     */
    @ApiOperation("查询规则")
    @PostMapping("/get_rule")
    public DisplayRuleRespDTO getRule(@RequestBody DisplayRuleQueryDTO reqDTO) {
        log.info("查询规则入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return displayRuleService.getRule(reqDTO);
    }

    /**
     * 更新规则
     *
     * @param reqDTO DisplayRuleReqDTO
     * @return Boolean
     */
    @ApiOperation("更新规则")
    @PostMapping("/update_rule")
    public Boolean updateRule(@RequestBody DisplayRuleSaveOrUpdateDTO reqDTO) {
        log.info("更新规则入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return displayRuleService.updateRule(reqDTO);
    }

    /**
     * 查询有无选择全部门店 true:有选择 false：没有
     *
     * @param reqDTO DisplayRuleQueryDTO
     * @return Boolean
     */
    @ApiOperation("查询有无选择全部门店 true:有选择 false：没有")
    @PostMapping("/query_has_all_store")
    public Boolean queryHasAllStore(@RequestBody DisplayRuleQueryDTO reqDTO) {
        log.info("查询有无选择全部门店入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return displayRuleService.queryHasAllStore(reqDTO);
    }

}
