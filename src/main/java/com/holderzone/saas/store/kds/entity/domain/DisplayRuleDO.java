package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @description 显示规则表
 * @date 2021/1/27 16:51
 */
@Data
@Accessors(chain = true)
@TableName("hsk_display_rule")
public class DisplayRuleDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId
    private Long id;
    /**
     * 全局唯一主键
     */
    private String guid;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
    /**
     * 是否删除
     */
    private Integer isDelete;
    /**
     * 显示状态
     */
    private Integer displayState;
    /**
     * 延迟时间
     */
    private Integer delayTime;
    /**
     * 批次
     */
    private Integer batch;
    /**
     * 生效状态 生效状态 0延迟生效 1立即生效
     */
    private Integer effectiveState;
    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;
    /**
     * 是否全部门店
     */
    private Boolean isAllStore;
    /**
     * 规则类型 0显示批次 1菜品汇总
     */
    private Integer ruleType;
    /**
     * 品牌guid
     */
    private String brandGuid;
}
