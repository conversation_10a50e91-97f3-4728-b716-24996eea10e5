package com.holderzone.saas.store.kds.aop;

import com.holderzone.saas.store.kds.constant.PointModeConstants;
import com.holderzone.saas.store.kds.entity.enums.PointModeEnum;

import java.lang.annotation.*;

@Documented
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireNotification {

    boolean assertResult() default false;
    int pointMode() default PointModeConstants.ALL;
}
