package com.holderzone.saas.store.kds.service.template;

import com.holderzone.saas.store.dto.kds.req.KdsPrintDTO;
import com.holderzone.saas.store.kds.service.print.KdsPrintTemplate;

public abstract class AbsPrintTemplate<T extends KdsPrintDTO> implements KdsPrintTemplate<T> {

    private int pageSize;

    private T printDTO;

    @Override
    public int getPageSize() {
        return pageSize;
    }

    @Override
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public void setPrintDTO(T printDTO) {
        this.printDTO = printDTO;
    }

    @Override
    public T getPrintDTO() {
        return printDTO;
    }
}