package com.holderzone.saas.store.kds.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.kds.req.DstItemQueryReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemGroupReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdPointItemQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.DstTypeBindRespDTO;
import com.holderzone.saas.store.dto.kds.resp.ItemGroupRespDTO;
import com.holderzone.saas.store.dto.kds.resp.PointTypeBindRespDTO;
import com.holderzone.saas.store.kds.builder.ItemGroupBuilder;
import com.holderzone.saas.store.kds.manager.DeviceBindItemGroupManager;
import com.holderzone.saas.store.kds.manager.bo.ItemGroupBO;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * KDS菜品绑定分组
 */
@Slf4j
@RestController
@RequestMapping("/device_bind_item_group")
@AllArgsConstructor
public class DeviceBindItemGroupController {

    private final DeviceBindItemGroupManager deviceBindItemGroupManager;

    @ApiOperation(value = "菜品分组列表")
    @PostMapping("/group/list")
    public List<ItemGroupRespDTO> queryGroupList(@RequestBody PrdPointItemQueryReqDTO queryReqDTO) {
        return deviceBindItemGroupManager.queryGroupList(queryReqDTO);
    }

    @ApiOperation(value = "菜品分组列表(当前设备已绑定)")
    @PostMapping("/group/myself/list")
    public List<ItemGroupRespDTO> queryMyselfGroupList(@RequestBody PrdPointItemQueryReqDTO queryReqDTO) {
        return deviceBindItemGroupManager.queryMySelfGroupList(queryReqDTO);
    }

    @ApiOperation(value = "新增菜品分组")
    @PostMapping("/group/save")
    public void saveGroup(@RequestBody @Validated ItemGroupReqDTO reqDTO) {
        log.info("新增菜品分组入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        ItemGroupBO biz = ItemGroupBuilder.build(reqDTO);
        deviceBindItemGroupManager.saveGroup(biz);
    }

    @ApiOperation(value = "编辑菜品分组(菜品新增)")
    @PostMapping("/group/update")
    public void updateGroup(@RequestBody ItemGroupReqDTO reqDTO) {
        log.info("编辑菜品分组入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        ItemGroupBO biz = ItemGroupBuilder.build(reqDTO);
        deviceBindItemGroupManager.updateGroup(biz);
    }

    @ApiOperation(value = "删除菜品分组")
    @DeleteMapping("/group/remove/{groupGuid}")
    public void removeGroup(@PathVariable("groupGuid") String groupGuid) {
        log.info("删除菜品分组入参：{}", groupGuid);
        deviceBindItemGroupManager.removeGroup(groupGuid);
    }

    @ApiOperation(value = "删除菜品分组下绑定商品")
    @DeleteMapping("/group/remove/{groupGuid}/{skuGuid}")
    public void removeGroupItem(@PathVariable("groupGuid") String groupGuid,
                                @PathVariable("skuGuid") String skuGuid) {
        log.info("删除菜品分组下绑定商品入参：groupGuid:{}, skuGuid:{}", groupGuid, skuGuid);
        deviceBindItemGroupManager.removeGroupItem(groupGuid, skuGuid);
    }

    @ApiOperation(value = "查询KDS制作口绑定商品列表")
    @PostMapping("/prd/item/list")
    public List<PointTypeBindRespDTO> queryPrdBindItem(@RequestBody PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        return deviceBindItemGroupManager.queryPrdBindItem(prdPointItemQueryReqDTO);
    }

    @ApiOperation(value = "查询KDS出堂口绑定商品列表")
    @PostMapping("/dst/item/list")
    public List<DstTypeBindRespDTO> queryDstBindItem(@RequestBody DstItemQueryReqDTO dstItemQueryReqDTO) {
        return deviceBindItemGroupManager.queryDstBindItem(dstItemQueryReqDTO);
    }

}
