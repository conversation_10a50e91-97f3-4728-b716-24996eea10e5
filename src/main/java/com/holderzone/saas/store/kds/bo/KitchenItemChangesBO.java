package com.holderzone.saas.store.kds.bo;

import com.holderzone.saas.store.dto.kds.req.KdsChangesItemDTO;
import com.holderzone.saas.store.kds.entity.bo.ChangeKitchenItemBO;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class KitchenItemChangesBO implements Serializable {

    private static final long serialVersionUID = 3230292162403526943L;

    private KdsChangesItemDTO kdsChangesItemDTO;

    private List<KitchenItemDO> changeKitchenItemDOList;

    private List<KitchenItemDO> needChangeKitchenItemDOList;

    private List<KitchenItemAttrDO> kitchenItemAttrList;

    /**
     * 设备ID -> 设备
     */
    private Map<String, DeviceConfigDO> deviceConfigInSqlMap;

    private ChangeKitchenItemBO changeKitchenItemBO;
}
