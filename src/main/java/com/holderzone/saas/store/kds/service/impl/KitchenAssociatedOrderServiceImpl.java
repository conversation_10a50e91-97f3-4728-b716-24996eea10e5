package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.kds.entity.domain.*;
import com.holderzone.saas.store.kds.mapper.KitchenAssociatedOrderMapper;
import com.holderzone.saas.store.kds.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 厨房商品联台单服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KitchenAssociatedOrderServiceImpl extends ServiceImpl<KitchenAssociatedOrderMapper, KitchenAssociatedOrderDO>
        implements KitchenAssociatedOrderService {

    @Override
    public void create(KitchenAssociatedOrderDO kitchenAssociatedOrderDO) {
        baseMapper.create(kitchenAssociatedOrderDO);
    }

    @Override
    public KitchenAssociatedOrderDO getByOrderGuid(String orderGuid) {
        LambdaQueryWrapper<KitchenAssociatedOrderDO> qw = new LambdaQueryWrapper<KitchenAssociatedOrderDO>()
                .eq(KitchenAssociatedOrderDO::getOrderGuid, orderGuid);
        return getOne(qw);
    }

    @Override
    public List<KitchenAssociatedOrderDO> listByOrderGuids(List<String> orderGuids) {
        LambdaQueryWrapper<KitchenAssociatedOrderDO> qw = new LambdaQueryWrapper<KitchenAssociatedOrderDO>()
                .in(KitchenAssociatedOrderDO::getOrderGuid, orderGuids);
        return list(qw);
    }
}
