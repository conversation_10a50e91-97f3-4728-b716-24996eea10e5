package com.holderzone.saas.store.kds.service.print;


import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.kds.req.ItemStateTransReqDTO;
import com.holderzone.saas.store.dto.kds.req.KdsChangesItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.kds.entity.domain.KdsPrintRecordDO;
import com.holderzone.saas.store.dto.kds.resp.KdsPrintOrderDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsPrintRecordDTO;
import com.holderzone.saas.store.dto.kds.req.KdsPrintRecordReqDTO;

import java.util.List;

public interface KdsPrintRecordService extends IService<KdsPrintRecordDO> {

    void printSingleItem(String deviceId, PrdDstItemDTO prdDstItemDTO, KdsInvoiceTypeEnum kdsInvoiceTypeEnum);

    void autoPrintPrdItem(String deviceId, List<PrdDstItemDTO> prdDstItemDTOS, boolean isPrintPerOrder);

    void manualPrintPrdItem(ItemStateTransReqDTO itemStateTransReqDTO, boolean isPrintPerOrder);

    void manualPrintDstItem(ItemStateTransReqDTO itemStateTransReqDTO);

    /**
     * 打印换菜单
     */
    void printChangesItem(KdsChangesItemDTO kdsChangesItemDTO);

    List<KdsPrintOrderDTO> getPrintOrder(KdsPrintRecordReqDTO kdsPrintRecordReqDTO);

    List<KdsPrintRecordDTO> listRecord(KdsPrintRecordReqDTO kdsPrintRecordReqDTO);

    void updatePrintResult(KdsPrintRecordReqDTO kdsPrintRecordReqDTO);

    void deleteRecord(KdsPrintRecordReqDTO kdsPrintRecordReqDTO);

    void batchDeleteRecord(List<String> arrayOfRecordGuid);
}
