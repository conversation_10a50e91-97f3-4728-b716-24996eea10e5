package com.holderzone.saas.store.kds.aop;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.kds.constant.PointModeConstants;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.entity.enums.PointModeEnum;
import com.holderzone.saas.store.kds.service.DeviceConfigService;
import com.holderzone.saas.store.kds.service.KdsStatusPushService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Aspect
@Component
@AllArgsConstructor
public class StatusNotifyAspect {

    private final KdsStatusPushService kdsStatusPushService;
    private final DeviceConfigService deviceConfigService;


    @AfterReturning(value = "@annotation(requireNotification)", returning = "result" )
    public void notifyAllDevices(JoinPoint joinPoint, RequireNotification requireNotification, Object result) {
        if (requireNotification.assertResult() && !Boolean.TRUE.equals(result)) {
            return;
        }
        UserContext userContext = UserContextUtils.get();

        if(requireNotification.pointMode()== PointModeConstants.ALL){
            kdsStatusPushService.statusChanged(userContext.getEnterpriseGuid(), userContext.getStoreGuid(), null, "");
            return;
        }

        List<DeviceConfigDO> deviceConfigList = deviceConfigService.listDeviceOfStore(userContext.getStoreGuid());
        deviceConfigList.stream().
                filter(s->s.getPointMode()==requireNotification.pointMode()).
                forEach(t-> kdsStatusPushService.statusChanged(userContext.getEnterpriseGuid(), userContext.getStoreGuid(), t.getGuid(), ""));
    }
}
