package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.kds.constant.RocketMqConfig;
import com.holderzone.saas.store.kds.entity.dto.KitchenItemDelayNotificationDTO;
import com.holderzone.saas.store.kds.service.KdsStatusPushService;
import com.holderzone.saas.store.kds.service.KitchenItemDelayNotificationService;
import com.holderzone.saas.store.kds.service.KitchenItemService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class KitchenItemDelayNotificationServiceImpl implements KitchenItemDelayNotificationService {

    private final DefaultRocketMqProducer rocketMqProducer;

    @Override
    public void sendMessage(KitchenItemDelayNotificationDTO dto) {
        int delayTimeLeft = dto.getDelayTimeLeft()>10 ? 10 : dto.getDelayTimeLeft();
        dto.setDelayTimeLeft(dto.getDelayTimeLeft()-delayTimeLeft);
        Message message = new Message(
                RocketMqConfig.KDS_MESSAGE_TOPIC,
                RocketMqConfig.KDS_DELAY_DISPLAY_TAG,
                JacksonUtils.toJsonByte(dto)
        );
        message.getProperties().put("userInfo", UserContextUtils.getJsonStr());
        // messageDelayLevel=1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 15m
        message.setDelayTimeLevel(delayTimeLeft+4);
        rocketMqProducer.sendMessage(message);
    }


}
