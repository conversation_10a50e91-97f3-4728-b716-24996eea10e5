package com.holderzone.saas.store.kds.entity.enums;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradeModeEnum
 * @date 2018/09/04 17:52
 * @description 订单交易模式枚举
 * @program holder-saas-store-trade
 */
public enum KdsKitchenStateEnum {

    NULL(-1,  "不明确"),

    TO_PRD(4,  "待制作"),

    COOKING(5, "制作中"),

    TO_DST(6,  "待出堂"),

    FINISHED(7,  "已出堂"),

    ;

    private int code;

    private String desc;

    KdsKitchenStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static KdsKitchenStateEnum ofCode(int code) {
        for (KdsKitchenStateEnum kdsTradeModeEnum : values()) {
            if (kdsTradeModeEnum.code == code) {
                return kdsTradeModeEnum;
            }
        }
        throw new BusinessException("不支持的厨房商品状态：" + code);
    }

    public static String getDescByCode(int code) {
        return ofCode(code).getDesc();
    }
}
