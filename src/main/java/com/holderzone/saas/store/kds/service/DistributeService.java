package com.holderzone.saas.store.kds.service;

import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.DstAreaRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DstBindDetailsRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DstBindStatusRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DstTypeBindRespDTO;

import java.util.List;

public interface DistributeService {

    DstAreaRespDTO queryBoundArea(DstBoundAreaReqDTO dstBoundAreaReqDTO);

    void bindArea(DstBindAreaReqDTO dstBindAreaReqDTO);

    void bindItem(DstBindItemReqDTO dstBindAreaReqDTO);

    void unbindItem(DstBindItemReqDTO dstBindAreaReqDTO);

    void reInitialize(String storeGuid, String deviceId);

    List<DstTypeBindRespDTO> queryBoundItemOfDevice(DstItemQueryReqDTO dstItemQueryReqDTO);

    /**
     * 查询所有出堂口绑定商品
     *
     * @param dstItemQueryReqDTO dstItemQueryReqDTO
     * @return List<DstTypeBindRespDTO>
     */
    List<DstTypeBindRespDTO> queryAllItemOfStore(DstItemQueryReqDTO dstItemQueryReqDTO);

    DstBindStatusRespDTO queryBindingPreview(DeviceQueryReqDTO deviceQueryReqDTO);

    DstBindDetailsRespDTO queryBindingDetails(DstItemQueryReqDTO dstItemQueryReqDTO);
}
