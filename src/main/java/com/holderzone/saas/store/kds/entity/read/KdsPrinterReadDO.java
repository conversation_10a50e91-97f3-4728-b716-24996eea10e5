package com.holderzone.saas.store.kds.entity.read;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class KdsPrinterReadDO implements Serializable {

    private static final long serialVersionUID = 799133805086548343L;

    /**
     * 自增id
     */
    private Long id;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 设备编号
     */
    private String deviceId;

    /**
     * 打印机guid
     */
    private String printerGuid;

    /**
     * 打印机名称
     */
    private String printerName;

    /**
     * 打印机ip
     */
    private String printerIp;

    /**
     * 打印端口
     */
    private Integer printerPort;

    /**
     * 打印纸张类型; 参数: 58, 80
     */
    private Integer pageSize;

    /**
     * 是否为主机
     */
    private Boolean isMaster;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 数据更新时间
     */
    private LocalDateTime gmtModified;
}