package com.holderzone.saas.store.kds.entity.read;

import com.holderzone.saas.store.kds.entity.domain.BaseDO;
import com.holderzone.saas.store.kds.entity.domain.ProductionPointDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GroupBindItemReadDO extends BaseDO {

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 设备Guid
     */
    private String deviceId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 商品列表数量
     */
    private Integer itemCount;

    /**
     * 商品列表
     */
    private List<KitchenItemReadDO> items;

    public static GroupBindItemReadDO of(ProductionPointDO productionPointDO) {
        GroupBindItemReadDO pointItemReadDO = new GroupBindItemReadDO();
        pointItemReadDO.setStoreGuid(productionPointDO.getStoreGuid());
        pointItemReadDO.setDeviceId(productionPointDO.getDeviceId());
        pointItemReadDO.setGroupName(productionPointDO.getName());
        pointItemReadDO.setItemCount(0);
        pointItemReadDO.setItems(Collections.emptyList());
        pointItemReadDO.setId(productionPointDO.getId());
        pointItemReadDO.setGuid(productionPointDO.getGuid());
        pointItemReadDO.setGmtCreate(productionPointDO.getGmtCreate());
        pointItemReadDO.setGmtModified(productionPointDO.getGmtModified());
        return pointItemReadDO;
    }
}
