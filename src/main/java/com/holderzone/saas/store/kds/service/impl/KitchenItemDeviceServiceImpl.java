package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.saas.store.kds.entity.domain.*;
import com.holderzone.saas.store.kds.mapper.KitchenItemDeviceMapper;
import com.holderzone.saas.store.kds.service.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@AllArgsConstructor
public class KitchenItemDeviceServiceImpl extends ServiceImpl<KitchenItemDeviceMapper, KitchenItemDeviceDO> implements KitchenItemDeviceService {

    @Override
    public List<KitchenItemDeviceDO> listDeviceIdByKitchenItemGuids(List<String> kitchenItemGuids) {
        if (CollectionUtils.isEmpty(kitchenItemGuids)) {
            return Lists.newArrayList();
        }
        QueryWrapper<KitchenItemDeviceDO> qw = new QueryWrapper<>();
        qw.lambda().in(KitchenItemDeviceDO::getKitchenItemGuid, kitchenItemGuids);
        qw.lambda().select(KitchenItemDeviceDO::getDeviceId);
        return list(qw);
    }

    @Override
    public List<KitchenItemDeviceDO> listByDeviceIds(List<String> deviceIds) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return Lists.newArrayList();
        }
        QueryWrapper<KitchenItemDeviceDO> qw = new QueryWrapper<>();
        qw.lambda().in(KitchenItemDeviceDO::getDeviceId, deviceIds);
        return list(qw);
    }

    @Override
    public List<KitchenItemDeviceDO> limitListByDeviceId(String deviceId) {
        return baseMapper.limitListByDeviceId(deviceId);
    }

    @Override
    public void updateBatchKitchenItemGuid(List<KitchenItemDeviceDO> kitchenItemDeviceDOList) {
        if (CollectionUtils.isEmpty(kitchenItemDeviceDOList)) {
            return;
        }
        baseMapper.updateBatchKitchenItemGuid(kitchenItemDeviceDOList);
    }

    @Override
    public void removeByKitchenItemGuid(String kitchenItemGuid) {
        UpdateWrapper<KitchenItemDeviceDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(KitchenItemDeviceDO::getKitchenItemGuid, kitchenItemGuid);
        remove(uw);
    }

    @Override
    public void removeBatchByKitchenItemGuids(List<String> kitchenItemGuids) {
        if (CollectionUtils.isEmpty(kitchenItemGuids)) {
            return;
        }
        UpdateWrapper<KitchenItemDeviceDO> uw = new UpdateWrapper<>();
        uw.lambda().in(KitchenItemDeviceDO::getKitchenItemGuid, kitchenItemGuids);
        remove(uw);
    }
}
