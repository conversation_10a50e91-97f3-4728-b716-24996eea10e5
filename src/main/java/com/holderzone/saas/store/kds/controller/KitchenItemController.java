package com.holderzone.saas.store.kds.controller;

import com.holderzone.framework.slf4j.starter.anno.LogAfter;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.KitchenItemDTO;
import com.holderzone.saas.store.dto.kds.resp.KitchenItemRespDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstRespDTO;
import com.holderzone.saas.store.kds.service.KitchenItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Api("厨房菜品接口")
@RequestMapping("/kitchen_item")
public class KitchenItemController {

    private final KitchenItemService kitchenItemService;

    @Autowired
    public KitchenItemController(KitchenItemService kitchenItemService) {
        this.kitchenItemService = kitchenItemService;
    }

    @PostMapping("/prepare")
    @ApiOperation(value = "菜品入厨房")
    public void prepare(@RequestBody @Validated ItemPrepareReqDTO itemPrepareReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("菜品入厨房入参:{}", JacksonUtils.writeValueAsString(itemPrepareReqDTO));
        }
        kitchenItemService.prepare(itemPrepareReqDTO);
    }

    @PostMapping("/prd_query")
    @ApiOperation(value = "查询制作点菜品状态")
    @LogAfter(value = "查询制作点菜品状态", logLevel = LogLevel.INFO)
    public PrdDstRespDTO prdQuery(@RequestBody @Validated PrdItemStatusReqDTO prdItemStatusReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询制作点菜品状态入参:{}", JacksonUtils.writeValueAsString(prdItemStatusReqDTO));
        }
        return kitchenItemService.queryPrdStatus(prdItemStatusReqDTO);
    }

    @PostMapping("/dst_query")
    @ApiOperation(value = "查询出堂口菜品状态")
    @LogAfter(value = "查询出堂口菜品状态", logLevel = LogLevel.INFO)
    public PrdDstRespDTO dstQuery(@RequestBody @Validated DstItemStatusReqDTO dstItemStatusReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询出堂口菜品状态入参:{}", JacksonUtils.writeValueAsString(dstItemStatusReqDTO));
        }
        return kitchenItemService.queryDstStatus(dstItemStatusReqDTO);
    }

    @PostMapping("/call")
    @ApiOperation(value = "叫起")
    public void call(@RequestBody @Validated ItemCallUpReqDTO itemCallUpReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("叫起入参:{}", JacksonUtils.writeValueAsString(itemCallUpReqDTO));
        }
        kitchenItemService.call(itemCallUpReqDTO);
    }

    @PostMapping("/urge")
    @ApiOperation(value = "催菜")
    public void urge(@RequestBody @Validated ItemUrgeReqDTO itemUrgeReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("催菜入参:{}", JacksonUtils.writeValueAsString(itemUrgeReqDTO));
        }
        kitchenItemService.urge(itemUrgeReqDTO);
    }

    @PostMapping("/remark")
    @ApiOperation(value = "修改备注")
    public void remark(@RequestBody @Validated OrderRemarkReqDTO orderRemarkReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("修改备注入参:{}", JacksonUtils.writeValueAsString(orderRemarkReqDTO));
        }
        kitchenItemService.remark(orderRemarkReqDTO);
    }

    @PostMapping("/change_table")
    @ApiOperation(value = "换台")
    public void changeTable(@RequestBody @Validated OrderTableReqDTO orderTableReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("换台入参:{}", JacksonUtils.writeValueAsString(orderTableReqDTO));
        }
        kitchenItemService.changeTable(orderTableReqDTO);
    }

    @PostMapping("/refund")
    @ApiOperation(value = "退菜")
    public void refund(@RequestBody @Validated ItemBatchRefundReqDTO itemBatchRefundReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("退菜入参:{}", JacksonUtils.writeValueAsString(itemBatchRefundReqDTO));
        }
        kitchenItemService.refund(itemBatchRefundReqDTO);
    }

    @PostMapping("/cooking")
    @ApiOperation(value = "制作菜品")
    public void cooking(@RequestBody @Validated ItemStateTransReqDTO itemStateTransReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("制作菜品入参:{}", JacksonUtils.writeValueAsString(itemStateTransReqDTO));
        }
        kitchenItemService.cooking(itemStateTransReqDTO);
    }

    @PostMapping("/complete")
    @ApiOperation(value = "完成菜品，参数要求同制作菜品")
    public void complete(@RequestBody @Validated ItemStateTransReqDTO itemStateTransReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("完成菜品入参:{}", JacksonUtils.writeValueAsString(itemStateTransReqDTO));
        }
        kitchenItemService.complete(itemStateTransReqDTO);
    }

    @PostMapping("/cancelComplete")
    @ApiOperation(value = "撤销菜品完成，参数要求同制作菜品")
    public void cancelComplete(@RequestBody @Validated ItemStateTransReqDTO itemStateTransReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("撤销菜品完成入参:{}", JacksonUtils.writeValueAsString(itemStateTransReqDTO));
        }
        kitchenItemService.cancelComplete(itemStateTransReqDTO);
    }

    @PostMapping("/distribute")
    @ApiOperation(value = "菜品出堂，参数要求同制作菜品")
    public List<PrdDstItemDTO> distribute(@RequestBody @Validated ItemStateTransReqDTO itemStateTransReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("菜品出堂入参:{}", JacksonUtils.writeValueAsString(itemStateTransReqDTO));
        }
        return kitchenItemService.distribute(itemStateTransReqDTO);
    }

    @PostMapping("/distribute_batch")
    @ApiOperation(value = "循环菜品出堂，参数要求同制作菜品")
    public void batchDistribute(@RequestBody @Validated List<ItemStateTransReqDTO> reqDTOList) {
        log.info("[循环菜品出堂],reqDTOList={}", JacksonUtils.writeValueAsString(reqDTOList));
        kitchenItemService.batchDistribute(reqDTOList);
    }

    @PostMapping("/prd_history")
    @ApiOperation(value = "查询制作点历史记录")
    public Page<KitchenItemDTO> prdHistory(@RequestBody @Validated PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询制作点历史记录:{}", JacksonUtils.writeValueAsString(prdDstItemHistoryReqDTO));
        }
        return kitchenItemService.prdHistory(prdDstItemHistoryReqDTO);
    }

    @PostMapping("/dst_history")
    @ApiOperation(value = "查询出堂口历史记录")
    public Page<KitchenItemDTO> dstHistory(@RequestBody @Validated PrdDstItemHistoryReqDTO itemStateTransReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询出堂口历史记录:{}", JacksonUtils.writeValueAsString(itemStateTransReqDTO));
        }
        return kitchenItemService.dstHistory(itemStateTransReqDTO);
    }

    @PostMapping("/prd_print_again")
    @ApiOperation(value = "制作记录重新打印")
    public void prdPrintAgain(@RequestBody @Validated(KitchenItemDTO.Prd.class) KitchenItemDTO kitchenItemDTO) {
        if (log.isInfoEnabled()) {
            log.info("制作记录重新打印入参:{}", JacksonUtils.writeValueAsString(kitchenItemDTO));
        }
        kitchenItemService.prdPrintAgain(kitchenItemDTO);
    }

    @PostMapping("/dst_print_again")
    @ApiOperation(value = "出堂记录重新打印")
    public void dstPrintAgain(@RequestBody @Validated(KitchenItemDTO.Dst.class) KitchenItemDTO kitchenItemDTO) {
        if (log.isInfoEnabled()) {
            log.info("出堂记录重新打印入参:{}", JacksonUtils.writeValueAsString(kitchenItemDTO));
        }
        kitchenItemService.dstPrintAgain(kitchenItemDTO);
    }

    @PostMapping("/cancel_dst")
    @ApiOperation(value = "撤销出堂")
    public void cancelDistribute(@RequestBody @Validated ItemCancelDstReqDTO itemCancelDstReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("撤销出堂入参:{}", JacksonUtils.writeValueAsString(itemCancelDstReqDTO));
        }
        kitchenItemService.cancelDistribute(itemCancelDstReqDTO);
    }

    @PostMapping("/query_by_order_item")
    @ApiOperation(value = "根据订单商品查询kds")
    public List<KitchenItemRespDTO> queryByOrderItem(@RequestBody ItemBatchRefundReqDTO query) {
        log.info("根据订单商品查询kds:{}", JacksonUtils.writeValueAsString(query));
        return kitchenItemService.queryByOrderItem(query);
    }

    @PostMapping("/query_by_order")
    @ApiOperation(value = "根据订单查询kds未出堂商品")
    public List<PrdDstItemDTO> queryByOrder(@RequestBody PrdDstItemQueryDTO query) {
        log.info("[根据订单查询kds未出堂商品]:{}", JacksonUtils.writeValueAsString(query));
        return kitchenItemService.queryByOrder(query);
    }

}
