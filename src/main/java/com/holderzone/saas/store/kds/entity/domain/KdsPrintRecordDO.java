package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("hsk_print_record")
public class KdsPrintRecordDO extends BaseDO {

    /**
     * 打印UID
     */
    private String recordUid;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 生成该打印记录的设备ID
     */
    private String deviceId;

    /**
     * 打印类型代码
     */
    private Integer invoiceType;

    /**
     * 打印机guid
     */
    private String printerGuid;

    /**
     * 0/打印中;1/打印成功;2/打印失败;3/打印超时(失败的特殊情况)
     */
    private Integer printStatus;

    /**
     * 打印状态详情
     */
    private String printStatusMsg;

    /**
     * 打印内容，Json格式
     */
    private String printContent;

    /**
     * 创建该条记录的员工id
     */
    private String createStaffGuid;

    /**
     * 是否逻辑删除：0=未删除， 1=已删除
     */
    @TableLogic
    private Boolean isDeleted;
}
