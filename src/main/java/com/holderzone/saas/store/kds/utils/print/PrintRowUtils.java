package com.holderzone.saas.store.kds.utils.print;

import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.printable.*;
import com.holderzone.saas.store.enums.print.ContentTypeEnum;

import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintRowUtils
 * @date 2018/02/14 09:00
 * @description 打印ContentType辅助工具类
 * @program holder-saas-store-print
 */
public class PrintRowUtils {

    public static void add(Collection<PrintRow> printRows, BarCode barCode) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.BAR_CODE.getType()).setBarCode(barCode));
    }

    public static void add(Collection<PrintRow> printRows, BlankRow blankRow) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.BLANK_ROW.getType()).setBlankRow(blankRow));
    }

    public static void add(Collection<PrintRow> printRows, CoordinateRow coordinateRow) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.COORDINATE_ROW.getType()).setCoordinateRow(coordinateRow));
    }

    public static void add(Collection<PrintRow> printRows, Image image) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.IMAGE.getType()).setImage(image));
    }

    public static void add(Collection<PrintRow> printRows, KeyValue keyValue) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.KEY_VALUE.getType()).setKeyValue(keyValue));
    }

    public static void add(Collection<PrintRow> printRows, Line line) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.LINE.getType()).setLine(line));
    }

    public static void add(Collection<PrintRow> printRows, QrCode qrCode) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.QR_CODE.getType()).setQrCode(qrCode));
    }

    public static void add(Collection<PrintRow> printRows, ReverseText reverseText) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.REVERSE_TEXT.getType()).setReverseText(reverseText));
    }

    public static void add(Collection<PrintRow> printRows, Section section) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.SECTION.getType()).setSection(section));
    }

    public static void add(Collection<PrintRow> printRows, Separator separator) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.SEPARATOR.getType()).setSeparator(separator));
    }

    public static void add(Collection<PrintRow> printRows, Table table) {
        printRows.add(new PrintRow().setContentType(ContentTypeEnum.TABLE.getType()).setTable(table));
    }
}
