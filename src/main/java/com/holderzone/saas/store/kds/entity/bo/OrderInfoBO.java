package com.holderzone.saas.store.kds.entity.bo;

import com.google.common.collect.Sets;
import com.holderzone.saas.store.dto.kds.req.ItemPrepareReqDTO;
import com.holderzone.saas.store.dto.kds.req.TradeDineInInfoDTO;
import com.holderzone.saas.store.dto.kds.req.TradeSnackInfoDTO;
import com.holderzone.saas.store.dto.kds.req.TradeTakeoutInfoDTO;
import com.holderzone.saas.store.kds.entity.domain.DistributeAreaDO;
import com.holderzone.saas.store.kds.entity.domain.PrdPointItemDO;
import com.holderzone.saas.store.kds.entity.enums.KdsTradeModeEnum;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.MapUtils;
import org.springframework.data.util.Pair;

import java.util.*;
import java.util.stream.Collectors;

@Data
@Accessors(chain = true)
public class OrderInfoBO {

    private String orderDesc;

    private String orderNumber;

    private String orderSerialNo;

    private String areaGuid;

    private String tableGuid;

    private Integer displayType;

    private String orderGuid;

    private String orderRemark;

    /**
     * 原商品全称
     */
    private String originalItemSkuName;

    private Map<String, PrdPointItemDO> skuPointMap;

    private Map<String, List<PrdPointItemDO>> skuPointsMap;

    private Map<Pair<String, String>, String> skuArea2DstMap;

    private Map<Pair<String, String>, Map<String, List<String>>> skuArea2DstsMap;

    private Boolean isTakeOutOrder = false;

    public OrderInfoBO() {
    }

    public OrderInfoBO(ItemPrepareReqDTO itemPrepareReqDTO) {
        fillOrderInfo(itemPrepareReqDTO);
        fillAreaInfo(itemPrepareReqDTO);
        this.displayType = KdsTradeModeEnum.getDisplayTypeByCode(itemPrepareReqDTO.getTradeMode());
        this.orderGuid = itemPrepareReqDTO.getOrderGuid();
        this.orderRemark = itemPrepareReqDTO.getOrderRemark();
    }

    private void fillOrderInfo(ItemPrepareReqDTO itemPrepareReqDTO) {
        if (KdsTradeModeEnum.DINE_IN.getCode() == itemPrepareReqDTO.getTradeMode()) {
            TradeDineInInfoDTO tradeDineInInfoDTO =
                    Objects.requireNonNull(itemPrepareReqDTO.getTradeDineInInfoDTO(), "正餐信息不得为空");
            this.orderDesc = KdsTradeModeEnum.DINE_IN.getDesc();
            this.orderNumber = tradeDineInInfoDTO.getOrderNo();
            this.orderSerialNo = tradeDineInInfoDTO.getAreaName() + "-" + tradeDineInInfoDTO.getTableName();
            if (Boolean.TRUE.equals(tradeDineInInfoDTO.getAssociatedFlag())) {
                this.orderSerialNo = String.format("联-%s (共%s桌)", tradeDineInInfoDTO.getAssociatedSn(),
                        tradeDineInInfoDTO.getAssociatedTableNames().size());
            }
        } else if (KdsTradeModeEnum.SNACK.getCode() == itemPrepareReqDTO.getTradeMode()) {
            TradeSnackInfoDTO tradeSnackInfoDTO =
                    Objects.requireNonNull(itemPrepareReqDTO.getTradeSnackInfoDTO(), "快餐信息不得为空");
            this.orderDesc = KdsTradeModeEnum.SNACK.getDesc();
            this.orderNumber = tradeSnackInfoDTO.getOrderNo();
            this.orderSerialNo = tradeSnackInfoDTO.getMarkNo();
        } else {
            TradeTakeoutInfoDTO tradeTakeoutInfoDTO =
                    Objects.requireNonNull(itemPrepareReqDTO.getTradeTakeoutInfoDTO(), "外卖信息不得为空");
            this.orderDesc = tradeTakeoutInfoDTO.getPlatformName();
            this.orderNumber = tradeTakeoutInfoDTO.getPlatformNumber();
            this.orderSerialNo = tradeTakeoutInfoDTO.getPlatformSerialNo();
            isTakeOutOrder = true;
        }
    }

    private void fillAreaInfo(ItemPrepareReqDTO itemPrepareReqDTO) {
        if (KdsTradeModeEnum.DINE_IN.getCode() == itemPrepareReqDTO.getTradeMode()) {
            TradeDineInInfoDTO tradeDineInInfoDTO =
                    Objects.requireNonNull(itemPrepareReqDTO.getTradeDineInInfoDTO(), "正餐信息不得为空");
            this.areaGuid = tradeDineInInfoDTO.getAreaGuid();
            this.tableGuid = tradeDineInInfoDTO.getTableGuid();
        } else if (KdsTradeModeEnum.SNACK.getCode() == itemPrepareReqDTO.getTradeMode()) {
            this.areaGuid = DistributeAreaDO.SNACK_AREA_GUID;
            this.tableGuid = DistributeAreaDO.SNACK_TABLE_GUID;
        } else {
            this.areaGuid = DistributeAreaDO.TAKEOUT_AREA_GUID;
            this.tableGuid = DistributeAreaDO.TAKEOUT_TABLE_GUID;
        }
    }

    public List<String> getDeviceIds() {
        Set<String> allDeviceIds = Sets.newHashSet();
        if (MapUtils.isNotEmpty(this.skuPointMap)) {
            allDeviceIds.addAll(this.skuPointMap.values().stream()
                    .map(PrdPointItemDO::getDeviceId)
                    .collect(Collectors.toSet()));
        }
        if (MapUtils.isNotEmpty(this.skuPointsMap)) {
            allDeviceIds.addAll(this.skuPointsMap.values().stream()
                    .flatMap(Collection::stream)
                    .map(PrdPointItemDO::getDeviceId)
                    .collect(Collectors.toSet()));
        }
        if (MapUtils.isNotEmpty(this.skuArea2DstsMap)) {
            for (Map<String, List<String>> deviceIdMap : skuArea2DstsMap.values()) {
                allDeviceIds.addAll(deviceIdMap.values().stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList()));
            }
        }
        if (MapUtils.isNotEmpty(this.skuArea2DstMap)) {
            allDeviceIds.addAll(new HashSet<>(this.skuArea2DstMap.values()));
        }
        return allDeviceIds.stream().distinct().collect(Collectors.toList());
    }
}
