package com.holderzone.saas.store.kds.handler;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.kds.req.ItemChangesReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemPrepareReqDTO;
import com.holderzone.saas.store.kds.bo.ItemPrepareSplitBO;
import com.holderzone.saas.store.dto.kds.req.KdsItemDTO;
import com.holderzone.saas.store.enums.kds.KdsItemStateEnum;
import com.holderzone.saas.store.kds.bo.KitchenItemChangesBO;
import com.holderzone.saas.store.kds.entity.bo.ChangeKitchenItemBO;
import com.holderzone.saas.store.kds.entity.bo.OrderInfoBO;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemDO;
import com.holderzone.saas.store.kds.entity.domain.PrdPointItemDO;
import com.holderzone.saas.store.kds.entity.enums.KdsKitchenStateEnum;
import com.holderzone.saas.store.kds.mapstruct.KitchenItemMapstruct;
import com.holderzone.saas.store.kds.service.DistributeAreaService;
import com.holderzone.saas.store.kds.service.DistributeItemService;
import com.holderzone.saas.store.kds.service.PrdPointItemService;
import com.holderzone.saas.store.kds.utils.DeepCloneUtils;
import com.holderzone.saas.store.kds.utils.ItemMd5Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * 菜品和设备 一对一
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SingleKitchenItemHandler extends BaseAbstractKitchenItemHandler {

    private final PrdPointItemService prdPointItemService;

    private final DistributeItemService distributeItemService;

    private final DistributeAreaService distributeAreaService;

    private final KitchenItemMapstruct kitchenItemMapstruct;

    @Override
    public OrderInfoBO fullOrderInfoBO(ItemPrepareReqDTO itemPrepareReqDTO) {
        String storeGuid = UserContextUtils.getStoreGuid();
        List<SkuInfoRespDTO> skus = itemPrepareReqDTO.getSkus();
        OrderInfoBO orderInfoBO = new OrderInfoBO(itemPrepareReqDTO);
        List<String> skuPointReq = Stream.concat(skus.stream().map(SkuInfoRespDTO::getSkuGuid),
                skus.stream().map(SkuInfoRespDTO::getParentGuid)).distinct().collect(Collectors.toList());
        // 规格ID -> 菜
        Map<String, PrdPointItemDO> skuPointMap = prdPointItemService.queryPrdPointByItem(skuPointReq, storeGuid);
        // 规格、区域 --> deviceID
        Map<Pair<String, String>, String> skuArea2DstMap = skuArea2Dst(storeGuid, skuPointReq,
                Collections.singletonList(orderInfoBO.getAreaGuid()));
        // 完善 orderInfoBO 信息
        orderInfoBO.setSkuPointMap(skuPointMap).setSkuArea2DstMap(skuArea2DstMap);
        return orderInfoBO;
    }

    @Override
    public ChangeKitchenItemBO fullChangeKitchenItemBO(ItemChangesReqDTO itemChangesReqDTO) {
        ChangeKitchenItemBO kitchenItemBO = new ChangeKitchenItemBO();
        kitchenItemBO.setOrderGuid(itemChangesReqDTO.getOrderGuid());
        kitchenItemBO.setOrderNo(itemChangesReqDTO.getOrderNo());
        kitchenItemBO.setDiningTableGuid(itemChangesReqDTO.getDiningTableGuid());
        kitchenItemBO.setDiningTableName(itemChangesReqDTO.getDiningTableName());
        kitchenItemBO.setAreaGuid(itemChangesReqDTO.getAreaGuid());
        kitchenItemBO.setStoreGuid(itemChangesReqDTO.getStoreGuid());
        kitchenItemBO.setTradeMode(itemChangesReqDTO.getTradeMode());
        // 通过规格id获取点位商品
        List<SkuInfoRespDTO> skus = itemChangesReqDTO.getSkus();
        List<String> skuPointReq = Stream.concat(skus.stream().map(SkuInfoRespDTO::getSkuGuid),
                skus.stream().map(SkuInfoRespDTO::getParentGuid)).distinct().collect(Collectors.toList());
        Map<String, PrdPointItemDO> skuPointMap = prdPointItemService.queryPrdPointByItem(skuPointReq, itemChangesReqDTO.getStoreGuid());
        kitchenItemBO.setSkuPointMap(skuPointMap);
        // 规格、区域 --> deviceID
        Map<Pair<String, String>, String> skuArea2DstMap = skuArea2Dst(itemChangesReqDTO.getStoreGuid(), skuPointReq,
                Collections.singletonList(itemChangesReqDTO.getAreaGuid()));
        kitchenItemBO.setSkuArea2DstMap(skuArea2DstMap);
        return kitchenItemBO;
    }

    @Override
    public List<KitchenItemDO> convertItemAndSplitIfNecessary(ItemPrepareSplitBO itemPrepareSplitBO) {
        String storeGuid = itemPrepareSplitBO.getStoreGuid();
        KdsItemDTO kdsItemDTO = itemPrepareSplitBO.getKdsItemDTO();
        OrderInfoBO orderInfoBO = itemPrepareSplitBO.getOrderInfoBO();
        List<KitchenItemAttrDO> attrs = itemPrepareSplitBO.getAttrs();
        Map<String, DeviceConfigDO> deviceConfigInSqlMap = itemPrepareSplitBO.getDeviceConfigInSqlMap();
        LocalDateTime prepareTime = itemPrepareSplitBO.getPrepareTime();
        List<SkuInfoRespDTO> skus = itemPrepareSplitBO.getSkus();
        kdsItemDTO.setOrderRemark(orderInfoBO.getOrderRemark());
        KitchenItemDO kitchenItemDO = kitchenItemMapstruct.fromKdsItemReq(kdsItemDTO);
        List<SkuInfoRespDTO> respDTOS = skus.stream().filter(sku -> kdsItemDTO.getSkuGuid().equals(sku.getSkuGuid())
                || kdsItemDTO.getSkuGuid().equals(sku.getParentGuid())).collect(Collectors.toList());
        // 规格对应的制作点菜品
        PrdPointItemDO prdPointItemDO = getPrdPointItemDO(kdsItemDTO, orderInfoBO.getSkuPointMap(), respDTOS);
        // 获取规格 id、区域 id 对应的设备 id
        String dstDeviceId = getDstDeviceId(orderInfoBO.getAreaGuid(), orderInfoBO.getSkuArea2DstMap(), kitchenItemDO, respDTOS);
        if (prdPointItemDO == null && dstDeviceId == null) {
            return Collections.emptyList();
        }
        // 判断设备是否关闭外卖、且当前菜品是外卖订单
        if (Boolean.TRUE.equals(orderInfoBO.getIsTakeOutOrder()) && !Optional.ofNullable(prdPointItemDO)
                .map(PrdPointItemDO::getDeviceId).map(deviceConfigInSqlMap::get)
                .map(DeviceConfigDO::getIsFilterTakeOutOrder).orElse(true)) {
            return Collections.emptyList();
        }
        kitchenItemDO.setStoreGuid(storeGuid);
        if (prdPointItemDO != null) {
            DeviceConfigDO deviceConfigDO = deviceConfigInSqlMap.get(prdPointItemDO.getDeviceId());
            if (null != deviceConfigDO) {
                kitchenItemDO.setPointGuid(prdPointItemDO.getPointGuid());
                kitchenItemDO.setPrdDeviceId(prdPointItemDO.getDeviceId());
                kitchenItemDO.setIsPrintAutomatic(deviceConfigDO.getIsPrintAutomatic());
            }
        }
        if (dstDeviceId != null) {
            kitchenItemDO.setDstDeviceId(dstDeviceId);
        }
        kitchenItemDO.setDisplayType(orderInfoBO.getDisplayType());
        kitchenItemDO.setOrderGuid(orderInfoBO.getOrderGuid());
        kitchenItemDO.setOrderDesc(orderInfoBO.getOrderDesc());
        kitchenItemDO.setOrderNumber(orderInfoBO.getOrderNumber());
        kitchenItemDO.setOrderSerialNo(orderInfoBO.getOrderSerialNo());
        kitchenItemDO.setOrderRemark(orderInfoBO.getOrderRemark());
        kitchenItemDO.setAreaGuid(orderInfoBO.getAreaGuid());
        kitchenItemDO.setTableGuid(orderInfoBO.getTableGuid());
        kitchenItemDO.setReturnCount(BigDecimal.ZERO);
        kitchenItemDO.setKitchenState(KdsKitchenStateEnum.TO_PRD.getCode());
        if (KdsItemStateEnum.HANG_UP.getCode() == kdsItemDTO.getItemState()) {
            kitchenItemDO.setHangUpTime(prepareTime);
        } else {
            kitchenItemDO.setPrepareTime(prepareTime);
        }
        kitchenItemDO.setOrderSortTime(prepareTime);
        kitchenItemDO.setAddItemBatch(0);
        kitchenItemDO.setItemAttrMd5(ItemMd5Utils.calItemMd5(orderInfoBO, kdsItemDTO, attrs));
        if (Boolean.TRUE.equals(kdsItemDTO.getIsWeight())) {
            return Collections.singletonList(kitchenItemDO);
        }
        int endExclusive = kdsItemDTO.getCurrentCount().intValue();
        kdsItemDTO.setCurrentCount(BigDecimal.ONE);
        return IntStream.range(0, endExclusive)
                .mapToObj(value -> {
                    KitchenItemDO kitchenItemCloned = (KitchenItemDO) DeepCloneUtils.cloneObject(kitchenItemDO);
                    kitchenItemCloned.setCurrentCount(BigDecimal.ONE);
                    return kitchenItemCloned;
                })
                .collect(Collectors.toList());
    }

    @Override
    public void handleChangeItemDevice(ChangeKitchenItemBO kitchenItemBO, KdsItemDTO changesKdsItem, KitchenItemDO changeKitchenItemDO) {
        List<SkuInfoRespDTO> respDTOS = kitchenItemBO.getSkus().stream()
                .filter(sku -> changesKdsItem.getSkuGuid().equals(sku.getSkuGuid())
                        || changesKdsItem.getSkuGuid().equals(sku.getParentGuid()))
                .collect(Collectors.toList());
        // 制作口
        // 规格对应的制作点菜品
        PrdPointItemDO prdPointItemDO = getPrdPointItemDO(changesKdsItem, kitchenItemBO.getSkuPointMap(), respDTOS);
        changeKitchenItemDO.setPointGuid(null);
        changeKitchenItemDO.setPrdDeviceId(null);
        if (Objects.nonNull(prdPointItemDO)) {
            changeKitchenItemDO.setPointGuid(prdPointItemDO.getPointGuid());
            changeKitchenItemDO.setPrdDeviceId(prdPointItemDO.getDeviceId());
        }
        // 出堂口
        // 区域不会变，直接取库里的, 如果库里没有则取传入的
        String areaGuid = kitchenItemBO.getAreaGuid();
        if (Objects.nonNull(kitchenItemBO.getOriginalKitchenItemDO())) {
            areaGuid = kitchenItemBO.getOriginalKitchenItemDO().getAreaGuid();
        }
        Map<Pair<String, String>, String> skuArea2DstMap = kitchenItemBO.getSkuArea2DstMap();
        // 获取规格 id、区域 id 对应的设备 id
        String dstDeviceId = getDstDeviceId(areaGuid, skuArea2DstMap, changeKitchenItemDO, respDTOS);
        changeKitchenItemDO.setDstDeviceId(null);
        if (StringUtils.hasText(dstDeviceId)) {
            changeKitchenItemDO.setDstDeviceId(dstDeviceId);
        }
    }

    @Override
    public Map<String, List<String>> getChangeDeviceIds(ItemChangesReqDTO itemChangesReqDTO, KitchenItemChangesBO biz) {
        List<SkuInfoRespDTO> skuList = itemChangesReqDTO.getSkus();
        Map<String, List<String>> deviceItemMap = Maps.newHashMap();
        // 原菜
        List<KdsItemDTO> originalKdsItemList = itemChangesReqDTO.getOriginalKdsItemList();
        // 新菜
        List<KdsItemDTO> changesKdsItemList = itemChangesReqDTO.getChangesKdsItemList();
        // 制作设备
        Map<String, PrdPointItemDO> skuPointMap = biz.getChangeKitchenItemBO().getSkuPointMap();
        for (KdsItemDTO kdsItemDTO : originalKdsItemList) {
            PrdPointItemDO originalPointItemDO = skuPointMap.get(kdsItemDTO.getSkuGuid());
            if (Objects.nonNull(originalPointItemDO)) {
                List<String> bindSkuGuidList = deviceItemMap.getOrDefault(originalPointItemDO.getDeviceId(), Lists.newArrayList());
                bindSkuGuidList.add(kdsItemDTO.getSkuGuid());
                deviceItemMap.put(originalPointItemDO.getDeviceId(), bindSkuGuidList);
            }
        }
        for (KdsItemDTO kdsItemDTO : changesKdsItemList) {
            PrdPointItemDO changePointItemDO = skuPointMap.get(kdsItemDTO.getSkuGuid());
            if (Objects.nonNull(changePointItemDO)) {
                List<String> bindSkuGuidList = deviceItemMap.getOrDefault(changePointItemDO.getDeviceId(), Lists.newArrayList());
                bindSkuGuidList.add(kdsItemDTO.getSkuGuid());
                deviceItemMap.put(changePointItemDO.getDeviceId(), bindSkuGuidList);
            }
        }
        // 出堂设备
        Map<Pair<String, String>, String> skuArea2DstMap = biz.getChangeKitchenItemBO().getSkuArea2DstMap();
        for (SkuInfoRespDTO infoRespDTO : skuList) {
            String deviceId = skuArea2DstMap.get(Pair.of(infoRespDTO.getSkuGuid(), itemChangesReqDTO.getAreaGuid()));
            if (StringUtils.isEmpty(deviceId)) {
                String parentItemDeviceId = skuArea2DstMap.get(Pair.of(infoRespDTO.getParentGuid(), itemChangesReqDTO.getAreaGuid()));
                if (!StringUtils.isEmpty(parentItemDeviceId)) {
                    List<String> bindSkuGuidList = deviceItemMap.getOrDefault(parentItemDeviceId, Lists.newArrayList());
                    bindSkuGuidList.add(infoRespDTO.getParentGuid());
                    deviceItemMap.put(parentItemDeviceId, bindSkuGuidList);
                }
            } else {
                List<String> bindSkuGuidList = deviceItemMap.getOrDefault(deviceId, Lists.newArrayList());
                bindSkuGuidList.add(infoRespDTO.getSkuGuid());
                deviceItemMap.put(deviceId, bindSkuGuidList);
            }
        }
        return deviceItemMap;
    }


    private Map<Pair<String, String>, String> skuArea2Dst(String storeGuid, List<String> skuGuidList, List<String> areaGuidList) {
        // 以设备 ID 对 SkuGuid 进行分组
        Map<String, List<String>> dstSkuMap = distributeItemService.queryDstSkuMap(storeGuid, skuGuidList);
        // 以设备 ID 对 areaGuid 进行分组
        Map<String, List<String>> dstAreaMap = distributeAreaService.queryDstAreaMap(storeGuid, areaGuidList);
        Map<Pair<String, String>, String> skuArea2DstMap = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : dstSkuMap.entrySet()) {
            String dstGuid = entry.getKey();
            List<String> dstAreaGuidList = dstAreaMap.get(dstGuid);
            if (!CollectionUtils.isEmpty(dstAreaGuidList)) {
                List<String> dstSkuGuidList = entry.getValue();
                if (!CollectionUtils.isEmpty(dstSkuGuidList)) {
                    for (String skuGuid : dstSkuGuidList) {
                        for (String areaGuid : areaGuidList) {
                            skuArea2DstMap.put(Pair.of(skuGuid, areaGuid), dstGuid);
                        }
                    }
                }
            }
        }
        return skuArea2DstMap;
    }

    /**
     * 获取设备id 菜谱模式下 和 普通模式下
     * 绑定过相同SPU商品就无须绑定
     *
     * @param kitchenItemDO 商品
     * @param respDTOS      sku
     * @return 设备id
     */
    private String getDstDeviceId(String areaGuid, Map<Pair<String, String>, String> skuArea2DstMap,
                                  KitchenItemDO kitchenItemDO, List<SkuInfoRespDTO> respDTOS) {
        String dstDeviceId = skuArea2DstMap
                .get(Pair.of(kitchenItemDO.getSkuGuid(), areaGuid));
        if (dstDeviceId == null) {
            for (SkuInfoRespDTO respDTO : respDTOS) {
                dstDeviceId = skuArea2DstMap.get(Pair.of(respDTO.getSkuGuid(), areaGuid));
                if (null != dstDeviceId) {
                    break;
                }
                dstDeviceId = skuArea2DstMap.get(Pair.of(respDTO.getParentGuid(), areaGuid));
                if (null != dstDeviceId) {
                    break;
                }
            }
        }
        return dstDeviceId;
    }

    /**
     * 获取KDS绑定商品信息 菜谱模式下 和 普通模式下
     * * 绑定过相同SPU商品就无须绑定
     *
     * @param kdsItemDTO 商品
     * @param respDTOS   sku
     */
    private PrdPointItemDO getPrdPointItemDO(KdsItemDTO kdsItemDTO, Map<String, PrdPointItemDO> skuPointMap, List<SkuInfoRespDTO> respDTOS) {
        PrdPointItemDO prdPointItemDO = skuPointMap.get(kdsItemDTO.getSkuGuid());
        if (null == prdPointItemDO) {
            for (SkuInfoRespDTO respDTO : respDTOS) {
                prdPointItemDO = skuPointMap.get(respDTO.getSkuGuid());
                if (null != prdPointItemDO) {
                    break;
                }
                prdPointItemDO = skuPointMap.get(respDTO.getParentGuid());
                if (null != prdPointItemDO) {
                    break;
                }
            }
        }
        return prdPointItemDO;
    }

}
