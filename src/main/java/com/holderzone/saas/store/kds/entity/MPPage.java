package com.holderzone.saas.store.kds.entity;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description MPPage
 * @date 2021/2/1 16:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MPPage<T> extends Page<T> {

    public MPPage() {
        super(1, 10);
    }

    public MPPage(long current, long size) {
        super(current, size);
    }

    public MPPage(long current, long size, long total) {
        super(current, size, total);
    }

}
