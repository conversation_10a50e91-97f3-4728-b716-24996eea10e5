package com.holderzone.saas.store.kds.entity.enums;

import com.holderzone.framework.exception.unchecked.BusinessException;

public enum LevelDelayEnum {

    LEVEL4(4, 30),

    LEVEL5(5, 60),

    <PERSON><PERSON><PERSON><PERSON><PERSON>(6, 120),

    <PERSON><PERSON><PERSON><PERSON><PERSON>(7, 180),

    <PERSON><PERSON><PERSON><PERSON><PERSON>(8, 240),

    LE<PERSON>L9(9, 300),

    ;

    /**
     * RocketMQ等级
     */
    private int level;

    /**
     * 延时时间，单位秒
     */
    private int seconds;

    LevelDelayEnum(int level, int seconds) {
        this.level = level;
        this.seconds = seconds;
    }

    public int getLevel() {
        return level;
    }

    public int getSeconds() {
        return seconds;
    }

    public static LevelDelayEnum ofLevel(int level) {
        for (LevelDelayEnum levelDelayEnum : LevelDelayEnum.values()) {
            if (levelDelayEnum.level == level) {
                return levelDelayEnum;
            }
        }
        throw new BusinessException("不支持的延时等级：level=" + level);
    }

    public static int getSecondsByLevel(int level) {
        return ofLevel(level).getSeconds();
    }
}
