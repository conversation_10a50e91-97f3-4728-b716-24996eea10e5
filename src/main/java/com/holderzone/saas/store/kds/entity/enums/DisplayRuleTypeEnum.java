package com.holderzone.saas.store.kds.entity.enums;

/**
 * <AUTHOR>
 * @description 规则类型 0显示规则 1菜品汇总
 * @date 2021/1/28 12:21
 */
public enum DisplayRuleTypeEnum {

    DISPLAY_BATCH(0, "显示批次"),
    ITEM_SUMMARY(1, "菜品汇总");

    private Integer code;
    private String desc;

    DisplayRuleTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
