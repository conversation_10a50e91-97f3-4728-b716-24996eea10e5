package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hsk_queue_item")
public class QueueItemDO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一GUID
     */
    @TableField(value = "guid")
    private String guid;

    /**
     * 门店GUID
     */
    @TableField(value = "store_guid")
    private String storeGuid;

    /**
     * 设备GUID
     */
    @TableField(value = "device_guid")
    private String deviceGuid;

    /**
     * 订单Guid
     */
    @TableField(value = "order_guid")
    private String orderGuid;

    /**
     * 订单流水号
     */
    @TableField(value = "order_no")
    private String orderNo;

    /**
     * 订单交易模式
     */
    @TableField(value = "order_mode")
    private Integer orderMode;

    /**
     * 订单描述
     */
    @TableField(value = "order_desc")
    private String orderDesc;

    /**
     * 队列Item状态：0=等待中，1=待取餐，2=已取餐
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 进入等待队列时间
     */
    @TableField(value = "in_time")
    private LocalDateTime inTime;

    /**
     * 进入出堂队列时间
     */
    @TableField(value = "dst_time")
    private LocalDateTime dstTime;

    /**
     * 出堂时关联的kitchenItemGuid列表
     */
    @TableField(value = "dst_items")
    private String dstItems;

    /**
     * 出堂队列过期时间
     */
    @TableField(value = "dst_expire_time")
    private LocalDateTime dstExpireTime;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private LocalDateTime gmtModified;

    public static final String COL_GUID = "guid";

    public static final String COL_ORDER_GUID = "order_guid";

    public static final String COL_ORDER_NO = "order_no";

    public static final String COL_ORDER_MODE = "order_mode";

    public static final String COL_ORDER_DESC = "order_desc";

    public static final String COL_STATUS = "status";

    public static final String COL_IN_TIME = "in_time";

    public static final String COL_DST_TIME = "dst_time";

    public static final String COL_DST_EXPIRE_TIME = "dst_expire_time";

    public static final String COL_GMT_CREATE = "gmt_create";

    public static final String COL_GMT_MODIFIED = "gmt_modified";
}