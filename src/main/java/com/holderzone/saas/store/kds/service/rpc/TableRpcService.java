package com.holderzone.saas.store.kds.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.AreaDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemFeignClient
 * @date 2018/09/04 11:49
 * @description //TODO
 * @program holder-saas-store-takeaway
 */
@Component
@FeignClient(value = "holder-saas-store-table", fallbackFactory = TableRpcService.ServiceFallBack.class)
public interface TableRpcService {

    @PostMapping("/area/query/all/{storeGuid}")
    List<AreaDTO> query(@PathVariable("storeGuid") String storeGuid);

    @ApiModelProperty("根据桌台guid查桌台详情")
    @PostMapping("/table/details/{tableGuid}")
    TableDTO getTableByGuid(@PathVariable("tableGuid") String tableGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<TableRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TableRpcService create(Throwable throwable) {
            return new TableRpcService() {

                @Override
                public List<AreaDTO> query(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "query",
                            JacksonUtils.writeValueAsString(storeGuid),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public TableDTO getTableByGuid(String tableGuid) {
                    log.error(HYSTRIX_PATTERN, "getTableByGuid", tableGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
