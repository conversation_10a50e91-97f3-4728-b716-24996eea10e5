package com.holderzone.saas.store.kds.entity.enums;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradeModeEnum
 * @date 2018/09/04 17:52
 * @description 订单交易模式枚举
 * @program holder-saas-store-trade
 */
public enum KdsTradeModeEnum {

    DINE_IN(0, 4, "正餐", "您有一笔新的订单"),

    SNACK(1, 2, "快餐", "您有一笔新的快销订单"),

    TAKEOUT(2, 1, "外卖", "您有一笔新的外卖订单"),

    ;

    private int code;

    private int displayType;

    private String desc;

    private String voice;

    KdsTradeModeEnum(int code, int displayType, String desc, String voice) {
        this.code = code;
        this.displayType = displayType;
        this.desc = desc;
        this.voice = voice;
    }

    public int getCode() {
        return code;
    }

    public int getDisplayType() {
        return displayType;
    }

    public String getDesc() {
        return desc;
    }

    public String getVoice() {
        return voice;
    }

    public static KdsTradeModeEnum ofCode(int code) {
        for (KdsTradeModeEnum kdsTradeModeEnum : values()) {
            if (kdsTradeModeEnum.code == code) {
                return kdsTradeModeEnum;
            }
        }
        throw new BusinessException("不支持的交易模式：code=" + code);
    }

    public static KdsTradeModeEnum ofDisplayType(int displayType) {
        for (KdsTradeModeEnum kdsTradeModeEnum : values()) {
            if (kdsTradeModeEnum.displayType == displayType) {
                return kdsTradeModeEnum;
            }
        }
        throw new BusinessException("不支持的交易模式：displayType=" + displayType);
    }

    public static int getDisplayTypeByCode(int code) {
        return ofCode(code).getDisplayType();
    }

    public static String getDescByCode(int code) {
        return ofCode(code).getDesc();
    }

    public static String getVoiceByCode(int code) {
        return ofCode(code).getVoice();
    }

    public static int getCodeByDisplayType(int displayType) {
        return ofDisplayType(displayType).getCode();
    }

    public static boolean isDineInByDisplayType(int displayType) {
        return DINE_IN.getDisplayType() == displayType;
    }

    public static boolean isSnackByDisplayType(int displayType) {
        return SNACK.getDisplayType() == displayType;
    }
}
