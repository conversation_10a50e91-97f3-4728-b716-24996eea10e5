package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleItemSortDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleItemSortQueryDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.kds.entity.domain.DisplayItemSortDO;
import com.holderzone.saas.store.kds.mapper.DisplayItemSortMapper;
import com.holderzone.saas.store.kds.service.DisplayItemSortService;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import com.holderzone.saas.store.kds.service.rpc.StoreClientService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * <p>
 * 菜品显示顺序配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
@Slf4j
@Service
@AllArgsConstructor
public class DisplayItemSortServiceImpl extends ServiceImpl<DisplayItemSortMapper, DisplayItemSortDO>
        implements DisplayItemSortService {

    private final DistributedIdService distributedIdService;

    private final StoreClientService storeClientService;

    @Override
    @Transactional
    public void saveOrUpdateItemSortRule(DisplayRuleItemSortDTO reqDTO) {
        DisplayItemSortDO one = this.getOne(new LambdaQueryWrapper<DisplayItemSortDO>()
                .eq(DisplayItemSortDO::getBrandGuid, reqDTO.getBrandGuid()));

        DisplayItemSortDO itemSortDO = new DisplayItemSortDO();
        itemSortDO.setBrandGuid(reqDTO.getBrandGuid());
        itemSortDO.setItemSortType(reqDTO.getItemSortType());
        itemSortDO.setItemDisplayType(reqDTO.getItemDisplayType());
        itemSortDO.setItemIntervalTime(reqDTO.getItemIntervalTime());
        if (ObjectUtils.isEmpty(one)) {
            log.info("保存菜品显示顺序规则");
            itemSortDO.setGuid(distributedIdService.nextDisplayRuleGuid());
            this.save(itemSortDO);
            return;
        }
        if (!StringUtils.hasText(reqDTO.getGuid())) {
            throw new BusinessException("更新菜品显示顺序规则Guid不能为空");
        }
        log.info("更新菜品显示顺序规则");
        this.update(itemSortDO, new LambdaQueryWrapper<DisplayItemSortDO>()
                .eq(DisplayItemSortDO::getGuid, reqDTO.getGuid()));
    }

    @Override
    public DisplayRuleItemSortDTO queryItemSortRule(DisplayRuleItemSortQueryDTO reqDTO) {
        DisplayItemSortDO itemSortDO = this.getOne(new LambdaQueryWrapper<DisplayItemSortDO>()
                .eq(DisplayItemSortDO::getBrandGuid, reqDTO.getBrandGuid()));
        if (ObjectUtils.isEmpty(itemSortDO)) {
            return null;
        }
        DisplayRuleItemSortDTO itemSortDTO = new DisplayRuleItemSortDTO();
        itemSortDTO.setGuid(itemSortDO.getGuid());
        itemSortDTO.setBrandGuid(itemSortDO.getBrandGuid());
        itemSortDTO.setItemSortType(itemSortDO.getItemSortType());
        itemSortDTO.setItemDisplayType(itemSortDO.getItemDisplayType());
        itemSortDTO.setItemIntervalTime(itemSortDO.getItemIntervalTime());
        return itemSortDTO;
    }

    @Override
    public DisplayRuleItemSortDTO queryItemSortRuleByStore(DisplayRuleItemSortQueryDTO reqDTO) {
        StoreDTO storeDTO = storeClientService.queryStoreByGuid(reqDTO.getStoreGuid());
        if (ObjectUtils.isEmpty(storeDTO)) {
            log.info("[未查询到门店]返回默认值");
            return new DisplayRuleItemSortDTO()
                    .setItemSortType(BooleanEnum.FALSE.getCode());
        }
        if (!StringUtils.hasText(storeDTO.getBelongBrandGuid())) {
            log.info("[门店未绑定品牌]返回默认值");
            return new DisplayRuleItemSortDTO()
                    .setItemSortType(BooleanEnum.FALSE.getCode());
        }
        reqDTO.setBrandGuid(storeDTO.getBelongBrandGuid());
        DisplayRuleItemSortDTO itemSortDTO = queryItemSortRule(reqDTO);
        if (ObjectUtils.isEmpty(itemSortDTO)) {
            log.info("[品牌下未配置菜品显示顺序]返回默认值");
            return new DisplayRuleItemSortDTO()
                    .setItemSortType(BooleanEnum.FALSE.getCode());
        }
        return itemSortDTO;
    }

}
