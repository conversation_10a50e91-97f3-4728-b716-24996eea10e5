package com.holderzone.saas.store.kds.service;

import com.holderzone.saas.store.dto.kds.req.QueueConfigDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.kds.entity.domain.QueueConfigDO;
import com.baomidou.mybatisplus.extension.service.IService;
public interface QueueConfigService extends IService<QueueConfigDO>{

    void create(StoreDeviceDTO storeDeviceDTO);

    void update(QueueConfigDTO queueConfigDTO);

    QueueConfigDTO query(QueueConfigDTO queueConfigDTO);

    void delete(StoreDeviceDTO storeDeviceDTO);
}
