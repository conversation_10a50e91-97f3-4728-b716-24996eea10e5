package com.holderzone.saas.store.kds.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.kds.resp.PrdPointItemDTO;
import com.holderzone.saas.store.kds.entity.domain.PrdPointItemDO;

import java.util.List;

/**
 * 制作点堂口商品 Mapper 接口
 *
 * <AUTHOR>
 * @since 2019-03-29
 */
public interface PrdPointItemMapper extends BaseMapper<PrdPointItemDO> {

    /**
     * 查询kds所有绑定的菜品
     *
     * @return
     */
    List<PrdPointItemDTO> queryAll();
}
