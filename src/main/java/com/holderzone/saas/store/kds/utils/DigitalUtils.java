package com.holderzone.saas.store.kds.utils;

import java.util.HashMap;
import java.util.Map;

public class DigitalUtils {

    private static Map<Character, String> DIGITAL_MAP = new HashMap<>();

    static {
        DIGITAL_MAP.put('0', "零");
        DIGITAL_MAP.put('1', "一");
        DIGITAL_MAP.put('2', "二");
        DIGITAL_MAP.put('3', "三");
        DIGITAL_MAP.put('4', "四");
        DIGITAL_MAP.put('5', "五");
        DIGITAL_MAP.put('6', "六");
        DIGITAL_MAP.put('7', "七");
        DIGITAL_MAP.put('8', "八");
        DIGITAL_MAP.put('9', "九");
    }

    public static String humanize(String voice) {
        StringBuilder sb = new StringBuilder();
        for (char aChar : voice.toCharArray()) {
            sb.append(DIGITAL_MAP.getOrDefault(aChar, String.valueOf(aChar)));
        }
        return sb.toString();
    }
}
