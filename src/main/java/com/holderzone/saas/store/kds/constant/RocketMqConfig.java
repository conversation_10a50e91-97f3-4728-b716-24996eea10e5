package com.holderzone.saas.store.kds.constant;

public class RocketMqConfig {

    public static final String KDS_MESSAGE_TOPIC = "kds-message-topic";

    public static final String KDS_PREPARE_TAG = "kds-prepare-tag";

    public static final String KDS_CHANGES_TAG = "kds-changes-tag";

    public static final String KDS_TRANSFER_TAG = "kds-transfer-tag";

    public static final String KDS_CALL_TAG = "kds-call-tag";

    public static final String KDS_URGE_TAG = "kds-urge-tag";

    public static final String KDS_REMARK_TAG = "kds-remark-tag";

    public static final String KDS_CHANGE_TABLE_TAG = "kds-change-table-tag";

    public static final String KDS_REFUND_TAG = "kds-refund-tag";

    public static final String KDS_DELAY_DISPLAY_TAG = "kds-delay-display-tag";

    public static final String KDS_MESSAGE_GROUP = "kds-message-group";

    public static final String KDS_QUEUE_EXPIRE_TOPIC = "kds-queue-expire-topic";

    public static final String KDS_QUEUE_EXPIRE_TAG = "kds-queue-expire-tag";

    public static final String KDS_QUEUE_EXPIRE_GROUP = "kds-queue-expire-group";

    public static final String DOWNSTREAM_CONTEXT = "downstream-context";

    public static final String DOWNSTREAM_DEVICE_TOPIC = "downstream-device-topic";

    public static final String DOWNSTREAM_DEVICE_BIND_TAG = "downstream-device-bind-tag";

    public static final String DOWNSTREAM_DEVICE_UNBIND_TAG = "downstream-device-unbind-tag";

    public static final String DOWNSTREAM_DEVICE_BINDING_GROUP = "downstream-device-binding-group";
}
