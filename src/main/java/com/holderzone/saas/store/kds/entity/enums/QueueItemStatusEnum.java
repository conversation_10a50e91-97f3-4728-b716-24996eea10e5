package com.holderzone.saas.store.kds.entity.enums;

import com.holderzone.framework.exception.unchecked.BusinessException;

public enum QueueItemStatusEnum {

    PREPARED(0, "等待中"),

    DISTRIBUTED(1, "待取餐"),

    ;

    private Integer status;

    private String description;

    QueueItemStatusEnum(Integer status, String description) {
        this.status = status;
        this.description = description;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDescription() {
        return description;
    }

    public static QueueItemStatusEnum ofStatus(Integer status) {
        for (QueueItemStatusEnum statusEnum : QueueItemStatusEnum.values()) {
            if (statusEnum.status.equals(status)) {
                return statusEnum;
            }
        }
        throw new BusinessException("不支持的Status：" + status);
    }

    public static String getDescByStatus(Integer status) {
        return ofStatus(status).getDescription();
    }

    public static boolean isPrepared(Integer status) {
        return PREPARED.status.equals(status);
    }

    public static boolean isDistributed(Integer status) {
        return DISTRIBUTED.status.equals(status);
    }
}
