package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.framework.base.dto.message.*;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.kds.service.QueuePushService;
import com.holderzone.saas.store.kds.service.rpc.MsgRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutorService;

@Slf4j
@Service
public class QueuePushServiceImpl implements QueuePushService {

    private static final String QUEUE_BUSINESS_TYPE = "kds_queue_status";

    private final ExecutorService executorService;

    private final MsgRpcService msgRpcService;

    @Autowired
    public QueuePushServiceImpl(ExecutorService executorService, MsgRpcService msgRpcService) {
        this.executorService = executorService;
        this.msgRpcService = msgRpcService;
    }

    @Override
    public void statusChanged(String enterpriseGuid, String storeGuid, String deviceId, String voiceMsg) {
        executorService.execute(() -> {
            PushMessageDTO pushMessageDTO = new PushMessageDTO();
            pushMessageDTO.setTopicType(TopicType.BUSINESS);
            BusinessMessage businessMessage = new BusinessMessage();
            businessMessage.setEnterpriseGuid(enterpriseGuid);
            businessMessage.setStoreGuid(storeGuid);
            businessMessage.setBusinessType(topic(deviceId));
            pushMessageDTO.setBusinessMessage(businessMessage);
            pushMessageDTO.setData(voiceMsg);
            MessageDTO messageDTO = new MessageDTO();
            messageDTO.setMessageType(MessageType.PUSH);
            messageDTO.setPushMessage(pushMessageDTO);
            msgRpcService.sendPrintMessage(messageDTO);
            log.info("队列消息推送完毕，deviceId：{}，voiceMsg：{}", deviceId, voiceMsg);
        });
    }

    private String topic(String deviceId) {
        if (!StringUtils.hasText(deviceId)) {
            return QUEUE_BUSINESS_TYPE;
        }
        return QUEUE_BUSINESS_TYPE + ":" + deviceId;
    }
}
