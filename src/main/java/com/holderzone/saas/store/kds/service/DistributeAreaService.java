package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.kds.req.DeviceQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.DstBindStatusRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DistributeAreaDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface DistributeAreaService extends IService<DistributeAreaDO> {

    DstBindStatusRespDTO queryAreaBindingPreview(DeviceQueryReqDTO deviceQueryReqDTO);

    List<DistributeAreaDO> queryBoundAreaOfStore(String storeGuid);

    List<DistributeAreaDO> queryBoundAreaOfDevice(String storeGuid, String deviceId);

    List<String> queryBoundAreaGuidOfDevice(String storeGuid, String deviceId);

    List<String> queryBoundAreaGuidOfDeviceList(String storeGuid, List<String> deviceIdList);

    List<String> queryOccupiedDeviceId(String storeGuid, String deviceId);

    void simpleSaveBatchArea(String storeGuid, String deviceId, List<String> toBeBoundAreaGuid);

    void simpleRemoveBatchArea(String storeGuid, String deviceId, List<String> toBeRemoveAreaGuid);

    void reInitialize(String storeGuid, String deviceId);

    Map<String, List<String>> queryDstAreaMap(String storeGuid, List<String> areaGuidList);
}
