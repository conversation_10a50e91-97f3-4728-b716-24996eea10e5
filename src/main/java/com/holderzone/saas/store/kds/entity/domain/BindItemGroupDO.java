package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 绑定菜品分组
 */
@Data
@TableName("hsk_bind_item_group")
public class BindItemGroupDO implements Serializable {

    private static final long serialVersionUID = -3188503906592357249L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 唯一GUID
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableLogic
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 分组名称
     */
    private String name;

}
