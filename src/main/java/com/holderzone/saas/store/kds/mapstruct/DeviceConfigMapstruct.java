package com.holderzone.saas.store.kds.mapstruct;

import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.DeviceStatusRespDTO;
import com.holderzone.saas.store.dto.kds.resp.ProductionPointRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.entity.domain.ItemConfigDO;
import com.holderzone.saas.store.kds.entity.domain.PrdPointItemDO;
import com.holderzone.saas.store.kds.entity.domain.ProductionPointDO;
import com.holderzone.saas.store.kds.entity.read.PointItemReadDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring")
public interface DeviceConfigMapstruct {

    @Mapping(source = "deviceId", target = "guid")
    DeviceConfigDO fromCreateReq(DeviceCreateReqDTO deviceCreateReqDTO);

    @Mapping(source = "guid", target = "deviceId")
    DeviceStatusRespDTO toStatusResp(DeviceConfigDO deviceConfigDO);

    DevicePrdConfDTO toPrdConfResp(DeviceConfigDO deviceConfigDO);

    DeviceDstConfDTO toDstConfResp(DeviceConfigDO deviceConfigDO);

    ProductionPointDO fromPointCreateReq(PrdPointCreateReqDTO prdPointCreateReqDTO);

    @Mapping(source = "pointGuid", target = "guid")
    ProductionPointDO fromPointUpdateReq(PrdPointUpdateReqDTO prdPointUpdateReqDTO);

    PrdPointItemDO fromItemConfigUpdateReq(PrdDstItemBindDTO prdDstItemBindDTO, PrdPointItemBindReqDTO prdPointItemBindReqDTO);

    ItemConfigDO fromItemConfigUpdateReq(ItemConfigUpdateReqDTO prdPointItemConfigUpdateReqDTO);

    @Mapping(source = "isPrintPerOrder", target = "isPrintPerOrder", defaultValue = "true")
    DeviceConfigDO fromDevicePrdConfReq(DevicePrdConfDTO devicePrdConfDTO);

    DeviceConfigDO fromDeviceDstConfReq(DeviceDstConfDTO deviceDstConfDTO);

    @Mappings({
            @Mapping(source = "pointName", target = "name"),
            @Mapping(source = "itemCount", target = "boundItemCount"),
    })
    ProductionPointRespDTO toPointResp(PointItemReadDO pointItemReadDO);

    List<ProductionPointRespDTO> toPointResp(List<PointItemReadDO> pointItemReadDOS);
}
