package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @description 显示门店表
 * @date 2021/1/27 16:51
 */
@Data
@Accessors(chain = true)
@TableName("hsk_display_store")
public class DisplayStoreDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId
    private Long id;
    /**
     * 全局唯一主键
     */
    private String guid;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
    /**
     * 是否删除
     */
    private Integer isDelete;
    /**
     * 显示规则guid
     */
    private String ruleGuid;
    /**
     * 门店guid
     */
    private String storeGuid;
    /**
     * 规则类型 0显示批次 1菜品汇总
     */
    private Integer ruleType;

}
