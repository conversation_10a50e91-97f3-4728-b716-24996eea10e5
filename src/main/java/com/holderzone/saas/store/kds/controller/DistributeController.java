package com.holderzone.saas.store.kds.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.*;
import com.holderzone.saas.store.kds.service.DeviceConfigService;
import com.holderzone.saas.store.kds.service.DistributeItemService;
import com.holderzone.saas.store.kds.service.DistributeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Api("KDS设备接口")
@RequestMapping("/distribute")
public class DistributeController {

    private final DistributeService distributeService;

    private final DeviceConfigService deviceConfigService;

    private final DistributeItemService distributeItemService;

    @Autowired
    public DistributeController(DistributeService distributeService,
                                DeviceConfigService deviceConfigService,
                                DistributeItemService distributeItemService) {
        this.distributeService = distributeService;
        this.deviceConfigService = deviceConfigService;
        this.distributeItemService = distributeItemService;
    }

    @PostMapping("/query_binding_preview")
    @ApiOperation(value = "查询出堂口绑定关系预览")
    public DstBindStatusRespDTO queryBindingPreview(@RequestBody @Validated DeviceQueryReqDTO deviceQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询出堂口绑定关系预览入参:{}", JacksonUtils.writeValueAsString(deviceQueryReqDTO));
        }
        return distributeService.queryBindingPreview(deviceQueryReqDTO);
    }

    @PostMapping("/query_binding_details")
    @ApiOperation(value = "查询出堂口绑定关系详情（包括预览和已绑定商品列表）")
    public DstBindDetailsRespDTO queryBindingDetails(@RequestBody @Validated DstItemQueryReqDTO dstItemQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询出堂口绑定关系详情入参:{}", JacksonUtils.writeValueAsString(dstItemQueryReqDTO));
        }
        return distributeService.queryBindingDetails(dstItemQueryReqDTO);
    }

    @PostMapping("/query_bound_item_of_device")
    @ApiOperation(value = "查询出堂口已绑定商品")
    public List<DstTypeBindRespDTO> queryBoundItemOfDevice(@RequestBody @Validated DstItemQueryReqDTO dstItemQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询出堂口已绑定商品入参:{}", JacksonUtils.writeValueAsString(dstItemQueryReqDTO));
        }
        return distributeService.queryBoundItemOfDevice(dstItemQueryReqDTO);
    }

    @PostMapping("/query_bound_area")
    @ApiOperation(value = "查询出堂口绑定区域")
    public DstAreaRespDTO queryBoundArea(@RequestBody @Validated DstBoundAreaReqDTO dstBoundAreaReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询出堂口绑定区域入参:{}", JacksonUtils.writeValueAsString(dstBoundAreaReqDTO));
        }
        return distributeService.queryBoundArea(dstBoundAreaReqDTO);
    }

    @PostMapping("/bind_area")
    @ApiOperation(value = "出堂口绑定区域")
    public void bindArea(@RequestBody @Validated DstBindAreaReqDTO dstBindAreaReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("出堂口绑定区域入参:{}", JacksonUtils.writeValueAsString(dstBindAreaReqDTO));
        }
        distributeService.bindArea(dstBindAreaReqDTO);
    }

    @PostMapping("/query_all_item_of_store")
    @ApiOperation(value = "查询所有出堂口绑定商品")
    public List<DstTypeBindRespDTO> queryAllItemOfStore(@RequestBody @Validated DstItemQueryReqDTO dstItemQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询所有出堂口绑定商品入参:{}", JacksonUtils.writeValueAsString(dstItemQueryReqDTO));
        }
        return distributeService.queryAllItemOfStore(dstItemQueryReqDTO);
    }

    @PostMapping("/bind_item")
    @ApiOperation(value = "KDS出堂口绑定菜品")
    public void bindItem(@RequestBody @Validated DstBindItemReqDTO dstBindItemReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("KDS出堂口绑定菜品入参:{}", JacksonUtils.writeValueAsString(dstBindItemReqDTO));
        }
        distributeService.bindItem(dstBindItemReqDTO);
    }

    @PostMapping("/unbind_item")
    @ApiOperation(value = "KDS出堂口解绑菜品")
    public void unbindItem(@RequestBody @Validated DstBindItemReqDTO dstBindItemReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("KDS出堂口解绑菜品入参:{}", JacksonUtils.writeValueAsString(dstBindItemReqDTO));
        }
        distributeService.unbindItem(dstBindItemReqDTO);
    }

    @PostMapping("/update_basic_config")
    @ApiOperation(value = "更新设备基础配置")
    public void updateBasicConfig(@Validated
                                  @RequestBody DeviceBasicConfUpdateReqDTO deviceBasicConfUpdateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新设备基础配置入参:{}", JacksonUtils.writeValueAsString(deviceBasicConfUpdateReqDTO));
        }
        deviceConfigService.updateBasic(
                deviceBasicConfUpdateReqDTO.getStoreGuid(),
                deviceBasicConfUpdateReqDTO.getDeviceId(),
                deviceBasicConfUpdateReqDTO.getDisplayMode(),
                deviceBasicConfUpdateReqDTO.getItemDisplayType()
        );
    }

    @PostMapping("/update_advanced_config")
    @ApiOperation(value = "更新设备高级配置")
    public void updateAdvancedConfig(@Validated(DeviceAdvanConfUpdateReqDTO.DistributeConfig.class)
                                     @RequestBody DeviceAdvanConfUpdateReqDTO deviceAdvanConfUpdateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新设备高级配置入参:{}", JacksonUtils.writeValueAsString(deviceAdvanConfUpdateReqDTO));
        }
        deviceConfigService.updateDstAdvanced(
                deviceAdvanConfUpdateReqDTO.getStoreGuid(),
                deviceAdvanConfUpdateReqDTO.getDeviceId(),
                deviceAdvanConfUpdateReqDTO.getDeviceDstConfDTO()
        );
    }

    @ApiOperation(value = "根据sku查询出堂商品配置")
    @PostMapping("/query_distribute_item_by_sku")
    public List<DistributeItemDTO> queryDistributeItemBySku(@RequestBody SingleDataDTO reqDTO) {
        log.info("[根据sku查询出堂商品配置],reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        return distributeItemService.queryDistributeItemBySku(reqDTO);
    }
}

