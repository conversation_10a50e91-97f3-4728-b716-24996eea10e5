package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.kds.entity.domain.DisplayRepeatItemStoreDO;

import java.util.List;

/**
 * 菜品重复绑定适用门店
 */
public interface DisplayRepeatItemStoreService extends IService<DisplayRepeatItemStoreDO> {

    List<String> storeGuidListByBrandGuid(String brandGuid);

    void save(String brandGuid, List<String> storeGuids);

    void removeByBrandGuid(String brandGuid);

}
