package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 显示商品表
 * @date 2021/1/27 16:49
 */
@Data
@Accessors(chain = true)
@TableName("hsk_display_item")
public class DisplayItemDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 全局唯一主键
     */
    private String guid;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
    /**
     * 是否删除
     */
    private Integer isDelete;
    /**
     * 显示规则guid
     */
    private String ruleGuid;
    /**
     * 商品guid
     */
    private String itemGuid;
    /**
     * 序号
     */
    private Integer sort;
    /**
     * 规则类型 0显示批次 1菜品汇总
     */
    private Integer ruleType;

    /**
     * 商品规格
     */
    private String productSpecGuid;

}
