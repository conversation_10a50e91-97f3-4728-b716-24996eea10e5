package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Functions;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.MappingRespDTO;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.*;
import com.holderzone.saas.store.kds.constant.Constants;
import com.holderzone.saas.store.kds.entity.domain.*;
import com.holderzone.saas.store.kds.entity.read.PointItemReadDO;
import com.holderzone.saas.store.kds.mapper.ProductionPointMapper;
import com.holderzone.saas.store.kds.mapstruct.DeviceConfigMapstruct;
import com.holderzone.saas.store.kds.service.*;
import com.holderzone.saas.store.kds.service.rpc.ItemRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机单据管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
@SuppressWarnings("unchecked")
public class ProductionPointServiceImpl extends ServiceImpl<ProductionPointMapper, ProductionPointDO> implements ProductionPointService {

    private final DeviceConfigService deviceConfigService;

    private final PrdPointItemService prdPointItemService;

    private final ItemConfigService itemConfigService;

    private final ItemRpcService itemRpcService;

    private final DistributedIdService distributedIdService;

    private final DeviceConfigMapstruct deviceConfigMapstruct;

    private final DeviceBindItemGroupService deviceBindItemGroupService;

    private final BindItemService bindItemService;

    @Autowired
    public ProductionPointServiceImpl(@Lazy DeviceConfigService deviceConfigService,
                                      PrdPointItemService prdPointItemService,
                                      ItemConfigService itemConfigService,
                                      ItemRpcService itemRpcService,
                                      DeviceConfigMapstruct deviceConfigMapstruct,
                                      DistributedIdService distributedIdService,
                                      DeviceBindItemGroupService deviceBindItemGroupService,
                                      BindItemService bindItemService) {
        this.deviceConfigService = deviceConfigService;
        this.prdPointItemService = prdPointItemService;
        this.itemConfigService = itemConfigService;
        this.itemRpcService = itemRpcService;
        this.distributedIdService = distributedIdService;
        this.deviceConfigMapstruct = deviceConfigMapstruct;
        this.deviceBindItemGroupService = deviceBindItemGroupService;
        this.bindItemService = bindItemService;
    }

    @Override
    public String createPoint(PrdPointCreateReqDTO prdPointCreateReqDTO) {
        deviceConfigService.assertThatDeviceExists(prdPointCreateReqDTO.getStoreGuid(), prdPointCreateReqDTO.getDeviceId());
        ProductionPointDO productionPointDO = deviceConfigMapstruct.fromPointCreateReq(prdPointCreateReqDTO);
        int pointSizeOfTheDevice = count(new LambdaQueryWrapper<ProductionPointDO>()
                .eq(ProductionPointDO::getStoreGuid, productionPointDO.getStoreGuid())
                .eq(ProductionPointDO::getDeviceId, productionPointDO.getDeviceId()));
        if (pointSizeOfTheDevice >= ProductionPointDO.MAX_POINT) {
            throw new BusinessException("最多创建4个堂口");
        }
        if (count(new LambdaQueryWrapper<ProductionPointDO>()
                .eq(ProductionPointDO::getName, prdPointCreateReqDTO.getName())
                .eq(ProductionPointDO::getStoreGuid, prdPointCreateReqDTO.getStoreGuid())) > 0) {
            throw new BusinessException("堂口名称[" + productionPointDO.getName() + "]重复");
        }
        String pointGuid = distributedIdService.nextPrdPointGuid();
        productionPointDO.setGuid(pointGuid);
        save(productionPointDO);
        return pointGuid;
    }

    @Override
    public void deletePoint(PrdPointDelReqDTO prdPointDelReqDTO) {
        deviceConfigService.assertThatDeviceExists(prdPointDelReqDTO.getStoreGuid(), prdPointDelReqDTO.getDeviceId());
        remove(new LambdaQueryWrapper<ProductionPointDO>()
                .eq(ProductionPointDO::getGuid, prdPointDelReqDTO.getPointGuid()));
        prdPointItemService.unbindItem(prdPointDelReqDTO);
        deviceBindItemGroupService.unbind(prdPointDelReqDTO.getStoreGuid(),
                prdPointDelReqDTO.getPointGuid(), prdPointDelReqDTO.getDeviceId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reInitialize(String storeGuid, String deviceId) {
        remove(new LambdaQueryWrapper<ProductionPointDO>()
                .eq(ProductionPointDO::getStoreGuid, storeGuid)
                .eq(ProductionPointDO::getDeviceId, deviceId));
        prdPointItemService.reInitialize(storeGuid, deviceId);
    }

    @Override
    public List<ProductionPointRespDTO> listPoint(PrdPointListReqDTO prdPointListReqDTO) {
        ProductionPointDO productionPointDO = new ProductionPointDO();
        productionPointDO.setStoreGuid(prdPointListReqDTO.getStoreGuid());
        productionPointDO.setDeviceId(prdPointListReqDTO.getDeviceId());
        List<PointItemReadDO> list;
        if (Boolean.TRUE.equals(prdPointListReqDTO.getAllowRepeatFlag())) {
            list = allowRepeatFlagListPoint(productionPointDO);
        } else {
            list = baseMapper.queryPrdPointWithItemCount(productionPointDO);
        }
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return deviceConfigMapstruct.toPointResp(list);
    }

    private List<PointItemReadDO> allowRepeatFlagListPoint(ProductionPointDO productionPointDO) {
        // 查询该设备堂口列表
        List<ProductionPointDO> allPrdPointInSql = list(new LambdaQueryWrapper<ProductionPointDO>()
                .select(ProductionPointDO::getGuid, ProductionPointDO::getName)
                .eq(ProductionPointDO::getStoreGuid, productionPointDO.getStoreGuid())
                .eq(ProductionPointDO::getDeviceId, productionPointDO.getDeviceId())
        );
        List<PointItemReadDO> defaultPointList = allPrdPointInSql.stream().map(e -> {
            PointItemReadDO pointItemReadDO = new PointItemReadDO();
            pointItemReadDO.setGuid(e.getGuid());
            pointItemReadDO.setStoreGuid(e.getStoreGuid());
            pointItemReadDO.setDeviceId(e.getDeviceId());
            pointItemReadDO.setPointName(e.getName());
            pointItemReadDO.setItemCount(0);
            return pointItemReadDO;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allPrdPointInSql)) {
            return defaultPointList;
        }
        Map<String, ProductionPointDO> allPrdPointMap = allPrdPointInSql.stream()
                .collect(Collectors.toMap(ProductionPointDO::getGuid, Function.identity(), (key1, key2) -> key1));
        // 查询堂口绑定的菜品分组
        List<DeviceBindItemGroupDO> deviceBindItemGroupList = deviceBindItemGroupService.listByDeviceId(productionPointDO.getDeviceId());
        deviceBindItemGroupList = deviceBindItemGroupList.stream()
                .filter(e -> allPrdPointMap.containsKey(e.getPointGuid()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deviceBindItemGroupList)) {
            return defaultPointList;
        }
        // 查询菜品分组下的商品
        List<String> groupGuids = deviceBindItemGroupList.stream()
                .map(DeviceBindItemGroupDO::getGroupGuid)
                .distinct()
                .collect(Collectors.toList());
        List<BindItemDO> bindItemList = bindItemService.listByGroupGuids(groupGuids);
        if (CollectionUtils.isEmpty(bindItemList)) {
            return defaultPointList;
        }
        // 查询绑定的菜品是否存在
        List<String> bindItemGuids = bindItemList.stream()
                .map(BindItemDO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        // 查询itemGuid -> parentItemGuid
        ItemStringListDTO query = new ItemStringListDTO();
        query.setDataList(bindItemGuids);
        Map<String, String> itemMap = itemRpcService.queryParentItemGuidByItem(query);
        // 查询门店商品
        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData(productionPointDO.getStoreGuid());
        List<MappingRespDTO> itemInfoRespList = itemRpcService.kdsMapping(itemSingleDTO);
        // 过滤
        List<String> existItemGuids = itemInfoRespList.stream()
                .map(MappingRespDTO::geteDishCode)
                .distinct()
                .collect(Collectors.toList());
        List<String> existParentItemGuids = itemInfoRespList.stream()
                .map(MappingRespDTO::getParentItemGuid)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(existParentItemGuids)) {
            existItemGuids.addAll(existParentItemGuids);
        }
        bindItemList.removeIf(e -> !existItemGuids.contains(e.getItemGuid())
                && !existItemGuids.contains(itemMap.getOrDefault(e.getItemGuid(), e.getItemGuid())));
        if (CollectionUtils.isEmpty(bindItemList)) {
            return defaultPointList;
        }
        Map<String, List<BindItemDO>> bindItemMap = bindItemList.stream()
                .collect(Collectors.groupingBy(BindItemDO::getGroupGuid));
        Map<String, List<DeviceBindItemGroupDO>> deviceBindItemGroupMap = deviceBindItemGroupList.stream()
                .collect(Collectors.groupingBy(DeviceBindItemGroupDO::getPointGuid));
        for (PointItemReadDO pointItemReadDO : defaultPointList) {
            String pointGuid = pointItemReadDO.getGuid();
            // 堂口绑定的分组
            List<DeviceBindItemGroupDO> innerDeviceBindItemGroupList = deviceBindItemGroupMap.get(pointGuid);
            if (CollectionUtils.isEmpty(innerDeviceBindItemGroupList)) {
                continue;
            }
            List<String> innerGroupGuidList = innerDeviceBindItemGroupList.stream()
                    .map(DeviceBindItemGroupDO::getGroupGuid)
                    .distinct()
                    .collect(Collectors.toList());
            for (String groupGuid : innerGroupGuidList) {
                List<BindItemDO> innerBindItemList = bindItemMap.getOrDefault(groupGuid, Lists.newArrayList());
                List<String> innerBindItemGuidList = innerBindItemList.stream()
                        .map(BindItemDO::getItemGuid)
                        .distinct()
                        .collect(Collectors.toList());
                pointItemReadDO.setItemCount(pointItemReadDO.getItemCount() + innerBindItemGuidList.size());
            }
        }
        return defaultPointList;
    }

    @Override
    public void updatePoint(PrdPointUpdateReqDTO prdPointUpdateReqDTO) {
        deviceConfigService.assertThatDeviceExists(
                prdPointUpdateReqDTO.getStoreGuid(), prdPointUpdateReqDTO.getDeviceId()
        );
        ProductionPointDO productionPointDO = deviceConfigMapstruct.fromPointUpdateReq(prdPointUpdateReqDTO);
        if (count(new LambdaQueryWrapper<ProductionPointDO>()
                .ne(ProductionPointDO::getGuid, productionPointDO.getGuid())
                .eq(ProductionPointDO::getName, productionPointDO.getName())
                .eq(ProductionPointDO::getStoreGuid, productionPointDO.getStoreGuid())) > 0) {
            throw new BusinessException("堂口名称[" + productionPointDO.getName() + "]重复");
        }
        update(productionPointDO, new LambdaQueryWrapper<ProductionPointDO>()
                .eq(ProductionPointDO::getGuid, productionPointDO.getGuid()));
    }

    @Override
    public void bindPointItem(PrdPointItemBindReqDTO prdPointItemBindReqDTO) {
        if (!Boolean.TRUE.equals(prdPointItemBindReqDTO.getBindingItemGroupFlag())
                && CollectionUtils.isEmpty(prdPointItemBindReqDTO.getBindingItems())) {
            throw new BusinessException("绑定商品列表不得为空");
        }
        deviceConfigService.assertThatDeviceExists(
                prdPointItemBindReqDTO.getStoreGuid(), prdPointItemBindReqDTO.getDeviceId()
        );
        assertThatPointExist(prdPointItemBindReqDTO.getPointGuid());
        prdPointItemService.bindItem(prdPointItemBindReqDTO);
    }

    @Override
    public void unbindPointItem(PrdPointItemBindReqDTO prdPointItemBindReqDTO) {
        deviceConfigService.assertThatDeviceExists(
                prdPointItemBindReqDTO.getStoreGuid(), prdPointItemBindReqDTO.getDeviceId()
        );
        assertThatPointExist(prdPointItemBindReqDTO.getPointGuid());
        prdPointItemService.unbindItem(prdPointItemBindReqDTO);
    }

    @Override
    public PointBindDetailsRespDTO queryBindingDetails(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        List<PointTypeBindRespDTO> pointTypeBindRespDTOS = queryBoundPointItem(prdPointItemQueryReqDTO);
        PointBindDetailsRespDTO pointBindDetailsRespDTO = new PointBindDetailsRespDTO();
        pointBindDetailsRespDTO.setBoundItemCount(pointTypeBindRespDTOS.stream()
                .mapToInt(itemType -> itemType.getItemList().size()).sum());
        pointBindDetailsRespDTO.setPointTypeBindList(pointTypeBindRespDTOS);
        return pointBindDetailsRespDTO;
    }

    @Override
    public List<PointTypeBindRespDTO> queryBoundPointItem(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        return queryPointItemByCondition(prdPointItemQueryReqDTO, false);
    }

    /**
     * 查询KDS制作点堂口已绑定未绑定菜品
     *
     * @param prdPointItemQueryReqDTO prdPointItemQueryReqDTO
     * @return List<PointTypeBindRespDTO>
     */
    @Override
    public List<PointTypeBindRespDTO> queryAllPointItem(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        return queryPointItemByCondition(prdPointItemQueryReqDTO, true);
    }

    @Override
    public void updateBasicConfig(DeviceBasicConfUpdateReqDTO deviceBasicConfUpdateReqDTO) {
        deviceConfigService.updateBasic(
                deviceBasicConfUpdateReqDTO.getStoreGuid(),
                deviceBasicConfUpdateReqDTO.getDeviceId(),
                deviceBasicConfUpdateReqDTO.getDisplayMode(),
                deviceBasicConfUpdateReqDTO.getItemDisplayType()
        );
    }

    @Override
    public void updateAdvancedConfig(DeviceAdvanConfUpdateReqDTO deviceAdvanConfUpdateReqDTO) {
        deviceConfigService.updatePrdAdvanced(
                deviceAdvanConfUpdateReqDTO.getStoreGuid(),
                deviceAdvanConfUpdateReqDTO.getDeviceId(),
                deviceAdvanConfUpdateReqDTO.getDevicePrdConfDTO()
        );
    }

    @Override
    public List<PointItemReadDO> fillEmptyPoint(String storeGuid, String deviceId, List<PointItemReadDO> pointItemReadInSql) {
        Map<String, PointItemReadDO> pointReadMap = pointItemReadInSql.stream()
                .collect(Collectors.toMap(PointItemReadDO::getGuid, Functions.identity()));
        List<ProductionPointDO> prdPointsInSql = list(new LambdaQueryWrapper<ProductionPointDO>()
                .eq(ProductionPointDO::getStoreGuid, storeGuid)
                .eq(ProductionPointDO::getDeviceId, deviceId)
                .orderByAsc(ProductionPointDO::getGmtCreate));
        return prdPointsInSql.stream()
                .map(productionPointDO -> Optional.ofNullable(pointReadMap.get(productionPointDO.getGuid()))
                        .orElse(PointItemReadDO.of(productionPointDO)))
                .collect(Collectors.toList());
    }

    private void assertThatPointExist(String pointGuid) {
        int count = count(new LambdaQueryWrapper<ProductionPointDO>()
                .eq(ProductionPointDO::getGuid, pointGuid));
        if (count == 0) {
            log.error("根据 pointGuid[{}] 未找到堂口", pointGuid);
            throw new BusinessException("堂口不存在");
        }
    }

    private List<PointTypeBindRespDTO> queryPointItemByCondition(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO,
                                                                 boolean queryAll) {
        assertThatPointExist(prdPointItemQueryReqDTO.getPointGuid());

        // 所有设备元数据
        Map<String, String> deviceNameMap = deviceConfigService.getDeviceNameMapOfStore(prdPointItemQueryReqDTO.getStoreGuid());

        // 所有制作点元数据
        List<ProductionPointDO> allPrdPointInSql = list(new LambdaQueryWrapper<ProductionPointDO>()
                .select(ProductionPointDO::getGuid, ProductionPointDO::getName)
                .eq(ProductionPointDO::getStoreGuid, prdPointItemQueryReqDTO.getStoreGuid()));
        Map<String, String> pointNameMap = allPrdPointInSql.stream()
                .collect(Collectors.toMap(ProductionPointDO::getGuid, ProductionPointDO::getName));
        // 堂口已绑定菜品
        List<PrdPointItemDO> prdPointItemInSql = prdPointItemService.queryBoundItem(prdPointItemQueryReqDTO);
        Map<String, PrdPointItemDO> prdPointItemMap = prdPointItemInSql.stream()
                .collect(Collectors.toMap(PrdPointItemDO::getSkuGuid, Function.identity()));
        List<String> bindSkuGuids = prdPointItemInSql.stream().map(PrdPointItemDO::getSkuGuid).distinct().collect(Collectors.toList());
        // 商品元数据
        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData(prdPointItemQueryReqDTO.getStoreGuid());
        itemSingleDTO.setSkuGuids(bindSkuGuids);
        List<MappingRespDTO> mappingRespDTOS = itemRpcService.kdsMapping(itemSingleDTO);

        log.info("商品元数据：{}", JacksonUtils.writeValueAsString(mappingRespDTOS));

        // 菜品基础配置
        List<ItemConfigDO> allItemConfigInSql = itemConfigService.queryBatchByStoreGuid(prdPointItemQueryReqDTO.getStoreGuid());
        Map<String, ItemConfigDO> itemConfigMap = allItemConfigInSql.stream()
                .collect(Collectors.toMap(ItemConfigDO::getSkuGuid, Function.identity()));

        // 制作点位信息
        String currentPointGuid = prdPointItemQueryReqDTO.getPointGuid();

        List<PointTypeBindRespDTO> list = mappingRespDTOS.stream()
                .collect(Collectors.groupingBy(o -> Pair.of(o.getCategoryId(), o.getCategoryName())))
                .entrySet().stream()
                .map(typeEntry -> {
                    // 对商品进行分类
                    PointTypeBindRespDTO pointTypeBindRespDTO = new PointTypeBindRespDTO();
                    String typeGuid = typeEntry.getKey().getFirst();
                    String typeName = typeEntry.getKey().getSecond();
                    pointTypeBindRespDTO.setTypeGuid(typeGuid);
                    pointTypeBindRespDTO.setTypeName(typeName);
                    List<MappingRespDTO> skuList = typeEntry.getValue();
                    Map<String, MappingRespDTO> itemMap = skuList.stream()
                            .collect(Collectors.toMap(MappingRespDTO::geteDishCode, Function.identity(), (key1, key2) -> key1));

                    // 分类下的商品，以菜名进行分组，即，菜 -> 多规格
                    Map<Pair<String, String>, List<MappingRespDTO>> itemSkusMap = typeEntry.getValue().stream()
                            .collect(Collectors.groupingBy(o -> Pair.of(o.geteDishCode(), o.geteDishName())));

                    List<PointItemBindRespDTO> typeItemList = itemSkusMap.entrySet().stream()
                            .map(itemEntry -> {
                                // 规格
                                PointItemBindRespDTO pointItemBindRespDTO = new PointItemBindRespDTO();
                                pointItemBindRespDTO.setItemGuid(itemEntry.getKey().getFirst());
                                pointItemBindRespDTO.setItemName(itemEntry.getKey().getSecond());
                                pointItemBindRespDTO.setPlanItemName(itemMap.getOrDefault(itemEntry.getKey().getFirst(),
                                        new MappingRespDTO()).getPlanItemName());
                                pointItemBindRespDTO.setIsBoundBySelf(false);
                                pointItemBindRespDTO.setIsBoundByOthers(false);
                                // 获取当前规格的菜品的绑定信息
                                List<PointSkuBindRespDTO> skuWithBindingInfo = getSkuWithBindingInfo(
                                        itemEntry, pointItemBindRespDTO,
                                        currentPointGuid, deviceNameMap, pointNameMap, prdPointItemMap, itemConfigMap
                                );
                                pointItemBindRespDTO.setSkus(skuWithBindingInfo.stream()
                                        .filter(pointSkuBindRespDTO ->
                                                queryAll | pointSkuBindRespDTO.getIsBoundBySelf())
                                        .collect(Collectors.toList())
                                );


                                return pointItemBindRespDTO;
                            })
                            .filter(pointItemBindRespDTO -> queryAll
                                    || !CollectionUtils.isEmpty(pointItemBindRespDTO.getSkus()))
                            .collect(Collectors.toList());
                    pointTypeBindRespDTO.setItemList(typeItemList);
                    return pointTypeBindRespDTO;
                })
                .filter(pointTypeBindRespDTO -> queryAll
                        || !CollectionUtils.isEmpty(pointTypeBindRespDTO.getItemList()))
                .collect(Collectors.toList());
        log.info("返回数据：{}", JacksonUtils.writeValueAsString(list));
        return list;
    }

    private List<PointSkuBindRespDTO> getSkuWithBindingInfo(Map.Entry<Pair<String, String>, List<MappingRespDTO>> itemEntry,
                                                            PointItemBindRespDTO pointItemBindRespDTO,
                                                            String currentPointGuid,
                                                            Map<String, String> deviceNameMap,
                                                            Map<String, String> pointNameMap,
                                                            Map<String, PrdPointItemDO> prdPointItemMap,
                                                            Map<String, ItemConfigDO> itemConfigMap) {
        return itemEntry.getValue()
                .stream()
                .map(itemSku -> {
                    PointSkuBindRespDTO pointSkuBindRespDTO = new PointSkuBindRespDTO();
                    pointSkuBindRespDTO.setSkuGuid(itemSku.geteDishSkuCode());
                    pointSkuBindRespDTO.setSkuName(itemSku.geteDishSkuName());
                    pointSkuBindRespDTO.setSkuCode(itemSku.getSkuCode());
                    setItemBindingFlag(
                            itemSku, pointSkuBindRespDTO, pointItemBindRespDTO,
                            currentPointGuid, deviceNameMap, pointNameMap, prdPointItemMap
                    );
                    setItemConfig(itemSku, pointSkuBindRespDTO, itemConfigMap);
                    return pointSkuBindRespDTO;
                })
                .collect(Collectors.toList());
    }

    private void setItemConfig(MappingRespDTO itemSku, PointSkuBindRespDTO pointSkuBindRespDTO, Map<String, ItemConfigDO> itemConfigMap) {
        ItemConfigDO itemConfigDO = itemConfigMap.get(itemSku.geteDishSkuCode());
        if (itemConfigDO != null) {
            pointSkuBindRespDTO.setTimeout(itemConfigDO.getTimeout());
            pointSkuBindRespDTO.setMaxCopies(itemConfigDO.getMaxCopies());
            pointSkuBindRespDTO.setDisplayType(itemConfigDO.getDisplayType());
        } else {
            pointSkuBindRespDTO.setTimeout(Constants.ITEM_TIMEOUT);
            pointSkuBindRespDTO.setMaxCopies(Constants.ITEM_MAX_COPIES);
            pointSkuBindRespDTO.setDisplayType(Constants.ITEM_DISPLAY_TYPE);
        }
    }

    private void setItemBindingFlag(MappingRespDTO itemSku,
                                    PointSkuBindRespDTO pointSkuBindRespDTO,
                                    PointItemBindRespDTO pointItemBindRespDTO,
                                    String currentPointGuid, Map<String, String> deviceNameMap,
                                    Map<String, String> pointNameMap, Map<String, PrdPointItemDO> prdPointItemMap) {
        PrdPointItemDO boundPrdPointItem = prdPointItemMap.get(itemSku.geteDishSkuCode());
        pointItemBindRespDTO.setPinyin(itemSku.getPinyin());
        if (boundPrdPointItem != null) {
            pointSkuBindRespDTO.setDeviceId(boundPrdPointItem.getDeviceId());
            pointSkuBindRespDTO.setDeviceName(deviceNameMap.get(boundPrdPointItem.getDeviceId()));
            pointSkuBindRespDTO.setPointGuid(boundPrdPointItem.getPointGuid());
            pointSkuBindRespDTO.setPointName(pointNameMap.get(boundPrdPointItem.getPointGuid()));
            if (currentPointGuid.equalsIgnoreCase(boundPrdPointItem.getPointGuid())) {
                pointItemBindRespDTO.setIsBoundBySelf(true);
                pointItemBindRespDTO.setIsBoundByOthers(false);
                pointSkuBindRespDTO.setIsBoundBySelf(true);
                pointSkuBindRespDTO.setIsBoundByOthers(false);
            } else if (StringUtils.hasText(boundPrdPointItem.getPointGuid())) {
                pointItemBindRespDTO.setIsBoundBySelf(false);
                pointItemBindRespDTO.setIsBoundByOthers(true);
                pointSkuBindRespDTO.setIsBoundBySelf(false);
                pointSkuBindRespDTO.setIsBoundByOthers(true);
            } else {
                pointItemBindRespDTO.setIsBoundBySelf(false);
                pointItemBindRespDTO.setIsBoundByOthers(false);
                pointSkuBindRespDTO.setIsBoundBySelf(false);
                pointSkuBindRespDTO.setIsBoundByOthers(false);
            }
        } else {
            pointItemBindRespDTO.setIsBoundBySelf(false);
            pointItemBindRespDTO.setIsBoundByOthers(false);
            pointSkuBindRespDTO.setIsBoundBySelf(false);
            pointSkuBindRespDTO.setIsBoundByOthers(false);
        }
    }
}
