package com.holderzone.saas.store.kds.service.template;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.kds.req.KdsPrintDTO;
import com.holderzone.saas.store.kds.service.print.KdsPrintTemplate;
import com.holderzone.saas.store.kds.service.print.KdsInvoiceTypeEnum;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintComponentFactory
 * @date 2018/02/14 09:00
 * @description 打印单据模板工厂类
 * @program holder-saas-store-print
 */
@Component
@SuppressWarnings("unchecked")
public class PrintComponentFactory {

    public KdsPrintTemplate<? extends KdsPrintDTO> create(Integer invoiceType, int pageSize,
                                                          String content) {
        KdsPrintTemplate<? extends KdsPrintDTO> printTemplate = create(invoiceType, pageSize);
        printTemplate.setPrintDTO(KdsInvoiceTypeEnum.resolvePrintBy(content));
        printTemplate.getPrintDTO().setCreateTime(DateTimeUtils.now());
        return printTemplate;
    }

    public KdsPrintTemplate<? extends KdsPrintDTO> create(Integer invoiceType, String content) {
        KdsPrintTemplate<? extends KdsPrintDTO> printTemplate = create(invoiceType);
        printTemplate.setPrintDTO(KdsInvoiceTypeEnum.resolvePrintBy(content));
        printTemplate.getPrintDTO().setCreateTime(DateTimeUtils.now());
        return printTemplate;
    }

    public KdsPrintTemplate<? extends KdsPrintDTO> create(Integer invoiceType, int pageSize) {
        KdsPrintTemplate<? extends KdsPrintDTO> printTemplate = create(invoiceType);
        printTemplate.setPageSize(pageSize);
        return printTemplate;
    }

    public KdsPrintTemplate<? extends KdsPrintDTO> create(Integer invoiceType) {
        switch (KdsInvoiceTypeEnum.ofType(invoiceType)) {
            case PRD_ITEM: {
                return new PrdItemTemplate();
            }
            case DST_ITEM: {
                return new DstItemTemplate();
            }
            case CHANGES_ITEM: {
                return new ChangeItemTemplate();
            }
            default: {
                throw new BusinessException("不支持的票据类型：" + invoiceType);
            }
        }
    }
}
