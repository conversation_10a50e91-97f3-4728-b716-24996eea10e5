package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.kds.req.DeviceQueryReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdDstItemBindDTO;
import com.holderzone.saas.store.dto.kds.resp.DistributeItemDTO;
import com.holderzone.saas.store.dto.kds.resp.DstBindStatusRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DistributeItemDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface DistributeItemService extends IService<DistributeItemDO> {

    DstBindStatusRespDTO queryItemBindingPreview(DeviceQueryReqDTO deviceQueryReqDTO);

    List<DistributeItemDO> queryBoundItemOfStore(String storeGuid);

    List<DistributeItemDO> queryBoundItemOfDevice(String storeGuid, String deviceId);

    List<String> queryOccupiedDeviceId(String storeGuid, String deviceId);

    List<String> queryBoundSkuGuidOfDevice(String storeGuid, String deviceId);

    List<String> queryBoundSkuGuidOfDeviceList(String storeGuid, List<String> occupiedDeviceId);

    void simpleSaveBatchSku(String storeGuid, String deviceId, List<PrdDstItemBindDTO> toBeBoundSkuList);

    void simpleRemoveBatchSku(String storeGuid, String deviceId, List<String> toBeRemoveSkuGuid);

    void reInitialize(String storeGuid, String deviceId);

    Map<String, List<String>> queryDstSkuMap(String storeGuid, List<String> skuGuidList);

    List<DistributeItemDTO> queryDistributeItemBySku(SingleDataDTO reqDTO);
}
