package com.holderzone.saas.store.kds.entity.query;

import com.holderzone.saas.store.dto.kds.req.DstItemStatusReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdItemStatusReqDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ItemQuery {

    private String storeGuid;

    private String orderGuid;

    private String deviceId;

    private List<String> deviceIdList;

    private String pointGuid;

    private Integer displayType;

    private Integer kitchenState;

    private List<String> kitchenItemGuidList;

    private List<String> orderGuidList;

    private List<String> skuGuidList;

    /**
     * 显示挂起的菜品
     * 关闭后，将不显示挂起的菜品
     */
    private Boolean isShowHangedItem;

    /**
     * 显示未制作菜品
     * 关闭之后，制作口必须点击制作按钮后，出堂口才能看到菜品
     */
    private Boolean isDisplayItemUnProduced;

    /**
     * 制作完成需手动确定
     */
    private Boolean isManualConfirm;

    private Integer displayRuleType;

    /**
     * 查询关键字
     */
    private String keywords;

    @ApiModelProperty(value = "加菜显示顺序 0按下单时间依次展示 1突出展示")
    private Integer itemSortType;

    /**
     * 加菜突出显示时间设置
     * 距第一次下单时间%s分钟后的新加菜品突出显示
     */
    @ApiModelProperty(value = "加菜突出显示时间设置 距第一次下单时间%s分钟后的新加菜品突出显示")
    private Integer itemIntervalTime;

    /**
     * 是否允许重复
     */
    @ApiModelProperty(value = "菜品是否允许重复显示")
    private Boolean allowRepeatFlag;

    @ApiModelProperty(value = "菜品分组集合")
    private List<String> groupGuids;

    public static ItemQuery of(List<String> orderGuidList) {
        ItemQuery itemQuery = new ItemQuery();
        itemQuery.setOrderGuidList(orderGuidList);
        return itemQuery;
    }

    public static ItemQuery of(String orderGuid) {
        ItemQuery itemQuery = new ItemQuery();
        itemQuery.setOrderGuid(orderGuid);
        return itemQuery;
    }

    public static ItemQuery of(String orderGuid, List<String> deviceIdList, List<String> kitchenItemGuidList) {
        ItemQuery itemQuery = new ItemQuery();
        itemQuery.setOrderGuid(orderGuid);
        itemQuery.setDeviceIdList(deviceIdList);
        itemQuery.setDisplayType(8);
        itemQuery.setKitchenState(4);
        itemQuery.setKitchenItemGuidList(kitchenItemGuidList);
        return itemQuery;
    }

    public static ItemQuery of(PrdItemStatusReqDTO prdItemStatusReqDTO) {
        ItemQuery itemQuery = new ItemQuery();
        itemQuery.setStoreGuid(prdItemStatusReqDTO.getStoreGuid());
        itemQuery.setDeviceId(prdItemStatusReqDTO.getDeviceId());
        itemQuery.setPointGuid(prdItemStatusReqDTO.getPointGuid());
        itemQuery.setDisplayType(Integer.valueOf(prdItemStatusReqDTO.getDisplayType()));
        itemQuery.setKitchenState(prdItemStatusReqDTO.getKitchenState());
        itemQuery.setKeywords(prdItemStatusReqDTO.getKeywords());
        itemQuery.setAllowRepeatFlag(prdItemStatusReqDTO.getAllowRepeatFlag());
        return itemQuery;
    }

    public static ItemQuery of(DstItemStatusReqDTO dstItemStatusReqDTO) {
        ItemQuery itemQuery = new ItemQuery();
        itemQuery.setStoreGuid(dstItemStatusReqDTO.getStoreGuid());
        itemQuery.setDeviceId(dstItemStatusReqDTO.getDeviceId());
        itemQuery.setDisplayType(Integer.valueOf(dstItemStatusReqDTO.getDisplayType()));
        itemQuery.setKeywords(dstItemStatusReqDTO.getKeywords());
        itemQuery.setAllowRepeatFlag(dstItemStatusReqDTO.getAllowRepeatFlag());
        return itemQuery;
    }

    public static ItemQuery of(String storeGuid, DstItemStatusReqDTO dstItemStatusReqDTO) {
        ItemQuery itemQuery = new ItemQuery();
        itemQuery.setStoreGuid(storeGuid);
        itemQuery.setDeviceId(dstItemStatusReqDTO.getDeviceId());
        itemQuery.setDisplayType(Integer.valueOf(dstItemStatusReqDTO.getDisplayType()));
        itemQuery.setKeywords(dstItemStatusReqDTO.getKeywords());
        itemQuery.setAllowRepeatFlag(dstItemStatusReqDTO.getAllowRepeatFlag());
        return itemQuery;
    }
}
