package com.holderzone.saas.store.kds.interceptor;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import io.undertow.servlet.spec.HttpServletRequestImpl;
import io.undertow.util.HeaderMap;
import io.undertow.util.HttpString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WebInterceptor
 * @date 2019/02/14 09:00
 * @description 固定UserInfo，调试使用！
 * 如需关闭，请注释掉：
 * FakeInterceptor的“@Configuration”
 * WebConfig#addInterceptors的“registry.addInterceptor(new FakeInterceptor()).order(-1);”
 * @program holder-saas-store-print
 */
@Configuration
public class FakeInterceptor implements HandlerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(FakeInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid("6506431195651982337");
        userContext.setEnterpriseName("企业0227_3");
        userContext.setEnterpriseNo("");
        userContext.setStoreGuid("6506453252643487745");
        userContext.setStoreName("门店0227_3");
        userContext.setStoreNo("4478046");
        userContext.setUserGuid("6480756476603191298");
        userContext.setUserName("tcw");
        userContext.setAccount("100001");
        userContext.setTel("***********");
        if (request instanceof HttpServletRequestImpl) {
            HttpServletRequestImpl httpServletRequest = (HttpServletRequestImpl) request;
            HeaderMap requestHeaders = httpServletRequest.getExchange().getRequestHeaders();
            requestHeaders.put(new HttpString(USER_INFO), JacksonUtils.writeValueAsString(userContext));
            log.info("|----->  Fake userInfo works now !!!");
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }
}
