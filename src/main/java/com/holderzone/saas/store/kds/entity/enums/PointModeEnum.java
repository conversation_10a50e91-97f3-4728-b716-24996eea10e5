package com.holderzone.saas.store.kds.entity.enums;

import com.holderzone.framework.exception.unchecked.BusinessException;

public enum PointModeEnum {

    PRODUCTION(0, "制作点"),

    DISTRIBUTE(1, "出堂口"),
    ALL (-1,"全部")
    ;

    private Integer pointMode;

    private String pointDesc;

    PointModeEnum(Integer pointMode, String pointDesc) {
        this.pointMode = pointMode;
        this.pointDesc = pointDesc;
    }

    public String getPointDesc() {
        return pointDesc;
    }

    public int  getCode(){
        return pointMode;
    }
    public static PointModeEnum ofMode(Integer pointMode) {
        for (PointModeEnum pointModeEnum : PointModeEnum.values()) {
            if (pointModeEnum.pointMode.equals(pointMode)) {
                return pointModeEnum;
            }
        }
        throw new BusinessException("不支持的PointMode：" + pointMode);
    }

    public static String getDescByMode(Integer pointMode) {
        return ofMode(pointMode).getPointDesc();
    }
}
