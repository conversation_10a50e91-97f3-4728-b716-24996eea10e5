package com.holderzone.saas.store.kds.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.PointBindDetailsRespDTO;
import com.holderzone.saas.store.dto.kds.resp.PointTypeBindRespDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdPointItemDTO;
import com.holderzone.saas.store.dto.kds.resp.ProductionPointRespDTO;
import com.holderzone.saas.store.kds.service.ItemConfigService;
import com.holderzone.saas.store.kds.service.PrdPointItemService;
import com.holderzone.saas.store.kds.service.ProductionPointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Api("KDS设备接口")
@RequestMapping("/production")
public class ProductionController {

    private final ProductionPointService productionPointService;

    private final ItemConfigService itemConfigService;

    private final PrdPointItemService prdPointItemService;

    @Autowired
    public ProductionController(ProductionPointService productionPointService, ItemConfigService itemConfigService, PrdPointItemService prdPointItemService) {
        this.productionPointService = productionPointService;
        this.itemConfigService = itemConfigService;
        this.prdPointItemService = prdPointItemService;
    }

    @PostMapping("/create_point")
    @ApiOperation(value = "创建KDS制作点堂口")
    public String createPoint(@Validated @RequestBody PrdPointCreateReqDTO prdPointCreateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("创建KDS制作点堂口入参:{}", JacksonUtils.writeValueAsString(prdPointCreateReqDTO));
        }
        return productionPointService.createPoint(prdPointCreateReqDTO);
    }

    @PostMapping("/list_point")
    @ApiOperation(value = "查询KDS制作点堂口列表")
    public List<ProductionPointRespDTO> listPoint(@Validated @RequestBody PrdPointListReqDTO prdPointListReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询KDS制作点堂口列表入参:{}", JacksonUtils.writeValueAsString(prdPointListReqDTO));
        }
        return productionPointService.listPoint(prdPointListReqDTO);
    }

    @PostMapping("/update_point")
    @ApiOperation(value = "更新KDS制作点堂口")
    public void updatePoint(@Validated @RequestBody PrdPointUpdateReqDTO prdPointUpdateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新KDS制作点堂口入参:{}", JacksonUtils.writeValueAsString(prdPointUpdateReqDTO));
        }
        productionPointService.updatePoint(prdPointUpdateReqDTO);
    }

    @PostMapping("/delete_point")
    @ApiOperation(value = "删除KDS制作点堂口")
    public void deletePoint(@Validated @RequestBody PrdPointDelReqDTO prdPointDelReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("删除KDS制作点堂口入参:{}", JacksonUtils.writeValueAsString(prdPointDelReqDTO));
        }
        productionPointService.deletePoint(prdPointDelReqDTO);
    }

    @PostMapping("/query_binding_details")
    @ApiOperation(value = "查询KDS制作点堂口绑定细节")
    public PointBindDetailsRespDTO queryBindingDetails(@Validated
                                                       @RequestBody PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询KDS制作点堂口绑定细节入参:{}", JacksonUtils.writeValueAsString(prdPointItemQueryReqDTO));
        }
        return productionPointService.queryBindingDetails(prdPointItemQueryReqDTO);
    }

    @PostMapping("/query_bound_point_item")
    @ApiOperation(value = "查询KDS制作点堂口已绑定菜品")
    public List<PointTypeBindRespDTO> queryBoundPointItem(@Validated
                                                          @RequestBody PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询KDS制作点堂口已绑定菜品入参:{}", JacksonUtils.writeValueAsString(prdPointItemQueryReqDTO));
        }
        return productionPointService.queryBoundPointItem(prdPointItemQueryReqDTO);
    }

    @PostMapping("/unbind_point_item")
    @ApiOperation(value = "KDS制作点堂口解绑菜品")
    public void unbindPointItem(@Validated @RequestBody PrdPointItemBindReqDTO prdPointItemBindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("KDS制作点堂口解绑菜品入参:{}", JacksonUtils.writeValueAsString(prdPointItemBindReqDTO));
        }
        productionPointService.unbindPointItem(prdPointItemBindReqDTO);
    }

    @PostMapping("/query_all_point_item")
    @ApiOperation(value = "查询KDS制作点堂口已绑定未绑定菜品")
    public List<PointTypeBindRespDTO> queryAllPointItem(@Validated
                                                        @RequestBody PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询KDS制作点堂口已绑定未绑定菜品入参:{}", JacksonUtils.writeValueAsString(prdPointItemQueryReqDTO));
        }
        return productionPointService.queryAllPointItem(prdPointItemQueryReqDTO);
    }

    @PostMapping("/bind_point_item")
    @ApiOperation(value = "KDS制作点堂口绑定菜品")
    public void bindPointItem(@Validated @RequestBody PrdPointItemBindReqDTO prdPointItemBindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("KDS制作点堂口绑定菜品入参:{}", JacksonUtils.writeValueAsString(prdPointItemBindReqDTO));
        }
        productionPointService.bindPointItem(prdPointItemBindReqDTO);
    }

    @PostMapping("/update_point_item_config")
    @ApiOperation(value = "更新堂口菜品配置")
    public void updatePointItemConfig(@Validated @RequestBody ItemConfBatchUpdateReqDTO itemConfBatchUpdateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新堂口菜品配置入参:{}", JacksonUtils.writeValueAsString(itemConfBatchUpdateReqDTO));
        }
        itemConfigService.insertOrUpdateBatch(itemConfBatchUpdateReqDTO);
    }

    @PostMapping("/update_basic_config")
    @ApiOperation(value = "更新设备基础配置")
    public void updateBasicConfig(@Validated
                                  @RequestBody DeviceBasicConfUpdateReqDTO deviceBasicConfUpdateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新设备基础配置入参:{}", JacksonUtils.writeValueAsString(deviceBasicConfUpdateReqDTO));
        }
        productionPointService.updateBasicConfig(deviceBasicConfUpdateReqDTO);
    }

    @PostMapping("/update_advanced_config")
    @ApiOperation(value = "更新设备高级配置")
    public void updateAdvancedConfig(@Validated(DeviceAdvanConfUpdateReqDTO.ProductionConfig.class)
                                     @RequestBody DeviceAdvanConfUpdateReqDTO deviceAdvanConfUpdateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新设备高级配置入参:{}", JacksonUtils.writeValueAsString(deviceAdvanConfUpdateReqDTO));
        }
        productionPointService.updateAdvancedConfig(deviceAdvanConfUpdateReqDTO);
    }

    @PostMapping("/query_item_by_sku")
    @ApiOperation(value = "根据商品sku查询绑定菜品")
    public List<PrdPointItemDTO> queryBoundItemBySku(@RequestBody PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("根据商品sku查询绑定菜品入参:{}", JacksonUtils.writeValueAsString(prdPointItemQueryReqDTO));
        }
        return prdPointItemService.queryBoundItemBySku(prdPointItemQueryReqDTO);
    }

    @PostMapping("/query_all_item")
    @ApiOperation(value = "查询kds所有绑定的菜品")
    public List<PrdPointItemDTO> queryAllBoundItem() {
        return prdPointItemService.queryAllBoundItem();
    }
}

