package com.holderzone.saas.store.kds.mapstruct;

import com.holderzone.saas.store.dto.kds.req.KdsPrinterBindUnbindReqDTO;
import com.holderzone.saas.store.dto.kds.req.KdsPrinterCreateReqDTO;
import com.holderzone.saas.store.dto.kds.req.KdsPrinterDeleteReqDTO;
import com.holderzone.saas.store.dto.kds.req.KdsPrinterUpdateReqDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsPrinterRespDTO;
import com.holderzone.saas.store.kds.entity.domain.KdsPrintRecordDO;
import com.holderzone.saas.store.kds.entity.domain.KdsPrinterDO;
import com.holderzone.saas.store.dto.kds.req.KdsPrintRecordReqDTO;
import com.holderzone.saas.store.kds.entity.query.PrintRecordQuery;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.stereotype.Component;

@Component
@Mapper(componentModel = "spring")
public interface KdsPrintMapstruct {

    KdsPrinterDO fromCreateReq(KdsPrinterCreateReqDTO kdsPrinterCreateReqDTO);

    @Mapping(source = "printerGuid", target = "guid")
    KdsPrinterDO fromUpdateReq(KdsPrinterUpdateReqDTO kdsPrinterUpdateReqDTO);

    @Mapping(source = "printerGuid", target = "guid")
    KdsPrinterDO fromDeleteReq(KdsPrinterDeleteReqDTO kdsPrinterDeleteReqDTO);

    @Mapping(source = "printerGuid", target = "guid")
    KdsPrinterDO fromBindUnbindReq(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO);

    @Mapping(source = "guid", target = "printerGuid")
    KdsPrinterRespDTO toKdsPrinterResp(KdsPrinterDO kdsPrinterDeleteReqDTO);

    PrintRecordQuery printRecordReqToQuery(KdsPrintRecordReqDTO kdsPrintRecordReqDTO);
}
