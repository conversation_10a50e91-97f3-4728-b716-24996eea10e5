package com.holderzone.saas.store.kds.service;

import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;

import java.util.Collection;

public interface KdsStatusPushService {

    void statusChanged(int tradeMode, Collection<DeviceConfigDO> deviceConfigInSql);

    void statusChanged(String enterpriseGuid, String storeGuid, String deviceId, String voiceMsg);

    /**
     * 语音播报
     */
    void voiceBroadcast(String enterpriseGuid, String storeGuid, String deviceId, String voiceMsg);

}
