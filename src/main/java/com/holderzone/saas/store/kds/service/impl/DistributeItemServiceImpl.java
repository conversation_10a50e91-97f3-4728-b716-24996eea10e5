package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.kds.req.DeviceQueryReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdDstItemBindDTO;
import com.holderzone.saas.store.dto.kds.resp.DistributeItemDTO;
import com.holderzone.saas.store.dto.kds.resp.DstBindStatusRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DistributeItemDO;
import com.holderzone.saas.store.kds.mapper.DistributeItemMapper;
import com.holderzone.saas.store.kds.service.DistributeItemService;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机单据管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class DistributeItemServiceImpl extends ServiceImpl<DistributeItemMapper, DistributeItemDO> implements DistributeItemService {

    private final DistributedIdService distributedIdService;

    @Autowired
    public DistributeItemServiceImpl(DistributedIdService distributedIdService) {
        this.distributedIdService = distributedIdService;
    }

    @Override
    public DstBindStatusRespDTO queryItemBindingPreview(DeviceQueryReqDTO deviceQueryReqDTO) { // 出堂口预览绑定状态，itemGuid可能相同，skuGuid不同
        List<DistributeItemDO> list = list(new LambdaQueryWrapper<DistributeItemDO>()
                .select(DistributeItemDO::getItemGuid, DistributeItemDO::getSkuGuid)
                .eq(DistributeItemDO::getStoreGuid, deviceQueryReqDTO.getStoreGuid())
                .eq(DistributeItemDO::getDeviceId, deviceQueryReqDTO.getDeviceId()));
        int count = list.stream().map(DistributeItemDO::getItemGuid).collect(toSet()).size();
        return new DstBindStatusRespDTO().setBoundItemCount(count);
    }

    @Override
    public List<DistributeItemDO> queryBoundItemOfStore(String storeGuid) { // 查询门店下绑定的菜品，查询 sku
        return list(new LambdaQueryWrapper<DistributeItemDO>()
                .select(DistributeItemDO::getDeviceId,
                        DistributeItemDO::getSkuGuid)
                .eq(DistributeItemDO::getStoreGuid, storeGuid));
    }

    @Override
    public List<DistributeItemDO> queryBoundItemOfDevice(String storeGuid, String deviceId) { // 查询设备绑定的菜品，查询 sku
        return list(new LambdaQueryWrapper<DistributeItemDO>()
                .select(DistributeItemDO::getDeviceId,
                        DistributeItemDO::getSkuGuid)
                .eq(DistributeItemDO::getStoreGuid, storeGuid)
                .eq(DistributeItemDO::getDeviceId, deviceId));
    }

    @Override
    public List<String> queryOccupiedDeviceId(String storeGuid, String deviceId) { // 返回绑定了sku 的 deviceId 集合，except current deviceId

        // 当前设备绑定的菜
        List<DistributeItemDO> distributeItemInSql = list(new LambdaQueryWrapper<DistributeItemDO>()
                .select(DistributeItemDO::getSkuGuid)
                .eq(DistributeItemDO::getStoreGuid, storeGuid)
                .eq(DistributeItemDO::getDeviceId, deviceId));

        if (CollectionUtils.isEmpty(distributeItemInSql)) return Collections.emptyList();

        // 当前设备绑定的菜的 skuGuid 集合
        List<String> occupiedSkuGuid = distributeItemInSql.stream()
                .map(DistributeItemDO::getSkuGuid).collect(Collectors.toList());

        // 在已经被绑定的sku集合中，查询未被当前设备绑定的sku
        distributeItemInSql = list(new LambdaQueryWrapper<DistributeItemDO>()
                .select(DistributeItemDO::getDeviceId)
                .eq(DistributeItemDO::getStoreGuid, storeGuid)
                .ne(DistributeItemDO::getDeviceId, deviceId)
                .in(DistributeItemDO::getSkuGuid, occupiedSkuGuid));

        return distributeItemInSql.stream().map(DistributeItemDO::getDeviceId).collect(Collectors.toList());
    }

    @Override
    public List<String> queryBoundSkuGuidOfDevice(String storeGuid, String deviceId) { // 查询被当前设备绑定的sku
        List<DistributeItemDO> distributeSkuInSql = list(new LambdaQueryWrapper<DistributeItemDO>()
                .select(DistributeItemDO::getSkuGuid)
                .eq(DistributeItemDO::getStoreGuid, storeGuid)
                .eq(DistributeItemDO::getDeviceId, deviceId));
        if (CollectionUtils.isEmpty(distributeSkuInSql)) return Collections.emptyList();
        return distributeSkuInSql.stream().map(DistributeItemDO::getSkuGuid).collect(Collectors.toList());
    }

    @Override
    public List<String> queryBoundSkuGuidOfDeviceList(String storeGuid, List<String> occupiedDeviceId) { // 查询出被设备绑定的sku集合
        if (CollectionUtils.isEmpty(occupiedDeviceId)) return Collections.emptyList();
        List<DistributeItemDO> distributeSkuInSql = list(new LambdaQueryWrapper<DistributeItemDO>()
                .select(DistributeItemDO::getSkuGuid)
                .eq(DistributeItemDO::getStoreGuid, storeGuid)
                .in(DistributeItemDO::getDeviceId, occupiedDeviceId));
        if (CollectionUtils.isEmpty(distributeSkuInSql)) return Collections.emptyList();
        return distributeSkuInSql.stream().map(DistributeItemDO::getSkuGuid).collect(Collectors.toList());
    }

    @Override
    public void simpleSaveBatchSku(String storeGuid, String deviceId, List<PrdDstItemBindDTO> toBeBoundSkuList) { // 存储待绑定的sku集合
        if (CollectionUtils.isEmpty(toBeBoundSkuList)) return;
        List<String> guids = distributedIdService.nextBatchDstItemGuid(toBeBoundSkuList.size());
        List<DistributeItemDO> distributeItem2Insert = toBeBoundSkuList.stream()
                .map(prdDstItemBindDTO -> {
                    DistributeItemDO distributeItemDO = new DistributeItemDO();
                    distributeItemDO.setGuid(guids.remove(guids.size() - 1));
                    distributeItemDO.setStoreGuid(storeGuid);
                    distributeItemDO.setDeviceId(deviceId);
                    distributeItemDO.setItemGuid(prdDstItemBindDTO.getItemGuid());
                    distributeItemDO.setSkuGuid(prdDstItemBindDTO.getSkuGuid());
                    return distributeItemDO;
                })
                .collect(Collectors.toList());
        saveBatch(distributeItem2Insert);
    }

    @Override
    public void simpleRemoveBatchSku(String storeGuid, String deviceId, List<String> toBeRemoveSkuGuid) { // 删除待解绑的sku
        if (CollectionUtils.isEmpty(toBeRemoveSkuGuid)) return;
        remove(new LambdaQueryWrapper<DistributeItemDO>()
                .eq(DistributeItemDO::getStoreGuid, storeGuid)
                .eq(DistributeItemDO::getDeviceId, deviceId)
                .in(DistributeItemDO::getSkuGuid, toBeRemoveSkuGuid));
    }

    @Override
    public void reInitialize(String storeGuid, String deviceId) { // 初始化出堂口，删除相关菜品
        remove(new LambdaQueryWrapper<DistributeItemDO>()
                .eq(DistributeItemDO::getStoreGuid, storeGuid)
                .eq(DistributeItemDO::getDeviceId, deviceId));
    }

    @Override
    public Map<String, List<String>> queryDstSkuMap(String storeGuid, List<String> skuGuidList) { // 以 deviceId，对传入的sku分组
        if (CollectionUtils.isEmpty(skuGuidList)) return Collections.emptyMap();
        return list(new LambdaQueryWrapper<DistributeItemDO>()
                .select(DistributeItemDO::getDeviceId,
                        DistributeItemDO::getSkuGuid)
                .in(DistributeItemDO::getSkuGuid, skuGuidList)
                .eq(DistributeItemDO::getStoreGuid, storeGuid)).stream()
                .collect(groupingBy(DistributeItemDO::getDeviceId, mapping(DistributeItemDO::getSkuGuid, toList())));

    }

    @Override
    public List<DistributeItemDTO> queryDistributeItemBySku(SingleDataDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getDatas()) || StringUtils.isEmpty(reqDTO.getData())) {
            log.warn("入参有误返空");
            return Lists.newArrayList();
        }
        List<DistributeItemDO> itemDOList = this.list(new LambdaQueryWrapper<DistributeItemDO>()
                .in(DistributeItemDO::getSkuGuid, reqDTO.getDatas())
                .eq(DistributeItemDO::getStoreGuid, reqDTO.getData()));
        return getItemDTOList(itemDOList);
    }

    private List<DistributeItemDTO> getItemDTOList(List<DistributeItemDO> itemDOList) {
        if (CollectionUtils.isEmpty(itemDOList)) {
            log.warn("查询为空返回");
            return Lists.newArrayList();
        }
        List<DistributeItemDTO> itemDTOList = new ArrayList<>();
        itemDOList.forEach(itemDO -> {
            DistributeItemDTO itemDTO = new DistributeItemDTO();
            itemDTO.setStoreGuid(itemDO.getStoreGuid());
            itemDTO.setDeviceId(itemDO.getDeviceId());
            itemDTO.setItemGuid(itemDO.getItemGuid());
            itemDTO.setSkuGuid(itemDO.getSkuGuid());
            itemDTOList.add(itemDTO);
        });
        return itemDTOList;
    }
}
