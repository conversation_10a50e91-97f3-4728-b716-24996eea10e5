package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@TableName("hsk_kitchen_item")
@EqualsAndHashCode(callSuper = true)
public class KitchenItemDO extends BaseDO {

    /**
     * 制作点堂口Guid
     */
    private String storeGuid;

    /**
     * 制作点堂口Guid
     */
    private String pointGuid;

    /**
     * 制作点堂口Guid
     */
    private String prdDeviceId;

    /**
     * 出堂口设备Guid
     */
    private String dstDeviceId;

    /**
     * 屏幕显示分类
     * 0b00001111
     * 低0位 外卖 1
     * 低1位 快餐 2
     * 低2位 正餐 4
     * 低3位 全部 8
     * <p>
     * tradeMode
     * 0：正餐
     * 1：快餐
     * <p>
     * 映射关系如下
     * tradeMode 0 -> displayType 4
     * tradeMode 1 -> displayType 2
     * tradeMode 2 -> displayType 1
     */
    private Integer displayType;

    /**
     * 订单Guid
     * 并台情况下，订单拆单显示
     */
    private String orderGuid;

    /**
     * 正餐：正餐
     * 快餐：快餐
     * 外卖：饿了么|美团
     */
    private String orderDesc;

    /**
     * 正餐：订单号
     * 快餐：订单号
     * 外卖：订单号
     */
    private String orderNumber;

    /**
     * 正餐：桌台
     * 快餐：牌号
     * 外卖：日流水号
     */
    private String orderSerialNo;

    /**
     * 整单备注
     */
    private String orderRemark;

    /**
     * 区域Guid
     */
    private String areaGuid;

    /**
     * 桌台Guid
     */
    private String tableGuid;

    /**
     * 订单下商品唯一Guid，
     * 不同于guid
     * 不同于itemGuid
     * 该字段标识唯一的一次菜品(批量)操作
     * <p>
     * 比如：白菜x10，则
     * guid表示这个1份的标识
     * itemGuid表示这个item类型的标识
     * orderItemGuid表示这个10份的标识
     */
    private String orderItemGuid;

    /**
     * 商品Guid
     */
    private String itemGuid;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * skuGuid
     */
    private String skuGuid;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * sku单位
     */
    private String skuUnit;

    /**
     * 是否是称重商品
     */
    private Boolean isWeight;

    /**
     * 当前商品数量
     * 称重       具体重量
     * 非称重     具体份数
     */
    private BigDecimal currentCount;

    /**
     * 已退商品数量
     */
    @Deprecated
    private BigDecimal returnCount;

    /**
     * 商品属性MD5，ItemAttrMd5BO转Json转Md5
     */
    private String itemAttrMd5;

    /**
     * 菜品备注
     */
    private String itemRemark;

    /**
     * 超时阈值
     * 单位：分钟
     */
    private Integer timeout;

    /**
     * 催促次数
     */
    @Deprecated
    private Integer urgedTimes;

    /**
     * 商品状态
     * 1.即起
     * 2.挂起
     * 3.叫起
     * 8.已上菜(已划菜)
     * 9.预定
     * 99.退菜
     */
    private Integer itemState;

    /**
     * 厨房商品状态
     * 4.待制作
     * 5.制作中
     * 6.待出堂
     * 7.已出堂
     */
    private Integer kitchenState;

    /**
     * 新订单自动打印
     * 开启后，点击制作不会再次打印；关闭则需要手动点击制作后打印
     */
    private Boolean isPrintAutomatic;

    /**
     * 广义下单时间
     * 即起菜品：prepareTime
     * 挂起菜品：hangUpTime
     * 叫起菜品：callUpTime
     */
    private LocalDateTime orderSortTime;

    /**
     * 准备时间
     */
    private LocalDateTime prepareTime;

    /**
     * 挂起时间（同准备时间，区别是item进厨房时状态是挂起）
     */
    private LocalDateTime hangUpTime;

    /**
     * 叫起时间
     */
    private LocalDateTime callUpTime;

    /**
     * 催菜时间
     */
    private LocalDateTime urgedTime;

    /**
     * 制作人Guid
     */
    private String cookStaffGuid;

    /**
     * 制作人姓名
     */
    private String cookStaffName;

    /**
     * 制作时间
     */
    private LocalDateTime cookTime;

    /**
     * 完成人Guid
     */
    private String completeStaffGuid;

    /**
     * 完成人姓名
     */
    private String completeStaffName;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 出堂人Guid
     */
    private String distributeStaffGuid;

    /**
     * 出堂人姓名
     */
    private String distributeStaffName;

    /**
     * 出堂时间
     */
    private LocalDateTime distributeTime;

    /**
     * 撤销出堂人Guid
     */
    private String cancelDstStaffGuid;

    /**
     * 撤销出堂人姓名
     */
    private String cancelDstStaffName;

    /**
     * 撤销出堂时间
     */
    private LocalDateTime cancelDstTime;
    /**
     * 延迟时间，单位：分钟
     */
    private Integer delayTimeMinutes;

    /**
     * 显示时间
     */
    private LocalDateTime displayTime;
    /**
     * 菜品批次
     */
    private Integer batch;
    /**
     * 菜品序号
     */
    private Integer sort;
    /**
     * 规则类型 0显示规则 1菜品汇总
     */
    private Integer displayRuleType;

    /**
     * 原商品Guid
     */
    private String originalItemGuid;

    /**
     * 原skuGuid
     */
    private String originalSkuGuid;

    /**
     * 原商品全称
     */
    private String originalItemSkuName;

    /**
     * 加菜批次
     * 0表示首次点单，不算加菜
     */
    private Integer addItemBatch;

    /**
     * 首次加菜时间
     */
    private LocalDateTime firstAddItemTime;

    /**
     * 加菜时间
     */
    private LocalDateTime addItemTime;

    /**
     * 制作口设备
     */
    @TableField(exist = false)
    private List<KitchenItemDeviceDO> prdPointDevices;

    /**
     * 出堂口设备
     */
    @TableField(exist = false)
    private List<KitchenItemDeviceDO> dstPointDevices;
}
