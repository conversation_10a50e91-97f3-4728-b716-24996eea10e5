package com.holderzone.saas.store.kds.utils;

import com.holderzone.saas.store.dto.common.PageDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleItemSortDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstRespDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.kds.entity.bo.PrdRespBO;
import com.holderzone.saas.store.kds.entity.enums.DisplayRuleType;
import com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class KitchenItemUtils {

    public static List<KitchenItemReadDO> filterByDisplayType(List<KitchenItemReadDO> kitchenItemReadInSql, int displayRuleType) {
        return kitchenItemReadInSql.stream()
                .peek(i -> {
                    if (i.getDisplayRuleType() == null || i.getDisplayRuleType() == -1) {
                        i.setDisplayRuleType(DisplayRuleType.BATCH);
                    }
                })
                .filter(k -> k.getDisplayRuleType() == displayRuleType)
                .collect(Collectors.toList());
    }

    public static List<KitchenItemReadDO> filterByDisplayTime(List<KitchenItemReadDO> kitchenItemReadInSql) {
        if (CollectionUtils.isEmpty(kitchenItemReadInSql)) {
            return new ArrayList<>();
        }
        return kitchenItemReadInSql.stream()
                .filter(i -> {
                    if (i.getUrgedTime() != null) {
                        return true;
                    }
                    return i.getDisplayTime() == null || i.getDisplayTime().isBefore(LocalDateTime.now());
                })
                .collect(Collectors.toList());
    }

    public static PrdDstRespDTO queryPrdStatusBySummaryItemOrder(PageDTO pageDTO,
                                                                 List<KitchenItemReadDO> kitchenItemReadInSql,
                                                                 PrdRespBO.PrdRespBOBuilder builder,
                                                                 PrdDstRespDTO prdDstRespDTO
    ) {
        PrdRespBO prdRespBO = builder.isOrderMode(true).isAllType(true).build();
        List<KitchenItemReadDO> orderItems = filterByDisplayType(kitchenItemReadInSql, DisplayRuleType.BATCH);
        prdDstRespDTO.setOrders(prdRespBO.getPrdDstOrderPage(pageDTO, orderItems));
        pageDTO.setPageSize(100);

        prdRespBO = builder.isOrderMode(false).isAllType(true).build();
        List<KitchenItemReadDO> items = filterByDisplayType(kitchenItemReadInSql, DisplayRuleType.SUMMERY);
        prdDstRespDTO.setItems(prdRespBO.getPrdDstItemPage(pageDTO, items));

        return prdDstRespDTO;
    }

    public static List<KitchenItemReadDO> setAddItemBatch(List<KitchenItemReadDO> items,
                                                          DisplayRuleItemSortDTO itemSortDTO) {
        items.forEach(item -> {
            int addItemBatch = BooleanEnum.FALSE.getCode();
            if (!ObjectUtils.isEmpty(item.getAddItemBatch()) && item.getAddItemBatch() > 0
                    && (!ObjectUtils.isEmpty(itemSortDTO) && itemSortDTO.getItemSortType().equals(BooleanEnum.TRUE.getCode()))) {
                if (itemSortDTO.getItemDisplayType().equals(BooleanEnum.TRUE.getCode())) {
                    Duration duration = Duration.between(item.getFirstAddItemTime(), item.getAddItemTime());
                    long minutes = duration.toMinutes();
                    if (minutes > itemSortDTO.getItemIntervalTime()) {
                        addItemBatch = BooleanEnum.TRUE.getCode();
                    }
                } else {
                    addItemBatch = BooleanEnum.TRUE.getCode();
                }
            }
            item.setAddItemBatch(addItemBatch);
        });
        return items;
    }

}
