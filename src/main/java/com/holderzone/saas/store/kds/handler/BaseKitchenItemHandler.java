package com.holderzone.saas.store.kds.handler;


import com.holderzone.saas.store.dto.kds.req.ItemChangesReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemPrepareReqDTO;
import com.holderzone.saas.store.kds.bo.KitchenItemChangesBO;
import com.holderzone.saas.store.kds.bo.KitchenItemPrepareBO;

public interface BaseKitchenItemHandler {

    /**
     * 入厨
     */
    KitchenItemPrepareBO prepare(ItemPrepareReqDTO itemPrepareReqDTO);

    /**
     * 换菜
     */
    KitchenItemChangesBO changes(ItemChangesReqDTO itemChangesReqDTO);
}
