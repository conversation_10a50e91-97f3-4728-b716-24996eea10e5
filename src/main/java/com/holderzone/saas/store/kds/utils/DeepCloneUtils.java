package com.holderzone.saas.store.kds.utils;

import com.holderzone.framework.exception.unchecked.BusinessException;

import java.io.*;

@SuppressWarnings("Duplicates")
public final class DeepCloneUtils {

    private DeepCloneUtils() {
    }

    public static Object cloneObject(Object obj) {
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        ObjectInputStream in = null;
        ByteArrayInputStream byteIn = null;
        ObjectOutputStream out = null;
        try {
            out = new ObjectOutputStream(byteOut);
            out.writeObject(obj);
            byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            in = new ObjectInputStream(byteIn);
            return in.readObject();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
                if (out != null) {
                    out.close();
                }
                if (byteIn != null) {
                    byteIn.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        throw new BusinessException("深拷贝出错");
    }
}
