package com.holderzone.saas.store.kds.event;

import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.DeviceQueryReqDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.kds.constant.RocketMqConfig;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.kds.service.DeviceConfigService;
import com.holderzone.saas.store.kds.service.QueueConfigService;
import com.holderzone.saas.store.kds.utils.ThrowableExtUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/09/18 20:47
 * @description
 * @program holder-saas-store-takeaway
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.DOWNSTREAM_DEVICE_TOPIC,
        tags = {
                RocketMqConfig.DOWNSTREAM_DEVICE_BIND_TAG,
                RocketMqConfig.DOWNSTREAM_DEVICE_UNBIND_TAG,
        },
        consumerGroup = RocketMqConfig.DOWNSTREAM_DEVICE_BINDING_GROUP)
public class DeviceBindingListener extends AbstractRocketMqConsumer<RocketMqTopic, StoreDeviceDTO> {

    private final DeviceConfigService deviceConfigService;

    private final QueueConfigService queueConfigService;

    @Autowired
    public DeviceBindingListener(DeviceConfigService deviceConfigService, QueueConfigService queueConfigService) {
        this.deviceConfigService = deviceConfigService;
        this.queueConfigService = queueConfigService;
    }

    @Override
    public boolean consumeMsg(StoreDeviceDTO storeDeviceDTO, MessageExt messageExt) {
        UserContextUtils.put(messageExt.getProperty(RocketMqConfig.DOWNSTREAM_CONTEXT));
        try {
            EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            switch (messageExt.getTags()) {
                case RocketMqConfig.DOWNSTREAM_DEVICE_BIND_TAG: {
                    if (BaseDeviceTypeEnum.KQS.getCode()
                            != storeDeviceDTO.getDeviceType()) {
                        return true;
                    }
                    queueConfigService.create(storeDeviceDTO);
                    break;
                }
                case RocketMqConfig.DOWNSTREAM_DEVICE_UNBIND_TAG: {
                    DeviceQueryReqDTO deviceQueryReqDTO = new DeviceQueryReqDTO();
                    deviceQueryReqDTO.setStoreGuid(storeDeviceDTO.getStoreGuid());
                    deviceQueryReqDTO.setDeviceId(storeDeviceDTO.getDeviceGuid());
                    if (log.isInfoEnabled()) {
                        log.info("初始化KDS设备入参：{}", JacksonUtils.writeValueAsString(deviceQueryReqDTO));
                    }
                    deviceConfigService.initialize(deviceQueryReqDTO);
                    queueConfigService.delete(storeDeviceDTO);
                    break;
                }
                default:
                    break;
            }

        } catch (Exception e) {
            log.error("初始化KDS设备消费失败：{}", ThrowableExtUtils.asStringIfAbsent(e));
            return false;
        } finally {
            EnterpriseIdentifier.remove();
        }
        return true;
    }
}
