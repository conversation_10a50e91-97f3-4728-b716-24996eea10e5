package com.holderzone.saas.store.kds.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleQueryDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayItemRespDTO;
import com.holderzone.saas.store.kds.service.DisplayItemService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @description 显示商品表
 * @date 2021/1/27 16:45
 */
@Slf4j
@RestController
@RequestMapping("/display_item")
@AllArgsConstructor
public class DisplayItemController {

    private final DisplayItemService displayItemService;

    /**
     * 查询kds商品列表
     *
     * @param reqDTO DisplayRuleQueryDTO
     * @return List<DisplayItemRespDTO>
     */
    @ApiOperation(value = "商品列表")
    @PostMapping("/item_list")
    public List<DisplayItemRespDTO> listItem(@RequestBody DisplayRuleQueryDTO reqDTO) {
        log.info("查询商品列表入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return displayItemService.listItem(reqDTO);
    }
}
