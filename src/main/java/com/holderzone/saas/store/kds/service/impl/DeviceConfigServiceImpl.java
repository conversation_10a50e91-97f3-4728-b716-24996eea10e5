package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.DeviceStatusRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.entity.enums.PointModeEnum;
import com.holderzone.saas.store.kds.mapper.DeviceConfigMapper;
import com.holderzone.saas.store.kds.mapstruct.DeviceConfigMapstruct;
import com.holderzone.saas.store.kds.service.*;
import com.holderzone.saas.store.kds.service.rpc.StoreClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机单据管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class DeviceConfigServiceImpl extends ServiceImpl<DeviceConfigMapper, DeviceConfigDO> implements DeviceConfigService {

    private final DeviceConfigMapstruct deviceConfigMapstruct;

    private final DistributeService distributeService;

    private final ProductionPointService productionPointService;

    private final DisplayRepeatItemService displayRepeatItemService;

    private final DeviceBindItemGroupService deviceBindItemGroupService;

    private final StoreClientService storeClientService;

    @Autowired
    public DeviceConfigServiceImpl(DeviceConfigMapstruct deviceConfigMapstruct,
                                   @Lazy DistributeService distributeService,
                                   @Lazy ProductionPointService productionPointService,
                                   DisplayRepeatItemService displayRepeatItemService,
                                   DeviceBindItemGroupService deviceBindItemGroupService,
                                   StoreClientService storeClientService) {
        this.deviceConfigMapstruct = deviceConfigMapstruct;
        this.distributeService = distributeService;
        this.productionPointService = productionPointService;
        this.displayRepeatItemService = displayRepeatItemService;
        this.deviceBindItemGroupService = deviceBindItemGroupService;
        this.storeClientService = storeClientService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceStatusRespDTO create(DeviceCreateReqDTO deviceCreateReqDTO) {
        DeviceConfigDO deviceConfigDO = deviceConfigMapstruct.fromCreateReq(deviceCreateReqDTO);
        assertDeviceNameAvailable(deviceConfigDO);
        String storeGuid = deviceCreateReqDTO.getStoreGuid();
        String deviceId = deviceCreateReqDTO.getDeviceId();
        Wrapper<DeviceConfigDO> wrapper = deviceConfigWrapper(deviceConfigDO.getStoreGuid(), deviceConfigDO.getGuid());
        DeviceConfigDO deviceConfigInSql = getOne(wrapper);

        // 创建设备时，设备不存在
        if (deviceConfigInSql == null) {
            save(deviceConfigDO);
        } else { // 设备存在，更新信息
            PointModeEnum pointModeInSql = PointModeEnum.ofMode(deviceConfigInSql.getPointMode());
            PointModeEnum pointModeNewReq = PointModeEnum.ofMode(deviceConfigDO.getPointMode());
            // 判断数据库中设备的设备模式与请求实体的设备模式是否相同
            if (pointModeInSql == pointModeNewReq) {
                update(deviceConfigDO, wrapper);
            } else {
                if (pointModeInSql == PointModeEnum.PRODUCTION
                        && pointModeNewReq == PointModeEnum.DISTRIBUTE) {
                    // 删除设备下所有制作点、设备下的所有菜品
                    productionPointService.reInitialize(storeGuid, deviceId);
                }
                if (pointModeInSql == PointModeEnum.DISTRIBUTE
                        && pointModeNewReq == PointModeEnum.PRODUCTION) {

                    // 删除堂口、堂口绑定的区域
                    distributeService.reInitialize(storeGuid, deviceId);
                }
                // 最后删除该设备信息
                remove(wrapper);
                save(deviceConfigDO);
            }
        }
        return query(new DeviceQueryReqDTO(storeGuid, deviceId));
    }

    @Override
    public DeviceStatusRespDTO query(DeviceQueryReqDTO deviceQueryReqDTO) {
        DeviceConfigDO deviceConfigInSql = queryDeviceByGuid(deviceQueryReqDTO.getStoreGuid(), deviceQueryReqDTO.getDeviceId());
        DeviceStatusRespDTO deviceStatusRespDTO = deviceConfigMapstruct.toStatusResp(deviceConfigInSql);
        switch (PointModeEnum.ofMode(deviceConfigInSql.getPointMode())) {
            case PRODUCTION: {
                DevicePrdConfDTO devicePrdConfDTO = deviceConfigMapstruct.toPrdConfResp(deviceConfigInSql);
                deviceStatusRespDTO.setDevicePrdConfDTO(devicePrdConfDTO);
                break;
            }
            case DISTRIBUTE: {
                DeviceDstConfDTO deviceDstConfDTO = deviceConfigMapstruct.toDstConfResp(deviceConfigInSql);
                deviceStatusRespDTO.setDeviceDstConfDTO(deviceDstConfDTO);
                break;
            }
            default:
                break;
        }
        // 是否允许重复绑定菜品
        Boolean allowRepeatFlag = displayRepeatItemService.queryAllowRepeatFlag(deviceQueryReqDTO.getStoreGuid());
        deviceStatusRespDTO.setAllowRepeatFlag(allowRepeatFlag);
        // 查询品牌销售模式
        BrandDTO brandDTO = storeClientService.queryBrandByStoreGuid(deviceQueryReqDTO.getStoreGuid());
        deviceStatusRespDTO.setSalesModel(Optional.ofNullable(brandDTO).orElse(new BrandDTO()).getSalesModel());
        return deviceStatusRespDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initialize(DeviceQueryReqDTO deviceQueryReqDTO) { // 设备初始化
        String deviceId = deviceQueryReqDTO.getDeviceId();
        String storeGuid = deviceQueryReqDTO.getStoreGuid();
        DeviceConfigDO deviceConfigInSql = getOne(deviceConfigWrapper(storeGuid, deviceId));
        if (deviceConfigInSql == null) {
            return;
        }
        PointModeEnum pointModeInSql = PointModeEnum.ofMode(deviceConfigInSql.getPointMode());
        if (pointModeInSql == PointModeEnum.PRODUCTION) {
            productionPointService.reInitialize(storeGuid, deviceId);
        }
        if (pointModeInSql == PointModeEnum.DISTRIBUTE) {
            distributeService.reInitialize(storeGuid, deviceId);
        }
        remove(deviceConfigWrapper(storeGuid, deviceId));
        deviceBindItemGroupService.reInitialize(storeGuid, deviceId);
    }

    @Override
    public void assertThatDeviceExists(String storeGuid, String deviceId) {
        if (0 == count(deviceConfigWrapper(storeGuid, deviceId))) {
            log.error("根据 storeGuid: {}，deviceId: {} 未找到设备", storeGuid, deviceId);
            throw new BusinessException("设备不存在");
        }
    }

    @Override
    public DeviceConfigDO queryDeviceByGuid(String storeGuid, String deviceId) {
        DeviceConfigDO deviceConfigInSql = getOne(deviceConfigWrapper(storeGuid, deviceId));
        if (deviceConfigInSql == null) {
            log.error("根据 storeGuid:{}，deviceId: {} 未找到设备", storeGuid, deviceId);
            throw new BusinessException("设备不存在");
        }
        return deviceConfigInSql;
    }

    @Override
    public DeviceConfigDO queryPrdDeviceByGuid(String storeGuid, String deviceId) {
        DeviceConfigDO deviceConfigInSql = getOne(new LambdaQueryWrapper<DeviceConfigDO>()
                .select(DeviceConfigDO::getGuid,
                        DeviceConfigDO::getDisplayMode,
                        DeviceConfigDO::getIsPrintPerOrder,
                        DeviceConfigDO::getIsShowHangedItem,
                        DeviceConfigDO::getIsProduceHangedItem,
                        DeviceConfigDO::getIsDisplayByMaxCopied)
                .eq(DeviceConfigDO::getStoreGuid, storeGuid)
                .eq(DeviceConfigDO::getGuid, deviceId));
        if (deviceConfigInSql == null) {
            log.error("根据 storeGuid:{}，deviceId: {} 未找到设备", storeGuid, deviceId);
            throw new BusinessException("设备不存在");
        }
        return deviceConfigInSql;
    }

    @Override
    public Map<String, DeviceConfigDO> listPrdDeviceByGuid(String storeGuid, List<String> deviceIdList) {
        if (CollectionUtils.isEmpty(deviceIdList)) return Collections.emptyMap();
        List<DeviceConfigDO> list = list(new LambdaQueryWrapper<DeviceConfigDO>()
                .select(DeviceConfigDO::getGuid,
                        DeviceConfigDO::getDisplayMode,
                        DeviceConfigDO::getIsShowHangedItem,
                        DeviceConfigDO::getIsProduceHangedItem,
                        DeviceConfigDO::getIsDisplayByMaxCopied,
                        DeviceConfigDO::getIsPrintAutomatic,
                        DeviceConfigDO::getIsPrintPerOrder,
                        DeviceConfigDO::getIsDineInOrderNotice,
                        DeviceConfigDO::getIsSnackOrderNotice,
                        DeviceConfigDO::getIsTakeoutOrderNotice,
                        DeviceConfigDO::getIsFilterTakeOutOrder
                )
                .eq(DeviceConfigDO::getStoreGuid, storeGuid)
                .in(DeviceConfigDO::getGuid, deviceIdList));
        return list.stream().collect(Collectors.toMap(DeviceConfigDO::getGuid, Function.identity()));
    }

    @Override
    public DeviceConfigDO queryDstDispatchAsPrintByGuid(String storeGuid, String deviceId) {
        DeviceConfigDO deviceConfigInSql = getOne(new LambdaQueryWrapper<DeviceConfigDO>()
                .select(DeviceConfigDO::getGuid,
                        DeviceConfigDO::getIsDispatchAsPrint)
                .eq(DeviceConfigDO::getStoreGuid, storeGuid)
                .eq(DeviceConfigDO::getGuid, deviceId));
        if (deviceConfigInSql == null) {
            log.error("根据 deviceId: {} 未找到设备", deviceId);
            throw new BusinessException("设备不存在");
        }
        return deviceConfigInSql;
    }

    @Override
    public DeviceConfigDO queryDstDisplayTypeByGuid(String storeGuid, String deviceId) {
        DeviceConfigDO deviceConfigInSql = getOne(new LambdaQueryWrapper<DeviceConfigDO>()
                .select(DeviceConfigDO::getGuid,
                        DeviceConfigDO::getDisplayType)
                .eq(DeviceConfigDO::getStoreGuid, storeGuid)
                .eq(DeviceConfigDO::getGuid, deviceId));
        if (deviceConfigInSql == null) {
            log.error("根据 deviceId: {} 未找到设备", deviceId);
            throw new BusinessException("设备不存在");
        }
        return deviceConfigInSql;
    }

    @Override
    public List<DeviceConfigDO> listDeviceOfStore(String storeGuid) { // 返回当前门店下的设备信息
        return list(new LambdaQueryWrapper<DeviceConfigDO>()
                .select(DeviceConfigDO::getGuid, DeviceConfigDO::getName, DeviceConfigDO::getPointMode)
                .eq(DeviceConfigDO::getStoreGuid, storeGuid));
    }

    @Override
    public Map<String, String> getDeviceNameMapOfStore(String storeGuid) { // 返回当前门店下 设备ID -> 设备 name 的 map
        List<DeviceConfigDO> deviceConfigInSql = listDeviceOfStore(storeGuid);
        if (CollectionUtils.isEmpty(deviceConfigInSql)) return Collections.emptyMap();
        return deviceConfigInSql.stream().collect(Collectors.toMap(DeviceConfigDO::getGuid, DeviceConfigDO::getName));
    }

    @Override
    public List<DeviceConfigDO> filterPointModeByDeviceIds(Integer pointMode, List<String> deviceIdList) {
        return list(new LambdaQueryWrapper<DeviceConfigDO>()
                .select(DeviceConfigDO::getGuid)
                .eq(DeviceConfigDO::getPointMode, pointMode)
                .in(DeviceConfigDO::getGuid, deviceIdList));
    }

    @Override
    public void updateBasic(String storeGuid, String deviceId, Integer displayMode, Integer itemDisplayType) { // 设置显示模式
        assertThatDeviceExists(storeGuid, deviceId);
        update(new DeviceConfigDO().setDisplayMode(displayMode).setItemDisplayType(itemDisplayType),
                deviceConfigWrapper(storeGuid, deviceId));
    }

    @Override
    public void updatePrdAdvanced(String storeGuid, String deviceId, DevicePrdConfDTO devicePrdConfDTO) { // 制作口初始化完成
        assertThatDeviceExists(storeGuid, deviceId);
        // 默认开启,兼容老包
        if (ObjectUtils.isEmpty(devicePrdConfDTO.getIsCallUpNotice())) {
            devicePrdConfDTO.setIsCallUpNotice(Boolean.TRUE);
        }
        DeviceConfigDO deviceConfigDO = deviceConfigMapstruct.fromDevicePrdConfReq(devicePrdConfDTO);
        deviceConfigDO.setIsInitialized(true);
        update(deviceConfigDO, deviceConfigWrapper(storeGuid, deviceId));
    }

    @Override
    public void updateDstAdvanced(String storeGuid, String deviceId, DeviceDstConfDTO deviceDstConfDTO) { // 出堂口初始化完成
        assertThatDeviceExists(storeGuid, deviceId);
        DeviceConfigDO deviceConfigDO = deviceConfigMapstruct.fromDeviceDstConfReq(deviceDstConfDTO);
        deviceConfigDO.setIsInitialized(true);
        update(deviceConfigDO, deviceConfigWrapper(storeGuid, deviceId));
    }

    @Override
    public String queryBoundPrinterGuidByDeviceId(String storeGuid, String deviceId) { // 查询设备绑定的打印机的Guid
        return queryDeviceByGuid(storeGuid, deviceId).getPrinterGuid();
    }

    @Override
    public void bindPrinter(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) { // 绑定打印机，且打印机不能重复，且只能有一台
        String storeGuid = kdsPrinterBindUnbindReqDTO.getStoreGuid();
        String deviceId = kdsPrinterBindUnbindReqDTO.getDeviceId();
        String printerGuid = kdsPrinterBindUnbindReqDTO.getPrinterGuid();
        DeviceConfigDO deviceConfigInSql = queryDeviceByGuid(storeGuid, deviceId);
        if (StringUtils.hasText(deviceConfigInSql.getPrinterGuid())
                && !deviceConfigInSql.getPrinterGuid().equalsIgnoreCase(printerGuid)) {
            throw new BusinessException("一台设备只能绑定一台打印机，请解绑原打印机后重试！");
        }
        DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setPrinterGuid(printerGuid);
        update(deviceConfigDO, deviceConfigWrapper(storeGuid, deviceId));
    }

    @Override
    public void rebindPrinter(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) { // 重新绑定打印机
        String storeGuid = kdsPrinterBindUnbindReqDTO.getStoreGuid();
        String deviceId = kdsPrinterBindUnbindReqDTO.getDeviceId();
        String printerGuid = kdsPrinterBindUnbindReqDTO.getPrinterGuid();
        DeviceConfigDO deviceConfigDO = new DeviceConfigDO();
        deviceConfigDO.setPrinterGuid(printerGuid);
        update(deviceConfigDO, deviceConfigWrapper(storeGuid, deviceId));
    }

    @Override
    public void unbindPrinter(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) { // 解绑打印机
        String storeGuid = kdsPrinterBindUnbindReqDTO.getStoreGuid();
        String deviceId = kdsPrinterBindUnbindReqDTO.getDeviceId();
        assertThatDeviceExists(storeGuid, deviceId);
        update(new DeviceConfigDO(), new UpdateWrapper<DeviceConfigDO>().lambda()
                .eq(DeviceConfigDO::getStoreGuid, storeGuid)
                .eq(DeviceConfigDO::getGuid, deviceId)
                .set(DeviceConfigDO::getPrinterGuid, ""));
    }

    @Override
    public void unbindPrinter(String printerGuid) {
        update(new DeviceConfigDO(), new UpdateWrapper<DeviceConfigDO>().lambda()
                .eq(DeviceConfigDO::getPrinterGuid, printerGuid)
                .set(DeviceConfigDO::getPrinterGuid, ""));
    }

    /**
     * 名称相同，设备Guid不同，则判定设备名称重复
     *
     * @param deviceConfigDO
     */
    private void assertDeviceNameAvailable(DeviceConfigDO deviceConfigDO) {
        if (count(new LambdaQueryWrapper<DeviceConfigDO>()
                .ne(DeviceConfigDO::getGuid, deviceConfigDO.getGuid())
                .eq(DeviceConfigDO::getName, deviceConfigDO.getName())
                .eq(DeviceConfigDO::getStoreGuid, deviceConfigDO.getStoreGuid())) > 0) {
            throw new BusinessException("设备名称[" + deviceConfigDO.getName() + "]重复");
        }
    }

    private Wrapper<DeviceConfigDO> deviceConfigWrapper(String storeGuid, String deviceId) {
        return new LambdaQueryWrapper<DeviceConfigDO>()
                .eq(DeviceConfigDO::getStoreGuid, storeGuid)
                .eq(DeviceConfigDO::getGuid, deviceId);
    }
}
