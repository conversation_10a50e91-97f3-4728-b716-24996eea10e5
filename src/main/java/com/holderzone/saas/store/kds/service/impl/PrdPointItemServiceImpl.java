package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.kds.req.PrdDstItemBindDTO;
import com.holderzone.saas.store.dto.kds.req.PrdPointDelReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdPointItemBindReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdPointItemQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdPointItemDTO;
import com.holderzone.saas.store.kds.entity.domain.DeviceBindItemGroupDO;
import com.holderzone.saas.store.kds.entity.domain.PrdPointItemDO;
import com.holderzone.saas.store.kds.mapper.PrdPointItemMapper;
import com.holderzone.saas.store.kds.mapstruct.DeviceConfigMapstruct;
import com.holderzone.saas.store.kds.service.DeviceBindItemGroupService;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import com.holderzone.saas.store.kds.service.PrdPointItemService;
import com.holderzone.saas.store.kds.service.rpc.ItemRpcService;
import com.holderzone.saas.store.kds.utils.SnowFlakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机单据管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class PrdPointItemServiceImpl extends ServiceImpl<PrdPointItemMapper, PrdPointItemDO> implements PrdPointItemService {

    private final DeviceConfigMapstruct deviceConfigMapstruct;

    private final DistributedIdService distributedIdService;

    private final PrdPointItemMapper prdPointItemMapper;

    private final ItemRpcService itemRpcService;

    private final DeviceBindItemGroupService deviceBindItemGroupService;

    @Autowired
    public PrdPointItemServiceImpl(DeviceConfigMapstruct deviceConfigMapstruct, DistributedIdService distributedIdService,
                                   PrdPointItemMapper prdPointItemMapper, ItemRpcService itemRpcService,
                                   DeviceBindItemGroupService deviceBindItemGroupService) {
        this.deviceConfigMapstruct = deviceConfigMapstruct;
        this.distributedIdService = distributedIdService;
        this.prdPointItemMapper = prdPointItemMapper;
        this.itemRpcService = itemRpcService;
        this.deviceBindItemGroupService = deviceBindItemGroupService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindItem(PrdPointItemBindReqDTO prdPointItemBindReqDTO) {
        // 直接绑定商品
        if (!CollectionUtils.isEmpty(prdPointItemBindReqDTO.getBindingItems())) {
            prdPointItemBind(prdPointItemBindReqDTO);
        }
        // 绑定菜品分组
        if (Boolean.TRUE.equals(prdPointItemBindReqDTO.getBindingItemGroupFlag())) {
            prdPointItemBindGroup(prdPointItemBindReqDTO);
        }
    }

    /**
     * 直接绑定商品
     */
    private void prdPointItemBind(PrdPointItemBindReqDTO prdPointItemBindReqDTO) {
        List<PrdDstItemBindDTO> bindingItems = prdPointItemBindReqDTO.getBindingItems();
        // allSkuGuidList 需要在removeIf前执行，不要移动到removeIf之后
        List<String> allSkuGuidList = bindingItems.stream()
                .map(PrdDstItemBindDTO::getSkuGuid).collect(Collectors.toList());
        List<PrdPointItemDO> prdPointItemInSql = list(new LambdaQueryWrapper<PrdPointItemDO>()
                .select(PrdPointItemDO::getPointGuid, PrdPointItemDO::getSkuGuid)
                .nested(wrapper -> wrapper
                        .in(PrdPointItemDO::getSkuGuid, allSkuGuidList)
                        .or()
                        .eq(PrdPointItemDO::getPointGuid, prdPointItemBindReqDTO.getPointGuid())
                )
                .eq(PrdPointItemDO::getStoreGuid, prdPointItemBindReqDTO.getStoreGuid()));
        if (prdPointItemInSql.stream().anyMatch(prdPointItemDO -> !prdPointItemDO.getPointGuid()
                .equalsIgnoreCase(prdPointItemBindReqDTO.getPointGuid()))) {
            throw new BusinessException("部分商品已被绑定");
        }
        List<String> existedSkuGuidList = prdPointItemInSql.stream()
                .map(PrdPointItemDO::getSkuGuid).collect(Collectors.toList());
        // 增加部分
        bindingItems.removeIf(req -> existedSkuGuidList.contains(req.getSkuGuid()));
        if (!CollectionUtils.isEmpty(bindingItems)) {
            List<String> guids = distributedIdService.nextBatchPointItemGuid(bindingItems.size());
            saveBatch(bindingItems.stream()
                    .map(prdDstItemBindDTO -> deviceConfigMapstruct.fromItemConfigUpdateReq(
                            prdDstItemBindDTO, prdPointItemBindReqDTO))
                    .peek(prdPointItemDO -> prdPointItemDO.setGuid(guids.remove(guids.size() - 1)))
                    .collect(Collectors.toList()));
        }
    }

    /**
     * 绑定菜品分组
     */
    private void prdPointItemBindGroup(PrdPointItemBindReqDTO prdPointItemBindReqDTO) {
        // 先删除
        deviceBindItemGroupService.unbind(prdPointItemBindReqDTO.getStoreGuid(), prdPointItemBindReqDTO.getPointGuid(),
                prdPointItemBindReqDTO.getDeviceId());
        List<String> bindingItemGroups = prdPointItemBindReqDTO.getBindingItemGroups();
        List<DeviceBindItemGroupDO> deviceBindItemGroupDOList = bindingItemGroups.stream()
                .map(groupGuid -> {
                    DeviceBindItemGroupDO deviceBindItemGroupDO = new DeviceBindItemGroupDO();
                    deviceBindItemGroupDO.setGuid(String.valueOf(SnowFlakeUtil.getInstance().nextId()));
                    deviceBindItemGroupDO.setPointGuid(prdPointItemBindReqDTO.getPointGuid());
                    deviceBindItemGroupDO.setDeviceId(prdPointItemBindReqDTO.getDeviceId());
                    deviceBindItemGroupDO.setGroupGuid(groupGuid);
                    deviceBindItemGroupDO.setStoreGuid(prdPointItemBindReqDTO.getStoreGuid());
                    return deviceBindItemGroupDO;
                }).collect(Collectors.toList());
        deviceBindItemGroupService.saveBatch(deviceBindItemGroupDOList);
    }

    @Override
    public void unbindItem(PrdPointItemBindReqDTO prdPointItemBindReqDTO) {
        List<PrdDstItemBindDTO> bindingItems = prdPointItemBindReqDTO.getBindingItems();
        if (CollectionUtils.isEmpty(bindingItems)) return;
        List<String> allSkuGuidList = bindingItems.stream()
                .map(PrdDstItemBindDTO::getSkuGuid).collect(Collectors.toList());
        // 菜谱解绑品牌库商品同时解绑普通模式下的门店商品
        List<String> skuGuidList = itemRpcService.listSkuGuid(allSkuGuidList);
        allSkuGuidList.addAll(skuGuidList);
        remove(new LambdaQueryWrapper<PrdPointItemDO>()
                .eq(PrdPointItemDO::getStoreGuid, prdPointItemBindReqDTO.getStoreGuid())
                .eq(PrdPointItemDO::getDeviceId, prdPointItemBindReqDTO.getDeviceId())
                .in(PrdPointItemDO::getSkuGuid, allSkuGuidList));
    }

    @Override
    public void unbindItem(PrdPointDelReqDTO prdPointDelReqDTO) {
        remove(new LambdaQueryWrapper<PrdPointItemDO>()
                .eq(PrdPointItemDO::getStoreGuid, prdPointDelReqDTO.getStoreGuid())
                .eq(PrdPointItemDO::getDeviceId, prdPointDelReqDTO.getDeviceId())
                .eq(PrdPointItemDO::getPointGuid, prdPointDelReqDTO.getPointGuid())
        );
    }

    @Override
    public void reInitialize(String storeGuid, String deviceId) {
        remove(new LambdaQueryWrapper<PrdPointItemDO>()
                .eq(PrdPointItemDO::getStoreGuid, storeGuid)
                .eq(PrdPointItemDO::getDeviceId, deviceId));
    }

    @Override
    public List<PrdPointItemDO> queryBoundItem(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        return list(new LambdaQueryWrapper<PrdPointItemDO>()
                .eq(PrdPointItemDO::getStoreGuid, prdPointItemQueryReqDTO.getStoreGuid())
        );
    }

    /**
     * 根据商品sku查询绑定菜品
     *
     * @param prdPointItemQueryReqDTO
     * @return
     */
    @Override
    public List<PrdPointItemDTO> queryBoundItemBySku(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        List<PrdPointItemDO> list = this.list(new LambdaQueryWrapper<PrdPointItemDO>()
                .eq(PrdPointItemDO::getSkuGuid, prdPointItemQueryReqDTO.getSearchKey())
        );
        return getPrdPointItemDTOS(list);
    }

    @NotNull
    private ArrayList<PrdPointItemDTO> getPrdPointItemDTOS(List<PrdPointItemDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        ArrayList<PrdPointItemDTO> arrayList = new ArrayList<>();
        // 实体类转换
        list.forEach(
                prdPointItemDO -> {
                    PrdPointItemDTO dto = new PrdPointItemDTO();
                    dto.setItemGuid(prdPointItemDO.getItemGuid());
                    dto.setPointGuid(prdPointItemDO.getPointGuid());
                    dto.setDeviceId(prdPointItemDO.getDeviceId());
                    dto.setSkuCode(prdPointItemDO.getSkuCode());
                    dto.setSkuGuid(prdPointItemDO.getSkuGuid());
                    dto.setStoreGuid(prdPointItemDO.getStoreGuid());
                    arrayList.add(dto);
                }
        );
        return arrayList;
    }

    @Override
    public Map<String, PrdPointItemDO> queryPrdPointByItem(List<String> skuGuidList, String storeGuid) {
        if (CollectionUtils.isEmpty(skuGuidList)) {
            return Collections.emptyMap();
        }
        List<PrdPointItemDO> prdPointItemInSql = list(new LambdaQueryWrapper<PrdPointItemDO>()
                .select(PrdPointItemDO::getDeviceId,
                        PrdPointItemDO::getPointGuid,
                        PrdPointItemDO::getSkuGuid)
                .in(PrdPointItemDO::getSkuGuid, skuGuidList)
                .eq(PrdPointItemDO::getStoreGuid, storeGuid)
        );
        if (CollectionUtils.isEmpty(prdPointItemInSql)) {
            return Collections.emptyMap();
        }
        return prdPointItemInSql.stream()
                .collect(Collectors.toMap(PrdPointItemDO::getSkuGuid, Function.identity()));
    }

    @Override
    public List<PrdPointItemDTO> queryPrdPointBySku(SingleDataDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getDatas()) || StringUtils.isEmpty(reqDTO.getData())) {
            log.warn("入参有误");
            return Lists.newArrayList();
        }
        List<PrdPointItemDO> prdPointItemDOList = list(new LambdaQueryWrapper<PrdPointItemDO>()
                .select(PrdPointItemDO::getDeviceId,
                        PrdPointItemDO::getPointGuid,
                        PrdPointItemDO::getSkuCode,
                        PrdPointItemDO::getItemGuid,
                        PrdPointItemDO::getStoreGuid,
                        PrdPointItemDO::getSkuGuid)
                .in(PrdPointItemDO::getSkuGuid, reqDTO.getDatas())
                .eq(PrdPointItemDO::getStoreGuid, reqDTO.getData())
        );
        return getPrdPointItemDTOS(prdPointItemDOList);
    }

    /**
     * 查询kds所有绑定的菜品
     *
     * @return
     */
    @Override
    public List<PrdPointItemDTO> queryAllBoundItem() {
        return prdPointItemMapper.queryAll();
    }
}
