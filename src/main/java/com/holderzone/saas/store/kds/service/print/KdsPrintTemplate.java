package com.holderzone.saas.store.kds.service.print;

import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.kds.req.KdsPrintDTO;

import java.util.List;

public interface KdsPrintTemplate<T extends KdsPrintDTO> {

    String getPrintFailedMsg();

    int getPageSize();

    void setPageSize(int pageSize);

    T getPrintDTO();

    void setPrintDTO(T t);

    List<PrintRow> getPrintRows();
}
