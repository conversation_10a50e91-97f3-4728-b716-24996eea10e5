package com.holderzone.saas.store.kds.service.print.impl;

import com.holderzone.framework.base.dto.message.*;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.KdsPrintDTO;
import com.holderzone.saas.store.dto.kds.req.KdsPrintRecordReqDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.kds.entity.domain.BaseDO;
import com.holderzone.saas.store.kds.entity.domain.KdsPrintRecordDO;
import com.holderzone.saas.store.kds.service.print.KdsPrintPushService;
import com.holderzone.saas.store.kds.service.print.KdsPrintTemplate;
import com.holderzone.saas.store.kds.service.rpc.MsgRpcService;
import com.holderzone.saas.store.kds.service.template.PrintComponentFactory;
import com.holderzone.saas.store.kds.utils.PrintLogUtils;
import com.holderzone.saas.store.kds.utils.SnowFlakeUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className KdsPrintPushServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印消息推送实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
@SuppressWarnings("Duplicates")
public class KdsPrintPushServiceImpl implements KdsPrintPushService {

    private static final String MESSAGE_TOPIC = "kds_print:";

    @Value("${batch-push.enable:true}")
    private boolean batchPushEnable;

    private final MsgRpcService msgRpcService;

    private final PrintComponentFactory printComponentFactory;

    @Autowired
    public KdsPrintPushServiceImpl(MsgRpcService msgRpcService, PrintComponentFactory printComponentFactory) {
        this.msgRpcService = msgRpcService;
        this.printComponentFactory = printComponentFactory;
    }

    @Override
    @SneakyThrows
    public void pushPrintTaskMsg(KdsPrintDTO kdsPrintDTO, List<KdsPrintRecordDO> arrayOfPrintRecord) {
        String storeGuid = kdsPrintDTO.getStoreGuid();
        String enterpriseGuid = kdsPrintDTO.getEnterpriseGuid();
        for (KdsPrintRecordDO kdsPrintRecordDO : arrayOfPrintRecord) {
            String searchKey = PrintLogUtils.searchKey(kdsPrintRecordDO.getRecordUid(), kdsPrintRecordDO.getInvoiceType());
            log.info("打印任务消息推送，处理中，searchKey={}，deviceId={}, recordGuid={}",
                    searchKey, kdsPrintRecordDO.getDeviceId(), kdsPrintRecordDO.getGuid());
        }
        if (batchPushEnable) {
            Map<String, List<KdsPrintRecordDO>> collect = arrayOfPrintRecord.stream()
                    .collect(Collectors.groupingBy(KdsPrintRecordDO::getDeviceId));
            for (Map.Entry<String, List<KdsPrintRecordDO>> entry : collect.entrySet()) {
                List<String> arrayOfRecordGuid = entry.getValue().stream()
                        .map(BaseDO::getGuid).collect(Collectors.toList());
                MessageDTO messageDTO = buildPrintTaskDetail(entry.getKey(),
                        storeGuid, enterpriseGuid, null, arrayOfRecordGuid);
                msgRpcService.sendPrintMessage(messageDTO);
            }
        } else {
            for (KdsPrintRecordDO printRecordDO : arrayOfPrintRecord) {
                MessageDTO messageDTO = buildPrintTaskDetail(printRecordDO.getDeviceId(),
                        storeGuid, enterpriseGuid, printRecordDO.getGuid(), null);
                msgRpcService.sendPrintMessage(messageDTO);
            }
        }
        for (KdsPrintRecordDO kdsPrintRecordDO : arrayOfPrintRecord) {
            String searchKey = PrintLogUtils.searchKey(kdsPrintRecordDO.getRecordUid(), kdsPrintRecordDO.getInvoiceType());
            log.info("打印任务消息推送，处理完毕，searchKey={}，deviceId={}, recordGuid={}",
                    searchKey, kdsPrintRecordDO.getDeviceId(), kdsPrintRecordDO.getGuid());
        }
    }

    @Override
    public void pushPrintSucceedMsg(KdsPrintRecordReqDTO kdsPrintRecordReqDTO, KdsPrintRecordDO kdsPrintRecordDO, int failedCount) {
        Integer invoiceType = kdsPrintRecordDO.getInvoiceType();
        String printContent = kdsPrintRecordDO.getPrintContent();
        PrintDTO printDTO = InvoiceTypeEnum.resolvePrintBy(invoiceType, printContent);
        String searchKey = PrintLogUtils.searchKey(printDTO.getPrintUid(), printDTO.getInvoiceType());
        log.info("打印成功消息推送，处理中，searchKey={}，deviceId={}, recordGuid={}",
                searchKey, kdsPrintRecordReqDTO.getDeviceId(), kdsPrintRecordReqDTO.getRecordGuid());
        MessageDTO messageDTO = buildFailedNumberDetail(kdsPrintRecordReqDTO.getDeviceId(), "", failedCount,
                printDTO.getStoreGuid(), printDTO.getEnterpriseGuid(), "");
        msgRpcService.sendPrintMessage(messageDTO);
        log.info("打印成功消息推送，处理成功，searchKey={}，推送内容：{}",
                searchKey, JacksonUtils.writeValueAsString(messageDTO));
    }

    @Override
    public void pushPrintFailedMsg(KdsPrintRecordReqDTO kdsPrintRecordReqDTO, KdsPrintRecordDO kdsPrintRecordDO, int failedCount) {
        Integer invoiceType = kdsPrintRecordDO.getInvoiceType();
        String printContent = kdsPrintRecordDO.getPrintContent();
        KdsPrintTemplate<? extends KdsPrintDTO> kdsPrintTemplate = printComponentFactory.create(invoiceType, printContent);
        KdsPrintDTO printDTO = kdsPrintTemplate.getPrintDTO();
        String printFailedMessage = kdsPrintTemplate.getPrintFailedMsg();
        String searchKey = PrintLogUtils.searchKey(printDTO.getPrintUid(), printDTO.getInvoiceType());
        log.info("打印失败消息推送，处理中，searchKey={}，deviceId={}, recordGuid={}",
                searchKey, kdsPrintRecordReqDTO.getDeviceId(), kdsPrintRecordReqDTO.getRecordGuid());
        MessageDTO messageDTO = buildFailedNumberDetail(kdsPrintRecordReqDTO.getDeviceId(), printFailedMessage, failedCount,
                printDTO.getStoreGuid(), printDTO.getEnterpriseGuid(), kdsPrintRecordReqDTO.getRecordGuid());
        msgRpcService.sendPrintMessage(messageDTO);
        log.info("打印失败消息推送，处理成功，searchKey={}，推送内容：{}",
                searchKey, JacksonUtils.writeValueAsString(messageDTO));
    }

    private MessageDTO buildPrintTaskDetail(String deviceId, String storeGuid, String enterpriseGuid,
                                            String recordGuid, List<String> arrayOfRecordGuid) {
        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("recordGuid", recordGuid);
        detailMap.put("arrayOfRecordGuid", arrayOfRecordGuid);
        detailMap.put("msgId", String.valueOf(SnowFlakeUtil.getInstance().nextId()));
        return buildMessage(deviceId, detailMap, storeGuid, enterpriseGuid);
    }

    private MessageDTO buildFailedNumberDetail(String deviceId, String msg, int count,
                                               String storeGuid, String enterpriseGuid, String recordGuid) {
        Map<String, Object> map = new HashMap<>();
        map.put("msg", msg);
        map.put("number", String.valueOf(count));
        map.put("failedKey", recordGuid);
        return buildMessage(deviceId, map, storeGuid, enterpriseGuid);
    }

    private MessageDTO buildMessage(String deviceId, Map<String, Object> detailMap, String storeGuid, String enterpriseGuid) {
        PushMessageDTO pushMessageDTO = new PushMessageDTO();
        pushMessageDTO.setTopicType(TopicType.BUSINESS);
        BusinessMessage businessMessage = new BusinessMessage();
        businessMessage.setBusinessType(MESSAGE_TOPIC + deviceId);
        businessMessage.setStoreGuid(storeGuid);
        businessMessage.setEnterpriseGuid(enterpriseGuid);
        pushMessageDTO.setBusinessMessage(businessMessage);
        pushMessageDTO.setData(JacksonUtils.writeValueAsString(detailMap));
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setMessageType(MessageType.PUSH);
        messageDTO.setPushMessage(pushMessageDTO);
        return messageDTO;
    }
}
