package com.holderzone.saas.store.kds.mapstruct;

import com.holderzone.saas.store.kds.entity.domain.BindItemDO;
import com.holderzone.saas.store.kds.entity.domain.PrdPointItemDO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring")
public interface BindItemMapstruct {

    PrdPointItemDO toPrdPointItemDO(BindItemDO bindItemDO);

    List<PrdPointItemDO> toPrdPointItemDOList(List<BindItemDO> bindItemDOList);
}
