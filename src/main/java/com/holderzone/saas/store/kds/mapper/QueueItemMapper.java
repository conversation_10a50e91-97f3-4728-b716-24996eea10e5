package com.holderzone.saas.store.kds.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.kds.entity.domain.QueueItemDO;
import com.holderzone.saas.store.kds.entity.query.QueueItemQuery;

import java.util.List;

public interface QueueItemMapper extends BaseMapper<QueueItemDO> {

    List<QueueItemDO> queryOriQueueItemsOfOrder(QueueItemQuery queueItemQuery);
}