package com.holderzone.saas.store.kds.handler;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.kds.req.ItemChangesReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemPrepareReqDTO;
import com.holderzone.saas.store.dto.kds.req.KdsChangesItemDTO;
import com.holderzone.saas.store.kds.bo.ItemPrepareSplitBO;
import com.holderzone.saas.store.dto.kds.req.KdsItemDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayItemRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayRuleItemRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayRuleRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayStoreRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.enums.kds.KdsItemStateEnum;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import com.holderzone.saas.store.kds.bo.KitchenItemChangesBO;
import com.holderzone.saas.store.kds.bo.KitchenItemPrepareBO;
import com.holderzone.saas.store.kds.constant.Constants;
import com.holderzone.saas.store.kds.entity.bo.ChangeKitchenItemBO;
import com.holderzone.saas.store.kds.entity.bo.OrderInfoBO;
import com.holderzone.saas.store.kds.entity.domain.*;
import com.holderzone.saas.store.kds.entity.enums.DisplayRuleType;
import com.holderzone.saas.store.kds.entity.enums.KdsKitchenStateEnum;
import com.holderzone.saas.store.kds.entity.enums.KdsTradeModeEnum;
import com.holderzone.saas.store.kds.mapstruct.DisplayRuleMapstruct;
import com.holderzone.saas.store.kds.mapstruct.KitchenItemMapstruct;
import com.holderzone.saas.store.kds.service.*;
import com.holderzone.saas.store.kds.service.rpc.ItemRpcService;
import com.holderzone.saas.store.kds.service.rpc.TradeRpcService;
import com.holderzone.saas.store.kds.utils.ItemMd5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public abstract class BaseAbstractKitchenItemHandler implements BaseKitchenItemHandler {

    @Resource
    private ItemRpcService itemRpcService;

    @Resource
    private PrdPointItemService prdPointItemService;

    @Resource
    private DeviceConfigService deviceConfigService;

    @Resource
    private DistributeItemService distributeItemService;

    @Resource
    private DistributeAreaService distributeAreaService;

    @Resource
    private DisplayStoreService displayStoreService;

    @Resource
    private DisplayRuleService displayRuleService;

    @Resource
    private DisplayItemService displayItemService;

    @Resource
    private TradeRpcService tradeRpcService;

    @Resource
    @Lazy
    private KitchenItemService kitchenItemService;

    @Resource
    private DistributedIdService distributedIdService;

    @Resource
    private KitchenItemMapstruct kitchenItemMapstruct;

    @Resource
    private DisplayRuleMapstruct displayRuleMapstruct;

    abstract OrderInfoBO fullOrderInfoBO(ItemPrepareReqDTO itemPrepareReqDTO);

    abstract ChangeKitchenItemBO fullChangeKitchenItemBO(ItemChangesReqDTO itemChangesReqDTO);

    abstract List<KitchenItemDO> convertItemAndSplitIfNecessary(ItemPrepareSplitBO itemPrepareSplitBO);

    abstract void handleChangeItemDevice(ChangeKitchenItemBO kitchenItemBO,
                                         KdsItemDTO changesKdsItem,
                                         KitchenItemDO changeKitchenItemDO);

    abstract Map<String, List<String>> getChangeDeviceIds(ItemChangesReqDTO itemChangesReqDT, KitchenItemChangesBO biz);

    @Override
    public KitchenItemPrepareBO prepare(ItemPrepareReqDTO itemPrepareReqDTO) {
        String storeGuid = UserContextUtils.getStoreGuid();
        List<KdsItemDTO> kdsItemDTOS = itemPrepareReqDTO.getItems();
        //设置菜品批次、生效时间、显示类型、菜品序号
        List<DisplayRuleRespDTO> displayRuleRespDTOS = queryStoreRuleList(storeGuid);
        // 查询商品的品牌Guid
        List<String> itemGuidList = itemPrepareReqDTO.getItems().stream()
                .map(KdsItemDTO::getItemGuid)
                .collect(Collectors.toList());
        List<ItemInfoRespDTO> mappingRespDTOList = itemRpcService.kdsItemParentMapping(itemGuidList);
        Map<String, String> itemParentGuidMap = new HashMap<>(100);
        mappingRespDTOList.forEach(m -> itemParentGuidMap.put(StringUtils.isEmpty(m.getParentGuid())
                ? m.getItemGuid() : m.getParentGuid(), m.getItemGuid()));
        log.info("商品父id查询itemParentGuidMap:{}", JacksonUtils.writeValueAsString(itemParentGuidMap));
        Map<String, DisplayRuleItemRespDTO> kitchenItemDisplayRuleMap = getKitchenItemDisplayRuleMap(
                itemGuidList, displayRuleRespDTOS, itemParentGuidMap
        );
        log.info("规则整合kitchenItemDisplayRuleMap:{}", JacksonUtils.writeValueAsString(kitchenItemDisplayRuleMap));
        // 计算商品总数，称重菜品数量为1
        long itemSum = calItemSum(kdsItemDTOS);
        if (itemSum == 0) {
            log.error("菜品数量为0：{}", JacksonUtils.writeValueAsString(kdsItemDTOS));
            throw new IllegalArgumentException("菜品数量为0");
        }
        // skuGuid 列表
        List<String> skuGuid = kdsItemDTOS.stream().map(KdsItemDTO::getSkuGuid).collect(Collectors.toList());
        //通过skuGuid查询关联sku
        List<SkuInfoRespDTO> skus = itemRpcService.findParentSkus(skuGuid);
        itemPrepareReqDTO.setSkus(skus);

        // 区分两种模式： 1.绑定菜品分组； 2.直接绑定菜品
        OrderInfoBO orderInfoBO = fullOrderInfoBO(itemPrepareReqDTO);
        log.info("orderInfoBO:{}", JacksonUtils.writeValueAsString(orderInfoBO));

        // 查询刚保存且需要打印的数据
        // 菜品对应的设备列表集合
        List<String> deviceIdList = orderInfoBO.getDeviceIds();
        // 设备ID -> 设备
        Map<String, DeviceConfigDO> deviceConfigInSqlMap = deviceConfigService.listPrdDeviceByGuid(storeGuid, deviceIdList);
        // 计算数据
        List<KitchenItemDO> kitchenItem2Insert = new ArrayList<>();
        List<KitchenItemAttrDO> kitchenItemAttr2Insert = new ArrayList<>();
        LocalDateTime prepareTime = LocalDateTime.now();
        boolean isNeedNotification = false;
        for (KdsItemDTO kdsItemDTO : kdsItemDTOS) {
            List<KitchenItemAttrDO> attrs = convertAttr(kdsItemDTO);
            ItemPrepareSplitBO itemPrepareSplitBO = buildItemPrepareSplitBO(storeGuid, kdsItemDTO, orderInfoBO,
                    attrs, deviceConfigInSqlMap, prepareTime, skus);
            List<KitchenItemDO> kitchenItemDOS = convertItemAndSplitIfNecessary(itemPrepareSplitBO);
            if (!CollectionUtils.isEmpty(kitchenItemDOS)) {
                kitchenItem2Insert.addAll(kitchenItemDOS);
            } else {
                isNeedNotification = true;
            }
            if (!CollectionUtils.isEmpty(attrs)) {
                kitchenItemAttr2Insert.addAll(attrs);
            }
        }
        setDistributeItemRule(kitchenItemDisplayRuleMap, kitchenItem2Insert, itemPrepareReqDTO.getTradeMode());
        log.info("设置完菜品显示规则后：{}", JacksonUtils.writeValueAsString(kitchenItem2Insert));
        // 设置加菜批次
        setAddItemBatch(itemPrepareReqDTO, kitchenItem2Insert);

        KitchenItemPrepareBO biz = new KitchenItemPrepareBO();
        biz.setKitchenItemList(kitchenItem2Insert);
        biz.setKitchenItemAttrList(kitchenItemAttr2Insert);
        biz.setIsNeedNotification(isNeedNotification);
        biz.setPrepareTime(prepareTime);
        biz.setDeviceConfigInSqlMap(deviceConfigInSqlMap);
        biz.setKitchenItemDisplayRuleMap(kitchenItemDisplayRuleMap);
        return biz;
    }

    @Override
    public KitchenItemChangesBO changes(ItemChangesReqDTO itemChangesReqDTO) {
        List<KdsItemDTO> originalKdsItemList = itemChangesReqDTO.getOriginalKdsItemList();
        Set<String> originalSkuGuidList = originalKdsItemList.stream().map(KdsItemDTO::getSkuGuid)
                .collect(Collectors.toSet());
        List<KdsItemDTO> changesKdsItemList = itemChangesReqDTO.getChangesKdsItemList();
        Set<String> changeSkuGuidList = changesKdsItemList.stream().map(KdsItemDTO::getSkuGuid)
                .collect(Collectors.toSet());
        originalSkuGuidList.addAll(changeSkuGuidList);
        // 通过skuGuid查询关联sku
        List<SkuInfoRespDTO> skuList = itemRpcService.findParentSkus(new ArrayList<>(changeSkuGuidList));
        itemChangesReqDTO.setSkus(skuList);
        KitchenItemChangesBO biz = new KitchenItemChangesBO();
        // 换菜
        changeKdsItem(itemChangesReqDTO, biz);
        log.info("changeKdsItem after KitchenItemChangesBO:{}", JacksonUtils.writeValueAsString(biz));
        // 打印换菜单
        KdsChangesItemDTO kdsChangesItemDTO = kitchenItemMapstruct.itemChangesDTO2KdsChangesItemDTO(itemChangesReqDTO);
        // 打印设备列表
        // 查询换菜前 和 换菜后菜品绑定的设备id
        Map<String, List<String>> deviceItemMap = getChangeDeviceIds(itemChangesReqDTO, biz);
        log.info("换菜查询打印设备列表:{}", JacksonUtils.writeValueAsString(deviceItemMap));
        kdsChangesItemDTO.setDeviceItemMap(deviceItemMap);
        kdsChangesItemDTO.setDeviceIds(deviceItemMap.keySet());
        biz.setKdsChangesItemDTO(kdsChangesItemDTO);
        return biz;
    }


    private void changeKdsItem(ItemChangesReqDTO itemChangesReqDTO, KitchenItemChangesBO biz) {
        String storeGuid = itemChangesReqDTO.getStoreGuid();
        // 原订单商品明细guid
        List<String> originalOrderItemGuidList = itemChangesReqDTO.getOriginalKdsItemList()
                .stream()
                .map(KdsItemDTO::getOrderItemGuid)
                .distinct()
                .collect(Collectors.toList());
        // 查询厨房原菜品记录
        List<KitchenItemDO> kitchenItemDOList = kitchenItemService.list(new LambdaQueryWrapper<KitchenItemDO>()
                .eq(KitchenItemDO::getOrderGuid, itemChangesReqDTO.getOrderGuid())
                .in(KitchenItemDO::getOrderItemGuid, originalOrderItemGuidList)
        );
        if (CollectionUtils.isEmpty(kitchenItemDOList)) {
            log.warn("厨房原菜品记录为空, 直接展示换后的菜");
        }
        // 查询需要更换的菜品明细
        List<KitchenItemDO> needChangeKitchenItemDOList = getNeedChangeKitchenItemDOList(itemChangesReqDTO, kitchenItemDOList);
        Map<String, KitchenItemDO> needChangeKitchenItemStateMap = needChangeKitchenItemDOList.stream()
                .collect(Collectors.toMap(KitchenItemDO::getOrderItemGuid, Function.identity(), (key1, key2) -> key1));
        // 通过门店和商品获取显示规则
        List<String> itemGuidList = itemChangesReqDTO.getChangesKdsItemList()
                .stream()
                .map(KdsItemDTO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        Map<String, DisplayRuleItemRespDTO> kitchenItemDisplayRuleMap = queryDisplayRule(itemGuidList, storeGuid);
        // 合并原菜
        KdsItemDTO mergeOriginalKdsItem = itemChangesReqDTO.getOriginalKdsItemList().get(0);
        String mergeOriginalKdsItemName = mergeOriginalKdsItemList(itemChangesReqDTO.getOriginalKdsItemList());
        // 换菜biz
        ChangeKitchenItemBO kitchenItemBO = fullChangeKitchenItemBO(itemChangesReqDTO);
        kitchenItemBO.setKitchenItemDisplayRuleMap(kitchenItemDisplayRuleMap);
        kitchenItemBO.setOriginalKdsItem(mergeOriginalKdsItem);
        kitchenItemBO.setOriginalKdsItemName(mergeOriginalKdsItemName);
        kitchenItemBO.setOriginalKitchenItemDO(needChangeKitchenItemStateMap.get(mergeOriginalKdsItem.getOrderItemGuid()));
        kitchenItemBO.setSkus(itemChangesReqDTO.getSkus());

        List<KitchenItemDO> changeKitchenItemDOList = Lists.newArrayList();
        List<KitchenItemAttrDO> kitchenItemAttrList = Lists.newArrayList();
        itemChangesReqDTO.getChangesKdsItemList().forEach(innerChangesKdsItem -> {
            // 构建商品属性
            List<KitchenItemAttrDO> attrList = convertAttr(innerChangesKdsItem);
            kitchenItemBO.setChangesKdsItem(innerChangesKdsItem);
            kitchenItemBO.setAttrList(attrList);
            KitchenItemDO changeKitchenItemDO = getChangeKitchenItemDO(kitchenItemBO);
            changeKitchenItemDOList.add(changeKitchenItemDO);
            // 分批
            addChangeKitchenItemList(changeKitchenItemDOList, innerChangesKdsItem, changeKitchenItemDO);
            if (CollectionUtils.isNotEmpty(attrList)) {
                kitchenItemAttrList.addAll(attrList);
            }
        });
        biz.setChangeKitchenItemDOList(changeKitchenItemDOList);
        biz.setNeedChangeKitchenItemDOList(needChangeKitchenItemDOList);
        biz.setKitchenItemAttrList(kitchenItemAttrList);

        // 查询刚保存且需要打印的数据
        Map<String, DeviceConfigDO> deviceConfigInSqlMap = deviceConfigService.listPrdDeviceByGuid(storeGuid, kitchenItemBO.getDeviceIds());
        biz.setDeviceConfigInSqlMap(deviceConfigInSqlMap);
        biz.setChangeKitchenItemBO(kitchenItemBO);
    }


    /**
     * 查询需要更换的菜品明细
     */
    private List<KitchenItemDO> getNeedChangeKitchenItemDOList(ItemChangesReqDTO itemChangesReqDTO, List<KitchenItemDO> kitchenItemDOList) {
        if (CollectionUtils.isEmpty(kitchenItemDOList)) {
            return Lists.newArrayList();
        }
        List<KdsItemDTO> originalKdsItemList = itemChangesReqDTO.getOriginalKdsItemList();
        // 排序
        List<KitchenItemDO> sotredKitchenItemDOList = kitchenItemDOList.stream()
                .sorted(Comparator.comparing(KitchenItemDO::getKitchenState))
                .collect(Collectors.toList());
        List<KitchenItemDO> needChangeKitchenItemDOList = Lists.newArrayList();
        for (KdsItemDTO kdsItemDTO : originalKdsItemList) {
            List<KitchenItemDO> kitchenItemByOrderItemGuidList = sotredKitchenItemDOList.stream()
                    .filter(e -> e.getOrderItemGuid().equals(kdsItemDTO.getOrderItemGuid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(kitchenItemByOrderItemGuidList)) {
                continue;
            }
            BigDecimal size = kdsItemDTO.getCurrentCount();
            if (Boolean.TRUE.equals(kdsItemDTO.getIsWeight())) {
                // 称重商品
                size = BigDecimal.ONE;
            }
            List<KitchenItemDO> subKitchenItemList = kitchenItemByOrderItemGuidList.subList(0,
                    Integer.parseInt(size.stripTrailingZeros().toPlainString()));
            if (CollectionUtils.isNotEmpty(subKitchenItemList)) {
                List<Long> subKitchenItemIdList = subKitchenItemList.stream().map(KitchenItemDO::getId).collect(Collectors.toList());
                sotredKitchenItemDOList.removeIf(e -> subKitchenItemIdList.contains(e.getId()));
                needChangeKitchenItemDOList.addAll(subKitchenItemList);
            }
        }
        return needChangeKitchenItemDOList;
    }

    private ItemPrepareSplitBO buildItemPrepareSplitBO(String storeGuid,
                                                       KdsItemDTO kdsItemDTO,
                                                       OrderInfoBO orderInfoBO,
                                                       List<KitchenItemAttrDO> attrs,
                                                       Map<String, DeviceConfigDO> deviceConfigInSqlMap,
                                                       LocalDateTime prepareTime, List<SkuInfoRespDTO> skus) {
        ItemPrepareSplitBO biz = new ItemPrepareSplitBO();
        biz.setStoreGuid(storeGuid);
        biz.setKdsItemDTO(kdsItemDTO);
        biz.setOrderInfoBO(orderInfoBO);
        biz.setAttrs(attrs);
        biz.setDeviceConfigInSqlMap(deviceConfigInSqlMap);
        biz.setPrepareTime(prepareTime);
        biz.setSkus(skus);
        return biz;
    }

    private List<KitchenItemAttrDO> convertAttr(KdsItemDTO kdsItemDTO) {
        if (CollectionUtils.isEmpty(kdsItemDTO.getAttrGroup())) {
            return Collections.emptyList();
        }
        String orderItemGuid = kdsItemDTO.getOrderItemGuid();
        return kdsItemDTO.getAttrGroup().stream()
                .flatMap(kdsAttrGroupDTO -> {
                    String groupGuid = kdsAttrGroupDTO.getGroupGuid();
                    String groupName = kdsAttrGroupDTO.getGroupName();
                    return kdsAttrGroupDTO.getAttrs().stream()
                            .map(kdsItemAttrDTO -> {
                                KitchenItemAttrDO kitchenItemAttrDO = new KitchenItemAttrDO();
                                kitchenItemAttrDO.setOrderItemGuid(orderItemGuid);
                                kitchenItemAttrDO.setGroupGuid(groupGuid);
                                kitchenItemAttrDO.setGroupName(groupName);
                                kitchenItemAttrDO.setAttrGuid(kdsItemAttrDTO.getAttrGuid());
                                kitchenItemAttrDO.setAttrName(kdsItemAttrDTO.getAttrName());
                                kitchenItemAttrDO.setAttrNumber(kdsItemAttrDTO.getAttrNumber());
                                return kitchenItemAttrDO;
                            });
                })
                .collect(Collectors.toList());
    }


    private List<DisplayRuleRespDTO> queryStoreRuleList(String storeGuid) {
        //返回结果
        List<DisplayRuleRespDTO> displayRules = Lists.newArrayList();
        //查询所有存在的规则 可能存在“全部门店”
        List<DisplayRuleDO> displayRuleDOList = Lists.newArrayList();
        //通过门店guid获取门店规则
        List<DisplayStoreDO> displayStoreDOList = displayStoreService.list(new LambdaQueryWrapper<DisplayStoreDO>()
                .eq(DisplayStoreDO::getStoreGuid, storeGuid)
                .eq(DisplayStoreDO::getIsDelete, Boolean.FALSE));
        if (!CollectionUtils.isEmpty(displayStoreDOList)) {
            List<String> ruleGuidList = displayStoreDOList.stream().map(DisplayStoreDO::getRuleGuid).collect(Collectors.toList());
            displayRuleDOList = displayRuleService.list(new LambdaQueryWrapper<DisplayRuleDO>()
                    .eq(DisplayRuleDO::getIsDelete, Boolean.FALSE)
                    .in(DisplayRuleDO::getGuid, ruleGuidList));
        }
        List<DisplayRuleDO> displayRuleDOS = displayRuleService.list(new LambdaQueryWrapper<DisplayRuleDO>()
                .eq(DisplayRuleDO::getIsDelete, Boolean.FALSE)
                .eq(DisplayRuleDO::getIsAllStore, 1));
        if (!CollectionUtils.isEmpty(displayRuleDOS)) {
            log.info("存在所有门店的显示的规则！");
            displayRuleDOList.addAll(displayRuleDOS);
        }
        if (CollectionUtils.isEmpty(displayRuleDOList)) {
            log.info("无该门店匹配的显示规则！");
            return displayRules;
        }
        List<String> checkRuleGuidList = displayRuleDOList.stream().map(DisplayRuleDO::getGuid).collect(Collectors.toList());
        List<DisplayItemDO> displayItemDOS = displayItemService.list(new LambdaQueryWrapper<DisplayItemDO>()
                .in(DisplayItemDO::getRuleGuid, checkRuleGuidList));
        if (CollectionUtils.isEmpty(displayItemDOS)) {
            log.info("无显示菜品！");
            return displayRules;
        }
        Map<String, List<DisplayItemDO>> displayItemDOMap = displayItemDOS.stream().collect(Collectors.groupingBy(DisplayItemDO::getRuleGuid));
        Map<String, List<DisplayStoreDO>> displayStoreDOMap = displayStoreDOList.stream().collect((Collectors.groupingBy(DisplayStoreDO::getRuleGuid)));
        displayRuleDOList.forEach(e -> {
            DisplayRuleRespDTO displayRuleRespDTO = new DisplayRuleRespDTO();
            BeanUtils.copyProperties(e, displayRuleRespDTO);
            List<DisplayItemDO> itemDOList = displayItemDOMap.get(e.getGuid());
            List<DisplayItemRespDTO> displayItemRespDTOS = displayRuleMapstruct.toItemRespList(itemDOList);
            displayRuleRespDTO.setItemList(displayItemRespDTOS);
            List<DisplayStoreDO> displayStoreDOS = displayStoreDOMap.get(e.getGuid());
            List<DisplayStoreRespDTO> displayStoreRespDTOS = displayRuleMapstruct.toStoreRespList(displayStoreDOS);
            displayRuleRespDTO.setStoreList(displayStoreRespDTOS);
            displayRules.add(displayRuleRespDTO);
        });
        log.info("最终显示规则为:{}", JacksonUtils.writeValueAsString(displayRules));
        return displayRules;
    }

    private Map<String, DisplayRuleItemRespDTO> getKitchenItemDisplayRuleMap(List<String> itemGuidList,
                                                                             List<DisplayRuleRespDTO> displayRuleRespDTOS,
                                                                             Map<String, String> itemParentGuidMap) {
        Map<String, DisplayRuleItemRespDTO> displayRuleItemBatchRespMap = new HashMap<>(10);
        Map<String, DisplayRuleItemRespDTO> displayRuleItemTotalRespMap = new HashMap<>(10);
        List<DisplayRuleRespDTO> filterDisplayRuleRespList = displayRuleRespDTOS.stream()
                .filter(e -> CollectionUtils.isNotEmpty(e.getItemList()))
                .collect(Collectors.toList());
        filterDisplayRuleRespList.forEach(e -> forEachFilterDisplayRuleRespList(e, itemParentGuidMap,
                displayRuleItemBatchRespMap, displayRuleItemTotalRespMap));
        return buildKitchenItemDisplayRuleMap(itemGuidList, displayRuleItemBatchRespMap, displayRuleItemTotalRespMap);
    }

    private void forEachFilterDisplayRuleRespList(DisplayRuleRespDTO e, Map<String, String> itemParentGuidMap,
                                                  Map<String, DisplayRuleItemRespDTO> displayRuleItemBatchRespMap,
                                                  Map<String, DisplayRuleItemRespDTO> displayRuleItemTotalRespMap) {
        boolean after = false;
        Integer effectiveState = e.getEffectiveState();
        //校验规则是否生效
        if (0 == effectiveState && e.getEffectiveTime() != null) {
            LocalDateTime effectiveTime = e.getEffectiveTime();
            after = effectiveTime.isBefore(LocalDateTime.now());
        }
        if (effectiveState == 1) {
            after = true;
        }
        if (!after) {
            return;
        }
        e.getItemList().forEach(b -> {
            DisplayRuleItemRespDTO dto = new DisplayRuleItemRespDTO();
            //商品赋值
            BeanUtils.copyProperties(b, dto);
            //主规则赋值
            BeanUtils.copyProperties(e, dto);
            if (!itemParentGuidMap.containsKey(b.getItemGuid())) {
                return;
            }
            String itemGuid = itemParentGuidMap.getOrDefault(b.getItemGuid(), b.getItemGuid());
            if (DisplayRuleType.BATCH == e.getRuleType()) {
                displayRuleItemBatchRespMap.put(itemGuid, dto);
            }
            if (DisplayRuleType.SUMMERY == e.getRuleType()) {
                displayRuleItemTotalRespMap.put(itemGuid, dto);
            }
        });
    }

    private Map<String, DisplayRuleItemRespDTO> buildKitchenItemDisplayRuleMap(List<String> itemGuidList,
                                                                               Map<String, DisplayRuleItemRespDTO> displayRuleItemBatchRespMap,
                                                                               Map<String, DisplayRuleItemRespDTO> displayRuleItemTotalRespMap) {
        Map<String, DisplayRuleItemRespDTO> kitchenItemDisplayRuleMap = new HashMap<>(itemGuidList.size());
        itemGuidList.forEach(itemGuid -> forEachDisplayRuleItemRespDTO(itemGuid, kitchenItemDisplayRuleMap,
                displayRuleItemBatchRespMap, displayRuleItemTotalRespMap));
        return kitchenItemDisplayRuleMap;
    }

    private void forEachDisplayRuleItemRespDTO(String itemGuid,
                                               Map<String, DisplayRuleItemRespDTO> kitchenItemDisplayRuleMap,
                                               Map<String, DisplayRuleItemRespDTO> displayRuleItemBatchRespMap,
                                               Map<String, DisplayRuleItemRespDTO> displayRuleItemTotalRespMap) {
        DisplayRuleItemRespDTO e = new DisplayRuleItemRespDTO();
        DisplayRuleItemRespDTO displayRuleItemRespDTO = displayRuleItemTotalRespMap.get(itemGuid);
        DisplayRuleItemRespDTO batchRuleItemRespDTO = displayRuleItemBatchRespMap.get(itemGuid);
        if (ObjectUtil.isNull(displayRuleItemRespDTO) && ObjectUtil.isNull(batchRuleItemRespDTO)) {
            //不存在匹配的规则
            e.setRuleType(-1);
            kitchenItemDisplayRuleMap.put(itemGuid, e);
            return;
        }
        //是否是汇总菜品
        boolean isTotalResult = false;
        //设置汇总菜品
        if (ObjectUtil.isNotNull(displayRuleItemRespDTO)) {
            e.setRuleType(displayRuleItemRespDTO.getRuleType());
            //排序
            e.setSort(displayRuleItemRespDTO.getSort());
            isTotalResult = true;
        }
        if (ObjectUtil.isNotNull(batchRuleItemRespDTO)) {
            //分批显示
            if (1 == batchRuleItemRespDTO.getDisplayState()) {
                e.setBatch(batchRuleItemRespDTO.getBatch());
            }
            //延迟显示
            if (0 == batchRuleItemRespDTO.getDisplayState()) {
                //延迟时间  单位：分钟
                e.setDelayTime(batchRuleItemRespDTO.getDelayTime());
            }
            //如果当前菜品未存在 汇总规则里 设置为批次规则
            if (!isTotalResult) {
                e.setRuleType(batchRuleItemRespDTO.getRuleType());
            }
        }
        if (isTotalResult) {
            e.setBatch(null);
        }
        kitchenItemDisplayRuleMap.put(itemGuid, e);
    }

    private long calItemSum(List<KdsItemDTO> kdsItemDTOS) {
        return kdsItemDTOS.stream()
                .map(kdsItemDTO -> {
                    if (Boolean.TRUE.equals(kdsItemDTO.getIsWeight())) {
                        return 1;
                    }
                    return kdsItemDTO.getCurrentCount().intValue();
                })
                .mapToLong(value -> value).sum();
    }

    /**
     * 设置菜品批次、生效时间、显示类型、菜品序号
     *
     * @param kitchenItemDisplayRuleMap 显示规则
     * @param kitchenItem2Insert        菜品数据
     */
    private void setDistributeItemRule(Map<String, DisplayRuleItemRespDTO> kitchenItemDisplayRuleMap,
                                       List<KitchenItemDO> kitchenItem2Insert,
                                       Integer tradeMode) {

        kitchenItem2Insert.forEach(e -> {
            String itemGuid = e.getItemGuid();
            DisplayRuleItemRespDTO displayRuleItemRespDTO = kitchenItemDisplayRuleMap.get(itemGuid);
            if (ObjectUtil.isNull(displayRuleItemRespDTO) || displayRuleItemRespDTO.getRuleType() == DisplayRuleType.OTHER) {
                //不存在匹配的规则
                e.setBatch(Constants.ITEM_NO_RULE_BATCH);
                e.setDisplayRuleType(-1);
                return;
            }
            e.setDisplayRuleType(displayRuleItemRespDTO.getRuleType());
            e.setSort(displayRuleItemRespDTO.getSort());
            e.setBatch(displayRuleItemRespDTO.getBatch());

            if (displayRuleItemRespDTO.getDelayTime() != null && (tradeMode == null || !tradeMode.equals(TradeModeEnum.TAKEOUT.getMode()))) {
                int delayTimeMinutes = displayRuleItemRespDTO.getDelayTime();
                e.setDelayTimeMinutes(delayTimeMinutes);
                e.setDisplayTime(LocalDateTime.now().plusMinutes(delayTimeMinutes));
                if (KdsItemStateEnum.HANG_UP.getCode() == e.getItemState()) {
                    e.setHangUpTime(e.getDisplayTime());
                } else {
                    e.setPrepareTime(e.getDisplayTime());
                }
                e.setOrderSortTime(e.getDisplayTime());
                if (displayRuleItemRespDTO.getRuleType() != DisplayRuleType.SUMMERY) {
                    e.setBatch(Constants.ITEM_DELAYED_BATCH);
                }
            }

        });
    }

    private void setAddItemBatch(ItemPrepareReqDTO itemPrepareReqDTO, List<KitchenItemDO> kitchenItem2Insert) {
        List<DineInItemDTO> inItemDTOList = tradeRpcService.queryItemByOrderGuid(itemPrepareReqDTO.getOrderGuid());
        if (org.springframework.util.CollectionUtils.isEmpty(inItemDTOList)) {
            log.warn("[订单初次加菜]OrderGuid={}", itemPrepareReqDTO.getOrderGuid());
            return;
        }
        Map<LocalDateTime, List<DineInItemDTO>> dateTimeListMap = inItemDTOList.stream()
                .collect(Collectors.groupingBy(DineInItemDTO::getGmtCreate));
        LocalDateTime now = LocalDateTime.now();
        kitchenItem2Insert.forEach(kitchenItemDO -> {
            kitchenItemDO.setAddItemBatch(dateTimeListMap.keySet().size() > 1 ? 1 : 0);
            kitchenItemDO.setAddItemTime(now);
            kitchenItemDO.setFirstAddItemTime(dateTimeListMap.keySet().stream().sorted().findFirst().orElse(now));
        });
    }

    private void addChangeKitchenItemList(List<KitchenItemDO> changeKitchenItemDOList,
                                          KdsItemDTO innerChangesKdsItem,
                                          KitchenItemDO changeKitchenItemDO) {
        BigDecimal currentCount = innerChangesKdsItem.getCurrentCount();
        currentCount = currentCount.divide(changeKitchenItemDO.getCurrentCount(), 0, RoundingMode.UP);
        if (currentCount.compareTo(BigDecimal.ONE) <= 0) {
            return;
        }
        long size;
        try {
            size = Long.parseLong(currentCount.stripTrailingZeros().toPlainString());
        } catch (NumberFormatException e) {
            log.error("currentCount parse int exception, e:", e);
            return;
        }
        List<String> kitchenItemGuidList = distributedIdService.nextBatchKitchenItemGuid(size - 1);
        for (int i = 0; i < size - 1; i++) {
            KitchenItemDO copyChangeKitchenItemDO = new KitchenItemDO();
            BeanUtils.copyProperties(changeKitchenItemDO, copyChangeKitchenItemDO);
            copyChangeKitchenItemDO.setGuid(kitchenItemGuidList.get(i));
            changeKitchenItemDOList.add(copyChangeKitchenItemDO);
        }
    }

    private KitchenItemDO getChangeKitchenItemDO(ChangeKitchenItemBO kitchenItemBO) {
        KitchenItemDO changeKitchenItemDO = new KitchenItemDO();
        KitchenItemDO originalKitchenItemDO = kitchenItemBO.getOriginalKitchenItemDO();
        if (Objects.nonNull(originalKitchenItemDO)) {
            BeanUtils.copyProperties(originalKitchenItemDO, changeKitchenItemDO);
        }

        KdsItemDTO changesKdsItem = kitchenItemBO.getChangesKdsItem();
        changeKitchenItemDO.setId(null);
        changeKitchenItemDO.setGuid(distributedIdService.nextKitchenItemGuid());
        changeKitchenItemDO.setOrderItemGuid(changesKdsItem.getOrderItemGuid());
        changeKitchenItemDO.setItemGuid(changesKdsItem.getItemGuid());
        changeKitchenItemDO.setItemName(changesKdsItem.getItemName());
        changeKitchenItemDO.setSkuGuid(changesKdsItem.getSkuGuid());
        changeKitchenItemDO.setSkuName(changesKdsItem.getSkuName());
        if (StringUtils.isEmpty(changesKdsItem.getSkuName())) {
            changeKitchenItemDO.setSkuName(null);
        }
        changeKitchenItemDO.setSkuUnit(changesKdsItem.getSkuUnit());
        changeKitchenItemDO.setSkuCode(changesKdsItem.getSkuCode());
        changeKitchenItemDO.setIsWeight(changesKdsItem.getIsWeight());
        changeKitchenItemDO.setItemRemark(changesKdsItem.getItemRemark());
        changeKitchenItemDO.setCurrentCount(BigDecimal.ONE);
        if (Boolean.TRUE.equals(changesKdsItem.getIsWeight())) {
            // 非称重商品更换称重商品直接用传入的currentCount
            changeKitchenItemDO.setCurrentCount(changesKdsItem.getCurrentCount());
        }
        addKitchenItemDOProperties(kitchenItemBO, changeKitchenItemDO);
        // 换菜后设备绑定处理
        handleChangeItemDevice(kitchenItemBO, changesKdsItem, changeKitchenItemDO);

        handleChangeItem(kitchenItemBO, changeKitchenItemDO);
        changesKdsItem.setOriginalItemSkuName(changeKitchenItemDO.getOriginalItemSkuName());

        // 改变kds分组的条件，经产品讨论，汇总模式不另外展示，只有订单模式展示换字，因此不用拆分出来
        OrderInfoBO orderInfoBO = new OrderInfoBO();
        orderInfoBO.setOrderRemark(changesKdsItem.getOrderRemark());
        orderInfoBO.setOrderDesc("换菜");
        List<KitchenItemAttrDO> attrList = kitchenItemBO.getAttrList();
        changeKitchenItemDO.setItemAttrMd5(ItemMd5Utils.calItemMd5(orderInfoBO, changesKdsItem, attrList));

        // 制作状态要恢复到未制作，商品状态继承原商品
        changeKitchenItemDO.setKitchenState(KdsKitchenStateEnum.TO_PRD.getCode());

        // 显示规则处理
        Map<String, DisplayRuleItemRespDTO> kitchenItemDisplayRuleMap = kitchenItemBO.getKitchenItemDisplayRuleMap();
        DisplayRuleItemRespDTO displayRuleItemRespDTO = kitchenItemDisplayRuleMap.get(changesKdsItem.getItemGuid());
        changeSetDistributeItemRule(displayRuleItemRespDTO, changeKitchenItemDO, kitchenItemBO.getTradeMode());
        return changeKitchenItemDO;
    }

    private void addKitchenItemDOProperties(ChangeKitchenItemBO kitchenItemBO, KitchenItemDO changeKitchenItemDO) {
        KitchenItemDO originalKitchenItemDO = kitchenItemBO.getOriginalKitchenItemDO();
        if (Objects.nonNull(originalKitchenItemDO)) {
            return;
        }
        changeKitchenItemDO.setStoreGuid(kitchenItemBO.getStoreGuid());
        changeKitchenItemDO.setReturnCount(BigDecimal.ZERO);
        changeKitchenItemDO.setOrderGuid(kitchenItemBO.getOrderGuid());
        changeKitchenItemDO.setDisplayType(KdsTradeModeEnum.getDisplayTypeByCode(kitchenItemBO.getTradeMode()));
        changeKitchenItemDO.setOrderGuid(kitchenItemBO.getOrderGuid());
        changeKitchenItemDO.setOrderDesc(KdsTradeModeEnum.DINE_IN.getDesc());
        if (KdsTradeModeEnum.SNACK.getCode() == kitchenItemBO.getTradeMode()) {
            changeKitchenItemDO.setOrderDesc(KdsTradeModeEnum.SNACK.getDesc());
        }
        changeKitchenItemDO.setOrderNumber(kitchenItemBO.getOrderNo());
        changeKitchenItemDO.setOrderSerialNo(kitchenItemBO.getDiningTableName());
        changeKitchenItemDO.setAreaGuid(kitchenItemBO.getAreaGuid());
        changeKitchenItemDO.setTableGuid(kitchenItemBO.getDiningTableGuid());
        KdsItemDTO changesKdsItem = kitchenItemBO.getChangesKdsItem();
        changeKitchenItemDO.setItemState(changesKdsItem.getItemState());
        LocalDateTime now = LocalDateTime.now();
        changeKitchenItemDO.setOrderSortTime(now);
        if (KdsItemStateEnum.HANG_UP.getCode() == changeKitchenItemDO.getItemState()) {
            changeKitchenItemDO.setHangUpTime(now);
        } else {
            changeKitchenItemDO.setPrepareTime(now);
        }
    }


    private void handleChangeItem(ChangeKitchenItemBO kitchenItemBO,
                                  KitchenItemDO changeKitchenItemDO) {
        KdsItemDTO originalKdsItem = kitchenItemBO.getOriginalKdsItem();
        changeKitchenItemDO.setOriginalItemGuid(originalKdsItem.getItemGuid());
        changeKitchenItemDO.setOriginalSkuGuid(originalKdsItem.getItemGuid());
        changeKitchenItemDO.setOriginalItemSkuName(kitchenItemBO.getOriginalKdsItemName());
    }


    private void changeSetDistributeItemRule(DisplayRuleItemRespDTO displayRuleItemRespDTO,
                                             KitchenItemDO changeKitchenItemDO,
                                             Integer tradeMode) {
        // 检查传入的 displayRuleItemRespDTO 是否为 null 或规则类型为 -1（无匹配规则），若是，则设置相应的批次号和规则类型为无规则
        if (ObjectUtil.isNull(displayRuleItemRespDTO) || displayRuleItemRespDTO.getRuleType() == -1) {
            //不存在匹配的规则
            changeKitchenItemDO.setBatch(Constants.ITEM_NO_RULE_BATCH);
            changeKitchenItemDO.setDisplayRuleType(-1);
            return;
        }
        // 设置显示规则的相关信息
        changeKitchenItemDO.setDisplayRuleType(displayRuleItemRespDTO.getRuleType());
        changeKitchenItemDO.setSort(displayRuleItemRespDTO.getSort());
        changeKitchenItemDO.setBatch(displayRuleItemRespDTO.getBatch());

        // 当延迟时间不为 null 且交易模式不为 2 或交易模式为 null 时，处理延迟显示逻辑
        if (displayRuleItemRespDTO.getDelayTime() != null && (tradeMode == null || tradeMode != 2)) {
            int delayTimeMinutes = displayRuleItemRespDTO.getDelayTime();
            // 设置延迟时间和显示时间
            changeKitchenItemDO.setDelayTimeMinutes(delayTimeMinutes);
            changeKitchenItemDO.setDisplayTime(LocalDateTime.now().plusMinutes(delayTimeMinutes));
            // 根据当前菜品状态，设置挂起时间或准备时间
            if (KdsItemStateEnum.HANG_UP.getCode() == changeKitchenItemDO.getItemState()) {
                changeKitchenItemDO.setHangUpTime(changeKitchenItemDO.getDisplayTime());
            } else {
                changeKitchenItemDO.setPrepareTime(changeKitchenItemDO.getDisplayTime());
            }
            // 设置订单排序时间
            changeKitchenItemDO.setOrderSortTime(changeKitchenItemDO.getDisplayTime());
            // 如果规则类型不为摘要，则设置批次号为延迟批次
            if (displayRuleItemRespDTO.getRuleType() != DisplayRuleType.SUMMERY) {
                changeKitchenItemDO.setBatch(Constants.ITEM_DELAYED_BATCH);
            }
        }
    }

    private Map<String, DisplayRuleItemRespDTO> queryDisplayRule(List<String> itemGuidList, String storeGuid) {
        //设置菜品批次、生效时间、显示类型、菜品序号
        List<DisplayRuleRespDTO> displayRuleRespDTOS = queryStoreRuleList(storeGuid);
        // 查询商品的品牌Guid
        List<ItemInfoRespDTO> mappingRespDTOList = itemRpcService.kdsItemParentMapping(itemGuidList);
        Map<String, String> itemParentGuidMap = new HashMap<>(100);
        mappingRespDTOList.forEach(m -> itemParentGuidMap.put(StringUtils.isEmpty(m.getParentGuid())
                ? m.getItemGuid() : m.getParentGuid(), m.getItemGuid()));
        return getKitchenItemDisplayRuleMap(itemGuidList, displayRuleRespDTOS, itemParentGuidMap);
    }

    /**
     * 合并原菜
     */
    private String mergeOriginalKdsItemList(List<KdsItemDTO> originalKdsItemList) {
        StringBuilder bName = new StringBuilder();
        for (int i = 0; i < originalKdsItemList.size(); i++) {
            KdsItemDTO itemDTO = originalKdsItemList.get(i);
            String itemName = itemDTO.getItemName();
            if (StringUtils.hasText(itemDTO.getSkuName())) {
                itemName = itemName + "(" + itemDTO.getSkuName() + ")";
            }
            bName.append(itemName);
            if (i != originalKdsItemList.size() - 1) {
                bName.append("、");
            }
        }
        return bName.toString();
    }

}
