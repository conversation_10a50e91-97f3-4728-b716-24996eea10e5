package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hsk_store_printer")
public class KdsPrinterDO extends BaseDO {

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 打印机名称
     * 门店下唯一
     */
    private String printerName;

    /**
     * 打印机IP
     */
    private String printerIp;

    /**
     * 打印机端口
     */
    private Integer printerPort;

    /**
     * 打印机页宽
     * 共两种
     * 58mm
     * 80mm
     */
    private Integer pageSize;
}
