package com.holderzone.saas.store.kds.controller;


import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleItemSortDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleItemSortQueryDTO;
import com.holderzone.saas.store.kds.service.DisplayItemSortService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 菜品显示顺序配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
@Slf4j
@AllArgsConstructor
@RestController
@Api(value = "菜品显示顺序配置接口")
@RequestMapping("/display_item_sort")
public class HskDisplayItemSortController {

    private final DisplayItemSortService displayItemSortService;

    /**
     * 更新菜品显示顺序配置
     */
    @ApiOperation("更新菜品显示顺序配置")
    @PostMapping("/save_or_update")
    public void saveOrUpdateItemSortRule(@RequestBody DisplayRuleItemSortDTO reqDTO) {
        log.info("[更新菜品显示顺序配置]入参={},UserGuid={}", JacksonUtils.writeValueAsString(reqDTO), UserContextUtils.getUserGuid());
        displayItemSortService.saveOrUpdateItemSortRule(reqDTO);
    }

    /**
     * 显示批次列表
     */
    @ApiOperation("查询菜品显示顺序配置")
    @PostMapping("/query")
    public DisplayRuleItemSortDTO queryItemSortRule(@RequestBody DisplayRuleItemSortQueryDTO reqDTO) {
        log.info("[查询菜品显示顺序配置]入参={}", JacksonUtils.writeValueAsString(reqDTO));
        return displayItemSortService.queryItemSortRule(reqDTO);
    }

}
