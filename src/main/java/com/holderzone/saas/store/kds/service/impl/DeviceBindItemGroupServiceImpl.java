package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.kds.entity.domain.DeviceBindItemGroupDO;
import com.holderzone.saas.store.kds.mapper.DeviceBindItemGroupMapper;
import com.holderzone.saas.store.kds.service.DeviceBindItemGroupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceBindItemGroupServiceImpl extends ServiceImpl<DeviceBindItemGroupMapper, DeviceBindItemGroupDO>
        implements DeviceBindItemGroupService {

    @Override
    public List<DeviceBindItemGroupDO> listByStoreGuid(String storeGuid) {
        QueryWrapper<DeviceBindItemGroupDO> qw = new QueryWrapper<>();
        qw.lambda().eq(DeviceBindItemGroupDO::getStoreGuid, storeGuid);
        return list(qw);
    }

    @Override
    public List<DeviceBindItemGroupDO> listByDeviceId(String deviceId) {
        QueryWrapper<DeviceBindItemGroupDO> qw = new QueryWrapper<>();
        qw.lambda().eq(DeviceBindItemGroupDO::getDeviceId, deviceId);
        return list(qw);
    }

    @Override
    public List<DeviceBindItemGroupDO> listByDeviceIdAndPointGuid(String deviceId, String pointGuid) {
        QueryWrapper<DeviceBindItemGroupDO> qw = new QueryWrapper<>();
        qw.lambda().eq(DeviceBindItemGroupDO::getDeviceId, deviceId);
        qw.lambda().eq(Objects.nonNull(pointGuid), DeviceBindItemGroupDO::getPointGuid, pointGuid);
        return list(qw);
    }

    @Override
    public void unbind(String storeGuid, String pointGuid, String deviceId) {
        UpdateWrapper<DeviceBindItemGroupDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(DeviceBindItemGroupDO::getStoreGuid, storeGuid);
        uw.lambda().eq(Objects.nonNull(pointGuid), DeviceBindItemGroupDO::getPointGuid, pointGuid);
        uw.lambda().eq(DeviceBindItemGroupDO::getDeviceId, deviceId);
        remove(uw);
    }

    @Override
    public void reInitialize(String storeGuid, String deviceId) {
        UpdateWrapper<DeviceBindItemGroupDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(DeviceBindItemGroupDO::getStoreGuid, storeGuid);
        uw.lambda().eq(DeviceBindItemGroupDO::getDeviceId, deviceId);
        remove(uw);
    }

    @Override
    public void unbind(String groupGuid) {
        UpdateWrapper<DeviceBindItemGroupDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(DeviceBindItemGroupDO::getGroupGuid, groupGuid);
        remove(uw);
    }
}
