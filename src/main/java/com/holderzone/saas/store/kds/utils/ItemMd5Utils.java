package com.holderzone.saas.store.kds.utils;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.kds.req.KdsItemDTO;
import com.holderzone.saas.store.kds.entity.bo.OrderInfoBO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO;
import com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO;
import org.springframework.util.DigestUtils;

import java.util.List;
import java.util.stream.Collectors;

public final class ItemMd5Utils {

    private ItemMd5Utils() {
    }

    /**
     * 整单备注     菜品备注        属性列表
     * 有            有             有             orderItemGuid
     * 有            有             无             orderItemGuid
     * 有            无             有             orderItemGuid
     * 有            无             无             orderItemGuid
     * 无            有             有             orderItemGuid
     * 无            有             无             orderItemGuid
     * 无            无             有             skuGuid + attrGuid
     * 无            无             无             skuGuid
     *
     * @param orderInfoBO 订单信息
     * @param kdsItemDTO  kds商品信息dto
     * @param temp        商品属性
     * @return 商品属性MD5
     */
    public static String calItemMd5(OrderInfoBO orderInfoBO, KdsItemDTO kdsItemDTO, List<KitchenItemAttrDO> temp) {
        String orderRemark = orderInfoBO.getOrderRemark();
        //赚餐到店自餐 默认有整单备注 生成md5时需排除
        if ("赚餐自营外卖".equals(orderInfoBO.getOrderDesc()) && "到店取餐".equals(orderInfoBO.getOrderRemark())) {
            orderRemark = "";
        }
        if (StringUtils.hasText(orderRemark)
                || StringUtils.hasText(kdsItemDTO.getItemRemark())
                || kdsItemDTO.getIsWeight()) {
            String orderItemGuid = kdsItemDTO.getOrderItemGuid();
            if (StringUtils.hasText(kdsItemDTO.getOriginalItemSkuName())) {
                orderItemGuid = orderItemGuid + kdsItemDTO.getOriginalItemSkuName();
            }
            return DigestUtils.md5DigestAsHex(orderItemGuid.getBytes());
        }
        List<String> collect = temp.stream()
                .map(KitchenItemAttrDO::getAttrGuid).sorted().collect(Collectors.toList());
        collect.add(0, kdsItemDTO.getSkuGuid());
        if (StringUtils.hasText(kdsItemDTO.getOriginalItemSkuName())) {
            collect.add(0, kdsItemDTO.getOriginalItemSkuName());
        }
        return DigestUtils.md5DigestAsHex(String.join("", collect).getBytes());
    }

    /**
     * 同上
     *
     * @param orderRemark
     * @param kitchenItemReadDO
     * @return
     */
    public static String calItemMd5(String orderRemark, KitchenItemReadDO kitchenItemReadDO) {
        if (StringUtils.hasText(orderRemark)
                || StringUtils.hasText(kitchenItemReadDO.getItemRemark())
                || kitchenItemReadDO.getIsWeight()) {
            return DigestUtils.md5DigestAsHex(kitchenItemReadDO.getOrderItemGuid().getBytes());
        }
        List<String> collect = kitchenItemReadDO.getAttrs().stream()
                .map(KitchenItemAttrDO::getAttrGuid).sorted().collect(Collectors.toList());
        collect.add(0, kitchenItemReadDO.getSkuGuid());
        return DigestUtils.md5DigestAsHex(String.join("", collect).getBytes());
    }
}
