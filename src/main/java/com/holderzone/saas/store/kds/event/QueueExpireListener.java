package com.holderzone.saas.store.kds.event;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.saas.store.dto.kds.resp.QueueItemDTO;
import com.holderzone.saas.store.kds.constant.RocketMqConfig;
import com.holderzone.saas.store.kds.service.QueueItemService;
import com.holderzone.saas.store.kds.utils.ThrowableExtUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/09/18 20:47
 * @description
 * @program holder-saas-store-takeaway
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.KDS_QUEUE_EXPIRE_TOPIC,
        tags = RocketMqConfig.KDS_QUEUE_EXPIRE_TAG,
        consumerGroup = RocketMqConfig.KDS_QUEUE_EXPIRE_GROUP)
public class QueueExpireListener extends AbstractRocketMqConsumer<RocketMqTopic, QueueItemDTO> {

    private final QueueItemService queueItemService;

    @Autowired
    public QueueExpireListener(QueueItemService queueItemService) {
        this.queueItemService = queueItemService;
    }

    @Override
    public boolean consumeMsg(QueueItemDTO queueItemDTO, MessageExt messageExt) {
        UserContextUtils.put(messageExt.getProperty("userInfo"));
        try {
            EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            queueItemService.expireAndNotify(queueItemDTO);
        } catch (Exception e) {
            log.error("过期队列元素消费失败：{}", ThrowableExtUtils.asStringIfAbsent(e));
            return false;
        } finally {
            EnterpriseIdentifier.remove();
        }
        return true;
    }
}
