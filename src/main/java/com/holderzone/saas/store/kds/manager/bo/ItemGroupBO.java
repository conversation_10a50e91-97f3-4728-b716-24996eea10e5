package com.holderzone.saas.store.kds.manager.bo;

import com.holderzone.saas.store.kds.entity.domain.BindItemDO;
import com.holderzone.saas.store.kds.entity.domain.BindItemGroupDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 菜品分组 BO
 */
@Data
public class ItemGroupBO implements Serializable {

    private static final long serialVersionUID = -8993349661482406553L;

    /**
     * 分组信息
     */
    private BindItemGroupDO bindItemGroup;

    /**
     * 分组下商品列表
     */
    private List<BindItemDO> bindItemList;
}
