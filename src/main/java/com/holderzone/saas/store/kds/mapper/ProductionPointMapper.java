package com.holderzone.saas.store.kds.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.kds.entity.domain.ProductionPointDO;
import com.holderzone.saas.store.kds.entity.read.PointItemReadDO;

import java.util.List;

/**
 * 制作点堂口 Mapper 接口
 *
 * <AUTHOR>
 * @since 2019-03-29
 */
public interface ProductionPointMapper extends BaseMapper<ProductionPointDO> {

    List<PointItemReadDO> queryPrdPointWithItemCount(ProductionPointDO productionPointDO);

    List<PointItemReadDO> queryPrdPointRepeatWithItemCount(ProductionPointDO productionPointDO);

}
