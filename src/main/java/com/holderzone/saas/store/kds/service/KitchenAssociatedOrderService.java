package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.kds.entity.domain.KitchenAssociatedOrderDO;

import java.util.List;


/**
 * 厨房商品联台单 service
 */
public interface KitchenAssociatedOrderService extends IService<KitchenAssociatedOrderDO> {

    void create(KitchenAssociatedOrderDO kitchenAssociatedOrderDO);

    KitchenAssociatedOrderDO getByOrderGuid(String orderGuid);

    List<KitchenAssociatedOrderDO> listByOrderGuids(List<String> orderGuids);
}
