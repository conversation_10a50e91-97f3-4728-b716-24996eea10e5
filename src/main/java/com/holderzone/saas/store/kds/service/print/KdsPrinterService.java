package com.holderzone.saas.store.kds.service.print;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.KdsPrinterRespDTO;
import com.holderzone.saas.store.kds.entity.domain.KdsPrinterDO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface KdsPrinterService extends IService<KdsPrinterDO> {

    void createPrinter(KdsPrinterCreateReqDTO kdsPrinterCreateReqDTO);

    void updatePrinter(KdsPrinterUpdateReqDTO kdsPrinterUpdateReqDTO);

    void deletePrinter(KdsPrinterDeleteReqDTO kdsPrinterDeleteReqDTO);

    Page<KdsPrinterRespDTO> pageAllPrinter(KdsPrinterPageReqDTO kdsPrinterPageReqDTO);

    void bindPrinter(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO);

    void rebindPrinter(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO);

    void unbindPrinter(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO);
}
