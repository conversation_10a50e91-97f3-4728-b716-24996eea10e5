package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.KitchenItemDTO;
import com.holderzone.saas.store.dto.kds.resp.KitchenItemRespDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstRespDTO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface KitchenItemService extends IService<KitchenItemDO> {

    void prepare(ItemPrepareReqDTO itemPrepareReqDTO);

    /**
     * 换菜
     */
    void changes(ItemChangesReqDTO itemChangesReqDTO);

    PrdDstRespDTO queryPrdStatus(PrdItemStatusReqDTO prdItemStatusReqDTO);

    PrdDstRespDTO queryDstStatus(DstItemStatusReqDTO dstItemStatusReqDTO);

    boolean call(ItemCallUpReqDTO itemCallUpReqDTO);

    boolean urge(ItemUrgeReqDTO itemUrgeReqDTO);

    boolean remark(OrderRemarkReqDTO orderRemarkReqDTO);

    boolean changeTable(OrderTableReqDTO orderTableReqDTO);

    boolean refund(ItemBatchRefundReqDTO itemBatchRefundReqDTO);

    void cooking(ItemStateTransReqDTO itemStateTransReqDTO);

    void complete(ItemStateTransReqDTO itemStateTransReqDTO);

    void cancelComplete(ItemStateTransReqDTO itemStateTransReqDTO);

    List<PrdDstItemDTO> distribute(ItemStateTransReqDTO itemStateTransReqDTO);

    void cancelDistribute(ItemCancelDstReqDTO itemCancelDstReqDTO);

    Page<KitchenItemDTO> prdHistory(PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO);

    Page<KitchenItemDTO> dstHistory(PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO);

    void prdPrintAgain(KitchenItemDTO kitchenItemDTO);

    void dstPrintAgain(KitchenItemDTO kitchenItemDTO);

    void autoPrint(String orderGuid,
                   String storeGuid,
                   List<String> kitchenItemGuidList,
                   List<String> deviceGuidList);

    void autoPrint(String orderGuid,
                   String storeGuid,
                   List<String> kitchenItemGuidList,
                   List<String> deviceGuidList, boolean isUrge);

    List<KitchenItemRespDTO> queryByOrderItem(ItemBatchRefundReqDTO query);

    List<PrdDstItemDTO> queryByOrder(PrdDstItemQueryDTO query);

    void batchDistribute(List<ItemStateTransReqDTO> reqDTOList);

    void transfer(KdsItemTransferReqDTO object);
}
