package com.holderzone.saas.store.kds.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.QueueConfigDTO;
import com.holderzone.saas.store.dto.kds.req.QueueQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsQueueRespDTO;
import com.holderzone.saas.store.dto.kds.resp.KitchenItemDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.kds.service.QueueConfigService;
import com.holderzone.saas.store.kds.service.QueueItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Api("KDS队列接口")
@RequestMapping("/kds_queue")
public class QueueController {

    private final QueueConfigService queueConfigService;

    private final QueueItemService queueItemService;

    @Autowired
    public QueueController(QueueConfigService queueConfigService, QueueItemService queueItemService) {
        this.queueConfigService = queueConfigService;
        this.queueItemService = queueItemService;
    }

    @PostMapping("/config/create")
    @ApiOperation(value = "创建取餐屏")
    public void updateConfig(@RequestBody StoreDeviceDTO storeDeviceDTO) {
        if (log.isInfoEnabled()) {
            log.info("创建取餐屏入参:{}", JacksonUtils.writeValueAsString(storeDeviceDTO));
        }
        queueConfigService.create(storeDeviceDTO);
    }

    @PostMapping("/config/query")
    @ApiOperation(value = "查询取餐屏")
    public QueueConfigDTO queryConfig(@Validated(QueueConfigDTO.Query.class)
                                      @RequestBody QueueConfigDTO queueConfigDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询取餐屏入参:{}", JacksonUtils.writeValueAsString(queueConfigDTO));
        }
        return queueConfigService.query(queueConfigDTO);
    }

    @PostMapping("/config/update")
    @ApiOperation(value = "更新取餐屏")
    public void updateConfig(@Validated(QueueConfigDTO.Update.class)
                             @RequestBody QueueConfigDTO queueConfigDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新取餐屏入参:{}", JacksonUtils.writeValueAsString(queueConfigDTO));
        }
        queueConfigService.update(queueConfigDTO);
    }

    @PostMapping("/item/query")
    @ApiOperation(value = "查询KDS队列")
    public KdsQueueRespDTO queryQueueItem(@RequestBody @Validated QueueQueryReqDTO queueQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询KDS队列入参:{}", JacksonUtils.writeValueAsString(queueQueryReqDTO));
        }
        return queueItemService.query(queueQueryReqDTO);
    }

    @PostMapping("/item/call_for_meal")
    @ApiOperation(value = "出堂口呼叫取餐")
    public void call(@RequestBody @Validated KitchenItemDTO kitchenItemDTO) {
        if (log.isInfoEnabled()) {
            log.info("出堂口呼叫取餐入参:{}", JacksonUtils.writeValueAsString(kitchenItemDTO));
        }
        queueItemService.dstCallForMealAgain(kitchenItemDTO);
    }
}

