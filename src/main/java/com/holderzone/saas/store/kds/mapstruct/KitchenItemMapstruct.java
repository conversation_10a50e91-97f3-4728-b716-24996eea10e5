package com.holderzone.saas.store.kds.mapstruct;

import com.holderzone.saas.store.dto.kds.req.ItemChangesReqDTO;
import com.holderzone.saas.store.dto.kds.req.KdsChangesItemDTO;
import com.holderzone.saas.store.dto.kds.req.KdsItemDTO;
import com.holderzone.saas.store.dto.kds.resp.KitchenItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemDO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring")
public interface KitchenItemMapstruct {

    KitchenItemDO fromKdsItemReq(KdsItemDTO kdsItemDTO);

    KitchenItemDTO toKitchenItemDTO(KitchenItemDO kitchenItemDO);

    List<KitchenItemDTO> toKitchenItemDTO(List<KitchenItemDO> kitchenItemDO);

    PrdDstItemDTO kitchenToPrdDstItem(KitchenItemDTO kitchenItemDTO);

    KdsChangesItemDTO itemChangesDTO2KdsChangesItemDTO(ItemChangesReqDTO itemChangesReqDTO);
}
