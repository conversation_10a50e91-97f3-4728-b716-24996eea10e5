package com.holderzone.saas.store.kds.service.print.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.KdsPrinterRespDTO;
import com.holderzone.saas.store.kds.entity.domain.KdsPrinterDO;
import com.holderzone.saas.store.kds.mapper.KdsPrinterMapper;
import com.holderzone.saas.store.kds.mapstruct.KdsPrintMapstruct;
import com.holderzone.saas.store.kds.service.DeviceConfigService;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import com.holderzone.saas.store.kds.service.print.KdsPrinterService;
import com.holderzone.saas.store.kds.utils.PageAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机单据管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class KdsPrinterServiceImpl extends ServiceImpl<KdsPrinterMapper, KdsPrinterDO> implements KdsPrinterService {

    private final DistributedIdService distributedIdService;

    private final DeviceConfigService deviceConfigService;

    private final KdsPrintMapstruct kdsPrintMapstruct;

    @Autowired
    public KdsPrinterServiceImpl(DistributedIdService distributedIdService,
                                 DeviceConfigService deviceConfigService, KdsPrintMapstruct kdsPrintMapstruct) {
        this.distributedIdService = distributedIdService;
        this.deviceConfigService = deviceConfigService;
        this.kdsPrintMapstruct = kdsPrintMapstruct;
    }

    @Override
    public void createPrinter(KdsPrinterCreateReqDTO kdsPrinterCreateReqDTO) {
        KdsPrinterDO kdsPrinterDO = kdsPrintMapstruct.fromCreateReq(kdsPrinterCreateReqDTO);
        checkPrinterName(kdsPrinterDO);
        kdsPrinterDO.setGuid(distributedIdService.nextPrinterGuid());
        save(kdsPrinterDO);
    }

    @Override
    public void updatePrinter(KdsPrinterUpdateReqDTO kdsPrinterUpdateReqDTO) {
        KdsPrinterDO kdsPrinterDO = kdsPrintMapstruct.fromUpdateReq(kdsPrinterUpdateReqDTO);
        checkPrinterGuid(kdsPrinterDO);
        checkPrinterName(kdsPrinterDO);
        update(kdsPrinterDO, wrapperByPrinterGuid(kdsPrinterDO.getGuid()));
    }

    @Override
    public void deletePrinter(KdsPrinterDeleteReqDTO kdsPrinterDeleteReqDTO) {
        KdsPrinterDO kdsPrinterDO = kdsPrintMapstruct.fromDeleteReq(kdsPrinterDeleteReqDTO);
        checkPrinterGuid(kdsPrinterDO);
        remove(wrapperByPrinterGuid(kdsPrinterDO.getGuid()));
        deviceConfigService.unbindPrinter(kdsPrinterDeleteReqDTO.getPrinterGuid());
    }

    @Override
    public Page<KdsPrinterRespDTO> pageAllPrinter(KdsPrinterPageReqDTO kdsPrinterPageReqDTO) {
        String storeGuid = kdsPrinterPageReqDTO.getStoreGuid();
        String deviceId = kdsPrinterPageReqDTO.getDeviceId();
        IPage<KdsPrinterDO> pageResult = page(new PageAdapter<>(kdsPrinterPageReqDTO), wrapperByStoreGuid(storeGuid));
        String printerGuid = deviceConfigService.queryBoundPrinterGuidByDeviceId(storeGuid, deviceId);
        List<KdsPrinterRespDTO> printerList = pageResult.getRecords().stream()
                .map(kdsPrintMapstruct::toKdsPrinterResp)
                .peek(kdsPrinterRespDTO -> {
                    if (kdsPrinterRespDTO.getPrinterGuid().equalsIgnoreCase(printerGuid)) {
                        kdsPrinterRespDTO.setDeviceId(deviceId);
                        kdsPrinterRespDTO.setIsBoundBySelf(true);
                    } else {
                        kdsPrinterRespDTO.setDeviceId(null);
                        kdsPrinterRespDTO.setIsBoundBySelf(false);
                    }
                })
                .collect(Collectors.toList());
        return new PageAdapter<>(pageResult, printerList);
    }

    @Override
    public void bindPrinter(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) {
        KdsPrinterDO kdsPrinterDO = kdsPrintMapstruct.fromBindUnbindReq(kdsPrinterBindUnbindReqDTO);
        checkPrinterGuid(kdsPrinterDO);
        deviceConfigService.bindPrinter(kdsPrinterBindUnbindReqDTO);
    }

    @Override
    public void rebindPrinter(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) {
        KdsPrinterDO kdsPrinterDO = kdsPrintMapstruct.fromBindUnbindReq(kdsPrinterBindUnbindReqDTO);
        checkPrinterGuid(kdsPrinterDO);
        deviceConfigService.rebindPrinter(kdsPrinterBindUnbindReqDTO);
    }

    @Override
    public void unbindPrinter(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) {
        KdsPrinterDO kdsPrinterDO = kdsPrintMapstruct.fromBindUnbindReq(kdsPrinterBindUnbindReqDTO);
        checkPrinterGuid(kdsPrinterDO);
        deviceConfigService.unbindPrinter(kdsPrinterBindUnbindReqDTO);
    }

    private void checkPrinterName(KdsPrinterDO kdsPrinterDO) {
        LambdaQueryWrapper<KdsPrinterDO> wrapper = new LambdaQueryWrapper<KdsPrinterDO>()
                .eq(KdsPrinterDO::getStoreGuid, kdsPrinterDO.getStoreGuid())
                .eq(KdsPrinterDO::getPrinterName, kdsPrinterDO.getPrinterName());

        if (!StringUtils.isEmpty(kdsPrinterDO.getGuid())) {
            wrapper.ne(KdsPrinterDO::getGuid, kdsPrinterDO.getGuid());
        }

        if (count(wrapper) > 0) throw new BusinessException("该门店已存在此名称相同的打印机");
    }

    private void checkPrinterGuid(KdsPrinterDO kdsPrinterDO) {
        if (0 == count(wrapperByPrinterGuid(kdsPrinterDO.getGuid()))) {
            throw new BusinessException("打印机(printerGuid:" + kdsPrinterDO.getGuid() + ")不存在");
        }
    }

    private Wrapper<KdsPrinterDO> wrapperByPrinterGuid(String printerGuid) {
        return new LambdaQueryWrapper<KdsPrinterDO>().eq(KdsPrinterDO::getGuid, printerGuid);
    }

    private Wrapper<KdsPrinterDO> wrapperByStoreGuid(String storeGuid) {
        return new LambdaQueryWrapper<KdsPrinterDO>().eq(KdsPrinterDO::getStoreGuid, storeGuid);
    }
}
