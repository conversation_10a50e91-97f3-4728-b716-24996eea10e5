package com.holderzone.saas.store.kds.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> R
 * @date 2021/1/28 11:59
 * @description
 */
@Component
@Slf4j
public class RedisUtils {

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * @param key
     * @param value
     * @param time 时间 单位：分钟
     * @describle 存json对象
     */
    public void set(String key, Object value, int time) {
        redisTemplate.opsForValue().set(key, value);
        redisTemplate.expire(key, time, TimeUnit.MINUTES);
    }
    /**
     * 获取指定 key 的值
     *
     * @param key
     * @return
     */
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 删除key
     *
     * @param key
     */
    public Boolean delete(String key) {
        Boolean flag = redisTemplate.delete(key);
        if(!flag){
            log.warn("key：{},删除redis，结果：{}", key, flag ? "成功" : "失败");
        }
        return flag;
    }
}
