package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * KDS设备显示菜品
 */
@Data
@TableName("hsk_kitchen_item_device")
public class KitchenItemDeviceDO implements Serializable {

    private static final long serialVersionUID = 4372802362104352727L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 唯一GUID
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 堂口id
     */
    private String pointGuid;

    /**
     * kitchen_item 主键
     */
    private String kitchenItemGuid;

    /**
     * 分组guid
     */
    private String groupGuid;

    /**
     * 老 kitchen_item 主键
     */
    @TableField(exist = false)
    private String originalKitchenItemGuid;
}
