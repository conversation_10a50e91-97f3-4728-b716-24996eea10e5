/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:QueueItemQuery.java
 * Date:2020-1-4
 * Author:terry
 */

package com.holderzone.saas.store.kds.entity.query;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-01-04 下午3:22
 */
@Data
public class QueueItemQuery {

    private String storeGuid;

    private List<String> orderGuidList;
}
