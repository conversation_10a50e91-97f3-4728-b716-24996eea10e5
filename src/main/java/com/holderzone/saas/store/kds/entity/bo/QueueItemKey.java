package com.holderzone.saas.store.kds.entity.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.holderzone.saas.store.kds.entity.domain.QueueItemDO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class QueueItemKey {

    /**
     * 门店GUID
     */
    @TableField(value = "store_guid")
    private String storeGuid;

    /**
     * 设备GUID
     */
    @TableField(value = "device_guid")
    private String deviceGuid;

    /**
     * 订单Guid
     */
    @TableField(value = "order_guid")
    private String orderGuid;

    public static QueueItemKey of(QueueItemDO queueItemDO) {
        QueueItemKey queueItemKey = new QueueItemKey();
        queueItemKey.setStoreGuid(queueItemDO.getStoreGuid());
        queueItemKey.setDeviceGuid(queueItemDO.getDeviceGuid());
        queueItemKey.setOrderGuid(queueItemDO.getOrderGuid());
        return queueItemKey;
    }

    public static QueueItemKey of(String storeGuid, String deviceGuid, String orderGuid) {
        QueueItemKey queueItemKey = new QueueItemKey();
        queueItemKey.setStoreGuid(storeGuid);
        queueItemKey.setDeviceGuid(deviceGuid);
        queueItemKey.setOrderGuid(orderGuid);
        return queueItemKey;
    }
}