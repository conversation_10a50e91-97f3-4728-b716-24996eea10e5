package com.holderzone.saas.store.kds.utils.print;

import java.math.BigDecimal;
import java.math.RoundingMode;

public final class BigDecimalUtils {

    public static String quantityTrimmed(BigDecimal quantity) {
        BigDecimal moneyScale0 = quantity.setScale(0, RoundingMode.HALF_UP);
        if (quantity.compareTo(moneyScale0) == 0) return moneyScale0.toString();
        BigDecimal moneyScale1 = quantity.setScale(1, RoundingMode.HALF_UP);
        if (quantity.compareTo(moneyScale1) == 0) return moneyScale1.toString();
        BigDecimal moneyScale2 = quantity.setScale(2, RoundingMode.HALF_UP);
        if (quantity.compareTo(moneyScale2) == 0) return moneyScale2.toString();
        return quantity.setScale(3, RoundingMode.HALF_UP).toString();
    }

    public static BigDecimal nonNullValue(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }
}
