package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.kds.req.PrdPointDelReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdPointItemBindReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdPointItemQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.DistributeItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdPointItemDTO;
import com.holderzone.saas.store.kds.entity.domain.PrdPointItemDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface PrdPointItemService extends IService<PrdPointItemDO> {

    void bindItem(PrdPointItemBindReqDTO prdPointItemBindReqDTO);

    void unbindItem(PrdPointItemBindReqDTO prdPointItemBindReqDTO);

    void unbindItem(PrdPointDelReqDTO prdPointDelReqDTO);

    void reInitialize(String storeGuid, String deviceId);

    List<PrdPointItemDO> queryBoundItem(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO);

    /**
     * 根据商品sku查询绑定菜品
     *
     * @param prdPointItemQueryReqDTO
     * @return
     */
    List<PrdPointItemDTO> queryBoundItemBySku(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO);

    Map<String, PrdPointItemDO> queryPrdPointByItem(List<String> skuGuidList,String storeGuid);

    List<PrdPointItemDTO> queryPrdPointBySku(SingleDataDTO reqDTO);

    /**
     * 查询kds所有绑定的菜品
     * @return
     */
    List<PrdPointItemDTO> queryAllBoundItem();
}
