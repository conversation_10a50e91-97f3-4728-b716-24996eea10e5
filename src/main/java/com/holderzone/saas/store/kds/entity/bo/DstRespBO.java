package com.holderzone.saas.store.kds.entity.bo;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.PageDTO;
import com.holderzone.saas.store.dto.kds.ItemComparable;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleItemSortDTO;
import com.holderzone.saas.store.dto.kds.resp.DstGroupItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstOrderDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.entity.group.OrderGroupKey;
import com.holderzone.saas.store.kds.entity.read.GroupBindItemReadDO;
import com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO;
import com.holderzone.saas.store.kds.utils.SplitUtils;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

@Slf4j
@Data
@Builder
public class DstRespBO extends BaseRespBO {

    /**
     * 是否为单菜品模式
     */
    private Boolean isSingleMode;

    /**
     * 是否为菜品汇总模式
     */
    private Boolean isSummaryMode;

    /**
     * 是否显示所有模式的商品
     */
    private Boolean isAllType;

    private DisplayRuleItemSortDTO itemSortDTO;

    public Page<PrdDstOrderDTO> getDstOrderPage(PageDTO pageDTO, List<KitchenItemReadDO> itemInfo) {
        return reasonable2Page(pageDTO, getDstOrderList(itemInfo));
    }

    public List<PrdDstOrderDTO> getDstOrderList(List<KitchenItemReadDO> items) {
        return items.stream()
                .collect(groupingBy(OrderGroupKey::of, LinkedHashMap::new, toList()))
                .entrySet().parallelStream()
                .map(this::getDstOrderDTO)
                .filter(o -> !CollectionUtils.isEmpty(o.getItems()))
                .sorted(ItemComparable::compareTo)
                .collect(Collectors.toList());
    }

    public List<DstGroupItemDTO> getDstBindGroupItemList(List<GroupBindItemReadDO> records,
                                                         List<GroupBindItemReadDO> groupBindItemReadList) {
        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        Map<String, GroupBindItemReadDO> groupBindItemReadMap = groupBindItemReadList.stream()
                .collect(Collectors.toMap(GroupBindItemReadDO::getGuid, Function.identity(),
                        (key1, key2) -> key1));
        for (GroupBindItemReadDO itemReadDO : records) {
            GroupBindItemReadDO groupBindItemReadDO = groupBindItemReadMap.get(itemReadDO.getGuid());
            if (Objects.nonNull(groupBindItemReadDO)) {
                itemReadDO.setItems(groupBindItemReadDO.getItems());
            }
        }
        List<DstGroupItemDTO> groupItemDTOList = records.parallelStream()
                .map(this::getDstGroupItemDTO)
                .collect(toList());
        // 菜品顺序调整： 相同sku放到一起，以第一个sku的位置往后加
        sortedGroupItemDTOList(groupItemDTOList);
        return groupItemDTOList;
    }

    private void sortedGroupItemDTOList(List<DstGroupItemDTO> groupItemDTOList) {
        if (CollectionUtils.isEmpty(groupItemDTOList)) {
            return;
        }
        for (DstGroupItemDTO dstGroupItemDTO : groupItemDTOList) {
            List<PrdDstItemDTO> items = dstGroupItemDTO.getItems();
            if (CollectionUtils.isEmpty(items)) {
                continue;
            }
            Map<String, List<PrdDstItemDTO>> groupSkuMap = Maps.newLinkedHashMap();
            List<String> sortedSkuGuids = Lists.newArrayList();
            for (PrdDstItemDTO item : items) {
                String skuGuid = item.getSkuGuid();
                if (!sortedSkuGuids.contains(skuGuid)) {
                    sortedSkuGuids.add(skuGuid);
                }
                List<PrdDstItemDTO> groupBySkuGuidList = groupSkuMap.getOrDefault(skuGuid, Lists.newArrayList());
                groupBySkuGuidList.add(item);
                groupSkuMap.put(skuGuid, groupBySkuGuidList);
            }
            log.info("sortedSkuGuids:{}", JacksonUtils.writeValueAsString(sortedSkuGuids));
            List<List<PrdDstItemDTO>> prdDstItemList = sortedSkuGuids.stream()
                    .map(skuGuid -> groupSkuMap.getOrDefault(skuGuid, Lists.newArrayList()))
                    .collect(toList());
            dstGroupItemDTO.setItems(prdDstItemList.stream().flatMap(Collection::stream).collect(toList()));
        }
    }


    private DstGroupItemDTO getDstGroupItemDTO(GroupBindItemReadDO groupBindItemReadDO) {
        DstGroupItemDTO dstGroupItemDTO = new DstGroupItemDTO();
        dstGroupItemDTO.setGroupGuid(groupBindItemReadDO.getGuid());
        dstGroupItemDTO.setGroupName(groupBindItemReadDO.getGroupName());
        if (CollectionUtils.isEmpty(groupBindItemReadDO.getItems())) {
            dstGroupItemDTO.setItems(Collections.emptyList());
        } else {
            dstGroupItemDTO.setItems(getDstItemListNew(groupBindItemReadDO.getItems()));
        }
        return dstGroupItemDTO;
    }

    private PrdDstOrderDTO getDstOrderDTO(Map.Entry<OrderGroupKey, List<KitchenItemReadDO>> orderEntry) {
        OrderGroupKey orderInfo = orderEntry.getKey();
        List<KitchenItemReadDO> itemInfo = orderEntry.getValue();
        PrdDstOrderDTO prdDstOrderDTO = new PrdDstOrderDTO();
        prdDstOrderDTO.setOrderGuid(orderInfo.getOrderGuid());
        prdDstOrderDTO.setOrderType(orderInfo.getDisplayType());
        prdDstOrderDTO.setOrderDesc(orderInfo.getOrderDesc());
        prdDstOrderDTO.setOrderNumber(orderInfo.getOrderNumber());
        prdDstOrderDTO.setOrderSerialNo(orderInfo.getOrderSerialNo());
        prdDstOrderDTO.setOrderRemark(orderInfo.getOrderRemark());
        List<PrdDstItemDTO> prdDstItemList = getDstItemList(itemInfo);
        prdDstOrderDTO.setItems(prdDstItemList);
        if (!CollectionUtils.isEmpty(prdDstItemList)) {
            PrdDstItemDTO prdDstItemDTO = prdDstItemList.get(0);
            prdDstOrderDTO.setUrgedTime(prdDstItemDTO.getUrgedTime());
            prdDstOrderDTO.setCallUpTime(prdDstItemDTO.getCallUpTime());
            prdDstOrderDTO.setPrepareTime(prdDstItemDTO.getPrepareTime());
            prdDstOrderDTO.setHangUpTime(prdDstItemDTO.getHangUpTime());
            prdDstOrderDTO.setAddItemTime(prdDstItemDTO.getAddItemTime());
            prdDstOrderDTO.setFirstAddItemTime(prdDstItemDTO.getFirstAddItemTime());
        }
        return prdDstOrderDTO;
    }

    public Page<PrdDstItemDTO> getDstItemPage(PageDTO pageDTO, List<KitchenItemReadDO> itemInfo) {
        return reasonable2Page(pageDTO, getDstItemList(itemInfo));
    }

    public List<PrdDstItemDTO> getDstItemList(List<KitchenItemReadDO> itemInfo) {
        Stream<List<KitchenItemReadDO>> stream;
        if (isSingleMode) {
            stream = itemInfo.stream().map(Collections::singletonList);
        } else {
            Function<KitchenItemReadDO, String> classifier = isSummaryMode
                    ? KitchenItemReadDO::getItemAttrMd5
                    : KitchenItemReadDO::getOrderItemGuid;
            stream = itemInfo.parallelStream()
                    .collect(groupingBy(classifier, LinkedHashMap::new, toList()))
                    .values().parallelStream();
        }
        return stream
                .flatMap(orderItems -> {
                    // 移除模式不匹配的商品
                    if (!isSingleMode) {
                        orderItems.removeIf(o -> !o.isTypeMatched(isAllType, !isSummaryMode));
                        if (orderItems.isEmpty()) return Stream.empty();
                    }
                    // 全局排序
                    orderItems.sort(ItemComparable::compareTo);
                    // 菜品基础信息
                    KitchenItemReadDO kitchenItemReadDO = orderItems.get(0);
                    PrdDstItemDTO prdDstItemDTO = getPrdDstItemDTO(kitchenItemReadDO);
                    setIsAddItem(prdDstItemDTO);
                    DeviceConfigDO dstDeviceConfig = Optional
                            .ofNullable(kitchenItemReadDO.getDstDeviceConfig())
                            .orElse(DeviceConfigDO.defaultConfig());
                    DeviceConfigDO prdDeviceConfig = Optional
                            .ofNullable(kitchenItemReadDO.getPrdDeviceConfig())
                            .orElse(DeviceConfigDO.defaultConfig());
                    Predicate<KitchenItemReadDO> predicateThatWhetherCookingIncluded = prdDeviceConfig.getIsManualConfirm()
                            ? KitchenItemReadDO::isCompleted : KitchenItemReadDO::isCooking;
                    Predicate<KitchenItemReadDO> predicate = !dstDeviceConfig.getIsDisplayItemUnProduced()
                            ? predicateThatWhetherCookingIncluded : !prdDeviceConfig.getIsProduceHangedItem()
                            ? KitchenItemReadDO::isCookingIncludeNormalState : KitchenItemReadDO::isCookingIncludeAllTheState;
                    List<KitchenItemReadDO> normalItems = orderItems.stream().filter(predicate).collect(Collectors.toList());
                    if (normalItems.isEmpty()) {
                        return Stream.empty();
                    }
                    // 称重商品
                    if (kitchenItemReadDO.getIsWeight()) {
                        return normalItems.stream().map(o -> calItemWeightInfo(prdDstItemDTO, o));
                    }
                    // 非称重商品，根据制作点、出堂口的配置过滤、拆分商品
                    List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(normalItems, -1);
                    return IntStream.range(0, batchItems.size())
                            .mapToObj(index -> calItemBatchInfo(index, prdDstItemDTO, batchItems.get(index)));
                })
                .sorted(ItemComparable::compareTo)
                .collect(Collectors.toList());
    }

    public List<PrdDstItemDTO> getDstItemListNew(List<KitchenItemReadDO> itemInfo) {
        Stream<List<KitchenItemReadDO>> stream;
        if (isSingleMode) {
            stream = itemInfo.stream().map(Collections::singletonList);
        } else {
            boolean itemSort = ObjectUtils.isEmpty(itemSortDTO) || itemSortDTO.getItemSortType() == BooleanEnum.FALSE.getCode();
            Function<KitchenItemReadDO, String> classifier = isSummaryMode
                    ? (itemSort ? KitchenItemReadDO::getItemAttrMd5 : KitchenItemReadDO::getUniqueSkuKey2)
                    : KitchenItemReadDO::getOrderItemGuid;
            stream = itemInfo.parallelStream()
                    .collect(groupingBy(classifier, LinkedHashMap::new, toList()))
                    .values().parallelStream().flatMap(List::stream)
                    .collect(Collectors.groupingBy((itemSort ? KitchenItemReadDO::getItemAttrMd5 : KitchenItemReadDO::getUniqueSkuKey2)))
                    .values().parallelStream();
        }
        return stream
                .flatMap(orderItems -> {
                    // 移除模式不匹配的商品
                    if (!isSingleMode) {
                        orderItems.removeIf(o -> !o.isTypeMatched(isAllType, !isSummaryMode));
                        if (orderItems.isEmpty()) return Stream.empty();
                    }
                    // 全局排序
                    orderItems.sort(ItemComparable::compareTo);
                    // 菜品基础信息
                    KitchenItemReadDO kitchenItemReadDO = orderItems.get(0);
                    PrdDstItemDTO prdDstItemDTO = getPrdDstItemDTO(kitchenItemReadDO);
                    setIsAddItem(prdDstItemDTO);
                    if (orderItems.isEmpty()) {
                        return Stream.empty();
                    }
                    // 称重商品
                    if (kitchenItemReadDO.getIsWeight()) {
                        return orderItems.stream().map(o -> calItemWeightInfo(prdDstItemDTO, o));
                    }
                    // 非称重商品，根据制作点、出堂口的配置过滤、拆分商品
                    List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(orderItems, -1);
                    return IntStream.range(0, batchItems.size())
                            .mapToObj(index -> calItemBatchInfo(index, prdDstItemDTO, batchItems.get(index)));
                })
                .sorted(ItemComparable::compareTo)
                .collect(Collectors.toList());
    }

    public List<PrdDstItemDTO> getSummaryDstItemListNew(List<KitchenItemReadDO> itemInfo) {
        Stream<List<KitchenItemReadDO>> stream;
        if (isSingleMode) {
            stream = itemInfo.stream().map(Collections::singletonList);
        } else {
            boolean itemSort = ObjectUtils.isEmpty(itemSortDTO) || itemSortDTO.getItemSortType() == BooleanEnum.FALSE.getCode();
            Function<KitchenItemReadDO, String> classifier = isSummaryMode
                    ? (itemSort ? KitchenItemReadDO::getItemAttrMd5 : KitchenItemReadDO::getUniqueSkuKey2)
                    : KitchenItemReadDO::getOrderItemGuid;
            stream = itemInfo.parallelStream()
                    .collect(groupingBy(classifier, LinkedHashMap::new, toList()))
                    .values().parallelStream();
        }
        return stream
                .flatMap(orderItems -> {
                    // 移除模式不匹配的商品
                    if (!isSingleMode) {
                        orderItems.removeIf(o -> !o.isTypeMatched(isAllType, !isSummaryMode));
                        if (orderItems.isEmpty()) return Stream.empty();
                    }
                    // 全局排序
                    orderItems.sort(ItemComparable::compareTo);
                    // 菜品基础信息
                    KitchenItemReadDO kitchenItemReadDO = orderItems.get(0);
                    PrdDstItemDTO prdDstItemDTO = getPrdDstItemDTO(kitchenItemReadDO);
                    setIsAddItem(prdDstItemDTO);
                    if (orderItems.isEmpty()) {
                        return Stream.empty();
                    }
                    // 称重商品
                    if (kitchenItemReadDO.getIsWeight()) {
                        return orderItems.stream().map(o -> calItemWeightInfo(prdDstItemDTO, o));
                    }
                    // 非称重商品，根据制作点、出堂口的配置过滤、拆分商品
                    List<List<KitchenItemReadDO>> batchItems = SplitUtils.splitList(orderItems, -1);
                    return IntStream.range(0, batchItems.size())
                            .mapToObj(index -> calItemBatchInfo(index, prdDstItemDTO, batchItems.get(index)));
                })
                .sorted(ItemComparable::compareTo2)
                .collect(Collectors.toList());
    }

    private void setIsAddItem(PrdDstItemDTO prdDstItemDTO) {
        if (!ObjectUtils.isEmpty(prdDstItemDTO.getAddItemBatch()) && prdDstItemDTO.getAddItemBatch() > 0
                && (!ObjectUtils.isEmpty(itemSortDTO) && itemSortDTO.getItemSortType().equals(BooleanEnum.TRUE.getCode()))) {
            if (itemSortDTO.getItemDisplayType().equals(BooleanEnum.TRUE.getCode())) {
                Duration duration = Duration.between(prdDstItemDTO.getFirstAddItemTime(), prdDstItemDTO.getAddItemTime());
                long minutes = duration.toMinutes();
                if (minutes > itemSortDTO.getItemIntervalTime()) {
                    prdDstItemDTO.setIsAddItem(true);
                }
            } else {
                prdDstItemDTO.setIsAddItem(true);
            }

        }
    }
}
