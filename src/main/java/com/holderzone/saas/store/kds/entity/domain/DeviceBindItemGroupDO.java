package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备绑定菜品分组
 */
@Data
@TableName("hsk_device_bind_item_group")
public class DeviceBindItemGroupDO implements Serializable {

    private static final long serialVersionUID = 5377341171103591269L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 唯一GUID
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @TableLogic
    @ApiModelProperty(value = "是否删除")
    private Integer isDelete;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 分组guid
     */
    private String groupGuid;

    /**
     * 堂口guid
     */
    private String pointGuid;

    /**
     * 设备id
     */
    private String deviceId;

}
