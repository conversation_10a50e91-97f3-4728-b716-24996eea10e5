package com.holderzone.saas.store.kds.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.holderzone.saas.store.kds.entity.read.KdsPrintRecordReadDO;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/5/23
 */
@Configuration
@EnableCaching
@SuppressWarnings("Duplicates")
public class RedisConfig extends CachingConfigurerSupport {

//    private final RedisProperties redisProperties;
//
//    @Autowired
//    public RedisConfig2(RedisProperties redisProperties) {
//        this.redisProperties = redisProperties;
//    }
//
//    @Bean
//    public RedisConnectionFactory redisConnectionFactory() {
//        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();
//        redisStandaloneConfiguration.setHostName(redisProperties.getHost());
//        redisStandaloneConfiguration.setPort(redisProperties.getPort());
//        redisStandaloneConfiguration.setDatabase(redisProperties.getDatabase());
//        redisStandaloneConfiguration.setPassword(RedisPassword.of(redisProperties.getPassword()));
//        return new JedisConnectionFactory(redisStandaloneConfiguration, jedisClientConfiguration());
//    }
//
//    @Bean
//    public JedisClientConfiguration jedisClientConfiguration() {
//        JedisClientConfiguration.JedisPoolingClientConfigurationBuilder builder =
//                (JedisClientConfiguration.JedisPoolingClientConfigurationBuilder) JedisClientConfiguration.builder();
//        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
//        RedisProperties.Pool pool = redisProperties.getJedis().getPool();
//        jedisPoolConfig.setMaxIdle(pool.getMaxIdle());
//        jedisPoolConfig.setMaxTotal(pool.getMinIdle());
//        jedisPoolConfig.setMaxWaitMillis(pool.getMaxWait().toMillis());
//        return builder.poolConfig(jedisPoolConfig).build();
//    }

    @Bean
    public RedisCacheManager redisCacheManager(RedisConnectionFactory connectionFactory) {
        return RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(redisCacheConfiguration())
                .transactionAware()
                .build();
    }

    @Bean
    public RedisCacheConfiguration redisCacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofSeconds(3600))
                .disableCachingNullValues();
    }

    @Bean
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory connectionFactory) {
        return new StringRedisTemplate(connectionFactory);
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplateObject(RedisConnectionFactory connectionFactory) {
        // KeySerializer
        RedisSerializer<String> keySerializer = new StringRedisSerializer();
        // ValueSerializer
        Jackson2JsonRedisSerializer<Object> valueSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        valueSerializer.setObjectMapper(objectMapper());
        // redisTemplate
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(keySerializer);
        template.setValueSerializer(valueSerializer);
        template.setHashKeySerializer(keySerializer);
        template.setHashValueSerializer(valueSerializer);
        template.afterPropertiesSet();
        // return bean
        return template;
    }

    @Bean
    public RedisTemplate<String, KdsPrintRecordReadDO> redisTemplateKdsPrintRecord(RedisConnectionFactory connectionFactory) {
        // KeySerializer
        RedisSerializer<String> keySerializer = new StringRedisSerializer();
        // ValueSerializer
        Jackson2JsonRedisSerializer<KdsPrintRecordReadDO> valueSerializer =
                new Jackson2JsonRedisSerializer<>(KdsPrintRecordReadDO.class);
        valueSerializer.setObjectMapper(objectMapper());
        // redisTemplate
        RedisTemplate<String, KdsPrintRecordReadDO> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(keySerializer);
        template.setValueSerializer(valueSerializer);
        template.setHashKeySerializer(keySerializer);
        template.setHashValueSerializer(valueSerializer);
        template.afterPropertiesSet();
        // return bean
        return template;
    }

    @Bean
    public RedisTemplate<String, List<KdsPrintRecordReadDO>> redisTemplateListKdsPrintRecord(RedisConnectionFactory connectionFactory) {
        // KeySerializer
        RedisSerializer<String> keySerializer = new StringRedisSerializer();
        // ValueSerializer
        ObjectMapper objectMapper = objectMapper();
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, KdsPrintRecordReadDO.class);
        Jackson2JsonRedisSerializer<List<KdsPrintRecordReadDO>> valueSerializer = new Jackson2JsonRedisSerializer<>(javaType);
        valueSerializer.setObjectMapper(objectMapper);
        // redisTemplate
        RedisTemplate<String, List<KdsPrintRecordReadDO>> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(keySerializer);
        template.setValueSerializer(valueSerializer);
        template.setHashKeySerializer(keySerializer);
        template.setHashValueSerializer(valueSerializer);
        template.afterPropertiesSet();
        // return bean
        return template;
    }

    private ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.findAndRegisterModules();
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        return objectMapper;
    }
}
