package com.holderzone.saas.store.kds.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;


/**
 * KDS菜品绑定 入厨方式
 */
@Component
public class KitchenItemHandlerFactory {

    private final BaseKitchenItemHandler singleKitchenItemHandler;

    private final BaseKitchenItemHandler multiKitchenItemHandler;

    @Autowired
    public KitchenItemHandlerFactory(@Qualifier("singleKitchenItemHandler") BaseKitchenItemHandler singleKitchenItemHandler,
                                     @Qualifier("multiKitchenItemHandler") BaseKitchenItemHandler multiKitchenItemHandler) {
        this.singleKitchenItemHandler = singleKitchenItemHandler;
        this.multiKitchenItemHandler = multiKitchenItemHandler;
    }

    public BaseKitchenItemHandler create(Boolean allowRepeatFlag) {
        if (Boolean.TRUE.equals(allowRepeatFlag)) {
            return multiKitchenItemHandler;
        }
        return singleKitchenItemHandler;
    }
}
