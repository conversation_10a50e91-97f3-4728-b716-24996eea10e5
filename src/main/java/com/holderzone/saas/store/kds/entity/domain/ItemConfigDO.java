package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.saas.store.kds.constant.Constants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hsk_item_config")
public class ItemConfigDO extends BaseDO {

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 规格Id
     */
    private String skuGuid;

    /**
     * 超时时间：分钟
     */
    private Integer timeout;

    /**
     * 最大制作份数
     */
    private Integer maxCopies;

    /**
     * “订单+汇总模式”下，菜品的“显示模式”
     */
    private Integer displayType;

    /**
     * 默认配置
     *
     * @return
     */
    public static ItemConfigDO defaultConfig() {
        ItemConfigDO itemConfigDO = new ItemConfigDO();
        itemConfigDO.setTimeout(Constants.ITEM_TIMEOUT);
        itemConfigDO.setMaxCopies(Constants.ITEM_MAX_COPIES);
        itemConfigDO.setDisplayType(Constants.ITEM_DISPLAY_TYPE);
        return itemConfigDO;
    }
}
