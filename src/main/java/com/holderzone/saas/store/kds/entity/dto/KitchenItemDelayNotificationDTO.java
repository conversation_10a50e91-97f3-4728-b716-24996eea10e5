package com.holderzone.saas.store.kds.entity.dto;

import com.holderzone.saas.store.dto.kds.req.ItemPrepareReqDTO;
import lombok.Data;

import java.util.List;

@Data
public class KitchenItemDelayNotificationDTO {
    private String storeGuid;

    private String orderGuid;
    //总共延迟时间
    private Integer delayTimeTotal;
    //剩余延迟时间
    private Integer delayTimeLeft;
    private List<String> kitchenItemGuidList;
    private List<String> deviceGuidList;
}
