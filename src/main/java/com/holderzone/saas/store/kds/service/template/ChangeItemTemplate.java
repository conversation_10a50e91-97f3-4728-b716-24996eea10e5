package com.holderzone.saas.store.kds.service.template;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.kds.req.KdsItemDTO;
import com.holderzone.saas.store.dto.kds.req.KdsPrintChangesItemDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsAttrGroupDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsItemAttrDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.*;
import com.holderzone.saas.store.kds.utils.print.BigDecimalUtils;
import com.holderzone.saas.store.kds.utils.print.PrintRowUtils;
import com.holderzone.saas.store.util.LocaleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 换菜单模板
 */
@Slf4j
public class ChangeItemTemplate extends BaseItemTemplate<KdsPrintChangesItemDTO> {

    private static final String INDENT = " ";

    @Override
    public List<PrintRow> getPrintRows() {
        List<PrintRow> printRows = new ArrayList<>();
        KdsPrintChangesItemDTO printDTO = getPrintDTO();
        // 单据名称
        PrintRowUtils.add(printRows, new Section()
                .addText(LocaleUtil.getMessage("change_note_list_invoice_header"), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));
        // 空白行
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(Strings.EMPTY, Font.SMALL)
                .setValueString(Strings.EMPTY, Font.SMALL));
        // 桌台号
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(LocaleUtil.getMessage("table_name") + printDTO.getDiningTableName(), Font.SMALL)
                .setValueString(Strings.EMPTY, Font.SMALL));
        // 订单号
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(LocaleUtil.getMessage("order_number") + printDTO.getOrderNo(), Font.SMALL)
                .setValueString(Strings.EMPTY, Font.SMALL));
        // ------------------------------------------------
        // 换菜信息
        addChangeItemRows(printRows);
        // ------------------------------------------------
        // 操作员 + 更换时间
        String changeTime = DateTimeUtils.localDateTime2String(printDTO.getGmtCreate(), "MM-dd HH:mm");
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(LocaleUtil.getMessage("operator_user") + printDTO.getCreateStaffName(), Font.SMALL)
                .setValueString(LocaleUtil.getMessage("change_time") + changeTime, Font.SMALL));
        // 打印时间
        String printTime = DateTimeUtils.localDateTime2String(printDTO.getCreateTime(), "MM-dd HH:mm");
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(Strings.EMPTY, Font.SMALL)
                .setValueString(LocaleUtil.getMessage("prt_time") + printTime, Font.SMALL));
        return printRows;
    }

    @Override
    public String getPrintFailedMsg() {
        return "换菜单打印失败，请及时处理";
    }


    /**
     * 更换菜品明细
     */
    private void addChangeItemRows(List<PrintRow> printRows) {
        KdsPrintChangesItemDTO printDTO = getPrintDTO();
        // 构建商品换菜table
        Table table = buildChangeItemTable();
        // 换菜table内容
        // 原菜品
        table.addRow(Lists.newArrayList(
                new Text(LocaleUtil.getMessage("original_item_name"), Font.NORMAL),
                Text.EMPTY));
        List<KdsItemDTO> originalKdsItemList = printDTO.getOriginalKdsItemList();
        if (Boolean.TRUE.equals(printDTO.getCancelFlag())) {
            // 过滤原菜品展示
            filterOriginalItem(printDTO, originalKdsItemList);
        }
        for (KdsItemDTO originalKdsItem : originalKdsItemList) {
            // 商品名称
            String originalItemName = originalKdsItem.getItemName();
            if (!StringUtils.isEmpty(originalKdsItem.getSkuName())) {
                originalItemName = originalItemName + " (" + originalKdsItem.getSkuName() + ")";
            }
            table.addRow(Lists.newArrayList(
                    new Text(INDENT + originalItemName, Font.NORMAL_BOLD),
                    new Text("X" + BigDecimalUtils.quantityTrimmed(originalKdsItem.getCurrentCount()), Font.NORMAL_BOLD)));
            // 如果有属性
            addChangeItemAttrRows(table, originalKdsItem);
            // 如果有备注
            addChangeItemRemarkRows(table, originalKdsItem);
        }
        // 更换为
        table.addRow(Lists.newArrayList(
                new Text(LocaleUtil.getMessage("change_item_name"), Font.NORMAL),
                Text.EMPTY));
        // 更换后菜品集合
        List<KdsItemDTO> changesKdsItemList = printDTO.getChangesKdsItemList();
        if (Boolean.FALSE.equals(printDTO.getCancelFlag())) {
            // 过滤原菜品展示
            filterChangeItem(printDTO, changesKdsItemList);
        }
        for (KdsItemDTO kdsItemDTO : changesKdsItemList) {
            // 商品名称
            String changeItemName = kdsItemDTO.getItemName();
            if (!StringUtils.isEmpty(kdsItemDTO.getSkuName())) {
                changeItemName = changeItemName + " (" + kdsItemDTO.getSkuName() + ")";
            }
            table.addRow(Lists.newArrayList(
                    new Text(INDENT + changeItemName, Font.NORMAL_BOLD),
                    new Text("X" + BigDecimalUtils.quantityTrimmed(kdsItemDTO.getCurrentCount()), Font.NORMAL_BOLD)));
            // 如果有属性
            addChangeItemAttrRows(table, kdsItemDTO);
            // 如果有备注
            addChangeItemRemarkRows(table, kdsItemDTO);
        }
        PrintRowUtils.add(printRows, table);
    }

    /**
     * 过滤商品展示
     */
    private void filterOriginalItem(KdsPrintChangesItemDTO printDTO, List<KdsItemDTO> originalKdsItemList) {
        List<KdsItemDTO> changesKdsItemList = printDTO.getChangesKdsItemList();
        KdsItemDTO changeItemDTO = changesKdsItemList.get(0);
        // 当前设备绑定的商品
        List<String> arrayOfItemGuid = printDTO.getArrayOfItemGuid();
        if (arrayOfItemGuid.contains(changeItemDTO.getSkuGuid())) {
            return;
        }
        originalKdsItemList.removeIf(e -> !arrayOfItemGuid.contains(e.getSkuGuid()));
    }

    /**
     * 过滤商品展示
     */
    private void filterChangeItem(KdsPrintChangesItemDTO printDTO, List<KdsItemDTO> changesKdsItemList) {
        List<KdsItemDTO> originalKdsItemList = printDTO.getOriginalKdsItemList();
        KdsItemDTO originalItemDTO = originalKdsItemList.get(0);
        // 当前设备绑定的商品
        List<String> arrayOfItemGuid = printDTO.getArrayOfItemGuid();
        if (arrayOfItemGuid.contains(originalItemDTO.getSkuGuid())) {
            return;
        }
        changesKdsItemList.removeIf(e -> !arrayOfItemGuid.contains(e.getSkuGuid()));
    }

    /**
     * 构建商品换菜table
     */
    private Table buildChangeItemTable() {
        int pageSize = getPageSize();
        List<Text> headers = Lists.newArrayList(Text.BLANK, Text.BLANK);
        List<Integer> columnWidths = 58 == pageSize ? Lists.newArrayList(24, 8) : Lists.newArrayList(40, 8);
        List<Boolean> alignRights = Lists.newArrayList(false, true);
        return new Table(headers, columnWidths, alignRights, true);
    }


    /**
     * 更换菜品属性明细
     */
    private void addChangeItemAttrRows(Table table, KdsItemDTO kdsItem) {
        List<KdsAttrGroupDTO> attrGroupList = kdsItem.getAttrGroup();
        if (CollectionUtils.isEmpty(attrGroupList)) {
            return;
        }
        for (KdsAttrGroupDTO kdsAttrGroupDTO : attrGroupList) {
            List<String> attrNameList = kdsAttrGroupDTO.getAttrs().stream().map(KdsItemAttrDTO::getAttrName).collect(Collectors.toList());
            String attr = kdsAttrGroupDTO.getGroupName() + ":" + String.join(",", attrNameList);
            table.addRow(Lists.newArrayList(
                    new Text(INDENT + INDENT + attr, Font.SMALL),
                    Text.BLANK));
        }
    }

    /**
     * 更换菜品备注
     */
    private void addChangeItemRemarkRows(Table table, KdsItemDTO kdsItem) {
        String itemRemark = kdsItem.getItemRemark();
        if (StringUtils.isEmpty(itemRemark)) {
            return;
        }
        table.addRow(Lists.newArrayList(
                new Text(INDENT + INDENT + LocaleUtil.getMessage("dish_remarks") + itemRemark, Font.SMALL),
                Text.BLANK));
    }
}
