package com.holderzone.saas.store.kds.entity.bo;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import lombok.Data;

@Data
public class AsyncTask<T> {

    private String userInfo;

    private String enterpriseGuid;

    private T data;

    public static <T> AsyncTask<T> wrapper(T data) {
        AsyncTask<T> asyncTask = new AsyncTask<>();
        asyncTask.data = data;
        asyncTask.userInfo = UserContextUtils.getJsonStr();
        asyncTask.enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        return asyncTask;
    }
}
