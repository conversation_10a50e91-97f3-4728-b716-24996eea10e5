package com.holderzone.saas.store.kds.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleQueryDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayStoreRespDTO;
import com.holderzone.saas.store.kds.service.DisplayStoreService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @description 显示门店
 * @date 2021/1/27 17:03
 */
@Slf4j
@RestController
@RequestMapping("/display_store")
@AllArgsConstructor
public class DisplayStoreController {

    private final DisplayStoreService displayStoreService;

    /**
     * 查询kds门店列表
     *
     * @param reqDTO DisplayRuleQueryDTO
     * @return List<DisplayStoreRespDTO>
     */
    @ApiOperation(value = "kds门店列表")
    @PostMapping("/item_store")
    public List<DisplayStoreRespDTO> listStore(@RequestBody DisplayRuleQueryDTO reqDTO) {
        log.info("查询商品列表入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return displayStoreService.listStore(reqDTO);
    }


}
