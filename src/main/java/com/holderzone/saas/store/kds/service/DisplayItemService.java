package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleQueryDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayItemRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DisplayItemDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 显示商品
 * @date 2021/1/29 16:41
 */
public interface DisplayItemService extends IService<DisplayItemDO> {

    /**
     * 查询kds商品列表
     *
     * @param reqDTO DisplayRuleQueryDTO
     * @return List<DisplayItemRespDTO>
     */
    List<DisplayItemRespDTO> listItem(DisplayRuleQueryDTO reqDTO);

    /**
     * 实时查询商品信息
     * @param itemGuidList  商品guid
     * @param displayItemRespDTOS  商品DTO
     * @return
     */
    List<DisplayItemRespDTO> queryItemInfo(List<String> itemGuidList,List<DisplayItemRespDTO> displayItemRespDTOS);


    Map<String, ItemWebRespDTO> mapSkuForName(List<String> skuGuidList);
}

