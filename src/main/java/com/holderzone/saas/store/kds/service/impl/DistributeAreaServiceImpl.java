package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.dto.kds.req.DeviceQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.DstBindStatusRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DistributeAreaDO;
import com.holderzone.saas.store.kds.mapper.DistributeAreaMapper;
import com.holderzone.saas.store.kds.service.DistributeAreaService;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机单据管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class DistributeAreaServiceImpl extends ServiceImpl<DistributeAreaMapper, DistributeAreaDO> implements DistributeAreaService {

    private final DistributedIdService distributedIdService;

    @Autowired
    public DistributeAreaServiceImpl(DistributedIdService distributedIdService) {
        this.distributedIdService = distributedIdService;
    }

    @Override
    public DstBindStatusRespDTO queryAreaBindingPreview(DeviceQueryReqDTO deviceQueryReqDTO) { // 区域绑定情况预览
        List<DistributeAreaDO> distributeAreaInSql = list(new LambdaQueryWrapper<DistributeAreaDO>()
                .select(DistributeAreaDO::getAreaGuid)
                .eq(DistributeAreaDO::getStoreGuid, deviceQueryReqDTO.getStoreGuid())
                .eq(DistributeAreaDO::getDeviceId, deviceQueryReqDTO.getDeviceId()));
        List<String> boundAreaGuidList = distributeAreaInSql.stream()
                .map(DistributeAreaDO::getAreaGuid)
                .collect(Collectors.toList());
        DstBindStatusRespDTO dstAreaRespDTO = new DstBindStatusRespDTO();
        dstAreaRespDTO.setIsSnackBound(boundAreaGuidList.remove(DistributeAreaDO.SNACK_AREA_GUID));
        dstAreaRespDTO.setIsTakeoutBound(boundAreaGuidList.remove(DistributeAreaDO.TAKEOUT_AREA_GUID));
        dstAreaRespDTO.setBoundAreaCount(boundAreaGuidList.size());
        return dstAreaRespDTO;
    }

    @Override
    public List<DistributeAreaDO> queryBoundAreaOfStore(String storeGuid) { // 查询门店下已绑定区域
        return list(new LambdaQueryWrapper<DistributeAreaDO>()
                .select(DistributeAreaDO::getDeviceId,
                        DistributeAreaDO::getAreaGuid)
                .eq(DistributeAreaDO::getStoreGuid, storeGuid));
    }

    @Override
    public List<DistributeAreaDO> queryBoundAreaOfDevice(String storeGuid, String deviceId) { // 查询某设备绑定的区域
        return list(new LambdaQueryWrapper<DistributeAreaDO>()
                .select(DistributeAreaDO::getDeviceId,
                        DistributeAreaDO::getAreaGuid)
                .eq(DistributeAreaDO::getStoreGuid, storeGuid)
                .eq(DistributeAreaDO::getDeviceId, deviceId));
    }

    @Override
    public List<String> queryBoundAreaGuidOfDevice(String storeGuid, String deviceId) { // 查询某设备绑定的区域
        List<DistributeAreaDO> distributeAreaInSql = list(new LambdaQueryWrapper<DistributeAreaDO>()
                .select(DistributeAreaDO::getAreaGuid)
                .eq(DistributeAreaDO::getStoreGuid, storeGuid)
                .eq(DistributeAreaDO::getDeviceId, deviceId));
        if (CollectionUtils.isEmpty(distributeAreaInSql)) return Collections.emptyList();
        return distributeAreaInSql.stream().map(DistributeAreaDO::getAreaGuid).collect(Collectors.toList());
    }

    @Override
    public List<String> queryBoundAreaGuidOfDeviceList(String storeGuid, List<String> deviceIdList) { // 查询门店下，指定设备列表的条件下，绑定的区域
        if (CollectionUtils.isEmpty(deviceIdList)) return Collections.emptyList();
        List<DistributeAreaDO> distributeAreaInSql = list(new LambdaQueryWrapper<DistributeAreaDO>()
                .select(DistributeAreaDO::getAreaGuid)
                .eq(DistributeAreaDO::getStoreGuid, storeGuid)
                .in(DistributeAreaDO::getDeviceId, deviceIdList));
        if (CollectionUtils.isEmpty(distributeAreaInSql)) return Collections.emptyList();
        return distributeAreaInSql.stream().map(DistributeAreaDO::getAreaGuid).collect(Collectors.toList());
    }

    @Override
    public List<String> queryOccupiedDeviceId(String storeGuid, String deviceId) { // 查询门店下已占用区域关联的设备ID，但是不包含本设备id，即绑定了该区域的其他设备
        List<DistributeAreaDO> distributeAreaInSql = list(new LambdaQueryWrapper<DistributeAreaDO>()
                .select(DistributeAreaDO::getAreaGuid)
                .eq(DistributeAreaDO::getStoreGuid, storeGuid)
                .eq(DistributeAreaDO::getDeviceId, deviceId));
        if (CollectionUtils.isEmpty(distributeAreaInSql)) return Collections.emptyList();
        List<String> occupiedAreaGuid = distributeAreaInSql.stream()
                .map(DistributeAreaDO::getAreaGuid).collect(Collectors.toList());
        distributeAreaInSql = list(new LambdaQueryWrapper<DistributeAreaDO>()
                .select(DistributeAreaDO::getDeviceId)
                .eq(DistributeAreaDO::getStoreGuid, storeGuid)
                .ne(DistributeAreaDO::getDeviceId, deviceId)
                .in(DistributeAreaDO::getAreaGuid, occupiedAreaGuid));
        return distributeAreaInSql.stream().map(DistributeAreaDO::getDeviceId).collect(Collectors.toList());
    }

    @Override
    public void simpleSaveBatchArea(String storeGuid, String deviceId, List<String> toBeBoundAreaGuid) { // 为设备绑定区域
        if (CollectionUtils.isEmpty(toBeBoundAreaGuid)) return;
        List<String> guids = distributedIdService.nextBatchDstAreaGuid(toBeBoundAreaGuid.size());
        List<DistributeAreaDO> distributeArea2Insert = toBeBoundAreaGuid.stream()
                .map(areaGuid -> {
                    DistributeAreaDO distributeAreaDO = new DistributeAreaDO();
                    distributeAreaDO.setGuid(guids.remove(guids.size() - 1));
                    distributeAreaDO.setStoreGuid(storeGuid);
                    distributeAreaDO.setDeviceId(deviceId);
                    distributeAreaDO.setAreaGuid(areaGuid);
                    return distributeAreaDO;
                })
                .collect(Collectors.toList());
        saveBatch(distributeArea2Insert);
    }

    @Override
    public void simpleRemoveBatchArea(String storeGuid, String deviceId, List<String> toBeRemoveAreaGuid) { // 设备区域解绑
        if (CollectionUtils.isEmpty(toBeRemoveAreaGuid)) return;
        remove(new LambdaQueryWrapper<DistributeAreaDO>()
                .eq(DistributeAreaDO::getStoreGuid, storeGuid)
                .eq(DistributeAreaDO::getDeviceId, deviceId)
                .in(DistributeAreaDO::getAreaGuid, toBeRemoveAreaGuid));
    }

    @Override
    public void reInitialize(String storeGuid, String deviceId) { // 移除设备所绑定的所有区域
        remove(new LambdaQueryWrapper<DistributeAreaDO>()
                .eq(DistributeAreaDO::getStoreGuid, storeGuid)
                .eq(DistributeAreaDO::getDeviceId, deviceId));
    }

    @Override
    public Map<String, List<String>> queryDstAreaMap(String storeGuid, List<String> areaGuidList) { // deviceID -> 区域集合列表
        if (CollectionUtils.isEmpty(areaGuidList)) return Collections.emptyMap();
        return list(new LambdaQueryWrapper<DistributeAreaDO>()
                .select(DistributeAreaDO::getDeviceId,
                        DistributeAreaDO::getAreaGuid)
                .in(DistributeAreaDO::getAreaGuid, areaGuidList)
                .eq(DistributeAreaDO::getStoreGuid, storeGuid)).stream()
                .collect(groupingBy(DistributeAreaDO::getDeviceId, mapping(DistributeAreaDO::getAreaGuid, toList())));
    }
}
