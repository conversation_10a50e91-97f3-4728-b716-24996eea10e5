package com.holderzone.saas.store.kds.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.saas.store.dto.kds.resp.PrdDstOrderDTO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemDO;
import com.holderzone.saas.store.kds.entity.query.ItemQuery;
import com.holderzone.saas.store.kds.entity.read.GroupBindItemReadDO;
import com.holderzone.saas.store.kds.entity.read.KitchenItemReadDO;
import com.holderzone.saas.store.kds.entity.read.PointItemReadDO;
import com.holderzone.saas.store.kds.utils.PageAdapter;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 厨房商品 Mapper 接口
 *
 * <AUTHOR>
 * @since 2019-03-29
 */
public interface KitchenItemMapper extends BaseMapper<KitchenItemDO> {

    List<KitchenItemReadDO> queryPrepareItem(ItemQuery itemQuery);

    List<PointItemReadDO> queryPrdPointItem(ItemQuery itemQuery);

    List<GroupBindItemReadDO> queryDstGroupBindItem(ItemQuery itemQuery);

    List<KitchenItemReadDO> queryPrdItem(ItemQuery itemQuery);

    IPage<PrdDstOrderDTO> queryPrdDstOrderAndItem(PageAdapter<PrdDstOrderDTO> page, @Param("request") ItemQuery itemQuery);

    List<KitchenItemReadDO> queryPrdItemByOrder(@Param("request") ItemQuery itemQuery);

    IPage<KitchenItemReadDO> queryPrdItemGroup(PageAdapter<KitchenItemReadDO> page, @Param("request") ItemQuery itemQuery);

    List<KitchenItemReadDO> queryPrdItemBySku(@Param("request") ItemQuery itemQuery);

    List<KitchenItemReadDO> queryDstItem(ItemQuery itemQuery);

    List<KitchenItemReadDO> queryDstItemAll(ItemQuery itemQuery);

    IPage<KitchenItemReadDO> pageDstItemAll(PageAdapter<KitchenItemReadDO> page, @Param("request") ItemQuery itemQuery);

    IPage<PrdDstOrderDTO> pageDstOrderItem(PageAdapter<PrdDstOrderDTO> page, @Param("request") ItemQuery itemQuery);

    List<KitchenItemReadDO> queryDstItemByOrder(@Param("request") ItemQuery itemQuery);

    IPage<KitchenItemReadDO> pageDstItemGroup(PageAdapter<KitchenItemReadDO> page, @Param("request") ItemQuery itemQuery);

    List<KitchenItemReadDO> queryDstItemBySku(@Param("request") ItemQuery itemQuery);

    List<KitchenItemReadDO> queryOrderItem(ItemQuery itemQuery);

    Integer updateOrderRemark(List<KitchenItemDO> kitchenItemList);

    void cancelDistribute(KitchenItemDO kitchenItemDO);

    @Deprecated
    void increaseUrgedTimes(@Param("orderItemGuidList") List<String> orderItemGuidList);

    void batchUpdateById(List<KitchenItemDO> updateKitchenItemDOList);
}
