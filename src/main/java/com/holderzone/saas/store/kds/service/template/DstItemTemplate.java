package com.holderzone.saas.store.kds.service.template;

import com.holderzone.saas.store.dto.kds.req.KdsPrintPrdDstDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.convertable.Text;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.kds.utils.DigitalUtils;
import com.holderzone.saas.store.kds.utils.print.PrintRowUtils;
import com.holderzone.saas.store.util.LocaleUtil;

import java.util.ArrayList;
import java.util.List;

public class DstItemTemplate extends BaseItemTemplate<KdsPrintPrdDstDTO> {

    @Override
    public List<PrintRow> getPrintRows() {
        List<PrintRow> printRows = new ArrayList<>();

        // 单据名称
        PrintRowUtils.add(printRows, new Section()
                .addText(LocaleUtil.getMessage("dispatch_note_list_invoice_header"), Font.NORMAL_BOLD)
                .setAlign(Text.Align.Center));

        PrintRowUtils.add(printRows, new Separator());

        printRows.addAll(super.getPrintRows());

        return printRows;
    }

    @Override
    public String getPrintFailedMsg() {
        KdsPrintPrdDstDTO printDTO = getPrintDTO();
        return "出堂单" + DigitalUtils.humanize(printDTO.getOrderNumber()) + "打印失败";
    }
}
