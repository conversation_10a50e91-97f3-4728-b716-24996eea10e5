package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.kds.entity.domain.DisplayRepeatItemDO;


/**
 * 菜品重复绑定配置
 */
public interface DisplayRepeatItemService extends IService<DisplayRepeatItemDO> {

    void saveOrUpdateConfig(DisplayRepeatItemDO displayRepeatItemDO);

    /**
     * 查询门店配置 是否允许重复绑定商品
     */
    Boolean queryAllowRepeatFlag(String storeGuid);

    DisplayRepeatItemDO getByBrandGuid(String brandGuid);

}
