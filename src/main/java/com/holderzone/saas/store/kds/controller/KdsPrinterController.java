package com.holderzone.saas.store.kds.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.KdsPrinterRespDTO;
import com.holderzone.saas.store.kds.service.print.KdsPrinterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Api("KDS设备接口")
@RequestMapping("/kds_printer")
public class KdsPrinterController {

    private final KdsPrinterService kdsPrinterService;

    @Autowired
    public KdsPrinterController(KdsPrinterService kdsPrinterService) {
        this.kdsPrinterService = kdsPrinterService;
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建门店打印机")
    public void create(@RequestBody @Validated KdsPrinterCreateReqDTO kdsPrinterCreateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("创建门店打印机入参:{}", JacksonUtils.writeValueAsString(kdsPrinterCreateReqDTO));
        }
        kdsPrinterService.createPrinter(kdsPrinterCreateReqDTO);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改门店打印机")
    public void update(@RequestBody @Validated KdsPrinterUpdateReqDTO kdsPrinterUpdateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("修改门店打印机入参:{}", JacksonUtils.writeValueAsString(kdsPrinterUpdateReqDTO));
        }
        kdsPrinterService.updatePrinter(kdsPrinterUpdateReqDTO);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除门店打印机")
    public void delete(@RequestBody @Validated KdsPrinterDeleteReqDTO kdsPrinterDeleteReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("删除门店打印机入参:{}", JacksonUtils.writeValueAsString(kdsPrinterDeleteReqDTO));
        }
        kdsPrinterService.deletePrinter(kdsPrinterDeleteReqDTO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "查询门店门店打印机列表")
    public Page<KdsPrinterRespDTO> page(@RequestBody @Validated KdsPrinterPageReqDTO kdsPrinterPageReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询门店门店打印机列表入参:{}", JacksonUtils.writeValueAsString(kdsPrinterPageReqDTO));
        }
        return kdsPrinterService.pageAllPrinter(kdsPrinterPageReqDTO);
    }

    @PostMapping("/bind")
    @ApiOperation(value = "KDS设备绑定打印机")
    public void bind(@RequestBody @Validated(KdsPrinterBindUnbindReqDTO.Bind.class)
                             KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("KDS设备绑定打印机入参:{}", JacksonUtils.writeValueAsString(kdsPrinterBindUnbindReqDTO));
        }
        kdsPrinterService.bindPrinter(kdsPrinterBindUnbindReqDTO);
    }

    @PostMapping("/rebind")
    @ApiOperation(value = "KDS设备重新绑定打印机")
    public void rebind(@RequestBody @Validated(KdsPrinterBindUnbindReqDTO.ReBind.class)
                               KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("KDS设备重新绑定打印机入参:{}", JacksonUtils.writeValueAsString(kdsPrinterBindUnbindReqDTO));
        }
        kdsPrinterService.rebindPrinter(kdsPrinterBindUnbindReqDTO);
    }

    @PostMapping("/unbind")
    @ApiOperation(value = "KDS设备解绑打印机")
    public void unbind(@RequestBody @Validated(KdsPrinterBindUnbindReqDTO.UnBind.class)
                               KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("KDS设备解绑打印机入参:{}", JacksonUtils.writeValueAsString(kdsPrinterBindUnbindReqDTO));
        }
        kdsPrinterService.unbindPrinter(kdsPrinterBindUnbindReqDTO);
    }
}

