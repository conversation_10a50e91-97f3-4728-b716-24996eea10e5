package com.holderzone.saas.store.kds.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleQueryDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleSaveOrUpdateDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayItemRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayRuleRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayStoreRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.kds.entity.MPPage;
import com.holderzone.saas.store.kds.entity.domain.DisplayItemDO;
import com.holderzone.saas.store.kds.entity.domain.DisplayRuleDO;
import com.holderzone.saas.store.kds.entity.domain.DisplayStoreDO;
import com.holderzone.saas.store.kds.entity.enums.DisplayRuleTypeEnum;
import com.holderzone.saas.store.kds.mapper.DisplayRuleMapper;
import com.holderzone.saas.store.kds.mapstruct.DisplayRuleMapstruct;
import com.holderzone.saas.store.kds.service.DisplayItemService;
import com.holderzone.saas.store.kds.service.DisplayRuleService;
import com.holderzone.saas.store.kds.service.DisplayStoreService;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import com.holderzone.saas.store.kds.service.rpc.StoreClientService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.kds.constant.Constants.*;

/**
 * <AUTHOR>
 * @description
 * @date 2021/1/27 17:07
 */

@Slf4j
@Service
@AllArgsConstructor
public class DisplayRuleServiceImpl extends ServiceImpl<DisplayRuleMapper, DisplayRuleDO> implements DisplayRuleService {

    private static final Integer ZERO = 0;

    private static final Integer ONE = 1;

    private final DisplayRuleMapper ruleMapper;

    private final DisplayStoreService storeService;

    private final DisplayItemService itemService;

    private final DisplayRuleMapstruct ruleMapstruct;

    private final DistributedIdService distributedIdService;

    @Resource
    private StoreClientService storeClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveRule(DisplayRuleSaveOrUpdateDTO reqDTO) {

        checkParam(reqDTO);
        String ruleGuid = distributedIdService.nextDisplayRuleGuid();

        // 菜品规则绑定
        List<DisplayItemRespDTO> itemList = reqDTO.getItemList();
        checkItem(reqDTO, itemList);
        List<DisplayItemDO> itemDOList = ruleMapstruct.toItemDOList(itemList);
        List<String> itemGuidList = distributedIdService.nextBatchDisplayItemGuid(itemDOList.size());
        itemDOList.forEach(itemDO -> {
            itemDO.setGuid(itemGuidList.remove(itemGuidList.size() - 1));
            if (ObjectUtils.isEmpty(itemDO.getSort())) {
                int sort = itemService.count(new LambdaQueryWrapper<DisplayItemDO>()
                        .eq(DisplayItemDO::getRuleType, reqDTO.getRuleType())) + 1;
                itemDO.setSort(sort);
            }
            itemDO.setRuleGuid(ruleGuid);
            itemDO.setRuleType(reqDTO.getRuleType());
        });
        boolean saveItem = itemService.saveBatch(itemDOList);

        // 门店规则绑定
        List<DisplayStoreRespDTO> storeList = reqDTO.getStoreList();
        checkStore(reqDTO, storeList);
        List<DisplayStoreDO> storeDOList = getDisplayStoreDOList(reqDTO, ruleGuid, storeList);
        boolean saveStore = storeService.saveBatch(storeDOList);

        // 显示规则保存
        DisplayRuleDO displayRuleDO = ruleMapstruct.ruleReqToRuleDO(reqDTO);
        displayRuleDO.setGuid(ruleGuid);
        displayRuleDO.setBrandGuid(reqDTO.getBrandGuid());
        boolean saveRule = this.save(displayRuleDO);

        return saveItem && saveStore && saveRule;
    }

    private void checkStore(DisplayRuleSaveOrUpdateDTO reqDTO, List<DisplayStoreRespDTO> storeList) {
        if (DisplayRuleTypeEnum.ITEM_SUMMARY.getCode().equals(reqDTO.getRuleType())) {
            DisplayRuleQueryDTO queryDTO = new DisplayRuleQueryDTO();
            queryDTO.setRuleType(reqDTO.getRuleType());
            List<DisplayStoreRespDTO> respDTOList = storeService.listStore(queryDTO);
            List<String> guidList = respDTOList.stream()
                    .map(DisplayStoreRespDTO::getStoreGuid)
                    .collect(Collectors.toList());
            storeList.forEach(item -> {
                if (guidList.contains(item.getStoreGuid())) {
                    throw new BusinessException("门店{" + item.getStoreName() + "}已经被使用");
                }
            });
        }
    }

    private void checkItem(DisplayRuleSaveOrUpdateDTO reqDTO, List<DisplayItemRespDTO> itemList) {
        if (DisplayRuleTypeEnum.DISPLAY_BATCH.getCode().equals(reqDTO.getRuleType())) {
            DisplayRuleQueryDTO queryDTO = new DisplayRuleQueryDTO();
            queryDTO.setRuleType(reqDTO.getRuleType());
            List<DisplayItemRespDTO> listItem = itemService.listItem(queryDTO);
            List<String> guidList = listItem.stream()
                    .map(DisplayItemRespDTO::getItemGuid)
                    .collect(Collectors.toList());
            itemList.forEach(item -> {
                if (guidList.contains(item.getItemGuid())) {
                    throw new BusinessException("商品{" + item.getItemName() + "}已经被使用");
                }
            });
        }
    }

    private void checkParam(DisplayRuleSaveOrUpdateDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO.getRuleType())) {
            throw new BusinessException(RULE_TYPE_CANNOT_BE_EMPTY);
        }
        if (DisplayRuleTypeEnum.DISPLAY_BATCH.getCode().equals(reqDTO.getRuleType())) {
            // 显示规则的特殊校验，菜品汇总可以为空
            if (ObjectUtils.isEmpty(reqDTO.getDisplayState())) {
                throw new BusinessException("显示状态不能为空");
            }
            if (ZERO.equals(reqDTO.getDisplayState()) && ObjectUtils.isEmpty(reqDTO.getDelayTime())) {
                throw new BusinessException("延迟显示时延迟时间不能为空");
            }
            if (ONE.equals(reqDTO.getDisplayState()) && ObjectUtils.isEmpty(reqDTO.getBatch())) {
                throw new BusinessException("分批显示时批次不能为空");
            }
        }
        if (ObjectUtils.isEmpty(reqDTO.getBrandGuid())) {
            throw new BusinessException(BRAND_GUID_CANNOT_BE_EMPTY);
        }
        if (ObjectUtils.isEmpty(reqDTO.getEffectiveState())) {
            throw new BusinessException("生效状态不能为空");
        }
        if (ZERO.equals(reqDTO.getEffectiveState()) && ObjectUtils.isEmpty(reqDTO.getEffectiveTime())) {
            throw new BusinessException("延迟生效时生效时间不能为空");
        }
        if (ObjectUtils.isEmpty(reqDTO.getIsAllStore())) {
            reqDTO.setIsAllStore(Boolean.TRUE);
        }
        if (ObjectUtils.isEmpty(reqDTO.getRuleType())) {
            throw new BusinessException("规则类型不能为空");
        }
        if (CollectionUtils.isEmpty(reqDTO.getItemList())) {
            throw new BusinessException("商品列表不能为空");
        }
        if (!reqDTO.getIsAllStore() && CollectionUtils.isEmpty(reqDTO.getStoreList())) {
            throw new BusinessException("门店列表不能为空");
        }
    }

    @Override
    public Page<DisplayRuleRespDTO> batchList(DisplayRuleQueryDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO.getRuleType())) {
            throw new BusinessException(RULE_TYPE_CANNOT_BE_EMPTY);
        }
        if (ObjectUtils.isEmpty(reqDTO.getBrandGuid())) {
            throw new BusinessException(BRAND_GUID_CANNOT_BE_EMPTY);
        }
        Page<DisplayRuleRespDTO> pageResult = new Page<>(reqDTO.getCurrentPage(), reqDTO.getPageSize());
        MPPage<DisplayRuleDO> ruleDOIPageQuery = new MPPage<>(reqDTO.getCurrentPage(), reqDTO.getPageSize());
        IPage<DisplayRuleDO> ruleDOIPage = this.page(ruleDOIPageQuery, new LambdaQueryWrapper<DisplayRuleDO>()
                .eq(DisplayRuleDO::getIsDelete, ZERO)
                .eq(DisplayRuleDO::getRuleType, reqDTO.getRuleType())
                .eq(DisplayRuleDO::getBrandGuid, reqDTO.getBrandGuid())
                .orderByAsc(DisplayRuleDO::getBatch)
        );
        if (CollectionUtils.isEmpty(ruleDOIPage.getRecords())) {
            log.info("查询规则为空");
            return new Page<>();
        }
        List<DisplayRuleRespDTO> respDTOList = ruleMapstruct.toRuleRespDTOList(ruleDOIPage.getRecords());
        for (int i = 0; i < respDTOList.size(); i++) {
            DisplayRuleRespDTO ruleDTO = respDTOList.get(i);
            List<DisplayItemRespDTO> itemRespDTOList = getDisplayItemRespDTOList(ruleDTO.getGuid(), reqDTO.getRuleType(), reqDTO.getBrandGuid());
            ruleDTO.setItemList(itemRespDTOList);
            if (!ruleDTO.getIsAllStore()) {
                List<DisplayStoreRespDTO> storeRespDTOList = getDisplayStoreRespDTOList(ruleDTO.getGuid(), ruleDTO.getRuleType());
                ruleDTO.setStoreList(storeRespDTOList);
            }
            ruleDTO.setRuleSort(i + ONE);
        }
        pageResult.setTotalCount(this.count(new LambdaQueryWrapper<DisplayRuleDO>()
                .eq(DisplayRuleDO::getIsDelete, ZERO)
                .eq(DisplayRuleDO::getRuleType, reqDTO.getRuleType())
                .eq(DisplayRuleDO::getBrandGuid, reqDTO.getBrandGuid()))
        );
        pageResult.setData(respDTOList);
        return pageResult;
    }

    @Override
    public Boolean deleteRule(DisplayRuleQueryDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO.getRuleGuid())) {
            throw new BusinessException(RULE_GUID_CANNOT_BE_EMPTY);
        }
        if (ObjectUtils.isEmpty(reqDTO.getRuleType())) {
            throw new BusinessException(RULE_TYPE_CANNOT_BE_EMPTY);
        }
        boolean removeRule = this.remove(new LambdaQueryWrapper<DisplayRuleDO>()
                .eq(DisplayRuleDO::getGuid, reqDTO.getRuleGuid())
                .eq(DisplayRuleDO::getRuleType, reqDTO.getRuleType())
        );
        boolean removeStore = storeService.remove(new LambdaQueryWrapper<DisplayStoreDO>()
                .eq(DisplayStoreDO::getRuleGuid, reqDTO.getRuleGuid())
                .eq(DisplayStoreDO::getRuleType, reqDTO.getRuleType())
        );
        boolean removeItem = itemService.remove(new LambdaQueryWrapper<DisplayItemDO>()
                .eq(DisplayItemDO::getRuleGuid, reqDTO.getRuleGuid())
                .eq(DisplayItemDO::getRuleType, reqDTO.getRuleType())
        );
        return removeRule && removeStore && removeItem;
    }

    @Override
    public DisplayRuleRespDTO getRule(DisplayRuleQueryDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO.getRuleGuid())) {
            throw new BusinessException(RULE_GUID_CANNOT_BE_EMPTY);
        }
        if (ObjectUtils.isEmpty(reqDTO.getRuleType())) {
            throw new BusinessException(RULE_TYPE_CANNOT_BE_EMPTY);
        }
        DisplayRuleDO ruleDO = this.getOne(new LambdaQueryWrapper<DisplayRuleDO>()
                .eq(DisplayRuleDO::getGuid, reqDTO.getRuleGuid())
                .eq(DisplayRuleDO::getIsDelete, ZERO)
                .eq(DisplayRuleDO::getRuleType, reqDTO.getRuleType())
        );
        DisplayRuleRespDTO ruleRespDTO = ruleMapstruct.toRuleRespDTO(ruleDO);
        if (ObjectUtils.isEmpty(ruleRespDTO.getBatch())) {
            ruleRespDTO.setBatch(null);
        }
        List<DisplayItemRespDTO> itemRespDTOList = getDisplayItemRespDTOList(reqDTO.getRuleGuid(), reqDTO.getRuleType(), reqDTO.getBrandGuid());
        ruleRespDTO.setItemList(itemRespDTOList);
        List<DisplayStoreRespDTO> storeRespDTOList = getDisplayStoreRespDTOList(reqDTO.getRuleGuid(), reqDTO.getRuleType());
        ruleRespDTO.setStoreList(storeRespDTOList);
        return ruleRespDTO;
    }

    private List<DisplayStoreRespDTO> getDisplayStoreRespDTOList(String ruleGuid, Integer ruleType) {
        List<DisplayStoreDO> storeDOList = storeService.list(new LambdaQueryWrapper<DisplayStoreDO>()
                .eq(DisplayStoreDO::getRuleGuid, ruleGuid)
                .eq(DisplayStoreDO::getRuleType, ruleType)
                .eq(DisplayStoreDO::getIsDelete, ZERO)
        );
        List<DisplayStoreRespDTO> displayStoreRespDTOS = ruleMapstruct.toStoreRespList(storeDOList);
        if (!CollectionUtils.isEmpty(displayStoreRespDTOS)) {
            List<String> storeList = displayStoreRespDTOS.stream().map(DisplayStoreRespDTO::getStoreGuid).collect(Collectors.toList());
            displayStoreRespDTOS = storeService.queryStoreInfo(storeList, displayStoreRespDTOS);
        }
        log.info("getDisplayStoreRespDTOList 最终返回参数：{}", JacksonUtils.writeValueAsString(displayStoreRespDTOS));
        return displayStoreRespDTOS;
    }

    private List<DisplayItemRespDTO> getDisplayItemRespDTOList(String ruleGuid, Integer ruleType, String brandGuid) {
        List<DisplayItemDO> itemDOList = itemService.list(new LambdaQueryWrapper<DisplayItemDO>()
                .eq(DisplayItemDO::getRuleGuid, ruleGuid)
                .eq(DisplayItemDO::getRuleType, ruleType)
                .eq(DisplayItemDO::getIsDelete, ZERO)
                .orderByAsc(DisplayItemDO::getSort)
        );

        if (CollUtil.isEmpty(itemDOList)) {
            return Collections.emptyList();
        }

        List<DisplayItemRespDTO> displayItemRespDTOS = ruleMapstruct.toItemRespList(itemDOList);
        List<String> skuGuidList = itemDOList.stream()
                .map(DisplayItemDO::getProductSpecGuid)
                .distinct()
                .collect(Collectors.toList());


        if (!CollectionUtils.isEmpty(displayItemRespDTOS)) {
            //商品名称实时获取
            List<String> itemGuidList = itemDOList.stream().map(DisplayItemDO::getItemGuid).collect(Collectors.toList());
            displayItemRespDTOS = itemService.queryItemInfo(itemGuidList, displayItemRespDTOS);
        }

        //获取菜谱模式
        BrandDTO brandDTO = storeClient.queryBrandByGuid(brandGuid);
        log.info("菜品模式数据：{}", JacksonUtils.writeValueAsString(brandDTO));

        Map<String, ItemWebRespDTO> skuForNameMap = itemService.mapSkuForName(skuGuidList);
        log.info("skuForNameMap:{}", skuForNameMap.size());
        displayItemRespDTOS.forEach(product -> {
            if (StringUtils.isEmpty(product.getProductSpecGuid())) {
                log.warn("productSpecGuid为空");
                return;
            }
            ItemWebRespDTO dto = skuForNameMap.get(product.getProductSpecGuid());
            if (!ObjectUtils.isEmpty(dto)) {
                if (!StringUtils.isEmpty(dto.getName())) {
                    product.setItemName(dto.getName());
                }
                if (!StringUtils.isEmpty(dto.getPlanItemName()) && brandDTO.getSalesModel() == 2) {
                    product.setPlanItemName(dto.getPlanItemName());
                }
            }
        });


        log.info("getDisplayItemRespDTOList 最终返回参数：{}", JacksonUtils.writeValueAsString(displayItemRespDTOS));
        return displayItemRespDTOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateRule(DisplayRuleSaveOrUpdateDTO reqDTO) {

        checkParam(reqDTO);
        String ruleGuid = reqDTO.getRuleGuid();

        // 更新规则
        DisplayRuleDO displayRuleDO = ruleMapstruct.ruleReqToRuleDO(reqDTO);
        if (ObjectUtils.isEmpty(displayRuleDO.getBatch())) {
            displayRuleDO.setBatch(-1);
        }
        if (ObjectUtils.isEmpty(displayRuleDO.getDelayTime())) {
            displayRuleDO.setDelayTime(0);
        }
        boolean updateRule = this.update(displayRuleDO, new LambdaQueryWrapper<DisplayRuleDO>()
                .eq(DisplayRuleDO::getGuid, ruleGuid)
                .eq(DisplayRuleDO::getRuleType, reqDTO.getRuleType())
        );

        // 更新商品
        List<DisplayItemRespDTO> itemList = reqDTO.getItemList();
        List<DisplayItemDO> itemDOList = ruleMapstruct.toItemDOList(itemList);
        List<String> idList = distributedIdService.nextBatchDisplayItemGuid(itemDOList.size());
        itemDOList.forEach(item -> {
            item.setGuid(idList.remove(idList.size() - 1));
            item.setRuleGuid(ruleGuid);
            item.setRuleType(reqDTO.getRuleType());
        });
        itemService.remove(new LambdaQueryWrapper<DisplayItemDO>()
                .eq(DisplayItemDO::getRuleGuid, ruleGuid)
                .eq(DisplayItemDO::getIsDelete, ZERO)
                .eq(DisplayItemDO::getRuleType, reqDTO.getRuleType())
        );
        checkItem(reqDTO, itemList);
        boolean updateItem = itemService.saveBatch(itemDOList);

        // 更新门店
        List<DisplayStoreRespDTO> storeList = reqDTO.getStoreList();
        List<DisplayStoreDO> storeDOList = getDisplayStoreDOList(reqDTO, ruleGuid, storeList);
        storeService.remove(new LambdaQueryWrapper<DisplayStoreDO>()
                .eq(DisplayStoreDO::getRuleGuid, ruleGuid)
                .eq(DisplayStoreDO::getIsDelete, ZERO)
                .eq(DisplayStoreDO::getRuleType, reqDTO.getRuleType())
        );
        checkStore(reqDTO, storeList);
        boolean updateStore = storeService.saveBatch(storeDOList);

        return updateRule && updateItem && updateStore;
    }

    private List<DisplayStoreDO> getDisplayStoreDOList(DisplayRuleSaveOrUpdateDTO reqDTO, String ruleGuid, List<DisplayStoreRespDTO> storeList) {
        List<DisplayStoreDO> storeDOList = ruleMapstruct.toStoreDOList(storeList);
        if (!reqDTO.getIsAllStore()) {
            List<String> storeGuidList = distributedIdService.nextBatchDisplayStoreGuid(storeDOList.size());
            storeDOList.forEach(storeDO -> {
                storeDO.setGuid(storeGuidList.remove(storeGuidList.size() - 1));
                storeDO.setRuleGuid(ruleGuid);
                storeDO.setRuleType(reqDTO.getRuleType());
            });
        }
        return storeDOList;
    }

    @Override
    public Boolean queryHasAllStore(DisplayRuleQueryDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO.getRuleType())) {
            throw new BusinessException(RULE_TYPE_CANNOT_BE_EMPTY);
        }
        LambdaQueryWrapper<DisplayRuleDO> wrapper = new LambdaQueryWrapper<DisplayRuleDO>()
                .eq(DisplayRuleDO::getIsAllStore, Boolean.TRUE)
                .eq(DisplayRuleDO::getRuleType, reqDTO.getRuleType())
                .eq(DisplayRuleDO::getBrandGuid, reqDTO.getBrandGuid());

        if (!ObjectUtils.isEmpty(reqDTO.getRuleGuid())) {
            log.info("编辑查询kds使用商品，规则guid={}", reqDTO.getRuleGuid());
            wrapper.ne(DisplayRuleDO::getGuid, reqDTO.getRuleGuid());
        }

        List<DisplayRuleDO> list = this.list(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

}