package com.holderzone.saas.store.kds.utils;

import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public final class SplitUtils {

    public static <T> List<List<T>> splitList(List<T> collection, int step) {
        if(CollectionUtils.isEmpty(collection)) {
            return Collections.emptyList();
        }
        if (step < 1) {
            return Collections.singletonList(collection);
        }
        return Stream.iterate(0, f -> f + 1)
                .limit(countStep(collection.size(), step))
                .parallel()
                .map(a -> collection.parallelStream().skip(a * step).limit(step).collect(Collectors.toList()))
                .filter(b -> !b.isEmpty())
                .collect(Collectors.toList());
    }

    private static Integer countStep(Integer size, int step) {
        return (size + step - 1) / step;
    }
}
