package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.saas.store.kds.entity.domain.BindItemDO;
import com.holderzone.saas.store.kds.mapper.BindItemMapper;
import com.holderzone.saas.store.kds.service.BindItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class BindItemServiceImpl extends ServiceImpl<BindItemMapper, BindItemDO>
        implements BindItemService {

    @Override
    public List<BindItemDO> listByStoreGuid(String storeGuid) {
        QueryWrapper<BindItemDO> qw = new QueryWrapper<>();
        qw.lambda().eq(BindItemDO::getStoreGuid, storeGuid);
        return list(qw);
    }

    @Override
    public List<BindItemDO> listByStoreGuidAndSkuGuids(String storeGuid, List<String> skuGuids) {
        if (CollectionUtils.isEmpty(skuGuids)) {
            return Lists.newArrayList();
        }
        QueryWrapper<BindItemDO> qw = new QueryWrapper<>();
        qw.lambda().eq(BindItemDO::getStoreGuid, storeGuid);
        qw.lambda().in(BindItemDO::getSkuGuid, skuGuids);
        return list(qw);
    }

    @Override
    public List<BindItemDO> listByGroupGuids(List<String> groupGuids) {
        QueryWrapper<BindItemDO> qw = new QueryWrapper<>();
        qw.lambda().in(BindItemDO::getGroupGuid, groupGuids);
        return list(qw);
    }

    @Override
    public List<BindItemDO> listByGroupGuidsAndSkuGuids(List<String> groupGuids, List<String> skuGuids) {
        QueryWrapper<BindItemDO> qw = new QueryWrapper<>();
        qw.lambda().in(BindItemDO::getSkuGuid, skuGuids);
        qw.lambda().in(BindItemDO::getGroupGuid, groupGuids);
        return list(qw);
    }

    @Override
    public void saveIgnoreBatch(List<BindItemDO> bindItemDOList) {
        if (CollectionUtils.isEmpty(bindItemDOList)) {
            return;
        }
        baseMapper.saveIgnoreBatch(bindItemDOList);
    }

    @Override
    public void removeByGroupGuid(String groupGuid) {
        UpdateWrapper<BindItemDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(BindItemDO::getGroupGuid, groupGuid);
        remove(uw);
    }

    @Override
    public void removeByGroupGuidAndItemGuid(String groupGuid, String itemGuid) {
        UpdateWrapper<BindItemDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(BindItemDO::getGroupGuid, groupGuid);
        uw.lambda().eq(BindItemDO::getItemGuid, itemGuid);
        remove(uw);
    }

    @Override
    public void removeByGroupGuidAndSkuGuid(String groupGuid, String skuGuid) {
        UpdateWrapper<BindItemDO> uw = new UpdateWrapper<>();
        uw.lambda().eq(BindItemDO::getGroupGuid, groupGuid);
        uw.lambda().eq(BindItemDO::getSkuGuid, skuGuid);
        remove(uw);
    }
}
