package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.kds.entity.domain.DeviceBindItemGroupDO;

import java.util.List;


public interface DeviceBindItemGroupService extends IService<DeviceBindItemGroupDO> {

    List<DeviceBindItemGroupDO> listByStoreGuid(String storeGuid);

    List<DeviceBindItemGroupDO> listByDeviceId(String deviceId);

    List<DeviceBindItemGroupDO> listByDeviceIdAndPointGuid(String deviceId, String pointGuid);

    void unbind(String storeGuid, String pointGuid, String deviceId);

    void reInitialize(String storeGuid, String deviceId);

    void unbind(String groupGuid);
}
