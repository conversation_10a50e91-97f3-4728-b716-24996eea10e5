package com.holderzone.saas.store.kds.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.MappingRespDTO;
import com.holderzone.saas.store.dto.kds.req.DstItemQueryReqDTO;
import com.holderzone.saas.store.dto.kds.req.PrdPointItemQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.*;
import com.holderzone.saas.store.dto.table.AreaDTO;
import com.holderzone.saas.store.kds.builder.ItemGroupBuilder;
import com.holderzone.saas.store.kds.constant.Constants;
import com.holderzone.saas.store.kds.entity.domain.*;
import com.holderzone.saas.store.kds.entity.enums.PointModeEnum;
import com.holderzone.saas.store.kds.manager.bo.ItemGroupBO;
import com.holderzone.saas.store.kds.service.*;
import com.holderzone.saas.store.kds.service.rpc.ItemRpcService;
import com.holderzone.saas.store.kds.service.rpc.TableRpcService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 绑定菜品分组
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DeviceBindItemGroupManager {

    private final DeviceConfigService deviceConfigService;

    private final ItemConfigService itemConfigService;

    private final ProductionPointService productionPointService;

    private final BindItemGroupService bindItemGroupService;

    private final BindItemService bindItemService;

    private final DeviceBindItemGroupService deviceBindItemGroupService;

    private final DistributeAreaService distributeAreaService;

    private final ItemRpcService itemRpcService;

    private final TableRpcService tableRpcService;

    /**
     * 新增分组
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveGroup(ItemGroupBO biz) {
        log.info("新增菜品分组biz：{}", JacksonUtils.writeValueAsString(biz));
        // 校验分组名称唯一性
        verifyItemGroup(biz);
        BindItemGroupDO bindItemGroup = biz.getBindItemGroup();
        List<BindItemDO> bindItemList = biz.getBindItemList();
        bindItemGroupService.save(bindItemGroup);
        if (CollectionUtils.isNotEmpty(bindItemList)) {
            bindItemService.saveIgnoreBatch(bindItemList);
        }
    }

    /**
     * 编辑分组 (菜品新增)
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateGroup(ItemGroupBO biz) {
        log.info("编辑菜品分组biz：{}", JacksonUtils.writeValueAsString(biz));
        // 校验分组名称唯一性
        verifyItemGroup(biz);
        BindItemGroupDO bindItemGroup = biz.getBindItemGroup();
        List<BindItemDO> bindItemList = biz.getBindItemList();
        bindItemGroupService.updateById(bindItemGroup);
        if (CollectionUtils.isNotEmpty(bindItemList)) {
            bindItemService.saveIgnoreBatch(bindItemList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeGroup(String guid) {
        bindItemGroupService.removeById(guid);
        bindItemService.removeByGroupGuid(guid);
        // 删除设备已关联的分组
        deviceBindItemGroupService.unbind(guid);
    }

    public void removeGroupItem(String groupGuid, String skuGuid) {
        ItemStringListDTO skuQueryDTO = new ItemStringListDTO();
        skuQueryDTO.setDataList(Lists.newArrayList(skuGuid));
        Map<String, String> skuParentMap = itemRpcService.queryParentSkuGuidBySku(skuQueryDTO);
        bindItemService.removeByGroupGuidAndSkuGuid(groupGuid, skuGuid);
        String parentSkuGuid = skuParentMap.get(skuGuid);
        if (!skuGuid.equals(parentSkuGuid)) {
            bindItemService.removeByGroupGuidAndSkuGuid(groupGuid, parentSkuGuid);
        }
    }


    public List<PointTypeBindRespDTO> queryPrdBindItem(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        // 所有设备元数据
        Map<String, String> deviceNameMap = deviceConfigService.getDeviceNameMapOfStore(prdPointItemQueryReqDTO.getStoreGuid());
        // 所有制作点元数据
        List<ProductionPointDO> allPrdPointInSql = productionPointService.list(new LambdaQueryWrapper<ProductionPointDO>()
                .select(ProductionPointDO::getGuid, ProductionPointDO::getName)
                .eq(ProductionPointDO::getStoreGuid, prdPointItemQueryReqDTO.getStoreGuid()));
        Map<String, String> pointNameMap = allPrdPointInSql.stream()
                .collect(Collectors.toMap(ProductionPointDO::getGuid, ProductionPointDO::getName));
        // 商品元数据
        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData(prdPointItemQueryReqDTO.getStoreGuid());
        List<MappingRespDTO> mappingRespDTOS = itemRpcService.kdsMapping(itemSingleDTO);
        log.info("当前门店菜品数据：{}", JacksonUtils.writeValueAsString(mappingRespDTOS));

        // 过滤商品
        filterMappingRespList(prdPointItemQueryReqDTO.getGroupGuid(), prdPointItemQueryReqDTO.getStoreGuid(), mappingRespDTOS);

        // 构建prdPointItemMap
        Map<String, List<PrdPointItemDO>> prdPointItemMap = buildPrdPointItemMap(prdPointItemQueryReqDTO);

        // 查询sku -> skuParentGuid
        ItemStringListDTO skuQueryDTO = new ItemStringListDTO();
        skuQueryDTO.setDataList(new ArrayList<>(prdPointItemMap.keySet()));
        Map<String, String> skuParentMap = itemRpcService.queryParentSkuGuidBySku(skuQueryDTO);

        // 菜品基础配置
        List<ItemConfigDO> allItemConfigInSql = itemConfigService.queryBatchByStoreGuid(prdPointItemQueryReqDTO.getStoreGuid());
        Map<String, ItemConfigDO> itemConfigMap = allItemConfigInSql.stream()
                .collect(Collectors.toMap(ItemConfigDO::getSkuGuid, Function.identity()));

        // 制作点位信息
        List<PointTypeBindRespDTO> list = mappingRespDTOS.stream()
                .collect(Collectors.groupingBy(o -> Pair.of(o.getCategoryId(), o.getCategoryName())))
                .entrySet().stream()
                .map(typeEntry -> {
                    // 对商品进行分类
                    PointTypeBindRespDTO pointTypeBindRespDTO = new PointTypeBindRespDTO();
                    String typeGuid = typeEntry.getKey().getFirst();
                    String typeName = typeEntry.getKey().getSecond();
                    pointTypeBindRespDTO.setTypeGuid(typeGuid);
                    pointTypeBindRespDTO.setTypeName(typeName);
                    List<MappingRespDTO> skuList = typeEntry.getValue();
                    Map<String, MappingRespDTO> itemMap = skuList.stream()
                            .collect(Collectors.toMap(MappingRespDTO::geteDishCode, Function.identity(), (key1, key2) -> key1));
                    // 分类下的商品，以菜名进行分组，即，菜 -> 多规格
                    Map<Pair<String, String>, List<MappingRespDTO>> itemSkusMap = skuList.stream()
                            .collect(Collectors.groupingBy(o -> Pair.of(o.geteDishCode(), o.geteDishName())));
                    List<PointItemBindRespDTO> typeItemList = itemSkusMap.entrySet().stream()
                            .map(itemEntry -> {
                                // 规格
                                PointItemBindRespDTO pointItemBindRespDTO = new PointItemBindRespDTO();
                                pointItemBindRespDTO.setItemGuid(itemEntry.getKey().getFirst());
                                pointItemBindRespDTO.setItemName(itemEntry.getKey().getSecond());
                                pointItemBindRespDTO.setPlanItemName(itemMap.getOrDefault(itemEntry.getKey().getFirst(),
                                        new MappingRespDTO()).getPlanItemName());
                                pointItemBindRespDTO.setIsBoundBySelf(false);
                                pointItemBindRespDTO.setIsBoundByOthers(false);
                                // 获取当前规格的菜品的绑定信息
                                List<PointSkuBindRespDTO> skuWithBindingInfo = getSkuWithBindingInfo(
                                        itemEntry, pointItemBindRespDTO, deviceNameMap, pointNameMap, prdPointItemMap,
                                        itemConfigMap, skuParentMap
                                );
                                pointItemBindRespDTO.setSkus(skuWithBindingInfo);
                                return pointItemBindRespDTO;
                            })
                            .filter(pointItemBindRespDTO -> !CollectionUtils.isEmpty(pointItemBindRespDTO.getSkus()))
                            .collect(Collectors.toList());
                    pointTypeBindRespDTO.setItemList(typeItemList);
                    return pointTypeBindRespDTO;
                })
                .filter(pointTypeBindRespDTO -> !CollectionUtils.isEmpty(pointTypeBindRespDTO.getItemList()))
                .collect(Collectors.toList());
        log.info("制作口菜品分组查询商品数据：{}", JacksonUtils.writeValueAsString(list));
        return list;
    }


    /**
     * 过滤商品
     */
    private void filterMappingRespList(String groupGuid, String storeGuid, List<MappingRespDTO> mappingRespList) {
        // 如果groupGuid不为空，则查询当前分组的商品
        if (StringUtils.isNotEmpty(groupGuid)) {
            List<BindItemDO> bindItemList = bindItemService.listByGroupGuids(Lists.newArrayList(groupGuid));
            // 过滤商品
            filterItemInfoRespList(bindItemList, mappingRespList);
        } else {
            // groupGuid为空，则查询所有分组未绑定的商品
            List<BindItemDO> bindItemList = bindItemService.listByStoreGuid(storeGuid);
            // 未绑定的商品
            unFilterBindItemList(bindItemList, mappingRespList);
        }
    }

    private Map<String, List<PrdPointItemDO>> buildPrdPointItemMap(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO) {
        String storeGuid = prdPointItemQueryReqDTO.getStoreGuid();
        // 查询门店下已绑定的分组
        List<DeviceBindItemGroupDO> deviceBindItemGroupList = deviceBindItemGroupService.listByStoreGuid(storeGuid);
        if (CollectionUtils.isEmpty(deviceBindItemGroupList)) {
            return Maps.newHashMap();
        }
        List<String> deviceBindList = deviceBindItemGroupList.stream()
                .map(DeviceBindItemGroupDO::getDeviceId)
                .distinct()
                .collect(Collectors.toList());
        // 制作口绑定的
        List<DeviceConfigDO> prdDeviceConfigList = deviceConfigService.filterPointModeByDeviceIds(PointModeEnum.PRODUCTION.getCode(), deviceBindList);
        List<String> prdDeviceConfigGuids = prdDeviceConfigList.stream()
                .map(DeviceConfigDO::getGuid)
                .collect(Collectors.toList());
        deviceBindItemGroupList = deviceBindItemGroupList.stream()
                .filter(e -> prdDeviceConfigGuids.contains(e.getDeviceId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deviceBindItemGroupList)) {
            return Maps.newHashMap();
        }
        Map<String, List<DeviceBindItemGroupDO>> deviceBindItemGroupMap = deviceBindItemGroupList.stream()
                .collect(Collectors.groupingBy(DeviceBindItemGroupDO::getGroupGuid));
        Map<String, List<PrdPointItemDO>> prdPointItemMap = Maps.newHashMap();
        List<BindItemDO> bindItemList = bindItemService.listByStoreGuid(storeGuid);
        for (BindItemDO bindItemDO : bindItemList) {
            List<DeviceBindItemGroupDO> deviceBindItemGroupDOList = deviceBindItemGroupMap.get(bindItemDO.getGroupGuid());
            if (CollectionUtils.isEmpty(deviceBindItemGroupDOList)) {
                continue;
            }
            List<PrdPointItemDO> prdPointItemList = Lists.newArrayList();
            for (DeviceBindItemGroupDO deviceBindItemGroupDO : deviceBindItemGroupDOList) {
                PrdPointItemDO prdPointItemDO = new PrdPointItemDO();
                prdPointItemDO.setStoreGuid(storeGuid);
                prdPointItemDO.setSkuGuid(bindItemDO.getSkuGuid());
                prdPointItemDO.setItemGuid(bindItemDO.getItemGuid());
                prdPointItemDO.setPointGuid(deviceBindItemGroupDO.getPointGuid());
                prdPointItemDO.setDeviceId(deviceBindItemGroupDO.getDeviceId());
                prdPointItemList.add(prdPointItemDO);
            }
            prdPointItemMap.put(bindItemDO.getSkuGuid(), prdPointItemList);
        }
        return prdPointItemMap;
    }

    private List<PointSkuBindRespDTO> getSkuWithBindingInfo(Map.Entry<Pair<String, String>, List<MappingRespDTO>> itemEntry,
                                                            PointItemBindRespDTO pointItemBindRespDTO,
                                                            Map<String, String> deviceNameMap,
                                                            Map<String, String> pointNameMap,
                                                            Map<String, List<PrdPointItemDO>> prdPointItemMap,
                                                            Map<String, ItemConfigDO> itemConfigMap,
                                                            Map<String, String> skuParentMap) {
        return itemEntry.getValue()
                .stream()
                .map(itemSku -> {
                    PointSkuBindRespDTO pointSkuBindRespDTO = new PointSkuBindRespDTO();
                    pointSkuBindRespDTO.setSkuGuid(itemSku.geteDishSkuCode());
                    pointSkuBindRespDTO.setSkuName(itemSku.geteDishSkuName());
                    pointSkuBindRespDTO.setSkuCode(itemSku.getSkuCode());
                    setItemBindingFlag(
                            itemSku, pointSkuBindRespDTO, pointItemBindRespDTO,
                            deviceNameMap, pointNameMap, prdPointItemMap, skuParentMap
                    );
                    setItemConfig(itemSku, pointSkuBindRespDTO, itemConfigMap);
                    return pointSkuBindRespDTO;
                })
                .collect(Collectors.toList());
    }

    private void setItemBindingFlag(MappingRespDTO itemSku,
                                    PointSkuBindRespDTO pointSkuBindRespDTO,
                                    PointItemBindRespDTO pointItemBindRespDTO,
                                    Map<String, String> deviceNameMap,
                                    Map<String, String> pointNameMap,
                                    Map<String, List<PrdPointItemDO>> prdPointItemMap,
                                    Map<String, String> skuParentMap) {
        pointItemBindRespDTO.setPinyin(itemSku.getPinyin());
        List<PrdPointItemDO> boundPrdPointItems = getBoundPrdPointItems(itemSku, prdPointItemMap, skuParentMap);
        if (CollectionUtils.isEmpty(boundPrdPointItems)) {
            return;
        }
        List<String> boundPrdDeviceIds = boundPrdPointItems.stream()
                .map(PrdPointItemDO::getDeviceId)
                .distinct()
                .collect(Collectors.toList());
        pointSkuBindRespDTO.setDeviceName(buildMultiName(boundPrdDeviceIds, deviceNameMap));
        List<String> boundPrdPointGuids = boundPrdPointItems.stream()
                .map(PrdPointItemDO::getPointGuid)
                .distinct()
                .collect(Collectors.toList());
        pointSkuBindRespDTO.setPointName(buildMultiName(boundPrdPointGuids, pointNameMap));
    }

    private List<PrdPointItemDO> getBoundPrdPointItems(MappingRespDTO itemSku,
                                                       Map<String, List<PrdPointItemDO>> prdPointItemMap,
                                                       Map<String, String> skuParentMap) {
        List<PrdPointItemDO> boundPrdPointItems = prdPointItemMap.get(itemSku.geteDishSkuCode());
        if (CollectionUtils.isNotEmpty(boundPrdPointItems)) {
            return boundPrdPointItems;
        }
        boundPrdPointItems = prdPointItemMap.get(itemSku.getParentSkuGuid());
        if (CollectionUtils.isNotEmpty(boundPrdPointItems)) {
            return boundPrdPointItems;
        }
        for (Map.Entry<String, List<PrdPointItemDO>> entry : prdPointItemMap.entrySet()) {
            if (itemSku.geteDishSkuCode().equals(skuParentMap.get(entry.getKey()))) {
                return entry.getValue();
            }
            if (Objects.nonNull(itemSku.getParentSkuGuid()) && itemSku.getParentSkuGuid().equals(skuParentMap.get(entry.getKey()))) {
                return entry.getValue();
            }
        }
        return Lists.newArrayList();
    }


    private String buildMultiName(List<String> ids, Map<String, String> nameMap) {
        List<String> nameList = ids.stream()
                .map(nameMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return StringUtils.join(nameList, ",");
    }

    private void setItemConfig(MappingRespDTO itemSku, PointSkuBindRespDTO pointSkuBindRespDTO, Map<String, ItemConfigDO> itemConfigMap) {
        ItemConfigDO itemConfigDO = itemConfigMap.get(itemSku.geteDishSkuCode());
        if (itemConfigDO != null) {
            pointSkuBindRespDTO.setTimeout(itemConfigDO.getTimeout());
            pointSkuBindRespDTO.setMaxCopies(itemConfigDO.getMaxCopies());
            pointSkuBindRespDTO.setDisplayType(itemConfigDO.getDisplayType());
        } else {
            pointSkuBindRespDTO.setTimeout(Constants.ITEM_TIMEOUT);
            pointSkuBindRespDTO.setMaxCopies(Constants.ITEM_MAX_COPIES);
            pointSkuBindRespDTO.setDisplayType(Constants.ITEM_DISPLAY_TYPE);
        }
    }


    /**
     * 查询分组列表
     */
    public List<ItemGroupRespDTO> queryGroupList(PrdPointItemQueryReqDTO queryReqDTO) {
        String storeGuid = UserContextUtils.getStoreGuid();
        // 查询所有分组列表
        List<BindItemGroupDO> bindItemGroupList = bindItemGroupService.listByStoreGuid(storeGuid);
        if (CollectionUtils.isEmpty(bindItemGroupList)) {
            return Lists.newArrayList();
        }
        // 查询分组下所有关联商品
        List<String> groupGuids = bindItemGroupList.stream()
                .map(BindItemGroupDO::getGuid)
                .collect(Collectors.toList());
        List<BindItemDO> bindItemList = bindItemService.listByGroupGuids(groupGuids);
        if (CollectionUtils.isNotEmpty(bindItemList)) {
            // 过滤当前存在的商品
            ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
            itemSingleDTO.setData(storeGuid);
            List<MappingRespDTO> itemInfoRespList = itemRpcService.kdsMapping(itemSingleDTO);
            filterBindItemList(bindItemList, itemInfoRespList);
        }
        // 查询当前设备、当前堂口已绑定的分组
        List<DeviceBindItemGroupDO> deviceBindItemGroupList = deviceBindItemGroupService.listByDeviceIdAndPointGuid(
                queryReqDTO.getDeviceId(), queryReqDTO.getPointGuid());
        // 构建返回对象
        return ItemGroupBuilder.buildItemGroupRespDTO(bindItemGroupList, bindItemList, deviceBindItemGroupList);
    }


    /**
     * 查询分组列表
     */
    public List<ItemGroupRespDTO> queryMySelfGroupList(PrdPointItemQueryReqDTO queryReqDTO) {
        String storeGuid = UserContextUtils.getStoreGuid();
        // 查询门店商品
        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData(storeGuid);
        List<MappingRespDTO> itemInfoRespList = itemRpcService.kdsMapping(itemSingleDTO);
        List<BindItemDO> bindItemList = Lists.newArrayList();
        List<BindItemGroupDO> bindItemGroupList = Lists.newArrayList();
        // 查询当前设备已绑定的分组
        List<DeviceBindItemGroupDO> deviceBindItemGroupList = deviceBindItemGroupService.listByDeviceIdAndPointGuid(
                queryReqDTO.getDeviceId(), queryReqDTO.getPointGuid());
        // 查询门已有的商品列表
        List<BindItemDO> allBindItemList = bindItemService.listByStoreGuid(storeGuid);
        if (CollectionUtils.isNotEmpty(allBindItemList)) {
            // 过滤当前存在的商品
            filterBindItemList(allBindItemList, itemInfoRespList);
        }
        if (CollectionUtils.isNotEmpty(deviceBindItemGroupList)) {
            List<String> currentGroupGuids = deviceBindItemGroupList.stream()
                    .map(DeviceBindItemGroupDO::getGroupGuid)
                    .distinct()
                    .collect(Collectors.toList());
            // 查询所有分组列表
            bindItemGroupList = new ArrayList<>(bindItemGroupService.listByIds(currentGroupGuids));
            if (CollectionUtils.isNotEmpty(bindItemGroupList)) {
                // 查询分组下所有关联商品
                List<String> groupGuids = bindItemGroupList.stream()
                        .map(BindItemGroupDO::getGuid)
                        .collect(Collectors.toList());
                bindItemList = allBindItemList.stream()
                        .filter(e -> groupGuids.contains(e.getGroupGuid()))
                        .collect(Collectors.toList());
            }
        }
        // 构建返回对象
        return ItemGroupBuilder.buildItemGroupRespDTO(itemInfoRespList, bindItemGroupList, allBindItemList,
                bindItemList, deviceBindItemGroupList);
    }


    /**
     * 过滤当前存在的商品
     */
    private void filterBindItemList(List<BindItemDO> bindItemList, List<MappingRespDTO> itemInfoRespList) {
        List<String> bindItemGuids = bindItemList.stream()
                .map(BindItemDO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        // 查询itemGuid -> parentItemGuid
        ItemStringListDTO query = new ItemStringListDTO();
        query.setDataList(bindItemGuids);
        Map<String, String> itemMap = itemRpcService.queryParentItemGuidByItem(query);
        // 过滤
        List<String> existItemGuids = itemInfoRespList.stream()
                .map(MappingRespDTO::geteDishCode)
                .distinct()
                .collect(Collectors.toList());
        List<String> existParentItemGuids = itemInfoRespList.stream()
                .map(MappingRespDTO::getParentItemGuid)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existParentItemGuids)) {
            existItemGuids.addAll(existParentItemGuids);
        }
        bindItemList.removeIf(e -> !existItemGuids.contains(e.getItemGuid())
                && !existItemGuids.contains(itemMap.getOrDefault(e.getItemGuid(), e.getItemGuid())));
    }

    /**
     * 过滤当前存在的商品
     */
    private void filterItemInfoRespList(List<BindItemDO> bindItemList, List<MappingRespDTO> itemInfoRespList) {
        List<String> bindItemSkuGuidList = bindItemList.stream()
                .map(BindItemDO::getSkuGuid)
                .distinct()
                .collect(Collectors.toList());

        // 查询sku skuGuid -> parentSkuGuid
        ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(bindItemSkuGuidList);
        Map<String, String> skuMap = itemRpcService.queryParentSkuGuidBySku(itemStringListDTO);
        List<String> skuGuidList = new ArrayList<>(skuMap.values());
        itemInfoRespList.removeIf(e -> !skuGuidList.contains(e.geteDishSkuCode())
                && !skuGuidList.contains(e.getParentSkuGuid()));
    }

    /**
     * 过滤当前存在的商品
     */
    private void unFilterBindItemList(List<BindItemDO> bindItemList, List<MappingRespDTO> itemInfoRespList) {
        List<String> bindItemSkuGuidList = bindItemList.stream()
                .map(BindItemDO::getSkuGuid)
                .distinct()
                .collect(Collectors.toList());

        // 查询sku skuGuid -> parentSkuGuid
        ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
        itemStringListDTO.setDataList(bindItemSkuGuidList);
        Map<String, String> skuMap = itemRpcService.queryParentSkuGuidBySku(itemStringListDTO);
        List<String> skuGuidList = new ArrayList<>(skuMap.values());
        itemInfoRespList.removeIf(e -> skuGuidList.contains(e.geteDishSkuCode())
                || skuGuidList.contains(e.getParentSkuGuid()));
    }

    /**
     * 校验
     */
    private void verifyItemGroup(ItemGroupBO biz) {
        // 校验分组名称唯一性
        BindItemGroupDO bindItemGroup = biz.getBindItemGroup();
        BindItemGroupDO groupDO = bindItemGroupService.getByName(bindItemGroup.getStoreGuid(), bindItemGroup.getName());
        if (Objects.nonNull(groupDO) && !bindItemGroup.getGuid().equals(groupDO.getGuid())) {
            throw new BusinessException("分组名称重复");
        }
    }

    /**
     * 查询出堂口绑定商品列表
     */
    public List<DstTypeBindRespDTO> queryDstBindItem(DstItemQueryReqDTO dstItemQueryReqDTO) {
        // 当前设备信息
        String currentStoreGuid = dstItemQueryReqDTO.getStoreGuid();
        // deviceId -> deviceName
        Map<String, String> deviceNameMap = deviceConfigService.getDeviceNameMapOfStore(currentStoreGuid);
        // 查询该门店下的所有商品信息
        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData(currentStoreGuid);
        List<MappingRespDTO> mappingRespDTOS = itemRpcService.kdsMapping(itemSingleDTO);
        // 过滤商品
        filterMappingRespList(dstItemQueryReqDTO.getGroupGuid(), dstItemQueryReqDTO.getStoreGuid(), mappingRespDTOS);
        // areaId -> areaName
        Map<String, String> areaNameMap = fetchAreaNameMap(dstItemQueryReqDTO);
        // 当前门店已绑定区域
        List<DistributeAreaDO> distributeAreaInSql = distributeAreaService.queryBoundAreaOfStore(currentStoreGuid)
                .stream().filter(p -> areaNameMap.containsKey(p.getAreaGuid())).collect(Collectors.toList());
        // 当前门店的所有菜品的配置（超时时间、最大制作数）
        List<ItemConfigDO> allItemConfigInSql = itemConfigService.queryBatchByStoreGuid(currentStoreGuid);
        // sku -> 菜品配置
        Map<String, ItemConfigDO> itemConfigMap = allItemConfigInSql.stream()
                .collect(Collectors.toMap(ItemConfigDO::getSkuGuid, Function.identity()));

        // 构建distributeItemList
        List<DistributeItemDO> distributeItemInSql = buildDistributeItemList(dstItemQueryReqDTO);

        // 已绑定关系辅助MAP、Sku已绑定“设备-区域”列表
        int probableSize = mappingRespDTOS.size();
        Map<Pair<String, String>, String> skuArea2DeviceMap = new HashMap<>(probableSize);
        Map<Pair<String, String>, Boolean> skuDevice2TrueMap = new HashMap<>(probableSize);
        Map<String, List<DstAreaBindRespDTO>> skuDeviceAreaMap = new HashMap<>(probableSize);
        resolveBindingMapForItem(
                deviceNameMap, areaNameMap,
                distributeItemInSql, distributeAreaInSql,
                skuArea2DeviceMap, skuDevice2TrueMap, skuDeviceAreaMap
        );
        // 构造返回值
        List<DstTypeBindRespDTO> list = mappingRespDTOS.stream()
                .collect(Collectors.groupingBy(o -> Pair.of(o.getCategoryId(), o.getCategoryName())))
                .entrySet()
                .stream()
                .map(typeEntry -> {
                    // 分类
                    DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
                    dstTypeBindRespDTO.setTypeGuid(typeEntry.getKey().getFirst());
                    dstTypeBindRespDTO.setTypeName(typeEntry.getKey().getSecond());
                    // 商品
                    List<MappingRespDTO> skuList = typeEntry.getValue();
                    Map<String, MappingRespDTO> itemMap = skuList.stream()
                            .collect(Collectors.toMap(MappingRespDTO::geteDishCode, Function.identity(), (key1, key2) -> key1));
                    Map<Pair<String, String>, List<MappingRespDTO>> itemSkusMap = skuList.stream()
                            .collect(Collectors.groupingBy(o -> Pair.of(o.geteDishCode(), o.geteDishName())));
                    List<DstItemBindRespDTO> typeItemList = itemSkusMap.entrySet().stream()
                            .map(itemEntry -> {
                                // 规格
                                DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
                                dstItemBindRespDTO.setItemGuid(itemEntry.getKey().getFirst());
                                dstItemBindRespDTO.setItemName(itemEntry.getKey().getSecond());
                                dstItemBindRespDTO.setPlanItemName(itemMap.getOrDefault(itemEntry.getKey().getFirst(),
                                        new MappingRespDTO()).getPlanItemName());
                                dstItemBindRespDTO.setIsBoundBySelf(false);
                                dstItemBindRespDTO.setIsBoundByOthers(false);
                                List<DstSkuBindRespDTO> skuWithBindingInfo = getSkuWithBindingInfo(itemEntry,
                                        dstItemBindRespDTO, itemConfigMap, skuDeviceAreaMap
                                );
                                dstItemBindRespDTO.setSkus(skuWithBindingInfo);
                                return dstItemBindRespDTO;
                            })
                            .filter(dstItemBindRespDTO -> !CollectionUtils.isEmpty(dstItemBindRespDTO.getSkus()))
                            .collect(Collectors.toList());
                    dstTypeBindRespDTO.setItemList(typeItemList);
                    return dstTypeBindRespDTO;
                })
                .filter(dstTypeBindRespDTO -> !CollectionUtils.isEmpty(dstTypeBindRespDTO.getItemList()))
                .collect(Collectors.toList());
        log.info("出堂口菜品列表：{}", JacksonUtils.writeValueAsString(list));
        return list;
    }

    private List<DistributeItemDO> buildDistributeItemList(DstItemQueryReqDTO dstItemQueryReqDTO) {
        String storeGuid = dstItemQueryReqDTO.getStoreGuid();
        // 查询门店下已绑定的分组
        List<DeviceBindItemGroupDO> deviceBindItemGroupList = deviceBindItemGroupService.listByStoreGuid(storeGuid);
        if (CollectionUtils.isEmpty(deviceBindItemGroupList)) {
            return Lists.newArrayList();
        }
        List<String> deviceBindList = deviceBindItemGroupList.stream()
                .map(DeviceBindItemGroupDO::getDeviceId)
                .distinct()
                .collect(Collectors.toList());
        // 出堂口绑定的
        List<DeviceConfigDO> dstDeviceConfigList = deviceConfigService.filterPointModeByDeviceIds(PointModeEnum.DISTRIBUTE.getCode(), deviceBindList);
        List<String> dstDeviceConfigGuids = dstDeviceConfigList.stream()
                .map(DeviceConfigDO::getGuid)
                .collect(Collectors.toList());
        deviceBindItemGroupList = deviceBindItemGroupList.stream()
                .filter(e -> dstDeviceConfigGuids.contains(e.getDeviceId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deviceBindItemGroupList)) {
            return Lists.newArrayList();
        }
        Map<String, List<DeviceBindItemGroupDO>> deviceBindItemGroupMap = deviceBindItemGroupList.stream()
                .collect(Collectors.groupingBy(DeviceBindItemGroupDO::getGroupGuid));
        List<DistributeItemDO> dstItemList = Lists.newArrayList();
        List<BindItemDO> bindItemList = bindItemService.listByStoreGuid(storeGuid);
        for (BindItemDO bindItemDO : bindItemList) {
            List<DeviceBindItemGroupDO> deviceBindItemGroupDOList = deviceBindItemGroupMap.get(bindItemDO.getGroupGuid());
            if (CollectionUtils.isEmpty(deviceBindItemGroupDOList)) {
                continue;
            }
            for (DeviceBindItemGroupDO deviceBindItemGroupDO : deviceBindItemGroupDOList) {
                DistributeItemDO distributeItemDO = new DistributeItemDO();
                distributeItemDO.setStoreGuid(storeGuid);
                distributeItemDO.setSkuGuid(bindItemDO.getSkuGuid());
                distributeItemDO.setItemGuid(bindItemDO.getItemGuid());
                distributeItemDO.setDeviceId(deviceBindItemGroupDO.getDeviceId());
                dstItemList.add(distributeItemDO);
            }
        }
        return dstItemList;
    }

    private void resolveBindingMapForItem(Map<String, String> deviceNameMap,
                                          Map<String, String> areaNameMap,
                                          List<DistributeItemDO> distributeItemInSql,
                                          List<DistributeAreaDO> distributeAreaInSql,
                                          Map<Pair<String, String>, String> skuArea2DeviceMap,
                                          Map<Pair<String, String>, Boolean> skuDevice2TrueMap,
                                          Map<String, List<DstAreaBindRespDTO>> skuDeviceAreaMap
    ) {
        Map<String, List<DistributeItemDO>> skuDeviceMap = distributeItemInSql.stream().collect(Collectors.groupingBy(DistributeItemDO::getSkuGuid));
        Map<String, List<DistributeAreaDO>> deviceAreaMap = distributeAreaInSql.stream().collect(Collectors.groupingBy(DistributeAreaDO::getDeviceId));
        for (String skuGuid : skuDeviceMap.keySet()) { // 遍历已绑定 skuGuid
            List<DistributeItemDO> skuDeviceList = skuDeviceMap.get(skuGuid); // sku 对应的菜
            if (!org.springframework.util.CollectionUtils.isEmpty(skuDeviceList)) {
                for (DistributeItemDO distributeItemDO : skuDeviceList) {
                    String deviceId = distributeItemDO.getDeviceId();// 菜所在的 deviceId
                    List<DistributeAreaDO> deviceAreaList = deviceAreaMap.get(deviceId);// deviceid 对应的区域列表
                    if (!org.springframework.util.CollectionUtils.isEmpty(deviceAreaList)) {
                        for (DistributeAreaDO distributeAreaDO : deviceAreaList) {
                            skuArea2DeviceMap.put(Pair.of(skuGuid, distributeAreaDO.getAreaGuid()), deviceId);
                            DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
                            dstAreaBindRespDTO.setDeviceId(distributeAreaDO.getDeviceId());
                            dstAreaBindRespDTO.setDeviceName(deviceNameMap.get(distributeAreaDO.getDeviceId()));
                            dstAreaBindRespDTO.setAreaGuid(distributeAreaDO.getAreaGuid());
                            dstAreaBindRespDTO.setAreaName(areaNameMap.get(distributeAreaDO.getAreaGuid()));
                            skuDeviceAreaMap.computeIfAbsent(skuGuid, k -> new ArrayList<>()).add(dstAreaBindRespDTO);
                        }
                    }
                    skuDevice2TrueMap.put(Pair.of(skuGuid, deviceId), true);
                }
            }
        }
    }

    private List<DstSkuBindRespDTO> getSkuWithBindingInfo(Map.Entry<Pair<String, String>, List<MappingRespDTO>> itemEntry,
                                                          DstItemBindRespDTO dstItemBindRespDTO,
                                                          Map<String, ItemConfigDO> itemConfigMap,
                                                          Map<String, List<DstAreaBindRespDTO>> skuDeviceAreaMap) {
        return itemEntry.getValue().stream()
                .map(itemSku -> {
                    String skuGuid = itemSku.geteDishSkuCode();
                    DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
                    dstSkuBindRespDTO.setSkuGuid(skuGuid);
                    dstSkuBindRespDTO.setSkuName(itemSku.geteDishSkuName());
                    dstSkuBindRespDTO.setSkuCode(itemSku.getSkuCode());
                    dstItemBindRespDTO.setPinyin(itemSku.getPinyin());
                    dstSkuBindRespDTO.setTimeout(Optional.ofNullable(itemConfigMap.get(skuGuid))
                            .map(ItemConfigDO::getTimeout).orElse(20));
                    dstSkuBindRespDTO.setAreas(skuDeviceAreaMap.getOrDefault(skuGuid, new ArrayList<>()));
                    return dstSkuBindRespDTO;
                })
                .collect(Collectors.toList());
    }

    private Map<String, String> fetchAreaNameMap(DstItemQueryReqDTO dstItemQueryReqDTO) {
        List<AreaDTO> areaList = tableRpcService.query(dstItemQueryReqDTO.getStoreGuid());
        AreaDTO snackAreaDTO = new AreaDTO();
        snackAreaDTO.setGuid(DistributeAreaDO.SNACK_AREA_GUID);
        snackAreaDTO.setAreaName(DistributeAreaDO.SNACK_AREA_NAME);
        areaList.add(snackAreaDTO);
        AreaDTO takeoutAreaDTO = new AreaDTO();
        takeoutAreaDTO.setGuid(DistributeAreaDO.TAKEOUT_AREA_GUID);
        takeoutAreaDTO.setAreaName(DistributeAreaDO.TAKEOUT_AREA_NAME);
        areaList.add(takeoutAreaDTO);
        return areaList.stream().collect(Collectors.toMap(AreaDTO::getGuid, AreaDTO::getAreaName));
    }
}
