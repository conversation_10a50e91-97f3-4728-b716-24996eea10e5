package com.holderzone.saas.store.kds.manager;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.kds.resp.DisplayRepeatItemRespDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRepeatItemStoreReqDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.kds.entity.domain.DisplayRepeatItemDO;
import com.holderzone.saas.store.kds.service.DisplayRepeatItemService;
import com.holderzone.saas.store.kds.service.DisplayRepeatItemStoreService;
import com.holderzone.saas.store.kds.service.rpc.StoreClientService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;


@Component
@RequiredArgsConstructor
public class DisplayRuleManager {

    private final DisplayRepeatItemService displayRepeatItemService;

    private final DisplayRepeatItemStoreService displayRepeatItemStoreService;

    private final StoreClientService storeClientService;

    /**
     * 保存菜品绑定配置
     */
    @Transactional
    public void saveOrUpdateRepeatItem(DisplayRepeatItemStoreReqDTO reqDTO) {
        if (Boolean.FALSE.equals(reqDTO.getAllowRepeatFlag())) {
            reqDTO.setStoreGuids(Lists.newArrayList());
        }
        if (Boolean.FALSE.equals(reqDTO.getAllStoreFlag()) && CollectionUtils.isEmpty(reqDTO.getStoreGuids())) {
            throw new BusinessException("适用门店不能为空");
        }
        DisplayRepeatItemDO displayRepeatItemDO = new DisplayRepeatItemDO();
        displayRepeatItemDO.setGuid(reqDTO.getBrandGuid());
        displayRepeatItemDO.setBrandGuid(reqDTO.getBrandGuid());
        displayRepeatItemDO.setAllowRepeatFlag(reqDTO.getAllowRepeatFlag());
        displayRepeatItemDO.setAllStoreFlag(reqDTO.getAllStoreFlag());
        displayRepeatItemService.saveOrUpdateConfig(displayRepeatItemDO);
        displayRepeatItemStoreService.save(reqDTO.getBrandGuid(), reqDTO.getStoreGuids());
    }

    /**
     * 查询菜品绑定配置
     */
    public DisplayRepeatItemRespDTO queryRepeatItem(String brandGuid) {
        DisplayRepeatItemRespDTO displayRepeatItemRespDTO = new DisplayRepeatItemRespDTO();
        displayRepeatItemRespDTO.setStoreGuids(Lists.newArrayList());
        DisplayRepeatItemDO displayRepeatItemDO = displayRepeatItemService.getById(brandGuid);
        if (Objects.isNull(displayRepeatItemDO)) {
            displayRepeatItemDO = new DisplayRepeatItemDO();
            displayRepeatItemDO.setAllowRepeatFlag(false);
            displayRepeatItemDO.setAllStoreFlag(true);
        }
        BeanUtils.copyProperties(displayRepeatItemDO, displayRepeatItemRespDTO);
        if (Boolean.FALSE.equals(displayRepeatItemRespDTO.getAllowRepeatFlag()) || Boolean.TRUE.equals(displayRepeatItemRespDTO.getAllStoreFlag())) {
            // 不支持重复绑定或者适用全部门店 直接返回
            return displayRepeatItemRespDTO;
        }
        // 查询支持重复绑定的门店
        List<String> storeGuids = displayRepeatItemStoreService.storeGuidListByBrandGuid(brandGuid);
        displayRepeatItemRespDTO.setStoreGuids(storeGuids);
        return displayRepeatItemRespDTO;
    }

    /**
     * 查询菜品绑定配置
     */
    public DisplayRepeatItemRespDTO queryRepeatItemByStoreGuid(String storeGuid) {
        DisplayRepeatItemRespDTO displayRepeatItemRespDTO = new DisplayRepeatItemRespDTO();
        Boolean allowRepeatFlag = displayRepeatItemService.queryAllowRepeatFlag(storeGuid);
        displayRepeatItemRespDTO.setAllowRepeatFlag(allowRepeatFlag);
        // 查询品牌销售模式
        BrandDTO brandDTO = storeClientService.queryBrandByStoreGuid(storeGuid);
        displayRepeatItemRespDTO.setSalesModel(Optional.ofNullable(brandDTO).orElse(new BrandDTO()).getSalesModel());
        return displayRepeatItemRespDTO;
    }
}
