package com.holderzone.saas.store.kds.service.template;

import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.kds.req.KdsPrintItemDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsAttrGroupDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsItemAttrDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.dto.print.template.PrintRow;
import com.holderzone.saas.store.dto.print.template.convertable.Font;
import com.holderzone.saas.store.dto.print.template.printable.KeyValue;
import com.holderzone.saas.store.dto.print.template.printable.Section;
import com.holderzone.saas.store.dto.print.template.printable.Separator;
import com.holderzone.saas.store.kds.utils.print.BigDecimalUtils;
import com.holderzone.saas.store.kds.utils.print.PrintRowUtils;
import com.holderzone.saas.store.util.LocaleUtil;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public abstract class BaseItemTemplate<T extends KdsPrintItemDTO> extends AbsPrintTemplate<T> {


    @Override
    public List<PrintRow> getPrintRows() {
        KdsPrintItemDTO printDTO = getPrintDTO();
        List<PrdDstItemDTO> itemRecordList = printDTO.getItemRecordList();
        PrdDstItemDTO prdDstItemDTO = itemRecordList.get(0);
        List<PrintRow> printRows = new ArrayList<>();

        // 订单信息

//        Font orderInfoFont = KdsInvoiceTypeEnum.PRD_ITEM.equals(invoiceType) ? Font.SMALL : Font.NORMAL;
        Font orderInfoFont =  Font.NORMAL;
        PrintRowUtils.add(printRows, new KeyValue()
                .setKeyString(prdDstItemDTO.getOrderDesc(), orderInfoFont)
                .setValueString(prdDstItemDTO.getOrderSerialNo(), orderInfoFont));

        PrintRowUtils.add(printRows, new Separator());

        // 商品列表
        for (PrdDstItemDTO dstItemDTO : itemRecordList) {
            String onHold = dstItemDTO.getIsHanged() ? LocaleUtil.getMessage("on_hold") : "";
            String itemName = dstItemDTO.getItemName()
                    + (dstItemDTO.getIsUrged() ? LocaleUtil.getMessage("reminder") : onHold);
            String itemQuantity = dstItemDTO.getIsWeight()
                    ? BigDecimalUtils.quantityTrimmed(dstItemDTO.getCurrentCount()) + " " + dstItemDTO.getSkuUnit()
                    : "X " + BigDecimalUtils.quantityTrimmed(dstItemDTO.getCurrentCount());
            PrintRowUtils.add(printRows, new KeyValue()
                    .setKeyString(itemName, Font.NORMAL)
                    .setValueString(itemQuantity, Font.NORMAL));
            if (StringUtils.hasText(dstItemDTO.getSkuName())) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setAlignEdges(false)
                        .setKeyString(LocaleUtil.getMessage("specifications"))
                        .setValueString(dstItemDTO.getSkuName()));
            }
            if (!CollectionUtils.isEmpty(dstItemDTO.getAttrGroup())) {
                for (KdsAttrGroupDTO kdsAttrGroupDTO : dstItemDTO.getAttrGroup()) {
                    PrintRowUtils.add(printRows, new KeyValue()
                            .setAlignEdges(false)
                            .setKeyString(kdsAttrGroupDTO.getGroupName() + "：")
                            .setValueString(kdsAttrGroupDTO.getAttrs().stream()
                                    .map(KdsItemAttrDTO::getAttrName)
                                    .collect(Collectors.joining(";"))));
                }
            }
            if (StringUtils.hasText(dstItemDTO.getItemRemark())) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setAlignEdges(false)
                        .setKeyString(LocaleUtil.getMessage("dish_remarks"))
                        .setValueString(dstItemDTO.getItemRemark()));
            }
            if (StringUtils.hasText(dstItemDTO.getOrderRemark())) {
                PrintRowUtils.add(printRows, new KeyValue()
                        .setAlignEdges(false)
                        .setKeyString(LocaleUtil.getMessage("order_remark"))
                        .setValueString(dstItemDTO.getOrderRemark()));
            }
        }

        PrintRowUtils.add(printRows, new Separator());

        // 流水号
        PrintRowUtils.add(printRows, new Section(LocaleUtil.getMessage("order_number") + prdDstItemDTO.getOrderNumber()));

        // 打印时间和操作人
        PrintRowUtils.add(printRows, new Section(LocaleUtil.getMessage("prt_time")
                + DateTimeUtils.localDateTime2String(printDTO.getCreateTime())));
        PrintRowUtils.add(printRows, new Section(LocaleUtil.getMessage("operator") + "["
                + printDTO.getOperatorStaffAccount() + "]" + printDTO.getOperatorStaffName()));

        return printRows;
    }
}
