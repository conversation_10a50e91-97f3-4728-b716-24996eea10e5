package com.holderzone.saas.store.kds.bo;

import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.kds.req.KdsItemDTO;
import com.holderzone.saas.store.kds.entity.bo.OrderInfoBO;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
public class ItemPrepareSplitBO implements Serializable {

    private static final long serialVersionUID = -4247230675662759853L;

    private String storeGuid;

    private KdsItemDTO kdsItemDTO;

    private OrderInfoBO orderInfoBO;

    private List<KitchenItemAttrDO> attrs;

    private Map<String, DeviceConfigDO> deviceConfigInSqlMap;

    private LocalDateTime prepareTime;

    private List<SkuInfoRespDTO> skus;
}
