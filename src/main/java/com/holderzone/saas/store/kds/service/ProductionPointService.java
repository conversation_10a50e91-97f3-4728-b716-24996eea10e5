package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.PointBindDetailsRespDTO;
import com.holderzone.saas.store.dto.kds.resp.PointTypeBindRespDTO;
import com.holderzone.saas.store.dto.kds.resp.ProductionPointRespDTO;
import com.holderzone.saas.store.kds.entity.domain.ProductionPointDO;
import com.holderzone.saas.store.kds.entity.read.PointItemReadDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface ProductionPointService extends IService<ProductionPointDO> {

    String createPoint(PrdPointCreateReqDTO prdPointCreateReqDTO);

    void deletePoint(PrdPointDelReqDTO prdPointDelReqDTO);

    void reInitialize(String storeGuid, String deviceId);

    List<ProductionPointRespDTO> listPoint(PrdPointListReqDTO prdPointListReqDTO);

    void updatePoint(PrdPointUpdateReqDTO prdPointUpdateReqDTO);

    void bindPointItem(PrdPointItemBindReqDTO prdPointItemBindReqDTO);

    void unbindPointItem(PrdPointItemBindReqDTO prdPointItemBindReqDTO);

    PointBindDetailsRespDTO queryBindingDetails(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO);

    List<PointTypeBindRespDTO> queryBoundPointItem(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO);

    /**
     * 查询KDS制作点堂口已绑定未绑定菜品
     *
     * @param prdPointItemQueryReqDTO prdPointItemQueryReqDTO
     * @return List<PointTypeBindRespDTO>
     */
    List<PointTypeBindRespDTO> queryAllPointItem(PrdPointItemQueryReqDTO prdPointItemQueryReqDTO);

    void updateBasicConfig(DeviceBasicConfUpdateReqDTO deviceBasicConfUpdateReqDTO);

    void updateAdvancedConfig(DeviceAdvanConfUpdateReqDTO deviceAdvanConfUpdateReqDTO);

    List<PointItemReadDO> fillEmptyPoint(String storeGuid, String deviceId, List<PointItemReadDO> pointItemReadInSql);
}
