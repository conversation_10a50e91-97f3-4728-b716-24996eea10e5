package com.holderzone.saas.store.kds.service.print;

import com.holderzone.saas.store.kds.entity.domain.KdsPrintRecordDO;
import com.holderzone.saas.store.dto.kds.req.KdsPrintDTO;
import com.holderzone.saas.store.dto.kds.req.KdsPrintRecordReqDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className KdsPrintPushService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface KdsPrintPushService {

    void pushPrintTaskMsg(KdsPrintDTO kdsPrintDTO, List<KdsPrintRecordDO> arrayOfPrintRecord);

    void pushPrintSucceedMsg(KdsPrintRecordReqDTO kdsPrintRecordReqDTO, KdsPrintRecordDO printRecordDO, int failedCount);

    void pushPrintFailedMsg(KdsPrintRecordReqDTO kdsPrintRecordReqDTO, KdsPrintRecordDO printRecordDO, int failedCount);
}
