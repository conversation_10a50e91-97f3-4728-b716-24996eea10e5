package com.holderzone.saas.store.kds.mapstruct;

import com.holderzone.saas.store.dto.kds.req.QueueConfigDTO;
import com.holderzone.saas.store.dto.kds.resp.QueueItemDTO;
import com.holderzone.saas.store.kds.entity.domain.QueueConfigDO;
import com.holderzone.saas.store.kds.entity.domain.QueueItemDO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring")
public interface QueueMapstruct {

    QueueConfigDO fromDTO(QueueConfigDTO queueConfigDTO);

    QueueConfigDTO doToDTO(QueueConfigDO queueConfigDO);

    QueueItemDTO doToDTO(QueueItemDO queueItemDO);

    List<QueueItemDTO> doToDTO(List<QueueItemDO> queueItemDO);
}
