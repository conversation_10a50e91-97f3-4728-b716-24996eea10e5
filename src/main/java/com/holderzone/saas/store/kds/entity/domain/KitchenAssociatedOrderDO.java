package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("hsk_kitchen_associated_order")
public class KitchenAssociatedOrderDO implements Serializable {

    private static final long serialVersionUID = -8092773479064945196L;

    /**
     * 唯一GUID
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 订单guid
     */
    private String orderGuid;

    /**
     * 是否联台单
     */
    private Boolean associatedFlag;

    /**
     * 联台单编号
     */
    private String associatedSn;

    /**
     * 联台单桌台列表
     */
    private String associatedTableGuids;

    /**
     * 联台单桌台列表
     */
    private String associatedTableNames;

}
