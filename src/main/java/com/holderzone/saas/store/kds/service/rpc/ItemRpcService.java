package com.holderzone.saas.store.kds.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.MappingRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemFeignClient
 * @date 2018/09/04 11:49
 * @description //TODO
 * @program holder-saas-store-takeaway
 */
@Component
@FeignClient(value = "holder-saas-store-item", fallbackFactory = ItemRpcService.ServiceFallBack.class)
public interface ItemRpcService {

    @PostMapping("/item/kds_mapping")
    List<MappingRespDTO> kdsMapping(@RequestBody ItemSingleDTO itemSingleDTO);

    @PostMapping("/item/kds_item_parent_mapping")
    List<ItemInfoRespDTO> kdsItemParentMapping(@RequestBody @Valid List<String> itemGuidList);

    @PostMapping("/item/parent_sku")
    List<SkuInfoRespDTO> findParentSkus(@RequestBody List<String> skuGuid);

    @ApiOperation(value = "根据规格guid查询父子级guid")
    @PostMapping("/item_sku/list_sku_guid")
    List<String> listSkuGuid(@RequestBody List<String> skuGuidList);

    /**
     * 根据规格guid查询对应商品全名
     * 包含菜谱
     *
     * @param skuGuidList 规格guid列表
     * @return 商品全名
     */
    @ApiOperation(value = "根据规格guid查询对应商品全名")
    @PostMapping("/item_sku/list_sku_for_name")
    List<ItemWebRespDTO> listSkuForName(@RequestBody List<String> skuGuidList);

    /**
     * 新根据商品guid获取门店商品详情列表（区分销售模式）
     */
    @PostMapping("/item/list_item_info_by_sales_model_new")
    List<ItemInfoRespDTO> listItemInfoBySalesModelNew(@RequestBody ItemStringListDTO itemStringListDTO);

    /**
     * 通过item查询父级ItemGuid
     */
    @PostMapping("/item/query_parent_item_guid_by_item")
    Map<String, String> queryParentItemGuidByItem(@RequestBody ItemStringListDTO query);

    @PostMapping("/item_sku/query_parent_sku_guid_by_sku")
    Map<String, String> queryParentSkuGuidBySku(@RequestBody ItemStringListDTO query);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ItemRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ItemRpcService create(Throwable throwable) {
            return new ItemRpcService() {

                @Override
                public List<MappingRespDTO> kdsMapping(ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "kdsMapping",
                            JacksonUtils.writeValueAsString(itemSingleDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemInfoRespDTO> kdsItemParentMapping(@Valid List<String> itemGuidList) {
                    log.error(HYSTRIX_PATTERN, "kdsItemParentMapping",
                            JacksonUtils.writeValueAsString(itemGuidList),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<SkuInfoRespDTO> findParentSkus(List<String> skuGuid) {
                    log.error(HYSTRIX_PATTERN, "findParentSkus",
                            JacksonUtils.writeValueAsString(skuGuid),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<String> listSkuGuid(List<String> skuGuidList) {
                    log.error(HYSTRIX_PATTERN, "listSkuGuid", skuGuidList, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemWebRespDTO> listSkuForName(List<String> skuGuidList) {
                    log.error(HYSTRIX_PATTERN, "listSkuForName", skuGuidList, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemInfoRespDTO> listItemInfoBySalesModelNew(ItemStringListDTO itemStringListDTO) {
                    log.error(HYSTRIX_PATTERN, "listItemInfoBySalesModelNew", itemStringListDTO,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Map<String, String> queryParentItemGuidByItem(ItemStringListDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryParentItemGuidByItem", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Map<String, String> queryParentSkuGuidBySku(ItemStringListDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryParentSkuGuidBySku", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
