package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.kds.entity.domain.BindItemDO;

import java.util.List;


public interface BindItemService extends IService<BindItemDO> {

    List<BindItemDO> listByStoreGuid(String storeGuid);

    List<BindItemDO> listByStoreGuidAndSkuGuids(String storeGuid, List<String> skuGuids);

    List<BindItemDO> listByGroupGuids(List<String> groupGuids);

    List<BindItemDO> listByGroupGuidsAndSkuGuids(List<String> groupGuids, List<String> skuGuids);

    void saveIgnoreBatch(List<BindItemDO> bindItemDOList);

    void removeByGroupGuid(String groupGuid);

    void removeByGroupGuidAndItemGuid(String groupGuid, String itemGuid);

    void removeByGroupGuidAndSkuGuid(String groupGuid, String skuGuid);
}
