package com.holderzone.saas.store.kds.event;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.kds.constant.RocketMqConfig;
import com.holderzone.saas.store.kds.entity.dto.KitchenItemDelayNotificationDTO;
import com.holderzone.saas.store.kds.service.KdsStatusPushService;
import com.holderzone.saas.store.kds.service.KitchenItemDelayNotificationService;
import com.holderzone.saas.store.kds.service.KitchenItemService;
import com.holderzone.saas.store.kds.utils.SnowFlakeUtil;
import com.holderzone.saas.store.kds.utils.ThrowableExtUtils;
import com.holderzone.saas.store.kds.utils.TraceIdUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/09/18 20:47
 * @description
 * @program holder-saas-store-takeaway
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.KDS_MESSAGE_TOPIC,
        tags = {
                RocketMqConfig.KDS_PREPARE_TAG,
                RocketMqConfig.KDS_CHANGES_TAG,
                RocketMqConfig.KDS_TRANSFER_TAG,
                RocketMqConfig.KDS_CALL_TAG,
                RocketMqConfig.KDS_URGE_TAG,
                RocketMqConfig.KDS_REMARK_TAG,
                RocketMqConfig.KDS_CHANGE_TABLE_TAG,
                RocketMqConfig.KDS_REFUND_TAG,
                RocketMqConfig.KDS_DELAY_DISPLAY_TAG
        },
        consumerGroup = RocketMqConfig.KDS_MESSAGE_GROUP)
@AllArgsConstructor
public class KitchenMessageListener extends AbstractRocketMqConsumer<RocketMqTopic, String> {

    private final KitchenItemService kitchenItemService;
    private final KitchenItemDelayNotificationService kitchenItemDelayNotificationService;
    private final KdsStatusPushService kdsStatusPushService;;
    @Override
    public boolean consumeMsg(String jsonStr, MessageExt messageExt) {
        try {
            UserContextUtils.put(messageExt.getProperty("userInfo"));
            EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            TraceIdUtils.setTraceId(String.valueOf(SnowFlakeUtil.getInstance().nextId()));
            switch (messageExt.getTags()) {
                case RocketMqConfig.KDS_PREPARE_TAG:
                    log.info("菜品入厨房入参:{}", jsonStr);
                    kitchenItemService.prepare(JacksonUtils.toObject(ItemPrepareReqDTO.class, jsonStr));
                    break;
                case RocketMqConfig.KDS_CHANGES_TAG:
                    log.info("菜品换菜入参:{}", jsonStr);
                    kitchenItemService.changes(JacksonUtils.toObject(ItemChangesReqDTO.class, jsonStr));
                    break;
                case RocketMqConfig.KDS_TRANSFER_TAG:
                    log.info("转菜入参:{}", jsonStr);
                    kitchenItemService.transfer(JacksonUtils.toObject(KdsItemTransferReqDTO.class, jsonStr));
                    break;
                case RocketMqConfig.KDS_CALL_TAG:
                    log.info("叫起入参:{}", jsonStr);
                    kitchenItemService.call(JacksonUtils.toObject(ItemCallUpReqDTO.class, jsonStr));
                    break;
                case RocketMqConfig.KDS_URGE_TAG:
                    log.info("催菜入参:{}", jsonStr);
                    kitchenItemService.urge(JacksonUtils.toObject(ItemUrgeReqDTO.class, jsonStr));
                    break;
                case RocketMqConfig.KDS_REMARK_TAG:
                    log.info("修改备注入参:{}", jsonStr);
//                    kitchenItemService.remark(JacksonUtils.toObject(OrderRemarkReqDTO.class, jsonStr));
                    break;
                case RocketMqConfig.KDS_CHANGE_TABLE_TAG:
                    log.info("换台入参:{}", jsonStr);
                    kitchenItemService.changeTable(JacksonUtils.toObject(OrderTableReqDTO.class, jsonStr));
                    break;
                case RocketMqConfig.KDS_REFUND_TAG:
                    log.info("退菜入参:{}", jsonStr);
                    kitchenItemService.refund(JacksonUtils.toObject(ItemBatchRefundReqDTO.class, jsonStr));
                    break;
                case RocketMqConfig.KDS_DELAY_DISPLAY_TAG:
                    log.info("延迟显示入参:{}", jsonStr);
                    KitchenItemDelayNotificationDTO dto = JacksonUtils.toObject(KitchenItemDelayNotificationDTO.class,jsonStr);
                    if(dto.getDelayTimeLeft()>0){
                        kitchenItemDelayNotificationService.sendMessage(dto);
                        break ;
                    }
                    log.info("延迟显示到期 {}", jsonStr);
                    kitchenItemService.autoPrint(
                            dto.getOrderGuid(),
                            dto.getStoreGuid(),
                            dto.getKitchenItemGuidList(),
                            dto.getDeviceGuidList());
                    dto.getDeviceGuidList().forEach(deviceGuid-> kdsStatusPushService.statusChanged(
                            UserContextUtils.getEnterpriseGuid(),
                            UserContextUtils.getStoreGuid(),
                            deviceGuid,
                            ""
                    ));
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("厨房消息消费失败：{}", ThrowableExtUtils.asStringIfAbsent(e));
            return false;
        } finally {
            EnterpriseIdentifier.remove();
            TraceIdUtils.clear();
        }
        return true;
    }
}
