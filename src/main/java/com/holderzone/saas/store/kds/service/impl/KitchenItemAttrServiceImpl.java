package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO;
import com.holderzone.saas.store.kds.mapper.KitchenItemAttrMapper;
import com.holderzone.saas.store.kds.service.KitchenItemAttrService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机单据管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
@AllArgsConstructor
public class KitchenItemAttrServiceImpl extends ServiceImpl<KitchenItemAttrMapper, KitchenItemAttrDO> implements KitchenItemAttrService {

    private final KitchenItemAttrMapper kitchenItemAttrMapper;

    @Override
    public List<KitchenItemAttrDO> listByOrderItemGuids(Set<String> idList) {
        return kitchenItemAttrMapper.selectList(new LambdaQueryWrapper<KitchenItemAttrDO>()
                .in(KitchenItemAttrDO::getOrderItemGuid, idList));
    }
}
