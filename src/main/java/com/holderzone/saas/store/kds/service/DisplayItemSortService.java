package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleItemSortDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleItemSortQueryDTO;
import com.holderzone.saas.store.kds.entity.domain.DisplayItemSortDO;

/**
 * <p>
 * 菜品显示顺序配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
public interface DisplayItemSortService extends IService<DisplayItemSortDO> {

    void saveOrUpdateItemSortRule(DisplayRuleItemSortDTO reqDTO);

    DisplayRuleItemSortDTO queryItemSortRule(DisplayRuleItemSortQueryDTO reqDTO);

    DisplayRuleItemSortDTO queryItemSortRuleByStore(DisplayRuleItemSortQueryDTO reqDTO);

}
