package com.holderzone.saas.store.kds.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleQueryDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayItemRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DisplayItemDO;
import com.holderzone.saas.store.kds.mapper.DisplayItemMapper;
import com.holderzone.saas.store.kds.mapstruct.DisplayRuleMapstruct;
import com.holderzone.saas.store.kds.service.DisplayItemService;
import com.holderzone.saas.store.kds.service.rpc.ItemRpcService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.kds.constant.Constants.RULE_TYPE_CANNOT_BE_EMPTY;

/**
 * <AUTHOR>
 * @description
 * @date 2021/1/29 14:38
 */
@Slf4j
@Service
@AllArgsConstructor
public class DisplayItemServiceImpl extends ServiceImpl<DisplayItemMapper, DisplayItemDO> implements DisplayItemService {

    private final DisplayRuleMapstruct ruleMapstruct;

    private final ItemRpcService itemRpcService;

    @Override
    public List<DisplayItemRespDTO> listItem(DisplayRuleQueryDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO.getRuleType())) {
            throw new BusinessException(RULE_TYPE_CANNOT_BE_EMPTY);
        }
        LambdaQueryWrapper<DisplayItemDO> queryWrapper = new LambdaQueryWrapper<DisplayItemDO>()
                .eq(DisplayItemDO::getRuleType, reqDTO.getRuleType());
        if (!ObjectUtils.isEmpty(reqDTO.getRuleGuid())) {
            log.info("编辑查询kds使用商品，规则guid={}", reqDTO.getRuleGuid());
            queryWrapper.ne(DisplayItemDO::getRuleGuid, reqDTO.getRuleGuid());
        }
        List<DisplayItemDO> itemDOList = this.list(queryWrapper);
        List<DisplayItemRespDTO> displayItemRespDTOS = ruleMapstruct.toItemRespList(itemDOList);
        //商品名称实时获取
        List<String> itemGuidList = itemDOList.stream().map(DisplayItemDO::getItemGuid).collect(Collectors.toList());
        queryItemInfo(itemGuidList,displayItemRespDTOS);
        log.info("listItem 最终返回参数：{}", JacksonUtils.writeValueAsString(displayItemRespDTOS));
        return displayItemRespDTOS;
    }

    @Override
    public List<DisplayItemRespDTO> queryItemInfo(List<String> itemGuidList,List<DisplayItemRespDTO> displayItemRespDTOS) {
        List<ItemInfoRespDTO> itemInfoRespDTOList = itemRpcService.kdsItemParentMapping(itemGuidList);
        if (!CollectionUtils.isEmpty(itemInfoRespDTOList)){
            Map<String, ItemInfoRespDTO> itemMap = itemInfoRespDTOList.stream().collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, e -> e));
            displayItemRespDTOS.removeIf(e -> {
                ItemInfoRespDTO itemInfoRespDTO = itemMap.get(e.getItemGuid());
                if (ObjectUtil.isNotNull(itemInfoRespDTO)) {
                    e.setItemName(itemInfoRespDTO.getName());
                    return false;
                }
                return true;
            });
        } else {
            displayItemRespDTOS.clear();
        }
        return displayItemRespDTOS;
    }

    @Override
    /**
     * 处理规格名称
     *
     * @param skuGuidList 规格
     * @return Map<String, ItemWebRespDTO>
     */
    public Map<String, ItemWebRespDTO> mapSkuForName(List<String> skuGuidList) {
        List<ItemWebRespDTO> skuForNameList = itemRpcService.listSkuForName(skuGuidList);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(skuForNameList)) {
            log.warn("未查询到规格对应名称,skuGuidList={}", skuGuidList);
            return new HashMap<>();
        }
        return skuForNameList.stream()
                .collect(Collectors.toMap(ItemWebRespDTO::getSkuGuid, Function.identity(), (v1, v2) -> v2));
    }

}