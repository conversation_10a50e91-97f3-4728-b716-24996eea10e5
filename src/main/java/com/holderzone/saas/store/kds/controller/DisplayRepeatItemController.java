package com.holderzone.saas.store.kds.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.resp.DisplayRepeatItemRespDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRepeatItemStoreReqDTO;
import com.holderzone.saas.store.kds.manager.DisplayRuleManager;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * KDS菜品绑定配置
 */
@Slf4j
@RestController
@RequestMapping("/display_repeat_item")
@AllArgsConstructor
public class DisplayRepeatItemController {

    private final DisplayRuleManager displayRuleManager;

    @ApiOperation(value = "查询菜品绑定配置")
    @GetMapping("/query")
    public DisplayRepeatItemRespDTO query(String brandGuid) {
        return displayRuleManager.queryRepeatItem(brandGuid);
    }

    @ApiOperation(value = "查询门店菜品绑定配置")
    @GetMapping("/query_by_store")
    public DisplayRepeatItemRespDTO queryByStoreGuid(String storeGuid) {
        return displayRuleManager.queryRepeatItemByStoreGuid(storeGuid);
    }

    @ApiOperation(value = "保存菜品绑定配置")
    @PostMapping("/save")
    public void saveOrUpdate(@RequestBody @Validated DisplayRepeatItemStoreReqDTO reqDTO) {
        log.info("保存可重复绑定的门店列表入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        displayRuleManager.saveOrUpdateRepeatItem(reqDTO);
    }
}
