package com.holderzone.saas.store.kds.service.print;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.KdsPrintChangesItemDTO;
import com.holderzone.saas.store.dto.kds.req.KdsPrintPrdDstDTO;
import com.holderzone.saas.store.dto.kds.req.KdsPrintDTO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintBusiness
 * @date 2018/07/31 13:53
 * @description 打印票据类型
 * @program holder-saas-store-print
 */
public enum KdsInvoiceTypeEnum {

    NULL(null, "基础清单", KdsPrintDTO.class),

    PRD_ITEM(0, "制作单", KdsPrintPrdDstDTO.class),

    DST_ITEM(5, "出堂单", KdsPrintPrdDstDTO.class),

    CHANGES_ITEM(10, "换菜单", KdsPrintChangesItemDTO.class);

    private Integer type;

    private String name;

    private Class<? extends KdsPrintDTO> printClass;

    KdsInvoiceTypeEnum(Integer type, String name,
                       Class<? extends KdsPrintDTO> printClass) {
        this.type = type;
        this.name = name;
        this.printClass = printClass;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据type值匹配相应的枚举值
     *
     * @param type
     * @return
     */
    public static KdsInvoiceTypeEnum ofType(Integer type) {
        for (KdsInvoiceTypeEnum invoiceTypeEnum : KdsInvoiceTypeEnum.values()) {
            if (Objects.equals(invoiceTypeEnum.getType(), type)) {
                return invoiceTypeEnum;
            }
        }
        throw new BusinessException("不支持的票据类型：" + type);
    }

    /**
     * 获取匹配票据类型的名字
     *
     * @param type
     * @return
     */
    public static String getInvoiceName(Integer type) {
        return ofType(type).getName();
    }

    public <F extends KdsPrintDTO> F resolvePrint(String jsonStr) {
        return (F) JacksonUtils.toObject(printClass, jsonStr);
    }

    public static <F extends KdsPrintDTO> F resolvePrintBy(String jsonStr) {
        Integer invoiceType = (Integer) JacksonUtils.toJSONObject(jsonStr).get("invoiceType");
        return (F) resolvePrintBy(invoiceType, jsonStr);
    }

    public static <F extends KdsPrintDTO> F resolvePrintBy(Integer invoiceType, String jsonStr) {
        return (F) ofType(invoiceType).resolvePrint(jsonStr);
    }
}
