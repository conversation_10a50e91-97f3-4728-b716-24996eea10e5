package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.kds.req.ItemPrepareReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemStateTransReqDTO;
import com.holderzone.saas.store.dto.kds.req.QueueQueryReqDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsQueueRespDTO;
import com.holderzone.saas.store.dto.kds.resp.KitchenItemDTO;
import com.holderzone.saas.store.dto.kds.resp.QueueItemDTO;
import com.holderzone.saas.store.kds.entity.bo.AsyncTask;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemDO;
import com.holderzone.saas.store.kds.entity.domain.QueueItemDO;

public interface QueueItemService extends IService<QueueItemDO> {

    void inPreparedQueue(AsyncTask<ItemPrepareReqDTO> asyncTask);

    void inDistributedQueue(AsyncTask<ItemStateTransReqDTO> asyncTask);

    void backPreparedQueue(AsyncTask<KitchenItemDO> asyncTask);

    void outPrepareQueue(AsyncTask<KitchenItemDO> asyncTask);

    KdsQueueRespDTO query(QueueQueryReqDTO queueQueryReqDTO);

    void expireAndNotify(QueueItemDTO queueItemDTO);

    void dstCallForMealAgain(KitchenItemDTO kitchenItemDTO);
}
