package com.holderzone.saas.store.kds.service.print.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.kds.ItemComparable;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.KdsPrintOrderDTO;
import com.holderzone.saas.store.dto.kds.resp.KdsPrintRecordDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemTableDTO;
import com.holderzone.saas.store.kds.constant.Constants;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;
import com.holderzone.saas.store.kds.entity.domain.KdsPrintRecordDO;
import com.holderzone.saas.store.kds.entity.domain.KdsPrinterDO;
import com.holderzone.saas.store.kds.entity.domain.KitchenAssociatedOrderDO;
import com.holderzone.saas.store.kds.entity.group.ItemGroupKey;
import com.holderzone.saas.store.kds.entity.query.PrintRecordQuery;
import com.holderzone.saas.store.kds.entity.read.KdsPrintRecordReadDO;
import com.holderzone.saas.store.kds.mapper.KdsPrintRecordMapper;
import com.holderzone.saas.store.kds.mapstruct.KdsPrintMapstruct;
import com.holderzone.saas.store.kds.mapstruct.KitchenItemMapstruct;
import com.holderzone.saas.store.kds.service.DeviceConfigService;
import com.holderzone.saas.store.kds.service.DistributedIdService;
import com.holderzone.saas.store.kds.service.KitchenAssociatedOrderService;
import com.holderzone.saas.store.kds.service.print.*;
import com.holderzone.saas.store.kds.service.template.PrintComponentFactory;
import com.holderzone.saas.store.kds.utils.DeepCloneUtils;
import com.holderzone.saas.store.kds.utils.PrintLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigServiceImpl
 * @date 2018/02/14 09:00
 * @description 打印机单据管理实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class KdsPrintRecordServiceImpl extends ServiceImpl<KdsPrintRecordMapper, KdsPrintRecordDO> implements KdsPrintRecordService {

    private final KdsPrinterService kdsPrinterService;

    private final KdsPrintCacheService kdsPrintCacheService;

    private final KdsPrintPushService kdsPrintPushService;

    private final KdsPrintMapstruct kdsPrintMapstruct;

    private final KitchenItemMapstruct kitchenItemMapstruct;

    private final DeviceConfigService deviceConfigService;

    private final DistributedIdService distributedIdService;

    private final ExecutorService executorService;

    private final PrintComponentFactory printComponentFactory;

    private final KitchenAssociatedOrderService kitchenAssociatedOrderService;

    @Autowired
    public KdsPrintRecordServiceImpl(KdsPrinterService kdsPrinterService,
                                     KdsPrintCacheService kdsPrintCacheService,
                                     KdsPrintPushService kdsPrintPushService,
                                     KdsPrintMapstruct kdsPrintMapstruct,
                                     KitchenItemMapstruct kitchenItemMapstruct,
                                     DeviceConfigService deviceConfigService,
                                     DistributedIdService distributedIdService,
                                     ExecutorService executorService,
                                     PrintComponentFactory printComponentFactory,
                                     KitchenAssociatedOrderService kitchenAssociatedOrderService) {
        this.kitchenItemMapstruct = kitchenItemMapstruct;
        this.deviceConfigService = deviceConfigService;
        this.kdsPrinterService = kdsPrinterService;
        this.kdsPrintCacheService = kdsPrintCacheService;
        this.kdsPrintPushService = kdsPrintPushService;
        this.kdsPrintMapstruct = kdsPrintMapstruct;
        this.distributedIdService = distributedIdService;
        this.executorService = executorService;
        this.printComponentFactory = printComponentFactory;
        this.kitchenAssociatedOrderService = kitchenAssociatedOrderService;
    }

    @Override
    public void printSingleItem(String deviceId, PrdDstItemDTO prdDstItemDTO, KdsInvoiceTypeEnum kdsInvoiceTypeEnum) {
        manualPrintItem(deviceId, kdsInvoiceTypeEnum, Collections.singletonList(prdDstItemDTO), true, false);
    }

    @Override
    public void autoPrintPrdItem(String deviceId, List<PrdDstItemDTO> prdDstItemDTOS, boolean isPrintPerOrder) {
        manualPrintItem(deviceId, KdsInvoiceTypeEnum.PRD_ITEM, prdDstItemDTOS, false, isPrintPerOrder);
    }

    @Override
    public void manualPrintPrdItem(ItemStateTransReqDTO itemStateTransReqDTO, boolean isPrintPerOrder) {
        manualPrintItem(itemStateTransReqDTO.getDeviceId(), KdsInvoiceTypeEnum.PRD_ITEM,
                itemStateTransReqDTO.getPrdDstItemList(), false, isPrintPerOrder);
    }

    @Override
    public void manualPrintDstItem(ItemStateTransReqDTO itemStateTransReqDTO) {
        manualPrintItem(itemStateTransReqDTO.getDeviceId(), KdsInvoiceTypeEnum.DST_ITEM,
                itemStateTransReqDTO.getPrdDstItemList(), false, false);
    }

    @Override
    public void printChangesItem(KdsChangesItemDTO kdsChangesItemDTO) {
        log.info("打印换菜单前入参:{}", JacksonUtils.writeValueAsString(kdsChangesItemDTO));
        // 换菜
        List<KdsItemDTO> originalKdsItemList = kdsChangesItemDTO.getOriginalKdsItemList();
        List<KdsItemDTO> changesKdsItemList = kdsChangesItemDTO.getChangesKdsItemList();
        if (CollectionUtils.isEmpty(originalKdsItemList) || CollectionUtils.isEmpty(changesKdsItemList)) {
            log.warn("换菜商品为空, itemChangesReqDTO:{}", JacksonUtils.writeValueAsString(kdsChangesItemDTO));
            return;
        }
        Set<String> deviceIds = kdsChangesItemDTO.getDeviceIds();
        if (CollectionUtils.isEmpty(deviceIds)) {
            log.warn("换菜前和换菜后商品都没绑定制作口/出堂口, itemChangesReqDTO:{}", JacksonUtils.writeValueAsString(kdsChangesItemDTO));
            return;
        }
        KdsInvoiceTypeEnum kdsInvoiceTypeEnum = KdsInvoiceTypeEnum.CHANGES_ITEM;
        for (String deviceId : deviceIds) {
            // 查找可用打印机
            KdsPrinterDO printer = findAvailablePrinter(deviceId, kdsInvoiceTypeEnum, false);
            if (printer == null) {
                continue;
            }
            log.info("开始执行打印流程，打印机信息：{}，原菜品信息：{}, 更换菜品信息:{}",
                    JacksonUtils.writeValueAsString(printer), JacksonUtils.writeValueAsString(originalKdsItemList),
                    JacksonUtils.writeValueAsString(changesKdsItemList));
            // 保存记录
            Pair<KdsPrintRecordDO, KdsPrintRecordReadDO> pair = processChangesItem(kdsChangesItemDTO, deviceId, printer);
            this.save(pair.getFirst());
            kdsPrintCacheService.save(Lists.newArrayList(pair.getSecond()));
            // 推送打印任务
            KdsPrintDTO kdsPrintDTO = new KdsPrintDTO();
            kdsPrintDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            kdsPrintDTO.setStoreGuid(UserContextUtils.getStoreGuid());
            executorService.execute(() -> kdsPrintPushService.pushPrintTaskMsg(kdsPrintDTO, Lists.newArrayList(pair.getFirst())));
        }
    }

    @Override
    public List<KdsPrintOrderDTO> getPrintOrder(KdsPrintRecordReqDTO kdsPrintRecordReqDTO) {
        if (kdsPrintCacheService.hasMsgId(kdsPrintRecordReqDTO.getMsgId(), kdsPrintRecordReqDTO.getRecordGuid(),
                kdsPrintRecordReqDTO.getArrayOfRecordGuid())) {
            log.error("重复获取打印内容, printRecordReqDTO:{}", JacksonUtils.writeValueAsString(kdsPrintRecordReqDTO));
            return Lists.newArrayList();
        }
        List<KdsPrintOrderDTO> printOrderContent = getPrintOrderContent(kdsPrintRecordReqDTO);
        kdsPrintCacheService.saveMsgId(kdsPrintRecordReqDTO.getMsgId(), kdsPrintRecordReqDTO.getRecordGuid(),
                kdsPrintRecordReqDTO.getArrayOfRecordGuid());
        return printOrderContent;
    }


    @Override
    public List<KdsPrintRecordDTO> listRecord(KdsPrintRecordReqDTO kdsPrintRecordReqDTO) {
        List<KdsPrintRecordDO> printRecordInSql = list(new LambdaQueryWrapper<KdsPrintRecordDO>()
                .eq(KdsPrintRecordDO::getDeviceId, kdsPrintRecordReqDTO.getDeviceId())
                .eq(KdsPrintRecordDO::getPrintStatus, kdsPrintRecordReqDTO.getPrintStatus())
        );
        return printRecordInSql.stream()
                .map(kdsPrintRecordDO -> {
                    KdsPrintRecordDTO kdsPrintRecordDTO = new KdsPrintRecordDTO();
                    kdsPrintRecordDTO.setRecordUid(kdsPrintRecordDO.getRecordUid());
                    kdsPrintRecordDTO.setRecordGuid(kdsPrintRecordDO.getGuid());
                    kdsPrintRecordDTO.setStoreGuid(kdsPrintRecordDO.getStoreGuid());
                    kdsPrintRecordDTO.setDeviceId(kdsPrintRecordDO.getDeviceId());
                    kdsPrintRecordDTO.setInvoiceType(kdsPrintRecordDO.getInvoiceType());
                    kdsPrintRecordDTO.setPrinterGuid(kdsPrintRecordDO.getPrinterGuid());
                    kdsPrintRecordDTO.setPrintStatus(kdsPrintRecordDO.getPrintStatus());
                    kdsPrintRecordDTO.setPrintContent(KdsInvoiceTypeEnum.resolvePrintBy(
                            kdsPrintRecordDO.getInvoiceType(), kdsPrintRecordDO.getPrintContent()));
                    kdsPrintRecordDTO.setCreateStaffGuid(kdsPrintRecordDO.getCreateStaffGuid());
                    kdsPrintRecordDTO.setGmtCreate(kdsPrintRecordDO.getGmtCreate());
                    kdsPrintRecordDTO.setGmtModified(kdsPrintRecordDO.getGmtModified());
                    // todo
                    kdsPrintRecordDTO.setMarkName("");
                    kdsPrintRecordDTO.setMarkNo("");
                    kdsPrintRecordDTO.setPrinterIp("");
                    kdsPrintRecordDTO.setPrinterName("");
                    return kdsPrintRecordDTO;
                })
                .collect(Collectors.toList());
    }

    @Override
    public void updatePrintResult(KdsPrintRecordReqDTO kdsPrintRecordReqDTO) {
        String recordGuid = kdsPrintRecordReqDTO.getRecordGuid();
        Wrapper<KdsPrintRecordDO> wrapper = wrapperByRecordGuid(recordGuid);
        if (0 == count(wrapper)) {
            return;
        }
        if (KdsPrintRecordReqDTO.STATUS_FAILED.equals(kdsPrintRecordReqDTO.getPrintStatus())) {
            update(new KdsPrintRecordDO()
                    .setPrintStatus(kdsPrintRecordReqDTO.getPrintStatus())
                    .setPrintStatusMsg(kdsPrintRecordReqDTO.getPrintStatusMsg()), wrapper);
            int countFailedByDevice = countByDeviceAndStatus(kdsPrintRecordReqDTO);
            KdsPrintRecordDO printRecordBefore = getOne(new LambdaQueryWrapper<KdsPrintRecordDO>()
                    .select(KdsPrintRecordDO::getRecordUid,
                            KdsPrintRecordDO::getInvoiceType, KdsPrintRecordDO::getPrintContent)
                    .eq(KdsPrintRecordDO::getGuid, recordGuid));
            String searchKey = PrintLogUtils.searchKey(printRecordBefore.getRecordUid(), printRecordBefore.getInvoiceType());
            log.info("Client端更新打印结果：打印失败，searchKey={}", searchKey);
            kdsPrintPushService.pushPrintFailedMsg(kdsPrintRecordReqDTO, printRecordBefore, countFailedByDevice);
        } else {
            KdsPrintRecordDO printRecordBefore = getOne(new LambdaQueryWrapper<KdsPrintRecordDO>()
                    .select(KdsPrintRecordDO::getRecordUid, KdsPrintRecordDO::getPrintStatus,
                            KdsPrintRecordDO::getInvoiceType, KdsPrintRecordDO::getPrintContent)
                    .eq(KdsPrintRecordDO::getGuid, recordGuid));
            String searchKey = PrintLogUtils.searchKey(printRecordBefore.getRecordUid(), printRecordBefore.getInvoiceType());
            update(new KdsPrintRecordDO()
                    .setPrintStatus(kdsPrintRecordReqDTO.getPrintStatus())
                    .setPrintStatusMsg(kdsPrintRecordReqDTO.getPrintStatusMsg()), wrapper);
            if (KdsPrintRecordReqDTO.STATUS_FAILED.equals(printRecordBefore.getPrintStatus())) {
                log.info("Client端更新打印结果：重打成功，searchKey={}", searchKey);
                int countFailedByDevice = countByDeviceAndStatus(kdsPrintRecordReqDTO);
                kdsPrintPushService.pushPrintSucceedMsg(kdsPrintRecordReqDTO, printRecordBefore, countFailedByDevice);
            } else {
                log.info("Client端更新打印结果：打印成功，searchKey={}", searchKey);
            }
        }
    }

    @Override
    public void deleteRecord(KdsPrintRecordReqDTO kdsPrintRecordReqDTO) {
        remove(wrapperByRecordGuid(kdsPrintRecordReqDTO.getRecordGuid()));
    }

    @Override
    public void batchDeleteRecord(List<String> arrayOfRecordGuid) {
        if (CollectionUtils.isEmpty(arrayOfRecordGuid)) {
            return;
        }
        remove(wrapperByRecordGuid(arrayOfRecordGuid));
    }

    private void manualPrintItem(String deviceId, KdsInvoiceTypeEnum kdsInvoiceTypeEnum,
                                 List<PrdDstItemDTO> prdDstItemDTOS, boolean isThrow,
                                 boolean isPrintPerOrder
    ) {
        if (CollectionUtils.isEmpty(prdDstItemDTOS)) {
            return;
        }

        // 查找可用打印机
        KdsPrinterDO printer = findAvailablePrinter(deviceId, kdsInvoiceTypeEnum, isThrow);
        if (printer == null){
            return;
        }

        log.info("开始执行打印流程，打印机信息：{}，菜品信息：{}",
                JacksonUtils.writeValueAsString(printer), JacksonUtils.writeValueAsString(prdDstItemDTOS));

        // 保存记录
        // 设置联台信息
        setPrdDstItemAssociatedTableInfo(prdDstItemDTOS);
        boolean isPrintPerItem = KdsInvoiceTypeEnum.PRD_ITEM == kdsInvoiceTypeEnum && !isPrintPerOrder;
        Pair<List<KdsPrintRecordDO>, List<KdsPrintRecordReadDO>> pair = isPrintPerItem
                ? processPerItem(prdDstItemDTOS, deviceId, printer)
                : processPerOrder(prdDstItemDTOS, deviceId, printer, kdsInvoiceTypeEnum);
        this.saveBatch(pair.getFirst());
        kdsPrintCacheService.save(pair.getSecond());

        // 推送打印任务
        KdsPrintDTO kdsPrintDTO = new KdsPrintDTO();
        kdsPrintDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        kdsPrintDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        executorService.execute(() -> kdsPrintPushService.pushPrintTaskMsg(kdsPrintDTO, pair.getFirst()));
    }

    /**
     * 设置联台信息
     */
    private void setPrdDstItemAssociatedTableInfo(List<PrdDstItemDTO> prdDstItemDTOList) {
        List<PrdDstItemTableDTO> hasKitchenItemList = prdDstItemDTOList.stream()
                .filter(e -> !CollectionUtils.isEmpty(e.getKitchenItemList()))
                .flatMap(e -> e.getKitchenItemList().stream())
                .collect(Collectors.toList());
        List<String> orderGuids = hasKitchenItemList.stream()
                .map(PrdDstItemTableDTO::getOrderGuid)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderGuids)) {
            return;
        }
        List<KitchenAssociatedOrderDO> kitchenAssociatedOrderList = kitchenAssociatedOrderService.listByOrderGuids(orderGuids);
        Map<String, KitchenAssociatedOrderDO> kitchenAssociatedOrderMap = kitchenAssociatedOrderList.stream()
                .collect(Collectors.toMap(KitchenAssociatedOrderDO::getOrderGuid, Function.identity(), (key1, key2) -> key1));
        for (PrdDstItemTableDTO prdDstItemTableDTO : hasKitchenItemList) {
            KitchenAssociatedOrderDO associatedOrderDO = kitchenAssociatedOrderMap.get(prdDstItemTableDTO.getOrderGuid());
            if (Objects.isNull(associatedOrderDO)
                    || Boolean.FALSE.equals(associatedOrderDO.getAssociatedFlag())
                    || StringUtils.isEmpty(associatedOrderDO.getAssociatedTableNames())) {
                continue;
            }
            prdDstItemTableDTO.setOrderSerialNo(buildAssociatedTableName(associatedOrderDO));
        }
    }

    /**
     * 构建联台单显示桌台信息
     */
    private String buildAssociatedTableName(KitchenAssociatedOrderDO associatedOrderDO) {
        // 联台单
        List<String> associatedTableNames = JacksonUtils.toObjectList(String.class, associatedOrderDO.getAssociatedTableNames());
        int totalSize = associatedTableNames.size();
        String associatedTableNameStr = String.join("、", associatedTableNames);
        if (totalSize > 10) {
            associatedTableNames = associatedTableNames.subList(0, 10);
            associatedTableNameStr = String.join("、", associatedTableNames) + "等" + totalSize + "桌";
        }
        String associatedTableName = "联-%s (%s)";
        return String.format(associatedTableName, associatedOrderDO.getAssociatedSn(), associatedTableNameStr);
    }

    /**
     * 查找可用打印机
     *
     * @param deviceId           设备ID
     * @param kdsInvoiceTypeEnum 单据类型
     * @param isThrow            未找到打印机时是否抛出异常
     * @return 可用打印机，可能为空
     */
    private KdsPrinterDO findAvailablePrinter(String deviceId, KdsInvoiceTypeEnum kdsInvoiceTypeEnum, boolean isThrow) {
        if (StringUtils.isEmpty(deviceId)) {
            log.error("deviceId为空");
            return null;
        }
        String searchKey = PrintLogUtils.searchKey(deviceId, kdsInvoiceTypeEnum.getType());
        DeviceConfigDO deviceConfigInSql = deviceConfigService.getOne(new LambdaQueryWrapper<DeviceConfigDO>()
                .eq(DeviceConfigDO::getGuid, deviceId));
        String printerGuid = deviceConfigInSql.getPrinterGuid();
        if (!StringUtils.hasText(printerGuid)) {
            if (isThrow) {
                throw new BusinessException("暂未找到打印机");
            }
            log.warn("未匹配到打印机，searchKey={}", searchKey);
            return null;
        }
        KdsPrinterDO printer = kdsPrinterService.getOne(new LambdaQueryWrapper<KdsPrinterDO>()
                .eq(KdsPrinterDO::getGuid, printerGuid));
        if (null == printer) {
            if (isThrow) {
                throw new BusinessException("暂未找到打印机");
            }
            log.warn("未匹配到打印机，searchKey={}", searchKey);
            return null;
        }
        return printer;
    }

    /**
     * 规则如下
     *
     * @param prdDstItemList 调用方需保证不为空列表
     * @param deviceId       调用方需保证不为空字符串
     * @param printer        调用方需保证不为空
     * @return 打印记录DO 和 打印记录ReadDO 的Pair对
     */
    private Pair<List<KdsPrintRecordDO>, List<KdsPrintRecordReadDO>> processPerItem(List<PrdDstItemDTO> prdDstItemList,
                                                                                    String deviceId, KdsPrinterDO printer) {
        // 一个菜品一张制作单
        List<PrdDstItemDTO> values = splitAndMergeItems(prdDstItemList);

        List<PrdDstItemDTO> valuesSorted = values.stream().sorted(ItemComparable::compareTo).collect(Collectors.toList());

        // 批量Guid
        List<String> batchGuid = valuesSorted.size() == 0 ? Collections.emptyList() : distributedIdService.nextBatchPrintRecordGuid(valuesSorted.size());

        List<KdsPrintRecordDO> printRecordsToInsert = new ArrayList<>();
        List<KdsPrintRecordReadDO> printRecordsToCache = new ArrayList<>();
        for (PrdDstItemDTO prdDstItemDTO : valuesSorted) {
            KdsPrintPrdDstDTO kdsPrintPrdDstDTO = getKdsPrintPrdDstDTO(prdDstItemDTO, deviceId);
            kdsPrintPrdDstDTO.setInvoiceType(KdsInvoiceTypeEnum.PRD_ITEM.getType());
            kdsPrintPrdDstDTO.setItemRecordList(Collections.singletonList(prdDstItemDTO));
            String recordGuid = batchGuid.remove(batchGuid.size() - 1);
            KdsPrintRecordDO printRecordDO = buildPrintRecord(kdsPrintPrdDstDTO, printer.getGuid(), recordGuid);
            printRecordsToInsert.add(printRecordDO);
            printRecordsToCache.add(mockPrintRecordReadDO(printRecordDO, printer));
        }
        return Pair.of(printRecordsToInsert, printRecordsToCache);
    }

    /**
     * 规则如下
     *
     * @param prdDstItemList 调用方需保证不为空列表
     * @param deviceId       调用方需保证不为空字符串
     * @param printer        调用方需保证不为空
     * @param kdsInvoiceTypeEnum
     * @return 打印记录DO 和 打印记录ReadDO 的Pair对
     */
    private Pair<List<KdsPrintRecordDO>, List<KdsPrintRecordReadDO>> processPerOrder(List<PrdDstItemDTO> prdDstItemList,
                                                                                     String deviceId, KdsPrinterDO printer,
                                                                                     KdsInvoiceTypeEnum kdsInvoiceTypeEnum) {
        // 一个订单一张出堂单
        List<List<PrdDstItemDTO>> orderListSrc = splitAndMergeItemsThenGroupByOrder(prdDstItemList);
        List<List<PrdDstItemDTO>> orderList = new ArrayList<>();
        for (List<PrdDstItemDTO> orderItemAll : orderListSrc) {
            List<List<PrdDstItemDTO>> subList = null;
            if(KdsInvoiceTypeEnum.PRD_ITEM.getType().equals(kdsInvoiceTypeEnum.getType())){
                 subList=sortAndGroupPrdDstItemListByBatch(orderItemAll);
            }else{
                subList = new ArrayList<>();
                subList.add(orderItemAll);
            }
            orderList.addAll(subList);
        }

        // 批量Guid
        List<String> batchGuid = distributedIdService.nextBatchPrintRecordGuid(orderList.size());

        // 实体转换并持久化
        List<KdsPrintRecordDO> printRecordsToInsert = new ArrayList<>();
        List<KdsPrintRecordReadDO> printRecordsToCache = new ArrayList<>();
        for (List<PrdDstItemDTO> orderItems : orderList) {
                PrdDstItemDTO prdDstItemDTO = orderItems.get(0);
                KdsPrintPrdDstDTO kdsPrintPrdDstDTO = getKdsPrintPrdDstDTO(prdDstItemDTO, deviceId);
                kdsPrintPrdDstDTO.setInvoiceType(kdsInvoiceTypeEnum.getType());
                kdsPrintPrdDstDTO.setItemRecordList(orderItems);
                String recordGuid = batchGuid.remove(batchGuid.size() - 1);
                KdsPrintRecordDO printRecordDO = buildPrintRecord(kdsPrintPrdDstDTO, printer.getGuid(), recordGuid);
                printRecordsToInsert.add(printRecordDO);
                printRecordsToCache.add(mockPrintRecordReadDO(printRecordDO, printer));

        }

        return Pair.of(printRecordsToInsert, printRecordsToCache);
    }

    /**
     * 换菜打印记录
     */
    private Pair<KdsPrintRecordDO, KdsPrintRecordReadDO> processChangesItem(KdsChangesItemDTO kdsChangesItemDTO,
                                                                            String deviceId, KdsPrinterDO printer) {
        KdsPrintChangesItemDTO kdsPrintChangesItemDTO = getKdsPrintChangesItemDTO(kdsChangesItemDTO, deviceId);
        log.info("换菜打印小票参数, kdsPrintChangesItemDTO:{}", JacksonUtils.writeValueAsString(kdsPrintChangesItemDTO));
        kdsPrintChangesItemDTO.setInvoiceType(KdsInvoiceTypeEnum.CHANGES_ITEM.getType());
        String recordGuid = distributedIdService.nextPrintRecordGuid();
        KdsPrintRecordDO printRecordDO = buildPrintRecord(kdsPrintChangesItemDTO, printer.getGuid(), recordGuid);
        return Pair.of(printRecordDO, mockPrintRecordReadDO(printRecordDO, printer));
    }

    /**
     *根据批次将订单拆分，并且为升序，batch为null排在最后一批
     *
     * @param orderItems
     * @return
     */
    private static  List<List<PrdDstItemDTO>> sortAndGroupPrdDstItemListByBatch(List<PrdDstItemDTO> orderItems){
        List<List<PrdDstItemDTO>> subList = new ArrayList<>();
        List<PrdDstItemDTO> nullBatchItem=orderItems.stream().filter(s->s.getBatch()==null||s.getBatch()== Constants.ITEM_NO_RULE_BATCH).collect(Collectors.toList());
        List<PrdDstItemDTO> delayedItem=orderItems.stream().filter(s->s.getBatch()!=null&&s.getBatch()== Constants.ITEM_DELAYED_BATCH).collect(Collectors.toList());
        List<PrdDstItemDTO> delayedUrgeItem=orderItems.stream().filter(s->s.getBatch()!=null&&s.getBatch()== Constants.ITEM_DELAYED_URGE_BATCH).collect(Collectors.toList());

        Map<Integer,List<PrdDstItemDTO>> batchItemMap = orderItems.stream().filter(s->s.getBatch()!=null)
                .filter(j->j.getBatch()>=Constants.ITEM_BATCH_MIN &&j.getBatch()<=Constants.ITEM_BATCH_MAX)
                .collect(Collectors.groupingBy(PrdDstItemDTO::getBatch));
        List<Integer> batchList = batchItemMap.keySet().stream().sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());
        for(Integer batch : batchList){
            subList.add(batchItemMap.get(batch));
        }
        if(!delayedUrgeItem.isEmpty()){
            if(subList.isEmpty()){
                subList.add(delayedUrgeItem);
            } else{
                delayedUrgeItem.addAll(subList.get(0));
                subList.set(0,delayedUrgeItem);
            }
        }

        if(!CollectionUtils.isEmpty(nullBatchItem)){
            if(subList.isEmpty()){
                subList.add(new ArrayList<>());
            }
            subList.get(subList.size()-1).addAll(nullBatchItem);
        }
        if(!CollectionUtils.isEmpty(delayedItem)){
            subList.add(delayedItem);
        }
        for(int i=0;i<subList.size();i++){
            int batch = i+1;
            subList.get(i).forEach(s->{
                s.setBatch(batch);
            });
        }
        return subList;
    }


    /**
     * KDS商品实体 转化为 KDS打印商品实体
     *
     * @param prdDstItemDTO
     * @param deviceId
     * @return
     */
    private KdsPrintPrdDstDTO getKdsPrintPrdDstDTO(PrdDstItemDTO prdDstItemDTO, String deviceId) {
        UserContext userContext = UserContextUtils.get();
        String enterpriseGuid = userContext.getEnterpriseGuid();
        String storeGuid = userContext.getStoreGuid();
        String userGuid = userContext.getUserGuid();
        String userName = userContext.getUserName();
        String userAccount = userContext.getAccount();
        KdsPrintPrdDstDTO kdsPrintPrdDstDTO = new KdsPrintPrdDstDTO();
        kdsPrintPrdDstDTO.setOrderDesc(prdDstItemDTO.getOrderDesc());
        kdsPrintPrdDstDTO.setOrderNumber(prdDstItemDTO.getOrderNumber());
        kdsPrintPrdDstDTO.setOrderSerialNo(prdDstItemDTO.getOrderSerialNo());
        kdsPrintPrdDstDTO.setEnterpriseGuid(enterpriseGuid);
        kdsPrintPrdDstDTO.setStoreGuid(storeGuid);
        kdsPrintPrdDstDTO.setPrintUid(prdDstItemDTO.getOrderGuid());
        kdsPrintPrdDstDTO.setOperatorStaffGuid(userGuid);
        kdsPrintPrdDstDTO.setOperatorStaffName(userName);
        kdsPrintPrdDstDTO.setOperatorStaffAccount(userAccount);
        kdsPrintPrdDstDTO.setCreateTime(DateTimeUtils.now());
        kdsPrintPrdDstDTO.setDeviceId(deviceId);
        return kdsPrintPrdDstDTO;
    }

    /**
     * 构建打印换菜单数据
     */
    private KdsPrintChangesItemDTO getKdsPrintChangesItemDTO(KdsChangesItemDTO kdsChangesItemDTO, String deviceId) {
        UserContext userContext = UserContextUtils.get();
        String enterpriseGuid = userContext.getEnterpriseGuid();
        String storeGuid = userContext.getStoreGuid();
        String userGuid = userContext.getUserGuid();
        String userName = userContext.getUserName();
        String userAccount = userContext.getAccount();
        KdsPrintChangesItemDTO kdsPrintChangesItemDTO = new KdsPrintChangesItemDTO();
        kdsPrintChangesItemDTO.setOrderNo(kdsChangesItemDTO.getOrderNo());
        kdsPrintChangesItemDTO.setDiningTableName(kdsChangesItemDTO.getDiningTableName());
        kdsPrintChangesItemDTO.setCreateStaffName(kdsChangesItemDTO.getCreateStaffName());
        kdsPrintChangesItemDTO.setGmtCreate(kdsChangesItemDTO.getGmtCreate());
        kdsPrintChangesItemDTO.setOriginalKdsItemList(kdsChangesItemDTO.getOriginalKdsItemList());
        kdsPrintChangesItemDTO.setChangesKdsItemList(kdsChangesItemDTO.getChangesKdsItemList());
        kdsPrintChangesItemDTO.setEnterpriseGuid(enterpriseGuid);
        kdsPrintChangesItemDTO.setStoreGuid(storeGuid);
        kdsPrintChangesItemDTO.setPrintUid(kdsChangesItemDTO.getOrderGuid());
        kdsPrintChangesItemDTO.setOperatorStaffGuid(userGuid);
        kdsPrintChangesItemDTO.setOperatorStaffName(userName);
        kdsPrintChangesItemDTO.setOperatorStaffAccount(userAccount);
        kdsPrintChangesItemDTO.setCreateTime(DateTimeUtils.now());
        kdsPrintChangesItemDTO.setDeviceId(deviceId);
        Map<String, List<String>> deviceItemMap = kdsChangesItemDTO.getDeviceItemMap();
        kdsPrintChangesItemDTO.setArrayOfItemGuid(deviceItemMap.getOrDefault(deviceId, Lists.newArrayList()));
        kdsPrintChangesItemDTO.setCancelFlag(kdsChangesItemDTO.getCancelFlag());
        return kdsPrintChangesItemDTO;
    }

    /**
     * 拆分、合并商品
     *
     * @param prdDstItemList
     * @return
     */
    private List<PrdDstItemDTO> splitAndMergeItems(List<PrdDstItemDTO> prdDstItemList) {
        Map<ItemGroupKey, PrdDstItemDTO> collect = prdDstItemList.stream().flatMap(this::splitItem)
                .collect(Collectors.toMap(ItemGroupKey::of, Function.identity(), this::mergeItem));
        return new ArrayList<>(collect.values());
    }

    /**
     * 拆分、合并、分类商品
     *
     * @param prdDstItemList
     * @return
     */
    private List<List<PrdDstItemDTO>> splitAndMergeItemsThenGroupByOrder(List<PrdDstItemDTO> prdDstItemList) {
        Map<String, List<PrdDstItemDTO>> collect = splitAndMergeItems(prdDstItemList).stream().collect(Collectors.groupingBy(PrdDstItemDTO::getOrderGuid));

        return new ArrayList<>(collect.values());
    }

    /**
     * 拆分商品
     *
     * @param prdDstItemDTO
     * @return
     */
    private Stream<? extends PrdDstItemDTO> splitItem(PrdDstItemDTO prdDstItemDTO) {
        if (prdDstItemDTO.getIsWeight()) {
            return splitWeightItem(prdDstItemDTO);
        }
        return splitNormalItem(prdDstItemDTO);
    }

    /**
     * 拆分称重商品
     *
     * @param prdDstItemDTO
     * @return
     */
    private Stream<? extends PrdDstItemDTO> splitWeightItem(PrdDstItemDTO prdDstItemDTO) {
        PrdDstItemTableDTO weightKitchenItem = Objects.requireNonNull(
                prdDstItemDTO.getWeightKitchenItem(), "称重商品不得为空"
        );
        // weightKitchenItem打印时未使用，故设为null以减小在mysql的存储
        prdDstItemDTO.setWeightKitchenItem(null);
        prdDstItemDTO.copyItemTableInfo(weightKitchenItem);
        return Stream.of(prdDstItemDTO);
    }

    /**
     * 拆分非称重商品
     *
     * @param prdDstItemDTO
     * @return
     */
    private Stream<? extends PrdDstItemDTO> splitNormalItem(PrdDstItemDTO prdDstItemDTO) {
        List<PrdDstItemTableDTO> kitchenItemList = Objects.requireNonNull(
                prdDstItemDTO.getKitchenItemList(), "非称重商品不得为空"
        ).subList(0, prdDstItemDTO.getCurrentCount().intValue());
        // kitchenItemList打印时未使用，故设为null以减小在mysql的存储
        prdDstItemDTO.setKitchenItemList(null);
        prdDstItemDTO.setCurrentCount(BigDecimal.ONE);
        return kitchenItemList.stream()
                .map(prdDstItemTableDTO -> {
                    PrdDstItemDTO prdDstItemCloned = (PrdDstItemDTO)
                            DeepCloneUtils.cloneObject(prdDstItemDTO);
                    prdDstItemCloned.copyItemTableInfo(prdDstItemTableDTO);
                    return prdDstItemCloned;
                });
    }

    /**
     * 合并称重、非称重商品
     *
     * @param curPrdDstItem
     * @param nextPrdDstItem
     * @return
     */
    private PrdDstItemDTO mergeItem(PrdDstItemDTO curPrdDstItem, PrdDstItemDTO nextPrdDstItem) {
        curPrdDstItem.setCurrentCount(curPrdDstItem.getCurrentCount().add(nextPrdDstItem.getCurrentCount()));
        curPrdDstItem.setIsHanged(curPrdDstItem.getIsHanged() || nextPrdDstItem.getIsHanged());
        curPrdDstItem.setIsUrged(curPrdDstItem.getIsUrged() || nextPrdDstItem.getIsUrged());
        return curPrdDstItem;
    }

    /**
     * 构造打印记录DO
     *
     * @param kdsPrintDTO
     * @param printerGuid
     * @param recordGuid
     * @return
     */
    private KdsPrintRecordDO buildPrintRecord(KdsPrintDTO kdsPrintDTO, String printerGuid, String recordGuid) {
        KdsPrintRecordDO printRecordDO = new KdsPrintRecordDO();
        printRecordDO.setGuid(recordGuid);
        printRecordDO.setRecordUid(kdsPrintDTO.getPrintUid());
        printRecordDO.setStoreGuid(kdsPrintDTO.getStoreGuid());
        printRecordDO.setDeviceId(kdsPrintDTO.getDeviceId());
        printRecordDO.setInvoiceType(kdsPrintDTO.getInvoiceType());
        printRecordDO.setPrinterGuid(printerGuid);
        printRecordDO.setCreateStaffGuid(kdsPrintDTO.getOperatorStaffGuid());
        printRecordDO.setPrintContent(JacksonUtils.writeValueAsString(kdsPrintDTO));
        return printRecordDO;
    }

    /**
     * 构造打印记录ReadDO，并存入Redis，以提高性能
     *
     * @param kdsPrintRecordDO
     * @param kdsPrinterDO
     * @return
     */
    private static KdsPrintRecordReadDO mockPrintRecordReadDO(KdsPrintRecordDO kdsPrintRecordDO,
                                                              KdsPrinterDO kdsPrinterDO) {
        KdsPrintRecordReadDO printRecordReadDO = new KdsPrintRecordReadDO();
        printRecordReadDO.setGuid(kdsPrintRecordDO.getGuid());
        printRecordReadDO.setRecordUid(kdsPrintRecordDO.getRecordUid());
        printRecordReadDO.setStoreGuid(kdsPrintRecordDO.getStoreGuid());
        printRecordReadDO.setInvoiceType(kdsPrintRecordDO.getInvoiceType());
        printRecordReadDO.setPrintContent(kdsPrintRecordDO.getPrintContent());
        printRecordReadDO.setKdsPrinterDO(kdsPrinterDO);
        return printRecordReadDO;
    }

    /**
     * 打印记录ReadDO 转换为 打印单DTO
     *
     * @param kdsPrintRecordReqDTO
     * @param printRecordReadDO
     * @return
     */
    private KdsPrintOrderDTO getOrderByPerDO(KdsPrintRecordReqDTO kdsPrintRecordReqDTO,
                                             KdsPrintRecordReadDO printRecordReadDO) {
        KdsPrinterDO printerDO = printRecordReadDO.getKdsPrinterDO();
        int pageSize = printerDO.getPageSize();
        Integer invoiceType = printRecordReadDO.getInvoiceType();
        String printContent = printRecordReadDO.getPrintContent();
        KdsPrintTemplate<? extends KdsPrintDTO> printTemplate =
                printComponentFactory.create(invoiceType, pageSize, printContent);
        KdsPrintDTO printDTO = printTemplate.getPrintDTO();
        printDTO.setOperatorStaffGuid(kdsPrintRecordReqDTO.getUserGuid());
        printDTO.setOperatorStaffName(kdsPrintRecordReqDTO.getUserName());
        printDTO.setOperatorStaffAccount(kdsPrintRecordReqDTO.getAccount());

        return new KdsPrintOrderDTO()
                .setPrintKey(printRecordReadDO.getGuid())
                .setPageSize(pageSize)
                .setPrintTimes(1)
                .setPrinterType(1)
                .setPrinterIp(printerDO.getPrinterIp())
                .setPrinterPort(printerDO.getPrinterPort())
                .setPrintRows(printTemplate.getPrintRows());
    }

    /**
     * 计算打印失败记录条数
     *
     * @param kdsPrintRecordReqDTO
     * @return
     */
    private int countByDeviceAndStatus(KdsPrintRecordReqDTO kdsPrintRecordReqDTO) {
        return count(new LambdaQueryWrapper<KdsPrintRecordDO>()
                .eq(KdsPrintRecordDO::getDeviceId, kdsPrintRecordReqDTO.getDeviceId())
                .eq(KdsPrintRecordDO::getPrintStatus, KdsPrintRecordReqDTO.STATUS_FAILED));
    }

    private Wrapper<KdsPrintRecordDO> wrapperByRecordGuid(List<String> arrayOfRecordGuid) {
        return new LambdaQueryWrapper<KdsPrintRecordDO>()
                .in(KdsPrintRecordDO::getGuid, arrayOfRecordGuid);
    }

    private Wrapper<KdsPrintRecordDO> wrapperByRecordGuid(String recordGuid) {
        return new LambdaQueryWrapper<KdsPrintRecordDO>()
                .eq(KdsPrintRecordDO::getGuid, recordGuid);
    }

    private List<KdsPrintOrderDTO> getPrintOrderContent(KdsPrintRecordReqDTO kdsPrintRecordReqDTO) {
        PrintRecordQuery printRecordQuery = kdsPrintMapstruct.printRecordReqToQuery(kdsPrintRecordReqDTO);
        if (StringUtils.hasText(printRecordQuery.getRecordGuid())) {
            KdsPrintRecordReadDO printRecordReadDO = kdsPrintCacheService.popSingle(printRecordQuery.getRecordGuid());
            if (null == printRecordReadDO) {
                printRecordReadDO = baseMapper.queryByRecordGuid(printRecordQuery);
            }
            if (null == printRecordReadDO) {
                return Collections.emptyList();
            }
            String searchKey = PrintLogUtils.searchKey(printRecordReadDO.getRecordUid(), printRecordReadDO.getInvoiceType());
            log.info("Client端拉取打印单，searchKey={}", searchKey);
            return Collections.singletonList(this.getOrderByPerDO(kdsPrintRecordReqDTO, printRecordReadDO));
        }
        if (!CollectionUtils.isEmpty(printRecordQuery.getArrayOfRecordGuid())) {
            List<KdsPrintRecordReadDO> arrayOfPrintRecordReadDO = kdsPrintCacheService.popBatch(printRecordQuery.getArrayOfRecordGuid());
            if (CollectionUtils.isEmpty(arrayOfPrintRecordReadDO)) {
                arrayOfPrintRecordReadDO = baseMapper.queryInRecordGuid(printRecordQuery);
            }
            if (CollectionUtils.isEmpty(arrayOfPrintRecordReadDO)) {
                return Collections.emptyList();
            }
            return arrayOfPrintRecordReadDO.stream()
                    .peek(printRecordReadDO -> {
                        String searchKey = PrintLogUtils.searchKey(printRecordReadDO.getRecordUid(), printRecordReadDO.getInvoiceType());
                        log.info("Client端拉取打印单，searchKey={}", searchKey);
                    })
                    .map(printRecordReadDO1 -> getOrderByPerDO(kdsPrintRecordReqDTO, printRecordReadDO1))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

}
