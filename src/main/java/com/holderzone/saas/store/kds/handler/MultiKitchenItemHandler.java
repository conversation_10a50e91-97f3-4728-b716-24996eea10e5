package com.holderzone.saas.store.kds.handler;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.kds.req.ItemChangesReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemPrepareReqDTO;
import com.holderzone.saas.store.dto.kds.req.KdsItemDTO;
import com.holderzone.saas.store.enums.kds.KdsItemStateEnum;
import com.holderzone.saas.store.kds.bo.ItemPrepareSplitBO;
import com.holderzone.saas.store.kds.bo.KitchenItemChangesBO;
import com.holderzone.saas.store.kds.entity.bo.ChangeKitchenItemBO;
import com.holderzone.saas.store.kds.entity.bo.OrderInfoBO;
import com.holderzone.saas.store.kds.entity.domain.*;
import com.holderzone.saas.store.kds.entity.enums.KdsKitchenStateEnum;
import com.holderzone.saas.store.kds.entity.enums.PointModeEnum;
import com.holderzone.saas.store.kds.mapstruct.BindItemMapstruct;
import com.holderzone.saas.store.kds.mapstruct.KitchenItemMapstruct;
import com.holderzone.saas.store.kds.service.*;
import com.holderzone.saas.store.kds.utils.DeepCloneUtils;
import com.holderzone.saas.store.kds.utils.ItemMd5Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Slf4j
@Component
@RequiredArgsConstructor
public class MultiKitchenItemHandler extends BaseAbstractKitchenItemHandler {

    private final BindItemService bindItemService;

    private final DeviceBindItemGroupService deviceBindItemGroupService;

    private final DistributeAreaService distributeAreaService;

    private final BindItemMapstruct bindItemMapstruct;

    private final KitchenItemMapstruct kitchenItemMapstruct;

    private final DeviceConfigService deviceConfigService;

    @Override
    public OrderInfoBO fullOrderInfoBO(ItemPrepareReqDTO itemPrepareReqDTO) {
        String storeGuid = UserContextUtils.getStoreGuid();
        List<SkuInfoRespDTO> skus = itemPrepareReqDTO.getSkus();
        OrderInfoBO orderInfoBO = new OrderInfoBO(itemPrepareReqDTO);
        List<String> skuPointReq = Stream.concat(skus.stream().map(SkuInfoRespDTO::getSkuGuid),
                skus.stream().map(SkuInfoRespDTO::getParentGuid)).distinct().collect(Collectors.toList());
        // 规格ID -> 菜
        Map<String, List<PrdPointItemDO>> skuPointsMap = buildSkuPointMap(skuPointReq, storeGuid);
        // 规格、区域 --> deviceID
        Map<Pair<String, String>, Map<String, List<String>>> skuArea2DstsMap = skuArea2Dst(storeGuid, skuPointReq,
                Collections.singletonList(orderInfoBO.getAreaGuid()));
        // 完善 orderInfoBO 信息
        orderInfoBO.setSkuPointsMap(skuPointsMap).setSkuArea2DstsMap(skuArea2DstsMap);
        return orderInfoBO;
    }

    @Override
    public ChangeKitchenItemBO fullChangeKitchenItemBO(ItemChangesReqDTO itemChangesReqDTO) {
        ChangeKitchenItemBO kitchenItemBO = new ChangeKitchenItemBO();
        kitchenItemBO.setOrderGuid(itemChangesReqDTO.getOrderGuid());
        kitchenItemBO.setOrderNo(itemChangesReqDTO.getOrderNo());
        kitchenItemBO.setDiningTableGuid(itemChangesReqDTO.getDiningTableGuid());
        kitchenItemBO.setDiningTableName(itemChangesReqDTO.getDiningTableName());
        kitchenItemBO.setAreaGuid(itemChangesReqDTO.getAreaGuid());
        kitchenItemBO.setStoreGuid(itemChangesReqDTO.getStoreGuid());
        kitchenItemBO.setTradeMode(itemChangesReqDTO.getTradeMode());
        // 通过规格id获取点位商品
        List<SkuInfoRespDTO> skus = itemChangesReqDTO.getSkus();
        List<String> skuPointReq = Stream.concat(skus.stream().map(SkuInfoRespDTO::getSkuGuid),
                skus.stream().map(SkuInfoRespDTO::getParentGuid)).distinct().collect(Collectors.toList());
        Map<String, List<PrdPointItemDO>> skuPointsMap = buildSkuPointMap(skuPointReq, itemChangesReqDTO.getStoreGuid());
        kitchenItemBO.setSkuPointsMap(skuPointsMap);
        // 规格、区域 --> deviceID
        Map<Pair<String, String>, Map<String, List<String>>> skuArea2DstsMap = skuArea2Dst(itemChangesReqDTO.getStoreGuid(), skuPointReq,
                Collections.singletonList(itemChangesReqDTO.getAreaGuid()));
        kitchenItemBO.setSkuArea2DstsMap(skuArea2DstsMap);
        return kitchenItemBO;
    }

    @Override
    public List<KitchenItemDO> convertItemAndSplitIfNecessary(ItemPrepareSplitBO itemPrepareSplitBO) {
        String storeGuid = itemPrepareSplitBO.getStoreGuid();
        KdsItemDTO kdsItemDTO = itemPrepareSplitBO.getKdsItemDTO();
        OrderInfoBO orderInfoBO = itemPrepareSplitBO.getOrderInfoBO();
        List<KitchenItemAttrDO> attrs = itemPrepareSplitBO.getAttrs();
        Map<String, DeviceConfigDO> deviceConfigInSqlMap = itemPrepareSplitBO.getDeviceConfigInSqlMap();
        LocalDateTime prepareTime = itemPrepareSplitBO.getPrepareTime();
        List<SkuInfoRespDTO> skus = itemPrepareSplitBO.getSkus();

        kdsItemDTO.setOrderRemark(orderInfoBO.getOrderRemark());
        KitchenItemDO kitchenItemDO = kitchenItemMapstruct.fromKdsItemReq(kdsItemDTO);

        List<SkuInfoRespDTO> respDTOS = skus.stream().filter(sku -> kdsItemDTO.getSkuGuid().equals(sku.getSkuGuid())
                || kdsItemDTO.getSkuGuid().equals(sku.getParentGuid())).collect(Collectors.toList());
        // 规格对应的制作点菜品
        List<PrdPointItemDO> prdPointItemList = getPrdPointItemDO(kdsItemDTO, orderInfoBO.getSkuPointsMap(), respDTOS);
        // 获取规格 id、区域 id 对应的设备 id
        Map<String, String> dstDeviceGroupMap = getDstDeviceGroupMap(orderInfoBO.getAreaGuid(), orderInfoBO.getSkuArea2DstsMap(), kitchenItemDO, respDTOS);
        if (CollectionUtils.isEmpty(prdPointItemList) && MapUtils.isEmpty(dstDeviceGroupMap)) {
            return Collections.emptyList();
        }
        // 判断设备是否关闭外卖、且当前菜品是外卖订单
        if (Boolean.TRUE.equals(orderInfoBO.getIsTakeOutOrder()) &&
                !getIsFilterTakeOutOrder(prdPointItemList, deviceConfigInSqlMap)) {
            return Collections.emptyList();
        }
        kitchenItemDO.setStoreGuid(storeGuid);
        // 制作口绑定设备
        buildPrdKitchenItemDevice(kitchenItemDO, prdPointItemList, deviceConfigInSqlMap);
        // 出堂口绑定设备
        buildDstKitchenItemDevice(kitchenItemDO, dstDeviceGroupMap);

        kitchenItemDO.setDisplayType(orderInfoBO.getDisplayType());
        kitchenItemDO.setOrderGuid(orderInfoBO.getOrderGuid());
        kitchenItemDO.setOrderDesc(orderInfoBO.getOrderDesc());
        kitchenItemDO.setOrderNumber(orderInfoBO.getOrderNumber());
        kitchenItemDO.setOrderSerialNo(orderInfoBO.getOrderSerialNo());
        kitchenItemDO.setOrderRemark(orderInfoBO.getOrderRemark());
        kitchenItemDO.setAreaGuid(orderInfoBO.getAreaGuid());
        kitchenItemDO.setTableGuid(orderInfoBO.getTableGuid());
        kitchenItemDO.setReturnCount(BigDecimal.ZERO);
        kitchenItemDO.setKitchenState(KdsKitchenStateEnum.TO_PRD.getCode());
        if (KdsItemStateEnum.HANG_UP.getCode() == kdsItemDTO.getItemState()) {
            kitchenItemDO.setHangUpTime(prepareTime);
        } else {
            kitchenItemDO.setPrepareTime(prepareTime);
        }
        kitchenItemDO.setOrderSortTime(prepareTime);
        kitchenItemDO.setAddItemBatch(0);
        kitchenItemDO.setItemAttrMd5(ItemMd5Utils.calItemMd5(orderInfoBO, kdsItemDTO, attrs));
        if (Boolean.TRUE.equals(kdsItemDTO.getIsWeight())) {
            return Collections.singletonList(kitchenItemDO);
        }
        int endExclusive = kdsItemDTO.getCurrentCount().intValue();
        kdsItemDTO.setCurrentCount(BigDecimal.ONE);
        return IntStream.range(0, endExclusive)
                .mapToObj(value -> {
                    KitchenItemDO kitchenItemCloned = (KitchenItemDO) DeepCloneUtils.cloneObject(kitchenItemDO);
                    kitchenItemCloned.setCurrentCount(BigDecimal.ONE);
                    return kitchenItemCloned;
                })
                .collect(Collectors.toList());
    }

    @Override
    public void handleChangeItemDevice(ChangeKitchenItemBO kitchenItemBO, KdsItemDTO changesKdsItem, KitchenItemDO changeKitchenItemDO) {
        List<SkuInfoRespDTO> respDTOS = kitchenItemBO.getSkus().stream()
                .filter(sku -> changesKdsItem.getSkuGuid().equals(sku.getSkuGuid())
                        || changesKdsItem.getSkuGuid().equals(sku.getParentGuid()))
                .collect(Collectors.toList());
        // 制作口
        // 规格对应的制作点菜品
        List<PrdPointItemDO> prdPointItemList = getPrdPointItemDO(changesKdsItem, kitchenItemBO.getSkuPointsMap(), respDTOS);
        changeKitchenItemDO.setPointGuid(null);
        changeKitchenItemDO.setPrdDeviceId(null);
        if (CollectionUtils.isNotEmpty(prdPointItemList)) {
            buildPrdKitchenItemDevice(changeKitchenItemDO, prdPointItemList, Maps.newHashMap());
        }

        // 出堂口
        // 获取规格 id、区域 id 对应的设备 id
        // 区域不会变，直接取库里的, 如果库里没有则取传入的
        String areaGuid = kitchenItemBO.getAreaGuid();
        if (Objects.nonNull(kitchenItemBO.getOriginalKitchenItemDO())) {
            areaGuid = kitchenItemBO.getOriginalKitchenItemDO().getAreaGuid();
        }
        Map<String, String> dstDeviceGroupMap = getDstDeviceGroupMap(areaGuid, kitchenItemBO.getSkuArea2DstsMap(), changeKitchenItemDO, respDTOS);
        changeKitchenItemDO.setDstDeviceId(null);
        if (MapUtils.isNotEmpty(dstDeviceGroupMap)) {
            buildDstKitchenItemDevice(changeKitchenItemDO, dstDeviceGroupMap);
        }
    }

    @Override
    public Map<String, List<String>> getChangeDeviceIds(ItemChangesReqDTO itemChangesReqDTO, KitchenItemChangesBO biz) {
        Map<String, List<String>> deviceItemMap = Maps.newHashMap();
        // 制作设备
        appendChangePrdDeviceItemMap(itemChangesReqDTO, biz, deviceItemMap);
        // 出堂设备
        appendChangeDstDeviceItemMap(itemChangesReqDTO, biz, deviceItemMap);
        return deviceItemMap;
    }

    private void appendChangePrdDeviceItemMap(ItemChangesReqDTO itemChangesReqDTO, KitchenItemChangesBO biz, Map<String, List<String>> deviceItemMap) {
        // 原菜
        List<KdsItemDTO> originalKdsItemList = itemChangesReqDTO.getOriginalKdsItemList();
        // 新菜
        List<KdsItemDTO> changesKdsItemList = itemChangesReqDTO.getChangesKdsItemList();
        Map<String, List<PrdPointItemDO>> skuPointsMap = biz.getChangeKitchenItemBO().getSkuPointsMap();
        for (KdsItemDTO kdsItemDTO : originalKdsItemList) {
            List<PrdPointItemDO> originalPointItemList = skuPointsMap.getOrDefault(kdsItemDTO.getSkuGuid(), Lists.newArrayList());
            for (PrdPointItemDO prdPointItemDO : originalPointItemList) {
                List<String> bindSkuGuidList = deviceItemMap.getOrDefault(prdPointItemDO.getDeviceId(), Lists.newArrayList());
                bindSkuGuidList.add(kdsItemDTO.getSkuGuid());
                deviceItemMap.put(prdPointItemDO.getDeviceId(), bindSkuGuidList);
            }
        }
        for (KdsItemDTO kdsItemDTO : changesKdsItemList) {
            List<PrdPointItemDO> changePointItemList = skuPointsMap.getOrDefault(kdsItemDTO.getSkuGuid(), Lists.newArrayList());
            for (PrdPointItemDO prdPointItemDO : changePointItemList) {
                List<String> bindSkuGuidList = deviceItemMap.getOrDefault(prdPointItemDO.getDeviceId(), Lists.newArrayList());
                bindSkuGuidList.add(kdsItemDTO.getSkuGuid());
                deviceItemMap.put(prdPointItemDO.getDeviceId(), bindSkuGuidList);
            }
        }
    }

    private void appendChangeDstDeviceItemMap(ItemChangesReqDTO itemChangesReqDTO, KitchenItemChangesBO biz, Map<String, List<String>> deviceItemMap) {
        List<SkuInfoRespDTO> skuList = itemChangesReqDTO.getSkus();
        // 出堂设备
        Map<Pair<String, String>, Map<String, List<String>>> skuArea2DstsMap = biz.getChangeKitchenItemBO().getSkuArea2DstsMap();
        for (SkuInfoRespDTO infoRespDTO : skuList) {
            Map<String, List<String>> deviceMap = skuArea2DstsMap.get(Pair.of(infoRespDTO.getSkuGuid(), itemChangesReqDTO.getAreaGuid()));
            if (MapUtils.isEmpty(deviceMap)) {
                Map<String, List<String>> parentItemDeviceIdMap = skuArea2DstsMap.getOrDefault(Pair.of(infoRespDTO.getParentGuid(),
                                itemChangesReqDTO.getAreaGuid()),
                        Maps.newHashMap());
                List<String> parentItemDeviceIdS = parentItemDeviceIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
                for (String parentItemDeviceId : parentItemDeviceIdS) {
                    List<String> bindSkuGuidList = deviceItemMap.getOrDefault(parentItemDeviceId, Lists.newArrayList());
                    bindSkuGuidList.add(infoRespDTO.getParentGuid());
                    deviceItemMap.put(parentItemDeviceId, bindSkuGuidList);
                }
            } else {
                List<String> deviceIds = deviceMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
                for (String deviceId : deviceIds) {
                    List<String> bindSkuGuidList = deviceItemMap.getOrDefault(deviceId, Lists.newArrayList());
                    bindSkuGuidList.add(infoRespDTO.getSkuGuid());
                    deviceItemMap.put(deviceId, bindSkuGuidList);
                }
            }
        }
    }

    private void buildPrdKitchenItemDevice(KitchenItemDO kitchenItemDO,
                                           List<PrdPointItemDO> prdPointItemList,
                                           Map<String, DeviceConfigDO> deviceConfigInSqlMap) {
        if (CollectionUtils.isEmpty(prdPointItemList)) {
            return;
        }
        DeviceConfigDO deviceConfigDO = deviceConfigInSqlMap.get(prdPointItemList.get(0).getDeviceId());
        if (null != deviceConfigDO) {
            kitchenItemDO.setIsPrintAutomatic(deviceConfigDO.getIsPrintAutomatic());
        }
        // 构建关联制作口设备
        List<KitchenItemDeviceDO> prdPointDevices = prdPointItemList.stream().map(e -> {
            KitchenItemDeviceDO kitchenItemDeviceDO = new KitchenItemDeviceDO();
            kitchenItemDeviceDO.setDeviceId(e.getDeviceId());
            kitchenItemDeviceDO.setPointGuid(e.getPointGuid());
            kitchenItemDeviceDO.setGroupGuid(e.getGroupGuid());
            return kitchenItemDeviceDO;
        }).collect(Collectors.toList());
        kitchenItemDO.setPrdPointDevices(prdPointDevices);
        kitchenItemDO.setPrdDeviceId(prdPointItemList.get(0).getDeviceId());
        kitchenItemDO.setPointGuid(prdPointItemList.get(0).getPointGuid());
    }


    private void buildDstKitchenItemDevice(KitchenItemDO kitchenItemDO,
                                           Map<String, String> dstDeviceGroupMap) {
        if (MapUtils.isEmpty(dstDeviceGroupMap)) {
            return;
        }
        // 出堂口绑定设备
        List<KitchenItemDeviceDO> dstPointDevices = Lists.newArrayList();
        for (Map.Entry<String, String> entry : dstDeviceGroupMap.entrySet()) {
            KitchenItemDeviceDO kitchenItemDeviceDO = new KitchenItemDeviceDO();
            kitchenItemDeviceDO.setDeviceId(entry.getKey());
            kitchenItemDeviceDO.setGroupGuid(entry.getValue());
            dstPointDevices.add(kitchenItemDeviceDO);
        }
        kitchenItemDO.setDstPointDevices(dstPointDevices);
        kitchenItemDO.setDstDeviceId(dstPointDevices.get(0).getDeviceId());
    }

    private boolean getIsFilterTakeOutOrder(List<PrdPointItemDO> prdPointItemList,
                                            Map<String, DeviceConfigDO> deviceConfigInSqlMap) {
        if (CollectionUtils.isEmpty(prdPointItemList)) {
            return false;
        }
        List<String> prdPointDeviceIds = prdPointItemList.stream()
                .map(PrdPointItemDO::getDeviceId)
                .distinct()
                .collect(Collectors.toList());
        boolean isFilterTakeOutOrder = false;
        for (String prdPointDeviceId : prdPointDeviceIds) {
            DeviceConfigDO deviceConfigDO = deviceConfigInSqlMap.get(prdPointDeviceId);
            if (Objects.nonNull(deviceConfigDO) && Boolean.TRUE.equals(deviceConfigDO.getIsFilterTakeOutOrder())) {
                isFilterTakeOutOrder = true;
            } else {
                prdPointItemList.removeIf(e -> prdPointDeviceId.equals(e.getDeviceId()));
            }
        }
        return isFilterTakeOutOrder;
    }

    private Map<String, List<PrdPointItemDO>> buildSkuPointMap(List<String> skuGuids, String storeGuid) {
        // 该门店绑定的分组列表
        List<DeviceBindItemGroupDO> deviceBindItemGroupList = deviceBindItemGroupService.listByStoreGuid(storeGuid);
        if (CollectionUtils.isEmpty(deviceBindItemGroupList)) {
            return Maps.newHashMap();
        }
        List<String> deviceBindList = deviceBindItemGroupList.stream()
                .map(DeviceBindItemGroupDO::getDeviceId)
                .distinct()
                .collect(Collectors.toList());
        // 制作口绑定的
        List<DeviceConfigDO> prdDeviceConfigList = deviceConfigService.filterPointModeByDeviceIds(PointModeEnum.PRODUCTION.getCode(), deviceBindList);
        List<String> prdDeviceConfigGuids = prdDeviceConfigList.stream()
                .map(DeviceConfigDO::getGuid)
                .collect(Collectors.toList());
        deviceBindItemGroupList = deviceBindItemGroupList.stream()
                .filter(e -> prdDeviceConfigGuids.contains(e.getDeviceId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deviceBindItemGroupList)) {
            return Maps.newHashMap();
        }
        Map<String, List<DeviceBindItemGroupDO>> deviceBindItemGroupMap = deviceBindItemGroupList.stream()
                .collect(Collectors.groupingBy(DeviceBindItemGroupDO::getGroupGuid));
        List<BindItemDO> bindItemList = bindItemService.listByGroupGuidsAndSkuGuids(new ArrayList<>(deviceBindItemGroupMap.keySet()), skuGuids);
        List<PrdPointItemDO> prdPointItemList = Lists.newArrayList();
        for (BindItemDO bindItemDO : bindItemList) {
            String groupGuid = bindItemDO.getGroupGuid();
            List<DeviceBindItemGroupDO> devices = deviceBindItemGroupMap.getOrDefault(groupGuid, Lists.newArrayList());
            for (DeviceBindItemGroupDO device : devices) {
                PrdPointItemDO prdPointItemDO = bindItemMapstruct.toPrdPointItemDO(bindItemDO);
                prdPointItemDO.setDeviceId(device.getDeviceId());
                prdPointItemDO.setPointGuid(device.getPointGuid());
                prdPointItemDO.setGroupGuid(groupGuid);
                prdPointItemList.add(prdPointItemDO);
            }
        }
        return prdPointItemList.stream().collect(Collectors.groupingBy(PrdPointItemDO::getSkuGuid));
    }

    private Map<Pair<String, String>, Map<String, List<String>>> skuArea2Dst(String storeGuid, List<String> skuGuidList,
                                                                             List<String> areaGuidList) {
        // 门店绑定设备
        List<DeviceBindItemGroupDO> deviceBindItemGroupList = deviceBindItemGroupService.listByStoreGuid(storeGuid);
        if (CollectionUtils.isEmpty(deviceBindItemGroupList)) {
            return Maps.newHashMap();
        }
        // 过滤出堂口设备
        List<String> allDeviceIds = deviceBindItemGroupList.stream()
                .map(DeviceBindItemGroupDO::getDeviceId)
                .distinct()
                .collect(Collectors.toList());
        List<DeviceConfigDO> dstDeviceConfigList = deviceConfigService.filterPointModeByDeviceIds(PointModeEnum.DISTRIBUTE.getCode(), allDeviceIds);
        List<String> dstDeviceGuidList = dstDeviceConfigList.stream().map(DeviceConfigDO::getGuid).collect(Collectors.toList());
        deviceBindItemGroupList.removeIf(e -> !dstDeviceGuidList.contains(e.getDeviceId()));
        if (CollectionUtils.isEmpty(deviceBindItemGroupList)) {
            return Maps.newHashMap();
        }
        // 绑定分组
        Map<String, List<String>> deviceBindItemGroupMap = deviceBindItemGroupList.stream()
                .collect(Collectors.groupingBy(DeviceBindItemGroupDO::getGroupGuid,
                        Collectors.mapping(DeviceBindItemGroupDO::getDeviceId, Collectors.toList())));
        // 查询门店商品
        List<BindItemDO> dstbindItemList = bindItemService.listByGroupGuidsAndSkuGuids(
                new ArrayList<>(deviceBindItemGroupMap.keySet()), skuGuidList);
        if (CollectionUtils.isEmpty(dstbindItemList)) {
            return Maps.newHashMap();
        }
        Map<String, String> bindItemMap = dstbindItemList.stream()
                .collect(Collectors.toMap(BindItemDO::getSkuGuid, BindItemDO::getGroupGuid, (key1, key2) -> key1));
        // 以设备 ID 对 areaGuid 进行分组
        Map<String, List<String>> dstAreaMap = distributeAreaService.queryDstAreaMap(storeGuid, areaGuidList);

        Map<String, Map<String, List<String>>> skuArea2DstMap = new HashMap<>();
        for (Map.Entry<String, String> entry : bindItemMap.entrySet()) {
            String skuGuid = entry.getKey();
            String groupGuid = entry.getValue();
            List<String> deviceIds = deviceBindItemGroupMap.get(groupGuid);
            if (CollectionUtils.isEmpty(deviceIds)) {
                continue;
            }
            for (String deviceId : deviceIds) {
                List<String> areaGuids = dstAreaMap.getOrDefault(deviceId, Lists.newArrayList());
                for (String areaGuid : areaGuids) {
                    String skuGuidAndAreaGuid = skuGuid + "," + areaGuid;
                    Map<String, List<String>> innerDeviceMap = skuArea2DstMap.getOrDefault(skuGuidAndAreaGuid, Maps.newHashMap());
                    List<String> innerDeviceIdList = innerDeviceMap.getOrDefault(groupGuid, Lists.newArrayList());
                    innerDeviceIdList.add(deviceId);
                    innerDeviceMap.put(groupGuid, innerDeviceIdList);
                    skuArea2DstMap.put(skuGuidAndAreaGuid, innerDeviceMap);
                }
            }
        }
        Map<Pair<String, String>, Map<String, List<String>>> skuArea2DstMergeMap = new HashMap<>();
        for (Map.Entry<String, Map<String, List<String>>> entry : skuArea2DstMap.entrySet()) {
            String skuGuidAndAreaGuid = entry.getKey();
            String[] split = skuGuidAndAreaGuid.split(",");
            skuArea2DstMergeMap.put(Pair.of(split[0], split[1]), entry.getValue());
        }
        return skuArea2DstMergeMap;
    }

    /**
     * 获取KDS绑定商品信息 菜谱模式下 和 普通模式下
     * * 绑定过相同SPU商品就无须绑定
     *
     * @param kdsItemDTO 商品
     * @param respDTOS   sku
     */
    private List<PrdPointItemDO> getPrdPointItemDO(KdsItemDTO kdsItemDTO,
                                                   Map<String, List<PrdPointItemDO>> skuPointsMap,
                                                   List<SkuInfoRespDTO> respDTOS) {
        List<PrdPointItemDO> prdPointItemList = skuPointsMap.get(kdsItemDTO.getSkuGuid());
        if (CollectionUtils.isNotEmpty(prdPointItemList)) {
            return prdPointItemList;
        }
        for (SkuInfoRespDTO respDTO : respDTOS) {
            List<PrdPointItemDO> prdPointItems = skuPointsMap.get(respDTO.getSkuGuid());
            if (CollectionUtils.isNotEmpty(prdPointItems)) {
                return prdPointItems;
            }
            List<PrdPointItemDO> prdPointParentItems = skuPointsMap.get(respDTO.getParentGuid());
            if (CollectionUtils.isNotEmpty(prdPointParentItems)) {
                return prdPointParentItems;
            }
        }
        return Lists.newArrayList();
    }

    private Map<String, String> getDstDeviceGroupMap(String areaGuid, Map<Pair<String, String>, Map<String, List<String>>> skuArea2DstsMap,
                                                     KitchenItemDO kitchenItemDO, List<SkuInfoRespDTO> respDTOS) {
        Map<String, List<String>> dstDeviceIdMap = skuArea2DstsMap
                .get(Pair.of(kitchenItemDO.getSkuGuid(), areaGuid));
        if (MapUtils.isNotEmpty(dstDeviceIdMap)) {
            return transferDeviceGroupMap(dstDeviceIdMap);
        }
        for (SkuInfoRespDTO respDTO : respDTOS) {
            Map<String, List<String>> innerDstDeviceIdMap = skuArea2DstsMap.get(Pair.of(respDTO.getSkuGuid(), areaGuid));
            if (MapUtils.isNotEmpty(innerDstDeviceIdMap)) {
                return transferDeviceGroupMap(innerDstDeviceIdMap);
            }
            Map<String, List<String>> innerParentDstDeviceIds = skuArea2DstsMap.get(Pair.of(respDTO.getParentGuid(), areaGuid));
            if (MapUtils.isNotEmpty(innerParentDstDeviceIds)) {
                return transferDeviceGroupMap(innerParentDstDeviceIds);
            }
        }
        return Maps.newHashMap();
    }


    private Map<String, String> transferDeviceGroupMap(Map<String, List<String>> dstDeviceIdMap) {
        Map<String, String> deviceGroupMap = Maps.newHashMap();
        for (Map.Entry<String, List<String>> entry : dstDeviceIdMap.entrySet()) {
            List<String> deviceIds = entry.getValue();
            for (String deviceId : deviceIds) {
                deviceGroupMap.put(deviceId, entry.getKey());
            }
        }
        return deviceGroupMap;
    }
}
