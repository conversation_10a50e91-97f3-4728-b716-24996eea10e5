package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface KitchenItemAttrService extends IService<KitchenItemAttrDO> {

    List<KitchenItemAttrDO> listByOrderItemGuids(Set<String> idList);

}
