package com.holderzone.saas.store.kds.service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DistributedIdService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface DistributedIdService {

    Long rawId(String tag);

    String nextId(String tag);

    List<String> nextBatchId(String tag, long count);

    String nextPrdPointGuid();

    List<String> nextBatchPointItemGuid(long count);

    String nextPrinterGuid();

    List<String> nextBatchDstAreaGuid(long count);

    List<String> nextBatchDstItemGuid(long count);

    String nextPrintRecordGuid();

    List<String> nextBatchPrintRecordGuid(long count);

    String nextKitchenItemGuid();

    List<String> nextBatchKitchenItemGuid(long count);

    List<String> nextBatchKitchenAttrGuid(long count);

    String nextQueueItemGuid();

    /**
     * TAG_DISPLAY_RULE
     * @return guid
     */
    String nextDisplayRuleGuid();

    /**
     * TAG_DISPLAY_ITEM
     * @param count 生成个数
     * @return guid
     */
    List<String> nextBatchDisplayItemGuid(long count);

    /**
     * TAG_DISPLAY_STORE
     * @param count 生成个数
     * @return guid
     */
    List<String> nextBatchDisplayStoreGuid(long count);
}
