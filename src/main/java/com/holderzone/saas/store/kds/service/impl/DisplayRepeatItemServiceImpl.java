package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.kds.entity.domain.DisplayRepeatItemDO;
import com.holderzone.saas.store.kds.mapper.DisplayRepeatItemMapper;
import com.holderzone.saas.store.kds.service.DisplayRepeatItemService;
import com.holderzone.saas.store.kds.service.DisplayRepeatItemStoreService;
import com.holderzone.saas.store.kds.service.rpc.StoreClientService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Objects;


@Slf4j
@Service
@RequiredArgsConstructor
public class DisplayRepeatItemServiceImpl extends ServiceImpl<DisplayRepeatItemMapper, DisplayRepeatItemDO>
        implements DisplayRepeatItemService {

    private final StoreClientService storeClientService;

    private final DisplayRepeatItemStoreService displayRepeatItemStoreService;

    @Override
    public void saveOrUpdateConfig(DisplayRepeatItemDO displayRepeatItemDO) {
        baseMapper.saveOrUpdateConfig(displayRepeatItemDO);
    }

    @Override
    public Boolean queryAllowRepeatFlag(String storeGuid) {
        StoreDTO storeDTO = storeClientService.queryStoreByGuid(storeGuid);
        if (ObjectUtils.isEmpty(storeDTO)) {
            log.info("[未查询到门店]返回默认值");
            return false;
        }
        if (!StringUtils.hasText(storeDTO.getBelongBrandGuid())) {
            log.info("[门店未绑定品牌]返回默认值");
            return false;
        }
        DisplayRepeatItemDO repeatItemDO = getByBrandGuid(storeDTO.getBelongBrandGuid());
        if (Objects.isNull(repeatItemDO)) {
            log.info("[品牌下未配置可重复绑定菜品]返回默认值");
            return false;
        }
        log.info("KDS菜品绑定配置:{}", JacksonUtils.writeValueAsString(repeatItemDO));
        if (Boolean.FALSE.equals(repeatItemDO.getAllowRepeatFlag())) {
            return false;
        }
        if (Boolean.TRUE.equals(repeatItemDO.getAllStoreFlag())) {
            return true;
        }
        // 适用部分门店
        List<String> storeGuids = displayRepeatItemStoreService.storeGuidListByBrandGuid(storeDTO.getBelongBrandGuid());
        log.info("KDS菜品绑定配置适用门店:{}", JacksonUtils.writeValueAsString(storeGuids));
        return storeGuids.contains(storeGuid);
    }

    @Override
    public DisplayRepeatItemDO getByBrandGuid(String brandGuid) {
        return getOne(new QueryWrapper<DisplayRepeatItemDO>()
                .lambda()
                .eq(DisplayRepeatItemDO::getBrandGuid, brandGuid));
    }
}