package com.holderzone.saas.store.kds.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ExecutorConfig {

    @Bean
    public ExecutorService executorService() {
        return new ThreadPoolExecutor(5, 50,
                5L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(500),
                new ThreadFactoryBuilder().setNameFormat("push-msg-%d").build());
    }
}
