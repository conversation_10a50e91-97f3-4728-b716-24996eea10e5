package com.holderzone.saas.store.kds.constant;

public class PointModeConstants {
    //全部
    public static final int ALL = -1;
    //制作点
    public static final int PRODUCTION = 0;
    //出堂口
    public static final int DISTRIBUTE = 1;

    /**
     * 制作点: 堂口模式
     */
    public static final int PRD_POINT = 0;

    /**
     * 制作点: 菜品汇总模式
     */
    public static final int PRD_SUMMARY_ITEM = 1;

    /**
     * 制作点: 订单模式1*4
     */
    public static final int PRD_ORDER = 2;

    /**
     * 制作点: 订单模式1*6
     */
    public static final int PRD_SUMMARY_ORDER = 3;

    /**
     * 制作点: 混合模式
     */
    public static final int PRD_BLEND_MODE = 4;

    /**
     * 出堂口: 单菜品模式
     */
    public static final int DST_SINGLE_ITEM = 0;

    /**
     * 出堂口: 菜品汇总模式
     */
    public static final int DST_SUMMARY_ITEM = 1;

    /**
     * 出堂口: 订单模式1*4
     */
    public static final int DST_ORDER = 2;

    /**
     * 出堂口: 订单模式1*6
     */
    public static final int DST_SUMMARY_ORDER = 3;

    /**
     * 出堂口: 混合模式
     */
    public static final int DST_BLEND_MODE = 4;

    /**
     * 混合-订单模式1*5
     */
    public static final int BLEND_MODE_ORDER = 5;

    /**
     * 混合-汇总模式1*10
     */
    public static final int BLEND_MODE_SUMMARY = 6;

    /**
     * 菜品分组汇总模式
     */
    public static final int GROUP_MODE_SUMMARY = 7;

}
