package com.holderzone.saas.store.kds.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleQueryDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayStoreRespDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.kds.entity.domain.DisplayRuleDO;
import com.holderzone.saas.store.kds.entity.domain.DisplayStoreDO;
import com.holderzone.saas.store.kds.mapper.DisplayRuleMapper;
import com.holderzone.saas.store.kds.mapper.DisplayStoreMapper;
import com.holderzone.saas.store.kds.mapstruct.DisplayRuleMapstruct;
import com.holderzone.saas.store.kds.service.DisplayRuleService;
import com.holderzone.saas.store.kds.service.DisplayStoreService;
import com.holderzone.saas.store.kds.service.rpc.StoreClientService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description
 * @date 2021/1/29 16:42
 */
@AllArgsConstructor
@Slf4j
@Service
public class DisplayStoreServiceImpl extends ServiceImpl<DisplayStoreMapper, DisplayStoreDO> implements DisplayStoreService {

    private final DisplayRuleMapstruct ruleMapstruct;

    private final StoreClientService storeClientService;

    private final DisplayRuleMapper displayRuleMapper;

    @Override
    public List<DisplayStoreRespDTO> listStore(DisplayRuleQueryDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO.getRuleType())) {
            log.warn("传入规则类型为空");
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DisplayStoreDO> queryWrapper = new LambdaQueryWrapper<DisplayStoreDO>()
                .eq(DisplayStoreDO::getRuleType, reqDTO.getRuleType());
        if (!ObjectUtils.isEmpty(reqDTO.getRuleGuid())) {
            log.info("编辑查询kds使用门店，规则guid={}", reqDTO.getRuleGuid());
            queryWrapper.ne(DisplayStoreDO::getRuleGuid, reqDTO.getRuleGuid());
        }
        List<DisplayStoreDO> storeDOList = this.list(queryWrapper);
        List<DisplayStoreRespDTO> displayStoreRespDTOS = ruleMapstruct.toStoreRespList(storeDOList);
        if (!CollectionUtils.isEmpty(displayStoreRespDTOS)) {
            List<String> storeList = displayStoreRespDTOS.stream()
                    .map(DisplayStoreRespDTO::getStoreGuid)
                    .collect(Collectors.toList());
            queryStoreInfo(storeList, displayStoreRespDTOS);
        }
        if (!ObjectUtils.isEmpty(reqDTO.getBrandGuid())) {
            List<DisplayRuleDO> ruleDOList = displayRuleMapper.selectList(new LambdaQueryWrapper<DisplayRuleDO>()
                    .eq(DisplayRuleDO::getBrandGuid, reqDTO.getBrandGuid())
                    .eq(DisplayRuleDO::getRuleType, reqDTO.getRuleType())
            );
            List<String> ruleGuidList = ruleDOList.stream().map(DisplayRuleDO::getGuid).collect(Collectors.toList());
            displayStoreRespDTOS.removeIf(s -> !ruleGuidList.contains(s.getRuleGuid()));
        }
        log.info("listStore 最终返回参数：{}", JacksonUtils.writeValueAsString(displayStoreRespDTOS));
        return displayStoreRespDTOS;
    }

    @Override
    public List<DisplayStoreRespDTO> queryStoreInfo(List<String> storeList, List<DisplayStoreRespDTO> displayStoreRespList) {
        List<StoreDTO> storeDOList = storeClientService.queryStoreByIdList(storeList);
        if (!CollectionUtils.isEmpty(storeDOList)) {
            Map<String, StoreDTO> storeMap = storeDOList.stream().collect(Collectors.toMap(StoreDTO::getGuid, e -> e));
            displayStoreRespList.forEach(e -> {
                StoreDTO storeDTO = storeMap.get(e.getStoreGuid());
                if (ObjectUtil.isNotNull(storeDTO)) {
                    e.setStoreName(storeDTO.getName());
                }
            });
        }
        return displayStoreRespList;
    }
}