package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.dto.kds.req.QueueConfigDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.kds.entity.domain.QueueConfigDO;
import com.holderzone.saas.store.kds.mapper.QueueConfigMapper;
import com.holderzone.saas.store.kds.mapstruct.QueueMapstruct;
import com.holderzone.saas.store.kds.service.QueueConfigService;
import com.holderzone.saas.store.kds.utils.ThrowableExtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Slf4j
@Service
public class QueueConfigServiceImpl extends ServiceImpl<QueueConfigMapper, QueueConfigDO> implements QueueConfigService {

    private final QueueMapstruct queueMapstruct;

    public QueueConfigServiceImpl(QueueMapstruct queueMapstruct) {
        this.queueMapstruct = queueMapstruct;
    }

    @Override
    public void create(StoreDeviceDTO storeDeviceDTO) {
        Assert.notNull(storeDeviceDTO.getDeviceGuid(), "设备Guid不得为空");
        Assert.notNull(storeDeviceDTO.getStoreGuid(), "门店Guid不得为空");
        QueueConfigDO queueConfigDO = new QueueConfigDO();
        queueConfigDO.setGuid(storeDeviceDTO.getDeviceGuid());
        queueConfigDO.setStoreGuid(storeDeviceDTO.getStoreGuid());
        if (count(new LambdaQueryWrapper<QueueConfigDO>()
                .eq(QueueConfigDO::getGuid, storeDeviceDTO.getDeviceGuid())
                .eq(QueueConfigDO::getStoreGuid, storeDeviceDTO.getStoreGuid())) == 0) {
            try {
                save(queueConfigDO);
            } catch (Exception e) {
                log.error("创建取餐屏发生异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
            }
        }
    }

    @Override
    public void update(QueueConfigDTO queueConfigDTO) {
        QueueConfigDO queueConfigDO = queueMapstruct.fromDTO(queueConfigDTO);
        update(queueConfigDO, new LambdaQueryWrapper<QueueConfigDO>()
                .eq(QueueConfigDO::getGuid, queueConfigDTO.getGuid())
                .eq(QueueConfigDO::getStoreGuid, queueConfigDTO.getStoreGuid())
        );
    }

    @Override
    public QueueConfigDTO query(QueueConfigDTO queueConfigDTO) {
        if (count(new LambdaQueryWrapper<QueueConfigDO>()
                .eq(QueueConfigDO::getGuid, queueConfigDTO.getGuid())
                .eq(QueueConfigDO::getStoreGuid, queueConfigDTO.getStoreGuid())) == 0) {
            StoreDeviceDTO storeDeviceDTO = new StoreDeviceDTO();
            storeDeviceDTO.setStoreGuid(queueConfigDTO.getStoreGuid());
            storeDeviceDTO.setDeviceGuid(queueConfigDTO.getGuid());
            create(storeDeviceDTO);
        }
        QueueConfigDO queueConfigInSql = getOne(new LambdaQueryWrapper<QueueConfigDO>()
                .eq(QueueConfigDO::getGuid, queueConfigDTO.getGuid())
                .eq(QueueConfigDO::getStoreGuid, queueConfigDTO.getStoreGuid()));
        return queueMapstruct.doToDTO(queueConfigInSql);
    }

    @Override
    public void delete(StoreDeviceDTO storeDeviceDTO) {
        remove(new LambdaQueryWrapper<QueueConfigDO>()
                .eq(QueueConfigDO::getGuid, storeDeviceDTO.getDeviceGuid())
                .eq(QueueConfigDO::getStoreGuid, storeDeviceDTO.getStoreGuid()));
    }
}
