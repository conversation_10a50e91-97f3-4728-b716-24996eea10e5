package com.holderzone.saas.store.kds.builder;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.item.resp.MappingRespDTO;
import com.holderzone.saas.store.dto.kds.req.ItemGroupReqDTO;
import com.holderzone.saas.store.dto.kds.resp.ItemGroupRespDTO;
import com.holderzone.saas.store.kds.entity.domain.BindItemDO;
import com.holderzone.saas.store.kds.entity.domain.BindItemGroupDO;
import com.holderzone.saas.store.kds.entity.domain.DeviceBindItemGroupDO;
import com.holderzone.saas.store.kds.manager.bo.ItemGroupBO;
import com.holderzone.saas.store.kds.utils.SnowFlakeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class ItemGroupBuilder {

    private ItemGroupBuilder() {

    }

    public static ItemGroupBO build() {
        ItemGroupBO biz = new ItemGroupBO();
        biz.setBindItemGroup(new BindItemGroupDO());
        biz.setBindItemList(Lists.newArrayList());
        return biz;
    }

    public static ItemGroupBO build(ItemGroupReqDTO reqDTO) {
        ItemGroupBO biz = build();
        BindItemGroupDO bindItemGroup = biz.getBindItemGroup();
        bindItemGroup.setGuid(reqDTO.getGroupGuid());
        if (StringUtils.isEmpty(bindItemGroup.getGuid())) {
            bindItemGroup.setGuid(String.valueOf(SnowFlakeUtil.getInstance().nextId()));
        }
        bindItemGroup.setName(reqDTO.getName());
        String storeGuid = UserContextUtils.getStoreGuid();
        bindItemGroup.setStoreGuid(storeGuid);

        List<BindItemDO> bindItemList = biz.getBindItemList();
        List<ItemGroupReqDTO.InnerSku> skus = reqDTO.getSkus();
        if (CollectionUtils.isNotEmpty(skus)) {
            for (ItemGroupReqDTO.InnerSku innerSku : skus) {
                BindItemDO bindItemDO = new BindItemDO();
                bindItemDO.setGuid(String.valueOf(SnowFlakeUtil.getInstance().nextId()));
                bindItemDO.setStoreGuid(storeGuid);
                bindItemDO.setGroupGuid(bindItemGroup.getGuid());
                bindItemDO.setItemGuid(innerSku.getItemGuid());
                bindItemDO.setSkuGuid(innerSku.getSkuGuid());
                bindItemList.add(bindItemDO);
            }
        }
        return biz;
    }


    public static List<ItemGroupRespDTO> buildItemGroupRespDTO(List<MappingRespDTO> itemInfoRespList,
                                                               List<BindItemGroupDO> bindItemGroupList,
                                                               List<BindItemDO> allBindItemList,
                                                               List<BindItemDO> bindItemList,
                                                               List<DeviceBindItemGroupDO> deviceBindItemGroupList) {
        List<String> groupGuids = deviceBindItemGroupList.stream()
                .map(DeviceBindItemGroupDO::getGroupGuid)
                .distinct()
                .collect(Collectors.toList());
        List<ItemGroupRespDTO> respList = bindItemGroupList.stream().map(e -> {
                    ItemGroupRespDTO respDTO = new ItemGroupRespDTO();
                    respDTO.setGuid(e.getGuid());
                    respDTO.setName(e.getName());
                    respDTO.setCurrentBindFlag(groupGuids.contains(e.getGuid()));
                    return respDTO;
                }
        ).collect(Collectors.toList());
        Map<String, List<BindItemDO>> countGroupingByGroupGuidMap = bindItemList.stream()
                .collect(Collectors.groupingBy(BindItemDO::getGroupGuid));
        for (ItemGroupRespDTO respDTO : respList) {
            List<BindItemDO> innerBindItemList = countGroupingByGroupGuidMap.get(respDTO.getGuid());
            if (CollectionUtils.isEmpty(innerBindItemList)) {
                respDTO.setItemCount(0L);
                continue;
            }
            List<String> innerBindItemGuidList = innerBindItemList.stream()
                    .map(BindItemDO::getItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            respDTO.setItemCount((long) innerBindItemGuidList.size());
        }
        List<String> allBindItemGuidList = allBindItemList.stream()
                .map(BindItemDO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        List<String> allItemCount = itemInfoRespList.stream()
                .map(MappingRespDTO::geteDishCode)
                .distinct()
                .collect(Collectors.toList());
        // 未绑定商品
        ItemGroupRespDTO unbindItemGroup = new ItemGroupRespDTO();
        unbindItemGroup.setGuid(Strings.EMPTY);
        unbindItemGroup.setName("未绑定商品");
        unbindItemGroup.setItemCount((long) allItemCount.size() - allBindItemGuidList.size());
        unbindItemGroup.setCurrentBindFlag(false);
        respList.add(0, unbindItemGroup);
        return respList;
    }

    public static List<ItemGroupRespDTO> buildItemGroupRespDTO(List<BindItemGroupDO> bindItemGroupList,
                                                               List<BindItemDO> bindItemList,
                                                               List<DeviceBindItemGroupDO> deviceBindItemGroupList) {
        List<String> groupGuids = deviceBindItemGroupList.stream()
                .map(DeviceBindItemGroupDO::getGroupGuid)
                .distinct()
                .collect(Collectors.toList());
        List<ItemGroupRespDTO> respList = bindItemGroupList.stream().map(e -> {
                    ItemGroupRespDTO respDTO = new ItemGroupRespDTO();
                    respDTO.setGuid(e.getGuid());
                    respDTO.setName(e.getName());
                    respDTO.setCurrentBindFlag(groupGuids.contains(e.getGuid()));
                    return respDTO;
                }
        ).collect(Collectors.toList());
        Map<String, List<BindItemDO>> countGroupingByGroupGuidMap = bindItemList.stream()
                .collect(Collectors.groupingBy(BindItemDO::getGroupGuid));
        for (ItemGroupRespDTO respDTO : respList) {
            List<BindItemDO> innerBindItemList = countGroupingByGroupGuidMap.get(respDTO.getGuid());
            if (CollectionUtils.isEmpty(innerBindItemList)) {
                respDTO.setItemCount(0L);
                continue;
            }
            List<String> innerBindItemGuidList = innerBindItemList.stream()
                    .map(BindItemDO::getItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            respDTO.setItemCount((long) innerBindItemGuidList.size());
        }
        return respList;
    }

}
