package com.holderzone.saas.store.kds.entity.bo;

import com.google.common.collect.Sets;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.kds.req.KdsItemDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayRuleItemRespDTO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemAttrDO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemDO;
import com.holderzone.saas.store.kds.entity.domain.PrdPointItemDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.MapUtils;
import org.springframework.data.util.Pair;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/9
 * @description 换菜BO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "换菜BO", description = "换菜BO")
public class ChangeKitchenItemBO implements Serializable {

    private static final long serialVersionUID = -8673812697932864696L;

    private String areaGuid;

    private String storeGuid;

    @ApiModelProperty(value = "交易模式：0=正餐，1=快餐，2=外卖")
    private Integer tradeMode;

    @NotBlank(message = "订单Guid不得为空")
    @ApiModelProperty(value = "订单Guid")
    private String orderGuid;

    @NotBlank(message = "订单号不得为空")
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "桌台guid")
    private String diningTableGuid;

    @ApiModelProperty(value = "桌台号")
    private String diningTableName;

    private KdsItemDTO originalKdsItem;

    /**
     * 原菜品集合名称
     */
    private String originalKdsItemName;

    private KdsItemDTO changesKdsItem;

    private KitchenItemDO originalKitchenItemDO;

    private List<KitchenItemAttrDO> attrList;

    private Map<String, PrdPointItemDO> skuPointMap;

    private Map<String, List<PrdPointItemDO>> skuPointsMap;

    private Map<Pair<String, String>, String> skuArea2DstMap;

    private Map<Pair<String, String>, Map<String, List<String>>> skuArea2DstsMap;

    private Map<String, DisplayRuleItemRespDTO> kitchenItemDisplayRuleMap;

    /**
     * 商品信息
     */
    private List<SkuInfoRespDTO> skus;


    public List<String> getDeviceIds() {
        Set<String> allDeviceIds = Sets.newHashSet();
        if (MapUtils.isNotEmpty(this.skuPointMap)) {
            allDeviceIds.addAll(this.skuPointMap.values().stream()
                    .map(PrdPointItemDO::getDeviceId)
                    .collect(Collectors.toSet()));
        }
        if (MapUtils.isNotEmpty(this.skuPointsMap)) {
            allDeviceIds.addAll(this.skuPointsMap.values().stream()
                    .flatMap(Collection::stream)
                    .map(PrdPointItemDO::getDeviceId)
                    .collect(Collectors.toSet()));
        }
        if (MapUtils.isNotEmpty(this.skuArea2DstsMap)) {
            for (Map<String, List<String>> deviceIdMap : skuArea2DstsMap.values()) {
                allDeviceIds.addAll(deviceIdMap.values().stream()
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList()));
            }
        }
        if (MapUtils.isNotEmpty(this.skuArea2DstMap)) {
            allDeviceIds.addAll(new HashSet<>(this.skuArea2DstMap.values()));
        }
        return allDeviceIds.stream().distinct().collect(Collectors.toList());
    }

}
