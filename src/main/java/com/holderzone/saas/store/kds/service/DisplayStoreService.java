package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleQueryDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayStoreRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DisplayStoreDO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 显示门店
 * @date 2021/1/29 16:41
 */
public interface DisplayStoreService extends IService<DisplayStoreDO> {

    /**
     * 查询kds门店列表
     *
     * @param reqDTO DisplayRuleQueryDTO
     * @return List<DisplayStoreRespDTO>
     */
    List<DisplayStoreRespDTO> listStore(DisplayRuleQueryDTO reqDTO);

    /***
     * 查询门店信息
     * @param storeGuidList 门店guid
     * @param displayStoreRespList 门店信息
     * @return
     */
    List<DisplayStoreRespDTO> queryStoreInfo(List<String> storeGuidList, List<DisplayStoreRespDTO> displayStoreRespList);
}

