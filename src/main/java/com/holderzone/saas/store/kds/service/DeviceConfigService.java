package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.DeviceStatusRespDTO;
import com.holderzone.saas.store.kds.entity.domain.DeviceConfigDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DeviceConfigService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface DeviceConfigService extends IService<DeviceConfigDO> {

    DeviceStatusRespDTO create(DeviceCreateReqDTO deviceCreateReqDTO);

    DeviceStatusRespDTO query(DeviceQueryReqDTO deviceQueryReqDTO);

    void initialize(DeviceQueryReqDTO deviceQueryReqDTO);

    void assertThatDeviceExists(String storeGuid, String deviceId);

    DeviceConfigDO queryDeviceByGuid(String storeGuid, String deviceId);

    DeviceConfigDO queryPrdDeviceByGuid(String storeGuid, String deviceId);

    Map<String, DeviceConfigDO> listPrdDeviceByGuid(String storeGuid, List<String> deviceIdList);

    DeviceConfigDO queryDstDispatchAsPrintByGuid(String storeGuid, String deviceId);

    DeviceConfigDO queryDstDisplayTypeByGuid(String storeGuid, String deviceId);

    List<DeviceConfigDO> listDeviceOfStore(String storeGuid);

    Map<String, String> getDeviceNameMapOfStore(String storeGuid);

    List<DeviceConfigDO> filterPointModeByDeviceIds(Integer pointMode, List<String> deviceIdList);

    void updateBasic(String storeGuid, String deviceId, Integer displayMode, Integer itemDisplayType);

    void updatePrdAdvanced(String storeGuid, String deviceId, DevicePrdConfDTO devicePrdConfDTO);

    void updateDstAdvanced(String storeGuid, String deviceId, DeviceDstConfDTO deviceDstConfDTO);

    String queryBoundPrinterGuidByDeviceId(String storeGuid, String deviceId);

    void bindPrinter(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO);

    void rebindPrinter(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO);

    void unbindPrinter(KdsPrinterBindUnbindReqDTO kdsPrinterBindUnbindReqDTO);

    void unbindPrinter(String printerGuid);
}
