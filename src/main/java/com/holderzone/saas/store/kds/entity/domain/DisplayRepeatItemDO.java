package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 菜品重复绑定配置
 */
@Data
@TableName("hsk_display_repeat_item")
public class DisplayRepeatItemDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 全局唯一主键
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 品牌guid
     */
    private String brandGuid;

    /**
     * 是否允许重复
     */
    private Boolean allowRepeatFlag;

    /**
     * 是否全部门店
     */
    private Boolean allStoreFlag;
}
