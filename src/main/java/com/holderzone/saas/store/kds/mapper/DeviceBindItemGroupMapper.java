package com.holderzone.saas.store.kds.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.saas.store.kds.entity.domain.DeviceBindItemGroupDO;
import com.holderzone.saas.store.kds.entity.query.ItemQuery;
import com.holderzone.saas.store.kds.entity.read.GroupBindItemReadDO;
import com.holderzone.saas.store.kds.utils.PageAdapter;
import org.apache.ibatis.annotations.Param;


/**
 * 设备绑定菜品分组 Mapper 接口
 */
public interface DeviceBindItemGroupMapper extends BaseMapper<DeviceBindItemGroupDO> {

    IPage<GroupBindItemReadDO> pageBindGroup(PageAdapter<GroupBindItemReadDO> page, @Param("request") ItemQuery itemQuery);
}
