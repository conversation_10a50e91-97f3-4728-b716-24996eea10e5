package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemDeviceDO;

import java.util.List;


public interface KitchenItemDeviceService extends IService<KitchenItemDeviceDO> {

    List<KitchenItemDeviceDO> listDeviceIdByKitchenItemGuids(List<String> kitchenItemGuids);

    List<KitchenItemDeviceDO> listByDeviceIds(List<String> deviceIds);

    List<KitchenItemDeviceDO> limitListByDeviceId(String deviceId);

    void updateBatchKitchenItemGuid(List<KitchenItemDeviceDO> kitchenItemDeviceDOList);

    void removeByKitchenItemGuid(String kitchenItemGuid);

    void removeBatchByKitchenItemGuids(List<String> kitchenItemGuids);
}
