package com.holderzone.saas.store.kds.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 绑定分组菜品
 */
@Data
@TableName("hsk_bind_item")
public class BindItemDO implements Serializable {

    private static final long serialVersionUID = 4372802362104352727L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 唯一GUID
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 分组guid
     */
    private String groupGuid;

    /**
     * item guid
     */
    private String itemGuid;

    /**
     * sku guid
     */
    private String skuGuid;

}
