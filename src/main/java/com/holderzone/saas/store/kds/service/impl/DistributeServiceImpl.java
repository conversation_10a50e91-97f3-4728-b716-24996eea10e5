package com.holderzone.saas.store.kds.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.resp.MappingRespDTO;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.*;
import com.holderzone.saas.store.dto.table.AreaDTO;
import com.holderzone.saas.store.kds.entity.domain.DeviceBindItemGroupDO;
import com.holderzone.saas.store.kds.entity.domain.DistributeAreaDO;
import com.holderzone.saas.store.kds.entity.domain.DistributeItemDO;
import com.holderzone.saas.store.kds.entity.domain.ItemConfigDO;
import com.holderzone.saas.store.kds.service.*;
import com.holderzone.saas.store.kds.service.rpc.ItemRpcService;
import com.holderzone.saas.store.kds.service.rpc.TableRpcService;
import com.holderzone.saas.store.kds.utils.SnowFlakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@SuppressWarnings("Duplicates")
public class DistributeServiceImpl implements DistributeService {

    private final DistributeAreaService distributeAreaService;

    private final DistributeItemService distributeItemService;

    private final DeviceConfigService deviceConfigService;

    private final ItemConfigService itemConfigService;

    private final TableRpcService tableRpcService;

    private final ItemRpcService itemRpcService;

    private final DeviceBindItemGroupService deviceBindItemGroupService;

    @Autowired
    public DistributeServiceImpl(DistributeAreaService distributeAreaService,
                                 DistributeItemService distributeItemService,
                                 DeviceConfigService deviceConfigService,
                                 ItemConfigService itemConfigService,
                                 TableRpcService tableRpcService,
                                 ItemRpcService itemRpcService,
                                 DeviceBindItemGroupService deviceBindItemGroupService) {
        this.distributeAreaService = distributeAreaService;
        this.distributeItemService = distributeItemService;
        this.deviceConfigService = deviceConfigService;
        this.itemConfigService = itemConfigService;
        this.tableRpcService = tableRpcService;
        this.itemRpcService = itemRpcService;
        this.deviceBindItemGroupService = deviceBindItemGroupService;
    }


    @Override
    public DstBindStatusRespDTO queryBindingPreview(DeviceQueryReqDTO deviceQueryReqDTO) {
        DstBindStatusRespDTO areaBindStatus = distributeAreaService.queryAreaBindingPreview(deviceQueryReqDTO);
        DstBindStatusRespDTO itemBindStatus = distributeItemService.queryItemBindingPreview(deviceQueryReqDTO);
        DstBindStatusRespDTO bindingPreview = new DstBindStatusRespDTO();
        bindingPreview.setBoundAreaCount(areaBindStatus.getBoundAreaCount());
        bindingPreview.setIsSnackBound(areaBindStatus.getIsSnackBound());
        bindingPreview.setIsTakeoutBound(areaBindStatus.getIsTakeoutBound());
        bindingPreview.setBoundItemCount(itemBindStatus.getBoundItemCount());
        return bindingPreview;
    }

    @Override
    public DstBindDetailsRespDTO queryBindingDetails(DstItemQueryReqDTO dstItemQueryReqDTO) {
        DeviceQueryReqDTO deviceQueryReqDTO = new DeviceQueryReqDTO(dstItemQueryReqDTO.getStoreGuid(), dstItemQueryReqDTO.getDeviceId());
        DstBindStatusRespDTO dstBindStatusRespDTO = this.queryBindingPreview(deviceQueryReqDTO);
        List<DstTypeBindRespDTO> dstTypeBindRespDTOS = this.queryBoundItemOfDevice(dstItemQueryReqDTO);
        int itemCount = 0;
        for (DstTypeBindRespDTO typeBindRespDTO : dstTypeBindRespDTOS) {
            if (CollectionUtils.isEmpty(typeBindRespDTO.getItemList())) {
                continue;
            }
            itemCount += typeBindRespDTO.getItemList().size();
        }
        dstBindStatusRespDTO.setBoundItemCount(itemCount);
        return new DstBindDetailsRespDTO().setDstBindStatus(dstBindStatusRespDTO).setDstTypeBindList(dstTypeBindRespDTOS);
    }

    /**
     * 判断该字符串是不是数字
     *
     * @param str
     * @return
     */
    public static boolean isNumber(String str) {
        Pattern pattern = Pattern.compile("[0-9]*");
        return pattern.matcher(str).matches();
    }

    @Override
    public DstAreaRespDTO queryBoundArea(DstBoundAreaReqDTO dstBoundAreaReqDTO) {

        // 当前设备信息
        String currentDeviceId = dstBoundAreaReqDTO.getDeviceId();

        // 所有区域元数据
        List<AreaDTO> areaList = tableRpcService.query(dstBoundAreaReqDTO.getStoreGuid());
        List<String> areaGuids = areaList.stream().map(AreaDTO::getGuid).collect(Collectors.toList());
        // 出堂口已绑定菜品
        List<DistributeItemDO> distributeItemInSql =
                distributeItemService.queryBoundItemOfStore(dstBoundAreaReqDTO.getStoreGuid());

        // 出堂口已绑定区域
        List<DistributeAreaDO> distributeAreaInSql =
                distributeAreaService.queryBoundAreaOfStore(dstBoundAreaReqDTO.getStoreGuid());

        //Bugfix:18959 若在商户后台删除了区域，那么在kds查询的时候进行校验，已经被删除的区域，在hsk_distribute_area中把相关的区域删除掉
        List<String> areaToDelete = new ArrayList<>();
        log.info("distributeAreaInSql={},areaGuids={}", JacksonUtils.writeValueAsString(distributeAreaInSql), JacksonUtils.writeValueAsString(areaGuids));
        if (!ObjectUtils.isEmpty(distributeAreaInSql)) {
            for (DistributeAreaDO distributeAreaDO : distributeAreaInSql) {
                String areaGuid = distributeAreaDO.getAreaGuid();
                if (isNumber(areaGuid)) {
                    if (!areaGuids.contains(areaGuid)) {
                        areaToDelete.add(distributeAreaDO.getAreaGuid());
                    }
                }
            }
        }

        if (!ObjectUtils.isEmpty(areaToDelete)) {
            distributeAreaService.simpleRemoveBatchArea(dstBoundAreaReqDTO.getStoreGuid(), currentDeviceId, areaToDelete);
        }
        // 已绑定关系辅助List（取出当前设备绑定的菜品）
        List<String> boundSkuGuidList = distributeItemInSql.stream()
                .filter(distributeItemDO -> currentDeviceId.equalsIgnoreCase(distributeItemDO.getDeviceId()))
                .map(DistributeItemDO::getSkuGuid).collect(Collectors.toList());

        // 已绑定关系辅助MAP
        int probableSize = distributeItemInSql.size() * distributeAreaInSql.size() / 8;
        Map<Pair<String, String>, String> areaSku2DeviceMap = new HashMap<>(probableSize);
        Map<Pair<String, String>, Boolean> areaDevice2TrueMap = new HashMap<>(probableSize);
        resolveBindingMapForArea(distributeItemInSql, distributeAreaInSql, areaSku2DeviceMap, areaDevice2TrueMap);

        // 构造返回值
        DstAreaRespDTO dstAreaRespDTO = new DstAreaRespDTO();
        dstAreaRespDTO.setAreaList(areaList.stream()
                .map(areaDTO -> resolveDstAreaStatus(
                        currentDeviceId,
                        areaDTO,
                        areaSku2DeviceMap,
                        areaDevice2TrueMap,
                        boundSkuGuidList
                ))
                .collect(Collectors.toList())
        );
        dstAreaRespDTO.setSnackStatus(resolveSnackStatus(currentDeviceId,
                areaSku2DeviceMap, areaDevice2TrueMap, boundSkuGuidList
        ));
        dstAreaRespDTO.setTakeoutStatus(resolveTakeoutStatus(currentDeviceId,
                areaSku2DeviceMap, areaDevice2TrueMap, boundSkuGuidList
        ));
        return dstAreaRespDTO;
    }

    /**
     * @param distributeItemInSql 门店下已绑菜品列表
     * @param distributeAreaInSql 门店下已绑区域列表
     * @param areaSku2DeviceMap   门店下 区域、菜 与 设备做全量映射关系
     * @param areaDevice2TrueMap  门店下 区域 与 设备 做关联
     */
    private void resolveBindingMapForArea(List<DistributeItemDO> distributeItemInSql,
                                          List<DistributeAreaDO> distributeAreaInSql,
                                          Map<Pair<String, String>, String> areaSku2DeviceMap,
                                          Map<Pair<String, String>, Boolean> areaDevice2TrueMap) {

        // 以区域 ID 对区域进行分组
        Map<String, List<DistributeAreaDO>> areaDeviceMap = distributeAreaInSql.stream()
                .collect(Collectors.groupingBy(DistributeAreaDO::getAreaGuid));

        // 以设备 ID 对菜品进行分组
        Map<String, List<DistributeItemDO>> deviceSkuMap = distributeItemInSql.stream()
                .collect(Collectors.groupingBy(DistributeItemDO::getDeviceId));

        for (String areaGuid : areaDeviceMap.keySet()) {
            List<DistributeAreaDO> areaDeviceList = areaDeviceMap.get(areaGuid);
            if (!CollectionUtils.isEmpty(areaDeviceList)) {
                for (DistributeAreaDO distributeAreaDO : areaDeviceList) {

                    // 获取区域所属设备Guid
                    String deviceId = distributeAreaDO.getDeviceId();

                    // 找到该设备下绑定的菜品
                    List<DistributeItemDO> deviceSkuList = deviceSkuMap.get(deviceId);

                    if (!CollectionUtils.isEmpty(deviceSkuList)) {
                        for (DistributeItemDO distributeItemDO : deviceSkuList) {
                            // areaSku2DeviceMap 集合大小为 areaGuid*菜品数 所以，在初始化 HashMap 时，设置了初始化大小
                            areaSku2DeviceMap.put(Pair.of(areaGuid, distributeItemDO.getSkuGuid()), deviceId);
                        }
                    }
                    // 表示区域被绑定到设备上
                    areaDevice2TrueMap.put(Pair.of(areaGuid, deviceId), true);
                }
            }
        }
    }

    @Override
    public List<DstTypeBindRespDTO> queryBoundItemOfDevice(DstItemQueryReqDTO dstItemQueryReqDTO) {
        return queryDistributeItemByCondition(dstItemQueryReqDTO, false);
    }

    /**
     * 查询所有出堂口绑定商品
     *
     * @param dstItemQueryReqDTO dstItemQueryReqDTO
     * @return List<DstTypeBindRespDTO>
     */
    @Override
    public List<DstTypeBindRespDTO> queryAllItemOfStore(DstItemQueryReqDTO dstItemQueryReqDTO) {
        return queryDistributeItemByCondition(dstItemQueryReqDTO, true);
    }

    @Override
    public void bindArea(DstBindAreaReqDTO dstBindAreaReqDTO) {
        // 查询已绑定区域
        String storeGuid = dstBindAreaReqDTO.getStoreGuid();
        String deviceId = dstBindAreaReqDTO.getDeviceId();
        List<String> boundAreaGuid = distributeAreaService.queryBoundAreaGuidOfDevice(storeGuid, deviceId);
        // 计算最新绑定区域
        List<String> newBindingAreaGuid =
                Optional.ofNullable(dstBindAreaReqDTO.getSelectedAreaGuidList()).orElse(new ArrayList<>());
        if (dstBindAreaReqDTO.getIsSnackSelected()) {
            newBindingAreaGuid.add(DistributeAreaDO.SNACK_AREA_GUID);
        }
        if (dstBindAreaReqDTO.getIsTakeoutSelected()) {
            newBindingAreaGuid.add(DistributeAreaDO.TAKEOUT_AREA_GUID);
        }
        // 计算待绑定区域
        List<String> toBeBoundAreaGuid = new ArrayList<>(newBindingAreaGuid);
        toBeBoundAreaGuid.removeAll(boundAreaGuid);
        // 计算已占用区域（即区域不可再次被选择）
        List<String> occupiedDeviceId = distributeItemService.queryOccupiedDeviceId(storeGuid, deviceId);
        List<String> occupiedAreaGuid = distributeAreaService.queryBoundAreaGuidOfDeviceList(storeGuid, occupiedDeviceId);
        // 断言待绑定区域可用
        occupiedAreaGuid.retainAll(toBeBoundAreaGuid);
        if (occupiedAreaGuid.size() > 0) {
            throw new BusinessException("部分区域已被绑定，请重新选择！");
        }
        // 插入待绑定区域
        distributeAreaService.simpleSaveBatchArea(storeGuid, deviceId, toBeBoundAreaGuid);
        // 删除需解除绑定的区域
        List<String> toBeRemoveAreaGuid = new ArrayList<>(boundAreaGuid);
        toBeRemoveAreaGuid.removeAll(newBindingAreaGuid);
        distributeAreaService.simpleRemoveBatchArea(storeGuid, deviceId, toBeRemoveAreaGuid);
    }

    @Override
    public void bindItem(DstBindItemReqDTO dstBindAreaReqDTO) {
        // 直接绑定商品
        if (!CollectionUtils.isEmpty(dstBindAreaReqDTO.getBindItemSkuList())) {
            dstItemBind(dstBindAreaReqDTO);
        }
        // 绑定菜品分组
        if (Boolean.TRUE.equals(dstBindAreaReqDTO.getBindingItemGroupFlag())) {
            dstItemBindGroup(dstBindAreaReqDTO);
        }
    }

    /**
     * 出堂口直接绑定商品
     */
    private void dstItemBind(DstBindItemReqDTO dstBindAreaReqDTO) {
        // 查询已绑定菜品
        String storeGuid = dstBindAreaReqDTO.getStoreGuid();
        String deviceId = dstBindAreaReqDTO.getDeviceId();
        List<String> boundSkuGuid = distributeItemService.queryBoundSkuGuidOfDevice(storeGuid, deviceId);
        // 计算最新绑定菜品
        List<PrdDstItemBindDTO> bindItemSkuList = dstBindAreaReqDTO.getBindItemSkuList();
        if (bindItemSkuList == null) {
            bindItemSkuList = new ArrayList<>();
        }
        List<String> newBindingSkuGuid = bindItemSkuList.stream()
                .map(PrdDstItemBindDTO::getSkuGuid).collect(Collectors.toList());
        // 计算待绑定菜品
        List<String> toBeBoundSkuGuid = new ArrayList<>(newBindingSkuGuid);
        toBeBoundSkuGuid.removeAll(boundSkuGuid);
        // 计算已占用菜品（即菜品不可再次被选择）
        List<String> occupiedDeviceId = distributeAreaService.queryOccupiedDeviceId(storeGuid, deviceId);
        List<String> occupiedSkuGuid = distributeItemService.queryBoundSkuGuidOfDeviceList(storeGuid, occupiedDeviceId);
        // 断言待绑定菜品可用
        occupiedSkuGuid.retainAll(toBeBoundSkuGuid);
        if (occupiedSkuGuid.size() > 0) {
            throw new BusinessException("部分菜品已被绑定，请重新选择！");
        }
        // 插入待绑定菜品
        List<PrdDstItemBindDTO> toBeBoundSkuList = bindItemSkuList.stream()
                .filter(prdDstItemBindDTO -> toBeBoundSkuGuid
                        .contains(prdDstItemBindDTO.getSkuGuid()))
                .collect(Collectors.toList());
        distributeItemService.simpleSaveBatchSku(storeGuid, deviceId, toBeBoundSkuList);
    }

    /**
     * 绑定菜品分组
     */
    private void dstItemBindGroup(DstBindItemReqDTO dstBindAreaReqDTO) {
        // 先删除
        deviceBindItemGroupService.unbind(dstBindAreaReqDTO.getStoreGuid(), null,
                dstBindAreaReqDTO.getDeviceId());
        List<String> bindingItemGroups = dstBindAreaReqDTO.getBindingItemGroups();
        List<DeviceBindItemGroupDO> deviceBindItemGroupDOList = bindingItemGroups.stream()
                .map(groupGuid -> {
                    DeviceBindItemGroupDO deviceBindItemGroupDO = new DeviceBindItemGroupDO();
                    deviceBindItemGroupDO.setGuid(String.valueOf(SnowFlakeUtil.getInstance().nextId()));
                    deviceBindItemGroupDO.setDeviceId(dstBindAreaReqDTO.getDeviceId());
                    deviceBindItemGroupDO.setGroupGuid(groupGuid);
                    deviceBindItemGroupDO.setStoreGuid(dstBindAreaReqDTO.getStoreGuid());
                    return deviceBindItemGroupDO;
                }).collect(Collectors.toList());
        deviceBindItemGroupService.saveBatch(deviceBindItemGroupDOList);
    }

    @Override
    public void unbindItem(DstBindItemReqDTO dstBindAreaReqDTO) {
        List<String> toBeRemoveSkuGuidList = dstBindAreaReqDTO.getUnbindSkuGuidList();
        if (CollectionUtils.isEmpty(toBeRemoveSkuGuidList)) return;
        distributeItemService.simpleRemoveBatchSku(dstBindAreaReqDTO.getStoreGuid(),
                dstBindAreaReqDTO.getDeviceId(), toBeRemoveSkuGuidList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reInitialize(String storeGuid, String deviceId) {
        distributeAreaService.reInitialize(storeGuid, deviceId);
        distributeItemService.reInitialize(storeGuid, deviceId);
    }

    private DstAreaStatusDTO resolveDstAreaStatus(String currentDeviceId, AreaDTO areaDTO,
                                                  Map<Pair<String, String>, String> areaSku2DeviceMap,
                                                  Map<Pair<String, String>, Boolean> areaDevice2TrueMap,
                                                  List<String> boundSkuGuidList) {
        String areaGuid = areaDTO.getGuid();
        DstAreaStatusDTO dstAreaStatusDTO = new DstAreaStatusDTO();
        dstAreaStatusDTO.setAreaGuid(areaGuid);
        dstAreaStatusDTO.setAreaName(areaDTO.getAreaName());

        // 当前设备下已绑菜品不空
        if (!CollectionUtils.isEmpty(boundSkuGuidList)) {

            List<String> deviceIdList = boundSkuGuidList.stream()
                    .map(skuGuid -> areaSku2DeviceMap.get(Pair.of(areaGuid, skuGuid)))
                    .filter(Objects::nonNull).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(deviceIdList)) {
                dstAreaStatusDTO.setIsBoundBySelf(false);
                dstAreaStatusDTO.setIsBoundByOthers(false);
            } else {
                boolean result = deviceIdList.stream().allMatch(currentDeviceId::equals);
                dstAreaStatusDTO.setIsBoundBySelf(result);
                dstAreaStatusDTO.setIsBoundByOthers(!result);
            }
        } else {
            Boolean result = areaDevice2TrueMap.get(Pair.of(areaGuid, currentDeviceId));
            dstAreaStatusDTO.setIsBoundBySelf(result != null && result);

            dstAreaStatusDTO.setIsBoundByOthers(false);
        }
        return dstAreaStatusDTO;
    }

    private DstAreaStatusDTO resolveSnackStatus(String currentDeviceId,
                                                Map<Pair<String, String>, String> areaSku2DeviceMap,
                                                Map<Pair<String, String>, Boolean> deviceArea2TrueMap,
                                                List<String> boundSkuGuidList) {
        AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid(DistributeAreaDO.SNACK_AREA_GUID);
        areaDTO.setAreaName(DistributeAreaDO.SNACK_AREA_NAME);
        return resolveDstAreaStatus(currentDeviceId, areaDTO, areaSku2DeviceMap, deviceArea2TrueMap, boundSkuGuidList);
    }

    private DstAreaStatusDTO resolveTakeoutStatus(String currentDeviceId,
                                                  Map<Pair<String, String>, String> areaSku2DeviceMap,
                                                  Map<Pair<String, String>, Boolean> deviceArea2TrueMap,
                                                  List<String> boundSkuGuidList) {
        AreaDTO areaDTO = new AreaDTO();
        areaDTO.setGuid(DistributeAreaDO.TAKEOUT_AREA_GUID);
        areaDTO.setAreaName(DistributeAreaDO.TAKEOUT_AREA_NAME);
        return resolveDstAreaStatus(currentDeviceId, areaDTO, areaSku2DeviceMap, deviceArea2TrueMap, boundSkuGuidList);
    }

    private List<DstTypeBindRespDTO> queryDistributeItemByCondition(DstItemQueryReqDTO dstItemQueryReqDTO, boolean queryAll) {
        // 当前设备信息
        String currentStoreGuid = dstItemQueryReqDTO.getStoreGuid();
        String currentDeviceId = dstItemQueryReqDTO.getDeviceId();

        // deviceId -> deviceName
        Map<String, String> deviceNameMap = deviceConfigService.getDeviceNameMapOfStore(currentStoreGuid);

        // 当前门店已绑定 sku
        List<DistributeItemDO> distributeItemInSql = distributeItemService.queryBoundItemOfStore(currentStoreGuid);
        List<String> bindSkuGuids = distributeItemInSql.stream().map(DistributeItemDO::getSkuGuid).distinct().collect(Collectors.toList());
        // 查询该门店下的所有商品信息
        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData(currentStoreGuid);
        itemSingleDTO.setSkuGuids(bindSkuGuids);
        List<MappingRespDTO> mappingRespDTOS = itemRpcService.kdsMapping(itemSingleDTO);

        // areaId -> areaName
        Map<String, String> areaNameMap = fetchAreaNameMap(dstItemQueryReqDTO);


        // 当前门店已绑定区域
        List<DistributeAreaDO> distributeAreaInSql = distributeAreaService.queryBoundAreaOfStore(currentStoreGuid)
                .stream().filter(p -> areaNameMap.containsKey(p.getAreaGuid())).collect(Collectors.toList());

        // 当前设备已经绑定的区域
        List<String> boundAreaGuidList = distributeAreaInSql.stream()
                .filter(distributeAreaDO -> currentDeviceId.equals(distributeAreaDO.getDeviceId()))
                .map(DistributeAreaDO::getAreaGuid).collect(Collectors.toList());

        // 当前门店的所有菜品的配置（超时时间、最大制作数）
        List<ItemConfigDO> allItemConfigInSql = itemConfigService.queryBatchByStoreGuid(currentStoreGuid);

        // sku -> 菜品配置
        Map<String, ItemConfigDO> itemConfigMap = allItemConfigInSql.stream()
                .collect(Collectors.toMap(ItemConfigDO::getSkuGuid, Function.identity()));

        // 已绑定关系辅助MAP、Sku已绑定“设备-区域”列表
        int probableSize = distributeItemInSql.size() * distributeAreaInSql.size() / 8;
        Map<Pair<String, String>, String> skuArea2DeviceMap = new HashMap<>(probableSize);
        Map<Pair<String, String>, Boolean> skuDevice2TrueMap = new HashMap<>(probableSize);
        Map<String, List<DstAreaBindRespDTO>> skuDeviceAreaMap = new HashMap<>(probableSize);
        resolveBindingMapForItem(
                deviceNameMap, areaNameMap,
                distributeItemInSql, distributeAreaInSql,
                skuArea2DeviceMap, skuDevice2TrueMap, skuDeviceAreaMap
        );

        // 构造返回值

        List<DstTypeBindRespDTO> list = mappingRespDTOS.stream()
                .collect(Collectors.groupingBy(o -> Pair.of(o.getCategoryId(), o.getCategoryName())))
                .entrySet()
                .stream()
                .map(typeEntry -> {
                    // 分类
                    DstTypeBindRespDTO dstTypeBindRespDTO = new DstTypeBindRespDTO();
                    dstTypeBindRespDTO.setTypeGuid(typeEntry.getKey().getFirst());
                    dstTypeBindRespDTO.setTypeName(typeEntry.getKey().getSecond());
                    // 商品
                    List<MappingRespDTO> skuList = typeEntry.getValue();
                    Map<String, MappingRespDTO> itemMap = skuList.stream()
                            .collect(Collectors.toMap(MappingRespDTO::geteDishCode, Function.identity(), (key1, key2) -> key1));
                    Map<Pair<String, String>, List<MappingRespDTO>> itemSkusMap = typeEntry.getValue().stream()
                            .collect(Collectors.groupingBy(o -> Pair.of(o.geteDishCode(), o.geteDishName())));
                    List<DstItemBindRespDTO> typeItemList = itemSkusMap.entrySet().stream()
                            .map(itemEntry -> {
                                // 规格
                                DstItemBindRespDTO dstItemBindRespDTO = new DstItemBindRespDTO();
                                dstItemBindRespDTO.setItemGuid(itemEntry.getKey().getFirst());
                                dstItemBindRespDTO.setItemName(itemEntry.getKey().getSecond());
                                dstItemBindRespDTO.setPlanItemName(itemMap.getOrDefault(itemEntry.getKey().getFirst(),
                                        new MappingRespDTO()).getPlanItemName());
                                dstItemBindRespDTO.setIsBoundBySelf(false);
                                dstItemBindRespDTO.setIsBoundByOthers(false);
                                List<DstSkuBindRespDTO> skuWithBindingInfo = getSkuWithBindingInfo(itemEntry,
                                        dstItemBindRespDTO, currentDeviceId, boundAreaGuidList,
                                        itemConfigMap, skuArea2DeviceMap, skuDevice2TrueMap, skuDeviceAreaMap
                                );
                                dstItemBindRespDTO.setSkus(skuWithBindingInfo.stream()
                                        .filter(dstSkuBindRespDTO ->
                                                queryAll || dstSkuBindRespDTO.getIsBoundBySelf())
                                        .peek(dstSkuBindRespDTO -> {
                                            if (!queryAll) {
                                                dstSkuBindRespDTO.setAreas(null);
                                            }
                                        })
                                        .collect(Collectors.toList())
                                );
                                return dstItemBindRespDTO;
                            })
                            .filter(dstItemBindRespDTO -> queryAll
                                    || !CollectionUtils.isEmpty(dstItemBindRespDTO.getSkus()))
                            .collect(Collectors.toList());
                    dstTypeBindRespDTO.setItemList(typeItemList);
                    return dstTypeBindRespDTO;
                })
                .filter(dstTypeBindRespDTO -> queryAll
                        || !CollectionUtils.isEmpty(dstTypeBindRespDTO.getItemList()))
                .collect(Collectors.toList());

        log.info("出堂口菜品列表：{}", JacksonUtils.writeValueAsString(list));
        return list;
    }


    private List<DstSkuBindRespDTO> getSkuWithBindingInfo(Map.Entry<Pair<String, String>, List<MappingRespDTO>> itemEntry,
                                                          DstItemBindRespDTO dstItemBindRespDTO,
                                                          String currentDeviceId, List<String> boundAreaGuidList,
                                                          Map<String, ItemConfigDO> itemConfigMap,
                                                          Map<Pair<String, String>, String> skuArea2DeviceMap,
                                                          Map<Pair<String, String>, Boolean> skuDevice2TrueMap,
                                                          Map<String, List<DstAreaBindRespDTO>> skuDeviceAreaMap) {
        return itemEntry.getValue().stream()
                .map(itemSku -> {
                    String skuGuid = itemSku.geteDishSkuCode();
                    DstSkuBindRespDTO dstSkuBindRespDTO = new DstSkuBindRespDTO();
                    dstSkuBindRespDTO.setSkuGuid(skuGuid);
                    dstSkuBindRespDTO.setSkuName(itemSku.geteDishSkuName());
                    dstSkuBindRespDTO.setSkuCode(itemSku.getSkuCode());
                    dstItemBindRespDTO.setPinyin(itemSku.getPinyin());
                    dstSkuBindRespDTO.setTimeout(Optional.ofNullable(itemConfigMap.get(skuGuid))
                            .map(ItemConfigDO::getTimeout).orElse(20));
                    setItemBindingFlag(skuGuid, currentDeviceId, boundAreaGuidList,
                            skuArea2DeviceMap, skuDevice2TrueMap, dstSkuBindRespDTO, dstItemBindRespDTO);
                    dstSkuBindRespDTO.setAreas(skuDeviceAreaMap.getOrDefault(skuGuid, new ArrayList<>()));
                    return dstSkuBindRespDTO;
                })
                .collect(Collectors.toList());
    }

    private void setItemBindingFlag(String skuGuid, String currentDeviceId, List<String> boundAreaGuidList,
                                    Map<Pair<String, String>, String> skuArea2DeviceMap,
                                    Map<Pair<String, String>, Boolean> skuDevice2TrueMap,
                                    DstSkuBindRespDTO dstSkuBindRespDTO, DstItemBindRespDTO dstItemBindRespDTO) {

        if (!CollectionUtils.isEmpty(boundAreaGuidList)) {
            List<String> deviceIdList = boundAreaGuidList.stream()
                    .map(areaGuid -> skuArea2DeviceMap.get(Pair.of(skuGuid, areaGuid)))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(deviceIdList)) {
                dstSkuBindRespDTO.setIsBoundBySelf(false);
                dstSkuBindRespDTO.setIsBoundBySelf(false);
                dstItemBindRespDTO.setIsBoundBySelf(false);
                dstItemBindRespDTO.setIsBoundByOthers(false);
            } else {
                boolean result = deviceIdList.stream().allMatch(currentDeviceId::equals);
                dstSkuBindRespDTO.setIsBoundBySelf(result);
                dstSkuBindRespDTO.setIsBoundByOthers(!result);
                dstItemBindRespDTO.setIsBoundBySelf(result);
                dstItemBindRespDTO.setIsBoundByOthers(!result);
            }
        } else {
            Boolean result = skuDevice2TrueMap.get(Pair.of(skuGuid, currentDeviceId));
            dstSkuBindRespDTO.setIsBoundBySelf(result != null && result);
            dstSkuBindRespDTO.setIsBoundByOthers(false);
            dstItemBindRespDTO.setIsBoundBySelf(result != null && result);
            dstItemBindRespDTO.setIsBoundByOthers(false);
        }
    }

    private void resolveBindingMapForItem(Map<String, String> deviceNameMap,
                                          Map<String, String> areaNameMap,
                                          List<DistributeItemDO> distributeItemInSql,
                                          List<DistributeAreaDO> distributeAreaInSql,
                                          Map<Pair<String, String>, String> skuArea2DeviceMap,
                                          Map<Pair<String, String>, Boolean> skuDevice2TrueMap,
                                          Map<String, List<DstAreaBindRespDTO>> skuDeviceAreaMap
    ) {
        Map<String, List<DistributeItemDO>> skuDeviceMap = distributeItemInSql.stream().collect(Collectors.groupingBy(DistributeItemDO::getSkuGuid));
        Map<String, List<DistributeAreaDO>> deviceAreaMap = distributeAreaInSql.stream().collect(Collectors.groupingBy(DistributeAreaDO::getDeviceId));
        for (String skuGuid : skuDeviceMap.keySet()) { // 遍历已绑定 skuGuid
            List<DistributeItemDO> skuDeviceList = skuDeviceMap.get(skuGuid); // sku 对应的菜
            if (!CollectionUtils.isEmpty(skuDeviceList)) {
                for (DistributeItemDO distributeItemDO : skuDeviceList) {
                    String deviceId = distributeItemDO.getDeviceId();// 菜所在的 deviceId
                    List<DistributeAreaDO> deviceAreaList = deviceAreaMap.get(deviceId);// deviceid 对应的区域列表
                    if (!CollectionUtils.isEmpty(deviceAreaList)) {
                        for (DistributeAreaDO distributeAreaDO : deviceAreaList) {
                            skuArea2DeviceMap.put(Pair.of(skuGuid, distributeAreaDO.getAreaGuid()), deviceId);
                            DstAreaBindRespDTO dstAreaBindRespDTO = new DstAreaBindRespDTO();
                            dstAreaBindRespDTO.setDeviceId(distributeAreaDO.getDeviceId());
                            dstAreaBindRespDTO.setDeviceName(deviceNameMap.get(distributeAreaDO.getDeviceId()));
                            dstAreaBindRespDTO.setAreaGuid(distributeAreaDO.getAreaGuid());
                            dstAreaBindRespDTO.setAreaName(areaNameMap.get(distributeAreaDO.getAreaGuid()));
                            skuDeviceAreaMap.computeIfAbsent(skuGuid, k -> new ArrayList<>()).add(dstAreaBindRespDTO);
                        }
                    }
                    skuDevice2TrueMap.put(Pair.of(skuGuid, deviceId), true);
                }
            }
        }
    }

    private Map<String, String> fetchAreaNameMap(DstItemQueryReqDTO dstItemQueryReqDTO) {
        List<AreaDTO> areaList = tableRpcService.query(dstItemQueryReqDTO.getStoreGuid());

        AreaDTO snackAreaDTO = new AreaDTO();
        snackAreaDTO.setGuid(DistributeAreaDO.SNACK_AREA_GUID);
        snackAreaDTO.setAreaName(DistributeAreaDO.SNACK_AREA_NAME);
        areaList.add(snackAreaDTO);

        AreaDTO takeoutAreaDTO = new AreaDTO();
        takeoutAreaDTO.setGuid(DistributeAreaDO.TAKEOUT_AREA_GUID);
        takeoutAreaDTO.setAreaName(DistributeAreaDO.TAKEOUT_AREA_NAME);
        areaList.add(takeoutAreaDTO);

        return areaList.stream().collect(Collectors.toMap(AreaDTO::getGuid, AreaDTO::getAreaName));
    }
}
