package com.holderzone.saas.store.kds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.kds.req.ItemConfBatchUpdateReqDTO;
import com.holderzone.saas.store.kds.entity.domain.ItemConfigDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemConfigService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface ItemConfigService extends IService<ItemConfigDO> {

    void insertOrUpdateBatch(ItemConfBatchUpdateReqDTO itemConfBatchUpdateReqDTO);

    List<ItemConfigDO> queryBatchBySkuGuid(List<String> skuGudList);

    List<ItemConfigDO> queryBatchByStoreGuid(String storeGuid);
}
