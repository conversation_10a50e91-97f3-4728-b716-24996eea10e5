package com.holderzone.saas.store.kds.entity.group;

import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import lombok.Data;

import java.util.Objects;

@Data
public class ItemGroupKey {


    /**
     * 订单Guid
     */
    private String orderGuid;

    /**
     * 厨房商品MD5
     */
    private String itemAttrMd5;

    /**
     * 是否是挂起单
     */
    private Boolean isHanged;

    /**
     * 是否是催单
     */
    private Boolean isUrged;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ItemGroupKey that = (ItemGroupKey) o;
        return Objects.equals(groupKey(), that.groupKey());
    }

    @Override
    public int hashCode() {
        return Objects.hash(groupKey());
    }

    private String groupKey() {
        if (isHanged) {
            return "hanged:" + orderGuid + ":" + itemAttrMd5;
        }
        if (isUrged) {
            return "urged:" + orderGuid + ":" + itemAttrMd5;
        }
        return "normal:" + orderGuid + ":" + itemAttrMd5;
    }

    public static ItemGroupKey of(PrdDstItemDTO prdDstItemDTO) {
        ItemGroupKey itemGroupKey = new ItemGroupKey();
        itemGroupKey.setOrderGuid(prdDstItemDTO.getOrderGuid());
        itemGroupKey.setItemAttrMd5(prdDstItemDTO.getItemAttrMd5());
        itemGroupKey.setIsHanged(prdDstItemDTO.getIsHanged());
        itemGroupKey.setIsUrged(prdDstItemDTO.getIsUrged());
        return itemGroupKey;
    }
}
