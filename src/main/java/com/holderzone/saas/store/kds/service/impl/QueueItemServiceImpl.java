package com.holderzone.saas.store.kds.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.anno.SwitchDatasourceAnno;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.kds.req.ItemPrepareReqDTO;
import com.holderzone.saas.store.dto.kds.req.ItemStateTransReqDTO;
import com.holderzone.saas.store.dto.kds.req.QueueQueryReqDTO;
import com.holderzone.saas.store.dto.kds.req.TradeSnackInfoDTO;
import com.holderzone.saas.store.dto.kds.resp.*;
import com.holderzone.saas.store.enums.kds.KdsItemStateEnum;
import com.holderzone.saas.store.kds.aop.SwitchContextAnno;
import com.holderzone.saas.store.kds.constant.RocketMqConfig;
import com.holderzone.saas.store.kds.entity.bo.AsyncTask;
import com.holderzone.saas.store.kds.entity.bo.QueueItemKey;
import com.holderzone.saas.store.kds.entity.bo.QueueOrderBO;
import com.holderzone.saas.store.kds.entity.domain.KitchenItemDO;
import com.holderzone.saas.store.kds.entity.domain.QueueConfigDO;
import com.holderzone.saas.store.kds.entity.domain.QueueItemDO;
import com.holderzone.saas.store.kds.entity.enums.KdsTradeModeEnum;
import com.holderzone.saas.store.kds.entity.enums.LevelDelayEnum;
import com.holderzone.saas.store.kds.entity.enums.QueueItemStatusEnum;
import com.holderzone.saas.store.kds.entity.query.QueueItemQuery;
import com.holderzone.saas.store.kds.mapper.QueueItemMapper;
import com.holderzone.saas.store.kds.mapstruct.QueueMapstruct;
import com.holderzone.saas.store.kds.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class QueueItemServiceImpl extends ServiceImpl<QueueItemMapper, QueueItemDO> implements QueueItemService {

    private final QueueConfigService queueConfigService;

    private final DistributedIdService distributedIdService;

    private final QueuePushService queuePushService;

    private final QueueMapstruct queueMapstruct;

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    private final KitchenItemService kitchenItemService;

    public QueueItemServiceImpl(QueueConfigService queueConfigService,
                                DistributedIdService distributedIdService,
                                QueuePushService queuePushService,
                                QueueMapstruct queueMapstruct,
                                DefaultRocketMqProducer defaultRocketMqProducer,
                                @Lazy KitchenItemService kitchenItemService) {
        this.queueConfigService = queueConfigService;
        this.distributedIdService = distributedIdService;
        this.queuePushService = queuePushService;
        this.queueMapstruct = queueMapstruct;
        this.defaultRocketMqProducer = defaultRocketMqProducer;
        this.kitchenItemService = kitchenItemService;
    }

    @Async
    @Override
    @SwitchContextAnno(userInfo = "#asyncTask.userInfo")
    @SwitchDatasourceAnno(enterpriseGuid = "#asyncTask.enterpriseGuid")
    public void inPreparedQueue(AsyncTask<ItemPrepareReqDTO> asyncTask) {
        ItemPrepareReqDTO itemPrepareReqDTO = asyncTask.getData();
        if (!KdsTradeModeEnum.SNACK.equals(
                KdsTradeModeEnum.ofCode(itemPrepareReqDTO.getTradeMode()))) {
            return;
        }
        List<QueueConfigDO> deviceOfStore = listDeviceOfStore(itemPrepareReqDTO.getStoreGuid());
        if (CollectionUtils.isEmpty(deviceOfStore)) {
            return;
        }
        for (QueueConfigDO queueConfigDO : deviceOfStore) {
            TradeSnackInfoDTO tradeSnackInfoDTO = itemPrepareReqDTO.getTradeSnackInfoDTO();
            Assert.notNull(tradeSnackInfoDTO, "快餐信息不得为空");
            QueueItemDO queueItemDO = new QueueItemDO();
            queueItemDO.setStoreGuid(itemPrepareReqDTO.getStoreGuid());
            queueItemDO.setDeviceGuid(queueConfigDO.getGuid());
            queueItemDO.setOrderGuid(itemPrepareReqDTO.getOrderGuid());
            queueItemDO.setOrderNo(tradeSnackInfoDTO.getOrderNo());
            queueItemDO.setOrderMode(KdsTradeModeEnum.getDisplayTypeByCode(itemPrepareReqDTO.getTradeMode()));
            queueItemDO.setOrderDesc(getMarkNo(tradeSnackInfoDTO.getMarkNo()));
            queueItemDO.setStatus(QueueItemStatusEnum.PREPARED.getStatus());
            queueItemDO.setInTime(DateTimeUtils.now());
            save(queueItemDO.setGuid(distributedIdService.nextQueueItemGuid()));
            // 队列状态变更推送
            UserContext userContext = UserContextUtils.get();
            queuePushService.statusChanged(
                    userContext.getEnterpriseGuid(),
                    userContext.getStoreGuid(),
                    queueConfigDO.getGuid(), ""
            );
        }
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    @SwitchContextAnno(userInfo = "#asyncTask.userInfo")
    @SwitchDatasourceAnno(enterpriseGuid = "#asyncTask.enterpriseGuid")
    public void inDistributedQueue(AsyncTask<ItemStateTransReqDTO> asyncTask) {
        Map<String, QueueOrderBO> queueOrderMap = buildQueueOrderMap(asyncTask.getData().getPrdDstItemList());
        if (CollectionUtils.isEmpty(queueOrderMap)) {
            log.info("queueOrderMap为空");
            return;
        }
        String storeGuid = UserContextUtils.getStoreGuid();
        List<QueueConfigDO> deviceOfStore = listDeviceOfStore(storeGuid);
        if (CollectionUtils.isEmpty(deviceOfStore)) {
            log.info("deviceOfStore为空");
            return;
        }
        // 查询原等待队列元素的inTime值、查询原取餐队列元素，并进行数据归类
        List<QueueItemDO> queueItems2Insert = new ArrayList<>();
        List<QueueItemDO> queueItems2Update = new ArrayList<>();
        Collection<QueueOrderBO> ordersDistributed = queueOrderMap.values();
        Map<String, Integer> itemLevelMap = new HashMap<>(deviceOfStore.size() * ordersDistributed.size());
        parseQueueItems(storeGuid, deviceOfStore, ordersDistributed, queueItems2Insert, queueItems2Update, itemLevelMap);
        // 新增或修改取餐队列元素
        if (!CollectionUtils.isEmpty(queueItems2Insert)) {
            saveBatch(queueItems2Insert);
        }
        if (!CollectionUtils.isEmpty(queueItems2Update)) {
            updateBatchById(queueItems2Update);
        }
        // 删除等待队列元素
        Set<String> orderToRemove = parseOrderToRemove(queueOrderMap);
        if (!CollectionUtils.isEmpty(orderToRemove)) {
            remove(new LambdaQueryWrapper<QueueItemDO>()
                    .in(QueueItemDO::getOrderGuid, orderToRemove)
                    .eq(QueueItemDO::getStatus, QueueItemStatusEnum.PREPARED.getStatus()));
        }
        // 发送expire任务 && 队列状态变更推送
        List<QueueItemDO> queueItems = new ArrayList<>();
        queueItems.addAll(queueItems2Insert);
        queueItems.addAll(queueItems2Update);
        postExpireTaskThenPushMessage(queueItems, itemLevelMap);
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    @SwitchContextAnno(userInfo = "#asyncTask.userInfo")
    @SwitchDatasourceAnno(enterpriseGuid = "#asyncTask.enterpriseGuid")
    public void backPreparedQueue(AsyncTask<KitchenItemDO> asyncTask) {
        KitchenItemDO kitchenItemDO = asyncTask.getData();
        if (!KdsTradeModeEnum.isSnackByDisplayType(kitchenItemDO.getDisplayType())) {
            return;
        }
        List<QueueItemDO> queueItemsInSql = list(new LambdaQueryWrapper<QueueItemDO>()
                .like(QueueItemDO::getDstItems, kitchenItemDO.getGuid())
                .eq(QueueItemDO::getOrderGuid, kitchenItemDO.getOrderGuid())
                .eq(QueueItemDO::getStatus, QueueItemStatusEnum.DISTRIBUTED.getStatus()));
        if (CollectionUtils.isEmpty(queueItemsInSql)) {
            return;
        }
        UserContext userContext = UserContextUtils.get();
        for (QueueItemDO queueItemInSql : queueItemsInSql) {
            // 从取餐队列移除
            List<String> strings = JacksonUtils.toObjectList(String.class, queueItemInSql.getDstItems());
            strings.remove(kitchenItemDO.getGuid());
            if (strings.isEmpty()) {
                removeById(queueItemInSql.getId());
            } else {
                QueueItemDO queueItemDO = new QueueItemDO();
                queueItemDO.setDstItems(JacksonUtils.writeValueAsString(strings));
                updateById(queueItemDO.setId(queueItemInSql.getId()));
            }
            // 添加到等待队列
            if (count(new LambdaQueryWrapper<QueueItemDO>()
                    .eq(QueueItemDO::getStoreGuid, queueItemInSql.getStoreGuid())
                    .eq(QueueItemDO::getDeviceGuid, queueItemInSql.getDeviceGuid())
                    .eq(QueueItemDO::getOrderGuid, kitchenItemDO.getOrderGuid())
                    .eq(QueueItemDO::getStatus, QueueItemStatusEnum.PREPARED.getStatus())
            ) == 0) {
                queueItemInSql.setStatus(QueueItemStatusEnum.PREPARED.getStatus());
                queueItemInSql.setDstTime(null);
                queueItemInSql.setDstItems(null);
                queueItemInSql.setDstExpireTime(null);
                save(queueItemInSql.setGuid(distributedIdService.nextQueueItemGuid()));
            }

            // 队列状态变更推送
            queuePushService.statusChanged(
                    userContext.getEnterpriseGuid(),
                    userContext.getStoreGuid(),
                    queueItemInSql.getDeviceGuid(),
                    ""
            );
        }
    }

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    @SwitchContextAnno(userInfo = "#asyncTask.userInfo")
    @SwitchDatasourceAnno(enterpriseGuid = "#asyncTask.enterpriseGuid")
    public void outPrepareQueue(AsyncTask<KitchenItemDO> asyncTask) {
        KitchenItemDO kitchenItemDO = asyncTask.getData();
        if (KdsTradeModeEnum.isSnackByDisplayType(kitchenItemDO.getDisplayType())
                && kitchenItemService.count(new LambdaQueryWrapper<KitchenItemDO>()
                .between(KitchenItemDO::getKitchenState, 4, 6)
                .ne(KitchenItemDO::getItemState, 99)
                .eq(KitchenItemDO::getOrderGuid, kitchenItemDO.getOrderGuid())) == 0) {
            remove(new LambdaQueryWrapper<QueueItemDO>()
                    .eq(QueueItemDO::getOrderGuid, kitchenItemDO.getOrderGuid())
                    .eq(QueueItemDO::getStatus, QueueItemStatusEnum.PREPARED.getStatus()));
            UserContext userContext = UserContextUtils.get();
            queuePushService.statusChanged(
                    userContext.getEnterpriseGuid(),
                    userContext.getStoreGuid(),
                    null, ""
            );
        }
    }

    @Override
    public KdsQueueRespDTO query(QueueQueryReqDTO queueQueryReqDTO) {
        KdsQueueRespDTO kdsQueueRespDTO = new KdsQueueRespDTO();

        List<QueueItemDO> list = list(new LambdaQueryWrapper<QueueItemDO>()
                .ge(QueueItemDO::getInTime, DateTimeUtils.now().minusDays(3))
                .eq(QueueItemDO::getStoreGuid, queueQueryReqDTO.getStoreGuid())
                .eq(QueueItemDO::getDeviceGuid, queueQueryReqDTO.getDeviceId()));
        Map<Integer, List<QueueItemDO>> collect = list.stream().collect(Collectors.groupingBy(QueueItemDO::getStatus));

        List<QueueItemDO> waitingQueueItems = collect.get(QueueItemStatusEnum.PREPARED.getStatus());
        if (CollectionUtils.isEmpty(waitingQueueItems)) {
            kdsQueueRespDTO.setWaitingQueue(Collections.emptyList());
        } else {
            waitingQueueItems.sort(Comparator.comparing(QueueItemDO::getInTime));
            kdsQueueRespDTO.setWaitingQueue(queueMapstruct.doToDTO(waitingQueueItems));
        }

        List<QueueItemDO> takeMealQueueItems = collect.get(QueueItemStatusEnum.DISTRIBUTED.getStatus());
        if (CollectionUtils.isEmpty(takeMealQueueItems)) {
            kdsQueueRespDTO.setTakeMealQueue(Collections.emptyList());
        } else {
            takeMealQueueItems.sort(Comparator.comparing(QueueItemDO::getDstTime));
            kdsQueueRespDTO.setTakeMealQueue(queueMapstruct.doToDTO(takeMealQueueItems));
        }

        return kdsQueueRespDTO;
    }

    @Override
    public void expireAndNotify(QueueItemDTO queueItemDTO) {
        boolean removed = remove(new LambdaQueryWrapper<QueueItemDO>()
                .eq(QueueItemDO::getStoreGuid, queueItemDTO.getStoreGuid())
                .eq(QueueItemDO::getDeviceGuid, queueItemDTO.getDeviceGuid())
                .le(QueueItemDO::getDstExpireTime, DateTimeUtils.now().plusSeconds(1))
        );
        if (removed) {
            UserContext userContext = UserContextUtils.get();
            queuePushService.statusChanged(
                    userContext.getEnterpriseGuid(),
                    userContext.getStoreGuid(),
                    queueItemDTO.getDeviceGuid(), ""
            );
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dstCallForMealAgain(KitchenItemDTO kitchenItemDTO) {
        Assert.notNull(kitchenItemDTO.getDisplayType(), "屏幕显示分类不得为空");
        if (!KdsTradeModeEnum.isSnackByDisplayType(kitchenItemDTO.getDisplayType())) {
            return;
        }
        String storeGuid = UserContextUtils.getStoreGuid();
        List<QueueConfigDO> deviceOfStore = listDeviceOfStore(storeGuid);
        if (CollectionUtils.isEmpty(deviceOfStore)) {
            return;
        }
        // 已存在的队列Item
        List<QueueItemDO> queueItemsInSql = list(new LambdaQueryWrapper<QueueItemDO>()
                .eq(QueueItemDO::getOrderGuid, kitchenItemDTO.getOrderGuid())
                .eq(QueueItemDO::getStatus, QueueItemStatusEnum.DISTRIBUTED.getStatus()));

        // 已生效的取餐队列元素，直接播报语音
        UserContext userContext = UserContextUtils.get();
        for (QueueItemDO queueItem : queueItemsInSql) {
            queuePushService.statusChanged(
                    userContext.getEnterpriseGuid(),
                    userContext.getStoreGuid(),
                    queueItem.getDeviceGuid(),
                    "请" + queueItem.getOrderDesc() + "号顾客前来取餐"
            );
        }

        // 已过期的取餐队列元素，首先入取餐队列，然后播报语音
        List<String> itemGuidInSql = queueItemsInSql.stream()
                .map(QueueItemDO::getDeviceGuid).collect(Collectors.toList());
        List<QueueConfigDO> collect1 = deviceOfStore.stream()
                .filter(queueConfigDO -> !itemGuidInSql.contains(queueConfigDO.getGuid()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect1)) {
            return;
        }
        List<QueueItemDO> queueItems2Insert = new ArrayList<>();
        Map<String, Integer> itemLevelMap = new HashMap<>(collect1.size());
        for (QueueConfigDO queueConfigDO : collect1) {
            QueueItemDO queueItemDO = new QueueItemDO();
            queueItemDO.setGuid(distributedIdService.nextQueueItemGuid());
            queueItemDO.setStoreGuid(storeGuid);
            queueItemDO.setDeviceGuid(queueConfigDO.getGuid());
            queueItemDO.setOrderGuid(kitchenItemDTO.getOrderGuid());
            queueItemDO.setOrderNo(kitchenItemDTO.getOrderNumber());
            queueItemDO.setOrderMode(kitchenItemDTO.getDisplayType());
            queueItemDO.setOrderDesc(getMarkNo(kitchenItemDTO.getOrderSerialNo()));
            queueItemDO.setStatus(QueueItemStatusEnum.DISTRIBUTED.getStatus());
            queueItemDO.setInTime(DateTimeUtils.now());
            queueItemDO.setDstTime(DateTimeUtils.now());
            queueItemDO.setDstItems(JacksonUtils.writeValueAsString(
                    Collections.singleton(kitchenItemDTO.getGuid())));
            queueItemDO.setDstExpireTime(queueItemDO.getDstTime()
                    .plusSeconds(LevelDelayEnum.getSecondsByLevel(queueConfigDO.getConfirmTtlLevel())));
            queueItems2Insert.add(queueItemDO);
            itemLevelMap.put(queueItemDO.getGuid(), queueConfigDO.getConfirmTtlLevel());
        }
        saveBatch(queueItems2Insert);

        // 发送expire任务 && 队列状态变更推送
        postExpireTaskThenPushMessage(queueItems2Insert, itemLevelMap);
    }

    private String getMarkNo(String markNo) {
        if (markNo == null) {
            return null;
        }
        int idx = markNo.indexOf("/");
        if (idx > -1) {
            markNo = markNo.substring(idx + 1);
        }
        return markNo;
    }

    private List<QueueConfigDO> listDeviceOfStore(String storeGuid) {
        return queueConfigService.list(
                new LambdaQueryWrapper<QueueConfigDO>().eq(QueueConfigDO::getStoreGuid, storeGuid)
        );
    }

    private Map<String, QueueOrderBO> buildQueueOrderMap(List<PrdDstItemDTO> prdDstItemList) {
        List<QueueOrderBO> queueOrderBoList = new ArrayList<>();
        for (PrdDstItemDTO prdDstItemDTO : prdDstItemList) {
            log.info("prdDstItemDTO={}", JacksonUtils.writeValueAsString(prdDstItemDTO));
            if (prdDstItemDTO.getIsWeight()) {
                PrdDstItemTableDTO weightKitchenItem = prdDstItemDTO.getWeightKitchenItem();
                Assert.notNull(weightKitchenItem, "称重商品不得为空");
                queueOrderBoList.add(QueueOrderBO.of(weightKitchenItem));
            } else {
                List<PrdDstItemTableDTO> kitchenItemList = prdDstItemDTO.getKitchenItemList();
                BigDecimal cookingCount = prdDstItemDTO.getCurrentCount();
                Assert.notNull(kitchenItemList, "非称重商品不得为空");
                Assert.notEmpty(kitchenItemList, "非称重商品不得为空");
                Assert.isTrue(cookingCount.intValue() <= kitchenItemList.size(), "非称重商品不得大于列表Size");
                queueOrderBoList.addAll(kitchenItemList.subList(0, cookingCount.intValue()).stream()
                        .map(QueueOrderBO::of).collect(Collectors.toList()));
            }
        }
        return queueOrderBoList.stream()
                .filter(QueueOrderBO::isSnackOrder)
                .collect(Collectors.toMap(QueueOrderBO::getOrderGuid, Function.identity(), QueueOrderBO::merge));
    }

    private Set<String> parseOrderToRemove(Map<String, QueueOrderBO> queueOrderMap) {
        List<KitchenItemDO> kitchenItemRemained = kitchenItemService.list(new LambdaQueryWrapper<KitchenItemDO>()
                .select(KitchenItemDO::getOrderGuid)
                .isNull(KitchenItemDO::getDistributeTime)
                .in(KitchenItemDO::getOrderGuid, queueOrderMap.keySet())
                .ne(KitchenItemDO::getItemState, KdsItemStateEnum.REFUNDED.getCode())
                .eq(KitchenItemDO::getDisplayType, KdsTradeModeEnum.SNACK.getDisplayType())
                .groupBy(KitchenItemDO::getOrderGuid));
        Set<String> ordersRemained = kitchenItemRemained.stream()
                .map(KitchenItemDO::getOrderGuid)
                .collect(Collectors.toSet());
        Set<String> orderToRemove = queueOrderMap.keySet();
        orderToRemove.removeAll(ordersRemained);
        return orderToRemove;
    }

    private void parseQueueItems(String storeGuid,
                                 List<QueueConfigDO> deviceOfStore, Collection<QueueOrderBO> ordersDistributed,
                                 List<QueueItemDO> queueItems2Insert, List<QueueItemDO> queueItems2Update,
                                 Map<String, Integer> itemLevelMap) {
//        List<QueueItemDO> oriQueueItemsOfOrder = list(new LambdaQueryWrapper<QueueItemDO>()
//                .eq(QueueItemDO::getStoreGuid, storeGuid)
//                .in(QueueItemDO::getOrderGuid, ordersDistributed.stream()
//                        .map(QueueOrderBO::getOrderGuid).collect(Collectors.toList())));
        QueueItemQuery queueItemQuery = new QueueItemQuery();
        queueItemQuery.setStoreGuid(storeGuid);
        queueItemQuery.setOrderGuidList(ordersDistributed.stream()
                .map(QueueOrderBO::getOrderGuid).collect(Collectors.toList()));
        List<QueueItemDO> oriQueueItemsOfOrder = baseMapper.queryOriQueueItemsOfOrder(queueItemQuery);
        Map<QueueItemKey, LocalDateTime> itemInTimeMap = oriQueueItemsOfOrder.stream()
                .filter(queueItemDO -> QueueItemStatusEnum.isPrepared(queueItemDO.getStatus()))
                .collect(Collectors.toMap(QueueItemKey::of, QueueItemDO::getInTime));
        Map<QueueItemKey, QueueItemDO> itemIdentityMap = oriQueueItemsOfOrder.stream()
                .filter(queueItemDO -> QueueItemStatusEnum.isDistributed(queueItemDO.getStatus()))
                .collect(Collectors.toMap(QueueItemKey::of, Function.identity()));
        for (QueueConfigDO queueConfigDO : deviceOfStore) {
            String deviceGuid = queueConfigDO.getGuid();
            Integer confirmTtl = queueConfigDO.getConfirmTtlLevel();
            for (QueueOrderBO queueOrder : ordersDistributed) {
                QueueItemDO queueItemDO = new QueueItemDO();
                queueItemDO.setStoreGuid(storeGuid);
                queueItemDO.setDeviceGuid(deviceGuid);
                queueItemDO.setOrderDesc(getMarkNo(queueOrder.getOrderDesc()));
                QueueItemKey queueItemKey = QueueItemKey.of(storeGuid, deviceGuid, queueOrder.getOrderGuid());
                QueueItemDO itemAlreadyExisted = itemIdentityMap.get(queueItemKey);
                if (itemAlreadyExisted != null) {
                    queueItemDO.setId(itemAlreadyExisted.getId());
                    queueItemDO.setGuid(itemAlreadyExisted.getGuid());
                    List<String> strings = JacksonUtils.toObjectList(String.class, itemAlreadyExisted.getDstItems());
                    strings.addAll(queueOrder.getKitchenItemGuidSet());
                    queueItemDO.setDstItems(JacksonUtils.writeValueAsString(strings));
                    queueItemDO.setDstExpireTime(DateTimeUtils.now()
                            .plusSeconds(LevelDelayEnum.getSecondsByLevel(confirmTtl)));
                    queueItems2Update.add(queueItemDO);
                    itemLevelMap.put(itemAlreadyExisted.getGuid(), confirmTtl);
                } else {
                    queueItemDO.setGuid(distributedIdService.nextQueueItemGuid());
                    queueItemDO.setOrderGuid(queueOrder.getOrderGuid());
                    queueItemDO.setOrderNo(queueOrder.getOrderNo());
                    queueItemDO.setOrderMode(queueOrder.getOrderMode());
                    queueItemDO.setStatus(QueueItemStatusEnum.DISTRIBUTED.getStatus());
                    queueItemDO.setInTime(itemInTimeMap.getOrDefault(queueItemKey, DateTimeUtils.now()));
                    queueItemDO.setDstTime(DateTimeUtils.now());
                    queueItemDO.setDstItems(JacksonUtils.writeValueAsString(queueOrder.getKitchenItemGuidSet()));
                    queueItemDO.setDstExpireTime(queueItemDO.getDstTime()
                            .plusSeconds(LevelDelayEnum.getSecondsByLevel(confirmTtl)));
                    queueItems2Insert.add(queueItemDO);
                    itemLevelMap.put(queueItemDO.getGuid(), confirmTtl);
                }
            }
        }
    }

    private void postExpireTaskThenPushMessage(List<QueueItemDO> queueItems2Insert, Map<String, Integer> itemLevelMap) {
        // 发送expire任务
        for (QueueItemDO queueItem : queueItems2Insert) {
            QueueItemDTO queueItemDTO = new QueueItemDTO();
            queueItemDTO.setGuid(queueItem.getGuid());
            queueItemDTO.setStoreGuid(queueItem.getStoreGuid());
            queueItemDTO.setDeviceGuid(queueItem.getDeviceGuid());
            Message message = new Message(
                    RocketMqConfig.KDS_QUEUE_EXPIRE_TOPIC,
                    RocketMqConfig.KDS_QUEUE_EXPIRE_TAG,
                    JacksonUtils.toJsonByte(queueItemDTO)
            );
            message.getProperties().put("userInfo", UserContextUtils.getJsonStr());
            message.setDelayTimeLevel(itemLevelMap.get(queueItem.getGuid()));
            defaultRocketMqProducer.sendMessage(message);
        }
        // 队列状态变更推送
        UserContext userContext = UserContextUtils.get();
        for (QueueItemDO queueItem : queueItems2Insert) {
            queuePushService.statusChanged(
                    userContext.getEnterpriseGuid(),
                    userContext.getStoreGuid(),
                    queueItem.getDeviceGuid(),
                    "请" + queueItem.getOrderDesc() + "号顾客前来取餐"
            );
        }
    }
}
