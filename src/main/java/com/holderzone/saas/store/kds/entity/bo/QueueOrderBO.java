package com.holderzone.saas.store.kds.entity.bo;

import com.holderzone.saas.store.dto.kds.resp.PrdDstItemTableDTO;
import com.holderzone.saas.store.kds.entity.enums.KdsTradeModeEnum;
import lombok.Data;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

@Data
public class QueueOrderBO {

    private String orderGuid;

    private String orderNo;

    private String orderDesc;

    private Integer orderMode;

    private String kitchenItemGuid;

    private Set<String> kitchenItemGuidSet;

    public static QueueOrderBO of(PrdDstItemTableDTO prdDstItemTableDTO) {
        QueueOrderBO queueOrderBo = new QueueOrderBO();
        queueOrderBo.setOrderGuid(prdDstItemTableDTO.getOrderGuid());
        queueOrderBo.setOrderNo(prdDstItemTableDTO.getOrderNumber());
        queueOrderBo.setOrderMode(prdDstItemTableDTO.getDisplayType());
        queueOrderBo.setOrderDesc(prdDstItemTableDTO.getOrderSerialNo());
        queueOrderBo.setKitchenItemGuid(prdDstItemTableDTO.getKitchenItemGuid());
        queueOrderBo.setKitchenItemGuidSet(new HashSet<>(Collections.singleton(prdDstItemTableDTO.getKitchenItemGuid())));
        return queueOrderBo;
    }

    public QueueOrderBO merge(QueueOrderBO queueOrderBO) {
        this.orderGuid = queueOrderBO.getOrderGuid();
        this.orderNo = queueOrderBO.getOrderNo();
        this.orderMode = queueOrderBO.getOrderMode();
        this.orderDesc = queueOrderBO.getOrderDesc();
        this.kitchenItemGuidSet.addAll(queueOrderBO.getKitchenItemGuidSet());
        return this;
    }

    public boolean isSnackOrder() {
        return this.orderMode == KdsTradeModeEnum.SNACK.getDisplayType();
    }
}
