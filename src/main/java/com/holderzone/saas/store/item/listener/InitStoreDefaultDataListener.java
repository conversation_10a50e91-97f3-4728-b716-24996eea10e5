package com.holderzone.saas.store.item.listener;

/**
 * <AUTHOR>
 * @version 1.0
 * @className InitDefaultDataListener
 * @date 2019/12/18 上午10:34
 * @description 默认门店数据监听
 * @program holder-saas-store-item
 */

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.enums.MchntTypeEnum;
import com.holderzone.saas.store.item.constant.MqConstant;
import com.holderzone.saas.store.item.service.IDefaultDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 初始化门店商品数据(属性，属性组，商品，分类，规格)
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = MqConstant.DOWNSTREAM_STORE_TOPIC,
        tags = {
                MqConstant.DOWNSTREAM_STORE_CREATE_TAG
        },
        consumerGroup = MqConstant.DOWNSTREAM_INIT_STORE_ITEM_GROUP
)
public class InitStoreDefaultDataListener extends AbstractRocketMqConsumer<RocketMqTopic, StoreDTO> {

    private final IDefaultDataService iDefaultDataService;

    @Autowired
    public InitStoreDefaultDataListener(IDefaultDataService iDefaultDataService) {
        this.iDefaultDataService = iDefaultDataService;
    }

    @Override
    public boolean consumeMsg(StoreDTO storeDTO, MessageExt messageExt) {
        if(Objects.equals(storeDTO.getMchntTypeCode(),MchntTypeEnum.CATERING.getCode())) {
            UserContextUtils.put(messageExt.getProperty(MqConstant.DOWNSTREAM_CONTEXT));
            //将Enterprise存入threadLocal中
            EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            try {
                ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
                itemSingleDTO.setModel(0);
                itemSingleDTO.setData(storeDTO.getGuid());
                iDefaultDataService.addAttr(itemSingleDTO); //初始化门店属性组
                iDefaultDataService.addInitTypeAndItem(itemSingleDTO); //初始化门店商品 分类 规格
                log.info("门店：{} 初始化门店商品数据成功", storeDTO.getName());
            } catch (BusinessException e) {
                log.error("门店：{} 初始化门店商品数据发生错误：{}，初始化消息已消费", storeDTO.getName(), e.getMessage());
                return true;
            } catch (Exception e) {
                log.error("门店：{} 初始化门店商品数据发生错误：{}", storeDTO.getName(), e.getMessage());
                return false;
            } finally {
                EnterpriseIdentifier.remove();
            }
        }
        return true;
    }
}
