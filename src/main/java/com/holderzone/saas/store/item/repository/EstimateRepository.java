package com.holderzone.saas.store.item.repository;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidDTO;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.PkgSkuDTO;
import com.holderzone.saas.store.enums.item.SoldOutEnum;
import com.holderzone.saas.store.item.config.LocalCacheConfig;
import com.holderzone.saas.store.item.mapper.EstimateMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-01-03
 * @description 估清商品仓储层操作
 *
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class EstimateRepository {

    private final EstimateMapper estimateMapper;

    @Cacheable(cacheNames = {LocalCacheConfig.CacheExpires.SECOND_10},key = "'ESTIMATE_ITEM_LIST' + #baseDTO.storeGuid" ,unless = "#result == null || #result.size() == 0")
    public List<ItemEstimateForAndroidRespDTO> listNormalEstimateItem(BaseDTO baseDTO){
        List<ItemEstimateForAndroidDTO> normalEstimateItemList = estimateMapper.queryEstimateAllForSyn(baseDTO);
        if (CollectionUtils.isEmpty(normalEstimateItemList)) {
            log.info("菜谱无沽清商品信息：{}", normalEstimateItemList);
            return transferItemEstimateResp(normalEstimateItemList);
        }
        List<PkgSkuDTO> pkgSkuDTOList = estimateMapper.queryNormalPkgSkuDTO(baseDTO.getStoreGuid());
        addPkgItem(pkgSkuDTOList, normalEstimateItemList);
        log.info("沽清商品信息：{}",normalEstimateItemList);
        return transferItemEstimateResp(normalEstimateItemList);
    }

    @Cacheable(cacheNames = {LocalCacheConfig.CacheExpires.SECOND_10},key = "'PLAN_ESTIMATE_ITEM_LIST' + #storeGuid + #planGuid" ,unless = "#result == null || #result.size() == 0")
    public List<ItemEstimateForAndroidRespDTO> listPlanEstimateItem(String storeGuid,String planGuid){
        List<ItemEstimateForAndroidDTO> planEstimateItemList = estimateMapper.queryEstimateAllForSynPlan(planGuid, storeGuid);
        if (CollectionUtils.isEmpty(planEstimateItemList)) {
            log.info("菜谱无沽清商品信息：{}", planEstimateItemList);
            return transferItemEstimateResp(planEstimateItemList);
        }
        List<PkgSkuDTO> pkgSkuDTOList = estimateMapper.queryPlanPkgSkuDTO(planGuid);
        addPkgItem(pkgSkuDTOList, planEstimateItemList);
        log.info("菜谱沽清商品信息：{}", planEstimateItemList);
        return transferItemEstimateResp(planEstimateItemList);
    }

    private void addPkgItem(List<PkgSkuDTO> pkgSkuDTOList,
                            List<ItemEstimateForAndroidDTO> estimateItemList) {
        if (!CollectionUtils.isEmpty(pkgSkuDTOList)) {
            Map<String, List<PkgSkuDTO>> pkgSkuGroupMap = pkgSkuDTOList.stream()
                    .collect(Collectors.groupingBy(PkgSkuDTO::getSkuGuid));
            List<String> planEstimateSkuGuidList = estimateItemList.stream()
                    .filter(e -> e.getResidueQuantity().compareTo(BigDecimal.ZERO) == 0)
                    .map(ItemEstimateForAndroidDTO::getSkuGuid)
                    .distinct()
                    .collect(Collectors.toList());
            pkgSkuGroupMap.forEach((skuGuid, subSkuList) -> {
                List<String> subSkuGuidList = subSkuList.stream()
                        .map(PkgSkuDTO::getSubSkuGuid)
                        .distinct()
                        .collect(Collectors.toList());
                if (planEstimateSkuGuidList.contains(skuGuid)) {
                    return;
                }
                if (planEstimateSkuGuidList.containsAll(subSkuGuidList)) {
                    ItemEstimateForAndroidDTO estimate = new ItemEstimateForAndroidDTO();
                    estimate.setSkuGuid(skuGuid);
                    estimate.setIsSoldOut(SoldOutEnum.YSE.getCode());
                    estimateItemList.add(estimate);
                }
            });
        }
    }

    public List<ItemEstimateForAndroidRespDTO> listEstimateBySkuGuid(Set<String> skuGuidList, String storeGuid) {
        return transferItemEstimateResp(estimateMapper.queryEstimateBySkuGuid(skuGuidList,storeGuid));
    }

    private List<ItemEstimateForAndroidRespDTO> transferItemEstimateResp(List<ItemEstimateForAndroidDTO> estimateItemList){
        if(CollUtil.isEmpty(estimateItemList)){
            return Lists.newArrayList();
        }
        //根据skuguid去重
        estimateItemList = estimateItemList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>((Comparator.comparing(ItemEstimateForAndroidDTO::getSkuGuid)))), ArrayList::new));
        List<ItemEstimateForAndroidRespDTO> result = Lists.newArrayList();
        estimateItemList.forEach(o -> {
            //是否手动禁售 2：是
            if (o.getIsSoldOut() == 2) {
                result.add(ItemEstimateForAndroidRespDTO.builder()
                        .isSoldOut(2)
                        .residueQuantity(BigDecimal.ZERO)
                        .reminderThreshold(BigDecimal.TEN)
                        .skuGuid(o.getSkuGuid()).build());
                //状态为限量 且库存数为0 则估清
            } else if (o.getIsSoldOut() == 1 && o.getIsTheLimit() == 2) {
                BigDecimal residueQuantity = BigDecimal.ZERO;
                //估清状态 1：否 2：是
                int isSoldOut = 2;
                //状态为限量 且 库存不为0 改为可售 库存加入结果对象
                if (o.getResidueQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    residueQuantity = o.getResidueQuantity();
                    isSoldOut = 1;
                }
                result.add(ItemEstimateForAndroidRespDTO.builder()
                        .isSoldOut(isSoldOut)
                        .residueQuantity(residueQuantity)
                        .reminderThreshold(BigDecimal.TEN)
                        .skuGuid(o.getSkuGuid()).build());
            }
        });
        return result;

    }
}
