package com.holderzone.saas.store.item.entity.enums;

public enum ItemQueryTypeEnum {

    ALL(0, "全部类型"),
    BANQUET(1 ,"宴会套餐"),
    UN_WEIGHT(2 ,"单品，套餐（可选，固定），规格"),;

    private int code;
    private String desc;

    ItemQueryTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (ItemQueryTypeEnum c : ItemQueryTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
