package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品售卖状态记录
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsi_estimate_sell_log")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EstimateSellLogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 商品GUID
     */
    private String itemGuid;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 规格GUID
     */
    private String skuGuid;

    /**
     * 规格名称(固定规格名称为空字符串"")
     */
    private String skuName;

    /**
     * 当前售卖状态 0-停售 1-在售
     */
    private Integer sell;

    /**
     * 操作类型
     */
    private String opType;

    /**
     * 操作时间
     */
    private LocalDateTime opTime;
}
