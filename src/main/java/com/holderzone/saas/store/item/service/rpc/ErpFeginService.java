package com.holderzone.saas.store.item.service.rpc;

import com.holderzone.saas.store.dto.erp.erpretail.GoodsExportDTO;
import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.CreateRepertoryReqDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ErpService
 * @date 2019/10/26 下午5:34
 * @description //Erp fegin远程调用
 * @program holder
 */

@Component
@FeignClient(name = "HOLDER-SAAS-STORE-ERP", fallbackFactory = ErpFeginService.ServiceFallBack.class)
public interface ErpFeginService {

    @ApiOperation(value = "商超开启库存", notes = "返回保存状态true/false")
    @PostMapping("/repertory/insert_repertory")
    boolean saveRepertory(CreateRepertoryReqDTO inRepertoryDTO);

    @ApiOperation(value = "商超修改库存信息", notes = "返回保存状态true/false")
    @PostMapping("/repertory/modify_goods_info")
    boolean modifyGoodsInfo(InOutGoodsDTO inOutGoodsDTO);

    @ApiOperation(value = "获取商超商品库存")
    @PostMapping("/repertory/query_goods_info")
    InOutGoodsDTO getItemStock(@RequestParam("goodsGuid") String guid);

    @ApiOperation(value = "获取商品信息导出列表")
    @PostMapping("/repertory/query_export_goods_list")
    List<GoodsExportDTO> queryExportGoodsList(@RequestBody List<String> goodsGuids);


    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ErpFeginService> {
        @Override
        public ErpFeginService create(Throwable throwable) {
            return new ErpFeginService() {

                @Override
                public boolean saveRepertory(CreateRepertoryReqDTO inRepertoryDTO) {
                    log.error("商超开启库存 " + throwable.getMessage());
                    throw new RuntimeException("商超开启库存" + throwable.getMessage());
                }

                @Override
                public boolean modifyGoodsInfo(InOutGoodsDTO inOutGoodsDTO) {
                    log.error("商超修改库存信息 " + throwable.getMessage());
                    throw new RuntimeException("商超修改库存信息" + throwable.getMessage());
                }

                @Override
                public InOutGoodsDTO getItemStock(String guid) {
                    log.error("获取商超商品库存 " + throwable.getMessage());
                    throw new RuntimeException("获取商超商品库存" + throwable.getMessage());
                }

                @Override
                public List<GoodsExportDTO> queryExportGoodsList(List<String> goodsGuids) {
                    log.error("获取商品导出信息 " + throwable.getMessage());
                    throw new RuntimeException("获取商品导出信息" + throwable.getMessage());
                }
            };
        }
    }
}
