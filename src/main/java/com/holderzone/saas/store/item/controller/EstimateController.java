package com.holderzone.saas.store.item.controller;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemSkuStockDTO;
import com.holderzone.saas.store.dto.item.req.EstimateBatchReqDTO;
import com.holderzone.saas.store.dto.item.req.EstimateForManualReqDTO;
import com.holderzone.saas.store.dto.item.req.EstimateMerchantReqDTO;
import com.holderzone.saas.store.dto.item.req.EstimateReqDTO;
import com.holderzone.saas.store.dto.item.req.estimate.EstimateForAndroidReqDTO;
import com.holderzone.saas.store.dto.item.resp.EstimateItemResidueMemchantRespDTO;
import com.holderzone.saas.store.dto.item.resp.EstimateMerchantConfigRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidDTO;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateResultRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.item.builder.EstimateBizBuilder;
import com.holderzone.saas.store.item.entity.bo.EstimateBO;
import com.holderzone.saas.store.item.service.IEstimateService;
import com.holderzone.saas.store.item.service.rpc.CloudEnterpriseFeignClient;
import com.holderzone.sdk.annotation.RateLimit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@RestController
@RequestMapping("/estimate")
@Api(value = "估清接口")
@AllArgsConstructor
public class EstimateController {

    private final IEstimateService iEstimateService;
    private final CloudEnterpriseFeignClient cloudEnterpriseFeignClient;

    @ApiOperation(value = "设置估清", notes = "商户后台菜品设置估清")
    @PostMapping("/save")
    public Integer saveEstimate(@RequestBody @Valid EstimateReqDTO request) {
        log.info("估清新增or更新接口入参,request={}", JacksonUtils.writeValueAsString(request));
        return iEstimateService.saveOrUpdate(request) ? 1 : 0;
    }

    /**
     * 商户后台菜品设置估清
     *
     * @param request
     * @return
     */
    @PostMapping("/batch_save")
    public Boolean batchSave(@RequestBody @Valid EstimateBatchReqDTO request) {
        log.info("估清新增or更新接口入参,request={}", JacksonUtils.writeValueAsString(request));
        try {
            iEstimateService.batchSave(request);
        } catch (BusinessException ex) {
            log.warn(" 批量保存估清失败 {}", ex);
            return false;
        }
        return true;
    }

    @PostMapping("/get_estimate_list_store")
    public Page<EstimateMerchantConfigRespDTO> getEstimateListStore(@RequestBody @Valid EstimateMerchantReqDTO request) {
        log.info("商户后台-估清菜品列表接口入参,request={}", JacksonUtils.writeValueAsString(request));
        return iEstimateService.getItemEstimates(request);
    }

    @ApiOperation(value = "一体机设置估清", notes = "一体机手动设置菜品估清状态")
    @PostMapping("/save_sold_out_status")
    public Integer saveSoldOutStatus(@RequestBody @Valid EstimateForManualReqDTO request) {
        log.info("一体机手动设置菜品估清状态接口入参,request={}", JacksonUtils.writeValueAsString(request));
        return iEstimateService.saveSoldOutStatus(request) ? 1 : 0;
    }

    @ApiOperation(value = "商户后台获取估清菜品", notes = "商户后台条件获取估清菜品剩余可售数")
    @PostMapping("/get_estimate_item_residue_store")
    public Page<EstimateItemResidueMemchantRespDTO> getEstimateItemResidueStore(@RequestBody @Valid EstimateMerchantReqDTO request) {
        log.info("商户后台-估清菜品剩余可售数接口入参,request={}", JacksonUtils.writeValueAsString(request));
        return iEstimateService.getEstimateItemResidue(request);
    }


    @ApiOperation(value = "下单校验估清", notes = "客户端下单时验证菜品是否估清，并扣减库存数量")
    @PostMapping("/verify_dinein_item_estimate")
    public EstimateResultRespDTO verifyDineInItemEstimate(@RequestBody @Valid List<DineInItemDTO> request) {
        log.info("验证订单菜品是否估清接口入参,request={}", JacksonUtils.writeValueAsString(request));
        EstimateResultRespDTO result = iEstimateService.verifyDineInItemEstimate(request);
        log.info("verify result = {}", result);
        return result;
    }


    @ApiOperation(value = "退估清", notes = "订单失败、退菜，加回库存")
    @PostMapping("/dinein_fail")
    public Boolean dineinFail(@RequestBody @Valid List<DineInItemDTO> request) {
        log.info("订单支付失败和退菜接口入参,request={}", JacksonUtils.writeValueAsString(request));
        Boolean result = iEstimateService.dineinFail(request);
        return result;
    }

    /**
     * 增加库存
     *
     * @param request
     * @return
     */
    @PostMapping("/inc_stock")
    public Boolean incStock(@RequestBody @Valid List<ItemSkuStockDTO> request) {
        log.info("增加库存,request={}", JacksonUtils.writeValueAsString(request));
        Boolean result = iEstimateService.incStock(request);
        log.info("verify result = {}", result);
        return result;
    }

    /**
     * 减少库存
     *
     * @param request
     * @return
     */
    @PostMapping("/desc_stock")
    public Boolean descStock(@RequestBody @Valid List<ItemSkuStockDTO> request) {
        log.info("减少库存,request={}", JacksonUtils.writeValueAsString(request));
        EstimateResultRespDTO result = iEstimateService.descStock(request);
        log.info("verify result = {}", result);
        return result.getSuccess();
    }

    @PostMapping("/query_estimate_for_synchronize")
//    @RateLimit(limitType = "estimateItem",limitCount = 100)
    public List<ItemEstimateForAndroidRespDTO> queryEstimateForSyn(@RequestBody @Valid BaseDTO baseDTO) {
        log.info("商品估清同步接口入参,baseDTO={}", JacksonUtils.writeValueAsString(baseDTO));
        return iEstimateService.queryEstimateForSyn(baseDTO);
    }


    @ApiOperation(value = "估清定时置满", notes = "job门店估清定时置满")
    @PostMapping("/store_item_estimate_reset")
    public Boolean storeItemEstimateReset(@RequestBody @Valid Map<String, List<String>> request) {
        log.info("job门店定时置满接口入参,request={}", JacksonUtils.writeValueAsString(request));
        Set<String> enterpriseGuidSet = request.keySet();
        enterpriseGuidSet.forEach(guid -> {
            Boolean isExist = cloudEnterpriseFeignClient.hasEnterprise(guid);
            if (isExist != null && !isExist) {
                request.remove(guid);
            }
        });
        if (CollectionUtils.isEmpty(request.keySet())) {
            return Boolean.TRUE;
        }
        return iEstimateService.storeItemEstimateReset(request) > 0 ? Boolean.TRUE : Boolean.FALSE;
    }

    @ApiOperation(value = "通过skuGuids查询估清数据", notes = "通过skuGuids查询估清数据")
    @PostMapping("/query_estimate_by_guids/{storeGuid}")
    public List<ItemEstimateForAndroidDTO> queryEstimateByGuids(@PathVariable("storeGuid") String storeGuid, @RequestBody List<String> skuGuids) {
        log.info("通过sku查询估清数据。门店guid={},skuGuids={}", storeGuid, skuGuids);
        return iEstimateService.queryEstimateByGuids(storeGuid, skuGuids);
    }

    /**
     * 新的一体机估清接口
     * 老估清接口也在使用
     *
     * @param request 入参
     * @return Boolean
     */
    @ApiOperation(value = "一体机设置估清*新")
    @PostMapping("/save_sold_out")
    public Boolean saveSoldOut(@RequestBody @Valid EstimateForAndroidReqDTO request) {
        log.info("一体机手动设置菜品估清状态*新接口 入参,request={}", JacksonUtils.writeValueAsString(request));
        EstimateBO biz = EstimateBizBuilder.build(request);
        return iEstimateService.saveSoldOut(biz);
    }

    /**
     * 一体机估清商品列表
     *
     * @param storeGuid 门店guid
     * @return 一体机估清商品列表
     */
    @ApiOperation(value = "一体机估清商品列表")
    @GetMapping("/list_estimate")
    public EstimateForAndroidRespDTO listEstimate(@RequestParam(value = "storeGuid") String storeGuid) {
        log.info("一体机估清商品列表 入参，request={}", storeGuid);
        return iEstimateService.listEstimate(storeGuid);
    }

    /**
     * 批量取消估清
     *
     * @param request 规格Guid列表
     * @return Boolean
     */
    @ApiOperation(value = "批量取消估清")
    @PostMapping("/batch_cancel_estimate")
    public Boolean batchCancelEstimate(@RequestBody SingleDataDTO request) {
        log.info("批量取消估清 入参,request={}", JacksonUtils.writeValueAsString(request));
        EstimateBO biz = EstimateBizBuilder.build(request);
        return iEstimateService.batchCancelEstimate(biz);
    }

    /**
     * 批量停售
     *
     * @param request 规格Guid列表
     * @return Boolean
     */
    @ApiOperation(value = "批量停售")
    @PostMapping("/batch_stop_sell")
    public Boolean batchStopSell(@RequestBody SingleDataDTO request) {
        log.info("批量停售 入参,request={}", JacksonUtils.writeValueAsString(request));
        EstimateBO biz = EstimateBizBuilder.build(request);
        return iEstimateService.batchStopSell(biz);
    }

    /**
     * 根据商品guid查询估清商品详情
     *
     * @param request 商品Guid
     * @return 估清商品详情
     */
    @ApiOperation(value = "根据商品guid查询估清商品详情")
    @PostMapping("/list_estimate_by_item")
    public EstimateItemRespDTO listEstimateByItem(@RequestBody SingleDataDTO request) {
        log.info("根据商品guid查询估清商品详情 入参,request={}", JacksonUtils.writeValueAsString(request));
        return iEstimateService.listEstimateByItem(request.getData());
    }

    /**
     * 商品估清定时恢复
     *
     * @param request 企业和门店map
     * @return boolean
     */
    @ApiOperation(value = "商品估清定时恢复", notes = "job门店估清定时恢复")
    @PostMapping("/store_item_estimate_cancel")
    public Boolean storeItemEstimateCancel(@RequestBody Map<String, List<String>> request) {
        log.info("job门店估清定时恢复 入参,request={}", JacksonUtils.writeValueAsString(request));
        request.keySet().removeIf(guid -> {
            Boolean isExist = cloudEnterpriseFeignClient.hasEnterprise(guid);
            return isExist != null && !isExist;
        });
        if (CollectionUtils.isEmpty(request.keySet())) {
            return Boolean.TRUE;
        }
        return iEstimateService.storeItemEstimateCancel(request);
    }
}
