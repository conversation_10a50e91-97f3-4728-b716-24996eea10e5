package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.item.constant.Constant;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.entity.bo.AttrGroupListAndAttrListBO;
import com.holderzone.saas.store.item.entity.bo.ItemAttrBO;
import com.holderzone.saas.store.item.entity.bo.ItemAttrGroupBO;
import com.holderzone.saas.store.item.entity.bo.ItemInfoBO;
import com.holderzone.saas.store.item.entity.domain.RAttrItemAttrGroupDO;
import com.holderzone.saas.store.item.entity.domain.RItemAttrGroupDO;
import com.holderzone.saas.store.item.mapper.RItemAttrGroupMapper;
import com.holderzone.saas.store.item.service.IRAttrItemAttrGroupService;
import com.holderzone.saas.store.item.service.IRItemAttrGroupService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.MapStructUtils;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.item.constant.Constant.OP_FAIL;
import static com.holderzone.saas.store.item.constant.Constant.SYSTEM_ERROR;

/**
 * <p>
 * 商品与属性组关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Service
@Slf4j
public class RItemAttrGroupServiceImpl extends ServiceImpl<RItemAttrGroupMapper, RItemAttrGroupDO> implements IRItemAttrGroupService {

    private final IRAttrItemAttrGroupService attrItemAttrGroupService;
    private final DynamicHelper dynamicHelper;
    private final RedisTemplate redisTemplate;

    @Autowired
    public RItemAttrGroupServiceImpl(IRAttrItemAttrGroupService attrItemAttrGroupService, DynamicHelper dynamicHelper, RedisTemplate redisTemplate) {
        this.attrItemAttrGroupService = attrItemAttrGroupService;
        this.dynamicHelper = dynamicHelper;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public List<AttrGroupListAndAttrListBO> selectAttrGroupAndAttrByItemGuidList(List<String> itemGuidList) {
        if (CollectionUtils.isEmpty(itemGuidList)) {
            return new ArrayList<>();
        }
        List<RItemAttrGroupDO> itemAttrGroupDOList = list(
                new LambdaQueryWrapper<RItemAttrGroupDO>()
                        .in(RItemAttrGroupDO::getItemGuid, itemGuidList));
        if (CollectionUtils.isNotEmpty(itemAttrGroupDOList)) {
            // 商品与属性组关联实体GUID集合
            List<String> itemAttrGroupGuidList = itemAttrGroupDOList
                    .stream()
                    .map(RItemAttrGroupDO::getGuid)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(itemAttrGroupGuidList)) {
                return new ArrayList<>();
            }
            // 属性与商品属性组关联实体的关联实体集合
            List<RAttrItemAttrGroupDO> attrItemAttrGroupDOList = attrItemAttrGroupService.list(
                    new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                            .in(RAttrItemAttrGroupDO::getItemAttrGroupGuid, itemAttrGroupGuidList));
            // 按商品分组的属性组的组的map
            Map<String, List<RItemAttrGroupDO>> attrGroupGroupMap = itemAttrGroupDOList
                    .stream()
                    .collect(Collectors.groupingBy(e -> e.getItemGuid()));
            List<AttrGroupListAndAttrListBO> attrGroupListAndAttrListBOS = new ArrayList<>();
            for (String itemGuid : itemGuidList) {
                // 该商品的属性组集合
                List<RItemAttrGroupDO> itemAttrGroupDOS = attrGroupGroupMap.get(itemGuid);
                if (CollectionUtils.isEmpty(itemAttrGroupDOS)) {
                    attrGroupListAndAttrListBOS.add(new AttrGroupListAndAttrListBO(Collections.emptyList(), Collections.emptyList(), itemGuid));
                    continue;
                } else {
                    List<String> thisItemAttrGroupGuidList = itemAttrGroupDOS
                            .stream()
                            .map(RItemAttrGroupDO::getGuid)
                            .collect(Collectors.toList());
                    List<RAttrItemAttrGroupDO> thisItemAttrItemAttrGroupDOList = attrItemAttrGroupDOList.stream()
                            .filter(attrItemAttrGroupDO -> thisItemAttrGroupGuidList.contains(attrItemAttrGroupDO.getItemAttrGroupGuid()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(thisItemAttrItemAttrGroupDOList)) {
                        throw new BusinessException("之前配置商品属性的时候配置错误了，属性组下面没有配置属性");
                    }
                    attrGroupListAndAttrListBOS.add(new AttrGroupListAndAttrListBO(itemAttrGroupDOS, thisItemAttrItemAttrGroupDOList, itemGuid));
                }
            }
            return attrGroupListAndAttrListBOS;

        } else {
            return new ArrayList<>();
        }

    }

    @Override
    @Transactional
    public boolean saveOrUpdateAndDeleteAttrGroupAndAttrRelation(List<ItemInfoBO> itemInfoBOList) {
        if (CollectionUtils.isEmpty(itemInfoBOList)) {
            return false;
        }
        //完善商品更新所需的字段
        fillItemUpdateReqDTOFields(itemInfoBOList);
        // 被更新的商品的GUID集合
        List<String> itemGuidList = itemInfoBOList
                .stream()
                .map(ItemInfoBO::getItemGuid)
                .collect(Collectors.toList());
        // 获取历史关联属性组以及属性
        List<AttrGroupListAndAttrListBO> attrGroupListAndAttrListBOList = selectAttrGroupAndAttrByItemGuidList(itemGuidList);
        // 所有商品的此类
        AttrGroupListAndAttrListBO allItemAttrGroupListAndAttrListBO = mergeAttrGroupListAndAttrListBOList(attrGroupListAndAttrListBOList);

        // 这些商品关联的属性组集合
        List<ItemAttrGroupBO> attrGroupList = new ArrayList<>();
        itemInfoBOList.forEach(itemUpdateReqDTO -> {
            List<ItemAttrGroupBO> thisItemAttrGroupList = itemUpdateReqDTO.getAttrGroupList();
            if (CollectionUtils.isNotEmpty(thisItemAttrGroupList)) {
                attrGroupList.addAll(thisItemAttrGroupList);
            }
        });
        // 入参商品关联了属性组
        if (CollectionUtils.isNotEmpty(attrGroupList)) {
            // 指定商品编辑之前关联了属性组
            if (allItemAttrGroupListAndAttrListBO.hasAttrGroup()) {
                // 之前商品与属性组关联实体
                List<RItemAttrGroupDO> itemAttrGroupDOList = allItemAttrGroupListAndAttrListBO.getItemAttrGroupDOList();
                // 旧的商品与属性组关联实体的主键集合
                List<String> oldItemAttrGroupGuidList = itemAttrGroupDOList
                        .stream()
                        .map(RItemAttrGroupDO::getGuid)
                        .collect(Collectors.toList());
                // 新的商品与属性组关联实体的主键集合
                List<String> newItemAttrGroupGuidList = attrGroupList
                        .stream()
                        .map(ItemAttrGroupBO::getItemAttrGroupGuid)
                        .collect(Collectors.toList());
                // 用于删除的商品属性组关联集合
                List<String> removeOldItemAttrGroupGuidList = new ArrayList<>(oldItemAttrGroupGuidList);
                boolean hasUpdateDO = removeOldItemAttrGroupGuidList.removeAll(newItemAttrGroupGuidList);
                if (CollectionUtils.isNotEmpty(removeOldItemAttrGroupGuidList)) {
                    // 删除不用的关联实体
                    removeByIds(removeOldItemAttrGroupGuidList);
                }
                // 如果有待更新的关联实体
                if (hasUpdateDO) {
                    List<ItemAttrGroupBO> updateItemAttrGroupList = attrGroupList
                            .stream()
                            .filter(itemAttrGroupReqDTO
                                    -> !StringUtils.isEmpty(itemAttrGroupReqDTO.getItemAttrGroupGuid()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(updateItemAttrGroupList)) {
                        log.error(SYSTEM_ERROR);
                        throw new BusinessException(SYSTEM_ERROR);
                    }
                    List<RItemAttrGroupDO> updateItemAttrGroupDOList = MapStructUtils.INSTANCE.itemAttrGroupBOList2itemAttrGroupDOList(updateItemAttrGroupList);
                    boolean updateBatchById = updateBatchById(updateItemAttrGroupDOList, updateItemAttrGroupDOList.size());
                    if (!updateBatchById) {
                        throw new BusinessException(OP_FAIL);
                    }
                    // 只留下新增的关联关系
                    attrGroupList.removeAll(updateItemAttrGroupList);
                    // 属性组的关联实体GUID集合
                    List<String> attrGroupRelateGuidList = updateItemAttrGroupDOList
                            .stream()
                            .map(RItemAttrGroupDO::getGuid)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(attrGroupRelateGuidList)) {
                        // 属性的关联实体GUID集合
                        List<RAttrItemAttrGroupDO> oldAttrRelateDOList = attrItemAttrGroupService.list(
                                new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                                        .in(RAttrItemAttrGroupDO::getItemAttrGroupGuid, attrGroupRelateGuidList));
                        if (CollectionUtils.isEmpty(oldAttrRelateDOList)) {
                            log.error(SYSTEM_ERROR);
                            throw new BusinessException(SYSTEM_ERROR);
                        }
                        // 旧的属性关联实体GUID集合
                        List<String> oldAttrRelateDOGuidList = oldAttrRelateDOList
                                .stream()
                                .map(RAttrItemAttrGroupDO::getGuid)
                                .collect(Collectors.toList());
                        List<ItemAttrBO> thisTimeAttrList = new ArrayList<>();
                        updateItemAttrGroupList.forEach(itemAttrGroupReqDTO -> {
                            // 关联的属性集合
                            List<ItemAttrBO> attrList = itemAttrGroupReqDTO.getAttrList();
                            thisTimeAttrList.addAll(attrList);
                        });
                        // 此次的属性关联GUID集合
                        List<String> thisTimeAttrRelateGuidList = thisTimeAttrList
                                .stream()
                                .map(ItemAttrBO::getAttrItemAttrGroupGuid)
                                .collect(Collectors.toList());
                        // 用于移除属性的集合
                        List<String> removeAttrGuidList = new ArrayList<>(oldAttrRelateDOGuidList);
                        // 剩下待删除的属性集合
                        removeAttrGuidList.removeAll(thisTimeAttrRelateGuidList);
                        if (!CollectionUtils.isEmpty(removeAttrGuidList)) {
                            attrItemAttrGroupService.removeByIds(removeAttrGuidList);
                        }
                    }
                    // 将要更新的属性关联实体集合
                    List<RAttrItemAttrGroupDO> toUpdateAttrDOList = new ArrayList<>();
                    // 将要新增的属性关联实体集合
                    List<RAttrItemAttrGroupDO> toSaveAttrDOList = new ArrayList<>();
                    updateItemAttrGroupList.forEach(itemAttrGroupReqDTO -> {
                        // 该属性组的属性集合
                        List<ItemAttrBO> attrList = itemAttrGroupReqDTO.getAttrList();
                        attrList.forEach(attr -> {
                            RAttrItemAttrGroupDO attrItemAttrGroupDO = MapStructUtils.INSTANCE.itemAttrBO2attrItemAttrGroupDO(attr);
                            if (!StringUtils.isEmpty(attrItemAttrGroupDO.getGuid())) {
                                toUpdateAttrDOList.add(attrItemAttrGroupDO);
                            } else {
                                // todo 调式 debug 看此处实体是否字段填充完毕
                                try {
                                    attrItemAttrGroupDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, GuidKeyConstant.HSI_R_ATTR_ITEM_ATTR_GROUP)));
                                } catch (IOException e) {
                                    throw new BusinessException(Constant.CREATE_GUID_FAIL);
                                }
                                toSaveAttrDOList.add(attrItemAttrGroupDO);
                            }
                        });
                    });
                    if (CollectionUtils.isNotEmpty(toUpdateAttrDOList)) {
                        boolean updateBatchByIdAttr = attrItemAttrGroupService.updateBatchById(toUpdateAttrDOList, toUpdateAttrDOList.size());
                        if (!updateBatchByIdAttr) {
                            throw new BusinessException(OP_FAIL);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(toSaveAttrDOList)) {
                        attrItemAttrGroupService.saveBatch(toSaveAttrDOList, toSaveAttrDOList.size());
                    }
                }
            }

            // 如果有待新增的关联实体
            if (CollectionUtils.isNotEmpty(attrGroupList)) {
                // 待保存的属性组关联实体
                List<RItemAttrGroupDO> saveItemAttrGroupDOList = new ArrayList<>();
                // 待保存的属性关联实体
                List<RAttrItemAttrGroupDO> saveAttrItemAttrGroupDOList = new ArrayList<>();
                attrGroupList.forEach(itemAttrGroupBO -> {
                    RItemAttrGroupDO itemAttrGroupDO = MapStructUtils.INSTANCE.attrGroupBO2itemAttrGroupDO(itemAttrGroupBO);
                    try {
                        itemAttrGroupDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, GuidKeyConstant.HSI_R_ITEM_ATTR_GROUP)));
                    } catch (IOException e) {
                        throw new BusinessException(Constant.CREATE_GUID_FAIL);
                    }
                    saveItemAttrGroupDOList.add(itemAttrGroupDO);
                    // 属性关联实体
                    List<ItemAttrBO> attrList = itemAttrGroupBO.getAttrList();
                    attrList.forEach(itemAttrBO -> {
                        RAttrItemAttrGroupDO attrItemAttrGroupDO = MapStructUtils.INSTANCE.attrBO2attrItemAttrGroupDO(itemAttrBO);
                        attrItemAttrGroupDO.setItemAttrGroupGuid(itemAttrGroupDO.getGuid());
                        try {
                            attrItemAttrGroupDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, GuidKeyConstant.HSI_R_ATTR_ITEM_ATTR_GROUP)));
                        } catch (IOException e) {
                            throw new BusinessException(Constant.CREATE_GUID_FAIL);
                        }
                        saveAttrItemAttrGroupDOList.add(attrItemAttrGroupDO);
                    });
                });
                saveBatch(saveItemAttrGroupDOList, saveItemAttrGroupDOList.size());
                attrItemAttrGroupService.saveBatch(saveAttrItemAttrGroupDOList);
            }
        } else {
            // 这些商品未关联属性
            List<ItemInfoBO> withoutAttrListItems = itemInfoBOList.stream()
                    .filter(itemUpdateReqDTO -> CollectionUtils.isEmpty(itemUpdateReqDTO.getAttrGroupList()))
                    .collect(Collectors.toList());
            // 不含属性的商品GUID集合
            List<String> withoutAttrItemGuidList = withoutAttrListItems
                    .stream()
                    .map(ItemInfoBO::getItemGuid)
                    .collect(Collectors.toList());
            // 目前不关联属性的AttrGroupListAndAttrListBO实体
            // todo test null情况
            List<AttrGroupListAndAttrListBO> withoutAttrItemList = attrGroupListAndAttrListBOList
                    .stream()
                    .filter(bo
                            -> withoutAttrItemGuidList.contains(bo.getItemGuid()))
                    .collect(Collectors.toList());

            // 所有目前无属性商品的此类
            AttrGroupListAndAttrListBO allWithoutAttrItemAttrGroupListAndAttrListBO = mergeAttrGroupListAndAttrListBOList(withoutAttrItemList);
            // 若该商品之前关联了属性，则删除之前的属性关联关系
            if (allWithoutAttrItemAttrGroupListAndAttrListBO.hasAttrGroup()) {
                List<RItemAttrGroupDO> itemAttrGroupDOList = allWithoutAttrItemAttrGroupListAndAttrListBO.getItemAttrGroupDOList();
                List<String> itemAttrGroupGuidList = itemAttrGroupDOList
                        .stream()
                        .map(RItemAttrGroupDO::getGuid)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(itemAttrGroupGuidList)) {
                    log.error(SYSTEM_ERROR);
                    throw new BusinessException(SYSTEM_ERROR);
                }
                attrItemAttrGroupService.remove(new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                        .in(RAttrItemAttrGroupDO::getItemAttrGroupGuid, itemAttrGroupGuidList));
                removeByIds(itemAttrGroupGuidList);
            }

        }
        return true;
    }

    private void fillItemUpdateReqDTOFields(List<ItemInfoBO> itemInfoBOS) {
        // 门店的历史商品详情数据
        itemInfoBOS.forEach(itemInfoBO -> {
            List<ItemAttrGroupBO> attrGroupList = itemInfoBO.getAttrGroupList();
            if (CollectionUtils.isEmpty(attrGroupList)) {
                itemInfoBO.setHasAttr(0);
            } else {
                attrGroupList.forEach(itemAttrGroupBO -> {
                    itemAttrGroupBO.setItemGuid(itemInfoBO.getItemGuid());
                    List<ItemAttrBO> attrList = itemAttrGroupBO.getAttrList();
                    if (CollectionUtils.isEmpty(attrList)) {
                        throw new ParameterException("商品配置错误");
                    }
                    attrList.forEach(attr -> attr.setItemAttrGroupGuid(itemAttrGroupBO.getItemAttrGroupGuid()));
                    // 是否有默认项
                    boolean withDefault = attrList
                            .stream()
                            .anyMatch(attrBO
                                    -> Integer.valueOf(1).equals(attrBO.getIsDefault()));
                    itemAttrGroupBO.setWithDefault(withDefault ? 1 : 0);
                });
            }
        });
    }

    /**
     * 合并attrGroupListAndAttrListBOList成为一个AttrGroupListAndAttrListBO
     *
     * @param attrGroupListAndAttrListBOList
     * @return
     */
    private AttrGroupListAndAttrListBO mergeAttrGroupListAndAttrListBOList(List<AttrGroupListAndAttrListBO> attrGroupListAndAttrListBOList) {

        if (CollectionUtils.isEmpty(attrGroupListAndAttrListBOList)) {
            return new AttrGroupListAndAttrListBO();
        }
        // 所有的商品关联的属性组关联实体集合
        List<RItemAttrGroupDO> allItemAttrGroupDOList = new ArrayList<>();
        // 所有商品的属性关联的商品属性组关联实体的关联实体集合
        List<RAttrItemAttrGroupDO> attrItemAttrGroupDOList = new ArrayList<>();
        for (AttrGroupListAndAttrListBO attrGroupListAndAttrListBO : attrGroupListAndAttrListBOList) {
            List<RItemAttrGroupDO> itemAttrGroupDOS = attrGroupListAndAttrListBO.getItemAttrGroupDOList();
            if (CollectionUtils.isNotEmpty(itemAttrGroupDOS)) {
                allItemAttrGroupDOList.addAll(itemAttrGroupDOS);
            }
            List<RAttrItemAttrGroupDO> attrItemAttrGroupDOS = attrGroupListAndAttrListBO.getAttrItemAttrGroupDOList();
            if (CollectionUtils.isNotEmpty(attrItemAttrGroupDOS)) {
                attrItemAttrGroupDOList.addAll(attrItemAttrGroupDOS);
            }
        }
        // 所有商品的此类
        return new AttrGroupListAndAttrListBO(allItemAttrGroupDOList, attrItemAttrGroupDOList, null);
    }

}
