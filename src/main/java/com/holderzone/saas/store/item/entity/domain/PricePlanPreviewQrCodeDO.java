package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 菜谱预览二维码记录表
 * @date 2021/6/3 17:34
 */
@Data
@Accessors(chain = true)
@TableName("hsi_price_plan_preview_qr_code")
public class PricePlanPreviewQrCodeDO {

    private Long id;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    private Integer isDelete;

    /**
     * 记录guid
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 方案guid
     */
    private String planGuid;

    /**
     * 二维码guid
     */
    private String qrCodeGuid;

    /**
     * 二维码生成时间戳,10位
     */
    private String qrCodeTime;

    /**
     * 是否过期
     */
    private Boolean isExpire;
}
