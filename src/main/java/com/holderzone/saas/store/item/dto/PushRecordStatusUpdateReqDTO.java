package com.holderzone.saas.store.item.dto;

import lombok.Data;

/**
 * 价格方案推送时更新推送记录入参
 */
@Data
public class PushRecordStatusUpdateReqDTO {

    /**
     * 方案guid
     */
    private String planGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 商品guid
     */
    private String itemGuid;

    /**
     * 更新成什么状态 {@link com.holderzone.saas.store.item.entity.enums.PricePlanPushStatusEnum}
     */
    private Integer status;

    /**
     * 推送后生成的新商品guid
     */
    private String newItemGuid;
}
