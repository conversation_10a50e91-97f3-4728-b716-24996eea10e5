package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemPadCalculateDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.takeaway.ErpMappingType;
import com.holderzone.saas.store.item.dto.UpdateTerminalStatusDTO;
import com.holderzone.saas.store.item.entity.bo.ItemInfoBO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 商品规格表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
public interface ISkuService extends IService<SkuDO> {

    /**
     * 获取导出商品数据
     *
     * @param itemSingleDTO storeGuid
     * @return list
     */
    List<ItemExcelTemplateRespDTO> listDownloadData(ItemSingleDTO itemSingleDTO);

    Integer rack(ItemRackDTO itemRackDTO);

    /**
     * 查询普通模式和菜谱模式共同的商品
     *
     * @param storeGuid
     * @return
     */
    List<MappingRespDTO> mappingAllItems(String storeGuid);

    /**
     * 批量查询普通模式和菜谱模式共同的商品
     *
     * @param storeGuids 门店列表
     * @return
     */
    Map<String, List<MappingRespDTO>> batchMappingAllItems(List<String> storeGuids);

    /**
     * 查询商品对应的菜谱名称
     *
     * @param brandGuid 品牌
     * @param skuDOList 规格
     * @return skuGuid-planItemName
     */
    HashMap<String, String> getPlanNameMap(String brandGuid, List<SkuDO> skuDOList);

    /**
     * 查询菜谱模式商品
     */
    List<MappingRespDTO> mappingRecipeModeItems(String storeGuid);

    List<MappingRespDTO> mapping(String storeGuid);

    /**
     * kds获取商品
     *
     * @param itemSingleDTO 请求参数
     * @return List<MappingRespDTO>
     */
    List<MappingRespDTO> kdsMapping(ItemSingleDTO itemSingleDTO);

    List<SkuTakeawayInfoRespDTO> selectSkuTakeawayInfoRespDTOList(ItemStringListDTO itemStringListDTO);

    List<SkuTakeawayInfoRespDTO> selectSkuTakeawayInfoRespDTOListV2(ItemStringListDTO itemStringListDTO);

    /**
     * 根据商品服务删除的商品下部分规格，来删除相关实体（调用此接口的前提是规格所属商品下仍有未删除规格）
     *
     * @param guidList
     * @return
     */
    Integer deleteSkuByIds(List<String> guidList, boolean judgePlanItem);

    /**
     * 根据商品GUID删除规格及相关实体
     *
     * @param itemGuidList
     * @return
     */
    Integer deleteSkuByItemGuidList(List<String> itemGuidList);


    /**
     * 新增或更新指定商品下的规格，并删除无用规格
     *
     * @param itemInfoBO
     * @return 新增或更新了的规格GUID集合
     */
    List<String> saveOrUpdateAndDeleteSku(ItemInfoBO itemInfoBO);

    /**
     * 根据商品GUID查询其下属规格GUID
     *
     * @param itemGuidList
     * @return
     */
    List<String> listSkuGuidByItemGuid(List<String> itemGuidList);


    void validateSkuCodeRepeat(List<SkuSaveReqDTO> skuList, String brandGuid, String storeGuid, Integer from, Integer flag);

    /**
     * 根据门店sku获取品牌推送的sku_guid
     *
     * @param request
     * @return
     */
    Map<String, String> selectErpSkuGuidBySkuGuid(SingleDataDTO request);

    /**
     * 批量整单折扣
     *
     * @param itemWholeDiscountDTO
     * @return
     */
    Integer wholeDiscount(ItemWholeDiscountDTO itemWholeDiscountDTO);

    /**
     * 更新指定商品下的规格(开放平台专用)
     *
     * @param itemInfoBO
     * @return 新增或更新了的规格GUID集合
     */
    List<String> updateSkuForOpen(ItemInfoBO itemInfoBO);

    /**
     * 删除指定商品下的规格(开放平台专用)
     *
     * @param itemInfoBO
     * @return 新增或更新了的规格GUID集合
     */
    List<String> deleteSkuForOpen(ItemInfoBO itemInfoBO);

    /**
     * 获取最低的销售价格，会员价格，虚拟价格
     *
     * @param itemList
     * @return
     */
    List<SkuDO> selectSearchSkuByItemGUid(List<String> itemList);

    /**
     * 商品搜索
     *
     * @param itemSearchReqDTO
     * @return
     */
    List<ItemSkuSearchRespDTO> search(ItemSkuSearchReqDTO itemSearchReqDTO);

    /**
     * 查询门店下全部的sku
     *
     * @param req
     * @return
     */
    List<SkuInfoRespDTO> all(ItemSkuAllReq req);

    /**
     * 分页查询门店下全部的sku
     *
     * @param req
     * @return
     */
    Page<SkuInfoRespDTO> list(ItemSkuListReqDTO req);

    /**
     * 批量修改sku
     *
     * @param batchUpdateDTO
     * @return
     */
    Boolean batchUpdate(@RequestBody ItemSkuBatchUpdateDTO batchUpdateDTO);

    /**
     * 批量增加销量
     *
     * @param itemSkuMonthlySaleIncDTO
     * @return
     */
    Boolean batchSaleInc(ItemSkuMonthlySaleIncDTO itemSkuMonthlySaleIncDTO);

    /**
     * 通过itemGuid和storeGuid删除Sku
     *
     * @param itemGuid
     * @param storeGuid
     * @return
     * <AUTHOR> chen
     * @version 1.0
     * @since 2020-11-04
     */
    Boolean deleteSkuByItemGuidAndStoreGuid(String itemGuid, String storeGuid);

    /**
     * @param itemGuid
     * @param storeGuid
     * @param skuGuid
     * @param originalSalePrice
     * @param originalMemberPrice
     * @return
     */
    Integer updateOriginPriceStoreGuid(String itemGuid, String storeGuid, String skuGuid, BigDecimal originalSalePrice, BigDecimal originalMemberPrice);

    /**
     * 根据itemGuid集合查询对应的sku
     *
     * @param itemGuidList itemGuid集合
     * @return itemGuid为key，对应的sku集合为value的Map
     */
    Map<String, List<SkuDO>> getSkuGuidMap(List<String> itemGuidList);

    Integer updateTerminalStatus(UpdateTerminalStatusDTO updateTerminalStatusDTO);

    /**
     * 售完下架
     *
     * @param skuGuids 规格Guid集合
     * @return 操作结果
     */
    Boolean sellOutRackItem(List<String> skuGuids);

    /**
     * 通过sku查询parentSku
     *
     * @param skuGuids skuGuid集合
     * @return parentSku及当前sku
     */
    List<SkuInfoRespDTO> findParentSKUS(List<String> skuGuids);

    /**
     * 通过商品guid查询sku 包含已删除的
     *
     * @param itemGuid 商品guid
     * @return sku列表
     */
    List<SkuDO> findByItemGuid(String itemGuid);

    /**
     * 通过sku查询parentItemGuid和parentSkuGuid拼接信息
     *
     * @param skuGuids skuGuid集合
     * @return 拼接信息
     */
    List<String> findParentItemSkus(List<String> skuGuids);

    /**
     * 判断当前是否有不可下单的商品
     * 不可下单：商品下架、商品售罄、库存不足
     *
     * @param orderItemReqDTO 需要检查的规格guid
     * @return 不可下单的商品
     */
    List<ItemPadCalculateDTO> checkOrderPlacementItem(OrderItemReqDTO orderItemReqDTO);

    /**
     * 根据规格guid查询规格信息
     *
     * @param skuGuidList 规格guid列表
     * @return 规格信息列表
     */
    List<SkuInfoRespDTO> listSkuInfo(List<String> skuGuidList);

    List<String> queryNotJoinWechatItem(List<String> skuGuidList);

    /**
     * 查询规格详情列表
     * 如果是套餐的规格，则增加子项的规格详情
     *
     * @param itemStringListDTO 规格guid列表
     * @return 规格详情列表
     */
    List<SkuTakeawayInfoRespDTO> listSkuInfoAndSub(ItemStringListDTO itemStringListDTO);

    /**
     * 根据当前门店销售模式查询商品信息
     */
    List<SkuInfoRespDTO> listSkuInfoByRecipeMode(String storeGuid, List<String> skuGuidList);

    /**
     * 查询外卖绑定的erp商品
     * 菜谱和普通模式都查询
     *
     * @param storeGuid 门店guid
     * @return erp商品
     */
    List<ErpMappingType> queryErpItem(String storeGuid);

    /**
     * 查询所有门店外卖绑定的erp商品
     *
     * @param storeGuids 门店guid列表
     * @return 所有门店外卖绑定的erp商品
     */
    List<StoreItemListRespDTO> queryErpItemByStoreGuids(List<String> storeGuids);

    /**
     * 根据规格guid查询对应商品全名
     * 包含菜谱
     *
     * @param skuGuidList 规格guid列表
     * @return 商品全名
     */
    List<ItemWebRespDTO> listSkuForName(List<String> skuGuidList);

    /**
     * 根据商品guid查询套餐商品信息
     * @param itemGuidList 商品guid
     * @return 套餐商品信息
     */
    List<SkuInfoPkgDTO> listPkgInfoByItemGuid(List<String> itemGuidList);

    List<String> listSkuGuid(List<String> skuGuidList);

    Map<String, String> queryParentSkuGuidBySku(ItemStringListDTO query);

}
