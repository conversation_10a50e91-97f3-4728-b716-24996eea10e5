package com.holderzone.saas.store.item.util;
import com.github.pagehelper.PageInfo;
import com.holderzone.framework.util.Page;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PageDataTransform
 * @date 2019/05/08 16:56
 * @description //TODO   page 对象互转 为了返回各端的page信息一致，统一使用page
 * @program holder-saas-store-item
 */
public class PageDataTransform {

    /**
     *  mybatisPageHelper PageInfo 转 page
     * @param fromPage
     * @return
     */
    public static Page pagehelperToPage(PageInfo fromPage) {
        Page page = new Page();
        page.setCurrentPage(fromPage.getPageNum());
        page.setData(fromPage.getList());
        page.setPageSize(fromPage.getPageSize());
        page.setTotalCount(fromPage.getTotal());
        return  page ;
    }

}
