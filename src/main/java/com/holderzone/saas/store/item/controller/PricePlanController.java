package com.holderzone.saas.store.item.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.member.merchant.dto.org.RequestProductInfo;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.req.price.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.price.ItemSkuAndPlanPriceDTO;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceAllItemSkuRespDTO;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceAllTypeRespDTO;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.deal.PricePlanChangeRequestDTO;
import com.holderzone.saas.store.dto.weixin.deal.PricePlanChangeResponseDTO;
import com.holderzone.saas.store.item.dto.SyncStoreAndItemDTO;
import com.holderzone.saas.store.item.service.*;
import com.holderzone.saas.store.item.util.DynamicHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/plan")
@Api(value = "价格方案接口")
@AllArgsConstructor
public class PricePlanController {

    private final IPricePlanService pricePlanService;

    private final IPricePlanItemService pricePlanItemService;

    private final IPricePlanStoreService pricePlanStoreService;

    private final IItemService itemService;

    private final IPricePlanPreviewQrCodeService planPreviewQrCodeService;

    private final DynamicHelper dynamicHelper;

    @ApiOperation(value = "会员优惠券活动查询菜谱下的菜品")
    @PostMapping("/memberPricePlanItemByGuidList")
    public TypeSkuPricePlanRespDTO memberPricePlanItemByGuidList(@RequestBody RequestProductInfo request) {
        log.info("memberPricePlanItemByGuidList：{}", JacksonUtils.writeValueAsString(request));
        return pricePlanService.memberPricePlanItemByGuidList(request);
    }

    @ApiOperation(value = "会员优惠券活动查询菜谱下的菜品")
    @PostMapping("/memberPricePlanItem")
    public TypeSkuPricePlanRespDTO memberPricePlanItem(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("memberPricePlanItem：{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return pricePlanService.memberPricePlanItem(itemSingleDTO);
    }

    @ApiOperation(value = "查询扫码点餐菜谱变动情况")
    @PostMapping("/pricePlanChangeInfo")
    public PricePlanChangeResponseDTO pricePlanChangeInfo(@RequestBody PricePlanChangeRequestDTO pricePlanChangeRequestDTO) {
        log.info("pricePlanChangeInfo：{}", JacksonUtils.writeValueAsString(pricePlanChangeRequestDTO));
        return pricePlanService.pricePlanChangeInfo(pricePlanChangeRequestDTO);
    }

    @ApiOperation(value = "商品销售模式变更")
    @GetMapping("/changeSaleModel")
    public void changeSaleModel(@RequestParam String brandGuid, @RequestParam Integer salesModel) {
        log.info("changeSaleModel入参：{},{}", brandGuid, salesModel);
        pricePlanService.changeSaleModel(brandGuid, salesModel);
    }

    @ApiOperation(value = "一体机预点餐")
    @PostMapping("/preOrderVerification")
    public PreOrderValidateRespDTO preOrderVerification(@RequestBody PreOrderValidateReq preOrderValidateReq) {
        log.info("一体机预点餐验证入参：{}", JacksonUtils.writeValueAsString(preOrderValidateReq));
        return pricePlanService.preOrderVerification(preOrderValidateReq);
    }

    @ApiOperation(value = "价格方案保存")
    @PostMapping("/save")
    @Transactional(rollbackFor = Exception.class)
    public String savePlan(@RequestBody PricePlanReqDTO reqDTO) {
        log.info("价格方案保存操作信息：{}", JSON.toJSONString(UserContextUtils.get()));
        log.info("价格方案保存入参：{}", JSON.toJSONString(reqDTO));
        return pricePlanService.savePlan(reqDTO);
    }

    @ApiOperation(value = "菜谱方案草稿删除")
    @PostMapping("/delete_plan_draft")
    public Boolean deletePlanDraft(@RequestBody PricePlanDraftReqDTO reqDTO) {
        log.info("菜谱方案草稿删除入参：{}", JSON.toJSONString(reqDTO));
        return pricePlanService.deletePlanDraft(reqDTO);
    }

    @ApiOperation(value = "方案列表查询")
    @PostMapping("/list")
    public Page<PricePlanRespDTO> planList(@RequestBody PricePlanPageReqDTO reqDTO) {
        log.info("价格方案列表查询入参：{}", JSON.toJSONString(reqDTO));
        return pricePlanService.planList(reqDTO);
    }

    @ApiOperation(value = "方案编辑查询")
    @GetMapping("/getOne")
    public PricePlanRespDTO getPlan(@RequestParam("planGuid") String planGuid) {
        log.info("价格方案编辑查询入参：{}", planGuid);
        return pricePlanService.getPlan(planGuid);
    }

    /**
     * 获取品牌商品分规格列表（价格方案导入商品时使用）
     *
     * @param request ItemTemplateMenuAllSubItemReqDTO
     * @return Page<ItemTemplateSubItemRespDTO>
     */
    @ApiOperation(value = "方案可导入菜品查询")
    @PostMapping("/get_brand_item_list")
    public Page<PricePlanItemAddQueryRespDTO> selectBrandSkuItemList(@RequestBody PricePlanItemAddQueryReqDTO request) {
        log.info("方案可导入菜品查询入参,request={}", JSON.toJSONString(request));
        return pricePlanItemService.selectBrandSkuItemList(request);
    }

    @ApiOperation(value = "方案菜品导入时保存")
    @PostMapping("/saveItem")
    public Boolean savePlanItem(@RequestBody PricePlanItemAddReqDTO reqDTO) {
        log.info("价格方案菜品保存入参：{}", JSON.toJSONString(reqDTO));
        return pricePlanItemService.savePlanItem(reqDTO);
    }

    @ApiOperation(value = "方案菜品导入后查询")
    @PostMapping("/itemList")
    public Page<PricePlanItemPageRespDTO> itemList(@RequestBody PricePlanItemPageReqDTO reqDTO) {
        log.info("价格方案菜品查询入参：{}", JSON.toJSONString(reqDTO));
        return pricePlanItemService.itemList(reqDTO);
    }

    @ApiOperation(value = "方案菜品移除")
    @PostMapping("/removeItems")
    public Boolean removeItems(@RequestBody PricePlanItemRemoveReqDTO reqDTO) {
        log.info("价格方案菜品移除入参：{}", JSON.toJSONString(reqDTO));
        if (CollectionUtils.isEmpty(reqDTO.getItemGuidList())) {
            throw new BusinessException("菜品不能为空");
        }
        return pricePlanItemService.removeItems(reqDTO);
    }

    @ApiOperation(value = "方案菜品更新")
    @PostMapping("/updateItems")
    public Boolean updateItems(@RequestBody PricePlanItemUpdateReqDTO reqDTO) {
        log.info("价格方案菜品更新入参：{}", JSON.toJSONString(reqDTO));
        return pricePlanItemService.updateItems(reqDTO);
    }

    @ApiOperation(value = "方案推送")
    @PostMapping("/push")
    public Boolean pushPlan(@RequestBody PricePlanPushReqDTO reqDTO) {
        log.info("价格方案推送入参：{}", JSON.toJSONString(reqDTO));
        return false;
    }

    @ApiOperation(value = "方案删除")
    @GetMapping("/delete")
    public Boolean deletePlan(@RequestParam("planGuid") String planGuid) {
        log.info("价格方案删除入参：{}", planGuid);
        return pricePlanService.deletePlan(planGuid);
    }

    /**
     * 永久停用方案
     *
     * @param planGuid 方案id
     * @return Boolean
     */
    @ApiOperation(value = "永久停用方案")
    @GetMapping("/permanently_deactivate")
    public Boolean permanentlyDeactivate(@RequestParam("planGuid") String planGuid) {
        log.info("方案永久停用 入参：{}", planGuid);
        return pricePlanService.permanentlyDeactivate(planGuid);
    }

    @ApiOperation(value = "门店控制列表")
    @PostMapping("/store_control_list")
    public List<PricePlanStoreRespDTO> storeControlList(@RequestBody PricePlanStoreReqDTO reqDTO) {
        log.info("门店控制列表入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return pricePlanStoreService.storeControlList(reqDTO);
    }

    @ApiOperation(value = "绑定门店与方案关系")
    @PostMapping("/store_plan_bind")
    public Boolean bingPlanStore(@RequestBody PricePlanStoreReqDTO reqDTO) {
        log.info("绑定门店与方案关系入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return pricePlanStoreService.bingPlanStore(reqDTO);
    }

    @ApiOperation(value = "根据门店guid删除方案绑定门店")
    @GetMapping("/delete_plan_store_by_store_guid")
    public Boolean deletePlanStoreByStoreGuid(@RequestParam("storeGuid") String storeGuid) {
        log.info("删除方案绑定关系，门店guid：{}", storeGuid);
        return pricePlanStoreService.deletePlanStoreByStoreGuid(storeGuid);
    }

    @ApiOperation(value = "根据门店guid查询方案绑定门店")
    @GetMapping("/get_plan_store_by_store_guid")
    public List<PricePlanBingStoreRespDTO> getPlanStoreByStoreGuid(@RequestParam("storeGuid") String storeGuid) {
        log.info("查询方案绑定关系，门店guid：{}", storeGuid);
        return pricePlanStoreService.getPlanStoreByStoreGuid(storeGuid);
    }

    @ApiOperation(value = "手动推送测试")
    @GetMapping("/push")
    public Integer push(@RequestParam("pricePlanGuid") String pricePlanGuid) {
        log.info("查询方案绑定关系，菜谱guid：{}", pricePlanGuid);
        SyncStoreAndItemDTO storeAndItemDTO = pricePlanItemService.getStoreAndItem(pricePlanGuid);
        return itemService.pushItemsByPrice(storeAndItemDTO);
    }

    @ApiOperation(value = "查询价格方案分类列表", notes = "查询品牌/门店分类列表")
    @GetMapping("/query_type_by_price_plan_guid")
    public List<TypeWebRespDTO> queryTypeByPricePlanGuid(@RequestParam String pricePlanGuid) {
        log.info("查询价格方案分类列表入参,pricePlanGuid={}", pricePlanGuid);
        return pricePlanItemService.queryTypeByPricePlanGuid(pricePlanGuid);
    }

    @ApiOperation(value = "价格方案草稿查询")
    @GetMapping("/get_price_plan_cache_data")
    public PricePlanReqDTO getPricePlanCacheData(@RequestParam("userGuid") String userGuid,
                                                 @RequestParam("brandGuid") String brandGuid) {
        log.info("价格方案草稿查询,userGuid={},brandGuid={}", userGuid, brandGuid);
        return pricePlanService.getPricePlanCacheData(userGuid, brandGuid);
    }

    @ApiOperation(value = "门店绑定菜谱方案校验")
    @PostMapping("/check_store_price_plan_rule")
    public List<PricePlanStoreCheckRespDTO> checkStorePricePlanRule(@RequestBody PricePlanReqDTO reqDTO) {
        log.info("门店绑定菜谱方案校验入参：{}", JSON.toJSONString(reqDTO));
        return pricePlanStoreService.checkStorePricePlanRule(reqDTO);
    }

    @ApiOperation(value = "商品批量设置上下架")
    @PostMapping("/batch_operating_item")
    public Boolean batchOperatingItem(@RequestBody PricePlanItemReqDTO reqDTO) {
        log.info("商品批量设置上下架入参：{}", JSON.toJSONString(reqDTO));
        return pricePlanItemService.batchOperatingItem(reqDTO);
    }

    /**
     * 通过门店guid查询所有菜谱方案
     *
     * @param storeGuid 门店guid
     * @return 所有菜谱方案
     */
    @ApiOperation(value = "通过门店guid查询所有菜谱方案")
    @GetMapping("/query_plans_by_store_guid")
    public List<PricePlanRespDTO> queryPlansByStoreGuid(@RequestParam("storeGuid") String storeGuid) {
        log.info("通过门店guid查询所有菜谱方案入参：{}", storeGuid);
        return pricePlanService.queryPlansByStoreGuid(storeGuid);
    }

    /**
     * 根据菜谱方案查询菜谱方案绑定菜品
     *
     * @param itemQueryReqDTO ItemQueryReqDTO
     * @return 商品列表
     */
    @ApiOperation(value = "根据菜谱方案查询菜谱方案绑定菜品")
    @PostMapping("/query_plan_items_by_plan")
    public Page<ItemWebRespDTO> queryPlanItemsByPlan(@RequestBody ItemQueryReqDTO itemQueryReqDTO) {
        log.info("根据菜谱方案查询菜谱方案绑定菜品入参：{}", JacksonUtils.writeValueAsString(itemQueryReqDTO));
        return pricePlanItemService.queryPlanItemsByPlan(itemQueryReqDTO);
    }

    @ApiOperation(value = "校验价格方案分类是否可以删除", notes = "校验价格方案分类是否可以删除")
    @PostMapping("/check_type_price_plan")
    public Boolean checkTypePricePlan(@RequestBody ItemQueryReqDTO itemQueryReqDTO) {
        log.info("校验价格方案分类是否可以删除入参,typeGuid={}", JacksonUtils.writeValueAsString(itemQueryReqDTO));
        return pricePlanItemService.checkTypePricePlan(itemQueryReqDTO);
    }

    @ApiOperation(value = "菜谱方案可导入菜品查询")
    @PostMapping("/find_price_plan_item_list")
    public List<TypeItemRespDTO> findPricePlanItemList(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("菜谱方案可导入菜品查询入参,request={}", JSON.toJSONString(itemSingleDTO));
        return pricePlanItemService.findPricePlanItemList(itemSingleDTO);
    }

    /**
     * 根据方案guid查询绑定门店信息
     *
     * @param itemSingleDTO 菜谱方案Guid
     * @return List<StoreDTO>
     */
    @ApiOperation(value = "根据方案guid查询绑定门店信息")
    @PostMapping("/query_plan_store_list_by_plan")
    public List<StoreDTO> queryPlanStoreListByPlan(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("根据方案guid查询绑定门店信息 入参,request={}", JSON.toJSONString(itemSingleDTO));
        return pricePlanItemService.queryPlanStoreListByPlan(itemSingleDTO);
    }

    /**
     * 通过门店guid查询所属菜谱方案
     *
     * @param storeGuid 门店guid
     * @return 所属菜谱方案，如果没有则返回空list
     */
    @ApiOperation(value = "通过门店guid查询所属菜谱方案")
    @GetMapping("/query_belong_plan_by_store_guid")
    public List<PricePlanRespDTO> queryBelongPlansByStoreGuid(@RequestParam("storeGuid") String storeGuid) {
        log.info("通过门店guid查询所属菜谱方案 入参：{}", storeGuid);
        return pricePlanStoreService.queryBelongPlansByStoreGuid(storeGuid);
    }

    /**
     * 通过菜谱guid预览菜谱
     *
     * @param reqDTO 菜谱guid,浏览模式
     * @return 预览的菜谱方案信息
     */
    @ApiOperation(value = "通过菜谱guid预览菜谱")
    @PostMapping("/preview_plan_by_guid")
    public PreviewPlanRespDTO previewPlansByGuid(@RequestBody PreviewPlanReqDTO reqDTO) {
        log.info("通过菜谱guid预览菜谱 入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        // 手动切库
        if (!ObjectUtils.isEmpty(reqDTO.getEnterpriseGuid())) {
            dynamicHelper.changeDatasource(reqDTO.getEnterpriseGuid());
            UserContextUtils.putErp(reqDTO.getEnterpriseGuid());
        }
        return pricePlanItemService.previewPlanByGuid(reqDTO);
    }

    /**
     * 二维码过期校验
     *
     * @param reqDTO 二维码id和方案id
     * @return 该二维码是否过期，过期时间为3天
     */
    @ApiOperation(value = "二维码过期校验，过期时间为3天")
    @PostMapping("/check_qr_code")
    public CheckQrCodeRespDTO checkQrCode(@RequestBody CheckQrCodeDTO reqDTO) {
        log.info("二维码过期校验 入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        // 手动切库
        if (!ObjectUtils.isEmpty(reqDTO.getEnterpriseGuid())) {
            dynamicHelper.changeDatasource(reqDTO.getEnterpriseGuid());
            UserContextUtils.putErp(reqDTO.getEnterpriseGuid());
        }
        return planPreviewQrCodeService.checkQrCode(reqDTO);
    }

    @ApiOperation(value = "菜谱复制")
    @GetMapping("/copy_plan")
    public Boolean copyPlan(String planGuid) {
        log.info("菜谱复制入参：{}，", planGuid);
        return pricePlanService.copyPlan(planGuid);
    }

    /**
     * 批量编辑方案商品的商品列表
     * v1.18.5 版本优化：取消分页，增加分类
     * 查询所有为已启用、即将启用、暂不启用菜谱的所有商品（去重）
     *
     * @param req 请求参数
     * @return 商品列表
     */
    @ApiOperation(value = "批量编辑方案商品的商品列表")
    @PostMapping("/list_all_plan_price_item")
    public List<PlanPriceAllTypeRespDTO> listAllPlanPriceItem(@RequestBody @Validated PlanPriceAllItemReqDTO req) {
        log.info("批量编辑方案商品的商品列表 req={}", JacksonUtils.writeValueAsString(req));
        return pricePlanItemService.listAllPlanPriceItem(req);
    }

    @ApiOperation(value = "编辑商品得商品规格信息和价格方案列表信息")
    @GetMapping("/list_item_sku_and_plan_price")
    public ItemSkuAndPlanPriceDTO listItemSkuAndPlanPrice(@RequestParam("itemGuid") String itemGuid) {
        log.info("批量编辑菜谱入参：{}，", itemGuid);
        return pricePlanItemService.listItemSkuAndPlanPrice(itemGuid);
    }

    @ApiOperation(value = "批量编辑保存商品菜谱信息")
    @PostMapping("/save_batch_edit_plan_price")
    public void saveBatchEditPlanPrice(@RequestBody PlanPriceEditReqDTO req) {
        log.info("批量编辑保存菜谱操作信息：{}", JSON.toJSONString(UserContextUtils.get()));
        log.info("批量编辑保存商品菜谱信息 req={}", JacksonUtils.writeValueAsString(req));
        pricePlanService.saveBatchEditPlanPrice(req);
    }

    @ApiOperation(value = "批量新增保存商品菜谱信息")
    @PostMapping("/save_batch_add_plan_price")
    public void saveBatchAddPlanPrice(@RequestBody PlanPriceAddReqDTO req) {
        log.info("批量新增保存菜谱操作信息：{}", JSON.toJSONString(UserContextUtils.get()));
        log.info("批量新增保存商品菜谱信息 req={}", JacksonUtils.writeValueAsString(req));
        pricePlanService.saveBatchAddPlanPrice(req);
    }

    @ApiOperation(value = "查询可用的价格方案")
    @PostMapping("/list_available_plan_price")
    public List<PlanPriceEditDTO> listAvailablePlanPrice(@RequestBody PlanPriceAvailableReqDTO req) {
        log.info("查询可用的价格方案 req={}", JacksonUtils.writeValueAsString(req));
        return pricePlanService.listAvailablePlanPrice(req);
    }

    @ApiOperation(value = "查询价格方案可用的商品分类")
    @GetMapping("/list_available_plan_item_type")
    public List<ItemTypeRespDTO> listAvailablePlanItemType(@RequestParam("planGuid") String planGuid) {
        log.info("查询价格方案可用的商品分类 planGuid={}", planGuid);
        return pricePlanService.listAvailablePlanItemType(planGuid);
    }

    /**
     * 批量下架的商品列表
     * 查询所有为已启用、即将启用、暂不启用菜谱的所有商品规格（去重）
     *
     * @param req 品牌必传
     * @return 商品列表
     */
    @ApiOperation(value = "批量下架商品列表")
    @PostMapping("/list_all_plan_price_item_sku")
    public Page<PlanPriceAllItemSkuRespDTO> listAllPlanPriceItemSku(@RequestBody @Validated PlanPriceAllItemSkuReqDTO req) {
        log.info("批量下架商品列表 req={}，", JacksonUtils.writeValueAsString(req));
        return pricePlanItemService.listAllPlanPriceItemSku(req);
    }

    /**
     * 批量下架保存商品菜谱信息
     *
     * @param request 批量下架保存入参实体
     */
    @ApiOperation(value = "批量下架保存商品菜谱信息")
    @PostMapping("/save_batch_sold_out_plan_item")
    public Boolean saveBatchSoldOutPlanItem(@RequestBody PlanPriceSoldOutReqDTO request) {
        log.info("批量下架保存菜谱操作信息：{}", JSON.toJSONString(UserContextUtils.get()));
        log.info("批量下架保存商品菜谱信息 request={}", JacksonUtils.writeValueAsString(request));
        return pricePlanService.saveBatchSoldOutPlanItem(request);
    }

    @ApiOperation(value = "方案延时补偿")
    @PostMapping("/delayed_compensate")
    public void delayedCompensate(@RequestBody List<String> enterpriseGuidList) {
        log.info("方案延时补偿 enterpriseGuidList={}", enterpriseGuidList);
        pricePlanService.delayedCompensate(enterpriseGuidList);
    }


    /**
     * 批量编辑方案分类的分类列表
     * 查询所有为已启用、即将启用、暂不启用菜谱的所有分类 按名称去重
     */
    @ApiOperation(value = "批量编辑方案分类的分类列表")
    @PostMapping("/list_all_plan_price_item_type")
    public List<TypeRespDTO> listAllPlanPriceItemType(@RequestBody @Validated PlanPriceAllItemReqDTO req) {
        log.info("批量编辑方案分类的分类列表 req={}", JacksonUtils.writeValueAsString(req));
        return pricePlanItemService.listAllPlanPriceItemType(req);
    }

    @ApiOperation(value = "批量编辑方案分类的分类列表 查询分类名称所存在的菜谱")
    @GetMapping("/list_item_type_and_plan_price")
    public List<PlanPriceEditDTO> listItemTypeAndPlanPrice(@RequestParam("typeName") String typeName) {
        log.info("查询分类名称所存在的菜谱入参：{}，", typeName);
        return pricePlanItemService.listItemTypeAndPlanPrice(typeName);
    }

    @ApiOperation(value = "批量编辑保存菜谱分类信息")
    @PostMapping("/save_batch_edit_plan_price_type")
    public void saveBatchEditPlanPriceType(@RequestBody PlanPriceEditReqDTO req) {
        log.info("批量编辑保存菜谱分类信息：{}", JacksonUtils.writeValueAsString(UserContextUtils.get()));
        log.info("批量编辑保存菜谱分类信息 req={}", JacksonUtils.writeValueAsString(req));
        pricePlanService.saveBatchEditPlanPriceType(req);
    }
}
