package com.holderzone.saas.store.item.controller;

import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.item.service.IDefaultDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/default_data")
@Api(value = "默认数据接口")
@Deprecated
public class DefaultDataController {

    private final IDefaultDataService defaultDataService;

    @Autowired
    public DefaultDataController(IDefaultDataService defaultDataService) {
        this.defaultDataService = defaultDataService;
    }

    @ApiOperation(value = "初始化默认属性、属性组", notes = "初始化默认属性、属性组 data:品牌guid")
    @PostMapping("/add_data")
    public void addDefaultData(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("创建品牌[{}]时添加默认属性、属性组;", itemSingleDTO.getData());
        defaultDataService.addAttr(itemSingleDTO);
    }
}
