package com.holderzone.saas.store.item.helper;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidDTO;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.PricePlanNowDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.enums.item.ModuleEntranceEnum;
import com.holderzone.saas.store.enums.item.WeekEnum;
import com.holderzone.saas.store.item.config.LocalCacheConfig;
import com.holderzone.saas.store.item.constant.Constant;
import com.holderzone.saas.store.item.entity.bo.ItemRelateGroupMealBO;
import com.holderzone.saas.store.item.entity.domain.*;
import com.holderzone.saas.store.item.entity.enums.ItemStateEnum;
import com.holderzone.saas.store.item.entity.query.ItemTemplateExecuteTimeQuery;
import com.holderzone.saas.store.item.entity.query.SetMealEstimateQuery;
import com.holderzone.saas.store.item.mapper.EstimateMapper;
import com.holderzone.saas.store.item.mapper.PricePlanItemMapper;
import com.holderzone.saas.store.item.service.*;
import com.holderzone.saas.store.item.service.rpc.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.item.constant.Constant.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemPushHelper
 * @date 2019/02/27 下午3:36
 * @description //商品相关辅助类
 * @program holder-saas-store-item
 */
@Slf4j
@Component
public class ItemHelper {
    @Autowired
    private IItemService itemService;
    @Autowired
    private IItemTMenuValidityService iItemTMenuValidityService;
    @Autowired
    private IEstimateService iEstimateService;
    @Autowired
    private BaseService baseService;
    @Autowired
    private EventPushHelper eventPushHelper;
    @Autowired
    private IRSkuSubgroupService skuSubgroupService;
    @Autowired
    private ISubgroupService subgroupService;
    @Autowired
    private IGroupMealService groupMealService;

    @Autowired
    private EstimateMapper estimateMapper;

    @Autowired
    private PricePlanItemMapper planItemMapper;

    /**
     * 调用推送商品方法,分配售卖门店
     *
     * @param itemStringListDTO 商品信息
     * @param skuGuidList       sku集合
     */
    public Integer distributeItems2Stores(ItemStringListDTO itemStringListDTO, List<String> skuGuidList, boolean isUpdateSubItem) {
        // 推送给售卖门店
        List<String> storeGuidList = itemStringListDTO.getDataList();
        if (CollectionUtils.isEmpty(skuGuidList) || CollectionUtils.isEmpty(storeGuidList)) {
            return 0;
        }
        PushItemReqDTO pushItemReqDTO = new PushItemReqDTO();
        pushItemReqDTO.setStoreGuidList(storeGuidList);
        pushItemReqDTO.setSkuGuidList(skuGuidList);
        pushItemReqDTO.setIsUpdateSubItem(isUpdateSubItem);
        return itemService.pushItems(pushItemReqDTO, true);
    }

    public int sort(Integer from, String brandGuid, String storeGuid) {
        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(from);
        if (ModuleEntranceEnum.BRAND.code() == from) {
            itemSingleDTO.setData(brandGuid);
        } else {
            itemSingleDTO.setData(storeGuid);
        }
        return itemService.getSort(itemSingleDTO);
    }

    /**
     * 根据品牌或者门店查询最后一个商品的sort并
     *
     * @param from      来源
     * @param brandGuid 品牌
     * @param storeGuid 门店
     * @return 品牌或者门店查询最后一个商品的sort并+1
     */
    public int maxSort(Integer from, String brandGuid, String storeGuid) {
        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(from);
        if (ModuleEntranceEnum.BRAND.code() == from) {
            itemSingleDTO.setData(brandGuid);
        } else {
            itemSingleDTO.setData(storeGuid);
        }
        return itemService.getMaxSort(itemSingleDTO);
    }

    /**
     * 校验组织GUID是否必填
     *
     * @param from
     * @param brandGuid
     * @param storeGuid
     */
    public void validateOrganizeGuid(Integer from, String brandGuid, String storeGuid) {
        if (from == 0 && StringUtils.isEmpty(storeGuid)) {
            throw new ParameterException(STORE_CANNOT_BE_NULL);
        }
        if (from == 1 && StringUtils.isEmpty(brandGuid)) {
            throw new ParameterException(BRAND_CANNOT_BE_NULL);
        }

        validateFrom(from);
    }

    /**
     * 校验from字段
     *
     * @param from
     */
    public void validateFrom(Integer from) {
        if (from != 0 && from != 1 && from != 3 && from != 4) {
            throw new ParameterException("来源入参错误");
        }
    }

    public void validateItemStringListDTO(ItemStringListDTO itemStringListDTO) {
        if (CollectionUtils.isEmpty(itemStringListDTO.getDataList())) {
            throw new ParameterException(WRONG_PARAMS);
        }
    }

    public void deletePicUrl(String itemGuid) {
        List<ItemDO> itemDOS = (ArrayList) itemService.listByIds(Arrays.asList(itemGuid));
        ItemDO before = itemDOS.get(0);
        String pictureUrl = before.getPictureUrl();
        if (!StringUtils.isEmpty(pictureUrl)) {
            baseService.delete(pictureUrl);
        }
    }

    /**
     * 传入订单 返回需检验商品的SKU和数量
     *
     * @param list
     * @return
     */
    public Map<String, BigDecimal> itemEstimateHander(List<DineInItemDTO> list) {
        Map<String, BigDecimal> result = new HashMap<>();
        for (DineInItemDTO a : list) {
            result = getDineInNumber(a, result);
        }
        return result;
    }

    //传入一个菜 ,计算其赠菜
    public Map<String, BigDecimal> getDineInNumber(DineInItemDTO dineInItemDTO, Map<String, BigDecimal> map) {
        //判断是否有赠菜
        if (Optional.ofNullable(dineInItemDTO.getFreeItemDTOS()).isPresent() && !dineInItemDTO.getFreeItemDTOS().isEmpty()) {
            //获取到赠菜
            List<FreeItemDTO> freeItemDTOS = dineInItemDTO.getFreeItemDTOS();
            //计算赠送数量
            BigDecimal total = freeItemDTOS.stream().map(FreeItemDTO::getCount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //加上赠送数量
            dineInItemDTO.setCurrentCount(dineInItemDTO.getCurrentCount().add(total));
        }
        //判断是否是套餐
        if (Optional.ofNullable(dineInItemDTO.getPackageSubgroupDTOS()).isPresent() && !dineInItemDTO.getPackageSubgroupDTOS().isEmpty()) {
            List<PackageSubgroupDTO> packageSubgroupDTOS = dineInItemDTO.getPackageSubgroupDTOS();
            //套餐主项sku存在
            if (map.containsKey(dineInItemDTO.getSkuGuid())) {
                BigDecimal count = map.get(dineInItemDTO.getSkuGuid());
                count = count.add(dineInItemDTO.getCurrentCount());
                map.put(dineInItemDTO.getSkuGuid(), count);
            } else { //套餐主项sku不存在
                map.put(dineInItemDTO.getSkuGuid(), dineInItemDTO.getCurrentCount());
            }
            for (PackageSubgroupDTO s : packageSubgroupDTOS) {
                if (Optional.ofNullable(s.getSubDineInItemDTOS()).isPresent()) {
                    List<SubDineInItemDTO> subDineInItemDTOS = s.getSubDineInItemDTOS();
                    subDineInItemDTOS.forEach(a -> {
                        //map已经存在相同sku
                        if (map.containsKey(a.getSkuGuid())) {
                            BigDecimal count = map.get(a.getSkuGuid());
                            count = count.add(a.getCurrentCount().multiply(a.getPackageDefaultCount()).multiply(dineInItemDTO.getCurrentCount()));
                            map.put(a.getSkuGuid(), count);
                        } else { //不存在
                            map.put(a.getSkuGuid(), a.getCurrentCount().multiply(a.getPackageDefaultCount()).multiply(dineInItemDTO.getCurrentCount()));
                        }
                    });
                }
            }
        } else { //单品、称重
            if (map.containsKey(dineInItemDTO.getSkuGuid())) {
                BigDecimal count = map.get(dineInItemDTO.getSkuGuid());
                count = count.add(dineInItemDTO.getCurrentCount());
                map.put(dineInItemDTO.getSkuGuid(), count);
            } else { //不存在
                map.put(dineInItemDTO.getSkuGuid(), dineInItemDTO.getCurrentCount());
            }
        }
        return map;
    }

    /**
     * 传入菜单时间段集合，商品模板guid ，判断时间是否合法
     *
     * @param templateGuid                   模板guid
     * @param itmeTemplateMenuValidityReqDTO 菜单执行时间
     * @return true：合法  false：不合法
     */
    public String validateMenuTime(Integer periodicMode, String templateGuid,
                                   ItmeTemplateMenuValidityReqDTO itmeTemplateMenuValidityReqDTO, String menuGuid) {
        String result = null;
        List<ItemTMenuValidityDO> itemTMenuValidityDOS = iItemTMenuValidityService.getMenuValiditys(templateGuid, periodicMode, menuGuid);
        if (ObjectUtils.isEmpty(itemTMenuValidityDOS) || itemTMenuValidityDOS.isEmpty()) {
            return result;
        }
        //按星期
        if (periodicMode.equals(2)) {
            Integer number = itemTMenuValidityDOS.stream().map(ItemTMenuValidityDO::getWeeksQuantum).collect(Collectors.toList()).stream().reduce(Integer::sum).orElse(0);
            int maxSize = 7 - number;
            if (maxSize == 0) {
                throw new BusinessException("当前已有星期已达到最大限制");
            }
            if (maxSize < itmeTemplateMenuValidityReqDTO.getWeeks().size()) {
                throw new BusinessException("星期最大还可选择" + maxSize + "个");
            }
            result = validateMenuTimeWeekHander(itemTMenuValidityDOS, itmeTemplateMenuValidityReqDTO.getWeeks());
        }
        //按时间段
        else if (periodicMode.equals(1)) {
            List<ItemTMenuValidityDO> listTimes = itemTMenuValidityDOS.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ItemTMenuValidityDO::getTimes))), ArrayList::new
            ));
            Integer number = listTimes.stream().map(ItemTMenuValidityDO::getTimesQuantum).collect(Collectors.toList()).stream().reduce(Integer::sum).orElse(0);
            List<String> dbtimes = listTimes.stream().map(ItemTMenuValidityDO::getTimes).collect(Collectors.toList());
            int maxSize = 5 - number;
            if (itmeTemplateMenuValidityReqDTO.getTimes() == null && !number.equals(0)) {
                throw new BusinessException("与已选时段冲突,请调整时段");
            }
            if (maxSize == 0) {
                throw new BusinessException("当前已有时间段已达到最大限制");
            }
            if (maxSize < itmeTemplateMenuValidityReqDTO.getTimes().size()) {
                throw new BusinessException("时间段最大还可选择" + maxSize + "个");
            }
            result = validateMenuTimeSegmentHander(dbtimes, itmeTemplateMenuValidityReqDTO);
        }
        return ObjectUtils.isEmpty(result) ? null : "执行时间 " + result + "已冲突";

    }

    /**
     * 星期验证
     *
     * @param weeks
     * @param times
     * @return
     */
    private String validateMenuTimeWeekHander(List<ItemTMenuValidityDO> weeks, List<Integer> times) {
        List<String> lweeks = new ArrayList<>();
        for (ItemTMenuValidityDO week : weeks) {
            String s = week.getWeeks();
            s = s.replace("[", "").replace("]", "").replace(" ", "");
            List<String> list = Arrays.asList(s.split(","));
            lweeks.addAll(list);
        }
        StringBuilder result = new StringBuilder();
        for (Integer o : times) {
            if (lweeks.contains(o.toString())) {
                result.append(WeekEnum.getDescByCode(o));
            }
        }
        return result.toString();
    }

    /**
     * 时间段验证
     *
     * @param dbTimes
     * @param times
     * @return
     */
    private String validateMenuTimeSegmentHander(List<String> dbTimes, ItmeTemplateMenuValidityReqDTO times) {
        StringBuilder result = new StringBuilder();
        //数据库已存在的时间段
        List<ItemTemplateMenuTimeReqDTO> itemTemplateMenuTimeReqDTOS = new ArrayList<>();
        dbTimes.stream().forEach(s -> itemTemplateMenuTimeReqDTOS.addAll(JacksonUtils.toObjectList(ItemTemplateMenuTimeReqDTO.class, s)));
        //菜单保存的时间段
        List<ItemTemplateMenuTimeReqDTO> meunTimes = times.getTimes();
        try {
            for (ItemTemplateMenuTimeReqDTO t1 : meunTimes) {
                SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
                Date start = sdf.parse(t1.getStartTime() + ":00");
                Date end = sdf.parse(t1.getEndTime() + ":59");
                for (ItemTemplateMenuTimeReqDTO o : itemTemplateMenuTimeReqDTOS) {
                    Date dbstart = sdf.parse(o.getStartTime() + ":00");
                    Date dbend = sdf.parse(o.getEndTime() + ":59");
                    boolean sflag = start.before(dbstart) && end.before(dbstart);
                    //时间段是否在比较的时间段后面
                    boolean eflag = start.after(dbend) && end.after(dbend);
                    if (Boolean.FALSE.equals(sflag) && Boolean.FALSE.equals(eflag)) {
                        result.append("[" + t1.getStartTime() + "-" + t1.getEndTime() + "] 与 [ " + o.getStartTime() + "-" + o.getEndTime() + "] ");
                    }
                }
            }
        } catch (Exception e) {
            throw new BusinessException("时间转换异常");
        }
        return result.toString();
    }

    /**
     * 传入模板有效期 对比时间是否重复
     *
     * @param request
     * @param itemTemplateDOS 门店已存在的模板执行有效期
     * @return true：合法  false：不合法
     */
    public Boolean validateTemplateTime(ItemTemplateReqDTO request, List<ItemTemplateDO> itemTemplateDOS) {
        if (ObjectUtils.isEmpty(itemTemplateDOS) || itemTemplateDOS.isEmpty()) {
            return Boolean.TRUE;
        }
        LocalDateTime effectiveStartTime = DateTimeUtils.mills2LocalDateTime(DateTimeUtils.parseStrDate(request.getEffectiveStartTime(), "yyyy-MM-dd HH:mm:ss"));
        LocalDateTime effectiveEndTime = DateTimeUtils.mills2LocalDateTime(DateTimeUtils.parseStrDate(request.getEffectiveEndTime(), "yyyy-MM-dd HH:mm:ss"));
        for (int i = 0, len = itemTemplateDOS.size(); i < len; i++) {
            //时间段是否在比较的时间段前面
            boolean sflag = effectiveStartTime.isBefore(itemTemplateDOS.get(i).getEffectiveStartTime()) && effectiveEndTime.isBefore(itemTemplateDOS.get(i).getEffectiveStartTime());
            //时间段是否在比较的时间段后面
            boolean eflag = effectiveStartTime.isAfter(itemTemplateDOS.get(i).getEffectiveEndTime()) && effectiveEndTime.isAfter(itemTemplateDOS.get(i).getEffectiveEndTime());
            if (Boolean.FALSE.equals(sflag) && Boolean.FALSE.equals(eflag)) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 传入itemguid ，套餐估清验证
     *
     * @param itemGuid
     * @return
     */
    public boolean setmealVerifyEstimate(String itemGuid,String storeGuid) {
        List<SetMealEstimateQuery> setMealEasimateQueries = iEstimateService.getSetMealSubitemEstimate(itemGuid,storeGuid);
        if (ObjectUtils.isEmpty(setMealEasimateQueries) || setMealEasimateQueries.isEmpty()) {
            return Boolean.TRUE;
        }
        Map<Integer, List<SetMealEstimateQuery>> collect = setMealEasimateQueries
                .stream()
                .collect(Collectors.groupingBy(SetMealEstimateQuery::getPickNum));
        for (Map.Entry<Integer, List<SetMealEstimateQuery>> m : collect.entrySet()) {
            //固定套餐
            if (m.getKey().equals(0)) {
                List<SetMealEstimateQuery> list = m.getValue();
                for (SetMealEstimateQuery o : list) {
                    //是否估清
                    if (o.getIsSoldOut() == 2 || (o.getIsSoldOut() == 1 && o.getIsTheLimit() == 2 && o.getResidueQuantity().compareTo(BigDecimal.ZERO) == 0)) {
                        return Boolean.FALSE;
                    }
                }
            } else { //可选套餐
                List<SetMealEstimateQuery> list = m.getValue();
                //可选套餐 估清商品数
                Integer flag = 0;
                for (SetMealEstimateQuery o : list) {
                    //是否估清
                    if (o.getIsSoldOut() == 2 || (o.getIsSoldOut() == 1 && o.getIsTheLimit() == 2 && o.getResidueQuantity().compareTo(BigDecimal.ZERO) == 0)) {
                        flag++;
                    }
                }
                //可选套餐子菜全部估清
                if (flag == list.size()) {
                    return Boolean.FALSE;
                }
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 根据执行时间解析出当前时间戳差
     *
     * @param list
     */
    public List<Long> templateExecuteHander(List<ItemTemplateExecuteTimeQuery> list) {
        List<Long> times = new ArrayList<>();
        //计算星期所需容器
        List<String> weeks = list.stream().filter(a -> a.getPeriodicMode().equals(2)).map(ItemTemplateExecuteTimeQuery::getWeeks).collect(Collectors.toList());
        //计算时间段所需容器
        List<Long> finalTimes = times;
        list.forEach(o -> {
            log.info("与当前时间差" + o.getDifference());
            LocalDateTime now = o.getNow();
            //时间段
            if (o.getPeriodicMode() == 1) {
                List<ItemTemplateMenuTimeReqDTO> itemTemplateMenuTimeReqDTOS = JacksonUtils.toObjectList(ItemTemplateMenuTimeReqDTO.class, o.getTimes());
                String strNow = DateTimeUtils.localDateTime2String(now, DateTimeUtils.PATTERN_DAY);
                LocalDateTime finalNow = now;
                itemTemplateMenuTimeReqDTOS.forEach(s -> {
                    String t = strNow + " " + s.getStartTime() + ":59";
                    String t1 = strNow + " " + s.getEndTime() + ":59";
                    LocalDateTime end = DateTimeUtils.mills2LocalDateTime(DateTimeUtils.parseStrDate(t, DateTimeUtils.PATTERN_SECONDS));
                    LocalDateTime end1 = DateTimeUtils.mills2LocalDateTime(DateTimeUtils.parseStrDate(t1, DateTimeUtils.PATTERN_SECONDS));
                    if (o.getDifference() < 0) {
                        end = end.plusDays(-o.getDifference());
                        end1 = end1.plusDays(-o.getDifference());
                    }
                    Duration between = Duration.between(finalNow, end);
                    Duration between1 = Duration.between(finalNow, end1);
                    long millis = between.toMillis();
                    long millis1 = between1.toMillis();
                    if (millis >= 0) {
                        finalTimes.add(millis);
                    }
                    if (millis1 >= 0) {
                        finalTimes.add(millis1);
                    }
                });
            }
            //星期
            else if (o.getPeriodicMode() == 2) {
                List<String> lweeks = new ArrayList<>();
                for (String a : weeks) {
                    a = a.replace("[", "").replace("]", "").replace(" ", "");
                    List<String> l = Arrays.asList(a.split(","));
                    lweeks.addAll(l);
                }
                if (o.getDifference() < 0) {
                    LocalDateTime end = now.plusDays(-o.getDifference());
                    Integer week = WeekEnum.getCodeByWeek(end.getDayOfWeek().toString());
                    if (lweeks.contains(week.toString())) {
                        Duration between = Duration.between(now, end);
                        long millis = between.toMillis();
                        if (millis >= 0) {
                            finalTimes.add(millis);
                        }
                    }
                }
            }
        });
        if (!finalTimes.isEmpty()) {
            Set<Long> set = new TreeSet<>(finalTimes);
            times = set.stream().collect(Collectors.toList());
        }
        return times;
    }

    public String getNowExecuteMenu(List<ItemTemplateExecuteTimeQuery> list) {
        LocalDateTime now = LocalDateTime.now();
        if (!ObjectUtils.isEmpty(list) && !list.isEmpty()) {
            now = list.get(0).getNow();
        }
        Map<String, String> weeksMap = list.stream()
                .filter(a -> a.getPeriodicMode().equals(2))
                .filter(a -> a.getDifference() >= 0)
                .collect(Collectors.toList())
                .stream()
                .collect(Collectors.toMap(ItemTemplateExecuteTimeQuery::getGuid, ItemTemplateExecuteTimeQuery::getWeeks));
        //星期执行有效
        if (Optional.ofNullable(weeksMap).isPresent()) {
            Integer week = WeekEnum.getCodeByWeek(now.getDayOfWeek().toString());
            for (Map.Entry<String, String> entry : weeksMap.entrySet()) {
                String strWeeks = entry.getValue();
                strWeeks = strWeeks.replace("[", "").replace("]", "").replace(" ", "");
                List<String> l = Arrays.asList(strWeeks.split(","));
                if (l.contains(week.toString())) {
                    return entry.getKey();
                }
            }
        }
        for (ItemTemplateExecuteTimeQuery o : list) {
            //当前时间之前就已经生效的
            if (o.getPeriodicMode() == 1 && o.getDifference() >= 0) {
                List<ItemTemplateMenuTimeReqDTO> collect = JacksonUtils.toObjectList(ItemTemplateMenuTimeReqDTO.class, o.getTimes());
                LocalDateTime finalNow = now;
                String strNow = DateTimeUtils.localDateTime2String(now, DateTimeUtils.PATTERN_DAY);
                for (ItemTemplateMenuTimeReqDTO s : collect) {
                    String strStart = strNow + " " + s.getStartTime() + ":00";
                    String strEnd = strNow + " " + s.getEndTime() + ":59";
                    LocalDateTime start = DateTimeUtils.mills2LocalDateTime(DateTimeUtils.parseStrDate(strStart, DateTimeUtils.PATTERN_SECONDS));
                    LocalDateTime end = DateTimeUtils.mills2LocalDateTime(DateTimeUtils.parseStrDate(strEnd, DateTimeUtils.PATTERN_SECONDS));
                    if (start.isBefore(finalNow) && end.isAfter(finalNow)) {
                        return o.getGuid();
                    }
                }
            }
        }
        return null;
    }

    public void pushMsg(String storeGuid) {
        if (Optional.ofNullable(storeGuid).isPresent()) {
            List<Long> times = iItemTMenuValidityService.getTimeAndTypeForSyn(SingleDataDTO.builder().data(storeGuid).build());
            times.add(0, 500L); //默认500ms
            Map<String, Object> map = new HashMap<>();
            if (times.isEmpty() || ObjectUtils.isEmpty(times)) {
                map.put("times", null);
            } else {
                map.put("times", times);
            }
            String content = JacksonUtils.writeValueAsString(map);
            eventPushHelper.itemTempLateTimeChangedSendMsgToAndriod(storeGuid, content);
        }
    }


    /**
     * 映射商品以及其相关套餐数量
     *
     * @param skuDOList
     */
    public Map<String, Integer> mapItem2RelatePkgNum(List<SkuDO> skuDOList) {
        // 商品GUID与关联的套餐数映射的MAP
        Map<String, Integer> item2RelatePkgNumMap = new HashMap<>();
        if (CollectionUtils.isEmpty(skuDOList)) {
            return item2RelatePkgNumMap;
        }
        List<String> skuGuidList = skuDOList.stream().map(SkuDO::getGuid).collect(Collectors.toList());
        List<RSkuSubgroupDO> skuSubgroupDOS = skuSubgroupService.list(new LambdaQueryWrapper<RSkuSubgroupDO>()
                .in(RSkuSubgroupDO::getSkuGuid, skuGuidList));
        // 涉及规格所关联的分组集合
        List<SubgroupDO> subgroupList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(skuSubgroupDOS)) {
            Set<String> subgroupGuidSet = skuSubgroupDOS.stream().map(RSkuSubgroupDO::getSubgroupGuid).collect(Collectors.toSet());
            List<SubgroupDO> subgroupDOS = (ArrayList) subgroupService.listByIds(subgroupGuidSet);
            subgroupList.addAll(subgroupDOS);
        }
        // 按商品分组的MAP
        Map<String, List<SkuDO>> itemGroupMap = skuDOList.stream().collect(Collectors.groupingBy(SkuDO::getItemGuid));
        Set<Map.Entry<String, List<SkuDO>>> entries = itemGroupMap.entrySet();
        entries.forEach(entry -> {
            String itemGuid = entry.getKey();
            List<SkuDO> thisItemSkuDOList = entry.getValue();
            Set<String> thisItemSkuGuidSet = thisItemSkuDOList.stream().map(SkuDO::getGuid).collect(Collectors.toSet());
            // 被当前规格推送的规格
            List<SkuDO> pushSkuList = skuDOList.stream().filter(skuDO -> thisItemSkuGuidSet.contains(skuDO.getParentGuid())).collect(Collectors.toList());
            // 当前规格关联的分组集合
            List<RSkuSubgroupDO> relateSkuSubgroupList = skuSubgroupDOS.stream().filter(rSkuSubgroupDO -> {
                if (thisItemSkuGuidSet.contains(rSkuSubgroupDO.getSkuGuid())) {
                    return true;
                }
                if (!CollectionUtils.isEmpty(pushSkuList)) {
                    List<String> pushSkuGuidList = pushSkuList.stream().map(SkuDO::getGuid).collect(Collectors.toList());
                    return pushSkuGuidList.contains(rSkuSubgroupDO.getSkuGuid());
                }
                return false;
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(relateSkuSubgroupList)) {
                // 关联的分组GUID集合
                List<String> relateSubgroupGuidList = relateSkuSubgroupList.stream().map(RSkuSubgroupDO::getSubgroupGuid).collect(Collectors.toList());
                // 关联的分组集合
                List<SubgroupDO> relateSubgroupList = subgroupList.stream()
                        .filter(subgroupDO -> relateSubgroupGuidList.contains(subgroupDO.getGuid()))
                        .collect(Collectors.toList());
                // 关联的套餐GUID集合
                Set<String> relatePkgGuidSet = relateSubgroupList.stream().map(SubgroupDO::getItemGuid).collect(Collectors.toSet());
                item2RelatePkgNumMap.put(itemGuid, relatePkgGuidSet.size());
            } else {
                item2RelatePkgNumMap.put(itemGuid, 0);
            }
        });
        return item2RelatePkgNumMap;
    }

    public void validateStoreGuid(BaseDTO baseDTO) {
        if (StringUtils.isEmpty(baseDTO.getStoreGuid())) {
            throw new ParameterException(WRONG_PARAMS);
        }
    }

    //切换商品类型
    public ItemReqDTO change(ItemReqDTO itemReqDTO) {
        if (!itemReqDTO.getItemType().equals(1) && !itemReqDTO.getItemType().equals(3)) {
            itemReqDTO.setItemType(itemReqDTO.getSkuList().size() > 1 ? 2 : 4);
        }
        return itemReqDTO;
    }

    public Map<String, Integer> mapItem2RelateGroupMealPakNum(List<SkuDO> skuDOList) {
        List<ItemRelateGroupMealBO> itemRelateGroupMealBOS = groupMealService.mapItem2RelateGroupMealPakNum(skuDOList);
        if (!CollectionUtils.isEmpty(itemRelateGroupMealBOS)) {
            return itemRelateGroupMealBOS
                    .stream()
                    .collect(Collectors
                            .toMap(ItemRelateGroupMealBO::getItemGuid,
                                    ItemRelateGroupMealBO::getNum));
        }
        return null;
    }

    public String getEffectStorePlanGuid(List<PricePlanNowDTO> planNowList) {
        int nowTime = transferPricePlanTime(LocalDateTime.now());

        // 有生效的菜谱
        String pricePlanNowAllTime = null;
        String pricePlanNowTime = null;
        for (PricePlanNowDTO planNow : planNowList) {
            Integer sellTimeType = planNow.getSellTimeType();
            if (sellTimeType == 0) {
                // 全时段菜品方案
                pricePlanNowAllTime = planNow.getPlanGuid();
                continue;
            }
            // 特殊时段菜品方案
            int startTimeInt = transferPricePlanTime(planNow.getStartTime());
            int endTimeInt = transferPricePlanTime(planNow.getEndTime());
            log.info("特殊时段时间,nowTime->{}, startTimeInt->{}, endTimeInt->{}", nowTime, startTimeInt, endTimeInt);
            boolean overDayNot = endTimeInt > startTimeInt && nowTime >= startTimeInt && nowTime <= endTimeInt;
            boolean overDay = endTimeInt <= startTimeInt && (nowTime >= startTimeInt || nowTime <= endTimeInt);
            if (overDayNot || overDay) {
                pricePlanNowTime = planNow.getPlanGuid();
            }

        }

        if (pricePlanNowTime != null) {
            return pricePlanNowTime;
        }
        return pricePlanNowAllTime;
    }

    private int transferPricePlanTime(LocalDateTime time){
        String startMinute = (String.valueOf(time.getMinute())).length() == 1 ? Constant.STRING_ZERO + time.getMinute() : String.valueOf(time.getMinute());
        return Integer.parseInt(time.getHour() + startMinute);
    }

}
