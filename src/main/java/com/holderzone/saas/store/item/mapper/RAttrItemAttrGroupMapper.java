package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.item.entity.domain.RAttrItemAttrGroupDO;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 属性值与商品属性组关联表的关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Repository
public interface RAttrItemAttrGroupMapper extends BaseMapper<RAttrItemAttrGroupDO> {

}
