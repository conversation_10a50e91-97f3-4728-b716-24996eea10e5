package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 套餐的分组
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsi_subgroup")
public class SubgroupDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @TableLogic
    private Integer isDelete;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    private String itemGuid;
    /**
     * 品牌GUID
     */
    private String brandGuid;

    /**
     * 关联的门店GUID
     */
    private String storeGuid;

    /**
     * 分组名称
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String name;

    /**
     * 选择商品数量:0:为固定套餐
     */
    private Integer pickNum;

    private Integer sort;

    /**
     * 品牌库对应的分组GUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的分组，则该字段为原分组GUID。
     */
    private String parentGuid;

    /**
     * 分组来源（0：门店自己创建的分组，1：品牌自己创建的分组,2:被推送过来的分组）
     */
    private Integer subgroupFrom;


}
