package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.ItmeTemplateMenuValidityReqDTO;
import com.holderzone.saas.store.item.entity.domain.ItemTMenuValidityDO;
import com.holderzone.saas.store.item.entity.query.ItemTemplateExecuteTimeQuery;

import java.util.List;

/**
 * <p>
 * 商品模拟-菜单-执行有效期 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
public interface IItemTMenuValidityService extends IService<ItemTMenuValidityDO> {


    /**
     * 根据传入商品模板guid查询有效期
     * @param guid 商品模板guid
     * @param periodicMode 执行模式 可不传 1：按时段  2：按星期  3：周期+时段
     * @param  menuGuid 菜单guid
     * @return 对应的执行时间集合
     */
    List<ItemTMenuValidityDO>  getMenuValiditys(String guid, Integer periodicMode, String menuGuid);

    /**
     * 保存商品模板-菜单-执行时间
     * @param request
     * @param menuGuid
     * @param periodicMode 执行模式 1：按时段  2：按星期  3：周期+时段
     * @param  isItFullTime 是否全时段 1：否 2：是
     * @return
     */
    Boolean saveItemMenuValidity(ItmeTemplateMenuValidityReqDTO request,Integer periodicMode,String menuGuid,Integer isItFullTime);


    /**
     * 商品模板-菜单执行时间-安卓同步菜单执行时间
     * @param request
     * @return
     */
    List<Long> getTimeAndTypeForSyn(SingleDataDTO request);


    /**
     * 获取当前时间门店运行菜单的有效时间
     * @param storeGuid
     * @return
     */
    List<ItemTemplateExecuteTimeQuery>  getNowMenuExecuteTimes(String storeGuid);

    /**
     * 删除菜单时删除对应的执行时间
     * @param menuGuid
     * @return
     */
    Boolean removeMenuTime(String menuGuid);
}
