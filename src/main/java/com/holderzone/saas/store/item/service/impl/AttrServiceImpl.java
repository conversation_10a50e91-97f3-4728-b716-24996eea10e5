package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.AttrReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrSaveReqDTO;
import com.holderzone.saas.store.dto.item.resp.AttrRespDTO;
import com.holderzone.saas.store.item.entity.domain.*;
import com.holderzone.saas.store.item.helper.EventPushHelper;
import com.holderzone.saas.store.item.mapper.AttrMapper;
import com.holderzone.saas.store.item.mapper.RTypeAttrMapper;
import com.holderzone.saas.store.item.service.*;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.MapStructUtils;
import com.holderzone.saas.store.item.util.PushUtils;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.item.constant.Constant.*;

/**
 * <p>
 * 属性值（属性组下的属性内容） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-19
 */
@Service
@Slf4j
public class AttrServiceImpl extends ServiceImpl<AttrMapper, AttrDO> implements IAttrService {

    private final AttrMapper attrMapper;
    private final IRTypeAttrService typeAttrService;
    private final RTypeAttrMapper typeAttrMapper;
    private final DynamicHelper dynamicHelper;
    private final IItemService itemService;
    private final IRItemAttrGroupService itemAttrGroupService;
    private final IRAttrItemAttrGroupService attrItemAttrGroupService;
    private final IAttrGroupService attrGroupService;
    private final EventPushHelper eventPushHelper;
    private final PushUtils pushUtils;
    private final RedisTemplate redisTemplate;

    @Autowired
    public AttrServiceImpl(AttrMapper attrMapper, IRTypeAttrService typeAttrService, RTypeAttrMapper typeAttrMapper, DynamicHelper dynamicHelper, @Lazy IItemService itemService, IRItemAttrGroupService itemAttrGroupService, IRAttrItemAttrGroupService attrItemAttrGroupService, @Lazy IAttrGroupService attrGroupService, @Lazy EventPushHelper eventPushHelper, PushUtils pushUtils, RedisTemplate redisTemplate) {
        this.attrMapper = attrMapper;
        this.typeAttrService = typeAttrService;
        this.typeAttrMapper = typeAttrMapper;
        this.dynamicHelper = dynamicHelper;
        this.itemService = itemService;
        this.itemAttrGroupService = itemAttrGroupService;
        this.attrItemAttrGroupService = attrItemAttrGroupService;
        this.attrGroupService = attrGroupService;
        this.eventPushHelper = eventPushHelper;
        this.pushUtils = pushUtils;
        this.redisTemplate = redisTemplate;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(AttrSaveReqDTO attrSaveReqDTO) {
        AttrDO attrDO = MapStructUtils.INSTANCE.mapAttrDO(attrSaveReqDTO);
        attrDO.setGuid(attrSaveReqDTO.getAttrGuid());
        AttrDO before = attrMapper.selectById(attrSaveReqDTO.getAttrGuid());
        if (before == null) {
            throw new BusinessException(OP_FAIL);
        }
        Integer count = attrMapper.selectCount(new LambdaQueryWrapper<AttrDO>()
                .eq(AttrDO::getAttrGroupGuid, before.getAttrGroupGuid())
                .eq(AttrDO::getName, attrSaveReqDTO.getName())
                .ne(AttrDO::getGuid, attrSaveReqDTO.getAttrGuid()));
        if (count != 0) {
            throw new ParameterException("属性名称重复");
        }

        // 1.保存修改的基本信息
        attrMapper.update(attrDO, new LambdaQueryWrapper<AttrDO>().eq(AttrDO::getGuid, attrSaveReqDTO.getAttrGuid()).or().eq(AttrDO::getParentGuid, attrSaveReqDTO.getAttrGuid()));

        // 2.绑定属性值与type
        List<String> typeGuidList = attrSaveReqDTO.getTypeGuidList();
        if (typeGuidList == null) {
            typeGuidList = new ArrayList<>();
        }

        // 已绑定的type
        List<String> existTypeGuidList = typeAttrService.listTypeGuidByAttr(attrSaveReqDTO.getAttrGuid());
        // 待删除的
        List toDeleteTypeGuid = ListUtils.subtract(existTypeGuidList, typeGuidList);
        List toAddTypeGuid = ListUtils.subtract(typeGuidList, existTypeGuidList);

        // 删除修改后没有哦但数据库中已有的绑定关系
        if (!ObjectUtils.isEmpty(toDeleteTypeGuid)) {
            typeAttrService.removeBind(toDeleteTypeGuid, attrSaveReqDTO.getAttrGuid());
        }
        // 添加新增的绑定关系
        if (!ObjectUtils.isEmpty(toAddTypeGuid)) {
            typeAttrService.save(toAddTypeGuid, attrSaveReqDTO.getAttrGuid());
        }
        // 查询数据库对应的属性实体
        AttrDO attrInDb = getById(attrSaveReqDTO.getAttrGuid());
        // 查询当前属性对应的属性组实体
        AttrGroupDO attrGroupDO = attrGroupService.getById(attrInDb.getAttrGroupGuid());
        // 将当前属性涉及到分类下的所有商品关联当前属性
        relateAttrToItemList(typeGuidList, attrInDb, attrGroupDO);

        return true;
    }

    @Transactional
    @Override
    public void deleteOrUpdatePushAttrAfterDeletePushRelation(List<AttrDO> pushAttrDOList) {
        if (CollectionUtils.isEmpty(pushAttrDOList)) {
            return;
        }
        // 被推送的属性GUID集合
        List<String> pushAttrGuidList = pushAttrDOList.stream().map(AttrDO::getGuid).collect(Collectors.toList());
        // 被推送属性与门店自建商品产生的关联关系集合（与被推送商品的关联关系已被删除）
        List<RAttrItemAttrGroupDO> attrItemAttrGroupDOS = attrItemAttrGroupService.list(new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                .in(RAttrItemAttrGroupDO::getAttrGuid, pushAttrGuidList));
        if (!CollectionUtils.isEmpty(attrItemAttrGroupDOS)) {
            // 目前关联了商品的属性GUID集合
            Set<String> relateItemAttrGuidSet = attrItemAttrGroupDOS.stream().map(RAttrItemAttrGroupDO::getAttrGuid).collect(Collectors.toSet());
            // 修改属性从被推送变为门店自建
            changeAttrFromPush2Self(relateItemAttrGuidSet);
            // 过滤出未关联商品的属性，以备后面删除
            pushAttrGuidList.removeAll(relateItemAttrGuidSet);
        }
        // 删除被推送过来的未关联商品的属性
        if (!CollectionUtils.isEmpty(pushAttrGuidList)) {
            removeByIds(pushAttrGuidList);
            typeAttrService.remove(new LambdaQueryWrapper<RTypeAttrDO>().in(RTypeAttrDO::getAttrGuid, pushAttrGuidList));
        }
    }

    /**
     * 将指定属性从推送改为门店自建
     *
     * @param relateItemAttrGuidSet
     */
    private void changeAttrFromPush2Self(Set<String> relateItemAttrGuidSet) {
        if (CollectionUtils.isEmpty(relateItemAttrGuidSet)) {
            return;
        }
        // 待更新的属性集合
        List<AttrDO> updateAttrDOList = (ArrayList) listByIds(relateItemAttrGuidSet);
        pushUtils.fixFieldsFromPush2SelfCreate(updateAttrDOList, AttrDO::setAttrFrom, AttrDO::setParentGuid);
        updateBatchById(updateAttrDOList, updateAttrDOList.size());
    }

    @Transactional
    @Override
    public void deletePushAttrRelate(List<RAttrItemAttrGroupDO> toDelAttrRelationDOList, List<AttrDO> pushAttrDOList) {
        if (!CollectionUtils.isEmpty(pushAttrDOList)) {
            // 被推送的属性GUID集合
            List<String> pushAttrGuidList = pushAttrDOList.stream().map(AttrDO::getGuid).collect(Collectors.toList());
            // 查询是否有门店自建商品与属性关联
            Set<String> storeGuidSet = pushAttrDOList.stream().map(AttrDO::getStoreGuid).collect(Collectors.toSet());
            // 门店被推送商品集合
            List<ItemDO> pushItemDOList = itemService.list(new LambdaQueryWrapper<ItemDO>()
                    .in(ItemDO::getStoreGuid, storeGuidSet).eq(ItemDO::getItemFrom, 2));
            // 被推送商品GUID集合
            List<String> pushItemGuidList = pushItemDOList.stream().map(ItemDO::getGuid).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(pushItemGuidList)) {
                // 推送商品与属性组的关联关系
                List<RItemAttrGroupDO> pushItemRelationList = itemAttrGroupService.list(new LambdaQueryWrapper<RItemAttrGroupDO>()
                        .in(RItemAttrGroupDO::getItemGuid, pushItemGuidList));
                if (!CollectionUtils.isEmpty(pushItemRelationList)) {
                    List<String> pushItemRelationGuidList = pushItemRelationList.stream().map(RItemAttrGroupDO::getGuid).collect(Collectors.toList());
                    // 所有推送商品与所有属性的关联关系
                    List<RAttrItemAttrGroupDO> pushItemAttrRelationList = attrItemAttrGroupService.list(new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                            .in(RAttrItemAttrGroupDO::getItemAttrGroupGuid, pushItemRelationGuidList));
                    // 仅剩下涉及属性的商品关联关系
                    pushItemAttrRelationList.removeIf(relation -> !pushAttrGuidList.contains(relation.getAttrGuid()));
                    if (!CollectionUtils.isEmpty(pushItemAttrRelationList)) {
                        toDelAttrRelationDOList.addAll(pushItemAttrRelationList);
                    }
                }

            }
        }


        if (!CollectionUtils.isEmpty(toDelAttrRelationDOList)) {
            // 待删除的属性关联关系GUID集合
            Set<String> toDelAttrRelationGuidSet = toDelAttrRelationDOList.stream().map(RAttrItemAttrGroupDO::getGuid).collect(Collectors.toSet());
            // 涉及的属性组关联关系GUID集合
            Set<String> relateAttrGroupRelationGuidSet = toDelAttrRelationDOList.stream()
                    .map(RAttrItemAttrGroupDO::getItemAttrGroupGuid).collect(Collectors.toSet());
            // 解绑被推送商品的与被推送属性
            attrItemAttrGroupService.removeByIds(toDelAttrRelationGuidSet);
            // 查询属性组关联关系在属性关系表里是否有关联实体，若无，则删除
            List<RAttrItemAttrGroupDO> relateAttrRelationDOList = attrItemAttrGroupService.list(new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                    .in(RAttrItemAttrGroupDO::getItemAttrGroupGuid, relateAttrGroupRelationGuidSet));
            if (!CollectionUtils.isEmpty(relateAttrRelationDOList)) {
                // 有关联属性的属性组关联关系GUID集合
                Set<String> withAttrAttrGroupRelationGuidSet = relateAttrRelationDOList.stream()
                        .map(RAttrItemAttrGroupDO::getItemAttrGroupGuid).collect(Collectors.toSet());
                // 仅剩下没有关联属性的属性组关联关系GUID
                relateAttrGroupRelationGuidSet.removeAll(withAttrAttrGroupRelationGuidSet);
            }
            if (!CollectionUtils.isEmpty(relateAttrGroupRelationGuidSet)) {
                // 删除无关联属性的属性组关联关系实体
                itemAttrGroupService.removeByIds(relateAttrGroupRelationGuidSet);
            }
        }
    }

    @Transactional
    @Override
    public boolean deleteByGuid(String attrGuid) {
        // 如果是自建属性，则直接删除，并删除与商品的关联关系，删除后，检测商品与属性组的关联关系是否还存在，并更新门店被推送商品与对应属性以及属性组的关联关系
        // 如果是被推送属性，则查询是否与门店自建商品关联，若关联，则属性变为自建。
        //bugFix 21546，本地测试用selectById没有查到数据，换成guid试试
        AttrDO before = attrMapper.selectByGuid(attrGuid);
        if (ObjectUtils.isEmpty(before)) {
            throw new ParameterException("删除的属性值不存在");
        }
        // 同时会删除该属性值与商品类型的绑定关系
        typeAttrService.deleteByAttr(attrGuid);
        // 属性与商品绑定列表
        List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOList = attrItemAttrGroupService.list(new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                .eq(RAttrItemAttrGroupDO::getAttrGuid, attrGuid));
        // 涉及的的属性组与商品的绑定关系
        List<String> itemAttrGroupGuidList = new ArrayList<>();
        // 如果属性与商品绑定
        if (!CollectionUtils.isEmpty(rAttrItemAttrGroupDOList)) {
            // 该绑定关系对应的属性组绑定关系
            List<String> thisAttrItemAttrGroupGuidList = rAttrItemAttrGroupDOList.stream().map(RAttrItemAttrGroupDO::getItemAttrGroupGuid).collect(Collectors.toList());
            itemAttrGroupGuidList.addAll(thisAttrItemAttrGroupGuidList);
            // 删除属性值与商品的直接绑定关系
            attrItemAttrGroupService.deleteByAttr(attrGuid);
            List<RItemAttrGroupDO> itemAttrGroupDOS = (ArrayList) itemAttrGroupService.listByIds(thisAttrItemAttrGroupGuidList);
            // 同步删除被推商品与该属性组的绑定关系
            if (!CollectionUtils.isEmpty(itemAttrGroupDOS)) {
                // 该属性组关联的商品GUID集合
                List<String> itemGuidList = itemAttrGroupDOS.stream().map(RItemAttrGroupDO::getItemGuid).collect(Collectors.toList());
                // 更新商品以及被推送商品的HASATTR字段，并更新对应的属性组关联关系
                updateItemAttrState(itemGuidList, itemAttrGroupDOS);
            }
        }
        attrMapper.deleteById(attrGuid);
        // 该属性推送到门店的属性
        List<AttrDO> pushAttrDOList = list(new LambdaQueryWrapper<AttrDO>().eq(AttrDO::getParentGuid, attrGuid));
        if (!CollectionUtils.isEmpty(pushAttrDOList)) {
            // 删除推送商品与推送属性的关联关系，以及删除未关联门店自建商品的推送属性
            deletePushAttrRelate(new ArrayList<>(), pushAttrDOList);
            // 将与门店自建商品关联的属性改为自建，未与门店商品关联的推送属性删除
            deleteOrUpdatePushAttrAfterDeletePushRelation(pushAttrDOList);
        }
        /*//查询有属性且已经关联的商品guid
        List<String> hasAttrItemGuid = itemService.getHasAttrItemGuid();
        //更新hasAttr为0
        for (String itemGuid : hasAttrItemGuid) {
            itemService.updateHasAttr(itemGuid, HasAttrEnum.NO_ATTRIBUTE.getCode());
        }*/

        /*if (before.getAttrFrom().equals(ModuleEntranceEnum.STORE.code()) || before.getAttrFrom().equals(ModuleEntranceEnum.PUSH.code())) {
        } else if (before.getAttrFrom().equals(ModuleEntranceEnum.BRAND.code())) {
            // 品牌删除属性，判断该属性被推送到门店有没有绑定门店自建商品，若绑定了自建商品，将该属性改为自建属性，下面这么多就为了干这事儿
            List<RItemAttrGroupDO> itemAttrGroupDOList = itemAttrGroupService.list(new LambdaQueryWrapper<RItemAttrGroupDO>().in(RItemAttrGroupDO::getGuid, itemAttrGroupGuidList));
            if (!ObjectUtils.isEmpty(itemAttrGroupDOList)) {
                List<String> itemGuidList = itemAttrGroupDOList.stream().map(RItemAttrGroupDO::getItemGuid).collect(Collectors.toList());
                List<ItemDO> itemDOList = itemService.list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, itemGuidList));
                List<ItemDO> storeSelfItem = itemDOList.stream().filter(itemDO -> itemDO.getItemFrom().equals(ModuleEntranceEnum.STORE.code())).collect(Collectors.toList());
                List<String> storeSelfItemGuidList = storeSelfItem.stream().map(ItemDO::getGuid).collect(Collectors.toList());
                List<String> filterItemAttrGroupGuidList = itemAttrGroupDOList.stream().filter(rItemAttrGroupDO -> storeSelfItemGuidList.contains(rItemAttrGroupDO.getItemGuid())).map(RItemAttrGroupDO::getGuid)
                        .collect(Collectors.toList());
                List<RAttrItemAttrGroupDO> list = attrItemAttrGroupService.list(new LambdaQueryWrapper<RAttrItemAttrGroupDO>().in(RAttrItemAttrGroupDO::getItemAttrGroupGuid, filterItemAttrGroupGuidList));
                // 推送到门店，但因为绑定了门店自建商品，需要改为自建的属性guid
                List<String> retainAttrGuid = list.stream().map(RAttrItemAttrGroupDO::getAttrGuid).collect(Collectors.toList());
                List<AttrDO> attrDOS = attrMapper.selectBatchIds(retainAttrGuid);
                attrDOS.forEach(attrDO -> {
                    attrDO.setParentGuid("");
                    attrDO.setAttrFrom(ModuleEntranceEnum.STORE.code());
                });
                if (!ObjectUtils.isEmpty(attrDOS)) {
                    this.updateBatchById(attrDOS, attrDOS.size());
                }
            }

            attrMapper.delete(new LambdaQueryWrapper<AttrDO>().eq(AttrDO::getGuid, attrGuid).or().eq(AttrDO::getParentGuid, attrGuid));
        } else {
            throw new ParameterException("模块入口参数错误");
        }

        // 该属性被删除后，其关联的属性组下的属性个数
        Integer countByGroup = attrMapper.selectCount(new LambdaQueryWrapper<AttrDO>().eq(AttrDO::getAttrGroupGuid, attrGroupGuid));
        if (0 == countByGroup) {
            // 如果该属性组下已无属性，则解绑属性组与商品的关系
            itemAttrGroupService.removeByIds(itemAttrGroupGuidList);
        }

        // 如果该属性被推送到过门店，则需要通知Android端
        if (before.getAttrFrom().equals(ModuleEntranceEnum.BRAND.code())) {
            List<AttrDO> attrDOS = attrMapper.selectList(new LambdaQueryWrapper<AttrDO>().eq(BasePushDO::getParentGuid, attrGuid));
            if (!ObjectUtils.isEmpty(attrDOS)) {
                Set<String> collect = attrDOS.stream().map(BasePushDO::getStoreGuid).collect(Collectors.toSet());
                eventPushHelper.pushMsgToAndriod(new ArrayList<>(collect));
            }
        }*/

        return true;
    }

    /**
     * 更新商品及被推送商品的hasAttr字段
     *
     * @param itemGuidList
     * @param itemAttrGroupDOS
     */
    private void updateItemAttrState(List<String> itemGuidList, List<RItemAttrGroupDO> itemAttrGroupDOS) {
        if (CollectionUtils.isEmpty(itemGuidList) || CollectionUtils.isEmpty(itemAttrGroupDOS)) {
            return;
        }
        Map<String, String> itemAttrGroupMap = itemAttrGroupDOS.stream()
                .collect(Collectors.toMap(RItemAttrGroupDO::getItemGuid, RItemAttrGroupDO::getGuid));
        List<String> itemAttrGroupGuidList = itemAttrGroupDOS.stream().map(RItemAttrGroupDO::getGuid).collect(Collectors.toList());
        List<RAttrItemAttrGroupDO> attrItemAttrGroupDOS = attrItemAttrGroupService.list(new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                .in(RAttrItemAttrGroupDO::getItemAttrGroupGuid, itemAttrGroupGuidList));
        List<ItemDO> itemDOS = itemService.list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, itemGuidList));
        if (!CollectionUtils.isEmpty(attrItemAttrGroupDOS)) {
            Map<String, List<RAttrItemAttrGroupDO>> attrItemAttrGroupMap = attrItemAttrGroupDOS.stream()
                    .collect(Collectors.groupingBy(RAttrItemAttrGroupDO::getItemAttrGroupGuid));
            // 关联了属性的属性组关联关系
            Set<String> withAttrAttrGroupGuidSet = attrItemAttrGroupDOS.stream()
                    .map(RAttrItemAttrGroupDO::getItemAttrGroupGuid).collect(Collectors.toSet());
            // 仅留下未关联属性的属性组关联关系
            itemAttrGroupGuidList.removeAll(withAttrAttrGroupGuidSet);
            itemDOS.forEach(itemDO -> {
                String attrGroupRGuid = itemAttrGroupMap.get(itemDO.getGuid());
                if (!StringUtils.isEmpty(attrGroupRGuid)) {
                    List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOS = attrItemAttrGroupMap.get(attrGroupRGuid);
                    if (rAttrItemAttrGroupDOS.size() > 0) {
                        return;
                    }
                }
                itemDO.setHasAttr(0);
            });
        } else {
            itemDOS.forEach(itemDO -> itemDO.setHasAttr(0));
        }
        // 删除无关联属性的属性组关联关系
        if (!CollectionUtils.isEmpty(itemAttrGroupGuidList)) {
            itemAttrGroupService.removeByIds(itemAttrGroupGuidList);
        }
        // 更新ItemHasAttr
        List<RItemAttrGroupDO> itemAttrGroupList = itemAttrGroupService.list(new LambdaQueryWrapper<RItemAttrGroupDO>()
                .in(RItemAttrGroupDO::getItemGuid, itemGuidList));
        if (!CollectionUtils.isEmpty(itemAttrGroupList)) {
            Map<String, List<RItemAttrGroupDO>> itemGroupMap = itemAttrGroupList.stream().collect(Collectors.groupingBy(e -> e.getItemGuid()));
            itemDOS.forEach(itemDO -> {
                // 该商品的属性组关联关系
                List<RItemAttrGroupDO> itemAttrGroupDOList = itemGroupMap.get(itemDO.getGuid());
                if (!CollectionUtils.isEmpty(itemAttrGroupDOList)) {
                    boolean hasRequiredAttr = itemAttrGroupDOList.stream().anyMatch(itemAttrGroupDO -> itemAttrGroupDO.getIsRequired() == 1);
                    itemDO.setHasAttr(hasRequiredAttr ? 2 : 1);
                }
            });
        }
        itemService.updateBatchById(itemDOS, itemDOS.size());
        // 同时更新被推送商品的HasAttr字段
        List<ItemDO> pushItemDOList = itemService.list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getParentGuid, itemGuidList));
        if (!CollectionUtils.isEmpty(pushItemDOList)) {
            pushItemDOList.forEach(pushItemDO -> {
                // 品牌库对应的商品
                ItemDO parentItemDO = itemDOS.stream().filter(itemDO -> itemDO.getGuid().equals(pushItemDO.getParentGuid())).findFirst().orElse(null);
                pushItemDO.setHasAttr(parentItemDO.getHasAttr());
            });
            itemService.updateBatchById(pushItemDOList, pushItemDOList.size());
        }
    }

    @Transactional(readOnly = true)
    @Override
    public List<AttrRespDTO> listAttrByGroup(ItemSingleDTO itemSingleDTO) {
        if (itemSingleDTO.getData() == null) {
            throw new ParameterException("属性组GUID不能为空");
        }
        List<AttrDO> attrDOList = attrMapper.selectList(
                new LambdaQueryWrapper<AttrDO>()
                        .eq(AttrDO::getAttrGroupGuid, itemSingleDTO.getData())
                        .eq(AttrDO::getIsDelete, FALSE));
        return MapStructUtils.INSTANCE.attrDOList2attrRespDTOList(attrDOList);
    }

    @Override
    public boolean deleteByGroup(String attrGroupGuid) {
        int flag = attrMapper.delete(new LambdaQueryWrapper<AttrDO>().eq(AttrDO::getAttrGroupGuid, attrGroupGuid));
        return 0 != flag;
    }

    @Override
    public Integer removePushAttr(String storeGuid) {
        // 查询当前门店下被推送过来的属性集合
        List<AttrDO> attrDOList = list(new LambdaQueryWrapper<AttrDO>().eq(AttrDO::getStoreGuid, storeGuid).eq(AttrDO::getAttrFrom, 2));
        if (CollectionUtils.isEmpty(attrDOList)) {
            return 1;
        }
        List<String> attrGuidList = attrDOList.stream().map(AttrDO::getGuid).collect(Collectors.toList());
        // 查询与商品有关联关系的属性
        List<RAttrItemAttrGroupDO> attrItemAttrGroupDOS = attrItemAttrGroupService.list(new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                .in(RAttrItemAttrGroupDO::getAttrGuid, attrGuidList));
        if (!CollectionUtils.isEmpty(attrItemAttrGroupDOS)) {
            // 将该分类变为门店自建分类
            Set<String> withItemAttrGuidSet = attrItemAttrGroupDOS.stream().map(RAttrItemAttrGroupDO::getAttrGuid).collect(Collectors.toSet());
            updateWithItemAttr(attrGuidList, withItemAttrGuidSet);
        }
        // 删除分类以及分类相关实体
        if (!CollectionUtils.isEmpty(attrGuidList)) {
            removeByIds(attrGuidList);
            // 删除属性与分类的绑定关系
            typeAttrService.remove(new LambdaQueryWrapper<RTypeAttrDO>().in(RTypeAttrDO::getAttrGuid, attrGuidList));
        }
        return 1;
    }

    /**
     * 检查属性是否被使用
     *
     * @param attrGuid 属性guid
     * @return Boolean
     */
    @Override
    public Boolean checkAttrUsed(String attrGuid) {
        List<RAttrItemAttrGroupDO> rAttrItemAttrGroupList = attrItemAttrGroupService.list(
                new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                        .eq(RAttrItemAttrGroupDO::getAttrGuid, attrGuid)
                        .eq(RAttrItemAttrGroupDO::getIsDelete, 0)
        );
        if (CollectionUtils.isEmpty(rAttrItemAttrGroupList)) {
            log.warn("未查询到属性和属性组关联,rAttrItemAttrGroupList={}", rAttrItemAttrGroupList);
            return Boolean.FALSE;
        }
        List<String> itemAttrGroupGuidList = rAttrItemAttrGroupList.stream()
                .map(RAttrItemAttrGroupDO::getItemAttrGroupGuid)
                .distinct()
                .collect(Collectors.toList());
        List<RItemAttrGroupDO> rItemAttrGroupList = itemAttrGroupService.list(new LambdaQueryWrapper<RItemAttrGroupDO>()
                .in(RItemAttrGroupDO::getGuid, itemAttrGroupGuidList)
                .eq(RItemAttrGroupDO::getIsDelete, 0)
        );
        if (CollectionUtils.isEmpty(rItemAttrGroupList)) {
            log.warn("未查询到商品和属性组关联,itemAttrGroupGuidList={}", itemAttrGroupGuidList);
            return Boolean.FALSE;
        }
        List<String> itemGuidList = rItemAttrGroupList.stream()
                .map(RItemAttrGroupDO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        int count = itemService.count(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getGuid, itemGuidList)
                .eq(ItemDO::getIsEnable, 1)
                .eq(ItemDO::getIsDelete, 0));
        if (count < 1) {
            log.warn("未查询到商品,itemGuidList={}", itemGuidList);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private void updateWithItemAttr(List<String> attrGuidList, Set<String> withItemAttrGuidSet) {
        if (CollectionUtils.isEmpty(attrGuidList) || CollectionUtils.isEmpty(withItemAttrGuidSet)) {
            return;
        }
        // todo 参考delAttr逻辑
        // 去掉关联了商品的分类，仅留下未关联商品的分类
        attrGuidList.removeAll(withItemAttrGuidSet);
        // 门店中被推送的关联了商品的分类
        List<AttrDO> withItemTypeDOList = (ArrayList) listByIds(withItemAttrGuidSet);
        // 将这些分类从被推送分类修改为门店自建分类
        pushUtils.fixFieldsFromPush2SelfCreate(withItemTypeDOList, AttrDO::setAttrFrom, AttrDO::setParentGuid);
        updateBatchById(withItemTypeDOList, withItemTypeDOList.size());
    }

    @Override
    public List<AttrRespDTO> listAttrByGroup(List<String> attrGroupGuidList) {
        if (ObjectUtils.isEmpty(attrGroupGuidList)) {
            return Lists.newArrayList();
        }
        List<AttrDO> attrDOS = attrMapper.selectList(new LambdaQueryWrapper<AttrDO>().in(AttrDO::getAttrGroupGuid, attrGroupGuidList));
        return MapStructUtils.INSTANCE.attrDOList2attrRespDTOList(attrDOS);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveAttrValue(AttrReqDTO attrReqDTO) {
        Integer count = attrMapper.selectCount(new LambdaQueryWrapper<AttrDO>().eq(AttrDO::getName, attrReqDTO.getName())
                .eq(AttrDO::getAttrGroupGuid, attrReqDTO.getAttrGroupGuid()));
        if (count > 0) {
            throw new ParameterException("重名");
        }
        if (attrReqDTO.getPrice() == null) {
            attrReqDTO.setPrice(BigDecimal.ZERO);
        }

        AttrGroupDO attrGroupDO = attrGroupService.getById(attrReqDTO.getAttrGroupGuid());
        if (attrGroupDO == null) {
            throw new BusinessException("属性组不存在");
        }
        AttrDO attrDO = MapStructUtils.INSTANCE.attrReqDTO2attrDO(attrReqDTO);
        attrDO.setStoreGuid(attrGroupDO.getStoreGuid());
        attrDO.setBrandGuid(attrGroupDO.getBrandGuid());
        attrDO.setAttrFrom(attrReqDTO.getFrom());
        try {
            attrDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
        } catch (IOException e) {
            throw new BusinessException("BatchIdGenerator生成商品guid失败");
        }
        if (attrDO.getIsDefault() == null) {
            attrDO.setIsDefault(0);
        }
        attrMapper.insert(attrDO);

        // 关联的分类guidList
        List<String> typeGuidList = attrReqDTO.getTypeGuidList();
        if (CollectionUtils.isEmpty(typeGuidList)) {
            return true;
        }
        // 保存分类与属性的关联关系
        typeAttrService.save(typeGuidList, attrDO.getGuid());

        // 关联无此属性的商品与该属性的关联关系,如果商品关联了当前属性后的当前属性组下属性数量<=10，且不是套餐，就算带有属性，也要关联当前属性
        relateAttrToItemList(typeGuidList, attrDO, attrGroupDO);
        return true;
    }

    /**
     * 关联无此属性的商品与该属性的关联关系,
     * 如果商品关联了当前属性后的当前属性组下属性数量<=10，且不是套餐，就算带有属性，也要关联当前属性
     *
     * @param typeGuidList 当前属性关联的分类GUID集合
     * @param attrDO       当前属性实体
     * @param attrGroupDO  当前属性所属属性组实体
     */
    private void relateAttrToItemList(List<String> typeGuidList, AttrDO attrDO, AttrGroupDO attrGroupDO) {
        if (CollectionUtils.isEmpty(typeGuidList)) {
            return;
        }
        // 已选分类集合下的所有非推送商品
        List<ItemDO> itemDOList = itemService.list(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getTypeGuid, typeGuidList).ne(ItemDO::getItemType, 1).ne(ItemDO::getItemFrom, 2));
        if (CollectionUtils.isEmpty(itemDOList)) {
            return;
        }
        // 商品的GUID集合
        Set<String> itemGuidSet = itemDOList.stream().map(ItemDO::getGuid).collect(Collectors.toSet());
        // 当前属性组与商品关联实体集合
        List<RItemAttrGroupDO> itemAttrGroupDOList = itemAttrGroupService.list(
                new LambdaQueryWrapper<RItemAttrGroupDO>()
                        .eq(RItemAttrGroupDO::getAttrGroupGuid, attrGroupDO.getGuid())
                        .in(RItemAttrGroupDO::getItemGuid, itemGuidSet));
        // 商品与属性关联关系的集合
        List<RAttrItemAttrGroupDO> attrItemAttrGroupDOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemAttrGroupDOList)) {
            // 商品与属性组关联关系的GUID集合
            Set<String> itemAttrGroupGuidSet = itemAttrGroupDOList.stream().map(RItemAttrGroupDO::getGuid).collect(Collectors.toSet());
            // 商品与属性关联关系的集合
            List<RAttrItemAttrGroupDO> attrItemAttrGroupDOListFromDb = attrItemAttrGroupService.list(
                    new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                            .in(RAttrItemAttrGroupDO::getItemAttrGroupGuid, itemAttrGroupGuidSet));
            attrItemAttrGroupDOList.addAll(attrItemAttrGroupDOListFromDb);
        }
        // 获取商品与该属性组下属性关联关系的映射
        Map<String, List<String>> itemAttrMap = getItemAttrMap(itemDOList, itemAttrGroupDOList, attrItemAttrGroupDOList);
        // 要新增的商品与属性组关联关系集合
        List<RItemAttrGroupDO> saveItemAttrGroupDOList = new ArrayList<>();
        // 要新增的属性与商品属性组关联关系集合
        List<RAttrItemAttrGroupDO> saveAttrItemAttrGroupDOList = new ArrayList<>();
        // 将此属性配置应用到商品集合上
        // 当前属性关联的属性组

        Iterator<ItemDO> iterator = itemDOList.iterator();
        while (iterator.hasNext()) {
            ItemDO item = iterator.next();
            // 当前属性组下与该商品关联的属性GUID集合
            List<String> attrGuidList = itemAttrMap.get(item.getGuid());
            if (attrGuidList.contains(attrDO.getGuid())) {
                iterator.remove();
                continue;
            }
            attrGuidList.add(attrDO.getGuid());
            // 假如加上当前属性以后，该商品关联的属性GUID集合
            if (attrGuidList.size() > 10) {
                iterator.remove();
            } else {
                // 商品与属性组关联的实体
                RItemAttrGroupDO itemAttrGroupDO = itemAttrGroupDOList.stream()
                        .filter(rItemAttrGroupDO -> rItemAttrGroupDO.getItemGuid().equals(item.getGuid()))
                        .findFirst().orElse(null);
                try {
                    if (itemAttrGroupDO == null) {
                        itemAttrGroupDO = new RItemAttrGroupDO();
                        itemAttrGroupDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
                        itemAttrGroupDO.setItemGuid(item.getGuid());
                        itemAttrGroupDO.setAttrGroupGuid(attrGroupDO.getGuid());
                        itemAttrGroupDO.setIsRequired(attrGroupDO.getIsRequired());
                        itemAttrGroupDO.setIsMultiChoice(attrGroupDO.getIsMultiChoice());
                        // 新增与商品的关联关系，默认项一定是基于属性来的
                        itemAttrGroupDO.setWithDefault(attrDO.getIsDefault());
                        saveItemAttrGroupDOList.add(itemAttrGroupDO);
                    }

                    // 当前商品与当前属性的关联实体
                    RAttrItemAttrGroupDO attrItemAttrGroupDO = new RAttrItemAttrGroupDO();
                    attrItemAttrGroupDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
                    attrItemAttrGroupDO.setAttrGuid(attrDO.getGuid());
                    attrItemAttrGroupDO.setItemAttrGroupGuid(itemAttrGroupDO.getGuid());
                    attrItemAttrGroupDO.setIsDefault(attrDO.getIsDefault());
                    saveAttrItemAttrGroupDOList.add(attrItemAttrGroupDO);
                } catch (IOException e) {
                    throw new BusinessException("BatchIdGenerator生成商品guid失败");
                }
            }
        }

        if (!CollectionUtils.isEmpty(saveItemAttrGroupDOList)) {
            itemAttrGroupService.saveBatch(saveItemAttrGroupDOList);
        }
        if (!CollectionUtils.isEmpty(saveAttrItemAttrGroupDOList)) {
            attrItemAttrGroupService.saveBatch(saveAttrItemAttrGroupDOList);
        }
        // 需被更新的商品集合
        // FIXME: 2019/2/12 下面6行 提到itemService封装成单独的方法
        itemDOList.forEach(itemDO -> {
            if (itemDO.getHasAttr() != 2) {
                itemDO.setHasAttr(attrGroupDO.getIsRequired() == 1 ? 2 : 1);
            }
        });
        if (!CollectionUtils.isEmpty(itemDOList)) {
            itemService.updateBatchById(itemDOList);
        }
    }

    /**
     * 获取商品与属性关联关系的映射
     *
     * @param itemDOList
     * @param itemAttrGroupDOList
     * @param attrItemAttrGroupDOList
     */
    private Map<String, List<String>> getItemAttrMap(List<ItemDO> itemDOList, List<RItemAttrGroupDO> itemAttrGroupDOList, List<RAttrItemAttrGroupDO> attrItemAttrGroupDOList) {
        // 商品GUID与属性GUID关联的映射关系
        Map<String, List<String>> itemAttrMap = new HashMap<>();
        if (CollectionUtils.isEmpty(itemAttrGroupDOList)) {
            itemDOList.forEach(itemDO -> {
                itemAttrMap.put(itemDO.getGuid(), new ArrayList<>());
            });
        } else {
            // 按商品GUID分组的MAP
            Map<String, List<RItemAttrGroupDO>> itemGroupMap = itemAttrGroupDOList.stream().collect(Collectors.groupingBy(e -> e.getItemGuid()));
            // 如果商品与属性组有关联关系，则商品与属性也一定有关联关系
            if (CollectionUtils.isEmpty(attrItemAttrGroupDOList)) {
                // fixme 出现了此问题
                log.error(SYSTEM_ERROR + "商品有关联属性组，但是没有关联属性");
                throw new BusinessException(SYSTEM_ERROR);
            }
            for (ItemDO itemDO : itemDOList) {
                // 此商品与属性组的关联实体
                List<RItemAttrGroupDO> thisItemAttrGroupList = itemGroupMap.get(itemDO.getGuid());
                if (!CollectionUtils.isEmpty(thisItemAttrGroupList)) {
                    String itemAttrGroupGuid = thisItemAttrGroupList.get(0).getGuid();
                    // 此商品关联的属性关联体集合
                    List<RAttrItemAttrGroupDO> thisItemAttrList = attrItemAttrGroupDOList.stream()
                            .filter(attr -> itemAttrGroupGuid.equals(attr.getItemAttrGroupGuid()))
                            .collect(Collectors.toList());
                    // 此属性组商品关联的属性GUID集合
                    List<String> thisItemAttrGuidList = thisItemAttrList.stream().map(RAttrItemAttrGroupDO::getAttrGuid).collect(Collectors.toList());
                    itemAttrMap.put(itemDO.getGuid(), thisItemAttrGuidList);
                } else {
                    itemAttrMap.put(itemDO.getGuid(), new ArrayList<>());
                }
            }
        }
        return itemAttrMap;
    }
}
