package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Strings;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubItemSkuWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubgroupWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeSkuRespDTO;
import com.holderzone.saas.store.enums.item.ModuleEntranceEnum;
import com.holderzone.saas.store.item.entity.bo.ItemInfoBO;
import com.holderzone.saas.store.item.entity.bo.SubgroupBO;
import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import com.holderzone.saas.store.item.entity.domain.SubgroupDO;
import com.holderzone.saas.store.item.entity.domain.TypeDO;
import com.holderzone.saas.store.item.entity.enums.ItemQueryTypeEnum;
import com.holderzone.saas.store.item.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.service.*;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.MapStructUtils;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.item.constant.Constant.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemPkgServiceImpl
 * @date 2019/01/24 下午2:28
 * @description
 * @program holder-saas-store-item
 */
@Service
@Slf4j
public class ItemPkgServiceImpl implements IItemPkgService {
    private static final String CREATE_TYPE = "请先创建分类";

    private final ITypeService typeService;
    private final IItemService itemService;
    private final ISkuService skuService;
    private final ISubgroupService subgroupService;
    private final DynamicHelper dynamicHelper;
    private final ItemHelper itemHelper;
    private final RedisTemplate redisTemplate;
    private final IGroupMealService groupMealService;

    @Autowired
    public ItemPkgServiceImpl(IGroupMealService groupMealService, ITypeService typeService, IItemService itemService, ISkuService skuService, ISubgroupService subgroupService, DynamicHelper dynamicHelper, ItemHelper itemHelper, RedisTemplate redisTemplate) {
        this.typeService = typeService;
        this.itemService = itemService;
        this.skuService = skuService;
        this.subgroupService = subgroupService;
        this.dynamicHelper = dynamicHelper;
        this.itemHelper = itemHelper;
        this.redisTemplate = redisTemplate;
        this.groupMealService = groupMealService;
    }

    @Override
    public List<TypeSkuRespDTO> selectSkuListForPkg(ItemSingleDTO itemSingleDTO) {
        String organiseGuid = itemSingleDTO.getData();
        if (StringUtils.isEmpty(organiseGuid)) {
            throw new ParameterException("所属门店或品牌不能为空");
        }
        // 符合的分类集合
        List<TypeDO> typeDOList = typeService.list(new LambdaQueryWrapper<TypeDO>()
                .eq(TypeDO::getBrandGuid, organiseGuid)
                .or()
                .eq(TypeDO::getStoreGuid, organiseGuid));
        typeDOList.sort(Comparator.comparing(TypeDO::getSort));
        if (CollectionUtils.isEmpty(typeDOList)) {
            throw new BusinessException(CREATE_TYPE);
        }
        List<String> typeGuidList = typeDOList.stream().map(TypeDO::getGuid).collect(Collectors.toList());
        // 商品集合
        LambdaQueryWrapper<ItemDO> itemDOLambdaQueryWrapper = new LambdaQueryWrapper<>();

        itemDOLambdaQueryWrapper.ne(ModuleEntranceEnum.PUSH.code() != itemSingleDTO.getFrom(), ItemDO::getItemType, 1)
                .in(ItemDO::getTypeGuid, typeGuidList);
        if (Optional.ofNullable(itemSingleDTO).map(ItemSingleDTO::getKeywords).isPresent()) {
            itemDOLambdaQueryWrapper.like(ItemDO::getName, itemSingleDTO.getKeywords());
        }
        if (StringUtils.isEmpty(itemSingleDTO.getItemQueryType())) {
            itemSingleDTO.setItemQueryType(ItemQueryTypeEnum.ALL.getCode());
        }
        if (itemSingleDTO.getItemQueryType().equals(ItemQueryTypeEnum.BANQUET.getCode())) {
            //宴会套餐只需要查询单品和规格
            itemDOLambdaQueryWrapper
                    .in(ItemDO::getItemType, Arrays.asList(
                            ItemTypeEnum.MULTI_SPEC.getTypeCode(),
                            ItemTypeEnum.SINGLE.getTypeCode()
                    ));
        } else {
            //团餐和套餐弹窗查询只查询单品(及其规格)
            itemDOLambdaQueryWrapper
                    .in(ItemDO::getItemType, Arrays.asList(
                            ItemTypeEnum.PACKAGE.getTypeCode(),
                            ItemTypeEnum.MULTI_SPEC.getTypeCode(),
                            ItemTypeEnum.WEIGHING.getTypeCode(),
                            ItemTypeEnum.SINGLE.getTypeCode()
                    ));
        }
        itemDOLambdaQueryWrapper.eq(ItemDO::getIsDelete, FALSE).eq(ItemDO::getIsEnable, Boolean.TRUE);
        // 排序：sort正序，创建时间逆序
        itemDOLambdaQueryWrapper.orderByAsc(ItemDO::getSort).orderByDesc(ItemDO::getGmtCreate);
        return getTypeSkuRespDTOS(typeDOList, itemDOLambdaQueryWrapper);
    }

    @Override
    public List<TypeSkuRespDTO> getTypeSkuRespDTOS(List<TypeDO> typeDOList, LambdaQueryWrapper<ItemDO> itemDOLambdaQueryWrapper) {
        List<ItemDO> itemDOList = itemService.list(itemDOLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(itemDOList)) {
            throw new BusinessException("当前无可用商品");
        }
        List<String> itemGuidList = itemDOList.stream().map(ItemDO::getGuid).collect(Collectors.toList());
        // 规格集合
        List<SkuDO> skuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>()
                .eq(SkuDO::getIsEnable, Boolean.TRUE)
                .eq(SkuDO::getIsRack, 1)
                .in(SkuDO::getItemGuid, itemGuidList));
        if (CollectionUtils.isEmpty(skuDOList)) {
            throw new BusinessException("当前无可用商品");
        }
        List<TypeSkuRespDTO> typeSkuRespDTOList = MapStructUtils.INSTANCE.typeDOList2typeSkuRespDTOList(typeDOList);
        Iterator<TypeSkuRespDTO> typeSkuRespDTOIterator = typeSkuRespDTOList.iterator();
        while (typeSkuRespDTOIterator.hasNext()) {
            TypeSkuRespDTO typeDTO = typeSkuRespDTOIterator.next();
            // 当前分类下的规格集合
            List<TypeSkuRespDTO.SkuNameRespDTO> skuNameRespDTOList = new ArrayList<>();
            // 当前分类下的商品集合
            List<ItemDO> itemDOS = itemDOList.stream()
                    .filter(itemDO -> typeDTO.getTypeGuid().equals(itemDO.getTypeGuid()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(itemDOS)) {
                itemDOS.forEach(itemDO -> {
                    List<SkuDO> skuDOS = skuDOList.stream()
                            .filter(skuDO -> itemDO.getGuid().equals(skuDO.getItemGuid()))
                            .collect(Collectors.toList());
                    List<TypeSkuRespDTO.SkuNameRespDTO> skuNameRespDTOS
                            = MapStructUtils.INSTANCE.itemDOskuDOList2skuNameRespDTOList(itemDO, skuDOS);
                    skuNameRespDTOList.addAll(skuNameRespDTOS);
                });
                typeDTO.setSkuNameRespDTOList(skuNameRespDTOList);
            }
        }
        typeSkuRespDTOList.removeIf(type -> CollectionUtils.isEmpty(type.getSkuNameRespDTOList()));
        return typeSkuRespDTOList;
    }

    @Override
    public List<TypeSkuRespDTO> selectTypeSkuListForPkg(ItemSingleDTO itemSingleDTO) {
        String organiseGuid = itemSingleDTO.getData();
        if (StringUtils.isEmpty(organiseGuid)) {
            throw new ParameterException("所属门店或品牌不能为空");
        }
        // 符合的分类集合
        List<TypeDO> typeDOList = typeService.list(new LambdaQueryWrapper<TypeDO>()
                .eq(TypeDO::getBrandGuid, organiseGuid).or()
                .eq(TypeDO::getStoreGuid, organiseGuid));
        typeDOList.sort(Comparator.comparing(TypeDO::getSort));
        if (CollectionUtils.isEmpty(typeDOList)) {
            throw new BusinessException(CREATE_TYPE);
        }
        List<String> typeGuidList = typeDOList.stream().map(TypeDO::getGuid).collect(Collectors.toList());
        // 商品集合
        LambdaQueryWrapper<ItemDO> itemDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        itemDOLambdaQueryWrapper
//                .ne(ModuleEntranceEnum.PUSH.code() != itemSingleDTO.getFrom(), ItemDO::getItemType, 1)
                .in(ItemDO::getTypeGuid, typeGuidList);
        if (Optional.ofNullable(itemSingleDTO).map(ItemSingleDTO::getKeywords).isPresent()) {
            itemDOLambdaQueryWrapper.like(ItemDO::getName, itemSingleDTO.getKeywords());
        }
        //只查询套餐
        itemDOLambdaQueryWrapper
                .in(ItemDO::getItemType, Arrays.asList(
                        ItemTypeEnum.PACKAGE.getTypeCode()
                ));
        return getTypeSkuRespDTOS(typeDOList, itemDOLambdaQueryWrapper);
    }

    @Transactional
    @Override
    public List<String> saveItemPkg(ItemPkgSaveReqDTO itemPkgSaveReqDTO) {
        fixAndFillItemPkgSaveReqDTO(itemPkgSaveReqDTO);
        validateItemPkgSaveReqDTO(itemPkgSaveReqDTO);
        try {
            itemPkgSaveReqDTO.setItemGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
        } catch (IOException e) {
            throw new BusinessException("BatchIdGenerator生成商品guid失败");
        }
        ItemDO itemDO = MapStructUtils.INSTANCE.itemPkgSaveReqDTO2itemDO(itemPkgSaveReqDTO);
        itemService.save(itemDO);
        ItemInfoBO itemInfoBO = MapStructUtils.INSTANCE.itemPkgSaveReqDTO2itemInfoBO(itemPkgSaveReqDTO);
        List<String> insertSkuGuidList = skuService.saveOrUpdateAndDeleteSku(itemInfoBO);

        if (CollectionUtils.isEmpty(insertSkuGuidList)) {
            log.error(SYSTEM_ERROR);
            throw new BusinessException("套餐子菜品不能为空");
        }

        // 保存套餐分组
        subgroupService.saveOrUpdateAndDeleteSubgroup(Arrays.asList(itemInfoBO));
        return insertSkuGuidList;
    }

    @Override
    @Transactional
    public List<String> updateItemPkg(ItemPkgSaveReqDTO itemPkgSaveReqDTO) {
        fixAndFillItemPkgSaveReqDTO(itemPkgSaveReqDTO);
        validateItemPkgSaveReqDTO(itemPkgSaveReqDTO);
        ItemDO itemDO = MapStructUtils.INSTANCE.itemPkgSaveReqDTO2itemDO(itemPkgSaveReqDTO);
        boolean update = itemService.updateById(itemDO);
        if (!update) {
            throw new BusinessException(OP_FAIL);
        }
        SkuSaveReqDTO skuSaveReqDTO = itemPkgSaveReqDTO.getSkuList().get(0);
        SkuDO skuDO = MapStructUtils.INSTANCE.skuSaveReqDTO2skuDO(skuSaveReqDTO);
        skuService.updateById(skuDO);

        ItemInfoBO itemInfoBO = MapStructUtils.INSTANCE.itemPkgSaveReqDTO2itemInfoBO(itemPkgSaveReqDTO);
        boolean isSuccess = subgroupService.saveOrUpdateAndDeleteSubgroup(Arrays.asList(itemInfoBO));
        // 删除图片
        if (StringUtils.isEmpty(itemPkgSaveReqDTO.getPictureUrl())) {
            itemHelper.deletePicUrl(itemPkgSaveReqDTO.getItemGuid());
        }
        if (!isSuccess) {
            log.error(SYSTEM_ERROR);
            throw new BusinessException(SYSTEM_ERROR);
        }
        return Arrays.asList(skuDO.getGuid());
    }

    @Override
    @Transactional
    public Integer saveOrUpdateGroupMealPkg(ItemGroupMealSaveReqDTO request) {
        //先查询宴会团餐分类guid
        TypeDO typeDO = typeService.getOne(new LambdaQueryWrapper<TypeDO>()
                .eq(TypeDO::getName, "宴会套餐")
                .eq(TypeDO::getTypeFrom, 0)
                .eq(TypeDO::getStoreGuid, request.getStoreGuid())
        );
        ItemDO itemDO = MapStructUtils.INSTANCE.groupMealSaveReqDTO2itemDO(request);
        itemDO.setTypeGuid(typeDO.getGuid());
        itemDO.setHasAttr(0);
        itemDO.setIsNew(0);
        itemDO.setIsBestseller(0);
        itemDO.setIsSign(0);
        if (Objects.isNull(itemDO.getGuid())) {
            String itemGuid;
            try {
                itemGuid = String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item"));
                itemDO.setGuid(itemGuid);
            } catch (IOException e) {
                throw new BusinessException("BatchIdGenerator生成商品guid失败");
            }
        }
        //判断名称是否重复
        ItemDO existNameItemDO = itemService.getOne(new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getName, itemDO.getName())
                .eq(ItemDO::getStoreGuid, itemDO.getStoreGuid())
                .ne(ItemDO::getGuid, itemDO.getGuid()));
        if (Objects.nonNull(existNameItemDO)) {
            throw new BusinessException("商品名称重复");
        }
        //判断商品编号
        if (Optional.ofNullable(request.getSku().getCode()).isPresent() && !Strings.isNullOrEmpty(request.getSku().getCode())) {
            SkuDO existCodeSkuDO = skuService.getOne(new LambdaQueryWrapper<SkuDO>()
                    .eq(SkuDO::getCode, request.getSku().getCode())
                    .eq(SkuDO::getStoreGuid, itemDO.getStoreGuid())
                    .ne(Optional.ofNullable(request.getSku().getSkuGuid()).isPresent(), SkuDO::getGuid, request.getSku().getSkuGuid()));
            if (Objects.nonNull(existCodeSkuDO)) {
                throw new BusinessException("商品编号重复");
            }
        }
        //保存or更新宴会套餐主商品
        itemService.saveOrUpdate(itemDO);
        SkuDO skuDO = MapStructUtils.INSTANCE.skuSaveReqDTO2skuDO(request.getSku());
        skuDO.setItemGuid(itemDO.getGuid());
        if (Objects.isNull(skuDO.getGuid())) {
            try {
                skuDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "sku")));
            } catch (IOException e) {
                throw new BusinessException("BatchIdGenerator生成sku guid 失败");
            }
        }
        //保存or更新宴会套餐主商品规格sku
        skuService.saveOrUpdate(skuDO);
        //保存or更新宴会套餐子项信息
        groupMealService.saveOrUpdateGroupMeal(request.getSkuList(), itemDO.getGuid());
        return 1;
    }

    private void validateItemPkgSaveReqDTO(ItemPkgSaveReqDTO itemPkgSaveReqDTO) {
        itemHelper.validateOrganizeGuid(itemPkgSaveReqDTO.getFrom(), itemPkgSaveReqDTO.getBrandGuid(), itemPkgSaveReqDTO.getStoreGuid());
        // 对推送商品不做以下校验
        if (Integer.valueOf(2).equals(itemPkgSaveReqDTO.getItemFrom())) {
            return;
        }
        // 门店下套餐名称门店内唯一
        if (itemPkgSaveReqDTO.getFrom() == 0) {
            // 门店入口
            // 可当前门店下该名称的商品数量
            int count = itemService.count(new LambdaQueryWrapper<ItemDO>()
                    .eq(ItemDO::getStoreGuid, itemPkgSaveReqDTO.getStoreGuid())
                    .eq(ItemDO::getName, itemPkgSaveReqDTO.getName())
                    .ne(!StringUtils.isEmpty(itemPkgSaveReqDTO.getItemGuid()), ItemDO::getGuid, itemPkgSaveReqDTO.getItemGuid()));
            if (count > 0) {
                throw new ParameterException(DUPLICATE_ITEM_NAME);
            }
        } else {
            // 当前品牌下该名称的商品数量
            int count = itemService.count(new LambdaQueryWrapper<ItemDO>()
                    .eq(ItemDO::getBrandGuid, itemPkgSaveReqDTO.getBrandGuid())
                    .isNull(ItemDO::getStoreGuid)
                    .eq(ItemDO::getName, itemPkgSaveReqDTO.getName())
                    .ne(!StringUtils.isEmpty(itemPkgSaveReqDTO.getItemGuid()), ItemDO::getGuid, itemPkgSaveReqDTO.getItemGuid()));
            if (count > 0) {
                throw new ParameterException(DUPLICATE_ITEM_NAME);
            }
        }
        List<SubgroupReqDTO> subgroupList = itemPkgSaveReqDTO.getSubgroupList();
        List<String> subgroupNameList = subgroupList.stream().map(SubgroupReqDTO::getName).collect(Collectors.toList());
        Set<String> subgroupNameSet = new HashSet<>(subgroupNameList);
        if (subgroupNameSet.size() != subgroupNameList.size()) {
            throw new ParameterException("分组名称重复");
        }
        checkSubgroupList(subgroupList);
        List<SkuSaveReqDTO> skuList = itemPkgSaveReqDTO.getSkuList();
        skuService.validateSkuCodeRepeat(skuList, itemPkgSaveReqDTO.getBrandGuid(), itemPkgSaveReqDTO.getStoreGuid(), itemPkgSaveReqDTO.getFrom(), null);
        Set<BigDecimal> minOrderNumSet = skuList.stream().map(SkuSaveReqDTO::getMinOrderNum).collect(Collectors.toSet());
        boolean notIntValue = minOrderNumSet.stream().anyMatch(minOrderNum -> (minOrderNum.compareTo(new BigDecimal(minOrderNum.intValue())) != 0));
        if (notIntValue) {
            throw new ParameterException("套餐的起卖数必须为整数");
        }
    }

    private void checkSubgroupList(List<SubgroupReqDTO> subgroupList) {
        subgroupList.forEach(subgroupReqDTO -> {
            // 该分组下的规格集合
            List<SubItemSkuReqDTO> subItemSkuList = subgroupReqDTO.getSubItemSkuList();
            List<String> thisSubgroupSkuGuidList = subItemSkuList.stream().map(SubItemSkuReqDTO::getSkuGuid).collect(Collectors.toList());
            Set<String> skuGuidSet = new HashSet<>(thisSubgroupSkuGuidList);
            //相同分组下，商品sku不能重复
            if (skuGuidSet.size() != thisSubgroupSkuGuidList.size()) {
                throw new ParameterException("商品规格重复");
            }
            // 检测设置默认值与商品选择数量的关系是否可行
            if (subgroupReqDTO.getPickNum() != 0) {
                // 默认选定的规格数
                long defaultNum = subItemSkuList.stream().filter(subItemSkuReqDTO -> subItemSkuReqDTO.getIsDefault() == 1).count();
                if (subgroupReqDTO.getPickNum().compareTo((int) defaultNum) < 0) {
                    throw new BusinessException(DEFAULT_NUM_CANNOT_GT_PICK_NUM);
                }
                // 所有分组下规格均不能重复选择
                boolean cannotRepeat = subItemSkuList.stream().allMatch(subItemSkuReqDTO -> subItemSkuReqDTO.getIsRepeat() == 0);
                if (cannotRepeat && (subgroupReqDTO.getPickNum() > subItemSkuList.size())) {
                    throw new BusinessException(PICK_NUM_GT_ACTUAL_NUM);
                }
            }
        });
    }

    /**
     * 完善字段
     *
     * @param itemPkgSaveReqDTO
     */
    private void fixAndFillItemPkgSaveReqDTO(ItemPkgSaveReqDTO itemPkgSaveReqDTO) {
        itemPkgSaveReqDTO.setHasAttr(0);
        itemPkgSaveReqDTO.setItemType(1);
        if (itemPkgSaveReqDTO.getItemFrom() == null) {
            itemPkgSaveReqDTO.setItemFrom(itemPkgSaveReqDTO.getFrom());
        }
        if (itemPkgSaveReqDTO.getSort() == null) {
            int sort = itemHelper.sort(itemPkgSaveReqDTO.getItemFrom(), itemPkgSaveReqDTO.getBrandGuid(), itemPkgSaveReqDTO.getStoreGuid());
            itemPkgSaveReqDTO.setSort(sort);
        }
        List<SubgroupReqDTO> subgroupList = getSubgroupReqDTOList(itemPkgSaveReqDTO);
        // 若只有一个分组，则设置其名称为‘默认分组’
        if (subgroupList.size() == 1) {
            SubgroupReqDTO subgroupReqDTO = subgroupList.get(0);
            subgroupReqDTO.setName(DEFAULT_GROUP);
        }

    }

    @NotNull
    private List<SubgroupReqDTO> getSubgroupReqDTOList(ItemPkgSaveReqDTO itemPkgSaveReqDTO) {
        List<SubgroupReqDTO> subgroupList = itemPkgSaveReqDTO.getSubgroupList();
        for (int i = 0; i < subgroupList.size(); i++) {
            SubgroupReqDTO subgroupReqDTO = subgroupList.get(i);
            subgroupReqDTO.setSort(i);
            if (subgroupReqDTO.getIsFixSubgroup() == 1) {
                subgroupReqDTO.setPickNum(0);
                List<SubItemSkuReqDTO> subItemSkuList = subgroupReqDTO.getSubItemSkuList();
                subItemSkuList.forEach(subItemSkuReqDTO -> {
                    subItemSkuReqDTO.setIsDefault(1);
                    subItemSkuReqDTO.setIsRepeat(0);
                    subItemSkuReqDTO.setAddPrice(BigDecimal.ZERO);
                });
            } else {
                List<SubItemSkuReqDTO> subItemSkuList = subgroupReqDTO.getSubItemSkuList();
                for (SubItemSkuReqDTO subItemSkuReqDTO : subItemSkuList) {
                    if (subItemSkuReqDTO.getAddPrice() == null) {
                        subItemSkuReqDTO.setAddPrice(BigDecimal.ZERO);
                    }
                }
            }
            List<SubItemSkuReqDTO> subItemSkuList = subgroupReqDTO.getSubItemSkuList();
            for (int j = 0; j < subItemSkuList.size(); j++) {
                SubItemSkuReqDTO subItemSkuReqDTO = subItemSkuList.get(j);
                subItemSkuReqDTO.setSort(j);
            }
        }
        return subgroupList;
    }

    @Override
    public void removeAllRelationStore(ItemPkgSaveReqDTO itemPkgSaveReqDTO, List<String> insertSkuGuidList) {
        //移除所有门店
        itemService.removeAllRelationStore(itemPkgSaveReqDTO, insertSkuGuidList, itemPkgSaveReqDTO.getItemGuid(), itemPkgSaveReqDTO.getBrandGuid());
    }

    /**
     * 根据商品guid获取商品及套餐信息
     * 如果有父id则会将父商品也查出来
     *
     * @param itemStringListDTO 商品guid
     * @return 商品及套餐信息
     */
    @Override
    public List<ItemInfoRespDTO> listPkgItemInfo(ItemStringListDTO itemStringListDTO) {
        List<String> itemGuidList = itemStringListDTO.getDataList();
        List<ItemDO> itemDOList = itemService.list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, itemGuidList));
        if (CollectionUtils.isEmpty(itemDOList)) {
            log.info("商品查询为空 itemGuidList={}", itemGuidList);
            return new ArrayList<>();
        }
        List<ItemInfoRespDTO> respDTOS = MapStructUtils.INSTANCE.itemDOList2ItemInfoRespDTOList(itemDOList);
        List<SubgroupBO> subgroupBOList = subgroupService.selectSubgroupListByItemGuidList(itemGuidList);
        if (CollectionUtils.isEmpty(subgroupBOList)) {
            return respDTOS;
        }
        List<SubgroupWebRespDTO> subgroupWebRespDTOS = MapStructUtils.INSTANCE.subgroupBOList2subgroupWebRespDTOList(subgroupBOList);
        Map<String, List<SubgroupWebRespDTO>> subGroupMap = subgroupWebRespDTOS.stream()
                .collect(Collectors.groupingBy(SubgroupWebRespDTO::getItemGuid));
        List<String> itemList = subgroupWebRespDTOS.stream()
                .flatMap(s -> s.getSubItemSkuList().stream().map(SubItemSkuWebRespDTO::getItemGuid))
                .collect(Collectors.toList());
        List<String> skuList = subgroupWebRespDTOS.stream()
                .flatMap(s -> s.getSubItemSkuList().stream().map(SubItemSkuWebRespDTO::getSkuGuid))
                .collect(Collectors.toList());
        Collection<ItemDO> itemDOS = itemService.listByIds(itemList);
        Collection<SkuDO> skuDOS = skuService.listByIds(skuList);
        Map<String, String> typeMap = itemDOS.stream()
                .collect(Collectors.toMap(ItemDO::getGuid, ItemDO::getTypeGuid));
        Collection<TypeDO> typeDOS = typeService.listByIds(typeMap.values());
        Map<String, String> typeNameMap = typeDOS.stream()
                .collect(Collectors.toMap(TypeDO::getGuid, TypeDO::getName));
        Map<String, String> itemNameMap = itemDOS.stream()
                .collect(Collectors.toMap(ItemDO::getGuid, ItemDO::getName));
        Map<String, String> skuNameMap = skuDOS.stream()
                .collect(Collectors.toMap(SkuDO::getGuid, SkuDO::getName));
        Map<String, SkuDO> skuMap = skuDOS.stream()
                .collect(Collectors.toMap(SkuDO::getGuid, Function.identity(), (key1, key2) -> key1));

        // 最终处理
        respDTOS.forEach(resp -> {
            if (Objects.equals(ItemTypeEnum.PACKAGE.getTypeCode(), resp.getItemType())) {
                List<SubgroupWebRespDTO> subgroupList = subGroupMap.get(resp.getItemGuid());
                if (!CollectionUtils.isEmpty(subgroupList)) {
                    // 设置子商品typeGuid，itemName,skuName
                    subgroupList.forEach(sub ->
                            sub.getSubItemSkuList().forEach(i -> {
                                i.setTypeGuid(typeMap.get(i.getItemGuid()));
                                i.setItemName(itemNameMap.get(i.getItemGuid()));
                                i.setSkuName(skuNameMap.get(i.getSkuGuid()));
                                i.setTypeName(typeNameMap.get(i.getTypeGuid()));
                                i.setSalePrice(Optional.ofNullable(skuMap.get(i.getSkuGuid())).orElse(new SkuDO()).getSalePrice());
                                i.setUnit(Optional.ofNullable(skuMap.get(i.getSkuGuid())).orElse(new SkuDO()).getUnit());
                            }));
                    resp.setSubgroupList(subgroupList);
                }
            }
        });
        return respDTOS;
    }

    /**
     * 外卖批量绑定查询品牌库商品
     */
    @Override
    public List<TypeSkuRespDTO> selectList(ItemSingleDTO itemSingleDTO) {
        String brandGuid = itemSingleDTO.getData();
        if (StringUtils.isEmpty(brandGuid)) {
            throw new ParameterException("品牌不能为空");
        }

        // 符合的分类集合
        List<TypeDO> typeDOList = typeService.list(new LambdaQueryWrapper<TypeDO>()
                .eq(TypeDO::getBrandGuid, brandGuid)
                .orderByAsc(TypeDO::getSort)
        );
        if (CollectionUtils.isEmpty(typeDOList)) {
            throw new BusinessException(CREATE_TYPE);
        }
        List<String> typeGuidList = typeDOList.stream()
                .map(TypeDO::getGuid)
                .distinct()
                .collect(Collectors.toList());

        // 商品集合
        LambdaQueryWrapper<ItemDO> itemQueryWrapper = new LambdaQueryWrapper<>();
        itemQueryWrapper.in(ItemDO::getTypeGuid, typeGuidList);
        if (Optional.of(itemSingleDTO).map(ItemSingleDTO::getKeywords).isPresent()) {
            itemQueryWrapper.like(ItemDO::getName, itemSingleDTO.getKeywords());
        }
        //团餐和套餐弹窗查询只查询单品(及其规格)
        itemQueryWrapper.in(ItemDO::getItemType, Arrays.asList(
                ItemTypeEnum.PACKAGE.getTypeCode(),
                ItemTypeEnum.MULTI_SPEC.getTypeCode(),
                ItemTypeEnum.SINGLE.getTypeCode()
        ));
        itemQueryWrapper.eq(ItemDO::getIsDelete, FALSE);
        itemQueryWrapper.eq(ItemDO::getIsEnable, Boolean.TRUE);
        // 排序：sort正序，创建时间逆序
        itemQueryWrapper.orderByAsc(ItemDO::getSort);
        itemQueryWrapper.orderByDesc(ItemDO::getGmtCreate);
        List<TypeSkuRespDTO> typeSkuRespDTOList = getTypeSkuRespDTOS(typeDOList, itemQueryWrapper);

        // 过滤可选套餐、宴会套餐和称重商品
        List<String> packageItemGuid = typeSkuRespDTOList.stream()
                .flatMap(t -> t.getSkuNameRespDTOList().stream())
                .filter(i -> Objects.equals(ItemTypeEnum.PACKAGE.getTypeCode(), i.getItemType()))
                .map(TypeSkuRespDTO.SkuNameRespDTO::getItemGuid)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(packageItemGuid)) {
            List<SubgroupDO> subgroupDOList = subgroupService.list(new LambdaQueryWrapper<SubgroupDO>()
                    .in(SubgroupDO::getItemGuid, packageItemGuid));
            List<String> notFixedGuidList = subgroupDOList.stream()
                    .filter(sub -> !Constant.NUMBER_ZERO.equals(sub.getPickNum()))
                    .map(SubgroupDO::getItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(notFixedGuidList)) {
                typeSkuRespDTOList.forEach(typeSku ->
                        typeSku.getSkuNameRespDTOList().removeIf(s -> notFixedGuidList.contains(s.getItemGuid())));
            }
        }
        return typeSkuRespDTOList;
    }
}
