package com.holderzone.saas.store.item.entity.query;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SetMealEstimateQuery
 * @date 2019/05/29 11:02
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
public class SetMealEstimateQuery {

    /**
     * 套餐商品guid
     */
    private String itemGuid;
    /**
     * 套餐是否为固定套餐 0：固定
     */
    private Integer pickNum;
    /**
     * 套餐下子项skuguid
     */
    private String skuGuid;
    /**
     * 套餐下子项估清是否手动售罄 1：否 2：是
     */
    private Integer isSoldOut;
    /**
     * 套餐下子项估清是否限量 1：否 2：是
     */
    private Integer isTheLimit;
    /**
     * 套餐下子项估清当前库存
     */
    private BigDecimal residueQuantity;

}
