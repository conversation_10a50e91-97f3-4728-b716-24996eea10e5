package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品模板-菜单
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsi_item_t_menu")
public class ItemTMenuDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 自增长
     */
    private Long id;

    /**
     * 业务主键
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 外键 商品模板guid
     */
    private String templateGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否逻辑删除 0：否  1：是
     */
    @TableLogic
    private Integer isDelete;


}
