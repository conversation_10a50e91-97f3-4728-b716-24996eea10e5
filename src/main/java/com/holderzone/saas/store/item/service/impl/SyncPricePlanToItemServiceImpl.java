package com.holderzone.saas.store.item.service.impl;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.item.dto.PushRecordStatusUpdateReqDTO;
import com.holderzone.saas.store.item.dto.SyncItemDTO;
import com.holderzone.saas.store.item.dto.UpdateTerminalStatusDTO;
import com.holderzone.saas.store.item.entity.domain.RSkuSubgroupDO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import com.holderzone.saas.store.item.entity.domain.SubgroupDO;
import com.holderzone.saas.store.item.entity.enums.PricePlanPushStatusEnum;
import com.holderzone.saas.store.item.feign.ItemClientService;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SyncPricePlanToItemServiceImpl implements SyncPricePlanToItemService {
    // 菜品的来源 , 从价格方案过来的为4
    private static final int ITEM_FROM = 4;

    // 来自于品牌推送
    private static final int FROM_BRAND = 1;

    @Autowired
    private IItemService iItemService;

    @Autowired
    private IItemPkgService itemPkgService;

    @Autowired
    private IPricePlanItemService iPricePlanItemService;

    @Autowired
    private IPricePlanPushRecordService iPricePlanPushRecordService;

    @Autowired
    private ItemHelper itemHelper;

    @Autowired
    private ItemClientService itemClientService;

    @Autowired
    private ISkuService iSkuService;

    @Autowired
    private ISubgroupService iSubgroupService;

    @Autowired
    private IRSkuSubgroupService irSkuSubgroupService;

    @Autowired
    private ICommonService iCommonService;

    @Autowired
    private IRedissonCacheService iRedissonCacheService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addPricePlanItem(SyncItemDTO itemDTO, String storeGuid, String brandGuid, String pricePlanGuid) {
        String skuGuid = itemDTO.getSkuGuid();
        String itemGuid = itemDTO.getItemGuid();
        BigDecimal salePrice = itemDTO.getSalePrice();
        BigDecimal memberPrice = itemDTO.getMemberPrice();
        // 先删除旧有的数据 删除依据是当前店铺store_guid的parent_guid是当前操作的item_guid(该item_guid是来自于品牌的)
        deleteItemByParentGuid(itemDTO.getItemGuid(), storeGuid);
        ItemReqDTO itemReqDTO = getItemReqDTO(itemGuid, storeGuid);
        itemReqDTO.setFrom(FROM_BRAND);
        //itemReqDTO.setItemFrom(ITEM_FROM);
        List<SkuSaveReqDTO> skuList = itemReqDTO.getSkuList();
        List<SkuSaveReqDTO> skuSaveReqDTOList = skuList.stream().peek(skuSaveReqDTO -> {
            SyncItemDTO syncItem = iPricePlanItemService.getSyncItem(itemGuid, skuSaveReqDTO.getSkuGuid());
            if (Objects.nonNull(syncItem)) {
                if (Objects.nonNull(syncItem.getSalePrice())) {
                    skuSaveReqDTO.setSalePrice(syncItem.getSalePrice());
                } else {
                    skuSaveReqDTO.setSalePrice(salePrice);
                }
                if (Objects.nonNull(syncItem.getMemberPrice())) {
                    skuSaveReqDTO.setMemberPrice(syncItem.getMemberPrice());
                } else {
                    skuSaveReqDTO.setMemberPrice(memberPrice);
                }
            } else {
                skuSaveReqDTO.setSalePrice(salePrice);
                skuSaveReqDTO.setMemberPrice(memberPrice);
            }
            skuSaveReqDTO.setIsRack(syncItem.getIsRack());
            skuSaveReqDTO.setIsJoinAio(syncItem.getIsJoinAio());
            skuSaveReqDTO.setIsJoinPad(syncItem.getIsJoinPad());
            skuSaveReqDTO.setIsJoinPos(syncItem.getIsJoinPos());
            String cacheKey = itemGuid + ":" + syncItem.getSkuGuid();
            iRedissonCacheService.setCache(cacheKey, syncItem, 20, TimeUnit.MINUTES);
        }).collect(Collectors.toList());
        itemReqDTO.setSkuList(skuSaveReqDTOList);
        // 更新item信息  如果原始Item的item_from是来自于品牌的话,则会新增一条记录 即:将原始的Item作为模板
        List<String> saveOrUpdateSkuGuidList = iItemService.updateItem(itemReqDTO);
        if (!CollectionUtils.isEmpty(itemReqDTO.getDataList())) {
            // 推送
            itemHelper.distributeItems2Stores(itemReqDTO, saveOrUpdateSkuGuidList, true);
        }
        // 更新需要推送的端
        skuSaveReqDTOList.forEach(skuSaveReqDTO -> {
            Integer isJoinAio = skuSaveReqDTO.getIsJoinAio();
            Integer isJoinPad = skuSaveReqDTO.getIsJoinPad();
            Integer isJoinPos = skuSaveReqDTO.getIsJoinPos();
            UpdateTerminalStatusDTO updateTerminalStatusDTO = new UpdateTerminalStatusDTO();
            updateTerminalStatusDTO.setIsJoinAio(isJoinAio);
            updateTerminalStatusDTO.setIsJoinPad(isJoinPad);
            updateTerminalStatusDTO.setIsJoinPos(isJoinPos);
            updateTerminalStatusDTO.setStoreGuid(skuSaveReqDTO.getStoreGuid());
            updateTerminalStatusDTO.setParentGuid(skuSaveReqDTO.getSkuGuid());
            updateTerminalStatusDTO.setIsJoinMiniAppMall(skuSaveReqDTO.getIsJoinMiniAppMall());
            updateTerminalStatusDTO.setIsJoinMiniAppTakeaway(skuSaveReqDTO.getIsJoinMiniAppTakeaway());
            updateTerminalStatusDTO.setIsJoinStore(skuSaveReqDTO.getIsJoinStore());
            iSkuService.updateTerminalStatus(updateTerminalStatusDTO);
        });
        // 将品牌所属的item_guid的store_guid设置为null
        updateOriginItemStoreGuid(itemGuid, storeGuid, brandGuid);
        skuList.forEach(skuSaveReqDTO -> {
            String cacheKey = itemGuid + ":" + skuSaveReqDTO.getSkuGuid();
            SyncItemDTO cache = iRedissonCacheService.getCache(cacheKey);
            BigDecimal originalSalePrice = cache.getOriginalSalePrice();
            BigDecimal originalMemberPrice = cache.getOriginalMemberPrice();
            updateOriginPriceStoreGuid(itemGuid, storeGuid, skuGuid, originalSalePrice, originalMemberPrice);
            iRedissonCacheService.removeCache(cacheKey);
        });
        return iItemService.getGuidByStoreGuidAndParentGuid(storeGuid, itemDTO.getItemGuid());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addPricePlanPkgItem(SyncItemDTO itemDTO, String storeGuid, String brandGuid, String pricePlanGuid) {
        String skuGuid = itemDTO.getSkuGuid();
        String itemGuid = itemDTO.getItemGuid();
        BigDecimal salePrice = itemDTO.getSalePrice();
        BigDecimal memberPrice = itemDTO.getMemberPrice();
        // 先删除旧有的数据 删除依据是当前店铺store_guid的parent_guid是当前操作的item_guid(该item_guid是来自于品牌的)
        String oldStoreItemGuid = deleteItemByParentGuid(itemDTO.getItemGuid(), storeGuid);
        ItemPkgSaveReqDTO itemPkgSaveReqDTO = getItemPkgSaveReqDTO(itemGuid, storeGuid);
        itemPkgSaveReqDTO.setFrom(FROM_BRAND);
        //itemPkgSaveReqDTO.setItemFrom(1);
        List<SkuSaveReqDTO> skuList = itemPkgSaveReqDTO.getSkuList();
        List<SkuSaveReqDTO> skuSaveReqDTOList = skuList.stream().peek(skuSaveReqDTO -> {
            SyncItemDTO syncItem = iPricePlanItemService.getSyncItem(itemGuid, skuSaveReqDTO.getSkuGuid());
            if (Objects.nonNull(syncItem)) {
                if (Objects.nonNull(syncItem.getSalePrice())) {
                    skuSaveReqDTO.setSalePrice(syncItem.getSalePrice());
                } else {
                    skuSaveReqDTO.setSalePrice(salePrice);
                }
                if (Objects.nonNull(syncItem.getMemberPrice())) {
                    skuSaveReqDTO.setMemberPrice(syncItem.getMemberPrice());
                } else {
                    skuSaveReqDTO.setMemberPrice(memberPrice);
                }
            } else {
                skuSaveReqDTO.setSalePrice(salePrice);
                skuSaveReqDTO.setMemberPrice(memberPrice);
            }
            skuSaveReqDTO.setIsRack(syncItem.getIsRack());
            skuSaveReqDTO.setIsJoinAio(syncItem.getIsJoinAio());
            skuSaveReqDTO.setIsJoinPad(syncItem.getIsJoinPad());
            skuSaveReqDTO.setIsJoinPos(syncItem.getIsJoinPos());
            String cacheKey = itemGuid + ":" + syncItem.getSkuGuid();
            iRedissonCacheService.setCache(cacheKey, syncItem, 20, TimeUnit.MINUTES);
            iRedissonCacheService.removeCache(cacheKey);
        }).collect(Collectors.toList());
        itemPkgSaveReqDTO.setSkuList(skuSaveReqDTOList);
        // 处理subgroup中的关系
        if (!StrUtil.isEmpty(oldStoreItemGuid)) {
            dealPkgInfo(oldStoreItemGuid, storeGuid);
        }
        // 更新item信息 如果原始Item的item_from是来自于品牌的话,则会新增一条记录 即:将原始的Item作为模板
        List<String> insertSkuGuidList = itemPkgService.updateItemPkg(itemPkgSaveReqDTO);
        if (!CollectionUtils.isEmpty(itemPkgSaveReqDTO.getDataList())) {
            // 直接推送至安卓端或者推送至门店
            itemHelper.distributeItems2Stores(itemPkgSaveReqDTO, insertSkuGuidList, true);
        }
        // 更新需要推送的端
        skuSaveReqDTOList.forEach(skuSaveReqDTO -> {
            Integer isJoinAio = skuSaveReqDTO.getIsJoinAio();
            Integer isJoinPad = skuSaveReqDTO.getIsJoinPad();
            Integer isJoinPos = skuSaveReqDTO.getIsJoinPos();
            UpdateTerminalStatusDTO updateTerminalStatusDTO = new UpdateTerminalStatusDTO();
            updateTerminalStatusDTO.setIsJoinAio(isJoinAio);
            updateTerminalStatusDTO.setIsJoinPad(isJoinPad);
            updateTerminalStatusDTO.setIsJoinPos(isJoinPos);
            updateTerminalStatusDTO.setStoreGuid(skuSaveReqDTO.getStoreGuid());
            updateTerminalStatusDTO.setParentGuid(skuSaveReqDTO.getSkuGuid());
            updateTerminalStatusDTO.setIsJoinMiniAppMall(skuSaveReqDTO.getIsJoinMiniAppMall());
            updateTerminalStatusDTO.setIsJoinMiniAppTakeaway(skuSaveReqDTO.getIsJoinMiniAppTakeaway());
            updateTerminalStatusDTO.setIsJoinStore(skuSaveReqDTO.getIsJoinStore());
            iSkuService.updateTerminalStatus(updateTerminalStatusDTO);
        });
        // 将品牌所属的item_guid的store_guid设置为null
        updateOriginItemStoreGuid(itemGuid, storeGuid, brandGuid);
        skuList.forEach(skuSaveReqDTO -> {
            String cacheKey = itemGuid + ":" + skuSaveReqDTO.getSkuGuid();
            SyncItemDTO cache = iRedissonCacheService.getCache(cacheKey);
            BigDecimal originalSalePrice = cache.getOriginalSalePrice();
            BigDecimal originalMemberPrice = cache.getOriginalMemberPrice();
            updateOriginPriceStoreGuid(itemGuid, storeGuid, skuGuid, originalSalePrice, originalMemberPrice);
            iRedissonCacheService.removeCache(cacheKey);
        });
        // 更新相关的信息存入关联表中 , 以便后期对一些数据做处理
        String newStoreItemGuid = iItemService.getGuidByStoreGuidAndParentGuid(storeGuid, itemDTO.getItemGuid());
        if (!StrUtil.isEmpty(oldStoreItemGuid)) {
            updateSubGroupItemToNewItem(oldStoreItemGuid, storeGuid, newStoreItemGuid);
        }
        setOldItemGuidAndNewItemGuidMapping(oldStoreItemGuid, newStoreItemGuid, storeGuid);
        updateSubGroup(itemDTO.getItemGuid(), newStoreItemGuid, storeGuid);
        return newStoreItemGuid;
    }

    /*private void deletePkgInfo(SyncItemDTO itemDTO, String storeGuid, String brandGuid) {
        String guid = iItemService.getGuidByStoreGuidAndParentGuid(storeGuid, itemDTO.getItemGuid());
        // 删除hsi_subgroup中的数据
        HashMap<String, Object> condition = new HashMap<>();
        condition.put("item_guid", guid);
        // condition.put("brand_guid", brandGuid);
        condition.put("store_guid", storeGuid);
        SubgroupDO subgroupDO = iSubgroupService.getOne(new QueryWrapper<SubgroupDO>().allEq(condition));
        if (!Objects.isNull(subgroupDO)) {
            String subgroupGuid = subgroupDO.getGuid();
            iSubgroupService.update(new UpdateWrapper<SubgroupDO>().set("is_delete", 1).eq("parent_guid", subgroupGuid).eq("store_guid", storeGuid));
            irSkuSubgroupService.update(new UpdateWrapper<RSkuSubgroupDO>().set("is_delete", 1).eq("subgroup_guid", subgroupGuid));
        }
    }*/

    @Override
    public String deleteItemByParentGuid(String parentGuid, String storeGuid) {
        List<Map<String, Object>> itemInfoListByParentId = iItemService.getItemInfoListByParentId(parentGuid, storeGuid);
        String oldGuid = "";
        if (!itemInfoListByParentId.isEmpty()) {
            for (Map<String, Object> data : itemInfoListByParentId) {
                oldGuid = (String) data.getOrDefault("guid", "");
                Long itemFrom = (Long) data.getOrDefault("item_from", -1);
                if (!StrUtil.isEmpty(oldGuid) && itemFrom == 2) {
                    iItemService.deleteByGuidAndFrom(oldGuid, itemFrom, storeGuid);
                    iSkuService.deleteSkuByItemGuidAndStoreGuid(oldGuid, storeGuid);
                }
            }
        }
        return oldGuid;
    }

    @Override
    public String updatePushRecordData(PushRecordStatusUpdateReqDTO recordStatusUpdateReqDTO) {
        iPricePlanPushRecordService.updatePushStatus(recordStatusUpdateReqDTO);
        return recordStatusUpdateReqDTO.getNewItemGuid();
    }

    /**
     * 获取套餐的ReqDTO
     *
     * @param itemGuid         菜品itemGuid
     * @param organizationGuid 店铺Guid
     * @return
     */
    private ItemPkgSaveReqDTO getItemPkgSaveReqDTO(String itemGuid, String organizationGuid) {
        ItemSingleDTO singleDTO = new ItemSingleDTO();
        singleDTO.setData(itemGuid);
        ItemInfoRespDTO itemInfo = iItemService.getItemInfo(singleDTO);
        ItemPkgSaveReqDTO itemPkgSaveReqDTO = new ItemPkgSaveReqDTO();
        itemPkgSaveReqDTO.setItemGuid(itemGuid);
        if (Objects.isNull(itemInfo)) {
            log.info("没有对应的套餐item数据{}", itemGuid);
            // return itemPkgSaveReqDTO;
        } else {
            itemPkgSaveReqDTO.setBrandGuid(itemInfo.getBrandGuid());
            itemPkgSaveReqDTO.setDescription(itemInfo.getDescription());
            itemPkgSaveReqDTO.setIsBestseller(itemInfo.getIsBestseller());
            itemPkgSaveReqDTO.setIsNew(itemInfo.getIsNew());
            itemPkgSaveReqDTO.setIsSign(itemInfo.getIsSign());
            itemPkgSaveReqDTO.setItemType(itemInfo.getItemType());
            itemPkgSaveReqDTO.setName(itemInfo.getName());
            itemPkgSaveReqDTO.setPictureUrl(itemInfo.getPictureUrl());
            itemPkgSaveReqDTO.setPinyin(itemInfo.getPinyin());

            itemPkgSaveReqDTO.setSkuList(getSkuDTO(itemInfo.getSkuList()));
            itemPkgSaveReqDTO.setSort(itemInfo.getSort());
            itemPkgSaveReqDTO.setTypeGuid(itemInfo.getTypeGuid());
            List<SubgroupReqDTO> subGroupListDTO = getSubGroupListDTO(itemInfo.getSubgroupList());
            itemPkgSaveReqDTO.setSubgroupList(subGroupListDTO);
        }
        itemPkgSaveReqDTO.setStoreGuid(organizationGuid);
        ArrayList<String> storeList = new ArrayList<>();
        storeList.add(organizationGuid);
        itemPkgSaveReqDTO.setDataList(storeList);
        return itemPkgSaveReqDTO;
    }

    /**
     * 获取单品的ReqDTO
     *
     * @param itemGuid         菜品itemGuid
     * @param organizationGuid 店铺Guid
     * @return
     */
    private ItemReqDTO getItemReqDTO(String itemGuid, String organizationGuid) {
        ItemSingleDTO singleDTO = new ItemSingleDTO();
        singleDTO.setData(itemGuid);
        ItemInfoRespDTO itemInfo = iItemService.getItemInfo(singleDTO);
        ItemReqDTO itemReqDTO = new ItemReqDTO();
        if (Objects.isNull(itemInfo)) {
            log.info("没有对应的item数据{}", itemGuid);
            //  return itemReqDTO;
        } else {
            itemReqDTO.setItemFrom(1);
            itemReqDTO.setItemGuid(itemInfo.getItemGuid());
            itemReqDTO.setBrandGuid(itemInfo.getBrandGuid());
            itemReqDTO.setTypeGuid(itemInfo.getTypeGuid());
            itemReqDTO.setItemType(itemInfo.getItemType());
            itemReqDTO.setHasAttr(itemInfo.getHasAttr());
            itemReqDTO.setName(itemInfo.getName());
            itemReqDTO.setPinyin(itemInfo.getPinyin());
            itemReqDTO.setSort(itemInfo.getSort());
            itemReqDTO.setDescription(itemInfo.getDescription());
            itemReqDTO.setPictureUrl(itemInfo.getPictureUrl());
            itemReqDTO.setIsBestseller(itemInfo.getIsBestseller());
            itemReqDTO.setIsNew(itemInfo.getIsNew());
            itemReqDTO.setIsSign(itemInfo.getIsSign());
            itemReqDTO.setUpCount(itemInfo.getUpCount());
            itemReqDTO.setDownCount(itemInfo.getDownCount());
            List<SkuInfoRespDTO> skuList = itemInfo.getSkuList();
            itemReqDTO.setSkuList(getSkuDTO(skuList));
            itemReqDTO.setAttrGroupList(getAttrGroupList(itemInfo.getAttrGroupList()));
        }
        ArrayList<String> storeList = new ArrayList<>();
        storeList.add(organizationGuid);
        itemReqDTO.setDataList(storeList);
        itemReqDTO.setStoreGuid(organizationGuid);
        return itemReqDTO;
    }

    /**
     * 获取item所属的sku
     *
     * @param skuList
     * @return
     * <AUTHOR> chen
     * @version 1.0
     * @since 2020-10-27
     */
    private List<SkuSaveReqDTO> getSkuDTO(List<SkuInfoRespDTO> skuList) {
        ArrayList<SkuSaveReqDTO> skuSaveReqDTOS = new ArrayList<>();
        for (SkuInfoRespDTO skuRespDTO : skuList) {
            SkuSaveReqDTO skuSaveReqDTO = new SkuSaveReqDTO();
            skuSaveReqDTO.setItemGuid(skuRespDTO.getItemGuid());
            skuSaveReqDTO.setSkuGuid(skuRespDTO.getSkuGuid());
            skuSaveReqDTO.setName(skuRespDTO.getName());// - 新增
            skuSaveReqDTO.setMemberPrice(skuRespDTO.getMemberPrice());// - 新增
            skuSaveReqDTO.setSalePrice(skuRespDTO.getSalePrice());// - 新增
            skuSaveReqDTO.setIsWholeDiscount(skuRespDTO.getIsWholeDiscount()); // -- 新增
            skuSaveReqDTO.setIsMemberDiscount(skuRespDTO.getIsMemberDiscount());
            skuSaveReqDTO.setMinOrderNum(skuRespDTO.getMinOrderNum()); // - 新增
            skuSaveReqDTO.setUnit(skuRespDTO.getUnit());// - 新增
            skuSaveReqDTO.setIsRack(skuRespDTO.getIsRack()); // -- 新增
            skuSaveReqDTO.setIsJoinBuffet(skuRespDTO.getIsJoinBuffet()); // --新增
            skuSaveReqDTO.setIsJoinWeChat(skuRespDTO.getIsJoinWeChat()); // -- 新增
            skuSaveReqDTO.setIsJoinMt(skuRespDTO.getIsJoinMt());
            skuSaveReqDTO.setIsJoinElm(skuRespDTO.getIsJoinElm());
            skuSaveReqDTO.setIsOpenStock(skuRespDTO.getIsOpenStock());
            skuSaveReqDTO.setBrandGuid(skuRespDTO.getBrandGuid());
            skuSaveReqDTO.setSkuFrom(1);
            skuSaveReqDTO.setIsJoinMiniAppMall(skuRespDTO.getIsJoinMiniAppMall());
            skuSaveReqDTO.setIsJoinMiniAppTakeaway(skuRespDTO.getIsJoinMiniAppTakeaway());
            skuSaveReqDTO.setIsJoinStore(skuRespDTO.getIsJoinStore());
            skuSaveReqDTOS.add(skuSaveReqDTO);
        }
        return skuSaveReqDTOS;
    }

    /**
     * 获取Item中属性组的配置列表
     *
     * @param attrGroupWebRespDTOList
     * @return
     * <AUTHOR> chen
     * @version 1.0
     * @since 2020-10-27
     */
    private List<ItemAttrGroupReqDTO> getAttrGroupList(List<AttrGroupWebRespDTO> attrGroupWebRespDTOList) {
        ArrayList<ItemAttrGroupReqDTO> itemAttrGroupReqDTOS = new ArrayList<>();
        for (AttrGroupWebRespDTO attrGroupWebRespDTO : attrGroupWebRespDTOList) {
            ItemAttrGroupReqDTO itemAttrGroupReqDTO = new ItemAttrGroupReqDTO();
            itemAttrGroupReqDTO.setAttrGroupGuid(attrGroupWebRespDTO.getAttrGroupGuid());
            // itemAttrGroupReqDTO.setBrandGuid("6712166044938534912"); -- "brandGuid":"6712166044938534912",
            // itemAttrGroupReqDTO.setName("name"); -- "name":"口味"
            // itemAttrGroupReqDTO.setSort(3);
            // itemAttrGroupReqDTO.setIsEnable(1);
            itemAttrGroupReqDTO.setItemGuid(attrGroupWebRespDTO.getItemGuid());
            itemAttrGroupReqDTO.setItemAttrGroupGuid(attrGroupWebRespDTO.getItemAttrGroupGuid());
            itemAttrGroupReqDTO.setIsRequired(attrGroupWebRespDTO.getIsRequired());
            itemAttrGroupReqDTO.setIsMultiChoice(attrGroupWebRespDTO.getIsMultiChoice());
            itemAttrGroupReqDTO.setWithDefault(attrGroupWebRespDTO.getWithDefault());
            // itemAttrGroupReqDTO.setAttrGroupFrom(1); -- "attrGroupFrom":1,
            itemAttrGroupReqDTO.setAttrList(getAttrDTOList(attrGroupWebRespDTO.getAttrList()));
            itemAttrGroupReqDTOS.add(itemAttrGroupReqDTO);
        }
        return itemAttrGroupReqDTOS;
    }


    /**
     * 获取指定Item中的属性
     *
     * @param attrWebRespDTOList
     * @return
     * <AUTHOR> chen
     * @version 1.0
     * @since 2020-10-27
     */
    private List<ItemAttrReqDTO> getAttrDTOList(List<AttrWebRespDTO> attrWebRespDTOList) {
        ArrayList<ItemAttrReqDTO> itemAttrReqDTOS = new ArrayList<>();
        for (AttrWebRespDTO attrWebRespDTO : attrWebRespDTOList) {
            ItemAttrReqDTO itemAttrReqDTO = new ItemAttrReqDTO();
            itemAttrReqDTO.setAttrItemAttrGroupGuid(attrWebRespDTO.getAttrItemAttrGroupGuid());
            itemAttrReqDTO.setAttrGuid(attrWebRespDTO.getAttrGuid());
            // itemAttrReqDTO.setName("特辣"); -- "name":"特辣",
            // itemAttrReqDTO.setPrice(0.0); -- "price":0,
            itemAttrReqDTO.setIsDefault(attrWebRespDTO.getIsDefault());
            itemAttrReqDTO.setItemAttrGroupGuid("默认的ItemAttrGroupGuid");
            // itemAttrReqDTO.setAttrFrom(ITEM_FROM); -- "attrFrom":1
            // itemAttrReqDTO.setBrandGuid("6712166044938534912"); -- "brandGuid":"6712166044938534912",
            itemAttrReqDTOS.add(itemAttrReqDTO);
        }
        return itemAttrReqDTOS;
    }

    /**
     * 获取套餐列表数据
     *
     * @return List
     */
    public List<SubgroupReqDTO> getSubGroupListDTO(List<SubgroupWebRespDTO> subgroupWebRespDTOList) {
        ArrayList<SubgroupReqDTO> subgroupReqDTOS = new ArrayList<>();
        if (subgroupWebRespDTOList == null || subgroupWebRespDTOList.isEmpty()) {
            return subgroupReqDTOS;
        }
        for (SubgroupWebRespDTO subgroupWebRespDTO : subgroupWebRespDTOList) {
            SubgroupReqDTO subgroupReqDTO = new SubgroupReqDTO();
            subgroupReqDTO.setIsFixSubgroup(subgroupWebRespDTO.getIsFixSubgroup());
            subgroupReqDTO.setName(subgroupWebRespDTO.getName());
            subgroupReqDTO.setPickNum(subgroupWebRespDTO.getPickNum());
            subgroupReqDTO.setSort(subgroupWebRespDTO.getSort());
            subgroupReqDTO.setSubItemSkuList(getSubItemSkuList(subgroupWebRespDTO.getSubItemSkuList()));
            subgroupReqDTO.setSubgroupGuid(subgroupWebRespDTO.getSubgroupGuid());
            subgroupReqDTOS.add(subgroupReqDTO);
        }
        return subgroupReqDTOS;
    }

    /**
     * 获取参套所含Item的规格
     *
     * @return List
     */
    public List<SubItemSkuReqDTO> getSubItemSkuList(List<SubItemSkuWebRespDTO> subItemSkuWebRespDTOList) {
        ArrayList<SubItemSkuReqDTO> subItemSkuReqDTOS = new ArrayList<>();
        for (SubItemSkuWebRespDTO subItemSkuWebRespDTO : subItemSkuWebRespDTOList) {
            SubItemSkuReqDTO subItemSkuReqDTO = new SubItemSkuReqDTO();
            subItemSkuReqDTO.setSkuGuid(subItemSkuWebRespDTO.getSkuGuid());
            subItemSkuReqDTO.setItemGuid(subItemSkuWebRespDTO.getItemGuid());
            // subItemSkuReqDTO.setName(""); -- "name":"水煮鱼",
            // subItemSkuReqDTO.setItemType(2); -- "itemType":2,
            // subItemSkuReqDTO.setUnit("份");
            // subItemSkuReqDTO.setSalePrice(15); -- salePrice":15,
            // subItemSkuReqDTO.setSaleCostPrice(23); -- "saleCostPrice":23,
            // subItemSkuReqDTO.setGrossMargin(-0.53); --"grossMargin":-0.53,
            subItemSkuReqDTO.setItemNum(subItemSkuWebRespDTO.getItemNum());
            subItemSkuReqDTO.setAddPrice(subItemSkuWebRespDTO.getAddPrice());
            subItemSkuReqDTO.setIsDefault(subItemSkuWebRespDTO.getIsDefault());
            subItemSkuReqDTO.setIsRepeat(subItemSkuWebRespDTO.getIsRepeat());
            //subItemSkuReqDTO.setIsRequired(0); --"isRequired":0
            subItemSkuReqDTOS.add(subItemSkuReqDTO);
        }
        return subItemSkuReqDTOS;
    }

    @Override
    public void dealPkgInfo(String itemGuid, String storeGuid) {
        // 1. 通过itemGuid获取到subgroup相关信息
        List<SubgroupDO> subgroupDOS = iSubgroupService.list(new QueryWrapper<SubgroupDO>()
                .eq("item_guid", itemGuid)
                .eq("store_guid", storeGuid)
                .isNull("brand_guid")
        );
        if (!subgroupDOS.isEmpty()) {
            for (SubgroupDO subgroupDO : subgroupDOS) {
                // 2. 通过subgroup中的信息查询sku_subgroup中的相关信息
                List<RSkuSubgroupDO> skuSubgroupDOS = irSkuSubgroupService.list(new QueryWrapper<RSkuSubgroupDO>().eq("subgroup_guid", subgroupDO.getGuid()));
                if (!skuSubgroupDOS.isEmpty()) {
                    for (RSkuSubgroupDO skuSubGroupDO : skuSubgroupDOS) {
                        // 2.1 更新sku_subgroup中的相关信息
                        //skuSubGroupDO.setIsDelete(1);
                        //irSkuSubgroupService.updateById(skuSubGroupDO);
                        irSkuSubgroupService.updateIsDelete(skuSubGroupDO.getGuid());
                        // 3. 通过sku_subgroup中的sku_guid到sku表中插叙相关信息
                        iSkuService.list(new QueryWrapper<SkuDO>().eq("guid", skuSubGroupDO.getItemGuid()));
                    }
                }
            }
        }
    }

    @Override
    public void updateSubGroupItemToNewItem(String itemGuid, String storeGuid, String newItemGuid) {
        // 1. 通过itemGuid获取到subgroup相关信息
        List<SubgroupDO> subgroupDOS = iSubgroupService.list(new QueryWrapper<SubgroupDO>()
                .eq("item_guid", itemGuid)
                .eq("store_guid", storeGuid)
                .isNull("brand_guid")
        );
        if (!subgroupDOS.isEmpty()) {
            for (SubgroupDO subgroupDO : subgroupDOS) {
                iSubgroupService.updateById(subgroupDO);
                // 1.1 更新subgroup中的数据为newItemGuid
                subgroupDO.setItemGuid(newItemGuid);
                iSubgroupService.updateById(subgroupDO);
            }
        }
    }

    /**
     * 将旧的itemGuid和新的itemGuid映射起来
     *
     * @param oldNewItemGuid
     * @param newItemGuid
     * @param storeGuid
     */
    private void setOldItemGuidAndNewItemGuidMapping(String oldNewItemGuid, String newItemGuid, String storeGuid) {
        Dict data = Dict.create().set("old_item_guid", oldNewItemGuid).set("new_item_guid", newItemGuid).set("store_guid", storeGuid);
        String s = JSONUtil.toJsonStr(data);
        String cacheKey = iCommonService.md5(oldNewItemGuid + ":" + newItemGuid);
        iRedissonCacheService.setCache(cacheKey, s, 604800, TimeUnit.SECONDS);
    }

    /**
     * 检测并更新数据subgroup的数据是否已经被更新为新的guid
     *
     * @param oldStoreItemGuid
     * @param newStoreItemGuid
     * @param storeGuid
     */
    private void updateSubGroup(String oldStoreItemGuid, String newStoreItemGuid, String storeGuid) {
        List<SubgroupDO> list = iSubgroupService.list(new QueryWrapper<SubgroupDO>()
                .eq("item_guid", newStoreItemGuid)
                .eq("store_guid", storeGuid)
                .eq("is_delete", 0));
        if (list.isEmpty()) {
            list = iSubgroupService.list(new QueryWrapper<SubgroupDO>()
                    .eq("item_guid", oldStoreItemGuid)
                    .eq("store_guid", storeGuid)
                    .eq("is_delete", 0));
            if (!list.isEmpty()) {
                list.stream().forEach(subgroupDO -> {
                    subgroupDO.setItemGuid(newStoreItemGuid);
                    iSubgroupService.updateById(subgroupDO);
                });
            }
        }
    }

    /**
     * 将指定品牌的store_guid设置为null
     *
     * @param itemGuid
     * @param storeGuid
     * @param brandGuid
     * @return
     */
    private Integer updateOriginItemStoreGuid(String itemGuid, String storeGuid, String brandGuid) {
        return iItemService.updateOriginItemStoreGuid(itemGuid, storeGuid, brandGuid);
    }

    /**
     * 设置当前Item的价格为原始价格
     *
     * @param itemGuid
     * @param storeGuid
     * @param skuGuid
     * @param originalSalePrice
     * @param originalMemberPrice
     * @return
     */
    private Integer updateOriginPriceStoreGuid(String itemGuid,
                                               String storeGuid,
                                               String skuGuid,
                                               BigDecimal originalSalePrice,
                                               BigDecimal originalMemberPrice) {
        return iSkuService.updateOriginPriceStoreGuid(itemGuid, storeGuid, skuGuid, originalSalePrice, originalMemberPrice);
    }
}
