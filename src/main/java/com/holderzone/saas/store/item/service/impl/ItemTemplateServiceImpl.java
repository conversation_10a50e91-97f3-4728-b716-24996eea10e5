package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.item.req.ItemTemplateReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemTemplateSearchReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenuSubItemDetailRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplatesRespDTO;
import com.holderzone.saas.store.item.constant.Constant;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.entity.domain.ItemTemplateDO;
import com.holderzone.saas.store.item.entity.query.ItemTemplateExecuteTimeQuery;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.helper.PageAdapter;
import com.holderzone.saas.store.item.mapper.ItemTemplateMapper;
import com.holderzone.saas.store.item.service.IItemTMenuSubitemService;
import com.holderzone.saas.store.item.service.IItemTMenuValidityService;
import com.holderzone.saas.store.item.service.IItemTemplateService;
import com.holderzone.saas.store.item.util.MapStructUtils;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.item.constant.Constant.TIME_CONFLICT;

/**
 * <p>
 * 商品模板 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
@Service
@Slf4j
public class ItemTemplateServiceImpl extends ServiceImpl<ItemTemplateMapper, ItemTemplateDO> implements IItemTemplateService {

    private final ItemTemplateMapper itemTemplateMapper;
    private final IItemTMenuValidityService itemTMenuValidityService;
    private final ItemHelper itemHelper;
    private final IItemTMenuSubitemService itemTMenuSubitemService;
    private final RedisTemplate redisTemplate;

    @Autowired
    public ItemTemplateServiceImpl(ItemTemplateMapper itemTemplateMapper,
                                   IItemTMenuValidityService itemTMenuValidityService,
                                   ItemHelper itemHelper,
                                   IItemTMenuSubitemService itemTMenuSubitemService,
                                   RedisTemplate redisTemplate) {
        this.itemTemplateMapper = itemTemplateMapper;
        this.itemTMenuValidityService = itemTMenuValidityService;
        this.itemHelper = itemHelper;
        this.itemTMenuSubitemService = itemTMenuSubitemService;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public Boolean saveOrUpdate(ItemTemplateReqDTO request) {
        Boolean flag = Boolean.FALSE;
        //删除模板
        if (Optional.ofNullable(request).map(ItemTemplateReqDTO::getGuid).isPresent()
                && request.getIsDelete().equals(1)) {
            ItemTemplateDO templateDO = getById(request.getGuid());
            itemHelper.pushMsg(templateDO.getStoreGuid());
            return removeById(request.getGuid());
        }
        ItemTemplateDO itemTemplateDO = MapStructUtils.INSTANCE.itemTemplateReqDTO2itemTemplateDO(request);
        if (!Optional.ofNullable(itemTemplateDO).map(ItemTemplateDO::getGuid).isPresent()) {
            try {
                itemTemplateDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, GuidKeyConstant.HSI_ITEM_TEMPLATE)));
            } catch (IOException e) {
                throw new BusinessException(Constant.CREATE_GUID_FAIL);
            }
        }
        Boolean status = verifyTemplateName(itemTemplateDO.getTemplateName(), itemTemplateDO.getGuid(), itemTemplateDO.getStoreGuid());
        if (!status) {
            throw new BusinessException("已存在相同名称的" + itemTemplateDO.getTemplateName() + "模板，保存失败");
        }
        log.info("insert db object={}", JacksonUtils.writeValueAsString(itemTemplateDO));
        //更新和保存验证时间段
        itemTemplateDO.setEffectiveStartTime(DateTimeUtils.mills2LocalDateTime(DateTimeUtils.parseStrDate(request.getEffectiveStartTime(), DateTimeUtils.PATTERN_SECONDS)));
        itemTemplateDO.setEffectiveEndTime(DateTimeUtils.mills2LocalDateTime(DateTimeUtils.parseStrDate(request.getEffectiveEndTime(), DateTimeUtils.PATTERN_SECONDS)));
        List<ItemTemplateDO> itemTemplateDOS = list(
                new LambdaQueryWrapper<ItemTemplateDO>()
                        .eq(ItemTemplateDO::getStoreGuid, request.getStoreGuid())
                        .ne(ItemTemplateDO::getGuid, itemTemplateDO.getGuid()));
        Boolean result = itemHelper.validateTemplateTime(request, itemTemplateDOS);
        if (!result) {
            throw new ParameterException(TIME_CONFLICT);
        }
        //保存和更新
        flag = saveOrUpdate(itemTemplateDO);
        if (flag) {
            ItemTemplateDO templateDO = getById(request.getGuid());
            if (!Optional.ofNullable(itemTemplateDO).map(ItemTemplateDO::getStoreGuid).isPresent()) {
                itemTemplateDO.setStoreGuid(templateDO.getStoreGuid());
            }
            itemHelper.pushMsg(itemTemplateDO.getStoreGuid());
        }
        return flag;
    }

    private Boolean verifyTemplateName(String templateName, String guid, String storeGuid) {
        List<ItemTemplateDO> list = list(
                new LambdaQueryWrapper<ItemTemplateDO>()
                        .ne(ItemTemplateDO::getGuid, guid)
                        .eq(ItemTemplateDO::getStoreGuid, storeGuid));
        if (!Optional.ofNullable(list).isPresent()) {
            return Boolean.TRUE;
        }
        List<String> strNames = list
                .stream()
                .map(ItemTemplateDO::getTemplateName)
                .collect(Collectors.toList());
        if (strNames.contains(templateName)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    @Override
    public ItemTemplatesRespDTO getStoreItemTemplates(ItemTemplateSearchReqDTO request) {
        Assert.hasText(request.getStoreGuid(), "门店guid不能为空");
        ItemTemplateDO itemTemplateDO = itemTemplateMapper.getStoreCurrentUsedTemplateName(request.getStoreGuid());
        String teeplateName = Optional.ofNullable(itemTemplateDO).isPresent() ? itemTemplateDO.getTemplateName() : "";
        IPage<ItemTemplateRespDTO> page = itemTemplateMapper.getStoreItemTemplates(new PageAdapter<>(request), request);
        return ItemTemplatesRespDTO.builder().currentTemplateName(teeplateName).templateRespDTOS(new PageAdapter<>(page, page.getRecords())).build();
    }

    @Override
    public String getStoreGuidByCode(Integer type, String guid) {
        List<String> guids = itemTemplateMapper.getStoreGuidByCode(type, guid);
        if (guids.isEmpty() || StringUtils.isEmpty(guids)) {
            return null;
        } else {
            Optional<String> first = guids.stream().findFirst();
            if (first.isPresent()) {
                return first.get();
            }
            return null;
        }
    }

    @Override
    public List<ItemTemplateMenuSubItemDetailRespDTO> getNowMeunSubItemForSyn(String storeGuid) {
        List<ItemTemplateExecuteTimeQuery> nowMenuExecuteTimes = itemTMenuValidityService.getNowMenuExecuteTimes(storeGuid);
        if (Optional.ofNullable(nowMenuExecuteTimes).isPresent() && !nowMenuExecuteTimes.isEmpty()) {
            String menuGuid = itemHelper.getNowExecuteMenu(nowMenuExecuteTimes);
            log.info("商品模板符合条件的模板信息menuGuid:{}",menuGuid);
            if (Optional.ofNullable(menuGuid).isPresent()) {
                return itemTMenuSubitemService.getNowMeunSubItemForSyn(menuGuid);
            }
        }
        return new ArrayList<>();
    }

}
