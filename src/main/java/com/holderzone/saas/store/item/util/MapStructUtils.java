package com.holderzone.saas.store.item.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.common.PlanItemDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceAllTypeRespDTO;
import com.holderzone.saas.store.enums.item.TagEnum;
import com.holderzone.saas.store.item.entity.bo.*;
import com.holderzone.saas.store.item.entity.domain.*;
import com.holderzone.saas.store.item.entity.query.ItemTemplateMenuSubItemDetailQuery;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MapStructUtils
 * @date 2019/01/19 下午3:28
 * @description //TODO
 * @program holder-saas-store-item
 */
@Mapper
public interface MapStructUtils {

    MapStructUtils INSTANCE = Mappers.getMapper(MapStructUtils.class);


    @Mapping(target = "attrGroupFrom", source = "from")
    AttrGroupDO attrGroupReqDTO2attrGroupDO(AttrGroupReqDTO attrGroupReqDTO);

    @Mapping(target = "attrFrom", source = "from")
    AttrDO attrReqDTO2attrDO(AttrReqDTO attrReqDTO);

    @Mapping(target = "guid", source = "attrGroupGuid")
    AttrGroupDO attrGroupUpdateReqDTO2attrGroupDO(AttrGroupUpdateReqDTO attrGroupUpdateReqDTO);

    @Mapping(target = "guid", source = "typeGuid")
    TypeDO typeReqDTO2typeDO(TypeReqDTO typeReqDTO);

    @Mapping(target = "typeGuid", source = "guid")
    TypeWebRespDTO typeDO2typeWebRespDTO(TypeDO typeDO);

    List<TypeWebRespDTO> typeDOList2typeWebRespDTOList(List<TypeDO> typeDOList);
    @Mapping(target = "attrGroupGuid", source = "guid")
    AttrGroupAttrRespDTO attrGroupDO2attrGroupAttrRespDTO(AttrGroupDO attrGroupDO);

    List<AttrGroupAttrRespDTO> attrGroupDOList2attrGroupAttrRespDTOList(List<AttrGroupDO> attrGroupDOList);

    @Mapping(target = "attrGuid", source = "guid")
    AttrRespDTO attrDO2attrRespDTO(AttrDO attrDO);

    List<AttrRespDTO> attrDOList2attrRespDTOList(List<AttrDO> attrDOList);

    @Mapping(target = "guid", source = "skuGuid")
    SkuDO skuSaveReqDTO2skuDO(SkuSaveReqDTO skuSaveReqDTO);

    List<SkuDO> skuSaveReqDTOList2skuDOList(List<SkuSaveReqDTO> skuSaveReqDTOList);

    @Mapping(target = "guid", source = "itemGuid")
    ItemDO itemReqDTO2itemDO(ItemReqDTO itemReqDTO);

    @Mappings(
            {
                    @Mapping(target = "itemGuid", source = "guid"),
                    @Mapping(target = "typeGuid", source = "typeGuid"),
                    @Mapping(target = "lowestPrice", ignore = true),
                    @Mapping(target = "isRack", ignore = true),
                    @Mapping(target = "isWholeDiscount", ignore = true),
                    @Mapping(target = "isMemberDiscount", ignore = true),
                    @Mapping(target = "isSoldOut", ignore = true)
            }
    )
    ItemWebRespDTO itemDO2itemWebRespDTO(ItemDO itemDO);

    List<ItemWebRespDTO> itemDOList2itemWebRespDTOList(List<ItemDO> itemDOList);

    TypeWebRespDTO typeAttrDO2typeWebRespDTO(RTypeAttrDO typeAttrDO);

    List<TypeWebRespDTO> typeAttrDOList2typeWebRespDTOList(List<RTypeAttrDO> typeAttrDOList);

    @Mappings({
            @Mapping(target = "itemGuid", source = "guid"),
            @Mapping(target = "tagList", ignore = true),
            @Mapping(target = "isFixPkg", ignore = true),
            @Mapping(target = "skuList", ignore = true),
            @Mapping(target = "attrGroupList", ignore = true),
            @Mapping(target = "subgroupList", ignore = true)
    })
    ItemSynRespDTO itemDO2itemSynRespDTO(ItemDO itemDO);

    List<ItemSynRespDTO> itemDOList2itemSynRespDTOList(List<ItemDO> itemDOList);

    @Mappings({
            @Mapping(target = "skuGuid", source = "guid"),
            @Mapping(target = "isJoinWeChat", source = "isJoinWeChat"),
            @Mapping(target = "memberPrice", source = "memberPrice")
    })
    SkuSynRespDTO skuDO2skuSynRespDTO(SkuDO skuDO);

    List<SkuSynRespDTO> skuDOList2skuSynRespDTOList(List<SkuDO> skuDOList);

    @Mappings({
            @Mapping(target = "name", ignore = true),
            @Mapping(target = "iconUrl", ignore = true),
            @Mapping(target = "attrList", ignore = true)
    })
    AttrGroupSynRespDTO rItemAttrGroupDO2attrGroupSynRespDTO(RItemAttrGroupDO rItemAttrGroupDO);

    @Mappings({
            @Mapping(target = "name", ignore = true),
            @Mapping(target = "price", ignore = true)
    })
    AttrSynRespDTO attrItemAttrGroupDO2attrSynRespDTO(RAttrItemAttrGroupDO attrItemAttrGroupDO);

    @Mapping(target = "typeGuid", source = "guid")
    TypeSynRespDTO typeDO2typeSynRespDTO(TypeDO typeDO);

    List<TypeSynRespDTO> typeDOList2typeSynRespDTOList(List<TypeDO> typeDOList);

    default TagRespDTO tagEnum2tagRespDTO(TagEnum source) {
        TagRespDTO tagRespDTO = new TagRespDTO();
        tagRespDTO.setId(source.getId());
        tagRespDTO.setName(source.getName());
        return tagRespDTO;
    }

    List<TagRespDTO> tagEnumList2tagRespDTOList(List<TagEnum> tagList);

    @Mappings({
            @Mapping(target = "currentPage", source = "current"),
            @Mapping(target = "pageSize", source = "size"),
            @Mapping(target = "totalCount", source = "total"),
            @Mapping(target = "data", source = "records")
    })
    Page<ItemWebRespDTO> itemDOIPage2itemWebRespDTOPage(IPage<ItemDO> itemDOIPage);

    @Mapping(target = "itemGuid", source = "guid")
    ItemInfoRespDTO itemDO2itemInfoRespDTO(ItemDO itemDO);

    List<ItemInfoRespDTO> itemDOList2ItemInfoRespDTOList(List<ItemDO> itemDOList);

    @Mappings({
            @Mapping(target = "typeGuid", source = "guid"),
            @Mapping(target = "skuNameRespDTOList", ignore = true)
    })
    TypeSkuRespDTO typeDO2typeSkuRespDTO(TypeDO typeDO);

    List<TypeSkuRespDTO> typeDOList2typeSkuRespDTOList(List<TypeDO> typeDOList);

    default List<TypeSkuRespDTO.SkuNameRespDTO> itemDOskuDOList2skuNameRespDTOList(ItemDO itemDO, List<SkuDO> skuDOS) {
        if (itemDO == null || CollectionUtils.isEmpty(skuDOS)) {
            return new ArrayList<>();
        }
        List<TypeSkuRespDTO.SkuNameRespDTO> skuNameRespDTOList = new ArrayList<>();
        skuDOS.forEach(skuDO -> {
            TypeSkuRespDTO.SkuNameRespDTO skuNameRespDTO = new TypeSkuRespDTO.SkuNameRespDTO();
            skuNameRespDTO.setSkuGuid(skuDO.getGuid());
            skuNameRespDTO.setItemGuid(itemDO.getGuid());
            StringBuilder sb = new StringBuilder(itemDO.getName());
            if (!StringUtils.isEmpty(skuDO.getName())) {
                sb.append("(" + skuDO.getName() + ")");
            }
            skuNameRespDTO.setName(sb.toString());
            skuNameRespDTO.setItemType(itemDO.getItemType() == 4 ? 2 : itemDO.getItemType());
            skuNameRespDTO.setUnit(skuDO.getUnit());
            skuNameRespDTO.setSalePrice(skuDO.getSalePrice());
            skuNameRespDTO.setSaleCostPrice(skuDO.getCostPrice());
            skuNameRespDTO.setAccountingPrice(skuDO.getAccountingPrice());
            skuNameRespDTO.setPictureUrl(itemDO.getPictureUrl());
            if (!StringUtils.isEmpty(skuDO.getParentGuid())) {
                skuNameRespDTO.setParentGuid(skuDO.getParentGuid());
            }
            if (Optional.ofNullable(skuDO.getCostPrice()).isPresent() && skuDO.getSalePrice().compareTo(BigDecimal.ZERO) != 0) {
                skuNameRespDTO.setGrossMargin((skuDO.getSalePrice().subtract(skuDO.getCostPrice())).divide(skuDO.getSalePrice(), 2, RoundingMode.HALF_UP));
            }
            skuNameRespDTOList.add(skuNameRespDTO);
        });
        return skuNameRespDTOList;
    }

    @Mappings({
            @Mapping(target = "typeGuid", source = "guid"),
            @Mapping(target = "itemWebRespDTOList", ignore = true)
    })
    TypeItemRespDTO typeDO2typeItemRespDTO(TypeDO typeDO);

    List<TypeItemRespDTO> typeDOList2typeItemRespDTOList(List<TypeDO> typeDOList);

    @Mapping(target = "guid", source = "itemGuid")
    ItemDO itemPkgSaveReqDTO2itemDO(ItemPkgSaveReqDTO itemPkgSaveReqDTO);

    @Mapping(target = "guid", source = "subgroupGuid")
    SubgroupDO subgroupReqDTO2subgroupDO(SubgroupReqDTO subgroupReqDTO);

    @Mapping(target = "guid", source = "skuSubgroupGuid")
    RSkuSubgroupDO subItemSkuReqDTO2skuSubgroupDO(SubItemSkuReqDTO subItemSkuReqDTO);

    @Mappings({
            @Mapping(target = "subgroupGuid", source = "guid"),
            @Mapping(target = "subItemSkuList", ignore = true)
    })
    SubgroupSynRespDTO subgroupDO2subgroupSynRespDTO(SubgroupDO subgroupDO);

    List<SubgroupSynRespDTO> subgroupDOList2subgroupSynRespDTOList(List<SubgroupDO> subgroupDOList);

    @Mapping(target = "defaultNum", source = "isDefault")
    SubItemSkuSynRespDTO skuSubgroupDO2subItemSkuSynRespDTO(RSkuSubgroupDO skuSubgroupDO);

    List<SubItemSkuSynRespDTO> skuSubgroupDOList2subItemSkuSynRespDTOList(List<RSkuSubgroupDO> skuSubgroupDOS);

    @Mapping(target = "skuSubgroupGuid", source = "guid")
    SubItemSkuWebRespDTO skuSubgroupDO2subItemSkuWebRespDTO(RSkuSubgroupDO skuSubgroupDO);

    @Mappings({
            @Mapping(target = "name", ignore = true),
            @Mapping(target = "iconUrl", ignore = true),
            @Mapping(target = "attrList", ignore = true),
            @Mapping(target = "itemAttrGroupGuid", source = "guid")
    })
    AttrGroupWebRespDTO rItemAttrGroupDO2attrGroupWebRespDTO(RItemAttrGroupDO itemAttrGroupDO);

    @Mappings({
            @Mapping(target = "name", ignore = true),
            @Mapping(target = "price", ignore = true),
            @Mapping(target = "attrItemAttrGroupGuid", source = "guid")
    })
    AttrWebRespDTO attrItemAttrGroupDO2attrWebRespDTO(RAttrItemAttrGroupDO rAttrItemAttrGroupDO);

    @Mappings({
            @Mapping(target = "subgroupGuid", source = "guid"),
            @Mapping(target = "subItemSkuList", ignore = true)
    })
    SubgroupWebRespDTO subgroupDO2subgroupWebRespDTO(SubgroupDO subgroupDO);

    AttrDO mapAttrDO(AttrSaveReqDTO attrSaveReqDTO);


    @Mappings({
            @Mapping(target = "id", source = "id"),
            @Mapping(target = "skuGuid", source = "guid"),
            @Mapping(target = "memberPrice", source = "memberPrice"),
            @Mapping(target = "costPrice", source = "costPrice"),
            @Mapping(target = "code", source = "code"),
            @Mapping(target = "isWholeDiscount", source = "isWholeDiscount"),
            @Mapping(target = "uck", constant = "1")
    })
    SkuInfoRespDTO skuDO2skuInfoRespDTO(SkuDO skuDO);

    List<SkuInfoRespDTO> skuDOList2skuInfoRespDTOList(List<SkuDO> skuDOList);

    @Mapping(target = "guid", source = "skuGuid")
    SkuDO skuInfoBO2skuDO(SkuInfoBO skuInfoBO);

    @Mapping(target = "guid", source = "itemAttrGroupGuid")
    RItemAttrGroupDO attrGroupBO2itemAttrGroupDO(ItemAttrGroupBO itemAttrGroupBO);

    @Mapping(target = "guid", source = "attrItemAttrGroupGuid")
    RAttrItemAttrGroupDO attrBO2attrItemAttrGroupDO(ItemAttrBO itemAttrBO);

    List<RItemAttrGroupDO> itemAttrGroupBOList2itemAttrGroupDOList(List<ItemAttrGroupBO> updateItemAttrGroupList);

    ItemInfoBO itemReqDTO2itemInfoBO(ItemReqDTO itemUpdateReqDTOS);

    @Mapping(target = "guid", source = "attrItemAttrGroupGuid")
    RAttrItemAttrGroupDO itemAttrBO2attrItemAttrGroupDO(ItemAttrBO attr);

    SubItemSkuBO subItemSkuReqDTO2subItemSkuBO(SubItemSkuReqDTO subItemSkuReqDTO);

    SubgroupBO subgroupReqDTO2subgroupBO(SubgroupReqDTO subgroupReqDTO);

    ItemInfoBO itemPkgSaveReqDTO2itemInfoBO(ItemPkgSaveReqDTO itemPkgSaveReqDTO);

    @Mapping(target = "guid", source = "subgroupGuid")
    SubgroupDO subgroupBO2subgroupDO(SubgroupBO subgroupBO);

    @Mapping(target = "guid", source = "skuSubgroupGuid")
    RSkuSubgroupDO subItemSkuBO2skuSubgroupDO(SubItemSkuBO subItemSkuBO);

    @Mappings({
            @Mapping(target = "subgroupGuid", source = "guid"),
            @Mapping(target = "subItemSkuList", ignore = true)
    })
    SubgroupBO subgroupDO2subgroupBO(SubgroupDO subgroupDO);

    List<SubgroupBO> subgroupDOList2subgroupBOList(List<SubgroupDO> subgroupDOList);

    @Mapping(target = "skuSubgroupGuid", source = "guid")
    SubItemSkuBO skuSubgroupDO2subItemSkuBO(RSkuSubgroupDO skuSubgroupDO);

    List<SubItemSkuBO> skuSubgroupDOList2subItemSkuBOList(List<RSkuSubgroupDO> skuSubgroupDOS);

    PushBO skuDO2pushBO(SkuDO pushedSkuDO);

    List<PushBO> skuDOList2pushBOList(List<SkuDO> pushedSkuDOList);

    PushBO typeDO2pushBO(TypeDO pushedTypeDO);

    List<PushBO> typeDOList2pushBOList(List<TypeDO> pushedTypeDOList);

    PushBO attrGroupDO2pushBO(AttrGroupDO pushedAttrGroupDO);

    List<PushBO> attrGroupDOList2pushBOList(List<AttrGroupDO> pushedAttrGroupDOList);

    PushBO attrDO2pushBO(AttrDO pushedAttrDO);

    List<PushBO> attrDOList2pushBOList(List<AttrDO> pushedAttrDOList);

    PushBO subgroupDO2pushBO(SubgroupDO pushedSubgroupDO);

    List<PushBO> subgroupDOList2pushBOList(List<SubgroupDO> pushedSubgroupDOList);

    PushBO itemDO2pushBO(ItemDO pushedItemDO);

    List<PushBO> itemDOList2pushBOList(List<ItemDO> pushedItemDOList);

    SubItemSkuWebRespDTO subItemSkuBO2subItemSkuWebRespDTO(SubItemSkuBO source);

    List<SubItemSkuWebRespDTO> subItemSkuBOList2subItemSkuWebRespDTOList(List<SubItemSkuBO> source);

    SubgroupWebRespDTO subgroupBO2subgroupWebRespDTO(SubgroupBO subgroupBO);

    List<SubgroupWebRespDTO> subgroupBOList2subgroupWebRespDTOList(List<SubgroupBO> subgroupBOList);

    SubItemSkuWebRespDTO subItemSku2subItemSkuWebRespDTO(SubItemSkuWebRespDTO subItemSku);

    List<SubItemSkuWebRespDTO> subItemSkuList2subItemSkuWebRespDTOList(List<SubItemSkuWebRespDTO> subItemSkuList);

    @Mapping(target = "dataList", ignore = true)
    ItemStringListDTO itemSingleDTO2itemStringListDTO(ItemSingleDTO itemSingleDTO);

    SkuTakeawayInfoRespDTO skuItemTypeBO2skuTakeawayInfoRespDTO(SkuItemTypeBO skuItemTypeBO);

    List<SkuTakeawayInfoRespDTO> skuItemTypeBOList2skuTakeawayInfoRespDTOList(List<SkuItemTypeBO> skuItemTypeBOList);

    default MappingRespDTO skuItemTypeBO2mappingRespDTO(SkuItemTypeBO skuItemTypeBO) {
        if (skuItemTypeBO == null) {
            return null;
        }

        MappingRespDTO mappingRespDTO = new MappingRespDTO();
        mappingRespDTO.seteDishCode(skuItemTypeBO.getItemGuid());
        mappingRespDTO.seteDishName(skuItemTypeBO.getItemName());
        mappingRespDTO.seteDishSkuCode(skuItemTypeBO.getSkuGuid());
        mappingRespDTO.seteDishSkuName(skuItemTypeBO.getSkuName());
        mappingRespDTO.setPinyin(skuItemTypeBO.getPinyin());
        mappingRespDTO.seteDishSkuUnit(skuItemTypeBO.getUnit());
        mappingRespDTO.setCategoryId(skuItemTypeBO.getTypeGuid());
        mappingRespDTO.setCategoryName(skuItemTypeBO.getTypeName());
        mappingRespDTO.setSkuCode(skuItemTypeBO.getCode());
        mappingRespDTO.setParentSkuGuid(skuItemTypeBO.getParentSkuGuid());
        mappingRespDTO.setParentItemGuid(skuItemTypeBO.getParentItemGuid());
        mappingRespDTO.setIsRack(skuItemTypeBO.getIsRack());
        mappingRespDTO.setIsChoice(skuItemTypeBO.getIsChoice());
        mappingRespDTO.setDishNameWithSpec(StringUtils.isEmpty(skuItemTypeBO.getSkuName()) ? skuItemTypeBO.getItemName() : skuItemTypeBO.getItemName() + "（" + skuItemTypeBO.getSkuName() + "）");
        mappingRespDTO.setErpItemPrice(skuItemTypeBO.getSalePrice());
        mappingRespDTO.setPlanItemName(skuItemTypeBO.getPlanItemName());
        mappingRespDTO.setTakeawayAccountingPrice(skuItemTypeBO.getTakeawayAccountingPrice());
        mappingRespDTO.setAccountingPrice(skuItemTypeBO.getAccountingPrice());
        return mappingRespDTO;
    }

    List<MappingRespDTO> skuItemTypeBOList2mappingRespDTOList(List<SkuItemTypeBO> skuItemTypeBOList);

    TypeItemListDTO typeDO2typeItemListDTO(TypeDO typeDO);

    List<TypeItemListDTO> typeDOList2typeItemListDTOList(List<TypeDO> typeDOList);

    TypeItemListDTO itemDO2typeItemListDTO(ItemDO thisTypeItem);

    List<TypeItemListDTO> itemDOList2typeItemListDTOList(List<ItemDO> thisTypeItemList);

    @Mappings({
            @Mapping(target = "dishGuid", source = "guid"),
            @Mapping(target = "dishFrom", source = "itemFrom"),
            @Mapping(target = "dishType", source = "itemType"),
            @Mapping(target = "isMultiSku", ignore = true),
            @Mapping(target = "isRack", ignore = true),
            @Mapping(target = "hasProperty", ignore = true),
            @Mapping(target = "code", ignore = true),
            @Mapping(target = "minOrderNum", ignore = true),
            @Mapping(target = "unit", ignore = true),
            @Mapping(target = "unitCode", ignore = true),
            @Mapping(target = "desc", source = "description"),
            @Mapping(target = "isWholeDiscount", ignore = true),
            @Mapping(target = "isMemberDiscount", ignore = true),
            @Mapping(target = "parentGuid", ignore = true),
            @Mapping(target = "isRecommend", ignore = true),
            @Mapping(target = "isDelete", ignore = true),
            @Mapping(target = "skuDTOS", ignore = true),
            @Mapping(target = "dishPkgSubgroupList", ignore = true)
    })
    DishRespDTO itemDO2dishRespDTO(ItemDO itemDO);

    @Mappings({
            @Mapping(target = "skuGuid", source = "guid"),
            @Mapping(target = "stock", ignore = true),
            @Mapping(target = "spec", ignore = true),
            @Mapping(target = "referencePrice", ignore = true),
            @Mapping(target = "isDelete", ignore = true),
            @Mapping(target = "updateSalePrice", ignore = true)
    })
    SkuRespDTO skuDO2skuRespDTO(SkuDO thisItemSkuDO);

    List<SkuRespDTO> skuDOList2skuRespDTOList(List<SkuDO> skuDOList);

    @Mappings({
            @Mapping(target = "subgroupGuid", source = "guid"),
            @Mapping(target = "dishList", ignore = true)
    })
    DishPkgSubgroupRespDTO subgroupDO2dishPkgSubgroupRespDTO(SubgroupDO thisPkgSubgroupDO);

    @Mappings({
            @Mapping(target = "skuName", ignore = true),
            @Mapping(target = "salePrice", ignore = true),
            @Mapping(target = "dishGuid", source = "itemGuid"),
            @Mapping(target = "dishName", ignore = true),
            @Mapping(target = "code", ignore = true),
            @Mapping(target = "typeGuid", ignore = true),
            @Mapping(target = "dishType", ignore = true),
            @Mapping(target = "unit", ignore = true),
            @Mapping(target = "dishNum", source = "itemNum"),
            @Mapping(target = "hasProperty", ignore = true)
    })
    DishPkgSubgroupSkuRespDTO skuSubgroupDO2dishPkgSubgroupSkuRespDTO(RSkuSubgroupDO skuSubgroupDO);

    @Mappings({
            @Mapping(target = "skuGuid", source = "guid"),
            @Mapping(target = "skuName", source = "name"),
            @Mapping(target = "itemName", ignore = true),
            @Mapping(target = "typeName", ignore = true),
            @Mapping(target = "typeGuid", ignore = true),
            @Mapping(target = "itemType", ignore = true)
    })
    SkuOrderInfoDTO skuDO2skuOrderInfoDTO(SkuDO skuDO);

    List<SkuDO> skuInfoBOList2skuDOList(List<SkuInfoBO> skuInfoBOList);

    @Mappings({

            @Mapping(target = "isSoldOut", source = "isSoldOut"),
            @Mapping(target = "isTheLimit", source = "isTheLimit"),
            @Mapping(target = "isItReset", source = "isItReset")
    })
    EstimateDO estimateReqDTO2estimateDO(EstimateReqDTO estimateReqDTO);


    @Mappings({
            @Mapping(target = "storeGuid", ignore = true),
            @Mapping(target = "isSoldOut", source = "isSoldOut"),
            @Mapping(target = "isTheLimit", source = "isTheLimit"),
            @Mapping(target = "isItReset", source = "isItReset")
    })
    EstimateRespDTO estimateDO2EstimateReqDTO(EstimateDO estimateDO);

    @Mappings({
            @Mapping(target = "effectiveStartTime", ignore = true),
            @Mapping(target = "effectiveEndTime", ignore = true)
    })
    ItemTemplateDO itemTemplateReqDTO2itemTemplateDO(ItemTemplateReqDTO request);

    ItemTMenuSubitemDO itemMenuSubItemReqDTO2itemTMenuSubitemDO(ItemMenuSubItemReqDTO request);

    ItemTMenuDO itemTMenuReqDTO2itemTMenuDO(ItemTMenuReqDTO request);

    @Mappings({
            @Mapping(target = "timesQuantum", ignore = true),
            @Mapping(target = "weeksQuantum", ignore = true),
            @Mapping(target = "times", ignore = true),
            @Mapping(target = "weeks", ignore = true)
    })
    ItemTMenuValidityDO itemTemplateMenuValidityReqDTO2itmeTMenuValidityDO(ItmeTemplateMenuValidityReqDTO request);


    List<ItemTemplateMenuSubItemDetailRespDTO> detailQuerys2SubItemDetailRespDTOs(List<ItemTemplateMenuSubItemDetailQuery> request);

    ItemTemplateMenuSubItemDetailRespDTO detailQuerys2SubItemDetailRespDTO(ItemTemplateMenuSubItemDetailQuery request);

    List<ItemTMenuSubitemDO> itemMenuSubItemReqDTO2itemTMenuSubitemDOs(List<ItemMenuSubItemReqDTO> saveList);

    List<ItemTemplateMenuSubItemDetailRespDTO> subItemDOList2SubItemDetailRespDTOList(List<ItemTMenuSubitemDO> list);

    ItemTemplateMenuSubItemDetailRespDTO subItemDOList2SubItemDetailRespDTO(ItemTMenuSubitemDO request);

    List<ItemBatchImportTempRespDTO> itemDoList2ItemBatchImportTempLists(List<ItemDO> list);

    @Mapping(target = "itemName", source = "name")
    ItemBatchImportTempRespDTO itemDo2ItemBatchImportTemp(ItemDO request);

    List<ItemSortRespDTO> itemDOList2ItemSortRespDTOS(List<ItemDO> list);

    ItemSortRespDTO itemDOItemSortRespDTO(ItemDO request);

    @Mappings({
            @Mapping(target = "currentPage", source = "current"),
            @Mapping(target = "pageSize", source = "size"),
            @Mapping(target = "totalCount", source = "total"),
            @Mapping(target = "data", source = "records")
    })
    Page<ItemSortRespDTO> itemDOListPage2ItemSortRespDTOPageS(IPage<ItemDO> itemDOIPage);

    List<ItemDO> ItemSortReqDTOList2ItemDOList(List<ItemSortReqDTO> request);

    @Mapping(target = "id", ignore = true)
    ItemDO ItemSortReqDTO2ItemDO(ItemSortRespDTO request);

    List<TypeDO> typeSortReqDTOList2TypeDOList(List<TypeSortReqDTO> request);

    @Mappings({
            @Mapping(target = "guid", source = "guid"),
            @Mapping(target = "name", source = "name"),
            @Mapping(target = "sort", source = "sort"),
    })
    TypeDO typeSortReqDTO2TypeDO(TypeSortReqDTO request);

    List<TypeSortRespDTO> typeWebRespListS2TypeSortReqList(List<TypeWebRespDTO> request);

    @Mapping(target = "guid", source = "typeGuid")
    TypeSortRespDTO typeWebResp2TypeSortReq(TypeWebRespDTO request);

    @Mappings({
            @Mapping(source = "itemGuid", target = "guid"),
            @Mapping(source = "name", target = "name"),
            @Mapping(source = "pinyin", target = "pinyin"),
            @Mapping(source = "itemFrom", target = "itemFrom"),
            @Mapping(source = "itemType", target = "itemType"),
            @Mapping(source = "storeGuid", target = "storeGuid")
    })
    ItemDO groupMealSaveReqDTO2itemDO(ItemGroupMealSaveReqDTO request);


    List<GroupMealDO> groupMealSubSkuReqDTOList2GroupMealDOList(List<GroupMealSkuSaveReqDTO> request);

    @Mappings({
            @Mapping(target = "subItemGuid", source = "itemGuid"),
            @Mapping(target = "guid", source = "guid")
    })
    GroupMealDO groupMealSubSkuReqDTO2GroupMealDO(GroupMealSkuSaveReqDTO request);

    List<GroupMealSubItemSkuWebRespDTO> subGroupMealBOS2GroupMealSubItemS(List<GroupMealSubItemBO> request);

    @Mappings({
            @Mapping(target = "skuSubgroupGuid", source = "guid"),
            @Mapping(target = "costPrice", source = "costPrice"),
            @Mapping(target = "price", source = "salePrice"),
            @Mapping(source = "num", target = "itemNum"),
            @Mapping(source = "sort", target = "sort"),
            @Mapping(source = "unit", target = "unit"),
            @Mapping(source = "subItemGuid", target = "itemGuid"),
            @Mapping(source = "subSkuGuid", target = "skuGuid"),
            @Mapping(target = "name", expression = "java(getGroupMealSubItemName(request))"),
            @Mapping(target = "grossMargin", expression = "java(getGroupMealSubItemGrossMargin(request))"),
            @Mapping(target = "salePrice", source = "subSkuSalePrice"),
            @Mapping(target = "saleCostPrice", source = "subSkuCostPrice")
    })
    GroupMealSubItemSkuWebRespDTO subGroupMealBO2GroupMealSubItem(GroupMealSubItemBO request);


    default String getGroupMealSubItemName(GroupMealSubItemBO request) {
        return StringUtils.isEmpty(request.getSkuName())
                ? request.getItemName()
                : request.getItemName() + "(" + request.getSkuName() + ")";
    }

    default BigDecimal getGroupMealSubItemGrossMargin(GroupMealSubItemBO request) {
        return Optional.ofNullable(request)
                .map(GroupMealSubItemBO::getCostPrice)
                .isPresent()
                && request.getSalePrice().compareTo(BigDecimal.ZERO) != 0
                ? (request.getSalePrice().subtract(request.getCostPrice()))
                .divide(request.getSalePrice(), 4, RoundingMode.HALF_UP)
                : null;
    }

    GroupMealItemDetailRespDTO itemDO2GroupMealItemDetailRespDTO(ItemDO itemDO);

    List<SubItemSkuSynRespDTO> groupMealSubItemBOS2SubItemSkuSynRespDTOS(List<GroupMealSubItemBO> request);

    @Mappings({
            @Mapping(target = "salePrice", source = "subSkuSalePrice"),
            @Mapping(target = "itemGuid", source = "subItemGuid"),
            @Mapping(target = "skuGuid", source = "subSkuGuid"),
            @Mapping(target = "addPrice", constant = "0"),
            @Mapping(target = "hasAttr", constant = "0"),
            @Mapping(target = "defaultNum", constant = "0"),
            @Mapping(target = "isRepeat", constant = "0"),
            @Mapping(target = "itemNum", source = "num"),
            @Mapping(target = "itemType", source = "subItemType"),
            @Mapping(target = "skuName", source = "skuName", defaultValue = ""),
            @Mapping(target = "attrGroupList", expression = "java(java.util.Collections.emptyList())")
    })
    SubItemSkuSynRespDTO groupMealSubItemBO2SubItemSkuSynRespDTO(GroupMealSubItemBO request);

    @Mappings({
            @Mapping(source = "planItemReqDTO.brandGuid", target = "brandGuid"),
            @Mapping(source = "planItemReqDTO.planGuid", target = "planGuid"),
            @Mapping(source = "planItemDTO.itemGuid", target = "itemGuid"),
    })
    PricePlanItemDO planItemDTO2DO(PricePlanItemAddReqDTO planItemReqDTO, PlanItemDTO planItemDTO);

    PricePlanBingStoreRespDTO PricePlanStoreDO2DTO(PricePlanStoreDO PricePlanStoreDO);

    List<PricePlanBingStoreRespDTO> PricePlanStoreDOList2DTOList(List<PricePlanStoreDO> PricePlanStoreDOList);

    PricePlanRespDTO planDO2PlanRespDTO(PricePlanDO respDTO);

    List<PricePlanRespDTO> PlanDOList2PlanRespDTOList(List<PricePlanDO> pricePlanDOList);

    PricePlanDO planReqDTO2PlanDO(PricePlanReqDTO pricePlanReqDTO);

    @Mapping(source = "guid", target = "typeGuid")
    TypePricePlanReqDTO typeDO2TypePricePlanReqDTO(TypeDO typeDO);

    ItemSynRespDTO infoRespDTO2ItemSynRespDTO(ItemInfoRespDTO infoRespDTO);

    @Mappings({
            @Mapping(source = "guid", target = "typeGuid"),
            @Mapping(source = "name", target = "typeName")
    })
    PlanPriceAllTypeRespDTO typeDO2TypeDTO(TypeDO typeDO);

    List<PlanPriceAllTypeRespDTO> typeDOList2TypeDTOList(List<TypeDO> typeDOList);

    PricePlanReqDTO planDO2PlanReqDTO(PricePlanDO pricePlanDO);

    List<PricePlanReqDTO> planDOList2PlanReqDTOList(List<PricePlanDO> pricePlanDOS);

    @Mappings({
            @Mapping(target = "gmtCreate", ignore = true)
    })
    ItemWebRespDTO planItemDO2ItemWebRespDTO(PricePlanItemDO planItemDO);

    List<ItemWebRespDTO> planItemDOList2ItemWebRespDTOList(List<PricePlanItemDO> planItemDOList);

    ItemWebRespDTO itemWebRespDTO2ItemWebRespDTO(ItemWebRespDTO itemWebRespDTO);

    List<ItemWebRespDTO> itemWebRespDTO2ItemWebRespDTOList(List<ItemWebRespDTO> itemWebRespDTOList);

    SkuRespDTO itemWebRespDTO2SkuRespDTO(ItemWebRespDTO itemWebRespDTO);

    List<SkuRespDTO> itemWebRespDTO2SkuRespDTOList(List<ItemWebRespDTO> itemWebRespDTOList);

    @Mappings({
            @Mapping(source = "guid", target = "typeGuid"),
    })
    TypeRespDTO typeDO2TypeRespDTO(TypeDO typeDO);

    List<TypeRespDTO> typeDO2TypeRespDTOList(List<TypeDO> typeDOList);
}

