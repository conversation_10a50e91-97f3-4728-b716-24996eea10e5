package com.holderzone.saas.store.item.controller;


import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.MchntItemBaseDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupUpdateReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrSaveReqDTO;
import com.holderzone.saas.store.dto.item.resp.AttrGroupAttrRespDTO;
import com.holderzone.saas.store.dto.item.resp.AttrRespDTO;
import com.holderzone.saas.store.item.service.IAttrGroupService;
import com.holderzone.saas.store.item.service.IAttrService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/attr")
@Api(value = "属性接口")
public class AttrController {

    private final IAttrGroupService attrGroupService;

    private final IAttrService attrService;

    @Autowired
    public AttrController(IAttrGroupService attrGroupService, IAttrService attrService) {
        this.attrGroupService = attrGroupService;
        this.attrService = attrService;
    }

    @ApiOperation(value = "获取属性组", notes = "属性组列表 data:门店guid  keywords: 0:查询自建属性  2：推送属性")
    @PostMapping("/list_attr_group")
    public List<AttrGroupAttrRespDTO> listAttrGroup(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("属性组列表接口入参,storeGuid：{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        checkFrom(itemSingleDTO);
        return attrGroupService.listAttrGroupByOrganization(itemSingleDTO);
    }


    @ApiOperation(value = "获取属性组", notes = "新增商品获取属性组列表 data: storeGuid or brandGuid")
    @PostMapping("/list_attr_for_save_item")
    public List<AttrGroupAttrRespDTO> listAttrForSaveItem(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("新增商品获取属性组列表接口入参,storeGuid：{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        checkFrom(itemSingleDTO);
        return attrGroupService.listAttrForSaveItem(itemSingleDTO);
    }

    @ApiOperation(value = "获取属性", notes = "获取某个属性组下属性值列表 datas:属性组guid集合")
    @PostMapping("/list_attr_by_group")
    public List<AttrRespDTO> listAttrByGroup(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("获取某个属性组下属性值列表入参,属性组guid：{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        checkFrom(itemSingleDTO);
        return attrService.listAttrByGroup(itemSingleDTO);
    }


    @ApiOperation(value = "新建属性组", notes = "新建属性组")
    @PostMapping("/save_attr_group")
    public boolean addAttrGroup(@RequestBody @Valid AttrGroupReqDTO attrGroupReqDTO) {
        log.info("save_attr_group接口入参,attrGroupReqDTO={}", JacksonUtils.writeValueAsString(attrGroupReqDTO));
        checkFrom(attrGroupReqDTO);
        return attrGroupService.addAttrGroup(attrGroupReqDTO);
    }

    @ApiOperation(value = "修改属性组", notes = "修改属性组")
    @PostMapping("/set_attr_group")
    public boolean updateAttrGroup(@RequestBody AttrGroupUpdateReqDTO attrGroupUpdateReqDTO) {
        log.info("set_attr_group,attrGroupUpdateReqDTO={}", JacksonUtils.writeValueAsString(attrGroupUpdateReqDTO));
        checkFrom(attrGroupUpdateReqDTO);
        return attrGroupService.updateAttrGroup(attrGroupUpdateReqDTO);
    }


    @ApiOperation(value = "保存属性", notes = "保存属性")
    @PostMapping("/save_attr")
    public boolean saveAttrValue(@RequestBody @Valid AttrReqDTO attrReqDTO) {
        log.info("新建属性值接口入参,attrReqDTO={}", JacksonUtils.writeValueAsString(attrReqDTO));
        checkFrom(attrReqDTO);
        return attrService.saveAttrValue(attrReqDTO);
    }

    @ApiOperation(value = "修改属性", notes = "修改属性")
    @PostMapping("/update_attr")
    public boolean updateAttrValue(@RequestBody @Valid AttrSaveReqDTO attrSaveReqDTO) {
        log.info("修改属性值入参:{}", JacksonUtils.writeValueAsString(attrSaveReqDTO));
        checkFrom(attrSaveReqDTO);
        return attrService.update(attrSaveReqDTO);
    }

    @ApiOperation(value = "删除属性值", notes = "删除属性值 data:属性guid")
    @PostMapping("/delete_attr")
    public boolean deleteAttrValue(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("删除属性值入参(属性值guid)：{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        checkFrom(itemSingleDTO);
        return attrService.deleteByGuid(itemSingleDTO.getData());
    }

    @ApiOperation(value = "删除属性组", notes = "删除属性组 data:属性组guid")
    @PostMapping("/delete_attr_group")
    public boolean deleteAttrGroup(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("删除属性组入参，属性组guid：{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        checkFrom(itemSingleDTO);
        return attrGroupService.deleteByGuid(itemSingleDTO);
    }

    /**
     * 检查属性是否被使用
     *
     * @param attrGuid 属性guid
     * @return Boolean
     */
    @ApiOperation(value = "检查属性是否被使用")
    @GetMapping("/check_attr_used")
    public Boolean checkAttrUsed(@RequestParam("attrGuid") String attrGuid) {
        log.info("检查属性是否被使用入参,attrGuid={}", attrGuid);
        return attrService.checkAttrUsed(attrGuid);
    }

    /**
     * 入参校验
     */
    private void checkFrom(MchntItemBaseDTO mchntItemBaseDTO) {
        if (ObjectUtils.isEmpty(mchntItemBaseDTO.getFrom())) {
            throw new ParameterException("调用内部服务请必填模块入口参数：form");
        }
    }
}
