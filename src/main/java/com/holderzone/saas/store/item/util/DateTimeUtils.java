package com.holderzone.saas.store.item.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalTime;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @className 时间工具类
 * @date 2019/05/23 17:33
 * @description 时间相关的处理
 * @program holder-saas-aggregation-app
 */
public class DateTimeUtils {

    /**
     * 判断当前时间是否在[strStartTime, strEndTime]区间，
     *
     * @param strTime      比较时间
     * @param strStartTime 开始时间
     *                     * @param strEndTime 结束时间
     * @param pattern      时间转换格式
     * @return
     * <AUTHOR>
     */
    public static boolean isEffectiveDate(String strTime, String strStartTime, String strEndTime, String pattern) throws ParseException {
        Date startTime = new SimpleDateFormat(pattern).parse(strStartTime);
        Date endTime = new SimpleDateFormat(pattern).parse(strEndTime);
        Date time = new SimpleDateFormat(pattern).parse(strTime);
        if (time.getTime() == startTime.getTime()
                || time.getTime() == endTime.getTime()) {
            return true;
        }
        Calendar date = Calendar.getInstance();
        date.setTime(time);
        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);
        Calendar end = Calendar.getInstance();
        end.setTime(endTime);
        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 校验时间段是否重叠
     *
     * @param startCompareTime 被比较的开始时间
     * @param endCompareTime   被比较的结束时间
     * @param startTime        比较的开始时间
     * @param endTime          比较的结束时间
     * @return 重叠返回true
     */
    public static boolean checkOverlap(LocalTime startCompareTime, LocalTime endCompareTime, LocalTime startTime, LocalTime endTime) {
        return minuteDuration(endCompareTime,startCompareTime) + minuteDuration(endTime,startTime)
                >= minuteDuration(endCompareTime,startTime);

//        boolean isOverlap = (startCompareTime.compareTo(startTime) >= 0 && startCompareTime.compareTo(endTime) <= 0)
//                || (endCompareTime.compareTo(endTime) <= 0 && endCompareTime.compareTo(startTime) >= 0);
//        boolean isOverlapToo = (17:00.isAfter(13:01) && 5:59.isBefore(16:59));
//        if (isOverlap || isOverlapToo) {
//            return true;
//        }
//        // 跨天处理
//        if (startCompareTime.isAfter(endCompareTime)) {
//            isOverlap = startTime.isAfter(startCompareTime) || endTime.isAfter(startCompareTime)
//                    || startTime.isBefore(endCompareTime) || endTime.isBefore(endCompareTime);
//            if (isOverlap) {
//                return true;
//            }
//        }
//        if (startTime.isAfter(endTime)) {
//            return startCompareTime.isAfter(startTime) || startCompareTime.isBefore(endTime)
//                    || endCompareTime.isAfter(startTime) || endCompareTime.isBefore(endTime);
//        }
//        return false;
    }
    private static long minuteDuration(LocalTime endTime,LocalTime startTime){
        return endTime.compareTo(startTime) < 0 ?
                24*60 - Duration.between(startTime,endTime).abs().toMinutes()
                : Duration.between(startTime,endTime).abs().toMinutes();
    }

}
