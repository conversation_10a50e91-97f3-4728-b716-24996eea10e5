package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品与属性组关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsi_r_item_attr_group")
public class RItemAttrGroupDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @TableLogic
    private Integer isDelete;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 商品GUID
     */
    private String itemGuid;

    /**
     * 属性组GUID
     */
    private String attrGroupGuid;

    /**
     * 是否必选:0 否 1 是
     */
    private Integer isRequired;

    /**
     * 是否支持多选:0 否 1 是
     */
    private Integer isMultiChoice;

    /**
     * 是否有默认选项：0：否1：是
     */
    private Integer withDefault;


}
