package com.holderzone.saas.store.item.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 价格方案推送处理时需要的商品
 */
@Data
@Accessors(chain = true)
public class SyncItemDTO {

    /**
     * 商品guid
     */
    private String itemGuid;

    /**
     * 是否套餐
     */
    private Boolean isPkgItem;

    /**
     * 规格guid
     */
    private String skuGuid;

    /**
     * 方案销售价格
     */
    private BigDecimal salePrice;

    /**
     * 方案会员价格
     */
    private BigDecimal memberPrice;

    /**
     * 是否删除
     */
    private Boolean isDelete;

    /**
     * 商品原销售价
     */
    private BigDecimal originalSalePrice;

    /**
     * 商品原会员价
     */
    private BigDecimal originalMemberPrice;

    /**
     * 是否上架(0：否，1：是)
     */
    private Integer isRack;

    /**
     * 是否上架一体机（0：否，1：是）
     */
    private Integer isJoinAio;

    /**
     * 是否上架POS机（0：否，1：是）
     */
    private Integer isJoinPos;

    /**
     * 是否上架Pad（0：否，1：是）
     */
    private Integer isJoinPad;
}
