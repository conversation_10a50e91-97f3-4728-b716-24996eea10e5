package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.ItemQueryReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemRetailSortReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemRetailSortRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemSortRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import com.holderzone.saas.store.item.entity.domain.ItemDO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className IRetailItemService
 * @date 2019/10/26 下午2:27
 * @description //零售商品service
 * @program holder-saas-store-item
 */

public interface IRetailItemService extends IService<ItemDO> {

    /**
     * 商超商户后台获取商品列表
     * @param itemQueryReqDTO
     * @return
     */
    Page<ItemWebRespDTO> selectItemListForWeb(ItemQueryReqDTO itemQueryReqDTO);


    /**
     * 商超排序分类获取商品集合
     *
     * @param request
     * @return
     */
    Page<ItemSortRespDTO> getItemSortList(ItemQueryReqDTO request);

    /**
     * 商超后台批量更新商品排序
     * @param request
     * @return
     */
    Boolean retailUpdateItemSort(ItemRetailSortReqDTO request);

    /**
     * 判断商品类型下是否存在商品
     * @param request
     * @return
     */
    boolean verifyThatitemExistsForType(SingleDataDTO request);

    /**
     * 商超获取分类排序和分类下商品集合
     * @param request
     * @return
     */
    ItemRetailSortRespDTO getSortTypeAndItems(ItemSingleDTO request);
}
