package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.item.common.ItemDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.resp.TypeSynRespDTO;
import com.holderzone.saas.store.item.entity.domain.TypeDO;
import com.holderzone.saas.store.item.entity.query.JournalingItemsQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 商品分类 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Repository
public interface TypeMapper extends BaseMapper<TypeDO> {

    List<JournalingItemsQuery> queryJournalingItemType();

    Integer deleteByGuid(String guid);

    List<TypeSynRespDTO> queryBrandGuidAndStoreName(@Param("query") ItemSingleDTO query);

    List<TypeSynRespDTO> queryOrdinaryByStore(@Param("query") ItemSingleDTO query);

    List<TypeSynRespDTO> queryPlanByStore(@Param("query") ItemSingleDTO query);

    List<ItemDTO> queryPlanSku(@Param("itemGuidList") List<String> itemGuidList, @Param("planGuid") String planGuid);
}
