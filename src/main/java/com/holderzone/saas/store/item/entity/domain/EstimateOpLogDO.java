package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品估清操作记录
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsi_estimate_op_log")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EstimateOpLogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 恢复售卖时间
     */
    private LocalDateTime businessStart;

    /**
     * 商品GUID
     */
    private String itemGuid;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 规格GUID
     */
    private String skuGuid;

    /**
     * 规格名称(固定规格名称为空字符串"")
     */
    private String skuName;

    /**
     * 操作提交的数据
     */
    private String params;

    /**
     * 相关id
     */
    private String opRefid;

    /**
     * 操作类型
     */
    private String opType;

    /**
     * 操作时间
     */
    private LocalDateTime opTime;

    /**
     * 操作员id
     */
    private String operatorGuid;

    /**
     * 操作员名称
     */
    private String operatorName;

    /**
     * 设备guid
     */
    private String deviceGuid;

}
