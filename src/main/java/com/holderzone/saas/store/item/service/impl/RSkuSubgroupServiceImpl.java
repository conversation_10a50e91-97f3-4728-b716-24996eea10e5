package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.item.req.SubItemSkuReqDTO;
import com.holderzone.saas.store.item.entity.domain.RSkuSubgroupDO;
import com.holderzone.saas.store.item.entity.domain.SubgroupDO;
import com.holderzone.saas.store.item.mapper.RSkuSubgroupMapper;
import com.holderzone.saas.store.item.service.IRSkuSubgroupService;
import com.holderzone.saas.store.item.service.ISubgroupService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.MapStructUtils;
import com.holderzone.sdk.util.BatchIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 规格与分组关联表（规格为套餐分组中的子菜规格） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-25
 */
@Service
public class RSkuSubgroupServiceImpl extends ServiceImpl<RSkuSubgroupMapper, RSkuSubgroupDO> implements IRSkuSubgroupService {

    private final ISubgroupService subgroupService;
    private final DynamicHelper dynamicHelper;
    private final RedisTemplate redisTemplate;

    private final RSkuSubgroupMapper rSkuSubgroupMapper;


    @Autowired
    public RSkuSubgroupServiceImpl(ISubgroupService subgroupService, DynamicHelper dynamicHelper, RedisTemplate redisTemplate, RSkuSubgroupMapper rSkuSubgroupMapper) {
        this.subgroupService = subgroupService;
        this.dynamicHelper = dynamicHelper;
        this.redisTemplate = redisTemplate;
        this.rSkuSubgroupMapper = rSkuSubgroupMapper;
    }

    @Transactional
    @Override
    public Integer removeSkuSubgroupByPkgGuidList(List<String> itemGuidList) {
        if (CollectionUtils.isEmpty(itemGuidList)) {
            return 1;
        }
        List<SubgroupDO> subgroupDOList = subgroupService.list(new LambdaQueryWrapper<SubgroupDO>().in(SubgroupDO::getItemGuid, itemGuidList));
        Set<String> subgroupGuidSet = subgroupDOList.stream().map(SubgroupDO::getGuid).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(subgroupGuidSet)) {
            throw new BusinessException("套餐下分组不能为空");
        }
        boolean remove = remove(new LambdaQueryWrapper<RSkuSubgroupDO>().in(RSkuSubgroupDO::getSubgroupGuid, subgroupGuidSet));
        return remove ? 1 : 0;
    }

    @Override
    public List<RSkuSubgroupDO> buildRSkuSubgroupDOWithSubgroupDO(List<SubItemSkuReqDTO> subItemSkuList, SubgroupDO subgroupDO) {
        List<RSkuSubgroupDO> skuSubgroupDOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(subItemSkuList) || subgroupDO == null) {
            return skuSubgroupDOList;
        }
        subItemSkuList.forEach(subItemSkuReqDTO -> {
            RSkuSubgroupDO skuSubgroupDO = MapStructUtils.INSTANCE.subItemSkuReqDTO2skuSubgroupDO(subItemSkuReqDTO);
            try {
                skuSubgroupDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
            } catch (IOException e) {
                throw new BusinessException("BatchIdGenerator生成商品guid失败");
            }
            skuSubgroupDO.setSubgroupGuid(subgroupDO.getGuid());
            // 如果是固定分组
            if (subgroupDO.getPickNum() == 0) {
                fixFixedSubgroupSubItemFields(skuSubgroupDO);
            }
            skuSubgroupDOList.add(skuSubgroupDO);
        });
        return skuSubgroupDOList;
    }

    /**
     * 修改固定分组的子商品字段
     *
     * @param skuSubgroupDO
     */
    private void fixFixedSubgroupSubItemFields(RSkuSubgroupDO skuSubgroupDO) {
        skuSubgroupDO.setAddPrice(BigDecimal.ZERO);
        skuSubgroupDO.setIsDefault(1);
        skuSubgroupDO.setIsRepeat(0);
    }

    @Override
    public Boolean updateIsDelete(String guid) {
        return rSkuSubgroupMapper.updateIsDelete(guid);
    }

    @Override
    public Boolean deleteByItemGuid(String itemGuid) {
        return rSkuSubgroupMapper.deleteByItemGuid(itemGuid);
    }


}
