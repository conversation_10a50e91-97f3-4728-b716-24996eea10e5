package com.holderzone.saas.store.item.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 菜谱变化类型枚举
 * @date 2021/4/9 14:16
 */
@Getter
@AllArgsConstructor
public enum PlanChangeTypeEnum {

    // 变动类型，0切换菜谱；1菜谱变化(当前进行中的菜谱有变化)；2没有变化(依然在当前进行中的菜谱)；3菜谱方案有效，但没有进行中的菜谱；4普通模式

    /**
     * 切换菜谱
     */
    PRICE_PLAN_CHANGE(0, "切换菜谱"),

    /**
     * 菜谱变化
     */
    PRICE_PLAN_MODIFY(1, "菜谱变化(当前进行中的菜谱有变化)"),

    /**
     * 没有变化
     */
    PRICE_PLAN_NORMAL(2, "没有变化(依然在当前进行中的菜谱)"),

    /**
     * 菜谱方案有效
     */
    PRICE_PLAN_NOT_EXIST(3, "菜谱方案有效，但没有进行中的菜谱"),

    /**
     * 普通模式
     */
    PRICE_PLAN_MODEL(4, "普通模式"),
    ;

    private final Integer code;

    private final String desc;
}
