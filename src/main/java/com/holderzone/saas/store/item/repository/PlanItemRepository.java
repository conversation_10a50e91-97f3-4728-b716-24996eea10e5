package com.holderzone.saas.store.item.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.saas.store.item.config.LocalCacheConfig;
import com.holderzone.saas.store.item.entity.domain.PricePlanDO;
import com.holderzone.saas.store.item.entity.domain.PricePlanItemDO;
import com.holderzone.saas.store.item.entity.domain.PricePlanStoreDO;
import com.holderzone.saas.store.item.entity.enums.ItemStateEnum;
import com.holderzone.saas.store.item.entity.enums.PricePlanStatusEnum;
import com.holderzone.saas.store.item.mapper.PricePlanItemMapper;
import com.holderzone.saas.store.item.mapper.PricePlanMapper;
import com.holderzone.saas.store.item.mapper.PricePlanStoreMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-01-03
 * @description 菜谱商品仓储层操作
 *
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class PlanItemRepository {

    private final PricePlanItemMapper planItemMapper;

    private final PricePlanStoreMapper planStoreMapper;

    private final PricePlanMapper pricePlanMapper;


    public List<PricePlanItemDO> listEffectPlanItem(String effectStorePlanGuid,String itemGuid) {
        return planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                .eq(PricePlanItemDO::getIsDelete, 0)
                .ne(PricePlanItemDO::getIsSoldOut, ItemStateEnum.IMMEDIATELY_DELETE.getCode())
                .eq(PricePlanItemDO::getPlanGuid, effectStorePlanGuid)
                .eq(StringUtils.isNotEmpty(itemGuid), PricePlanItemDO::getItemGuid, itemGuid)
                .orderByAsc(PricePlanItemDO::getSort));
    }

    public List<PricePlanItemDO> listAllByStoreAndBrand(String storeGuid, String brandGuid) {
        List<PricePlanStoreDO> planStoreDOList = planStoreMapper.selectList(new LambdaQueryWrapper<PricePlanStoreDO>()
                .eq(PricePlanStoreDO::getStoreGuid, storeGuid)
                .eq(PricePlanStoreDO::getBrandGuid, brandGuid)
                .eq(PricePlanStoreDO::getIsDelete, 0)
        );
        List<String> planGuidList = planStoreDOList.stream()
                .map(PricePlanStoreDO::getPlanGuid)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(planGuidList)) {
            return Lists.newArrayList();
        }
        List<PricePlanDO> pricePlanDOList = pricePlanMapper.selectList(new LambdaQueryWrapper<PricePlanDO>()
                .in(PricePlanDO::getGuid, planGuidList)
                .eq(PricePlanDO::getIsDelete, 0)
                .and(p -> p.eq(PricePlanDO::getStatus, PricePlanStatusEnum.USING.getCode())
                        .or()
                        .eq(PricePlanDO::getStatus, PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode())
                )
        );
        if (CollectionUtils.isEmpty(pricePlanDOList)) {
            log.info("可用菜谱为空，门店guid：{}", storeGuid);
            return Lists.newArrayList();
        }
        List<String> queryList = pricePlanDOList.stream()
                .map(PricePlanDO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        List<PricePlanItemDO> planItemDOList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                .in(PricePlanItemDO::getPlanGuid, queryList)
                .eq(PricePlanItemDO::getIsDelete, 0)
        );
        return planItemDOList;
    }
}
