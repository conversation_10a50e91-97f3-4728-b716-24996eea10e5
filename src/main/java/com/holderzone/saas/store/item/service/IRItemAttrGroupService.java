package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.item.entity.bo.AttrGroupListAndAttrListBO;
import com.holderzone.saas.store.item.entity.bo.ItemInfoBO;
import com.holderzone.saas.store.item.entity.domain.RItemAttrGroupDO;

import java.util.List;

/**
 * <p>
 * 商品与属性组关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
public interface IRItemAttrGroupService extends IService<RItemAttrGroupDO> {

    /**
     * 查询指定商品下关联的属性组以及属性关联实体集合
     * @param itemGuidList
     * @return
     */
    List<AttrGroupListAndAttrListBO> selectAttrGroupAndAttrByItemGuidList(List<String> itemGuidList);

    /**
     * 新增或更新属性组以及属性与指定商品的关联关系，并删除无用的关联关系
     * @param itemInfoBOList 指定商品集合
     * @return
     */
    boolean saveOrUpdateAndDeleteAttrGroupAndAttrRelation(List<ItemInfoBO> itemInfoBOList);
}
