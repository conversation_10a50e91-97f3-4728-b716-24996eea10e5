package com.holderzone.saas.store.item.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.item.req.DoubleDataPageDTO;
import com.holderzone.saas.store.dto.item.req.SingleDataPageDTO;
import com.holderzone.saas.store.dto.item.resp.ErpItemDTO;
import com.holderzone.saas.store.dto.item.resp.ErpTypeDTO;

import java.util.List;

public interface ErpService {

    List<ErpTypeDTO> listType(String storeGuid);

    Page<ErpItemDTO> pageItem(SingleDataPageDTO singleDataPageDTO);

    Page<ErpItemDTO> pageItemOfType(SingleDataPageDTO singleDataPageDTO);

    Page<ErpItemDTO> pageItemByName(DoubleDataPageDTO doubleDataPageDTO);
}
