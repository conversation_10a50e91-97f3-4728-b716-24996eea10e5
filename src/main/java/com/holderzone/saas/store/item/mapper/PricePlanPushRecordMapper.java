package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.item.dto.PushRecordStatusUpdateReqDTO;
import com.holderzone.saas.store.item.entity.domain.PricePlanPushRecordDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PricePlanPushRecordMapper extends BaseMapper<PricePlanPushRecordDO> {

    List<PricePlanPushRecordDO> getPushRecords(@Param("planGuid") String planGuid);

    void updatePushStatus(@Param("reqDTO") PushRecordStatusUpdateReqDTO reqDTO);

    void deletePushRecords(@Param("planGuid") String planGuid);
}
