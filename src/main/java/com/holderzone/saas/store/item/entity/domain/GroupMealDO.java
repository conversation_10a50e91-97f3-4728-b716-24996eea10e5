package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GroupMealDO
 * @date 2019/12/06 下午5:03
 * @description //团餐关系表
 * @program holder-saas-store-item
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsi_group_meal")
public class GroupMealDO implements Serializable {

    /**
     * 主键
     */
    private Long id;
    /**
     * 业务主键
     */
    @TableId(value = "guid",type = IdType.INPUT )
    private String guid;
    /**
     * 团餐主项商品guid
     */
    private String itemGuid;
    /**
     * 团餐子项商品guid
     */
    private String subItemGuid;
    /**
     * 团餐子项skuguid
     */
    private String skuGuid;
    /**
     * 团餐子项数量
     */
    private BigDecimal num;

    /**
     * 排序 越小越靠前
     */
    private Integer sort;

    /**
     * 门店guid
     */
    private String storeGuid;
    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;
    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否逻辑删除 0：否 1：是
     */
    @TableLogic
    private Integer isDelete;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GroupMealDO that = (GroupMealDO) o;
        return Objects.equals(guid, that.guid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(guid);
    }
}
