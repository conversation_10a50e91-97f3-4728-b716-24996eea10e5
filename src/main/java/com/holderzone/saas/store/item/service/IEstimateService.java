package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.common.ItemSkuStockDTO;
import com.holderzone.saas.store.dto.item.req.EstimateBatchReqDTO;
import com.holderzone.saas.store.dto.item.req.EstimateForManualReqDTO;
import com.holderzone.saas.store.dto.item.req.EstimateMerchantReqDTO;
import com.holderzone.saas.store.dto.item.req.EstimateReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateResultRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.item.entity.bo.EstimateBO;
import com.holderzone.saas.store.item.entity.domain.EstimateDO;
import com.holderzone.saas.store.item.entity.query.SetMealEstimateQuery;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @className IEstimateService
 * @date 2019/05/07 14:27
 * @description 商品sku估清表 服务接口类
 * @program holder-saas-store-itme
 */
public interface IEstimateService extends IService<EstimateDO> {

    /**
     * 设置或更新商品Sku开启估清
     *
     * @param request
     * @return
     */
    Boolean saveOrUpdate(EstimateReqDTO request);

    /**
     * 设置或更新商品Sku开启估清
     *
     * @param request
     * @return
     */
    Boolean batchSave(EstimateBatchReqDTO request);

    /**
     * 根据Store_guid和Sku查询单个商品估清状态
     *
     * @param request
     * @return
     */
    EstimateRespDTO getEstimateReqDTO(EstimateReqDTO request);


    /**
     * @param request
     * @return 商户后台获取估清菜品列表
     */
    Page<EstimateMerchantConfigRespDTO> getItemEstimates(EstimateMerchantReqDTO request);

    /**
     * @param request
     * @return 商户后台菜品剩余量列表
     */
    Page<EstimateItemResidueMemchantRespDTO> getEstimateItemResidue(EstimateMerchantReqDTO request);

    /**
     * @param request
     * @return 客户端下单时验证菜品是否估清，并扣减库存数量
     */
    EstimateResultRespDTO verifyDineInItemEstimate(List<DineInItemDTO> request);

    /**
     * @param request
     * @return 增加库存
     */
    Boolean incStock(@RequestBody @Valid List<ItemSkuStockDTO> request);

    /**
     * @param request
     * @return 减少库存
     */
    EstimateResultRespDTO descStock(@RequestBody @Valid List<ItemSkuStockDTO> request);


    /**
     * @param request
     * @return 客户端下单支付失败、退菜 加上库存
     */
    Boolean dineinFail(List<DineInItemDTO> request);

    /**
     * 安卓估清菜品同步
     *
     * @param baseDTO
     * @return
     */
    List<ItemEstimateForAndroidRespDTO> queryEstimateForSyn(BaseDTO baseDTO);

    /**
     * 一体机手动设置菜品估清状态
     *
     * @param request
     * @return
     */
    boolean saveSoldOutStatus(EstimateForManualReqDTO request);

    /**
     * 定时job 估清自动置满
     *
     * @param request
     * @return
     */
    Integer storeItemEstimateReset(Map<String, List<String>> request);

    /**
     * 根据套餐itemguid 查询子项是否估清
     *
     * @param itemGuid
     * @param storeGuid
     * @return
     */
    List<SetMealEstimateQuery> getSetMealSubitemEstimate(String itemGuid, String storeGuid);

    List<ItemEstimateForAndroidDTO> queryEstimateByGuids(String storeGuid, List<String> skuGuids);

    /**
     * 新的一体机估清接口
     * 老估清接口也在使用
     *
     * @param biz 入参
     * @return Boolean
     */
    Boolean saveSoldOut(EstimateBO biz);

    /**
     * 一体机估清商品列表
     *
     * @param storeGuid 门店guid
     * @return 一体机估清商品列表
     */
    EstimateForAndroidRespDTO listEstimate(String storeGuid);

    /**
     * 批量取消估清
     *
     * @param biz 规格Guid列表
     * @return Boolean
     */
    Boolean batchCancelEstimate(EstimateBO biz);

    /**
     * 批量停售
     *
     * @param biz 规格Guid列表
     * @return Boolean
     */
    Boolean batchStopSell(EstimateBO biz);

    /**
     * 查询该门店指定商品集合的估清信息
     *
     * @param storeGuid   门店guid
     * @param skuGuidList 规格guid
     * @return
     */
    List<EstimateDO> listByStoreGuidAndSkuGuidList(String storeGuid, List<String> skuGuidList);

    /**
     * 查询门店集合下所有未设置长期估清的商品
     *
     * @param storeGuids 门店guids
     * @return
     */
    List<EstimateDO> listByStoreGuidsAndIsForeverList(List<String> storeGuids);

    /**
     * 商品估清定时恢复
     *
     * @param request 企业和门店map
     * @return boolean
     */
    Boolean storeItemEstimateCancel(Map<String, List<String>> request);

    /**
     * 根据商品guid查询估清商品详情
     *
     * @param itemGuid 商品Guid
     * @return 估清商品详情
     */
    EstimateItemRespDTO listEstimateByItem(String itemGuid);
}
