package com.holderzone.saas.store.item.controller;


import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.ItemGroupMealSaveReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemPkgSaveReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeSkuRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.item.entity.enums.SalesModelEnum;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.service.IItemPkgService;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 商品表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@RestController
@RequestMapping("/item_pkg")
@Slf4j
public class ItemPkgController {

    @Autowired
    private IItemPkgService itemPkgService;

    @Autowired
    private ItemHelper itemHelper;

    @Resource
    private OrganizationService organizationService;

    @PostMapping("/select_sku_list")
    public List<TypeSkuRespDTO> selectSkuListForPkg(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("套餐或推送获取规格列表接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return itemPkgService.selectSkuListForPkg(itemSingleDTO);
    }

    @PostMapping("/select_type_sku_list")
    public List<TypeSkuRespDTO> selectTypeSkuListForPkg(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("套餐获取规格列表接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return itemPkgService.selectTypeSkuListForPkg(itemSingleDTO);
    }

    @PostMapping("/save_pkg")
    public Integer savePkg(@RequestBody @Valid ItemPkgSaveReqDTO itemPkgSaveReqDTO) {
        log.info("套餐新增接口入参,itemPkgSaveReqDTO={}", JacksonUtils.writeValueAsString(itemPkgSaveReqDTO));
        List<String> insertSkuGuidList = itemPkgService.saveItemPkg(itemPkgSaveReqDTO);
        if (!CollectionUtils.isEmpty(itemPkgSaveReqDTO.getDataList())) {
            // 直接推送至安卓端或者推送至门店
            Integer push = itemHelper.distributeItems2Stores(itemPkgSaveReqDTO, insertSkuGuidList, false);
            return Integer.valueOf(1).equals(push) ? 1 : 3;
        }
        return 1;
    }

    @PostMapping("/update_pkg")
    public Integer updatePkg(@RequestBody @Valid ItemPkgSaveReqDTO itemPkgSaveReqDTO) {
        log.info("套餐update接口入参,itemPkgSaveReqDTO={}", JacksonUtils.writeValueAsString(itemPkgSaveReqDTO));
        List<String> insertSkuGuidList = itemPkgService.updateItemPkg(itemPkgSaveReqDTO);
        if (!CollectionUtils.isEmpty(itemPkgSaveReqDTO.getDataList())) {
            // 直接推送至安卓端或者推送至门店
            Integer push = itemHelper.distributeItems2Stores(itemPkgSaveReqDTO, insertSkuGuidList, false);
            return Integer.valueOf(1).equals(push) ? 1 : 3;
        } else {
            //门店商品功能下编辑没有品牌，不走这里（update_item、update_pkg接口要看）
            if (!ObjectUtils.isEmpty(itemPkgSaveReqDTO.getBrandGuid())) {
                BrandDTO brandDTO = organizationService.queryBrandByGuid(itemPkgSaveReqDTO.getBrandGuid());
                if (Objects.nonNull(brandDTO) && Objects.nonNull(brandDTO.getSalesModel()) &&
                        Objects.equals(SalesModelEnum.NORMAL_MODE.getCode(), brandDTO.getSalesModel())) {
                    //移除所有门店
                    itemPkgService.removeAllRelationStore(itemPkgSaveReqDTO, insertSkuGuidList);
                }
            }
        }
        return 1;
    }

    /**
     * 保存/更新团餐
     *
     * @param request
     * @return
     */
    @PostMapping("/save_group_meal_pkg")
    public Integer saveOrUpdateGroupMealPkg(@RequestBody @Valid ItemGroupMealSaveReqDTO request) {
        log.info("门店团餐save or update 接口入参，request = {}", JacksonUtils.writeValueAsString(request));
        return itemPkgService.saveOrUpdateGroupMealPkg(request);
    }

    /**
     * 根据商品guid获取商品及套餐信息
     *
     * @param itemStringListDTO 商品guid
     * @return 商品及套餐信息
     */
    @ApiOperation(value = "根据商品guid获取商品及套餐信息")
    @PostMapping("/list_pkg_item_info")
    public List<ItemInfoRespDTO> listPkgItemInfo(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("根据商品guid获取商品及套餐信息，itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        if (CollectionUtils.isEmpty(itemStringListDTO.getDataList())) {
            log.info("根据商品guid获取商品及套餐信息-商品guid列表为空！");
            return new ArrayList<>();
        }
        return itemPkgService.listPkgItemInfo(itemStringListDTO);
    }

    /**
     * 外卖批量绑定查询品牌库商品
     */
    @PostMapping("/select_list")
    public List<TypeSkuRespDTO> selectList(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("[外卖批量绑定查询品牌库商品]入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return itemPkgService.selectList(itemSingleDTO);
    }
}
