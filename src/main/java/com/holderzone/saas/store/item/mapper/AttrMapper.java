package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.item.entity.domain.AttrDO;
import feign.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 属性值（属性组下的属性内容） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Repository
public interface AttrMapper extends BaseMapper<AttrDO> {

    AttrDO selectByGuid(@Param("guid") String guid);
}
