package com.holderzone.saas.store.item.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 商品状态枚举
 * @date 2021/4/6 14:16
 */
@Getter
@AllArgsConstructor
public enum ItemStateEnum {
    // 商品状态 0:上架 1：售完下架 2：立即下架（直接删除） 3：即将下架 4：已下架（售完商品的下架）

    /**
     * 已上架
     */
    ON_RACK(0, "已上架"),

    /**
     * 售完下架
     */
    SOLD_OUT(1, "售完下架"),

    /**
     * 立即下架（直接删除）
     */
    IMMEDIATELY_DELETE(2, "立即下架"),

    /**
     * 即将下架
     */
    IS_ABOUT_TO_DELETE(3, "即将下架"),

    /**
     * 已下架（售完商品的下架）
     */
    IS_SOLD_OUT(4, "已下架"),
    ;

    private final Integer code;

    private final String desc;
}
