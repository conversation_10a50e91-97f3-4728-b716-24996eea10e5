package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.AttrReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrSaveReqDTO;
import com.holderzone.saas.store.dto.item.resp.AttrRespDTO;
import com.holderzone.saas.store.item.entity.domain.AttrDO;
import com.holderzone.saas.store.item.entity.domain.RAttrItemAttrGroupDO;

import java.util.List;

/**
 * <p>
 * 属性值（属性组下的属性内容） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-19
 */
public interface IAttrService extends IService<AttrDO> {

    /**
     * 新增属性值
     *
     * @param attrReqDTO attrReqDTO
     * @return boolean
     */
    boolean saveAttrValue(AttrReqDTO attrReqDTO);

    /**
     * 修改属性值
     *
     * @param attrSaveReqDTO attrSaveReqDTO
     * @return boolean
     */
    boolean update(AttrSaveReqDTO attrSaveReqDTO);

    /**
     * 在删除了被推送属性与推送商品之间的关联关系后，删除未与门店自建商品关联的属性，剩下推送属性改为自建
     * @param pushAttrDOList
     */
    void deleteOrUpdatePushAttrAfterDeletePushRelation(List<AttrDO> pushAttrDOList);

    /**
     * 删除被推送属性与门店被推送商品的关联关系，以及未关联门店自建实体的推送实体的删除
     * @param toDelAttrRelationDOList 将被删除的属性与商品的关联实体
     * @param pushAttrDOList 被推送属性集合
     */
    void deletePushAttrRelate(List<RAttrItemAttrGroupDO> toDelAttrRelationDOList, List<AttrDO> pushAttrDOList);

    /**
     * 删除属性值
     * 同时会删除该属性值与商品类型的绑定关系
     *
     * @param attrGuid 属性值guid
     * @return boolean
     */
    boolean deleteByGuid(String attrGuid);

    /**
     * 获取当前属性组下的所有属性
     *
     * @param itemSingleDTO 属性组guid
     * @return 属性值guid
     */
    List<AttrRespDTO> listAttrByGroup(ItemSingleDTO itemSingleDTO);

    /**
     * 获取属性值集合
     *
     * @param attrGroupGuidList 属性组guid
     * @return 属性值list
     */
    List<AttrRespDTO> listAttrByGroup(List<String> attrGroupGuidList);

    /**
     * 通过属性组删除属性值
     *
     * @param attrGroupGuid 属性组guid
     * @return boolean
     */
    boolean deleteByGroup(String attrGroupGuid);

    Integer removePushAttr(String storeGuid);

    /**
     * 检查属性是否被使用
     *
     * @param attrGuid 属性guid
     * @return Boolean
     */
    Boolean checkAttrUsed(String attrGuid);
}
