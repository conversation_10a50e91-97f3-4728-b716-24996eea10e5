package com.holderzone.saas.store.item.controller;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.req.ItemShoppingCardRequest;
import com.holderzone.saas.store.dto.item.req.ItemShoppingCartCheckRequest;
import com.holderzone.saas.store.dto.item.req.ItemShoppingCartCheckResponse;
import com.holderzone.saas.store.dto.item.req.ItemSkuListReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemShoppingCartResp;
import com.holderzone.saas.store.dto.item.resp.ItemStockResp;
import com.holderzone.saas.store.item.service.IMiniAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/mini_app")
@Api(tags = "小程序专用接口")
@AllArgsConstructor
public class MiniAppController {

    private final IMiniAppService miniAppService;

    /**
     * 小程序端查询购物车列表接口
     *
     * @param itemShoppingCardRequest
     * @return
     */
    @ApiOperation(value = "小程序端查询购物车列表接口，必填参数：storeGuid")
    @PostMapping("/query_shopping_card_for_mini_app")
    public ItemShoppingCartResp queryShoppingCardForMiniApp(@RequestBody ItemShoppingCardRequest itemShoppingCardRequest) {
        log.info("小程序端查询购物车列表接口入参,itemShoppingCardRequest={}", JacksonUtils.writeValueAsString(itemShoppingCardRequest));
        long startTime = System.currentTimeMillis();
        if (StringUtils.isEmpty(itemShoppingCardRequest.getStoreGuid())
                || StringUtils.isEmpty(itemShoppingCardRequest.getEnterpriseGuid())
                || ObjectUtils.isEmpty(itemShoppingCardRequest.getSkuGuids())) {
            throw new ParameterException("门店、企业或sku不能为空");
        }
        ItemShoppingCartResp itemAndTypeForAndroidRespDTO = miniAppService.queryShoppingCardForMiniApp(itemShoppingCardRequest);
        log.warn("程序运行时间： {}", (System.currentTimeMillis() - startTime) + "ms");
        return itemAndTypeForAndroidRespDTO;
    }


    /**
     * 小程序端校验购物车列表接口
     *
     * @param itemShoppingCardRequest
     * @return
     */
    @ApiOperation(value = "小程序端校验购物车列表接口，必填参数：storeGuid")
    @PostMapping("/check_shopping_card_for_mini_app")
    public ItemShoppingCartCheckResponse checkShoppingCardForMiniApp(@RequestBody ItemShoppingCartCheckRequest itemShoppingCardRequest) {
        log.info("小程序端校验购物车列表接口入参,itemShoppingCardRequest={}", JacksonUtils.writeValueAsString(itemShoppingCardRequest));
        long startTime = System.currentTimeMillis();
        if (StringUtils.isEmpty(itemShoppingCardRequest.getStoreGuid())
                || ObjectUtils.isEmpty(itemShoppingCardRequest.getData())) {
            throw new ParameterException("门店或购物车信息不能为空");
        }
        ItemShoppingCartCheckResponse ret = miniAppService.checkShoppingCardForMiniApp(itemShoppingCardRequest);
        log.warn("程序运行时间： {}", (System.currentTimeMillis() - startTime) + "ms");
        return ret;
    }


    /**
     * 减少商城库存
     *
     * @param itemShoppingCardRequest
     * @return
     */
    @PostMapping("/desc_stock")
    @ApiOperation(value = "减少商城库存")
    public ItemStockResp descStock(@RequestBody @Valid ItemShoppingCartCheckRequest itemShoppingCardRequest) {
        log.info("减少商城库存,itemShoppingCardRequest={}", JacksonUtils.writeValueAsString(itemShoppingCardRequest));
        ItemStockResp itemStockResp = miniAppService.descStock(itemShoppingCardRequest);
        log.info("verify result = {}", JacksonUtils.writeValueAsString(itemStockResp));
        return itemStockResp;
    }


}
