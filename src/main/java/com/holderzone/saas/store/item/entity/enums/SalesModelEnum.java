package com.holderzone.saas.store.item.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 销售模式: 1 普通模式 2 菜谱方案
 */
@Getter
@AllArgsConstructor
public enum SalesModelEnum {

    /**
     * 普通模式
     */
    NORMAL_MODE(1, "普通模式"),

    /**
     * 菜谱方案
     */
    RECIPE_MODE(2, "菜谱模式");

    private final int code;

    private final String desc;

    public static String getDesc(int code) {
        for (SalesModelEnum e : SalesModelEnum.values()) {
            if (e.getCode() == code) {
                return e.getDesc();
            }
        }
        return "未知模式";
    }
}
