package com.holderzone.saas.store.item.service;


import com.holderzone.saas.store.dto.item.req.ItemShoppingCardRequest;
import com.holderzone.saas.store.dto.item.req.ItemShoppingCartCheckRequest;
import com.holderzone.saas.store.dto.item.req.ItemShoppingCartCheckResponse;
import com.holderzone.saas.store.dto.item.resp.ItemShoppingCartResp;
import com.holderzone.saas.store.dto.item.resp.ItemStockResp;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

public interface IMiniAppService {

    ItemShoppingCartCheckResponse checkShoppingCardForMiniApp(ItemShoppingCartCheckRequest itemShoppingCartCheckRequest);

    ItemShoppingCartResp queryShoppingCardForMiniApp(ItemShoppingCardRequest itemShoppingCardRequest);

    ItemStockResp descStock(ItemShoppingCartCheckRequest itemShoppingCardRequest);
}
