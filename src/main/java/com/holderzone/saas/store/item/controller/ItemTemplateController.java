package com.holderzone.saas.store.item.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.ItemTemplateMenuDetailsReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemTemplateMenuSubitemReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemTemplateReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemTemplateSearchReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenuDetailRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenusRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplatesRespDTO;
import com.holderzone.saas.store.item.service.IItemTMenuService;
import com.holderzone.saas.store.item.service.IItemTMenuSubitemService;
import com.holderzone.saas.store.item.service.IItemTMenuValidityService;
import com.holderzone.saas.store.item.service.IItemTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;


@Slf4j
@RestController
@RequestMapping("/item_template")
@Api(value = "销售模板接口")
public class ItemTemplateController {


    private final IItemTemplateService iItemTemplateService;

    private IItemTMenuValidityService itemTMenuValidityService;

    private final IItemTMenuService iItemTMenuService;

    private final IItemTMenuSubitemService itemTMenuSubitemService;

    @Autowired
    public ItemTemplateController(IItemTemplateService iItemTemplateService, IItemTMenuService iItemTMenuService, IItemTMenuValidityService itemTMenuValidityService, IItemTMenuSubitemService itemTMenuSubitemService) {
        this.iItemTemplateService = iItemTemplateService;
        this.iItemTMenuService = iItemTMenuService;
        this.itemTMenuValidityService = itemTMenuValidityService;
        this.itemTMenuSubitemService = itemTMenuSubitemService;
    }

    @ApiOperation(value = "保存、更新商品模板", notes = "保存、更新商品模板")
    @PostMapping("/save")
    public Integer saveItemTemplate(@RequestBody @Valid ItemTemplateReqDTO request) {
        log.info("商品模板新增or更新接口入参,request={}", JacksonUtils.writeValueAsString(request));
        return iItemTemplateService.saveOrUpdate(request) ? 1 : 0;
    }


    @ApiOperation(value = "新增菜单", notes = "新增菜单及其菜单子项菜品、执行时间")
    @PostMapping("/save_menu")
    public Integer saveItemMenu(@RequestBody @Valid ItemTemplateMenuSubitemReqDTO request) {
        log.info("新增or更新菜单及其菜单子项菜品接口入参,request={}", JacksonUtils.writeValueAsString(request));
        return iItemTMenuService.saveItemMenu(request) ? 1 : 0;
    }

    @ApiOperation(value = "查询商品模板", notes = "条件查询门店商品模板")
    @PostMapping("/get_store_item_templates")
    public ItemTemplatesRespDTO getStoreItemTemplates(@RequestBody @Valid ItemTemplateSearchReqDTO request) {
        log.info("条件查询门店商品模板接口入参,request={}", JacksonUtils.writeValueAsString(request));
        return iItemTemplateService.getStoreItemTemplates(request);
    }

    @ApiOperation(value = "查询商品模板列表", notes = "查询门店商品模板菜单列表")
    @PostMapping("/get_item_template_menus")
    public List<ItemTemplateMenusRespDTO> getItemTemplateMenus(@RequestBody @Valid SingleDataDTO request) {
        log.info("查询门店商品模板菜单列表接口入参,request={}", JacksonUtils.writeValueAsString(request));
        return iItemTMenuService.getItemTemplateMenus(request);
    }

    @ApiOperation(value = "同步商品模板执行时", notes = "安卓同步商品模板执行时间")
    @PostMapping("/get_item_template_execute_time")
    public List<Long> getTimeAndTypeForSyn(@RequestBody @Valid SingleDataDTO request) {
        log.info("安卓同步商品模板执行时间接口入参,request={}", JacksonUtils.writeValueAsString(request));
        return itemTMenuValidityService.getTimeAndTypeForSyn(request);
    }


    @ApiOperation(value = "查询菜单详情", notes = "查询菜单详情")
    @PostMapping("/get_item_template_menu_detail")
    public ItemTemplateMenuDetailRespDTO getItemTemplateMenuDetail(@RequestBody @Valid ItemTemplateMenuDetailsReqDTO request) {
        log.info("获取商品模板菜单详情接口入参,request={}", JacksonUtils.writeValueAsString(request));
        ItemTemplateMenuDetailRespDTO result = itemTMenuSubitemService.getItemTemplateMenuDetail(request);
        return result;
    }


    @ApiOperation(value = "移除菜单下菜品", notes = "移除菜单下菜品")
    @PostMapping("/batch_remove")
    public Integer menuSubItemBatchRemove(@RequestBody @Valid SingleDataDTO request) {
        log.info("（批量）移除菜单下菜品接口入参,request={}", JacksonUtils.writeValueAsString(request));
        return itemTMenuSubitemService.menuSubItemBatchRemove(request) ? 1 : 0;
    }

}
