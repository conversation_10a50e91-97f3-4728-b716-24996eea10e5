package com.holderzone.saas.store.item.entity.bo;

import com.holderzone.saas.store.item.entity.domain.TypeDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PushItemReqDTO
 * @date 2019/02/13 上午11:02
 * @description //TODO
 * @program holder-saas-store-item
 */
@Data
public class PushTypeReqDTO implements Serializable {

    @ApiModelProperty(value = "菜品分类列表")
    private List<TypeDO> typeDOList;

    @ApiModelProperty(value = "门店guid列表")
    private List<String> storeGuidList;
}
