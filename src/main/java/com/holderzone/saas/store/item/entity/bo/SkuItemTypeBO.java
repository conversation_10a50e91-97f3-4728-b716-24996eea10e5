package com.holderzone.saas.store.item.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SkuItemTypeBO
 * @date 2019/02/28 上午11:20
 * @description //TODO
 * @program holder-saas-store-item
 */
@Data
public class SkuItemTypeBO implements Serializable {

    @ApiModelProperty("skuGuid")
    private String skuGuid;

    @ApiModelProperty("商品类型：1.套餐（不称重，无规格），2.多规格商品（单商品，不称重），3.称重商品（单商品，称重），4.单品。")
    private Integer itemType;

    @ApiModelProperty("规格名称（规格商品才有）")
    private String skuName;

    @ApiModelProperty("商品名称")
    private String itemName;

    @ApiModelProperty("商品拼音")
    private String pinyin;

    @ApiModelProperty("售卖价格")
    private BigDecimal salePrice;

    @ApiModelProperty("商品GUID")
    private String itemGuid;

    @ApiModelProperty("分类GUID")
    private String typeGuid;

    @ApiModelProperty("分类名称")
    private String typeName;

    @ApiModelProperty("规格编号")
    private String code;

    @ApiModelProperty("规格单位")
    private String unit;

    @ApiModelProperty("分类排序")
    private Integer sort;

    @ApiModelProperty("分类排序")
    private Long id;

    @ApiModelProperty("品牌库对应的SKUGUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的商品，则该字段为原skuGUID。")
    private String parentSkuGuid;

    @ApiModelProperty("是否上架(0：否，1：是)")
    private Integer isRack;

    @ApiModelProperty(" 当前销售模式下 是否支持可选(0：否，1：是)")
    private Integer isChoice;

    @ApiModelProperty("品牌库对应的itemGUID：如果是自己创建的内容，则此字段为自己guid，如果是被推送过来的商品，则该字段为原itemGUID。")
    private String parentItemGuid;

    @ApiModelProperty(value = "菜谱售卖名称")
    private String planItemName;

    @ApiModelProperty("外卖核算价")
    private BigDecimal takeawayAccountingPrice;

    @ApiModelProperty("堂食核算价")
    private BigDecimal accountingPrice;

}
