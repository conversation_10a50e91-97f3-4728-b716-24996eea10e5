package com.holderzone.saas.store.item.service.rpc;

import com.netflix.hystrix.exception.HystrixBadRequestException;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RegionService
 * @date 18-9-17 下午4:03
 * @description 服务间调用-base服务
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "base-service", fallbackFactory = BaseService.ServiceFallBack.class)
public interface BaseService {

    @DeleteMapping("/file")
    void delete(@RequestParam("fileUrl") String fileUrl);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<BaseService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public BaseService create(Throwable cause) {
            return new BaseService() {

                @Override
                public void delete(String fileUrl) {
                    log.error("e={}", cause.getMessage());
                    throw new HystrixBadRequestException("调用oss文件删除异常! " + cause.getMessage());
                }
            };
        }
    }
}
