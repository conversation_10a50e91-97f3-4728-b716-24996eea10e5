package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.ItemMenuSubItemReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemTemplateMenuDetailsReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenuDetailRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenuSubItemDetailRespDTO;
import com.holderzone.saas.store.item.entity.domain.ItemTMenuSubitemDO;

import java.util.List;

/**
 * <p>
 * 商品模板-菜单-商品 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
public interface IItemTMenuSubitemService extends IService<ItemTMenuSubitemDO> {

    /**
     * 保存商品模板-菜单-菜品子项
     * @param request
     * @param menuGuid 菜单guid
     * @return
     */
    Boolean saveItemMenuSubItem(List<ItemMenuSubItemReqDTO> request,String menuGuid);

    /**
     * 获取商品模板-菜单详情
     * @param request
     * @return
     */
    ItemTemplateMenuDetailRespDTO getItemTemplateMenuDetail(ItemTemplateMenuDetailsReqDTO request);

    /**
     * （批量）移除菜单下菜品
     * @return
     */
    Boolean menuSubItemBatchRemove(SingleDataDTO request);

    /**
     * 根据菜单guid获取到当前时间生效模板的菜单
     * @param menuGuid
     * @return
     */
    List<ItemTemplateMenuSubItemDetailRespDTO> getNowMeunSubItemForSyn(String menuGuid);

    /**
     * 删除菜单时删除子菜
     * @param menuGuid
     * @return
     */
    Boolean removeMenuSubItem(String menuGuid);
}
