package com.holderzone.saas.store.item.entity.query;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateMenuSubItemDetailQuery
 * @date 2019/06/03 19:21
 * @description //TODO 商品模板菜单菜品详情
 * @program holder-saas-aggregation-app
 */
@Data
public class ItemTemplateMenuSubItemDetailQuery {


    /**
     * 菜单菜品guid
     */
    private String guid;

    /**
     * 菜单guid
     */
    private String menuGuid;

    /**
     * 菜品名称
     */
    private String name;
    /**
     * 菜品sku guid
     */
    private String skuGuid;

    /**
     * 菜品sku名称
     */
    private String skuName;
    /**
     * 菜品单位
     */
    private String unit;

    /**
     * 菜品类型guid
     */
    private String typeGuid;

    /**
     * 菜品类型名称
     */
    private String typeName;
    /**
     * 菜品原价
     */
    private BigDecimal salePrice;
    /**
     * 菜品售价
     */
    private BigDecimal price;
}
