package com.holderzone.saas.store.item.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GuidKeyConstant
 * @date 2019/05/25 14:19
 * @description
 * @program holder-saas-store-item
 */
public class GuidKeyConstant {
    public static final String HSI_ITEM_T_MENU_SUBITEM = "hsi_item_t_menu_subitem";
    public static final String HSI_ITEM_T_MENU_VALIDITY = "hsi_item_t_menu_validity";
    public static final String HSI_GROUP_MEAL = "hsi_group_meal";
    public static final String HSI_ATTR_GROUP = "hsi_attr_group";
    public static final String HSI_ATTR = "hsi_attr";
    public static final String HSI_R_TYPE_ATTR = "hsi_r_type_attr";
    public static final String HSI_R_ITEM_ATTR_GROUP = "hsi_r_item_attr_group";
    public static final String HSI_R_ATTR_ITEM_ATTR_GROUP = "hsi_r_attr_item_attr_group";
    public static final String HSI_ESTIMATE = "hsi_estimate";
    public static final String HSI_ESTIMATE_OP_LOG = "hsi_estimate_op_log";
    public static final String HSI_ESTIMATE_SELL_LOG = "hsi_estimate_sell_log";
    public static final String HSI_ITEM = "hsi_item";
    public static final String HSI_SKU = "hsi_sku";
    public static final String HSI_R_SKU_SUBGROUP = "hsi_r_sku_subgroup";
    public static final String HSI_TYPE = "hsi_type";
    public static final String HSI_ITEM_TEMPLATE = "hsi_item_template";
    public static final String HSI_ITME_T_MENU_VALIDITY = "hsi_itme_t_menu_validity";
    public static final String HSI_PRICEPLAN = "hsi_priceplan";
    public static final String HSI_PRICEPLAN_ITEM = "hsi_priceplan_item";
    public static final String HSI_PRICE_PLAN_PREVIEW_QR_CODE = "hsi_price_plan_preview_qr_code";
    public static final String HSI_PRICEPLAN_STORE = "hsi_priceplan_store";
    public static final String HSI_PRICEPLAN_PUSH_RECORD = "hsi_priceplan_push_record";
    public static final String HSI_PAD_PICTURE = "hsi_pad_picture";

}
