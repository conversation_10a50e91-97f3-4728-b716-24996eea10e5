package com.holderzone.saas.store.item.service.rpc;

import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR> @version 2.0.0
 * @className BusinessMessageService
 * @date 19-1-8 下午4:02
 * @description 服务间调用-消息服务
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-message", fallbackFactory = BusinessMessageService.ServiceFallBack.class)
public interface BusinessMessageService {

    /**
     * 推送消息至安卓端
     *
     * @param businessMessageDTO
     * @return
     */
    @ApiOperation(value = "接受消息的接口", notes = "返回success，表示推送成功")
    @PostMapping("/msg")
    String msg(BusinessMessageDTO businessMessageDTO);

    @ApiOperation(value = "批量接受消息", notes = "返回success，表示推送成功")
    @PostMapping("/all/msg")
    String allMsg(@RequestBody List<BusinessMessageDTO> businessMessageDTOS);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<BusinessMessageService> {

        @Override
        public BusinessMessageService create(Throwable throwable) {
            return new BusinessMessageService() {
                @Override
                public String msg(BusinessMessageDTO businessMessageDTO) {
                    log.error("推送消息至安卓失败" + throwable.getMessage());
                    throw new RuntimeException("推送消息至安卓失败" + throwable.getMessage());
                }

                @Override
                public String allMsg(List<BusinessMessageDTO> businessMessageDTOS) {
                    log.error("批量推送消息至安卓失败" + throwable.getMessage());
                    throw new RuntimeException("批量推送消息至安卓失败" + throwable.getMessage());
                }
            };
        }
    }
}