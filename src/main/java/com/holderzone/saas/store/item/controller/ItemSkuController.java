package com.holderzone.saas.store.item.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.item.common.ItemPadCalculateDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.ItemSkuSearchRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import com.holderzone.saas.store.item.service.IItemService;
import com.holderzone.saas.store.item.service.ISkuService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@RestController
@RequestMapping("/item_sku")
@Slf4j
@AllArgsConstructor
public class ItemSkuController {


    private final ISkuService skuService;
    private final IItemService itemService;


    /**
     * @param skuGuid 根据skuGuid
     * @return SkuInfoRespDTO
     */
    @GetMapping("/info")
    public SkuInfoRespDTO info(@RequestParam String skuGuid) {
        SkuDO skuDO = skuService.getById(skuGuid);
        SkuInfoRespDTO dto = new SkuInfoRespDTO();
        if (skuDO == null) {
            log.warn("查询商品规格为空，skuguid：{}", skuGuid);
            log.warn("查询商品规格为空，userContext:{}, enterpriseGuid:{}", UserContextUtils.getJsonStr(),
                    EnterpriseIdentifier.getEnterpriseGuid());
            return dto;
        }
        BeanUtils.copyProperties(skuDO, dto);
        return dto;
    }

    /**
     * 查询code
     *
     * @param skuGuidList
     * @return
     */
    @PostMapping("/getCode")
    public Map<String, String> getCode(@RequestBody List<String> skuGuidList) {
        if (CollectionUtils.isEmpty(skuGuidList)) {
            return Collections.emptyMap();
        }
        List<SkuDO> skuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>()
                .in(SkuDO::getGuid, skuGuidList)
                .isNotNull(SkuDO::getCode)
        );
        return skuDOList.stream().collect(Collectors.toMap(SkuDO::getGuid, SkuDO::getCode));
    }

    /**
     * 查询门店下全部的sku
     *
     * @param req
     * @return
     */
    @PostMapping("/all")
    public List<SkuInfoRespDTO> all(@RequestBody ItemSkuAllReq req) {
        log.info("查询门店下全部sku入参：{}，企业guid：{}", JSON.toJSONString(req), UserContextUtils.getEnterpriseGuid());
        return skuService.all(req);
    }

    /**
     * 分页查询门店下全部的sku
     *
     * @param req
     * @return
     */
    @PostMapping("/list")
    public Page<SkuInfoRespDTO> list(@RequestBody @Valid ItemSkuListReqDTO req) {
        return skuService.list(req);
    }

    /**
     * sku搜索
     *
     * @param itemSearchReqDTO
     * @return
     */
    @PostMapping("/search")
    public List<ItemSkuSearchRespDTO> search(@RequestBody ItemSkuSearchReqDTO itemSearchReqDTO) {
        return skuService.search(itemSearchReqDTO);
    }

    /**
     * sku 批量修改
     *
     * @param batchUpdateDTO
     * @return
     */
    @PostMapping("/batch_update")
    public Boolean batchUpdate(@RequestBody ItemSkuBatchUpdateDTO batchUpdateDTO) {
        return skuService.batchUpdate(batchUpdateDTO);
    }

    /**
     * sku 批量增加销量
     *
     * @param itemSkuMonthlySaleIncDTO
     * @return
     */
    @PostMapping("/batch_sale_inc")
    public Boolean batchSaleInc(@RequestBody ItemSkuMonthlySaleIncDTO itemSkuMonthlySaleIncDTO) {
        return skuService.batchSaleInc(itemSkuMonthlySaleIncDTO);
    }

    /**
     * 判断当前是否有不可下单的商品
     * 不可下单：商品下架、商品售罄、库存不足
     *
     * @param orderItemReqDTO 需要检查的规格guid
     * @return 不可下单的商品
     */
    @ApiOperation(value = "判断当前是否有不可下单的商品")
    @PostMapping(value = "/check_order_placement_item")
    public List<ItemPadCalculateDTO> checkOrderPlacementItem(@RequestBody OrderItemReqDTO orderItemReqDTO) {
        log.info("判断当前是否有不可下单的商品 orderItemReqDTO={}", JacksonUtils.writeValueAsString(orderItemReqDTO));
        return skuService.checkOrderPlacementItem(orderItemReqDTO);
    }

    /**
     * 根据规格guid查询规格信息
     *
     * @param skuGuidList 规格guid列表
     * @return 规格信息列表
     */
    @ApiOperation(value = "根据规格guid查询规格信息")
    @PostMapping("/list_sku_info")
    public List<SkuInfoRespDTO> listSkuInfo(@RequestBody List<String> skuGuidList) {
        log.info("根据规格guid查询规格信息 入参 skuGuidList={}", skuGuidList);
        return skuService.listSkuInfo(skuGuidList);
    }

    /**
     * 根据规格guid查询没有加入微信点餐的商品
     *
     * @param skuGuidList 规格guid列表
     * @return 没有加入微信点餐的商品
     */
    @ApiOperation(value = "根据规格guid查询没有加入微信点餐的商品")
    @PostMapping("/query_not_join_wechat_item")
    public List<String> queryNotJoinWechatItem(@RequestBody List<String> skuGuidList) {
        log.info("根据规格guid查询没有加入微信点餐的商品 入参 skuGuidList={}", skuGuidList);
        return skuService.queryNotJoinWechatItem(skuGuidList);
    }

    /**
     * 根据规格guid查询对应商品全名
     * 包含菜谱
     *
     * @param skuGuidList 规格guid列表
     * @return 商品全名
     */
    @ApiOperation(value = "根据规格guid查询对应商品全名")
    @PostMapping("/list_sku_for_name")
    public List<ItemWebRespDTO> listSkuForName(@RequestBody List<String> skuGuidList) {
        log.info("根据规格guid查询对应商品全名 入参，skuGuidList={}", skuGuidList);
        return skuService.listSkuForName(skuGuidList);
    }

    /**
     * 根据规格guid查询父子级guid
     *
     * @param skuGuidList 规格guid列表
     * @return 门店库sku
     */
    @ApiOperation(value = "根据规格guid查询父子级guid")
    @PostMapping("/list_sku_guid")
    public List<String> listSkuGuid(@RequestBody List<String> skuGuidList) {
        log.info("根据规格guid查询父子级guid 入参，skuGuidList={}", skuGuidList);
        return skuService.listSkuGuid(skuGuidList);
    }


    /**
     * 通过sku查询父级skuGuid
     * 如果已经是父级，返回本身skuGuid
     */
    @ApiOperation(value = "通过sku查询父级skuGuid")
    @PostMapping("/query_parent_sku_guid_by_sku")
    public Map<String, String> queryParentSkuGuidBySku(@RequestBody ItemStringListDTO query) {
        log.info("[通过sku查询父级skuGuid]入参,query={}", JacksonUtils.writeValueAsString(query));
        return skuService.queryParentSkuGuidBySku(query);
    }
}

