package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.item.req.PricePlanReqDTO;
import com.holderzone.saas.store.dto.item.resp.PricePlanNowDTO;
import com.holderzone.saas.store.dto.item.resp.PricePlanStoreInfoDTO;
import com.holderzone.saas.store.item.entity.domain.PricePlanStoreDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface PricePlanStoreMapper extends BaseMapper<PricePlanStoreDO> {

    /**
     * 查询方案关联的门店guid集合
     *
     * @param planGuid 方案guid
     * @return 门店guid集合
     */
    List<String> getPlanStoreGuidList(@Param("planGuid") String planGuid,
                                      @Param("filterGuidList") List<String> filterGuidList);


    /**
     * 查询方案关联的门店guid集合
     *
     * @param planGuidList 方案guidList
     * @return 门店guid集合
     */
    List<String> getPlanStoreGuidListByPlanGuidList(@Param("planGuidList") List<String> planGuidList);

    /**
     * 删除方案关联门店
     *
     * @param planGuid 方案guid
     */
    void deleteStores(@Param("planGuid") String planGuid);

    /**
     * 查询价格方案门店绑定关系
     *
     * @param reqDTO 请求条件
     * @return 返回参数
     */
    List<PricePlanStoreInfoDTO> findPlanStoreGuid(@Param("reqDTO") PricePlanReqDTO reqDTO);

    /**
     * 查询在生效时间内的价格方案
     *
     * @param nowDateTime   当前时间
     * @param storeGuidList 门店GUID LIST
     * @return 返回参数
     */
    List<PricePlanNowDTO> findPlanNowStoreGuidList(@Param("nowDateTime") LocalDateTime nowDateTime,
                                                   @Param("storeGuidList") List<String> storeGuidList);

    /**
     * 查询在生效时间内的价格方案
     *
     * @param nowDateTime 当前时间
     * @param storeGuid   门店GUID
     * @return 返回参数
     */
    List<PricePlanNowDTO> findPlanNowStoreGuid(@Param("nowDateTime") LocalDateTime nowDateTime, @Param("storeGuid") String storeGuid);

    /**
     * 查询在生效时间内和即将生效的价格方案
     *
     * @param storeGuid 门店GUID
     * @return 返回参数
     */
    List<PricePlanNowDTO> findPlanMemberStoreGuid(@Param("storeGuid") String storeGuid);

    /**
     * 查询当前门店即将生效和已生效的菜谱方案
     *
     * @param storeGuid storeGuid
     * @return List<PricePlanNowDTO>
     */
    List<PricePlanNowDTO> findPlanByStoreGuid(@Param("storeGuid") String storeGuid);

    /**
     * 功能描述：根据方案列表查询绑定门店
     *
     * @param planGuidList 方案guid列表
     * @return java.util.List<com.holderzone.saas.store.dto.item.resp.PricePlanStoreInfoDTO>
     * @date 2021/10/8
     */
    List<PricePlanStoreInfoDTO> getPlanStoreListByPlanGuidList(@Param("planGuidList") List<String> planGuidList);
}
