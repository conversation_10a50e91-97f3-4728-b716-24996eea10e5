package com.holderzone.saas.store.item.entity.enums;

public enum HasAttrEnum {
    //0：无属性; 1:有属性; 2:有必选属性组(如果是套餐，则该字段=0)
    NO_ATTRIBUTE(0, "无属性"),
    HAS_ATTRIBUTE(1, "有属性"),
    HAS_MUST_ATTRIBUTE_GROUP(2, "有必选属性组"),
    ;

    private int code;
    private String desc;

    HasAttrEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (HasAttrEnum c : HasAttrEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
