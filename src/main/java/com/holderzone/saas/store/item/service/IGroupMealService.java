package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.item.req.GroupMealSkuSaveReqDTO;
import com.holderzone.saas.store.item.entity.bo.ItemRelateGroupMealBO;
import com.holderzone.saas.store.item.entity.domain.GroupMealDO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;

import java.util.List;

public interface IGroupMealService extends IService<GroupMealDO> {

    /**
     * 保存or更新团餐子项商品
     * @param skuList
     * @param guid
     * @return
     */
    Boolean saveOrUpdateGroupMeal(List<GroupMealSkuSaveReqDTO> skuList, String guid);

    /**
     * 获取单品和宴会套餐直接的关联及其单品和宴会套餐关联的数量
     * @param skuDOList
     * @return
     */
    List<ItemRelateGroupMealBO> mapItem2RelateGroupMealPakNum(List<SkuDO> skuDOList);
}
