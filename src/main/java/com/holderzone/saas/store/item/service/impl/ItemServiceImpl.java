package com.holderzone.saas.store.item.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.Chinese2PinyinUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.member.merchant.dto.card.ResponseIntegralConversionItem;
import com.holderzone.holder.saas.member.merchant.dto.card.ResponseIntegralConversionItemSku;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.CountItemDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.GoodsExportDTO;
import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.CreateRepertoryReqDTO;
import com.holderzone.saas.store.dto.item.common.*;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.member.request.ConversionIntegralQuery;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.ItemUploadUpdateReq;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.PrintItemReqDto;
import com.holderzone.saas.store.dto.weixin.deal.ItemInfoDTO;
import com.holderzone.saas.store.dto.weixin.deal.MenuInfoAllDTO;
import com.holderzone.saas.store.dto.weixin.resp.ItemImgDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.item.ModuleEntranceEnum;
import com.holderzone.saas.store.enums.item.TagEnum;
import com.holderzone.saas.store.item.config.LocalCacheConfig;
import com.holderzone.saas.store.item.constant.Constant;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.dto.PushItemParam;
import com.holderzone.saas.store.item.dto.SyncItemDTO;
import com.holderzone.saas.store.item.dto.SyncStoreAndItemDTO;
import com.holderzone.saas.store.item.entity.MPPage;
import com.holderzone.saas.store.item.entity.bo.*;
import com.holderzone.saas.store.item.entity.domain.*;
import com.holderzone.saas.store.item.entity.enums.*;
import com.holderzone.saas.store.item.entity.query.JournalingItemsQuery;
import com.holderzone.saas.store.item.feign.MemberMerchantClientService;
import com.holderzone.saas.store.item.helper.EventPushHelper;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.helper.PageAdapter;
import com.holderzone.saas.store.item.mapper.*;
import com.holderzone.saas.store.item.repository.PlanItemRepository;
import com.holderzone.saas.store.item.service.*;
import com.holderzone.saas.store.item.service.rpc.CloudEnterpriseFeignClient;
import com.holderzone.saas.store.item.service.rpc.ErpFeginService;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import com.holderzone.saas.store.item.util.*;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.item.constant.Constant.*;

/**
 * <p>
 * 商品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Service
@Slf4j
public class ItemServiceImpl extends ServiceImpl<ItemMapper, ItemDO> implements IItemService {

    /**
     * 上架状态
     */
    private static final Integer RACK = 1;
    public static final String STORE_GUID_NOT_EMPTY = "门店guid不能为空";
    public static final String ITEM_GUID_CANNOT_EMPTY = "商品guid不能为空";
    public static final String TYPE_GUID = "19980403";
    public static final String TYPE_NAME = "积分商城";
    private final ISkuService skuService;
    private final SkuMapper skuMapper;
    private final IRItemAttrGroupService itemAttrGroupService;
    private final IRAttrItemAttrGroupService attrItemAttrGroupService;
    private final IAttrService attrService;
    private final IAttrGroupService attrGroupService;
    private final ITypeService typeService;
    private final ISubgroupService subgroupService;
    private final IRSkuSubgroupService skuSubgroupService;
    private final EventPushHelper eventPushHelper;
    private final ItemHelper itemHelper;
    private final ItemMapper itemMapper;
    private final IItemTemplateService itemTemplateService;
    private final IItemTMenuSubitemService itemTMenuSubitemService;
    private final ErpFeginService erpFeginService;
    private final RedisTemplate redisTemplate;
    private final OrganizationService organizationService;

    @Resource
    private PlanItemRepository planItemRepository;
    @Resource
    private PricePlanItemMapper planItemMapper;
    @Resource
    private PadPictureMapper padPictureMapper;
    private final IPricePlanService pricePlanService;
    private final IPricePlanStoreService pricePlanStoreService;
    private final EstimateMapper estimateMapper;
    private final IRedissonCacheService redissonCacheService;
    private final RedisUtil redisUtil;

    private final TypeMapper typeMapper;
    private final MemberMerchantClientService merchantClientService;
    private final PricePlanMapper pricePlanMapper;

    @Autowired
    private CloudEnterpriseFeignClient cloudEnterpriseFeignClient;

    @Autowired
    public ItemServiceImpl(@Lazy ISkuService skuService,
                           SkuMapper skuMapper, IRItemAttrGroupService itemAttrGroupService,
                           IRAttrItemAttrGroupService attrItemAttrGroupService,
                           IAttrService attrService,
                           IAttrGroupService attrGroupService,
                           ITypeService typeService,
                           ISubgroupService subgroupService,
                           IRSkuSubgroupService skuSubgroupService,
                           EventPushHelper eventPushHelper,
                           @Lazy ItemHelper itemHelper,
                           ItemMapper itemMapper,
                           @Lazy IItemTemplateService itemTemplateService,
                           @Lazy IItemTMenuSubitemService itemTMenuSubitemService,
                           ErpFeginService erpFeginService,
                           RedisTemplate redisTemplate,
                           OrganizationService organizationService,
                           @Lazy IPricePlanService pricePlanService,
                           @Lazy IPricePlanStoreService pricePlanStoreService, EstimateMapper estimateMapper,
                           IRedissonCacheService redissonCacheService, RedisUtil redisUtil,
                           TypeMapper typeMapper, MemberMerchantClientService merchantClientService, PricePlanMapper pricePlanMapper) {
        this.skuService = skuService;
        this.skuMapper = skuMapper;
        this.itemAttrGroupService = itemAttrGroupService;
        this.attrItemAttrGroupService = attrItemAttrGroupService;
        this.attrService = attrService;
        this.attrGroupService = attrGroupService;
        this.typeService = typeService;
        this.subgroupService = subgroupService;
        this.skuSubgroupService = skuSubgroupService;
        this.eventPushHelper = eventPushHelper;
        this.itemHelper = itemHelper;
        this.itemMapper = itemMapper;
        this.itemTemplateService = itemTemplateService;
        this.itemTMenuSubitemService = itemTMenuSubitemService;
        this.erpFeginService = erpFeginService;
        this.redisTemplate = redisTemplate;
        this.organizationService = organizationService;
        this.pricePlanService = pricePlanService;
        this.pricePlanStoreService = pricePlanStoreService;
        this.estimateMapper = estimateMapper;
        this.redissonCacheService = redissonCacheService;
        this.redisUtil = redisUtil;
        this.typeMapper = typeMapper;
        this.merchantClientService = merchantClientService;
        this.pricePlanMapper = pricePlanMapper;
    }

    @Transactional
    @Override
    public ItemSaveRespDTO saveItem(ItemReqDTO itemReqDTO) {
        fixItemFrom(itemReqDTO);
        // 完善字段
        fixAndFillItemReqDTO(itemReqDTO);
        // 校验入参
        validateItemReqDTO(itemReqDTO, itemReqDTO.getFlag());
        try {
            itemReqDTO.setItemGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
        } catch (IOException e) {
            throw new BusinessException("BatchIdGenerator生成商品guid失败");
        }
        ItemDO itemDO = MapStructUtils.INSTANCE.itemReqDTO2itemDO(itemReqDTO);
        itemDO.setIsDelete(0);
        if (itemReqDTO.getFrom() == ModuleEntranceEnum.STORE.code()) {
            //门店自建菜品，parentGuid设为和itemGuid相同
            itemDO.setParentGuid(itemReqDTO.getItemGuid());
        }

        this.save(itemDO);
        List<String> storeGuidList = new ArrayList<>();

        if (StringUtils.isNotBlank(itemReqDTO.getStoreGuid())) {
            storeGuidList.add(itemReqDTO.getStoreGuid());
        }
        if (!CollectionUtils.isEmpty(itemReqDTO.getDataList())) {
            storeGuidList.addAll(itemReqDTO.getDataList());
        }
        if (!CollectionUtils.isEmpty(storeGuidList)) {
            storeGuidList.stream().distinct().forEach(storeGuid -> {
                ItemUploadUpdateReq req = new ItemUploadUpdateReq();
                req.setStoreGuid(storeGuid);
                req.setIsItemUpload(1);
                organizationService.itemUploadUpdate(req);
            });
        }

        ItemInfoBO itemInfoBO = MapStructUtils.INSTANCE.itemReqDTO2itemInfoBO(itemReqDTO);
        List<String> insertSkuGuidList = skuService.saveOrUpdateAndDeleteSku(itemInfoBO);
        // 保存属性
        itemAttrGroupService.saveOrUpdateAndDeleteAttrGroupAndAttrRelation(Arrays.asList(itemInfoBO));
        //商超开启和关闭库存操作 IsOpenStock  =  1 保存库存
        setStock(itemReqDTO, itemDO);
        ItemSaveRespDTO respDTO = new ItemSaveRespDTO();
        respDTO.setItemGuid(itemDO.getGuid());
        respDTO.setInsertSkuGuidList(insertSkuGuidList);
        return respDTO;
    }


    /**
     * 设置库存
     *
     * @param itemReqDTO 保存商品请求model
     * @param itemDO     商品实体
     */
    private void setStock(ItemReqDTO itemReqDTO, ItemDO itemDO) {
        if (Optional.ofNullable(itemReqDTO.getSkuList().get(0)).map(SkuSaveReqDTO::getIsOpenStock).isPresent()
                && ObjectUtils.nullSafeEquals(itemReqDTO.getSkuList().get(0).getIsOpenStock(), 1)) {
            CreateRepertoryReqDTO repertoryDTO = (CreateRepertoryReqDTO
                    .builder()
                    .invoiceType(6)  //期初入库
                    .inOut(0)
                    .detailList(Arrays.asList(InOutGoodsDTO.builder()
                            .itemType(itemDO.getItemType())
                            .barCode(itemReqDTO.getSkuList().get(0).getUpc())
                            .goodsName(itemReqDTO.getName())
                            .goodsGuid(itemReqDTO.getItemGuid())
                            .goodsCode(itemReqDTO.getSkuList().get(0).getCode())
                            .goodsClassifyGuid(itemReqDTO.getTypeGuid())
                            .goodsClassifyName(typeService.getById(itemReqDTO.getTypeGuid()).getName())
                            .count(itemReqDTO.getSkuList().get(0).getTotalStock())
                            .goodsImage(itemReqDTO.getPictureUrl())
                            .pinyin(itemReqDTO.getPinyin())
                            .safeNum(itemReqDTO.getSkuList().get(0).getSafeStock())
                            .unitName(itemReqDTO.getSkuList().get(0).getUnit())
                            .unitPrice(itemReqDTO.getSkuList().get(0).getSalePrice())
                            .isOpenStock(1)
                            .goodsImage(itemDO.getPictureUrl())
                            .totalAmount(itemReqDTO.getSkuList().get(0).getSalePrice())
                            .build())).build());
            repertoryDTO.setStoreGuid(itemReqDTO.getStoreGuid());
            if (itemReqDTO.getItemFrom() != 3) {
                erpFeginService.saveRepertory(repertoryDTO);
            }
        }
    }


    private void fixItemFrom(ItemReqDTO itemReqDTO) {
        Integer from = itemReqDTO.getFrom();
        if (Integer.valueOf(0).equals(from)) {
            // 门店入口
            String storeGuid = itemReqDTO.getStoreGuid();
            if (StringUtils.isEmpty(storeGuid)) {
                throw new ParameterException(STORE_CANNOT_BE_NULL);
            }
            //小程序推送的商品，不做校验
        } else if (Integer.valueOf(3).equals(from)) {

        } else {
            // 品牌入口
            String brandGuid = itemReqDTO.getBrandGuid();
            if (StringUtils.isEmpty(brandGuid)) {
                throw new ParameterException(BRAND_CANNOT_BE_NULL);
            }
        }
        itemReqDTO.setItemFrom(itemReqDTO.getFrom());
    }

    @Transactional
    @Override
    public List<String> updateItem(ItemReqDTO itemReqDTO) {
        ItemDO oldItem = itemMapper.selectOne(new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getGuid, itemReqDTO.getItemGuid()));
        if (ObjectUtils.isEmpty(oldItem)) {
            throw new BusinessException("商品已被删除");
        }
        // todo sku以及属性都要加悲观锁，快餐也是
        // 确定如果商品的规格被删除，而该规格关联了套餐，那么将套餐对应关联规格的子商品删除，如果套餐因此无子商品，则删除对应套餐
        fixAndFillItemReqDTO(itemReqDTO);
        validateItemReqDTO(itemReqDTO, itemReqDTO.getFlag());
        ItemInfoBO itemInfoBO = MapStructUtils.INSTANCE.itemReqDTO2itemInfoBO(itemReqDTO);
        //判断是否修改名称
//        boolean isUpdateName = !oldItem.getName().equals(itemReqDTO.getName());

        // 如果品牌是菜谱模式则不进入校验
        if (Objects.nonNull(itemReqDTO.getBrandGuid())) {
            BrandDTO brandDTO = organizationService.queryBrandByGuid(itemReqDTO.getBrandGuid());
            if (brandDTO.getSalesModel() == SalesModelEnum.NORMAL_MODE.getCode()) {
                //检查推送的门店是否覆盖所在套餐的菜品
                checkStoreItem(itemReqDTO);
            }
        }
        //针对商超获取DB库存状态作对比
        SkuDO dbSkuDO = skuService.getById(itemReqDTO.getSkuList().get(0).getSkuGuid());
        // 新增或更新了的规格GUID集合
        List<String> saveOrUpdateSkuGuidList = skuService.saveOrUpdateAndDeleteSku(itemInfoBO);
        // 保存属性
        boolean success = itemAttrGroupService.saveOrUpdateAndDeleteAttrGroupAndAttrRelation(Arrays.asList(itemInfoBO));
        if (!success) {
            log.error(SYSTEM_ERROR);
            throw new BusinessException(SYSTEM_ERROR);
        }
        // 如果存在套餐
        if (itemInfoBO.getItemType() == 1) {
            // 新增，更新，或删除套餐下的分组
            boolean save = subgroupService.saveOrUpdateAndDeleteSubgroup(Arrays.asList(itemInfoBO));
            if (!save) {
                log.error(SYSTEM_ERROR);
                throw new BusinessException(SYSTEM_ERROR);
            }
        }
        ItemDO itemDO = MapStructUtils.INSTANCE.itemReqDTO2itemDO(itemReqDTO);
        boolean update = updateById(itemDO);
        if (!update) {
            throw new BusinessException(OP_FAIL);
        }
        // 删除图片
        if (StringUtils.isEmpty(itemReqDTO.getPictureUrl())) {
            itemHelper.deletePicUrl(itemReqDTO.getItemGuid());
        }
        //商超专用字段 itemReqDTO.getFlag() = 1 商超编辑
        if (Optional.ofNullable(itemReqDTO.getSkuList().get(0)).map(SkuSaveReqDTO::getIsOpenStock).isPresent() && Objects.equals(itemReqDTO.getFlag(), 1)) {
            //商超开启和关闭库存操作 IsOpenStock   0->1:创建  1->0:修改
            int currentIsOpenStock = itemReqDTO.getSkuList().get(0).getIsOpenStock();
            if (dbSkuDO.getIsOpenStock() < currentIsOpenStock) {  //创建库存
                CreateRepertoryReqDTO updateReperory = CreateRepertoryReqDTO
                        .builder()
                        .invoiceType(6)  //期初入库
                        .inOut(0)
                        .detailList(Arrays.asList(InOutGoodsDTO.builder()
                                .itemType(itemDO.getItemType())
                                .barCode(itemReqDTO.getSkuList().get(0).getUpc())
                                .goodsName(itemReqDTO.getName())
                                .goodsGuid(itemReqDTO.getItemGuid())
                                .goodsCode(itemReqDTO.getSkuList().get(0).getCode())
                                .goodsClassifyGuid(itemReqDTO.getTypeGuid())
                                .goodsClassifyName(typeService.getById(itemReqDTO.getTypeGuid()).getName())
                                .goodsImage(itemReqDTO.getPictureUrl())
                                .unitPrice(itemReqDTO.getSkuList().get(0).getSalePrice())
                                .pinyin(itemReqDTO.getPinyin())
                                .goodsImage(itemDO.getPictureUrl())
                                .count(BigDecimal.ZERO)
                                .unitName(itemReqDTO.getSkuList().get(0).getUnit())
                                .safeNum(itemReqDTO.getSkuList().get(0).getSafeStock())
                                .isOpenStock(currentIsOpenStock).build())).build();
                updateReperory.setStoreGuid(itemReqDTO.getStoreGuid());
                erpFeginService.saveRepertory(updateReperory);
            } else { //修改
                erpFeginService.modifyGoodsInfo(InOutGoodsDTO.builder()
                        .itemType(itemDO.getItemType())
                        .barCode(itemReqDTO.getSkuList().get(0).getUpc())
                        .goodsName(itemReqDTO.getName())
                        .goodsGuid(itemReqDTO.getItemGuid())
                        .goodsCode(itemReqDTO.getSkuList().get(0).getCode())
                        .goodsClassifyGuid(itemReqDTO.getTypeGuid())
                        .goodsClassifyName(typeService.getById(itemReqDTO.getTypeGuid()).getName())
                        .goodsImage(itemReqDTO.getPictureUrl())
                        .unitPrice(itemReqDTO.getSkuList().get(0).getSalePrice())
                        .pinyin(itemReqDTO.getPinyin())
                        .goodsImage(itemDO.getPictureUrl())
                        .unitName(itemReqDTO.getSkuList().get(0).getUnit())
                        .safeNum(itemReqDTO.getSkuList().get(0).getSafeStock())
                        .isOpenStock(currentIsOpenStock).build()
                );
            }
        }
        return saveOrUpdateSkuGuidList;
    }

    /**
     * 校验门店下是否存在套餐
     *
     * @param itemReqDTO 商品请求dto
     */
    private void checkStoreItem(ItemReqDTO itemReqDTO) {
        if (StringUtils.isNotBlank(itemReqDTO.getItemGuid()) && itemReqDTO.getFrom() == ModuleEntranceEnum.BRAND.code()) {
            List<String> packageStoreGuidList = itemMapper.selectStoreGuidListByPackageChildItemGuid(itemReqDTO.getItemGuid());
            if (CollectionUtils.isEmpty(itemReqDTO.getDataList()) || !itemReqDTO.getDataList().containsAll(packageStoreGuidList)) {
                packageStoreGuidList.removeAll(itemReqDTO.getDataList());
                if (!CollectionUtils.isEmpty(packageStoreGuidList)) {
                    List<String> packageItemNameList = itemMapper.selectPackageNameListByPackageChildItemGuid(itemReqDTO.getItemGuid(), packageStoreGuidList);
                    //该门店下存在套餐
                    if (!CollectionUtils.isEmpty(packageItemNameList)) {
                        List<String> storeNameList = organizationService.queryStoreByIdList(packageStoreGuidList)
                                .stream().map(StoreDTO::getName).collect(Collectors.toList());
                        throw new BusinessException("由于存在于套餐:" + StringUtils.join(packageItemNameList, "、") + "，以下门店无法解除分配：" + StringUtils.join(storeNameList, "、"));
                    }
                }
            }
        }
    }


    @Transactional
    @Override
    public Integer editPadPicture(PadPictureDTO padPictureDTO) {
        String planGuid = padPictureDTO.getPlanGuid();

        if (StringUtils.isEmpty(padPictureDTO.getItemGuid()) || StringUtils.isEmpty(padPictureDTO.getStoreGuid())) {
            throw new BusinessException(WRONG_PARAMS);
        }
        // 根据菜品、门店guid查询，如果是菜谱模式用菜品加方案
        LambdaQueryWrapper<PadPictureDO> queryWrapper = new LambdaQueryWrapper<PadPictureDO>()
                .eq(PadPictureDO::getItemGuid, padPictureDTO.getItemGuid());
        if (StringUtils.isNotEmpty(planGuid)) {
            queryWrapper.eq(PadPictureDO::getPlanGuid, planGuid);
        } else {
            queryWrapper.eq(PadPictureDO::getStoreGuid, padPictureDTO.getStoreGuid());
        }
        PadPictureDO padPictureDO = padPictureMapper.selectOne(queryWrapper);
        //为空的话新增
        if (null == padPictureDO) {
            padPictureDO = new PadPictureDO().
                    setIsDelete(Constant.FALSE).
                    setGuid(GUIDUtils.generateGuid(GuidKeyConstant.HSI_PAD_PICTURE)).
                    setItemGuid(padPictureDTO.getItemGuid()).
                    setPlanGuid(planGuid).
                    setStoreGuid(padPictureDTO.getStoreGuid()).
                    setSmallPicture(padPictureDTO.getSmallPicture()).
                    setBigPicture(padPictureDTO.getBigPicture()).
                    setVerticalPicture(padPictureDTO.getVerticalPicture()).
                    setDetailPicture(padPictureDTO.getDetailPicture());
            return padPictureMapper.insert(padPictureDO);
        }
        padPictureDO.setSmallPicture(padPictureDTO.getSmallPicture()).
                setBigPicture(padPictureDTO.getBigPicture()).
                setVerticalPicture(padPictureDTO.getVerticalPicture()).
                setDetailPicture(padPictureDTO.getDetailPicture());
        return padPictureMapper.updateById(padPictureDO);
    }


    @Override
    @Transactional
    public Integer batchDelete(ItemStringListDTO itemStringListDTO, Boolean isCheckIntegral) {
        itemHelper.validateFrom(itemStringListDTO.getFrom());
        // 待删除商品的GUID集合
        List<String> toDeleteItemGuidList = new ArrayList<>(itemStringListDTO.getDataList());

        if (isCheckIntegral) {
            // 如果有商品已设置为积分兑换商品，则不能直接删除
            ConversionIntegralQuery query = new ConversionIntegralQuery();
            query.setItemGuidList(itemStringListDTO.getDataList());
            MemberResult<List<ResponseIntegralConversionItem>> conversionItemList = merchantClientService
                    .queryConversionItemByItemGuid(query);
            if (!CollectionUtils.isEmpty(conversionItemList.getTData())) {
                log.warn("如果有商品已设置为积分兑换商品，则不能直接删除");
                return 5;
            }
        }

        // 检查商品是否被价格方案使用，如果有不允许删除
        List<PricePlanItemDO> pricePlanItemList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                .in(PricePlanItemDO::getItemGuid, toDeleteItemGuidList)
                .eq(PricePlanItemDO::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (!CollectionUtils.isEmpty(pricePlanItemList)) {
            List<String> planGuidList = pricePlanItemList.stream()
                    .map(PricePlanItemDO::getPlanGuid)
                    .distinct()
                    .collect(Collectors.toList());
            List<String> message = new ArrayList<>();
            List<PricePlanDO> pricePlanDOList = pricePlanMapper.listByPlanGuidList(planGuidList);
            if (CollectionUtils.isEmpty(pricePlanDOList)) {
                log.error("未查询到价格方案，价格方案guid：{}", planGuidList);
                throw new BusinessException(NOT_FOUND_PRICE_PLAN_INFO);
            }
            //需要判断此方案的状态
            List<PricePlanDO> collect = pricePlanDOList.stream().filter(planDO ->
//                    planDO.getIsDelete() == 0
                            ObjectUtil.notEqual(planDO.getStatus(), PricePlanStatusEnum.LASTING_DISABLE.getCode())
//                            && !(ObjectUtil.equal(planDO.getStatus(), PricePlanStatusEnum.USING.getCode())
//                            && (planDO.getStoreNum() == null || planDO.getStoreNum() == 0))
            ).collect(Collectors.toList());
            log.info("删除商品，存在的方案：{}", collect);
            if (CollectionUtil.isNotEmpty(collect)) {
                List<String> planNameList = collect.stream()
                        .map(PricePlanDO::getName)
                        .distinct()
                        .collect(Collectors.toList());
                planNameList.forEach(item -> message.add(String.format(" “%s” ", item)));
                //组装提示信息
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("商品存在以下菜谱方案:\n");
                message.forEach(m -> stringBuilder.append(m).append("\n"));
                stringBuilder.append("，请手动清除");
                throw new BusinessException(stringBuilder.toString());
            }

        }
        // 将被删除的商品集合
        List<ItemDO> deleteItemDOList = new ArrayList<>(listByIds(toDeleteItemGuidList));
        if (CollectionUtils.isEmpty(deleteItemDOList)) {
            return 0;
        }
        // 选中商品中被推送商品的GUID集合
        List<String> pushItemGuidList = deleteItemDOList.stream()
                .filter(itemDO -> Integer.valueOf(2).equals(itemDO.getItemFrom()))
                .map(ItemDO::getGuid)
                .collect(Collectors.toList());
        // 被推送商品没有删除功能，只能由品牌库对应商品操作
        if (!CollectionUtils.isEmpty(pushItemGuidList)) {
            toDeleteItemGuidList.removeAll(pushItemGuidList);
            deleteItemDOList.removeIf(itemDO -> pushItemGuidList.contains(itemDO.getGuid()));
        }
        if (CollectionUtils.isEmpty(deleteItemDOList)) {
            throw new BusinessException("请到品牌库进行删除商品");
        }
        // 请求来源于商品库，则将涉及的门店被推送商品一起删除
        if (itemStringListDTO.getFrom() == 1) {
            // 被该商品推送至门店的子商品
            List<ItemDO> sonItemList = list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getParentGuid, itemStringListDTO.getDataList()));
            // 若被推送商品列表不为空，则同步删除被推送商品
            if (!CollectionUtils.isEmpty(sonItemList)) {
                List<String> sonItemGuidList = sonItemList.stream().map(ItemDO::getGuid).collect(Collectors.toList());
                toDeleteItemGuidList.addAll(sonItemGuidList);
                deleteItemDOList.addAll(sonItemList);
            }
        }

        // 是否被成功删除
        boolean remove;
        if (CollectionUtils.isEmpty(toDeleteItemGuidList)) {
            log.error(SYSTEM_ERROR);
            throw new BusinessException(SYSTEM_ERROR);
        }

        //获取到模板引用的菜品
        List<String> templateGuidList = itemMapper.getItemTemplateGuid(toDeleteItemGuidList);
        if (Optional.ofNullable(templateGuidList).isPresent() && !templateGuidList.isEmpty()) {
            itemTMenuSubitemService.menuSubItemBatchRemove(SingleDataDTO.builder()
                    .datas(templateGuidList).build());
        }
        remove = removeByIds(toDeleteItemGuidList);
        // 删除商品下关联的规格及规格关联其他实体
        Integer deleteSkuByItemGuidList = skuService.deleteSkuByItemGuidList(toDeleteItemGuidList);
        if (Integer.valueOf(0).equals(deleteSkuByItemGuidList)) {
            log.error(SYSTEM_ERROR);
            throw new BusinessException(SYSTEM_ERROR);
        }

        // 根据GUID构造ItemInfoBO
        List<ItemInfoBO> itemInfoBOList = getItemInfoBOListByItemGuidList(toDeleteItemGuidList);
        // 删除商品关联的属性
        boolean saveOrUpdateAndDeleteAttrGroupAndAttrRelation = itemAttrGroupService.saveOrUpdateAndDeleteAttrGroupAndAttrRelation(itemInfoBOList);
        // 删除商品分组
        boolean saveOrUpdateAndDeleteSubgroup = subgroupService.saveOrUpdateAndDeleteSubgroup(itemInfoBOList);
        if (!saveOrUpdateAndDeleteAttrGroupAndAttrRelation || !saveOrUpdateAndDeleteSubgroup) {
            log.error(SYSTEM_ERROR);
            throw new BusinessException(SYSTEM_ERROR);
        }
        return remove ? 1 : 0;

    }


    /**
     * 根据商品GUID构造ItemInfoBO实体
     *
     * @param toDeleteItemGuidList
     * @return
     */
    private List<ItemInfoBO> getItemInfoBOListByItemGuidList(List<String> toDeleteItemGuidList) {
        List<ItemInfoBO> itemInfoBOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(toDeleteItemGuidList)) {
            return itemInfoBOList;
        }
        toDeleteItemGuidList.forEach(guid -> {
            ItemInfoBO itemInfoBO = new ItemInfoBO();
            itemInfoBO.setItemGuid(guid);
            itemInfoBOList.add(itemInfoBO);
        });
        return itemInfoBOList;
    }


    @Override
    public boolean countTypeOrItem(String brandGuid) {
        // 该品牌下关联的分类数
        int countType = typeService.count(new LambdaQueryWrapper<TypeDO>().eq(TypeDO::getBrandGuid, brandGuid));
        if (countType > 0) {
            return true;
        }
        // 该品牌下关联的商品数
        int countItem = count(new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getBrandGuid, brandGuid));
        return countItem > 0;
    }

    /**
     * 校验商品附带的属性组以及属性结构
     *
     * @param itemAttrGroupReqDTOList
     */
    private void validateItemAttrGroupAndAttr(List<ItemAttrGroupReqDTO> itemAttrGroupReqDTOList) {
        itemAttrGroupReqDTOList.forEach(itemAttrGroupReqDTO -> {
            List<ItemAttrReqDTO> itemAttrReqDTOList = itemAttrGroupReqDTO.getAttrList();
            boolean hasDefault = itemAttrReqDTOList.stream().anyMatch(itemAttrReqDTO -> Integer.valueOf(1).equals(itemAttrReqDTO.getIsDefault()));
            itemAttrGroupReqDTO.setWithDefault(hasDefault ? 1 : 0);
            if (Integer.valueOf(1).equals(itemAttrGroupReqDTO.getIsMultiChoice()) && itemAttrReqDTOList.size() == 1) {
                throw new ParameterException("可多选属性组下属性数量必须大于1");
            }
        });
    }

    /**
     * 填充及修改字段
     *
     * @param itemReqDTO
     */
    private void fixAndFillItemReqDTO(ItemReqDTO itemReqDTO) {
        List<SkuSaveReqDTO> skuList = itemReqDTO.getSkuList();
        if (itemReqDTO.getItemType() == 2) {
            // 若是非称重的单规格商品。则类型设置为单品
            if (skuList.size() == 1) {
                itemReqDTO.setItemType(4);
                skuList.get(0).setName(EMPTY);
            }
        }
        skuList.forEach(skuSaveReqDTO -> {
            skuSaveReqDTO.setItemGuid(itemReqDTO.getItemGuid());
            skuSaveReqDTO.setStoreGuid(itemReqDTO.getStoreGuid());
            skuSaveReqDTO.setBrandGuid(itemReqDTO.getBrandGuid());
            skuSaveReqDTO.setSkuFrom(itemReqDTO.getItemFrom());
        });
        if (itemReqDTO.getSort() == null) {
            int sort = itemHelper.maxSort(itemReqDTO.getFrom(), itemReqDTO.getBrandGuid(), itemReqDTO.getStoreGuid());
            itemReqDTO.setSort(sort);
        }
        List<ItemAttrGroupReqDTO> itemAttrGroupReqDTOList = itemReqDTO.getAttrGroupList();
        itemReqDTO.setHasAttr(0);
        if (!CollectionUtils.isEmpty(itemAttrGroupReqDTOList)) {
            itemAttrGroupReqDTOList.forEach(itemAttrGroupReqDTO -> itemAttrGroupReqDTO.setItemGuid(itemReqDTO.getItemGuid()));
            boolean hasRequiredAttrGroup = itemAttrGroupReqDTOList.stream().anyMatch(attrGroup -> attrGroup.getIsRequired() == 1);
            itemReqDTO.setHasAttr(hasRequiredAttrGroup ? 2 : 1);
            validateItemAttrGroupAndAttr(itemAttrGroupReqDTOList);
        }
    }

    @Transactional
    @Override
    public List<String> updateItemSku(ItemReqDTO itemReqDTO) {
        // 确定如果商品的规格被删除，而该规格关联了套餐，那么将套餐对应关联规格的子商品删除，如果套餐因此无子商品，则删除对应套餐
        fixAndFillItemReqDTO(itemReqDTO);
        validateItemReqDTO(itemReqDTO, itemReqDTO.getFlag());
        ItemInfoBO itemInfoBO = MapStructUtils.INSTANCE.itemReqDTO2itemInfoBO(itemReqDTO);
        // 新增或更新了的规格GUID集合
        List<String> saveOrUpdateSkuGuidList = skuService.updateSkuForOpen(itemInfoBO);
        ItemDO itemDO = MapStructUtils.INSTANCE.itemReqDTO2itemDO(itemReqDTO);
        boolean update = updateById(itemDO);
        if (!update) {
            throw new BusinessException(OP_FAIL);
        }
        // 删除图片
        if (StringUtils.isEmpty(itemReqDTO.getPictureUrl())) {
            itemHelper.deletePicUrl(itemReqDTO.getItemGuid());
        }
        return saveOrUpdateSkuGuidList;
    }


    @Transactional
    @Override
    public List<String> deleteItemSku(ItemReqDTO itemReqDTO) {
        // todo sku以及属性都要加悲观锁，快餐也是
        // 确定如果商品的规格被删除，而该规格关联了套餐，那么将套餐对应关联规格的子商品删除，如果套餐因此无子商品，则删除对应套餐
        fixAndFillItemReqDTO(itemReqDTO);
        validateItemReqDTO(itemReqDTO, itemReqDTO.getFlag());
        ItemInfoBO itemInfoBO = MapStructUtils.INSTANCE.itemReqDTO2itemInfoBO(itemReqDTO);
        // 新增或更新了的规格GUID集合
        List<String> saveOrUpdateSkuGuidList = skuService.deleteSkuForOpen(itemInfoBO);
        ItemDO itemDO = MapStructUtils.INSTANCE.itemReqDTO2itemDO(itemReqDTO);
        boolean update = updateById(itemDO);
        if (!update) {
            throw new BusinessException(OP_FAIL);
        }
        // 删除图片
        if (StringUtils.isEmpty(itemReqDTO.getPictureUrl())) {
            itemHelper.deletePicUrl(itemReqDTO.getItemGuid());
        }
        return saveOrUpdateSkuGuidList;
    }

    /**
     * 校验商品请求入参
     *
     * @param itemReqDTO
     * @param flag       1:商超版验证
     */
    private void validateItemReqDTO(ItemReqDTO itemReqDTO, Integer flag) {
        // 校验编辑商品时商品的参数
        if (!StringUtils.isEmpty(itemReqDTO.getItemGuid()) && (itemReqDTO.getItemFrom() == null || itemReqDTO.getHasAttr() == null)) {
            throw new ParameterException(WRONG_PARAMS);
        }
        //对小程序推送过来的商品不做以下校验
        if (Integer.valueOf(3).equals(itemReqDTO.getItemFrom())) {
            return;
        }
        // 校验门店或品牌必填
        itemHelper.validateOrganizeGuid(itemReqDTO.getFrom(), itemReqDTO.getBrandGuid(), itemReqDTO.getStoreGuid());
        // 对推送商品不做以下校验
        if (Integer.valueOf(2).equals(itemReqDTO.getItemFrom())) {
            return;
        }
        int count;
        if (itemReqDTO.getFrom() == 0) {
            // 门店入口
            count = count(new LambdaQueryWrapper<ItemDO>()
                    .eq(ItemDO::getName, itemReqDTO.getName())
                    .eq(ItemDO::getStoreGuid, itemReqDTO.getStoreGuid())
                    .ne(!StringUtils.isEmpty(itemReqDTO.getItemGuid()), ItemDO::getGuid, itemReqDTO.getItemGuid()));
        } else {
            // 品牌入口
            count = count(new LambdaQueryWrapper<ItemDO>()
                    .eq(ItemDO::getName, itemReqDTO.getName())
                    .eq(ItemDO::getBrandGuid, itemReqDTO.getBrandGuid())
                    .isNull(ItemDO::getStoreGuid)
                    .ne(!StringUtils.isEmpty(itemReqDTO.getItemGuid()), ItemDO::getGuid, itemReqDTO.getItemGuid()));
        }

        if (count > 0) {
            throw new ParameterException(DUPLICATE_ITEM_NAME);
        }
        List<SkuSaveReqDTO> skuList = itemReqDTO.getSkuList();
        validateSkuNameRepeat(skuList);
        validateSkuCodeLengthRepeat(skuList);
        // 校验SKU编码是否在品牌或门店内重复
        skuService.validateSkuCodeRepeat(skuList, itemReqDTO.getBrandGuid(), itemReqDTO.getStoreGuid(), itemReqDTO.getFrom(), flag);
        // todo 这次迭代屏蔽商品条码功能
        if (Objects.nonNull(flag) && flag.equals(1)) {
            validateUpc(skuList);
        }
        List<ItemAttrGroupReqDTO> attrGroupList = itemReqDTO.getAttrGroupList();
        validateItemAttrGroup(attrGroupList);
    }

    /**
     * 校验upc
     *
     * @param skuList
     */
    private void validateUpc(List<SkuSaveReqDTO> skuList) {
        // 商品条码集合
        List<String> upcList = skuList.stream()
                .filter(skuSaveReqDTO -> !StringUtils.isEmpty(skuSaveReqDTO.getUpc()))
                .map(SkuSaveReqDTO::getUpc)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(upcList)) {
            Set<String> upcSet = new HashSet<>(upcList);
            if (upcSet.size() != upcList.size()) {
                throw new ParameterException(DUPLICATE_UPC);
            }
            // 查询企业下有upc的规格集合
            List<SkuDO> skuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>().isNotNull(SkuDO::getUpc).ne(SkuDO::getUpc, EMPTY));
            skuDOList.removeIf(skuDO -> !upcList.contains(skuDO.getUpc()));
            if (!CollectionUtils.isEmpty(skuDOList)) {
                skuList.forEach(skuSaveReqDTO -> {
                    String upc = skuSaveReqDTO.getUpc();
                    if (!StringUtils.isEmpty(upc)) {
                        // 与当前商品规格条码重复的规格GUID集合
                        List<String> sameUpcSkuGuidList = skuDOList.stream()
                                .filter(skuDO -> upc.equals(skuDO.getUpc()))
                                .map(SkuDO::getGuid)
                                .collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(sameUpcSkuGuidList)
                                && !sameUpcSkuGuidList.contains(skuSaveReqDTO.getSkuGuid())) {
                            throw new ParameterException(DUPLICATE_UPC);
                        }
                    }
                });
            }
        }
    }

    private void validateItemAttrGroup(List<ItemAttrGroupReqDTO> attrGroupList) {
        if (!CollectionUtils.isEmpty(attrGroupList)) {
            attrGroupList.forEach(attrGroup -> {
                if (attrGroup.getIsMultiChoice() == 0) {
                    List<ItemAttrReqDTO> attrList = attrGroup.getAttrList();
                    long defaultAttrNum = attrList.stream().filter(attr -> attr.getIsDefault() == 1).count();
                    if (defaultAttrNum > 1) {
                        throw new ParameterException("单选属性组配置的属性默认项数量不能大于1");
                    }
                }
            });
        }
    }

    /**
     * 校验规格名称是否重复
     *
     * @param skuList
     */
    private void validateSkuNameRepeat(List<SkuSaveReqDTO> skuList) {
        List<String> skuNameList = skuList.stream().map(SkuSaveReqDTO::getName).collect(Collectors.toList());
        if (skuNameList.size() > 1) {
            boolean emptySkuName = skuNameList.stream().anyMatch(StringUtils::isEmpty);
            if (emptySkuName) {
                throw new ParameterException("多规格商品的规格名称必填");
            }
        }
        Set<String> skuNameSet = new HashSet<>(skuNameList);
        if (skuNameList.size() > skuNameSet.size()) {
            throw new ParameterException("规格名称在同一商品下唯一");
        }
    }

    /**
     * 校验规格名称是否重复
     *
     * @param skuList
     */
    private void validateSkuCodeLengthRepeat(List<SkuSaveReqDTO> skuList) {
        List<String> skuCodeList = skuList
                .stream()
                .map(SkuSaveReqDTO::getCode)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        if ((!ObjectUtils.isEmpty(skuCodeList)) && skuCodeList.size() > 0) {
            boolean emptySkuCode = skuCodeList.stream().anyMatch(code -> (code.length() < 1 || code.length() > 16));
            if (emptySkuCode) {
                throw new ParameterException("多规格商品的编号长度位1-16位字符");
            }
        }
    }

    /**
     * 获取门店菜品或门店菜谱菜品
     *
     * @param req req
     * @return ItemAndTypeForAndroidRespDTO
     */
    @Override
    @Cacheable(cacheNames = {LocalCacheConfig.CacheExpires.SECOND_10}, unless = "#result == null || #result.itemList.size() == 0")
    public ItemAndTypeForAndroidRespDTO selectItemDetailAndTypeForSyn(ItemQueryListReq req) {

        String source = UserContextUtils.getSource();

        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(req.getStoreGuid());
        log.info("菜品模式数据：{}", JacksonUtils.writeValueAsString(brandDTO));
        if (!ObjectUtils.isEmpty(brandDTO.getSalesModel()) && brandDTO.getSalesModel() == SalesModelEnum.RECIPE_MODE.getCode()) {
            // 查询生效的菜谱方案
            Pair<Boolean, ItemAndTypeForAndroidRespDTO> p = pricePlanItem(req);
            // false 表示没有生效的菜谱
            if (p.getFirst()) {
                ItemAndTypeForAndroidRespDTO respDTO = p.getSecond();
                log.info("查询生效的菜谱方案:{}", respDTO.getPricePlanGuid());

                // 设置积分兑换商品
                setIntegralConversionItem(req.getOperSubjectGuid(), req.getStoreGuid(), respDTO);
                //设置估清商品的数量
                setEstimateItem(respDTO.getItemList(), req.getStoreGuid());
                return respDTO;
            }
        }

        // 判断是否启用宴会套餐
        TypeDO typeDO = typeService.getOne(new LambdaQueryWrapper<TypeDO>()
                .eq(TypeDO::getStoreGuid, req.getStoreGuid())
                .eq(TypeDO::getName, "宴会套餐")
                .eq(TypeDO::getIsEnable, 0)
        );
        ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = new ItemAndTypeForAndroidRespDTO();
        itemAndTypeForAndroidRespDTO.setSalesModel(1);
        // 返回的商品列表
        List<ItemSynRespDTO> appItemList = new ArrayList<>();
        // 返回的分类列表
        List<TypeSynRespDTO> typeList = new ArrayList<>();

        itemAndTypeForAndroidRespDTO.setItemList(appItemList);
        itemAndTypeForAndroidRespDTO.setTypeList(typeList);

        // 商品列表
        LambdaQueryWrapper<ItemDO> wrapper = new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getStoreGuid, req.getStoreGuid())
                .eq(ItemDO::getIsEnable, true)
                .eq(StringUtils.isNotEmpty(req.getItemGuid()), ItemDO::getGuid, req.getItemGuid())
                .orderByAsc(ItemDO::getSort);
        // 除了一体机以外的东西都不能用宴会套餐，排除宴会套餐
        if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.All_IN_ONE.getCode()), source)) {
            wrapper.ne(Optional.ofNullable(typeDO).isPresent(), ItemDO::getItemType, 5);
        } else {
            wrapper.ne(ItemDO::getItemType, 5);
        }
        List<ItemDO> itemDOList = list(wrapper);
        log.info("商品列表条件查询对应的菜品信息列表：{}", JacksonUtils.writeValueAsString(CollectionUtils.isEmpty(itemDOList) ? "" : itemDOList.stream().map(ItemDO::getGuid).collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(itemDOList)) {
            return itemAndTypeForAndroidRespDTO;
        }
        // 该门店下套餐GUID集合
        List<String> pkgGuidList = itemDOList.stream()
                .filter(itemDO -> itemDO.getItemType() == 1)
                .map(ItemDO::getGuid)
                .collect(Collectors.toList());
        // 所有套餐子商品的规格GUID集合
        List<String> subItemSkuGuidList;
        if (!CollectionUtils.isEmpty(pkgGuidList)) {
            List<RSkuSubgroupDO> subgroupList = subgroupService.selectSkuSubgroupByItemGuidList(pkgGuidList);
            subItemSkuGuidList = new ArrayList<>(subgroupList.stream().map(RSkuSubgroupDO::getSkuGuid).collect(Collectors.toSet()));
        } else {
            subItemSkuGuidList = new ArrayList<>();
        }
        log.info("套餐子商品的规格GUID集合：{}", JacksonUtils.writeValueAsString(subItemSkuGuidList));
        // 商品GUID集合
        List<String> itemGuidList = itemDOList.stream().map(ItemDO::getGuid).collect(Collectors.toList());
        // 商品的规格以及子商品的规格的详情集合
        Integer isJoinMiniAppMall = null;
        Integer isJoinMiniAppTakeaway = null;
        Integer isJoinStore = null;
        if (!StringUtils.isEmpty(req.getSceneCode())) {
            String[] senceCodeArgs = req.getSceneCode().split(",");
            for (String code : senceCodeArgs) {
                if (code.equals("1")) {
                    isJoinMiniAppMall = 1;
                }
                if (code.equals("2")) {
                    isJoinMiniAppTakeaway = 1;
                }
                if (code.equals("3")) {
                    isJoinStore = 1;
                }
            }
        }
        // 设备类型判断
        Integer isJoinAio = null;
        Integer isJoinPos = null;
        Integer isJoinPad = null;
        Integer isJoinBuffet = null;
        if (!StringUtils.isEmpty(source)) {
            if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.All_IN_ONE.getCode()), source)
                    || Objects.equals(String.valueOf(BaseDeviceTypeEnum.KDS.getCode()), source)) {
                isJoinAio = 1;
            }
            if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.PV1.getCode()), source)
                    || Objects.equals(String.valueOf(BaseDeviceTypeEnum.M1.getCode()), source)
                    || Objects.equals(String.valueOf(BaseDeviceTypeEnum.POS.getCode()), source)) {
                isJoinPos = 1;
            }
            if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.CLOUD_PANEL.getCode()), source)) {
                isJoinPad = 1;
            }
            if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.SELF.getCode()), source)) {
                isJoinBuffet = 1;
            }
        }

        List<SkuDO> skuDOList = skuService.list(
                new LambdaQueryWrapper<SkuDO>()
                        .and(j -> j.in(SkuDO::getGuid, subItemSkuGuidList)
                                .or(i -> i.in(SkuDO::getItemGuid, itemGuidList)
                                        .eq(req.getIsRack() == 1, SkuDO::getIsRack, 1)
                                )
                        )
                        .eq(isJoinMiniAppMall != null, SkuDO::getIsJoinMiniAppMall, isJoinMiniAppMall)
                        .eq(isJoinMiniAppTakeaway != null, SkuDO::getIsJoinMiniAppTakeaway, isJoinMiniAppTakeaway)
                        .eq(isJoinStore != null, SkuDO::getIsJoinStore, isJoinStore)
                        .eq(req.getIsRack() == 1, SkuDO::getIsRack, 1)
                        .eq(SkuDO::getIsEnable, true)
                        .eq(isJoinAio != null, SkuDO::getIsJoinAio, isJoinAio)
                        .eq(isJoinPos != null, SkuDO::getIsJoinPos, isJoinPos)
                        .eq(isJoinPad != null, SkuDO::getIsJoinPad, isJoinPad)
                        .eq(isJoinBuffet != null, SkuDO::getIsJoinBuffet, isJoinBuffet)
        );
        if (CollectionUtils.isEmpty(skuDOList)) {
            return itemAndTypeForAndroidRespDTO;
        }
        log.info("商品sku信息列表内容skuDOList：{}", JacksonUtils.writeValueAsString(CollectionUtils.isEmpty(skuDOList) ? "" : skuDOList.stream().map(SkuDO::getGuid).collect(Collectors.toList())));
        // 商品与子商品的所有商品GUID集合
        Set<String> allItemGuidSet = skuDOList.stream().map(SkuDO::getItemGuid).collect(Collectors.toSet());
        // 将商品转成返回的实体,not null
        List<ItemSynRespDTO> itemSynRespDTOList = MapStructUtils.INSTANCE.itemDOList2itemSynRespDTOList(itemDOList);

        // 商品及套餐子商品的ITEMDO ,not null
        List<ItemDO> allItemDOList = new ArrayList<>(itemDOList);
        // 商品及套餐子商品的返回实体集合,not null
        List<ItemSynRespDTO> allItemSynRespDTOList = new ArrayList<>(itemSynRespDTOList);
        // 联合上架单品与套餐子商品
        combineRackItemAndSubItem(allItemGuidSet, itemGuidList, allItemDOList, allItemSynRespDTOList);
        // 关联ITEM与SKU以及属性
        attchAttr2Item(allItemSynRespDTOList, allItemDOList);

        // 区分开商品与套餐子商品
        // 仅获取app端上的商品集合，并重新设置规格为已上架的规格
        List<ItemSynRespDTO> thisStoreAppItemList = selectAppItemAndSetSku(allItemSynRespDTOList, itemGuidList, skuDOList);
        // 如果没有上架商品
        if (CollectionUtils.isEmpty(thisStoreAppItemList)) {
            return itemAndTypeForAndroidRespDTO;
        } else {
            appItemList.addAll(thisStoreAppItemList);
        }
        log.info("商品已上架的规格：{}", JacksonUtils.writeValueAsString(CollectionUtils.isEmpty(thisStoreAppItemList) ? "" : thisStoreAppItemList.stream().map(ItemSynRespDTO::getItemGuid).collect(Collectors.toList())));
        // 关联分组以及子商品到套餐商品实体上  appItemList :所有APP上上架的商品（不含子商品）   allItemSynRespDTOList：所有商品和子商品  skuDOList：所有规格
        attachSubGroupAndSubItem2Pkg(appItemList, allItemSynRespDTOList, skuDOList);
        // 关联团餐及其子菜关系   appItemList :所有APP上上架的商品（不含子商品）   allItemSynRespDTOList：所有商品和子商品  skuDOList：所有规格
        if (!Optional.ofNullable(typeDO).isPresent()) {
            attachGroupMealAndSubItemPkg(appItemList);
        }
        // 仅为套餐修改安卓端同步时的套餐层的hasAttr字段（子商品的该字段不修改）
        setPkgHasAttrForAppOnly(appItemList);
        Set<String> withItemTypeGuidSet = appItemList.stream().map(ItemSynRespDTO::getTypeGuid).collect(Collectors.toSet());
        // 获取同步的分类集合
        List<TypeSynRespDTO> typeSynRespDTOList = selectTypeList(itemSynRespDTOList, withItemTypeGuidSet);
        List<ItemTemplateMenuSubItemDetailRespDTO> nowMeunSubItemForSyn = itemTemplateService.getNowMeunSubItemForSyn(req.getStoreGuid());
        if (Optional.ofNullable(nowMeunSubItemForSyn).isPresent() && !nowMeunSubItemForSyn.isEmpty()) {
            List<ItemSynRespDTO> items = itemAndTypeForAndroidRespDTO.getItemList();
            List<ItemSynRespDTO> itemList = new ArrayList<>();
            List<String> subItemGuids = nowMeunSubItemForSyn
                    .stream()
                    .map(ItemTemplateMenuSubItemDetailRespDTO::getSkuGuid)
                    .collect(Collectors.toList());
            Map<String, List<ItemTemplateMenuSubItemDetailRespDTO>> collect = nowMeunSubItemForSyn
                    .stream()
                    .collect(Collectors
                            .groupingBy(ItemTemplateMenuSubItemDetailRespDTO::getSkuGuid));
            for (ItemSynRespDTO o : items) {
                List<SkuSynRespDTO> skuList = o.getSkuList();
                int flag = 0;
                List<SkuSynRespDTO> nSkuList = new ArrayList<>();
                for (SkuSynRespDTO s : skuList) {
                    if (subItemGuids.contains(s.getSkuGuid())) {
                        s.setSalePrice(collect.get(s.getSkuGuid()).get(0).getPrice());
                        nSkuList.add(s);
                        flag++;
                    }
                }
                if (flag > 0) {
                    o.setSkuList(nSkuList);
                    itemList.add(o);
                }
            }
            itemAndTypeForAndroidRespDTO.setItemList(itemList);
        }
        if (Optional.ofNullable(itemAndTypeForAndroidRespDTO).map(ItemAndTypeForAndroidRespDTO::getItemList).isPresent()) {
            List<String> itemTypeGuids = itemAndTypeForAndroidRespDTO
                    .getItemList()
                    .stream()
                    .map(ItemSynRespDTO::getTypeGuid)
                    .collect(Collectors.toList());
            List<String> dbTypeGuids = typeSynRespDTOList
                    .stream()
                    .map(TypeSynRespDTO::getTypeGuid)
                    .collect(Collectors.toList());
            List<String> retainList = new ArrayList<>(itemTypeGuids);
            retainList.retainAll(dbTypeGuids);
            List<TypeSynRespDTO> resultType = new ArrayList<>();
            typeSynRespDTOList.forEach(s -> {
                if (retainList.contains(s.getTypeGuid())) {
                    resultType.add(s);
                }
            });
            typeList.addAll(resultType);
        }
        log.info("返回商品类型列表：{}", JacksonUtils.writeValueAsString(typeList));
        //设置商品毛利率
        setCrossMargin(itemAndTypeForAndroidRespDTO.getItemList(), itemDOList, subItemSkuGuidList);

        // 设置积分兑换商品
        setIntegralConversionItem(req.getOperSubjectGuid(), req.getStoreGuid(), itemAndTypeForAndroidRespDTO);

        //设置估清商品的数量
        setEstimateItem(itemAndTypeForAndroidRespDTO.getItemList(), req.getStoreGuid());
        return itemAndTypeForAndroidRespDTO;
    }

    private void setEstimateItem(List<ItemSynRespDTO> itemList, String storeGuid) {
        if (ObjectUtils.isEmpty(itemList)) {
            return;
        }
        Set<String> skuGuidList = new HashSet<>();
        itemList.forEach(obj -> {
            obj.getSkuList().forEach(sku -> skuGuidList.add(sku.getSkuGuid()));
        });
        List<EstimateDO> estimate = estimateMapper.queryEstimateBySkuGuidListAndStore(new ArrayList<>(skuGuidList), storeGuid);
        Map<String, EstimateDO> residueQuantityMap = estimate.stream()
                .collect(Collectors.toMap(EstimateDO::getSkuGuid, Function.identity(), (key1, key2) -> key2));
        itemList.forEach(obj -> {
            List<SkuSynRespDTO> skuList = obj.getSkuList();
            if (CollectionUtil.isNotEmpty(skuList)) {
                skuList.forEach(sku -> {
                    EstimateDO estimateDO = residueQuantityMap.get(sku.getSkuGuid());
                    if (ObjectUtil.isNotNull(estimateDO)) {

                        if (ObjectUtil.equal(estimateDO.getIsTheLimit(), 2)) {
                            sku.setStock(BigDecimalUtil.nonNullValue(estimateDO.getResidueQuantity()).intValue());
                            sku.setIsOpenStock(BooleanEnum.TRUE.getCode());
                        }

                        if (ObjectUtil.equal(estimateDO.getIsSoldOut(), 2)) {
                            sku.setStock(0);
                            sku.setIsOpenStock(BooleanEnum.TRUE.getCode());
                        }

                    }
                });
            }
        });
    }

    /**
     * 设置积分兑换商品
     *
     * @param operSubjectGuid 运营主体
     * @param storeGuid       门店guid
     * @param respDTO         商品返回实体
     */
    private void setIntegralConversionItem(String operSubjectGuid, String storeGuid, ItemAndTypeForAndroidRespDTO respDTO) {
        if (!ObjectUtils.isEmpty(operSubjectGuid)) {

            // 查询积分商城商品
            UserContext userContext = UserContextUtils.get();
            userContext.setOperSubjectGuid(operSubjectGuid);
            UserContextUtils.put(userContext);
            MemberResult<List<ResponseIntegralConversionItem>> result = merchantClientService.queryConversionRuleByStore(storeGuid);
            List<ResponseIntegralConversionItem> conversionItemList = result.getTData();
            log.info("积分商品查询结果 result={}", JacksonUtils.writeValueAsString(conversionItemList));
            if (CollectionUtils.isEmpty(conversionItemList)) {
                log.error("未查询到积分兑换商品，storeGuid={}", storeGuid);
                return;
            }

            // 商品正常存在才存在积分商品
            List<ItemSynRespDTO> itemList = respDTO.getItemList();
            if (CollectionUtils.isEmpty(itemList)) {
                log.warn("商品集合为空");
                return;
            }
            List<String> iGuidList = itemList.stream()
                    .map(ItemSynRespDTO::getItemGuid)
                    .collect(Collectors.toList());
            log.info("iGuidList={}", iGuidList);
            conversionItemList.removeIf(con -> !iGuidList.contains(con.getItemGuid()));

            // 积分兑换商品对应的商品如果已经下架则不展示
            List<String> skuGuidList = new ArrayList<>();
            conversionItemList.forEach(item -> {
                List<String> skuList = item.getConversionItemSkuList().stream()
                        .map(ResponseIntegralConversionItemSku::getSkuGuid)
                        .distinct()
                        .collect(Collectors.toList());
                skuGuidList.addAll(skuList);
            });
            if (!CollectionUtils.isEmpty(skuGuidList)) {
                List<SkuDO> list = skuService.list(new LambdaQueryWrapper<SkuDO>().in(SkuDO::getGuid, skuGuidList));
                List<String> itemGuidList = list.stream()
                        .filter(sku -> Objects.equals(0, sku.getIsRack()))
                        .map(SkuDO::getItemGuid)
                        .distinct()
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(itemGuidList)) {
                    conversionItemList.removeIf(item -> itemGuidList.contains(item.getItemGuid()));
                }
            }

            Map<String, ItemSynRespDTO> originItemMap = itemList.stream()
                    .collect(Collectors.toMap(ItemSynRespDTO::getItemGuid, item -> item));

            // 商品设置
            for (ResponseIntegralConversionItem cItem : conversionItemList) {
                ItemSynRespDTO source = originItemMap.get(cItem.getItemGuid());
                if (ObjectUtils.isEmpty(source)) {
                    log.warn("商品原数据查询异常 itemGuid={}", cItem.getItemGuid());
                    continue;
                }
                ItemSynRespDTO target = new ItemSynRespDTO();
                BeanUtils.copyProperties(source, target);
                // 完善数据
                target.setTypeGuid(TYPE_GUID);
                target.setTypeName(TYPE_NAME);
                target.setDescription("积分商城兑换商品");

                // 规格设置
                Map<String, SkuSynRespDTO> originSkuMap = target.getSkuList().stream()
                        .collect(Collectors.toMap(SkuSynRespDTO::getSkuGuid, sku -> sku));
                List<SkuSynRespDTO> skuSynRespDTOList = new ArrayList<>();
                List<ResponseIntegralConversionItemSku> conversionItemSkuList = cItem.getConversionItemSkuList();
                if (!CollectionUtils.isEmpty(conversionItemSkuList)) {
                    for (ResponseIntegralConversionItemSku cSku : conversionItemSkuList) {
                        SkuSynRespDTO skuSynRespDTO = originSkuMap.get(cSku.getSkuGuid());
                        if (skuSynRespDTO == null) {
                            continue;
                        }
                        skuSynRespDTO.setIntegralConversionValue(cSku.getIntegralConversionValue());
                        skuSynRespDTO.setIntegralConversionMoney(cSku.getIntegralConversionMoney());
                        skuSynRespDTOList.add(skuSynRespDTO);
                    }
                }
                target.setSkuList(skuSynRespDTOList);
                itemList.add(target);
            }

            // 如果积分商城商品为空则不返回积分商城分类
            List<ItemSynRespDTO> dtoList = itemList.stream()
                    .filter(i -> Objects.equals(TYPE_GUID, i.getTypeGuid()))
                    .collect(Collectors.toList());
            log.info("积分商城商品={}", JacksonUtils.writeValueAsString(dtoList));

            if (!CollectionUtils.isEmpty(dtoList)) {
                // 分类设置
                TypeSynRespDTO typeSynRespDTO = new TypeSynRespDTO();
                // 这是夏如鹏强烈要求设置的--他的生日作为积分商城约定的分类guid
                typeSynRespDTO.setTypeGuid(TYPE_GUID);
                typeSynRespDTO.setName(TYPE_NAME);
                // 默认放在最上面
                typeSynRespDTO.setSort(1);
                typeSynRespDTO.setDescription(TYPE_NAME);

                respDTO.getTypeList().add(typeSynRespDTO);
            }
        }
    }

    /**
     * 查询价格方案的菜品。
     * 特殊时段菜谱方案，时间段不会交叉；
     * 当同时存在全时段和特殊时段菜谱方案时，优先推送特殊时段菜谱下的菜品
     */
    private Pair<Boolean, ItemAndTypeForAndroidRespDTO> pricePlanItem(ItemQueryListReq req) {

        ItemAndTypeForAndroidRespDTO respDTO = new ItemAndTypeForAndroidRespDTO();
        List<TypeSynRespDTO> typeList = new ArrayList<>();
        List<ItemSynRespDTO> itemList = new ArrayList<>();
        respDTO.setItemList(itemList);
        respDTO.setTypeList(typeList);

        Pair<Boolean, ItemAndTypeForAndroidRespDTO> pair = Pair.of(true, respDTO);

        List<PricePlanNowDTO> planNowList = pricePlanStoreService.findPlanNowStoreGuid(LocalDateTime.now(), req.getStoreGuid());
        log.info("菜品方案查询结果：{}", JacksonUtils.writeValueAsString(planNowList));

        // 没有生效的菜谱
        if (CollectionUtil.isEmpty(planNowList)) {
            respDTO.setSalesModel(SalesModelEnum.NORMAL_MODE.getCode());
            pair = Pair.of(false, respDTO);
            return pair;
        }
        //根据时间过滤生效的菜谱guid
        String effectStorePlanGuid = itemHelper.getEffectStorePlanGuid(planNowList);

        if (Objects.isNull(effectStorePlanGuid)) {
            respDTO.setSalesModel(SalesModelEnum.RECIPE_MODE.getCode());
            return pair;
        }
        respDTO.setSalesModel(SalesModelEnum.RECIPE_MODE.getCode());
        respDTO.setPricePlanGuid(effectStorePlanGuid);
        List<PricePlanItemDO> pricePlanItemDOList = planItemRepository.listEffectPlanItem(effectStorePlanGuid, req.getItemGuid());
        if (CollectionUtil.isEmpty(pricePlanItemDOList)) {
            log.info("菜谱方案下选的菜品为空：{}", effectStorePlanGuid);
            return pair;
        }

        List<String> allItemGuidList = pricePlanItemDOList.stream()
                .map(PricePlanItemDO::getItemGuid)
                .collect(Collectors.toList());

        List<String> allSkuGuidList = pricePlanItemDOList.stream()
                .map(PricePlanItemDO::getSkuGuid)
                .collect(Collectors.toList());

        List<ItemDO> itemDOList = list(new LambdaQueryWrapper<ItemDO>()
                        .eq(ItemDO::getIsEnable, true)
                        .in(ItemDO::getGuid, allItemGuidList)
//                .orderByAsc(ItemDO::getSort)
        );

        if (CollectionUtils.isEmpty(itemDOList)) {
            return pair;
        }

        // 菜品分类转换
        Map<String, PricePlanItemDO> planItemMap = pricePlanItemDOList.stream()
                .collect(Collectors.toMap(PricePlanItemDO::getItemGuid, Function.identity(), (key1, key2) -> key2));
        for (ItemDO itemDO : itemDOList) {
            PricePlanItemDO pricePlanItemDO = planItemMap.get(itemDO.getGuid());
            itemDO.setTypeGuid(pricePlanItemDO.getTypeGuid());
            itemDO.setSort(pricePlanItemDO.getSort());
            itemDO.setDescription(pricePlanItemDO.getDescription());
        }

        // 根据菜谱商品重新排序
        itemDOList.sort(Comparator.comparing(ItemDO::getSort));

        // 将商品转成返回的实体
        List<ItemSynRespDTO> itemSynRespDTOList = MapStructUtils.INSTANCE.itemDOList2itemSynRespDTOList(itemDOList);

        // 获取菜品的分类集合pricePlanItemDOList
        List<TypeSynRespDTO> typeSynRespDTOList = selectTypeList2(pricePlanItemDOList);
        log.info("价格方案分类数据：{}", JacksonUtils.writeValueAsString(typeSynRespDTOList));
        typeList.addAll(typeSynRespDTOList);


        // 该门店下套餐GUID集合
        List<String> pkgGuidList = itemDOList.stream().filter(itemDO -> itemDO.getItemType() == 1)
                .map(ItemDO::getGuid)
                .collect(Collectors.toList());
        // 商品GUID集合
        List<String> itemGuidList = itemDOList.stream().map(ItemDO::getGuid).collect(Collectors.toList());

        // 所有套餐子商品的规格GUID集合
        List<String> subItemSkuGuidList;
        if (!CollectionUtils.isEmpty(pkgGuidList)) {
            List<RSkuSubgroupDO> subgroupList = subgroupService.selectSkuSubgroupByItemGuidList(pkgGuidList);
            subItemSkuGuidList = subgroupList.stream()
                    .map(RSkuSubgroupDO::getSkuGuid)
                    .distinct()
                    .collect(Collectors.toList());
        } else {
            subItemSkuGuidList = new ArrayList<>();
        }
        log.info("菜谱方案套餐子商品的规格：{}", JacksonUtils.writeValueAsString(subItemSkuGuidList));

        // 获取终端等属性
        PricePlanTerminalDTO pricePlanTerminal = getPricePlanTerminal(req.getSceneCode());
        List<SkuDO> skuDOList = skuService.list(
                new LambdaQueryWrapper<SkuDO>()
                        .and(j -> j.in(SkuDO::getGuid, subItemSkuGuidList)
                                .or(i -> i.in(SkuDO::getGuid, allSkuGuidList)
                                        .eq(req.getIsRack() == 1, SkuDO::getIsRack, 1)
                                )
                        )
                        .eq(pricePlanTerminal.getIsJoinMiniAppMall() != null, SkuDO::getIsJoinMiniAppMall, pricePlanTerminal.getIsJoinMiniAppMall())
                        .eq(pricePlanTerminal.getIsJoinMiniAppTakeaway() != null, SkuDO::getIsJoinMiniAppTakeaway, pricePlanTerminal.getIsJoinMiniAppTakeaway())
                        .eq(pricePlanTerminal.getIsJoinStore() != null, SkuDO::getIsJoinStore, pricePlanTerminal.getIsJoinStore())
//                        .eq(isRack == 1, SkuDO::getIsRack, 1)
                        .eq(SkuDO::getIsEnable, true)
                        .eq(pricePlanTerminal.getIsJoinAio() != null, SkuDO::getIsJoinAio, pricePlanTerminal.getIsJoinAio())
                        .eq(pricePlanTerminal.getIsJoinPos() != null, SkuDO::getIsJoinPos, pricePlanTerminal.getIsJoinPos())
                        .eq(pricePlanTerminal.getIsJoinPad() != null, SkuDO::getIsJoinPad, pricePlanTerminal.getIsJoinPad())
                        .eq(pricePlanTerminal.getIsJoinBuffet() != null, SkuDO::getIsJoinBuffet, pricePlanTerminal.getIsJoinBuffet())
        );
        if (CollectionUtils.isEmpty(skuDOList)) {
            return pair;
        }

        // 商品与子商品的所有商品GUID集合
        Set<String> allItemGuidSet = skuDOList.stream().map(SkuDO::getItemGuid).collect(Collectors.toSet());
        // 商品及套餐子商品的ITEMDO ,not null
        List<ItemDO> allItemDOList = new ArrayList<>(itemDOList);
        // 商品及套餐子商品的返回实体集合
        List<ItemSynRespDTO> allItemSynRespDTOList = new ArrayList<>(itemSynRespDTOList);
        // 联合上架单品与套餐子商品
        combineRackItemAndSubItem(allItemGuidSet, itemGuidList, allItemDOList, allItemSynRespDTOList);
        // 关联ITEM与SKU以及属性
        attchAttr2Item(allItemSynRespDTOList, allItemDOList);

        // 区分开商品与套餐子商品
        // 仅获取app端上的商品集合，并重新设置规格为已上架的规格
        List<ItemSynRespDTO> thisStoreAppItemList = selectAppItemAndSetSku(allItemSynRespDTOList, itemGuidList, skuDOList);
        // 如果没有上架商品
        if (CollectionUtils.isEmpty(thisStoreAppItemList)) {
            return pair;
        } else {
            itemList.addAll(thisStoreAppItemList);
        }

        // 关联分组以及子商品到套餐商品实体上
        // itemList :所有APP上上架的商品（不含子商品）
        // allItemSynRespDTOList：所有商品和子商品
        // skuDOList：所有规格
        attachSubGroupAndSubItem2Pkg(itemList, allItemSynRespDTOList, skuDOList);
        // 仅为套餐修改安卓端同步时的套餐层的hasAttr字段（子商品的该字段不修改）
        setPkgHasAttrForAppOnly(itemList);

        // 使用价格方案设置的规格会员价和售卖价
        assembleBaseInfo(itemList, pricePlanItemDOList);
        // 稳一手，再排一次序
        itemList.sort(Comparator.comparing(ItemSynRespDTO::getSort));
        return pair;
    }

    private void assembleBaseInfo(List<ItemSynRespDTO> itemList, List<PricePlanItemDO> pricePlanItemDOList) {
        Map<String, PricePlanItemDO> skuGuidMap = pricePlanItemDOList.stream()
                .collect(Collectors.toMap(PricePlanItemDO::getSkuGuid, pricePlanItemDO -> pricePlanItemDO));
        for (ItemSynRespDTO item : itemList) {
            List<SkuSynRespDTO> skuList = item.getSkuList();
            // delete
            skuList.removeIf(s -> ObjectUtils.isEmpty(skuGuidMap.get(s.getSkuGuid())));
            for (SkuSynRespDTO sku : skuList) {
                String skuGuid = sku.getSkuGuid();
                // 设置菜谱方案设置的价格
                PricePlanItemDO pricePlanItemDO = skuGuidMap.get(skuGuid);
                // sku replace
                copyPricePlanItemDO2SkuSynRespDTO(pricePlanItemDO, sku);
            }
            // item replace
            copyPricePlanItemDO2ItemSynRespDTO(skuGuidMap.get(skuList.get(skuList.size() - 1).getSkuGuid()), item);
            // subgroup sku replace
            List<SubgroupSynRespDTO> subgroupList = item.getSubgroupList();
            resetSubgroupSkuList(subgroupList, skuGuidMap);
        }
    }

    /**
     * 重新设置套餐子商品
     */
    private void resetSubgroupSkuList(List<SubgroupSynRespDTO> subgroupList, Map<String, PricePlanItemDO> skuGuidMap) {
        if (CollectionUtils.isEmpty(subgroupList)) {
            return;
        }
        for (SubgroupSynRespDTO subgroupSynRespDTO : subgroupList) {
            List<SubItemSkuSynRespDTO> subItemSkuList = subgroupSynRespDTO.getSubItemSkuList();
            if (CollectionUtils.isEmpty(subItemSkuList)) {
                continue;
            }
            for (SubItemSkuSynRespDTO subSku : subItemSkuList) {
                PricePlanItemDO pricePlanItemDO = skuGuidMap.get(subSku.getSkuGuid());
                copyPricePlanItemDO2SubItemSkuSynRespDTO(pricePlanItemDO, subSku);
            }
        }
    }


    /**
     * 商品item copy属性
     */
    private void copyPricePlanItemDO2ItemSynRespDTO(PricePlanItemDO pricePlanItemDO, ItemSynRespDTO item) {
        if (Objects.isNull(pricePlanItemDO)) {
            return;
        }
        item.setDescription(blankSetting(pricePlanItemDO.getDescription()));
        item.setEnglishBrief(blankSetting(pricePlanItemDO.getEnglishBrief()));
        item.setEnglishIngredientsDesc(blankSetting(pricePlanItemDO.getEnglishIngredientsDesc()));

        item.setSort(pricePlanItemDO.getSort());
        item.setName(pricePlanItemDO.getPlanItemName());

        item.setPictureUrl(emptyNullSetting(pricePlanItemDO.getPictureUrl()));
        item.setBigPictureUrl(emptyNullSetting(pricePlanItemDO.getBigPictureUrl()));
        item.setDetailBigPictureUrl(emptyNullSetting(pricePlanItemDO.getDetailBigPictureUrl()));
    }

    /**
     * 商品sku copy属性
     */
    private void copyPricePlanItemDO2SkuSynRespDTO(PricePlanItemDO pricePlanItemDO, SkuSynRespDTO sku) {
        if (Objects.isNull(pricePlanItemDO)) {
            return;
        }
        sku.setMemberPrice(priceNullSetting(pricePlanItemDO.getMemberPrice()));
        sku.setAccountingPrice(priceNullSetting(pricePlanItemDO.getAccountingPrice()));
        sku.setLinePrice(priceNullSetting(pricePlanItemDO.getLinePrice()));
        sku.setSalePrice(pricePlanItemDO.getSalePrice());
    }

    /**
     * 套餐子商品copy属性
     */
    private void copyPricePlanItemDO2SubItemSkuSynRespDTO(PricePlanItemDO pricePlanItemDO, SubItemSkuSynRespDTO sku) {
        if (Objects.isNull(pricePlanItemDO)) {
            return;
        }
        sku.setSalePrice(pricePlanItemDO.getSalePrice());
    }


    private String emptyNullSetting(String value) {
        return ObjectUtils.isEmpty(value) ? null : value;
    }

    private BigDecimal priceNullSetting(BigDecimal value) {
        return ObjectUtils.isEmpty(value) ? null : value;
    }

    private String blankSetting(String value) {
        return StringUtils.isNotBlank(value) ? value : "";

    }

    private List<TypeSynRespDTO> selectTypeList2(List<PricePlanItemDO> pricePlanItemDOList) {
        Set<String> typeGuidSet = pricePlanItemDOList.stream().map(PricePlanItemDO::getTypeGuid).collect(Collectors.toSet());
        List<TypeDO> typeDOList = typeService.list(new LambdaQueryWrapper<TypeDO>()
                .in(TypeDO::getGuid, typeGuidSet)
                .eq(TypeDO::getIsEnable, 1)
                .orderByAsc(TypeDO::getSort));
        return MapStructUtils.INSTANCE.typeDOList2typeSynRespDTOList(typeDOList);
    }

    /**
     * 获取终端等属性
     *
     * @param senceCode 适用场景（1商城;2外卖;3堂食，用英文逗号分隔）
     * @return PricePlanTerminalDTO
     */
    private PricePlanTerminalDTO getPricePlanTerminal(String senceCode) {
        PricePlanTerminalDTO terminalDTO = new PricePlanTerminalDTO();
        if (!StringUtils.isEmpty(senceCode)) {
            String[] senceCodeArgs = senceCode.split(",");
            for (String code : senceCodeArgs) {
                if (code.equals("1")) {
                    terminalDTO.setIsJoinMiniAppMall(1);
                }
                if (code.equals("2")) {
                    terminalDTO.setIsJoinMiniAppTakeaway(1);
                }
                if (code.equals("3")) {
                    terminalDTO.setIsJoinStore(1);
                }
            }
        }
        // 设备类型判断
        String source = UserContextUtils.getSource();
        if (!StringUtils.isEmpty(source)) {
            if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.All_IN_ONE.getCode()), source)
                    || Objects.equals(String.valueOf(BaseDeviceTypeEnum.KDS.getCode()), source)) {
                terminalDTO.setIsJoinAio(1);
            }
            if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.PV1.getCode()), source)
                    || Objects.equals(String.valueOf(BaseDeviceTypeEnum.M1.getCode()), source)
                    || Objects.equals(String.valueOf(BaseDeviceTypeEnum.POS.getCode()), source)) {
                terminalDTO.setIsJoinPos(1);
            }
            if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.CLOUD_PANEL.getCode()), source)) {
                terminalDTO.setIsJoinPad(1);
            }
            if (Objects.equals(String.valueOf(BaseDeviceTypeEnum.SELF.getCode()), source)) {
                terminalDTO.setIsJoinBuffet(1);
            }
        }

        return terminalDTO;
    }

    /**
     * 设置商品毛利率
     *
     * @param itemSynRespDTOS    返回实体
     * @param itemDOList         商品实体
     * @param subItemSkuGuidList 子商品sku
     */
    private void setCrossMargin(List<ItemSynRespDTO> itemSynRespDTOS, List<ItemDO> itemDOList, List<String> subItemSkuGuidList) {
        BigDecimal itemGrossMargin;
        if (ObjectUtils.isEmpty(itemSynRespDTOS)) {
            return;
        }
        Map<String, ItemDO> collect = itemDOList.stream().collect(Collectors.toMap(ItemDO::getGuid, Function.identity()));
        for (ItemSynRespDTO itemSynRespDTO : itemSynRespDTOS) {
            List<SubgroupSynRespDTO> subgroupList = itemSynRespDTO.getSubgroupList();
            if (ObjectUtils.isEmpty(subgroupList)) {
                continue;
            }
            for (SubgroupSynRespDTO subgroupSynRespDTO : subgroupList) {
                List<SubItemSkuSynRespDTO> subItemSkuList = subgroupSynRespDTO.getSubItemSkuList();
                if (ObjectUtils.isEmpty(subItemSkuGuidList)) {
                    continue;
                }
                for (SubItemSkuSynRespDTO subItemSkuSynRespDTO : subItemSkuList) {
                    if (Objects.nonNull(collect.get(subItemSkuSynRespDTO.getItemGuid()))
                            && Objects.nonNull(collect.get(subItemSkuSynRespDTO.getItemGuid()).getPictureUrl())) {
                        subItemSkuSynRespDTO.setPictureUrl(collect.get(subItemSkuSynRespDTO.getItemGuid()).getPictureUrl());
                    }
                    itemGrossMargin = null;
                    //如果成本价、销售价不为空，才设置宴会套餐毛利率
                    if (Objects.nonNull(subItemSkuSynRespDTO.getCostPrice()) && Objects.nonNull(subItemSkuSynRespDTO.getSalePrice())
                            && subItemSkuSynRespDTO.getCostPrice().compareTo(BigDecimal.ZERO) >= 0
                            && subItemSkuSynRespDTO.getSalePrice().compareTo(BigDecimal.ZERO) > 0) {
                        itemGrossMargin = ((subItemSkuSynRespDTO.getSalePrice())
                                .subtract(subItemSkuSynRespDTO.getCostPrice()))
                                .divide((subItemSkuSynRespDTO.getSalePrice()), 4, RoundingMode.HALF_UP);
                    }
                    subItemSkuSynRespDTO.setItemGrossMargin(itemGrossMargin);
                }
            }
        }
    }

    /**
     * 返回一个组合的数据
     *
     * @param wxSearchItemDto 微信扫描点餐搜索商品dto
     * @return Pair<List < PricePlanItemDO>, IPage<ItemDO>>
     */
    private Pair<List<PricePlanItemDO>, IPage<ItemDO>> queryItems(WxSearchItemDto wxSearchItemDto) {
        List<PricePlanItemDO> pricePlanItemDOList = new ArrayList<>();
        LocalDateTime nowDateTime = LocalDateTime.now();
        String minute = (nowDateTime.getMinute() + "").length() == 1 ? "0" + nowDateTime.getMinute() : nowDateTime.getMinute() + "";
        int nowTime = Integer.parseInt(nowDateTime.getHour() + "" + minute);

        List<PricePlanNowDTO> planNowList = pricePlanStoreService.findPlanNowStoreGuid(nowDateTime, wxSearchItemDto.getStoreGuid());
        if (CollectionUtil.isEmpty(planNowList)) {
            // 如果生效时间内没有菜谱，则返回普通模式的查询
            log.warn("生效时间内没有菜谱");
            return Pair.of(pricePlanItemDOList, itemMapper.wxSearchItems(new PageAdapter<>(wxSearchItemDto), wxSearchItemDto));
        } else {
            PricePlanNowDTO pricePlanNowAllTime = null;
            PricePlanNowDTO pricePlanNowTime = null;
            for (PricePlanNowDTO planNow : planNowList) {
                Integer sellTimeType = planNow.getSellTimeType();
                if (sellTimeType == 0) {
                    // 全时段菜品方案
                    pricePlanNowAllTime = planNow;
                } else {
                    // 特殊时段菜品方案
                    LocalDateTime startTime = planNow.getStartTime();
                    LocalDateTime endTime = planNow.getEndTime();
                    String startMinute = (startTime.getMinute() + "").length() == 1 ? "0" + startTime.getMinute() : startTime.getMinute() + "";
                    int startTimeInt = Integer.parseInt(startTime.getHour() + "" + startMinute);
                    String endMinute = (endTime.getMinute() + "").length() == 1 ? "0" + endTime.getMinute() : endTime.getMinute() + "";
                    int endTimeInt = Integer.parseInt(endTime.getHour() + "" + endMinute);
                    log.info("特殊时段时间,nowTime->{}, startTimeInt->{}, endTimeInt->{}", nowTime, startTimeInt, endTimeInt);
                    if (endTimeInt > startTimeInt) {
                        // 灭有垮天
                        if (nowTime >= startTimeInt && nowTime <= endTimeInt) {
                            pricePlanNowTime = planNow;
                            log.info("特殊时段方案筛选结果：,{}", JacksonUtils.writeValueAsString(pricePlanNowTime));
                        }
                    } else {
                        // 垮天了
                        if (nowTime >= startTimeInt || nowTime <= endTimeInt) {
                            pricePlanNowTime = planNow;
                            log.info("特殊时段垮天方案筛选结果：,{}", JacksonUtils.writeValueAsString(pricePlanNowTime));
                        }
                    }

                }
            }

            // 没有菜谱方案
            log.info("特殊时段筛选结果：,{},all->{}", JacksonUtils.writeValueAsString(pricePlanNowTime), JacksonUtils.writeValueAsString(pricePlanNowAllTime));
            if (Objects.isNull(pricePlanNowTime) && Objects.isNull(pricePlanNowAllTime)) {
                // 如果菜谱模式下查询不到菜谱，则返回普通模式的查询
                log.warn("没有菜谱方案");
                return Pair.of(pricePlanItemDOList, itemMapper.wxSearchItems(new PageAdapter<>(wxSearchItemDto), wxSearchItemDto));
            }

            if (!Objects.isNull(pricePlanNowTime)) {
                // 特殊时段菜谱 商品列表
                pricePlanItemDOList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                        .eq(PricePlanItemDO::getIsDelete, 0)
                        .in(PricePlanItemDO::getIsSoldOut, 0, 1, 3)
                        .eq(PricePlanItemDO::getPlanGuid, pricePlanNowTime.getPlanGuid())
                        .like(StringUtils.isNotEmpty(wxSearchItemDto.getKeywords()), PricePlanItemDO::getPlanItemName,
                                wxSearchItemDto.getKeywords().replaceAll("%", ""))
                        .orderByAsc(PricePlanItemDO::getSort));
            } else {
                // 全时段菜谱 商品列表
                pricePlanItemDOList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                        .in(PricePlanItemDO::getIsSoldOut, 0, 1, 3)
                        .eq(PricePlanItemDO::getPlanGuid, pricePlanNowAllTime.getPlanGuid())
                        .like(StringUtils.isNotEmpty(wxSearchItemDto.getKeywords()), PricePlanItemDO::getPlanItemName,
                                wxSearchItemDto.getKeywords().replaceAll("%", ""))
                        .orderByAsc(PricePlanItemDO::getSort));
            }
            log.info("菜谱方案下选的菜品：{}", JacksonUtils.writeValueAsString(pricePlanItemDOList));
            if (CollectionUtil.isEmpty(pricePlanItemDOList)) {
                // 如果菜谱模式下菜谱商品为空，则返回普通模式的查询
                log.warn("菜谱方案没有商品");
                return Pair.of(pricePlanItemDOList, new PageAdapter<>(new PageAdapter<>(wxSearchItemDto), new ArrayList<>()));
            }

            List<String> allItemGuidList = pricePlanItemDOList.stream()
                    .map(PricePlanItemDO::getItemGuid)
                    .collect(Collectors.toList());
            List<ItemDO> itemDOList = list(new LambdaQueryWrapper<ItemDO>()
                            .eq(ItemDO::getIsEnable, true)
                            .in(ItemDO::getGuid, allItemGuidList)
//                    .orderByAsc(ItemDO::getSort)错误的顺序
            );
            log.info("查询到初始品牌菜品：{}", JSONObject.toJSONString(itemDOList));

            // 最小改动：将菜谱的数据设置进item，以避免下面模糊查询出错
            for (PricePlanItemDO planItem : pricePlanItemDOList) {
                for (ItemDO item : itemDOList) {
                    if (Objects.equals(item.getGuid(), planItem.getItemGuid())) {
                        item.setName(planItem.getPlanItemName());
                        item.setDescription(planItem.getDescription());
                        item.setSort(planItem.getSort());
                        item.setPictureUrl(planItem.getPictureUrl());
                        item.setTypeGuid(planItem.getTypeGuid());
                    }
                }
            }

            // 按照菜谱排序
            itemDOList.sort(Comparator.comparing(ItemDO::getSort));
            /*if (StringUtils.isNotEmpty(wxSearchItemDto.getKeywords())) {
                String search = wxSearchItemDto.getKeywords().replaceAll("%", "");
                itemDOList = itemDOList.stream()
                        .filter(i -> i.getName().contains(search))
                        .collect(Collectors.toList());
                log.info("过滤key[{}], 结果[{}]", search, JSONObject.toJSONString(itemDOList));
            }*/
            return Pair.of(pricePlanItemDOList, new PageAdapter<>(new PageAdapter<>(wxSearchItemDto), itemDOList));
        }
    }

    @Override
    public Page<ItemSynRespDTO> wxSearchItems(WxSearchItemDto wxSearchItemDto) {
        Page<ItemSynRespDTO> page = new Page<>();
        // 返回的商品列表
        List<ItemSynRespDTO> appItemList;
        // 菜谱查询的商品数据
        List<PricePlanItemDO> planItemDOList = new ArrayList<>();
        //根据菜品模式查询商品-待修改
        IPage<ItemDO> itemDOIPage;
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(wxSearchItemDto.getStoreGuid());
        if (brandDTO.getSalesModel() == SalesModelEnum.RECIPE_MODE.getCode()) {
            // 查询生效的菜谱方案
            Pair<List<PricePlanItemDO>, IPage<ItemDO>> queryItemPair = queryItems(wxSearchItemDto);
            planItemDOList = queryItemPair.getFirst();
            itemDOIPage = queryItemPair.getSecond();
        } else {
            itemDOIPage = itemMapper.wxSearchItems(new PageAdapter<>(wxSearchItemDto), wxSearchItemDto);
        }

        List<ItemDO> itemDOList = itemDOIPage.getRecords();
        if (CollectionUtils.isEmpty(itemDOList)) {
            return page;
        }
        // 该门店下套餐GUID集合
        List<String> pkgGuidList = itemDOList.stream()
                .filter(itemDO -> itemDO.getItemType() == 1)
                .map(ItemDO::getGuid)
                .collect(Collectors.toList());
        // 所有套餐子商品的规格GUID集合
        List<String> subItemSkuGuidList;
        if (!CollectionUtils.isEmpty(pkgGuidList)) {
            List<RSkuSubgroupDO> subgroupList = subgroupService.selectSkuSubgroupByItemGuidList(pkgGuidList);
            subItemSkuGuidList = subgroupList.stream().map(RSkuSubgroupDO::getSkuGuid).distinct().collect(Collectors.toList());
        } else {
            subItemSkuGuidList = new ArrayList<>();
        }
        // 商品GUID集合
        List<String> itemGuidList = itemDOList.stream().map(ItemDO::getGuid).collect(Collectors.toList());
        // 商品的规格以及子商品的规格的详情集合
        List<SkuDO> skuDOList = skuService.list(
                new LambdaQueryWrapper<SkuDO>()
                        .and(j -> j.in(SkuDO::getGuid, subItemSkuGuidList)
                                .or(i -> i.in(SkuDO::getItemGuid, itemGuidList)))
                        .eq(SkuDO::getIsEnable, true)
                        // 假如这是专门给扫码点餐用的，那么这里加上搜索的限制，如果后续有问题再考虑用其他方式
                        .eq(SkuDO::getIsJoinWeChat, 1)
        );
        if (CollectionUtils.isEmpty(skuDOList)) {
            return page;
        }

        // 商品与子商品的所有商品GUID集合
        Set<String> allItemGuidSet = skuDOList.stream().map(SkuDO::getItemGuid).collect(Collectors.toSet());
        // 将商品转成返回的实体,not null
        List<ItemSynRespDTO> itemSynRespDTOList = MapStructUtils.INSTANCE.itemDOList2itemSynRespDTOList(itemDOList);
        // 商品及套餐子商品的ITEMDO ,not null
        List<ItemDO> allItemDOList = new ArrayList<>(itemDOList);
        // 商品及套餐子商品的返回实体集合,not null
        List<ItemSynRespDTO> allItemSynRespDTOList = new ArrayList<>(itemSynRespDTOList);
        // 联合上架单品与套餐子商品
        combineRackItemAndSubItem(allItemGuidSet, itemGuidList, allItemDOList, allItemSynRespDTOList);
        // 关联ITEM与SKU以及属性
        attchAttr2Item(allItemSynRespDTOList, allItemDOList);

        // 区分开商品与套餐子商品
        // 仅获取app端上的商品集合，并重新设置规格为已上架的规格
        List<ItemSynRespDTO> thisStoreAppItemList = selectAppItemAndSetSku(allItemSynRespDTOList, itemGuidList, skuDOList);
        // 如果没有上架商品
        if (CollectionUtils.isEmpty(thisStoreAppItemList)) {
            return page;
        } else {
            appItemList = new ArrayList<>(thisStoreAppItemList);
        }
        // 关联分组以及子商品到套餐商品实体上  appItemList :所有APP上上架的商品（不含子商品）   allItemSynRespDTOList：所有商品和子商品  skuDOList：所有规格
        attachSubGroupAndSubItem2Pkg(appItemList, allItemSynRespDTOList, skuDOList);
        // 仅为套餐修改安卓端同步时的套餐层的hasAttr字段（子商品的该字段不修改）
        setPkgHasAttrForAppOnly(appItemList);
        // 设置菜谱模式的价格
        if (brandDTO.getSalesModel() == SalesModelEnum.RECIPE_MODE.getCode() && !CollectionUtils.isEmpty(planItemDOList)) {
            Map<String, List<PricePlanItemDO>> planItemDOMap = planItemDOList.stream()
                    .collect(Collectors.groupingBy(PricePlanItemDO::getItemGuid));
            appItemList.forEach(item -> {
                List<PricePlanItemDO> planItemDOS = planItemDOMap.get(item.getItemGuid());
                List<SkuSynRespDTO> skuList = new ArrayList<>();
                Map<String, SkuSynRespDTO> skuSynRespDTOMap = item.getSkuList().stream()
                        .collect(Collectors.toMap(SkuSynRespDTO::getSkuGuid, s -> s));
                planItemDOS.forEach(sku -> {
                    SkuSynRespDTO skuSynRespDTO = skuSynRespDTOMap.get(sku.getSkuGuid());
                    skuSynRespDTO.setSalePrice(sku.getSalePrice());
                    skuSynRespDTO.setMemberPrice(sku.getMemberPrice());
                    skuList.add(skuSynRespDTO);
                });
                item.setSkuList(skuList);
            });
        }
        setCrossMargin(appItemList, itemDOList, subItemSkuGuidList);
        page.setCurrentPage(itemDOIPage.getCurrent());
        page.setData(appItemList);
        page.setPageSize(itemDOIPage.getSize());
        page.setTotalCount(itemDOIPage.getTotal());
        return page;
    }

    /**
     * 仅为套餐修改安卓端同步时的套餐层的hasAttr字段（子商品的该字段不修改）
     *
     * @param appItemList
     */
    private void setPkgHasAttrForAppOnly(List<ItemSynRespDTO> appItemList) {
        if (CollectionUtils.isEmpty(appItemList)) {
            return;
        }
        appItemList.forEach(itemSynRespDTO -> {
            if (itemSynRespDTO.getItemType() != 1) {
                return;
            }
            List<SubgroupSynRespDTO> subgroupList = itemSynRespDTO.getSubgroupList();
            List<SubItemSkuSynRespDTO> subItemList = new ArrayList<>();
            subgroupList.forEach(subgroupSynRespDTO -> subItemList.addAll(subgroupSynRespDTO.getSubItemSkuList()));
            boolean hasRequiredAttr = subItemList.stream().anyMatch(subItemSkuSynRespDTO -> subItemSkuSynRespDTO.getHasAttr() == 2);
            if (hasRequiredAttr) {
                itemSynRespDTO.setHasAttr(2);
            } else {
                boolean hasAttr = subItemList.stream().anyMatch(subItemSkuSynRespDTO -> subItemSkuSynRespDTO.getHasAttr() == 1);
                if (hasAttr) {
                    itemSynRespDTO.setHasAttr(1);
                }
            }
        });
    }

    /**
     * 获取安卓端同步的分类集合
     *
     * @param itemSynRespDTOList  itemSynRespDTOList
     * @param withItemTypeGuidSet 未用
     * @return List<TypeSynRespDTO>
     */
    private List<TypeSynRespDTO> selectTypeList(List<ItemSynRespDTO> itemSynRespDTOList, Set<String> withItemTypeGuidSet) {
        Set<String> typeGuidSet = itemSynRespDTOList.stream().map(ItemSynRespDTO::getTypeGuid).collect(Collectors.toSet());
        List<TypeDO> typeDOList = typeService.list(new LambdaQueryWrapper<TypeDO>()
                .in(TypeDO::getGuid, typeGuidSet)
                .eq(TypeDO::getIsEnable, 1)
                .orderByAsc(TypeDO::getSort));
        return MapStructUtils.INSTANCE.typeDOList2typeSynRespDTOList(typeDOList);
    }

    /**
     * 获取安卓端同步的菜谱分类集合
     *
     * @param typeGuidSet 分类 GUID LIST
     * @return List<TypeSynRespDTO>
     */
    private List<TypeSynRespDTO> selectTypeList(Set<String> typeGuidSet) {
        List<TypeDO> typeDOList = typeService.list(new LambdaQueryWrapper<TypeDO>()
                .in(TypeDO::getGuid, typeGuidSet)
                .eq(TypeDO::getIsEnable, 1)
                .orderByAsc(TypeDO::getSort));
        return MapStructUtils.INSTANCE.typeDOList2typeSynRespDTOList(typeDOList);
    }


    /**
     * 关联分组和子商品到团餐套餐上
     *
     * @param appItemList 所有APP上上架的商品（不含子商品）
     */
    private void attachGroupMealAndSubItemPkg(List<ItemSynRespDTO> appItemList) {
        //获取到已上架的团餐
        List<ItemSynRespDTO> rackPkgList = appItemList.stream().filter(appItem -> appItem.getItemType() == 5).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(rackPkgList)) {
            List<String> groupMealItems = rackPkgList
                    .stream()
                    .map(ItemSynRespDTO::getItemGuid)
                    .collect(Collectors.toList());
            //获取到团餐下子商品详情和sku详情
            List<GroupMealSubItemBO> listGroupMealSubItemS = itemMapper.getListGroupMealSubItemS(groupMealItems);
            if (!CollectionUtils.isEmpty(listGroupMealSubItemS)) {
                //根据套餐主项进行有多少个团餐商品
                Map<String, List<GroupMealSubItemBO>> collect = listGroupMealSubItemS.stream()
                        .collect(Collectors.groupingBy(groupMealSubItemBO -> groupMealSubItemBO.getItemGuid()));
                //循环团餐主项进行归档
                for (ItemSynRespDTO pkgDTO : rackPkgList) {
                    // 套餐DTO
                    //获取到单个团餐详情 未分组
                    List<GroupMealSubItemBO> groupMealSubItemBOS = collect.get(pkgDTO.getItemGuid());
                    //判断是否有加子菜，有子菜则进行分组
                    if (Objects.nonNull(groupMealSubItemBOS)) {
                        //循环团餐子项按照分组名称进行分组
                        Map<String, List<GroupMealSubItemBO>> listMap = groupMealSubItemBOS.stream()
                                .collect(Collectors.groupingBy(groupMealSubItemBO -> groupMealSubItemBO.getTypeName()));
                        //循环分组
                        List<SubgroupSynRespDTO> subgroupSynRespDTOList = new ArrayList<>();
                        listMap.forEach((typeName, list) -> {
                            SubgroupSynRespDTO subgroupSynRespDTO = new SubgroupSynRespDTO();
                            subgroupSynRespDTO.setItemGuid(list.get(0).getItemGuid());
                            subgroupSynRespDTO.setName(typeName);
                            subgroupSynRespDTO.setPickNum(10);
                            subgroupSynRespDTO.setSubgroupGuid(list.get(0).getTypeGuid());
                            List<SubItemSkuSynRespDTO> subItemSkuSynRespDTOS = MapStructUtils.INSTANCE.groupMealSubItemBOS2SubItemSkuSynRespDTOS(list);
                            subgroupSynRespDTO.setSubItemSkuList(subItemSkuSynRespDTOS);
                            subgroupSynRespDTOList.add(subgroupSynRespDTO);
                        });
                        pkgDTO.setSubgroupList(subgroupSynRespDTOList);
                    }
                    pkgDTO.setIsFixPkg(1);
                }
            }
        }
    }

    /**
     * 关联分组和子商品到套餐上
     *
     * @param appItemList           所有APP上上架的商品（不含子商品）
     * @param allItemSynRespDTOList 所有商品和子商品
     * @param skuDOList             所有涉及的规格集合
     */
    private void attachSubGroupAndSubItem2Pkg(List<ItemSynRespDTO> appItemList,
                                              List<ItemSynRespDTO> allItemSynRespDTOList,
                                              List<SkuDO> skuDOList) {
        // 上架的套餐集合
        // test 有套餐时
        List<ItemSynRespDTO> rackPkgList = appItemList.stream()
                .filter(appItem -> appItem.getItemType() == 1)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(rackPkgList)) {
            //排除部分子商品下架套餐
            Set<String> excludePkgItem = new HashSet<>();
            // 套餐的商品GUID集合
            List<String> pkgItemGuidList = rackPkgList.stream()
                    .map(ItemSynRespDTO::getItemGuid)
                    .collect(Collectors.toList());
            List<SubgroupDO> subgroupDOList = subgroupService.list(new LambdaQueryWrapper<SubgroupDO>()
                    .in(SubgroupDO::getItemGuid, pkgItemGuidList)
                    .orderByAsc(SubgroupDO::getSort));
            if (CollectionUtils.isEmpty(subgroupDOList)) {
                log.error(SYSTEM_ERROR);
                throw new BusinessException(SYSTEM_ERROR);
            }
            // 按商品分组的MAP
            Map<String, List<SubgroupDO>> itemGroupMap = subgroupDOList.stream()
                    .collect(Collectors.groupingBy(SubgroupDO::getItemGuid));
            List<String> subgroupGuidList = subgroupDOList.stream()
                    .map(SubgroupDO::getGuid)
                    .collect(Collectors.toList());
            // sku与分组的关联实体集合
            List<RSkuSubgroupDO> skuSubgroupDOList = skuSubgroupService.list(new LambdaQueryWrapper<RSkuSubgroupDO>()
                    .in(RSkuSubgroupDO::getSubgroupGuid, subgroupGuidList)
                    .orderByAsc(RSkuSubgroupDO::getSort));
            if (CollectionUtils.isEmpty(skuSubgroupDOList)) {
                log.error(SYSTEM_ERROR);
                throw new BusinessException(SYSTEM_ERROR);
            }
            // 按套餐分组分组的MAP
            Map<String, List<RSkuSubgroupDO>> subgroupGroupMap = skuSubgroupDOList.stream()
                    .collect(Collectors.groupingBy(RSkuSubgroupDO::getSubgroupGuid));
            Iterator<ItemSynRespDTO> pkgIterator = rackPkgList.iterator();
            while (pkgIterator.hasNext()) {
                // 套餐DTO
                ItemSynRespDTO pkgDTO = pkgIterator.next();
                // 当前套餐下的分组
                List<SubgroupDO> thisPkgSubgroupDOList = itemGroupMap.get(pkgDTO.getItemGuid());
                if (CollectionUtils.isEmpty(thisPkgSubgroupDOList)) {
                    log.error(" 套餐下分组为空 商品Guid为{}", pkgDTO.getItemGuid());
                    throw new BusinessException("套餐下分组不能为空");
                }
                List<SubgroupSynRespDTO> subgroupSynRespDTOList = MapStructUtils.INSTANCE.subgroupDOList2subgroupSynRespDTOList(thisPkgSubgroupDOList);
                subgroupSynRespDTOList.forEach(subgroupSynRespDTO -> {
                    // 查询当前分组下的子商品与分组关联实体
                    List<RSkuSubgroupDO> skuSubgroupDOS = subgroupGroupMap.get(subgroupSynRespDTO.getSubgroupGuid());
                    if (CollectionUtils.isEmpty(skuSubgroupDOS)) {
                        throw new BusinessException("分组下至少关联一个商品");
                    }
                    // 当前分组下的子商品实体
                    List<SubItemSkuSynRespDTO> subItemSkuSynRespDTOList = MapStructUtils.INSTANCE.skuSubgroupDOList2subItemSkuSynRespDTOList(skuSubgroupDOS);
                    subItemSkuSynRespDTOList.forEach(subItemSkuSynRespDTO -> {
                        ItemSynRespDTO thisItem = allItemSynRespDTOList.stream()
                                .filter(itemSynRespDTO -> itemSynRespDTO.getItemGuid().equals(subItemSkuSynRespDTO.getItemGuid()))
                                .findFirst().orElse(null);
                        SkuDO thisSkuDO = skuDOList.stream().filter(skuDO -> skuDO.getGuid().equals(subItemSkuSynRespDTO.getSkuGuid())).findFirst().orElse(null);
                        fillSubItemSkuSynRespDTOFields(subItemSkuSynRespDTO, thisItem, thisSkuDO);
                        //需要排除的套餐
                        if (ObjectUtil.isNull(thisItem)) {
                            excludePkgItem.add(pkgDTO.getItemGuid());
                        }
                    });
//                    //过滤套餐子商品中在终端已下线的商品，防止终端报错
//                    List<SubItemSkuSynRespDTO> collect = subItemSkuSynRespDTOList.stream().filter(subItemSku -> ObjectUtil.isNotNull(subItemSku.getTypeGuid())
//                                    && ObjectUtil.isNotNull(subItemSku.getItemName()) && ObjectUtil.isNotNull(subItemSku.getUnit()))
//                            .collect(Collectors.toList());
                    subgroupSynRespDTO.setSubItemSkuList(subItemSkuSynRespDTOList);
                });
                pkgDTO.setSubgroupList(subgroupSynRespDTOList);
                boolean fixPkg = subgroupSynRespDTOList.stream().allMatch(subgroupSynRespDTO -> subgroupSynRespDTO.getPickNum() == 0);
                if (fixPkg) {
                    pkgDTO.setIsFixPkg(1);
                }
            }
            if (CollectionUtil.isNotEmpty(excludePkgItem)) {
                log.info("需要移除的套餐信息itemGuid：{}", JacksonUtils.writeValueAsString(excludePkgItem));
                appItemList.removeIf(item -> excludePkgItem.contains(item.getItemGuid()));
            }

        }
    }

    /**
     * 获取App端的上架商品（不含套餐子商品）
     *
     * @param allItemSynRespDTOList
     * @param itemGuidList          所有当前门店的商品GUID集合（不一定含子商品GUID）
     * @param skuDOList             所有涉及的规格实体集合
     * @return
     */
    private List<ItemSynRespDTO> selectRackAppItemAndSetSku(List<ItemSynRespDTO> allItemSynRespDTOList, List<String> itemGuidList, List<SkuDO> skuDOList) {
        if (CollectionUtils.isEmpty(allItemSynRespDTOList)
                || CollectionUtils.isEmpty(itemGuidList)
                || CollectionUtils.isEmpty(skuDOList)) {
            log.error(SYSTEM_ERROR);
            throw new BusinessException(SYSTEM_ERROR);
        }
        List<ItemSynRespDTO> appItemList = allItemSynRespDTOList.stream()
                .filter(itemSynRespDTO -> itemGuidList.contains(itemSynRespDTO.getItemGuid()))
                .collect(Collectors.toList());
        Iterator<ItemSynRespDTO> appItemIterator = appItemList.iterator();
        BigDecimal itemGrossMargin = BigDecimal.ZERO;
        while (appItemIterator.hasNext()) {
            // app端的商品
            ItemSynRespDTO appItem = appItemIterator.next();
            // 当前商品所含上架规格
            List<SkuDO> thisItemSkuDOList = skuDOList.stream()
                    .filter(skuDO -> appItem.getItemGuid().equals(skuDO.getItemGuid())
                            && skuDO.getIsRack() == 1)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(thisItemSkuDOList)) {
                // 移除当前商品
                appItemIterator.remove();
            } else {
                List<SkuSynRespDTO> skuSynRespDTOList = MapStructUtils.INSTANCE.skuDOList2skuSynRespDTOList(thisItemSkuDOList);
                skuSynRespDTOList.sort(Comparator.comparing(SkuSynRespDTO::getSalePrice));
                appItem.setSkuList(skuSynRespDTOList);
                //设置商品毛利率
                for (SkuSynRespDTO skuSynRespDTO : skuSynRespDTOList) {
                    itemGrossMargin = null;
                    //如果成本价、销售价不为空，才设置毛利率
                    if (Objects.nonNull(skuSynRespDTO.getCostPrice()) && Objects.nonNull(skuSynRespDTO.getSalePrice())
                            && skuSynRespDTO.getCostPrice().compareTo(BigDecimal.ZERO) >= 0
                            && skuSynRespDTO.getSalePrice().compareTo(BigDecimal.ZERO) > 0) {
                        itemGrossMargin = ((skuSynRespDTO.getSalePrice())
                                .subtract((skuSynRespDTO.getCostPrice())))
                                .divide((skuSynRespDTO.getSalePrice()), 4, RoundingMode.HALF_UP);
                    }
                    skuSynRespDTO.setItemGrossMargin(itemGrossMargin);
                }
            }
        }
        return appItemList;
    }

    /**
     * 获取App端的商品（不含套餐子商品、不区分是否上架）
     *
     * @param allItemSynRespDTOList
     * @param itemGuidList          所有当前门店的商品GUID集合（不一定含子商品GUID）
     * @param skuDOList             所有涉及的规格实体集合
     * @return
     */
    private List<ItemSynRespDTO> selectAppItemAndSetSku(List<ItemSynRespDTO> allItemSynRespDTOList, List<String> itemGuidList, List<SkuDO> skuDOList) {
        if (CollectionUtils.isEmpty(allItemSynRespDTOList)
                || CollectionUtils.isEmpty(itemGuidList)
                || CollectionUtils.isEmpty(skuDOList)) {
            log.error(SYSTEM_ERROR);
            throw new BusinessException(SYSTEM_ERROR);
        }
        List<ItemSynRespDTO> appItemList = allItemSynRespDTOList.stream()
                .filter(itemSynRespDTO -> itemGuidList.contains(itemSynRespDTO.getItemGuid()))
                .collect(Collectors.toList());
        Iterator<ItemSynRespDTO> appItemIterator = appItemList.iterator();

        while (appItemIterator.hasNext()) {
            // app端的商品
            ItemSynRespDTO appItem = appItemIterator.next();
            // 当前商品所含上架规格
            List<SkuDO> thisItemSkuDOList = skuDOList.stream()
                    .filter(skuDO -> appItem.getItemGuid().equals(skuDO.getItemGuid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(thisItemSkuDOList)) {
                // 移除当前商品
                appItemIterator.remove();
                continue;
            }
            List<SkuSynRespDTO> skuSynRespDTOList = MapStructUtils.INSTANCE.skuDOList2skuSynRespDTOList(thisItemSkuDOList);
            //设置商品毛利率和设置称重商品code
            setItemGrossMarginAndWeightCode(skuSynRespDTOList);
            //若skulist大小大于1并且商品若是单品类型则更正为多规格（马尾咖啡老板助手导致发生此情况）
            if(skuSynRespDTOList.size() > 1 && appItem.getItemType() == ItemTypeEnum.SINGLE.getTypeCode()){
                appItem.setItemType(ItemTypeEnum.MULTI_SPEC.getTypeCode());
            }
            appItem.setSkuList(skuSynRespDTOList);
        }
        return appItemList;
    }

    private void setItemGrossMarginAndWeightCode(List<SkuSynRespDTO> skuSynRespDTOList) {
        for (SkuSynRespDTO skuSynRespDTO : skuSynRespDTOList) {
            BigDecimal itemGrossMargin = null;
            //如果成本价、销售价不为空，才设置毛利率
            if (Objects.nonNull(skuSynRespDTO.getCostPrice()) && Objects.nonNull(skuSynRespDTO.getSalePrice())
                    && skuSynRespDTO.getCostPrice().compareTo(BigDecimal.ZERO) >= 0
                    && skuSynRespDTO.getSalePrice().compareTo(BigDecimal.ZERO) > 0) {
                itemGrossMargin = ((skuSynRespDTO.getSalePrice())
                        .subtract((skuSynRespDTO.getCostPrice())))
                        .divide((skuSynRespDTO.getSalePrice()), 4, RoundingMode.HALF_UP);
            }
            skuSynRespDTO.setItemGrossMargin(itemGrossMargin);
        }
    }

    /**
     * 将关联规格和属性到商品实体
     *
     * @param allItemSynRespDTOList
     * @param allItemDOList
     */
    private void attchAttr2Item(List<ItemSynRespDTO> allItemSynRespDTOList, List<ItemDO> allItemDOList) {
        if (CollectionUtils.isEmpty(allItemSynRespDTOList) || CollectionUtils.isEmpty(allItemDOList)) {
            log.error(SYSTEM_ERROR);
            throw new BusinessException(SYSTEM_ERROR);
        }
        Set<String> allItemGuidSet = allItemDOList.stream().map(ItemDO::getGuid).collect(Collectors.toSet());
        // 获取商品关联的属性组详情
        List<AttrGroupSynRespDTO> attrGroupSynRespDTOList = attrGroupService.selectAttrGroupSynRespDtoByItemGuidList(allItemGuidSet);
        // 按商品分组的属性组详情MAP
        Map<String, List<AttrGroupSynRespDTO>> itemGroupMap = attrGroupSynRespDTOList.stream().collect(Collectors.groupingBy(AttrGroupSynRespDTO::getItemGuid));
        Set<String> typeGuidSet = allItemDOList.stream().map(ItemDO::getTypeGuid).collect(Collectors.toSet());
        // 获取商品关联分类
        Collection<TypeDO> typeDOS = typeService.listByIds(typeGuidSet);
        if (CollectionUtils.isEmpty(typeDOS)) {
            log.error(SYSTEM_ERROR);
            throw new BusinessException(SYSTEM_ERROR);
        }
        // 遍历每一个商品以及子商品实体
        for (ItemSynRespDTO itemSynRespDTO : allItemSynRespDTOList) {
            // 当前商品
            setType(itemSynRespDTO, typeDOS);
            itemSynRespDTO.setIsFixPkg(0);
            ItemDO item = allItemDOList.stream()
                    .filter(itemDO -> itemDO.getGuid().equals(itemSynRespDTO.getItemGuid()))
                    .findFirst().orElse(null);
            if (item == null) {
                log.error(SYSTEM_ERROR);
                throw new BusinessException(SYSTEM_ERROR);
            }
            List<TagEnum> tagList = getTagList(item);
            List<TagRespDTO> tagRespDTOList = MapStructUtils.INSTANCE.tagEnumList2tagRespDTOList(tagList);
            itemSynRespDTO.setTagList(tagRespDTOList);
            // 与当前商品关联的属性组
            List<AttrGroupSynRespDTO> attrGroupList;
            if (itemSynRespDTO.getHasAttr() != 0) {
                attrGroupList = itemGroupMap.getOrDefault(itemSynRespDTO.getItemGuid(), new ArrayList<>());
            } else {
                attrGroupList = new ArrayList<>();
            }
            // 关联商品与属性
            itemSynRespDTO.setAttrGroupList(attrGroupList);
        }
    }

    @NotNull
    private List<TagEnum> getTagList(ItemDO item) {
        Integer isBestseller = item.getIsBestseller();
        Integer isNew = item.getIsNew();
        Integer isSign = item.getIsSign();
        Integer isRecommend = item.getIsRecommend();
        // 标签集合
        return getTagEnumList(isBestseller, isNew, isSign, isRecommend);
    }

    /**
     * 标签集合
     */
    @NotNull
    private List<TagEnum> getTagEnumList(Integer isBestseller,
                                         Integer isNew,
                                         Integer isSign,
                                         Integer isRecommend) {
        List<TagEnum> tagList = new ArrayList<>();
        if (isBestseller == 1) {
            tagList.add(TagEnum.BESTSELLER);
        }
        if (isNew == 1) {
            tagList.add(TagEnum.NEW);
        }
        if (isSign == 1) {
            tagList.add(TagEnum.SIGN);
        }
        if (isRecommend == 1) {
            tagList.add(TagEnum.RECOMMEND);
        }
        return tagList;
    }

    /**
     * 设置商品的分类名称
     *
     * @param itemSynRespDTO
     * @param typeDOS
     */
    private void setType(ItemSynRespDTO itemSynRespDTO, Collection<TypeDO> typeDOS) {
        if (itemSynRespDTO == null || CollectionUtils.isEmpty(typeDOS)) {
            log.error(SYSTEM_ERROR);
            throw new BusinessException(SYSTEM_ERROR);
        }
        TypeDO thisItemType = typeDOS.stream().filter(typeDO -> typeDO.getGuid().equals(itemSynRespDTO.getTypeGuid())).findFirst().orElse(null);
        itemSynRespDTO.setTypeName(thisItemType == null ? "" : thisItemType.getName());
    }

    /**
     * 如果除了上架商品外还有子商品，则联合上架商品以及子商品实体，以一起处理商品与属性的关联关系
     *
     * @param allItemGuidSet
     * @param itemGuidList
     * @param allItemDOList
     * @param allItemSynRespDTOList
     */
    private void combineRackItemAndSubItem(Set<String> allItemGuidSet, List<String> itemGuidList, List<ItemDO> allItemDOList,
                                           List<ItemSynRespDTO> allItemSynRespDTOList) {
        // 获取子商品集合,may null
        allItemGuidSet.removeAll(itemGuidList);
        if (!CollectionUtils.isEmpty(allItemGuidSet)) {
            // 有不是上架单品的子商品
            // 子商品集合
            List<ItemDO> subItemDOList = list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, allItemGuidSet));
            allItemDOList.addAll(subItemDOList);
            // 子商品的返回数据
            List<ItemSynRespDTO> subItemSynRespDTOList = MapStructUtils.INSTANCE.itemDOList2itemSynRespDTOList(subItemDOList);
            allItemSynRespDTOList.addAll(subItemSynRespDTOList);
        }
    }


    /**
     * 完善子商品字段
     *
     * @param subItemSkuSynRespDTO 当前子商品
     * @param thisItem             子商品对应的数据库商品实体
     * @param thisSkuDO            子商品对应的数据库规格实体
     */
    private void fillSubItemSkuSynRespDTOFields(SubItemSkuSynRespDTO subItemSkuSynRespDTO, ItemSynRespDTO thisItem, SkuDO thisSkuDO) {
        if (thisItem != null) {
            subItemSkuSynRespDTO.setHasAttr(thisItem.getHasAttr());
            subItemSkuSynRespDTO.setAttrGroupList(thisItem.getAttrGroupList());
            subItemSkuSynRespDTO.setItemName(thisItem.getName());
            subItemSkuSynRespDTO.setItemType(thisItem.getItemType());
            subItemSkuSynRespDTO.setTypeGuid(thisItem.getTypeGuid());
            subItemSkuSynRespDTO.setTypeName(thisItem.getTypeName());
            subItemSkuSynRespDTO.setPictureUrl(thisItem.getPictureUrl());
            if (thisSkuDO != null) {
                subItemSkuSynRespDTO.setSkuName(thisSkuDO.getName());
                subItemSkuSynRespDTO.setCode(thisSkuDO.getCode());
                subItemSkuSynRespDTO.setIsJoinWeChat(thisSkuDO.getIsJoinWeChat());
                subItemSkuSynRespDTO.setUnit(thisSkuDO.getUnit());
                subItemSkuSynRespDTO.setSalePrice(thisSkuDO.getSalePrice());
            } else {
                subItemSkuSynRespDTO.setUnit("");
            }
        } else {
            subItemSkuSynRespDTO.setHasAttr(0);
        }
    }

    @Override
    public Page<ItemWebRespDTO> selectItemListForWeb(ItemQueryReqDTO itemQueryReqDTO) {
        // 如果上架的值是-1,则不过滤是否上架
        if (Integer.valueOf("-1").equals(itemQueryReqDTO.getIsRack())) {
            itemQueryReqDTO.setIsRack(null);
        }
        // 带上架条件初筛的规格集合
        List<SkuDO> skuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>()
                .eq(SkuDO::getIsEnable, true)
                .eq(itemQueryReqDTO.getIsRack() != null, SkuDO::getIsRack, itemQueryReqDTO.getIsRack())
                .eq(Objects.nonNull(itemQueryReqDTO.getIsWholeDiscount()), SkuDO::getIsWholeDiscount, itemQueryReqDTO.getIsWholeDiscount())
                .eq(!StringUtils.isEmpty(itemQueryReqDTO.getStoreGuid()), SkuDO::getStoreGuid, itemQueryReqDTO.getStoreGuid())
                .eq(!StringUtils.isEmpty(itemQueryReqDTO.getBrandGuid()), SkuDO::getBrandGuid, itemQueryReqDTO.getBrandGuid())
        );
        if (CollectionUtils.isEmpty(skuDOList)) {
            Page<ItemWebRespDTO> page = new Page<>();
            page.setData(new ArrayList<>());
            page.setPageSize(itemQueryReqDTO.getPageSize());
            page.setCurrentPage(itemQueryReqDTO.getCurrentPage());
            page.setTotalCount(0L);
            return page;
        }
        // 符合上下架过滤条件的商品GUID
        Set<String> withRackItemGuidList = skuDOList.stream().map(SkuDO::getItemGuid).collect(Collectors.toSet());
        MPPage<ItemDO> itemDOIPageQuery = new MPPage<>(itemQueryReqDTO.getCurrentPage(), itemQueryReqDTO.getPageSize());
        LambdaQueryWrapper<ItemDO> itemPageWrapper = new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getIsEnable, true)
                .in(ItemDO::getGuid, withRackItemGuidList)
                .eq(!StringUtils.isEmpty(itemQueryReqDTO.getStoreGuid()), ItemDO::getStoreGuid, itemQueryReqDTO.getStoreGuid())
                .eq(!StringUtils.isEmpty(itemQueryReqDTO.getBrandGuid()), ItemDO::getBrandGuid, itemQueryReqDTO.getBrandGuid())
                .eq(!StringUtils.isEmpty(itemQueryReqDTO.getTypeGuid()), ItemDO::getTypeGuid, itemQueryReqDTO.getTypeGuid())
                .eq(Objects.nonNull(itemQueryReqDTO.getModel()), ItemDO::getItemType, 5)
                .ne(Objects.isNull(itemQueryReqDTO.getModel()), ItemDO::getItemType, 5)
                .like(!StringUtils.isEmpty(itemQueryReqDTO.getCode()), ItemDO::getCode, itemQueryReqDTO.getCode());

        if (StringUtils.isNotEmpty(itemQueryReqDTO.getName())) {
            List<String> codeItemGuidList = skuDOList.stream()
                    .filter(sku -> !com.holderzone.framework.util.StringUtils.isEmpty(sku.getCode()) && sku.getCode().contains(itemQueryReqDTO.getName()))
                    .map(SkuDO::getItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            itemPageWrapper.and(wrapper -> wrapper
                    .like(ItemDO::getName, itemQueryReqDTO.getName())
                    .or()
                    .like(ItemDO::getGuid, itemQueryReqDTO.getName())
                    .or()
                    .in(ItemDO::getGuid, codeItemGuidList)
            );
        }
        //筛选商品类型
        if (!CollectionUtils.isEmpty(itemQueryReqDTO.getItemTypeList())) {
            itemPageWrapper.in(ItemDO::getItemType, itemQueryReqDTO.getItemTypeList());
        }

        itemPageWrapper.and(!StringUtils.isEmpty(itemQueryReqDTO.getSearchKey()),
                wrapper -> wrapper.like(ItemDO::getName, itemQueryReqDTO.getSearchKey())
                        .or().like(ItemDO::getCode, itemQueryReqDTO.getSearchKey()));
        itemPageWrapper.orderByAsc(Objects.isNull(itemQueryReqDTO.getModel()), ItemDO::getSort)
                .orderByDesc(Objects.isNull(itemQueryReqDTO.getModel()), ItemDO::getGmtCreate)
                .orderByDesc(Objects.nonNull(itemQueryReqDTO.getModel()), ItemDO::getGmtCreate);

        IPage<ItemDO> itemDOIPage = this.page(itemDOIPageQuery, itemPageWrapper);
        List<ItemDO> itemDOS = itemDOIPage.getRecords();
        List<String> itemGuidList = itemDOS.stream().map(ItemDO::getGuid).collect(Collectors.toList());
        List<String> typeGuidList = itemDOS.stream().map(ItemDO::getTypeGuid).collect(Collectors.toList());
        List<TypeDO> typeDOS = typeService.list(new LambdaQueryWrapper<TypeDO>().in(TypeDO::getGuid, typeGuidList));
        Map<String, String> typeCollents = typeDOS.stream().collect(Collectors
                .toMap(TypeDO::getGuid, a -> a.getName()));
        Page<ItemWebRespDTO> itemWebRespDTOPage = MapStructUtils.INSTANCE.itemDOIPage2itemWebRespDTOPage(itemDOIPage);
        itemWebRespDTOPage.getData().stream()
                .peek(itemWebRespDTO
                        -> itemWebRespDTO.setTypeName(typeCollents
                        .get(itemWebRespDTO.getTypeGuid())));
        // 页面上展示的商品集合
        List<ItemWebRespDTO> itemWebRespDTOList = itemWebRespDTOPage.getData();
        if (CollectionUtils.isEmpty(itemWebRespDTOList)) {
            return itemWebRespDTOPage;
        }
        // 列表商品所有的规格集合
        skuDOList.removeIf(skuDO -> !itemGuidList.contains(skuDO.getItemGuid()));

        // 被当前商品集合推送至门店的规格集合
        addAllToSkuDOList(itemQueryReqDTO, skuDOList);

        // 商品与关联套餐数量映射关系
        Map<String, Integer> item2RelatePkgNumMap = itemHelper.mapItem2RelatePkgNum(skuDOList);
        //todo 商品和宴会套餐映射关系  传入skuGuid 返回 map<itemGuid,num>
        Map<String, Integer> relateMaping = itemHelper.mapItem2RelateGroupMealPakNum(skuDOList);
        if (Objects.nonNull(relateMaping)) {
            item2RelatePkgNumMap.putAll(relateMaping);
        }

        //品牌下商品所在门店数统计
        final Map<String, Long> countUseStoreMap = countItemUseStoreMap(itemQueryReqDTO.getBrandGuid(), itemWebRespDTOList);

        Iterator<ItemWebRespDTO> iterator = itemWebRespDTOList.iterator();
        while (iterator.hasNext()) {
            ItemWebRespDTO itemWebRespDTO = iterator.next();
            // 此商品所含规格集合
            List<SkuDO> thisItemSkuDOList = skuDOList.stream()
                    .filter(skuDO -> skuDO.getItemGuid().equals(itemWebRespDTO.getItemGuid()))
                    .collect(Collectors.toList());
            int num = item2RelatePkgNumMap.get(itemWebRespDTO.getItemGuid());
            itemWebRespDTO.setIsWithPkg(num > 0 ? 1 : 0);
            if (CollectionUtils.isEmpty(thisItemSkuDOList)) {
                iterator.remove();
                continue;
            }
            // 设置上架门店数
            itemWebRespDTO.setRackStoreNum(0);

            if (!countUseStoreMap.isEmpty()) {
                // 获取上架门店数
                final Long itemStoreNum = countUseStoreMap.getOrDefault(itemWebRespDTO.getItemGuid(), 0L);
                itemWebRespDTO.setRackStoreNum(itemStoreNum.intValue());
            }

//            if (!ObjectUtils.isEmpty(itemQueryReqDTO.getBrandGuid())) {
//                //todo 1
//                List<String> storeGuidList = getUseStoreGuidList(itemWebRespDTO.getItemGuid(), itemQueryReqDTO.getBrandGuid());
//                if (!CollectionUtils.isEmpty(storeGuidList)) {
//                    itemWebRespDTO.setRackStoreNum(storeGuidList.size());
//                }
//            }

            // 是否有上架
            boolean hasRack = thisItemSkuDOList.stream().anyMatch(skuDO -> skuDO.getIsRack() == 1);
            // 是否有上架
            boolean IsJoinAio = thisItemSkuDOList.stream().anyMatch(skuDO -> skuDO.getIsJoinAio() == 1);
            // 是否有上架
            boolean IsJoinPos = thisItemSkuDOList.stream().anyMatch(skuDO -> skuDO.getIsJoinPos() == 1);
            // 是否有上架
            boolean IsJoinPad = thisItemSkuDOList.stream().anyMatch(skuDO -> skuDO.getIsJoinPad() == 1);
            // 是否有会员折扣
            boolean hasMemberDiscount = thisItemSkuDOList.stream().anyMatch(skuDO -> skuDO.getIsMemberDiscount() == 1);
            // 是否有整单折扣
            boolean hasWholeDiscount = thisItemSkuDOList.stream().anyMatch(skuDO -> skuDO.getIsWholeDiscount() == 1);
            // 是否支持小程序商城
            boolean isJoinMall = thisItemSkuDOList.stream().anyMatch(skuDO -> skuDO.getIsJoinMiniAppMall() == 1);
            // 是否支持小程序外卖
            boolean isJoinTakeaway = thisItemSkuDOList.stream().anyMatch(skuDO -> skuDO.getIsJoinMiniAppMall() == 1);
            // 是否支持小程序堂食
            boolean isJoinStore = thisItemSkuDOList.stream().anyMatch(skuDO -> skuDO.getIsJoinStore() == 1);


            // 最低价格的SKU
            SkuDO lowestPriceSku = thisItemSkuDOList.stream().min(Comparator.comparing(SkuDO::getSalePrice)).orElse(null);
            if (lowestPriceSku == null) {
                log.error(SYSTEM_ERROR);
                throw new BusinessException(SYSTEM_ERROR);
            }
            itemWebRespDTO.setLowestPrice(lowestPriceSku.getSalePrice());
            itemWebRespDTO.setIsRack(hasRack ? 1 : 0);
            itemWebRespDTO.setIsJoinAio(IsJoinAio ? 1 : 0);
            itemWebRespDTO.setIsJoinPos(IsJoinPos ? 1 : 0);
            itemWebRespDTO.setIsJoinPad(IsJoinPad ? 1 : 0);
            itemWebRespDTO.setIsMemberDiscount(hasMemberDiscount ? 1 : 0);
            itemWebRespDTO.setIsWholeDiscount(hasWholeDiscount ? 1 : 0);
            itemWebRespDTO.setIsJoinStore(isJoinStore ? 1 : 0);
            itemWebRespDTO.setIsJoinMiniAppMall(isJoinMall ? 1 : 0);
            itemWebRespDTO.setIsJoinMiniAppTakeaway(isJoinTakeaway ? 1 : 0);
            //属性集合
            itemWebRespDTO.setAttrGroupList(selectItemAttrList(itemWebRespDTO.getItemGuid()));
            //sku集合
            List<SkuRespDTO> skuRespList = thisItemSkuDOList.stream().map(sku -> {
                SkuRespDTO skuRespDTO = new SkuRespDTO();
                BeanUtils.copyProperties(sku, skuRespDTO);
                return skuRespDTO;
            }).collect(Collectors.toList());
            itemWebRespDTO.setSkuList(skuRespList);
            // 标签集合
            List<TagEnum> tagList = getTagEnumList(itemWebRespDTO.getIsBestseller(), itemWebRespDTO.getIsNew(), itemWebRespDTO.getIsSign(), itemWebRespDTO.getIsRecommend());

            List<TagRespDTO> tagRespDTOList = MapStructUtils.INSTANCE.tagEnumList2tagRespDTOList(tagList);
            itemWebRespDTO.setTagList(tagRespDTOList);
        }
        return itemWebRespDTOPage;
    }

    private void addAllToSkuDOList(ItemQueryReqDTO itemQueryReqDTO, List<SkuDO> skuDOList) {
        List<SkuDO> pushedSkuList = new ArrayList<>();
        // 从品牌库进入
        inFromBrand(itemQueryReqDTO, skuDOList, pushedSkuList);
        if (!CollectionUtils.isEmpty(pushedSkuList)) {
            skuDOList.addAll(pushedSkuList);
        }
    }

    private void inFromBrand(ItemQueryReqDTO itemQueryReqDTO, List<SkuDO> skuDOList, List<SkuDO> pushedSkuList) {
        if (!StringUtils.isEmpty(itemQueryReqDTO.getBrandGuid())) {
            // 获取上架门店数
            // 品牌库的列表商品的规格GUID集合
            Set<String> skuGuidSet = skuDOList.stream().map(SkuDO::getGuid).collect(Collectors.toSet());
            List<SkuDO> storeSkuList = skuService.list(new LambdaQueryWrapper<SkuDO>()
                    .eq(SkuDO::getIsEnable, true)
                    .in(SkuDO::getParentGuid, skuGuidSet));
            if (!CollectionUtils.isEmpty(storeSkuList)) {
                pushedSkuList.addAll(storeSkuList);
            }
        }
    }

    public Map<String, Long> countItemUseStoreMap(String brandGuid, List<ItemWebRespDTO> itemWebRespDTOList) {
        //商品统计
        if (!ObjectUtils.isEmpty(brandGuid)) {

            final List<String> webRespitemGuidList = itemWebRespDTOList.stream().map(ItemWebRespDTO::getItemGuid).distinct().collect(Collectors.toList());
            return countUseStoreNum(webRespitemGuidList, brandGuid);
        }
        return Collections.emptyMap();
    }

    @Override
    public Page<PadPictureRespDTO> queryPadPicture(PadPictureDTO padPictureDTO) {

        if (StringUtils.isEmpty(padPictureDTO.getStoreGuid())) {
            throw new BusinessException(WRONG_PARAMS);
        }
        IPage<PadPictureRespDTO> pictureRespDTOPage;
        if (StringUtils.isEmpty(padPictureDTO.getPlanGuid())) {
            pictureRespDTOPage = padPictureMapper.queryPadPicture(new PageAdapter<>(padPictureDTO), padPictureDTO);
            pictureRespDTOPage.getRecords().forEach(p -> {
                if (StringUtils.isEmpty(p.getSmallPicture()) && StringUtils.isNotBlank(p.getItemSmallPicture())) {
                    p.setSmallPicture(p.getItemSmallPicture());
                }
            });
        } else {
            pictureRespDTOPage = padPictureMapper.queryPricePlanPadPicture(new PageAdapter<>(padPictureDTO), padPictureDTO);
            pictureRespDTOPage.getRecords().forEach(p -> {
                if (StringUtils.isEmpty(p.getSmallPicture()) && StringUtils.isNotBlank(p.getPlanSmallPicture())) {
                    p.setSmallPicture(p.getPlanSmallPicture());
                }
            });
        }
        return new PageAdapter<>(pictureRespDTOPage, pictureRespDTOPage.getRecords());
    }


    @Override
    public ItemInfoRespDTO getItemInfo(ItemSingleDTO itemSingleDTO) {
        String itemGuid = itemSingleDTO.getData();
        List<ItemDO> itemList = this.list(new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getGuid, itemGuid));
        if (CollectionUtils.isEmpty(itemList)) {
            return null;
        }
        ItemDO itemDO = itemList.get(0);
        ItemInfoRespDTO itemInfoRespDTO = MapStructUtils.INSTANCE.itemDO2itemInfoRespDTO(itemDO);
        List<SkuDO> skuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>().eq(SkuDO::getItemGuid, itemDO.getGuid()));
        List<SkuInfoRespDTO> skuInfoRespDTOList = MapStructUtils.INSTANCE.skuDOList2skuInfoRespDTOList(skuDOList);
        itemInfoRespDTO.setSkuList(skuInfoRespDTOList);
        List<AttrGroupWebRespDTO> attrGroupWebRespDTOList = new ArrayList<>();
        if (itemInfoRespDTO.getHasAttr() != 0) {
            // 获取指定商品下的属性组
            attrGroupWebRespDTOList = selectItemAttrList(itemDO.getGuid());
            if (CollectionUtils.isEmpty(attrGroupWebRespDTOList)) {
                itemInfoRespDTO.setHasAttr(0);
                this.updateHasAttr(itemGuid, itemInfoRespDTO.getHasAttr());
            }
        }
        itemInfoRespDTO.setAttrGroupList(attrGroupWebRespDTOList);

        if (itemInfoRespDTO.getItemType() == 1) {
            List<SubgroupWebRespDTO> subgroupWebRespDTOList = selectSubgroupList(itemGuid);
            itemInfoRespDTO.setSubgroupList(subgroupWebRespDTOList);
        }
        boolean openStockFlag = skuDOList.stream().anyMatch(a -> a.getIsOpenStock() == 1);
        //获取sku是否开启库存 1开启
        if (openStockFlag) {
            //获取商超库存
            try {
                InOutGoodsDTO itemStockInfo = erpFeginService.getItemStock(itemDO.getGuid());
                if (!ObjectUtils.isEmpty(itemStockInfo)) {
                    skuInfoRespDTOList.get(0).setIsOpenStock(itemStockInfo.getIsOpenStock());
                    skuInfoRespDTOList.get(0).setTotalStock(itemStockInfo.getCount());
                    skuInfoRespDTOList.get(0).setSafeStock(itemStockInfo.getSafeNum());
                }
            } catch (RuntimeException ex) {
                log.warn("查询erp库存失败", ex);
            }
        }
        //品牌商品返归已分配门店信息
        //bug:22654，门店自建商品的parentGuid设为本身的guid后，在查询分配门店商品时会查到自己，
        //进而在更新商品时前端在subStoreList有值时会作为分配售卖门店参数传入，造成错误推送，故将查询分配门店逻辑修改
        if (itemDO.getItemFrom() == ItemFromTypeEnum.BRAND_CREATE.getCode()) {
            // 查询门店guid列表
            List<String> storeGuidList = getUseStoreGuidList(itemGuid, itemDO.getBrandGuid());

            if (!CollectionUtils.isEmpty(storeGuidList)) {
                List<StoreDTO> storeDTOS = organizationService.queryStoreByIdList(storeGuidList);
                if (!CollectionUtils.isEmpty(storeDTOS)) {
                    //商品有所属套餐时，商品解绑门店时需判断解绑门店是否有所属套餐
                    checkStorePkg(storeDTOS, itemGuid);
                    itemInfoRespDTO.setSubstoreList(storeDTOS);
                }
            }
        }
        // 菜谱模式下修改商品信息
        if (StringUtils.isNotEmpty(itemSingleDTO.getStoreGuid()) && StringUtils.isNotEmpty(itemSingleDTO.getPlanGuid())) {
            log.info(" 门店guid：{},商品guid：{},方案guid：{}", itemSingleDTO.getStoreGuid(), itemSingleDTO.getData()
                    , itemSingleDTO.getPlanGuid());
            BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(itemSingleDTO.getStoreGuid());
            if (brandDTO.getSalesModel() == SalesModelEnum.RECIPE_MODE.getCode()) {
                List<PricePlanItemDO> planItemDOList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                        .eq(PricePlanItemDO::getItemGuid, itemGuid)
                        .eq(PricePlanItemDO::getPlanGuid, itemSingleDTO.getPlanGuid())
                );
                if (CollectionUtils.isEmpty(planItemDOList)) {
                    log.warn("查询不到方案商品");
                    return itemInfoRespDTO;
                }
                List<String> skuGuidList = planItemDOList.stream()
                        .map(PricePlanItemDO::getSkuGuid)
                        .collect(Collectors.toList());
                PricePlanItemDO planItem = planItemDOList.get(0);
                itemInfoRespDTO.setTypeGuid(planItem.getTypeGuid());
                TypeDO typeDO = typeService.getOne(new LambdaQueryWrapper<TypeDO>()
                        .eq(TypeDO::getGuid, planItem.getTypeGuid()));
                itemInfoRespDTO.setTypeName(typeDO.getName());
                itemInfoRespDTO.setSort(planItem.getSort());
                itemInfoRespDTO.setDescription(planItem.getDescription());

                if (ObjectUtils.isEmpty(planItem.getEnglishBrief())) {
                    itemInfoRespDTO.setEnglishBrief("");
                } else {
                    itemInfoRespDTO.setEnglishBrief(planItem.getEnglishBrief());
                }
                if (ObjectUtils.isEmpty(planItem.getEnglishIngredientsDesc())) {
                    itemInfoRespDTO.setEnglishIngredientsDesc("");
                } else {
                    itemInfoRespDTO.setEnglishIngredientsDesc(planItem.getEnglishIngredientsDesc());
                }
                if (ObjectUtils.isEmpty(planItem.getPictureUrl())) {
                    itemInfoRespDTO.setPictureUrl("");
                } else {
                    itemInfoRespDTO.setPictureUrl(planItem.getPictureUrl());
                }
                if (ObjectUtils.isEmpty(planItem.getBigPictureUrl())) {
                    itemInfoRespDTO.setBigPictureUrl("");
                } else {
                    itemInfoRespDTO.setBigPictureUrl(planItem.getBigPictureUrl());
                }
                if (ObjectUtils.isEmpty(planItem.getDetailBigPictureUrl())) {
                    itemInfoRespDTO.setDetailBigPictureUrl("");
                } else {
                    itemInfoRespDTO.setDetailBigPictureUrl(planItem.getDetailBigPictureUrl());
                }
                itemInfoRespDTO.setName(planItem.getPlanItemName());

                List<SkuInfoRespDTO> skuList = itemInfoRespDTO.getSkuList();
                skuList.removeIf(sku -> !skuGuidList.contains(sku.getSkuGuid()));
                planItemDOList.forEach(plan ->
                        skuList.forEach(sku -> {
                            if (Objects.equals(sku.getSkuGuid(), plan.getSkuGuid())) {
                                sku.setMemberPrice(plan.getMemberPrice());
                                sku.setSalePrice(plan.getSalePrice());
                                sku.setAccountingPrice(plan.getAccountingPrice());
                                sku.setTakeawayAccountingPrice(plan.getTakeawayAccountingPrice());
                                sku.setIsPkgItem(plan.getIsPkgItem());
                                sku.setIsRack(plan.getIsSoldOut() == 0 ? 1 : 0);
                            }
                        })
                );
            }
        }
        return itemInfoRespDTO;
    }

    private List<String> getUseStoreGuidList(String itemGuid, String brandGuid) {
        List<ItemDO> itemDOS = this.list(new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getParentGuid, itemGuid)
                .eq(ItemDO::getIsEnable, Boolean.TRUE)
                .eq(ItemDO::getItemFrom, ItemFromTypeEnum.BRAND_PUSH.getCode()));
        List<String> storeGuidList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemDOS)) {
            storeGuidList = itemDOS.stream().map(ItemDO::getStoreGuid).collect(Collectors.toList());
        }

        BrandDTO brandDTO = organizationService.queryBrandByGuid(brandGuid);
        if (brandDTO.getSalesModel() == SalesModelEnum.RECIPE_MODE.getCode()) {
            // 菜谱方案的门店
            List<PricePlanItemDO> planItemList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                    .eq(PricePlanItemDO::getItemGuid, itemGuid)
                    .eq(PricePlanItemDO::getIsDelete, 0)
            );
            List<String> planGuidList = planItemList.stream()
                    .map(PricePlanItemDO::getPlanGuid)
                    .distinct()
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(planGuidList)) {
                List<PricePlanStoreDO> planStoreList = pricePlanStoreService.list(new LambdaQueryWrapper<PricePlanStoreDO>()
                        .in(PricePlanStoreDO::getPlanGuid, planGuidList)
                );
                if (!CollectionUtils.isEmpty(planStoreList)) {
                    List<String> storeList = planStoreList.stream()
                            .map(PricePlanStoreDO::getStoreGuid)
                            .distinct()
                            .collect(Collectors.toList());
                    storeGuidList.addAll(storeList);
                }
            }
        }
        return storeGuidList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 计算品牌 使用门店数量
     *
     * @param itemGuids
     * @return
     */
    private Map<String, Long> countUseStoreNum(List<String> itemGuids, String brandGuid) {
        List<ItemDO> itemDOS = this.list(new LambdaQueryWrapper<ItemDO>()
                .select(BasePushDO::getStoreGuid, BasePushDO::getParentGuid)
                .in(ItemDO::getParentGuid, itemGuids)
                .eq(ItemDO::getIsEnable, Boolean.TRUE)
                .eq(ItemDO::getItemFrom, ItemFromTypeEnum.BRAND_PUSH.getCode()));

        Map<String, Long> itemStoreNumMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(itemDOS)) {
            //统计商品所在门店数量
            itemStoreNumMap = itemDOS.stream().collect(Collectors.groupingBy(ItemDO::getParentGuid, Collectors.counting()));
        }


        BrandDTO brandDTO = organizationService.queryBrandByGuid(brandGuid);
        if (brandDTO.getSalesModel() == SalesModelEnum.RECIPE_MODE.getCode()) {
            //统计商品所在菜谱下门店
            final List<CountItemDTO> countItemDTOS = planItemMapper.countUseStoreNum(itemGuids);
            if (CollUtil.isEmpty(countItemDTOS)) {
                return itemStoreNumMap;
            }
            for (CountItemDTO itemDTO : countItemDTOS) {
                Long num = itemStoreNumMap.getOrDefault(itemDTO.getItem(), 0L);
                //累加数量
                num += itemDTO.getNum();
                itemStoreNumMap.put(itemDTO.getItem(), num);
            }
        }
        return itemStoreNumMap;
    }

    @Override
    public List<String> getItemInfoList2(List<String> itemGuidList) {
        List<ItemDO> list = this.list(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getGuid, itemGuidList));
        List<String> itemInfoRespDTOList = new ArrayList<>();
        for (ItemDO itemDO : list) {
            String parentGuid = itemDO.getParentGuid();
            if (StringUtils.isNotBlank(parentGuid)) {
                itemInfoRespDTOList.add(parentGuid);
            }

        }
        return itemInfoRespDTOList;
    }

    private List<ItemInfoRespDTO> getItemInfoListNew(List<String> itemGuidList) {
        List<ItemDO> itemDOList;
        itemDOList = itemMapper.findByGuids(itemGuidList);
        List<ItemInfoRespDTO> itemInfoRespDTOList = MapStructUtils.INSTANCE.itemDOList2ItemInfoRespDTOList(itemDOList);
        itemInfoRespDTOList = itemInfoRespDTOList
                .stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ItemInfoRespDTO::getItemGuid))), ArrayList::new));

        log.info("itemInfoRespDTOList={}", JacksonUtils.writeValueAsString(itemInfoRespDTOList));
        List<String> typeList = itemInfoRespDTOList.stream().map(ItemInfoRespDTO::getTypeGuid).collect(Collectors.toList());
        Map<String, ItemInfoTypeRespDTO> infoTypeRespDTOMap = getStringItemInfoTypeRespDTOMap(UserContextUtils.getStoreGuid(), typeList, 1);
        log.info("infoTypeRespDTOMap={}", JacksonUtils.writeValueAsString(infoTypeRespDTOMap));
        //查询商品sku集合并放到返回实体里
        for (ItemInfoRespDTO itemInfoRespDTO : itemInfoRespDTOList) {
            ItemInfoTypeRespDTO itemInfoTypeRespDTO = infoTypeRespDTOMap.get(itemInfoRespDTO.getTypeGuid());
            log.info("普通模式key获取{}", JacksonUtils.writeValueAsString(itemInfoTypeRespDTO));
            if (ObjectUtils.isEmpty(itemInfoTypeRespDTO)) {
                itemInfoRespDTO.setTypeName(null);
                continue;
            }
            itemInfoRespDTO.setTypeName(itemInfoTypeRespDTO.getName());
            itemInfoRespDTO.setTypeSort(itemInfoTypeRespDTO.getSort());
        }
        return itemInfoRespDTOList;
    }

    @Override
    public List<ItemInfoRespDTO> getItemInfoList(List<String> itemGuidList, boolean hasDeleted) {
        List<ItemDO> itemDOList;
        if (hasDeleted) {
            itemDOList = itemMapper.findByGuids(itemGuidList);
        } else {
            itemDOList = itemMapper.selectBatchIds(itemGuidList);
        }
        List<ItemInfoRespDTO> itemInfoRespDTOList = MapStructUtils.INSTANCE.itemDOList2ItemInfoRespDTOList(itemDOList);
        //查询商品sku集合并放到返回实体里
        for (ItemInfoRespDTO itemInfoRespDTO : itemInfoRespDTOList) {
            List<SkuDO> skuDOList;
            if (hasDeleted) {
                skuDOList = skuService.findByItemGuid(itemInfoRespDTO.getItemGuid());
            } else {
                skuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>()
                        .eq(itemInfoRespDTO.getItemGuid() != null, SkuDO::getItemGuid, itemInfoRespDTO.getItemGuid()));
            }
            List<SkuInfoRespDTO> skuInfoRespDTOList = MapStructUtils.INSTANCE.skuDOList2skuInfoRespDTOList(skuDOList);
            if (CollectionUtils.isEmpty(skuInfoRespDTOList)) {
                itemInfoRespDTO.setSkuList(null);
                continue;
            }
            skuInfoRespDTOList.forEach(sku -> {
                sku.setPictureUrl(itemInfoRespDTO.getPictureUrl());
                if (StringUtils.isNotEmpty(sku.getStoreGuid()) && StringUtils.isEmpty(sku.getParentGuid())) {
                    sku.setParentGuid(sku.getSkuGuid());
                }
            });
            itemInfoRespDTO.setSkuList(skuInfoRespDTOList);
            TypeDO typeDO = typeService.getOne(new LambdaQueryWrapper<TypeDO>()
                    .eq(TypeDO::getGuid, itemInfoRespDTO.getTypeGuid()));
            if (ObjectUtils.isEmpty(typeDO)) {
                itemInfoRespDTO.setTypeName(null);
                continue;
            }
            itemInfoRespDTO.setTypeName(typeDO.getName());
        }
        return itemInfoRespDTOList;
    }

    @NotNull
    private Map<String, ItemInfoTypeRespDTO> getStringItemInfoTypeRespDTOMap(String storeGuid, List<String> typeList, Integer isModel) {
        TypeSingleDTO typeSingleDTO = new TypeSingleDTO();
        typeSingleDTO.setItemTypeList(typeList);
        typeSingleDTO.setIsModel(isModel);
        typeSingleDTO.setStoreGuid(storeGuid);
        log.info("请求获取商品分类入参，typeSingleDTO={}", JacksonUtils.writeValueAsString(typeSingleDTO));
        return this.queryStoreItemType(typeSingleDTO)
                .stream()
                .collect(Collectors.toMap(ItemInfoTypeRespDTO::getTypeGuid, ItemInfoTypeRespDTO -> ItemInfoTypeRespDTO));
    }

    @Override
    public Boolean updateItemSort(ItemSortUpdateReqDTO itemSortUpdateReqDTO) {
        Integer changeSort = itemSortUpdateReqDTO.getSort();
        String changeItemGuid = itemSortUpdateReqDTO.getItemGuid();
        if (!ObjectUtils.isEmpty(itemSortUpdateReqDTO.getTypeGuid())) {
            List<ItemDO> itemDOList = this.list(new LambdaQueryWrapper<ItemDO>()
                    .eq(ItemDO::getTypeGuid, itemSortUpdateReqDTO.getTypeGuid())
                    .eq(ItemDO::getIsDelete, 0)
                    .eq(ItemDO::getIsEnable, 1)
                    .orderByAsc(ItemDO::getSort)
                    .orderByDesc(ItemDO::getGmtCreate)
            );

            List<ItemDO> updateItemList = new ArrayList<>();
            if (changeSort < itemDOList.size()) {
                // 删除本身
                itemDOList.removeIf(i -> Objects.equals(i.getGuid(), changeItemGuid));

                // 修改前面的
                for (int i = 0; i < changeSort - 1; i++) {
                    ItemDO itemDO = itemDOList.get(i);
                    itemDO.setSort(i + 1);
                }

                // 修改本身
                ItemDO itemDO = this.getOne(new LambdaQueryWrapper<ItemDO>()
                        .eq(ItemDO::getGuid, changeItemGuid)
                );
                itemDO.setSort(changeSort);
                updateItemList.add(itemDO);

                // 修改后面的
                for (int i = changeSort - 1; i < itemDOList.size(); i++) {
                    ItemDO item = itemDOList.get(i);
                    item.setSort(i + 2);
                }
                updateItemList.addAll(itemDOList);
            } else {
                // 删除本身
                itemDOList.removeIf(i -> Objects.equals(i.getGuid(), changeItemGuid));

                // 修改前面的
                for (int i = 0; i < itemDOList.size(); i++) {
                    ItemDO itemDO = itemDOList.get(i);
                    itemDO.setSort(i + 1);
                }
                updateItemList.addAll(itemDOList);

                // 修改本身
                ItemDO itemDO = this.getOne(new LambdaQueryWrapper<ItemDO>()
                        .eq(ItemDO::getGuid, changeItemGuid)
                );
                itemDO.setSort(changeSort);
                updateItemList.add(itemDO);
            }

            return this.updateBatchById(updateItemList);
        }

        ItemDO itemDO = new ItemDO();
        itemDO.setSort(changeSort);
        // 修该品牌库排序
        if (itemSortUpdateReqDTO.getFrom().equals(ModuleEntranceEnum.BRAND.code())) {
            // 品牌修改的排序同步到门店该商品的排序  即更新item表中parent_guid为itemDO.getGuid()的商品的sort
            return this.update(itemDO, new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getGuid, changeItemGuid)
                    .or().eq(ItemDO::getParentGuid, changeItemGuid));
        }
        return this.update(itemDO, new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getGuid, changeItemGuid));
    }

    /**
     * 菜谱MQ推送
     *
     * @param storeAndItem storeAndItem
     * @return Integer
     */
    @Override
    @Transactional
    public Integer pushItemsByPrice(SyncStoreAndItemDTO storeAndItem) {

        if (CollectionUtils.isEmpty(storeAndItem.getStoreGuidList())) {
            itemMapper.correctStoreItemByPlanPriceGuid(new ArrayList<>(0), storeAndItem.getPricePlanGuid());
            itemMapper.correctStoreSkuByPlanPriceGuid(new ArrayList<>(0), storeAndItem.getPricePlanGuid());
            return 1;
        }
        PushItemParam pushItemParam = new PushItemParam();
        pushItemParam.setPricePlanGuid(storeAndItem.getPricePlanGuid());
        // 被推送规格的GUID集合
        List<String> skuGuidList = storeAndItem.getItemList().stream().map(SyncItemDTO::getSkuGuid).distinct().collect(Collectors.toList());

        Map<String, List<SyncItemDTO>> skuMap = storeAndItem.getItemList().stream().collect(Collectors.groupingBy(SyncItemDTO::getSkuGuid));
        // 被推送门店的GUID集合
        List<String> storeGuidList = storeAndItem.getStoreGuidList();
        pushItemParam.setStoreGuidList(storeGuidList);
        // 被推送门店的商品集合
        List<ItemSynRespDTO> itemSynRespDTOList = listItemSynRespDTOByIds(skuGuidList, storeGuidList);
        pushItemParam.setItemSynRespDTOList(itemSynRespDTOList);
        // 获取品牌库推送的分类集合
        List<TypeDO> typeListFromBrand = selectTypeListFromBrand(itemSynRespDTOList);
        pushItemParam.setTypeListFromBrand(typeListFromBrand);
        // 获取品牌库推送的规格集合
        List<SkuDO> skuListFromBrand = selectSkuListFromBrand(itemSynRespDTOList);
        skuListFromBrand.forEach(skuDO -> {
            List<SyncItemDTO> pricePlanSkuList = skuMap.get(skuDO.getGuid());
            if (CollectionUtils.isEmpty(pricePlanSkuList)) {
                return;
            }
            SyncItemDTO pricePlanSku = pricePlanSkuList.get(0);
            BeanUtils.copyProperties(pricePlanSku, skuDO);
            skuDO.setPricePlanGuid(storeAndItem.getPricePlanGuid());
        });
        pushItemParam.setSkuListFromBrand(skuListFromBrand);
        // 获取品牌库推送的属性组集合
        List<AttrGroupDO> attrGroupListFromBrand = selectAttrGroupListFromBrand(itemSynRespDTOList);
        pushItemParam.setAttrGroupListFromBrand(attrGroupListFromBrand);
        // 获取品牌库推送的属性集合
        List<AttrDO> attrListFromBrand;
        if (CollectionUtils.isEmpty(attrGroupListFromBrand)) {
            attrListFromBrand = new ArrayList<>();
        } else {
            attrListFromBrand = selectAttrListFromBrand(itemSynRespDTOList);
        }
        pushItemParam.setAttrListFromBrand(attrListFromBrand);
        // 获取品牌库推送的套餐下分组集合
        List<SubgroupDO> subgroupListFromBrand = selectSubgroupListFromBrand(itemSynRespDTOList);
        pushItemParam.setSubgroupListFromBrand(subgroupListFromBrand);

        int ret = pushItemV2(pushItemParam);
        if (ret != 1) {
            return ret;
        }
        List<String> needCorrectItemGuidList = itemSynRespDTOList.stream().map(ItemSynRespDTO::getItemGuid).collect(Collectors.toList());
        List<String> needCorrectSkuGuidList = skuListFromBrand.stream().map(SkuDO::getGuid).collect(Collectors.toList());
        //设置item
        itemMapper.updateStoreItemByPlanPriceGuid(needCorrectItemGuidList, storeGuidList, storeAndItem.getPricePlanGuid());

        itemMapper.correctStoreItemIsEnable(needCorrectItemGuidList, storeGuidList);
        itemMapper.correctStoreSkuIsEnable(needCorrectSkuGuidList, storeGuidList);

        itemMapper.correctStoreItemByPlanPriceGuid(storeGuidList, storeAndItem.getPricePlanGuid());
        itemMapper.correctStoreSkuByPlanPriceGuid(storeGuidList, storeAndItem.getPricePlanGuid());

        return ret;
    }

    @Transactional
    @Override
    public Integer pushItems(PushItemReqDTO pushItemReqDTO, boolean isNeedCorrect) {
        log.info("商品推送入参,pushItemReqDTO:{}, isNeedCorrect:{}", JacksonUtils.writeValueAsString(pushItemReqDTO), isNeedCorrect);
        PushItemParam pushItemParam = new PushItemParam();
        // 是否推送更新子商品
        pushItemParam.setIsUpdateSubItem(pushItemReqDTO.getIsUpdateSubItem());
        // 被推送规格的GUID集合
        List<String> skuGuidList = pushItemReqDTO.getSkuGuidList();
        // 被推送门店的GUID集合
        List<String> storeGuidList = pushItemReqDTO.getStoreGuidList();
        pushItemParam.setStoreGuidList(storeGuidList);
        validatePushItemDTO(pushItemReqDTO);
        List<ItemSynRespDTO> itemSynRespDTOList = listItemSynRespDTOByIds(skuGuidList, storeGuidList);
        pushItemParam.setItemSynRespDTOList(itemSynRespDTOList);
        // 获取品牌库推送的分类集合
        List<TypeDO> typeListFromBrand = selectTypeListFromBrand(itemSynRespDTOList);
        pushItemParam.setTypeListFromBrand(typeListFromBrand);
        // 获取品牌库推送的规格集合
        List<SkuDO> skuListFromBrand = selectSkuListFromBrand(itemSynRespDTOList);
        pushItemParam.setSkuListFromBrand(skuListFromBrand);
        // 获取品牌库推送的属性组集合
        List<AttrGroupDO> attrGroupListFromBrand = selectAttrGroupListFromBrand(itemSynRespDTOList);
        pushItemParam.setAttrGroupListFromBrand(attrGroupListFromBrand);
        // 获取品牌库推送的属性集合
        List<AttrDO> attrListFromBrand;
        if (CollectionUtils.isEmpty(attrGroupListFromBrand)) {
            attrListFromBrand = new ArrayList<>();
        } else {
            attrListFromBrand = selectAttrListFromBrand(itemSynRespDTOList);
        }
        pushItemParam.setAttrListFromBrand(attrListFromBrand);
        // 获取品牌库推送的套餐下分组集合
        List<SubgroupDO> subgroupListFromBrand = selectSubgroupListFromBrand(itemSynRespDTOList);
        pushItemParam.setSubgroupListFromBrand(subgroupListFromBrand);

        int ret = pushItemV2(pushItemParam);
        if (ret != 1) {
            return ret;
        }
        if (isNeedCorrect && !CollectionUtils.isEmpty(skuGuidList)) {
            List<String> needCorrectItemGuidList = itemSynRespDTOList.stream().filter(i -> {
                for (SkuSynRespDTO sku : i.getSkuList()) {
                    if (skuGuidList.contains(sku.getSkuGuid())) {
                        return true;
                    }
                }
                return false;
            }).map(ItemSynRespDTO::getItemGuid).collect(Collectors.toList());
            itemMapper.correctStoreItemIsEnableByBrandPush(needCorrectItemGuidList, storeGuidList);
            itemMapper.correctStoreSkuIsEnableByBrandPush(skuGuidList, storeGuidList);
        }
        return ret;
    }

    public Integer pushItemV2(PushItemParam pushItemParam) {
        log.info("商品推送入参V2,pushItemParam:{}", JacksonUtils.writeValueAsString(pushItemParam));
        // 被推送门店的GUID集合
        List<String> storeGuidList = pushItemParam.getStoreGuidList();
        List<ItemSynRespDTO> itemSynRespDTOList = pushItemParam.getItemSynRespDTOList();
        // 获取品牌库推送的分类集合
        List<TypeDO> typeListFromBrand = pushItemParam.getTypeListFromBrand();
        // 获取品牌库推送的规格集合
        List<SkuDO> skuListFromBrand = pushItemParam.getSkuListFromBrand();
        // 获取品牌库推送的属性组集合
        List<AttrGroupDO> attrGroupListFromBrand = pushItemParam.getAttrGroupListFromBrand();
        // 获取品牌库推送的属性集合
        List<AttrDO> attrListFromBrand = pushItemParam.getAttrListFromBrand();
        // 获取品牌库推送的套餐下分组集合
        List<SubgroupDO> subgroupListFromBrand = pushItemParam.getSubgroupListFromBrand();
        // 获取门店对应实体
        // 获取各门店对应的分类实体集合
        List<TypeDO> typeListFromStore = selectTypeListFromStore(typeListFromBrand, storeGuidList);
        // 获取各门店对应的商品实体集合
        List<ItemDO> itemListFromStore = selectItemListFromStore(itemSynRespDTOList, storeGuidList);

        // 获取各门店对应的规格集合
        List<SkuDO> skuListFromStore = selectSkuListFromStore(pushItemParam.getIsUpdateSubItem(), skuListFromBrand,
                subgroupListFromBrand, storeGuidList);

        // 获取各门店对应的属性组集合
        List<AttrGroupDO> attrGroupListFromStore;
        if (CollectionUtils.isEmpty(attrGroupListFromBrand)) {
            attrGroupListFromStore = new ArrayList<>();
        } else {
            attrGroupListFromStore = selectAttrGroupListFromStore(attrGroupListFromBrand, storeGuidList);
        }
        // 获取各门店对应的属性集合
        List<AttrDO> attrListFromStore;
        if (CollectionUtils.isEmpty(attrListFromBrand)) {
            attrListFromStore = new ArrayList<>();
        } else {
            attrListFromStore = selectAttrListFromStore(attrListFromBrand, storeGuidList);
        }
        // 获取各门店对应的套餐下分组集合
        List<SubgroupDO> subgroupListFromStore;
        if (!CollectionUtils.isEmpty(subgroupListFromBrand)) {
            subgroupListFromStore = selectSubgroupListFromStore(subgroupListFromBrand, storeGuidList);
        } else {
            subgroupListFromStore = new ArrayList<>();
        }
        // 按门店分组的规格集合
        Map<String, List<SkuDO>> storeGroupSkuMap = skuListFromStore.stream()
                .collect(Collectors.groupingBy(SkuDO::getStoreGuid));
        // 按门店分组的属性组集合
        Map<String, List<AttrGroupDO>> storeGroupAttrGroupMap = new HashMap<>();
        // 按门店分组的属性集合
        Map<String, List<AttrDO>> storeGroupAttrMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(attrGroupListFromStore)) {
            Map<String, List<AttrGroupDO>> attrGroupMap = attrGroupListFromStore.stream()
                    .collect(Collectors.groupingBy(BasePushDO::getStoreGuid));
            storeGroupAttrGroupMap.putAll(attrGroupMap);
            Map<String, List<AttrDO>> attrMap = attrListFromStore.stream()
                    .collect(Collectors.groupingBy(BasePushDO::getStoreGuid));
            storeGroupAttrMap.putAll(attrMap);
        }
        // 按门店分组的分组集合
        Map<String, List<SubgroupDO>> storeSubgroupMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(subgroupListFromStore)) {
            Map<String, List<SubgroupDO>> subgroupMap = subgroupListFromStore.stream()
                    .collect(Collectors.groupingBy(SubgroupDO::getStoreGuid));
            storeSubgroupMap.putAll(subgroupMap);
        }

        // 按门店分组的商品集合
        Map<String, List<ItemDO>> storeGroupItemMap = itemListFromStore.stream()
                .collect(Collectors.groupingBy(BasePushDO::getStoreGuid));
        // 按门店分组的分类集合
        Map<String, List<TypeDO>> storeGroupTypeMap = typeListFromStore.stream()
                .collect(Collectors.groupingBy(BasePushDO::getStoreGuid));
        List<SkuDO> insertSkuDOList = new ArrayList<>();
        List<AttrDO> insertAttrDOList = new ArrayList<>();
        List<RItemAttrGroupDO> insertItemAttrDOList = new ArrayList<>();
        List<RAttrItemAttrGroupDO> insertAttrItemAttrGroupDOList = new ArrayList<>();
        List<SubgroupDO> insertSubgroupDOList = new ArrayList<>();
        List<SubgroupDO> alreadyUpdateSubgroupDOList = new ArrayList<>();
        List<RSkuSubgroupDO> insertSkuSubgroupDOList = new ArrayList<>();
        List<ItemDO> insertItemDOList = new ArrayList<>();
        List<ItemDO> updateItemDOList = new ArrayList<>();

        storeGuidList.forEach(storeGuid -> {
            List<RSkuSubgroupDO> thisStoreInsertSkuSubgroupDOList = new ArrayList<>();
            Map<String, List<TypeDO>> typeMap = storeGroupTypeMap.get(storeGuid).stream()
                    .collect(Collectors.groupingBy(BasePushDO::getParentGuid));
            Map<String, List<ItemDO>> itemMap = storeGroupItemMap.get(storeGuid).stream()
                    .collect(Collectors.groupingBy(BasePushDO::getParentGuid));
            Map<String, List<SkuDO>> skuMap = storeGroupSkuMap.get(storeGuid).stream()
                    .collect(Collectors.groupingBy(SkuDO::getParentGuid));
            List<AttrGroupDO> attrGroupDOS = storeGroupAttrGroupMap.get(storeGuid);
            List<AttrDO> attrDOS = storeGroupAttrMap.get(storeGuid);
            List<SubgroupDO> subgroupDOS = storeSubgroupMap.get(storeGuid);
            itemSynRespDTOList.forEach(itemSynRespDTO -> {
                ItemDO thisItem = itemMap.get(itemSynRespDTO.getItemGuid()).get(0);
                if (typeMap.get(itemSynRespDTO.getTypeGuid()) == null) {
                    log.warn("分类集合为空 typeGuid={}", itemSynRespDTO.getTypeGuid());
                }
                TypeDO typeDO = typeMap.get(itemSynRespDTO.getTypeGuid()).get(0);
                if (StringUtils.isEmpty(thisItem.getTypeGuid())) {
                    insertItemDOList.add(thisItem);
                } else {
                    updateItemDOList.add(thisItem);
                }
                thisItem.setTypeGuid(typeDO.getGuid());
                List<SkuSynRespDTO> skuList = itemSynRespDTO.getSkuList();
                for (int i = 0; i < skuList.size(); i++) {
                    SkuSynRespDTO skuSynRespDTO = skuList.get(i);
                    SkuDO skuDO = skuMap.get(skuSynRespDTO.getSkuGuid()).get(0);
                    //bugfix:21578
                    if (skuDO.getIsRack() != null) {
                        if (skuDO.getIsRack() == 0) {
                            skuDO.setIsJoinMiniAppTakeaway(0);
                            skuDO.setIsJoinStore(0);
                        }
                        if (skuDO.getIsRack() == 1) {
                            skuDO.setIsJoinMiniAppTakeaway(1);
                            skuDO.setIsJoinStore(1);
                        }
                    }
                    if (StringUtils.isEmpty(skuDO.getItemGuid())) {
                        skuDO.setItemGuid(thisItem.getGuid());
                        // 设置skuGuid
                        int skuCount = skuMapper.skuCount(thisItem.getGuid());
                        int count = skuCount + i + 1;
                        if (count > 99) {
                            throw new BusinessException("规格编号已达到上限不可新建");
                        }
                        //美团、饿了吗等外卖绑定sku只支持数字+字母
                        String itemGuid = thisItem.getGuid().replace("-", "");
                        if (count < 10) {
                            skuDO.setGuid(itemGuid + "0" + count);
                        } else {
                            skuDO.setGuid(itemGuid + count);
                        }
                        insertSkuDOList.add(skuDO);
                    }
                }
                if (itemSynRespDTO.getItemType() != 1 && itemSynRespDTO.getHasAttr() != 0) {
                    List<AttrGroupSynRespDTO> attrGroupList = itemSynRespDTO.getAttrGroupList();
                    for (AttrGroupSynRespDTO attrGroupSynRespDTO : attrGroupList) {
                        AttrGroupDO thisAttrGroup = attrGroupDOS.stream()
                                .filter(attrGroupDO -> attrGroupSynRespDTO.getAttrGroupGuid().equals(attrGroupDO.getParentGuid()))
                                .findFirst().orElse(null);
                        RItemAttrGroupDO itemAttrGroupDO = new RItemAttrGroupDO();
                        itemAttrGroupDO.setItemGuid(thisItem.getGuid());
                        itemAttrGroupDO.setAttrGroupGuid(thisAttrGroup.getGuid());
                        itemAttrGroupDO.setIsRequired(attrGroupSynRespDTO.getIsRequired());
                        itemAttrGroupDO.setWithDefault(attrGroupSynRespDTO.getWithDefault());
                        itemAttrGroupDO.setIsMultiChoice(attrGroupSynRespDTO.getIsMultiChoice());
                        try {
                            itemAttrGroupDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
                        } catch (IOException e) {
                            throw new BusinessException("BatchIdGenerator生成商品guid失败");
                        }
                        insertItemAttrDOList.add(itemAttrGroupDO);
                        List<AttrSynRespDTO> attrList = attrGroupSynRespDTO.getAttrList();
                        for (AttrSynRespDTO attrSynRespDTO : attrList) {
                            AttrDO thisAttr = attrDOS.stream()
                                    .filter(attrDO -> attrSynRespDTO.getAttrGuid().equals(attrDO.getParentGuid()))
                                    .findFirst().orElse(null);
                            thisAttr.setIsDefault(attrSynRespDTO.getIsDefault());
                            if (StringUtils.isEmpty(thisAttr.getAttrGroupGuid())) {
                                thisAttr.setAttrGroupGuid(thisAttrGroup.getGuid());
                                insertAttrDOList.add(thisAttr);
                            }
                            RAttrItemAttrGroupDO attrItemAttrGroupDO = new RAttrItemAttrGroupDO();
                            attrItemAttrGroupDO.setItemAttrGroupGuid(itemAttrGroupDO.getGuid());
                            try {
                                attrItemAttrGroupDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
                            } catch (IOException e) {
                                throw new BusinessException("BatchIdGenerator生成商品guid失败");
                            }
                            attrItemAttrGroupDO.setIsDefault(attrSynRespDTO.getIsDefault());
                            attrItemAttrGroupDO.setAttrGuid(thisAttr.getGuid());
                            insertAttrItemAttrGroupDOList.add(attrItemAttrGroupDO);
                        }
                    }
                }
                if (itemSynRespDTO.getItemType() == 1) {
                    List<SubgroupSynRespDTO> subgroupList = itemSynRespDTO.getSubgroupList();
                    for (SubgroupSynRespDTO subgroupSynRespDTO : subgroupList) {
                        SubgroupDO thisSubgroup = subgroupDOS.stream()
                                .filter(subgroupDO -> subgroupSynRespDTO.getSubgroupGuid().equals(subgroupDO.getParentGuid()))
                                .findFirst().orElse(null);
                        if (StringUtils.isEmpty(thisSubgroup.getItemGuid())) {
                            thisSubgroup.setItemGuid(thisItem.getGuid());
                            insertSubgroupDOList.add(thisSubgroup);
                        } else {
                            alreadyUpdateSubgroupDOList.add(thisSubgroup);
                        }
                        List<SubItemSkuSynRespDTO> subItemSkuList = subgroupSynRespDTO.getSubItemSkuList();
                        for (SubItemSkuSynRespDTO subItemSkuSynRespDTO : subItemSkuList) {
                            RSkuSubgroupDO skuSubgroupDO = new RSkuSubgroupDO();
                            try {
                                skuSubgroupDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
                            } catch (IOException e) {
                                throw new BusinessException("BatchIdGenerator生成商品guid失败");
                            }
                            skuSubgroupDO.setSubgroupGuid(thisSubgroup.getGuid());
                            skuSubgroupDO.setAddPrice(subItemSkuSynRespDTO.getAddPrice());
                            skuSubgroupDO.setIsRepeat(subItemSkuSynRespDTO.getIsRepeat());
                            skuSubgroupDO.setIsDefault(subItemSkuSynRespDTO.getDefaultNum());
                            skuSubgroupDO.setItemNum(subItemSkuSynRespDTO.getItemNum());
                            skuSubgroupDO.setSort(subItemSkuSynRespDTO.getSort());
                            // 此处存储的是品牌库对应商品的GUID，会在本方法范围内靠后换成门店库对应的商品GUID。
                            skuSubgroupDO.setItemGuid(subItemSkuSynRespDTO.getItemGuid());
                            // 此处存储的是品牌库对应规格的GUID，会在本方法范围内靠后换成门店库对应的规格GUID。
                            skuSubgroupDO.setSkuGuid(subItemSkuSynRespDTO.getSkuGuid());
                            thisStoreInsertSkuSubgroupDOList.add(skuSubgroupDO);
                        }
                    }
                }
            });

            for (RSkuSubgroupDO skuSubgroupDO : thisStoreInsertSkuSubgroupDOList) {
                // 纠正itemGuid以及skuGuid字段
                SkuDO skuDO = skuMap.get(skuSubgroupDO.getSkuGuid()).get(0);
                skuSubgroupDO.setItemGuid(skuDO.getItemGuid());
                skuSubgroupDO.setSkuGuid(skuDO.getGuid());
            }
            if (!CollectionUtils.isEmpty(thisStoreInsertSkuSubgroupDOList)) {
                insertSkuSubgroupDOList.addAll(thisStoreInsertSkuSubgroupDOList);
            }

        });
        // 删除门店对应商品历史关联的属性组以及属性的中间关联实体
        Set<String> relateStoreItemGuidList = new HashSet<>();
        if (!CollectionUtils.isEmpty(insertItemDOList)) {
            Set<String> insertItemGuidSet = insertItemDOList.stream().map(ItemDO::getGuid).collect(Collectors.toSet());
            relateStoreItemGuidList.addAll(insertItemGuidSet);
        }
        if (!CollectionUtils.isEmpty(updateItemDOList)) {
            Set<String> updateItemGuidSet = updateItemDOList.stream().map(ItemDO::getGuid).collect(Collectors.toSet());
            relateStoreItemGuidList.addAll(updateItemGuidSet);
        }
        //移除指定套餐下分组与单品属性
        removeRelateStoreItemList(relateStoreItemGuidList);
        // 移除以前指定套餐下分组与单品的关联关系
        removeOldSkuSubgroup(updateItemDOList);

        if (!CollectionUtils.isEmpty(insertSkuDOList)) {
            log.info("推送保存的实体信息：{}", JacksonUtils.writeValueAsString(insertSkuDOList));
            insertSkuDOList.sort(Comparator.comparing(SkuDO::getParentGuid));
            skuService.saveBatch(insertSkuDOList, insertSkuDOList.size());
        }
        if (!CollectionUtils.isEmpty(insertAttrDOList)) {
            attrService.saveBatch(insertAttrDOList, insertAttrDOList.size());
        }
        if (!CollectionUtils.isEmpty(insertItemAttrDOList)) {
            itemAttrGroupService.saveBatch(insertItemAttrDOList, insertItemAttrDOList.size());
        }
        if (!CollectionUtils.isEmpty(insertAttrItemAttrGroupDOList)) {
            attrItemAttrGroupService.saveBatch(insertAttrItemAttrGroupDOList, insertAttrItemAttrGroupDOList.size());
        }
        if (!CollectionUtils.isEmpty(insertSubgroupDOList)) {
            subgroupService.saveBatch(insertSubgroupDOList, insertSubgroupDOList.size());
        }
        if (!CollectionUtils.isEmpty(insertSkuSubgroupDOList)) {
            skuSubgroupService.saveBatch(insertSkuSubgroupDOList, insertSkuSubgroupDOList.size());
        }
        if (!CollectionUtils.isEmpty(insertItemDOList)) {
            saveBatch(insertItemDOList, insertItemDOList.size());
        }
        if (!CollectionUtils.isEmpty(updateItemDOList)) {
            updateBatchById(updateItemDOList, updateItemDOList.size());
        }

        // 删除被推送门店套餐的历史无用分组
        removeOtherSubgroupsOfItem(insertSubgroupDOList, alreadyUpdateSubgroupDOList);
        // 此次推送的商品名称集合
        List<String> itemNames = itemListFromStore.stream().map(ItemDO::getName).collect(Collectors.toList());
        // 此次推送的分类名称集合
        List<String> typeNames = typeListFromStore.stream().map(TypeDO::getName).collect(Collectors.toList());
        // 更新门店的同名实体（其中，对商品以及分类的同名处理，因为门店的批量导入功能依然可能导致重名，所以只对因为分配售卖门店功能产生的重名实体修改名称为X自建）
        updateDuplicateNameInStore(storeGuidList, itemNames, typeNames);
        return 1;
    }

    private void removeRelateStoreItemList(Collection<String> relateStoreItemGuidList) {
        if (!CollectionUtils.isEmpty(relateStoreItemGuidList)) {
            List<RItemAttrGroupDO> itemAttrGroupDOS = itemAttrGroupService.list(new LambdaQueryWrapper<RItemAttrGroupDO>().in(RItemAttrGroupDO::getItemGuid, relateStoreItemGuidList));
            if (!CollectionUtils.isEmpty(itemAttrGroupDOS)) {
                Set<String> itemAttrGroupGuidSet = itemAttrGroupDOS.stream().map(RItemAttrGroupDO::getGuid).collect(Collectors.toSet());
                itemAttrGroupService.removeByIds(itemAttrGroupGuidSet);
                attrItemAttrGroupService.remove(new LambdaQueryWrapper<RAttrItemAttrGroupDO>().in(RAttrItemAttrGroupDO::getItemAttrGroupGuid, itemAttrGroupGuidSet));
            }
        }
    }

    private void removeOldSkuSubgroup(List<ItemDO> updateItemDOList) {
        // 被推送到门店的套餐GUID集合
        Set<String> updatePkgGuidSet = updateItemDOList.stream().filter(itemDO -> itemDO.getItemType() == 1).map(ItemDO::getGuid).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(updatePkgGuidSet)) {
            skuSubgroupService.removeSkuSubgroupByPkgGuidList(new ArrayList<>(updatePkgGuidSet));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeAllRelationStore(ItemStringListDTO itemReqDTO, List<String> skuGuidList, String itemGuid, String brandGuid) {
        // 查询门店之前关联的guid列表
        List<String> storeGuidList = getUseStoreGuidList(itemGuid, brandGuid);
        if (CollectionUtils.isEmpty(storeGuidList)) {
            return;
        }
        List<ItemSynRespDTO> itemSynRespDTOList = listItemSynRespDTOByIds(skuGuidList, storeGuidList);
        // 获取品牌库推送的分类集合
        List<TypeDO> typeListFromBrand = selectTypeListFromBrand(itemSynRespDTOList);
        // 获取各门店对应的商品实体集合
        List<ItemDO> itemListFromStore = selectItemListFromStore(itemSynRespDTOList, storeGuidList);
        // 获取各门店对应的分类实体集合
        List<TypeDO> typeListFromStore = selectTypeListFromStore(typeListFromBrand, storeGuidList);
        // 按门店分组的商品集合
        Map<String, List<ItemDO>> storeGroupItemMap = itemListFromStore.stream().collect(Collectors.groupingBy(BasePushDO::getStoreGuid));
        // 按门店分组的分类集合
        Map<String, List<TypeDO>> storeGroupTypeMap = typeListFromStore.stream().collect(Collectors.groupingBy(BasePushDO::getStoreGuid));
        List<ItemDO> updateItemDOList = new ArrayList<>();
        storeGuidList.forEach(storeGuid -> {
            Map<String, List<TypeDO>> typeMap = storeGroupTypeMap.get(storeGuid).stream().collect(Collectors.groupingBy(BasePushDO::getParentGuid));
            Map<String, List<ItemDO>> itemMap = storeGroupItemMap.get(storeGuid).stream().collect(Collectors.groupingBy(BasePushDO::getParentGuid));
            itemSynRespDTOList.forEach(itemSynRespDTO -> {
                ItemDO thisItem = itemMap.get(itemSynRespDTO.getItemGuid()).get(0);
                if (typeMap.get(itemSynRespDTO.getTypeGuid()) == null) {
                    log.warn("分类集合为空 typeGuid={}", itemSynRespDTO.getTypeGuid());
                }
                TypeDO typeDO = typeMap.get(itemSynRespDTO.getTypeGuid()).get(0);
                if (!StringUtils.isEmpty(thisItem.getTypeGuid())) {
                    updateItemDOList.add(thisItem);
                }
                thisItem.setTypeGuid(typeDO.getGuid());
            });
        });
        if (!CollectionUtils.isEmpty(updateItemDOList)) {
            Set<String> updateItemGuidSet = updateItemDOList.stream().map(ItemDO::getGuid).collect(Collectors.toSet());
            //移除指定套餐下分组与单品属性
            removeRelateStoreItemList(updateItemGuidSet);
            // 被推送到门店的套餐GUID集合
            Set<String> updatePkgGuidSet = updateItemDOList.stream().filter(itemDO -> itemDO.getItemType() == 1).map(ItemDO::getGuid).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(updatePkgGuidSet)) {
                skuSubgroupService.removeSkuSubgroupByPkgGuidList(new ArrayList<>(updatePkgGuidSet));
            }
            //更新
            updateBatchById(updateItemDOList, updateItemDOList.size());
        }
        // 删除被推送门店套餐的历史无用分组 todo 要不要?
        //removeOtherSubgroupsOfItem(insertSubgroupDOList, alreadyUpdateSubgroupDOList);

        //剩下的门店都取消规格分配
        List<String> noStoreList = Collections.singletonList("0");
        if (!CollectionUtils.isEmpty(skuGuidList)) {
            List<String> needCorrectItemGuidList = itemSynRespDTOList.stream().filter(i -> {
                for (SkuSynRespDTO sku : i.getSkuList()) {
                    if (skuGuidList.contains(sku.getSkuGuid())) {
                        return true;
                    }
                }
                return false;
            }).map(ItemSynRespDTO::getItemGuid).collect(Collectors.toList());
            itemMapper.correctStoreItemIsEnableByBrandPush(needCorrectItemGuidList, noStoreList);
            itemMapper.correctStoreSkuIsEnableByBrandPush(skuGuidList, noStoreList);
        }
    }

    /**
     * 更新门店同名实体为（自建）后缀
     *
     * @param storeGuidList
     * @param itemNames     推送的商品名称
     * @param typeNames     推送的分类名称
     */
    private void updateDuplicateNameInStore(List<String> storeGuidList, List<String> itemNames, List<String> typeNames) {
        if (CollectionUtils.isEmpty(storeGuidList) || CollectionUtils.isEmpty(itemNames) || CollectionUtils.isEmpty(typeNames)) {
            return;
        }
        List<AttrGroupDO> attrGroupList = attrGroupService.list(new LambdaQueryWrapper<AttrGroupDO>().in(AttrGroupDO::getStoreGuid, storeGuidList));
        List<AttrGroupDO> attrGroupDOS = (List<AttrGroupDO>) selectDuplicateNameDOList(attrGroupList, false);
        List<TypeDO> typeList = typeService.list(new LambdaQueryWrapper<TypeDO>().in(TypeDO::getStoreGuid, storeGuidList).in(TypeDO::getName, typeNames));
        List<TypeDO> typeDOS = (List<TypeDO>) selectDuplicateNameDOList(typeList, false);
        List<ItemDO> itemList = list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getStoreGuid, storeGuidList).in(ItemDO::getName, itemNames));
        List<ItemDO> itemDOS = (List<ItemDO>) selectDuplicateNameDOList(itemList, false);
        List<AttrDO> attrList = attrService.list(new LambdaQueryWrapper<AttrDO>().in(AttrDO::getStoreGuid, storeGuidList));
        List<AttrDO> attrDOS = (List<AttrDO>) selectDuplicateNameDOList(attrList, true);
        if (!CollectionUtils.isEmpty(attrGroupDOS)) {
            attrGroupService.updateBatchById(attrGroupDOS, attrGroupDOS.size());
        }
        if (!CollectionUtils.isEmpty(typeDOS)) {
            typeService.updateBatchById(typeDOS, typeDOS.size());
        }
        if (!CollectionUtils.isEmpty(itemDOS)) {
            updateBatchById(itemDOS, itemDOS.size());
        }
        if (!CollectionUtils.isEmpty(attrDOS)) {
            attrService.updateBatchById(attrDOS, attrDOS.size());
        }

    }


    private List<? extends BasePushDO> selectDuplicateNameDOList(List<? extends BasePushDO> pushDOList, boolean attr) {

        Map<String, List<BasePushDO>> storeGroupPushDOMap = pushDOList.stream().collect(Collectors.groupingBy(BasePushDO::getStoreGuid));
        List<? extends BasePushDO> updateBasePushDOList = new ArrayList<>();
        Set<Map.Entry<String, List<BasePushDO>>> entries = storeGroupPushDOMap.entrySet();
        entries.forEach(entry -> {
            List<BasePushDO> itemDOS = entry.getValue();
            if (attr) {
                List<AttrDO> attrList = new ArrayList<>();
                itemDOS.forEach(itemDO -> attrList.add((AttrDO) itemDO));
                // 按属性组分组的属性map
                Map<String, List<AttrDO>> attrGroupGroupAttrMap = attrList.stream().collect(Collectors.groupingBy(AttrDO::getAttrGroupGuid));
                Set<Map.Entry<String, List<AttrDO>>> attrGroupEntries = attrGroupGroupAttrMap.entrySet();
                attrGroupEntries.forEach(attrGroupEntry -> {
                    List<AttrDO> thisAttrGroupAttrList = attrGroupEntry.getValue();
                    List<BasePushDO> basePushDOS = new ArrayList<>(thisAttrGroupAttrList);
                    selectDupLicateNameDOs(basePushDOS, (List<BasePushDO>) updateBasePushDOList, 1);
                });
            } else {
                selectDupLicateNameDOs(itemDOS, (List<BasePushDO>) updateBasePushDOList, 0);
            }
        });
        return updateBasePushDOList;
    }

    /**
     * @param basePushDOList,updateItemList
     * @param flag                          1:属性组推送自建不加（自建） ， 0：其他需加（自建）
     */
    private void selectDupLicateNameDOs(List<BasePushDO> basePushDOList, List<BasePushDO> updateItemList, Integer flag) {
        Map<String, List<BasePushDO>> nameGroupPushDOMap = basePushDOList.stream().collect(Collectors.groupingBy(BasePushDO::getName));
        Set<Map.Entry<String, List<BasePushDO>>> nameEntries = nameGroupPushDOMap.entrySet();
        nameEntries.forEach(nameEntry -> {
            List<? extends BasePushDO> thisNameBasePushDOS = nameEntry.getValue();
            if (thisNameBasePushDOS.size() > 1) {
                // 与门店自建商品重名
                thisNameBasePushDOS.forEach(pushDO -> {
                    if (StringUtils.isEmpty(pushDO.getParentGuid()) || pushDO.getGuid().equals(pushDO.getParentGuid())) {
                        if (flag != null && flag == 0) {
                            pushDO.setName(pushDO.getName() + SAVE_BY_STORE);
                        }
                        updateItemList.add(pushDO);
                    }
                });
            }
        });
    }

    private List<ItemDO> selectItemListFromStore(List<ItemSynRespDTO> itemListFromBrand, List<String> storeGuidList) {
        List<String> itemGuidList = itemListFromBrand.stream().map(ItemSynRespDTO::getItemGuid).collect(Collectors.toList());
        List<ItemDO> pushedItemDOList = list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getParentGuid, itemGuidList)
                .in(ItemDO::getStoreGuid, storeGuidList));
        List<PushBO> pushBOList = MapStructUtils.INSTANCE.itemDOList2pushBOList(pushedItemDOList);
        // 获取还需被推送的pair
        List<Pair<String, String>> requiredPairList = selectToPushPairList(pushBOList, storeGuidList, itemGuidList);
        // 更新已被推送实体
        if (!CollectionUtils.isEmpty(pushedItemDOList)) {
            updatePushedItemDOList(pushedItemDOList, itemListFromBrand);
        }
        if (CollectionUtils.isEmpty(requiredPairList)) {
            return pushedItemDOList;
        }
        if (!CollectionUtils.isEmpty(requiredPairList)) {
            List<ItemDO> itemDOS = insertItemDOList(itemListFromBrand, requiredPairList);
            pushedItemDOList.addAll(itemDOS);
        }
        return pushedItemDOList;

    }

    private List<ItemDO> insertItemDOList(List<ItemSynRespDTO> itemListFromBrand, List<Pair<String, String>> requiredPairList) {
        List<ItemDO> storeItemDOList = new ArrayList<>();
        requiredPairList.forEach(pair -> {
            String parentItemGuid = pair.getFirst();
            String storeGuid = pair.getSecond();
            ItemSynRespDTO brandItemDO = itemListFromBrand.stream().filter(itemSynRespDTO -> itemSynRespDTO.getItemGuid().equals(parentItemGuid)).findFirst().orElse(null);
            ItemDO itemDO = new ItemDO();
            setFields(itemDO, brandItemDO);
            itemDO.setStoreGuid(storeGuid);
            try {
                itemDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
            } catch (IOException e) {
                throw new BusinessException("BatchIdGenerator生成商品guid失败");
            }
            storeItemDOList.add(itemDO);
        });
        return storeItemDOList;
    }

    private void updatePushedItemDOList(List<ItemDO> pushedItemDOList, List<ItemSynRespDTO> itemListFromBrand) {
        List<ItemDO> updateTypeDOList = new ArrayList<>();
        for (ItemDO itemDO : pushedItemDOList) {
            ItemSynRespDTO brandItemDO = itemListFromBrand.stream()
                    .filter(brandItem -> brandItem.getItemGuid().equals(itemDO.getParentGuid()))
                    .findFirst()
                    .orElse(null);
            setFields(itemDO, brandItemDO);
            updateTypeDOList.add(itemDO);
        }
        updateBatchById(updateTypeDOList);
    }

    private void setFields(ItemDO storeItemDO, ItemSynRespDTO brandItemDO) {
        storeItemDO.setIsRecommend(brandItemDO.getIsRecommend());
        storeItemDO.setIsSign(brandItemDO.getIsSign());
        storeItemDO.setIsBestseller(brandItemDO.getIsBestseller());
        storeItemDO.setIsNew(brandItemDO.getIsNew());
        storeItemDO.setSort(brandItemDO.getSort());
        storeItemDO.setPictureUrl(brandItemDO.getPictureUrl());
        storeItemDO.setDescription(brandItemDO.getDescription());
        storeItemDO.setNameAbbr(brandItemDO.getNameAbbr());
        storeItemDO.setName(brandItemDO.getName());
        storeItemDO.setPinyin(brandItemDO.getPinyin());
        storeItemDO.setHasAttr(brandItemDO.getHasAttr());
        storeItemDO.setItemType(brandItemDO.getItemType());
        storeItemDO.setParentGuid(brandItemDO.getItemGuid());
        storeItemDO.setItemFrom(2);
        storeItemDO.setIsEnable(true);
        storeItemDO.setIsSoldOut(brandItemDO.getIsSoldOut());
    }

    private List<SubgroupDO> selectSubgroupListFromStore(List<SubgroupDO> subgroupListFromBrand, List<String> storeGuidList) {
        if (CollectionUtils.isEmpty(subgroupListFromBrand)) {
            return new ArrayList<>();
        }
        List<String> subgroupGuidList = subgroupListFromBrand.stream().map(SubgroupDO::getGuid).collect(Collectors.toList());
        List<SubgroupDO> pushedSubgroupDOList = subgroupService.list(new LambdaQueryWrapper<SubgroupDO>().in(SubgroupDO::getParentGuid, subgroupGuidList)
                .in(SubgroupDO::getStoreGuid, storeGuidList));
        List<PushBO> pushBOList = MapStructUtils.INSTANCE.subgroupDOList2pushBOList(pushedSubgroupDOList);
        // 获取还需被推送的pair
        List<Pair<String, String>> requiredPairList = selectToPushPairList(pushBOList, storeGuidList, subgroupGuidList);
        // 更新已被推送实体
        if (!CollectionUtils.isEmpty(pushedSubgroupDOList)) {
            updatePushedSubgroupDOList(pushedSubgroupDOList, subgroupListFromBrand);
        }
        if (CollectionUtils.isEmpty(requiredPairList)) {
            return pushedSubgroupDOList;
        }
        if (!CollectionUtils.isEmpty(requiredPairList)) {
            // 新增的属性集合
            List<SubgroupDO> subgroupDOList = insertSubgroupDOList(subgroupListFromBrand, requiredPairList);
            pushedSubgroupDOList.addAll(subgroupDOList);
        }
        return pushedSubgroupDOList;
    }

    private void removeOtherSubgroupsOfItem(List<SubgroupDO> insertSubgroupDOList, List<SubgroupDO> updateSubgroupDOGuidList) {
        List<String> insertSubgroupGuidList = insertSubgroupDOList.stream().map(SubgroupDO::getGuid).collect(Collectors.toList());
        List<String> updateSubgroupGuidList = updateSubgroupDOGuidList.stream().map(SubgroupDO::getGuid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(insertSubgroupGuidList) && CollectionUtils.isEmpty(updateSubgroupGuidList)) {
            return;
        }
        Set<String> itemGuidList = new HashSet<>();
        if (!CollectionUtils.isEmpty(insertSubgroupDOList)) {
            Set<String> itemGuidSet = insertSubgroupDOList.stream().map(SubgroupDO::getItemGuid).collect(Collectors.toSet());
            itemGuidList.addAll(itemGuidSet);
        }
        if (!CollectionUtils.isEmpty(updateSubgroupDOGuidList)) {
            Set<String> itemGuidSet = updateSubgroupDOGuidList.stream().map(SubgroupDO::getItemGuid).collect(Collectors.toSet());
            itemGuidList.addAll(itemGuidSet);
        }
        if (CollectionUtils.isEmpty(itemGuidList)) {
            log.error("removeOtherSubgroupsOfThisItem方法出错");
            return;
        }
        List<SubgroupDO> toDelSubgroupDOList = subgroupService.list(new LambdaQueryWrapper<SubgroupDO>()
                .in(SubgroupDO::getItemGuid, itemGuidList)
                .notIn(!CollectionUtils.isEmpty(insertSubgroupGuidList), SubgroupDO::getGuid, insertSubgroupGuidList)
                .notIn(!CollectionUtils.isEmpty(updateSubgroupGuidList), SubgroupDO::getGuid, updateSubgroupGuidList));
        if (!CollectionUtils.isEmpty(toDelSubgroupDOList)) {
            Set<String> toDelSubgroupGuidSet = toDelSubgroupDOList.stream().map(SubgroupDO::getGuid).collect(Collectors.toSet());
            subgroupService.removeByIds(toDelSubgroupGuidSet);
            skuSubgroupService.remove(new LambdaQueryWrapper<RSkuSubgroupDO>().in(RSkuSubgroupDO::getSubgroupGuid, toDelSubgroupGuidSet));
        }

    }

    private List<SubgroupDO> insertSubgroupDOList(List<SubgroupDO> subgroupListFromBrand, List<Pair<String, String>> requiredPairList) {

        List<SubgroupDO> storeSubgroupDOList = new ArrayList<>();
        requiredPairList.forEach(pair -> {
            String parentSubgroupGuid = pair.getFirst();
            String storeGuid = pair.getSecond();
            SubgroupDO brandSubgroupDO = subgroupListFromBrand.stream().filter(subgroupDO -> subgroupDO.getGuid().equals(parentSubgroupGuid)).findFirst().orElse(null);
            SubgroupDO storeSubgroupDO = new SubgroupDO();
            setFields(storeSubgroupDO, brandSubgroupDO);
            storeSubgroupDO.setStoreGuid(storeGuid);
            try {
                storeSubgroupDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
            } catch (IOException e) {
                throw new BusinessException("BatchIdGenerator生成商品guid失败");
            }
            storeSubgroupDOList.add(storeSubgroupDO);
        });
        return storeSubgroupDOList;
    }

    private void updatePushedSubgroupDOList(List<SubgroupDO> pushedSubgroupDOList, List<SubgroupDO> subgroupListFromBrand) {
        List<SubgroupDO> updateSubgroupDOList = new ArrayList<>();
        for (SubgroupDO storeSubgroupDO : pushedSubgroupDOList) {
            SubgroupDO brandSubgroupDO = subgroupListFromBrand.stream().filter(brandAttr -> brandAttr.getGuid().equals(storeSubgroupDO.getParentGuid())).findFirst().orElse(null);
            setFields(storeSubgroupDO, brandSubgroupDO);
            updateSubgroupDOList.add(storeSubgroupDO);
        }
        subgroupService.updateBatchById(updateSubgroupDOList);
    }

    private void setFields(SubgroupDO storeSubgroupDO, SubgroupDO brandSubgroupDO) {
        storeSubgroupDO.setSort(brandSubgroupDO.getSort());
        storeSubgroupDO.setName(brandSubgroupDO.getName());
        storeSubgroupDO.setPickNum(brandSubgroupDO.getPickNum());
        storeSubgroupDO.setParentGuid(brandSubgroupDO.getGuid());
        storeSubgroupDO.setSubgroupFrom(2);
    }

    private List<AttrDO> selectAttrListFromStore(List<AttrDO> attrListFromBrand, List<String> storeGuidList) {
        List<String> attrGuidList = attrListFromBrand.stream().map(AttrDO::getGuid).collect(Collectors.toList());
        List<AttrDO> pushedAttrDOList = attrService.list(new LambdaQueryWrapper<AttrDO>().in(AttrDO::getParentGuid, attrGuidList)
                .in(AttrDO::getStoreGuid, storeGuidList));
        List<PushBO> pushBOList = MapStructUtils.INSTANCE.attrDOList2pushBOList(pushedAttrDOList);
        // 获取还需被推送的pair
        List<Pair<String, String>> requiredPairList = selectToPushPairList(pushBOList, storeGuidList, attrGuidList);
        // 更新已被推送实体
        if (!CollectionUtils.isEmpty(pushedAttrDOList)) {
            updatePushedAttrDOList(pushedAttrDOList, attrListFromBrand);
        }
        if (CollectionUtils.isEmpty(requiredPairList)) {
            return pushedAttrDOList;
        }
        if (!CollectionUtils.isEmpty(requiredPairList)) {
            // 新增的属性集合
            List<AttrDO> attrDOList = insertAttrDOList(attrListFromBrand, requiredPairList);
            pushedAttrDOList.addAll(attrDOList);
        }
        return pushedAttrDOList;
    }

    private List<AttrDO> insertAttrDOList(List<AttrDO> attrListFromBrand, List<Pair<String, String>> requiredPairList) {

        List<AttrDO> storeAttrDOList = new ArrayList<>();
        requiredPairList.forEach(pair -> {
            String parentAttrGuid = pair.getFirst();
            String storeGuid = pair.getSecond();
            AttrDO brandAttrDO = attrListFromBrand.stream().filter(attrDO -> attrDO.getGuid().equals(parentAttrGuid)).findFirst().orElse(null);
            AttrDO storeAttrDO = new AttrDO();
            setFields(storeAttrDO, brandAttrDO);
            storeAttrDO.setStoreGuid(storeGuid);
            try {
                storeAttrDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
            } catch (IOException e) {
                throw new BusinessException("BatchIdGenerator生成商品guid失败");
            }
            storeAttrDOList.add(storeAttrDO);
        });
        return storeAttrDOList;
    }

    private void updatePushedAttrDOList(List<AttrDO> pushedAttrDOList, List<AttrDO> attrListFromBrand) {
        List<AttrDO> updateAttrDOList = new ArrayList<>();
        for (AttrDO storeAttrDO : pushedAttrDOList) {
            AttrDO brandAttrDO = attrListFromBrand.stream().filter(brandAttr -> brandAttr.getGuid().equals(storeAttrDO.getParentGuid())).findFirst().orElse(null);
            setFields(storeAttrDO, brandAttrDO);
            updateAttrDOList.add(storeAttrDO);
        }
        attrService.updateBatchById(updateAttrDOList);
    }

    private void setFields(AttrDO storeAttrDO, AttrDO brandAttrDO) {
        storeAttrDO.setIsDefault(brandAttrDO.getIsDefault());
        storeAttrDO.setName(brandAttrDO.getName());
        storeAttrDO.setPrice(brandAttrDO.getPrice());
        storeAttrDO.setAttrFrom(2);
        storeAttrDO.setParentGuid(brandAttrDO.getGuid());
    }

    private List<AttrGroupDO> selectAttrGroupListFromStore(List<AttrGroupDO> attrGroupListFromBrand, List<String> storeGuidList) {
        List<String> attrGroupGuidList = attrGroupListFromBrand.stream().map(AttrGroupDO::getGuid).collect(Collectors.toList());
        List<AttrGroupDO> pushedAttrGroupDOList = attrGroupService.list(new LambdaQueryWrapper<AttrGroupDO>().in(AttrGroupDO::getParentGuid, attrGroupGuidList)
                .in(AttrGroupDO::getStoreGuid, storeGuidList));
        List<PushBO> pushBOList = MapStructUtils.INSTANCE.attrGroupDOList2pushBOList(pushedAttrGroupDOList);
        // 获取还需被推送的pair
        List<Pair<String, String>> requiredPairList = selectToPushPairList(pushBOList, storeGuidList, attrGroupGuidList);
        // 更新已被推送实体
        if (!CollectionUtils.isEmpty(pushedAttrGroupDOList)) {
            updatePushedAttrGroupDOList(pushedAttrGroupDOList, attrGroupListFromBrand);
        }
        if (CollectionUtils.isEmpty(requiredPairList)) {
            return pushedAttrGroupDOList;
        }
        if (!CollectionUtils.isEmpty(requiredPairList)) {
            // 新增的属性组集合
            List<AttrGroupDO> attrGroupDOList = insertAttrGroupDOList(attrGroupListFromBrand, requiredPairList);
            pushedAttrGroupDOList.addAll(attrGroupDOList);
        }
        return pushedAttrGroupDOList;
    }

    private List<AttrGroupDO> insertAttrGroupDOList(List<AttrGroupDO> attrGroupListFromBrand, List<Pair<String, String>> requiredPairList) {

        List<AttrGroupDO> storeAttrGroupDOList = new ArrayList<>();
        requiredPairList.forEach(pair -> {
            String parentAttrGroupGuid = pair.getFirst();
            String storeGuid = pair.getSecond();
            AttrGroupDO brandAttrGroupDO = attrGroupListFromBrand.stream().filter(attrGroupDO -> attrGroupDO.getGuid().equals(parentAttrGroupGuid)).findFirst().orElse(null);
            AttrGroupDO storeAttrGroupDO = new AttrGroupDO();
            setFields(storeAttrGroupDO, brandAttrGroupDO);
            storeAttrGroupDO.setStoreGuid(storeGuid);
            try {
                storeAttrGroupDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
            } catch (IOException e) {
                throw new BusinessException("BatchIdGenerator生成商品guid失败");
            }
            storeAttrGroupDOList.add(storeAttrGroupDO);
        });
        attrGroupService.saveBatch(storeAttrGroupDOList, storeAttrGroupDOList.size());
        return storeAttrGroupDOList;
    }

    private void updatePushedAttrGroupDOList(List<AttrGroupDO> pushedAttrGroupDOList, List<AttrGroupDO> attrGroupListFromBrand) {
        List<AttrGroupDO> updateAttrGroupDOList = new ArrayList<>();
        for (AttrGroupDO attrGroupDO : pushedAttrGroupDOList) {
            AttrGroupDO brandAttrGroupDO = attrGroupListFromBrand.stream().filter(brandAttrGroup -> brandAttrGroup.getGuid().equals(attrGroupDO.getParentGuid())).findFirst().orElse(null);
            setFields(attrGroupDO, brandAttrGroupDO);
            updateAttrGroupDOList.add(attrGroupDO);
        }
        attrGroupService.updateBatchById(updateAttrGroupDOList);
    }

    private void setFields(AttrGroupDO storeAttrGroupDO, AttrGroupDO brandAttrGroupDO) {
        storeAttrGroupDO.setAttrGroupFrom(2);
        storeAttrGroupDO.setWithDefault(brandAttrGroupDO.getWithDefault());
        storeAttrGroupDO.setDescription(brandAttrGroupDO.getDescription());
        storeAttrGroupDO.setIsEnable(brandAttrGroupDO.getIsEnable());
        storeAttrGroupDO.setIsMultiChoice(brandAttrGroupDO.getIsMultiChoice());
        storeAttrGroupDO.setIsRequired(brandAttrGroupDO.getIsRequired());
        storeAttrGroupDO.setIconUrl(brandAttrGroupDO.getIconUrl());
        storeAttrGroupDO.setName(brandAttrGroupDO.getName());
        storeAttrGroupDO.setSort(brandAttrGroupDO.getSort());
        storeAttrGroupDO.setNameChange(brandAttrGroupDO.getNameChange());
        storeAttrGroupDO.setParentGuid(brandAttrGroupDO.getGuid());
    }

    private List<SkuDO> selectSkuListFromStore(Boolean isUpdateSubItem, List<SkuDO> skuListFromBrand,
                                               List<SubgroupDO> subgroupListFromBrand, List<String> storeGuidList) {
        List<String> skuGuidList = skuListFromBrand.stream().map(SkuDO::getGuid).collect(Collectors.toList());
        List<SkuDO> pushedSkuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>().in(SkuDO::getParentGuid, skuGuidList)
                .in(SkuDO::getStoreGuid, storeGuidList));
        List<PushBO> pushBOList = MapStructUtils.INSTANCE.skuDOList2pushBOList(pushedSkuDOList);
        // 获取还需被推送的pair
        List<Pair<String, String>> requiredPairList = selectToPushPairList(pushBOList, storeGuidList, skuGuidList);
        // 更新已被推送sku
        if (!CollectionUtils.isEmpty(pushedSkuDOList)) {
            updatePushedSkuDOList(isUpdateSubItem, pushedSkuDOList, skuListFromBrand, subgroupListFromBrand);
        }
        if (CollectionUtils.isEmpty(requiredPairList)) {
            return pushedSkuDOList;
        }
        if (!CollectionUtils.isEmpty(requiredPairList)) {
            List<SkuDO> skuDOList = insertSkuDOList(skuListFromBrand, requiredPairList);
            pushedSkuDOList.addAll(skuDOList);
        }
        return pushedSkuDOList;

    }

    private List<SkuDO> insertSkuDOList(List<SkuDO> skuListFromBrand, List<Pair<String, String>> requiredPairList) {
        List<SkuDO> storeSkuDOList = new ArrayList<>();
        requiredPairList.forEach(pair -> {
            String parentSkuGuid = pair.getFirst();
            String storeGuid = pair.getSecond();
            SkuDO brandSkuDO = skuListFromBrand.stream()
                    .filter(skuDO -> skuDO.getGuid().equals(parentSkuGuid))
                    .findFirst().orElse(null);
            SkuDO storeSkuDO = new SkuDO();
            setFields(storeSkuDO, brandSkuDO);
            storeSkuDO.setParentGuid(parentSkuGuid);
            storeSkuDO.setStoreGuid(storeGuid);
            /*try {
                storeSkuDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
            } catch (IOException e) {
                throw new BusinessException("BatchIdGenerator生成商品guid失败");
            }*/
            storeSkuDOList.add(storeSkuDO);
        });
        return storeSkuDOList;
    }

    private void updatePushedSkuDOList(Boolean isUpdateSubItem, List<SkuDO> pushedSkuDOList, List<SkuDO> skuListFromBrand,
                                       List<SubgroupDO> subgroupListFromBrand) {
        // 判断是否需要更新套餐子商品
        if (Boolean.FALSE.equals(isUpdateSubItem) && !CollectionUtils.isEmpty(subgroupListFromBrand)) {
            // 查询需要推送的门店商品对应的品牌库的sku
            List<String> pushedSkuParentGuidList = pushedSkuDOList.stream()
                    .map(SkuDO::getParentGuid)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            List<SkuDO> skuBrandDOList = new ArrayList<>(skuService.listByIds(pushedSkuParentGuidList));
            Map<String, String> skuBrandDOMap = skuBrandDOList.stream()
                    .collect(Collectors.toMap(SkuDO::getGuid, SkuDO::getItemGuid, (key1, key2) -> key1));
            // 套餐商品
            List<String> groupItemGuids = subgroupListFromBrand.stream().map(SubgroupDO::getItemGuid).collect(Collectors.toList());
            pushedSkuDOList = pushedSkuDOList.stream().filter(e -> {
                String brandItemGuid = skuBrandDOMap.get(e.getParentGuid());
                return groupItemGuids.contains(brandItemGuid);
            }).collect(Collectors.toList());
        }
        List<SkuDO> updateSkuDOList = new ArrayList<>();
        for (SkuDO skuDO : pushedSkuDOList) {
            SkuDO brandSkuDO = skuListFromBrand.stream()
                    .filter(brandSku -> brandSku.getGuid().equals(skuDO.getParentGuid()))
                    .findFirst().orElse(null);
            setFields(skuDO, brandSkuDO);
            updateSkuDOList.add(skuDO);
        }
        log.info("修改商品:{}", JacksonUtils.writeValueAsString(updateSkuDOList));
        if (!CollectionUtils.isEmpty(updateSkuDOList)) {
            skuService.updateBatchById(updateSkuDOList);
        }
    }

    private void setFields(SkuDO storeSkuDO, SkuDO brandSkuDO) {
        storeSkuDO.setName(brandSkuDO.getName());
        storeSkuDO.setCode(brandSkuDO.getCode());
        storeSkuDO.setIsJoinAio(brandSkuDO.getIsJoinAio());
        storeSkuDO.setIsJoinPad(brandSkuDO.getIsJoinPad());
        storeSkuDO.setIsJoinPos(brandSkuDO.getIsJoinPos());
        storeSkuDO.setIsJoinElm(brandSkuDO.getIsJoinElm());
        storeSkuDO.setIsJoinMt(brandSkuDO.getIsJoinMt());
        storeSkuDO.setIsJoinWeChat(brandSkuDO.getIsJoinWeChat());
        storeSkuDO.setIsMemberDiscount(brandSkuDO.getIsMemberDiscount());
        storeSkuDO.setIsRack(brandSkuDO.getIsRack());
        storeSkuDO.setIsWholeDiscount(brandSkuDO.getIsWholeDiscount());
        storeSkuDO.setMinOrderNum(brandSkuDO.getMinOrderNum());
        storeSkuDO.setSalePrice(brandSkuDO.getSalePrice());
        storeSkuDO.setUnit(brandSkuDO.getUnit());
        storeSkuDO.setUnitCode(brandSkuDO.getUnitCode());
        storeSkuDO.setUpc(brandSkuDO.getUpc());
        storeSkuDO.setIsJoinBuffet(brandSkuDO.getIsJoinBuffet());
        storeSkuDO.setElmSku(brandSkuDO.getElmSku());
        storeSkuDO.setMtSku(brandSkuDO.getMtSku());
        storeSkuDO.setTotalStock(brandSkuDO.getTotalStock());
        storeSkuDO.setStock(brandSkuDO.getStock());
        storeSkuDO.setMemberPrice(brandSkuDO.getMemberPrice());
        storeSkuDO.setCostPrice(brandSkuDO.getCostPrice());
        storeSkuDO.setSkuFrom(2);
        storeSkuDO.setIsEnable(true);
        storeSkuDO.setPricePlanGuid(brandSkuDO.getPricePlanGuid());
        storeSkuDO.setIsJoinKds(brandSkuDO.getIsJoinKds());
        storeSkuDO.setAccountingPrice(brandSkuDO.getAccountingPrice());
        storeSkuDO.setTakeawayAccountingPrice(brandSkuDO.getTakeawayAccountingPrice());
    }

    /**
     * 获取需被推送的pair集合
     *
     * @param pushBOList
     * @param storeGuidList
     * @param skuGuidList
     * @return
     */
    private List<Pair<String, String>> selectToPushPairList(List<PushBO> pushBOList, List<String> storeGuidList, List<String> skuGuidList) {
        // 获取已存在的分类pair
        List<Pair<String, String>> existPairList = getPushBOPairListByPushBOList(pushBOList);

        // 需要的分类的pair集合（由type的parentGuid以及storeGuid确定是否为需要的分类）
        List<Pair<String, String>> requiredPairList = getPushBOPairList(storeGuidList, skuGuidList);
        // 排除已存在的对应分类
        requiredPairList.removeAll(existPairList);
        return requiredPairList;
    }

    /**
     * 获取门店的对应的分类集合
     *
     * @param typeListFromBrand
     * @param storeGuidList
     * @return
     */
    private List<TypeDO> selectTypeListFromStore(List<TypeDO> typeListFromBrand, List<String> storeGuidList) {
        List<String> typeGuidList = typeListFromBrand.stream().map(TypeDO::getGuid).collect(Collectors.toList());
        List<TypeDO> pushedTypeDOList = typeService.list(new LambdaQueryWrapper<TypeDO>().in(TypeDO::getParentGuid, typeGuidList)
                .in(TypeDO::getStoreGuid, storeGuidList));
        List<PushBO> pushBOList = MapStructUtils.INSTANCE.typeDOList2pushBOList(pushedTypeDOList);
        // 获取还需被推送的pair
        List<Pair<String, String>> requiredPairList = selectToPushPairList(pushBOList, storeGuidList, typeGuidList);
        // 更新已被推送分类
        if (!CollectionUtils.isEmpty(pushedTypeDOList)) {
            updatePushedTypeDOList(pushedTypeDOList, typeListFromBrand);
        }
        if (CollectionUtils.isEmpty(requiredPairList)) {
            return pushedTypeDOList;
        }
        if (!CollectionUtils.isEmpty(requiredPairList)) {
            List<TypeDO> typeDOList = insertTypeDOList(typeListFromBrand, requiredPairList);
            pushedTypeDOList.addAll(typeDOList);
        }
        return pushedTypeDOList;
    }

    /**
     * 将还未推送到门店的分类推送到门店
     *
     * @param typeListFromBrand
     * @param typePairList
     */
    private List<TypeDO> insertTypeDOList(List<TypeDO> typeListFromBrand, List<Pair<String, String>> typePairList) {
        List<TypeDO> storeTypeDOList = new ArrayList<>();

        typePairList.forEach(pair -> {
            String parentTypeGuid = pair.getFirst();
            String storeGuid = pair.getSecond();
            TypeDO brandTypeDO = typeListFromBrand.stream().filter(typeDO -> typeDO.getGuid().equals(parentTypeGuid)).findFirst().orElse(null);
            TypeDO storeTypeDO = new TypeDO(brandTypeDO);
            storeTypeDO.setStoreGuid(storeGuid);
            try {
                storeTypeDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
            } catch (IOException e) {
                throw new BusinessException("BatchIdGenerator生成商品guid失败");
            }
            storeTypeDOList.add(storeTypeDO);
        });
        //判断是否有必点
        updateMustPoint(storeTypeDOList);
        typeService.saveBatch(storeTypeDOList, storeTypeDOList.size());
        return storeTypeDOList;
    }

    /**
     * 更新已被推送分类
     *
     * @param pushedTypeDOList
     * @param typeListFromBrand
     */
    private void updatePushedTypeDOList(List<TypeDO> pushedTypeDOList, List<TypeDO> typeListFromBrand) {
        List<TypeDO> updateTypeDOList = new ArrayList<>();
        for (TypeDO typeDO : pushedTypeDOList) {
            TypeDO brandTypeDO = typeListFromBrand.stream().filter(brandType -> brandType.getGuid().equals(typeDO.getParentGuid())).findFirst().orElse(null);
            typeDO.setName(brandTypeDO.getName());
            typeDO.setIsEnable(brandTypeDO.getIsEnable());
            typeDO.setSort(brandTypeDO.getSort());
            typeDO.setIconUrl(brandTypeDO.getIconUrl());
            typeDO.setDescription(brandTypeDO.getDescription());
            typeDO.setIsMustPoint(brandTypeDO.getIsMustPoint());
            updateTypeDOList.add(typeDO);
        }

        //判断是否有必点
        updateMustPoint(updateTypeDOList);
        typeService.updateBatchById(updateTypeDOList);
    }

    private void updateMustPoint(List<TypeDO> updateTypeDOList) {
        List<TypeDO> typeDOList = updateTypeDOList
                .stream()
                .filter(in -> Objects.nonNull(in.getIsMustPoint())
                        && in.getIsMustPoint() == BooleanEnum.TRUE.getCode()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(typeDOList)) {
            List<TypeDO> typeDOS = typeMapper.selectList(
                    new LambdaQueryWrapper<TypeDO>()
                            .in(TypeDO::getStoreGuid, typeDOList.stream().map(BasePushDO::getStoreGuid).collect(Collectors.toList()))
                            .isNull(TypeDO::getBrandGuid)
                            .eq(TypeDO::getIsMustPoint, BooleanEnum.TRUE.getCode()));
            if (CollUtil.isNotEmpty(typeDOS)) {
                log.info("必点分类已存在，批量修改必点分类");
                typeDOS.forEach(in -> in.setIsMustPoint(BooleanEnum.FALSE.getCode()));
                typeService.updateBatchById(typeDOS);
            }
        }
    }

    /**
     * 获取品牌库推送的套餐下分组集合
     *
     * @param itemSynRespDTOList
     * @return
     */
    private List<SubgroupDO> selectSubgroupListFromBrand(List<ItemSynRespDTO> itemSynRespDTOList) {
        Set<String> subgroupGuidSet = new HashSet<>();
        itemSynRespDTOList.forEach(itemSynRespDTO -> {
            if (itemSynRespDTO.getItemType() == 1) {
                List<SubgroupSynRespDTO> subgroupList = itemSynRespDTO.getSubgroupList();
                Set<String> thisItemSubgroupGuidSet = subgroupList.stream().map(SubgroupSynRespDTO::getSubgroupGuid).collect(Collectors.toSet());
                subgroupGuidSet.addAll(thisItemSubgroupGuidSet);
            }
        });
        if (CollectionUtils.isEmpty(subgroupGuidSet)) {
            return new ArrayList<>();
        } else {
            Collection<SubgroupDO> subgroupDOS = subgroupService.listByIds(subgroupGuidSet);
            return new ArrayList<>(subgroupDOS);
        }
    }

    /**
     * 获取品牌库推送的属性集合
     *
     * @param itemSynRespDTOList
     * @return
     */
    private List<AttrDO> selectAttrListFromBrand(List<ItemSynRespDTO> itemSynRespDTOList) {
        Set<String> attrGuidSet = new HashSet<>();
        itemSynRespDTOList.forEach(itemSynRespDTO -> {
            List<AttrGroupSynRespDTO> attrGroupList = itemSynRespDTO.getAttrGroupList();
            if (!CollectionUtils.isEmpty(attrGroupList)) {
                attrGroupList.forEach(attrGroupSynRespDTO -> {
                    List<AttrSynRespDTO> attrList = attrGroupSynRespDTO.getAttrList();
                    Set<String> thisItemThisGroupAttrGuidSet = attrList.stream().map(AttrSynRespDTO::getAttrGuid).collect(Collectors.toSet());
                    attrGuidSet.addAll(thisItemThisGroupAttrGuidSet);
                });
            }
        });
        Collection<AttrDO> attrDOS = attrService.listByIds(attrGuidSet);
        return new ArrayList<>(attrDOS);
    }

    /**
     * 获取品牌库推送的属性组集合
     *
     * @param itemSynRespDTOList
     * @return
     */
    private List<AttrGroupDO> selectAttrGroupListFromBrand(List<ItemSynRespDTO> itemSynRespDTOList) {
        Set<String> attrGroupGuidSet = new HashSet<>();
        itemSynRespDTOList.forEach(itemSynRespDTO -> {
            List<AttrGroupSynRespDTO> attrGroupList = itemSynRespDTO.getAttrGroupList();
            if (!CollectionUtils.isEmpty(attrGroupList)) {
                List<String> attrGroupGuidList = attrGroupList.stream().map(AttrGroupSynRespDTO::getAttrGroupGuid).collect(Collectors.toList());
                attrGroupGuidSet.addAll(attrGroupGuidList);
            }
        });
        if (CollectionUtils.isEmpty(attrGroupGuidSet)) {
            return new ArrayList<>();
        } else {
            Collection<AttrGroupDO> attrGroupDOS = attrGroupService.listByIds(attrGroupGuidSet);
            return new ArrayList<>(attrGroupDOS);
        }
    }

    /**
     * 获取品牌库推送的规格集合
     *
     * @param itemSynRespDTOList
     * @return
     */
    private List<SkuDO> selectSkuListFromBrand(List<ItemSynRespDTO> itemSynRespDTOList) {
        Set<String> skuGuidSet = new HashSet<>();
        itemSynRespDTOList.forEach(itemSynRespDTO -> {
            List<SkuSynRespDTO> skuList = itemSynRespDTO.getSkuList();
            Set<String> thisItemSkuGuidSet = skuList.stream().map(SkuSynRespDTO::getSkuGuid).collect(Collectors.toSet());
            skuGuidSet.addAll(thisItemSkuGuidSet);
        });
        Collection<SkuDO> skuDOS = skuService.listByIds(skuGuidSet);
        return new ArrayList<>(skuDOS);
    }

    /**
     * 获取品牌库推送的分类集合
     *
     * @param itemSynRespDTOList
     * @return
     */
    private List<TypeDO> selectTypeListFromBrand(List<ItemSynRespDTO> itemSynRespDTOList) {
        Set<String> typeGuidSet = itemSynRespDTOList.stream().map(ItemSynRespDTO::getTypeGuid).collect(Collectors.toSet());
        Collection<TypeDO> typeDOS = typeService.listByIds(typeGuidSet);
        return new ArrayList<>(typeDOS);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CreateRepertoryReqDTO batchImport(ItemDoubleParamDTO<List<ItemExcelTemplateReqDTO>, String> itemDoubleParamDTO) {
        // 批量导入商品的所有分类名称
        List<ItemExcelTemplateReqDTO> itemExcelTemplateReqDTOList = itemDoubleParamDTO.getFirstData();
        if (ObjectUtils.isEmpty(itemExcelTemplateReqDTOList)) {
            return null;
        }
        // 准备数据
        final Integer from = itemDoubleParamDTO.getFrom();
        final ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(from);
        itemSingleDTO.setData(itemDoubleParamDTO.getSecondData());

        // 处理类型
        Set<String> typeNames = itemExcelTemplateReqDTOList.stream().filter(typeDO -> {
            String typeName = typeDO.getTypeName();
            int length = typeName.trim().length();
            return length > 0 && length < 21;
        }).map(o -> o.getTypeName().trim()).collect(Collectors.toSet());
        List<TypeDO> typeDOS;
        if (from.equals(ModuleEntranceEnum.STORE.code())) {
            typeDOS = typeService.list(new LambdaQueryWrapper<TypeDO>().eq(TypeDO::getStoreGuid, itemDoubleParamDTO.getSecondData()));
        } else if (from.equals(ModuleEntranceEnum.BRAND.code())) {
            typeDOS = typeService.list(new LambdaQueryWrapper<TypeDO>().eq(TypeDO::getBrandGuid, itemDoubleParamDTO.getSecondData()));
        } else {
            throw new ParameterException("模块入口参数错误");
        }

        // 库中已有的类型名称
        Set<String> existentTypeNames = typeDOS.stream().map(TypeDO::getName).collect(Collectors.toSet());

        // 库中没有的类型，新增类型
        List<String> toInsertTypeName = (List<String>) org.apache.commons.collections.CollectionUtils.subtract(typeNames, existentTypeNames);
        Integer typeSort = typeService.getSort(itemSingleDTO);
        List<TypeDO> toInsertType = new ArrayList<>();
        if (!ObjectUtils.isEmpty(toInsertTypeName)) {
            toInsertType = buildType(toInsertTypeName, typeSort, from, itemDoubleParamDTO.getSecondData());
            typeDOS.addAll(toInsertType);
        }

        // 构建商品入库信息 ItemDO, SkuDO
        ArrayList<ItemDO> toInsertItem = new ArrayList<>(itemExcelTemplateReqDTOList.size());
        ArrayList<SkuDO> toInsertSku = new ArrayList<>(itemExcelTemplateReqDTOList.size());
        int itemSort = getSort(itemSingleDTO);
        CreateRepertoryReqDTO repertoryReqDTO = new CreateRepertoryReqDTO(); //创建开启库存的容器
        buildItemAndSku(toInsertItem, toInsertSku, itemExcelTemplateReqDTOList, typeDOS, itemSort, from, itemDoubleParamDTO.getSecondData(), repertoryReqDTO);
        if (CollectionUtils.isEmpty(toInsertItem)) {
            return null;
        }
        if (CollectionUtils.isEmpty(toInsertSku)) {
            return null;
        }
        saveBatch(toInsertItem, toInsertItem.size());
        skuService.saveBatch(toInsertSku, toInsertSku.size());
        Set<String> typeGuidSet = toInsertItem.stream().map(ItemDO::getTypeGuid).collect(Collectors.toSet());
        toInsertType.removeIf(typeDO -> !typeGuidSet.contains(typeDO.getGuid()));
        typeService.saveBatch(toInsertType, toInsertType.size());

        return toInsertItem.size() > 0 ? repertoryReqDTO : null;
    }

    /**
     * 批量开启库存
     *
     * @param repertoryReqDTO
     * @param storeGuid
     */
    @Override
    public void batchOpenStack(CreateRepertoryReqDTO repertoryReqDTO, String storeGuid) {
        if (!CollectionUtils.isEmpty(repertoryReqDTO.getDetailList())) {
            repertoryReqDTO.getDetailList().forEach(inOutGoodsDTO -> {
                inOutGoodsDTO.setGoodsClassifyName(typeService.getById(inOutGoodsDTO.getGoodsClassifyGuid()).getName());
            });
            repertoryReqDTO.setInvoiceType(6);//期初入库
            repertoryReqDTO.setInOut(0);
            repertoryReqDTO.setStoreGuid(storeGuid);
            erpFeginService.saveRepertory(repertoryReqDTO);  //开启库存
        }
    }

    /**
     * 批量导入商品 - 构建商品和sku
     *
     * @param itemContainer               商品容器 传入一个空List以接受构建好的商品
     * @param skuContainer                sku容器
     * @param itemExcelTemplateReqDTOList 数据
     * @param typeDOS                     类型集合
     * @param itemSort                    排序
     * @param from                        来源
     * @param organizationGuid            StoreGuid or BrandGuid
     * @param repertoryReqDTO             待开启库存的商品集合
     */
    private void buildItemAndSku(List<ItemDO> itemContainer, List<SkuDO> skuContainer,
                                 List<ItemExcelTemplateReqDTO> itemExcelTemplateReqDTOList,
                                 List<TypeDO> typeDOS, int itemSort, Integer from,
                                 String organizationGuid, CreateRepertoryReqDTO repertoryReqDTO) {
        List<InOutGoodsDTO> detailList = new ArrayList<>();
        for (int i = 0, itemExcelTemplateReqDTOListSize = itemExcelTemplateReqDTOList.size(); i < itemExcelTemplateReqDTOListSize; i++) {
            ItemExcelTemplateReqDTO o = itemExcelTemplateReqDTOList.get(i);
            if (StringUtils.isEmpty(o.getItemName().trim())) {
                continue;
            }
            int length = o.getItemName().trim().length();
            if (length < 1 || length > 40) {
                continue;
            }
            if (!StringUtils.isEmpty(o.getNameAbbr()) && !StringUtils.isEmpty(o.getNameAbbr().trim())) {
                int nameAbbrLength = o.getNameAbbr().trim().length();
                if (nameAbbrLength < 1 || nameAbbrLength > 40) {
                    continue;
                }
            }
            ItemDO itemDO = new ItemDO();
            String itemGuid = null;
            try {
                itemGuid = String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item"));
            } catch (IOException e) {
                throw new BusinessException("BatchIdGenerator生成商品guid失败");
            }
            itemDO.setGuid(itemGuid);
            Optional<String> typeGuid = typeDOS.stream()
                    .filter(typeDO -> typeDO.getName().trim().equals(o.getTypeName().trim()))
                    .map(TypeDO::getGuid).findFirst();
            if (!typeGuid.isPresent()) {
                continue;
            }
            itemDO.setTypeGuid(typeGuid.get());

            itemDO.setItemFrom(from);
            itemDO.setPictureUrl(o.getPictureUrl());
            if (o.getItemType().trim().equals("称重")) {
                itemDO.setItemType(3);
                o.setMinOrderNum(new BigDecimal("0.01"));
            } else if (o.getItemType().trim().equals("普通")) {
                itemDO.setItemType(4);
                o.setMinOrderNum(BigDecimal.ONE);
            } else {
                continue;
            }
            itemDO.setHasAttr(FALSE);
            itemDO.setIsNew(0);
            itemDO.setIsBestseller(0);
            itemDO.setIsSign(0);
            itemDO.setName(o.getItemName());
            String pinyin = Chinese2PinyinUtils.initials(o.getItemName());
            itemDO.setPinyin(Optional.ofNullable(pinyin).isPresent() && !Strings.isNullOrEmpty(pinyin) ? pinyin : "TESHUZIFU");
            itemDO.setNameAbbr(o.getNameAbbr());
            itemDO.setSort(itemSort++);

            SkuDO skuDO = new SkuDO();
            // 目前都按照单规格处理
            skuDO.setGuid(itemGuid + "01");

            skuDO.setItemGuid(itemGuid);
            BigDecimal salePrice = o.getSalePrice();
            if (salePrice.compareTo(BigDecimal.ZERO) < 0 ||
                    salePrice.compareTo(BigDecimal.valueOf(99999.99d)) > 0) {
                continue;
            }
            if (o.getSalePrice() != null) {
                skuDO.setSalePrice(o.getSalePrice());
            }
            //bugfix: 21555 21578
            if (o.getIsRack() != null) {
                if (o.getIsRack() == 0) {
                    skuDO.setIsJoinStore(0);
                    skuDO.setIsJoinMiniAppTakeaway(0);
                }
                if (o.getIsRack() == 1) {
                    skuDO.setIsJoinStore(1);
                    skuDO.setIsJoinMiniAppTakeaway(1);
                }
            }
            skuDO.setUnit(Optional.ofNullable(o.getUnit()).isPresent() ? o.getUnit() : "份");
            BigDecimal minOrderNum = o.getMinOrderNum();
            if (minOrderNum.compareTo(BigDecimal.valueOf(0.001d)) < 0
                    || minOrderNum.compareTo(BigDecimal.valueOf(999.999d)) > 0) {
                continue;
            }
            if (Integer.valueOf(4).equals(itemDO.getItemType())) {
                int isInt = minOrderNum.compareTo(BigDecimal.valueOf(minOrderNum.intValue()));
                if (!Integer.valueOf(0).equals(isInt)) {
                    continue;
                }
            }
            skuDO.setMinOrderNum(o.getMinOrderNum());
            skuDO.setIsWholeDiscount(o.getIsWholeDiscount());  //整单折扣
            skuDO.setIsJoinBuffet(itemDO.getItemType().equals(3) ? 0 : Objects.isNull(o.getIsJoinBuffet()) ? 0 : o.getIsJoinBuffet());    //参与自助点餐机(除称重商品)
            skuDO.setIsJoinWeChat(o.getIsJoinWechat());    //参与微信点餐
            skuDO.setIsRack(Objects.isNull(o.getIsRack()) ? 1 : o.getIsRack());    //上架
            skuDO.setIsJoinAio(o.getIsJoinAio());
            skuDO.setIsJoinPos(o.getIsJoinPos());
            skuDO.setIsJoinPad(o.getIsJoinPad());
            skuDO.setMemberPrice(o.getMemberPrice());  //会员价
            skuDO.setCostPrice(o.getCostPrice()); //成本价
            skuDO.setCode(o.getCode());  //货号
            skuDO.setUpc(o.getUpc());  //商品条码
            //todo 筛选出开启库存的商品
            skuDO.setIsOpenStock(o.getIsOpenStock());  //开启库存
            //todo 请求erp 开启库存
            if (Optional.ofNullable(o).map(ItemExcelTemplateReqDTO::getIsOpenStock).isPresent() && o.getIsOpenStock() == 1) {
                detailList.add(InOutGoodsDTO.builder()
                        .itemType(itemDO.getItemType())
                        .barCode(skuDO.getUpc())
                        .goodsName(itemDO.getName())
                        .goodsGuid(itemDO.getGuid())
                        .goodsCode(skuDO.getCode())
                        .goodsClassifyGuid(itemDO.getTypeGuid())
                        .count(skuDO.getTotalStock())
                        .goodsImage(itemDO.getPictureUrl())
                        .pinyin(itemDO.getPinyin())
                        .safeNum(o.getSafeStock())
                        .count(o.getTotalStock())
                        .unitName(skuDO.getUnit())
                        .unitPrice(skuDO.getSalePrice())
                        .isOpenStock(1)
                        .goodsImage(itemDO.getPictureUrl())
                        .totalAmount(skuDO.getSalePrice())
                        .build());
            }
            skuDO.setSkuFrom(from);
            if (from.equals(ModuleEntranceEnum.STORE.code())) {
                itemDO.setStoreGuid(organizationGuid);
                skuDO.setStoreGuid(organizationGuid);
            } else if (from.equals(ModuleEntranceEnum.BRAND.code())) {
                itemDO.setBrandGuid(organizationGuid);
                skuDO.setBrandGuid(organizationGuid);
            } else {
                // 如果来源参数错误 舍弃该条数据
                continue;
            }
            itemContainer.add(itemDO);
            skuContainer.add(skuDO);
        }
        repertoryReqDTO.setDetailList(detailList);
    }

    /**
     * 批量导入商品 - 构建类型
     *
     * @param toInsertTypeName 类型集合
     * @param typeSort         type排序
     * @param from             来源
     * @param organizationGuid StoreGuid or BrandGuid
     * @return TypeDOList
     */
    private List<TypeDO> buildType(List<String> toInsertTypeName, Integer typeSort, Integer from, String organizationGuid) {
        List<TypeDO> toInsertType = new ArrayList<>(toInsertTypeName.size());
        for (String s : toInsertTypeName) {
            TypeDO typeDO = new TypeDO();
            typeDO.setName(s);
            try {
                typeDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
            } catch (IOException e) {
                throw new BusinessException("BatchIdGenerator生成商品guid失败");
            }
            typeDO.setIsEnable(TRUE);
            typeDO.setTypeFrom(from);
            if (from.equals(ModuleEntranceEnum.STORE.code())) {
                typeDO.setStoreGuid(organizationGuid);
            } else if (from.equals(ModuleEntranceEnum.BRAND.code())) {
                typeDO.setBrandGuid(organizationGuid);
            } else {
                // 如果来源参数错误 舍弃
                continue;
            }
            typeDO.setSort(typeSort++);
            toInsertType.add(typeDO);
        }
        return toInsertType;
    }

    @Override
    public int getSort(ItemSingleDTO itemSingleDTO) {
        if (itemSingleDTO.getFrom().equals(ModuleEntranceEnum.STORE.code())) {
            return this.count(new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getStoreGuid, itemSingleDTO.getData())) + 1;
        } else if (itemSingleDTO.getFrom().equals(ModuleEntranceEnum.BRAND.code())) {
            return this.count(new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getBrandGuid, itemSingleDTO.getData())) + 1;
        } else {
            throw new ParameterException("模块入口参数错误");
        }
    }

    @Override
    public List<TypeItemListDTO> selectTypeItemList(PrintItemReqDto itemReqDto) {
        String storeGuid = itemReqDto.getStoreGuid();
        // 查询生效的菜谱方案
        List<SkuDO> skuDOPlanList = this.pricePlanItemSku(storeGuid, null);
        // 非菜谱方案
        List<SkuDO> skuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>().eq(SkuDO::getStoreGuid, itemReqDto.getStoreGuid())
                .eq(SkuDO::getIsRack, 1).eq(SkuDO::getIsEnable, 1));
        if (!CollectionUtils.isEmpty(skuDOPlanList)) {
            skuDOList.addAll(skuDOPlanList);
        }
        if (CollectionUtils.isEmpty(skuDOList)) {
            throw new BusinessException("当前无上架商品");
        }
        Set<String> itemGuidSet = skuDOList.stream().map(SkuDO::getItemGuid).collect(Collectors.toSet());
        // 获取打印所需商品列表
        List<ItemDO> itemDOList = (List<ItemDO>) listByIds(itemGuidSet);
        //获取品牌商品Guid
        Set<String> brandItemGuids = itemDOList.stream()
                .filter(itemDO -> itemDO.getItemFrom() == 1)
                .map(ItemDO::getGuid).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(brandItemGuids)) {
            //展示了品牌商品就不展示推送到门店的商品
            itemDOList.removeIf(itemDO -> itemDO.getItemFrom() == 2 && brandItemGuids.contains(itemDO.getParentGuid()));
        }
        List<String> bindItemGuids = new ArrayList<>();
        //查询已绑定菜品信息
        if (!CollectionUtils.isEmpty(itemReqDto.getItemGuids())) {
            List<ItemDO> bindItems = list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, itemReqDto.getItemGuids())
                    .or().in(ItemDO::getParentGuid, itemReqDto.getItemGuids()));
            bindItemGuids.addAll(bindItems.stream().map(ItemDO::getGuid).collect(Collectors.toList()));
            bindItemGuids.addAll(bindItems.stream().map(ItemDO::getParentGuid).collect(Collectors.toList()));
        }
        List<ItemDO> pkgList = itemDOList.stream().filter(itemDO -> itemDO.getItemType() == 1).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pkgList)) {
            itemDOList.removeAll(pkgList);
            List<String> pkgGuidList = pkgList.stream().map(ItemDO::getGuid).collect(Collectors.toList());
            List<SubgroupDO> subgroupDOList = subgroupService.list(new LambdaQueryWrapper<SubgroupDO>().in(SubgroupDO::getItemGuid, pkgGuidList));
            List<String> subgroupGuidList = subgroupDOList.stream().map(SubgroupDO::getGuid).collect(Collectors.toList());
            List<RSkuSubgroupDO> skuSubgroupDOS = skuSubgroupService.list(new LambdaQueryWrapper<RSkuSubgroupDO>().in(RSkuSubgroupDO::getSubgroupGuid, subgroupGuidList));
            Set<String> subItemGuidSet = skuSubgroupDOS.stream().map(RSkuSubgroupDO::getItemGuid).collect(Collectors.toSet());
            // 去掉已经上架的商品
            subItemGuidSet.removeAll(itemGuidSet);
            if (!CollectionUtils.isEmpty(subItemGuidSet)) {
                List<ItemDO> subItemDOList = (ArrayList) listByIds(subItemGuidSet);
                itemDOList.addAll(subItemDOList);
            }
        }
        Set<String> typeGuidSet = itemDOList.stream().map(ItemDO::getTypeGuid).collect(Collectors.toSet());
        List<TypeDO> typeDOList = (ArrayList) typeService.listByIds(typeGuidSet);
        //获取品牌库分类Guid
        List<String> brandTypeGuids = typeDOList.stream().filter(typeDO -> typeDO.getTypeFrom() == 1).map(TypeDO::getGuid).collect(Collectors.toList());
        Iterator<TypeDO> iterator = typeDOList.iterator();
        while (iterator.hasNext()) {
            TypeDO typeDO = iterator.next();
            //品牌库存在就不在展示
            if (2 == typeDO.getTypeFrom() && brandTypeGuids.contains(typeDO.getParentGuid())) {
                List<ItemDO> itemDOS = itemDOList.stream().filter(itemDO -> itemDO.getTypeGuid().equals(typeDO.getGuid())).collect(Collectors.toList());
                itemDOS.forEach(itemDO -> itemDO.setTypeGuid(typeDO.getParentGuid()));
                iterator.remove();
            }
        }
        List<TypeItemListDTO> typeItemListDTOList = MapStructUtils.INSTANCE.typeDOList2typeItemListDTOList(typeDOList);
        typeItemListDTOList.forEach(typeItemListDTO -> {
            List<ItemDO> thisTypeItemList = itemDOList.stream().filter(itemDO -> itemDO.getTypeGuid().equals(typeItemListDTO.getGuid())).collect(Collectors.toList());
            List<TypeItemListDTO> thisTypeItemDTOList = new ArrayList<>();
            TypeItemListDTO itemDto;
            for (ItemDO itemDO : thisTypeItemList) {
                itemDto = new TypeItemListDTO();
                itemDto.setGuid(itemDO.getGuid());
                itemDto.setName(itemDO.getName());
                itemDto.setUsed(bindItemGuids.contains(itemDO.getGuid()));
                thisTypeItemDTOList.add(itemDto);
            }
            typeItemListDTO.setItemList(thisTypeItemDTOList);
        });
        return typeItemListDTOList;
    }


    @Override
    public List<SkuDO> pricePlanItemSku(String storeGuid, List<String> skuGuids) {
        List<PricePlanNowDTO> planList = pricePlanStoreService.findPlanStoreGuid(storeGuid);
        if (CollectionUtil.isEmpty(planList)) {
            // 没有菜谱方案
            return new ArrayList<>();
        }
        List<String> planGuidList = new ArrayList<>();
        for (PricePlanNowDTO plan : planList) {
            String planGuid = plan.getPlanGuid();
            planGuidList.add(planGuid);
        }

        // 菜谱 商品列表
        List<PricePlanItemDO> pricePlanItemDOList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                .in(PricePlanItemDO::getIsSoldOut, 0, 1, 3)
                .in(PricePlanItemDO::getPlanGuid, planGuidList)
                .in(!CollectionUtils.isEmpty(skuGuids), PricePlanItemDO::getSkuGuid, skuGuids));
        if (CollectionUtil.isEmpty(pricePlanItemDOList)) {
            // 没有菜谱方案菜品
            return new ArrayList<>();
        }

        List<String> allItemSkuGuidList = pricePlanItemDOList.stream().map(PricePlanItemDO::getSkuGuid).collect(Collectors.toList());
        List<SkuDO> skuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>()
                .in(SkuDO::getGuid, allItemSkuGuidList)
                .eq(SkuDO::getIsRack, 1)
                .eq(SkuDO::getIsEnable, 1));

        // 菜谱商品名称
        Map<String, String> planItemNameMap = buildPlanItemNameMap(pricePlanItemDOList);
        for (SkuDO skuDO : skuDOList) {
            skuDO.setPlanItemName(planItemNameMap.get(skuDO.getGuid()));
        }
        return skuDOList;
    }


    private Map<String, String> buildPlanItemNameMap(List<PricePlanItemDO> planItemDOList) {
        Map<String, String> planNameMap = Maps.newHashMap();
        Map<String, List<PricePlanItemDO>> planSkuMap = planItemDOList.stream()
                .collect(Collectors.groupingBy(PricePlanItemDO::getSkuGuid));
        for (Map.Entry<String, List<PricePlanItemDO>> entry : planSkuMap.entrySet()) {
            String skuGuid = entry.getKey();
            List<PricePlanItemDO> planSkuList = entry.getValue();
            StringBuilder planItemSkuName = new StringBuilder();
            planSkuList.forEach(item -> {
                if (planItemSkuName.toString().contains(item.getPlanItemName())) {
                    return;
                }
                planItemSkuName.append(item.getPlanItemName());
                planItemSkuName.append("、");
            });
            if (!StringUtils.isEmpty(planItemSkuName)) {
                String resultStr = planItemSkuName.substring(0, planItemSkuName.length() - 1);
                planNameMap.put(skuGuid, resultStr);
            }
        }
        return planNameMap;
    }

    private List<ItemSynRespDTO> listItemSynRespDTOByIds(List<String> skuGuidList, List<String> storeGuidList) {
        // 被推送的规格集合
        List<SkuDO> skuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>().in(SkuDO::getGuid, skuGuidList));
        List<String> skuGuidSet = skuDOList.stream()
                .map(SkuDO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        // 直接被推送商品的GUID集合
        List<String> itemGuidSet = skuDOList.stream()
                .map(SkuDO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        // 被推送的商品集合
        List<ItemDO> itemDOList = this.list(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getGuid, itemGuidSet)
                .orderByAsc(ItemDO::getSort)
                .orderByDesc(ItemDO::getGmtCreate)
        );

        // 该门店下套餐集合
        List<String> pkgGuidList = itemDOList.stream()
                .filter(itemDO -> itemDO.getItemType() == 1)
                .map(ItemDO::getGuid).collect(Collectors.toList());
        // 所有套餐子商品的规格GUID集合
        List<String> subItemSkuGuidList = new ArrayList<>();
        // 子商品的商品GUID集合
        Set<String> subItemItemGuidLSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(pkgGuidList)) {
            List<RSkuSubgroupDO> subgroupList = subgroupService.selectSkuSubgroupByItemGuidList(pkgGuidList);
            subItemSkuGuidList = new ArrayList<>(subgroupList.stream().map(RSkuSubgroupDO::getSkuGuid).collect(Collectors.toSet()));
            subItemItemGuidLSet = subgroupList.stream().map(RSkuSubgroupDO::getItemGuid).collect(Collectors.toSet());
            // 仅剩下未被直接推送的子商品GUID
            if (!CollectionUtils.isEmpty(subItemItemGuidLSet)) {
                subItemItemGuidLSet.removeAll(itemGuidSet);
            }
            // 仅剩下未被直接推送的子商品规格GUID
            if (!CollectionUtils.isEmpty(subItemSkuGuidList)) {
                subItemSkuGuidList.removeAll(skuGuidSet);
            }
        }

        if (!CollectionUtils.isEmpty(subItemSkuGuidList)) {
            List<SkuDO> subItemSkuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>().in(SkuDO::getGuid, subItemSkuGuidList));
            // 将子商品的规格添加到被推送的所有规格集合中
            skuDOList.addAll(subItemSkuDOList);
        }
        if (!CollectionUtils.isEmpty(subItemItemGuidLSet)) {
            List<ItemDO> subItemDOList = list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, subItemItemGuidLSet));
            itemDOList.addAll(subItemDOList);
        }
        List<ItemSynRespDTO> itemSynRespDTOList = MapStructUtils.INSTANCE.itemDOList2itemSynRespDTOList(itemDOList);
        // 导入商品后对分类进行重新排序
        List<String> typeGuidList = itemSynRespDTOList.stream().map(ItemSynRespDTO::getTypeGuid).distinct().collect(Collectors.toList());
        List<ItemDO> itemByTypeGuidList = this.list(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getTypeGuid, typeGuidList)
                .eq(ItemDO::getIsDelete, 0)
                .orderByAsc(ItemDO::getSort)
        );
        Map<String, ItemDO> byItemGuidMap = itemByTypeGuidList.stream()
                .collect(Collectors.toMap(ItemDO::getGuid, Function.identity(), (key1, key2) -> key2));
        Map<String, List<ItemDO>> itemByTypeGuidMap = itemByTypeGuidList.stream().collect(Collectors.groupingBy(ItemDO::getTypeGuid));
        Map<String, List<ItemSynRespDTO>> typeMap = itemSynRespDTOList.stream()
                .collect(Collectors.groupingBy(ItemSynRespDTO::getTypeGuid));
        if (!CollectionUtils.isEmpty(typeGuidList)) {
            typeGuidList.forEach(
                    typeGuid -> {
                        ArrayList<ItemDO> updateList = new ArrayList<>();
                        for (String s : storeGuidList) {
                            TypeDO typeDO = typeService.getOne(new LambdaQueryWrapper<TypeDO>()
                                    .eq(TypeDO::getStoreGuid, s)
                                    .eq(TypeDO::getParentGuid, typeGuid));
                            if (null == typeDO) {
                                continue;
                            }
                            List<ItemDO> typeItemList = itemByTypeGuidMap.get(typeGuid);
                            typeItemList.sort(Comparator.comparing(ItemDO::getSort, Comparator.nullsLast(Comparator.naturalOrder()))
                                    .thenComparing(ItemDO::getGmtModified, Comparator.reverseOrder()));
                            for (int j = 0; j < typeItemList.size(); j++) {
                                ItemDO itemDO = typeItemList.get(j);
                                itemDO.setSort(j + 1);
                                itemDO.setGmtModified(null);
                            }
                            updateList.addAll(typeItemList);

                            // 设置推送的商品的序号
                            List<ItemSynRespDTO> itemSynRespDTOS = typeMap.get(typeGuid);
                            for (ItemSynRespDTO respDTO : itemSynRespDTOS) {
                                respDTO.setSort(byItemGuidMap.get(respDTO.getItemGuid()).getSort());
                            }
                        }
                        if (!CollectionUtils.isEmpty(updateList)) {
                            this.updateBatchById(updateList);
                        }
                    }
            );
        }
        List<ItemSynRespDTO> itemList = new ArrayList<>(itemSynRespDTOList);
        log.info("推送商品：{}", JacksonUtils.writeValueAsString(itemList));
        List<SkuSynRespDTO> skuSynRespDTOList = MapStructUtils.INSTANCE.skuDOList2skuSynRespDTOList(skuDOList);
        // 关联ITEM与SKU以及属性(仅GUID正确，具体详情需再次查询数据库)
        attchAttr2Item(itemList, itemDOList);
        attchSku2Item(itemList, skuSynRespDTOList);
        attachSubGroupAndSubItem2Pkg(itemList, itemList, skuDOList);
        return itemList;
    }

    private void attchSku2Item(List<ItemSynRespDTO> itemSynRespDTOList, List<SkuSynRespDTO> skuSynRespDTOList) {
        itemSynRespDTOList.forEach(itemSynRespDTO -> {
            // 当前商品所含规格
            List<SkuSynRespDTO> thisItemSkuList = skuSynRespDTOList.stream()
                    .filter(skuSynRespDTO -> itemSynRespDTO.getItemGuid().equals(skuSynRespDTO.getItemGuid()))
                    .collect(Collectors.toList());
            itemSynRespDTO.setSkuList(thisItemSkuList);
        });
    }

    /**
     * 获取一共要被推送的pair
     *
     * @param storeGuidList
     * @param pushBOGuidList
     * @return
     */
    private List<Pair<String, String>> getPushBOPairList(List<String> storeGuidList, List<String> pushBOGuidList) {
        Set<String> pushBOGuidSet = new HashSet<>(pushBOGuidList);
        // 需要的分类的pair集合（由type的parentGuid以及storeGuid确定是否为需要的分类）
        List<Pair<String, String>> pushBOPairList = new ArrayList<>();
        pushBOGuidSet.forEach(pushBOGuid -> {
            storeGuidList.forEach(storeGuid -> {
                Pair<String, String> typePair = Pair.of(pushBOGuid, storeGuid);
                pushBOPairList.add(typePair);
            });
        });
        return pushBOPairList;
    }

    /**
     * 获取推送实体对应的pair
     *
     * @param storePushBOList
     * @return
     */
    private List<Pair<String, String>> getPushBOPairListByPushBOList(List<PushBO> storePushBOList) {
        List<Pair<String, String>> pushBOPairList = new ArrayList<>();
        storePushBOList.forEach(pushBO -> {
            Pair<String, String> pair = Pair.of(pushBO.getParentGuid(), pushBO.getStoreGuid());
            pushBOPairList.add(pair);
        });
        return pushBOPairList;
    }

    private void validatePushItemDTO(PushItemReqDTO pushItemReqDTO) {
        if (CollectionUtils.isEmpty(pushItemReqDTO.getSkuGuidList()) || CollectionUtils.isEmpty(pushItemReqDTO.getStoreGuidList())) {
            throw new ParameterException("商品以及被推送门店不能为空");
        }
    }

    private void fixItemInfoRespDTOFieldForWeb(ItemInfoRespDTO itemInfoRespDTO) {
        // 若商品是单品，则给前端转化为规格商品（数据库仍然保存为单品类型）
        if (itemInfoRespDTO.getItemType() == 4) {
            itemInfoRespDTO.setItemType(2);
        }
    }

    public List<SubgroupWebRespDTO> selectSubgroupList(String itemGuid) {
        List<SubgroupBO> subgroupBOList = subgroupService.selectSubgroupListByItemGuidList(Arrays.asList(itemGuid));
        List<SubgroupWebRespDTO> subgroupWebRespDTOList =
                MapStructUtils.INSTANCE.subgroupBOList2subgroupWebRespDTOList(subgroupBOList);
        // 子商品SKUGuid的集合
        List<String> skuGuidList = new ArrayList<>();
        for (SubgroupBO subgroupBO : subgroupBOList) {
            List<SubItemSkuBO> subItemSkuList = subgroupBO.getSubItemSkuList();
            for (SubItemSkuBO subItemSkuBO : subItemSkuList) {
                skuGuidList.add(subItemSkuBO.getSkuGuid());
            }
        }
        List<SkuDO> skuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>().in(SkuDO::getGuid, skuGuidList));
        Set<String> itemGuidSet = skuDOList.stream().map(SkuDO::getItemGuid).collect(Collectors.toSet());
        List<ItemDO> itemDOList = list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, itemGuidSet));
        subgroupWebRespDTOList.forEach(subgroupWebRespDTO -> {
            List<SubItemSkuWebRespDTO> subItemSkuList = subgroupWebRespDTO.getSubItemSkuList();
            // 当前分组下的子商品实体
            List<SubItemSkuWebRespDTO> subItemSkuWebRespDTOList =
                    MapStructUtils.INSTANCE.subItemSkuList2subItemSkuWebRespDTOList(subItemSkuList);
            subItemSkuWebRespDTOList.forEach(subItemSkuWebRespDTO -> {
                ItemDO thisItem = itemDOList.stream()
                        .filter(itemDO -> itemDO.getGuid().equals(subItemSkuWebRespDTO.getItemGuid()))
                        .findFirst()
                        .orElse(null);
                SkuDO thisSkuDO = skuDOList.stream()
                        .filter(skuDO -> skuDO.getGuid().equals(subItemSkuWebRespDTO.getSkuGuid()))
                        .findFirst()
                        .orElse(null);
                if (ObjectUtils.isEmpty(thisItem) || ObjectUtils.isEmpty(thisSkuDO)) {
                    log.warn("未查询到商品或规格信息 thisItem={},thisSkuDO={}", JacksonUtils.writeValueAsString(thisItem),
                            JacksonUtils.writeValueAsString(thisSkuDO));
                    return;
                }
                fillSubItemSkuWebRespDTOFields(subItemSkuWebRespDTO, thisItem, thisSkuDO);
            });
            subgroupWebRespDTO.setSubItemSkuList(subItemSkuWebRespDTOList);
            subgroupWebRespDTO.setIsFixSubgroup(subgroupWebRespDTO.getPickNum() == 0 ? 1 : 0);
        });
        return subgroupWebRespDTOList;
    }

    private void fillSubItemSkuWebRespDTOFields(SubItemSkuWebRespDTO subItemSkuSynRespDTO, ItemDO thisItem,
                                                SkuDO thisSkuDO) {
        subItemSkuSynRespDTO.setHasAttr(thisItem.getHasAttr());
        subItemSkuSynRespDTO.setItemName(thisItem.getName());
        subItemSkuSynRespDTO.setSkuName(thisSkuDO.getName());
        subItemSkuSynRespDTO.setSalePrice(thisSkuDO.getSalePrice());
        subItemSkuSynRespDTO.setTypeGuid(thisSkuDO.getTypeGuid());
        subItemSkuSynRespDTO.setPictureUrl(thisItem.getPictureUrl());
        subItemSkuSynRespDTO.setCode(thisItem.getCode());

        subItemSkuSynRespDTO.setName(StringUtils.isEmpty(thisSkuDO.getName()) ? thisItem.getName() :
                thisItem.getName() + "(" + thisSkuDO.getName() + ")");
        subItemSkuSynRespDTO.setItemType(thisItem.getItemType());
        subItemSkuSynRespDTO.setUnit(thisSkuDO.getUnit());
    }

    /**
     * 获取指定商品下的属性组
     *
     * @param itemGuid
     * @return
     */
    public List<AttrGroupWebRespDTO> selectItemAttrList(String itemGuid) {
        // 商品与属性组关联关系实体集合
        List<RItemAttrGroupDO> itemAttrGroupDOList = itemAttrGroupService.list(new LambdaQueryWrapper<RItemAttrGroupDO>()
                .eq(RItemAttrGroupDO::getItemGuid, itemGuid));
        if (CollectionUtils.isEmpty(itemAttrGroupDOList)) {
            log.warn("商品属性关联有误，商品guid:{}", itemGuid);
            return new ArrayList<>();
        }
        // 商品与属性组关联实体GUID集合
        List<String> itermAttrGroupDOGuidList = itemAttrGroupDOList.stream().map(RItemAttrGroupDO::getGuid).collect(Collectors.toList());
        // 属性与商品属性组关联实体集合
        List<RAttrItemAttrGroupDO> attrItemAttrGroupDOList = attrItemAttrGroupService.list(new LambdaQueryWrapper<RAttrItemAttrGroupDO>().in(RAttrItemAttrGroupDO::getItemAttrGroupGuid, itermAttrGroupDOGuidList));
        // 涉及的所有属性组GUID集合
        Set<String> attrGroupGuidSet = itemAttrGroupDOList.stream().map(RItemAttrGroupDO::getAttrGroupGuid).collect(Collectors.toSet());
        // 涉及到的所有属性的GUID集合
        Set<String> attrGuidSet = attrItemAttrGroupDOList.stream().map(RAttrItemAttrGroupDO::getAttrGuid).collect(Collectors.toSet());
        // 涉及的所有属性实体集合
        List<AttrGroupDO> attrGroupDOList = attrGroupService.list(new LambdaQueryWrapper<AttrGroupDO>().in(AttrGroupDO::getGuid, attrGroupGuidSet));
        // 涉及的所有属性实体的集合
        List<AttrDO> attrDOList = attrService.list(new LambdaQueryWrapper<AttrDO>().in(AttrDO::getGuid, attrGuidSet));
        List<AttrGroupWebRespDTO> attrGroupWebRespDTOList = new ArrayList<>();
        itemAttrGroupDOList.forEach(itemAttrGroupDO -> {
            AttrGroupWebRespDTO attrGroupWebRespDTO = MapStructUtils.INSTANCE.rItemAttrGroupDO2attrGroupWebRespDTO(itemAttrGroupDO);
            AttrGroupDO groupDO = attrGroupDOList.stream().filter(attrGroupDO -> attrGroupDO.getGuid().equals(attrGroupWebRespDTO.getAttrGroupGuid())).findFirst().orElse(null);
            if (ObjectUtils.isEmpty(groupDO)) {
                return;
            }
            attrGroupWebRespDTO.setName(groupDO.getName());
            attrGroupWebRespDTO.setIconUrl(groupDO.getIconUrl());
            attrGroupWebRespDTO.setStoreGuid(groupDO.getStoreGuid());
            attrGroupWebRespDTO.setBrandGuid(groupDO.getBrandGuid());
            attrGroupWebRespDTO.setAttrGroupFrom(groupDO.getAttrGroupFrom());
            List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOList = attrItemAttrGroupDOList.stream()
                    .filter(rAttrItemAttrGroupDO -> rAttrItemAttrGroupDO.getItemAttrGroupGuid().equals(itemAttrGroupDO.getGuid()))
                    .collect(Collectors.toList());
            List<AttrWebRespDTO> attrWebRespDTOList = getAttrWebRespDTOList(rAttrItemAttrGroupDOList, attrDOList);
            attrGroupWebRespDTO.setAttrList(attrWebRespDTOList);
            attrGroupWebRespDTOList.add(attrGroupWebRespDTO);
        });
        return attrGroupWebRespDTOList;
    }


    /**
     * 根据属性与商品属性组关联实体以及对应属性实体来获取返回给前端的属性实体
     *
     * @param rAttrItemAttrGroupDOList 属性与商品属性组关联实体
     * @param attrDOList               对应属性实体
     * @return 返回给前端的属性实体
     */
    private List<AttrWebRespDTO> getAttrWebRespDTOList(List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOList, List<AttrDO> attrDOList) {
        List<AttrWebRespDTO> attrWebRespDTOList = new ArrayList<>();
        rAttrItemAttrGroupDOList.forEach(rAttrItemAttrGroupDO -> {
            AttrWebRespDTO attrWebRespDTO = MapStructUtils.INSTANCE.attrItemAttrGroupDO2attrWebRespDTO(rAttrItemAttrGroupDO);
            AttrDO attrDO = attrDOList.stream().filter(ad -> ad.getGuid().equals(attrWebRespDTO.getAttrGuid())).findFirst().orElse(null);
            if (ObjectUtils.isEmpty(attrDO)) {
                return;
            }
            attrWebRespDTO.setName(attrDO.getName());
            attrWebRespDTO.setPrice(attrDO.getPrice());
            attrWebRespDTO.setStoreGuid(attrDO.getStoreGuid());
            attrWebRespDTO.setBrandGuid(attrDO.getBrandGuid());
            attrWebRespDTO.setAttrFrom(attrDO.getAttrFrom());
            attrWebRespDTOList.add(attrWebRespDTO);
        });
        return attrWebRespDTOList;
    }

    @Override
    @Transactional
    public Integer removePush(String storeGuid) {
        // fixme 该接口有一个弊端：修改一次门店所属品牌，则所有被推送实体都会被移除。
        // 删除被推送到该门店的商品
        List<ItemDO> pushedItemList = list(new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getStoreGuid, storeGuid).eq(ItemDO::getItemFrom, 2));
        if (!CollectionUtils.isEmpty(pushedItemList)) {
            // 被推送商品的GUID集合
            Set<String> itemGuidSet = pushedItemList.stream().map(ItemDO::getGuid).collect(Collectors.toSet());
            ItemStringListDTO itemStringListDTO = new ItemStringListDTO();
            itemStringListDTO.setDataList(new ArrayList<>(itemGuidSet));
            batchDelete(itemStringListDTO, Boolean.FALSE);
        }
        // 删除被推送到该门店的分类
        typeService.removePushType(storeGuid);
        attrService.removePushAttr(storeGuid);
        attrGroupService.removePushAttrGroup(storeGuid);
        return 1;
    }

    @Override
    public List<ItemInfoRespDTO> kdsItemParentMapping(List<String> itemGuidList) {
        if (CollectionUtils.isEmpty(itemGuidList)) {
            return new ArrayList<>();
        }
        return itemMapper.selectItemWithParentGuidList(itemGuidList);
    }

    @Override
    public Page<ItemTemplateSubItemRespDTO> selectSkuItemList(ItemTemplateMenuAllSubItemReqDTO request) {
        IPage<ItemTemplateSubItemRespDTO> page = itemMapper.getSkuItemList(new PageAdapter<>(request), request);
        log.info("result data= {}", JacksonUtils.writeValueAsString(page));
        return new PageAdapter<>(page, page.getRecords());
    }

    @Override
    public List<ItemBatchImportTempRespDTO> selectItemsBeforeImport(BatchImportGetItemsReqDTO request) {
        return itemMapper.getItemsBeforeImport(request);
    }

    @Override
    public List<JournalingItemRespDTO> selectJournalingItem() {
        List<JournalingItemRespDTO> list = new ArrayList<>();
        List<JournalingItemsQuery> journalingItem = itemMapper.getJournalingItem();
        Map<String, List<JournalingItemsQuery>> brandPushItems = journalingItem.stream()
                .filter(o -> o.getItemFrom() == 2)
                .collect(Collectors.groupingBy(JournalingItemsQuery::getParentGuid));
        List<JournalingItemsQuery> storeItems = journalingItem.stream()
                .filter(o -> o.getItemFrom() == 0).collect(Collectors.toList());
        brandPushItems.forEach((k, v) -> {
            JournalingItemRespDTO build = JournalingItemRespDTO
                    .builder()
                    .itemFrom(2)
                    .guid(k)
                    .name(v.get(0).getName())
                    .subGuids(v.stream().map(JournalingItemsQuery::getGuid).collect(Collectors.toList()))
                    .build();
            list.add(build);
        });
        storeItems.forEach(o -> {
            JournalingItemRespDTO build = JournalingItemRespDTO
                    .builder()
                    .itemFrom(o.getItemFrom())
                    .guid(o.getGuid())
                    .name(o.getName())
                    .build();
            list.add(build);
        });
        return list;
    }

    @Override
    public List<ItemImgDTO> selectItemPictureUrls(SingleDataDTO request) {
        List<Map<String, Object>> listMaps = listMaps(new QueryWrapper<ItemDO>().select("guid", "picture_url").in("guid", request.getDatas()));
        List<ItemImgDTO> itemImgDTOS = new ArrayList<>();
        listMaps.forEach(o ->
                itemImgDTOS.add(
                        ItemImgDTO.builder()
                                .itemGuid(o.get("guid").toString())
                                .imgUrl(Objects.isNull(o.get("picture_url"))
                                        ? "" : o.get("picture_url").toString())
                                .build()
                )
        );
        return itemImgDTOS;
    }

    @Override
    public GroupMealItemDetailRespDTO selectGroupMealDetail(String data) {
        //获取宴会套餐详情 商品
        ItemDO itemDO = getById(data);
        //获取宴会套餐详情 规格
        SkuDO skuDO = skuService.getOne(new LambdaQueryWrapper<SkuDO>().eq(SkuDO::getItemGuid, itemDO.getGuid()));
        GroupMealItemDetailRespDTO groupMealItemDetailRespDTO = MapStructUtils.INSTANCE.itemDO2GroupMealItemDetailRespDTO(itemDO);
        SkuInfoRespDTO skuInfoRespDTO = MapStructUtils.INSTANCE.skuDO2skuInfoRespDTO(skuDO);
        groupMealItemDetailRespDTO.setSkuList(Arrays.asList(skuInfoRespDTO));
        //获取宴会套餐下面子菜集合
        List<GroupMealSubItemBO> groupMealSubItemBOS = itemMapper.getGroupMealSubItemS(data);
        if (CollectionUtils.isEmpty(groupMealSubItemBOS)) {
            log.error("宴会套餐子菜品为空，商品guid：{}", data);
            return groupMealItemDetailRespDTO;
        }
        List<GroupMealSubgroupWebRespDTO> groupMealSubgroupList = new ArrayList<>();
        groupMealSubItemBOS
                .stream()
                .collect(Collectors.groupingBy(GroupMealSubItemBO::getTypeName))
                .forEach((k, v) -> {
                    GroupMealSubgroupWebRespDTO groupMealSubgroupWebRespDTO = new GroupMealSubgroupWebRespDTO();
                    groupMealSubgroupWebRespDTO.setTypeSkuQuantity(v.size());  //已选商品N道
                    groupMealSubgroupWebRespDTO.setTypeSkuNUmber(v
                            .stream()
                            .mapToInt(GroupMealSubItemBO::getNum)
                            .sum());  //已选商品N份
                    groupMealSubgroupWebRespDTO.setTypeSkuSumPrice(v
                            .stream()
                            .map(GroupMealSubItemBO::getSalePrice)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));  //销售合计
                    groupMealSubgroupWebRespDTO.setTypeSkuSumCostPrice(v
                            .stream()
                            .filter(o -> Objects.nonNull(o.getCostPrice()))
                            .map(GroupMealSubItemBO::getCostPrice)
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                    ); //总成本计算
                    groupMealSubgroupWebRespDTO.setName(k);
                    List<GroupMealSubItemSkuWebRespDTO> groupMealSubItemSkuWebRespDTOS = MapStructUtils.INSTANCE.subGroupMealBOS2GroupMealSubItemS(
                            v
                                    .stream()
                                    .sorted(Comparator.comparing(GroupMealSubItemBO::getSort)
                                    ).collect(Collectors.toList()));
                    groupMealSubgroupWebRespDTO.setGroupMealSubItemSkuList(groupMealSubItemSkuWebRespDTOS);
                    groupMealSubgroupList.add(groupMealSubgroupWebRespDTO);
                });
        groupMealItemDetailRespDTO.setGroupMealSubgroupList(groupMealSubgroupList); //设置分类及其子菜
        groupMealItemDetailRespDTO.setSumSkuQuantity(groupMealSubgroupList
                .stream()
                .mapToInt(GroupMealSubgroupWebRespDTO::getTypeSkuQuantity)
                .sum());  //总道数
        groupMealItemDetailRespDTO.setSumSkuNUmber(groupMealSubgroupList
                .stream()
                .mapToInt(GroupMealSubgroupWebRespDTO::getTypeSkuNUmber)
                .sum());  //总份数
        //判断是否计算总毛利润  如果有成本价为null，则不计算
        boolean flag = groupMealSubItemBOS
                .stream()
                .allMatch(
                        groupMealSubItemBO
                                -> Objects.nonNull(groupMealSubItemBO.getCostPrice()));
        if (flag && groupMealItemDetailRespDTO.getSkuList().get(0).getSalePrice().compareTo(BigDecimal.ZERO) != 0
                && !CollectionUtils.isEmpty(groupMealItemDetailRespDTO.getGroupMealSubgroupList())) {
            //计算总毛利率  销售价-总成本价/销售价
            groupMealItemDetailRespDTO.setGrossMargin(
                    (groupMealItemDetailRespDTO.getSkuList().get(0).getSalePrice()
                            .subtract(
                                    groupMealSubgroupList
                                            .stream()
                                            .map(GroupMealSubgroupWebRespDTO::getTypeSkuSumCostPrice)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            ))
                            .divide(groupMealItemDetailRespDTO.getSkuList().get(0).getSalePrice(), 4, RoundingMode.HALF_UP));
        }

        return groupMealItemDetailRespDTO;
    }

    @Override
    public Boolean updateHasAttr(String itemGuid, Integer hasAttr) {
        return itemMapper.updateHasAttr(itemGuid, hasAttr);
    }

    @Override
    public List<String> getHasAttrItemGuid() {
        return itemMapper.getHasAttrItemGuid();
    }

    @Override
    @Transactional
    public Integer batchUpdate(ItemBatchUpdateDTO itemBatchUpdateDTO) {
        List<String> itemGuidList = itemBatchUpdateDTO.getItemList();
        if (itemBatchUpdateDTO.getIsWholeDiscount() != null) {
            ItemWholeDiscountDTO itemWholeDiscountDTO = new ItemWholeDiscountDTO();
            itemWholeDiscountDTO.setWholeDiscountState(itemBatchUpdateDTO.getIsWholeDiscount());
            itemWholeDiscountDTO.setItemGuidList(itemGuidList);
            skuService.wholeDiscount(itemWholeDiscountDTO);
        }

        ItemRackDTO rack = new ItemRackDTO();
        rack.setRackState(itemBatchUpdateDTO.getIsRack());
        BeanUtils.copyProperties(itemBatchUpdateDTO, rack);
        rack.setItemGuidList(itemGuidList);
        skuService.rack(rack);

        List<ItemDO> itemList = itemGuidList.stream().map(itemGuid -> {
            ItemDO item = new ItemDO();
            BeanUtils.copyProperties(itemBatchUpdateDTO, item);
            item.setGuid(itemGuid);
            return item;
        }).collect(Collectors.toList());
        this.updateBatchById(itemList);


        return 1;
    }

    @Override
    @Transactional
    public Boolean switchSort(@Valid ItemSortSwitchReqDTO itemReqDTO) {
        ItemDO sourceItem = this.getById(itemReqDTO.getSourceItemGuid());
        ItemDO targetItem = this.getById(itemReqDTO.getTargetItemGuid());
        if (sourceItem == null || targetItem == null) {
            return false;
        }
        long srcId = sourceItem.getId();
        Integer srcSort = sourceItem.getSort();
        this.baseMapper.deleteByGuid(sourceItem.getGuid());
        this.baseMapper.deleteByGuid(targetItem.getGuid());
        sourceItem.setId(targetItem.getId());
        sourceItem.setSort(targetItem.getSort());
        targetItem.setId(srcId);
        targetItem.setSort(srcSort);

        boolean ret2 = this.save(targetItem);
        boolean ret1 = this.save(sourceItem);
        if (!ret1 || !ret2) {
            throw new BusinessException("商品类型排序交换失败");
        }
        return true;
    }

    @Override
    public List<ItemSearchRespDTO> search(ItemSearchReqDTO itemSearchReqDTO) {
        ItemDO itemDO = new ItemDO();
        BeanUtils.copyProperties(itemSearchReqDTO, itemDO);
        LambdaQueryWrapper<ItemDO> lambdaQueryWrapper = new LambdaQueryWrapper<>(itemDO);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemSearchReqDTO.getItemGuidList())) {
            lambdaQueryWrapper.in(ItemDO::getGuid, itemSearchReqDTO.getItemGuidList());
        }
        List<ItemDO> itemList = this.list(lambdaQueryWrapper);
        List<String> itemGuidList = itemList.stream().map(ItemDO::getGuid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemGuidList)) {
            return new ArrayList<>();
        }
        List<SkuDO> skuList = skuService.selectSearchSkuByItemGUid(itemGuidList);
        Map<String, SkuDO> skuMap = new HashMap<>();
        skuList.forEach(s -> skuMap.put(s.getItemGuid(), s));
        return this.list(lambdaQueryWrapper).stream().map(item -> {
            SkuDO sku = skuMap.get(item.getGuid());
            ItemSearchRespDTO dto = new ItemSearchRespDTO();
            if (!ObjectUtils.isEmpty(sku)) {
                if (itemSearchReqDTO.getIsRack() != null && sku.getIsRack() != null && !itemSearchReqDTO.getIsRack().equals(sku.getIsRack())) {
                    return null;
                }
                BeanUtils.copyProperties(sku, dto);
            }
            BeanUtils.copyProperties(item, dto);
            return dto;
        }).filter(t -> !ObjectUtils.isEmpty(t)).collect(Collectors.toList());
    }

    @Override
    public Integer voteUp(ItemVoteReqDTO itemVoteReqDTO) {
        return this.baseMapper.voteUp(itemVoteReqDTO.getItemGuid());
    }

    @Override
    public Integer voteDown(ItemVoteReqDTO itemVoteReqDTO) {
        return this.baseMapper.voteDown(itemVoteReqDTO.getItemGuid());
    }


    public ItemInfoAndTypeRespDTO newListItemInfoAndType(ItemStringListDTO itemStringListDTO) {
        //根据门店guid获取门店下商品信息列表
        List<ItemInfoRespDTO> itemInfoRespDTOS = this.queryItemInfoByStoreGuidList(itemStringListDTO);

//        List<ItemEstimateForAndroidRespDTO> estimateSkuList = new ArrayList<>();
//        itemStringListDTO.getDataList().forEach(storeGuid -> {
//            BaseDTO baseDTO = new BaseDTO();
//            baseDTO.setStoreGuid(storeGuid);
//            estimateSkuList.addAll(queryEstimateForSyn(baseDTO));
//        });
        // 装入sku
//        setSkuList(itemInfoRespDTOS, estimateSkuList);

        // 设置属性组
//        itemInfoRespDTOS.forEach(item -> {
//                    List<AttrGroupWebRespDTO> attrGroupList = selectItemAttrList(item.getItemGuid());
//                    attrGroupList.forEach(attrGroup -> attrGroup.setAttrList(transformAttr(attrGroup)));
//                    item.setAttrGroupList(attrGroupList);
//                }
//        );

        // 设置套餐组
//        itemInfoRespDTOS.forEach(item -> item.setSubgroupList(transformSubgroup(item)));

        //根据门店guidList批量获取分类信息
//        List<TypeWebRespDTO> typeWebRespDTOS = typeService.queryTypeByStoreGuidList(itemStringListDTO);


        //获取分类
        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData(itemStringListDTO.getStoreGuid());
        List<TypeWebRespDTO> typeWebRespDTOS = typeService.queryType(itemSingleDTO);
        log.info("获取typeWebRespDTOS={}", JacksonUtils.writeValueAsString(typeWebRespDTOS));

        List<String> itemList = itemStringListDTO.getItemList();

//        List<ItemInfoRespDTO> dtoList = itemInfoRespDTOS.stream().filter(n -> StringUtils.isEmpty(n.getParentGuid()) || StringUtils.isEmpty(n.getItemGuid()))
//                .collect(Collectors.toList());
//        log.info("问题商品: {}", JacksonUtils.writeValueAsString(dtoList));

        log.info("菜谱模式下过滤前商品: {}", JacksonUtils.writeValueAsString(itemList));
        itemInfoRespDTOS = itemInfoRespDTOS.stream()
                .filter(i -> (!StringUtils.isEmpty(i.getParentGuid()) && itemList.contains(i.getParentGuid()))
                        || itemList.contains(i.getItemGuid()))
                .collect(Collectors.toList());
        log.info("菜谱模式下过滤后商品: {}", JacksonUtils.writeValueAsString(itemInfoRespDTOS));

        ItemInfoAndTypeRespDTO itemInfoAndTypeRespDTO = new ItemInfoAndTypeRespDTO();
        itemInfoAndTypeRespDTO.setTypeList(typeWebRespDTOS);
        itemInfoAndTypeRespDTO.setItemInfoList(itemInfoRespDTOS);
        return itemInfoAndTypeRespDTO;
    }

    @Override
    public ItemInfoAndTypeRespDTO listItemInfoAndType(ItemStringListDTO itemStringListDTO) {
        //根据门店guid获取门店下商品信息列表
        List<ItemInfoRespDTO> itemInfoRespDTOS = this.queryItemInfoByStoreGuidList(itemStringListDTO);

        List<ItemEstimateForAndroidRespDTO> estimateSkuList = new ArrayList<>();
        itemStringListDTO.getDataList().forEach(storeGuid -> {
            BaseDTO baseDTO = new BaseDTO();
            baseDTO.setStoreGuid(storeGuid);
            estimateSkuList.addAll(queryEstimateForSyn(baseDTO));
        });
        // 装入sku
        setSkuList(itemInfoRespDTOS, estimateSkuList);

        // 设置属性组
        itemInfoRespDTOS.forEach(item -> {
                    List<AttrGroupWebRespDTO> attrGroupList = selectItemAttrList(item.getItemGuid());
                    attrGroupList.forEach(attrGroup -> attrGroup.setAttrList(transformAttr(attrGroup)));
                    item.setAttrGroupList(attrGroupList);
                }
        );

        // 设置套餐组
        itemInfoRespDTOS.forEach(item -> item.setSubgroupList(transformSubgroup(item)));

        //根据门店guidList批量获取分类信息
        List<TypeWebRespDTO> typeWebRespDTOS = typeService.queryTypeByStoreGuidList(itemStringListDTO);

        ItemInfoAndTypeRespDTO itemInfoAndTypeRespDTO = new ItemInfoAndTypeRespDTO();
        itemInfoAndTypeRespDTO.setTypeList(typeWebRespDTOS);
        itemInfoAndTypeRespDTO.setItemInfoList(itemInfoRespDTOS);
        return itemInfoAndTypeRespDTO;
    }

    @Override
    public List<ItemInfoRespDTO> queryItemInfoByStoreGuidList(ItemStringListDTO itemStringListDTO) {
        Assert.notEmpty(itemStringListDTO.getDataList(), "storeGuid集合不能为空");
        List<ItemDO> itemDOList = list(
                new LambdaQueryWrapper<ItemDO>()
                        .in(ItemDO::getStoreGuid, itemStringListDTO.getDataList())
                        .eq(ItemDO::getIsDelete, 0)
                        .eq(ItemDO::getIsEnable, 1)
        );
        if (CollectionUtils.isEmpty(itemDOList)) {
            log.info(NO_ITEM_WITHIN_STORES);
            return new ArrayList<>();
        }
        return MapStructUtils.INSTANCE.itemDOList2ItemInfoRespDTOList(itemDOList);
    }

    @Override
    public List<Map<String, Object>> getItemInfoListByParentId(String parentItemGuid, String storeGuid) {
        return itemMapper.getItemInfoListByParentId(parentItemGuid, storeGuid);
    }

    @Override
    public Integer removeItemByParentId(String parentItemGuid, String storeGuid) {
        return itemMapper.removeItemByParentId(parentItemGuid, storeGuid);
    }

    @Override
    public String getGuidByStoreGuidAndParentGuid(String storeGuid, String parentGuid) {
        return itemMapper.getGuidByStoreGuidAndParentGuid(storeGuid, parentGuid);
    }

    @Override
    public Integer deleteByGuidAndFrom(String guid, Long itemFrom, String storeGuid) {
        return itemMapper.deleteByGuidAndFrom(guid, itemFrom, storeGuid);
    }

    @Override
    public Integer updateOriginItemStoreGuid(String itemGuid, String storeGuid, String brandGuid) {
        return itemMapper.updateOriginItemStoreGuid(itemGuid, storeGuid, brandGuid);
    }

    @Override
    public List<Map<String, Object>> getItemsByStoreGuid(String storeGuid) {
        return itemMapper.getItemsByStoreGuid(storeGuid);
    }

    @Override
    public List<TypeItemListDTO> queryStoreByBrandList(String brandGuid) {
        if (ObjectUtils.isEmpty(brandGuid)) {
            throw new BusinessException(BRAND_CANNOT_BE_NULL);
        }
        log.info("品牌guid：{}", brandGuid);

        // 这里可以换成模糊查询
        List<ItemDO> itemDOList = this.list(new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getBrandGuid, brandGuid)
                .ne(ItemDO::getItemType, 1)
        );
        if (CollectionUtils.isEmpty(itemDOList)) {
            log.info(ITEM_UNDER_BRAND_IS_EMPTY);
            return Lists.newArrayList();
        }

        List<String> typeGuidList = itemDOList.stream()
                .map(ItemDO::getTypeGuid)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(typeGuidList)) {
            log.info("商品分类Guid为空");
            return Lists.newArrayList();
        }

        List<TypeDO> typeDOList = typeService.list(new LambdaQueryWrapper<TypeDO>()
                .in(TypeDO::getGuid, typeGuidList));
        if (CollectionUtils.isEmpty(typeDOList)) {
            log.info("商品分类为空");
            return Lists.newArrayList();
        }
        log.info("商品分类DO：{}", JacksonUtils.writeValueAsString(typeDOList));

        ArrayList<TypeItemListDTO> response = new ArrayList<>();
        typeDOList.forEach(type -> {
            List<TypeItemListDTO> itemList = itemDOList.stream()
                    .filter(i -> i.getTypeGuid().equals(type.getGuid()))
                    .map(i -> {
                        TypeItemListDTO list = new TypeItemListDTO();
                        list.setGuid(i.getGuid());
                        list.setName(i.getName());
                        return list;
                    })
                    .collect(Collectors.toList());

            TypeItemListDTO typeDTO = new TypeItemListDTO();
            typeDTO.setGuid(type.getGuid());
            typeDTO.setName(type.getName());
            typeDTO.setItemList(itemList);
            response.add(typeDTO);
        });

        return response;
    }

    /**
     * 批量移动商品:选中一个分类下的商品，批量移动到其它分类下，重新排序
     *
     * @param typeSortReqDTO 商品guidList，目标分类guid
     * @return boolean
     */
    @Override
    public Boolean batchMoveItem(TypeSortReqDTO typeSortReqDTO) {
        if (CollectionUtils.isEmpty(typeSortReqDTO.getItemGuidList())) {
            throw new BusinessException("商品guid集合不能为空");
        }
        if (ObjectUtils.isEmpty(typeSortReqDTO.getGuid())) {
            throw new BusinessException("分类guid不能为空");
        }
        String typeGuid = typeSortReqDTO.getGuid();
        int reqSize = typeSortReqDTO.getItemGuidList().size();

        List<ItemDO> moveItemDOList = this.list(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getGuid, typeSortReqDTO.getItemGuidList())
                .eq(ItemDO::getIsDelete, 0)
        );
        for (int i = 0; i < moveItemDOList.size(); i++) {
            ItemDO itemDO = moveItemDOList.get(i);
            itemDO.setTypeGuid(typeGuid);
            itemDO.setSort(i + 1);
        }

        List<ItemDO> itemDOList = this.list(new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getTypeGuid, typeGuid)
                .eq(ItemDO::getIsDelete, 0)
        );
        for (int i = 0; i < itemDOList.size(); i++) {
            ItemDO itemDO = itemDOList.get(i);
            itemDO.setSort(i + 1 + reqSize);
        }

        itemDOList.addAll(moveItemDOList);

        return this.updateBatchById(itemDOList);
    }

    @Override
    public List<String> queryFilterItem(ItemSingleDTO itemSingleDTO) {
        List<SkuDO> list = skuService.list(new LambdaQueryWrapper<SkuDO>()
                .eq(SkuDO::getStoreGuid, itemSingleDTO.getData())
                .eq(SkuDO::getSkuFrom, 2)
                .eq(SkuDO::getIsDelete, 0)
                .eq(SkuDO::getIsEnable, 1)
        );
        return list.stream()
                .map(SkuDO::getParentGuid)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 根据门店GUID查询所有商品详情已经分类信息（区分销售模式）
     *
     * @param itemSingleDTO storeGuid
     * @return 分类和商品详情
     */
    @Override
    public ItemInfoAndTypeRespDTO queryStoreItemBySalesModel(ItemSingleDTO itemSingleDTO) {
        ItemInfoAndTypeRespDTO itemInfoAndType = new ItemInfoAndTypeRespDTO();
        String storeGuid = itemSingleDTO.getStoreGuid();
        if (StringUtils.isEmpty(storeGuid)) {
            log.error("storeGuid不能为空");
            return itemInfoAndType;
        }

        // 根据门店查询所属品牌
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        log.info("品牌数据：{}", JacksonUtils.writeValueAsString(brandDTO));
        if (ObjectUtils.isEmpty(brandDTO) || ObjectUtils.isEmpty(brandDTO.getSalesModel())) {
            log.warn("未查询到品牌信息:{}", storeGuid);
            return itemInfoAndType;
        }

        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setStoreGuid(storeGuid);
        List<ItemEstimateForAndroidRespDTO> estimateSkuList = queryEstimateForSyn(baseDTO);

        if (Objects.equals(SalesModelEnum.RECIPE_MODE.getCode(), brandDTO.getSalesModel())) {
            // 菜谱模式需要查询生效菜谱绑定的菜
            itemInfoAndType = getInfoAndTypeRespDTO(storeGuid, estimateSkuList);

        } else {
            // 普通模式查询门店菜品
            ArrayList<String> storeGuidList = new ArrayList<>();
            storeGuidList.add(storeGuid);
            ItemStringListDTO dto = new ItemStringListDTO().setDataList(storeGuidList);
            itemInfoAndType = this.listItemInfoAndType(dto);
        }

        // 若用户标识为空，则不返回微信的数据
        if (Objects.nonNull(itemSingleDTO.getWxtoken())) {
            // 通过redis获取用户数据
            getInfoByRedis(itemSingleDTO, itemInfoAndType);
        }
        // 将父商品GUID设置到itemGuid上
//        setParentGuid(itemInfoAndType);由赚餐自己处理

        return itemInfoAndType;
    }

    @Override
    public ItemInfoAndTypeRespDTO queryStoreItemBySalesNew(ItemSingleDTO itemSingleDTO) {
        ItemInfoAndTypeRespDTO itemInfoAndType = new ItemInfoAndTypeRespDTO();
        String storeGuid = itemSingleDTO.getStoreGuid();
        if (StringUtils.isEmpty(storeGuid)) {
            log.error("queryStoreItemBySalesNew方法,storeGuid不能为空");
            return itemInfoAndType;
        }

        // 根据门店查询所属品牌
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        log.info("queryStoreItemBySalesNew,品牌数据：{}", JacksonUtils.writeValueAsString(brandDTO));
        if (ObjectUtils.isEmpty(brandDTO) || ObjectUtils.isEmpty(brandDTO.getSalesModel())) {
            log.warn("queryStoreItemBySalesNew,未查询到品牌信息:{}", storeGuid);
            return itemInfoAndType;
        }

        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setStoreGuid(storeGuid);
//        List<ItemEstimateForAndroidRespDTO> estimateSkuList = queryEstimateForSyn(baseDTO);

        if (Objects.equals(SalesModelEnum.RECIPE_MODE.getCode(), brandDTO.getSalesModel())) {
            log.info("菜谱模式》》》》》》》》》》》》》》》");
            // 菜谱模式需要查询生效菜谱绑定的菜
            itemInfoAndType = getNewInfoAndTypeRespDTO(storeGuid, itemSingleDTO.getItemList());
        } else {
            log.info("普通模式》》》》》》》》》》》》》》》");
            // 普通模式查询门店菜品
            ArrayList<String> storeGuidList = new ArrayList<>();
            storeGuidList.add(storeGuid);
            ItemStringListDTO dto = new ItemStringListDTO().setDataList(storeGuidList);
            dto.setStoreGuid(itemSingleDTO.getStoreGuid());
            dto.setItemList(itemSingleDTO.getItemList());
            itemInfoAndType = this.newListItemInfoAndType(dto);

        }

        // 若用户标识为空，则不返回微信的数据
//        if (Objects.nonNull(itemSingleDTO.getWxtoken())) {
//            // 通过redis获取用户数据
//            getInfoByRedis(itemSingleDTO, itemInfoAndType);
//        }
        // 将父商品GUID设置到itemGuid上
//        setParentGuid(itemInfoAndType);由赚餐自己处理

        return itemInfoAndType;
    }

    @Override
    public List<ItemInfoTypeRespDTO> queryStoreItemType(TypeSingleDTO typeSingleDTO) {
        List<ItemInfoTypeRespDTO> typeWebRespDTOList = Lists.newArrayList();
        List<TypeDO> typeDOList;
        if (Objects.equals(SalesModelEnum.RECIPE_MODE.getCode(), typeSingleDTO.getIsModel())) {

            // 菜谱模式需要查询生效菜谱绑定的菜
//            itemInfoAndType = getNewInfoAndTypeRespDTO(storeGuid, itemSingleDTO.getItemList());

            log.info("菜谱模式：{}", JacksonUtils.writeValueAsString(typeSingleDTO.getItemTypeList()));
            Set<String> typeGuidSet = new HashSet<>(typeSingleDTO.getItemTypeList());
            typeDOList = typeService.list(new LambdaQueryWrapper<TypeDO>()
                    .in(TypeDO::getGuid, typeGuidSet)
                    .eq(TypeDO::getIsEnable, 1)
                    .orderByAsc(TypeDO::getSort));
            log.info("菜谱模式获取数据：{}", JacksonUtils.writeValueAsString(typeDOList));
        } else {
            log.info("普通模式》》》》》》》》》》》》》》》");
            // 普通模式查询门店菜品
//            itemInfoAndType = this.newListItemInfoAndType(dto);

            Assert.hasText(typeSingleDTO.getStoreGuid(), "storeGuid不能为空");
            // 数据库的分类集合
            log.info("普通模式：{}", JacksonUtils.writeValueAsString(typeSingleDTO.getItemTypeList()));

            typeDOList = typeMapper.selectList(
                    new LambdaQueryWrapper<TypeDO>()
                            .eq(TypeDO::getStoreGuid, typeSingleDTO.getStoreGuid())
                            .in(CollUtil.isNotEmpty(typeSingleDTO.getItemTypeList()), TypeDO::getGuid, typeSingleDTO.getItemTypeList())
                            .eq(TypeDO::getIsEnable, 1)
                            .ne(TypeDO::getName, "宴会套餐"));
            log.info("普通模式获取数据：{}", JacksonUtils.writeValueAsString(typeDOList));
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(typeDOList)) {
            log.warn("未查询到分类信息，itemSingleDTO={}", JacksonUtils.writeValueAsString(typeDOList));
            return typeWebRespDTOList;
        }
        for (TypeDO typeDO : typeDOList) {
            ItemInfoTypeRespDTO respDTO = new ItemInfoTypeRespDTO();
            respDTO.setTypeGuid(typeDO.getGuid());
            respDTO.setName(typeDO.getName());
            respDTO.setSort(typeDO.getSort());
            respDTO.setIsMustPoint(typeDO.getIsMustPoint());
            typeWebRespDTOList.add(respDTO);
        }
        log.info("分类数据：{}", JacksonUtils.writeValueAsString(typeWebRespDTOList));
        return typeWebRespDTOList;
    }

    /**
     * 通过redis获取用户数据
     *
     * @param itemSingleDTO   ItemSingleDTO
     * @param itemInfoAndType ItemInfoAndTypeRespDTO
     */
    private void getInfoByRedis(ItemSingleDTO itemSingleDTO, ItemInfoAndTypeRespDTO itemInfoAndType) {
        String cacheKey = "itemConfig:" + itemSingleDTO.getEnterpriseGuid() + ":" + itemSingleDTO.getWxtoken();
        log.info("cacheKey:{}", cacheKey);
        Object cache = redisUtil.get(cacheKey);
        if (ObjectUtils.isEmpty(cache)) {
            log.warn("缓存里没有数据");
            return;
        }

        MenuInfoAllDTO menuInfoAllDTO = JacksonUtils.toObject(MenuInfoAllDTO.class, cache.toString());
        if (Objects.nonNull(menuInfoAllDTO)) {
            itemInfoAndType.setMenuInfoAllDTO(menuInfoAllDTO);

            // 过滤商品信息
            if (!CollectionUtils.isEmpty(itemSingleDTO.getItemList())) {
                log.info("过滤￥商品：{}", JacksonUtils.writeValueAsString(itemInfoAndType));
                filterData(itemInfoAndType, itemSingleDTO.getItemList());
            }
        } else {
            log.warn("缓存为空！");
        }
    }

    /**
     * 将父商品GUID设置到itemGuid上
     *
     * @param itemInfoAndType ItemInfoAndTypeRespDTO
     */
    private void setParentGuid(ItemInfoAndTypeRespDTO itemInfoAndType) {
        if (!CollectionUtils.isEmpty(itemInfoAndType.getItemInfoList())) {
            List<ItemInfoRespDTO> itemInfoList = itemInfoAndType.getItemInfoList();
            itemInfoList.forEach(item -> {
                if (!ObjectUtils.isEmpty(item.getParentGuid())) {
                    item.setItemGuid(item.getParentGuid());
                }
            });
        }

        if (!ObjectUtils.isEmpty(itemInfoAndType.getMenuInfoAllDTO())) {
            MenuInfoAllDTO menuInfoAllDTO = itemInfoAndType.getMenuInfoAllDTO();
            List<ItemInfoDTO> itemInfoDTOList = menuInfoAllDTO.getItemInfoDTOS();
            itemInfoDTOList.forEach(item -> {
                if (!ObjectUtils.isEmpty(item.getParentGuid())) {
                    item.setItemGuid(item.getParentGuid());
                }
            });
        }
    }

    private ItemInfoAndTypeRespDTO getNewInfoAndTypeRespDTO(String storeGuid, List<String> itemList) {
        ItemInfoAndTypeRespDTO itemInfoAndType = new ItemInfoAndTypeRespDTO();

        // 查询门店关联的菜谱的所有菜品
        List<PricePlanStoreDO> planStoreDOList = pricePlanStoreService.list(new LambdaQueryWrapper<PricePlanStoreDO>()
                .eq(PricePlanStoreDO::getStoreGuid, storeGuid));
        List<String> storePlanGuidList = planStoreDOList.stream()
                .map(PricePlanStoreDO::getPlanGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storePlanGuidList)) {
            log.warn("门店下没有方案");
            return itemInfoAndType;
        }

        List<PricePlanDO> planDOList = pricePlanService.list(new LambdaQueryWrapper<PricePlanDO>()
                .in(PricePlanDO::getGuid, storePlanGuidList)
                .in(PricePlanDO::getStatus, 1, 4)
        );
        List<String> planGuidList = planDOList.stream()
                .map(PricePlanDO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(planGuidList)) {
            log.warn("没有查询到方案：{}", storePlanGuidList);
            return itemInfoAndType;
        }

        List<PricePlanItemDO> planItemDOList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                .in(PricePlanItemDO::getPlanGuid, planGuidList)
                .ne(PricePlanItemDO::getIsSoldOut, ItemStateEnum.IMMEDIATELY_DELETE.getCode())
        );

        if (CollectionUtil.isEmpty(planItemDOList)) {
            log.warn("门店的所有方案下菜品为空");
            return itemInfoAndType;
        }

        deleteOtherByPlanItemDO(planItemDOList);

        List<String> allItemGuidList = planItemDOList.stream()
                .map(PricePlanItemDO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        List<ItemDO> itemDOList = list(new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getIsEnable, true)
                .in(ItemDO::getGuid, allItemGuidList)
                .orderByAsc(ItemDO::getSort));
        if (CollectionUtils.isEmpty(itemDOList)) {
            return itemInfoAndType;
        }
        log.info("菜谱方案基础菜品数据：{}", JacksonUtils.writeValueAsString(itemDOList));

        // 菜品分类转换
        Map<String, PricePlanItemDO> planItemMap = planItemDOList.stream()
                .collect(Collectors.toMap(PricePlanItemDO::getItemGuid, Function.identity(), (key1, key2) -> key2));
        for (ItemDO itemDO : itemDOList) {
            PricePlanItemDO pricePlanItemDO = planItemMap.get(itemDO.getGuid());
            itemDO.setTypeGuid(pricePlanItemDO.getTypeGuid());
            itemDO.setSort(pricePlanItemDO.getSort());
        }

        // 将商品转成返回的实体
        List<ItemInfoRespDTO> respItemDTOList = MapStructUtils.INSTANCE.itemDOList2ItemInfoRespDTOList(itemDOList);

        // 装入sku
//        setSkuList(respItemDTOList, estimateSkuList);
//
//        // 使用价格方案设置的规格会员价和售卖价
//        setPlanItemInfo(planItemDOList, respItemDTOList);
//
//        // 设置菜谱方案的属性组
//        respItemDTOList.forEach(item -> {
//                    List<AttrGroupWebRespDTO> attrGroupList = selectItemAttrList(item.getItemGuid());
//                    attrGroupList.forEach(attrGroup -> attrGroup.setAttrList(transformAttr(attrGroup)));
//                    item.setAttrGroupList(attrGroupList);
//                }
//        );

        // 设置套餐组
//        respItemDTOList.forEach(item -> item.setSubgroupList(transformSubgroup(item)));
        log.info("菜谱模式下过滤前商品: {}", JacksonUtils.writeValueAsString(respItemDTOList));
        respItemDTOList = respItemDTOList.stream()
                .filter(i -> itemList.contains(i.getParentGuid()) || itemList.contains(i.getItemGuid()))
                .collect(Collectors.toList());
        log.info("getNewInfoAndTypeRespDTO,菜谱模式下过滤后商品: {}", JacksonUtils.writeValueAsString(respItemDTOList));

        itemInfoAndType.setItemInfoList(respItemDTOList);

        // 获取菜品的分类集合
        List<TypeWebRespDTO> typeWebRespDTOS = getTypeWebRespDTOS(planItemDOList);
        log.info("getNewInfoAndTypeRespDTO,价格方案分类数据：{}", JacksonUtils.writeValueAsString(typeWebRespDTOS));
        itemInfoAndType.setTypeList(typeWebRespDTOS);
        return itemInfoAndType;
    }

    private ItemInfoAndTypeRespDTO getInfoAndTypeRespDTO(String storeGuid, List<ItemEstimateForAndroidRespDTO> estimateSkuList) {
        ItemInfoAndTypeRespDTO itemInfoAndType = new ItemInfoAndTypeRespDTO();

        // 查询门店关联的菜谱的所有菜品
        List<PricePlanStoreDO> planStoreDOList = pricePlanStoreService.list(new LambdaQueryWrapper<PricePlanStoreDO>()
                .eq(PricePlanStoreDO::getStoreGuid, storeGuid));
        List<String> storePlanGuidList = planStoreDOList.stream()
                .map(PricePlanStoreDO::getPlanGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storePlanGuidList)) {
            log.warn("门店下没有方案");
            return itemInfoAndType;
        }

        List<PricePlanDO> planDOList = pricePlanService.list(new LambdaQueryWrapper<PricePlanDO>()
                .in(PricePlanDO::getGuid, storePlanGuidList)
                .in(PricePlanDO::getStatus, 1, 4)
        );
        List<String> planGuidList = planDOList.stream()
                .map(PricePlanDO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(planGuidList)) {
            log.warn("没有查询到方案：{}", storePlanGuidList);
            return itemInfoAndType;
        }

        List<PricePlanItemDO> planItemDOList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                .in(PricePlanItemDO::getPlanGuid, planGuidList)
                .ne(PricePlanItemDO::getIsSoldOut, ItemStateEnum.IMMEDIATELY_DELETE.getCode())
        );

        if (CollectionUtil.isEmpty(planItemDOList)) {
            log.warn("门店的所有方案下菜品为空");
            return itemInfoAndType;
        }

        deleteOtherByPlanItemDO(planItemDOList);

        List<String> allItemGuidList = planItemDOList.stream()
                .map(PricePlanItemDO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        List<ItemDO> itemDOList = list(new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getIsEnable, true)
                .in(ItemDO::getGuid, allItemGuidList)
                .orderByAsc(ItemDO::getSort));
        if (CollectionUtils.isEmpty(itemDOList)) {
            return itemInfoAndType;
        }
        log.info("菜谱方案基础菜品数据：{}", JacksonUtils.writeValueAsString(itemDOList));

        // 菜品分类转换
        Map<String, PricePlanItemDO> planItemMap = planItemDOList.stream()
                .collect(Collectors.toMap(PricePlanItemDO::getItemGuid, Function.identity(), (key1, key2) -> key2));
        for (ItemDO itemDO : itemDOList) {
            PricePlanItemDO pricePlanItemDO = planItemMap.get(itemDO.getGuid());
            itemDO.setTypeGuid(pricePlanItemDO.getTypeGuid());
            itemDO.setSort(pricePlanItemDO.getSort());
        }

        // 将商品转成返回的实体
        List<ItemInfoRespDTO> respItemDTOList = MapStructUtils.INSTANCE.itemDOList2ItemInfoRespDTOList(itemDOList);

        // 装入sku
        setSkuList(respItemDTOList, estimateSkuList);

        // 使用价格方案设置的规格会员价和售卖价
        setPlanItemInfo(planItemDOList, respItemDTOList);

        // 设置菜谱方案的属性组
        respItemDTOList.forEach(item -> {
                    List<AttrGroupWebRespDTO> attrGroupList = selectItemAttrList(item.getItemGuid());
                    attrGroupList.forEach(attrGroup -> attrGroup.setAttrList(transformAttr(attrGroup)));
                    item.setAttrGroupList(attrGroupList);
                }
        );

        // 设置套餐组
        respItemDTOList.forEach(item -> item.setSubgroupList(transformSubgroup(item)));

        itemInfoAndType.setItemInfoList(respItemDTOList);

        // 获取菜品的分类集合
        List<TypeWebRespDTO> typeWebRespDTOS = getTypeWebRespDTOS(planItemDOList);
        log.info("价格方案分类数据：{}", JacksonUtils.writeValueAsString(typeWebRespDTOS));
        itemInfoAndType.setTypeList(typeWebRespDTOS);
        return itemInfoAndType;
    }

    private void deleteOtherByPlanItemDO(List<PricePlanItemDO> planItemDOList) {
        for (int i = planItemDOList.size() - 1; i >= 0; i--) {
            for (int j = planItemDOList.size() - 1; j > i; j--) {
                if (planItemDOList.get(j).getSkuGuid().equals(planItemDOList.get(i).getSkuGuid())) {
                    // 删除重复元素
                    planItemDOList.remove(j);
                }
            }
        }
    }

    /**
     * 过滤商品信息
     * 如果itemList有过滤的id则只返还这些商品，没有则全部返还
     *
     * @param itemInfoAndType 返还实体
     * @param itemList        要过滤的商品
     */
    private void filterData(ItemInfoAndTypeRespDTO itemInfoAndType, List<String> itemList) {
        List<ItemInfoRespDTO> itemInfoList = itemInfoAndType.getItemInfoList();
        List<ItemInfoRespDTO> respList = itemInfoList.stream()
                .filter(i -> itemList.contains(i.getParentGuid()) || itemList.contains(i.getItemGuid()))
                .collect(Collectors.toList());
        log.info("商品查询过滤后商品: {}", JacksonUtils.writeValueAsString(respList));
        if (CollectionUtils.isEmpty(respList)) {
            itemInfoAndType.setItemInfoList(new ArrayList<>());
        } else {
            itemInfoAndType.setItemInfoList(respList);
        }

        MenuInfoAllDTO infoAllDTO = itemInfoAndType.getMenuInfoAllDTO();
        List<ItemInfoDTO> infoDTOList = infoAllDTO.getItemInfoDTOS();
        List<ItemInfoDTO> respDTOList = infoDTOList.stream()
                .filter(i -> itemList.contains(i.getParentGuid()) || itemList.contains(i.getItemGuid()))
                .collect(Collectors.toList());
        log.info("缓存获取过滤后商品: {}", JacksonUtils.writeValueAsString(respDTOList));
        if (CollectionUtils.isEmpty(respDTOList)) {
            infoAllDTO.setItemInfoDTOS(new ArrayList<>());
        } else {
            infoAllDTO.setItemInfoDTOS(respDTOList);
        }
        itemInfoAndType.setMenuInfoAllDTO(infoAllDTO);
    }

    /**
     * 套餐转换
     *
     * @param itemSynRespDTO 商品
     * @return 套餐
     */
    private List<SubgroupWebRespDTO> transformSubgroup(ItemInfoRespDTO itemSynRespDTO) {
        Integer itemType = itemSynRespDTO.getItemType();
        List<SubgroupWebRespDTO> subgroupList = itemSynRespDTO.getSubgroupList();
        //isFixPkg是否是固定套餐，0：否，1：是
        Integer isFixPkg = itemSynRespDTO.getIsFixPkg();
        return 1 != itemType || ObjectUtils.isEmpty(subgroupList)
                ? Lists.newArrayList()
                : subgroupList.stream().peek(x -> {
            x.setPickNum(1 == isFixPkg ? 0 : x.getPickNum());
            x.setSubItemSkuList(transformSubItemSku(x.getSubItemSkuList()));
        }).collect(Collectors.toList());
    }

    /**
     * 套餐商品转换
     *
     * @param subItemSkuList 商品
     * @return 商品
     */
    private List<SubItemSkuWebRespDTO> transformSubItemSku(List<SubItemSkuWebRespDTO> subItemSkuList) {
        return ObjectUtils.isEmpty(subItemSkuList)
                ? Lists.newArrayList()
                : subItemSkuList.stream().peek(x -> x.setAttrGroupList(transformAttrGroup(x.getAttrGroupList())))
                .collect(Collectors.toList());
    }

    /**
     * 属性转换
     *
     * @param attrGroupList 属性集合
     * @return 属性集合
     */
    private List<AttrGroupWebRespDTO> transformAttrGroup(List<AttrGroupWebRespDTO> attrGroupList) {
        return ObjectUtils.isEmpty(attrGroupList)
                ? Lists.newArrayList()
                : attrGroupList.stream().peek(x -> x.setAttrList(transformAttr(x)))
                .collect(Collectors.toList());
    }

    /**
     * 属性转换
     *
     * @param attrGroupSynRespDTO 属性
     * @return 属性
     */
    private List<AttrWebRespDTO> transformAttr(AttrGroupWebRespDTO attrGroupSynRespDTO) {
        List<AttrWebRespDTO> attrList = attrGroupSynRespDTO.getAttrList();
        if (ObjectUtils.isEmpty(attrList)) {
            return Lists.newArrayList();
        }
        log.info("商户属性组:{}", JacksonUtils.writeValueAsString(attrGroupSynRespDTO));
        List<AttrWebRespDTO> collect = attrList.stream().map(x -> {
            AttrWebRespDTO itemInfoAttrDTO = new AttrWebRespDTO();
            itemInfoAttrDTO.setAttrGuid(x.getAttrGuid());
            itemInfoAttrDTO.setName(x.getName());
            itemInfoAttrDTO.setPrice(x.getPrice());
            itemInfoAttrDTO.setUck(x.getIsDefault());
            itemInfoAttrDTO.setAttrItemAttrGroupGuid(x.getAttrItemAttrGroupGuid());
            return itemInfoAttrDTO;
        }).collect(Collectors.toList());
        if (attrGroupSynRespDTO.getIsRequired() == 1 && collect.stream().noneMatch(x -> x.getUck() == 1)) {
            collect.get(0).setUck(1);
        }
        return collect;
    }

    /**
     * 展示最必选的价格
     */
    private void showPrice(ItemInfoRespDTO itemInfoDTO) {
        cheapUnit(itemInfoDTO);

        List<AttrGroupWebRespDTO> attrGroupList = itemInfoDTO.getAttrGroupList();
        List<SubgroupWebRespDTO> subgroupList = itemInfoDTO.getSubgroupList();
        BigDecimal showPrice = itemInfoDTO.getShowPrice();
        BigDecimal showMemberPrice = itemInfoDTO.getShowMemberPrice();
        itemInfoDTO.setShowPrice(showPrice.add(cheapAttr(attrGroupList)).add(cheapSubgroup(subgroupList)));
        if (showMemberPrice != null && showMemberPrice.compareTo(BigDecimal.ZERO) > 0) {
            itemInfoDTO.setShowMemberPrice(showMemberPrice.add(cheapAttr(attrGroupList)).add(cheapSubgroup(subgroupList)));
        }
    }

    /**
     * 显示最便宜的价格
     *
     * @param itemInfoDTO 商品
     */
    private void cheapUnit(ItemInfoRespDTO itemInfoDTO) {
        List<SkuInfoRespDTO> skuList = itemInfoDTO.getSkuList();
        SkuInfoRespDTO itemInfoSkuDTO = Collections.min(skuList, Comparator.comparing(SkuInfoRespDTO::getSalePrice));
        itemInfoDTO.setShowUnit(itemInfoSkuDTO.getUnit());
        itemInfoDTO.setShowPrice(itemInfoSkuDTO.getSalePrice());
        itemInfoDTO.setMinOrderNum(itemInfoSkuDTO.getMinOrderNum());

        List<SkuInfoRespDTO> collect = skuList.stream().filter(x -> {
            BigDecimal memberPrice = x.getMemberPrice();
            return memberPrice != null && memberPrice.compareTo(BigDecimal.ZERO) > 0;
        }).collect(Collectors.toList());

        if (!ObjectUtils.isEmpty(collect)) {
            SkuInfoRespDTO min = Collections.min(collect, Comparator.comparing(SkuInfoRespDTO::getMemberPrice));
            itemInfoDTO.setShowUnit(min.getUnit());
            itemInfoDTO.setShowMemberPrice(min.getMemberPrice());
            itemInfoDTO.setMinOrderNum(min.getMinOrderNum());
        }
    }

    /**
     * @param attrGroupList 属性组
     * @return 必选属性价格
     */
    private BigDecimal cheapAttr(List<AttrGroupWebRespDTO> attrGroupList) {
        return ObjectUtils.isEmpty(attrGroupList)
                ? BigDecimal.ZERO
                : attrGroupList.stream().flatMap(x -> x.getAttrList().stream())
                .filter(x -> x.getIsDefault() == 1)
                .map(AttrWebRespDTO::getPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    /**
     * @param subgroupList 套餐分组
     * @return 必选分组下所有子商品
     */
    private BigDecimal cheapSubgroup(List<SubgroupWebRespDTO> subgroupList) {
        return ObjectUtils.isEmpty(subgroupList)
                ? BigDecimal.ZERO
                : subAttrFee(subgroupList.stream().filter(x -> x.getPickNum() == 0)
                .flatMap(x -> x.getSubItemSkuList().stream()).collect(Collectors.toList()));
    }

    /**
     * 套餐子项属性加价
     *
     * @param itemInfoSubSkuDTOS 套餐子项商品
     * @return 价格
     */
    private BigDecimal subAttrFee(List<SubItemSkuWebRespDTO> itemInfoSubSkuDTOS) {
        log.info("skuList:{}", itemInfoSubSkuDTOS);
        return ObjectUtils.isEmpty(itemInfoSubSkuDTOS)
                ? BigDecimal.ZERO
                : itemInfoSubSkuDTOS.stream().map(x -> {
            BigDecimal itemNum = x.getItemNum() == null ? BigDecimal.ZERO : x.getItemNum();
            Integer defaultNum = x.getDefaultNum();
            BigDecimal attrTotalPrice = getAttrTotalPrice(x.getAttrGroupList());
            return attrTotalPrice.multiply(new BigDecimal(defaultNum)).multiply(x.getItemType() == 3 ? BigDecimal.ONE : itemNum)
                    .add(x.getAddPrice().multiply(new BigDecimal(defaultNum)));
        }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    /**
     * 属性加价计算
     *
     * @param itemInfoAttrGroupDTOS 属性集合
     * @return 价格
     */
    private BigDecimal getAttrTotalPrice(List<AttrGroupWebRespDTO> itemInfoAttrGroupDTOS) {
        return ObjectUtils.isEmpty(itemInfoAttrGroupDTOS)
                ? BigDecimal.ZERO
                : itemInfoAttrGroupDTOS.stream().map(x -> {
            List<AttrWebRespDTO> attrList = x.getAttrList();
            if (!ObjectUtils.isEmpty(attrList)) {
                return attrList.stream().filter(k -> k.getUck() == 1).map(k ->
                                Optional.ofNullable(k.getPrice()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            }
            return BigDecimal.ZERO;
        }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }


    /**
     * 根据门店Guid和商品Guid查询门店下有没有这些个商品
     * 讲道理我觉得这个接口是没有必要的
     *
     * @param itemStringListDTO 门店guid，商品guid
     * @return 有则返回商品信息，无则返回空
     */
    @Override
    public ItemInfoAndTypeRespDTO queryStoreItemAndFilter(ItemStringListDTO itemStringListDTO) {
        ItemInfoAndTypeRespDTO itemInfoAndTypeRespDTO = new ItemInfoAndTypeRespDTO();
        List<String> itemList = itemStringListDTO.getItemList();
        List<String> storeGuidList = itemStringListDTO.getDataList();
        if (CollectionUtils.isEmpty(itemList)) {
            log.warn("传入过滤的的商品为空");
            return itemInfoAndTypeRespDTO;
        }
        if (CollectionUtils.isEmpty(storeGuidList)) {
            log.warn("查询门店下商品详情及分类信息：门店列表为空！");
            return itemInfoAndTypeRespDTO;
        }

        // 根据门店调用接口获取商品
        List<ItemInfoRespDTO> itemInfoList = new ArrayList<>();
        storeGuidList.forEach(storeGuid -> {
            ItemSingleDTO dto = new ItemSingleDTO();
            dto.setStoreGuid(storeGuid);
            List<ItemInfoRespDTO> infoList = this.queryStoreItemBySalesModel(dto).getItemInfoList();
            if (CollectionUtils.isEmpty(infoList)) {
                log.warn("门店：{} 下商品为空", storeGuid);
                return;
            }
            itemInfoList.addAll(infoList);
        });
        if (CollectionUtils.isEmpty(itemInfoList)) {
            log.warn("根据门店查询到的商品为空！");
            return itemInfoAndTypeRespDTO;
        }

        // 过滤商品是否在门店下
        log.info("菜谱模式下过滤￥商品: {}", JacksonUtils.writeValueAsString(itemInfoList));
        List<ItemInfoRespDTO> respList = itemInfoList.stream()
                .filter(i -> itemList.contains(i.getParentGuid()) || itemList.contains(i.getItemGuid()))
                .collect(Collectors.toList());
        log.info("菜谱模式下过滤后商品: {}", JacksonUtils.writeValueAsString(respList));
        itemInfoAndTypeRespDTO.setItemInfoList(respList);

        // 根据分类guid获取分类信息
        List<String> typeGuidList = respList.stream()
                .map(ItemInfoRespDTO::getTypeGuid)
                .collect(Collectors.toList());
        List<TypeWebRespDTO> respDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(typeGuidList)) {
            List<TypeDO> typeDOList = typeService.list(new LambdaQueryWrapper<TypeDO>()
                    .in(TypeDO::getGuid, typeGuidList));
            respDTOList.addAll(MapStructUtils.INSTANCE.typeDOList2typeWebRespDTOList(typeDOList));
        }
        itemInfoAndTypeRespDTO.setTypeList(respDTOList);

        return itemInfoAndTypeRespDTO;
    }

    /**
     * 设置sku的值
     *
     * @param itemInfoRespDTOList 要返回的数据
     * @param estimateSkuList     估清的数据
     */
    private void setSkuList(List<ItemInfoRespDTO> itemInfoRespDTOList, List<ItemEstimateForAndroidRespDTO> estimateSkuList) {
        List<String> itemGuidList = itemInfoRespDTOList.stream()
                .map(ItemInfoRespDTO::getItemGuid)
                .collect(Collectors.toList());
        List<SkuDO> skuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>()
                .in(SkuDO::getItemGuid, itemGuidList));
        Map<String, List<SkuDO>> skuMap = skuDOList.stream()
                .collect(Collectors.groupingBy(SkuDO::getItemGuid));
        itemInfoRespDTOList.forEach(item -> {
            // 是否支持会员价,先默认true吧，后面接通了再改
            item.setSkuList(transformSku(skuMap.get(item.getItemGuid()), estimateSkuList));
        });
    }

    /**
     * 规格转换
     *
     * @param skuList 规格集合
     * @return 规格集合
     */
    private List<SkuInfoRespDTO> transformSku(List<SkuDO> skuList, List<ItemEstimateForAndroidRespDTO> estimateSkuList) {
        if (ObjectUtils.isEmpty(skuList)) {
            return Lists.newArrayList();
        }
        List<SkuInfoRespDTO> collect = skuList.stream().map(x -> {
            SkuInfoRespDTO itemInfoSkuDTO = MapStructUtils.INSTANCE.skuDO2skuInfoRespDTO(x);
            itemInfoSkuDTO.setEnablePreferentialPrice(x.getMemberPrice() != null && x.getMemberPrice().compareTo(BigDecimal.ZERO) > 0);
            if (!ObjectUtils.isEmpty(estimateSkuList)) {
                setEstimateSku(itemInfoSkuDTO, estimateSkuList);
            }
            return itemInfoSkuDTO;
        }).collect(Collectors.toList());
        collect.get(0).setUck(1);
        return collect;
    }

    /**
     * 沽清剩余数量设置
     *
     * @param itemInfoSkuDTO 返回值
     */
    private void setEstimateSku(SkuInfoRespDTO itemInfoSkuDTO, List<ItemEstimateForAndroidRespDTO> estimateSkuList) {
        String skuGuid = itemInfoSkuDTO.getSkuGuid();
        Optional<ItemEstimateForAndroidRespDTO> first = estimateSkuList.stream()
                .filter(x -> x.getSkuGuid().equals(skuGuid))
                .findFirst();
        if (first.isPresent()) {
            ItemEstimateForAndroidRespDTO itemEstimateForAndroidRespDTO = first.get();
            itemInfoSkuDTO.setIsSoldOut(itemEstimateForAndroidRespDTO.getIsSoldOut());
            itemInfoSkuDTO.setResidueQuantity(itemEstimateForAndroidRespDTO.getResidueQuantity());
        }
    }

    /**
     * 安卓估清菜品同步
     * 取自IEstimateService的queryEstimateForSyn接口（避免循环依赖）
     *
     * @param baseDTO
     * @return
     */
    private List<ItemEstimateForAndroidRespDTO> queryEstimateForSyn(BaseDTO baseDTO) {
        List<ItemEstimateForAndroidDTO> list = new ArrayList<>();

        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(baseDTO.getStoreGuid());
        log.info("queryEstimateForSyn菜品模式数据：{}", JacksonUtils.writeValueAsString(brandDTO));
        if (ObjectUtils.isEmpty(brandDTO.getSalesModel())) {
            log.warn("未查询到品牌信息:{}", baseDTO.getStoreGuid());
            return new ArrayList<>();
        }
        boolean isPlan = false;
        if (brandDTO.getSalesModel() == SalesModelEnum.RECIPE_MODE.getCode()) {
            LocalDateTime nowDateTime = LocalDateTime.now();
            String minute = (nowDateTime.getMinute() + "").length() == 1 ? "0" + nowDateTime.getMinute() : nowDateTime.getMinute() + "";
            int nowTime = Integer.parseInt(nowDateTime.getHour() + "" + minute);
            List<PricePlanNowDTO> planNowList = pricePlanStoreService.findPlanNowStoreGuid(nowDateTime, baseDTO.getStoreGuid());
            // 有生效的菜谱
            if (!CollectionUtil.isEmpty(planNowList)) {
                isPlan = true;

                PricePlanNowDTO pricePlanNowAllTime = null;
                PricePlanNowDTO pricePlanNowTime = null;
                for (PricePlanNowDTO planNow : planNowList) {
                    Integer sellTimeType = planNow.getSellTimeType();
                    if (sellTimeType == 0) {
                        // 全时段菜品方案
                        pricePlanNowAllTime = planNow;
                    } else {
                        // 特殊时段菜品方案
                        LocalDateTime startTime = planNow.getStartTime();
                        LocalDateTime endTime = planNow.getEndTime();
                        String startMinute = (startTime.getMinute() + "").length() == 1 ? "0" + startTime.getMinute() : startTime.getMinute() + "";
                        int startTimeInt = Integer.parseInt(startTime.getHour() + "" + startMinute);
                        String endMinute = (endTime.getMinute() + "").length() == 1 ? "0" + endTime.getMinute() : endTime.getMinute() + "";
                        int endTimeInt = Integer.parseInt(endTime.getHour() + "" + endMinute);
                        log.info("特殊时段时间,nowTime->{}, startTimeInt->{}, endTimeInt->{}", nowTime, startTimeInt, endTimeInt);
                        if (endTimeInt > startTimeInt) {
                            // 灭有垮天
                            if (nowTime >= startTimeInt && nowTime <= endTimeInt) {
                                pricePlanNowTime = planNow;
                            }
                        } else {
                            // 垮天了
                            if (nowTime >= startTimeInt || nowTime <= endTimeInt) {
                                pricePlanNowTime = planNow;
                            }
                        }

                    }
                }

                if (!Objects.isNull(pricePlanNowAllTime)) {
                    String planGuid = pricePlanNowAllTime.getPlanGuid();
                    list = estimateMapper.queryEstimateForSynPlan(planGuid, baseDTO.getStoreGuid());
                }
                if (!Objects.isNull(pricePlanNowTime)) {
                    String planGuid = pricePlanNowTime.getPlanGuid();
                    list = estimateMapper.queryEstimateForSynPlan(planGuid, baseDTO.getStoreGuid());
                }
            }
        }

        if (!isPlan) {
            list = estimateMapper.queryEstimateForSyn(baseDTO);
        }

        List<ItemEstimateForAndroidRespDTO> result = new ArrayList<>();
        list.forEach(o -> {
            //是否手动禁售 2：是
            if (o.getIsSoldOut() == 2) {
                result.add(ItemEstimateForAndroidRespDTO.builder()
                        .isSoldOut(2)
                        .residueQuantity(BigDecimal.ZERO)
                        .reminderThreshold(BigDecimal.TEN)
                        .skuGuid(o.getSkuGuid()).build());
                //状态为限量 且库存数为0 则估清
            } else if (o.getIsSoldOut() == 1 && o.getIsTheLimit() == 2) {
                BigDecimal residueQuantity = BigDecimal.ZERO;
                //估清状态 1：否 2：是
                Integer isSoldOut = 2;
                //状态为限量 且 库存不为0 改为可售 库存加入结果对象
                if (o.getResidueQuantity().compareTo(BigDecimal.ZERO) == 1) {
                    residueQuantity = o.getResidueQuantity();
                    isSoldOut = 1;
                }
                result.add(ItemEstimateForAndroidRespDTO.builder()
                        .isSoldOut(isSoldOut)
                        .residueQuantity(residueQuantity)
                        .reminderThreshold(BigDecimal.TEN)
                        .skuGuid(o.getSkuGuid()).build());
            }
        });
        log.info("result size = {}", result.size());
        return result;
    }

    private List<TypeWebRespDTO> getTypeWebRespDTOS(List<PricePlanItemDO> pricePlanItemDOList) {
        Set<String> typeGuidSet = pricePlanItemDOList.stream()
                .map(PricePlanItemDO::getTypeGuid)
                .collect(Collectors.toSet());
        List<TypeDO> typeDOList = typeService.list(new LambdaQueryWrapper<TypeDO>()
                .in(TypeDO::getGuid, typeGuidSet)
                .eq(TypeDO::getIsEnable, 1)
                .orderByAsc(TypeDO::getSort));

        List<TypeWebRespDTO> typeWebRespDTOS = MapStructUtils.INSTANCE.typeDOList2typeWebRespDTOList(typeDOList);
        return typeWebRespDTOS;
    }

    private void setPlanItemInfo(List<PricePlanItemDO> pricePlanItemDOList, List<ItemInfoRespDTO> respItemDTOList) {
        Map<String, PricePlanItemDO> skuGuidMap = pricePlanItemDOList.stream()
                .collect(Collectors.toMap(PricePlanItemDO::getSkuGuid, PricePlanItemDO -> PricePlanItemDO));
        for (ItemInfoRespDTO item : respItemDTOList) {
            List<SkuInfoRespDTO> skuList = item.getSkuList();
            for (SkuInfoRespDTO sku : skuList) {
                String skuGuid = sku.getSkuGuid();
                PricePlanItemDO pricePlanItemDO = skuGuidMap.get(skuGuid);
                if (ObjectUtils.isEmpty(pricePlanItemDO)) {
                    log.warn("菜谱没有绑定该商品：{}", JacksonUtils.writeValueAsString(item));
                    return;
                }
                sku.setMemberPrice(null);
                sku.setSalePrice(pricePlanItemDO.getSalePrice());
                item.setName(pricePlanItemDO.getPlanItemName());

                if (!Objects.isNull(pricePlanItemDO.getMemberPrice())) {
                    sku.setMemberPrice(pricePlanItemDO.getMemberPrice());
                }
                if (StringUtils.isNotBlank(pricePlanItemDO.getDescription())) {
                    item.setDescription(pricePlanItemDO.getDescription());
                }
                if (StringUtils.isNotBlank(pricePlanItemDO.getEnglishBrief())) {
                    item.setEnglishBrief(pricePlanItemDO.getEnglishBrief());
                }
                if (StringUtils.isNotBlank(pricePlanItemDO.getEnglishIngredientsDesc())) {
                    item.setEnglishIngredientsDesc(pricePlanItemDO.getEnglishIngredientsDesc());
                }
                if (StringUtils.isNotBlank(pricePlanItemDO.getPictureUrl())) {
                    item.setPictureUrl(pricePlanItemDO.getPictureUrl());
                }
                if (StringUtils.isNotBlank(pricePlanItemDO.getBigPictureUrl())) {
                    item.setBigPictureUrl(pricePlanItemDO.getBigPictureUrl());
                }
                if (StringUtils.isNotBlank(pricePlanItemDO.getDetailBigPictureUrl())) {
                    item.setDetailBigPictureUrl(pricePlanItemDO.getDetailBigPictureUrl());
                }
            }
        }
    }

    /**
     * 商品有所属套餐时，商品解绑门店时需判断解绑门店是否有所属套餐
     *
     * @param storeDTOS 门店
     * @param itemGuid  商品
     */
    private void checkStorePkg(List<StoreDTO> storeDTOS, String itemGuid) {
        List<ItemDO> itemDOS = this.list(new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getParentGuid, itemGuid));
        if (CollectionUtil.isEmpty(itemDOS)) {
            return;
        }
        List<String> itemGuidList = itemDOS.stream().map(ItemDO::getGuid).collect(Collectors.toList());
        List<RSkuSubgroupDO> rSkuSubskuList = skuSubgroupService.list(new LambdaQueryWrapper<RSkuSubgroupDO>().in(RSkuSubgroupDO::getItemGuid, itemGuidList).eq(RSkuSubgroupDO::getIsDelete, Boolean.FALSE));
        if (CollectionUtils.isEmpty(rSkuSubskuList)) {
            return;
        }
        List<String> subgroupGuidList = rSkuSubskuList.stream().map(RSkuSubgroupDO::getSubgroupGuid).collect(Collectors.toList());
        List<SubgroupDO> subgroupDOList = subgroupService.list(new LambdaQueryWrapper<SubgroupDO>().in(SubgroupDO::getGuid, subgroupGuidList).eq(SubgroupDO::getIsDelete, Boolean.FALSE));
        if (CollectionUtils.isEmpty(subgroupDOList)) {
            return;
        }
        List<String> itemList = subgroupDOList.stream().map(SubgroupDO::getItemGuid).collect(Collectors.toList());
        List<ItemDO> itemDOList = list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, itemList).eq(ItemDO::getIsDelete, Boolean.FALSE).eq(ItemDO::getIsEnable, Boolean.TRUE));

        Map<String, List<ItemDO>> storeSubgroupMap = itemDOList.stream().filter(e -> ObjectUtil.isNotNull(e.getStoreGuid())).collect(Collectors.groupingBy(ItemDO::getStoreGuid));
        for (StoreDTO storeDTO : storeDTOS) {
            List<ItemDO> doList = storeSubgroupMap.get(storeDTO.getGuid());
            if (CollectionUtils.isEmpty(doList)) {
                storeDTO.setIsStorePkg(0);
                storeDTO.setIsStoreCount(0);
            } else {
                storeDTO.setIsStorePkg(1);
                storeDTO.setIsStoreCount(doList.size());
            }
        }
//        List<String> storeGuidList = storeDTOS.stream().map(StoreDTO::getGuid).collect(Collectors.toList());
//        List<SubgroupDO> subgroupDOList = subgroupService.list(new LambdaQueryWrapper<SubgroupDO>().in(SubgroupDO::getStoreGuid, storeGuidList).eq(SubgroupDO::getIsDelete,Boolean.FALSE));
//        if (!CollectionUtils.isEmpty(subgroupDOList)){
//            Map<String, List<SubgroupDO>> subgroupMap = subgroupDOList.stream().collect(Collectors.groupingBy(SubgroupDO::getStoreGuid));
//            for (StoreDTO storeDTO : storeDTOS) {
//                List<SubgroupDO> subgroupList = subgroupMap.get(storeDTO.getGuid());
//                if (!CollectionUtils.isEmpty(subgroupList)){
//                    List<String> subgroupGuidList = subgroupList.stream().map(SubgroupDO::getGuid).collect(Collectors.toList());
//                    List<RSkuSubgroupDO> rSkuSubskuList = skuSubgroupService.list(new LambdaQueryWrapper<RSkuSubgroupDO>().in(RSkuSubgroupDO::getSubgroupGuid, subgroupGuidList));
//                    log.info("sku和套餐关系：{}",JacksonUtils.writeValueAsString(rSkuSubskuList));
//                    if (!CollectionUtils.isEmpty(rSkuSubskuList)){
//                        Map<String, List<RSkuSubgroupDO>> rSkuSubskuMap = rSkuSubskuList.stream().collect(Collectors.groupingBy(RSkuSubgroupDO::getItemGuid));
//                        if (CollectionUtils.isEmpty(rSkuSubskuMap.get(itemGuid))){
//                            storeDTO.setIsStorePkg(0);
//                        }else {
//                            storeDTO.setIsStorePkg(1);
//                        }
//                    }
//                }
//                log.info("门店信息：{}",JacksonUtils.writeValueAsString(storeDTO));
//            }
//        }
    }

    @Override
    public List<String> selectSpuItems(ItemSpuReqDTO itemSpuReqDTO) {
        //查询对应spu商品信息
        List<ItemDO> itemDOS = list(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getGuid, itemSpuReqDTO.getItemGuids())
                .or()
                .in(ItemDO::getParentGuid, itemSpuReqDTO.getItemGuids()).eq(ItemDO::getStoreGuid, itemSpuReqDTO.getStoreGuid()));
        List<String> itemGuids = new ArrayList<>();
        if (CollectionUtils.isEmpty(itemDOS)) {
            return itemGuids;
        }
        itemGuids.addAll(itemDOS.stream().map(ItemDO::getGuid).collect(Collectors.toList()));
        itemGuids.addAll(itemDOS.stream().filter(itemDO -> StringUtils.isNotEmpty(itemDO.getParentGuid()))
                .map(ItemDO::getParentGuid).collect(Collectors.toList()));
        return itemGuids;
    }

    @Override
    public Map<String, List<String>> selectSpuItemMaps(ItemSpuReqDTO itemSpuReqDTO) {
        List<String> itemGuids = itemSpuReqDTO.getItemGuids();
        // 查询对应spu商品信息
        List<ItemDO> itemDOS = list(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getGuid, itemGuids)
                .or()
                .in(ItemDO::getParentGuid, itemGuids).eq(ItemDO::getStoreGuid, itemSpuReqDTO.getStoreGuid()));
        if (CollectionUtils.isEmpty(itemDOS)) {
            return Maps.newHashMap();
        }
        Map<String, List<String>> spuItemMaps = Maps.newHashMap();
        for (String itemGuid : itemGuids) {
            List<String> innerItemGuids = spuItemMaps.getOrDefault(itemGuid, Lists.newArrayList());
            for (ItemDO itemDO : itemDOS) {
                if (itemGuid.equals(itemDO.getGuid()) || itemGuid.equals(itemDO.getParentGuid())) {
                    innerItemGuids.add(itemDO.getGuid());
                }
            }
            spuItemMaps.put(itemGuid, innerItemGuids);
        }
        return spuItemMaps;
    }

    /**
     * 赚餐后台查询商品接口
     * 原来是从mdm调，现在要加入菜谱方案，改为直接掉商户后台
     *
     * @param itemQueryDTO ItemQueryDTO
     * @return List<SelectItemDTO>
     */
    @Override
    public List<SelectItemDTO> selectItemList(ItemQueryDTO itemQueryDTO) {
        if (ObjectUtils.isEmpty(itemQueryDTO.getStoreGuid())) {
            throw new BusinessException(STORE_GUID_NOT_EMPTY);
        }
        // 根据门店查询所属品牌
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(itemQueryDTO.getStoreGuid());
        log.info("品牌数据：{}", JacksonUtils.writeValueAsString(brandDTO));
        if (ObjectUtils.isEmpty(brandDTO)) {
            throw new BusinessException("未查询到品牌信息");
        }

        // 普通模式查询商品
        List<SelectItemDTO> selectItemList = new ArrayList<>();

        // 菜谱模式查询商品，需要用菜谱方案数据覆盖
        if (Objects.equals(SalesModelEnum.RECIPE_MODE.getCode(), brandDTO.getSalesModel())) {
            ItemSingleDTO singleDTO = new ItemSingleDTO();
            singleDTO.setStoreGuid(itemQueryDTO.getStoreGuid());
            ItemInfoAndTypeRespDTO respDTO = this.queryStoreItemBySalesModel(singleDTO);
            Map<String, ItemInfoRespDTO> itemMap = respDTO.getItemInfoList().stream()
                    .collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, ItemInfoRespDTO -> ItemInfoRespDTO));
            for (SelectItemDTO selectItem : selectItemList) {
                ItemInfoRespDTO itemInfoRespDTO = itemMap.get(selectItem.getThirdNo());
                selectItem.setBrandGuid(itemInfoRespDTO.getBrandGuid());
                selectItem.setDescription(itemInfoRespDTO.getDescription());
                selectItem.setTypeGuid(itemInfoRespDTO.getTypeGuid());
                selectItem.setPictureUrl(itemInfoRespDTO.getPictureUrl());
                selectItem.setName(itemInfoRespDTO.getName());
                itemInfoRespDTO.getSkuList().forEach(sku ->
                        selectItem.getItemSkuDOList().forEach(
                                skuDTO -> {
                                    if (Objects.equals(sku.getSkuGuid(), skuDTO.getThirdNo()))
                                        skuDTO.setSalePrice(sku.getSalePrice());
                                }
                        )
                );
            }
        } else {
            selectItemList = itemMapper.selectItemList(itemQueryDTO);
        }
        return selectItemList;
    }

    /**
     * 存入item服务的redis
     *
     * @param redisReqDTO 存入redis所需要的值
     * @return 成功
     */
    @Override
    public Boolean putItemRedis(ItemRedisReqDTO redisReqDTO) {
        if (ObjectUtils.isEmpty(redisReqDTO.getRedisKey())) {
            throw new BusinessException("存入的key不能为空");
        }
        if (ObjectUtils.isEmpty(redisReqDTO.getRedisValue())) {
            log.warn("key：{}的值为空", redisReqDTO.getRedisKey());
            if (!ObjectUtils.isEmpty(redisReqDTO.getRedisTime()) && !ObjectUtils.isEmpty(redisReqDTO.getTimeUnit())) {
                redisUtil.set(redisReqDTO.getRedisKey(), null, redisReqDTO.getRedisTime(), redisReqDTO.getTimeUnit());
            } else {
                redisUtil.set(redisReqDTO.getRedisKey(), null);
            }
        } else {
            if (!ObjectUtils.isEmpty(redisReqDTO.getRedisTime()) && !ObjectUtils.isEmpty(redisReqDTO.getTimeUnit())) {
                redisUtil.set(redisReqDTO.getRedisKey(), redisReqDTO.getRedisValue(),
                        redisReqDTO.getRedisTime(), redisReqDTO.getTimeUnit());
            } else {
                redisUtil.set(redisReqDTO.getRedisKey(), redisReqDTO.getRedisValue());
            }
        }
        return true;
    }

    @Override
    public List<ItemExportRespDTO> getExportItemList(ItemExportReqDTO itemExportReqDTO) {
        List<ItemExportRespDTO> respDTOList = itemMapper.selectExportItemList(itemExportReqDTO);
        if (CollectionUtils.isEmpty(respDTOList)) {
            return Lists.newArrayList();
        }
        final List<String> itemGuidList = respDTOList.stream().map(ItemExportRespDTO::getItemGuid).collect(Collectors.toList());
        //查询goods的部分数据
        final List<GoodsExportDTO> goodsExportDTOList = erpFeginService.queryExportGoodsList(itemGuidList);
        if (CollectionUtils.isEmpty(goodsExportDTOList)) {
            return respDTOList;
        }
        goodsExportDTOList.forEach(goods -> {
            for (ItemExportRespDTO resp : respDTOList) {
                if (Objects.equals(goods.getGoodsGuid(), resp.getItemGuid())) {
                    resp.setSafeNum(goods.getSafeNum());
                    resp.setRemainRepertoryNum(goods.getCount());
                    break;
                }
            }
        });
        return respDTOList;
    }

    @Override
    public Map<String, String> findSkusByItemName(List<String> itemNames) {
        if (CollectionUtils.isEmpty(itemNames)) {
            return new HashMap<>();
        }
        //查询商品信息
        String storeGuid = UserContextUtils.getStoreGuid();
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        if (null == brandDTO || StringUtils.isEmpty(brandDTO.getGuid())) {
            return new HashMap<>();
        }
        List<ItemDO> itemDOS = list(new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getItemFrom, 1)
                .eq(ItemDO::getIsEnable, true)
                .eq(ItemDO::getBrandGuid, brandDTO.getGuid())
                .in(ItemDO::getName, itemNames));
        if (CollectionUtils.isEmpty(itemDOS)) {
            return new HashMap<>();
        }
        //查询对应sku
        List<String> itemGuids = itemDOS.stream().map(ItemDO::getGuid).collect(Collectors.toList());
        List<SkuDO> skuDOS = skuService.list(new LambdaQueryWrapper<SkuDO>()
                .in(SkuDO::getItemGuid, itemGuids)
                .eq(SkuDO::getIsEnable, true));
        Map<String, String> skus = new HashMap<>();
        itemDOS.forEach(itemDO -> skuDOS.stream().filter(skuDO -> skuDO.getItemGuid().equals(itemDO.getGuid()))
                .findFirst()
                .ifPresent(skuDO -> skus.put(itemDO.getName(), skuDO.getGuid())));
        return skus;
    }

    /**
     * 根据商品guid获取菜谱中的商品详情列表
     *
     * @param itemStringListDTO 门店guid，商品guid列表
     * @return 商品详情列表
     */
    @Override
    public List<ItemInfoRespDTO> selectItemInfoListOnPlan(ItemStringListDTO itemStringListDTO) {
        if (ObjectUtils.isEmpty(itemStringListDTO.getStoreGuid())) {
            throw new BusinessException(STORE_GUID_NOT_EMPTY);
        }
        if (ObjectUtils.isEmpty(itemStringListDTO.getDataList())) {
            throw new BusinessException(ITEM_GUID_CANNOT_EMPTY);
        }
        List<ItemInfoRespDTO> respDTOList = new ArrayList<>();

        List<PricePlanItemDO> pricePlanItemDOList = getPlanItemList(itemStringListDTO.getStoreGuid(), itemStringListDTO.getDataList());
        if (CollectionUtils.isEmpty(pricePlanItemDOList)) {
            log.warn("菜谱方案商品为空");
            return respDTOList;
        }
        Map<String, PricePlanItemDO> skuMap = pricePlanItemDOList.stream()
                .collect(Collectors.toMap(PricePlanItemDO::getSkuGuid, s -> s));

        // 通过菜谱和商品guid查询商品详情
        List<String> itemGuidList = pricePlanItemDOList.stream()
                .map(PricePlanItemDO::getItemGuid)
                .collect(Collectors.toList());

        respDTOList = this.getItemInfoList(itemGuidList, false);
        // 设置菜谱的价格
        if (!CollectionUtils.isEmpty(respDTOList)) {
            respDTOList.forEach(item ->
                    item.getSkuList().forEach(sku -> {
                        PricePlanItemDO pricePlanItemDO = skuMap.get(sku.getSkuGuid());
                        if (ObjectUtils.isEmpty(pricePlanItemDO)) {
                            log.warn("菜谱里没有这个规格 skuGuid={}", sku.getSkuGuid());
                            return;
                        }
                        sku.setSalePrice(pricePlanItemDO.getSalePrice());
                        sku.setMemberPrice(pricePlanItemDO.getMemberPrice());
                        sku.setAccountingPrice(pricePlanItemDO.getAccountingPrice());
                        sku.setTakeawayAccountingPrice(pricePlanItemDO.getTakeawayAccountingPrice());
                    })
            );
        }
        return respDTOList;
    }

    /**
     * 根据品牌或者门店查询最后一个商品的sort并+1
     *
     * @param itemSingleDTO 品牌或者门店，来源
     * @return 品牌或者门店查询最后一个商品的sort并+1
     */
    @Override
    public int getMaxSort(ItemSingleDTO itemSingleDTO) {
        if (itemSingleDTO.getFrom().equals(ModuleEntranceEnum.STORE.code())) {
            List<ItemDO> itemDOList = this.list(new LambdaQueryWrapper<ItemDO>()
                    .eq(ItemDO::getStoreGuid, itemSingleDTO.getData())
                    .eq(ItemDO::getIsDelete, 0)
                    .orderByDesc(ItemDO::getSort)
            );
            if (CollectionUtils.isEmpty(itemDOList)) {
                return 1;
            }
            return itemDOList.get(0).getSort() + 1;
        } else if (itemSingleDTO.getFrom().equals(ModuleEntranceEnum.BRAND.code())) {
            List<ItemDO> itemDOList = this.list(new LambdaQueryWrapper<ItemDO>()
                    .eq(ItemDO::getBrandGuid, itemSingleDTO.getData())
                    .eq(ItemDO::getIsDelete, 0)
                    .orderByDesc(ItemDO::getSort)
            );
            if (CollectionUtils.isEmpty(itemDOList)) {
                return 1;
            }
            return itemDOList.get(0).getSort() + 1;
        } else {
            throw new ParameterException("模块入口参数错误");
        }
    }

    /**
     * 通过skuGuid查询规格商品信息
     * 暂未包含规格信息
     *
     * @param skuGuidList 规格guid
     * @return 商品信息
     */
    @Override
    public List<ItemInfoRespDTO> listItemInfoBySku(List<String> skuGuidList) {
        if (CollectionUtils.isEmpty(skuGuidList)) {
            throw new BusinessException("规格guid不能为空");
        }
        List<SkuDO> skuDOList = skuMapper.selectList(new LambdaQueryWrapper<SkuDO>()
                .in(!CollectionUtils.isEmpty(skuGuidList), SkuDO::getGuid, skuGuidList)
                .eq(SkuDO::getIsDelete, 0)
                .eq(SkuDO::getIsEnable, 1));
        List<String> itemGuidList = skuDOList.stream()
                .map(SkuDO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        List<ItemDO> itemDOList = this.list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, itemGuidList));
        return MapStructUtils.INSTANCE.itemDOList2ItemInfoRespDTOList(itemDOList);
    }

    /**
     * 获取品牌下所有分类及上架商品
     * 如果商品被菜谱使用要展示其菜谱名称
     * 按品牌商品名称、菜谱名称、sku编号模糊搜索
     *
     * @param itemSingleDTO 品牌guid，关键字
     * @return 分类及商品
     */
    @Override
    public List<TypeItemRespDTO> listTypeAndItemByBrand(ItemSingleDTO itemSingleDTO) {
        String brandGuid = itemSingleDTO.getData();
        if (ObjectUtils.isEmpty(brandGuid)) {
            throw new BusinessException("品牌guid不能为空");
        }
        BrandDTO brandDTO = organizationService.queryBrandByGuid(brandGuid);
        if (ObjectUtils.isEmpty(brandDTO) || ObjectUtils.isEmpty(brandDTO.getSalesModel())) {
            throw new BusinessException("未查询到品牌");
        }

        // 商品
        List<ItemWebRespDTO> itemWebRespDTOList;
        if (Objects.equals(SalesModelEnum.NORMAL_MODE.getCode(), brandDTO.getSalesModel())) {
            // 查询是否企业为单店模式
            String managementModel = cloudEnterpriseFeignClient.queryManagementModel(UserContextUtils.getEnterpriseGuid());
            if (EnterpriseDTO.ManagementModel.SINGLE.name().equals(managementModel)) {
                itemSingleDTO.setSingleFlag(true);
            }
            itemWebRespDTOList = skuMapper.listBrandItemSkuByNormal(itemSingleDTO);
        } else {
            itemWebRespDTOList = queryPlanItem(itemSingleDTO, brandGuid);
        }
        if (CollectionUtils.isEmpty(itemWebRespDTOList)) {
            log.warn("商品查询为空,query={}", JacksonUtils.writeValueAsString(itemSingleDTO));
            return new ArrayList<>();
        }
        // 过滤商品
        if (Objects.nonNull(itemSingleDTO.getItemQueryType()) && ItemQueryTypeEnum.UN_WEIGHT.getCode() == itemSingleDTO.getItemQueryType()) {
            itemWebRespDTOList = itemWebRespDTOList.stream()
                    .filter(e -> Objects.nonNull(e.getItemType()) && (ItemTypeEnum.PACKAGE.getTypeCode() == e.getItemType()
                            || ItemTypeEnum.MULTI_SPEC.getTypeCode() == e.getItemType()
                            || ItemTypeEnum.SINGLE.getTypeCode() == e.getItemType())).collect(Collectors.toList());
        }

        Map<String, List<ItemWebRespDTO>> typeItemMap = itemWebRespDTOList.stream()
                .collect(Collectors.groupingBy(ItemWebRespDTO::getTypeGuid));

        // 分类
        Set<String> typeGuidSet = itemWebRespDTOList.stream()
                .map(ItemWebRespDTO::getTypeGuid)
                .collect(Collectors.toSet());
        List<TypeDO> typeDOList = queryTypeList(itemSingleDTO, typeGuidSet);

        // 返回
        List<TypeItemRespDTO> respDTOList = new ArrayList<>();
        typeDOList.forEach(typeDO -> {
            TypeItemRespDTO respDTO = new TypeItemRespDTO();
            respDTO.setTypeGuid(typeDO.getGuid());
            respDTO.setName(typeDO.getName());

            List<ItemWebRespDTO> dtoList = typeItemMap.get(typeDO.getGuid());
            if (CollectionUtils.isEmpty(dtoList)) {
                return;
            }
            respDTO.setItemWebRespDTOList(dtoList);
            respDTOList.add(respDTO);
        });
        return respDTOList;
    }

    /**
     * 查询分类
     */
    private List<TypeDO> queryTypeList(ItemSingleDTO itemSingleDTO, Set<String> typeGuidSet) {
        LambdaQueryWrapper<TypeDO> qw = new LambdaQueryWrapper<TypeDO>()
                .in(TypeDO::getGuid, typeGuidSet)
                .eq(TypeDO::getIsDelete, 0)
                .eq(TypeDO::getIsEnable, 1);
        if (Objects.nonNull(itemSingleDTO.getSingleFlag()) && Boolean.TRUE.equals(itemSingleDTO.getSingleFlag())) {
            qw.eq(TypeDO::getTypeFrom, ModuleEntranceEnum.STORE.code());
        } else {
            qw.eq(TypeDO::getTypeFrom, ModuleEntranceEnum.BRAND.code());
        }
        return typeService.list(qw);
    }


    /**
     * 查询菜谱模式下的商品
     */
    private List<ItemWebRespDTO> queryPlanItem(ItemSingleDTO itemSingleDTO, String brandGuid) {
        List<ItemWebRespDTO> itemWebRespDTOList = skuMapper.listBrandItemSkuByRecipe(itemSingleDTO);
        if (!CollectionUtils.isEmpty(itemWebRespDTOList)) {
            List<String> skuGuidList = itemWebRespDTOList.stream()
                    .map(ItemWebRespDTO::getSkuGuid)
                    .distinct()
                    .collect(Collectors.toList());
            List<SkuDO> skuDOList = new ArrayList<>(skuService.listByIds(skuGuidList));
            HashMap<String, String> planNameMap = skuService.getPlanNameMap(brandGuid, skuDOList);
            log.info("查询到的规格方案名称,planNameMap={}", planNameMap);
            if (MapUtils.isNotEmpty(planNameMap)) {
                itemWebRespDTOList.forEach(skuItem -> {
                    String planItemName = planNameMap.get(skuItem.getSkuGuid());
                    if (StringUtils.isNotEmpty(planItemName)) {
                        skuItem.setPlanItemName(planItemName);
                    }
                });
            }
        }
        return itemWebRespDTOList;
    }

    /**
     * 根据商品guid获取门店商品详情列表（区分销售模式）
     */
    @Override
    public List<ItemInfoRespDTO> listItemInfoBySalesModel(ItemStringListDTO itemStringListDTO) {
        String storeGuid = itemStringListDTO.getStoreGuid();
        UserContext userContext = UserContextUtils.get();
        userContext.setStoreGuid(storeGuid);
        UserContextUtils.put(userContext);
        if (ObjectUtils.isEmpty(storeGuid)) {
            throw new BusinessException(STORE_GUID_NOT_EMPTY);
        }
        List<String> itemGuidList = itemStringListDTO.getDataList();
        if (ObjectUtils.isEmpty(itemGuidList)) {
            throw new BusinessException(ITEM_GUID_CANNOT_EMPTY);
        }
        List<ItemInfoRespDTO> respDTOList = this.getItemInfoList(itemGuidList, false);
        if (CollectionUtils.isEmpty(respDTOList)) {
            return respDTOList;
        }
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        log.info("【listItemInfoBySalesModel】根据门店guid查询门店关联的品牌信息，brandDTO={}", JacksonUtils.writeValueAsString(brandDTO));
        if (!ObjectUtils.isEmpty(brandDTO) && !ObjectUtils.isEmpty(brandDTO.getSalesModel()) &&
                brandDTO.getSalesModel() == SalesModelEnum.RECIPE_MODE.getCode()) {
            List<PricePlanItemDO> pricePlanItemDOList = getPlanItemList(storeGuid, itemGuidList);
            if (CollectionUtils.isEmpty(pricePlanItemDOList)) {
                log.warn("菜谱方案商品为空");
                return respDTOList;
            }
            Map<String, PricePlanItemDO> planSkuMap = pricePlanItemDOList.stream()
                    .collect(Collectors.toMap(PricePlanItemDO::getSkuGuid, s -> s));

            // 设置菜谱的价格
            respDTOList.forEach(item ->
                    item.getSkuList().forEach(sku -> {
                        PricePlanItemDO pricePlanItemDO = planSkuMap.get(sku.getSkuGuid());
                        if (ObjectUtils.isEmpty(pricePlanItemDO)) {
                            log.warn("菜谱里没有这个规格 skuGuid={}", sku.getSkuGuid());
                            return;
                        }
                        sku.setSalePrice(pricePlanItemDO.getSalePrice());
                        sku.setMemberPrice(pricePlanItemDO.getMemberPrice());
                        sku.setAccountingPrice(pricePlanItemDO.getAccountingPrice());
                        sku.setTakeawayAccountingPrice(pricePlanItemDO.getTakeawayAccountingPrice());
                        sku.setPictureUrl(pricePlanItemDO.getPictureUrl());
                    })
            );
        }
        return respDTOList;
    }

    @Override
    public List<ItemInfoRespDTO> listItemInfoBySalesModelNew(ItemStringListDTO itemStringListDTO) {
        String storeGuid = itemStringListDTO.getStoreGuid();
        UserContext userContext = UserContextUtils.get();
        userContext.setStoreGuid(storeGuid);
        UserContextUtils.put(userContext);
        List<ItemInfoRespDTO> respDTOList = Lists.newArrayList();
        if (ObjectUtils.isEmpty(storeGuid)) {
            throw new BusinessException(STORE_GUID_NOT_EMPTY);
        }
        List<String> itemGuidList = itemStringListDTO.getDataList();
        if (ObjectUtils.isEmpty(itemGuidList)) {
            throw new BusinessException("listItemInfoBySalesModelNew,商品guid不能为空");
        }
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        log.info("【listItemInfoBySalesModel】根据门店guid查询门店关联的品牌信息，brandDTO={}", JacksonUtils.writeValueAsString(brandDTO));
        if (!ObjectUtils.isEmpty(brandDTO) && !ObjectUtils.isEmpty(brandDTO.getSalesModel()) &&
                brandDTO.getSalesModel() == SalesModelEnum.RECIPE_MODE.getCode()) {
            List<PricePlanItemDO> pricePlanItemDOList = getPlanItemList(storeGuid, itemGuidList);
            if (CollectionUtils.isEmpty(pricePlanItemDOList)) {
                log.warn("listItemInfoBySalesModelNew,菜谱方案商品为空");
                return respDTOList;
            }
            pricePlanItemDOList = pricePlanItemDOList
                    .stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PricePlanItemDO::getItemGuid))), ArrayList::new));


            Map<String, Integer> itemMap = pricePlanItemDOList.stream()
                    .collect(Collectors.toMap(PricePlanItemDO::getItemGuid, PricePlanItemDO::getSort));
            log.info("菜谱信息，itemMap={}", JacksonUtils.writeValueAsString(itemMap));

            List<String> typeList = pricePlanItemDOList.stream().map(PricePlanItemDO::getTypeGuid).collect(Collectors.toList());
            log.info("菜谱信息，typeList={}", JacksonUtils.writeValueAsString(typeList));

            Map<String, ItemInfoTypeRespDTO> infoTypeRespDTOMap = getStringItemInfoTypeRespDTOMap(storeGuid, typeList, 2);

            log.info("菜谱信息，pricePlanItemDOList={}", JacksonUtils.writeValueAsString(pricePlanItemDOList));
            // 设置菜谱的价格
            for (PricePlanItemDO itemInfoRespDTO : pricePlanItemDOList) {
                ItemInfoRespDTO infoRespDTO = new ItemInfoRespDTO();
                ItemInfoTypeRespDTO itemInfoTypeRespDTO = infoTypeRespDTOMap.get(itemInfoRespDTO.getTypeGuid());
                log.info("菜谱模式key获取，pricePlanItemDOList={}", JacksonUtils.writeValueAsString(itemInfoTypeRespDTO));
                Integer planItemSort = itemMap.get(itemInfoRespDTO.getItemGuid());
                if (!ObjectUtils.isEmpty(itemInfoTypeRespDTO)) {
                    infoRespDTO.setTypeSort(itemInfoTypeRespDTO.getSort());
                }
                if (!ObjectUtils.isEmpty(planItemSort)) {
                    infoRespDTO.setSort(planItemSort);
                }
                infoRespDTO.setItemGuid(itemInfoRespDTO.getItemGuid());
                infoRespDTO.setTypeGuid(itemInfoRespDTO.getTypeGuid());
                respDTOList.add(infoRespDTO);
            }
        } else {
            respDTOList = this.getItemInfoListNew(itemGuidList);
        }
        log.info("新加菜品信息，respDTOList={}", JacksonUtils.writeValueAsString(respDTOList));
        return respDTOList;
    }

    @Override
    public PrintSortRespDTO selectPrintItemType(PrintItemTypeDTO itemTypeDTO) {
        PrintSortRespDTO printSortRespDTO = new PrintSortRespDTO();
        //获取分类
        List<TypeRespDTO> typeRespDTOList = Lists.newArrayList();
        List<ItemPrintTypeRespDTO> itemTypeRespDTOList = Lists.newArrayList();
        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData(itemTypeDTO.getStoreGuid());
        List<TypeWebRespDTO> typeWebRespDTOS = typeService.queryType(itemSingleDTO);
        log.info("获取typeWebRespDTOS={}", JacksonUtils.writeValueAsString(typeWebRespDTOS));
        if (!CollectionUtils.isEmpty(typeWebRespDTOS)) {
            for (TypeWebRespDTO typeWebRespDTO : typeWebRespDTOS) {
                TypeRespDTO typeRespDTO = new TypeRespDTO();
                typeRespDTO.setSort(typeWebRespDTO.getSort());
                typeRespDTO.setTypeGuid(typeWebRespDTO.getTypeGuid());
                typeRespDTO.setName(typeWebRespDTO.getName());
                typeRespDTO.setStoreGuid(typeWebRespDTO.getStoreGuid());
                typeRespDTO.setPricePlanGuid(typeWebRespDTO.getPricePlanGuid());
                typeRespDTOList.add(typeRespDTO);
            }
        }
//        selectItemListForWeb();
        printSortRespDTO.setTypeRespDTOList(typeRespDTOList);
        List<String> typeGuid = typeRespDTOList.stream().map(TypeRespDTO::getTypeGuid).collect(Collectors.toList());
        itemTypeDTO.setTypeGuid(typeGuid);

        log.info("查询商品参数itemDOList={}", JacksonUtils.writeValueAsString(itemTypeDTO));
        // 如果上架的值是-1,则不过滤是否上架
//        if (Integer.valueOf("-1").equals(itemTypeDTO.getIsRack())) {
//            itemTypeDTO.setIsRack(null);
//        }
        // 符合上下架过滤条件的商品GUID
        LambdaQueryWrapper<ItemDO> itemPageWrapper = new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getIsEnable, true)
                .in(ItemDO::getGuid, itemTypeDTO.getItemList())
//                .eq(!StringUtils.isEmpty(itemTypeDTO.getStoreGuid()), ItemDO::getStoreGuid, itemTypeDTO.getStoreGuid())
                .in(!CollectionUtils.isEmpty(typeGuid), ItemDO::getTypeGuid, itemTypeDTO.getTypeGuid())
                .ne(Objects.isNull(itemTypeDTO.getModel()), ItemDO::getItemType, 5);

        itemPageWrapper.orderByAsc(Objects.isNull(itemTypeDTO.getModel()), ItemDO::getSort)
                .orderByDesc(Objects.isNull(itemTypeDTO.getModel()), ItemDO::getGmtCreate)
                .orderByDesc(Objects.nonNull(itemTypeDTO.getModel()), ItemDO::getGmtCreate);

        List<ItemDO> itemDOList = this.list(itemPageWrapper);
        log.info("获取itemDOList={}", JacksonUtils.writeValueAsString(itemDOList));
        if (!CollectionUtils.isEmpty(itemDOList)) {
            for (ItemDO itemDO : itemDOList) {
                ItemPrintTypeRespDTO itemTypeRespDTO = new ItemPrintTypeRespDTO();
                itemTypeRespDTO.setItemType(itemDO.getItemType());
                itemTypeRespDTO.setItemGuid(itemDO.getGuid());
                itemTypeRespDTO.setSort(itemDO.getSort());
                itemTypeRespDTOList.add(itemTypeRespDTO);
            }

        }
        log.info("获取itemTypeRespDTOList={}", JacksonUtils.writeValueAsString(itemTypeRespDTOList));
        printSortRespDTO.setItemTypeRespDTOList(itemTypeRespDTOList);
        log.info("获取门店分类以及指定商品返参,itemTypeDTO={}", JacksonUtils.writeValueAsString(printSortRespDTO));
        return printSortRespDTO;
    }

    @Override
    public List<ItemInfoRespDTO> queryParentItemInfo(List<String> itemGuidList) {
        if (CollectionUtils.isEmpty(itemGuidList)) {
            return Lists.newArrayList();
        }
        List<ItemDO> itemDOList = itemMapper.findByGuids(itemGuidList);
        if (CollectionUtils.isEmpty(itemDOList)) {
            return Lists.newArrayList();
        }
        List<String> parentGuidList = itemDOList.stream()
                .map(ItemDO::getParentGuid)
                .filter(parentGuid -> !StringUtils.isEmpty(parentGuid))
                .distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(parentGuidList)) {
            List<ItemDO> parentItemDOList = itemMapper.findByGuids(parentGuidList);
            itemDOList.addAll(parentItemDOList);
        }
        return MapStructUtils.INSTANCE.itemDOList2ItemInfoRespDTOList(itemDOList);
    }

    /**
     * 通过门店和当前时间查询当前所使用的菜谱，并获取菜谱商品
     *
     * @param storeGuid    门店id
     * @param itemGuidList 商品id列表，可空
     * @return 菜谱商品
     */
    private List<PricePlanItemDO> getPlanItemList(String storeGuid, List<String> itemGuidList) {
        List<PricePlanItemDO> pricePlanItemDOList = new ArrayList<>();
        // 根据门店查询生效的菜谱
        LocalDateTime now = LocalDateTime.now();

        List<PricePlanNowDTO> planNowList = pricePlanStoreService.findPlanNowStoreGuid(now, storeGuid);
        if (CollUtil.isEmpty(planNowList)) {
            log.warn("没有生效的菜谱方案 now={},storeGuid={}", now, storeGuid);
            return pricePlanItemDOList;
        }

        // 定位生效的菜谱
        org.apache.commons.lang3.tuple.Pair<PricePlanNowDTO, PricePlanNowDTO> planPair = locatePlan(planNowList, now);
        PricePlanNowDTO pricePlanNowAllTime = planPair.getLeft();
        PricePlanNowDTO pricePlanNowTime = planPair.getRight();

        // 没有菜谱方案
        log.info("生效的菜谱：pricePlanNowTime={},pricePlanNowAllTime={}", JacksonUtils.writeValueAsString(pricePlanNowTime),
                JacksonUtils.writeValueAsString(pricePlanNowAllTime));
        if (Objects.isNull(pricePlanNowTime) && Objects.isNull(pricePlanNowAllTime)) {
            log.warn("没有生效的菜谱方案");
            return pricePlanItemDOList;
        }

        if (!Objects.isNull(pricePlanNowTime)) {
            // 特殊时段菜谱 商品列表
            pricePlanItemDOList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                    .eq(PricePlanItemDO::getIsDelete, 0)
                    .ne(PricePlanItemDO::getIsSoldOut, ItemStateEnum.IMMEDIATELY_DELETE.getCode())
                    .eq(!Objects.isNull(pricePlanNowTime), PricePlanItemDO::getPlanGuid, pricePlanNowTime.getPlanGuid())
                    .in(!CollectionUtils.isEmpty(itemGuidList), PricePlanItemDO::getItemGuid, itemGuidList)
            );
            return pricePlanItemDOList;
        }
        // 全时段菜谱 商品列表
        pricePlanItemDOList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                .ne(PricePlanItemDO::getIsSoldOut, ItemStateEnum.IMMEDIATELY_DELETE.getCode())
                .eq(PricePlanItemDO::getIsDelete, 0)
                .eq(PricePlanItemDO::getPlanGuid, pricePlanNowAllTime.getPlanGuid())
                .in(!CollectionUtils.isEmpty(itemGuidList), PricePlanItemDO::getItemGuid, itemGuidList)
        );
        return pricePlanItemDOList;
    }

    private org.apache.commons.lang3.tuple.Pair<PricePlanNowDTO, PricePlanNowDTO> locatePlan(List<PricePlanNowDTO> planNowList, LocalDateTime now) {
        PricePlanNowDTO pricePlanNowAllTime = null;
        PricePlanNowDTO pricePlanNowTime = null;
        log.info("当前时间：" + getNowTime(now));
        for (PricePlanNowDTO planNow : planNowList) {
            if (planNow.getSellTimeType() == 0) {
                // 全时段菜品方案
                pricePlanNowAllTime = planNow;
            } else {
                // 特殊时段菜品方案
                pricePlanNowTime = specialPlanLocate(pricePlanNowTime, getNowTime(now), planNow);
            }
        }
        return org.apache.commons.lang3.tuple.Pair.of(pricePlanNowAllTime, pricePlanNowTime);
    }

    private int getNowTime(LocalDateTime now) {
        String minute = (now.getMinute() + "").length() == 1 ? "0" + now.getMinute() : now.getMinute() + "";
        return Integer.parseInt(now.getHour() + "" + minute);
    }

    private PricePlanNowDTO specialPlanLocate(PricePlanNowDTO pricePlanNowTime, int nowTime, PricePlanNowDTO planNow) {
        LocalDateTime startTime = planNow.getStartTime();
        LocalDateTime endTime = planNow.getEndTime();
        String startMinute = (startTime.getMinute() + "").length() == 1 ? "0" + startTime.getMinute() : startTime.getMinute() + "";
        int startTimeInt = Integer.parseInt(startTime.getHour() + "" + startMinute);
        String endMinute = (endTime.getMinute() + "").length() == 1 ? "0" + endTime.getMinute() : endTime.getMinute() + "";
        int endTimeInt = Integer.parseInt(endTime.getHour() + "" + endMinute);
        log.info("特殊时段时间,nowTime->{}, startTimeInt->{}, endTimeInt->{}", nowTime, startTimeInt, endTimeInt);
        if (endTimeInt > startTimeInt) {
            // 没有跨天
            if (nowTime >= startTimeInt && nowTime <= endTimeInt) {
                pricePlanNowTime = planNow;
                log.info("没有垮天特殊时段方案筛选结果：,{}", JacksonUtils.writeValueAsString(pricePlanNowTime));
            }
        } else {
            // 跨天了
            if (nowTime >= startTimeInt || nowTime <= endTimeInt) {
                pricePlanNowTime = planNow;
                log.info("跨天了特殊时段方案筛选结果：,{}", JacksonUtils.writeValueAsString(pricePlanNowTime));
            }
        }
        return pricePlanNowTime;
    }

    @Override
    public String getSubItemGuid(String parentItemGuid) {
        //如果是菜谱模式直接返回
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(UserContextUtils.getStoreGuid());
        if (brandDTO != null && brandDTO.getSalesModel() == SalesModelEnum.RECIPE_MODE.getCode()) {
            return null;
        }
        return itemMapper.getSubItemGuid(parentItemGuid, UserContextUtils.getStoreGuid());
    }

    /**
     * 查询商品是否所在门店
     *
     * @param itemSingleDTO 门店、品牌、商品
     * @return 存在的门店
     */
    @Override
    public List<String> queryItemStore(ItemSingleDTO itemSingleDTO) {
        List<String> skuGuidList = itemSingleDTO.getSkuGuids();
        if (CollectionUtils.isEmpty(skuGuidList)) {
            throw new BusinessException("商品skuGuid不能为空");
        }
        List<String> itemGuidList = itemSingleDTO.getItemList();
        if (CollectionUtils.isEmpty(itemGuidList)) {
            throw new BusinessException("商品Guid不能为空");
        }
        List<String> storeGuidList = itemSingleDTO.getStoreGuids();
        if (CollectionUtils.isEmpty(storeGuidList)) {
            throw new BusinessException("门店Guid不能为空");
        }
        BrandDTO brandDTO = organizationService.queryBrandByGuid(itemSingleDTO.getData());
        if (!ObjectUtils.isEmpty(brandDTO) && brandDTO.getSalesModel() == SalesModelEnum.RECIPE_MODE.getCode()) {
            List<String> existSkuList = new ArrayList<>();
            storeGuidList.forEach(storeGuid -> {
                List<PricePlanItemDO> planItemList = this.getPlanItemList(storeGuid, itemGuidList);
                if (!CollectionUtils.isEmpty(planItemList)) {
                    existSkuList.add(storeGuid);
                }
            });
            return existSkuList;
        }
        List<SkuDO> skuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>()
                .in(SkuDO::getStoreGuid, storeGuidList)
                .in(SkuDO::getParentGuid, skuGuidList)
                .eq(SkuDO::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(SkuDO::getIsEnable, Boolean.TRUE)
                .eq(SkuDO::getIsRack, RACK)
        );
        return skuDOList.stream()
                .map(SkuDO::getStoreGuid)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<ItemInfoRespDTO> getItemNameList(ItemStringListDTO itemStringListDTO) {
        if (CollectionUtils.isEmpty(itemStringListDTO.getDataList())) {
            log.info("商品名称为空！");
            return Lists.newArrayList();
        }
        return itemMapper.getItemNameList(itemStringListDTO);
    }

    @Override
    public Map<String, String> queryParentItemGuidBySku(ItemStringListDTO query) {
        if (CollectionUtils.isEmpty(query.getDataList())) {
            log.info("skuGuid为空！");
            return Maps.newHashMap();
        }
        List<SkuDO> skuDOS = skuMapper.selectList(new LambdaQueryWrapper<SkuDO>()
                .in(SkuDO::getGuid, query.getDataList()));
        if (CollectionUtils.isEmpty(skuDOS)) {
            log.info("sku为空！");
            return Maps.newHashMap();
        }
        List<String> itemGuidList = skuDOS.stream()
                .map(SkuDO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        List<ItemDO> itemDOS = itemMapper.selectList(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getGuid, itemGuidList));
        Map<String, ItemDO> itemDOMap = itemDOS.stream()
                .collect(Collectors.toMap(ItemDO::getGuid, Function.identity(), (a, b) -> b));
        Map<String, String> skuItemMap = new HashMap<>();
        skuDOS.forEach(skuDO -> {
            ItemDO itemDO = itemDOMap.get(skuDO.getItemGuid());
            if (Objects.isNull(itemDO)) {
                return;
            }
            String itemParentGuid = itemDO.getParentGuid();
            if (org.springframework.util.StringUtils.isEmpty(itemParentGuid)) {
                itemParentGuid = itemDO.getGuid();
            }
            skuItemMap.put(skuDO.getGuid(), itemParentGuid);
        });
        return skuItemMap;
    }

    @Override
    public Map<String, String> queryParentItemGuidByItem(ItemStringListDTO query) {
        if (CollectionUtils.isEmpty(query.getDataList())) {
            log.info("itemGuid为空！");
            return Maps.newHashMap();
        }
        List<ItemDO> itemDOS = itemMapper.selectList(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getGuid, query.getDataList()));
        if (CollectionUtils.isEmpty(itemDOS)) {
            log.info("item为空！");
            return Maps.newHashMap();
        }
        Map<String, String> itemMap = new HashMap<>();
        itemDOS.forEach(item -> {
            String itemParentGuid = item.getParentGuid();
            if (org.springframework.util.StringUtils.isEmpty(itemParentGuid)) {
                itemParentGuid = item.getGuid();
            }
            itemMap.put(item.getGuid(), itemParentGuid);
        });
        return itemMap;
    }

    @Override
    public Page<ItemWebRespDTO> queryItemByBrand(ItemQueryReqDTO queryReqDTO) {
        if (CollectionUtils.isEmpty(queryReqDTO.getBrandGuidList())) {
            throw new BusinessException("品牌guid不能为空");
        }

        MPPage<ItemDO> itemDOIPageQuery = new MPPage<>(queryReqDTO.getCurrentPage(), queryReqDTO.getPageSize());
        LambdaQueryWrapper<ItemDO> itemPageWrapper = new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getIsEnable, true)
                .in(!CollectionUtils.isEmpty(queryReqDTO.getBrandGuidList()), ItemDO::getBrandGuid, queryReqDTO.getBrandGuidList())
                .eq(!StringUtils.isEmpty(queryReqDTO.getTypeGuid()), ItemDO::getTypeGuid, queryReqDTO.getTypeGuid())
                .in(!CollectionUtils.isEmpty(queryReqDTO.getItemTypeList()), ItemDO::getItemType, queryReqDTO.getItemTypeList())
                .and(!StringUtils.isEmpty(queryReqDTO.getSearchKey()),
                        wrapper -> wrapper.like(ItemDO::getName, queryReqDTO.getSearchKey())
                                .or()
                                .like(ItemDO::getGuid, queryReqDTO.getSearchKey())
                );
        itemPageWrapper.orderByDesc(ItemDO::getSort)
                .orderByDesc(ItemDO::getGmtCreate);
        IPage<ItemDO> itemDOIPage = this.page(itemDOIPageQuery, itemPageWrapper);
        return MapStructUtils.INSTANCE.itemDOIPage2itemWebRespDTOPage(itemDOIPage);
    }

    @Override
    public List<ItemWebRespDTO> queryItemByGuid(ItemStringListDTO query) {
        if (CollectionUtils.isEmpty(query.getDataList())) {
            throw new BusinessException(ITEM_GUID_CANNOT_EMPTY);
        }
        List<ItemDO> itemDOList = this.list(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getGuid, query.getDataList())
                .eq(ItemDO::getIsEnable, true)
        );
        return MapStructUtils.INSTANCE.itemDOList2itemWebRespDTOList(itemDOList);
    }

    /**
     * 查询推荐商品
     */
    @Override
    public List<ItemSynRespDTO> queryRecommendItem(ItemSingleDTO query) {
        if (com.holderzone.framework.util.StringUtils.isEmpty(query.getStoreGuid())) {
            throw new BusinessException(STORE_GUID_NOT_EMPTY);
        }

        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(query.getStoreGuid());
        log.info("[菜品模式数据]brandDTO={}", JacksonUtils.writeValueAsString(brandDTO));
        if (!ObjectUtils.isEmpty(brandDTO.getSalesModel()) && brandDTO.getSalesModel() == SalesModelEnum.NORMAL_MODE.getCode()) {
            return getOrdinaryItemRespDTOS(query);
        }

        List<PricePlanNowDTO> planNowList = pricePlanStoreService.findPlanNowStoreGuid(LocalDateTime.now(), query.getStoreGuid());
        log.info("[菜品方案查询结果]planNowList={}", JacksonUtils.writeValueAsString(planNowList));

        // 没有生效的菜谱
        if (CollectionUtil.isEmpty(planNowList)) {
            log.warn("没有生效的菜谱");
            return getOrdinaryItemRespDTOS(query);
        }
        // 根据时间过滤生效的菜谱guid
        String effectStorePlanGuid = itemHelper.getEffectStorePlanGuid(planNowList);
        if (Objects.isNull(effectStorePlanGuid)) {
            log.warn("时间过滤没有生效的菜谱");
            return getOrdinaryItemRespDTOS(query);
        }
        List<PricePlanItemDO> pricePlanItemDOList = planItemRepository.listEffectPlanItem(effectStorePlanGuid, null);
        if (CollUtil.isEmpty(pricePlanItemDOList)) {
            log.info("[菜谱方案下选的菜品为空]effectStorePlanGuid={}", effectStorePlanGuid);
            return getOrdinaryItemRespDTOS(query);
        }

        List<String> allItemGuidList = pricePlanItemDOList.stream()
                .map(PricePlanItemDO::getItemGuid)
                .collect(Collectors.toList());

        List<ItemDO> itemDOList = list(new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getIsEnable, true)
                .in(ItemDO::getGuid, allItemGuidList)
                .eq(ItemDO::getIsRecommend, Boolean.TRUE)
        );
        // 根据菜谱商品重新排序
        itemDOList.sort(Comparator.comparing(ItemDO::getSort));
        // 将商品转成返回的实体
        List<ItemSynRespDTO> itemSynRespDTOList = MapStructUtils.INSTANCE.itemDOList2itemSynRespDTOList(itemDOList);
        Map<String, PricePlanItemDO> planItemDOMap = pricePlanItemDOList.stream()
                .collect(Collectors.toMap(PricePlanItemDO::getItemGuid, Function.identity(), (v1, v2) -> v1));
        itemSynRespDTOList.forEach(itemSynRespDTO -> {
            PricePlanItemDO pricePlanItemDO = planItemDOMap.get(itemSynRespDTO.getItemGuid());
            if (ObjectUtils.isEmpty(pricePlanItemDO)) {
                return;
            }
            itemSynRespDTO.setName(pricePlanItemDO.getPlanItemName());
            itemSynRespDTO.setPictureUrl(pricePlanItemDO.getPictureUrl());
            itemSynRespDTO.setBigPictureUrl(pricePlanItemDO.getBigPictureUrl());
            itemSynRespDTO.setDetailBigPictureUrl(pricePlanItemDO.getDetailBigPictureUrl());
            itemSynRespDTO.setEnglishIngredientsDesc(pricePlanItemDO.getEnglishIngredientsDesc());
            itemSynRespDTO.setEnglishBrief(pricePlanItemDO.getEnglishBrief());
        });
        return itemSynRespDTOList;
    }

    private List<ItemSynRespDTO> getOrdinaryItemRespDTOS(ItemSingleDTO query) {
        List<ItemDO> itemDOList = itemMapper.selectList(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getStoreGuid, query.getStoreGuid())
                .eq(ItemDO::getIsEnable, Boolean.TRUE)
                .eq(ItemDO::getIsDelete, Boolean.FALSE)
                .eq(ItemDO::getIsRecommend, Boolean.TRUE)
        );
        return MapStructUtils.INSTANCE.itemDOList2itemSynRespDTOList(itemDOList);
    }

    @Override
    public List<ItemSynRespDTO> queryPlanItemByStore(String storeGuid) {
        List<PricePlanNowDTO> planNowList = pricePlanStoreService.findPlanNowStoreGuid(LocalDateTime.now(), storeGuid);
        log.info("[菜品方案查询结果]planNowList={}", JacksonUtils.writeValueAsString(planNowList));

        // 没有生效的菜谱
        if (CollUtil.isEmpty(planNowList)) {
            log.warn("没有生效的菜谱");
            return Lists.newArrayList();
        }
        // 根据时间过滤生效的菜谱guid
        String effectStorePlanGuid = itemHelper.getEffectStorePlanGuid(planNowList);
        if (Objects.isNull(effectStorePlanGuid)) {
            log.warn("时间过滤没有生效的菜谱");
            return Lists.newArrayList();
        }
        List<PricePlanItemDO> pricePlanItemDOList = planItemRepository.listEffectPlanItem(effectStorePlanGuid, null);
        if (CollUtil.isEmpty(pricePlanItemDOList)) {
            log.info("[菜谱方案下选的菜品为空]effectStorePlanGuid={}", effectStorePlanGuid);
            return Lists.newArrayList();
        }

        List<String> allItemGuidList = pricePlanItemDOList.stream()
                .map(PricePlanItemDO::getItemGuid)
                .collect(Collectors.toList());

        List<ItemDO> itemDOList = list(new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getIsEnable, true)
                .in(ItemDO::getGuid, allItemGuidList)
                .eq(ItemDO::getIsRecommend, Boolean.TRUE)
        );
        // 根据菜谱商品重新排序
        itemDOList.sort(Comparator.comparing(ItemDO::getSort));
        // 将商品转成返回的实体
        List<ItemSynRespDTO> itemSynRespDTOList = MapStructUtils.INSTANCE.itemDOList2itemSynRespDTOList(itemDOList);
        Map<String, PricePlanItemDO> planItemDOMap = pricePlanItemDOList.stream()
                .collect(Collectors.toMap(PricePlanItemDO::getItemGuid, Function.identity(), (v1, v2) -> v1));
        itemSynRespDTOList.forEach(itemSynRespDTO -> {
            PricePlanItemDO pricePlanItemDO = planItemDOMap.get(itemSynRespDTO.getItemGuid());
            if (ObjectUtils.isEmpty(pricePlanItemDO)) {
                return;
            }
            itemSynRespDTO.setName(pricePlanItemDO.getPlanItemName());
            itemSynRespDTO.setPictureUrl(pricePlanItemDO.getPictureUrl());
            itemSynRespDTO.setBigPictureUrl(pricePlanItemDO.getBigPictureUrl());
            itemSynRespDTO.setDetailBigPictureUrl(pricePlanItemDO.getDetailBigPictureUrl());
            itemSynRespDTO.setEnglishIngredientsDesc(pricePlanItemDO.getEnglishIngredientsDesc());
            itemSynRespDTO.setEnglishBrief(pricePlanItemDO.getEnglishBrief());
        });
        return itemSynRespDTOList;
    }

}
