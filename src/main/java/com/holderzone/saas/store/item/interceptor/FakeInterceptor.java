package com.holderzone.saas.store.item.interceptor;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.util.JacksonUtils;
import io.undertow.servlet.spec.HttpServletRequestImpl;
import io.undertow.util.HeaderMap;
import io.undertow.util.HttpString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FakeInterceptor
 * @date 2019/02/19 上午11:15
 * @description //仅开发阶段使用的，上线后去掉
 * @program holder-saas-store-item
 */
@Configuration
public class FakeInterceptor implements HandlerInterceptor {
    private static final Logger log = LoggerFactory.getLogger(FakeInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid("6491846954039715841");
        if (request instanceof HttpServletRequestImpl) {
            HttpServletRequestImpl httpServletRequest = (HttpServletRequestImpl) request;
            HeaderMap requestHeaders = httpServletRequest.getExchange().getRequestHeaders();
            requestHeaders.put(new HttpString(USER_INFO), JacksonUtils.writeValueAsString(userContext));
            log.info("Fake userInfo works now.");
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }
}
