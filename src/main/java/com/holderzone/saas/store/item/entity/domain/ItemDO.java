package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.enums.item.ModuleEntranceEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("hsi_item")
@NoArgsConstructor
@AllArgsConstructor
public class ItemDO extends BasePushDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @TableLogic
    private Integer isDelete;

    /**
     * 商品GUID
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 商品关联的分类GUID
     */
    private String typeGuid;
    /**
     * 价格方案GUID
     */
    private String pricePlanGuid;
    /**
     * 品牌GUID
     */
    private String brandGuid;

    /**
     * 商品来源（0：门店自己创建的商品，1：品牌自己创建的商品,2:被推送过来的商品）
     */
    private Integer itemFrom;

    /**
     * 商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品 5.团餐。
     */
    private Integer itemType;

    /**
     * 是否售罄:0 否 1 是
     */
    private Integer isSoldOut;

    /**
     * 属性组状态:0：无属性; 1:有属性; 2:有必选属性组(如果是套餐，则该字段=0)
     */
    private Integer hasAttr;

    /**
     * 拼音简码
     */
    private String pinyin;

    /**
     * 商品名称简写
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String nameAbbr;

    /**
     * 商品描述
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String description;

    /**
     * 图片路径数组json
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String pictureUrl;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否产生订单：0：否，1：是。
     */
    private Integer inOrder;

    /**
     * 是否是新品（0：否，1：是）(新品)
     */
    private Integer isNew;

    /**
     * 是否热销：0：否，1：是
     */
    private Integer isBestseller;

    /**
     * 是否是招牌：0：否，1：是
     */
    private Integer isSign;

    /**
     * 是否因重复而改名：0：否，1：是
     */
    private Integer nameChange;

    /**
     * 图文详情（冗余小程序端字段）
     */
    private String remarkDetail;

    /**
     * 配送类型 1同城配送;2快递运输（冗余小程序端字段）
     */
    private Integer mallDelivery;

    /**
     * code（冗余小程序端字段）
     */
    private String code;

    /**
     * 好评数（冗余小程序端字段）
     */
    private Integer upCount;

    /**
     * 差评数（冗余小程序端字段）
     */
    private Integer downCount;

    /**
     * 审核状态（冗余小程序端字段）
     */
    private Integer auditStatus;

    /**
     * 视频url json数组（冗余小程序端字段）
     */
    private String videoUrls;
    /**
     * 是否推荐（冗余小程序端字段） 0否 1是
     */
    private Integer isRecommend;
    /**
     *  是否启用
     */
    private Boolean isEnable;


    // 仅用于被推送商品的创建
    public ItemDO(ItemInfoRespDTO itemInfoRespDTO) {
        this.typeGuid = itemInfoRespDTO.getTypeGuid();
        this.brandGuid = itemInfoRespDTO.getBrandGuid();
        this.itemFrom = ModuleEntranceEnum.PUSH.code();
        this.itemType = itemInfoRespDTO.getItemType();
        this.isSoldOut = itemInfoRespDTO.getIsSoldOut();
        this.hasAttr = itemInfoRespDTO.getHasAttr();
        this.name = itemInfoRespDTO.getName();
        this.pinyin = itemInfoRespDTO.getPinyin();
        this.nameAbbr = itemInfoRespDTO.getNameAbbr();
        this.description = itemInfoRespDTO.getDescription();
        this.pictureUrl = itemInfoRespDTO.getPictureUrl();
        this.sort = itemInfoRespDTO.getSort();
        this.parentGuid = itemInfoRespDTO.getItemGuid();
        this.inOrder = 0;
        this.isNew = itemInfoRespDTO.getIsNew();
        this.isRecommend = itemInfoRespDTO.getIsRecommend();
        this.isBestseller = itemInfoRespDTO.getIsBestseller();
        this.isSign = itemInfoRespDTO.getIsSign();
    }
}
