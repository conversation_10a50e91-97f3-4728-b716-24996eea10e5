package com.holderzone.saas.store.item.service;

import com.holderzone.saas.store.item.dto.PushRecordStatusUpdateReqDTO;
import com.holderzone.saas.store.item.dto.SyncItemDTO;

public interface SyncPricePlanToItemService {
    /**
     * 将价格方案中的菜品信息同步新增到系统[旧的]的item中(hsi_item)
     *
     * @param itemDTO       价格方案菜品DTO
     * @param storeGuid     店铺Guid
     * @param brandGuid     品牌Guid
     * @param pricePlanGuid 价格方案Guid
     * @return String
     * <AUTHOR> chen
     * @version 1.0
     * @since 2020-10-28
     */
    String addPricePlanItem(SyncItemDTO itemDTO, String storeGuid, String brandGuid, String pricePlanGuid);

    /**
     * 将价格方案中的套餐信息同步新增到系统[旧的]的item中(hsi_item)
     *
     * @param itemDTO       价格方案菜品DTO
     * @param storeGuid     店铺Guid
     * @param brandGuid     品牌Guid
     * @param pricePlanGuid 价格方案Guid
     * @return String
     * <AUTHOR> chen
     * @version 1.0
     * @since 2020-10-28
     */
    String addPricePlanPkgItem(SyncItemDTO itemDTO, String storeGuid, String brandGuid, String pricePlanGuid);

    /**
     * 通过父级GUID删除Item
     *
     * @param parentGuid
     * @param storeGuid
     * @return
     */
    String deleteItemByParentGuid(String parentGuid, String storeGuid);

    /**
     * 更新推送记录表
     *
     * @param recordStatusUpdateReqDTO
     * @return
     */
    String updatePushRecordData(PushRecordStatusUpdateReqDTO recordStatusUpdateReqDTO);


    /**
     * 处理套餐相关的信息
     * <p>
     * 处理hsi_subgroup、hsi_i_r_sku_subgroup、hsi_sku
     * 这几张数据表的关系
     * </p>
     *
     * @param itemGuid
     * @param storeGuid
     */
    void dealPkgInfo(String itemGuid, String storeGuid);

    /**
     * 处理hsi_subgroup中的item的所属关系
     *
     * @param itemGuid
     * @param storeGuid
     * @param newItemGuid
     */
    void updateSubGroupItemToNewItem(String itemGuid, String storeGuid, String newItemGuid);
}
