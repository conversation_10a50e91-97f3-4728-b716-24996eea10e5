package com.holderzone.saas.store.item.entity.bo;

import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class SkuItemMappingBO implements Serializable {

    private static final long serialVersionUID = 4324881221291213613L;

    /**
     * 当前模式可选的skuGUID
     */
    private List<String> choiceSkuGuids;

    /**
     * sku
     */
    private List<SkuDO> innerSkuList;

    /**
     * 商品map
     */
    private Map<String, ItemDO> itemMap;

    /**
     * 门店菜谱商品名称
     */
    private Map<String, String> innerPlanNameMap;

}
