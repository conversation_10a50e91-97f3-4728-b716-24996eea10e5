package com.holderzone.saas.store.item.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PushBO
 * @date 2019/02/22 下午5:52
 * @description //TODO
 * @program holder-saas-store-item
 */
@Data
public class PushBO {

    @ApiModelProperty(value = "guid")
    private String guid;

    @ApiModelProperty(value = "品牌GUID")
    private String brandGuid;

    @ApiModelProperty(value = "关联的门店GUID")
    private String storeGuid;

    @ApiModelProperty(value = "name")
    private String name;

    @ApiModelProperty(value = "品牌库对应的实体GUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的实体，则该字段为原实体GUID")
    private String parentGuid;
}
