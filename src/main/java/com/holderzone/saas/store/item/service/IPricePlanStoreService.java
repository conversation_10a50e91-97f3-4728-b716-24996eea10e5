package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.item.req.PricePlanReqDTO;
import com.holderzone.saas.store.dto.item.req.PricePlanStoreReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.item.entity.domain.PricePlanStoreDO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020/10/29 17:12
 */
public interface IPricePlanStoreService extends IService<PricePlanStoreDO> {

    /**
     * 门店控制列表
     *
     * @param reqDTO 品牌guid,价格方案Guid
     * @return List<PricePlanStoreRespDTO>
     */
    List<PricePlanStoreRespDTO> storeControlList(PricePlanStoreReqDTO reqDTO);

    /**
     * 绑定门店与方案关系
     *
     * @param reqDTO 品牌guid,价格方案Guid,门店GUid列表
     * @return boolean
     */
    Boolean bingPlanStore(PricePlanStoreReqDTO reqDTO);

    /**
     * 根据门店guid删除方案与门店绑定关系
     *
     * @param storeGuid 门店guid
     * @return boolean
     */
    Boolean deletePlanStoreByStoreGuid(String storeGuid);

    /**
     * 根据门店guid查询方案绑定门店
     *
     * @param storeGuid 门店guid
     * @return 方案与门店绑定关系返回对象列表
     */
    List<PricePlanBingStoreRespDTO> getPlanStoreByStoreGuid(String storeGuid);

    /***
     * 门店绑定菜谱方案校验
     * @param reqDTO 请求参数
     * @return 返回
     */
    List<PricePlanStoreCheckRespDTO> checkStorePricePlanRule(PricePlanReqDTO reqDTO);

    /***
     * 查询在生效时间内的价格方案
     * @param nowDateTime 当前时间
     * @param storeGuid   门店GUID
     * @return 返回
     */
    List<PricePlanNowDTO> findPlanNowStoreGuid(LocalDateTime nowDateTime, String storeGuid);

    /***
     * 查询在生效时间内的价格方案
     * @param nowDateTime 当前时间
     * @param storeGuidList   门店GUID
     * @return 返回
     */
    List<PricePlanNowDTO> findPlanNowStoreList(LocalDateTime nowDateTime, List<String> storeGuidList);

    /***
     * 查询在生效时间内和即将生效的价格方案
     * @param storeGuid   门店GUID
     * @return 返回
     */
    List<PricePlanNowDTO> findPlanMemberStoreGuid(String storeGuid);

    /**
     * 查询当前门店即将生效和已生效的菜谱方案
     *
     * @param storeGuid storeGuid
     * @return List<PricePlanNowDTO>
     */
    List<PricePlanNowDTO> findPlanStoreGuid(String storeGuid);

    /**
     * 通过门店guid查询所属菜谱方案
     *
     * @param storeGuid 门店guid
     * @return 所属菜谱方案，如果没有则返回空list
     */
    List<PricePlanRespDTO> queryBelongPlansByStoreGuid(String storeGuid);

    /**
     * 将原方案绑定门店修改为新方案绑定的门店
     *
     * @param planGuid       方案Guid
     * @param parentPlanGuid 原方案Guid
     */
    void updatePlanStore(String planGuid, String parentPlanGuid);
}
