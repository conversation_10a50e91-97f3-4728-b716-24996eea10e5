package com.holderzone.saas.store.item;

import com.holderzone.saas.store.item.util.SpringContextUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableFeignClients
@EnableScheduling
@MapperScan(basePackages = "com.holderzone.saas.store.item.mapper")
@EnableCaching
public class HolderSaasStoreItemApplication {
    public static void main(String[] args) {
        ConfigurableApplicationContext app=SpringApplication.run(HolderSaasStoreItemApplication.class, args);
        SpringContextUtils.getInstance().setCfgContext(app);
    }
}
