package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.item.constant.Constant;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.entity.domain.RTypeAttrDO;
import com.holderzone.saas.store.item.mapper.RTypeAttrMapper;
import com.holderzone.saas.store.item.service.IRTypeAttrService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.sdk.util.BatchIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.item.constant.Constant.FALSE;

/**
 * <p>
 * 分类与属性值关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Service
public class RTypeAttrServiceImpl extends ServiceImpl<RTypeAttrMapper, RTypeAttrDO> implements IRTypeAttrService {

    private final RTypeAttrMapper typeAttrMapper;
    private final DynamicHelper dynamicHelper;
    private final RedisTemplate redisTemplate;

    @Autowired
    public RTypeAttrServiceImpl(RTypeAttrMapper typeAttrMapper, DynamicHelper dynamicHelper, RedisTemplate redisTemplate) {
        this.typeAttrMapper = typeAttrMapper;
        this.dynamicHelper = dynamicHelper;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public List<String> listTypeGuidByAttr(String attrGuid) {
        return typeAttrMapper.selectList(
                new LambdaQueryWrapper<RTypeAttrDO>()
                        .eq(RTypeAttrDO::getAttrGuid, attrGuid)
                        .eq(RTypeAttrDO::getIsDelete, FALSE))
                .parallelStream()
                .map(RTypeAttrDO::getTypeGuid)
                .collect(Collectors.toList());
    }

    @Override
    public Integer removeByAttr(String attrGuid) {
        return typeAttrMapper.delete(new LambdaQueryWrapper<RTypeAttrDO>().eq(RTypeAttrDO::getAttrGuid, attrGuid));
    }

    @Override
    public Integer removeBind(@NotEmpty List<String> typeGuidList, @NotBlank String attrGuid) {
        if (ObjectUtils.isEmpty(typeGuidList) || ObjectUtils.isEmpty(attrGuid)) {
            return 0;
        }
        return typeAttrMapper.delete(new LambdaQueryWrapper<RTypeAttrDO>()
                .eq(RTypeAttrDO::getAttrGuid, attrGuid)
                .in(RTypeAttrDO::getTypeGuid, typeGuidList));
    }

    @Override
    public Boolean save(@NotEmpty List<String> typeGuidList, @NotBlank String attrGuid) {
        if (ObjectUtils.isEmpty(typeGuidList) || ObjectUtils.isEmpty(attrGuid)) {
            return Boolean.FALSE;
        }

        ArrayList<RTypeAttrDO> list = new ArrayList<>(typeGuidList.size());
        typeGuidList.forEach(typeGuid -> {
            RTypeAttrDO rTypeAttrDO = new RTypeAttrDO();
            rTypeAttrDO.setTypeGuid(typeGuid);
            rTypeAttrDO.setAttrGuid(attrGuid);
            try {
                rTypeAttrDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, GuidKeyConstant.HSI_R_TYPE_ATTR)));
            } catch (IOException e) {
                throw new BusinessException(Constant.CREATE_GUID_FAIL);
            }
            list.add(rTypeAttrDO);
        });

        return this.saveBatch(list, list.size());
    }

    @Override
    public void deleteByAttr(String attrGuid) {
        typeAttrMapper.delete(
                new LambdaQueryWrapper<RTypeAttrDO>()
                        .eq(RTypeAttrDO::getAttrGuid, attrGuid));
    }
}
