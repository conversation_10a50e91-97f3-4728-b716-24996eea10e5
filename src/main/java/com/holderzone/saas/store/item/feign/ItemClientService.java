package com.holderzone.saas.store.item.feign;

import com.holderzone.saas.store.dto.item.req.ItemReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "holder-saas-store-item", fallbackFactory = ItemClientService.ItemFallBack.class)
public interface ItemClientService {
    @PostMapping("/item/update_item")
    Integer updateItem(@RequestBody ItemReqDTO itemUpdateReqDTO);

    @Component
    @Slf4j
    class ItemFallBack implements FallbackFactory<ItemClientService> {
        @Override
        public ItemClientService create(Throwable cause) {
            return new ItemClientService() {
                @Override
                public Integer updateItem(ItemReqDTO itemUpdateReqDTO) {
                    log.error("更新商品数据异常：{}", cause.getMessage());
                    throw new RuntimeException(cause.getMessage());
                }
            };
        }
    }
}