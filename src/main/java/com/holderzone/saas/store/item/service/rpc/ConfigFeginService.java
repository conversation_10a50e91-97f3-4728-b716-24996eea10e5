package com.holderzone.saas.store.item.service.rpc;

import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqQueryDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemClientService
 * @date 2018/09/07 下午4:29
 * @description //TODO
 * @program holder-saas-store-item
 */
@Component
@FeignClient(name = "holder-saas-store-config", fallbackFactory = ConfigFeginService.ConfigFallBack.class)
public interface ConfigFeginService {

    /**
     * 保存、更新门店置满时间
     *
     * @param configReqDTO
     * @return 保存更新行数
     */
    @PostMapping("/config/save_config")
    Integer saveEstimateResetTime(@RequestBody ConfigReqDTO configReqDTO);

    /**
     * 查询门店估清置满时间
     *
     * @param configReqQueryDTO
     * @return 查询门店估清置满时间
     */
    @PostMapping("/config/get_config_by_code")
    ConfigRespDTO selectEstimateResetTime(@RequestBody ConfigReqQueryDTO configReqQueryDTO);


    @Component
    @Slf4j
    class ConfigFallBack implements FallbackFactory<ConfigFeginService> {

        @Override
        public ConfigFeginService create(Throwable throwable) {
            return new ConfigFeginService() {

                @Override
                public Integer saveEstimateResetTime(ConfigReqDTO configReqDTO) {
                    log.error("设置估清重置时间异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public ConfigRespDTO selectEstimateResetTime(ConfigReqQueryDTO configReqQueryDTO) {
                    log.error("查询门店估清置满时间异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }

}
