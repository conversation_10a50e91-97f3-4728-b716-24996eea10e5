package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.item.entity.domain.ItemTMenuDO;
import com.holderzone.saas.store.item.entity.query.ItemTemplatesQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 商品模板-菜单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
@Component
public interface ItemTMenuMapper extends BaseMapper<ItemTMenuDO> {

    /**
     * 根据模板guid查询菜单列表
     * @param request
     * @return
     */
    List<ItemTemplatesQuery> getItemTemplateMenus(@Param("dto") SingleDataDTO request);
}
