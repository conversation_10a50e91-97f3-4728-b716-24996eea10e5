package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.PlanItemUpdateDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.req.price.PlanPriceAllItemReqDTO;
import com.holderzone.saas.store.dto.item.req.price.PlanPriceAllItemSkuReqDTO;
import com.holderzone.saas.store.dto.item.req.price.PlanPriceEditReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.price.*;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.item.dto.SyncItemDTO;
import com.holderzone.saas.store.item.dto.SyncStoreAndItemDTO;
import com.holderzone.saas.store.item.entity.domain.PricePlanItemDO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

public interface IPricePlanItemService extends IService<PricePlanItemDO> {

    /**
     * 保存方案关联菜品（菜品导入）
     *
     * @param reqDTO PricePlanItemAddReqDTO
     * @return 结果
     */
    Boolean savePlanItem(PricePlanItemAddReqDTO reqDTO);

    /**
     * 查询方案关联菜品（导入后）
     *
     * @param reqDTO PricePlanItemPageReqDTO
     * @return 分页结果
     */
    Page<PricePlanItemPageRespDTO> itemList(PricePlanItemPageReqDTO reqDTO);

    /**
     * 移除方案关联菜品
     *
     * @param reqDTO PricePlanItemRemoveReqDTO
     * @return 结果
     */
    Boolean removeItems(PricePlanItemRemoveReqDTO reqDTO);

    /**
     * 更新方案关联菜品
     *
     * @param reqDTO PricePlanItemUpdateReqDTO
     * @return 结果
     */
    Boolean updateItems(PricePlanItemUpdateReqDTO reqDTO);

    /**
     * 查询方案关联门店及菜品信息（推送用）
     *
     * @param planGuid 方案guid
     * @return SyncStoreAndItemDTO
     */
    SyncStoreAndItemDTO getStoreAndItem(String planGuid);

    /**
     * 分页获取方案可导入菜品
     *
     * @param request PricePlanItemAddQueryReqDTO
     * @return Page<PricePlanItemAddQueryRespDTO>
     */
    Page<PricePlanItemAddQueryRespDTO> selectBrandSkuItemList(PricePlanItemAddQueryReqDTO request);

    /**
     * 查询价格方案规格商品价格
     *
     * @param itemGuid 商品guid
     * @param skuGuid  规格guid
     * @return 价格
     */
    SyncItemDTO getSyncItem(String itemGuid, String skuGuid);

    List<TypeWebRespDTO> queryTypeByPricePlanGuid(String pricePlanGuid);

    /**
     * 更新方案关联菜品
     *
     * @param updatePlanItemDTOList PricePlanItemUpdateReqDTO
     * @return 结果
     */
    Boolean updateItemList(List<PlanItemUpdateDTO> updatePlanItemDTOList);

    /***
     * 更新商品上下架情况
     * @param reqDTO 请求参数
     * @return 返回参数
     */
    Boolean batchOperatingItem(PricePlanItemReqDTO reqDTO);

    /**
     * 根据菜谱方案查询菜谱方案绑定菜品
     *
     * @param itemQueryReqDTO ItemQueryReqDTO
     * @return 商品列表
     */
    Page<ItemWebRespDTO> queryPlanItemsByPlan(ItemQueryReqDTO itemQueryReqDTO);

    /***
     * 校验分类是否可以删除
     * @param itemQueryReqDTO 类型
     * @return 返回参数
     */
    Boolean checkTypePricePlan(@RequestBody ItemQueryReqDTO itemQueryReqDTO);

    /**
     * 保存方案关联菜品（菜品导入）
     *
     * @param reqDTO      PricePlanItemAddReqDTO
     * @param saveTypeMap 商品分类
     * @param isCopy      是否复制菜谱方案菜品数据
     * @return 结果
     */
    List<PricePlanItemDO> saveUpdatePlanItem(PricePlanItemAddReqDTO reqDTO, Map<String, String> saveTypeMap, boolean isCopy);

    /***
     * 查询菜谱方案可倒入菜品
     * @param itemSingleDTO 请求参数
     * @return
     */
    List<TypeItemRespDTO> findPricePlanItemList(ItemSingleDTO itemSingleDTO);

    /**
     * 根据方案guid查询绑定门店信息
     *
     * @param itemSingleDTO 菜谱方案Guid
     * @return List<StoreDTO>
     */
    List<StoreDTO> queryPlanStoreListByPlan(ItemSingleDTO itemSingleDTO);

    /**
     * 修改绑定关系
     *
     * @param planGuid   菜谱方案Guid
     * @param parentGuid 原菜谱方案Guid
     */
    void updatePlanItem(String planGuid, String parentGuid);

    /**
     * 通过菜谱guid预览菜谱
     *
     * @param reqDTO 菜谱guid,浏览模式
     * @return 预览的菜谱方案信息
     */
    PreviewPlanRespDTO previewPlanByGuid(PreviewPlanReqDTO reqDTO);

    /**
     * 功能描述：查询所有为已启用、即将启用、暂不启用菜谱的所有商品
     * @date 2021/9/30
     * @param req 品牌guid,商品信息
     * @return List<PlanPriceAllTypeRespDTO>
     */
    List<PlanPriceAllTypeRespDTO> listAllPlanPriceItem(PlanPriceAllItemReqDTO req);


    /**
     * 功能描述：查询所有为已启用、即将启用、暂不启用菜谱的所有分类
     */
    List<TypeRespDTO> listAllPlanPriceItemType(PlanPriceAllItemReqDTO req);

    /**
     * 功能描述：根据菜品guid查询菜品规格和已经应用得菜品
     * @date 2021/9/30
     * @param itemGuid 商品guid
     * @return com.holderzone.saas.store.dto.item.resp.price.ItemSkuAndPlanPriceDTO
     */
    ItemSkuAndPlanPriceDTO listItemSkuAndPlanPrice(String itemGuid);

    /**
     * 功能描述：查询分类名称所存在的菜谱
     */
    List<PlanPriceEditDTO> listItemTypeAndPlanPrice(String typeName);

    /**
     * 功能描述：保存批量编辑得商品菜谱信息
     * @date 2021/9/30
     * @param req 请求参数
     */
    void saveBatchEditPlanPrice(PlanPriceEditReqDTO req);

    /**
     * 功能描述：查询所有为已启用、即将启用、暂不启用菜谱的所有商品规格（去重）
     * @date 2021/10/21
     * @param req 查询参数
     * @return com.holderzone.framework.util.Page<com.holderzone.saas.store.dto.item.resp.price.PlanPriceAllItemSkuRespDTO>
     */
    Page<PlanPriceAllItemSkuRespDTO> listAllPlanPriceItemSku(PlanPriceAllItemSkuReqDTO req);
}
