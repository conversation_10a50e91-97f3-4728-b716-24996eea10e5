package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.ItemGroupMealSaveReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemPkgSaveReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeSkuRespDTO;
import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.TypeDO;

import java.util.List;

/**
 * <p>
 * 商品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
public interface IItemPkgService {

    /**
     * 根据所属品牌或门店GUID获取能作为套餐子商品的商品规格列表
     * @param itemSingleDTO
     * @return
     */
    List<TypeSkuRespDTO> selectSkuListForPkg(ItemSingleDTO itemSingleDTO);

    List<TypeSkuRespDTO> getTypeSkuRespDTOS(List<TypeDO> typeDOList, LambdaQueryWrapper<ItemDO> itemDOLambdaQueryWrapper);

    List<TypeSkuRespDTO> selectTypeSkuListForPkg(ItemSingleDTO itemSingleDTO);

    /**
     * 套餐
     * @param itemPkgSaveReqDTO
     * @return
     */
    List<String> saveItemPkg(ItemPkgSaveReqDTO itemPkgSaveReqDTO);

    /**
     * 修改套餐
     * @param itemPkgSaveReqDTO
     * @return
     */
    List<String> updateItemPkg(ItemPkgSaveReqDTO itemPkgSaveReqDTO);

    /**
     * 门店新增or更新团餐
     * @param request
     * @return
     */
    Integer saveOrUpdateGroupMealPkg(ItemGroupMealSaveReqDTO request);

    void removeAllRelationStore(ItemPkgSaveReqDTO itemPkgSaveReqDTO, List<String> insertSkuGuidList);

    /**
     * 根据商品guid获取商品及套餐信息
     *
     * @param itemStringListDTO 商品guid
     * @return 商品及套餐信息
     */
    List<ItemInfoRespDTO> listPkgItemInfo(ItemStringListDTO itemStringListDTO);

    /**
     * 外卖批量绑定查询品牌库商品
     */
    List<TypeSkuRespDTO> selectList(ItemSingleDTO itemSingleDTO);
}
