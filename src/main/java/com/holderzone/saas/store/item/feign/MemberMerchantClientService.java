package com.holderzone.saas.store.item.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.merchant.dto.card.ResponseIntegralConversionItem;
import com.holderzone.holder.saas.member.merchant.dto.card.ResponseIntegralConversionItemSku;
import com.holderzone.saas.store.dto.member.request.ConversionIntegralQuery;
import com.holderzone.saas.store.item.util.MemberResult;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberMerchantClientService
 * @date 2021/6/25 17:14
 * @description 会员调用服务接口
 * @program holder-saas-store-item
 */
@Component
@FeignClient(name = "holder-saas-member-merchant", fallbackFactory = MemberMerchantClientService.MemberFallback.class)
public interface MemberMerchantClientService {

    /**
     * 根据门店获取会员卡积分兑换规则
     * 门店系统
     *
     * @return 会员卡积分兑换规则
     */
    @ApiOperation("根据门店获取会员卡积分兑换规则，门店系统")
    @GetMapping("/hsa-card-integral/query_conversion_rule_by_store")
    MemberResult<List<ResponseIntegralConversionItem>> queryConversionRuleByStore(@RequestParam("storeGuid") String storeGuid);

    /**
     * 根据商品Guid获取会员卡积分兑换商品
     * 门店系统
     *
     * @param query 商品guid列表
     * @return 会员卡积分兑换商品
     */
    @ApiOperation("根据商品Guid获取会员卡积分兑换商品")
    @PostMapping("/hsa-card-integral/query_conversion_item_by_item_guid")
    MemberResult<List<ResponseIntegralConversionItem>> queryConversionItemByItemGuid(@RequestBody ConversionIntegralQuery query);

    /**
     * 根据skuGuid获取会员卡积分兑换商品规格
     * 门店系统
     *
     * @param query 商品skuGuid列表
     * @return 会员卡积分兑换商品sku
     */
    @ApiOperation("根据skuGuid获取会员卡积分兑换商品规格")
    @PostMapping("/hsa-card-integral/query_conversion_item_sku_by_sku_guid")
    MemberResult<List<ResponseIntegralConversionItemSku>> queryConversionItemSkuBySkuGuid(@RequestBody ConversionIntegralQuery query);

    @Slf4j
    @Component
    class MemberFallback implements FallbackFactory<MemberMerchantClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public MemberMerchantClientService create(Throwable throwable) {
            return new MemberMerchantClientService() {

                @Override
                public MemberResult<List<ResponseIntegralConversionItem>> queryConversionRuleByStore(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryConversionRule", storeGuid, ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public MemberResult<List<ResponseIntegralConversionItem>> queryConversionItemByItemGuid(ConversionIntegralQuery query) {
                    log.error(HYSTRIX_PATTERN, "queryConversionItemByItemGuid", JacksonUtils.writeValueAsString(query), ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public MemberResult<List<ResponseIntegralConversionItemSku>> queryConversionItemSkuBySkuGuid(ConversionIntegralQuery query) {
                    log.error(HYSTRIX_PATTERN, "queryConversionItemSkuBySkuGuid", JacksonUtils.writeValueAsString(query), ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

            };
        }
    }
}
