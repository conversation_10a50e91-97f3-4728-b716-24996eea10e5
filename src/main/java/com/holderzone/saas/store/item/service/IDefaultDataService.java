package com.holderzone.saas.store.item.service;

import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className IDefaultDataService
 * @date 2019/03/13 上午10:40
 * @description 默认数据服务
 * @program holder-saas-store-business
 */
public interface IDefaultDataService {


    /**
     * 添加默认属性、属性组
     * @param itemSingleDTO  data: 品牌guid或者门店guid  model: 0：门店初始化  1：品牌初始化
     */
    void addAttr(ItemSingleDTO itemSingleDTO);

    /**
     * 添加默认属性、属性组
     * @param itemSingleDTO  data: 品牌guid或者门店guid  model: 0：门店初始化  1：品牌初始化
     */
    void addInitTypeAndItem(ItemSingleDTO itemSingleDTO);
}
