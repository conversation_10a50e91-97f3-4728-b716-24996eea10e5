package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.member.merchant.dto.org.RequestProductInfo;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.req.price.PlanPriceAddReqDTO;
import com.holderzone.saas.store.dto.item.req.price.PlanPriceAvailableReqDTO;
import com.holderzone.saas.store.dto.item.req.price.PlanPriceEditReqDTO;
import com.holderzone.saas.store.dto.item.req.price.PlanPriceSoldOutReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTypeRespDTO;
import com.holderzone.saas.store.dto.item.resp.PreOrderValidateRespDTO;
import com.holderzone.saas.store.dto.item.resp.PricePlanRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeSkuPricePlanRespDTO;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditDTO;
import com.holderzone.saas.store.dto.weixin.deal.PricePlanChangeRequestDTO;
import com.holderzone.saas.store.dto.weixin.deal.PricePlanChangeResponseDTO;
import com.holderzone.saas.store.item.entity.domain.PricePlanDO;

import java.time.LocalTime;
import java.util.List;

public interface IPricePlanService extends IService<PricePlanDO> {

    /**
     * 会员优惠券活动查询菜谱下的菜品
     *
     * @param request planGuidList
     * @return List<BaseTypeSkuRespDTO>
     */
    TypeSkuPricePlanRespDTO memberPricePlanItemByGuidList(RequestProductInfo request);

    /**
     * 会员优惠券活动查询菜谱下的菜品
     *
     * @param itemSingleDTO itemSingleDTO
     * @return List<BaseTypeSkuRespDTO>
     */
    TypeSkuPricePlanRespDTO memberPricePlanItem(ItemSingleDTO itemSingleDTO);

    /**
     * 查询扫码点餐菜谱变动情况
     *
     * @param pricePlanChangeRequestDTO pricePlanChangeRequestDTO
     * @return PricePlanChangeResponseDTO
     */
    PricePlanChangeResponseDTO pricePlanChangeInfo(PricePlanChangeRequestDTO pricePlanChangeRequestDTO);

    /**
     * 商品销售模式变更
     *
     * @param brandGuid  brandGuid
     * @param salesModel salesModel
     */
    void changeSaleModel(String brandGuid, Integer salesModel);

    /**
     * 测试推送
     *
     * @param pricePlanGuid guid
     * @return Integer
     */
    Integer testPush(String pricePlanGuid);

    /**
     * 价格方案保存
     *
     * @param reqDTO PricePlanReqDTO
     * @return 方案guid
     */
    String savePlan(PricePlanReqDTO reqDTO);

    /**
     * 解绑菜谱冲突的菜谱方案关联
     *
     * @param storeGuids       门店Guid集合
     * @param startCompareTime 开始时间
     * @param endCompareTime   结束时间
     * @param sellTimeType     售卖类型 0 默认（全时段） 1特殊时段
     * @param filterPlanGuid   当前菜谱方案GUID
     */
    void unBindStoreAndPlan(List<String> storeGuids, LocalTime startCompareTime, LocalTime endCompareTime,
                            Integer sellTimeType, String filterPlanGuid);

    /**
     * 价格方案列表查询
     *
     * @param reqDTO PricePlanPageReqDTO
     * @return Page<PricePlanPageRespDTO>
     */
    Page<PricePlanRespDTO> planList(PricePlanPageReqDTO reqDTO);

    /**
     * 一体机预点餐验证
     *
     * @param preOrderValidateReq preOrderValidateReq
     * @return PricePlanRespDTO
     */
    PreOrderValidateRespDTO preOrderVerification(PreOrderValidateReq preOrderValidateReq);

    /**
     * 价格方案编辑查询
     *
     * @param planGuid 方案guid
     * @return PricePlanRespDTO
     */
    PricePlanRespDTO getPlan(String planGuid);

    /**
     * 价格方案推送 日志记录
     *
     * @param reqDTO PricePlanPushReqDTO
     * @return 成功
     */
    Boolean pushPlanRecord(PricePlanPushReqDTO reqDTO);

    /**
     * 总策略：商户后台创建特殊时段方案，触发开始点通知，
     * 开始点通知执行时，
     * 触发结束点通知，结束点通知执行时，再次出发开始点通知
     * <p>
     * 特殊时段价格方案，开始时间点到后，触发通知
     *
     * @param plan plan
     */
    void planStartNoti(PricePlanDO plan, boolean first);

    /**
     * 总策略：商户后台创建特殊时段方案，触发开始点通知，
     * 开始点通知执行时，
     * 触发结束点通知，结束点通知执行时，再次出发开始点通知
     * <p>
     * 特殊时段价格方案，结束时间点到后，触发通知
     *
     * @param plan plan
     */
    void planEndNoti(PricePlanDO plan);

    /**
     * 价格方案删除
     *
     * @param planGuid 方案guid
     * @return 结果
     */
    Boolean deletePlan(String planGuid);

    /***
     * 查询价格方案草稿信息
     * @param userGuid 用户Guid
     * @param brandGuid 品牌Guid
     * @return 参数
     */
    PricePlanReqDTO getPricePlanCacheData(String userGuid, String brandGuid);

    /***
     * 删除菜谱方案草稿
     * @param reqDTO 请求参数
     * @return true 、false
     */
    Boolean deletePlanDraft(PricePlanDraftReqDTO reqDTO);

    /**
     * 通过门店guid查询所有菜谱方案
     *
     * @param storeGuid 门店guid
     * @return 所有菜谱方案
     */
    List<PricePlanRespDTO> queryPlansByStoreGuid(String storeGuid);

    /**
     * 永久停用方案
     *
     * @param planGuid 方案id
     * @return Boolean
     */
    Boolean permanentlyDeactivate(String planGuid);

    /**
     * 编辑已启用菜谱预计生效 菜谱处理
     *
     * @param pricePlanDO 菜谱方案
     */
    PricePlanDO usingPricePlanEditHandle(PricePlanDO pricePlanDO);

    /**
     * 菜谱复制
     *
     * @param planGuid 菜谱guid
     * @return 操作结果
     */
    Boolean copyPlan(String planGuid);

    /**
     * 功能描述：批量更新菜谱信息
     *
     * @param req 请求参数
     * @date 2021/10/8
     */
    void saveBatchEditPlanPrice(PlanPriceEditReqDTO req);

    /**
     * 功能描述：批量更新菜谱分类信息
     */
    void saveBatchEditPlanPriceType(PlanPriceEditReqDTO req);

    /**
     * 功能描述：批量编辑新增商品
     *
     * @param req 批量编辑新增商品传递得数据
     * @date 2021/10/9
     */
    void saveBatchAddPlanPrice(PlanPriceAddReqDTO req);

    /**
     * 功能描述：查询可用的价格方案
     *
     * @param req 请求参数
     * @return java.util.List<com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditDTO>
     * @date 2021/10/11
     */
    List<PlanPriceEditDTO> listAvailablePlanPrice(PlanPriceAvailableReqDTO req);

    /**
     * 功能描述：查询菜品的分类信息
     *
     * @param planGuid 菜谱guid
     * @return java.util.List<com.holderzone.saas.store.dto.item.resp.ItemTypeRespDTO>
     * @date 2021/10/15
     */
    List<ItemTypeRespDTO> listAvailablePlanItemType(String planGuid);

    /**
     * 批量下架保存商品菜谱信息
     *
     * @param request 批量下架保存入参实体
     */
    Boolean saveBatchSoldOutPlanItem(PlanPriceSoldOutReqDTO request);

    /**
     * 方案延时补偿
     *
     * @param enterpriseGuidList 需要执行的企业guid
     */
    void delayedCompensate(List<String> enterpriseGuidList);
}
