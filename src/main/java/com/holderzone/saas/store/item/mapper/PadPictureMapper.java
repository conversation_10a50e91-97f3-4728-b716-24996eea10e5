package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.saas.store.dto.item.req.PadPictureDTO;
import com.holderzone.saas.store.dto.item.resp.PadPictureRespDTO;
import com.holderzone.saas.store.item.entity.domain.PadPictureDO;
import com.holderzone.saas.store.item.helper.PageAdapter;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 菜品下关联的pad点餐图片 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-22
 */
public interface PadPictureMapper extends BaseMapper<PadPictureDO> {

    /**
     * 普通模式查询pad点餐图片
     *
     * @param page          Page
     * @param padPictureDTO 属性值保存DTO
     * @return 查询结果
     */
    IPage<PadPictureRespDTO> queryPadPicture(PageAdapter page, @Param("dto") PadPictureDTO padPictureDTO);

    /**
     * 菜谱模式查询pad点餐图片
     *
     * @param page          Page
     * @param padPictureDTO 属性值保存DTO
     * @return 查询结果
     */
    IPage<PadPictureRespDTO> queryPricePlanPadPicture(PageAdapter page, @Param("dto") PadPictureDTO padPictureDTO);
}
