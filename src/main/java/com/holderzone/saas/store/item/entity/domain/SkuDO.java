package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;


/**
 * <p>
 * 商品规格表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsi_sku")
public class SkuDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @TableLogic
    private Integer isDelete;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 品牌GUID
     */
    private String brandGuid;

    /**
     * 关联的门店GUID
     */
    private String storeGuid;

    /**
     * 商品GUID
     */
    private String itemGuid;
    /**
     * 价格方案GUID
     */
    private String pricePlanGuid;
    /**
     * 类型GUID 冗余小程序字段
     */
    private String typeGuid;
    /**
     * upc商品条码
     */
    private String upc;

    /**
     * 当日库存
     */
    private BigDecimal stock;

    /**
     * 总库存
     */
    private BigDecimal totalStock;


    /**
     * 规格名称(固定规格名称为空字符串"")
     */
    private String name;

    /**
     * 编号
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String code;

    /**
     * 售卖价格
     */
    @ApiModelProperty("售卖价格")
    private BigDecimal salePrice;

    @ApiModelProperty("外卖价格")
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal takeawayPrice;

    @ApiModelProperty("外卖会员价格")
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal takeawayMemberPrice;

    @ApiModelProperty("成本价格")
    private BigDecimal costPrice;

    /**
     * 会员价格
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal memberPrice;

    /**
     * 虚拟价格（冗余小程序端字段）
     */
    private BigDecimal virtualPrice;

    /**
     * 商品规格单位
     */
    private String unit;

    /**
     * 称重单位对应的编号
     */
    private Integer unitCode;

    /**
     * 起卖数(非称重即为整数，称重即为小数)
     */
    private BigDecimal minOrderNum;

    /**
     * 是否参与会员折扣（0：否，1：是）
     */
    private Integer isMemberDiscount;

    /**
     * 是否加入整单折扣(0：否，1：是)
     */
    private Integer isWholeDiscount;

    /**
     * 是否开启库存（0：否 ， 1：是）
     */
    private Integer isOpenStock;

    /**
     * 是否上架(0：否，1：是)
     */
    private Integer isRack;

    /**
     * 是否上架一体机（0：否，1：是）
     */
    private Integer isJoinAio;

    /**
     * 是否上架POS机（0：否，1：是）
     */
    private Integer isJoinPos;

    /**
     * 是否上架Pad（0：否，1：是）
     */
    private Integer isJoinPad;

    /**
     * 是否参与自助点餐机（0：否，1：是）
     */
    private Integer isJoinBuffet;

    /**
     * 是否参与微信点餐（0：否，1：是）
     */
    private Integer isJoinWeChat;

    /**
     * 是否参与美团外卖（0：否，1：是）
     */
    private Integer isJoinMt;

    /**
     * 是否参与饿了么外卖（0：否，1：是）
     */
    private Integer isJoinElm;

    /**
     * 是否参与小程序商城（0：否，1：是） （冗余小程序端字段）
     */
    private Integer isJoinMiniAppMall;
    /**
     * 是否参与小程序外卖（0：否，1：是） （冗余小程序端字段）
     */
    private Integer isJoinMiniAppTakeaway;
    /**
     * 是否支持堂食（0：否，1：是） （冗余小程序端字段）
     */
    private Integer isJoinStore;

    /**
     * 美团sku
     */
    private String mtSku;

    /**
     * 饿了么sku
     */
    private String elmSku;

    /**
     * 品牌库对应的SKUGUID：如果是自己创建的内容，则此字段为skuGuid，如果是被推送过来的商品，则该字段为原skuGUID。
     */
    private String parentGuid;

    /**
     * 规格来源（0：门店自己创建的规格，1：品牌自己创建的规格,2:被推送过来的规格）
     */
    private Integer skuFrom;


    /**
     * 外卖打包费（冗余小程序端字段）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal takeawayPackageFee;

    /**
     * 商城打包费（冗余小程序端字段）
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal mallPackageFee;

    /**
     * 是否自动置满库存（冗余小程序端字段）
     */
    private Boolean isFull;

    /**
     * 置满时间（冗余小程序端字段）
     */
    private Integer fullTime;


    /**
     * 小程序端折扣（冗余小程序端字段）
     */
    private Double discount;

    /**
     * 月销售量（冗余小程序端字段）
     */
    private Integer monthlySale;

    /**
     * sku（冗余小程序端字段）
     */
    private String sku;
    /**
     * 是否启用
     */
    private Boolean isEnable;

    /**
     * 是否上架Kds（0：否，1：是）
     */
    private Integer isJoinKds;

    /**
     * 堂食核算价
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal accountingPrice;

    /**
     * 外卖核算价
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal takeawayAccountingPrice;

    /**
     * 菜谱商品名称
     */
    @TableField(exist = false)
    private String planItemName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SkuDO that = (SkuDO) o;
        return Objects.equals(guid, that.guid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(guid);
    }
}
