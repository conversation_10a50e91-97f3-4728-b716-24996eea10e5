package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.item.entity.domain.EstimateOpLogDO;

import java.util.List;


/**
 * @description 商品估清操作记录 服务接口类
 */
public interface IEstimateOpLogService extends IService<EstimateOpLogDO> {

    /**
     * 保存商品估清操作记录
     */
    void saveOpLog(List<EstimateOpLogDO> opLogList, String enterpriseGuid);

}
