package com.holderzone.saas.store.item.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Sets;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqQueryDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemSkuStockDTO;
import com.holderzone.saas.store.dto.item.req.EstimateBatchReqDTO;
import com.holderzone.saas.store.dto.item.req.EstimateForManualReqDTO;
import com.holderzone.saas.store.dto.item.req.EstimateMerchantReqDTO;
import com.holderzone.saas.store.dto.item.req.EstimateReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateResultRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateSkuRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.common.ConfigEnum;
import com.holderzone.saas.store.item.builder.EstimateBizBuilder;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.entity.bo.EstimateBO;
import com.holderzone.saas.store.item.entity.domain.EstimateDO;
import com.holderzone.saas.store.item.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.item.entity.enums.SalesModelEnum;
import com.holderzone.saas.store.item.entity.query.SetMealEstimateQuery;
import com.holderzone.saas.store.item.helper.EventPushHelper;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.helper.PageAdapter;
import com.holderzone.saas.store.item.mapper.EstimateMapper;
import com.holderzone.saas.store.item.mapper.SkuMapper;
import com.holderzone.saas.store.item.mapper.SubgroupMapper;
import com.holderzone.saas.store.item.repository.EstimateRepository;
import com.holderzone.saas.store.item.service.*;
import com.holderzone.saas.store.item.service.rpc.ConfigFeginService;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import com.holderzone.saas.store.item.util.*;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className IEstimateServiceImpl
 * @date 2019/05/07 14:27
 * @description //TODO 商品sku估清表 服务实现类
 * @program holder-saas-store-item
 */
@Service
@Slf4j
public class IEstimateServiceImpl extends ServiceImpl<EstimateMapper, EstimateDO> implements IEstimateService {

    private static final int IS_THE_LIMIT = 2;
    public static final String THE_STORE_GUID_IS_EMPTY = "门店guid为空";
    private final EstimateMapper estimateMapper;

    private final DynamicHelper dynamicHelper;

    private final ItemHelper itemHelper;

    private final ISkuService skuService;

    private final IItemService itemService;

    private final ConfigFeginService configFeginService;

    private final OrganizationService organizationService;

    private final IEstimateOpLogService estimateOpLogService;

    @Value("${job_executor.corePoolSize}")
    private Integer corePoolSize;

    @Value("${job_executor.maximumPoolSize}")
    private Integer maximumPoolSize;

    @Value("${job_executor.keepAliveTime}")
    private Integer keepAliveTime;

    @Value("${job_executor.maxQueueSize}")
    private Integer maxQueueSize;

    private final PlatformTransactionManager txManager;
    private final RedisTemplate redisTemplate;
    private final SkuMapper skuMapper;
    private final EventPushHelper eventPushHelper;

    private static final String REDIS_ESTIMATE_ITEM = "estimateItem";

    @Autowired
    private SubgroupMapper subgroupMapper;

    @Autowired
    private IPricePlanStoreService pricePlanStoreService;

    @Autowired
    private EstimateRepository estimateRepository;

    @Autowired
    public IEstimateServiceImpl(
            SkuMapper skuMapper,
            EstimateMapper estimateMapper, DynamicHelper dynamicHelper, @Lazy ItemHelper itemHelper,
            ISkuService skuService, IItemService itemService, ConfigFeginService configFeginService,
            PlatformTransactionManager txManager, RedisTemplate redisTemplate, OrganizationService organizationService,
            EventPushHelper eventPushHelper, IEstimateOpLogService estimateOpLogService) {
        this.estimateMapper = estimateMapper;
        this.dynamicHelper = dynamicHelper;
        this.itemHelper = itemHelper;
        this.skuService = skuService;
        this.itemService = itemService;
        this.configFeginService = configFeginService;
        this.txManager = txManager;
        this.redisTemplate = redisTemplate;
        this.skuMapper = skuMapper;
        this.organizationService = organizationService;
        this.eventPushHelper = eventPushHelper;
        this.estimateOpLogService = estimateOpLogService;
    }

    /**
     * 估清置满线程池
     */
    ExecutorService executor;

    ExecutorService estimateCancelExecutor;

    @Override
    @Transactional
    public Boolean saveOrUpdate(EstimateReqDTO request) {
        //判断是否开启了自动自满
        if (Objects.nonNull(request.getIsItReset()) && Objects.equals(request.getIsItReset(), 2)) {
            //查询是否配置了自动置满时间
            ConfigReqQueryDTO configReqQueryDTO = new ConfigReqQueryDTO();
            configReqQueryDTO.setDicCode(ConfigEnum.ESTIMATE_RECOVERY_TIME.getCode());
            configReqQueryDTO.setStoreGuid(request.getStoreGuid());
            ConfigRespDTO configRespDTO = configFeginService.selectEstimateResetTime(configReqQueryDTO);
            //初始化默认估清置满时间
            if (Objects.isNull(configRespDTO)) {
                configFeginService.saveEstimateResetTime(ConfigReqDTO.init(request.getStoreGuid()));
            }
        }
        EstimateDO estimateDO = MapStructUtils.INSTANCE.estimateReqDTO2estimateDO(request);
        //limitQuantity未传设为0
        estimateDO.setLimitQuantity(Optional.ofNullable(request.getLimitQuantity()).orElse(BigDecimal.ZERO));
        String skuGuid = request.getSkuGuid();
        if (!RedissonLockUtil.tryLock(skuGuid, 1, 3)) {
            throw new BusinessException("系统繁忙请稍候重试");
        }
        try {
            EstimateDO dbEstimate = getOne(
                    new LambdaQueryWrapper<EstimateDO>()
                            .eq(EstimateDO::getSkuGuid, skuGuid)
                            .orderByDesc(EstimateDO::getId), false);
            if (Objects.isNull(dbEstimate)) {
                String guid = String.valueOf(BatchIdGenerator.getGuid(redisTemplate, GuidKeyConstant.HSI_ESTIMATE));
                estimateDO.setGuid(guid);
                estimateDO.setVersion(1);
                estimateDO.setUniqueResetFlag(1);
                estimateDO.setResidueQuantity(request.getLimitQuantity());
                estimateDO.setLimitQuantity(BigDecimal.valueOf(1000));
            } else {
                estimateDO.setIsDelete(0);
                estimateDO.setGmtModified(LocalDateTime.now());
                //修改了限制数量
                estimateDO.setGuid(dbEstimate.getGuid());
                if (estimateDO.getLimitQuantity() != null &&
                        dbEstimate.getLimitQuantity().compareTo(estimateDO.getLimitQuantity()) != 0
                        && estimateDO.getLimitQuantity().compareTo(BigDecimal.ZERO) != 0) {
                    estimateDO.setUniqueResetFlag(1);
                }
            }
            //是否限量
            if (estimateDO.getIsTheLimit() != null && estimateDO.getIsTheLimit() == IS_THE_LIMIT) {
                //限量,并且限量值存在，判断限量值是否正确
                if (estimateDO.getLimitQuantity() != null && estimateDO.getLimitQuantity().compareTo(BigDecimal.ZERO) < 0) {
                    throw new BusinessException("开启库存后,库存数量不能小于0");
                }
            }

            //开启库存，校验库存的值是否正确
            if (estimateDO.getIsTheLimit() != null && estimateDO.getIsTheLimit() == 2) {
                if (estimateDO.getLimitQuantity() != null) {
                    if (estimateDO.getLimitQuantity().compareTo(BigDecimal.ZERO) < 0) {
                        throw new BusinessException("库存数量不能小于0");
                    }
                    if (estimateDO.getLimitQuantity().compareTo(BigDecimal.ZERO) > 0) {
                        estimateDO.setUniqueResetFlag(1);
                    }
                } else if (dbEstimate != null && dbEstimate.getLimitQuantity() == null) {
                    estimateDO.setLimitQuantity(BigDecimal.valueOf(1000));
                }
            }
            if (estimateDO.getLimitQuantity() != null && estimateDO.getLimitQuantity().compareTo(BigDecimal.ZERO) < 0) {
                estimateDO.setIsTheLimit(1);
            }


            log.info("insert db object={}", JacksonUtils.writeValueAsString(estimateDO));
            boolean ret = saveOrUpdate(estimateDO);
            if (ret && estimateDO.getIsTheLimit() != null) {
                int isOpenStock = estimateDO.getIsTheLimit() == 2 ? 1 : 0;
                skuMapper.updateStockStatus(estimateDO.getSkuGuid(), estimateDO.getLimitQuantity(), isOpenStock);
            }
            return ret;
        } catch (IOException e) {
            throw new BusinessException("BatchIdGenerator生成商品guid失败");
        } finally {
            RedissonLockUtil.unlock(request.getSkuGuid());
            // 删除估清redis
            removeEstimateRedis(request.getStoreGuid());
        }
    }

    @Override
    @Transactional
    public Boolean batchSave(EstimateBatchReqDTO request) {
        List<EstimateReqDTO> requestList = request.getSkuGuidList().stream().map(skuGuid -> {
            EstimateReqDTO estimateReqDTO = new EstimateReqDTO();
            BeanUtils.copyProperties(request, estimateReqDTO);
            estimateReqDTO.setSkuGuid(skuGuid);
            return estimateReqDTO;
        }).collect(Collectors.toList());
        for (EstimateReqDTO item : requestList) {
            boolean ret = this.saveOrUpdate(item);
            if (!ret) {
                throw new BusinessException("批量保存失败");
            }
        }
        // 删除估清redis
        removeEstimateRedis(request.getStoreGuid());
        return true;
    }

    @Override
    public EstimateRespDTO getEstimateReqDTO(EstimateReqDTO request) {
        EstimateDO estimateDO = getOne(new LambdaQueryWrapper<EstimateDO>()
                .eq(EstimateDO::getSkuGuid, request.getSkuGuid())
                .eq(EstimateDO::getStoreGuid, request.getStoreGuid()));
        return MapStructUtils.INSTANCE.estimateDO2EstimateReqDTO(estimateDO);
    }

    @Override
    public Page<EstimateMerchantConfigRespDTO> getItemEstimates(EstimateMerchantReqDTO request) {
        IPage<EstimateMerchantConfigRespDTO> page = estimateMapper.getItemEstimates(new PageAdapter<>(request), request);
        log.info("result data= {}", JacksonUtils.writeValueAsString(page));
        return new PageAdapter<>(page, page.getRecords());
    }

    @Override
    public Page<EstimateItemResidueMemchantRespDTO> getEstimateItemResidue(EstimateMerchantReqDTO request) {
        IPage<EstimateItemResidueMemchantRespDTO> page = estimateMapper.getEstimateItemResidue(new PageAdapter<>(request), request);
        log.info("result data= {}", JacksonUtils.writeValueAsString(page));
        return new PageAdapter<>(page, page.getRecords());
    }


    @Override
    public EstimateResultRespDTO verifyDineInItemEstimate(List<DineInItemDTO> request) {
        List<DineInItemDTO> notPackageItem = request.stream()
                .filter(i -> ItemTypeEnum.PACKAGE.getTypeCode() != i.getItemType())
                .collect(Collectors.toList());
        List<ItemSkuStockDTO> stockList = getStockList(notPackageItem);
        // 套餐子项-扣减库存
        reducePackageStock(request);
        if (CollectionUtils.isEmpty(stockList)) {
            // 删除估清redis
            removeEstimateRedis(UserContextUtils.getStoreGuid());
            return EstimateResultRespDTO.builder().success(true).build();
        }
        // 校验估清并扣减库存
        EstimateResultRespDTO estimateResultRespDTO = descStock(stockList);
        // 如果校验不通过，则将不通过的商品拼接返回给前端
        if (!estimateResultRespDTO.getSuccess()) {
            // 校验不通过的商品名称集合，key为skuGuid，value为展示名称，可能为单品名称也可能为单品所在的套餐名称
            Map<String, Set<String>> errorProductMap = Maps.newHashMap();
            stockList.forEach(e -> {
                Set<String> productNames = errorProductMap.get(e.getSkuGuid());
                if (CollectionUtils.isEmpty(productNames)) {
                    productNames = Sets.newHashSet();
                }
                productNames.add(e.getProductName());
                errorProductMap.put(e.getSkuGuid(), productNames);
            });

            // 构建返回提示信息
            StringBuilder errorMsg = new StringBuilder();
            estimateResultRespDTO.getSkuGuids().forEach(e -> {
                Set<String> productNames = errorProductMap.get(e);
                if (!CollectionUtils.isEmpty(productNames)) {
                    productNames.forEach(productName -> {
                        errorMsg.append(productName).append("、");
                    });
                }
            });
            String errorMsgStr = errorMsg.toString();
            if (errorMsg.length() > 0) {
                errorMsgStr = errorMsg.substring(0, errorMsg.length() - 1);
            }
            estimateResultRespDTO.setErrorMsg(errorMsgStr + "库存不足不可下单");
        }
        return estimateResultRespDTO;
    }

    @NotNull
    private List<ItemSkuStockDTO> getStockList(List<DineInItemDTO> request) {
        return request.stream()
                .map(item -> {
                    String itemName = item.getItemName();
                    if (!StringUtils.isEmpty(item.getSkuName())) {
                        itemName = itemName + "-" + item.getSkuName();
                    }
                    BigDecimal stock = item.getCurrentCount();
                    if (!ObjectUtils.isEmpty(item.getFreeCount())) {
                        stock = stock.add(item.getFreeCount());
                    }
                    return ItemSkuStockDTO.builder().skuGuid(item.getSkuGuid())
                            .stock(stock)
                            .productName(itemName)
                            .build();
                })
                .collect(Collectors.toList());
    }

    private void reducePackageStock(List<DineInItemDTO> itemDTOList) {
        List<DineInItemDTO> packageItem = itemDTOList.stream()
                .filter(i -> ItemTypeEnum.PACKAGE.getTypeCode() == i.getItemType())
                .collect(Collectors.toList());
        List<ItemSkuStockDTO> packageStockList = getStockList(packageItem);
        // 套餐子项
        List<ItemSkuStockDTO> subItemSkuStockList = getSubItemSkuStockList(itemDTOList);
        log.info("[reduceSubStock]packageStockList={},subItemSkuStockList={}",
                JacksonUtils.writeValueAsString(packageStockList), JacksonUtils.writeValueAsString(subItemSkuStockList));
        packageStockList.addAll(subItemSkuStockList);
        if (CollectionUtils.isEmpty(packageStockList)) {
            return;
        }
        Map<String, BigDecimal> skuGuidStockMap = packageStockList.stream()
                .collect(Collectors.groupingBy(ItemSkuStockDTO::getSkuGuid, CollectorsUtil.summingBigDecimal(ItemSkuStockDTO::getStock)));
        List<EstimateDO> estimateDOList = getVerifySkuItemHandler(skuGuidStockMap);
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        definition.setReadOnly(false);
        TransactionStatus transactionStatus = txManager.getTransaction(definition);
        // 获取到需要扣减库存的商品个数
        Integer count = (int) estimateDOList
                .stream()
                .filter(s -> s.getIsTheLimit().equals(2))
                .count();
        Integer updateLine = 0;
        List<String> skuGuids = new ArrayList<>();
        for (EstimateDO estimateDO : estimateDOList) {
            BigDecimal tradeNumber = skuGuidStockMap.get(estimateDO.getSkuGuid());
            log.info("[reduceSubStock]EstimateDO={},tradeNumber={}", JacksonUtils.writeValueAsString(estimateDO), tradeNumber);
            if (ObjectUtils.isEmpty(tradeNumber) || BigDecimalUtil.equelZero(estimateDO.getResidueQuantity())) {
                continue;
            }
            if (estimateDO.getIsTheLimit() == 2) {
                log.info("[reduceSubStock]校验库存是否充足,进行扣减isSoldOut={}，isTheLimit={}",
                        estimateDO.getIsSoldOut(), estimateDO.getIsTheLimit());
                // 不管库存是否充足，扣减为0为止
                updateResidueQuantity(estimateDO, tradeNumber);
                skuMapper.updateStockStatus(estimateDO.getSkuGuid(), estimateDO.getResidueQuantity(), 1);
                //乐观锁实现
                boolean flag = updateById(estimateDO);
                if (flag) {
                    updateLine++;
                }
                skuGuids.add(estimateDO.getSkuGuid());
            }
        }
        try {
            if (count.equals(updateLine)) {
                txManager.commit(transactionStatus);
                //菜谱方案售完下架处理
                skuService.sellOutRackItem(skuGuids);
            } else {
                log.error("[reduceSubStock]减少库存失败,所选商品已达到估清剩余数量");
                txManager.rollback(transactionStatus);
            }
        } catch (Exception e) {
            log.error("[reduceSubStock]减少库存失败", e);
            txManager.rollback(transactionStatus);
        }

        // log
        EstimateBO biz = EstimateBizBuilder.build();
        biz.setReduceStockSkuList(estimateDOList);
        estimateOpLogService.saveOpLog(biz.buildEstimateOpLog(), UserContextUtils.getEnterpriseGuid());

    }

    private void updateResidueQuantity(EstimateDO estimateDO, BigDecimal tradeNumber) {
        if (estimateDO.getResidueQuantity().compareTo(tradeNumber) > -1) {
            BigDecimal updateNumber = estimateDO.getResidueQuantity().subtract(tradeNumber);
            log.info("[reduceSubStock]库存充足,进行扣减residueQuantity={},tradeNumber={},updateNumber={}",
                    estimateDO.getResidueQuantity(), tradeNumber, updateNumber);
            estimateDO.setResidueQuantity(updateNumber);
        } else {
            log.info("[reduceSubStock]库存不足足,扣减为0,residueQuantity={},tradeNumber={},updateNumber={}",
                    estimateDO.getResidueQuantity(), tradeNumber, estimateDO.getResidueQuantity());
            estimateDO.setResidueQuantity(BigDecimal.ZERO);
        }
    }

    /**
     * 下单商品 降套餐子项加到stockList
     */
    private List<ItemSkuStockDTO> getSubItemSkuStockList(List<DineInItemDTO> request) {
        List<ItemSkuStockDTO> stockList = Lists.newArrayList();
        // 套餐子项
        request.forEach(e -> {
            List<PackageSubgroupDTO> packageSubgroupDTOS = e.getPackageSubgroupDTOS();
            if (CollectionUtils.isEmpty(packageSubgroupDTOS)) {
                return;
            }

            // 套餐所有子项
            List<SubDineInItemDTO> subList = packageSubgroupDTOS.stream()
                    .flatMap(sub -> sub.getSubDineInItemDTOS().stream())
                    .collect(Collectors.toList());

            subList.forEach(sub -> {
                BigDecimal stock = e.getCurrentCount().multiply(sub.getCurrentCount()).multiply(sub.getPackageDefaultCount());
                String itemName = e.getItemName();
                if (!StringUtils.isEmpty(e.getSkuName())) {
                    itemName = itemName + "-" + e.getSkuName();
                }
                String skuGuid = sub.getSkuGuid();
                ItemSkuStockDTO itemSkuStockDTO = ItemSkuStockDTO.builder().skuGuid(skuGuid).stock(stock)
                        .productName(itemName).gmtCreate(e.getGmtCreate()).build();
                stockList.add(itemSkuStockDTO);
            });
        });
        return stockList;
    }

    /**
     * @param request
     * @return 减少库存
     */
    @Override
    public EstimateResultRespDTO descStock(@Valid List<ItemSkuStockDTO> request) {
        log.info("request={}", JacksonUtils.writeValueAsString(request));
        Map<String, BigDecimal> map = request.stream()
                .collect(Collectors.groupingBy(ItemSkuStockDTO::getSkuGuid, CollectorsUtil.summingBigDecimal(ItemSkuStockDTO::getStock)));
        List<EstimateDO> list = getVerifySkuItemHandler(map);
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        definition.setReadOnly(false);
        TransactionStatus transactionStatus = txManager.getTransaction(definition);
        // 获取到需要扣减库存的商品个数
        Integer count = (int) list
                .stream()
                .filter(s -> s.getIsTheLimit().equals(2))
                .count();
        Integer updateLine = 0;
        List<String> skuGuids = new ArrayList<>();
        // 前端不做，要后端校验，提示哪些商品估清了
        List<String> errorSkuGuids = Lists.newArrayList();
        for (EstimateDO s : list) {
            BigDecimal tradeNumber = map.get(s.getSkuGuid());
            if (ObjectUtils.isEmpty(tradeNumber)) {
                continue;
            }
            log.info("EstimateDO={},tradeNumber={}", JacksonUtils.writeValueAsString(s), tradeNumber);
            if (s.getIsTheLimit() == 2) {
                log.info("校验库存是否充足,进行扣减isSoldOut={}，isTheLimit={}", s.getIsSoldOut(), s.getIsTheLimit());
                // 库存充足 进行扣减
                if (s.getResidueQuantity().compareTo(tradeNumber) <= -1) {
                    errorSkuGuids.add(s.getSkuGuid());
                    continue;
                }
                BigDecimal updateNumber = s.getResidueQuantity().subtract(tradeNumber);
                log.info("库存充足,进行扣减residueQuantity={},tradeNumber={},updateNumber={}", s.getResidueQuantity(), tradeNumber, updateNumber);
                s.setResidueQuantity(updateNumber);
                skuMapper.updateStockStatus(s.getSkuGuid(), s.getResidueQuantity(), 1);
                //乐观锁实现
                boolean flag = updateById(s);
                if (flag) {
                    updateLine++;
                } else {
                    errorSkuGuids.add(s.getSkuGuid());
                }
                skuGuids.add(s.getSkuGuid());
            }
        }
        try {
            if (count.equals(updateLine)) {
                txManager.commit(transactionStatus);
                //菜谱方案售完下架处理
                skuService.sellOutRackItem(skuGuids);
            } else {
                log.error("减少库存失败,所选商品已达到估清剩余数量");
                txManager.rollback(transactionStatus);
                return EstimateResultRespDTO.builder().success(false).skuGuids(errorSkuGuids).build();
            }
        } catch (Exception e) {
            log.error("减少库存失败", e);
            txManager.rollback(transactionStatus);
            return EstimateResultRespDTO.builder().success(false).skuGuids(errorSkuGuids).build();
        }

        // log
        EstimateBO biz = EstimateBizBuilder.build();
        biz.setReduceStockSkuList(list);
        estimateOpLogService.saveOpLog(biz.buildEstimateOpLog(), UserContextUtils.getEnterpriseGuid());
        // 删除估清redis
        removeEstimateRedis(UserContextUtils.getStoreGuid());
        return EstimateResultRespDTO.builder().success(true).build();
    }


    /**
     * @param request
     * @return 增加库存
     */
    @Override
    public Boolean incStock(@RequestBody @Valid List<ItemSkuStockDTO> request) {
        Map<String, BigDecimal> map = request.stream()
                .collect(Collectors.groupingBy(ItemSkuStockDTO::getSkuGuid, CollectorsUtil.summingBigDecimal(ItemSkuStockDTO::getStock)));
        List<EstimateDO> list = getVerifySkuItemHandler(map);
        Map<String, EstimateDO> estimateMap = list.stream().collect(Collectors.toMap(EstimateDO::getSkuGuid, Function.identity(), (key1, key2) -> key1));

        List<String> skuGuids = Lists.newArrayList();
        for (ItemSkuStockDTO skuStockDTO : request) {
            EstimateDO estimateDO = estimateMap.get(skuStockDTO.getSkuGuid());
            if (Objects.isNull(estimateDO)) {
                continue;
            }
            // 回退库存
            log.info("return  EstimateDO  ={}", JacksonUtils.writeValueAsString(estimateDO));
            if (Objects.isNull(skuStockDTO.getGmtCreate()) || Objects.isNull(estimateDO.getGmtModified())) {
                log.info("回退库存,时间无法比较,下单时间:{},设置时间:{}", JacksonUtils.writeValueAsString(skuStockDTO), JacksonUtils.writeValueAsString(estimateDO));
                continue;
            }
            // 下单时间比设置估清时间小，则不回退库存
            if (skuStockDTO.getGmtCreate().compareTo(estimateDO.getGmtModified()) < 0) {
                continue;
            }
            log.info("开始回退库存:{}", JacksonUtils.writeValueAsString(estimateDO));
            rebackEstimateHandler(estimateDO, skuStockDTO.getStock());
            skuGuids.add(estimateDO.getSkuGuid());
        }

        // log
        if (!CollectionUtils.isEmpty(skuGuids)) {
            List<EstimateDO> estimateDbList = listByStoreGuidAndSkuGuidList(UserContextUtils.getStoreGuid(), skuGuids);
            if (!CollectionUtils.isEmpty(estimateDbList)) {
                EstimateBO biz = EstimateBizBuilder.build();
                biz.setReturnStockSkuList(estimateDbList);
                estimateOpLogService.saveOpLog(biz.buildEstimateOpLog(), UserContextUtils.getEnterpriseGuid());
            }
        }
        // 删除估清redis
        removeEstimateRedis(UserContextUtils.getStoreGuid());
        return Boolean.TRUE;
    }

    @Override
    public Boolean dineinFail(List<DineInItemDTO> request) {
        List<ItemSkuStockDTO> stockList = request.stream()
                .map(item -> ItemSkuStockDTO.builder().skuGuid(item.getSkuGuid()).stock(item.getCurrentCount()).gmtCreate(item.getGmtCreate()).build())
                .collect(Collectors.toList());
        // 套餐子项-点此恢复

        return incStock(stockList);
    }

    @Override
    public List<ItemEstimateForAndroidRespDTO> queryEstimateForSyn(BaseDTO baseDTO) {
        List<ItemEstimateForAndroidRespDTO> estimateList = Lists.newArrayList();
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(baseDTO.getStoreGuid());
        log.info("queryEstimateForSyn菜品模式数据：{}", JacksonUtils.writeValueAsString(brandDTO));
        if (ObjectUtils.isEmpty(brandDTO)) {
            log.warn("未查询到品牌信息:{}", baseDTO.getStoreGuid());
            return estimateList;
        }
        //菜谱guid
        String planGuid = getStorePlanGuid(brandDTO,baseDTO.getStoreGuid());
        //菜谱guid为空则是普通模式下得菜品沽清信息
        if (planGuid == null) {
            estimateList = estimateRepository.listNormalEstimateItem(baseDTO);
        }else {
            estimateList = estimateRepository.listPlanEstimateItem(baseDTO.getStoreGuid(),planGuid);
        }
        log.info("估清商品列表：{}",JacksonUtils.writeValueAsString(estimateList));
        return estimateList;
    }

    static boolean isPlan(Integer saleModel){
        return !ObjectUtils.isEmpty(saleModel) && saleModel == SalesModelEnum.RECIPE_MODE.getCode();
    }

    private String getStorePlanGuid(BrandDTO brandDTO, String storeGuid) {
        if(!isPlan(brandDTO.getSalesModel())){
            return null;
        }
        List<PricePlanNowDTO> planNowList = pricePlanStoreService.findPlanNowStoreGuid(LocalDateTime.now(), storeGuid);
        if (CollectionUtil.isEmpty(planNowList)) {
            return null;
        }
        return itemHelper.getEffectStorePlanGuid(planNowList);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveSoldOutStatus(EstimateForManualReqDTO request) {
        ItemSingleDTO singleDTO = new ItemSingleDTO();
        singleDTO.setData(request.getItemGuid());
        ItemInfoRespDTO itemInfo = itemService.getItemInfo(singleDTO);
        //判断是否是套餐 且当前操作为取消估清
        if (Objects.equals(itemInfo.getItemType(), 1) && Objects.equals(request.getIsSoldOut(), 1)) {
            //是套餐判断子菜是否估清
            Boolean flag = itemHelper.setmealVerifyEstimate(request.getItemGuid(), UserContextUtils.getStoreGuid());
            if (!flag) {
                throw new BusinessException("取消估清失败，套餐子菜已估清");
            }
        }
        List<String> skus = skuService.listSkuGuidByItemGuid(Arrays.asList(request.getItemGuid()));
        Integer itemType = request.getItemType();
        if (ItemTypeEnum.MULTI_SPEC.getTypeCode() == itemType) {
            skuEstimateHandler(request, skus);  //调整多规格商品sku是否估清
        } else {
            onlyEstimateHandler(request, skus); //套餐和单规格商品估清
        }
        // 估清修改的的时候触发这个
        /*
        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setStoreGuid(request.getStoreGuid());
        List<ItemEstimateForAndroidRespDTO> estimateSkuList = this.queryEstimateForSyn(baseDTO);
        addItemEstimate(request.getStoreGuid(), estimateSkuList);
        */

        // 一体机手动估清后触发推送
        eventPushHelper.soldOutPushMsgToAndriod(UserContextUtils.getStoreGuid());
        // 删除估清redis
        removeEstimateRedis(UserContextUtils.getStoreGuid());
        return Boolean.TRUE;
    }

    /**
     * 更新商品sku估清
     *
     * @param estimateDO 估清对象
     * @param request    商品估清参数
     */
    private void updateHandler(EstimateForManualReqDTO request, EstimateDO estimateDO) {
        if (!ObjectUtils.isEmpty(estimateDO)) {
            estimateDO.setIsSoldOut(request.getIsSoldOut());
            //更改状态为无限售卖 同时将限量改为不限量
            if (request.getIsSoldOut() == 1) {
                estimateDO.setIsTheLimit(1);
            }
            estimateDO.setGmtModified(LocalDateTime.now());
            updateById(estimateDO);
        }
    }

    private void savaHandler(String skuGuid, EstimateForManualReqDTO request) {
        EstimateDO estimateDO = new EstimateDO();
        try {
            estimateDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
            estimateDO.setIsSoldOut(request.getIsSoldOut());
            estimateDO.setResidueQuantity(BigDecimal.ZERO);
            estimateDO.setIsTheLimit(1);
            estimateDO.setLimitQuantity(new BigDecimal(1000));
            estimateDO.setSkuGuid(skuGuid);
            estimateDO.setVersion(1);
            estimateDO.setStoreGuid(UserContextUtils.getStoreGuid());
        } catch (IOException e) {
            throw new BusinessException("BatchIdGenerator生成商品guid失败");
        }
        save(estimateDO);
    }

    /**
     * 调整多规格商品sku是否估清
     * 前端传递过来商品的sku是需要估清的,剩下的则是取消禁售。
     * 如果不传递sku，那么则所有商品的sku都取消禁售
     *
     * @param request 商品估清请求参数
     * @param skus    商品sku集合
     */
    private void skuEstimateHandler(EstimateForManualReqDTO request, List<String> skus) {
        List<String> skuGuidList = request.getSkuGuidList();  //需要估清sku商品的guid
        String storeGuid = UserContextUtils.getStoreGuid();  //门店guid
        request.setIsSoldOut(2);  //配置sku商品为禁售
        //如果skuGuidList为空,那么该商品所有sku解除禁售，可上架售卖
        if (ObjectUtils.isEmpty(skuGuidList) || skuGuidList.isEmpty()) {
            estimateMapper.batchUpdateEstimateBySkuGuid(skus, 1, storeGuid);  //参数1为：不禁售
            return;
        }
        //skuGuidList：该商品中的sku设置为估清，禁售
        onlyEstimateHandler(request, skuGuidList);
        skus.removeAll(skuGuidList);  //去除需要估清的sku商品,那么剩下的sku则取消禁售。
        if (!skus.isEmpty()) {
            estimateMapper.batchUpdateEstimateBySkuGuid(skus, 1, storeGuid);
        }
    }

    /**
     * 套餐和单规格商品估清
     *
     * @param skus    商品skus
     * @param request 估清请求参数
     */
    private void onlyEstimateHandler(EstimateForManualReqDTO request, List<String> skus) {
        for (String skuGuid : skus) {
            EstimateDO estimateDO = getOne(new LambdaQueryWrapper<EstimateDO>()
                    .eq(EstimateDO::getSkuGuid, skuGuid)
                    .eq(EstimateDO::getStoreGuid, UserContextUtils.getStoreGuid()));
            if (!ObjectUtils.isEmpty(estimateDO)) {
                updateHandler(request, estimateDO);
            } else {
                savaHandler(skuGuid, request);
            }
        }
    }

/*
    private void addItemEstimate(String storeGuid, List<ItemEstimateForAndroidRespDTO> estimateSkuList) {
        redisTemplate.opsForValue().set("estimateItem:" + storeGuid, estimateSkuList, 10, TimeUnit.MINUTES);
    }
*/

    @Override
    public Integer storeItemEstimateReset(Map<String, List<String>> request) {
        executor = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime.longValue(), TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(maximumPoolSize));
        long start = System.currentTimeMillis();
        List<CompletableFuture<Void>> pageContentFutures = new ArrayList<>();
        //参数
        request.forEach((s, o) -> {
            pageContentFutures.add(handle(s, o));
        });
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                pageContentFutures.toArray(new CompletableFuture[pageContentFutures.size()])
        );

        allFutures.join();
        log.info("所有线程已执行完[{}]", allFutures.isDone());
        allFutures.whenComplete((aVoid, throwable) -> {
            log.info("执行最后一步操作");
            // doSth();
            long end = System.currentTimeMillis();
            log.info("耗时:" + (end - start));
        });
        return 1;
    }

    /**
     * @param guid        估清业务主键
     * @param tradeNumber 需要退还数量
     * @return 乐观锁更新失败 -> 再次主动补偿更新
     */
    private Boolean rebackHander(String guid, BigDecimal tradeNumber) {
        EstimateDO estimateDO = getOne(new LambdaQueryWrapper<EstimateDO>().eq(EstimateDO::getGuid, guid));
        //当前状态为非限量 1：不限量 2：限量
        if (!Optional.ofNullable(estimateDO).isPresent() || estimateDO.getIsTheLimit() != 2) {
            return Boolean.TRUE;
        } else {
            rebackEstimateHandler(estimateDO, tradeNumber);
            return Boolean.TRUE;
        }
    }

    /**
     * @param map list 转 map ,key为skuguid  value为数量
     * @return 获取到验证的商品估清sku
     */
    private List<EstimateDO> getVerifySkuItemHandler(Map<String, BigDecimal> map) {
        List<String> skus = map.isEmpty() ? null : new ArrayList<>(map.keySet());
        log.info("当前查询的估清skus={},storeGuid={}", JacksonUtils.writeValueAsString(skus), UserContextUtils.getStoreGuid());
        return list(new LambdaQueryWrapper<EstimateDO>()
                .in(EstimateDO::getSkuGuid, skus)
                .eq(EstimateDO::getStoreGuid, UserContextUtils.getStoreGuid()));
    }

    /**
     * 退菜和订单失败修改对应的sku估清库存
     *
     * @param estimateDO  需要更新的估清Do|
     * @param tradeNumber 需要退换的数量
     */
    private void rebackEstimateHandler(EstimateDO estimateDO, BigDecimal tradeNumber) {
        estimateDO.setResidueQuantity(estimateDO.getResidueQuantity().add(tradeNumber));
        if (!update(estimateDO, new LambdaQueryWrapper<EstimateDO>()
                .eq(EstimateDO::getGuid, estimateDO.getGuid())
                .eq(EstimateDO::getIsTheLimit, 2)
                .eq(EstimateDO::getStoreGuid, UserContextUtils.getStoreGuid()))) {
            //更新失败，自动补偿
            rebackHander(estimateDO.getGuid(), tradeNumber);
        } else {
            skuMapper.updateStockStatus(
                    estimateDO.getSkuGuid(),
                    estimateDO.getResidueQuantity(),
                    1);
        }
    }

    //duo 可不传入,则默认最多3个线程
    CompletableFuture<Void> handle(String enterpriseGuid, List<String> stores) {
        return CompletableFuture.runAsync(() -> {
            //int i = 1/0;
            log.info("当前线程" + Thread.currentThread().getName() + " 当前数据库enterpriseGuid：" + enterpriseGuid + " store guids " + stores);
            try {
                dynamicHelper.changeDatasource(enterpriseGuid);
                //先更新是否限量
                estimateMapper.storeItemEstimateLimitReset(stores);
                //再更新仅第一次需要更新的数量
                estimateMapper.storeItemEstimateUniqueReset(stores);
                //再更新限量的剩余数量
                Integer rowsNumber = estimateMapper.storeItemEstimateReset(stores);
                List<EstimateDO> estimateDOList = estimateMapper.queryEstimateByStoreGuidList(stores);
                estimateDOList.forEach(estimateDO -> {
                    if (estimateDO.getIsTheLimit() == null || estimateDO.getResidueQuantity() == null) {
                        return;
                    }
                    int isOpenStock = estimateDO.getIsTheLimit() == 2 ? 1 : 0;
                    skuMapper.updateStockStatus(estimateDO.getSkuGuid(), estimateDO.getResidueQuantity(), isOpenStock);
                });
                log.info("当前数据库enterpriseGuid :" + enterpriseGuid + " 估清置满sku总数 ：" + rowsNumber);
            } catch (Exception e) {
                log.error("当前数据库enterpriseGuid :" + enterpriseGuid + "估清置满失败，msg ", e.getMessage());
            } finally {
                // 删除估清redis
                batchRemoveEstimateRedis(stores);
            }
        }, executor).exceptionally(
                new Function<Throwable, Void>() {
                    //捕捉异常,不会导致整个流程中断
                    @Override
                    public Void apply(Throwable throwable) {
                        log.info("线程[{}]发生了异常, 继续执行其他线程,错误详情[{}]", Thread.currentThread().getName(), throwable.getMessage());
                        return null;
                    }
                });
    }

    @Override
    public List<SetMealEstimateQuery> getSetMealSubitemEstimate(String itemGuid, String storeGuid) {
        return estimateMapper.getSetMealSubitemEstimate(itemGuid, storeGuid);
    }

    @Override
    public List<ItemEstimateForAndroidDTO> queryEstimateByGuids(String storeGuid, List<String> skuGuids) {
        List<ItemEstimateForAndroidDTO> itemEstimateList = new ArrayList<>();
        List<EstimateDO> estimateDOS = estimateMapper.selectList(
                new LambdaQueryWrapper<EstimateDO>()
                        .eq(EstimateDO::getIsSoldOut, 2)  //是否禁售 1:否 2:是
                        .eq(EstimateDO::getStoreGuid, storeGuid)
                        .eq(EstimateDO::getIsDelete, 0)
                        .in(EstimateDO::getSkuGuid, skuGuids));
        for (EstimateDO estimateDO : estimateDOS) {
            ItemEstimateForAndroidDTO itemEstimateForAndroidDTO = new ItemEstimateForAndroidDTO();
            BeanUtils.copyProperties(estimateDO, itemEstimateForAndroidDTO);
            itemEstimateList.add(itemEstimateForAndroidDTO);
        }
        log.info("skuGuids查询到的商品估清列表：{}", itemEstimateList);
        return itemEstimateList;
    }

    /**
     * 新的一体机估清接口
     * 老估清接口也在使用
     *
     * @param biz 入参
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSoldOut(EstimateBO biz) {
        List<EstimateDO> skuReqList = biz.getSkuList();
        if (CollectionUtils.isEmpty(skuReqList)) {
            throw new BusinessException("估清商品规格为空");
        }
        Map<String, EstimateDO> skuReqMap = skuReqList.stream()
                .collect(Collectors.toMap(EstimateDO::getSkuGuid, Function.identity(), (v1, v2) -> v2));
        List<String> skuGuidList = skuReqList.stream()
                .map(EstimateDO::getSkuGuid)
                .distinct()
                .collect(Collectors.toList());
        List<EstimateDO> updateList = this.list(new LambdaQueryWrapper<EstimateDO>()
                .in(EstimateDO::getSkuGuid, skuGuidList)
                .eq(EstimateDO::getStoreGuid, biz.getStoreGuid())
        );
        List<String> updateSkuGuidList = CollectionUtils.isEmpty(updateList) ? Lists.newArrayList() :
                updateList.stream()
                        .map(EstimateDO::getSkuGuid)
                        .distinct()
                        .collect(Collectors.toList());

        // 删除多余估清
        List<EstimateDO> allEstimateList = this.list(new LambdaQueryWrapper<EstimateDO>()
                .eq(EstimateDO::getItemGuid, biz.getItemGuid())
                .eq(EstimateDO::getStoreGuid, biz.getStoreGuid())
        );

        List<EstimateDO> removeEstimateList = allEstimateList.stream()
                .filter(e -> !updateSkuGuidList.contains(e.getSkuGuid()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(removeEstimateList)) {
            List<String> removeSkuGuidList = removeEstimateList.stream()
                    .map(EstimateDO::getSkuGuid)
                    .distinct()
                    .collect(Collectors.toList());
            estimateMapper.batchDeleteEstimateBySkuGuid(removeSkuGuidList, biz.getStoreGuid());
            biz.setRemoveSkuList(removeEstimateList);
        }

        // 修改估清
        if (!CollectionUtils.isEmpty(updateList)) {
            updateList.forEach(sku -> {
                EstimateDO skuReqDTO = skuReqMap.get(sku.getSkuGuid());
                if (ObjectUtils.isEmpty(skuReqDTO)) {
                    log.warn("未匹配到估清数据");
                    return;
                }
                sku.setResidueQuantity(skuReqDTO.getLimitQuantity());
                sku.setLimitQuantity(skuReqDTO.getLimitQuantity());
                sku.setIsSoldOut(Objects.equals(BigDecimal.ZERO, skuReqDTO.getLimitQuantity()) ? 2 : 1);
                sku.setIsTheLimit(Objects.equals(BigDecimal.ZERO, skuReqDTO.getLimitQuantity()) ? 1 : 2);
                sku.setIsForeverEstimate(skuReqDTO.getIsForeverEstimate());
                sku.setGmtModified(LocalDateTime.now());
                sku.setIsDelete(BooleanEnum.FALSE.getCode());
            });
            this.updateBatchById(updateList);
            biz.setUpdateSkuList(updateList);
            List<String> toUpdateSkuList = updateList.stream()
                    .map(EstimateDO::getSkuGuid)
                    .distinct()
                    .collect(Collectors.toList());
            skuReqList.removeIf(sku -> toUpdateSkuList.contains(sku.getSkuGuid()));
        }

        // 新增估清
        if (!CollectionUtils.isEmpty(skuReqList)) {
            skuReqList.forEach(sku -> {
                sku.setGuid(GUIDUtils.generateGuid(GuidKeyConstant.HSI_ESTIMATE));
                sku.setIsSoldOut(Objects.equals(BigDecimal.ZERO, sku.getLimitQuantity()) ? 2 : 1);
                sku.setResidueQuantity(sku.getLimitQuantity());
                sku.setIsTheLimit(Objects.equals(BigDecimal.ZERO, sku.getLimitQuantity()) ? 1 : 2);
                sku.setVersion(1);
                sku.setStoreGuid(biz.getStoreGuid());
//            estimateDO.setIsTheLimitReset();
//            estimateDO.setUniqueResetFlag();
//            estimateDO.setReminderThreshold();
            });
            this.saveBatch(skuReqList);
            biz.setCreateSkuList(skuReqList);
        }

        // 估清修改的的时候触发这个
        /*
        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setStoreGuid(biz.getStoreGuid());
        List<ItemEstimateForAndroidRespDTO> estimateSkuList = this.queryEstimateForSyn(baseDTO);
        addItemEstimate(biz.getStoreGuid(), estimateSkuList);
        */

        // 一体机手动估清后触发推送
        String storeGuid = UserContextUtils.getStoreGuid();
        eventPushHelper.soldOutPushMsgToAndriod(storeGuid);

        // 营业日自动移除估清
        autoDeleteEstimate(storeGuid);

        // 记录操作日志
        estimateOpLogService.saveOpLog(biz.buildEstimateOpLog(), UserContextUtils.getEnterpriseGuid());
        // 删除估清redis
        removeEstimateRedis(biz.getStoreGuid());
        return true;
    }

    /**
     * 营业日自动移除估清
     *
     * @param storeGuid 门店guid
     */
    private void autoDeleteEstimate(String storeGuid) {
        StoreDTO storeDTO = organizationService.queryStoreByGuid(storeGuid);
        log.info("估清列表查询门店信息 storeDTO={}", JacksonUtils.writeValueAsString(storeDTO));
        if (ObjectUtils.isEmpty(storeDTO) || ObjectUtils.isEmpty(storeDTO.getBusinessStart())) {
            log.warn("未查询到门店营业时间信息 storeGuid={}", storeGuid);
            throw new BusinessException("未查询到门店营业时间信息");
        }
        ConfigReqQueryDTO configReqQueryDTO = new ConfigReqQueryDTO();
        configReqQueryDTO.setDicCode(ConfigEnum.DELETE_ESTIMATE_ITEM.getCode());
        configReqQueryDTO.setStoreGuid(storeGuid);
        ConfigRespDTO configRespDTO = configFeginService.selectEstimateResetTime(configReqQueryDTO);
        if (ObjectUtils.isEmpty(configRespDTO)) {
            ConfigReqDTO saveConfig = new ConfigReqDTO();
            saveConfig.setStoreGuid(storeGuid);
            saveConfig.setDicCode(ConfigEnum.DELETE_ESTIMATE_ITEM.getCode());
            saveConfig.setDicName(ConfigEnum.DELETE_ESTIMATE_ITEM.getDesc());
            saveConfig.setDictValue(String.valueOf(storeDTO.getBusinessStart()));
            configFeginService.saveEstimateResetTime(saveConfig);
            return;
        }
        if (!Objects.equals(String.valueOf(storeDTO.getBusinessStart()), configRespDTO.getDictValue())) {
            log.info("查询到门店营业时间与任务不一致 storeGuid={} businessStart={}", storeGuid, storeDTO.getBusinessStart());
            ConfigReqDTO updateConfig = new ConfigReqDTO();
            updateConfig.setGuid(configRespDTO.getGuid());
            updateConfig.setStoreGuid(configRespDTO.getStoreGuid());
            updateConfig.setDicCode(configRespDTO.getDicCode());
            updateConfig.setDicName(configRespDTO.getDicName());
            updateConfig.setDictValue(String.valueOf(storeDTO.getBusinessStart()));
            updateConfig.setEnterpriseGuid(configRespDTO.getEnterpriseGuid());
            updateConfig.setGmtModified(LocalDateTime.now());
            configFeginService.saveEstimateResetTime(updateConfig);
        }
    }

    /**
     * 一体机估清商品列表
     *
     * @param storeGuid 门店guid
     * @return 一体机估清商品列表
     */
    @Override
    public EstimateForAndroidRespDTO listEstimate(String storeGuid) {
        if (StringUtils.isEmpty(storeGuid)) {
            throw new BusinessException(THE_STORE_GUID_IS_EMPTY);
        }
        EstimateForAndroidRespDTO estimate = new EstimateForAndroidRespDTO();

        // 恢复售卖时间，以开始营业时间为准
        BusinessDateReqDTO reqDTO = new BusinessDateReqDTO();
        ArrayList<String> storeGuidList = new ArrayList<>();
        storeGuidList.add(UserContextUtils.getStoreGuid());
        reqDTO.setStoreGuidList(storeGuidList);
        reqDTO.setQueryDateTime(LocalDateTime.now());
        LocalDate businessDay = organizationService.queryBusinessDay(reqDTO);
        log.info("估清列表查询门店营业日 businessDay={}", JacksonUtils.writeValueAsString(businessDay));
        StoreDTO storeDTO = organizationService.queryStoreByGuid(storeGuid);
        log.info("估清列表查询门店信息 storeDTO={}", JacksonUtils.writeValueAsString(storeDTO));
        if (ObjectUtils.isEmpty(storeDTO) || ObjectUtils.isEmpty(storeDTO.getBusinessStart())) {
            log.warn("未查询到门店营业时间信息 storeGuid={}", storeGuid);
            return estimate;
        }
        estimate.setResumeSaleTime(businessDay.plusDays(1) + " " + storeDTO.getBusinessStart() + "恢复售卖");

        List<EstimateDO> estimateList = this.list(new LambdaQueryWrapper<EstimateDO>()
                .eq(EstimateDO::getStoreGuid, storeGuid)
                .eq(EstimateDO::getIsDelete, BooleanEnum.FALSE.getCode())
                .orderByDesc(EstimateDO::getGmtModified)
        );
        if (CollectionUtils.isEmpty(estimateList)) {
            log.warn("未查询到估清商品信息 storeGuid={}", storeGuid);
            return estimate;
        }
        //构建套餐的估清数据
        buildSubGroupParentEstimate(estimateList, storeGuid);
        // 先通过过skus查询到所有商品和规格信息
        List<String> skuGuidList = estimateList.stream()
                .map(EstimateDO::getSkuGuid)
                .distinct()
                .collect(Collectors.toList());

        // 查询规格下的套餐商品
        List<SkuInfoRespDTO> skuInfoList = skuService.listSkuInfo(skuGuidList);
        if(CollectionUtil.isEmpty(skuInfoList)){
            log.warn("未查询到估清商品信息,雇请商品在门店库不存在 storeGuid={}，skuGuidList={}", storeGuid,skuGuidList);
            //如不存在清除雇请商品表
            remove(new LambdaQueryWrapper<EstimateDO>()
                    .in(EstimateDO::getSkuGuid, skuGuidList));
            return estimate;
        }
        Map<String, SkuInfoRespDTO> skuInfoMap = skuInfoList.stream()
                .collect(Collectors.toMap(SkuInfoRespDTO::getSkuGuid, Function.identity(), (v1, v2) -> v2));

        List<ItemInfoRespDTO> itemInfoList = itemService.listItemInfoBySku(skuGuidList);

        // 然后用估清list设置skuReqList，通过itemguid分组
        List<EstimateSkuRespDTO> skuRespList = new ArrayList<>();
        estimateList.forEach(estimateDO -> {
            SkuInfoRespDTO skuInfo = skuInfoMap.get(estimateDO.getSkuGuid());
            if (ObjectUtils.isEmpty(skuInfo)) {
                log.warn("未查询到估清商品的规格信息 skuGuid={}", estimateDO.getSkuGuid());
                return;
            }
            EstimateSkuRespDTO skuRespDTO = new EstimateSkuRespDTO();
            skuRespDTO.setSkuGuid(estimateDO.getSkuGuid());
            skuRespDTO.setSkuName(skuInfo.getName());
            skuRespDTO.setLimitQuantity(estimateDO.getLimitQuantity());
            skuRespDTO.setResidueQuantity(estimateDO.getResidueQuantity());
            skuRespDTO.setIsForeverEstimate(estimateDO.getIsForeverEstimate());
            skuRespDTO.setItemGuid(skuInfo.getItemGuid());
            skuRespDTO.setEstimateGmtModified(estimateDO.getGmtModified());
            skuRespList.add(skuRespDTO);
        });
        Map<String, List<EstimateSkuRespDTO>> estimateSkuRespMap = skuRespList.stream()
                .collect(Collectors.groupingBy(EstimateSkuRespDTO::getItemGuid));

        // 最后通过商品guid去获取，然后设置进去
        List<EstimateItemRespDTO> itemRespList = new ArrayList<>();
        itemInfoList.forEach(itemInfo -> {
            EstimateItemRespDTO itemRespDTO = new EstimateItemRespDTO();
            itemRespDTO.setItemGuid(itemInfo.getItemGuid());
            itemRespDTO.setItemName(itemInfo.getName());
            // 套餐要加【套】，称重要加【重】
            if (Objects.equals(ItemTypeEnum.PACKAGE.getTypeCode(), itemInfo.getItemType())) {
                itemRespDTO.setItemName("【套】" + itemInfo.getName());
            }
            if (Objects.equals(ItemTypeEnum.WEIGHING.getTypeCode(), itemInfo.getItemType())) {
                itemRespDTO.setItemName("【重】" + itemInfo.getName());
            }
            List<EstimateSkuRespDTO> skuRespDTOList = estimateSkuRespMap.get(itemInfo.getItemGuid());
            if (ObjectUtils.isEmpty(skuRespDTOList)) {
                log.warn("未匹配到估清规格信息 itemGuid={}", itemInfo.getItemGuid());
                return;
            }
            itemRespDTO.setSkuRespList(skuRespDTOList);
            List<EstimateSkuRespDTO> newSkuList = new ArrayList<>(skuRespDTOList);
            newSkuList.sort(Comparator.comparing(EstimateSkuRespDTO::getEstimateGmtModified).reversed());
            itemRespDTO.setEstimateGmtModified(newSkuList.get(0).getEstimateGmtModified());
            itemRespList.add(itemRespDTO);
        });
        // 修改时间倒序
        itemRespList.sort(Comparator.comparing(EstimateItemRespDTO::getEstimateGmtModified).reversed());
        estimate.setItemRespList(itemRespList);
        return estimate;
    }

    private void buildSubGroupParentEstimate(List<EstimateDO> estimateList, String storeGuid) {
        //过滤出售罄的商品
        List<String> soldOutSkuGuid = estimateList.stream()
                .filter(e -> e.getResidueQuantity().compareTo(BigDecimal.ZERO) == 0)
                .map(EstimateDO::getSkuGuid)
                .collect(Collectors.toList());
        if(CollUtil.isEmpty(soldOutSkuGuid)){
            return;
        }

        // 构建套餐主商品估清信息
        List<PkgSkuDTO> pkgSkuDTOList = estimateMapper.queryNormalPkgSkuDTO(storeGuid);
        if (CollectionUtils.isEmpty(pkgSkuDTOList)) {
            return;
        }
        Map<String, List<PkgSkuDTO>> pkgSkuGroupMap = pkgSkuDTOList.stream()
                .collect(Collectors.groupingBy(PkgSkuDTO::getSkuGuid));

        pkgSkuGroupMap.forEach((skuGuid, subSkuList) -> {
            List<String> subSkuGuidList = subSkuList.stream()
                    .map(PkgSkuDTO::getSubSkuGuid)
                    .distinct()
                    .collect(Collectors.toList());
            if (soldOutSkuGuid.contains(skuGuid)) {
                return;
            }
            if (soldOutSkuGuid.containsAll(subSkuGuidList)) {
                EstimateDO estimateDO = new EstimateDO();
                estimateDO.setSkuGuid(skuGuid);
                estimateDO.setLimitQuantity(BigDecimal.ZERO);
                estimateDO.setResidueQuantity(BigDecimal.ZERO);
                estimateDO.setIsForeverEstimate(2);
                estimateDO.setGmtModified(LocalDateTime.now());
                estimateList.add(estimateDO);
            }
        });
    }

    /**
     * 批量取消估清
     *
     * @param biz 规格Guid列表
     * @return Boolean
     */
    @Override
    public Boolean batchCancelEstimate(EstimateBO biz) {
        List<String> skuGuidList = biz.getItemGuidList();
        String storeGuid = biz.getStoreGuid();
        if (CollectionUtils.isEmpty(skuGuidList)) {
            throw new BusinessException("批量取消估清商品规格Guid为空");
        }
        if (StringUtils.isEmpty(storeGuid)) {
            throw new BusinessException(THE_STORE_GUID_IS_EMPTY);
        }
        //若存在套餐下的子商品-点此恢复 2024.11.4估清功能迭代后不在估清套餐下子菜

        // 查询删除之前的数据记录日志
        List<EstimateDO> oldEstimateList = listByStoreGuidAndSkuGuidList(storeGuid, skuGuidList);
        biz.setBatchCancelSkuList(oldEstimateList);

        estimateMapper.batchDeleteEstimateBySkuGuid(skuGuidList, storeGuid);

        // 记录操作日志
        estimateOpLogService.saveOpLog(biz.buildEstimateOpLog(), UserContextUtils.getEnterpriseGuid());

        // 删除估清redis
        removeEstimateRedis(storeGuid);
        return true;
    }

    private List<String> listSubGroupEstimateSkuGuidBySku(List<String> skuGuidList) {
        //根据guid查询套餐估清子菜品
        return estimateMapper.listSubGroupEstimateSkuGuidBySku(skuGuidList);
    }

    /**
     * 批量停售
     *
     * @param biz 规格Guid列表
     * @return Boolean
     */
    @Override
    public Boolean batchStopSell(EstimateBO biz) {
        List<String> skuGuidList = biz.getItemGuidList();
        if (CollectionUtils.isEmpty(skuGuidList)) {
            throw new BusinessException("批量停售商品Guid为空");
        }
        if (StringUtils.isEmpty(biz.getStoreGuid())) {
            throw new BusinessException(THE_STORE_GUID_IS_EMPTY);
        }
        estimateMapper.batchStopSellBySkuGuid(skuGuidList, biz.getStoreGuid());
        // 查询修改之后的数据记录日志
        List<EstimateDO> updateEstimateList = listByStoreGuidAndSkuGuidList(biz.getStoreGuid(), skuGuidList);
        biz.setBatchStopSkuList(updateEstimateList);
        estimateOpLogService.saveOpLog(biz.buildEstimateOpLog(), UserContextUtils.getEnterpriseGuid());

        // 删除估清redis
        removeEstimateRedis(biz.getStoreGuid());
        return true;
    }

    @Override
    public List<EstimateDO> listByStoreGuidAndSkuGuidList(String storeGuid, List<String> skuGuidList) {
        QueryWrapper<EstimateDO> qw = new QueryWrapper<>();
        qw.lambda().eq(EstimateDO::getStoreGuid, storeGuid);
        qw.lambda().in(EstimateDO::getSkuGuid, skuGuidList);
        return list(qw);
    }

    @Override
    public List<EstimateDO> listByStoreGuidsAndIsForeverList(List<String> storeGuids) {
        QueryWrapper<EstimateDO> qw = new QueryWrapper<>();
        qw.lambda().in(EstimateDO::getStoreGuid, storeGuids);
        qw.lambda().eq(EstimateDO::getIsForeverEstimate, 1);
        return list(qw);
    }

    /**
     * 商品估清定时恢复
     *
     * @param request 企业和门店map
     * @return boolean
     */
    @Override
    public Boolean storeItemEstimateCancel(Map<String, List<String>> request) {
        estimateCancelExecutor = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime.longValue(),
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(maximumPoolSize));
        long start = System.currentTimeMillis();
        List<CompletableFuture<Void>> pageContentFutures = new ArrayList<>();

        //参数
        request.forEach((s, o) -> pageContentFutures.add(handleEstimateCancel(s, o)));
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                pageContentFutures.toArray(new CompletableFuture[pageContentFutures.size()])
        );

        allFutures.join();
        log.info("所有线程已执行完[{}]", allFutures.isDone());
        allFutures.whenComplete((aVoid, throwable) -> {
            log.info("执行最后一步操作");
            // doSth();
            long end = System.currentTimeMillis();
            log.info("耗时:" + (end - start));
        });
        return true;
    }

    /**
     * 根据商品guid查询估清商品详情
     *
     * @param itemGuid 商品Guid
     * @return 估清商品详情
     */
    @Override
    public EstimateItemRespDTO listEstimateByItem(String itemGuid) {
        if (StringUtils.isEmpty(itemGuid)) {
            throw new BusinessException("商品guid不能为空");
        }
        List<EstimateDO> list = this.list(new LambdaQueryWrapper<EstimateDO>()
                .eq(EstimateDO::getStoreGuid, UserContextUtils.getStoreGuid())
                .eq(EstimateDO::getItemGuid, itemGuid)
                .eq(EstimateDO::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        EstimateItemRespDTO dto = new EstimateItemRespDTO();
        dto.setItemGuid(list.get(0).getItemGuid());
        List<EstimateSkuRespDTO> skuRespList = new ArrayList<>();
        list.forEach(estimateDO -> {
            EstimateSkuRespDTO skuRespDTO = new EstimateSkuRespDTO();
            skuRespDTO.setSkuGuid(estimateDO.getSkuGuid());
            skuRespDTO.setLimitQuantity(estimateDO.getLimitQuantity());
            skuRespDTO.setResidueQuantity(estimateDO.getResidueQuantity());
            skuRespDTO.setIsForeverEstimate(estimateDO.getIsForeverEstimate());
            skuRespList.add(skuRespDTO);
        });
        dto.setSkuRespList(skuRespList);
        return dto;
    }

    private CompletableFuture<Void> handleEstimateCancel(String enterpriseGuid, List<String> stores) {
        //捕捉异常,不会导致整个流程中断
        return CompletableFuture.runAsync(() -> {
            log.info("当前线程" + Thread.currentThread().getName() + " 当前数据库enterpriseGuid：" + enterpriseGuid +
                    " storeGuidList " + stores);
            try {
                dynamicHelper.changeDatasource(enterpriseGuid);
                // 查询需要恢复的商品数据
                List<EstimateDO> schedulingSkuList = listByStoreGuidsAndIsForeverList(stores);
                EstimateBO biz = EstimateBizBuilder.build();
                biz.setSchedulingSkuList(schedulingSkuList);
                // 恢复所有门店下需要定时恢复的商品
                estimateMapper.batchDeleteEstimateByStoreGuid(stores);
                estimateOpLogService.saveOpLog(biz.buildEstimateOpLog(), enterpriseGuid);
            } catch (Exception e) {
                log.error("当前数据库enterpriseGuid :" + enterpriseGuid + "商品估清定时恢复失败，msg ", e.getMessage());
            } finally {
                // 删除估清redis
                batchRemoveEstimateRedis(stores);
            }
        }, estimateCancelExecutor).exceptionally(
                throwable -> {
                    log.info("线程[{}]发生了异常, 继续执行其他线程,错误详情[{}]", Thread.currentThread().getName(), throwable.getMessage());
                    return null;
                });
    }


    private void removeEstimateRedis(String storeGuid) {
        redisTemplate.delete(REDIS_ESTIMATE_ITEM + ":" + storeGuid);
    }

    private void batchRemoveEstimateRedis(List<String> storeGuids) {
        List<String> estimateRedisKey = storeGuids.stream().map(e -> REDIS_ESTIMATE_ITEM + ":" + e).collect(Collectors.toList());
        redisTemplate.delete(estimateRedisKey);
    }
}


