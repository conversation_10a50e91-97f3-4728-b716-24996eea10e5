package com.holderzone.saas.store.item.entity;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @date 2021/6/3 10:36
 */
public class QRCode implements Serializable {
    private String content;
    private Integer width;
    private Integer height;

    public QRCode() {
    }

    public String getContent() {
        return this.content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getWidth() {
        return this.width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return this.height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }
}