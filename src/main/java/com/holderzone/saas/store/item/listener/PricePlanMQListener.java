package com.holderzone.saas.store.item.listener;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.item.constant.MqConstant;
import com.holderzone.saas.store.item.dto.SyncPricePlanMessageDTO;
import com.holderzone.saas.store.item.entity.domain.PricePlanDO;
import com.holderzone.saas.store.item.entity.enums.PricePlanStatusEnum;
import com.holderzone.saas.store.item.entity.enums.SalesModelEnum;
import com.holderzone.saas.store.item.helper.EventPushHelper;
import com.holderzone.saas.store.item.mapper.PricePlanStoreMapper;
import com.holderzone.saas.store.item.service.IPricePlanService;
import com.holderzone.saas.store.item.service.impl.PricePlanServiceImpl;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * 监听价格方案的MQ
 *
 * <AUTHOR> chen
 * @version 1.0
 * @since 2020-10-29
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = MqConstant.PRICE_PLAN_SYNC_ITEM_TOPIC,
        tags = {
                MqConstant.PRICE_PLAN_SYNC_ITEM_TAG
        },
        consumerGroup = MqConstant.PRICE_PLAN_SYNC_ITEM_GROUP
)
public class PricePlanMQListener extends AbstractRocketMqConsumer<RocketMqTopic, SyncPricePlanMessageDTO> {
    // 新增操作
    public static final int ADD = 1;

    // 删除操作
    public static final int DELETE = 2;

    @Resource
    private PricePlanStoreMapper planStoreMapper;

    @Resource
    private IPricePlanService pricePlanService;

    @Resource
    private EventPushHelper eventPushHelper;

    @Resource
    private PricePlanServiceImpl planService;

    @Resource
    private OrganizationService organizationService;

    @Override
    public boolean consumeMsg(SyncPricePlanMessageDTO messageDTO, MessageExt messageExt) {
        log.info("PricePlanMQListener 开始处理菜谱消息的推送 messageDTO={}", JacksonUtils.writeValueAsString(messageDTO));

        String pricePlanGuid = messageDTO.getPricePlanGuid();
        String enterpriseGuid = messageDTO.getEnterpriseGuid();
        String brandGuid = messageDTO.getBrandGuid();
        Integer messageType = messageDTO.getMessageType();

        // 将Enterprise存入threadLocal中 设置数据源
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        // 构造Feign的header信息
        UserContext userInfoDTO = UserContextUtils.get();
        userInfoDTO.setEnterpriseGuid(enterpriseGuid);

        // 查询销售模式
        BrandDTO brandDTO = organizationService.queryBrandByGuid(brandGuid);
        if (Objects.nonNull(brandDTO) && Objects.nonNull(brandDTO.getSalesModel())
                && SalesModelEnum.NORMAL_MODE.getCode() == brandDTO.getSalesModel()) {
            log.warn("该品牌销售模式为普通模式,品牌信息:{},该消息:{}", brandDTO, messageDTO);
            return false;
        }

        // 查询菜谱信息
        PricePlanDO pricePlanDO = pricePlanService.getById(pricePlanGuid);
        if (ObjectUtils.isEmpty(pricePlanDO)) {
            log.warn("方案查询为空 pricePlanGuid={}", pricePlanGuid);
            return false;
        }
        if (PricePlanStatusEnum.LASTING_DISABLE.getCode() == pricePlanDO.getStatus()) {
            log.warn("方案查询已永久停用,不推送消息 pricePlanGuid={}", pricePlanGuid);
            return false;
        }
        List<String> storeGuidList = planStoreMapper.getPlanStoreGuidList(pricePlanGuid, null);
        if (CollectionUtils.isEmpty(storeGuidList)) {
            log.warn("菜谱方案{}，没有配置门店，不推送消息", pricePlanDO.getGuid());
            return false;
        }
        String contentMessage;
        if (messageType == 1) {
            LocalDateTime startTime = pricePlanDO.getStartTime().atDate(LocalDate.now());
            LocalDateTime endTime = pricePlanDO.getEndTime().atDate(LocalDate.now());

            String startHour = startTime.format(DateTimeFormatter.ofPattern("HH:mm"));
            String endHour = endTime.format(DateTimeFormatter.ofPattern("HH:mm"));

            int startInt = Integer.parseInt(startHour.replace(":", ""));
            int endInt = Integer.parseInt(endHour.replace(":", ""));
            if (endInt > startInt) {
                contentMessage = "售卖每天" + startHour + "至" + endHour + "的菜谱时间已到，请重新登录当前设备进行切换菜谱";
            } else {
                contentMessage = "售卖每天" + startHour + "至次日" + endHour + "的菜谱时间已到，请重新登录当前设备进行切换菜谱";
            }
            //菜谱生效 解绑冲突了门店及菜谱信息
            if (PricePlanStatusEnum.USING.getCode() == pricePlanDO.getStatus()) {
                pricePlanService.unBindStoreAndPlan(storeGuidList, pricePlanDO.getStartTime(), pricePlanDO.getEndTime()
                        , pricePlanDO.getSellTimeType(), pricePlanDO.getGuid());
            }
            // 1推送方案商品
            eventPushHelper.pricePlanChangedSendMsgToAndriod(storeGuidList, contentMessage,
                    BusinessMsgTypeEnum.PRICE_PLAN_CHANGE.getId());

            // 开启结束点通知
            pricePlanService.planEndNoti(pricePlanDO);
        } else if (messageType == 2) {
            LocalDateTime startTime = pricePlanDO.getStartTime().atDate(LocalDate.now());
            LocalDateTime endTime = pricePlanDO.getEndTime().atDate(LocalDate.now());

            String startHour = startTime.format(DateTimeFormatter.ofPattern("HH:mm"));
            String endHour = endTime.format(DateTimeFormatter.ofPattern("HH:mm"));
            int startInt = Integer.parseInt(startHour.replace(":", ""));
            int endInt = Integer.parseInt(endHour.replace(":", ""));
            if (endInt > startInt) {
                contentMessage = "每天" + startHour + "至" + endHour + "的菜谱已售卖结束，不退出登录不影响正常售卖，当前设备重新登录后将清空菜谱菜品";
            } else {
                contentMessage = "每天" + startHour + "至次日" + endHour + "的菜谱已售卖结束，不退出登录不影响正常售卖，当前设备重新登录后将清空菜谱菜品";
            }
            // 2删除操作
            eventPushHelper.pricePlanChangedSendMsgToAndriod(storeGuidList, contentMessage, BusinessMsgTypeEnum.PRICE_PLAN_END.getId());

            // 开启开始点通知
            pricePlanService.planStartNoti(pricePlanDO, false);
        } else {
            //菜谱生效 解绑冲突了门店及菜谱信息
            if (PricePlanStatusEnum.USING.getCode() == pricePlanDO.getStatus()) {
                pricePlanService.unBindStoreAndPlan(storeGuidList, pricePlanDO.getStartTime(), pricePlanDO.getEndTime(), pricePlanDO.getSellTimeType(), pricePlanDO.getGuid());
            }
            if (PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode() == pricePlanDO.getStatus()) {
                planService.timeExecution(pricePlanDO.getGuid());
            }
            // 3全时段推送
            contentMessage = "门店商品已更新，请重新登录当前设备同步信息";
            log.info("菜谱提示异常日志跟踪15，{}", pricePlanGuid);
            eventPushHelper.pushMsgToAndriod(storeGuidList, contentMessage);
        }
        return true;
    }
}
