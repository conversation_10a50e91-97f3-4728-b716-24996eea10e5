package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupUpdateReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.enums.item.ModuleEntranceEnum;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.entity.domain.*;
import com.holderzone.saas.store.item.helper.EventPushHelper;
import com.holderzone.saas.store.item.mapper.AttrGroupMapper;
import com.holderzone.saas.store.item.mapper.AttrMapper;
import com.holderzone.saas.store.item.service.*;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.MapStructUtils;
import com.holderzone.saas.store.item.util.PushUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.item.constant.Constant.OP_FAIL;
import static com.holderzone.saas.store.item.constant.Constant.TRUE;

/**
 * <p>
 * 属性值（属性组下的属性内容） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Service
@Slf4j
public class AttrGroupServiceImpl extends ServiceImpl<AttrGroupMapper, AttrGroupDO> implements IAttrGroupService {
    private final AttrMapper attrMapper;
    private final AttrGroupMapper attrGroupMapper;
    private final IAttrService attrService;
    private final DynamicHelper dynamicHelper;
    private final IRTypeAttrService typeAttrService;
    private final ITypeService typeService;
    private final EventPushHelper eventPushHelper;
    private final IRItemAttrGroupService itemAttrGroupService;
    private final PushUtils pushUtils;
    private final IItemService itemService;
    private final IRAttrItemAttrGroupService attrItemAttrGroupService;

    @Autowired
    public AttrGroupServiceImpl(AttrMapper attrMapper, AttrGroupMapper attrGroupMapper, IAttrService attrService, DynamicHelper dynamicHelper, IRTypeAttrService typeAttrService, @Lazy ITypeService typeService, @Lazy EventPushHelper eventPushHelper, IRItemAttrGroupService itemAttrGroupService, PushUtils pushUtils, @Lazy IItemService itemService, IRAttrItemAttrGroupService attrItemAttrGroupService) {
        this.attrMapper = attrMapper;
        this.attrGroupMapper = attrGroupMapper;
        this.attrService = attrService;
        this.dynamicHelper = dynamicHelper;
        this.typeAttrService = typeAttrService;
        this.typeService = typeService;
        this.eventPushHelper = eventPushHelper;
        this.itemAttrGroupService = itemAttrGroupService;
        this.pushUtils = pushUtils;
        this.itemService = itemService;
        this.attrItemAttrGroupService = attrItemAttrGroupService;
    }

    @Transactional(readOnly = true)
    @Override
    public List<AttrGroupAttrRespDTO> listAttrGroupByOrganization(ItemSingleDTO itemSingleDTO) {
        if (StringUtils.isEmpty(itemSingleDTO.getData())) {
            throw new ParameterException("所属品牌或门店不能为空");
        }
        List<AttrGroupDO> attrGroupDOList = list(
                new LambdaQueryWrapper<AttrGroupDO>()
                        .eq(itemSingleDTO.getFrom().equals(ModuleEntranceEnum.STORE.code()), AttrGroupDO::getStoreGuid, itemSingleDTO.getData())
                        .eq(itemSingleDTO.getFrom().equals(ModuleEntranceEnum.BRAND.code()), AttrGroupDO::getBrandGuid, itemSingleDTO.getData())
                        // .eq(Optional.ofNullable(itemSingleDTO.getKeywords()).isPresent(), AttrGroupDO::getAttrGroupFrom, Integer.valueOf(itemSingleDTO.getKeywords()))
                        .orderByAsc(AttrGroupDO::getSort));
        if (CollectionUtils.isEmpty(attrGroupDOList)) {
            return new ArrayList<>();
        }
        // 排序 1.sort升序，2.创建时间降序
        attrGroupDOList.sort((o1, o2) -> {
            int sort = o1.getSort() - o2.getSort();
            if (sort == 0) {
                sort = (int) (DateTimeUtils.localDateTime2Mills(o1.getGmtCreate()) - DateTimeUtils.localDateTime2Mills(o2.getGmtCreate()));
            }
            return sort;
        });
        //属性组返回实体
        List<AttrGroupAttrRespDTO> attrGroupAttrRespDTOList = MapStructUtils.INSTANCE.attrGroupDOList2attrGroupAttrRespDTOList(attrGroupDOList);
        List<String> attrGroupGuidList = attrGroupDOList
                .stream()
                .map(AttrGroupDO::getGuid)
                .collect(Collectors.toList());
        // 查询属性集合
        List<AttrDO> attrDOList = attrMapper
                .selectList(new LambdaQueryWrapper<AttrDO>()
                        .in(AttrDO::getAttrGroupGuid, attrGroupGuidList));
        if (CollectionUtils.isEmpty(attrDOList)) {
            return attrGroupAttrRespDTOList;
        }
        // 属性返回实体
        List<AttrRespDTO> attrRespDTOList = MapStructUtils.INSTANCE.attrDOList2attrRespDTOList(attrDOList);
        // 属性GUID集合
        List<String> attrGuidList = attrRespDTOList
                .stream()
                .map(AttrRespDTO::getAttrGuid)
                .collect(Collectors.toList());
        // 将属性关联分类
        List<RTypeAttrDO> typeAttrDOList = typeAttrService
                .list(new LambdaQueryWrapper<RTypeAttrDO>()
                        .in(RTypeAttrDO::getAttrGuid, attrGuidList));
        if (CollectionUtils.isNotEmpty(typeAttrDOList)) {
            // 设置分类到对应的属性上
            setAttrTypeList(typeAttrDOList, attrRespDTOList);
        }
        // 将属性组关联属性
        attrGroupAttrRespDTOList.forEach(attrGroupAttrRespDTO -> {
            List<AttrRespDTO> thisGroupAttrList = attrRespDTOList
                    .stream()
                    .filter(attrRespDTO -> attrGroupAttrRespDTO.getAttrGroupGuid().equals(attrRespDTO.getAttrGroupGuid()))
                    .collect(Collectors.toList());
            attrGroupAttrRespDTO.setAttrList(thisGroupAttrList);
        });
        return attrGroupAttrRespDTOList;
    }

    /**
     * 设置分类集合到对应的属性上
     *
     * @param typeAttrDOList
     * @param attrRespDTOList
     */
    private void setAttrTypeList(List<RTypeAttrDO> typeAttrDOList, List<AttrRespDTO> attrRespDTOList) {
        // 页面属性关联的所有分类GUID
        Set<String> typeGuidSet = typeAttrDOList
                .stream()
                .map(RTypeAttrDO::getTypeGuid)
                .collect(Collectors.toSet());
        // 页面涉及所有分类的详情集合
        if (CollectionUtils.isNotEmpty(typeGuidSet)) {
            List<TypeDO> typeDOList = (List<TypeDO>) typeService.listByIds(typeGuidSet);
            attrRespDTOList.forEach(attrRespDTO -> {
                Set<RTypeAttrDO> typeSet = typeAttrDOList
                        .stream()
                        .filter(typeAttrDO -> attrRespDTO.getAttrGuid().equals(typeAttrDO.getAttrGuid()))
                        .collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(typeSet)) {
                    List<TypeWebRespDTO> typeWebRespDTOList = MapStructUtils.INSTANCE.typeAttrDOList2typeWebRespDTOList(typeSet
                            .stream()
                            .collect(Collectors.toList()));
                    typeWebRespDTOList.forEach(typeWebRespDTO -> {
                        TypeDO typeDetail = typeDOList
                                .stream()
                                .filter(typeDO -> typeDO.getGuid().equals(typeWebRespDTO.getTypeGuid()))
                                .findFirst()
                                .orElse(null);
                        if (Objects.isNull(typeDetail)) {
                            throw new BusinessException("后台代码有误，请修改");
                        }
                        typeWebRespDTO.setName(typeDetail.getName());
                    });
                    attrRespDTO.setTypeList(typeWebRespDTOList);
                }
            });
        }
    }

    @Transactional(readOnly = true)
    @Override
    public List<AttrGroupAttrRespDTO> listAttrForSaveItem(ItemSingleDTO itemSingleDTO) {
        if (Objects.isNull(itemSingleDTO.getData())) {
            throw new ParameterException("所属品牌或门店不能为空");
        }
        List<AttrGroupDO> attrGroupDOList = list(new LambdaQueryWrapper<AttrGroupDO>()
                .eq(AttrGroupDO::getIsEnable, TRUE)
                .eq(Objects.equals(ModuleEntranceEnum.STORE.code(), itemSingleDTO.getFrom()), AttrGroupDO::getAttrGroupFrom, 0)
                .eq(Objects.equals(ModuleEntranceEnum.STORE.code(), itemSingleDTO.getFrom()), AttrGroupDO::getStoreGuid, itemSingleDTO.getData())
                .eq(Objects.equals(ModuleEntranceEnum.BRAND.code(), itemSingleDTO.getFrom()), AttrGroupDO::getBrandGuid, itemSingleDTO.getData())
                .orderByAsc(AttrGroupDO::getSort)
        );
        if (CollectionUtils.isEmpty(attrGroupDOList)) {
            return new ArrayList<>();
        }
        // 将属性组关联属性
        List<AttrGroupAttrRespDTO> attrGroupAttrRespDTOList = MapStructUtils.INSTANCE.attrGroupDOList2attrGroupAttrRespDTOList(attrGroupDOList);
        // 属性组GUID集合
        List<String> attrGroupGuidList = attrGroupDOList
                .stream()
                .map(AttrGroupDO::getGuid)
                .collect(Collectors.toList());
        // 属性集合
        List<AttrRespDTO> attrRespDTOList = attrService.listAttrByGroup(attrGroupGuidList);
        Iterator<AttrGroupAttrRespDTO> iterator = attrGroupAttrRespDTOList.iterator();
        while (iterator.hasNext()) {
            AttrGroupAttrRespDTO attrGroupAttrRespDTO = iterator.next();
            List<AttrRespDTO> thisGroupAttrList = attrRespDTOList.stream()
                    .filter(attrRespDTO -> attrGroupAttrRespDTO.getAttrGroupGuid().equals(attrRespDTO.getAttrGroupGuid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(thisGroupAttrList)) {
                iterator.remove();
                continue;
            }
            attrGroupAttrRespDTO.setAttrList(thisGroupAttrList);
        }
        return attrGroupAttrRespDTOList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addAttrGroup(AttrGroupReqDTO attrGroupReqDTO) {
        int count = 0;
        count = this.count(new LambdaQueryWrapper<AttrGroupDO>()
                .eq(Objects.equals(attrGroupReqDTO.getFrom(), ModuleEntranceEnum.STORE.code()), AttrGroupDO::getStoreGuid, attrGroupReqDTO.getStoreGuid())
                .eq(AttrGroupDO::getName, attrGroupReqDTO.getName())
                .eq(Objects.equals(attrGroupReqDTO.getFrom(), ModuleEntranceEnum.STORE.code()), AttrGroupDO::getAttrGroupFrom, Integer.valueOf(0))
                .eq(Objects.equals(attrGroupReqDTO.getFrom(), ModuleEntranceEnum.BRAND.code()), AttrGroupDO::getBrandGuid, attrGroupReqDTO.getBrandGuid())
        );
        if (count > 0) {
            throw new ParameterException("该名称已存在，请修改");
        }
        AttrGroupDO attrGroupDO = MapStructUtils.INSTANCE.attrGroupReqDTO2attrGroupDO(attrGroupReqDTO);
        // 设置sort
        if (ObjectUtils.isEmpty(attrGroupDO.getSort())) {
            ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
            itemSingleDTO.setFrom(attrGroupReqDTO.getFrom());
            if (itemSingleDTO.getFrom().equals(ModuleEntranceEnum.BRAND.code())) {
                itemSingleDTO.setData(attrGroupReqDTO.getBrandGuid());
            } else {
                itemSingleDTO.setData(attrGroupReqDTO.getStoreGuid());
            }
            int lastSort = getLastSort(itemSingleDTO);
            attrGroupDO.setSort(lastSort);
        }
        attrGroupDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HSI_ATTR_GROUP));
        attrGroupDO.setAttrGroupFrom(attrGroupReqDTO.getFrom());
        return this.save(attrGroupDO);
    }

    @Override
    public int getLastSort(ItemSingleDTO itemSingleDTO) {
        return this.count(new LambdaQueryWrapper<AttrGroupDO>()
                .eq(Objects.equals(itemSingleDTO.getFrom(), ModuleEntranceEnum.STORE.code()), AttrGroupDO::getStoreGuid, itemSingleDTO.getData())
                .eq(Objects.equals(itemSingleDTO.getFrom(), ModuleEntranceEnum.BRAND.code()), AttrGroupDO::getBrandGuid, itemSingleDTO.getData())
        ) + 1;
    }

    @Override
    public Integer removePushAttrGroup(String storeGuid) {

        List<AttrGroupDO> attrGroupDOList = list(new LambdaQueryWrapper<AttrGroupDO>().eq(AttrGroupDO::getStoreGuid, storeGuid).eq(AttrGroupDO::getAttrGroupFrom, 2));
        if (CollectionUtils.isEmpty(attrGroupDOList)) {
            return 1;
        }
        List<String> attrGroupGuidList = attrGroupDOList.stream().map(AttrGroupDO::getGuid).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attrGroupGuidList)) {
            // 与商品相关联
            List<RItemAttrGroupDO> itemAttrGroupDOList = itemAttrGroupService.list(new LambdaQueryWrapper<RItemAttrGroupDO>()
                    .in(RItemAttrGroupDO::getAttrGroupGuid, attrGroupGuidList));
            // 与门店自建属性关联
            List<AttrDO> storeAttrList = attrService.list(new LambdaQueryWrapper<AttrDO>().eq(AttrDO::getStoreGuid, storeGuid));
            if (!CollectionUtils.isEmpty(itemAttrGroupDOList) || !CollectionUtils.isEmpty(storeAttrList)) {
                // 将该属性组变为门店自建属性组
                Set<String> withItemAttrGroupGuidSet = itemAttrGroupDOList.stream().map(RItemAttrGroupDO::getAttrGroupGuid).collect(Collectors.toSet());
                Set<String> withAttrAttrGroupGuidSet = storeAttrList.stream().map(AttrDO::getAttrGroupGuid).collect(Collectors.toSet());
                Set<String> noDelAttrGroupGuidList = new HashSet<>();
                if (!CollectionUtils.isEmpty(withItemAttrGroupGuidSet)) {
                    noDelAttrGroupGuidList.addAll(withItemAttrGroupGuidSet);
                }
                if (!CollectionUtils.isEmpty(withAttrAttrGroupGuidSet)) {
                    noDelAttrGroupGuidList.addAll(withAttrAttrGroupGuidSet);
                }
                updateWithItemType(attrGroupGuidList, noDelAttrGroupGuidList);
            }
            boolean remove = removeByIds(attrGroupGuidList);
            return remove ? 1 : 0;
        }
        return 0;
    }

    @Override
    public List<AttrGroupSynRespDTO> selectAttrGroupSynRespDtoByItemGuidList(Collection<String> itemGuidList) {
        if (CollectionUtils.isEmpty(itemGuidList)) {
            return new ArrayList<>();
        }
        //todo 查询商品与属性组关联关系实体集合
        List<RItemAttrGroupDO> itemAttrGroupDOList = itemAttrGroupService.list(
                new LambdaQueryWrapper<RItemAttrGroupDO>()
                        .in(RItemAttrGroupDO::getItemGuid, itemGuidList));
        if (CollectionUtils.isEmpty(itemAttrGroupDOList)) {
            return new ArrayList<>();
        }
        // todo 属性与商品属性组关联实体集合
        List<RAttrItemAttrGroupDO> attrItemAttrGroupDOList = selectAttrItemAttrGroupDOList(itemAttrGroupDOList);
        // todo 查询所有商品关联的所有属性组实体集合
        List<AttrGroupDO> attrGroupDOList = selectAttrGroupDOList(itemAttrGroupDOList);
        // todo 查询所有商品关联的所有属性实体的集合
        List<AttrDO> attrDOList = selectAttrDOList(attrItemAttrGroupDOList);
        // 属性组集合
        List<AttrGroupSynRespDTO> attrGroupList = new ArrayList<>();
        //todo 设置已选属性组和属性
        itemAttrGroupDOList.forEach(rItemAttrGroupDO -> {
            AttrGroupSynRespDTO attrGroupSynRespDTO = MapStructUtils.INSTANCE.rItemAttrGroupDO2attrGroupSynRespDTO(rItemAttrGroupDO);
            AttrGroupDO attrGroup = attrGroupDOList.stream()
                    .filter(attrGroupDO -> attrGroupDO.getGuid().equals(attrGroupSynRespDTO.getAttrGroupGuid()))
                    .findFirst().orElse(null);
            if (attrGroup != null) {
                attrGroupSynRespDTO.setName(attrGroup.getName());
                attrGroupSynRespDTO.setIconUrl(attrGroup.getIconUrl());
            }
            List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOList = attrItemAttrGroupDOList.stream()
                    .filter(rAttrItemAttrGroupDO -> rAttrItemAttrGroupDO.getItemAttrGroupGuid().equals(rItemAttrGroupDO.getGuid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(rAttrItemAttrGroupDOList)) {
                throw new ParameterException("后台配置有误，属性组下属性不能为空");
            }
            // 属性集合
            List<AttrSynRespDTO> attrList = getAttrSynRespDTOList(rAttrItemAttrGroupDOList, attrDOList);
            // 是否有有价格的属性
            attrList.forEach(o ->
                    System.out.println(JacksonUtils.writeValueAsString(o))
            );
            attrList = attrList.stream()
                    .filter(attrSynRespDTO -> attrSynRespDTO.getPrice() != null)
                    .collect(Collectors.toList());
            boolean showPrice = attrList.stream()
                    .anyMatch(attrSynRespDTO -> attrSynRespDTO.getPrice().signum() > 0);
            attrGroupSynRespDTO.setShowPrice(showPrice ? 1 : 0);
            attrGroupSynRespDTO.setAttrList(attrList);
            attrGroupList.add(attrGroupSynRespDTO);
        });
        return attrGroupList;
    }


    /**
     * 根据属性与商品属性组关联实体以及对应属性实体来获取返回给前端的属性实体
     *
     * @param rAttrItemAttrGroupDOList 属性与商品属性组关联实体
     * @param attrDOList               对应属性实体
     * @return 返回给前端的属性实体
     */
    private List<AttrSynRespDTO> getAttrSynRespDTOList(List<RAttrItemAttrGroupDO> rAttrItemAttrGroupDOList, List<AttrDO> attrDOList) {
        if (CollectionUtils.isEmpty(rAttrItemAttrGroupDOList) || CollectionUtils.isEmpty(attrDOList)) {
            return new ArrayList<>();
        }
        List<AttrSynRespDTO> attrSynRespDTOList = new ArrayList<>();
        rAttrItemAttrGroupDOList.forEach(rAttrItemAttrGroupDO -> {
            AttrSynRespDTO attrSynRespDTO = MapStructUtils.INSTANCE.attrItemAttrGroupDO2attrSynRespDTO(rAttrItemAttrGroupDO);
            AttrDO attrDO = attrDOList
                    .stream()
                    .filter(ad
                            -> ad.getGuid().equals(attrSynRespDTO.getAttrGuid()))
                    .findFirst()
                    .orElse(null);
            if (attrDO != null) {
                attrSynRespDTO.setName(attrDO.getName());
                attrSynRespDTO.setPrice(attrDO.getPrice());
            }
            attrSynRespDTOList.add(attrSynRespDTO);
        });
        return attrSynRespDTOList;
    }

    /**
     * 返回关联商品与属性组关联实体的属性实体
     *
     * @param attrItemAttrGroupDOList
     * @return
     */
    private List<AttrDO> selectAttrDOList(List<RAttrItemAttrGroupDO> attrItemAttrGroupDOList) {
        if (CollectionUtils.isEmpty(attrItemAttrGroupDOList)) {
            return new ArrayList<>();
        }
        // 涉及到的所有属性的GUID集合
        Set<String> attrGuidSet = attrItemAttrGroupDOList
                .stream()
                .map(RAttrItemAttrGroupDO::getAttrGuid)
                .collect(Collectors.toSet());
        // 涉及的所有属性实体的集合
        return (List<AttrDO>) attrService.listByIds(attrGuidSet);
    }


    /**
     * 返回商品与属性组关联的涉及到的属性组实体
     *
     * @param itemAttrGroupDOList 商品与属性组关联实体
     * @return
     */
    private List<AttrGroupDO> selectAttrGroupDOList(List<RItemAttrGroupDO> itemAttrGroupDOList) {
        if (CollectionUtils.isEmpty(itemAttrGroupDOList)) {
            return new ArrayList<>();
        }
        // 涉及的所有属性组GUID集合
        Set<String> attrGroupGuidSet = itemAttrGroupDOList
                .stream()
                .map(RItemAttrGroupDO::getAttrGroupGuid)
                .collect(Collectors.toSet());
        // 涉及的所有属性实体集合
        return (List<AttrGroupDO>) listByIds(attrGroupGuidSet);
    }

    /**
     * 根据商品与属性组的关联实体，获取对应的属性与对应关联实体的关联实体
     *
     * @param itemAttrGroupDOList
     * @return
     */
    private List<RAttrItemAttrGroupDO> selectAttrItemAttrGroupDOList(List<RItemAttrGroupDO> itemAttrGroupDOList) {
        if (CollectionUtils.isEmpty(itemAttrGroupDOList)) {
            return new ArrayList<>();
        }
        // 获取商品与属性组关联的guid
        List<String> itermAttrGroupDOGuidList = itemAttrGroupDOList
                .stream()
                .map(RItemAttrGroupDO::getGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(itermAttrGroupDOGuidList)) {
            // todo 查询属性与商品属性组关联集合
            List<RAttrItemAttrGroupDO> attrItemAttrGroupDOList = attrItemAttrGroupService.list(
                    new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                            .in(RAttrItemAttrGroupDO::getItemAttrGroupGuid, itermAttrGroupDOGuidList));
            if (CollectionUtils.isEmpty(attrItemAttrGroupDOList)) {
                log.error("商品安卓同步时发现后台配置错误：商户后台配置不正确，有商品关联了属性组但是未关联属性");
                throw new BusinessException("商户后台配置不正确，有商品关联了属性组但是未关联属性");
            }
            return attrItemAttrGroupDOList;
        }
        return new ArrayList<>();
    }

    private void updateWithItemType(List<String> attrGroupGuidList, Set<String> withItemAttrGroupGuidSet) {
        if (CollectionUtils.isEmpty(attrGroupGuidList) || CollectionUtils.isEmpty(withItemAttrGroupGuidSet)) {
            return;
        }
        // 去掉关联了商品的属性组，仅留下未关联商品的属性组
        attrGroupGuidList.removeAll(withItemAttrGroupGuidSet);
        // 门店中被推送的关联了商品的属性组
        List<AttrGroupDO> withItemAttrGroupDOList = (ArrayList) listByIds(withItemAttrGroupGuidSet);
        // 将这些分类从被推送分类修改为门店自建分类
        pushUtils.fixFieldsFromPush2SelfCreate(withItemAttrGroupDOList, AttrGroupDO::setAttrGroupFrom, AttrGroupDO::setParentGuid);
        updateBatchById(withItemAttrGroupDOList, withItemAttrGroupDOList.size());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateAttrGroup(AttrGroupUpdateReqDTO attrGroupUpdateReqDTO) {
        int count = 0;
        count = this.count(new LambdaQueryWrapper<AttrGroupDO>()
                .ne(AttrGroupDO::getGuid, attrGroupUpdateReqDTO.getAttrGroupGuid())
                .eq(Objects.equals(attrGroupUpdateReqDTO.getFrom(), ModuleEntranceEnum.STORE.code()), AttrGroupDO::getStoreGuid, attrGroupUpdateReqDTO.getStoreGuid())
                .eq(AttrGroupDO::getName, attrGroupUpdateReqDTO.getName())
                .eq(Objects.equals(attrGroupUpdateReqDTO.getFrom(), ModuleEntranceEnum.STORE.code()), AttrGroupDO::getAttrGroupFrom, 0)
                .eq(Objects.equals(attrGroupUpdateReqDTO.getFrom(), ModuleEntranceEnum.BRAND.code()), AttrGroupDO::getBrandGuid, attrGroupUpdateReqDTO.getBrandGuid())
        );
        if (count > 0) {
            throw new ParameterException("该名称已存在，请修改");
        }
        AttrGroupDO attrGroupDO = MapStructUtils.INSTANCE.attrGroupUpdateReqDTO2attrGroupDO(attrGroupUpdateReqDTO);
        List<String> defaultAttrGuidList = attrGroupUpdateReqDTO.getDefaultAttrGuidList();
        if (CollectionUtils.isNotEmpty(defaultAttrGuidList)) {
            if (attrGroupUpdateReqDTO.getIsMultiChoice() == 0 && defaultAttrGuidList.size() > 1) {
                throw new ParameterException("默认选择属性不支持多选");
            }
            attrGroupDO.setWithDefault(1);
            //更新品牌和门店的属性组
            updateBrandAndStoreAttrGroup(attrGroupDO);
            List<AttrDO> attrList = new ArrayList<>();
            defaultAttrGuidList.forEach(s -> {
                AttrDO attrDO = new AttrDO();
                attrDO.setAttrGroupGuid(attrGroupUpdateReqDTO.getAttrGroupGuid());
                attrDO.setGuid(s);
                attrDO.setIsDefault(1);
                attrList.add(attrDO);
            });
            List<AttrDO> list = attrService
                    .list(new LambdaQueryWrapper<AttrDO>()
                            .eq(AttrDO::getAttrGroupGuid, attrGroupDO.getGuid())
                            .notIn(AttrDO::getGuid, defaultAttrGuidList));
            if (CollectionUtils.isNotEmpty(list)) {
                List<AttrDO> notDefaultAttrList = list
                        .stream()
                        .map(attrDO -> attrDO.setIsDefault(0))
                        .collect(Collectors.toList());
                attrList.addAll(notDefaultAttrList);
            }
            // todo 测试，看打印的SQL
            attrService.updateBatchById(attrList, attrList.size());
        } else {
            attrGroupDO.setWithDefault(0);
            updateBrandAndStoreAttrGroup(attrGroupDO);
            List<AttrDO> list = attrService.list(new LambdaQueryWrapper<AttrDO>().eq(AttrDO::getAttrGroupGuid, attrGroupDO.getGuid()));
            if (CollectionUtils.isNotEmpty(list)) {
                List<AttrDO> updateAttrList = list
                        .stream()
                        .map(attrDO -> attrDO.setIsDefault(0))
                        .collect(Collectors.toList());
                attrService.updateBatchById(updateAttrList, updateAttrList.size());
            }

        }
        return true;
    }

    /**
     * 更新当前属性组的基本信息，
     * 以及，更新当前属性组推送到门店的属性组基本信息,
     * 不把是否必选，多选以及默认属性的配置同步给门店
     *
     * @param attrGroupDO
     */
    private void updateBrandAndStoreAttrGroup(AttrGroupDO attrGroupDO) {
        boolean update = updateById(attrGroupDO);
        if (!update) {
            throw new BusinessException(OP_FAIL);
        }
        attrGroupDO.setBrandGuid(null);
        attrGroupDO.setStoreGuid(null);
        // 不把是否必选，多选以及默认属性的配置同步给门店
        attrGroupDO.setIsRequired(null);
        attrGroupDO.setIsMultiChoice(null);
        attrGroupMapper.update(attrGroupDO, new LambdaQueryWrapper<AttrGroupDO>()
                .eq(BasePushDO::getParentGuid, attrGroupDO.getGuid()));
    }

    @Override
    @Transactional
    public boolean deleteByGuid(ItemSingleDTO itemSingleDTO) {
        // 1.删除此属性组以及其下关联的属性以及与商品的关联，若有与商品的关联，则将被推送商品与属性组的关联解除
        int flag = 0;
        // 删除属性组本身
        flag = attrGroupMapper.delete(
                new LambdaQueryWrapper<AttrGroupDO>()
                        .eq(AttrGroupDO::getGuid, itemSingleDTO.getData()));
        // 该属性关联的属性值
        List<AttrDO> theseAttrDOList = attrService.list(
                new LambdaQueryWrapper<AttrDO>()
                        .eq(AttrDO::getAttrGroupGuid, itemSingleDTO.getData()));
        if (CollectionUtils.isNotEmpty(theseAttrDOList)) {
            // 删除该属性组关联的属性
            attrService.deleteByGroup(itemSingleDTO.getData());
            // 删除该属性组或属性与商品的关联关系
            List<String> theseAttrGuidList = theseAttrDOList
                    .stream()
                    .map(AttrDO::getGuid)
                    .collect(Collectors.toList());
            // 删除这些属性与分类的关联关系
            if (CollectionUtils.isNotEmpty(theseAttrGuidList)) {
                typeAttrService.remove(
                        new LambdaQueryWrapper<RTypeAttrDO>()
                                .in(RTypeAttrDO::getAttrGuid, theseAttrGuidList));
                // 待删除的属性关联关系
                List<RAttrItemAttrGroupDO> attrRelationList = attrItemAttrGroupService.list(
                        new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                                .in(RAttrItemAttrGroupDO::getAttrGuid, theseAttrGuidList));
                // 待删除的属性关联关系
                List<RAttrItemAttrGroupDO> toDelAttrRelationDOList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(attrRelationList)) {
                    toDelAttrRelationDOList.addAll(attrRelationList);
                }
                // 被推送的属性集合
                List<AttrDO> pushAttrDOList = attrService.list(
                        new LambdaQueryWrapper<AttrDO>()
                                .in(AttrDO::getParentGuid, theseAttrGuidList));
                // 删除被推送属性与门店被推送商品的关联关系，以及未关联门店自建实体的推送实体的删除
                attrService.deletePushAttrRelate(toDelAttrRelationDOList, pushAttrDOList);
            }
        }

        // 2.判断该属性组是否被推送至门店，如果有，则 判断属性组或推送属性是否与自建商品关联，若关联，判断属性组是否与自建属性关联，则改为自建属性组或属性。

        // 门店被推送的属性组集合
        List<AttrGroupDO> pushAttrGroupDOList = list(new LambdaQueryWrapper<AttrGroupDO>().eq(AttrGroupDO::getParentGuid, itemSingleDTO.getData()));
        if (!CollectionUtils.isEmpty(pushAttrGroupDOList)) {
            // 门店被推送的属性组GUID集合
            List<String> pushAttrGroupGuidList = pushAttrGroupDOList.stream().map(AttrGroupDO::getGuid).collect(Collectors.toList());
            // 查询门店被推送属性组下的所有属性
            List<AttrDO> attrGroupAttrDOList = attrService.list(new LambdaQueryWrapper<AttrDO>().in(AttrDO::getAttrGroupGuid, pushAttrGroupGuidList));
            if (!CollectionUtils.isEmpty(attrGroupAttrDOList)) {
                // 过滤出被推送的属性
                List<AttrDO> pushAttrDOList = attrGroupAttrDOList.stream().filter(attrDO -> !StringUtils.isEmpty(attrDO.getParentGuid())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(pushAttrDOList)) {
                    attrGroupAttrDOList.removeAll(pushAttrDOList);
                    // 在删除了被推送属性与推送商品之间的关联关系后，删除未与门店自建商品关联的属性，剩下推送属性改为自建
                    attrService.deleteOrUpdatePushAttrAfterDeletePushRelation(pushAttrDOList);
                }
            }
            // 被推送属性组与门店自建商品产生的关系集合（与被推送商品的关联关系已被删除）
            List<RItemAttrGroupDO> itemAttrGroupDOS = itemAttrGroupService.list(new LambdaQueryWrapper<RItemAttrGroupDO>()
                    .in(RItemAttrGroupDO::getAttrGroupGuid, pushAttrGroupGuidList));
            if (!CollectionUtils.isEmpty(itemAttrGroupDOS)) {
                // 目前关联了商品的属性组GUID集合
                Set<String> relateItemAttrGroupGuidSet = itemAttrGroupDOS.stream().map(RItemAttrGroupDO::getAttrGroupGuid).collect(Collectors.toSet());
                // 修改属性组从被推送为自建
                changeAttrGroupFromPush2Self(relateItemAttrGroupGuidSet);
                // 过滤出未关联商品的属性组，删除
                pushAttrGroupGuidList.removeAll(relateItemAttrGroupGuidSet);
            }
            // 不删除与门店自建属性有关联的推送属性组
            if (!CollectionUtils.isEmpty(pushAttrGroupGuidList) && !CollectionUtils.isEmpty(attrGroupAttrDOList)) {
                // 关联了门店属性的属性组GUID集合
                Set<String> relateStoreAttrAttrGroupGuidSet = attrGroupAttrDOList.stream().map(AttrDO::getAttrGroupGuid).collect(Collectors.toSet());
                // 修改属性组从被推送为自建
                changeAttrGroupFromPush2Self(relateStoreAttrAttrGroupGuidSet);
                // 过滤出未关联商品的属性组，删除
                pushAttrGroupGuidList.removeAll(relateStoreAttrAttrGroupGuidSet);
            }
            // 删除被推送过来的未关联商品的属性组
            if (!CollectionUtils.isEmpty(pushAttrGroupGuidList)) {
                removeByIds(pushAttrGroupGuidList);
            }

        }



        /*// 门店的分组可以直接删除
        if (itemSingleDTO.getFrom().equals(ModuleEntranceEnum.STORE.code())) {
        } else {
            // brand的分组，需要判断门店在给分组下是否还有自建属性，若有，则将该分组改为门店自建分组，下面的属性值也改为自建属性值
            // 品牌推送到门店的属性组集合
            List<AttrGroupDO> storeAttrGroupDOList = list(new LambdaQueryWrapper<AttrGroupDO>().eq(AttrGroupDO::getParentGuid, itemSingleDTO.getData()));
            // 品牌推送到门店的属性组GUID集合
            List<String> storeAttrGroupGuidList = storeAttrGroupDOList.stream().map(AttrGroupDO::getGuid).collect(Collectors.toList());
            // 门店存在被该属性组推送的属性组
            if (!CollectionUtils.isEmpty(storeAttrGroupGuidList)){
                List<AttrDO> listStoreSelfAttr = attrService.list(new LambdaQueryWrapper<AttrDO>()
                        .in(AttrDO::getAttrGroupGuid, storeAttrGroupGuidList).eq(AttrDO::getAttrFrom, ModuleEntranceEnum.STORE.code()));
                // 关联了自建属性的属性组
                if (!ObjectUtils.isEmpty(listStoreSelfAttr)) {
                    Set<String> attrGroupGuidList = listStoreSelfAttr.stream().map(AttrDO::getAttrGroupGuid).collect(Collectors.toSet());
                    // 将有门店自建attr的attrGroup改为门店自建attrGroup
                    List<AttrGroupDO> list = this.list(new LambdaQueryWrapper<AttrGroupDO>().in(AttrGroupDO::getGuid, attrGroupGuidList));
                    list.forEach(attrGroupDO -> {
                        attrGroupDO.setParentGuid("");
                        attrGroupDO.setAttrGroupFrom(ModuleEntranceEnum.STORE.code());
                    });
                    updateBatchById(list);
                }

                // 推送属性关联了门店自建商品
            }
            // 将改为门店自建attrGroup下的attr也设置为门店自建attr
            List<AttrDO> list1 = attrService.list(new LambdaQueryWrapper<AttrDO>().eq(AttrDO::getAttrGroupGuid, itemSingleDTO.getData()));
            list1.forEach(attrDO -> {
                attrDO.setAttrFrom(ModuleEntranceEnum.STORE.code());
                attrDO.setParentGuid("");
            });
            attrService.updateBatchById(listStoreSelfAttr);

            flag = attrGroupMapper.delete(new LambdaQueryWrapper<AttrGroupDO>().eq(AttrGroupDO::getGuid, itemSingleDTO.getData()));

            // 若该属性组被推送过到门店，删除的时候需要通知门店
            List<AttrGroupDO> attrGroupDOS = attrGroupMapper.selectList(new LambdaQueryWrapper<AttrGroupDO>().eq(BasePushDO::getParentGuid, itemSingleDTO.getData()));
            if (!ObjectUtils.isEmpty(attrGroupDOS)) {
                Set<String> collect = attrGroupDOS.stream().map(BasePushDO::getStoreGuid).collect(Collectors.toSet());
                eventPushHelper.pushMsgToAndriod(new ArrayList<>(collect));
            }
        }*/


        // 删除属性组后，更新此属性组关联了的item的hasAttr字段
        changeItemHasAttrWithDeleteAttrGroup();

        return 0 != flag;
    }

    /**
     * 将指定属性组从推送改为门店自建
     *
     * @param relateItemAttrGroupGuidSet
     */
    private void changeAttrGroupFromPush2Self(Set<String> relateItemAttrGroupGuidSet) {
        if (CollectionUtils.isEmpty(relateItemAttrGroupGuidSet)) {
            return;
        }
        // 待更新的属性组集合
        List<AttrGroupDO> updateAttrGroupDOList = (ArrayList) listByIds(relateItemAttrGroupGuidSet);
        pushUtils.fixFieldsFromPush2SelfCreate(updateAttrGroupDOList, AttrGroupDO::setAttrGroupFrom, AttrGroupDO::setParentGuid);
        updateBatchById(updateAttrGroupDOList, updateAttrGroupDOList.size());
    }

    /**
     * 删除属性组时 更新此属性组关联了的item的hasAttr字段
     */
    private void changeItemHasAttrWithDeleteAttrGroup() {
        // 当前企业下所有的商品与属性组的关联关系
        List<RItemAttrGroupDO> rItemAttrGroupDOS = itemAttrGroupService.list(new LambdaQueryWrapper<RItemAttrGroupDO>());
        if (!CollectionUtils.isEmpty(rItemAttrGroupDOS)) {
            // 带属性组的商品GUID集合
            Set<String> withAttrGroupItemGuidSet = rItemAttrGroupDOS.stream().map(RItemAttrGroupDO::getItemGuid).collect(Collectors.toSet());
            // 更新无属性组商品hasAttr字段
            // todo test sql
            itemService.update(new ItemDO().setHasAttr(0), new LambdaQueryWrapper<ItemDO>().notIn(ItemDO::getGuid, withAttrGroupItemGuidSet));
            // 有必选属性的属性组关联关系集合
            List<RItemAttrGroupDO> requiredAttrGroupRelationList = rItemAttrGroupDOS.stream()
                    .filter(itemAttrGroupDO -> itemAttrGroupDO.getIsRequired() == 1)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(requiredAttrGroupRelationList)) {
                // 带必选属性的商品GUID集合
                Set<String> hasRequiredAttrItemGuidSet = requiredAttrGroupRelationList.stream().map(RItemAttrGroupDO::getItemGuid).collect(Collectors.toSet());
                // 仅剩下含非必选属性组的商品GUID集合
                withAttrGroupItemGuidSet.removeAll(hasRequiredAttrItemGuidSet);
                itemService.update(new ItemDO().setHasAttr(2), new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, hasRequiredAttrItemGuidSet));
            }
            if (!CollectionUtils.isEmpty(withAttrGroupItemGuidSet)) {
                itemService.update(new ItemDO().setHasAttr(1), new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, withAttrGroupItemGuidSet));
            }
        } else {
            // 更新无属性组商品hasAttr字段
            itemService.update(new ItemDO().setHasAttr(0), new LambdaQueryWrapper<ItemDO>());
        }
    }
}
