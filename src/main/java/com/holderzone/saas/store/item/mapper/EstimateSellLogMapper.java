package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.item.entity.domain.EstimateSellLogDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <p>
 * 商品售卖状态记录 Mapper 接口
 * </p>
 */
@Component
public interface EstimateSellLogMapper extends BaseMapper<EstimateSellLogDO> {

    List<EstimateSellLogDO> listByStoreGuidAndSkuGuidsLimit1(@Param("storeGuid") String storeGuid, @Param("skuGuids") List<String> skuGuids);

}
