package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.TypeReqDTO;
import com.holderzone.saas.store.dto.item.req.TypeSortReqDTO;
import com.holderzone.saas.store.dto.item.req.TypeSortSwitchReqDTO;
import com.holderzone.saas.store.dto.item.resp.JournalingItemRespDTO;
import com.holderzone.saas.store.dto.item.resp.SaleTypeRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeSynRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeWebRespDTO;
import com.holderzone.saas.store.item.entity.domain.TypeDO;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 商品分类 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
public interface ITypeService extends IService<TypeDO> {

    Integer getSort(ItemSingleDTO itemSingleDTO);

    Integer saveType(TypeReqDTO typeReqDTO);

    Integer updateType(TypeReqDTO typeReqDTO);

    List<TypeWebRespDTO> queryType(ItemSingleDTO itemSingleDTO);

    /**
     * 根据门店guidList批量获取分类信息
     *
     * @param itemStringListDTO
     * @return
     */
    List<TypeWebRespDTO> queryTypeByStoreGuidList(ItemStringListDTO itemStringListDTO);

    Integer deleteType(ItemSingleDTO itemSingleDTO);

    /**
     * 移除指定门店下被推送的分类，如果有分类下关联了商品，则将分类转为门店自建分类
     *
     * @param storeGuid
     * @return
     */
    Integer removePushType(String storeGuid);

    /**
     * 报表大屏商品分类支持->查询企业下门店所有商品分类（包含推送分类）
     *
     * @return
     */
    List<JournalingItemRespDTO> queryJournalingItemType();

    /**
     * 商超批量更新分类排序
     *
     * @param typeList
     * @return
     */
    Boolean retailUpdateTypesort(List<TypeSortReqDTO> typeList);

    /**
     * mdm 删除分类 （包含分类和属性的关系，分类下的商品及其规格）
     *
     * @param request
     */
    void mdmRemoveType(SingleDataDTO request);

    /**
     * 设置团餐当前状态
     *
     * @param request
     * @return
     */
    Boolean setGroupMealStatus(SingleDataDTO request);

    /**
     * 查询团餐当前状态
     *
     * @param storeGuid
     * @return
     */
    String selectGroupMealStatus(String storeGuid);

    /**
     * 交换两个分类的排序
     *
     * @param typeReqDTO
     * @return
     */
    Boolean switchSort(@RequestBody @Valid TypeSortSwitchReqDTO typeReqDTO);

    /**
     * 批量修改分类顺序
     *
     * @param typeReqDTOList 里面只有sort和typeGuid
     * @return 1
     */
    Integer batchModifySort(List<TypeReqDTO> typeReqDTOList);

    /**
     * 通过价格方案查询分类列表
     *
     * @param itemSingleDTO 价格方案guid
     * @return 分类列表
     */
    List<TypeWebRespDTO> queryTypeByPlan(ItemSingleDTO itemSingleDTO);

    /***
     *  新增菜谱方案type
     * @param typeReqDTO
     * @return 返回值为type Guid
     */
    String saveTypePlan(TypeReqDTO typeReqDTO);

    /**
     * 修改菜谱方案分类信息
     *
     * @param planGuid   菜谱方案Guid
     * @param parentGuid 原菜谱方案Guid
     */
    void updateTypePlan(String planGuid, String parentGuid);

    List<TypeWebRespDTO> querySourceTypeInfo(ItemStringListDTO listDTO);

    /**
     * 查询品牌列表下的所有分类
     */
    List<TypeWebRespDTO> queryTypeByBrand(ItemStringListDTO query);

    /**
     * 通过门店查询门店商品在品牌库所属的分类
     */
    List<SaleTypeRespDTO> queryBrandTypeByStore(ItemSingleDTO query);
}
