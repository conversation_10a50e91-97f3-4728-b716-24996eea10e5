package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.ItemTemplateMenuTimeReqDTO;
import com.holderzone.saas.store.dto.item.req.ItmeTemplateMenuValidityReqDTO;
import com.holderzone.saas.store.item.constant.Constant;
import com.holderzone.saas.store.item.entity.domain.ItemTMenuValidityDO;
import com.holderzone.saas.store.item.entity.query.ItemTemplateExecuteTimeQuery;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.mapper.ItemTMenuValidityMapper;
import com.holderzone.saas.store.item.service.IItemTMenuValidityService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.MapStructUtils;
import com.holderzone.sdk.util.BatchIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 商品模拟-菜单-执行有效期 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
@Service
public class ItemTMenuValidityServiceImpl extends ServiceImpl<ItemTMenuValidityMapper, ItemTMenuValidityDO> implements IItemTMenuValidityService {

    private final ItemTMenuValidityMapper itemTMenuValidityMapper;
    private final DynamicHelper dynamicHelper;
    private final  ItemHelper itemHelper;
    private final RedisTemplate redisTemplate;

    @Autowired
    public ItemTMenuValidityServiceImpl(ItemTMenuValidityMapper itemTMenuValidityMapper, DynamicHelper dynamicHelper, @Lazy ItemHelper itemHelper, RedisTemplate redisTemplate) {
        this.itemTMenuValidityMapper = itemTMenuValidityMapper;
        this.dynamicHelper = dynamicHelper;
        this.itemHelper = itemHelper;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public List<ItemTMenuValidityDO> getMenuValiditys(String guid, Integer periodicMode, String menuGuid) {
        return itemTMenuValidityMapper.getMenuValiditys(guid,periodicMode,menuGuid);
    }

    @Override
    public Boolean saveItemMenuValidity(ItmeTemplateMenuValidityReqDTO request,Integer periodicMode,String menuGuid,Integer isItFullTime) {
        ItemTMenuValidityDO itemTMenuValidityDO = MapStructUtils.INSTANCE.itemTemplateMenuValidityReqDTO2itmeTMenuValidityDO(request);
        if(!Optional.ofNullable(request).map(ItmeTemplateMenuValidityReqDTO::getGuid).isPresent()){
            if(StringUtils.isEmpty(itemTMenuValidityDO)){
                itemTMenuValidityDO = new ItemTMenuValidityDO();
            }
            try {
                itemTMenuValidityDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
            } catch (IOException e) {
                throw new BusinessException(Constant.CREATE_GUID_FAIL);
            }
        }
            //按时段 1 按星期 2 时段+周期 3
           if(periodicMode == 1) {
               if(isItFullTime.equals(2)) {
                   List<ItemTemplateMenuTimeReqDTO> times = Arrays.asList(ItemTemplateMenuTimeReqDTO.builder().startTime("00:00").endTime("23:59").build());
                   request =  ItmeTemplateMenuValidityReqDTO.builder().times(times).build();
               }
               itemTMenuValidityDO.setIsItFullTime(isItFullTime);
               itemTMenuValidityDO.setTimes(JacksonUtils.writeValueAsString(request.getTimes()));
               itemTMenuValidityDO.setTimesQuantum(request.getTimes().size());
               itemTMenuValidityDO.setPeriodicMode(1);
           }else  if(periodicMode == 2 ) {
               List<Integer> weeks =request.getWeeks();
               Integer[] intWeeks = weeks
                       .stream()
                       .toArray(Integer[]::new);
               String strWeeks = Arrays.toString(intWeeks);
               itemTMenuValidityDO.setWeeks(strWeeks);
               itemTMenuValidityDO.setPeriodicMode(2);
               itemTMenuValidityDO.setWeeksQuantum(request.getWeeks().size());
           }
          itemTMenuValidityDO.setItemMenuGuid(menuGuid);
        return  saveOrUpdate(itemTMenuValidityDO);
    }



    @Override
    public List<Long> getTimeAndTypeForSyn(SingleDataDTO request) {
        request.setData(UserContextUtils.getStoreGuid());
        List<ItemTemplateExecuteTimeQuery> list =  itemTMenuValidityMapper.getTimeAndTypeForSyn(request);
       return itemHelper.templateExecuteHander(list);
    }

    @Override
    public List<ItemTemplateExecuteTimeQuery> getNowMenuExecuteTimes(String storeGuid) {
       return   itemTMenuValidityMapper.getNowMenuExecuteTimes(storeGuid);
    }

    @Override
    public Boolean removeMenuTime(String menuGuid) {
        return remove(new LambdaQueryWrapper<ItemTMenuValidityDO>().eq(ItemTMenuValidityDO::getItemMenuGuid,menuGuid));
    }
}
