package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.ItemTMenuReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemTemplateMenuSubitemReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenusRespDTO;
import com.holderzone.saas.store.item.entity.domain.ItemTMenuDO;

import java.util.List;

/**
 * <p>
 * 商品模板-菜单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
public interface IItemTMenuService extends IService<ItemTMenuDO> {

    /**
     * 保存和更新商品模板-菜单
     * @param request
     * @return
     */
    Boolean saveOrUpdate(ItemTMenuReqDTO request);

    /**
     * 保存商品模板-菜单及其菜品子项
     * @param request
     * @return
     */
    Boolean saveItemMenu(ItemTemplateMenuSubitemReqDTO request);

    /**
     * 根据商品模板guid查询菜单列表
     * @param request
     * @return
     */
    List<ItemTemplateMenusRespDTO> getItemTemplateMenus(SingleDataDTO request);
}
