package com.holderzone.saas.store.item.entity.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SubItemSkuBO
 * @date 2019/01/03 下午3:41
 * @description //套餐分组下的子商品实体
 * @program holder-saas-store-dto
 */
@Data
@ApiModel
public class SubItemSkuBO {

    @ApiModelProperty(value = "套餐分组的唯一标识",required = true)
    private String subgroupGuid;

    @ApiModelProperty(value = "sku与分组关联实体GUID")
    private String skuSubgroupGuid;

    @ApiModelProperty(value = "规格Guid",required = true)
    private String skuGuid;

    @ApiModelProperty(value = "商品及规格组合名称")
    private String name;

    @ApiModelProperty(value = "规格的商品GUID",required = true)
    private String itemGuid;

    @ApiModelProperty(value = "商品类型(注：套餐子商品中不包括套餐)：1.套餐（不称重，无规格），2.规格商品（单商品，不称重），3.称重商品（单商品，称重），4.单品。",required = true)
    private Integer itemType;

    @ApiModelProperty(value = "商品单位",required = true)
    private String unit;

    @ApiModelProperty(value = "商品元素中的数量",required = true)
    private BigDecimal itemNum;

    @ApiModelProperty(value = "商品加价",required = true)
    private BigDecimal addPrice;

    @ApiModelProperty(value = "属性组状态:0：无属性; 1:有属性; 2:有必选属性组",required = true)
    private Integer hasAttr;

    @ApiModelProperty(value = "是否默认勾选，1：是，0,否",required = true)
    private Integer isDefault;

    @ApiModelProperty(value = "是否可重复选择，0:否,1:是",required = true)
    private Integer isRepeat;

    @ApiModelProperty(value = "排序")
    private Integer sort;
}
