package com.holderzone.saas.store.item.util;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PushUtils
 * @date 2019/03/12 下午4:48
 * @description //TODO
 * @program holder-saas-store-item
 */
@Component
public class PushUtils {
    /**
     * 将实体从被推送修改为门店自建
     *
     */
    public  <T> void fixFieldsFromPush2SelfCreate(List<T> withItemTypeDOList, BiConsumer<T, Integer> con1, BiConsumer<T, String> con2) {
        withItemTypeDOList.forEach(typeDO -> {
            con1.accept(typeDO, 0);
            con2.accept(typeDO, null);
        });
    }

}
