package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.item.req.ItemTemplateReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemTemplateSearchReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenuSubItemDetailRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplatesRespDTO;
import com.holderzone.saas.store.item.entity.domain.ItemTemplateDO;

import java.util.List;

/**
 * <p>
 * 商品模板 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
public interface IItemTemplateService extends IService<ItemTemplateDO> {

    /**
     *  保存 、 更新 商品模板
     * @param request
     * @return
     */
    Boolean saveOrUpdate(ItemTemplateReqDTO request);

    /**
     * 获取门店销售模板
     * @param request
     * @return
     */
    ItemTemplatesRespDTO getStoreItemTemplates(ItemTemplateSearchReqDTO request);


    /**
     * 根据传入guid获取门店guid
     * @param type 类型 0：模板guid 1：菜单guid
     * @param guid
     * @return
     */
    String getStoreGuidByCode(Integer type, String guid);

    /**
     * 根据门店获取到当前时间生效模板的菜单
     * @param storeGuid
     * @return
     */
    List<ItemTemplateMenuSubItemDetailRespDTO> getNowMeunSubItemForSyn(String storeGuid);
}
