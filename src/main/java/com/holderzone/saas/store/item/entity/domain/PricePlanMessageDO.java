package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 菜谱方案消息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-3-29
 */
@Data
@Accessors(chain = true)
@TableName("hsi_price_plan_message")
public class PricePlanMessageDO {

    private Long id;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 内容
     */
    private String message;

}
