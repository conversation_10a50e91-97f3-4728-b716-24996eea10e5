package com.holderzone.saas.store.item.entity.enums;

public enum ItemTypeEnum {
    //商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品 5.团餐。
    PACKAGE(1,"套餐"),
    MULTI_SPEC(2,"多规格商品"),
    WEIGHING(3,"称重商品"),
    SINGLE(4,"单品"),
    GROUP(5,"团餐");

    ItemTypeEnum(int i) {
    }
    private int typeCode;

    private String typeName;

    ItemTypeEnum(int typeCode, String typeName) {
        this.typeCode = typeCode;
        this.typeName = typeName;
    }

    public int getTypeCode() {
        return typeCode;
    }

    public String getTypeName() {
        return typeName;
    }
}
