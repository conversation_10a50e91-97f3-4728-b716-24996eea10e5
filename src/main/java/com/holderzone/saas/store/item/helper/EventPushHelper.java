package com.holderzone.saas.store.item.helper;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.resp.PricePlanNowDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.item.entity.enums.SalesModelEnum;
import com.holderzone.saas.store.item.service.IPricePlanStoreService;
import com.holderzone.saas.store.item.service.rpc.BusinessMessageService;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.holderzone.saas.store.item.constant.Constant.MSG_TO_ANDROID_SUBJECT;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LogHelper
 * @date 2018/12/20 上午9:58
 * @description //TODO
 * @program holder-saas-store-item
 */
@Component
@Slf4j
public class EventPushHelper {

    @Autowired
    private BusinessMessageService businessMessageService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private IPricePlanStoreService pricePlanStoreService;

    /**
     * 若有商品信息变更(非菜谱模块)，则推送消息给指定门店的安卓端设备
     *
     * @param storeGuidList storeGuidList
     */
    public void preVerificationPushMsgToAndriod(List<String> storeGuidList) {
        if (CollectionUtils.isEmpty(storeGuidList)) {
            log.error("preVerificationPushMsgToAndriod推送消息至安卓端选择的门店为空");
            throw new ParameterException("门店不能为空");
        }
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuidList.get(0));
        log.info("preVerificationPushMsgToAndriod 菜品模式数据：{}", JacksonUtils.writeValueAsString(brandDTO));
        if (brandDTO.getSalesModel() == SalesModelEnum.RECIPE_MODE.getCode()) {
            LocalDateTime nowDateTime = LocalDateTime.now();
            List<PricePlanNowDTO> planNowList = pricePlanStoreService.findPlanNowStoreList(nowDateTime, storeGuidList);

            log.info("preVerificationPushMsgToAndriod 菜品方案查询结果：{}", JacksonUtils.writeValueAsString(planNowList));

            // 没有生效的菜谱
            if (CollectionUtil.isEmpty(planNowList)) {
                log.info("菜谱提示异常日志跟踪7}");
                pushMsgToAndriod(storeGuidList);
            }

            return;
        }

        log.info("菜谱提示异常日志跟踪8}");
        pushMsgToAndriod(storeGuidList);
    }

    /**
     * 若有商品信息变更，则推送消息给指定门店的安卓端设备
     *
     * @param storeGuidList storeGuidList
     */
    public void pushMsgToAndriod(List<String> storeGuidList) {
        if (CollectionUtils.isEmpty(storeGuidList)) {
            log.error("推送消息至安卓端选择的门店为空");
            throw new ParameterException("门店不能为空");
        }
        List<BusinessMessageDTO> msgDTOList = new ArrayList<>();
        for (String storeGuid : storeGuidList) {
            BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
            businessMessageDTO.setMessageType(BusinessMsgTypeEnum.ITEM_STATE_MSG_TYPE.getId());
            businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.ITEM_CHANGED.getId());
            businessMessageDTO.setPlatform("2");
            businessMessageDTO.setStoreGuid(storeGuid);
            businessMessageDTO.setSubject(MSG_TO_ANDROID_SUBJECT);
            businessMessageDTO.setContent("门店商品已更新，请重新登录当前设备同步信息");
            msgDTOList.add(businessMessageDTO);
        }
        if (msgDTOList.size() == 1) {
            businessMessageService.msg(msgDTOList.get(0));
        } else {
            businessMessageService.allMsg(msgDTOList);
        }
    }

    /**
     * 估清推送消息到安卓刷新商品
     *
     * @param storeGuid 门店guid
     */
    public void soldOutPushMsgToAndriod(String storeGuid) {
        if (ObjectUtils.isEmpty(storeGuid)) {
            log.error("估清推送消息到安卓端选择的门店为空");
            throw new ParameterException("门店不能为空");
        }
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageType(BusinessMsgTypeEnum.ITEM_STATE_MSG_TYPE.getId());
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.PAD_SOLD_OUT_MESSAGE.getId());
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid(storeGuid);
        businessMessageDTO.setSubject(MSG_TO_ANDROID_SUBJECT);
        businessMessageDTO.setContent("门店商品已更新，请重新登录当前设备同步信息");
        businessMessageService.msg(businessMessageDTO);
    }

    /**
     * 商品模板执行时间切换推送消息到android端
     *
     * @param storeGuid
     */
    public void itemTempLateTimeChangedSendMsgToAndriod(String storeGuid, String content) {
        log.info("push msg 推送销售模板变更消息至移动端 content={}", JacksonUtils.writeValueAsString(content));
        BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
        businessMessageDTO.setMessageType(BusinessMsgTypeEnum.ITEM_TEMPLATE_CHANGED_MSG_TYPE.getId());
        businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.ITEM_TEMPLATE_TIME_CHANGDE.getId());
        businessMessageDTO.setPlatform("2");
        businessMessageDTO.setStoreGuid(storeGuid);
        businessMessageDTO.setSubject(MSG_TO_ANDROID_SUBJECT);
        businessMessageDTO.setContent(content);
        businessMessageService.msg(businessMessageDTO);
    }

    /**
     * 菜谱方案切换推送消息到android端
     *
     * @param storeGuidList     storeGuidList
     * @param content           content
     * @param detailMessageType detailMessageType
     */
    public void pricePlanChangedSendMsgToAndriod(List<String> storeGuidList, String content, Integer detailMessageType) {
        log.info("push msg 菜谱方案切换推送消息到android端 content={}, {}", content, storeGuidList.toString());

        List<BusinessMessageDTO> msgDTOList = new ArrayList<>();
        for (String storeGuid : storeGuidList) {
            BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
            businessMessageDTO.setMessageType(BusinessMsgTypeEnum.BUSINESS_MSG_TYPE.getId());
            businessMessageDTO.setDetailMessageType(detailMessageType);
            businessMessageDTO.setPlatform("2");
            businessMessageDTO.setStoreGuid(storeGuid);
            businessMessageDTO.setSubject(MSG_TO_ANDROID_SUBJECT);
            businessMessageDTO.setContent(content);
            msgDTOList.add(businessMessageDTO);
        }

        if (msgDTOList.size() == 1) {
            businessMessageService.msg(msgDTOList.get(0));
        } else {
            businessMessageService.allMsg(msgDTOList);
        }
    }

    /**
     * 若有商品信息变更，则推送消息给指定门店的安卓端设备
     *
     * @param storeGuidList
     */
    public void pushMsgToAndriod(List<String> storeGuidList, String content) {
        if (CollectionUtils.isEmpty(storeGuidList)) {
            log.error("推送消息至安卓端选择的门店为空");
            throw new ParameterException("门店不能为空");
        }
        List<BusinessMessageDTO> msgDTOList = new ArrayList<>();
        for (String storeGuid : storeGuidList) {
            BusinessMessageDTO businessMessageDTO = new BusinessMessageDTO();
            businessMessageDTO.setMessageType(BusinessMsgTypeEnum.ITEM_STATE_MSG_TYPE.getId());
            businessMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.ITEM_CHANGED.getId());
            businessMessageDTO.setPlatform("2");
            businessMessageDTO.setStoreGuid(storeGuid);
            businessMessageDTO.setSubject(MSG_TO_ANDROID_SUBJECT);
            businessMessageDTO.setContent(content);
            msgDTOList.add(businessMessageDTO);
        }
        if (msgDTOList.size() == 1) {
            businessMessageService.msg(msgDTOList.get(0));
        } else {
            businessMessageService.allMsg(msgDTOList);
        }
    }
}
