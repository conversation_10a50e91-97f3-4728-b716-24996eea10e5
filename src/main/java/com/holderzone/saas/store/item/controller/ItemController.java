package com.holderzone.saas.store.item.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.CreateRepertoryReqDTO;
import com.holderzone.saas.store.dto.item.common.*;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.print.PrintItemReqDto;
import com.holderzone.saas.store.dto.takeaway.ErpMappingType;
import com.holderzone.saas.store.dto.weixin.resp.ItemImgDTO;
import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import com.holderzone.saas.store.item.entity.enums.SalesModelEnum;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.manage.ItemManage;
import com.holderzone.saas.store.item.service.IItemService;
import com.holderzone.saas.store.item.service.ISkuService;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.MapStructUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

import static com.holderzone.saas.store.item.constant.Constant.WRONG_PARAMS;

@Slf4j
@RestController
@RequestMapping("/item")
@Api(value = "商品接口")
public class ItemController {

    private final IItemService itemService;
    private final ISkuService skuService;
    private final ItemHelper itemHelper;
    private final DynamicHelper dynamicHelper;
    private final OrganizationService organizationService;

    private final ItemManage itemManage;

    @Autowired
    public ItemController(IItemService itemService, ISkuService skuService, ItemHelper itemHelper, DynamicHelper dynamicHelper,
                          OrganizationService organizationService, ItemManage itemManage) {
        this.itemService = itemService;
        this.skuService = skuService;
        this.itemHelper = itemHelper;
        this.dynamicHelper = dynamicHelper;
        this.organizationService = organizationService;
        this.itemManage = itemManage;
    }


    @ApiOperation(value = "获取商品信息", notes = "获取商品信息")
    @GetMapping("/getItemInfo")
    public ItemInfoRespDTO getItemInfo(@RequestParam String itemGuid) {
        log.info("商品getItemInfo入参,itemReqDTO={}", itemGuid);

        ItemDO itemDO = itemService.getById(itemGuid);
        if (Objects.isNull(itemDO)) {
            return new ItemInfoRespDTO();
        }
        ItemInfoRespDTO itemInfoRespDTO = new ItemInfoRespDTO();
        itemInfoRespDTO.setItemGuid(itemDO.getGuid());
        itemInfoRespDTO.setParentGuid(itemDO.getParentGuid());

        return itemInfoRespDTO;
    }

    @ApiOperation(value = "获取商品信息", notes = "获取商品信息")
    @GetMapping("/getItemInfoList2")
    public List<String> getItemInfoList2(@RequestParam List<String> itemGuidList) {
        log.info("商品getItemInfo入参,itemReqDTO={}", itemGuidList);
        return itemService.getItemInfoList2(itemGuidList);
    }

    @ApiOperation(value = "获取商品信息", notes = "获取商品信息")
    @PostMapping("/getItemInfoList3")
    public List<String> getItemInfoList3(@RequestBody ItemStringListDTO stringListDTO) {
        log.info("商品getItemInfo入参,stringListDTO={}", JacksonUtils.writeValueAsString(stringListDTO));
        return itemService.getItemInfoList2(stringListDTO.getDataList());
    }

    @ApiOperation(value = "获取商品信息", notes = "获取商品信息")
    @GetMapping("/getItemSku")
    public SkuInfoRespDTO getItemSku(@RequestParam String skuGuid) {
        log.info("商品getItemSku入参,itemReqDTO={}", skuGuid);

        SkuDO skuDO = skuService.getById(skuGuid);
        if (Objects.isNull(skuDO)) {
            return new SkuInfoRespDTO();
        }
        SkuInfoRespDTO skuInfoRespDTO = new SkuInfoRespDTO();
        skuInfoRespDTO.setSkuGuid(skuDO.getGuid());
        skuInfoRespDTO.setParentGuid(skuDO.getParentGuid());

        return skuInfoRespDTO;
    }

    @ApiOperation(value = "保存商品", notes = " result 1:success  2:fail  3:save item success , allot store fail.")
    @PostMapping("/save_item")
    public String saveItem(@RequestBody @Valid ItemReqDTO itemReqDTO) {
        log.info("商品新增接口入参,itemReqDTO={}", JacksonUtils.writeValueAsString(itemReqDTO));
        itemReqDTO = itemHelper.change(itemReqDTO);
        ItemSaveRespDTO itemSaveRespDTO = itemService.saveItem(itemReqDTO);
        // 推送放在外面，避免属性推送有问题
        if (!org.apache.commons.collections.CollectionUtils.isEmpty(itemReqDTO.getDataList())) {
            Integer push = itemHelper.distributeItems2Stores(itemReqDTO, itemSaveRespDTO.getInsertSkuGuidList(), true);
            if (push != 1) {
                throw new BusinessException("推送到商品到门店失败");
            }
        }
        return itemSaveRespDTO.getItemGuid();
    }


    @ApiOperation(value = "更新商品", notes = "更新商品")
    @PostMapping("/update_item")
    public Integer updateItem(@RequestBody @Valid ItemReqDTO itemReqDTO) {
        log.info("商品update接口入参,itemReqDTO={}", JacksonUtils.writeValueAsString(itemReqDTO));
        itemReqDTO = itemHelper.change(itemReqDTO);
        if (StringUtils.isEmpty(itemReqDTO.getItemGuid())) {
            throw new ParameterException(WRONG_PARAMS);
        }
        // 入库的规格GUID集合
        List<String> saveOrUpdateSkuGuidList = itemService.updateItem(itemReqDTO);
        //门店操作
        if (!CollectionUtils.isEmpty(itemReqDTO.getDataList())) {
            // 推送
            Integer push = itemHelper.distributeItems2Stores(itemReqDTO, saveOrUpdateSkuGuidList, true);
            return Integer.valueOf(1).equals(push) ? 1 : 3;
        } else {
            //门店商品功能下编辑没有品牌，不走这里（update_item、update_pkg接口要看）
            if (!ObjectUtils.isEmpty(itemReqDTO.getBrandGuid())) {
                BrandDTO brandDTO = organizationService.queryBrandByGuid(itemReqDTO.getBrandGuid());
                if (Objects.nonNull(brandDTO) && Objects.nonNull(brandDTO.getSalesModel()) &&
                        Objects.equals(SalesModelEnum.NORMAL_MODE.getCode(), brandDTO.getSalesModel())) {
                    itemService.removeAllRelationStore(itemReqDTO, saveOrUpdateSkuGuidList, itemReqDTO.getItemGuid(),
                            itemReqDTO.getBrandGuid());
                }
            }
        }
        return 1;
    }

    @ApiOperation(value = "更新商品pad图片", notes = "更新商品pad图片")
    @PostMapping("/editPadPicture")
    public Integer editPadPicture(@RequestBody @Valid PadPictureDTO padPictureDTO) {
        log.info("更新商品pad图片接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(padPictureDTO));
        return itemService.editPadPicture(padPictureDTO);
    }

    @ApiOperation(value = "删除商品", notes = "删除商品")
    @PostMapping("/delete_item")
    public Integer deleteItem(@RequestBody @Valid ItemSingleDTO itemSingleDTO) {
        log.info("商品delete接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        ItemStringListDTO itemStringListDTO = MapStructUtils.INSTANCE.itemSingleDTO2itemStringListDTO(itemSingleDTO);
        itemStringListDTO.setDataList(Arrays.asList(itemSingleDTO.getData()));
        // 校验积分
        return itemService.batchDelete(itemStringListDTO, Boolean.TRUE);
    }

    @PostMapping("/query_for_synchronize")
    @ApiOperation(value = "查询门店全部商品", notes = "查询门店全部商品")
//    @RateLimit(limitType = "synchronizeItemAndType",limitCount = 100)
    public ItemAndTypeForAndroidRespDTO selectItemAndTypeForSyn(@RequestBody ItemQueryListReq itemQueryListReq) {
        log.info("正餐商品同步接口入参,baseDTO={}", JacksonUtils.writeValueAsString(itemQueryListReq));
        long startTime = System.currentTimeMillis();
        if (StringUtils.isEmpty(itemQueryListReq.getStoreGuid())) {
            throw new ParameterException("门店唯一标识不能为空");
        }
        ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = itemService.selectItemDetailAndTypeForSyn(itemQueryListReq);
        log.warn("程序运行时间： {}", (System.currentTimeMillis() - startTime) + "ms");
        return itemAndTypeForAndroidRespDTO;
    }

    @PostMapping("/wx_search")
    @ApiOperation(value = "赚餐扫码点餐关键字查询商品", notes = "赚餐扫码点餐关键字查询商品")
    public Page<ItemSynRespDTO> wxSearchItems(@RequestBody WxSearchItemDto wxSearchItemDto) {
        log.info("赚餐扫描点餐-关键字搜索商品入参:{}", JacksonUtils.writeValueAsString(wxSearchItemDto));
        return itemService.wxSearchItems(wxSearchItemDto);
    }

    @ApiOperation(value = "pad商品图片列表查询")
    @PostMapping("/queryPadPicture")
    public Page<PadPictureRespDTO> queryPadPicture(@RequestBody @Valid PadPictureDTO padPictureDTO) {
        log.info("pad商品图片列表查询 接口入参,padPictureDTO={}", JacksonUtils.writeValueAsString(padPictureDTO));
        return itemService.queryPadPicture(padPictureDTO);
    }

    @ApiOperation(value = "商品列表")
    @PostMapping("/select_item_for_web")
    public Page<ItemWebRespDTO> selectItemForWeb(@RequestBody @Valid ItemQueryReqDTO itemQueryReqDTO) {
        log.info("商品列表接口入参,itemQueryReqDTO={}", JacksonUtils.writeValueAsString(itemQueryReqDTO));
        return itemService.selectItemListForWeb(itemQueryReqDTO);
    }

    @ApiOperation(value = "商品详情", notes = "商品详情")
    @PostMapping("/get_item_info")
    public ItemInfoRespDTO selectItemInfo(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("获取一个商品详情接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return itemService.getItemInfo(itemSingleDTO);
    }

    @ApiOperation(value = "根据商品guid查询商品详情以及估清信息", notes = "商品详情")
    @PostMapping("/get_item_info_estimate")
    public ItemInfoEstimateSingleDTO getItemInfoEstimate(@RequestBody ItemQueryListReq itemSingleDTO) {
        log.info("根据商品guid查询商品详情以及估清信息,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return itemManage.getItemInfoEstimate(itemSingleDTO);
    }

    @ApiOperation(value = "获取商品详情列表", notes = "获取商品详情列表")
    @PostMapping("/get_item_info_list")
    public List<ItemInfoRespDTO> selectItemInfoList(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("获取商品详情列表接口入参,商品guidList-itemSingleDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        if (CollectionUtils.isEmpty(itemStringListDTO.getDataList())) {
            log.info("根据商品guid列表查询商品信息：商品guid列表为空！");
            return new ArrayList<>();
        }
        return itemService.getItemInfoList(itemStringListDTO.getDataList(), false);
    }

    @ApiOperation(value = "获取商品列表 - 已删除商品也需查询", notes = "获取商品列表 - 已删除商品也需查询")
    @PostMapping("/get_items")
    public List<ItemInfoRespDTO> selectItems(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("订单查询商品详情列表接口入参,商品guidList-itemSingleDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        if (CollectionUtils.isEmpty(itemStringListDTO.getDataList())) {
            log.info("根据商品guid列表查询商品信息：商品guid列表为空！");
            return new ArrayList<>();
        }
        return itemService.getItemInfoList(itemStringListDTO.getDataList(), true);
    }

    @ApiOperation(value = "获取商品名称")
    @PostMapping("/get_item_name_list")
    public List<ItemInfoRespDTO> getItemNameList(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("获取商品名称,itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        return itemService.getItemNameList(itemStringListDTO);
    }

    @ApiOperation(value = "批量获取门店下商品详情及分类信息", notes = "批量获取门店下商品详情及分类信息")
    @PostMapping("/batch_get_item_info_type_list")
    public ItemInfoAndTypeRespDTO batchGetItemInfoAndTypeList(@RequestBody ItemStringListDTO itemStringListDTO) {
        if (CollectionUtils.isEmpty(itemStringListDTO.getDataList())) {
            log.info("查询门店下商品详情及分类信息：门店列表为空！");
            return new ItemInfoAndTypeRespDTO();
        }
        log.info("批量获取门店下商品详情及分类信息接口入参：门店guidList-itemSingleDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        return itemService.listItemInfoAndType(itemStringListDTO);
    }

    @PostMapping("/get_export_item_list")
    public List<ItemExportRespDTO> getExportItemList(@RequestBody ItemExportReqDTO itemExportReqDTO) {
        log.info("获取导出菜品数据入参:{}", JacksonUtils.writeValueAsString(itemExportReqDTO));
        return itemService.getExportItemList(itemExportReqDTO);
    }


    @PostMapping("/download_data")
    public List<ItemExcelTemplateRespDTO> getDownloadItemData(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("获取导出菜品数据入参:{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return skuService.listDownloadData(itemSingleDTO);
    }

    @ApiOperation(value = "获取品牌商品及分类", notes = "获取品牌商品及分类 data：brandGuid Result：true:count>0,即品牌下有商品分类或商品;false:count=0，即品牌下无商品分类或商品")
    @PostMapping("/count_type_or_item")
    public boolean countTypeOrItem(@RequestParam String brandGuid) {
        return itemService.countTypeOrItem(brandGuid);
    }

    @ApiOperation(value = "更新商品排序", notes = "更新商品排序")
    @PostMapping("update_item_sort")
    public boolean updateItemSort(@RequestBody @Valid ItemSortUpdateReqDTO itemSortUpdateReqDTO) {

        log.info("更新菜品排序入参:{}", JacksonUtils.writeValueAsString(itemSortUpdateReqDTO));
        return itemService.updateItemSort(itemSortUpdateReqDTO);
    }


    @ApiOperation(value = "推送商品", notes = "推送商品")
    @PostMapping("push_item")
    public Integer pushItem(@RequestBody @Valid PushItemReqDTO pushItemReqDTO) {
        log.info("推送商品入参:{}", JacksonUtils.writeValueAsString(pushItemReqDTO));
        return itemService.pushItems(pushItemReqDTO, false);
    }

    @ApiOperation(value = "批量导入商品", notes = "批量导入商品")
    @PostMapping("/batch_import")
    public int batchImport(@RequestBody ItemDoubleParamDTO<List<ItemExcelTemplateReqDTO>, String> itemDoubleParamDTO) {
        log.info("商品批量导入入参：{}", JacksonUtils.writeValueAsString(itemDoubleParamDTO));
        if (ObjectUtils.isEmpty(itemDoubleParamDTO.getFrom())) {
            throw new ParameterException("调用内部服务请必填模块入口参数：from");
        }
        CreateRepertoryReqDTO createRepertoryReqDTO = itemService.batchImport(itemDoubleParamDTO);
        if (Optional.ofNullable(createRepertoryReqDTO).isPresent()) {
            itemService.batchOpenStack(createRepertoryReqDTO, itemDoubleParamDTO.getSecondData());
        }
        return 1;
    }

    @ApiOperation(value = "上下架", notes = "上下架")
    @PostMapping("rack_item")
    public Integer rackItem(@RequestBody @Valid ItemRackDTO itemRackDTO) {
        log.info("上下架商品入参:{}", JacksonUtils.writeValueAsString(itemRackDTO));
        return skuService.rack(itemRackDTO);
    }

    @ApiOperation(value = "erp商品售罄回调 售完下架")
    @PostMapping("/erp/callback")
    public Boolean erpCallback(@RequestBody ItemErpReqDto itemErpReqDto) {
        log.info("erp商品售罄回调 售完下架：{}", JacksonUtils.writeValueAsString(itemErpReqDto));
        //手动切库
        dynamicHelper.changeDatasource(itemErpReqDto.getEnterpriseGuid());
        UserContextUtils.putErp(itemErpReqDto.getEnterpriseGuid());
        return skuService.sellOutRackItem(itemErpReqDto.getSkuGuids());
    }


    @ApiOperation(value = "修改整单折扣", notes = "修改整单折扣")
    @PostMapping("whole_discount_item")
    public Integer wholeDiscountItem(@RequestBody @Valid ItemWholeDiscountDTO itemWholeDiscountDTO) {
        log.info("整单折扣商品入参:{}", JacksonUtils.writeValueAsString(itemWholeDiscountDTO));
        return skuService.wholeDiscount(itemWholeDiscountDTO);
    }

    /**
     * 批量修改商品
     *
     * @return boolean
     */
    @PostMapping("batch_update")
    public Integer batchUpdate(@RequestBody @Valid ItemBatchUpdateDTO itemBatchUpdateDTO) {
        log.info("批量修改商品入参:{}", JacksonUtils.writeValueAsString(itemBatchUpdateDTO));
        return itemService.batchUpdate(itemBatchUpdateDTO);
    }

    @ApiOperation(value = "批量删除", notes = "批量删除")
    @PostMapping("/batch_delete")
    public Integer batchDelete(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("商品batch_delete接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        itemHelper.validateItemStringListDTO(itemStringListDTO);
        // 这里也要校验积分
        return itemService.batchDelete(itemStringListDTO, Boolean.TRUE);
    }

    @ApiOperation(value = "绑定获取商品(菜谱和普通模式都查询)", notes = "绑定美团获取商品")
    @PostMapping("/mapping_all_Items")
    public List<MappingRespDTO> mappingAllItems(@RequestBody @Valid ItemSingleDTO itemSingleDTO) {
        log.info("商品mapping接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return skuService.mappingAllItems(itemSingleDTO.getData());
    }


    @ApiOperation(value = "批量获取商品(菜谱和普通模式都查询)", notes = "批量获取商品(菜谱和普通模式都查询)")
    @PostMapping("/batch_mapping_all_Items")
    public Map<String, List<MappingRespDTO>> batchMappingAllItems(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("批量获取商品接口入参,itemSingleDTO:{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return skuService.batchMappingAllItems(itemSingleDTO.getStoreGuids());
    }

    @ApiOperation(value = "绑定获取商品", notes = "绑定美团获取商品")
    @PostMapping("/mapping")
    public List<MappingRespDTO> mapping(@RequestBody @Valid ItemSingleDTO itemSingleDTO) {
        log.info("商品mapping接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return skuService.mapping(itemSingleDTO.getData());
    }

    @ApiOperation(value = "kds获取商品", notes = "kds获取商品")
    @PostMapping("/kds_mapping")
    public List<MappingRespDTO> kdsMapping(@RequestBody @Valid ItemSingleDTO itemSingleDTO) {
        log.info("商品mapping接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return skuService.kdsMapping(itemSingleDTO);
    }

    @ApiOperation(value = "kds商品parentGuid获取商品", notes = "kds获取商品")
    @PostMapping("/kds_item_parent_mapping")
    public List<ItemInfoRespDTO> kdsItemParentMapping(@RequestBody @Valid List<String> itemGuidList) {
        log.info("商品mapping接口入参,itemGuidList={}", JacksonUtils.writeValueAsString(itemGuidList));
        return itemService.kdsItemParentMapping(itemGuidList);
    }

    @ApiOperation(value = "通过sku查询parentSku", notes = "查询parentSku")
    @PostMapping("/parent_sku")
    public List<SkuInfoRespDTO> findParentSKUS(@RequestBody @Valid List<String> skuGuids) {
        log.info("通过sku查询parentSku接口入参,skuGuids={}", JacksonUtils.writeValueAsString(skuGuids));
        return skuService.findParentSKUS(skuGuids);
    }

    @ApiOperation(value = "通过sku查询parentItemGuid和parentSkuGuid拼接信息", notes = "查询parentSku")
    @PostMapping("/parent_item_sku")
    public List<String> findParentItemSku(@RequestBody @Valid List<String> skuGuids) {
        log.info("通过sku查询parentItemGuid和parentSkuGuid拼接信息,skuGuids={}", JacksonUtils.writeValueAsString(skuGuids));
        return skuService.findParentItemSkus(skuGuids);
    }

    @ApiOperation(value = "获取规格详情", notes = "外卖下单获取规格详情")
    @PostMapping("/selectSkuInfo")
    public List<SkuTakeawayInfoRespDTO> selectSkuTakeawayInfoRespDTOList(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("外卖下单获取规格详情入参,itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        itemHelper.validateItemStringListDTO(itemStringListDTO);
        return skuService.selectSkuTakeawayInfoRespDTOList(itemStringListDTO);
    }

    @ApiOperation(value = "获取规格详情", notes = "外卖下单获取规格详情")
    @PostMapping("/selectSkuInfo/v2")
    public List<SkuTakeawayInfoRespDTO> selectSkuTakeawayInfoRespDTOListV2(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("外卖下单获取规格详情入参,itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        itemHelper.validateItemStringListDTO(itemStringListDTO);
        return skuService.selectSkuTakeawayInfoRespDTOListV2(itemStringListDTO);
    }

    @ApiOperation(value = "根据门店当前模式查询规格商品信息")
    @PostMapping("/list_sku_info/by_mode")
    public List<SkuInfoRespDTO> listSkuInfoByRecipeMode(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("根据门店当前模式查询规格商品信息入参,itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        return skuService.listSkuInfoByRecipeMode(itemStringListDTO.getStoreGuid(), itemStringListDTO.getDataList());
    }

    @ApiOperation(value = "根据套餐商品查询套餐明细")
    @PostMapping("/list_pkg_info/by_item")
    public List<SkuInfoPkgDTO> listPkgInfoByItemGuid(@RequestBody List<String> itemGuidList) {
        log.info("根据套餐商品查询套餐明细,itemGuidList={}", JacksonUtils.writeValueAsString(itemGuidList));
        return skuService.listPkgInfoByItemGuid(itemGuidList);
    }

    /**
     * 查询规格详情列表
     * 如果是套餐的规格，则增加子项的规格详情
     *
     * @param itemStringListDTO 规格guid列表
     * @return 规格详情列表
     */
    @ApiOperation(value = "查询规格详情列表")
    @PostMapping("/list_sku_info")
    public List<SkuTakeawayInfoRespDTO> listSkuInfoAndSub(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("外卖下单获取规格详情入参,itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        return skuService.listSkuInfoAndSub(itemStringListDTO);
    }


    @ApiOperation(value = "打印获取商品列表", notes = "打印获取商品列表")
    @PostMapping("/selectTypeItemList")
    public List<TypeItemListDTO> selectTypeItemList(@RequestBody PrintItemReqDto itemReqDto) {
        log.info("获取打印商品列表入参,baseDTO={}", JacksonUtils.writeValueAsString(itemReqDto));
        itemHelper.validateStoreGuid(itemReqDto);
        return itemService.selectTypeItemList(itemReqDto);
    }

    @ApiOperation(value = "通过商品guid和门店guid查询对应spu的所有商品Guid", notes = "通过商品guid和门店guid查询对应spu的所有商品Guid")
    @PostMapping("/selectSpuItems")
    public List<String> selectSpuItems(@RequestBody ItemSpuReqDTO itemSpuReqDTO) {
        log.info("获取商品对应spu商品信息,itemSpuReqDTO={}", JacksonUtils.writeValueAsString(itemSpuReqDTO));
        return itemService.selectSpuItems(itemSpuReqDTO);
    }


    @ApiOperation(value = "通过商品guid和门店guid查询对应spu的所有商品Guid", notes = "通过商品guid和门店guid查询对应spu的所有商品Guid")
    @PostMapping("/selectSpuItemMaps")
    public Map<String, List<String>> selectSpuItemMaps(@RequestBody ItemSpuReqDTO itemSpuReqDTO) {
        log.info("获取商品对应spu商品信息,itemSpuReqDTO={}", JacksonUtils.writeValueAsString(itemSpuReqDTO));
        return itemService.selectSpuItemMaps(itemSpuReqDTO);
    }

    @ApiOperation(value = "移除推送", notes = "组织模块变更门店所属品牌时，需要调用的接口，以清除当前门店中存在的之前绑定品牌推送过来的商品，分类，属性数据。" +
            "且调用此接口时，门店与品牌绑定关系必须为一个门店只能绑定一个品牌，如果一个门店能绑定多个品牌，则接口内容需要变更。")
    @PostMapping("/remove_push")
    public Integer removePush(@RequestParam String storeGuid) {
        log.info("移除指定门店被推实体入参,storeGuid={}", JacksonUtils.writeValueAsString(storeGuid));
        return itemService.removePush(storeGuid);
    }

    @ApiOperation(value = "获取商品模板规格列表", notes = "获取商品模板规格列表")
    @PostMapping("/get_sku_item_list")
    public Page<ItemTemplateSubItemRespDTO> selectSkuItemList(@RequestBody @Valid ItemTemplateMenuAllSubItemReqDTO request) {
        log.info("按条件分页获取sku商品列表入参,request={}", JacksonUtils.writeValueAsString(request));
        return itemService.selectSkuItemList(request);

    }

    @ApiOperation(value = "导入商品验证", notes = "商品批量导入之前验证 获取商品集合 flag 0：查询品牌商品  1：查询门店商品")
    @PostMapping("/get_items_before_import")
    public List<ItemBatchImportTempRespDTO> selectItemByNameBeforeImportThenInBatches(@RequestBody @Valid BatchImportGetItemsReqDTO request) {
        log.info("根据flag 查询商品集合,request={}", JacksonUtils.writeValueAsString(request));
        return itemService.selectItemsBeforeImport(request);
    }

    @ApiOperation(value = "报表大屏菜品支持", notes = "报表大屏菜品支持")
    @PostMapping("/get_journaling_item")
    public List<JournalingItemRespDTO> selectJournalingItem(@RequestBody @Valid BaseDTO request) {
        log.info("报表大屏菜品支持->查询企业下门店所有商品（包含推送商品）, request={}", JacksonUtils.writeValueAsString(request));
        return itemService.selectJournalingItem();
    }


    @ApiOperation(value = "微信获取商品图片", notes = "微信获取商品图片 datas:商品guid集合")
    @PostMapping("/get_item_picture_urls")
    public List<ItemImgDTO> selectItemPictureUrls(@RequestBody SingleDataDTO request) {
        log.info("根据商品guid获取商品图片集合, request={}", JacksonUtils.writeValueAsString(request));
        return itemService.selectItemPictureUrls(request);
    }


    @ApiOperation(value = "mdm，查询门店下推送的父商品", notes = "mdm 对接 erp扣减订单下品牌商品的bom -> 查询品牌下商品guid")
    @PostMapping("/get_erp_sku_guid")
    public Map<String, String> selectErpSkuGuidBySkuGuid(@RequestBody SingleDataDTO request) {
        log.info("订单扣减ERP库存根据门店sku查询父级品牌guid,request = {}", JacksonUtils.writeValueAsString(request));
        return skuService.selectErpSkuGuidBySkuGuid(request);
    }


    @ApiOperation(value = "获取团餐详情", notes = "获取团餐详情 data：商品guid")
    @PostMapping("/select_group_meal_detail")
    public GroupMealItemDetailRespDTO selectGroupMealDetail(@RequestBody SingleDataDTO request) {
        log.info("获取团餐详情 入参， request = {}", JacksonUtils.writeValueAsString(request));
        return itemService.selectGroupMealDetail(request.getData());
    }

    @ApiOperation(value = "更新商品规格", notes = "更新商品规格")
    @PostMapping("/update_item_sku")
    public Integer updateItemSku(@RequestBody @Valid ItemReqDTO itemReqDTO) {
        log.info("商品sku更新接口入参,itemReqDTO={}", JacksonUtils.writeValueAsString(itemReqDTO));
        itemReqDTO = itemHelper.change(itemReqDTO);
        if (StringUtils.isEmpty(itemReqDTO.getItemGuid())) {
            throw new ParameterException(WRONG_PARAMS);
        }
        // 入库的规格GUID集合
        itemService.updateItemSku(itemReqDTO);
        return 1;
    }

    @ApiOperation(value = "删除商品规格", notes = "删除商品规格")
    @PostMapping("/delete_item_sku")
    public Integer deleteItemSku(@RequestBody @Valid ItemReqDTO itemReqDTO) {
        log.info("商品sku删除接口入参,itemReqDTO={}", JacksonUtils.writeValueAsString(itemReqDTO));
        itemReqDTO = itemHelper.change(itemReqDTO);
        if (StringUtils.isEmpty(itemReqDTO.getItemGuid())) {
            throw new ParameterException(WRONG_PARAMS);
        }
        // 入库的规格GUID集合
        itemService.deleteItemSku(itemReqDTO);
        return 1;
    }

    /**
     * 交换排序
     *
     * @param itemReqDTO
     * @return
     */
    @PostMapping("/switch_sort")
    public Integer switchSort(@RequestBody @Valid ItemSortSwitchReqDTO itemReqDTO) {
        log.info("交换两个商品的排序,{}", JacksonUtils.writeValueAsString(itemReqDTO));
        try {
            itemService.switchSort(itemReqDTO);
            return 1;
        } catch (BusinessException ex) {
            log.warn("交换排序失败", ex);
        }
        return 0;
    }

    /**
     * 门店是否有商品
     *
     * @param storeGuid
     * @return
     */
    @GetMapping("/store_has_item")
    public Integer storeHasItem(@RequestParam String storeGuid) {
        LambdaQueryWrapper<ItemDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ItemDO::getStoreGuid, storeGuid);
        return itemService.count(lambdaQueryWrapper) > 0 ? 1 : 0;
    }

    /**
     * 按条件搜索
     *
     * @param itemSearchReqDTO
     * @return
     */
    @PostMapping("/search")
    public List<ItemSearchRespDTO> search(@RequestBody ItemSearchReqDTO itemSearchReqDTO) {
        return itemService.search(itemSearchReqDTO);
    }

    /**
     * 商品点赞
     */
    @PostMapping("/vote_up")
    public Integer voteUp(@RequestBody ItemVoteReqDTO itemVoteReqDTO) {
        return itemService.voteUp(itemVoteReqDTO);
    }

    /**
     * 商品点踩
     */
    @PostMapping("/vote_down")
    public Integer voteDown(@RequestBody ItemVoteReqDTO itemVoteReqDTO) {
        return itemService.voteDown(itemVoteReqDTO);
    }

    /**
     * 获取品牌下所有商品及分类
     * 查的是单个品牌
     *
     * @param itemSingleDTO ItemSingleDTO
     * @return List<TypeItemListDTO>
     */
    @ApiOperation(value = "获取品牌下所有商品及分类")
    @PostMapping("/query_item_by_brand_list")
    public List<TypeItemListDTO> queryStoreByBrandList(@RequestBody @Valid ItemSingleDTO itemSingleDTO) {
        log.info("获取品牌下所有商品及分类接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return itemService.queryStoreByBrandList(itemSingleDTO.getData());
    }

    /**
     * 批量移动商品:选中一个分类下的商品，批量移动到其它分类下
     *
     * @param typeSortReqDTO 商品guidList，目标分类guid
     * @return boolean
     */
    @ApiOperation(value = "批量移动商品")
    @PostMapping("/batch_move_item")
    public Boolean batchMoveItem(@RequestBody TypeSortReqDTO typeSortReqDTO) {
        log.info("批量移动商品入参,typeSortReqDTO={}", JacksonUtils.writeValueAsString(typeSortReqDTO));
        return itemService.batchMoveItem(typeSortReqDTO);
    }


    @ApiOperation(value = "根据门店查询已下架和已推送的商品")
    @PostMapping("/query_filter_item")
    public List<String> queryFilterItem(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("批量移动商品入参,typeSortReqDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return itemService.queryFilterItem(itemSingleDTO);
    }

    /**
     * 根据门店GUID查询所有商品详情已经分类信息（区分销售模式）
     *
     * @param itemSingleDTO storeGuid
     * @return 分类和商品详情
     */
    @ApiOperation(value = "根据门店GUID查询所有商品详情已经分类信息（区分销售模式）")
    @PostMapping("/query_store_item_By_sales_model")
    public ItemInfoAndTypeRespDTO queryStoreItemBySalesModel(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("根据门店GUID查询所有商品详情已经分类信息（区分销售模式） 入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        // 赚餐调用切库
        if (!ObjectUtils.isEmpty(itemSingleDTO.getEnterpriseGuid()) && !ObjectUtils.isEmpty(itemSingleDTO.getStoreGuid())) {
            UserContextUtils.putErpAndStore(itemSingleDTO.getEnterpriseGuid(), itemSingleDTO.getStoreGuid());
            EnterpriseIdentifier.setEnterpriseGuid(itemSingleDTO.getEnterpriseGuid());
        }
        return itemService.queryStoreItemBySalesModel(itemSingleDTO);
    }


    /**
     * 新根据门店GUID查询所有商品详情已经分类信息（区分销售模式）
     * 只适用于打印业务
     *
     * @param itemSingleDTO storeGuid
     * @return 分类和商品详情
     */
    @ApiOperation(value = "新根据门店GUID查询所有商品详情已经分类信息（区分销售模式）")
    @PostMapping("/query_store_item_By_sales_model_new")
    public ItemInfoAndTypeRespDTO queryStoreItemBySalesNew(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("新根据门店GUID查询所有商品详情已经分类信息（区分销售模式） 入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        // 赚餐调用切库
        if (!ObjectUtils.isEmpty(itemSingleDTO.getEnterpriseGuid()) && !ObjectUtils.isEmpty(itemSingleDTO.getStoreGuid())) {
            UserContextUtils.putErpAndStore(itemSingleDTO.getEnterpriseGuid(), itemSingleDTO.getStoreGuid());
            EnterpriseIdentifier.setEnterpriseGuid(itemSingleDTO.getEnterpriseGuid());
        }
        return itemService.queryStoreItemBySalesNew(itemSingleDTO);
    }

    /**
     * 根据门店Guid和商品Guid查询门店下有没有这些个商品
     * 讲道理我觉得这个接口是没有必要的
     *
     * @param itemStringListDTO 门店guid，商品guid
     * @return 有则返回商品信息，无则返回空
     */
    @ApiOperation(value = "根据门店guidList查询所有商品并过滤")
    @PostMapping("/query_store_item_and_filter")
    public ItemInfoAndTypeRespDTO queryStoreItemAndFilter(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("根据门店guidList查询所有商品并过滤 入参,itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        // 赚餐调用切库
        if (!ObjectUtils.isEmpty(itemStringListDTO.getEnterpriseGuid())) {
            UserContextUtils.putErp(itemStringListDTO.getEnterpriseGuid());
            EnterpriseIdentifier.setEnterpriseGuid(itemStringListDTO.getEnterpriseGuid());
        }
        return itemService.queryStoreItemAndFilter(itemStringListDTO);
    }

    /**
     * 赚餐后台查询商品接口
     * 原来是从mdm调，现在要加入菜谱方案，改为直接掉商户后台
     * 沟通发现必传门店和企业，其他字段都是空的
     *
     * @param itemQueryDTO ItemQueryDTO
     * @return List<SelectItemDTO>
     * @Deprecated 现在直接用/query_store_item_By_sales_model查询商品了
     */
    @ApiOperation("赚餐后台查询商品")
    @PostMapping("/selectItemList")
    public List<SelectItemDTO> selectItemList(@RequestBody ItemQueryDTO itemQueryDTO) {
        log.info("赚餐后台查询商品 入参：{}", JacksonUtils.writeValueAsString(itemQueryDTO));
        // 赚餐调用切库
        if (!ObjectUtils.isEmpty(itemQueryDTO.getEnterpriseGuid())) {
            UserContextUtils.putErp(itemQueryDTO.getEnterpriseGuid());
            EnterpriseIdentifier.setEnterpriseGuid(itemQueryDTO.getEnterpriseGuid());
        }
        return itemService.selectItemList(itemQueryDTO);
    }

    /**
     * 存入item服务的redis
     *
     * @param redisReqDTO 存入redis所需要的值
     * @return 成功
     */
    @ApiOperation("存入item服务的redis")
    @PostMapping("/put_item_redis")
    public Boolean putItemRedis(@RequestBody ItemRedisReqDTO redisReqDTO) {
        log.info("存入item服务的redis key 入参：{}", JacksonUtils.writeValueAsString(redisReqDTO.getRedisKey()));
        return itemService.putItemRedis(redisReqDTO);
    }

    /**
     * 通过商品名称查询规格guid
     *
     * @param itemNames 商品名称集合
     * @return 成功
     */
    @PostMapping("/findSkus")
    public Map<String, String> findSkusByItemName(@RequestBody List<String> itemNames) {
        log.info("通过商品名称查询规格guid 入参：{}", JacksonUtils.writeValueAsString(itemNames));
        return itemService.findSkusByItemName(itemNames);
    }

    /**
     * 根据商品guid获取菜谱中的商品详情列表
     *
     * @param itemStringListDTO 门店guid，商品guid列表
     * @return 商品详情列表
     */
    @ApiOperation(value = "根据商品guid获取菜谱中的商品详情列表", notes = "根据商品guid获取菜谱中的商品详情列表")
    @PostMapping("/select_item_info_list_on_plan")
    public List<ItemInfoRespDTO> selectItemInfoListOnPlan(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("根据商品guid获取菜谱中的商品详情列表，itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        if (CollectionUtils.isEmpty(itemStringListDTO.getDataList())) {
            log.info("根据商品guid获取菜谱中的商品详情列表-商品guid列表为空！");
            return new ArrayList<>();
        }
        return itemService.selectItemInfoListOnPlan(itemStringListDTO);
    }

    @ApiOperation(value = "根据商品guid获取门店商品详情列表（区分销售模式）")
    @PostMapping("/list_item_info_by_sales_model")
    public List<ItemInfoRespDTO> listItemInfoBySalesModel(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("根据商品guid获取门店商品详情列表（区分销售模式），itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        return itemService.listItemInfoBySalesModel(itemStringListDTO);
    }


    @ApiOperation(value = "新根据商品guid获取门店商品详情列表（区分销售模式）")
    @PostMapping("/list_item_info_by_sales_model_new")
    List<ItemInfoRespDTO> listItemInfoBySalesModelNew(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("新根据商品guid获取门店商品详情列表（区分销售模式），itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        return itemService.listItemInfoBySalesModelNew(itemStringListDTO);
    }

    /**
     * 获取分类排序  区分销售模式
     *
     * @param typeSingleDTO
     * @return
     */
    @ApiOperation(value = "根据商品guid获取门店商品详情列表（区分销售模式）")
    @PostMapping("/query_store_item_type")
    public List<ItemInfoTypeRespDTO> queryStoreItemType(@RequestBody TypeSingleDTO typeSingleDTO) {
        log.info("根据分类获取（区分销售模式），itemStringListDTO={}", JacksonUtils.writeValueAsString(typeSingleDTO));
        return itemService.queryStoreItemType(typeSingleDTO);
    }

    /**
     * 查询外卖绑定的erp商品
     * 菜谱和普通模式都查询
     *
     * @param itemSingleDTO 门店guid
     * @return erp商品
     */
    @ApiOperation(value = "查询外卖绑定的erp商品")
    @PostMapping("/query_erp_item")
    public List<ErpMappingType> queryErpItem(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("查询外卖绑定的erp商品 入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return skuService.queryErpItem(itemSingleDTO.getData());
    }

    /**
     * 查询所有门店外卖绑定的erp商品
     *
     * @param itemStringListDTO 门店guid列表
     * @return 所有门店外卖绑定的erp商品
     */
    @ApiOperation(value = "查询所有门店外卖绑定的erp商品")
    @PostMapping("/query_erp_item/by_stores")
    public List<StoreItemListRespDTO> queryErpItemByStoreGuids(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("查询外卖绑定的erp商品 入参,itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        return skuService.queryErpItemByStoreGuids(itemStringListDTO.getDataList());
    }

    /**
     * 获取品牌下所有分类及商品
     *
     * @param itemSingleDTO 品牌guid，关键字
     * @return 分类及商品
     */
    @ApiOperation(value = "获取品牌下所有分类及商品")
    @PostMapping("/list_type_and_item_by_brand")
    public List<TypeItemRespDTO> listTypeAndItemByBrand(@RequestBody @Valid ItemSingleDTO itemSingleDTO) {
        log.info("获取品牌下所有分类及商品 入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return itemService.listTypeAndItemByBrand(itemSingleDTO);
    }

    /**
     * 获取门店分类以及指定商品
     *
     * @param itemTypeDTO guid
     * @return 分类以及指定商品
     */
    @ApiOperation(value = "获取品牌下所有分类及商品")
    @PostMapping("/select_print_item_type")
    public PrintSortRespDTO selectPrintItemType(@RequestBody PrintItemTypeDTO itemTypeDTO) {
        log.info("获取门店分类以及指定商品 入参,itemTypeDTO={}", JacksonUtils.writeValueAsString(itemTypeDTO));
        return itemService.selectPrintItemType(itemTypeDTO);
    }

    @ApiOperation(value = "查询商品及父级信息 - 已删除商品也需查询")
    @PostMapping("/query_parent_item_info")
    public List<ItemInfoRespDTO> queryParentItemInfo(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("查询商品及父级信息入参,商品guidList={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        return itemService.queryParentItemInfo(itemStringListDTO.getDataList());
    }

    @GetMapping("/query_parent_item_guid")
    public String getSubItemGuid(@RequestParam String parentItemGuid) {
        return itemService.getSubItemGuid(parentItemGuid);
    }

    /**
     * 查询商品是否所在门店
     *
     * @param itemSingleDTO 门店、品牌、商品
     * @return 存在的门店
     */
    @ApiOperation(value = "查询商品是否所在门店")
    @PostMapping("/query_item_store")
    public List<String> queryItemStore(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("[查询商品是否所在门店]入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return itemService.queryItemStore(itemSingleDTO);
    }

    /**
     * 通过sku查询父级ItemGuid
     * 如果已经是父级，返回本身ItemGuid
     */
    @ApiOperation(value = "通过sku查询父级ItemGuid")
    @PostMapping("/query_parent_item_guid_by_sku")
    public Map<String, String> queryParentItemGuidBySku(@RequestBody ItemStringListDTO query) {
        log.info("[通过sku查询父级ItemGuid]入参,query={}", JacksonUtils.writeValueAsString(query));
        return itemService.queryParentItemGuidBySku(query);
    }

    /**
     * 通过item查询父级ItemGuid
     * 如果已经是父级，返回本身ItemGuid
     */
    @ApiOperation(value = "通过item查询父级ItemGuid")
    @PostMapping("/query_parent_item_guid_by_item")
    public Map<String, String> queryParentItemGuidByItem(@RequestBody ItemStringListDTO query) {
        log.info("[通过item查询父级ItemGuid]入参,query={}", JacksonUtils.writeValueAsString(query));
        return itemService.queryParentItemGuidByItem(query);
    }

    /**
     * 查询品牌下所有商品
     */
    @ApiOperation(value = "查询品牌下所有商品")
    @PostMapping("/query_item_by_brand")
    public Page<ItemWebRespDTO> queryItemByBrand(@RequestBody ItemQueryReqDTO queryReqDTO) {
        log.info("[查询品牌下所有商品]queryReqDTO={}", JacksonUtils.writeValueAsString(queryReqDTO));
        return itemService.queryItemByBrand(queryReqDTO);
    }

    /**
     * 根据商品id查询商品信息
     */
    @ApiOperation(value = "根据商品id查询商品信息")
    @PostMapping("/query_item_by_guid")
    public List<ItemWebRespDTO> queryItemByGuid(@RequestBody ItemStringListDTO query) {
        log.info("[根据商品id查询商品信息]query={}", JacksonUtils.writeValueAsString(query));
        return itemService.queryItemByGuid(query);
    }

    /**
     * 查询推荐商品
     */
    @ApiOperation(value = "查询推荐商品")
    @PostMapping("/query_recommend_item")
    public List<ItemSynRespDTO> queryRecommendItem(@RequestBody ItemSingleDTO query) {
        log.info("[查询推荐商品]query={}", JacksonUtils.writeValueAsString(query));
        return itemService.queryRecommendItem(query);
    }

}
