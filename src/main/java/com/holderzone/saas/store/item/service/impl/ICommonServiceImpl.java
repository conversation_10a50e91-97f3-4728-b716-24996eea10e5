package com.holderzone.saas.store.item.service.impl;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.item.constant.MqConstant;
import com.holderzone.saas.store.item.service.ICommonService;
import org.apache.rocketmq.common.message.Message;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Service
public class ICommonServiceImpl implements ICommonService {
    private static final Logger log = LoggerFactory.getLogger(ICommonServiceImpl.class);

    @Autowired
    private DefaultRocketMqProducer producer;

    @Autowired
    private RedissonClient redissonSingleClient;

    @Override
    public <V> void sendPricePlanToMQ(V value) {
        Message message = new Message(MqConstant.PRICE_PLAN_SYNC_ITEM_TOPIC,
                MqConstant.PRICE_PLAN_SYNC_ITEM_TAG,
                JacksonUtils.toJsonByte(value));
        producer.sendMessage(message);
    }

    @Override
    public String md5(String src) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("md5");
            byte[] b = src.getBytes();
            byte[] digest = md5.digest(b);
            char[] chars = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
            StringBuffer sb = new StringBuffer();
            for (byte bb : digest) {
                sb.append(chars[(bb >> 4) & 15]);
                sb.append(chars[bb & 15]);
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }

    @Override
    public RLock getDistributedLock(String lockName) {
        return redissonSingleClient.getLock(lockName);
    }
}
