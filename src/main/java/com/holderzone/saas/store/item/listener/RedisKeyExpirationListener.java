package com.holderzone.saas.store.item.listener;

import cn.hutool.core.util.ObjectUtil;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.item.dto.SyncPricePlanMessageDTO;
import com.holderzone.saas.store.item.service.ICommonService;
import com.holderzone.saas.store.item.service.impl.PricePlanServiceImpl;
import com.holderzone.saas.store.item.util.DynamicHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RedisKeyExpirationListener
 * @date 2021/03/29 10:56
 * @description
 * @program holder-saas-store-item
 */
@Component
@Slf4j
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {

    @Autowired
    private ICommonService iCommonService;

    private final DynamicHelper dynamicHelper;

    private final PricePlanServiceImpl pricePlanService;

    private static final ScheduledExecutorService EXECUTOR_SERVICE = Executors.newScheduledThreadPool(10);

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public RedisKeyExpirationListener(RedisMessageListenerContainer listenerContainer, DynamicHelper dynamicHelper, PricePlanServiceImpl pricePlanService) {
        super(listenerContainer);
        this.dynamicHelper = dynamicHelper;
        this.pricePlanService = pricePlanService;
    }

    //价格方案Redis Key
    private static final String KEY_PREFIX = "price:plan-";

    // 方案定时执行 redis key
    private static final String KEY_EXECUTION = "plan:execution-";

    // 二维码定时过期
    private static final String KEY_QR_CODE = "Plan:QrCode-";

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String expiredKey = message.toString();
        SyncPricePlanMessageDTO messageDTO;
        if (expiredKey.startsWith(KEY_PREFIX)) {
            String[] split = expiredKey.split("plan-");
            String json = split[1];
            messageDTO = JacksonUtils.toObject(SyncPricePlanMessageDTO.class, json);

        } else if (expiredKey.startsWith(KEY_EXECUTION)) {
            String[] split = expiredKey.split("execution-");
            String json = split[1];
            messageDTO = JacksonUtils.toObject(SyncPricePlanMessageDTO.class, json);

        } else {
            return;
        }
        if (ObjectUtil.isNull(messageDTO)) {
            return;
        }
        dynamicHelper.changeDatasource(messageDTO.getEnterpriseGuid());
        UserContextUtils.putErp(messageDTO.getEnterpriseGuid());
        //防止服务集群下多次执行
        try {
            Boolean ifAbsent = stringRedisTemplate.opsForValue().setIfAbsent(expiredKey, expiredKey);
            if (Boolean.FALSE.equals(ifAbsent)) {
                log.warn("[重复执行],expiredKey={}", expiredKey);
                return;
            }

            if (expiredKey.startsWith(KEY_PREFIX)) {
                iCommonService.sendPricePlanToMQ(messageDTO);
                log.info("价格方案定时执行完毕- key : " + KEY_PREFIX + " , value : " + messageDTO.getPricePlanGuid());
            }

            // 方案定时生效，改变方案信息
            if (expiredKey.startsWith(KEY_EXECUTION)) {
                pricePlanService.timeExecution(messageDTO.getPricePlanGuid());
                log.info("价格方案定时执行完毕- key : " + KEY_EXECUTION + " , value : " + messageDTO.getPricePlanGuid());
            }
        } catch (Exception e) {
            log.error("防止服务集群下多次执行异常", e);
        } finally {
            //延时60s删除key
            EXECUTOR_SERVICE.schedule(() -> {
                dynamicHelper.changeDatasource(messageDTO.getEnterpriseGuid());
                UserContextUtils.putErp(messageDTO.getEnterpriseGuid());
                try {
                    stringRedisTemplate.delete(expiredKey);
                } catch (Exception e) {
                    log.warn("[延时60s删除key]异常",e);
                }
            }, 60, TimeUnit.SECONDS);

        }
    }
}
