package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.common.enums.BooleanEnum;
import com.holderzone.saas.store.dto.item.req.CheckQrCodeDTO;
import com.holderzone.saas.store.dto.item.resp.CheckQrCodeRespDTO;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.entity.domain.PricePlanDO;
import com.holderzone.saas.store.item.entity.domain.PricePlanPreviewQrCodeDO;
import com.holderzone.saas.store.item.mapper.PricePlanMapper;
import com.holderzone.saas.store.item.mapper.PricePlanPreviewQrCodeMapper;
import com.holderzone.saas.store.item.service.IPricePlanPreviewQrCodeService;
import com.holderzone.saas.store.item.util.GUIDUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2021/6/3 17:45
 */
@Service
@AllArgsConstructor
@Slf4j
public class PricePlanPreviewQrCodeServiceImpl extends ServiceImpl<PricePlanPreviewQrCodeMapper,
        PricePlanPreviewQrCodeDO> implements IPricePlanPreviewQrCodeService {

    private final PricePlanPreviewQrCodeMapper planPreviewQrCodeMapper;

    private final PricePlanMapper planMapper;

    private final RedisTemplate<String, String> redisTemplate;

    // 单位是秒
    private static final Long THREE_DAYS = 259200L;

    // 测试时使用的时间，5分钟
    private static final Long FIVE_MINUTES = 300L;

    // 二维码定时过期
    private static final String KEY_QR_CODE = "Plan:QrCode-";

    /**
     * 二维码过期校验
     *
     * @param reqDTO 二维码id和方案id
     * @return 该二维码是否过期，过期时间为3天
     */
    @Override
    public CheckQrCodeRespDTO checkQrCode(CheckQrCodeDTO reqDTO) {
        if (Objects.isNull(reqDTO.getPlanGuid())) {
            throw new BusinessException("方案guid不能为空");
        }
        if (Objects.isNull(reqDTO.getQrCodeGuid())) {
            throw new BusinessException("二维码guid");
        }

        CheckQrCodeRespDTO respDTO = new CheckQrCodeRespDTO();
        respDTO.setIsDeletePlan(Boolean.FALSE);
        respDTO.setIsExpired(Boolean.FALSE);

        // 菜品被删除时返回
        PricePlanDO pricePlanDO = planMapper.selectOne(new LambdaQueryWrapper<PricePlanDO>()
                .eq(PricePlanDO::getGuid, reqDTO.getPlanGuid())
                .eq(PricePlanDO::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        if (ObjectUtils.isEmpty(pricePlanDO)) {
            respDTO.setIsDeletePlan(Boolean.TRUE);
        }

        PricePlanPreviewQrCodeDO previewQrCodeDO = planPreviewQrCodeMapper.selectOne(
                new LambdaQueryWrapper<PricePlanPreviewQrCodeDO>()
                        .eq(PricePlanPreviewQrCodeDO::getPlanGuid, reqDTO.getPlanGuid())
                        .eq(PricePlanPreviewQrCodeDO::getQrCodeGuid, reqDTO.getQrCodeGuid())
        );
        log.info("数据库里二维码信息：{}", JacksonUtils.writeValueAsString(previewQrCodeDO));

        if (Objects.isNull(previewQrCodeDO)) {
            log.info("数据库无二维码信息，二维码guid:{}", reqDTO.getQrCodeGuid());
            PricePlanPreviewQrCodeDO qrCodeDO = new PricePlanPreviewQrCodeDO();
            qrCodeDO.setGuid(GUIDUtils.generateGuid(GuidKeyConstant.HSI_PRICE_PLAN_PREVIEW_QR_CODE));
            qrCodeDO.setPlanGuid(reqDTO.getPlanGuid());
            qrCodeDO.setQrCodeGuid(reqDTO.getQrCodeGuid());
            qrCodeDO.setQrCodeTime(String.valueOf(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"))));
            qrCodeDO.setIsExpire(Boolean.FALSE);
            respDTO.setIsExpired(!this.save(qrCodeDO));
            return respDTO;
        }

        // 比较与当前时间比较
        long nowTime = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")) - Long.parseLong(previewQrCodeDO.getQrCodeTime());
        log.info("二维码生成时间与当前时间计算:{}", nowTime);
        if (nowTime >= FIVE_MINUTES) {
            // 这个暂时必要性不大，但是以后可能用到
            this.update(new LambdaUpdateWrapper<PricePlanPreviewQrCodeDO>()
                    .set(PricePlanPreviewQrCodeDO::getIsExpire, Boolean.TRUE)
                    .eq(PricePlanPreviewQrCodeDO::getPlanGuid, reqDTO.getPlanGuid())
                    .eq(PricePlanPreviewQrCodeDO::getQrCodeGuid, reqDTO.getQrCodeGuid())
            );
            log.info("二维码已过期，二维码guid:{}", reqDTO.getQrCodeGuid());
            respDTO.setIsExpired(Boolean.TRUE);
        }

        // 有时间试试这种写法-通过redis过期事件修改是否过期
        /*String redisKey = reqDTO.getPlanGuid() + "-" + reqDTO.getQrCodeGuid() + ":";
        // 查询并更变数据
        PricePlanPreviewQrCodeDO previewQrCodeDO = planPreviewQrCodeMapper.selectOne(new
        LambdaQueryWrapper<PricePlanPreviewQrCodeDO>()
                .eq(PricePlanPreviewQrCodeDO::getPlanGuid, reqDTO.getPlanGuid()));
        if (Objects.isNull(previewQrCodeDO)) {

            redisTemplate.opsForValue().set(redisKey, NOW_TIME_LONG.toString(), 3, TimeUnit.DAYS);
            return Boolean.FALSE;
        }
        if (previewQrCodeDO.getIsExpire()) {
            return Boolean.TRUE;
        }*/
        return respDTO;
    }
}
