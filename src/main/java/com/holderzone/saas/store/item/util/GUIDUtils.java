package com.holderzone.saas.store.item.util;

import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GUIDUtils
 * @date 2018/07/13 下午2:15
 * @description //GUID工具类
 * @program holder-saas-store-menu
 */
@Slf4j
public class GUIDUtils {
    public static boolean canBeGuid(String guid) {
        return guid != null && !"".equals(guid);
    }

    public static String generateGuid(String redisKey) {
        try {
            return String.valueOf(BatchIdGenerator.getGuid(SpringContextUtils.getInstance().getBean("redisTemplate"), redisKey));
        } catch (IOException e) {
            log.error("BatchIdGenerator类中生成guid失败");
            return null;
        }
    }


    public static List<String> generateGuids(String redisKey, long count) {
        try {
            return BatchIdGenerator.batchGetGuids(SpringContextUtils.getInstance().getBean("redisTemplate"), redisKey, count)
                    .stream().map(String::valueOf).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("BatchIdGenerator类中生成guid失败");
            return null;
        }
    }

}
