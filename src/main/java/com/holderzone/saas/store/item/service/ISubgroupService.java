package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.exception.ParamException;
import com.holderzone.saas.store.item.entity.bo.ItemInfoBO;
import com.holderzone.saas.store.item.entity.bo.SubgroupBO;
import com.holderzone.saas.store.item.entity.domain.RSkuSubgroupDO;
import com.holderzone.saas.store.item.entity.domain.SubgroupDO;

import java.util.List;

/**
 * <p>
 * 套餐的分组 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-24
 */
public interface ISubgroupService extends IService<SubgroupDO> {

    /**
     * 删除套餐集合下面的分组及子商品关联关系
     *
     * @param toDeleteItemGuidList 要被删除的商品GUID集合
     * @throws ParamException
     */
    void removeSubgroupByItemGuidList(List<String> toDeleteItemGuidList) throws ParamException;

    /**
     * 根据商品GUID集合查询其下属分组集合
     *
     * @param itemGuidList
     * @return
     */
    List<SubgroupBO> selectSubgroupListByItemGuidList(List<String> itemGuidList);

    /**
     * 新增或更新指定商品集合下的分组集合，并删除无用的分组集合
     *
     * @param itemInfoBOList
     * @return
     */
    boolean saveOrUpdateAndDeleteSubgroup(List<ItemInfoBO> itemInfoBOList);

    /**
     * 根据套餐GUID集合查询其下子商品与分组关联集合
     *
     * @param pkgGuidList
     * @return
     */
    List<RSkuSubgroupDO> selectSkuSubgroupByItemGuidList(List<String> pkgGuidList);

    /**
     * 通过itemGuid和storeGuid获取guid
     *
     * @param itemGuid
     * @param storeGuid
     * @return
     */
    List<String> getGuidListByItemGuidAndStoreGuid(String itemGuid, String storeGuid);

    /**
     * 通过storeGuid和brandGuid获取guid
     *
     * @param storeGuid
     * @param brandGuid
     * @return
     */
    List<String> getGuidListByStoreGuidAndBrandGuid(String storeGuid, String brandGuid);

    /**
     * 通过itemGuid和storeGuid删除数据
     *
     * @param itemGuid
     * @param storeGuid
     */
    void deleteByItemGuidAndStoreGuid(String itemGuid, String storeGuid);
}
