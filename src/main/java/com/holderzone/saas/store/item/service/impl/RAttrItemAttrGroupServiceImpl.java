package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.item.entity.domain.RAttrItemAttrGroupDO;
import com.holderzone.saas.store.item.mapper.RAttrItemAttrGroupMapper;
import com.holderzone.saas.store.item.service.IRAttrItemAttrGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <p>
 * 属性值与商品属性组关联表的关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Service
public class RAttrItemAttrGroupServiceImpl extends ServiceImpl<RAttrItemAttrGroupMapper, RAttrItemAttrGroupDO> implements IRAttrItemAttrGroupService {


    private final RAttrItemAttrGroupMapper attrItemAttrGroupMapper;

    @Autowired
    public RAttrItemAttrGroupServiceImpl(RAttrItemAttrGroupMapper attrItemAttrGroupMapper) {
        this.attrItemAttrGroupMapper = attrItemAttrGroupMapper;
    }

    @Override
    public boolean deleteByAttr(String attrGuid) {
        if (ObjectUtils.isEmpty(attrGuid)) {
            return false;
        }
        attrItemAttrGroupMapper.delete(
                new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                        .eq(RAttrItemAttrGroupDO::getAttrGuid, attrGuid));
        return true;
    }
}