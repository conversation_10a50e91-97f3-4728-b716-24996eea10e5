package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.item.req.CheckQrCodeDTO;
import com.holderzone.saas.store.dto.item.resp.CheckQrCodeRespDTO;
import com.holderzone.saas.store.item.entity.domain.PricePlanPreviewQrCodeDO;

/**
 * <AUTHOR>
 * @description
 * @date 2021/6/3 17:45
 */
public interface IPricePlanPreviewQrCodeService extends IService<PricePlanPreviewQrCodeDO> {

    /**
     * 二维码过期校验
     *
     * @param reqDTO 二维码id和方案id
     * @return 该二维码是否过期，过期时间为3天
     */
    CheckQrCodeRespDTO checkQrCode(CheckQrCodeDTO reqDTO);
}
