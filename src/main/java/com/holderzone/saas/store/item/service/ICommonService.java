package com.holderzone.saas.store.item.service;

import org.redisson.api.RLock;

/**
 * 公共的Service,用于封装一些公共的方法
 *
 * @param
 */
public interface ICommonService {
    /**
     * 将价格方案信息发送到MQ
     *
     * @param value
     * @param <V>
     */
    <V> void sendPricePlanToMQ(V value);

    /**
     * MD5加密
     *
     * @param src
     * @return
     */
    String md5(String src);

    /**
     * 获取Redis的分布式
     *
     * @param lockName
     * @return RLock
     */
    RLock getDistributedLock(String lockName);
}
