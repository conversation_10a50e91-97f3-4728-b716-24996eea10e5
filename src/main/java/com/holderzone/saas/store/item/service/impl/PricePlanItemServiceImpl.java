package com.holderzone.saas.store.item.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.PlanItemDTO;
import com.holderzone.saas.store.dto.item.common.PlanItemUpdateDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.req.price.PlanPriceAllItemReqDTO;
import com.holderzone.saas.store.dto.item.req.price.PlanPriceAllItemSkuReqDTO;
import com.holderzone.saas.store.dto.item.req.price.PlanPriceEditReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.price.*;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.constant.ItemType;
import com.holderzone.saas.store.item.dto.SyncItemDTO;
import com.holderzone.saas.store.item.dto.SyncStoreAndItemDTO;
import com.holderzone.saas.store.item.entity.domain.*;
import com.holderzone.saas.store.item.entity.enums.ItemStateEnum;
import com.holderzone.saas.store.item.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.item.entity.enums.PricePlanStatusEnum;
import com.holderzone.saas.store.item.mapper.*;
import com.holderzone.saas.store.item.service.IPricePlanItemService;
import com.holderzone.saas.store.item.service.ISkuService;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import com.holderzone.saas.store.item.util.GUIDUtils;
import com.holderzone.saas.store.item.util.MapStructUtils;
import com.holderzone.saas.store.item.util.PageUtils;
import com.holderzone.saas.store.item.util.QrcodeServer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.item.constant.Constant.FALSE;
import static com.holderzone.saas.store.item.constant.Constant.PRICE_PLAN_CANNOT_BE_EMPTY;

@Service
@AllArgsConstructor
@Slf4j
public class PricePlanItemServiceImpl extends ServiceImpl<PricePlanItemMapper, PricePlanItemDO> implements IPricePlanItemService {

    private final PricePlanMapper planMapper;

    private final PricePlanItemMapper planItemMapper;

    private final PricePlanStoreMapper planStoreMapper;

    private final ISkuService skuService;
    private final TypeMapper typeMapper;
    private final ItemMapper itemMapper;

    private final OrganizationService organizationService;

    private final QrcodeServer qrcodeServer;

    private static final String PREVIEW_PLAN = "previewPlan";

    private static final Long NOW_TIME_LONG = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean savePlanItem(PricePlanItemAddReqDTO reqDTO) {
        List<PlanItemDTO> planItemDTOList = reqDTO.getPlanItemDTOList();
        //更新方案包含商品数（多规格商品每种规格都算一个）
        planMapper.updateItemNum(reqDTO.getPlanGuid(), planItemDTOList.size(), 1);
        //查询itemGuid对应的skuGuid
        Map<String, List<SkuDO>> skuMap = skuService.getSkuGuidMap(planItemDTOList.stream().map(PlanItemDTO::getItemGuid).collect(Collectors.toList()));
        List<PricePlanItemDO> itemDOList = new ArrayList<>();
        //组装方案菜品保存对象
        planItemDTOList.forEach(p -> {
            List<SkuDO> skuList = skuMap.get(p.getItemGuid());
            skuList.forEach(sku -> {
                PricePlanItemDO itemDO = MapStructUtils.INSTANCE.planItemDTO2DO(reqDTO, p);
                itemDO.setGuid(GUIDUtils.generateGuid(GuidKeyConstant.HSI_PRICEPLAN_ITEM));
                itemDO.setIsPkgItem(p.getItemType().equals(ItemTypeEnum.PACKAGE.getTypeCode()));
                itemDO.setSkuGuid(sku.getGuid());
//                itemDO.setSalePrice(sku.getSalePrice());
//                itemDO.setMemberPrice(sku.getMemberPrice());
                itemDOList.add(itemDO);
            });
        });
        return this.saveBatch(itemDOList);
    }

    @Override
    public com.holderzone.framework.util.Page<PricePlanItemPageRespDTO> itemList(PricePlanItemPageReqDTO reqDTO) {
        Page<PricePlanItemPageRespDTO> page = new Page<>(reqDTO.getCurrentPage(), reqDTO.getPageSize());
        List<PricePlanItemPageRespDTO> respDTOList = planItemMapper.itemList(reqDTO, page);
        //查询每个商品的规格信息
        respDTOList.forEach(r -> r.setSkuList(planItemMapper.getSkuList(r.getItemGuid(), reqDTO.getPlanGuid())));
        page.setRecords(respDTOList);
        return PageUtils.convert(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeItems(PricePlanItemRemoveReqDTO reqDTO) {
        //移除方案菜品
        List<String> itemGuidList = reqDTO.getItemGuidList();
        if (itemGuidList.isEmpty()) {
            return Boolean.TRUE;
        }
        Boolean result = planItemMapper.logicDeleteByItem(itemGuidList);
        //更新方案包含菜品数
        Integer itemCount = planItemMapper.getCount(reqDTO.getPlanGuid());
        planMapper.updateItemNum(reqDTO.getPlanGuid(), itemCount, 3);
        return result;
    }

    @Override
    public Boolean updateItems(PricePlanItemUpdateReqDTO reqDTO) {
        int percentNum = Optional.ofNullable(reqDTO.getPercentNum()).orElse(0);
        BigDecimal fixedPrice = Optional.ofNullable(reqDTO.getFixedPrice()).orElse(BigDecimal.ZERO);
        List<PricePlanItemUpdateDTO> itemList = reqDTO.getItemList();
        List<PricePlanItemSkuUpdateDTO> skuList = new ArrayList<>();
        if (itemList.isEmpty()) {
            return Boolean.TRUE;
        }
        itemList.forEach(d -> {
            if (d.getIsBatchModified()) {
                d.getSkuList().forEach(sku -> {
                    //属于批量调整的价格才需要重新计算
                    if (percentNum != 0) {
                        //按百分比调整，价格为原价乘以百分比
                        sku.setSalePrice(sku.getOriginalPrice().multiply(new BigDecimal(percentNum)).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                    } else if (fixedPrice.compareTo(BigDecimal.ZERO) > 0) {
                        //按固定值调整，直接设为固定值
                        sku.setSalePrice(fixedPrice);
                    }
                });
            }
            skuList.addAll(d.getSkuList());
        });
        return planItemMapper.updateItems(skuList);
    }

    @Override
    public SyncStoreAndItemDTO getStoreAndItem(String planGuid) {
        SyncStoreAndItemDTO resp = new SyncStoreAndItemDTO();
        resp.setPricePlanGuid(planGuid);
        List<String> storeGuidList = planStoreMapper.getPlanStoreGuidList(planGuid, null);
        if (CollectionUtils.isEmpty(storeGuidList)) {
            resp.setStoreGuidList(new ArrayList<>());
            resp.setItemList(new ArrayList<>());
            return resp;
        }
        //查询方案关联门店
        resp.setStoreGuidList(storeGuidList);
        //查询关联商品
        resp.setItemList(planItemMapper.getSyncItems(planGuid));
        return resp;
    }

    @Override
    public com.holderzone.framework.util.Page<PricePlanItemAddQueryRespDTO> selectBrandSkuItemList(PricePlanItemAddQueryReqDTO request) {
        //查询可导入的菜品
        Page<PricePlanItemAddQueryRespDTO> page = new Page<>(request.getCurrentPage(), request.getPageSize());
        List<PricePlanItemAddQueryRespDTO> respDTOS = planItemMapper.getBrandSkuItemList(request, page);
        //查询方案已有菜品sku集合
        List<String> itemGuidList = planItemMapper.getPlanItemGuidList(request.getPlanGuid());
        if (CollectionUtils.isNotEmpty(itemGuidList)) {
            //方案已选择的进行标记
            respDTOS.forEach(r -> r.setIsSelected(itemGuidList.contains(r.getItemGuid())));
        } else {
            respDTOS.forEach(r -> r.setIsSelected(Boolean.FALSE));
        }
        page.setRecords(respDTOS);
        return PageUtils.convert(page);
    }

    @Override
    public SyncItemDTO getSyncItem(String itemGuid, String skuGuid) {
        return planItemMapper.getSyncItem(itemGuid, skuGuid);
    }

    @Override
    public List<TypeWebRespDTO> queryTypeByPricePlanGuid(String pricePlanGuid) {
        if (StringUtils.isEmpty(pricePlanGuid)) {
            throw new BusinessException(PRICE_PLAN_CANNOT_BE_EMPTY);
        }
        List<TypeDO> typeDOList = typeMapper.selectList(new LambdaQueryWrapper<TypeDO>().eq(TypeDO::getPricePlanGuid, pricePlanGuid));

        List<TypeWebRespDTO> typeWebRespDTOList = MapStructUtils.INSTANCE.typeDOList2typeWebRespDTOList(typeDOList);
        typeWebRespDTOList.sort(Comparator.comparing(TypeWebRespDTO::getSort));
        return typeWebRespDTOList;
    }

    @Override
    public Boolean updateItemList(List<PlanItemUpdateDTO> updatePlanItemDTOList) {
        if (updatePlanItemDTOList.isEmpty()) {
            return Boolean.TRUE;
        }
        return planItemMapper.updateItemList(updatePlanItemDTOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchOperatingItem(PricePlanItemReqDTO reqDTO) {
        List<PricePlanItemDO> pricePlanItemDOList = this.list(new LambdaQueryWrapper<PricePlanItemDO>()
                .eq(PricePlanItemDO::getPlanGuid, reqDTO.getPlanGuid())
                .in(PricePlanItemDO::getItemGuid, reqDTO.getItemGuidList()));
        pricePlanItemDOList.forEach(e -> {
            //商品上下架操作
            if (!ObjectUtils.isEmpty(reqDTO.getIsSoldOut())) {
                e.setIsSoldOut(reqDTO.getIsSoldOut());
            }
            //商品批量修改分类
            if (!ObjectUtils.isEmpty(reqDTO.getTypeGuid())) {
                e.setTypeGuid(reqDTO.getTypeGuid());
            }
        });
        return this.saveOrUpdateBatch(pricePlanItemDOList);
    }

    /**
     * 根据菜谱方案查询菜谱方案绑定菜品
     *
     * @param itemQueryReqDTO ItemQueryReqDTO
     * @return 商品列表
     */
    @Override
    public com.holderzone.framework.util.Page<ItemWebRespDTO> queryPlanItemsByPlan(ItemQueryReqDTO itemQueryReqDTO) {
        if (ObjectUtils.isEmpty(itemQueryReqDTO.getPlanGuid())) {
            throw new BusinessException(PRICE_PLAN_CANNOT_BE_EMPTY);
        }
        Page<ItemWebRespDTO> page = new Page<>(itemQueryReqDTO.getCurrentPage(), itemQueryReqDTO.getPageSize());
        List<ItemWebRespDTO> allItem = planItemMapper.queryPlanItemsByPlanAllPage(itemQueryReqDTO, page);
        if (CollectionUtils.isEmpty(allItem)) {
            log.warn("查询商品为空");
            return new com.holderzone.framework.util.Page<>();
        }
        log.info("商品列表 list:{}", JSON.toJSONString(allItem));
        // 过滤传入的商品数据
        if (!CollectionUtils.isEmpty(itemQueryReqDTO.getFilterItemList())) {
            allItem = allItem.stream()
                    .filter(i -> !itemQueryReqDTO.getFilterItemList().contains(i.getItemGuid()))
                    .collect(Collectors.toList());
        }
        log.info("过滤传入的商品数据后 list:{}", JSON.toJSONString(allItem));
        // 过滤传入的规格数据
        if (!CollectionUtils.isEmpty(itemQueryReqDTO.getFilterSkuList())) {
            log.info("过滤传入的规格数据");
            allItem.removeIf(i -> itemQueryReqDTO.getFilterSkuList().contains(i.getSkuGuid()));
        }
        log.info("过滤传入的规格数据后 list:{}", JSON.toJSONString(allItem));
        List<String> typeGuidList = allItem.stream().map(ItemWebRespDTO::getTypeGuid).collect(Collectors.toList());
        List<TypeDO> typeDOList = typeMapper.selectList(new LambdaQueryWrapper<TypeDO>().in(TypeDO::getGuid, typeGuidList));
        Map<String, TypeDO> typeMap = typeDOList.stream().collect(Collectors.toMap(TypeDO::getGuid, typeDO -> typeDO));

        Map<String, List<ItemWebRespDTO>> itemMap = allItem.stream().collect(Collectors.groupingBy(ItemWebRespDTO::getItemGuid));

        Map<String, ItemWebRespDTO> skuMap = allItem.stream().collect(Collectors.toMap(ItemWebRespDTO::getSkuGuid, Function.identity(), (dto1, dto2) -> dto2));

        // 删除重复的规格数据
        List<ItemWebRespDTO> collect = allItem.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ItemWebRespDTO::getItemGuid))), ArrayList::new));
        // 方案数据设置
        log.info("删除重复的规格数据：{},返回排序前 list:{}", JSON.toJSONString(collect),JSON.toJSONString(collect));
        collect.sort(Comparator.comparing(ItemWebRespDTO::getSort));
        log.info("返回排序后 list:{}", JSON.toJSONString(collect));
        setSkuList(collect, typeMap, skuMap, itemMap);

        page.setRecords(collect);
        page.setTotal(planItemMapper.queryPlanItemsByPlanCount(itemQueryReqDTO));
        return PageUtils.convert(page);
    }

    private void setSkuList(List<ItemWebRespDTO> allItem, Map<String, TypeDO> typeMap, Map<String, ItemWebRespDTO> skuMap, Map<String, List<ItemWebRespDTO>> itemMap) {
        if(CollUtil.isEmpty(skuMap)){
            return;
        }
        //批量查询商品规格
        Set<String> skuList = new HashSet<>(skuMap.keySet());
        // 商品规格设置
        List<SkuDO> skuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>().in(SkuDO::getGuid, skuList));
        log.info("商品规格设置skuDOList list:{}", JSON.toJSONString(skuDOList));
        if (CollectionUtils.isEmpty(skuDOList)) {
            log.warn("查询不到规格数据：{}", skuList);
            throw new BusinessException("查询不到规格数据");
        }
        List<SkuRespDTO> skuRespDTOList = MapStructUtils.INSTANCE.skuDOList2skuRespDTOList(skuDOList);

        for (int i = 0; i < allItem.size(); i++) {
            ItemWebRespDTO item = allItem.get(i);
            if (null == typeMap.get(item.getTypeGuid())) {
                log.warn("获取不到分类数据：{}", item.getTypeGuid());
                return;
            }
            // sort
            item.setSort(i + 1);
            item.setTypeName(typeMap.get(item.getTypeGuid()).getName());

            List<ItemWebRespDTO> respDTOList = itemMap.get(item.getItemGuid());
            if (CollectionUtils.isEmpty(respDTOList)) {
                throw new BusinessException("商品数据异常");
            }
            Set<String> collect = respDTOList.stream().map(ItemWebRespDTO::getSkuGuid).collect(Collectors.toSet());
            List<SkuRespDTO> finalSku = skuRespDTOList.stream().filter(e -> collect.contains(e.getSkuGuid())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(finalSku)) {
                throw new BusinessException("查询不到规格数据");
            }
            finalSku.forEach(sku -> {
                ItemWebRespDTO itemWebRespDTO = skuMap.get(sku.getSkuGuid());
                if (Objects.equals(itemWebRespDTO.getSkuGuid(), sku.getSkuGuid())) {
                    sku.setPlanItemGuid(itemWebRespDTO.getPlanItemGuid());
                    sku.setMemberPrice(null);
                    if (!ObjectUtils.isEmpty(itemWebRespDTO.getMemberPrice())) {
                        sku.setMemberPrice(itemWebRespDTO.getMemberPrice());
                    }
                    if (!ObjectUtils.isEmpty(itemWebRespDTO.getSalePrice())) {
                        sku.setSalePrice(itemWebRespDTO.getSalePrice());
                    }
                    sku.setAccountingPrice(null);
                    if (!ObjectUtils.isEmpty(itemWebRespDTO.getAccountingPrice())) {
                        sku.setAccountingPrice(itemWebRespDTO.getAccountingPrice());
                    }
                    sku.setTakeawayAccountingPrice(null);
                    if (!ObjectUtils.isEmpty(itemWebRespDTO.getTakeawayAccountingPrice())) {
                        sku.setTakeawayAccountingPrice(itemWebRespDTO.getTakeawayAccountingPrice());
                    }
                    sku.setLinePrice(null);
                    if (!ObjectUtils.isEmpty(itemWebRespDTO.getLinePrice())) {
                        sku.setLinePrice(itemWebRespDTO.getLinePrice());
                    }
                }
            });
            item.setSkuList(finalSku);
        }
    }

    @Override
    public Boolean checkTypePricePlan(ItemQueryReqDTO itemQueryReqDTO) {
        Assert.hasLength(itemQueryReqDTO.getTypeGuid(), "分类Guid为空！");
        List<PricePlanItemDO> planItemDOS = this.list(new LambdaQueryWrapper<PricePlanItemDO>().eq(PricePlanItemDO::getTypeGuid, itemQueryReqDTO.getTypeGuid()));
        planItemDOS.removeIf(i -> itemQueryReqDTO.getFilterItemList().contains(i.getItemGuid()));
        if (CollectionUtils.isEmpty(planItemDOS)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public List<PricePlanItemDO> saveUpdatePlanItem(PricePlanItemAddReqDTO reqDTO, Map<String, String> saveTypeMap, boolean isCopy) {
        log.info("是否复制 isCopy：{}", isCopy);
        List<ItemWebRespDTO> itemWebList = reqDTO.getItemWebRespDTOList();
        String planGuid = reqDTO.getPlanGuid();
        if (CollectionUtils.isEmpty(itemWebList)) {
            return new ArrayList<>();
        }

        // 立即上架 默认值
        itemWebList.forEach(i -> {
            if (Objects.isNull(i.getIsSoldOut())) {
                i.setIsSoldOut(ItemStateEnum.ON_RACK.getCode());
            }
        });

        // 处理立即下架（直接删除）的商品
        List<String> toDelete = itemWebList.stream()
                .filter(i -> Objects.equals(ItemStateEnum.IMMEDIATELY_DELETE.getCode(), i.getIsSoldOut()) || Objects.equals(ItemStateEnum.IS_ABOUT_TO_DELETE.getCode(), i.getIsSoldOut()))
                .map(ItemWebRespDTO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(toDelete)) {
            this.remove(new LambdaQueryWrapper<PricePlanItemDO>().in(PricePlanItemDO::getItemGuid, toDelete).eq(PricePlanItemDO::getPlanGuid, planGuid));
            itemWebList.removeIf(i -> toDelete.contains(i.getItemGuid()));
        }

        List<PricePlanItemDO> itemDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(itemWebList)) {
            // 处理多规格删除规格的商品（外层没有，里面有），不需要更新商品数
            List<String> toDeleteSku = itemWebList.stream()
                    .filter(e -> CollectionUtils.isNotEmpty(e.getSkuList()))
                    .flatMap(e -> e.getSkuList().stream())
                    .filter(e -> Objects.nonNull(e.getIsSoldOut()) && Objects.equals(ItemStateEnum.IMMEDIATELY_DELETE.getCode(), e.getIsSoldOut()))
                    .map(SkuRespDTO::getSkuGuid).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(toDeleteSku)) {
                log.info("删除多规格商品部分规格：{}", toDeleteSku);
                this.remove(new LambdaQueryWrapper<PricePlanItemDO>().in(PricePlanItemDO::getSkuGuid, toDeleteSku).eq(PricePlanItemDO::getPlanGuid, planGuid));
            }

            // 组装方案菜品保存对象
            itemWebList.forEach(itemWeb -> {
                if (CollectionUtils.isEmpty(itemWeb.getSkuList())) {
                    return;
                }
                for (SkuRespDTO skuRespDTO : itemWeb.getSkuList()) {
                    if (Objects.nonNull(skuRespDTO.getIsSoldOut()) && Objects.equals(ItemStateEnum.IMMEDIATELY_DELETE.getCode(), skuRespDTO.getIsSoldOut())) {
                        log.info("规格将删除：{}", skuRespDTO.getSkuGuid());
                        continue;
                    }
                    PricePlanItemDO itemDO = new PricePlanItemDO();
                    itemDO.setPlanGuid(planGuid);
                    itemDO.setBrandGuid(reqDTO.getBrandGuid());
                    itemDO.setItemGuid(itemWeb.getItemGuid());
                    itemDO.setSkuGuid(skuRespDTO.getSkuGuid());
                    itemDO.setSalePrice(skuRespDTO.getSalePrice());
                    itemDO.setMemberPrice(skuRespDTO.getMemberPrice());
                    itemDO.setAccountingPrice(skuRespDTO.getAccountingPrice());
                    itemDO.setTakeawayAccountingPrice(skuRespDTO.getTakeawayAccountingPrice());
                    itemDO.setLinePrice(skuRespDTO.getLinePrice());
                    itemDO.setIsSoldOut(itemWeb.getIsSoldOut());
                    itemDO.setDescription(itemWeb.getDescription());
                    itemDO.setEnglishBrief(itemWeb.getEnglishBrief());
                    itemDO.setEnglishIngredientsDesc(itemWeb.getEnglishIngredientsDesc());
                    itemDO.setPictureUrl(itemWeb.getPictureUrl());
                    itemDO.setBigPictureUrl(itemWeb.getBigPictureUrl());
                    itemDO.setDetailBigPictureUrl(itemWeb.getDetailBigPictureUrl());

                    itemDO.setPlanItemName(itemWeb.getPlanItemName());
                    itemDO.setSort(itemWeb.getSort());
                    if (ObjectUtils.isEmpty(itemDO.getIsSoldOut())) {
                        itemDO.setIsSoldOut(ItemStateEnum.ON_RACK.getCode());
                    }

                    //是否为新增
                    if (StringUtils.isEmpty(skuRespDTO.getPlanItemGuid()) || isCopy) {
                        // 先查询一下数据里有没有再考虑生成guid
                        List<PricePlanItemDO> list = this.list(new LambdaQueryWrapper<PricePlanItemDO>()
                                .eq(PricePlanItemDO::getPlanGuid, planGuid)
                                .eq(PricePlanItemDO::getSkuGuid, skuRespDTO.getSkuGuid())
                                .eq(PricePlanItemDO::getIsDelete, 0)
                                .ne(PricePlanItemDO::getIsSoldOut, ItemStateEnum.IMMEDIATELY_DELETE.getCode()));
                        if (CollectionUtils.isEmpty(list)) {
                            itemDO.setGuid(GUIDUtils.generateGuid(GuidKeyConstant.HSI_PRICEPLAN_ITEM));
                        } else {
                            itemDO.setGuid(list.get(0).getGuid());
                        }
                    } else {
                        itemDO.setGuid(skuRespDTO.getPlanItemGuid());
                    }
                    if (ObjectUtil.isNotNull(itemWeb.getItemType())) {
                        itemDO.setIsPkgItem(itemWeb.getItemType().equals(ItemTypeEnum.PACKAGE.getTypeCode()));
                    }

                    // 是否最新编辑的商品
                    if (Objects.isNull(itemWeb.getNewupdateflag())) {
                        itemDO.setNewUpdateFlag(Boolean.FALSE);
                    } else {
                        itemDO.setNewUpdateFlag(itemWeb.getNewupdateflag());
                    }

                    //分类Guid不为空且不是编辑时复制新方案菜品直接保存
                    if (!StringUtils.isEmpty(itemWeb.getTypeGuid()) && !isCopy) {
                        itemDO.setTypeGuid(itemWeb.getTypeGuid());
                    } else {
                        String typeGuid = saveTypeMap.get(itemWeb.getTypeName());
                        if (StringUtils.isEmpty(typeGuid)) {
                            log.warn("菜谱保存时typeGuid为空,不保存商品 typeName={}", itemWeb.getTypeName());
                            continue;
                        } else {
                            itemDO.setTypeGuid(typeGuid);
                        }
                    }
                    itemDO.setGmtModified(LocalDateTime.now());
                    itemDOList.add(itemDO);
                }
            });
        }

        // 查询db
        List<PricePlanItemDO> dbList = this.list(new LambdaQueryWrapper<PricePlanItemDO>()
                .eq(PricePlanItemDO::getPlanGuid, reqDTO.getPlanGuid())
        );
        // 编排sort
        pricePlanItemSorted(dbList, itemDOList);
        // 合并商品
        List<PricePlanItemDO> finalList = mergePricePlanItemList(dbList, itemDOList);
        if (CollectionUtils.isNotEmpty(finalList)) {
            Map<String, Integer> sortMap = finalList.stream().collect(Collectors.toMap(PricePlanItemDO::getGuid, PricePlanItemDO::getSort));
            log.info("排序完成之后商品sort:{}", JacksonUtils.writeValueAsString(sortMap));

            //需要对dbList去重根据planGuid,itemGuid,skuGuid,typeGuid
            List<PricePlanItemDO> collect = finalList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                    new TreeSet<>(Comparator.comparing(o ->
                            o.getPlanGuid() + o.getItemGuid() + o.getSkuGuid() + o.getTypeGuid()))), ArrayList::new));
            this.saveOrUpdateBatch(collect);
        }
        return itemDOList;
    }

    private List<PricePlanItemDO> mergePricePlanItemList(List<PricePlanItemDO> dbList, List<PricePlanItemDO> itemDOList) {
        Map<String, PricePlanItemDO> dbMap = dbList.stream().collect(Collectors.toMap(PricePlanItemDO::getGuid, Function.identity(), (key1, key2) -> key2));
        for (PricePlanItemDO planItemDO : itemDOList) {
            dbMap.remove(planItemDO.getGuid());
        }
        if (MapUtils.isNotEmpty(dbMap)) {
            itemDOList.addAll(dbMap.values());
        }
        return itemDOList;
    }

    private void pricePlanItemSorted(List<PricePlanItemDO> dbList, List<PricePlanItemDO> memoryList) {
        if (CollectionUtils.isEmpty(dbList) && CollectionUtils.isEmpty(memoryList)) {
            return;
        }
        // 根据菜谱分类进行分组
        Map<String, List<PricePlanItemDO>> dbMap = dbList.stream().collect(Collectors.groupingBy(PricePlanItemDO::getTypeGuid));
        Map<String, List<PricePlanItemDO>> memoryMap = memoryList.stream().collect(Collectors.groupingBy(PricePlanItemDO::getTypeGuid));
        for (Map.Entry<String, List<PricePlanItemDO>> entry : dbMap.entrySet()) {
            String typeGuid = entry.getKey();
            List<PricePlanItemDO> dbSkuList = entry.getValue();
            List<PricePlanItemDO> memorySkuList = memoryMap.get(typeGuid);
            // 分类内排序
            pricePlanItemSortedUpdate(dbSkuList, memorySkuList);
            memoryMap.remove(typeGuid);
        }

        if (MapUtils.isEmpty(memoryMap)) {
            return;
        }
        log.info("排序处理新增的商品分类, memoryMap:{}", JacksonUtils.writeValueAsString(memoryMap.keySet()));
        // 处理此次新增的
        pricePlanItemSortedCreate(memoryMap);
    }

    private void pricePlanItemSortedUpdate(List<PricePlanItemDO> dbSkuList, List<PricePlanItemDO> memorySkuList) {
        if (CollectionUtils.isEmpty(memorySkuList)) {
            return;
        }
        Map<String, PricePlanItemDO> memorySkuMap = memorySkuList.stream()
                .collect(Collectors.toMap(PricePlanItemDO::getGuid, Function.identity(), (key1, key2) -> key1));
        List<PricePlanItemDO> tempDbPricePlanItemList = Lists.newArrayList();
        for (PricePlanItemDO pricePlanItemDO : dbSkuList) {
            PricePlanItemDO memoryItem = memorySkuMap.get(pricePlanItemDO.getGuid());
            if (Objects.isNull(memoryItem)) {
                tempDbPricePlanItemList.add(pricePlanItemDO);
            }
        }
        // 先把不修改序号的放入
        List<PricePlanItemDO> allPricePlanItemList = Lists.newArrayList(tempDbPricePlanItemList);
        // 不修改序号的进行从小到大排序
        reSetPricePlanItemListSort(allPricePlanItemList);
        // 将需要修改的按sort排序
        List<PricePlanItemDO> tempMemoryPricePlanItemList = new ArrayList<>(memorySkuMap.values());
        tempMemoryPricePlanItemList = tempMemoryPricePlanItemList.stream()
                .sorted(Comparator.comparing(PricePlanItemDO::getSort, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
        // 依次插入
        for (PricePlanItemDO pricePlanItemDO : tempMemoryPricePlanItemList) {
            allPricePlanItemList.add(pricePlanItemDO);
            reSetPricePlanItemListSort(allPricePlanItemList);
        }
        log.info("同分类下商品排序:{}", JacksonUtils.writeValueAsString(allPricePlanItemList));
    }

    /**
     * 重新设置排序值
     */
    private void reSetPricePlanItemListSort(List<PricePlanItemDO> allPricePlanItemList) {
        allPricePlanItemList = allPricePlanItemList.stream()
                .sorted(Comparator.comparing(PricePlanItemDO::getSort, Comparator.nullsLast(Integer::compareTo))
                        .thenComparing(PricePlanItemDO::getId, Comparator.nullsFirst(Long::compareTo)))
                .collect(Collectors.toList());
        for (int i = 0; i < allPricePlanItemList.size(); i++) {
            // 多规格商品排序值根据spu来
            if (i == 0) {
                allPricePlanItemList.get(0).setSort(1);
                continue;
            }
            PricePlanItemDO prePricePlanItemDO = allPricePlanItemList.get(i - 1);
            PricePlanItemDO currentPrePricePlanItemDO = allPricePlanItemList.get(i);
            if (prePricePlanItemDO.getItemGuid().equals(currentPrePricePlanItemDO.getItemGuid())) {
                // 如果是同一个item,排序值一样
                currentPrePricePlanItemDO.setSort(prePricePlanItemDO.getSort());
            } else {
                currentPrePricePlanItemDO.setSort(prePricePlanItemDO.getSort() + 1);
            }
        }
    }

    private void pricePlanItemSortedCreate(Map<String, List<PricePlanItemDO>> memoryMap) {
        // 处理此次新增的
        for (Map.Entry<String, List<PricePlanItemDO>> entry : memoryMap.entrySet()) {
            List<PricePlanItemDO> memorySkuList = entry.getValue();
            if (CollectionUtils.isNotEmpty(memorySkuList)) {
                for (int i = 0; i < memorySkuList.size(); i++) {
                    if (Objects.isNull(memorySkuList.get(i).getSort())) {
                        memorySkuList.get(i).setSort(i + 1);
                    }
                }
                List<String> memoryItemGuidSortedList = itemGuidListSorted(memorySkuList);
                rePricePlanItemSorted(memorySkuList, memoryItemGuidSortedList);
            }
        }
    }

    private void rePricePlanItemSorted(List<PricePlanItemDO> skuList, List<String> dbItemGuidSortedList) {
        Map<String, Integer> beforeSortMap = skuList.stream().collect(Collectors.toMap(PricePlanItemDO::getSkuGuid, PricePlanItemDO::getSort));
        log.info("排序之前sort:{}", JacksonUtils.writeValueAsString(beforeSortMap));
        skuList.forEach(e -> {
            int index = dbItemGuidSortedList.indexOf(e.getItemGuid());
            if (index == -1) {
                log.error("重新编排sort有误，dbItemGuidSortedList：{}，e.getItemGuid：{}",
                        JacksonUtils.writeValueAsString(dbItemGuidSortedList), e.getItemGuid());
            }
            e.setSort(index + 1);
        });
        Map<String, Integer> afterSortMap = skuList.stream().collect(Collectors.toMap(PricePlanItemDO::getSkuGuid, PricePlanItemDO::getSort));
        log.info("排序之后sort:{}", JacksonUtils.writeValueAsString(afterSortMap));
    }

    private List<String> itemGuidListSorted(List<PricePlanItemDO> dbSkuList) {
        List<PricePlanItemDO> dbSortSkuList = dbSkuList.stream()
                .sorted(Comparator.comparing(PricePlanItemDO::getSort, Comparator.nullsLast(Integer::compareTo))
                        .thenComparing(PricePlanItemDO::getGuid))
                .collect(Collectors.toList());
        List<String> itemGuidList = Lists.newArrayList();
        dbSortSkuList.forEach(e -> {
            if (!itemGuidList.contains(e.getItemGuid())) {
                itemGuidList.add(e.getItemGuid());
            }
        });
        return itemGuidList;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<TypeItemRespDTO> findPricePlanItemList(ItemSingleDTO itemSingleDTO) {
        //查询品牌库分类集合
        List<TypeDO> typeDOList = typeMapper.selectList(new LambdaQueryWrapper<TypeDO>()
                .eq(TypeDO::getBrandGuid, itemSingleDTO.getData())
                .eq(TypeDO::getTypeFrom, 1)
                .orderByAsc(TypeDO::getSort));
        if (CollectionUtils.isEmpty(typeDOList)) {
            throw new BusinessException("请先创建分类");
        }

        LambdaQueryWrapper<SkuDO> wrapper = new LambdaQueryWrapper<SkuDO>()
                .eq(SkuDO::getIsDelete, FALSE)
                .eq(SkuDO::getIsEnable, Boolean.TRUE)
                .eq(SkuDO::getIsRack, 1);

        //菜谱方案guid不为空 排除已有商品规格
        String planGuid = itemSingleDTO.getPlanGuid();
        if (!StringUtils.isEmpty(planGuid)) {
            List<PricePlanItemDO> planItemDOS = list(new LambdaQueryWrapper<PricePlanItemDO>()
                    .eq(PricePlanItemDO::getPlanGuid, planGuid)
                    .eq(PricePlanItemDO::getIsDelete, Boolean.FALSE));
            if (CollectionUtils.isNotEmpty(planItemDOS)) {
                List<String> skuGuids = planItemDOS.stream().map(PricePlanItemDO::getSkuGuid).distinct().collect(Collectors.toList());
                wrapper.notIn(SkuDO::getGuid, skuGuids);
            }
        }

        // 要显示的规格集合
        List<SkuDO> skuDOList = skuService.list(wrapper);
        if (CollectionUtils.isEmpty(skuDOList)) {
            log.warn("要显示的规格为空");
            return new ArrayList<>();
        }

        // 处理前端传入需要显示出来的商品
        if (!CollectionUtils.isEmpty(itemSingleDTO.getSkuGuids())) {
            List<SkuDO> skuList = skuService.list(new LambdaQueryWrapper<SkuDO>().in(SkuDO::getGuid, itemSingleDTO.getSkuGuids()));
            skuDOList.addAll(skuList);
        }

        // 要显示的商品集合
        List<String> itemGuidList = skuDOList.stream().map(SkuDO::getItemGuid).distinct().collect(Collectors.toList());
        List<ItemDO> itemDOList = itemMapper.selectList(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getGuid, itemGuidList)
                .eq(ItemDO::getIsDelete, FALSE)
                .ne(ItemDO::getItemType, ItemType.GROUP)
                .like(StringUtils.hasText(itemSingleDTO.getKeywords()), ItemDO::getName, itemSingleDTO.getKeywords())
        );
        if (org.springframework.util.CollectionUtils.isEmpty(itemDOList)) {
            log.warn("当前无可用商品");
            return new ArrayList<>();
        }

        List<TypeItemRespDTO> typeSkuRespDTOList = MapStructUtils.INSTANCE.typeDOList2typeItemRespDTOList(typeDOList);
        Iterator<TypeItemRespDTO> typeSkuRespDTOIterator = typeSkuRespDTOList.iterator();
        while (typeSkuRespDTOIterator.hasNext()) {
            TypeItemRespDTO typeDTO = typeSkuRespDTOIterator.next();
            List<ItemWebRespDTO> itemWebRespDTOList = new ArrayList<>();

            // 当前分类下的商品集合
            List<ItemDO> itemDOS = itemDOList.stream()
                    .filter(itemDO -> Objects.equals(typeDTO.getTypeGuid(), itemDO.getTypeGuid()))
                    .collect(Collectors.toList());

            if (org.springframework.util.CollectionUtils.isEmpty(itemDOS)) {
                typeSkuRespDTOIterator.remove();
            } else {
                itemDOS.forEach(itemDO -> {
                    // 当前商品下的规格集合
                    List<SkuDO> skuDOS = skuDOList.stream()
                            .filter(skuDO -> Objects.equals(itemDO.getGuid(), skuDO.getItemGuid()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(skuDOS)) {
                        log.info("商品已被选中：{}", itemDO.getGuid());
                        return;
                    }
                    ItemWebRespDTO itemWebRespDTO = MapStructUtils.INSTANCE.itemDO2itemWebRespDTO(itemDO);
                    itemWebRespDTO.setSkuList(MapStructUtils.INSTANCE.skuDOList2skuRespDTOList(skuDOS));
                    itemWebRespDTOList.add(itemWebRespDTO);
                });
                typeDTO.setItemWebRespDTOList(itemWebRespDTOList);
            }
        }

        return typeSkuRespDTOList;
    }

    /**
     * 根据方案guid查询绑定门店信息
     *
     * @param itemSingleDTO 菜谱方案Guid
     * @return List<StoreDTO>
     */
    @Override
    public List<StoreDTO> queryPlanStoreListByPlan(ItemSingleDTO itemSingleDTO) {
        if (ObjectUtils.isEmpty(itemSingleDTO.getPlanGuid())) {
            throw new BusinessException(PRICE_PLAN_CANNOT_BE_EMPTY);
        }
        List<String> storeGuidList = planStoreMapper.getPlanStoreGuidList(itemSingleDTO.getPlanGuid(), itemSingleDTO.getFilterGuidList());
        if (CollectionUtils.isEmpty(storeGuidList)) {
            return new ArrayList<>();
        }
        return organizationService.queryStoreByIdList(storeGuidList);
    }

    @Override
    public void updatePlanItem(String planGuid, String parentGuid) {
        //删除原菜谱方案商品
        this.remove(new LambdaUpdateWrapper<PricePlanItemDO>().eq(PricePlanItemDO::getPlanGuid, parentGuid));
        //将新菜谱方案商品关联原菜谱方案
        this.update(new LambdaUpdateWrapper<PricePlanItemDO>().set(PricePlanItemDO::getPlanGuid, parentGuid).eq(PricePlanItemDO::getPlanGuid, planGuid));
    }

    /**
     * 通过菜谱guid预览菜谱
     *
     * @param reqDTO 菜谱guid,浏览模式
     * @return 预览的菜谱方案信息
     */
    @Override
    public PreviewPlanRespDTO previewPlanByGuid(PreviewPlanReqDTO reqDTO) {
        PreviewPlanRespDTO respDTO = new PreviewPlanRespDTO();
        if (StringUtils.isEmpty(reqDTO.getPlanGuid())) {
            throw new BusinessException("方案guid不能为空");
        }
        if (ObjectUtils.isEmpty(reqDTO.getBrowseType())) {
            throw new BusinessException("浏览模式不能为空");
        }

        // 展示按钮的状态：已启用、即将启用、暂时停用
        PricePlanDO pricePlanDO = planMapper.selectOne(new LambdaQueryWrapper<PricePlanDO>()
                .eq(PricePlanDO::getGuid, reqDTO.getPlanGuid())
                .eq(PricePlanDO::getIsDelete, BooleanEnum.FALSE.getCode())
                .in(PricePlanDO::getStatus, PricePlanStatusEnum.USING.getCode(), PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode(),
                        PricePlanStatusEnum.NOT_ENABLED.getCode()));
        if (ObjectUtils.isEmpty(pricePlanDO) || ObjectUtils.isEmpty(pricePlanDO.getGuid())) {
            log.warn("未查询到对应方案：{}", reqDTO.getPlanGuid());
            return respDTO;
        }
        respDTO.setPlanGuid(pricePlanDO.getGuid());
        respDTO.setPlanName(pricePlanDO.getName());

        // 设置品牌信息
        respDTO.setBrandGuid(pricePlanDO.getBrandGuid());
        BrandDTO brandDTO = organizationService.queryBrandByGuid(pricePlanDO.getBrandGuid());
        if (Objects.nonNull(brandDTO) && Objects.nonNull(brandDTO.getName())) {
            respDTO.setBrandName(brandDTO.getName());
        }
        if (Objects.nonNull(brandDTO) && Objects.nonNull(brandDTO.getLogoUrl())) {
            respDTO.setBrandLogoUrl(brandDTO.getLogoUrl());
        }

        // 二维码生成-由前端生成
        /*String content = "https://www.baidu.com/";
        String redisKey = PREVIEW_PLAN + reqDTO.getPlanGuid() + NOW_TIME_LONG;
        try {
            respDTO.setQrCode(qrcodeServer.display(redisKey));
        } catch (Exception e) {
            qrcodeServer.generate(content, redisKey, 300, 300, 60 * 60);
            respDTO.setQrCode(qrcodeServer.display(redisKey));
        }*/

        // 根据方案查询关系
        LambdaQueryWrapper<PricePlanItemDO> wrapper = new LambdaQueryWrapper<PricePlanItemDO>()
                .eq(PricePlanItemDO::getPlanGuid, pricePlanDO.getGuid())
                .eq(PricePlanItemDO::getIsDelete, BooleanEnum.FALSE.getCode())
                .ne(PricePlanItemDO::getIsSoldOut, ItemStateEnum.IMMEDIATELY_DELETE.getCode());
        if (2 == reqDTO.getBrowseType()) {
            wrapper.eq(PricePlanItemDO::getNewUpdateFlag, BooleanEnum.TRUE.getCode());
        }
        List<PricePlanItemDO> planItemDOList = this.list(wrapper);
        if (CollectionUtils.isEmpty(planItemDOList)) {
            log.warn("方案未绑定商品或者没有最新编辑商品：{}", pricePlanDO.getGuid());
            return respDTO;
        }

        // 根据关系查询分类
        List<String> typeGuidList = planItemDOList.stream().map(PricePlanItemDO::getTypeGuid).distinct().collect(Collectors.toList());
        List<TypeDO> typeDOList = typeMapper.selectBatchIds(typeGuidList);
        if (CollectionUtils.isEmpty(typeDOList)) {
            log.warn("未查询到分类数据：{}", typeGuidList);
            return respDTO;
        }

        // 分类顺序要按照sort来
        typeDOList.sort(Comparator.comparing(TypeDO::getSort));
        List<String> typeList = typeDOList.stream().map(TypeDO::getGuid).collect(Collectors.toList());

        // 根据关系查询商品
        List<String> itemGuidList = planItemDOList.stream().map(PricePlanItemDO::getItemGuid).distinct().collect(Collectors.toList());
        List<ItemDO> itemDOList = itemMapper.selectBatchIds(itemGuidList);
        if (CollectionUtils.isEmpty(itemDOList)) {
            log.warn("未查询到商品数据：{}", itemGuidList);
            return respDTO;
        }

        // 根据关系查询规格
        List<String> skuGuidList = planItemDOList.stream().map(PricePlanItemDO::getSkuGuid).distinct().collect(Collectors.toList());
        List<SkuDO> skuDOList = new ArrayList<>(skuService.listByIds(skuGuidList));
        if (CollectionUtils.isEmpty(skuDOList)) {
            log.warn("未查询到规格数据：{}", skuGuidList);
            return respDTO;
        }

        // map准备
        Map<String, List<PricePlanItemDO>> typeMap = planItemDOList.stream().collect(Collectors.groupingBy(PricePlanItemDO::getTypeGuid));
        Map<String, TypeDO> typeDOMap = typeDOList.stream().collect(Collectors.toMap(TypeDO::getGuid, t -> t));
        Map<String, ItemDO> itemDOMap = itemDOList.stream().collect(Collectors.toMap(ItemDO::getGuid, i -> i));
        Map<String, List<SkuDO>> skuMap = skuDOList.stream().collect(Collectors.groupingBy(SkuDO::getItemGuid));
        Map<String, SkuDO> skuDOMap = skuDOList.stream().collect(Collectors.toMap(SkuDO::getGuid, s -> s));

        // 组装返回信息
        List<PricePlanItemTypeRespDTO> typeRespList = new ArrayList<>();
        // 组装分类
        typeList.forEach(typeGuid -> {
            PricePlanItemTypeRespDTO typeRespDTO = new PricePlanItemTypeRespDTO();
            typeRespDTO.setTypeGuid(typeGuid);
            typeRespDTO.setTypeName(typeDOMap.get(typeGuid).getName());

            // 组装商品
            List<PricePlanItemDO> planItemList = typeMap.get(typeGuid);
            planItemList.sort(Comparator.comparing(PricePlanItemDO::getSalePrice));

            // 删除多规格多余的商品数据，只取销售价最低的
            for (int i = planItemList.size() - 1; i >= 0; i--) {
                for (int j = planItemList.size() - 1; j > i; j--) {
                    if (planItemList.get(j).getItemGuid().equals(planItemList.get(i).getItemGuid())) {
                        // 删除重复元素
                        planItemList.remove(j);
                    }
                }
            }

            // 根据sort排序
            planItemList.sort(Comparator.comparing(PricePlanItemDO::getSort));

            List<PricePlanItemRespDTO> planItemRespDTOList = new ArrayList<>();
            planItemList.forEach(planItemDO -> {
                PricePlanItemRespDTO itemRespDTO = new PricePlanItemRespDTO();
                ItemDO brandItemDO = itemDOMap.get(planItemDO.getItemGuid());
                itemRespDTO.setItemGuid(planItemDO.getItemGuid());
                itemRespDTO.setTypeGuid(typeGuid);
                itemRespDTO.setItemType(brandItemDO.getItemType());
                itemRespDTO.setNewUpdateFlag(planItemDO.getNewUpdateFlag());
                itemRespDTO.setSalePrice(planItemDO.getSalePrice());

                // 菜谱部分数据：会员价
                if (ObjectUtils.isEmpty(planItemDO.getMemberPrice())) {
                    itemRespDTO.setMemberPrice(null);
                } else {
                    itemRespDTO.setMemberPrice(planItemDO.getMemberPrice());
                }

                // 菜谱部分数据：名字
                if (!ObjectUtils.isEmpty(planItemDO.getPlanItemName())) {
                    itemRespDTO.setItemName(planItemDO.getPlanItemName());
                } else {
                    itemRespDTO.setItemName(brandItemDO.getName());
                }

                // 菜谱部分数据：描述
                if (!ObjectUtils.isEmpty(planItemDO.getDescription())) {
                    itemRespDTO.setDescription(planItemDO.getDescription());
                } else {
                    itemRespDTO.setDescription("");
                }

                // 菜谱部分数据：图片
                if (!ObjectUtils.isEmpty(planItemDO.getPictureUrl())) {
                    itemRespDTO.setPictureUrl(planItemDO.getPictureUrl());
                } else {
                    itemRespDTO.setPictureUrl("");
                }

                // 规格里面的数据，显示售价最低的那条
                SkuDO skuDO = skuDOMap.get(planItemDO.getSkuGuid());
                itemRespDTO.setMinOrderNum(skuDO.getMinOrderNum());
                itemRespDTO.setOriginalPrice(skuDO.getSalePrice());

                planItemRespDTOList.add(itemRespDTO);
            });

            typeRespDTO.setPlanItemRespDTOList(planItemRespDTOList);
            typeRespList.add(typeRespDTO);
        });
        respDTO.setPlanItemTypeRespDTOList(typeRespList);
        return respDTO;
    }

    @Override
    public List<PlanPriceAllTypeRespDTO> listAllPlanPriceItem(PlanPriceAllItemReqDTO req) {
        List<PlanPriceAllItemRespDTO> itemRespDTOList = planItemMapper.listAllPlanPriceItem(req);
        if (CollectionUtils.isEmpty(itemRespDTOList)) {
            log.info("方案商品查询为空 req={}", JacksonUtils.writeValueAsString(req));
            return new ArrayList<>();
        }
        Map<String, List<PlanPriceAllItemRespDTO>> typeItemMap = itemRespDTOList.stream().collect(Collectors.groupingBy(PlanPriceAllItemRespDTO::getTypeGuid));
        List<String> typeGuidList = itemRespDTOList.stream().map(PlanPriceAllItemRespDTO::getTypeGuid).distinct().collect(Collectors.toList());

        List<TypeDO> typeDOList = typeMapper.selectBatchIds(typeGuidList);
        typeDOList.sort(Comparator.comparing(TypeDO::getSort));
        List<PlanPriceAllTypeRespDTO> typeRespDTOList = MapStructUtils.INSTANCE.typeDOList2TypeDTOList(typeDOList);
        typeRespDTOList.forEach(type -> type.setItemList(typeItemMap.get(type.getTypeGuid())));
        return typeRespDTOList;
    }

    @Override
    public List<TypeRespDTO> listAllPlanPriceItemType(PlanPriceAllItemReqDTO req) {
        List<String> typeGuidList = planItemMapper.listAllPlanPriceItemTypeGuid(req.getBrandGuid());
        if (CollectionUtils.isEmpty(typeGuidList)) {
            log.info("方案商品分类为空 req={}", JacksonUtils.writeValueAsString(req));
            return Lists.newArrayList();
        }
        List<TypeDO> typeDOList = typeMapper.selectBatchIds(typeGuidList);
        Map<String, TypeDO> typeDOMap = typeDOList.stream()
                .collect(Collectors.toMap(TypeDO::getName, Function.identity(), (key1, key2) -> key1));
        typeDOList = typeDOMap.values().stream()
                .sorted(Comparator.comparing(TypeDO::getName))
                .collect(Collectors.toList());
        return MapStructUtils.INSTANCE.typeDO2TypeRespDTOList(typeDOList);
    }

    @Override
    public ItemSkuAndPlanPriceDTO listItemSkuAndPlanPrice(String itemGuid) {
        if (StringUtils.isEmpty(itemGuid)) {
            throw new BusinessException("商品guid不能为空");
        }
        return planItemMapper.listItemSkuAndPlanPrice(itemGuid);
    }

    @Override
    public List<PlanPriceEditDTO> listItemTypeAndPlanPrice(String typeName) {
        return planItemMapper.listItemTypeAndPlanPrice(typeName);
    }

    @Override
    public com.holderzone.framework.util.Page<PlanPriceAllItemSkuRespDTO> listAllPlanPriceItemSku(PlanPriceAllItemSkuReqDTO req) {
        Page<PlanPriceAllItemSkuRespDTO> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        List<PlanPriceAllItemSkuRespDTO> list = planItemMapper.listAllPlanPriceItemSku(req, page);
        page.setRecords(list);
        page.setTotal(planItemMapper.listAllPlanPriceItemSkuCount(req));
        return PageUtils.convert(page);
    }

    @Override
    public void saveBatchEditPlanPrice(PlanPriceEditReqDTO req) {

    }
}
