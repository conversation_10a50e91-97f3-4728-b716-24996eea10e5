package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.saas.store.dto.item.common.ItemDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.item.entity.bo.GroupMealSubItemBO;
import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.TypeDO;
import com.holderzone.saas.store.item.entity.query.JournalingItemsQuery;
import com.holderzone.saas.store.item.helper.PageAdapter;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 商品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Component
public interface ItemMapper extends BaseMapper<ItemDO> {

    IPage<ItemTemplateSubItemRespDTO> getSkuItemList(PageAdapter page, @Param("dto") ItemTemplateMenuAllSubItemReqDTO request);

    List<String> getItemTemplateGuid(@Param("list") List<String> list);

    List<JournalingItemsQuery> getJournalingItem();

    List<GroupMealSubItemBO> getGroupMealSubItemS(@Param("itemGuid") String itemGuid);

    List<GroupMealSubItemBO> getListGroupMealSubItemS(@Param("list") List<String> itemGuidS);

    List<ItemBatchImportTempRespDTO> getItemsBeforeImport(@Param("dto") BatchImportGetItemsReqDTO request);

    Boolean updateHasAttr(@Param("itemGuid") String itemGuid, @Param("hasAttr") Integer hasAttr);

    List<String> getHasAttrItemGuid();

    Integer deleteByGuid(String guid);

    Integer voteUp(String guid);

    Integer voteDown(String guid);

    List<Map<String, Object>> getItemInfoListByParentId(@Param("parentItemGuid") String parentItemGuid, @Param("storeGuid") String storeGuid);

    Integer removeItemByParentId(@Param("parentItemGuid") String parentItemGuid, @Param("storeGuid") String storeGuid);

    String getGuidByStoreGuidAndParentGuid(@Param("storeGuid") String storeGuid, @Param("parentGuid") String parentGuid);

    Integer deleteByGuidAndFrom(@Param("guid") String guid, @Param("itemFrom") Long itemFrom, @Param("storeGuid") String storeGuid);

    Integer updateOriginItemStoreGuid(@Param("itemGuid") String itemGuid, @Param("storeGuid") String storeGuid, @Param("brandGuid") String brandGuid);

    List<Map<String, Object>> getItemsByStoreGuid(@Param("storeGuid") String storeGuid);

    // 价格方案状态矫正将不在列表中的skuGuid设置为false;
    Integer correctStoreItemIsEnable(@Param("itemGuidList") List<String> itemGuidToEnableList, @Param("storeGuidList") List<String> storeGuidList);

    Integer correctStoreSkuIsEnable(@Param("skuGuidList") List<String> skuGuidToEnableList, @Param("storeGuidList") List<String> storeGuidList);

    //品牌推送状态矫正
    Integer correctStoreItemIsEnableByBrandPush(@Param("itemGuidList") List<String> itemGuidToEnableList, @Param("storeGuidList") List<String> storeGuidList);

    Integer correctStoreSkuIsEnableByBrandPush(@Param("skuGuidList") List<String> skuGuidToEnableList, @Param("storeGuidList") List<String> storeGuidList);

    // 根据品牌套餐子菜品guid查询套餐所在门店列表,用于校验门店分配错误
    List<String> selectStoreGuidListByPackageChildItemGuid(@Param("itemGuid") String itemGuid);

    List<String> selectPackageNameListByPackageChildItemGuid(@Param("itemGuid") String itemGuid, @Param("storeGuids") List<String> storeGuids);

    Integer correctStoreItemByPlanPriceGuid(
            @Param("storeGuidList") List<String> storeGuidList,
            @Param("planPriceGuid") String planPriceGuid);

    Integer correctStoreSkuByPlanPriceGuid(
            @Param("storeGuidList") List<String> storeGuidList,
            @Param("planPriceGuid") String planPriceGuid
    );

    Integer updateStoreItemByPlanPriceGuid(
            @Param("itemGuidList") List<String> itemGuidToEnableList,
            @Param("storeGuidList") List<String> storeGuidList,
            @Param("planPriceGuid") String planPriceGuid);

    List<TypeDO> selectItemTypeGuidList(@Param("itemGuidList") List<String> itemGuidList);

    List<ItemInfoRespDTO> selectItemWithParentGuidList(@Param("itemGuidList") List<String> itemGuidList);

    /**
     * 微信搜索商品
     *
     * @param page 分页数据
     * @param dto  参数
     * @return 查询结果
     */
    IPage<ItemDO> wxSearchItems(PageAdapter page, @Param("dto") WxSearchItemDto dto);

    List<SelectItemDTO> selectItemList(ItemQueryDTO itemQueryDTO);

    List<ItemExportRespDTO> selectExportItemList(ItemExportReqDTO itemExportReqDTO);

    /**
     * 通过guid集合查询商品信息）（包含删除的）
     *
     * @param itemGuidList 商品guid集合
     * @return 商品集合
     */
    List<ItemDO> findByGuids(@Param("itemGuidList") List<String> itemGuidList);

    /**
     * 通过分类查询已上架商品
     *
     * @param typeGuidList 分类
     * @return已上架商品
     */
    List<ItemDO> listRackItemByType(@Param("typeGuidList") List<String> typeGuidList, @Param("isRack") Integer isRack);

    int countByGuidSet(@Param("itemGuidList") Set<String> itemGuidSet);

    List<ItemDTO> listBaseItem(@Param("itemGuidList")List<String> itemGuidList);

    String getSubItemGuid(@Param("parentGuid")String parentGuid,@Param("storeGuid")String storeGuid);

    List<ItemInfoRespDTO> getItemNameList(@Param("req") ItemStringListDTO req);
}
