package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.item.entity.bo.ItemRelateGroupMealBO;
import com.holderzone.saas.store.item.entity.domain.GroupMealDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface GroupMealMapper extends BaseMapper<GroupMealDO> {

    List<ItemRelateGroupMealBO> mapItem2RelateGroupMealPakNum(@Param("skuGuids")List<String> skuGuids);
}
