package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.saas.store.item.entity.enums.PricePlanPushStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 价格方案推送记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-30
 */
@Data
@Accessors(chain = true)
@TableName("hsi_price_plan_push_record")
public class PricePlanPushRecordDO {

    private Long id;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    private Integer isDelete;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 品牌guid
     */
    private String brandGuid;

    /**
     * 方案guid
     */
    private String planGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 商品guid
     */
    private String itemGuid;

    /**
     * 推送时间
     */
    private LocalDateTime pushDate;

    /**
     * 推送状态 {@link PricePlanPushStatusEnum}
     */
    private Integer pushStatus;

    /**
     * 推送到商品表后生成的新商品guid
     */
    private String newItemGuid;
}
