package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.item.entity.domain.EstimateSellLogDO;

import java.util.List;


/**
 * @description 商品售卖状态记录 服务接口类
 */
public interface IEstimateSellLogService extends IService<EstimateSellLogDO> {

    /***
     * 记录商品售卖状态记录
     * 以下情况会使商品售卖状态发生变化：
     * 1.商品估清新增编辑时将数量设置为0，此时状态应为停售
     * 2.批量停售操作，此时状态为停售
     * 3.批量恢复操作，此时状态为在售
     * 4.没有勾选长期估清的商品到了营业开始时间自动恢复，此时状态为在售
     * 5.用户下单，扣减商品库存，剩余数量为0时，此时状态为停售
     * 6.退菜，反结账，回退库存，剩余数量大于0时，此时状态为在售
     */
    void saveSellLog(List<EstimateSellLogDO> sellLogList, String enterpriseGuid);


    /**
     * 查询目标门店下 该商品集合的最近的一条售卖状态记录
     */
    List<EstimateSellLogDO> listByStoreGuidAndSkuGuidsLimit1(String storeGuid, List<String> skuGuids);

}
