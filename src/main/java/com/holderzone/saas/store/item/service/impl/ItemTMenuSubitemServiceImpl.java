package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.ItemMenuSubItemReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemTemplateMenuDetailsReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateExecuteTimeSlotRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenuDetailRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenuSubItemDetailRespDTO;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.entity.domain.ItemTMenuSubitemDO;
import com.holderzone.saas.store.item.entity.query.ItemTemplateMenuDetailQuery;
import com.holderzone.saas.store.item.entity.query.ItemTemplateMenuSubItemDetailQuery;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.mapper.ItemTMenuSubitemMapper;
import com.holderzone.saas.store.item.service.IItemTMenuService;
import com.holderzone.saas.store.item.service.IItemTMenuSubitemService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.MapStructUtils;
import com.holderzone.sdk.util.BatchIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品模板-菜单-商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
@Service
public class ItemTMenuSubitemServiceImpl extends ServiceImpl<ItemTMenuSubitemMapper, ItemTMenuSubitemDO> implements IItemTMenuSubitemService {
    private final ItemTMenuSubitemMapper itemTMenuSubitemMapper;
    private final DynamicHelper dynamicHelper;
    private final ItemHelper itemHelper;
    private final IItemTMenuService iItemTMenuService;
    private final RedisTemplate redisTemplate;

    @Autowired
    public ItemTMenuSubitemServiceImpl(ItemTMenuSubitemMapper itemTMenuSubitemMapper, DynamicHelper dynamicHelper, ItemHelper itemHelper, @Lazy IItemTMenuService iItemTMenuService, RedisTemplate redisTemplate) {
        this.itemTMenuSubitemMapper = itemTMenuSubitemMapper;
        this.dynamicHelper = dynamicHelper;
        this.itemHelper = itemHelper;
        this.iItemTMenuService = iItemTMenuService;
        this.redisTemplate = redisTemplate;
    }

    @Transactional
    @Override
    public Boolean saveItemMenuSubItem(List<ItemMenuSubItemReqDTO> request, String menuGuid) {
        request.forEach(o -> {
            o.setItemMenuGuid(menuGuid);
        });
        //传入的列表
        List<ItemTMenuSubitemDO> list = MapStructUtils.INSTANCE.itemMenuSubItemReqDTO2itemTMenuSubitemDOs(request);
        //数据库存在的数据列表
        List<ItemTMenuSubitemDO> dbList = super.list(new LambdaQueryWrapper<ItemTMenuSubitemDO>().eq(ItemTMenuSubitemDO::getItemMenuGuid, menuGuid));

        //保存
        List<ItemTMenuSubitemDO> saveList = new ArrayList<>(list);
        saveList.removeAll(dbList);
        //删除
        List<ItemTMenuSubitemDO> removeList = new ArrayList<>(dbList);
        removeList.removeAll(list);
        //更新
        List<ItemTMenuSubitemDO> updateList = new ArrayList<>(list);
        updateList.retainAll(dbList);

        if (!saveList.isEmpty() && Optional.ofNullable(saveList).isPresent()) {
            List<Long> guids = BatchIdGenerator.batchGetGuids(redisTemplate, GuidKeyConstant.HSI_ITEM_T_MENU_SUBITEM, saveList.size());
            for (int i = 0, len = saveList.size(); i < len; i++) {
                saveList.get(i).setGuid(guids.get(i).toString());
            }
            saveBatch(saveList);
        }
        if (!updateList.isEmpty() && Optional.ofNullable(updateList).isPresent()) {
            updateBatchById(updateList);
        }
        if (!removeList.isEmpty() && Optional.ofNullable(removeList).isPresent()) {
            List<String> collect = removeList.stream().map(ItemTMenuSubitemDO::getGuid).collect(Collectors.toList());
            removeByIds(collect);
        }
        return Boolean.TRUE;
    }

    @Override
    public ItemTemplateMenuDetailRespDTO getItemTemplateMenuDetail(ItemTemplateMenuDetailsReqDTO request) {
        //查询执行时间
        ItemTemplateMenuDetailQuery menuDetail = itemTMenuSubitemMapper.getItemTemplateMenuDetail(request);
        //查询菜单下菜品
        List<ItemTemplateMenuSubItemDetailQuery> subItemDetails = itemTMenuSubitemMapper.getItemTemplateMenuSubItemDetailQuery(request);
        if (!ObjectUtils.isEmpty(menuDetail)) {
            return itemTemplateMenuDetailHander(menuDetail, subItemDetails);
        }
        return null;
    }

    @Override
    public Boolean menuSubItemBatchRemove(SingleDataDTO request) {
        String storeGuid = itemTMenuSubitemMapper.getStoreGuid(request.getDatas().get(0));
        itemHelper.pushMsg(storeGuid);
        return super.removeByIds(request.getDatas());
    }

    @Override
    public List<ItemTemplateMenuSubItemDetailRespDTO> getNowMeunSubItemForSyn(String menuGuid) {
        List<ItemTMenuSubitemDO> list = list(new LambdaQueryWrapper<ItemTMenuSubitemDO>().eq(ItemTMenuSubitemDO::getItemMenuGuid, menuGuid));
        List<ItemTemplateMenuSubItemDetailRespDTO> subItems = MapStructUtils.INSTANCE.subItemDOList2SubItemDetailRespDTOList(list);
        return subItems;
    }

    @Override
    public Boolean removeMenuSubItem(String menuGuid) {
        return remove(new LambdaQueryWrapper<ItemTMenuSubitemDO>().eq(ItemTMenuSubitemDO::getItemMenuGuid, menuGuid));
    }

    private ItemTemplateMenuDetailRespDTO itemTemplateMenuDetailHander(ItemTemplateMenuDetailQuery menuDetail, List<ItemTemplateMenuSubItemDetailQuery> subItemDetails) {
        String menuGuid = menuDetail.getMenuGuid();
        Integer periodicMode = menuDetail.getPeriodicMode();
        List<Integer> weeks = null;
        List<ItemTemplateExecuteTimeSlotRespDTO> times = null;
        List<ItemTemplateMenuSubItemDetailRespDTO> subItems = MapStructUtils.INSTANCE.detailQuerys2SubItemDetailRespDTOs(subItemDetails);
        //按时段
        if (periodicMode.equals(1)) {
            times = new ArrayList<>();
            times = JacksonUtils.toObjectList(ItemTemplateExecuteTimeSlotRespDTO.class, menuDetail.getTimes());
        }
        //按星期
        if (periodicMode.equals(2)) {
            String strWeeks = menuDetail.getWeeks();
            strWeeks = strWeeks.replace("[", "");
            strWeeks = strWeeks.replace("]", "");
            strWeeks = strWeeks.replace(" ", "");
            String[] split = strWeeks.split(",");
            weeks = new ArrayList<>();
            for (String o : split) {
                weeks.add(Integer.parseInt(o));
            }
        }//按时段+周期
        else {
        }
        return ItemTemplateMenuDetailRespDTO
                .builder()
                .timeGuid(menuDetail.getGuid())
                .isItFullTime(menuDetail.getIsItFullTime())
                .menuGuid(menuGuid)
                .periodicMode(periodicMode)
                .times(times)
                .weeks(weeks)
                .subItems(subItems)
                .build();
    }
}
