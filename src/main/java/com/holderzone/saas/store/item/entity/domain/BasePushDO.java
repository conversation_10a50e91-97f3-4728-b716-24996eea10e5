package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BasePushDO
 * @date 2019/02/25 下午2:34
 * @description //TODO
 * @program holder-saas-store-item
 */
@Data
@Accessors(chain = true)
public class BasePushDO implements Serializable {
    /**
     * guid
     */
    protected String guid;
    /**
     * 门店GUID
     */
    protected String storeGuid;

    /**
     * 父实体GUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的实体，则该字段为品牌库对应的实体GUID。
     */
    protected String parentGuid;

    /**
     * 名称
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    protected String name;
}
