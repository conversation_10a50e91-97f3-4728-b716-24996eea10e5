package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.item.entity.domain.RSkuSubgroupDO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 规格与分组关联表（规格为套餐分组中的子菜规格） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-25
 */
public interface RSkuSubgroupMapper extends BaseMapper<RSkuSubgroupDO> {
    Boolean updateIsDelete(@Param("guid") String guid);

    Boolean deleteByItemGuid(@Param("itemGuid") String itemGuid);


}
