package com.holderzone.saas.store.item.service;

import com.holderzone.saas.store.item.dto.SyncPricePlanMessageDTO;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;

/**
 * 基于Redisson封装的Cache
 *
 * <AUTHOR> chen
 * @version 1.0
 * @since 2020-10-30
 */
public interface IRedissonCacheService {

    /**
     * 设置缓存
     *
     * @param key        key
     * @param messageDTO messageDTO
     * @param time       time
     */
    void setCacheMap(String key, SyncPricePlanMessageDTO messageDTO, long time);

    Object getCacheMap(String key);

    /**
     * 删除缓存MAP
     *
     * @param key key
     * @return Object
     */
    Object removeCacheMap(String key);

    /**
     * 获取Cache组件
     *
     * @return
     */
    <V> RMapCache<String, V> getSyncPricePlanItemDTORMapCache();

    /**
     * 设置缓存数据
     *
     * @param key
     * @param value
     * @param expire
     * @param timeUnit
     */
    <V> void setCache(String key, V value, long expire, TimeUnit timeUnit);

    /**
     * 获取key指定的缓存
     *
     * @param key
     * @return
     */
    <V> V getCache(String key);

    /**
     * 通过key移除缓存
     *
     * @param key
     * @return
     */
    Object removeCache(String key);

    /**
     * 获取RedissonClient实例
     *
     * @return
     * <AUTHOR> chen
     * @version 1.0
     * @since 2020-10-30
     */
    RedissonClient getRedissonClient();
}
