package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <p>
 * 价格方案表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-26
 */
@Data
@Accessors(chain = true)
@TableName("hsi_price_plan")
public class PricePlanDO {

    private Long id;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @TableLogic
    private Integer isDelete;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 品牌guid（价格方案属于品牌维度）
     */
    private String brandGuid;

    /**
     * 方案名称（品牌下唯一）
     */
    private String name;

    /**
     * 方案描述
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String description;

    /**
     * 方案状态-0 未启用 1 已启用 2暂不启用 3永久停用 4即将启用
     *
     * @see com.holderzone.saas.store.item.entity.enums.PricePlanStatusEnum
     */
    private Integer status;

    /**
     * 方案包含的商品数
     */
    private Integer itemNum;

    /**
     * 使用方案的门店数
     */
    private Integer storeNum;

    /**
     * 推送方式-1立即推送 2按时间
     */
    private Integer pushType;

    /**
     * 推送（生效）时间
     */
    private LocalDateTime pushDate;

    /***
     *  方案编号
     */
    private String planCode;
    /***
     *  起始时间
     */
    private LocalTime startTime;
    /***
     *  结束时间
     */
    private LocalTime endTime;
    /***
     *  生效时间
     */
    private LocalDateTime effectiveTime;

    /***
     *  即将生效时间
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private LocalDateTime instantlyEffectiveTime;

    /***
     *  售卖类型-0 默认（全时段）-1特殊时段
     */
    private Integer sellTimeType;

    /**
     * 原菜谱方案Guid
     */
    private String parentGuid;

    /**
     * 最新编辑时间
     */
    private LocalDateTime updateTime;
}
