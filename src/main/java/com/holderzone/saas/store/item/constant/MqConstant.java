package com.holderzone.saas.store.item.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MqConstant
 * @date 2019/12/18 上午11:25
 * @description //
 * @program holder
 */


public interface MqConstant {

    String DOWNSTREAM_CONTEXT = "downstream-context";
    String DOWNSTREAM_INIT_STORE_ITEM_GROUP = "downstream-init-store-item-group";
    String DOWNSTREAM_STORE_TOPIC = "downstream-store-topic";
    String DOWNSTREAM_STORE_CREATE_TAG = "downstream-store-create-tag";

    String DOWNSTREAM_INIT_BRAND_ITEM_GROUP = "downstream-init-brand-item-group";
    String DOWNSTREAM_BRAND_CREATE_TOPIC = "downstream-brand-create-topic";
    String DOWNSTREAM_BRAND_CREATE_TAG = "downstream-brand-create-tag";
    /**
     * 门店数据总线
     */
    String STORE_DATA_BUS_TOPIC = "store-data-bus-topic";

    /**
     * 价格方案
     */
    String PRICE_PLAN_SYNC_ITEM_GROUP = "price-plan-sync-item-group";
    String PRICE_PLAN_SYNC_ITEM_TOPIC = "price-plan-sync-item-topic";
    String PRICE_PLAN_SYNC_ITEM_TAG = "price-plan-sync-item-tag";
}
