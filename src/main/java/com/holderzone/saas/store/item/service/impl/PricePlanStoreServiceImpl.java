package com.holderzone.saas.store.item.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.req.PricePlanReqDTO;
import com.holderzone.saas.store.dto.item.req.PricePlanStoreReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.item.config.LocalCacheConfig;
import com.holderzone.saas.store.item.constant.Constant;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.entity.domain.PricePlanDO;
import com.holderzone.saas.store.item.entity.domain.PricePlanStoreDO;
import com.holderzone.saas.store.item.entity.enums.PricePlanStatusEnum;
import com.holderzone.saas.store.item.mapper.PricePlanMapper;
import com.holderzone.saas.store.item.mapper.PricePlanStoreMapper;
import com.holderzone.saas.store.item.service.IPricePlanStoreService;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import com.holderzone.saas.store.item.util.DateTimeUtils;
import com.holderzone.saas.store.item.util.GUIDUtils;
import com.holderzone.saas.store.item.util.MapStructUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2020/10/29 17:12
 */
@Service
@AllArgsConstructor
@Slf4j
public class PricePlanStoreServiceImpl extends ServiceImpl<PricePlanStoreMapper, PricePlanStoreDO>
        implements IPricePlanStoreService {

    private final OrganizationService organizationService;

    private final PricePlanStoreMapper pricePlanStoreMapper;

    private final PricePlanMapper pricePlanMapper;


    /**
     * 门店控制列表
     *
     * @param reqDTO 品牌guid,价格方案Guid
     * @return List<PricePlanStoreRespDTO>
     */
    @Override
    public List<PricePlanStoreRespDTO> storeControlList(PricePlanStoreReqDTO reqDTO) {
        //必需参数校验
        checkPriceDTO(reqDTO);

        //通过品牌guid查询门店列表
        ArrayList<String> brandList = new ArrayList<>();
        brandList.add(reqDTO.getBrandGuid());
        List<StoreDTO> storeDTOList = organizationService.queryStoreByBrandList(brandList);

        if (CollectionUtils.isEmpty(storeDTOList)) {
            log.warn(Constant.STORE_UNDER_BRAND_IS_EMPTY);
            return new ArrayList<>();
        }

        //筛选勾选自建菜品的门店
        List<StoreDTO> filterStore = storeDTOList.stream()
                .filter(storeDTO -> Constant.FALSE.compareTo(storeDTO.getIsSelfBuildItems()) == 0)
                .collect(Collectors.toList());

        //根据价格方案和门店列表查询，是否这个价格方案要取反，然后查出的数据取门店guid，这些门店guid都有其他占用
        List<String> storeGuidList = filterStore.stream()
                .map(StoreDTO::getGuid)
                .collect(Collectors.toList());

        //根据门店guid列表查询门店与价格方案绑定关系
        List<PricePlanStoreDO> priceStoreList = pricePlanStoreMapper.selectList(
                new LambdaQueryWrapper<PricePlanStoreDO>()
                        .ne(PricePlanStoreDO::getPlanGuid, reqDTO.getPlanGuid())
                        .in(PricePlanStoreDO::getStoreGuid, storeGuidList));

        List<String> priceStoreGuidList = priceStoreList.stream()
                .map(PricePlanStoreDO::getStoreGuid)
                .collect(Collectors.toList());

        //根据价格方案和门店列表查询绑定表，如果有说明该价格方案里有绑定，取出这些门店guid，这些门店是当前价格方案
        //根据价格方案guid查询绑定关系
        List<PricePlanStoreDO> pricePlanStoreDOList = pricePlanStoreMapper.selectList(
                new LambdaQueryWrapper<PricePlanStoreDO>()
                        .eq(PricePlanStoreDO::getPlanGuid, reqDTO.getPlanGuid())
                        .in(PricePlanStoreDO::getStoreGuid, storeGuidList)
        );

        List<String> currentStoreGuidList = pricePlanStoreDOList.stream()
                .map(PricePlanStoreDO::getStoreGuid)
                .collect(Collectors.toList());

        //分配给员工的门店
        List<String> userStoreGuidList = reqDTO.getStoreGuidList();

        //组装返回结果
        ArrayList<PricePlanStoreRespDTO> planStoreRespDTOList = new ArrayList<>();
        filterStore.forEach(
                storeDTO -> {
                    PricePlanStoreRespDTO pricePlanStoreRespDTO = new PricePlanStoreRespDTO();
                    pricePlanStoreRespDTO.setStoreGuid(storeDTO.getGuid());
                    pricePlanStoreRespDTO.setStoreName(storeDTO.getName());
                    pricePlanStoreRespDTO.setHasPricePlan(Boolean.FALSE);
                    pricePlanStoreRespDTO.setIsCurrent(Boolean.FALSE);
                    pricePlanStoreRespDTO.setIsCurrentUser(Boolean.TRUE);
                    if (!CollectionUtils.isEmpty(priceStoreGuidList)
                            && priceStoreGuidList.contains(storeDTO.getGuid())) {
                        pricePlanStoreRespDTO.setHasPricePlan(Boolean.TRUE);
                    }
                    if (!CollectionUtils.isEmpty(currentStoreGuidList)
                            && currentStoreGuidList.contains(storeDTO.getGuid())) {
                        pricePlanStoreRespDTO.setIsCurrent(Boolean.TRUE);
                    }
                    //员工没有分配的门店设置为不可选，即非当前用户
                    if (!CollectionUtils.isEmpty(userStoreGuidList) && !userStoreGuidList.contains(storeDTO.getGuid())) {
                        pricePlanStoreRespDTO.setIsCurrentUser(Boolean.FALSE);
                        //不是当前用户的门店，但是有绑定关系
                        List<PricePlanStoreDO> PlanStoreList = pricePlanStoreMapper.selectList(new LambdaQueryWrapper<PricePlanStoreDO>()
                                .eq(PricePlanStoreDO::getStoreGuid, storeDTO.getGuid()));
                        if (!CollectionUtils.isEmpty(PlanStoreList)) {
                            pricePlanStoreRespDTO.setHasPricePlan(Boolean.TRUE);
                        }
                    }
                    planStoreRespDTOList.add(pricePlanStoreRespDTO);
                }
        );
        return planStoreRespDTOList;
    }

    /**
     * 必需参数校验
     *
     * @param reqDTO brandGuid，planGuid
     */
    private void checkPriceDTO(PricePlanStoreReqDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO.getBrandGuid())) {
            throw new BusinessException(Constant.BRAND_CANNOT_BE_NULL);
        }
        if (ObjectUtils.isEmpty(reqDTO.getPlanGuid())) {
            throw new BusinessException(Constant.PRICE_PLAN_CANNOT_BE_EMPTY);
        }
    }

    /**
     * 绑定门店与方案关系
     * 绑定逻辑：先删除，再绑定
     *
     * @param reqDTO 品牌guid,价格方案Guid,门店GUid列表
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean bingPlanStore(PricePlanStoreReqDTO reqDTO) {
        log.info("修改菜谱门店绑定关系，参数：{}",reqDTO);
        //必需参数校验
        checkPriceDTO(reqDTO);

        // 清空方案下所有绑定关系
        pricePlanStoreMapper.delete(new LambdaQueryWrapper<PricePlanStoreDO>()
                .eq(PricePlanStoreDO::getPlanGuid, reqDTO.getPlanGuid()));

        ArrayList<PricePlanStoreDO> planStoreDOList = new ArrayList<>();
        List<String> pricePlanStoreDTOList = reqDTO.getStoreGuidList();

        if (ObjectUtils.isEmpty(pricePlanStoreDTOList)) {
            log.warn(Constant.STORE_BINDING_INFORMATION_LIST_IS_EMPTY);
            pricePlanMapper.updateStoreNum(reqDTO.getPlanGuid(), Constant.NUMBER_ZERO, 3);
            //已经清空相关数据，无需保存空集合
            return Boolean.TRUE;
        }
        //绑定关系组装
        pricePlanStoreDTOList.forEach(
                storeGuid -> {
                    PricePlanStoreDO planStoreDO = new PricePlanStoreDO();
                    planStoreDO.setGuid(GUIDUtils.generateGuid(GuidKeyConstant.HSI_PRICEPLAN_STORE));
                    planStoreDO.setBrandGuid(reqDTO.getBrandGuid());
                    planStoreDO.setPlanGuid(reqDTO.getPlanGuid());
                    planStoreDO.setStoreGuid(storeGuid);
                    planStoreDOList.add(planStoreDO);
                }
        );
        // 计算每个价格方案的绑定数，更新对应价格方案字段
        List<String> planGuidCountList = planStoreDOList.stream()
                .map(PricePlanStoreDO::getPlanGuid)
                .distinct()
                .collect(Collectors.toList());

        Long storeCount = planStoreDOList.stream()
                .filter(p -> reqDTO.getPlanGuid().equals(p.getPlanGuid()))
                .count();
        //只更新自己的门店数
        pricePlanMapper.updateStoreNum(reqDTO.getPlanGuid(), storeCount.intValue(), 3);
        if (CollectionUtils.isEmpty(planGuidCountList)) {
            pricePlanMapper.updateStoreNum(reqDTO.getPlanGuid(), Constant.NUMBER_ZERO, 3);
        }
        return this.saveBatch(planStoreDOList);
    }

    /**
     * 根据门店guid删除方案与门店绑定关系
     * 待完善
     *
     * @param storeGuid 门店guid
     * @return
     */
    @Override
    public Boolean deletePlanStoreByStoreGuid(String storeGuid) {
        //查出绑定关系
        List<PricePlanStoreDO> planStoreDOList = pricePlanStoreMapper.selectList(
                new LambdaQueryWrapper<PricePlanStoreDO>()
                        .eq(PricePlanStoreDO::getStoreGuid, storeGuid));
        //删除该门店下所有绑定关系
        int delete = pricePlanStoreMapper.delete(new LambdaQueryWrapper<PricePlanStoreDO>()
                .eq(PricePlanStoreDO::getStoreGuid, storeGuid));

        //更新方案门店数
        planStoreDOList.forEach(
                pricePlanStoreDO ->
                        // 更新绑定数为0
                        pricePlanMapper.updateStoreNum(pricePlanStoreDO.getPlanGuid(), Constant.NUMBER_ZERO, 3)
        );
        return delete == 1 ? Boolean.TRUE : Boolean.FALSE;
    }

    /**
     * 根据门店guid查询方案绑定门店
     *
     * @param storeGuid 门店guid
     * @return 方案与门店绑定关系返回对象列表
     */
    @Override
    public List<PricePlanBingStoreRespDTO> getPlanStoreByStoreGuid(String storeGuid) {
        //查出绑定关系
        List<PricePlanStoreDO> planStoreDOList = pricePlanStoreMapper.selectList(
                new LambdaQueryWrapper<PricePlanStoreDO>()
                        .eq(PricePlanStoreDO::getStoreGuid, storeGuid));
        return MapStructUtils.INSTANCE.PricePlanStoreDOList2DTOList(planStoreDOList);
    }

    @Override
    public List<PricePlanStoreCheckRespDTO> checkStorePricePlanRule(PricePlanReqDTO reqDTO) {
        Assert.notNull(reqDTO, "请求参数不能为空！");
        List<PricePlanStoreCheckRespDTO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(reqDTO.getStoreGuidList())) {
            log.warn("校验门店为空");
            return result;
        }

        // 延迟生效时 时间格式转换
        if (2 == reqDTO.getPushType()) {
            String localYear = reqDTO.getPushDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String localTime = reqDTO.getPushDate().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            LocalDateTime pushDateTime = LocalDateTime.parse(localYear + " " + localTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            reqDTO.setPushDate(pushDateTime);
        }

        List<PricePlanStoreInfoDTO> planStoreList = pricePlanStoreMapper.findPlanStoreGuid(reqDTO);
        if (CollectionUtils.isEmpty(planStoreList)) {
            reqDTO.getStoreGuidList().forEach(
                    storeGuid -> {
                        PricePlanStoreCheckRespDTO pricePlanStoreCheckRespDTO = new PricePlanStoreCheckRespDTO();
                        pricePlanStoreCheckRespDTO.setStoreGuid(storeGuid);
                        pricePlanStoreCheckRespDTO.setIsDefault(Boolean.FALSE);
                        pricePlanStoreCheckRespDTO.setIsOverlapping(Boolean.FALSE);
                        result.add(pricePlanStoreCheckRespDTO);
                    }
            );
            log.warn("没有查询到绑定的门店");
            return result;
        }

        //该方案下已绑定的门店
//        List<String> storeGuidList = new ArrayList<>();
//        if (!StringUtils.isEmpty(reqDTO.getGuid())) {
//            List<PricePlanStoreDO> planStoreDOList = pricePlanStoreMapper.selectList(new LambdaQueryWrapper<PricePlanStoreDO>()
//                    .eq(PricePlanStoreDO::getPlanGuid, reqDTO.getGuid()));
//            storeGuidList.addAll(planStoreDOList.stream().map(PricePlanStoreDO::getStoreGuid)
//                    .distinct()
//                    .collect(Collectors.toList()));
//        }

        if (!CollectionUtils.isEmpty(planStoreList)) {
            if (0 == reqDTO.getSellTimeType()) {
                planStoreList.forEach(e -> {
                    PricePlanStoreCheckRespDTO pricePlanStoreCheckRespDTO = new PricePlanStoreCheckRespDTO();
                    pricePlanStoreCheckRespDTO.setStoreGuid(e.getStoreGuid());
                    pricePlanStoreCheckRespDTO.setIsDefault(Boolean.FALSE);
                    if (reqDTO.getGuid() != null && ObjectUtil.notEqual(reqDTO.getGuid(),e.getPlanGuid())) {
                        pricePlanStoreCheckRespDTO.setIsDefault(Boolean.TRUE);
                    }
                    pricePlanStoreCheckRespDTO.setIsOverlapping(Boolean.FALSE);
                    result.add(pricePlanStoreCheckRespDTO);
                });

            } else {
                //特殊时段
                // 排除自身
                planStoreList.removeIf(
                        plan -> !ObjectUtils.isEmpty(reqDTO.getGuid()) && Objects.equals(reqDTO.getGuid(), plan.getPlanGuid()));

                Map<String, List<PricePlanStoreInfoDTO>> storeMap = planStoreList.stream()
                        .collect(Collectors.groupingBy(PricePlanStoreInfoDTO::getStoreGuid));

                reqDTO.getStoreGuidList().forEach(
                        storeGuid -> {
                            List<PricePlanStoreInfoDTO> storeInfoList = storeMap.get(storeGuid);
                            PricePlanStoreCheckRespDTO checkRespDTO = new PricePlanStoreCheckRespDTO();
                            checkRespDTO.setStoreGuid(storeGuid);
                            checkRespDTO.setIsOverlapping(Boolean.FALSE);
                            if (!CollectionUtils.isEmpty(storeInfoList)) {
                                storeInfoList.forEach(store -> {
                                    LocalTime startCompareTime = reqDTO.getStartTime();
                                    LocalTime endCompareTime = reqDTO.getEndTime();
                                    if (!ObjectUtils.isEmpty(startCompareTime) && !ObjectUtils.isEmpty(endCompareTime)) {
                                        LocalTime startTime = store.getStartTime();
                                        LocalTime endTime = store.getEndTime();
                                        boolean isOverlap = DateTimeUtils.checkOverlap(startCompareTime, endCompareTime, startTime, endTime);
                                        if (isOverlap) {
                                            checkRespDTO.setIsOverlapping(Boolean.TRUE);
                                        }
                                    }
                                });
                            }
                            checkRespDTO.setIsDefault(Boolean.FALSE);
                            result.add(checkRespDTO);
                        }
                );
            }
        }
        return result;
    }


    /***
     * 查询在生效时间内的价格方案
     * @param nowDateTime 当前时间
     * @param storeGuid   门店GUID
     * @return 返回
     */
    @Override
    public List<PricePlanNowDTO> findPlanNowStoreGuid(LocalDateTime nowDateTime, String storeGuid) {
        return pricePlanStoreMapper.findPlanNowStoreGuid(nowDateTime, storeGuid);
    }

    /***
     * 查询在生效时间内的价格方案
     * @param nowDateTime 当前时间
     * @param storeGuidList   门店GUID
     * @return 返回
     */
    @Override
    public List<PricePlanNowDTO> findPlanNowStoreList(LocalDateTime nowDateTime, List<String> storeGuidList) {
        return pricePlanStoreMapper.findPlanNowStoreGuidList(nowDateTime, storeGuidList);
    }

    /**
     * 查询在生效时间内和即将生效的价格方案
     *
     * @param storeGuid 门店GUID
     * @return 返回
     */
    @Override
    public List<PricePlanNowDTO> findPlanMemberStoreGuid(String storeGuid) {
        return pricePlanStoreMapper.findPlanMemberStoreGuid(storeGuid);
    }

    /**
     * 查询当前门店即将生效和已生效的菜谱方案
     *
     * @param storeGuid storeGuid
     * @return List<PricePlanNowDTO>
     */
    @Override
    public List<PricePlanNowDTO> findPlanStoreGuid(String storeGuid) {
        return pricePlanStoreMapper.findPlanByStoreGuid(storeGuid);
    }

    /**
     * 通过门店guid查询所属菜谱方案
     *
     * @param storeGuid 门店guid
     * @return 所属菜谱方案，如果没有则返回空list
     */
    @Override
    public List<PricePlanRespDTO> queryBelongPlansByStoreGuid(String storeGuid) {
        List<PricePlanStoreDO> planStoreDOList = this.list(new LambdaQueryWrapper<PricePlanStoreDO>()
                .eq(PricePlanStoreDO::getStoreGuid, storeGuid)
                .eq(PricePlanStoreDO::getIsDelete, 0)
        );
        if (CollectionUtils.isEmpty(planStoreDOList)) {
            log.warn("门店:{}所属方案为空!", storeGuid);
            return new ArrayList<>();
        }
        List<String> planList = planStoreDOList.stream()
                .map(PricePlanStoreDO::getPlanGuid)
                .distinct()
                .collect(Collectors.toList());

        //查询修改备份的菜谱方案 列表中需排除
        List<PricePlanDO> backPlanDOS = pricePlanMapper.selectList(new LambdaQueryWrapper<PricePlanDO>()
                .isNotNull(PricePlanDO::getParentGuid));
        List<String> filterPlanGuids = backPlanDOS.stream().map(PricePlanDO::getParentGuid).collect(Collectors.toList());
        planList.removeAll(filterPlanGuids);
        if (CollectionUtils.isEmpty(planList)) {
            log.warn("planList为空");
            return new ArrayList<>();
        }
        List<PricePlanDO> planDOList = pricePlanMapper.selectList(new LambdaQueryWrapper<PricePlanDO>()
                .in(PricePlanDO::getGuid, planList)
                .eq(PricePlanDO::getIsDelete, 0)
                .and(w -> w.eq(PricePlanDO::getStatus, PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode())
                        .or()
                        .eq(PricePlanDO::getStatus, PricePlanStatusEnum.USING.getCode()))
        );
        log.info("planDOList：{}", JacksonUtils.writeValueAsString(planDOList));
        return MapStructUtils.INSTANCE.PlanDOList2PlanRespDTOList(planDOList);
    }

    @Override
    public void updatePlanStore(String planGuid, String parentPlanGuid) {
        //删除原菜谱方案商品
        this.remove(new LambdaUpdateWrapper<PricePlanStoreDO>().eq(PricePlanStoreDO::getPlanGuid, parentPlanGuid));
        //将新菜谱方案商品关联原菜谱方案
        this.update(new LambdaUpdateWrapper<PricePlanStoreDO>()
                .set(PricePlanStoreDO::getPlanGuid, parentPlanGuid)
                .eq(PricePlanStoreDO::getPlanGuid, planGuid));
    }
}
