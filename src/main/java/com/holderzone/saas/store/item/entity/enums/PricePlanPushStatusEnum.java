package com.holderzone.saas.store.item.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 价格方案推送状态枚举
 */
@Getter
@AllArgsConstructor
public enum PricePlanPushStatusEnum {

    /**
     * 未推送（点击方案推送后保存推送记录的默认状态）
     */
    NOT_PUSH(0, "未推送"),

    /**
     * 已推送
     */
    PUSH_SUCCESS(1, "已推送"),

    /**
     * 推送失败
     */
    PUSH_FAIL(2, "推送失败"),
    ;

    private final int code;

    private final String desc;
}
