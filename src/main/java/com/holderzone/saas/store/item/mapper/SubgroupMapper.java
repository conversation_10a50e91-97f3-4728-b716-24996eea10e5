package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.item.resp.PkgSkuDTO;
import com.holderzone.saas.store.item.entity.domain.SubgroupDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 套餐的分组 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-24
 */
@Mapper
@Component
public interface SubgroupMapper extends BaseMapper<SubgroupDO> {
    List<String> getGuidListByItemGuidAndStoreGuid(@Param("itemGuid") String itemGuid, @Param("storeGuid") String storeGuid);

    List<String> getGuidListByStoreGuidAndBrandGuid(@Param("storeGuid") String storeGuid, @Param("brandGuid") String brandGuid);

    void deleteByItemGuidAndStoreGuid(@Param("itemGuid") String itemGuid, @Param("storeGuid") String storeGuid);

    List<String> listParentItemGuidBySubGroupSku(@Param("skuGuidList") List<String> skuGuidList);
}
