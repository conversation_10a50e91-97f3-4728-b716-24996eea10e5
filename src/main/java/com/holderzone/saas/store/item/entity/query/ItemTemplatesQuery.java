package com.holderzone.saas.store.item.entity.query;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplatesQuery
 * @date 2019/06/01 14:20
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
public class ItemTemplatesQuery {

    private Integer periodicMode;

    private String guid;

    private String menuGuid;

    private String weeks;

    private String times;

    private Integer isDelete;
}
