package com.holderzone.saas.store.item.util;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ListUtils
 * @date 2019/03/16 下午6:09
 * @description //TODO
 * @program holder-saas-store-item
 */
@Component
public class ListUtils {

    public <T> void sort(List<T> doList, Supplier<Integer> sort, Function updateTime){
//        doList.sort(Comparator.comparing(sort).thenComparing(updateTime));
    }
}
