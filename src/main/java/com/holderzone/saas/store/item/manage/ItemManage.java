package com.holderzone.saas.store.item.manage;

import cn.hutool.core.collection.CollUtil;
import com.holderzone.saas.store.dto.item.req.ItemQueryListReq;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.enums.item.ItemTypeEnum;
import com.holderzone.saas.store.item.repository.EstimateRepository;
import com.holderzone.saas.store.item.service.IItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-01-26
 * @description 商品业务类
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ItemManage {

    private final IItemService itemService;

    private final EstimateRepository estimateRepository;

    public ItemInfoEstimateSingleDTO getItemInfoEstimate(ItemQueryListReq req){
        ItemInfoEstimateSingleDTO singleDTO = new ItemInfoEstimateSingleDTO();
        ItemAndTypeForAndroidRespDTO itemAndTypeForAndroidRespDTO = itemService.selectItemDetailAndTypeForSyn(req);
        if(itemAndTypeForAndroidRespDTO == null || CollUtil.isEmpty(itemAndTypeForAndroidRespDTO.getItemList())){
            return singleDTO;
        }
        ItemSynRespDTO item = itemAndTypeForAndroidRespDTO.getItemList().get(0);
        Set<String> skuGuidList = item.getSkuList().stream().map(SkuSynRespDTO::getSkuGuid).collect(Collectors.toSet());
        if (ItemTypeEnum.PKG.getCode() == item.getItemType()) {
            List<String> subSkuList = item.getSubgroupList().stream()
                    .flatMap(g -> g.getSubItemSkuList().stream())
                    .map(SubItemSkuSynRespDTO::getSkuGuid)
                    .distinct()
                    .collect(Collectors.toList());
            skuGuidList.addAll(subSkuList);
        }
        //查询估清数据
        List<ItemEstimateForAndroidRespDTO> itemEstimateForAndroidDTO = estimateRepository.listEstimateBySkuGuid(skuGuidList,req.getStoreGuid());

        singleDTO.setItemInfo(item);
        singleDTO.setItemEstimateList(itemEstimateForAndroidDTO);
        return singleDTO;
    }
}
