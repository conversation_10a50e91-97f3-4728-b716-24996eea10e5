package com.holderzone.saas.store.item.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 价格方案推送发送MQ消息对象
 */
@Data
@Accessors(chain = true)
public class SyncPricePlanMessageDTO implements Serializable {

    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 品牌guid
     */
    private String brandGuid;

    /**
     * 方案guid
     */
    private String pricePlanGuid;

    /**
     * 消息类型（通知接收方是1-推送方案商品 还是 2-删除操作   3-全时段推送）
     */
    private Integer messageType;
}
