package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.entity.domain.EstimateSellLogDO;
import com.holderzone.saas.store.item.mapper.EstimateSellLogMapper;
import com.holderzone.saas.store.item.service.IEstimateSellLogService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.GUIDUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @description 商品售卖状态记录 服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IEstimateSellLogServiceImpl extends ServiceImpl<EstimateSellLogMapper, EstimateSellLogDO> implements IEstimateSellLogService {

    private final DynamicHelper dynamicHelper;

    private static final ExecutorService executor = new ThreadPoolExecutor(10, 20, 5L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(10));

    /**
     * 实现
     * 会根据传过来的估清商品，查询最近的一条日志记录，如果和当前状态相反，则保存，如果不相反，则忽略
     */
    // @Async
    @Override
    public void saveSellLog(List<EstimateSellLogDO> sellLogList, String enterpriseGuid) {
        if (CollectionUtils.isEmpty(sellLogList)) {
            return;
        }

        Map<String, List<EstimateSellLogDO>> sellLogByStoreMap = sellLogList.stream().collect(Collectors.groupingBy(EstimateSellLogDO::getStoreGuid));
        for (Map.Entry<String, List<EstimateSellLogDO>> entry : sellLogByStoreMap.entrySet()) {
            executor.execute(() -> {
                dynamicHelper.changeDatasource(enterpriseGuid);
                UserContextUtils.putErp(enterpriseGuid);

                String storeGuid = entry.getKey();
                List<EstimateSellLogDO> sellLogByStoreList = entry.getValue();
                // lock
                String lockKey = enterpriseGuid + "-" + storeGuid;
                if (!RedissonLockUtil.tryLock(lockKey, 30, 35)) {
                    return;
                }
                try {
                    Set<String> skuGuidSet = sellLogByStoreList.stream().map(EstimateSellLogDO::getSkuGuid).collect(Collectors.toSet());

                    // 查询最近的状态记录
                    List<EstimateSellLogDO> lastSellLogList = listByStoreGuidAndSkuGuidsLimit1(storeGuid, new ArrayList<>(skuGuidSet));
                    Map<String, EstimateSellLogDO> lastSellLogMap = lastSellLogList.stream()
                            .collect(Collectors.toMap(EstimateSellLogDO::getSkuGuid, Function.identity(), (key1, key2) -> key2));

                    // 如果当前状态和上一次记录日志状态一致，则不保存当前状态记录
                    Iterator<EstimateSellLogDO> iterator = sellLogByStoreList.iterator();
                    while (iterator.hasNext()) {
                        EstimateSellLogDO sellLog = iterator.next();
                        EstimateSellLogDO lastSellLog = lastSellLogMap.get(sellLog.getSkuGuid());
                        if (Objects.isNull(lastSellLog)) {
                            continue;
                        }
                        if (sellLog.getSell().equals(lastSellLog.getSell())) {
                            iterator.remove();
                        }
                    }
                    if (CollectionUtils.isEmpty(sellLogByStoreList)) {
                        return;
                    }

                    // 保存售卖记录
                    List<String> guids = GUIDUtils.generateGuids(GuidKeyConstant.HSI_ESTIMATE_SELL_LOG, sellLogByStoreList.size());
                    if (CollectionUtils.isEmpty(guids)) {
                        log.error("保存商品售卖记录失败，生成guid失败,当前门店storeGuid:{}", storeGuid);
                        return;
                    }
                    for (int i = 0; i < sellLogByStoreList.size(); i++) {
                        sellLogByStoreList.get(i).setGuid(guids.get(i));
                    }
                    saveBatch(sellLogByStoreList);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    RedissonLockUtil.unlock(lockKey);
                }
            });
        }
    }

    @Override
    public List<EstimateSellLogDO> listByStoreGuidAndSkuGuidsLimit1(String storeGuid, List<String> skuGuids) {
        if (CollectionUtils.isEmpty(skuGuids)) {
            return Lists.newArrayList();
        }
        return baseMapper.listByStoreGuidAndSkuGuidsLimit1(storeGuid, skuGuids);
    }
}


