package com.holderzone.saas.store.item.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.merchant.dto.org.RequestProductInfo;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.PlanPriceEditPriceBaseDTO;
import com.holderzone.saas.store.dto.item.common.PlanPriceExistItemDTO;
import com.holderzone.saas.store.dto.item.common.PricePlanStoreBaseDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.req.price.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.QueryStoreDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.weixin.deal.PricePlanChangeRequestDTO;
import com.holderzone.saas.store.dto.weixin.deal.PricePlanChangeResponseDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.item.config.ThreadPoolConfig;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.dto.SyncPricePlanMessageDTO;
import com.holderzone.saas.store.item.entity.domain.*;
import com.holderzone.saas.store.item.entity.enums.*;
import com.holderzone.saas.store.item.helper.EventPushHelper;
import com.holderzone.saas.store.item.mapper.ItemMapper;
import com.holderzone.saas.store.item.mapper.PricePlanItemMapper;
import com.holderzone.saas.store.item.mapper.PricePlanMapper;
import com.holderzone.saas.store.item.mapper.PricePlanStoreMapper;
import com.holderzone.saas.store.item.service.*;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import com.holderzone.saas.store.item.util.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.security.SecureRandom;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class PricePlanServiceImpl extends ServiceImpl<PricePlanMapper, PricePlanDO> implements IPricePlanService {

    private final PricePlanMapper pricePlanMapper;

    private final PricePlanItemMapper planItemMapper;

    private final PricePlanStoreMapper planStoreMapper;

    private final IPricePlanPushRecordService pushRecordService;

    private final ICommonService commonService;

    private final IRedissonCacheService cacheService;

    private final IPricePlanItemService planItemService;

    private final OrganizationService organizationService;

    private final ITypeService typeService;

    private final ISkuService skuService;

    private final IPricePlanStoreService pricePlanStoreService;

    private ItemMapper itemMapper;

    private final RedisUtil redisUtil;

    private final EventPushHelper eventPushHelper;

    private final IItemPadPictureService itemPadPictureService;

    private final DynamicHelper dynamicHelper;

    //价格方案Redis Key
    private static final String KEY_PREFIX = "price:plan-";

    // 方案定时执行 redis key
    private static final String KEY_EXECUTION = "plan:execution-";

    /**
     * 方案草稿缓存前缀
     */
    private static final String PLAN_CACHE = "planCache:";

    /**
     * 冒号字符串
     */
    private static final String COLON = ":";

    //价格方案推送类型：1立即推送 2定时推送
    private static final int PLAN_PUSH_NOW = 1;
    private static final int PLAN_PUSH_TIME = 2;

    //价格方案发送MQ消息操作类型
    private static final int PLAN_PUSH_ADD = 1;
    private static final int PLAN_PUSH_DELETE = 2;
    private static final int PLAN_PUSH_CHANGE = 3;

    //菜谱方案草稿
    private static final int PLAN_TYPE = 0;
    // 保存数据库
    private static final int TO_DATA_BASE = 1;

    //售卖类型-0 默认（全时段）-1特殊时段
    private static final int SELL_TIME_TYPE = 0;

    /**
     * 菜谱商品分类最后一个
     */
    private static final int PLAN_ITEM_SORT_LAST_ONE = 999999;

    /**
     * 菜谱分类最后一个
     */
    private static final int PLAN_TYPE_SORT_LAST_ONE = 999999;

    private final ThreadPoolConfig threadPoolConfig;

    /**
     * 会员优惠券活动查询菜谱下的菜品
     *
     * @param request planGuidList
     * @return List<BaseTypeSkuRespDTO>
     */
    @Override
    public TypeSkuPricePlanRespDTO memberPricePlanItemByGuidList(RequestProductInfo request) {
        TypeSkuPricePlanRespDTO planRespDTO = new TypeSkuPricePlanRespDTO();
        List<String> planGuidList = Arrays.asList(request.getPricePlanGuidArray().split(","));

        // 查询菜谱下菜品
        LambdaQueryWrapper<PricePlanItemDO> itemQueryWrapperWrapper = new LambdaQueryWrapper<>();
        itemQueryWrapperWrapper.eq(PricePlanItemDO::getIsDelete, 0);
        itemQueryWrapperWrapper.in(PricePlanItemDO::getIsSoldOut, 0, 1, 3);
        itemQueryWrapperWrapper.in(PricePlanItemDO::getPlanGuid, planGuidList);
        if (StringUtils.isNotEmpty(request.getKeywords())) {
            itemQueryWrapperWrapper.like(PricePlanItemDO::getPlanItemName, request.getKeywords());
        }
        List<PricePlanItemDO> pricePlanItemDOList = planItemMapper.selectList(itemQueryWrapperWrapper);
        log.info("会员优惠券活动查询菜谱下的菜品pricePlanItemDOList:{}", pricePlanItemDOList);

        // 没有菜品，返回空
        if (CollectionUtils.isEmpty(pricePlanItemDOList)) {
            // 当前是菜谱模式，但是没有可用的菜谱，2作为占位使用
            planRespDTO.setPricePlanGuidArray("2");
            return planRespDTO;
        }

        // 如果存在多个菜谱，可能存在重复的菜，所以要去重
        List<PricePlanItemDO> uniquePricePlanItemDOList = pricePlanItemDOList.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PricePlanItemDO::getSkuGuid))), ArrayList::new));

        // 按分类  分组菜谱商品
        Map<String, List<PricePlanItemDO>> typeItemMap = uniquePricePlanItemDOList.stream()
                .collect(Collectors.groupingBy(PricePlanItemDO::getTypeGuid));
        // 按商品  分组菜谱商品
        Map<String, List<PricePlanItemDO>> itemSkuMap = uniquePricePlanItemDOList.stream()
                .collect(Collectors.groupingBy(PricePlanItemDO::getItemGuid));

        // 取出所有分类GUID
        List<String> typeGuidList = uniquePricePlanItemDOList
                .stream()
                .map(PricePlanItemDO::getTypeGuid)
                .collect(Collectors.toList())
                .stream().distinct().collect(Collectors.toList());

        // 取出所有规格GUID，并查询出规格详情
        List<String> skuGuidList = uniquePricePlanItemDOList
                .stream()
                .map(PricePlanItemDO::getSkuGuid)
                .collect(Collectors.toList());

        LambdaQueryWrapper<SkuDO> skuQueryWrapperWrapper = new LambdaQueryWrapper<>();
        skuQueryWrapperWrapper.in(SkuDO::getGuid, skuGuidList);
        List<SkuDO> skuDOList = skuService.list(skuQueryWrapperWrapper);
        Map<String, SkuDO> skuMap = skuDOList.stream()
                .collect(Collectors.toMap(SkuDO::getGuid, sku -> sku));


        List<TypeDO> typeList = (ArrayList) typeService.listByIds(typeGuidList);
        log.info("会员优惠券活动查询菜谱下的菜品typeList:{}", typeList);
        Map<String, TypeDO> typeMap = typeList.stream()
                .collect(Collectors.toMap(TypeDO::getGuid, type -> type));

        List<TypeSkuRespDTO> baseTypeSkuRespDTOList = Lists.newArrayList();
        typeItemMap.forEach((typeGuid, itemList) -> {
            TypeSkuRespDTO baseTypeSkuRespDTO = new TypeSkuRespDTO();
            TypeDO typeDO = typeMap.get(typeGuid);
            if (Objects.nonNull(typeDO)) {
                baseTypeSkuRespDTO.setName(typeMap.get(typeGuid).getName());
            }

            baseTypeSkuRespDTO.setTypeGuid(typeGuid);

            List<TypeSkuRespDTO.SkuNameRespDTO> skuNameRespDTOList = new ArrayList<>();
            List<PricePlanItemDO> uniqueItemList = itemList
                    .stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                            new TreeSet<>(Comparator.comparing(PricePlanItemDO::getItemGuid))), ArrayList::new));

            uniqueItemList.forEach(item -> {
                String planItemName = item.getPlanItemName();
                String itemGuid = item.getItemGuid();
                List<PricePlanItemDO> skuList = itemSkuMap.get(itemGuid);
                skuList.forEach(po -> {
                    SkuDO skuDO = skuMap.get(po.getSkuGuid());
                    if (ObjectUtils.isEmpty(skuDO)) {
                        log.warn("商品数据异常1 skuGuid={}", po.getSkuGuid());
                        return;
                    }
                    TypeSkuRespDTO.SkuNameRespDTO skuNameRespDTO = new TypeSkuRespDTO.SkuNameRespDTO();
                    skuNameRespDTO.setItemGuid(itemGuid);
                    StringBuilder sb = new StringBuilder(planItemName);
                    if (!StringUtils.isEmpty(skuDO.getName())) {
                        sb.append("(" + skuDO.getName() + ")");
                        skuNameRespDTO.setName(sb.toString());
                    } else {
                        skuNameRespDTO.setName(planItemName);
                    }
                    skuNameRespDTO.setSkuGuid(po.getSkuGuid());
                    skuNameRespDTO.setSalePrice(po.getSalePrice());
                    skuNameRespDTOList.add(skuNameRespDTO);
                });

            });

            baseTypeSkuRespDTO.setSkuNameRespDTOList(skuNameRespDTOList);
            if (!CollectionUtils.isEmpty(baseTypeSkuRespDTO.getSkuNameRespDTOList())) {
                baseTypeSkuRespDTOList.add(baseTypeSkuRespDTO);
            }

        });
        log.info("会员优惠券活动查询菜谱下的菜品baseTypeSkuRespDTOList:{}", baseTypeSkuRespDTOList);

        String planAllGuid = planGuidList.toString()
                .replace("[", "")
                .replace("]", "")
                .replaceAll(" ", "");
        planRespDTO.setPricePlanGuidArray(planAllGuid);
        planRespDTO.setTypeSkuRespDTOList(baseTypeSkuRespDTOList);

        return planRespDTO;
    }

    /**
     * 会员优惠券活动查询菜谱下的菜品
     *
     * @param itemSingleDTO itemSingleDTO
     * @return List<BaseTypeSkuRespDTO>
     */
    @Override
    public TypeSkuPricePlanRespDTO memberPricePlanItem(ItemSingleDTO itemSingleDTO) {
        TypeSkuPricePlanRespDTO planRespDTO = new TypeSkuPricePlanRespDTO();

        String storeGuid = itemSingleDTO.getData();
        String itemName = itemSingleDTO.getKeywords();
        List<PricePlanNowDTO> planList = pricePlanStoreService.findPlanMemberStoreGuid(storeGuid);
        if (CollectionUtils.isEmpty(planList)) {
            // 当前是菜谱模式，但是没有可用的菜谱，2作为占位使用
            planRespDTO.setPricePlanGuidArray("2");
            return planRespDTO;
        }

        List<String> planGuidList = planList.stream().map(PricePlanNowDTO::getPlanGuid).collect(Collectors.toList());

        // 查询菜谱下菜品
        LambdaQueryWrapper<PricePlanItemDO> itemQueryWrapperWrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isEmpty(itemName)) {
            itemQueryWrapperWrapper.like(PricePlanItemDO::getPlanItemName, itemName);
        }
        itemQueryWrapperWrapper.eq(PricePlanItemDO::getIsDelete, 0);
        itemQueryWrapperWrapper.in(PricePlanItemDO::getIsSoldOut, 0, 1, 3);
        itemQueryWrapperWrapper.in(PricePlanItemDO::getPlanGuid, planGuidList);
        List<PricePlanItemDO> pricePlanItemDOList = planItemMapper.selectList(itemQueryWrapperWrapper);

        // 没有菜品，返回空
        if (CollectionUtils.isEmpty(pricePlanItemDOList)) {
            // 当前是菜谱模式，但是没有可用的菜谱，2作为占位使用
            planRespDTO.setPricePlanGuidArray("2");
            return planRespDTO;
        }

        // 如果存在多个菜谱，可能存在重复的菜，所以要去重
        List<PricePlanItemDO> uniquePricePlanItemDOList = pricePlanItemDOList.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PricePlanItemDO::getSkuGuid))), ArrayList::new));

        // 按分类  分组菜谱商品
        Map<String, List<PricePlanItemDO>> typeItemMap = uniquePricePlanItemDOList.stream()
                .collect(Collectors.groupingBy(PricePlanItemDO::getTypeGuid));
        // 按商品  分组菜谱商品
        Map<String, List<PricePlanItemDO>> itemSkuMap = uniquePricePlanItemDOList.stream()
                .collect(Collectors.groupingBy(PricePlanItemDO::getItemGuid));

        // 取出所有分类GUID
        List<String> typeGuidList = uniquePricePlanItemDOList
                .stream()
                .map(PricePlanItemDO::getTypeGuid)
                .collect(Collectors.toList())
                .stream().distinct().collect(Collectors.toList());

        // 取出所有规格GUID，并查询出规格详情
        List<String> skuGuidList = uniquePricePlanItemDOList
                .stream()
                .map(PricePlanItemDO::getSkuGuid)
                .collect(Collectors.toList());

        LambdaQueryWrapper<SkuDO> skuQueryWrapperWrapper = new LambdaQueryWrapper<>();
        skuQueryWrapperWrapper.in(SkuDO::getGuid, skuGuidList);
        List<SkuDO> skuDOList = skuService.list(skuQueryWrapperWrapper);
        Map<String, SkuDO> skuMap = skuDOList.stream()
                .collect(Collectors.toMap(SkuDO::getGuid, sku -> sku));

        List<TypeDO> typeList = (ArrayList) typeService.listByIds(typeGuidList);
        Map<String, TypeDO> typeMap = typeList.stream()
                .collect(Collectors.toMap(TypeDO::getGuid, type -> type));

        List<TypeSkuRespDTO> baseTypeSkuRespDTOList = Lists.newArrayList();
        typeItemMap.forEach((typeGuid, itemList) -> {
            TypeSkuRespDTO baseTypeSkuRespDTO = new TypeSkuRespDTO();
            TypeDO typeDO = typeMap.get(typeGuid);
            if (Objects.nonNull(typeDO)) {
                baseTypeSkuRespDTO.setName(typeDO.getName());
            }
            baseTypeSkuRespDTO.setTypeGuid(typeGuid);

            List<TypeSkuRespDTO.SkuNameRespDTO> skuNameRespDTOList = new ArrayList<>();
            List<PricePlanItemDO> uniqueItemList = itemList
                    .stream()
                    .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                            new TreeSet<>(Comparator.comparing(PricePlanItemDO::getItemGuid))), ArrayList::new));

            uniqueItemList.forEach(item -> {
                String planItemName = item.getPlanItemName();
                String itemGuid = item.getItemGuid();
                List<PricePlanItemDO> skuList = itemSkuMap.get(itemGuid);
                skuList.forEach(po -> {
                    SkuDO skuDO = skuMap.get(po.getSkuGuid());
                    if (ObjectUtils.isEmpty(skuDO)) {
                        log.warn("商品数据异常2 skuGuid={}", po.getSkuGuid());
                        return;
                    }
                    TypeSkuRespDTO.SkuNameRespDTO skuNameRespDTO = new TypeSkuRespDTO.SkuNameRespDTO();
                    skuNameRespDTO.setItemGuid(itemGuid);
                    StringBuilder sb = new StringBuilder(planItemName);
                    if (!StringUtils.isEmpty(skuDO.getName())) {
                        sb.append("(").append(skuDO.getName()).append(")");
                        skuNameRespDTO.setName(sb.toString());
                    } else {
                        skuNameRespDTO.setName(planItemName);
                    }
                    skuNameRespDTO.setSkuGuid(po.getSkuGuid());
                    skuNameRespDTO.setSalePrice(po.getSalePrice());
                    skuNameRespDTOList.add(skuNameRespDTO);
                });

            });

            baseTypeSkuRespDTO.setSkuNameRespDTOList(skuNameRespDTOList);
            if (!CollectionUtils.isEmpty(baseTypeSkuRespDTO.getSkuNameRespDTOList())) {
                baseTypeSkuRespDTOList.add(baseTypeSkuRespDTO);
            }

        });

        String planAllGuid = planGuidList.toString()
                .replace("[", "")
                .replace("]", "")
                .replaceAll(" ", "");
        planRespDTO.setPricePlanGuidArray(planAllGuid);
        planRespDTO.setTypeSkuRespDTOList(baseTypeSkuRespDTOList);

        return planRespDTO;
    }

    /**
     * 查询扫码点餐菜谱变动情况
     *
     * @param pricePlanChangeRequestDTO pricePlanChangeRequestDTO
     * @return PricePlanChangeResponseDTO
     */
    @Override
    public PricePlanChangeResponseDTO pricePlanChangeInfo(PricePlanChangeRequestDTO pricePlanChangeRequestDTO) {
        String storeGuid = pricePlanChangeRequestDTO.getStoreGuid();
        String pricePlanGuid = pricePlanChangeRequestDTO.getPricePlanGuid();
        List<String> skuGuidList = pricePlanChangeRequestDTO.getSkuGuidList();

        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        log.info("pricePlanChangeInfo菜品模式数据：{}", JacksonUtils.writeValueAsString(brandDTO));
        if (brandDTO.getSalesModel() == SalesModelEnum.NORMAL_MODE.getCode()) {
            // 由菜谱模式，变为普通模式，提示发生了变化
            if (StringUtils.isNotBlank(pricePlanGuid)) {
                PricePlanChangeResponseDTO pricePlanChangeResponseDTO = new PricePlanChangeResponseDTO();
                pricePlanChangeResponseDTO.setChangeMessage("菜品已更新，需要退出重新扫码选购商品哦~");
                pricePlanChangeResponseDTO.setChangeType(PlanChangeTypeEnum.PRICE_PLAN_CHANGE.getCode());
                return pricePlanChangeResponseDTO;
            }

            // 请求来的是普通模式，后台配置也是普通模式，没有发生变化
            PricePlanChangeResponseDTO pricePlanChangeResponseDTO = new PricePlanChangeResponseDTO();
            pricePlanChangeResponseDTO.setChangeType(PlanChangeTypeEnum.PRICE_PLAN_MODEL.getCode());
            return pricePlanChangeResponseDTO;
        }

        // 查询生效的菜谱方案
        LocalDateTime nowDateTime = LocalDateTime.now();
        List<PricePlanNowDTO> planNowList = pricePlanStoreService.findPlanNowStoreGuid(nowDateTime, storeGuid);
        log.info("pricePlanChangeInfo菜品方案查询结果：{}", JacksonUtils.writeValueAsString(planNowList));
        if (CollectionUtil.isEmpty(planNowList)) {
            log.info("不存在菜谱");
            PricePlanChangeResponseDTO pricePlanChangeResponseDTO = new PricePlanChangeResponseDTO();
            pricePlanChangeResponseDTO.setChangeMessage("");
            pricePlanChangeResponseDTO.setChangeType(PlanChangeTypeEnum.PRICE_PLAN_MODEL.getCode());
            return pricePlanChangeResponseDTO;
        }

        int nowTime = Integer.parseInt(nowDateTime.format(DateTimeFormatter.ofPattern("HH:mm")).replace(":", ""));
        String pricePlanNowAllGuid = "";
        String pricePlanNowGuid = "";
        for (PricePlanNowDTO planNow : planNowList) {
            Integer sellTimeType = planNow.getSellTimeType();
            if (sellTimeType == 0) {
                // 全时段菜品方案
                pricePlanNowAllGuid = planNow.getPlanGuid();
                log.info("全时段方案筛选结果：,{}", pricePlanNowAllGuid);
            } else {
                // 特殊时段菜品方案
                LocalDateTime startTime = planNow.getStartTime();
                LocalDateTime endTime = planNow.getEndTime();
                int startTimeInt = Integer.parseInt(startTime.format(DateTimeFormatter.ofPattern("HH:mm")).replace(":", ""));
                int endTimeInt = Integer.parseInt(endTime.format(DateTimeFormatter.ofPattern("HH:mm")).replace(":", ""));

                log.info("特殊时段时间,nowTime->{}, startTimeInt->{}, endTimeInt->{}", nowTime, startTimeInt, endTimeInt);
                if (endTimeInt > startTimeInt) {
                    // 灭有垮天
                    if (nowTime >= startTimeInt && nowTime <= endTimeInt) {
                        pricePlanNowGuid = planNow.getPlanGuid();
                        log.info("特殊时段方案筛选结果：,{}", pricePlanNowGuid);
                    }
                } else {
                    // 垮天了
                    if (nowTime >= startTimeInt || nowTime <= endTimeInt) {
                        pricePlanNowGuid = planNow.getPlanGuid();
                        log.info("特殊时段垮天方案筛选结果：,{}", pricePlanNowGuid);
                    }
                }

            }
        }

        // 存在有效的菜谱，但是没有进行中的菜谱
        if (StringUtils.isBlank(pricePlanNowGuid) && StringUtils.isBlank(pricePlanNowAllGuid)) {
            log.info("存在有效的菜谱，但是没有进行中的菜谱");
            PricePlanChangeResponseDTO pricePlanChangeResponseDTO = new PricePlanChangeResponseDTO();
            pricePlanChangeResponseDTO.setChangeMessage("");
            pricePlanChangeResponseDTO.setChangeType(PlanChangeTypeEnum.PRICE_PLAN_NOT_EXIST.getCode());
            return pricePlanChangeResponseDTO;

        }

        if (StringUtils.isBlank(pricePlanGuid)) {
            log.info("前端是普通模式，现在变成了菜谱模式");
            PricePlanChangeResponseDTO pricePlanChangeResponseDTO = new PricePlanChangeResponseDTO();
            pricePlanChangeResponseDTO.setChangeMessage("菜品已更新，需要退出重新扫码选购商品哦~");
            pricePlanChangeResponseDTO.setChangeType(PlanChangeTypeEnum.PRICE_PLAN_CHANGE.getCode());
            pricePlanChangeResponseDTO.setPlanGiud("".equals(pricePlanNowAllGuid) ? pricePlanNowGuid : pricePlanNowAllGuid);
            return pricePlanChangeResponseDTO;
        }

        // 菜谱菜品发生了变化 从全时段切道了  特殊时段
        if (StringUtils.isNotBlank(pricePlanNowGuid)) {
            if (!pricePlanGuid.equals(pricePlanNowGuid)) {
                log.info("菜谱菜品发生了变化 从全时段切道了  特殊时段");
                PricePlanChangeResponseDTO pricePlanChangeResponseDTO = new PricePlanChangeResponseDTO();
                pricePlanChangeResponseDTO.setChangeType(PlanChangeTypeEnum.PRICE_PLAN_CHANGE.getCode());
                pricePlanChangeResponseDTO.setPlanGiud(pricePlanNowGuid);

                return pricePlanChangeResponseDTO;
            } else {
                boolean change = isChange(pricePlanGuid, skuGuidList);
                // 发生了变化
                if (change) {
                    log.info("菜谱菜品发生了变化 change a");
                    PricePlanChangeResponseDTO pricePlanChangeResponseDTO = new PricePlanChangeResponseDTO();
                    pricePlanChangeResponseDTO.setChangeMessage("菜品已更新，需要退出重新扫码选购商品哦~");
                    pricePlanChangeResponseDTO.setChangeType(PlanChangeTypeEnum.PRICE_PLAN_MODIFY.getCode());
                    pricePlanChangeResponseDTO.setPlanGiud(pricePlanGuid);

                    return pricePlanChangeResponseDTO;
                }
            }
        } else {
            if (StringUtils.isNotBlank(pricePlanNowAllGuid)) {
                if (!pricePlanGuid.equals(pricePlanNowAllGuid)) {
                    log.info("菜谱菜品发生了变化 从特殊时段切道了  全时段");
                    PricePlanChangeResponseDTO pricePlanChangeResponseDTO = new PricePlanChangeResponseDTO();
                    pricePlanChangeResponseDTO.setPlanGiud(pricePlanNowAllGuid);
                    pricePlanChangeResponseDTO.setChangeType(PlanChangeTypeEnum.PRICE_PLAN_CHANGE.getCode());
                    return pricePlanChangeResponseDTO;
                } else {
                    boolean change = isChange(pricePlanGuid, skuGuidList);
                    // 发生了变化
                    if (change) {
                        log.info("菜谱菜品发生了变化 change b");
                        PricePlanChangeResponseDTO pricePlanChangeResponseDTO = new PricePlanChangeResponseDTO();
                        pricePlanChangeResponseDTO.setChangeMessage("菜品已更新，需要退出重新扫码选购商品哦~");
                        pricePlanChangeResponseDTO.setChangeType(PlanChangeTypeEnum.PRICE_PLAN_MODIFY.getCode());
                        pricePlanChangeResponseDTO.setPlanGiud(pricePlanGuid);

                        return pricePlanChangeResponseDTO;
                    }
                }
            }
        }

        log.info("菜谱菜品发生了变化 end");

        PricePlanChangeResponseDTO pricePlanChangeResponseDTO = new PricePlanChangeResponseDTO();
        pricePlanChangeResponseDTO.setPlanGiud(pricePlanNowGuid);
        pricePlanChangeResponseDTO.setChangeType(PlanChangeTypeEnum.PRICE_PLAN_NORMAL.getCode());
        return pricePlanChangeResponseDTO;
    }

    /**
     * 菜谱的菜谱是否发生了变化，true 发生了变化
     *
     * @param pricePlanGuid pricePlanGuid
     * @param skuGuidList   skuGuidList
     * @return boolean
     */
    private boolean isChange(String pricePlanGuid, List<String> skuGuidList) {
        //临时修改 暂不判断菜品信息是否修改
        return false;
        /*List<PricePlanItemDO> planItemDataList = planItemService.list(new LambdaQueryWrapper<PricePlanItemDO>()
                .eq(PricePlanItemDO::getIsDelete, 0)
                .in(PricePlanItemDO::getIsSoldOut, 0, 1, 3)
                .eq(PricePlanItemDO::getPlanGuid, pricePlanGuid));
        List<String> itemSkuGuidDataList = planItemDataList.stream().map(PricePlanItemDO::getSkuGuid).collect(Collectors.toList());
        List<SkuDO> skuDOList = skuService.list(new LambdaQueryWrapper<SkuDO>()
                .eq(SkuDO::getIsJoinWeChat, 1)
                .in(SkuDO::getGuid, itemSkuGuidDataList));
        List<String> itemSkuGuidDataList2 = skuDOList.stream().map(SkuDO::getGuid).collect(Collectors.toList());
        itemSkuGuidDataList2.sort(Comparator.comparing(String::hashCode));
        skuGuidList.sort(Comparator.comparing(String::hashCode));
        boolean isEquals = itemSkuGuidDataList2.toString().equals(skuGuidList.toString());
        return !isEquals;*/
    }

    /**
     * 商品销售模式变更
     *
     * @param brandGuid  brandGuid
     * @param salesModel salesModel
     */
    @Override
    public void changeSaleModel(String brandGuid, Integer salesModel) {
        PricePlanPageReqDTO reqDTO = new PricePlanPageReqDTO();
        reqDTO.setBrandGuid(brandGuid);
        List<PricePlanRespDTO> pricePlanRespList = pricePlanMapper.planStateList(reqDTO);

        // 切换到普通模式后，停用菜谱下的所有定时推送任务
        if (salesModel == SalesModelEnum.NORMAL_MODE.getCode()) {
            for (PricePlanRespDTO respDTO : pricePlanRespList) {
                PricePlanDO pricePlanDO = new PricePlanDO();
                pricePlanDO.setGuid(respDTO.getGuid());
                pricePlanDO.setBrandGuid(brandGuid);
                planPriceDisabledCache(pricePlanDO);
            }

            // 推送消息给安卓端
            List<StoreDTO> storeDTOList = organizationService.queryStoreByBrandList(Arrays.asList(brandGuid));
            List<String> storeGuidList = storeDTOList.stream().map(StoreDTO::getGuid).collect(Collectors.toList());
//            LocalDateTime nowDateTime = LocalDateTime.now();
//            List<PricePlanNowDTO> planNowStoreList = pricePlanStoreService.findPlanNowStoreList(nowDateTime, storeGuidList);
//            List<String> storeGuidNowList = planNowStoreList.stream().map(PricePlanNowDTO::getStoreGuid).collect(Collectors.toList());
            log.info("待推送的门店：{}", JacksonUtils.writeValueAsString(storeGuidList));
            if (CollectionUtils.isNotEmpty(storeGuidList)) {
                log.info("菜谱提示异常日志跟踪9}");
                eventPushHelper.pushMsgToAndriod(storeGuidList);
            }

            // 切换到菜谱模式
        } else {
            for (PricePlanRespDTO respDTO : pricePlanRespList) {
                PricePlanDO pricePlanDO = new PricePlanDO();
                pricePlanDO.setGuid(respDTO.getGuid());
                pricePlanDO.setPushType(respDTO.getPushType());
                pricePlanDO.setSellTimeType(respDTO.getSellTimeType());
                pricePlanDO.setPushDate(LocalDateTime.now());
                pricePlanDO.setStartTime(respDTO.getStartTime());
                pricePlanDO.setEndTime(respDTO.getEndTime());
                pricePlanDO.setBrandGuid(respDTO.getBrandGuid());
                // 立即生效  预计生效时间
                pushPlan("模式切换<菜谱模式>", pricePlanDO, null);
            }
        }
    }

    /**
     * 测试推送
     *
     * @param pricePlanGuid guid
     * @return Integer
     */
    @Override
    public Integer testPush(String pricePlanGuid) {

        return 1;
    }

    /**
     * 一体机预点餐验证
     *
     * @param preOrderValidateReq preOrderValidateReq
     * @return PricePlanRespDTO
     */
    @Override
    public PreOrderValidateRespDTO preOrderVerification(PreOrderValidateReq preOrderValidateReq) {
        String storeGuid = preOrderValidateReq.getStoreGuid();
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        // 开启菜谱方式模式
        if (brandDTO.getSalesModel() == SalesModelEnum.RECIPE_MODE.getCode()) {
            LocalDateTime nowDateTime = LocalDateTime.now();
            // 查询生效的菜谱方案
            List<PricePlanNowDTO> planNowList = pricePlanStoreService.findPlanNowStoreGuid(nowDateTime, storeGuid);
            PricePlanNowDTO pricePlanNowAllTime = null;
            boolean hasSpecialTimePlan = false;
            for (PricePlanNowDTO planNow : planNowList) {
                Integer sellTimeType = planNow.getSellTimeType();
                if (sellTimeType == 0) {
                    // 全时段菜品方案
                    pricePlanNowAllTime = planNow;
                } else {
                    // 进行中的特殊时段菜谱
                    LocalDateTime startTime = planNow.getStartTime();
                    LocalDateTime endTime = planNow.getEndTime();
                    int startTimeInt = Integer.parseInt(startTime.format(DateTimeFormatter.ofPattern("HH:mm")).replace(":", ""));
                    int endTimeInt = Integer.parseInt(endTime.format(DateTimeFormatter.ofPattern("HH:mm")).replace(":", ""));
                    LocalTime nowTime = LocalTime.now();
                    int nowTimeInt = Integer.parseInt(nowTime.format(DateTimeFormatter.ofPattern("HH:mm")).replace(":", ""));
                    log.info("有正在售卖的特殊菜谱now:{}, sta:{},end:{}", nowTimeInt, startTimeInt, endTimeInt);
                    if (nowTimeInt >= startTimeInt && nowTimeInt <= endTimeInt) {
                        log.info("有正在售卖的特殊菜谱 ...");
                        hasSpecialTimePlan = true;
                    }
                }
            }

            PreOrderValidateRespDTO preOrderValidateRespDTO = new PreOrderValidateRespDTO();

            // 没有全时段菜谱方案
            if (Objects.isNull(pricePlanNowAllTime)) {
                preOrderValidateRespDTO.setDesc("因当前只有特殊时段菜谱请到后台添加全时段菜谱进行预约下单");
                return preOrderValidateRespDTO;
            }
            if (hasSpecialTimePlan) {
                preOrderValidateRespDTO.setDesc("因当前是特殊时段菜谱，无法预约全时段菜谱。");
                return preOrderValidateRespDTO;
            }

            return null;
        }

        return null;
    }

    /**
     * 价格方案保存
     *
     * @param reqDTO PricePlanReqDTO
     * @return 方案guid
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String savePlan(PricePlanReqDTO reqDTO) {
        log.info("保存菜谱操作基础信息：guid:{},pushType:{},status:{},planType:{}", reqDTO.getGuid(), reqDTO.getPushType(), reqDTO.getStatus(), reqDTO.getPlanType());
        // 前置处理
        LocalDateTime now = LocalDateTime.now();
        beforeHandle(reqDTO);

        //保存草稿到缓存中
        if (!ObjectUtils.isEmpty(reqDTO.getPlanType()) && PLAN_TYPE == reqDTO.getPlanType()) {
            String redisKey = PLAN_CACHE + UserContextUtils.getUserGuid() + COLON + reqDTO.getBrandGuid();
            redisUtil.set(redisKey, JSON.toJSONString(reqDTO));
            return null;
        }

        List<PricePlanItemDO> updateBeforePlanItemDOList = Lists.newArrayList();
        List<PricePlanItemDO> updateAfterItemList = Lists.newArrayList();
        PricePlanDO updateBeforePricePlanDO = new PricePlanDO();
        PricePlanDO pricePlanDO;
        if (StringUtils.isBlank(reqDTO.getGuid())) {
            pricePlanDO = addPricePlan(reqDTO, null);
        } else {
            //编辑方案
            // 菜谱预览：编辑时先行把所有商品设置为非最新编辑
            planItemService.update(new LambdaUpdateWrapper<PricePlanItemDO>()
                    .set(PricePlanItemDO::getNewUpdateFlag, Boolean.FALSE)
                    .eq(PricePlanItemDO::getPlanGuid, reqDTO.getGuid())
                    .ne(PricePlanItemDO::getIsSoldOut, ItemStateEnum.IMMEDIATELY_DELETE.getCode())
                    .eq(PricePlanItemDO::getNewUpdateFlag, Boolean.TRUE)
                    .eq(PricePlanItemDO::getIsDelete, Boolean.FALSE)
            );

            pricePlanDO = this.getById(reqDTO.getGuid());
            pricePlanDO.setUpdateTime(now);
            BeanUtil.copyProperties(pricePlanDO, updateBeforePricePlanDO);

            // 编辑时商品数量校验
            checkItemNum(reqDTO, pricePlanDO);

            //方案已启用状态且为预计生效保存备份 生效时再推送终端
            if (PricePlanStatusEnum.USING.getCode() == pricePlanDO.getStatus()
                    && 2 == reqDTO.getPushType() && PricePlanStatusEnum.USING.getCode() == reqDTO.getStatus()) {
                updateUsingPricePlan(pricePlanDO, reqDTO);
                return pricePlanDO.getGuid();
            }

            //批量编辑菜谱时使用
            if (ObjectUtil.equal(reqDTO.getBatchEdit(), Boolean.TRUE) && 2 == reqDTO.getPushType()) {
                pricePlanDO.setPushDate(reqDTO.getPushDate());
                pricePlanDO.setInstantlyEffectiveTime(reqDTO.getPushDate());
            }

            // 查询修改前的菜品
            updateBeforePlanItemDOList = planItemService.list(new LambdaQueryWrapper<PricePlanItemDO>()
                    .eq(PricePlanItemDO::getIsDelete, 0)
                    .eq(PricePlanItemDO::getPlanGuid, reqDTO.getGuid())
            );

            //重名校验
            if (!reqDTO.getName().equals(pricePlanDO.getName()) && pricePlanMapper.existSameName(reqDTO)) {
                throw new BusinessException("已存在同名方案");
            }
            pricePlanDO.setBrandGuid(reqDTO.getBrandGuid())
                    .setName(reqDTO.getName())
                    .setDescription(reqDTO.getDescription())
                    .setSellTimeType(reqDTO.getSellTimeType())
                    .setStatus(reqDTO.getStatus())
                    .setPushType(reqDTO.getPushType())
                    .setGmtModified(null)
            ;
            //立即生效
            if (Objects.equals(PricePlanStatusEnum.USING.getCode(), reqDTO.getStatus())) {
                if (1 == reqDTO.getPushType()) {
                    pricePlanDO.setPushDate(now);
                    pricePlanDO.setEffectiveTime(now);
                    pricePlanDO.setInstantlyEffectiveTime(null);
                } else {
                    //预计生效时间
                    pricePlanDO.setPushDate(reqDTO.getPushDate());
                    pricePlanDO.setInstantlyEffectiveTime(reqDTO.getPushDate());
                    if (reqDTO.getPushDate().isAfter(now)) {
                        pricePlanDO.setStatus(PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode());
                    } else {
                        pricePlanDO.setStatus(PricePlanStatusEnum.USING.getCode());
                    }
                }
            }

            if (PricePlanStatusEnum.NOT_ENABLED.getCode() == reqDTO.getStatus()) {
                pricePlanDO.setInstantlyEffectiveTime(null);
            }

            //商品售卖时段  特殊时段
            if (SELL_TIME_TYPE != reqDTO.getSellTimeType()) {
                pricePlanDO.setStartTime(reqDTO.getStartTime());
                pricePlanDO.setEndTime(reqDTO.getEndTime());
            }
            this.updateById(pricePlanDO);
        }
        String planGuid = pricePlanDO.getGuid();

        // 分类处理
        Map<String, String> saveTypeMap = new HashMap<>();
        typeHandle(reqDTO, planGuid, saveTypeMap);

        //商品处理
        if (CollectionUtils.isNotEmpty(reqDTO.getItemWebRespDTOList())) {
            PricePlanItemAddReqDTO addReqDTO = new PricePlanItemAddReqDTO();
            addReqDTO.setPlanGuid(planGuid);
            addReqDTO.setBrandGuid(reqDTO.getBrandGuid());
            addReqDTO.setItemWebRespDTOList(reqDTO.getItemWebRespDTOList());
            log.info("菜谱保存商品处理：addReqDTO={}", JacksonUtils.writeValueAsString(addReqDTO));
            updateAfterItemList = planItemService.saveUpdatePlanItem(addReqDTO, saveTypeMap, false);
            // 更新方案包含商品数（多规格商品每种规格都算一个）
            Integer itemCount = planItemMapper.getCount(planGuid);
            pricePlanMapper.updateItemNum(planGuid, itemCount, 3);
        }
        //门店绑定
        List<PricePlanStoreDO> updateBeforePricePlanStoreList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(reqDTO.getStoreGuidList())) {
            // 查询修改前的门店
            updateBeforePricePlanStoreList = pricePlanStoreService.list(new LambdaQueryWrapper<PricePlanStoreDO>()
                    .eq(PricePlanStoreDO::getPlanGuid, planGuid));

            PricePlanStoreReqDTO planStoreReqDTO = new PricePlanStoreReqDTO();
            planStoreReqDTO.setPlanGuid(planGuid);
            planStoreReqDTO.setBrandGuid(reqDTO.getBrandGuid());
            planStoreReqDTO.setStoreGuidList(reqDTO.getStoreGuidList());
            // 如果方案修改为暂不启用清空所有绑定门店，并更新门店数
            if (Objects.equals(PricePlanStatusEnum.NOT_ENABLED.getCode(), reqDTO.getStatus())) {
                planStoreReqDTO.setStoreGuidList(new ArrayList<>());
            }
            pricePlanStoreService.bingPlanStore(planStoreReqDTO);
        }


        // 立即生效或定时菜谱推送处理(只有在启用或即将启用的)
        if (pricePlanDO.getStatus() == PricePlanStatusEnum.USING.getCode()
                || pricePlanDO.getStatus() == PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode()) {

            String sourceName = "新增菜谱";
            if (!StringUtils.isBlank(reqDTO.getGuid())) {
                sourceName = "修改菜谱";

                if (updateBeforePricePlanDO.getStatus() == PricePlanStatusEnum.USING.getCode()) {
                    // 售卖时段是否变了
                    boolean pushTimeChange = pushTimeChange(updateBeforePricePlanDO, reqDTO);
                    // 验证当前修改的菜谱是否在售卖中
                    if (!pushTimeChange) {
                        // 验证当前修改的菜谱是否在售卖中
                        boolean isSale = validateAllTimePlanPush(reqDTO.getStoreGuidList(), updateBeforePricePlanDO);
                        if ((isSale)) {
                            // 验证菜品名称，价格，数量变化
                            boolean itemChange = pricePlanInfoChange(updateBeforePlanItemDOList, updateAfterItemList);
                            if (itemChange) {
                                // 当推送时间没有变化时，如果名称等有变化，提示信息同全时段菜谱
                                // 所以这里把售卖类型 变成了 全时段
                                pricePlanDO.setSellTimeType(0);
                            }
                        }

                        // 通知被删除的门店，菜谱发生了变更
                        planStoreRemovePush(updateBeforePricePlanStoreList, reqDTO.getStoreGuidList());
                    }
                } else if (updateBeforePricePlanDO.getStatus() == PricePlanStatusEnum.NOT_ENABLED.getCode()
                        && (pricePlanDO.getSellTimeType() == 0)) {
                    // 如果修改的是全时段菜谱
                    boolean isPush = validatePriceAllTime(reqDTO.getStoreGuidList());
                    if (!isPush) {
                        return planGuid;
                    }

                }
            }
            // 立即生效 预计生效时间
            pushPlan(sourceName, pricePlanDO, reqDTO.getStoreGuidList());
            return planGuid;
        }

        if (StringUtils.isBlank(reqDTO.getGuid())) {
            return planGuid;
        }

        // 菜谱计划暂停或永久停止后，移除定时推送（依据REDIS超时原理）
        planPriceDisabledCache(pricePlanDO);

        // 如果父id不为空删除子方案
        if (!ObjectUtils.isEmpty(pricePlanDO.getParentGuid())) {
            log.info("修改父方案状态为暂不启用");
            PricePlanDO planDO = this.getById(pricePlanDO.getParentGuid());
            if (planDO != null && !Objects.equals(PricePlanStatusEnum.NOT_ENABLED.getCode(), planDO.getStatus())) {
                planDO.setStatus(PricePlanStatusEnum.LASTING_DISABLE.getCode());
                this.updateById(planDO);
            }
            deleteToRackItem(planGuid);
        }

        if (CollectionUtils.isEmpty(reqDTO.getStoreGuidList())) {
            return planGuid;
        }

        // 门店为空，就不用判断模式
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(reqDTO.getStoreGuidList().get(0));
        if (brandDTO.getSalesModel() == SalesModelEnum.NORMAL_MODE.getCode()) {
            log.info("普通模式不推送菜谱方案");
            return planGuid;
        }

        // 验证当前修改的菜谱是否在售卖中
        boolean isSale = validateAllTimePlanPush(reqDTO.getStoreGuidList(), updateBeforePricePlanDO);
        if (isSale) {
            // 通知被删除的门店，菜谱发生了变更
            planStoreRemovePush(updateBeforePricePlanStoreList, reqDTO.getStoreGuidList());
            // 菜谱停用，门店被删除了，所以这里单独推送
            log.info("菜谱提示异常日志跟踪10}");
            eventPushHelper.pushMsgToAndriod(reqDTO.getStoreGuidList());
        }
        return planGuid;
    }

    /**
     * 暂不启用删除即将下架菜品
     *
     * @param planGuid 方案guid
     */
    private void deleteToRackItem(String planGuid) {
        List<PricePlanItemDO> list = planItemService.list(new LambdaQueryWrapper<PricePlanItemDO>()
                .eq(PricePlanItemDO::getPlanGuid, planGuid));
        ArrayList<String> toDelete = new ArrayList<>();
        list.forEach(planItem -> {
            if (Objects.equals(ItemStateEnum.IS_ABOUT_TO_DELETE.getCode(), planItem.getIsSoldOut())) {
                toDelete.add(planItem.getItemGuid());
            }
        });
        if (CollectionUtils.isNotEmpty(toDelete)) {
            log.warn("暂不启用删除即将下架菜品,toDelete={}", toDelete);
            planItemService.remove(new LambdaQueryWrapper<PricePlanItemDO>()
                    .in(PricePlanItemDO::getItemGuid, toDelete));
        }
    }

    /**
     * 分类处理
     */
    private void typeHandle(PricePlanReqDTO reqDTO, String planGuid, Map<String, String> saveTypeMap) {
        List<TypePricePlanReqDTO> typeList = reqDTO.getTypeList();
        if (CollectionUtils.isEmpty(typeList)) {
            return;
        }
        //先根据删除排序
        typeList.sort(Comparator.comparing(TypePricePlanReqDTO::getIsDelete).reversed());
        log.info("typeList={}", JacksonUtils.writeValueAsString(typeList));
        // 如果是批量编辑菜谱分类，需要先查询分类是否存在
        if (Boolean.TRUE.equals(reqDTO.getBatchEdit())) {
            addTypeGuid(reqDTO, planGuid, typeList);
        }
        for (TypePricePlanReqDTO type : typeList) {
            // 删除分类
            if (1 == type.getIsDelete()) {
                if (StringUtils.isEmpty(type.getTypeGuid())) {
                    continue;
                }
                deleteType(type);
            }
            // 商品分类Guid为null 分类名称不为null 则为新增分类
            insertType(type, planGuid, saveTypeMap);
            // 商品分类Guid不为null 为修改分类
            updateType(type, planGuid, reqDTO);
        }
        // 查询当前菜谱下所有的分类, 重新排序
        List<TypeDO> typeDbList = typeService.list(new LambdaQueryWrapper<TypeDO>()
                .eq(TypeDO::getPricePlanGuid, planGuid)
                .eq(TypeDO::getIsDelete, 0));
        if (CollectionUtils.isEmpty(typeDbList)) {
            return;
        }
        typeDbList = typeDbList.stream()
                .sorted(Comparator.comparing(TypeDO::getSort)
                        .thenComparing(Comparator.comparing(TypeDO::getGmtModified).reversed()))
                .collect(Collectors.toList());
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < typeDbList.size(); i++) {
            TypeDO typeDO = typeDbList.get(i);
            typeDO.setSort(i + 1);
            typeDO.setGmtModified(now);
        }
        typeService.updateBatchById(typeDbList);
    }

    /**
     * 删除分类
     */
    private void deleteType(TypePricePlanReqDTO type) {
        ItemSingleDTO singleDTO = new ItemSingleDTO();
        singleDTO.setData(type.getTypeGuid());
        typeService.deleteType(singleDTO);
    }

    /**
     * 新增分类
     */
    private void insertType(TypePricePlanReqDTO type, String planGuid, Map<String, String> saveTypeMap) {
        if (StringUtils.isEmpty(type.getTypeGuid()) && StringUtils.isNotBlank(type.getName())) {
            if (Objects.isNull(type.getSort())) {
                type.setSort(PLAN_TYPE_SORT_LAST_ONE);
            }
            addPricePlanType(planGuid, saveTypeMap, type);
        }
    }


    /**
     * 编辑分类
     */
    private void updateType(TypePricePlanReqDTO type, String planGuid, PricePlanReqDTO reqDTO) {
        // 商品分类Guid不为null 为修改分类
        if (StringUtils.isNotEmpty(type.getTypeGuid())) {
            TypeReqDTO typeReqDTO = new TypeReqDTO();
            typeReqDTO.setName(type.getName());
            if (Boolean.TRUE.equals(reqDTO.getBatchEdit()) && StringUtils.isNotEmpty(type.getUpdateAfterName())) {
                typeReqDTO.setName(type.getUpdateAfterName());
            }
            typeReqDTO.setPricePlanGuid(planGuid);
            typeReqDTO.setDescription(type.getDescription());
            typeReqDTO.setSort(type.getSort());
            typeReqDTO.setIsDelete(type.getIsDelete());
            typeReqDTO.setTypeGuid(type.getTypeGuid());
            typeReqDTO.setIsEnable(1);
            typeReqDTO.setFrom(5);
            typeService.updateType(typeReqDTO);
        }
    }


    /**
     * 查询分类是否存在, 并设置分类guid
     */
    private void addTypeGuid(PricePlanReqDTO reqDTO, String planGuid, List<TypePricePlanReqDTO> typeList) {
        // 查询目前菜谱所有分类
        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setData(planGuid);
        List<TypeWebRespDTO> typeRespList = typeService.queryTypeByPlan(itemSingleDTO);
        Map<String, TypeWebRespDTO> typeRespMap = typeRespList.stream()
                .collect(Collectors.toMap(TypeWebRespDTO::getName, Function.identity(), (key1, key2) -> key1));
        for (TypePricePlanReqDTO typeReq : typeList) {
            TypeWebRespDTO typeWebRespDTO = typeRespMap.get(typeReq.getName());
            if (Objects.nonNull(typeWebRespDTO)) {
                typeReq.setTypeGuid(typeWebRespDTO.getTypeGuid());
            }
        }
        // 商品所属分类
        List<ItemWebRespDTO> itemWebRespList = reqDTO.getItemWebRespDTOList();
        if (CollectionUtils.isEmpty(itemWebRespList)) {
            return;
        }
        for (ItemWebRespDTO itemWebRespDTO : itemWebRespList) {
            TypeWebRespDTO typeWebRespDTO = typeRespMap.get(itemWebRespDTO.getTypeName());
            if (Objects.nonNull(typeWebRespDTO)) {
                itemWebRespDTO.setTypeGuid(typeWebRespDTO.getTypeGuid());
            }
        }
    }

    private void beforeHandle(PricePlanReqDTO reqDTO) {
        if (CollectionUtils.isNotEmpty(reqDTO.getItemWebRespDTOList())) {
            Set<String> itemGuidSet = Sets.newHashSet();
            //清除前端错误数据
            reqDTO.getItemWebRespDTOList().removeIf(e -> StringUtils.isNotEmpty(reqDTO.getGuid()) &&
                    StringUtils.isNotEmpty(e.getPlanGuid()) && ObjectUtil.notEqual(e.getPlanGuid(), reqDTO.getGuid()));

            reqDTO.getItemWebRespDTOList().forEach(item -> {
                if (item.getPlanItemName().length() > 40) {
                    throw new BusinessException("菜谱方案商品名称请输入1-40个字符");
                }
                if (StringUtils.isNotEmpty(item.getItemGuid())) {
                    itemGuidSet.add(item.getItemGuid());
                }
            });
            if (CollectionUtil.isEmpty(itemGuidSet)) {
                throw new BusinessException("前端传递商品数据为空");
            }
            //校验前端传递的商品是否存在
            int count = itemMapper.countByGuidSet(itemGuidSet);
            if (count != itemGuidSet.size()) {
                throw new BusinessException("保存商品有误");
            }
        }
        // 分类删除校验分类下是否有商品
        checkPlanType(reqDTO);
        // 如果是按时间生效 并且传入的时间比服务器时间小 将其设为及时生效
        if (2 == reqDTO.getPushType() && null != reqDTO.getPushDate() &&
                !reqDTO.getPushDate().isAfter(LocalDateTime.now())) {
            log.warn("菜谱生效传入的时间比服务器时间小,将其设为及时生效");
            reqDTO.setPushType(1);
        }
        // 菜谱方案立即生效 需解绑菜谱冲突的菜谱方案关联
        if (1 == reqDTO.getPushType() && CollectionUtils.isNotEmpty(reqDTO.getStoreGuidList())) {
            unBindStoreAndPlan(reqDTO.getStoreGuidList(), reqDTO.getStartTime(), reqDTO.getEndTime(),
                    reqDTO.getSellTimeType(), reqDTO.getGuid());
        }
        // 延迟生效
        if (2 == reqDTO.getPushType() && CollectionUtils.isNotEmpty(reqDTO.getItemWebRespDTOList())) {
            reqDTO.getItemWebRespDTOList().forEach(
                    item -> {
                        if (Objects.equals(ItemStateEnum.IMMEDIATELY_DELETE.getCode(), item.getIsSoldOut())
                                && reqDTO.getPushDate().isAfter(LocalDateTime.now())) {
                            item.setIsSoldOut(ItemStateEnum.IS_ABOUT_TO_DELETE.getCode());
                        }
                    }
            );
        }
    }

    private void checkPlanType(PricePlanReqDTO reqDTO) {
        if (CollectionUtils.isEmpty(reqDTO.getTypeList())) {
            return;
        }
        Set<String> typeGuidSet = reqDTO.getTypeList().stream()
                .map(TypePricePlanReqDTO::getTypeGuid)
                .filter(e -> !StringUtils.isEmpty(e))
                .collect(Collectors.toSet());
        // 校验商品分类是否存在该菜谱下
        if (CollectionUtils.isNotEmpty(typeGuidSet)) {
            List<TypeDO> typeDbList = new ArrayList<>(typeService.listByIds(typeGuidSet));
            for (TypeDO typeDO : typeDbList) {
                if (!reqDTO.getGuid().equals(typeDO.getPricePlanGuid())) {
                    log.error("菜谱分类有误,菜谱guid:{}, 分类信息:{}", reqDTO.getGuid(), JacksonUtils.writeValueAsString(typeDO));
                    throw new BusinessException("菜谱分类有误，请退出刷新后重新编辑菜谱");
                }
            }
        }
        // 删除分类 校验
        Set<String> toDeleteSet = reqDTO.getTypeList().stream()
                .filter(t -> 1 == t.getIsDelete() && !StringUtils.isEmpty(t.getTypeGuid()))
                .map(TypePricePlanReqDTO::getTypeGuid)
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(toDeleteSet)) {
            Integer count = planItemMapper.selectCount(new LambdaQueryWrapper<PricePlanItemDO>()
                    .in(PricePlanItemDO::getTypeGuid, toDeleteSet)
                    .eq(PricePlanItemDO::getIsDelete, Boolean.FALSE)
                    .eq(PricePlanItemDO::getPlanGuid, reqDTO.getGuid())
            );
            if (count > 0) {
                throw new BusinessException("准备删除的分类下有商品，请退出刷新后重新编辑菜谱");
            }
        }
    }

    /**
     * 校验商品数量是否为空，为空抛出异常
     *
     * @param reqDTO      入参
     * @param pricePlanDO 方案
     */
    private void checkItemNum(PricePlanReqDTO reqDTO, PricePlanDO pricePlanDO) {
        if (CollectionUtils.isNotEmpty(reqDTO.getItemWebRespDTOList())) {
            // 需要删除的商品
            List<String> toDeleteSkuGuidList = Lists.newArrayList();
            List<String> toAddSkuGuidList = Lists.newArrayList();
            for (ItemWebRespDTO dto : reqDTO.getItemWebRespDTOList()) {
                if (CollectionUtils.isNotEmpty(dto.getSkuList())) {
                    toDeleteSkuGuidList.addAll(dto.getSkuList().stream()
                            .filter(e -> Objects.equals(ItemStateEnum.IMMEDIATELY_DELETE.getCode(), e.getIsSoldOut()))
                            .map(SkuRespDTO::getSkuGuid)
                            .collect(Collectors.toList()));
                    toAddSkuGuidList.addAll(dto.getSkuList().stream()
                            .filter(e -> ObjectUtils.isEmpty(e.getIsSoldOut()))
                            .map(SkuRespDTO::getSkuGuid)
                            .collect(Collectors.toList()));
                } else {
                    if (ObjectUtils.isEmpty(dto.getIsSoldOut())) {
                        toAddSkuGuidList.add(dto.getSkuGuid());
                    }
                    if (Objects.equals(ItemStateEnum.IMMEDIATELY_DELETE.getCode(), dto.getIsSoldOut())) {
                        toDeleteSkuGuidList.add(dto.getSkuGuid());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(toDeleteSkuGuidList)) {
                List<PricePlanItemDO> planItemList = planItemService.list(new LambdaQueryWrapper<PricePlanItemDO>()
                        .eq(PricePlanItemDO::getPlanGuid, reqDTO.getGuid())
                        .ne(PricePlanItemDO::getIsSoldOut, ItemStateEnum.IMMEDIATELY_DELETE.getCode())
                );
                List<PricePlanItemDO> filterItemList = planItemList.stream()
                        .filter(e -> !toDeleteSkuGuidList.contains(e.getSkuGuid())).collect(Collectors.toList());
                if (filterItemList.size() + toAddSkuGuidList.size() == 0) {
                    throw new BusinessException("下架商品后" + pricePlanDO.getName() + "菜谱商品将为空，请清除对应菜谱再保存");
                }
            }
        }
    }

    /**
     * 当修改全时段菜谱时，查看有没有正在进行中的特殊时段菜谱，
     * 存在特殊则不推送
     *
     * @param storeGuidList store
     * @return false 不推
     */
    private boolean validatePriceAllTime(List<String> storeGuidList) {

        if (CollUtil.isEmpty(storeGuidList)) {
            return true;
        }

        LocalDateTime nowDateTime = LocalDateTime.now();
        List<PricePlanNowDTO> planNowList = planStoreMapper.findPlanNowStoreGuidList(nowDateTime, storeGuidList);
        for (PricePlanNowDTO planNow : planNowList) {
            if (planNow.getSellTimeType() == 1) {
                LocalDateTime startTime = planNow.getStartTime();
                int startTimeInt = Integer.parseInt(startTime.format(DateTimeFormatter.ofPattern("HH:mm")).replace(":", ""));

                LocalDateTime endTime = planNow.getEndTime();
                int endTimeInt = Integer.parseInt(endTime.format(DateTimeFormatter.ofPattern("HH:mm")).replace(":", ""));

                LocalTime nowTime = LocalTime.now();
                int nowTimeInt = Integer.parseInt(nowTime.format(DateTimeFormatter.ofPattern("HH:mm")).replace(":", ""));

                log.info("有正在售卖的特殊菜谱now:{}, sta:{},end:{}", nowTimeInt, startTimeInt, endTimeInt);

                if (nowTimeInt >= startTimeInt && nowTimeInt <= endTimeInt) {
                    log.info("有正在售卖的特殊菜谱，则不推送修改的全时段菜谱 ...");
                    // 正在售卖特殊时段菜品
                    return false;
                }
            }
        }
        return true;

    }

    /**
     * 保存菜谱方案分类
     *
     * @param planGuid    菜谱方案Guid
     * @param saveTypeMap 分类名称和分类
     * @param typeDTO     分类信息
     */
    private void addPricePlanType(String planGuid, Map<String, String> saveTypeMap, TypePricePlanReqDTO typeDTO) {
        TypeReqDTO typeReqDTO = new TypeReqDTO();
        typeReqDTO.setName(typeDTO.getName());
        typeReqDTO.setPricePlanGuid(planGuid);
        typeReqDTO.setDescription(typeDTO.getDescription());
        typeReqDTO.setSort(typeDTO.getSort());
        typeReqDTO.setIsDelete(typeDTO.getIsDelete());
        //5 代表菜谱方案
        typeReqDTO.setFrom(5);
        typeReqDTO.setIsEnable(1);
        if (StringUtils.isNotEmpty(typeDTO.getTypeGuid())) {
            typeReqDTO.setParentGuid(typeDTO.getTypeGuid());
        }
        typeReqDTO.setMenuClassifyPictureType(typeDTO.getMenuClassifyPictureType());
        String typeGudId = typeService.saveTypePlan(typeReqDTO);
        saveTypeMap.put(typeDTO.getName(), typeGudId);
    }

    /**
     * 新增菜谱方案
     *
     * @param reqDTO       价格方案保存入参
     * @param parentPlanDO 原方案
     * @return 菜谱方案信息
     */
    private PricePlanDO addPricePlan(PricePlanReqDTO reqDTO, PricePlanDO parentPlanDO) {
        //新增方案，重名校验
        if (null == parentPlanDO && CollectionUtils.isEmpty(reqDTO.getItemWebRespDTOList())) {
            throw new BusinessException("方案菜品不能为空");
        }
        //修改菜谱方案名称未修改不校验
        boolean isNotUpdateName = null != parentPlanDO && reqDTO.getName().equals(parentPlanDO.getName());
        if (!isNotUpdateName && pricePlanMapper.existSameName(reqDTO)) {
            throw new BusinessException("已存在同名方案");
        }
        //根据父菜谱查询是否存在正在处理的子菜谱
        if (null != parentPlanDO && processing(parentPlanDO.getGuid())) {
            log.error("存在相同的父菜谱,父菜谱guid：{}", parentPlanDO.getGuid());
            throw new BusinessException("存在相同的父菜谱");
        }
        PricePlanDO pricePlanDO = new PricePlanDO();
        LocalDateTime now = LocalDateTime.now();
        String guid = GUIDUtils.generateGuid(GuidKeyConstant.HSI_PRICEPLAN);
        String flag = getPlanCode(guid);
        pricePlanDO.setGuid(guid)
                .setUpdateTime(now)
                .setBrandGuid(reqDTO.getBrandGuid())
                .setName(reqDTO.getName())
                .setDescription(reqDTO.getDescription())
                .setStatus(reqDTO.getStatus())
                .setItemNum(reqDTO.getItemWebRespDTOList().size())
                .setStoreNum(CollectionUtil.isNotEmpty(reqDTO.getStoreGuidList()) ? reqDTO.getStoreGuidList().size() : 0)
                .setSellTimeType(reqDTO.getSellTimeType())
                .setPushType(reqDTO.getPushType())
                .setPlanCode(flag)
        ;
        //立即生效
        if (1 == reqDTO.getPushType()) {
            pricePlanDO.setEffectiveTime(now);
            pricePlanDO.setPushDate(now);
        } else {
            //预计生效时间
            pricePlanDO.setPushDate(reqDTO.getPushDate());
            if (!ObjectUtils.isEmpty(parentPlanDO)) {
                pricePlanDO.setEffectiveTime(parentPlanDO.getEffectiveTime());
            }
            pricePlanDO.setInstantlyEffectiveTime(reqDTO.getPushDate());
            if (null != reqDTO.getPushDate()) {
                if (reqDTO.getPushDate().isAfter(now)) {
                    pricePlanDO.setStatus(PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode());
                } else {
                    pricePlanDO.setStatus(PricePlanStatusEnum.USING.getCode());
                }
            }
        }
        //商品售卖时段  特殊时段
        if (SELL_TIME_TYPE != reqDTO.getSellTimeType()) {
            pricePlanDO.setStartTime(reqDTO.getStartTime());
            pricePlanDO.setEndTime(reqDTO.getEndTime());
        }
        if (null != parentPlanDO) {
            pricePlanDO.setParentGuid(parentPlanDO.getGuid());
        }
        //如果是暂不启用，设置生效时间，推送时间为空
        if (reqDTO.getStatus() == PricePlanStatusEnum.NOT_ENABLED.getCode()) {
            pricePlanDO.setEffectiveTime(null);
            pricePlanDO.setPushDate(null);
        }
        this.save(pricePlanDO);
        return pricePlanDO;
    }

    private boolean processing(String guid) {
        int count = this.count(new LambdaQueryWrapper<PricePlanDO>()
                .eq(PricePlanDO::getParentGuid, guid)
                .eq(PricePlanDO::getIsDelete, BooleanEnum.FALSE.getCode())
                .in(PricePlanDO::getStatus, Lists.newArrayList(1, 2, 4)));
        return count > 0;
    }

    /**
     * 生成菜谱编号
     *
     * @param guid 菜谱guid
     * @return 菜谱编号
     */
    private String getPlanCode(String guid) {
        StringBuilder flag = new StringBuilder();
        if (StringUtils.isNotEmpty(guid)) {
            SecureRandom rand = new SecureRandom();
            for (int j = 0; j < 6; j++) {
                flag.append(guid.charAt(rand.nextInt(9)));
            }
        }
        return flag.toString();
    }

    /**
     * 编辑已启用方案且生效时间为预计生效 保存备份数据
     *
     * @param pricePlanDO 菜谱方案信息
     * @param reqDTO      菜谱方案保存参数
     */
    private void updateUsingPricePlan(PricePlanDO pricePlanDO, PricePlanReqDTO reqDTO) {
        List<String> storeGuidList = reqDTO.getStoreGuidList();
        //保存方案信息
        PricePlanDO backPricePlanDo = addPricePlan(reqDTO, pricePlanDO);
        // 保存分类信息
        Map<String, String> typeMap = reSavePlanType(reqDTO.getGuid(), backPricePlanDo.getGuid(), reqDTO);
        // 备份pad图片
        savePadPicture(reqDTO, storeGuidList, backPricePlanDo);
        //保存商品信息
        copyPlanItems(pricePlanDO, reqDTO, backPricePlanDo, typeMap);
        //保存门店信息
        PricePlanStoreReqDTO planStoreReqDTO = new PricePlanStoreReqDTO();
        planStoreReqDTO.setPlanGuid(backPricePlanDo.getGuid());
        planStoreReqDTO.setBrandGuid(reqDTO.getBrandGuid());
        planStoreReqDTO.setStoreGuidList(storeGuidList);

        // 如果方案修改为暂不启用清空所有绑定门店，并更新门店数
        if (Objects.equals(PricePlanStatusEnum.NOT_ENABLED.getCode(), reqDTO.getStatus())) {
            planStoreReqDTO.setStoreGuidList(new ArrayList<>());
        }
        pricePlanStoreService.bingPlanStore(planStoreReqDTO);
        pushPlan("修改菜谱", backPricePlanDo, storeGuidList);
    }

    /**
     * 重新保存菜谱分类
     */
    private Map<String, String> reSavePlanType(String oldPlanGuid, String newPlanGuid, PricePlanReqDTO reqDTO) {
        // 保存分类信息
        Map<String, String> typeMap = new HashMap<>();
        List<TypePricePlanReqDTO> typeList = reqDTO.getTypeList();
        // 查询原菜谱分类列表
        List<TypeDO> typeDOList = typeService.list(new LambdaQueryWrapper<TypeDO>()
                .eq(TypeDO::getPricePlanGuid, oldPlanGuid)
        );
        if (CollectionUtils.isEmpty(typeList)) {
            // 未修改商品分类信息（前端未传值），手动复制
            typeDOList.forEach(t -> addPricePlanType(newPlanGuid, typeMap
                    , MapStructUtils.INSTANCE.typeDO2TypePricePlanReqDTO(t)));
            return typeMap;
        }
        if (Boolean.TRUE.equals(reqDTO.getBatchEdit())) {
            // 批量编辑菜谱
            reSavePlanTypeForBatchEdit(typeMap, typeList, typeDOList, newPlanGuid);
        } else {
            //排除删除的分类
            typeList = typeList.stream().filter(e -> e.getIsDelete() == 0).collect(Collectors.toList());
            for (TypePricePlanReqDTO typeDTO : typeList) {
                addPricePlanType(newPlanGuid, typeMap, typeDTO);
            }
        }
        return typeMap;
    }


    /**
     * 批量编辑菜谱 - 即将生效设置分类
     */
    private void reSavePlanTypeForBatchEdit(Map<String, String> typeMap, List<TypePricePlanReqDTO> typeList,
                                            List<TypeDO> typeDOList, String newPlanGuid) {
        Map<String, TypePricePlanReqDTO> typeReqMap = typeList.stream()
                .collect(Collectors.toMap(TypePricePlanReqDTO::getName, Function.identity(), (key1, key2) -> key1));
        for (TypeDO typeDO : typeDOList) {
            TypePricePlanReqDTO typePricePlanReqDTO = typeReqMap.remove(typeDO.getName());
            if (Objects.nonNull(typePricePlanReqDTO)) {
                if (Objects.nonNull(typePricePlanReqDTO.getSort())) {
                    typeDO.setSort(typePricePlanReqDTO.getSort());
                }
                if (StringUtils.isNotEmpty(typePricePlanReqDTO.getUpdateAfterName())) {
                    typeDO.setName(typePricePlanReqDTO.getUpdateAfterName());
                }
            }
            addPricePlanType(newPlanGuid, typeMap
                    , MapStructUtils.INSTANCE.typeDO2TypePricePlanReqDTO(typeDO));
        }
        for (TypePricePlanReqDTO pricePlanReqDTO : typeReqMap.values()) {
            if (StringUtils.isEmpty(pricePlanReqDTO.getUpdateAfterName())) {
                // UpdateAfterName为空，表示不是修改分类名称, 修改分类名称的不新增
                addPricePlanType(newPlanGuid, typeMap, pricePlanReqDTO);
            }
        }
        // 查询当前菜谱下所有的分类, 重新排序
        List<TypeDO> newTypeDbList = typeService.list(new LambdaQueryWrapper<TypeDO>()
                .eq(TypeDO::getPricePlanGuid, newPlanGuid)
                .eq(TypeDO::getIsDelete, 0));
        if (CollectionUtils.isEmpty(newTypeDbList)) {
            return;
        }
        // 重新排序
        newTypeDbList = newTypeDbList.stream()
                .sorted(Comparator.comparing(TypeDO::getSort)
                        .thenComparing(Comparator.comparing(TypeDO::getId).reversed()))
                .collect(Collectors.toList());
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < newTypeDbList.size(); i++) {
            TypeDO typeDO = newTypeDbList.get(i);
            typeDO.setSort(i + 1);
            typeDO.setGmtModified(now);
        }
        typeService.updateBatchById(newTypeDbList);
    }


    /**
     * 备份pad图片
     *
     * @param reqDTO          原菜谱信息
     * @param storeGuidList   菜谱要绑定的门店
     * @param backPricePlanDo 备份的菜谱信息
     */
    private void savePadPicture(PricePlanReqDTO reqDTO, List<String> storeGuidList, PricePlanDO backPricePlanDo) {
        String originalPlanGuid = reqDTO.getGuid();
        String newPlanGuid = backPricePlanDo.getGuid();

        // 原菜谱的商品
        List<PricePlanItemDO> planItemDOList = planItemService.list(new LambdaQueryWrapper<PricePlanItemDO>()
                .eq(PricePlanItemDO::getPlanGuid, originalPlanGuid)
                .ne(PricePlanItemDO::getIsSoldOut, ItemStateEnum.IMMEDIATELY_DELETE.getCode())
        );
        if (CollectionUtils.isEmpty(planItemDOList)) {
            log.warn("方案下商品为空 planGuid={}", originalPlanGuid);
            return;
        }
        List<String> itemGuidList = planItemDOList.stream()
                .map(PricePlanItemDO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());

        // 原菜谱的门店
        List<PricePlanStoreDO> planStoreDOList = pricePlanStoreService.list(new LambdaQueryWrapper<PricePlanStoreDO>()
                .eq(PricePlanStoreDO::getPlanGuid, originalPlanGuid)
        );
        if (CollectionUtils.isEmpty(planStoreDOList)) {
            log.warn("原菜谱未绑定门店 originalPlanGuid={}", originalPlanGuid);
            return;
        }
        List<String> originalStoreGuidList = planStoreDOList.stream()
                .map(PricePlanStoreDO::getStoreGuid)
                .collect(Collectors.toList());
        originalStoreGuidList.retainAll(storeGuidList);
        if (CollectionUtils.isEmpty(originalStoreGuidList)) {
            log.warn("原门店与即将绑定门店的交集为空");
            return;
        }

        List<PadPictureDO> padPictureDOList = itemPadPictureService.list(new LambdaQueryWrapper<PadPictureDO>()
                .in(PadPictureDO::getItemGuid, itemGuidList)
                .in(PadPictureDO::getStoreGuid, originalStoreGuidList)
                .eq(PadPictureDO::getPlanGuid, originalPlanGuid)
        );
        if (CollectionUtils.isEmpty(padPictureDOList)) {
            log.warn("原pad图片查询为空 itemGuidList={},originalPlanGuid={},originalStoreGuidList={}", itemGuidList,
                    originalPlanGuid, originalStoreGuidList);
            return;
        }
        Map<String, List<PadPictureDO>> padPictureMap = padPictureDOList.stream()
                .collect(Collectors.groupingBy(PadPictureDO::getStoreGuid));
        List<PadPictureDO> backupPadPictureList = new ArrayList<>();

        originalStoreGuidList.forEach(storeGuid -> {
            List<PadPictureDO> pictureDOList = padPictureMap.get(storeGuid);
            pictureDOList.forEach(padPicture -> {
                padPicture.setId(null);
                padPicture.setGmtCreate(null);
                padPicture.setGmtModified(null);
                padPicture.setGuid(GUIDUtils.generateGuid(GuidKeyConstant.HSI_PAD_PICTURE));
                padPicture.setPlanGuid(newPlanGuid);
                backupPadPictureList.add(padPicture);
            });
        });
        if (CollectionUtils.isEmpty(backupPadPictureList)) {
            log.warn("备份保存的pad图片为空");
            return;
        }
        itemPadPictureService.saveBatch(backupPadPictureList);
    }

    /**
     * 复制商品信息
     *
     * @param pricePlanDO     原菜谱方案
     * @param reqDTO          菜谱方案保存参数
     * @param backPricePlanDo 备份菜谱方案
     * @param typeMap         新增的分类信息
     */
    private void copyPlanItems(PricePlanDO pricePlanDO, PricePlanReqDTO reqDTO, PricePlanDO backPricePlanDo, Map<String, String> typeMap) {
        List<ItemWebRespDTO> itemWebRespDTOS = reqDTO.getItemWebRespDTOList();
        // 即将下架的商品Guid
        List<String> toDelete = itemWebRespDTOS.stream()
                .filter(i -> Objects.equals(ItemStateEnum.IS_ABOUT_TO_DELETE.getCode(), i.getIsSoldOut()))
                .map(ItemWebRespDTO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(toDelete)) {
            //即将下架商品不保存备份 直接排除
            itemWebRespDTOS.removeIf(itemWebRespDTO -> toDelete.contains(itemWebRespDTO.getItemGuid()));
        }
        PricePlanItemAddReqDTO addReqDTO = new PricePlanItemAddReqDTO();
        addReqDTO.setPlanGuid(backPricePlanDo.getGuid());
        addReqDTO.setBrandGuid(reqDTO.getBrandGuid());
        addReqDTO.setItemWebRespDTOList(itemWebRespDTOS);
        //未修改商品信息前端未传值 手动复制
        List<String> itemGuids = itemWebRespDTOS.stream().map(ItemWebRespDTO::getItemGuid).collect(Collectors.toList());

        //查询需要复制的信息
        List<PricePlanItemDO> planItemDOS = planItemService.list(new LambdaQueryWrapper<PricePlanItemDO>()
                .eq(PricePlanItemDO::getPlanGuid, pricePlanDO.getGuid())
                .notIn(CollectionUtils.isNotEmpty(toDelete), PricePlanItemDO::getItemGuid, toDelete)
                .notIn(CollectionUtils.isNotEmpty(itemGuids), PricePlanItemDO::getItemGuid, itemGuids));
        copyItems(backPricePlanDo, planItemDOS);

        planItemService.saveUpdatePlanItem(addReqDTO, typeMap, true);
        //查询商品数量
        List<PricePlanItemDO> itemDOS = planItemService.list(new LambdaQueryWrapper<PricePlanItemDO>()
                .select(PricePlanItemDO::getItemGuid)
                .eq(PricePlanItemDO::getPlanGuid, backPricePlanDo.getGuid())
                .groupBy(PricePlanItemDO::getItemGuid));
        //更新商品数
        pricePlanMapper.updateItemNum(backPricePlanDo.getGuid(), itemDOS.size(), 3);
    }

    /**
     * 复制菜谱方案商品信息
     *
     * @param backPricePlanDo 菜谱方案
     * @param planItemDOS     商品信息
     */
    private void copyItems(PricePlanDO backPricePlanDo, List<PricePlanItemDO> planItemDOS) {
        if (CollectionUtils.isEmpty(planItemDOS)) {
            return;
        }
        List<PricePlanItemDO> backItemDOs = new ArrayList<>();
        PricePlanItemDO backItemDO;
        Set<String> typeGuids = planItemDOS.stream().map(PricePlanItemDO::getTypeGuid).collect(Collectors.toSet());
        List<TypeDO> typeDOS = typeService.list(new LambdaQueryWrapper<TypeDO>()
                .in(TypeDO::getParentGuid, typeGuids)
                .eq(TypeDO::getPricePlanGuid, backPricePlanDo.getGuid()));
        for (PricePlanItemDO itemDO : planItemDOS) {
            backItemDO = new PricePlanItemDO();
            BeanUtil.copyProperties(itemDO, backItemDO, "id", "guid", "typeGuid", "planGuid");
            backItemDO.setPlanGuid(backPricePlanDo.getGuid());
            backItemDO.setGuid(GUIDUtils.generateGuid(GuidKeyConstant.HSI_PRICEPLAN_ITEM));
            //查询分类信息
            Optional<TypeDO> optional = typeDOS.stream().filter(typeDO -> itemDO.getTypeGuid().equals(typeDO.getParentGuid())).findFirst();
            if (optional.isPresent()) {
                backItemDO.setTypeGuid(optional.get().getGuid());
                backItemDOs.add(backItemDO);
            }
        }
        planItemService.saveBatch(backItemDOs);
    }

    /**
     * 通知被删除的门店，菜谱方案把你这个店除名了
     *
     * @param updateBeforePricePlanStoreList 修改前的门店
     * @param storeGuidList                  修改后的门店
     */
    private void planStoreRemovePush(List<PricePlanStoreDO> updateBeforePricePlanStoreList, List<String> storeGuidList) {
        if (CollectionUtils.isNotEmpty(updateBeforePricePlanStoreList)) {
            // 通知之前的门店，正在进行的菜谱门店变了
            if (CollectionUtils.isEmpty(storeGuidList)) {
                List<String> updateBeforeStoreGuidList = updateBeforePricePlanStoreList
                        .stream()
                        .map(PricePlanStoreDO::getStoreGuid)
                        .collect(Collectors.toList());
                // 推送消息给安卓端
                log.info("菜谱提示异常日志跟踪11}");
                eventPushHelper.pushMsgToAndriod(updateBeforeStoreGuidList);
                return;
            }

            List<String> delStoreGuidList = new ArrayList<>();
            for (PricePlanStoreDO beforeStore : updateBeforePricePlanStoreList) {
                String storeGuid = beforeStore.getStoreGuid();
                boolean isDel = true;
                for (String afterStoreGuid : storeGuidList) {
                    if (storeGuid.equals(afterStoreGuid)) {
                        isDel = false;
                        break;
                    }
                }
                if (isDel) {
                    delStoreGuidList.add(storeGuid);
                }
            }

            if (CollectionUtils.isNotEmpty(delStoreGuidList)) {
                // 推送消息给安卓端
                log.info("菜谱提示异常日志跟踪12}");
                eventPushHelper.pushMsgToAndriod(delStoreGuidList);
            }
        }
    }

    /**
     * 定时执行增加
     *
     * @param plan 方案信息
     * @param type 类型 1增加任务 2立即执行
     */
    private void planTimedExecution(PricePlanDO plan, Integer type) {
        long timerDuration = Duration.between(LocalDateTime.now(), plan.getPushDate()).getSeconds();
        log.info("定时执行时间：" + timerDuration + "秒");

        // 定时推送，设置Redis过期时间，过期时由监听器触发推送任务(RedisKeyExpirationListener)
        SyncPricePlanMessageDTO messageDTO = new SyncPricePlanMessageDTO();
        messageDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid())
                .setBrandGuid(plan.getBrandGuid())
                .setPricePlanGuid(plan.getGuid());

        String messageJson = JacksonUtils.writeValueAsString(messageDTO);
        String cacheKey = KEY_EXECUTION + messageJson;
        // 先删除在增加
        try {
            cacheService.removeCacheMap(cacheKey);
        } catch (Exception e) {
            log.error("", e);
        }
        switch (type) {
            case 1:
                log.info("方案定时执行cacheKey：{}", cacheKey);
                cacheService.setCacheMap(cacheKey, messageDTO, timerDuration);
                log.info("实际定时执行时间：{}", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(plan.getPushDate()));
                break;
            case 2:
                timeExecution(plan.getGuid());
                break;
            default:
                log.error("错误的参数类型");
        }
    }

    /**
     * 定时执行的内容
     *
     * @param planGuid 方案guid
     */
    public void timeExecution(String planGuid) {
        PricePlanDO planDO = new PricePlanDO();
        planDO.setGuid(planGuid);
        planDO.setStatus(PricePlanStatusEnum.USING.getCode());
        planDO.setEffectiveTime(LocalDateTime.now());
        planDO.setInstantlyEffectiveTime(null);
        pricePlanMapper.updateById(planDO);

        deleteToRackItem(planGuid);

        PricePlanDO pricePlanDO = this.getById(planGuid);

        // 如果方案的父id不为空，说明是备份的方案，生效的同时将父方案删除
        if (!ObjectUtils.isEmpty(pricePlanDO.getParentGuid())) {
            log.info("定时执行 删除备份前的方案 parentGuid={}", pricePlanDO.getParentGuid());
//            pricePlanMapper.deleteById(pricePlanDO.getParentGuid());
            // 删除方案会影响到消费消息，所以改状态不删除
            pricePlanMapper.updateStatus(pricePlanDO.getParentGuid(), PricePlanStatusEnum.LASTING_DISABLE.getCode());
        }

        //菜谱生效 解绑冲突了门店及菜谱信息
        List<String> storeGuidList = planStoreMapper.getPlanStoreGuidList(planGuid, null);
        this.unBindStoreAndPlan(storeGuidList, pricePlanDO.getStartTime(), pricePlanDO.getEndTime()
                , pricePlanDO.getSellTimeType(), pricePlanDO.getGuid());
        log.info("定时执行完毕,方案{}", planGuid);
    }

    /**
     * 解绑菜谱冲突的菜谱方案关联
     *
     * @param storeGuids       门店Guid集合
     * @param startCompareTime 开始时间
     * @param endCompareTime   结束时间
     * @param sellTimeType     售卖类型 0 默认（全时段） 1特殊时段
     * @param filterPlanGuid   当前方案Guid
     */
    @Override
    public void unBindStoreAndPlan(List<String> storeGuids, LocalTime startCompareTime, LocalTime endCompareTime
            , Integer sellTimeType, String filterPlanGuid) {
        storeGuids.forEach(
                storeGuid -> {
                    List<PricePlanStoreDO> planStoreDOList = pricePlanStoreService.list(new LambdaQueryWrapper<PricePlanStoreDO>()
                            .eq(PricePlanStoreDO::getStoreGuid, storeGuid)
                            .eq(PricePlanStoreDO::getIsDelete, 0)
                            .ne(StringUtils.isNotEmpty(filterPlanGuid), PricePlanStoreDO::getPlanGuid, filterPlanGuid)
                    );
                    if (CollectionUtils.isEmpty(planStoreDOList)) {
                        log.warn("门店绑定为空");
                        return;
                    }
                    List<String> planGuidList = planStoreDOList.stream()
                            .map(PricePlanStoreDO::getPlanGuid)
                            .distinct()
                            .collect(Collectors.toList());
                    List<PricePlanDO> pricePlanDOList = pricePlanMapper.selectList(new LambdaQueryWrapper<PricePlanDO>()
                                    .in(CollectionUtils.isNotEmpty(planGuidList), PricePlanDO::getGuid, planGuidList)
                                    .eq(PricePlanDO::getStatus, PricePlanStatusEnum.USING.getCode())
                                    .eq(PricePlanDO::getIsDelete, BooleanEnum.FALSE.getCode())
//                            .isNull(PricePlanDO::getParentGuid)
                    );
                    for (PricePlanDO plan : pricePlanDOList) {
                        if (0 == plan.getSellTimeType() && 0 == sellTimeType) {
                            log.info("门店：{}已绑定全时段方案{}，删除绑定关系", storeGuid, plan.getGuid());
                            untieTheStore(storeGuid, plan);
                        }
                        if (1 == plan.getSellTimeType() && 1 == sellTimeType) {
                            // 特殊时段重叠的要解绑
                            LocalTime startTime = plan.getStartTime();
                            LocalTime endTime = plan.getEndTime();
                            boolean overlap = DateTimeUtils.checkOverlap(startCompareTime, endCompareTime, startTime, endTime);
                            if (overlap) {
                                log.info("门店：{}特殊时段重叠，删除方案{}绑定关系", storeGuid, plan.getGuid());
                                untieTheStore(storeGuid, plan);
                                return;
                            }
                        }
                    }
                }
        );
    }

    private void untieTheStore(String storeGuid, PricePlanDO plan) {
        pricePlanStoreService.remove(new LambdaQueryWrapper<PricePlanStoreDO>()
                .eq(PricePlanStoreDO::getStoreGuid, storeGuid)
                .eq(PricePlanStoreDO::getPlanGuid, plan.getGuid())
        );
        //更新方案门店数
        pricePlanMapper.updateStoreNum(plan.getGuid(), Math.max(plan.getStoreNum() - 1, 0), 3);
    }

    /**
     * 验证原方案和前端新改的方案 推送类型和时段是否发生了变化
     *
     * @param pricePlanDO 数据库
     * @param reqDTO      web请求来的
     * @return true发生了变化，false没有变化
     */
    private boolean pushTimeChange(PricePlanDO pricePlanDO, PricePlanReqDTO reqDTO) {
        // 售卖类型-0 默认（全时段）-1特殊时段
        Integer sellTimeType = pricePlanDO.getSellTimeType();
        // 推送方式-1立即推送 2按时间
        Integer pushType = pricePlanDO.getPushType();
        LocalDateTime instantlyEffectiveTime = pricePlanDO.getInstantlyEffectiveTime();
        LocalTime startTime = pricePlanDO.getStartTime();
        LocalTime endTime = pricePlanDO.getEndTime();

        if (sellTimeType == 0) {
            // 原数据是立即推送
            if (pushType == 1) {
                if (reqDTO.getPushType() == 2) {
                    // 修改成了预定生效
                    return true;
                }

                // 原数据是预定生效
            } else {
                if (reqDTO.getPushType() == 1) {
                    // 改成了立即生效
                    return true;
                } else {
                    // 预定生效时间发生了变化
                    if (Objects.isNull(instantlyEffectiveTime) || !instantlyEffectiveTime.equals(reqDTO.getPushDate())) {
                        return true;
                    }
                }
            }
        } else {
            // 原数据是立即推送
            if (pushType == 1) {
                if (reqDTO.getPushType() == 1) {
                    int startTimeInt = Integer.parseInt(startTime.format(DateTimeFormatter.ofPattern("HH:mm"))
                            .replace(":", ""));
                    int endTimeInt = Integer.parseInt(endTime.format(DateTimeFormatter.ofPattern("HH:mm"))
                            .replace(":", ""));

                    // 时段变了
                    int startTimeWebInt = Integer.parseInt(reqDTO.getStartTime().format(DateTimeFormatter
                            .ofPattern("HH:mm")).replace(":", ""));
                    int endTimeWebInt = Integer.parseInt(reqDTO.getEndTime().format(DateTimeFormatter
                            .ofPattern("HH:mm")).replace(":", ""));
                    if (startTimeInt != startTimeWebInt || endTimeInt != endTimeWebInt) {
                        return true;
                    }
                } else {
                    // 变成了预定生效
                    return true;
                }

                // 原数据是预定生效
            } else {
                if (reqDTO.getPushType() == 1) {
                    // 改成了立即生效
                    return true;
                } else {
                    // 预定生效时间发生了变化
                    if (Objects.isNull(instantlyEffectiveTime) || !instantlyEffectiveTime.equals(reqDTO.getPushDate())) {
                        return true;
                    }

                    int startTimeInt = Integer.parseInt(startTime.format(DateTimeFormatter.ofPattern("HH:mm"))
                            .replace(":", ""));
                    int endTimeInt = Integer.parseInt(endTime.format(DateTimeFormatter.ofPattern("HH:mm"))
                            .replace(":", ""));

                    // 时段变了
                    int startTimeWebInt = Integer.parseInt(reqDTO.getStartTime().format(DateTimeFormatter
                            .ofPattern("HH:mm")).replace(":", ""));
                    int endTimeWebInt = Integer.parseInt(reqDTO.getEndTime().format(DateTimeFormatter
                            .ofPattern("HH:mm")).replace(":", ""));
                    if (startTimeInt != startTimeWebInt || endTimeInt != endTimeWebInt) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 判断价格方案中的菜品和新提交的菜品，是否发生了变化
     *
     * @param updateBeforePlanItemDOList 数据库中
     * @param updateAfterItemList        前端提交
     * @return true 变化了；false 没变
     */
    private boolean pricePlanInfoChange(List<PricePlanItemDO> updateBeforePlanItemDOList, List<PricePlanItemDO> updateAfterItemList) {
        // 前端没有提交来商品信息，则不校验
        if (CollectionUtils.isEmpty(updateAfterItemList)) {
            return false;
        }

        if (updateBeforePlanItemDOList.size() != updateAfterItemList.size()) {
            return true;
        }

        Map<String, PricePlanItemDO> webSkuMap = updateAfterItemList.stream()
                .collect(Collectors.toMap(PricePlanItemDO::getSkuGuid, item -> item));
        for (PricePlanItemDO databaseDataItem : updateBeforePlanItemDOList) {
            String skuGuid = databaseDataItem.getSkuGuid();
            PricePlanItemDO webItem = webSkuMap.get(skuGuid);
            // sku 不一致，则发生了变化
            if (Objects.isNull(webItem)) {
                return true;
            }
            // 商品名称变化
            String planItemName = databaseDataItem.getPlanItemName();
            if (!planItemName.equals(webItem.getPlanItemName())) {
                return true;
            }
            // 商品图片变化
            String pictureUrl = databaseDataItem.getPictureUrl();
            pictureUrl = Optional.ofNullable(pictureUrl).orElse("");
            String webUrl = Optional.ofNullable(webItem.getPictureUrl()).orElse("");
            if (!pictureUrl.equals(webUrl)) {
                return true;
            }
            // 销售价变化
            BigDecimal salePrice = databaseDataItem.getSalePrice();
            if (salePrice.compareTo(webItem.getSalePrice()) != 0) {
                return true;
            }
            // 会员价变化
            BigDecimal memberPrice = databaseDataItem.getMemberPrice();
            memberPrice = Optional.ofNullable(memberPrice).orElse(BigDecimal.ZERO);
            BigDecimal webMemberPrice = Optional.ofNullable(webItem.getMemberPrice()).orElse(BigDecimal.ZERO);
            if (memberPrice.compareTo(webMemberPrice) != 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 验证修改全时段菜谱时，是否正在售卖特殊时段菜谱，
     * 如果正在售卖特殊菜谱，就不推送修改的全时段菜谱；
     * 如果没有正在售卖特殊菜谱，或修改是的特殊菜谱，则正常走逻辑
     * <p>
     * 反之同样适用
     *
     * @param storeGuidList storeGuidList
     * @param pricePlanDO   pricePlanDO
     * @return false 不推送，true正常推送
     */
    private boolean validateAllTimePlanPush(List<String> storeGuidList, PricePlanDO pricePlanDO) {
        LocalDateTime nowDateTime = LocalDateTime.now();

        // 如果修改的是全时段菜谱
        if (pricePlanDO.getSellTimeType() == 0) {
            if (CollectionUtils.isEmpty(storeGuidList)) {
                return true;
            }
            List<PricePlanNowDTO> planNowList = planStoreMapper.findPlanNowStoreGuidList(nowDateTime, storeGuidList);
            log.info("validateAllTimePlanPush：{}", JacksonUtils.writeValueAsString(planNowList));
            PricePlanNowDTO pricePlanNowTime = null;
            for (PricePlanNowDTO planNow : planNowList) {
                Integer sellTimeType = planNow.getSellTimeType();
                if (sellTimeType == 1) {
                    LocalDateTime startTime = planNow.getStartTime();
                    LocalDateTime endTime = planNow.getEndTime();
                    int startTimeInt = Integer.parseInt(startTime.format(DateTimeFormatter.ofPattern("HH:mm"))
                            .replace(":", ""));
                    int endTimeInt = Integer.parseInt(endTime.format(DateTimeFormatter.ofPattern("HH:mm"))
                            .replace(":", ""));
                    LocalTime nowTime = LocalTime.now();
                    int nowTimeInt = Integer.parseInt(nowTime.format(DateTimeFormatter.ofPattern("HH:mm"))
                            .replace(":", ""));
                    log.info("有正在售卖的特殊菜谱now:{}, sta:{},end:{}", nowTimeInt, startTimeInt, endTimeInt);
                    if (nowTimeInt >= startTimeInt && nowTimeInt <= endTimeInt) {
                        log.info("有正在售卖的特殊菜谱 ...");
                        // 正在售卖特殊时段菜品
                        pricePlanNowTime = planNow;
                        break;
                    }
                }
            }

            // 正在售卖特殊，所以修改的全时段菜谱信息不推送
            return Objects.isNull(pricePlanNowTime);

            // 如果修改的是特殊时段菜谱
        } else {
            LocalTime startTime = pricePlanDO.getStartTime();
            LocalTime endTime = pricePlanDO.getEndTime();
            int startTimeInt = Integer.parseInt(startTime.format(DateTimeFormatter.ofPattern("HH:mm"))
                    .replace(":", ""));
            int endTimeInt = Integer.parseInt(endTime.format(DateTimeFormatter.ofPattern("HH:mm"))
                    .replace(":", ""));
            LocalTime nowTime = LocalTime.now();
            int nowTimeInt = Integer.parseInt(nowTime.format(DateTimeFormatter.ofPattern("HH:mm"))
                    .replace(":", ""));
            log.info("有正在售卖的特殊菜谱now:{}, sta:{},end:{}", nowTimeInt, startTimeInt, endTimeInt);
            if (nowTimeInt >= startTimeInt && nowTimeInt <= endTimeInt) {
                log.info("有正在售卖的特殊菜谱 ...");
                // 正在售卖特殊时段菜品
                return true;
            } else {
                return false;
            }
        }
    }

    /**
     * 当菜谱方案停用后，移除推送
     *
     * @param pricePlanDO pricePlanDO
     */
    private void planPriceDisabledCache(PricePlanDO pricePlanDO) {
        SyncPricePlanMessageDTO messageDTO = new SyncPricePlanMessageDTO();
        messageDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid())
                .setBrandGuid(pricePlanDO.getBrandGuid())
                .setPricePlanGuid(pricePlanDO.getGuid());

        // 删除定时执行的redis数据
        String messageJson = JacksonUtils.writeValueAsString(messageDTO);
        String cacheKey = KEY_EXECUTION + messageJson;
        try {
            cacheService.removeCacheMap(cacheKey);
        } catch (Exception e) {
            log.error("", e);
        }

        messageDTO.setMessageType(PLAN_PUSH_ADD);
        String json = JacksonUtils.writeValueAsString(messageDTO);
        try {
            cacheService.removeCacheMap(KEY_PREFIX + json);
        } catch (Exception e) {
            log.error("", e);
        }
        messageDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid())
                .setBrandGuid(pricePlanDO.getBrandGuid())
                .setPricePlanGuid(pricePlanDO.getGuid())
                .setMessageType(PLAN_PUSH_DELETE);
        json = JacksonUtils.writeValueAsString(messageDTO);
        try {
            cacheService.removeCacheMap(KEY_PREFIX + json);
        } catch (Exception e) {
            log.error("", e);
        }

    }

    /**
     * 价格方案/菜谱推送。
     * 如果是立即推送，则直接push到MQ，然后由MQ消费此消息，把消息转化放入EMQ，由EMQ推送到终端；
     * 如果是定时推送，则放入REDIS中，设置REDIS过期时间戳，定时取出消息，到达指定时间后，处理方式同立即推送。
     *
     * @param sourceName    触发推送的业务名称
     * @param pricePlanDO   pricePlanDO
     * @param storeGuidList storeGuidList
     */
    private void pushPlan(String sourceName, PricePlanDO pricePlanDO, List<String> storeGuidList) {
        // 门店为空，就不用判断模式
        if (CollectionUtils.isNotEmpty(storeGuidList)) {
            BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuidList.get(0));
            log.info("pushPlan 查询菜谱模式数据：{}", JacksonUtils.writeValueAsString(brandDTO));
            if (brandDTO.getSalesModel() == SalesModelEnum.NORMAL_MODE.getCode()) {
                log.info("普通模式不推送菜谱方案");
                return;
            }
        }

        String planGuid = pricePlanDO.getGuid();
        // 推送类型：1-立即推送，2-按时间推送
        Integer pushType = pricePlanDO.getPushType();
        // 售卖类型-0 默认（全时段）-1特殊时段
        Integer sellTimeType = pricePlanDO.getSellTimeType();
        LocalDateTime pushDateTime = pricePlanDO.getPushDate();

        PricePlanPushReqDTO reqDTO = new PricePlanPushReqDTO();
        reqDTO.setPlanGuid(planGuid);
        reqDTO.setPushType(pushType);
        reqDTO.setPushDate(pushDateTime);
        reqDTO.setSellTimeType(sellTimeType);

        log.info("价格方案推送入参：{},{}", sourceName, JSON.toJSONString(reqDTO));
        // 发送推送消息
        this.planStartNoti(pricePlanDO, true);

        // 定时执行逻辑后移，避免被删除
        log.info("按时间推送，调用定时执行逻辑:{}", pricePlanDO.getGuid());
        planTimedExecution(pricePlanDO, 1);
    }

    @Override
    public com.holderzone.framework.util.Page<PricePlanRespDTO> planList(PricePlanPageReqDTO reqDTO) {
        Page<PricePlanRespDTO> page = new Page<>(reqDTO.getCurrentPage(), reqDTO.getPageSize());
        //查询修改备份的菜谱方案 列表中需排除
        List<PricePlanDO> backPlanDOS = this.list(new LambdaQueryWrapper<PricePlanDO>()
                .isNotNull(PricePlanDO::getParentGuid));
        if (CollectionUtils.isNotEmpty(backPlanDOS)) {
            reqDTO.setFilterGuids(backPlanDOS.stream().map(PricePlanDO::getParentGuid).collect(Collectors.toList()));
        }
        //判断是否根据门店模糊查询
        if (StringUtils.isNotBlank(reqDTO.getStoreName())) {
            reqDTO.setIncludeGuids(listPlanGuidByStoreName(reqDTO.getBrandGuid(), reqDTO.getStoreName()));
            if (CollectionUtil.isEmpty(reqDTO.getIncludeGuids())) {
                page.setRecords(Lists.newArrayList());
                return PageUtils.convert(page);
            }
        }
        List<PricePlanRespDTO> list = pricePlanMapper.planList(reqDTO, page);
        //状态名称赋值
        if (CollectionUtils.isNotEmpty(list)) {
            //添加门店名称信息
            Map<String, String> storeMap = addStoreInfo(list);
            list.forEach(p -> {
                p.setStatusName(PricePlanStatusEnum.getDesc(p.getStatus()));
                List<PricePlanStoreBaseDTO> storeList = p.getStoreList();
                if (CollectionUtil.isNotEmpty(storeList) && ObjectUtil.isNotNull(storeMap)) {
                    storeList.forEach(store -> store.setStoreName(storeMap.get(store.getStoreGuid())));
                }
            });
            page.setRecords(list);
        } else {
            page.setRecords(Lists.newArrayList());
        }
        return PageUtils.convert(page);
    }

    public List<String> listPlanGuidByStoreName(String brandGuid, String storeName) {
        //先根据门店名称模糊查询出门店列表
        QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setStoreName(storeName);
        queryStoreDTO.setBrandGuidList(Collections.singletonList(brandGuid));
        List<StoreDTO> storeList = organizationService.listStoreByCondition(queryStoreDTO);
        if (CollUtil.isEmpty(storeList)) {
            return Lists.newArrayList();
        }
        List<String> listStoreGuid = storeList.stream().map(StoreDTO::getGuid).collect(Collectors.toList());
        //根据门店guid去查询对应的菜谱绑定门店
        List<PricePlanStoreDO> planStoreList = planStoreMapper.selectList(new LambdaQueryWrapper<PricePlanStoreDO>()
                .eq(PricePlanStoreDO::getBrandGuid, brandGuid)
                .in(PricePlanStoreDO::getStoreGuid, listStoreGuid)
                .eq(PricePlanStoreDO::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (CollUtil.isEmpty(planStoreList)) {
            return Lists.newArrayList();
        }
        return planStoreList.stream().map(PricePlanStoreDO::getPlanGuid).collect(Collectors.toList());
    }

    private Map<String, String> addStoreInfo(List<PricePlanRespDTO> list) {
        Set<String> storeGuidSet = new HashSet<>();
        List<List<PricePlanStoreBaseDTO>> collect = list.stream().map(PricePlanRespDTO::getStoreList).collect(Collectors.toList());
        for (List<PricePlanStoreBaseDTO> storeList : collect) {
            if (CollectionUtil.isNotEmpty(storeList)) {
                storeList.forEach(obj -> storeGuidSet.add(obj.getStoreGuid()));
            }
        }

        Map<String, String> storeMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(storeGuidSet)) {
            List<StoreDTO> storeList = organizationService.queryStoreByIdList(CollectionUtil.newArrayList(storeGuidSet));
            storeMap = storeList.stream().collect(Collectors.toMap(StoreDTO::getGuid, StoreDTO::getName));
        }
        return storeMap;
    }

    @Override
    public PricePlanRespDTO getPlan(String planGuid) {
        //基础信息
        PricePlanDO planDO = this.getById(planGuid);
        PricePlanRespDTO respDTO = new PricePlanRespDTO();
        BeanUtil.copyProperties(planDO, respDTO);
        /*log.info("方案:{} 状态为:{}", planGuid, respDTO.getStatus());
        if (Objects.equals(PricePlanStatusEnum.USING.getCode(), respDTO.getStatus()) ||
                Objects.equals(PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode(), respDTO.getStatus())) {
            respDTO.setStatus(PricePlanStatusEnum.USING.getCode());
            respDTO.setStatusName(PricePlanStatusEnum.USING.getDesc());
        } else {
            respDTO.setStatus(PricePlanStatusEnum.NOT_ENABLED.getCode());
            respDTO.setStatusName(PricePlanStatusEnum.NOT_ENABLED.getDesc());
        }*/
        return respDTO;
    }

    /**
     * 价格方案推送 日志记录
     *
     * @param reqDTO PricePlanPushReqDTO
     * @return 成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean pushPlanRecord(PricePlanPushReqDTO reqDTO) {

        // 删除原有的推送记录
//        pushRecordService.deletePushRecords(reqDTO.getPlanGuid());
        // 保存推送记录
        List<PricePlanPushRecordDO> recordDOList = pushRecordService.getPushRecords(reqDTO.getPlanGuid());
        recordDOList.forEach(r ->
                r.setGuid(GUIDUtils.generateGuid(GuidKeyConstant.HSI_PRICEPLAN_PUSH_RECORD))
                        .setPushDate(reqDTO.getPushDate())
                        .setPushStatus(PricePlanPushStatusEnum.NOT_PUSH.getCode()));

        return pushRecordService.saveBatch(recordDOList);
    }

    /**
     * 总策略：商户后台创建特殊时段方案，触发开始点通知，
     * 开始点通知执行时，
     * 触发结束点通知，结束点通知执行时，再次出发开始点通知
     * <p>
     * 特殊时段价格方案，开始时间点到后，触发通知
     *
     * @param plan plan
     */
    @Override
    public void planStartNoti(PricePlanDO plan, boolean first) {
        SyncPricePlanMessageDTO messageDTO = new SyncPricePlanMessageDTO();
        long duration = getDuration(plan, first, messageDTO);
        log.warn("异常追踪7,duration={}", duration);

        if (duration <= 0) {
            // 若时间提前后小于当前时间则直接推送
            log.info("直接推送,plan={}", JacksonUtils.writeValueAsString(plan));
            commonService.sendPricePlanToMQ(messageDTO);
        } else {
            log.info("实际开始推送时间：{}", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(plan.getPushDate()));
            // 定时推送，设置Redis过期时间，过期时由监听器触发推送任务(PricePlanServicesExpirationListener)
            String json = JacksonUtils.writeValueAsString(messageDTO);
            planPriceDisabledCache(plan);
            cacheService.setCacheMap(KEY_PREFIX + json, messageDTO, duration);
        }
    }

    /**
     * 计算过期时间（单位秒）
     */
    private long getDuration(PricePlanDO plan, boolean first, SyncPricePlanMessageDTO messageDTO) {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        log.warn("异常追踪4,plan={}", JacksonUtils.writeValueAsString(plan));
        LocalTime startTime = plan.getStartTime();
        LocalTime endTime = plan.getEndTime();
        LocalDateTime pushDate = plan.getPushDate();
        long duration;
        // 售卖类型-0 默认（全时段）-1特殊时段
        Integer sellTimeType = plan.getSellTimeType();
        if (sellTimeType == 0) {
            duration = Duration.between(LocalDateTime.now(), pushDate).getSeconds();
            messageDTO.setEnterpriseGuid(enterpriseGuid)
                    .setBrandGuid(plan.getBrandGuid())
                    .setPricePlanGuid(plan.getGuid())
                    .setMessageType(PLAN_PUSH_CHANGE);
        } else {
            // 当前时间点
            LocalDateTime nowTime = LocalDateTime.now();

            if (first) {
                LocalDateTime pushDateTime = first(startTime, endTime, pushDate);
                log.warn("异常追踪5,pushDateTime={}", pushDateTime);
                duration = Duration.between(nowTime, pushDateTime).getSeconds();
                log.warn("异常追踪6,duration={}", duration);
            } else {
                LocalDateTime pushDateTime = notFirst(startTime, endTime, nowTime);
                duration = Duration.between(nowTime, pushDateTime).getSeconds();
            }

            messageDTO.setEnterpriseGuid(enterpriseGuid)
                    .setBrandGuid(plan.getBrandGuid())
                    .setPricePlanGuid(plan.getGuid())
                    .setMessageType(PLAN_PUSH_ADD);

        }
        return duration;
    }

    private LocalDateTime first(LocalTime startTime, LocalTime endTime, LocalDateTime pushDate) {
        // 售卖时段
        String startMinute = (startTime.getMinute() + "").length() == 1 ? "0" + startTime.getMinute() : startTime.getMinute() + "";
        int startTimeInt = Integer.parseInt(startTime.getHour() + "" + startMinute);
        String endMinute = (endTime.getMinute() + "").length() == 1 ? "0" + endTime.getMinute() : endTime.getMinute() + "";
        int endTimeInt = Integer.parseInt(endTime.getHour() + "" + endMinute);

        // 立即推送时间或预定生效时间
        int pushTimeInt = Integer.parseInt(pushDate.format(DateTimeFormatter.ofPattern("HH:mm")).replace(":", ""));

        LocalDateTime pushDateTime = null;
        // 预定生效日期在当天或者大于当天
        if (startTimeInt > endTimeInt) {
            // 推送时段垮天
            if (pushTimeInt >= startTimeInt) {
                log.info("xaaaaaaaaaaaaaaaaaa:1");
                pushDateTime = pushDate;
            } else if (pushTimeInt < startTimeInt) {
                log.info("xaaaaaaaaaaaaaaaaaa:2");
                String localYear = pushDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                String localTime = startTime.format(DateTimeFormatter.ofPattern("HH:mm")) + ":00";
                pushDateTime = LocalDateTime.parse(localYear + " " + localTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        } else {
            // 推送时段不垮天
            if (pushTimeInt > startTimeInt && pushTimeInt > endTimeInt) {
                log.info("xaaaaaaaaaaaaaaaaaa:3");
                // 预定生效时间 大于 推送时段，次日推送
                LocalDateTime localDateTime = pushDate.plusDays(1);
                String localYear = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                String localTime = startTime.format(DateTimeFormatter.ofPattern("HH:mm")) + ":00";
                pushDateTime = LocalDateTime.parse(localYear + " " + localTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else if (pushTimeInt > startTimeInt && pushTimeInt < endTimeInt) {
                log.info("xaaaaaaaaaaaaaaaaaa:4");
                // 预定生效时间 在 推送时段之间
                pushDateTime = pushDate;
            } else if (pushTimeInt < startTimeInt) {
                log.info("xaaaaaaaaaaaaaaaaaa:5");
                // 预定生效时间 小于 推送开始时间
                String localYear = pushDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                String localTime = startTime.format(DateTimeFormatter.ofPattern("HH:mm")) + ":00";
                pushDateTime = LocalDateTime.parse(localYear + " " + localTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else {
                log.info("xaaaaaaaaaaaaaaaaaa:6");
                // 预定生效时间 == 推送开始时段
                pushDateTime = pushDate;
            }
        }
        log.info("xaaaaaaaaaaaaaaaaaa:{}", pushDateTime);
        return pushDateTime;
    }

    private LocalDateTime notFirst(LocalTime startTime, LocalTime endTime, LocalDateTime nowTime) {
        // 售卖时段
        String startMinute = (startTime.getMinute() + "").length() == 1 ? "0" + startTime.getMinute() : startTime.getMinute() + "";
        int startTimeInt = Integer.parseInt(startTime.getHour() + "" + startMinute);
        String endMinute = (endTime.getMinute() + "").length() == 1 ? "0" + endTime.getMinute() : endTime.getMinute() + "";
        int endTimeInt = Integer.parseInt(endTime.getHour() + "" + endMinute);

        String pushNowYear = nowTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String startHour = startTime.format(DateTimeFormatter.ofPattern("HH:mm")) + ":00";

        LocalDateTime pushDateTime = null;
        if (endTimeInt > startTimeInt) {
            // 没有跨天
            LocalDateTime startNotiTime = LocalDateTime.parse(pushNowYear + " " + startHour, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            pushDateTime = startNotiTime.plusDays(1);
        } else {
            // 跨天
            pushDateTime = LocalDateTime.parse(pushNowYear + " " + startHour, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }

        return pushDateTime;
    }

    /**
     * 总策略：商户后台创建特殊时段方案，触发开始点通知，
     * 开始点通知执行时，
     * 触发结束点通知，结束点通知执行时，再次出发开始点通知
     * <p>
     * 特殊时段价格方案，结束时间点到后，触发通知
     *
     * @param plan plan
     */
    @Override
    public void planEndNoti(PricePlanDO plan) {
        log.warn("异常追踪1,plan={}", JacksonUtils.writeValueAsString(plan));
        LocalDateTime startTime = plan.getStartTime().atDate(LocalDate.now());
        LocalDateTime endTime = plan.getEndTime().atDate(LocalDate.now());
        LocalDateTime nowTime = LocalDateTime.now();

        String startMinute = (startTime.getMinute() + "").length() == 1 ? "0" + startTime.getMinute() : startTime.getMinute() + "";
        int startTimeInt = Integer.parseInt(startTime.getHour() + "" + startMinute);
        String endMinute = (endTime.getMinute() + "").length() == 1 ? "0" + endTime.getMinute() : endTime.getMinute() + "";
        int endTimeInt = Integer.parseInt(endTime.getHour() + "" + endMinute);
        LocalDateTime endNotiTime;
        if (endTimeInt > startTimeInt) {
            // 没有跨天
            String endYear = nowTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String endHour = endTime.format(DateTimeFormatter.ofPattern("HH:mm")) + ":59";
            endNotiTime = LocalDateTime.parse(endYear + " " + endHour, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            log.warn("异常追踪2,endNotiTime={}", endNotiTime);
        } else {
            // 时间段配置跨天
            LocalDateTime localNowTime = LocalDateTime.now().plusDays(1);
            String endYear = localNowTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String endHour = endTime.format(DateTimeFormatter.ofPattern("HH:mm")) + ":59";
            endNotiTime = LocalDateTime.parse(endYear + " " + endHour, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            log.warn("异常追踪3,endNotiTime={}", endNotiTime);
        }

        // 计算过期时间（单位秒）
        long duration = Duration.between(nowTime, endNotiTime).getSeconds();
        log.warn("异常追踪4,duration={}", duration);

        SyncPricePlanMessageDTO messageDTO = new SyncPricePlanMessageDTO();
        messageDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid())
                .setBrandGuid(plan.getBrandGuid())
                .setPricePlanGuid(plan.getGuid())
                .setMessageType(PLAN_PUSH_DELETE);

        log.info("实际结束推送时间：{}", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(endNotiTime));

        if (duration <= 0) {
            // 若结束售卖时间到达则直接推送
            commonService.sendPricePlanToMQ(messageDTO);
        } else {
            // 定时推送，设置Redis过期时间，过期时由监听器触发推送任务(PricePlanServicesExpirationListener)
            String json = JacksonUtils.writeValueAsString(messageDTO);
            planPriceDisabledCache(plan);
            cacheService.setCacheMap(KEY_PREFIX + json, messageDTO, duration);
        }
    }

    /**
     * 当配置改成 非菜谱方案模式，
     * 当删除或暂不启用 创建的价格方式后，判断是否是特殊时段价格方案，
     * 如果是：删除REDIS缓存，价格方案结束通知
     *
     * @param pricePlanGuid
     */
    private void removePricePlanStartTip(String pricePlanGuid, String storeGuid) {
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        // 非菜谱方案模式
        if (brandDTO.getSalesModel() == SalesModelEnum.NORMAL_MODE.getCode()) {
            cacheService.removeCache(KEY_PREFIX + pricePlanGuid);
        }

        PricePlanDO pricePlan = this.getById(pricePlanGuid);

        // 删除菜谱后
        Integer isDelete = pricePlan.getIsDelete();
        if (isDelete == 1) {
            cacheService.removeCache(KEY_PREFIX + pricePlanGuid);
        }

        // 停用菜谱后 0 未启用 1 已启用 2暂不启用 3永久停用
        Integer status = pricePlan.getStatus();
        if (status != 1) {
            cacheService.removeCache(KEY_PREFIX + pricePlanGuid);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deletePlan(String planGuid) {
        PricePlanDO pricePlanDO = this.getById(planGuid);
        // 菜谱计划暂停或永久停止后，移除定时推送（依据REDIS超时原理）
        planPriceDisabledCache(pricePlanDO);
        //删除方案关联菜品
        planItemMapper.logicDeleteItems(planGuid);
        //删除方案关联门店
        planStoreMapper.deleteStores(planGuid);
        this.removeById(planGuid);
        if (StringUtils.isEmpty(pricePlanDO.getParentGuid())) {
            return true;
        }
        //删除父方案
        return deleteParentPlan(pricePlanDO.getParentGuid());
    }

    /**
     * 删除父方案
     * 如果父方案不为空则递归去删
     *
     * @param parentPlanGuid 父方案guid
     * @return 成功/失败
     */
    private Boolean deleteParentPlan(String parentPlanGuid) {
        PricePlanDO parentPlan = getById(parentPlanGuid);
        if (ObjectUtils.isEmpty(parentPlan)) {
            log.warn("没有找到父级菜谱数据");
            return true;
        }
        List<PricePlanStoreDO> storeDOS = planStoreMapper.selectList(new LambdaQueryWrapper<PricePlanStoreDO>()
                .eq(PricePlanStoreDO::getPlanGuid, parentPlanGuid));
        if (CollectionUtils.isNotEmpty(storeDOS)) {
            List<String> storeGuids = storeDOS.stream().map(PricePlanStoreDO::getStoreGuid).collect(Collectors.toList());
            // 验证当前修改的菜谱是否在售卖中
            boolean isSale = validateAllTimePlanPush(storeGuids, parentPlan);
            if (isSale) {
                // 菜谱停用，门店被删除了，所以这里单独推送
                log.info("菜谱提示异常日志跟踪13}");
                eventPushHelper.pushMsgToAndriod(storeGuids);
            }
        }
        //删除方案关联菜品
        planItemMapper.logicDeleteItems(parentPlanGuid);
        //删除方案关联门店
        planStoreMapper.deleteStores(parentPlanGuid);
        removeById(parentPlanGuid);
        if (StringUtils.isEmpty(parentPlan.getParentGuid())) {
            return true;
        }
        //删除父方案
        return deleteParentPlan(parentPlan.getParentGuid());
    }

    @Override
    public PricePlanReqDTO getPricePlanCacheData(String userGuid, String brandGuid) {
        String redisKey = PLAN_CACHE + userGuid + COLON + brandGuid;
        Object result = redisUtil.get(redisKey);
        if (ObjectUtil.isNull(result)) {
            log.info("草稿不存在");
            return null;
        }
        return JSON.parseObject(result.toString(), PricePlanReqDTO.class);
    }

    @Override
    public Boolean deletePlanDraft(PricePlanDraftReqDTO reqDTO) {
        Assert.notNull(reqDTO, "请求参数不能为空！");
        Assert.hasLength(reqDTO.getUserGuid(), "用户Guid为空！");
        Assert.hasLength(reqDTO.getBrandGuid(), "品牌guid为空！");
        String redisKey = PLAN_CACHE + reqDTO.getUserGuid() + COLON + reqDTO.getBrandGuid();
        return redisUtil.delete(redisKey);
    }

    @Override
    public List<PricePlanRespDTO> queryPlansByStoreGuid(String storeGuid) {
        // 先根据门店查询所属品牌
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        if (ObjectUtils.isEmpty(brandDTO)) {
            log.warn("未查询到品牌信息 storeGuid: {}", storeGuid);
            throw new BusinessException("未查询到品牌信息");
        }
        // 然后通过品牌查询价格方案，这里只展示即将启用和已启用的菜谱
        List<PricePlanDO> pricePlanDOList = this.list(new LambdaQueryWrapper<PricePlanDO>()
                .eq(PricePlanDO::getBrandGuid, brandDTO.getGuid())
                .eq(PricePlanDO::getIsDelete, 0)
                .isNull(PricePlanDO::getParentGuid)
                .and(p -> p.eq(PricePlanDO::getStatus, PricePlanStatusEnum.USING.getCode())
                        .or()
                        .eq(PricePlanDO::getStatus, PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode())
                )
        );
        ArrayList<PricePlanRespDTO> respDTOList = new ArrayList<>();
        pricePlanDOList.forEach(
                pricePlanDO -> {
                    PricePlanRespDTO pricePlanRespDTO = new PricePlanRespDTO();
                    BeanUtil.copyProperties(pricePlanDO, pricePlanRespDTO);
                    respDTOList.add(pricePlanRespDTO);
                }
        );
        return respDTOList;
    }

    /**
     * 永久停用方案
     *
     * @param planGuid 方案id
     * @return Boolean
     */
    @Override
    public Boolean permanentlyDeactivate(String planGuid) {
        PricePlanDO byId = this.getById(planGuid);
        // 菜谱计划暂停或永久停止后，移除定时推送（依据REDIS超时原理）
        PricePlanDO pricePlanDO = new PricePlanDO();
        pricePlanDO.setBrandGuid(byId.getBrandGuid());
        pricePlanDO.setGuid(planGuid);
        planPriceDisabledCache(pricePlanDO);

        return this.update(new LambdaUpdateWrapper<PricePlanDO>()
                .set(PricePlanDO::getStatus, PricePlanStatusEnum.LASTING_DISABLE.getCode())
                .eq(PricePlanDO::getGuid, planGuid)
        );
    }

    @Override
    public PricePlanDO usingPricePlanEditHandle(PricePlanDO pricePlanDO) {
        PricePlanDO parentPlanDO = this.getById(pricePlanDO.getParentGuid());
        //保存方案信息
        BeanUtil.copyProperties(pricePlanDO, parentPlanDO,
                "guid", "id", "planCode", "parentGuid");
        parentPlanDO.setEffectiveTime(LocalDateTime.now());
        parentPlanDO.setStatus(PricePlanStatusEnum.USING.getCode());
        parentPlanDO.setInstantlyEffectiveTime(null);
        pricePlanMapper.updateById(parentPlanDO);
        //修改之前和修改后的门店信息
        List<PricePlanStoreDO> updateBeforeStores = planStoreMapper.selectList(new LambdaQueryWrapper<PricePlanStoreDO>()
                .eq(PricePlanStoreDO::getPlanGuid, parentPlanDO.getGuid()));
        List<String> updateAfterStoreGuids = planStoreMapper.selectList(new LambdaQueryWrapper<PricePlanStoreDO>()
                        .eq(PricePlanStoreDO::getPlanGuid, pricePlanDO.getGuid())).
                stream().map(PricePlanStoreDO::getStoreGuid).collect(Collectors.toList());
        // 验证当前修改的菜谱是否在售卖中
        boolean isSale = validateAllTimePlanPush(updateAfterStoreGuids, parentPlanDO);
        //删除已有分类
        typeService.updateTypePlan(pricePlanDO.getGuid(), parentPlanDO.getGuid());
        //保存商品信息
        planItemService.updatePlanItem(pricePlanDO.getGuid(), parentPlanDO.getGuid());
        //保存门店信息
        pricePlanStoreService.updatePlanStore(pricePlanDO.getGuid(), parentPlanDO.getGuid());
        //删除编辑后的方案
        this.remove(new LambdaQueryWrapper<PricePlanDO>().eq(PricePlanDO::getGuid, pricePlanDO.getGuid()));
        if (isSale) {
            // 通知被删除的门店，菜谱发生了变更
            planStoreRemovePush(updateBeforeStores, updateAfterStoreGuids);
        }
        return parentPlanDO;
    }

    @Override
    public Boolean copyPlan(String planGuid) {
        //查询原菜谱方案
        PricePlanDO plan = pricePlanMapper.selectById(planGuid);
        if (null == plan) {
            throw new BusinessException("菜谱方案不存在");
        }
        //复制方案信息
        PricePlanDO copyPlan = copyPlan(plan);
        //复制分类信息
        Map<String, String> typeMap = new HashMap<>();
        List<TypeDO> typeDOList = typeService.list(new LambdaQueryWrapper<TypeDO>()
                .eq(TypeDO::getPricePlanGuid, planGuid)
        );
        typeDOList.forEach(t -> addPricePlanType(copyPlan.getGuid(), typeMap
                , MapStructUtils.INSTANCE.typeDO2TypePricePlanReqDTO(t)));
        //复制商品信息
        List<PricePlanItemDO> planItemDOS = planItemService.list(new LambdaQueryWrapper<PricePlanItemDO>()
                .eq(PricePlanItemDO::getPlanGuid, plan.getGuid()));
        copyItems(copyPlan, planItemDOS);
        //新菜谱类型 ParentGuid 置为空,和之前的类型没有关联（只有复制菜谱）
        typeService.update(new LambdaUpdateWrapper<TypeDO>().set(TypeDO::getParentGuid, null).eq(TypeDO::getPricePlanGuid,
                copyPlan.getGuid()));
        return true;
    }

    /**
     * 复制菜谱方案基础信息
     *
     * @param plan 原菜谱方案
     * @return 复制后菜谱方案
     */
    private PricePlanDO copyPlan(PricePlanDO plan) {
        PricePlanDO copyPlan = new PricePlanDO();
        String guid = GUIDUtils.generateGuid(GuidKeyConstant.HSI_PRICEPLAN);
        copyPlan.setGuid(guid);
        copyPlan.setBrandGuid(plan.getBrandGuid());
        copyPlan.setDescription(plan.getDescription());
        copyPlan.setStatus(PricePlanStatusEnum.NOT_ENABLED.getCode());
        copyPlan.setItemNum(plan.getItemNum());
        copyPlan.setPushType(1);
        copyPlan.setPlanCode(getPlanCode(guid));
        copyPlan.setStoreNum(0);
        copyPlan.setSellTimeType(0);
        //查询已经复制过的菜谱方案 菜谱方案名规则为  （复制）菜谱  （复制1）菜谱 (复制2)菜谱
        List<PricePlanDO> copyPlans = this.list(new LambdaQueryWrapper<PricePlanDO>()
                .likeRight(PricePlanDO::getName, "(复制")
                .likeLeft(PricePlanDO::getName, ")" + plan.getName()));
        String copyName;
        if (CollectionUtils.isEmpty(copyPlans)) {
            copyName = "(复制)" + plan.getName();
        } else {
            copyName = "(复制" + (copyPlans.size() + 1) + ")" + plan.getName();
        }
        //如果超过20字，截取前面20字
        if (copyName.length() > 20) {
            copyName = copyName.substring(0, 20);
        }
        copyPlan.setName(copyName);
        save(copyPlan);
        return copyPlan;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchEditPlanPrice(PlanPriceEditReqDTO req) {
        //数据结构的转换
        List<PricePlanReqDTO> list = transferBatchEditPlanPriceData(req);

        if (CollectionUtils.isNotEmpty(list)) {
            for (PricePlanReqDTO pricePlanReqDTO : list) {
                pricePlanReqDTO.setBatchEdit(Boolean.TRUE);
            }
            asyncSavePlanLimit(list);
        }
    }

    @Override
    public void saveBatchEditPlanPriceType(PlanPriceEditReqDTO req) {
        List<PricePlanReqDTO> list = transferBatchEditPlanPriceTypeData(req);
        if (CollectionUtils.isNotEmpty(list)) {
            for (PricePlanReqDTO pricePlanReqDTO : list) {
                pricePlanReqDTO.setBatchEdit(Boolean.TRUE);
            }
            asyncSavePlanLimit(list);
        }
    }

    @Override
    public List<PlanPriceEditDTO> listAvailablePlanPrice(PlanPriceAvailableReqDTO req) {
        //查询修改备份的菜谱方案 列表中需排除
        List<PricePlanDO> backPlanDOS = this.list(new LambdaQueryWrapper<PricePlanDO>()
                .isNotNull(PricePlanDO::getParentGuid));
        if (CollectionUtils.isNotEmpty(backPlanDOS)) {
            List<String> collect = backPlanDOS.stream().map(PricePlanDO::getParentGuid).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(req.getPlanGuidList())) {
                collect.addAll(req.getPlanGuidList());
            }
            req.setPlanGuidList(collect);
        }
        List<PlanPriceEditDTO> list = planItemMapper.listAvailablePlanPrice(req);
        //根据菜谱名字去重取
        if (CollUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(PlanPriceEditDTO::getPlanName))), ArrayList::new
        ));
    }

    @Override
    public List<ItemTypeRespDTO> listAvailablePlanItemType(String planGuid) {
        return planItemMapper.listAvailablePlanItemType(planGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchAddPlanPrice(PlanPriceAddReqDTO req) {
        List<PlanPriceAddBaseDTO> priceAddBaseList = req.getPlanPriceAddBaseList();
        if (CollectionUtils.isEmpty(priceAddBaseList)) {
            throw new BusinessException("方案信息为空");
        }
        List<ItemWebRespDTO> itemWebRespDTOList = req.getItemWebRespDTOList();
        if (CollectionUtils.isEmpty(itemWebRespDTOList)) {
            return;
        }
        Integer pushType = req.getPushType();

        //根据商品信息查询方案信息
        List<String> planGuidList = priceAddBaseList.stream()
                .map(PlanPriceAddBaseDTO::getPlanGuid)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(planGuidList)) {
            throw new BusinessException("方案Guid为空");
        }
        List<PlanPriceExistItemDTO> planPriceAddList = planItemMapper.listPlanPriceExistItem(planGuidList);
        Map<String, PlanPriceExistItemDTO> existItemMap = planPriceAddList.stream()
                .collect(Collectors.toMap(PlanPriceExistItemDTO::getPlanGuid, obj -> obj));

        //根据商品信息查询方案信息
        Map<String, List<String>> storeListMap = planStoreMapper.getPlanStoreListByPlanGuidList(planGuidList)
                .stream().collect(Collectors.groupingBy(PricePlanStoreInfoDTO::getPlanGuid,
                        Collectors.mapping(PricePlanStoreInfoDTO::getStoreGuid, Collectors.toList())));

        // 多规格商品处理
        Map<String, Map<String, List<ItemWebRespDTO>>> multiSpecMapByPlanGuid = Maps.newHashMap();
        List<String> specItemGuidList = Lists.newArrayList();
        List<String> specSkuGuidList = Lists.newArrayList();
        for (ItemWebRespDTO dto : itemWebRespDTOList) {
            if (dto.getItemType() == ItemTypeEnum.MULTI_SPEC.getTypeCode()) {
                specItemGuidList.add(dto.getItemGuid());
                if (CollectionUtils.isNotEmpty(dto.getSkuList())) {
                    specSkuGuidList.addAll(dto.getSkuList().stream().map(SkuRespDTO::getSkuGuid).collect(Collectors.toList()));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(specItemGuidList)) {
            // 查询方案中其他的规格商品
            ItemQueryReqDTO queryReqDTO = new ItemQueryReqDTO();
            queryReqDTO.setFilterSkuList(specSkuGuidList);
            queryReqDTO.setPlanGuidList(planGuidList);
            queryReqDTO.setPlanItemList(specItemGuidList);
            List<ItemWebRespDTO> queryItemWebRespDTOList = planItemMapper.queryPlanItemsByPlanAll(queryReqDTO);
            Map<String, List<ItemWebRespDTO>> collect = queryItemWebRespDTOList.stream()
                    .collect(Collectors.groupingBy(ItemWebRespDTO::getPlanGuid));
            for (Map.Entry<String, List<ItemWebRespDTO>> entry : collect.entrySet()) {
                multiSpecMapByPlanGuid.put(entry.getKey(), entry.getValue().stream()
                        .collect(Collectors.groupingBy(ItemWebRespDTO::getItemGuid)));
            }
        }

        //构建批量新增的商品信息
        List<PricePlanReqDTO> list = new ArrayList<>();
        //新增的商品信息
        for (PlanPriceAddBaseDTO obj : priceAddBaseList) {
            PricePlanReqDTO pricePlanReq = new PricePlanReqDTO();
            List<ItemWebRespDTO> singleItemWebRespDTOList = MapStructUtils.INSTANCE.itemWebRespDTO2ItemWebRespDTOList(
                    req.getItemWebRespDTOList());
            //此方案已存在的商品信息
            PlanPriceExistItemDTO existItemDTO = existItemMap.get(obj.getPlanGuid());
            singleItemWebRespDTOList.forEach(item -> {
                item.setTypeGuid(obj.getTypeGuid());
                item.setTypeName(obj.getTypeName());
                // 如果前端没传就初始化字段，表示清空覆盖
                if (StringUtils.isEmpty(item.getEnglishBrief())) {
                    item.setEnglishBrief("");
                }
                if (StringUtils.isEmpty(item.getEnglishIngredientsDesc())) {
                    item.setEnglishIngredientsDesc("");
                }
                // 添加已存在菜谱中的其他规格的商品
                if (ItemTypeEnum.MULTI_SPEC.getTypeCode() == item.getItemType()) {
                    Map<String, List<ItemWebRespDTO>> multiSpecMapByItemGuid = multiSpecMapByPlanGuid.get(obj.getPlanGuid());
                    if (MapUtils.isNotEmpty(multiSpecMapByItemGuid)) {
                        List<ItemWebRespDTO> multiSpecOtherList = multiSpecMapByItemGuid.get(item.getItemGuid());
                        if (CollectionUtils.isNotEmpty(multiSpecOtherList)) {
                            item.getSkuList().addAll(MapStructUtils.INSTANCE.itemWebRespDTO2SkuRespDTOList(multiSpecOtherList));
                        }
                    }
                }
            });
            pricePlanReq.setItemWebRespDTOList(singleItemWebRespDTOList);
            pricePlanReq.setGuid(obj.getPlanGuid());
            pricePlanReq.setPushType(pushType);
            // pricePlanReq.setPushDate(req.getPushDate());
            pricePlanReq.setStatus(PricePlanStatusEnum.USING.getCode());
            if (!ObjectUtil.equal(pricePlanReq.getStatus(), PricePlanStatusEnum.NOT_ENABLED.getCode())) {
                pricePlanReq.setPushDate(req.getPushDate());
//                pricePlanReq.setStatus(ObjectUtil.equal(req.getPushType(), 1) ? PricePlanStatusEnum.USING.getCode() :
//                PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode());
            }
            pricePlanReq.setSellTimeType(existItemDTO.getSellTimeType());
            pricePlanReq.setStartTime(existItemDTO.getStartTime());
            pricePlanReq.setEndTime(existItemDTO.getEndTime());
            pricePlanReq.setStoreGuidList(storeListMap.get(obj.getPlanGuid()));
            pricePlanReq.setName(existItemDTO.getPlanName());
            pricePlanReq.setBrandGuid(existItemDTO.getBrandGuid());
            pricePlanReq.setBatchEdit(Boolean.TRUE);
            list.add(pricePlanReq);
        }

        if (CollectionUtils.isNotEmpty(list)) {
            for (PricePlanReqDTO pricePlanReqDTO : list) {
                // 校验商品名称
                if (CollectionUtils.isNotEmpty(pricePlanReqDTO.getItemWebRespDTOList())) {
                    pricePlanReqDTO.getItemWebRespDTOList().forEach(item -> {
                        if (item.getPlanItemName().length() > 40) {
                            throw new BusinessException("菜谱方案商品名称请输入1-40个字符");
                        }
                    });
                }
            }
            asyncSavePlanLimit(list);
        }
    }

    private static final int LIMIT_DEAL = 5;

    private void asyncSavePlanLimit(List<PricePlanReqDTO> list) {
        List<List<PricePlanReqDTO>> groupList = Lists.partition(list, LIMIT_DEAL);
        UserContext userContext = UserContextUtils.get();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();

        groupList.forEach(planList -> CompletableFuture.runAsync(() -> {
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            UserContextUtils.put(userContext);
            planList.forEach(plan -> {
                try {
                    this.savePlan(plan);
                } catch (Exception e) {
                    log.error("方案[{}]发生了异常, 异常信息,e:", plan.getName(), e);
                    log.warn("[菜谱执行批量操作]方案[{}]发生了异常, 继续执行其他线程,错误详情[{}]", plan.getName(), e.getMessage());
                }
            });
            log.info("菜谱执行批量操作，已完成菜谱：{}", planList.stream().map(PricePlanReqDTO::getGuid).collect(Collectors.toList()));
        }, threadPoolConfig.getAsyncExecutor()).exceptionally(t -> {
            log.error("异常信息,e:", t);
            log.warn("[菜谱执行批量操作]线程[{}]发生了异常, 继续执行其他线程,错误详情[{}]", Thread.currentThread().getName(), t.getMessage());
            return null;
        }));
    }

    private List<PricePlanReqDTO> transferBatchEditPlanPriceData(PlanPriceEditReqDTO req) {
        List<PlanPriceEditItemDTO> planPriceList = req.getPlanPriceList();
        Integer pushType = req.getPushType();
        //根据商品信息查询方案信息
        List<String> collect = planPriceList.stream().map(PlanPriceEditItemDTO::getItemGuid).collect(Collectors.toList());
        List<PlanPriceEditDTO> planPriceEditList = planItemMapper.listPlanPriceEditByItems(collect);
        Map<String, PlanPriceEditDTO> priceEditDTOMap = planPriceEditList.stream()
                .collect(Collectors.toMap(PlanPriceEditDTO::getPlanGuid, obj -> obj));

        List<Map<String, ItemWebRespDTO>> itemList = new ArrayList<>();
        Map<String, Map<String, List<SkuRespDTO>>> skuSalePriceList = new HashMap<>();
        Map<String, Map<String, List<SkuRespDTO>>> skuMemberPriceList = new HashMap<>();
        Map<String, Map<String, List<SkuRespDTO>>> skuAccountingPriceList = new HashMap<>();
        Map<String, Map<String, List<SkuRespDTO>>> skuTakeawayAccountingPriceList = new HashMap<>();
        Map<String, Map<String, List<SkuRespDTO>>> skuLinePriceList = new HashMap<>();
        Set<String> keyList = new HashSet<>();
        // 分类map
        Map<String, Set<String>> itemTypeNameMap = new HashMap<>();

        for (PlanPriceEditItemDTO planPriceEditItemDTO : planPriceList) {
            String itemGuid = planPriceEditItemDTO.getItemGuid();

            Map<String, ItemWebRespDTO> itemMap = new HashMap<>();

            buildDescription(itemMap, keyList, itemGuid, planPriceEditItemDTO.getDescriptionList());

            buildSort(itemMap, keyList, itemGuid, planPriceEditItemDTO.getSortList());

            buildSaleName(itemMap, keyList, itemGuid, planPriceEditItemDTO.getSaleNameList());

            buildTypeName(itemMap, keyList, itemGuid, planPriceEditItemDTO.getTypeList(), itemTypeNameMap);

            Map<String, List<SkuRespDTO>> skuSalePriceMap
                    = buildSalePrice(itemMap, keyList, itemGuid, planPriceEditItemDTO.getSalePriceList());

            Map<String, List<SkuRespDTO>> skuMemberPriceMap
                    = buildMemberPrice(itemMap, keyList, itemGuid, planPriceEditItemDTO.getMemberPriceList());

            Map<String, List<SkuRespDTO>> skuAccountingPriceMap
                    = buildAccountingPrice(itemMap, keyList, itemGuid, planPriceEditItemDTO.getAccountingPriceList());

            Map<String, List<SkuRespDTO>> skuTakeawayAccountingPriceMap = buildTakeawayAccountingPrice(itemMap, keyList,
                    itemGuid, planPriceEditItemDTO.getTakeawayAccountingPriceList());

            Map<String, List<SkuRespDTO>> skuLinePriceMap
                    = buildLinePrice(itemMap, keyList, itemGuid, planPriceEditItemDTO.getLinePriceList());

            buildEnglishBrief(itemMap, keyList, itemGuid, planPriceEditItemDTO.getEnglishBriefList());

            buildEnglishIngredientsDesc(itemMap, keyList, itemGuid, planPriceEditItemDTO.getEnglishIngredientsDescList());

            // 将前面不涉及图片的修改处理下
            itemMap.values().forEach(item -> {
                item.setPictureUrl(null);
                item.setBigPictureUrl(null);
                item.setDetailBigPictureUrl(null);
            });

            buildPicture(itemMap, keyList, itemGuid, planPriceEditItemDTO.getPictureList());

            itemList.add(itemMap);
            skuSalePriceList.put(planPriceEditItemDTO.getItemGuid(), skuSalePriceMap);
            skuMemberPriceList.put(planPriceEditItemDTO.getItemGuid(), skuMemberPriceMap);
            skuAccountingPriceList.put(planPriceEditItemDTO.getItemGuid(), skuAccountingPriceMap);
            skuTakeawayAccountingPriceList.put(planPriceEditItemDTO.getItemGuid(), skuTakeawayAccountingPriceMap);
            skuLinePriceList.put(planPriceEditItemDTO.getItemGuid(), skuLinePriceMap);
        }

        if (CollectionUtil.isEmpty(itemList) || CollectionUtil.isEmpty(priceEditDTOMap)) {
            throw new BusinessException("菜谱方案批量编辑菜品失败");
        }

        List<String> strings = new ArrayList<>(keyList);
        Map<String, List<String>> storeListMap = planStoreMapper.getPlanStoreListByPlanGuidList(strings)
                .stream().collect(Collectors.groupingBy(PricePlanStoreInfoDTO::getPlanGuid,
                        Collectors.mapping(PricePlanStoreInfoDTO::getStoreGuid, Collectors.toList())));

        List<PricePlanReqDTO> list = new ArrayList<>();

        keyList.forEach(key -> {
            PlanPriceEditDTO planPriceEditDTO = priceEditDTOMap.get(key);
            if (ObjectUtil.isNull(planPriceEditDTO)) {
                return;
            }

            PricePlanReqDTO ppr = new PricePlanReqDTO();

            ppr.setGuid(key);
            ppr.setPushType(pushType);
            ppr.setStatus(PricePlanStatusEnum.USING.getCode());
//            if (Objects.equals(PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode(), planPriceEditDTO.getStatus()) &&
//                    Objects.equals(1, pushType)) {
//                ppr.setStatus(PricePlanStatusEnum.USING.getCode());
//            }
            if (!ObjectUtil.equal(ppr.getStatus(), PricePlanStatusEnum.NOT_ENABLED.getCode())) {
                ppr.setPushDate(req.getPushDate());
//                ppr.setStatus(ObjectUtil.equal(req.getPushType(), 1) ? PricePlanStatusEnum.USING.getCode() :
//                PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode());
            }
            ppr.setSellTimeType(planPriceEditDTO.getSellTimeType());
            ppr.setStoreGuidList(storeListMap.get(key));
            ppr.setName(planPriceEditDTO.getPlanName());
            ppr.setStartTime(planPriceEditDTO.getStartTime());
            ppr.setEndTime(planPriceEditDTO.getEndTime());
            ppr.setBrandGuid(planPriceEditDTO.getBrandGuid());
            ppr.setItemWebRespDTOList(buildItemWebResp(key, itemList, skuSalePriceList, skuMemberPriceList,
                    skuAccountingPriceList, skuTakeawayAccountingPriceList, skuLinePriceList));
            // 分类list
            Set<String> typeNameSet = itemTypeNameMap.getOrDefault(key, Sets.newHashSet());
            List<TypePricePlanReqDTO> typeList = typeNameSet.stream().map(e -> {
                TypePricePlanReqDTO typePricePlanReqDTO = new TypePricePlanReqDTO();
                typePricePlanReqDTO.setName(e);
                typePricePlanReqDTO.setIsDelete(0);
                return typePricePlanReqDTO;
            }).collect(Collectors.toList());
            ppr.setTypeList(typeList);
            ppr.setBatchEdit(Boolean.TRUE);

            list.add(ppr);
        });
        return list;
    }

    private Map<String, List<SkuRespDTO>> buildLinePrice(Map<String, ItemWebRespDTO> itemMap,
                                                         Set<String> keyList, String itemGuid,
                                                         List<PlanPriceSaleOrMemberPriceEditDTO> linePriceList) {
        if (CollectionUtil.isNotEmpty(linePriceList)) {
            Map<String, List<SkuRespDTO>> skuLinePriceMap = new HashMap<>();

            for (PlanPriceSaleOrMemberPriceEditDTO linePriceDTO : linePriceList) {
                List<PlanPriceEditPriceBaseDTO> skuPriceList = linePriceDTO.getSkuPriceList();
                List<SkuRespDTO> list = new ArrayList<>();
                for (PlanPriceEditPriceBaseDTO priceBaseDTO : skuPriceList) {
                    SkuRespDTO skuRespDTO = new SkuRespDTO();
                    skuRespDTO.setLinePrice(priceBaseDTO.getPrice());
                    skuRespDTO.setSkuGuid(priceBaseDTO.getSkuGuid());
                    skuRespDTO.setPlanItemGuid(itemGuid);
                    list.add(skuRespDTO);
                }
                linePriceDTO.getPlanPriceList().forEach(obj ->
                {
                    keyList.add(obj.getPlanGuid());
                    skuLinePriceMap.put(obj.getPlanGuid(), list);
                    ItemWebRespDTO itemWeb = itemMap.get(obj.getPlanGuid());
                    if (ObjectUtil.isNull(itemWeb)) {
                        itemWeb = new ItemWebRespDTO();
                        itemWeb.setItemGuid(itemGuid);
                    }
                    itemWeb.setSkuList(list);
                    itemMap.put(obj.getPlanGuid(), itemWeb);
                });
            }
            return skuLinePriceMap;
        }
        return null;
    }

    private Map<String, List<SkuRespDTO>> buildAccountingPrice(Map<String, ItemWebRespDTO> itemMap,
                                                               Set<String> keyList, String itemGuid,
                                                               List<PlanPriceSaleOrMemberPriceEditDTO> accountingPriceList) {
        if (CollectionUtil.isNotEmpty(accountingPriceList)) {
            Map<String, List<SkuRespDTO>> skuAccountingPriceMap = new HashMap<>();
            for (PlanPriceSaleOrMemberPriceEditDTO accountingPriceDTO : accountingPriceList) {
                List<PlanPriceEditPriceBaseDTO> skuPriceList = accountingPriceDTO.getSkuPriceList();
                List<SkuRespDTO> list = new ArrayList<>();
                for (PlanPriceEditPriceBaseDTO priceBaseDTO : skuPriceList) {
                    SkuRespDTO skuRespDTO = new SkuRespDTO();
                    skuRespDTO.setAccountingPrice(priceBaseDTO.getPrice());
                    skuRespDTO.setSkuGuid(priceBaseDTO.getSkuGuid());
                    skuRespDTO.setPlanItemGuid(itemGuid);
                    list.add(skuRespDTO);
                }
                accountingPriceDTO.getPlanPriceList().forEach(obj ->
                {
                    keyList.add(obj.getPlanGuid());
                    skuAccountingPriceMap.put(obj.getPlanGuid(), list);
                    ItemWebRespDTO itemWeb = itemMap.get(obj.getPlanGuid());
                    if (ObjectUtil.isNull(itemWeb)) {
                        itemWeb = new ItemWebRespDTO();
                        itemWeb.setItemGuid(itemGuid);
                    }
                    itemWeb.setSkuList(list);
                    itemMap.put(obj.getPlanGuid(), itemWeb);
                });
            }
            return skuAccountingPriceMap;
        }
        return null;
    }

    private Map<String, List<SkuRespDTO>> buildTakeawayAccountingPrice(Map<String, ItemWebRespDTO> itemMap,
                                                                       Set<String> keyList, String itemGuid,
                                                                       List<PlanPriceSaleOrMemberPriceEditDTO> takeawayAccountingPriceList) {
        if (CollectionUtil.isNotEmpty(takeawayAccountingPriceList)) {
            Map<String, List<SkuRespDTO>> skuTakeawayAccountingPriceMap = new HashMap<>();
            for (PlanPriceSaleOrMemberPriceEditDTO takeawayAccountingPriceDTO : takeawayAccountingPriceList) {
                List<PlanPriceEditPriceBaseDTO> skuPriceList = takeawayAccountingPriceDTO.getSkuPriceList();
                List<SkuRespDTO> list = new ArrayList<>();
                for (PlanPriceEditPriceBaseDTO priceBaseDTO : skuPriceList) {
                    SkuRespDTO skuRespDTO = new SkuRespDTO();
                    skuRespDTO.setTakeawayAccountingPrice(priceBaseDTO.getPrice());
                    skuRespDTO.setSkuGuid(priceBaseDTO.getSkuGuid());
                    skuRespDTO.setPlanItemGuid(itemGuid);
                    list.add(skuRespDTO);
                }
                takeawayAccountingPriceDTO.getPlanPriceList().forEach(obj -> {
                    keyList.add(obj.getPlanGuid());
                    skuTakeawayAccountingPriceMap.put(obj.getPlanGuid(), list);
                    ItemWebRespDTO itemWeb = itemMap.get(obj.getPlanGuid());
                    if (ObjectUtil.isNull(itemWeb)) {
                        itemWeb = new ItemWebRespDTO();
                        itemWeb.setItemGuid(itemGuid);
                    }
                    itemWeb.setSkuList(list);
                    itemMap.put(obj.getPlanGuid(), itemWeb);
                });
            }
            return skuTakeawayAccountingPriceMap;
        }
        return null;
    }

    private List<ItemWebRespDTO> buildItemWebResp(String key, List<Map<String, ItemWebRespDTO>> itemList,
                                                  Map<String, Map<String, List<SkuRespDTO>>> skuSalePriceList,
                                                  Map<String, Map<String, List<SkuRespDTO>>> skuMemberPriceList,
                                                  Map<String, Map<String, List<SkuRespDTO>>> skuAccountingPriceList,
                                                  Map<String, Map<String, List<SkuRespDTO>>> skuTakeawayAccountingPriceList,
                                                  Map<String, Map<String, List<SkuRespDTO>>> skuLinePriceList) {
        List<ItemWebRespDTO> resp = new ArrayList<>();
        for (Map<String, ItemWebRespDTO> item : itemList) {
            if (item.get(key) != null) {
                ItemWebRespDTO itemWebRespDTO = item.get(key);
                Map<String, List<SkuRespDTO>> stringListMap = skuSalePriceList.get(itemWebRespDTO.getItemGuid());
                Map<String, List<SkuRespDTO>> memberPriceListMap = skuMemberPriceList.get(itemWebRespDTO.getItemGuid());
                Map<String, List<SkuRespDTO>> accountingPriceListMap = skuAccountingPriceList.get(itemWebRespDTO.getItemGuid());
                Map<String, List<SkuRespDTO>> takeawayAccountingPriceListMap = skuTakeawayAccountingPriceList.get(itemWebRespDTO.getItemGuid());
                Map<String, List<SkuRespDTO>> linePriceListMap = skuLinePriceList.get(itemWebRespDTO.getItemGuid());
                Map<String, SkuRespDTO> saleCollect = new HashMap<>();
                Map<String, SkuRespDTO> memberCollect = new HashMap<>();
                Map<String, SkuRespDTO> accountingCollect = new HashMap<>();
                Map<String, SkuRespDTO> takeawayAccountingCollect = new HashMap<>();
                Map<String, SkuRespDTO> lineCollect = new HashMap<>();

                List<PricePlanSkuRespDTO> skuList = planItemMapper.getSkuListByPlanItemGuid(itemWebRespDTO.getItemGuid(), key);
                PricePlanSkuRespDTO pricePlanSkuRespDTO = skuList.get(0);
                itemWebRespDTO.setItemType(ObjectUtil.equal(pricePlanSkuRespDTO.getIsPkgItem(), BooleanEnum.TRUE.getCode())
                        ? ItemTypeEnum.PACKAGE.getTypeCode() : ItemTypeEnum.MULTI_SPEC.getTypeCode());
                itemWebRespDTO.setIsSoldOut(pricePlanSkuRespDTO.getIsSoldOut());
                if (null == itemWebRespDTO.getPictureUrl()) {
                    itemWebRespDTO.setPictureUrl(pricePlanSkuRespDTO.getPictureUrl());
                }
                if (null == itemWebRespDTO.getBigPictureUrl()) {
                    itemWebRespDTO.setBigPictureUrl(pricePlanSkuRespDTO.getBigPictureUrl());
                }
                if (null == itemWebRespDTO.getDetailBigPictureUrl()) {
                    itemWebRespDTO.setDetailBigPictureUrl(pricePlanSkuRespDTO.getDetailBigPictureUrl());
                }
                if (ObjectUtil.isNull(itemWebRespDTO.getPlanItemName())) {
                    itemWebRespDTO.setPlanItemName(pricePlanSkuRespDTO.getPlanItemName());
                }
                // 设置商品排序
                setItemSort(itemWebRespDTO, pricePlanSkuRespDTO);
                // 商品分类名称 （放在设置sort后）
                setItemTypeName(itemWebRespDTO, pricePlanSkuRespDTO);
                if (CollectionUtil.isNotEmpty(stringListMap)) {
                    List<SkuRespDTO> skuResp = stringListMap.get(key);
                    if (CollectionUtils.isNotEmpty(skuResp)) {
                        saleCollect = skuResp.stream().collect(Collectors.toMap(SkuRespDTO::getSkuGuid, obj -> obj));
                    }
                }

                if (CollectionUtil.isNotEmpty(memberPriceListMap)) {
                    List<SkuRespDTO> memberSkuResp = memberPriceListMap.get(key);
                    if (CollectionUtils.isNotEmpty(memberSkuResp)) {
                        memberCollect = memberSkuResp.stream().collect(Collectors.toMap(SkuRespDTO::getSkuGuid, obj -> obj));
                    }
                }
                if (CollectionUtil.isNotEmpty(accountingPriceListMap)) {
                    List<SkuRespDTO> accountingSkuResp = accountingPriceListMap.get(key);
                    if (CollectionUtils.isNotEmpty(accountingSkuResp)) {
                        accountingCollect = accountingSkuResp.stream().collect(Collectors.toMap(SkuRespDTO::getSkuGuid, obj -> obj));
                    }
                }
                if (CollectionUtil.isNotEmpty(takeawayAccountingPriceListMap)) {
                    List<SkuRespDTO> accountingSkuResp = takeawayAccountingPriceListMap.get(key);
                    if (CollectionUtils.isNotEmpty(accountingSkuResp)) {
                        takeawayAccountingCollect = accountingSkuResp.stream().collect(Collectors.toMap(SkuRespDTO::getSkuGuid, obj -> obj));
                    }
                }
                if (CollectionUtil.isNotEmpty(linePriceListMap)) {
                    List<SkuRespDTO> lineSkuResp = linePriceListMap.get(key);
                    if (CollectionUtils.isNotEmpty(lineSkuResp)) {
                        lineCollect = lineSkuResp.stream().collect(Collectors.toMap(SkuRespDTO::getSkuGuid, obj -> obj));
                    }
                }
                List<SkuRespDTO> listSku = new ArrayList<>();
                Map<String, SkuRespDTO> finalSaleCollect = saleCollect;
                Map<String, SkuRespDTO> finalMemberCollect = memberCollect;
                Map<String, SkuRespDTO> finalAccountingCollect = accountingCollect;
                Map<String, SkuRespDTO> finalTakeawayAccountingCollect = takeawayAccountingCollect;
                Map<String, SkuRespDTO> finalLineCollect = lineCollect;

                skuList.forEach(obj -> {
                    SkuRespDTO skuRespDTO = new SkuRespDTO();
                    skuRespDTO.setPlanItemGuid(obj.getPlanItemGuid());
                    skuRespDTO.setSkuGuid(obj.getSkuGuid());
                    skuRespDTO.setSalePrice(obj.getSalePrice());
                    skuRespDTO.setMemberPrice(obj.getMemberPrice());
                    skuRespDTO.setAccountingPrice(obj.getAccountingPrice());
                    skuRespDTO.setTakeawayAccountingPrice(obj.getTakeawayAccountingPrice());
                    skuRespDTO.setLinePrice(obj.getLinePrice());
                    if (ObjectUtil.isNotNull(finalSaleCollect.get(obj.getSkuGuid()))) {
                        skuRespDTO.setSalePrice(finalSaleCollect.get(obj.getSkuGuid()).getSalePrice());
                    }
                    if (ObjectUtil.isNotNull(finalMemberCollect.get(obj.getSkuGuid()))) {
                        skuRespDTO.setMemberPrice(finalMemberCollect.get(obj.getSkuGuid()).getMemberPrice());
                    }
                    if (ObjectUtil.isNotNull(finalAccountingCollect.get(obj.getSkuGuid()))) {
                        skuRespDTO.setAccountingPrice(finalAccountingCollect.get(obj.getSkuGuid()).getAccountingPrice());
                    }
                    if (ObjectUtil.isNotNull(finalTakeawayAccountingCollect.get(obj.getSkuGuid()))) {
                        skuRespDTO.setTakeawayAccountingPrice(finalTakeawayAccountingCollect.get(obj.getSkuGuid()).getTakeawayAccountingPrice());
                    }
                    if (ObjectUtil.isNotNull(finalLineCollect.get(obj.getSkuGuid()))) {
                        skuRespDTO.setLinePrice(finalLineCollect.get(obj.getSkuGuid()).getLinePrice());
                    }
                    listSku.add(skuRespDTO);
                });

                itemWebRespDTO.setSkuList(listSku);
                resp.add(itemWebRespDTO);
            }
        }
        return resp;
    }

    private void setItemTypeName(ItemWebRespDTO itemWebRespDTO, PricePlanSkuRespDTO pricePlanSkuRespDTO) {
        if (null == itemWebRespDTO.getTypeName()) {
            itemWebRespDTO.setTypeName(pricePlanSkuRespDTO.getTypeName());
            itemWebRespDTO.setTypeGuid(pricePlanSkuRespDTO.getTypeGuid());
            return;
        }
        if (itemWebRespDTO.getTypeName().equals(pricePlanSkuRespDTO.getTypeName()) && Objects.isNull(itemWebRespDTO.getSort())) {
            // 如果修改分类和原有分类一样, 需要重新设置排序值为之前的排序值
            itemWebRespDTO.setSort(pricePlanSkuRespDTO.getSort());
        }
    }

    private void setItemSort(ItemWebRespDTO itemWebRespDTO, PricePlanSkuRespDTO pricePlanSkuRespDTO) {
        if (ObjectUtil.isNull(itemWebRespDTO.getSort())) {
            if (StringUtils.isEmpty(itemWebRespDTO.getTypeName())) {
                // 没有设置分类，还是使用原有的排序值
                itemWebRespDTO.setSort(pricePlanSkuRespDTO.getSort());
            } else {
                // 产品要求 只要设置了分类，切换分类，排在分类下最后一个
                itemWebRespDTO.setSort(PLAN_ITEM_SORT_LAST_ONE);
            }
        }
    }

    private void buildEnglishIngredientsDesc(Map<String, ItemWebRespDTO> itemMap, Set<String> keyList
            , String itemGuid, List<PlanPriceEnglishIngredientsEditDTO> englishIngredientsDescList) {
        if (CollectionUtil.isNotEmpty(englishIngredientsDescList)) {
            for (PlanPriceEnglishIngredientsEditDTO planPriceEnglishIngredientsDesc : englishIngredientsDescList) {
                String englishIngredientsDesc = planPriceEnglishIngredientsDesc.getEnglishIngredientsDesc();
                planPriceEnglishIngredientsDesc.getPlanPriceList().forEach(obj ->
                {
                    keyList.add(obj.getPlanGuid());
                    ItemWebRespDTO itemWeb = itemMap.get(obj.getPlanGuid());
                    if (ObjectUtil.isNull(itemWeb)) {
                        itemWeb = new ItemWebRespDTO();
                        itemWeb.setItemGuid(itemGuid);
                    }
                    itemWeb.setEnglishIngredientsDesc(englishIngredientsDesc);
                    itemMap.put(obj.getPlanGuid(), itemWeb);
                });
            }
        }
    }

    private void buildEnglishBrief(Map<String, ItemWebRespDTO> itemMap, Set<String> keyList,
                                   String itemGuid, List<PlanPriceEnglishBriefEditDTO> englishBriefList) {
        if (CollectionUtil.isNotEmpty(englishBriefList)) {
            for (PlanPriceEnglishBriefEditDTO planPriceEnglishBrief : englishBriefList) {
                String englishBrief = planPriceEnglishBrief.getEnglishBrief();

                planPriceEnglishBrief.getPlanPriceList().forEach(obj ->
                {
                    keyList.add(obj.getPlanGuid());
                    ItemWebRespDTO itemWeb = itemMap.get(obj.getPlanGuid());
                    if (ObjectUtil.isNull(itemWeb)) {
                        itemWeb = new ItemWebRespDTO();
                        itemWeb.setItemGuid(itemGuid);
                    }
                    itemWeb.setEnglishBrief(englishBrief);
                    itemMap.put(obj.getPlanGuid(), itemWeb);
                });
            }
        }
    }

    private Map<String, List<SkuRespDTO>> buildMemberPrice(Map<String, ItemWebRespDTO> itemMap, Set<String> keyList,
                                                           String itemGuid, List<PlanPriceSaleOrMemberPriceEditDTO> memberPriceList) {
        if (CollectionUtil.isNotEmpty(memberPriceList)) {
            Map<String, List<SkuRespDTO>> skuMemberPriceMap = new HashMap<>();

            for (PlanPriceSaleOrMemberPriceEditDTO planPriceMemberPrice : memberPriceList) {
                List<PlanPriceEditPriceBaseDTO> skuPriceList = planPriceMemberPrice.getSkuPriceList();
                List<SkuRespDTO> list = new ArrayList<>();
                for (PlanPriceEditPriceBaseDTO priceBaseDTO : skuPriceList) {
                    SkuRespDTO skuRespDTO = new SkuRespDTO();
                    skuRespDTO.setMemberPrice(priceBaseDTO.getPrice());
                    skuRespDTO.setSkuGuid(priceBaseDTO.getSkuGuid());
                    skuRespDTO.setPlanItemGuid(itemGuid);
                    list.add(skuRespDTO);
                }
                planPriceMemberPrice.getPlanPriceList().forEach(obj ->
                {
                    keyList.add(obj.getPlanGuid());
                    skuMemberPriceMap.put(obj.getPlanGuid(), list);
                    ItemWebRespDTO itemWeb = itemMap.get(obj.getPlanGuid());
                    if (ObjectUtil.isNull(itemWeb)) {
                        itemWeb = new ItemWebRespDTO();
                        itemWeb.setItemGuid(itemGuid);
                    }
                    itemWeb.setSkuList(list);
                    itemMap.put(obj.getPlanGuid(), itemWeb);
                });
            }
            return skuMemberPriceMap;
        }
        return null;
    }

    private Map<String, List<SkuRespDTO>> buildSalePrice(Map<String, ItemWebRespDTO> itemMap, Set<String> keyList,
                                                         String itemGuid, List<PlanPriceSaleOrMemberPriceEditDTO> salePriceList) {
        if (CollectionUtil.isNotEmpty(salePriceList)) {
            Map<String, List<SkuRespDTO>> skuSalePriceMap = new HashMap<>();
            for (PlanPriceSaleOrMemberPriceEditDTO planPriceSalePrice : salePriceList) {
                List<PlanPriceEditPriceBaseDTO> skuPriceList = planPriceSalePrice.getSkuPriceList();
                List<SkuRespDTO> list = new ArrayList<>();
                for (PlanPriceEditPriceBaseDTO priceBaseDTO : skuPriceList) {
                    SkuRespDTO skuRespDTO = new SkuRespDTO();
                    skuRespDTO.setSalePrice(priceBaseDTO.getPrice());
                    skuRespDTO.setSkuGuid(priceBaseDTO.getSkuGuid());
                    skuRespDTO.setPlanItemGuid(itemGuid);
                    list.add(skuRespDTO);
                }
                planPriceSalePrice.getPlanPriceList().forEach(obj ->
                {
                    keyList.add(obj.getPlanGuid());
                    skuSalePriceMap.put(obj.getPlanGuid(), list);

                    ItemWebRespDTO itemWeb = itemMap.get(obj.getPlanGuid());
                    if (ObjectUtil.isNull(itemWeb)) {
                        itemWeb = new ItemWebRespDTO();
                        itemWeb.setItemGuid(itemGuid);
                    }
                    itemWeb.setSkuList(list);
                    itemMap.put(obj.getPlanGuid(), itemWeb);
                });
            }
            return skuSalePriceMap;
        }
        return null;
    }

    private void buildSaleName(Map<String, ItemWebRespDTO> itemMap, Set<String> keyList,
                               String itemGuid, List<PlanPriceSaleNameEditDTO> saleNameList) {
        if (CollectionUtil.isNotEmpty(saleNameList)) {
            for (PlanPriceSaleNameEditDTO planPriceSaleName : saleNameList) {
                String saleName = planPriceSaleName.getSaleName();
                planPriceSaleName.getPlanPriceList().forEach(obj ->
                {
                    ItemWebRespDTO itemWeb = itemMap.get(obj.getPlanGuid());
                    if (ObjectUtil.isNull(itemWeb)) {
                        itemWeb = new ItemWebRespDTO();
                        itemWeb.setItemGuid(itemGuid);
                    }
                    itemWeb.setPlanItemName(saleName);
                    itemMap.put(obj.getPlanGuid(), itemWeb);
                    keyList.add(obj.getPlanGuid());
                });
            }
        }
    }


    private void buildTypeName(Map<String, ItemWebRespDTO> itemMap, Set<String> keyList,
                               String itemGuid, List<PlanPriceTypeEditDTO> typeNameList,
                               Map<String, Set<String>> itemTypeNameMap) {
        if (CollectionUtils.isEmpty(typeNameList)) {
            return;
        }
        for (PlanPriceTypeEditDTO planPriceEditTypeName : typeNameList) {
            String typeName = planPriceEditTypeName.getTypeName();
            planPriceEditTypeName.getPlanPriceList().forEach(obj -> {
                ItemWebRespDTO itemWeb = itemMap.get(obj.getPlanGuid());
                if (ObjectUtil.isNull(itemWeb)) {
                    itemWeb = new ItemWebRespDTO();
                    itemWeb.setItemGuid(itemGuid);
                }
                // 分类名称map
                Set<String> typeNameSet = itemTypeNameMap.getOrDefault(obj.getPlanGuid(), Sets.newHashSet());
                typeNameSet.add(typeName);
                itemTypeNameMap.put(obj.getPlanGuid(), typeNameSet);
                itemWeb.setTypeName(typeName);
                itemMap.put(obj.getPlanGuid(), itemWeb);
                keyList.add(obj.getPlanGuid());
            });
        }
    }

    private void buildDescription(Map<String, ItemWebRespDTO> itemMap, Set<String> keyList, String itemGuid,
                                  List<PlanPriceDescriptionEditDTO> descriptionList) {
        if (CollectionUtil.isNotEmpty(descriptionList)) {
            for (PlanPriceDescriptionEditDTO planPriceDescription : descriptionList) {

                String finalDescription = planPriceDescription.getDescription();
                planPriceDescription.getPlanPriceList().forEach(obj ->
                {
                    ItemWebRespDTO itemWeb = itemMap.get(obj.getPlanGuid());
                    if (ObjectUtil.isNull(itemWeb)) {
                        itemWeb = new ItemWebRespDTO();
                        itemWeb.setItemGuid(itemGuid);
                    }
                    itemWeb.setDescription(finalDescription);
                    itemWeb.setPictureUrl(null);
                    itemWeb.setBigPictureUrl(null);
                    itemWeb.setDetailBigPictureUrl(null);
                    itemMap.put(obj.getPlanGuid(), itemWeb);
                    keyList.add(obj.getPlanGuid());
                });
            }
        }
    }

    private void buildSort(Map<String, ItemWebRespDTO> itemMap, Set<String> keyList, String itemGuid,
                           List<PlanPriceSortEditDTO> sortList) {
        if (CollectionUtils.isEmpty(sortList)) {
            return;
        }
        for (PlanPriceSortEditDTO planPriceSortEditDTO : sortList) {
            final Integer finalSort = planPriceSortEditDTO.getSort();
            planPriceSortEditDTO.getPlanPriceList().forEach(obj ->
            {
                ItemWebRespDTO itemWeb = itemMap.get(obj.getPlanGuid());
                if (ObjectUtil.isNull(itemWeb)) {
                    itemWeb = new ItemWebRespDTO();
                    itemWeb.setItemGuid(itemGuid);
                }
                itemWeb.setSort(finalSort);
                itemMap.put(obj.getPlanGuid(), itemWeb);
                keyList.add(obj.getPlanGuid());
            });
        }
    }

    private void buildPicture(Map<String, ItemWebRespDTO> itemMap, Set<String> keyList,
                              String itemGuid, List<PlanPricePictureEditDTO> pictureList) {
        if (CollectionUtil.isNotEmpty(pictureList)) {
            for (PlanPricePictureEditDTO planPricePictureEditDTO : pictureList) {
                planPricePictureEditDTO.getPlanPriceList().forEach(obj -> {
                    ItemWebRespDTO itemWeb = itemMap.get(obj.getPlanGuid());
                    if (ObjectUtil.isNull(itemWeb)) {
                        itemWeb = new ItemWebRespDTO();
                        itemWeb.setItemGuid(itemGuid);
                    }
                    itemWeb.setPictureUrl(planPricePictureEditDTO.getPictureUrl());
                    itemWeb.setBigPictureUrl(planPricePictureEditDTO.getBigPictureUrl());
                    itemWeb.setDetailBigPictureUrl(planPricePictureEditDTO.getDetailBigPictureUrl());
                    itemMap.put(obj.getPlanGuid(), itemWeb);
                    keyList.add(obj.getPlanGuid());
                });
            }
        }
    }

    /**
     * 批量下架保存商品菜谱信息
     *
     * @param request 批量下架保存入参实体
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Boolean saveBatchSoldOutPlanItem(PlanPriceSoldOutReqDTO request) {

        List<String> planGuidList = request.getPlanGuidList();
        List<String> toSoldOutSkuGuidList = request.getSkuGuidList();
        Integer pushType = request.getPushType();
        LocalDateTime pushDate = request.getPushDate();
        Integer soldOutType = request.getSoldOutType();
        checkParam(planGuidList, toSoldOutSkuGuidList, pushType, pushDate, soldOutType);

        // 查询要更改的方案信息
        List<PricePlanDO> pricePlanDOList = pricePlanMapper.selectBatchIds(planGuidList);
        if (CollectionUtils.isEmpty(pricePlanDOList)) {
            log.info("未查询到方案信息 planGuidList={}", planGuidList);
            throw new BusinessException("未查询到方案信息");
        }
        List<PricePlanReqDTO> planReqDTOList = MapStructUtils.INSTANCE.planDOList2PlanReqDTOList(pricePlanDOList);

        // 要更改方案绑定的门店
        List<PricePlanStoreDO> planStoreDOList = planStoreMapper.selectList(new LambdaQueryWrapper<PricePlanStoreDO>()
                .in(PricePlanStoreDO::getPlanGuid, planGuidList));
        Map<String, List<String>> planStoreGuidMap = null;
        if (CollectionUtils.isEmpty(planStoreDOList)) {
            log.info("未查询到方案所绑定信息 planGuidList={}", planGuidList);
        } else {
            planStoreGuidMap = planStoreDOList.stream()
                    .collect(Collectors.groupingBy(PricePlanStoreDO::getPlanGuid,
                            Collectors.mapping(PricePlanStoreDO::getStoreGuid, Collectors.toList())));
        }

        // 完善菜谱批量下架保存入参
        List<PricePlanReqDTO> toSavePlanList = new ArrayList<>();
        List<PricePlanItemDO> checkItemList = planItemService.list(new LambdaQueryWrapper<PricePlanItemDO>()
                .in(PricePlanItemDO::getPlanGuid, planGuidList)
                .ne(PricePlanItemDO::getIsSoldOut, ItemStateEnum.IMMEDIATELY_DELETE.getCode())
        );
        Map<String, List<PricePlanItemDO>> groupByPlanGuidMap = checkItemList.stream()
                .collect(Collectors.groupingBy(PricePlanItemDO::getPlanGuid));
        boolean isCheck = false;
        StringBuilder msg = new StringBuilder();
        msg.append("下架商品后");

        // 传入的每个方案直接循环获取商品，如果不包含要下架的商品则跳过不处理，要下架的商品修改状态.
        for (PricePlanReqDTO plan : planReqDTOList) {
            String planGuid = plan.getGuid();

            // 查询方案下所有的商品
            ItemQueryReqDTO queryDTO = new ItemQueryReqDTO();
            queryDTO.setPlanGuid(planGuid);
            queryDTO.setCurrentPage(1L);
            queryDTO.setPageSize(1000L);
            com.holderzone.framework.util.Page<ItemWebRespDTO> itemWebPage = planItemService.queryPlanItemsByPlan(queryDTO);
            List<ItemWebRespDTO> itemWebRespDTOList = itemWebPage.getData();

            // 不包含要下架的商品则跳过不处理
            List<String> planSkuGuidList = itemWebRespDTOList.stream()
                    .flatMap(item -> item.getSkuList().stream().map(SkuRespDTO::getSkuGuid))
                    .collect(Collectors.toList());
            planSkuGuidList.retainAll(toSoldOutSkuGuidList);
            if (CollectionUtils.isEmpty(planSkuGuidList)) {
                log.info("此菜谱不存在此商品无法下架，planGuid:{}", planGuid);
                continue;
            }

            // 要下架的商品修改状态为下架
            setStateDown(itemWebRespDTOList, toSoldOutSkuGuidList, soldOutType);

            plan.setPlanType(TO_DATA_BASE);
            plan.setPushType(pushType);
            plan.setBatchEdit(Boolean.TRUE);
            if (Objects.equals(PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode(), plan.getStatus()) &&
                    Objects.equals(1, pushType)) {
                plan.setStatus(PricePlanStatusEnum.USING.getCode());
            }
            if (!ObjectUtil.equal(plan.getStatus(), PricePlanStatusEnum.NOT_ENABLED.getCode())) {
                plan.setPushDate(pushDate);
            }

            // 门店guid集合
            checkStore(plan, planStoreGuidMap);

            // 编辑时商品数量校验
            isCheck = buildCheck(plan, itemWebRespDTOList, groupByPlanGuidMap, toSoldOutSkuGuidList, msg, isCheck);
            // 方案保存商品信息
            plan.setItemWebRespDTOList(itemWebRespDTOList);
            // 不改分类，所以不传
            toSavePlanList.add(plan);

        }

        if (isCheck) {
            String checkMsg = msg.substring(0, msg.length() - 1);
            throw new BusinessException(checkMsg + "菜谱商品将为空，请清除对应菜谱再保存");
        }

        if (CollectionUtils.isNotEmpty(toSavePlanList)) {
            asyncSavePlanLimit(toSavePlanList);
        }

        return true;
    }

    private void checkStore(PricePlanReqDTO plan, Map<String, List<String>> planStoreGuidMap) {
        if (null != planStoreGuidMap) {
            List<String> storeGuidList = planStoreGuidMap.get(plan.getGuid());
            if (CollectionUtils.isEmpty(storeGuidList)) {
                log.info("方案未绑定门店 planGuid={}", plan.getGuid());
            } else {
                plan.setStoreGuidList(storeGuidList);
            }
        }
    }

    private boolean buildCheck(PricePlanReqDTO plan, List<ItemWebRespDTO> itemWebRespDTOList,
                               Map<String, List<PricePlanItemDO>> groupByPlanGuidMap,
                               List<String> toSoldOutSkuGuidList, StringBuilder msg, boolean isCheck) {
        if (CollectionUtils.isNotEmpty(itemWebRespDTOList)) {
            // 现有商品
            List<PricePlanItemDO> planItemList = groupByPlanGuidMap.get(plan.getGuid());
            List<PricePlanItemDO> filterItemList = planItemList.stream()
                    .filter(e -> !toSoldOutSkuGuidList.contains(e.getSkuGuid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterItemList)) {
                msg.append(plan.getName()).append("、");
                isCheck = true;
            }
        }
        return isCheck;
    }

    private void setStateDown(List<ItemWebRespDTO> itemWebRespDTOList, List<String> toSoldOutSkuGuidList, Integer soldOutType) {
        for (ItemWebRespDTO item : itemWebRespDTOList) {
            if (Objects.equals(ItemTypeEnum.MULTI_SPEC.getTypeCode(), item.getItemType())) {
                List<SkuRespDTO> skuList = item.getSkuList();
                for (SkuRespDTO sku : skuList) {
                    if (toSoldOutSkuGuidList.contains(sku.getSkuGuid())) {
                        sku.setIsSoldOut(ItemStateEnum.IMMEDIATELY_DELETE.getCode());
                    }
                }
            } else {
                if (toSoldOutSkuGuidList.contains(item.getSkuList().get(0).getSkuGuid())) {
                    item.setIsSoldOut(soldOutType);
                }
            }
        }
    }

    /**
     * 方案延时补偿
     *
     * @param enterpriseGuidList 需要执行的企业guid
     */
    @Override
    public void delayedCompensate(List<String> enterpriseGuidList) {
        CountDownLatch delayedCompensateCountDownLatch = new CountDownLatch(enterpriseGuidList.size());
        ExecutorService delayedCompensateExecutor = Executors.newFixedThreadPool(enterpriseGuidList.size());
        for (String enterpriseGuid : enterpriseGuidList) {
            delayedCompensateExecutor.execute(() -> {
                try {
                    log.info("[方案延时补偿]开始,enterpriseGuid={}", enterpriseGuid);
                    dynamicHelper.changeDatasource(enterpriseGuid);
                    UserContextUtils.putErp(enterpriseGuid);
                    handleUpcomingEffectivePlan();
                    handlePeriodEffectivePlan();
                    log.info("[方案延时补偿]结束,enterpriseGuid={}", enterpriseGuid);
                } catch (Exception e) {
                    log.error("方案延时补偿失败,enterpriseGuid={},e={}", enterpriseGuid, e.getMessage());
                } finally {
                    delayedCompensateCountDownLatch.countDown();
                }
            });
        }
        try {
            delayedCompensateCountDownLatch.await();
        } catch (Exception exception) {
            exception.printStackTrace();
        } finally {
            delayedCompensateExecutor.shutdown();
        }
    }

    /**
     * 处理即将生效菜谱延迟
     */
    private void handleUpcomingEffectivePlan() {
        // 即将生效且时间已经到了的菜谱
        List<PricePlanDO> toEffectivePlan = this.list(new LambdaQueryWrapper<PricePlanDO>()
                .eq(PricePlanDO::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(PricePlanDO::getStatus, PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode())
                .le(PricePlanDO::getInstantlyEffectiveTime, LocalDateTime.now())
        );
        if (CollectionUtils.isEmpty(toEffectivePlan)) {
            return;
        }
        log.info("[处理即将生效菜谱延迟],即将生效且时间已经到了的菜谱={}", toEffectivePlan.stream().map(PricePlanDO::getGuid).collect(Collectors.toList()));
        // 删除缓存，立即生效
        toEffectivePlan.forEach(plan -> {
            planPriceDisabledCache(plan);

            // 全时段生效
            log.info("[处理即将生效菜谱延迟],特殊时段生效,planGuid={}", plan.getGuid());
            timeExecution(plan.getGuid());
            List<String> storeGuidList = planStoreMapper.getPlanStoreGuidList(plan.getGuid(), null);
            if (CollectionUtils.isEmpty(storeGuidList)) {
                log.warn("[处理即将生效菜谱延迟]，planGuid={}，没有配置门店，不推送消息", plan.getGuid());
                return;
            }
            String contentMessage = "门店商品已更新，请重新登录当前设备同步信息";
            eventPushHelper.pushMsgToAndriod(storeGuidList, contentMessage);

            // 特殊时段生效
            if (1 == plan.getSellTimeType()) {
                log.info("[处理即将生效菜谱延迟],特殊时段生效,planGuid={}", plan.getGuid());
                SyncPricePlanMessageDTO periodTimeMessageDTO = new SyncPricePlanMessageDTO();
                periodTimeMessageDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid())
                        .setBrandGuid(plan.getBrandGuid())
                        .setPricePlanGuid(plan.getGuid())
                        .setMessageType(PLAN_PUSH_ADD);
                commonService.sendPricePlanToMQ(periodTimeMessageDTO);
            }
        });
    }

    /**
     * 处理特殊时段菜谱切换延迟
     */
    private void handlePeriodEffectivePlan() {
        // 特殊时段且已启用
        List<PricePlanDO> periodEffectivePlan = this.list(new LambdaQueryWrapper<PricePlanDO>()
                .eq(PricePlanDO::getIsDelete, BooleanEnum.FALSE.getCode())
                .eq(PricePlanDO::getStatus, PricePlanStatusEnum.USING.getCode())
                .eq(PricePlanDO::getSellTimeType, 1));
        if (CollectionUtils.isEmpty(periodEffectivePlan)) {
            return;
        }
        log.info("[处理特殊时段菜谱切换延迟],planGuidList={}", periodEffectivePlan.stream().map(PricePlanDO::getGuid).collect(Collectors.toList()));
        periodEffectivePlan.forEach(plan -> {
            boolean beyondToday = plan.getStartTime().isAfter(plan.getEndTime());
            boolean firstDay = LocalTime.now().isAfter(plan.getStartTime()) && LocalTime.now().isBefore(LocalTime.MAX);
            boolean secondDay = LocalTime.now().isAfter(LocalTime.MIN) && LocalTime.now().isBefore(plan.getEndTime());
            boolean inRangeDay = LocalTime.now().isAfter(plan.getStartTime()) && LocalTime.now().isBefore(plan.getEndTime());
            // 跨天
            if (beyondToday) {
                if (firstDay || secondDay) {
                    // 生效中
                    log.info("[处理特殊时段菜谱切换延迟],跨天生效中,planGuid={}", plan.getGuid());
                    compensate(plan, PLAN_PUSH_ADD);
                }

                if (inRangeDay) {
                    // 未生效
                    log.info("[处理特殊时段菜谱切换延迟],跨天未生效,planGuid={}", plan.getGuid());
                    compensate(plan, PLAN_PUSH_DELETE);
                }
                return;
            }

            // 不跨天
            if (inRangeDay) {
                // 生效中
                log.info("[处理特殊时段菜谱切换延迟],不跨天生效中,planGuid={}", plan.getGuid());
                compensate(plan, PLAN_PUSH_ADD);
                return;
            }
            // 未生效
            log.info("[处理特殊时段菜谱切换延迟],不跨天未生效,planGuid={}", plan.getGuid());
            compensate(plan, PLAN_PUSH_DELETE);
        });
    }

    /**
     * 补偿，组装开始点/结束点key
     * 此时开始点/结束点不应该存在于缓存
     *
     * @param plan     方案
     * @param pushType 推送类型
     */
    private void compensate(PricePlanDO plan, int pushType) {
        SyncPricePlanMessageDTO messageDTO = new SyncPricePlanMessageDTO();
        messageDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid())
                .setBrandGuid(plan.getBrandGuid())
                .setPricePlanGuid(plan.getGuid())
                .setMessageType(pushType);
        String json = JacksonUtils.writeValueAsString(messageDTO);
        String redisKey = KEY_PREFIX + json;
        Object cache = cacheService.getCacheMap(redisKey);
        log.info("补偿缓存查询，redisKey={},cache={}", redisKey, JacksonUtils.writeValueAsString(cache));
        if (!ObjectUtils.isEmpty(cache)) {
            log.info("[处理特殊时段菜谱切换延迟],补偿,planGuid={}，pushType={}", plan.getGuid(), pushType);
            try {
                cacheService.removeCacheMap(redisKey);
            } catch (Exception e) {
                log.error("", e);
            }
            commonService.sendPricePlanToMQ(messageDTO);
        }
    }

    /**
     * 检查参数
     *
     * @param planGuidList 方案GuidList
     * @param skuGuidList  商品规格GuidList
     * @param pushType     推送类型：1-立即推送，2-按时间推送
     * @param pushDate     推送时间
     * @param soldOutType  下架方式：1-售完下架，2-立即下架（直接删除）
     */
    private void checkParam(List<String> planGuidList, List<String> skuGuidList, Integer pushType,
                            LocalDateTime pushDate, Integer soldOutType) {
        if (ObjectUtils.isEmpty(pushType)) {
            throw new BusinessException("推送类型不能为空");
        }
        if (CollectionUtils.isEmpty(skuGuidList)) {
            throw new BusinessException("菜品不能为空");
        }
        if (CollectionUtils.isEmpty(planGuidList)) {
            throw new BusinessException("方案不能为空");
        }
        if (Objects.equals(2, pushType) && ObjectUtils.isEmpty(pushDate)) {
            throw new BusinessException("推送时间不能为空");
        }
        if (ObjectUtils.isEmpty(soldOutType)) {
            throw new BusinessException("下架方式不能为空");
        }
    }

    /**
     * 构建批量编辑菜谱分类的入参
     */
    private List<PricePlanReqDTO> transferBatchEditPlanPriceTypeData(PlanPriceEditReqDTO req) {
        List<PlanPriceEditItemDTO> planPriceList = req.getPlanPriceList();
        Integer pushType = req.getPushType();
        Map<String, List<TypePricePlanReqDTO>> groupByPlanGuidMap = Maps.newHashMap();
        // 构建 groupByPlanGuidMap
        for (PlanPriceEditItemDTO planPriceEditItemDTO : planPriceList) {
            appendTypePricePlanReqMap(groupByPlanGuidMap, planPriceEditItemDTO);
        }
        // 查询菜谱信息
        List<PricePlanDO> planList = new ArrayList<>(listByIds(groupByPlanGuidMap.keySet()));
        Map<String, PricePlanDO> planMap = planList.stream()
                .collect(Collectors.toMap(PricePlanDO::getGuid, Function.identity(), (key1, key2) -> key1));
        Map<String, List<String>> storeListMap = planStoreMapper.getPlanStoreListByPlanGuidList(new ArrayList<>(groupByPlanGuidMap.keySet()))
                .stream().collect(Collectors.groupingBy(PricePlanStoreInfoDTO::getPlanGuid,
                        Collectors.mapping(PricePlanStoreInfoDTO::getStoreGuid, Collectors.toList())));
        List<PricePlanReqDTO> pricePlanList = Lists.newArrayList();
        groupByPlanGuidMap.forEach((planGuid, typeList) -> {
            PricePlanDO pricePlanDO = planMap.get(planGuid);
            if (Objects.isNull(pricePlanDO)) {
                return;
            }
            PricePlanReqDTO pricePlanReqDTO = new PricePlanReqDTO();
            pricePlanReqDTO.setGuid(planGuid);
            pricePlanReqDTO.setPushType(pushType);
            pricePlanReqDTO.setStatus(PricePlanStatusEnum.USING.getCode());
            pricePlanReqDTO.setPushDate(req.getPushDate());
            pricePlanReqDTO.setStoreGuidList(storeListMap.get(planGuid));
            pricePlanReqDTO.setSellTimeType(pricePlanDO.getSellTimeType());
            pricePlanReqDTO.setName(pricePlanDO.getName());
            pricePlanReqDTO.setStartTime(pricePlanDO.getStartTime());
            pricePlanReqDTO.setEndTime(pricePlanDO.getEndTime());
            pricePlanReqDTO.setBrandGuid(pricePlanDO.getBrandGuid());
            pricePlanReqDTO.setTypeList(typeList);
            pricePlanReqDTO.setItemWebRespDTOList(Lists.newArrayList());
            pricePlanReqDTO.setBatchEdit(Boolean.TRUE);
            pricePlanList.add(pricePlanReqDTO);
        });
        return pricePlanList;
    }

    private void appendTypePricePlanReqMap(Map<String, List<TypePricePlanReqDTO>> groupByPlanGuidMap,
                                           PlanPriceEditItemDTO planPriceEditItemDTO) {
        // 分类名称
        String typeName = planPriceEditItemDTO.getTypeName();
        // 修改的分类名称
        List<PlanPriceTypeEditDTO> typeList = planPriceEditItemDTO.getTypeList();
        if (CollectionUtils.isNotEmpty(typeList)) {
            for (PlanPriceTypeEditDTO typeEditDTO : typeList) {
                for (PlanPriceEditDTO typeEditPlanDTO : typeEditDTO.getPlanPriceList()) {
                    List<TypePricePlanReqDTO> typePricePlanReqDTOList = groupByPlanGuidMap.getOrDefault(typeEditPlanDTO.getPlanGuid(), Lists.newArrayList());
                    TypePricePlanReqDTO typePricePlanReqDTO = typePricePlanReqDTOList.stream()
                            .filter(e -> typeName.equals(e.getName()))
                            .findFirst()
                            .orElse(new TypePricePlanReqDTO());
                    typePricePlanReqDTO.setName(typeName);
                    typePricePlanReqDTO.setUpdateAfterName(typeEditDTO.getTypeName());
                    typePricePlanReqDTO.setIsDelete(0);
                    typePricePlanReqDTOList.add(typePricePlanReqDTO);
                    groupByPlanGuidMap.put(typeEditPlanDTO.getPlanGuid(), typePricePlanReqDTOList);
                }
            }
        }
        // 修改的分类排序名称
        List<PlanPriceTypeEditDTO> typeSortList = planPriceEditItemDTO.getTypeSortList();
        if (CollectionUtils.isNotEmpty(typeSortList)) {
            for (PlanPriceTypeEditDTO typeSortEditDTO : typeSortList) {
                for (PlanPriceEditDTO editPlanDTO : typeSortEditDTO.getPlanPriceList()) {
                    List<TypePricePlanReqDTO> typePricePlanReqDTOList = groupByPlanGuidMap.getOrDefault(editPlanDTO.getPlanGuid(), Lists.newArrayList());
                    TypePricePlanReqDTO typePricePlanReqDTO = typePricePlanReqDTOList.stream()
                            .filter(e -> typeName.equals(e.getName()))
                            .findFirst()
                            .orElse(new TypePricePlanReqDTO());
                    typePricePlanReqDTO.setName(typeName);
                    typePricePlanReqDTO.setSort(typeSortEditDTO.getSort());
                    typePricePlanReqDTO.setIsDelete(0);
                    typePricePlanReqDTOList.add(typePricePlanReqDTO);
                    groupByPlanGuidMap.put(editPlanDTO.getPlanGuid(), typePricePlanReqDTOList);
                }
            }
        }
    }

}
