package com.holderzone.saas.store.item.service.rpc;

import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessMessageService
 * @date 2018/09/27 11:44
 * @description //TODO
 * @program holder-saas-store-takeaway
 */
@Component
@FeignClient(value = "holder-saas-cloud-enterprise", fallbackFactory = CloudEnterpriseFeignClient.ServiceFallBack.class)
public interface CloudEnterpriseFeignClient {

    /**
     * 得到所有的企业
     *
     * @return List<EnterpriseDTO>
     */
    @GetMapping("/enterprise/all")
    List<EnterpriseDTO> getAllEntPrise();

    /**
     * 根据门店GUID、员工GUID查询营业日起始时间
     *
     * @return
     */
    @GetMapping("/enterprise/hasEnterprise")
    Boolean hasEnterprise(@RequestParam("enterpriseGuid") String enterpriseGuid);

    /**
     * 根据企业guid查询企业经营模式
     *
     * @param enterpriseGuid 企业guid
     * @return SINGLE, 单店 CHAIN,连锁 PLATFORM平台
     */
    @GetMapping("/enterprise/management_model/{enterpriseGuid}")
    String queryManagementModel(@PathVariable(value = "enterpriseGuid") String enterpriseGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<CloudEnterpriseFeignClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}";

        @Override
        public CloudEnterpriseFeignClient create(Throwable throwable) {
            return new CloudEnterpriseFeignClient() {

                /**
                 * 得到所有的企业
                 * @return List<EnterpriseDTO>
                 */
                @Override
                public List<EnterpriseDTO> getAllEntPrise() {
                    log.error(HYSTRIX_PATTERN, "/enterprise/all", throwable);
                    return Collections.emptyList();
                }

                /**
                 * 根据门店GUID、员工GUID查询营业日起始时间
                 *
                 * @param enterpriseGuid
                 * @return
                 */
                @Override
                public Boolean hasEnterprise(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "/enterprise/hasEnterprise", enterpriseGuid, throwable);
                    return Boolean.TRUE;
                }

                @Override
                public String queryManagementModel(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "/management_model/{enterpriseGuid}", enterpriseGuid, throwable);
                    return null;
                }
            };
        }
    }
}