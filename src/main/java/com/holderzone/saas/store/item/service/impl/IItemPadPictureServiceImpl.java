package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.item.entity.domain.PadPictureDO;
import com.holderzone.saas.store.item.mapper.PadPictureMapper;
import com.holderzone.saas.store.item.service.IItemPadPictureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description pad图片Service实现
 * @date 2021/10/15 14:42
 * @className: IItemPadPictureServiceImpl
 */
@Service
@Slf4j
public class IItemPadPictureServiceImpl extends ServiceImpl<PadPictureMapper, PadPictureDO> implements IItemPadPictureService {
}
