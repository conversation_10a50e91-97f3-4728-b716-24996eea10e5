package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.item.dto.PushRecordStatusUpdateReqDTO;
import com.holderzone.saas.store.item.entity.domain.PricePlanPushRecordDO;

import java.util.List;

public interface IPricePlanPushRecordService extends IService<PricePlanPushRecordDO> {

    /**
     * 生成方案推送记录，总条数=门店数*商品数
     * @param planGuid 方案guid
     * @return 推送记录
     */
    List<PricePlanPushRecordDO> getPushRecords(String planGuid);

    /**
     * 更新方案推送记录状态
     * @param reqDTO PushRecordStatusUpdateReqDTO
     */
    void updatePushStatus(PushRecordStatusUpdateReqDTO reqDTO);

    /**
     * 删除方案推送记录
     * @param planGuid 方案guid
     */
    void deletePushRecords(String planGuid);
}
