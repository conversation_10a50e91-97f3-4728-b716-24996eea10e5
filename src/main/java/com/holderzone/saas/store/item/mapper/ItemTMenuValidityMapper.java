package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.item.entity.domain.ItemTMenuValidityDO;
import com.holderzone.saas.store.item.entity.query.ItemTemplateExecuteTimeQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 商品模拟-菜单-执行有效期 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
@Component
public interface ItemTMenuValidityMapper extends BaseMapper<ItemTMenuValidityDO> {

    /**
     * 根据传入商品模板guid查询有效期
     * @param guid 商品模板guid
     * @param menuGuid 菜单guid
     * @return 对应的执行时间集合
     */
    List<ItemTMenuValidityDO> getMenuValiditys(@Param("guid") String guid, @Param("periodicMode") Integer periodicMode, @Param("menuGuid") String menuGuid);

    /**
     * 商品模板-菜单执行时间-安卓同步菜单执行时间
     * @param request
     * @return
     */
    List<ItemTemplateExecuteTimeQuery> getTimeAndTypeForSyn(@Param("dto") SingleDataDTO request);

    /**
     * 获取当前时间门店模板菜单执行时间
     * @param storeGuid
     * @return
     */
    List<ItemTemplateExecuteTimeQuery> getNowMenuExecuteTimes(@Param("guid") String storeGuid);
}
