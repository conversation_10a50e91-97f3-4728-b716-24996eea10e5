package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.item.entity.domain.RTypeAttrDO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <p>
 * 分类与属性值关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
public interface IRTypeAttrService extends IService<RTypeAttrDO> {

    /**
     * 通过属性值guid获取绑定的分类guid集合
     *
     * @param attrGuid 属性guid
     * @return list
     */
    List<String> listTypeGuidByAttr(String attrGuid);

    /**
     * 通过属性值guid删除与分类的绑定关系
     *
     * @param attrGuid 属性值guid
     * @return Integer
     */
    Integer removeByAttr(String attrGuid);

    /**
     * 删除属性值与分类的绑定关系
     *
     * @param typeGuidList 分类guidList
     * @param attrGuid     属性值guid
     * @return Integer
     */
    Integer removeBind(@NotEmpty List<String> typeGuidList, @NotBlank String attrGuid);

    /**
     * 绑定关系
     *
     * @param typeGuidList 类型guidList
     * @param attrGuid     属性值guid
     * @return Boolean
     */
    Boolean save(@NotEmpty List<String> typeGuidList, @NotBlank String attrGuid);

    /**
     * 删除属性与商品类型的绑定关系
     *
     * @param attrGuid 属性值guid
     */
    void deleteByAttr(String attrGuid);
}
