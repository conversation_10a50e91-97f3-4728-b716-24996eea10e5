package com.holderzone.saas.store.item.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 价格方案状态枚举
 */
@Getter
@AllArgsConstructor
public enum PricePlanStatusEnum {

    /**
     * 未启用（新建后未推送到门店）
     */
    NOT_USE(0, "未启用"),

    /**
     * 已启用（推送到门店）
     */
    USING(1, "已启用"),

    /**
     * 已禁用
     */
    NOT_ENABLED(2, "暂不启用"),

    /**
     * 永久停用
     */
    LASTING_DISABLE(3, "永久停用"),

    /**
     * 即将启用
     */
    RIGHT_AWAY_DISABLE(4, "即将启用"),
    ;

    private final int code;

    private final String desc;

    public static String getDesc(int code) {
        for (PricePlanStatusEnum e : PricePlanStatusEnum.values()) {
            if (e.getCode() == code) {
                return e.getDesc();
            }
        }
        return "未知状态";
    }
}
