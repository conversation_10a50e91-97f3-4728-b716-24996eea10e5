package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.ItemQueryReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemRetailSortReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemSortReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.enums.item.TagEnum;
import com.holderzone.saas.store.item.entity.MPPage;
import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import com.holderzone.saas.store.item.entity.domain.TypeDO;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.mapper.ItemMapper;
import com.holderzone.saas.store.item.service.IRetailItemService;
import com.holderzone.saas.store.item.service.ISkuService;
import com.holderzone.saas.store.item.service.ITypeService;
import com.holderzone.saas.store.item.util.MapStructUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.holderzone.saas.store.item.constant.Constant.SYSTEM_ERROR;

/**
 * <AUTHOR>
 * @version 1.0
 * @className IRetailItemServiceImpl
 * @date 2019/10/26 下午2:30
 * @description //零售商品Service实现
 * @program holder-saas-store-item
 */

@Service
@Slf4j
public class IRetailItemServiceImpl extends ServiceImpl<ItemMapper, ItemDO> implements IRetailItemService {

    private ISkuService skuService;

    private ITypeService typeService;

    private ItemHelper itemHelper;

    public IRetailItemServiceImpl(ISkuService skuService, ITypeService typeService, ItemHelper itemHelper) {
        this.skuService = skuService;
        this.typeService = typeService;
        this.itemHelper = itemHelper;
    }

    @Override
    public Page<ItemWebRespDTO> selectItemListForWeb(ItemQueryReqDTO itemQueryReqDTO) {
        Page<ItemWebRespDTO> itemWebRespDTOPage = new Page<>();
        itemWebRespDTOPage.setData(new ArrayList<>());
        itemWebRespDTOPage.setPageSize(itemQueryReqDTO.getPageSize());
        itemWebRespDTOPage.setCurrentPage(itemQueryReqDTO.getCurrentPage());
        itemWebRespDTOPage.setTotalCount(0L);
        // 如果上架的值是-1,则不过滤是否上架
        if (Integer.valueOf("-1").equals(itemQueryReqDTO.getIsRack())) {
            itemQueryReqDTO.setIsRack(null);
        }
        itemQueryReqDTO.setBrandGuid(null);
        // 带上架条件初筛的规格集合
        List<SkuDO> skuDOList = skuService.list(
                new LambdaQueryWrapper<SkuDO>()
                        .eq(itemQueryReqDTO.getIsRack() != null, SkuDO::getIsRack, itemQueryReqDTO.getIsRack())
                        .eq(!StringUtils.isEmpty(itemQueryReqDTO.getStoreGuid()), SkuDO::getStoreGuid, itemQueryReqDTO.getStoreGuid())
                        .eq(!StringUtils.isEmpty(itemQueryReqDTO.getBrandGuid()), SkuDO::getBrandGuid, itemQueryReqDTO.getBrandGuid()));
        if (CollectionUtils.isEmpty(skuDOList)) {
            return itemWebRespDTOPage;
        }
        // 符合上下架过滤条件的商品GUID
        Set<String> withRackItemGuidList = skuDOList.stream().map(SkuDO::getItemGuid).collect(Collectors.toSet());
        MPPage<ItemDO> itemDOIPageQuery = new MPPage<>(itemQueryReqDTO.getCurrentPage(), itemQueryReqDTO.getPageSize());
        IPage<ItemDO> itemDOIPage = this.page(itemDOIPageQuery,
                new LambdaQueryWrapper<ItemDO>()
                        .in(ItemDO::getGuid, withRackItemGuidList)
                        .eq(!StringUtils.isEmpty(itemQueryReqDTO.getStoreGuid()), ItemDO::getStoreGuid, itemQueryReqDTO.getStoreGuid())
                        .eq(!StringUtils.isEmpty(itemQueryReqDTO.getBrandGuid()), ItemDO::getBrandGuid, itemQueryReqDTO.getBrandGuid())
                        .eq(!StringUtils.isEmpty(itemQueryReqDTO.getTypeGuid()), ItemDO::getTypeGuid, itemQueryReqDTO.getTypeGuid())
                        .like(!StringUtils.isEmpty(itemQueryReqDTO.getName()), ItemDO::getName, itemQueryReqDTO.getName())
                        .orderByDesc(ItemDO::getGmtCreate));
        List<ItemDO> itemDOS = itemDOIPage.getRecords();
        List<String> itemGuidList = itemDOS
                .stream()
                .map(ItemDO::getGuid)
                .collect(Collectors.toList());
        List<String> typeGuidList = itemDOS
                .stream()
                .map(ItemDO::getTypeGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(typeGuidList)) {
            return itemWebRespDTOPage;
        }
        List<TypeDO> typeDOS = typeService.list(
                new LambdaQueryWrapper<TypeDO>()
                        .in(TypeDO::getGuid, typeGuidList));
        Map<String, String> typeCollents = typeDOS.stream().collect(Collectors
                .toMap(TypeDO::getGuid, TypeDO::getName));
        itemWebRespDTOPage = MapStructUtils.INSTANCE.itemDOIPage2itemWebRespDTOPage(itemDOIPage);
        itemWebRespDTOPage
                .getData()
                .forEach(itemWebRespDTO -> itemWebRespDTO.setTypeName(typeCollents.get(itemWebRespDTO.getTypeGuid())));
        // 页面上展示的商品集合
        List<ItemWebRespDTO> itemWebRespDTOList = itemWebRespDTOPage.getData();
        if (CollectionUtils.isEmpty(itemWebRespDTOList)) {
            return itemWebRespDTOPage;
        }
        // 列表商品所有的规格集合
        skuDOList.removeIf(skuDO -> !itemGuidList.contains(skuDO.getItemGuid()));

        // 被当前商品集合推送至门店的规格集合
        List<SkuDO> pushedSkuList = new ArrayList<>();
        // 从门店库进入
        if (!StringUtils.isEmpty(itemQueryReqDTO.getBrandGuid())) {
            return itemWebRespDTOPage;
        }
        // 获取上架门店数
        // 品牌库的列表商品的规格GUID集合
        Set<String> skuGuidSet = skuDOList
                .stream()
                .map(SkuDO::getGuid)
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(skuGuidSet)) {
            List<SkuDO> storeSkuList = skuService.list(
                    new LambdaQueryWrapper<SkuDO>()
                            .in(SkuDO::getParentGuid, skuGuidSet));
            if (!CollectionUtils.isEmpty(storeSkuList)) {
                pushedSkuList.addAll(storeSkuList);
            }
        }
        if (!CollectionUtils.isEmpty(pushedSkuList)) {
            skuDOList.addAll(pushedSkuList);
        }

        // 商品与关联套餐数量映射关系
        Map<String, Integer> item2RelatePkgNumMap = itemHelper.mapItem2RelatePkgNum(skuDOList);

        Iterator<ItemWebRespDTO> iterator = itemWebRespDTOList.iterator();
        while (iterator.hasNext()) {
            ItemWebRespDTO itemWebRespDTO = iterator.next();
            // 此商品所含规格集合
            List<SkuDO> thisItemSkuDOList = skuDOList.stream()
                    .filter(skuDO -> skuDO.getItemGuid().equals(itemWebRespDTO.getItemGuid()))
                    .collect(Collectors.toList());
            int num = item2RelatePkgNumMap.get(itemWebRespDTO.getItemGuid());
            itemWebRespDTO.setIsWithPkg(num > 0 ? 1 : 0);
            if (CollectionUtils.isEmpty(thisItemSkuDOList)) {
                iterator.remove();
                continue;
            }
            setItemWebRespDTO(thisItemSkuDOList, pushedSkuList, itemWebRespDTO);
            }

        return itemWebRespDTOPage;
    }

    private void setItemWebRespDTO(List<SkuDO> thisItemSkuDOList,
                                   List<SkuDO> pushedSkuList,
                                   ItemWebRespDTO itemWebRespDTO) {
        List<String> thisItemSkuGuidList = thisItemSkuDOList
                .stream()
                .map(SkuDO::getGuid)
                .collect(Collectors.toList());
        // 此商品对应的推送到门店的规格集合
        List<SkuDO> storeSkuDOList = pushedSkuList.stream()
                .filter(skuDO -> thisItemSkuGuidList.contains(skuDO.getParentGuid()))
                .collect(Collectors.toList());
        // 设置上架门店数
        itemWebRespDTO.setRackStoreNum(0);
        setRackStoreNum(storeSkuDOList, itemWebRespDTO);
        // 是否有上架
        boolean hasRack = thisItemSkuDOList
                .stream()
                .anyMatch(skuDO -> skuDO.getIsRack() == 1);
        // 是否有会员折扣
        boolean hasMemberDiscount = thisItemSkuDOList
                .stream()
                .anyMatch(skuDO -> skuDO.getIsMemberDiscount() == 1);
        // 是否有整单折扣
        boolean hasWholeDiscount = thisItemSkuDOList
                .stream()
                .anyMatch(skuDO -> skuDO.getIsWholeDiscount() == 1);
        // 最低价格的SKU
        setLowestPrice(thisItemSkuDOList, itemWebRespDTO);
        itemWebRespDTO.setIsRack(hasRack ? 1 : 0);
        itemWebRespDTO.setIsMemberDiscount(hasMemberDiscount ? 1 : 0);
        itemWebRespDTO.setIsWholeDiscount(hasWholeDiscount ? 1 : 0);
        itemWebRespDTO.setCode(Optional.ofNullable(thisItemSkuDOList.get(0)).map(SkuDO::getCode).isPresent() ? thisItemSkuDOList.get(0).getCode() : null);
        // 标签集合
        List<TagEnum> tagList = getTagList(itemWebRespDTO);
        List<TagRespDTO> tagRespDTOList = MapStructUtils.INSTANCE.tagEnumList2tagRespDTOList(tagList);
        itemWebRespDTO.setTagList(tagRespDTOList);
    }

    private void setRackStoreNum(List<SkuDO> storeSkuDOList, ItemWebRespDTO itemWebRespDTO) {
        if (!CollectionUtils.isEmpty(storeSkuDOList)) {
            storeSkuDOList.removeIf(skuDO -> skuDO.getIsRack() != 1);
            if (!CollectionUtils.isEmpty(storeSkuDOList)) {
                // 按门店分组的规格集合
                Map<String, List<SkuDO>> storeGroupSkuMap = storeSkuDOList
                        .stream()
                        .collect(Collectors.groupingBy(SkuDO::getStoreGuid));
                itemWebRespDTO.setRackStoreNum(storeGroupSkuMap.size());
            }
        }
    }

    private void setLowestPrice(List<SkuDO> thisItemSkuDOList,
                                ItemWebRespDTO itemWebRespDTO) {
        SkuDO lowestPriceSku = thisItemSkuDOList
                .stream()
                .min(Comparator.comparing(SkuDO::getSalePrice))
                .orElse(null);
        if (lowestPriceSku == null) {
            log.error(SYSTEM_ERROR);
            throw new BusinessException(SYSTEM_ERROR);
        }
        itemWebRespDTO.setLowestPrice(lowestPriceSku.getSalePrice());
    }

    @NotNull
    private List<TagEnum> getTagList(ItemWebRespDTO itemWebRespDTO) {
        List<TagEnum> tagList = new ArrayList<>();
        if (itemWebRespDTO.getIsBestseller() == 1) {
            tagList.add(TagEnum.BESTSELLER);
        }
        if (itemWebRespDTO.getIsNew() == 1) {
            tagList.add(TagEnum.NEW);
        }
        tagList.add(TagEnum.NEW);
        if (itemWebRespDTO.getIsSign() == 1) {
            tagList.add(TagEnum.SIGN);
        }
        if (itemWebRespDTO.getIsRecommend() == 1) {
            tagList.add(TagEnum.RECOMMEND);
        }
        return tagList;
    }


    @Override
    public Page<ItemSortRespDTO> getItemSortList(ItemQueryReqDTO request) {
        MPPage<ItemDO> itemDOIPageQuery = new MPPage<>(request.getCurrentPage(), request.getPageSize());
        IPage<ItemDO> itemDOIPage = this.page(itemDOIPageQuery, new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getStoreGuid, request.getStoreGuid())
                .eq(ItemDO::getTypeGuid, request.getTypeGuid())
                .orderByAsc(ItemDO::getSort));
        return MapStructUtils.INSTANCE.itemDOListPage2ItemSortRespDTOPageS(itemDOIPage);
    }

    @Override
    @Transactional
    public Boolean retailUpdateItemSort(ItemRetailSortReqDTO request) {
        //修改分类排序
        Boolean modifyTypeFlag = typeService.retailUpdateTypesort(request.getTypeList());
        //修改分类下商品排序
        List<ItemSortReqDTO> itemSortReqDTOS = request.getTypeList()
                .stream().flatMap(typeSortReqDTO -> {
                    List<ItemSortReqDTO> itemList = typeSortReqDTO.getItemList();
                    if (CollectionUtils.isEmpty(itemList)) {
                        return Stream.empty();
                    }
                    return itemList.stream();
                })
                .collect(Collectors.toList());
        List<ItemDO> itemDOList = MapStructUtils.INSTANCE.ItemSortReqDTOList2ItemDOList(itemSortReqDTOS);
        Boolean modifyItemFlag = Objects.nonNull(itemDOList) ? super.updateBatchById(itemDOList) : Boolean.TRUE;
        return Objects.equals(modifyTypeFlag, modifyItemFlag) && modifyTypeFlag.equals(Boolean.TRUE) ? Boolean.TRUE : Boolean.FALSE;
    }

    @Override
    public boolean verifyThatitemExistsForType(SingleDataDTO request) {
        return super.count(new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getStoreGuid, request.getStoreGuid())
                .eq(ItemDO::getTypeGuid, request.getData())
                .ne(ItemDO::getIsDelete, 1)) > 0 ? Boolean.TRUE : Boolean.FALSE;
    }

    @Override
    public ItemRetailSortRespDTO getSortTypeAndItems(ItemSingleDTO request) {
        List<TypeWebRespDTO> typeWebRespDTOS = typeService.queryType(request);
        List<TypeSortRespDTO> typeList = MapStructUtils.INSTANCE.typeWebRespListS2TypeSortReqList(typeWebRespDTOS);
        List<String> typeGuidS = typeList.stream().map(TypeSortRespDTO::getGuid).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(typeGuidS)) {
            return null;
        }
        List<ItemDO> itemDOS = list(new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getStoreGuid, request.getStoreGuid())
                .in(ItemDO::getTypeGuid, typeGuidS)
                .notIn(ItemDO::getIsDelete, 1)
                .eq(ItemDO::getItemFrom, 0)
                .orderByAsc(ItemDO::getSort));
        Map<String, List<ItemDO>> listMap = itemDOS.stream().collect(Collectors.groupingBy(ItemDO::getTypeGuid));
        typeList.forEach(o -> {
            List<ItemDO> subItems = listMap.get(o.getGuid());
            o.setItemList(CollectionUtils.isEmpty(subItems) ? new ArrayList<>() : MapStructUtils.INSTANCE.itemDOList2ItemSortRespDTOS(subItems));
        });
        return ItemRetailSortRespDTO.builder().typeList(typeList).build();
    }
}
