package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.ParamException;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.item.entity.bo.ItemInfoBO;
import com.holderzone.saas.store.item.entity.bo.SubItemSkuBO;
import com.holderzone.saas.store.item.entity.bo.SubgroupBO;
import com.holderzone.saas.store.item.entity.domain.RSkuSubgroupDO;
import com.holderzone.saas.store.item.entity.domain.SubgroupDO;
import com.holderzone.saas.store.item.mapper.SubgroupMapper;
import com.holderzone.saas.store.item.service.IRSkuSubgroupService;
import com.holderzone.saas.store.item.service.ISubgroupService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.MapStructUtils;
import com.holderzone.sdk.util.BatchIdGenerator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.item.constant.Constant.OP_FAIL;
import static com.holderzone.saas.store.item.constant.Constant.WRONG_PARAMS;

/**
 * <p>
 * 套餐的分组 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-24
 */
@Service
public class SubgroupServiceImpl extends ServiceImpl<SubgroupMapper, SubgroupDO> implements ISubgroupService {
    private final IRSkuSubgroupService skuSubgroupService;
    private final DynamicHelper dynamicHelper;
    private final RedisTemplate redisTemplate;

    @Autowired
    private SubgroupMapper subgroupMapper;

    @Autowired
    public SubgroupServiceImpl(@Lazy IRSkuSubgroupService skuSubgroupService, DynamicHelper dynamicHelper, RedisTemplate redisTemplate) {
        this.skuSubgroupService = skuSubgroupService;
        this.dynamicHelper = dynamicHelper;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public void removeSubgroupByItemGuidList(List<String> toDeleteItemGuidList) throws ParamException {
        if (CollectionUtils.isEmpty(toDeleteItemGuidList)) {
            throw new ParamException(WRONG_PARAMS);
        }
        // 查询指定商品下的分组集合
        List<SubgroupDO> subgroupDOList = list(new LambdaQueryWrapper<SubgroupDO>().in(SubgroupDO::getItemGuid, toDeleteItemGuidList));
        if (!CollectionUtils.isEmpty(subgroupDOList)) {
            List<String> subgroupGuidList = subgroupDOList.stream().map(SubgroupDO::getGuid).collect(Collectors.toList());
            skuSubgroupService.remove(new LambdaQueryWrapper<RSkuSubgroupDO>().in(RSkuSubgroupDO::getSubgroupGuid, subgroupGuidList));
            removeByIds(subgroupGuidList);
        }
    }

    @Override
    public List<SubgroupBO> selectSubgroupListByItemGuidList(List<String> itemGuidList) {
        if (CollectionUtils.isEmpty(itemGuidList)) {
            return new ArrayList<>();
        }
        List<SubgroupDO> subgroupDOList = list(
                new LambdaQueryWrapper<SubgroupDO>()
                        .in(SubgroupDO::getItemGuid, itemGuidList)
                        .orderByAsc(SubgroupDO::getSort));
        List<SubgroupBO> subgroupBOList = MapStructUtils.INSTANCE.subgroupDOList2subgroupBOList(subgroupDOList);
        List<String> subgroupGuidList = subgroupDOList
                .stream()
                .map(SubgroupDO::getGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subgroupGuidList)) {
            return new ArrayList<>();
        }
        // sku与分组的关联实体集合
        List<RSkuSubgroupDO> skuSubgroupDOList = skuSubgroupService.list(
                new LambdaQueryWrapper<RSkuSubgroupDO>()
                        .in(RSkuSubgroupDO::getSubgroupGuid, subgroupGuidList)
                        .orderByAsc(RSkuSubgroupDO::getSort));
        subgroupBOList.forEach(subgroupBO -> {
            List<RSkuSubgroupDO> skuSubgroupDOS = skuSubgroupDOList.stream()
                    .filter(rSkuSubgroupDO -> rSkuSubgroupDO.getSubgroupGuid().equals(subgroupBO.getSubgroupGuid()))
                    .collect(Collectors.toList());
            // 当前分组下的子商品实体
            List<SubItemSkuBO> subItemSkuBOList = MapStructUtils.INSTANCE.skuSubgroupDOList2subItemSkuBOList(skuSubgroupDOS);
            subgroupBO.setSubItemSkuList(subItemSkuBOList);
            subgroupBO.setIsFixSubgroup(subgroupBO.getPickNum() == 0 ? 1 : 0);
        });
        return subgroupBOList;
    }

    @Override
    @Transactional
    public boolean saveOrUpdateAndDeleteSubgroup(List<ItemInfoBO> itemInfoBOList) {
        if (CollectionUtils.isEmpty(itemInfoBOList)) {
            return false;
        }
        List<String> itemGuidList = itemInfoBOList.stream().map(ItemInfoBO::getItemGuid).collect(Collectors.toList());
        // 更新之前的分组集合
        List<SubgroupBO> beforeSubgroupList = selectSubgroupListByItemGuidList(itemGuidList);
        fixAndFillItemInfoBOFields(itemInfoBOList);
        // 所有的分组集合
        List<SubgroupBO> subgroupBOList = getAllItemSubgroupList(itemInfoBOList);
        // 待保存的分组集合
        List<SubgroupDO> toSaveSubgroupDOList = new ArrayList<>();
        // 待更新的分组集合
        List<SubgroupDO> toUpdateSubgroupDOList = new ArrayList<>();

        // 待保存的子商品与分组关联实体集合
        List<RSkuSubgroupDO> toSaveSkuSubgroupDOList = new ArrayList<>();
        // 待更新的子商品与分组关联实体集合
        List<RSkuSubgroupDO> toUpdateSkuSubgroupDOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(subgroupBOList)) {
            for (int i = 0; i < subgroupBOList.size(); i++) {
                SubgroupBO subgroupBO = subgroupBOList.get(i);
                if (subgroupBO.getIsFixSubgroup() == 1) {
                    subgroupBO.setPickNum(0);
                }
                SubgroupDO subgroupDO = MapStructUtils.INSTANCE.subgroupBO2subgroupDO(subgroupBO);
                if (StringUtils.isEmpty(subgroupDO.getGuid())) {
                    try {
                        subgroupDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
                    } catch (IOException e) {
                        throw new BusinessException("BatchIdGenerator生成商品guid失败");
                    }
                    toSaveSubgroupDOList.add(subgroupDO);
                } else {
                    toUpdateSubgroupDOList.add(subgroupDO);
                }
                subgroupDO.setSort(i + 1);
                // 该分组下的规格集合
                List<SubItemSkuBO> subItemSkuList = subgroupBO.getSubItemSkuList();
                subItemSkuList.forEach(subItemSkuBO -> {
                    RSkuSubgroupDO skuSubgroupDO = MapStructUtils.INSTANCE.subItemSkuBO2skuSubgroupDO(subItemSkuBO);
                    if (StringUtils.isEmpty(skuSubgroupDO.getGuid())) {
                        try {
                            skuSubgroupDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "item")));
                        } catch (IOException e) {
                            throw new BusinessException("BatchIdGenerator生成商品guid失败");
                        }
                        skuSubgroupDO.setSubgroupGuid(subgroupDO.getGuid());
                        toSaveSkuSubgroupDOList.add(skuSubgroupDO);
                    } else {
                        toUpdateSkuSubgroupDOList.add(skuSubgroupDO);
                    }
                    // 如果是固定分组
                    if (subgroupBO.getPickNum() == 0) {
                        fixFixedSubgroupSubItemFields(skuSubgroupDO);
                    }
                });
            }
        }
        // 待更新的分组GUID集合
        List<String> updateSubgroupGuidList = toUpdateSubgroupDOList.stream().map(SubgroupDO::getGuid).collect(Collectors.toList());
        // 待更新的子商品与分组关联实体GUID集合
        List<String> updateSubItemSubgroupGuidList = toUpdateSkuSubgroupDOList.stream().map(RSkuSubgroupDO::getGuid).collect(Collectors.toList());
        // 所有子商品与原分组关联实体GUID集合
        List<String> beforeSubItemSubgroupGuidList = getSkuSubgroupGuidListBySubgroupList(beforeSubgroupList);
        // 待删除的分组集合
        // todo test null List-beforeSubgroupList or updateSubgroupGuidList
        List<SubgroupBO> toDelSubgroupList = beforeSubgroupList.stream()
                .filter(subgroupBO -> !updateSubgroupGuidList.contains(subgroupBO.getSubgroupGuid())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(toDelSubgroupList)) {
            // 删除分组
            List<String> toDelSubgroupGuidList = toDelSubgroupList.stream().map(SubgroupBO::getSubgroupGuid).collect(Collectors.toList());
            removeByIds(toDelSubgroupGuidList);
        }
        if (!CollectionUtils.isEmpty(beforeSubItemSubgroupGuidList) && !CollectionUtils.isEmpty(updateSubItemSubgroupGuidList)) {
            // 待删除的子商品与分组关联关系GUID集合
            beforeSubItemSubgroupGuidList.removeAll(updateSubItemSubgroupGuidList);
        }
        if (!CollectionUtils.isEmpty(beforeSubItemSubgroupGuidList)) {
            skuSubgroupService.removeByIds(beforeSubItemSubgroupGuidList);
        }
        if (!CollectionUtils.isEmpty(toSaveSubgroupDOList)) {
            saveBatch(toSaveSubgroupDOList);
        }
        if (!CollectionUtils.isEmpty(toUpdateSubgroupDOList)) {
            boolean updateBatchById = updateBatchById(toUpdateSubgroupDOList, toUpdateSubgroupDOList.size());
            if (!updateBatchById) {
                throw new BusinessException(OP_FAIL);
            }
        }
        if (!CollectionUtils.isEmpty(toSaveSkuSubgroupDOList)) {
            skuSubgroupService.saveBatch(toSaveSkuSubgroupDOList);
        }
        if (!CollectionUtils.isEmpty(toUpdateSkuSubgroupDOList)) {
            boolean updateBatchById = skuSubgroupService.updateBatchById(toUpdateSkuSubgroupDOList, toUpdateSkuSubgroupDOList.size());
            if (!updateBatchById) {
                throw new BusinessException(OP_FAIL);
            }
        }
        return true;
    }

    @Override
    public List<RSkuSubgroupDO> selectSkuSubgroupByItemGuidList(List<String> pkgGuidList) {
        if (CollectionUtils.isEmpty(pkgGuidList)) {
            return new ArrayList<>();
        }
        // 所有分组的集合
        List<SubgroupDO> subgroupDOList = list(new LambdaQueryWrapper<SubgroupDO>()
                        .in(SubgroupDO::getItemGuid, pkgGuidList));
        if (CollectionUtils.isEmpty(subgroupDOList)) {
            return new ArrayList<>();
        }
        // 分组GUID集合
        List<String> subgroupGuidList = subgroupDOList
                .stream()
                .map(SubgroupDO::getGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subgroupDOList)) {
            return new ArrayList<>();
        }
        // 所有分组与规格关联实体的集合
        return skuSubgroupService.list(new LambdaQueryWrapper<RSkuSubgroupDO>()
                .in(RSkuSubgroupDO::getSubgroupGuid, subgroupGuidList));

    }

    /**
     * 获取指定分组集合下商品与分组的关联实体的GUID集合
     *
     * @param beforeSubgroupList
     * @return
     */
    // todo 抽取工具类
    private List<String> getSkuSubgroupGuidListBySubgroupList(List<SubgroupBO> beforeSubgroupList) {
        List<String> beforeSubItemSubgroupGuidList = new ArrayList<>();
        if (CollectionUtils.isEmpty(beforeSubgroupList)) {
            return beforeSubItemSubgroupGuidList;
        }
        // 遍历修改之前的分组集合
        beforeSubgroupList.forEach(subgroupBO -> {
            List<SubItemSkuBO> subItemSkuList = subgroupBO.getSubItemSkuList();
            // 子商品与分组关联实体GUID
            List<String> subItemSkuGuidList = subItemSkuList.stream().map(SubItemSkuBO::getSkuSubgroupGuid).collect(Collectors.toList());
            beforeSubItemSubgroupGuidList.addAll(subItemSkuGuidList);
        });
        return beforeSubItemSubgroupGuidList;
    }

    /**
     * 修改固定分组的子商品字段
     *
     * @param skuSubgroupDO
     */
    private void fixFixedSubgroupSubItemFields(RSkuSubgroupDO skuSubgroupDO) {
        skuSubgroupDO.setAddPrice(BigDecimal.ZERO);
        skuSubgroupDO.setIsDefault(1);
        skuSubgroupDO.setIsRepeat(0);
    }

    /**
     * 获取所有商品的分组集合
     *
     * @param itemInfoBOList
     * @return
     */
    // todo 待抽工具类
    private List<SubgroupBO> getAllItemSubgroupList(List<ItemInfoBO> itemInfoBOList) {
        List<SubgroupBO> subgroupBOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(itemInfoBOList)) {
            return subgroupBOList;
        }
        for (ItemInfoBO itemInfoBO : itemInfoBOList) {
            List<SubgroupBO> subgroupList = itemInfoBO.getSubgroupList();
            if (!CollectionUtils.isEmpty(subgroupList)) {
                subgroupBOList.addAll(subgroupList);
            }
        }
        return subgroupBOList;
    }

    private void fixAndFillItemInfoBOFields(List<ItemInfoBO> itemInfoBOList) {
        itemInfoBOList.forEach(itemInfoBO -> {
            List<SubgroupBO> subgroupList = itemInfoBO.getSubgroupList();
            if (CollectionUtils.isEmpty(subgroupList)) {
                return;
            }
            subgroupList.forEach(subgroupBO -> {
                subgroupBO.setItemGuid(itemInfoBO.getItemGuid());
                subgroupBO.setStoreGuid(itemInfoBO.getStoreGuid());
                subgroupBO.setBrandGuid(itemInfoBO.getBrandGuid());
                subgroupBO.setSubgroupFrom(itemInfoBO.getItemFrom());
                List<SubItemSkuBO> subItemSkuList = subgroupBO.getSubItemSkuList();
                subItemSkuList.forEach(subItemSkuBO -> subItemSkuBO.setSubgroupGuid(subgroupBO.getSubgroupGuid()));
            });
        });
    }


    @Override
    public List<String> getGuidListByItemGuidAndStoreGuid(String itemGuid, String storeGuid) {
        return subgroupMapper.getGuidListByItemGuidAndStoreGuid(itemGuid , storeGuid);
    }

    @Override
    public List<String> getGuidListByStoreGuidAndBrandGuid(String storeGuid, String brandGuid) {
        return subgroupMapper.getGuidListByStoreGuidAndBrandGuid(storeGuid , brandGuid);
    }

    @Override
    public void deleteByItemGuidAndStoreGuid(String itemGuid, String storeGuid) {
        subgroupMapper.deleteByItemGuidAndStoreGuid(itemGuid , storeGuid);
    }
}
