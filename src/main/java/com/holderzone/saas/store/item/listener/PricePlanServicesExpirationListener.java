package com.holderzone.saas.store.item.listener;

import com.holderzone.saas.store.item.dto.SyncPricePlanMessageDTO;
import com.holderzone.saas.store.item.service.ICommonService;
import com.holderzone.saas.store.item.service.IRedissonCacheService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.map.event.EntryExpiredListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 监听Redis的key过期,用于价格方案的定时推送
 *
 * <AUTHOR> chen
 * @version 1.0
 * @since 2020-10-28
 */
//@Component
@Slf4j
public class PricePlanServicesExpirationListener {
    private static final String KEY_PREFIX = "price:plan";

    // 幂等key的记录后缀
    private static final String IDEMPOTENT_KEY_SUFFIX = "idempotent";

    @Autowired
    private ICommonService iCommonService;

    @Autowired
    private IRedissonCacheService iRedissonCacheService;

    @PostConstruct
    public void onMessage() {
        iRedissonCacheService.getSyncPricePlanItemDTORMapCache()
                .addListener((EntryExpiredListener<String, SyncPricePlanMessageDTO>) event -> {
                    String key = event.getKey();
                    log.info("价格方案到期监听key->:" + key);
                    if (key.startsWith(KEY_PREFIX)) {
                        RLock distributedLock = iCommonService.getDistributedLock(key);
                        distributedLock.lock();
                        String idempotentKey = iCommonService.md5(key);
                        try {
                            if (Objects.isNull(iRedissonCacheService.getCache(idempotentKey))) {
                                iRedissonCacheService.setCache(idempotentKey, "1", 30, TimeUnit.SECONDS);
                                iCommonService.sendPricePlanToMQ(event.getValue());
                                log.info("价格方案定时推送到期 - key : " + event.getKey() + " , value : " + event.getValue().getPricePlanGuid());
                            }
                        } finally {
                            distributedLock.unlock();
                        }
                    }
                });
    }
}