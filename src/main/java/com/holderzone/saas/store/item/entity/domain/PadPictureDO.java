package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 菜品下关联的pad点餐图片
 * Created by pantao on 2021/7/21.
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsi_item_pad_picture")
@NoArgsConstructor
@AllArgsConstructor
public class PadPictureDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Integer isDelete;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 菜品guid
     */
    private String itemGuid;

    /**
     * 菜品guid
     */
    private String planGuid;

    /**
     * 小图
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String smallPicture;

    /**
     * 大图
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String bigPicture;

    /**
     * 竖图
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String verticalPicture;

    /**
     * 详情大图
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String detailPicture;

    /**
     * 门店guid
     */
    private String storeGuid;
}
