package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.item.dto.PushRecordStatusUpdateReqDTO;
import com.holderzone.saas.store.item.entity.domain.PricePlanPushRecordDO;
import com.holderzone.saas.store.item.mapper.PricePlanPushRecordMapper;
import com.holderzone.saas.store.item.service.IPricePlanPushRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PricePlanPushRecordServiceImpl extends ServiceImpl<PricePlanPushRecordMapper, PricePlanPushRecordDO>
        implements IPricePlanPushRecordService {

    @Autowired
    private PricePlanPushRecordMapper pushRecordMapper;

    @Override
    public List<PricePlanPushRecordDO> getPushRecords(String planGuid) {
        return pushRecordMapper.getPushRecords(planGuid);
    }

    @Override
    public void updatePushStatus(PushRecordStatusUpdateReqDTO reqDTO) {
        pushRecordMapper.updatePushStatus(reqDTO);
    }

    @Override
    public void deletePushRecords(String planGuid) {
        pushRecordMapper.deletePushRecords(planGuid);
    }
}
