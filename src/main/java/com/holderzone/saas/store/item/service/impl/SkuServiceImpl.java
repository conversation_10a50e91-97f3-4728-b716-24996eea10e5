package com.holderzone.saas.store.item.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.ParamException;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemDTO;
import com.holderzone.saas.store.dto.item.common.ItemPadCalculateDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.takeaway.ErpMappingItem;
import com.holderzone.saas.store.dto.takeaway.ErpMappingType;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.item.constant.ItemType;
import com.holderzone.saas.store.item.dto.UpdateTerminalStatusDTO;
import com.holderzone.saas.store.item.entity.bo.*;
import com.holderzone.saas.store.item.entity.domain.*;
import com.holderzone.saas.store.item.entity.enums.ItemStateEnum;
import com.holderzone.saas.store.item.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.item.entity.enums.PricePlanStatusEnum;
import com.holderzone.saas.store.item.entity.enums.SalesModelEnum;
import com.holderzone.saas.store.item.helper.EventPushHelper;
import com.holderzone.saas.store.item.helper.PageAdapter;
import com.holderzone.saas.store.item.mapper.*;
import com.holderzone.saas.store.item.repository.PlanItemRepository;
import com.holderzone.saas.store.item.service.*;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import com.holderzone.saas.store.item.util.MapStructUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.item.constant.Constant.*;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 商品规格表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Service
@Slf4j
@AllArgsConstructor
public class SkuServiceImpl extends ServiceImpl<SkuMapper, SkuDO> implements ISkuService {
    private final SkuMapper skuMapper;

    private final ITypeService typeService;
    private final IRSkuSubgroupService skuSubgroupService;
    private final ISubgroupService subgroupService;
    private final IRItemAttrGroupService itemAttrGroupService;
    private final IRAttrItemAttrGroupService attrItemAttrGroupService;
    private final ItemMapper itemMapper;
    private final TypeMapper typeMapper;
    private final PricePlanItemMapper planItemMapper;

    private final PricePlanStoreMapper planStoreMapper;

    private final IPricePlanStoreService pricePlanStoreService;

    private final PricePlanMapper pricePlanMapper;

    private final OrganizationService organizationService;
    private final IItemService itemService;
    private final EventPushHelper eventPushHelper;

    private final EstimateMapper estimateMapper;

    private final SubgroupMapper subgroupMapper;

    private final PlanItemRepository planItemRepository;


    @Transactional(readOnly = true, rollbackFor = Exception.class)
    @Override
    public List<ItemExcelTemplateRespDTO> listDownloadData(ItemSingleDTO itemSingleDTO) {

        // 满足条件的菜品
        List<ItemDO> itemDOS = itemMapper.selectList(new LambdaQueryWrapper<ItemDO>()
                .eq(ItemDO::getStoreGuid, itemSingleDTO.getData())
                .or()
                .eq(ItemDO::getBrandGuid, itemSingleDTO.getData()));

        if (ObjectUtils.isEmpty(itemDOS)) {
            return new ArrayList<>();
        }

        // 分别查出满足条件的sku和type 以构建数据
        List<String> itemGuidList = itemDOS.stream()
                .map(i -> StringUtils.isEmpty(i.getParentGuid()) ? i.getGuid() : i.getParentGuid())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemGuidList)) {
            return new ArrayList<>();
        }

        // 保险起见查父菜的分类
        List<ItemDO> selectList = itemMapper.selectList(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getGuid, itemGuidList));
        List<String> typeGuidList = selectList.stream().map(ItemDO::getTypeGuid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(typeGuidList)) {
            return new ArrayList<>();
        }

        List<SkuDO> skuDOS = skuMapper.selectList(
                new LambdaQueryWrapper<SkuDO>()
                        .in(SkuDO::getItemGuid, itemGuidList));
        List<TypeDO> typeDOS = typeService.list(
                new LambdaQueryWrapper<TypeDO>()
                        .in(TypeDO::getGuid, typeGuidList));

        // 构建需要的数据
        ArrayList<ItemExcelTemplateRespDTO> itemExcelTemplateRespDTOList = new ArrayList<>();
        skuDOS.forEach(skuDO -> {
            ItemExcelTemplateRespDTO itemExcelTemplateRespDTO = new ItemExcelTemplateRespDTO();
            itemExcelTemplateRespDTO.setSkuName(skuDO.getName());
            itemExcelTemplateRespDTO.setSku(skuDO.getGuid());
            itemExcelTemplateRespDTO.setSkuCode(skuDO.getCode());
            itemExcelTemplateRespDTO.setSalePrice(skuDO.getSalePrice());
            itemExcelTemplateRespDTO.setMinOrderNum(skuDO.getMinOrderNum());
            itemExcelTemplateRespDTO.setUnit(skuDO.getUnit());
            itemExcelTemplateRespDTO.setIsWholeDiscount(skuDO.getIsWholeDiscount() == 1 ? "是" : "否");
            List<ItemDO> itemDOList = selectList.stream()
                    .filter(itemDO -> itemDO.getGuid().equals(skuDO.getItemGuid()))
                    .collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(itemDOList)) {
                ItemDO itemDO = itemDOList.get(0);
                itemExcelTemplateRespDTO.setItemName(itemDO.getName());
                itemExcelTemplateRespDTO.setNameAbbr(itemDO.getNameAbbr());
                itemExcelTemplateRespDTO.setItemType(itemDO.getItemType() == 3 ? "称重商品" : "普通商品");
                itemExcelTemplateRespDTO.setPictureUrl(itemDO.getPictureUrl()); //导出添加图片地址
                List<TypeDO> typeDOList = typeDOS.stream()
                        .filter(typeDO -> typeDO.getGuid().equals(itemDO.getTypeGuid()))
                        .collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(typeDOList)) {
                    TypeDO typeDO = typeDOList.get(0);
                    itemExcelTemplateRespDTO.setTypeName(typeDO.getName());
                }
            }
            itemExcelTemplateRespDTOList.add(itemExcelTemplateRespDTO);
        });
        return itemExcelTemplateRespDTOList;
    }

    @Override
    @Transactional
    public Integer rack(ItemRackDTO itemRackDTO) {
        validateItemRackDTO(itemRackDTO);
        // 选择商品的GUID集合
        List<String> itemGuidList = itemRackDTO.getItemGuidList();
        if (CollectionUtils.isEmpty(itemGuidList)) {
            return 0;
        }
        List<SkuDO> skuDOList = list(
                new LambdaQueryWrapper<SkuDO>()
                        .in(SkuDO::getItemGuid, itemGuidList));
        List<String> skuGuidList = skuDOList.stream().map(SkuDO::getGuid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuGuidList)) {
            log.error("SKU上下架发现系统错误，商品GUID没有对应的SKUGuid");
            throw new BusinessException("系统错误");
        }
        // 被选中规格推送至门店的规格集合
        List<SkuDO> pushedSkuDOList = list(
                new LambdaQueryWrapper<SkuDO>()
                        .in(SkuDO::getParentGuid, skuGuidList));
        if (CollectionUtils.isNotEmpty(pushedSkuDOList)) {
            skuDOList.addAll(pushedSkuDOList);
        }
        skuDOList.forEach(skuDO -> {
            skuDO.setIsRack(itemRackDTO.getRackState());
//            BeanUtils.copyProperties(itemRackDTO, skuDO);
            if (itemRackDTO.getRackState() == 1) {
                skuDO.setIsJoinMiniAppTakeaway(1);
                skuDO.setIsJoinStore(1);
            }
            if (itemRackDTO.getRackState() == 0) {
                skuDO.setIsJoinMiniAppTakeaway(0);
                skuDO.setIsJoinStore(0);
            }
        });

        boolean update = updateBatchById(skuDOList, skuDOList.size());
        if (itemRackDTO.getRackState() != null && itemRackDTO.getRackState() == 1) {
            // 校验此次操作上架商品的信息是否符合上架标准（校验是否有结构错误，如套餐下分组为空）
            validateTheseRackItems(skuDOList);
        }

        // 门店库上下架操作后推送至门店
        if (!ObjectUtils.isEmpty(itemRackDTO.getStoreGuid())) {
            eventPushHelper.pushMsgToAndriod(Collections.singletonList(itemRackDTO.getStoreGuid()));
        }

        // 品牌库上下架操作后推送到所有品牌下门店
        if (CollectionUtils.isNotEmpty(itemRackDTO.getItemGuidList())) {
            List<ItemDO> itemDOList = itemService.list(new LambdaQueryWrapper<ItemDO>()
                    .in(ItemDO::getGuid, itemRackDTO.getItemGuidList()));
            if (CollectionUtils.isEmpty(itemDOList)) {
                log.warn("上下架商品查询异常 itemGuidList={}", itemRackDTO.getItemGuidList());
                throw new BusinessException("上下架商品查询异常");
            }
            String brandGuid = itemDOList.get(0).getBrandGuid();
            // 品牌库
            if (!StringUtils.isEmpty(brandGuid)) {
                // 下架菜谱方案对应商品
                if (itemRackDTO.getRackState() == 0) {
                    List<PricePlanItemDO> planItemDOList = planItemMapper.selectList(
                            new LambdaQueryWrapper<PricePlanItemDO>()
                                    .in(PricePlanItemDO::getItemGuid, itemGuidList)
                                    .eq(PricePlanItemDO::getIsDelete, BooleanEnum.FALSE.getCode())
                    );
                    Map<String, List<PricePlanItemDO>> planItemMap = planItemDOList.stream()
                            .collect(groupingBy(PricePlanItemDO::getPlanGuid));
                    planItemMap.forEach((planGuid, itemList) -> {
                        Set<String> itemSet = itemList.stream()
                                .map(PricePlanItemDO::getItemGuid)
                                .collect(Collectors.toSet());
                        pricePlanMapper.updateItemNum(planGuid, itemSet.size(), 2);
                    });
                    planItemMapper.logicDeleteByItem(itemGuidList);
                }
                log.info("根据品牌guid查询品牌下所有门店Guid brandGuid={}", brandGuid);
                List<String> storeGuidList = organizationService.queryStoreGuidListByBrandGui(brandGuid);
                if (!CollectionUtils.isEmpty(storeGuidList)) {
                    log.info("品牌下门店 storeGuidList={}", storeGuidList);
                    eventPushHelper.pushMsgToAndriod(storeGuidList);
                }
            }
        }

        return update ? 1 : 0;
    }


    @Override
    public List<String> updateSkuForOpen(ItemInfoBO itemInfoBO) {
        fixAndFillItemReqDTO(itemInfoBO);
        // 新增或更新了的规格GUID集合
        List<String> saveOrUpdateSkuGuidList = new ArrayList<>();
        if (itemInfoBO == null) {
            return saveOrUpdateSkuGuidList;
        }
        // 历史规格
        List<SkuDO> beforeSkuDOList = list(
                new LambdaQueryWrapper<SkuDO>()
                        .eq(SkuDO::getItemGuid, itemInfoBO.getItemGuid()));
        List<SkuInfoBO> skuInfoBOList = itemInfoBO.getSkuList();
        if (ObjectUtils.isEmpty(skuInfoBOList)) {
            return saveOrUpdateSkuGuidList;
        }
        List<SkuDO> skuDOList = MapStructUtils.INSTANCE.skuInfoBOList2skuDOList(skuInfoBOList);
        //更新
        List<SkuDO> toUpdateSkuList = new ArrayList<>(skuDOList);
        toUpdateSkuList.retainAll(beforeSkuDOList);

        if (!ObjectUtils.isEmpty(toUpdateSkuList)) {
            boolean updateBatchById = updateBatchById(toUpdateSkuList, toUpdateSkuList.size());
            if (!updateBatchById) {
                throw new BusinessException(OP_FAIL);
            }
            List<String> updateSkuGuidList = toUpdateSkuList.stream().map(SkuDO::getGuid).collect(Collectors.toList());
            saveOrUpdateSkuGuidList.addAll(updateSkuGuidList);
        }
        return saveOrUpdateSkuGuidList;
    }


    @Override
    public List<String> deleteSkuForOpen(ItemInfoBO itemInfoBO) {
        fixAndFillItemReqDTO(itemInfoBO);

        List<SkuInfoBO> skuInfoBOList = itemInfoBO.getSkuList();
        List<String> saveOrUpdateSkuGuidList = new ArrayList<>();
        if (itemInfoBO == null || CollectionUtils.isEmpty(skuInfoBOList)) {
            return saveOrUpdateSkuGuidList;
        }
        // 要删除的规格
        List<SkuDO> toDeleteSkuList = MapStructUtils.INSTANCE.skuInfoBOList2skuDOList(skuInfoBOList);
        if (!ObjectUtils.isEmpty(toDeleteSkuList)) {
            // 待删除的规格GUID集合
            List<String> deleteSkuGuidList = toDeleteSkuList
                    .stream()
                    .map(SkuDO::getGuid)
                    .collect(Collectors.toList());
            deleteSkuByIds(deleteSkuGuidList, true);
        }
        return saveOrUpdateSkuGuidList;
    }


    /**
     * 校验当次上架商品结构的正确性
     *
     * @param skuDOList
     */
    private void validateTheseRackItems(List<SkuDO> skuDOList) {
        // 被选中上架商品的GUID集合
        Set<String> itemGuidSet = skuDOList.stream().map(SkuDO::getItemGuid).collect(Collectors.toSet());
        List<ItemDO> itemList = (ArrayList) itemMapper.selectBatchIds(itemGuidSet);
        List<ItemDO> pkgList = itemList.stream().filter(itemDO -> itemDO.getItemType() == 1).collect(Collectors.toList());
        Set<String> skuGuidSet = skuDOList.stream().map(SkuDO::getGuid).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(pkgList)) {
            List<String> pkgGuidList = pkgList.stream().map(ItemDO::getGuid).collect(Collectors.toList());
            List<SubgroupDO> subgroupDOS = subgroupService.list(new LambdaQueryWrapper<SubgroupDO>()
                    .in(SubgroupDO::getItemGuid, pkgGuidList));
            List<String> subgroupGuidList = subgroupDOS.stream().map(SubgroupDO::getGuid).collect(Collectors.toList());
            List<RSkuSubgroupDO> skuSubgroupDOS = skuSubgroupService.list(new LambdaQueryWrapper<RSkuSubgroupDO>()
                    .in(RSkuSubgroupDO::getSubgroupGuid, subgroupGuidList));
            // 子商品的商品GUID集合
            Set<String> subItemGuidSet = skuSubgroupDOS.stream().map(RSkuSubgroupDO::getItemGuid).collect(Collectors.toSet());
            // 仅剩下还未获取详情的商品
            subItemGuidSet.removeAll(itemGuidSet);
            // 子商品的规格GUID集合
            Set<String> subSkuGuidSet = skuSubgroupDOS.stream().map(RSkuSubgroupDO::getSkuGuid).collect(Collectors.toSet());
            // 仅剩下还未获取详情的规格
            subSkuGuidSet.removeAll(skuGuidSet);

            if (!CollectionUtils.isEmpty(subItemGuidSet)) {
                // 仅包含在套餐分组内的商品
                List<ItemDO> simpleSubItemList = itemMapper.selectBatchIds(subItemGuidSet);
                itemList.addAll(simpleSubItemList);
                // 合并所有的商品GUID
                itemGuidSet.addAll(subItemGuidSet);
            }
            if (!CollectionUtils.isEmpty(subSkuGuidSet)) {
                List<SkuDO> simpleSubSkuList = skuMapper.selectBatchIds(subSkuGuidSet);
                // 合并所有规格
                skuDOList.addAll(simpleSubSkuList);
                // 合并所有规格
                skuGuidSet.addAll(subSkuGuidSet);
            }
            validatePkgStructure(pkgList, subgroupDOS, skuSubgroupDOS);
        }
        // 带属性的商品GUID集合
        List<String> withAttrItemGuidList = itemList.stream()
                .filter(itemDO -> itemDO.getHasAttr() != 0).map(ItemDO::getGuid).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(withAttrItemGuidList)) {
            validateAttrStructure(itemList, withAttrItemGuidList);
        }

        itemList.forEach(itemDO -> {
            List<SkuDO> skuDOS = skuDOList.stream()
                    .filter(skuDO -> skuDO.getItemGuid().equals(itemDO.getGuid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(skuDOS)) {
                log.error(WRONG_ITEM_STRUCTURE);
                throw new BusinessException(WRONG_ITEM_STRUCTURE);
            }
            if (itemDO.getItemType() == 2) {
                boolean emptySkuName = skuDOS.stream().anyMatch(skuDO -> StringUtils.isEmpty(skuDO.getName()));
                if (emptySkuName) {
                    log.error(WRONG_ITEM_STRUCTURE);
                    throw new BusinessException(WRONG_ITEM_STRUCTURE);
                }
            }
        });
    }

    private void validateAttrStructure(List<ItemDO> itemList, List<String> withAttrItemGuidList) {
        // 商品与属性组关联关系集合
        List<RItemAttrGroupDO> itemAttrGroupDOS = itemAttrGroupService.list(new LambdaQueryWrapper<RItemAttrGroupDO>()
                .in(RItemAttrGroupDO::getItemGuid, withAttrItemGuidList));
        if (CollectionUtils.isEmpty(itemAttrGroupDOS)) {
            log.error(WRONG_ITEM_STRUCTURE);
            throw new BusinessException(WRONG_ITEM_STRUCTURE);
        }
        List<String> itemAttrGroupGuidList = itemAttrGroupDOS.stream().map(RItemAttrGroupDO::getGuid).collect(Collectors.toList());
        // 属性与商品的关联关系
        List<RAttrItemAttrGroupDO> attrItemAttrGroupDOS = attrItemAttrGroupService.list(new LambdaQueryWrapper<RAttrItemAttrGroupDO>()
                .in(RAttrItemAttrGroupDO::getItemAttrGroupGuid, itemAttrGroupGuidList));
        if (CollectionUtils.isEmpty(attrItemAttrGroupDOS)) {
            log.error(WRONG_ITEM_STRUCTURE);
            throw new BusinessException(WRONG_ITEM_STRUCTURE);
        }
        itemList.forEach(itemDO -> {
            if (itemDO.getHasAttr() != 0) {
                // 此商品关联的属性组关联关系
                List<RItemAttrGroupDO> itemAttrGroupDOList = itemAttrGroupDOS.stream()
                        .filter(itemAttrGroupDO -> itemAttrGroupDO.getItemGuid().equals(itemDO.getGuid()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(itemAttrGroupDOList)) {
                    log.error(WRONG_ITEM_STRUCTURE);
                    throw new BusinessException(WRONG_ITEM_STRUCTURE);
                }
                itemAttrGroupDOList.forEach(itemAttrGroupDO -> {
                    List<RAttrItemAttrGroupDO> attrItemAttrGroupDOList = attrItemAttrGroupDOS.stream()
                            .filter(attr -> attr.getItemAttrGroupGuid().equals(itemAttrGroupDO.getGuid()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(attrItemAttrGroupDOList)) {
                        log.error(WRONG_ITEM_STRUCTURE);
                        throw new BusinessException(WRONG_ITEM_STRUCTURE);
                    }
                    if (itemAttrGroupDO.getIsMultiChoice() == 0) {
                        long defaultAttrNum = attrItemAttrGroupDOList.stream().filter(attr -> attr.getIsDefault() == 1).count();
                        if (defaultAttrNum > 1) {
                            log.error(WRONG_ITEM_STRUCTURE);
                            throw new BusinessException(WRONG_ITEM_STRUCTURE);
                        }
                    }
                });
                boolean hasRequiredAttr = itemAttrGroupDOList.stream().anyMatch(itemAttrGroupDO -> itemAttrGroupDO.getIsRequired() == 1);
                Integer hasAttr = itemDO.getHasAttr();
                if ((hasRequiredAttr && hasAttr != 2)
                        || (!hasRequiredAttr && hasAttr == 2)) {
                    log.error(WRONG_ITEM_STRUCTURE);
                    throw new BusinessException(WRONG_ITEM_STRUCTURE);
                }

            }

        });
    }

    /**
     * 校验套餐结构的正确性
     *
     * @param pkgList
     * @param subgroupDOS
     * @param skuSubgroupDOS
     */
    private void validatePkgStructure(List<ItemDO> pkgList, List<SubgroupDO> subgroupDOS, List<RSkuSubgroupDO> skuSubgroupDOS) {
        pkgList.forEach(pkg -> {
            if (pkg.getHasAttr() != 0) {
                log.error("套餐" + pkg.getName() + "结构异常");
                throw new BusinessException("套餐" + pkg.getName() + "结构异常");
            }
            // 此套餐关联的分组
            List<SubgroupDO> subgroupDOList = subgroupDOS.stream()
                    .filter(subgroupDO -> subgroupDO.getItemGuid().equals(pkg.getGuid()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subgroupDOList)) {
                log.error("套餐" + pkg.getName() + "下分组不能为空");
                throw new BusinessException("套餐" + pkg.getName() + "下分组不能为空");
            }
            subgroupDOList.forEach(subgroupDO -> {
                // 此分组下与子规格关联的关系
                List<RSkuSubgroupDO> thisSubgroupSkuList = skuSubgroupDOS.stream()
                        .filter(skuSubgroup -> skuSubgroup.getSubgroupGuid().equals(subgroupDO.getGuid()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(thisSubgroupSkuList)) {
                    log.error("套餐" + pkg.getName() + "下分组" + subgroupDO.getName() + "下关联商品为空");
                    throw new BusinessException("套餐" + pkg.getName() + "下分组" + subgroupDO.getName() + "下关联商品为空");
                }
            });
        });
    }

    private void validateItemRackDTO(ItemRackDTO itemRackDTO) {
        Integer rackState = itemRackDTO.getRackState();
        if (rackState != null && rackState != 1 && rackState != 0) {
            throw new ParameterException("上下架状态字段传输错误");
        }
    }

    @Override
    public List<MappingRespDTO> mappingAllItems(String storeGuid) {
        if (StringUtils.isEmpty(storeGuid)) {
            throw new BusinessException("门店guid不能为空");
        }
        List<MappingRespDTO> mappingRespDTOS = new ArrayList<>();
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);

        // 查询商品对应的菜谱名称
        HashMap<String, String> planNameMap = Maps.newHashMap();
        //查询菜谱商品
        List<SkuDO> skuDOList = getRecipeModeSkuDOS(storeGuid, brandDTO.getGuid(), planNameMap);

        //门店商品
        final List<SkuDO> skuDOS = list(new LambdaQueryWrapper<SkuDO>().eq(SkuDO::getStoreGuid, storeGuid));
        //销售模式是否为菜谱模式
        boolean isPriceMde = Objects.equals(brandDTO.getSalesModel(), SalesModelEnum.RECIPE_MODE.getCode());
        //当前模式可选的skuGUID
        List<String> choiceSkuGuids = isPriceMde ? skuDOList.stream().map(SkuDO::getGuid).collect(Collectors.toList())
                : skuDOS.stream().map(SkuDO::getGuid).collect(Collectors.toList());
        skuDOList.addAll(skuDOS);
        if (CollectionUtils.isEmpty(skuDOList)) {
            log.info("门店下商品为空");
            return mappingRespDTOS;
        }
        List<SkuItemTypeBO> skuItemTypeBOList = selectSkuItemTypeBOList(skuDOList, choiceSkuGuids, planNameMap);
        if (CollectionUtils.isEmpty(skuItemTypeBOList)) {
            return mappingRespDTOS;
        }
        log.info("移除可选套餐和称重前，size: {}", skuItemTypeBOList.size());
        // 过滤可选套餐、宴会套餐和称重商品
        List<String> packageItemGuid = skuItemTypeBOList.stream()
                .filter(i -> Objects.equals(ItemTypeEnum.PACKAGE.getTypeCode(), i.getItemType()))
                .map(SkuItemTypeBO::getItemGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(packageItemGuid)) {
            List<SubgroupDO> subgroupDOList = subgroupMapper.selectList(new LambdaQueryWrapper<SubgroupDO>()
                    .in(SubgroupDO::getItemGuid, packageItemGuid));
            List<String> notFixedGuidList = subgroupDOList.stream()
                    .filter(sub -> 0 != sub.getPickNum())
                    .map(SubgroupDO::getItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notFixedGuidList)) {
                skuItemTypeBOList.removeIf(s -> notFixedGuidList.contains(s.getItemGuid()));
            }
        }
        skuItemTypeBOList.removeIf(skuItemTypeBO -> Objects.equals(ItemTypeEnum.WEIGHING.getTypeCode(), skuItemTypeBO.getItemType())
                || Objects.equals(ItemTypeEnum.GROUP.getTypeCode(), skuItemTypeBO.getItemType()));
        if (CollectionUtils.isEmpty(skuItemTypeBOList)) {
            return new ArrayList<>();
        }
        skuItemTypeBOList.sort(Comparator.comparing(SkuItemTypeBO::getId));
        skuItemTypeBOList.sort(Comparator.comparing(SkuItemTypeBO::getSort));
        log.info("移除套餐称重后，size: {}", skuItemTypeBOList.size());
        mappingRespDTOS.addAll(MapStructUtils.INSTANCE.skuItemTypeBOList2mappingRespDTOList(skuItemTypeBOList));
        return mappingRespDTOS;
    }

    @Override
    public Map<String, List<MappingRespDTO>> batchMappingAllItems(List<String> storeGuids) {
        Map<String, List<MappingRespDTO>> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(storeGuids)) {
            return resultMap;
        }
        StopWatch stopWatch = new StopWatch();
        // 查询门店的销售模式， 默认传入门店都所属同一品牌
        stopWatch.start("查询门店销售模式");
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuids.get(0));
        stopWatch.stop();
        // 商品对应的菜谱名称
        Map<String, Map<String, String>> planNameMap = Maps.newHashMap();
        // 查询门店菜谱sku
        stopWatch.start("查询门店菜谱sku");
        Map<String, List<SkuDO>> pricePlanSkuMap = batchQueryPricePlanItem(brandDTO.getGuid(), storeGuids, planNameMap);
        stopWatch.stop();
        // 查询门店sku
        stopWatch.start("查询门店sku");
        List<SkuDO> storeSkuList = list(new LambdaQueryWrapper<SkuDO>().in(SkuDO::getStoreGuid, storeGuids));
        stopWatch.stop();
        // 查询sku的itemType
        List<ItemDO> itemList = queryItemType(pricePlanSkuMap, storeSkuList);
        Map<String, ItemDO> itemMap = itemList.stream().collect(Collectors.toMap(ItemDO::getGuid, Function.identity(), (key1, key2) -> key1));
        // 销售模式是否为菜谱模式
        boolean isPriceMde = Objects.equals(brandDTO.getSalesModel(), SalesModelEnum.RECIPE_MODE.getCode());
        stopWatch.start("合并菜谱和门店商品");
        for (String storeGuid : storeGuids) {
            SkuItemMappingBO biz = new SkuItemMappingBO();
            // 菜谱sku
            List<SkuDO> innerPricePlanSkuList = pricePlanSkuMap.getOrDefault(storeGuid, Lists.newArrayList());
            // 门店sku
            List<SkuDO> innerStoreSkuList = storeSkuList.stream().filter(e -> storeGuid.equals(e.getStoreGuid())).collect(toList());
            // 当前模式可选的skuGUID
            List<String> choiceSkuGuids = isPriceMde ? innerPricePlanSkuList.stream().map(SkuDO::getGuid).collect(Collectors.toList())
                    : innerStoreSkuList.stream().map(SkuDO::getGuid).collect(Collectors.toList());
            biz.setChoiceSkuGuids(choiceSkuGuids);
            if (CollectionUtils.isNotEmpty(innerStoreSkuList)) {
                innerPricePlanSkuList.addAll(innerStoreSkuList);
            }
            biz.setInnerSkuList(innerPricePlanSkuList);
            // 商品item
            biz.setItemMap(itemMap);
            // 门店菜谱商品名称
            biz.setInnerPlanNameMap(planNameMap.getOrDefault(storeGuid, Maps.newHashMap()));
            resultMap.put(storeGuid, mergePricePlanSkuAndStoreSku(biz));
        }
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        return resultMap;
    }


    /**
     * 批量查询门店菜谱商品信息
     */
    private Map<String, List<SkuDO>> batchQueryPricePlanItem(String brandGuid, List<String> storeGuids, Map<String, Map<String, String>> planNameMap) {
        // 查询所有门店 sku
        List<PricePlanSkuRespDTO> pricePlanSkuRespList = planItemMapper.listPlanByStoreGuids(brandGuid, storeGuids);
        List<String> pricePlanSkuGuidList = pricePlanSkuRespList.stream().map(PricePlanSkuRespDTO::getSkuGuid).distinct().collect(toList());
        if (CollectionUtils.isEmpty(pricePlanSkuGuidList)){
            return Maps.newHashMap();
        }
        // 查询所有sku
        List<SkuDO> pricePlanSkuList = new ArrayList<>(listByIds(pricePlanSkuGuidList));
        // 品牌库商品名称
        Map<String, String> skuItemMap = pricePlanSkuList.stream()
                .collect(Collectors.toMap(SkuDO::getGuid, SkuDO::getName, (v1, v2) -> v1));
        // 根据门店分组
        Map<String, List<PricePlanSkuRespDTO>> planSkuGroupByStoreMap = pricePlanSkuRespList.stream()
                .collect(groupingBy(PricePlanSkuRespDTO::getStoreGuid));
        Map<String, List<SkuDO>> resultMap = Maps.newHashMap();
        // sku根据门店分组返回，构建菜谱商品名称集合
        for (Map.Entry<String, List<PricePlanSkuRespDTO>> entry : planSkuGroupByStoreMap.entrySet()) {
            List<PricePlanSkuRespDTO> planSkuRespGroupByStoreList = entry.getValue();
            // 构建菜谱商品名称集合
            planNameMap.put(entry.getKey(), generateItemNameByRecipeMode(planSkuRespGroupByStoreList, skuItemMap));
            // sku根据门店分组返回
            List<String> planSkuGuidGroupByStoreList = planSkuRespGroupByStoreList.stream().map(PricePlanSkuRespDTO::getSkuGuid).distinct().collect(toList());
            List<SkuDO> planSkuGroupByStoreList = pricePlanSkuList.stream().filter(e -> planSkuGuidGroupByStoreList.contains(e.getGuid())).collect(toList());
            resultMap.put(entry.getKey(), planSkuGroupByStoreList);
        }
        return resultMap;
    }


    private List<ItemDO> queryItemType(Map<String, List<SkuDO>> pricePlanSkuMap, List<SkuDO> storeSkuList) {
        List<SkuDO> pricePlanSkuList = pricePlanSkuMap.values().stream().flatMap(Collection::stream).collect(toList());
        if (CollectionUtils.isNotEmpty(storeSkuList)) {
            pricePlanSkuList.addAll(storeSkuList);
        }
        if (CollectionUtils.isEmpty(pricePlanSkuList)){
            return Lists.newArrayList();
        }
        List<String> itemGuidList = pricePlanSkuList.stream().map(SkuDO::getItemGuid).distinct().collect(toList());
        return new ArrayList<>(itemService.listByIds(itemGuidList));
    }

    /**
     * 构建菜谱商品名称集合
     */
    private Map<String, String> generateItemNameByRecipeMode(List<PricePlanSkuRespDTO> pricePlanSkuRespList, Map<String, String> skuItemMap) {
        Map<String, List<PricePlanSkuRespDTO>> planSkuMap = pricePlanSkuRespList.stream()
                .collect(groupingBy(PricePlanSkuRespDTO::getSkuGuid));
        Map<String, String> planNameMap = Maps.newHashMap();
        for (Map.Entry<String, List<PricePlanSkuRespDTO>> entry : planSkuMap.entrySet()) {
            String skuGuid = entry.getKey();
            String skuName = skuItemMap.get(skuGuid);
            List<PricePlanSkuRespDTO> planSkuList = entry.getValue();
            StringBuilder skuPlanName = new StringBuilder();
            planSkuList.forEach(item -> {
                if (skuPlanName.toString().contains(item.getPlanItemName())) {
                    return;
                }
                skuPlanName.append(item.getPlanItemName());
                if (!StringUtils.isEmpty(skuName)) {
                    skuPlanName.append("（").append(skuName).append("）");
                }
                skuPlanName.append("、");
            });
            if (!StringUtils.isEmpty(skuPlanName)) {
                String resultStr = skuPlanName.substring(0, skuPlanName.length() - 1);
                planNameMap.put(skuGuid, resultStr);
            }
        }
        return planNameMap;
    }

    private List<MappingRespDTO> mergePricePlanSkuAndStoreSku(SkuItemMappingBO biz) {
        List<MappingRespDTO> mappingRespList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(biz.getInnerSkuList())) {
            log.info("门店下商品为空");
            return mappingRespList;
        }
        List<SkuItemTypeBO> skuItemTypeBOList = skuList2SkuItemTypeBOList(biz);
        log.info("移除可选套餐和称重前，size: {}", skuItemTypeBOList.size());
        // 过滤可选套餐、宴会套餐和称重商品
        List<String> packageItemGuid = skuItemTypeBOList.stream()
                .filter(i -> Objects.equals(ItemTypeEnum.PACKAGE.getTypeCode(), i.getItemType()))
                .map(SkuItemTypeBO::getItemGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(packageItemGuid)) {
            List<SubgroupDO> subgroupDOList = subgroupMapper.selectList(new LambdaQueryWrapper<SubgroupDO>()
                    .in(SubgroupDO::getItemGuid, packageItemGuid));
            List<String> notFixedGuidList = subgroupDOList.stream()
                    .filter(sub -> 0 != sub.getPickNum())
                    .map(SubgroupDO::getItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notFixedGuidList)) {
                skuItemTypeBOList.removeIf(s -> notFixedGuidList.contains(s.getItemGuid()));
            }
        }
        skuItemTypeBOList.removeIf(skuItemTypeBO -> Objects.equals(ItemTypeEnum.WEIGHING.getTypeCode(), skuItemTypeBO.getItemType())
                || Objects.equals(ItemTypeEnum.GROUP.getTypeCode(), skuItemTypeBO.getItemType()));
        if (CollectionUtils.isEmpty(skuItemTypeBOList)) {
            return Lists.newArrayList();
        }
        log.info("移除套餐称重后，size: {}", skuItemTypeBOList.size());
        mappingRespList.addAll(MapStructUtils.INSTANCE.skuItemTypeBOList2mappingRespDTOList(skuItemTypeBOList));
        return mappingRespList;
    }

    private List<SkuItemTypeBO> skuList2SkuItemTypeBOList(SkuItemMappingBO biz) {
        // 当前模式可选的skuGUID
        List<String> choiceSkuGuids = biz.getChoiceSkuGuids();
        List<SkuDO> innerSkuList = biz.getInnerSkuList();
        Map<String, String> innerPlanNameMap = biz.getInnerPlanNameMap();
        Map<String, ItemDO> itemMap = biz.getItemMap();

        List<SkuItemTypeBO> skuItemTypeBOList = new ArrayList<>();
        innerSkuList.forEach(skuDO -> {
            ItemDO itemDO = itemMap.get(skuDO.getItemGuid());
            if (Objects.isNull(itemDO)) {
                return;
            }
            SkuItemTypeBO skuItemTypeBO = new SkuItemTypeBO();
            skuItemTypeBO.setItemGuid(skuDO.getItemGuid());
            skuItemTypeBO.setItemName(itemDO.getName());
            skuItemTypeBO.setSkuGuid(skuDO.getGuid());
            skuItemTypeBO.setSkuName(skuDO.getName());
            skuItemTypeBO.setItemType(itemDO.getItemType());
            skuItemTypeBO.setCode(skuDO.getCode());
            skuItemTypeBO.setIsRack(skuDO.getIsRack());
            skuItemTypeBO.setParentSkuGuid(skuDO.getGuid());
            if (!ObjectUtils.isEmpty(skuDO.getParentGuid())) {
                skuItemTypeBO.setParentSkuGuid(skuDO.getParentGuid());
            }
            if (CollectionUtils.isNotEmpty(choiceSkuGuids) && choiceSkuGuids.contains(skuDO.getGuid())) {
                skuItemTypeBO.setIsChoice(1);
            }
            String planItemName = innerPlanNameMap.get(skuDO.getGuid());
            if (!StringUtils.isEmpty(planItemName)) {
                skuItemTypeBO.setPlanItemName(planItemName);
            }
            skuItemTypeBOList.add(skuItemTypeBO);
        });
        return skuItemTypeBOList;
    }

    /**
     * 获取商品对应的菜品名称map
     *
     * @param brandGuid 品牌guid
     * @param skuDOList 商品sku列表
     * @return 商品对应的菜品名称map
     */
    @Override
    public HashMap<String, String> getPlanNameMap(String brandGuid, List<SkuDO> skuDOList) {
        Set<String> skuGuidSet = skuDOList.stream()
                .map(SkuDO::getGuid)
                .collect(Collectors.toSet());
        Map<String, String> skuItemMap = skuDOList.stream()
                .collect(Collectors.toMap(SkuDO::getGuid, SkuDO::getName, (v1, v2) -> v1));
        List<PricePlanDO> pricePlanDOList = pricePlanMapper.selectList(new LambdaQueryWrapper<PricePlanDO>()
                .in(!StringUtils.isEmpty(brandGuid), PricePlanDO::getBrandGuid, brandGuid)
                .eq(PricePlanDO::getIsDelete, 0)
                .and(p -> p.eq(PricePlanDO::getStatus, PricePlanStatusEnum.USING.getCode())
                        .or()
                        .eq(PricePlanDO::getStatus, PricePlanStatusEnum.RIGHT_AWAY_DISABLE.getCode())
                )
        );
        if (CollectionUtils.isEmpty(pricePlanDOList)) {
            log.warn("品牌下未查询到可以用方案，brandGuid={}", brandGuid);
            return Maps.newHashMap();
        }
        List<String> planGuidList = pricePlanDOList.stream()
                .map(PricePlanDO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        List<PricePlanItemDO> planItemDOList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                .in(PricePlanItemDO::getPlanGuid, planGuidList)
                .in(PricePlanItemDO::getSkuGuid, skuGuidSet)
                .eq(PricePlanItemDO::getIsDelete, 0)
        );
        if (CollectionUtils.isEmpty(planItemDOList)) {
            log.warn("未查询到方案商品信息，planGuidList={} skuGuidSet={}", planGuidList, skuGuidSet);
            return Maps.newHashMap();
        }
        HashMap<String, String> planNameMap = new HashMap<>();
        return buildPlanItemNameMap(planItemDOList, skuItemMap, planNameMap);
    }

    private HashMap<String, String> buildPlanItemNameMap(List<PricePlanItemDO> planItemDOList, Map<String, String> skuItemMap, HashMap<String, String> planNameMap) {

        Map<String, List<PricePlanItemDO>> planSkuMap = planItemDOList.stream()
                .collect(groupingBy(PricePlanItemDO::getSkuGuid));
        for (Map.Entry<String, List<PricePlanItemDO>> entry : planSkuMap.entrySet()) {
            String skuGuid = entry.getKey();
            String skuName = skuItemMap.get(skuGuid);
            List<PricePlanItemDO> planSkuList = entry.getValue();
            StringBuilder planItemSkuName = new StringBuilder();
            planSkuList.forEach(item -> {
                if (planItemSkuName.toString().contains(item.getPlanItemName())) {
                    return;
                }
                planItemSkuName.append(item.getPlanItemName());
                if (!StringUtils.isEmpty(skuName)) {
                    planItemSkuName.append("（").append(skuName).append("）");
                }
                planItemSkuName.append("、");
            });
            if (!StringUtils.isEmpty(planItemSkuName)) {
                String resultStr = planItemSkuName.substring(0, planItemSkuName.length() - 1);
                planNameMap.put(skuGuid, resultStr);
            }
        }
        return planNameMap;
    }

    @Override
    public List<MappingRespDTO> mappingRecipeModeItems(String storeGuid) {
        if (StringUtils.isEmpty(storeGuid)) {
            throw new BusinessException("门店guid不能为空");
        }
        List<MappingRespDTO> mappingRespDTOS = new ArrayList<>();
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        //销售模式是否为菜谱模式
        boolean isPriceMde = Objects.equals(brandDTO.getSalesModel(), SalesModelEnum.RECIPE_MODE.getCode());
        List<SkuItemTypeBO> skuItemTypeList;
        if (isPriceMde) {
            //查询菜谱商品
            List<SkuInfoRespDTO> recipeModeSkuList = getRecipeModeSkuListForDefaultPlan(storeGuid, null);
            if (CollectionUtils.isEmpty(recipeModeSkuList)) {
                return mappingRespDTOS;
            }
            skuItemTypeList = skuInfoRespList2SkuItemTypeBOList(recipeModeSkuList);
        } else {
            //门店商品
            List<SkuDO> skuResult = list(new LambdaQueryWrapper<SkuDO>().eq(SkuDO::getStoreGuid, storeGuid));
            if (CollectionUtils.isEmpty(skuResult)) {
                return mappingRespDTOS;
            }
            // 当前模式可选的skuGUID
            List<String> choiceSkuGuids = skuResult.stream().map(SkuDO::getGuid).collect(Collectors.toList());
            skuItemTypeList = selectSkuItemTypeBOList(skuResult, choiceSkuGuids, null);
        }

        if (CollectionUtils.isEmpty(skuItemTypeList)) {
            return mappingRespDTOS;
        }
        log.info("移除可选套餐和称重前，size: {}, body: {}",
                skuItemTypeList.size(), JacksonUtils.writeValueAsString(skuItemTypeList));
        // 过滤可选套餐、宴会套餐和称重商品
        List<String> packageItemGuidList = skuItemTypeList.stream()
                .filter(i -> Objects.equals(ItemTypeEnum.PACKAGE.getTypeCode(), i.getItemType()))
                .map(SkuItemTypeBO::getItemGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(packageItemGuidList)) {
            List<SubgroupDO> subgroupDOList = subgroupMapper.selectList(new LambdaQueryWrapper<SubgroupDO>()
                    .in(SubgroupDO::getItemGuid, packageItemGuidList));
            List<String> notFixedGuidList = subgroupDOList.stream()
                    .filter(sub -> 0 != sub.getPickNum())
                    .map(SubgroupDO::getItemGuid)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notFixedGuidList)) {
                skuItemTypeList.removeIf(s -> notFixedGuidList.contains(s.getItemGuid()));
            }
        }
        skuItemTypeList.removeIf(skuItemTypeBO -> Objects.equals(ItemTypeEnum.WEIGHING.getTypeCode(), skuItemTypeBO.getItemType())
                || Objects.equals(ItemTypeEnum.GROUP.getTypeCode(), skuItemTypeBO.getItemType()));
        if (CollectionUtils.isEmpty(skuItemTypeList)) {
            return new ArrayList<>();
        }
        skuItemTypeList.sort(Comparator.comparing(SkuItemTypeBO::getId));
        skuItemTypeList.sort(Comparator.comparing(SkuItemTypeBO::getSort));
        log.info("移除套餐称重后，size: {}, body: {}",
                skuItemTypeList.size(), JacksonUtils.writeValueAsString(skuItemTypeList));
        mappingRespDTOS.addAll(MapStructUtils.INSTANCE.skuItemTypeBOList2mappingRespDTOList(skuItemTypeList));
        return mappingRespDTOS;
    }


    /**
     * 查询菜谱模式下该门店菜谱商品（只会查询全时段菜谱）
     */
    private List<SkuInfoRespDTO> getRecipeModeSkuListForDefaultPlan(String storeGuid, List<String> skuGuids) {
        List<SkuInfoRespDTO> skuInfoRespDTOS = Lists.newArrayList();
        // 根据门店查询生效的菜谱
        LocalDateTime now = LocalDateTime.now();
        // String minute = (now.getMinute() + "").length() == 1 ? "0" + now.getMinute() : now.getMinute() + "";
        // int nowTime = Integer.parseInt(now.getHour() + "" + minute);
        // log.info("当前时间：" + nowTime);

        List<PricePlanNowDTO> planNowList = pricePlanStoreService.findPlanNowStoreGuid(now, storeGuid);
        if (CollectionUtil.isEmpty(planNowList)) {
            log.warn("没有生效的菜谱方案 now={},storeGuid={}", now.toString(), storeGuid);
            return skuInfoRespDTOS;
        }

        // 有生效的菜谱
        PricePlanNowDTO pricePlanNowAllTime = null;
        for (PricePlanNowDTO planNow : planNowList) {
            Integer sellTimeType = planNow.getSellTimeType();
            if (sellTimeType == 0) {
                // 全时段菜品方案
                pricePlanNowAllTime = planNow;
            }
        }

        log.info("生效的菜谱：pricePlanNowAllTime={}", JacksonUtils.writeValueAsString(pricePlanNowAllTime));
        // 没有菜谱方案
        if (Objects.isNull(pricePlanNowAllTime)) {
            log.warn("没有生效的菜谱方案,storeGuid:{}", storeGuid);
            return skuInfoRespDTOS;
        }

        List<PricePlanItemDO> pricePlanItemDOList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                .ne(PricePlanItemDO::getIsSoldOut, ItemStateEnum.IMMEDIATELY_DELETE.getCode())
                .eq(PricePlanItemDO::getIsDelete, 0)
                .eq(PricePlanItemDO::getPlanGuid, pricePlanNowAllTime.getPlanGuid())
                .in(CollectionUtils.isNotEmpty(skuGuids), PricePlanItemDO::getSkuGuid, skuGuids)
        );

        Map<String, PricePlanItemDO> planSkuMap = pricePlanItemDOList.stream()
                .collect(Collectors.toMap(PricePlanItemDO::getSkuGuid, s -> s));

        List<SkuDO> recipeModeSkuList = list(
                new LambdaQueryWrapper<SkuDO>()
                        .in(SkuDO::getGuid, new ArrayList<>(planSkuMap.keySet())));

        // 通过菜谱和商品guid查询商品详情
        List<String> itemGuidList = recipeModeSkuList.stream()
                .map(SkuDO::getItemGuid)
                .collect(Collectors.toList());

        List<ItemDO> list = itemService.list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, itemGuidList));
        Map<String, ItemDO> itemDOMap = list.stream().collect(Collectors.toMap(ItemDO::getGuid, i -> i));

        skuInfoRespDTOS = MapStructUtils.INSTANCE.skuDOList2skuInfoRespDTOList(recipeModeSkuList);
        log.info("查询商品结果：{}", JacksonUtils.writeValueAsString(skuInfoRespDTOS));

        // 设置菜谱的价格
        if (CollectionUtils.isNotEmpty(recipeModeSkuList)) {
            skuInfoRespDTOS.forEach(sku -> {
                ItemDO itemDO = itemDOMap.get(sku.getItemGuid());
                if (ObjectUtils.isEmpty(itemDO)) {
                    log.warn("未查询到商品信息 itemGuid={}", sku.getItemGuid());
                    return;
                }
                // 默认设置品牌库信息
                sku.setItemType(itemDO.getItemType());
                sku.setItemName(itemDO.getName());

                if (!StringUtils.isEmpty(sku.getParentGuid())) {
                    sku.setSkuGuid(sku.getParentGuid());
                }

                if (!StringUtils.isEmpty(itemDO.getParentGuid())) {
                    sku.setItemGuid(itemDO.getParentGuid());
                }

                // 如果是菜谱商品,则设置菜谱信息
                PricePlanItemDO planSku = planSkuMap.get(sku.getSkuGuid());
                if (ObjectUtils.isEmpty(planSku)) {
                    log.warn("菜谱中未查询到商品信息 skuGuid={}", sku.getSkuGuid());
                    return;
                }
                sku.setItemName(planSku.getPlanItemName());
                sku.setSalePrice(planSku.getSalePrice());
                sku.setTypeGuid(planSku.getTypeGuid());
                sku.setTakeawayAccountingPrice(planSku.getTakeawayAccountingPrice());
                sku.setAccountingPrice(planSku.getAccountingPrice());
            });
        }
        return skuInfoRespDTOS;
    }

    private List<SkuItemTypeBO> skuInfoRespList2SkuItemTypeBOList(List<SkuInfoRespDTO> recipeModeSkuList) {
        List<SkuItemTypeBO> skuItemTypeBOList = new ArrayList<>();

        Set<String> typeGuidSet = recipeModeSkuList.stream()
                .map(SkuInfoRespDTO::getTypeGuid)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(typeGuidSet)) {
            return new ArrayList<>();
        }

        List<TypeDO> typeDOList = (ArrayList) typeService.listByIds(typeGuidSet);
        Map<String, TypeDO> typeMap = typeDOList.stream()
                .collect(Collectors.toMap(TypeDO::getGuid, Function.identity(), (key1, key2) -> key1));

        recipeModeSkuList.forEach(skuDO -> {
            TypeDO thisTypeDO = typeMap.get(skuDO.getTypeGuid());
            if (Objects.isNull(thisTypeDO)) {
                return;
            }

            SkuItemTypeBO skuItemTypeBO = new SkuItemTypeBO();
            skuItemTypeBO.setItemGuid(skuDO.getItemGuid());
            skuItemTypeBO.setSkuGuid(skuDO.getSkuGuid());
            skuItemTypeBO.setTypeGuid(thisTypeDO.getGuid());
            skuItemTypeBO.setItemName(skuDO.getItemName());
            skuItemTypeBO.setSkuName(skuDO.getName());
            skuItemTypeBO.setTypeName(thisTypeDO.getName());
            skuItemTypeBO.setCode(skuDO.getCode());
            skuItemTypeBO.setItemType(skuDO.getItemType());
            skuItemTypeBO.setSalePrice(skuDO.getSalePrice());
            skuItemTypeBO.setUnit(skuDO.getUnit());
            skuItemTypeBO.setSort(thisTypeDO.getSort());
            skuItemTypeBO.setId(thisTypeDO.getId());
            skuItemTypeBO.setIsRack(skuDO.getIsRack());
            skuItemTypeBO.setSalePrice(skuDO.getSalePrice());
            skuItemTypeBO.setParentSkuGuid(skuDO.getSkuGuid());
            skuItemTypeBO.setParentItemGuid(skuDO.getItemGuid());
            skuItemTypeBO.setIsChoice(1);

            skuItemTypeBO.setTakeawayAccountingPrice(skuDO.getTakeawayAccountingPrice());
            skuItemTypeBO.setAccountingPrice(skuDO.getAccountingPrice());

            skuItemTypeBOList.add(skuItemTypeBO);
        });
        return skuItemTypeBOList;
    }


    @Override
    public List<MappingRespDTO> mapping(String storeGuid) {
        if (StringUtils.isEmpty(storeGuid)) {
            throw new BusinessException("门店guid不能为空");
        }
        List<MappingRespDTO> mappingRespDTOS = new ArrayList<>();
        List<SkuDO> skuDOList;

        // 判断销售模式--商品列表展示门店关联的全部菜谱商品（已启用和即将启用状态的菜谱）
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        if (Objects.nonNull(brandDTO) && Objects.equals(brandDTO.getSalesModel(), SalesModelEnum.RECIPE_MODE.getCode())) {
            skuDOList = getRecipeModeSkuDOS(storeGuid, brandDTO.getGuid(), null);
        } else {
            // 查询除了套餐外的菜品,对上下架不做限定，传入上下架字段，由调用方处理
            skuDOList = list(
                    new LambdaQueryWrapper<SkuDO>()
                            .eq(SkuDO::getStoreGuid, storeGuid)
            );
        }

        if (CollectionUtils.isEmpty(skuDOList)) {
            return mappingRespDTOS;
        }
        List<SkuItemTypeBO> skuItemTypeBOList = selectSkuItemTypeBOList(skuDOList, null, null);
        if (CollectionUtils.isEmpty(skuItemTypeBOList)) {
            return mappingRespDTOS;
        }
        log.info("移除套餐称重前，size: {}, body: {}",
                skuItemTypeBOList.size(), JacksonUtils.writeValueAsString(skuItemTypeBOList));
        skuItemTypeBOList.removeIf(skuItemTypeBO -> skuItemTypeBO.getItemType() == 1
                || skuItemTypeBO.getItemType() == 3);
        if (CollectionUtils.isEmpty(skuItemTypeBOList)) {
            return new ArrayList<>();
        }
        skuItemTypeBOList.sort(Comparator.comparing(SkuItemTypeBO::getId));
        skuItemTypeBOList.sort(Comparator.comparing(SkuItemTypeBO::getSort));
        log.info("移除套餐称重后，size: {}, body: {}",
                skuItemTypeBOList.size(), JacksonUtils.writeValueAsString(skuItemTypeBOList));
        mappingRespDTOS.addAll(MapStructUtils.INSTANCE.skuItemTypeBOList2mappingRespDTOList(skuItemTypeBOList));
        return mappingRespDTOS;
    }

    /**
     * 菜品模式sku
     *
     * @param storeGuid
     * @param brandGuid
     * @return
     */
    private List<SkuDO> getRecipeModeSkuDOS(String storeGuid, String brandGuid, HashMap<String, String> planNameMap) {
        //获取菜谱商品信息
        List<PricePlanItemDO> planItemDOList = planItemRepository.listAllByStoreAndBrand(storeGuid, brandGuid);
        if(CollectionUtils.isEmpty(planItemDOList)){
            return Lists.newArrayList();
        }
        List<String> skuGuids = planItemDOList.stream()
                .map(PricePlanItemDO::getSkuGuid)
                .distinct()
                .collect(Collectors.toList());
        List<SkuDO> skuDOList = this.list(new LambdaQueryWrapper<SkuDO>()
                .in(SkuDO::getGuid, skuGuids));
        if (planNameMap != null) {
            //查询菜谱商品名称集合
            Map<String, String> skuItemMap = skuDOList.stream()
                    .collect(Collectors.toMap(SkuDO::getGuid, SkuDO::getName, (v1, v2) -> v1));
            buildPlanItemNameMap(planItemDOList, skuItemMap, planNameMap);
        }
        return skuDOList;
    }

    /**
     * 通过skuList获取itemList
     *
     * @param skuDOList skuList
     * @return List<ItemDO>
     */
    private List<ItemDO> getItemListBySkuList(List<SkuDO> skuDOList) {
        //通过skulist获取itemguid
        List<String> itemGuidList = skuDOList.stream()
                .map(SkuDO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemGuidList)) {
            return new ArrayList<>();
        }
        //通过itemguid获取商品列表
        return itemMapper.selectList(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getGuid, itemGuidList)
                .eq(ItemDO::getIsEnable, Boolean.TRUE));
    }


    @Override
    public List<MappingRespDTO> kdsMapping(ItemSingleDTO itemSingleDTO) {
        String storeGuid = itemSingleDTO.getStoreGuid();
        List<MappingRespDTO> mappingRespDTOS = new ArrayList<>();
        List<SkuDO> skuDOStoreList = new ArrayList<>();

        // kds商品区分销售模式
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        log.info("菜品模式数据：{}", JacksonUtils.writeValueAsString(brandDTO));
        if (!ObjectUtils.isEmpty(brandDTO.getSalesModel()) && brandDTO.getSalesModel() == SalesModelEnum.RECIPE_MODE.getCode()) {
            // 开启菜谱方式模式
            skuDOStoreList = itemService.pricePlanItemSku(storeGuid, null);
        } else {
            // 非菜谱菜品规格  查询除了套餐外的菜品,对上下架不做限定
            skuDOStoreList = list(new LambdaQueryWrapper<SkuDO>()
                    .eq(SkuDO::getIsEnable, true)
                    .eq(SkuDO::getIsRack, 1)
                    .eq(SkuDO::getStoreGuid, storeGuid));
        }

        // 合并菜谱方案和普通模式菜品
//        skuDOStoreList.removeAll(skuDOPlanList);
//        skuDOStoreList.addAll(skuDOPlanList);

        if (CollectionUtils.isEmpty(skuDOStoreList)) {
            return mappingRespDTOS;
        }
        //品牌库商品商品GUID
        List<String> brandSkuGuids = skuDOStoreList.stream()
                .filter(skuDO -> skuDO.getSkuFrom() == 1)
                .map(SkuDO::getGuid).collect(Collectors.toList());
        //spu重复的门店SKU信息
        List<SkuDO> repetitionSkus = skuDOStoreList.stream()
                .filter(skuDO -> skuDO.getSkuFrom() == 2 && brandSkuGuids.contains(skuDO.getParentGuid()))
                .collect(Collectors.toList());
        List<String> bindSkuGuids = itemSingleDTO.getSkuGuids();
        List<String> removeSkuGuids = new ArrayList<>();
        for (SkuDO skuDO : repetitionSkus) {
            //已绑定商品包含门店商品  品牌库商品不展示
            if (CollectionUtils.isNotEmpty(bindSkuGuids) && bindSkuGuids.contains(skuDO.getGuid())) {
                List<SkuDO> brandSkus = skuDOStoreList.stream()
                        .filter(sku -> sku.getGuid().equals(skuDO.getParentGuid()))
                        .collect(Collectors.toList());
                brandSkus.forEach(sku -> removeSkuGuids.add(sku.getGuid()));
            } else {
                //展示品牌库商品
                removeSkuGuids.add(skuDO.getGuid());
            }
        }
        skuDOStoreList.removeIf(skuDO -> removeSkuGuids.contains(skuDO.getGuid()));

        // 套餐商品将子商品加入进kds查询的商品里
        List<ItemDO> itemDOList = getItemListBySkuList(skuDOStoreList);
        if (CollectionUtils.isEmpty(itemDOList)) {
            log.warn("itemDOList为空");
            return mappingRespDTOS;
        }
        List<String> pkgItemGuidList = itemDOList.stream()
                .filter(i -> Objects.equals(1, i.getItemType()))
                .map(ItemDO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(pkgItemGuidList)) {
            List<RSkuSubgroupDO> subgroupDOList = subgroupService.selectSkuSubgroupByItemGuidList(pkgItemGuidList);
            List<String> skuGuidList = subgroupDOList.stream()
                    .map(RSkuSubgroupDO::getSkuGuid)
                    .distinct()
                    .collect(Collectors.toList());
            skuGuidList.removeAll(removeSkuGuids);
            if (CollectionUtils.isNotEmpty(skuGuidList)) {
                List<SkuDO> subSkuDOList = this.list(new LambdaQueryWrapper<SkuDO>()
                        .in(SkuDO::getGuid, skuGuidList)
                        .eq(SkuDO::getIsDelete, 0)
                        .eq(SkuDO::getIsEnable, 1));
                replaceSubSkuDOList(skuDOStoreList, subSkuDOList);
            }
        }
        List<SkuItemTypeBO> skuItemTypeBOList = selectSkuItemTypeBOList2(skuDOStoreList);
        if (!CollectionUtils.isNotEmpty(skuItemTypeBOList)) {
            return mappingRespDTOS;
        }

        log.info("移除套餐称重前，size: {}, body: {}",
                skuItemTypeBOList.size(), JacksonUtils.writeValueAsString(skuItemTypeBOList));
        skuItemTypeBOList.removeIf(skuItemTypeBO -> skuItemTypeBO.getItemType() == 1);

        if (CollectionUtils.isEmpty(skuItemTypeBOList)) {
            return new ArrayList<>();
        }
        skuItemTypeBOList.sort(Comparator.comparing(SkuItemTypeBO::getId));
        skuItemTypeBOList.sort(Comparator.comparing(SkuItemTypeBO::getSort));
        log.info("移除套餐后，size: {}, body: {}",
                skuItemTypeBOList.size(), JacksonUtils.writeValueAsString(skuItemTypeBOList));
        mappingRespDTOS.addAll(MapStructUtils.INSTANCE.skuItemTypeBOList2mappingRespDTOList(skuItemTypeBOList));
        log.info("KDS 菜品绑定结果: {}", JacksonUtils.writeValueAsString(mappingRespDTOS));
        return mappingRespDTOS;
    }

    private void replaceSubSkuDOList(List<SkuDO> skuDOStoreList, List<SkuDO> subSkuDOList) {
        // 菜谱商品名称
        Map<String, SkuDO> skuDOStoreMap = skuDOStoreList.stream()
                .collect(Collectors.toMap(SkuDO::getGuid, Function.identity(), (key1, key2) -> key1));
        skuDOStoreList.removeAll(subSkuDOList);
        skuDOStoreList.addAll(subSkuDOList);
        for (SkuDO skuDO : skuDOStoreList) {
            skuDO.setPlanItemName(skuDOStoreMap.getOrDefault(skuDO.getGuid(), new SkuDO()).getPlanItemName());
        }
    }

    @Override
    public List<SkuTakeawayInfoRespDTO> selectSkuTakeawayInfoRespDTOList(ItemStringListDTO itemStringListDTO) {
        List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(itemStringListDTO.getDataList())) {
            return skuTakeawayInfoRespDTOS;
        }
        if (CollectionUtils.isNotEmpty(itemStringListDTO.getDataList())) {
            //查询SKU
            List<SkuDO> skuDOList = getSkuDOS(itemStringListDTO);
            if (CollectionUtils.isEmpty(skuDOList)) {
                return skuTakeawayInfoRespDTOS;
            }
            // 规格详情集合
            List<SkuItemTypeBO> skuItemTypeList = selectSkuItemTypeBOList(skuDOList, null, null);
            if (CollectionUtils.isEmpty(skuItemTypeList)) {
                return skuTakeawayInfoRespDTOS;
            }
            // 称重、非固定套餐不能参与外卖下单
            List<SkuItemTypeBO> fixedPackageList = skuItemTypeList.stream()
                    .filter(skuItemTypeBO -> skuItemTypeBO.getItemType() == 1)
                    .filter(skuItemTypeBO -> {
                        List<SubgroupBO> subgroupBOS = subgroupService.selectSubgroupListByItemGuidList(
                                Collections.singletonList(skuItemTypeBO.getItemGuid()));
                        return subgroupBOS.stream()
                                .map(SubgroupBO::getIsFixSubgroup).allMatch(integer -> 1 == integer);
                    })
                    .collect(Collectors.toList());
            skuItemTypeList.removeIf(skuItemTypeBO -> skuItemTypeBO.getItemType() == 1
                    || skuItemTypeBO.getItemType() == 3);
            skuItemTypeList.addAll(fixedPackageList);
            if (CollectionUtils.isEmpty(skuItemTypeList)) {
                return skuTakeawayInfoRespDTOS;
            }
            skuTakeawayInfoRespDTOS.addAll(MapStructUtils.INSTANCE.skuItemTypeBOList2skuTakeawayInfoRespDTOList(skuItemTypeList));
        }
        return skuTakeawayInfoRespDTOS;
    }


    @Override
    public List<SkuTakeawayInfoRespDTO> selectSkuTakeawayInfoRespDTOListV2(ItemStringListDTO itemStringListDTO) {
        List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(itemStringListDTO.getDataList())) {
            return skuTakeawayInfoRespDTOS;
        }
        String storeGuid = itemStringListDTO.getStoreGuid();
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        //销售模式是否为菜谱模式
        boolean isPriceMde = Objects.equals(brandDTO.getSalesModel(), SalesModelEnum.RECIPE_MODE.getCode());
        List<SkuItemTypeBO> skuItemTypeBOList;
        if (isPriceMde) {
            // 菜谱商品
            List<SkuInfoRespDTO> recipeModeSkuList = getRecipeModeSkuListForDefaultPlan(storeGuid, itemStringListDTO.getDataList());
            if (CollectionUtils.isEmpty(recipeModeSkuList)) {
                return skuTakeawayInfoRespDTOS;
            }
            skuItemTypeBOList = skuInfoRespList2SkuItemTypeBOList(recipeModeSkuList);
        } else {
            // 门店商品
            List<SkuDO> skuResult = list(new LambdaQueryWrapper<SkuDO>().eq(SkuDO::getStoreGuid, storeGuid)
                    .and(w -> w.in(SkuDO::getParentGuid, itemStringListDTO.getDataList())
                            .or()
                            .in(SkuDO::getGuid, itemStringListDTO.getDataList()))
            );
            if (CollectionUtils.isEmpty(skuResult)) {
                return skuTakeawayInfoRespDTOS;
            }
            skuItemTypeBOList = selectSkuItemTypeBOList(skuResult, null, null);
        }

        if (CollectionUtils.isEmpty(skuItemTypeBOList)) {
            return skuTakeawayInfoRespDTOS;
        }
        // 称重、非固定套餐不能参与外卖下单
        List<SkuItemTypeBO> fixedPackage = skuItemTypeBOList
                .stream()
                .filter(skuItemTypeBO
                        -> skuItemTypeBO.getItemType() == 1)
                .filter(skuItemTypeBO -> {
                    List<SubgroupBO> subgroupBOS = subgroupService.selectSubgroupListByItemGuidList(
                            Collections.singletonList(skuItemTypeBO.getItemGuid()));
                    return subgroupBOS
                            .stream()
                            .map(SubgroupBO::getIsFixSubgroup).
                                    allMatch(integer -> 1 == integer);
                })
                .collect(Collectors.toList());
        skuItemTypeBOList.removeIf(skuItemTypeBO -> skuItemTypeBO.getItemType() == 1
                || skuItemTypeBO.getItemType() == 3);
        skuItemTypeBOList.addAll(fixedPackage);
        if (CollectionUtils.isEmpty(skuItemTypeBOList)) {
            return skuTakeawayInfoRespDTOS;
        }
        skuTakeawayInfoRespDTOS.addAll(MapStructUtils.INSTANCE.skuItemTypeBOList2skuTakeawayInfoRespDTOList(skuItemTypeBOList));
        return skuTakeawayInfoRespDTOS;
    }

    /**
     * 外卖查询SKU 如果有重复的需去重
     *
     * @param itemStringListDTO 请求参数
     * @return SKU集合
     */
    private List<SkuDO> getSkuDOS(ItemStringListDTO itemStringListDTO) {
        //查询门店菜品信息
        List<SkuDO> skuDOList = list(new LambdaQueryWrapper<SkuDO>().
                or(i -> i.in(SkuDO::getGuid, itemStringListDTO.getDataList())
                        .eq(SkuDO::getStoreGuid, itemStringListDTO.getStoreGuid()))
                .or(i -> i.in(SkuDO::getParentGuid, itemStringListDTO.getDataList())
                        .eq(SkuDO::getStoreGuid, itemStringListDTO.getStoreGuid()))
        );
        List<String> storeParentSkuGuids = skuDOList.stream().map(SkuDO::getParentGuid).collect(Collectors.toList());
        //查询菜谱方案菜品
        List<SkuDO> skuDOS = itemService.pricePlanItemSku(itemStringListDTO.getStoreGuid(), itemStringListDTO.getDataList());
        List<String> planSkuGuids = skuDOS.stream().map(SkuDO::getGuid).collect(Collectors.toList());
        //菜谱和门店有相同SPU去重
        if (CollectionUtils.isNotEmpty(skuDOList) && CollectionUtils.isNotEmpty(skuDOS)) {
            storeParentSkuGuids.retainAll(planSkuGuids);
            //有重复
            if (CollectionUtils.isNotEmpty(storeParentSkuGuids)) {
                //根据销售模式判断下单的商品
                BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(itemStringListDTO.getStoreGuid());
                if (SalesModelEnum.NORMAL_MODE.getCode() == brandDTO.getSalesModel()) {
                    skuDOS.removeIf(skuDO -> storeParentSkuGuids.contains(skuDO.getGuid()));
                } else {
                    skuDOList.removeIf(skuDO -> planSkuGuids.contains(skuDO.getParentGuid()));
                }
            }

        }
        skuDOList.addAll(skuDOS);
        return skuDOList;
    }

    @Override
    @Transactional
    public Integer deleteSkuByIds(List<String> guidList, boolean judgePlanItem) {
        if (CollectionUtils.isEmpty(guidList)) {
            return 0;
        }
        if (judgePlanItem) {
            // 如果商品的规格被删除，而改该规格被价格方案使用，则无法删除，并给出使用的价格方案名
            toJudgePlanItem(guidList);
        }
        // 因为产品说要在删除规格的地方删除，所以这个接口由前端调用进行判断
        // 如果该商品已设置为积分兑换商品，则不能删除规格
        /*MemberResult<List<ResponseIntegralConversionItemSku>> skuBySkuGuid = merchantClientService
                .queryConversionItemSkuBySkuGuid(guidList);
        if (!CollectionUtils.isEmpty(skuBySkuGuid.getTData())) {
            throw new BusinessException("已配置积分兑换规则的商品规格不能直接删除，需先到积分管理-消耗规则-兑换门店商品中删除关联规则");
        }*/

        // 删除被当前规格推送的规格
        deletePushSku(guidList);
        boolean removeByIds = removeByIds(guidList);
        // 删除永久停用菜谱里对应规格
        if (removeByIds) {
            deletePlanSku(guidList);
        }

        // 当商品关联了套餐时，将当前商品从对应套餐中移除;若涉及商品此前关联了套餐，则判断最新的套餐是否有子菜全部被清除的，若存在，则删除套餐
        deleteGroupSku(guidList);
        return removeByIds ? 1 : 0;
    }

    private void deleteGroupSku(List<String> guidList) {
        List<RSkuSubgroupDO> relatedSkuSubgroups = skuSubgroupService.list(new LambdaQueryWrapper<RSkuSubgroupDO>()
                .in(RSkuSubgroupDO::getSkuGuid, guidList));
        if (CollectionUtils.isEmpty(relatedSkuSubgroups)) {
            return;
        }
        Collection<SubgroupDO> subgroupList = subgroupService.listByIds(relatedSkuSubgroups.stream()
                .map(RSkuSubgroupDO::getSubgroupGuid).distinct().collect(Collectors.toList()));
        List<String> message = new ArrayList<>();
        Collection<ItemDO> itemList = itemMapper.selectBatchIds(subgroupList.stream()
                .map(SubgroupDO::getItemGuid).collect(Collectors.toList()));
        List<String> itemNameList = itemList.stream().map(ItemDO::getName).distinct().collect(Collectors.toList());
        itemNameList.forEach(item -> {
            message.add(String.format(" “%s” ", item));
        });
        if (CollectionUtils.isNotEmpty(message)) {
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append("商品规格存在于以下套餐:\n");
            message.forEach(m -> stringBuffer.append(m).append("\n"));
            stringBuffer.append("，请手动清除");
            throw new BusinessException(stringBuffer.toString());
        }
        skuSubgroupService.remove(new LambdaQueryWrapper<RSkuSubgroupDO>().in(RSkuSubgroupDO::getSkuGuid, guidList));
        removeDeletedSkuRelatedSubgroups();
    }

    private void deletePlanSku(List<String> guidList) {
        // 修改方案商品数
        List<PricePlanBySkuRespDTO> skuList = planItemMapper.listPlanBySkuList(guidList);
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }

        Map<String, List<PricePlanBySkuRespDTO>> planItemMap = skuList.stream()
                .collect(groupingBy(PricePlanBySkuRespDTO::getPlanGuid));

        // 删除方案里的规格
        planItemMapper.deleteBySku(guidList);

        planItemMap.forEach((p, pi) -> {
            Set<String> itemGuidSet = pi.stream()
                    .map(PricePlanBySkuRespDTO::getItemGuid)
                    .collect(Collectors.toSet());
            List<PricePlanItemDO> planItemDOList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                    .eq(PricePlanItemDO::getPlanGuid, p)
                    .in(PricePlanItemDO::getItemGuid, itemGuidSet)
                    .notIn(PricePlanItemDO::getSkuGuid, guidList)
                    .eq(PricePlanItemDO::getIsDelete, BooleanEnum.FALSE.getCode())
            );
            Set<String> notDelCount = planItemDOList.stream()
                    .map(PricePlanItemDO::getItemGuid)
                    .collect(Collectors.toSet());
            pricePlanMapper.updateItemNum(p, itemGuidSet.size() - notDelCount.size(), 2);
        });

    }

    private void deletePushSku(List<String> guidList) {
        List<SkuDO> pushedSkuDOList = list(new LambdaQueryWrapper<SkuDO>().in(SkuDO::getParentGuid, guidList));
        if (CollectionUtil.isEmpty(pushedSkuDOList)) {
            return;
        }
        List<String> pushedSkuGuidList = pushedSkuDOList.stream().map(SkuDO::getGuid).collect(Collectors.toList());
        guidList.addAll(pushedSkuGuidList);
        removeByIds(pushedSkuGuidList);
        // 推送SKU涉及的商品GUID
        Set<String> itemGuidSet = pushedSkuDOList.stream().map(SkuDO::getItemGuid).collect(Collectors.toSet());
        // 涉及到的被推送商品
        List<ItemDO> pushedItemDOList = itemMapper.selectList(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getGuid, itemGuidSet)
                .eq(ItemDO::getItemType, 2));
        if (!CollectionUtils.isEmpty(pushedItemDOList)) {
            // 被推送的多规格商品GUID
            List<String> multiSkuItemGuidList = pushedItemDOList.stream().map(ItemDO::getGuid).collect(Collectors.toList());
            // 被推送商品涉及规格集合
            List<SkuDO> pushItemSkuDOList = list(new LambdaQueryWrapper<SkuDO>().in(SkuDO::getItemGuid, multiSkuItemGuidList));
            // 按商品分组
            Map<String, List<SkuDO>> itemGroupSkuList = pushItemSkuDOList.stream().collect(Collectors.groupingBy(e -> e.getItemGuid()));
            Set<String> keySet = itemGroupSkuList.keySet();
            // 单规格的商品GUID集合
            List<String> singleSkuItemGuidList = new ArrayList<>();
            // 单规格的商品规格GUID集合
            List<String> updateSkuGuidList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(keySet)) {
                for (String itemGuid : keySet) {
                    // 此商品关联的规格集合
                    List<SkuDO> thisItemSkuDOList = itemGroupSkuList.get(itemGuid);
                    if (thisItemSkuDOList.size() == 1) {
                        singleSkuItemGuidList.add(itemGuid);
                        updateSkuGuidList.add(thisItemSkuDOList.get(0).getGuid());
                    }
                }
            }
            // 更新商品为单品类型
            if (!CollectionUtils.isEmpty(singleSkuItemGuidList)) {
                itemMapper.update(new ItemDO().setItemType(4), new LambdaQueryWrapper<ItemDO>()
                        .in(ItemDO::getGuid, singleSkuItemGuidList));
            }
            // 更新规格名称为空
            if (!CollectionUtils.isEmpty(updateSkuGuidList)) {
                update(new SkuDO().setName(EMPTY), new LambdaQueryWrapper<SkuDO>().in(SkuDO::getGuid, updateSkuGuidList));
            }
        }

    }

    private void toJudgePlanItem(List<String> guidList) {
        guidList.forEach(
                sku -> {
                    // 这里直接查单个更好，属性不会太多，但是价格方案关系会有很多
                    List<PricePlanBySkuRespDTO> skuRespList = planItemMapper.listPlanBySku(sku);
                    if (CollectionUtils.isNotEmpty(skuRespList)) {
                        StringBuilder builder = new StringBuilder();
                        builder.append("无法删除规格，以下菜谱方案正在使用：");
                        skuRespList.forEach(s -> builder.append(s.getPlanName()).append(","));
                        builder.deleteCharAt(builder.length() - 1);
                        throw new BusinessException(builder.toString());
                    }
                }
        );
    }

    @Override
    public Integer deleteSkuByItemGuidList(List<String> itemGuidList) {
        if (CollectionUtils.isEmpty(itemGuidList)) {
            return 0;
        }
        // 将被删除的商品下关联的规格GUID集合
        List<String> deleteSkuGuidList = listSkuGuidByItemGuid(itemGuidList);
        if (CollectionUtils.isEmpty(deleteSkuGuidList)) {
            log.error(SYSTEM_ERROR);
            throw new BusinessException(SYSTEM_ERROR);
        }
        // 删除商品关联的规格
        Integer deleteSkuByIds = deleteSkuByIds(deleteSkuGuidList, true);
        if (Integer.valueOf(0).equals(deleteSkuByIds)) {
            log.error(SYSTEM_ERROR);
            throw new BusinessException(SYSTEM_ERROR);
        }
        return 1;
    }


    @Override
    public List<String> saveOrUpdateAndDeleteSku(ItemInfoBO itemInfoBO) {
        fixAndFillItemReqDTO(itemInfoBO);
        log.info("itemInfoBO={}", JSON.toJSONString(itemInfoBO));
        // 新增或更新了的规格GUID集合
        List<String> saveOrUpdateSkuGuidList = new ArrayList<>();
        if (itemInfoBO == null) {
            return saveOrUpdateSkuGuidList;
        }
        // 历史规格
        List<SkuDO> beforeSkuDOList = list(
                new LambdaQueryWrapper<SkuDO>()
                        .eq(SkuDO::getItemGuid, itemInfoBO.getItemGuid()));
        log.info("beforeSkuDOList={}", JSON.toJSONString(beforeSkuDOList));
        List<SkuInfoBO> skuInfoBOList = itemInfoBO.getSkuList();
        if (CollectionUtils.isEmpty(skuInfoBOList)) {
            return saveOrUpdateSkuGuidList;
        }

        // 要保存的规格
        List<SkuDO> skuDOList = MapStructUtils.INSTANCE.skuInfoBOList2skuDOList(skuInfoBOList);
        log.info("skuDOList={}", JSON.toJSONString(skuDOList));
        //保存
        List<SkuDO> toSaveSkuList = new ArrayList<>(skuDOList);
        toSaveSkuList.removeAll(beforeSkuDOList);
        // 为了避免方案关联表和品牌库规格匹配不完整，不允许新建
        /*toSaveSkuList.forEach(
                sku -> {
                    List<PricePlanBySkuRespDTO> skuRespList = planItemMapper.listPlanByItem(sku.getItemGuid());
                    if (CollectionUtils.isNotEmpty(skuRespList)) {
                        // 无法增加规格，请先下架菜谱方案：菜谱方案1、菜谱方案2里的商品再增加
                        StringBuilder builder = new StringBuilder();
                        builder.append("无法增加规格，请先下架菜谱方案：");
                        skuRespList.forEach(s -> builder.append(s.getPlanName()).append(","));
                        builder.deleteCharAt(builder.length() - 1);
                        builder.append("里的商品再增加");
                        throw new BusinessException(builder.toString());
                    }
                }
        );*/
        log.info("toSaveSkuList={}", JSON.toJSONString(toSaveSkuList));
        //删除
        List<SkuDO> toDeleteSkuList = new ArrayList<>(beforeSkuDOList);
        toDeleteSkuList.removeAll(skuDOList);
        log.info("toDeleteSkuList={}", JSON.toJSONString(toDeleteSkuList));
        //更新
        List<SkuDO> toUpdateSkuList = new ArrayList<>(skuDOList);
        toUpdateSkuList.retainAll(beforeSkuDOList);
        log.info("toUpdateSkuList={}", JSON.toJSONString(toUpdateSkuList));
        if (CollectionUtils.isNotEmpty(toDeleteSkuList)) {
            // 待删除的规格GUID集合
            List<String> deleteSkuGuidList = toDeleteSkuList
                    .stream()
                    .map(SkuDO::getGuid)
                    .collect(Collectors.toList());
            deleteSkuByIds(deleteSkuGuidList, true);
        }
        if (CollectionUtils.isNotEmpty(toSaveSkuList)) {
            //美团、饿了吗等外卖绑定sku只支持数字+字母
            String tempItemGuid = itemInfoBO.getItemGuid().replace("-", "");
            // 编辑时需要校验规格数量
            String maxGuid = skuMapper.getMaxGuid(itemInfoBO.getItemGuid(), tempItemGuid);
            int oldSku = 0;
            if (!StringUtils.isEmpty(maxGuid)) {
                String replace = maxGuid.replace(tempItemGuid, "");
                if (!StringUtils.isEmpty(replace)) {
                    oldSku = Integer.parseInt(replace);
                }
            }
            for (int i = 0; i < toSaveSkuList.size(); i++) {
                SkuDO skuDO = toSaveSkuList.get(i);
                int count = i + 1;
                int number = oldSku + count;
                if (count > 99 || number > 99) {
                    throw new BusinessException("规格编号已达到上限不可新建");
                }
                if (number < 10) {
                    skuDO.setGuid(tempItemGuid + "0" + number);
                } else {
                    skuDO.setGuid(tempItemGuid + number);
                }
                skuDO.setTypeGuid(itemInfoBO.getTypeGuid());
                skuDO.setIsJoinStore(1);
                skuDO.setIsJoinMiniAppTakeaway(1);
            }
            saveBatch(toSaveSkuList, toSaveSkuList.size());
            List<String> insertSkuGuidList = toSaveSkuList.stream().map(SkuDO::getGuid).collect(Collectors.toList());
            saveOrUpdateSkuGuidList.addAll(insertSkuGuidList);
        }
        if (!CollectionUtils.isEmpty(toUpdateSkuList)) {
            toUpdateSkuList.forEach(skuDO -> skuDO.setTypeGuid(itemInfoBO.getTypeGuid()));
            boolean updateBatchById = updateBatchById(toUpdateSkuList, toUpdateSkuList.size());
            if (!updateBatchById) {
                throw new BusinessException(OP_FAIL);
            }
            List<String> updateSkuGuidList = toUpdateSkuList.stream().map(SkuDO::getGuid).collect(Collectors.toList());
            saveOrUpdateSkuGuidList.addAll(updateSkuGuidList);
        }
        return saveOrUpdateSkuGuidList;
    }


    @Override
    public List<String> listSkuGuidByItemGuid(List<String> itemGuidList) {
        if (CollectionUtils.isEmpty(itemGuidList)) {
            return new ArrayList<>();
        }
        List<SkuDO> delateSkuDOList = list(new LambdaQueryWrapper<SkuDO>().in(SkuDO::getItemGuid, itemGuidList));
        return delateSkuDOList.stream().map(SkuDO::getGuid).collect(Collectors.toList());
    }

    private void fixAndFillItemReqDTO(ItemInfoBO itemInfoBO) {
        if (itemInfoBO == null) {
            return;
        }
        List<SkuInfoBO> skuList = itemInfoBO.getSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }
        if (Integer.valueOf(2).equals(itemInfoBO.getItemType())) {
            // 若是非称重的单规格商品。则类型设置为单品
            if (skuList.size() == 1) {
                itemInfoBO.setItemType(4);
                skuList.get(0).setName(EMPTY);
            }
        }
        skuList.forEach(skuSaveReqDTO -> {
            skuSaveReqDTO.setItemGuid(itemInfoBO.getItemGuid());
            skuSaveReqDTO.setStoreGuid(itemInfoBO.getStoreGuid());
            skuSaveReqDTO.setBrandGuid(itemInfoBO.getBrandGuid());
            skuSaveReqDTO.setSkuFrom(itemInfoBO.getItemFrom());
        });
    }

    private void removeDeletedSkuRelatedSubgroups() {
        // todo test sql
        List<RSkuSubgroupDO> allSkuSubgroupDOList = skuSubgroupService.list(new LambdaQueryWrapper<>());
        // 有子商品的分组
        Set<String> subgroupGuidSet = allSkuSubgroupDOList.stream().map(RSkuSubgroupDO::getSubgroupGuid).collect(Collectors.toSet());
        // 所有的分组集合
        List<SubgroupDO> subgroupDOS = subgroupService.list(new LambdaQueryWrapper<>());
        // 无子商品的分组,待删除
        List<SubgroupDO> toDelSubgroupList = subgroupDOS.stream()
                .filter(subgroupDO -> !subgroupGuidSet.contains(subgroupDO.getGuid())).collect(Collectors.toList());
        // 若有分组被删除，则检测是否有套餐下无分组，若存在无分组的套餐，则删除套餐及其相关规格等
        if (!CollectionUtils.isEmpty(toDelSubgroupList)) {
            // 有被删除分组的套餐GUID集合
            Set<String> pkgGuidSet = toDelSubgroupList.stream().map(SubgroupDO::getItemGuid).collect(Collectors.toSet());
            // 有子商品的分组集合
            List<SubgroupDO> withSubItemSubgroupList = subgroupDOS.stream()
                    .filter(subgroupDO -> subgroupGuidSet.contains(subgroupDO.getGuid())).collect(Collectors.toList());
            // 有未删除子商品的套餐GUID集合
            Set<String> withSubItemPkgGuidSet = withSubItemSubgroupList.stream().map(SubgroupDO::getItemGuid).collect(Collectors.toSet());
            // 排除含有未删除子商品的套餐，剩下无子商品的套餐
            pkgGuidSet.removeAll(withSubItemPkgGuidSet);
            // 删除无子商品的套餐
            if (!CollectionUtils.isEmpty(pkgGuidSet)) {
                // 删除这些套餐以及对应的被推送套餐下关联的规格等
                // 被该商品推送至门店的商品
                List<ItemDO> sonPkgList = itemMapper.selectList(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getParentGuid, pkgGuidSet));
                // 若被推送商品列表不为空，则同步删除被推送商品
                if (!CollectionUtils.isEmpty(sonPkgList)) {
                    List<String> sonItemGuidList = sonPkgList.stream().map(ItemDO::getGuid).collect(Collectors.toList());
                    pkgGuidSet.addAll(sonItemGuidList);
                }
                // 删除关联的规格（不用删除对应的属性及属性组以及分组以及分组子商品的关联关系，因为套餐无属性且这些将被删除的套餐是根据无分组的条件筛选而来，所以其分组已经被删除了）
                remove(new LambdaQueryWrapper<SkuDO>().in(SkuDO::getItemGuid, pkgGuidSet));
                // 删除对应套餐
                itemMapper.deleteBatchIds(pkgGuidSet);
                try {
                    subgroupService.removeSubgroupByItemGuidList(new ArrayList<>(pkgGuidSet));
                } catch (ParamException e) {
                    log.error("系统错误" + e.getMessage());
                }
            }
            // 删除无子商品的分组
            List<String> withoutSubItemSubgroupGuidList = toDelSubgroupList.stream().map(SubgroupDO::getGuid).collect(Collectors.toList());
            subgroupService.removeByIds(withoutSubItemSubgroupGuidList);
        }
    }

    @Override
    public void validateSkuCodeRepeat(List<SkuSaveReqDTO> skuList, String brandGuid, String storeGuid, Integer from, Integer flag) {
        // 要保存商品中的编码集合
        List<String> codeList = skuList.stream()
                .map(SkuSaveReqDTO::getCode)
                .filter(code -> !StringUtils.isEmpty(code))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(codeList)) {
            return;
        }
        Set<String> codeSet = new HashSet<>(codeList);
        if (codeList.size() > codeSet.size()) {
            throw new ParameterException("商品规格SKU简码有重复，请修改");
        }
        // 查询所在组织机构下的规格集合
        List<SkuDO> dbSkuDOList;
        if (from == 1) {
            // 在品牌库内唯一（不含其下面的门店库）
            dbSkuDOList = list(new LambdaQueryWrapper<SkuDO>().eq(SkuDO::getBrandGuid, brandGuid).isNull(SkuDO::getStoreGuid));
        } else {
            // 在门店库内唯一
            dbSkuDOList = list(new LambdaQueryWrapper<SkuDO>().eq(SkuDO::getStoreGuid, storeGuid));
        }
        if (!CollectionUtils.isEmpty(dbSkuDOList)) {
            // 仅留下有CODE的规格
            dbSkuDOList.removeIf(skuDO -> StringUtils.isEmpty(skuDO.getCode()));
        }
        if (!CollectionUtils.isEmpty(dbSkuDOList)) {
            // 仅留下与入参code重合的规格
            dbSkuDOList.removeIf(skuDO -> !codeList.contains(skuDO.getCode()));
        }
        // 规格GUID集合
        List<String> skuGuidList = skuList.stream()
                .filter(skuSaveReqDTO -> !StringUtils.isEmpty(skuSaveReqDTO.getSkuGuid()))
                .map(SkuSaveReqDTO::getSkuGuid)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(dbSkuDOList) && !CollectionUtils.isEmpty(skuGuidList)) {
            // 仅留下与入参规格GUID不同的规格
            dbSkuDOList.removeIf(skuDO -> skuGuidList.contains(skuDO.getGuid()));
        }
        // 留下有相同code的规格
        if (!CollectionUtils.isEmpty(dbSkuDOList) && Objects.isNull(flag)) {
            throw new ParameterException(DUPLICATE_CODE);
        } else if (Objects.nonNull(flag) && flag == 1 && !CollectionUtils.isEmpty(dbSkuDOList)) {
            throw new ParameterException(DUPLICATE_RETAIL_CODE);
        }
    }

    private List<SkuItemTypeBO> selectSkuItemTypeBOList(List<SkuDO> skuDOList, List<String> choiceSkuGuids,
                                                        HashMap<String, String> planNameMap) {
        List<SkuItemTypeBO> skuItemTypeBOList = new ArrayList<>();
        //通过skulist获取itemList
        List<ItemDO> itemDOList = getItemListBySkuList(skuDOList);

        Set<String> typeGuidSet = itemDOList.stream()
                .map(ItemDO::getTypeGuid)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(typeGuidSet)) {
            return new ArrayList<>();
        }
        Collection<TypeDO> typeDOList = typeService.listByIds(typeGuidSet);
        skuDOList.forEach(skuDO -> {
            // 数据迁移有脏数据，sku存在但是sku对应的商品不存在
            ItemDO thisItemDO = itemDOList
                    .stream()
                    .filter(itemDO -> skuDO.getItemGuid().equals(itemDO.getGuid()))
                    .findFirst()
                    .orElse(null);
            if (thisItemDO != null) {
                TypeDO thisTypeDO = typeDOList
                        .stream()
                        .filter(typeDO -> thisItemDO.getTypeGuid().equals(typeDO.getGuid()))
                        .findFirst()
                        .orElse(null);
                if (thisTypeDO != null) {
                    SkuItemTypeBO skuItemTypeBO = new SkuItemTypeBO();
                    skuItemTypeBO.setTakeawayAccountingPrice(skuDO.getTakeawayAccountingPrice());
                    skuItemTypeBO.setAccountingPrice(skuDO.getAccountingPrice());
                    skuItemTypeBO.setItemGuid(skuDO.getItemGuid());
                    skuItemTypeBO.setSkuGuid(skuDO.getGuid());
                    skuItemTypeBO.setTypeGuid(thisTypeDO.getGuid());
                    skuItemTypeBO.setItemName(thisItemDO.getName());
                    skuItemTypeBO.setSkuName(skuDO.getName());
                    skuItemTypeBO.setTypeName(thisTypeDO.getName());
                    skuItemTypeBO.setCode(skuDO.getCode());
                    skuItemTypeBO.setItemType(thisItemDO.getItemType());
                    skuItemTypeBO.setSalePrice(skuDO.getSalePrice());
                    skuItemTypeBO.setUnit(skuDO.getUnit());
                    skuItemTypeBO.setSort(thisTypeDO.getSort());
                    skuItemTypeBO.setId(thisTypeDO.getId());
                    skuItemTypeBO.setPinyin(thisItemDO.getPinyin());
                    skuItemTypeBO.setIsRack(skuDO.getIsRack());
                    if (!ObjectUtils.isEmpty(skuDO.getParentGuid())) {
                        skuItemTypeBO.setParentSkuGuid(skuDO.getParentGuid());
                    } else {
                        skuItemTypeBO.setParentSkuGuid(skuDO.getGuid());
                    }
                    if (!ObjectUtils.isEmpty(thisItemDO.getParentGuid())) {
                        skuItemTypeBO.setParentItemGuid(thisItemDO.getParentGuid());
                    } else {
                        skuItemTypeBO.setParentItemGuid(thisItemDO.getGuid());
                    }
                    if (CollectionUtils.isNotEmpty(choiceSkuGuids) && choiceSkuGuids.contains(skuDO.getGuid())) {
                        skuItemTypeBO.setIsChoice(1);
                    }
                    if (MapUtils.isNotEmpty(planNameMap)) {
                        String planItemName = planNameMap.get(skuDO.getGuid());
                        if (!StringUtils.isEmpty(planItemName)) {
                            skuItemTypeBO.setPlanItemName(planItemName);
                        }
                    }
                    skuItemTypeBOList.add(skuItemTypeBO);
                }
            }
        });
        return skuItemTypeBOList;
    }

    private List<SkuItemTypeBO> selectSkuItemTypeBOList2(List<SkuDO> skuDOList) {
        //通过skulist获取itemList
        List<ItemDO> itemDOList = getItemListBySkuList(skuDOList);
        Set<String> typeGuidSet = itemDOList.stream()
                .map(ItemDO::getTypeGuid)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(typeGuidSet)) {
            return new ArrayList<>();
        }
        List<TypeDO> typeDOList = (ArrayList) typeService.listByIds(typeGuidSet);
        //获取品牌库分类Guid
        List<String> brandTypeGuids = typeDOList
                .stream()
                .filter(typeDO -> typeDO.getTypeFrom() == 1)
                .map(TypeDO::getGuid)
                .collect(Collectors.toList());
        Iterator<TypeDO> iterator = typeDOList.iterator();
        while (iterator.hasNext()) {
            TypeDO typeDO = iterator.next();
            // 品牌库存在就不在展示
            if (brandTypeGuids.contains(typeDO.getParentGuid()) && 2 == typeDO.getTypeFrom()) {
                List<ItemDO> itemDOS = itemDOList.stream()
                        .filter(itemDO -> itemDO.getTypeGuid().equals(typeDO.getGuid())).collect(Collectors.toList());
                itemDOS.forEach(itemDO -> itemDO.setTypeGuid(typeDO.getParentGuid()));
                iterator.remove();
            }
        }
        List<SkuItemTypeBO> skuItemTypeBOList = new ArrayList<>();
        for (SkuDO skuDO : skuDOList) {// 数据迁移有脏数据，sku存在但是sku对应的商品不存在
            ItemDO thisItemDO = itemDOList
                    .stream()
                    .filter(itemDO -> skuDO.getItemGuid().equals(itemDO.getGuid()) || skuDO.getItemGuid().equals(itemDO.getParentGuid()))
                    .findFirst()
                    .orElse(null);
            if (thisItemDO != null) {
                TypeDO thisTypeDO = typeDOList
                        .stream()
                        .filter(typeDO -> thisItemDO.getTypeGuid().equals(typeDO.getGuid()))
                        .findFirst()
                        .orElse(null);
                if (thisTypeDO != null) {
                    SkuItemTypeBO skuItemTypeBO = buildSkuItemTypeBO(thisItemDO, skuDO, thisTypeDO);
                    skuItemTypeBOList.add(skuItemTypeBO);
                }
            }
        }
        return skuItemTypeBOList;
    }

    private SkuItemTypeBO buildSkuItemTypeBO(ItemDO thisItemDO, SkuDO skuDO, TypeDO thisTypeDO) {
        SkuItemTypeBO skuItemTypeBO = new SkuItemTypeBO();
        skuItemTypeBO.setItemGuid(thisItemDO.getGuid());
        skuItemTypeBO.setParentItemGuid(thisItemDO.getParentGuid());
        skuItemTypeBO.setPlanItemName(skuDO.getPlanItemName());
        skuItemTypeBO.setSkuGuid(skuDO.getGuid());
        skuItemTypeBO.setTypeGuid(thisTypeDO.getGuid());
        skuItemTypeBO.setItemName(thisItemDO.getName());
        skuItemTypeBO.setSkuName(skuDO.getName());
        skuItemTypeBO.setTypeName(thisTypeDO.getName());
        skuItemTypeBO.setCode(skuDO.getCode());
        skuItemTypeBO.setItemType(thisItemDO.getItemType());
        skuItemTypeBO.setSalePrice(skuDO.getSalePrice());
        skuItemTypeBO.setUnit(skuDO.getUnit());
        skuItemTypeBO.setSort(thisTypeDO.getSort());
        skuItemTypeBO.setId(thisTypeDO.getId());
        skuItemTypeBO.setPinyin(thisItemDO.getPinyin());
        skuItemTypeBO.setIsRack(skuDO.getIsRack());
        if (!ObjectUtils.isEmpty(skuDO.getParentGuid())) {
            skuItemTypeBO.setParentSkuGuid(skuDO.getParentGuid());
        } else {
            skuItemTypeBO.setParentSkuGuid(skuDO.getGuid());
        }
        return skuItemTypeBO;
    }

    @Override
    public Map<String, String> selectErpSkuGuidBySkuGuid(SingleDataDTO request) {
        if (CollectionUtils.isEmpty(request.getDatas())) {
            return new HashMap<>();
        }
        List<SkuDO> skuDOList = list(
                new LambdaQueryWrapper<SkuDO>()
                        .in(SkuDO::getGuid, request.getDatas()));
        return skuDOList.stream()
//                .filter(skuDO -> Objects.nonNull(skuDO.getParentGuid()))
                .collect(Collectors.toMap(SkuDO::getGuid, o -> org.apache.commons.lang3.StringUtils.isBlank(o.getParentGuid()) ? o.getGuid() : o.getParentGuid()));
    }

    @Override
    public Integer wholeDiscount(ItemWholeDiscountDTO itemWholeDiscountDTO) {
        validateItemWholeDiscountDTO(itemWholeDiscountDTO);
        // 选择商品的GUID集合
        List<String> itemGuidList = itemWholeDiscountDTO.getItemGuidList();
        if (CollectionUtils.isEmpty(itemGuidList)) {
            return 0;
        }
        List<SkuDO> skuDOList = list(
                new LambdaQueryWrapper<SkuDO>()
                        .in(SkuDO::getItemGuid, itemGuidList));
        List<String> skuGuidList = skuDOList.stream().map(SkuDO::getGuid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuGuidList)) {
            log.error("SKU整单折扣发现系统错误，商品GUID没有对应的SKUGuid");
            throw new BusinessException("系统错误");
        }
        // 被选中规格推送至门店的规格集合
        List<SkuDO> pushedSkuDOList = list(
                new LambdaQueryWrapper<SkuDO>()
                        .in(SkuDO::getParentGuid, skuGuidList));
        if (CollectionUtils.isNotEmpty(pushedSkuDOList)) {
            skuDOList.addAll(pushedSkuDOList);
        }
        skuDOList.forEach(skuDO -> skuDO.setIsWholeDiscount(itemWholeDiscountDTO.getWholeDiscountState()));
        boolean update = updateBatchById(skuDOList, skuDOList.size());
        return update ? 1 : 0;
    }


    private void validateItemWholeDiscountDTO(ItemWholeDiscountDTO itemWholeDiscountDTO) {
        Integer wholeDiscount = itemWholeDiscountDTO.getWholeDiscountState();
        if (wholeDiscount != 1 && wholeDiscount != 0) {
            throw new ParameterException("整单折扣状态字段传输错误");
        }
    }

    @Override
    public List<SkuDO> selectSearchSkuByItemGUid(List<String> itemList) {
        return this.baseMapper.selectSearchSkuByItemGuid(itemList);
    }

    @Override
    public List<SkuInfoRespDTO> all(ItemSkuAllReq req) {
        LambdaQueryWrapper<SkuDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SkuDO::getStoreGuid, req.getStoreGuid());
        if (req.getIsJoinMiniAppMall() != null) {
            wrapper.eq(SkuDO::getIsJoinMiniAppMall, req.getIsJoinMiniAppMall());
        }
        if (req.getIsJoinMiniAppTakeaway() != null) {
            wrapper.eq(SkuDO::getIsJoinMiniAppTakeaway, req.getIsJoinMiniAppTakeaway());
        }
        if (req.getIsJoinStore() != null) {
            wrapper.eq(SkuDO::getIsJoinStore, req.getIsJoinStore());
        }

        List<SkuDO> skuDOList = fixTypeGuid(this.list(wrapper));
        log.info("查询sku返回数据：{}", JSON.toJSONString(skuDOList));
        return attachItemInfo(skuDOList, SkuInfoRespDTO.class);
    }

    @Override
    public Page<SkuInfoRespDTO> list(ItemSkuListReqDTO req) {
        IPage<SkuDO> pageAdapter = new PageAdapter<>(req);
        SkuDO sku = new SkuDO();
        BeanUtils.copyProperties(req, sku);
        LambdaQueryWrapper<SkuDO> skuDOLambdaQueryWrapper = new LambdaQueryWrapper<>(sku);
        IPage<SkuDO> srcRet = this.page(pageAdapter, skuDOLambdaQueryWrapper);
        List<SkuDO> skuDOList = fixTypeGuid(srcRet.getRecords());
        List<SkuInfoRespDTO> dtoList = attachItemInfo(skuDOList, SkuInfoRespDTO.class);
        Page<SkuInfoRespDTO> ret = new PageAdapter<>();
        ret.setTotalCount(srcRet.getTotal());
        ret.setPageSize(srcRet.getSize());
        ret.setCurrentPage(srcRet.getCurrent());
        ret.setData(dtoList);
        return ret;
    }


    private List<SkuDO> fixTypeGuid(List<SkuDO> skuDOList) {
        return skuDOList.stream().map(skuDO -> {
            if (StringUtils.isEmpty(skuDO.getTypeGuid())) {
                ItemDO itemDO = itemMapper.selectById(skuDO.getItemGuid());
                skuDO.setTypeGuid(itemDO.getTypeGuid());
                this.updateById(skuDO);
            }
            return skuDO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ItemSkuSearchRespDTO> search(ItemSkuSearchReqDTO itemSearchReqDTO) {
        return skuMapper.querySkuByStoreGuidAndSkuGuidWithIsDelete(itemSearchReqDTO.getStoreGuid(), itemSearchReqDTO.getSkuGuidList());
    }

    /**
     * 附加商品信息
     *
     * @param skuList
     * @param clazz
     * @param <T>
     * @return
     */
    private <T> List<T> attachItemInfo(List<SkuDO> skuList, Class<T> clazz) {
        List<String> itemGuidList = skuList.stream().map(SkuDO::getItemGuid).collect(Collectors.toList());
        if (itemGuidList.isEmpty()) {
            return new ArrayList<>();
        }
        //获取商品信息
        LambdaQueryWrapper<ItemDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ItemDO::getGuid, itemGuidList);
        List<ItemDO> itemList = itemMapper.selectList(lambdaQueryWrapper);
        log.info("itemList：{}", JSON.toJSONString(itemList));
        Map<String, ItemDO> itemMap = new HashMap<>();
        Set<String> typeGuidList = new HashSet<>();
        itemList.forEach(item -> {
            itemMap.put(item.getGuid(), item);
            typeGuidList.add(item.getTypeGuid());
        });
        log.info("itemMap：{}", JSON.toJSONString(itemMap));
        log.info("typeGuidList：{}", JSON.toJSONString(typeGuidList));
        //获取商品分类信息
        Map<String, TypeDO> typeMap = new HashMap<>();
        LambdaQueryWrapper<TypeDO> typeQueryWrapper = new LambdaQueryWrapper<>();
        typeQueryWrapper.in(TypeDO::getGuid, typeGuidList);
        List<TypeDO> typeList = typeMapper.selectList(typeQueryWrapper);
        log.info("typeList：{}", JSON.toJSONString(typeList));
        typeList.forEach(type -> {
            typeMap.put(type.getGuid(), type);
        });

        List<T> retList = new ArrayList<>();
        for (SkuDO sku : skuList) {
            T target;
            try {
                target = clazz.newInstance();
            } catch (InstantiationException | IllegalAccessException e) {
                log.error("新建实例失败");
                continue;
            }
            SkuRespAdapter skuRespAdapter = new SkuRespAdapter();
            ItemDO itemDO = itemMap.get(sku.getItemGuid());
            if (!ObjectUtils.isEmpty(itemDO)) {
                skuRespAdapter.set(itemDO);
                TypeDO typeDO = typeMap.get(itemDO.getTypeGuid());
                skuRespAdapter.set(typeDO);
                BeanUtils.copyProperties(itemDO, target);
            }


            if (!ObjectUtils.isEmpty(itemDO) &&
                    (itemDO.getItemType() == ItemType.SINGLE_SKU || itemDO.getItemType() == ItemType.SINGLE_WEIGHT_SKU)) {
                sku.setName(itemDO.getName());
            }
            skuRespAdapter.set(sku);
            BeanUtils.copyProperties(skuRespAdapter, target);
            BeanUtils.copyProperties(sku, target);
            retList.add(target);
        }
        return retList;
    }

    @Override
    @Transactional
    public Boolean batchUpdate(ItemSkuBatchUpdateDTO batchUpdateDTO) {
        List<SkuDO> skuList = batchUpdateDTO.getSkuList().stream().map(
                s -> {
                    SkuDO skuDO = new SkuDO();
                    BeanUtils.copyProperties(s, skuDO);
                    skuDO.setGuid(s.getSkuGuid());
                    return skuDO;
                }
        ).collect(Collectors.toList());
        return this.updateBatchById(skuList);
    }

    @Override
    @Transactional
    public Boolean batchSaleInc(ItemSkuMonthlySaleIncDTO itemSkuMonthlySaleIncDTO) {
        List<String> skuGuidList = itemSkuMonthlySaleIncDTO.getSkuList()
                .stream().map(SkuMonthlySaleDTO::getGuid)
                .collect(Collectors.toList());
        skuMapper.initMonthlySale(skuGuidList);
        itemSkuMonthlySaleIncDTO.getSkuList().forEach(s -> {
            skuMapper.updateMonthlySaleInc(s.getGuid(), s.getMonthlySale());
        });
        return true;
    }

    @Override
    public Boolean deleteSkuByItemGuidAndStoreGuid(String itemGuid, String storeGuid) {
        return skuMapper.deleteSkuByItemGuidAndStoreGuid(itemGuid, storeGuid);
    }

    @Override
    public Integer updateOriginPriceStoreGuid(String itemGuid, String storeGuid, String skuGuid,
                                              BigDecimal originalSalePrice, BigDecimal originalMemberPrice) {
        return skuMapper.updateOriginPriceStoreGuid(itemGuid, storeGuid, skuGuid, originalSalePrice, originalMemberPrice);
    }

    @Override
    public Map<String, List<SkuDO>> getSkuGuidMap(List<String> itemGuidList) {
        List<SkuDO> skuDOList = skuMapper.getSkuListByItemList(itemGuidList);
        log.info("itemGuid集合：{}，查询到的sku集合：{}", JSON.toJSONString(itemGuidList), JSON.toJSONString(skuDOList));
        Map<String, List<SkuDO>> skuMap = skuDOList.stream().collect(Collectors.toMap(SkuDO::getItemGuid, s -> {
            List<SkuDO> skuList = new ArrayList<>();
            skuList.add(s);
            return skuList;
        }, (List<SkuDO> value1, List<SkuDO> value2) -> {
            value1.addAll(value2);
            return value1;
        }));
        log.info("以itemGuid为key，skuDO为value组合的Map：{}", JSON.toJSONString(skuMap));
        return skuMap;
    }

    @Override
    public Integer updateTerminalStatus(UpdateTerminalStatusDTO updateTerminalStatusDTO) {
        return skuMapper.updateTerminalStatus(updateTerminalStatusDTO);
    }

    @Data
    private static class SkuRespAdapter {
        @ApiModelProperty(value = "skuGuid", required = true)
        private String skuGuid;
        @ApiModelProperty("sku名称")
        private String skuName;
        @ApiModelProperty("单位")
        private String unit;
        @ApiModelProperty("商品Guid")
        private String itemGuid;
        @ApiModelProperty("商品分类Guid")
        private String typeGuid;
        @ApiModelProperty(value = "商品分类名称（冗余小程序端字段）")
        private String typeName;
        @ApiModelProperty(value = "商品主图路径")
        private String pictureUrl;
        @ApiModelProperty(value = "商品名称")
        private String itemName;

        private void set(ItemDO item) {
            this.typeGuid = item.getTypeGuid();
            this.pictureUrl = item.getPictureUrl();
            this.itemName = item.getName();
        }

        private void set(TypeDO typeDO) {
            this.typeGuid = typeDO.getGuid();
            this.typeName = typeDO.getName();
        }

        private void set(SkuDO skuDO) {
            this.skuGuid = skuDO.getGuid();
            this.skuName = skuDO.getName();
        }
    }

    @Override
    public Boolean sellOutRackItem(List<String> skuGuids) {
        if (CollectionUtils.isEmpty(skuGuids)) {
            return false;
        }
        //查询当前正在售卖的门店GUID
        List<String> storeGuidNowList = pricePlanMapper.findIsSoldOutStores(skuGuids, LocalDateTime.now());
        List<PricePlanItemDO> pricePlanItemDOS = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                .eq(PricePlanItemDO::getIsSoldOut, 1)
                .in(PricePlanItemDO::getSkuGuid, skuGuids));
        if (CollectionUtils.isEmpty(pricePlanItemDOS)) {
            return false;
        }
        Map<String, List<PricePlanItemDO>> planMap = pricePlanItemDOS.stream().collect(Collectors.groupingBy(PricePlanItemDO::getPlanGuid));
        for (String planGuid : planMap.keySet()) {
            //更新菜谱方案商品数
            pricePlanMapper.updateItemNum(planGuid, planMap.get(planGuid).size(), 2);
        }
        //将所有售完下架的设置删除
        planItemMapper.delete(new LambdaUpdateWrapper<PricePlanItemDO>()
                .in(PricePlanItemDO::getSkuGuid, skuGuids)
                .eq(PricePlanItemDO::getIsSoldOut, 1));

        log.info("待推送的门店：{}", JacksonUtils.writeValueAsString(storeGuidNowList));
        if (CollectionUtils.isNotEmpty(storeGuidNowList)) {
            log.info("菜谱提示异常日志跟踪14}");
            eventPushHelper.pushMsgToAndriod(storeGuidNowList);
        }
        return true;
    }

    @Override
    public List<SkuInfoRespDTO> findParentSKUS(List<String> skuGuids) {
        List<SkuInfoRespDTO> respDTOS = new ArrayList<>();
        String storeGuid = UserContextUtils.getStoreGuid();
        if (CollectionUtils.isEmpty(skuGuids)) {
            return respDTOS;
        }
        List<SkuDO> skuDOS = list(new LambdaQueryWrapper<SkuDO>()
                .in(SkuDO::getParentGuid, skuGuids)
                .eq(SkuDO::getStoreGuid, storeGuid)
                .or()
                .in(SkuDO::getGuid, skuGuids));

        SkuInfoRespDTO dto;
        for (SkuDO skuDO : skuDOS) {
            dto = new SkuInfoRespDTO();
            dto.setSkuGuid(skuDO.getGuid());
            String parentGuid = StringUtils.isEmpty(skuDO.getParentGuid()) ? skuDO.getGuid() : skuDO.getParentGuid();
            dto.setParentGuid(parentGuid);
            respDTOS.add(dto);
        }
        return respDTOS;
    }

    @Override
    public List<SkuDO> findByItemGuid(String itemGuid) {
        return skuMapper.findByItemGuid(itemGuid);
    }

    @Override
    public List<String> findParentItemSkus(List<String> skuGuids) {
        if (CollectionUtils.isEmpty(skuGuids)) {
            log.error("skuGuids为空");
            return new ArrayList<>();
        }
        return skuMapper.findParentItemSkus(skuGuids);
    }

    /**
     * 判断当前是否有不可下单的商品
     * 不可下单：商品下架、商品售罄、库存不足
     *
     * @param orderItemReqDTO 需要检查的规格guid
     * @return 不可下单的商品
     */
    @Override
    public List<ItemPadCalculateDTO> checkOrderPlacementItem(OrderItemReqDTO orderItemReqDTO) {
        Map<String, Integer> skuAndQuantityMap = orderItemReqDTO.getSkuAndQuantityMap();
        List<ItemPadCalculateDTO> badSkuGuidList = new ArrayList<>();
        if (skuAndQuantityMap.isEmpty()) {
            log.warn("skuGuidList为空");
            return badSkuGuidList;
        }
        if (ObjectUtils.isEmpty(orderItemReqDTO.getStoreGuid())) {
            log.warn("storeGuid为空");
            return badSkuGuidList;
        }
        Set<String> skuGuidList = skuAndQuantityMap.keySet();
        if (CollectionUtils.isEmpty(skuGuidList)) {
            log.warn("skuGuidList为空");
            return badSkuGuidList;
        }

        // 排除下架商品
        List<SkuDO> skuDOList = this.list(new LambdaQueryWrapper<SkuDO>()
                .in(SkuDO::getGuid, skuGuidList)
                .eq(SkuDO::getIsRack, 1)
                .eq(SkuDO::getIsJoinPad, 1)
                .eq(SkuDO::getIsDelete, 0)
                .eq(SkuDO::getIsEnable, 1)
        );
        if (CollectionUtils.isEmpty(skuDOList)) {
            skuGuidList.forEach(obj -> {
                ItemPadCalculateDTO calculateDTO = new ItemPadCalculateDTO();
                calculateDTO.setSkuGuid(obj);
                calculateDTO.setIsJoinPad(BooleanEnum.FALSE.getCode());
                badSkuGuidList.add(calculateDTO);
            });
            log.warn("都是下架商品");
            return badSkuGuidList;
        }

        // 上架的商品skuGList
        List<String> skuGList = skuDOList.stream().map(SkuDO::getGuid).collect(Collectors.toList());
        if (skuGuidList.size() > skuDOList.size()) {
            // 下架商品
            skuGuidList.removeIf(skuGList::contains);
            skuGuidList.forEach(obj -> {
                ItemPadCalculateDTO calculateDTO = new ItemPadCalculateDTO();
                calculateDTO.setSkuGuid(obj);
                calculateDTO.setIsJoinPad(BooleanEnum.FALSE.getCode());
                badSkuGuidList.add(calculateDTO);
            });
        }

        // 淦，这表里不一定有
        List<EstimateDO> estimateDOList = estimateMapper.selectList(new LambdaQueryWrapper<EstimateDO>()
                .in(EstimateDO::getSkuGuid, skuGuidList)
                .eq(EstimateDO::getStoreGuid, orderItemReqDTO.getStoreGuid())
                .eq(EstimateDO::getIsDelete, 0)
        );
        if (CollectionUtils.isEmpty(estimateDOList)) {
            log.warn("估清表为空");
            return badSkuGuidList;
        }

        // 禁售商品
        List<EstimateDO> bannedSkuList = estimateDOList.stream()
                .filter(es -> Objects.equals(2, es.getIsSoldOut()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(bannedSkuList)) {
            List<String> bannedGuid = bannedSkuList.stream()
                    .map(EstimateDO::getSkuGuid)
                    .collect(Collectors.toList());
            bannedGuid.forEach(obj -> {
                ItemPadCalculateDTO calculateDTO = new ItemPadCalculateDTO();
                calculateDTO.setSkuGuid(obj);
                calculateDTO.setIsJoinPad(BooleanEnum.FALSE.getCode());
                badSkuGuidList.add(calculateDTO);
            });
        }

        // 库存不足商品
        List<EstimateDO> soldOutSkuList = estimateDOList.stream()
                .filter(es -> Objects.equals(1, es.getIsSoldOut()))
                .filter(es -> Objects.equals(2, es.getIsTheLimit()))
                .collect(Collectors.toList());
        for (EstimateDO e : soldOutSkuList) {
            if (e.getResidueQuantity().compareTo(BigDecimal.valueOf(skuAndQuantityMap.get(e.getSkuGuid()))) < 0) {
                ItemPadCalculateDTO calculateDTO = new ItemPadCalculateDTO();
                calculateDTO.setSkuGuid(e.getSkuGuid());
                calculateDTO.setIsJoinPad(BooleanEnum.TRUE.getCode());
                calculateDTO.setResidueQuantity(e.getResidueQuantity());
                if (e.getResidueQuantity().compareTo(BigDecimal.ZERO) == 0) {
                    calculateDTO.setIsJoinPad(BooleanEnum.FALSE.getCode());
                }
                badSkuGuidList.add(calculateDTO);
            }
        }
        return badSkuGuidList;
    }

    /**
     * 根据规格guid查询规格信息
     *
     * @param skuGuidList 规格guid列表
     * @return 规格信息列表
     */
    @Override
    public List<SkuInfoRespDTO> listSkuInfo(List<String> skuGuidList) {
        List<SkuDO> skuDOList = this.list(new LambdaQueryWrapper<SkuDO>()
                .in(CollectionUtils.isNotEmpty(skuGuidList), SkuDO::getGuid, skuGuidList)
                .eq(SkuDO::getIsDelete, 0)
                .eq(SkuDO::getIsEnable, 1));
        if(CollectionUtil.isEmpty(skuDOList)){
            return Lists.newArrayList();
        }
        List<String> itemGuidList = skuDOList.stream()
                .map(SkuDO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        List<ItemDTO> itemDTOList = itemMapper.listBaseItem(itemGuidList);
        List<SkuInfoRespDTO> skuInfoRespDTOS = MapStructUtils.INSTANCE.skuDOList2skuInfoRespDTOList(skuDOList);

        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(UserContextUtils.getStoreGuid());
        log.info("[品牌信息]brandDTO={}", JacksonUtils.writeValueAsString(brandDTO));
        if (!ObjectUtils.isEmpty(brandDTO.getSalesModel()) && brandDTO.getSalesModel() == SalesModelEnum.NORMAL_MODE.getCode()) {
            return queryOrdinarySku(itemDTOList, skuInfoRespDTOS);
        }

        // 菜谱模式，取默认菜谱
        PricePlanDO pricePlanDO = pricePlanMapper.queryDefaultPlan(UserContextUtils.getStoreGuid(), brandDTO.getGuid());
        if (ObjectUtils.isEmpty(pricePlanDO)) {
            return queryOrdinarySku(itemDTOList, skuInfoRespDTOS);
        }

        List<ItemDTO> planItemList = typeMapper.queryPlanSku(itemGuidList, pricePlanDO.getGuid());
        if (org.springframework.util.CollectionUtils.isEmpty(planItemList)) {
            return queryOrdinarySku(itemDTOList, skuInfoRespDTOS);
        }

        return queryOrdinarySku(planItemList, skuInfoRespDTOS);
    }

    @NotNull
    private List<SkuInfoRespDTO> queryOrdinarySku(List<ItemDTO> itemDTOList,
                                                  List<SkuInfoRespDTO> skuInfoRespDTOS) {
        Map<String, ItemDTO> itemDOMap = itemDTOList.stream()
                .collect(Collectors.toMap(ItemDTO::getGuid, i -> i));
        skuInfoRespDTOS.forEach(sku -> {
            ItemDTO itemDO = itemDOMap.get(sku.getItemGuid());
            if (ObjectUtils.isEmpty(itemDO)) {
                log.warn("未查询到商品信息 itemGuid={}", sku.getItemGuid());
                return;
            }
            sku.setItemName(itemDO.getName());
            sku.setItemType(itemDO.getItemType());
            sku.setTypeGuid(itemDO.getTypeGuid());
            sku.setTypeName(itemDO.getTypeName());
            sku.setItemType(itemDO.getItemType());
        });
        return skuInfoRespDTOS;
    }

    /**
     * 根据规格guid查询没有加入微信点餐的商品
     *
     * @param skuGuidList 规格guid列表
     * @return 没有加入微信点餐的商品
     */
    @Override
    public List<String> queryNotJoinWechatItem(List<String> skuGuidList) {
        List<SkuDO> skuDOList = this.list(new LambdaQueryWrapper<SkuDO>()
                .in(CollectionUtils.isNotEmpty(skuGuidList), SkuDO::getGuid, skuGuidList)
                .eq(SkuDO::getIsDelete, 0)
                .eq(SkuDO::getIsEnable, 1)
                .eq(SkuDO::getIsJoinWeChat, 0)
        );
        return skuDOList.stream().map(SkuDO::getGuid).collect(Collectors.toList());
    }

    /**
     * 查询规格详情列表
     * 如果是套餐的规格，则增加子项的规格详情
     *
     * @param itemStringListDTO 规格guid列表
     * @return 规格详情列表
     */
    @Override
    public List<SkuTakeawayInfoRespDTO> listSkuInfoAndSub(ItemStringListDTO itemStringListDTO) {
        List<String> skuGuidList = itemStringListDTO.getDataList();
        List<SkuTakeawayInfoRespDTO> skuTakeawayInfoRespDTOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuGuidList)) {
            log.warn("入参为空");
            return skuTakeawayInfoRespDTOS;
        }
        List<SkuDO> skuDOList = this.list(new LambdaQueryWrapper<SkuDO>()
                .in(CollectionUtils.isNotEmpty(skuGuidList), SkuDO::getGuid, skuGuidList)
                .eq(SkuDO::getIsDelete, 0)
                .eq(SkuDO::getIsEnable, 1));
        if (CollectionUtils.isEmpty(skuDOList)) {
            log.warn("规格结果为空");
            return skuTakeawayInfoRespDTOS;
        }
        List<String> itemGuidList = skuDOList.stream()
                .map(SkuDO::getItemGuid)
                .distinct()
                .collect(Collectors.toList());
        List<ItemInfoRespDTO> pkgItemInfoList = listPkgItemInfo(itemGuidList);

        Map<String, ItemInfoRespDTO> itemInfoMap = pkgItemInfoList.stream()
                .collect(Collectors.toMap(ItemInfoRespDTO::getItemGuid, Function.identity(), (dto1, dto2) -> dto2));
        for (SkuDO sku : skuDOList) {
            ItemInfoRespDTO itemInfo = itemInfoMap.get(sku.getItemGuid());
            if (ObjectUtils.isEmpty(itemInfo)) {
                continue;
            }

            SkuTakeawayInfoRespDTO infoRespDTO = new SkuTakeawayInfoRespDTO();
            infoRespDTO.setSkuGuid(sku.getGuid());
            infoRespDTO.setParentSkuGuid(sku.getParentGuid());
            infoRespDTO.setSkuName(sku.getName());
            infoRespDTO.setItemName(itemInfo.getName());
            infoRespDTO.setSalePrice(sku.getSalePrice());
            infoRespDTO.setUnit(sku.getUnit());
            infoRespDTO.setIsRack(sku.getIsRack());
            infoRespDTO.setItemType(itemInfo.getItemType());
            infoRespDTO.setItemGuid(itemInfo.getItemGuid());
            infoRespDTO.setTypeGuid(itemInfo.getTypeGuid());
            infoRespDTO.setTypeName(itemInfo.getTypeName());
            infoRespDTO.setCode(itemInfo.getCode());
            if (Objects.equals(ItemTypeEnum.PACKAGE.getTypeCode(), itemInfo.getItemType())) {
                infoRespDTO.setSubgroupList(itemInfo.getSubgroupList());
            }
            skuTakeawayInfoRespDTOS.add(infoRespDTO);
        }
        return skuTakeawayInfoRespDTOS;
    }

    /**
     * 因循环依赖复制的接口
     *
     * @param itemGuidList
     * @return
     */
    private List<ItemInfoRespDTO> listPkgItemInfo(List<String> itemGuidList) {
        List<ItemDO> itemDOList = itemService.list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, itemGuidList));
        if (org.springframework.util.CollectionUtils.isEmpty(itemDOList)) {
            log.info("商品查询为空 itemGuidList={}", itemGuidList);
            return new ArrayList<>();
        }
        List<ItemInfoRespDTO> respDTOS = MapStructUtils.INSTANCE.itemDOList2ItemInfoRespDTOList(itemDOList);
        List<SubgroupBO> subgroupBOList = subgroupService.selectSubgroupListByItemGuidList(itemGuidList);
        if (org.springframework.util.CollectionUtils.isEmpty(subgroupBOList)) {
            return respDTOS;
        }
        List<SubgroupWebRespDTO> subgroupWebRespDTOS = MapStructUtils.INSTANCE.subgroupBOList2subgroupWebRespDTOList(subgroupBOList);
        Map<String, List<SubgroupWebRespDTO>> subGroupMap = subgroupWebRespDTOS.stream()
                .collect(Collectors.groupingBy(SubgroupWebRespDTO::getItemGuid));
        List<String> itemList = subgroupWebRespDTOS.stream()
                .flatMap(s -> s.getSubItemSkuList().stream().map(SubItemSkuWebRespDTO::getItemGuid))
                .collect(Collectors.toList());
        List<String> skuList = subgroupWebRespDTOS.stream()
                .flatMap(s -> s.getSubItemSkuList().stream().map(SubItemSkuWebRespDTO::getSkuGuid))
                .collect(Collectors.toList());
        Collection<ItemDO> itemDOS = itemService.listByIds(itemList);
        Collection<SkuDO> skuDOS = this.listByIds(skuList);
        Map<String, String> typeMap = itemDOS.stream()
                .collect(Collectors.toMap(ItemDO::getGuid, ItemDO::getTypeGuid));
        Map<String, String> itemNameMap = itemDOS.stream()
                .collect(Collectors.toMap(ItemDO::getGuid, ItemDO::getName));
        Map<String, String> skuNameMap = skuDOS.stream()
                .collect(Collectors.toMap(SkuDO::getGuid, SkuDO::getName));

        // 最终处理
        respDTOS.forEach(resp -> {
            if (Objects.equals(ItemTypeEnum.PACKAGE.getTypeCode(), resp.getItemType())) {
                List<SubgroupWebRespDTO> subgroupList = subGroupMap.get(resp.getItemGuid());
                if (!org.springframework.util.CollectionUtils.isEmpty(subgroupList)) {
                    // 设置子商品typeGuid，itemName,skuName
                    subgroupList.forEach(sub ->
                            sub.getSubItemSkuList().forEach(i -> {
                                i.setTypeGuid(typeMap.get(i.getItemGuid()));
                                i.setItemName(itemNameMap.get(i.getItemGuid()));
                                i.setSkuName(skuNameMap.get(i.getSkuGuid()));
                            }));
                    resp.setSubgroupList(subgroupList);
                }
            }
        });
        return respDTOS;
    }


    @Override
    public List<SkuInfoRespDTO> listSkuInfoByRecipeMode(String storeGuid, List<String> skuGuidList) {
        List<SkuInfoRespDTO> skuListByMode = Lists.newArrayList();
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(storeGuid);
        log.info("查询当前品牌信息:{}", JacksonUtils.writeValueAsString(brandDTO));
        if (Objects.nonNull(brandDTO) && Objects.equals(brandDTO.getSalesModel(), SalesModelEnum.RECIPE_MODE.getCode())) {
            skuListByMode = getRecipeModeSkuList(storeGuid, skuGuidList);
        } else {
            // 查询除了套餐外的菜品,对上下架不做限定，传入上下架字段，由调用方处理
            List<SkuDO> skuDOList = list(
                    new LambdaQueryWrapper<SkuDO>()
                            .in(SkuDO::getGuid, skuGuidList)
            );
            if (CollectionUtils.isEmpty(skuDOList)) {
                return skuListByMode;
            }
            List<String> itemGuidList = skuDOList.stream().map(SkuDO::getItemGuid).distinct().collect(Collectors.toList());
            List<ItemDO> list = itemService.list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, itemGuidList));
            Map<String, ItemDO> itemDOMap = list.stream().collect(Collectors.toMap(ItemDO::getGuid, i -> i));
            List<SkuInfoRespDTO> storeSkuInfoList = MapStructUtils.INSTANCE.skuDOList2skuInfoRespDTOList(skuDOList);

            List<String> pkgGuidList = list.stream()
                    .filter(itemDO -> itemDO.getItemType() == 1)
                    .map(ItemDO::getGuid)
                    .collect(Collectors.toList());
            Map<String, List<SkuInfoPkgDTO>> pkgMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(pkgGuidList)) {
                List<SkuInfoPkgDTO> pkgList = skuMapper.listPkgInfoByItemGuid(pkgGuidList);
                pkgMap = pkgList.stream().collect(groupingBy(SkuInfoPkgDTO::getParentGuid));
            }

            Map<String, List<SkuInfoPkgDTO>> finalPkgMap = pkgMap;
            storeSkuInfoList.forEach(sku -> {
                ItemDO itemDO = itemDOMap.get(sku.getItemGuid());
                if (ObjectUtils.isEmpty(itemDO)) {
                    log.warn("未查询到商品信息 itemGuid={}", sku.getItemGuid());
                    return;
                }
                sku.setItemName(itemDO.getName());
                sku.setItemType(itemDO.getItemType());
                sku.setItemTypeGuid(itemDO.getTypeGuid());

                //设置商品套餐信息
                if (itemDO.getItemType() == 1) {
                    sku.setIsPkgItem(Boolean.TRUE);
                    sku.setListPkg(finalPkgMap.get(itemDO.getGuid()));
                }
            });
            skuListByMode.addAll(storeSkuInfoList);
        }
        log.info("规格商品信息：{}", JacksonUtils.writeValueAsString(skuListByMode));
        return skuListByMode;
    }

    @Override
    public List<SkuInfoPkgDTO> listPkgInfoByItemGuid(List<String> itemGuidList) {
        if (CollectionUtil.isEmpty(itemGuidList)) {
            return Lists.newArrayList();
        }
        return skuMapper.listPkgInfoByItemGuid(itemGuidList);
    }

    @Override
    public List<String> listSkuGuid(List<String> skuGuidList) {
        if (CollectionUtils.isEmpty(skuGuidList)) {
            return Lists.newArrayList();
        }
        List<SkuDO> subSkuList = this.list(new LambdaQueryWrapper<SkuDO>()
                .in(SkuDO::getParentGuid, skuGuidList)
                .eq(SkuDO::getStoreGuid, UserContextUtils.getStoreGuid()));
        List<SkuDO> storeSkuList = this.list(new LambdaQueryWrapper<SkuDO>()
                .in(SkuDO::getGuid, skuGuidList));
        List<String> allGuidList = subSkuList.stream().map(SkuDO::getGuid).distinct().collect(toList());
        allGuidList.addAll(storeSkuList.stream().map(SkuDO::getParentGuid).filter(Objects::nonNull).distinct().collect(toList()));
        return allGuidList;
    }

    @Override
    public Map<String, String> queryParentSkuGuidBySku(ItemStringListDTO query) {
        if (CollectionUtils.isEmpty(query.getDataList())) {
            log.info("skuGuid为空！");
            return Maps.newHashMap();
        }
        List<SkuDO> skuList = this.list(new LambdaQueryWrapper<SkuDO>()
                .in(SkuDO::getGuid, query.getDataList()));
        if (CollectionUtils.isEmpty(skuList)) {
            log.info("sku为空！");
            return Maps.newHashMap();
        }
        Map<String, String> skuMap = new HashMap<>();
        skuList.forEach(sku -> {
            String skuParentGuid = sku.getParentGuid();
            if (StringUtils.isEmpty(skuParentGuid)) {
                skuParentGuid = sku.getGuid();
            }
            skuMap.put(sku.getGuid(), skuParentGuid);
        });
        return skuMap;
    }

    /**
     * 查询外卖绑定的erp商品
     * 菜谱和普通模式都查询
     *
     * @param storeGuid 门店guid
     * @return erp商品
     */
    @Override
    public List<ErpMappingType> queryErpItem(String storeGuid) {
        List<MappingRespDTO> allItems = this.mappingRecipeModeItems(storeGuid);
        if (CollectionUtils.isEmpty(allItems)) {
            return new ArrayList<>();
        }
        return allItems.stream().filter(erp -> Objects.nonNull(erp.getIsRack()) && erp.getIsRack() == 1
                        && null != erp.getIsChoice() && erp.getIsChoice() == 1)
                .collect(groupingBy(o -> o.getCategoryId() + ":" + o.getCategoryName(), LinkedHashMap::new, toList()))
                .entrySet().stream().map(stringListEntry -> {
                    ErpMappingType erpMappingType = new ErpMappingType();
                    String key = stringListEntry.getKey();
                    String categoryId = key.split(":")[0];
                    String categoryName = key.split(":")[1];
                    erpMappingType.setErpItemTypeId(categoryId);
                    erpMappingType.setErpItemTypeName(categoryName);
                    List<ErpMappingItem> erpMappingItemList = stringListEntry.getValue().stream()
                            .map(mappingRespDTO -> {
                                ErpMappingItem erpMappingItem = new ErpMappingItem();
                                erpMappingItem.setErpItemGuid(mappingRespDTO.geteDishCode());
                                erpMappingItem.setErpItemName(mappingRespDTO.getDishNameWithSpec());
                                erpMappingItem.setErpItemSkuId(mappingRespDTO.getParentSkuGuid());
                                erpMappingItem.setErpItemPrice(mappingRespDTO.getErpItemPrice());
                                erpMappingItem.setTakeawayAccountingPrice(mappingRespDTO.getTakeawayAccountingPrice());
                                return erpMappingItem;
                            }).collect(Collectors.toList());
                    erpMappingType.setErpItemList(erpMappingItemList);
                    return erpMappingType;
                }).collect(Collectors.toList());
    }

    /**
     * 查询所有门店外卖绑定的erp商品
     *
     * @param storeGuids 门店guid列表
     * @return 所有门店外卖绑定的erp商品
     */
    @Override
    public List<StoreItemListRespDTO> queryErpItemByStoreGuids(List<String> storeGuids) {
        List<StoreItemListRespDTO> list = Collections.synchronizedList(new ArrayList<>());
        if (CollectionUtils.isEmpty(storeGuids)) {
            return list;
        }
        Set<String> storeGuidSet = new HashSet<>(storeGuids);
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        storeGuidSet.parallelStream().forEach(storeGuid -> {
            UserContextUtils.putErp(enterpriseGuid);
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            List<MappingRespDTO> itemsByStoreGuid = this.mappingRecipeModeItems(storeGuid);
            if (CollectionUtils.isNotEmpty(itemsByStoreGuid)) {
                List<ErpMappingItem> erpMappingItemList = itemsByStoreGuid.stream()
                        .filter(erp -> Objects.nonNull(erp.getIsRack()) && erp.getIsRack() == 1
                                && null != erp.getIsChoice() && erp.getIsChoice() == 1)
                        .map(mappingRespDTO -> {
                            ErpMappingItem erpMappingItem = new ErpMappingItem();
                            erpMappingItem.setErpItemGuid(mappingRespDTO.geteDishCode());
                            erpMappingItem.setErpItemName(mappingRespDTO.getDishNameWithSpec());
                            erpMappingItem.setErpItemSkuId(mappingRespDTO.getParentSkuGuid());
                            erpMappingItem.setErpItemPrice(mappingRespDTO.getErpItemPrice());
                            erpMappingItem.setTakeawayAccountingPrice(mappingRespDTO.getTakeawayAccountingPrice());
                            return erpMappingItem;
                        })
                        .collect(toList());
                StoreItemListRespDTO respDTO = new StoreItemListRespDTO();
                respDTO.setStoreGuid(storeGuid);
                respDTO.setErpMappingItemList(erpMappingItemList);
                list.add(respDTO);
            }
        });
        log.info("查询外卖绑定的erp商品:{}", JacksonUtils.writeValueAsString(list));
        return list;
    }

    /**
     * 根据规格guid查询对应商品全名
     * 包含菜谱
     *
     * @param skuGuidList 规格guid列表
     * @return 商品全名
     */
    @Override
    public List<ItemWebRespDTO> listSkuForName(List<String> skuGuidList) {
        if (CollectionUtils.isEmpty(skuGuidList)) {
            log.warn("listSkuForName-入参规格guid为空");
            return new ArrayList<>();
        }
        List<SkuDO> skuDOList = new ArrayList<>(this.listByIds(skuGuidList));
        if (CollectionUtils.isEmpty(skuDOList)) {
            log.warn("listSkuForName-未查询到规格信息,skuGuidList={}", skuGuidList);
            return new ArrayList<>();
        }
        List<String> itemGuidList = skuDOList.stream()
                .map(SkuDO::getItemGuid)
                .distinct()
                .collect(toList());
        List<ItemDO> itemDOList = itemMapper.selectList(new LambdaQueryWrapper<ItemDO>()
                .in(ItemDO::getGuid, itemGuidList));
        if (CollectionUtils.isEmpty(itemDOList)) {
            log.warn("listSkuForName-未查询到商品信息,itemGuidList={}", itemGuidList);
            return new ArrayList<>();
        }
        Map<String, String> itemNameMap = itemDOList.stream()
                .collect(Collectors.toMap(ItemDO::getGuid, ItemDO::getName, (v1, v2) -> v2));
        HashMap<String, String> planNameMap = getPlanNameMap("", skuDOList);

        List<ItemWebRespDTO> respDTOList = new ArrayList<>();
        skuDOList.forEach(skuDO -> {
            ItemWebRespDTO respDTO = new ItemWebRespDTO();
            respDTO.setSkuGuid(skuDO.getGuid());
            String itemName = itemNameMap.get(skuDO.getItemGuid());
            if (StringUtils.isEmpty(itemName)) {
                log.warn("未查询到商品名称,itemName={}", skuDO.getItemGuid());
                return;
            }
            respDTO.setName(itemName);
            if (!StringUtils.isEmpty(skuDO.getName())) {
                respDTO.setName(itemName + "（" + skuDO.getName() + "）");
            }
            String planItemName = planNameMap.get(skuDO.getGuid());
            if (!StringUtils.isEmpty(planItemName)) {
                respDTO.setPlanItemName(planItemName);
            }
            respDTOList.add(respDTO);
        });
        return respDTOList;
    }

    /**
     * 查询菜谱模式下该门店菜谱商品（全时段菜谱和特殊时段菜谱的菜品都有查询）
     */
    private List<SkuInfoRespDTO> getRecipeModeSkuList(String storeGuid, List<String> skuGuidList) {
        List<SkuInfoRespDTO> skuInfoRespDTOS = Lists.newArrayList();
        // 根据门店查询生效的菜谱
        LocalDateTime now = LocalDateTime.now();
        String minute = (now.getMinute() + "").length() == 1 ? "0" + now.getMinute() : now.getMinute() + "";
        int currentTime = Integer.parseInt(now.getHour() + "" + minute);
        log.info("当前时间：" + currentTime);

        List<PricePlanNowDTO> planingList = pricePlanStoreService.findPlanNowStoreGuid(now, storeGuid);
        if (CollectionUtils.isEmpty(planingList)) {
            log.warn("没有生效的菜谱方案 now={},storeGuid={}", now.toString(), storeGuid);
            return skuInfoRespDTOS;
        }

        // 有生效的菜谱
        PricePlanNowDTO pricePlanNowAllTime = null;
        PricePlanNowDTO pricePlaningTime = null;
        for (PricePlanNowDTO planing : planingList) {
            Integer sellTimeType = planing.getSellTimeType();
            if (sellTimeType == 0) {
                // 全时段菜品方案
                pricePlanNowAllTime = planing;
            } else {
                // 特殊时段菜品方案
                LocalDateTime startTime = planing.getStartTime();
                LocalDateTime endTime = planing.getEndTime();
                String startMinute = (startTime.getMinute() + "").length() == 1 ? "0" + startTime.getMinute() : startTime.getMinute() + "";
                int startTimeInt = Integer.parseInt(startTime.getHour() + "" + startMinute);
                String endMinute = (endTime.getMinute() + "").length() == 1 ? "0" + endTime.getMinute() : endTime.getMinute() + "";
                int endTimeInt = Integer.parseInt(endTime.getHour() + "" + endMinute);
                log.info("特殊时段时间,nowTime->{}, startTimeInt->{}, endTimeInt->{}", currentTime, startTimeInt, endTimeInt);
                if (endTimeInt > startTimeInt) {
                    // 没有垮天
                    if (currentTime >= startTimeInt && currentTime <= endTimeInt) {
                        pricePlaningTime = planing;
                        log.info("没有垮天特殊时段方案筛选结果：,{}", JacksonUtils.writeValueAsString(pricePlaningTime));
                    }
                } else {
                    // 跨天了
                    if (currentTime >= startTimeInt || currentTime <= endTimeInt) {
                        pricePlaningTime = planing;
                        log.info("跨天了特殊时段方案筛选结果：,{}", JacksonUtils.writeValueAsString(pricePlaningTime));
                    }
                }
            }
        }

        // 没有菜谱方案
        log.info("生效的菜谱：pricePlanNowTime={},pricePlanNowAllTime={}", JacksonUtils.writeValueAsString(pricePlaningTime),
                JacksonUtils.writeValueAsString(pricePlanNowAllTime));
        if (Objects.isNull(pricePlaningTime) && Objects.isNull(pricePlanNowAllTime)) {
            log.warn("没有生效的菜谱方案");
            return skuInfoRespDTOS;
        }

        String planGuid = "";
        if (!Objects.isNull(pricePlaningTime)) {
            // 特殊时段菜谱 商品列表
            planGuid = pricePlaningTime.getPlanGuid();
        } else {
            // 全时段菜谱 商品列表
            planGuid = pricePlanNowAllTime.getPlanGuid();
        }
        Map<String, PricePlanItemDO> planSkuMap = findPricePlanItem(planGuid, skuGuidList);
        if (MapUtils.isEmpty(planSkuMap)) {
            log.warn("菜谱方案商品为空");
            return skuInfoRespDTOS;
        }

        List<SkuDO> recipeModeSkuList = list(
                new LambdaQueryWrapper<SkuDO>()
                        .in(SkuDO::getGuid, planSkuMap.keySet()));

        // 通过菜谱和商品guid查询商品详情
        List<String> itemGuidList = recipeModeSkuList.stream()
                .map(SkuDO::getItemGuid)
                .collect(Collectors.toList());

        List<ItemDO> list = itemService.list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, itemGuidList));
        Map<String, ItemDO> itemDOMap = list.stream().collect(Collectors.toMap(ItemDO::getGuid, i -> i));

        skuInfoRespDTOS = MapStructUtils.INSTANCE.skuDOList2skuInfoRespDTOList(recipeModeSkuList);
        log.info("查询商品结果：{}", JacksonUtils.writeValueAsString(skuInfoRespDTOS));

        List<String> pkgGuidList = list.stream()
                .filter(itemDO -> itemDO.getItemType() == 1)
                .map(ItemDO::getGuid)
                .collect(Collectors.toList());
        Map<String, List<SkuInfoPkgDTO>> pkgMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(pkgGuidList)) {
            List<SkuInfoPkgDTO> pkgList = skuMapper.listPkgInfoByItemGuid(pkgGuidList);
            // 查询菜谱里对应的商品信息
            List<String> subSkuGuids = pkgList.stream().map(SkuInfoPkgDTO::getSkuGuid).collect(toList());
            Map<String, PricePlanItemDO> pricePlanSubItemMap = findPricePlanItem(planGuid, subSkuGuids);
            // 更新商品信息
            updateSkuInfoPkg(pkgList, pricePlanSubItemMap);
            pkgMap = pkgList.stream().collect(groupingBy(SkuInfoPkgDTO::getParentGuid));
        }

        // 设置菜谱的价格
        if (CollectionUtils.isNotEmpty(skuInfoRespDTOS)) {
            Map<String, List<SkuInfoPkgDTO>> finalPkgMap = pkgMap;
            skuInfoRespDTOS.forEach(sku -> {
                ItemDO itemDO = itemDOMap.get(sku.getItemGuid());
                if (ObjectUtils.isEmpty(itemDO)) {
                    log.warn("未查询到商品信息 itemGuid={}", sku.getItemGuid());
                    return;
                }
                // 默认设置品牌库信息
                sku.setItemType(itemDO.getItemType());
                sku.setItemName(itemDO.getName());
                sku.setItemTypeGuid(itemDO.getTypeGuid());

                // 如果是菜谱商品,则设置菜谱信息
                PricePlanItemDO planSku = planSkuMap.get(sku.getSkuGuid());
                if (ObjectUtils.isEmpty(planSku)) {
                    log.warn("菜谱中未查询到商品信息 skuGuid={}", sku.getSkuGuid());
                    return;
                }
                sku.setItemName(planSku.getPlanItemName());
                sku.setSalePrice(planSku.getSalePrice());
                sku.setAccountingPrice(planSku.getAccountingPrice());
                sku.setTakeawayAccountingPrice(planSku.getTakeawayAccountingPrice());
                //设置商品套餐信息
                if (itemDO.getItemType() == 1) {
                    sku.setIsPkgItem(Boolean.TRUE);
                    sku.setListPkg(finalPkgMap.get(itemDO.getGuid()));
                }
            });
        }
        return skuInfoRespDTOS;
    }


    private Map<String, PricePlanItemDO> findPricePlanItem(String planGuid, List<String> skuGuids) {
        List<PricePlanItemDO> pricePlanItemDOList = planItemMapper.selectList(new LambdaQueryWrapper<PricePlanItemDO>()
                .ne(PricePlanItemDO::getIsSoldOut, ItemStateEnum.IMMEDIATELY_DELETE.getCode())
                .eq(PricePlanItemDO::getIsDelete, 0)
                .eq(PricePlanItemDO::getPlanGuid, planGuid)
                .in(PricePlanItemDO::getSkuGuid, skuGuids));
        return pricePlanItemDOList.stream()
                .collect(Collectors.toMap(PricePlanItemDO::getSkuGuid, Function.identity(), (key1, key2) -> key1));
    }

    private void updateSkuInfoPkg(List<SkuInfoPkgDTO> pkgList, Map<String, PricePlanItemDO> pricePlanSubItemMap) {
        for (SkuInfoPkgDTO pkgDTO : pkgList) {
            PricePlanItemDO pricePlanItemDO = pricePlanSubItemMap.get(pkgDTO.getSkuGuid());
            if (Objects.isNull(pricePlanItemDO)) {
                continue;
            }
            pkgDTO.setItemName(pricePlanItemDO.getPlanItemName());
            pkgDTO.setSalePrice(pricePlanItemDO.getSalePrice());
            pkgDTO.setAccountingPrice(pricePlanItemDO.getAccountingPrice());
            pkgDTO.setTakeawayAccountingPrice(pricePlanItemDO.getTakeawayAccountingPrice());
        }
    }
}
