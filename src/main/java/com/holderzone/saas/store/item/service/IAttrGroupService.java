package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupReqDTO;
import com.holderzone.saas.store.dto.item.req.AttrGroupUpdateReqDTO;
import com.holderzone.saas.store.dto.item.resp.AttrGroupAttrRespDTO;
import com.holderzone.saas.store.dto.item.resp.AttrGroupSynRespDTO;
import com.holderzone.saas.store.item.entity.domain.AttrGroupDO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 属性值（属性组下的属性内容） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
public interface IAttrGroupService extends IService<AttrGroupDO> {

    /**
     * 获取属性组集合（包括属性组下的属性值）
     *
     * @param itemSingleDTO 门店guid or 品牌guid
     * @return 属性组集合（包括属性值）
     */
    List<AttrGroupAttrRespDTO> listAttrGroupByOrganization(ItemSingleDTO itemSingleDTO);

    /**
     * 新增商品获取属性组列表（只含启用的属性组）
     *
     * @param itemSingleDTO 门店guid or 品牌guid
     * @return 启用的属性组集合（包括属性值）
     */
    List<AttrGroupAttrRespDTO> listAttrForSaveItem(ItemSingleDTO itemSingleDTO);

    /**
     * 新增属性组
     *
     * @param attrGroupReqDTO attrGroupReqDTO
     * @return boolean
     */
    boolean addAttrGroup(AttrGroupReqDTO attrGroupReqDTO);

    /**
     * 更新属性组
     *
     * @param attrGroupUpdateReqDTO attrGroupUpdateReqDTO
     * @return boolean
     */
    boolean updateAttrGroup(AttrGroupUpdateReqDTO attrGroupUpdateReqDTO);

    /**
     * 删除属性组
     * 删除属性组会同时删除属性组下的属性值
     *
     * @param itemSingleDTO 属性组guid
     * @return boolean
     */
    boolean deleteByGuid(ItemSingleDTO itemSingleDTO);

    int getLastSort(ItemSingleDTO itemSingleDTO);

    /**
     * 移除指定门店下被推送的属性组，如果有属性组下关联了门店自建属性或商品，则将属性组转为门店自建属性组
     * @param storeGuid
     * @return
     */
    Integer removePushAttrGroup(String storeGuid);

    /**
     * 根据商品GUID集合返回关联的属性组详情
     * @param itemGuidList
     * @return
     */
    List<AttrGroupSynRespDTO> selectAttrGroupSynRespDtoByItemGuidList(Collection<String> itemGuidList);
}
