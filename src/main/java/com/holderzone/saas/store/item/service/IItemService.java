package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.CreateRepertoryReqDTO;
import com.holderzone.saas.store.dto.item.common.*;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.print.PrintItemReqDto;
import com.holderzone.saas.store.dto.weixin.resp.ItemImgDTO;
import com.holderzone.saas.store.item.dto.SyncStoreAndItemDTO;
import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 商品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
public interface IItemService extends IService<ItemDO> {

    ItemSaveRespDTO saveItem(ItemReqDTO itemSaveReqDTO);

    List<String> getItemInfoList2(List<String> itemGuidList);

    List<String> updateItem(ItemReqDTO itemUpdateReqDTO);

    /**
     * 更新商品pad图片
     *
     * @param padPictureDTO 属性值保存DTO
     * @return
     */
    Integer editPadPicture(PadPictureDTO padPictureDTO);

    Integer batchDelete(ItemStringListDTO itemStringListDTO, Boolean isCheckIntegral);

    // todo 添加根据门店或品牌获取其下商品以及分类的接口

    /**
     * 查询当前品牌是否有绑定的商品分类或商品
     *
     * @param brandGuid 当前品牌GUID
     * @return true:count>0,即品牌下有商品分类或商品;false:count=0，即品牌下无商品分类或商品
     */
    boolean countTypeOrItem(String brandGuid);

    /**
     * 获取门店菜品或门店菜谱菜品
     *
     * @param req req
     * @return ItemAndTypeForAndroidRespDTO
     */
    ItemAndTypeForAndroidRespDTO selectItemDetailAndTypeForSyn(ItemQueryListReq req);

    /**
     * 赚餐扫描点餐搜索商品
     *
     * @param wxSearchItemDto 微信搜索请求参数
     * @return 商品数据
     */
    Page<ItemSynRespDTO> wxSearchItems(WxSearchItemDto wxSearchItemDto);

    List<AttrGroupWebRespDTO> selectItemAttrList(String itemGuid);

    List<SubgroupWebRespDTO> selectSubgroupList(String itemGuid);

    Page<ItemWebRespDTO> selectItemListForWeb(ItemQueryReqDTO itemQueryReqDTO);

    Page<PadPictureRespDTO> queryPadPicture(PadPictureDTO padPictureDTO);

    ItemInfoRespDTO getItemInfo(ItemSingleDTO itemSingleDTO);

    /**
     * 根据商品guid列表批量查询商品信息
     *
     * @param itemGuidList 商品Guid集合
     * @param hasDeleted   是否包含已删除商品
     * @return 商品信息
     */
    List<ItemInfoRespDTO> getItemInfoList(List<String> itemGuidList, boolean hasDeleted);

    /**
     * 菜品排序更新
     *
     * @param itemSortUpdateReqDTO itemSortUpdateReqDTO
     * @return Boolean
     */
    Boolean updateItemSort(ItemSortUpdateReqDTO itemSortUpdateReqDTO);

    /**
     * 菜谱MQ推送
     *
     * @param storeAndItem storeAndItem
     * @return Integer
     */
    Integer pushItemsByPrice(SyncStoreAndItemDTO storeAndItem);

    Integer pushItems(PushItemReqDTO pushItemReqDTO, boolean isNeedCorrect);

    void removeAllRelationStore(ItemStringListDTO itemReqDTO, List<String> skuGuidList, String itemGuid, String brandGuid);

    /**
     * 批量导入商品
     *
     * @param itemDoubleParamDTO itemDoubleParamDTO 第一个数据是商品数据集合， 第二个数据是storeGuid or brandGuid
     * @return 导入的数量
     */
    CreateRepertoryReqDTO batchImport(ItemDoubleParamDTO<List<ItemExcelTemplateReqDTO>, String> itemDoubleParamDTO);


    /**
     * 批量开启库存
     *
     * @param repertoryReqDTO
     * @param storeGuid
     */
    void batchOpenStack(CreateRepertoryReqDTO repertoryReqDTO, String storeGuid);

    /**
     * 获取下一个排序值
     *
     * @param itemSingleDTO from/data都必传，  data：storeGuid or brandGuid
     * @return 排序值
     */
    int getSort(ItemSingleDTO itemSingleDTO);

    /**
     * 打印获取商品列表
     *
     * @param itemReqDto baseDTO
     * @return List<TypeItemListDTO>
     */
    List<TypeItemListDTO> selectTypeItemList(PrintItemReqDto itemReqDto);

    /**
     * 查询价格方案的菜品规格。
     *
     * @param storeGuid storeGuid
     * @param skuGuids  查询的skuGuid
     * @return List<SkuDO>
     */
    List<SkuDO> pricePlanItemSku(String storeGuid, List<String> skuGuids);

    Integer removePush(String storeGuid);

    /**
     * 获取itemGuid和parentGuid的List
     *
     * @param itemGuidList
     * @return
     */
    List<ItemInfoRespDTO> kdsItemParentMapping(List<String> itemGuidList);

    /**
     * 分页获取商品模板菜单子商品集合
     *
     * @param request
     * @return
     */
    Page<ItemTemplateSubItemRespDTO> selectSkuItemList(ItemTemplateMenuAllSubItemReqDTO request);

    /**
     * 在批量导入之前根据商品名称获取商品集合
     *
     * @param request
     * @return
     */
    List<ItemBatchImportTempRespDTO> selectItemsBeforeImport(BatchImportGetItemsReqDTO request);

    /**
     * 报表大屏菜品支持->查询企业下门店所有商品（包含推送商品）
     *
     * @return
     */
    List<JournalingItemRespDTO> selectJournalingItem();

    /**
     * 根据商品guid获取商品图片集合
     *
     * @param request
     */
    List<ItemImgDTO> selectItemPictureUrls(SingleDataDTO request);

    /**
     * 获取团餐商品详情
     *
     * @param data
     * @return
     */
    GroupMealItemDetailRespDTO selectGroupMealDetail(String data);

    /**
     * 更新ItemDO的hasAttr字段
     *
     * @param itemGuid
     * @return
     */
    Boolean updateHasAttr(String itemGuid, Integer hasAttr);

    /**
     * 获取有属性商品且商品属性组关联为0的商品guid
     *
     * @return
     */
    List<String> getHasAttrItemGuid();

    List<String> updateItemSku(ItemReqDTO itemUpdateReqDTO);

    List<String> deleteItemSku(ItemReqDTO itemUpdateReqDTO);

    /**
     * 批量修改
     *
     * @param itemBatchUpdateDTO
     * @return
     */
    Integer batchUpdate(ItemBatchUpdateDTO itemBatchUpdateDTO);

    /**
     * 交换排序
     *
     * @param itemReqDTO
     * @return
     */
    Boolean switchSort(@Valid ItemSortSwitchReqDTO itemReqDTO);

    /**
     * 根据条件搜索
     *
     * @param itemSearchReqDTO
     * @return
     */
    List<ItemSearchRespDTO> search(@RequestBody ItemSearchReqDTO itemSearchReqDTO);

    /**
     * 点赞
     *
     * @param itemVoteReqDTO
     * @return
     */
    Integer voteUp(ItemVoteReqDTO itemVoteReqDTO);

    /**
     * 点踩
     *
     * @param itemVoteReqDTO
     * @return
     */
    Integer voteDown(ItemVoteReqDTO itemVoteReqDTO);

    /**
     * 根据门店guid列表批量获取门店下商品详情及分类信息
     *
     * @param itemStringListDTO 里面是门店guid列表
     * @return 门店下商品详情及分类信息
     */
    ItemInfoAndTypeRespDTO listItemInfoAndType(ItemStringListDTO itemStringListDTO);

    /**
     * 根据门店guid列表批量获取门店下商品详情
     *
     * @param itemStringListDTO 里面是门店guid列表
     * @return
     */
    List<ItemInfoRespDTO> queryItemInfoByStoreGuidList(ItemStringListDTO itemStringListDTO);

    /**
     * 通过父级ID获取子集ID
     *
     * @param parentItemGuid
     * @param storeGuid
     * @return
     */
    List<Map<String, Object>> getItemInfoListByParentId(String parentItemGuid, String storeGuid);

    /**
     * 同归parentGuid和storeGuid删除item
     *
     * @param parentItemGuid
     * @param storeGuid
     * @return
     */
    Integer removeItemByParentId(String parentItemGuid, String storeGuid);

    /**
     * 通过storeGuid和parentGuid获取Item的guid
     *
     * @param storeGuid
     * @param parentGuid
     * @return
     */
    String getGuidByStoreGuidAndParentGuid(String storeGuid, String parentGuid);

    /**
     * 通过GUID删除数据
     *
     * @param guid
     * @param itemFrom
     * @param storeGuid
     * @return
     */
    Integer deleteByGuidAndFrom(String guid, Long itemFrom, String storeGuid);

    /**
     * 将品牌所属的storeGuid置为null
     *
     * @param itemGuid
     * @param storeGuid
     * @param brandGuid
     */
    Integer updateOriginItemStoreGuid(String itemGuid, String storeGuid, String brandGuid);

    /**
     * 通过storeGuid查询items
     *
     * @param storeGuid
     * @return
     */
    List<Map<String, Object>> getItemsByStoreGuid(String storeGuid);

    /**
     * 获取品牌下所有商品及分类
     *
     * @param brandGuid String
     * @return List<TypeItemListDTO>
     */
    List<TypeItemListDTO> queryStoreByBrandList(String brandGuid);

    /**
     * 批量移动商品:选中一个分类下的商品，批量移动到其它分类下
     *
     * @param typeSortReqDTO 商品guidList，目标分类guid
     * @return boolean
     */
    Boolean batchMoveItem(TypeSortReqDTO typeSortReqDTO);

    List<String> queryFilterItem(ItemSingleDTO itemSingleDTO);

    /**
     * 根据门店GUID查询所有商品详情已经分类信息（区分销售模式）
     *
     * @param itemSingleDTO storeGuid
     * @return 分类和商品详情
     */
    ItemInfoAndTypeRespDTO queryStoreItemBySalesModel(ItemSingleDTO itemSingleDTO);


    ItemInfoAndTypeRespDTO queryStoreItemBySalesNew(ItemSingleDTO itemSingleDTO);

    /**
     * 获取分类排序  区分销售模式
     *
     * @param typeSingleDTO
     * @return
     */
    List<ItemInfoTypeRespDTO> queryStoreItemType(TypeSingleDTO typeSingleDTO);

    /**
     * 根据门店guidList查询所有商品并过滤
     * 只返回传入的商品GUID对应的商品的详情
     *
     * @param itemStringListDTO 门店guid，商品guid
     * @return 过滤后的商品信息
     */
    ItemInfoAndTypeRespDTO queryStoreItemAndFilter(ItemStringListDTO itemStringListDTO);

    /**
     * 通过商品guid和门店guid查询对应spu的所有商品Guid
     *
     * @param itemSpuReqDTO 商品信息请求参数
     * @return 对应商品Guid集合
     */
    List<String> selectSpuItems(ItemSpuReqDTO itemSpuReqDTO);

    Map<String, List<String>> selectSpuItemMaps(ItemSpuReqDTO itemSpuReqDTO);

    /**
     * 赚餐后台查询商品接口
     * 原来是从mdm调，现在要加入菜谱方案，改为直接掉商户后台
     *
     * @param itemQueryDTO ItemQueryDTO
     * @return List<SelectItemDTO>
     */
    List<SelectItemDTO> selectItemList(ItemQueryDTO itemQueryDTO);

    /**
     * 存入item服务的redis
     *
     * @param redisReqDTO 存入redis所需要的值
     * @return 成功
     */
    Boolean putItemRedis(ItemRedisReqDTO redisReqDTO);

    /**
     * 查询导出商品信息
     *
     * @param itemExportReqDTO
     * @return
     */
    List<ItemExportRespDTO> getExportItemList(ItemExportReqDTO itemExportReqDTO);

    /**
     * 通过商品名称集合查询对应sku
     *
     * @param itemNames 商品名称
     * @return 查询结果
     */
    Map<String, String> findSkusByItemName(List<String> itemNames);

    /**
     * 根据商品guid获取菜谱中的商品详情列表
     *
     * @param itemStringListDTO 门店guid，商品guid列表
     * @return 商品详情列表
     */
    List<ItemInfoRespDTO> selectItemInfoListOnPlan(ItemStringListDTO itemStringListDTO);

    /**
     * 根据品牌或者门店查询最后一个商品的sort并+1
     *
     * @param itemSingleDTO 品牌或者门店，来源
     * @return 品牌或者门店查询最后一个商品的sort并+1
     */
    int getMaxSort(ItemSingleDTO itemSingleDTO);

    /**
     * 通过skuGuid查询规格商品信息
     * 暂未包含规格信息
     *
     * @param skuGuidList 规格guid
     * @return 商品信息
     */
    List<ItemInfoRespDTO> listItemInfoBySku(List<String> skuGuidList);

    /**
     * 获取品牌下所有分类及商品
     *
     * @param itemSingleDTO 品牌guid，关键字
     * @return 分类及商品
     */
    List<TypeItemRespDTO> listTypeAndItemByBrand(ItemSingleDTO itemSingleDTO);

    List<ItemInfoRespDTO> listItemInfoBySalesModel(ItemStringListDTO itemStringListDTO);

    List<ItemInfoRespDTO> listItemInfoBySalesModelNew(ItemStringListDTO itemStringListDTO);

    PrintSortRespDTO selectPrintItemType(PrintItemTypeDTO itemTypeDTO);

    List<ItemInfoRespDTO> queryParentItemInfo(List<String> dataList);

    String getSubItemGuid(String parentItemGuid);

    /**
     * 查询商品是否所在门店
     *
     * @param itemSingleDTO 门店、品牌、商品
     * @return 存在的门店
     */
    List<String> queryItemStore(ItemSingleDTO itemSingleDTO);

    List<ItemInfoRespDTO> getItemNameList(ItemStringListDTO itemStringListDTO);

    Map<String, String> queryParentItemGuidBySku(ItemStringListDTO query);

    Map<String, String> queryParentItemGuidByItem(ItemStringListDTO query);

    Page<ItemWebRespDTO> queryItemByBrand(ItemQueryReqDTO queryReqDTO);

    List<ItemWebRespDTO> queryItemByGuid(ItemStringListDTO query);

    /**
     * 查询推荐商品
     */
    List<ItemSynRespDTO> queryRecommendItem(ItemSingleDTO query);

    /**
     * 通过门店查询方案商品
     */
    List<ItemSynRespDTO> queryPlanItemByStore(String storeGuid);
}
