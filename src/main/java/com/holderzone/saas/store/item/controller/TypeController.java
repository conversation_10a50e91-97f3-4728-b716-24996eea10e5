package com.holderzone.saas.store.item.controller;


import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.TypeReqDTO;
import com.holderzone.saas.store.dto.item.req.TypeSortSwitchReqDTO;
import com.holderzone.saas.store.dto.item.resp.JournalingItemRespDTO;
import com.holderzone.saas.store.dto.item.resp.SaleTypeRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeSynRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeWebRespDTO;
import com.holderzone.saas.store.item.entity.domain.TypeDO;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.service.IRetailItemService;
import com.holderzone.saas.store.item.service.ITypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 门店库的商品分类入口
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Slf4j
@RestController
@RequestMapping("/type")
@Api(value = "分类接口")
public class TypeController {

    private final ITypeService typeService;

    private final ItemHelper itemHelper;

    private final IRetailItemService retailItemService;

    @Autowired
    public TypeController(ITypeService typeService, ItemHelper itemHelper, IRetailItemService retailItemService) {
        this.typeService = typeService;
        this.itemHelper = itemHelper;
        this.retailItemService = retailItemService;
    }

    @GetMapping("/info")
    public TypeWebRespDTO info(@RequestParam String typeGuid) {
        log.info("分类查询：{}", typeGuid);
        TypeDO typeDO = typeService.getById(typeGuid);
        if (ObjectUtils.isEmpty(typeDO)) {
            return null;
        }
        TypeWebRespDTO typeWebRespDTO = new TypeWebRespDTO();
        BeanUtils.copyProperties(typeDO, typeWebRespDTO);
        return typeWebRespDTO;
    }

    @PostMapping("/get_sort")
    public Integer getSort(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("分类获取分类排序接口入参,dishSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return typeService.getSort(itemSingleDTO);
    }

    @ApiOperation(value = "保存分类", notes = "保存分类")
    @PostMapping("/save")
    public Integer save(@RequestBody @Valid TypeReqDTO typeReqDTO) {
        log.info("分类新增接口入参,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTO));
        return typeService.saveType(typeReqDTO);
    }

    @ApiOperation(value = "修改分类", notes = "修改分类")
    @PostMapping("/update")
    public Integer update(@RequestBody @Valid TypeReqDTO typeReqDTO) {
        log.info("分类修改接口入参,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTO));
        return typeService.updateType(typeReqDTO);
    }

    /**
     * 交换两个分类的排序
     *
     * @param typeReqDTO
     * @return
     */
    @PostMapping("/switch_sort")
    public Boolean switchSort(@RequestBody @Valid TypeSortSwitchReqDTO typeReqDTO) {
        log.info("交换两个分类的排序,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTO));
        try {
            typeService.switchSort(typeReqDTO);
            return true;
        } catch (BusinessException ex) {
            log.warn("交换分类排序失败", ex);
        }
        return false;
    }

    /**
     * 批量修改分类顺序
     *
     * @param typeReqDTOList 里面只有sort和typeGuid
     * @return 1
     */
    @ApiOperation(value = "批量修改分类顺序")
    @PostMapping("/batch_modify_sort")
    public Integer batchModifySort(@RequestBody List<TypeReqDTO> typeReqDTOList) {
        log.info("批量修改分类顺序,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTOList));
        return typeService.batchModifySort(typeReqDTOList);
    }

    @PostMapping("/delete")
    public Integer delete(@RequestBody @Valid ItemSingleDTO itemSingleDTO) {
        log.info("分类修改接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return typeService.deleteType(itemSingleDTO);
    }

    @ApiOperation(value = "快速创建分类", notes = "快速创建分类")
    @PostMapping("/quick_save")
    public Integer quickSave(@RequestBody TypeReqDTO typeReqDTO) {
        log.info("分类新增接口入参,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTO));
        validateQuickSaveTypeReqDTO(typeReqDTO);
        return typeService.saveType(typeReqDTO);
    }


    @ApiOperation(value = "查询品牌/门店分类列表", notes = "查询品牌/门店分类列表")
    @PostMapping("/query_type")
    public List<TypeWebRespDTO> queryType(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("分类接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return typeService.queryType(itemSingleDTO);
    }

    @ApiOperation(value = "查询品牌列表下的所有分类")
    @PostMapping("/query_type_by_brand")
    public List<TypeWebRespDTO> queryTypeByBrand(@RequestBody ItemStringListDTO query) {
        log.info("[查询品牌列表下的所有分类]query={}", JacksonUtils.writeValueAsString(query));
        return typeService.queryTypeByBrand(query);
    }

    @ApiOperation(value = "查询门店分类列表", notes = "查询门店分类列表")
    @PostMapping("/query_type_by_stores")
    @Deprecated
    public List<TypeWebRespDTO> queryTypeByStoreGuidList(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("根据门店列表查询分类接口入参,itemStringListDTO={}", JacksonUtils.writeValueAsString(itemStringListDTO));
        itemHelper.validateItemStringListDTO(itemStringListDTO);
        return typeService.queryTypeByStoreGuidList(itemStringListDTO);
    }


    @ApiOperation(value = "报表大屏支持", notes = "报表大屏支持")
    @PostMapping("/query_journaling_item_type")
    public List<JournalingItemRespDTO> queryJournalingItemType(@RequestBody @Valid BaseDTO request) {
        log.info("报表大屏商品分类支持->查询企业下门店所有商品分类（包含推送分类）, request={}", JacksonUtils.writeValueAsString(request));
        return typeService.queryJournalingItemType();
    }


    @ApiOperation(value = "判断商品类型下面是否存在商品  存在1/2", notes = "判断商品类型下面是否存在商品  存在1/2")
    @PostMapping("/verify_item_exists_for_type")
    public Integer verifyThatitemExistsForType(@RequestBody @Valid SingleDataDTO request) {
        log.info("判断商品类型下面是否存在商品,request = {}", JacksonUtils.writeValueAsString(request));
        return retailItemService.verifyThatitemExistsForType(request) ? 1 : 2;
    }

    @ApiOperation(value = "修改团餐功能状态", notes = "修改团餐功能状态")
    @PostMapping("/set_group_meal_status")
    public String setGroupMealStatus(@RequestBody SingleDataDTO request) {
        log.info("设置团餐当前状态, 入参 request = {}", JacksonUtils.writeValueAsString(request));
        return typeService.setGroupMealStatus(request) ? request.getData() : "-1";
    }


    @ApiOperation(value = "查询团餐功能开启状态", notes = "查询团餐功能开启状态")
    @PostMapping("/select_group_meal_status")
    public String selectGroupMealStatus(@RequestBody SingleDataDTO request) {
        log.info("查询团餐当前状态，入参 request = {}", JacksonUtils.writeValueAsString(request));
        return typeService.selectGroupMealStatus(request.getData());
    }


    private void validateQuickSaveTypeReqDTO(TypeReqDTO typeReqDTO) {
        if (StringUtils.isEmpty(typeReqDTO.getName())) {
            throw new ParameterException("名称必填");
        } else if (typeReqDTO.getName().equals("宴会套餐")) {
            throw new ParameterException("宴会套餐分类名称已被宴会套餐功能占用，请重新命名");
        }
    }

    /**
     * 通过价格方案查询分类列表
     *
     * @param itemSingleDTO 价格方案guid
     * @return 分类列表
     */
    @ApiOperation(value = "通过价格方案查询分类列表", notes = "通过价格方案查询分类列表")
    @PostMapping("/query_type_by_plan")
    public List<TypeWebRespDTO> queryTypeByPlan(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("通过价格方案查询分类列表入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        return typeService.queryTypeByPlan(itemSingleDTO);
    }

    /**
     * 相关所有分类
     */
    @ApiOperation(value = "根据分类id查询源分类信息")
    @PostMapping("/query_source_type_info")
    public List<TypeWebRespDTO> querySourceTypeInfo(@RequestBody ItemStringListDTO itemStringListDTO) {
        log.info("根据分类id查询源分类信息,itemStringListDTO={}", itemStringListDTO);
        return typeService.querySourceTypeInfo(itemStringListDTO);
    }

    /**
     * 通过门店查询门店商品在品牌库所属的分类
     */
    @ApiOperation(value = "通过门店查询门店商品在品牌库所属的分类")
    @PostMapping("/query_brand_type_by_store")
    public List<SaleTypeRespDTO> queryBrandTypeByStore(@RequestBody ItemSingleDTO query) {
        log.info("[通过门店查询门店商品在品牌库所属的分类],query={}", query);
        return typeService.queryBrandTypeByStore(query);
    }

}
