package com.holderzone.saas.store.item.entity.bo;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.item.constant.EstimateOpTypeEnum;
import com.holderzone.saas.store.item.entity.domain.EstimateDO;
import com.holderzone.saas.store.item.entity.domain.EstimateOpLogDO;
import com.holderzone.saas.store.item.service.EstimateOpLogBuildable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class EstimateBO implements EstimateOpLogBuildable {

    @ApiModelProperty(value = "商品guid")
    private String itemGuid;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "估清规格信息列表")
    private List<EstimateDO> skuList;

    private List<String> itemGuidList;

    private List<EstimateDO> createSkuList;

    private List<EstimateDO> updateSkuList;

    private List<EstimateDO> removeSkuList;

    private List<EstimateDO> batchCancelSkuList;

    private List<EstimateDO> batchStopSkuList;

    private List<EstimateDO> schedulingSkuList;

    private List<EstimateDO> reduceStockSkuList;

    private List<EstimateDO> returnStockSkuList;

    @Override
    @JsonIgnore
    public List<EstimateOpLogDO> buildEstimateOpLog() {
        List<EstimateOpLogDO> opLogList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(createSkuList)) {
            List<EstimateOpLogDO> createOpLogList = build(createSkuList, EstimateOpTypeEnum.CREATE.getOpType());
            opLogList.addAll(createOpLogList);
        }
        if (CollectionUtils.isNotEmpty(updateSkuList)) {
            List<EstimateOpLogDO> updateOpLogList = build(updateSkuList, EstimateOpTypeEnum.UPDATE.getOpType());
            opLogList.addAll(updateOpLogList);
        }
        if (CollectionUtils.isNotEmpty(removeSkuList)) {
            List<EstimateOpLogDO> removeOpLogList = build(removeSkuList, EstimateOpTypeEnum.REMOVE.getOpType());
            opLogList.addAll(removeOpLogList);
        }
        if (CollectionUtils.isNotEmpty(batchCancelSkuList)) {
            List<EstimateOpLogDO> batchOpLogList = build(batchCancelSkuList, EstimateOpTypeEnum.BATCH_CANCEL.getOpType());
            opLogList.addAll(batchOpLogList);
        }
        if (CollectionUtils.isNotEmpty(batchStopSkuList)) {
            List<EstimateOpLogDO> batchOpLogList = build(batchStopSkuList, EstimateOpTypeEnum.BATCH_STOP.getOpType());
            opLogList.addAll(batchOpLogList);
        }
        if (CollectionUtils.isNotEmpty(schedulingSkuList)) {
            List<EstimateOpLogDO> schedulingLogList = build(schedulingSkuList, EstimateOpTypeEnum.SCHEDULING.getOpType());
            opLogList.addAll(schedulingLogList);
        }
        if (CollectionUtils.isNotEmpty(reduceStockSkuList)) {
            List<EstimateOpLogDO> reduceStockLogList = build(reduceStockSkuList, EstimateOpTypeEnum.REDUCE_STOCK.getOpType());
            opLogList.addAll(reduceStockLogList);
        }
        if (CollectionUtils.isNotEmpty(returnStockSkuList)) {
            List<EstimateOpLogDO> returnStockLogList = build(returnStockSkuList, EstimateOpTypeEnum.RETURN_STOCK.getOpType());
            opLogList.addAll(returnStockLogList);
        }
        return opLogList;
    }


    private List<EstimateOpLogDO> build(List<EstimateDO> skuList, String opType) {
        List<EstimateOpLogDO> opLogList = Lists.newArrayList();
        for (EstimateDO estimateDO : skuList) {
            EstimateOpLogDO opLogDO = new EstimateOpLogDO();
            opLogDO.setStoreGuid(estimateDO.getStoreGuid());
            opLogDO.setSkuGuid(estimateDO.getSkuGuid());
            opLogDO.setParams(JSONObject.toJSONString(estimateDO));
            opLogDO.setOpRefid(estimateDO.getGuid());
            opLogDO.setOpType(String.valueOf(opType));
            opLogDO.setOpTime(LocalDateTime.now());
            opLogDO.setOperatorGuid(UserContextUtils.getUserGuid());
            opLogDO.setOperatorName(UserContextUtils.getUserName());
            opLogDO.setDeviceGuid(deviceId);
            opLogList.add(opLogDO);
        }
        return opLogList;
    }
}
