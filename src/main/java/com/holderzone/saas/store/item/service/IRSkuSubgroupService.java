package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.item.req.SubItemSkuReqDTO;
import com.holderzone.saas.store.item.entity.domain.RSkuSubgroupDO;
import com.holderzone.saas.store.item.entity.domain.SubgroupDO;

import java.util.List;

/**
 * <p>
 * 规格与分组关联表（规格为套餐分组中的子菜规格） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-25
 */
public interface IRSkuSubgroupService extends IService<RSkuSubgroupDO> {
    /**
     * 根据套餐GUID集合删除其下所有分组与子商品的关联关系（不删除分组）
     *
     * @param itemGuidList 套餐GUID集合
     * @return
     */
    Integer removeSkuSubgroupByPkgGuidList(List<String> itemGuidList);

    /**
     * 根据分组实体构建其下子商品规格实体
     *
     * @param subItemSkuList
     * @param subgroupDO
     * @return
     */
    List<RSkuSubgroupDO> buildRSkuSubgroupDOWithSubgroupDO(List<SubItemSkuReqDTO> subItemSkuList, SubgroupDO subgroupDO);

    /**
     * 更新删除状态
     *
     * @param guid
     * @return
     */
    Boolean updateIsDelete(String guid);

    /**
     * 通过itemGuid更新is_delete字段
     *
     * @param itemGuid
     * @return
     */
    Boolean deleteByItemGuid(String itemGuid);

}
