package com.holderzone.saas.store.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.item.entity.domain.RAttrItemAttrGroupDO;

/**
 * <p>
 * 属性值与商品属性组关联表的关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
public interface IRAttrItemAttrGroupService extends IService<RAttrItemAttrGroupDO> {

    /**
     * 更加属性值guid删除绑定关系
     *
     * @param attrGuid 属性值guid
     * @return boolean
     */
    boolean deleteByAttr(String attrGuid);
}
