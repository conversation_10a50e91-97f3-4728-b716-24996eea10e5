package com.holderzone.saas.store.item.service.rpc;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.organization.*;
import com.holderzone.saas.store.item.config.LocalCacheConfig;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = OrganizationService.ServiceFallBack.class)
public interface OrganizationService {

    @PostMapping("/store/query_store_by_idlist")
    List<StoreDTO> queryStoreByIdList(@RequestBody List<String> storeGuidList);

    @ApiOperation("设置商品是否上传")
    @PostMapping("/store/itemUploadUpdate")
    boolean itemUploadUpdate(@RequestBody @Validated ItemUploadUpdateReq req);

    @ApiOperation("根据品牌列表查询品牌列表下的所有门店")
    @PostMapping("/store/query_store_by_brandlist")
    List<StoreDTO> queryStoreByBrandList(@RequestBody List<String> brandGuidList);

    /**
     * 根据门店guid查询门店关联的品牌信息
     *
     * @param storeGuid 门店guid
     * @return 品牌信息
     */
    @ApiOperation(value = "根据门店guid查询门店关联的品牌信息", notes = "若门店未关联到品牌则返回为null，后期一个门店可关联多个品牌")
    @RequestMapping("/store/query_brand_by_storeguid")
    BrandDTO queryBrandByStoreGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 根据门店guidList查询门店关联的品牌信息
     *
     * @param storeGuidList 门店guidList
     * @return 品牌信息
     */
    @ApiOperation(value = "根据门店guidList查询门店关联的品牌信息")
    @GetMapping("/store/query_brand_by_store_guid_list")
    List<BrandDTO> queryBrandListByStoreGuidList(@RequestParam("storeGuidList") List<String> storeGuidList);

    /**
     * 根据品牌guid查询品牌信息
     *
     * @param brandGuid 品牌guid
     * @return 品牌信息
     */
    @ApiOperation("根据品牌guid查询品牌信息")
    @PostMapping("/brand/query_brand_by_guid")
    BrandDTO queryBrandByGuid(@RequestParam("brandGuid") String brandGuid);

    /**
     * 根据品牌guid查询品牌下所有门店Guid
     *
     * @param brandGuid 品牌guid
     * @return 品牌下所有门店
     */
    @ApiOperation("根据品牌guid查询品牌下所有门店Guid")
    @PostMapping("/brand/query_store_guid_list_by_brand_guid")
    List<String> queryStoreGuidListByBrandGui(@RequestBody String brandGuid);

    /**
     * 获取营业日期
     *
     * @param businessDateReqDTO 入参
     * @return 营业日
     */
    @PostMapping("/store/query_business_day")
    LocalDate queryBusinessDay(@RequestBody BusinessDateReqDTO businessDateReqDTO);

    /**
     * 根据门店guid查询门店详细信息
     *
     * @param storeGuid 门店guid
     * @return StoreDTO
     */
    @ApiOperation("根据门店guid查询门店详细信息")
    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 根据门店名称模糊查询门店列表
     * @param queryStoreDTO 查询条件
     * @return 门店列表
     */
    @PostMapping("/store/list_store_by_condition")
    List<StoreDTO> listStoreByCondition(@RequestBody QueryStoreDTO queryStoreDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<OrganizationService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OrganizationService create(Throwable cause) {
            return new OrganizationService() {

                @Override
                public List<StoreDTO> queryStoreByIdList(List<String> storeGuidList) {
                    throw new RuntimeException("通过guidList查询门店信息失败,e:{}" + cause.getMessage());
                }

                @Override
                public boolean itemUploadUpdate(ItemUploadUpdateReq req) {
                    log.error("通过门店guid设置商品是否上传失败 {}", req, cause);
                    return false;
                }

                @Override
                public List<StoreDTO> queryStoreByBrandList(List<String> brandGuidList) {
                    log.error("根据品牌列表查询品牌列表下的所有门店 e={}", cause.getMessage());
                    throw new ParameterException("根据品牌列表查询品牌列表下的所有门店失败!");
                }

                @Override
                public BrandDTO queryBrandByStoreGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryBrandByStoreGuid", storeGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<BrandDTO> queryBrandListByStoreGuidList(List<String> storeGuidList) {
                    log.error(HYSTRIX_PATTERN, "queryBrandListByStoreGuidList", storeGuidList, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public BrandDTO queryBrandByGuid(String brandGuid) {
                    log.error(HYSTRIX_PATTERN, "queryBrandByGuid", brandGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<String> queryStoreGuidListByBrandGui(String brandGuid) {
                    log.error(HYSTRIX_PATTERN, "queryStoreGuidListByBrandGui", brandGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public LocalDate queryBusinessDay(BusinessDateReqDTO businessDateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryBusinessDay", JacksonUtils.writeValueAsString(businessDateReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryStoreByGuid", storeGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<StoreDTO> listStoreByCondition(QueryStoreDTO queryStoreDTO) {
                    log.error(HYSTRIX_PATTERN, "listStoreByCondition", JacksonUtils.writeValueAsString(queryStoreDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
