package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateMenusRespDTO;
import com.holderzone.saas.store.enums.item.WeekEnum;
import com.holderzone.saas.store.item.constant.Constant;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.entity.domain.ItemTMenuDO;
import com.holderzone.saas.store.item.entity.query.ItemTemplatesQuery;
import com.holderzone.saas.store.item.helper.ItemHelper;
import com.holderzone.saas.store.item.mapper.ItemTMenuMapper;
import com.holderzone.saas.store.item.service.IItemTMenuService;
import com.holderzone.saas.store.item.service.IItemTMenuSubitemService;
import com.holderzone.saas.store.item.service.IItemTMenuValidityService;
import com.holderzone.saas.store.item.service.IItemTemplateService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.MapStructUtils;
import com.holderzone.sdk.util.BatchIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品模板-菜单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
@Service
public class ItemTMenuServiceImpl extends ServiceImpl<ItemTMenuMapper, ItemTMenuDO> implements IItemTMenuService {
    private final DynamicHelper dynamicHelper;
    private final IItemTMenuSubitemService itemTMenuSubitemService;
    private final IItemTMenuValidityService itemTMenuValidityService;
    private final ItemHelper itemHelper;
    private final RedisTemplate redisTemplate;
    private final IItemTemplateService iItemTemplateService;
    private  final ItemTMenuMapper itemTMenuMapper;

    @Autowired
    public ItemTMenuServiceImpl(DynamicHelper dynamicHelper, IItemTMenuSubitemService itemTMenuSubitemService, IItemTMenuValidityService itemTMenuValidityService, ItemHelper itemHelper, RedisTemplate redisTemplate, IItemTemplateService iItemTemplateService, ItemTMenuMapper itemTMenuMapper) {
        this.dynamicHelper = dynamicHelper;
        this.itemTMenuSubitemService = itemTMenuSubitemService;
        this.itemTMenuValidityService = itemTMenuValidityService;
        this.itemHelper = itemHelper;
        this.redisTemplate = redisTemplate;
        this.iItemTemplateService = iItemTemplateService;
        this.itemTMenuMapper = itemTMenuMapper;
    }

    @Override
    public Boolean saveOrUpdate(ItemTMenuReqDTO request) {
        ItemTMenuDO  itemTMenuDO = MapStructUtils.INSTANCE.itemTMenuReqDTO2itemTMenuDO(request);
        if (!Optional.ofNullable(itemTMenuDO).map(ItemTMenuDO::getGuid).isPresent()) {
            try {
                String guid = String.valueOf(BatchIdGenerator.getGuid(redisTemplate, GuidKeyConstant.HSI_ITEM));
                itemTMenuDO.setGuid(guid);
            } catch (IOException e) {
                throw new BusinessException("BatchIdGenerator生成商品guid失败");
            }
        }
        return saveOrUpdate(itemTMenuDO);
    }

    @Transactional
    @Override
    public Boolean saveItemMenu(ItemTemplateMenuSubitemReqDTO request) {
        //删除菜单
        if(Optional.ofNullable(request).map(ItemTemplateMenuSubitemReqDTO::getMenuGuid).isPresent()) {
            if(request.getIsDelete().equals(1)) {
                ItemTMenuDO itemTMenuDO = super.getById(request.getMenuGuid());
                String storeGuid = iItemTemplateService.getStoreGuidByCode(0,itemTMenuDO.getTemplateGuid());
                itemHelper.pushMsg(storeGuid);
                itemTMenuValidityService.removeMenuTime(request.getMenuGuid());
                itemTMenuSubitemService.removeMenuSubItem(request.getMenuGuid());
               return removeById(request.getMenuGuid());
            }
        }
        Assert.hasText(request.getTemplateGuid(),"商品模板guid不能为空");
        //1.验证时间是否重复、合法
        String  result = itemHelper.validateMenuTime(request.getPeriodicMode(),request.getTemplateGuid(),request.getItmeTemplateMenuValidityReqDTO(),request.getMenuGuid());
        if(result != null) {
            throw  new BusinessException(result);
        }
        if(!Optional.ofNullable(request).map(ItemTemplateMenuSubitemReqDTO::getMenuGuid).isPresent()) {
            try {
                request.setMenuGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, GuidKeyConstant.HSI_ITEM)));
            } catch (IOException e) {
                throw new BusinessException(Constant.CREATE_GUID_FAIL);
            }
        }
        Boolean flag = saveMenu(request);
        if (flag){
            String storeGuid = iItemTemplateService.getStoreGuidByCode(0,request.getTemplateGuid());
           itemHelper.pushMsg(storeGuid);
        }
        return flag;
    }



    @Override
    public List<ItemTemplateMenusRespDTO> getItemTemplateMenus(SingleDataDTO request) {
        List<ItemTemplateMenusRespDTO> itemTemplateMenusRespDTOlist = new ArrayList<>();
        List<ItemTemplatesQuery> itemTemplates = itemTMenuMapper.getItemTemplateMenus(request);
        Map<String, List<ItemTemplatesQuery>> collect =  itemTemplates
                .stream()
                .collect(Collectors.groupingBy(ItemTemplatesQuery::getMenuGuid));
        Map<String,List<ItemTemplatesQuery>> sortmap = new TreeMap<>();
        collect.entrySet()
                .stream()
                .forEach(x->sortmap.put(x.getKey(),x.getValue()));
        if(!itemTemplates.isEmpty()){
            //按时段
           if(itemTemplates.get(0).getPeriodicMode().equals(1)){
               sortmap.forEach((k,v)->{
                   List<ItemTemplateMenuTimeReqDTO>  menuTimes = JacksonUtils.toObjectList(ItemTemplateMenuTimeReqDTO.class,v.
                           stream()
                           .findFirst()
                           .get()
                           .getTimes());
                   List<String> executeTimes = new ArrayList<>();
                   menuTimes.forEach(o->{
                       executeTimes.add(o.getStartTime() + "-" + o.getEndTime());
                   });
                   itemTemplateMenusRespDTOlist.add(ItemTemplateMenusRespDTO.builder()
                   .guid(k)
                   .executeTimes(executeTimes)
                   .templateGuid(v.get(0).getGuid()).build());
               });
           }else{ //按星期
               sortmap.forEach((k,v) -> {
                 String str =   v.get(0).getWeeks() ;
                   str =  str.replace("[","").replace("]","").replace(" ","");
                   String[] strWeeks = str.split(",");
                   List<String> executeTimes = new ArrayList<>();
                   for (String o:strWeeks) {
                       executeTimes.add(WeekEnum.getDescByCode(Integer.parseInt(o)));
                   }
                   itemTemplateMenusRespDTOlist.add(ItemTemplateMenusRespDTO.builder()
                           .guid(k)
                           .executeTimes(executeTimes)
                           .templateGuid(v
                                   .stream()
                                   .findFirst()
                                   .get()
                                   .getGuid()).build());
               });
           }
        }
        Collections.reverse(itemTemplateMenusRespDTOlist);
        return itemTemplateMenusRespDTOlist;
    }

    /**
     * 保存商品模板-菜单-子项菜品
     * @param request
     * @return
     */
    private  Boolean  saveItemMenuSubItem(ItemTemplateMenuSubitemReqDTO request){
        List<ItemMenuSubItemReqDTO> list = request.getItemMenuSubItemReqDTOS();
      return  itemTMenuSubitemService.saveItemMenuSubItem(list,request.getMenuGuid());
    }

    /**
     * 保存商品模板-菜单-执行时间
     * @param request
     * @return
     */
    private  Boolean  saveItemTMenuValidity(ItemTemplateMenuSubitemReqDTO request){
        ItmeTemplateMenuValidityReqDTO itmeTemplateMenuValidityReqDTO = request.getItmeTemplateMenuValidityReqDTO();
        return  itemTMenuValidityService.saveItemMenuValidity(itmeTemplateMenuValidityReqDTO,request.getPeriodicMode(),request.getMenuGuid(),request.getIsItFullTime());
    }

    /**
     * 更新/保存菜单及其子项信息
     * @param request
     * @return
     */
    private Boolean saveMenu(ItemTemplateMenuSubitemReqDTO request){
        ItemTMenuReqDTO  itemTMenuReqDTO =  ItemTMenuReqDTO.builder()
                .templateGuid(request.getTemplateGuid())
                .guid(request.getMenuGuid()).build();
        //保存商品模板-菜单
        saveOrUpdate(itemTMenuReqDTO);
        //更新商品模板-菜单-执行时间
        saveItemTMenuValidity(request);
        //保存商品模板-菜单-子项菜品
        saveItemMenuSubItem(request);
        return Boolean.TRUE;
    }

}
