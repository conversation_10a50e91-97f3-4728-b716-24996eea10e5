package com.holderzone.saas.store.item.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UpdateTerminalStatusDTO {
    @ApiModelProperty("店铺guid")
    private String storeGuid;

    @ApiModelProperty("父级guid")
    private String parentGuid;

    @ApiModelProperty("商品guid")
    private String itemGuid;

    @ApiModelProperty("是否上架一体机（0：否，1：是）")
    private Integer isJoinAio;

    @ApiModelProperty("是否上架POS机（0：否，1：是）")
    private Integer isJoinPos;

    @ApiModelProperty("是否上架Pad（0：否，1：是）")
    private Integer isJoinPad;

    @ApiModelProperty(value = "是否参与小程序商城（冗余小程序端字段）")
    private Integer isJoinMiniAppMall;

    @ApiModelProperty(value = "是否参与小程序外卖（冗余小程序端字段）")
    private Integer isJoinMiniAppTakeaway;
    
    @ApiModelProperty(value = " 是否支持堂食（冗余小程序端字段）")
    private Integer isJoinStore;
}
