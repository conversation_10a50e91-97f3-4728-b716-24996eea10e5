package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.dto.item.req.GroupMealSkuSaveReqDTO;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.entity.bo.ItemRelateGroupMealBO;
import com.holderzone.saas.store.item.entity.domain.GroupMealDO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import com.holderzone.saas.store.item.mapper.GroupMealMapper;
import com.holderzone.saas.store.item.service.IGroupMealService;
import com.holderzone.saas.store.item.util.MapStructUtils;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className IGroupMealDOServiceImpl
 * @date 2019/12/07 下午3:11
 * @description //
 * @program holder
 */

@Slf4j
@Service
public class IGroupMealServiceImpl extends ServiceImpl<GroupMealMapper, GroupMealDO> implements IGroupMealService {

    private  final GroupMealMapper groupMealMapper;
    private final RedisTemplate redisTemplate;

    @Autowired
    public IGroupMealServiceImpl(GroupMealMapper groupMealMapper, RedisTemplate redisTemplate) {
        this.groupMealMapper = groupMealMapper;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public Boolean saveOrUpdateGroupMeal(List<GroupMealSkuSaveReqDTO> skuList, String guid) {
        //传入的列表
        List<GroupMealDO> list = MapStructUtils.INSTANCE.groupMealSubSkuReqDTOList2GroupMealDOList(skuList);
        list.stream().forEach(groupMealDO -> groupMealDO.setItemGuid(guid));
        //数据库存在的数据列表
        List<GroupMealDO> dbList = super.list(
                new LambdaQueryWrapper<GroupMealDO>()
                        .eq(GroupMealDO::getItemGuid, guid));
        //保存
        List<GroupMealDO> saveList = new ArrayList<>(list);
        saveList.removeAll(dbList);
        //删除
        List<GroupMealDO> removeList = new ArrayList<>(dbList);
        removeList.removeAll(list);
        //更新
        List<GroupMealDO> updateList = new ArrayList<>(list);
        updateList.retainAll(dbList);

        if (!saveList.isEmpty() && Optional.ofNullable(saveList).isPresent()) {
            List<Long> guids = BatchIdGenerator.batchGetGuids(redisTemplate, GuidKeyConstant.HSI_GROUP_MEAL, saveList.size());
            for (int i = 0, len = saveList.size(); i < len; i++) {
                saveList.get(i).setGuid(guids.get(i).toString());
            }
            saveBatch(saveList);
        }
        if (!updateList.isEmpty() && Objects.nonNull(updateList)) {
            updateBatchById(updateList);
        }
        if (!removeList.isEmpty() && Objects.nonNull(removeList)) {
            List<String> collect = removeList.stream().map(GroupMealDO::getGuid).collect(Collectors.toList());
            removeByIds(collect);
        }
        return true;
    }

    @Override
    public List<ItemRelateGroupMealBO> mapItem2RelateGroupMealPakNum(List<SkuDO> skuDOList) {
        return groupMealMapper.mapItem2RelateGroupMealPakNum(skuDOList
                .stream()
                .map(SkuDO::getGuid)
                .collect(Collectors.toList()));
    }
}
