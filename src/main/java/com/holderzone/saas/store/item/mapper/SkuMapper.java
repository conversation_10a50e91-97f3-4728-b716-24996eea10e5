package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.resp.ItemSkuSearchRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.SkuInfoPkgDTO;
import com.holderzone.saas.store.item.dto.UpdateTerminalStatusDTO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 商品规格表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Repository
public interface SkuMapper extends BaseMapper<SkuDO> {

    /**
     * 获取每个商品最小的销售价格、会员价格、虚拟价格
     *
     * @param itemList
     * @return
     */
    List<SkuDO> selectSearchSkuByItemGuid(List<String> itemList);


    /**
     * 根据条件获取包括未删除的数据
     *
     * @param skuGuidList
     * @return
     */
    List<ItemSkuSearchRespDTO> querySkuByStoreGuidAndSkuGuidWithIsDelete(
            @Param("storeGuid") String storeGuid, @Param("skuGuidList") List<String> skuGuidList);

    /**
     * 增加月销量
     *
     * @param guid
     * @param monthlySale
     * @return
     */
    int updateMonthlySaleInc(@Param("guid") String guid, @Param("monthlySale") Integer monthlySale);

    /**
     * 初始化月销量
     *
     * @param skuList
     * @return
     */
    int initMonthlySale(List<String> skuList);

    /**
     * 更改库存状态
     *
     * @param guid
     * @param stock
     * @param isOpenStock
     * @return
     */
    int updateStockStatus(@Param("guid") String guid,
                          @Param("stock") BigDecimal stock,
                          @Param("isOpenStock") Integer isOpenStock
    );

    /**
     * 通过itemGuid和storeGuid删除Sku
     *
     * @param itemGuid
     * @param storeGuid
     * @return
     */
    Boolean deleteSkuByItemGuidAndStoreGuid(@Param("itemGuid") String itemGuid, @Param("storeGuid") String storeGuid);

    /**
     * @param itemGuid
     * @param storeGuid
     * @param skuGuid
     * @param originalSalePrice
     * @return
     */
    Integer updateOriginPriceStoreGuid(@Param("itemGuid") String itemGuid,
                                       @Param("storeGuid") String storeGuid,
                                       @Param("skuGuid") String skuGuid,
                                       @Param("originalSalePrice") BigDecimal originalSalePrice,
                                       @Param("originalMemberPrice") BigDecimal originalMemberPrice);

    /**
     * 根据itemGuid集合查询对应的skuGuid集合
     *
     * @param itemGuidList itemGuid集合
     * @return sku集合
     */
    List<SkuDO> getSkuListByItemList(@Param("list") List<String> itemGuidList);

    /**
     * 更新终端的上架状态
     *
     * @param updateTerminalStatusDTO
     * @return
     */
    Integer updateTerminalStatus(@Param("updateTerminalStatusDTO") UpdateTerminalStatusDTO updateTerminalStatusDTO);

    int skuCount(@Param("itemGuid") String itemGuid);

    String getMaxGuid(@Param("itemGuid") String itemGuid,@Param("tempItemGuid") String tempItemGuid);

    /**
     * 用部分skuGuid查询到相同itemGuid下的所有skuGuid
     *
     * @param skuGuids
     * @return
     */
    List<String> selectAllSkuGuidsByPartSkuGuids(@Param("skuGuids") List<String> skuGuids);

    /**
     * 商品guid查询sku(包含删除的)
     *
     * @param itemGuid 商品guid
     * @return sku集合
     */
    List<SkuDO> findByItemGuid(@Param("itemGuid") String itemGuid);

    /**
     * 通过sku查询parentItemGuid和parentSkuGuid拼接信息
     *
     * @param skuGuids skuGuid集合
     * @return 拼接信息
     */
    List<String> findParentItemSkus(@Param("skuGuids")List<String> skuGuids);

    List<ItemWebRespDTO> listBrandItemSkuByRecipe(@Param("dto")ItemSingleDTO itemSingleDTO);

    List<ItemWebRespDTO>  listBrandItemSkuByNormal(@Param("dto")ItemSingleDTO itemSingleDTO);

    List<SkuInfoPkgDTO> listPkgInfoByItemGuid(@Param("itemGuids")List<String> itemGuids);
}
