package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.item.req.EstimateMerchantReqDTO;
import com.holderzone.saas.store.dto.item.resp.EstimateItemResidueMemchantRespDTO;
import com.holderzone.saas.store.dto.item.resp.EstimateMerchantConfigRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidDTO;
import com.holderzone.saas.store.dto.item.resp.PkgSkuDTO;
import com.holderzone.saas.store.item.entity.domain.EstimateDO;
import com.holderzone.saas.store.item.entity.query.SetMealEstimateQuery;
import com.holderzone.saas.store.item.helper.PageAdapter;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 商品sku估清表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-07
 */
@Component
public interface EstimateMapper extends BaseMapper<EstimateDO> {


    IPage<EstimateMerchantConfigRespDTO> getItemEstimates(PageAdapter page, @Param("dto") EstimateMerchantReqDTO request);

    IPage<EstimateItemResidueMemchantRespDTO> getEstimateItemResidue(PageAdapter page, @Param("dto") EstimateMerchantReqDTO request);

    List<ItemEstimateForAndroidDTO> queryEstimateForSyn(@Param("dto") BaseDTO baseDTO);

    List<ItemEstimateForAndroidDTO> queryEstimateAllForSyn(@Param("dto") BaseDTO baseDTO);

    /**
     * 按菜谱查询估清的商品
     *
     * @param planGuid  plan
     * @param storeGuid 门店guid
     * @return List<ItemEstimateForAndroidDTO>
     */
    List<ItemEstimateForAndroidDTO> queryEstimateForSynPlan(@Param("planGuid") String planGuid,
                                                            @Param("storeGuid") String storeGuid);

    List<ItemEstimateForAndroidDTO> queryEstimateAllForSynPlan(@Param("planGuid") String planGuid,
                                                            @Param("storeGuid") String storeGuid);

    Integer storeItemEstimateReset(@Param("storeGuids") List<String> storeGuids);

    List<EstimateDO> queryEstimateBySkuGuidList(@Param("skuGuidList") List<String> skuGuidList);

    List<EstimateDO> queryEstimateBySkuGuidListAndStore(@Param("skuGuidList") List<String> skuGuidList, @Param("storeGuid") String storeGuid);

    List<EstimateDO> queryEstimateByStoreGuidList(@Param("storeGuidList") List<String> storeGuidList);


    Integer storeItemEstimateLimitReset(@Param("storeGuids") List<String> stores);

    Integer storeItemEstimateUniqueReset(@Param("storeGuids") List<String> stores);

    List<SetMealEstimateQuery> getSetMealSubitemEstimate(@Param("itemGuid") String itemGuid,
                                                         @Param("storeGuid") String storeGuid);

    int batchUpdateEstimateBySkuGuid(@Param("skuGuidList") List<String> skuGuidList,
                                     @Param("status") Integer status,
                                     @Param("storeGuid") String storeGuid);

    int batchStopSellBySkuGuid(@Param("skuGuidList") List<String> skuGuidList,
                               @Param("storeGuid") String storeGuid);

    int batchUpdateEstimateByItemGuid(@Param("itemGuidList") List<String> itemGuidList,
                                      @Param("status") Integer status,
                                      @Param("storeGuid") String storeGuid);

    int batchDeleteEstimateByItemGuid(@Param("itemGuidList") List<String> itemGuidList,
                                      @Param("storeGuid") String storeGuid);

    int batchDeleteEstimateBySkuGuid(@Param("skuGuidList") List<String> skuGuidList,
                                     @Param("storeGuid") String storeGuid);

    void batchDeleteEstimateByStoreGuid(@Param("storeList") List<String> storeList);

    List<String> listSubGroupEstimateSkuGuidBySku(@Param("skuGuidList") List<String> skuGuidList);

    List<ItemEstimateForAndroidDTO> queryEstimateBySkuGuid(@Param("skuGuidList") Set<String> skuGuidList, @Param("storeGuid") String storeGuid);

    List<PkgSkuDTO> queryPlanPkgSkuDTO(@Param("planGuid") String planGuid);

    /**
     * 门店下所有套餐信息
     */
    List<PkgSkuDTO> queryNormalPkgSkuDTO(@Param("storeGuid") String storeGuid);

}
