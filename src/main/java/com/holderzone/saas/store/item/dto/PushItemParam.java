package com.holderzone.saas.store.item.dto;

import com.holderzone.saas.store.dto.item.resp.ItemSynRespDTO;
import com.holderzone.saas.store.item.entity.domain.*;
import lombok.Data;

import java.util.List;
@Data
public class PushItemParam {
    /**
     * 价格方案GUID
     */
    private String pricePlanGuid;

    // 被推送门店的GUID集合
    private List<String> storeGuidList ;

    private List<ItemSynRespDTO> itemSynRespDTOList ;
    // 获取品牌库推送的分类集合
    private List<TypeDO> typeListFromBrand ;
    // 获取品牌库推送的规格集合
    private List<SkuDO> skuListFromBrand ;
    // 获取品牌库推送的属性组集合
    private List<AttrGroupDO> attrGroupListFromBrand;
    // 获取品牌库推送的属性集合
    private List<AttrDO> attrListFromBrand;
    // 获取品牌库推送的套餐下分组集合
    private List<SubgroupDO> subgroupListFromBrand ;

    /**
     * 是否更新子商品
     */
    private Boolean isUpdateSubItem;

}
