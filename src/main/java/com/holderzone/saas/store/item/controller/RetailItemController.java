package com.holderzone.saas.store.item.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.ItemQueryReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemRetailSortReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemRetailSortRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemSortRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemWebRespDTO;
import com.holderzone.saas.store.item.service.IRetailItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/retail/item")
@Api(value = "零售商品接口")
public class RetailItemController {


    private final IRetailItemService retailItemService;

    public RetailItemController(IRetailItemService retailItemService) {
        this.retailItemService = retailItemService;
    }

    @ApiOperation("商户后台获取商品列表")
    @PostMapping("/select_item_for_web")
    public Page<ItemWebRespDTO> selectItemForWeb(@RequestBody @Valid ItemQueryReqDTO itemQueryReqDTO) {
        log.info("零售商品列表接口入参,itemQueryReqDTO={}", JacksonUtils.writeValueAsString(itemQueryReqDTO));
        return retailItemService.selectItemListForWeb(itemQueryReqDTO);
    }


    @ApiOperation(value = "查询分类下商品列表", notes = "查询分类下商品列表")
    @PostMapping("/get_item_sort_list")
    public Page<ItemSortRespDTO> getItemSortList(@RequestBody @Valid ItemQueryReqDTO request) {
        log.info("商超排序分类获取商品集合,request = {}", JacksonUtils.writeValueAsString(request));
        return retailItemService.getItemSortList(request);
    }


    @ApiOperation(value = "更新商品排序", notes = "更新商品排序")
    @PostMapping("/retail_update_item_sort")
    public Integer retailUpdateItemSort(@RequestBody @Valid ItemRetailSortReqDTO request) {
        log.info("商超排序分类获取商品集合,request = {}", JacksonUtils.writeValueAsString(request));
        return retailItemService.retailUpdateItemSort(request) ? 1 : 2;
    }


    @ApiOperation(value = "获取分类及其商品列表", notes = "获取分类及其商品列表")
    @PostMapping("/get_sort_type_and_item")
    public ItemRetailSortRespDTO getSortTypeAndItems(@RequestBody @Valid ItemSingleDTO request) {
        request.setFrom(0); //设置门店来源查询
        log.info("商超获取分类排序和分类下商品集合,request = {}", JacksonUtils.writeValueAsString(request));
        return retailItemService.getSortTypeAndItems(request);
    }


}
