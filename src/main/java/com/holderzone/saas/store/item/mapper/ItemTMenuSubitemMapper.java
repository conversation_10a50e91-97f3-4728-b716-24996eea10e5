package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.item.req.ItemTemplateMenuDetailsReqDTO;
import com.holderzone.saas.store.item.entity.domain.ItemTMenuSubitemDO;
import com.holderzone.saas.store.item.entity.query.ItemTemplateMenuDetailQuery;
import com.holderzone.saas.store.item.entity.query.ItemTemplateMenuSubItemDetailQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 商品模板-菜单-商品 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
@Component
public interface ItemTMenuSubitemMapper extends BaseMapper<ItemTMenuSubitemDO> {

    /**
     * 获取商品模板-菜单执行时间详情
     * @param request
     * @return
     */
    ItemTemplateMenuDetailQuery getItemTemplateMenuDetail(@Param("dto")ItemTemplateMenuDetailsReqDTO request);


    /**
     * 获取商品模板-菜单菜品详情
     * @param request
     * @return
     */
   List<ItemTemplateMenuSubItemDetailQuery> getItemTemplateMenuSubItemDetailQuery(@Param("dto")ItemTemplateMenuDetailsReqDTO request);

    /**
     * 根据菜单下—菜品guid 获取门店guid
     * @param guid
     * @return
     */
    String getStoreGuid(@Param("guid") String guid);
}
