package com.holderzone.saas.store.item.entity.query;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateMenuDetailQuery
 * @date 2019/06/01 18:53
 * @description //TODO 商品模板菜单执行时间详情
 * @program holder-saas-aggregation-app
 */
@Data
public class ItemTemplateMenuDetailQuery {


    /**
     * 执行时间业务主键
     */
    private String guid;

    /**
     * 菜单guid
     */
    private String menuGuid;
    /**
     * 菜单执行时间模式 1：时间段 2：星期  3：混合（时间段+星期）
     */
    private Integer periodicMode;

    /**
     * 是否全时段 1：否 2：是
     */
    private Integer isItFullTime;


    /**
     * 菜单执行星期
     */
    private String weeks;
    /**
     * 菜单执行时间段json串
     */
    private String times;
}
