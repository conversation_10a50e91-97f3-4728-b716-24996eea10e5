package com.holderzone.saas.store.item.util;

import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> R
 * @date 2021/3/11 10:29
 * @description
 */
@Component
@AllArgsConstructor
public class RedisUtil {

    private final RedisTemplate redisTemplate;


    /**
     * @param key
     * @param value
     * @describle 存json对象
     */
    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
//        redisTemplate.expire(key, 24, TimeUnit.HOURS);
        redisTemplate.persist(key);
    }

    public void set(String key, Object value, long time, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, time, timeUnit);
    }

    /**
     * 获取指定 key 的值
     *
     * @param key
     * @return
     */
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 删除key
     *
     * @param key
     */
    public Boolean delete(String key) {
        Boolean flag = redisTemplate.delete(key);
        return flag;
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return 判断key是否存在
     */
    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }
}
