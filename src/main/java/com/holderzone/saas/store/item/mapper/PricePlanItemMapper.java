package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.holderzone.saas.store.dto.common.CountItemDTO;
import com.holderzone.saas.store.dto.item.common.PlanItemUpdateDTO;
import com.holderzone.saas.store.dto.item.common.PlanPriceExistItemDTO;
import com.holderzone.saas.store.dto.item.req.ItemQueryReqDTO;
import com.holderzone.saas.store.dto.item.req.PricePlanItemAddQueryReqDTO;
import com.holderzone.saas.store.dto.item.req.PricePlanItemPageReqDTO;
import com.holderzone.saas.store.dto.item.req.PricePlanItemSkuUpdateDTO;
import com.holderzone.saas.store.dto.item.req.price.PlanPriceAllItemReqDTO;
import com.holderzone.saas.store.dto.item.req.price.PlanPriceAllItemSkuReqDTO;
import com.holderzone.saas.store.dto.item.req.price.PlanPriceAvailableReqDTO;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.price.ItemSkuAndPlanPriceDTO;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceAllItemRespDTO;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceAllItemSkuRespDTO;
import com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditDTO;
import com.holderzone.saas.store.item.dto.SyncItemDTO;
import com.holderzone.saas.store.item.entity.domain.PricePlanItemDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PricePlanItemMapper extends BaseMapper<PricePlanItemDO> {

    /**
     * 分页查询方案关联商品
     *
     * @param reqDTO PricePlanItemPageReqDTO
     * @param page   Page
     * @return List<PricePlanItemPageRespDTO>
     */
    List<PricePlanItemPageRespDTO> itemList(@Param("req") PricePlanItemPageReqDTO reqDTO,
                                            Page<PricePlanItemPageRespDTO> page);

    /**
     * 更新方案关联商品信息
     *
     * @param reqDTOList List<PricePlanItemUpdateReqDTO>
     * @return 结果
     */
    Boolean updateItems(@Param("reqList") List<PricePlanItemSkuUpdateDTO> reqDTOList);

    /**
     * 查询方案关联商品信息（推送商品用）
     *
     * @param planGuid 方案guid
     * @return List<SyncItemDTO>
     */
    List<SyncItemDTO> getSyncItems(@Param("planGuid") String planGuid);

    /**
     * 删除方案关联菜品
     *
     * @param planGuid 方案guid
     */
    void deleteItems(@Param("planGuid") String planGuid);

    /**
     * 逻辑删除方案关联菜品
     *
     * @param planGuid 方案guid
     */
    void logicDeleteItems(@Param("planGuid") String planGuid);

    /**
     * 查询方案关联菜品数
     *
     * @param planGuid 方案guid
     * @return 数量
     */
    Integer getCount(@Param("planGuid") String planGuid);

    /**
     * 查询方案可导入菜品
     *
     * @param request PricePlanItemAddQueryReqDTO
     * @param page    Page<ItemTemplateSubItemRespDTO>
     * @return List<PricePlanItemAddQueryRespDTO>
     */
    List<PricePlanItemAddQueryRespDTO> getBrandSkuItemList(@Param("dto") PricePlanItemAddQueryReqDTO request,
                                                           Page<PricePlanItemAddQueryRespDTO> page);

    /**
     * 查询方案关联菜品sku集合
     *
     * @param planGuid 方案guid
     * @return skuGuid集合
     */
    List<String> getPlanSkuGuidList(@Param("planGuid") String planGuid);

    /**
     * 查询方案关联菜品guid集合
     *
     * @param planGuid 方案guid
     * @return itemGuid集合
     */
    List<String> getPlanItemGuidList(@Param("planGuid") String planGuid);

    /**
     * 查询价格方案规格商品价格
     *
     * @param itemGuid 商品guid
     * @param skuGuid  规格guid
     * @return 价格
     */
    SyncItemDTO getSyncItem(@Param("itemGuid") String itemGuid,
                            @Param("skuGuid") String skuGuid);

    /**
     * 通过itemGuid查询对应的方案售价等信息
     *
     * @param itemGuid 商品guid
     * @return List<PricePlanSkuRespDTO>
     */
    List<PricePlanSkuRespDTO> getSkuList(@Param("itemGuid") String itemGuid,
                                         @Param("planGuid") String planGuid);

    /**
     * 通过itemGuid查询对应的方案售价等信息
     * 为避免影响到以前的业务，新写一个查询
     * 菜谱批量编辑所用
     *
     * @param itemGuid 商品guid
     * @return 菜谱商品信息
     */
    List<PricePlanSkuRespDTO> getSkuListByPlanItemGuid(@Param("itemGuid") String itemGuid,
                                                       @Param("planGuid") String planGuid);

    List<PricePlanSkuRespDTO> getSkuListAll(@Param("itemGuidList") List<String> itemGuidList,
                                            @Param("planGuidList") List<String> planGuidList);

    /**
     * 通过itemGuid集合批量逻辑删除
     *
     * @param itemGuidList itemGuid集合
     */
    Boolean logicDeleteByItem(@Param("list") List<String> itemGuidList);

    List<PricePlanBySkuRespDTO> listPlanBySku(@Param("skuGuid") String skuGuid);

    List<PricePlanBySkuRespDTO> listPlanBySkuList(@Param("skuList") List<String> skuGuidList);

    List<PricePlanBySkuRespDTO> listPlanByItem(@Param("itemGuid") String itemGuid);

    List<PricePlanSkuRespDTO> listPlanByStoreGuids(@Param("brandGuid") String brandGuid, @Param("storeGuids") List<String> storeGuids);

    /**
     * 更新方案关联商品信息
     *
     * @param reqDTOList List<PlanItemUpdateDTO>
     * @return 结果
     */
    Boolean updateItemList(@Param("reqList") List<PlanItemUpdateDTO> reqDTOList);

    /**
     * 分页查询方案商品
     *
     * @param itemQueryReqDTO ItemQueryReqDTO
     * @param page            Page<ItemWebRespDTO>
     * @return List<ItemWebRespDTO>
     */
    List<ItemWebRespDTO> queryPlanItemsByPlan(@Param("dto") ItemQueryReqDTO itemQueryReqDTO, Page<ItemWebRespDTO> page);

    int queryPlanItemsByPlanCount(@Param("dto") ItemQueryReqDTO itemQueryReqDTO);

    List<ItemWebRespDTO> queryPlanItemsByPlanAll(@Param("dto") ItemQueryReqDTO itemQueryReqDTO);

    /**
     * 功能描述：查询所有为已启用、即将启用、暂不启用菜谱的所有商品（去重
     *
     * @param req 请求参数
     * @return java.util.List<com.holderzone.saas.store.dto.item.resp.price.PlanPriceAllItemRespDTO>
     * @date 2021/9/30
     */
    List<PlanPriceAllItemRespDTO> listAllPlanPriceItem(@Param("dto") PlanPriceAllItemReqDTO req);

    /**
     * 功能描述：查询所有为已启用、即将启用、暂不启用菜谱的所有分类(去重)
     */
    List<String> listAllPlanPriceItemTypeGuid(@Param("brandGuid") String brandGuid);


    /**
     * 功能描述：查询所有为已启用、即将启用、暂不启用菜谱的所有商品规格（去重
     *
     * @param req 请求参数
     * @return java.util.List<com.holderzone.saas.store.dto.item.resp.price.PlanPriceAllItemSkuRespDTO>
     * @date 2021/9/30
     */
    List<PlanPriceAllItemSkuRespDTO> listAllPlanPriceItemSku(@Param("dto") PlanPriceAllItemSkuReqDTO req,
                                                             Page<PlanPriceAllItemSkuRespDTO> page);

    /**
     * 功能描述：根据菜品guid查询菜品规格和已经应用得菜品规格总数
     *
     * @param req 请求参数
     * @return int 总数
     * @date 2021/9/30
     */
    int listAllPlanPriceItemSkuCount(@Param("dto") PlanPriceAllItemSkuReqDTO req);

    /**
     * 功能描述：根据菜品guid查询菜品规格和已经应用得菜品
     *
     * @param itemGuid 商品guid
     * @return com.holderzone.saas.store.dto.item.resp.price.ItemSkuAndPlanPriceDTO
     * @date 2021/9/30
     */
    ItemSkuAndPlanPriceDTO listItemSkuAndPlanPrice(@Param("itemGuid") String itemGuid);


    /**
     * 功能描述：查询分类名称所存在的菜谱
     */
    List<PlanPriceEditDTO> listItemTypeAndPlanPrice(@Param("typeName") String typeName);


    /**
     * 功能描述：根据菜品guid查询菜品规格和已经应用得菜品总数
     *
     * @param req 请求参数
     * @return int 总数
     * @date 2021/9/30
     */
    int listAllPlanPriceItemCount(PlanPriceAllItemReqDTO req);

    /**
     * 功能描述：
     *
     * @param reqList 商品guid列表
     * @return java.util.List<com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditDTO>
     * @date 2021/10/8
     */
    List<PlanPriceEditDTO> listPlanPriceEditByItems(@Param("reqList") List<String> reqList);

    /**
     * 功能描述：查询方案已存在的商品信息
     *
     * @param reqList 方案列表信息
     * @return java.util.List<com.holderzone.saas.store.dto.item.common.PlanPriceExistItemDTO>
     * @date 2021/10/11
     */
    List<PlanPriceExistItemDTO> listPlanPriceExistItem(@Param("reqList") List<String> reqList);

    /**
     * 功能描述：查询可用的价格方案
     *
     * @param req 请求参数
     * @return java.util.List<com.holderzone.saas.store.dto.item.resp.price.PlanPriceEditDTO>
     * @date 2021/10/11
     */
    List<PlanPriceEditDTO> listAvailablePlanPrice(PlanPriceAvailableReqDTO req);

    /**
     * 功能描述：查询方案的菜品分类
     *
     * @param planGuid 方案guid
     * @return java.util.List<com.holderzone.saas.store.dto.item.resp.ItemTypeRespDTO>
     * @date 2021/10/15
     */
    List<ItemTypeRespDTO> listAvailablePlanItemType(@Param("planGuid") String planGuid);

    /**
     * 逻辑删除方案商品
     * sql里没写if判断，如果要是入参为空就应当报错，不然就是删除所有规格的大bug了
     *
     * @param guidList 规格guid
     */
    void deleteBySku(@Param("guidList") List<String> guidList);

    List<ItemWebRespDTO> queryPlanItemsByPlanAllPage(@Param("dto") ItemQueryReqDTO itemQueryReqDTO, Page<ItemWebRespDTO> page);


    /**
     * 功能描述：统计使用门店数量
     *
     * @param itemGuidList 商品guid集合
     * @return java.util.List<com.holderzone.saas.store.dto.item.common.CountItemDTO>
     * @date 2021/10/15
     */
    List<CountItemDTO> countUseStoreNum(@Param("itemGuidList") List<String> itemGuidList);
}
