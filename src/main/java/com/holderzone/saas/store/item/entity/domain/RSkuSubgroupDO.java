package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 规格与分组关联表（规格为套餐分组中的子菜规格）
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsi_r_sku_subgroup")
public class RSkuSubgroupDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @TableLogic
    private Integer isDelete;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 分组GUID
     */
    private String subgroupGuid;

    /**
     * 规格GUID
     */
    private String skuGuid;

    /**
     * 冗余的商品GUID
     */
    private String itemGuid;

    /**
     * 每份子菜规格所含数量
     */
    private BigDecimal itemNum;

    /**
     * 该规格在分组内的排序
     */
    private Integer sort;

    /**
     * 商品加价
     */
    private BigDecimal addPrice;

    /**
     * 是否默认勾选，1：是，0,否
     */
    private Integer isDefault;

    /**
     * 是否可重复选择，0:否,1:是
     */
    private Integer isRepeat;


}
