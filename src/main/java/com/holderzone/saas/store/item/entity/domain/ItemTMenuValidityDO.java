package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品模拟-菜单-执行有效期
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsi_itme_t_menu_validity")
public class ItemTMenuValidityDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 业务主键
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 外键 商品菜单guid
     */
    private String itemMenuGuid;

    /**
     * 执行星期集合  周一到周末 0-6 用,分割
     */
    private String weeks;

    /**
     * 星期有效时间段 存时间段集合
     */
    private String times;

    /**
     * 是否全时段 1:否 2：是
     */
    private Integer isItFullTime;

    /**
     *时间周期模式  1：按时段  2：按星期  3：周期+时段  默认 1
     */
    private Integer  periodicMode;

    /**
     * 星期数量
     */
    private  Integer weeksQuantum;

    /**
     * 时间段数量
     */
    private Integer  timesQuantum;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否逻辑删除 0：否  1：是
     */
    @TableLogic
    private Integer isDelete;


}
