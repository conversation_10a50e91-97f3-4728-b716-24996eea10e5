package com.holderzone.saas.store.item.util;

import com.holderzone.framework.util.Page;

/**
 * 分页工具
 */
public class PageUtils {

    /**
     * 将mybatis-plus的分页类转换为掌控者自建分页类
     * @param page mybatis-plus Page
     * @return holder Page
     */
    public static <T> Page<T> convert(com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> page) {
        Page<T> holderPage = new Page<>();
        holderPage.setCurrentPage(page.getCurrent());
        holderPage.setPageSize(page.getSize());
        holderPage.setTotalCount(page.getTotal());
        holderPage.setData(page.getRecords());
        return holderPage;
    }
}
