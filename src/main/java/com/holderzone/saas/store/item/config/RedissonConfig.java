package com.holderzone.saas.store.item.config;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

import java.time.Duration;

@Configuration
public class RedissonConfig {
    @Autowired
    private RedissonClient redissonSingleClient;

    @Value("${redisson.address}")
    private String host;

    @Value("${redisson.database}")
    private Integer database;

    @Value("${redisson.password}")
    private String password;

    private final static int MIN_IDLE = 50;

    private final static int MAX_IDLE = 150;

    private final static int MAX_ACTIVE = 500;

    private final static int MAX_WAIT = 200;

    private final static int TIMEOUT = 5000;

    private final static int SHUTDOWN_TIMEOUT = 10000;

    @Bean
    public RedisMessageListenerContainer listenerContainer() {
        RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration();
        host = host.replace("redis://", "").replace("rediss://", "");
        String[] split = host.split(":");
        configuration.setHostName(split[0]);
        RedisPassword redisPassword = RedisPassword.of(password);
        configuration.setPassword(redisPassword);
        configuration.setDatabase(0);
        configuration.setPort(Integer.parseInt(split[1]));

        //连接池配置
        GenericObjectPoolConfig genericObjectPoolConfig =
                new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(MAX_IDLE);
        genericObjectPoolConfig.setMinIdle(MIN_IDLE);
        genericObjectPoolConfig.setMaxTotal(MAX_ACTIVE);
        genericObjectPoolConfig.setMaxWaitMillis(MAX_WAIT);

        //redis客户端配置
        LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder
                builder =  LettucePoolingClientConfiguration.builder().
                commandTimeout(Duration.ofMillis(TIMEOUT));

        builder.shutdownTimeout(Duration.ofMillis(SHUTDOWN_TIMEOUT));
        builder.poolConfig(genericObjectPoolConfig);
        LettuceClientConfiguration lettuceClientConfiguration = builder.build();

        //根据配置和客户端配置创建连接
        LettuceConnectionFactory lettuceConnectionFactory = new
                LettuceConnectionFactory(configuration,lettuceClientConfiguration);
        lettuceConnectionFactory.afterPropertiesSet();
        RedisMessageListenerContainer listenerContainer = new RedisMessageListenerContainer();
        listenerContainer.setConnectionFactory(lettuceConnectionFactory);
        return listenerContainer;
    }
}