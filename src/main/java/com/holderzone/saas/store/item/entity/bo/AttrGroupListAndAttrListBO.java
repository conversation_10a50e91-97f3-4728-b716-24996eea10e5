package com.holderzone.saas.store.item.entity.bo;

import com.holderzone.saas.store.item.entity.domain.RAttrItemAttrGroupDO;
import com.holderzone.saas.store.item.entity.domain.RItemAttrGroupDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AttrGroupListAndAttrListBO
 * @date 2019/01/31 上午11:47
 * @description //TODO
 * @program holder-saas-store-item
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AttrGroupListAndAttrListBO implements Serializable {
    /**
     * 商品与属性组关联实体集合
     */
    private List<RItemAttrGroupDO> itemAttrGroupDOList;
    /**
     * 属性与商品和属性组关联实体的关联实体
     */
    private List<RAttrItemAttrGroupDO> attrItemAttrGroupDOList;

    /**
     * 所属的商品GUID
     */
    private String itemGuid;

    /**
     * 是否有属性组
     *
     * @return
     */
    public boolean hasAttrGroup() {
        return !CollectionUtils.isEmpty(itemAttrGroupDOList);
    }
}
