package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.saas.store.dto.item.req.ItemTemplateSearchReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemTemplateRespDTO;
import com.holderzone.saas.store.item.entity.domain.ItemTemplateDO;
import com.holderzone.saas.store.item.helper.PageAdapter;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 商品模板 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
@Component
public interface ItemTemplateMapper extends BaseMapper<ItemTemplateDO> {


    /**
     * 条件查询门店销售模板
     * @param page
     * @param requset
     * @return
     */
    IPage<ItemTemplateRespDTO> getStoreItemTemplates(PageAdapter page, @Param("dto") ItemTemplateSearchReqDTO requset);

    /**
     *获取当前用户使用的模板
     * @param storeGuid
     * @return
     */
    ItemTemplateDO getStoreCurrentUsedTemplateName(@Param("guid") String storeGuid);

    /**
     * 根据类型获取门店guid
     * @param type 0：模板 1：菜单
     * @param guid
     * @return
     */
    List<String> getStoreGuidByCode(@Param("type") Integer type, @Param("guid") String guid);
}
