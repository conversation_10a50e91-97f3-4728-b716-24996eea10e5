package com.holderzone.saas.store.item.entity.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class JournalingItemsQuery implements Serializable {

    @ApiModelProperty(value = "商品/分类guid")
    private String guid;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;


    @ApiModelProperty(value = "品牌推送下的父guid")
    private String parentGuid;

    @ApiModelProperty(value = "来源  0:门店自建  2：品牌推送")
    private Integer itemFrom;

}
