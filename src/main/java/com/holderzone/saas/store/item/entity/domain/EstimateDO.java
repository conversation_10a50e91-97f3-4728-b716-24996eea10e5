package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品sku估清表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsi_estimate")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EstimateDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 商品guid
     */
    private String itemGuid;

    /**
     * 商品sku guid
     */
    private String skuGuid;

    /**
     * 是否禁售 1:否 2:是
     */
    private Integer isSoldOut;

    /**
     * 当前时间节点是否限量  1：否  2：是
     */
    private Integer isTheLimit;


    /**
     * 下个时间节点是否限量  1：否  2：是
     */
    private Integer isTheLimitReset;

    /**
     * 是否仅下个时间节点执行一次 0：否 1：是
     */
    private Integer uniqueResetFlag;


    /**
     * 限量数量
     */
    private BigDecimal limitQuantity;

    /**
     * 当前剩余数量
     */
    private BigDecimal residueQuantity;

    /**
     * 提醒阈值
     */
    private BigDecimal reminderThreshold;

    /**
     * 是否次日置满  1：否  2：是
     */
    private Integer isItReset;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否逻辑删除  0：否 1：是
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 数据当前版本号 乐观锁实现
     */
    @Version
    private Integer version;

    /**
     * 是否长期估清（未勾选次日自动移除列表恢复售卖）
     * 1：否  2：是
     */
    private Integer isForeverEstimate;
}
