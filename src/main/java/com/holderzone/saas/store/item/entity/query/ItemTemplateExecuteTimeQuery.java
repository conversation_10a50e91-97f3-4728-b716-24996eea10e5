package com.holderzone.saas.store.item.entity.query;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemTemplateExecuteTimeQuery
 * @date 2019/05/31 17:54
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Data
public class ItemTemplateExecuteTimeQuery {

    /**
     * 菜单guid
     */
    private String guid;

    /**
     * 与当前日期的差值 1:之前1天 0：当天 1：后一天
     */
    private Integer difference;

    /**
     * mysql 当前时间
     */
    private LocalDateTime now;
    /**
     * 星期
     */
    private String weeks;
    /**
     * 时间段json串
     */
    private String times;
    /**
     * 执行模式 1：时段 2：星期  3：混合（星期+时间段）
     */
    private Integer periodicMode;

}
