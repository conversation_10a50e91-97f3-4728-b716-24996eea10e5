package com.holderzone.saas.store.item.service.impl;

import com.holderzone.saas.store.item.dto.SyncPricePlanMessageDTO;
import com.holderzone.saas.store.item.service.IRedissonCacheService;
import org.redisson.api.MapOptions;
import org.redisson.api.RMap;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class IRedissonCacheServiceImpl implements IRedissonCacheService {
    private static final String PRICE_PLAN_CACHE_NAME = "price-plan-cache";

    private static final String SUFFIX = "_value";

    @Autowired
    private RedissonClient redissonSingleClient;

    /**
     * 设置缓存
     *
     * @param key        key
     * @param messageDTO messageDTO
     * @param time       time
     */
    @Override
    public void setCacheMap(String key, SyncPricePlanMessageDTO messageDTO, long time) {
        RMap<Object, Object> mapCache = redissonSingleClient.getMap(key);
        mapCache.put(PRICE_PLAN_CACHE_NAME, messageDTO);
        mapCache.expire(time, TimeUnit.SECONDS);

    }

    @Override
    public Object getCacheMap(String key) {
        return redissonSingleClient.getMap(key).get(PRICE_PLAN_CACHE_NAME);
    }

    /**
     * 删除缓存MAP
     *
     * @param key key
     * @return Object
     */
    @Override
    public Object removeCacheMap(String key) {
        RMapCache<Object, Object> mapCache = redissonSingleClient.getMapCache(key);
        return mapCache.delete();
    }

    @Override
    public <V> RMapCache<String, V> getSyncPricePlanItemDTORMapCache() {
        MapOptions<String, V> mapOptions = MapOptions.defaults();
        return redissonSingleClient.getMapCache(PRICE_PLAN_CACHE_NAME, new JsonJacksonCodec(), mapOptions);
    }

    @Override
    public <V> void setCache(String key, V value, long expire, TimeUnit timeUnit) {
        RMapCache<String, Object> mapCache = getSyncPricePlanItemDTORMapCache();
        //key += ThreadLocalRandom.current().nextInt(999999);
        mapCache.put(key, value, expire, timeUnit);
    }

    @Override
    public <V> V getCache(String key) {
        RMapCache<String, Object> syncPricePlanItemDTORMapCache = getSyncPricePlanItemDTORMapCache();
        return (V) syncPricePlanItemDTORMapCache.get(key);
    }

    @Override
    public Object removeCache(String key) {
        return redissonSingleClient.getMapCache(PRICE_PLAN_CACHE_NAME).remove(key);
    }

    @Override
    public RedissonClient getRedissonClient() {
        return redissonSingleClient;
    }
}
