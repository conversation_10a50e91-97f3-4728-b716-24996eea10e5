package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品模板
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsi_item_template")
public class ItemTemplateDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 自增长
     */
    private Long id;

    /**
     * 业务主键
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 外键 门店guid
     */
    private String storeGuid;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 开始有效期
     */
    private LocalDateTime effectiveStartTime;

    /**
     * 结束有效期
     */
    private LocalDateTime effectiveEndTime;

    /**
     * 是否激活 1：激活 2：冻结
     */
    private Integer isItActivated;

    /**
     * 周期模式  1：按时段  2：按星期
     */
    private Integer periodicMode;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否逻辑删除 0：否  1：是
     */
    @TableLogic
    private Integer isDelete;


}
