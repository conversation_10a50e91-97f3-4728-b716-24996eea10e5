package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 价格方案门店关联表
 * @date 2020/10/29 12:06
 */
@Data
@Accessors(chain = true)
@TableName("hsi_price_plan_store")
public class PricePlanStoreDO {

    private Long id;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    private Integer isDelete;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 品牌GUID
     */
    private String brandGuid;

    /**
     * 方案GUID
     */
    private String planGuid;

    /**
     * 关联门店GUID
     */
    private String storeGuid;

}
