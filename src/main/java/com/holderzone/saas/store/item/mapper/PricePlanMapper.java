package com.holderzone.saas.store.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.holderzone.saas.store.dto.item.req.PricePlanPageReqDTO;
import com.holderzone.saas.store.dto.item.req.PricePlanReqDTO;
import com.holderzone.saas.store.dto.item.resp.PricePlanRespDTO;
import com.holderzone.saas.store.item.entity.domain.PricePlanDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Repository
public interface PricePlanMapper extends BaseMapper<PricePlanDO> {

    /**
     * 检查方案重名
     *
     * @param reqDTO PricePlanReqDTO
     * @return 是否
     */
    boolean existSameName(@Param("req") PricePlanReqDTO reqDTO);

    /**
     * 按品牌查询方案列表
     *
     * @param reqDTO PricePlanPageReqDTO
     * @return List<PricePlanRespDTO>
     */
    List<PricePlanRespDTO> planStateList(@Param("req") PricePlanPageReqDTO reqDTO);

    /**
     * 分页查询方案列表
     *
     * @param reqDTO PricePlanPageReqDTO
     * @param page   Page<PricePlanRespDTO>
     * @return List<PricePlanRespDTO>
     */
    List<PricePlanRespDTO> planList(@Param("req") PricePlanPageReqDTO reqDTO,
                                    Page<PricePlanRespDTO> page);

    /**
     * 更新方案包含商品数（增加）
     *
     * @param planGuid 方案guid
     * @param itemNum  商品数
     * @param type     1增加 2减少 3直接更新
     */
    void updateItemNum(@Param("planGuid") String planGuid,
                       @Param("itemNum") Integer itemNum,
                       @Param("type") Integer type);

    /**
     * 批量更新方案包含商品数（增加）
     *
     * @param planGuidList 方案guid
     * @param itemNum      商品数
     * @param type         1增加 2减少 3直接更新
     */
    void batchUpdateItemNum(@Param("planGuidList") Set<String> planGuidList,
                            @Param("itemNum") Integer itemNum,
                            @Param("type") Integer type);

    /**
     * 检查方案是否已存在全时段
     *
     * @param reqDTO PricePlanReqDTO
     * @return 是否
     */
    Boolean existSellTimeType(@Param("req") PricePlanReqDTO reqDTO);

    /**
     * 更新方案包含门店数（增加）
     *
     * @param planGuid 方案guid
     * @param storeNum 门店数
     * @param type     1增加 2减少 3直接更新
     */
    void updateStoreNum(@Param("planGuid") String planGuid,
                        @Param("storeNum") Integer storeNum,
                        @Param("type") Integer type);

    /**
     * 通过sku查询正在售卖售完下架的门店GUID
     *
     * @param skuGuids sku
     * @return 门店GUID集合
     */
    List<String> findIsSoldOutStores(@Param("skuGuids") List<String> skuGuids,
                                     @Param("nowDateTime") LocalDateTime nowDateTime);

    List<PricePlanDO> listByPlanGuidList(@Param("planGuidList") List<String> planGuidList);

    void updateStatus(@Param("guid")String guid, @Param("status")int status);

    /**
     * 查询默认菜谱
     */
    PricePlanDO queryDefaultPlan(@Param("storeGuid") String storeGuid, @Param("brandGuid") String brandGuid);

}
