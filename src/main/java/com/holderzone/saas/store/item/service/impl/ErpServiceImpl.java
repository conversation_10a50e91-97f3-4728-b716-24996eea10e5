package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.PageDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.DoubleDataPageDTO;
import com.holderzone.saas.store.dto.item.req.SingleDataPageDTO;
import com.holderzone.saas.store.dto.item.resp.ErpItemDTO;
import com.holderzone.saas.store.dto.item.resp.ErpTypeDTO;
import com.holderzone.saas.store.dto.item.resp.TypeWebRespDTO;
import com.holderzone.saas.store.item.entity.MPPage;
import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import com.holderzone.saas.store.item.service.ErpService;
import com.holderzone.saas.store.item.service.IItemService;
import com.holderzone.saas.store.item.service.ISkuService;
import com.holderzone.saas.store.item.service.ITypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ErpServiceImpl implements ErpService {

    private final ITypeService typeService;

    private final IItemService itemService;

    private final ISkuService skuService;

    @Autowired
    public ErpServiceImpl(ITypeService typeService, IItemService itemService, ISkuService skuService) {
        this.typeService = typeService;
        this.itemService = itemService;
        this.skuService = skuService;
    }

    @Override
    public List<ErpTypeDTO> listType(String storeGuid) {
        ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
        itemSingleDTO.setFrom(0);
        itemSingleDTO.setData(storeGuid);
        List<TypeWebRespDTO> typeWebRespDTOS = typeService.queryType(itemSingleDTO);
        return typeWebRespDTOS.stream()
                .map(typeWebRespDTO -> new ErpTypeDTO(
                        typeWebRespDTO.getTypeGuid(), typeWebRespDTO.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public Page<ErpItemDTO> pageItem(SingleDataPageDTO singleDataPageDTO) {
        Assert.hasText(singleDataPageDTO.getData(), "门店Guid不得为空");
        return pageItemByWrapper(singleDataPageDTO, () -> new LambdaQueryWrapper<ItemDO>()
                .ne(ItemDO::getItemType, 1)
                .eq(ItemDO::getStoreGuid, singleDataPageDTO.getData()));
    }

    @Override
    public Page<ErpItemDTO> pageItemOfType(SingleDataPageDTO singleDataPageDTO) {
        Assert.hasText(singleDataPageDTO.getData(), "分类Guid不得为空");
        return pageItemByWrapper(singleDataPageDTO, () -> new LambdaQueryWrapper<ItemDO>()
                .ne(ItemDO::getItemType, 1)
                .eq(ItemDO::getTypeGuid, singleDataPageDTO.getData()));
    }

    @Override
    public Page<ErpItemDTO> pageItemByName(DoubleDataPageDTO doubleDataPageDTO) {
        Assert.hasText(doubleDataPageDTO.getData1(), "门店Guid不得为空");
        LambdaQueryWrapper<ItemDO> lambdaQueryWrapper = new LambdaQueryWrapper<ItemDO>().ne(ItemDO::getItemType, 1)
                .eq(ItemDO::getStoreGuid, doubleDataPageDTO.getData1());
        if (Optional.ofNullable(doubleDataPageDTO.getData2()).isPresent()) {
            lambdaQueryWrapper.like(ItemDO::getName, doubleDataPageDTO.getData2());
        }
        return pageItemByWrapper(doubleDataPageDTO, () -> lambdaQueryWrapper);
    }

    private Page<ErpItemDTO> pageItemByWrapper(PageDTO pageDTO, Supplier<LambdaQueryWrapper<ItemDO>> supplier) {
        List<ItemDO> items = itemService.list(supplier.get());
        Page<ErpItemDTO> pageResult = new Page<>(pageDTO.getCurrentPage(), pageDTO.getPageSize());
        if (CollectionUtils.isEmpty(items)) {
            return pageResult;
        }
        List<String> itemGuidList = items.stream().map(ItemDO::getGuid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemGuidList)) {
            return pageResult;
        }
        int totalCount = skuService.count(new LambdaQueryWrapper<SkuDO>().in(SkuDO::getItemGuid, itemGuidList));
        if (totalCount == 0) {
            return pageResult;
        }
        pageResult.setTotalCount(totalCount);
        MPPage<ItemDO> itemDOIPageQuery = new MPPage<>(pageDTO.getCurrentPage(), pageDTO.getPageSize());
        IPage<ItemDO> itemDOList = itemService.page(itemDOIPageQuery, supplier.get().orderByAsc(ItemDO::getSort)
        );
        if (CollectionUtils.isEmpty(itemDOList.getRecords())) {
            return pageResult;
        }

        Map<String, ItemDO> itemMap = itemDOList.getRecords().stream()
                .collect(Collectors.toMap(ItemDO::getGuid, Function.identity()));

        List<SkuDO> skuDOList = skuService.list(
                new LambdaQueryWrapper<SkuDO>().in(SkuDO::getItemGuid, itemMap.keySet())
        );

        pageResult.setData(convertSku2ErpItem(skuDOList, itemMap));
        return pageResult;
    }

    private List<ErpItemDTO> convertItem2ErpItem(List<ItemDO> itemDOList) {
        if (CollectionUtils.isEmpty(itemDOList)) {
            return new ArrayList<>();
        }

        Map<String, ItemDO> itemMap = itemDOList.stream()
                .collect(Collectors.toMap(ItemDO::getGuid, Function.identity()));
        if (CollectionUtils.isEmpty(itemMap)) {
            return new ArrayList<>();
        }

        List<SkuDO> skuDOList = skuService.list(
                new LambdaQueryWrapper<SkuDO>().in(SkuDO::getItemGuid, itemMap.keySet())
        );
        return convertSku2ErpItem(skuDOList, itemMap);
    }

    private List<ErpItemDTO> convertSku2ErpItem(List<SkuDO> skuDOList, Map<String, ItemDO> itemMap) {
        if (CollectionUtils.isEmpty(skuDOList)) {
            return new ArrayList<>();
        }
        return skuDOList.stream()
                .map(skuDO -> {
                    ErpItemDTO erpItemDTO = new ErpItemDTO();
                    erpItemDTO.setGuid(skuDO.getItemGuid());
                    erpItemDTO.setSkuId(skuDO.getGuid());
                    ItemDO itemDO = itemMap.get(skuDO.getItemGuid());
                    if (itemDO != null) {
                        String skuName = StringUtils.hasText(skuDO.getName())
                                ? "（" + skuDO.getName() + "）"
                                : "";
                        erpItemDTO.setName(itemDO.getName() + skuName);
                        erpItemDTO.setIcon(itemDO.getPictureUrl());
                        erpItemDTO.setSort(itemDO.getSort());
                        erpItemDTO.setId(itemDO.getId());
                    } else {
                        erpItemDTO.setSort(Integer.MAX_VALUE);
                        erpItemDTO.setId(Long.MAX_VALUE);
                        log.error("规格、商品不匹配：通过itemGuid[{}]未查询到商品", skuDO.getItemGuid());
                    }
                    return erpItemDTO;
                })
                .sorted(Comparator.comparing(ErpItemDTO::getId))
                .sorted(Comparator.comparing(ErpItemDTO::getSort))
                .collect(Collectors.toList());
    }
}
