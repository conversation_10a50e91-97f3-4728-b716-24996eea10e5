package com.holderzone.saas.store.item.constant;

import lombok.AllArgsConstructor;

/**
 * 商品估清操作类型
 */
@AllArgsConstructor
public enum EstimateOpTypeEnum {

    /**
     * 创建
     */
    CREATE(true, "CREATE"),

    /**
     * 编辑
     */
    UPDATE(true, "UPDATE"),

    /**
     * 删除
     */
    REMOVE(true, "REMOVE"),

    /**
     * 批量停售
     */
    BATCH_STOP(true, "BATCH_STOP"),

    /**
     * 批量恢复
     */
    BATCH_CANCEL(true, "BATCH_CANCEL"),


    /**
     * 定时修复，扣减库存,回退库存 操作类型不记录在操作日志中
     */
    /**
     * 定时恢复
     */
    SCHEDULING(false, "SCHEDULING"),

    /**
     * 扣减库存
     */
    REDUCE_STOCK(false, "REDUCE_STOCK"),

    /**
     * 回退库存
     */
    RETURN_STOCK(false, "RETURN_STOCK");

    /**
     * 是否操作类型
     */
    private boolean isOpType;

    /**
     * 操作类型
     */
    private String opType;

    public boolean isOpType() {
        return isOpType;
    }

    public void setOpType(boolean opType) {
        isOpType = opType;
    }

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }


    public static boolean isOpLog(String opType) {
        for (EstimateOpTypeEnum value : values()) {
            if (value.getOpType().equals(opType)) {
                return value.isOpType();
            }
        }
        return false;
    }
}
