package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.item.common.ItemSkuStockDTO;
import com.holderzone.saas.store.dto.item.req.*;
import com.holderzone.saas.store.dto.item.resp.*;
import com.holderzone.saas.store.dto.item.resp.AttrGroupWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.CartCheckResult;
import com.holderzone.saas.store.dto.item.resp.ItemShoppingCartResp;
import com.holderzone.saas.store.dto.item.resp.SubgroupWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateResultRespDTO;
import com.holderzone.saas.store.item.entity.domain.EstimateDO;
import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.SkuDO;
import com.holderzone.saas.store.item.service.IEstimateService;
import com.holderzone.saas.store.item.service.IItemService;
import com.holderzone.saas.store.item.service.IMiniAppService;
import com.holderzone.saas.store.item.service.ISkuService;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class IMiniAppServiceImpl implements IMiniAppService {
    private ISkuService skuService;
    private IItemService itemService;
    private IEstimateService iEstimateService;

    @Override
    public ItemShoppingCartCheckResponse checkShoppingCardForMiniApp(ItemShoppingCartCheckRequest itemShoppingCartCheckRequest) {
        ItemShoppingCartCheckResponse itemShoppingCartCheckResponse = new ItemShoppingCartCheckResponse();

        List<SingleShoppingCartCheck> singleShoppingCartChecks = itemShoppingCartCheckRequest.getData();
        List<String> skuGuidList = singleShoppingCartChecks.stream().map(SingleShoppingCartCheck::getSkuGuid).collect(Collectors.toList());
        Map<String, SingleShoppingCartCheck> singleDOMap = new HashMap<>();
        for (SingleShoppingCartCheck single : singleShoppingCartChecks) {
            singleDOMap.put(single.getSkuGuid(), single);
        }
        List<SkuDO> skuDOS = skuService.list(new LambdaQueryWrapper<SkuDO>()
                .eq(SkuDO::getStoreGuid, itemShoppingCartCheckRequest.getStoreGuid())
                .in(SkuDO::getGuid, skuGuidList));
        List<CartCheckResult> skuDetailResult = new ArrayList<>();
        boolean ret = Boolean.TRUE;
        if (!ObjectUtils.isEmpty(skuDOS)) {
            for (SkuDO skuDO : skuDOS) {
                SingleShoppingCartCheck single = singleDOMap.get(skuDO.getGuid());
                CartCheckResult singleResult = checkSku(single, skuDO, itemShoppingCartCheckRequest.getIsTakeaway());
                if (!singleResult.isSuccess()) {
                    ret = Boolean.FALSE;
                }
                skuDetailResult.add(singleResult);
            }
        }
        if (ret) {
            return itemShoppingCartCheckResponse.setCode(10000).setMessage("校验成功").setData(skuDetailResult);
        } else {
            return itemShoppingCartCheckResponse.setCode(10001).setMessage("校验失败").setData(skuDetailResult);
        }
    }


    private CartCheckResult checkSku(SingleShoppingCartCheck single, SkuDO skuDO, boolean checkTakeaway) {
        CartCheckResult singleResult = new CartCheckResult();
        singleResult.setSkuGuid(skuDO.getGuid());
        singleResult.setSuccess(Boolean.TRUE);
        singleResult.setErrorMsg("校验成功");
        //需要检验的列表
        String[] checkFieldList = checkTakeaway ?
                new String[]{
                        "skuName",
                        "takeawayPrice",
                        "takeawayMemberPrice",
                        "virtualPrice",
                        "takeawayPackageFee"
                } : new String[]{
                "skuName",
                "salePrice",
                "memberPrice"
        };


        if (!StringUtils.isEmpty(single.getSkuName()) && !single.getSkuName().equals(skuDO.getName())) {
            log.info("参数中的skuName:{},数据库中的skuName:{}", single.getSkuName(), skuDO.getName());
            singleResult.setSuccess(Boolean.FALSE);
            singleResult.setErrorMsg("sku名称不同:参数中的skuName=" + single.getSkuName() + "数据库中的skuName" + skuDO.getName());
        }
        for (String field : checkFieldList) {
            try {
                Field f = SingleShoppingCartCheck.class.getField(field);
                Field d = SkuDO.class.getField(field);
                String name = f.getAnnotation(ApiModelProperty.class).value();
                f.setAccessible(true);
                d.setAccessible(true);
                Object src = f.get(single);
                Object destination = d.get(skuDO);
                if (src != null && !src.equals(destination)) {
                    log.info("{},{},{}", f.getName(), src, destination);
                    singleResult.setSuccess(Boolean.FALSE);
                    singleResult.setErrorMsg(name + "不同:参数中的=" + src + "数据库中的=" + destination);
                }
            } catch (NoSuchFieldException | IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        //库存例外
        if (single.getStock() != null && skuDO.getStock() != null && single.getStock().intValue() >= skuDO.getStock().intValue()) {
            log.info("参数中的stock:{},数据库中的stock:{}", single.getStock(), skuDO.getStock());
            singleResult.setSuccess(Boolean.FALSE);
            singleResult.setErrorMsg("超过最大可扣库存数量:数据库中的stock=" + single.getStock() + "数据库中的stock=" + skuDO.getStock());
        }
        return singleResult;
    }

    @Override
    public ItemShoppingCartResp queryShoppingCardForMiniApp(ItemShoppingCardRequest itemShoppingCardRequest) {
        ItemShoppingCartResp itemShoppingCartResp = new ItemShoppingCartResp();
        String storeGuid = itemShoppingCardRequest.getStoreGuid();
        List<String> skuGuidList = itemShoppingCardRequest.getSkuGuids();
        List<SkuDO> skuDOS = skuService.list(new LambdaQueryWrapper<SkuDO>()
                .eq(SkuDO::getStoreGuid, storeGuid)
                .in(SkuDO::getGuid, skuGuidList));
        Map<String, List<SkuDO>> itemSkuMaps = new HashMap<>();

        for (SkuDO skuDO : skuDOS) {
            //多规格商品
            if (!ObjectUtils.isEmpty(itemSkuMaps.get(skuDO.getItemGuid()))) {
                List<SkuDO> skuS = itemSkuMaps.get(skuDO.getItemGuid());
                skuS.add(skuDO);
                itemSkuMaps.put(skuDO.getItemGuid(), skuS);
                //普通商品
            } else {
                List<SkuDO> skuS = new ArrayList<>();
                skuS.add(skuDO);
                itemSkuMaps.put(skuDO.getItemGuid(), skuS);
            }
        }
        Map<String, EstimateDO> estimateMap = new HashMap<>();
        List<EstimateDO> list = iEstimateService.list(new LambdaQueryWrapper<EstimateDO>()
                .eq(EstimateDO::getStoreGuid, storeGuid)
                .eq(EstimateDO::getIsDelete, 0)
                .in(EstimateDO::getSkuGuid, skuGuidList));
        list.forEach(estimateDO -> {
            estimateMap.put(estimateDO.getSkuGuid(), estimateDO);
        });
        List<String> itemGuidList = skuDOS.stream().map(SkuDO::getItemGuid).collect(Collectors.toList());
        List<ItemDO> itemDOS = itemService.list(new LambdaQueryWrapper<ItemDO>().in(ItemDO::getGuid, itemGuidList));
        List<SingleShoppingCart> cartList = new ArrayList<>();
        for (ItemDO itemDO : itemDOS) {
            List<SkuDO> skuDOList = itemSkuMaps.get(itemDO.getGuid());
            if (ObjectUtils.isEmpty(skuDOList)) {
                continue;
            }
            for (SkuDO skuDO : skuDOList) {
                SingleShoppingCart singleShoppingCart = new SingleShoppingCart();
                BeanUtils.copyProperties(itemDO, singleShoppingCart);
                BeanUtils.copyProperties(skuDO, singleShoppingCart);

                singleShoppingCart.setItemGuid(itemDO.getGuid());
                singleShoppingCart.setName(itemDO.getName());
                singleShoppingCart.setSkuName(skuDO.getName());
                singleShoppingCart.setSkuGuid(skuDO.getGuid());
                if (estimateMap.containsKey(skuDO.getGuid())) {
                    EstimateDO e = estimateMap.get(skuDO.getGuid());
                    singleShoppingCart.setStock(e.getResidueQuantity());
                }
                List<AttrGroupWebRespDTO> attrGroupWebRespDTOList = new ArrayList<>();
                if (itemDO.getHasAttr() != 0) {
                    // 获取指定商品下的属性组
                    attrGroupWebRespDTOList = itemService.selectItemAttrList(itemDO.getGuid());
                    if (!CollectionUtils.isEmpty(attrGroupWebRespDTOList)) {
                        singleShoppingCart.setAttrGroupList(attrGroupWebRespDTOList);
                    }
                }

                if (itemDO.getItemType() == 1) {
                    List<SubgroupWebRespDTO> subgroupWebRespDTOList = itemService.selectSubgroupList(itemDO.getGuid());
                    singleShoppingCart.setSubgroupList(subgroupWebRespDTOList);
                }
                cartList.add(singleShoppingCart);
            }
        }
        itemShoppingCartResp.setData(cartList);
        return itemShoppingCartResp;
    }

    @Override
    public ItemStockResp descStock(ItemShoppingCartCheckRequest itemShoppingCartCheckRequest) {
        ItemShoppingCartCheckResponse ret = checkShoppingCardForMiniApp(itemShoppingCartCheckRequest);
        if (ret.getCode() == 10000) {
            List<ItemSkuStockDTO> itemSkuStockDTOList = itemShoppingCartCheckRequest.getData().stream().map(s -> {
                ItemSkuStockDTO skuStockDTO = new ItemSkuStockDTO();
                BeanUtils.copyProperties(s, skuStockDTO);
                return skuStockDTO;
            }).collect(Collectors.toList());
            log.info("itemSkuStockDTOList={}", JacksonUtils.writeValueAsString(itemSkuStockDTOList));
            EstimateResultRespDTO descStockRet = iEstimateService.descStock(itemSkuStockDTOList);
            ItemStockResp itemStockResp = new ItemStockResp();
            if (descStockRet.getSuccess()) {
                itemStockResp.setMessage("库存扣减成功");
                itemStockResp.setCode("10000");
            } else {
                itemStockResp.setMessage("库存扣减失败," + descStockRet.getErrorMsg());
                itemStockResp.setCode("10001");
            }
            return itemStockResp;
        }
        ItemStockResp itemStockResp = new ItemStockResp();
        itemStockResp.setCode("10001");
        itemStockResp.setMessage("扣库存校验失败");
        return itemStockResp;
    }
}
