package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.item.entity.domain.PricePlanMessageDO;
import com.holderzone.saas.store.item.mapper.PricePlanMessageMapper;
import com.holderzone.saas.store.item.service.IPricePlanMessageService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@Slf4j
public class PricePlanMessageServiceImpl extends ServiceImpl<PricePlanMessageMapper, PricePlanMessageDO> implements IPricePlanMessageService {

}
