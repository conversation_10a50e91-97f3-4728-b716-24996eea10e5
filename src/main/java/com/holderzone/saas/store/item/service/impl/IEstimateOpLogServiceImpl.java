package com.holderzone.saas.store.item.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.item.resp.SkuInfoRespDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.item.constant.Constant;
import com.holderzone.saas.store.item.constant.EstimateOpTypeEnum;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.entity.domain.EstimateDO;
import com.holderzone.saas.store.item.entity.domain.EstimateOpLogDO;
import com.holderzone.saas.store.item.entity.domain.EstimateSellLogDO;
import com.holderzone.saas.store.item.mapper.EstimateOpLogMapper;
import com.holderzone.saas.store.item.service.*;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.GUIDUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @description 商品估清操作记录 服务实现类
 */
@Slf4j
@Service
public class IEstimateOpLogServiceImpl extends ServiceImpl<EstimateOpLogMapper, EstimateOpLogDO> implements IEstimateOpLogService {

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private ISkuService skuService;

    @Autowired
    private DynamicHelper dynamicHelper;

    @Autowired
    private IEstimateSellLogService estimateSellLogService;

    @Async
    @Override
    public void saveOpLog(List<EstimateOpLogDO> opLogList, String enterpriseGuid) {
        if (CollectionUtils.isEmpty(opLogList)) {
            return;
        }
        dynamicHelper.changeDatasource(enterpriseGuid);
        UserContextUtils.putErp(enterpriseGuid);

        // 查询门店信息
        Set<String> storeGuids = opLogList.stream().map(EstimateOpLogDO::getStoreGuid).collect(Collectors.toSet());
        List<StoreDTO> storeList = organizationService.queryStoreByIdList(new ArrayList<>(storeGuids));
        if (CollectionUtils.isEmpty(storeList)) {
            log.error("门店为空,当前保存日志为:{}", JacksonUtils.writeValueAsString(opLogList));
            return;
        }
        for (StoreDTO storeDTO : storeList) {
            // 计算门店营业开始时间
            LocalDate businessDay = LocalDate.now();
            if (LocalTime.now().compareTo(storeDTO.getBusinessStart()) > 0) {
                businessDay = businessDay.plusDays(1);
            }
            storeDTO.setBusinessDayStart(businessDay.atTime(storeDTO.getBusinessStart()));
        }
        Map<String, StoreDTO> storeMap = storeList.stream().collect(Collectors.toMap(StoreDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        // 设置门店信息，营业时间
        opLogList.forEach(e -> {
            StoreDTO storeDTO = storeMap.get(e.getStoreGuid());
            if (Objects.isNull(storeDTO)) {
                return;
            }
            e.setStoreName(storeDTO.getName());
            e.setBusinessStart(storeDTO.getBusinessDayStart());
        });

        // 查询并设置商品信息
        Map<String, List<EstimateOpLogDO>> skuByStoreGuidMap = opLogList.stream().collect(Collectors.groupingBy(EstimateOpLogDO::getStoreGuid));
        for (Map.Entry<String, List<EstimateOpLogDO>> entry : skuByStoreGuidMap.entrySet()) {
            List<EstimateOpLogDO> skuByStoreGuidList = entry.getValue();
            Set<String> skuGuids = skuByStoreGuidList.stream().map(EstimateOpLogDO::getSkuGuid).collect(Collectors.toSet());
            List<SkuInfoRespDTO> skuInfoRespList = skuService.listSkuInfoByRecipeMode(entry.getKey(), new ArrayList<>(skuGuids));
            if (CollectionUtils.isEmpty(skuInfoRespList)) {
                return;
            }
            Map<String, SkuInfoRespDTO> skuInfoMap = skuInfoRespList.stream()
                    .collect(Collectors.toMap(SkuInfoRespDTO::getSkuGuid, Function.identity(), (key1, key2) -> key1));
            skuByStoreGuidList.forEach(e -> {
                SkuInfoRespDTO skuInfo = skuInfoMap.get(e.getSkuGuid());
                if (Objects.isNull(skuInfo)) {
                    return;
                }
                e.setItemGuid(skuInfo.getItemGuid());
                e.setItemName(skuInfo.getItemName());
                e.setSkuName(skuInfo.getName());
            });
        }

        // 保存商品售卖状态记录
        List<EstimateSellLogDO> sellLogList = transferSellLogList(opLogList);
        estimateSellLogService.saveSellLog(sellLogList, enterpriseGuid);

        if (CollectionUtils.isEmpty(opLogList)) {
            return;
        }

        // 保存操作记录
        List<String> guids = GUIDUtils.generateGuids(GuidKeyConstant.HSI_ESTIMATE_OP_LOG, opLogList.size());
        if (CollectionUtils.isEmpty(guids)) {
            log.error("保存商品估清操作记录失败，生成guid失败");
            return;
        }
        for (int i = 0; i < opLogList.size(); i++) {
            opLogList.get(i).setGuid(guids.get(i));
        }
        saveBatch(opLogList);
    }

    /**
     * 操作日志记录转换
     */
    private List<EstimateSellLogDO> transferSellLogList(List<EstimateOpLogDO> opLogList) {
        List<EstimateSellLogDO> sellLogList = Lists.newArrayList();

        Iterator<EstimateOpLogDO> iterator = opLogList.iterator();
        while (iterator.hasNext()) {
            EstimateOpLogDO e = iterator.next();
            // 售卖状态记录
            EstimateSellLogDO sellLog = new EstimateSellLogDO();
            BeanUtils.copyProperties(e, sellLog);
            if (EstimateOpTypeEnum.CREATE.getOpType().equals(e.getOpType())
                    || EstimateOpTypeEnum.UPDATE.getOpType().equals(e.getOpType())) {
                // 新增和编辑，根据剩余数量判断当前状态
                EstimateDO estimateDO = JSONObject.parseObject(e.getParams(), EstimateDO.class);
                if (estimateDO.getResidueQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    sellLog.setSell(Constant.FALSE);
                } else {
                    sellLog.setSell(Constant.TRUE);
                }
            } else if (EstimateOpTypeEnum.REMOVE.getOpType().equals(e.getOpType())) {
                // 恢复，当前商品售卖状态应为在售
                sellLog.setSell(Constant.TRUE);
            } else if (EstimateOpTypeEnum.BATCH_STOP.getOpType().equals(e.getOpType())) {
                // 批量停售，当前商品售卖状态应为停售
                sellLog.setSell(Constant.FALSE);
            } else if (EstimateOpTypeEnum.BATCH_CANCEL.getOpType().equals(e.getOpType())) {
                // 批量恢复，当前商品售卖状态应为在售
                sellLog.setSell(Constant.TRUE);
            } else if (EstimateOpTypeEnum.SCHEDULING.getOpType().equals(e.getOpType())) {
                // 定时恢复操作，当前商品售卖状态应为在售
                sellLog.setSell(Constant.TRUE);
            } else if (EstimateOpTypeEnum.REDUCE_STOCK.getOpType().equals(e.getOpType())
                    || EstimateOpTypeEnum.RETURN_STOCK.getOpType().equals(e.getOpType())) {
                if (StringUtils.isEmpty(e.getParams())) {
                    log.error("日志记录参数有误,{}", JacksonUtils.writeValueAsString(e.getParams()));
                    continue;
                }
                // 加扣减库存，根据加扣减完成之后剩余数量判断当前状态
                EstimateDO estimateDO = JSONObject.parseObject(e.getParams(), EstimateDO.class);
                if (estimateDO.getResidueQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    sellLog.setSell(Constant.FALSE);
                } else {
                    sellLog.setSell(Constant.TRUE);
                }
            }
            sellLogList.add(sellLog);

            // 移除
            if (!EstimateOpTypeEnum.isOpLog(e.getOpType())) {
                iterator.remove();
            }
        }
        return sellLogList;
    }

}


