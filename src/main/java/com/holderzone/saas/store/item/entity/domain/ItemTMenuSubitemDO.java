package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <p>
 * 商品模板-菜单-商品
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsi_item_t_menu_subitem")
public class ItemTMenuSubitemDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 自增长
     */

    private Long id;

    /**
     * 业务主键
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 外键 商品菜单guid
     */
    private String itemMenuGuid;

    /**
     * 商品sku
     */
    private String skuGuid;

    /**
     * 模板价格
     */
    private BigDecimal price;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否逻辑删除 0：否  1：是
     */
    @TableLogic
    private Integer isDelete;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ItemTMenuSubitemDO that = (ItemTMenuSubitemDO) o;
        return Objects.equals(guid, that.guid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(guid);
    }
}
