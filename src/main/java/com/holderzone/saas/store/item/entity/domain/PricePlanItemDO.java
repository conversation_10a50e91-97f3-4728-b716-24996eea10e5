package com.holderzone.saas.store.item.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 价格方案菜品表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-28
 */
@Data
@Accessors(chain = true)
@TableName("hsi_price_plan_item")
public class PricePlanItemDO {

    private Long id;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    @TableLogic
    private Integer isDelete;

    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 品牌GUID
     */
    private String brandGuid;

    /**
     * 方案GUID
     */
    private String planGuid;

    /**
     * 菜品GUID
     */
    private String itemGuid;

    /**
     * 菜品规格GUID
     */
    private String skuGuid;

    /**
     * 方案销售价格
     */
    private BigDecimal salePrice;

    /**
     * 方案会员价格
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal memberPrice;

    /**
     * 是否套餐
     */
    private Boolean isPkgItem;

    /***
     * 方案商品名称
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String planItemName;

    /***
     * 是否售完下架 0:已上架 1：售完下架 2：立即下架（直接删除） 3：即将下架 4：已下架（售完商品的下架）
     * @see com.holderzone.saas.store.item.entity.enums.ItemStateEnum
     */
    private Integer isSoldOut;

    /***
     * 菜谱方案商品分类
     */
    private String typeGuid;

    /**
     * 商品描述
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String description;

    /**
     * 图片路径数组json,这里对应新一期加入的“列表小图”
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String pictureUrl;

    /**
     * 图片路径数组json,这里对应新一期加入的“列表大图”
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String bigPictureUrl;

    /**
     * 图片路径数组json,这里对应新一期加入的“详情大图”
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String detailBigPictureUrl;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否是最新编辑
     */
    private Boolean newUpdateFlag;

    /**
     * 英文简述
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String englishBrief;

    /**
     * 英文配料描述
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String englishIngredientsDesc;

    /**
     * 堂食核算价
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal accountingPrice;

    /**
     * 外卖核算价
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal takeawayAccountingPrice;

    /**
     * 划线价
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private BigDecimal linePrice;
}
