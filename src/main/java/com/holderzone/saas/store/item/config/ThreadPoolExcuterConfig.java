package com.holderzone.saas.store.item.config;

import com.holderzone.saas.store.item.util.DynamicHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ThreadPoolExcuterConfig
 * @date 2018/12/05 下午5:26
 * @description //TODO
 * @program holder-saas-store-item
 */
@Component
@Slf4j
public class ThreadPoolExcuterConfig {
    @Autowired
    private DynamicHelper dynamicHelper;

    private final AtomicInteger mCount = new AtomicInteger(0);

    private final ExecutorService executorService = new ThreadPoolExecutor(5, 15,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(99999),
            r -> new Thread(r, "dishCustomerPublish-" + mCount.incrementAndGet()),
            new ThreadPoolExecutor.AbortPolicy());

    public Future<?> excute(Runnable runnable) throws ExecutionException, InterruptedException {
//        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
//        dynamicHelper.changeDatasource(enterpriseGuid);

        return executorService.submit(runnable);
    }
}
