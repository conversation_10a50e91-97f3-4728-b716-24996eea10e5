package com.holderzone.saas.store.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.common.ItemStringListDTO;
import com.holderzone.saas.store.dto.item.req.TypeReqDTO;
import com.holderzone.saas.store.dto.item.req.TypeSortReqDTO;
import com.holderzone.saas.store.dto.item.req.TypeSortSwitchReqDTO;
import com.holderzone.saas.store.dto.item.resp.JournalingItemRespDTO;
import com.holderzone.saas.store.dto.item.resp.SaleTypeRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeSynRespDTO;
import com.holderzone.saas.store.dto.item.resp.TypeWebRespDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.item.constant.Constant;
import com.holderzone.saas.store.item.constant.GuidKeyConstant;
import com.holderzone.saas.store.item.entity.domain.ItemDO;
import com.holderzone.saas.store.item.entity.domain.RTypeAttrDO;
import com.holderzone.saas.store.item.entity.domain.TypeDO;
import com.holderzone.saas.store.item.entity.enums.SalesModelEnum;
import com.holderzone.saas.store.item.entity.query.JournalingItemsQuery;
import com.holderzone.saas.store.item.helper.EventPushHelper;
import com.holderzone.saas.store.item.mapper.ItemMapper;
import com.holderzone.saas.store.item.mapper.TypeMapper;
import com.holderzone.saas.store.item.service.IItemService;
import com.holderzone.saas.store.item.service.IRTypeAttrService;
import com.holderzone.saas.store.item.service.ITypeService;
import com.holderzone.saas.store.item.service.rpc.OrganizationService;
import com.holderzone.saas.store.item.util.DynamicHelper;
import com.holderzone.saas.store.item.util.MapStructUtils;
import com.holderzone.saas.store.item.util.PushUtils;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.item.constant.Constant.NO_TYPE_WITHIN_STORES;
import static com.holderzone.saas.store.item.constant.Constant.SAVE_BY_STORE;

/**
 * <p>
 * 商品分类 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Service
@Slf4j
public class TypeServiceImpl extends ServiceImpl<TypeMapper, TypeDO> implements ITypeService {

    private final TypeMapper typeMapper;
    private final DynamicHelper dynamicHelper;
    private final IItemService itemService;
    private final ItemMapper itemMapper;
    private final IRTypeAttrService typeAttrService;
    private final EventPushHelper eventPushHelper;
    private final PushUtils pushUtils;
    private final RedisTemplate redisTemplate;

    private final OrganizationService organizationService;

    @Autowired
    public TypeServiceImpl(TypeMapper typeMapper,
                           DynamicHelper dynamicHelper,
                           @Lazy IItemService itemService,
                           ItemMapper itemMapper,
                           IRTypeAttrService typeAttrService,
                           @Lazy EventPushHelper eventPushHelper,
                           PushUtils pushUtils,
                           RedisTemplate redisTemplate,
                           OrganizationService organizationService) {
        this.typeMapper = typeMapper;
        this.dynamicHelper = dynamicHelper;
        this.itemService = itemService;
        this.itemMapper = itemMapper;
        this.typeAttrService = typeAttrService;
        this.eventPushHelper = eventPushHelper;
        this.pushUtils = pushUtils;
        this.redisTemplate = redisTemplate;
        this.organizationService = organizationService;
    }

    @Override
    public Integer getSort(ItemSingleDTO itemSingleDTO) {
        if (Integer.valueOf(0).equals(itemSingleDTO.getFrom())) {
            // 来源为门店入口
            if (StringUtils.isEmpty(itemSingleDTO.getData())) {
                throw new ParameterException("门店必选");
            }
            return typeMapper.selectCount(
                    new LambdaQueryWrapper<TypeDO>()
                            .eq(TypeDO::getStoreGuid, itemSingleDTO.getData())) + 1;
        } else if (Integer.valueOf(1).equals(itemSingleDTO.getFrom())) {
            return typeMapper.selectCount(
                    new LambdaQueryWrapper<TypeDO>()
                            .eq(TypeDO::getBrandGuid, itemSingleDTO.getData())
                            .isNull(TypeDO::getStoreGuid)) + 1;
        } else {
            throw new ParameterException("来源入参不正确");
        }
    }


    @Override
    public Integer saveType(TypeReqDTO typeReqDTO) {
        validateTypeReqDTO(typeReqDTO);
        Integer count;

        TypeDO mustPointTypeDO = null;
        if (typeReqDTO.getFrom() == 0) { // 门店新建商品分类
            count = typeMapper.selectCount(
                    new LambdaQueryWrapper<TypeDO>()
                            .eq(TypeDO::getStoreGuid, typeReqDTO.getStoreGuid())
                            .eq(TypeDO::getName, typeReqDTO.getName()));

            mustPointTypeDO = typeMapper.selectOne(
                    new LambdaQueryWrapper<TypeDO>()
                            .isNull(TypeDO::getBrandGuid)
                            .eq(TypeDO::getStoreGuid, typeReqDTO.getStoreGuid())
                            .eq(TypeDO::getIsMustPoint, BooleanEnum.TRUE.getCode()));
        } else if (typeReqDTO.getFrom() == 5) {
            count = typeMapper.selectCount(
                    new LambdaQueryWrapper<TypeDO>()
                            .eq(TypeDO::getPricePlanGuid, typeReqDTO.getPricePlanGuid())
                            .isNull(TypeDO::getStoreGuid)
                            .isNull(TypeDO::getBrandGuid)
            );
        } else {// 品牌新建商品分类
            count = typeMapper.selectCount(
                    new LambdaQueryWrapper<TypeDO>()
                            .eq(TypeDO::getBrandGuid, typeReqDTO.getBrandGuid())
                            .isNull(TypeDO::getStoreGuid)
                            .isNull(TypeDO::getParentGuid)
                            .eq(TypeDO::getName, typeReqDTO.getName()));

            mustPointTypeDO = typeMapper.selectOne(
                    new LambdaQueryWrapper<TypeDO>()
                            .eq(TypeDO::getBrandGuid, typeReqDTO.getBrandGuid())
                            .isNull(TypeDO::getStoreGuid)
                            .isNull(TypeDO::getParentGuid)
                            .eq(TypeDO::getIsMustPoint, BooleanEnum.TRUE.getCode()));
        }
        if (count > 0) {
            throw new ParameterException("重名");
        }
        // 快速建分类时，添加排序字段
        if (typeReqDTO.getSort() == null) {
            if (typeReqDTO.getFrom() == 0) {
                Integer typeCount = typeMapper.selectCount(
                        new LambdaQueryWrapper<TypeDO>()
                                .eq(TypeDO::getStoreGuid, typeReqDTO.getStoreGuid()));
                typeReqDTO.setSort(typeCount + 1);
            } else if (typeReqDTO.getFrom() == 5) {
                Integer typeCount = typeMapper.selectCount(
                        new LambdaQueryWrapper<TypeDO>()
                                .eq(TypeDO::getPricePlanGuid, typeReqDTO.getPricePlanGuid())
                                .isNull(TypeDO::getStoreGuid)
                                .isNull(TypeDO::getBrandGuid)
                );
                typeReqDTO.setSort(typeCount + 1);
            } else {
                Integer typeCount = typeMapper.selectCount(
                        new LambdaQueryWrapper<TypeDO>()
                                .eq(TypeDO::getBrandGuid, typeReqDTO.getBrandGuid())
                                .isNull(TypeDO::getParentGuid)
                                .isNull(TypeDO::getStoreGuid));
                typeReqDTO.setSort(typeCount + 1);
            }
        }
        // 快速建分类时为空
        if (typeReqDTO.getIsEnable() == null) {
            typeReqDTO.setIsEnable(1);
        }
        TypeDO typeDO = MapStructUtils.INSTANCE.typeReqDTO2typeDO(typeReqDTO);
        try {
            typeDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, GuidKeyConstant.HSI_TYPE)));
        } catch (IOException e) {
            throw new BusinessException(Constant.CREATE_GUID_FAIL);
        }
        typeDO.setItemNum(0);
        typeDO.setTypeFrom(typeReqDTO.getFrom());

        //判断是否必点
        if (Objects.nonNull(typeReqDTO.getIsMustPoint())
                && typeReqDTO.getIsMustPoint() == BooleanEnum.TRUE.getCode()
                && Objects.nonNull(mustPointTypeDO)) {
            log.info("必点分类修改为非必点");
            mustPointTypeDO.setIsMustPoint(BooleanEnum.FALSE.getCode());
            typeMapper.updateById(mustPointTypeDO);
        }
        return typeMapper.insert(typeDO);
    }

    @Override
    public Integer updateType(TypeReqDTO typeReqDTO) {
        validateGuid(typeReqDTO);
        //编辑菜谱分类图片类型，编辑后直接返回
        if (null != typeReqDTO.getMenuClassifyPictureType()) {
            boolean update = this.update(new LambdaUpdateWrapper<TypeDO>()
                    .set(TypeDO::getMenuClassifyPictureType, typeReqDTO.getMenuClassifyPictureType())
                    .eq(TypeDO::getGuid, typeReqDTO.getTypeGuid())
            );
            return update ? 1 : 0;
        }
        // 如果该分类有对应推送到门店的分类实体，那么直接现在做修改或删除就更新门店信息  属性同理
        // 如果被编辑分类是被推送过来的分类，则目前不能编辑任何内容
        validateTypeReqDTOForUpdate(typeReqDTO);
        // 关联的门店GUID集合
        Set<String> relateStoreGuidSet = new HashSet<>();
        TypeDO typeDO = MapStructUtils.INSTANCE.typeReqDTO2typeDO(typeReqDTO);
        if (!StringUtils.isEmpty(typeDO.getStoreGuid())) {
            relateStoreGuidSet.add(typeDO.getStoreGuid());
        }
        // 查询由商品库推送到门店商品库的商品分类
        List<TypeDO> pushTypeList = list(
                new LambdaQueryWrapper<TypeDO>()
                        .eq(TypeDO::getParentGuid, typeReqDTO.getTypeGuid()));
        if (!CollectionUtils.isEmpty(pushTypeList)) {
            if (typeReqDTO.getFrom() == 1) {
                // 当请求来源于品牌库且有推送至门店的分类
                // 修改门店中存在的门店自建同名分类名称
                Set<String> storeGuidSet = fixDuplicateNameFromStore(typeDO, pushTypeList);
                if (!CollectionUtils.isEmpty(storeGuidSet)) {
                    relateStoreGuidSet.addAll(storeGuidSet);
                }
            }
            // 同步修改被推送分类的信息
            changePushTypeFields(pushTypeList, typeReqDTO);
            pushTypeList.add(typeDO);
            boolean update = updateBatchById(pushTypeList, pushTypeList.size());
            Set<String> storeGuidSet = pushTypeList
                    .stream()
                    .map(TypeDO::getStoreGuid)
                    .collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(storeGuidSet)) {
                relateStoreGuidSet.addAll(storeGuidSet);
            }
//            if (!CollectionUtils.isEmpty(relateStoreGuidSet)) {
//                eventPushHelper.pushMsgToAndriod(new ArrayList<>(relateStoreGuidSet));
//            }
            if (typeDO.getIsEnable() == 0) {
                ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
                itemSingleDTO.setData(typeDO.getGuid());
                disableType(itemSingleDTO);
            }
            return update ? 1 : 0;
        }
        boolean update = updateById(typeDO);
        if (typeDO.getIsEnable() == 0) {
            ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
            itemSingleDTO.setData(typeDO.getGuid());
            disableType(itemSingleDTO);
        }
        return update ? 1 : 0;
    }

    private void validateGuid(TypeReqDTO typeReqDTO) {
        if (StringUtils.isEmpty(typeReqDTO.getTypeGuid())) {
            throw new ParameterException("字段不完整");
        }
    }

    /**
     * 修改被推送的分类的相应字段
     *
     * @param pushTypeList
     * @param typeReqDTO
     */
    private void changePushTypeFields(List<TypeDO> pushTypeList, TypeReqDTO typeReqDTO) {
        pushTypeList.forEach(typeDO -> {
            typeDO.setName(typeReqDTO.getName());
            typeDO.setDescription(typeReqDTO.getDescription());
            typeDO.setIconUrl(typeReqDTO.getIconUrl());
            typeDO.setSort(typeReqDTO.getSort());
            typeDO.setIsEnable(typeReqDTO.getIsEnable());
        });
    }

    /**
     * 修改门店中自建的与此分类重名分类的名称，修改为XX（自建）
     *
     * @param typeDO       当前被修改分类
     * @param pushTypeList 被当前分类推送至门店的分类集合
     * @Return 返回受影响的门店GUID集合
     */
    private Set<String> fixDuplicateNameFromStore(TypeDO typeDO, List<TypeDO> pushTypeList) {
        // 被推送的商品分类所属门店的门店guid列表
        List<String> storeGuidList = pushTypeList.stream().map(TypeDO::getStoreGuid).collect(Collectors.toList());
        // 重名的分类集合
        List<TypeDO> duplicateNameTypeList = list(new LambdaQueryWrapper<TypeDO>()
                .ne(TypeDO::getParentGuid, typeDO.getGuid())
                .ne(TypeDO::getGuid, typeDO.getGuid())
                .eq(TypeDO::getName, typeDO.getName())
                .in(TypeDO::getStoreGuid, storeGuidList));
        if (duplicateNameTypeList.isEmpty()) {
            return new HashSet<>();
        }
        duplicateNameTypeList.forEach(pushTypeDO -> pushTypeDO.setName(typeDO.getName() + SAVE_BY_STORE));
        // todo 打印sql
        saveOrUpdateBatch(duplicateNameTypeList, duplicateNameTypeList.size());
        return duplicateNameTypeList.stream().map(TypeDO::getStoreGuid).collect(Collectors.toSet());
    }

    /**
     * 修改时校验
     *
     * @param typeReqDTO
     */
    private void validateTypeReqDTOForUpdate(TypeReqDTO typeReqDTO) {
        Integer count;
        TypeDO mustPointTypeDO = null;
        if (typeReqDTO.getFrom() == 1) {
            count = typeMapper.selectCount(new LambdaQueryWrapper<TypeDO>().eq(TypeDO::getBrandGuid, typeReqDTO.getBrandGuid())
                    .isNull(TypeDO::getStoreGuid)
                    .eq(TypeDO::getName, typeReqDTO.getName())
                    .ne(TypeDO::getGuid, typeReqDTO.getTypeGuid()));

            mustPointTypeDO = typeMapper.selectOne(
                    new LambdaQueryWrapper<TypeDO>()
                            .eq(TypeDO::getBrandGuid, typeReqDTO.getBrandGuid())
                            .isNull(TypeDO::getStoreGuid)
                            .isNull(TypeDO::getParentGuid)
                            .eq(TypeDO::getIsMustPoint, BooleanEnum.TRUE.getCode()));

        } else if (typeReqDTO.getFrom() == 0) {
            count = typeMapper.selectCount(new LambdaQueryWrapper<TypeDO>().eq(TypeDO::getStoreGuid, typeReqDTO.getStoreGuid())
                    .eq(TypeDO::getName, typeReqDTO.getName())
                    .ne(TypeDO::getGuid, typeReqDTO.getTypeGuid()));

            mustPointTypeDO = typeMapper.selectOne(
                    new LambdaQueryWrapper<TypeDO>()
                            .isNull(TypeDO::getBrandGuid)
                            .eq(TypeDO::getStoreGuid, typeReqDTO.getStoreGuid())
                            .eq(TypeDO::getIsMustPoint, BooleanEnum.TRUE.getCode()));
        } else if (typeReqDTO.getFrom() == 5) {
            count = typeMapper.selectCount(new LambdaQueryWrapper<TypeDO>()
                    .eq(TypeDO::getPricePlanGuid, typeReqDTO.getPricePlanGuid())
                    .eq(TypeDO::getName, typeReqDTO.getName())
                    .ne(TypeDO::getGuid, typeReqDTO.getTypeGuid()));
        } else {
            throw new ParameterException("来源入参错误");
        }
        if (count > 0) {
            throw new ParameterException("菜品分类重名");
        }

        if (Objects.nonNull(typeReqDTO.getIsMustPoint())
                && typeReqDTO.getIsMustPoint() == BooleanEnum.TRUE.getCode()
                && Objects.nonNull(mustPointTypeDO)) {
            log.info("修改必点菜品，取消其他必点菜品");
            mustPointTypeDO.setIsMustPoint(BooleanEnum.FALSE.getCode());
            typeMapper.updateById(mustPointTypeDO);
        }
    }

    /**
     * 校验入参
     *
     * @param typeReqDTO
     */
    private void validateTypeReqDTO(TypeReqDTO typeReqDTO) {
        if (typeReqDTO.getFrom() == 0 && StringUtils.isEmpty(typeReqDTO.getStoreGuid())) {
            throw new ParameterException("门店必选");
        }
        if (typeReqDTO.getFrom() == 1 && StringUtils.isEmpty(typeReqDTO.getBrandGuid())) {
            throw new ParameterException("品牌必选");
        }
        if (typeReqDTO.getFrom() != 1 && typeReqDTO.getFrom() != 0 && typeReqDTO.getFrom() != 5) {
            throw new ParameterException("来源入参错误");
        }
        if (typeReqDTO.getName().equals("宴会套餐")) {
            throw new BusinessException("宴会套餐分类名称已被宴会套餐功能占用，请重新命名");
        }
    }

    @Override
    public List<TypeWebRespDTO> queryType(ItemSingleDTO itemSingleDTO) {
        Assert.hasText(itemSingleDTO.getData(), "storeGuid不能为空");
        // 请求来源： 0：门店入口,1:商品库入口;
        Integer source = itemSingleDTO.getFrom();
        if (!Integer.valueOf(1).equals(source) && !Integer.valueOf(0).equals(source)) {
            throw new ParameterException("来源入参错误");
        }
        String organizeGuid = itemSingleDTO.getData();
        // 数据库的分类集合
        List<TypeDO> typeDOList;
        if (Integer.valueOf(0).equals(source)) {
            if (StringUtils.isEmpty(organizeGuid)) {
                throw new ParameterException("门店必填");
            }
            List<ItemDO> itemDOList = itemService.list(
                    new LambdaQueryWrapper<ItemDO>()
                            .eq(ItemDO::getIsEnable, true)
                            .in(ItemDO::getStoreGuid, itemSingleDTO.getData()));
            Map<String, List<ItemDO>> typeGroupMap = itemDOList
                    .stream()
                    .collect(Collectors.groupingBy(e -> e.getTypeGuid()));
            typeDOList = typeMapper.selectList(
                    new LambdaQueryWrapper<TypeDO>()
                            .eq(TypeDO::getStoreGuid, organizeGuid)
                            .eq(TypeDO::getIsEnable, 1)
                            .ne(TypeDO::getName, "宴会套餐"));
            setItemNum(typeDOList, typeGroupMap);
        } else {
            if (StringUtils.isEmpty(organizeGuid)) {
                throw new ParameterException("品牌必填");
            }
            List<ItemDO> itemDOList = itemService.list(
                    new LambdaQueryWrapper<ItemDO>()
                            .in(ItemDO::getBrandGuid, itemSingleDTO.getData()));
            Map<String, List<ItemDO>> typeGroupMap = itemDOList
                    .stream()
                    .collect(Collectors.groupingBy(e -> e.getTypeGuid()));
            typeDOList = typeMapper.selectList(
                    new LambdaQueryWrapper<TypeDO>()
                            .eq(TypeDO::getBrandGuid, organizeGuid)
                            .eq(TypeDO::getIsEnable, 1)
                            .isNull(TypeDO::getStoreGuid)
                            .ne(TypeDO::getName, "宴会套餐"));
            setItemNum(typeDOList, typeGroupMap);
        }
        List<TypeWebRespDTO> typeWebRespDTOList = MapStructUtils.INSTANCE.typeDOList2typeWebRespDTOList(typeDOList);
        if (CollectionUtils.isEmpty(typeWebRespDTOList)) {
            log.warn("未查询到分类信息，itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
            return typeWebRespDTOList;
        }
        setRackItemNum(typeWebRespDTOList);
        if (Integer.valueOf(0).equals(source)) {
            List<TypeWebRespDTO> collect = typeWebRespDTOList.stream()
                    .filter(dto -> dto.getItemNum() > 0 || dto.getTypeFrom() != 2)
                    .collect(Collectors.toList());
            collect.sort(Comparator.comparing(TypeWebRespDTO::getSort));
            return collect;
        }
        typeWebRespDTOList.sort(Comparator.comparing(TypeWebRespDTO::getSort));
        return typeWebRespDTOList;
    }

    /**
     * 设置商品商品上下架数量
     *
     * @param typeWebRespDTOList 分类列表
     */
    private void setRackItemNum(List<TypeWebRespDTO> typeWebRespDTOList) {
        List<String> typeGuidList = typeWebRespDTOList.stream()
                .map(TypeWebRespDTO::getTypeGuid)
                .collect(Collectors.toList());
        List<ItemDO> rackItemDOList = itemMapper.listRackItemByType(typeGuidList, 1);
        Map<String, List<ItemDO>> rackItemDOMap = rackItemDOList.stream()
                .collect(Collectors.groupingBy(ItemDO::getTypeGuid));
        List<ItemDO> unRackItemDOList = itemMapper.listRackItemByType(typeGuidList, 0);
        Map<String, List<ItemDO>> unRackItemDOMap = unRackItemDOList.stream()
                .collect(Collectors.groupingBy(ItemDO::getTypeGuid));
        typeWebRespDTOList.forEach(type -> {
            String typeGuid = type.getTypeGuid();
            List<ItemDO> rackItemList = rackItemDOMap.get(typeGuid);
            if (CollectionUtils.isEmpty(rackItemList)) {
                type.setRackItemNum(0);
            } else {
                type.setRackItemNum(rackItemList.size());
            }
            List<ItemDO> unRackItemList = unRackItemDOMap.get(typeGuid);
            if (CollectionUtils.isEmpty(unRackItemList)) {
                type.setUnRackItemNum(0);
            } else {
                type.setUnRackItemNum(unRackItemList.size());
            }
        });
    }

    @Override
    public List<TypeWebRespDTO> queryTypeByStoreGuidList(ItemStringListDTO itemStringListDTO) {
        Assert.notEmpty(itemStringListDTO.getDataList(), "storeGuid集合不能为空");
        List<TypeDO> typeDOList = list(
                new LambdaQueryWrapper<TypeDO>()
                        .in(TypeDO::getStoreGuid, itemStringListDTO.getDataList())
                        .eq(TypeDO::getIsEnable, 1)
                        .orderByAsc(TypeDO::getSort));
        if (CollectionUtils.isEmpty(typeDOList)) {
            log.info(NO_TYPE_WITHIN_STORES);
            return new ArrayList<>();
        }
        return MapStructUtils.INSTANCE.typeDOList2typeWebRespDTOList(typeDOList);
    }

    private void setItemNum(List<TypeDO> typeDOList, Map<String, List<ItemDO>> typeGroupMap) {
        typeDOList.forEach(typeDO -> {
            List<ItemDO> itemDOS = typeGroupMap.get(typeDO.getGuid());
            if (CollectionUtils.isEmpty(itemDOS)) {
                typeDO.setItemNum(0);
            } else {
                typeDO.setItemNum(itemDOS.size());
            }
        });
        if (!CollectionUtils.isEmpty(typeDOList)) {
            updateBatchById(typeDOList, typeDOList.size());
        }
    }

    @Override
    @Transactional
    public Integer deleteType(ItemSingleDTO itemSingleDTO) {
        List<String> deleteTypeGuidList = selectDeleteOrDisableTypeGuidList(itemSingleDTO);
        // 删除分类以及分类相关实体
        boolean remove = removeTypeAndTypeRelate(deleteTypeGuidList, true);
        return remove ? 1 : 0;
    }

    /**
     * 禁用分类
     *
     * @param itemSingleDTO
     * @return
     */
    private Integer disableType(ItemSingleDTO itemSingleDTO) {
        List<String> deleteTypeGuidList = selectDeleteOrDisableTypeGuidList(itemSingleDTO);
        // 删除分类以及分类相关实体
        boolean remove = removeTypeAndTypeRelate(deleteTypeGuidList, false);
        return remove ? 1 : 0;
    }

    /**
     * 根据选择分类GUID获取删除或禁用分类的GUID集合
     *
     * @param itemSingleDTO
     * @return
     */
    private List<String> selectDeleteOrDisableTypeGuidList(ItemSingleDTO itemSingleDTO) {
        // 如果是被推送过来的分类，不能禁用
        validateItemSingleDTO(itemSingleDTO);
        String deleteTypeGuid = itemSingleDTO.getData();
        // 被禁用分类的GUID集合
        List<String> deleteTypeGuidList = new ArrayList<>(Arrays.asList(deleteTypeGuid));
        // 被推送到门店的分类集合
        List<TypeDO> pushedTypeDOList = list(
                new LambdaQueryWrapper<TypeDO>()
                        .eq(TypeDO::getParentGuid, deleteTypeGuid));
        if (!CollectionUtils.isEmpty(pushedTypeDOList)) {
            Set<String> pushedTypeGuidSet = pushedTypeDOList
                    .stream()
                    .map(TypeDO::getGuid)
                    .collect(Collectors.toSet());
            deleteTypeGuidList.addAll(pushedTypeGuidSet);
            if (!CollectionUtils.isEmpty(pushedTypeGuidSet)) {
                // 被推送分类的商品集合
                List<ItemDO> thisTypeItemList = itemService.list(
                        new LambdaQueryWrapper<ItemDO>()
                                .in(ItemDO::getTypeGuid, pushedTypeGuidSet));
                if (!CollectionUtils.isEmpty(thisTypeItemList)) {
                    // 关联了商品的门店的分类GUID集合
                    Set<String> withItemTypeGuidSet = thisTypeItemList
                            .stream()
                            .map(ItemDO::getTypeGuid)
                            .collect(Collectors.toSet());
                    // 将关联了自建商品的分类从待删除列表中删除，并将关联了自建商品的分类变为门店自建
                    updateWithItemType(deleteTypeGuidList, withItemTypeGuidSet);
                }
            }

        }
        return deleteTypeGuidList;
    }

    private boolean removeTypeAndTypeRelate(List<String> deleteTypeGuidList, boolean remove) {
        if (CollectionUtils.isNotEmpty(deleteTypeGuidList)) {
            // 删除分类与属性对应的关联关系
            typeAttrService.remove(
                    new LambdaQueryWrapper<RTypeAttrDO>()
                            .in(RTypeAttrDO::getTypeGuid, deleteTypeGuidList));
            if (remove) {
                // 删除分类
                return removeByIds(deleteTypeGuidList);
            } else {
                update(new TypeDO().setIsEnable(0),
                        new LambdaQueryWrapper<TypeDO>()
                                .in(TypeDO::getGuid, deleteTypeGuidList));
                return true;
            }
        }
        return false;
    }

    /**
     * 将关联了自建商品的分类从待删除列表中删除，并将关联了自建商品的分类变为门店自建
     *
     * @param deleteTypeGuidList
     * @param withItemTypeGuidSet
     */
    private void updateWithItemType(List<String> deleteTypeGuidList, Set<String> withItemTypeGuidSet) {
        if (CollectionUtils.isEmpty(deleteTypeGuidList) || CollectionUtils.isEmpty(withItemTypeGuidSet)) {
            return;
        }
        // 去掉关联了商品的分类，仅留下未关联商品的分类
        deleteTypeGuidList.removeAll(withItemTypeGuidSet);
        // 门店中被推送的关联了商品的分类
        List<TypeDO> withItemTypeDOList = (ArrayList) listByIds(withItemTypeGuidSet);
        // 将这些分类从被推送分类修改为门店自建分类
        pushUtils.fixFieldsFromPush2SelfCreate(withItemTypeDOList, TypeDO::setTypeFrom, TypeDO::setParentGuid);
        updateBatchById(withItemTypeDOList, withItemTypeDOList.size());
    }

    @Override
    public Integer removePushType(String storeGuid) {
        List<TypeDO> typeDOList = list(
                new LambdaQueryWrapper<TypeDO>()
                        .eq(TypeDO::getStoreGuid, storeGuid).eq(TypeDO::getTypeFrom, 2));
        if (CollectionUtils.isEmpty(typeDOList)) {
            return 1;
        }
        List<String> typeGuidList = typeDOList.stream().map(TypeDO::getGuid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(typeGuidList)) {
            return 1;
        }
        List<ItemDO> itemDOList = itemService.list(
                new LambdaQueryWrapper<ItemDO>()
                        .in(ItemDO::getTypeGuid, typeGuidList));
        if (CollectionUtils.isNotEmpty(itemDOList)) {
            // 将该分类变为门店自建分类
            Set<String> withItemTypeGuidSet = itemDOList
                    .stream()
                    .map(ItemDO::getTypeGuid)
                    .collect(Collectors.toSet());
            updateWithItemType(typeGuidList, withItemTypeGuidSet);
        }
        // 删除分类以及分类相关实体
        boolean remove = removeTypeAndTypeRelate(typeGuidList, true);
        return remove ? 1 : 0;
    }

    @Override
    public List<JournalingItemRespDTO> queryJournalingItemType() {
        List<JournalingItemRespDTO> list = new ArrayList<>();
        List<JournalingItemsQuery> journalingItemType = typeMapper.queryJournalingItemType();
        Map<String, List<JournalingItemsQuery>> brandPushItemTypes = journalingItemType.stream()
                .filter(o -> o.getItemFrom() == 2)
                .collect(Collectors.groupingBy(JournalingItemsQuery::getParentGuid));
        List<JournalingItemsQuery> storeItemTypes = journalingItemType.stream()
                .filter(o -> o.getItemFrom() == 0).collect(Collectors.toList());
        brandPushItemTypes.forEach((k, v) -> {
            JournalingItemRespDTO build = JournalingItemRespDTO
                    .builder()
                    .itemFrom(2)
                    .guid(k)
                    .name(v.get(0).getName())
                    .subGuids(v.stream().map(JournalingItemsQuery::getGuid).collect(Collectors.toList()))
                    .build();
            list.add(build);
        });
        storeItemTypes.forEach(o -> {
            JournalingItemRespDTO build = JournalingItemRespDTO
                    .builder()
                    .itemFrom(o.getItemFrom())
                    .guid(o.getGuid())
                    .name(o.getName())
                    .build();
            list.add(build);
        });
        return list;
    }

    @Override
    public Boolean retailUpdateTypesort(List<TypeSortReqDTO> typeList) {
        List<TypeDO> typeDOS = MapStructUtils.INSTANCE.typeSortReqDTOList2TypeDOList(typeList);
        return Objects.nonNull(typeDOS) ? super.updateBatchById(typeDOS) : Boolean.TRUE;
    }

    @Override
    public void mdmRemoveType(SingleDataDTO request) {
        //deleteType(request);
    }

    @Override
    public Boolean setGroupMealStatus(SingleDataDTO request) {
        String storeGuid = request.getStoreGuid();
        if (!RedissonLockUtil.tryLock(storeGuid, 1, 3)) {
            throw new BusinessException("系统繁忙请稍候重试");
        }
        try {
            TypeDO dbTypeDO = getOne(new LambdaQueryWrapper<TypeDO>()
                    .eq(TypeDO::getStoreGuid, request.getStoreGuid())
                    .eq(TypeDO::getName, "宴会套餐"));
            if (dbTypeDO == null) {
                dbTypeDO = TypeDO.initGroupMealDO();
                dbTypeDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, GuidKeyConstant.HSI_TYPE)));
                dbTypeDO.setStoreGuid(request.getStoreGuid());
            } else {
                //修改了当前状态
                dbTypeDO.setIsEnable(Integer.valueOf(request.getData()));
            }
            log.info("insert db object={}", JacksonUtils.writeValueAsString(dbTypeDO));
            return saveOrUpdate(dbTypeDO);
        } catch (IOException e) {
            throw new BusinessException(Constant.CREATE_GUID_FAIL);
        } finally {
            RedissonLockUtil.unlock(request.getStoreGuid());
        }
    }

    @Override
    public String selectGroupMealStatus(String storeGuid) {
        TypeDO typeDO = getOne(new LambdaQueryWrapper<TypeDO>()
                .eq(TypeDO::getName, "宴会套餐")
                .eq(TypeDO::getStoreGuid, storeGuid));
        if (Objects.isNull(typeDO)) {
            try {
                typeDO = TypeDO.initGroupMealDO();
                typeDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, GuidKeyConstant.HSI_TYPE)));
                typeDO.setStoreGuid(storeGuid);
                save(typeDO);
            } catch (IOException e) {
                throw new BusinessException(Constant.CREATE_GUID_FAIL);
            }
        }
        return typeDO.getIsEnable().toString();
    }


    private void validateItemSingleDTO(ItemSingleDTO itemSingleDTO) {
        if (StringUtils.isEmpty(itemSingleDTO.getData())) {
            throw new ParameterException("至少选中一个分类");
        }
        // 被选择删除的分类GUID
        String typeGuid = itemSingleDTO.getData();
        // 分类与商品关联的数量
        int typeItemCount = itemService.count(
                new LambdaQueryWrapper<ItemDO>()
                        .eq(ItemDO::getTypeGuid, typeGuid));
        if (typeItemCount > 0) {
            throw new BusinessException("该分类下已关联商品，操作失败");
        }
        // 分类与属性关联的数量
        int typeAttrCount = typeAttrService.count(
                new LambdaQueryWrapper<RTypeAttrDO>()
                        .eq(RTypeAttrDO::getTypeGuid, typeGuid));
        if (typeAttrCount > 0) {
            throw new BusinessException("该分类下已关联属性，操作失败");
        }
    }

    @Override
    @Transactional
    public Boolean switchSort(@Valid TypeSortSwitchReqDTO typeReqDTO) {
        TypeDO sourceType = this.getById(typeReqDTO.getSourceTypeGuid());
        TypeDO targetType = this.getById(typeReqDTO.getTargetTypeGuid());
        if (sourceType == null || targetType == null) {
            return false;
        }
        long srcId = sourceType.getId();
        Integer srcSort = sourceType.getSort();
        this.baseMapper.deleteByGuid(sourceType.getGuid());
        this.baseMapper.deleteByGuid(targetType.getGuid());
        sourceType.setId(targetType.getId());
        sourceType.setSort(targetType.getSort());
        targetType.setId(srcId);
        targetType.setSort(srcSort);

        boolean ret2 = this.save(targetType);
        boolean ret1 = this.save(sourceType);
        if (!ret1 || !ret2) {
            throw new BusinessException("商品类型排序交换失败");
        }
        return true;
    }

    /**
     * 批量修改分类顺序
     *
     * @param typeReqDTOList 里面只有sort和typeGuid
     * @return 1
     */
    @Override
    public Integer batchModifySort(List<TypeReqDTO> typeReqDTOList) {
        if (CollectionUtils.isEmpty(typeReqDTOList)) {
            return 1;
        }
        for (TypeReqDTO type : typeReqDTOList) {
            this.update(new LambdaUpdateWrapper<TypeDO>()
                    .set(TypeDO::getSort, type.getSort())
                    .eq(TypeDO::getGuid, type.getTypeGuid()));
        }
        return 1;
    }

    /**
     * 通过价格方案查询分类列表
     *
     * @param itemSingleDTO 价格方案guid
     * @return 分类列表
     */
    @Override
    public List<TypeWebRespDTO> queryTypeByPlan(ItemSingleDTO itemSingleDTO) {
        if (StringUtils.isEmpty(itemSingleDTO.getData())) {
            throw new BusinessException("菜谱方案guid不能为空");
        }
        List<TypeDO> typeDOList = this.list(new LambdaQueryWrapper<TypeDO>()
                .eq(TypeDO::getPricePlanGuid, itemSingleDTO.getData())
                .eq(TypeDO::getIsDelete, 0)
        );
        return MapStructUtils.INSTANCE.typeDOList2typeWebRespDTOList(typeDOList);
    }

    @Override
    public String saveTypePlan(TypeReqDTO typeReqDTO) {
        validateTypeReqDTO(typeReqDTO);
        Integer count;
        if (typeReqDTO.getFrom() == 0) { // 门店新建商品分类
            count = typeMapper.selectCount(
                    new LambdaQueryWrapper<TypeDO>()
                            .eq(TypeDO::getStoreGuid, typeReqDTO.getStoreGuid())
                            .eq(TypeDO::getName, typeReqDTO.getName()));
        } else if (typeReqDTO.getFrom() == 5) {
            count = typeMapper.selectCount(
                    new LambdaQueryWrapper<TypeDO>()
                            .eq(TypeDO::getPricePlanGuid, typeReqDTO.getPricePlanGuid())
                            .eq(TypeDO::getName, typeReqDTO.getName())
                            .isNull(TypeDO::getStoreGuid)
                            .isNull(TypeDO::getBrandGuid)
            );
        } else {// 品牌新建商品分类
            count = typeMapper.selectCount(
                    new LambdaQueryWrapper<TypeDO>()
                            .eq(TypeDO::getBrandGuid, typeReqDTO.getBrandGuid())
                            .isNull(TypeDO::getStoreGuid)
                            .isNull(TypeDO::getParentGuid)
                            .eq(TypeDO::getName, typeReqDTO.getName()));
        }
        if (count > 0) {
            throw new ParameterException("分类重名");
        }
        // 快速建分类时，添加排序字段
        if (typeReqDTO.getSort() == null) {
            if (typeReqDTO.getFrom() == 0) {
                Integer typeCount = typeMapper.selectCount(
                        new LambdaQueryWrapper<TypeDO>()
                                .eq(TypeDO::getStoreGuid, typeReqDTO.getStoreGuid()));
                typeReqDTO.setSort(typeCount + 1);
            } else if (typeReqDTO.getFrom() == 5) {
                Integer typeCount = typeMapper.selectCount(
                        new LambdaQueryWrapper<TypeDO>()
                                .eq(TypeDO::getPricePlanGuid, typeReqDTO.getPricePlanGuid())
                                .isNull(TypeDO::getStoreGuid)
                                .isNull(TypeDO::getBrandGuid)
                );
                typeReqDTO.setSort(typeCount + 1);
            } else {
                Integer typeCount = typeMapper.selectCount(
                        new LambdaQueryWrapper<TypeDO>()
                                .eq(TypeDO::getBrandGuid, typeReqDTO.getBrandGuid())
                                .isNull(TypeDO::getParentGuid)
                                .isNull(TypeDO::getStoreGuid));
                typeReqDTO.setSort(typeCount + 1);
            }
        }
        // 快速建分类时为空
        if (typeReqDTO.getIsEnable() == null) {
            typeReqDTO.setIsEnable(1);
        }
        TypeDO typeDO = MapStructUtils.INSTANCE.typeReqDTO2typeDO(typeReqDTO);
        try {
            typeDO.setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, GuidKeyConstant.HSI_TYPE)));
        } catch (IOException e) {
            throw new BusinessException(Constant.CREATE_GUID_FAIL);
        }
        typeDO.setItemNum(0);
        typeDO.setTypeFrom(typeReqDTO.getFrom());
        typeDO.setParentGuid(typeReqDTO.getParentGuid());
        typeDO.setGmtCreate(LocalDateTime.now());
        typeMapper.insert(typeDO);
        return typeDO.getGuid();
    }

    @Override
    public void updateTypePlan(String planGuid, String parentGuid) {
        //删除原菜谱方案分类
        this.remove(new LambdaUpdateWrapper<TypeDO>().eq(TypeDO::getPricePlanGuid, parentGuid));
        //将新菜谱方案分类关联原菜谱方案
        this.update(new LambdaUpdateWrapper<TypeDO>()
                .set(TypeDO::getPricePlanGuid, parentGuid)
                .eq(TypeDO::getPricePlanGuid, planGuid));
    }

    @Override
    public List<TypeWebRespDTO> querySourceTypeInfo(ItemStringListDTO listDTO) {
        List<TypeDO> typeDOList = this.list(new LambdaQueryWrapper<TypeDO>()
                .in(TypeDO::getGuid, listDTO.getDataList()));
        if (CollectionUtils.isEmpty(typeDOList)) {
            return Lists.newArrayList();
        }
        List<String> parentGuidList = typeDOList.stream()
                .map(TypeDO::getParentGuid)
                .filter(parentGuid -> !StringUtils.isEmpty(parentGuid))
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(parentGuidList)) {
            List<TypeDO> parentTypeDOList = this.list(new LambdaQueryWrapper<TypeDO>()
                    .in(TypeDO::getGuid, parentGuidList));
            typeDOList.addAll(parentTypeDOList);
        }
        return MapStructUtils.INSTANCE.typeDOList2typeWebRespDTOList(typeDOList);
    }

    /**
     * 查询品牌列表下的所有分类
     */
    @Override
    public List<TypeWebRespDTO> queryTypeByBrand(ItemStringListDTO query) {
        if (org.springframework.util.CollectionUtils.isEmpty(query.getDataList())) {
            return Lists.newArrayList();
        }
        List<TypeDO> typeDOList = this.list(new LambdaQueryWrapper<TypeDO>()
                .in(TypeDO::getBrandGuid, query.getDataList())
        );
        return MapStructUtils.INSTANCE.typeDOList2typeWebRespDTOList(typeDOList);
    }

    /**
     * 通过门店查询门店商品在品牌库所属的分类
     * 1.普通模式下，分类是门店售卖商品所属的分类
     * 2.菜谱模式下，分类是当前门店绑定的所有菜谱的分类（通过名称去重）
     */
    @Override
    public List<SaleTypeRespDTO> queryBrandTypeByStore(ItemSingleDTO query) {
        if (StringUtils.isEmpty(query.getStoreGuid())) {
            throw new BusinessException("门店guid不能为空");
        }
        BrandDTO brandDTO = organizationService.queryBrandByStoreGuid(query.getStoreGuid());
        log.info("[菜品模式数据]brandDTO={}", JacksonUtils.writeValueAsString(brandDTO));
        if (!ObjectUtils.isEmpty(brandDTO.getSalesModel()) && brandDTO.getSalesModel() == SalesModelEnum.NORMAL_MODE.getCode()) {
            return queryOrdinaryType(query);
        }

        List<TypeSynRespDTO> typeSynRespDTOList = typeMapper.queryPlanByStore(query);
        if (org.springframework.util.CollectionUtils.isEmpty(typeSynRespDTOList)) {
            return queryOrdinaryType(query);
        }
        return getTypeRespDTOList(typeSynRespDTOList);
    }

    /**
     * 普通模式分类查询
     */
    private List<SaleTypeRespDTO> queryOrdinaryType(ItemSingleDTO query) {
        List<TypeSynRespDTO> typeSynRespDTOList = typeMapper.queryOrdinaryByStore(query);
        return getTypeRespDTOList(typeSynRespDTOList);
    }

    @NotNull
    private List<SaleTypeRespDTO> getTypeRespDTOList(List<TypeSynRespDTO> typeSynRespDTOList) {
        Map<String, List<TypeSynRespDTO>> typeGroupMap = typeSynRespDTOList.stream()
                .collect(Collectors.groupingBy(TypeSynRespDTO::getName));
        List<SaleTypeRespDTO> typeRespDTOList = new ArrayList<>();
        typeGroupMap.forEach((name, typeList) -> {
            SaleTypeRespDTO respDTO = new SaleTypeRespDTO();
            List<String> typeGuidList = typeList.stream()
                    .map(TypeSynRespDTO::getTypeGuid)
                    .distinct()
                    .collect(Collectors.toList());
            List<String> parentGuidList = typeList.stream()
                    .map(TypeSynRespDTO::getParentGuid)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            typeGuidList.addAll(parentGuidList);
            respDTO.setTypeGuidList(typeGuidList);
            respDTO.setName(name);
            typeRespDTOList.add(respDTO);
        });
        return typeRespDTOList;
    }

}
