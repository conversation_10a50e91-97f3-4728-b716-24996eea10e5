package com.holderzone.saas.store.item.builder;

import com.beust.jcommander.internal.Lists;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.estimate.EstimateForAndroidReqDTO;
import com.holderzone.saas.store.dto.item.req.estimate.EstimateSkuReqDTO;
import com.holderzone.saas.store.item.entity.bo.EstimateBO;
import com.holderzone.saas.store.item.entity.domain.EstimateDO;

import java.util.ArrayList;
import java.util.List;

public class EstimateBizBuilder {

    public static EstimateBO build() {
        EstimateBO biz = new EstimateBO();
        biz.setItemGuidList(Lists.newArrayList());
        biz.setCreateSkuList(Lists.newArrayList());
        biz.setUpdateSkuList(Lists.newArrayList());
        biz.setRemoveSkuList(Lists.newArrayList());
        biz.setBatchCancelSkuList(Lists.newArrayList());
        biz.setBatchStopSkuList(Lists.newArrayList());
        biz.setSchedulingSkuList(Lists.newArrayList());
        biz.setReduceStockSkuList(Lists.newArrayList());
        biz.setReturnStockSkuList(Lists.newArrayList());
        return biz;
    }


    public static EstimateBO build(EstimateForAndroidReqDTO request) {
        EstimateBO biz = build();
        biz.setDeviceId(request.getDeviceId());
        biz.setStoreGuid(request.getStoreGuid());
        biz.setItemGuid(request.getItemGuid());
        List<EstimateDO> skuList = new ArrayList<>();
        for (EstimateSkuReqDTO skuReqDTO : request.getSkuReqList()) {
            EstimateDO estimateDO = new EstimateDO();
            estimateDO.setItemGuid(request.getItemGuid());
            estimateDO.setSkuGuid(skuReqDTO.getSkuGuid());
            estimateDO.setLimitQuantity(skuReqDTO.getLimitQuantity());
            estimateDO.setIsForeverEstimate(skuReqDTO.getIsForeverEstimate());
            skuList.add(estimateDO);
        }
        biz.setSkuList(skuList);
        return biz;
    }


    public static EstimateBO build(SingleDataDTO request) {
        EstimateBO biz = build();
        biz.setDeviceId(request.getDeviceId());
        biz.setStoreGuid(request.getStoreGuid());
        biz.getItemGuidList().addAll(request.getDatas());
        return biz;
    }
}
