package com.holderzone.saas.store.staff.utils;

import com.holderzone.saas.store.dto.organization.RegionDTO;
import com.holderzone.saas.store.staff.entity.bo.RegionBO;
import com.holderzone.saas.store.staff.entity.domain.UserDataDO;

import java.util.*;
import java.util.stream.Collectors;

public class TreeUtils {

    public static void main(String[] args) {
        List<UserDataDO> arrayOfRegionDO = new ArrayList<>();
        UserDataDO orgDO0 = new UserDataDO();
        orgDO0.setRegionCode("1");
        orgDO0.setRegionName("1");
        arrayOfRegionDO.add(orgDO0);

        UserDataDO orgDO1 = new UserDataDO();
        orgDO1.setRegionCode("111,11,1");
        orgDO1.setRegionName("111,11,1");
        arrayOfRegionDO.add(orgDO1);

//        UserDataDO orgDO2 = new UserDataDO();
//        orgDO2.setRegionCode("112,11,1");
//        orgDO2.setRegionName("112,11,1");
//        arrayOfRegionDO.add(orgDO2);

        UserDataDO orgDO3 = new UserDataDO();
        orgDO3.setRegionCode("121,12,1");
        orgDO3.setRegionName("121,12,1");
        arrayOfRegionDO.add(orgDO3);

        UserDataDO orgDO4 = new UserDataDO();
//        orgDO4.setRegionCode("122,12,1");
//        orgDO4.setRegionName("122,12,1");
        orgDO4.setRegionCode("12,1");
        orgDO4.setRegionName("12,1");
        arrayOfRegionDO.add(orgDO4);

        UserDataDO orgDO5 = new UserDataDO();
        orgDO5.setRegionCode("211,21,2");
        orgDO5.setRegionName("211,21,2");
        arrayOfRegionDO.add(orgDO5);

        UserDataDO orgDO6 = new UserDataDO();
        orgDO6.setRegionCode("212,21,2");
        orgDO6.setRegionName("212,21,2");
        arrayOfRegionDO.add(orgDO6);

        UserDataDO orgDO7 = new UserDataDO();
        orgDO7.setRegionCode("221,22,2");
        orgDO7.setRegionName("221,22,2");
        arrayOfRegionDO.add(orgDO7);

        UserDataDO orgDO8 = new UserDataDO();
        orgDO8.setRegionCode("222,22,2");
        orgDO8.setRegionName("222,22,2");
        arrayOfRegionDO.add(orgDO8);

        System.out.println(collectRegionAsTree(arrayOfRegionDO));
    }

    public static List<RegionDTO> collectRegionAsTree(List<UserDataDO> arrayOfRegionDO) {
        List<RegionDTO> arrayOfRegionDTO = new ArrayList<>();
        recursive(arrayOfRegionDTO, do2Bo(arrayOfRegionDO));
        return arrayOfRegionDTO;
    }

    private static List<RegionBO> do2Bo(List<UserDataDO> arrayOfRegionDO) {
        return arrayOfRegionDO.stream()
                .map(orgDO -> {
                    String[] guids = orgDO.getRegionCode().split(",");
                    String[] names = orgDO.getRegionName().split(",");
                    RegionBO root = new RegionBO();
                    RegionBO temp = root;
                    int depth = guids.length;
                    for (int i = 0; i < depth; i++) {
                        int j = depth - i - 1;
                        temp.setCode(guids[j]);
                        temp.setName(names[j]);
                        if (i < depth - 1) {
                            RegionBO subOrgBO = new RegionBO();
                            temp.setChild(subOrgBO);
                            temp = subOrgBO;
                        }
                    }
                    return root;
                })
                .collect(Collectors.toList());
    }

    private static void recursive(List<RegionDTO> arrayOfRegionDTO, List<RegionBO> arrayOfRegionBO) {
        Map<String, List<RegionBO>> mapOfArrayOfRegionBO = arrayOfRegionBO.stream()
                .collect(Collectors.groupingBy(
                        o -> o.getCode() + "_" + o.getName(),
                        LinkedHashMap::new, Collectors.toList()
                ));
        mapOfArrayOfRegionBO.forEach((guidNamePair, arrayOfRegionBoGrouped) -> {
            List<RegionDTO> arrayOfSubRegionDTO = new ArrayList<>();
            String[] guidAndName = guidNamePair.split("_");
            RegionDTO regionDTO = new RegionDTO(guidAndName[0], guidAndName[1], arrayOfSubRegionDTO);
            arrayOfRegionDTO.add(regionDTO);
            List<RegionBO> arrayOfSubRegionBO = arrayOfRegionBoGrouped.stream()
                    .map(RegionBO::getChild)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            recursive(arrayOfSubRegionDTO, arrayOfSubRegionBO);
            if (arrayOfSubRegionBO.size() < arrayOfRegionBoGrouped.size()) {
                regionDTO.setIsCheckable(true);
            }
            if (arrayOfSubRegionDTO.isEmpty()) {
                regionDTO.setChildren(null);
                regionDTO.setIsCheckable(true);
            } else if (null == regionDTO.getIsCheckable()) {
                regionDTO.setIsCheckable(false);
            }
        });
    }
}
