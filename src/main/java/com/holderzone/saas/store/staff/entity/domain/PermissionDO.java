package com.holderzone.saas.store.staff.entity.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className PermissionDO
 * @date 18-11-15 上午10:31
 * @description PermissionDO
 * @program holder-saas-store-staff
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PermissionDO {
    /**
     * 角色guid
     */
    private String roleGuid;

    /**
     * 权限code
     */
    private Integer permissionCode;

    /**
     * 权限名称
     */
    private String permissionName;

    /**
     * 权限备注，整单折扣-最高可打x折，整单让价-最高可让x元
     */
    private BigDecimal remark;

    /**
     * 创建人guid
     */
    private String createUserGuid;

    /**
     * 更新人guid
     */
    private String updateUserGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PermissionDO that = (PermissionDO) o;
        return Objects.equals(permissionCode, that.permissionCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(permissionCode);
    }
}
