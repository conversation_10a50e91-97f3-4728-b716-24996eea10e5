package com.holderzone.saas.store.staff.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className Constant
 * @date 2020/12/30 15:10
 * @description 常量类
 * @program holder-saas-store-business
 */
public class Constant {

    private Constant() {
        throw new IllegalStateException("Utility class");
    }

    public static final Integer FALSE = 0;

    public static final Integer TRUE = 1;

    public static final Integer NUMBER_ZERO = 0;

    public static final Integer NUMBER_ONE = 1;

    public static final String USER_PERMISSION_TABLE_IS_EMPTY = "用户权限名称表查询为空";

    public static final String AUTHORIZATION_CODE_CANNOT_BE_EMPTY = "授权码不能为空";

    public static final String NOT_FOUND_YOUR_FACE = "授权失败，未查询到您的人脸";

    public static final String AUTHORIZATION_CODE_NOT_FOUND = "授权失败，未查询到您的授权信息";

}
