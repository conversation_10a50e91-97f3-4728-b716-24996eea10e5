package com.holderzone.saas.store.staff.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hss_product_theme")
public class ProductThemeDO implements Serializable {

    private static final long serialVersionUID = 6676743751346682213L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 产品GUID
     */
    private String productGuid;

    /**
     * 产品名称
     */
    private String productName;


    /**
     * 终端GUID
     */
    private String terminalGuid;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * todo 云端添加该code
     * 终端编码
     */
    private String terminalCode;

    /**
     * 主题GUID
     */
    private String themeGuid;

    /**
     * 主题名称
     */
    private String themeName;

    /**
     * 主题编码，对应于 hse_theme_db 的 type
     */
    private String themeCode;

    /**
     * 主题状态：0：上架，1：下架
     */
    private Integer themeStatus;

    /**
     * 到期时间
     */
    private LocalDateTime themeExpireTime;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
