package com.holderzone.saas.store.staff.event;

import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.holderzone.framework.dds.starter.exception.DynamicException;
import com.holderzone.framework.dds.starter.utils.DynamicInfoHelper;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.authorization.ProductResource;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.saas.store.staff.config.RocketMqConfig;
import com.holderzone.saas.store.staff.entity.domain.StoreSourceDO;
import com.holderzone.saas.store.staff.service.StoreSourceService;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import com.holderzone.saas.store.staff.service.remote.EnterpriseDataClient;
import com.holderzone.saas.store.staff.utils.DynamicHelper;
import com.holderzone.saas.store.staff.utils.EnterpriseUtils;
import com.mysql.cj.exceptions.CJException;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className GrantProductAuthListener
 * @date 19-1-30 上午9:58
 * @description 订阅云端产品授权相关消息（同步场景：对企业或门店授权-新建或叠加、对企业或门店修改授权），处理逻辑：若推送过来的产品之前不存在则表示新增，若存在则表示修改
 * @program holder-saas-store-staff
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.PRODUCT_AUTH_ASYNC_TOPIC,
        tags = RocketMqConfig.PRODUCT_AUTH_ASYNC_TAG,
        consumerGroup = RocketMqConfig.PRODUCT_AUTH_CONSUMER_GROUP,
        consumeThreadMax = 4,
        consumeThreadMin = 1,
        reTryConsume = 4
)
@AllArgsConstructor
public class SyncErpAuthListener extends AbstractRocketMqConsumer<RocketMqTopic,String> {

    private final StoreSourceService storeSourceService;

    private final DynamicHelper dynamicHelper;

    private final RedisTemplate redisTemplate;

    private final EnterpriseClient enterpriseClient;
    private final EnterpriseDataClient enterpriseDataClient;
    private final DynamicInfoHelper dynamicInfoHelper;
    private final ObjectMapper objectMapper;

    @Override
    public boolean consumeMsg(String message, MessageExt messageExt) {
        // 产品授权逻辑：
        // 若消息中storeGuid为空则表示对企业授权
        // 若消息中storeGuid不为空则表示对门店授权

        String lockKey = "lock:" + messageExt.getMsgId();
        try {
            UnMessage<ProductResource> unMessage =   objectMapper.readValue(message,new TypeReference<UnMessage<ProductResource>>() {});
            log.info("权限消息消费，企业：{}，当前时间为：{}",
                    unMessage.getEnterpriseGuid(), DateTimeUtils.now().toString());
            ProductResource productResource =  unMessage.getMessage();

            String enterpriseGuid = unMessage.getEnterpriseGuid();
            if(!DynamicHelper.changeAndCheckDatasource(dynamicInfoHelper,enterpriseGuid)){
                return true;
            }
            if (!EnterpriseUtils.checkEnterpriseExists(enterpriseDataClient, enterpriseClient, enterpriseGuid)) {
                return true;
            }
             //加锁
            boolean tryLock = RedissonLockUtil.tryLock(lockKey, TimeUnit.SECONDS, 5, 30);
            if (!tryLock) {
                return false;
            }

            // 判断是：新增/叠加权限（数量为0）、还是修改权限（数量不为0）
            LambdaQueryWrapper<StoreSourceDO> lambdaQueryWrapper = new LambdaQueryWrapper<StoreSourceDO>()
                    .eq(StoreSourceDO::getProductGuid, productResource.getProductGuid());
            if (StringUtils.hasText(unMessage.getStoreGuid())) {
                lambdaQueryWrapper.eq(StoreSourceDO::getStoreGuid, unMessage.getStoreGuid());
            }
            boolean addOrElseUpdate = storeSourceService.count(lambdaQueryWrapper) == 0;
            boolean resultAuth;
            if (addOrElseUpdate) {
                resultAuth = storeSourceService.saveProductAuth(
                        productResource,
                        unMessage.getStoreGuid()
                );
            } else {
                resultAuth = storeSourceService.updateProductAuth(
                        productResource,
                        unMessage.getStoreGuid()
                );
            }
            return resultAuth;
        }  catch (Exception e) {
            log.error("授权产品权限消息消费，发生异常，enterpriseGuid：{}，异常：",
                    EnterpriseIdentifier.getEnterpriseGuid(), e);
        } finally {
            //解锁
            RedissonLockUtil.unlock(lockKey);
            dynamicHelper.clear();
        }
        return false;
    }
}
