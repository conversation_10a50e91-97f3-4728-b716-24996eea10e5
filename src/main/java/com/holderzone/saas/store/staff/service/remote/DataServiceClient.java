package com.holderzone.saas.store.staff.service.remote;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.resource.common.dto.data.ServerDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className DataServiceClient
 * @date 18-9-21 下午1:41
 * @description
 * @program holder-saas-store-staff
 */
@Deprecated
@Component
@FeignClient(value = "data-service", fallbackFactory = DataServiceClient.ServiceFallBack.class)
public interface DataServiceClient {

    /**
     * 根据传入的资源guid的list查询对应的资源
     *
     * @param sourceGuids 资源guid数组
     * @return 服务列表
     */
    @PostMapping("/server/selectMultiServer")
    List<ServerDTO> selectMultiSources(@RequestBody List<String> sourceGuids);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<DataServiceClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public DataServiceClient create(Throwable cause) {
            return sourceGuids -> {
                log.error(HYSTRIX_PATTERN, "selectMultiSources", JacksonUtils.writeValueAsString(sourceGuids),
                          ThrowableUtils.asString(cause));
                throw new BusinessException("调用服务查询资源失败");
            };
        }
    }
}
