package com.holderzone.saas.store.staff.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.user.UserAuthorityDTO;
import com.holderzone.saas.store.dto.user.req.PermissionsReqDTO;
import com.holderzone.saas.store.dto.user.resp.PermissionsRespDTO;
import com.holderzone.saas.store.staff.entity.domain.UserAuthorityDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UserMapper
 * @date 2018/01/15 下午14:519
 * @description User表相关接口
 * @program holder-saas-store-staff
 */
@Repository
public interface UserAuthorityMapper extends BaseMapper<UserAuthorityDO> {

    /**
     * 查询所有可升级权限
     * @param reqDTO
     * @return List<PermissionsRespDTO>
     */
    List<PermissionsRespDTO> queryEmployeePermissions(@Param("reqDTO") PermissionsReqDTO reqDTO);

    /**
     * 根据资源code、用户guid和授权号查询授权信息
     * @param reqDTO 资源code,权限code
     * @return UserAuthorityDO
     */
    PermissionsRespDTO queryAuthorize(@Param("reqDTO") UserAuthorityDTO reqDTO);

    /**
     * 查询后台是否设置授权
     * @param reqDTO
     * @return
     */
    Integer queryUserAuthority(@Param("reqDTO") PermissionsReqDTO reqDTO);

    /**
     * 查询门店下是否有员工勾选自己设置的权限
     * @param permissionsReqDTO userAuthorityQueryDTO
     * @return List<PermissionsRespDTO>
     */
    Integer queryAuthorityAnyMatch(@Param("dto") PermissionsReqDTO permissionsReqDTO);
}
