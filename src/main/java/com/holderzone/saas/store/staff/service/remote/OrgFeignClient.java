package com.holderzone.saas.store.staff.service.remote;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.*;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Component
@FeignClient(value = "holder-saas-store-organization", fallbackFactory = OrgFeignClient.ServiceClientFallback.class)
public interface OrgFeignClient {

    /**
     * 查询企业、组织、门店并返回上下级结构
     *
     * @param queryErp
     * @param queryStore
     * @return
     */
    @PostMapping("/organization/query_erp_org_store")
    List<OrgGeneralDTO> queryErpOrgStore(@RequestParam(value = "queryErp") Integer queryErp,
                                         @RequestParam(value = "queryStore") Integer queryStore);

    /**
     * 根据传入的组织guid集合查询组织并返回包含上下级的树形结构（不包含企业、门店）
     *
     * @param orgGuidList
     * @return
     */
    @PostMapping("/organization/query_org_by_child")
    List<OrgGeneralDTO> queryOrgByChildIdList(@RequestBody List<String> orgGuidList);

    /**
     * 根据传入的组织guid集合查询组织并返回仅包含自身的扁平结构
     *
     * @param guidList
     * @return
     */
    @PostMapping("/organization/query_org_by_idlist")
    List<OrganizationDTO> queryOrgNameByIdList(List<String> guidList);

    /**
     * 根据传入的组织guid集合查询根节点到当前节点的包含上下级的树形结构
     *
     * @param guidList
     * @return Key是组织GUID，Value是从root节点到当前节点的OrgDTO有序列表
     */
    @PostMapping("/organization/query_org_parent_list")
    Map<String, List<OrganizationDTO>> queryOrgWithParentByIdList(@RequestBody List<String> guidList);

    /**
     * 根据传入的guid集合查询门店：仅包含Guid、Name
     *
     * @param guidList
     * @return
     */
    @PostMapping("/store/query_store_by_idlist")
    List<StoreDTO> queryStoreByGuidList(@RequestBody List<String> guidList);

    @PostMapping("/store/query_store_by_idlist_and_brand")
    List<StoreDTO> queryStoreByGuidListAndBrandId(@RequestBody SingleDataDTO singleDataDTO);

    /**
     * 根据传入的guid集合查询门店：包含Guid、Name，且包含组织、品牌、区域信息
     *
     * @param guidList
     * @return
     */
    @PostMapping("/store/query_store_detail_by_idlist")
    List<StoreDTO> queryStoreDetailByGuidList(@RequestBody List<String> guidList);

    /**
     * 根据传入的guid集合查询门店：包含Guid、Name，且包含组织、品牌、区域信息
     */
    @PostMapping("/store/query_store_and_brand_by_id_list")
    List<StoreDTO> queryStoreAndBrandByIdList(@RequestBody List<String> guidList);

    /**
     * 根据传入的品牌guid集合查询品牌
     *
     * @param guidList
     * @return
     */
    @PostMapping("/brand/query_brand_by_idlist")
    List<BrandDTO> queryBrandByIdList(@RequestBody List<String> guidList);

    /**
     * 查询企业下所有品牌
     *
     * @return
     */
    @PostMapping("/brand/query_list")
    List<BrandDTO> queryBrandList();

    /**
     * 根据门店code查询门店信息
     *
     * @param storeCode storeCode
     * @return StoreDTO
     */
    @PostMapping("/store/query_store_by_code")
    StoreDTO queryStoreByCode(@RequestParam("storeCode") String storeCode);

    /**
     * 根据设备编号查询设备的绑定状态
     *
     * @param deviceNo 系统设备编号
     * @return 设备信息
     */
    @GetMapping("/device/find_device_status/{deviceNo}")
    StoreDeviceDTO findDeviceStatus(@PathVariable("deviceNo") String deviceNo);

    /**
     * 建立门店设备绑定关系
     *
     * @param storeDeviceDTO DTO
     * @return Integer
     */
    @PostMapping("/device/bind")
    void bindDeviceStatus(@RequestBody StoreDeviceDTO storeDeviceDTO);

    /**
     * 查询广义组织的子树Guid集合(包括自身)，扁平Guid列表
     *
     * @param genericOrgGuids
     * @return
     */
    @PostMapping("/organization/query_all_child_org")
    List<String> queryOrgChildren(@RequestBody List<String> genericOrgGuids);

    /**
     * 解析条件规则为门店Guid列表
     *
     * @param storeParserDTO
     * @return
     */
    @PostMapping("/store/parse_by_condition")
    List<String> parseByCondition(@RequestBody StoreParserDTO storeParserDTO);

    /**
     * 查询所有门店Guid
     *
     * @return
     */
    @PostMapping("/store/query_all_store_guid")
    List<String> queryAllStoreGuid();

    /**
     * 查询所有门店
     *
     * @return
     */
    @PostMapping("/store/query_all_store")
    List<StoreDTO> queryAllStore();

    @Slf4j
    @Component
    class ServiceClientFallback implements FallbackFactory<OrgFeignClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OrgFeignClient create(Throwable cause) {
            return new OrgFeignClient() {

                @PostMapping("/organization/query_erp_org_store")
                @Override
                public List<OrgGeneralDTO> queryErpOrgStore(Integer queryErp, Integer queryStore) {
                    log.error(HYSTRIX_PATTERN, "queryErpOrgStore",
                            "queryErp=" + queryErp + ", queryStore" + queryStore, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<OrgGeneralDTO> queryOrgByChildIdList(List<String> orgGuidList) {
                    log.error(HYSTRIX_PATTERN, "queryOrgByChildIdList",
                            JacksonUtils.writeValueAsString(orgGuidList), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<OrganizationDTO> queryOrgNameByIdList(List<String> guidList) {
                    log.error(HYSTRIX_PATTERN, "queryOrgNameByIdList",
                            JacksonUtils.writeValueAsString(guidList), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Map<String, List<OrganizationDTO>> queryOrgWithParentByIdList(List<String> guidList) {
                    log.error(HYSTRIX_PATTERN, "queryOrgWithParentByIdList",
                            JacksonUtils.writeValueAsString(guidList), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<StoreDTO> queryStoreByGuidList(List<String> guidList) {
                    log.error(HYSTRIX_PATTERN, "queryStoreByGuidList",
                            JacksonUtils.writeValueAsString(guidList), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<StoreDTO> queryStoreByGuidListAndBrandId(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "queryStoreByGuidListAndBrandId",
                            JacksonUtils.writeValueAsString(singleDataDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<StoreDTO> queryStoreDetailByGuidList(List<String> guidList) {
                    log.error(HYSTRIX_PATTERN, "queryStoreDetailByGuidList",
                            JacksonUtils.writeValueAsString(guidList), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<StoreDTO> queryStoreAndBrandByIdList(List<String> guidList) {
                    log.error(HYSTRIX_PATTERN, "queryStoreAndBrandByIdList",
                            JacksonUtils.writeValueAsString(guidList), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<BrandDTO> queryBrandByIdList(List<String> guidList) {
                    log.error(HYSTRIX_PATTERN, "queryBrandByIdList",
                            JacksonUtils.writeValueAsString(guidList), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<BrandDTO> queryBrandList() {
                    log.error(HYSTRIX_PATTERN, "queryBrandList",
                            "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public StoreDTO queryStoreByCode(String storeCode) {
                    log.error(HYSTRIX_PATTERN, "queryStoreByCode", "入参门店code为：" + storeCode, ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public StoreDeviceDTO findDeviceStatus(String deviceId) {
                    log.error(HYSTRIX_PATTERN, "findDeviceStatus", "设备id为：" + deviceId, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void bindDeviceStatus(StoreDeviceDTO storeDeviceDTO) {
                    log.error(HYSTRIX_PATTERN, "bindDeviceStatus",
                            JacksonUtils.writeValueAsString(storeDeviceDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<String> queryOrgChildren(List<String> genericOrgGuids) {
                    log.error(HYSTRIX_PATTERN, "queryOrgChildren",
                            JacksonUtils.writeValueAsString(genericOrgGuids), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<String> parseByCondition(StoreParserDTO storeParserDTO) {
                    log.error(HYSTRIX_PATTERN, "parseByCondition",
                            JacksonUtils.writeValueAsString(storeParserDTO), ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<String> queryAllStoreGuid() {
                    log.error(HYSTRIX_PATTERN, "queryAllStoreGuid",
                            "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<StoreDTO> queryAllStore() {
                    log.error(HYSTRIX_PATTERN, "queryAllStore",
                            "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
