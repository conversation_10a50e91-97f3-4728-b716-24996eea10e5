package com.holderzone.saas.store.staff.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.organization.*;
import com.holderzone.saas.store.dto.user.*;
import com.holderzone.saas.store.staff.entity.constant.Constants;
import com.holderzone.saas.store.staff.entity.domain.RoleDO;
import com.holderzone.saas.store.staff.entity.domain.UserDO;
import com.holderzone.saas.store.staff.entity.domain.UserDataDO;
import com.holderzone.saas.store.staff.entity.read.UserReadDO;
import com.holderzone.saas.store.staff.mapper.RoleMapper;
import com.holderzone.saas.store.staff.mapper.UserDataMapper;
import com.holderzone.saas.store.staff.mapper.UserMapper;
import com.holderzone.saas.store.staff.mapstruct.UserMapstruct;
import com.holderzone.saas.store.staff.service.DistributedService;
import com.holderzone.saas.store.staff.service.UserDataService;
import com.holderzone.saas.store.staff.service.remote.OrgFeignClient;
import com.holderzone.saas.store.staff.service.remote.UserClient;
import com.holderzone.saas.store.staff.utils.TreeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@SuppressWarnings("Duplicates")
public class UserDataServiceImpl extends ServiceImpl<UserDataMapper, UserDataDO> implements UserDataService {

    private final UserMapper userMapper;

    private final RoleMapper roleMapper;

    private final OrgFeignClient orgFeignClient;

    private final DistributedService distributedService;

    private final UserDataMapper userDataMapper;

    private final UserClient userClient;

    @Autowired
    public UserDataServiceImpl(UserMapper userMapper, RoleMapper roleMapper,
                               OrgFeignClient orgFeignClient, DistributedService distributedService,
                               UserDataMapper userDataMapper, UserClient userClient) {
        this.userMapper = userMapper;
        this.roleMapper = roleMapper;
        this.orgFeignClient = orgFeignClient;
        this.distributedService = distributedService;
        this.userDataMapper = userDataMapper;
        this.userClient = userClient;
    }

    @Override
    public void save(UserDTO userDTO) {
        String userGuid = userDTO.getGuid();
        if (userGuid.equals(UserContextUtils.getUserGuid()) &&
                Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            throw new BusinessException("管理员数据权限不允许修改");
        }
        // 保存门店、组织、品牌、区域规则
        List<UserDataDO> toBatchSave = new ArrayList<>();
        int lengthOfBatchGuid = (Objects.nonNull(userDTO.getUserDataStoreRule()) ? 1 : 0)
                + Optional.ofNullable(userDTO.getUserDataCondRuleList()).map(List::size).orElse(0);
        List<String> batchGuids = distributedService.nextBatchUserDataGuid(lengthOfBatchGuid);
        if (Objects.nonNull(userDTO.getUserDataStoreRule())) {
            String ruleGuid = batchGuids.remove(batchGuids.size() - 1);
            UserDataStoreRuleDTO userDataStoreRule = userDTO.getUserDataStoreRule();
            if (!CollectionUtils.isEmpty(userDataStoreRule.getUserStoreData())) {
                toBatchSave.addAll(userDataStoreRule.getUserStoreData().stream()
                        .map(userDataDTO -> {
                            UserDataDO userDataDO = new UserDataDO();
                            userDataDO.setUserGuid(userGuid);
                            userDataDO.setRuleGuid(ruleGuid);
                            userDataDO.setStoreGuid(userDataDTO.getGuid());
                            return userDataDO;
                        })
                        .collect(Collectors.toList()));
            }
        }
        if (!CollectionUtils.isEmpty(userDTO.getUserDataCondRuleList())) {
            for (UserDataCondRuleDTO userDataCondRule : userDTO.getUserDataCondRuleList()) {
                String ruleGuid = batchGuids.remove(batchGuids.size() - 1);
                if (CollectionUtils.isEmpty(userDataCondRule.getUserOrgData())
                        && CollectionUtils.isEmpty(userDataCondRule.getUserRegionData())
                        && CollectionUtils.isEmpty(userDataCondRule.getUserBrandData())) {
                    // 三者都是“全部”，写入一条空数据
                    toBatchSave.add(new UserDataDO().setUserGuid(userGuid).setRuleGuid(ruleGuid));
                    continue;
                }
                if (!CollectionUtils.isEmpty(userDataCondRule.getUserOrgData())) {
                    toBatchSave.addAll(userDataCondRule.getUserOrgData().stream()
                            .map(userDataDTO -> {
                                UserDataDO userDataDO = new UserDataDO();
                                userDataDO.setUserGuid(userGuid);
                                userDataDO.setRuleGuid(ruleGuid);
                                userDataDO.setOrganizationGuid(userDataDTO.getGuid());
                                return userDataDO;
                            })
                            .collect(Collectors.toList()));
                }
                if (!CollectionUtils.isEmpty(userDataCondRule.getUserRegionData())) {
                    toBatchSave.addAll(userDataCondRule.getUserRegionData().stream()
                            .map(userDataDTO -> {
                                UserDataDO userDataDO = new UserDataDO();
                                userDataDO.setUserGuid(userGuid);
                                userDataDO.setRuleGuid(ruleGuid);
                                userDataDO.setRegionCode(userDataDTO.getAdcode());
                                userDataDO.setRegionName(userDataDTO.getName());
                                return userDataDO;
                            })
                            .collect(Collectors.toList()));
                }
                if (!CollectionUtils.isEmpty(userDataCondRule.getUserBrandData())) {
                    toBatchSave.addAll(userDataCondRule.getUserBrandData().stream()
                            .map(userDataDTO -> {
                                UserDataDO userDataDO = new UserDataDO();
                                userDataDO.setUserGuid(userGuid);
                                userDataDO.setRuleGuid(ruleGuid);
                                userDataDO.setBrandGuid(userDataDTO.getGuid());
                                return userDataDO;
                            })
                            .collect(Collectors.toList()));
                }
            }
        }
        if (!CollectionUtils.isEmpty(toBatchSave)) saveBatch(toBatchSave);
        // 保存整单折扣、整单让价、可分配角色
        UserDO userDO = new UserDO();
        userDO.setDiscountThreshold(userDTO.getDiscountThreshold());
        userDO.setAllowanceThreshold(userDTO.getAllowanceThreshold());
        userDO.setProductDiscountThreshold(userDTO.getProductDiscountThreshold());
        userDO.setRefundThreshold(userDTO.getRefundThreshold());
        List<UserRoleDistDTO> userRolesDistributable = userDTO.getUserRolesDistributable();
        if (!CollectionUtils.isEmpty(userRolesDistributable)) {
            userDO.setRolesDistributable(userRolesDistributable.stream()
                    .filter(o -> o.getIsChecked() != null ? o.getIsChecked() : true).map(UserRoleDistDTO::getGuid)
                    .collect(Collectors.joining(",")));
        }
        userMapper.update(userDO, new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userGuid));
    }

    @Override
    public void appendRole(String roleGuid) {
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            return;
        }
        UserDO userInSql = userMapper.selectOne(new LambdaQueryWrapper<UserDO>()
                .select(UserDO::getGuid, UserDO::getRolesDistributable)
                .eq(UserDO::getGuid, UserContextUtils.getUserGuid()));
        String rolesDistributable = userInSql.getRolesDistributable();
        if (!StringUtils.hasText(rolesDistributable)) {
            userInSql.setRolesDistributable(roleGuid);
        } else {
            userInSql.setRolesDistributable(rolesDistributable + "," + roleGuid);
        }
        userMapper.update(userInSql, new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userInSql.getGuid()));
    }

    @Override
    public void updateNewStore(String storeGuid) {
        UserContext userContext = UserContextUtils.get();
        if (ObjectUtils.isEmpty(userContext) || Objects.equals(Constants.Account.ADMIN, userContext.getAccount()))
            return;
        String userGuid = userContext.getUserGuid();
        if (!StringUtils.hasText(userGuid)) {
            log.info("企业[{}]于云端创建了门店[{}]", userContext.getEnterpriseGuid(), storeGuid);
            return;
        }
        List<UserDataDO> userDataDOS = userDataMapper.selectList(new LambdaQueryWrapper<UserDataDO>()
                .eq(UserDataDO::getUserGuid, userGuid));
        String ruleGuid;
        if (!CollectionUtils.isEmpty(userDataDOS)) {
            ruleGuid = userDataDOS.get(0).getRuleGuid();
        } else {
            List<String> batchGuidList = distributedService.nextBatchUserDataGuid(2);
            ruleGuid = batchGuidList.remove(batchGuidList.size() - 1);
        }
        userDataMapper.insert(UserDataDO.builder().ruleGuid(ruleGuid).userGuid(userGuid).storeGuid(storeGuid).build());
    }

    @Override
    public UserDTO query(UserDTO userDTO) {
        UserDTO userResult = new UserDTO();
        // 管理员特殊处理
        if (userDTO.getGuid().equals(UserContextUtils.getUserGuid()) &&
                Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            userResult.setUserDataStoreRule(null);
            userResult.setUserDataCondRuleList(Collections.singletonList(UserDataCondRuleDTO.empty()));
            userResult.setDiscountThreshold(BigDecimal.valueOf(0.00));
            userResult.setAllowanceThreshold(BigDecimal.valueOf(Long.MAX_VALUE));
            userResult.setProductDiscountThreshold(BigDecimal.valueOf(0.00));
            userResult.setRefundThreshold(BigDecimal.valueOf(Long.MAX_VALUE));
            List<RoleDO> arrayOfRoleInErp = roleMapper.selectList(new LambdaQueryWrapper<RoleDO>()
                    .select(RoleDO::getGuid, RoleDO::getName));
            userResult.setUserRolesDistributable(arrayOfRoleInErp.stream()
                    .map(roleDO -> UserRoleDistDTO.checked(roleDO.getGuid(), roleDO.getName()))
                    .collect(Collectors.toList()));
            return userResult;
        }
        List<UserDataDO> userDataInSql = list(new LambdaQueryWrapper<UserDataDO>()
                .eq(UserDataDO::getUserGuid, userDTO.getGuid()));

        // 已配置数据权限时返回所有权限数据
        List<UserDataDO> arrayOfStoreData = userDataInSql.stream()
                .filter(userDataDO -> StringUtils.hasText(userDataDO.getStoreGuid()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(arrayOfStoreData)) {
            List<StoreDTO> arrayOfStore = orgFeignClient.queryStoreByGuidList(
                    arrayOfStoreData.stream().map(UserDataDO::getStoreGuid).collect(Collectors.toList())
            );
            UserDataStoreRuleDTO userDataStoreRule = new UserDataStoreRuleDTO()
                    .setUserStoreData(arrayOfStore.stream()
                            .map(store -> {
                                UserDataDTO userDataDTO = new UserDataDTO();
                                userDataDTO.setGuid(store.getGuid());
                                userDataDTO.setName(store.getName());
                                userDataDTO.setIsDeleted(store.getIsDeleted());
                                return userDataDTO;
                            })
                            .collect(Collectors.toList()));
            userResult.setUserDataStoreRule(userDataStoreRule);
        }
        // 条件规则
        List<UserDataDO> arrayOfCondData = userDataInSql.stream()
                .filter(userDataDO -> !StringUtils.hasText(userDataDO.getStoreGuid()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(arrayOfCondData)) {
            List<UserDataCondRuleDTO> userDataCondRules = arrayOfCondData.stream()
                    .collect(Collectors.groupingBy(UserDataDO::getRuleGuid))
                    .values().stream()
                    .map(arrayOfUserDataOfThisRule -> {
                        UserDataCondRuleDTO userDataCondRuleDTO = new UserDataCondRuleDTO();
                        // 组织、品牌、区域分离
                        List<UserDataDO> arrayOfOrgData = new ArrayList<>();
                        List<UserDataDO> arrayOfBrandData = new ArrayList<>();
                        List<UserDataDO> arrayOfRegionData = new ArrayList<>();
                        for (UserDataDO userDataDO : arrayOfUserDataOfThisRule) {
                            if (StringUtils.hasText(userDataDO.getOrganizationGuid())) {
                                arrayOfOrgData.add(userDataDO);
                            } else if (StringUtils.hasText(userDataDO.getBrandGuid())) {
                                arrayOfBrandData.add(userDataDO);
                            } else {
                                arrayOfRegionData.add(userDataDO);
                            }
                        }
                        // 组织填充
                        if (CollectionUtils.isEmpty(arrayOfOrgData)) {
                            userDataCondRuleDTO.setUserOrgData(Collections.emptyList());
                        } else {
                            List<OrganizationDTO> arrayOfOrg = orgFeignClient.queryOrgNameByIdList(arrayOfOrgData.stream()
                                    .map(UserDataDO::getOrganizationGuid).collect(Collectors.toList())
                            );
                            userDataCondRuleDTO.setUserOrgData(arrayOfOrg.stream()
                                    .map(organization -> {
                                        UserDataDTO userDataDTO = new UserDataDTO();
                                        userDataDTO.setGuid(organization.getGuid());
                                        userDataDTO.setName(organization.getName());
                                        userDataDTO.setIsDeleted(organization.getIsDeleted());
                                        return userDataDTO;
                                    })
                                    .collect(Collectors.toList()));
                        }
                        // 品牌填充
                        if (CollectionUtils.isEmpty(arrayOfBrandData)) {
                            userDataCondRuleDTO.setUserBrandData(Collections.emptyList());
                        } else {
                            List<BrandDTO> arrayOfBrand = orgFeignClient.queryBrandByIdList(arrayOfBrandData.stream()
                                    .map(UserDataDO::getBrandGuid).collect(Collectors.toList())
                            );
                            userDataCondRuleDTO.setUserBrandData(arrayOfBrand.stream()
                                    .map(brand -> {
                                        UserDataDTO userDataDTO = new UserDataDTO();
                                        userDataDTO.setGuid(brand.getGuid());
                                        userDataDTO.setName(brand.getName());
                                        userDataDTO.setIsDeleted(brand.getIsDeleted());
                                        return userDataDTO;
                                    })
                                    .collect(Collectors.toList()));
                        }
                        // 区域填充
                        if (CollectionUtils.isEmpty(arrayOfRegionData)) {
                            userDataCondRuleDTO.setUserRegionData(Collections.emptyList());
                        } else {
                            userDataCondRuleDTO.setUserRegionData(arrayOfRegionData.stream()
                                    .map(region -> {
                                        UserRegionDTO userRegionDTO = new UserRegionDTO();
                                        userRegionDTO.setAdcode(region.getRegionCode());
                                        userRegionDTO.setName(region.getRegionName());
                                        userRegionDTO.setIsDeleted(false);
                                        return userRegionDTO;
                                    })
                                    .collect(Collectors.toList()));
                        }

                        return userDataCondRuleDTO;
                    })
                    .collect(Collectors.toList());
            userResult.setUserDataCondRuleList(userDataCondRules);
        }
        // 整单折扣、整单让价、可分配角色
        UserDO userInSql = userMapper.selectOne(new LambdaQueryWrapper<UserDO>()
                .select(UserDO::getGuid,
                        UserDO::getDiscountThreshold,
                        UserDO::getAllowanceThreshold,
                        UserDO::getRolesDistributable,
                        UserDO::getProductDiscountThreshold,
                        UserDO::getRefundThreshold)
                .eq(UserDO::getGuid, userDTO.getGuid()));
        List<String> arrayOfRoleGuidChecked;
        if (userInSql != null) {
            userResult.setDiscountThreshold(userInSql.getDiscountThreshold());
            userResult.setAllowanceThreshold(userInSql.getAllowanceThreshold());
            userResult.setProductDiscountThreshold(userInSql.getProductDiscountThreshold());
            userResult.setRefundThreshold(userInSql.getRefundThreshold());
            arrayOfRoleGuidChecked = Arrays.asList(
                    Optional.ofNullable(userInSql.getRolesDistributable()).orElse("").split(",")
            );
        } else {
            userResult.setDiscountThreshold(new BigDecimal("10.00"));
            userResult.setAllowanceThreshold(new BigDecimal("0.00"));
            userResult.setProductDiscountThreshold(new BigDecimal("10.00"));
            userResult.setRefundThreshold(new BigDecimal("0.00"));
            arrayOfRoleGuidChecked = Arrays.asList("");
        }

        List<RoleDO> arrayOfRoleInErp = roleMapper.selectList(new LambdaQueryWrapper<RoleDO>()
                .select(RoleDO::getGuid, RoleDO::getName));
        // todo 如果角色被删除了，那么已分配的用户可管理角色怎么办
        userResult.setUserRolesDistributable(arrayOfRoleInErp.stream()
                .map(roleDO -> {
                    boolean isChecked = arrayOfRoleGuidChecked.contains(roleDO.getGuid());
                    return new UserRoleDistDTO(roleDO.getGuid(), roleDO.getName(), isChecked);
                })
                .collect(Collectors.toList()));

        return userResult;
    }

    @Override
    public void update(UserDTO userDTO) {
        deleteUserDataRules(userDTO.getGuid());
        save(userDTO);
    }

    @Override
    public void deleteUserDataRules(String userGuid) {
        remove(new LambdaQueryWrapper<UserDataDO>().eq(UserDataDO::getUserGuid, userGuid));
    }

    @Override
    public UserDTO queryUserRolesDistributable() {
        // 管理员默认可分配所有角色
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            List<RoleDO> arrayOfRoleInErp = roleMapper.selectList(new LambdaQueryWrapper<RoleDO>()
                    .select(RoleDO::getGuid, RoleDO::getName));
            return new UserDTO().setUserRolesDistributable(arrayOfRoleInErp.stream()
                    .map(p -> UserRoleDistDTO.checkedIgnore(p.getGuid(), p.getName()))
                    .collect(Collectors.toList()));
        }
        UserDO userInSql = userMapper.selectOne(new LambdaQueryWrapper<UserDO>()
                .select(UserDO::getRolesDistributable)
                .eq(UserDO::getGuid, UserContextUtils.getUserGuid()));
        if (userInSql == null) {
            return new UserDTO().setUserRolesDistributable(Collections.emptyList());
        }
        String rolesDistributable = userInSql.getRolesDistributable();
        if (!StringUtils.hasText(rolesDistributable)) {
            return new UserDTO().setUserRolesDistributable(Collections.emptyList());
        }
        List<String> arrayOfRoleGuidChecked = Arrays.asList(rolesDistributable.split(","));
        List<RoleDO> arrayOfRoleChecked = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(arrayOfRoleGuidChecked)) {
            arrayOfRoleChecked = roleMapper.selectList(new LambdaQueryWrapper<RoleDO>()
                    .in(RoleDO::getGuid, arrayOfRoleGuidChecked));
        }
        return new UserDTO().setUserRolesDistributable(arrayOfRoleChecked.stream()
                .map(roleDO -> UserRoleDistDTO.checkedIgnore(roleDO.getGuid(), roleDO.getName()))
                .collect(Collectors.toList()));
    }

    @Override
    public UserDTO queryUserDataThreshold() {
        // 管理员默认可支持最大阈值
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            return new UserDTO().setDiscountThreshold(BigDecimal.valueOf(0.00))
                    .setAllowanceThreshold(BigDecimal.valueOf(Long.MAX_VALUE))
                    .setProductDiscountThreshold(BigDecimal.ZERO)
                    .setRefundThreshold(BigDecimal.valueOf(Long.MAX_VALUE));
        }
        UserDO userInSql = userMapper.selectOne(new LambdaQueryWrapper<UserDO>()
                .select(UserDO::getGuid, UserDO::getDiscountThreshold, UserDO::getAllowanceThreshold, UserDO::getProductDiscountThreshold, UserDO::getRefundThreshold)
                .eq(UserDO::getGuid, UserContextUtils.getUserGuid()));
        if (userInSql.getDiscountThreshold() == null) {
            userInSql.setDiscountThreshold(BigDecimal.valueOf(10.00));
        }
        if (userInSql.getAllowanceThreshold() == null) {
            userInSql.setAllowanceThreshold(BigDecimal.ZERO);
        }
        if (userInSql.getProductDiscountThreshold() == null) {
            userInSql.setProductDiscountThreshold(BigDecimal.valueOf(10.00));
        }
        if (userInSql.getRefundThreshold() == null) {
            userInSql.setRefundThreshold(BigDecimal.ZERO);
        }
        UserDTO userDTO = new UserDTO().setDiscountThreshold(userInSql.getDiscountThreshold())
                .setAllowanceThreshold(userInSql.getAllowanceThreshold())
                .setProductDiscountThreshold(userInSql.getProductDiscountThreshold())
                .setRefundThreshold(userInSql.getRefundThreshold());
        log.info("用户阈值：{}", JacksonUtils.writeValueAsString(userDTO));
        return userDTO;
    }

    @Override
    public UserSpinnerDTO queryStoreMergedSpinner() {
        List<String> arrayOfStoreGuid = queryAllStoreGuid();
        List<StoreDTO> arrayOfStoreDTO = CollectionUtils.isEmpty(arrayOfStoreGuid)
                ? Collections.emptyList() : orgFeignClient.queryStoreAndBrandByIdList(arrayOfStoreGuid);
        return UserSpinnerDTO.empty().setArrayOfStoreDTO(arrayOfStoreDTO);
    }

    @Override
    public UserSpinnerDTO queryStoreMergedSpinnerByBrandGuid(SingleDataDTO singleDataDTO) {
        List<String> arrayOfStoreGuid = queryAllStoreGuid();
        if (ObjectUtils.isEmpty(singleDataDTO))
            singleDataDTO = new SingleDataDTO();
        singleDataDTO.setDatas(arrayOfStoreGuid);
        List<StoreDTO> arrayOfStoreDTO = CollectionUtils.isEmpty(arrayOfStoreGuid)
                ? Collections.emptyList() : orgFeignClient.queryStoreByGuidListAndBrandId(singleDataDTO);
        return UserSpinnerDTO.empty().setArrayOfStoreDTO(arrayOfStoreDTO);
    }

    @Override
    public UserSpinnerDTO queryOrgMergedSpinner() {
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            List<OrgGeneralDTO> arrayOfOrgDTO = collectOrgFromRule(null, RuleCondBO.selected());
            return UserSpinnerDTO.empty().setArrayOfOrgDTO(arrayOfOrgDTO);
        }

        List<UserDataDO> userDataInSql = list(new LambdaQueryWrapper<UserDataDO>()
                .eq(UserDataDO::getUserGuid, UserContextUtils.getUserGuid()));
        if (CollectionUtils.isEmpty(userDataInSql)) return UserSpinnerDTO.empty();

        // 门店规则元数据，提取条件
        List<String> storeGuidOfRuleStore = userDataInSql.stream()
                .filter(userDataDO -> StringUtils.hasText(userDataDO.getStoreGuid()))
                .map(UserDataDO::getStoreGuid).collect(Collectors.toList());
        List<String> orgGuidOfRuleStore = new ArrayList<>();
        extractRuleCondFromRuleStore(storeGuidOfRuleStore,
                orgGuidOfRuleStore, null, null);

        // 条件规则元数据，提取门店
        Map<String, List<UserDataDO>> userDataOfRuleCond = userDataInSql.stream()
                .filter(userDataDO -> !StringUtils.hasText(userDataDO.getStoreGuid()))
                .collect(Collectors.groupingBy(UserDataDO::getRuleGuid));
        RuleCondBO<String> orgGuidOfRuleCond = new RuleCondBO<>();
        extractRuleStoreFromRuleCond(userDataOfRuleCond, null,
                orgGuidOfRuleCond, null, null);

        // 归并条件
        List<OrgGeneralDTO> arrayOfOrgDTO = collectOrgFromRule(orgGuidOfRuleStore, orgGuidOfRuleCond);

        return UserSpinnerDTO.empty().setArrayOfOrgDTO(arrayOfOrgDTO);
    }

    @Override
    public UserSpinnerDTO queryBrandMergedSpinner() {
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            List<BrandDTO> arrayOfBrandDTO = collectBrandFromRule(null, RuleCondBO.selected());
            return UserSpinnerDTO.empty().setArrayOfBrandDTO(arrayOfBrandDTO);
        }

        List<UserDataDO> userDataInSql = list(new LambdaQueryWrapper<UserDataDO>()
                .eq(UserDataDO::getUserGuid, UserContextUtils.getUserGuid()));
        if (CollectionUtils.isEmpty(userDataInSql)) return UserSpinnerDTO.empty();

        // 门店规则元数据，提取条件
        List<String> storeGuidOfRuleStore = userDataInSql.stream()
                .filter(userDataDO -> StringUtils.hasText(userDataDO.getStoreGuid()))
                .map(UserDataDO::getStoreGuid).collect(Collectors.toList());
        List<String> brandGuidOfRuleStore = new ArrayList<>();
        extractRuleCondFromRuleStore(storeGuidOfRuleStore,
                null, brandGuidOfRuleStore, null);

        // 条件规则元数据，提取门店
        Map<String, List<UserDataDO>> userDataOfRuleCond = userDataInSql.stream()
                .filter(userDataDO -> !StringUtils.hasText(userDataDO.getStoreGuid()))
                .collect(Collectors.groupingBy(UserDataDO::getRuleGuid));
        RuleCondBO<String> brandGuidOfRuleCond = new RuleCondBO<>();
        extractRuleStoreFromRuleCond(userDataOfRuleCond, null,
                null, brandGuidOfRuleCond, null);

        // 归并条件
        List<BrandDTO> arrayOfBrandDTO = collectBrandFromRule(brandGuidOfRuleStore, brandGuidOfRuleCond);

        return UserSpinnerDTO.empty().setArrayOfBrandDTO(arrayOfBrandDTO);
    }

    @Override
    public UserSpinnerDTO queryCondMergedSpinner() {
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            List<OrgGeneralDTO> arrayOfOrgDTO = collectOrgFromRule(null, RuleCondBO.selected());
            List<BrandDTO> arrayOfBrandDTO = collectBrandFromRule(null, RuleCondBO.selected());
            List<RegionDTO> arrayOfRegionDTO = collectRegionFromRule(null, RuleCondBO.selected());
            return new UserSpinnerDTO(arrayOfOrgDTO, arrayOfBrandDTO, arrayOfRegionDTO);
        }

        List<UserDataDO> userDataInSql = list(new LambdaQueryWrapper<UserDataDO>()
                .eq(UserDataDO::getUserGuid, UserContextUtils.getUserGuid()));
        if (CollectionUtils.isEmpty(userDataInSql)) return UserSpinnerDTO.empty();

        // 门店规则元数据、提取条件
        List<String> storeGuidOfRuleStore = userDataInSql.stream()
                .filter(userDataDO -> StringUtils.hasText(userDataDO.getStoreGuid()))
                .map(UserDataDO::getStoreGuid).collect(Collectors.toList());
        List<String> orgGuidOfRuleStore = new ArrayList<>();
        List<String> brandGuidOfRuleStore = new ArrayList<>();
        List<UserDataDO> regionDataOfRuleStore = new ArrayList<>();
        extractRuleCondFromRuleStore(storeGuidOfRuleStore, orgGuidOfRuleStore, brandGuidOfRuleStore, regionDataOfRuleStore);

        // 条件规则元数据、提取门店
        Map<String, List<UserDataDO>> userDataOfRuleCond = userDataInSql.stream()
                .filter(userDataDO -> !StringUtils.hasText(userDataDO.getStoreGuid()))
                .collect(Collectors.groupingBy(UserDataDO::getRuleGuid));
        RuleCondBO<String> orgGuidOfRuleCond = new RuleCondBO<>();
        RuleCondBO<String> brandGuidOfRuleCond = new RuleCondBO<>();
        RuleCondBO<UserDataDO> regionDataOfRuleCond = new RuleCondBO<>();
        extractRuleStoreFromRuleCond(userDataOfRuleCond, null,
                orgGuidOfRuleCond, brandGuidOfRuleCond, regionDataOfRuleCond);

        // 归并条件
        List<OrgGeneralDTO> arrayOfOrgDTO = collectOrgFromRule(orgGuidOfRuleStore, orgGuidOfRuleCond);
        List<BrandDTO> arrayOfBrandDTO = collectBrandFromRule(brandGuidOfRuleStore, brandGuidOfRuleCond);
        List<RegionDTO> arrayOfRegionDTO = collectRegionFromRule(regionDataOfRuleStore, regionDataOfRuleCond);

        return new UserSpinnerDTO(arrayOfOrgDTO, arrayOfBrandDTO, arrayOfRegionDTO);
    }

    @Override
    public UserSpinnerDTO queryStoreAndCondSpinner() {
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            List<StoreDTO> arrayOfStoreDTO = collectStoreFromRule(null, RuleCondBO.selected());
            List<OrgGeneralDTO> arrayOfOrgDTO = collectOrgFromRule(null, RuleCondBO.selected());
            List<BrandDTO> arrayOfBrandDTO = collectBrandFromRule(null, RuleCondBO.selected());
            List<RegionDTO> arrayOfRegionDTO = collectRegionFromRule(arrayOfStoreDTO.stream()
                            .filter(storeDTO -> StringUtils.hasText(storeDTO.getProvinceCode())
                                    && StringUtils.hasText(storeDTO.getProvinceName())
                                    && StringUtils.hasText(storeDTO.getCityCode())
                                    && StringUtils.hasText(storeDTO.getCityName())
                                    && StringUtils.hasText(storeDTO.getCountyCode())
                                    && StringUtils.hasText(storeDTO.getCountyName()))
                            .map(it -> {
                                String regionCode = it.getCountyCode() + "," + it.getCityCode() + "," + it.getProvinceCode();
                                String regionName = it.getCountyName() + "," + it.getCityName() + "," + it.getProvinceName();
                                return new UserDataDO().setRegionCode(regionCode).setRegionName(regionName);
                            }).collect(Collectors.toList())
                    , RuleCondBO.selected());
            return new UserSpinnerDTO(arrayOfStoreDTO, arrayOfOrgDTO, arrayOfBrandDTO, arrayOfRegionDTO);
        }

        List<UserDataDO> userDataInSql = list(new LambdaQueryWrapper<UserDataDO>()
                .eq(UserDataDO::getUserGuid, UserContextUtils.getUserGuid()));
        if (CollectionUtils.isEmpty(userDataInSql)) return UserSpinnerDTO.empty();

        // 门店规则元数据、提取条件
        List<String> storeGuidOfRuleStore = userDataInSql.stream()
                .filter(userDataDO -> StringUtils.hasText(userDataDO.getStoreGuid()))
                .map(UserDataDO::getStoreGuid).collect(Collectors.toList());
        List<String> orgGuidOfRuleStore = new ArrayList<>();
        List<String> brandGuidOfRuleStore = new ArrayList<>();
        List<UserDataDO> regionDataOfRuleStore = new ArrayList<>();
        extractRuleCondFromRuleStore(storeGuidOfRuleStore,
                orgGuidOfRuleStore, brandGuidOfRuleStore, regionDataOfRuleStore);

        // 条件规则元数据、提取门店
        Map<String, List<UserDataDO>> userDataOfRuleCond = userDataInSql.stream()
                .filter(userDataDO -> !StringUtils.hasText(userDataDO.getStoreGuid()))
                .collect(Collectors.groupingBy(UserDataDO::getRuleGuid));
        RuleCondBO<String> storeGuidOfRuleCond = new RuleCondBO<>();
        RuleCondBO<String> orgGuidOfRuleCond = new RuleCondBO<>();
        RuleCondBO<String> brandGuidOfRuleCond = new RuleCondBO<>();
        RuleCondBO<UserDataDO> regionDataOfRuleCond = new RuleCondBO<>();
        extractRuleStoreFromRuleCond(userDataOfRuleCond, storeGuidOfRuleCond,
                orgGuidOfRuleCond, brandGuidOfRuleCond, regionDataOfRuleCond);

        // 归并条件
        List<StoreDTO> arrayOfStoreDTO = collectStoreFromRule(storeGuidOfRuleStore, storeGuidOfRuleCond);
        List<OrgGeneralDTO> arrayOfOrgDTO = collectOrgFromRule(orgGuidOfRuleStore, orgGuidOfRuleCond);
        List<BrandDTO> arrayOfBrandDTO = collectBrandFromRule(brandGuidOfRuleStore, brandGuidOfRuleCond);
        List<RegionDTO> arrayOfRegionDTO = collectRegionFromRule(regionDataOfRuleStore, regionDataOfRuleCond);

        return new UserSpinnerDTO(arrayOfStoreDTO, arrayOfOrgDTO, arrayOfBrandDTO, arrayOfRegionDTO);
    }

    @Override
    public UserSpinnerDTO queryGenericOrgSpinner() {
        return new UserSpinnerDTO().setArrayOfOrgDTO(orgFeignClient.queryErpOrgStore(1, 1));
    }

    @Override
    public List<String> queryAllStoreGuid() {
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            return orgFeignClient.queryAllStoreGuid();
        }
        List<UserDataDO> userDataInSql = list(new LambdaQueryWrapper<UserDataDO>()
                .eq(UserDataDO::getUserGuid, UserContextUtils.getUserGuid()));
        if (CollectionUtils.isEmpty(userDataInSql)) return Collections.emptyList();

        // 门店规则元数据
        Set<String> storeGuidOfRuleStore = userDataInSql.stream()
                .filter(userDataDO -> StringUtils.hasText(userDataDO.getStoreGuid()))
                .map(UserDataDO::getStoreGuid).collect(Collectors.toSet());

        // 条件规则元数据、提取门店
        Map<String, List<UserDataDO>> userDataOfRuleCond = userDataInSql.stream()
                .filter(userDataDO -> !StringUtils.hasText(userDataDO.getStoreGuid()))
                .collect(Collectors.groupingBy(UserDataDO::getRuleGuid));
        Set<String> storeGuidOfRuleCond = new HashSet<>();
        if (!CollectionUtils.isEmpty(userDataOfRuleCond)) {
            for (List<UserDataDO> arrayOfUserDataDO : userDataOfRuleCond.values()) {
                List<String> orgGuidOfSingleRuleCond = new ArrayList<>();
                List<String> brandGuidOfSingleRuleCond = new ArrayList<>();
                List<UserDataDO> regionDataOfSingleRuleCond = new ArrayList<>();
                for (UserDataDO userDataDO : arrayOfUserDataDO) {
                    if (StringUtils.hasText(userDataDO.getOrganizationGuid())) {
                        orgGuidOfSingleRuleCond.add(userDataDO.getOrganizationGuid());
                    } else if (StringUtils.hasText(userDataDO.getBrandGuid())) {
                        brandGuidOfSingleRuleCond.add(userDataDO.getBrandGuid());
                    } else if (StringUtils.hasText(userDataDO.getRegionCode())) {
                        regionDataOfSingleRuleCond.add(userDataDO);
                    }
                }
                if (CollectionUtils.isEmpty(orgGuidOfSingleRuleCond)
                        && CollectionUtils.isEmpty(brandGuidOfSingleRuleCond)
                        && CollectionUtils.isEmpty(regionDataOfSingleRuleCond)) {
                    storeGuidOfRuleCond.addAll(orgFeignClient.queryAllStoreGuid());
                    break;
                } else {
                    StoreParserDTO storeParserDTO = new StoreParserDTO();
                    storeParserDTO.setOrganizationGuidList(orgGuidOfSingleRuleCond);
                    storeParserDTO.setBrandGuidList(brandGuidOfSingleRuleCond);
                    storeParserDTO.setRegionCodeList(regionDataOfSingleRuleCond.stream()
                            .map(it -> it.getRegionCode().substring(
                                    it.getRegionCode().lastIndexOf(",") + 1))
                            .collect(Collectors.toList()));
                    storeGuidOfRuleCond.addAll(orgFeignClient.parseByCondition(storeParserDTO));
                }
            }
        }

        // 归并门店
        Set<String> storeGuidOfRule = new HashSet<>();
        storeGuidOfRule.addAll(storeGuidOfRuleStore);
        storeGuidOfRule.addAll(storeGuidOfRuleCond);

        return new ArrayList<>(storeGuidOfRule);
    }

    @Override
    public List<LocalUserInfoDTO> queryAllUserToLocal(LocalUserReqDTO localUserReqDTO) {
        List<UserDO> allUsersInEnterprise = userMapper.selectList(new LambdaQueryWrapper<>());
        List<LocalUserInfoDTO> respList = Lists.newArrayList();
        allUsersInEnterprise.forEach(userDO -> {
            UserContextUtils.put(
                    JacksonUtils.writeValueAsString(
                            UserInfoDTO.builder()
                                    .enterpriseGuid(UserContextUtils.getEnterpriseGuid())
                                    .account(userDO.getAccount())
                                    .userGuid(userDO.getGuid())
                                    .userName(userDO.getName())
                                    .build()));
            UserSpinnerDTO userSpinnerDTO = queryStoreMergedSpinner();
            if (!CollectionUtils.isEmpty(userSpinnerDTO.getArrayOfStoreDTO()) &&
                    userSpinnerDTO.getArrayOfStoreDTO() != null &&
                    userSpinnerDTO.getArrayOfStoreDTO().stream()
                            .anyMatch(storeDTO -> Objects.equals(localUserReqDTO.getStoreGuid(), storeDTO.getGuid()))) {
//                List<MenuSourceDTO> sourceByUser = menuService.getSourceByUser(localUserReqDTO.getTerminalCode());
//                List<MenuSourceDTO> moduleSourceByUser = menuService.getModuleSourceByUser(localUserReqDTO.getTerminalCode());
                respList.add(LocalUserInfoDTO.builder()
                        .account(userDO.getAccount())
                        .allowanceThreshold(userDO.getAllowanceThreshold())
                        .discountThreshold(userDO.getDiscountThreshold())
                        .productDiscountThreshold(userDO.getProductDiscountThreshold())
                        .refundThreshold(userDO.getRefundThreshold())
                        .enterpriseNo(userDO.getEnterpriseNo())
                        .name(userDO.getName())
                        .password(userDO.getPassword())
                        .phone(userDO.getPhone())
                        .userGuid(userDO.getGuid())
//                        .menuSourceDTOList(sourceByUser)
//                        .moduleSourceDTOList(moduleSourceByUser)
                        .build());
            }
        });
        if (respList.stream().noneMatch(o -> Objects.equals(Constants.Account.ADMIN, o.getAccount()))) {
            String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
            if (!StringUtils.isEmpty(enterpriseGuid)) {
                com.holderzone.resource.common.dto.user.UserDTO adminByEnterpriseGuid = userClient.getAdminByEnterpriseGuid(enterpriseGuid);
                if (adminByEnterpriseGuid != null) {
                    LocalUserInfoDTO admin = LocalUserInfoDTO.builder()
                            .account(adminByEnterpriseGuid.getAccount())
                            .allowanceThreshold(BigDecimal.valueOf(Long.MAX_VALUE))
                            .discountThreshold(BigDecimal.ZERO)
                            .productDiscountThreshold(BigDecimal.ZERO)
                            .refundThreshold(BigDecimal.valueOf(Long.MAX_VALUE))
                            .enterpriseNo(adminByEnterpriseGuid.getEnterpriseGuid())
                            .name(adminByEnterpriseGuid.getName())
                            .password(adminByEnterpriseGuid.getPassword())
                            .phone(adminByEnterpriseGuid.getTel())
                            .userGuid(adminByEnterpriseGuid.getUserGuid())
                            .build();
                    respList.add(admin);
                }
            }
        }
        return respList;
    }

    private void extractRuleCondFromRuleStore(List<String> storeGuidOfRuleStore, List<String> orgGuidOfRuleStore,
                                              List<String> brandGuidOfRuleStore, List<UserDataDO> regionDataOfRuleStore) {
        if (!CollectionUtils.isEmpty(storeGuidOfRuleStore)) {
            List<StoreDTO> storeOfRuleStore = orgFeignClient.queryStoreDetailByGuidList(storeGuidOfRuleStore);
            if (null != orgGuidOfRuleStore) {
                String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
                orgGuidOfRuleStore.addAll(storeOfRuleStore.stream()
                        .map(StoreDTO::getParentId).filter(e -> !enterpriseGuid.equals(e)).collect(Collectors.toList()));
            }
            if (null != brandGuidOfRuleStore) {
                brandGuidOfRuleStore.addAll(storeOfRuleStore.stream()
                        .map(StoreDTO::getBelongBrandGuid).filter(StringUtils::hasText).collect(Collectors.toList()));
            }
            if (null != regionDataOfRuleStore) {
                regionDataOfRuleStore.addAll(storeOfRuleStore.stream()
                        .filter(storeDTO -> StringUtils.hasText(storeDTO.getProvinceCode())
                                && StringUtils.hasText(storeDTO.getProvinceName())
                                && StringUtils.hasText(storeDTO.getCityCode())
                                && StringUtils.hasText(storeDTO.getCityName())
                                && StringUtils.hasText(storeDTO.getCountyCode())
                                && StringUtils.hasText(storeDTO.getCountyName()))
                        .map(it -> {
                            String regionCode = it.getCountyCode() + "," + it.getCityCode() + "," + it.getProvinceCode();
                            String regionName = it.getCountyName() + "," + it.getCityName() + "," + it.getProvinceName();
                            return new UserDataDO().setRegionCode(regionCode).setRegionName(regionName);
                        })
                        .collect(Collectors.toList()));
            }
        }
    }

    private void extractRuleStoreFromRuleCond(Map<String, List<UserDataDO>> userDataOfRuleCond,
                                              RuleCondBO<String> storeGuidOfRuleCond, RuleCondBO<String> orgGuidOfRuleCond,
                                              RuleCondBO<String> brandGuidOfRuleCond, RuleCondBO<UserDataDO> regionDataOfRuleCond) {
        if (!CollectionUtils.isEmpty(userDataOfRuleCond)) {
            userDataOfRuleCond.forEach((ruleGuid, arrayOfUserDataDO) -> {
                List<String> orgGuidOfSingleRuleCond = new ArrayList<>();
                List<String> brandGuidOfSingleRuleCond = new ArrayList<>();
                List<UserDataDO> regionDataOfSingleRuleCond = new ArrayList<>();
                for (UserDataDO userDataDO : arrayOfUserDataDO) {
                    if (StringUtils.hasText(userDataDO.getOrganizationGuid())) {
                        orgGuidOfSingleRuleCond.add(userDataDO.getOrganizationGuid());
                    } else if (StringUtils.hasText(userDataDO.getBrandGuid())) {
                        brandGuidOfSingleRuleCond.add(userDataDO.getBrandGuid());
                    } else {
                        if (!StringUtils.isEmpty(userDataDO.getRegionCode())) {
                            regionDataOfSingleRuleCond.add(userDataDO);
                        }
                    }
                }
                if (null != orgGuidOfRuleCond && !orgGuidOfRuleCond.isAllSelected()) {
                    if (CollectionUtils.isEmpty(orgGuidOfSingleRuleCond)) {
                        orgGuidOfRuleCond.setAllSelected(true);
                    } else {
                        orgGuidOfRuleCond.getData().addAll(orgGuidOfSingleRuleCond);
                    }
                }
                if (null != brandGuidOfRuleCond && !brandGuidOfRuleCond.isAllSelected()) {
                    if (CollectionUtils.isEmpty(brandGuidOfSingleRuleCond)) {
                        brandGuidOfRuleCond.setAllSelected(true);
                    } else {
                        brandGuidOfRuleCond.getData().addAll(brandGuidOfSingleRuleCond);
                    }
                }
                if (null != regionDataOfRuleCond && !regionDataOfRuleCond.isAllSelected()) {
                    if (CollectionUtils.isEmpty(regionDataOfSingleRuleCond)) {
                        regionDataOfRuleCond.setAllSelected(true);
                    } else {
                        regionDataOfRuleCond.getData().addAll(regionDataOfSingleRuleCond);
                    }
                }
                if (null != storeGuidOfRuleCond && !storeGuidOfRuleCond.isAllSelected()) {
                    if (CollectionUtils.isEmpty(orgGuidOfSingleRuleCond)
                            && CollectionUtils.isEmpty(brandGuidOfSingleRuleCond)
                            && CollectionUtils.isEmpty(regionDataOfSingleRuleCond)) {
                        storeGuidOfRuleCond.setAllSelected(true);
                    } else {
                        StoreParserDTO storeParserDTO = new StoreParserDTO();
                        storeParserDTO.setOrganizationGuidList(orgGuidOfSingleRuleCond);
                        storeParserDTO.setBrandGuidList(brandGuidOfSingleRuleCond);
                        storeParserDTO.setRegionCodeList(regionDataOfSingleRuleCond.stream()
                                .map(it -> it.getRegionCode().substring(
                                        it.getRegionCode().lastIndexOf(",") + 1))
                                .collect(Collectors.toList()));
                        List<String> storeGuidOfSingleRuleCond = orgFeignClient.parseByCondition(storeParserDTO);
                        storeGuidOfRuleCond.getData().addAll(storeGuidOfSingleRuleCond);
                    }
                }
            });
        }
    }

    private List<StoreDTO> collectStoreFromRule(List<String> storeGuidOfRuleStore, RuleCondBO<String> storeGuidOfRuleCond) {
        List<StoreDTO> arrayOfStoreDTO;
        if (storeGuidOfRuleCond.isAllSelected()) {
            arrayOfStoreDTO = orgFeignClient.queryAllStore();
        } else {
            Set<String> storeGuidOfRule = new HashSet<>();
            storeGuidOfRule.addAll(storeGuidOfRuleStore);
            storeGuidOfRule.addAll(storeGuidOfRuleCond.getData());
            arrayOfStoreDTO = orgFeignClient.queryStoreByGuidList(new ArrayList<>(storeGuidOfRule));
        }
        return arrayOfStoreDTO;
    }

    private List<OrgGeneralDTO> collectOrgFromRule(List<String> orgGuidOfRuleStore, RuleCondBO<String> orgGuidOfRuleCond) {
        List<OrgGeneralDTO> arrayOfOrgDTO;
        if (orgGuidOfRuleCond.isAllSelected()) {
            arrayOfOrgDTO = orgFeignClient.queryErpOrgStore(1, 0);
        } else {
            Set<String> orgGuidOfRule = new HashSet<>();
            orgGuidOfRule.addAll(orgGuidOfRuleStore);
            orgGuidOfRule.addAll(orgGuidOfRuleCond.getData());
            arrayOfOrgDTO = orgFeignClient.queryOrgByChildIdList(new ArrayList<>(orgGuidOfRule));
        }
        return arrayOfOrgDTO;
    }

    private List<BrandDTO> collectBrandFromRule(List<String> brandGuidOfRuleStore, RuleCondBO<String> brandGuidOfRuleCond) {
        List<BrandDTO> arrayOfBrandDTO;
        if (brandGuidOfRuleCond.isAllSelected()) {
            arrayOfBrandDTO = orgFeignClient.queryBrandList();
        } else {
            Set<String> brandGuidOfRule = new HashSet<>();
            brandGuidOfRule.addAll(brandGuidOfRuleStore);
            brandGuidOfRule.addAll(brandGuidOfRuleCond.getData());
            arrayOfBrandDTO = orgFeignClient.queryBrandByIdList(new ArrayList<>(brandGuidOfRule));
        }
        return arrayOfBrandDTO;
    }

    private List<RegionDTO> collectRegionFromRule(List<UserDataDO> regionDataOfRuleStore, RuleCondBO<UserDataDO> regionDataOfRuleCond) {
        List<RegionDTO> arrayOfRegionDTO;
        if (regionDataOfRuleCond.isAllSelected()) {
            arrayOfRegionDTO = TreeUtils.collectRegionAsTree(new ArrayList<>(regionDataOfRuleStore));
        } else {
            Set<UserDataDO> regionCodeOfRule = new HashSet<>();
            regionCodeOfRule.addAll(regionDataOfRuleStore);
            regionCodeOfRule.addAll(regionDataOfRuleCond.getData());
            arrayOfRegionDTO = TreeUtils.collectRegionAsTree(new ArrayList<>(regionCodeOfRule));
        }
        return arrayOfRegionDTO;
    }

    @Override
    public List<UserDTO> queryAIOUsers(BaseDTO baseDTO) {
        if (StringUtils.isEmpty(baseDTO.getStoreGuid())) {
            return Lists.newArrayList();
        }
        List<UserReadDO> userDOS = userMapper.queryAIOUsers(baseDTO.getStoreGuid());
        List<UserDTO> userDTOS = UserMapstruct.INSTANCE.toUserDTO(userDOS);
        if (userDOS.stream().noneMatch(o -> Objects.equals(Constants.Account.ADMIN, o.getAccount()))) {
            // 查询默认管理员GUID
            String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
            if (!StringUtils.isEmpty(enterpriseGuid)) {
                com.holderzone.resource.common.dto.user.UserDTO adminByEnterpriseGuid = userClient.getAdminByEnterpriseGuid(enterpriseGuid);
                if (adminByEnterpriseGuid != null) {
                    UserDTO admin = new UserDTO();
                    admin.setGuid(adminByEnterpriseGuid.getUserGuid());
                    admin.setName(adminByEnterpriseGuid.getName());
                    admin.setAccount(adminByEnterpriseGuid.getAccount());
                    admin.setPassword(adminByEnterpriseGuid.getPassword());
                    userDTOS.add(admin);
                }
            }
        }
        return userDTOS;
    }

    @Override
    public void batchDeleteUserDataRules(List<String> userGuidList) {
        remove(new LambdaQueryWrapper<UserDataDO>()
                .in(!CollectionUtils.isEmpty(userGuidList), UserDataDO::getUserGuid, userGuidList));
    }

    @Override
    public Integer getStaffAliveStoreCount() {
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            List<String> allStoreGuid = orgFeignClient.queryAllStoreGuid();
            return allStoreGuid.size();
        }
        List<String> storeGuidList = queryAllStoreGuid();
        return storeGuidList.size();

    }
}
