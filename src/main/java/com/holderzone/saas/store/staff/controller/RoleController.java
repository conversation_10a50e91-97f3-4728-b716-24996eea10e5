package com.holderzone.saas.store.staff.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.user.RoleDTO;
import com.holderzone.saas.store.dto.user.RoleQueryDTO;
import com.holderzone.saas.store.staff.service.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className RoleController
 * @date 19-1-15 下午2:31
 * @description 角色服务控制器
 * @program holder-saas-store-staff
 */
@Slf4j
@RestController
@RequestMapping("/role")
@Api(description = "角色相关接口")
public class RoleController {

    private final RoleService roleService;

    @Autowired
    public RoleController(RoleService roleService) {
        this.roleService = roleService;
    }

    @ApiOperation(value = "创建角色")
    @PostMapping("/create")
    public boolean createRole(@RequestBody @Validated(RoleDTO.Create.class) RoleDTO roleDTO) {
        if (log.isInfoEnabled()) {
            log.info("创建角色入参：{}", JacksonUtils.writeValueAsString(roleDTO));
        }
        return roleService.createRole(roleDTO);
    }

    @ApiOperation(value = "更新角色")
    @PostMapping("/update")
    public boolean updateRole(@RequestBody @Validated(RoleDTO.Update.class) RoleDTO roleDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新角色入参：{}", JacksonUtils.writeValueAsString(roleDTO));
        }
        return roleService.updateRole(roleDTO);
    }

    @ApiOperation(value = "根据角色名称分页查询角色列表")
    @PostMapping("/query_page_by_name")
    public Page<RoleDTO> queryPageByName(@RequestBody RoleQueryDTO roleQueryDTO) {
        if (log.isInfoEnabled()) {
            log.info("根据角色名称分页查询角色列表入参：{}", JacksonUtils.writeValueAsString(roleQueryDTO));
        }
        return roleService.queryPageByName(roleQueryDTO);
    }

    @ApiOperation(value = "删除角色")
    @PostMapping("/delete")
    public boolean deleteRole(@RequestParam("roleGuid") String roleGuid) {
        log.info("删除角色入参：{}", "roleGuid=" + roleGuid);
        return roleService.deleteRole(roleGuid);
    }

    @ApiOperation(value = "根据角色guid判断该角色下是否存在帐号")
    @PostMapping("/query_exist_user")
    public boolean queryExistUser(@RequestParam("roleGuid") String roleGuid) {
        log.info("根据角色guid判断该角色下是否存在帐号入参：{}", "roleGuid=" + roleGuid);
        return roleService.queryExistUser(roleGuid);
    }

    @ApiOperation(value = "复制角色", notes = "生成除名字外其他都相同的角色信息，包括角色权限")
    @PostMapping("/copy_role")
    public boolean copyRole(@RequestParam("roleGuid") String roleGuid) {
        log.info("复制角色入参：{}", "roleGuid=" + roleGuid);
        return roleService.copyRole(roleGuid);
    }
}