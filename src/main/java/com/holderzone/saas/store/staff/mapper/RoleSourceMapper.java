package com.holderzone.saas.store.staff.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.staff.entity.domain.RoleSourceDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UserRoleMapper
 * @date 19-1-15 下午3:16
 * @description 角色-资源DO表相关操作
 * @program holder-saas-store-staff
 */
@Repository
public interface RoleSourceMapper extends BaseMapper<RoleSourceDO> {

    int batchUpdateSource(@Param("roleSourceDOList") List<RoleSourceDO> roleSourceDOList);

    int deleteUnnecessaryRoleSource(@Param("terminalGuidList") List<String> terminalGuidList);
    int updateModifiedRoleSource(@Param("terminalGuidList") List<String> terminalGuidList);


}
