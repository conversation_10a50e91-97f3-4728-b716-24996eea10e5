package com.holderzone.saas.store.staff.service.remote;

import com.holderzone.resource.common.dto.data.EnterpriseRedisDTO;
import com.holderzone.resource.common.dto.data.EnterpriseServerDatabaseDTO;
import com.holderzone.resource.common.dto.data.EnterpriseServerDatabaseQueryDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className EnterpriseClient
 * @date 18-9-13 下午5:19
 * @description 服务间调用-云端商户服务
 * @program holder-saas-store-store
 */
@Component
@FeignClient(value = "holder-saas-cloud-data", fallbackFactory = EnterpriseDataClient.ServiceFallBack.class)
public interface EnterpriseDataClient {

    @ApiOperation(value = "查询企业对应的数据库是否存在")
    @GetMapping("/enterpriseDatabase/enterprise-db-is-exists/{enterprise_guid}")
    Integer enterpriseDBIsExists(@PathVariable("enterprise_guid") String enterpriseGuid);
    @PostMapping("/enterpriseDatabase/selectEnterpriseDatabase")
    List<EnterpriseServerDatabaseDTO> selectDatabasesBy(@RequestBody EnterpriseServerDatabaseQueryDTO enterpriseServerDatabaseQuery);

    @GetMapping("/redis/enterprise-redis-is-exists/{enterprise_guid}")
    Integer enterpriseRedisIsExists(@PathVariable("enterprise_guid") String enterpriseGuid);
    @ApiOperation(value = "查询商户redis信息")
    @GetMapping("/redis/selectEnterpriseRedis")
    List<EnterpriseRedisDTO> selectEnterpriseRedis(String enterpriseGuid);
    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<EnterpriseDataClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public EnterpriseDataClient create(Throwable cause) {
            return new EnterpriseDataClient() {

                @Override
                public Integer enterpriseDBIsExists(String enterpriseGuid) {
                    return 1;
                }

                @Override
                public Integer enterpriseRedisIsExists(String enterpriseGuid) {
                    return 1;
                }

                @Override
                public List<EnterpriseRedisDTO> selectEnterpriseRedis(String enterpriseGuid) {
                    return null;
                }

                @Override
                public List<EnterpriseServerDatabaseDTO> selectDatabasesBy(EnterpriseServerDatabaseQueryDTO enterpriseServerDatabaseQuery) {
                    return null;
                }
            };
        }
    }
}
