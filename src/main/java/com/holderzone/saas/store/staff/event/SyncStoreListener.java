package com.holderzone.saas.store.staff.event;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.exception.DynamicException;
import com.holderzone.framework.dds.starter.utils.DynamicInfoHelper;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.staff.config.RocketMqConfig;
import com.holderzone.saas.store.staff.service.UserDataService;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import com.holderzone.saas.store.staff.service.remote.EnterpriseDataClient;
import com.holderzone.saas.store.staff.utils.DynamicHelper;
import com.holderzone.saas.store.staff.utils.EnterpriseUtils;
import com.holderzone.saas.store.staff.utils.ThrowableExtUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.DOWNSTREAM_STORE_TOPIC,
        tags = RocketMqConfig.DOWNSTREAM_STORE_CREATE_TAG,
        consumerGroup = RocketMqConfig.DOWNSTREAM_STORE_GRANT_MANAGE_STORE_GROUP,
        consumeThreadMax = 4,
        consumeThreadMin = 1,
        reTryConsume = 4
)
@AllArgsConstructor
public class SyncStoreListener extends AbstractRocketMqConsumer<RocketMqTopic, StoreDTO> {

    private final UserDataService userDataService;

    private final EnterpriseClient enterpriseClient;

    private final EnterpriseDataClient enterpriseDataClient;

    private final DynamicInfoHelper dynamicInfoHelper;

    @Override
    public boolean consumeMsg(StoreDTO storeDTO, MessageExt messageExt) {
        UserContextUtils.put(messageExt.getProperty(RocketMqConfig.DOWNSTREAM_CONTEXT));
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        if(!DynamicHelper.changeAndCheckDatasource(dynamicInfoHelper,enterpriseGuid)){
            return true;
        }
        try {
             if(!EnterpriseUtils.checkEnterpriseExists(enterpriseDataClient,enterpriseClient,enterpriseGuid)){
                 return true;
             }
            userDataService.updateNewStore(storeDTO.getGuid());
            log.info("门店：{} 授权自管理门店成功", storeDTO.getName());
        }  catch (Exception e) {
            log.error("门店：{} 授权自管理门店发生错误：",
                    storeDTO.getName(), e);
            return false;
        } finally {
            EnterpriseIdentifier.remove();
        }
        return true;
    }
}
