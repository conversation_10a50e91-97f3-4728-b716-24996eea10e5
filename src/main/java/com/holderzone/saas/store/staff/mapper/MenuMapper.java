package com.holderzone.saas.store.staff.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.staff.entity.domain.MenuDO;
import com.holderzone.saas.store.staff.entity.domain.StoreSourceDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className RoleMapper
 * @date 19-1-15 下午2:31
 * @description Menu表Mapper接口
 * @program holder-saas-store-staff
 */
@Repository
public interface MenuMapper extends BaseMapper<MenuDO> {
    List<StoreSourceDO> getUserModuleList(@Param("userGuid") String userGuid, @Param("terminalCode") String terminalCode);
}

