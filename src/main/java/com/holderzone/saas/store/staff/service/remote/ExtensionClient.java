package com.holderzone.saas.store.staff.service.remote;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.resource.common.dto.extension.TerminalDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className ExtensionClient
 * @date 18-9-29 上午10:33
 * @description 服务间调用：extension-service
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(value = "holder-saas-cloud-extension", fallbackFactory = ExtensionClient.ServiceFallBack.class)
public interface ExtensionClient {

    @GetMapping("/terminal/{terminalGuid}")
    TerminalDTO getTerminal(@PathVariable("terminalGuid") String terminalGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ExtensionClient> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ExtensionClient create(Throwable cause) {
            return terminalGuid -> {
                log.error(HYSTRIX_PATTERN, "getTerminal", "终端guid为：" + terminalGuid, ThrowableUtils.asString(cause));
                throw new BusinessException("调用服务查询终端相关信息失败");
            };
        }
    }
}
