package com.holderzone.saas.store.staff.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description hss_authorization_record 实体类
 * @date 2020/12/18 14:49
 */
@Accessors(chain = true)
@TableName("hss_authorization_record")
public class AuthorizationRecordDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 授权guid
     */
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 授权号
     */
    private String authorizationCode;

    /**
     * 资源guid
     */
    private String sourceGuid;

    /**
     * 资源名字
     */
    private String sourceName;

    /**
     * 资源code
     */
    private String sourceCode;
    /**
     * 授权人guid
     */
    private String authorizationStaffGuid;

    /**
     * 授权人name
     */
    private String authorizationStaffName;

    /**
     * 门店名
     */
    private String storeName;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 操作人guid
     */
    private String operatorGuid;

    /**
     * 操作人name
     */
    private String operatorName;

    /**
     * 授权时间
     */
    private LocalDateTime authorizationTime;

    /**
     * 最后使用时间
     */
    private LocalDateTime lastUseTime;

    /**
     * 使用次数
     */
    private Integer useCount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Boolean getDeleted() {
        return isDeleted;
    }

    public void setDeleted(Boolean deleted) {
        isDeleted = deleted;
    }

    public String getAuthorizationCode() {
        return authorizationCode;
    }

    public void setAuthorizationCode(String authorizationCode) {
        this.authorizationCode = authorizationCode;
    }

    public String getSourceGuid() {
        return sourceGuid;
    }

    public void setSourceGuid(String sourceGuid) {
        this.sourceGuid = sourceGuid;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getAuthorizationStaffGuid() {
        return authorizationStaffGuid;
    }

    public void setAuthorizationStaffGuid(String authorizationStaffGuid) {
        this.authorizationStaffGuid = authorizationStaffGuid;
    }

    public String getAuthorizationStaffName() {
        return authorizationStaffName;
    }

    public void setAuthorizationStaffName(String authorizationStaffName) {
        this.authorizationStaffName = authorizationStaffName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public String getOperatorGuid() {
        return operatorGuid;
    }

    public void setOperatorGuid(String operatorGuid) {
        this.operatorGuid = operatorGuid;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public LocalDateTime getAuthorizationTime() {
        return authorizationTime;
    }

    public void setAuthorizationTime(LocalDateTime authorizationTime) {
        this.authorizationTime = authorizationTime;
    }

    public LocalDateTime getLastUseTime() {
        return lastUseTime;
    }

    public void setLastUseTime(LocalDateTime lastUseTime) {
        this.lastUseTime = lastUseTime;
    }

    public Integer getUseCount() {
        return useCount;
    }

    public void setUseCount(Integer useCount) {
        this.useCount = useCount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AuthorizationRecordDO)) return false;
        AuthorizationRecordDO that = (AuthorizationRecordDO) o;
        return Objects.equals(getId(), that.getId()) && Objects.equals(getGuid(), that.getGuid()) && Objects.equals(getGmtCreate(), that.getGmtCreate()) && Objects.equals(getGmtModified(), that.getGmtModified()) && Objects.equals(isDeleted, that.isDeleted) && Objects.equals(getAuthorizationCode(), that.getAuthorizationCode()) && Objects.equals(getSourceGuid(), that.getSourceGuid()) && Objects.equals(getSourceName(), that.getSourceName()) && Objects.equals(getSourceCode(), that.getSourceCode()) && Objects.equals(getAuthorizationStaffGuid(), that.getAuthorizationStaffGuid()) && Objects.equals(getAuthorizationStaffName(), that.getAuthorizationStaffName()) && Objects.equals(getStoreName(), that.getStoreName()) && Objects.equals(getStoreGuid(), that.getStoreGuid()) && Objects.equals(getOperatorGuid(), that.getOperatorGuid()) && Objects.equals(getOperatorName(), that.getOperatorName()) && Objects.equals(getAuthorizationTime(), that.getAuthorizationTime()) && Objects.equals(getLastUseTime(), that.getLastUseTime()) && Objects.equals(getUseCount(), that.getUseCount());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getGuid(), getGmtCreate(), getGmtModified(), isDeleted, getAuthorizationCode(), getSourceGuid(), getSourceName(), getSourceCode(), getAuthorizationStaffGuid(), getAuthorizationStaffName(), getStoreName(), getStoreGuid(), getOperatorGuid(), getOperatorName(), getAuthorizationTime(), getLastUseTime(), getUseCount());
    }

    @Override
    public String toString() {
        return "AuthorizationRecordDO{" +
                "id=" + id +
                ", guid='" + guid + '\'' +
                ", gmtCreate=" + gmtCreate +
                ", gmtModified=" + gmtModified +
                ", isDeleted=" + isDeleted +
                ", authorizationCode='" + authorizationCode + '\'' +
                ", sourceGuid='" + sourceGuid + '\'' +
                ", sourceName='" + sourceName + '\'' +
                ", sourceCode='" + sourceCode + '\'' +
                ", authorizationStaffGuid='" + authorizationStaffGuid + '\'' +
                ", authorizationStaffName='" + authorizationStaffName + '\'' +
                ", storeName='" + storeName + '\'' +
                ", storeGuid='" + storeGuid + '\'' +
                ", operatorGuid='" + operatorGuid + '\'' +
                ", operatorName='" + operatorName + '\'' +
                ", authorizationTime=" + authorizationTime +
                ", lastUseTime=" + lastUseTime +
                ", useCount=" + useCount +
                '}';
    }
}
