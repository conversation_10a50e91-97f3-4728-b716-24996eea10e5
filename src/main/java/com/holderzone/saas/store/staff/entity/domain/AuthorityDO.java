package com.holderzone.saas.store.staff.entity.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className AuthorityDO
 * @date 20-12-18 上午10:32
 * @description hss_authority DO
 * @program holder-saas-store-staff
 */
@Accessors(chain = true)
@TableName(value = "hss_authority")
public class AuthorityDO implements Serializable {

    private static final long serialVersionUID = 5829162238319212183L;

    /**
     * id
     */
    private Integer id;

    /**
     * 终端code
     */
    private String terminalCode;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 资源名称
     */
    private String sourceName;

    /**
     * 资源code
     */
    private String sourceCode;

    /**
     * 资源url
     */
    private String sourceUrl;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 权限来源：0，自己创建；1，来自角色列表
     */
    private Integer sourceFrom;

    public Integer getSourceFrom() {
        return sourceFrom;
    }

    public void setSourceFrom(Integer sourceFrom) {
        this.sourceFrom = sourceFrom;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public AuthorityDO() {

    }

    public AuthorityDO(Integer id, String terminalCode, String terminalName, String sourceName, String sourceCode, String sourceUrl) {
        this.id = id;
        this.terminalCode = terminalCode;
        this.terminalName = terminalName;
        this.sourceName = sourceName;
        this.sourceCode = sourceCode;
        this.sourceUrl = sourceUrl;
    }

    public AuthorityDO(String terminalCode, String terminalName, String sourceName, String sourceCode, String sourceUrl) {
        this.terminalCode = terminalCode;
        this.terminalName = terminalName;
        this.sourceName = sourceName;
        this.sourceCode = sourceCode;
        this.sourceUrl = sourceUrl;
    }

    public AuthorityDO(String terminalName, String sourceName, String sourceCode, String sourceUrl) {
        this.terminalName = terminalName;
        this.sourceName = sourceName;
        this.sourceCode = sourceCode;
        this.sourceUrl = sourceUrl;
    }

    public AuthorityDO(String sourceName, String sourceCode, String sourceUrl) {
        this.sourceName = sourceName;
        this.sourceCode = sourceCode;
        this.sourceUrl = sourceUrl;
    }

    public AuthorityDO(String sourceCode, String sourceUrl) {
        this.sourceCode = sourceCode;
        this.sourceUrl = sourceUrl;
    }


    public AuthorityDO(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer guid) {
        this.id = id;
    }

    public String getTerminalCode() {
        return terminalCode;
    }

    public void setTerminalCode(String terminalCode) {
        this.terminalCode = terminalCode;
    }

    public String getTerminalName() {
        return terminalName;
    }

    public void setTerminalName(String terminalName) {
        this.terminalName = terminalName;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getSourceUrl() {
        return sourceUrl;
    }

    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }
}
