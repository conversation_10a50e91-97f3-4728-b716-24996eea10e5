package com.holderzone.saas.store.staff.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.invoice.UserAuthorityInvoiceDTO;
import com.holderzone.saas.store.dto.user.*;
import com.holderzone.saas.store.dto.user.req.AuthorizationReqDTO;
import com.holderzone.saas.store.dto.user.req.PermissionsReqDTO;
import com.holderzone.saas.store.dto.user.resp.PermissionsRespDTO;
import com.holderzone.saas.store.dto.user.resp.UserAuthorityBriefDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.dto.user.resp.UserFaceDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.staff.constant.Constant;
import com.holderzone.saas.store.staff.entity.constant.Constants;
import com.holderzone.saas.store.staff.entity.domain.*;
import com.holderzone.saas.store.staff.entity.enums.SourceFromEnum;
import com.holderzone.saas.store.staff.entity.query.UserSourceQuery;
import com.holderzone.saas.store.staff.mapper.AuthorityMapper;
import com.holderzone.saas.store.staff.mapper.UserAuthorityMapper;
import com.holderzone.saas.store.staff.mapper.UserMapper;
import com.holderzone.saas.store.staff.service.*;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.staff.constant.Constant.AUTHORIZATION_CODE_CANNOT_BE_EMPTY;
import static com.holderzone.saas.store.staff.constant.Constant.AUTHORIZATION_CODE_NOT_FOUND;
import static com.holderzone.saas.store.staff.constant.Constant.USER_PERMISSION_TABLE_IS_EMPTY;

@Slf4j
@Service
public class UserAuthorityServiceImpl extends ServiceImpl<UserAuthorityMapper, UserAuthorityDO> implements UserAuthorityService {

    private final AuthorityMapper authorityMapper;

    private final UserAuthorityMapper userAuthorityMapper;

    private final AuthorizationRecordService authorizationRecordService;

    private final DistributedService distributedService;

    private final UserService userService;

    private final RedisTemplate redisTemplate;

    private final UserMapper userMapper;

    private final UserDataService userDataService;

    //redis key electronic_invoice
    private static final String ELECTRONIC_INVOICE_KEY = "ELECTRONIC_INVOICE_KEY:";

    //electronic_invoice
    private static final String ELECTRONIC_INVOICE = "electronic_invoice";

    @Autowired
    public UserAuthorityServiceImpl(AuthorityMapper authorityMapper, UserAuthorityMapper userAuthorityMapper,
                                    AuthorizationRecordService authorizationRecordService, DistributedService distributedService,
                                    UserService userService, RedisTemplate redisTemplate, UserMapper userMapper, UserDataService userDataService) {
        this.authorityMapper = authorityMapper;
        this.userAuthorityMapper = userAuthorityMapper;
        this.redisTemplate = redisTemplate;
        this.authorizationRecordService = authorizationRecordService;
        this.distributedService = distributedService;
        this.userService = userService;
        this.userMapper = userMapper;
        this.userDataService = userDataService;
    }


    @Override
    @Transactional
    public void saveUserAuthority(UserAuthoritySaveDTO userAuthoritySaveDTO) {
        log.info("saveUserAuthority:{}", JacksonUtils.writeValueAsString(userAuthoritySaveDTO));
        String userGuid = userAuthoritySaveDTO.getUserGuid();
        if (ObjectUtils.isEmpty(userGuid)) {
            throw new BusinessException("被授权人guid不能为空");
        }

        List<UserAuthorityDTO> userAuthorityDTOList = userAuthoritySaveDTO.getUserAuthorityDTOList();

        // 先删除，再保存
        this.remove(new LambdaQueryWrapper<UserAuthorityDO>()
                .eq(UserAuthorityDO::getUserGuid, userGuid)
                .eq(UserAuthorityDO::getIsDelete, 0));

        redisTemplate.delete(ELECTRONIC_INVOICE_KEY + userGuid);
        // 如果传入为空则为删除
        if (CollectionUtils.isEmpty(userAuthorityDTOList)) {
            return;
        }

        //授权号同一企业下不可重复，授权号对同一个人来说是一样的
        Set<String> authCodeSet = userAuthorityDTOList.stream()
                .map(UserAuthorityDTO::getAuthorityCode)
                .collect(Collectors.toSet());
        int count = this.count(new LambdaQueryWrapper<UserAuthorityDO>()
                .in(UserAuthorityDO::getAuthorityCode, authCodeSet)
                .eq(UserAuthorityDO::getIsDelete, 0)
        );
        if (count > 0) {
            throw new BusinessException("同一企业下授权号不可重复");
        }

        List<UserAuthorityDO> authorityDOList = new ArrayList<>();
        userAuthorityDTOList.forEach(userAuthorityDTO -> {
            UserAuthorityDO userAuthorityDO = new UserAuthorityDO();
            String guid = distributedService.nextAuthorityGuid();
            userAuthorityDO.setGuid(guid);
            userAuthorityDO.setAuthorityCode(userAuthorityDTO.getAuthorityCode());
            userAuthorityDO.setUserGuid(userGuid);
            userAuthorityDO.setAuthorizerGuid(UserContextUtils.getUserGuid());
            userAuthorityDO.setAuthorizerAccount(UserContextUtils.getUserAccount());
            userAuthorityDO.setSourceCode(userAuthorityDTO.getSourceCode());
            userAuthorityDO.setElectronicTaxpayerName(userAuthorityDTO.getElectronicTaxpayerName());
            userAuthorityDO.setElectronicTaxpayerPhone(userAuthorityDTO.getElectronicTaxpayerPhone());
            authorityDOList.add(userAuthorityDO);
        });
        this.saveBatch(authorityDOList);

        cacheElectronicInvoice(authorityDOList, userGuid);
    }

    private void cacheElectronicInvoice(List<UserAuthorityDO> authorityDOList, String userGuid) {
        List<UserAuthorityDO> userAuthorityDOList = authorityDOList.stream()
                .filter(in -> in.getSourceCode().equals(ELECTRONIC_INVOICE)).collect(Collectors.toList());
        log.info("electronic_invoice:{}", JacksonUtils.writeValueAsString(userAuthorityDOList));

        if (CollUtil.isNotEmpty(userAuthorityDOList)) {
            UserAuthorityDO userAuthorityDO = userAuthorityDOList.get(0);
            if (!StringUtils.isEmpty(userAuthorityDO.getElectronicTaxpayerName())
                    && !StringUtils.isEmpty(userAuthorityDO.getElectronicTaxpayerPhone())) {
                UserAuthorityInvoiceDTO userAuthorityDTO = new UserAuthorityInvoiceDTO();
                BeanUtils.copyProperties(userAuthorityDO, userAuthorityDTO);
                redisTemplate.opsForValue()
                        .set(ELECTRONIC_INVOICE_KEY + userGuid, JSON.toJSONString(userAuthorityDTO));

            }
        }
    }

    @Override
    public Boolean updateAuthority(UserAuthorityUpdateDTO userAuthorityUpdateDTO) throws IOException {
        List<UserAuthorityDTO> userAuthorityDTOS = userAuthorityUpdateDTO.getUserAuthorityDTOS();
        String userGuid = "";
        String authorityCode = "";
        if (!ObjectUtils.isEmpty(userAuthorityDTOS)) {
            userGuid = userAuthorityDTOS.get(0).getUserGuid();
            authorityCode = userAuthorityDTOS.get(0).getAuthorityCode();
            for (UserAuthorityDTO userAuthorityDTO : userAuthorityDTOS) {
                //更新相关用户权限
                UserAuthorityDO userAuthorityDO = new UserAuthorityDO();
                userAuthorityDO.setGuid(userAuthorityDTO.getGuid());
                userAuthorityDO.setSourceCode(userAuthorityDTO.getSourceCode());
                userAuthorityDO.setAuthorityCode(userAuthorityDTO.getAuthorityCode());
                userAuthorityDO.setUserGuid(userAuthorityDTO.getUserGuid());
                userAuthorityDO.setAuthorizerGuid(UserContextUtils.getUserGuid());
                userAuthorityDO.setAuthorizerAccount(UserContextUtils.getUserAccount());
                userAuthorityDO.setIsDelete(userAuthorityDTO.getIsDelete());
                this.update(userAuthorityDO, new LambdaQueryWrapper<UserAuthorityDO>()
                        .eq(UserAuthorityDO::getGuid, userAuthorityDTO.getGuid()));
            }
        }

        if (userAuthorityUpdateDTO.getIsDebt() == 1 || userAuthorityUpdateDTO.getIsReturn() == 1) {
            List<AuthorityDO> authorityDTOS = authorityMapper.selectList(new LambdaQueryWrapper<AuthorityDO>()
                    .eq(AuthorityDO::getTerminalCode, "3"));
            for (AuthorityDO authorityDO : authorityDTOS) {
                //插入相关用户权限
                UserAuthorityDO userAuthorityDO = new UserAuthorityDO();
                String userAuthorityGuid = String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "hssUserAuthority"));
                userAuthorityDO.setGuid(userAuthorityGuid);
                if (userAuthorityUpdateDTO.getIsReturn() == 1) {
                    if (authorityDO.getSourceName().contains("退菜")) {
                        userAuthorityDO.setSourceCode(authorityDO.getSourceCode());

                        userAuthorityDO.setAuthorityCode(authorityCode);
                        userAuthorityDO.setUserGuid(userGuid);
                        userAuthorityDO.setAuthorizerGuid(UserContextUtils.getUserGuid());
                        userAuthorityDO.setAuthorizerAccount(UserContextUtils.getUserAccount());
                        userAuthorityDO.setIsDelete(0);
                        this.save(userAuthorityDO);
                    }
                }
                if (userAuthorityUpdateDTO.getIsDebt() == 1) {
                    if (authorityDO.getSourceName().contains("挂账")) {
                        userAuthorityDO.setSourceCode(authorityDO.getSourceCode());

                        userAuthorityDO.setAuthorityCode(authorityCode);
                        userAuthorityDO.setUserGuid(userGuid);
                        userAuthorityDO.setAuthorizerGuid(UserContextUtils.getUserGuid());
                        userAuthorityDO.setAuthorizerAccount(UserContextUtils.getUserAccount());
                        userAuthorityDO.setIsDelete(0);
                        this.save(userAuthorityDO);
                    }
                }
            }
        }
        return Boolean.TRUE;
    }


    @Override
    public List<UserAuthorityDTO> queryUserAuthority(UserAuthorityQueryDTO userAuthorityQueryDTO) {
        String userGuid = userAuthorityQueryDTO.getUserGuid();
        ArrayList<UserAuthorityDTO> respDTOS = new ArrayList<>();

        List<UserAuthorityDO> userAuthorityDOList = this.list(new LambdaQueryWrapper<UserAuthorityDO>()
                .eq(UserAuthorityDO::getUserGuid, userGuid)
                .eq(UserAuthorityDO::getIsDelete, 0));
        if (CollectionUtils.isEmpty(userAuthorityDOList)) {
            return respDTOS;
        }
        UserDO userDO = userService.getByUserGuid(userGuid);
        userAuthorityDOList.forEach(
                userAuthorityDO -> {
                    UserAuthorityDTO respDTO = new UserAuthorityDTO();
                    respDTO.setUserGuid(userAuthorityDO.getUserGuid());
                    respDTO.setAuthorizerAccount(userAuthorityDO.getAuthorizerAccount());
                    respDTO.setAuthorizerGuid(userAuthorityDO.getAuthorizerGuid());
                    respDTO.setAuthorityCode(userAuthorityDO.getAuthorityCode());
                    respDTO.setSourceCode(userAuthorityDO.getSourceCode());
                    respDTO.setInputFace(!StringUtils.isEmpty(userDO.getFaceCode()));
                    respDTO.setElectronicTaxpayerName(userAuthorityDO.getElectronicTaxpayerName());
                    respDTO.setElectronicTaxpayerPhone(userAuthorityDO.getElectronicTaxpayerPhone());
                    respDTOS.add(respDTO);
                }
        );
        return respDTOS;
    }

    @Override
    public UserAuthorityInvoiceDTO queryUserInvoice(String userGuid) {
        try {
            UserAuthorityDO userAuthorityDO = baseMapper.selectOne(new LambdaQueryWrapper<UserAuthorityDO>()
                    .eq(UserAuthorityDO::getUserGuid, userGuid)
                    .eq(UserAuthorityDO::getSourceCode, ELECTRONIC_INVOICE)
                    .eq(UserAuthorityDO::getIsDelete, 0));
            log.info("userAuthorityDO:{}", JacksonUtils.writeValueAsString(userAuthorityDO));
            if (ObjectUtils.isEmpty(userAuthorityDO)
                    || StringUtils.isEmpty(userAuthorityDO.getElectronicTaxpayerName())
                    || StringUtils.isEmpty(userAuthorityDO.getElectronicTaxpayerPhone())) {
                return null;
            }
            UserAuthorityInvoiceDTO userAuthorityDTO = new UserAuthorityInvoiceDTO();
            BeanUtils.copyProperties(userAuthorityDO, userAuthorityDTO);
            return userAuthorityDTO;
        } catch (Exception e) {
            log.error("queryUserInvoice error:{}", e);
        }
        return null;
    }

    /**
     * 根据员工、门店、终端查询可升级权限
     * UserAuthorityDO
     *
     * @param reqDTO PermissionsReqDTO
     * @return sourceCode、sourceName、sourceUrl
     */
    @Override
    public List<PermissionsRespDTO> queryEmployeePermissions(PermissionsReqDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO.getUserGuid())) {
            throw new BusinessException("员工guid不能为空");
        }
        if (ObjectUtils.isEmpty(reqDTO.getStoreGuid())) {
            throw new BusinessException("门店guid不能为空");
        }
        if (ObjectUtils.isEmpty(reqDTO.getTerminalCode())) {
            throw new BusinessException("终端code不能为空");
        }
        return userAuthorityMapper.queryEmployeePermissions(reqDTO);
    }

    @Override
    @Transactional
    public Boolean authorize(AuthorizationReqDTO reqDTO) {
        String sourceCode = reqDTO.getSourceCode();
        String authorityCode = reqDTO.getAuthorityCode();
        String storeGuid = UserContextUtils.getStoreGuid();
        if (ObjectUtils.isEmpty(sourceCode)) {
            throw new BusinessException("资源code不能为空");
        }
        if (ObjectUtils.isEmpty(authorityCode)) {
            throw new BusinessException(AUTHORIZATION_CODE_CANNOT_BE_EMPTY);
        }

        AuthorityDO authorityDO = authorityMapper.selectOne(new LambdaQueryWrapper<AuthorityDO>()
                .eq(AuthorityDO::getSourceCode, sourceCode));
        String terminalCode = authorityDO.getTerminalCode();
        if (StringUtils.isEmpty(terminalCode)) {
            throw new BusinessException("授权终端code为空");
        }

        // 验证是否存在授权号，授权号是否有该权限
        UserAuthorityDTO userAuthorityDTO = new UserAuthorityDTO();
        userAuthorityDTO.setAuthorityCode(authorityCode);
        userAuthorityDTO.setSourceCode(sourceCode);
        PermissionsRespDTO permissions = userAuthorityMapper.queryAuthorize(userAuthorityDTO);
        log.info("授权查询结果：{}", JacksonUtils.writeValueAsString(permissions));
        if (ObjectUtils.isEmpty(permissions)) {
            return Boolean.FALSE;
        }

        // 如果来源是角色列表，查询被授权人是否有权限
        if (!ObjectUtils.isEmpty(permissions.getSourceFrom())
                && SourceFromEnum.ROLE_LIST.getCode().equals(permissions.getSourceFrom())) {

            List<MenuSourceDTO> menuSourceList = userMapper.queryUserSource(new UserSourceQuery()
                    .setTerminalCode(terminalCode)
                    .setStoreGuid(storeGuid)
                    .setUserGuid(permissions.getUserGuid()));

            if (CollectionUtils.isEmpty(menuSourceList)) {
                log.warn("授权失败: 员工权限为空");
                return Boolean.FALSE;
            }
            List<String> codeList = menuSourceList.stream()
                    .map(MenuSourceDTO::getSourceCode)
                    .distinct()
                    .collect(Collectors.toList());

            if (!codeList.contains(sourceCode)) {
                log.warn("授权失败: 授权人没有该权限");
                return Boolean.FALSE;
            }

        }

        // 判断授权号归属员工管理的门店里有没有包含当前的门店，如果没有则授权失败
        List<UserDataDO> userDataDOList = userDataService.list(new LambdaQueryWrapper<UserDataDO>()
                .eq(UserDataDO::getUserGuid, permissions.getUserGuid()));

        List<String> storeGuidList = userDataDOList.stream()
                .map(UserDataDO::getStoreGuid)
                .collect(Collectors.toList());
        if (!storeGuidList.contains(storeGuid)) {
            log.warn("授权失败: 授权号归属员工管理的门店里有没有包含当前的门店");
            throw new BusinessException("授权失败: 授权号归属员工管理的门店里有没有包含当前的门店");
        }

        // 保存授权记录
        saveAuthorizeRecord(permissions);
        log.info("授权成功：{}", JacksonUtils.writeValueAsString(reqDTO));
        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteAuthority(AuthorizationReqDTO deleteDTO) {
        return this.remove(new LambdaQueryWrapper<UserAuthorityDO>()
                .eq(UserAuthorityDO::getUserGuid, deleteDTO.getUserGuid()));
    }

    @Override
    public List<PermissionsRespDTO> queryAuthority() {
        List<AuthorityDO> authorityDOList = authorityMapper.selectList(new LambdaQueryWrapper<AuthorityDO>()
                .eq(AuthorityDO::getIsDelete, 0)
                .eq(AuthorityDO::getTerminalCode, "3"));
        ArrayList<PermissionsRespDTO> respDTOS = new ArrayList<>();
        authorityDOList.forEach(
                authorityDO -> {
                    PermissionsRespDTO permissionsRespDTO = new PermissionsRespDTO();
                    permissionsRespDTO.setTerminalCode(authorityDO.getTerminalCode());
                    permissionsRespDTO.setTerminalName(authorityDO.getTerminalName());
                    permissionsRespDTO.setSourceCode(authorityDO.getSourceCode());
                    permissionsRespDTO.setSourceName(authorityDO.getSourceName());
                    permissionsRespDTO.setSourceUrl(authorityDO.getSourceUrl());
                    respDTOS.add(permissionsRespDTO);
                }
        );
        return respDTOS;
    }

    @Override
    public Integer queryUserAuthority(PermissionsReqDTO reqDTO) {
        return userAuthorityMapper.queryUserAuthority(reqDTO);
    }

    @Override
    public void inputFace(UserAuthoritySaveDTO inputFaceDTO) {
        List<UserAuthorityDTO> authorityDTOList = inputFaceDTO.getUserAuthorityDTOList();
        if (CollectionUtils.isEmpty(authorityDTOList)) {
            return;
        }
        Map<String, UserAuthorityDTO> sourceMap = authorityDTOList.stream()
                .collect(Collectors.toMap(UserAuthorityDTO::getSourceCode, Function.identity(), (v1, v2) -> v1));
        List<String> sourceCodeList = authorityDTOList.stream()
                .map(UserAuthorityDTO::getSourceCode)
                .collect(Collectors.toList());
        List<UserAuthorityDO> authorityDOList = this.list(new LambdaQueryWrapper<UserAuthorityDO>()
                .eq(UserAuthorityDO::getUserGuid, inputFaceDTO.getUserGuid())
                .eq(UserAuthorityDO::getIsDelete, Boolean.FALSE)
                .in(UserAuthorityDO::getSourceCode, sourceCodeList)
        );
        authorityDOList.forEach(authorityDO -> {
            UserAuthorityDTO authorityDTO = sourceMap.get(authorityDO.getSourceCode());
            if (!ObjectUtils.isEmpty(authorityDTO)) {
                authorityDO.setAuthorityCode(authorityDTO.getAuthorityCode());
            }
        });
        this.updateBatchById(authorityDOList);
    }

    @Override
    public UserFaceDTO authorizeFace(AuthorizationReqDTO reqDTO) {
        String sourceCode = reqDTO.getSourceCode();
        String authorityCode = reqDTO.getAuthorityCode();
        String storeGuid = UserContextUtils.getStoreGuid();

        AuthorityDO authorityDO = authorityMapper.selectOne(new LambdaQueryWrapper<AuthorityDO>()
                .eq(AuthorityDO::getSourceCode, sourceCode)
                .eq(AuthorityDO::getIsDelete, BooleanEnum.FALSE.getCode()));
        if (ObjectUtils.isEmpty(authorityDO)) {
            log.warn("人脸授权失败: 权限{}不可授权", sourceCode);
            throw new BusinessException(Constant.NOT_FOUND_YOUR_FACE);
        }

        UserDO userDO = userMapper.selectOne(new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getFaceCode, authorityCode)
                .eq(UserDO::getIsEnable, BooleanEnum.TRUE.getCode())
                .eq(UserDO::getIsDeleted, BooleanEnum.FALSE.getCode()));
        if (ObjectUtils.isEmpty(userDO)) {
            log.warn("人脸授权失败: 未查询到人脸信息");
            throw new BusinessException(Constant.NOT_FOUND_YOUR_FACE);
        }

        // 只看员工授权管理是否勾选且管理该门店，不管授权人是否勾选权限
        UserAuthorityDO userAuthorityDO = userAuthorityMapper.selectOne(new LambdaQueryWrapper<UserAuthorityDO>()
                .eq(UserAuthorityDO::getUserGuid, userDO.getGuid())
                .eq(UserAuthorityDO::getSourceCode, sourceCode)
                .eq(UserAuthorityDO::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        if (ObjectUtils.isEmpty(userAuthorityDO)) {
            log.warn("人脸授权失败: 授权人未管理该功能");
            throw new BusinessException(Constant.NOT_FOUND_YOUR_FACE);
        }

        // 判断授权号归属员工管理的门店里有没有包含当前的门店，如果没有则授权失败
        List<UserDataDO> userDataDOList = userDataService.list(new LambdaQueryWrapper<UserDataDO>()
                .eq(UserDataDO::getUserGuid, userDO.getGuid()));

        List<String> storeGuidList = userDataDOList.stream()
                .map(UserDataDO::getStoreGuid)
                .collect(Collectors.toList());
        if (!storeGuidList.contains(storeGuid)) {
            log.warn("人脸授权失败: 授权人未管理当前的门店");
            throw new BusinessException(Constant.NOT_FOUND_YOUR_FACE);
        }

        // 保存授权记录
        saveRecord(reqDTO, authorityDO, userDO);

        log.info("人脸授权成功：授权人={},授权参数={}", userDO.getName(), JacksonUtils.writeValueAsString(reqDTO));
        UserFaceDTO faceDTO = new UserFaceDTO();
        faceDTO.setPhone(userDO.getPhone());
        faceDTO.setUserGuid(userDO.getGuid());
        faceDTO.setUserName(userDO.getName());
        faceDTO.setAccount(userDO.getAccount());
        return faceDTO;
    }

    @Override
    public List<PermissionsRespDTO> queryAuthorityAnyMatch(UserAuthorityQueryDTO userAuthorityQueryDTO) {
        // 查询可升级权限
        PermissionsReqDTO reqDTO = new PermissionsReqDTO();
        reqDTO.setTerminalCode("3");
        reqDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        reqDTO.setUserGuid(UserContextUtils.getUserGuid());
        List<PermissionsRespDTO> permissionsList = queryEmployeePermissions(reqDTO);
        if (CollectionUtils.isEmpty(permissionsList)) {
            throw new BusinessException(USER_PERMISSION_TABLE_IS_EMPTY);
        }

        String storeGuid = userAuthorityQueryDTO.getStoreGuid();
        if (StringUtils.isEmpty(storeGuid)) {
            return Collections.emptyList();
        }
        List<UserBriefDTO> userBriefDTOS = userService.storeUsers(storeGuid);
        if (CollectionUtils.isEmpty(userBriefDTOS)) {
            return Collections.emptyList();
        }
        List<String> userGuids = userBriefDTOS.stream()
                .filter(Objects::nonNull)
                .map(UserBriefDTO::getUserGuid)
                .filter(userGuid -> !StringUtils.isEmpty(userGuid)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userGuids)) {
            return Collections.emptyList();
        }

        List<PermissionsRespDTO> resultList = new ArrayList<>();
        permissionsList.forEach(
                permission -> {
                    // 查询后台是否设置授权
                    PermissionsReqDTO dto = new PermissionsReqDTO();
                    dto.setSourceFrom(SourceFromEnum.BUILD_SELF.getCode());
                    dto.setSourceCode(permission.getSourceCode());
                    dto.setUserGuids(userGuids);
                    Integer count = userAuthorityMapper.queryAuthorityAnyMatch(dto);
                    PermissionsRespDTO result = new PermissionsRespDTO();
                    result.setSourceCode(permission.getSourceCode());
                    if (Objects.nonNull(count) && count > 0) {
                        result.setAnyoneAuthorized(Boolean.TRUE);
                    } else {
                        result.setAnyoneAuthorized(Boolean.FALSE);
                    }
                    resultList.add(result);
                }
        );
        return resultList;
    }

    @Override
    public UserAuthorityBriefDTO authorizeUser(AuthorizationReqDTO reqDTO) {
        String sourceCode = reqDTO.getSourceCode();
        String authorityCode = reqDTO.getAuthorityCode();
        String storeGuid = UserContextUtils.getStoreGuid();
        if (ObjectUtils.isEmpty(sourceCode)) {
            throw new BusinessException("资源code不能为空");
        }
        if (ObjectUtils.isEmpty(authorityCode)) {
            throw new BusinessException(AUTHORIZATION_CODE_CANNOT_BE_EMPTY);
        }

        AuthorityDO authorityDO = authorityMapper.selectOne(new LambdaQueryWrapper<AuthorityDO>()
                .eq(AuthorityDO::getSourceCode, sourceCode));
        String terminalCode = authorityDO.getTerminalCode();
        if (StringUtils.isEmpty(terminalCode)) {
            throw new BusinessException(AUTHORIZATION_CODE_NOT_FOUND);
        }

        // 验证是否存在授权号，授权号是否有该权限
        UserAuthorityDTO userAuthorityDTO = new UserAuthorityDTO();
        userAuthorityDTO.setAuthorityCode(authorityCode);
        userAuthorityDTO.setSourceCode(sourceCode);
        PermissionsRespDTO permissions = userAuthorityMapper.queryAuthorize(userAuthorityDTO);
        log.info("授权查询结果：{}", JacksonUtils.writeValueAsString(permissions));
        if (ObjectUtils.isEmpty(permissions)) {
            throw new BusinessException(AUTHORIZATION_CODE_NOT_FOUND);
        }

        // 如果来源是角色列表，查询被授权人是否有权限
        if (!ObjectUtils.isEmpty(permissions.getSourceFrom())
                && SourceFromEnum.ROLE_LIST.getCode().equals(permissions.getSourceFrom())) {

            List<MenuSourceDTO> menuSourceList = userMapper.queryUserSource(new UserSourceQuery()
                    .setTerminalCode(terminalCode)
                    .setStoreGuid(storeGuid)
                    .setUserGuid(permissions.getUserGuid()));

            if (CollectionUtils.isEmpty(menuSourceList)) {
                log.warn("授权失败: 员工权限为空");
                throw new BusinessException(AUTHORIZATION_CODE_NOT_FOUND);
            }
            List<String> codeList = menuSourceList.stream()
                    .map(MenuSourceDTO::getSourceCode)
                    .distinct()
                    .collect(Collectors.toList());

            if (!codeList.contains(sourceCode)) {
                log.warn("授权失败: 授权人没有该权限");
                throw new BusinessException(AUTHORIZATION_CODE_NOT_FOUND);
            }

        }

        // 判断授权号归属员工管理的门店里有没有包含当前的门店，如果没有则授权失败
        List<UserDataDO> userDataDOList = userDataService.list(new LambdaQueryWrapper<UserDataDO>()
                .eq(UserDataDO::getUserGuid, permissions.getUserGuid()));

        List<String> storeGuidList = userDataDOList.stream()
                .map(UserDataDO::getStoreGuid)
                .collect(Collectors.toList());
        if (!storeGuidList.contains(storeGuid)) {
            log.warn("授权失败: 授权号归属员工管理的门店里有没有包含当前的门店");
            throw new BusinessException("授权失败: 授权号归属员工管理的门店里有没有包含当前的门店");
        }

        // 保存授权记录
        saveAuthorizeRecord(permissions);
        log.info("授权成功：{}", JacksonUtils.writeValueAsString(reqDTO));
        String userGuid = permissions.getUserGuid();
        UserDO authorizer = userService.getByUserGuid(userGuid);
        UserAuthorityBriefDTO userAuthorityBriefDTO = new UserAuthorityBriefDTO();
        userAuthorityBriefDTO.setPhone(authorizer.getPhone());
        userAuthorityBriefDTO.setUserGuid(authorizer.getGuid());
        userAuthorityBriefDTO.setUserName(authorizer.getName());
        userAuthorityBriefDTO.setAccount(authorizer.getAccount());
        return userAuthorityBriefDTO;
    }

    private void saveRecord(AuthorizationReqDTO reqDTO, AuthorityDO authorityDO, UserDO userDO) {
        AuthorizationRecordDO recordDO = new AuthorizationRecordDO();
        String guid = distributedService.nextAuthorityGuid();
        recordDO.setGuid(guid);
        recordDO.setAuthorizationCode(reqDTO.getAuthorityCode());
        recordDO.setSourceGuid("");
        recordDO.setSourceName(authorityDO.getSourceName());
        recordDO.setSourceCode(authorityDO.getSourceCode());
        recordDO.setAuthorizationStaffGuid(userDO.getGuid());
        recordDO.setAuthorizationStaffName(userDO.getName());
        recordDO.setStoreGuid(UserContextUtils.getStoreGuid());
        recordDO.setStoreName(UserContextUtils.getStoreName());
        recordDO.setOperatorGuid(UserContextUtils.getUserGuid());
        recordDO.setOperatorName(UserContextUtils.getUserName());
        recordDO.setAuthorizationTime(LocalDateTime.now());
        recordDO.setLastUseTime(LocalDateTime.now());

        int count = authorizationRecordService.count(new LambdaQueryWrapper<AuthorizationRecordDO>()
                .eq(AuthorizationRecordDO::getAuthorizationCode, reqDTO.getAuthorityCode())
                .eq(AuthorizationRecordDO::getAuthorizationStaffGuid, userDO.getGuid())
                .eq(AuthorizationRecordDO::getOperatorGuid, UserContextUtils.getUserGuid())
        );
        recordDO.setUseCount(count + 1);
        log.info("人脸授权记录保存：{}", JacksonUtils.writeValueAsString(recordDO));
        authorizationRecordService.save(recordDO);
    }

    private List<MenuSourceDTO> queryUserCompetence(String terminalCode, String authorizerGuid, String authorizerAccount) {
        String storeGuid = UserContextUtils.getStoreGuid();
        if (Constants.Account.ADMIN.equals(authorizerAccount)) {
            return userMapper.queryUserSourceOfAdmin(new UserSourceQuery()
                    .setTerminalCode(terminalCode)
                    .setStoreGuid(storeGuid));
        }
        return userMapper.queryUserSource(new UserSourceQuery()
                .setTerminalCode(terminalCode)
                .setStoreGuid(storeGuid)
                .setUserGuid(authorizerGuid));
    }

    private void saveAuthorizeRecord(PermissionsRespDTO permissions) {
        AuthorizationRecordDO recordDO = new AuthorizationRecordDO();
        String guid = distributedService.nextAuthorityGuid();
        recordDO.setGuid(guid);
        String authorityCode = permissions.getAuthorityCode();
        recordDO.setAuthorizationCode(authorityCode);
        recordDO.setSourceGuid("");
        AuthorityDO authority = authorityMapper.selectOne(new LambdaQueryWrapper<AuthorityDO>()
                .eq(AuthorityDO::getSourceCode, permissions.getSourceCode()));
        recordDO.setSourceName(authority.getSourceName());
        recordDO.setSourceCode(authority.getSourceCode());

        recordDO.setAuthorizationStaffGuid(permissions.getAuthorizerGuid());

        if (Constants.Account.ADMIN.equals(permissions.getAuthorizerAccount())) {
            recordDO.setAuthorizationStaffName("默认管理员");
        } else {
            UserDO userDO = userService.getOne(new LambdaQueryWrapper<UserDO>()
                    .eq(UserDO::getGuid, permissions.getAuthorizerGuid()));
            if (!ObjectUtils.isEmpty(userDO.getName())) {
                recordDO.setAuthorizationStaffName(userDO.getName());
            }
        }
        recordDO.setStoreGuid(UserContextUtils.getStoreGuid());
        recordDO.setStoreName(UserContextUtils.getStoreName());
        recordDO.setOperatorGuid(UserContextUtils.getUserGuid());
        recordDO.setOperatorName(UserContextUtils.getUserName());
        recordDO.setAuthorizationTime(LocalDateTime.now());
        recordDO.setLastUseTime(LocalDateTime.now());

        int count = authorizationRecordService.count(new LambdaQueryWrapper<AuthorizationRecordDO>()
                .eq(AuthorizationRecordDO::getAuthorizationCode, authorityCode));
        recordDO.setUseCount(count + 1);
        log.info("授权记录保存：{}", JacksonUtils.writeValueAsString(recordDO));
        authorizationRecordService.save(recordDO);
    }

}
