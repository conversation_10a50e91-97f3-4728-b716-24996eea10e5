package com.holderzone.saas.store.staff.config;

import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.extension.injector.LogicSqlInjector;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MybatisPlusConfig
 * @date 2018/12/28 上午10:39
 * @description mybatis-plus 相关配置
 * @program holder-saas-store-staff
 */
@Configuration
@MapperScan("com.holderzone.saas.store.staff.mapper")
public class MybatisPlusConfig {

    /**
     * 分页插件
     * 与sdk中的PageInterceptor冲突了，一旦配置这个，RoutingStatementHandler会创建代理类，PageInterceptor反射获取delegate就会获取不到
     */
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        return new PaginationInterceptor();
    }

    /**
     * 逻辑删除配置
     *
     * @return ISqlInjector
     */
    @Bean
    public ISqlInjector sqlInjector() {
        return new LogicSqlInjector();
    }
}
