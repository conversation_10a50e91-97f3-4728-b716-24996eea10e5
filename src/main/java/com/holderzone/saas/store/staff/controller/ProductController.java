package com.holderzone.saas.store.staff.controller;

import com.holderzone.saas.store.dto.store.store.StoreProductDTO;
import com.holderzone.saas.store.dto.user.ThemeReqDTO;
import com.holderzone.saas.store.staff.service.ProductService;
import com.holderzone.saas.store.staff.service.ProductThemeService;
import com.holderzone.saas.store.staff.utils.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className ProductController
 * @date 19-1-15 下午2:31
 * @description 企业/门店产品相关接口
 * @program holder-saas-store-staff
 */
@RestController
@RequestMapping("/product")
@Api(description = "企业/门店产品相关接口")
public class ProductController {

    private final ProductService productService;

    private final ProductThemeService productThemeService;

    @Autowired
    public ProductController(ProductService productService, ProductThemeService productThemeService) {
        this.productService = productService;
        this.productThemeService = productThemeService;
    }

    @ApiOperation(
            value = "查询企业经营类型",
            notes = "查询企业经营类型")
    @GetMapping("/query_mchnt_type")
    public String queryMchntType() {
        return productService.queryMchntType();
    }

    @ApiOperation(
            value = "根据传入的门店guid集合返回门店使用产品的基本信息",
            notes = "若门店未关联产品则使用企业授权的产品，注：一个企业或门店可使用多个产品（产品叠加）")
    @PostMapping("/query_product_by_idlist")
    public Map<String, List<StoreProductDTO>> queryProductByIdList(@RequestBody List<String> storeGuidList) {
        return productService.queryProductByIdList(storeGuidList);
    }

    @ApiOperation("根据传入的门店guid返回门店使用产品的基本信息")
    @GetMapping("/query_product_by_store_guid")
    public List<StoreProductDTO> queryProductByStoreGuid(
            @RequestParam @ApiParam("门店guid") String storeGuid,
            @RequestParam @ApiParam("是否包含企业产品") boolean withEnterpriseProduct) {
        return productService.queryProductByStoreGuid(storeGuid, withEnterpriseProduct);
    }

    @ApiOperation(
            value = "查询产品主题",
            notes = "查询产品主题")
    @PostMapping("/query_theme")
    public String queryTheme(@RequestBody @Valid ThemeReqDTO themeReqDTO) {
        return productThemeService.query(themeReqDTO.getStoreGuid(), themeReqDTO.getTerminalCode());
    }

    @PostMapping("/update_expiration_date")
    @ApiOperation(
            value = "更新企业、门店产品的过期时间",
            notes = "更新企业、门店产品的过期时间"
    )
    public Integer updateExpirationDate(@RequestParam("enterpriseGuid") String enterpriseGuid,
                                        @RequestParam("storeGuid") String storeGuid,
                                        @RequestParam("endDate") Long endDate,
                                        @RequestParam("productGuid") String productGuid) {
        String end = DateUtils.stampToDate(endDate.toString(), DateUtils.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
        return productService.updateExpirationDate(enterpriseGuid, storeGuid, end, productGuid);
    }
}