package com.holderzone.saas.store.staff.service.remote;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordConfirmDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordCreateDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordQueryAllDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className BusinessClient
 * @date 18-9-11 下午5:01
 * @description 服务间调用-营业中心相关服务
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(value = "holder-saas-store-business", fallbackFactory = BusinessClient.ServiceFallback.class)
public interface BusinessClient {

    /**
     * 根据(storeGuid&terminalId&status)查询交接班记录列表
     *
     * @param handoverRecordQueryAllDTO DTO
     * @return 交接班记录列表
     */
    @PostMapping("/handover/queryByPage")
    Page<HandoverRecordDTO> queryByPage(@RequestBody HandoverRecordQueryAllDTO handoverRecordQueryAllDTO);

    /**
     * 根据门店guid和设备固件编号查询未交班的记录
     *
     * @param storeGuid  storeGuid
     * @param terminalId 设备编号
     * @return 未交班的记录
     */
    @PostMapping("/handover/queryAll/{storeGuid}/{terminalId}")
    HandoverRecordDTO queryByUserStoreGuidAndTerminalId(@PathVariable("storeGuid") String storeGuid,
                                                        @PathVariable("terminalId") String terminalId);

    @PostMapping("/handover/queryByUserGuid")
    HandoverRecordDTO queryByUserGuid(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO);

    /**
     * 根据员工guid查询该员工当前的班次详情列表
     *
     * @param handoverRecordConfirmDTO 员工列表
     * @return 员工班次详情列表
     */
    @PostMapping("/handover/list_by_user_guid")
    @ApiOperation(value = "根据员工guid查询该员工当前的班次详情列表")
    List<HandoverRecordDTO> listByUserGuid(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO);

    @PostMapping("/handover/query_by_storeGuid_and_userGuid")
    HandoverRecordDTO queryByStoreGuidAndUserGuid(@RequestParam("storeGuid") String storeGuid, @RequestParam("userGuid") String userGuid);

    /**
     * 创建一个交接班记录（即接班/开班）
     *
     * @param handoverRecordCreateDTO DTO
     */
    @PostMapping("/handover/create")
    void create(@RequestBody HandoverRecordCreateDTO handoverRecordCreateDTO);

    @Slf4j
    @Component
    class ServiceFallback implements FallbackFactory<BusinessClient> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public BusinessClient create(Throwable cause) {
            return new BusinessClient() {
                @Override
                public Page<HandoverRecordDTO> queryByPage(HandoverRecordQueryAllDTO handoverRecordQueryAllDTO) {
                    log.error(HYSTRIX_PATTERN, "queryByPage", JacksonUtils.writeValueAsString(handoverRecordQueryAllDTO),
                            ThrowableUtils.asString(cause));
                    throw new BusinessException("调用服务查询交接班记录失败");
                }

                @Override
                public HandoverRecordDTO queryByUserStoreGuidAndTerminalId(String storeGuid, String terminalId) {
                    log.error(HYSTRIX_PATTERN, "queryByUserStoreGuidAndTerminalId", "storeGuid为：{}，terminalId为：{}", storeGuid, terminalId);
                    throw new BusinessException("调用服务查询交接班信息失败");
                }

                @Override
                public HandoverRecordDTO queryByUserGuid(HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
                    log.error(HYSTRIX_PATTERN, "queryByUserGuid", "入参：" + JacksonUtils.writeValueAsString(handoverRecordConfirmDTO));
                    throw new BusinessException("调用服务查询交接班信息失败");
                }

                @Override
                public List<HandoverRecordDTO> listByUserGuid(HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
                    log.error(HYSTRIX_PATTERN, "listByUserGuid", JacksonUtils.writeValueAsString(handoverRecordConfirmDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public HandoverRecordDTO queryByStoreGuidAndUserGuid(String storeGuid, String userGuid) {
                    log.error(HYSTRIX_PATTERN, "queryByStoreGuidAndUserGuid", "storeGuid：" + storeGuid + "userGuid" + userGuid);
                    throw new BusinessException("queryByStoreGuidAndUserGuid方法执行失败");
                }

                @Override
                public void create(HandoverRecordCreateDTO handoverRecordCreateDTO) {
                    log.error(HYSTRIX_PATTERN, "create", JacksonUtils.writeValueAsString(handoverRecordCreateDTO),
                            ThrowableUtils.asString(cause));
                    throw new BusinessException("调用服务开班失败");
                }
            };
        }
    }
}

