package com.holderzone.saas.store.staff.service.remote;

import com.holderzone.framework.util.ThrowableUtils;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UserClient
 * @date 18-8-31 上午9:04
 * @description 服务间调用-员工相关服务
 * @program holder-saas-store-store
 */
@Component
@FeignClient(value = "authorization-service", fallbackFactory = AuthClient.ServiceFallBack.class)
public interface AuthClient {

    @DeleteMapping("/kickout/{userGuid}")
    void delToken(@PathVariable("userGuid") String userGuid);

//    @PostMapping("/login")
//    String login(LoginDTO loginDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<AuthClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public AuthClient create(Throwable throwable) {
            return new AuthClient() {
                @Override
                public void delToken(String userGuid) {
                    log.error(HYSTRIX_PATTERN, "delToken", "userGuid为：" + userGuid, ThrowableUtils.asString(throwable));
                }

//                @Override
//                public String login(LoginDTO loginDTO) {
//                    log.error(HYSTRIX_PATTERN, "login", JacksonUtils.writeValueAsString(loginDTO), ThrowableUtils.asString(throwable));
//                    throw new BusinessException("调用authorization-service服务login方法熔断");
//                }
            };
        }
    }
}
