package com.holderzone.saas.store.staff.entity.query;


import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class UserCondQuery implements Serializable {

    private static final long serialVersionUID = -3035046977692643979L;

    /**
     * 用户复选的每个节点（企业或组织或门店）及其子节点的guid列表的集合
     */
    private List<String> orgGuidsWithChildren;

    /**
     * 搜索关键字：姓名、手机号、员工帐号
     */
    private String searchKey;

    /**
     * 创建人GUID
     */
    private String createStaffGuid;

    /**
     * 当前用户guid
     */
    private String currentStaffGuid;

    private Boolean isEnable;

    /**
     * 离线版：专用
     * 是否返回password：1是 其他否
     * 附加判断操作员
     */
    private Integer showPassword;

    /**
     * 是否限制为操作员
     */
    private Integer isWaiter;
}
