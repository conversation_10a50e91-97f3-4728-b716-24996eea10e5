package com.holderzone.saas.store.staff.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.resource.common.dto.user.HolderAdjustUserDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.user.*;
import com.holderzone.saas.store.dto.user.req.UserFaceInputReqDTO;
import com.holderzone.saas.store.dto.user.resp.UserAuthorityBriefDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.dto.user.resp.UserFaceDTO;
import com.holderzone.saas.store.staff.controller.AuthDTO;
import com.holderzone.saas.store.staff.entity.domain.UserDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UserService
 * @date 2018/8/17 11:16
 * @description 员工服务接口
 * @program holder-saas-store-staff
 */
public interface UserService extends IService<UserDO> {

    /**
     * 自动生成帐号
     *
     * @return
     */
    String newAccount();

    /**
     * 自动生成授权码
     *
     * @return
     */
    String newAuthCode();

    /**
     * 新增员工
     *
     * @param userDTO userDTO
     * @return 新建成功的员工guid
     */
    String create(UserDTO userDTO);

    /**
     * 同步holder账号
     */
    void syncHolderUser(List<String> organizationIds);

    /**
     * 云端新增员工，同步至当前系统
     *
     * @param userDTO
     */
    void createFromCloud(UserDTO userDTO);

    void updateFromCloud(UserDTO userDTO);

    void createBatchFromCloud(List<UserDO> userList);

    void updateBatchFromCloud(List<UserDO> userList);

    /**
     * 生成用户职位字典
     */
    UserOfficeDTO newUserOffice(UserOfficeDTO userOfficeDTO);

    /**
     * 查询用户职位Spiner
     *
     * @return
     */
    List<UserOfficeDTO> userOfficeSpinner();

    /**
     * 更新员工信息
     *
     * @param userDTO DTO
     */
    String update(UserDTO userDTO);

    /**
     * 更新user表的更新时间
     */
    void updateTimeByGuid(String guid);

    /**
     * 启用禁用员工
     *
     * @param userDTO
     */
    void enableOrDisable(UserDTO userDTO);

    /**
     * 删除员工
     *
     * @param userDTO
     */
    void delete(UserDTO userDTO);

    /**
     * 修改密码
     *
     * @param updatePwdDTO DTO
     */
    void updatePwd(UpdatePwdDTO updatePwdDTO);

    /**
     * 忘记密码接口实现（发短信、修改密码）
     *
     * @param resetPwdDTO
     * @return 员工信息
     */
    void resetPwd(ResetPwdDTO resetPwdDTO);

    /**
     * 云端修改员工，同步至当前系统
     *
     * @param userDTO
     */
    void resetPwdFromCloud(UserDTO userDTO);

    /**
     * 分页查询员工信息
     *
     * @param userQueryDTO 查询参数
     * @return Page<UserDTO>
     */
    Page<UserDTO> pageQuery(UserQueryDTO userQueryDTO);

    /**
     * 根据userGuid 查询用户信息
     */
    UserDO getByUserGuid(String userGuid);

    /**
     * 根据用户guid查询用户相关信息
     *
     * @param userDTO 用户guid
     * @return 员工信息
     */
    UserDTO query(UserDTO userDTO);

    /**
     * app登陆验证接口
     *
     * @param validateDTO DTO
     * @return ValidateRespDTO
     */
    ValidateRespDTO validate(ValidateDTO validateDTO);

    /**
     * 鉴权
     *
     * @param authDTO
     * @return
     */
    boolean checkUserAuthentication(AuthDTO authDTO);

    List<UserDTO> listUser(List<String> userGuidList);

    boolean deleteUserDownStreamOp(String userGuid);

    /***
     *  门店查店员
     * @param userQueryDTO 请求参数
     * @return 返回参数
     */
    List<UserDTO> findByStoreGuid(UserQueryDTO userQueryDTO);

    /**
     * 查询全部员工集合
     */
    List<UserDO> findAll();

    /**
     * 通过组织guid查询holder员工
     */
    List<HolderUserDTO> findHolderUserByOrganizationIds(List<String> organizationIds);

    /**
     * 手机号批量删除员工
     *
     * @param userDTO 手机号
     */
    List<UserDTO> batchDelete(UserDTO userDTO);

    void deleteByPhone(String phone, Boolean integrateFlag);

    UserFaceDTO getUserFaceInfo(SingleDataDTO dto);

    void inputFace(UserFaceInputReqDTO reqDTO);

    /**
     * 查询门店有权限的员工信息
     *
     * @param storeGuid 门店Guid
     * @return 员工信息集合
     */
    List<UserBriefDTO> storeUsers(String storeGuid);

    /**
     * 查询接收系统短信通知的员工信息
     *
     * @return 员工信息集合
     */
    List<UserAuthorityBriefDTO> ReceiveUsers();
}