package com.holderzone.saas.store.staff.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.resource.common.dto.authorization.ProductResource;
import com.holderzone.saas.store.staff.entity.domain.StoreSourceDO;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MenuService
 * @date 2018/8/17 17:04
 * @description 企业/门店关联资源相关操作
 * @program holder-saas-store-staff
 */
public interface StoreSourceService extends IService<StoreSourceDO> {

    boolean saveProductAuth(ProductResource productResource, String storeGuid);

    boolean updateProductAuth(ProductResource productResource, String storeGuid);

    void deleteProductAuth(String storeGuid, String productGuid, String chargeGuid);
}
