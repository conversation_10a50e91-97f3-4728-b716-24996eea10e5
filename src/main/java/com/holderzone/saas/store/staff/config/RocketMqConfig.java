package com.holderzone.saas.store.staff.config;

public final class RocketMqConfig {

    // 用户相关
    public static final String USER_SYNC_TOPIC = "user-sync-topic";

    public static final String USER_SYNC_UPLOAD_TAG = "user-sync-upload-tag";

    public static final String USER_SYNC_DOWNLOAD_TAG = "user-sync-download-tag";

    public static final String USER_SYNC_DOWNLOAD_CONSUMER_GROUP = "user-sync-download-consumer-group";

    // 菜单相关
    public static final String MENU_ASYNC_TOPIC = "menu-async-topic";

    // 云端对菜单的新增、修改、删除、修改下级url会推送修改的菜单（菜单增量推送），并用tag区分全部菜单
    public static final String MENU_ASYNC_TAG = "menu-async-tag";

    // 云端对企业或门店授权时会推送全部菜单（菜单全量推送），并用tag区分
    public static final String MENU_ALL_ASYNC_TAG = "menu-all-async-tag";

    public static final String MENU_SYNC_CONSUMER_GROUP = "menu-sync-consumer-group";

    // 产品授权相关
    public static final String PRODUCT_AUTH_ASYNC_TOPIC = "PRODUCT-AUTH-ASYNC-TOPIC";

    public static final String PRODUCT_AUTH_ASYNC_TAG = "PRODUCT-AUTH-ASYNC-TAG";

    public static final String PRODUCT_AUTH_CONSUMER_GROUP = "PRODUCT-AUTH-ASYNC-TOPIC";

    // 产品基本信息相关
    public static final String HOLDER_SAAS_PRODUCT_TOPIC = "holder-saas-product-topic";

    public static final String HOLDER_SAAS_PRODUCT_TAG = "holder-saas-product-tag";

    public static final String PRODUCT_CONSUMER_GROUP = "PRODUCT_CONSUMER_GROUP";

    // 产品主题相关

    public static final String HOLDER_SAAS_THEME_TOPIC = "holder-saas-theme-topic";

    public static final String HOLDER_SAAS_THEME_TAG = "holder-saas-theme-tag";

    public static final String THEME_CONSUMER_GROUP = "THEME_CONSUMER_GROUP";

    // 其他

    public static final String DOWNSTREAM_CONTEXT = "downstream-context";

    public static final String DOWNSTREAM_STORE_TOPIC = "downstream-store-topic";

    public static final String DOWNSTREAM_STORE_CREATE_TAG = "downstream-store-create-tag";

    public static final String DOWNSTREAM_STORE_GRANT_MANAGE_STORE_GROUP = "downstream-store-grant-manage-store-group";

}
