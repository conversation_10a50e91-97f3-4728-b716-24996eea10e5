package com.holderzone.saas.store.staff.service.impl;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.user.AioPermissionDTO;
import com.holderzone.saas.store.dto.user.RolePermissionDTO;
import com.holderzone.saas.store.staff.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RedisServiceImpl
 * @date 18-11-16 上午9:54
 * @description Redis接口实现
 * @program holder-saas-store-staff
 */
@Slf4j
@Service
@SuppressWarnings("unchecked")
public class RedisServiceImpl implements RedisService {

    @Autowired
    private static RedisService staticRedisService;

    @Autowired
    private RedisTemplate redisTemplate;
//
//    @Autowired
//    private UserRoleClient userRoleClient;

    /**
     * redis key模版
     */
    private final String REDIS_KEY = "staff:permission:{0}:{1}:{2}";

    // 这里也可改成注入构造方法来替代
    @PostConstruct
    public void initRedisService() {
        staticRedisService = this;
    }

    /**
     * 根据roleGuid删除相关缓存
     *
     * @param roleGuid roleGuid
     */
    static void deleteAllPermissionByRoleGuid(String roleGuid) {
        staticRedisService.deletePermissionByRoleGuid(roleGuid);
        // 删除该角色关联的用户权限缓存（一体机与商户后台）
        // todo 这里怎么处理
        List<String> userGuidList = Collections.emptyList();
//        List<String> userGuidList = userRoleClient.findUser(roleGuid).stream().map(UserDTO::getUserGuid)
//                                                        .distinct().collect(Collectors.toList());
        if (userGuidList != null && !userGuidList.isEmpty()) {
            staticRedisService.deletePermissionByUserGuidList(userGuidList);
            staticRedisService.deleteAioPermissionByUserGuidList(userGuidList);
        }
    }

    @Override
    public void putPermissionByUserGuid(String userGuid, List<RolePermissionDTO> rolePermissionDTOList) {
        String redisKey = MessageFormat.format(REDIS_KEY, "merchant", "userGuid", userGuid);
        redisTemplate.opsForValue().set(redisKey, JacksonUtils.writeValueAsString(rolePermissionDTOList), 1, TimeUnit.DAYS);
    }

    @Override
    public void putPermissionByRoleGuid(String roleGuid, List<RolePermissionDTO> rolePermissionDTOList) {
        String redisKey = MessageFormat.format(REDIS_KEY, "merchant", "roleGuid", roleGuid);
        redisTemplate.opsForValue().set(redisKey, JacksonUtils.writeValueAsString(rolePermissionDTOList), 1, TimeUnit.DAYS);
    }

    @Override
    public void deletePermissionByUserGuidList(List<String> userGuidList) {
        List<String> keys = userGuidList.stream().map(p -> p = MessageFormat.format(REDIS_KEY, "merchant", "userGuid", p))
                .collect(Collectors.toList());
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
    }

    @Override
    public void deletePermissionByRoleGuid(String roleGuid) {
        String redisKey = MessageFormat.format(REDIS_KEY, "merchant", "roleGuid", roleGuid);
        redisTemplate.delete(redisKey);
    }

    @Override
    public List<RolePermissionDTO> getPermissionByUserGuid(String userGuid) {
        String redisKey = MessageFormat.format(REDIS_KEY, "merchant", "userGuid", userGuid);
        String result = (String) redisTemplate.opsForValue().get(redisKey);
        if (StringUtils.hasText(result)) {
            return JacksonUtils.toObjectList(RolePermissionDTO.class, result);
        }
        return null;
    }

    @Override
    public List<RolePermissionDTO> getPermissionByRoleGuid(String roleGuid) {
        String redisKey = MessageFormat.format(REDIS_KEY, "merchant", "roleGuid", roleGuid);
        String result = (String) redisTemplate.opsForValue().get(redisKey);
        if (StringUtils.hasText(result)) {
            return JacksonUtils.toObjectList(RolePermissionDTO.class, result);
        }
        return null;
    }

    @Override
    public void putAioPermissionByUserGuid(String userGuid, List<AioPermissionDTO> aioPermissionDTOList) {
        String redisKey = MessageFormat.format(REDIS_KEY, "app", "userGuid", userGuid);
        redisTemplate.opsForValue().set(redisKey, JacksonUtils.writeValueAsString(aioPermissionDTOList), 1, TimeUnit.DAYS);
    }

    @Override
    public void deleteAioPermissionByUserGuidList(List<String> userGuidList) {
        List<String> keys = userGuidList.stream().map(p -> p = MessageFormat.format(REDIS_KEY, "app", "userGuid", p))
                .collect(Collectors.toList());
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
    }

    @Override
    public List<AioPermissionDTO> getAioPermissionByUserGuid(String userGuid) {
        String redisKey = MessageFormat.format(REDIS_KEY, "app", "userGuid", userGuid);
        String result = (String) redisTemplate.opsForValue().get(redisKey);
        if (StringUtils.hasText(result)) {
            return JacksonUtils.toObjectList(AioPermissionDTO.class, result);
        }
        return null;
    }
}
