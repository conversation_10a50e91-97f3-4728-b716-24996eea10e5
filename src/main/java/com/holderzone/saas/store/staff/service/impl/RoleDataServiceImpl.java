package com.holderzone.saas.store.staff.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.enums.TerminalTypeEnum;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.resource.common.dto.extension.BaseDictionaryDTO;
import com.holderzone.resource.common.enums.CommercialActivitiesEnum;
import com.holderzone.saas.store.dto.user.*;
import com.holderzone.saas.store.enums.user.*;
import com.holderzone.saas.store.staff.entity.constant.Constants;
import com.holderzone.saas.store.staff.entity.domain.MenuDO;
import com.holderzone.saas.store.staff.entity.domain.RoleSourceDO;
import com.holderzone.saas.store.staff.entity.domain.StoreSourceDO;
import com.holderzone.saas.store.staff.mapper.MenuMapper;
import com.holderzone.saas.store.staff.mapper.RoleSourceMapper;
import com.holderzone.saas.store.staff.mapper.StoreSourceMapper;
import com.holderzone.saas.store.staff.mapstruct.MenuMapStruct;
import com.holderzone.saas.store.staff.service.RoleDataService;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className RoleServiceImpl
 * @date 19-1-15 下午2:31
 * @description 角色-操作权限相关实现
 * @program holder-saas-store-staff
 */
@Slf4j
@Service
public class RoleDataServiceImpl extends ServiceImpl<RoleSourceMapper, RoleSourceDO> implements RoleDataService {
    private static final String HOME_MODULE_DISPLAY = "/home_module_display";
    @Autowired
    private RoleSourceMapper roleSourceMapper;
    @Autowired
    private StoreSourceMapper storeSourceMapper;
    @Autowired
    private MenuMapper menuMapper;
    @Autowired
    private MenuMapStruct menuMapStruct;
    @Resource
    private EnterpriseClient enterpriseClient;

    @Override
    public List<TerminalDTO> queryRoleTerminal(String roleGuid) {
        Set<String> roleTerminal;
        // 管理员默认所有终端已有
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            roleTerminal = roleSourceMapper.selectList(null).stream().map(RoleSourceDO::getTerminalGuid).collect(Collectors.toSet());
        } else {
            roleTerminal = roleSourceMapper.selectList(new LambdaQueryWrapper<RoleSourceDO>().eq(RoleSourceDO::getRoleGuid, roleGuid))
                    .stream().map(RoleSourceDO::getTerminalGuid).collect(Collectors.toSet());
        }

        List<StoreSourceDO> allSourceList = storeSourceMapper.selectList(null);

        List<TerminalDTO> result = new ArrayList<>();
        allSourceList.stream().collect(Collectors.groupingBy(o -> o.getTerminalGuid() + "_" + o.getTerminalCode() + "_" + o.getTerminalName()))
                .forEach((k, v) -> {
                    String[] terminalInfo = k.split("_");
                    if (!(Objects.equals("null", terminalInfo[0]) || Objects.equals("null", terminalInfo[1]) || Objects.equals("null", terminalInfo[2]))) {
                        result.add(new TerminalDTO().setTerminalGuid(terminalInfo[0]).setTerminalCode(terminalInfo[1]).setTerminalName(terminalInfo[2])
                                .setIsChecked(roleTerminal.contains(terminalInfo[0])));
                    }
                });
        return result;
    }

    /**
     * 查询所有终端-菜单-资源信息，并根据roleGuid筛选出该角色已有的终端/资源（已有的信息的isChecked字段为true）
     *
     * @param roleGuid 角色guid
     * @return 终端-模块-资源信息
     */
    @Override
    public DefaultMenuChecked queryRoleData(String roleGuid, String terminalGuid) {
        // 该终端下所有的资源信息
        List<StoreSourceDO> storeSourceList = storeSourceMapper
                .selectList(new LambdaQueryWrapper<StoreSourceDO>()
                        .eq(StoreSourceDO::getTerminalGuid, terminalGuid)
                        .ne(StoreSourceDO::getSourceUrl, HOME_MODULE_DISPLAY));
        if (CollectionUtils.isEmpty(storeSourceList)) {
            throw new BusinessException("当前企业产品没有资源");
        }
        Set<StoreSourceDO> distincted = storeSourceList.stream().collect(
                () -> new TreeSet<>(Comparator.comparing(StoreSourceDO::getSourceGuid)
                        .thenComparing(StoreSourceDO::getModuleGuid)),
                Set::add,
                TreeSet::addAll
        );
        if (CollectionUtils.isEmpty(distincted)) {
            throw new BusinessException("当前企业产品没有资源");
        }

        EnterpriseQueryDTO queryDTO = new EnterpriseQueryDTO();
        queryDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        // 连锁企业排除部分权限
        EnterpriseDTO enterprise = enterpriseClient.findEnterprise(queryDTO);
        if (Objects.equals(EnterpriseDTO.ManagementModel.CHAIN.name(), enterprise.getManagementModel().name())) {
            distincted.removeIf(d -> SourceFilterEnum.getAllSourceCode().contains(d.getSourceCode()));
            // 去掉新建单品；sourceCode: "item_store_create"
//            distincted.removeIf(d -> Objects.equals("item_store_create", d.getSourceCode()));
            // 去掉excel批量导入；sourceCode: "store_batch_import_from_excel"
//            distincted.removeIf(d -> Objects.equals("store_batch_import_from_excel", d.getSourceCode()));
            // 批量导入商品 sourceCode: "item_store_batch_import"
//            distincted.removeIf(d -> Objects.equals("item_store_batch_import", d.getSourceCode()));
            log.info("连锁企业权限过滤完成");
        }
        // 该终端下所有需要鉴权的模块id集合
        Set<String> needCheckAuthModuleList = storeSourceList.stream()
                .filter(p -> "1".equals(p.getModuleType()))
                .map(StoreSourceDO::getModuleGuid)
                .collect(Collectors.toSet());

        // 该角色该终端下的菜单与服务资源信息对应关系（已拥有的）
        LambdaQueryWrapper<RoleSourceDO> wrapper = new LambdaQueryWrapper<RoleSourceDO>()
                .eq(RoleSourceDO::getRoleGuid, roleGuid)
                .eq(RoleSourceDO::getTerminalGuid, terminalGuid);
        List<RoleSourceDO> roleSourceList = roleSourceMapper.selectList(wrapper);
        Map<String, Set<String>> sourceGuidSet = roleSourceList.stream()
                .collect(Collectors.toMap(
                        RoleSourceDO::getMenuGuid,
                        v -> Sets.newHashSet(v.getSourceGuid()),
                        (Set<String> oldSet, Set<String> newSet) -> {
                            newSet.addAll(oldSet);
                            return newSet;
                        }
                ));
        // 该终端下的所有需要显示的菜单，并过滤到所有不需要鉴权的菜单
        List<MenuDO> menuDOS = menuMapper.selectList(
                new LambdaQueryWrapper<MenuDO>().eq(MenuDO::getTerminalGuid, terminalGuid)
                        .eq(MenuDO::getIsEnable, true));

        List<MenuDTO> allMenuList = menuMapStruct
                .menuDOList2DTOList(menuDOS.stream()
                        .filter(p -> !StringUtils.hasText(p.getModuleGuid())
                                || needCheckAuthModuleList.contains(p.getModuleGuid()))
                        .sorted(Comparator.comparing(MenuDO::getMenuSort))
                        .collect(Collectors.toList()));
        //如果菜单为空，抛出错误，避免下面No value present
        if (CollectionUtils.isEmpty(allMenuList)) {
            throw new BusinessException("该终端下没有已启用的菜单");
        }

        // 一体机报表权限code处理
        aioReportMenuHandler(distincted, roleSourceList, menuDOS, sourceGuidSet);

        // 单店企业排除部分权限
        if (Objects.equals(EnterpriseDTO.ManagementModel.SINGLE.name(), enterprise.getManagementModel().name())) {
            // 排除品牌库 menuName: "品牌库"
            allMenuList.removeIf(a -> Objects.equals("品牌库", a.getMenuName()));
            // 排除组织 menuName: "组织机构"
            //如果来自holder同步的企业不排除组织
            if (!(ObjectUtil.equal(enterprise.getRegType(), "HOLDER_PLATFORM") || ObjectUtil.equal(enterprise.getRegType(), "HOLDER_CLIENT"))) {
                allMenuList.removeIf(a -> Objects.equals("组织机构", a.getMenuName()));
            }
        }

        // 商户类型不为食堂(10003)的企业，排除食堂的权限  menuName: "食堂卡管理"
        BaseDictionaryDTO managementType = enterpriseClient.queryManagementType(UserContextUtils.getEnterpriseGuid());
        if (Objects.nonNull(managementType) && Objects.nonNull(managementType.getCode())) {
            if (Objects.equals(CommercialActivitiesEnum.CANTEEN.getCode(), managementType.getCode())) {
                // 排除食堂企业的积分模块
                allMenuList.removeIf(a -> Objects.equals("积分管理", a.getMenuName()));
            } else {
                // 排除其他类型企业的食堂权限
                allMenuList.removeIf(a -> Objects.equals("食堂卡管理", a.getMenuName()));
            }
        }

        // parentId长度最小的为最顶级菜单
        int size = allMenuList.stream()
                .min(Comparator.comparing(p -> p.getParentIds().split(",").length))
                .get().getParentIds().split(",").length;
        List<MenuDTO> firstMenuList = allMenuList.stream()
                .filter(p -> p.getParentIds().split(",").length == size)
                .sorted(Comparator.comparing(MenuDTO::getMenuSort))
                .collect(Collectors.toList());

        // 该终端下的所有模块-资源对应关系
        Map<String, List<StoreSourceDO>> moduleSourceMap = distincted.stream()
                .collect(Collectors.groupingBy(StoreSourceDO::getModuleGuid));

        Set<String> defaultCheckedMenuList = new HashSet<>();

        firstMenuList.forEach(p -> {
            Pair<Set<String>, List<MenuDTO>> result = this.formatMenuTree(allMenuList, moduleSourceMap, sourceGuidSet, p.getMenuGuid(), new HashSet<>());
            defaultCheckedMenuList.addAll(result.getKey());
            // 若存在下级菜单则对最下级菜单的sourceDTOList属性赋值，否则对当前菜单的sourceDTOList属性赋值
            if (!result.getValue().isEmpty()) {
                p.setMenus(result.getValue());
            } else {
                if (moduleSourceMap.containsKey(p.getModuleGuid())) {
                    Set<String> isCheckedList = new HashSet<>();
                    List<StoreSourceDO> storeSourceDOS = moduleSourceMap.get(p.getModuleGuid());
                    handleAuthSource(storeSourceDOS);
                    handleMtMemberAuthSource(storeSourceDOS);
                    handlePosMemberAuthSource(storeSourceDOS);
                    handleTakeawayItemMappingSource(storeSourceDOS);

                    p.setSourceDTOList(buildSource(p.getMenuGuid(), storeSourceDOS, sourceGuidSet, isCheckedList, defaultCheckedMenuList));
                    p.setIsCheckedList(CollectionUtils.isEmpty(isCheckedList) ? Collections.emptyList() : new ArrayList<>(isCheckedList));
                }
            }
        });

        // 过滤第一层没有下级菜单的菜单
        clearMenuDTOList(firstMenuList);
        return new DefaultMenuChecked().setMenuDTOList(firstMenuList).setDefaultCheckedMenuList(new ArrayList<>(defaultCheckedMenuList));
    }

    private void clearMenuDTOList(List<MenuDTO> firstMenuList) {
        firstMenuList.removeIf(e -> CollectionUtils.isEmpty(e.getMenus()) && CollectionUtils.isEmpty(e.getSourceDTOList()));
    }


    /**
     * 一体机/报表 权限菜单处理
     */
    private void aioReportMenuHandler(Set<StoreSourceDO> distincted, List<RoleSourceDO> roleSourceList,
                                      List<MenuDO> menuList, Map<String, Set<String>> sourceGuidSet) {
        Map<String, List<String>> authorityReportSourceCodeMap = Arrays.stream(AuthorityReportSourceEnum.values())
                .filter(e -> !StringUtils.isEmpty(e.getCurrentSourceCode()))
                .collect(Collectors.groupingBy(AuthorityReportSourceEnum::getOriginalSourceCode,
                        Collectors.mapping(AuthorityReportSourceEnum::getCurrentSourceCode, Collectors.toList())));
        List<String> hasSourceCodes = roleSourceList.stream().map(RoleSourceDO::getSourceCode).distinct().collect(Collectors.toList());
        Map<String, MenuDO> menuMap = menuList.stream()
                .collect(Collectors.toMap(MenuDO::getModuleGuid, Function.identity(), (key1, key2) -> key1));
        Map<String, StoreSourceDO> distinctedMap = distincted.stream()
                .collect(Collectors.toMap(StoreSourceDO::getSourceCode, Function.identity(), (key1, key2) -> key1));
        for (StoreSourceDO storeSourceDO : distincted) {
            List<String> currentSourceCodes = authorityReportSourceCodeMap.get(storeSourceDO.getSourceCode());
            if (CollectionUtils.isEmpty(currentSourceCodes)) {
                continue;
            }
            boolean isNotExist = hasSourceCodes.contains(storeSourceDO.getSourceCode()) && hasSourceCodes.stream().noneMatch(currentSourceCodes::contains);
            if (isNotExist) {
                addSourceGuidSet(distinctedMap, currentSourceCodes, menuMap, sourceGuidSet);
            }
        }
        distincted.removeIf(e -> AuthorityReportSourceEnum.ORIGINAL_SOURCE_CODES.contains(e.getSourceCode()));
    }

    private void addSourceGuidSet(Map<String, StoreSourceDO> distinctedMap, List<String> currentSourceCodes,
                                  Map<String, MenuDO> menuMap, Map<String, Set<String>> sourceGuidSet) {
        String currentSourceCode = currentSourceCodes.get(0);
        StoreSourceDO currentStoreSource = distinctedMap.get(currentSourceCode);
        if (Objects.isNull(currentStoreSource)) {
            return;
        }
        MenuDO menuDO = menuMap.get(currentStoreSource.getModuleGuid());
        if (Objects.nonNull(menuDO)) {
            Set<String> sourceGuidByMenuGuidSet = sourceGuidSet.getOrDefault(menuDO.getMenuGuid(), Sets.newHashSet());
            sourceGuidByMenuGuidSet.addAll(getCurrentSourceSourceGuidSet(distinctedMap, currentSourceCodes));
            sourceGuidSet.put(menuDO.getMenuGuid(), sourceGuidByMenuGuidSet);
        } else {
            log.error("菜单数据丢失, currentStoreSource:{}", JacksonUtils.writeValueAsString(currentStoreSource));
        }
    }

    private Set<String> getCurrentSourceSourceGuidSet(Map<String, StoreSourceDO> distinctedMap, List<String> currentSourceCodes) {
        Set<String> replaceSourceGuidSet = Sets.newHashSet();
        for (String currentSourceCode : currentSourceCodes) {
            StoreSourceDO storeSourceDO = distinctedMap.get(currentSourceCode);
            if (Objects.isNull(storeSourceDO)) {
                continue;
            }
            replaceSourceGuidSet.add(storeSourceDO.getSourceGuid());
        }
        return replaceSourceGuidSet;
    }

    private static List<SourceDTO> buildSource(String menuGuid,
                                               List<StoreSourceDO> storeSourceDOS,
                                               Map<String, Set<String>> sourceGuidSet,
                                               Set<String> isCheckedList,
                                               Set<String> defaultCheckedMenuList) {
        return storeSourceDOS.stream().map(o -> {
            if (sourceGuidSet.containsKey(menuGuid) && sourceGuidSet.get(menuGuid).contains(o.getSourceGuid())) {
                isCheckedList.add(o.getSourceGuid());
                defaultCheckedMenuList.add(menuGuid);
            }
            return new SourceDTO().setSourceGuid(o.getSourceGuid())
                    .setSourceCode(o.getSourceCode())
                    .setSourceCode(o.getSourceCode())
                    .setSourceUrl(o.getSourceUrl())
                    .setSourceName(o.getSourceName())
                    .setIsChecked(sourceGuidSet
                            .get(menuGuid) != null && sourceGuidSet
                            .get(menuGuid).contains(o.getSourceGuid()));
        }).collect(Collectors.toList());
    }

    private static void handleAuthSource(List<StoreSourceDO> storeSourceDOS) {
        List<StoreSourceDO> sourceDOList = storeSourceDOS.stream()
                .filter(s -> Objects.nonNull(AuthoritySourceEnum.getEnumBySourceCode(s.getSourceCode())) ||
                        Objects.nonNull(AuthoritySourceEnum.getEnumByConvertCode(s.getSourceCode())))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sourceDOList)) {
            storeSourceDOS.removeIf(s -> Objects.nonNull(AuthoritySourceEnum.getEnumBySourceCode(s.getSourceCode())) ||
                    Objects.nonNull(AuthoritySourceEnum.getEnumByConvertCode(s.getSourceCode())));
            Map<String, StoreSourceDO> sourceDOMap = sourceDOList.stream()
                    .collect(Collectors.toMap(StoreSourceDO::getSourceCode, Function.identity(), (v1, v2) -> v1));
            List<StoreSourceDO> sourceList = sourceDOList.stream()
                    .filter(s -> Objects.nonNull(AuthoritySourceEnum.getEnumBySourceCode(s.getSourceCode())))
                    .collect(Collectors.toList());
            sourceList.forEach(source -> {
                storeSourceDOS.add(source);
                AuthoritySourceEnum sourceEnum = AuthoritySourceEnum.getEnumBySourceCode(source.getSourceCode());
                if (!ObjectUtils.isEmpty(sourceEnum)) {
                    StoreSourceDO convert = sourceDOMap.get(sourceEnum.getConvertCode());
                    if (!ObjectUtils.isEmpty(convert)) {
                        storeSourceDOS.add(convert);
                    }
                }
            });
        }
    }

    /**
     * 处理会员营销-顾客管理-顾客列表
     * 将第三方授权同步放在首位
     */
    private static void handleMtMemberAuthSource(List<StoreSourceDO> storeSourceList) {
        List<StoreSourceDO> sourceList = storeSourceList.stream()
                .filter(s -> Objects.nonNull(s) && Objects.nonNull(MtMemberAuthSourceEnum.getEnumBySourceCode(s.getSourceCode())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sourceList)) {
            return;
        }
        storeSourceList.removeIf(s -> Objects.nonNull(s) && Objects.nonNull(MtMemberAuthSourceEnum.getEnumBySourceCode(s.getSourceCode())));
        storeSourceList.addAll(0, sourceList);
    }

    /**
     * 处理移动端-快速收银-会员支付
     * 将会员支付放在首位
     */
    private static void handlePosMemberAuthSource(List<StoreSourceDO> storeSourceList) {
        List<StoreSourceDO> sourceList = storeSourceList.stream()
                .filter(s -> Objects.nonNull(s) && Objects.nonNull(PosPaySourceEnum.getEnumBySourceCode(s.getSourceCode())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sourceList)) {
            return;
        }
        storeSourceList.removeIf(s -> Objects.nonNull(s) && Objects.nonNull(PosPaySourceEnum.getEnumBySourceCode(s.getSourceCode())));
        storeSourceList.addAll(0, sourceList);
    }

    /**
     * 处理商户后台-门店及员工-门店管理-外卖管理-查询商品/关联商品/解除关联
     */
    private static void handleTakeawayItemMappingSource(List<StoreSourceDO> storeSourceList) {
        storeSourceList.removeIf(s -> Objects.nonNull(s)
                && Objects.nonNull(TakeawayItemMappingSourceEnum.getEnumBySourceCode(s.getSourceCode())));
    }


    @Override
    public boolean saveRoleData(TerminalSourceDTO terminalSourceDTO) {
        // 报表权限 兼容新老数据处理&新老一体机包
        roleDataBeforeHandler(terminalSourceDTO);
        roleSourceMapper.delete(new LambdaQueryWrapper<RoleSourceDO>()
                .eq(RoleSourceDO::getRoleGuid, terminalSourceDTO.getRoleGuid())
                .eq(RoleSourceDO::getTerminalGuid, terminalSourceDTO.getTerminalGuid()));

        List<RoleSourceDO> roleSourceList = new ArrayList<>();
        terminalSourceDTO.getMenuDTOList().forEach(p -> p.getSourceDTOList().forEach(k -> {
            roleSourceList.add(new RoleSourceDO()
                    .setRoleGuid(terminalSourceDTO.getRoleGuid())
                    .setTerminalGuid(terminalSourceDTO.getTerminalGuid())
                    .setTerminalName(terminalSourceDTO.getTerminalName())
                    .setTerminalCode(terminalSourceDTO.getTerminalCode())
                    .setMenuGuid(p.getMenuGuid())
                    .setSourceGuid(k.getSourceGuid())
                    .setSourceCode(k.getSourceCode())
                    .setSourceUrl(k.getSourceUrl()));
        }));
        return this.saveBatch(roleSourceList);
    }


    /**
     * 保存权限前置处理
     */
    private void roleDataBeforeHandler(TerminalSourceDTO terminalSourceDTO) {
        // 一体机/报表权限处理
        aioReportHandler(terminalSourceDTO);
    }

    /**
     * 一体机/报表 权限处理
     */
    private void aioReportHandler(TerminalSourceDTO terminalSourceDTO) {
        if (StringUtils.isEmpty(terminalSourceDTO.getTerminalCode()) ||
                !Integer.valueOf(terminalSourceDTO.getTerminalCode()).equals(TerminalTypeEnum.TERMINAL_AIO.getCode())) {
            return;
        }
        List<MenuDTO> menuDTOList = terminalSourceDTO.getMenuDTOList();
        // 判断是否勾选 至少一个字段的报表权限
        List<String> currentSourceCodes = menuDTOList.stream()
                .flatMap(e -> e.getSourceDTOList().stream())
                .map(SourceDTO::getSourceCode)
                .distinct()
                .collect(Collectors.toList());
        List<String> originalSourceCodes = AuthorityReportSourceEnum.getOriginalSourceCodeByCurrentSourceCodes(currentSourceCodes);
        if (CollectionUtils.isEmpty(originalSourceCodes)) {
            return;
        }
        // 查询原资源code
        List<StoreSourceDO> storeSourceList = storeSourceMapper.querySourceByCodes(originalSourceCodes);
        if (CollectionUtils.isEmpty(storeSourceList)) {
            return;
        }
        // 去重
        Map<String, StoreSourceDO> storeSourceMap = storeSourceList.stream()
                .collect(Collectors.toMap(StoreSourceDO::getSourceCode, Function.identity(), (key1, key2) -> key1));
        List<StoreSourceDO> distinctStoreSourceList = new ArrayList<>(storeSourceMap.values());
        // 查询menuGuid
        List<String> moduleGuidList = distinctStoreSourceList.stream()
                .map(StoreSourceDO::getModuleGuid)
                .distinct()
                .collect(Collectors.toList());
        List<MenuDO> menuList = menuMapper.selectList(new LambdaQueryWrapper<MenuDO>()
                .in(MenuDO::getModuleGuid, moduleGuidList)
                .eq(MenuDO::getIsEnable, true));
        if (CollectionUtils.isEmpty(menuList)) {
            return;
        }
        // 添加到角色
        appendMenuDTOList(menuList, menuDTOList, distinctStoreSourceList);
    }

    /**
     * 添加到角色
     */
    private void appendMenuDTOList(List<MenuDO> menuList, List<MenuDTO> menuDTOList, List<StoreSourceDO> distinctStoreSourceList) {
        // 添加到角色
        Map<String, MenuDO> menuMap = menuList.stream()
                .collect(Collectors.toMap(MenuDO::getModuleGuid, Function.identity(), (key1, key2) -> key1));
        for (StoreSourceDO storeSourceDO : distinctStoreSourceList) {
            MenuDO menuDO = menuMap.get(storeSourceDO.getModuleGuid());
            if (Objects.isNull(menuDO)) {
                continue;
            }
            MenuDTO menuDTO = menuDTOList.stream()
                    .filter(e -> menuDO.getMenuGuid().equals(e.getMenuGuid()))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(menuDTO)) {
                menuDTO = new MenuDTO();
                menuDTO.setMenuGuid(menuDO.getMenuGuid());
                menuDTO.setSourceDTOList(Lists.newArrayList());
                menuDTOList.add(menuDTO);
            }
            List<SourceDTO> sourceDTOList = menuDTO.getSourceDTOList();
            SourceDTO sourceDTO = sourceDTOList.stream()
                    .filter(e -> storeSourceDO.getSourceGuid().equals(e.getSourceGuid()))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(sourceDTO)) {
                sourceDTO = new SourceDTO();
                sourceDTO.setSourceGuid(storeSourceDO.getSourceGuid());
                sourceDTO.setSourceCode(storeSourceDO.getSourceCode());
                sourceDTO.setSourceUrl(storeSourceDO.getSourceUrl());
                log.info("保存角色授权信息, 一体机报表适配老权限, sourceDTO:{}", JacksonUtils.writeValueAsString(sourceDTO));
                sourceDTOList.add(sourceDTO);
            }
        }
    }

    @Override
    public boolean batchSaveRoleData(List<RoleSourceDO> roleSourceDOList) {
        return this.saveBatch(roleSourceDOList);
    }

    /**
     * 将扁平菜单转换为tree结构，同时将最底级菜单底下的所有资源set进去，同时标记该角色该终端下的服务资源是否已拥有（已拥有的isChecked属性设为true）
     *
     * @param menuDTOList           扁平菜单结构
     * @param moduleSourceMap       该终端下模块-服务资源对应关系
     * @param sourceGuidSet         该角色该终端下已有的菜单-服务资源对应关系
     * @param parentMenuId          父级菜单id（菜单扁平集合中，parentIds数组长度最短的为最顶级）
     * @param defaultCheckedMenuSet 若最底级菜单下存在服务，则提取该菜单的guid组成集合（前端需要）
     * @return Pair中key为所有的有服务资源的最底级菜单集合，value为tree结构的菜单
     */
    private Pair<Set<String>, List<MenuDTO>> formatMenuTree(List<MenuDTO> menuDTOList, Map<String, List<StoreSourceDO>> moduleSourceMap,
                                                            Map<String, Set<String>> sourceGuidSet, String parentMenuId, Set<String> defaultCheckedMenuSet) {
        List<MenuDTO> firstChildList = new ArrayList<>();
        List<MenuDTO> notFirstChildList = menuDTOList.stream().filter(p -> {
            boolean flag = p.getParentIds().endsWith(parentMenuId);
            if (flag) {
                firstChildList.add(p);
            }
            return !flag;
        }).collect(Collectors.toList());

        if (!firstChildList.isEmpty()) {
            firstChildList.forEach(p -> {
                Pair<Set<String>, List<MenuDTO>> pair = this
                        .formatMenuTree(notFirstChildList, moduleSourceMap, sourceGuidSet, String.valueOf(p.getMenuGuid()), defaultCheckedMenuSet);
                p.setMenus(pair.getValue());
            });
        }
        if (!firstChildList.isEmpty()) {
            firstChildList.forEach(p -> {
                if (moduleSourceMap.containsKey(p.getModuleGuid())) {
                    Set<String> isCheckedList = new HashSet<>();
                    List<StoreSourceDO> storeSourceDOS = moduleSourceMap.get(p.getModuleGuid());
                    handleAuthSource(storeSourceDOS);
                    handleMtMemberAuthSource(storeSourceDOS);
                    handlePosMemberAuthSource(storeSourceDOS);
                    handleTakeawayItemMappingSource(storeSourceDOS);
                    p.setSourceDTOList(new ArrayList<>(storeSourceDOS.stream()
                            .map(o -> {
                                if (sourceGuidSet.containsKey(p.getMenuGuid())) {
                                    if (sourceGuidSet.get(p.getMenuGuid()).contains(o.getSourceGuid())) {
                                        defaultCheckedMenuSet.add(p.getMenuGuid());
                                        isCheckedList.add(o.getSourceGuid());
                                    }
                                }
                                return new SourceDTO().setSourceGuid(o.getSourceGuid())
                                        .setSourceCode(o.getSourceCode())
                                        .setSourceCode(o.getSourceCode())
                                        .setSourceUrl(o.getSourceUrl())
                                        .setSourceName(o.getSourceName())
                                        .setIsChecked(sourceGuidSet
                                                .get(p.getMenuGuid()) != null && sourceGuidSet
                                                .get(p.getMenuGuid()).contains(o.getSourceGuid()));
                            }).collect(Collectors.toList())));
                    p.setIsCheckedList(CollectionUtils.isEmpty(isCheckedList) ? Collections.emptyList() : new ArrayList<>(isCheckedList));
                }
            });
            return new Pair<>(defaultCheckedMenuSet, firstChildList);
        }
        return new Pair<>(Collections.emptySet(), Collections.emptyList());
    }
}
