package com.holderzone.saas.store.staff.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hss_product")
public class ProductDO implements Serializable {

    private static final long serialVersionUID = 6676743751346682213L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 产品GUID
     */
    private String productGuid;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品类型：0=时间，1=数量
     */
    private Integer productType;

    /**
     * 产品规格GUID
     */
    private String chargeGuid;

    /**
     * 商家经营类型编码
     */
    private String mchntTypeCode;

    /**
     * 产品授权起始日期
     */
    private LocalDate gmtProductStart;

    /**
     * 产品授权截止日期
     */
    private LocalDate gmtProductEnd;

    /**
     * 是否启用:0/禁用,1/启用
     */
    private Boolean isEnable;

    /**
     * 是否删除:0=未删除,1=已删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
