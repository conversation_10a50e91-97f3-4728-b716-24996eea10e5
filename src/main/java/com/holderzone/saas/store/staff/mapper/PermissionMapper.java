package com.holderzone.saas.store.staff.mapper;

import com.holderzone.saas.store.staff.entity.domain.PermissionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PermissionMapper
 * @date 2018/11/15 上午11:50
 * @description Permission表相关接口
 * @program holder-saas-store-staff
 */
@Mapper
@Repository
public interface PermissionMapper {
    int deletePermission(@Param("roleGuid") String roleGuid);

    List<PermissionDO> selectPermissionByRoleGuid(@Param("roleGuid") String roleGuid);

    List<PermissionDO> selectPermissionByRoleGuidList(List<String> roleGuidList);

    int batchInsert(List<PermissionDO> permissionDOList);
}
