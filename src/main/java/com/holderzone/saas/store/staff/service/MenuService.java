package com.holderzone.saas.store.staff.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.resource.common.dto.product.MenuDTO;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import com.holderzone.saas.store.staff.entity.domain.MenuDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MenuService
 * @date 2018/8/17 17:04
 * @description 菜单基础数据相关操作
 * @program holder-saas-store-staff
 */
public interface MenuService extends IService<MenuDO> {

    /**
     * 批量插入菜单（删除 + 插入）
     *
     * @param menuList
     * @return
     */
    boolean insertMenu(List<MenuDTO> menuList);

    /**
     * 删除指定菜单
     *
     * @param menuGuid
     * @return
     */
    boolean deleteMenu(String menuGuid);

    /**
     * 更新指定菜单
     *
     * @param menuDTO
     * @return
     */
    boolean updateMenu(MenuDTO menuDTO);

    /**
     * 菜单绑定页面（菜单关联下级url），处理逻辑为新建相同数量的菜单来建立菜单与页面（下级url）的一对一关系
     *
     * @param menuList menuDTOList
     * @return 执行结果
     */
    boolean bindMenuAndPage(List<MenuDTO> menuList);

    /**
     * 获取指定用户的权限列表
     *
     * @param terminalCode
     * @return
     */
    List<MenuSourceDTO> getSourceByUser(String terminalCode);

    /**
     * 获取指定菜单的权限列表
     *
     * @param menuGuid
     * @return
     */
    List<MenuSourceDTO> getSourceByMenu(String menuGuid);

    /**
     * 根据pageUrl查询
     *
     * @param pageUrl
     * @return
     */
    List<MenuSourceDTO> getPageUrlByMenu(String pageUrl);

    /**
     * 查询指定用户的可访问模块列表
     *
     * @param terminalCode
     * @return
     */
    List<MenuSourceDTO> getModuleSourceByUser(String terminalCode);

    /**
     * 根据用户guid和终端code返回用户能查看的菜单树
     *
     * @param userGuid     userGuid
     * @param terminalCode 终端code
     * @return 菜单树
     */
    List<com.holderzone.saas.store.dto.user.MenuDTO> getMerchantMenu(String userGuid, String terminalCode);

    List<String> queryUserSourceOnOut(Integer terminalCode, String userOrPhone, String enterpriseGuid);
}
