package com.holderzone.saas.store.staff.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.dto.user.RoleDTO;
import com.holderzone.saas.store.staff.entity.domain.UserRoleDO;
import com.holderzone.saas.store.staff.mapper.UserRoleMapper;
import com.holderzone.saas.store.staff.service.DistributedService;
import com.holderzone.saas.store.staff.service.RedisService;
import com.holderzone.saas.store.staff.service.UserRoleService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className GrantServiceImpl
 * @date 2018/8/21 11:01
 * @description 用户-角色服务接口实现
 * @program holder-saas-store-staff
 */
@Service
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRoleDO> implements UserRoleService {

    @Autowired
    private RedisService redisService;

    private final DistributedService distributedService;

    @Autowired
    public UserRoleServiceImpl(DistributedService distributedService) {
        this.distributedService = distributedService;
    }

    @Override
    public void addUserRoleRelation(String userGuid, List<RoleDTO> arrayOfRoleDTO) {
        List<String> arrayOfUserRoleGuid = distributedService.nextBatchUserRoleGuid(arrayOfRoleDTO.size());
        saveBatch(arrayOfRoleDTO.stream()
                .map(roleDTO -> {
                    String guid = arrayOfUserRoleGuid.remove(arrayOfUserRoleGuid.size() - 1);
                    return new UserRoleDO().setGuid(guid).setUserGuid(userGuid).setRoleGuid(roleDTO.getGuid());
                })
                .collect(Collectors.toList()));

        // todo 缓存怎么工作的
//        redisService.deletePermissionByUserGuidList(arrayOfRoleDTO.stream().map(UserDTO::getGuid).collect(Collectors.toList()));
//        redisService.deleteAioPermissionByUserGuidList(arrayOfRoleDTO.stream().map(UserDTO::getGuid).collect(Collectors.toList()));
    }

    @Override
    public void updateUserRoleRelation(String userGuid, List<RoleDTO> arrayOfRoleDTO) {
        deleteUserRoleRelation(userGuid);
        addUserRoleRelation(userGuid, arrayOfRoleDTO);
    }

    @Override
    public void deleteUserRoleRelation(String userGuid) {
        remove(new LambdaQueryWrapper<UserRoleDO>().eq(UserRoleDO::getUserGuid, userGuid));
    }

    @Override
    public List<RoleDTO> findRoleByUserGuid(String userGuid) {
        return null;
    }

    @Override
    public void batchDeleteUserRoleRelation(List<String> userGuidList) {
        remove(new LambdaQueryWrapper<UserRoleDO>()
                .in(CollectionUtils.isNotEmpty(userGuidList), UserRoleDO::getUserGuid, userGuidList));
    }
}
