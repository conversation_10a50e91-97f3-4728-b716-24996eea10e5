package com.holderzone.saas.store.staff.service.remote;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.resource.common.dto.user.UserDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UserCenterClient
 * @date 2018/08/14 下午4:32
 * @description 服务间调用-员工服务
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(value = "holder-saas-cloud-user", fallbackFactory = UserClient.ServiceFallback.class)
public interface UserClient {

    /**
     * 用户手机号云端验重
     *
     * @param tel 手机号
     * @return 数据库的count结果
     */
    @GetMapping("/user/countTel/{tel}")
    int queryExistTel(@PathVariable("tel") String tel, @RequestParam("userGuid") String userGuid);

    /**
     * 查询用户信息
     *
     * @param userGuid
     * @return
     */
    @GetMapping("/user/{userGuid}")
    UserDTO findUserById(@PathVariable("userGuid") String userGuid);

    @GetMapping("/user/admin/{enterpriseGuid}")
    UserDTO getAdminByEnterpriseGuid(@PathVariable("enterpriseGuid") String enterpriseGuid);

    @Slf4j
    @Component
    class ServiceFallback implements FallbackFactory<UserClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public UserClient create(Throwable throwable) {
            return new UserClient() {
                @Override
                public int queryExistTel(String tel, String userGuid) {
                    log.error(HYSTRIX_PATTERN, "tel", "tel为：" + tel, ThrowableUtils.asString(throwable));
                    throw new BusinessException("调用服务对用户手机号验重失败");
                }

                @Override
                public UserDTO findUserById(String userGuid) {
                    log.error(HYSTRIX_PATTERN, "findUserById", "userGuid为：" + userGuid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("调用服务查询用户密码失败");
                }

                @Override
                public UserDTO getAdminByEnterpriseGuid(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "findUserById", "enterpriseGuid为：" + enterpriseGuid, ThrowableUtils.asString(throwable));
                    throw new BusinessException("根据企业guid查询管理员账号信息");
                }
            };
        }
    }
}