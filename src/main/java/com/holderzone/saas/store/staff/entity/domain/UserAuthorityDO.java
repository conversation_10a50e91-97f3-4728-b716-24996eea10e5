package com.holderzone.saas.store.staff.entity.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className UserAuthorityDO
 * @date 20-12-18 上午10:32
 * @description hss_user_authority DO
 * @program holder-saas-store-staff
 */
@Accessors(chain = true)
@TableName(value = "hss_user_authority")
@Data
public class UserAuthorityDO implements Serializable {

    private static final long serialVersionUID = 5829162238319212183L;

    /**
     * guid
     */
    private String guid;

    /**
     * 被授权人guid，员工guid
     */
    private String userGuid;

    /**
     * 资源code
     */
    private String sourceCode;

    /**
     * 授权码
     */
    private String authorityCode;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 授权人guid
     */
    private String authorizerGuid;

    /**
     * 授权人账户
     */
    private String authorizerAccount;

    /**
     * 电子税务局注册人姓名
     */
    private String electronicTaxpayerName;

    /**
     * 电子税务局注册手机号
     */
    private String electronicTaxpayerPhone;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getAuthorizerGuid() {
        return authorizerGuid;
    }

    public void setAuthorizerGuid(String authorizerGuid) {
        this.authorizerGuid = authorizerGuid;
    }

    public String getAuthorizerAccount() {
        return authorizerAccount;
    }

    public void setAuthorizerAccount(String authorizerAccount) {
        this.authorizerAccount = authorizerAccount;
    }

    @Override
    public String toString() {
        return "UserAuthorityDO{" +
                "guid='" + guid + '\'' +
                ", userGuid='" + userGuid + '\'' +
                ", sourceCode='" + sourceCode + '\'' +
                ", authorityCode='" + authorityCode + '\'' +
                ", isDelete=" + isDelete +
                ", authorizerGuid='" + authorizerGuid + '\'' +
                ", authorizerAccount='" + authorizerAccount + '\'' +
                '}';
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getAuthorityCode() {
        return authorityCode;
    }

    public void setAuthorityCode(String authorityCode) {
        this.authorityCode = authorityCode;
    }


    public UserAuthorityDO(String guid, String userGuid, String sourceCode, String authorityCode) {
        this.guid = guid;
        this.userGuid = userGuid;
        this.sourceCode = sourceCode;
        this.authorityCode = authorityCode;
    }

    public UserAuthorityDO(String sourceCode, String authorityCode) {
        this.sourceCode = sourceCode;
        this.authorityCode = authorityCode;
    }

    public UserAuthorityDO(String authorityCode) {
        this.authorityCode = authorityCode;
    }

    public UserAuthorityDO() {

    }

}
