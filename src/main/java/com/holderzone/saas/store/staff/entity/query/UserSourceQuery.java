package com.holderzone.saas.store.staff.entity.query;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class UserSourceQuery {

    private String userGuid;

    private String terminalCode;

    private String menuGuid;

    private String requestUri;

    private String storeGuid;

    private LocalDate now = LocalDate.now();
}
