package com.holderzone.saas.store.staff.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.invoice.UserAuthorityInvoiceDTO;
import com.holderzone.saas.store.dto.user.UserAuthorityDTO;
import com.holderzone.saas.store.dto.user.UserAuthorityQueryDTO;
import com.holderzone.saas.store.dto.user.UserAuthoritySaveDTO;
import com.holderzone.saas.store.dto.user.UserAuthorityUpdateDTO;
import com.holderzone.saas.store.dto.user.req.AuthorizationReqDTO;
import com.holderzone.saas.store.dto.user.req.PermissionsReqDTO;
import com.holderzone.saas.store.dto.user.resp.PermissionsRespDTO;
import com.holderzone.saas.store.dto.user.resp.UserAuthorityBriefDTO;
import com.holderzone.saas.store.dto.user.resp.UserFaceDTO;
import com.holderzone.saas.store.staff.entity.domain.UserAuthorityDO;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UserService
 * @date 2018/8/17 11:16
 * @description 员工服务接口
 * @program holder-saas-store-staff
 */
public interface UserAuthorityService extends IService<UserAuthorityDO> {

    /**
     * 保存员工账号授权
     *
     * @param userAuthoritySaveDTO
     * @return 保存结果
     */
    void saveUserAuthority(UserAuthoritySaveDTO userAuthoritySaveDTO);


    /**
     * 新增权限
     *
     * @param userAuthorityUpdateDTO
     * @return 保存结果
     */
    Boolean updateAuthority(UserAuthorityUpdateDTO userAuthorityUpdateDTO) throws IOException;

    /**
     * 查询权限
     *
     * @param userAuthorityQueryDTO
     * @return 保存结果
     */
    List<UserAuthorityDTO> queryUserAuthority(UserAuthorityQueryDTO userAuthorityQueryDTO);

    /**
     * 查询开票人信息
     */
    UserAuthorityInvoiceDTO queryUserInvoice(String userGuid);


    /**
     * 根据员工、门店、终端查询可升级权限
     *
     * @param reqDTO PermissionsReqDTO
     * @return sourceCode、sourceName、sourceUrl
     */
    List<PermissionsRespDTO> queryEmployeePermissions(PermissionsReqDTO reqDTO);

    /**
     * 根据授权码和资源code校验信息，并记录
     *
     * @param reqDTO 授权码，资源code
     * @return Boolean
     */
    Boolean authorize(AuthorizationReqDTO reqDTO);

    /**
     * 根据被授权人guid删除授权关系
     *
     * @param deleteDTO 被授权人guid
     */
    Boolean deleteAuthority(AuthorizationReqDTO deleteDTO);

    /**
     * 查询所有可升级权限
     *
     * @return List<PermissionsRespDTO>
     */
    List<PermissionsRespDTO> queryAuthority();

    /**
     * 查询后台是否设置授权
     * @param reqDTO
     * @return
     */
    Integer queryUserAuthority(PermissionsReqDTO reqDTO);

    void inputFace(UserAuthoritySaveDTO userAuthoritySaveDTO);

    UserFaceDTO authorizeFace(AuthorizationReqDTO reqDTO);

    /**
     * 查询门店下是否有员工勾选自己设置的权限
     * @param userAuthorityQueryDTO userAuthorityQueryDTO
     * @return List<PermissionsRespDTO>
     */
    List<PermissionsRespDTO> queryAuthorityAnyMatch(UserAuthorityQueryDTO userAuthorityQueryDTO);

    /**
     * 根据授权码和资源code校验信息，并记录，并返回授权人信息
     *
     * @param reqDTO 授权码，资源code
     * @return UserAuthorityBriefDTO
     */
    UserAuthorityBriefDTO authorizeUser(AuthorizationReqDTO reqDTO);
}