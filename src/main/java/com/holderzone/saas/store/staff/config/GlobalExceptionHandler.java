package com.holderzone.saas.store.staff.config;

import com.holderzone.feign.spring.boot.exception.ExceptionHandlerAdapter;
import com.holderzone.saas.store.staff.exception.StoreBindingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className GlobalExceptionHandler
 * @date 2018/8/16 17:05
 * @description 统一异常拦截
 * @program holder-saas-store-staff
 */
@SuppressWarnings("Duplicates")
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler extends ExceptionHandlerAdapter {

    /**
     * 特殊处理android中的登陆验证接口异常
     *
     * @param e exception
     * @return Resp
     */
    @ExceptionHandler(value = StoreBindingException.class)
    public ResponseEntity<String> exception(StoreBindingException e) {
        String message = e.getMessage();
        log.error("android登陆验证失败，参数binding错误", e);
        return new ResponseEntity<>(message, HttpStatus.NOT_ACCEPTABLE);
    }
}
