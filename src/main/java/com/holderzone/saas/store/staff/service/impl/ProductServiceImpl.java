package com.holderzone.saas.store.staff.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.enterprise.MessageConfigDTO;
import com.holderzone.saas.store.dto.store.store.StoreProductDTO;
import com.holderzone.saas.store.enums.MchntTypeEnum;
import com.holderzone.saas.store.staff.entity.bo.ProductBasicBO;
import com.holderzone.saas.store.staff.entity.domain.ProductDO;
import com.holderzone.saas.store.staff.mapper.ProductMapper;
import com.holderzone.saas.store.staff.mapstruct.ProductMapstruct;
import com.holderzone.saas.store.staff.service.ProductService;
import com.holderzone.saas.store.staff.service.StoreSourceService;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.staff.constant.Constant.NUMBER_ONE;
import static com.holderzone.saas.store.staff.constant.Constant.NUMBER_ZERO;

@Slf4j
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, ProductDO> implements ProductService {

    private final ProductMapstruct productMapstruct;

    private final StoreSourceService storeSourceService;

    private final EnterpriseClient enterpriseClient;

    @Autowired
    public ProductServiceImpl(ProductMapstruct productMapstruct, StoreSourceService storeSourceService,
                              EnterpriseClient enterpriseClient) {
        this.productMapstruct = productMapstruct;
        this.storeSourceService = storeSourceService;
        this.enterpriseClient = enterpriseClient;
    }

    @Override
    public void saveOrUpdate(ProductBasicBO productBasic) {
        ProductDO productDO = productMapstruct.bo2Do(productBasic);
        Wrapper<ProductDO> wrapper = wrapperByGuid(
                productDO.getStoreGuid(),
                productDO.getProductGuid(),
                productDO.getChargeGuid());
        if (count(wrapper) == 0) {
            save(productDO);
            return;
        }
        update(productDO, wrapper);
    }

    @Override
    @Transactional(
            rollbackFor = {Exception.class}
    )
    public void deleteProduct(ProductBasicBO productBasic) {
        String storeGuid = productBasic.getStoreGuid();
        String productGuid = productBasic.getProductGuid();
        String chargeGuid = productBasic.getChargeGuid();
        log.info("deleteProduct,storeGuid:{}, productGuid:{}, chargeGuid:{}", storeGuid, productGuid, chargeGuid);
        //删除产品
        remove(wrapperByGuid(storeGuid, productGuid, chargeGuid));
        //删除资源
        storeSourceService.deleteProductAuth(storeGuid, productGuid, chargeGuid);
    }

    @Override
    public String queryMchntType() {
        List<ProductDO> productList = list(new LambdaQueryWrapper<ProductDO>().isNull(ProductDO::getStoreGuid));
        Optional<String> first = productList.stream()
                .map(ProductDO::getMchntTypeCode)
                .filter(StringUtils::hasText).findFirst();
        if (first.isPresent()) {
            return first.get();
        }
        productList = list(new LambdaQueryWrapper<ProductDO>().isNotNull(ProductDO::getMchntTypeCode));
        return productList.stream()
                .map(ProductDO::getMchntTypeCode)
                .filter(StringUtils::hasText).findFirst().orElse(MchntTypeEnum.CATERING.getCode());
    }

    @Override
    public List<StoreProductDTO> queryProductByStoreGuid(String storeGuid, boolean withEnterpriseProduct) {
        List<ProductDO> storeProductList = baseMapper.selectByStoreGuid(storeGuid, withEnterpriseProduct);
        return storeProductList.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<StoreProductDTO>> queryProductByIdList(List<String> storeGuidList) {
        Map<String, List<StoreProductDTO>> resultMap = new HashMap<>(16);

        // 门店授权处理
        List<ProductDO> storeProduct = list(new LambdaQueryWrapper<ProductDO>()
                .in(ProductDO::getStoreGuid, storeGuidList));
        log.info("门店产品：{}", JacksonUtils.writeValueAsString(storeProduct));
        storeProduct.stream()
                .collect(Collectors.groupingBy(ProductDO::getStoreGuid))
                .forEach((storeGuid, productListOfCurStore) -> {
                    List<StoreProductDTO> storeProductList = productListOfCurStore.stream()
                            .map(this::convertToDto).collect(Collectors.toList());
                    if (resultMap.containsKey(storeGuid)) {
                        resultMap.get(storeGuid).addAll(storeProductList);
                    } else {
                        resultMap.put(storeGuid, storeProductList);
                    }
                });


        // 企业授权处理
        List<ProductDO> erpProduct = list(new LambdaQueryWrapper<ProductDO>()
                .isNull(ProductDO::getStoreGuid));
        log.info("企业产品：{}", JacksonUtils.writeValueAsString(erpProduct));
        List<StoreProductDTO> erpProductList = erpProduct.stream()
                .map(this::convertToDto).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(erpProductList)) {
            return resultMap;
        }
        storeGuidList.forEach(storeGuid -> {
            if (resultMap.containsKey(storeGuid)) {
                resultMap.get(storeGuid).addAll(erpProductList);
            } else {
                resultMap.put(storeGuid, erpProductList);
            }
        });

        return resultMap;
    }

    private Wrapper<ProductDO> wrapperByGuid(String storeGuid, String productGuid, String chargeGuid) {
        LambdaQueryWrapper<ProductDO> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(storeGuid)) {
            wrapper.eq(ProductDO::getStoreGuid, storeGuid);
        } else {
            wrapper.isNull(ProductDO::getStoreGuid);
        }
        return wrapper
                .eq(ProductDO::getProductGuid, productGuid)
                .eq(ProductDO::getChargeGuid, chargeGuid);
    }

    private StoreProductDTO convertToDto(ProductDO productDO) {
        StoreProductDTO storeProductDTO = new StoreProductDTO();

        String enterpriseGuid = EnterpriseIdentifier.getEnterpriseGuid();
        MessageConfigDTO messageConfigDTO = enterpriseClient.getMessageInfo(enterpriseGuid);
        boolean number = !ObjectUtils.isEmpty(messageConfigDTO) && !ObjectUtils.isEmpty(productDO.getProductType()) &&
                NUMBER_ONE.equals(productDO.getProductType());
        if (number) {
            storeProductDTO.setResidueCount(messageConfigDTO.getResidueCount());
        }else {
            storeProductDTO.setResidueCount(NUMBER_ZERO);
        }
        boolean overdue = productDO.getGmtProductEnd() != null
                && LocalDate.now().compareTo(productDO.getGmtProductEnd()) > 0;
        return storeProductDTO
                .setProductName(productDO.getProductName())
                .setProductGuid(productDO.getProductGuid())
                .setProductType(productDO.getProductType())
                .setGmtProductEnd(productDO.getGmtProductEnd())
                .setGmtProductStart(productDO.getGmtProductStart())
                .setGmtCreate(productDO.getGmtCreate())
                .setOverdue(overdue);
    }

    @Override
    public Integer updateExpirationDate(String enterpriseGuid, String storeGuid, String endDate, String productGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        if (StringUtils.isEmpty(storeGuid)) {
            // 更新的是企业的产品过期时间
            return baseMapper.updateExpirationDateByProductGuid(productGuid, endDate);
        } else {
            // 更新的是门店产品的过期时间
            return baseMapper.updateExpirationDateByStoreGuidAndProductGuid(storeGuid, productGuid, endDate);
        }
    }
}
