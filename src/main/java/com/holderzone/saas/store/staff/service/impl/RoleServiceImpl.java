package com.holderzone.saas.store.staff.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.Assert;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.user.RoleDTO;
import com.holderzone.saas.store.dto.user.RoleQueryDTO;
import com.holderzone.saas.store.staff.entity.domain.RoleDO;
import com.holderzone.saas.store.staff.entity.domain.RoleSourceDO;
import com.holderzone.saas.store.staff.entity.domain.UserRoleDO;
import com.holderzone.saas.store.staff.mapper.RoleMapper;
import com.holderzone.saas.store.staff.mapper.RoleSourceMapper;
import com.holderzone.saas.store.staff.mapper.UserRoleMapper;
import com.holderzone.saas.store.staff.mapstruct.RoleMapStruct;
import com.holderzone.saas.store.staff.service.RoleDataService;
import com.holderzone.saas.store.staff.service.RoleService;
import com.holderzone.saas.store.staff.service.UserDataService;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className RoleServiceImpl
 * @date 19-1-15 下午2:31
 * @description 角色相关接口实现
 * @program holder-saas-store-staff
 */
@Slf4j
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, RoleDO> implements RoleService {

    private final RoleMapStruct roleMapStruct;

    private final RoleMapper roleMapper;

    private final UserRoleMapper userRoleMapper;

    private final RedisTemplate redisTemplate;

    private final RoleSourceMapper roleSourceMapper;

    private final RoleDataService roleDataService;

    private final UserDataService userDataService;

    @Autowired
    public RoleServiceImpl(RoleMapStruct roleMapStruct, RoleMapper roleMapper, UserRoleMapper userRoleMapper, RedisTemplate redisTemplate,
                           RoleSourceMapper roleSourceMapper, RoleDataService roleDataService, UserDataService userDataService) {
        this.roleMapStruct = roleMapStruct;
        this.roleMapper = roleMapper;
        this.userRoleMapper = userRoleMapper;
        this.redisTemplate = redisTemplate;
        this.roleSourceMapper = roleSourceMapper;
        this.roleDataService = roleDataService;
        this.userDataService = userDataService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createRole(RoleDTO roleDTO) {
        Assert.valid(roleDTO.getName().length() <= 16, "角色名字超长");
        if (this.count(new LambdaQueryWrapper<RoleDO>().eq(RoleDO::getName, roleDTO.getName())) != 0) {
            throw new BusinessException("已存在该角色名称，请修改");
        }
        try {
            RoleDO role = roleMapStruct.roleDTO2DO(roleDTO)
                    .setCreateStaffGuid(UserContextUtils.getUserGuid())
                    .setModifiedStaffGuid(UserContextUtils.getUserGuid())
                    .setGmtCreate(DateTimeUtils.now())
                    .setGmtModified(DateTimeUtils.now())
                    .setGuid(String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "role")));
            userDataService.appendRole(role.getGuid());
            return this.save(role);
        } catch (IOException e) {
            throw new BusinessException("BatchIdGenerator生成角色guid失败");
        }
    }

    @Override
    public boolean updateRole(RoleDTO roleDTO) {
        RoleDO originalRole = this.getOne(new LambdaQueryWrapper<RoleDO>().eq(RoleDO::getGuid, roleDTO.getGuid()));

        if (!originalRole.getName().equals(roleDTO.getName())
                && this.count(new LambdaQueryWrapper<RoleDO>().eq(RoleDO::getName, roleDTO.getName())) != 0) {
            throw new BusinessException("已存在该角色名称，请修改");
        }
        return this.update(roleMapStruct.roleDTO2DO(roleDTO)
                        .setModifiedStaffGuid(UserContextUtils.getUserGuid())
                        .setGmtModified(DateTimeUtils.now()),
                new LambdaQueryWrapper<RoleDO>().eq(RoleDO::getGuid, roleDTO.getGuid()));
    }

    @Override
    public Page<RoleDTO> queryPageByName(RoleQueryDTO roleQueryDTO) {
        if (StringUtils.hasText(roleQueryDTO.getRoleName())) {
            roleQueryDTO.setRoleName(roleQueryDTO.getRoleName().replace("_", "\\_"));
            roleQueryDTO.setRoleName(roleQueryDTO.getRoleName().replace("%", "\\%"));
        }
        Integer count = roleMapper.countByRoleQueryDTO(roleQueryDTO);
        if (count == null || count == 0) {
            return new Page<>(roleQueryDTO.getCurrentPage(), roleQueryDTO.getPageSize(), 0, Collections.emptyList());
        }
        return new Page<>(roleQueryDTO.getCurrentPage(), roleQueryDTO.getPageSize(), count, roleMapper.selectListByRoleQueryDTO(roleQueryDTO));
    }

    @Override
    public boolean deleteRole(String roleGuid) {
        if (this.queryExistUser(roleGuid)) {
            throw new BusinessException("角色下有账号不可删除");
        }
        return this.remove(new LambdaQueryWrapper<RoleDO>().eq(RoleDO::getGuid, roleGuid));
    }

    @Override
    public boolean queryExistUser(String roleGuid) {
        Integer result = userRoleMapper.selectCount(new LambdaQueryWrapper<UserRoleDO>().eq(UserRoleDO::getRoleGuid, roleGuid));
        return result == null || !result.equals(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyRole(String roleGuid) {
        try {
            boolean lockResult = RedissonLockUtil.tryLock(roleGuid, 3, 10);
            if (!lockResult) {
                log.error("复制角色时加锁失败");
                return false;
            }
            RoleDO originalRole = roleMapper.selectOne(new LambdaQueryWrapper<RoleDO>().eq(RoleDO::getGuid, roleGuid));
            Assert.valid(originalRole.getName().length() <= 12, "角色名字超长");
            String newRoleGuid = String.valueOf(BatchIdGenerator.getGuid(redisTemplate, "role"));
            String copyName = originalRole.getName() + "-copy";
            List<RoleDO> copiedRole = roleMapper.selectList(new LambdaQueryWrapper<RoleDO>()
                    .likeRight(RoleDO::getName, originalRole.getName() + "-copy"));
            if (!CollectionUtils.isEmpty(copiedRole)) {
                RoleDO roleDO = copiedRole.get(copiedRole.size() - 1);
                String name = roleDO.getName();
                String y = name.substring(name.lastIndexOf('y') + 1);
                if (StringUtils.hasText(y)) {
                    copyName = copyName + (Integer.parseInt(y) + 1);
                } else {
                    copyName = copyName + 1;
                }
            }
            boolean result1 = this.save(originalRole
                    .setName(copyName)
                    .setCreateStaffGuid(UserContextUtils.getUserGuid())
                    .setModifiedStaffGuid(UserContextUtils.getUserGuid())
                    .setGmtCreate(DateTimeUtils.now())
                    .setGmtModified(DateTimeUtils.now())
                    .setGuid(newRoleGuid));

            List<RoleSourceDO> newRoleSourceList = roleSourceMapper.selectList(
                    new LambdaQueryWrapper<RoleSourceDO>().eq(RoleSourceDO::getRoleGuid, roleGuid)
            )
                    .stream()
                    .peek(p -> p.setRoleGuid(newRoleGuid))
                    .collect(Collectors.toList());
            boolean result2 = roleDataService.batchSaveRoleData(newRoleSourceList);

            userDataService.appendRole(newRoleGuid);
            return result1 && result2;
        } catch (IOException e) {
            throw new BusinessException("BatchIdGenerator生成角色guid失败");
        } finally {
            RedissonLockUtil.unlock(roleGuid);
        }
    }
}
