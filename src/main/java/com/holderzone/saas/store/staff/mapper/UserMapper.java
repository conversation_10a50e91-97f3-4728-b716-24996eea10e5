package com.holderzone.saas.store.staff.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import com.holderzone.saas.store.staff.entity.domain.RoleDO;
import com.holderzone.saas.store.staff.entity.domain.UserDO;
import com.holderzone.saas.store.staff.entity.query.UserCondQuery;
import com.holderzone.saas.store.staff.entity.query.UserSourceQuery;
import com.holderzone.saas.store.staff.entity.read.UserReadDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UserMapper
 * @date 2018/01/15 下午14:519
 * @description User表相关接口
 * @program holder-saas-store-staff
 */
@Repository
public interface UserMapper extends BaseMapper<UserDO> {

    UserReadDO queryUserDetail(UserDO userDO);

    IPage<UserReadDO> pageQueryUser(IPage<UserReadDO> iPage, @Param("userCondQuery") UserCondQuery userCondQuery);

    RoleDO queryUserRolesInline(HashMap<String, String> hashMap);

    List<MenuSourceDTO> queryUserSourceOfAdmin(UserSourceQuery userSourceQuery);

    List<MenuSourceDTO> queryUserSource(UserSourceQuery userSourceQuery);

    List<MenuSourceDTO> queryUserModuleSource(UserSourceQuery userSourceQuery);

    List<String> getAllMatchedUrl(UserSourceQuery userSourceQuery);

    Integer countMatchedUrl(UserSourceQuery userSourceQuery);

    Integer countMatchedUrlOfAdmin(UserSourceQuery userSourceQuery);

    List<String> queryUserManagedTerminal(@Param("userGuid") String userGuid);

    List<String> queryUsersByTerminal(@Param("userGuidList") List<String> userGuidList, @Param("terminalCode") Integer terminalCode);

    List<UserReadDO> queryAIOUsers(@Param("storeGuid") String storeGuid);

    List<UserReadDO> findByStoreGuid(@Param("userCondQuery") UserCondQuery userCondQuery);

    void saveUser(UserDO userDO);

    void updateBatchFromCloud(@Param("userList") List<UserDO> userList);

    /**
     * 只能查企业级产品
     *
     * @param userGuid     用户id
     * @param terminalCode 终端code
     * @return 权限列表
     */
    List<MenuSourceDTO> queryUserSourceOnOut(@Param("userGuid") String userGuid, @Param("terminalCode") Integer terminalCode);

    List<MenuSourceDTO> queryUserSourceOfAdminOnOut(@Param("terminalCode")Integer terminalCode);
}
