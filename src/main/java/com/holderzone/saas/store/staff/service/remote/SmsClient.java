package com.holderzone.saas.store.staff.service.remote;

import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @className SmsClient
 * @date 18-9-7 上午10:43
 * @description 服务间调用-短信发送服务
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(value = "base-service", fallbackFactory = SmsClient.ServiceFallback.class)
public interface SmsClient {
    @PostMapping("/message/sendMessage")
    void sendMessage(@RequestBody MessageDTO messageDTO);

    @Slf4j
    @Component
    class ServiceFallback implements FallbackFactory<SmsClient> {

        @Override
        public SmsClient create(Throwable cause) {
            return messageDTO -> log.error("短信发送失败，入参：{}，错误详情：{}", JacksonUtils.writeValueAsString(messageDTO),
                                           ThrowableUtils.asString(cause));
        }
    }
}
