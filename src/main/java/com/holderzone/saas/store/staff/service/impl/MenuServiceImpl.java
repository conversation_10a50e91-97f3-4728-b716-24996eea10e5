package com.holderzone.saas.store.staff.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.product.MenuDTO;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import com.holderzone.saas.store.dto.user.req.PermissionsReqDTO;
import com.holderzone.saas.store.dto.user.resp.PermissionsRespDTO;
import com.holderzone.saas.store.enums.user.*;
import com.holderzone.saas.store.staff.config.MarketingConfig;
import com.holderzone.saas.store.staff.entity.constant.Constants;
import com.holderzone.saas.store.staff.entity.domain.MenuDO;
import com.holderzone.saas.store.staff.entity.domain.ProductDO;
import com.holderzone.saas.store.staff.entity.domain.StoreSourceDO;
import com.holderzone.saas.store.staff.entity.domain.UserDO;
import com.holderzone.saas.store.staff.entity.enums.SourceFromEnum;
import com.holderzone.saas.store.staff.entity.query.UserSourceQuery;
import com.holderzone.saas.store.staff.mapper.*;
import com.holderzone.saas.store.staff.mapstruct.MenuMapStruct;
import com.holderzone.saas.store.staff.service.MenuService;
import com.holderzone.saas.store.staff.service.ProductService;
import com.holderzone.saas.store.staff.service.UserAuthorityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.staff.constant.Constant.NUMBER_ZERO;
import static com.holderzone.saas.store.staff.constant.Constant.USER_PERMISSION_TABLE_IS_EMPTY;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MenuServiceImpl
 * @date 19-1-15 下午2:31
 * @description 菜单相关操作
 * @program holder-saas-store-staff
 */
@Slf4j
@Service
public class MenuServiceImpl extends ServiceImpl<MenuMapper, MenuDO> implements MenuService {

    private final StoreSourceMapper storeSourceMapper;

    private final MenuMapStruct menuMapStruct;

    private final UserMapper userMapper;

    private final ProductService productService;

    private final UserAuthorityService userAuthorityService;

    private final MarketingConfig marketingConfig;

    @Value("#{'${wx-corp.exclude-operSubjectGuids}'.split(',')}")
    private List<String> wxCorpExcludeOperSubjectGuids;

    @Autowired
    public MenuServiceImpl(StoreSourceMapper storeSourceMapper, MenuMapStruct menuMapStruct, UserMapper userMapper,
                           ProductService productService, UserAuthorityService userAuthorityService, MarketingConfig marketingConfig) {
        this.storeSourceMapper = storeSourceMapper;
        this.menuMapStruct = menuMapStruct;
        this.userMapper = userMapper;
        this.productService = productService;
        this.userAuthorityService = userAuthorityService;
        this.marketingConfig = marketingConfig;
    }

    @Override
    public boolean insertMenu(List<MenuDTO> menuList) {
        if (CollectionUtils.isEmpty(menuList)) {
            return false;
        }

        // 删除原来的菜单信息
        List<String> oriMenuGuidList = menuList.stream()
                .map(MenuDTO::getMenuGuid)
                .collect(Collectors.toList());

        int delete = baseMapper.delete(new LambdaQueryWrapper<MenuDO>()
                .in(MenuDO::getMenuGuid, oriMenuGuidList));
        log.info("deleteMenu:{},delete:{}", oriMenuGuidList, delete);
        // 插入新的菜单信息
        return this.saveBatch(menuList.stream().map(this::convertMenu).collect(Collectors.toList()));
    }

    @Override
    public boolean deleteMenu(String menuGuid) {
        List<MenuDO> menuListInDb = baseMapper.selectList(
                new LambdaQueryWrapper<MenuDO>()
                        .select(MenuDO::getId)
                        .eq(MenuDO::getMenuGuid, menuGuid)
                        .or()
                        .like(MenuDO::getParentIds, menuGuid)
        );
        List<Long> menuIdListInDb = menuListInDb.stream()
                .map(MenuDO::getId).collect(Collectors.toList());
        return baseMapper.deleteBatchIds(menuIdListInDb) == menuListInDb.size();
    }

    @Override
    public boolean updateMenu(MenuDTO menuDTO) {
        MenuDO menuDO = convertMenu(menuDTO);
        return baseMapper.update(menuDO, new LambdaQueryWrapper<MenuDO>()
                .eq(MenuDO::getMenuGuid, menuDO.getMenuGuid())
        ) == 1;
    }

    @Override
    public boolean bindMenuAndPage(List<MenuDTO> menuList) {
        if (CollectionUtils.isEmpty(menuList)) {
            log.error("菜单列表为空，联系云平台开发人员，解决逻辑错误");
            return true;
        }

        return this.saveBatch(menuList.stream()
                .map(this::convertMenu)
                .collect(Collectors.toList())
        );
    }

    @Override
    public List<com.holderzone.saas.store.dto.user.MenuDTO> getMerchantMenu(String userGuid, String terminalCode) {
        int count = productService.count(new LambdaQueryWrapper<ProductDO>()
                .le(ProductDO::getGmtProductStart, LocalDate.now())
                .and(wrapper -> wrapper
                        .ge(ProductDO::getGmtProductEnd, LocalDate.now())
                        .or()
                        .isNull(ProductDO::getGmtProductEnd)
                )
        );
        if (count <= 0) {
            throw new BusinessException("该账号未开通服务，请联系售后人员。");
        }

        // 获取菜单逻辑：先获取当前用户的角色信息，查询商户后台终端角色-资源关联关系中的模块id集合，拿到模块id集合后倒推出菜单tree

        // 用户关联的模块集合（管理员帐号默认所有权限）
        List<StoreSourceDO> moduleList;
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            moduleList = storeSourceMapper.selectList(
                    new LambdaQueryWrapper<StoreSourceDO>()
                            .select(StoreSourceDO::getModuleGuid,
                                    StoreSourceDO::getPageUrl, StoreSourceDO::getPageTitle)
                            .eq(StoreSourceDO::getTerminalCode, terminalCode));
            log.info("admin getMerchantMenu : {}", JacksonUtils.writeValueAsString(moduleList));
        } else {
            // 该终端该用户的权限资源
            moduleList = baseMapper.getUserModuleList(userGuid, terminalCode);
            // 该终端基础模块下的资源
            moduleList.addAll(storeSourceMapper.selectList(
                    new LambdaQueryWrapper<StoreSourceDO>()
                            .select(StoreSourceDO::getModuleGuid,
                                    StoreSourceDO::getPageUrl, StoreSourceDO::getPageTitle)
                            .eq(StoreSourceDO::getTerminalCode, terminalCode)
                            .eq(StoreSourceDO::getModuleType, "0")));
            log.info("user {} getMerchantMenu : {}", userGuid, JacksonUtils.writeValueAsString(moduleList));
        }

        if (moduleList.isEmpty()) {
            throw new BusinessException("该账号无登陆权限。");
//            return Collections.emptyList();
        }

        // 查询所有下级菜单
        Map<String, List<StoreSourceDO>> moduleMap = moduleList.stream()
                .collect(Collectors.groupingBy(StoreSourceDO::getModuleGuid));
        List<MenuDO> menuListInDb = baseMapper.selectList(new LambdaQueryWrapper<MenuDO>()
                .in(MenuDO::getModuleGuid, new ArrayList<>(moduleMap.keySet())));
        log.info("storeSource ref menu : {}", JacksonUtils.writeValueAsString(menuListInDb));
        if (CollectionUtils.isEmpty(menuListInDb)) {
            throw new BusinessException("该账号无登陆权限。");
//            return Collections.emptyList();
        }

        List<String> parentMenuList = menuListInDb.stream()
                .flatMap(menuDO -> Arrays.stream(menuDO.getParentIds().split(",")))
                .distinct().collect(Collectors.toList());
        // 此处parentMenuSet理论上不为空，可是如果为空，就会查出所有记录，menuDOList的元素就会重复。
        if (!CollectionUtils.isEmpty(parentMenuList)) {
            LambdaQueryWrapper<MenuDO> wrapper = new LambdaQueryWrapper<>();
            parentMenuList.forEach(p -> wrapper.or().eq(MenuDO::getMenuGuid, p));
            // 查询下级菜单的所有上级
            menuListInDb.addAll(baseMapper.selectList(wrapper));
            log.info("下级菜单的所有上级 : {}", JacksonUtils.writeValueAsString(menuListInDb));
        }

        List<com.holderzone.saas.store.dto.user.MenuDTO> menuListResult = menuMapStruct.menuDOList2DTOList(menuListInDb);
        log.info("刚转换的menuListResult : {}", JacksonUtils.writeValueAsString(menuListResult));

        // 将相同模块（页面）的页面title与页面跳转路径赋值到菜单中
        menuListResult.forEach(p -> {
            List<StoreSourceDO> storeSourceList = moduleMap.get(p.getModuleGuid());
            if (!CollectionUtils.isEmpty(storeSourceList)) {
                // 替换企微配置的跳转地址
                replaceWechatCpTpUrl(storeSourceList);
                // 理论上第一个元素就有pageUrl，保险起见，有else的逻辑
                if (StringUtils.hasText(storeSourceList.get(0).getPageUrl())) {
                    p.setPageUrl(storeSourceList.get(0).getPageUrl());
                    p.setPageTitle(storeSourceList.get(0).getPageTitle());
                } else {
                    storeSourceList.stream()
                            .filter(storeSourceDO -> StringUtils.hasText(storeSourceDO.getPageUrl()))
                            .findAny()
                            .ifPresent(storeSourceDO -> {
                                p.setPageUrl(storeSourceDO.getPageUrl());
                                p.setPageTitle(storeSourceDO.getPageTitle());
                            });
                }
            }
        });
        //移除数据报表旧
        menuListResult.removeIf(m -> ObjectUtil.equal(m.getMenuGuid(),"1906251030067900000"));
        log.info("处理后的menuListResult : {}", JacksonUtils.writeValueAsString(menuListResult));
        // 需要对查询出来的扁平菜单转换为上下级菜单
        return formatTreeMenu(menuListResult);
    }

    /**
     * 替换企微配置的跳转地址
     */
    private void replaceWechatCpTpUrl(List<StoreSourceDO> storeSourceList) {
        // 判断是否何师
        if (!wxCorpExcludeOperSubjectGuids.contains(UserContextUtils.get().getOperSubjectGuid())) {
            return;
        }
        for (StoreSourceDO storeSourceDO : storeSourceList) {
            if ("企微配置".equals(storeSourceDO.getModuleName()) && !StringUtils.isEmpty(storeSourceDO.getPageUrl())) {
                String oldPageUrl = storeSourceDO.getPageUrl();
                String updateAfterPageUrl = marketingConfig.centerTransferTool(oldPageUrl);
                log.info("企微配置替换跳转url, 原url:{}, 新url:{}", oldPageUrl, updateAfterPageUrl);
                storeSourceDO.setPageUrl(updateAfterPageUrl);
            }
        }
    }

    @Override
    public List<String> queryUserSourceOnOut(Integer terminalCode, String userOrPhone, String enterpriseGuid) {
        UserContextUtils.putErp(enterpriseGuid);
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);

        UserDO userDO = userMapper.selectOne(new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getGuid, userOrPhone)
                .or()
                .eq(UserDO::getPhone, userOrPhone));
        Set<String> sourceCodeList = new HashSet<>();
        if (ObjectUtils.isEmpty(userDO)) {
            return new ArrayList<>(sourceCodeList);
        }
        if (Constants.Account.ADMIN.equals(userDO.getAccount())) {
            List<MenuSourceDTO> adminSourceList = userMapper.queryUserSourceOfAdminOnOut(terminalCode);
            adminSourceList.forEach(adminSource -> {
                String convert = BossSourceEnum.getConvert(adminSource.getSourceCode());
                if (StringUtils.isEmpty(convert)) {
                    return;
                }
                if (Objects.equals(BossSourceEnum.SALE_REPORT.getSourceCode(), adminSource.getSourceCode()) ||
                        Objects.equals(BossSourceEnum.USER_RUN.getSourceCode(), adminSource.getSourceCode())) {
                    sourceCodeList.add(BossSourceEnum.DATA_REPORT.getConvert());
                }
                sourceCodeList.add(convert);
            });
            return new ArrayList<>(sourceCodeList);
        }
        List<MenuSourceDTO> userSourceList = userMapper.queryUserSourceOnOut(userDO.getGuid(), terminalCode);
        userSourceList.forEach(source -> {
            String convert = BossSourceEnum.getConvert(source.getSourceCode());
            if (StringUtils.isEmpty(convert)) {
                return;
            }
            if (Objects.equals(BossSourceEnum.SALE_REPORT.getSourceCode(), source.getSourceCode()) ||
                    Objects.equals(BossSourceEnum.USER_RUN.getSourceCode(), source.getSourceCode())) {
                sourceCodeList.add(BossSourceEnum.DATA_REPORT.getConvert());
            }
            sourceCodeList.add(convert);
        });
        return new ArrayList<>(sourceCodeList);
    }

    @Override
    public List<MenuSourceDTO> getSourceByUser(String terminalCode) {
        String storeGuid = UserContextUtils.getStoreGuid();
        String userGuid = UserContextUtils.getUserGuid();

        // 查询可升级权限
        PermissionsReqDTO reqDTO = new PermissionsReqDTO();
        reqDTO.setTerminalCode(terminalCode);
        reqDTO.setStoreGuid(storeGuid);
        reqDTO.setUserGuid(userGuid);
        List<PermissionsRespDTO> permissionsList = userAuthorityService.queryEmployeePermissions(reqDTO);
        if (CollectionUtils.isEmpty(permissionsList)) {
            throw new BusinessException(USER_PERMISSION_TABLE_IS_EMPTY);
        }

        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            List<MenuSourceDTO> menuSourceDTOS = userMapper.queryUserSourceOfAdmin(new UserSourceQuery()
                    .setTerminalCode(terminalCode)
                    .setStoreGuid(storeGuid));
            if (CollectionUtils.isEmpty(menuSourceDTOS)) {
                return menuSourceDTOS;
            }
            handlePermissions(menuSourceDTOS, permissionsList);
            return menuSourceDTOS;
        }

        List<MenuSourceDTO> sourceDTOList = userMapper.queryUserSource(new UserSourceQuery()
                .setTerminalCode(terminalCode)
                .setStoreGuid(storeGuid)
                .setUserGuid(userGuid));
        if (CollectionUtils.isEmpty(sourceDTOList)) {
            return sourceDTOList;
        }
        // 授权处理
        authorizeHandel(sourceDTOList, permissionsList);
        // 适配处理：为了不影响以前的授权逻辑
        adaptiveHandle(sourceDTOList);
        // 终端设备授权类型处理
        terminalTypeHandle(sourceDTOList);
        // 一体机报表权限处理
        aioReportAuthorizeHandle(sourceDTOList);
        log.info("一体机权限获取列表：{}", JacksonUtils.writeValueAsString(sourceDTOList));
        return sourceDTOList;
    }


    /**
     * 一体机报表权限处理
     */
    private void aioReportAuthorizeHandle(List<MenuSourceDTO> sourceDTOList) {
        Map<String, List<AuthorityReportSourceEnum>> authorityReportSourceCodeMap = Arrays.stream(AuthorityReportSourceEnum.values())
                .filter(e -> !StringUtils.isEmpty(e.getCurrentSourceCode()))
                .collect(Collectors.groupingBy(AuthorityReportSourceEnum::getOriginalSourceCode));
        List<String> sourceCodes = sourceDTOList.stream().map(MenuSourceDTO::getSourceCode).distinct().collect(Collectors.toList());
        for (Map.Entry<String, List<AuthorityReportSourceEnum>> entry : authorityReportSourceCodeMap.entrySet()) {
            List<String> currentSourceCodes = entry.getValue()
                    .stream()
                    .map(AuthorityReportSourceEnum::getCurrentSourceCode)
                    .distinct()
                    .collect(Collectors.toList());
            if (sourceCodes.contains(entry.getKey()) && currentSourceCodes.stream().noneMatch(sourceCodes::contains)) {
                log.info("一体机报表老权限适配, old:{}", entry.getKey());
                List<MenuSourceDTO> newMenuSources = entry.getValue().stream().map(e -> {
                    MenuSourceDTO menuSourceDTO = new MenuSourceDTO();
                    menuSourceDTO.setSourceName(e.getSourceName());
                    menuSourceDTO.setSourceCode(e.getCurrentSourceCode());
                    return menuSourceDTO;
                }).collect(Collectors.toList());
                sourceDTOList.addAll(newMenuSources);
            }
        }
    }

    private void authorizeHandel(List<MenuSourceDTO> sourceDTOList, List<PermissionsRespDTO> permissionsList) {
        // 所有权限默认不能升级
        for (MenuSourceDTO menuSourceDTO : sourceDTOList) {
            menuSourceDTO.setIsElevatedPrivileges(Boolean.FALSE);
        }

        List<String> menuSourcecodeList = sourceDTOList.stream()
                .map(MenuSourceDTO::getSourceCode)
                .collect(Collectors.toList());

        permissionsList.forEach(

                permissions -> {
                    // 查询后台是否设置授权
                    PermissionsReqDTO dto = new PermissionsReqDTO();
                    dto.setSourceFrom(SourceFromEnum.BUILD_SELF.getCode());
                    dto.setSourceCode(permissions.getSourceCode());
                    dto.setUserGuid(UserContextUtils.getUserGuid());
                    Integer count = userAuthorityService.queryUserAuthority(dto);

                    // 判断该员工是否有权限,没有则升级权限
                    if (!menuSourcecodeList.contains(permissions.getSourceCode())) {
                        // 升级权限
                        MenuSourceDTO menuSource = new MenuSourceDTO();
                        menuSource.setSourceName(permissions.getSourceName());
                        menuSource.setSourceCode(permissions.getSourceCode());
                        menuSource.setIsElevatedPrivileges(Boolean.TRUE);
                        if (count > 0) {
                            menuSource.setIsElevatedPrivileges(Boolean.FALSE);
                        }
                        menuSource.setSourceGuid("");
                        sourceDTOList.add(menuSource);
                    }
                }
        );
    }

    private static void terminalTypeHandle(List<MenuSourceDTO> sourceDTOList) {
        Set<String> sourceCodeSet = sourceDTOList.stream()
                .map(MenuSourceDTO::getSourceCode)
                .collect(Collectors.toSet());
        sourceDTOList.forEach(source -> {
            AuthoritySourceEnum sourceEnum = AuthoritySourceEnum.getEnumBySourceCode(source.getSourceCode());
            if (!ObjectUtils.isEmpty(sourceEnum) && sourceCodeSet.contains(sourceEnum.getConvertCode())) {
                source.setType(AuthorityTypeEnum.FACE.getCode());
            }
        });
    }

    private void adaptiveHandle(List<MenuSourceDTO> sourceDTOList) {
        List<String> toDelete = new ArrayList<>();
        for (AuthorityFaceEnum faceEnum : AuthorityFaceEnum.values()) {
            PermissionsReqDTO dto = new PermissionsReqDTO();
            dto.setSourceFrom(SourceFromEnum.BUILD_SELF.getCode());
            dto.setSourceCode(faceEnum.getSourceCode());
            dto.setUserGuid(UserContextUtils.getUserGuid());
            Integer count = userAuthorityService.queryUserAuthority(dto);
            if (count < 1) {
                toDelete.add(faceEnum.getSourceCode());
            }
        }
        sourceDTOList.removeIf(s -> toDelete.contains(s.getSourceCode()));
    }

    private static void handlePermissions(List<MenuSourceDTO> menuSourceDTOS, List<PermissionsRespDTO> permissionsList) {
        // 管理员所有权限默认不能升级
        for (MenuSourceDTO menuSourceDTO : menuSourceDTOS) {
            menuSourceDTO.setIsElevatedPrivileges(Boolean.FALSE);
        }
        // 补全超额挂账权限，来源是自建的要补充进去，可否升级为false
        List<PermissionsRespDTO> buildSelfList = permissionsList.stream()
                .filter(permissions -> NUMBER_ZERO.equals(permissions.getSourceFrom()))
                .collect(Collectors.toList());
        buildSelfList.forEach(
                buildSelf -> {
                    MenuSourceDTO menuSource = new MenuSourceDTO();
                    menuSource.setSourceName(buildSelf.getSourceName());
                    menuSource.setSourceCode(buildSelf.getSourceCode());
                    menuSource.setIsElevatedPrivileges(Boolean.FALSE);
                    menuSource.setSourceGuid("");
                    menuSourceDTOS.add(menuSource);
                }
        );
    }

    @Override
    public List<MenuSourceDTO> getPageUrlByMenu(String pageUrl) {
        if (StringUtils.isEmpty(pageUrl)) {
            return Collections.emptyList();
        }
        // 根据跳转地址查询权限
        List<StoreSourceDO> storeSourceList = getByLikePageUrl(pageUrl);
        // 如果是查询营销中心权限，则需要查询小程序配置中是否有企微配置
        if (marketingConfig.getCenterUrl().equals(pageUrl)) {
            List<StoreSourceDO> memberToolStoreSourceList = getByLikePageUrl(marketingConfig.getToolUrl());
            List<StoreSourceDO> wechatCpTpSourceList = memberToolStoreSourceList.stream()
                    .filter(e -> "企微配置".equals(e.getModuleName()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(wechatCpTpSourceList)) {
                storeSourceList.addAll(wechatCpTpSourceList);
            }
        }
        return CollectionUtils.isEmpty(storeSourceList)
                ? Collections.emptyList()
                : menuMapStruct.storeSourceDOList2MenuSource(storeSourceList.stream().distinct().collect(Collectors.toList()));
    }

    private List<StoreSourceDO> getByLikePageUrl(String pageUrl) {
        String userAccount = UserContextUtils.getUserAccount();
        List<StoreSourceDO> storeSourceList;
        //判断是否是超级管理员
        if (ObjectUtil.equal(userAccount, Constants.Account.ADMIN)) {
            storeSourceList = storeSourceMapper.queryAdminSource(pageUrl);
        } else {
            storeSourceList = storeSourceMapper.querySourceByUserAndMenu(UserContextUtils.getUserGuid(), null, pageUrl);
        }
        return storeSourceList;
    }

    @Override
    public List<MenuSourceDTO> getSourceByMenu(String menuGuid) {
        MenuDO menuDO = baseMapper.selectOne(new LambdaQueryWrapper<MenuDO>()
                .eq(MenuDO::getMenuGuid, menuGuid)
                .eq(MenuDO::getIsEnable, true));
        if (Objects.isNull(menuDO)) {
            return Collections.emptyList();
        }

        List<StoreSourceDO> storeSourceList;
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            // 管理员返回所有权限
            storeSourceList = storeSourceMapper.selectList(new LambdaQueryWrapper<StoreSourceDO>()
                    .select(StoreSourceDO::getSourceGuid, StoreSourceDO::getSourceName,
                            StoreSourceDO::getSourceCode, StoreSourceDO::getSourceUrl)
                    .eq(StoreSourceDO::getModuleGuid, menuDO.getModuleGuid()));
        } else {
            // 普通用户根据菜单guid、员工guid查询该条件下拥有的资源信息
            storeSourceList = storeSourceMapper.querySourceByUserAndMenu(UserContextUtils.getUserGuid(), menuGuid, null);
        }

        // holder同步过来的企业要增加创建组织的code和url
        if (Objects.equals("员工列表", menuDO.getMenuName()) || Objects.equals("门店列表", menuDO.getMenuName())) {
            StoreSourceDO organizationCreateSource = null;
            if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
                organizationCreateSource = storeSourceMapper.querySourceByCode("organization_create");
            } else {
                organizationCreateSource = storeSourceMapper.querySourceByUserAndCode(UserContextUtils.getUserGuid(),
                        "organization_create");
            }
            if (Objects.nonNull(organizationCreateSource)) {
                storeSourceList.add(organizationCreateSource);
            }
        }

        return CollectionUtils.isEmpty(storeSourceList)
                ? Collections.emptyList()
                : menuMapStruct.storeSourceDOList2MenuSource(storeSourceList.stream().distinct().collect(Collectors.toList()));
    }

    @Override
    public List<MenuSourceDTO> getModuleSourceByUser(String terminalCode) {
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            return userMapper.queryUserSourceOfAdmin(new UserSourceQuery()
                    .setTerminalCode(terminalCode)
                    .setStoreGuid(UserContextUtils.getStoreGuid()));
        }
        return userMapper.queryUserModuleSource(new UserSourceQuery()
                .setTerminalCode(terminalCode)
                .setStoreGuid(UserContextUtils.getStoreGuid())
                .setUserGuid(UserContextUtils.getUserGuid()));
    }

    /**
     * 简单的，DTO 到 DO 的转换
     *
     * @param p
     * @return
     */
    private MenuDO convertMenu(MenuDTO p) {
        return new MenuDO()
                .setGmtSync(DateTimeUtils.now())
                .setGmtCreate(p.getCreateTime() == null
                        ? DateTimeUtils.now()
                        : DateTimeUtils.mills2LocalDateTime(p.getCreateTime()))
                .setMenuGuid(p.getMenuGuid())
                .setMenuIcon(p.getMenuIcon())
                .setMenuName(p.getMenuName())
                .setMenuSort((long) (p.getMenuSort() == null ? 0 : p.getMenuSort()))
                .setParentIds(p.getParentId())
                .setModuleGuid(p.getUrlGuid())
                .setTerminalGuid(p.getTerminalGuid())
                .setIsEnable(p.getIsEnabled())
                .setCreateStaffGuid(p.getCreateStaffGuid());
    }

    /**
     * 将查询出来的菜单扁平结构转换为树形结构
     *
     * @param menuDTOList menuDTOList
     * @return 树形结构
     */
    private List<com.holderzone.saas.store.dto.user.MenuDTO> formatTreeMenu(List<com.holderzone.saas.store.dto.user.MenuDTO> menuDTOList) {
        // parentIds的size最小为最上级菜单
        // 最顶层size最小。其实是恒等于1的。
        int minSize = menuDTOList.stream()
                .map(p -> p.getParentIds().split(",").length)
                .min(Comparator.naturalOrder())
                .orElse(0);

        List<com.holderzone.saas.store.dto.user.MenuDTO> firstChildMenu = menuDTOList.stream()
                .filter(p -> p.getParentIds().split(",").length == minSize)
                .sorted(Comparator.comparing(
                        com.holderzone.saas.store.dto.user.MenuDTO::getMenuSort))
                .collect(Collectors.toList());

        menuDTOList.removeAll(firstChildMenu);

        firstChildMenu.forEach(p -> p.setMenus(this.formatTreeMenuByParent(menuDTOList, p.getMenuGuid())));

        return firstChildMenu;
    }

    /**
     * 将查询出来的菜单扁平结构转换为树形结构
     *
     * @param menuDTOList 每次循环的菜单对象集合
     * @param parentId    父级菜单id
     * @return 该集合在该父级菜单下的树形子集
     */
    private List<com.holderzone.saas.store.dto.user.MenuDTO> formatTreeMenuByParent(List<com.holderzone.saas.store.dto.user.MenuDTO> menuDTOList,
                                                                                    String parentId) {
        List<com.holderzone.saas.store.dto.user.MenuDTO> menuList = new ArrayList<>();
        List<com.holderzone.saas.store.dto.user.MenuDTO> childMenuList = new ArrayList<>();
        for (com.holderzone.saas.store.dto.user.MenuDTO menuDTO : menuDTOList) {
            if (menuDTO.getParentIds().endsWith(parentId)) {
                menuList.add(menuDTO);
            } else {
                childMenuList.add(menuDTO);
            }
        }

        if (menuList.isEmpty()) {
            return Collections.emptyList();
        }

        menuList.sort(Comparator.comparing(com.holderzone.saas.store.dto.user.MenuDTO::getMenuSort));

        for (com.holderzone.saas.store.dto.user.MenuDTO menuDTO : menuList) {
            List<com.holderzone.saas.store.dto.user.MenuDTO> childMenuListOfThisMenu = this.formatTreeMenuByParent(childMenuList, menuDTO.getMenuGuid());
            menuDTO.setMenus(childMenuListOfThisMenu);
            // 移除已格式化的树，提高效率
            childMenuList.removeAll(childMenuListOfThisMenu);
        }

        return menuList;
    }


}
