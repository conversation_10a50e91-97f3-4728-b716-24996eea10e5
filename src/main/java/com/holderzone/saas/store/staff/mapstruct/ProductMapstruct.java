package com.holderzone.saas.store.staff.mapstruct;

import com.holderzone.resource.common.dto.product.ThemeSyncDTO;
import com.holderzone.saas.store.staff.entity.bo.ProductBasicBO;
import com.holderzone.saas.store.staff.entity.domain.ProductDO;
import com.holderzone.saas.store.staff.entity.domain.ProductThemeDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring")
public interface ProductMapstruct {

    @Mappings({
            @Mapping(source = "mchntTypeCode", target = "mchntTypeCode"),
            @Mapping(source = "isEnable", target = "isEnable"),
            @Mapping(source = "isDeleted", target = "isDeleted"),
    })
    ProductDO bo2Do(ProductBasicBO productBasic);

    @Mappings({
            @Mapping(source = "themeType", target = "themeCode"),
    })
    ProductThemeDO bo2Do(ThemeSyncDTO themeSyncDTO);

    List<ProductThemeDO> bo2Do(List<ThemeSyncDTO> themeSyncDTO);
}
