package com.holderzone.saas.store.staff.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.store.store.StoreProductDTO;
import com.holderzone.saas.store.staff.entity.bo.ProductBasicBO;
import com.holderzone.saas.store.staff.entity.domain.ProductDO;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ProductService extends IService<ProductDO> {

    void saveOrUpdate(ProductBasicBO productBasicBO);

    void deleteProduct(ProductBasicBO productBasicBO);

    String queryMchntType();

    Map<String, List<StoreProductDTO>> queryProductByIdList(List<String> storeGuidList);

    List<StoreProductDTO> queryProductByStoreGuid(String storeGuid, boolean withEnterpriseProduct);

    /**
     * 更新企业产品的过期时间
     *
     * @param enterpriseGuid
     * @param storeGuid
     * @param endDate
     * @param productGuid
     * @return
     */
    Integer updateExpirationDate(String enterpriseGuid, String storeGuid, String endDate, String productGuid);
}
