package com.holderzone.saas.store.staff.service;

import com.holderzone.saas.store.dto.user.RoleDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className GrantService
 * @date 2018/8/21 10:58
 * @description 用户-角色服务接口
 * @program holder-saas-store-staff
 */
public interface UserRoleService {

    /**
     * 对用户授予角色（包括批量）
     *
     * @param userGuid
     * @param arrayOfRoleDTO userDTOList
     */
    void addUserRoleRelation(String userGuid, List<RoleDTO> arrayOfRoleDTO);

    void updateUserRoleRelation(String userGuid, List<RoleDTO> arrayOfRoleDTO);

    void deleteUserRoleRelation(String userGuid);

    /**
     * 根据用户Guid查询用户拥有的角色
     *
     * @param userGuid 用户Guid
     * @return 角色信息
     */
    List<RoleDTO> findRoleByUserGuid(String userGuid);

    void batchDeleteUserRoleRelation(List<String> userGuidList);
}
