package com.holderzone.saas.store.staff.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RoleSourceDO
 * @date 19-1-23 上午10:01
 * @description
 * @program holder-saas-store-staff
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hss_role_source")
@EqualsAndHashCode
public class RoleSourceDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 角色guid
     */
    private String roleGuid;

    /**
     * 终端guid
     */
    private String terminalGuid;

    /**
     * 终端类型（0-web，1-其他）
     */
    private String terminalCode;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 最底层菜单guid
     */
    private String menuGuid;

    /**
     * 资源guid
     */
    private String sourceGuid;

    /**
     * 资源code
     */
    private String sourceCode;

    /**
     * 资源code
     */
    private String sourceUrl;
}
