package com.holderzone.saas.store.staff.entity.bo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className ProductBasicBo
 * @date 19-3-1 上午11:26
 * @description 产品基本信息BO
 * @program holder-saas-store-staff
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ProductBasicBO {
    /**
     * 企业guid
     */
    private String enterpriseGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 产品guid
     */
    private String productGuid;

    /**
     * 产品名
     */
    private String productName;


    /**
     * 产品类型：0=时间，1=数量
     */
    private Integer productType;

    /**
     * 产品规格
     */
    private String chargeGuid;

    /**
     * 商家经营类型
     */
    private String mchntTypeCode;

    /**
     * 是否启用:0/禁用,1/启用
     */
    private Boolean isEnable;

    /**
     * 是否删除:0/删除,1/正常
     */
    private Boolean isDeleted;

    /**
     * 产品授权起始日期
     */
    private LocalDate gmtProductStart;

    /**
     * 产品授权截止日期
     */
    private LocalDate gmtProductEnd;
}
