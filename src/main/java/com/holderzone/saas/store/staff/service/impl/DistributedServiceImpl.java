package com.holderzone.saas.store.staff.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.staff.service.DistributedService;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DistributedServiceImpl implements DistributedService {

    private static final String TAG_STAFF_USER = "staff/user";

    private static final String TAG_STAFF_USER_ROLE = "staff/user_role";

    private static final String TAG_STAFF_USER_DATA = "staff/user_data";

    private static final String TAG_STAFF_USER_OFFICE = "staff/user_office";

    private static final String TAG_STAFF_USER_AUTHORITY = "staff/user_authority";

    private final RedisTemplate redisTemplate;

    @Autowired
    public DistributedServiceImpl(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public String nextId(String tag) {
        try {
            return String.valueOf(BatchIdGenerator.getGuid(redisTemplate, tag));
        } catch (IOException e) {
            throw new BusinessException("生成Guid失败：" + e.getMessage());
        }
    }

    @Override
    public List<String> nextBatchId(String tag, long count) {
        try {
            return BatchIdGenerator.batchGetGuids(redisTemplate, tag, count)
                    .stream().map(String::valueOf).collect(Collectors.toList());
        } catch (IOException e) {
            throw new BusinessException("生成Guid失败：" + e.getMessage());
        }
    }

    @Override
    public String nextUserGuid() {
        return nextId(TAG_STAFF_USER);
    }

    @Override
    public List<String> nextBatchUserGuid(long count) {
        return nextBatchId(TAG_STAFF_USER, count);
    }

    @Override
    public String nextUserRoleGuid() {
        return nextId(TAG_STAFF_USER_ROLE);
    }

    @Override
    public List<String> nextBatchUserRoleGuid(long count) {
        return nextBatchId(TAG_STAFF_USER_ROLE, count);
    }

    @Override
    public List<String> nextBatchUserDataGuid(long count) {
        return nextBatchId(TAG_STAFF_USER_DATA, count);
    }

    @Override
    public String nextOfficeGuid() {
        return nextId(TAG_STAFF_USER_OFFICE);
    }

    @Override
    public String nextAuthorityGuid() {
        return nextId(TAG_STAFF_USER_AUTHORITY);
    }
}
