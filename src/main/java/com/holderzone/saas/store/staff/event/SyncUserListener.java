package com.holderzone.saas.store.staff.event;

import com.google.common.collect.Lists;
import com.holderzone.framework.dds.starter.utils.DynamicInfoHelper;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.resource.common.dto.user.UserDTO;
import com.holderzone.resource.common.util.MessageType;
import com.holderzone.saas.store.staff.config.RocketMqConfig;
import com.holderzone.saas.store.staff.entity.domain.UserDO;
import com.holderzone.saas.store.staff.service.UserService;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import com.holderzone.saas.store.staff.service.remote.EnterpriseDataClient;
import com.holderzone.saas.store.staff.utils.DynamicHelper;
import com.holderzone.saas.store.staff.utils.EnterpriseUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.holderzone.resource.common.util.MessageType.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className InitMerchantListener
 * @date 18-11-28 下午3:48
 * @description 订阅云端创建用户消息
 * @program holder-saas-store-staff
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.USER_SYNC_TOPIC,
        tags = RocketMqConfig.USER_SYNC_DOWNLOAD_TAG,
        consumerGroup = RocketMqConfig.USER_SYNC_DOWNLOAD_CONSUMER_GROUP,
        consumeThreadMax = 4,
        consumeThreadMin = 1,
        reTryConsume = 4
)
@AllArgsConstructor
public class SyncUserListener extends AbstractRocketMqConsumer<RocketMqTopic, UnMessage> {

    private final UserService userService;

    private final DynamicHelper dynamicHelper;

    private final EnterpriseClient enterpriseClient;

    private final EnterpriseDataClient enterpriseDataClient;

    private final DynamicInfoHelper dynamicInfoHelper;

    @Override
    public boolean consumeMsg(UnMessage unMessage, MessageExt messageExt) {
        log.info("用户消息消费，参数：{}，当前时间为：{}",
                JacksonUtils.writeValueAsString(unMessage), DateTimeUtils.now());

        String enterpriseGuid = unMessage.getEnterpriseGuid();
        if (!DynamicHelper.changeAndCheckDatasource(dynamicInfoHelper, enterpriseGuid)) {
            log.info("企业:{}切换数据源失败",enterpriseGuid);
            return true;
        }

        try {
            if (!EnterpriseUtils.checkEnterpriseExists(enterpriseDataClient, enterpriseClient, enterpriseGuid)) {
                log.info("校验企业:{}数据库以及redis信息失败",enterpriseGuid);
                return true;
            }
            // 过滤掉默认管理员账号
//            if (Objects.equals(Constants.Account.ADMIN, message.getAccount())) {
//                return true;
//            }
            switch (MessageType.getTypeByCode(unMessage.getMessageType())) {
                case ADD: {
                    UserDTO message = JacksonUtils.toObject(UserDTO.class, JacksonUtils.writeValueAsString(unMessage.getMessage()));
                    log.info("收到员工【{}】新增消息", message.getAccount());
                    com.holderzone.saas.store.dto.user.UserDTO userDTO = getFromMessage(message, ADD);
                    userDTO.setIsDeleted(Boolean.FALSE);
                    userService.createFromCloud(userDTO);
                    break;
                }
                case UPDATE: {
                    UserDTO message = JacksonUtils.toObject(UserDTO.class, JacksonUtils.writeValueAsString(unMessage.getMessage()));
                    log.info("收到员工【{}】修改消息", message.getAccount());
                    try {
                        com.holderzone.saas.store.dto.user.UserDTO userDTO = getFromMessage(message, UPDATE);
                        userService.updateFromCloud(userDTO);
                    } catch (Exception e) {
                        log.error("修改用户：{}，消费失败",
                                JacksonUtils.writeValueAsString(unMessage), e);
                    }
                    break;
                }
                case OTHER: {
                    UserDTO message = JacksonUtils.toObject(UserDTO.class, JacksonUtils.writeValueAsString(unMessage.getMessage()));
                    String userGuid = message.getUserGuid();
                    String password = message.getPassword();
                    log.info("收到员工【{}】重置密码", message.getAccount());
                    com.holderzone.saas.store.dto.user.UserDTO userDTO = new com.holderzone.saas.store.dto.user.UserDTO();
                    Assert.hasText(userGuid, "用户GUID不能为空");
                    Assert.hasText(password, "用户新密码不能为空");
                    userDTO.setGuid(userGuid);
                    userDTO.setPassword(password);
                    userService.resetPwdFromCloud(userDTO);
                    break;
                }
                case BATCH_ADD: {
                    List<UserDTO> message = JacksonUtils.toObjectList(UserDTO.class, JacksonUtils.writeValueAsString(unMessage.getMessage()));
                    Set<String> userGuidSet = message.stream().map(UserDTO::getUserGuid).collect(Collectors.toSet());
                    log.info("收到员工【{}】批量新增消息", JacksonUtils.writeValueAsString(userGuidSet));
                    List<UserDO> fromMessage = getFromMessage(message);
                    userService.createBatchFromCloud(fromMessage);
                    break;
                }
                case BATCH_UPDATE: {
                    List<UserDTO> message = JacksonUtils.toObjectList(UserDTO.class, JacksonUtils.writeValueAsString(unMessage.getMessage()));
                    Set<String> userGuidSet = message.stream().map(UserDTO::getUserGuid).collect(Collectors.toSet());
                    log.info("收到员工【{}】批量编辑消息", JacksonUtils.writeValueAsString(userGuidSet));
                    List<UserDO> fromMessage = getFromMessage(message);
                    userService.updateBatchFromCloud(fromMessage);
                    break;
                }
                default:
            }
        } catch (Throwable e) {
            if (log.isErrorEnabled()) {
                log.error("新增用户/修改用户密码入参：{}，消费失败",
                        JacksonUtils.writeValueAsString(unMessage), e);
            }
            return false;
        } finally {
            dynamicHelper.clear();
        }
        return true;
    }

    private com.holderzone.saas.store.dto.user.UserDTO getFromMessage(UserDTO message, MessageType messageType) {
        com.holderzone.saas.store.dto.user.UserDTO userDTO = new com.holderzone.saas.store.dto.user.UserDTO();
        switch (messageType) {
            case ADD: {
                addVerify(message);
                break;
            }
            case UPDATE: {
                Assert.hasText(message.getUserGuid(), "用户GUID不能为空");
                break;
            }
            case DELETE: {
                Assert.notEmpty(message.getPhoneList(), "用户手机号不能为空");
                break;
            }
            default:
        }
        userDTO.setGuid(message.getUserGuid());
        userDTO.setAccount(message.getAccount());
        userDTO.setName(message.getName());
        userDTO.setPassword(message.getPassword());
        userDTO.setAuthCode(message.getAuthCode());
        userDTO.setPhone(message.getTel());
        userDTO.setPhoneList(message.getPhoneList());
        userDTO.setAddress(message.getAddress());
        userDTO.setIdCardAddress(message.getAddress());
        userDTO.setBirthday(message.getBirth() != null
                ? DateTimeUtils.mills2LocalDateTime(message.getBirth())
                : null);
        userDTO.setOnBoardingTime(message.getRegTime() != null ?
                DateTimeUtils.mills2LocalDateTime(message.getRegTime())
                : null);
        userDTO.setEnterpriseNo(message.getMerchantNo());
        userDTO.setCreateStaffGuid(message.getCreateStaffGuid());
        userDTO.setUpdateStaffGuid(message.getUpdateStaffGuid());
        if (!StringUtils.isEmpty(message.getIsEnabled())) {
            userDTO.setIsEnable("1".equals(message.getIsEnabled()));
        }
        if (StringUtils.isEmpty(userDTO.getCreateStaffGuid())) {
            userDTO.setCreateStaffGuid("0");
        }
        if (StringUtils.isEmpty(userDTO.getUpdateStaffGuid())) {
            userDTO.setUpdateStaffGuid("0");
        }
        userDTO.setIntegrateFlag(message.getIntegrateFlag());
        return userDTO;
    }

    private void addVerify(UserDTO message) {
        Assert.hasText(message.getUserGuid(), "用户GUID不能为空");
        Assert.hasText(message.getPassword(), "用户密码不能为空");
        Assert.hasText(message.getAccount(), "用户帐号不能为空");
        Assert.hasText(message.getName(), "用户姓名不能为空");
        Assert.hasText(message.getAuthCode(), "用户授权码不能为空");
        Assert.hasText(message.getTel(), "用户手机号不能为空");
        Assert.hasText(message.getMerchantNo(), "用户企业编号不能为空");
    }

    private List<UserDO> getFromMessage(List<UserDTO> messages) {
        List<UserDO> userList = Lists.newArrayList();
        messages.forEach(message -> {
            UserDO userDO = new UserDO();
            userDO.setGuid(message.getUserGuid());
            userDO.setAccount(message.getAccount());
            userDO.setName(message.getName());
            userDO.setPassword(message.getPassword());
            userDO.setAuthCode(message.getAuthCode());
            userDO.setPhone(message.getTel());
            userDO.setAddress(message.getAddress());
            userDO.setIdCardAddress(message.getAddress());
            userDO.setBirthday(message.getBirth() != null
                    ? DateTimeUtils.mills2LocalDateTime(message.getBirth())
                    : null);
            userDO.setOnBoardingTime(message.getRegTime() != null ?
                    DateTimeUtils.mills2LocalDateTime(message.getRegTime())
                    : null);
            userDO.setEnterpriseNo(message.getMerchantNo());
            userDO.setCreateStaffGuid(message.getCreateStaffGuid());
            userDO.setUpdateStaffGuid(message.getUpdateStaffGuid());
            if (StringUtils.isEmpty(userDO.getCreateStaffGuid())) {
                userDO.setCreateStaffGuid("0");
            }
            if (StringUtils.isEmpty(userDO.getUpdateStaffGuid())) {
                userDO.setUpdateStaffGuid("0");
            }
            if (!StringUtils.isEmpty(message.getIsEnabled())) {
                userDO.setIsEnable("1".equals(message.getIsEnabled()));
            }
            userDO.setIsEnable(true);
            userDO.setIsDeleted(false);
            userDO.setRegType(message.getRegType());
            userDO.setIntegrateFlag(message.getIntegrateFlag());
            userList.add(userDO);
        });
        return userList;
    }

}