package com.holderzone.saas.store.staff.service.remote;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.resource.common.dto.device.DeviceStoreDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className DeviceClient
 * @date 18-9-11 下午4:38
 * @description 服务间调用-设备服务
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(value = "holder-saas-cloud-device", fallbackFactory = DeviceClient.ServiceFallback.class)
public interface DeviceClient {
    /**
     * 根据设备编号查询云端设备绑定信息
     *
     * @param deviceNo 厂商设备编号
     * @return 设备相关信息
     */
    @PostMapping("/device/findByNo/{deviceNo}")
    DeviceStoreDTO queryDeviceInfoByDeviceNo(@PathVariable("deviceNo") String deviceNo);

    @Slf4j
    @Component
    class ServiceFallback implements FallbackFactory<DeviceClient> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public DeviceClient create(Throwable cause) {
            return deviceNo -> {
                log.error(HYSTRIX_PATTERN, "create", "入参设备编号deviceNo为：" + deviceNo, ThrowableUtils.asString(cause));
                throw new BusinessException("根据设备编号查询设备相关信息接口熔断");
            };
        }
    }
}
