package com.holderzone.saas.store.staff.entity.read;

import com.holderzone.saas.store.staff.entity.domain.RoleDO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class UserReadDO implements Serializable {

    private static final long serialVersionUID = -2224829712878641342L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户GUID
     */
    private String guid;

    /**
     * 企业编码
     */
    private String enterpriseNo;

    /**
     * 员工账号
     */
    private String account;

    /**
     * 员工密码
     */
    private String password;

    /**
     * 授权码
     */
    private String authCode;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 员工所属组织，单组织节点Guid
     */
    private String orgGuid;

    /**
     * 员工职位编码
     */
    private Integer officeCode;

    /**
     * 员工职位名称
     */
    private String officeName;

    /**
     * 员工身份证号码
     */
    private String idCardNo;

    /**
     * 员工身份证地址
     */
    private String idCardAddress;

    /**
     * 员工居住地址
     */
    private String address;

    /**
     * 员工生日
     */
    private LocalDateTime birthday;

    /**
     * 员工入职时间
     */
    private LocalDateTime onBoardingTime;

    /**
     * 创建人GUID
     */
    private String createStaffGuid;

    /**
     * 更新人GUID
     */
    private String updateStaffGuid;

    /**
     * 是否启用：0=禁用，1=启用
     */
    private Boolean isEnable;

    /**
     * 是否删除：0=未删除，1=已删除
     */
    private Boolean isDeleted;

    /***
     * 是否是操作员：0=否，1=是
     */
    private Boolean isWaiter;
    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 用户角色列表
     */
    private List<RoleDO> roles;

    /**
     * 人脸识别码
     */
    private String faceCode;

    /**
     * 接收系统短信通知：0=不接收，1=接收
     */
    private Boolean isReceive;
}
