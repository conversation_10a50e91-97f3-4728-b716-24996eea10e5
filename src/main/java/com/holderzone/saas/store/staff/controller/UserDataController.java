package com.holderzone.saas.store.staff.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.user.LocalUserInfoDTO;
import com.holderzone.saas.store.dto.user.LocalUserReqDTO;
import com.holderzone.saas.store.dto.user.UserDTO;
import com.holderzone.saas.store.dto.user.UserSpinnerDTO;
import com.holderzone.saas.store.staff.service.UserDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/user_data")
@Api(description = "员工数据权限管理接口")
public class UserDataController {

    private final UserDataService userDataService;

    @Autowired
    public UserDataController(UserDataService userDataService) {
        this.userDataService = userDataService;
    }

    @ApiOperation(value = "保存用户数据权限", notes = "保存用户数据权限")
    @PostMapping(value = "/save")
    public void save(@Valid @RequestBody UserDTO userDTO) {
        userDataService.save(userDTO);
    }

    @PostMapping(value = "/update_new_store/{storeGuid}")
    public void updateNewStore(@PathVariable(value = "storeGuid") String storeGuid) {
        log.info("入参：{}", storeGuid);
        log.info("userInfo：{}", UserContextUtils.getJsonStr());
        userDataService.updateNewStore(storeGuid);
    }

    @ApiOperation(value = "查询用户数据权限", notes = "查询用户数据权限")
    @PostMapping(value = "/query")
    public UserDTO query(@Valid @RequestBody UserDTO userDTO) {
        return userDataService.query(userDTO);
    }

    @ApiOperation(value = "更新用户数据权限", notes = "更新用户数据权限")
    @PostMapping(value = "/update")
    public void update(@Valid @RequestBody UserDTO userDTO) {
        userDataService.update(userDTO);
    }

    @ApiOperation(value = "查询用户可分配角色", notes = "查询用户可分配角色")
    @PostMapping(value = "/query_roles_distributable")
    public UserDTO queryUserRolesDistributable() {
        return userDataService.queryUserRolesDistributable();
    }

    @ApiOperation(value = "查询用户整单折扣、整单让价阈值", notes = "查询用户整单折扣、整单让价阈值")
    @PostMapping(value = "/query_data_threshold")
    public UserDTO queryUserDataThreshold() {
        return userDataService.queryUserDataThreshold();
    }


    @ApiOperation(value = "查询门店Spinner")
    @PostMapping(value = "/query_store_spinner")
    public UserSpinnerDTO queryStoreSpinner() {
        return userDataService.queryStoreMergedSpinner();
    }

    @ApiOperation(value = "查询门店营业数")
    @PostMapping("/get_staff_alive_store_count")
    public Integer getStaffAliveStoreCount(){
        return userDataService.getStaffAliveStoreCount();
    }

    @PostMapping(value = "/query_store_spinner_by_brand_guid")
    public UserSpinnerDTO queryStoreSpinnerByBrandGuid(@RequestBody SingleDataDTO singleDataDTO) {
        return userDataService.queryStoreMergedSpinnerByBrandGuid(singleDataDTO);
    }

    @ApiOperation(value = "查询组织Spinner")
    @PostMapping(value = "/query_org_spinner")
    public UserSpinnerDTO queryOrgSpinner() {
        return userDataService.queryOrgMergedSpinner();
    }

    @ApiOperation(value = "查询品牌Spinner")
    @PostMapping(value = "/query_brand_spinner")
    public UserSpinnerDTO queryBrandSpinner() {
        return userDataService.queryBrandMergedSpinner();
    }

    @ApiOperation(value = "查询条件匹配Spinner")
    @PostMapping(value = "/query_condition_spinner")
    public UserSpinnerDTO queryConditionSpinner() {
        return userDataService.queryCondMergedSpinner();
    }

    @ApiOperation(value = "查询门店和条件匹配Spinner")
    @PostMapping(value = "/query_store_and_cond_spinner")
    public UserSpinnerDTO queryStoreAndCondSpinner() {
        return userDataService.queryStoreAndCondSpinner();
    }

    @ApiOperation(value = "查询广义组织Spinner")
    @PostMapping(value = "/query_generic_org_spinner")
    public UserSpinnerDTO queryGenericOrgSpinner() {
        return userDataService.queryGenericOrgSpinner();
    }

    @ApiOperation(value = "查询所有门店Guid")
    @PostMapping(value = "/query_all_store_guid")
    public List<String> queryAllStoreGuid() {
        return userDataService.queryAllStoreGuid();
    }

    @ApiOperation(value = "查询有一体机权限的用户")
    @PostMapping(value = "/query_aio_users")
    public List<UserDTO> queryAIOUsers(BaseDTO baseDTO) {
        return userDataService.queryAIOUsers(baseDTO);
    }


    @ApiModelProperty(value = "本地化用户数据")
    @PostMapping(value = "/local_user_info")
    public List<LocalUserInfoDTO> queryAllUserToLocal(@Validated @RequestBody LocalUserReqDTO localUserReqDTO) {
        return userDataService.queryAllUserToLocal(localUserReqDTO);
    }
}
