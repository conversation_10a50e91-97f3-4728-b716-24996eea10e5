package com.holderzone.saas.store.staff.service.remote;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.resource.common.dto.enterprise.MessageConfigDTO;
import com.holderzone.resource.common.dto.extension.BaseDictionaryDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className EnterpriseClient
 * @date 18-9-13 下午5:19
 * @description 服务间调用-云端商户服务
 * @program holder-saas-store-store
 */
@Component
@FeignClient(value = "holder-saas-cloud-enterprise", fallbackFactory = EnterpriseClient.ServiceFallBack.class)
public interface EnterpriseClient {
    @PostMapping("/enterprise/find")
    EnterpriseDTO findEnterprise(@RequestBody EnterpriseQueryDTO query);

    @GetMapping("/enterprise/sms/info")
    MessageConfigDTO getMessageInfo(@RequestParam("enterpriseGuid") String enterpriseGuid);

    @GetMapping("/enterprise/hasEnterprise")
    Boolean hasEnterprise(@RequestParam("enterpriseGuid") String enterpriseGuid);

    /**
     * 根据企业guid查询企业经营模式
     *
     * @param enterpriseGuid 企业guid
     * @return SINGLE, 单店 CHAIN,连锁 PLATFORM平台
     */
    @GetMapping("/enterprise/management_model/{enterpriseGuid}")
    String queryManagementModel(@PathVariable(value = "enterpriseGuid") String enterpriseGuid);

    /**
     * 根据企业guid查询企业经营类型
     *
     * @param enterpriseGuid 企业guid
     * @return 数据字典实体
     */
    @ApiOperation("根据企业guid查询企业经营类型")
    @GetMapping("/enterprise/management_type/{enterpriseGuid}")
    BaseDictionaryDTO queryManagementType(@PathVariable(value = "enterpriseGuid") String enterpriseGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<EnterpriseClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public EnterpriseClient create(Throwable cause) {
            return new EnterpriseClient() {
                @Override
                public EnterpriseDTO findEnterprise(EnterpriseQueryDTO query) {
                    log.error(HYSTRIX_PATTERN, "findEnterprise", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(cause));
                    throw new BusinessException("调用服务查询商户信息失败");
                }

                @Override
                public MessageConfigDTO getMessageInfo(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "getMessageInfo", "enterpriseGuid为：" + enterpriseGuid,
                            ThrowableUtils.asString(cause));
                    throw new BusinessException("调用服务查询商户短信相关信息失败");
                }

                @Override
                public Boolean hasEnterprise(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "/enterprise/hasEnterprise", enterpriseGuid, cause);
                    return null;
                }

                @Override
                public String queryManagementModel(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "/management_model/{enterpriseGuid}", enterpriseGuid, cause);
                    return null;
                }

                @Override
                public BaseDictionaryDTO queryManagementType(String enterpriseGuid) {
                    log.error(HYSTRIX_PATTERN, "/management_type/{enterpriseGuid}", enterpriseGuid, cause);
                    return null;
                }
            };
        }
    }
}
