package com.holderzone.saas.store.staff.aop;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ControllerAspect
 * @date 2018/10/16 上午10:56
 * @description //controller切面
 * @program holder-saas-aggregation-merchant
 */
@Slf4j
@Aspect
@Component
@SuppressWarnings("Duplicates")
public class ControllerAspect {

    @Pointcut("execution(* com.holderzone.saas.store.staff.controller.*.*(..))")
    public void pointCut() {
    }

    @Before("pointCut()")
    public void doBefore(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            // 解析userInfo
            UserContext userContext = UserContextUtils.get();
            if (userContext != null) {
                String enterpriseGuid = userContext.getEnterpriseGuid();
                String enterpriseName = userContext.getEnterpriseName();
                String storeGuid = userContext.getStoreGuid();
                String storeName = userContext.getStoreName();
                String userGuid = userContext.getUserGuid();
                String userName = userContext.getUserName();
                String account = userContext.getAccount();
                // 记录userInfo日志
                StringBuilder userInfo = new StringBuilder();
                userInfo.append("[");
                userInfo.append("enterpriseGuid=").append(enterpriseGuid).append(", ");
                userInfo.append("enterpriseName=").append(enterpriseName).append(", ");
                userInfo.append("storeGuid=").append(storeGuid).append(", ");
                userInfo.append("storeName=").append(storeName).append(", ");
                userInfo.append("userGuid=").append(userGuid).append(", ");
                userInfo.append("userName=").append(userName).append(", ");
                userInfo.append("newAccount=").append(account);
                userInfo.append("]");
                if (log.isInfoEnabled()) {
                    log.info("userInfo: {}", userInfo.toString());
                }
                // BaseDTO填充
                for (Object arg : args) {
                    if (arg instanceof BaseDTO) {
                        BaseDTO baseDTO = (BaseDTO) arg;
                        Optional.ofNullable(enterpriseGuid).ifPresent(baseDTO::setEnterpriseGuid);
                        Optional.ofNullable(enterpriseName).ifPresent(baseDTO::setEnterpriseName);
                        Optional.ofNullable(storeGuid).ifPresent(baseDTO::setStoreGuid);
                        Optional.ofNullable(storeName).ifPresent(baseDTO::setStoreName);
                        Optional.ofNullable(userGuid).ifPresent(baseDTO::setUserGuid);
                        Optional.ofNullable(userName).ifPresent(baseDTO::setUserName);
                        Optional.ofNullable(account).ifPresent(baseDTO::setAccount);
                    }
                }
            }
        }
    }
}
