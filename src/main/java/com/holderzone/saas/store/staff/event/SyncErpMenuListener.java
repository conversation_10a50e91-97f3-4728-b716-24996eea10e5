package com.holderzone.saas.store.staff.event;

import com.holderzone.framework.dds.starter.exception.DynamicException;
import com.holderzone.framework.dds.starter.utils.DynamicInfoHelper;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.resource.common.dto.product.MenuDTO;
import com.holderzone.resource.common.util.MessageType;
import com.holderzone.saas.store.staff.config.RocketMqConfig;
import com.holderzone.saas.store.staff.service.MenuService;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import com.holderzone.saas.store.staff.service.remote.EnterpriseDataClient;
import com.holderzone.saas.store.staff.utils.DynamicHelper;
import com.holderzone.saas.store.staff.utils.EnterpriseUtils;
import com.mysql.cj.exceptions.CJException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className InitEnterpriseMenuListener
 * @date 19-1-24 上午10:01
 * @description 订阅云端菜单相关消息，important：RocketListenerHandler注解中的tags属性，多个情况下逗号前后不能添加空格（不能对该类进行格式化），否则订阅关系会无效！！！！
 * @program holder-saas-store-staff
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.MENU_ASYNC_TOPIC,
        tags = {
                RocketMqConfig.MENU_ASYNC_TAG,
                RocketMqConfig.MENU_ALL_ASYNC_TAG
        },
        consumerGroup = RocketMqConfig.MENU_SYNC_CONSUMER_GROUP,
        consumeThreadMax = 4,
        consumeThreadMin = 1,
        reTryConsume = 4
)
@AllArgsConstructor
public class SyncErpMenuListener extends AbstractRocketMqConsumer<RocketMqTopic, UnMessage<String>> {

    private final MenuService menuService;

    private final DynamicHelper dynamicHelper;

    private final EnterpriseClient enterpriseClient;

    private final EnterpriseDataClient enterpriseDataClient;

    private final DynamicInfoHelper dynamicInfoHelper;

    @Override
    public boolean consumeMsg(UnMessage<String> unMessage, MessageExt messageExt) {
        log.info("菜单消息消费，参数：{}，当前时间为：{}",
                JacksonUtils.writeValueAsString(unMessage), DateTimeUtils.now().toString());

        String enterpriseGuid = unMessage.getEnterpriseGuid();
        if(!DynamicHelper.changeAndCheckDatasource(dynamicInfoHelper,enterpriseGuid)){
            return true;
        }
        try {
            if(!EnterpriseUtils.checkEnterpriseExists(enterpriseDataClient,enterpriseClient,enterpriseGuid)){
                return true;
            }

            log.info("设置动态数据源的enterpriseGuid : {}", unMessage.getEnterpriseGuid());
            // 云端对企业或门店授权时会推送一条全部菜单的消息
            // 而其他对菜单的修改（新增、删除、修改、修改下级url）也会推送一条消息
            // 两条消息的topic一样，通过tag区分
            String tag = messageExt.getProperty("TAGS");
            if (RocketMqConfig.MENU_ASYNC_TAG.equals(tag)) {
                log.info("菜单消费的TAG是MENU_ASYNC_TAG");
                return this.incrMenu(unMessage);
            } else if (RocketMqConfig.MENU_ALL_ASYNC_TAG.equals(tag)) {
                log.info("菜单消费的TAG是MENU_ALL_ASYNC_TAG");
                return this.allMenuAdd(unMessage);
            } else {
                log.info("无效的Tag：" + tag);
            }
        }   catch (Exception e) {
            log.error("菜单权限消息消费，发生异常，enterpriseGuid：{}，异常：{}",
                    unMessage.getEnterpriseGuid(), e);
        } finally {
            dynamicHelper.clear();
        }

        return false;
    }

    /**
     * 云端每次对菜单的
     * 新增操作
     * 修改操作（两种情况：修改菜单基本信息，修改菜单关联的url-即为修改菜单的所属模块）
     * 删除操作
     * 都会推送消息过来
     * <p>
     * 推送的都是当前操作的菜单（增量）
     *
     * @param unMessage 推送信息
     * @return 消息执行结果
     */
    private boolean incrMenu(UnMessage<String> unMessage) {
        // 菜单的增量更新每次都是推送所有企业下的消息过来
        String message = unMessage.getMessage();
        String messageType = unMessage.getMessageType();
        MessageType type = MessageType.getTypeByCode(messageType);
        boolean ackResult = false;

        switch (type) {
            case ADD: {
                MenuDTO cloudMenu = JacksonUtils.toObject(MenuDTO.class, message);
                ackResult = menuService.insertMenu(Collections.singletonList(cloudMenu));
                break;
            }
            // 修改菜单基本信息、修改菜单关联url（修改菜单与模块的关联关系）会推送update消息
            case UPDATE: {
                MenuDTO cloudMenu = JacksonUtils.toObject(MenuDTO.class, message);
                ackResult = menuService.updateMenu(cloudMenu);
                break;
            }
            case DELETE: {
                ackResult = menuService.deleteMenu(message);
                break;
            }
            // 菜单修改关联的下级url会推送other消息
            case OTHER: {
                List<MenuDTO> cloudMenuList = JacksonUtils.toObjectList(MenuDTO.class, message);
                ackResult = menuService.bindMenuAndPage(cloudMenuList);
                break;
            }
            default: {
                log.error(RocketMqConfig.MENU_ASYNC_TOPIC + "下不支持的消息，消息体：{}" + JacksonUtils.writeValueAsString(unMessage));
                break;
            }
        }

        return ackResult;
    }

    /**
     * 对企业或门店授权时，云端会推送当前企业或当前门店的所属企业下的全部菜单（全量更新）
     *
     * @param unMessage 推送信息
     * @return 消息执行结果
     */
    private boolean allMenuAdd(UnMessage<String> unMessage) {
        // 菜单的全部添加在企业或门店授权的时候推送，只推送该企业或门店

        // 先删除再添加
        List<MenuDTO> cloudMenuList = JacksonUtils.toObjectList(MenuDTO.class, unMessage.getMessage());

        return menuService.insertMenu(this.convertTree(cloudMenuList, new ArrayList<>()));
    }

    /**
     * 将云端推送的树形结构转换为入库需要的扁平结构
     *
     * @param cloudMenuList 云端推送的上下级结构
     * @param menuDTOList   入库需要的扁平结构
     * @return menuDTOList
     */
    private List<MenuDTO> convertTree(List<MenuDTO> cloudMenuList, List<MenuDTO> menuDTOList) {
        cloudMenuList.forEach(p -> {
            // 将自己添加进去，存在下级继续递归添加
            menuDTOList.add(p);
            if (!CollectionUtils.isEmpty(p.getMenus())) {
                this.convertTree(p.getMenus(), menuDTOList);
            }
        });
        return menuDTOList;
    }
}
