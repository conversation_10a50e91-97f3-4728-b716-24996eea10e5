package com.holderzone.saas.store.staff.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className RoleDO
 * @date 19-1-15 下午2:03
 * @description 角色表DO
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "hss_role")
public class RoleDO implements Serializable {

    private static final long serialVersionUID = 6749537288404738808L;

    /**
     * 主键id，自增
     */
    @TableId
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 是否启用，1-已启用，0-未启用
     */
    private Boolean isEnable;

    /**
     * 是否删除，1-已删除，0-未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建人guid
     */
    private String createStaffGuid;

    /**
     * 更新人guid
     */
    private String modifiedStaffGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;
}
