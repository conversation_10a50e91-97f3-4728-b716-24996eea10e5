package com.holderzone.saas.store.staff.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.resource.common.dto.user.UserDTO;
import com.holderzone.resource.common.util.MessageType;
import com.holderzone.saas.store.staff.config.RocketMqConfig;
import com.holderzone.saas.store.staff.entity.domain.UserDO;
import com.holderzone.saas.store.staff.service.UserUploadService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
public class UserUploadServiceImpl implements UserUploadService {

    private final DefaultRocketMqProducer rocketMqProducer;

    @Autowired
    public UserUploadServiceImpl(DefaultRocketMqProducer rocketMqProducer) {
        this.rocketMqProducer = rocketMqProducer;
    }

    @Override
    public void addUser(UserDO userDO) {
        // userGuid, newAccount, name ,password, newAuthCode, tel, isEnabled, enterpriseGuid, merchantNo 必传
        UserDTO userDTO = new UserDTO();
        userDTO.setUserGuid(userDO.getGuid());
        userDTO.setAccount(userDO.getAccount());
        userDTO.setName(userDO.getName());
        userDTO.setPassword(userDO.getPassword());
        userDTO.setAuthCode(userDO.getAuthCode());
        userDTO.setTel(userDO.getPhone());
        userDTO.setAddress(userDO.getAddress());
        userDTO.setBirth(Optional.ofNullable(userDO.getBirthday()).map(DateTimeUtils::localDateTime2Mills).orElse(null));
        userDTO.setRegTime(Optional.ofNullable(userDO.getOnBoardingTime()).map(DateTimeUtils::localDateTime2Mills).orElse(null));
        userDTO.setRegType(userDO.getRegType());
        userDTO.setIsEnabled("1");
        userDTO.setIsDeleted("0");
        userDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        userDTO.setMerchantNo(userDO.getEnterpriseNo());
        userDTO.setCreateStaffGuid(UserContextUtils.getUserGuid());
        userDTO.setUpdateStaffGuid(UserContextUtils.getUserGuid());
        userDTO.setUpdateStaffName(UserContextUtils.getUserName());
        userDTO.setUpdateStaffAccount(UserContextUtils.getUserAccount());
        UnMessage<UserDTO> unMessage = new UnMessage<>();
        unMessage.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        unMessage.setStoreGuid(UserContextUtils.getStoreGuid());
        unMessage.setMessageType(MessageType.ADD.code());
        unMessage.setMessage(userDTO);
        Message message = new Message(RocketMqConfig.USER_SYNC_TOPIC,
                RocketMqConfig.USER_SYNC_UPLOAD_TAG, JacksonUtils.toJsonByte(unMessage));
        rocketMqProducer.sendMessage(message);
    }

    @Override
    public void updateUser(UserDO userDO) {
        // userGuid 必传
        UserDTO userDTO = new UserDTO();
        userDTO.setUserGuid(userDO.getGuid());
        userDTO.setName(userDO.getName());
        userDTO.setAccount(userDO.getAccount());
        userDTO.setPassword(userDO.getPassword());
        userDTO.setTel(userDO.getPhone());
        userDTO.setAddress(userDO.getAddress());
        userDTO.setBirth(Optional.ofNullable(userDO.getBirthday()).map(DateTimeUtils::localDateTime2Mills).orElse(null));
        userDTO.setRegTime(Optional.ofNullable(userDO.getOnBoardingTime()).map(DateTimeUtils::localDateTime2Mills).orElse(null));
        userDTO.setIsEnabled(userDO.getIsEnable() != null ? (userDO.getIsEnable() ? "1" : "0") : null);
        userDTO.setUpdateStaffGuid(UserContextUtils.getUserGuid());
        userDTO.setUpdateStaffName(UserContextUtils.getUserName());
        userDTO.setUpdateStaffAccount(UserContextUtils.getUserAccount());
        UnMessage<UserDTO> unMessage = new UnMessage<>();
        unMessage.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        unMessage.setStoreGuid(UserContextUtils.getStoreGuid());
        unMessage.setMessageType(MessageType.UPDATE.code());
        unMessage.setMessage(userDTO);
        Message message = new Message(RocketMqConfig.USER_SYNC_TOPIC,
                RocketMqConfig.USER_SYNC_UPLOAD_TAG, JacksonUtils.toJsonByte(unMessage));
        rocketMqProducer.sendMessage(message);
    }

    @Override
    public void deleteUser(String userGuid) {
        UserDTO userDTO = new UserDTO();
        userDTO.setUserGuid(userGuid);
        UnMessage<UserDTO> unMessage = new UnMessage<>();
        unMessage.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        unMessage.setStoreGuid(UserContextUtils.getStoreGuid());
        unMessage.setMessageType(MessageType.DELETE.code());
        unMessage.setMessage(userDTO);
        Message message = new Message(RocketMqConfig.USER_SYNC_TOPIC,
                RocketMqConfig.USER_SYNC_UPLOAD_TAG, JacksonUtils.toJsonByte(unMessage));
        rocketMqProducer.sendMessage(message);
    }
}
