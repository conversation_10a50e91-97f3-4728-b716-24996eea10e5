package com.holderzone.saas.store.staff.service.impl;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class RuleCondBO<T> {

    private boolean allSelected;

    private List<T> data = new ArrayList<>();

    @JsonIgnore
    public static <T> RuleCondBO<T> selected() {
        return new RuleCondBO<T>().setAllSelected(true);
    }
}
