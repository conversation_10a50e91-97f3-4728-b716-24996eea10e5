package com.holderzone.saas.store.staff.utils;

import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.dds.starter.utils.JacksonUtil;
import com.holderzone.resource.common.dto.data.EnterpriseServerDatabaseDTO;
import com.holderzone.resource.common.dto.data.EnterpriseServerDatabaseQueryDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import com.holderzone.saas.store.staff.service.remote.EnterpriseDataClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Slf4j
public class EnterpriseUtils {
    public static boolean checkEnterpriseExists(EnterpriseDataClient enterpriseDataClient,EnterpriseClient enterpriseClient , String enterpriseGuid){

        Boolean ret =
                enterpriseClient.hasEnterprise(enterpriseGuid);
        if (ret == null) {
            throw  new RuntimeException("调用查询企业接口失败 ");
        }
        if(!ret){
            return false;
        }
        EnterpriseQueryDTO queryDTO = new EnterpriseQueryDTO();
        queryDTO.setEnterpriseGuid(enterpriseGuid);
        EnterpriseDTO enterprise=enterpriseClient.findEnterprise(queryDTO);
        if(enterprise==null){
            return false;
        }
        if(enterprise.getManagementModel()!=null){
            boolean isPlatform =  EnterpriseDTO.ManagementModel.PLATFORM.name().equals(enterprise.getManagementModel().name());
            if(isPlatform){
                return false;
            }
        }
        Integer dbExists= enterpriseDataClient.enterpriseDBIsExists(enterpriseGuid);
        Integer redisExists= enterpriseDataClient.enterpriseDBIsExists(enterpriseGuid);
        return dbExists != null && redisExists != null && dbExists != 0 && redisExists != 0;
    }
}
