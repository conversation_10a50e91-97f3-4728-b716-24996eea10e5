package com.holderzone.saas.store.staff.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.user.RoleDTO;
import com.holderzone.saas.store.dto.user.RoleQueryDTO;
import com.holderzone.saas.store.staff.entity.domain.RoleDO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className RoleMapper
 * @date 19-1-15 下午2:31
 * @description 角色表Mapper接口
 * @program holder-saas-store-staff
 */
@Repository
public interface RoleMapper extends BaseMapper<RoleDO> {
    Integer countByRoleQueryDTO(RoleQueryDTO roleQueryDTO);

    List<RoleDTO> selectListByRoleQueryDTO(RoleQueryDTO roleQueryDTO);
}

