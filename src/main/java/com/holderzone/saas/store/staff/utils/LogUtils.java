package com.holderzone.saas.store.staff.utils;

import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className LogUtils
 * @date 18-8-31 下午2:00
 * @description 日志工具类
 * @program holder-saas-store-staff
 */
@NoArgsConstructor
public class LogUtils {
    public static final String MODULE_USER = "帐号管理";

    public static final String MODULE_ROLE = "角色管理";

    public static final String MODULE_ORGANIZATION = "组织管理";

    public static final String CHILD_MODULE_USER_CREATE = "新建员工";

    public static final String CHILD_MODULE_USER_DELETE = "删除员工";

    public static final String CHILD_MODULE_USER_UPDATE = "编辑员工";

    public static final String CHILD_MODULE_ROLE_CREATE = "新建角色";

    public static final String CHILD_MODULE_ROLE_DELETE = "删除角色";

    public static final String CHILD_MODULE_ROLE_UPDATE = "编辑角色";

}
