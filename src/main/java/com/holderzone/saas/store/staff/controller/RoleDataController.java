package com.holderzone.saas.store.staff.controller;

import com.holderzone.saas.store.dto.user.DefaultMenuChecked;
import com.holderzone.saas.store.dto.user.TerminalDTO;
import com.holderzone.saas.store.dto.user.TerminalSourceDTO;
import com.holderzone.saas.store.staff.service.RoleDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className RoleDataController
 * @date 19-2-13 上午10:48
 * @description 角色权限相关接口
 * @program holder-saas-store-staff
 */
@RestController
@RequestMapping("/role_data")
@Api(description = "角色权限相关接口")
public class RoleDataController {

    @Autowired
    private RoleDataService roleDataService;

    @ApiOperation(value = "根据角色guid查询终端信息")
    @PostMapping("/query_role_terminal")
    public List<TerminalDTO> queryRoleTerminal(@ApiParam("角色guid") @RequestParam String roleGuid) {
        return roleDataService.queryRoleTerminal(roleGuid);
    }

    @ApiOperation(value = "根据终端guid与角色guid查询模块/菜单及服务资源")
    @PostMapping("/query_role_terminal_data")
    public DefaultMenuChecked queryRoleData(@ApiParam("角色guid") @RequestParam String roleGuid,
                                            @ApiParam("终端guid") @RequestParam String terminalGuid) {
        return roleDataService.queryRoleData(roleGuid, terminalGuid);
    }

    @ApiOperation(value = "保存角色授权信息（多个终端分开保存）")
    @PostMapping("/save")
    public boolean saveRoleData(@RequestBody TerminalSourceDTO terminalSourceDTO) {
        return roleDataService.saveRoleData(terminalSourceDTO);
    }

}
