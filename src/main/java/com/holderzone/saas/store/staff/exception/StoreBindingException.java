package com.holderzone.saas.store.staff.exception;

import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className StoreBindingException
 * @date 18-11-5 上午9:43
 * @description 定义android端登陆异常-参数bing错误
 * @program holder-saas-store-staff
 */
@NoArgsConstructor
public class StoreBindingException extends RuntimeException {
    private static final long serialVersionUID = -868444582701771795L;

    public StoreBindingException(String message) {
        super(message);
    }

    public StoreBindingException(String message, Throwable cause) {
        super(message, cause);
    }
}