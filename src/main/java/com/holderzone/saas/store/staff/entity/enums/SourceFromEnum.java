package com.holderzone.saas.store.staff.entity.enums;

public enum SourceFromEnum {

    BUILD_SELF(0, "自己创建"),
    ROLE_LIST(1, "角色列表");

    private Integer code;
    private String desc;

    SourceFromEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (SourceFromEnum c : SourceFromEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }

}
