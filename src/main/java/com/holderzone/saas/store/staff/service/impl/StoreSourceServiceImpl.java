package com.holderzone.saas.store.staff.service.impl;

import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.resource.common.dto.authorization.ModuleResourceDTO;
import com.holderzone.resource.common.dto.authorization.ProductResource;
import com.holderzone.resource.common.dto.authorization.TerminalResourceDTO;
import com.holderzone.resource.common.dto.data.ServerDTO;
import com.holderzone.resource.common.dto.data.SourceDTO;
import com.holderzone.saas.store.staff.entity.domain.RoleSourceDO;
import com.holderzone.saas.store.staff.entity.domain.StoreSourceDO;
import com.holderzone.saas.store.staff.mapper.RoleSourceMapper;
import com.holderzone.saas.store.staff.mapper.StoreSourceMapper;
import com.holderzone.saas.store.staff.service.StoreSourceService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MenuServiceImpl
 * @date 19-1-15 下午2:31
 * @description 企业/门店关联资源相关操作
 * @program holder-saas-store-staff
 */
@Slf4j
@Service
@AllArgsConstructor
public class StoreSourceServiceImpl extends ServiceImpl<StoreSourceMapper, StoreSourceDO> implements StoreSourceService {

    private final StoreSourceMapper storeSourceMapper;

    private final RoleSourceMapper roleSourceMapper;


    /**
     * 同步云端进行产品授权（对企业/门店新建或叠加授权），处理逻辑：只保存最底层的模块信息，同时数据铺平到source层，保存所有的source信息
     *
     * @param productResource 产品授权信息
     * @param storeGuid       门店guid
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveProductAuth(ProductResource productResource, String storeGuid) {
        return handleProductAuth(productResource, storeGuid);
    }

    private static String getMd5(MD5 md5Utils, StoreSourceDO dto) {
        StoreSourceDO dto1Copy = new StoreSourceDO();
        BeanUtils.copyProperties(dto, dto1Copy);
        dto1Copy.setId(null);
        dto1Copy.setGmtCreate(null);
        dto1Copy.setGmtModified(null);
        return md5Utils.digestHex(dto1Copy.toString());
    }

    private static String getStoreSourceKey(MD5 md5Utils, StoreSourceDO s) {
        return getStoreSourceKey(md5Utils, s.getTerminalGuid(), s.getModuleGuid(), s.getSourceGuid());
    }

    private static String getStoreSourceKey(MD5 md5Utils, String... key) {
        return md5Utils.digestHex(StringUtils.arrayToDelimitedString(key, "-"));
    }

    /**
     * 同步云端修改产品授权信息
     * 处理逻辑：先全量更新产品的授权信息（hss_store_source表数据先删除再新增），再对已分配出去的资源进行处理；对已分配出去的资源（hss_role_source表中数据）若已分配的资源不在新产品中存在则删除原来的角色-资源关系，其他不做处理
     *
     * @param productResource 新的产品授权信息
     * @param storeGuid       门店guid
     * @return 执行结果
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public boolean updateProductAuth(ProductResource productResource, String storeGuid) {
        String productGuid = productResource.getProductGuid();

        List<StoreSourceDO> storeSourceOld = storeSourceMapper.selectList(
                new LambdaQueryWrapper<StoreSourceDO>()
                        .eq(StoreSourceDO::getProductGuid, productGuid)
                        .eq(StringUtils.hasText(storeGuid), StoreSourceDO::getStoreGuid, storeGuid));
        List<StoreSourceDO> storeSourceNew = this.getStoreSourceListByProductResource(productResource, storeGuid);

        MD5 md5Utils = new MD5();

        Map<String, StoreSourceDO> storeSourceOldMap = new HashMap<>(100);
        storeSourceOld.forEach(s -> storeSourceOldMap.put(getStoreSourceKey(md5Utils, s), s));
        Map<String, StoreSourceDO> storeSourceNewMap = new HashMap<>(100);
        storeSourceNew.forEach(s -> storeSourceNewMap.put(getStoreSourceKey(md5Utils, s), s));
        List<Long> toDeletedIdList = storeSourceOld.stream().filter(s -> !storeSourceNewMap.containsKey(getStoreSourceKey(md5Utils, s)))
                .map(StoreSourceDO::getId).collect(Collectors.toList());
        List<StoreSourceDO> toUpdateList = storeSourceOld.stream()
                .filter(s -> {
                    StoreSourceDO newOne = storeSourceNewMap.get(getStoreSourceKey(md5Utils, s));
                    if (newOne == null) {
                        return false;
                    }
                    String oldMd5 = getMd5(md5Utils, s);
                    String newMd5 = getMd5(md5Utils, newOne);
                    return !oldMd5.equals(newMd5);
                }).map(s2 -> {
                    StoreSourceDO newOne = storeSourceNewMap.get(getStoreSourceKey(md5Utils, s2));
                    newOne.setId(s2.getId());
                    log.info("old {}", s2.toString());
                    log.info("new {}", newOne.toString());
                    return newOne;
                }).collect(Collectors.toList());
        List<StoreSourceDO> toSaveList = storeSourceNew.stream().filter(s -> !storeSourceOldMap.containsKey(getStoreSourceKey(md5Utils, s))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(toDeletedIdList)) {
            storeSourceMapper.deleteBatchIds(toDeletedIdList);
        }
        if (!CollectionUtils.isEmpty(toUpdateList)) {
            this.updateBatchById(toUpdateList);
        }
        if (!CollectionUtils.isEmpty(toSaveList)) {
            this.saveBatch(toSaveList);
        }

        log.info("更新产品资源 {} 删除{},更新{},新增{} "
                , EnterpriseIdentifier.getEnterpriseGuid(), toDeletedIdList.size(), toUpdateList.size(), toSaveList.size());
        List<String> terminalGuidList = productResource.getTerminalList().stream()
                .map(TerminalResourceDTO::getTerminalGuid)
                .collect(Collectors.toList());
        storeSourceOld.clear();
        storeSourceNew.clear();
        storeSourceOldMap.clear();
        storeSourceNewMap.clear();
        toDeletedIdList.clear();
        toUpdateList.clear();
        toSaveList.clear();
        if (CollectionUtils.isEmpty(terminalGuidList)) {
            return true;
        }

//        int deleted = roleSourceMapper.updateModifiedRoleSource(terminalGuidList);
        // 为了避免死锁问题，改成单表的
        List<StoreSourceDO> list = this.list();
        if (!CollectionUtils.isEmpty(list)) {
            List<String> terGuidList = list.stream()
                    .map(StoreSourceDO::getTerminalGuid)
                    .collect(Collectors.toList());
            List<RoleSourceDO> sourceDOList = roleSourceMapper.selectList(new LambdaQueryWrapper<RoleSourceDO>()
                    .in(RoleSourceDO::getTerminalGuid, terGuidList)
            );
            sourceDOList.forEach(sourceDO ->
                    list.forEach(
                            storeSourceDO -> {

                                if (Objects.equals(sourceDO.getSourceGuid(), storeSourceDO.getSourceGuid())
                                        && Objects.equals(sourceDO.getTerminalGuid(), storeSourceDO.getTerminalGuid())) {
                                    sourceDO.setSourceUrl(storeSourceDO.getSourceUrl());
                                    sourceDO.setSourceCode(storeSourceDO.getSourceCode());
                                    roleSourceMapper.updateById(sourceDO);
                                }

                            }
                    )

            );
            log.info("更新角色资源");
        }
        int deleted = roleSourceMapper.deleteUnnecessaryRoleSource(terminalGuidList);
        log.info("更新角色资源 {} 删除{}", EnterpriseIdentifier.getEnterpriseGuid(), deleted);
        return true;
    }

    @Override
    public void deleteProductAuth(String storeGuid, String productGuid, String chargeGuid) {
        remove(wrapperByGuid(storeGuid, productGuid, chargeGuid));
    }

    private Wrapper<StoreSourceDO> wrapperByGuid(String storeGuid, String productGuid, String chargeGuid) {
        LambdaQueryWrapper<StoreSourceDO> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(storeGuid)) {
            wrapper.eq(StoreSourceDO::getStoreGuid, storeGuid);
        } else {
            wrapper.isNull(StoreSourceDO::getStoreGuid);
        }
        return wrapper
                .eq(StoreSourceDO::getProductGuid, productGuid)
                .eq(StoreSourceDO::getChargeGuid, chargeGuid);
    }

    private List<StoreSourceDO> getStoreSourceListByProductResource(ProductResource productResource, String storeGuid) {
        String productGuid = productResource.getProductGuid();
        String chargeGuid = productResource.getChargeGuid();
        return productResource.getTerminalList().stream()
                .flatMap(terminalResourceDTO -> terminalResourceDTO.getModuleResourceList()
                        .stream()
                        .flatMap(moduleResourceDTO -> {
                            List<ModuleResourceDTO> leafModuleList = new ArrayList<>();
                            parseLeafModuleRecursive(moduleResourceDTO, leafModuleList);
                            return leafModuleList.stream();
                        })
                        .flatMap(moduleResourceDTO -> {
                            List<ServerDTO> serverList = moduleResourceDTO.getServerList();
                            if (CollectionUtils.isEmpty(serverList)) {
                                return Stream.of(assemblyStoreSource(
                                        storeGuid, productGuid, chargeGuid,
                                        terminalResourceDTO, moduleResourceDTO,
                                        null));
                            }
                            return serverList.stream()
                                    .map(ServerDTO::getSourceList)
                                    .flatMap(Collection::stream)
                                    .map(sourceDTO -> assemblyStoreSource(
                                            storeGuid, productGuid, chargeGuid,
                                            terminalResourceDTO, moduleResourceDTO,
                                            sourceDTO));
                        })
                )
                .collect(Collectors.toList());
    }

    private boolean handleProductAuth(ProductResource productResource, String storeGuid) {
        List<StoreSourceDO> collect = getStoreSourceListByProductResource(productResource, storeGuid);
        return this.saveBatch(collect);
    }

    private StoreSourceDO assemblyStoreSource(String storeGuid,
                                              String productGuid, String chargeGuid, TerminalResourceDTO terminalResourceDTO,
                                              ModuleResourceDTO moduleResourceDTO, SourceDTO sourceDTO) {
        StoreSourceDO storeSourceDO = new StoreSourceDO()
                .setStoreGuid(storeGuid)
                .setProductGuid(productGuid)
                .setChargeGuid(chargeGuid)
                .setTerminalGuid(terminalResourceDTO.getTerminalGuid())
                .setTerminalName(terminalResourceDTO.getTerminalName())
                .setTerminalCode(terminalResourceDTO.getTerminalCode())
                .setModuleGuid(moduleResourceDTO.getModuleGuid())
                .setModuleName(moduleResourceDTO.getModuleName())
                .setModuleType(moduleResourceDTO.getBusinessType())
                .setPageTitle(moduleResourceDTO.getTitle())
                .setPageUrl(moduleResourceDTO.getPageNo());
        if (sourceDTO != null) {
            storeSourceDO
                    .setSourceGuid(sourceDTO.getSourceGuid())
                    .setSourceCode(sourceDTO.getCode())
                    .setSourceName(sourceDTO.getName())
                    .setSourceUrl(sourceDTO.getUrl());
        }
        return storeSourceDO.setGmtCreate(DateTimeUtils.now()).setGmtModified(DateTimeUtils.now());
    }

    private LambdaQueryWrapper<StoreSourceDO> wrapperByProductGuid(String productGuid) {
        return new LambdaQueryWrapper<StoreSourceDO>()
                .eq(StoreSourceDO::getProductGuid, productGuid);
    }

    /**
     * 商户后台只保存最底层的模块与该模块下的资源，所有这里是 根据模块资源DTO获取最底层模块
     *
     * @param dto        ModuleResourceDTO
     * @param resultList 所有ModuleResourceDTO的最底级组成的集合
     * @return 当前入参的ModuleResourceDTO的所有最底级模块组成的集合
     */
    private void parseLeafModuleRecursive(ModuleResourceDTO dto, List<ModuleResourceDTO> resultList) {
        List<ModuleResourceDTO> moduleResourceList = dto.getModuleResourceList();
        if (CollectionUtils.isEmpty(moduleResourceList)) {
            resultList.add(dto);
        } else {
            moduleResourceList.forEach(p -> this.parseLeafModuleRecursive(p, resultList));
        }
    }
}
