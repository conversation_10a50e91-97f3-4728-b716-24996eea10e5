package com.holderzone.saas.store.staff.service;

import com.holderzone.saas.store.dto.user.AioPermissionDTO;
import com.holderzone.saas.store.dto.user.RolePermissionDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RedisService
 * @date 18-11-16 上午9:53
 * @description Redis操作相关接口
 * @program holder-saas-store-staff
 */
public interface RedisService {

    void putPermissionByUserGuid(String userGuid, List<RolePermissionDTO> rolePermissionDTOList);

    void putPermissionByRoleGuid(String roleGuid, List<RolePermissionDTO> rolePermissionDTOList);

    void deletePermissionByUserGuidList(List<String> userGuidList);

    void deletePermissionByRoleGuid(String roleGuid);

    List<RolePermissionDTO> getPermissionByUserGuid(String userGuid);

    List<RolePermissionDTO> getPermissionByRoleGuid(String roleGuid);

    void putAioPermissionByUserGuid(String userGuid, List<AioPermissionDTO> aioPermissionDTOList);

    void deleteAioPermissionByUserGuidList(List<String> userGuidList);

    List<AioPermissionDTO> getAioPermissionByUserGuid(String userGuid);
}
