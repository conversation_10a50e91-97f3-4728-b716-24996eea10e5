package com.holderzone.saas.store.staff;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.holderzone.saas.store.staff.utils.SpringContextUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableSwagger2
@SpringBootApplication
@EnableFeignClients
@EnableEurekaClient
@EnableApolloConfig
public class HolderSaasStoreStaffApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext app = SpringApplication.run(HolderSaasStoreStaffApplication.class, args);

        /**
         * 设置Spring容器上下文
         */
        SpringContextUtils.getInstance().setCfgContext((ConfigurableApplicationContext) app);
    }
}