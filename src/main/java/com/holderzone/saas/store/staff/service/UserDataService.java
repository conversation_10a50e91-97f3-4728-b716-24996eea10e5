package com.holderzone.saas.store.staff.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.user.LocalUserInfoDTO;
import com.holderzone.saas.store.dto.user.LocalUserReqDTO;
import com.holderzone.saas.store.dto.user.UserDTO;
import com.holderzone.saas.store.dto.user.UserSpinnerDTO;
import com.holderzone.saas.store.staff.entity.domain.UserDataDO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserDataService extends IService<UserDataDO> {

    /**
     * 保存用户数据权限
     *
     * @param userDTO
     */
    void save(UserDTO userDTO);

    void appendRole(String roleGuid);

    void updateNewStore(String storeGuid);

    /**
     * 查询用户数据权限
     *
     * @param userDTO
     */
    UserDTO query(UserDTO userDTO);

    /**
     * 修改用户数据权限
     *
     * @param userDTO
     */
    void update(UserDTO userDTO);

    /**
     * 删除用户门店管理规则
     *
     * @param userGuid
     */
    void deleteUserDataRules(String userGuid);

    /**
     * 查询用户可分配角色
     *
     * @return
     */
    UserDTO queryUserRolesDistributable();

    /**
     * 查询用户整单折扣、整单让价阈值
     *
     * @return
     */
    UserDTO queryUserDataThreshold();

    /**
     * 查询门店两种规则合并为门店的下拉框
     *
     * @return
     */
    UserSpinnerDTO queryStoreMergedSpinner();

    UserSpinnerDTO queryStoreMergedSpinnerByBrandGuid(SingleDataDTO singleDataDTO);

    /**
     * 查询组织列表下拉框
     *
     * @return
     */
    UserSpinnerDTO queryOrgMergedSpinner();

    /**
     * 查询品牌列表下拉框
     *
     * @return
     */
    UserSpinnerDTO queryBrandMergedSpinner();

    /**
     * 查询门店匹配交集条件下拉框
     *
     * @return
     */
    UserSpinnerDTO queryCondMergedSpinner();

    /**
     * 查询门店两种规则下拉框：包括门店列表下拉框和门店匹配交集条件下拉框
     *
     * @return
     */
    UserSpinnerDTO queryStoreAndCondSpinner();

    /**
     * 查询广义组织过滤器：包含企业、组织、门店的树形结构
     *
     * @return
     */
    UserSpinnerDTO queryGenericOrgSpinner();

    /**
     * 查询用户能管理的所有门店Guid
     *
     * @return
     */
    List<String> queryAllStoreGuid();

    List<LocalUserInfoDTO> queryAllUserToLocal(LocalUserReqDTO localUserReqDTO);

    List<UserDTO> queryAIOUsers(BaseDTO baseDTO);

    void batchDeleteUserDataRules(List<String> userGuidList);

    Integer getStaffAliveStoreCount();
}
