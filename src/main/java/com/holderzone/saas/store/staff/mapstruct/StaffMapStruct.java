package com.holderzone.saas.store.staff.mapstruct;

import com.holderzone.resource.common.dto.product.ModuleDTO;
import com.holderzone.saas.store.dto.user.ModuleSourceDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className StaffMapStruct
 * @date 18-9-20 上午10:32
 * @description 员工部分MapStruct
 * @program holder-saas-store-staff
 */
@Mapper
public interface StaffMapStruct {
    StaffMapStruct INSTANCE = Mappers.getMapper(StaffMapStruct.class);

    ModuleSourceDTO toModuleDTO(ModuleDTO moduleDTO);
}
