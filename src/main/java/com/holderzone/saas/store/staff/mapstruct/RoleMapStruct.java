package com.holderzone.saas.store.staff.mapstruct;

import com.holderzone.saas.store.dto.user.RoleDTO;
import com.holderzone.saas.store.staff.entity.domain.RoleDO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className RoleMapStruct
 * @date 19-1-15 下午2:40
 * @description 角色相关domain转换
 * @program holder-saas-store-staff
 */
@Component
@Mapper(componentModel = "spring")
public interface RoleMapStruct {
    RoleDO roleDTO2DO(RoleDTO roleDTO);

    RoleDTO roleDO2DTO(RoleDO roleDO);

    List<RoleDTO> roleDOList2DTOList(List<RoleDO> roleDOList);
}
