package com.holderzone.saas.store.staff.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.base.dto.message.MessageType;
import com.holderzone.framework.base.dto.message.ShortMessageDTO;
import com.holderzone.framework.base.dto.message.ShortMessageType;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.security.SecurityManager;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.resource.common.dto.enterprise.MessageConfigDTO;
import com.holderzone.resource.common.dto.extension.BaseDictionaryDTO;
import com.holderzone.resource.common.dto.holder.HolderResultDTO;
import com.holderzone.resource.common.enums.RegTypeEnum;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordConfirmDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordCreateDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.organization.OrgGeneralDTO;
import com.holderzone.saas.store.dto.organization.OrganizationDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.store.StoreProductDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.user.*;
import com.holderzone.saas.store.dto.user.req.UserFaceInputReqDTO;
import com.holderzone.saas.store.dto.user.resp.UserAuthorityBriefDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.dto.user.resp.UserFaceDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.TerminalEnum;
import com.holderzone.saas.store.staff.controller.AuthDTO;
import com.holderzone.saas.store.staff.entity.constant.Constants;
import com.holderzone.saas.store.staff.entity.domain.*;
import com.holderzone.saas.store.staff.entity.enums.DicTypeEnum;
import com.holderzone.saas.store.staff.entity.query.ModuleTypeQuery;
import com.holderzone.saas.store.staff.entity.query.UserCondQuery;
import com.holderzone.saas.store.staff.entity.query.UserSourceQuery;
import com.holderzone.saas.store.staff.entity.read.UserReadDO;
import com.holderzone.saas.store.staff.exception.StoreBindingException;
import com.holderzone.saas.store.staff.mapper.*;
import com.holderzone.saas.store.staff.mapstruct.UserMapstruct;
import com.holderzone.saas.store.staff.service.*;
import com.holderzone.saas.store.staff.service.remote.*;
import com.holderzone.saas.store.staff.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UserserviceImpl
 * @date 2018/8/17 11:18
 * @description 员工服务接口实现
 * @program holder-saas-store-staff
 */
@Slf4j
@Service
@SuppressWarnings("unchecked")
public class UserServiceImpl extends ServiceImpl<UserMapper, UserDO> implements UserService {

    private final UserUploadService userUploadService;

    private final UserRoleService userRoleService;

    private final RoleMapper roleMapper;

    private final DataDicMapper dataDicMapper;

    private final StoreSourceMapper storeSourceMapper;

    private final MenuMapper menuMapper;

    private final UserMapstruct userMapstruct;

    private final DistributedService distributedService;

    private final UserClient userClient;

    private final OrgFeignClient orgFeignClient;

    private final SmsClient smsClient;

    private final BusinessClient businessClient;

    private final EnterpriseClient enterpriseClient;

    private final AuthClient authClient;

    private final DynamicHelper dynamicHelper;

    private final UserDataService userDataService;

    private final ProductService productService;

    @Value("${holder.host:#{null}}")
    private String holderRequestHost;

    @Autowired
    public UserServiceImpl(UserUploadService userUploadService, UserRoleService userRoleService,
                           UserDataService userDataService,
                           RoleMapper roleMapper, DataDicMapper dataDicMapper,
                           StoreSourceMapper storeSourceMapper, MenuMapper menuMapper,
                           UserMapstruct userMapstruct, DistributedService distributedService,
                           UserClient userClient, OrgFeignClient orgFeignClient, SmsClient smsClient,
                           BusinessClient businessClient, EnterpriseClient enterpriseClient,
                           AuthClient authClient, DynamicHelper dynamicHelper, ProductService productService) {
        this.userUploadService = userUploadService;
        this.userRoleService = userRoleService;
        this.userDataService = userDataService;
        this.roleMapper = roleMapper;
        this.dataDicMapper = dataDicMapper;
        this.storeSourceMapper = storeSourceMapper;
        this.menuMapper = menuMapper;
        this.userMapstruct = userMapstruct;
        this.distributedService = distributedService;
        this.userClient = userClient;
        this.orgFeignClient = orgFeignClient;
        this.smsClient = smsClient;
        this.businessClient = businessClient;
        this.enterpriseClient = enterpriseClient;
        this.authClient = authClient;
        this.dynamicHelper = dynamicHelper;
        this.productService = productService;
    }

    @Override
    public String newAccount() {
        Predicate<String> accountPredicate =
                account -> count(new LambdaQueryWrapper<UserDO>().eq(UserDO::getAccount, account)) == 0;
        return String.valueOf(RandomCodeUtils.generateIgnoreDuplicates(accountPredicate, 3));
    }

    @Override
    public String newAuthCode() {
        Predicate<String> authCodePredicate =
                authCode -> count(new LambdaQueryWrapper<UserDO>().eq(UserDO::getAuthCode, authCode)) == 0;
        return String.valueOf(RandomCodeUtils.generateIgnoreDuplicates(authCodePredicate, 3));
    }

    @Override
    public String create(UserDTO userDTO) {
        // 判断账号，不允许创建管理员账号
        if (Constants.Account.ADMIN.equals(userDTO.getAccount()) ||
                count(new LambdaQueryWrapper<UserDO>().eq(UserDO::getAccount, userDTO.getAccount())) > 0) {
            throw new BusinessException("账号已存在，创建失败");
        }
        // 调用云端接口、判断手机号重复
        if (userClient.queryExistTel(userDTO.getPhone(), "") > 0) {
            throw new BusinessException("手机号重复，创建失败");
        }
        userDTO.setEnterpriseNo(UserContextUtils.getEnterpriseNo());
        UserDO userDO = userMapstruct.fromUserDTO(userDTO);
        // 生成可用授权码
        Predicate<String> authCodePredicate =
                authCode -> count(new LambdaQueryWrapper<UserDO>().eq(UserDO::getAuthCode, authCode)) == 0;
        Integer authCode = RandomCodeUtils.generate(authCodePredicate, 3);
        // 用户数据入库
        String userGuid = distributedService.nextUserGuid();
        userDO.setGuid(userGuid);
        userDO.setEnterpriseNo(UserContextUtils.getEnterpriseNo());
        userDO.setCreateStaffGuid(UserContextUtils.getUserGuid());
        userDO.setUpdateStaffGuid(UserContextUtils.getUserGuid());
        userDO.setPassword(SecurityManager.entryptMd5(userDO.getPassword(), true));
        userDO.setAuthCode(Optional.ofNullable(authCode).map(String::valueOf).orElse(null));
        userDO.setIsDeleted(Boolean.FALSE);
        Optional.ofNullable(userDTO.getUserOrg()).map(UserOrgDTO::getGuid).ifPresent(userDO::setOrgGuid);
        createUserOfficeIfAbsentThenSetUpOffice(userDTO, userDO);
        save(userDO);
        // 用户角色数据入库
        userRoleService.addUserRoleRelation(userGuid, userDTO.getUserRoles());
        // 上传用户到云端
        userUploadService.addUser(userDO);
        return userGuid;
    }

    @Override
    public void syncHolderUser(List<String> organizationIds) {
        // 通过组织id查询holder员工账号
        List<HolderUserDTO> holderUserList = findHolderUserByOrganizationIds(organizationIds);
        if (CollectionUtils.isEmpty(holderUserList)) {
            holderUserList = Lists.newArrayList();
        }
        List<UserDTO> userList = userMapstruct.holderUserDTOS2UserDTOS(holderUserList);

        List<UserDTO> createUserList = Lists.newArrayList();
        List<UserDTO> updateUserList = Lists.newArrayList();
        List<String> removeUserList = Lists.newArrayList();

        // 查询当前所有员工
        List<UserDO> oldUserList = findAll();
        if (CollectionUtils.isEmpty(oldUserList)) {
            // 如果还没有员工账号，则全部都是新增
            if (CollectionUtils.isNotEmpty(userList)) {
                createUserList.addAll(userList);
            }
        } else {
            if (CollectionUtils.isEmpty(userList)) {
                List<String> oldUserIds = oldUserList.stream().map(UserDO::getGuid).collect(Collectors.toList());
                removeUserList.addAll(oldUserIds);
            } else {
                // 筛选出需要新增的，需要修改的，需要删除的
                Map<String, UserDTO> userMap = userList.stream().collect(Collectors.toMap(UserDTO::getGuid, Function.identity(), (key1, key2) -> key1));
                for (UserDO oldUser : oldUserList) {
                    UserDTO userDTO = userMap.get(oldUser.getGuid());
                    if (Objects.isNull(userDTO)) {
                        removeUserList.add(oldUser.getGuid());
                    } else {
                        updateUserList.add(userDTO);
                    }
                    userMap.remove(oldUser.getGuid());
                }
                if (MapUtils.isNotEmpty(userMap)) {
                    createUserList.addAll(userMap.values());
                }
            }
        }
        log.info("同步员工createUserList:{},updateUserList:{},removeUserList:{}", createUserList, updateUserList, removeUserList);
        if (CollectionUtils.isNotEmpty(createUserList)) {
            createHolderUser(createUserList);
        }
        if (CollectionUtils.isNotEmpty(updateUserList)) {
            List<String> userIds = updateUserList.stream().map(UserDTO::getGuid).collect(Collectors.toList());
            List<UserDO> oldUpdateUserList = oldUserList.stream().filter(e -> userIds.contains(e.getGuid())).collect(Collectors.toList());
            updateHolderUser(updateUserList, oldUpdateUserList);
        }
        if (CollectionUtils.isNotEmpty(removeUserList)) {
            removeHolderUser(removeUserList);
        }
    }

    /**
     * 同步holder创建账号
     */
    private void createHolderUser(List<UserDTO> userList) {
        String enterpriseNo = UserContextUtils.getEnterpriseNo();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        String userGuid = UserContextUtils.getUserGuid();
        // 查询100000
        com.holderzone.resource.common.dto.user.UserDTO adminUser = userClient.getAdminByEnterpriseGuid(enterpriseGuid);
        if (Objects.nonNull(adminUser)) {
            userList = userList.stream().filter(e -> !e.getGuid().equals(adminUser.getUserGuid())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }
        // todo 调用云端接口、判断手机号重复


        userList.forEach(userDTO -> {
            UserDO userDO = userMapstruct.fromUserDTO(userDTO);
            // 生成可用授权码
            Predicate<String> authCodePredicate =
                    authCode -> count(new LambdaQueryWrapper<UserDO>().eq(UserDO::getAuthCode, authCode)) == 0;
            Integer authCode = RandomCodeUtils.generate(authCodePredicate, 3);
            // 生成账号
            Predicate<String> accountPredicate =
                    account -> count(new LambdaQueryWrapper<UserDO>().eq(UserDO::getAccount, account)) == 0;
            Integer accountCode = RandomCodeUtils.generateIgnoreDuplicates(accountPredicate, 3);

            // 用户数据入库
            userDO.setGuid(userDTO.getGuid());
            userDO.setAccount(String.valueOf(accountCode));
            userDO.setEnterpriseNo(enterpriseNo);
            userDO.setCreateStaffGuid(userGuid);
            userDO.setUpdateStaffGuid(userGuid);
            userDO.setRegType(RegTypeEnum.HOLDER_PLATFORM.getType());
            // userDO.setPassword(SecurityManager.entryptMd5(userDO.getPassword(), true));
            userDO.setAuthCode(Optional.ofNullable(authCode).map(String::valueOf).orElse(null));
            userDO.setIsEnable(Boolean.TRUE);
            userDO.setIsDeleted(Boolean.FALSE);
            baseMapper.saveUser(userDO);
            // 上传用户到云端
            userUploadService.addUser(userDO);
        });
    }

    /**
     * 同步holder修改账号
     */
    private void updateHolderUser(List<UserDTO> userList, List<UserDO> oldUserList) {
        Map<String, UserDO> oldUserMap = oldUserList.stream().collect(Collectors.toMap(UserDO::getGuid, Function.identity(), (key1, key2) -> key1));
        userList.forEach(userDTO -> {
            UserDO userDO = oldUserMap.get(userDTO.getGuid());
            // update
            userDO.setUpdateStaffGuid(UserContextUtils.getUserGuid());
            userDO.setName(userDTO.getName());
            userDO.setPassword(userDTO.getPassword());
            userDO.setOrgGuid(userDTO.getOrgGuid());
            update(userDO, new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userDO.getGuid()));
            // 修改用户同步至云端
            userUploadService.updateUser(userDO);
        });
    }


    /**
     * 同步holder删除账号
     */
    private void removeHolderUser(List<String> userGuids) {
        userGuids.forEach(userGuid -> {
            // 如果该账号没有创建新的角色或账号则可以删除
            /*
            if (!noDataCreatedBy(userGuid)) {
                throw new BusinessException("该账号已产生关联数据，不能删除");
            }
            */
            // 存在未交班记录不能删除
            HandoverRecordConfirmDTO confirmDTO = new HandoverRecordConfirmDTO();
            confirmDTO.setUserGuid(userGuid);
            if (businessClient.queryByUserGuid(confirmDTO) != null) {
                log.error("当前账号正在门店使用，不能删除，当前员工:{}", JacksonUtils.writeValueAsString(userGuid));
                // throw new BusinessException("当前账号正在门店使用，不能删除");
                return;
            }
            // 删除用户
            remove(new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userGuid));
            deleteUserDownStreamOp(userGuid);
        });
    }

    @Override
    public void createFromCloud(UserDTO userDTO) {
        LambdaQueryWrapper<UserDO> eq = new LambdaQueryWrapper<UserDO>()
                .eq((UserDO::getPhone), userDTO.getPhone()).orderByAsc(UserDO::getId).last("limit 1");
        UserDO obj = this.getOne(eq);
        if (obj != null) {
            this.update(userMapstruct.fromUserDTO(userDTO),
                    new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userDTO.getGuid()));
            return;
        }
        save(userMapstruct.fromUserDTO(userDTO));
    }

    @Override
    public void updateFromCloud(UserDTO userDTO) {
        this.update(userMapstruct.fromUserDTO(userDTO),
                new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userDTO.getGuid()));
    }

    @Override
    public void createBatchFromCloud(List<UserDO> userList) {
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }
        // todo 优化
        saveBatch(userList);
    }

    @Override
    public void updateBatchFromCloud(List<UserDO> userList) {
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }
        baseMapper.updateBatchFromCloud(userList);
    }

    @Override
    public UserOfficeDTO newUserOffice(UserOfficeDTO userOfficeDTO) {
        DataDicDO dataDicDO = dataDicMapper.selectOne(new LambdaQueryWrapper<DataDicDO>()
                .select(DataDicDO::getItemCode)
                .eq(DataDicDO::getTypeCode, DicTypeEnum.OFFICE.getTypeCode())
                .orderByDesc(DataDicDO::getItemCode)
                .last("limit 1"));
        Integer officeCode = Optional.ofNullable(dataDicDO)
                .map(DataDicDO::getItemCode)
                .map(integer -> integer + 1)
                .orElse(1);
        String officeName = userOfficeDTO.getName();
        Assert.hasText(officeName, "职位名称不得为空");
        dataDicMapper.insert(new DataDicDO()
                .setGuid(distributedService.nextOfficeGuid())
                .setTypeCode(DicTypeEnum.OFFICE.getTypeCode())
                .setTypeName(DicTypeEnum.OFFICE.getTypeName())
                .setItemCode(officeCode)
                .setItemName(officeName));
        return new UserOfficeDTO(officeCode, officeName);
    }

    @Override
    public List<UserOfficeDTO> userOfficeSpinner() {
        List<DataDicDO> arrayOfDataDicDO = dataDicMapper.selectList(new LambdaQueryWrapper<DataDicDO>()
                .eq(DataDicDO::getTypeCode, DicTypeEnum.OFFICE.getTypeCode()));
        if (CollectionUtils.isEmpty(arrayOfDataDicDO)) return Collections.emptyList();
        return arrayOfDataDicDO.stream()
                .map(dataDicDO -> new UserOfficeDTO(dataDicDO.getItemCode(), dataDicDO.getItemName()))
                .collect(Collectors.toList());
    }

    @Override
    public String update(UserDTO userDTO) {
        UserDO userDO = userMapstruct.fromUserDTO(userDTO);

        // 原来的user信息
        UserDO originalDO = this.getOne(new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userDTO.getGuid()));
        // 调用云端接口，判断手机号
        if (!originalDO.getPhone().equals(userDTO.getPhone())
                && userClient.queryExistTel(userDTO.getPhone(), originalDO.getGuid()) > 0) {
            throw new BusinessException("手机号重复，修改失败");
        }
        Integer count = baseMapper.selectCount(new LambdaQueryWrapper<UserDO>().eq(UserDO::getAccount, userDTO.getAccount()).ne(UserDO::getGuid, originalDO.getGuid()));
        if (count == null || count > 0) {
            throw new BusinessException("账号重复，修改失败");
        }
        userDO.setUpdateStaffGuid(UserContextUtils.getUserGuid());
        Optional.ofNullable(userDTO.getUserOrg()).map(UserOrgDTO::getGuid).ifPresent(userDO::setOrgGuid);
        createUserOfficeIfAbsentThenSetUpOffice(userDTO, userDO);
        userDO.setEnterpriseNo(null).setPassword(null).setAuthCode(null).setIsDeleted(null);
        update(userDO, new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userDO.getGuid()));
        // 更新用户角色数据
        userRoleService.updateUserRoleRelation(userDO.getGuid(), userDTO.getUserRoles());
        // 修改用户同步至云端
        userUploadService.updateUser(userDO);
        return userDTO.getGuid();
    }

    @Override
    public void updateTimeByGuid(String guid) {
        UserDO userDO = new UserDO();
        userDO.setGmtModified(LocalDateTime.now());
        update(userDO, new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, guid));
        UserDO user = getOne(new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, guid));
        // 修改用户同步至云端
        userUploadService.updateUser(user);
    }

    @Override
    public void enableOrDisable(UserDTO userDTO) {
        String userGuid = userDTO.getGuid();
        UserDO userDO = new UserDO().setIsEnable(userDTO.getIsEnable());
        update(userDO, new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userGuid));
        userUploadService.updateUser(userDO.setGuid(userGuid));
    }

    @Override
    public void delete(UserDTO userDTO) {
        // 如果该账号没有创建新的角色或账号则可以删除
        String userGuid = userDTO.getGuid();
        if (!noDataCreatedBy(userGuid)) {
            throw new BusinessException("该账号已产生关联数据，不能删除");
        }
        // 存在未交班记录不能删除
        HandoverRecordConfirmDTO confirmDTO = new HandoverRecordConfirmDTO();
        confirmDTO.setUserGuid(userGuid);
        if (businessClient.queryByUserGuid(confirmDTO) != null) {
            throw new BusinessException("当前账号正在门店使用，不能删除");
        }
        // 删除用户
        remove(new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userGuid));
        deleteUserDownStreamOp(userGuid);
    }

    @Override
    public void updatePwd(UpdatePwdDTO updatePwdDTO) {
        Assert.state(StringUtils.hasText(updatePwdDTO.getGuid())
                || StringUtils.hasText(updatePwdDTO.getOldPwd()), "用户GUID和原密码不能同时为空");
        String newPassword = updatePwdDTO.getNewPwd();
        boolean isUpdateOneSelf = !StringUtils.hasText(updatePwdDTO.getGuid());
        String userGuid = isUpdateOneSelf ? UserContextUtils.getUserGuid() : updatePwdDTO.getGuid();
        if (isUpdateOneSelf) {
            if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
                com.holderzone.resource.common.dto.user.UserDTO userById = userClient.findUserById(userGuid);
                if (!SecurityManager.decryptMd5(updatePwdDTO.getOldPwd(),
                        userById.getPassword(), true)) {
                    throw new BusinessException("原密码错误");
                }
                String encryptPassword = SecurityManager.entryptMd5(newPassword, true);
                userUploadService.updateUser(new UserDO().setGuid(userGuid).setPassword(encryptPassword));
                return;
            }
            UserDO userInSql = getOne(new LambdaQueryWrapper<UserDO>()
                    .select(UserDO::getPassword)
                    .eq(UserDO::getGuid, userGuid));
            if (null == userInSql) {
                throw new BusinessException("不存在该用户");
            }
            if (!SecurityManager.decryptMd5(updatePwdDTO.getOldPwd(), userInSql.getPassword(), true)) {
                throw new BusinessException("原密码错误");
            }
        } else {
            if (0 == count(new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userGuid))) {
                throw new BusinessException("不存在该用户");
            }
        }
        String encryptPassword = SecurityManager.entryptMd5(newPassword, true);
        UserDO userDO = new UserDO().setPassword(encryptPassword);
        update(userDO, new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userGuid));
        userUploadService.updateUser(userDO.setGuid(userGuid));
    }

    @Override
    public void resetPwd(ResetPwdDTO resetPwdDTO) {
        String userGuid = Optional.ofNullable(resetPwdDTO)
                .map(ResetPwdDTO::getGuid).filter(StringUtils::hasText)
                .orElse(UserContextUtils.getUserGuid());
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        Assert.hasText(userGuid, "userGuid不能为空");
        Assert.hasText(enterpriseGuid, "enterpriseGuid不能为空");
        if (Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            String newPassword = sendNewPwd(enterpriseGuid,
                    Objects.requireNonNull(UserContextUtils.getUserTel(), "管理员手机号不得为空"));
            String encryptPassword = SecurityManager.entryptMd5(newPassword, true);
            userUploadService.updateUser(new UserDO().setGuid(userGuid).setPassword(encryptPassword));
            return;
        }
        UserDO userInSql = getOne(new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userGuid));
        if (null == userInSql) throw new BusinessException("不存在该员工");
        String newPassword = sendNewPwd(enterpriseGuid, userInSql.getPhone());
        String encryptPassword = SecurityManager.entryptMd5(newPassword, true);
        UserDO userDO = new UserDO().setPassword(encryptPassword);
        update(userDO, new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userGuid));
        userUploadService.updateUser(userDO.setGuid(userGuid));
    }

    @Override
    public void resetPwdFromCloud(UserDTO userDTO) {
        update(new UserDO().setPassword(userDTO.getPassword()),
                new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userDTO.getGuid()));
    }

    @Override
    public Page<UserDTO> pageQuery(UserQueryDTO userQueryDTO) {
        UserCondQuery userCondQuery = userMapstruct.fromUserQueryDTO(userQueryDTO).setCurrentStaffGuid(UserContextUtils.getUserGuid());
        if (!CollectionUtils.isEmpty(userQueryDTO.getGenericOrgGuids())) {
            List<String> orgGuidsWithChildren = orgFeignClient.queryOrgChildren(userQueryDTO.getGenericOrgGuids());
            userCondQuery.setOrgGuidsWithChildren(orgGuidsWithChildren);
        }
        log.info("组织Guid集合{}", JacksonUtils.writeValueAsString(userCondQuery));
        IPage<UserReadDO> page = baseMapper.pageQueryUser(new PageAdapter<>(userQueryDTO), userCondQuery);
        List<String> orgGuids = page.getRecords().stream()
                .map(UserReadDO::getOrgGuid)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> orgNameMap = orgFeignClient.queryOrgNameByIdList(orgGuids).stream()
                .collect(Collectors.toMap(OrganizationDTO::getGuid, OrganizationDTO::getName));
        List<UserDTO> userResults = page.getRecords().stream()
                .map(userReadDO -> {
                    UserDTO userDTO = userMapstruct.toUserDTO(userReadDO);
                    String orgGuid = userReadDO.getOrgGuid();
                    if (StringUtils.hasText(orgGuid)) {
                        userDTO.setUserOrg(new UserOrgDTO()
                                .setGuid(orgGuid)
                                .setName(orgNameMap.get(orgGuid)));
                    }
                    return userDTO;
                })
                .collect(Collectors.toList());
        return new PageAdapter<>(page, userResults);
    }

    @Override
    public UserDO getByUserGuid(String userGuid) {
        QueryWrapper<UserDO> qw = new QueryWrapper<>();
        qw.lambda().eq(UserDO::getGuid, userGuid);
        return getOne(qw);
    }

    @Override
    public UserDTO query(UserDTO userDTO) {
        UserDO userDO = userMapstruct.fromUserDTO(userDTO);
        // 查询用户及其角色列表
        UserReadDO userReadDO = baseMapper.queryUserDetail(userDO);
        UserDTO userResult = userMapstruct.toUserDTO(userReadDO);
        // 查询用户所属组织
        String orgGuid = userReadDO.getOrgGuid();
        if (StringUtils.hasText(orgGuid)) {
            List<String> orgGuids = Collections.singletonList(userReadDO.getOrgGuid());
            Map<String, String> orgNameMap = orgFeignClient.queryOrgWithParentByIdList(orgGuids)
                    .entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey,
                            org -> org.getValue().stream()
                                    .map(OrganizationDTO::getName)
                                    .collect(Collectors.joining("-"))
                    ));
            userResult.setUserOrg(new UserOrgDTO().setGuid(orgGuid)
                    .setOrgNameTreeJoined(orgNameMap.get(orgGuid)));
        }
        // 用户担任职位
        if (Objects.nonNull(userReadDO.getOfficeCode()) && StringUtils.hasText(userReadDO.getOfficeName())) {
            userResult.setUserOffice(new UserOfficeDTO(userReadDO.getOfficeCode(), userReadDO.getOfficeName()));
        }
        // 查询企业的整个组织树
        List<OrgGeneralDTO> orgGeneralDTOS = orgFeignClient.queryErpOrgStore(1, 1);
        userResult.setEntireOrgTree(orgGeneralDTOS);

        return userResult;
    }

    @Override
    public ValidateRespDTO validate(ValidateDTO validateDTO) {
        // 设备是否注册
        StoreDeviceDTO serverStatus = orgFeignClient.findDeviceStatus(validateDTO.getDeviceId());
        if (Boolean.FALSE.equals(serverStatus.getRegister())) {
            throw new BusinessException("设备未注册");
        }

        // 门店是否存在，是否可用
        StoreDTO storeDTO = orgFeignClient.queryStoreByCode(validateDTO.getStoreNo());
        if (null == storeDTO) {
            throw new BusinessException("不存在当前门店");
        }
        if (!storeDTO.getIsEnable()) {
            throw new BusinessException("门店不可用，请联系管理员");
        }

        // 门店是否已开通服务
        Map<String, List<StoreProductDTO>> productInfo = productService.queryProductByIdList(
                Collections.singletonList(storeDTO.getGuid())
        );
        LocalDateTime productStart;
        LocalDateTime productEnd;
        if (!productInfo.containsKey(storeDTO.getGuid())) {
            throw new BusinessException("产品未授权或已过期");
        } else {
            List<StoreProductDTO> storeProductList = productInfo.get(storeDTO.getGuid());
            storeProductList = storeProductList.stream()
                    .filter(storeProductDTO -> storeProductDTO.getProductType() == 0)
                    .collect(Collectors.toList());
            log.info("门店产品列表：{}", JacksonUtils.writeValueAsString(storeProductList));
            if (CollectionUtils.isEmpty(storeProductList)) {
                throw new BusinessException("产品未授权或已过期");
            }
            productStart = storeProductList.stream()
                    .map(StoreProductDTO::getGmtProductStart)
                    .min(Comparator.naturalOrder())
                    .map(LocalDate::atStartOfDay)
                    .orElseThrow(() -> new BusinessException("产品服务数据不一致"));
            if (storeProductList.stream()
                    .map(StoreProductDTO::getGmtProductEnd)
                    .anyMatch(Objects::isNull)) {
                productEnd = null;
            } else {
                productEnd = storeProductList.stream()
                        .map(StoreProductDTO::getGmtProductEnd)
                        .max(Comparator.naturalOrder())
                        .map(localDate -> localDate.atTime(23, 59, 59))
                        .get();
            }
        }

        // 用户是否有当前终端权限
        // 一体机的非管理员用户，无当前终端权限则禁止登陆
        if (validateDTO.getDeviceTypeCode() == BaseDeviceTypeEnum.All_IN_ONE.getCode()
                && !Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            List<String> terminals = baseMapper.queryUserManagedTerminal(UserContextUtils.getUserGuid());
            if (!terminals.contains(validateDTO.getDeviceTypeCode().toString())) {
                throw new BusinessException("用户无当前终端权限");
            }
        }

        // 用户是否有当前门店权限
        // 用户数据权限中无当前门店权限则禁止登陆
        if (!userDataService.queryAllStoreGuid().contains(storeDTO.getGuid())) {
            throw new BusinessException("用户无当前门店权限");
        }

        // 设备未绑定
        if (!serverStatus.getBinding()) {
            if (Boolean.TRUE.equals(validateDTO.getBinding())) {
                // 特殊处理，与android端沟通好返回特殊的Result code，直接拿错误信息提示给用户
                throw new StoreBindingException("设备已解绑，请重新绑定门店");
            }
            // 建立门店-设备绑定关系
            StoreDeviceDTO dto = new StoreDeviceDTO();
            dto.setBinding(true);
            dto.setDeviceNo(validateDTO.getDeviceId());
            dto.setDeviceGuid(serverStatus.getDeviceGuid());
            dto.setCreateUserGuid(UserContextUtils.getUserGuid());
            dto.setStoreNo(storeDTO.getCode());
            dto.setStoreGuid(storeDTO.getGuid());
            dto.setStoreName(storeDTO.getName());
            dto.setDeviceType(validateDTO.getDeviceTypeCode());
            dto.setDeviceName(TerminalEnum.getTerminalCodeByDeviceType(validateDTO.getDeviceTypeCode()));
            dto.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            dto.setCreateUserName(UserContextUtils.getUserName());
            dto.setGmtCreate(DateTimeUtils.now());
            // 设备类型为一体机时设置sort值
            if (validateDTO.getDeviceTypeCode() == BaseDeviceTypeEnum.All_IN_ONE.getCode()) {
                dto.setSort(10000);
            }
            // 建立门店-设备绑定关系，同时将绑定信息同步到云端
            orgFeignClient.bindDeviceStatus(dto);
        }

        // 查询交班记录，并开启新的班次
        // 查询该门店员工的未交班记录，若没有未交班的记录则开班
        HandoverRecordDTO recordDTOPage = businessClient.queryByStoreGuidAndUserGuid(
                storeDTO.getGuid(), UserContextUtils.getUserGuid());
        if (null == recordDTOPage || 1 == recordDTOPage.getStatus()) {
            HandoverRecordCreateDTO handoverRecordCreateDTO = new HandoverRecordCreateDTO()
                    .setCreateUserName(UserContextUtils.getUserName())
                    .setCreateUserGuid(UserContextUtils.getUserGuid())
                    .setTerminalId(validateDTO.getDeviceId())
                    .setStoreName(storeDTO.getName())
                    .setStoreGuid(storeDTO.getGuid());
            businessClient.create(handoverRecordCreateDTO);
        }

        UserDO userDO = this.getByUserGuid(UserContextUtils.getUserGuid());
        if (Objects.isNull(userDO)) {
            throw new BusinessException("数据异常，请联系管理员");
        }

        // 组装返回实体
        ValidateRespDTO validateRespDTO = new ValidateRespDTO();
        validateRespDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid())
                .setEnterpriseName(UserContextUtils.getEnterpriseName())
                .setStoreGuid(storeDTO.getGuid())
                .setStoreName(storeDTO.getName())
                .setValidStartTime(productStart)
                .setValidEndTime(productEnd == null ? productStart.plusYears(100) : productEnd)
                .setProductStartTime(DateTimeUtils.localDateTime2Mills(productStart))
                .setProductEndTime(productEnd == null ? -1L : DateTimeUtils.localDateTime2Mills(productEnd))
                .setDeviceId(serverStatus.getDeviceGuid())
                .setUserGuid(UserContextUtils.getUserGuid())
                .setUserName(UserContextUtils.getUserName())
                .setUserAccount(UserContextUtils.getUserAccount())
                .setPhoneNumber(UserContextUtils.getUserTel())
                .setIsInputFace(!StringUtils.isEmpty(userDO.getFaceCode()))
                .setStoreNo(validateDTO.getStoreNo());

        // 查询经营模式
        BaseDictionaryDTO managementType = enterpriseClient.queryManagementType(UserContextUtils.getEnterpriseGuid());
        log.info("查询经营模式 managementType={}", JacksonUtils.writeValueAsString(managementType));
        if (Objects.nonNull(managementType) && Objects.nonNull(managementType.getCode())) {
            validateRespDTO.setManageTypeCode(managementType.getCode());
        }

        return validateRespDTO;
    }

    @Override
    public boolean checkUserAuthentication(AuthDTO authDTO) {
        try {
            // 手动切库
            String enterpriseGuid = authDTO.getEnterpriseGuid();
            dynamicHelper.changeDatasource(enterpriseGuid);
            dynamicHelper.changeRedis(enterpriseGuid);
            // 查询是否为基础类权限页面(模块)
            MenuDO menuInSql = menuMapper.selectOne(new LambdaQueryWrapper<MenuDO>()
                    .select(MenuDO::getIsEnable, MenuDO::getModuleGuid)
                    .eq(MenuDO::getMenuGuid, authDTO.getMenuGuid()));
            if (menuInSql == null) {
                throw new BusinessException("错误的MenuGuid: " + authDTO.getMenuGuid());
            }
            if (menuInSql.getIsEnable()) {
                List<StoreSourceDO> storeSourceList = storeSourceMapper.queryModuleType(new ModuleTypeQuery()
                        .setModuleGuid(menuInSql.getModuleGuid()).setRequestUri(authDTO.getRequestUri()));
                if (CollectionUtils.isEmpty(storeSourceList)) {
                    if (!SpringContextUtils.getInstance().isProdEnv()) {
                        MenuDO menuDO = menuMapper.selectOne(new LambdaQueryWrapper<MenuDO>()
                                .select(MenuDO::getMenuGuid, MenuDO::getMenuName,
                                        MenuDO::getMenuUrl, MenuDO::getModuleGuid)
                                .eq(MenuDO::getMenuGuid, authDTO.getMenuGuid()));
                        log.error("MenuGuid和RequestUrl不匹配，menu: {}，uri: {}",
                                JacksonUtils.writeValueAsString(menuDO), authDTO.getRequestUri());
                    }
                    return false;
                }
                if (storeSourceList.stream()
                        .map(StoreSourceDO::getModuleType).anyMatch("0"::equals)) {
                    return true;
                }
            }
            // 查询用户资源是否存在
            UserSourceQuery userSourceQuery = new UserSourceQuery()
                    .setUserGuid(authDTO.getUserGuid())
                    .setTerminalCode(authDTO.getTerminalCode())
                    .setRequestUri(authDTO.getRequestUri());
            if (menuInSql.getIsEnable()) {
                userSourceQuery.setMenuGuid(authDTO.getMenuGuid());
            }

            boolean isMatched;
            if (Constants.Account.ADMIN.equals(authDTO.getUserAccount())) {
                // 管理员只需要验证url存在于storeSource
                isMatched = baseMapper.countMatchedUrlOfAdmin(userSourceQuery) > 0;
                log.info("管理员：{}", isMatched ? "鉴权通过" : "鉴权未通过，没有权限");
            } else {
                // 否则，需联接用户、角色、权限进行验证
                isMatched = baseMapper.countMatchedUrl(userSourceQuery) > 0;
                log.info("非管理员：{}", isMatched ? "鉴权通过" : "鉴权未通过，没有权限");
            }
            return isMatched;
        } catch (Throwable e) {
            log.error("鉴权发生异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
            return false;
        } finally {
            dynamicHelper.clear();
        }
    }

    @Override
    public List<UserDTO> listUser(List<String> userGuidList) {
        if (CollectionUtils.isEmpty(userGuidList)) {
            return Collections.emptyList();
        }
        List<UserDO> list = list(new LambdaQueryWrapper<UserDO>().in(UserDO::getGuid, userGuidList));
        List<UserReadDO> readDOList = Lists.newArrayList();
        list.forEach(userDO -> {
            UserReadDO userReadDO = baseMapper.queryUserDetail(userDO);
            readDOList.add(userReadDO);
        });
        return userMapstruct.toUserDTO(readDOList);
    }

    @Override
    public boolean deleteUserDownStreamOp(String userGuid) {
        // 删除用户角色
        userRoleService.deleteUserRoleRelation(userGuid);
        // 删除用户门店管理规则
        userDataService.deleteUserDataRules(userGuid);
        // 踢出用户
        // authClient.delToken(userGuid);
        // 从云端删除用户
        userUploadService.deleteUser(userGuid);
        return true;
    }

    @Override
    public List<UserDTO> findByStoreGuid(UserQueryDTO userQueryDTO) {
        log.info("请求参数集合：{}", JacksonUtils.writeValueAsString(userQueryDTO));
        cn.hutool.core.lang.Assert.notNull(userQueryDTO, "参数异常！");
        UserCondQuery userCondQuery = new UserCondQuery();
        userCondQuery.setOrgGuidsWithChildren(userQueryDTO.getGenericOrgGuids());
        Optional.ofNullable(userQueryDTO.getShowPassword()).ifPresent(showPassword -> {
            userCondQuery.setShowPassword(showPassword);
            //先用showPassword判断操作员
            userCondQuery.setIsWaiter(1);
        });
        List<UserReadDO> userReadDOList = baseMapper.findByStoreGuid(userCondQuery);
        //按终端过滤掉无权限用户
        if (userQueryDTO.getDeviceTypeCode() != null && !Constants.Account.ADMIN.equals(UserContextUtils.getUserAccount())) {
            if (!CollectionUtils.isEmpty(userReadDOList)) {
                final List<String> userGuidList = userReadDOList.stream().map(UserReadDO::getGuid).collect(Collectors.toList());
                List<String> authorizedUserGuidList = baseMapper.queryUsersByTerminal(userGuidList, userQueryDTO.getDeviceTypeCode());
                userReadDOList.removeIf(u -> !authorizedUserGuidList.contains(u.getGuid()));
            }
        }
        return userMapstruct.toUserDTO(userReadDOList);
    }

    @Override
    public List<UserDO> findAll() {
        return list(new LambdaQueryWrapper<>());
    }

    @Override
    public List<HolderUserDTO> findHolderUserByOrganizationIds(List<String> organizationIds) {
        List<HolderUserDTO> holderUserList = Lists.newArrayList();
        // 通过组织guids查询对应组织下的员工
        String httpRequestUrl = String.format("%s/team/teamUserInfo/findUserDept", holderRequestHost).intern();
        List<Long> deptIds = organizationIds.stream().map(Long::valueOf).collect(Collectors.toList());
        log.info("通过部门ids查询holder员工请求url:{}，参数:{}", httpRequestUrl, JacksonUtils.writeValueAsString(deptIds));
        String result = HttpsClientUtils.doPost(httpRequestUrl, JSONObject.toJSONString(deptIds));
        log.info("通过部门ids查询holder员工返回:{}", result);
        if (StringUtils.isEmpty(result)) {
            log.error("通过部门ids查询holder员工失败,{}", result);
            return holderUserList;
        }
        HolderResultDTO<HolderUserDTO> holderResultDTO = JSON.parseObject(result, HolderResultDTO.class);
        if (holderResultDTO.getReturnCode() != 0) {
            log.error("查询holder组织机构失败,{}", holderResultDTO);
            return holderUserList;
        }
        holderUserList = JSONObject.parseArray(JSON.toJSONString(holderResultDTO.getDataList()), HolderUserDTO.class);
        return holderUserList;
    }

    /**
     * 手机号批量删除员工
     * 不抛出异常，返回错误提示
     *
     * @param userDTO 手机号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UserDTO> batchDelete(UserDTO userDTO) {
        List<UserDTO> userErrorList = Lists.newArrayList();
        // 如果该账号没有创建新的角色或账号则可以删除
        List<String> phoneList = userDTO.getPhoneList();
        if (CollectionUtils.isEmpty(phoneList)) {
            throw new BusinessException("手机号为空");
        }
        List<UserDO> userDOList = this.list(new LambdaQueryWrapper<UserDO>().in(UserDO::getPhone, phoneList));
        if (CollectionUtils.isEmpty(userDOList)) {
            log.warn("batchDelete未查询到员工信息,phoneList={}", phoneList);
            return userErrorList;
        }
        Map<String, UserDO> userDbMap = userDOList.stream().collect(Collectors.toMap(UserDO::getGuid, Function.identity(), (key1, key2) -> key2));
        List<String> userGuidList = new ArrayList<>(userDbMap.keySet());

        // 校验用户是否存在关联数据
        if (!userDTO.getIntegrateFlag()) {
            List<UserDO> createStaffUserList = this.list(new LambdaQueryWrapper<UserDO>()
                    .in(UserDO::getCreateStaffGuid, userGuidList));
            Map<String, List<UserDO>> createStaffUserMap = createStaffUserList.stream()
                    .collect(Collectors.groupingBy(UserDO::getCreateStaffGuid));
            List<RoleDO> createStaffRoleList = roleMapper.selectList(new LambdaQueryWrapper<RoleDO>()
                    .in(RoleDO::getCreateStaffGuid, userGuidList));
            Map<String, List<RoleDO>> createStaffRoleMap = createStaffRoleList.stream()
                    .collect(Collectors.groupingBy(RoleDO::getCreateStaffGuid));
            userDOList.forEach(user -> {
                List<UserDO> userList = createStaffUserMap.get(user.getGuid());
                List<RoleDO> roleList = createStaffRoleMap.get(user.getGuid());
                if (!CollectionUtils.isEmpty(userList) || !CollectionUtils.isEmpty(roleList)) {
                    throw new BusinessException(user.getPhone() + "已产生关联数据，不能删除");
                }
            });
        }

        // 存在未交班记录不能删除
        HandoverRecordConfirmDTO confirmDTO = new HandoverRecordConfirmDTO();
        confirmDTO.setUserGuidList(userGuidList);
        UserContextUtils.putErp(userDTO.getEnterpriseGuid());
        List<HandoverRecordDTO> handoverRecordDTOList = businessClient.listByUserGuid(confirmDTO);
        if (CollectionUtils.isNotEmpty(handoverRecordDTOList)) {
            Map<String, List<HandoverRecordDTO>> handoverMap = handoverRecordDTOList.stream()
                    .collect(Collectors.groupingBy(HandoverRecordDTO::getCreateGuid));
            Iterator<String> iterator = userGuidList.iterator();
            while (iterator.hasNext()) {
                String userGuid = iterator.next();
                List<HandoverRecordDTO> handoverRecord = handoverMap.get(userGuid);
                if (!CollectionUtils.isEmpty(handoverRecord)) {
                    UserDO userDO = userDbMap.get(userGuid);
                    userErrorList.add(new UserDTO().setPhone(userDO.getPhone()).setErrorMsg("当前账号正在门店使用，不能删除"));
                    iterator.remove();
                }
            }
        }

        if (CollectionUtils.isEmpty(userGuidList)) {
            return userErrorList;
        }
        // 删除用户
        remove(new LambdaQueryWrapper<UserDO>().in(UserDO::getGuid, userGuidList));
        // 删除用户角色
        userRoleService.batchDeleteUserRoleRelation(userGuidList);
        // 删除用户门店管理规则
        userDataService.batchDeleteUserDataRules(userGuidList);
        return userErrorList;
    }

    @Override
    public void deleteByPhone(String phone, Boolean integrateFlag) {
        // 如果该账号没有创建新的角色或账号则可以删除
        if (StringUtils.isEmpty(phone)) {
            throw new BusinessException("手机号为空");
        }
        UserDO userDO = this.getOne(new LambdaQueryWrapper<UserDO>().eq(UserDO::getPhone, phone));
        if (ObjectUtils.isEmpty(userDO)) {
            log.warn("没有查询到员工,phone={}", phone);
            return;
        }
        String userGuid = userDO.getGuid();
        if (!integrateFlag && !noDataCreatedBy(userGuid)) {
            throw new BusinessException("该账号已产生关联数据，不能删除");
        }
        // 存在未交班记录不能删除
        HandoverRecordConfirmDTO confirmDTO = new HandoverRecordConfirmDTO();
        confirmDTO.setUserGuid(userGuid);
        if (businessClient.queryByUserGuid(confirmDTO) != null) {
            throw new BusinessException("当前账号正在门店使用，不能删除");
        }
        // 删除用户
        remove(new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userGuid));
        // 删除用户角色
        userRoleService.deleteUserRoleRelation(userGuid);
        // 删除用户门店管理规则
        userDataService.deleteUserDataRules(userGuid);
    }

    @Override
    public UserFaceDTO getUserFaceInfo(SingleDataDTO dto) {
        UserDO userDO = this.getOne(new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getGuid, dto.getData()));
        UserFaceDTO faceDTO = new UserFaceDTO();
        if (ObjectUtils.isEmpty(userDO)) {
            return faceDTO;
        }
        faceDTO.setPhone(userDO.getPhone());
        faceDTO.setUserGuid(userDO.getGuid());
        faceDTO.setUserName(userDO.getName());
        faceDTO.setAccount(userDO.getAccount());
        faceDTO.setFaceCode(userDO.getFaceCode());
        faceDTO.setIsInputFace(!StringUtils.isEmpty(userDO.getFaceCode()));

        return faceDTO;
    }

    @Override
    public void inputFace(UserFaceInputReqDTO reqDTO) {
        UserDO userDO = this.getOne(new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getGuid, reqDTO.getUserGuid()));
        if (!ObjectUtils.isEmpty(userDO)) {
            userDO.setGmtModified(null);
            userDO.setFaceCode(reqDTO.getFaceCode());
            this.updateById(userDO);
        }
    }

    @Override
    public List<UserBriefDTO> storeUsers(String storeGuid) {
        //查询门店数据权限
        List<UserDataDO> userDatas = userDataService.list(new LambdaQueryWrapper<UserDataDO>().eq(UserDataDO::getStoreGuid, storeGuid));
        if (CollectionUtils.isEmpty(userDatas)) {
            return Collections.emptyList();
        }
        //通过guid查询用户信息
        List<String> userGuids = userDatas.stream().map(UserDataDO::getUserGuid).collect(Collectors.toList());
        List<UserDO> userDOS = this.list(new LambdaQueryWrapper<UserDO>()
                .in(UserDO::getGuid, userGuids)
                .select(UserDO::getGuid, UserDO::getName, UserDO::getAccount));
        List<UserBriefDTO> userBriefDTOS = new ArrayList<>();
        userDOS.forEach(userDO -> userBriefDTOS.add(new UserBriefDTO(userDO.getGuid(), userDO.getName(), userDO.getAccount())));
        return userBriefDTOS;
    }

    @Override
    public List<UserAuthorityBriefDTO> ReceiveUsers() {
        List<UserDO> userDOS = list(new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getIsReceive, true)
                .eq(UserDO::getIsDeleted, false)
                .eq(UserDO::getIsEnable, true));
        return userDOS.stream()
                .map(userDO -> {
                    UserAuthorityBriefDTO userAuthorityBriefDTO = new UserAuthorityBriefDTO();
                    userAuthorityBriefDTO.setPhone(userDO.getPhone());
                    userAuthorityBriefDTO.setUserGuid(userDO.getGuid());
                    userAuthorityBriefDTO.setUserName(userDO.getName());
                    userAuthorityBriefDTO.setAccount(userDO.getAccount());
                    return userAuthorityBriefDTO;
                }).collect(Collectors.toList());
    }

    /**
     * 根据用户的guid判断是否可以删除或修改
     *
     * @param createStaffGuid 创建人guid
     * @return true/false
     */
    private boolean noDataCreatedBy(String createStaffGuid) {
        return count(new LambdaQueryWrapper<UserDO>().eq(UserDO::getCreateStaffGuid, createStaffGuid)) == 0
                && roleMapper.selectCount(new LambdaQueryWrapper<RoleDO>().eq(RoleDO::getCreateStaffGuid, createStaffGuid)) == 0;
    }

    /**
     * 生成6位随机密码并发送短信，然后返回生成的密码
     *
     * @param enterpriseGuid 企业GUID
     * @param phone          电话号码
     * @return 生成的密码
     */
    private String sendNewPwd(String enterpriseGuid, String phone) {
        // 判断商户短信配置及剩余条数
        MessageConfigDTO messageConfigDTO = enterpriseClient.getMessageInfo(enterpriseGuid);
        if (messageConfigDTO == null || messageConfigDTO.getResidueCount() <= 0) {
            throw new BusinessException("商户短信条数不足，发送失败");
        }

        // 生成随机的六位数作为新密码
        String newPwd = MathRandomUtils.getNextRandomNumber();

        // 请求短信服务
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setMessageType(MessageType.SHORT_MESSAGE);
        ShortMessageDTO shortMessageDTO = new ShortMessageDTO();
        shortMessageDTO.setPhoneNumber(phone);
        shortMessageDTO.setShortMessageType(ShortMessageType.RESET_PASSWORD);
        shortMessageDTO.setParams(new HashMap<String, String>(1) {{
            put("Code", newPwd);
        }});
        messageDTO.setShortMessage(shortMessageDTO);
        smsClient.sendMessage(messageDTO);

        return newPwd;
    }

    /**
     * 创建职位（如果不存在），然后设置office字段值到UserDO中
     *
     * @param userDTO
     * @param userDO
     */
    private void createUserOfficeIfAbsentThenSetUpOffice(UserDTO userDTO, UserDO userDO) {
        UserOfficeDTO userOffice = userDTO.getUserOffice();
        if (null != userOffice) {
            Integer officeCode = userOffice.getCode();
            if (null != officeCode) {
                String officeName = userOffice.getName();
                Assert.hasText(officeName, "职位名称不得为空");
                userDO.setOfficeCode(officeCode);
                userDO.setOfficeName(officeName);
            } else {
                UserOfficeDTO userOfficeDTO = newUserOffice(userOffice);
                userDO.setOfficeCode(userOfficeDTO.getCode());
                userDO.setOfficeName(userOfficeDTO.getName());
            }
        }
    }


}