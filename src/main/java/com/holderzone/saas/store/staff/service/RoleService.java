package com.holderzone.saas.store.staff.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.user.RoleDTO;
import com.holderzone.saas.store.dto.user.RoleQueryDTO;
import com.holderzone.saas.store.staff.entity.domain.RoleDO;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className RoleService
 * @date 2018/8/17 17:04
 * @description 角色相关接口
 * @program holder-saas-store-staff
 */
public interface RoleService extends IService<RoleDO> {

    boolean createRole(RoleDTO roleDTO);

    boolean updateRole(RoleDTO roleDTO);

    Page<RoleDTO> queryPageByName(RoleQueryDTO roleQueryDTO);

    boolean deleteRole(String roleGuid);

    boolean queryExistUser(String roleGuid);

    boolean copyRole(String roleGuid);
}
