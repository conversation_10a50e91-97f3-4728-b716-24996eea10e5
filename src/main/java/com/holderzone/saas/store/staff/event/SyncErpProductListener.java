package com.holderzone.saas.store.staff.event;

import com.holderzone.framework.dds.starter.utils.DynamicInfoHelper;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.OrganizationDTO;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.resource.common.util.MessageType;
import com.holderzone.saas.store.staff.config.RocketMqConfig;
import com.holderzone.saas.store.staff.entity.bo.ProductBasicBO;
import com.holderzone.saas.store.staff.service.ProductService;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import com.holderzone.saas.store.staff.service.remote.EnterpriseDataClient;
import com.holderzone.saas.store.staff.utils.DynamicHelper;
import com.holderzone.saas.store.staff.utils.EnterpriseUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className InitEnterpriseProductListener
 * @date 19-2-22 上午9:58
 * @description 订阅云端产品基本信息消息（同步场景：新建企业时对产品授权、叠加产品时对产品授权）
 * @program holder-saas-store-staff
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.HOLDER_SAAS_PRODUCT_TOPIC,
        tags = RocketMqConfig.HOLDER_SAAS_PRODUCT_TAG,
        consumerGroup = RocketMqConfig.PRODUCT_CONSUMER_GROUP,
        consumeThreadMax = 4,
        consumeThreadMin = 1,
        reTryConsume = 4
)
@AllArgsConstructor
public class SyncErpProductListener extends AbstractRocketMqConsumer<RocketMqTopic, UnMessage<String>> {

    private final DynamicHelper dynamicHelper;

    private final ProductService productService;

    private final EnterpriseClient enterpriseClient;

    private final EnterpriseDataClient enterpriseDataClient;

    private final DynamicInfoHelper dynamicInfoHelper;

    /**
     * 创建企业：MessageType.ADD：erpGuid
     * 更新企业：MessageType.DELETE，MessageType.ADD：erpGuid
     * 商户续费：MessageType.UPDATE：erpGuid
     * 商户开通服务：MessageType.ADD：erpGuid
     * 商户取消服务：MessageType.DELETE：erpGuid
     * <p>
     * 商户自主注册：MessageType.ADD：erpGuid，storeGuid
     * 开通商户服务（合作商）：MessageType.ADD：erpGuid，storeGuid
     * <p>
     * 门店授权（即开通服务）：MessageType.ADD：erpGuid，storeGuid
     * 门店续费：MessageType.ADD：erpGuid，storeGuid
     * 门店变更服务：MessageType.ADD：erpGuid，storeGuid（这个正确吗？）
     * 门店取消服务：MessageType.DELETE：erpGuid，storeGuid（云平台未实现）
     * 门店定时生效：MessageType.ADD：erpGuid，storeGuid
     * 门店定时过期：MessageType.DELETE：erpGuid，storeGuid（云平台未实现）
     *
     * @param unMessage
     * @param messageExt
     * @return
     */
    @Override
    public boolean consumeMsg(UnMessage<String> unMessage, MessageExt messageExt) {
        log.info("产品消息消费，参数：{}，当前时间为：{}",
                JacksonUtils.writeValueAsString(unMessage), DateTimeUtils.nowString());

        String storeGuid = unMessage.getStoreGuid();
        String enterpriseGuid = unMessage.getEnterpriseGuid();
        if (!DynamicHelper.changeAndCheckDatasource(dynamicInfoHelper, enterpriseGuid)) {
            return true;
        }
        MessageType messageType = MessageType.getTypeByCode(unMessage.getMessageType());
        try {
            if (!EnterpriseUtils.checkEnterpriseExists(enterpriseDataClient, enterpriseClient, enterpriseGuid)) {
                return true;
            }
            // 若门店guid为空则代表对企业授权，使用EnterpriseDTO反序列化
            // 若门店guid不为空则代表对门店授权，使用OrganizationDTO反序列化
            ProductBasicBO productBasic;
            if (StringUtils.hasText(storeGuid)) {
                OrganizationDTO storeProduct = JacksonUtils.toObject(
                        OrganizationDTO.class,
                        unMessage.getMessage()
                );
                productBasic = assembly(storeGuid, storeProduct);
            } else {
                EnterpriseDTO erpProduct = JacksonUtils.toObject(
                        EnterpriseDTO.class,
                        unMessage.getMessage()
                );
                productBasic = assembly(erpProduct);
            }
            handle(productBasic, messageType);
            return true;
        } catch (Exception e) {
            log.warn("产品消息消费，发生异常，erpGuid：{}",
                    enterpriseGuid, e);
            return false;
        } finally {
            dynamicHelper.clear();
        }
    }

    private ProductBasicBO assembly(EnterpriseDTO erpProduct) {
        ProductBasicBO productBasic = new ProductBasicBO();
        productBasic.setEnterpriseGuid(erpProduct.getEnterpriseGuid());
        productBasic.setStoreGuid(null);
        productBasic.setProductGuid(erpProduct.getDictionaryKey());
        productBasic.setProductName(erpProduct.getProductName());
        productBasic.setChargeGuid(erpProduct.getDictionaryStandard());
        productBasic.setMchntTypeCode(erpProduct.getCommercialActivities());
        productBasic.setIsEnable(erpProduct.getEnabled() == null || erpProduct.getEnabled() == 1);
        Long startTime = erpProduct.getValidStartTime();
        Long endTime = erpProduct.getValidEndTime();
        setProductTypeAndTime(productBasic, startTime, endTime);
        return productBasic;
    }

    private ProductBasicBO assembly(String storeGuid, OrganizationDTO cloudProduct) {
        ProductBasicBO productBasic = new ProductBasicBO();
        productBasic.setEnterpriseGuid(cloudProduct.getEnterpriseGuid());
        productBasic.setStoreGuid(storeGuid);
        productBasic.setProductGuid(cloudProduct.getProductGuid());
        productBasic.setProductName(cloudProduct.getProductName());
        productBasic.setChargeGuid(cloudProduct.getChargeGuid());
        productBasic.setMchntTypeCode(cloudProduct.getCommercialActivities());
        productBasic.setIsEnable(true);
        Long effectiveStartTime = cloudProduct.getEffectiveStartTime();
        Long effectiveEndTime = cloudProduct.getEffectiveEndTime();
        setProductTypeAndTime(productBasic, effectiveStartTime, effectiveEndTime);
        return productBasic;
    }

    private void setProductTypeAndTime(ProductBasicBO productBasic, Long startTime, Long endTime) {
        // 数量类型的产品
        if ((startTime == null || startTime == 0)
                && (endTime == null || endTime == 0)) {
            productBasic.setProductType(1);
        }
        // 时间类型的产品
        else {
            productBasic.setProductType(0);
            if (startTime == null) {
                productBasic.setGmtProductStart(DateTimeUtils.now().toLocalDate());
            } else {
                LocalDateTime localDateTime = LocalDateTime.ofInstant(
                        Instant.ofEpochMilli(startTime),
                        DateTimeUtils.chinaZoneId()
                );
                productBasic.setGmtProductStart(localDateTime.toLocalDate());
            }
            if (endTime == null || endTime == -1) {
                productBasic.setGmtProductEnd(null);
            } else {
                LocalDateTime localDateTime = LocalDateTime.ofInstant(
                        Instant.ofEpochMilli(endTime),
                        DateTimeUtils.chinaZoneId()
                );
                productBasic.setGmtProductEnd(localDateTime.toLocalDate());
            }
        }
    }

    private void handle(ProductBasicBO productBasic, MessageType messageType) {
        switch (messageType) {
            case ADD:
            case UPDATE: {
                productService.saveOrUpdate(productBasic);
                break;
            }
            case DELETE: {
                productService.deleteProduct(productBasic);
                break;
            }
            default: {
                log.warn("不支持的操作类型：{}", messageType);
                break;
            }
        }
    }
}
