package com.holderzone.saas.store.staff.controller;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.invoice.UserAuthorityInvoiceDTO;
import com.holderzone.saas.store.dto.user.UserAuthorityDTO;
import com.holderzone.saas.store.dto.user.UserAuthorityQueryDTO;
import com.holderzone.saas.store.dto.user.UserAuthoritySaveDTO;
import com.holderzone.saas.store.dto.user.UserAuthorityUpdateDTO;
import com.holderzone.saas.store.dto.user.req.AuthorizationReqDTO;
import com.holderzone.saas.store.dto.user.req.UserFaceInputReqDTO;
import com.holderzone.saas.store.dto.user.resp.PermissionsRespDTO;
import com.holderzone.saas.store.dto.user.resp.UserAuthorityBriefDTO;
import com.holderzone.saas.store.dto.user.resp.UserFaceDTO;
import com.holderzone.saas.store.staff.service.UserAuthorityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/user_authority")
@Api(description = "账号权限管理接口")
public class UserAuthorityController {

    private final UserAuthorityService userAuthorityService;

    @Autowired
    public UserAuthorityController(UserAuthorityService userAuthorityService) {
        this.userAuthorityService = userAuthorityService;
    }

    @ApiOperation(value = "保存员工账号授权")
    @PostMapping(value = "/save_user_authority")
    public void saveUserAuthority(@RequestBody UserAuthoritySaveDTO userAuthoritySaveDTO) {
        log.info("保存账号权限入参：{}", JacksonUtils.writeValueAsString(userAuthoritySaveDTO));
        userAuthorityService.saveUserAuthority(userAuthoritySaveDTO);
    }

    @ApiOperation(value = "更新员工账号权限")
    @PostMapping(value = "/update")
    public void updateAuthority(@RequestBody UserAuthorityUpdateDTO userAuthorityUpdateDTO) throws IOException {
        log.info("更新账号权限入参：{}", JacksonUtils.writeValueAsString(userAuthorityUpdateDTO));
        userAuthorityService.updateAuthority(userAuthorityUpdateDTO);
    }

    @ApiOperation(value = "查询员工账号权限")
    @PostMapping(value = "/query_user_authority")
    public List<UserAuthorityDTO> queryUserAuthority(@RequestBody UserAuthorityQueryDTO userAuthorityQueryDTO) {
        log.info("查询账号权限入参：{}", JacksonUtils.writeValueAsString(userAuthorityQueryDTO));
        return userAuthorityService.queryUserAuthority(userAuthorityQueryDTO);
    }

    /**
     * 查询开票人信息
     */
    @ApiOperation(value = "查询开票人信息")
    @GetMapping(value = "/query_user_invoice")
    public UserAuthorityInvoiceDTO queryUserInvoice(@RequestParam String userGuid) {
        log.info("查询开票人信息入参：{}", JacksonUtils.writeValueAsString(userGuid));
        return userAuthorityService.queryUserInvoice(userGuid);
    }

    @ApiOperation(value = "aio授权")
    @PostMapping(value = "/authorize")
    public Boolean authorize(@RequestBody AuthorizationReqDTO reqDTO) {
        log.info("aio授权接口入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return userAuthorityService.authorize(reqDTO);
    }

    @ApiOperation(value = "aio授权并返回授权人信息")
    @PostMapping(value = "/authorize/user")
    public UserAuthorityBriefDTO authorizeUser(@RequestBody AuthorizationReqDTO reqDTO) {
        log.info("aio授权并返回授权人信息接口入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return userAuthorityService.authorizeUser(reqDTO);
    }

    @ApiOperation(value = "删除员工账号权限")
    @PostMapping(value = "/delete")
    public Boolean deleteAuthority(@RequestBody AuthorizationReqDTO deleteDTO) {
        log.info("删除员工账号权限：{}", JacksonUtils.writeValueAsString(deleteDTO));
        return userAuthorityService.deleteAuthority(deleteDTO);
    }

    @ApiOperation(value = "查询所有可升级权限")
    @PostMapping(value = "/query_authority")
    public List<PermissionsRespDTO> queryAuthority() {
        return userAuthorityService.queryAuthority();
    }

    @ApiOperation(value = "人脸授权")
    @PostMapping(value = "/authorize_face")
    public UserFaceDTO authorizeFace(@RequestBody @Validated AuthorizationReqDTO reqDTO) {
        log.info("[人脸授权]接口入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return userAuthorityService.authorizeFace(reqDTO);
    }

    @ApiOperation(value = "查询门店下是否有员工勾选自己设置的权限")
    @PostMapping(value = "/query_authority/any_match")
    public List<PermissionsRespDTO> queryAuthorityAnyMatch(@RequestBody @Validated UserAuthorityQueryDTO UserAuthorityQueryDTO) {
        log.info("[查询门店下是否有员工勾选自己设置的权限]接口入参：{}", JacksonUtils.writeValueAsString(UserAuthorityQueryDTO));
        return userAuthorityService.queryAuthorityAnyMatch(UserAuthorityQueryDTO);
    }
}
