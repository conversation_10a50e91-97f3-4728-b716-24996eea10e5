package com.holderzone.saas.store.staff.controller;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class AuthDTO implements Serializable {

    private static final long serialVersionUID = 7093534558822946947L;

    @NotBlank(message = "企业GUID不得为空")
    @ApiModelProperty("企业GUID")
    private String enterpriseGuid;

    @NotBlank(message = "用户GUID不得为空")
    @ApiModelProperty("用户GUID")
    private String userGuid;

    @NotBlank(message = "用户Account不得为空")
    @ApiModelProperty("用户Account")
    private String userAccount;

    @NotBlank(message = "终端编码不得为空")
    @ApiModelProperty("终端编码")
    private String terminalCode;

    @NotBlank(message = "菜单GUID不得为空")
    @ApiModelProperty("菜单GUID")
    private String menuGuid;

    @NotBlank(message = "请求路径不得为空")
    @ApiModelProperty("请求路径")
    private String requestUri;
}
