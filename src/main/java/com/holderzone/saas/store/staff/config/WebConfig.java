package com.holderzone.saas.store.staff.config;


import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WebConfig
 * @date 2018/08/14 13:31
 * @description
 * @program holder-saas-store-staff
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
//        registry.addInterceptor(new FakeInterceptor()).order(-1);
    }
}
