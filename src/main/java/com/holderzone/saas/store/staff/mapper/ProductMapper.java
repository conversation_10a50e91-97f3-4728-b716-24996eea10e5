package com.holderzone.saas.store.staff.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.staff.entity.domain.ProductDO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductMapper extends BaseMapper<ProductDO> {
    List<ProductDO> selectByStoreGuid(String storeGuid, boolean withEnterpriseProduct);

    Integer updateExpirationDateByStoreGuidAndProductGuid(String storeGuid, String productGuid, String endDate);

    Integer updateExpirationDateByProductGuid(String productGuid, String endDate);
}

