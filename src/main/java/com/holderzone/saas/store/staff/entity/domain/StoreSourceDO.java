package com.holderzone.saas.store.staff.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className StoreSourceDO
 * @date 19-1-23 上午9:44
 * @description
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hss_store_source")
public class StoreSourceDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 门店guid
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String storeGuid;

    /**
     * 产品guid
     */

    @TableField(strategy = FieldStrategy.IGNORED)
    private String productGuid;

    /**
     * 规格guid
     */

    @TableField(strategy = FieldStrategy.IGNORED)
    private String chargeGuid;

    /**
     * 终端guid
     */

    @TableField(strategy = FieldStrategy.IGNORED)
    private String terminalGuid;

    /**
     * 终端名
     */

    @TableField(strategy = FieldStrategy.IGNORED)
    private String terminalName;

    /**
     * 终端code
     */

    @TableField(strategy = FieldStrategy.IGNORED)
    private String terminalCode;

    /**
     * 模块guid（存产品最底级模块guid）
     */

    @TableField(strategy = FieldStrategy.IGNORED)
    private String moduleGuid;

    /**
     * 模块名（存产品最底级模块名）
     */

    @TableField(strategy = FieldStrategy.IGNORED)
    private String moduleName;

    /**
     * 模块类型（0-基础类业务，1-权限类业务）
     */

    @TableField(strategy = FieldStrategy.IGNORED)
    private String moduleType;

    /**
     * 跳转页标题
     */

    @TableField(strategy = FieldStrategy.IGNORED)
    private String pageTitle;

    /**
     * 跳转页url
     */

    @TableField(strategy = FieldStrategy.IGNORED)
    private String pageUrl;

    /**
     * 资源guid
     */

    @TableField(strategy = FieldStrategy.IGNORED)
    private String sourceGuid;

    /**
     * 资源名称
     */

    @TableField(strategy = FieldStrategy.IGNORED)
    private String sourceName;

    /**
     * 资源code
     */

    @TableField(strategy = FieldStrategy.IGNORED)
    private String sourceCode;

    /**
     * 资源url
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String sourceUrl;

    /**
     * 数据创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 数据修改时间
     */
    private LocalDateTime gmtModified;


}
