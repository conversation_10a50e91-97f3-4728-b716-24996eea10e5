package com.holderzone.saas.store.staff.service;

import com.holderzone.saas.store.dto.user.DefaultMenuChecked;
import com.holderzone.saas.store.dto.user.TerminalDTO;
import com.holderzone.saas.store.dto.user.TerminalSourceDTO;
import com.holderzone.saas.store.staff.entity.domain.RoleSourceDO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className RoleService
 * @date 19-1-15 下午2:31
 * @description 角色-操作权限相关接口
 * @program holder-saas-store-staff
 */
@Repository
public interface RoleDataService {
    List<TerminalDTO> queryRoleTerminal(String roleGuid);

    DefaultMenuChecked queryRoleData(String roleGuid, String terminalGuid);

    boolean saveRoleData(TerminalSourceDTO terminalSourceDTO);

    boolean batchSaveRoleData(List<RoleSourceDO> roleSourceDOList);
}
