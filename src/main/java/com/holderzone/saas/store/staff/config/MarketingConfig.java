package com.holderzone.saas.store.staff.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


@Data
@Configuration
public class MarketingConfig {

    @Value("${member.center.hostUrl}")
    private String centerUrl;

    @Value("${member.tool.hostUrl}")
    private String toolUrl;


    /**
     * 营销中心地址 转 小程序配置中心
     */
    public String centerTransferTool(String hostUrl) {
        return hostUrl.replace(toolUrl + "/#", centerUrl);
    }
}
