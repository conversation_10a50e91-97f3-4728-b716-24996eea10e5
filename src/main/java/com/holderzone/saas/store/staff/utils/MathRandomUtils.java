package com.holderzone.saas.store.staff.utils;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className MathRandomUtils
 * @date 18-9-7 上午9:50
 * @description 随机数生成工具类
 * @program holder-saas-store-staff
 */
public class MathRandomUtils {

    /**
     * 生成6位随机数
     * 实现思路：math.random()范围是[0.0, 1.0)，那么math.random()*9+1一定是小于10的，(Math.random()*9+1)*100000一定是<10*100000=1000000的一个数
     *
     * @return Random Number
     */
    public static String getNextRandomNumber() {
        return String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
    }
}
