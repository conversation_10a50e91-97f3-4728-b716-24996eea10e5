package com.holderzone.saas.store.staff.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import com.holderzone.saas.store.staff.entity.domain.AuthorityDO;
import com.holderzone.saas.store.staff.entity.domain.RoleDO;
import com.holderzone.saas.store.staff.entity.domain.UserDO;
import com.holderzone.saas.store.staff.entity.query.UserCondQuery;
import com.holderzone.saas.store.staff.entity.query.UserSourceQuery;
import com.holderzone.saas.store.staff.entity.read.UserReadDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UserMapper
 * @date 2018/01/15 下午14:519
 * @description User表相关接口
 * @program holder-saas-store-staff
 */
@Repository
public interface AuthorityMapper extends BaseMapper<AuthorityDO> {

}
