package com.holderzone.saas.store.staff.controller;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.user.*;
import com.holderzone.saas.store.dto.user.req.UserFaceInputReqDTO;
import com.holderzone.saas.store.dto.user.resp.UserAuthorityBriefDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.dto.user.resp.UserFaceDTO;
import com.holderzone.saas.store.staff.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UserController
 * @date 2018/8/14 11:48
 * @description 员工服务控制器
 * @program holder-saas-store-staff
 */
@Slf4j
@RestController
@RequestMapping("/user")
@Api(description = "员工管理接口")
public class UserController {

    private final UserService userService;

    @Autowired
    public UserController(UserService userService) {
        this.userService = userService;
    }

    @ApiOperation(value = "自动生成帐号", notes = "自动生成六位数字的帐号")
    @PostMapping(value = "/new_account")
    public String newAccount(@RequestParam(value = "enterpriseGuid", required = false) String enterpriseGuid) {
        if (!StringUtils.isEmpty(enterpriseGuid)) {
            UserContextUtils.putErp(enterpriseGuid);
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        }
        return userService.newAccount();
    }

    @ApiOperation(value = "自动生成授权码", notes = "自动生成六位数字的授权码")
    @PostMapping(value = "/new_auth_code")
    public String newAuthCode() {
        return userService.newAuthCode();
    }

    @ApiOperation(value = "新建职位", notes = "新建职位")
    @PostMapping(value = "/new_user_office")
    public UserOfficeDTO newUserOffice(@RequestBody UserOfficeDTO userOfficeDTO) {
        return userService.newUserOffice(userOfficeDTO);
    }

    @ApiOperation(value = "查询职位列表", notes = "查询职位列表")
    @PostMapping(value = "/list_user_office")
    public List<UserOfficeDTO> listUserOffice() {
        return userService.userOfficeSpinner();
    }

    @ApiOperation(value = "新建员工")
    @PostMapping(value = "/create")
    public String create(@RequestBody @Validated(UserDTO.Create.class) UserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("新建员工接口入参：{}", JacksonUtils.writeValueAsString(userDTO));
        }
        return userService.create(userDTO);
    }

    @ApiOperation(value = "更新员工信息")
    @PostMapping(value = "/update")
    public String update(@Validated(UserDTO.Update.class) @RequestBody UserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新员工信息接口入参：{}，", JacksonUtils.writeValueAsString(userDTO));
        }
        return userService.update(userDTO);
    }

    @ApiOperation(value = "更新员工表信息， 只更新user表的更新时间")
    @PutMapping(value = "/update_time/{guid}")
    public void updateTime(@PathVariable("guid") String guid) {
        log.info("更新user表的更新时间入参：{}", guid);
        userService.updateTimeByGuid(guid);
    }

    @ApiOperation(value = "查询用户信息")
    @PostMapping(value = "/query")
    public UserDTO query(@Validated(UserDTO.Query.class) @RequestBody UserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询用户信息接口入参：{}，", JacksonUtils.writeValueAsString(userDTO));
        }
        return userService.query(userDTO);
    }

    @ApiOperation(value = "查询用户人脸信息")
    @PostMapping(value = "/getUserFaceInfo")
    public UserFaceDTO getUserFaceInfo(@RequestBody SingleDataDTO dto) {
        log.info("查询用户信息,dto={}", JacksonUtils.writeValueAsString(dto));
        return userService.getUserFaceInfo(dto);
    }

    @ApiOperation(value = "批量查询用户信息")
    @PostMapping(value = "/list_user")
    public List<UserDTO> listUser(@RequestBody List<String> userGuidList) {
        if (log.isInfoEnabled()) {
            log.info("批量查询用户信息接口入参：{}", JacksonUtils.writeValueAsString(userGuidList));
        }
        return userService.listUser(userGuidList);
    }

    @ApiOperation(value = "启用(禁用)员工")
    @PostMapping(value = "/enable_or_disable")
    public void enableOrDisable(@Validated(UserDTO.EnableOrDisable.class) @RequestBody UserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("启用(禁用)员工入参：{}", JacksonUtils.writeValueAsString(userDTO));
        }
        userService.enableOrDisable(userDTO);
    }

    @ApiOperation(value = "删除员工")
    @PostMapping(value = "/delete")
    public void delete(@Validated(UserDTO.Delete.class) @RequestBody UserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("删除员工入参：{}", JacksonUtils.writeValueAsString(userDTO));
        }
        userService.delete(userDTO);
    }

    @ApiOperation(value = "手机号删除员工")
    @GetMapping(value = "/delete_by_phone")
    public void deleteByPhone(@RequestParam("phone") String phone, @RequestParam("integrateFlag") Boolean integrateFlag) {
        if (log.isInfoEnabled()) {
            log.info("手机号删除员工参：phone={},integrateFlag={}", phone, integrateFlag);
        }
        userService.deleteByPhone(phone, integrateFlag);
    }

    /**
     * 手机号批量删除员工
     *
     * @param userDTO 手机号
     */
    @ApiOperation(value = "手机号批量删除员工")
    @PostMapping(value = "/batch_delete")
    public List<UserDTO> batchDelete(@RequestBody UserDTO userDTO) {
        if (log.isInfoEnabled()) {
            log.info("手机号批量删除员工参：{}", JacksonUtils.writeValueAsString(userDTO));
        }
        EnterpriseIdentifier.setEnterpriseGuid(userDTO.getEnterpriseGuid());
        UserContextUtils.putErp(userDTO.getEnterpriseGuid());
        return userService.batchDelete(userDTO);
    }

    @ApiOperation(value = "分页查询员工信息")
    @PostMapping(value = "/page_query")
    public Page<UserDTO> pageQuery(@Validated @RequestBody UserQueryDTO userQueryDTO) {
        if (log.isInfoEnabled()) {
            log.info("分页查询员工信息入参：{}", JacksonUtils.writeValueAsString(userQueryDTO));
        }
        return userService.pageQuery(userQueryDTO);
    }

    @ApiOperation(value = "修改密码（用户自行输入密码）")
    @PostMapping(value = "/update_pwd")
    public void updatePwd(@Validated @RequestBody UpdatePwdDTO updatePwdDTO) {
        if (log.isInfoEnabled()) {
            log.info("修改密码入参：{}", JacksonUtils.writeValueAsString(updatePwdDTO));
        }
        userService.updatePwd(updatePwdDTO);
    }

    @ApiOperation(value = "修改密码（用户自行输入密码）")
    @PostMapping(value = "/self_update_pwd")
    public void selfModifyPwd(@Validated @RequestBody UpdatePwdDTO updatePwdDTO) {
        if (log.isInfoEnabled()) {
            log.info("修改密码入参：{}", JacksonUtils.writeValueAsString(updatePwdDTO));
        }
        if (!StringUtils.isEmpty(updatePwdDTO.getGuid())) throw new BusinessException("不能修改别人的密码");
        userService.updatePwd(updatePwdDTO);
    }

    @ApiOperation(value = "重置密码（生成随机密码并发送短信）")
    @PostMapping(value = "/reset_pwd")
    public void resetPwd(@RequestBody(required = false) ResetPwdDTO resetPwdDTO) {
        if (log.isInfoEnabled()) {
            log.info("重置密码请求入参：{}", Optional.ofNullable(resetPwdDTO)
                    .map(JacksonUtils::writeValueAsString).orElse("null"));
        }
        userService.resetPwd(resetPwdDTO);
    }

    /**
     * app登陆校验接口
     *
     * @param validateDTO validateDTO
     * @return 门店/设备基础信息
     */
    @ApiOperation(value = "app登陆验证接口（门店设备绑定-门店用户有效性-交接班校验）", notes = "验证成功返回门店/设备基础信息，门店使用产品信息等")
    @PostMapping(value = "/validate")
    public ValidateRespDTO validate(@RequestBody ValidateDTO validateDTO) {
        log.info("app登陆验证接口入参：{}", JacksonUtils.writeValueAsString(validateDTO));
        return userService.validate(validateDTO);
    }

    @ApiOperation(value = "鉴权")
    @PostMapping("/check_authorization")
    public boolean checkUserAuthorization(@Valid @RequestBody AuthDTO authDTO) {
        if (log.isInfoEnabled()) {
            log.info("鉴权入参:{}", JacksonUtils.writeValueAsString(authDTO));
        }
        return userService.checkUserAuthentication(authDTO);
    }

    @ApiOperation(value = "mdm删除用户下游接口")
    @PostMapping("/delete_down_stream/{userGuid}")
    public boolean deleteUserDownStream(@PathVariable String userGuid) {
        if (log.isInfoEnabled()) {
            log.info("mdm删除用户下游接口入参:{}", userGuid);
        }
        return userService.deleteUserDownStreamOp(userGuid);
    }

    @ApiOperation(value = "门店查询员工信息")
    @PostMapping(value = "/find_by_store_guid")
    public List<UserDTO> findByStoreGuid(@Validated @RequestBody UserQueryDTO userQueryDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询员工信息入参：{}", JacksonUtils.writeValueAsString(userQueryDTO));
        }
        return userService.findByStoreGuid(userQueryDTO);
    }

    @ApiOperation(value = "同步holder账号")
    @PostMapping(value = "/sync/holder")
    public void syncHolderUser(@RequestBody List<String> organizationIds) {
        if (log.isInfoEnabled()) {
            log.info("同步holder账号接口入参：{}", JacksonUtils.writeValueAsString(organizationIds));
        }
        userService.syncHolderUser(organizationIds);
    }

    @ApiOperation(value = "录入人脸")
    @PostMapping(value = "/inputFace")
    public void inputFace(@RequestBody UserFaceInputReqDTO reqDTO) {
        log.info("[录入人脸]入参={}", JacksonUtils.writeValueAsString(reqDTO));
        userService.inputFace(reqDTO);
    }

    @ApiOperation(value = "查询门店有权限的员工信息")
    @PostMapping(value = "/store_users")
    List<UserBriefDTO> storeUsers(@RequestParam("storeGuid") String storeGuid) {
        if (log.isInfoEnabled()) {
            log.info("查询门店有权限的员工信息入参：{}", JacksonUtils.writeValueAsString(storeGuid));
        }
        return userService.storeUsers(storeGuid);
    }

    @ApiOperation(value = "查询接收系统短信通知的员工信息")
    @GetMapping(value = "/receive_users")
    List<UserAuthorityBriefDTO> receiveUsers(@RequestParam("enterpriseGuid") String enterpriseGuid) {
        if (log.isInfoEnabled()) {
            log.info("查询接收系统短信通知的员工信息企业guid：{}", enterpriseGuid);
        }
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        UserContextUtils.putErp(enterpriseGuid);
        return userService.ReceiveUsers();
    }
}
