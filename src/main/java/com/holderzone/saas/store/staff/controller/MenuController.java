package com.holderzone.saas.store.staff.controller;

import com.holderzone.saas.store.dto.user.MenuDTO;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import com.holderzone.saas.store.staff.service.MenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MenuController
 * @date 19-2-10 上午10:31
 * @description 菜单-菜单资源相关接口
 * @program holder-saas-store-staff
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/menu")
@Api(description = "菜单-菜单资源相关接口")
public class MenuController {

    private final MenuService menuService;

    @Autowired
    public MenuController(MenuService menuService) {
        this.menuService = menuService;
    }

    @PostMapping("/get_merchant_menu")
    @ApiOperation(value = "根据用户guid与终端code获取该条件下的菜单信息")
    public List<MenuDTO> getMerchantMenu(@RequestParam("userGuid") String userGuid,
                                         @RequestParam("terminalCode") String terminalCode) {
        log.info("获取菜单接口入参：{}", "userGuid=" + userGuid + ", terminalCode=" + terminalCode);
        return menuService.getMerchantMenu(userGuid, terminalCode);
    }

    @PostMapping("/get_source_by_user")
    @ApiOperation(value = "根据某个用户在指定终端下的资源信息")
    public List<MenuSourceDTO> getSourceByUser(@RequestParam("terminalCode") String terminalCode) {
        log.info("获取用户的资源接口入参：{}", "terminalCode=" + terminalCode);
        return menuService.getSourceByUser(terminalCode);
    }

    @PostMapping("/get_module_source_by_user")
    @ApiOperation(value = "根据终端Code获取该终端下的模块资源信息")
    public List<MenuSourceDTO> getModuleSourceByUser(@RequestParam("terminalCode") String terminalCode) {
        log.info("获取用户的模块接口入参：{}", "terminalCode=" + terminalCode);
        return menuService.getModuleSourceByUser(terminalCode);
    }

    @PostMapping("/get_source_by_menu")
    @ApiOperation(value = "根据菜单guid获取该菜单下的资源信息")
    public List<MenuSourceDTO> getSourceByMenu(@RequestParam("menuGuid") String menuGuid) {
        log.info("获取菜单下的资源接口入参：{}", "menuGuid=" + menuGuid);
        return menuService.getSourceByMenu(menuGuid);
    }

    @PostMapping("/get_page_Url_by_menu")
    @ApiOperation(value = "根据菜单PageUrl获取该菜单下的资源信息")
    public List<MenuSourceDTO> getPageUrlByMenu(@RequestParam("pageUrl") String pageUrl) {
        log.info("获取菜单下的资源接口入参：{}", "pageUrl=" + pageUrl);
        return menuService.getPageUrlByMenu(pageUrl);
    }

    @ApiOperation(value = "外部获取员工权限")
    @GetMapping("/get_source_code_on_out")
    public List<String> queryUserSourceOnOut(@NotNull(message = "终端code不能为空") @RequestParam("terminalCode") Integer terminalCode,
                                              @NotNull(message = "用户或手机号不能为空") @RequestParam("userOrPhone") String userOrPhone,
                                              @NotNull(message = "企业guid不能为空") @RequestParam("enterpriseGuid") String enterpriseGuid) {
        log.info("外部获取员工权限,terminalCode={},userGuid={},enterpriseGuid={}", terminalCode, userOrPhone, enterpriseGuid);
        return menuService.queryUserSourceOnOut(terminalCode, userOrPhone, enterpriseGuid);
    }

}