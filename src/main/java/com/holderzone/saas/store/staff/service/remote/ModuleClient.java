package com.holderzone.saas.store.staff.service.remote;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.resource.common.dto.product.ModuleDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className ModuleClient
 * @date 18-9-18 上午11:23
 * @description
 * @program holder-saas-store-staff
 */
@Deprecated
@Component
@FeignClient(value = "product-service", fallbackFactory = ModuleClient.ServiceFallback.class)
public interface ModuleClient {

    /**
     * 查询所有模块
     *
     * @return List<ModuleDTO>
     */
    @GetMapping("/module/list")
    List<ModuleDTO> getModuleList();

    @Slf4j
    @Component
    class ServiceFallback implements FallbackFactory<ModuleClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ModuleClient create(Throwable cause) {
            return () -> {
                log.error(HYSTRIX_PATTERN, "getModuleList", null, ThrowableUtils.asString(cause));
                throw new BusinessException("调用服务查询模块失败");
            };
        }
    }
}
