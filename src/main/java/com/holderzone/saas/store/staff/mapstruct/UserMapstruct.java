package com.holderzone.saas.store.staff.mapstruct;

import com.holderzone.saas.store.dto.user.HolderUserDTO;
import com.holderzone.saas.store.dto.user.UserDTO;
import com.holderzone.saas.store.dto.user.UserQueryDTO;
import com.holderzone.saas.store.staff.entity.domain.UserDO;
import com.holderzone.saas.store.staff.entity.query.UserCondQuery;
import com.holderzone.saas.store.staff.entity.read.UserReadDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring")
public interface UserMapstruct {
    UserMapstruct INSTANCE = Mappers.getMapper(UserMapstruct.class);

    UserDO fromUserDTO(UserDTO userDTO);

//    UserDTO toUserDTO(UserDO userDO);

    @Mapping(source = "roles", target = "userRoles")
    UserDTO toUserDTO(UserReadDO userReadDO);

    List<UserDTO> toUserDTO(List<UserReadDO> userReadDO);

    UserCondQuery fromUserQueryDTO(UserQueryDTO userQueryDTO);


    @Mappings({
            @Mapping(source = "userId", target = "guid"),
            @Mapping(source = "username", target = "name"),
            @Mapping(source = "account", target = "phone"),
            @Mapping(source = "deptId", target = "orgGuid")
    })
    UserDTO holderUserDTO2UserDTO(HolderUserDTO holderUserDTO);

    List<UserDTO> holderUserDTOS2UserDTOS(List<HolderUserDTO> holderUserDTOS);

}
