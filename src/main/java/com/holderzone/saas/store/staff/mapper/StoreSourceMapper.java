package com.holderzone.saas.store.staff.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.staff.entity.domain.StoreSourceDO;
import com.holderzone.saas.store.staff.entity.query.ModuleTypeQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UserRoleMapper
 * @date 19-1-15 下午3:16
 * @description 企业/门店关联资源表DO
 * @program holder-saas-store-staff
 */
@Repository
public interface StoreSourceMapper extends BaseMapper<StoreSourceDO> {

    List<StoreSourceDO> queryModuleType(ModuleTypeQuery moduleTypeQuery);

    List<StoreSourceDO> querySourceByUserAndMenu(@Param("userGuid") String userGuid,
                                                 @Param("menuGuid") String menuGuid,
                                                 @Param("pageUrl") String pageUrl);

    StoreSourceDO querySourceByUserAndCode(@Param("userGuid") String userGuid, @Param("sourceCode") String sourceCode);

    StoreSourceDO querySourceByCode(@Param("sourceCode") String sourceCode);

    List<StoreSourceDO> querySourceByCodes(@Param("sourceCodes") List<String> sourceCodes);

    List<StoreSourceDO> queryAdminSource(@Param("pageUrl")String pageUrl);
}
