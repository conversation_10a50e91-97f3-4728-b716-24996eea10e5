package com.holderzone.saas.store.staff.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UserDataDO
 * @date 19-1-23 上午10:04
 * @description
 * @program holder-saas-store-staff
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hss_user_data")
@EqualsAndHashCode
@Builder
public class UserDataDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 用户guid
     */
    private String userGuid;

    /**
     * 规则guid
     */
    private String ruleGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 组织guid
     */
    private String organizationGuid;

    /**
     * 区域code：区市省Code以英文拼接
     */
    private String regionCode;

    /**
     * 区域名：区市省名称以英文拼接
     */
    private String regionName;

    /**
     * 品牌guid
     */
    private String brandGuid;

    /**
     * 门店、组织、区域、品牌是否已被删除
     */
    private Boolean isDeleted;
}
