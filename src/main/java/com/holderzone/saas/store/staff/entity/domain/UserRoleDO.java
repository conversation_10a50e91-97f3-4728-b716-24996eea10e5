package com.holderzone.saas.store.staff.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className UserRoleDO
 * @date 19-1-15 下午2:14
 * @description r_user_role表 DO
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "hss_r_user_role")
public class UserRoleDO implements Serializable {
    private static final long serialVersionUID = 5829162238319212183L;

    /**
     * 主键id，自增
     */
    @TableId
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 用户guid
     */
    private String userGuid;

    /**
     * 角色guid
     */
    private String roleGuid;
}
