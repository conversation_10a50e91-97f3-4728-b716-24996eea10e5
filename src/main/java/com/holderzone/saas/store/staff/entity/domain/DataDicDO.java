package com.holderzone.saas.store.staff.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hss_data_dictionary")
public class DataDicDO implements Serializable {

    private static final long serialVersionUID = 6676743751346682213L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 字典类型编码
     * 如：education
     */
    private String typeCode;

    /**
     * 字典类型名称
     * 如：学历
     */
    private String typeName;

    /**
     * 字典项目值
     * 如：1
     */
    private Integer itemCode;

    /**
     * 字典项目名
     * 如：大学本科
     */
    private String itemName;
}
