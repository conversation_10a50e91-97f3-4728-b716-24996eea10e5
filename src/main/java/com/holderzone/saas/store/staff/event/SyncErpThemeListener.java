package com.holderzone.saas.store.staff.event;

import com.holderzone.framework.dds.starter.exception.DynamicException;
import com.holderzone.framework.dds.starter.utils.DynamicInfoHelper;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.resource.common.dto.product.ProductThemeSyncDTO;
import com.holderzone.saas.store.staff.config.RocketMqConfig;
import com.holderzone.saas.store.staff.service.ProductThemeService;
import com.holderzone.saas.store.staff.service.remote.EnterpriseClient;
import com.holderzone.saas.store.staff.service.remote.EnterpriseDataClient;
import com.holderzone.saas.store.staff.utils.DynamicHelper;
import com.holderzone.saas.store.staff.utils.EnterpriseUtils;
import com.holderzone.saas.store.staff.utils.ThrowableExtUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className InitEnterpriseProductListener
 * @date 19-2-22 上午9:58
 * @description 订阅云端产品基本信息消息（同步场景：新建企业时对产品授权、叠加产品时对产品授权）
 * @program holder-saas-store-staff
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.HOLDER_SAAS_THEME_TOPIC,
        tags = RocketMqConfig.HOLDER_SAAS_THEME_TAG,
        consumerGroup = RocketMqConfig.THEME_CONSUMER_GROUP,
        consumeThreadMax = 4,
        consumeThreadMin = 1,
        reTryConsume = 4
)
@AllArgsConstructor
public class SyncErpThemeListener extends AbstractRocketMqConsumer<RocketMqTopic, UnMessage<String>> {

    private final DynamicHelper dynamicHelper;

    private final ProductThemeService productThemeService;

    private final EnterpriseClient enterpriseClient;

    private final EnterpriseDataClient enterpriseDataClient;

    private final DynamicInfoHelper dynamicInfoHelper;

    @Override
    public boolean consumeMsg(UnMessage<String> unMessage, MessageExt messageExt) {
        log.info("产品主题消息消费，参数：{}，当前时间为：{}",
                JacksonUtils.writeValueAsString(unMessage), DateTimeUtils.nowString());
        String enterpriseGuid = unMessage.getEnterpriseGuid();
        if(!DynamicHelper.changeAndCheckDatasource(dynamicInfoHelper,enterpriseGuid)){
            return true;
        }
        try {
            if(!EnterpriseUtils.checkEnterpriseExists(enterpriseDataClient,enterpriseClient,enterpriseGuid)){
                return true;
            }
            ProductThemeSyncDTO productThemeSyncDTO = JacksonUtils.toObject(
                    ProductThemeSyncDTO.class, unMessage.getMessage()
            );
            productThemeService.save(productThemeSyncDTO);
            return true;
        }  catch (Exception e) {
            log.error("产品主题MQ消息消费异常，ergGuid：{}，异常：{}",
                    unMessage.getEnterpriseGuid(), ThrowableExtUtils.asStringIfAbsent(e));
        } finally {
            dynamicHelper.clear();
        }
        return false;
    }
}
