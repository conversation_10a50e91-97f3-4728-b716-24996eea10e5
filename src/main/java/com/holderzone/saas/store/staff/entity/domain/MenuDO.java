package com.holderzone.saas.store.staff.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className MenuDO
 * @date 19-1-22 下午5:10
 * @description
 * @program holder-saas-store-staff
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@TableName("hss_menu")
public class MenuDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 菜单guid
     */
    private String menuGuid;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单地址
     */
    private String menuUrl;

    /**
     * 上级菜单ids（逗号分割，最顶级为模块guid）
     */
    private String parentIds;

    /**
     * 菜单图标
     */
    private String menuIcon;

    /**
     * 菜单排序值
     */
    private Long menuSort;

    /**
     * 模块guid（只存最底级模块id）
     */
    private String moduleGuid;

    /**
     * 终端guid
     */
    private String terminalGuid;

    /**
     * 是否启用（默认为1-已启用）
     */
    private Boolean isEnable;

    /**
     * 创建人guid
     */
    private String createStaffGuid;

    /**
     * 数据创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 数据同步时间
     */
    private LocalDateTime gmtSync;
}
