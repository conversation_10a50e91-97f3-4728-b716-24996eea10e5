package com.holderzone.saas.store.staff.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.product.ProductThemeSyncDTO;
import com.holderzone.resource.common.dto.product.ThemeSyncDTO;
import com.holderzone.saas.store.staff.entity.domain.ProductThemeDO;
import com.holderzone.saas.store.staff.mapper.ProductThemeMapper;
import com.holderzone.saas.store.staff.mapstruct.ProductMapstruct;
import com.holderzone.saas.store.staff.service.ProductThemeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProductThemeServiceImpl extends ServiceImpl<ProductThemeMapper, ProductThemeDO> implements ProductThemeService {

    private final ProductMapstruct productMapstruct;

    @Autowired
    public ProductThemeServiceImpl(ProductMapstruct productMapstruct) {
        this.productMapstruct = productMapstruct;
    }

    @Override
    public void save(ProductThemeSyncDTO productThemeSyncDTO) {
        // 删除企业、门店主题
        if (!StringUtils.hasText(productThemeSyncDTO.getStoreGuid())) {
            remove(new LambdaQueryWrapper<ProductThemeDO>().isNull(ProductThemeDO::getStoreGuid));
        } else {
            remove(new LambdaQueryWrapper<ProductThemeDO>().eq(ProductThemeDO::getStoreGuid, productThemeSyncDTO.getStoreGuid()));
        }
        // 插入企业/门店主题
        List<ThemeSyncDTO> themeSyncList = productThemeSyncDTO.getSyncDTOList();
        if (CollectionUtils.isEmpty(themeSyncList)) {
            return;
        }
        this.saveBatch(themeSyncList.stream()
                .map(themeSyncDTO -> {
                    ProductThemeDO productThemeDO = productMapstruct.bo2Do(themeSyncDTO);
                    productThemeDO.setStoreGuid(productThemeSyncDTO.getStoreGuid());
                    productThemeDO.setProductGuid(productThemeSyncDTO.getProductGuid());
                    long expireTime = themeSyncDTO.getExpireTime().getTime();
                    productThemeDO.setThemeExpireTime(DateTimeUtils.mills2LocalDateTime(expireTime));
                    return productThemeDO;
                })
                .collect(Collectors.toList()));
    }

    @Override
    public String query(String storeGuid, String terminalCode) {
        // 移动端查询可用主题
        if (StringUtils.hasText(storeGuid)) {
            List<ProductThemeDO> list = list(new LambdaQueryWrapper<ProductThemeDO>()
                    .eq(ProductThemeDO::getStoreGuid, storeGuid)
                    .eq(ProductThemeDO::getTerminalCode, terminalCode));
            if (!CollectionUtils.isEmpty(list)) {
                return list.get(0).getThemeCode();
            }
        }
        // Web端查询可用主题
        ProductThemeDO erpTheme = getOne(new LambdaQueryWrapper<ProductThemeDO>()
                .isNull(ProductThemeDO::getStoreGuid)
                .eq(ProductThemeDO::getTerminalCode, terminalCode));
        if (erpTheme == null) {
            return null;
        }
        return erpTheme.getThemeCode();
    }
}
