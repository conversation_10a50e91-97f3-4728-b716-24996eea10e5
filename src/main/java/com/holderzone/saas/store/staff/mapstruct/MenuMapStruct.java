package com.holderzone.saas.store.staff.mapstruct;

import com.holderzone.saas.store.dto.user.MenuDTO;
import com.holderzone.saas.store.dto.user.MenuSourceDTO;
import com.holderzone.saas.store.staff.entity.domain.MenuDO;
import com.holderzone.saas.store.staff.entity.domain.StoreSourceDO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MenuMapStruct
 * @date 19-1-15 下午2:40
 * @description 菜单相关domain转换
 * @program holder-saas-store-staff
 */
@Component
@Mapper(componentModel = "spring")
public interface MenuMapStruct {
    MenuDTO menuDO2DTO(MenuDO menuDO);

    List<MenuDTO> menuDOList2DTOList(List<MenuDO> menuDOList);

    MenuSourceDTO storeSourceDO2MenuSource(StoreSourceDO storeSourceDO);

    List<MenuSourceDTO> storeSourceDOList2MenuSource(List<StoreSourceDO> storeSourceDOList);
}
