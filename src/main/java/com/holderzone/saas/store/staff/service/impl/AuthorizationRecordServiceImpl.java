package com.holderzone.saas.store.staff.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.staff.entity.domain.AuthorizationRecordDO;
import com.holderzone.saas.store.staff.mapper.AuthorizationRecordMapper;
import com.holderzone.saas.store.staff.service.AuthorizationRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AuthorizationRecordServiceImpl extends ServiceImpl<AuthorizationRecordMapper, AuthorizationRecordDO>
        implements AuthorizationRecordService {
}
