package com.holderzone.saas.store.staff.config;

import com.holderzone.framework.dds.starter.db.config.DynamicDataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MybatisPlusConfig 动态数据源配置
 * @date 2018/12/28 上午10:39
 * @description DynamicDataSource替换Mybatis-plus中的DataSource配置
 * @program holder-saas-store-staff
 */
@Configuration
public class MyDataSourceConfig {

    @Bean
    public PlatformTransactionManager transactionManager(DynamicDataSource dynamicDataSource) {
        return new DataSourceTransactionManager(dynamicDataSource);
    }
}
