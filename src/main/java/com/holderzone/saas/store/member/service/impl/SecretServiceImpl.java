package com.holderzone.saas.store.member.service.impl;

import com.google.common.base.Joiner;
import com.holderzone.framework.response.LogicResponse;
import com.holderzone.saas.store.dto.member.MemberCheckDTO;
import com.holderzone.saas.store.member.entity.bo.SecretBO;
import com.holderzone.saas.store.member.entity.constant.RedisConstant;
import com.holderzone.saas.store.member.service.MemberService;
import com.holderzone.saas.store.member.service.SecretService;
import com.holderzone.saas.store.member.utils.RSAUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/8/22.
 */
@Service
public class SecretServiceImpl implements SecretService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private MemberService memberService;

    public static final String RAND_KEY = Joiner.on(RedisConstant.SEPARATOR).skipNulls().join(RedisConstant.MEMBER_HEAD, "rand");

    @Override
    public SecretBO getPublicKeyAndRand() {
        SecretBO secretBO = new SecretBO();
        // 获取公钥
        secretBO.setPublicKey(RSAUtils.getBase64PublicKey());

        // 生成随机值
        String rand = RandomStringUtils.randomAlphabetic(6);
        secretBO.setRand(rand);

        // 将生成的随机值存到Redis中,并设置失效时间
        stringRedisTemplate.opsForValue().set(RAND_KEY, rand, 60, TimeUnit.SECONDS);
        return secretBO;
    }

    @Override
    public LogicResponse<String> check(MemberCheckDTO memberCheckDTO) {
        // 取得密文
        String password = memberCheckDTO.getPassword();

        // 解密
        String plaintext = RSAUtils.decrypt(password);

        String[] arr = plaintext.split("\\|");

        // 校验随机值
        String rand = arr[1];
        String randInRedis = stringRedisTemplate.opsForValue().get(RAND_KEY);
        //随机值失效
        stringRedisTemplate.delete(RAND_KEY);

        if (!rand.equals(randInRedis)) {
            return LogicResponse.failure().body("非法的请求");
        }

        // 校验密码
        String passwd = arr[0];

        // 实际中根据用户名从数据库中查询出密码
        String realPasswd = memberService.getPasswordByPhone(memberCheckDTO.getPhone());
        if (!realPasswd.equals(passwd)) {
            return LogicResponse.failure().body("密码输入错误");
        }

        return LogicResponse.success("校验通过");
    }

    @Override
    public String getPassWord(String secretPassword) {
        String password = RSAUtils.decrypt(secretPassword);
        if (StringUtils.isNotBlank(password)) {
            String[] arr = password.split("\\|");
            // 校验随机值
            String rand = arr[1];
            String randInRedis = stringRedisTemplate.opsForValue().get(RAND_KEY);
            //随机值失效
            stringRedisTemplate.delete(RAND_KEY);

            if (!rand.equals(randInRedis)) {
                return null;
            }

            // 校验密码
            password = arr[0];
        }
        return password;
    }
}
