package com.holderzone.saas.store.member.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.terminal.dto.card.CardRechargeCashInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.card.ResponseCardRechargeRule;
import com.holderzone.saas.store.member.entity.member.RechargeReqDTO;
import com.holderzone.saas.store.member.entity.member.RechargeRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EntServiceClient
 * @date 2018/10/30 18:45
 * @description //TODO
 * @program holder-saas-store-member
 */
@Component
@FeignClient(name = "holder-saas-member-terminal", fallbackFactory = HsmServiceClient.FallBack.class)
public interface HsmServiceClient {

    /**
     * 查询充值打印信息
     *
     * @param orderNumber orderNumber
     * @return page ResponseMemberConsumption
     */
    @ApiOperation("查询充值打印信息")
    @GetMapping("/hsmca/card/getMemberCardRechargeInfo")
    CardRechargeCashInfoDTO getMemberCardRechargeInfo(@RequestParam(value = "orderNumber") String orderNumber);

    /**
     * 给某卡充值
     *
     * @param memberInfoCardGuid
     * @param rechargeReqDTO
     * @return
     */
    @PostMapping("/hsmca/card/{memberInfoCardGuid}/recharge")
    RechargeRespDTO cardCharge(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid,
                               @RequestBody RechargeReqDTO rechargeReqDTO);

    @GetMapping("/hsmca/card/cancelCharge")
    Integer cancelCharge(@RequestParam String payGuid, @RequestParam String orderGuid);

    /**
     * 通过持卡人guid查询当前卡等级对应的在某门店充值规则
     *
     * @param memberInfoCardGuid 持卡人guid
     * @return 充值规则
     */
    @ApiOperation(value = "通过持卡人guid查询当前卡等级对应的在某门店充值规则", notes = "通过持卡人guid查询当前卡等级对应的在某门店充值规则")
    @GetMapping(value = "/hsmca/card/cardRechargeRule", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    ResponseCardRechargeRule memberCardRechargeRule(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid);


    @Component
    class FallBack implements FallbackFactory<HsmServiceClient> {
        private static final Logger logger = LoggerFactory.getLogger(HsmServiceClient.FallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public HsmServiceClient create(Throwable throwable) {
            return new HsmServiceClient() {
                @Override
                public CardRechargeCashInfoDTO getMemberCardRechargeInfo(String orderNumber) {
                    logger.info("方法：getMemberCardRechargeInfo， 调用会员服务失败, 请求入参，orderNumber:{}, rechargeReqDTO:{}"
                            , orderNumber, throwable);
                    throw new BusinessException("123");
                }

                @Override
                public RechargeRespDTO cardCharge(String memberInfoCardGuid, RechargeReqDTO rechargeReqDTO) {
                    logger.info("方法：cardCharge， 调用会员服务失败, 请求入参，memberInfoCardGuid:{}, rechargeReqDTO:{}， throwable :{}"
                            , memberInfoCardGuid, JacksonUtils.writeValueAsString(rechargeReqDTO), throwable);
                    throw new ServerException();
                }

                @Override
                public Integer cancelCharge(String payGuid, String orderGuid) {
                    logger.info("方法：cancelCharge， 调用会员服务失败, 请求入参，payGuid:{}, orderGuid:{}， throwable :{}"
                            , payGuid, orderGuid, throwable);
                    throw new BusinessException("123");
                }

                @Override
                public ResponseCardRechargeRule memberCardRechargeRule(String memberInfoCardGuid) {
                    logger.error(HYSTRIX_PATTERN, "memberCardRechargeRule", memberInfoCardGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
