package com.holderzone.saas.store.member.config;


import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WebConfig
 * @date 2018-07-26 09:51:44
 * @description
 * @program holder-saas-store-order
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

    }

}
