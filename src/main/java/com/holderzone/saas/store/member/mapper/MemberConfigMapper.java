package com.holderzone.saas.store.member.mapper;

import com.holderzone.saas.store.member.domain.MemberConfigDO;
import org.springframework.stereotype.Repository;

@Repository
public interface MemberConfigMapper {
    int deleteByPrimaryKey(String memberConfigGuid);

    int insert(MemberConfigDO record);

    int insertSelective(MemberConfigDO record);

    MemberConfigDO selectByPrimaryKey(String memberConfigGuid);

    int updateByPrimaryKeySelective(MemberConfigDO record);

    int updateByPrimaryKey(MemberConfigDO record);

    MemberConfigDO selectByConfigKey(int code);
}