package com.holderzone.saas.store.member.entity.bo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseMemberBO
 * @date 2018/08/25 下午3:21
 * @description //TODO
 * @program holder-saas-config-center
 */
public class BaseMemberBO {

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 业务主键
     */
    private String memberGuid;

    /**
     * 名字
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 性别 0：女，1:男，2：保密
     */
    private Byte sex;

    /**
     * 会员账号
     */
    private String accountNumber;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 会员等级名称
     */
    private String memberGradeName;

    /**
     * 会员电子卡号
     */
    private String cardNum;

    /**
     * 剩余积分
     */
    private Integer residualIntegral;

    /**
     * 账户余额
     */
    private BigDecimal balance;

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Byte getSex() {
        return sex;
    }

    public void setSex(Byte sex) {
        this.sex = sex;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public LocalDate getBirthday() {
        return birthday;
    }

    public void setBirthday(LocalDate birthday) {
        this.birthday = birthday;
    }

    public String getMemberGradeName() {
        return memberGradeName;
    }

    public void setMemberGradeName(String memberGradeName) {
        this.memberGradeName = memberGradeName;
    }

    public String getCardNum() {
        return cardNum;
    }

    public void setCardNum(String cardNum) {
        this.cardNum = cardNum;
    }

    public Integer getResidualIntegral() {
        return residualIntegral;
    }

    public void setResidualIntegral(Integer residualIntegral) {
        this.residualIntegral = residualIntegral;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }
}
