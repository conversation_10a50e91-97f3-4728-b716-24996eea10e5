package com.holderzone.saas.store.member.controller;

import com.holderzone.framework.exception.ParamException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.member.grade.AccountValidityDTO;
import com.holderzone.saas.store.dto.member.grade.MemberGradeDTO;
import com.holderzone.saas.store.member.service.MemberGradeService;
import com.holderzone.saas.store.member.utils.ValidateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/8/21.
 */
@RestController
@RequestMapping("/grade")
@Api(description = "会员等级接口")
public class MemberGradeController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(MemberGradeController.class);

    @Autowired
    private MemberGradeService memberGradeService;

    @ApiOperation(value = "账号有效期设置", notes = "账号有效期设置")
    @PostMapping(value = "/update_account_validity", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean updateAccountValidity(@RequestBody AccountValidityDTO accountValidityDTO) throws ParamException {
        if (logger.isInfoEnabled()) {
            logger.info("接口路径:/grade/update_account_validity,账号有效期调整，accountValidityDTO={}", JacksonUtils.writeValueAsString(accountValidityDTO));
        }
        ValidateUtils.accountValidityValidate(accountValidityDTO);
        return memberGradeService.updateAccountValidity(accountValidityDTO);
    }

    @ApiOperation(value = "查询当前账号有效期", notes = "查询当前账号有效期")
    @RequestMapping(value = "/get_account_validity", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public AccountValidityDTO getAccountValidity() throws ParamException {
        return memberGradeService.getAccountValidity();
    }

    @ApiOperation(value = "增加会员等级", notes = "增加会员等级")
    @PostMapping(value = "/add_member_grade", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean addMemberGrade(@RequestBody @Validated MemberGradeDTO memberGradeDTO) throws ParamException {
        if (logger.isInfoEnabled()) {
            logger.info("接口路径:/grade/add_member_grade,增加会员等级，memberGradeDTO={}", JacksonUtils.writeValueAsString(memberGradeDTO));
        }
        ValidateUtils.memberGradeValidate(memberGradeDTO);
        return memberGradeService.addMemberGrade(memberGradeDTO);
    }

    @ApiOperation(value = "修改会员等级", notes = "修改会员等级")
    @PostMapping(value = "/update_member_grade", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean updateMemberGrade(@RequestBody @Validated MemberGradeDTO memberGradeDTO) throws ParamException {
        if (logger.isInfoEnabled()) {
            logger.info("接口路径:/grade/update_member_grade,修改会员等级，memberGradeDTO={}", JacksonUtils.writeValueAsString(memberGradeDTO));
        }
        ValidateUtils.memberGradeValidate(memberGradeDTO);
        return memberGradeService.updateMemberGrade(memberGradeDTO);
    }

    @ApiOperation(value = "会员等级列表", notes = "会员等级列表")
    @PostMapping(value = "/member_grade_list", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<MemberGradeDTO> memberGradeList(BaseDTO baseDTO) throws ParamException {
        logger.info("获取会员等级级积分规则");
        return memberGradeService.memberGradeList();
    }

    @ApiOperation(value = "会员等级详情", notes = "会员等级详情")
    @PostMapping(value = "/member_grade_detail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public MemberGradeDTO memberGradeDetail(@RequestBody MemberGradeDTO memberGradeDTO) throws ParamException {
        if (logger.isInfoEnabled()) {
            logger.info("接口路径:/grade/member_grade_detail,会员等级详情，memberGradeDTO={}", JacksonUtils.writeValueAsString(memberGradeDTO));
        }
        return memberGradeService.memberGradeDetail(memberGradeDTO.getMemberGradeGuid());
    }

    @ApiOperation(value = "删除等级", notes = "删除等级")
    @PostMapping(value = "/delete_member_grade", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean deleteMemberGrade(@RequestBody MemberGradeDTO memberGradeDTO) throws ParamException {
        if (logger.isInfoEnabled()) {
            logger.info("接口路径:/grade/delete_member_grade,删除等级，memberGradeDTO={}", JacksonUtils.writeValueAsString(memberGradeDTO));
        }
        return memberGradeService.deleteMemberGrade(memberGradeDTO);
    }
}
