package com.holderzone.saas.store.member.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberConsumeRuleReadDO
 * @date 2018/12/14 17:28
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class MemberConsumeRuleReadDO {
    @ApiModelProperty(value = "是否开启积分抵现 0：关闭，1:开启")
    private Byte isOpen;

    /**
     * 消费积分单位
     */
    @ApiModelProperty(value = "消费积分单位")
    private Integer consumeIntegralUnit;

    /**
     * 单笔订单最大消费积分
     */
    @ApiModelProperty(value = "单笔订单最大消费积分")
    private Integer consumeIntegralMax;

    /**
     * 消费积分金额单位
     */
    @ApiModelProperty(value = "消费积分金额单位")
    private BigDecimal consumeFeeUnit;

    /**
     * 消费积分的最小金额
     */
    @ApiModelProperty(value = "消费积分的最小金额")
    private BigDecimal consumeFeeMin;

    /**
     * 会员权益折扣
     */
    @ApiModelProperty(value = "会员权益折扣")
    private BigDecimal memberDiscount;
}
