package com.holderzone.saas.store.member.mapper;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.member.TransactionRecordDTO;
import com.holderzone.saas.store.dto.member.request.HandoverReqDTO;
import com.holderzone.saas.store.dto.member.request.MemberPayRecordReqDTO;
import com.holderzone.saas.store.dto.member.response.MemberPayRecordRespDTO;
import com.holderzone.saas.store.dto.member.response.TransactionRecordRespDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO;
import com.holderzone.saas.store.dto.order.response.daily.MemberConsumeRespDTO;
import com.holderzone.saas.store.member.domain.TransactionRecordDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import javax.validation.Valid;
import java.util.List;

@Repository
public interface TransactionRecordMapper {
    int deleteByPrimaryKey(String transactionRecordGuid);

    int insert(TransactionRecordDO record);

    int insertSelective(TransactionRecordDO record);

    TransactionRecordDO selectByPrimaryKey(String transactionRecordGuid);

    int updateByPrimaryKeySelective(TransactionRecordDO record);

    int updateByPrimaryKey(TransactionRecordDO record);

    List<TransactionRecordDO> selectByMemberGuid(Page<TransactionRecordRespDTO> result, @Param
            ("transactionRecordDTO") TransactionRecordDTO transactionRecordDTO);

    List<TransactionRecordDO> selectMemberTransactions(Page<MemberPayRecordRespDTO> page, @Param("memberPayRecordReqDTO")MemberPayRecordReqDTO memberPayRecordReqDTO);

    List<TransactionRecordDO> selectBySequenceNo(String sequenceNo);

    MemberConsumeRespDTO getMemberRechargeRecord(@Param("dto")  DailyReqDTO request);

    MemberConsumeRespDTO getMemberRechargeStore(@Param("dto") HandoverReqDTO request);

    List<GatherRespDTO> getBusinessMemberRechargeEarnings(@Param("dto")  DailyReqDTO request);
    List<GatherRespDTO> getBusinessMemberRechargeEarningsHandover(@Param("dto")  HandoverReqDTO request);
}