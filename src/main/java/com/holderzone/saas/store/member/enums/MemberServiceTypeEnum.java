package com.holderzone.saas.store.member.enums;

/**
 * 会员服务类型枚举
 * 
 * <AUTHOR>
 * @date 2025/8/8
 */
public enum MemberServiceTypeEnum {
    
    /**
     * 会员中台服务
     */
    MEMBER_CENTER("MEMBER_CENTER", "会员中台服务"),
    
    /**
     * 阿里会员服务
     */
    ALI_MEMBER("ALI_MEMBER", "阿里会员服务");
    
    private final String code;
    private final String description;
    
    MemberServiceTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static MemberServiceTypeEnum getByCode(String code) {
        for (MemberServiceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return MEMBER_CENTER; // 默认返回会员中台服务
    }
}
