package com.holderzone.saas.store.member.mapper;

import com.holderzone.saas.store.dto.member.request.MemberCardsReqDTO;
import com.holderzone.saas.store.member.domain.MemberCardDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MemberCardMapper {
    int deleteByPrimaryKey(String memberCardGuid);

    int insert(MemberCardDO record);

    int insertSelective(MemberCardDO record);

    MemberCardDO selectByPrimaryKey(String memberCardGuid);

    int updateByPrimaryKeySelective(MemberCardDO record);

    int updateByPrimaryKey(MemberCardDO record);

    String getElectronCardNum(String memberGuid);

    List<MemberCardDO> getMemberCards(@Param("memberCardsReqDTO") MemberCardsReqDTO memberCardsReqDTO);

    MemberCardDO getMemberEleCard(String memberGuid);
}