package com.holderzone.saas.store.member.utils;

import com.holderzone.framework.exception.ParamException;
import com.holderzone.framework.validate.Validator;
import com.holderzone.saas.store.dto.member.MemberDTO;
import com.holderzone.saas.store.dto.member.MemberLoginDTO;
import com.holderzone.saas.store.dto.member.MemberNotifyDTO;
import com.holderzone.saas.store.dto.member.TransactionRecordDTO;
import com.holderzone.saas.store.dto.member.grade.AccountValidityDTO;
import com.holderzone.saas.store.dto.member.grade.MemberGradeDTO;

/**
 * Created by zhaohongyang on 2018/8/21.
 */
public class ValidateUtils {

    /**
     * 验证创建会员参数
     *
     * @param memberDTO
     */
    public static void createMemberValidate(MemberDTO memberDTO) throws ParamException {
        Validator validator = Validator.create().notBlank(memberDTO.getPhone(), "手机号")
                .notBlank(memberDTO.getPassword(), "会员密码");
        if (!validator.isValid()) {
            throw new ParamException(validator.getMessage());
        }
    }

    /**
     * 验证修改会员参数
     *
     * @param memberDTO
     */
    public static void updateMemberValidate(MemberDTO memberDTO) throws ParamException {
        Validator validator = Validator.create().notBlank(memberDTO.getPhone(), "手机号");
        if (!validator.isValid()) {
            throw new ParamException(validator.getMessage());
        }
    }


    /**
     * 验证交易记录参数
     *
     * @param transactionRecordDTO
     */
    public static void transactionRecordValidate(TransactionRecordDTO transactionRecordDTO) throws ParamException {
        Validator validator = Validator.create().notBlank(transactionRecordDTO.getPhone(), "手机号").notNull
                (transactionRecordDTO.getType(), "交易类型");
        if (!validator.isValid()) {
            throw new ParamException(validator.getMessage());
        }
    }

    public static void createMemberNotifyValidate(MemberNotifyDTO memberNotifyDTO) throws ParamException {
        Validator validator = Validator.create().notNull(memberNotifyDTO.getType(), "交易类型").notNull(memberNotifyDTO
                .getAmount(), "交易金额");
        if (!validator.isValid()) {
            throw new ParamException(validator.getMessage());
        }
    }

    public static void createMemberLoginValidate(MemberLoginDTO memberLoginDTO) throws ParamException {
        Validator validator = Validator.create().notBlank(memberLoginDTO.getMemberGuid(), "memberGuid");
        if (!validator.isValid()) {
            throw new ParamException(validator.getMessage());
        }

    }

    public static void accountValidityValidate(AccountValidityDTO accountValidityDTO) throws ParamException {
        Validator validator = Validator.create().notNull(accountValidityDTO.getExpiryType(), "有效期类型");
        if (!validator.isValid()) {
            throw new ParamException(validator.getMessage());
        }
    }

    public static void memberGradeValidate(MemberGradeDTO memberGradeDTO) throws ParamException {
        Validator validator = Validator.create().notBlank(memberGradeDTO.getName(), "等级名称").notNull(memberGradeDTO
                .getNeedIntegral(), "成为该等级需要的积分").notNull(memberGradeDTO.getDiscount(), "权益折扣").notNull(memberGradeDTO.getIntegralRuleDTOS(), "积分规则");
        if (!validator.isValid()) {
            throw new ParamException(validator.getMessage());
        }
    }
}
