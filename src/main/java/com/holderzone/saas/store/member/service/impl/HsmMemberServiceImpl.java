package com.holderzone.saas.store.member.service.impl;

import cn.hutool.core.date.DateUtil;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.base.dto.message.MessageType;
import com.holderzone.framework.base.dto.message.ShortMessageDTO;
import com.holderzone.framework.base.dto.message.ShortMessageType;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.common.enums.OrderSourceEnum;
import com.holderzone.holder.saas.member.terminal.dto.card.CardRechargeCashInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.card.ResponseCardRechargeRule;
import com.holderzone.resource.common.dto.enterprise.DeductShortMessageDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.MessageConfigDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmAggPayRespDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.print.content.PrintStoredCashDTO;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import com.holderzone.saas.store.member.entity.member.RechargeReqDTO;
import com.holderzone.saas.store.member.entity.member.RechargeRespDTO;
import com.holderzone.saas.store.member.mapstruct.HsmMemberMapStruct;
import com.holderzone.saas.store.member.service.HsmMemberService;
import com.holderzone.saas.store.member.service.rpc.*;
import com.holderzone.saas.store.util.BigDecimalUtil;
import com.netflix.hystrix.exception.HystrixBadRequestException;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import feign.FeignException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;

@Slf4j
@Service
public class HsmMemberServiceImpl implements HsmMemberService {

    private final HsmMemberMapStruct hsmMemberMapStruct;

    private final SaasAggPayClient saasAggPayClient;

    private final HsmServiceClient hsmServiceClient;

    private final MemberTransactionPrintService memberTransactionPrintService;

    private final MemberStoreService memberStoreService;

    private final EntServiceClient entServiceClient;

    private final MsgClientService msgClientService;

    @Value("${wxStorePay.appId}")
    private String appId;

    @Value("${wxStorePay.mchntName}")
    private String mchntName;

    @Value("${wxStorePay.appSecret}")
    private String appSecret;

    @Autowired
    public HsmMemberServiceImpl(HsmMemberMapStruct hsmMemberMapStruct, SaasAggPayClient saasAggPayClient, HsmServiceClient hsmServiceClient, MemberTransactionPrintService memberTransactionPrintService, MemberStoreService memberStoreService, EntServiceClient entServiceClient, MsgClientService msgClientService) {
        this.hsmMemberMapStruct = hsmMemberMapStruct;
        this.saasAggPayClient = saasAggPayClient;
        this.hsmServiceClient = hsmServiceClient;
        this.memberTransactionPrintService = memberTransactionPrintService;
        this.memberStoreService = memberStoreService;
        this.entServiceClient = entServiceClient;
        this.msgClientService = msgClientService;
    }

    @Override
    public HsmAggPayRespDTO recharge(HsmRechargeReqDTO hsmRechargeReqDTO) {
        checkParm(hsmRechargeReqDTO);
        HsmAggPayRespDTO hsmAggPayRespDTO = new HsmAggPayRespDTO();
        RechargeReqDTO rechargeReqDTO = hsmMemberMapStruct.hsmRecharge2PlatformRecharge(hsmRechargeReqDTO);
        StoreDTO storeDTO = memberStoreService.queryStoreByGuid(hsmRechargeReqDTO.getStoreGuid());
        rechargeReqDTO.setBrandGuid(storeDTO.getBelongBrandGuid());
        // 先用brandGuid代替brandName使用
        rechargeReqDTO.setBrandName(storeDTO.getBelongBrandGuid());
        rechargeReqDTO.setStoreAddress(storeDTO.getAddressDetail());
        rechargeReqDTO.setStoreTelephone(storeDTO.getContactTel());
        rechargeReqDTO.setPayName(hsmRechargeReqDTO.getPayName());
        rechargeReqDTO.setCanteenCard(hsmRechargeReqDTO.getCanteenCard());
        Integer payWay = hsmRechargeReqDTO.getPayWay();
        if (1 == payWay) {
            rechargeReqDTO.setPayChannel(1);
        } else {
            rechargeReqDTO.setPayChannel(0);
        }
        log.warn("会员充值，memberInfoCardGuid：{}", hsmRechargeReqDTO.getMemberInfoCardGuid());
        if (rechargeReqDTO.getPayWay() > 5) {
            rechargeReqDTO.setPayWay(5);
        }
        RechargeRespDTO rechargeRespDTO = hsmServiceClient.cardCharge(hsmRechargeReqDTO.getMemberInfoCardGuid(), rechargeReqDTO);
        String payGuid = rechargeRespDTO.getPayGuid();
        String orderGuid = rechargeRespDTO.getOrderGuid();
        log.info("接收到的数据为:{}", hsmRechargeReqDTO);
        if (rechargeRespDTO.getArrival() != null) {
            String printResult = print(hsmRechargeReqDTO, rechargeRespDTO);
            log.info("打印printResult:{}", printResult);
            hsmAggPayRespDTO.setResult(Boolean.TRUE);
            log.info("第一次支付返回数据:{}", hsmRechargeReqDTO);
            return hsmAggPayRespDTO;
        }
        AggPayRespDTO payRespDTO = aggPay(hsmRechargeReqDTO, payGuid, orderGuid);
        if (!"10000".equalsIgnoreCase(payRespDTO.getCode())) {
            throw new ParameterException(payRespDTO.getMsg());
        }
        log.info("第二次支付返回数据:{}", payRespDTO);
        hsmAggPayRespDTO.setPayGuid(payGuid);
        hsmAggPayRespDTO.setOrderGuid(orderGuid);
        hsmAggPayRespDTO.setResult(Boolean.TRUE);
        return hsmAggPayRespDTO;
    }

    private void checkParm(HsmRechargeReqDTO hsmRechargeReqDTO) {
        ResponseCardRechargeRule rechargeRule = hsmServiceClient.memberCardRechargeRule(hsmRechargeReqDTO.getMemberInfoCardGuid());
        if (Objects.nonNull(rechargeRule) && Objects.nonNull(rechargeRule.getRechargeUpperLimit())
                && BigDecimalUtil.greaterThan(hsmRechargeReqDTO.getRechargeMoney(), rechargeRule.getRechargeUpperLimit())) {
            throw new BusinessException(String.format("单笔充值上限%s元", rechargeRule.getRechargeUpperLimit()));
        }
    }

    @Override
    public WxPayRespDTO wechatRecharge(HsmRechargeReqDTO hsmRechargeReqDTO) {
        log.info("微信会员充值，当前请求头信息：{}", UserContextUtils.getJsonStr());
        UserContext userContext = UserContextUtils.get();
        if (userContext == null || !StringUtils.hasText(userContext.getAllianceId())) {
            throw new BusinessException("当前线程没有用户信息");
        }
        RechargeReqDTO rechargeReqDTO = hsmMemberMapStruct.hsmRecharge2PlatformRecharge(hsmRechargeReqDTO);
        StoreDTO storeDTO = memberStoreService.queryStoreByGuid(hsmRechargeReqDTO.getStoreGuid());
        if (storeDTO == null) {
            throw new BusinessException("门店" + hsmRechargeReqDTO.getStoreGuid() + "]不存在");
        }
        rechargeReqDTO.setBrandGuid(storeDTO.getBelongBrandGuid());
        rechargeReqDTO.setEnterpriseName(findEnterprise(rechargeReqDTO.getEnterpriseGuid()).getName());
        // 先用brandGuid代替brandName使用
        rechargeReqDTO.setBrandName(storeDTO.getBelongBrandGuid());
        rechargeReqDTO.setStoreAddress(storeDTO.getAddressDetail());
        rechargeReqDTO.setStoreTelephone(storeDTO.getContactTel());
        rechargeReqDTO.setPayWay(5);
        rechargeReqDTO.setPayChannel(2);
        rechargeReqDTO.setAllianceId(userContext.getAllianceId());
        hsmRechargeReqDTO.setEnterpriseName(rechargeReqDTO.getEnterpriseName());
        log.info("会员充值调用会员服务请求入参：{}", JacksonUtils.writeValueAsString(rechargeReqDTO));
        RechargeRespDTO rechargeRespDTO = hsmServiceClient.cardCharge(hsmRechargeReqDTO.getMemberInfoCardGuid(), rechargeReqDTO);
        String payGuid = rechargeRespDTO.getPayGuid();
        String orderGuid = rechargeRespDTO.getOrderGuid();
        return wchatPay(hsmRechargeReqDTO, payGuid, orderGuid);
    }

    @Override
    public String callback(SaasNotifyDTO saasNotifyDTO) {
        AggPayPollingRespDTO aggPayPollingRespDTO = saasNotifyDTO.getAggPayPollingRespDTO();
        log.info("会员充值支付回调 ，请求入参：{}", JacksonUtils.writeValueAsString(aggPayPollingRespDTO));
        // erpGuid, storeGuid, deviceType, deviceId可用
        BaseInfo baseInfo = saasNotifyDTO.getBaseInfo();
        // todo 回调成功时 是否需要判断该订单是否处于撤销状态或已撤销，
        if ("10000".equals(aggPayPollingRespDTO.getCode())) {
            if (aggPayPollingRespDTO.getPaySt().equals("2")) {
                log.info("支付成功，payGuid={}", aggPayPollingRespDTO.getPayGUID());
                RechargeReqDTO rechargeReqDTO = new RechargeReqDTO();
                String payPowerId = aggPayPollingRespDTO.getPayPowerId();
                log.info("payPowerId: {}", payPowerId);
//                rechargeReqDTO.setPayWay(getPayWay(payPowerId));
                rechargeReqDTO.setPayWay(1);
                rechargeReqDTO.setPayName("聚合支付");
                rechargeReqDTO.setEnterpriseName(baseInfo.getEnterpriseName());
                rechargeReqDTO.setEnterpriseGuid(baseInfo.getEnterpriseGuid());
                rechargeReqDTO.setStoreGuid(baseInfo.getStoreGuid());
                rechargeReqDTO.setStoreName(baseInfo.getStoreName());
                rechargeReqDTO.setPayGuid(aggPayPollingRespDTO.getPayGUID());
                rechargeReqDTO.setOrderGuid(aggPayPollingRespDTO.getOrderGUID());
                rechargeReqDTO.setBankTransactionId(aggPayPollingRespDTO.getBankTransactionId());
                //fixme 后面透传到PAY服务，免得写死有影响
                rechargeReqDTO.setAllianceId("1fb529b8da78459ca64187f94dc3ae3e");
                rechargeReqDTO.setOrderSource(getOrderSourceCodeByType(baseInfo.getDeviceType()));
                log.info("支付回调 ，调用会员充值请求入参：{}", JacksonUtils.writeValueAsString(rechargeReqDTO));
                RechargeRespDTO rechargeRespDTO = hsmServiceClient.cardCharge("123", rechargeReqDTO);
                if (PayPowerId.BEST_BAR_CODE.getId().equals(payPowerId) || PayPowerId.BEST_QR_CODE.getId().equals(payPowerId)) {
                    rechargeRespDTO.setPayWay(PayPowerId.BEST_BAR_CODE.getName());
                }
                if (rechargeRespDTO.getPayChannel() == null || 2 != rechargeRespDTO.getPayChannel()) {
                    boolean isRepeatCallback = Optional.ofNullable(rechargeRespDTO.getIsRepeatCallback()).orElse(false);
                    if (!isRepeatCallback) {
                        print(baseInfo, rechargeRespDTO);
                    }
                }
            } else {
                log.error("支付失败：paySt={}，msg={}", aggPayPollingRespDTO.getPaySt(), aggPayPollingRespDTO.getMsg());
            }
        } else {
            log.error("支付失败：code={}，msg={}", aggPayPollingRespDTO.getCode(), aggPayPollingRespDTO.getMsg());
        }
        return "SUCCESS";
    }

    private int getOrderSourceCodeByType(Integer type) {
        if (type == null) {
            return OrderSourceEnum.UNDEFINED.getCode();
        }
        switch (BaseDeviceTypeEnum.getDeviceTypeByCode(type)) {
            case WECHAT_UNUSED:
            case WECHAT:
                return OrderSourceEnum.SOURCE_WECHAT.getCode();
            case All_IN_ONE:
                return OrderSourceEnum.SOURCE_ONE_MACHINE.getCode();
            case POS:
            case M1:
            case PV1:
                return OrderSourceEnum.SOURCE_POS.getCode();
            case WECHAT_MINI:
            case TCD:
                return OrderSourceEnum.SOURCE_ZHUANCAN.getCode();
            case ALI:
                return OrderSourceEnum.SOURCE_ALI.getCode();
            case CLOUD_PANEL:
                return OrderSourceEnum.SOURCE_PAD.getCode();
            default:
                return OrderSourceEnum.SOURCE_BACKGROUND.getCode();
        }
    }

    @Override
    public Integer revokeCharge(SaasPollingDTO saasPollingDTO) {

//        return null;
        return hsmServiceClient.cancelCharge(saasPollingDTO.getPayGuid(), saasPollingDTO.getOrderGuid());
    }

    @Override
    public Boolean printRecharge(CardRechargeCashInfoQO cardRechargeCashInfoQO) {
        //获取会员打印信息
        CardRechargeCashInfoDTO cardRechargeCashInfoDTO = hsmServiceClient.getMemberCardRechargeInfo(cardRechargeCashInfoQO.getOrderNumber());
        if (Objects.isNull(cardRechargeCashInfoDTO) || StringUtils.isEmpty(cardRechargeCashInfoDTO.getCardNo())) {
            log.info("获取会员充值信息失败，订单号：{}", cardRechargeCashInfoQO.getOrderNumber());
            return false;
        }
        BaseDTO baseDTO = new BaseDTO();
        BeanUtils.copyProperties(cardRechargeCashInfoQO, baseDTO);
        RechargeRespDTO rechargeRespDTO = new RechargeRespDTO();
        BeanUtils.copyProperties(cardRechargeCashInfoDTO, rechargeRespDTO);
        rechargeRespDTO.setOrderGuid(cardRechargeCashInfoDTO.getSerialNumber());
        String result = print(baseDTO, rechargeRespDTO);
        return "SUCCESS".equals(result);
    }

    private int getPayWay(String payPowerId) {
        int payWay;
        switch (payPowerId) {
            case "7":
            case "8":
            case "9":
            case "50":
            case "51":
                payWay = 1;
                break;
            case "1":
            case "2":
            case "3":
            case "31":
            case "32":
                payWay = 2;
                break;
            case "52":
                payWay = 5;
                break;
            default:
                payWay = 6;
                break;
        }
        return payWay;
    }

    private String print(BaseDTO baseDTO, RechargeRespDTO rechargeRespDTO) {
        log.info("rechargeRespDTO -----------------------  = {}", JacksonUtils.writeValueAsString(rechargeRespDTO));
        PrintStoredCashDTO printStoredCashDTO = new PrintStoredCashDTO();
        //交易流水号
        printStoredCashDTO.setSerialNumber(rechargeRespDTO.getSerialNumber());
        //充值金额
        printStoredCashDTO.setRecharge(rechargeRespDTO.getRecharge());
        //赠送金额
        printStoredCashDTO.setPresented(rechargeRespDTO.getPresented());
        //赠送积分
        // fixme 升级memberDto依赖，放开此处
        printStoredCashDTO.setPresentedIntegral(rechargeRespDTO.getPresentedIntegral());
        //到账金额
        printStoredCashDTO.setArrival(rechargeRespDTO.getArrival());
        //充值方式
        printStoredCashDTO.setPayWay(StringUtils.hasText(rechargeRespDTO.getPayName())
                ? rechargeRespDTO.getPayName() : rechargeRespDTO.getPayWay());
        //充值卡号
        printStoredCashDTO.setCardNo(rechargeRespDTO.getCardNo());
        //当前余额
        printStoredCashDTO.setCurrentCash(rechargeRespDTO.getCurrentCash());
        //当前积分
        printStoredCashDTO.setIntegration(rechargeRespDTO.getIntegration());
        //充值时间
        if (Objects.isNull(rechargeRespDTO.getRechargeTime())){
            printStoredCashDTO.setRechargeTime(DateTimeUtils.nowMillis());
        }else {
            printStoredCashDTO.setRechargeTime(rechargeRespDTO.getRechargeTime());
        }

        //企业信息
        printStoredCashDTO.setEnterpriseGuid(baseDTO.getEnterpriseGuid());
        //门店信息
        printStoredCashDTO.setStoreGuid(baseDTO.getStoreGuid());
        printStoredCashDTO.setStoreName(baseDTO.getStoreName());
        printStoredCashDTO.setStoreAddress(rechargeRespDTO.getStoreAddress());
        printStoredCashDTO.setTel(rechargeRespDTO.getTel());
        //单据类型
        printStoredCashDTO.setInvoiceType(10);
        //打印唯一标识
        printStoredCashDTO.setPrintUid(rechargeRespDTO.getOrderGuid());
        //操作人
        printStoredCashDTO.setOperatorStaffGuid(baseDTO.getUserGuid());
        printStoredCashDTO.setOperatorStaffName(baseDTO.getUserName());
        //打印时间
        printStoredCashDTO.setCreateTime(DateTimeUtils.nowMillis());
        //设备id
        printStoredCashDTO.setDeviceId(baseDTO.getDeviceId());
        //打印来源
        printStoredCashDTO.setPrintSourceEnum(PrintSourceEnum.getPrintSourceByDeviceType(baseDTO.getDeviceType()));
        //会员信息
        printStoredCashDTO.setMemberPhone(rechargeRespDTO.getMemberPhone());
        printStoredCashDTO.setMemberName(rechargeRespDTO.getMemberName());

        log.info("打印参数：{}", JacksonUtils.writeValueAsString(printStoredCashDTO));
        String transactionLog = memberTransactionPrintService.printMemberTransactionLog(printStoredCashDTO);
        log.info("打印结果：{}", transactionLog);
        return transactionLog;
    }

    private void sendMsg(BaseDTO baseDTO, RechargeRespDTO rechargeRespDTO) {
        String enterpriseGuid = baseDTO.getEnterpriseGuid();
        MessageConfigDTO messageInfo = entServiceClient.getMessageInfo(enterpriseGuid);
        // 会员充值
        if (messageInfo == null) {
            log.error("发送短信失败,未获取到该企业短信信息");
            return;
        }
        //
        if (messageInfo.getAfterCharge() == 0) {
            log.info("发送短信失败，未开启充值发送短信功能");
            return;
        }
        if (messageInfo.getResidueCount() < 1) {
            log.error("发送短信失败，商户剩余短信条数不足");
            return;
        }
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setMessageType(MessageType.SHORT_MESSAGE);
        ShortMessageDTO shortMessageDTO = new ShortMessageDTO();
        Map<String, String> parms = new HashMap<>(4);
        parms.put("Entertainment", rechargeRespDTO.getTel());
        parms.put("Date", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        parms.put("Money", rechargeRespDTO.getRecharge().toString());
        parms.put("Banlace", rechargeRespDTO.getCurrentCash().toString());
        shortMessageDTO.setParams(parms);
        shortMessageDTO.setShortMessageType(ShortMessageType.NEW_MEMBER_REPAID);
        shortMessageDTO.setPhoneNumber(rechargeRespDTO.getTel());
        shortMessageDTO.setContent(parms.get("Entertainment") + "温馨提示:" + "尊敬的会员，您的账户" + parms.get("Name") + "已成功充值" + parms
                .get("Money") + "元," + "当前余额" + parms.get("Banlace") + "元。");
        messageDTO.setShortMessage(shortMessageDTO);
        //发送短信
        log.info("MemberTransactionImpl send message before request: entry {} " + JacksonUtils.writeValueAsString(shortMessageDTO));
        msgClientService.sendMessage(messageDTO);
        List<DeductShortMessageDTO> deductShortMessageDTOS = new ArrayList<>();
        DeductShortMessageDTO deductShortMessageDTO = new DeductShortMessageDTO();
        deductShortMessageDTO.setEnterpriseGuid(enterpriseGuid);
        deductShortMessageDTO.setDeductCount(1);
        deductShortMessageDTOS.add(deductShortMessageDTO);
        entServiceClient.deductShortMessage(deductShortMessageDTOS);
    }

    private AggPayRespDTO aggPay(HsmRechargeReqDTO hsmRechargeReqDTO, String payGuid, String orderGuid) {
        SaasAggPayDTO aggPayDTO = hsmMemberMapStruct.billPayReqDTO2SaasAggPayDTO(hsmRechargeReqDTO);
        AggPayPreTradingReqDTO payPreTradingReqDTO = new AggPayPreTradingReqDTO();
        payPreTradingReqDTO.setTimestamp(DateTimeUtils.nowMillis());
        payPreTradingReqDTO.setStoreName(hsmRechargeReqDTO.getStoreName());
        payPreTradingReqDTO.setAmount(hsmRechargeReqDTO.getRechargeMoney());
        payPreTradingReqDTO.setAuthCode(hsmRechargeReqDTO.getPayCode());
        payPreTradingReqDTO.setTerminalId(hsmRechargeReqDTO.getDeviceId());
        payPreTradingReqDTO.setGoodsName("会员充值");
        // 聚合支付用分为单位
        payPreTradingReqDTO.setPayGUID(String.valueOf(payGuid));
        payPreTradingReqDTO.setOrderGUID(orderGuid);
        payPreTradingReqDTO.setBody("聚合支付订单：" + orderGuid);
        payPreTradingReqDTO.setAttachData("1");
        aggPayDTO.setReqDTO(payPreTradingReqDTO);
        aggPayDTO.setSaasCallBackUrl("http://holder-saas-store-member/hsm_member/callback");
        return saasAggPayClient.pay(aggPayDTO);
    }

    private WxPayRespDTO wchatPay(HsmRechargeReqDTO hsmRechargeReqDTO, String payGuid, String orderGuid) {
        SaasAggWeChatPublicAccountPayDTO saasAggWeChatPublicAccountPay = hsmMemberMapStruct.billPayReqDTO2SaasAggPayDTO2(hsmRechargeReqDTO);
        saasAggWeChatPublicAccountPay.setDeviceType(12);
        saasAggWeChatPublicAccountPay.setSaasCallBackUrl("http://holder-saas-store-member/hsm_member/callback");
        AggWeChatPublicAccountPayDTO aggWeChatPublicAccountPayDTO = new AggWeChatPublicAccountPayDTO();
        //重定向到微信订单详情页面
//        aggWeChatPublicAccountPayDTO.setOutNotifyUrl(hsmRechargeReqDTO.getOutNotifyUrl());
        aggWeChatPublicAccountPayDTO.setRedirectUrl(hsmRechargeReqDTO.getOutNotifyUrl());
        //微信回调买单人的openid
        aggWeChatPublicAccountPayDTO.setAppId(appId);
        aggWeChatPublicAccountPayDTO.setMchntName(mchntName);
        aggWeChatPublicAccountPayDTO.setAppSecret(appSecret);
        aggWeChatPublicAccountPayDTO.setStoreName(hsmRechargeReqDTO.getStoreName());
        aggWeChatPublicAccountPayDTO.setGoodsName("会员充值");
        aggWeChatPublicAccountPayDTO.setBody("会员充值");
        aggWeChatPublicAccountPayDTO.setEnterpriseName(hsmRechargeReqDTO.getEnterpriseName());
        aggWeChatPublicAccountPayDTO.setAmount(hsmRechargeReqDTO.getRechargeMoney());
        aggWeChatPublicAccountPayDTO.setOrderGUID(orderGuid);
        aggWeChatPublicAccountPayDTO.setPayGUID(payGuid);
        saasAggWeChatPublicAccountPay.setPublicAccountPayDTO(aggWeChatPublicAccountPayDTO);
        WxPayRespDTO wxPayRespDTO = new WxPayRespDTO();
        try {
            log.info("微信公众号支付：{}", JacksonUtils.writeValueAsString(saasAggWeChatPublicAccountPay));
            String payUrl = saasAggPayClient.weChatPublic(saasAggWeChatPublicAccountPay);
            wxPayRespDTO.setPayUrl(payUrl);
            wxPayRespDTO.setCouldPay(1);
        } catch (Exception e) {
            e.printStackTrace();
            wxPayRespDTO.setCouldPay(0);
            String message = getAggPayErrorMsg(e);
            wxPayRespDTO.setErrorMsg(message);
        }
        return wxPayRespDTO;
    }

    private EnterpriseDTO findEnterprise(String enterpriseGuid) {
        BaseDTO baseDTO = new BaseDTO();
        baseDTO.setEnterpriseGuid(enterpriseGuid);
        return entServiceClient.findEnterprise(baseDTO);
    }

    /**
     * 特定于判断聚合支付错误时是否是门店未配置的问题导致的
     *
     * @param e
     * @return
     */
    private String getAggPayErrorMsg(Exception e) {
        String message;
        if (e instanceof HystrixRuntimeException || e instanceof HystrixBadRequestException) {
            log.info("会员充值的异常类型是HystrixRuntimeException");
            message = e.getCause().getMessage();
        } else if (e instanceof FeignException) {
            log.info("会员充值的异常类型是FeignException");
            message = e.getMessage();
        } else {
            log.info("无法识别到异常类型");
            return "聚合支付发生错误";
        }
        message = cutMsg(message);
        log.info("会员充值异常message：{}", message);
        Pattern pattern = Pattern.compile("[a-zA-z]");
        if (!pattern.matcher(message).find()) {
            return "该门店未配置收款账户，无法支付";
        }
        return "聚合支付发生错误";
    }

    private String cutMsg(String message) {
        if (StringUtils.isEmpty(message)) {
            return null;
        } else {
            String content = "; content:\n";
            int contentLength = content.length();
            int contentIndex = message.indexOf(content);
            String substring = message.substring(contentIndex + contentLength);
            log.info("substring:{}", substring);
            ErrorResult errorResult = JacksonUtils.toObject(ErrorResult.class, substring);
            log.info("errorResult:{}", errorResult);
            return errorResult.getMessage();
        }
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    static class ErrorResult {
        private String timestamp;

        private String path;

        private Integer status;

        private String error;

        private String message;
    }
}
