package com.holderzone.saas.store.member.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberCardReadDO
 * @date 2018/12/17 16:41
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class MemberCardReadDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    private Boolean isDelete;

    /**
     * 业务主键
     */
    private String memberGuid;

    /**
     * 门店GUID
     */
    private String storeGuid;

    /**
     * 名字
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 性别 0：女，1:男，2：保密
     */
    private Byte sex;

    /**
     * 会员账号
     */
    private String accountNumber;

    /**
     * 密码
     */
    private String password;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 会员有效期（开始日期）
     */
    private LocalDate startDate;

    /**
     * 会员有效期（截止日期）
     */
    private LocalDate expiryDate;

    /**
     * 会员有效期类型 0：永久有效，1:时间限制
     */
    private Byte expiryType;

    /**
     * 会员有效期配置key（MemberConfigTypeEnum）
     */
    private Byte expiryConfigKey;

    /**
     * 最近登录日期
     */
    private LocalDateTime recentlyLoginDate;

    /**
     * 最近消费日期
     */
    private LocalDateTime recentlyConsumeDate;

    /**
     * 会员等级的业务主键
     */
    private String memberGradeGuid;

    /**
     * 累计消费次数
     */
    private Integer totalConsumeNum;

    /**
     * 累计充值次数
     */
    private Integer totalPrepaidNum;

    /**
     * 累计积分
     */
    private Integer totalIntegral;

    /**
     * 剩余积分
     */
    private Integer residualIntegral;

    /**
     * 累计消费金额（全部消费）
     */
    private BigDecimal totalConsumeFee;

    /**
     * 累计支付金额（余额支付）
     */
    private BigDecimal totalPayFee;

    /**
     * 累计充值金额
     */
    private BigDecimal totalPrepaidFee;

    /**
     * 累计赠送金额
     */
    private BigDecimal totalPresentFee;

    /**
     * 账户余额
     */
    private BigDecimal balance;

    /**
     * 操作人guid
     */
    private String staffGuid;

    /**
     * 操作人姓名
     */
    private String staffName;

    private List<MemberCardDO> memberCardDOS;
}
