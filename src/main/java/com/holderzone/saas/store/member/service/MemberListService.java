package com.holderzone.saas.store.member.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import com.holderzone.saas.store.dto.member.request.MemberCardsReqDTO;
import com.holderzone.saas.store.dto.member.request.MemberIntegralReqDTO;
import com.holderzone.saas.store.dto.member.request.MemberListReqDTO;
import com.holderzone.saas.store.dto.member.request.MemberPayRecordReqDTO;
import com.holderzone.saas.store.dto.member.response.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberListService
 * @date 2018/09/30 14:50
 * @description //TODO
 * @program holder-saas-store-order
 */
public interface MemberListService {
    /**
     * 查询所有会员信息，分页
     * @param memberListReqDTO
     * @return
     */
    Page<MemberListRespDTO> findMemberList(MemberListReqDTO memberListReqDTO);

    /**
     * 根据会员guid获取会员详情
     * @param baseMemberDTO
     * @return
     */
    MemberDetailRespDTO getMemberDetailByGuid(BaseMemberDTO baseMemberDTO);

    /**
     * 查询满足条件的会员卡
     * @param memberCardsReqDTO
     * @return
     */
    List<MemberCardsRespDTO> getMemberCards(MemberCardsReqDTO memberCardsReqDTO);

    /**
     * 查询所有会员等级
      * @param baseDTO
     * @return
     */
    List<MemberGradeListDTO> memberGradeList(BaseDTO baseDTO);

    /**
     * 查询会员交易记录
     * @param memberPayRecordReqDTO
     * @return
     */
    Page<MemberPayRecordRespDTO> getTransactionRecord(MemberPayRecordReqDTO memberPayRecordReqDTO);

    /**
     * 查询会员积分类型
     * @param baseDTO
     * @return
     */
    List<MemberIntegralTypeRespDTO> getMemberIntegralType(BaseDTO baseDTO);

    /**
     * 查询会员积分记录
     * @param memberListReqDTO
     * @return
     */
    Page<MemberIntegralRecordRespDTO> getmemberIntegralRecords(MemberIntegralReqDTO memberListReqDTO);

    /**
     * 所有会员信息，不分页，导出用
     * @param memberListReqDTO
     * @return
     */
    List<MemberListRespDTO> AllMemberList(MemberListReqDTO memberListReqDTO);
}
