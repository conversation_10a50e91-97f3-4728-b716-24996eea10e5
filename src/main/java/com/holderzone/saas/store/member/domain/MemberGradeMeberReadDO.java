package com.holderzone.saas.store.member.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberGradeMeberReadDO
 * @date 2018/09/26 9:57
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class MemberGradeMeberReadDO implements Serializable{

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "业务主键")
    private String memberGuid;

    @ApiModelProperty(value = "注册时间")
    private LocalDateTime registerTime;


    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "性别 0：女，1:男，2：保密")
    private Byte sex;





    @ApiModelProperty(value = "生日")
    private LocalDate birthday;





    @ApiModelProperty(value = "会员等级的业务主键")
    private String memberGradeGuid;

    @ApiModelProperty(value = "会员等级")
    private String memberGradeDesc;

    @ApiModelProperty(value = "累计消费次数")
    private Integer totalConsumeNum;

    @ApiModelProperty(value = "累计充值次数")
    private Integer totalPrepaidNum;



    @ApiModelProperty(value = "累计消费金额（全部消费）")
    private BigDecimal totalConsumeFee;



    @ApiModelProperty(value = "累计充值金额")
    private BigDecimal totalPrepaidFee;



}
