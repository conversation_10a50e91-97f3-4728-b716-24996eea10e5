package com.holderzone.saas.store.member.entity.enums;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/8/19.
 */
public enum MemberGradeColorEnum {
    红色(1, "红色"),
    ;

    private int code;
    private String desc;

    private MemberGradeColorEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (MemberGradeColorEnum c : MemberGradeColorEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
