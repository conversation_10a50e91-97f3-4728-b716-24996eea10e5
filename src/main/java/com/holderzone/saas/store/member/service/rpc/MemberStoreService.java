package com.holderzone.saas.store.member.service.rpc;

import com.holderzone.saas.store.dto.organization.StoreDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberStoreService
 * @date 2018/10/18 11:51
 * @description //TODO
 * @program holder-saas-store-member
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = MemberStoreService.FallBack.class)
public interface MemberStoreService {

    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    @Component
    class FallBack implements FallbackFactory<MemberStoreService> {
        private static final Logger logger = LoggerFactory.getLogger(FallBack.class);

        @Override
        public MemberStoreService create(Throwable throwable) {
            return new MemberStoreService() {
                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    logger.error("获取门店详情失败,msg={}",throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }
}