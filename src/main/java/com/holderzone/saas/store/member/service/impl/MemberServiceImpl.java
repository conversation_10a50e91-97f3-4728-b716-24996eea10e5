package com.holderzone.saas.store.member.service.impl;

import com.alibaba.fastjson.JSON;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.base.dto.message.MessageType;
import com.holderzone.framework.base.dto.message.ShortMessageDTO;
import com.holderzone.framework.base.dto.message.ShortMessageType;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.publish.impl.CustomerPublishImpl;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.security.SecurityManager;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.ID2Utils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.resource.common.dto.enterprise.DeductShortMessageDTO;
import com.holderzone.resource.common.dto.enterprise.MessageConfigDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.member.MemberLoginRespDTO;
import com.holderzone.saas.store.dto.member.MemberNotifyDTO;
import com.holderzone.saas.store.dto.member.MemberRechargeDTO;
import com.holderzone.saas.store.dto.member.TransactionRecordDTO;
import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import com.holderzone.saas.store.dto.member.request.CreatMemberDTO;
import com.holderzone.saas.store.dto.member.request.ForgetPassWdReqDTO;
import com.holderzone.saas.store.dto.member.request.ModifiPassWdReqDTO;
import com.holderzone.saas.store.dto.member.request.UpdateMemberReqDTO;
import com.holderzone.saas.store.dto.member.response.BaseMemberRespDTO;
import com.holderzone.saas.store.dto.member.response.TransactionRecordRespDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.pay.constant.AggPayStateEnum;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import com.holderzone.saas.store.dto.trade.constant.PaymentType;
import com.holderzone.saas.store.member.domain.*;
import com.holderzone.saas.store.member.entity.bo.MemberPayBO;
import com.holderzone.saas.store.member.entity.bo.TransactionPrintBO;
import com.holderzone.saas.store.member.entity.constant.RedisConstant;
import com.holderzone.saas.store.member.entity.enums.MemberConfigTypeEnum;
import com.holderzone.saas.store.member.entity.enums.SendShortMessageTypeEnum;
import com.holderzone.saas.store.member.entity.pojo.MemberExpiryConfig;
import com.holderzone.saas.store.member.entity.transform.MemberTransform;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.member.mapper.*;
import com.holderzone.saas.store.member.service.MemberChargeService;
import com.holderzone.saas.store.member.service.MemberService;
import com.holderzone.saas.store.member.service.MemberTransactionService;
import com.holderzone.saas.store.member.service.rpc.EntServiceClient;
import com.holderzone.saas.store.member.service.rpc.MemberStoreService;
import com.holderzone.saas.store.member.service.rpc.MsgClientService;
import com.holderzone.saas.store.member.service.rpc.SaasAggPayClient;
import com.holderzone.saas.store.member.utils.CommonUtils;
import com.holderzone.saas.store.member.utils.DynamicHelper;
import com.holderzone.saas.store.member.utils.SendMessageUtil;
import com.holderzone.saas.store.member.utils.SquenceUtils;
import com.netflix.hystrix.exception.HystrixBadRequestException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.holderzone.framework.util.StringUtils.getStr;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberServiceImpl
 * @date 2018-08-06 11:37:50
 * @description
 * @program holder-saas-store-member
 */
@Service
@Slf4j
public class MemberServiceImpl implements MemberService {

    @Autowired
    private MemberMapper memberMapper;

    @Autowired
    private TransactionRecordMapper transactionRecordMapper;

    @Autowired
    private MemberConfigMapper memberConfigMapper;

    @Autowired
    private IntegralRuleMapper integralRuleMapper;

    @Autowired
    private MsgClientService msgClientService;

    @Autowired
    private MemberStoreService memberStoreService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private MemberTransform memberTransform = MemberTransform.INSTANCE;

    @Autowired
    private MemberGradeMapper memberGradeMapper;

    @Autowired
    private IntegralRecordMapper integralRecordMapper;

    @Autowired
    private MemberCardMapper memberCardMapper;

    @Autowired
    private DynamicHelper helper;

    @Autowired
    private SaasAggPayClient saasAggPayClient;

    @Autowired
    private CustomerPublishImpl customerPublish;

    @Autowired
    private EntServiceClient entServiceClient;

    @Autowired
    private SendMessageUtil su;

    @Autowired
    private DynamicHelper c1;

    @Autowired
    private MemberTransactionService memberTransactionService;

    @Value("${agg.notify}")
    private String notifyUrl;

    @Autowired
    private MemberChargeService memberChargeService;

    @Override
    @Transactional
    public Boolean create(CreatMemberDTO creatMemberDTO) {

        if (creatMemberDTO.getPassword().length() != 6) {
            throw new ParameterException("密码必须是6位字符");
        }
        if (creatMemberDTO.getPhone().length() != 11) {
            throw new ParameterException("请输入11位长度的手机号");
        }
        if (creatMemberDTO.getName().length() > 8) {
            throw new ParameterException("姓名必须小于8位字符");
        }
        //todo

        MessageDTO m1 = new MessageDTO();
        List<DeductShortMessageDTO> dslist = new ArrayList<>();
        DeductShortMessageDTO ds = new DeductShortMessageDTO();
        ds.setDeductCount(1);
        ds.setEnterpriseGuid(creatMemberDTO.getEnterpriseGuid());
        dslist.add(ds);

        Map<String, String> parms = new HashMap<>();

        ShortMessageDTO s1 = new ShortMessageDTO();


        String memberGuid = helper.generateGuid(RedisConstant.MEMBER_GUID);
        MemberDO memberDO = memberTransform.creatMemberDTO2MemberDO(creatMemberDTO);
        memberDO.setMemberGuid(memberGuid);
        //设置默认等级(初始化时创建)
        List<String> strings = memberGradeMapper.selectDefult();

        memberDO.setMemberGradeGuid(strings.get(0));

        //设置账号有效期
        MemberConfigDO memberConfigDO = memberConfigMapper.selectByConfigKey(MemberConfigTypeEnum.MEMBER_VALIDITY
                .getCode());
        if (memberConfigDO == null) {
            memberDO.setStartDate(LocalDate.now());
            memberDO.setExpiryType((byte) 0);
            parms.put("BeginTime", getDateTimeAsString(LocalDateTime.now(), "yyyy-MM-dd"));
            parms.put("EndTime", "永久");

        } else {
            String value = memberConfigDO.getValue();
            MemberExpiryConfig memberExpiryConfig = JacksonUtils.toObject(MemberExpiryConfig.class, value);
            memberDO.setStartDate(LocalDate.now());
            memberDO.setExpiryType(memberExpiryConfig.getExpiryType());
            //有限制的有效期
            if (memberExpiryConfig.getExpiryType().equals((byte) 1)) {
                memberDO.setStartDate(LocalDate.now());
                memberDO.setExpiryDate(LocalDate.now().plusYears(memberExpiryConfig.getYear()));
                memberDO.setExpiryConfigKey((byte) MemberConfigTypeEnum.MEMBER_VALIDITY.getCode());
                parms.put("BeginTime", getDateTimeAsString(LocalDateTime.now(), "yyyy-MM-dd"));
                parms.put("EndTime", getDateTimeAsString(memberDO.getExpiryDate(), "yyyy-MM-dd"));
            }
            if (memberExpiryConfig.getExpiryType().equals((byte) 0)) {
                memberDO.setStartDate(LocalDate.now());
                parms.put("BeginTime", getDateTimeAsString(LocalDateTime.now(), "yyyy-MM-dd"));
                parms.put("EndTime", "永久");
            }


        }

        //创建会员时创建电子卡
        MemberCardDO memberCardDO = new MemberCardDO();
        memberCardDO.setMemberGuid(memberGuid);
        memberCardDO.setMemberCardGuid(helper.generateGuid(RedisConstant.MEMBER_CARD_GUID));
        //电子会员卡号待定
        memberCardDO.setNum(String.valueOf(ID2Utils.builder().defaultBuild().nextId()));
        memberCardDO.setType((byte) 0);

        //实体卡
        if (creatMemberDTO.getNum() != null) {
            MemberCardDO memberEntityCardDO = new MemberCardDO();
            memberEntityCardDO.setMemberGuid(memberGuid);
            memberEntityCardDO.setMemberCardGuid(helper.generateGuid(RedisConstant
                    .MEMBER_CARD_GUID));
            //电子会员卡号待定
            memberEntityCardDO.setNum(creatMemberDTO.getNum());
            memberEntityCardDO.setType((byte) 1);
            try {
                memberCardMapper.insertSelective(memberEntityCardDO);
            } catch (DuplicateKeyException e) {
                throw new ParameterException("实体卡卡号重复");
            }
        }
        parms.put("Entertainment", creatMemberDTO.getEnterpriseName());
        //MD5加盐密码
        memberDO.setPassword(SecurityManager.entryptMd5(creatMemberDTO.getPassword(), true));
        memberMapper.insertSelective(memberDO);
        memberCardMapper.insertSelective(memberCardDO);
        m1.setMessageType(MessageType.SHORT_MESSAGE);
        s1.setPhoneNumber(memberDO.getPhone());
        s1.setShortMessageType(ShortMessageType.REGISTER_VER_SUCCESS);
        s1.setParams(parms);
        s1.setContent(parms.get("Entertainment") + "温馨提示:" + "尊敬的顾客，您已成功注册为会员，有效期" + parms.get("BeginTime") + "至" +
                parms.get("EndTime") + ",感谢您对本店的支持");
        m1.setShortMessage(s1);
        su.sendMessage(m1, creatMemberDTO.getEnterpriseGuid(), SendShortMessageTypeEnum.MEMBER_REGIS.getCode());
        c1.removeThreadLocalRedisInfo();
        c1.removeThreadLocalDatabaseInfo();
        return Boolean.TRUE;
    }

    @Override
    public String getPasswordByPhone(String phone) {
        return memberMapper.getPasswordByPhone(phone);
    }

    @Override
    public Boolean validateMemberByPhoneAndPasswd(ModifiPassWdReqDTO modifiPassWdReqDTO) {
        MemberDO memberDO = memberMapper.getMemberByPhone(modifiPassWdReqDTO.getPhone());
        if (memberDO == null) {
            throw new ParameterException("找不到该手机号对应的会员信息");
        }
        if (SecurityManager.decryptMd5(modifiPassWdReqDTO.getOldPassword(), memberDO.getPassword
                (), true)) {
            memberDO.setRecentlyLoginDate(LocalDateTime.now());
            memberMapper.updateByPrimaryKeySelective(memberDO);
        } else {
            return false;
        }
        return true;
    }

    @Override
    public BaseMemberRespDTO getMemberByPhone(BaseMemberDTO baseMemberDTO) {

        MemberCardReadDO memberByPhone = memberMapper.getMemberByPhoneOrCardNum(baseMemberDTO.getPhone());
        if (baseMemberDTO.getPhone().length() > 16) {
            throw new ParameterException("卡号长度不符");
        }
        if (memberByPhone == null) {
            throw new ParameterException("您搜索的会员不存在");
        }
        //会员有效期判断
        if (memberByPhone != null && memberByPhone.getExpiryType().intValue() != 0) {
            if (memberByPhone.getExpiryDate() != null && LocalDate.now().isAfter(memberByPhone.getExpiryDate())) {
                throw new ParameterException("该会员已过有效期");
            }
        }
        BaseMemberRespDTO baseMemberRespDTO = memberTransform.memberCardDOToBO(memberByPhone);
        //查询会员卡号
        String cardNum = memberCardMapper.getElectronCardNum(memberByPhone.getMemberGuid());
        baseMemberRespDTO.setCardNum(cardNum);
        //查询会员等级
        MemberGradeDO memberGradeDO = memberGradeMapper.getByMemberGradeGuid(memberByPhone.getMemberGradeGuid());
        if (memberGradeDO != null) {
            baseMemberRespDTO.setMemberGradeName(memberGradeDO.getName());
            if (memberGradeDO.getPrepaidLimit().equals("[null]")) {
                memberGradeDO.setPrepaidLimit("[]");
            }
            baseMemberRespDTO.setPrepaidLimit(JSON.parseArray(memberGradeDO.getPrepaidLimit()));
            baseMemberRespDTO.setPrepaidRule(memberGradeDO.getPrepaidRule());
        }

        return baseMemberRespDTO;
    }

    @Override
    public MemberLoginRespDTO getMemberByGuid(String memberGuid) {
        MemberDO memberDO = memberMapper.getMemberByGuid(memberGuid);
        MemberLoginRespDTO memberLoginRespDTO = new MemberLoginRespDTO();
        memberLoginRespDTO.setMemberGuid(memberDO.getMemberGuid());
        //会员卡余额=账户余额
        memberLoginRespDTO.setRemaining(memberDO.getBalance());
        //可用积分=剩余积分
        memberLoginRespDTO.setCouldUseScore(memberDO.getResidualIntegral());
        //获取积分规则
        MemberConsumeRuleReadDO m = integralRuleMapper.selectMemberConsumeInfoByGuid(memberDO.getMemberGradeGuid());

        memberLoginRespDTO.setMemberDiscount(m.getMemberDiscount());

        memberLoginRespDTO.setConsumeIntegralMax(m.getConsumeIntegralMax());
        memberLoginRespDTO.setConsumeIntegralUnit(m.getConsumeIntegralUnit());
        memberLoginRespDTO.setIsOpen(m.getIsOpen());
        memberLoginRespDTO.setConsumeFeeUnit(m.getConsumeFeeUnit());
        memberLoginRespDTO.setConsumeFeeMin(m.getConsumeFeeMin());

        return memberLoginRespDTO;
    }

    @Override
    public MemberLoginRespDTO getMemberLoginResByPhone(String phone) {

        MemberCardReadDO memberCardReadDO = memberMapper.getMemberByPhoneOrCardNum(phone);
        if (memberCardReadDO == null) {
            throw new HystrixBadRequestException("您搜索的会员不存在");
        }
        //会员有效期判断
        if (memberCardReadDO != null && memberCardReadDO.getExpiryType().intValue() != 0) {
            if (memberCardReadDO.getExpiryDate() != null && LocalDate.now().isAfter(memberCardReadDO.getExpiryDate())) {
                throw new HystrixBadRequestException("该会员已过有效期");
            }
        }

        //为出参设置熟悉

        if (memberCardReadDO != null) {
            MemberLoginRespDTO memberLoginRespDTO = new MemberLoginRespDTO();
            memberLoginRespDTO.setMemberGuid(memberCardReadDO.getMemberGuid());
            memberLoginRespDTO.setName(memberCardReadDO.getName());
            memberLoginRespDTO.setPassword(memberCardReadDO.getPassword());
            memberLoginRespDTO.setRemaining(memberCardReadDO.getBalance());
            memberLoginRespDTO.setCouldUseScore(memberCardReadDO.getResidualIntegral());
            memberLoginRespDTO.setTelPhoneNo(memberCardReadDO.getPhone());


            MemberConsumeRuleReadDO m = integralRuleMapper.selectMemberConsumeInfoByGuid(memberCardReadDO
                    .getMemberGradeGuid());

            memberLoginRespDTO.setMemberDiscount(m.getMemberDiscount());

            memberLoginRespDTO.setConsumeIntegralMax(m.getConsumeIntegralMax());
            memberLoginRespDTO.setConsumeIntegralUnit(m.getConsumeIntegralUnit());
            memberLoginRespDTO.setIsOpen(m.getIsOpen());
            memberLoginRespDTO.setConsumeFeeUnit(m.getConsumeFeeUnit());
            memberLoginRespDTO.setConsumeFeeMin(m.getConsumeFeeMin());
            return memberLoginRespDTO;
        }
        return null;

    }

    private void setIntegraRule(List<IntegralRuleDO> integralRuleDOS, MemberLoginRespDTO memberLoginRespDTO) {
        for (IntegralRuleDO integralRuleDO : integralRuleDOS) {
            //规则类型 0：获取规则，1:消费规则
            if (integralRuleDO.getType().equals((byte) 1)) {
                memberLoginRespDTO.setConsumeIntegralMax(integralRuleDO.getConsumeIntegralMax());
                memberLoginRespDTO.setConsumeIntegralUnit(integralRuleDO.getConsumeIntegralUnit());
                memberLoginRespDTO.setIsOpen(integralRuleDO.getIsOpen());
                memberLoginRespDTO.setConsumeFeeUnit(integralRuleDO.getConsumeFeeUnit());
                memberLoginRespDTO.setConsumeFeeMin(integralRuleDO.getConsumeFeeMin());
            }
        }
    }

    @Override
    public Boolean updateMember(UpdateMemberReqDTO updateMemberReqDTO) {
        MemberDO memberByPhone = memberMapper.getMemberByGuid(updateMemberReqDTO.getMemberGuid());

        if (updateMemberReqDTO.getPhone() == null) {
            throw new ParameterException("手机号不能为空");
        }
        if (updateMemberReqDTO.getName().length() > 8) {
            throw new ParameterException("会员姓名不能超过8个字符");
        }
        if (updateMemberReqDTO.getPhone().length() != 11) {
            throw new ParameterException("请输入11位手机号");
        }
        if (!updateMemberReqDTO.getPhone().equals(memberByPhone.getPhone())) {
            String passwordByPhone = memberMapper.getPasswordByPhone(updateMemberReqDTO.getPhone());
            if (StringUtils.isNotBlank(passwordByPhone)) {
                throw new ParameterException("手机号已经存在");
            } else {
                memberByPhone.setPhone(updateMemberReqDTO.getPhone());

            }
        }
        memberByPhone.setName(updateMemberReqDTO.getName());
        memberByPhone.setSex(updateMemberReqDTO.getSex());
        memberByPhone.setBirthday(updateMemberReqDTO.getBirthday());

        int i = memberMapper.updateByPrimaryKeySelective(memberByPhone);
        return Boolean.TRUE;
    }

    @Override
    public Page<TransactionRecordRespDTO> queryTransactionRecord(TransactionRecordDTO transactionRecordDTO) {
        Page<TransactionRecordRespDTO> result = new Page<>();
        if (transactionRecordDTO.getCurrentPage() != null) {
            result.setCurrentPage(transactionRecordDTO.getCurrentPage());
        }
        if (transactionRecordDTO.getPageSize() != null) {
            result.setPageSize(transactionRecordDTO.getPageSize());
        }

        List<TransactionRecordDO> transactionRecordDOS = transactionRecordMapper.selectByMemberGuid(result,
                transactionRecordDTO);
        List<TransactionRecordRespDTO> transactionRecordRespDTOS = memberTransform
                .transactionRecordDOS2transactionRecordRespDTOS(transactionRecordDOS);
        result.setData(transactionRecordRespDTOS);
        return result;
    }

    @Override
    public Boolean modifiPassWd(ModifiPassWdReqDTO modifiPassWdReqDTO) {
        MemberDO memberByPhone = memberMapper.getMemberByGuid(modifiPassWdReqDTO.getMemberGuid());
        if (modifiPassWdReqDTO.getNewPassword().equals(modifiPassWdReqDTO.getOldPassword())) {
            throw new ParameterException("新密码和旧密码不能一致");
        }
        if (!SecurityManager.decryptMd5(modifiPassWdReqDTO.getOldPassword(), memberByPhone.getPassword(), true)) {
            throw new ParameterException("原密码错误");
        }
        if (modifiPassWdReqDTO.getNewPassword() == null || StringUtils.isEmpty(modifiPassWdReqDTO.getNewPassword())) {
            throw new ParameterException("新密码不能为空");
        }
        memberByPhone.setPassword(SecurityManager.entryptMd5(modifiPassWdReqDTO.getNewPassword(), true));
        memberMapper.updateByPrimaryKeySelective(memberByPhone);
        return Boolean.TRUE;
    }

    @Override
    public Boolean forgetPassWd(ForgetPassWdReqDTO forgetPassWdReqDTO) {
        String VERIFICATION_KEY = CommonUtils.getVerificationKey(forgetPassWdReqDTO.getPhone());
        String verificationCode = stringRedisTemplate.opsForValue().get(VERIFICATION_KEY);
        if (!forgetPassWdReqDTO.getVerificationCode().equals(verificationCode)) {
            throw new ParameterException("验证码校验失败");
        }
        MemberDO memberByPhone = memberMapper.getMemberByPhone(forgetPassWdReqDTO.getPhone());
        if (memberByPhone == null) {
            throw new ParameterException("手机号不存在");
        }
        memberByPhone.setPassword(SecurityManager.entryptMd5(forgetPassWdReqDTO.getNewPassword(), true));
        int i = memberMapper.updateByPrimaryKeySelective(memberByPhone);
        if (i >= 1) {
            stringRedisTemplate.delete(VERIFICATION_KEY);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean verificationCode(BaseMemberDTO baseMemberDTO) {
        MessageDTO messageDTO = new MessageDTO();
        //设置信息类别
        messageDTO.setMessageType(MessageType.SHORT_MESSAGE);
        ShortMessageDTO shortMessageDTO = new ShortMessageDTO();
        //设置手机号码
        shortMessageDTO.setPhoneNumber(baseMemberDTO.getPhone());
        Map<String, String> paramMap = new HashMap<>(1);
        //生成随机验证码
        String numeric = RandomStringUtils.randomNumeric(6);

        String VERIFICATION_KEY = CommonUtils.getVerificationKey(baseMemberDTO.getPhone());

        stringRedisTemplate.opsForValue().set(VERIFICATION_KEY, numeric, 60 * 10, TimeUnit.SECONDS);
//        stringRedisTemplate.opsForValue().set(VERIFICATION_KEY, numeric);
        paramMap.put("Code", numeric);

        shortMessageDTO.setParams(paramMap);
        shortMessageDTO.setShortMessageType(ShortMessageType.MEMBER_FORGET_PWD_VER);
        shortMessageDTO.setContent("亲，您的验证码为:" + paramMap.get("Code") + "10分钟以后失效,如非本人操作，请忽略本短信！");
        messageDTO.setShortMessage(shortMessageDTO);
        List<DeductShortMessageDTO> ds = new ArrayList<>();
        DeductShortMessageDTO d = new DeductShortMessageDTO();
        d.setEnterpriseGuid(baseMemberDTO.getEnterpriseGuid());
        d.setDeductCount(1);
        ds.add(d);
        MessageConfigDTO messageInfo = entServiceClient.getMessageInfo(baseMemberDTO.getEnterpriseGuid());

        if (messageInfo == null) {
            throw new ParameterException("未获取到企业短信配置");
        }
        messageInfo.setResidueCount(messageInfo.getResidueCount() == -1 ? Integer.MAX_VALUE : messageInfo.getResidueCount());
        if (messageInfo.getResidueCount() >= 1) {
            msgClientService.sendMessage(messageDTO);
            entServiceClient.deductShortMessage(ds);
        }
        if ( messageInfo.getResidueCount() < 1) {
            throw new ParameterException("商户短信数量不足");
        }


        return Boolean.TRUE;
    }

    public static String getDateTimeAsString(LocalDateTime localDateTime, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return localDateTime.format(formatter);
    }

    public static String getDateTimeAsString(LocalDate localDate, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return localDate.format(formatter);
    }

    @Override
    public MemberCardDO getMemberCard(String memberGuid) {
        return memberCardMapper.getMemberEleCard(memberGuid);
    }

    @Override
    public Boolean InitDefaultMemberData(String entGuid) {

        MemberGradeDO memberGradeDO = new MemberGradeDO();
        memberGradeDO.setColorRGBA("#19be6b");
        memberGradeDO.setStyle(new Integer(0).byteValue());
        memberGradeDO.setIsDefault(new Integer(1).byteValue());
        memberGradeDO.setGmtCreate(LocalDateTime.now());
        memberGradeDO.setGmtModified(LocalDateTime.now());
        memberGradeDO.setFeeType(new Integer(0).byteValue());
        memberGradeDO.setDelete(false);
        memberGradeDO.setDiscount(new BigDecimal(10));
        String memberGradeGuid = helper.generateGuid(RedisConstant.MEMBER_GRADE_GUID);
        memberGradeDO.setMemberGradeGuid(memberGradeGuid);
        memberGradeDO.setName("普通会员");
        memberGradeDO.setPrepaidRule(new Integer(2).byteValue());
        memberGradeDO.setNeedIntegral(0);
        memberGradeDO.setMinimumPrepaidFee(new BigDecimal(0));
        memberGradeMapper.insertSelective(memberGradeDO);

        //获取规则，消费积分规则
        IntegralRuleDO io = new IntegralRuleDO();
        io.setAllowBalance(new Integer(0).byteValue());
        io.setConsumeFeeMin(new BigDecimal(0));
        io.setConsumeFeeUnit(new BigDecimal(0));
        io.setConsumeIntegralMax(0);
        io.setConsumeIntegralUnit(0);
        io.setIsOpen(new Integer(0).byteValue());
        io.setGmtCreate(LocalDateTime.now());
        io.setType(new Integer(0).byteValue());
        io.setMemberGradeGuid(memberGradeGuid);
        String integralRuleGuid = helper.generateGuid(RedisConstant.INTEGRAL_RULE_GUID);
        io.setIntegralRuleGuid(integralRuleGuid);
        io.setConsumeType(null);
        io.setGetType(2);

        integralRuleMapper.insertDefault(io);

        //获取规则 充值积分规则
        IntegralRuleDO io2 = new IntegralRuleDO();
        io2.setAllowBalance(new Integer(0).byteValue());
        io2.setConsumeFeeMin(new BigDecimal(0));
        io2.setConsumeFeeUnit(new BigDecimal(0));
        io2.setConsumeIntegralMax(0);
        io2.setConsumeIntegralUnit(0);
        io2.setIsOpen(new Integer(0).byteValue());
        io2.setGmtCreate(LocalDateTime.now());
        io2.setType(new Integer(0).byteValue());
        io2.setMemberGradeGuid(memberGradeGuid);
        io2.setGetType(1);
        String integralRuleGuid2 = helper.generateGuid(RedisConstant.INTEGRAL_RULE_GUID);
        io2.setIntegralRuleGuid(integralRuleGuid2);
        io2.setConsumeType(null);
        integralRuleMapper.insertDefault(io2);

        //消费抵现规则
        IntegralRuleDO io3 = new IntegralRuleDO();
        io3.setAllowBalance(new Integer(0).byteValue());
        io3.setConsumeFeeMin(new BigDecimal(0));
        io3.setConsumeFeeUnit(new BigDecimal(0));
        io3.setConsumeIntegralMax(0);
        io3.setConsumeIntegralUnit(0);
        io3.setIsOpen(new Integer(0).byteValue());
        io3.setGmtCreate(LocalDateTime.now());
        io3.setType(new Integer(1).byteValue());
        io3.setMemberGradeGuid(memberGradeGuid);
        io3.setConsumeType(3);
        io3.setGetType(null);
        String integralRuleGuid3 = helper.generateGuid(RedisConstant.INTEGRAL_RULE_GUID);
        io3.setIntegralRuleGuid(integralRuleGuid3);
        integralRuleMapper.insertDefault(io3);

        return Boolean.TRUE;
    }

    @Override
    public String checkPrepaidRule(MemberRechargeDTO memberRechargeDTO) {
        MemberDO memberByGuid = memberMapper.getMemberByGuid(memberRechargeDTO.getMemberGuid());
        if (memberByGuid == null) {
            throw new ParameterException("找不到会员信息");
        }

        MemberGradeDO memberGradeDO = memberGradeMapper.selectByPrimaryKey(memberByGuid.getMemberGradeGuid());
        if (memberGradeDO != null && memberGradeDO.getPrepaidRule().intValue() == 1) {
            List<Integer> bigDecimals = JSON.parseArray(memberGradeDO.getPrepaidLimit(), Integer.class);

            if (!bigDecimals.contains(memberRechargeDTO.getAmount().intValue())) {
                throw new ParameterException("当前充值金额与定额充值额度不匹配");
            }

        }
        if (memberGradeDO != null && memberGradeDO.getPrepaidRule().intValue() == 0) {
            throw new ParameterException("该会员等级不可充值!");
        }

        return "SUCCESS";
    }

    @Override
    public AggPayRespDTO memberCharge(MemberRechargeDTO memberRechargeDTO) {
        Integer paymentType = memberRechargeDTO.getPaymentType();
        String guid = helper.generateGuid(RedisConstant.INTEGRAL_RULE_GUID);
        MemberChargeDO memberChargeDo = memberTransform.createMemberChargeDo(memberRechargeDTO);
        memberChargeDo.setGuid(guid);
        memberChargeDo.setSequenceNo(SquenceUtils.nextId());
        if (Objects.equals(paymentType, PaymentType.JH_PAY.getId())) {
            // 聚合支付
            memberChargeDo.setAggPayGuid(guid);
            SaasAggPayDTO aggPayDTO = memberTransform.createSaasAggPayDto(memberRechargeDTO);
            AggPayPreTradingReqDTO payPreTradingReqDTO = memberTransform.createAggPayDto(memberRechargeDTO);
            payPreTradingReqDTO.setOrderGUID(guid);
            payPreTradingReqDTO.setPayGUID(guid);
            payPreTradingReqDTO.setOutNotifyUrl(notifyUrl);
            payPreTradingReqDTO.setAttachData(getStr(":", memberRechargeDTO.getMemberGuid(), memberChargeDo
                    .getSequenceNo()));
            aggPayDTO.setReqDTO(payPreTradingReqDTO);
            aggPayDTO.setSaasCallBackUrl("http://holder-saas-store-member/member/call_back");
            AggPayRespDTO payRespDTO = saasAggPayClient.pay(aggPayDTO);
            String code = payRespDTO.getCode();
            if ("10000".equalsIgnoreCase(code)) {
                AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
                pollingRespDTO.setCode(code);
                pollingRespDTO.setMsg(payRespDTO.getMsg());
                pollingRespDTO.setPaySt(AggPayStateEnum.READY.getId());
                memberChargeDo.setState(Integer.valueOf(AggPayStateEnum.READY.getId()));
            } else {
                memberChargeDo.setState(Integer.valueOf(AggPayStateEnum.FAILURE.getId()));
            }
            memberChargeService.insert(memberChargeDo);
            return payRespDTO;
        } else {
            memberChargeDo.setState(Integer.valueOf(AggPayStateEnum.SUCCESS.getId()));
            StoreDTO storeDTO = memberStoreService.queryStoreByGuid(memberRechargeDTO.getStoreGuid());
            memberChargeDo.setBusinessDay(checkBusinessDay(storeDTO));
            memberChargeDo.setPaidTime(DateTimeUtils.now());
            MemberNotifyDTO memberNotifyDTO = MemberNotifyDTO.builder()
                    .enterpriseGuid(memberRechargeDTO.getEnterpriseGuid())
                    .orderGuid(guid)
                    .amount(memberRechargeDTO.getAmount())
                    .type(0)
                    .flag(true)
                    .prepaidType(Byte.valueOf("" + memberRechargeDTO.getPaymentType()))
                    .prepaidTypeName(memberRechargeDTO.getPaymentName())
                    .useIntegral(false)
                    .storeGuid(memberRechargeDTO.getStoreGuid())
                    .storeName(memberRechargeDTO.getStoreName())
                    .memberGuid(memberRechargeDTO.getMemberGuid())
                    .sequenceNo(memberChargeDo.getSequenceNo())
                    .tradingTime(DateTimeUtils.now())
                    .operationStaffGuid(memberRechargeDTO.getUserGuid())
                    .operationStaffName(memberRechargeDTO.getUserName())
                    .build();
            memberNotifyDTO.setDeviceId(memberRechargeDTO.getDeviceId());
            memberNotifyDTO.setDeviceType(memberRechargeDTO.getDeviceType());
            dealMemberPayResult(memberRechargeDTO, memberNotifyDTO);
            memberChargeService.insert(memberChargeDo);
            return AggPayRespDTO.builder()
                    .msg("success")
                    .code("10000")
                    .result("success")
                    .build();
        }
    }

    public static LocalDate checkBusinessDay(StoreDTO storeInfo) {
        LocalDate businessDay;
        if (null == storeInfo.getBusinessStart() || null == storeInfo.getBusinessEnd()) {
            businessDay = LocalDate.now();
        } else {
            LocalTime now = LocalTime.now();
            LocalTime min = LocalTime.MIN;
            //营业日为当天
            businessDay = LocalDate.now();
            //跨天(结束时间小于等于开始时间)
            if (storeInfo.getBusinessStart().compareTo(storeInfo.getBusinessEnd()) >= 0) {
                //营业日为前一天（0：00<=当前时间<开始时间）
                if (min.compareTo(now) >= 0 && now.isBefore(storeInfo.getBusinessStart())) {
                    businessDay = LocalDate.now().minusDays(1);
                }
            }
        }
        return businessDay;
    }

    @Override
    public String saasTradingCallBack(SaasNotifyDTO saasNotifyDTO) {
        AggPayPollingRespDTO aggPayPollingRespDTO = saasNotifyDTO.getAggPayPollingRespDTO();
        //case 支付成功
        if (StringUtils.isNotBlank(aggPayPollingRespDTO.getOrderGUID())
                && StringUtils.equals("10000",aggPayPollingRespDTO.getCode())
                && StringUtils.equals("2",aggPayPollingRespDTO.getPaySt())
        ) {
            LocalDate businessDay = null;
            if (AggPayStateEnum.SUCCESS.getId().equals(aggPayPollingRespDTO.getPaySt())) {
                businessDay = checkBusinessDay(memberStoreService.queryStoreByGuid(saasNotifyDTO.getBaseInfo()
                        .getStoreGuid()));
            }
            memberChargeService.updateByTradingCallBack(aggPayPollingRespDTO, businessDay);
            UserContext userContext = UserContextUtils.get();
            BaseDTO baseDTO = memberTransform.userInfo2BaseDto(userContext);
            baseDTO.setDeviceId(saasNotifyDTO.getBaseInfo().getDeviceId());
            baseDTO.setDeviceType(saasNotifyDTO.getBaseInfo().getDeviceType());
            String attachData = aggPayPollingRespDTO.getAttachData();
            String[] split = attachData.split(":");
            boolean flag = AggPayStateEnum.SUCCESS.getId().equalsIgnoreCase(aggPayPollingRespDTO.getPaySt());
            MemberNotifyDTO memberNotifyDTO = MemberNotifyDTO.builder()
                    .enterpriseGuid(saasNotifyDTO.getBaseInfo().getEnterpriseGuid())
                    .orderGuid(aggPayPollingRespDTO.getOrderGUID())
                    .amount(aggPayPollingRespDTO.getAmount())
                    .type(0)
                    .flag(flag)
                    .prepaidType(Byte.valueOf(PaymentType.JH_PAY.getId() + ""))
                    .prepaidTypeName(PaymentType.JH_PAY.getName())
                    .useIntegral(false)
                    .storeGuid(userContext.getStoreGuid())
                    .storeName(userContext.getStoreName())
                    .memberGuid(split[0])
                    .sequenceNo(split[1])
                    .tradingTime(DateTimeUtils.now())
                    .operationStaffGuid(userContext.getUserGuid())
                    .operationStaffName(userContext.getUserName())
                    .build();
            dealMemberPayResult(baseDTO, memberNotifyDTO);
            return "success";
        } else {
            return "failure";
        }
    }

    @Override
    public String aggPayNotify(AggPayNotifyDTO aggPayNotifyDTO) {
        String paySt = aggPayNotifyDTO.getPaySt();
        if (AggPayStateEnum.SUCCESS.getId().equalsIgnoreCase(paySt) || AggPayStateEnum.FAILURE.getId()
                .equalsIgnoreCase(paySt)) {
            memberChargeService.updateByAggPayNotify(aggPayNotifyDTO);
            return "success";
        }
        return "failure";
    }

    private void dealMemberPayResult(BaseDTO baseDTO, MemberNotifyDTO memberNotifyDTO) {
        memberNotifyDTO.setEnterpriseName(baseDTO.getEnterpriseName());
        TransactionPrintBO transactionPrintBO = memberTransactionService.addTransactionRecord(memberNotifyDTO);
        MemberPayBO memberPayBO = new MemberPayBO();
        BaseInfo bs = memberTransform.createBaseInfo(baseDTO);
        memberPayBO.setBs(bs);
        memberPayBO.setEntGuid(memberNotifyDTO.getEnterpriseGuid() == null ? bs.getEnterpriseGuid() : memberNotifyDTO
                .getEnterpriseGuid());
        memberPayBO.setMemberNotifyDTO(memberNotifyDTO);
        memberPayBO.setTransactionPrintBO(transactionPrintBO);
        memberPayBO.setMessageDTO(transactionPrintBO.getMessageDTO());
        memberPayBO.setType(memberNotifyDTO.getType());
        memberPayBO.setStoreGuid(memberNotifyDTO.getStoreGuid());
        if (transactionPrintBO.getMessageDTO() != null) {
            customerPublish.publish(new CustomerEvent<>(memberPayBO));
        }
    }
}
