package com.holderzone.saas.store.member.entity.member;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@ApiModel(
    value = "RechargeReqDTO",
    description = "充值reqDTO"
)
public class RechargeReqDTO {
    @ApiModelProperty("支付guid")
    private String payGuid;
    @ApiModelProperty("订单guid")
    private String orderGuid;
    @ApiModelProperty("会员持卡guid")
    private String memberInfoCardGuid;
    @DecimalMin(
        value = "0.01",
        message = "充值金额在0.01~999999.99"
    )
    @DecimalMax(
        value = "999999.99",
        message = "充值金额在0.01~999999.99"
    )
    @ApiModelProperty(
        value = "充值金额",
        required = true
    )
    private BigDecimal rechargeMoney;
    @ApiModelProperty("联盟id")
    private String allianceId;
    @ApiModelProperty(
        value = "企业GUID",
        required = true
    )
    private String enterpriseGuid;
    @ApiModelProperty(
        value = "企业名称",
        required = true
    )
    private String enterpriseName;
    @ApiModelProperty(
        value = "品牌GUID",
        required = true
    )
    private String brandGuid;
    @ApiModelProperty(
        value = "品牌名称",
        required = true
    )
    private String brandName;
    @ApiModelProperty(
        value = "门店GUID",
        required = true
    )
    private String storeGuid;
    @ApiModelProperty("门店名称")
    private String storeName;
    @ApiModelProperty("充值所在门店所在的地址")
    private String storeAddress;
    @ApiModelProperty("充值所在门店所在的地址")
    private String storeTelephone;
    @NotNull
    @ApiModelProperty("支付方式，0 卡余额支付 1现金，2聚合支付，3银行卡支付 ，4人脸支付 5,自定义")
    private Integer payWay;
    @ApiModelProperty("支付方式名称")
    private String payName;
    @ApiModelProperty("支付码")
    private String payCode;
    @ApiModelProperty("银行交易流水号")
    private String bankTransactionId;
    @NotNull
    @ApiModelProperty("订单（充值）来源，0微信，1一体机,2POS")
    private Integer orderSource;
    @ApiModelProperty("充值/下单时间")
    private Date orderTime;
    @ApiModelProperty(
        value = "支付渠道",
        notes = "0:现金，1,聚合2微信"
    )
    private Integer payChannel;

    @ApiModelProperty(value = "食堂卡支付")
    private Boolean canteenCard;

    @JsonIgnore
    public String getPayWayDesc() {
        return 0 == this.payWay ? "账户余额" : (1 == this.payWay ? "微信支付" : (2 == this.payWay ? "支付宝" : (3 == this.payWay ? "现金" : (4 == this.payWay ? "银行卡支付" : (5 == this.payWay ? "微信公众号" : "未知")))));
    }
}
