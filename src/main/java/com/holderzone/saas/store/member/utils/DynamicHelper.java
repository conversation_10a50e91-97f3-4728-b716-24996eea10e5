package com.holderzone.saas.store.member.utils;


import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.sdk.util.IdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DynamicUtils
 * @date 2018/10/25 15:28
 * @description //TODO
 * @program holder-saas-store-order
 */
@Component
public class DynamicHelper {

    private static final Logger log = LoggerFactory.getLogger(DynamicHelper.class);

    @Value("${self.open-dynamic-datasource}")
    private Boolean openDynamicDatasource;


    public void changeDatasource(String enterpriseGuid) {
        if (openDynamicDatasource) {
            try {
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            } catch (Exception e) {
                log.error("手动切换数据源异常！enterpriseGuid:" + enterpriseGuid + ":{}", e.getMessage());
            }
        }
    }

    public void changeRedis(String enterpriseGuid) {
        if (openDynamicDatasource) {
            try {
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            } catch (Exception e) {
                log.error("手动切换数据源异常！enterpriseGuid:" + enterpriseGuid + ":{}", e.getMessage());
            }
        }
    }


    /**
     * 生成guid
     *
     * @param redisKey
     * @return
     */
    public String generateGuid(String redisKey) {
        RedisTemplate redisTemplate = SpringContextUtils.getInstance().getBean("redisTemplate");
        return String.valueOf(IdGenerator.builder(redisTemplate,
                5).next(redisKey));
    }

    public void removeThreadLocalDatabaseInfo() {
        if (openDynamicDatasource) {
            EnterpriseIdentifier.remove();
        }
    }

    public void removeThreadLocalRedisInfo() {
        if (openDynamicDatasource) {
            EnterpriseIdentifier.remove();
        }
    }
}
