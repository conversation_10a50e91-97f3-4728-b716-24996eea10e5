package com.holderzone.saas.store.member.entity.enums;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/8/19.
 */
public enum ConsumeIntegralRuleTypeEnum {
    OFFSET(3, "消费抵现"),
    ;

    private int code;
    private String desc;

    private ConsumeIntegralRuleTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (ConsumeIntegralRuleTypeEnum c : ConsumeIntegralRuleTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
