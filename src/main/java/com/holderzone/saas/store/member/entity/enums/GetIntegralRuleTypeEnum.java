package com.holderzone.saas.store.member.entity.enums;

import com.holderzone.saas.store.member.domain.IntegralRuleDO;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/8/19.
 */
public enum GetIntegralRuleTypeEnum {
    PREPAID(1, "充值积分"),
    CONSUME(2, "消费积分"),
    ;

    private int code;
    private String desc;

    private GetIntegralRuleTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (GetIntegralRuleTypeEnum c : GetIntegralRuleTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
