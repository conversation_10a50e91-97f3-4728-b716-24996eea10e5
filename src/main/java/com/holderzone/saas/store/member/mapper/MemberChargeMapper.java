package com.holderzone.saas.store.member.mapper;

import com.holderzone.saas.store.dto.pay.AggPayNotifyDTO;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.member.domain.MemberChargeDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberChargeMapper
 * @date 2019/03/18 15:16
 * @description
 * @program holder-saas-store-member
 */
@Repository
public interface MemberChargeMapper {


    void insert(MemberChargeDO memberChargeDo);

    void updateByTradingCallBack(@Param("aggPayPollingRespDTO") AggPayPollingRespDTO aggPayPollingRespDTO, @Param("businessDay") LocalDate businessDay);

    void updateByAggPayNotify(AggPayNotifyDTO aggPayNotifyDTO);
}
