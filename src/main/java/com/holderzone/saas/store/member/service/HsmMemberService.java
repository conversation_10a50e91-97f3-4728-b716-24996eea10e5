package com.holderzone.saas.store.member.service;

import com.holderzone.saas.store.dto.pay.CardRechargeCashInfoQO;
import com.holderzone.saas.store.dto.member.hsm.HsmAggPayRespDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.dto.pay.SaasNotifyDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;

public interface HsmMemberService {

    HsmAggPayRespDTO recharge(HsmRechargeReqDTO hsmRechargeReqDTO);

    WxPayRespDTO wechatRecharge(HsmRechargeReqDTO hsmRechargeReqDTO);

    String callback(SaasNotifyDTO saasNotifyDTO);

    Integer revokeCharge(SaasPollingDTO saasPollingDTO);

    Boolean printRecharge(CardRechargeCashInfoQO cardRechargeCashInfoQO);
}
