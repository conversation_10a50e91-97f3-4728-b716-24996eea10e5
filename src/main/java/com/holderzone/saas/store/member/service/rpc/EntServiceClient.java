package com.holderzone.saas.store.member.service.rpc;

import com.holderzone.resource.common.dto.enterprise.DeductShortMessageDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.MessageConfigDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EntServiceClient
 * @date 2018/10/30 18:45
 * @description //TODO
 * @program holder-saas-store-member
 */
@Component
@FeignClient(name = "holder-saas-cloud-enterprise", fallbackFactory = EntServiceClient.FallBack.class)
public interface EntServiceClient {
    @GetMapping("/enterprise/sms/info")
    public MessageConfigDTO getMessageInfo(@RequestParam("enterpriseGuid") String enterpriseGuid);

    @PostMapping("/enterprise/sms/deduct")
    public Boolean deductShortMessage(@RequestBody List<DeductShortMessageDTO> shortMessageList);

    @PostMapping("/enterprise/find")
    EnterpriseDTO findEnterprise(BaseDTO baseDTO);

    @Component
    public class FallBack implements FallbackFactory<EntServiceClient> {
        private static final Logger logger = LoggerFactory.getLogger(EntServiceClient.FallBack.class);

        @Override
        public EntServiceClient create(Throwable throwable) {
            return new EntServiceClient() {
                @Override
                public MessageConfigDTO getMessageInfo(String enterpriseGuid) {
                    logger.error("获取企业短信详情失败,msg={}", throwable.getMessage());
                    return null;

                }

                @Override
                public Boolean deductShortMessage(List<DeductShortMessageDTO> shortMessageList) {
                    logger.error("扣除剩余短信失败,msg={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public EnterpriseDTO findEnterprise(BaseDTO baseDTO) {
                    logger.error("查询企业信息发生异常，msg={}", throwable.getMessage());
                    EnterpriseDTO enterpriseDTO = new EnterpriseDTO();
                    enterpriseDTO.setEnterpriseGuid(baseDTO.getEnterpriseGuid());
                    enterpriseDTO.setName(baseDTO.getEnterpriseName());
                    return enterpriseDTO;
                }
            };
        }
    }

}
