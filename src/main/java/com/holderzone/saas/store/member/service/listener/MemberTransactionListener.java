package com.holderzone.saas.store.member.service.listener;

import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.publish.impl.CustomerPublishImpl;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.constant.member.RocketMQKeysConstant;
import com.holderzone.saas.store.dto.member.MemberNotifyDTO;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import com.holderzone.saas.store.member.entity.bo.MemberPayBO;
import com.holderzone.saas.store.member.entity.bo.TransactionPrintBO;
import com.holderzone.saas.store.member.service.MemberTransactionService;
import com.holderzone.saas.store.member.utils.DynamicHelper;
import com.holderzone.saas.store.member.utils.RedisUtil;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberTransactionListener
 * @date 2018/10/08 14:44
 * @description
 * @program holder-saas-store-member
 */
@Component
@RocketListenerHandler(topic = RocketMQKeysConstant
        .MEMBER_NOTIFY_TOPIC,
        tags = RocketMQKeysConstant.MEMBER_NOTIFY_TAG,
        consumerGroup = RocketMQKeysConstant.MEMBER_NOTIFY_CONSUMER_GROUP, reTryConsume = 3)
public class MemberTransactionListener extends AbstractRocketMqConsumer<RocketMqTopic, MemberNotifyDTO> {
    private static final Logger logger = LoggerFactory.getLogger(MemberTransactionListener.class);
    @Autowired
    private MemberTransactionService memberTransactionService;
    @Autowired
    private DynamicHelper helper;
    @Autowired
    private DynamicHelper c1;
    @Autowired
    private CustomerPublishImpl customerPublish;
    @Autowired
    private RedisUtil redisUtil;

    @Override
    public boolean consumeMsg(MemberNotifyDTO memberNotifyDTO, MessageExt messageExt) {

        helper.changeDatasource(memberNotifyDTO.getEnterpriseGuid());
        helper.changeRedis(memberNotifyDTO.getEnterpriseGuid());
        String msgkey = redisUtil.initKey("memberNotify", messageExt.getMsgId());
        if (redisUtil.get(msgkey) != null && redisUtil.get(msgkey).equals("consumed")) {
            return true;
        }
        if (!memberNotifyDTO.getFlag()) {
            return true;
        }

        BaseInfo bs = JacksonUtils.toObject(BaseInfo.class, messageExt.getProperty("baseInfo"));
        memberNotifyDTO.setEnterpriseName(bs.getEnterpriseName());

        TransactionPrintBO transactionPrintBO = memberTransactionService.addTransactionRecord(memberNotifyDTO);
        logger.info(messageExt.getMsgId() + "被消费了");
        //记录消息消费状态
        redisUtil.set(msgkey, "consumed");
        Date currentTime = new Date();
        Date nextDay = getNextDay(currentTime);


        redisUtil.expireAt(msgkey, nextDay);
        MemberPayBO memberPayBO = new MemberPayBO();
        memberPayBO.setBs(bs);
        memberPayBO.setEntGuid(memberNotifyDTO.getEnterpriseGuid() == null ? bs.getEnterpriseGuid() : memberNotifyDTO
                .getEnterpriseGuid());
        memberPayBO.setMemberNotifyDTO(memberNotifyDTO);
        memberPayBO.setTransactionPrintBO(transactionPrintBO);
        memberPayBO.setMessageDTO(transactionPrintBO.getMessageDTO());
        memberPayBO.setType(memberNotifyDTO.getType());
        memberPayBO.setStoreGuid(memberNotifyDTO.getStoreGuid());
        if (transactionPrintBO.getMessageDTO() != null) {
            customerPublish.publish(new CustomerEvent<>(memberPayBO));
        }

        c1.removeThreadLocalDatabaseInfo();
        c1.removeThreadLocalRedisInfo();
        return true;
    }

    static Date getNextDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, +1);//+1今天的时间加一天
        date = calendar.getTime();
        return date;
    }
}
