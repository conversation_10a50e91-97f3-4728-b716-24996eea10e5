package com.holderzone.saas.store.member.service;


import com.holderzone.saas.store.dto.member.MemberNotifyDTO;
import com.holderzone.saas.store.dto.member.request.HandoverReqDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO;
import com.holderzone.saas.store.dto.order.response.daily.MemberConsumeRespDTO;
import com.holderzone.saas.store.member.entity.bo.TransactionPrintBO;

import javax.validation.Valid;
import java.util.List;


public interface MemberTransactionService {
    /**
     * 从mq接受消息后新增会员交易记录
     * @param memberNotifyDTO
     * @return
     */
    TransactionPrintBO addTransactionRecord(MemberNotifyDTO memberNotifyDTO);

    Boolean checkBalance(MemberNotifyDTO memberNotifyDTO);

    /**
     * 商户获取会员消费统计
     * @param request
     * @return
     */
    MemberConsumeRespDTO getMemberRechargeRecord(@Valid DailyReqDTO request);

    /**
     * 交接班获取当班时间内充值订单和充值金额
     * @param request
     * @return
     */
    MemberConsumeRespDTO getMemberRechargeStore(@Valid HandoverReqDTO request);

    /**
     * 营业日报获取会员充值收入
     * @param request
     * @return
     */
    List<GatherRespDTO> getBusinessMemberRechargeEarnings(@Valid DailyReqDTO request);
}
