package com.holderzone.saas.store.member.service.impl;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import com.holderzone.saas.store.dto.member.request.MemberCardsReqDTO;
import com.holderzone.saas.store.dto.member.request.MemberIntegralReqDTO;
import com.holderzone.saas.store.dto.member.request.MemberListReqDTO;
import com.holderzone.saas.store.dto.member.request.MemberPayRecordReqDTO;
import com.holderzone.saas.store.dto.member.response.*;
import com.holderzone.saas.store.member.domain.*;
import com.holderzone.saas.store.member.entity.enums.IntegralTransactionTypeEnum;
import com.holderzone.saas.store.member.entity.transform.MemberTransform;
import com.holderzone.saas.store.member.mapper.*;
import com.holderzone.saas.store.member.service.MemberListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberListServiceImpl
 * @date 2018/09/30 14:51
 * @description //TODO
 * @program holder-saas-store-order
 */
@Service
public class MemberListServiceImpl implements MemberListService {
    @Autowired
    private MemberMapper memberMapper;

    @Autowired
    private MemberCardMapper memberCardMapper;

    @Autowired
    private TransactionRecordMapper transactionRecordMapper;

    private MemberTransform memberTransform = MemberTransform.INSTANCE;

    @Autowired
    private MemberGradeMapper memberGradeMapper;


    @Autowired
    private IntegralRecordMapper integralRecordMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    @Override
    public Page<MemberListRespDTO> findMemberList(MemberListReqDTO memberListReqDTO) {

        Page<MemberListRespDTO> result = new Page<>();
        if (memberListReqDTO.getCurrentPage() != null) {
            result.setCurrentPage(memberListReqDTO.getCurrentPage());

        }
        if (memberListReqDTO.getPageSize() != null) {
            result.setPageSize(memberListReqDTO.getPageSize());
        }
        if (memberListReqDTO.getSex() != null && memberListReqDTO.getSex() == -1) {
            memberListReqDTO.setSex(null);
        }
        List<MemberGradeMeberReadDO> memberListRecords = memberMapper.selectMemberList(result,
                memberListReqDTO);
        List<MemberListRespDTO> transactionMemberDTOS = memberTransform
                .transactionMemberGradeMeberDO2MemberResponseDTOS(memberListRecords);
        result.setData(transactionMemberDTOS);
        return result;
    }

    @Override
    public MemberDetailRespDTO getMemberDetailByGuid(BaseMemberDTO baseMemberDTO) {
        MemberDetailReadDO memberDetailReadDO = memberMapper.getMemberDetailByGuid(baseMemberDTO.getMemberGuid());
        return memberTransform.transactionMemberDetailReadDO2MemberDetailResponseDTO(memberDetailReadDO);
    }

    @Override
    public List<MemberCardsRespDTO> getMemberCards(MemberCardsReqDTO memberCardsReqDTO) {
        String memberGuid = memberCardsReqDTO.getMemberGuid();
       if (memberMapper.getMemberDetailByGuid(memberGuid) == null) {
            return null;
        }
        StringBuilder keybuilder=new StringBuilder();
        String key=keybuilder.append("member:").append("membercards:").append(memberGuid).toString();
        List<MemberCardDO> memberDOS = memberCardMapper.getMemberCards(memberCardsReqDTO);

        return memberTransform.transactionMemberCardDO2MemberCardsRespDTO(memberDOS);
    }

    @Override
    public List<MemberGradeListDTO> memberGradeList(BaseDTO baseDTO) {
        List<MemberGradeListReadDO> allMemberGrade = memberGradeMapper.getAllMemberGrade();
        return memberTransform.transactionMemberGradeListReadDO2MemberGradeDTOList(allMemberGrade);
    }

    @Override
    public Page<MemberPayRecordRespDTO> getTransactionRecord(MemberPayRecordReqDTO memberPayRecordReqDTO) {
        Page<MemberPayRecordRespDTO> page = new Page<>();
        if (memberPayRecordReqDTO.getCurrentPage() != null) {
            page.setCurrentPage(memberPayRecordReqDTO.getCurrentPage());

        }
        if (memberPayRecordReqDTO.getPageSize() != null) {
            page.setPageSize(memberPayRecordReqDTO.getPageSize());
        }
        if (memberPayRecordReqDTO.getMemberGuid() == null || memberMapper.getMemberDetailByGuid(memberPayRecordReqDTO
                .getMemberGuid()) == null) {
            return null;

        }
        List<TransactionRecordDO> memberListRecords = transactionRecordMapper.selectMemberTransactions(page,
                memberPayRecordReqDTO);
        List<MemberPayRecordRespDTO> memberListRespDTOS = memberTransform
                .transactionMemberTransactionDO2PayRecordRespDTOS(memberListRecords);
        Collections.sort(memberListRespDTOS,
                (o1, o2) -> o2.getTransactionTime().compareTo(o1.getTransactionTime()));
        page.setData(memberListRespDTOS);
        return page;
    }

    @Override
    public List<MemberIntegralTypeRespDTO> getMemberIntegralType(BaseDTO baseDTO) {
        List<MemberIntegralTypeRespDTO> memberIntegralType = new ArrayList<MemberIntegralTypeRespDTO>();

        for (IntegralTransactionTypeEnum c : IntegralTransactionTypeEnum.values()) {
            MemberIntegralTypeRespDTO memberIntegralTypeRespDTO = new MemberIntegralTypeRespDTO();
            memberIntegralTypeRespDTO.setCode(c.getCode());
            memberIntegralTypeRespDTO.setDesc(c.getDesc());
            memberIntegralType.add(memberIntegralTypeRespDTO);
        }
        return memberIntegralType;
    }

    @Override
    public Page<MemberIntegralRecordRespDTO> getmemberIntegralRecords(MemberIntegralReqDTO memberIntegralReqDTO) {
        Page<MemberIntegralRecordRespDTO> page = new Page<>();

        if (memberIntegralReqDTO.getCurrentPage() != null) {
            page.setCurrentPage(memberIntegralReqDTO.getCurrentPage());

        }
        if (memberIntegralReqDTO.getPageSize() != null) {
            page.setPageSize(memberIntegralReqDTO.getPageSize());
        }
        if (memberMapper.getMemberDetailByGuid(memberIntegralReqDTO.getMemberGuid()) == null) {
            throw new ParameterException("找不到对应的会员信息");

        }
        List<IntegralRecordDO> memberListRecords = integralRecordMapper.selectMemberIntegral(page,
                memberIntegralReqDTO);

        List<MemberIntegralRecordRespDTO> memberListRespDTOS = new ArrayList<>();
        for (IntegralRecordDO io : memberListRecords) {
            MemberIntegralRecordRespDTO memberIntegralRecordRespDTO = new MemberIntegralRecordRespDTO();

            memberIntegralRecordRespDTO.setType(io.getType());
            if(io.getType() != null){
                memberIntegralRecordRespDTO.setTypeDesc(IntegralTransactionTypeEnum.getDesc(io.getType()));
            }
            memberIntegralRecordRespDTO.setMemberGuid(io.getMemberGuid());
            memberIntegralRecordRespDTO.setOrderNo(io.getOrderNo());
            memberIntegralRecordRespDTO.setResidualIntegral(io.getResidualIntegral());
            memberIntegralRecordRespDTO.setTransactionIntegral(io.getTransactionIntegral());
            memberIntegralRecordRespDTO.setStaffGuid(io.getStaffGuid());
            memberIntegralRecordRespDTO.setStaffName(io.getStaffName());
            memberIntegralRecordRespDTO.setTransactionTime(io.getTransactionTime());
            memberIntegralRecordRespDTO.setBusinessDay(io.getBusinessDay());
            memberListRespDTOS.add(memberIntegralRecordRespDTO);

        }
        Collections.sort(memberListRespDTOS,
                (o1, o2) -> o2.getTransactionTime().compareTo(o1.getTransactionTime()));
        page.setData(memberListRespDTOS);
        return page;
    }

    @Override
    public List<MemberListRespDTO> AllMemberList(MemberListReqDTO memberListReqDTO) {
        List<MemberGradeMeberReadDO> memberListRecords = memberMapper.AllMemberList(
                memberListReqDTO);

        List<MemberListRespDTO> transactionMemberDTOS = memberTransform
                .transactionMemberGradeMeberDO2MemberResponseDTOS(memberListRecords);

        return transactionMemberDTOS;

    }
}
