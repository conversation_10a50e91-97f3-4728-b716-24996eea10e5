package com.holderzone.saas.store.member.service.listener;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.saas.store.constant.member.RocketMQKeysConstant;
import com.holderzone.saas.store.member.mapper.MemberGradeMapper;
import com.holderzone.saas.store.member.service.MemberService;
import com.holderzone.saas.store.member.service.MemberTransactionService;
import com.holderzone.saas.store.member.utils.DynamicHelper;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberTransactionListener
 * @date 2018/10/08 14:44
 * @description //TODO
 * @program holder-saas-store-member
 */

@Component
@RocketListenerHandler(topic = RocketMQKeysConstant
        .INIT_DATABASE_TOPIC,
        tags = RocketMQKeysConstant.INIT_TABLE_INFO_TAG,
        consumerGroup = RocketMQKeysConstant.MEMBER_DEFAULT_GRADE, reTryConsume = 3)
public class MemberGradeDefaultListener extends AbstractRocketMqConsumer<RocketMqTopic, String> {
    private static final Logger logger = LoggerFactory.getLogger(MemberGradeDefaultListener.class);
    @Autowired
    MemberTransactionService memberTransactionService;
    @Autowired
    MemberGradeMapper memberGradeMapper;
    @Autowired
    DynamicHelper helper;
    @Autowired
    private MemberService memberService;
    @Autowired
    private DynamicHelper c;

    @Override
    public boolean consumeMsg(String enterpriseGuid, MessageExt messageExt) {

        logger.info(messageExt.getMsgId() + "被消费了");
        helper.changeDatasource(enterpriseGuid);
        helper.changeRedis(enterpriseGuid);
        if (memberGradeMapper.selectDefult().size() == 0) {
            memberService.InitDefaultMemberData(enterpriseGuid);
        }
        c.removeThreadLocalDatabaseInfo();
        c.removeThreadLocalRedisInfo();

        return true;
    }


}

