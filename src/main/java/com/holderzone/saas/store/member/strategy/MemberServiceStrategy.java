package com.holderzone.saas.store.member.strategy;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import com.holderzone.saas.store.dto.member.MemberLoginRespDTO;
import com.holderzone.saas.store.dto.member.MemberRechargeDTO;
import com.holderzone.saas.store.dto.member.TransactionRecordDTO;
import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import com.holderzone.saas.store.dto.member.request.*;
import com.holderzone.saas.store.dto.member.response.*;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.member.domain.MemberCardDO;
import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;

import java.util.List;

/**
 * 会员服务策略接口
 * 
 * <AUTHOR>
 * @date 2025/8/8
 */
public interface MemberServiceStrategy {
    
    /**
     * 获取策略类型
     * 
     * @return 会员服务类型
     */
    MemberServiceTypeEnum getServiceType();

    /**
     * 创建会员
     */
    Boolean create(CreatMemberDTO creatMemberDTO);

    /**
     * 根据手机号获取密码
     */
    String getPasswordByPhone(String phone);

    /**
     * 验证会员密码
     */
    Boolean validateMemberByPhoneAndPasswd(ModifiPassWdReqDTO modifiPassWdReqDTO);

    /**
     * 根据手机号获取会员信息
     */
    BaseMemberRespDTO getMemberByPhone(BaseMemberDTO baseMemberDTO);

    /**
     * 根据GUID获取会员信息
     */
    MemberLoginRespDTO getMemberByGuid(String memberGuid);

    /**
     * 根据手机号获取会员登录信息
     */
    MemberLoginRespDTO getMemberLoginResByPhone(String phone);

    /**
     * 更新会员信息
     */
    Boolean updateMember(UpdateMemberReqDTO updateMemberReqDTO);

    /**
     * 查询交易记录
     */
    Page<TransactionRecordRespDTO> queryTransactionRecord(TransactionRecordDTO transactionRecordDTO);

    /**
     * 修改会员密码
     */
    Boolean modifiPassWd(ModifiPassWdReqDTO modifiPassWdReqDTO);

    /**
     * 忘记会员密码
     */
    Boolean forgetPassWd(ForgetPassWdReqDTO forgetPassWdReqDTO);

    /**
     * 发送验证码
     */
    Boolean verificationCode(BaseMemberDTO baseMemberDTO);

    /**
     * 获取会员卡
     */
    MemberCardDO getMemberCard(String memberGuid);

    /**
     * 初始化默认会员数据
     */
    Boolean InitDefaultMemberData(String entGuid);

    /**
     * 验证充值规则
     */
    String checkPrepaidRule(MemberRechargeDTO memberRechargeDTO);

    /**
     * 会员充值
     */
    AggPayRespDTO memberCharge(MemberRechargeDTO memberRechargeDTO);

    /**
     * 交易中心回调
     */
    String saasTradingCallBack(SaasNotifyDTO saasNotifyDTO);

    /**
     * 聚合支付回调
     */
    String aggPayNotify(AggPayNotifyDTO aggPayNotifyDTO);

    /**
     * 根据手机号后四位查询会员
     */
    List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail);
}
