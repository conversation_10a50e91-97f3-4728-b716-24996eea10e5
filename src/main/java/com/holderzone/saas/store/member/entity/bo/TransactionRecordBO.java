package com.holderzone.saas.store.member.entity.bo;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TransactionRecordBO
 * @date 2018/08/27 下午5:29
 * @description //TODO
 * @program holder-saas-config-center
 */
public class TransactionRecordBO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 交易时间
     */
    private LocalDateTime transactionTime;

    /**
     * 交易金额
     */
    private BigDecimal fee;

    /**
     * 交易类型 0：充值，1:支付
     */
    private Byte type;

    /**
     * 余额
     */
    private BigDecimal balance;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDateTime getTransactionTime() {
        return transactionTime;
    }

    public void setTransactionTime(LocalDateTime transactionTime) {
        this.transactionTime = transactionTime;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }
}
