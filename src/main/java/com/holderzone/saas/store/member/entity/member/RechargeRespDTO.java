package com.holderzone.saas.store.member.entity.member;

import com.mos.secure.ext.annotations.DesensitizationProp;
import com.mos.secure.ext.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@ApiModel(
    value = "RechargeRespDTO",
    description = "充值响应DTO"
)
public class RechargeRespDTO {
    @ApiModelProperty("支付Guid")
    private String payGuid;
    @ApiModelProperty("订单Guid")
    private String orderGuid;
    @ApiModelProperty("门店名")
    private String storeName;
    @ApiModelProperty("交易流水号")
    private String serialNumber;
    @ApiModelProperty("充值金额")
    private BigDecimal recharge;
    @ApiModelProperty("赠送金额")
    private BigDecimal presented;
    @ApiModelProperty("赠送积分")
    private BigDecimal presentedIntegral;
    @ApiModelProperty("到账金额")
    private BigDecimal arrival;
    @ApiModelProperty("充值方式")
    private String payWay;
    @ApiModelProperty("充值方式名称")
    private String payName;
    @ApiModelProperty("充值卡号")
    private String cardNo;
    @ApiModelProperty("当前金额")
    private BigDecimal currentCash;
    @ApiModelProperty("当前积分")
    private String integration;
    @ApiModelProperty("充值时间")
    private Long rechargeTime;
    @ApiModelProperty("门店地址")
    private String storeAddress;
    @ApiModelProperty("门店电话")
    private String tel;
    @ApiModelProperty(
        value = "支付渠道",
        notes = "0:现金，1,聚合2微信"
    )
    private Integer payChannel;
    @ApiModelProperty("门店电话")
    private Boolean isRepeatCallback;

    private String memberName;

    private String memberPhone;
}
