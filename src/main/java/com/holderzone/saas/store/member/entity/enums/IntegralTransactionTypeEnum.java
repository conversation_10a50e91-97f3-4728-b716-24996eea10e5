package com.holderzone.saas.store.member.entity.enums;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/8/17.
 * 会员交易类型枚举
 */
public enum IntegralTransactionTypeEnum {
    PREPAID(1, "充值积分"),
    CONSUME(2, "消费积分"),

    OFFSET(3, "消费抵现"),
    RECOVERY_BACK(4, "反结账退回"),
    RECOVERY_DEDUCTION(5, "反结账扣减"),
    ;

    private int code;
    private String desc;

    private IntegralTransactionTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (IntegralTransactionTypeEnum c : IntegralTransactionTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
