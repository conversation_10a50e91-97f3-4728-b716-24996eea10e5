package com.holderzone.saas.store.member.utils;

import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.ConfigurableApplicationContext;

import java.lang.annotation.Annotation;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SpringContextUtils
 * @date 2018/10/25 14:55
 * @description //TODO
 * @program holder-saas-store-order
 */
public class SpringContextUtils {

    private ConfigurableApplicationContext cfgContext;

    /**
     * 实体对象
     */
    private static final SpringContextUtils INSTANCE = new SpringContextUtils();

    private SpringContextUtils() {
        if (INSTANCE != null) {
            throw new Error("error");
        }
    }

    public static SpringContextUtils getInstance() {
        return INSTANCE;
    }

    /**
     * 防止序列化产生对象
     *
     * @return 防止序列化
     */
    private Object readResolve() {
        return INSTANCE;
    }

    /**
     * 根据名称获取Bean
     *
     * @param name
     * @return
     */
    @SuppressWarnings("unchecked")
    public <T> T getBean(String name) {
        return (T) cfgContext.getBean(name);
    }

    /**
     * 获取bean的名字
     *
     * @param clazz 类型
     * @return bean名字
     */
    @SuppressWarnings("rawtypes")
    public String getBeanName(Class clazz) {
        return cfgContext.getBeanNamesForType(clazz)[0];
    }

    /**
     * 根据类获取Bean
     *
     * @param clazz
     * @return
     */
    public <T> T getBean(Class<T> clazz) {
        return cfgContext.getBean(clazz);
    }

    /**
     * 判断一个bean是否存在Spring容器中.
     *
     * @param clazz 类型
     * @return 成功 true 失败 false
     */
    @SuppressWarnings("rawtypes")
    public boolean existsBean(Class clazz) {
        return cfgContext.containsBean(clazz.getName());
    }

    /**
     * 动态注册一个Bean动Spring容器中
     *
     * @param beanName  名称
     * @param beanClazz 定义bean
     */
    @SuppressWarnings("rawtypes")
    public void registerBean(String beanName, Class beanClazz, Map<String, Object> propertys) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(beanClazz);
        if (propertys != null) {
            propertys.forEach((k, v) -> builder.addPropertyValue(k, v));
        }
        builder.setScope(BeanDefinition.SCOPE_SINGLETON);
        registerBean(beanName, builder.getBeanDefinition());

    }

    /**
     * 动态注册一个Bean动Spring容器中
     *
     * @param beanName
     * @param obj
     */
    public void registerBean(String beanName, Object obj) {
        cfgContext.getBeanFactory().registerSingleton(beanName, obj);
    }

    /**
     * 动态注册一个Bean动Spring容器中
     *
     * @param beanName
     * @param beanDefinition
     */
    public void registerBean(String beanName, BeanDefinition beanDefinition) {
        BeanDefinitionRegistry beanDefinitionRegistry = (BeanDefinitionRegistry) cfgContext.getBeanFactory();
        beanDefinitionRegistry.registerBeanDefinition(beanName, beanDefinition);
    }

    /**
     * 根据枚举类型获取Spring注册的Bean
     *
     * @param annotationType 枚举
     * @return
     */
    public Map<String, Object> getBeanWithAnnotation(Class<? extends Annotation> annotationType) {
        return cfgContext.getBeansWithAnnotation(annotationType);
    }

    /**
     * 动态注册一个Bean动Spring容器中
     *
     * @param beanName  名称
     * @param beanClazz 定义bean
     */
    @SuppressWarnings("rawtypes")
    public void registerBean(String beanName, Class beanClazz) {
        registerBean(beanName, beanClazz, null);
    }

    /**
     * @param cfgContext
     * @return
     * <AUTHOR>
     * @description //TODO
     * @date 2018-07-09 15:47:56
     */
    public void setCfgContext(ConfigurableApplicationContext cfgContext) {
        this.cfgContext = cfgContext;
    }

}
