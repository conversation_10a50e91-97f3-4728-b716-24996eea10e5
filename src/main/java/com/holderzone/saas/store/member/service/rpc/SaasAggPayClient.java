package com.holderzone.saas.store.member.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.pay.*;
import feign.hystrix.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SaasAggPayClient
 * @date 2019/03/18 11:42
 * @description
 * @program holder-saas-store-member
 */
@Component
@FeignClient(value = "holder-saas-store-pay", fallbackFactory = SaasAggPayClient.SaasAggPayClientFallBack.class)
public interface SaasAggPayClient {

    @PostMapping("agg/pay")
    AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO);

    @PostMapping("agg/wechat/public")
    String weChatPublic(SaasAggWeChatPublicAccountPayDTO publicAccountPayDTO);

    @PostMapping("agg/query")
    AggPayPollingRespDTO query(SaasPollingDTO saasPollingDTO);

    @Component
    class SaasAggPayClientFallBack implements FallbackFactory<SaasAggPayClient> {
        @Override
        public SaasAggPayClient create(Throwable throwable) {
            return new SaasAggPayClient() {
                @Override
                public AggPayRespDTO pay(SaasAggPayDTO saasAggPayDTO) {
                    throw new BusinessException("调用交易中心异常");
                }

                @Override
                public String weChatPublic(SaasAggWeChatPublicAccountPayDTO publicAccountPayDTO) {
                    throw new BusinessException("调用交易中心异常");
                }

                @Override
                public AggPayPollingRespDTO query(SaasPollingDTO saasPollingDTO) {
                    return AggPayPollingRespDTO.errorResp("10005", "查询交易中心异常");
                }
            };
        }
    }

}
