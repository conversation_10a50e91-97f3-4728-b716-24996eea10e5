package com.holderzone.saas.store.member.mapper;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.member.request.MemberIntegralReqDTO;
import com.holderzone.saas.store.dto.member.response.MemberIntegralRecordRespDTO;
import com.holderzone.saas.store.member.domain.IntegralRecordDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IntegralRecordMapper {
    int deleteByPrimaryKey(String integralRecordGuid);

    int insert(IntegralRecordDO record);

    int insertSelective(IntegralRecordDO record);

    IntegralRecordDO selectByPrimaryKey(String integralRecordGuid);

    int updateByPrimaryKeySelective(IntegralRecordDO record);

    int updateByPrimaryKey(IntegralRecordDO record);

    List<IntegralRecordDO> selectMemberIntegral(Page<MemberIntegralRecordRespDTO> page, @Param("memberIntegralReqDTO") MemberIntegralReqDTO memberIntegralReqDTO);

    List<IntegralRecordDO> selectRecordByOrderGuid(String orderGuid);

    int batchInsert(List<IntegralRecordDO> historyIntegralRecordDo);
}