package com.holderzone.saas.store.member.service;

import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.saas.store.dto.member.MemberDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SendMessageService
 * @date 2018/11/01 11:18
 * @description //短信发送服务调用
 * @program holder-saas-store-member
 */
public interface SendMessageService {
    void sendMessage(MessageDTO m1,String entGuid,Integer type) throws Exception;
}
