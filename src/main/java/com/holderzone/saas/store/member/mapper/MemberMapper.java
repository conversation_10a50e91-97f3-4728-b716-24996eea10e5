package com.holderzone.saas.store.member.mapper;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import com.holderzone.saas.store.dto.member.request.MemberCardsReqDTO;
import com.holderzone.saas.store.dto.member.request.MemberIntegralReqDTO;
import com.holderzone.saas.store.dto.member.request.MemberListReqDTO;
import com.holderzone.saas.store.dto.member.request.MemberPayRecordReqDTO;
import com.holderzone.saas.store.dto.member.response.MemberIntegralRecordRespDTO;
import com.holderzone.saas.store.dto.member.response.MemberListRespDTO;
import com.holderzone.saas.store.dto.member.response.MemberPayRecordRespDTO;
import com.holderzone.saas.store.member.domain.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MemberMapper {
    int deleteByPrimaryKey(String memberGuid);

    int insert(MemberDO record);

    int insertSelective(MemberDO record);

    MemberDO selectByPrimaryKey(String memberGuid);

    int updateByPrimaryKeySelective(MemberDO record);

    int updateByPrimaryKey(MemberDO record);

    String getPasswordByPhone(String phone);

    MemberDO getMemberByPhone(String phone);

    MemberDO getMemberByGuid(String memberGuid);

    MemberDO getMemberByGuidForUpdate(String memberGuid);

    MemberDO getMemberByPhoneAndPasswd(@Param("phone") String phone, @Param("password") String password);

    List<MemberGradeMeberReadDO> selectMemberList(Page<MemberListRespDTO> result, @Param
            ("memberListReqDTO")MemberListReqDTO memberListReqDTO);

    MemberDetailReadDO getMemberDetailByGuid(String memberGuid);


    List<MemberGradeMeberReadDO> AllMemberList(@Param("memberListReqDTO") MemberListReqDTO memberListReqDTO);

    MemberCardReadDO getMemberByPhoneOrCardNum(String phone);

    int selectMemberByMemberGuid(@Param("memberGradeGuid") String memberGradeGuid);
}