package com.holderzone.saas.store.member.mapstruct;

import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.dto.pay.SaasAggPayDTO;
import com.holderzone.saas.store.dto.pay.SaasAggWeChatPublicAccountPayDTO;
import com.holderzone.saas.store.member.entity.member.RechargeReqDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2019/6/19 17:11
 */
@Component
@Mapper(componentModel = "spring")
public interface HsmMemberMapStruct {

    SaasAggPayDTO billPayReqDTO2SaasAggPayDTO(HsmRechargeReqDTO hsmRechargeReqDTO);

    SaasAggWeChatPublicAccountPayDTO billPayReqDTO2SaasAggPayDTO2(HsmRechargeReqDTO hsmRechargeReqDTO);

    RechargeReqDTO hsmRecharge2PlatformRecharge(HsmRechargeReqDTO rechargeReqDTO);
}
