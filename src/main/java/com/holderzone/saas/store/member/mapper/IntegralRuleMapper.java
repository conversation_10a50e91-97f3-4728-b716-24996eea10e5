package com.holderzone.saas.store.member.mapper;

import com.holderzone.saas.store.dto.member.MemberLoginRespDTO;
import com.holderzone.saas.store.member.domain.IntegralRuleDO;
import com.holderzone.saas.store.member.domain.MemberConsumeRuleReadDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IntegralRuleMapper {
    int deleteByPrimaryKey(String integralRuleGuid);

    int insert(IntegralRuleDO record);

    int insertSelective(IntegralRuleDO record);

    IntegralRuleDO selectByPrimaryKey(String integralRuleGuid);

    int updateByPrimaryKeySelective(IntegralRuleDO record);

    int updateByPrimaryKey(IntegralRuleDO record);

    List<IntegralRuleDO> selectByGradeGuid(String memberGradeGuid);

    IntegralRuleDO selectByGetTypeAndGradeGuid(@Param("code") int code, @Param("memberGradeGuid") String memberGradeGuid);

    int insertDefault(IntegralRuleDO io);

    int batchUpdate(@Param(value ="rules") List<IntegralRuleDO> ios);

    int insertSelectiveBatch(@Param(value ="list") List<IntegralRuleDO> ios);
    IntegralRuleDO selectConsumeRule(String memberGradeGuid);

    MemberConsumeRuleReadDO selectMemberConsumeInfoByGuid(String memberGradeGuid);
}