package com.holderzone.saas.store.member.service.impl;

import com.holderzone.saas.store.dto.pay.AggPayNotifyDTO;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.member.domain.MemberChargeDO;
import com.holderzone.saas.store.member.mapper.MemberChargeMapper;
import com.holderzone.saas.store.member.service.MemberChargeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberChargeServiceImpl
 * @date 2019/03/18 15:17
 * @description
 * @program holder-saas-store-member
 */
@Service
public class MemberChargeServiceImpl implements MemberChargeService {

    private final MemberChargeMapper memberChargeMapper;

    @Autowired
    public MemberChargeServiceImpl(MemberChargeMapper memberChargeMapper) {
        this.memberChargeMapper = memberChargeMapper;
    }

    @Override
    public void insert(MemberChargeDO memberChargeDo) {
        memberChargeMapper.insert(memberChargeDo);
    }

    @Override
    public void updateByTradingCallBack(AggPayPollingRespDTO aggPayPollingRespDTO, LocalDate businessDay) {
        memberChargeMapper.updateByTradingCallBack(aggPayPollingRespDTO, businessDay);
    }

    @Override
    public void updateByAggPayNotify(AggPayNotifyDTO aggPayNotifyDTO) {
        memberChargeMapper.updateByAggPayNotify(aggPayNotifyDTO);
    }
}
