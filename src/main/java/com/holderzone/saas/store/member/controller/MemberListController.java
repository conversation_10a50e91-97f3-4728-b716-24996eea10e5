package com.holderzone.saas.store.member.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import com.holderzone.saas.store.dto.member.request.*;
import com.holderzone.saas.store.dto.member.response.*;
import com.holderzone.saas.store.member.entity.constant.RedisConstant;
import com.holderzone.saas.store.member.service.MemberListService;
import com.holderzone.saas.store.member.utils.DynamicHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/8/21.
 */
@RestController
@RequestMapping("/member_list")
@Api(description = "web端会员数据接口")
public class MemberListController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(MemberListController.class);

    @Autowired
    private MemberListService memberListService;
    @Autowired
    private DynamicHelper helper;

    @ApiOperation(value = "获取会员列表", notes = "获取会员列表")
    @ApiImplicitParam(name = "memberListReqDTO", value = "获取满足查询条件的会员实体", required = true, dataType =
            "memberListReqDTO")
    @PostMapping(value = "/getMemberList", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<MemberListRespDTO> memberList(@RequestBody MemberListReqDTO memberListReqDTO) {
        if (logger.isInfoEnabled()) {
            logger.info("接口路径:/member_list/getMemberList,获取会员列表，memberListReqDTO={}", JacksonUtils.writeValueAsString(memberListReqDTO));
        }
        return memberListService.findMemberList(memberListReqDTO);
    }


    @ApiOperation(value = "获取会员详情", notes = "获取会员详情")
    @ApiImplicitParam(name = "baseMemberDTO", value = "根据GUID获取会员详情", required = true, dataType =
            "baseMemberDTO")
    @PostMapping(value = "/memberDetail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public MemberDetailRespDTO memberList(@RequestBody BaseMemberDTO baseMemberDTO) {
        if (logger.isInfoEnabled()) {
            logger.info("接口路径:/member_list/memberDetail,获取会员详情，baseMemberDTO={}", JacksonUtils.writeValueAsString(baseMemberDTO));
        }
        return memberListService.getMemberDetailByGuid(baseMemberDTO);
    }


    @ApiOperation(value = "查询会员卡", notes = "查询会员卡")
    @ApiImplicitParam(name = "memberCardsReqDTO", value = "根据卡号和Guid查询会员卡", required = true, dataType =
            "memberCardsReqDTO")
    @PostMapping(value = "/memberCards", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<MemberCardsRespDTO> memberCards(@RequestBody MemberCardsReqDTO memberCardsReqDTO) {

        if (logger.isInfoEnabled()) {
            logger.info("接口路径:/member_list/memberCards,获取会员卡，memberCardsReqDTO={}", JacksonUtils.writeValueAsString(memberCardsReqDTO));
        }

        return memberListService.getMemberCards(memberCardsReqDTO);
    }


    @ApiOperation(value = "查询会员等级", notes = "查询会员等级")
    @ApiImplicitParam(name = "baseDTO", value = "所有会员等级", required = true, dataType =
            "baseDTO")
    @PostMapping(value = "/memberGrades", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<MemberGradeListDTO> memberGrades(@RequestBody BaseDTO baseDTO) {
        if (logger.isInfoEnabled()) {
            logger.info("接口路径:/member_list/memberGrades,所有会员等级，baseDTO={}", JacksonUtils.writeValueAsString(baseDTO));
        }

        return memberListService.memberGradeList(baseDTO);

    }


    @ApiOperation(value = "获取会员交易记录", notes = "获取会员交易记录")
    @ApiImplicitParam(name = "memberPayRecordReqDTO", value = "获取会员交易记录", required = true, dataType =
            "memberPayRecordReqDTO")
    @PostMapping(value = "/getMemberTransactionRecords", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<MemberPayRecordRespDTO> memberPayRecords(@RequestBody MemberPayRecordReqDTO memberPayRecordReqDTO) {
        if (logger.isInfoEnabled()) {
            logger.info("接口路径:/member_list/getMemberTransactionRecords,获取会员交易记录，memberPayRecordReqDTO={}", JacksonUtils.writeValueAsString(memberPayRecordReqDTO));
        }
        return memberListService.getTransactionRecord(memberPayRecordReqDTO);
    }


    @ApiOperation(value = "获取会员积分类型", notes = "获取会员积分类型")
    @ApiImplicitParam(name = "baseDTO", value = "获取会员积分类型", required = true, dataType =
            "baseDTO")
    @PostMapping(value = "/getMemberIntegralType", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<MemberIntegralTypeRespDTO> getMemberType(@RequestBody BaseDTO baseDTO) {
        if (logger.isInfoEnabled()) {
            logger.info("接口路径:/member_list/getMemberIntegralType,获取会员积分类型，baseDTO={}", JacksonUtils.writeValueAsString(baseDTO));
        }
        return memberListService.getMemberIntegralType(baseDTO);
    }


    @ApiOperation(value = "会员积分记录", notes = "会员积分记录")
    @ApiImplicitParam(name = "MemberIntegralReqDTO", value = "获取会员积分记录", required = true, dataType =
            "MemberIntegralReqDTO")
    @PostMapping(value = "/getMemberIntegralRecords", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Page<MemberIntegralRecordRespDTO> memberIntegralRecords(@RequestBody MemberIntegralReqDTO memberIntegrals) {
        if (logger.isInfoEnabled()) {
            logger.info("接口路径:/member_list/getMemberIntegralRecords,获取会员积分记录，memberIntegrals={}", JacksonUtils.writeValueAsString(memberIntegrals));
        }
        return memberListService.getmemberIntegralRecords(memberIntegrals);
    }


    @ApiOperation(value = "获取会员列表不分页", notes = "获取会员列表不分页")

    @PostMapping(value = "/getMemberListwithNoPage", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<MemberListRespDTO> AllmemberList(@RequestBody MemberListReqDTO memberListReqDTO) {
        if (logger.isInfoEnabled()) {
            logger.info("接口路径:/member_list/getMemberListwithNoPage,获取会员列表不分页，memberListReqDTO={}", JacksonUtils.writeValueAsString(memberListReqDTO));
        }

        return memberListService.AllMemberList(memberListReqDTO);
    }
}
