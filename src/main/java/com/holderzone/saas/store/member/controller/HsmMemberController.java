package com.holderzone.saas.store.member.controller;

import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.member.hsm.HsmAggPayRespDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.dto.pay.CardRechargeCashInfoQO;
import com.holderzone.saas.store.dto.pay.SaasNotifyDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.member.service.HsmMemberService;
import com.holderzone.saas.store.member.utils.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/8/21.
 */
@Slf4j
@RestController
@RequestMapping("/hsm_member")
@Api(description = "新会员接口")
public class HsmMemberController extends BaseController {

    private final HsmMemberService hsmMemberService;
    private final RedisUtil redisUtil;

    @Autowired
    public HsmMemberController(HsmMemberService hsmMemberService, RedisUtil redisUtil) {
        this.hsmMemberService = hsmMemberService;
        this.redisUtil = redisUtil;
    }

    @ApiOperation(value = "会员充值接口", notes = "会员充值接口")
    @PostMapping(value = "/recharge", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public HsmAggPayRespDTO recharge(@RequestBody HsmRechargeReqDTO hsmRechargeReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("会员充值接口入参：{}", JacksonUtils.writeValueAsString(hsmRechargeReqDTO));
        }
        return hsmMemberService.recharge(hsmRechargeReqDTO);
    }

    @ApiOperation(value = "充值重打印接口", notes = "充值重打印接口")
    @PostMapping(value = "/print_recharge", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean printRecharge(@RequestBody CardRechargeCashInfoQO cardRechargeCashInfoQO) {
        return hsmMemberService.printRecharge(cardRechargeCashInfoQO);
    }

    @ApiOperation(value = "会员充值接口（公众号）", notes = "会员充值接口（公众号）")
    @PostMapping(value = "/wx_recharge", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public WxPayRespDTO wxRecharge(@RequestBody HsmRechargeReqDTO hsmRechargeReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("会员充值接口（公众号）入参：{}", JacksonUtils.writeValueAsString(hsmRechargeReqDTO));
        }
        return hsmMemberService.wechatRecharge(hsmRechargeReqDTO);
    }

    @ApiOperation(value = "会员充值接口", notes = "会员充值接口", response = Boolean.class)
    @PostMapping(value = "/callback", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String callback(@RequestBody SaasNotifyDTO saasNotifyDTO) {
        if (log.isInfoEnabled()) {
            log.info("会员充值接口入参：{}", JacksonUtils.writeValueAsString(saasNotifyDTO));
        }
        EnterpriseIdentifier.setEnterpriseGuid(saasNotifyDTO.getBaseInfo().getEnterpriseGuid());
        String lockKey = "memberPayCallback:";
        boolean lockSuccess = false;
        try {
            lockKey = lockKey + saasNotifyDTO.getAggPayPollingRespDTO().getOrderGUID();
            lockSuccess = redisUtil.setNx(lockKey, "1", 20);
            if (!lockSuccess) {
                log.info("callback会员充值接口重复回调！");
                return null;
            }
            return hsmMemberService.callback(saasNotifyDTO);
        } finally {
            if (lockSuccess) {
                redisUtil.delete(lockKey);
            }
        }
    }

    @ApiOperation(value = "会员充值撤销接口", notes = "会员充值撤销接口", response = Boolean.class)
    @PostMapping(value = "/hsm_member/revoke_charge")
    public Integer revokeCharge(@RequestBody SaasPollingDTO saasPollingDTO){
        if (log.isInfoEnabled()) {
            log.info("会员充值撤销接口入参：{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        }
        return hsmMemberService.revokeCharge(saasPollingDTO);
    }
}
