package com.holderzone.saas.store.member.domain;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * hsm_integral_record
 * <AUTHOR>
public class IntegralRecordDO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    private Boolean isDelete;

    /**
     * 业务主键
     */
    private String integralRecordGuid;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 交易时间
     */
    private LocalDateTime transactionTime;


    /**
     * 营业日 2018-9-30新增字段
     */
    private LocalDate businessDay;
    /**
     * 剩余积分
     */
    private Integer residualIntegral;

    /**
     * 交易积分
     */
    private Integer transactionIntegral;

    /**
     * 交易类型 （IntegralTransactionTypeEnum）
     */
    private Integer type;

    /**
     * 会员账号
     */
    private String accountNumber;

    /**
     * 业务主键
     */
    private String memberGuid;



    /**

     * 操作人guid
     */
    private String staffGuid;

    /**
     * 操作人姓名
     */
    private String staffName;


    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Boolean getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    public String getIntegralRecordGuid() {
        return integralRecordGuid;
    }

    public void setIntegralRecordGuid(String integralRecordGuid) {
        this.integralRecordGuid = integralRecordGuid;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public LocalDateTime getTransactionTime() {
        return transactionTime;
    }

    public void setTransactionTime(LocalDateTime transactionTime) {
        this.transactionTime = transactionTime;
    }

    public Integer getResidualIntegral() {
        return residualIntegral;
    }

    public void setResidualIntegral(Integer residualIntegral) {
        this.residualIntegral = residualIntegral;
    }

    public Integer getTransactionIntegral() {
        return transactionIntegral;
    }

    public void setTransactionIntegral(Integer transactionIntegral) {
        this.transactionIntegral = transactionIntegral;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getMemberGuid() {
        return memberGuid;
    }

    public void setMemberGuid(String memberGuid) {
        this.memberGuid = memberGuid;
    }

    public String getStaffGuid() {
        return staffGuid;
    }

    public void setStaffGuid(String staffGuid) {
        this.staffGuid = staffGuid;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public LocalDate getBusinessDay() {
        return businessDay;
    }

    public void setBusinessDay(LocalDate businessDay) {
        this.businessDay = businessDay;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IntegralRecordDO other = (IntegralRecordDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtModified() == null ? other.getGmtModified() == null : this.getGmtModified().equals(other.getGmtModified()))
            && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()))
            && (this.getIntegralRecordGuid() == null ? other.getIntegralRecordGuid() == null : this.getIntegralRecordGuid().equals(other.getIntegralRecordGuid()))
            && (this.getOrderNo() == null ? other.getOrderNo() == null : this.getOrderNo().equals(other.getOrderNo()))
            && (this.getTransactionTime() == null ? other.getTransactionTime() == null : this.getTransactionTime().equals(other.getTransactionTime()))
                && (this.getBusinessDay() == null ? other.getBusinessDay()== null : this.getBusinessDay().equals(other.getBusinessDay()))
            && (this.getResidualIntegral() == null ? other.getResidualIntegral() == null : this.getResidualIntegral().equals(other.getResidualIntegral()))
            && (this.getTransactionIntegral() == null ? other.getTransactionIntegral() == null : this.getTransactionIntegral().equals(other.getTransactionIntegral()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getAccountNumber() == null ? other.getAccountNumber() == null : this.getAccountNumber().equals(other.getAccountNumber()))
            && (this.getMemberGuid() == null ? other.getMemberGuid() == null : this.getMemberGuid().equals(other.getMemberGuid()))
            && (this.getStaffGuid() == null ? other.getStaffGuid() == null : this.getStaffGuid().equals(other.getStaffGuid()))
            && (this.getStaffName() == null ? other.getStaffName() == null : this.getStaffName().equals(other.getStaffName()));

    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtModified() == null) ? 0 : getGmtModified().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        result = prime * result + ((getIntegralRecordGuid() == null) ? 0 : getIntegralRecordGuid().hashCode());
        result = prime * result + ((getOrderNo() == null) ? 0 : getOrderNo().hashCode());
        result = prime * result + ((getTransactionTime() == null) ? 0 : getTransactionTime().hashCode());
        result = prime * result + ((getBusinessDay() == null) ? 0 : getBusinessDay().hashCode());
        result = prime * result + ((getResidualIntegral() == null) ? 0 : getResidualIntegral().hashCode());
        result = prime * result + ((getTransactionIntegral() == null) ? 0 : getTransactionIntegral().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getAccountNumber() == null) ? 0 : getAccountNumber().hashCode());
        result = prime * result + ((getMemberGuid() == null) ? 0 : getMemberGuid().hashCode());
        result = prime * result + ((getStaffGuid() == null) ? 0 : getStaffGuid().hashCode());
        result = prime * result + ((getStaffName() == null) ? 0 : getStaffName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", isDelete=").append(isDelete);
        sb.append(", integralRecordGuid=").append(integralRecordGuid);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", transactionTime=").append(transactionTime);
        sb.append(", businessDay=").append(businessDay);
        sb.append(", residualIntegral=").append(residualIntegral);
        sb.append(", transactionIntegral=").append(transactionIntegral);
        sb.append(", type=").append(type);
        sb.append(", accountNumber=").append(accountNumber);
        sb.append(", memberGuid=").append(memberGuid);
        sb.append(", staffGuid=").append(staffGuid);
        sb.append(", staffName=").append(staffName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }


}