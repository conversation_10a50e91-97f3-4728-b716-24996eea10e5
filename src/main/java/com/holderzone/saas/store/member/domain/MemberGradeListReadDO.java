package com.holderzone.saas.store.member.domain;

import com.holderzone.saas.store.dto.member.response.MemberGradeListDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberGradeListReadDO
 * @date 2018/09/28 16:42
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class MemberGradeListReadDO  {
    @ApiModelProperty(value = "会员等级Guid")
    private String memberGradeGuid;
    @ApiModelProperty(value = "会员等级名称")
    private String name;
}
