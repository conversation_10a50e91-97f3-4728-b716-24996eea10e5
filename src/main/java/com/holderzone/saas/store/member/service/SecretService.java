package com.holderzone.saas.store.member.service;

import com.holderzone.framework.response.LogicResponse;
import com.holderzone.saas.store.dto.member.MemberCheckDTO;
import com.holderzone.saas.store.member.entity.bo.SecretBO;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/8/22.
 */
public interface SecretService {
    SecretBO getPublicKeyAndRand();

    LogicResponse<String> check(MemberCheckDTO memberCheckDTO);

    String getPassWord(String secretPassword);
}
