package com.holderzone.saas.store.member.entity.bo;

import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.resource.common.dto.enterprise.DeductShortMessageDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TransactionPrintBO
 * @date 2018/10/18 10:44
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class TransactionPrintBO {
    //余额
    private BigDecimal balance;
    //电子卡号
    private String cardNum;
    //打印时间
    private LocalDateTime printTime;
    //剩余积分
    private Integer Residualintegral;
    //充值时间
    private LocalDateTime PrepaidTime;

    private String StoreGuid;
    private String StoreName;
    private String StoreAddres;
    private String StoreTel;


    private MessageDTO  messageDTO;

    private List<DeductShortMessageDTO> dlist;



}
