package com.holderzone.saas.store.member.controller;

import com.holderzone.framework.event.publish.impl.CustomerPublishImpl;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.member.MemberLoginDTO;
import com.holderzone.saas.store.dto.member.MemberLoginRespDTO;
import com.holderzone.saas.store.dto.member.MemberNotifyDTO;
import com.holderzone.saas.store.dto.member.request.HandoverReqDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO;
import com.holderzone.saas.store.dto.order.response.daily.MemberConsumeRespDTO;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import com.holderzone.saas.store.member.entity.bo.MemberPayBO;
import com.holderzone.saas.store.member.entity.bo.TransactionPrintBO;
import com.holderzone.saas.store.member.entity.transform.MemberTransform;
import com.holderzone.saas.store.member.service.MemberService;
import com.holderzone.saas.store.member.service.MemberTransactionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TransactionController
 * @date 2018/08/27 上午11:18
 * @description //会员卡交易相关接口
 * @program holder-saas-config-center
 */
@RestController
@RequestMapping("/transaction")
@Api(description = "会员卡交易相关接口")
public class TransactionController {

    private static final Logger logger = LoggerFactory.getLogger(TransactionController.class);

    @Autowired
    private MemberTransactionService memberTransactionService;

    @Autowired
    private MemberService memberService;

    @Autowired
    private CustomerPublishImpl customerPublish;

    private MemberTransform memberTransform = MemberTransform.INSTANCE;

    @ApiOperation(value = "支付前获取会员信息", notes = "支付前同过电话号码获取会员信息（返回带密码）", response = Result.class)
    @PostMapping(value = "/get_member", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public MemberLoginRespDTO getMember(@RequestBody MemberLoginDTO memberLoginDTO) {
        if (logger.isInfoEnabled()) {
            logger.info("接口路径:/transaction/get_member,获取会员列表不分页，memberLoginDTO={}", JacksonUtils.writeValueAsString
                    (memberLoginDTO));

        }
        return memberService.getMemberLoginResByPhone(memberLoginDTO.getPhone());
    }

    @ApiOperation(value = "会员卡支付", notes = "会员卡支付", response = Result.class)
    @PostMapping(value = "/pay", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Boolean pay(@RequestBody MemberNotifyDTO memberNotifyDTO) {
        if (logger.isInfoEnabled()) {
            logger.info("会员卡支付入参", JacksonUtils.writeValueAsString(memberNotifyDTO));
        }
        if (memberNotifyDTO.getType() == 1 && memberNotifyDTO.getConsumeType() == 0 && !memberTransactionService.checkBalance(memberNotifyDTO)) {
            throw new ParameterException("会员余额不足");
        }
        TransactionPrintBO transactionPrintBO = memberTransactionService.addTransactionRecord(memberNotifyDTO);
        MemberPayBO memberPayBO = new MemberPayBO();
        BaseInfo baseInfo = memberTransform.memberNotifyDTO2BaseInfo(memberNotifyDTO);
        memberPayBO.setBs(baseInfo);
        memberPayBO.setEntGuid(memberNotifyDTO.getEnterpriseGuid() == null ? baseInfo.getEnterpriseGuid() :
                memberNotifyDTO
                        .getEnterpriseGuid());
        memberPayBO.setMemberNotifyDTO(memberNotifyDTO);
        memberPayBO.setTransactionPrintBO(transactionPrintBO);
        memberPayBO.setMessageDTO(transactionPrintBO.getMessageDTO());
        memberPayBO.setType(memberNotifyDTO.getType());
        memberPayBO.setStoreGuid(memberNotifyDTO.getStoreGuid());
//        if (transactionPrintBO.getMessageDTO() != null) {
//            customerPublish.publish(new CustomerEvent<>(memberPayBO));
//        }
        return Boolean.TRUE;
    }


    @ApiOperation(value = "会员充值统计", notes = "商户获取会员充值统计记录", response = Result.class)
    @PostMapping(value = "/get_member_recharge_record", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public MemberConsumeRespDTO getMemberRechargeRecord(@RequestBody @Valid DailyReqDTO request) {
        return memberTransactionService.getMemberRechargeRecord(request);
    }


    @ApiOperation(value = "交接班-会员充值统计", notes = "交接班-会员充值收入和充值订单数统计", response = Result.class)
    @PostMapping(value = "/get_member_recharge_store", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public MemberConsumeRespDTO getMemberRechargeStore(@RequestBody @Valid HandoverReqDTO request) {
        return memberTransactionService.getMemberRechargeStore(request);
    }

    @ApiOperation(value = "营业日报-收入概况-会员充值收入", notes = "营业日报-收入概况-会员充值收入统计", response = Result.class)
    @PostMapping(value = "/get_business_member_recharge_earnings", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public List<GatherRespDTO> getBusinessMemberRechargeEarnings(@RequestBody @Valid DailyReqDTO request) {
        return memberTransactionService.getBusinessMemberRechargeEarnings(request);
    }


}
