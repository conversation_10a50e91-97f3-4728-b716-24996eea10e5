package com.holderzone.saas.store.member.entity.bo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberBO
 * @date 2018/08/25 下午3:20
 * @description //TODO
 * @program holder-saas-config-center
 */
public class MemberBO extends BaseMemberBO {


    /**
     * 会员有效期（开始日期）
     */
    private LocalDate startDate;

    /**
     * 会员有效期（截止日期）
     */
    private LocalDate expiryDate;

    /**
     * 最近登录日期
     */
    private LocalDateTime recentlyLoginDate;

    /**
     * 最近消费日期
     */
    private LocalDateTime recentlyConsumeDate;

    /**
     * 会员等级的业务主键
     */
    private String memberGradeGuid;

    /**
     * 累计消费次数
     */
    private Integer totalConsumeNum;

    /**
     * 累计充值次数
     */
    private Integer totalPrepaidNum;

    /**
     * 累计积分
     */
    private Integer totalIntegral;


    /**
     * 累计消费金额（全部消费）
     */
    private BigDecimal totalConsumeFee;

    /**
     * 累计支付金额（余额支付）
     */
    private BigDecimal totalPayFee;

    /**
     * 累计充值金额
     */
    private BigDecimal totalPrepaidFee;

    /**
     * 累计赠送金额
     */
    private BigDecimal totalPresentFee;

    /**
     * 每积分抵扣多少钱
     */
    private BigDecimal MoneyDiscountPerScore;

    /**
     * 会员权益折扣
     */
    private BigDecimal memberDiscount;

    public BigDecimal getMoneyDiscountPerScore() {
        return MoneyDiscountPerScore;
    }

    public void setMoneyDiscountPerScore(BigDecimal moneyDiscountPerScore) {
        MoneyDiscountPerScore = moneyDiscountPerScore;
    }

    public BigDecimal getMemberDiscount() {
        return memberDiscount;
    }

    public void setMemberDiscount(BigDecimal memberDiscount) {
        this.memberDiscount = memberDiscount;
    }


    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(LocalDate expiryDate) {
        this.expiryDate = expiryDate;
    }

    public LocalDateTime getRecentlyLoginDate() {
        return recentlyLoginDate;
    }

    public void setRecentlyLoginDate(LocalDateTime recentlyLoginDate) {
        this.recentlyLoginDate = recentlyLoginDate;
    }

    public LocalDateTime getRecentlyConsumeDate() {
        return recentlyConsumeDate;
    }

    public void setRecentlyConsumeDate(LocalDateTime recentlyConsumeDate) {
        this.recentlyConsumeDate = recentlyConsumeDate;
    }

    public String getMemberGradeGuid() {
        return memberGradeGuid;
    }

    public void setMemberGradeGuid(String memberGradeGuid) {
        this.memberGradeGuid = memberGradeGuid;
    }

    public Integer getTotalConsumeNum() {
        return totalConsumeNum;
    }

    public void setTotalConsumeNum(Integer totalConsumeNum) {
        this.totalConsumeNum = totalConsumeNum;
    }

    public Integer getTotalPrepaidNum() {
        return totalPrepaidNum;
    }

    public void setTotalPrepaidNum(Integer totalPrepaidNum) {
        this.totalPrepaidNum = totalPrepaidNum;
    }

    public Integer getTotalIntegral() {
        return totalIntegral;
    }

    public void setTotalIntegral(Integer totalIntegral) {
        this.totalIntegral = totalIntegral;
    }

    public BigDecimal getTotalConsumeFee() {
        return totalConsumeFee;
    }

    public void setTotalConsumeFee(BigDecimal totalConsumeFee) {
        this.totalConsumeFee = totalConsumeFee;
    }

    public BigDecimal getTotalPayFee() {
        return totalPayFee;
    }

    public void setTotalPayFee(BigDecimal totalPayFee) {
        this.totalPayFee = totalPayFee;
    }

    public BigDecimal getTotalPrepaidFee() {
        return totalPrepaidFee;
    }

    public void setTotalPrepaidFee(BigDecimal totalPrepaidFee) {
        this.totalPrepaidFee = totalPrepaidFee;
    }

    public BigDecimal getTotalPresentFee() {
        return totalPresentFee;
    }

    public void setTotalPresentFee(BigDecimal totalPresentFee) {
        this.totalPresentFee = totalPresentFee;
    }
}
