package com.holderzone.saas.store.member.service;

import com.holderzone.framework.response.Result;
import com.holderzone.saas.store.dto.member.MemberDTO;
import com.holderzone.saas.store.dto.member.TransactionRecordDTO;
import com.holderzone.saas.store.dto.member.grade.AccountValidityDTO;
import com.holderzone.saas.store.dto.member.grade.MemberGradeDTO;
import com.holderzone.saas.store.member.entity.bo.BaseMemberBO;
import com.sun.org.apache.xpath.internal.operations.Bool;

import java.util.List;


public interface MemberGradeService {
    /**
     * 修改会员账号的有效期
     * @param accountValidityDTO
     * @return
     */
    Boolean updateAccountValidity(AccountValidityDTO accountValidityDTO);

    /**
     * 查询当前账号的有效期
     * @return
     */
    AccountValidityDTO getAccountValidity();

    /**
     * 新增会员等级
     * @param memberGradeDTO
     * @return
     */
    Boolean addMemberGrade(MemberGradeDTO memberGradeDTO);

    /**
     * 修改会员等级
      * @param memberGradeDTO
     * @return
     */
    Boolean updateMemberGrade(MemberGradeDTO memberGradeDTO);

    /**
     * 获取所有会员等级
     * @return
     */
    List<MemberGradeDTO> memberGradeList();

    /**
     * 删除会员等级
     * @param memberGradeDTO
     * @return
     */
    Boolean deleteMemberGrade(MemberGradeDTO memberGradeDTO);

    /**
     * 根据会员等级guid获取对应等级的详情信息
     * @param memberGradeGuid
     * @return
     */
    MemberGradeDTO memberGradeDetail(String memberGradeGuid);
}
