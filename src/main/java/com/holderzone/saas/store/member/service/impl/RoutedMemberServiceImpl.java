package com.holderzone.saas.store.member.service.impl;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import com.holderzone.saas.store.dto.member.MemberLoginRespDTO;
import com.holderzone.saas.store.dto.member.MemberRechargeDTO;
import com.holderzone.saas.store.dto.member.TransactionRecordDTO;
import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import com.holderzone.saas.store.dto.member.request.*;
import com.holderzone.saas.store.dto.member.response.*;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.member.domain.MemberCardDO;
import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;
import com.holderzone.saas.store.member.factory.MemberServiceStrategyFactory;
import com.holderzone.saas.store.member.service.MemberService;
import com.holderzone.saas.store.member.service.MerchantRouteConfigService;
import com.holderzone.saas.store.member.strategy.MemberServiceStrategy;
import com.holderzone.saas.store.member.util.MerchantContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 路由会员服务实现类
 * 根据商户配置路由到不同的会员服务
 * 
 * <AUTHOR>
 * @date 2025/8/8
 */
@Slf4j
@Service("routedMemberService")
public class RoutedMemberServiceImpl implements MemberService {
    
    @Autowired
    private MerchantRouteConfigService merchantRouteConfigService;
    
    @Autowired
    private MemberServiceStrategyFactory memberServiceStrategyFactory;
    
    /**
     * 获取当前请求对应的会员服务策略
     * 
     * @return 会员服务策略
     */
    private MemberServiceStrategy getCurrentStrategy() {
        String entGuid = MerchantContextUtil.getEntGuid();
        String storeGuid = MerchantContextUtil.getStoreGuid();
        
        MemberServiceTypeEnum serviceType = merchantRouteConfigService.getMemberServiceType(entGuid, storeGuid);
        log.info("根据商户信息获取会员服务类型: entGuid={}, storeGuid={}, serviceType={}", 
                entGuid, storeGuid, serviceType.getDescription());
        
        return memberServiceStrategyFactory.getStrategy(serviceType);
    }

    @Override
    public Boolean create(CreatMemberDTO creatMemberDTO) {
        return null;
    }

    @Override
    public String getPasswordByPhone(String phone) {
        return "";
    }

    @Override
    public Boolean validateMemberByPhoneAndPasswd(ModifiPassWdReqDTO modifiPassWdReqDTO) {
        return null;
    }

    @Override
    public BaseMemberRespDTO getMemberByPhone(BaseMemberDTO baseMemberDTO) {
        return null;
    }

    @Override
    public MemberLoginRespDTO getMemberByGuid(String memberGuid) {
        return null;
    }

    @Override
    public MemberLoginRespDTO getMemberLoginResByPhone(String phone) {
        return null;
    }

    @Override
    public Boolean updateMember(UpdateMemberReqDTO updateMemberReqDTO) {
        return null;
    }

    @Override
    public Page<TransactionRecordRespDTO> queryTransactionRecord(TransactionRecordDTO transactionRecordDTO) {
        return null;
    }

    @Override
    public Boolean modifiPassWd(ModifiPassWdReqDTO modifiPassWdReqDTO) {
        return null;
    }

    @Override
    public Boolean forgetPassWd(ForgetPassWdReqDTO forgetPassWdReqDTO) {
        return null;
    }

    @Override
    public Boolean verificationCode(BaseMemberDTO baseMemberDTO) {
        return null;
    }

    @Override
    public MemberCardDO getMemberCard(String memberGuid) {
        return null;
    }

    @Override
    public Boolean InitDefaultMemberData(String entGuid) {
        return null;
    }

    @Override
    public String checkPrepaidRule(MemberRechargeDTO memberRechargeDTO) {
        return "";
    }

    @Override
    public AggPayRespDTO memberCharge(MemberRechargeDTO memberRechargeDTO) {
        return null;
    }

    @Override
    public String saasTradingCallBack(SaasNotifyDTO saasNotifyDTO) {
        return "";
    }

    @Override
    public String aggPayNotify(AggPayNotifyDTO aggPayNotifyDTO) {
        return "";
    }

    @Override
    public List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail) {
        return getCurrentStrategy().queryMemberByPhoneTail(phoneTail);
    }
}
