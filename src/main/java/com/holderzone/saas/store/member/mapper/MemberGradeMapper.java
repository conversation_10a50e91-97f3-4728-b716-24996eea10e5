package com.holderzone.saas.store.member.mapper;

import com.holderzone.saas.store.member.domain.MemberGradeDO;
import com.holderzone.saas.store.member.domain.MemberGradeListReadDO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MemberGradeMapper {
    int deleteByPrimaryKey(String memberGradeGuid);

    int insert(MemberGradeDO record);

    int insertSelective(MemberGradeDO record);

    MemberGradeDO selectByPrimaryKey(String memberGradeGuid);

    int updateByPrimaryKeySelective(MemberGradeDO record);

    int updateByPrimaryKey(MemberGradeDO record);

    String getNameById(String memberGradeGuid);

    MemberGradeDO getByMemberGradeGuid(String memberGradeGuid);

    List<String> selectDefult();

    int deleteMemberGrade(String memberGradeGuid);

    MemberGradeDO getByName(String name);

    List<MemberGradeDO> selectAllMemberGrade();

    List<MemberGradeListReadDO> getAllMemberGrade();
}