package com.holderzone.saas.store.member.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberChargeDO
 * @date 2019/03/18 14:25
 * @description
 * @program holder-saas-store-member
 */
@Data
public class MemberChargeDO {

    private String guid;

    private String sequenceNo;

    private Integer payPowerId;

    private String aggPayOrderNo;

    private String aggPayGuid;

    private String storeGuid;

    private String storeName;

    private String bankTransactionId;

    private Integer state;

    private BigDecimal amount;

    private Integer paymentType;

    private String paymentTypeName;

    private String memberGuid;

    private String memberName;

    private String telNo;

    private LocalDate businessDay;

    private LocalDateTime paidTime;

    private String operationStaffGuid;

    private String operationStaffName;

    private String remark;


}
