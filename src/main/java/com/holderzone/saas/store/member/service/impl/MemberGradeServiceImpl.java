package com.holderzone.saas.store.member.service.impl;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.member.grade.AccountValidityDTO;
import com.holderzone.saas.store.dto.member.grade.MemberGradeDTO;
import com.holderzone.saas.store.dto.orderlog.detail.TakeawayLogDTO;
import com.holderzone.saas.store.member.domain.IntegralRuleDO;
import com.holderzone.saas.store.member.domain.MemberConfigDO;
import com.holderzone.saas.store.member.domain.MemberDO;
import com.holderzone.saas.store.member.domain.MemberGradeDO;
import com.holderzone.saas.store.member.entity.constant.RedisConstant;
import com.holderzone.saas.store.member.entity.enums.ConsumeIntegralRuleTypeEnum;
import com.holderzone.saas.store.member.entity.enums.GetIntegralRuleTypeEnum;
import com.holderzone.saas.store.member.entity.enums.MemberConfigTypeEnum;
import com.holderzone.saas.store.member.entity.transform.MemberTransform;
import com.holderzone.saas.store.member.mapper.IntegralRuleMapper;
import com.holderzone.saas.store.member.mapper.MemberConfigMapper;
import com.holderzone.saas.store.member.mapper.MemberGradeMapper;
import com.holderzone.saas.store.member.mapper.MemberMapper;
import com.holderzone.saas.store.member.service.MemberGradeService;
import com.holderzone.saas.store.member.utils.DynamicHelper;
import com.holderzone.sdk.util.IdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberServiceImpl
 * @date 2018-08-06 11:37:50
 * @description
 * @program holder-saas-store-member
 */
@Service
public class MemberGradeServiceImpl implements MemberGradeService {

    @Autowired
    private MemberMapper memberMapper;

    @Autowired
    private MemberGradeMapper memberGradeMapper;

    @Autowired
    private IntegralRuleMapper integralRuleMapper;

    @Autowired
    private MemberConfigMapper memberConfigMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private DynamicHelper helper;

    private MemberTransform memberTransform = MemberTransform.INSTANCE;

    final BigDecimal DivideNUM=new BigDecimal(10);
    @Override
    public Boolean updateAccountValidity(AccountValidityDTO accountValidityDTO) {
        //查询会员配置
        MemberConfigDO memberConfigDO = memberConfigMapper.selectByConfigKey(MemberConfigTypeEnum.MEMBER_VALIDITY
                .getCode());
        //没有采用默认配置
        if (memberConfigDO == null) {
            MemberConfigDO memberConfigDO1 = new MemberConfigDO();
            String memberConfigGuid = helper.generateGuid(RedisConstant.MEMBER_CONFIG_GUID);
            memberConfigDO1.setMemberConfigGuid(memberConfigGuid);
            memberConfigDO1.setKey(MemberConfigTypeEnum.MEMBER_VALIDITY.getCode());
            memberConfigDO1.setValue(JacksonUtils.writeValueAsString(accountValidityDTO));
            memberConfigDO1.setDesc("有效期配置，value实体类MemberExpiryConfig");
            memberConfigMapper.insertSelective(memberConfigDO1);
        } else {
            memberConfigDO.setValue(JacksonUtils.writeValueAsString(accountValidityDTO));
            memberConfigMapper.updateByPrimaryKeySelective(memberConfigDO);
        }
        return Boolean.TRUE;
    }

    @Override
    public AccountValidityDTO getAccountValidity() {
        MemberConfigDO memberConfigDO = memberConfigMapper.selectByConfigKey(MemberConfigTypeEnum.MEMBER_VALIDITY
                .getCode());
        AccountValidityDTO accountValidityDTO = new AccountValidityDTO();
        if (memberConfigDO == null) {
            accountValidityDTO.setExpiryType((byte) 0);
        }

        if (memberConfigDO != null) {
            return JacksonUtils.toObject(AccountValidityDTO.class, memberConfigDO.getValue());
        }
        return accountValidityDTO;
    }

    @Override
    @Transactional
    public Boolean addMemberGrade(MemberGradeDTO memberGradeDTO) {
        if (memberGradeDTO.getDiscount().compareTo(DivideNUM)>0||memberGradeDTO.getDiscount().compareTo(BigDecimal.ZERO)<0){
            throw new ParameterException("权益折扣超出范围");
        }
        if (memberGradeDTO.getIsDefault().equals((byte) 1)) {
            throw new ParameterException("不允许添加默认等级");
        }
        if (memberGradeDTO.getName().indexOf(" ")!=-1){
            throw new ParameterException("等级名称不可含有空格");
        }
        if (memberGradeDTO.getName().length()>8){
            throw  new ParameterException("等级名称过长");
        }
        if (memberGradeMapper.getByName(memberGradeDTO.getName())!=null){
            throw  new ParameterException("等级名称重复");
        }
        //生产会员Guid
        String memberGradeGuid = helper.generateGuid(RedisConstant
                .MEMBER_GUID);

        List<MemberGradeDO> memberGradeDOS = memberGradeMapper.selectAllMemberGrade();
        for (MemberGradeDO m:
             memberGradeDOS) {
            if(memberGradeDTO.getNeedIntegral()<=(m.getNeedIntegral())){
                throw new ParameterException("该会员等级所需的积分不能小于等于之前的会员等级");
            }
        }
        MemberGradeDO memberGradeDO = memberTransform.memberGradeDTO2DO(memberGradeDTO);
        memberGradeDO.setMemberGradeGuid(memberGradeGuid);
        memberGradeDO.setDiscount(memberGradeDTO.getDiscount());
        if (memberGradeDTO.getPrepaidLimit().size()!=0||memberGradeDTO.getPrepaidLimit()!=null){
            memberGradeDO.setPrepaidLimit(JSON.toJSONString(memberGradeDTO.getPrepaidLimit()));
        }

        memberGradeMapper.insertSelective(memberGradeDO);
        List<IntegralRuleDO> ios=new ArrayList<>();
        List<MemberGradeDTO.IntegralRuleDTO> integralRuleDTOS = memberGradeDTO.getIntegralRuleDTOS();
        for (MemberGradeDTO.IntegralRuleDTO integralRuleDTO : integralRuleDTOS) {
            IntegralRuleDO integralRuleDO = memberTransform.integralRuleDTO2DO(integralRuleDTO);
            integralRuleDO.setMemberGradeGuid(memberGradeGuid);
            String integralRuleGuid = helper.generateGuid(RedisConstant
                    .INTEGRAL_RULE_GUID);
            integralRuleDO.setIntegralRuleGuid(integralRuleGuid);
           ios.add(integralRuleDO);
        }
        integralRuleMapper.insertSelectiveBatch(ios);
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateMemberGrade(MemberGradeDTO memberGradeDTO) {
        if (memberGradeDTO.getDiscount().compareTo(DivideNUM)>0||memberGradeDTO.getDiscount().compareTo(BigDecimal.ZERO)<0){
            throw new ParameterException("权益折扣超出范围");
        }
        if (memberGradeDTO.getName().indexOf(" ")!=-1){
            throw new ParameterException("等级名称不可含有空格");
        }
        if (memberGradeDTO.getName().length()>8){
            throw  new ParameterException("等级名称过长");
        }
        MemberGradeDO byName1 = memberGradeMapper.getByName(memberGradeDTO.getName());
        if (byName1!=null&&!memberGradeDTO.getMemberGradeGuid().equals(byName1.getMemberGradeGuid())){
            throw  new ParameterException("等级名称重复");
        }
        List<MemberGradeDO> memberGradeDOS = memberGradeMapper.selectAllMemberGrade();
        MemberGradeDO memberGradeDO = memberGradeMapper.selectByPrimaryKey(memberGradeDTO.getMemberGradeGuid());
        List<MemberGradeDO> beforecollect = memberGradeDOS.stream().filter(a -> a.getGmtCreate().isBefore(memberGradeDO.getGmtCreate())).collect(Collectors.toList());
        List<MemberGradeDO> aftercollect = memberGradeDOS.stream().filter(a -> a.getGmtCreate().isAfter(memberGradeDO.getGmtCreate())).collect(Collectors.toList());
        if (beforecollect.size()>=1) {
            for (MemberGradeDO m :
                    beforecollect) {
                if (memberGradeDTO.getNeedIntegral() <= m.getNeedIntegral()) {
                    throw new ParameterException("该会员所需积分不能小于等于之前创建的会员等级所需积分");
                }
            }
        }

        if (aftercollect.size()>=1) {
            for (MemberGradeDO m :
                    aftercollect) {
                if (memberGradeDTO.getNeedIntegral() >= m.getNeedIntegral()) {
                    throw new ParameterException("该会员所需积分不能大于等于之后创建的会员等级所需积分");
                }
            }
        }
        MemberGradeDO memberGradeDOInDb = memberGradeMapper.getByMemberGradeGuid(memberGradeDTO.getMemberGradeGuid());
        memberGradeDOInDb.setColor(memberGradeDTO.getColor());
        memberGradeDOInDb.setDiscount(memberGradeDTO.getDiscount());
        memberGradeDOInDb.setNeedIntegral(memberGradeDTO.getNeedIntegral());
        memberGradeDOInDb.setPrepaidLimit(JSON.toJSONString(memberGradeDTO.getPrepaidLimit()));
        memberGradeDOInDb.setPrepaidRule(memberGradeDTO.getPrepaidRule());
        memberGradeDOInDb.setStyle(memberGradeDTO.getStyle());

        memberGradeDOInDb.setColorRGBA(memberGradeDTO.getColorRGBA());
        memberGradeDOInDb.setPicture(memberGradeDTO.getPicture());
        if (!memberGradeDTO.getName().equals(memberGradeDOInDb.getName())) {

                memberGradeDOInDb.setName(memberGradeDTO.getName());

        }
        memberGradeMapper.updateByPrimaryKeySelective(memberGradeDOInDb);
        List<MemberGradeDTO.IntegralRuleDTO> integralRuleDTOS = memberGradeDTO.getIntegralRuleDTOS();
        //同时修改积分规则
        List<IntegralRuleDO> ios=new ArrayList<>();
        for (MemberGradeDTO.IntegralRuleDTO integralRuleDTO : integralRuleDTOS) {
            IntegralRuleDO integralRuleDO = memberTransform.integralRuleDTO2DO(integralRuleDTO);
            ios.add(integralRuleDO);

        }
        int k=  integralRuleMapper.batchUpdate(ios);
        return Boolean.TRUE;
    }

    @Override
    public List<MemberGradeDTO> memberGradeList() {
        List<MemberGradeDO> memberGradeDOS = memberGradeMapper.selectAllMemberGrade();
        List<MemberGradeDTO> memberGradeDTOS = new ArrayList<>();
        for (MemberGradeDO memberGradeDO : memberGradeDOS) {
            MemberGradeDTO memberGradeDTO = memberTransform.memberGradeDO2DTO(memberGradeDO);
            //根据等级Guid查询积分规则
            List<IntegralRuleDO> integralRuleDOS = integralRuleMapper.selectByGradeGuid(memberGradeDO
                    .getMemberGradeGuid());
            List<MemberGradeDTO.IntegralRuleDTO> integralRuleDTOS = new ArrayList<>();
            for (IntegralRuleDO integralRuleDO : integralRuleDOS) {
                MemberGradeDTO.IntegralRuleDTO integralRuleDTO = memberTransform.integralRuleDO2DTO(integralRuleDO);
                if (integralRuleDTO.getGetType()!=null&&(integralRuleDTO.getGetType().equals(1)||integralRuleDTO.getGetType().equals(2))) {
                    integralRuleDTO.setGetTypeName(GetIntegralRuleTypeEnum.getDesc(integralRuleDTO.getGetType()));
                }
                if (integralRuleDO.getConsumeType()!=null&&integralRuleDO.getConsumeType().equals(3)){
                    integralRuleDTO.setConsumeTypeName(ConsumeIntegralRuleTypeEnum.getDesc(integralRuleDO.getConsumeType()));
                }
                integralRuleDTOS.add(integralRuleDTO);
            }
            memberGradeDTO.setIntegralRuleDTOS(integralRuleDTOS);
            List<String> strings = JSON.parseArray(memberGradeDO.getPrepaidLimit(), String.class);
            memberGradeDTO.setPrepaidLimit(strings);
            memberGradeDTOS.add(memberGradeDTO);
        }
        Collections.sort(memberGradeDTOS, Comparator.comparing(MemberGradeDTO::getGmtCreate));
        return memberGradeDTOS;
    }

    @Override
    public Boolean deleteMemberGrade(MemberGradeDTO memberGradeDTO) {

        MemberGradeDO memberGradeDO = memberGradeMapper.selectByPrimaryKey(memberGradeDTO.getMemberGradeGuid());

        int  membercount=memberMapper.selectMemberByMemberGuid(memberGradeDTO.getMemberGradeGuid());
        if (membercount>0){
            throw new ParameterException("该会员等级已经有对应的会员,不能删除");
        }
        if (memberGradeDO!=null &&memberGradeDO.getIsDefault().equals((byte)1)){
            throw new ParameterException("默认等级不能删除");
        }
        memberGradeMapper.deleteMemberGrade(memberGradeDTO.getMemberGradeGuid());
        return Boolean.TRUE;
    }

    @Override
    public MemberGradeDTO memberGradeDetail(String memberGradeGuid) {
        MemberGradeDO memberGradeDO = memberGradeMapper.getByMemberGradeGuid(memberGradeGuid);
        List<IntegralRuleDO> integralRuleDOS = integralRuleMapper.selectByGradeGuid(memberGradeDO
                .getMemberGradeGuid());
        List<MemberGradeDTO.IntegralRuleDTO> integralRuleDTOS = new ArrayList<>();
        MemberGradeDTO memberGradeDTO = memberTransform.memberGradeDO2DTO(memberGradeDO);
        if(integralRuleDOS != null){
            for (IntegralRuleDO integralRuleDO : integralRuleDOS) {
                MemberGradeDTO.IntegralRuleDTO integralRuleDTO = memberTransform.integralRuleDO2DTO(integralRuleDO);
                if (integralRuleDTO.getGetType()!=null&&(integralRuleDTO.getGetType().equals(1)||integralRuleDTO.getGetType().equals(2))) {
                    integralRuleDTO.setGetTypeName(GetIntegralRuleTypeEnum.getDesc(integralRuleDTO.getGetType()));
                }
                if (integralRuleDO.getConsumeType()!=null&&integralRuleDO.getConsumeType().equals(3)){
                    integralRuleDTO.setConsumeTypeName(ConsumeIntegralRuleTypeEnum.getDesc(integralRuleDO.getConsumeType()));
                }
                integralRuleDTOS.add(integralRuleDTO);
            }
        }
        memberGradeDTO.setIntegralRuleDTOS(integralRuleDTOS);
        List<String> strings = JSON.parseArray(memberGradeDO.getPrepaidLimit(), String.class);
        memberGradeDTO.setPrepaidLimit(strings);
        memberGradeDTO.setDiscount(memberGradeDTO.getDiscount());
        return memberGradeDTO;
    }

    public static void main(String[] args) {
        System.out.println(new BigDecimal(-2.00).compareTo(BigDecimal.ZERO));
    }
}
