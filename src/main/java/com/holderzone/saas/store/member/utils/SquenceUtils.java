package com.holderzone.saas.store.member.utils;

import com.holderzone.framework.util.RegexUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SquenceUtils
 * @date 2018/11/02 15:01
 * @description //
 * @program holder-saas-store-trading-center
 */
public class SquenceUtils {
    /**
     * 10
     */
    private static final int TEN = 10;

    /**
     * 100
     */
    private static final int HUNDRED = 100;

    /**
     * 上一次时间戳
     */
    private static long lastTimeMill = -1L;

    /**
     * 序列号
     */
    private static long sequence = 0L;

    /**
     * 最大序列号
     */
    private static final int MAX_SEQUENCE = 999;

    /**
     * 生成唯一Id
     *
     * @return 唯一Id
     */
    public static String nextId() {

        long currentTimeMill;

        long currentSequence;

        String sequenceStr;

        synchronized (SquenceUtils.class) {

            currentTimeMill = currentTimeMill();

            if (currentTimeMill == lastTimeMill) {
                if (sequence >= MAX_SEQUENCE) {
                    sequence = 0;
                    currentTimeMill = nextTimeMill();
                } else {
                    sequence += 1;
                }
            } else {
                sequence = 0L;
            }

            currentSequence = sequence;

            lastTimeMill = currentTimeMill;
        }

        if (currentSequence < TEN) {
            sequenceStr = String.format("00%d", currentSequence);
        } else if (currentSequence < HUNDRED) {
            sequenceStr = String.format("0%d", currentSequence);
        } else {
            sequenceStr = String.valueOf(currentSequence);
        }

        return String.format("%d%s", currentTimeMill, sequenceStr);
    }

    /**
     * 当前timeMill
     *
     * @return
     */
    private static long currentTimeMill() {
        String now = LocalDateTime.now().toString();
        String replaceAll = RegexUtils.getReplaceAll(now, "[^\\d]+", "");
        return Long.valueOf(replaceAll);
    }

    /**
     * 下一个timeMill
     *
     * @return
     */
    private static long nextTimeMill() {
        long curTimeMill = currentTimeMill();
        while (curTimeMill <= lastTimeMill) {
            curTimeMill = currentTimeMill();
        }
        return curTimeMill;
    }

}
