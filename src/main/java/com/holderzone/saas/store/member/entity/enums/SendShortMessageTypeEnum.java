package com.holderzone.saas.store.member.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SendShortMessageTypeEnum
 * @date 2018/11/01 17:37
 * @description //TODO
 * @program holder-saas-store-member
 */
public enum SendShortMessageTypeEnum {
    MEMBER_PREPAIEE(0, "会员充值"),
    MEMBER_CONSUME(1,"会员消费"),
    MEMBER_PAYRETURN(2,"会员退款"),
    MEMBER_REGIS(3,"会员注册"),
    MEMBER_FORGET_PASSWORD(4,"会员忘记密码")

    ;
    private int code;
    private String desc;

    private SendShortMessageTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static String getDesc(int code) {
        for (SendShortMessageTypeEnum s : SendShortMessageTypeEnum.values()) {
            if (s.getCode() == code) {
                return s.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
