package com.holderzone.saas.store.member.entity.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RedisConstant
 * @date 2018/08/22 下午6:43
 * @description //redis常量类
 * @program holder-saas-config-center
 */
public class RedisConstant {

    public static final String MEMBER_HEAD = "member";

    public static final String SEPARATOR = ":";

    public static final String MEMBER_GUID = "member";

    public static final String MEMBER_GRADE_GUID = "memberGrade";

    public static final String INTEGRAL_RULE_GUID = "integralRule";

    public static final String TRANSACTION_RECORD_GUID = "transactionRecord";

    public static final String INTEGRAL_RECORD_GUID = "integralRecordGuid";

    public static final String MEMBER_CARD_GUID = "memberCard";

    public static final String MEMBER_CONFIG_GUID = "memberConfig";

    public static final String MEMBER_VERIFICATION_CODE = "memberVerificationCode";

}
