package com.holderzone.saas.store.member.config;

import com.holderzone.framework.event.publish.impl.CustomerPublishImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

/**
 * <AUTHOR>
 * @date 2018/5/17
 */
@Configuration
public class EventConfig {

    @Autowired
    private ApplicationContext applicationContext;

    @Bean
    @Scope("singleton")
    public CustomerPublishImpl customerPublish() {
        CustomerPublishImpl customerPublish = new CustomerPublishImpl();
        String packageName = "com.holderzone.saas.store.member.service.listener";
        customerPublish.setPackName(packageName);
        customerPublish.addObserver();
        customerPublish.getListsForSpring()
                .forEach(customerObserverClass -> customerPublish.getLists().add(applicationContext.getBean(customerObserverClass)));
        return customerPublish;
    }

}
