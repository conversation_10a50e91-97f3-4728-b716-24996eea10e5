package com.holderzone.saas.store.member.controller.refactor;

import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/8
 */
@RestController
@RequestMapping("/member")
public class MemberController {

    @ApiOperation("根据手机号后四位查询会员")
    @GetMapping(value = "/query_member_by_phone_tail")
    List<SimpleMemberInfoDTO> queryMemberByPhoneTail(@RequestParam("phoneTail") String phoneTail){

    }
}
