package com.holderzone.saas.store.member.entity.transform;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.member.MemberNotifyDTO;
import com.holderzone.saas.store.dto.member.MemberRechargeDTO;
import com.holderzone.saas.store.dto.member.grade.MemberGradeDTO;
import com.holderzone.saas.store.dto.member.request.CreatMemberDTO;
import com.holderzone.saas.store.dto.member.response.*;
import com.holderzone.saas.store.dto.pay.AggPayNotifyDTO;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.AggPayPreTradingReqDTO;
import com.holderzone.saas.store.dto.pay.SaasAggPayDTO;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import com.holderzone.saas.store.member.domain.*;
import com.holderzone.saas.store.member.entity.bo.BaseMemberBO;
import com.holderzone.saas.store.member.entity.bo.TransactionRecordBO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface MemberTransform {

    MemberTransform INSTANCE = Mappers.getMapper(MemberTransform.class);

    /**
     * memberDOToBO
     *
     * @param memberDO
     * @return
     */
    BaseMemberRespDTO memberDOToBO(MemberDO memberDO);

    TransactionRecordRespDTO transactionRecordDOToBO(TransactionRecordDO transactionRecordDO);

    @Mapping(target = "prepaidLimit", ignore = true)
    MemberGradeDO memberGradeDTO2DO(MemberGradeDTO memberGradeDTO);

    IntegralRuleDO integralRuleDTO2DO(MemberGradeDTO.IntegralRuleDTO integralRuleDTO);

    @Mapping(target = "prepaidLimit", ignore = true)
    MemberGradeDTO memberGradeDO2DTO(MemberGradeDO memberGradeDO);

    MemberGradeDTO.IntegralRuleDTO integralRuleDO2DTO(IntegralRuleDO integralRuleDO);

    MemberDO creatMemberDTO2MemberDO(CreatMemberDTO creatMemberDTO);

    List<TransactionRecordRespDTO> transactionRecordDOS2transactionRecordRespDTOS(List<TransactionRecordDO> transactionRecordDOS);

    MemberListRespDTO memberGradeMemberDO2DTO(MemberGradeMeberReadDO memberGradeMeberReadDO);

    List<MemberListRespDTO> transactionMemberGradeMeberDO2MemberResponseDTOS(List<MemberGradeMeberReadDO> memberListRecords);

    MemberDetailRespDTO transactionMemberDetailReadDO2MemberDetailResponseDTO(MemberDetailReadDO memberDetailReadDO);

    MemberCardsRespDTO transactionMemberCardDO2MemberCardsRespDTO(MemberCardDO memberCardDO);

    List<MemberCardsRespDTO> transactionMemberCardDO2MemberCardsRespDTO(List<MemberCardDO> memberDOS);


    MemberGradeListDTO transactionMemberGradeListReadDO2MemberGradeList(MemberGradeMeberReadDO memberGradeMeberReadDO);

    List<MemberGradeListDTO> transactionMemberGradeListReadDO2MemberGradeDTOList(List<MemberGradeListReadDO> allMemberGrade);

    MemberPayRecordRespDTO transactionMemberTransactionDO2PayRecordRespDTO(TransactionRecordDO memberListRecords);

    List<MemberPayRecordRespDTO> transactionMemberTransactionDO2PayRecordRespDTOS(List<TransactionRecordDO> memberListRecords);


    BaseMemberRespDTO memberCardDOToBO(MemberCardReadDO memberByPhone);

    BaseInfo memberNotifyDTO2BaseInfo(MemberNotifyDTO memberNotifyDTO);

    SaasAggPayDTO createSaasAggPayDto(MemberRechargeDTO memberRechargeDTO);

    BaseInfo createBaseInfo(BaseDTO baseDTO);

    @Mappings({
            @Mapping(target = "payPowerId", source = "payPowerId"),
            @Mapping(target = "telNo", source = "telPhoneNo"),
            @Mapping(target = "paymentTypeName", source = "paymentName"),
            @Mapping(target = "paymentType", source = "paymentType"),
            @Mapping(target = "operationStaffGuid", source = "userGuid"),
            @Mapping(target = "operationStaffName", source = "userName"),
    })
    MemberChargeDO createMemberChargeDo(MemberRechargeDTO memberRechargeDTO);


    @Mappings({
            @Mapping(target = "terminalId", source = "deviceId"),
    })
    AggPayPreTradingReqDTO createAggPayDto(MemberRechargeDTO memberRechargeDTO);

    BaseDTO userInfo2BaseDto(UserContext userContext);


    @Mappings({
            @Mapping(target = "timePaid", source = "paidTime"),
            @Mapping(target = "codeUrl", source = "code_url"),
    })
    AggPayPollingRespDTO aggNotify2SaasCallBack(AggPayNotifyDTO aggPayNotifyDTO);

}
