package com.holderzone.saas.store.member.controller;

import com.holderzone.framework.response.LogicResponse;
import com.holderzone.saas.store.dto.member.MemberCheckDTO;
import com.holderzone.saas.store.member.entity.bo.SecretBO;
import com.holderzone.saas.store.member.service.SecretService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


/**
 * Created by z<PERSON><PERSON>yang on 2018/8/21.
 */
@RestController
@Api(description = "非对称加密接口(还没用)")
@Deprecated
public class RSAController extends BaseController {

    @Autowired
    private SecretService secretService;

    @ApiOperation(value = "获取公钥和随机值", notes = "获取公钥和随机值", response = LogicResponse.class)
    @RequestMapping(value = "/secret", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public LogicResponse<SecretBO> secret(@RequestBody String phone) {
        return LogicResponse.success(secretService.getPublicKeyAndRand());
    }

    @ApiOperation(value = "校验接口", notes = "加密校验", response = LogicResponse.class)
    @PostMapping(value = "/check", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public LogicResponse<String> check(@RequestBody MemberCheckDTO memberCheckDTO) {
        return secretService.check(memberCheckDTO);
    }

    public static void main(String[] args) {
        for (int i = 0; i < 10; i++) {
//            System.out.println(RandomStringUtils.randomNumeric(6));
//            System.out.println(SortingUtils.getStr(RedisConstant.SEPARATOR,RedisConstant.MEMBER_HEAD,"rand"));
//            System.out.println(Joiner.on(RedisConstant.SEPARATOR).skipNulls().join(RedisConstant.MEMBER_HEAD, "rand"));
//            System.out.println(ID2Utils.builder().defaultBuild().nextId());
//            System.out.println(ID2Utils.builder().defaultBuild().nextId());
//            BigDecimal balance = new BigDecimal(1.1);
//            balance = balance.add(new BigDecimal(1.1));
//            System.out.println(balance);
//            MemberExpiryConfig memberExpiryConfig = new MemberExpiryConfig();
//            memberExpiryConfig.setExpiryType((byte)1);
//            memberExpiryConfig.setYear(1);
//            String memberExpiry = JacksonUtils.writeValueAsString(memberExpiryConfig);
//            System.out.println(memberExpiry);
//            MemberExpiryConfig memberExpiryConfig1 = JacksonUtils.toObject(MemberExpiryConfig.class, memberExpiry);
//            System.out.println(LocalDate.now());
            BigDecimal divide = new BigDecimal(12.1).divide(new BigDecimal(3.3), 0, BigDecimal.ROUND_DOWN);
            System.out.println(divide);
        }
    }
}
