package com.holderzone.saas.store.member.entity.bo;

import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.saas.store.dto.member.MemberNotifyDTO;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberPayBO
 * @date 2018/11/05 15:20
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class MemberPayBO {
    private String entGuid;
    private TransactionPrintBO transactionPrintBO;
    private MemberNotifyDTO memberNotifyDTO;
    private String StoreGuid;
    private BaseInfo bs;
    private Integer type;
    private MessageDTO messageDTO;

}
