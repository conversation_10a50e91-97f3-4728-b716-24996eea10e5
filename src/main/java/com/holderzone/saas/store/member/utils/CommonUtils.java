package com.holderzone.saas.store.member.utils;

import com.google.common.base.Joiner;
import com.holderzone.saas.store.member.entity.constant.RedisConstant;
import com.holderzone.sdk.util.IdGenerator;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CommonUtils
 * @date 2018/09/04 11:26
 * @description //通用工具类
 * @program holder-saas-store-order
 */
public class CommonUtils {

    /**
     * 生成guid
     *
     * @param redisTemplate
     * @param redisKey
     * @return
     */
   /* public static String generateGuid(RedisTemplate redisTemplate, String redisKey) {
        return String.valueOf(IdGenerator.builder(redisTemplate).build().next(redisKey));
    }*/

    /**
     * 生成guid
     *
     * @param phone
     * @return
     */
    public static String getVerificationKey(String phone) {
        return Joiner.on(RedisConstant.SEPARATOR).skipNulls().join(RedisConstant
                .MEMBER_HEAD, RedisConstant.MEMBER_VERIFICATION_CODE, phone);
    }

}
