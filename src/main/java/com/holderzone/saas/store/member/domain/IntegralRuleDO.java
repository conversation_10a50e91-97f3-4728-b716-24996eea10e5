package com.holderzone.saas.store.member.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * hsm_integral_rule
 * <AUTHOR>
public class IntegralRuleDO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    private Boolean isDelete;

    /**
     * 业务主键
     */
    private String integralRuleGuid;

    /**
     * 规则类型 0：获取规则，1:消费规则
     */
    private Byte type;

    /**
     * 是否开启 0：关闭，1:开启
     */
    private Byte isOpen;

    /**
     * 使用余额是否累计积分 0：否，1:是
     */
    private Byte allowBalance;

    /**
     * 会员等级的业务主键
     */
    private String memberGradeGuid;

    /**
     * 获取规则类型（GetIntegralRuleTypeEnum）
     */
    private Integer getType;

    /**
     * 消费规则类型（ConsumeIntegralRuleTypeEnum）
     */
    private Integer consumeType;

    /**
     * 获取积分单位
     */
    private Integer getIntegralUnit;

    /**
     * 获取积分金额单位
     */
    private BigDecimal getFeeUnit;

    /**
     * 消费积分单位
     */
    private Integer consumeIntegralUnit;

    /**
     * 单笔订单最大消费积分
     */
    private Integer consumeIntegralMax;

    /**
     * 消费积分金额单位
     */
    private BigDecimal consumeFeeUnit;

    /**
     * 消费积分的最小金额
     */
    private BigDecimal consumeFeeMin;

    private static final long serialVersionUID = 1L;

    public Byte getAllowBalance() {
        return allowBalance;
    }

    public void setAllowBalance(Byte allowBalance) {
        this.allowBalance = allowBalance;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Boolean getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    public String getIntegralRuleGuid() {
        return integralRuleGuid;
    }

    public void setIntegralRuleGuid(String integralRuleGuid) {
        this.integralRuleGuid = integralRuleGuid;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public Byte getIsOpen() {
        return isOpen;
    }

    public void setIsOpen(Byte isOpen) {
        this.isOpen = isOpen;
    }

    public String getMemberGradeGuid() {
        return memberGradeGuid;
    }

    public void setMemberGradeGuid(String memberGradeGuid) {
        this.memberGradeGuid = memberGradeGuid;
    }

    public Integer getGetType() {
        return getType;
    }

    public void setGetType(Integer getType) {
        this.getType = getType;
    }

    public Integer getConsumeType() {
        return consumeType;
    }

    public void setConsumeType(Integer consumeType) {
        this.consumeType = consumeType;
    }

    public Integer getGetIntegralUnit() {
        return getIntegralUnit;
    }

    public void setGetIntegralUnit(Integer getIntegralUnit) {
        this.getIntegralUnit = getIntegralUnit;
    }

    public BigDecimal getGetFeeUnit() {
        return getFeeUnit;
    }

    public void setGetFeeUnit(BigDecimal getFeeUnit) {
        this.getFeeUnit = getFeeUnit;
    }

    public Integer getConsumeIntegralUnit() {
        return consumeIntegralUnit;
    }

    public void setConsumeIntegralUnit(Integer consumeIntegralUnit) {
        this.consumeIntegralUnit = consumeIntegralUnit;
    }

    public Integer getConsumeIntegralMax() {
        return consumeIntegralMax;
    }

    public void setConsumeIntegralMax(Integer consumeIntegralMax) {
        this.consumeIntegralMax = consumeIntegralMax;
    }

    public BigDecimal getConsumeFeeUnit() {
        return consumeFeeUnit;
    }

    public void setConsumeFeeUnit(BigDecimal consumeFeeUnit) {
        this.consumeFeeUnit = consumeFeeUnit;
    }

    public BigDecimal getConsumeFeeMin() {
        return consumeFeeMin;
    }

    public void setConsumeFeeMin(BigDecimal consumeFeeMin) {
        this.consumeFeeMin = consumeFeeMin;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IntegralRuleDO other = (IntegralRuleDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtModified() == null ? other.getGmtModified() == null : this.getGmtModified().equals(other.getGmtModified()))
            && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()))
            && (this.getIntegralRuleGuid() == null ? other.getIntegralRuleGuid() == null : this.getIntegralRuleGuid().equals(other.getIntegralRuleGuid()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getIsOpen() == null ? other.getIsOpen() == null : this.getIsOpen().equals(other.getIsOpen()))
            && (this.getMemberGradeGuid() == null ? other.getMemberGradeGuid() == null : this.getMemberGradeGuid().equals(other.getMemberGradeGuid()))
            && (this.getGetType() == null ? other.getGetType() == null : this.getGetType().equals(other.getGetType()))
            && (this.getConsumeType() == null ? other.getConsumeType() == null : this.getConsumeType().equals(other.getConsumeType()))
            && (this.getGetIntegralUnit() == null ? other.getGetIntegralUnit() == null : this.getGetIntegralUnit().equals(other.getGetIntegralUnit()))
            && (this.getGetFeeUnit() == null ? other.getGetFeeUnit() == null : this.getGetFeeUnit().equals(other.getGetFeeUnit()))
            && (this.getConsumeIntegralUnit() == null ? other.getConsumeIntegralUnit() == null : this.getConsumeIntegralUnit().equals(other.getConsumeIntegralUnit()))
            && (this.getConsumeIntegralMax() == null ? other.getConsumeIntegralMax() == null : this.getConsumeIntegralMax().equals(other.getConsumeIntegralMax()))
            && (this.getConsumeFeeUnit() == null ? other.getConsumeFeeUnit() == null : this.getConsumeFeeUnit().equals(other.getConsumeFeeUnit()))
            && (this.getConsumeFeeMin() == null ? other.getConsumeFeeMin() == null : this.getConsumeFeeMin().equals(other.getConsumeFeeMin()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtModified() == null) ? 0 : getGmtModified().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        result = prime * result + ((getIntegralRuleGuid() == null) ? 0 : getIntegralRuleGuid().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getIsOpen() == null) ? 0 : getIsOpen().hashCode());
        result = prime * result + ((getMemberGradeGuid() == null) ? 0 : getMemberGradeGuid().hashCode());
        result = prime * result + ((getGetType() == null) ? 0 : getGetType().hashCode());
        result = prime * result + ((getConsumeType() == null) ? 0 : getConsumeType().hashCode());
        result = prime * result + ((getGetIntegralUnit() == null) ? 0 : getGetIntegralUnit().hashCode());
        result = prime * result + ((getGetFeeUnit() == null) ? 0 : getGetFeeUnit().hashCode());
        result = prime * result + ((getConsumeIntegralUnit() == null) ? 0 : getConsumeIntegralUnit().hashCode());
        result = prime * result + ((getConsumeIntegralMax() == null) ? 0 : getConsumeIntegralMax().hashCode());
        result = prime * result + ((getConsumeFeeUnit() == null) ? 0 : getConsumeFeeUnit().hashCode());
        result = prime * result + ((getConsumeFeeMin() == null) ? 0 : getConsumeFeeMin().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", isDelete=").append(isDelete);
        sb.append(", integralRuleGuid=").append(integralRuleGuid);
        sb.append(", type=").append(type);
        sb.append(", isOpen=").append(isOpen);
        sb.append(", memberGradeGuid=").append(memberGradeGuid);
        sb.append(", getType=").append(getType);
        sb.append(", consumeType=").append(consumeType);
        sb.append(", getIntegralUnit=").append(getIntegralUnit);
        sb.append(", getFeeUnit=").append(getFeeUnit);
        sb.append(", consumeIntegralUnit=").append(consumeIntegralUnit);
        sb.append(", consumeIntegralMax=").append(consumeIntegralMax);
        sb.append(", consumeFeeUnit=").append(consumeFeeUnit);
        sb.append(", consumeFeeMin=").append(consumeFeeMin);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}