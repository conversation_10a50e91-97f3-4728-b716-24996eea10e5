package com.holderzone.saas.store.member.entity.pojo;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberExpiryConfig
 * @date 2018/08/29 上午11:29
 * @description //TODO
 * @program holder-saas-config-center
 */
public class MemberExpiryConfig {
    /**
     * 会员有效期类型 0：永久有效，1:时间限制
     */
    private Byte expiryType;

    /**
     * 有效期年份
     */
    private int year;

    public Byte getExpiryType() {
        return expiryType;
    }

    public void setExpiryType(Byte expiryType) {
        this.expiryType = expiryType;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }
}
