package com.holderzone.saas.store.member.domain;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * hsm_member_grade
 * <AUTHOR>
public class MemberGradeDO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    private Boolean isDelete;

    /**
     * 业务主键
     */
    private String memberGradeGuid;

    /**
     * 等级名称
     */
    private String name;

    /**
     * 成为该等级需要的积分
     */
    private Integer needIntegral;

    /**
     * 权益折扣
     */
    private BigDecimal discount;

    /**
     * 开通费用类型 0：免费，1:充值
     */
    private Byte feeType;

    /**
     * 是否默认 0：否，1:是
     */
    private Byte isDefault;

    /**
     * 最低充值金额
     */
    private BigDecimal minimumPrepaidFee;

    /**
     * 充值规则 0：不可充值，1:定额充值，2：不定额充值
     */
    private Byte prepaidRule;

    /**
     * 定额充值额度（JsonArray）
     */
    private String prepaidLimit;

    /**
     * 等级样式 0：纯色，1:图片
     */
    private Byte style;

    /**
     * 纯色样式 （MemberGradeColorEnum.code）
     */
    private Integer color;


    /**
     * 纯色样式 （RGBA自定义颜色）
     */
    private String colorRGBA;

    /**
     * 图片链接
     */
    private String picture;

    private static final long serialVersionUID = 1L;

    public Byte getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Byte isDefault) {
        this.isDefault = isDefault;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Boolean getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    public String getMemberGradeGuid() {
        return memberGradeGuid;
    }

    public void setMemberGradeGuid(String memberGradeGuid) {
        this.memberGradeGuid = memberGradeGuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getNeedIntegral() {
        return needIntegral;
    }

    public void setNeedIntegral(Integer needIntegral) {
        this.needIntegral = needIntegral;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public Byte getFeeType() {
        return feeType;
    }

    public void setFeeType(Byte feeType) {
        this.feeType = feeType;
    }

    public BigDecimal getMinimumPrepaidFee() {
        return minimumPrepaidFee;
    }

    public void setMinimumPrepaidFee(BigDecimal minimumPrepaidFee) {
        this.minimumPrepaidFee = minimumPrepaidFee;
    }

    public Byte getPrepaidRule() {
        return prepaidRule;
    }

    public void setPrepaidRule(Byte prepaidRule) {
        this.prepaidRule = prepaidRule;
    }

    public String getPrepaidLimit() {
        return prepaidLimit;
    }

    public void setPrepaidLimit(String prepaidLimit) {
        this.prepaidLimit = prepaidLimit;
    }

    public Byte getStyle() {
        return style;
    }

    public void setStyle(Byte style) {
        this.style = style;
    }

    public Boolean getDelete() {
        return isDelete;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        MemberGradeDO that = (MemberGradeDO) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (gmtCreate != null ? !gmtCreate.equals(that.gmtCreate) : that.gmtCreate != null) return false;
        if (gmtModified != null ? !gmtModified.equals(that.gmtModified) : that.gmtModified != null) return false;
        if (isDelete != null ? !isDelete.equals(that.isDelete) : that.isDelete != null) return false;
        if (memberGradeGuid != null ? !memberGradeGuid.equals(that.memberGradeGuid) : that.memberGradeGuid != null)
            return false;
        if (name != null ? !name.equals(that.name) : that.name != null) return false;
        if (needIntegral != null ? !needIntegral.equals(that.needIntegral) : that.needIntegral != null) return false;
        if (discount != null ? !discount.equals(that.discount) : that.discount != null) return false;
        if (feeType != null ? !feeType.equals(that.feeType) : that.feeType != null) return false;
        if (isDefault != null ? !isDefault.equals(that.isDefault) : that.isDefault != null) return false;
        if (minimumPrepaidFee != null ? !minimumPrepaidFee.equals(that.minimumPrepaidFee) : that.minimumPrepaidFee != null)
            return false;
        if (prepaidRule != null ? !prepaidRule.equals(that.prepaidRule) : that.prepaidRule != null) return false;
        if (prepaidLimit != null ? !prepaidLimit.equals(that.prepaidLimit) : that.prepaidLimit != null) return false;
        if (style != null ? !style.equals(that.style) : that.style != null) return false;
        if (color != null ? !color.equals(that.color) : that.color != null) return false;
        if (colorRGBA != null ? !colorRGBA.equals(that.colorRGBA) : that.colorRGBA != null) return false;
        return picture != null ? picture.equals(that.picture) : that.picture == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (gmtCreate != null ? gmtCreate.hashCode() : 0);
        result = 31 * result + (gmtModified != null ? gmtModified.hashCode() : 0);
        result = 31 * result + (isDelete != null ? isDelete.hashCode() : 0);
        result = 31 * result + (memberGradeGuid != null ? memberGradeGuid.hashCode() : 0);
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (needIntegral != null ? needIntegral.hashCode() : 0);
        result = 31 * result + (discount != null ? discount.hashCode() : 0);
        result = 31 * result + (feeType != null ? feeType.hashCode() : 0);
        result = 31 * result + (isDefault != null ? isDefault.hashCode() : 0);
        result = 31 * result + (minimumPrepaidFee != null ? minimumPrepaidFee.hashCode() : 0);
        result = 31 * result + (prepaidRule != null ? prepaidRule.hashCode() : 0);
        result = 31 * result + (prepaidLimit != null ? prepaidLimit.hashCode() : 0);
        result = 31 * result + (style != null ? style.hashCode() : 0);
        result = 31 * result + (color != null ? color.hashCode() : 0);
        result = 31 * result + (colorRGBA != null ? colorRGBA.hashCode() : 0);
        result = 31 * result + (picture != null ? picture.hashCode() : 0);
        return result;
    }

    public void setDelete(Boolean delete) {
        isDelete = delete;
    }

    public String getColorRGBA() {
        return colorRGBA;
    }

    public void setColorRGBA(String colorRGBA) {
        this.colorRGBA = colorRGBA;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getColor() {
        return color;
    }

    public void setColor(Integer color) {
        this.color = color;
    }

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", isDelete=").append(isDelete);
        sb.append(", memberGradeGuid=").append(memberGradeGuid);
        sb.append(", name=").append(name);
        sb.append(", needIntegral=").append(needIntegral);
        sb.append(", discount=").append(discount);
        sb.append(", feeType=").append(feeType);
        sb.append(", minimumPrepaidFee=").append(minimumPrepaidFee);
        sb.append(", prepaidRule=").append(prepaidRule);
        sb.append(", prepaidLimit=").append(prepaidLimit);
        sb.append(", style=").append(style);
        sb.append(", color=").append(color);
        sb.append(", picture=").append(picture);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}