package com.holderzone.saas.store.member.utils;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.resource.common.dto.enterprise.DeductShortMessageDTO;
import com.holderzone.resource.common.dto.enterprise.MessageConfigDTO;
import com.holderzone.saas.store.member.service.rpc.EntServiceClient;
import com.holderzone.saas.store.member.service.rpc.MsgClientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SendMessageUtil
 * @date 2018/11/01 11:18
 * @description //TODO
 * @program holder-saas-store-member
 */
@Component
public class SendMessageUtil2 {

    private ExecutorService executorService = new ThreadPoolExecutor(5,20,
            5L, TimeUnit.SECONDS,new ArrayBlockingQueue<>(20),
            new ThreadFactoryBuilder().setNameFormat("message-pool-%d").build());


    @Autowired
    private EntServiceClient es;
    @Autowired
    private MsgClientService ms;

    private static final Logger logger = LoggerFactory.getLogger(SendMessageUtil2.class);



    public  String sendMessage(MessageDTO m1,String entGuid,Integer type){
        SendShortMessage st=new SendShortMessage();

        String o="";
        Future<?> submit = executorService.submit(() -> {
            st.call();


        });
        try {
            if (submit.isDone()&&!submit.isCancelled()) {
                o = (String) submit.get();
            };
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        return  o;
    }

    class SendShortMessage implements Callable<String> {
       private String entGuid;
       private Integer type;
       private MessageDTO m1;

       @Override
       public String call() {
           List<DeductShortMessageDTO> ds = new ArrayList<>();
           DeductShortMessageDTO d = new DeductShortMessageDTO();
           d.setDeductCount(1);
           d.setEnterpriseGuid(entGuid);
           ds.add(d);
           MessageConfigDTO messageInfo = es.getMessageInfo(entGuid);
           //会员充值
        if (messageInfo==null){
           return ("发送短信失败,未获取到该企业短信信息。");
        }
        if (messageInfo!=null&&messageInfo.getResidueCount()<1){
            return ("发送短信失败，商户剩余短信条数不足");
        }
           if (messageInfo != null && type.equals(0)) {
               if (messageInfo.getResidueCount() >= 1 && messageInfo.getAfterCharge() == 1) {
                   ms.sendMessage(m1);
                   es.deductShortMessage(ds);
               }
           }
           //会员消费
           if (messageInfo != null && type.equals(1)) {
               if (messageInfo.getResidueCount() >= 1 && messageInfo.getAfterConsume() == 1) {
                   ms.sendMessage(m1);
                   es.deductShortMessage(ds);
               }
           }

           //会员退款
           if (messageInfo != null && type.equals(2)) {
               if (messageInfo.getResidueCount() >= 1) {
                   ms.sendMessage(m1);
                   es.deductShortMessage(ds);
               }
           }

           //会员注册
           if (messageInfo != null && type.equals(3)) {

               if (messageInfo.getResidueCount() >= 1) {

                   ms.sendMessage(m1);
                   DateTimeFormatter ftf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                   String formatTime = ftf.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(System.currentTimeMillis()), ZoneId.systemDefault()));
                   logger.info("注册短信发送请求时间:" + formatTime);
                   es.deductShortMessage(ds);
               }
           }

          return "SUCCESS";
       }


   }

}
