package com.holderzone.saas.store.member.service;

import com.holderzone.saas.store.dto.pay.AggPayNotifyDTO;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.member.domain.MemberChargeDO;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberChargeService
 * @date 2019/03/18 15:17
 * @description
 * @program holder-saas-store-member
 */
public interface MemberChargeService {

    void insert(MemberChargeDO memberChargeDo);

    void updateByTradingCallBack(AggPayPollingRespDTO aggPayPollingRespDTO, LocalDate businessDay);

    void updateByAggPayNotify(AggPayNotifyDTO aggPayNotifyDTO);

}
