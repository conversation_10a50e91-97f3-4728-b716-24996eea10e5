package com.holderzone.saas.store.member.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 商户上下文工具类
 * 用于从请求头或参数中获取商户信息
 * 
 * <AUTHOR>
 * @date 2025/8/8
 */
@Slf4j
public class MerchantContextUtil {
    
    private static final String ENT_GUID_HEADER = "X-Ent-Guid";
    private static final String STORE_GUID_HEADER = "X-Store-Guid";
    private static final String ENT_GUID_PARAM = "entGuid";
    private static final String STORE_GUID_PARAM = "storeGuid";
    
    /**
     * 获取企业GUID
     * 优先从请求头获取，其次从参数获取
     * 
     * @return 企业GUID
     */
    public static String getEntGuid() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            log.warn("无法获取当前请求，返回空的企业GUID");
            return null;
        }
        
        // 优先从请求头获取
        String entGuid = request.getHeader(ENT_GUID_HEADER);
        if (StringUtils.isNotBlank(entGuid)) {
            return entGuid;
        }
        
        // 从参数获取
        entGuid = request.getParameter(ENT_GUID_PARAM);
        if (StringUtils.isNotBlank(entGuid)) {
            return entGuid;
        }
        
        log.warn("无法从请求中获取企业GUID");
        return null;
    }
    
    /**
     * 获取门店GUID
     * 优先从请求头获取，其次从参数获取
     * 
     * @return 门店GUID
     */
    public static String getStoreGuid() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            log.warn("无法获取当前请求，返回空的门店GUID");
            return null;
        }
        
        // 优先从请求头获取
        String storeGuid = request.getHeader(STORE_GUID_HEADER);
        if (StringUtils.isNotBlank(storeGuid)) {
            return storeGuid;
        }
        
        // 从参数获取
        storeGuid = request.getParameter(STORE_GUID_PARAM);
        if (StringUtils.isNotBlank(storeGuid)) {
            return storeGuid;
        }
        
        log.warn("无法从请求中获取门店GUID");
        return null;
    }
    
    /**
     * 获取当前HTTP请求
     * 
     * @return HttpServletRequest
     */
    private static HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            return attributes.getRequest();
        } catch (Exception e) {
            log.warn("获取当前请求失败", e);
            return null;
        }
    }
}
