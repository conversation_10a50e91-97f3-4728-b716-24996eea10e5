package com.holderzone.saas.store.member.service;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import com.holderzone.saas.store.dto.member.MemberLoginRespDTO;
import com.holderzone.saas.store.dto.member.MemberRechargeDTO;
import com.holderzone.saas.store.dto.member.TransactionRecordDTO;
import com.holderzone.saas.store.dto.member.common.BaseMemberDTO;
import com.holderzone.saas.store.dto.member.request.*;
import com.holderzone.saas.store.dto.member.response.*;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.member.domain.MemberCardDO;

import java.util.List;


public interface MemberService {

    /**
     * 创建会员
     *
     * @param creatMemberDTO
     * @return
     */
    Boolean create(CreatMemberDTO creatMemberDTO);

    /**
     * 根据手机号获取密码
     *
     * @param phone
     * @return
     */
    String getPasswordByPhone(String phone);

    /**
     * 验证会员密码
     *
     * @param modifiPassWdReqDTO
     * @return
     */
    Boolean validateMemberByPhoneAndPasswd(ModifiPassWdReqDTO modifiPassWdReqDTO);

    BaseMemberRespDTO getMemberByPhone(BaseMemberDTO baseMemberDTO);

    MemberLoginRespDTO getMemberByGuid(String memberGuid);

    /**
     * 根据卡号或者会员电话查询会员信息及消费规则
     *
     * @param phone
     * @return
     */
    MemberLoginRespDTO getMemberLoginResByPhone(String phone);

    Boolean updateMember(UpdateMemberReqDTO updateMemberReqDTO);

    /**
     * 会员交易记录查询
     *
     * @param transactionRecordDTO
     * @return
     */
    Page<TransactionRecordRespDTO> queryTransactionRecord(TransactionRecordDTO transactionRecordDTO);

    /**
     * 修改会员密码
     *
     * @param modifiPassWdReqDTO
     * @return
     */
    Boolean modifiPassWd(ModifiPassWdReqDTO modifiPassWdReqDTO);

    /**
     * 忘记会员密码
     *
     * @param forgetPassWdReqDTO
     * @return
     */
    Boolean forgetPassWd(ForgetPassWdReqDTO forgetPassWdReqDTO);

    /**
     * 忘记密码时发送验证码
     *
     * @param baseMemberDTO
     * @return
     */
    Boolean verificationCode(BaseMemberDTO baseMemberDTO);

    /**
     * 获取会员卡
     *
     * @param memberGuid
     * @return
     */
    MemberCardDO getMemberCard(String memberGuid);

    /**
     * 默认数据生成
     *
     * @param entGuid
     * @return
     */
    Boolean InitDefaultMemberData(String entGuid);

    /**
     * 验证充值规则
     *
     * @param memberRechargeDTO
     * @return
     */
    String checkPrepaidRule(MemberRechargeDTO memberRechargeDTO);

    /**
     * 会员充值接口
     *
     * @param memberRechargeDTO
     * @return
     */
    AggPayRespDTO memberCharge(MemberRechargeDTO memberRechargeDTO);

    /**
     * 交易中心回调
     *
     * @param saasNotifyDTO
     * @return
     */
    String saasTradingCallBack(SaasNotifyDTO saasNotifyDTO);

    /**
     * 聚合支付回调
     *
     * @param aggPayNotifyDTO
     * @return
     */
    String aggPayNotify(AggPayNotifyDTO aggPayNotifyDTO);

    /**
     * 根据手机号后四位查询会员
     *
     * @param phoneTail 手机号
     * @return 会员信息
     */
    List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail);
}
