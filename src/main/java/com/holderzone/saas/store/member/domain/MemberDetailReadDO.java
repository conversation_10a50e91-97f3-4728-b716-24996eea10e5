package com.holderzone.saas.store.member.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.holderzone.saas.store.dto.member.response.MemberDetailRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberDetailReadDO
 * @date 2018/09/28 11:01
 * @description //TODO
 * @program holder-saas-store-member
 */
@Data
public class MemberDetailReadDO implements Serializable {
    @ApiModelProperty
    private String memberGuid;
    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "性别 0：女，1:男，2：保密")
    private Byte sex;



    @ApiModelProperty(value = "生日")

    private LocalDate birthday;

    @ApiModelProperty(value = "注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime registerTime;

    @ApiModelProperty(value = "会员有效期（开始日期）")
    private LocalDate startDate;

    @ApiModelProperty(value = "会员有效期（截止日期）")
    private LocalDate expiryDate;





    @ApiModelProperty(value = "会员等级的业务主键")
    private String memberGradeGuid;

    @ApiModelProperty(value = "会员等级名称")
    private String memberGradeDesc;

    @ApiModelProperty(value = "累计消费次数")
    private Integer totalConsumeNum;



    @ApiModelProperty(value = "累计积分")
    private Integer totalIntegral;

    @ApiModelProperty(value = "可用积分")
    private Integer residualIntegral;

    @ApiModelProperty(value = "累计消费金额（全部消费）")
    private BigDecimal totalConsumeFee;

    @ApiModelProperty(value = "累计支付金额（余额支付）")
    private BigDecimal totalPayFee;

    @ApiModelProperty(value = "累计充值金额")
    private BigDecimal totalPrepaidFee;

    @ApiModelProperty(value = "累计赠送金额")
    private BigDecimal totalPresentFee;

    @ApiModelProperty(value = "账户余额")
    private BigDecimal balance;
}
