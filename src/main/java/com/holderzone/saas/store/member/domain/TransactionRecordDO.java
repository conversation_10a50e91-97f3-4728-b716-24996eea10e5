package com.holderzone.saas.store.member.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * hsm_transaction_record
 * <AUTHOR>
@Data
public class TransactionRecordDO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    private Boolean isDelete;

    /**
     * 业务主键
     */
    private String transactionRecordGuid;

    /**
     * 会员业务主键
     */
    private String memberGuid;

    /**
     * 交易流水号
     */
    private String sequenceNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 交易时间
     */
    private LocalDateTime transactionTime;

    /**
     * 交易金额
     */
    private BigDecimal fee;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 交易类型 0：充值，1:支付 2退款
     */
    private Byte type;

    /**
     * 充值类型 0：现金，1:聚合支付，2：刷卡
     */
    private Byte prepaidType;

    /**
     * 充值类型
     */
    private String prepaidTypeDesc;

    /**
     * 会员账号
     */
    private String accountNumber;

    /**
     * 操作人guid
     */
    private String staffGuid;

    /**
     * 操作人姓名
     */
    private String staffName;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店姓名
     */
    private String storeName;

    /**
     * 营业日 营业日 2018-9-30新增字段
     */
    private LocalDate businessDay;

    /**
     * 支付方式名称 2018-10-18新增
     */
    private String prepaidTypeName;

    @Override
    public String toString() {
        return "TransactionRecordDO{" +
                "id=" + id +
                ", gmtCreate=" + gmtCreate +
                ", gmtModified=" + gmtModified +
                ", isDelete=" + isDelete +
                ", transactionRecordGuid='" + transactionRecordGuid + '\'' +
                ", memberGuid='" + memberGuid + '\'' +
                ", sequenceNo='" + sequenceNo + '\'' +
                ", orderNo='" + orderNo + '\'' +
                ", transactionTime=" + transactionTime +
                ", fee=" + fee +
                ", balance=" + balance +
                ", type=" + type +
                ", prepaidType=" + prepaidType +
                ", prepaidTypeDesc='" + prepaidTypeDesc + '\'' +
                ", accountNumber='" + accountNumber + '\'' +
                ", staffGuid='" + staffGuid + '\'' +
                ", staffName='" + staffName + '\'' +
                ", storeGuid='" + storeGuid + '\'' +
                ", storeName='" + storeName + '\'' +
                ", businessDay=" + businessDay +
                ", PrepaidTypeName='" + prepaidTypeName + '\'' +
                '}';
    }

}