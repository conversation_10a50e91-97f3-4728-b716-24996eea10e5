package com.holderzone.saas.store.member.service.impl;

import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.base.dto.message.MessageType;
import com.holderzone.framework.base.dto.message.ShortMessageDTO;
import com.holderzone.framework.base.dto.message.ShortMessageType;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.enterprise.DeductShortMessageDTO;
import com.holderzone.saas.store.dto.member.MemberNotifyDTO;
import com.holderzone.saas.store.dto.member.request.HandoverReqDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.order.response.daily.GatherRespDTO;
import com.holderzone.saas.store.dto.order.response.daily.MemberConsumeRespDTO;
import com.holderzone.saas.store.member.domain.*;
import com.holderzone.saas.store.member.entity.bo.TransactionPrintBO;
import com.holderzone.saas.store.member.entity.constant.RedisConstant;
import com.holderzone.saas.store.member.entity.enums.GetIntegralRuleTypeEnum;
import com.holderzone.saas.store.member.entity.enums.IntegralTransactionTypeEnum;
import com.holderzone.saas.store.member.mapper.*;
import com.holderzone.saas.store.member.service.MemberTransactionService;
import com.holderzone.saas.store.member.service.rpc.EntServiceClient;
import com.holderzone.saas.store.member.service.rpc.MemberStoreService;
import com.holderzone.saas.store.member.service.rpc.MsgClientService;
import com.holderzone.saas.store.member.utils.BigDecimalUtil;
import com.holderzone.saas.store.member.utils.DynamicHelper;
import com.holderzone.saas.store.member.utils.SendMessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberTransactionServiceImpl
 * @date 2018/08/28 下午3:47
 * @description //TODO
 * @program holder-saas-config-center
 */
@Service
@Slf4j
public class MemberTransactionServiceImpl implements MemberTransactionService {

    @Autowired
    private TransactionRecordMapper transactionRecordMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private MemberMapper memberMapper;

    @Autowired
    private IntegralRecordMapper integralRecordMapper;

    @Autowired
    private IntegralRuleMapper integralRuleMapper;

    @Autowired
    private MemberStoreService memberStoreService;

    @Autowired
    private MemberCardMapper memberCardMapper;

    @Autowired
    private MemberGradeMapper memberGradeMapper;
    @Autowired
    private DynamicHelper helper;

    @Autowired
    private EntServiceClient entServiceClient;

    @Autowired
    private MsgClientService msgClientService;

    @Override
    @Transactional
    public TransactionPrintBO addTransactionRecord(MemberNotifyDTO memberNotifyDTO) {
        TransactionRecordDO transactionRecordDO = new TransactionRecordDO();
        IntegralRecordDO integralRecordDO = new IntegralRecordDO();
        //
        TransactionPrintBO transactionPrintBO = new TransactionPrintBO();
        //短信发送
        MessageDTO m1 = new MessageDTO();
        Map<String, String> parms = new HashMap<>();
        List<DeductShortMessageDTO> dslist = new ArrayList<>();
        DeductShortMessageDTO ds = new DeductShortMessageDTO();
        ds.setDeductCount(1);
        ds.setEnterpriseGuid(memberNotifyDTO.getEnterpriseGuid());
        dslist.add(ds);
        ShortMessageDTO s1 = new ShortMessageDTO();

        //获取会员信息

        MemberDO memberDO = memberMapper.getMemberByGuid(memberNotifyDTO.getMemberGuid());
        if (memberDO == null) {
            throw new ParameterException("会员Guid不存在");
        }

        integralRecordDO.setAccountNumber(memberDO.getAccountNumber());
        integralRecordDO.setIsDelete(false);
        integralRecordDO.setMemberGuid(memberDO.getMemberGuid());
        if (memberNotifyDTO.getOrderGuid() != null) {
            integralRecordDO.setOrderNo(memberNotifyDTO.getOrderGuid());
        }
        if (memberNotifyDTO.getOrderGuid() == null) {
            integralRecordDO.setOrderNo(memberNotifyDTO.getSequenceNo());
        }
        integralRecordDO.setTransactionTime(memberNotifyDTO.getTradingTime() == null ? LocalDateTime.now() :
                memberNotifyDTO.getTradingTime());
        integralRecordDO.setStaffGuid(memberNotifyDTO.getOperationStaffGuid());
        integralRecordDO.setStaffName(memberNotifyDTO.getOperationStaffName());

        integralRecordDO.setBusinessDay(memberNotifyDTO.getBusinessDay() == null ? LocalDate.now() : memberNotifyDTO
                .getBusinessDay());
        //生成交易记录随机业务主键
        String transactionRecordGuid = helper.generateGuid(RedisConstant
                .TRANSACTION_RECORD_GUID);
        transactionRecordDO.setTransactionRecordGuid(transactionRecordGuid);
        transactionRecordDO.setMemberGuid(memberNotifyDTO.getMemberGuid());
        transactionRecordDO.setSequenceNo(memberNotifyDTO.getSequenceNo());
        transactionRecordDO.setType(memberNotifyDTO.getType().byteValue());
        transactionRecordDO.setOrderNo(memberNotifyDTO.getOrderGuid());
        transactionRecordDO.setTransactionTime(memberNotifyDTO.getTradingTime() == null ? LocalDateTime.now() :
                memberNotifyDTO.getTradingTime());

        //获取所有会员等级并按所需积分升序排列
        List<MemberGradeDO> memberGradeDOList = memberGradeMapper.selectAllMemberGrade();
        Collections.sort(memberGradeDOList, new Comparator<MemberGradeDO>() {
            @Override
            public int compare(MemberGradeDO o1, MemberGradeDO o2) {
                return o1.getNeedIntegral() - o2.getNeedIntegral();
            }
        });
        if (memberNotifyDTO.getType().equals(1)) {

            transactionRecordDO.setFee(memberNotifyDTO.getAmount().multiply(new BigDecimal(-1)));
        }
        if (memberNotifyDTO.getType().equals(0) || memberNotifyDTO.getType().equals(2)) {
            transactionRecordDO.setFee(memberNotifyDTO.getAmount());
        }
        transactionRecordDO.setAccountNumber(memberDO.getAccountNumber());
        transactionRecordDO.setStoreGuid(memberNotifyDTO.getStoreGuid());
        transactionRecordDO.setStoreName(memberNotifyDTO.getStoreName());
        transactionRecordDO.setGmtCreate(LocalDateTime.now());
        transactionRecordDO.setGmtModified(LocalDateTime.now());
        transactionRecordDO.setStaffGuid(memberNotifyDTO.getOperationStaffGuid());
        transactionRecordDO.setStaffName(memberNotifyDTO.getOperationStaffName());
        transactionRecordDO.setTransactionTime(memberNotifyDTO.getTradingTime() == null ? LocalDateTime.now() :
                memberNotifyDTO.getTradingTime());


        //会员充值
        if (memberNotifyDTO.getType().equals(0)) {
            memberDO.setRecentlyLoginDate(LocalDateTime.now());
            transactionRecordDO.setPrepaidType(memberNotifyDTO.getPrepaidType());
            transactionRecordDO.setPrepaidTypeName(memberNotifyDTO.getPrepaidTypeName());
            BigDecimal balance = memberDO.getBalance();
            //账户余额计算
            balance = balance.add(memberNotifyDTO.getAmount());
            memberDO.setBalance(balance);
            transactionRecordDO.setBalance(balance);
            //累计充值次数+1
            memberDO.setTotalPrepaidNum(memberDO.getTotalPrepaidNum() + 1);
            //累计充值金额计算
            memberDO.setTotalPrepaidFee(memberDO.getTotalPrepaidFee().add(memberNotifyDTO.getAmount()));
            //获取充值积分规则
            IntegralRuleDO integralRuleDO = integralRuleMapper.selectByGetTypeAndGradeGuid(GetIntegralRuleTypeEnum
                    .PREPAID.getCode(), memberDO.getMemberGradeGuid());
            //开启充值时获取积分
            if (integralRuleDO != null) {
                if (integralRuleDO.getIsOpen().equals((byte) 1)) {
                    //积分单位
                    BigDecimal divide = new BigDecimal(0);
                    Integer getIntegralUnit = integralRuleDO.getGetIntegralUnit();
                    BigDecimal getFeeUnit = integralRuleDO.getGetFeeUnit();
                    //应计积分单位=消费金额/消费金额单位
                    if (getFeeUnit.intValue() != 0) {

                        divide = memberNotifyDTO.getAmount().divide(getFeeUnit, 0, BigDecimal.ROUND_DOWN);
                    }

                    //所得积分=应计积分单位*积分单位
                    BigDecimal multiply = divide.multiply(new BigDecimal(getIntegralUnit));
                    //设置总积分
                    memberDO.setTotalIntegral(memberDO.getTotalIntegral() + multiply.intValue());
                    //计算剩余积分
                    int residualIntegral = memberDO.getResidualIntegral() + multiply.intValue();
                    memberDO.setResidualIntegral(residualIntegral);
                    integralRecordDO.setIntegralRecordGuid(helper.generateGuid(RedisConstant.INTEGRAL_RECORD_GUID));
                    integralRecordDO.setType(IntegralTransactionTypeEnum.PREPAID.getCode());
                    integralRecordDO.setResidualIntegral(residualIntegral);
                    integralRecordDO.setTransactionIntegral(multiply.intValue());
                    //新增积分交易记录
                    if (multiply.intValue() != 0) {
                        integralRecordMapper.insertSelective(integralRecordDO);
                    }
                }
            }
            transactionRecordMapper.insertSelective(transactionRecordDO);
            //充值成功后判断是否升级
            //先查询所有会员等级所需积分，并升序排列

            for (int i = 0; i < memberGradeDOList.size(); i++) {
                System.out.println(memberGradeDOList.get(i));
                if (memberDO.getTotalIntegral() >= memberGradeDOList.get(i).getNeedIntegral()) {
                    memberDO.setMemberGradeGuid(memberGradeDOList.get(i).getMemberGradeGuid());
                }
            }
            //充值成功后设置余额及交易时间
            transactionPrintBO.setBalance(memberDO.getBalance());
            transactionPrintBO.setResidualintegral(memberDO.getResidualIntegral());
            transactionPrintBO.setPrepaidTime(memberNotifyDTO.getTradingTime());
            //发送消息
            m1.setMessageType(MessageType.SHORT_MESSAGE);
            parms.put("Name", memberDO.getPhone());
            parms.put("Banlace", memberDO.getBalance() + "");
            parms.put("Money", memberNotifyDTO.getAmount() + "");
            parms.put("Entertainment", memberNotifyDTO.getEnterpriseName());
            s1.setParams(parms);
            s1.setShortMessageType(ShortMessageType.MEMBER_PREPAID);
            s1.setPhoneNumber(memberDO.getPhone());
            s1.setContent(parms.get("Entertainment") + "温馨提示:" + "尊敬的会员，您的账户" + parms.get("Name") + "已成功充值" + parms
                    .get("Money") + "元," + "当前余额" + parms.get("Banlace") + "元。");
            m1.setShortMessage(s1);
            //发送短信
            log.info("MemberTransactionImpl send message before request: entry {} " + JacksonUtils.writeValueAsString(s1));
            transactionPrintBO.setMessageDTO(m1);
            transactionPrintBO.setDlist(dslist);

        }

        //消费类型
        if (memberNotifyDTO.getType().equals(1)) {
            memberDO.setRecentlyLoginDate(LocalDateTime.now());
            //会员卡消费
            if (memberNotifyDTO.getConsumeType().equals(0)) {
                BigDecimal balance = memberDO.getBalance();
                if (balance != null) {
                    balance = balance.subtract(memberNotifyDTO.getAmount());
                    memberDO.setBalance(balance);
                    transactionRecordDO.setBalance(balance);
                    memberDO.setRecentlyConsumeDate(memberNotifyDTO.getTradingTime());
                    memberDO.setTotalConsumeNum(memberDO.getTotalConsumeNum() + 1);
                    //累计支付金额计算
                    memberDO.setTotalPayFee(memberDO.getTotalPayFee().add(memberNotifyDTO.getAmount()));
                    memberDO.setTotalConsumeFee(memberDO.getTotalConsumeFee().add(memberNotifyDTO.getAmount()));
                }
                transactionRecordMapper.insertSelective(transactionRecordDO);
                m1.setMessageType(MessageType.SHORT_MESSAGE);
                parms.put("Name", memberDO.getPhone());
                parms.put("BMon", memberDO.getBalance() + "");
                parms.put("AMon", memberNotifyDTO.getAmount() + "");
                parms.put("Entertainment", memberNotifyDTO.getEnterpriseName());
                s1.setParams(parms);
                s1.setShortMessageType(ShortMessageType.MEMBER_COSUME);
                s1.setPhoneNumber(memberDO.getPhone());
                s1.setContent(parms.get("Entertainment") + "温馨提示:" + "尊敬的会员，您的账户" + parms.get("Name") + "已成功支付" +
                        parms.get("AMon") + "元," + "当前余额" + parms.get("BMon") + "元。");
                m1.setShortMessage(s1);
                transactionPrintBO.setMessageDTO(m1);
                transactionPrintBO.setDlist(dslist);

            }
            //其它消费不计算余额
            if (memberNotifyDTO.getConsumeType().equals(1)) {
                memberDO.setRecentlyConsumeDate(memberNotifyDTO.getTradingTime());
                memberDO.setTotalConsumeNum(memberDO.getTotalConsumeNum() + 1);

                memberDO.setTotalConsumeFee(memberDO.getTotalConsumeFee().add(memberNotifyDTO.getAmount()));
            }
            //消费积分
            IntegralRuleDO integralRuleDO = integralRuleMapper.selectByGetTypeAndGradeGuid(GetIntegralRuleTypeEnum
                    .CONSUME.getCode(), memberDO.getMemberGradeGuid());
            //如果是会员卡余额支付且使用余额累计积分
            boolean enableCardPay = memberNotifyDTO.getConsumeType().equals(0) && integralRuleDO.getAllowBalance()
                    .equals((byte) 1);
            //非会员卡余额支付
            boolean noCard = memberNotifyDTO.getConsumeType().equals(1);
            if (enableCardPay || noCard) {
                //允许累计积分
                //积分规则开启
                if (integralRuleDO.getIsOpen().equals((byte) 1)) {
                    //获取积分金额单位
                    Integer getIntegralUnit = integralRuleDO.getGetIntegralUnit();
                    //获取积分单位
                    BigDecimal getFeeUnit = integralRuleDO.getGetFeeUnit();
                    BigDecimal divide = memberNotifyDTO.getAmount().divide(getFeeUnit, 0, BigDecimal.ROUND_DOWN);
                    //本次交易积多少分
                    BigDecimal multiply = divide.multiply(new BigDecimal(getIntegralUnit));

                    memberDO.setTotalIntegral(memberDO.getTotalIntegral() + multiply.intValue());
                    int residualIntegral = memberDO.getResidualIntegral() + multiply.intValue();
                    memberDO.setResidualIntegral(residualIntegral);
                    integralRecordDO.setIntegralRecordGuid(helper.generateGuid(RedisConstant.INTEGRAL_RECORD_GUID));
                    integralRecordDO.setType(IntegralTransactionTypeEnum.CONSUME.getCode());
                    //剩余积分
                    integralRecordDO.setResidualIntegral(residualIntegral);
                    integralRecordDO.setTransactionIntegral(multiply.intValue());
                    if (multiply.intValue() != 0) {
                        integralRecordMapper.insertSelective(integralRecordDO);
                    }

                }
            }
            //扣除积分
            //如果使用积分抵扣
            if (memberNotifyDTO.getUseIntegral() != null && memberNotifyDTO.getUseIntegral()) {
                Integer integral = memberNotifyDTO.getIntegral();
                //增加积分抵现记录
                integralRecordDO.setIntegralRecordGuid(helper.generateGuid(RedisConstant.INTEGRAL_RECORD_GUID));
                integralRecordDO.setType(IntegralTransactionTypeEnum.OFFSET.getCode());
                //剩余积分=剩余积分-本次抵扣积分
                int residualIntegral = memberDO.getResidualIntegral() - integral;
                memberDO.setResidualIntegral(residualIntegral);
                integralRecordDO.setResidualIntegral(residualIntegral);
                //设置本次交易积分为负数
                integralRecordDO.setTransactionIntegral(-1 * integral);
                //修改会员表
                memberMapper.updateByPrimaryKeySelective(memberDO);
                //新增积分记录表
                integralRecordMapper.insertSelective(integralRecordDO);

            }
            //消费成功后判断是否升级
            for (int i = 0; i < memberGradeDOList.size(); i++) {
                if (memberDO.getTotalIntegral() >= memberGradeDOList.get(i).getNeedIntegral()) {
                    memberDO.setMemberGradeGuid(memberGradeDOList.get(i).getMemberGradeGuid());
                }
            }
        }
        //退款
        if (memberNotifyDTO.getType().equals(2)) {

            //通过memberGuid查最新的剩余积分
            int organinalResidualIntegral = memberDO.getResidualIntegral();
            transactionRecordDO.setIsDelete(false);
            //余额
            //时间
            transactionRecordDO.setGmtModified(LocalDateTime.now());

            //使用会员卡消费,退款时
            if (memberNotifyDTO.getConsumeType().equals(0)) {
                BigDecimal balance = memberDO.getBalance();
                if (balance != null) {
                    balance = balance.add(memberNotifyDTO.getAmount());
                    memberDO.setBalance(balance);
                    transactionRecordDO.setBalance(balance);

                    memberDO.setTotalConsumeNum(memberDO.getTotalConsumeNum() - 1);
                    //累计支付金额计算
                    memberDO.setTotalPayFee(memberDO.getTotalPayFee().subtract(memberNotifyDTO.getAmount()));
                    memberDO.setTotalConsumeFee(memberDO.getTotalConsumeFee().subtract(memberNotifyDTO.getAmount()));
                }
                m1.setMessageType(MessageType.SHORT_MESSAGE);
                parms.put("Name", memberDO.getPhone());
                parms.put("BMon", memberDO.getBalance() + "");
                parms.put("AMon", memberNotifyDTO.getAmount() + "");
                parms.put("Entertainment", memberNotifyDTO.getEnterpriseName());
                s1.setParams(parms);
                s1.setShortMessageType(ShortMessageType.MEMBER_RETURN_PAY);
                s1.setPhoneNumber(memberDO.getPhone());
                s1.setContent(parms.get("Entertainment") + "温馨提示:" + "尊敬的会员，您的账户" + parms.get("Name") + "已成功退款" +
                        parms.get("AMon") + "元," + "当前余额" + parms.get("BMon") + "元。");
                m1.setShortMessage(s1);
                transactionPrintBO.setMessageDTO(m1);
                transactionPrintBO.setDlist(dslist);
                transactionRecordMapper.insertSelective(transactionRecordDO);
            }
            //非会员卡消费退款不重新计算余额
            if (memberNotifyDTO.getConsumeType().equals(1)) {
                //消费次数-1
                memberDO.setTotalConsumeNum(memberDO.getTotalConsumeNum() - 1);
                //总消费金额减去
                memberDO.setTotalConsumeFee(memberDO.getTotalConsumeFee().subtract(memberNotifyDTO.getAmount()));
            }
            //获取该订单号对应的积分记录
            List<IntegralRecordDO> historyIntegralRecordDo = integralRecordMapper.selectRecordByOrderGuid
                    (memberNotifyDTO.getOrderGuid());
            Collections.sort(historyIntegralRecordDo, new Comparator<IntegralRecordDO>() {
                @Override
                public int compare(IntegralRecordDO o1, IntegralRecordDO o2) {
                    return Optional.ofNullable(o1.getType()).orElse(0).compareTo(Optional.ofNullable(o2.getType()).orElse(0));
                }
            });
            List<IntegralRecordDO> ios = new ArrayList<>();
            int consume_integral = 0;
            if (historyIntegralRecordDo.size() > 0) {
                for (IntegralRecordDO io : historyIntegralRecordDo) {
                    //交易时间
                    IntegralRecordDO io2 = new IntegralRecordDO();
                    //会员GUid
                    io2.setMemberGuid(io.getMemberGuid());
                    io2.setTransactionTime(memberNotifyDTO.getTradingTime() == null ? LocalDateTime.now() :
                            memberNotifyDTO.getTradingTime());
                    io2.setBusinessDay(memberNotifyDTO.getBusinessDay());
                    io2.setGmtModified(memberNotifyDTO.getTradingTime() == null ? LocalDateTime.now() :
                            memberNotifyDTO.getTradingTime());
                    //积分记录主键
                    io2.setIntegralRecordGuid(helper.generateGuid(RedisConstant.INTEGRAL_RECORD_GUID));
                    //操作人
                    io2.setStaffGuid(memberNotifyDTO.getOperationStaffGuid());
                    io2.setStaffName(memberNotifyDTO.getOperationStaffName());
                    //积分类型设置为反结账退回
                    if (Optional.ofNullable(io.getType()).orElse(-1).equals(IntegralTransactionTypeEnum.OFFSET.getCode())) {
                        io2.setType(IntegralTransactionTypeEnum.RECOVERY_BACK.getCode());
                    }
                    //求之前消费的总积分
                    if (Optional.ofNullable(io.getType()).orElse(-1).equals(IntegralTransactionTypeEnum.CONSUME.getCode())) {
                        io2.setType(IntegralTransactionTypeEnum.RECOVERY_DEDUCTION.getCode());
                        consume_integral = consume_integral + Math.abs(io.getTransactionIntegral());
                    }
                    //本次积分=原来积分的相反值
                    io2.setTransactionIntegral(io.getTransactionIntegral() * -1);
                    //剩余积分等于会员表原始剩余积分+本次积分
                    organinalResidualIntegral = organinalResidualIntegral + io2.getTransactionIntegral();

                    io2.setResidualIntegral(organinalResidualIntegral);
                    io2.setIsDelete(false);
                    io2.setAccountNumber(io.getAccountNumber());
                    //订单号
                    io2.setOrderNo(io.getOrderNo());
                    ios.add(io2);

                }
                integralRecordMapper.batchInsert(ios);
            }
            memberDO.setResidualIntegral(organinalResidualIntegral);
            memberDO.setTotalIntegral(memberDO.getTotalIntegral() - consume_integral);
            //判断是否要降级
            //所有等级按所需积分降序
            Collections.reverse(memberGradeDOList);
            for (int i = 0; i < memberGradeDOList.size(); i++) {
                if (memberDO.getTotalIntegral() >= memberGradeDOList.get(i).getNeedIntegral()) {
                    memberDO.setMemberGradeGuid(memberGradeDOList.get(i).getMemberGradeGuid());
                    break;
                }
            }
        }
        if (m1.getMessageType() != null) {
//            msgClientService.sendMessage(m1);
            sendMessageUtil.sendMessage(m1, memberNotifyDTO.getEnterpriseGuid(), memberNotifyDTO.getType());
        }
        memberMapper.updateByPrimaryKeySelective(memberDO);
        return transactionPrintBO;
    }

    @Autowired
    private SendMessageUtil sendMessageUtil;

    @Override
    public Boolean checkBalance(MemberNotifyDTO memberNotifyDTO) {
        MemberDO memberByGuidForUpdate = memberMapper.getMemberByGuidForUpdate(memberNotifyDTO.getMemberGuid());
        if (BigDecimalUtil.greaterEqual(memberByGuidForUpdate.getBalance(), memberNotifyDTO.getAmount())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;

    }

    @Override
    public MemberConsumeRespDTO getMemberRechargeRecord(@Valid DailyReqDTO request) {
        MemberConsumeRespDTO  memberConsumeRespDTO = transactionRecordMapper.getMemberRechargeRecord(request);
        List<GatherRespDTO> list = transactionRecordMapper.getBusinessMemberRechargeEarnings(request); //查询充值收入项
        List<AmountItemDTO> prepaidItems = new ArrayList<>();
       if(list !=null) {
           for (GatherRespDTO gatherRespDTO: list) {
               AmountItemDTO  amountItemDTO = new  AmountItemDTO();
               amountItemDTO.setAmount(gatherRespDTO.getPrepaidAmount());
               amountItemDTO.setCode(gatherRespDTO.getGatherCode());
               amountItemDTO.setName(gatherRespDTO.getGatherName());
               prepaidItems.add(amountItemDTO);
           }
           memberConsumeRespDTO.setPrepaidItems(prepaidItems);
       }
        return memberConsumeRespDTO;
    }

    @Override
    public MemberConsumeRespDTO getMemberRechargeStore(@Valid HandoverReqDTO request) {
        MemberConsumeRespDTO  memberConsumeRespDTO = transactionRecordMapper.getMemberRechargeStore(request);
        List<GatherRespDTO> list = transactionRecordMapper.getBusinessMemberRechargeEarningsHandover(request); //查询充值收入项
        List<AmountItemDTO> prepaidItems = new ArrayList<>();
        if(list !=null) {
            for (GatherRespDTO gatherRespDTO: list) {
                AmountItemDTO  amountItemDTO = new  AmountItemDTO();
                amountItemDTO.setAmount(gatherRespDTO.getPrepaidAmount());
                // 此处支付方式与trade的定义不统一
                // 此处0:现金支付 ，1:聚合支付 ,2：银行卡支付
                // trade是 CASH(1, "现金支付"),AGG(2, "聚合支付"),CARD(3, "银联卡支付"),
                amountItemDTO.setCode(gatherRespDTO.getGatherCode() + 1);
                amountItemDTO.setName(gatherRespDTO.getGatherName());
                prepaidItems.add(amountItemDTO);
            }
            memberConsumeRespDTO.setPrepaidItems(prepaidItems);
        }
        return memberConsumeRespDTO;
    }

    @Override
    public List<GatherRespDTO> getBusinessMemberRechargeEarnings(@Valid DailyReqDTO request) {
        return transactionRecordMapper.getBusinessMemberRechargeEarnings(request);
    }
}
