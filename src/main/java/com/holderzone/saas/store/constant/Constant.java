package com.holderzone.saas.store.constant;

/**
 * <AUTHOR>
 * @description 常量类
 * @date 2021/8/23 12:14
 */
public class Constant {

    private Constant() {
    }


    // 数字类常量------------------------------------------------------------------------------------------------


    public static final Integer FALSE = 0;

    public static final Integer TRUE = 1;

    public static final Integer NUMBER_ZERO = 0;

    public static final Integer NUMBER_ONE = 1;

    /**
     * 手机号位数
     */
    public static final Integer PHONE_COUNT = 11;

    /**
     * 支付授权码长度
     */
    public static final Integer AUTH_CODE_LENGTH = 18;

    /**
     * OPENID长度界限
     */
    public static final Integer OPENID_COUNT_LIMIT = 20;

    public static final int VOLUME_CODE_TYPE = -1;

    public static final String FAST_FOOD_INTI_MARK = "001";


    // 字母类常量------------------------------------------------------------------------------------------------


    public static final String JSON = "json";

    public static final String UTF8 = "UTF-8";

    public static final String WX_AUTHORIZER_EVENT_KEY_AGAIN = "isAgain";

    public static final String WX_AUTHORIZER_EVENT_KEY_MYSELF = "myself";

    public static final String WX_AUTHORIZER_EVENT_KEY_OPENID = "openId";


    // 符号类常量------------------------------------------------------------------------------------------------


    public static final String EMPTY = "";

    public static final String ENCRYPTION_SYMBOL = "***";


    // 字符串类常量------------------------------------------------------------------------------------------------


    public static final String MSG_TO_ANDROID_SUBJECT = "商品服务消息";

    public static final String STORE_CANNOT_BE_NULL = "门店不能为空";

    public static final String STORE_UNDER_BRAND_IS_EMPTY = "品牌下门店为空";

    public static final String ITEM_UNDER_BRAND_IS_EMPTY = "品牌下商品为空";

    public static final String NOT_FOUND_PRICE_PLAN_INFO = "被使用中，未查询到菜谱方案信息";

    public static final String PRICE_PLAN_IS_IN_USE = "菜谱方案正在使用!";

    public static final String BRAND_CANNOT_BE_NULL = "品牌不能为空";

    public static final String PRICE_PLAN_CANNOT_BE_EMPTY = "方案不能为空";

    public static final String WRONG_PARAMS = "参数错误";

    public static final String DEFAULT_GROUP = "默认分组";

    public static final String DEFAULT_NUM_CANNOT_GT_PICK_NUM = "分组下默认选择的商品规格数不能大于分组的可选数";

    public static final String PICK_NUM_GT_ACTUAL_NUM = "分组的可选商品数大于实际可选数";

    public static final String NO_TYPE_WITHIN_STORES = "当前门店列表下无商品分类数据";

    public static final String NO_ITEM_WITHIN_STORES = "当前门店列表下无商品信息数据";

    public static final String SYSTEM_ERROR = "系统异常";

    public static final String WRONG_ITEM_STRUCTURE = "商品结构异常";

    public static final String OP_FAIL = "操作失败";

    public static final String DUPLICATE_UPC = "商品条码已存在";

    public static final String DUPLICATE_CODE = "sku简码已存在";

    public static final String DUPLICATE_RETAIL_CODE = "商品货号已存在";

    public static final String DUPLICATE_ITEM_NAME = "该商品名称已存在";

    public static final String TIME_CONFLICT = "时间冲突";

    public static final String ORDER_GUID_IS_EMPTY = "订单Guid为空";

    public static final String PAD_ORDER_GUID_IS_EMPTY = "下单表guid为空";

    public static final String PAD_COMBINE_ORDER_GUID_IS_EMPTY = "下单表combineOrderGuid为空";

    public static final String NO_RESULTS_FOUND = "未查询到结果";

    public static final String RECEIVED_ORDER_BATCH_INFOR_IS_EMPTY = "已接单的批次信息为空";

    public static final String GUESTS_QUANTITY_IS_EMPTY = "就餐人数为空";

    public static final String SMS_CODE_IS_EMPTY = "短信验证码为空";

    public static final String WECHAT_USER_INFORMATION_IS_EMPTY = "微信用户信息为空";

    public static final String NOT_LAST_UPDATE = "请更新到最新版本";

    public static final String NOT_EXIST_ACTIVITY = "活动不存在或者过期";

    public static final String NOT_EXIST_ACTIVITYGUID = "第三方活动guid不能为空";

    public static final String IS_THIRD_SHARE_TIPS = "验券失败：不可与其他团购活动叠加";

    public static final String ACTIVITY_WRONG = "套餐活动未关联商品无法选择";

    public static final String COUPON_CODE_INVALID = "验券失败：券码无效";

    public static final String COUPON_CODE_VOUCHER_INVALID = "验券失败：代金券请在结账时进行验证";

    public static final String COUPON_CODE_MAITON_INVALID = "验券失败：买单券请在结账时进行验证";

    public static final String COUPON_UN_BIND_STORE = "请在管理后台绑定团购平台门店";

    public static final String ORDER_NOT_EXIST = "订单不存在";

    public static final String ORDER_REFUND_STATUS_ERROR = "订单发起退款失败";

    public static final String ORDER_REFUND_END = "当前订单已全部退款";

    public static final String ORDER_ADJUST_REFUND_ERROR = "已调整的订单不能发起退款";

    public static final String TEMPLATE_NOT_EXIST = "模版不存在";

    public static final String UN_FULL_CONDITION = "订单不满足使用条件";
    public static final String OTHER_DISCOUNT_SHARE = "不能与其它优惠共享";

    public static final String WX_CP_AUTHORIZE_ERROR = "授权失败";

    public static final String WX_CP_QR_CODE_PARAMS_ERROR = "二维码参数异常";

    public static final int BATCH_AGG_REFUND_REQ_PARTITION = 2;

    public static final String SPECIALS_ACTIVITY_MUTUALLY_EXCLUSIVE = "已选中活动与其他营销活动、会员折扣、会员价、代金券，互斥！";


}
