package com.holderzone.saas.store.constant.member;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RocketMQKeysConstant
 * @date 2018/09/05 10:33
 * @description
 * @program holder-saas-store-order
 */
public interface RocketMQKeysConstant {

    String MEMBER_NOTIFY_TOPIC = "member-notify-topic";

    String ORDER_NOTIFY_TOPIC = "order-notify-topic";

    String MEMBER_NOTIFY_TAG = "member-notify-tag";
    String INIT_DATABASE_TOPIC="data-init-database-topic";
//    String INIT_TABLE_INFO_TAG="data-init-database-topic-tag";
    String INIT_TABLE_INFO_TAG="holder-saas-store-member";

    String ORDER_NOTIFY_TRADING_TAG = "order-notify-trading-tag";

    String MEMBER_NOTIFY_CONSUMER_GROUP = "member-notify-consumer-group";

    String ORDER_NOTIFY_CONSUMER_GROUP = "order-notify-consumer-group";


    String MEMBER_DEFAULT_GRADE="member_default_grade";

    String CASH_BOX_NOTIFY_TAG = "cash-box-notify-tag";

    String CASH_BOX_NOTIFY_TOPIC = "cash-box-notify-topic";


}
