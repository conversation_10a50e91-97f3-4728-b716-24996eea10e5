package com.holderzone.saas.store.constant.takeaway;

import java.math.BigDecimal;

public interface TakeawayConstants {


    /**
     * 外卖自动接单
     */
    interface TakeawayAutoAcceptConstants {
        String TIMEOUT_PRINT_QUEUE = "TIMEOUT_PRINT_QUEUE_HASH";
    }

    interface TikTokHeader {

        /**
         * 同一实体下同一action的msg_id相同，服务商可根据msg_id对于消息去重
         */
        String MSG_ID_KEY = "Msg-Id";

        /**
         * 抖音侧签名，服务商可根据签名判断该消息是否来自抖音开放平台
         */
        String SIGNATURE_KEY = "X-Douyin-Signature";

        /**
         * 固定值application/json
         */
        String CONTENT_TYPE_KEY = "Content-Type";

        String CONTENT_TYPE_VALUE = "application/json";

        String ACCESS_TOKEN = "access-token";

    }

    interface TikTok {

        String GRANT_TYPE = "client_credential";

    }

    String BATCH_QUERY_ITEM_OPERATE = "批量获取菜品";

    String TIKTOK_ACCESS_TOKEN = "TIKTOK_ACCESS_TOKEN";

    int BATCH_QUERY_ITEM_PARTITION = 5;

    BigDecimal ONE_HUNDRED = new BigDecimal("100");
}
