package com.holderzone.saas.store.constant.member;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RocketMQKeysConstant
 * @date 2018/09/05 10:33
 * @description
 * @program holder-saas-store-order
 */
public interface MemberSyncConstant {

    String MEMBER_SYNC_PROPERTY = "userInfo";

    String MEMBER_ERP_SYNC_TOPIC = "member-erp-sync-topic";

    String MEMBER_ERP_SAVE_TAG = "member-erp-save-tag";

    String MEMBER_ERP_MODIFY_TAG = "member-erp-modify-tag";

    String MEMBER_ERP_DELETE_TAG = "member-erp-delete-tag";

    String MEMBER_ERP_SYNC_CONSUMER_GROUP = "member-erp-sync-consumer-group";

    String MEMBER_BRAND_SYNC_TOPIC = "member-brand-sync-topic";

    String MEMBER_BRAND_SAVE_TAG = "member-brand-save-tag";

    String MEMBER_BRAND_MODIFY_TAG = "member-brand-modify-tag";

    String MEMBER_BRAND_DELETE_TAG = "member-brand-delete-tag";

    String MEMBER_BRAND_SYNC_CONSUMER_GROUP = "member-brand-sync-consumer-group";

    String MEMBER_STORE_SYNC_TOPIC = "member-store-save-topic";

    String MEMBER_STORE_SAVE_TAG = "member-store-save-tag";

    String MEMBER_STORE_MODIFY_TAG = "member-store-modify-tag";

    String MEMBER_STORE_DELETE_TAG = "member-store-delete-tag";

    String MEMBER_STORE_SYNC_CONSUMER_GROUP = "member-store-sync-consumer-group";

    String MEMBER_STORE_ITEM_SYNC_TOPIC = "member-store-item-sync-topic";

    String MEMBER_STORE_SKU_SAVE_TAG = "member-store-sku-save-tag";

    String MEMBER_STORE_SKU_MODIFY_TAG = "member-store-sku-modify-tag";

    String MEMBER_STORE_SKU_DELETE_TAG = "member-store-sku-delete-tag";

    String MEMBER_STORE_ITEM_TYPE_MODIFY_TAG = "member-store-item-type-modify-tag";

    String MEMBER_STORE_ITEM_TYPE_DELETE_TAG = "member-store-item-type-delete-tag";

    String MEMBER_STORE_ITEM_MODIFY_TAG = "member-store-item-modify-tag";

    String MEMBER_ITEM_SYNC_CONSUMER_GROUP = "member-item-sync-consumer-group";


    public static final String ALLIANCE_ID  = "1fb529b8da78459ca64187f94dc3ae3e";
}
