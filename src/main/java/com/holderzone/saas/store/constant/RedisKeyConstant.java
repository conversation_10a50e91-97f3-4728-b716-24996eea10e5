package com.holderzone.saas.store.constant;

/**
 * <AUTHOR>
 * @date 2024/3/22
 * @description redis key
 */
public class RedisKeyConstant {

    private RedisKeyConstant() {
    }

    /**
     * 出餐码
     */
    public static final String FOOD_FINISH_BAR_CODE = "foodFinishBarCode:";

    /**
     * 外卖商品信息
     */
    public static final String TAKEOUT_ITEM = "takeoutItem:";

    /**
     * 满减满折活动
     * 商品展示
     */
    public static final String MEMBER_MARKET_ACTIVITY = "MemberMarketActivityList:%s:%s";

    /**
     * 满减满折活动
     * 订单结算
     */
    public static final String FULL_DISCOUNT_AND_REDUCTION_ACTIVITY_LIST = "FullDiscountAndReductionActivityList:%s:%s";

    /**
     * 限时特价活动
     */
    public static final String LIMIT_SPECIALS_ACTIVITY = "LimitSpecialsActivity:%s:%s";

    /**
     * 第N份优惠
     */
    public static final String NTH_ACTIVITY = "NTH_ACTIVITY:%s:%s";

    /**
     * 扫码查询到的商品id
     */
    public static final String SCAN_ORDER_ITEM_GUID = "ScanOrderItemGuid:%s";

    /**
     * 会员权益
     */
    public static final String MEMBER_RIGHTS = "MemberRights:%s";

    /**
     * 计算后商品信息
     */
    public static final String CALCULATE_ITEM_INFO = "CalculateItemInfo:%s:%s";

    /**
     * 会员权益缓存
     */
    public static final String MEMBER_INFO_CARD_RIGHTS_INFO_SUPPORT = "MemberInfoCardRightsInfoSupport:%s:%s";

    // LOCK_KEY------------------------------------------------------------------------------------------------

    /**
     * 多次支付锁
     */
    public static final String MULTIPLE_PAY_LOCK_KEY = "MultiplePay:%s-%d";

    /**
     * 预支付
     */
    public static final String ADVANCE_PAY_LOCK_KEY = "AdvancePay:%s";

}
