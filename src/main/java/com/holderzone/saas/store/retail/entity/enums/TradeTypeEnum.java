package com.holderzone.saas.store.retail.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradeTypeEnum
 * @date 2018/09/04 17:52
 * @description 订单交易模式枚举
 * @program holder-saas-store-trade
 */
public enum TradeTypeEnum {

    GENERAL_IN(1, "正常支付转入"),
    REFUND_OUT(2, "退单退款"),
    ;

    private int code;
    private String desc;

    TradeTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (TradeTypeEnum c : TradeTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
