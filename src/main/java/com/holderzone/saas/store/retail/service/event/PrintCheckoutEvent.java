package com.holderzone.saas.store.retail.service.event;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.retail.event.BaseOrderEvent;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintCheckoutEvent
 * @date 2019/09/21 16:18
 * @description //TODO
 * @program IdeaProjects
 */
@Data
public class PrintCheckoutEvent extends BaseOrderEvent {

    private RetailOrderDetailRespDTO dineinOrderDetailRespDTO;

    public PrintCheckoutEvent(BaseDTO baseDTO, RetailOrderDetailRespDTO dineinOrderDetailRespDTO) {
        setBaseDTO(baseDTO);
        this.dineinOrderDetailRespDTO = dineinOrderDetailRespDTO;
    }
}