package com.holderzone.saas.store.retail.service.handler.print;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailCheckOutDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.retail.anno.HandlerEventClass;
import com.holderzone.saas.store.retail.event.AbstractPrintHandler;
import com.holderzone.saas.store.retail.event.BaseOrderEvent;
import com.holderzone.saas.store.retail.service.event.PrintCheckoutEvent;
import com.holderzone.saas.store.retail.service.feign.StoreClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CheckoutPrintHandler
 * @date 2019/09/21 16:39
 * @description //TODO
 * @program IdeaProjects
 */
@Component
@Slf4j
@HandlerEventClass({PrintCheckoutEvent.class})
//@ConditionalOnClass(PrintCheckoutEvent.class)
public class CheckoutPrintHandler extends AbstractPrintHandler {

    @Autowired
    private StoreClientService storeClientService;

    @Autowired
    private DefaultRocketMqProducer defaultRocketMqProducer;

    @Override
    public boolean execute(BaseOrderEvent baseOrderEvent) {
        if (baseOrderEvent instanceof PrintCheckoutEvent) {
            return doExecute((PrintCheckoutEvent) baseOrderEvent);
        }
        return false;
    }


    public boolean doExecute(PrintCheckoutEvent printCheckoutEvent) {
        BaseDTO baseDTO = printCheckoutEvent.getBaseDTO();
        RetailOrderDetailRespDTO dineinOrderDetailRespDTO = printCheckoutEvent.getDineinOrderDetailRespDTO();
        PrintRetailCheckOutDTO printCheckOutDTO = new PrintRetailCheckOutDTO();
        setPrintBaseInfo(printCheckOutDTO, baseDTO, InvoiceTypeEnum.RETAIL_CHECKOUT, dineinOrderDetailRespDTO);
        internalPrintCheckout(printCheckOutDTO, dineinOrderDetailRespDTO);
        printCheckOutDTO.setOrderRemark(dineinOrderDetailRespDTO.getRemark());
//        log.info("255:remark={}", dineinOrderDetailRespDTO.getMark());
        Message messageCheckOut = getPrintMessage(printCheckOutDTO);
        return defaultRocketMqProducer.sendMessage(messageCheckOut);
    }

    private void internalPrintCheckout(PrintRetailCheckOutDTO printCheckOutDTO, RetailOrderDetailRespDTO
            dineinOrderDetailRespDTO) {
        internalPrintPreCheckout(printCheckOutDTO, dineinOrderDetailRespDTO, storeClientService);
        printCheckOutDTO.setCheckOutTime(DateTimeUtils.localDateTime2Mills(dineinOrderDetailRespDTO.getCheckoutTime()));
        printCheckOutDTO.setActuallyPay(dineinOrderDetailRespDTO.getActuallyPayFee());
        printCheckOutDTO.setPayRecordList(requirePayRecordList(dineinOrderDetailRespDTO));
        printCheckOutDTO.setChangedPay(Optional.ofNullable(dineinOrderDetailRespDTO.getChangeFee()).orElse(BigDecimal.ZERO));
//        printCheckOutDTO.setTradeMode(dineinOrderDetailRespDTO.getTradeMode());
    }


}