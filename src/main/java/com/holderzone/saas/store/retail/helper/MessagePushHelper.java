package com.holderzone.saas.store.retail.helper;

import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.retail.context.RequestContext;
import com.holderzone.saas.store.retail.service.feign.BusinessMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @className MessagePushHelper
 * @date 2018/10/25 15:28
 * @description
 * @program holder-saas-store-trade
 */
@Component
@Slf4j
public class MessagePushHelper {

    private final BusinessMessageService businessMessageService;

    @Autowired
    public MessagePushHelper(BusinessMessageService businessMessageService) {
        this.businessMessageService = businessMessageService;
    }

    public void recoveryMsgPush() {
        BusinessMessageDTO messageDTO = BusinessMessageDTO.builder()
                .subject("反结账老板助手通知")
                .messageType(BusinessMsgTypeEnum.RECOVERY.getId())
                .detailMessageType(BusinessMsgTypeEnum.RECOVERY.getId())
                .content(StringUtils.EMPTY)
                .platform("2")
                //老板助手接受企业下所有门店，跟安卓约定storeGuid写成messageType
                .storeGuid(RequestContext.getStoreGuid())
                .storeName(RequestContext.getStoreName())
                .build();
        businessMessageService.msg(messageDTO);
    }

}
