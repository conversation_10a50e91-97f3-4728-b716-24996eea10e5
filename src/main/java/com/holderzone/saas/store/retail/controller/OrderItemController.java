package com.holderzone.saas.store.retail.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.retail.bill.request.ReturnItemReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.CancelFreeItemReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.PriceChangeItemReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailAddGoodsReqDTO;
import com.holderzone.saas.store.dto.retail.item.BatchItemReturnOrFreeReqDTO;
import com.holderzone.saas.store.retail.service.DineInItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderItemController
 * @date 2019/01/04 8:53
 * @description 正餐商品接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/order_item")
@Api(description = "零售商品接口")
@Slf4j
public class OrderItemController {

    private final DineInItemService dineInItemService;

    @Autowired
    public OrderItemController(DineInItemService dineInItemService) {
        this.dineInItemService = dineInItemService;
    }

    @ApiOperation(value = "零售商品新增接口", notes = "零售商品新增接口")
    @PostMapping("/retail_add_item")
    public String retailAddItem(@RequestBody RetailAddGoodsReqDTO retailAddGoodsReqDTO) {
        log.info("零售商品新增接口入参：{}", JacksonUtils.writeValueAsString(retailAddGoodsReqDTO));
        return dineInItemService.addItem(retailAddGoodsReqDTO);
    }

    @ApiOperation(value = "批量赠送接口", notes = "批量赠送接口")
    @PostMapping("/free")
    public BatchItemReturnOrFreeReqDTO freeItem(@RequestBody BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO) {
        return dineInItemService.returnOrFreeItem(batchItemReturnOrFreeReqDTO, Boolean.FALSE);

    }

    @ApiOperation(value = "批量取消赠送接口", notes = "批量取消赠送接口")
    @PostMapping("/cancel_free")
    public Boolean cancelFree(@RequestBody CancelFreeItemReqDTO cancelFreeItemReqDTO) {
        return dineInItemService.cancelFree(cancelFreeItemReqDTO);

    }

    @ApiOperation(value = "改价折扣接口", notes = "批量取消赠送接口")
    @PostMapping("/change_price")
    public Boolean changePrice(@RequestBody PriceChangeItemReqDTO priceChangeItemReqDTO) {
        return dineInItemService.changePrice(priceChangeItemReqDTO);
    }

    @ApiOperation(value = "退货", notes = "批量退货接口")
    @PostMapping("/return_items")
    public Boolean returnItems(@RequestBody ReturnItemReqDTO returnItemReqDTO) {
        log.info("零售商品退货接口入参：{}", JacksonUtils.writeValueAsString(returnItemReqDTO));
        return dineInItemService.returnItems(returnItemReqDTO);
    }

    @ApiOperation(value = "退单", notes = "退单接口")
    @PostMapping("/return_order")
    public Boolean returnOrder(@RequestBody ReturnItemReqDTO returnItemReqDTO) {
        log.info("零售商品退单接口入参：{}", JacksonUtils.writeValueAsString(returnItemReqDTO));
        return dineInItemService.returnOrder(returnItemReqDTO);
    }

}
