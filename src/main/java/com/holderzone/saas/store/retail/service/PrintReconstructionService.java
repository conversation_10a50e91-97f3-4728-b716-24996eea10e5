package com.holderzone.saas.store.retail.service;

import com.holderzone.saas.store.retail.event.BaseOrderEvent;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintAfterCodingService
 * @date 2019/09/21 11:20
 * @description //TODO
 * @program IdeaProjects
 */
public interface PrintReconstructionService {

    public boolean publish(BaseOrderEvent baseOrderEvent);

    public void asynPublish(BaseOrderEvent baseOrderEvent);
}