package com.holderzone.saas.store.retail.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 零售订单交易记录
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_retail_transaction_record")
public class RetailTransactionRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 银行流水号
     */
    private String bankTransactionId;


    /**
     * 交易金额
     */
    private BigDecimal amount;


    /**
     * 交易类型，1:正常支付转入，2:退单退款
     */
    private Integer tradeType;

    /**
     * 1：待支付 2：支付中 3：支付失败 4：支付成功
     */
    private Integer state;

    /**
     * 支付方式 1：现金支付，2：聚合支付，3：银行卡支付，4:会员卡支付，10：其他支付方式
     */
    private Integer paymentType;

    /**
     * 支付方式名
     */
    private String paymentTypeName;

    /**
     * 营业日时间
     */
    private LocalDate businessDay;

    /**
     * 人脸支付失败时安卓自己随机加的3位数字
     */
    private String faceCode;

    /**
     * 操作人员guid
     */
    private String staffGuid;

    /**
     * 操作人名字
     */
    private String staffName;

    /**
     * 门店名
     */
    private String storeName;

    /**
     * 门店guid
     */
    private String storeGuid;

}
