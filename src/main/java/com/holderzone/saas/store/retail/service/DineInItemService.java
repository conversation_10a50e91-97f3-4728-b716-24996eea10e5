package com.holderzone.saas.store.retail.service;

import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.retail.bill.request.ReturnItemReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.CancelFreeItemReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.PriceChangeItemReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailAddGoodsReqDTO;
import com.holderzone.saas.store.dto.retail.item.BatchItemReturnOrFreeReqDTO;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInItemService
 * @date 2018/09/04 16:08
 * @description 订单菜品操作服务类
 * @program holder-saas-store-trade
 */
public interface DineInItemService {


    /**
     * 零售添加商品
     *
     * @param createFastFoodReqDTO
     * @return
     */
    String addItem(RetailAddGoodsReqDTO createFastFoodReqDTO);

    /**
     * 批量新增菜品（正餐，快餐，微信）
     *
     * @param createDineInOrderReqDTO
     * @return
     */
    EstimateItemRespDTO batchAddItems(CreateDineInOrderReqDTO createDineInOrderReqDTO, Boolean isFastFood);


    /**
     * 赠、退菜
     *
     * @param batchItemReturnOrFreeReqDTO
     * @param isReturn
     * @return
     */
    BatchItemReturnOrFreeReqDTO returnOrFreeItem(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO, Boolean
            isReturn);

    Boolean cancelFree(CancelFreeItemReqDTO cancelFreeItemReqDTO);

    Boolean changePrice(PriceChangeItemReqDTO priceChangeItemReqDTO);

    Boolean returnItems(ReturnItemReqDTO returnItemReqDTO);

    @Transactional(isolation = Isolation.SERIALIZABLE)
    Boolean returnOrder(ReturnItemReqDTO returnItemReqDTO);
}
