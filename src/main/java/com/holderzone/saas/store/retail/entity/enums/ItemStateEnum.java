package com.holderzone.saas.store.retail.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemStateEnum
 * @date 2018/10/23 11:54
 * @description
 * @program holder-saas-store-trade
 */
public enum ItemStateEnum {
    //点了菜
    GENERAL(1, "即起"),
    //暂时不上的菜
    HANG_UP(2, "挂起"),
    //暂时不上的菜  叫上
    CALL_UP(3, "叫起"),
    WAIT(4, "待制作"),
    MAKING(5, "制作中"),
    READY_OUT_OF_KITCHEN(6, "待出堂"),
    OUT_OF_KITCHEN(7, "已出堂"),
    SERVED(8, "已上菜"),
    ;

    private int code;
    private String desc;

    ItemStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (ItemStateEnum c : ItemStateEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
