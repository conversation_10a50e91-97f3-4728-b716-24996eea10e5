package com.holderzone.saas.store.retail.service.impl.mp;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.retail.entity.constant.OrderRedisConstant;
import com.holderzone.saas.store.retail.entity.domain.RetailOrderDO;
import com.holderzone.saas.store.retail.helper.RedisHelper;
import com.holderzone.saas.store.retail.mapper.RetailOrderMapper;
import com.holderzone.saas.store.retail.service.mp.RetailOrderService;
import com.holderzone.saas.store.retail.utils.RedisKeyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 订单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Service
public class RetailOrderServiceImpl extends ServiceImpl<RetailOrderMapper, RetailOrderDO> implements RetailOrderService {

    @Override
    public RetailOrderDO getByIdWithLock(Serializable id) {
        return getOne(new LambdaQueryWrapper<RetailOrderDO>().eq(RetailOrderDO::getGuid, id).last("for update"));
    }

}
