package com.holderzone.saas.store.retail.utils;


import com.holderzone.saas.store.retail.entity.constant.OrderRedisConstant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RedisKeyUtil
 * @date 2018/09/04 11:26
 * @description
 * @program holder-saas-store-trade
 */
public class RedisKeyUtil {

    /**
     * 生成redisKey
     *
     * @param businessGroup
     * @param businessKey
     * @return
     */
    public static String getKey(String businessGroup, String businessKey) {
        StringBuffer buffer = new StringBuffer();
        buffer.append(OrderRedisConstant.ORDER_GROUP).append(":");
        buffer.append(businessGroup).append(":");
        buffer.append(businessKey);
        return buffer.toString();
    }

    public static String getHstOrderItemKey(String businessKey) {
        return getKey(OrderRedisConstant.HST_ORDER_ITEM_GROUP, businessKey);
    }
}