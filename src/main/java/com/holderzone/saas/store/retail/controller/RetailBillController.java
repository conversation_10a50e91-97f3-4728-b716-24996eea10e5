package com.holderzone.saas.store.retail.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.member.request.MemberConsumeReqDTO;
import com.holderzone.saas.store.dto.member.response.MemberConsumeRespDTO;
import com.holderzone.saas.store.dto.retail.bill.request.RetailAggPayReqDTO;
import com.holderzone.saas.store.dto.retail.bill.request.RetailCalculateReqDTO;
import com.holderzone.saas.store.dto.retail.bill.request.RetailPayReqDTO;
import com.holderzone.saas.store.dto.retail.bill.response.RetailAggPayRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.dto.retail.pay.SaasNotifyDTO;
import com.holderzone.saas.store.retail.service.BillService;
import com.holderzone.saas.store.retail.service.CalculateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInOrderController
 * @date 2019/01/04 8:53
 * @description 结账接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/retail_bill")
@Api(description = "结账接口")
@Slf4j
public class RetailBillController {

    private final BillService billService;

    private final CalculateService calculateService;


    @Autowired
    public RetailBillController(BillService billService, CalculateService calculateService) {
        this.billService = billService;
        this.calculateService = calculateService;
    }

    @ApiOperation(value = "计算账单优惠", notes = "计算账单优惠")
    @PostMapping("/calculate")
    public RetailOrderDetailRespDTO calculate(@RequestBody RetailCalculateReqDTO retailCalculateReqDTO) {
        log.info("计算账单优惠入参：{}", JacksonUtils.writeValueAsString(retailCalculateReqDTO));
        return calculateService.calculate(retailCalculateReqDTO);
    }

    @ApiOperation(value = "支付", notes = "支付")
    @PostMapping("/pay")
    public Boolean pay(@RequestBody RetailPayReqDTO billPayReqDTO) {
        log.info("支付入参：{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        return billService.pay(billPayReqDTO);
    }

    @ApiOperation(value = "聚合支付", notes = "聚合支付")
    @PostMapping("/agg_pay")
    public RetailAggPayRespDTO aggPay(@RequestBody RetailAggPayReqDTO retailAggPayReqDTO) {
        log.info("聚合支付入参：{}", JacksonUtils.writeValueAsString(retailAggPayReqDTO));
        return billService.aggPay(retailAggPayReqDTO);
    }

    @ApiOperation(value = "聚合支付回调", notes = "聚合支付回调")
    @PostMapping("/callback")
    public String aggCallBack(@RequestBody SaasNotifyDTO saasNotifyDTO) {
        log.info("收到交易中心回调 aggPayPollingRespDTO={}", JacksonUtils.writeValueAsString(saasNotifyDTO));
        return billService.aggCallBack(saasNotifyDTO);
    }

    @ApiOperation(value = "会员消费记录列表", notes = "会员消费记录列表")
    @PostMapping("/member_consume_records")
    public Page<MemberConsumeRespDTO> memberConsumeRecords(@RequestBody MemberConsumeReqDTO memberConsumeReqDTO) {
        return billService.memberConsumeRecords(memberConsumeReqDTO);
    }

}
