package com.holderzone.saas.store.retail.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

@Configuration
public class TreadPoolConfig {


    /**
     * 结账线程
     *
     * @return
     */
    @Bean(value = "checkOutThreadPool")
    public ExecutorService buildCheckOutThreadPool() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("check-out-thread-%d").build();

        ExecutorService pool = new ThreadPoolExecutor(60, 360, 3000L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(1500), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());

        return pool;
    }

}