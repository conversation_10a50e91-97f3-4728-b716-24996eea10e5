package com.holderzone.saas.store.retail.service.feign;


import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.holder.saas.cmember.app.dto.account.response.CardFullInfoRespDTO;
import com.holderzone.holder.saas.cmember.app.dto.account.response.MemberAndCardInfoRespDTO;
import com.holderzone.holder.saas.member.dto.account.request.cmember.QueryStoreAndMemberAndCardReqDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberClientService
 * @date 2018/08/06 11:22
 * @description
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-cmember-base-service", fallbackFactory = MemberBaseClientService
        .MemberClientFallback.class)
public interface MemberBaseClientService {

    @GetMapping("/hsmca/member/{memberInfoCardGuid}/fullInfo")
    CardFullInfoRespDTO fullInfo(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid);

    @GetMapping("/hsmca/card/{memberInfoCardGuid}/hasMemberPrice")
    boolean hasMemberPrice(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid);

    @GetMapping("/hsmca/member/getMemberInfoAndCardTwo")
    MemberAndCardInfoRespDTO getMemberInfoAndCardTwo(@RequestBody QueryStoreAndMemberAndCardReqDTO
                                                             queryStoreAndMemberAndCardReqDTO);

    @Component
    class MemberClientFallback implements FallbackFactory<MemberBaseClientService> {

        private static final Logger logger = LoggerFactory.getLogger(MemberClientFallback.class);

        @Override
        public MemberBaseClientService create(Throwable throwable) {
            return new MemberBaseClientService() {
                @Override
                public CardFullInfoRespDTO fullInfo(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid) {
                    logger.error("获取会员信息调用异常e={}", throwable.getMessage());
                    throw new ParameterException("获取会员信息调用异常");
                }

                @Override
                public boolean hasMemberPrice(String memberInfoCardGuid) {
                    logger.error("获取会员是否能使用会员价 e={}", throwable.getMessage());
                    throw new ParameterException("获取会员是否能使用会员价");
                }

                @Override
                public MemberAndCardInfoRespDTO getMemberInfoAndCardTwo(QueryStoreAndMemberAndCardReqDTO
                                                                                queryStoreAndMemberAndCardReqDTO) {
                    logger.error("获取会员信息调用异常e={}", throwable.getMessage());
                    throw new ParameterException("获取会员信息调用异常");
                }
            };
        }
    }

}
