package com.holderzone.saas.store.retail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.cmember.app.dto.order.request.CancelPayReqDTO;
import com.holderzone.holder.saas.member.terminal.dto.retail.RequestRetailPayBaseInfo;
import com.holderzone.holder.saas.member.terminal.dto.retail.RequestRetailPayInfo;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.SubstractGoodsReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.SubstractRepertoryForTradeReqDTO;
import com.holderzone.saas.store.dto.member.request.MemberConsumeReqDTO;
import com.holderzone.saas.store.dto.member.response.MemberConsumeRespDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.AggPayPreTradingReqDTO;
import com.holderzone.saas.store.dto.pay.AggPayRespDTO;
import com.holderzone.saas.store.dto.pay.SaasAggPayDTO;
import com.holderzone.saas.store.dto.retail.BaseInfo;
import com.holderzone.saas.store.dto.retail.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.retail.bill.constant.PayPowerId;
import com.holderzone.saas.store.dto.retail.bill.request.RetailAggPayReqDTO;
import com.holderzone.saas.store.dto.retail.bill.request.RetailPayReqDTO;
import com.holderzone.saas.store.dto.retail.bill.response.RetailAggPayRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.dto.retail.pay.SaasNotifyDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.retail.context.RequestContext;
import com.holderzone.saas.store.retail.entity.bo.AggPayAttachDataBO;
import com.holderzone.saas.store.retail.entity.constant.CommonConstant;
import com.holderzone.saas.store.retail.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.retail.entity.domain.*;
import com.holderzone.saas.store.retail.entity.enums.*;
import com.holderzone.saas.store.retail.helper.DynamicHelper;
import com.holderzone.saas.store.retail.helper.PageAdapter;
import com.holderzone.saas.store.retail.helper.RedisHelper;
import com.holderzone.saas.store.retail.service.BillService;
import com.holderzone.saas.store.retail.service.DineInService;
import com.holderzone.saas.store.retail.service.PrintReconstructionService;
import com.holderzone.saas.store.retail.service.feign.*;
import com.holderzone.saas.store.retail.service.mp.RetailDiscountService;
import com.holderzone.saas.store.retail.service.mp.RetailOrderItemService;
import com.holderzone.saas.store.retail.service.mp.RetailOrderService;
import com.holderzone.saas.store.retail.service.mp.RetailTransactionRecordService;
import com.holderzone.saas.store.retail.transform.OrderTransform;
import com.holderzone.saas.store.retail.transform.PrintDTOAdapter;
import com.holderzone.saas.store.retail.transform.RetailTransform;
import com.holderzone.saas.store.retail.utils.CollectionUtil;
import com.holderzone.saas.store.retail.utils.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillServiceImpl
 * @date 2019/01/28 17:28
 * @description //TODO
 * @program holder-saas-store-dto
 */
@Slf4j
@Service
public class BillServiceImpl implements BillService {

    @Resource(name = "checkOutThreadPool")
    private ExecutorService checkOutThreadPool;

    private final RetailOrderService orderService;

    private final ErpClientService erpClientService;

    private final RetailOrderItemService retailOrderItemService;

    private final RetailMemberBaseMsmService retailMemberBaseMsmService;

    private final RetailDiscountService retailDiscountService;

    private final DynamicHelper dynamicHelper;

    private final RetailTransactionRecordService retailTransactionRecordService;

    private final AggPayClientService aggPayClientService;

    private final MessageClientService messageClientService;

    private final StoreClientService storeClientService;

    private final DineInService dineInService;

    private final RedisHelper redisHelper;

    private OrderTransform orderTransform = OrderTransform.INSTANCE;

    private RetailTransform retailTransform = RetailTransform.INSTANCE;

    @Autowired
    private PrintReconstructionService printReconstructionService;


    @Autowired
    public BillServiceImpl(RetailOrderService orderService, DynamicHelper dynamicHelper,
                           RetailTransactionRecordService retailTransactionRecordService,
                           AggPayClientService aggPayClientService, MessageClientService messageClientService,
                           StoreClientService storeClientService, DineInService dineInService,
                           RetailMemberBaseMsmService retailMemberBaseMsmService,
                           RedisHelper redisHelper, RetailDiscountService retailDiscountService,
                           RetailOrderItemService retailOrderItemService, ErpClientService erpClientService) {
        this.orderService = orderService;
        this.dynamicHelper = dynamicHelper;
        this.retailTransactionRecordService = retailTransactionRecordService;
        this.aggPayClientService = aggPayClientService;
        this.messageClientService = messageClientService;
        this.storeClientService = storeClientService;
        this.dineInService = dineInService;
        this.redisHelper = redisHelper;
        this.retailMemberBaseMsmService = retailMemberBaseMsmService;
        this.retailDiscountService = retailDiscountService;
        this.retailOrderItemService = retailOrderItemService;
        this.erpClientService = erpClientService;
    }


    @Override
    @Transactional
    public Boolean pay(RetailPayReqDTO retailBillPayReqDTO) {
        RetailOrderDO orderDO = orderService.getById(retailBillPayReqDTO.getOrderGuid());
        //订单结账的时候生成orderNo
        String orderNo = redisHelper.generateOrderNo("", RequestContext.getStoreGuid());
        orderDO.setOrderNo(orderNo);
        if (orderDO.isUnfinished()) {
            throw new ParameterException("订单状态不允许结账");
        }
        orderDO.setState(StateEnum.SUCCESS.getCode());
        orderDO.setCheckoutStaffGuid(RequestContext.getUserGuid());
        orderDO.setCheckoutStaffName(RequestContext.getUserName());
        orderDO.setCheckoutStaffAccount(RequestContext.getAccount());
        orderDO.setCheckoutTime(LocalDateTime.now());
        orderDO.setCheckoutDeviceType(retailBillPayReqDTO.getDeviceType());
        orderDO.setOrderFee(retailBillPayReqDTO.getOrderFee());
        orderDO.setChangeFee(retailBillPayReqDTO.getChangeFee());
        orderDO.setActuallyPayFee(retailBillPayReqDTO.getActuallyPayFee());
        boolean memberPay = CommonUtil.hasGuid(orderDO.getMemberGuid());
        boolean useMemberIntegral = false;
        LocalDate businessDay = CommonUtil.getBusinessDay(storeClientService.queryStoreBizByGuid(RequestContext
                .getStoreGuid()));

        List<RetailPayReqDTO.Payment> payments = retailBillPayReqDTO.getPayments();
        List<Long> guids = dynamicHelper.generateGuids(GuidKeyConstant.HST_TRANSACTION_RECORD, payments.size());
        List<RetailTransactionRecordDO> saveList = new ArrayList<>();
        for (RetailPayReqDTO.Payment payment : payments) {
            RetailTransactionRecordDO transactionRecordDO = new RetailTransactionRecordDO();
            Long guid = guids.remove(0);
            transactionRecordDO.setGuid(guid);
            transactionRecordDO.setOrderGuid(Long.valueOf(retailBillPayReqDTO.getOrderGuid()));
            transactionRecordDO.setAmount(payment.getAmount());
            transactionRecordDO.setPaymentType(payment.getPaymentType());
            //自定义支付
            if (payment.getPaymentType() == 10) {
                transactionRecordDO.setPaymentTypeName(payment.getPaymentTypeName());
            } else {
                transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.getDesc(payment.getPaymentType()));
            }
            //人脸支付字段
            if (payment.getPaymentType().equals(PaymentTypeEnum.FACE.getCode())) {
                if (StringUtils.isEmpty(payment.getBankTransactionId())) {
                    throw new ParameterException("人脸支付流水号不能为空");
                }
                transactionRecordDO.setBankTransactionId(payment.getBankTransactionId());
                transactionRecordDO.setFaceCode(payment.getFaceCode());
            }
            if (payment.getPaymentType().equals(PaymentTypeEnum.CARD.getCode())) {
                transactionRecordDO.setBankTransactionId(payment.getBankTransactionId());
                transactionRecordDO.setFaceCode(payment.getFaceCode());
            }
            transactionRecordDO.setStaffGuid(RequestContext.getUserGuid());
            transactionRecordDO.setStaffName(RequestContext.getUserName());
            transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
            transactionRecordDO.setStoreGuid(RequestContext.getStoreGuid());
            transactionRecordDO.setStoreName(RequestContext.getStoreName());
            transactionRecordDO.setTradeType(TradeTypeEnum.GENERAL_IN.getCode());
            transactionRecordDO.setBusinessDay(businessDay);
            if (memberPay && payment.getPaymentType().equals(PaymentTypeEnum.MEMBER.getCode())) {
                String memberConsumptionGuid = memberPayRetail(orderDO, payment, retailBillPayReqDTO);
                useMemberIntegral = true;
                orderDO.setMemberConsumptionGuid(memberConsumptionGuid);
            }
            saveList.add(transactionRecordDO);
        }
        log.warn("会员支付安卓入参：{}，guid：{}，orderNo：{}", JacksonUtils.writeValueAsString(retailBillPayReqDTO), orderDO
                        .getGuid()
                , orderDO.getOrderNo());
        //其他支付方式扣会员积分
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = retailBillPayReqDTO.getDiscountFeeDetailDTOS();
        Map<Integer, DiscountFeeDetailDTO> discountFeeDetailDTOMap = CollectionUtil.toMap(discountFeeDetailDTOS,
                "discountType");
        //除了会员余额支付之外的和会员有关系的支付
        boolean flag = true;
        if (discountFeeDetailDTOMap.get(DiscountTypeEnum.MEMBER_GROUPON.getCode()) == null) {
            flag = false;
        }
        Boolean withMember = (memberPay || flag) && !useMemberIntegral;
        if (withMember) {
            String memberConsumptionGuid = memberPayRetail(orderDO, null, retailBillPayReqDTO);
            orderDO.setMemberConsumptionGuid(memberConsumptionGuid);
        }
        retailTransactionRecordService.saveBatch(saveList);
        orderDO.setBusinessDay(businessDay);
        orderService.updateById(orderDO);

        //扣减库存
        saleOutRepertory(retailBillPayReqDTO.getOrderGuid());
//        String redisKey = RedisKeyUtil.getHstCalcKey(String.valueOf(orderDO.getGuid()));
//        Map<String, String> discountMap = JSON.parseObject(redisHelper.get(redisKey), HashMap.class);
        //支付成功之后更新优惠金额
        List<RetailDiscountDO> discountDOS = retailTransform.discountFeeDetailDTOS2discountDOS2(discountFeeDetailDTOS);
        for (RetailDiscountDO retail : discountDOS) {
            Long guid = dynamicHelper.generateGuid(GuidKeyConstant.HST_RETAIL_DISCOUNT);
            retail.setGuid(guid);
            retail.setOrderGuid(orderDO.getGuid());
            retail.setStoreGuid(orderDO.getStoreGuid());
            retail.setStoreName(orderDO.getStoreName());
            retail.setDiscountState(0);
            retail.setStaffGuid(RequestContext.getUserGuid());
            retail.setStaffName(RequestContext.getUserName());
        }
        //之前是calculate保存时，存入数据库，结账时去修改。现在是calculate不保存，结账时去插入。
        retailDiscountService.saveBatch(discountDOS);


        //异步执行结账成功后的打印等操作
        RetailOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(retailBillPayReqDTO.getOrderGuid());
        UserInfoDTO userInfo = RequestContext.getUserInfo();
        checkOutThreadPool.execute(() -> {
            RequestContext.put(JacksonUtils.writeValueAsString(userInfo));
            dynamicHelper.changeDatasource(userInfo.getEnterpriseGuid());
            printReconstructionService.asynPublish(PrintDTOAdapter.orderDetail2CheckoutEvent(retailBillPayReqDTO,
                    orderDetail));
        });

        return Boolean.TRUE;
    }

    /**
     * 支付成功扣减库存
     *
     * @param orderGuid 订单guid
     */
    private void saleOutRepertory(String orderGuid) {
        List<RetailOrderItemDO> itemList = retailOrderItemService.listByOrderGuid(Long.valueOf(orderGuid));
        SubstractRepertoryForTradeReqDTO saleOutRepertoryDTO = new SubstractRepertoryForTradeReqDTO();
        List<SubstractGoodsReqDTO> saleItemsDTOs = new ArrayList<>();
        for (RetailOrderItemDO item : itemList) {
            SubstractGoodsReqDTO sub = new SubstractGoodsReqDTO();
            sub.setCount(item.getCurrentCount().add(item.getFreeCount()));
            sub.setGoodsGuid(item.getItemGuid());
            sub.setUnitPrice(item.getPrice().subtract(item.getTotalDiscountFee()));
            saleItemsDTOs.add(sub);
        }
        saleOutRepertoryDTO.setInvoiceNo(orderGuid);
        saleOutRepertoryDTO.setInvoiceType(3);
        saleOutRepertoryDTO.setDetailList(saleItemsDTOs);

        log.info("扣减库存入参：saleOutRepertoryDTO={}", saleOutRepertoryDTO);
        boolean erpResult = erpClientService.saleOutRepertory(saleOutRepertoryDTO);
        log.info("扣减库存结果：{}", erpResult);
    }

    public List<RetailDiscountDO> changeDiscount(List<RetailDiscountDO> discountDOS, Map<String, String> discountMap,
                                                 RetailOrderDO orderDO) {
        //1.设置guid，orderGuid
        for (RetailDiscountDO dicountDO : discountDOS) {
            Long guid = dynamicHelper.generateGuid(GuidKeyConstant.HST_RETAIL_DISCOUNT);
            dicountDO.setGuid(guid);
            dicountDO.setOrderGuid(orderDO.getGuid());
            dicountDO.setStoreGuid(orderDO.getStoreGuid());
            dicountDO.setStoreName(orderDO.getStoreName());
            dicountDO.setOrderItemGuid(dicountDO.getOrderItemGuid());
            dicountDO.setDiscountState(0);
            dicountDO.setStaffGuid(RequestContext.getUserGuid());
            dicountDO.setStaffName(RequestContext.getUserName());
        }
        //2.若缓存中没数据，直接返回
        if (ObjectUtils.isEmpty(discountMap)) {
            return discountDOS;
        }

        //3.把缓存的数据组装进去
        for (RetailDiscountDO dicountDO : discountDOS) {
            String jsonStr = discountMap.get(String.valueOf(dicountDO.getDiscountType()));
            if (StringUtils.isEmpty(jsonStr)) {
                continue;
            }
            DiscountDO discountRedis = JacksonUtils.toObject(DiscountDO.class, jsonStr);
            if (!ObjectUtils.isEmpty(discountRedis.getDiscount())) {
                dicountDO.setDiscount(discountRedis.getDiscount());
            }
            if (!ObjectUtils.isEmpty(discountRedis.getDiscountFee())) {
                dicountDO.setDiscountFee(discountRedis.getDiscountFee());
            }
        }
        return discountDOS;
    }


    @Override
    public String memberPayRetail(RetailOrderDO orderDO, RetailPayReqDTO.Payment payment, RetailPayReqDTO
            billPayReqDTO) {
        RequestRetailPayInfo confirmPayReqDTO = new RequestRetailPayInfo();

        RequestRetailPayBaseInfo baseInfoReqDTO = new RequestRetailPayBaseInfo();
        log.info("会员支付参数1：memberInfoCardGuid={},memberInfoGuid={}", orderDO.getMemberCardGuid(), orderDO
                .getMemberGuid());
        log.info("会员支付参数2：memberInfoCardGuid={}", billPayReqDTO.getMemberInfoCardGuid());
        baseInfoReqDTO.setMemberInfoCardGuid(orderDO.getMemberCardGuid());
        baseInfoReqDTO.setEnterpriseGuid(RequestContext.getEnterpriseGuid());
        baseInfoReqDTO.setEnterpriseName("");
        baseInfoReqDTO.setBrandGuid("");
        baseInfoReqDTO.setBrandName("");
        baseInfoReqDTO.setMemberInfoGuid(orderDO.getMemberGuid());
        baseInfoReqDTO.setStoreGuid(RequestContext.getStoreGuid());
        baseInfoReqDTO.setStoreName(RequestContext.getStoreName());

        confirmPayReqDTO.setBaseInfoReqDTO(baseInfoReqDTO);
        confirmPayReqDTO.setUseIntegral(billPayReqDTO.getUseIntegral());
        log.info("orderGuid={}", String.valueOf(orderDO.getGuid()));
        confirmPayReqDTO.setOrderGuid(String.valueOf(orderDO.getGuid()));

        if (orderDO.getDeviceType().equals(BaseDeviceTypeEnum.All_IN_ONE.getCode())) {
            confirmPayReqDTO.setOrderSource(1);
        } else if (orderDO.getDeviceType().equals(BaseDeviceTypeEnum.POS.getCode())) {
            confirmPayReqDTO.setOrderSource(2);
        } else if (orderDO.getDeviceType().equals(BaseDeviceTypeEnum.WECHAT.getCode())) {
            confirmPayReqDTO.setOrderSource(0);
        } else {
            confirmPayReqDTO.setOrderSource(2);
        }
        confirmPayReqDTO.setOrderPaymentAmount(billPayReqDTO.getActuallyPayFee());
        confirmPayReqDTO.setOrderDiscountAmount(billPayReqDTO.getIntegralDiscountMoney());
        confirmPayReqDTO.setConsumptionTime(DateTimeUtils.now());
        confirmPayReqDTO.setConsumptionGuid(billPayReqDTO.getMemberConsumptionGuid());
        confirmPayReqDTO.setOrderNumber(orderDO.getOrderNo());

        log.warn("会员支付入参：{}，guid：{}，orderNo：{}", JacksonUtils.writeValueAsString(confirmPayReqDTO), orderDO.getGuid()
                , orderDO.getOrderNo());
        String pay = retailMemberBaseMsmService.payOrder(confirmPayReqDTO);
        log.warn("会员支付返回：{}", pay);

        return pay;
    }

    @Override
    public RetailAggPayRespDTO aggPay(RetailAggPayReqDTO retailAggPayReqDTO) {
        RetailAggPayRespDTO retailAggPayRespDTO = new RetailAggPayRespDTO();
        RetailTransactionRecordDO transactionRecordDO = new RetailTransactionRecordDO();
        List<RetailTransactionRecordDO> saveList = new ArrayList<>();
        Long guid = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
        transactionRecordDO.setGuid(guid);
        transactionRecordDO.setOrderGuid(Long.valueOf(retailAggPayReqDTO.getOrderGuid()));
        transactionRecordDO.setAmount(retailAggPayReqDTO.getAmount());
        transactionRecordDO.setPaymentType(PaymentTypeEnum.AGG.getCode());
        transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.AGG.getDesc());
        transactionRecordDO.setStaffGuid(RequestContext.getUserGuid());
        transactionRecordDO.setStaffName(RequestContext.getUserName());
        transactionRecordDO.setState(TradeStateEnum.READY.getCode());
        transactionRecordDO.setStoreGuid(RequestContext.getStoreGuid());
        transactionRecordDO.setStoreName(RequestContext.getStoreName());
        transactionRecordDO.setTradeType(TradeTypeEnum.GENERAL_IN.getCode());
        saveList.add(transactionRecordDO);

        Boolean hasClientMemberPay = Boolean.FALSE;
        //如果是最后一次，更新金额
        if (retailAggPayReqDTO.getLast()) {
            List<DiscountFeeDetailDTO> discountFeeDetailDTOS = retailAggPayReqDTO.getDiscountFeeDetailDTOS();
            List<RetailDiscountDO> discountDOS = retailTransform.discountFeeDetailDTOS2discountDOS2
                    (discountFeeDetailDTOS);
            //会员支付
            RetailOrderDO orderDO = orderService.getById(retailAggPayReqDTO.getOrderGuid());
            boolean memberPay = CommonUtil.hasGuid(orderDO.getMemberGuid());
            List<DiscountFeeDetailDTO> discountFeeDetailDTOS1 = retailAggPayReqDTO.getDiscountFeeDetailDTOS();
            Map<Integer, DiscountFeeDetailDTO> discountFeeDetailDTOMap = CollectionUtil.toMap(discountFeeDetailDTOS1,
                    "discountType");

            orderDO.setOrderFee(retailAggPayReqDTO.getOrderFee());
            orderDO.setActuallyPayFee(retailAggPayReqDTO.getActuallyPayFee());
            orderDO.setCheckoutDeviceType(retailAggPayReqDTO.getDeviceType());
            orderDO.setCheckoutStaffAccount(RequestContext.getAccount());
            hasClientMemberPay = memberPay || (retailAggPayReqDTO.getUseIntegral() != null && retailAggPayReqDTO
                    .getUseIntegral
                            () > 0) || false;
            if (hasClientMemberPay) {
                RetailPayReqDTO billPayReqDTO1 = retailTransform.billAggPayReqDTO2BillPayReqDTO(retailAggPayReqDTO);
                //与会员对接：聚合支付传入支付方式数据
                List<RetailPayReqDTO.Payment> payLists = new ArrayList<>();
                RetailPayReqDTO.Payment pay = new RetailPayReqDTO.Payment();
                pay.setAmount(retailAggPayReqDTO.getAmount());
                pay.setPaymentType(PaymentTypeEnum.AGG.getCode());
                pay.setPaymentTypeName(PaymentTypeEnum.AGG.getDesc());
                payLists.add(pay);
                billPayReqDTO1.setPayments(payLists);
                log.info("聚合支付，调用会员：billPayReqDTO1={},orderDO={}", billPayReqDTO1, orderDO);
                String memberConsumptionGuid = memberPayRetail(orderDO, null, billPayReqDTO1);
                orderDO.setMemberConsumptionGuid(memberConsumptionGuid);
            }

            retailDiscountService.updateBatchById(discountDOS);

//            if (orderDO.getRecoveryType().equals(RecoveryTypeEnum.NEW.getCode()) && BigDecimalUtil.greaterThanZero
//                    (orderDO.getPrepayFee())) {
//                //增加预付金使用记录
//                TransactionRecordDO transactionRecordDO1 = getTransactionRecordDO(orderDO, retailAggPayReqDTO
// .getOrderGuid(),
//                        retailAggPayReqDTO.getDeviceId());
//                saveList.add(transactionRecordDO1);
//                //预付金清零
//                orderDO.setPrepayFee(BigDecimal.ZERO);
//            }


            String orderNo = redisHelper.generateOrderNo("", RequestContext.getStoreGuid());
            log.info("聚合支付orderNo={}", orderNo);
            orderDO.setOrderNo(orderNo);
            orderService.updateById(orderDO);

            for (RetailDiscountDO retail : discountDOS) {
                Long retailGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_RETAIL_DISCOUNT);
                retail.setGuid(retailGuid);
                retail.setOrderGuid(orderDO.getGuid());
                retail.setStoreGuid(orderDO.getStoreGuid());
                retail.setStoreName(orderDO.getStoreName());
                retail.setDiscountState(0);
                retail.setStaffGuid(RequestContext.getUserGuid());
                retail.setStaffName(RequestContext.getUserName());
            }
            //之前是calculate保存时，存入数据库，结账时去修改。现在是calculate不保存，结账时去插入。
            retailDiscountService.saveBatch(discountDOS);
        }
        retailTransactionRecordService.saveBatch(saveList);
        SaasAggPayDTO aggPayDTO = orderTransform.billPayReqDTO2SaasAggPayDTO(retailAggPayReqDTO);
        AggPayPreTradingReqDTO payPreTradingReqDTO = new AggPayPreTradingReqDTO();
        payPreTradingReqDTO.setTimestamp(DateTimeUtils.nowMillis());
        payPreTradingReqDTO.setStoreName(RequestContext.getStoreName());
        payPreTradingReqDTO.setAmount(retailAggPayReqDTO.getAmount());
        payPreTradingReqDTO.setAuthCode(retailAggPayReqDTO.getAuthCode());
        payPreTradingReqDTO.setTerminalId(retailAggPayReqDTO.getDeviceId());
        payPreTradingReqDTO.setGoodsName("商品");
        payPreTradingReqDTO.setBody("聚合支付订单：" + retailAggPayReqDTO.getOrderGuid());
        // 聚合支付用分为单位
        payPreTradingReqDTO.setAmount(retailAggPayReqDTO.getAmount());
        payPreTradingReqDTO.setOrderGUID(retailAggPayReqDTO.getOrderGuid());
        payPreTradingReqDTO.setPayGUID(String.valueOf(guid));
        if (retailAggPayReqDTO.getLast()) {
            payPreTradingReqDTO.setAttachData("1");

        }
        aggPayDTO.setReqDTO(payPreTradingReqDTO);
        aggPayDTO.setSaasCallBackUrl(CommonConstant.AGG_CALLBACK_URL);
        AggPayRespDTO payRespDTO = aggPayClientService.pay(aggPayDTO);
        String code = payRespDTO.getCode();

        if (CommonConstant.AGG_SUCCESS.equalsIgnoreCase(code)) {
            retailAggPayRespDTO.setPayGuid(String.valueOf(guid));
            retailAggPayRespDTO.setJhOrderGuid(retailAggPayReqDTO.getOrderGuid());
            retailAggPayRespDTO.setResult(Boolean.TRUE);
        } else {
            //快餐聚合支付失败退估清
            RetailOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(retailAggPayReqDTO.getOrderGuid());
            if (hasClientMemberPay) {
                log.warn("发起聚合支付失败撤销会员支付，MemberConsumptionGuid：{}", orderDetail.getMemberConsumptionGuid());
                CancelPayReqDTO cancelPayReqDTO = new CancelPayReqDTO();
                cancelPayReqDTO.setIsSettlement(Boolean.FALSE);
/*                String memberConsumptionGuid = memberOrderClientService.cancelPay(orderDetail
                                .getMemberConsumptionGuid(),
                        cancelPayReqDTO);*/

                retailMemberBaseMsmService.refund(orderDetail.getMemberConsumptionGuid(), orderDetail.getOrderNo(), false, true);
                RetailOrderDO orderDO = new RetailOrderDO();
                orderDO.setGuid(Long.valueOf(orderDetail.getGuid()));
                //orderDO.setMemberConsumptionGuid(memberConsumptionGuid);
                orderService.updateById(orderDO);
            }
            log.error("发起聚合支付失败,返回：{}", JacksonUtils.writeValueAsString(payRespDTO));
            throw new ParameterException("发起聚合支付失败,返回：{}");
        }
        return retailAggPayRespDTO;

    }


    @Override
    public String aggCallBack(SaasNotifyDTO saasNotifyDTO) {
        AggPayPollingRespDTO aggPayPollingRespDTO = saasNotifyDTO.getAggPayPollingRespDTO();
        BaseInfo baseInfo = saasNotifyDTO.getBaseInfo();
        log.warn("聚合支付回调，saasNotifyDTO：{}", JacksonUtils.writeValueAsString(saasNotifyDTO));
        //支付成功
        if (CommonConstant.AGG_SUCCESS.equals(aggPayPollingRespDTO.getCode()) && aggPayPollingRespDTO.getPaySt()
                .equals(AggPayStateEnum.SUCCESS.getCode())) {
            RetailTransactionRecordDO transactionRecordDO = retailTransactionRecordService.getById(aggPayPollingRespDTO
                    .getPayGUID());
            if (transactionRecordDO.getState().intValue() == TradeStateEnum.SUCCESS.getCode()) {
                //如果已经成功了  直接跳过处理
                log.info("重复回调");
                return "SUCCESS";
            }
            LocalDate businessDay = CommonUtil.getBusinessDay(storeClientService.queryStoreBizByGuid(RequestContext
                    .getStoreGuid()));
            transactionRecordDO.setBusinessDay(businessDay);
            transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
            retailTransactionRecordService.updateById(transactionRecordDO);
            RetailOrderDO orderDO = orderService.getById(aggPayPollingRespDTO
                    .getOrderGUID());
            orderDO.setState(StateEnum.SUCCESS.getCode());
            orderDO.setBusinessDay(businessDay);
            orderDO.setCheckoutStaffGuid(RequestContext.getUserGuid());
            orderDO.setCheckoutStaffName(RequestContext.getUserName());
            orderDO.setCheckoutTime(LocalDateTime.now());
            AggPayAttachDataBO aggPayAttachDataBO = orderTransform.baseInfo2AggPayAttachDataBO(baseInfo);
            if (StringUtils.isNotEmpty(aggPayPollingRespDTO.getAttachData())) {
                aggPayAttachDataBO.setLast(Boolean.TRUE);
            } else {
                aggPayAttachDataBO.setLast(Boolean.FALSE);
            }
            if (aggPayAttachDataBO.getLast()) {
                orderService.updateById(orderDO);
                UserInfoDTO userInfo = RequestContext.getUserInfo();
                checkOutThreadPool.execute(() -> {
                    RequestContext.put(JacksonUtils.writeValueAsString(userInfo));
                    dynamicHelper.changeDatasource(userInfo.getEnterpriseGuid());
                    RetailOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(aggPayPollingRespDTO
                            .getOrderGUID());
                    printReconstructionService.asynPublish(PrintDTOAdapter.orderDetail2CheckoutEvent(saasNotifyDTO.getBaseInfo(),
                            orderDetail));
                });
            }
            saleOutRepertory(aggPayPollingRespDTO.getOrderGUID());
            BigDecimal divide = aggPayPollingRespDTO.getAmount();
            String nameById = PayPowerId.getNameById(aggPayPollingRespDTO.getPayPowerId());
            BusinessMessageDTO build = BusinessMessageDTO.builder()
                    .storeGuid(baseInfo.getStoreGuid())
                    .storeName(baseInfo.getStoreName())
                    .messageType(1)
                    .detailMessageType(12)
                    .platform("2")
                    .subject("收款成功，" + nameById + "到账" + divide.doubleValue() + "元")
                    .content(saasNotifyDTO.getBaseInfo().getDeviceId())
                    .build();
            messageClientService.notifyMsg(build);

        } else
        //支付失败(aggPayPollingRespDTO.getPaySt().equals("3"))
        {
            RetailTransactionRecordDO transactionRecordDO = retailTransactionRecordService.getById(aggPayPollingRespDTO
                    .getPayGUID());
            if (transactionRecordDO.getState().intValue() == TradeStateEnum.FAILURE.getCode()) {
                //原来成功或者失败 直接跳过
                return "SUCCESS";
            }
            //快餐聚合支付失败退估清
            RetailOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(aggPayPollingRespDTO.getOrderGUID
                    ());
            log.warn("聚合支付失败");
            transactionRecordDO.setState(TradeStateEnum.FAILURE.getCode());
            retailTransactionRecordService.updateById(transactionRecordDO);
            RetailOrderDO orderDO = orderService.getById(aggPayPollingRespDTO
                    .getOrderGUID());
            if (StringUtils.isNotEmpty(aggPayPollingRespDTO.getAttachData()) && orderDO.getMemberConsumptionGuid() !=
                    null && !"0".equals(orderDO.getMemberConsumptionGuid())) {
                //会员退积分
                log.warn("聚合支付失败退会员积分，MemberConsumptionGuid：{}", orderDO.getMemberConsumptionGuid());
                CancelPayReqDTO cancelPayReqDTO = new CancelPayReqDTO();
                cancelPayReqDTO.setIsSettlement(Boolean.FALSE);
               /* String memberConsumptionGuid = memberOrderClientService.cancelPay(orderDO.getMemberConsumptionGuid(),
                        cancelPayReqDTO);*/
                retailMemberBaseMsmService.refund(orderDetail.getMemberConsumptionGuid(), orderDetail.getOrderNo(), false, true);
                //orderDO.setMemberConsumptionGuid(memberConsumptionGuid);
            }
            orderDO.setState(StateEnum.FAILURE.getCode());
            orderService.updateById(orderDO);
        }
        return "SUCCESS";
    }


    @Override
    public Page<MemberConsumeRespDTO> memberConsumeRecords(MemberConsumeReqDTO memberConsumeReqDTO) {
        LambdaQueryWrapper<RetailOrderDO> orderDOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        //时间区间
        if (memberConsumeReqDTO.getTimeType() == 0) {
            orderDOLambdaQueryWrapper.ge(memberConsumeReqDTO.getBeginTime() != null, RetailOrderDO::getBusinessDay,
                    memberConsumeReqDTO.getBeginTime()).le(memberConsumeReqDTO.getEndTime() != null,
                    RetailOrderDO::getBusinessDay, memberConsumeReqDTO.getEndTime());
            orderDOLambdaQueryWrapper.orderByDesc(RetailOrderDO::getBusinessDay);
        }
        if (memberConsumeReqDTO.getTimeType() == 1) {
            orderDOLambdaQueryWrapper.ge(memberConsumeReqDTO.getBeginTime() != null, RetailOrderDO::getCheckoutTime,
                    memberConsumeReqDTO.getBeginTime()).le(memberConsumeReqDTO.getEndTime() != null,
                    RetailOrderDO::getCheckoutTime, memberConsumeReqDTO.getEndTime());
            orderDOLambdaQueryWrapper.orderByDesc(RetailOrderDO::getCheckoutTime);
        }

        orderDOLambdaQueryWrapper.in(RetailOrderDO::getState, Lists.newArrayList(StateEnum.SUCCESS.getCode(), StateEnum
                .REFUNDED.getCode()));
        //门店
        if (CollectionUtil.isNotEmpty(memberConsumeReqDTO.getStoreGuids())) {
            orderDOLambdaQueryWrapper.in(RetailOrderDO::getStoreGuid, memberConsumeReqDTO.getStoreGuids());
        }

        orderDOLambdaQueryWrapper.like(StringUtils.isNotEmpty(memberConsumeReqDTO.getOrderGuid()),
                RetailOrderDO::getOrderNo,
                memberConsumeReqDTO.getOrderGuid());
        orderDOLambdaQueryWrapper.eq(RetailOrderDO::getMemberGuid, memberConsumeReqDTO.getMemberGuid());

        IPage<RetailOrderDO> page = orderService.page(new PageAdapter<>(memberConsumeReqDTO),
                orderDOLambdaQueryWrapper);
        List<RetailOrderDO> records = page.getRecords();
        List<MemberConsumeRespDTO> memberConsumeRespDTOS = new ArrayList<>();
        for (RetailOrderDO record : records) {
            MemberConsumeRespDTO memberConsumeRespDTO = new MemberConsumeRespDTO();
            memberConsumeRespDTOS.add(memberConsumeRespDTO);
            memberConsumeRespDTO.setActuallyPayFee(record.getActuallyPayFee());

            memberConsumeRespDTO.setCheckoutedTimestamp(record.getCheckoutTime());
            memberConsumeRespDTO.setCheckoutStaffName(record.getCheckoutStaffName());
            memberConsumeRespDTO.setOrderGuid(String.valueOf(record.getOrderNo()));
            memberConsumeRespDTO.setShouldPayFee(record.getOrderFee());
            memberConsumeRespDTO.setStoreName(record.getStoreName());
            memberConsumeRespDTO.setTotalDiscountFee(record.getOrderFee().subtract(record.getActuallyPayFee()));
        }


        return new PageAdapter<>(page, memberConsumeRespDTOS);
    }

}
