package com.holderzone.saas.store.retail.transform;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.retail.service.event.PrintCheckoutEvent;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderDetailAdapter
 * @date 2019/10/31 11:15
 * @description //TODO
 * @program IdeaProjects
 */
public class PrintDTOAdapter {

    public static PrintCheckoutEvent orderDetail2CheckoutEvent(BaseDTO baseDTO, RetailOrderDetailRespDTO dineinOrderDetailRespDTO) {

        return new PrintCheckoutEvent(baseDTO, dineinOrderDetailRespDTO);
    }
}