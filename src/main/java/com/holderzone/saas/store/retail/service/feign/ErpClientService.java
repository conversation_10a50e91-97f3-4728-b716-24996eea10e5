package com.holderzone.saas.store.retail.service.feign;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.erp.OrderSkuDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.SubstractRepertoryForTradeReqDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ErpClientService
 * @date 2018/08/14 14:12
 * @description //
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-erp", fallbackFactory = ErpClientService.ResourceClientServiceFallBack.class)
public interface ErpClientService {

    /**
     * @param orderSkuDTO
     */
    @PostMapping("/inOutDocument/reduceStockForOrder")
    void reduceStockForOrder(@RequestBody OrderSkuDTO orderSkuDTO);

    @PostMapping("/repertory/sale_out_repertory")
    boolean saleOutRepertory(@RequestBody SubstractRepertoryForTradeReqDTO substractRepertoryForTradeReqDTO);

    @Component
    class ResourceClientServiceFallBack implements FallbackFactory<ErpClientService> {

        private static final Logger logger = LoggerFactory.getLogger(ResourceClientServiceFallBack.class);

        @Override
        public ErpClientService create(Throwable throwable) {

            return new ErpClientService() {
                @Override
                public void reduceStockForOrder(OrderSkuDTO orderSkuDTO) {
                    logger.error("扣减库存失败e={}", throwable.getMessage());
                    throw new ParameterException("扣减库存失败!");
                }

                @Override
                public boolean saleOutRepertory(SubstractRepertoryForTradeReqDTO substractRepertoryForTradeReqDTO) {
                    logger.error("新建库存单失败 e={}", throwable.getMessage());
                    throw new ParameterException("新建库存单失败!");
                }


            };
        }
    }

}
