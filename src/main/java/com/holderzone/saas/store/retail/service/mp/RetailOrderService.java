package com.holderzone.saas.store.retail.service.mp;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.retail.entity.domain.RetailOrderDO;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 订单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface RetailOrderService extends IService<RetailOrderDO> {

    /**
     * 悲观锁查订单
     *
     * @param id
     * @return
     */
    RetailOrderDO getByIdWithLock(Serializable id);
}
