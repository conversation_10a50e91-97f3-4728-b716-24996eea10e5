package com.holderzone.saas.store.retail.transform;

import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.inside.OrderTableInfoDTO;
import com.holderzone.saas.store.dto.order.request.bill.*;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.PriceChangeItemReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineInOrderListRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnItemDTO;
import com.holderzone.saas.store.dto.pay.SaasAggPayDTO;
import com.holderzone.saas.store.dto.pay.SaasAggRefundDTO;
import com.holderzone.saas.store.dto.retail.bill.request.RetailAggPayReqDTO;
import com.holderzone.saas.store.dto.retail.bill.request.RetailCalculateReqDTO;
import com.holderzone.saas.store.dto.retail.bill.request.RetailPayReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailAddGoodsReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.dto.table.TableStatusChangeDTO;
import com.holderzone.saas.store.dto.retail.BaseInfo;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import com.holderzone.saas.store.retail.entity.bo.AggPayAttachDataBO;
import com.holderzone.saas.store.retail.entity.bo.DiscountRuleBO;
import com.holderzone.saas.store.retail.entity.domain.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderTransform
 * @date 2018/09/18 20:47
 * @description
 * @program holder-saas-store-trade
 */
@Mapper
public interface OrderTransform {

    OrderTransform INSTANCE = Mappers.getMapper(OrderTransform.class);

    OrderTableInfoDTO orderDO2OrderTableInfoDTO(OrderDO orderDO);

    RetailOrderItemDO dineInItemDTO2RetailOrderItemDO(DineInItemDTO dineInItemDTO);

    RetailPayReqDTO billPayReq2Retail(BillPayReqDTO billPayReqDTO);

    DineinOrderDetailRespDTO orderDO2DineinOrderDetailRespDTO(OrderDO orderDO);

    DineinOrderDetailRespDTO retailOrderDO2DineinOrderDetailRespDTO(RetailOrderDO orderDO);

    RetailOrderDetailRespDTO orderDetail2Retail(DineinOrderDetailRespDTO dineinOrderDetailRespDTO);

    BillCalculateReqDTO retail2BillCalculate(RetailCalculateReqDTO billCalculateReqDTO);

    List<DineInOrderListRespDTO> orderDOList2DineInOrderListRespDTOList(List<OrderDO> orderDOS);

    DineInOrderListRespDTO orderDO2DineInOrderListRespDTO(OrderDO orderDO);

    DineInItemDTO orderItemDO2DineInItemDTO(OrderItemDO orderItemDO);

    FreeItemDTO freeReturnItemDO2freeItemDTO(FreeReturnItemDO freeReturnItemDO);

    ReturnItemDTO freeReturnItemDO2rerurnItemDTO(FreeReturnItemDO freeReturnItemDO);

    FreeReturnItemDO retailOrderItemDO2freeReturnItemDO(RetailOrderItemDO orderItemDO);

    SubDineInItemDTO orderItemDO2SubDineInItemDTO(OrderItemDO subGroupOrderItemDO);

    DiscountRuleBO billCalculateReqDTO2DiscountRuleBO(BillCalculateReqDTO billCalculateReqDTO);

    List<DiscountRuleBO> systemDiscountDTOS2DiscountRuleBOS(List<SystemDiscountDTO> systemDiscountDTOS);

    DiscountFeeDetailDTO discountDO2DiscountFeeDetailDTO(DiscountDO discountDO);

    List<DiscountDO> discountFeeDetailDTOS2discountDOS(List<DiscountFeeDetailDTO> discountFeeDetailDTOS);

    List<ActuallyPayFeeDetailDTO> transactionRecordDOS2ActuallyPayFeeDetailDTOS(List<TransactionRecordDO>
                                                                                        transactionRecordDOS);

    List<DiscountFeeDetailDTO> discountDOS2DiscountFeeDetailDTOS(List<DiscountDO> discountDOS);

    CreateDineInOrderReqDTO Retail2Create(RetailAddGoodsReqDTO retailAddGoodsReqDTO);

    SaasAggPayDTO billPayReqDTO2SaasAggPayDTO(RetailAggPayReqDTO billPayReqDTO);

    TableStatusChangeDTO aggPayAttachDataBO2TableStatusChangeDTO(AggPayAttachDataBO aggPayAttachDataBO);

    AggPayAttachDataBO baseInfo2AggPayAttachDataBO(BaseInfo baseInfo);

    SaasAggRefundDTO refundReqDTO2SaasAggRefundDTO(RefundReqDTO refundReqDTO);

    SaasAggRefundDTO cancelOrderReqDTO2SaasAggRefundDTO(CancelOrderReqDTO cancelOrderReqDTO);

    DiscountDO discountFeeDetailDTO2DiscountDO(DiscountFeeDetailDTO discountFeeDetailDTO);

    BillPayReqDTO billAggPayReqDTO2BillPayReqDTO(RetailAggPayReqDTO billPayReqDTO);

    RetailOrderItemDO priceChangeItemReqDTO2RetailOrderItemDO(PriceChangeItemReqDTO priceChangeItemReqDTO);

}
