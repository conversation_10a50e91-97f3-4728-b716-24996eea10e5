package com.holderzone.saas.store.retail.controller;


import com.holderzone.saas.store.dto.order.SettlementRulesDTO;
import com.holderzone.saas.store.retail.config.SettlementRulesConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-10
 */
@RestController
@RequestMapping("/hst_settlement_rules")
@Api(description = "规则查询")
@Slf4j
public class SettlementRulesController {

    @Autowired
    SettlementRulesConfig settlementRulesConfig;

    @ApiOperation(value = "查询规则", notes = "获取所有规则")
    @RequestMapping("/get_settlement_rules")
    public List<SettlementRulesDTO> getSettlementRules() {
        return settlementRulesConfig.getList();
    }

}
