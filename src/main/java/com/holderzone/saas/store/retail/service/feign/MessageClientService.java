package com.holderzone.saas.store.retail.service.feign;

import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MessageClientService
 * @date 2018/09/25 13:49
 * @description
 * @program holder-saas-store-trading-center
 */
@Component
@FeignClient(name = "holder-saas-store-message", fallbackFactory = MessageClientService.MessageClientServiceFallBack.class)
public interface MessageClientService {

    @PostMapping("/msg")
    void notifyMsg(BusinessMessageDTO businessMessageDTO);

    @Component
    class MessageClientServiceFallBack implements FallbackFactory<MessageClientService> {

        private static final Logger logger = LoggerFactory.getLogger(MessageClientServiceFallBack.class);

        @Override
        public MessageClientService create(Throwable throwable) {
            return businessMessageDTO -> logger.error("通知消息服务异常 e={}", throwable.getMessage());
        }
    }

}
