package com.holderzone.saas.store.retail.interceptor;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.retail.context.RequestContext;
import org.slf4j.MDC;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WebInterceptor
 * @date 2018/09/10 16:26
 * @description
 * @program holder-saas-store-trade
 */
@Configuration
public class WebInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws
            Exception {
        if (StringUtils.hasText(request.getHeader(USER_INFO))) {
            String userInfo = URLDecoder.decode(request.getHeader(USER_INFO), "utf-8");
            RequestContext.put(userInfo);
            UserInfoDTO userInfoDTO = JacksonUtils.toObject(UserInfoDTO.class, userInfo);
            MDC.put("userGuid", userInfoDTO.getUserGuid());
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView
            modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception
            ex) throws Exception {
        RequestContext.remove();
    }

}
