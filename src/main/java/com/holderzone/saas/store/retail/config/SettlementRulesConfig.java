package com.holderzone.saas.store.retail.config;

import com.holderzone.saas.store.dto.order.SettlementRulesDTO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SettlementRulesConfig
 * @date 2019/10/24 15:58
 * @description //TODO
 * @program IdeaProjects
 */
@Component
@Data
@ConfigurationProperties(prefix = "settlementrules")
public class SettlementRulesConfig {

    private List<SettlementRulesDTO> list;

}