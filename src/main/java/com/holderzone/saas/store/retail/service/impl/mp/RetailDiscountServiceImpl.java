package com.holderzone.saas.store.retail.service.impl.mp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.retail.entity.domain.RetailDiscountDO;
import com.holderzone.saas.store.retail.entity.enums.DiscountTypeEnum;
import com.holderzone.saas.store.retail.mapper.RetailDiscountMapper;
import com.holderzone.saas.store.retail.service.mp.RetailDiscountService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 订单优惠记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Service
public class RetailDiscountServiceImpl extends ServiceImpl<RetailDiscountMapper, RetailDiscountDO> implements RetailDiscountService {

    @Override
    public List<RetailDiscountDO> listByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<RetailDiscountDO>().eq(RetailDiscountDO::getOrderGuid, orderGuid));

    }

    @Override
    public void removeByOrderGuids(List<Long> subOrderGuids) {
        remove(new LambdaQueryWrapper<RetailDiscountDO>().in(RetailDiscountDO::getOrderGuid, subOrderGuids));
    }

    @Override
    public RetailDiscountDO getGrouponDiscount(String orderGuid) {
        return getOne(new LambdaQueryWrapper<RetailDiscountDO>().eq(RetailDiscountDO::getOrderGuid, orderGuid).eq
                (RetailDiscountDO::getDiscountType, DiscountTypeEnum.GROUPON.getCode()));
    }
}
