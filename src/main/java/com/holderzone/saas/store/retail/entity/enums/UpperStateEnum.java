package com.holderzone.saas.store.retail.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UpperStateEnum
 * @date 2018/10/23 11:54
 * @description
 * @program holder-saas-store-trade
 */
public enum UpperStateEnum {
    GENERAL(0, "无并单"),
    MAIN(1, "主单"),
    SUB(2, "子单"),
    ;

    private int code;
    private String desc;

    UpperStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (UpperStateEnum c : UpperStateEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
