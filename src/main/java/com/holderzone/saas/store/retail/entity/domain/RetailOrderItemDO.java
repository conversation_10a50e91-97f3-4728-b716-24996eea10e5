package com.holderzone.saas.store.retail.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 订单商品
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_retail_order_item")
public class RetailOrderItemDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 商品guid
     */
    private String itemGuid;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品编号
     */
    private String code;

    /**
     * 商品类别guid
     */
    private String itemTypeGuid;

    /**
     * 商品类别名称
     */
    private String itemTypeName;


    /**
     * 商品的规格
     */
    private String skuGuid;

    /**
     * 规格名称
     */
    private String skuName;

    /**
     * sku价格
     */
    private BigDecimal price;
    /**
     * 商品原价
     */
    private BigDecimal originalPrice;
    /**
     * 折扣
     */
    private Integer discountPercent;
    /**
     * 菜品优惠合计
     */
    private BigDecimal totalDiscountFee;

    /**
     * 商品类型(1.套餐主项，2.规格，3.称重，4.单品 )
     */
    private Integer itemType;

    /**
     * 商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂 ，8.已上菜 )
     */
    private Integer itemState;


    /**
     * 当前数量（不包括赠送，不要重复减赠送折扣）
     */
    private BigDecimal currentCount;

    /**
     * 赠送数量（销售统计=当前+赠送）
     */
    private BigDecimal freeCount;

    /**
     * 退货数量（赠送变为退之后不计入销售和增菜统计）
     */
    private BigDecimal returnCount;

    /**
     * 计数单位
     */
    private String unit;

    /**
     * 是否参与整单折扣(0：否，1：是)
     */
    private Integer isWholeDiscount;

    /**
     * 是否参与会员折扣（0：否，1：是）
     */
    private Integer isMemberDiscount;


    /**
     * 价格改变类型，1为改价，2为折扣
     */
    private Integer priceChangeType;

    /**
     * 商品备注
     */
    private String remark;

    /**
     * 商品条形码
     */
    private String itemUpc;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 创建操作人guid
     */
    private String createStaffGuid;

    /**
     * 创建操作人name
     */
    private String createStaffName;

}
