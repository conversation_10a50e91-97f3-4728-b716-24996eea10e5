package com.holderzone.saas.store.retail.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 商品属性
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_item_attr")
public class ItemAttrDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 订单guid
     */
    private Long orderGuid;

    /**
     * 订单商品guid
     */
    private Long orderItemGuid;

    /**
     * 属性guid
     */
    private String attrGuid;

    /**
     * 属性名称
     */
    private String attrName;

    /**
     * 属性组guid
     */
    private String attrGroupGuid;

    /**
     * 属性组名称
     */
    private String attrGroupName;

    /**
     * 属性价格
     */
    private BigDecimal attrPrice;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;


}
