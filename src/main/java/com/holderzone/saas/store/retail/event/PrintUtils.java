package com.holderzone.saas.store.retail.event;

import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.common.ItemAttrDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.organization.StoreBizDTO;
import com.holderzone.saas.store.dto.print.content.PrintDTO;
import com.holderzone.saas.store.dto.print.content.nested.AdditionalCharge;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.nested.PrintItemRecord;
import com.holderzone.saas.store.dto.print.content.nested.ReduceRecord;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailCheckOutDTO;
import com.holderzone.saas.store.dto.retail.bill.common.RetailItemDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import com.holderzone.saas.store.retail.config.RocketMqConfig;
import com.holderzone.saas.store.retail.context.RequestContext;
import com.holderzone.saas.store.retail.entity.enums.DiscountTypeEnum;
import com.holderzone.saas.store.retail.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.retail.entity.enums.PaymentTypeEnum;
import com.holderzone.saas.store.retail.service.feign.StoreClientService;
import com.holderzone.saas.store.retail.utils.BigDecimalUtil;
import com.holderzone.saas.store.retail.utils.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintUtils
 * @date 2019/09/23 11:31
 * @description //TODO
 * @program IdeaProjects
 */
@Slf4j
public class PrintUtils {


    protected List<PrintItemRecord> extractItemRecords(RetailOrderDetailRespDTO dineinOrderDetailRespDTO, boolean
            isReturn) {
        return Optional.ofNullable(dineinOrderDetailRespDTO.getDineInItemDTOS())
                .map(dineInItemDTOS -> dineInItemDTOS.stream()
                        .flatMap(dineInItemDTO -> {
                            List<PrintItemRecord> printItemRecords = new ArrayList<>();
                            if (BigDecimalUtil.greaterThanZero(BigDecimalUtil.nonNullValue(dineInItemDTO
                                    .getCurrentCount()))) {
                                printItemRecords.add(newPrintItemRecord(dineInItemDTO, dineInItemDTO.getCurrentCount
                                        (), false));
                            }
                            if (BigDecimalUtil.nonNullValue(dineInItemDTO.getFreeCount()).compareTo(BigDecimal.ZERO)
                                    > 0 && !isReturn) {
                                printItemRecords.add(newPrintItemRecord(dineInItemDTO, dineInItemDTO.getFreeCount(),
                                        true));
                            }

                            return printItemRecords.stream();
                        })
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    protected PrintItemRecord newPrintItemRecord(RetailItemDTO dineInItemDTO, BigDecimal number, boolean asGift) {
        PrintItemRecord printItemRecord = new PrintItemRecord();
        printItemRecord.setOrderItemGuid(dineInItemDTO.getGuid());
        printItemRecord.setOriginalOrderItemGuid(dineInItemDTO.getOriginalOrderItemGuid());
        printItemRecord.setItemGuid(dineInItemDTO.getItemGuid());
        printItemRecord.setUnit(dineInItemDTO.getUnit());
        String itemName = dineInItemDTO.getItemName();
        if (Integer.valueOf(ItemTypeEnum.SKU.getCode()).equals(dineInItemDTO.getItemType())) {
            itemName = itemName + "(" + dineInItemDTO.getSkuName() + ")";
        }
        printItemRecord.setItemName(itemName);
        printItemRecord.setItemTypeGuid(dineInItemDTO.getItemTypeGuid());
        printItemRecord.setItemTypeName(dineInItemDTO.getItemTypeName());
        if (dineInItemDTO.getPriceChangeType() != null && dineInItemDTO.getPriceChangeType().intValue() != 1) {
            printItemRecord.setPrice(dineInItemDTO.getOriginalPrice());
        } else {
            if (asGift) {
                printItemRecord.setPrice(dineInItemDTO.getOriginalPrice());
            } else {
                printItemRecord.setPrice(dineInItemDTO.getPrice());
            }
        }
        printItemRecord.setNumber(number);
        printItemRecord.setAsPackage(Integer.valueOf(ItemTypeEnum.GROUP.getCode()).equals(dineInItemDTO.getItemType()));
        printItemRecord.setAsWeight(Integer.valueOf(ItemTypeEnum.WEIGH.getCode()).equals(dineInItemDTO.getItemType()));
        printItemRecord.setAsGift(asGift);
        if (Integer.valueOf(ItemTypeEnum.GROUP.getCode()).equals(dineInItemDTO.getItemType())
                && !CollectionUtil.isEmpty(dineInItemDTO.getPackageSubgroupDTOS())) {
            List<PrintItemRecord> subItemRecords = dineInItemDTO.getPackageSubgroupDTOS().stream()
                    .map(PackageSubgroupDTO::getSubDineInItemDTOS)
                    .flatMap(subDineInItemDTOS -> {
                        if (CollectionUtils.isEmpty(subDineInItemDTOS)) {
                            return Stream.empty();
                        }
                        return subDineInItemDTOS.stream();
                    })
                    .map(subDineInItemDTO -> {
                        PrintItemRecord subPrintItemRecord = new PrintItemRecord();
                        subPrintItemRecord.setItemGuid(subDineInItemDTO.getItemGuid());
                        subPrintItemRecord.setOriginalOrderItemGuid(subDineInItemDTO.getOriginalOrderItemGuid());
                        subPrintItemRecord.setOrderItemGuid(subDineInItemDTO.getGuid());
                        subPrintItemRecord.setUnit(subDineInItemDTO.getUnit());
                        String subItemName = subDineInItemDTO.getItemName();
                        BigDecimal pkgDefaultCnt = Optional.ofNullable(subDineInItemDTO.getPackageDefaultCount())
                                .orElse(BigDecimal.ONE);
                        if (Integer.valueOf(ItemTypeEnum.SKU.getCode()).equals(subDineInItemDTO.getItemType())) {
                            subItemName = subItemName + "(" + subDineInItemDTO.getSkuName() + ")";
                        }
                        subPrintItemRecord.setItemName(subItemName);
                        subPrintItemRecord.setItemTypeGuid(subDineInItemDTO.getItemTypeGuid());
                        subPrintItemRecord.setPrice(subDineInItemDTO.getPrice());
                        subPrintItemRecord.setPkgCnt(pkgDefaultCnt);
                        subPrintItemRecord.setNumber(subDineInItemDTO.getCurrentCount());
                        subPrintItemRecord.setAsPackage(false);
                        subPrintItemRecord.setAsWeight(Integer.valueOf(ItemTypeEnum.WEIGH.getCode())
                                .equals(subDineInItemDTO.getItemType()));
                        subPrintItemRecord.setAsGift(asGift);
                        subPrintItemRecord.setRemark(subDineInItemDTO.getRemark());
                        subPrintItemRecord.setPropertyPrice(requirePropertyPrice(
                                subDineInItemDTO.getSingleItemAttrTotal(),
                                subDineInItemDTO.getItemAttrDTOS()));
                        subPrintItemRecord.setIngredientPrice(subDineInItemDTO.getAddPrice());
                        fillItemProperty(subPrintItemRecord, subDineInItemDTO.getItemAttrDTOS());
                        return subPrintItemRecord;
                    })
                    .collect(Collectors.toList());
            printItemRecord.setSubItemRecords(subItemRecords);
        }

        printItemRecord.setRemark(dineInItemDTO.getRemark());
        fillItemProperty(printItemRecord, dineInItemDTO.getItemAttrDTOS());
        if (Integer.valueOf(ItemTypeEnum.GROUP.getCode()).equals(dineInItemDTO.getItemType())) {
            printItemRecord.setPropertyPrice(BigDecimal.ZERO);
        } else {
            printItemRecord.setPropertyPrice(requirePropertyPrice(
                    dineInItemDTO.getSingleItemAttrTotal(), dineInItemDTO.getItemAttrDTOS()));
        }
        printItemRecord.setIngredientPrice(BigDecimal.ZERO);

        return printItemRecord;
    }

    protected void internalPrintPreCheckout(PrintRetailCheckOutDTO printPreCheckoutDTO, RetailOrderDetailRespDTO
            dineinOrderDetailRespDTO, StoreClientService storeClientService) {
        internalPrintItemDetail(printPreCheckoutDTO, dineinOrderDetailRespDTO);
        printPreCheckoutDTO.setReduceRecordList(extractReduceRecordList(dineinOrderDetailRespDTO));
        printPreCheckoutDTO.setPayAble(dineinOrderDetailRespDTO.getActuallyPayFee());
        StoreBizDTO storeBizDTO = storeClientService.queryStoreBizByGuid(RequestContext.getStoreGuid());
        printPreCheckoutDTO.setStoreAddress(storeBizDTO.getAddressDetail());
        printPreCheckoutDTO.setTel(storeBizDTO.getContactTel());
    }

    protected BigDecimal requirePropertyPrice(BigDecimal singleItemAttrTotal, List<ItemAttrDTO> itemAttrList) {
        if (singleItemAttrTotal != null) {
            return singleItemAttrTotal;
        }
        return Optional.ofNullable(itemAttrList)
                .map(itemAttrDTOS -> itemAttrDTOS.stream()
                        .map(itemAttrDTO -> itemAttrDTO.getAttrPrice()
                                .multiply(BigDecimal.valueOf(itemAttrDTO.getNum())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                .orElse(BigDecimal.ZERO);
    }

    protected void fillItemProperty(PrintItemRecord printItemRecord, List<ItemAttrDTO> itemAttrDTOS) {
        if (CollectionUtils.isEmpty(itemAttrDTOS)) {
            return;
        }
        printItemRecord.setProperty(itemAttrDTOS.stream()
                .map(itemAttrDTO -> {
                    if (Integer.valueOf(1).equals(itemAttrDTO.getNum())) {
                        return itemAttrDTO.getAttrName();
                    }
                    return itemAttrDTO.getAttrName() + "x" + itemAttrDTO.getNum();
                })
                .collect(Collectors.joining("，")));
    }

    protected BigDecimal extractItemPriceTotal(RetailOrderDetailRespDTO dineinOrderDetailRespDTO) {
        BigDecimal nonFreeItemTotal = bigDecimalReduce(dineinOrderDetailRespDTO.getDineInItemDTOS(),
                RetailItemDTO::getBeforeItemPrice);
        List<FreeItemDTO> freeItems = dineinOrderDetailRespDTO.getDineInItemDTOS().stream()
                .map(RetailItemDTO::getFreeItemDTOS)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        BigDecimal freeItemTotal = bigDecimalReduce(freeItems, FreeItemDTO::getItemPrice);
        return nonFreeItemTotal.add(freeItemTotal);
    }

    public static <T> BigDecimal bigDecimalReduce(List<T> list, Function<T, BigDecimal> mapper) {
        return list.stream().map(mapper).reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    protected List<AdditionalCharge> extractAdditionalChargeList(RetailOrderDetailRespDTO dineinOrderDetailRespDTO) {
        // 目前没有附加费
        return Collections.emptyList();
    }

    protected List<ReduceRecord> extractReduceRecordList(RetailOrderDetailRespDTO dineinOrderDetailRespDTO) {
        return Optional.ofNullable(dineinOrderDetailRespDTO.getDiscountFeeDetailDTOS())
                .map(discountFeeDetailDTOS -> discountFeeDetailDTOS.stream()
                        .map(discountFeeDetailDTO -> {
                            ReduceRecord reduceRecord = new ReduceRecord();
                            String discountName = discountFeeDetailDTO.getDiscountName();
                            if (discountFeeDetailDTO.getDiscountType().equals(DiscountTypeEnum.WHOLE.getCode())) {
                                discountName = discountName + "[" + discountFeeDetailDTO.getDiscount() + "]折";
                            }
                            reduceRecord.setName(discountName);
                            reduceRecord.setAmount(discountFeeDetailDTO.getDiscountFee());
                            return reduceRecord;
                        })
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    protected void internalPrintItemDetail(PrintRetailCheckOutDTO printItemDetailDTO, RetailOrderDetailRespDTO
            dineinOrderDetailRespDTO) {
        printItemDetailDTO.setItemRecordList(extractItemRecords(dineinOrderDetailRespDTO, false));
        printItemDetailDTO.setStoreName(RequestContext.getStoreName());
        printItemDetailDTO.setOrderNo(dineinOrderDetailRespDTO.getOrderNo());
        printItemDetailDTO.setOpenTableTime(DateTimeUtils.localDateTime2Mills(dineinOrderDetailRespDTO.getGmtCreate()));
        printItemDetailDTO.setTotal(extractItemPriceTotal(dineinOrderDetailRespDTO));
//        printItemDetailDTO.setTotal(dineinOrderDetailRespDTO.getOrderFee());
    }


    protected List<PayRecord> requirePayRecordList(RetailOrderDetailRespDTO order) {
        List<PayRecord> payRecords = Optional.ofNullable(order.getActuallyPayFeeDetailDTOS())
                .map(actuallyPayFeeDetailDTOS1 -> actuallyPayFeeDetailDTOS1.stream()
                        .map(actuallyPayFeeDetailDTO -> {
                            PayRecord payRecord = new PayRecord();
                            payRecord.setPayName(actuallyPayFeeDetailDTO.getPaymentTypeName());
                            BigDecimal amount = actuallyPayFeeDetailDTO.getAmount();
                            if (actuallyPayFeeDetailDTO.getPaymentType().equals(PaymentTypeEnum.CASH.getCode()) &&
                                    BigDecimalUtil.greaterThanZero(order.getChangeFee())) {
                                amount = amount.add(order.getChangeFee());
                            }
                            payRecord.setAmount(amount);
                            return payRecord;
                        })
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
        return payRecords;
    }


    /**
     * 设置基本信息
     *
     * @param printDTO
     * @param baseDTO
     * @param invoiceTypeEnum
     * @param dineinOrderDetailRespDTO
     */
    protected void setPrintBaseInfo(PrintDTO printDTO, BaseDTO baseDTO,
                                    InvoiceTypeEnum invoiceTypeEnum, RetailOrderDetailRespDTO dineinOrderDetailRespDTO) {
        printDTO.setInvoiceType(invoiceTypeEnum.getType());
        printDTO.setStoreGuid(baseDTO.getStoreGuid());
        printDTO.setEnterpriseGuid(baseDTO.getEnterpriseGuid());
        printDTO.setPrintUid(dineinOrderDetailRespDTO.getOrderNo());
        printDTO.setOperatorStaffGuid(baseDTO.getUserGuid());
        printDTO.setOperatorStaffName(baseDTO.getUserName());
        printDTO.setCreateTime(DateTimeUtils.nowMillis());
        printDTO.setDeviceId(baseDTO.getDeviceId());
        printDTO.setPrintSourceEnum(PrintSourceEnum.getPrintSourceByDeviceType(baseDTO.getDeviceType()));
    }

    /**
     * 微型工厂方法
     *
     * @param
     * @return
     */
    public Message getPrintMessage(PrintDTO printDTO) {
        String body = JacksonUtils.writeValueAsString(printDTO);
        log.info("printbody is {}", body);
        return new Message(RocketMqConfig.PRINT_MESSAGE_TOPIC, RocketMqConfig.PRINT_MESSAGE_TAG, body.getBytes());
    }


}