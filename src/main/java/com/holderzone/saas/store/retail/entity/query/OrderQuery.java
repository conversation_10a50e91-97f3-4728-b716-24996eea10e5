package com.holderzone.saas.store.retail.entity.query;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderQuery
 * @date 2018/07/27 上午 11:56
 * @description
 * @program holder-saas-store-trade
 */
@NoArgsConstructor
@Data
public class OrderQuery {

    private String orderGuid;

    private String storeGuid;

    private String operationStaffGuid;

    private String operationStaffName;

    private Long operationTimestamp;
}
