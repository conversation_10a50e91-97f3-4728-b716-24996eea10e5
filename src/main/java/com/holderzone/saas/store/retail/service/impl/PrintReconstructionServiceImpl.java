package com.holderzone.saas.store.retail.service.impl;

import com.holderzone.framework.util.Assert;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.retail.anno.HandlerEventClass;
import com.holderzone.saas.store.retail.context.RequestContext;
import com.holderzone.saas.store.retail.event.BaseOrderEvent;
import com.holderzone.saas.store.retail.event.OrderHandler;
import com.holderzone.saas.store.retail.helper.DynamicHelper;
import com.holderzone.saas.store.retail.service.PrintReconstructionService;
import com.holderzone.saas.store.retail.utils.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintReconstructionServiceImpl
 * @date 2019/09/21 11:23
 * @description //TODO
 * @program IdeaProjects
 */
@Service
@Slf4j
public class PrintReconstructionServiceImpl implements PrintReconstructionService {

    @Autowired
    List<OrderHandler> handlers;

    Map<String, List<OrderHandler>> handlerMap;

    @Resource(name = "checkOutThreadPool")
    ExecutorService executorService;

    @Autowired
    private DynamicHelper dynamicHelper;

    @PostConstruct
    private void init() {
        handlerMap = new HashMap();
        handlers.stream().forEach(a -> {
                    HandlerEventClass annotation = a.getClass().getAnnotation(HandlerEventClass.class);
                    Assert.notNull(annotation, a.getClass().getName() + "没配置event");
                    Class[] value = annotation.value();  //所有event
                    Arrays.stream(value).map(Class::getName).forEach(b -> {
                        List<OrderHandler> orderHandlers = handlerMap.get(b);
                        if (CollectionUtil.isEmpty(orderHandlers)) {
                            orderHandlers = new ArrayList<>();
                            handlerMap.put(b, orderHandlers);
                        }
                        orderHandlers.add(a);
                    });
                }
        );
//        handlerMap = handlers.stream().collect(Collectors.groupingBy(a -> {
//            ParameterizedTypeImpl genericSuperclass = (ParameterizedTypeImpl)a.getClass().getGenericInterfaces()[0] ;
//            return genericSuperclass.getActualTypeArguments()[0].getTypeName();
//        }));
    }

    /**
     * 所有同步
     *
     * @param baseOrderEvent
     */
    @Override
    public boolean publish(BaseOrderEvent baseOrderEvent) {
        return Optional.of(handlerMap.get(baseOrderEvent.getClass().getName())).get().stream().anyMatch(a -> a.handle(baseOrderEvent));
    }

    ;

    /**
     * 所有异步
     *
     * @param baseOrderEvent
     */
    @Override
    public void asynPublish(BaseOrderEvent baseOrderEvent) {
        UserInfoDTO userInfoDTO = RequestContext.getUserInfo();
        Optional.of(handlerMap.get(baseOrderEvent.getClass().getName())).get().forEach(a -> executorService.submit(() -> {
            RequestContext.put(JacksonUtils.writeValueAsString(userInfoDTO));
            dynamicHelper.changeDatasource(userInfoDTO.getEnterpriseGuid());
            a.handle(baseOrderEvent);
        }));
    }

    ;


}