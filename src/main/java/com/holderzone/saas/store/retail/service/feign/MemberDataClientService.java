package com.holderzone.saas.store.retail.service.feign;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.holder.saas.datac.dto.request.DutyStatisReqDTO;
import com.holderzone.holder.saas.datac.dto.response.DutyStatisRespDTO;
import com.holderzone.holder.saas.member.dto.datacenter.request.ConsumptionStatisticsReqDTO;
import com.holderzone.holder.saas.member.dto.datacenter.response.ConsumptionStatisticsRespDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;


@Component
@FeignClient(name = "holder-saas-member-data-center", fallbackFactory = MemberDataClientService.MemberDataClientServiceFallback.class)
public interface MemberDataClientService {

    @PostMapping("/hsmdc/duty/statistics")
    DutyStatisRespDTO getStatisticsOfTotal(DutyStatisReqDTO dutyStatisReqDTO);

    @PostMapping("hsmdc/store/business/consumptionStatistics")
    ConsumptionStatisticsRespDTO getStatisticsOfWay(ConsumptionStatisticsReqDTO consumptionStatisticsReqDTO);

    @Component
    class MemberDataClientServiceFallback implements FallbackFactory<MemberDataClientService> {

        private static final Logger logger = LoggerFactory.getLogger(MemberDataClientService.MemberDataClientServiceFallback.class);

        @Override
        public MemberDataClientService create(Throwable throwable) {

            return new MemberDataClientService() {

                @Override
                public DutyStatisRespDTO getStatisticsOfTotal(DutyStatisReqDTO dutyStatisReqDTO) {
                    logger.error("获取会员概况调用异常e={}", throwable.getMessage());
                    throw new ParameterException("获取会员概况调用异常");
                }

                @Override
                public ConsumptionStatisticsRespDTO getStatisticsOfWay(ConsumptionStatisticsReqDTO consumptionStatisticsReqDTO) {
                    logger.error("获取会员消费支付方式调用异常e={}", throwable.getMessage());
                    throw new ParameterException("获取会员消费支付方式调用异常");
                }
            };
        }
    }
}