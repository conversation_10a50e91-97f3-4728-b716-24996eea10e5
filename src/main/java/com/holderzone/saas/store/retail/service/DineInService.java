package com.holderzone.saas.store.retail.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderListReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderListRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailRemarkReqDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInService
 * @date 2018/09/04 16:08
 * @description //
 * @program holder-saas-store-order
 */
public interface DineInService {


    /**
     * 修改整单备注
     *
     * @param retailRemarkReqDTO
     * @return
     */
    Boolean updateRemark(RetailRemarkReqDTO retailRemarkReqDTO);

    RetailOrderDetailRespDTO getOrderDetail(String orderGuid);

    RetailOrderDetailRespDTO getOrderDetailForAndroid(String orderGuid);


    /**
     * 查询订单列表
     *
     * @param retailOrderListReqDTO
     * @return
     */
    Page<RetailOrderListRespDTO> orderList(RetailOrderListReqDTO retailOrderListReqDTO);


}
