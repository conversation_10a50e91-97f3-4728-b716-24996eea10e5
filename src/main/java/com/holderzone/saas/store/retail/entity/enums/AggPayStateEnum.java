package com.holderzone.saas.store.retail.entity.enums;


/**
 * <AUTHOR>
 * @version 1.0
 * @className StateEnum
 * @date 2018/09/04 17:52
 * @description 订单状态枚举
 * @program holder-saas-store-trade
 */
public enum AggPayStateEnum {

    READY("0", "待支付"),
    PENDING("1", "支付中"),
    FAILURE("3", "支付失败"),
    SUCCESS("2", "支付成功");

    private String code;
    private String desc;

    AggPayStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(String code) {
        for (AggPayStateEnum c : AggPayStateEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
