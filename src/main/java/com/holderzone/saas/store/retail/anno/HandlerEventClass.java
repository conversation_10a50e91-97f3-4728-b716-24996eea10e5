package com.holderzone.saas.store.retail.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandlerClass
 * @date 2019/09/23 13:28
 * @description //TODO
 * @program IdeaProjects
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface HandlerEventClass {
    Class[] value() default {};
}