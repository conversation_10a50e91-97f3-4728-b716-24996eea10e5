package com.holderzone.saas.store.retail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.kds.req.OrderRemarkReqDTO;
import com.holderzone.saas.store.dto.retail.bill.common.RetailItemDTO;
import com.holderzone.saas.store.dto.retail.dinein.*;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.retail.entity.domain.*;
import com.holderzone.saas.store.retail.entity.enums.FreeReturnTypeEnum;
import com.holderzone.saas.store.retail.entity.enums.PaymentTypeEnum;
import com.holderzone.saas.store.retail.entity.enums.StateEnum;
import com.holderzone.saas.store.retail.entity.enums.TradeTypeEnum;
import com.holderzone.saas.store.retail.mapper.OrderMapper;
import com.holderzone.saas.store.retail.service.DineInService;
import com.holderzone.saas.store.retail.service.mp.*;
import com.holderzone.saas.store.retail.transform.RetailTransform;
import com.holderzone.saas.store.retail.utils.AmountCalculationUtil;
import com.holderzone.saas.store.retail.utils.BigDecimalUtil;
import com.holderzone.saas.store.retail.utils.CollectionUtil;
import com.holderzone.saas.store.retail.utils.OrderUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className
 * @date 2018/09/04 16:10
 * @description
 * @program holder-saas-store-trade
 */
@Service
@Slf4j
public class DineInServiceImpl implements DineInService {

    private final OrderMapper orderMapper;

    private final RetailOrderService retailOrderService;

    private final RetailOrderItemService retailOrderItemService;

    private final RetailFreeReturnService retailFreeReturnService;

    private final RetailTransactionRecordService retailTransactionRecordService;

    private final RetailDiscountService retailDiscountService;

    private RetailTransform retailTransform = RetailTransform.INSTANCE;

    @Autowired
    public DineInServiceImpl(RetailDiscountService retailDiscountService, OrderMapper orderMapper,
                             RetailOrderService retailOrderService, RetailOrderItemService retailOrderItemService,
                             RetailFreeReturnService retailFreeReturnService, RetailTransactionRecordService retailTransactionRecordService
    ) {
        this.retailDiscountService = retailDiscountService;
        this.retailOrderService = retailOrderService;
        this.retailOrderItemService = retailOrderItemService;
        this.retailFreeReturnService = retailFreeReturnService;
        this.retailTransactionRecordService = retailTransactionRecordService;
        this.orderMapper = orderMapper;
    }


    @Override
    public Boolean updateRemark(RetailRemarkReqDTO retailRemarkReqDTO) {
        RetailOrderDO orderDO = new RetailOrderDO();
        RetailOrderDO orderDOInDb = retailOrderService.getByIdWithLock(retailRemarkReqDTO.getGuid());
        if (!orderDOInDb.getState().equals(StateEnum.READY.getCode())) {
            throw new ParameterException("订单状态异常");
        }
        orderDO.setGuid(Long.valueOf(retailRemarkReqDTO.getGuid()));
        if (retailRemarkReqDTO.getRemark() == null) {
            retailRemarkReqDTO.setRemark(StringUtils.EMPTY);
        }
        orderDO.setRemark(retailRemarkReqDTO.getRemark());
        OrderRemarkReqDTO orderRemarkReqDTO = new OrderRemarkReqDTO();
        orderRemarkReqDTO.setOrderGuid(retailRemarkReqDTO.getGuid());
        orderRemarkReqDTO.setOrderRemark(orderDO.getRemark());
        return retailOrderService.updateById(orderDO);
    }

    @Override
    public RetailOrderDetailRespDTO getOrderDetail(String orderGuid) {
        RetailOrderDO retailOrderDO = retailOrderService.getById(orderGuid);
        if (retailOrderDO == null) {
            throw new ParameterException("没有该订单的记录：" + orderGuid);
        }
        RetailOrderDetailRespDTO orderDetailRespDTO = getSingleOrderDetail(retailOrderDO);
        return orderDetailRespDTO;
    }

    @Override
    public RetailOrderDetailRespDTO getOrderDetailForAndroid(String orderGuid) {
        RetailOrderDetailRespDTO orderDetail = getOrderDetail(orderGuid);
        List<RetailItemDTO> retailItemDTOS = orderDetail.getDineInItemDTOS();
        for (RetailItemDTO retailItemDTO : retailItemDTOS) {
            if (retailItemDTO.getPriceChangeType() != null && retailItemDTO.getPriceChangeType().intValue() == 2) {
                //折扣需要补偿价格
                BigDecimal reduiceprice = retailItemDTO.getOriginalPrice().subtract(retailItemDTO.getOriginalPrice()
                        .multiply(new BigDecimal(retailItemDTO.getDiscountPercent()))
                        .divide(new BigDecimal(1000), 2))
                        .multiply(retailItemDTO.getCurrentCount());
                retailItemDTO.setItemPrice(retailItemDTO.getItemPrice().subtract(reduiceprice));
            }
            if (BigDecimalUtil.greaterThanZero(retailItemDTO.getCurrentCount())) {
                retailItemDTO.setFinalSinglePrice(retailItemDTO.getPrice().subtract(retailItemDTO.getTotalDiscountFee().divide(retailItemDTO
                        .getCurrentCount(), 2, BigDecimal.ROUND_HALF_DOWN)));
            } else {
                retailItemDTO.setFinalSinglePrice(retailItemDTO.getPrice());
            }
        }
        return orderDetail;
    }

    private RetailOrderDetailRespDTO getSingleOrderDetail(RetailOrderDO retailOrderDO) {
        if (retailOrderDO == null) {
            throw new ParameterException("没有该订单的记录");
        }
        String orderGuid = String.valueOf(retailOrderDO.getGuid());
        List<RetailOrderItemDO> orderItemDOS = retailOrderItemService.listByOrderGuid(Long.valueOf(orderGuid));
        //========= 兼容版本 加上原价  ，数据库可也要改
        orderItemDOS.stream().filter(a -> a.getPriceChangeType() == 0 && a.getOriginalPrice() == null).forEach(a -> a
                .setOriginalPrice(a.getPrice()));
        Map<Long, List<RetailOrderItemDO>> itemListMap = CollectionUtil.toListMap(orderItemDOS, "guid");

        RetailOrderDetailRespDTO orderDetailRespDTO = retailTransform.retailOrderDO2RetailOrderDetailRespDTO
                (retailOrderDO);
        orderDetailRespDTO.setMemberCardNum(retailOrderDO.getMemberCardNum());
        orderDetailRespDTO.setState(OrderUtil.fixOrderState(retailOrderDO.getState()));
        orderDetailRespDTO.setDeviceTypeName(BaseDeviceTypeEnum.getDesc(retailOrderDO.getDeviceType()));
        if (retailOrderDO.getCheckoutDeviceType() != null) {
            orderDetailRespDTO.setCheckoutDeviceTypeName(BaseDeviceTypeEnum.getDesc(retailOrderDO
                    .getCheckoutDeviceType()));
        }
        //赠送信息
        List<RetailFreeReturnItemDO> freeReturnItemDOS = null;
        if (AmountCalculationUtil.hasFreeRetail(orderItemDOS)) {
            Map<Long, RetailOrderItemDO> itemDOMap = CollectionUtil.toMap(orderItemDOS, "guid");
            freeReturnItemDOS = retailFreeReturnService.list(new
                    LambdaQueryWrapper<RetailFreeReturnItemDO>().in(RetailFreeReturnItemDO::getOrderItemGuid, new ArrayList<>
                    (itemDOMap.keySet())).eq(RetailFreeReturnItemDO::getType, FreeReturnTypeEnum.FREE.getCode()));
        }
        List<RetailItemDTO> retailItemDTOList = AmountCalculationUtil.buildItemRetail(orderItemDOS,
                freeReturnItemDOS);
        orderDetailRespDTO.setOrderFee(retailOrderDO.getOrderFee());
//        Map<String, RetailItemDTO> dineInItemDTOMap = CollectionUtil.toMap(retailItemDTOList, "guid");

//        //退货信息
//        List<RetailFreeReturnItemDO> returnItemDOS = retailFreeReturnService.list(new LambdaQueryWrapper<RetailFreeReturnItemDO>()
//                .eq(RetailFreeReturnItemDO::getOrderGuid, orderGuid).eq(RetailFreeReturnItemDO::getType, FreeReturnTypeEnum
//                        .RETURN.getCode()));
//        List<ReturnItemDTO> returnItemDTOS = AmountCalculationUtil.bulidReturnItemDTOS(returnItemDOS, dineInItemDTOMap);

        List<RetailTransactionRecordDO> transactionRecordDOS;
        Map<Integer, List<RetailTransactionRecordDO>> tradeType = new HashMap<>();
        if (!retailOrderDO.getState().equals(StateEnum.READY.getCode())) {
            transactionRecordDOS = retailTransactionRecordService.listByOrderGuid(orderGuid);
            tradeType = CollectionUtil.toListMap(transactionRecordDOS, "tradeType");
        }

        List<RetailTransactionRecordDO> successList = tradeType.get(TradeTypeEnum.GENERAL_IN.getCode());
        List<RetailTransactionRecordDO> returnList = tradeType.get(TradeTypeEnum.REFUND_OUT.getCode());
        List<RetailTransactionRecordDO> transactionRecordS = new ArrayList();
        if (CollectionUtil.isNotEmpty(successList)) {
            Map<Integer, List<RetailTransactionRecordDO>> paymentType = CollectionUtil.toListMap(successList,
                    "paymentType");
            for (Integer integer : paymentType.keySet()) {
                List<RetailTransactionRecordDO> recordDOS = paymentType.get(integer);
                //============ 当支付方式为其他方式的时候，不需要合并支付 ================
                if (integer.equals(PaymentTypeEnum.OTHER.getCode())) {
                    transactionRecordS.addAll(recordDOS);
                    continue;
                }
                if (CollectionUtil.isNotEmpty(recordDOS)) {
                    if (recordDOS.size() > 1) {
                        BigDecimal agg = BigDecimal.ZERO;
                        for (RetailTransactionRecordDO recordDO : recordDOS) {
                            agg = agg.add(recordDO.getAmount());
                        }
                        RetailTransactionRecordDO transactionRecordDO = recordDOS.get(0);
                        transactionRecordDO.setAmount(agg);
                        transactionRecordS.add(transactionRecordDO);
                    } else {
                        transactionRecordS.add(recordDOS.get(0));
                    }
                }
            }
        }else if (CollectionUtil.isNotEmpty(returnList)){
            transactionRecordS.addAll(returnList);
        }

        List<RetailDiscountDO> discountDOS = retailDiscountService.listByOrderGuid(orderGuid);
        AmountCalculationUtil.repairAmount(orderDetailRespDTO, retailOrderDO, transactionRecordS, discountDOS);
        orderDetailRespDTO.setDiscountFee(retailOrderDO.getOrderFee().subtract(retailOrderDO.getActuallyPayFee()));

//        orderDetailRespDTO.setReturnItemDTOS(returnItemDTOS);
        orderDetailRespDTO.setDineInItemDTOS(retailItemDTOList);
        AmountCalculationUtil.filterZeroItem(orderDetailRespDTO);
        return orderDetailRespDTO;
    }

    @Override
    @Transactional
    public Page<RetailOrderListRespDTO> orderList(RetailOrderListReqDTO retailOrderListReqDTO) {
        //sql查询订单列表
        log.info("订单列表入参 retailOrderListReqDTO= {}", JacksonUtils.writeValueAsString(retailOrderListReqDTO));
        if (retailOrderListReqDTO.getGoodsName() != null) {
            retailOrderListReqDTO.setGoodsName("'%" + retailOrderListReqDTO.getGoodsName() + "%'");
        }
        Integer pageStart = (retailOrderListReqDTO.getCurrentPage() - 1) * retailOrderListReqDTO.getPageSize();
        List<RetailOrderListRespDTO> dineInOrderListRespDTOS = orderMapper.getOrderList(pageStart, retailOrderListReqDTO.getPageSize(), retailOrderListReqDTO);
        log.info("订单列表返回 page= {}", JacksonUtils.writeValueAsString(dineInOrderListRespDTOS));
        int totolCount = orderMapper.findRow();
        for (RetailOrderListRespDTO retailOrderListRespDTO : dineInOrderListRespDTOS) {
            Integer state = retailOrderListRespDTO.getState();
            if (StateEnum.READY.getCode() <= state && state <= StateEnum.FAILURE.getCode()) {
                retailOrderListRespDTO.setState(1);
            }
            if (StateEnum.SUCCESS.getCode() == state) {
                retailOrderListRespDTO.setState(2);
            }
            if (StateEnum.REFUNDED.getCode() == state) {
                retailOrderListRespDTO.setState(3);
            }
            if (StateEnum.CANCEL.getCode() == state) {
                retailOrderListRespDTO.setState(4);
            }
        }
        return new Page<>(retailOrderListReqDTO.getCurrentPage(), retailOrderListReqDTO.getPageSize(), totolCount, dineInOrderListRespDTOS);
    }

}
