package com.holderzone.saas.store.retail.service.mp;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.retail.entity.domain.RetailTransactionRecordDO;

import java.util.List;

/**
 * <p>
 * 订单交易记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface RetailTransactionRecordService extends IService<RetailTransactionRecordDO> {

    List<RetailTransactionRecordDO> listJhAndPreByOrderGuid(String orderGuid);

    List<RetailTransactionRecordDO> listGeneralByOrderGuid(String orderGuid);

    List<RetailTransactionRecordDO> listByOrderGuid(String orderGuid);

    List<RetailTransactionRecordDO> listByOrderGuids(List<Long> orderGuids);

}
