package com.holderzone.saas.store.retail.utils;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.holder.saas.cmember.app.dto.common.DishInfoDTO;
import com.holderzone.saas.store.dto.organization.StoreBizDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.retail.bill.common.RetailItemDTO;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @className CommonUtils
 * @date 2018/09/04 11:26
 * @description //通用工具类
 * @program holder-saas-store-trade
 */
public class CommonUtil {

    public static List<DishInfoDTO> orderItem2DishInfoDTO(List<RetailItemDTO> retailItemDTOS) {
        List<DishInfoDTO> dishInfoDTOlist = Lists.newArrayList();
        for (RetailItemDTO retailItemDTO : retailItemDTOS) {
            dishInfoDTOlist.add(convertFromDineInItemDTO(retailItemDTO));
        }
        return dishInfoDTOlist;
    }

    public static boolean hasGuid(String guid) {
        return StringUtils.isNotEmpty(guid) && !"0".equals(guid);
    }

    private static DishInfoDTO convertFromDineInItemDTO(RetailItemDTO retailItemDTO) {
        DishInfoDTO dishInfoDTO = new DishInfoDTO();
        dishInfoDTO.setOrderItemGuid(retailItemDTO.getGuid());
        dishInfoDTO.setDishGuid(retailItemDTO.getItemGuid());
        dishInfoDTO.setDishName(retailItemDTO.getItemName());
        dishInfoDTO.setDishSpecification(retailItemDTO.getSkuGuid());
        dishInfoDTO.setDishUnit(retailItemDTO.getUnit());
        //9.21早与蔡商议，因加商品券增加赠送数量GiftDishNum字段，原DishNum字段仍然传当前数量与赠送数量之和
        dishInfoDTO.setDishNum(retailItemDTO.getCurrentCount().add(retailItemDTO.getFreeCount()));
        dishInfoDTO.setGiftDishNum(retailItemDTO.getFreeCount());
        dishInfoDTO.setMainGoodGuid(null);
        dishInfoDTO.setIsMainGood(1);
        dishInfoDTO.setSurcharge(BigDecimal.ZERO);
        dishInfoDTO.setRemark(retailItemDTO.getRemark());
        dishInfoDTO.setDishType(retailItemDTO.getItemType());
        dishInfoDTO.setSubtotal(retailItemDTO.getItemPrice());
        dishInfoDTO.setDishOriginalUnitPrice(retailItemDTO.getPrice());
        dishInfoDTO.setDishSellUnitPrice(retailItemDTO.getPrice());
        if (BigDecimalUtil.greaterEqual(retailItemDTO.getItemPrice(), retailItemDTO.getTotalDiscountFee())) {
            dishInfoDTO.setPayPrice(retailItemDTO.getItemPrice().subtract(retailItemDTO.getTotalDiscountFee()));
        } else {
            dishInfoDTO.setPayPrice(BigDecimal.ZERO);
        }
        return dishInfoDTO;

    }

    /**
     * 获取当天结束时间
     *
     * @return
     */
    public static Date getTodayEndTime() {
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.HOUR_OF_DAY, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 999);
        return todayEnd.getTime();
    }

    /**
     * 获取当天结束时间
     *
     * @return
     */
    public static LocalDate getBusinessDay(StoreBizDTO storeBizDTO) {
        //todo 对接新营业日接口
        LocalTime businessStartTime = storeBizDTO.getBusinessStart();
        LocalTime businessEndTime = storeBizDTO.getBusinessEnd();
        //营业日为当天
        LocalDate businessDay;
        if (businessEndTime == null || businessStartTime == null) {
            throw new ParameterException("获取营业日区间失败");
        }
        LocalTime now = LocalTime.now();
        LocalTime min = LocalTime.MIN;

        businessDay = LocalDate.now();

        //跨天(结束时间小于等于开始时间)
        if (businessStartTime.compareTo(businessEndTime) >= 0) {
            //营业日为前一天（0：00<=当前时间<开始时间）
            if (min.compareTo(now) >= 0 && now.isBefore(businessStartTime)) {
                businessDay = LocalDate.now().minusDays(1);
            }
        }

        return businessDay;
    }


    /**
     * 获取当天结束时间
     *
     * @return
     */
    public static LocalDate getBusinessDay(StoreDTO storeDTO) {

        LocalTime businessStartTime = storeDTO.getBusinessStart();
        LocalTime businessEndTime = storeDTO.getBusinessEnd();
        //营业日为当天
        LocalDate businessDay;
        if (businessEndTime == null || businessStartTime == null) {
            throw new ParameterException("获取营业日区间失败");
        }
        LocalTime now = LocalTime.now();
        LocalTime min = LocalTime.MIN;

        businessDay = LocalDate.now();

        //跨天(结束时间小于等于开始时间)
        if (businessStartTime.compareTo(businessEndTime) >= 0) {
            //营业日为前一天（0：00<=当前时间<开始时间）
            if (min.compareTo(now) >= 0 && now.isBefore(businessStartTime)) {
                businessDay = LocalDate.now().minusDays(1);
            }
        }

        return businessDay;
    }


}
