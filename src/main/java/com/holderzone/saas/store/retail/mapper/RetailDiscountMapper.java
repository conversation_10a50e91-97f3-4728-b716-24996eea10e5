package com.holderzone.saas.store.retail.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.retail.entity.domain.DiscountDO;
import com.holderzone.saas.store.retail.entity.domain.RetailDiscountDO;

import java.util.List;

/**
 * <p>
 * 订单优惠记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface RetailDiscountMapper extends BaseMapper<RetailDiscountDO> {

    int batchUpdate(List<DiscountDO> record);
}
