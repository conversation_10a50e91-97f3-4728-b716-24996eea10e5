package com.holderzone.saas.store.retail.service.mp;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.retail.entity.domain.RetailFreeReturnItemDO;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 赠送/退货记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface RetailFreeReturnService extends IService<RetailFreeReturnItemDO> {

    List<RetailFreeReturnItemDO> listFreeByOrderGuid(String orderGuid);

    List<RetailFreeReturnItemDO> listByOrderGuid(String orderGuid);

    void deleteByOrderGuid(String orderGuid);

    void removeByItemGuids(ArrayList<Long> orderItemGuids);
}
