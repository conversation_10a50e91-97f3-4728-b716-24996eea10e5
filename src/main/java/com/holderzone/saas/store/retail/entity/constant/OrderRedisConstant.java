package com.holderzone.saas.store.retail.entity.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRedisConstant
 * @date 2018-07-25 18:25:11
 * @description
 * @program holder-saas-store-trade
 */
public class OrderRedisConstant {

    public static final String ORDER_GROUP = "retailOrder";

    public static final String RETAIL_HANG_GROUP = "retailHang";

    public static final String RETAIL_DISCOUNT_RULE = "retailDiscountRule";

    public static final String ORDER_NO_GROUP = "orderNo";

    public static final String HST_ORDER_ITEM_GROUP = "hstOrderItem";

    public static final String INCR_GROUP = "incr";

    public static final int ORDER_HOUR_TIMEOUT = 24;

}
