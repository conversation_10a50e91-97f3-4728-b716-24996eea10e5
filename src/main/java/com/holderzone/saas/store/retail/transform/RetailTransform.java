package com.holderzone.saas.store.retail.transform;


import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.retail.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.retail.bill.common.RetailItemDTO;
import com.holderzone.saas.store.dto.retail.bill.request.RetailAggPayReqDTO;
import com.holderzone.saas.store.dto.retail.bill.request.RetailCalculateReqDTO;
import com.holderzone.saas.store.dto.retail.bill.request.RetailPayReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.PriceChangeItemReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderListRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.ReturnItemDTO;
import com.holderzone.saas.store.retail.entity.bo.DiscountRuleBO;
import com.holderzone.saas.store.retail.entity.domain.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderTransform
 * @date 2018/09/18 20:47
 * @description
 * @program holder-saas-store-trade
 */
@Mapper
public interface RetailTransform {

    RetailTransform INSTANCE = Mappers.getMapper(RetailTransform.class);

    List<RetailOrderListRespDTO> orderDOList2RetailOrderListRespDTOList(List<OrderDO> orderDOS);

    List<RetailOrderListRespDTO> retailDOList2RetailOrderListRespDTOList(List<RetailOrderDO> orderDOS);

    RetailOrderDetailRespDTO orderDO2DineinOrderDetailRespDTO(OrderDO orderDO);

    RetailOrderDetailRespDTO retailOrderDO2RetailOrderDetailRespDTO(RetailOrderDO orderDO);

    RetailOrderItemDO priceChangeItemReqDTO2RetailOrderItemDO(PriceChangeItemReqDTO priceChangeItemReqDTO);

    RetailPayReqDTO billAggPayReqDTO2BillPayReqDTO(RetailAggPayReqDTO billPayReqDTO);

    List<DiscountDO> discountFeeDetailDTOS2discountDOS(List<DiscountFeeDetailDTO> discountFeeDetailDTOS);

    List<RetailDiscountDO> discountFeeDetailDTOS2discountDOS2(List<DiscountFeeDetailDTO> discountFeeDetailDTOS);

    DiscountRuleBO billCalculateReqDTO2DiscountRuleBO(RetailCalculateReqDTO billCalculateReqDTO);

    DiscountDO discountFeeDetailDTO2DiscountDO(DiscountFeeDetailDTO discountFeeDetailDTO);

    RetailDiscountDO retailDiscountFeeDetailDTO2DiscountDO(DiscountFeeDetailDTO discountFeeDetailDTO);

    List<DiscountFeeDetailDTO> discountDOS2DiscountFeeDetailDTOS(List<DiscountDO> discountDOS);

    List<DiscountFeeDetailDTO> discountDOS2DiscountFeeDetailDTOS2(List<RetailDiscountDO> discountDOS);

    List<ActuallyPayFeeDetailDTO> transactionRecordDOS2ActuallyPayFeeDetailDTOS(List<RetailTransactionRecordDO>
                                                                                        transactionRecordDOS);

    RetailItemDTO orderItemDO2DineInItemDTO(OrderItemDO orderItemDO);

    RetailItemDTO retailOrderItemDO2DineInItemDTO(RetailOrderItemDO orderItemDO);

    ReturnItemDTO freeReturnItemDO2rerurnItemDTO(FreeReturnItemDO freeReturnItemDO);

    ReturnItemDTO freeReturnItemDO2rerurnItemDTO2(RetailFreeReturnItemDO freeReturnItemDO);

    List<com.holderzone.saas.store.dto.retail.SystemDiscountDTO> fromList2List(List<com.holderzone.saas.store.dto.trade.SystemDiscountDTO> systemDiscountDTOS);

    List<DiscountRuleBO> systemDiscountDTOS2DiscountRuleBOS(List<com.holderzone.saas.store.dto.retail.SystemDiscountDTO> systemDiscountDTOS);

    FreeItemDTO freeReturnItemDO2freeItemDTO(RetailFreeReturnItemDO retailFreeReturnItemDO);

    RetailFreeReturnItemDO retailOrderItemDO2freeReturnItemDO(RetailOrderItemDO orderItemDO);

}
