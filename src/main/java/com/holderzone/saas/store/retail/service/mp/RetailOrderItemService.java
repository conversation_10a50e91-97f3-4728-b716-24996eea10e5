package com.holderzone.saas.store.retail.service.mp;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.retail.entity.domain.RetailOrderItemDO;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 订单商品 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface RetailOrderItemService extends IService<RetailOrderItemDO> {

    List<RetailOrderItemDO> listByOrderGuid(Long orderGuid);

    List<RetailOrderItemDO> listByIdsWithLock(Collection<? extends Serializable> idList);

    List<RetailOrderItemDO> listByOrderLists(List<String> orderLists);
}
