package com.holderzone.saas.store.retail.service;

import com.holderzone.saas.store.dto.retail.HangOrderDTO;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HangOrderService
 * @date 2018/09/07 11:03
 * @description //TODO
 * @program holder-saas-store-order
 */
public interface HangOrderService {

    Boolean gainOrder(HangOrderDTO hangOrderDTO);

    String hangOrder(HangOrderDTO hangOrderDTO);

    Map<String, String> hangOrderList(HangOrderDTO hangOrderDTO);
}
