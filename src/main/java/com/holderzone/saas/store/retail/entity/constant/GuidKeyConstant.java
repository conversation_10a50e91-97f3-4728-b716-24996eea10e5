package com.holderzone.saas.store.retail.entity.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GuidKeyConstant
 * @date 2018/09/04 17:19
 * @description
 * @program holder-saas-store-trade
 */
public class GuidKeyConstant {

    public static final String HST_RETAIL_ORDER = "hst_retail_order";

    public static final String HST_RETAIL_ORDER_ITEM = "hst_retail_order_item";

    public static final String FREE_RETURN_ITEM = "freeReturnItem";

    public static final String HST_RETAIL_DISCOUNT = "hst_retail_discount";

    public static final String HST_TRANSACTION_RECORD = "hstTransactionRecord";

}
