package com.holderzone.saas.store.retail.context;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RequestContext
 * @date 2019/01/21 10:42
 * @description 请求上下文
 * @program holder-saas-store-trade
 */
public class RequestContext {

    private static final ThreadLocal<String> THREAD_LOCAL = new ThreadLocal<>();

    public static void put(String str) {
        THREAD_LOCAL.set(str);
    }

    public static void setBaseDTO(BaseDTO baseDTO) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setUserName(baseDTO.getUserName());
        userInfoDTO.setUserGuid(baseDTO.getUserGuid());
        userInfoDTO.setStoreName(baseDTO.getStoreName());
        userInfoDTO.setStoreGuid(baseDTO.getStoreGuid());
        userInfoDTO.setEnterpriseGuid(baseDTO.getEnterpriseGuid());
        userInfoDTO.setAccount(baseDTO.getAccount());
        userInfoDTO.setEnterpriseName(baseDTO.getEnterpriseName());
        THREAD_LOCAL.set(JacksonUtils.writeValueAsString(userInfoDTO));
    }

    public static void putEnterpriseGuid(String enterpriseGuid) {
        String json = "{\"enterpriseGuid\":\"" + enterpriseGuid + "\"}";
        THREAD_LOCAL.set(json);
    }

    public static UserInfoDTO getUserInfo() {
        String result = THREAD_LOCAL.get();
        return JacksonUtils.toObject(UserInfoDTO.class, result);
    }

    public static String getJsonStr() {
        return THREAD_LOCAL.get();
    }

    public static String getEnterpriseGuid() {
        String result = THREAD_LOCAL.get();
        UserInfoDTO userInfoDTO = JacksonUtils.toObject(UserInfoDTO.class, result);
        if (null != userInfoDTO) {
            return userInfoDTO.getEnterpriseGuid();
        }
        return null;
    }

    public static String getUserGuid() {
        String result = THREAD_LOCAL.get();
        UserInfoDTO userInfoDTO = JacksonUtils.toObject(UserInfoDTO.class, result);
        if (null != userInfoDTO) {
            return userInfoDTO.getUserGuid();
        }
        return null;
    }

    public static String getAccount() {
        String result = THREAD_LOCAL.get();
        UserInfoDTO userInfoDTO = JacksonUtils.toObject(UserInfoDTO.class, result);
        if (null != userInfoDTO) {
            return userInfoDTO.getAccount();
        }
        return null;
    }

    public static String getStoreGuid() {
        String result = THREAD_LOCAL.get();
        UserInfoDTO userInfoDTO = JacksonUtils.toObject(UserInfoDTO.class, result);
        if (null != userInfoDTO) {
            return userInfoDTO.getStoreGuid();
        }
        return null;
    }

    public static String getStoreName() {
        String result = THREAD_LOCAL.get();
        UserInfoDTO userInfoDTO = JacksonUtils.toObject(UserInfoDTO.class, result);
        if (null != userInfoDTO) {
            return userInfoDTO.getStoreName();
        }
        return null;
    }

    public static String getUserName() {
        String result = THREAD_LOCAL.get();
        UserInfoDTO userInfoDTO = JacksonUtils.toObject(UserInfoDTO.class, result);
        if (null != userInfoDTO) {
            return userInfoDTO.getUserName();
        }
        return null;
    }

    public static void remove() {
        THREAD_LOCAL.remove();
    }

}
