package com.holderzone.saas.store.retail.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.retail.HangOrderDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderListReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderListRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailRemarkReqDTO;
import com.holderzone.saas.store.retail.context.RequestContext;
import com.holderzone.saas.store.retail.service.DineInService;
import com.holderzone.saas.store.retail.service.HangOrderService;
import com.holderzone.saas.store.retail.service.PrintReconstructionService;
import com.holderzone.saas.store.retail.transform.PrintDTOAdapter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInOrderController
 * @date 2019/01/04 8:53
 * @description 堂食订单接口
 * @program holder-saas-store-trade
 */
@RestController
@RequestMapping("/retail_order")
@Api(description = "正餐接口")
@Slf4j
public class RetailOrderController {

    private final DineInService dineInService;

    private final HangOrderService hangOrderService;

    private final PrintReconstructionService printReconstructionService;

    @Autowired
    public RetailOrderController(DineInService dineInService, HangOrderService hangOrderService,
                                 PrintReconstructionService printReconstructionService) {
        this.dineInService = dineInService;
        this.hangOrderService = hangOrderService;
        this.printReconstructionService = printReconstructionService;
    }

    @ApiOperation(value = "订单列表", notes = "订单列表")
    @PostMapping("/order_list")
    public Page<RetailOrderListRespDTO> orderList(@RequestBody RetailOrderListReqDTO retailOrderListReqDTO) {
        return dineInService.orderList(retailOrderListReqDTO);
    }

    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @PostMapping("/get_order_detail")
    public RetailOrderDetailRespDTO getOrderDetail(@RequestBody SingleDataDTO singleDataDTO) {
        return dineInService.getOrderDetailForAndroid(singleDataDTO.getData());
    }

    @ApiOperation(value = "修改整单备注", notes = "修改整单备注")
    @PostMapping("/update_remark")
    public Boolean updateRemark(@RequestBody RetailRemarkReqDTO retailRemarkReqDTO) {
        return dineInService.updateRemark(retailRemarkReqDTO);
    }

    @ApiOperation(value = "挂起订单", notes = "挂起订单")
    @PostMapping("/hang_order")
    public String hangOrder(@RequestBody HangOrderDTO hangOrderDTO) {
        return hangOrderService.hangOrder(hangOrderDTO);
    }

    @ApiOperation(value = "挂起订单列表", notes = "挂起订单列表")
    @PostMapping("/hang_order_list")
    public Map<String, String> hangOrderList(@RequestBody HangOrderDTO hangOrderDTO) {
        return hangOrderService.hangOrderList(hangOrderDTO);
    }

    @ApiOperation(value = "删除挂起订单", notes = "删除挂起订单")
    @PostMapping("/gain_order")
    public Boolean gainOrder(@RequestBody HangOrderDTO hangOrderDTO) {
        return hangOrderService.gainOrder(hangOrderDTO);
    }

    @ApiOperation(value = "打印结账单", notes = "打印结账单")
    @PostMapping("/print_check_out")
    public boolean printCheckOut(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("请求头：{}",RequestContext.getUserInfo());
        //异步执行结账成功后的打印等操作
        RetailOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(singleDataDTO.getData());
        printReconstructionService.asynPublish(PrintDTOAdapter.orderDetail2CheckoutEvent(singleDataDTO,
                orderDetail));
        return true;
    }
}
