package com.holderzone.saas.store.retail.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DiscountRuleBO
 * @date 2019/02/23 16:03
 * @description
 * @program holder-saas-store-trade
 */
@Data
public class DiscountSortBO {

    @ApiModelProperty(value = "折扣执行顺序")
    private Integer sort;

    @ApiModelProperty(value = "折扣类型（1：菜品维度，2：订单维度）")
    private Integer type;
}
