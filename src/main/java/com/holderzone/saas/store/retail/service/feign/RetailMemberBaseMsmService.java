package com.holderzone.saas.store.retail.service.feign;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.holder.saas.member.terminal.dto.retail.RequestRetailPayInfo;
import com.holderzone.holder.saas.member.terminal.dto.retail.ResponseMemberAndCardInfo;
import com.holderzone.holder.saas.member.terminal.dto.retail.ResponseRetailIntegralCompute;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RetailMemberBaseServiceMsm
 * @date 2019/10/29 16:52
 * @description //TODO
 * @program IdeaProjects
 */
@Component
@FeignClient(name = "holder-saas-member-terminal", fallbackFactory = RetailMemberBaseMsmService
        .RetailMemberBaseMsmServiceFallback.class)
public interface RetailMemberBaseMsmService {

    /**
     * 会员退款
     *
     * @return
     */
    @GetMapping("/hss/member/consumption/refund")
    Boolean refund(@RequestParam("consumptionGuid") String consumptionGuid, @RequestParam("newOrderNum") String newOrderNum
            , @RequestParam("isRefund") Boolean isRefund, @RequestParam("isChargeBack") Boolean isChargeBack);

    /**
     * 支付结算
     *
     * @param payReqDTO
     * @return
     */
    @PostMapping("/hss/member/consumption/pay")
    String payOrder(@RequestBody RequestRetailPayInfo payReqDTO);


    /**
     * 积分抵扣
     *
     * @param memberInfoCardGuid
     * @param orderMoney
     * @return
     */
    @GetMapping("/hss/rule/integral/compute")
    ResponseRetailIntegralCompute retailCompute(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid,
                                                @RequestParam("orderMoney") BigDecimal orderMoney);

    /**
     * 查询会员接口
     */
    @GetMapping("/hss/member/getMemberAndCardInfo")
    ResponseMemberAndCardInfo getMemberAndCardInfo(@RequestParam("phoneOrCardNum") String phoneOrCardNum);


    @Component
    class RetailMemberBaseMsmServiceFallback implements FallbackFactory<RetailMemberBaseMsmService> {

        private static final Logger logger = LoggerFactory.getLogger(MemberOrderClientService.MemberClientFallback.class);

        @Override
        public RetailMemberBaseMsmService create(Throwable throwable) {
            return new RetailMemberBaseMsmService() {

                @Override
                public Boolean refund(String consumptionGuid, String newOrderNum, Boolean isRefund, Boolean isChargeBack) {
                    logger.error("（零售）会员退积分调用异常e={}", throwable.getMessage());
                    throw new ParameterException("会员退积分调用异常");
                }

                @Override
                public String payOrder(RequestRetailPayInfo payReqDTO) {
                    logger.error("（零售）会员支付调用异常e={}", throwable.getMessage());
                    throw new ParameterException("会员支付调用异常");
                }

                @Override
                public ResponseRetailIntegralCompute retailCompute(String memberInfoCardGuid, BigDecimal orderMoney) {
                    logger.error("（零售）会员积分抵扣调用异常e={}", throwable.getMessage());
                    throw new ParameterException("会员积分抵扣调用异常");
                }

                @Override
                public ResponseMemberAndCardInfo getMemberAndCardInfo(String phoneOrCardNum) {
                    logger.error("（零售）查询会员信息调用异常e={}", throwable.getMessage());
                    throw new ParameterException("查询会员信息调用异常");
                }
            };
        }
    }
}