package com.holderzone.saas.store.retail.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.retail.entity.domain.RetailOrderDO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface RetailOrderMapper extends BaseMapper<RetailOrderDO> {

    Integer handoverOrderCount(@Param("dto") HandoverPayQueryDTO request);

}
