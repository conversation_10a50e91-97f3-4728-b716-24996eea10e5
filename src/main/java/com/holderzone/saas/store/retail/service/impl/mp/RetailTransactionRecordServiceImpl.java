package com.holderzone.saas.store.retail.service.impl.mp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.retail.entity.domain.RetailTransactionRecordDO;
import com.holderzone.saas.store.retail.entity.enums.PaymentTypeEnum;
import com.holderzone.saas.store.retail.entity.enums.TradeStateEnum;
import com.holderzone.saas.store.retail.entity.enums.TradeTypeEnum;
import com.holderzone.saas.store.retail.mapper.RetailTransactionRecordMapper;
import com.holderzone.saas.store.retail.service.mp.RetailTransactionRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 订单交易记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Service
public class RetailTransactionRecordServiceImpl extends ServiceImpl<RetailTransactionRecordMapper, RetailTransactionRecordDO>
        implements RetailTransactionRecordService {

    @Override
    public List<RetailTransactionRecordDO> listJhAndPreByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<RetailTransactionRecordDO>().eq(RetailTransactionRecordDO::getOrderGuid, orderGuid).eq
                (RetailTransactionRecordDO::getPaymentType, PaymentTypeEnum.AGG.getCode()));

    }


    @Override
    public List<RetailTransactionRecordDO> listGeneralByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<RetailTransactionRecordDO>().eq(RetailTransactionRecordDO::getOrderGuid, orderGuid).eq
                (RetailTransactionRecordDO::getTradeType, TradeTypeEnum.GENERAL_IN.getCode()));
    }

    @Override
    public List<RetailTransactionRecordDO> listByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<RetailTransactionRecordDO>().eq(RetailTransactionRecordDO::getOrderGuid, orderGuid).eq
                (RetailTransactionRecordDO::getState, TradeStateEnum.SUCCESS.getCode()));
    }

    @Override
    public List<RetailTransactionRecordDO> listByOrderGuids(List<Long> orderGuids) {
        return list(new LambdaQueryWrapper<RetailTransactionRecordDO>().in(RetailTransactionRecordDO::getOrderGuid, orderGuids).eq
                (RetailTransactionRecordDO::getState, TradeStateEnum.SUCCESS.getCode()));
    }
}
