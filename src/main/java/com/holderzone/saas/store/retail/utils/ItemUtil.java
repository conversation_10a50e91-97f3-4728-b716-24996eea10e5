package com.holderzone.saas.store.retail.utils;

import com.holderzone.saas.store.dto.erp.OrderSkuDTO;
import com.holderzone.saas.store.dto.erp.SkuInfo;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.retail.context.RequestContext;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemPushHelper
 * @date 2019/02/27 下午3:36
 * @description //商品相关辅助类
 * @program holder-saas-store-item
 */
@Component
public class ItemUtil {


    public static OrderSkuDTO getErpDTO(List<DineInItemDTO> list) {
        Map<String, BigDecimal> stringBigDecimalMap = itemEstimateHander(list);
        OrderSkuDTO orderSkuDTO = new OrderSkuDTO();
        orderSkuDTO.setEnterpriseGuid(RequestContext.getEnterpriseGuid());
        orderSkuDTO.setOperatorGuid(RequestContext.getUserGuid());
        orderSkuDTO.setOperatorName(RequestContext.getUserName());
        orderSkuDTO.setStoreGuid(RequestContext.getStoreGuid());
        List<SkuInfo> skuList = new ArrayList<>();
        orderSkuDTO.setSkuList(skuList);
        for (String s : stringBigDecimalMap.keySet()) {
            SkuInfo skuInfo = new SkuInfo();
            skuInfo.setSkuGuid(s);
            skuInfo.setCount(stringBigDecimalMap.get(s));
            skuList.add(skuInfo);
        }
        return orderSkuDTO;
    }

    public static OrderSkuDTO getErpDTO(List<DineInItemDTO> list, boolean isout) {
        Map<String, BigDecimal> stringBigDecimalMap = itemEstimateHander(list);
        OrderSkuDTO orderSkuDTO = new OrderSkuDTO();
        orderSkuDTO.setOut(isout);
        orderSkuDTO.setEnterpriseGuid(RequestContext.getEnterpriseGuid());
        orderSkuDTO.setOperatorGuid(RequestContext.getUserGuid());
        orderSkuDTO.setOperatorName(RequestContext.getUserName());
        orderSkuDTO.setStoreGuid(RequestContext.getStoreGuid());
        List<SkuInfo> skuList = new ArrayList<>();
        orderSkuDTO.setSkuList(skuList);
        for (String s : stringBigDecimalMap.keySet()) {
            SkuInfo skuInfo = new SkuInfo();
            skuInfo.setSkuGuid(s);
            skuInfo.setCount(stringBigDecimalMap.get(s));
            skuList.add(skuInfo);
        }
        return orderSkuDTO;
    }

    /**
     * 传入订单 返回需检验商品的SKU和数量
     *
     * @param list
     * @return
     */
    public static Map<String, BigDecimal> itemEstimateHander(List<DineInItemDTO> list) {
        Map<String, BigDecimal> result = new HashMap<>();
        for (DineInItemDTO a : list) {
            result = getDineInNumber(a, result);
        }
        return result;
    }

    //传入一个菜 ,计算其赠菜
    public static Map<String, BigDecimal> getDineInNumber(DineInItemDTO dineInItemDTO, Map<String, BigDecimal> map) {
        //判断是否有赠菜
        if (Optional.ofNullable(dineInItemDTO.getFreeItemDTOS()).isPresent() && !dineInItemDTO.getFreeItemDTOS()
                .isEmpty()) {
            //获取到赠菜
            List<FreeItemDTO> freeItemDTOS = dineInItemDTO.getFreeItemDTOS();
            //计算赠送数量
            BigDecimal total = freeItemDTOS.stream().map(FreeItemDTO::getCount).reduce(BigDecimal.ZERO,
                    BigDecimal::add);
            //加上赠送数量
            dineInItemDTO.setCurrentCount(dineInItemDTO.getCurrentCount().add(total));
        }
        //判断是否是套餐
        if (Optional.ofNullable(dineInItemDTO.getPackageSubgroupDTOS()).isPresent() && !dineInItemDTO
                .getPackageSubgroupDTOS().isEmpty()) {
            List<PackageSubgroupDTO> packageSubgroupDTOS = dineInItemDTO.getPackageSubgroupDTOS();
            //套餐主项sku存在
            if (map.containsKey(dineInItemDTO.getSkuGuid())) {
                BigDecimal count = map.get(dineInItemDTO.getSkuGuid());
                count = count.add(dineInItemDTO.getCurrentCount());
                map.put(dineInItemDTO.getSkuGuid(), count);
            } else { //套餐主项sku不存在
                map.put(dineInItemDTO.getSkuGuid(), dineInItemDTO.getCurrentCount());
            }
            for (PackageSubgroupDTO s : packageSubgroupDTOS) {
                if (Optional.ofNullable(s.getSubDineInItemDTOS()).isPresent()) {
                    List<SubDineInItemDTO> subDineInItemDTOS = s.getSubDineInItemDTOS();
                    subDineInItemDTOS.forEach(a -> {
                        //map已经存在相同sku
                        if (map.containsKey(a.getSkuGuid())) {
                            BigDecimal count = map.get(a.getSkuGuid());
                            count = count.add(a.getCurrentCount().multiply(a.getPackageDefaultCount()).multiply
                                    (dineInItemDTO.getCurrentCount()));
                            map.put(a.getSkuGuid(), count);
                        } else { //不存在
                            map.put(a.getSkuGuid(), a.getCurrentCount().multiply(a.getPackageDefaultCount()).multiply
                                    (dineInItemDTO.getCurrentCount()));
                        }
                    });
                }
            }
        } else { //单品、称重
            if (map.containsKey(dineInItemDTO.getSkuGuid())) {
                BigDecimal count = map.get(dineInItemDTO.getSkuGuid());
                count = count.add(dineInItemDTO.getCurrentCount());
                map.put(dineInItemDTO.getSkuGuid(), count);
            } else { //不存在
                map.put(dineInItemDTO.getSkuGuid(), dineInItemDTO.getCurrentCount());
            }
        }
        return map;
    }
}
