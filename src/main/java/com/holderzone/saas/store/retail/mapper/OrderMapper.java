package com.holderzone.saas.store.retail.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderListReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderListRespDTO;
import com.holderzone.saas.store.retail.entity.domain.RetailOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <p>
 * 订单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Mapper
@Repository
public interface OrderMapper extends BaseMapper<RetailOrderDO> {

    List<RetailOrderListRespDTO> getOrderList(@Param("pageStart") Integer pageStart,
                                              @Param("pageSize") Integer pageSize,
                                              @Param("retailOrderListReqDTO") RetailOrderListReqDTO retailOrderListReqDTO);

    Integer findRow();

}
