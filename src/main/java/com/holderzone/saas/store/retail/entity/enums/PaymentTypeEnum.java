package com.holderzone.saas.store.retail.entity.enums;

import com.holderzone.framework.exception.unchecked.ParameterException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeEnum
 * @date 2018/09/04 17:52
 * @description
 * @program holder-saas-store-trade
 */
public enum PaymentTypeEnum {

    CASH(1, "现金支付"),
    AGG(2, "聚合支付"),
    CARD(3, "银联支付"),
    MEMBER(4, "会员余额支付"),
    FACE(5, "人脸支付"),
    OTHER(10, "其他支付方式"),
    ;

    private int code;
    private String desc;

    PaymentTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (PaymentTypeEnum c : PaymentTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        throw new ParameterException("支付方式类型不匹配");
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
