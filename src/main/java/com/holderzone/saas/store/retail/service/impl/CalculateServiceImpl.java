package com.holderzone.saas.store.retail.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.dto.account.request.cmember.QueryStoreAndMemberAndCardReqDTO;
import com.holderzone.holder.saas.member.terminal.dto.retail.ResponseMemberAndCardInfo;
import com.holderzone.holder.saas.member.terminal.dto.retail.ResponseRetailIntegralCompute;
import com.holderzone.saas.store.dto.retail.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.retail.bill.common.RetailItemDTO;
import com.holderzone.saas.store.dto.retail.bill.request.RetailCalculateReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import com.holderzone.saas.store.retail.context.RequestContext;
import com.holderzone.saas.store.retail.entity.bo.DiscountRuleBO;
import com.holderzone.saas.store.retail.entity.constant.OrderRedisConstant;
import com.holderzone.saas.store.retail.entity.domain.RetailDiscountDO;
import com.holderzone.saas.store.retail.entity.domain.RetailFreeReturnItemDO;
import com.holderzone.saas.store.retail.entity.domain.RetailOrderDO;
import com.holderzone.saas.store.retail.entity.domain.RetailOrderItemDO;
import com.holderzone.saas.store.retail.entity.enums.DiscountTypeEnum;
import com.holderzone.saas.store.retail.entity.enums.FreeReturnTypeEnum;
import com.holderzone.saas.store.retail.helper.RedisHelper;
import com.holderzone.saas.store.retail.service.CalculateService;
import com.holderzone.saas.store.retail.service.feign.BusinessClientService;
import com.holderzone.saas.store.retail.service.feign.RetailMemberBaseMsmService;
import com.holderzone.saas.store.retail.service.mp.RetailDiscountService;
import com.holderzone.saas.store.retail.service.mp.RetailFreeReturnService;
import com.holderzone.saas.store.retail.service.mp.RetailOrderItemService;
import com.holderzone.saas.store.retail.service.mp.RetailOrderService;
import com.holderzone.saas.store.retail.transform.RetailTransform;
import com.holderzone.saas.store.retail.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CalculateServiceImpl
 * @date 2019/01/28 17:28
 * @description
 * @program holder-saas-store-trade
 */
@Slf4j
@Service
public class CalculateServiceImpl implements CalculateService {

    private final RetailOrderService retailOrderService;

    private final RetailOrderItemService retailOrderItemService;

    private final RetailFreeReturnService retailFreeReturnService;

    private final RetailMemberBaseMsmService retailMemberBaseMsmService;

    private final BusinessClientService businessClientService;

    private final RetailDiscountService retailDiscountService;

    private RetailTransform retailTransform = RetailTransform.INSTANCE;

    private final RedisHelper redisHelper;

    @Autowired
    public CalculateServiceImpl(RetailFreeReturnService retailFreeReturnService,
                                BusinessClientService businessClientService, RedisHelper redisHelper,
                                RetailOrderService retailOrderService, RetailOrderItemService retailOrderItemService,
                                RetailMemberBaseMsmService retailMemberBaseMsmService,
                                RetailDiscountService retailDiscountService) {
        this.retailFreeReturnService = retailFreeReturnService;
        this.businessClientService = businessClientService;
        this.retailOrderService = retailOrderService;
        this.retailOrderItemService = retailOrderItemService;
        this.redisHelper = redisHelper;
        this.retailMemberBaseMsmService = retailMemberBaseMsmService;
        this.retailDiscountService = retailDiscountService;
    }


    @Override
    @Transactional
    public RetailOrderDetailRespDTO calculate(RetailCalculateReqDTO retailCalculate) {

        String orderGuid = retailCalculate.getOrderGuid();
        RetailOrderDO orderDO = retailOrderService.getById(orderGuid);
        if (!OrderUtil.unfinished(orderDO)) {
            throw new ParameterException("只有未结账订单可以结算");
        }
        RetailOrderDetailRespDTO orderDetailRespDTO;

        orderDetailRespDTO = getSingleOrderDetail(orderDO);

        //金额计算
        calculateAmount(orderDetailRespDTO, orderDO, retailCalculate);

        log.info("orderDetailRespDTO={}", orderDetailRespDTO);
        return orderDetailRespDTO;
    }


    //方法只用作构建结算页面显示的订单详细信息和计算每个菜的小计，金额和优惠计算移到外层
    private RetailOrderDetailRespDTO getSingleOrderDetail(RetailOrderDO orderDO) {
        String orderGuid = String.valueOf(orderDO.getGuid());
        List<RetailOrderItemDO> orderItemDOS = retailOrderItemService.listByOrderGuid(Long.valueOf(orderGuid));

        RetailOrderDetailRespDTO orderDetailRespDTO = retailTransform.retailOrderDO2RetailOrderDetailRespDTO(orderDO);

        //赠送信息
        List<RetailFreeReturnItemDO> freeReturnItemDOS = null;
        if (AmountCalculationUtil.hasFree(orderItemDOS)) {
            Map<Long, RetailOrderItemDO> itemDOMap = CollectionUtil.toMap(orderItemDOS, "guid");
            freeReturnItemDOS = retailFreeReturnService.list(new
                    LambdaQueryWrapper<RetailFreeReturnItemDO>().in(RetailFreeReturnItemDO::getOrderItemGuid, new
                    ArrayList<>
                    (itemDOMap.keySet())).eq(RetailFreeReturnItemDO::getType, FreeReturnTypeEnum.FREE.getCode()));
        }
        //计算菜品相关
        List<RetailItemDTO> retailItemDTOList = AmountCalculationUtil.buildItem(orderItemDOS,
                freeReturnItemDOS);

        orderDetailRespDTO.setDineInItemDTOS(retailItemDTOList);
        AmountCalculationUtil.filterZeroItem(orderDetailRespDTO);

        return orderDetailRespDTO;
    }


    private RetailOrderDetailRespDTO calculateAmount(RetailOrderDetailRespDTO orderDetailRespDTO, RetailOrderDO orderDO,
                                                     RetailCalculateReqDTO retailCalculateReqDTO) {
        log.warn("========================================开始计算订单金额==============================================");
        String orderGuid = orderDetailRespDTO.getGuid();
        log.warn("订单guid：{},订单号：{}", orderGuid, orderDetailRespDTO.getOrderNo());

        //取优惠规则&&返回当前的登录会员信息
        DiscountRuleBO discountRuleBO = getDiscountRuleBO(orderDO, retailCalculateReqDTO, orderDetailRespDTO);

        boolean hasMember = CommonUtil.hasGuid(orderDO.getMemberGuid());
        log.warn("是否登陆会员：{}", hasMember);

        //剩余可优惠金额
        orderDetailRespDTO.setOrderSurplusFee(orderDetailRespDTO.getOrderFee());

        //计算折扣优惠
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = new ArrayList<>();

        //并单把所有菜放在一起算
        List<RetailItemDTO> allItems = orderDetailRespDTO.getDineInItemDTOS();
        for (RetailItemDTO allItem : allItems) {
            allItem.setTotalDiscountFee(BigDecimal.ZERO);
        }
        log.warn("所有菜品：{}", JacksonUtils.writeValueAsString(allItems));

        //1.菜品赠送优惠
        DiscountFeeDetailDTO freeDiscount = getDiscountFeeDetailDTO(DiscountTypeEnum.FREE);
        //getFreeDiscountFee方法新增计算赠送优惠金额字段逻辑
        freeDiscount.setDiscountFee(AmountCalculationUtil.getFreeDiscountFee(allItems));
        orderDetailRespDTO.setOrderSurplusFee(orderDetailRespDTO.getOrderSurplusFee().subtract(freeDiscount
                .getDiscountFee()));
        discountFeeDetailDTOS.add(freeDiscount);
        log.warn("1.菜品赠送优惠：{}，订单剩余金额：{}", freeDiscount.getDiscountFee(), orderDetailRespDTO.getOrderSurplusFee());

        //3.3   =====================  单品折扣 START ====================================
        DiscountFeeDetailDTO singeItemDiscountFee = getDiscountFeeDetailDTO(DiscountTypeEnum.SINGLE_DISCOUNT);
        singeItemDiscountFee.setDiscountFee(AmountCalculationUtil.getSingleDiscountFee(allItems));
        orderDetailRespDTO.setOrderSurplusFee(orderDetailRespDTO.getOrderSurplusFee().subtract(singeItemDiscountFee
                .getDiscountFee()));
        discountFeeDetailDTOS.add(singeItemDiscountFee);
        log.warn("2.3 .单品则扣优惠：{}，订单剩余金额：{}", freeDiscount.getDiscountFee(), orderDetailRespDTO.getOrderSurplusFee
                ());
        // =============================单品折扣  END ===================================

        //5.整单折扣
        DiscountFeeDetailDTO whole = getDiscountFeeDetailDTO(DiscountTypeEnum.WHOLE);
        BigDecimal wholeDiscountFee = BigDecimal.ZERO;
        if (discountRuleBO.getWholeDiscount() != null) {
            for (RetailItemDTO retailItemDTO : allItems) {
                //菜品是否能参加整单折扣
                if (retailItemDTO.getIsWholeDiscount() == 1) {
                    BigDecimal discountCalculateFee = retailItemDTO.getItemPrice().subtract(BigDecimalUtil
                            .greaterThanZero(retailItemDTO.getTotalDiscountFee()) ? retailItemDTO
                            .getTotalDiscountFee() : BigDecimal.ZERO);
                    if (BigDecimalUtil.greaterThanZero(discountCalculateFee)) {
                        BigDecimal singleWholeDiscountFee = discountCalculateFee.multiply(BigDecimalUtil
                                .getRealDiscount(discountRuleBO.getWholeDiscount()));
                        retailItemDTO.setTotalDiscountFee(retailItemDTO.getTotalDiscountFee().add
                                (singleWholeDiscountFee));
                        wholeDiscountFee = wholeDiscountFee.add(singleWholeDiscountFee);
                    }
                }
            }
        }
        whole.setDiscountFee(BigDecimalUtil.setScale2(wholeDiscountFee));
        orderDetailRespDTO.setOrderSurplusFee(orderDetailRespDTO.getOrderSurplusFee().subtract(whole
                .getDiscountFee()));
        log.warn("5.整单折扣计算后订单剩余金额：{}，优惠金额：{}", orderDetailRespDTO.getOrderSurplusFee(), whole.getDiscountFee());
        //入参存入整单折扣数据
        whole.setDiscount(discountRuleBO.getWholeDiscount());
        discountFeeDetailDTOS.add(whole);


        //需要分摊的金额
        BigDecimal fentanAmt = BigDecimal.ZERO;

        //6.积分抵扣
        DiscountFeeDetailDTO pointsDeduction = getDiscountFeeDetailDTO(DiscountTypeEnum.POINTS_DEDUCTION);
        pointsDeduction.setRule(String.valueOf(discountRuleBO.getMemberIntegral()));
        discountFeeDetailDTOS.add(pointsDeduction);
        //入参传入是否积分抵扣数据
        pointsDeduction.setRule(String.valueOf(discountRuleBO.getMemberIntegral()));

        if (hasMember || StringUtils.isNotEmpty(retailCalculateReqDTO.getMemberInfoCardGuid())) {
            log.warn("6.积分抵扣请求金额：{}，卡号：{}", JacksonUtils.writeValueAsString(orderDetailRespDTO.getOrderSurplusFee()),
                    orderDO.getMemberCardGuid());
            ResponseRetailIntegralCompute compute = new ResponseRetailIntegralCompute();
            if (StringUtils.isNotEmpty(orderDO.getMemberCardGuid()) && !"0".equals(orderDO.getMemberCardGuid())) {
                compute = retailMemberBaseMsmService.retailCompute(orderDO.getMemberCardGuid(),
                        orderDetailRespDTO.getOrderSurplusFee());
            } else {
                if (StringUtils.isNotEmpty(retailCalculateReqDTO.getMemberInfoCardGuid()) && !"0".equals
                        (retailCalculateReqDTO.getMemberInfoCardGuid())) {
                    compute = retailMemberBaseMsmService.retailCompute(retailCalculateReqDTO.getMemberInfoCardGuid(),
                            orderDetailRespDTO.getOrderSurplusFee());
                }
            }

            log.warn("6.积分抵扣返回：{}", JacksonUtils.writeValueAsString(compute));
            if (BigDecimalUtil.greaterThanZero(compute.getDeductionMoney())) {
                if (discountRuleBO.getMemberIntegral() != null && discountRuleBO.getMemberIntegral() == 1) {
                    log.warn("6.积分抵扣金额大于0且开启积分抵扣");
                    pointsDeduction.setDiscountFee(compute.getDeductionMoney());
                    orderDetailRespDTO.setOrderSurplusFee(orderDetailRespDTO.getOrderSurplusFee().subtract
                            (pointsDeduction.getDiscountFee()));
                } else {
                    pointsDeduction.setDiscountFee(BigDecimal.ZERO);
                }

            }
            orderDetailRespDTO.setIntegralOffsetResultRespDTO(compute);
            log.warn("6.积分抵扣计算后订单剩余金额：{}，优惠金额：{}", orderDetailRespDTO.getOrderSurplusFee(), pointsDeduction
                    .getDiscountFee());
        } else {
            pointsDeduction.setDiscountFee(BigDecimal.ZERO);
        }
        if (pointsDeduction.getDiscountFee() != null) {
            fentanAmt = fentanAmt.add(pointsDeduction.getDiscountFee());
        }


        //7.系统省零
        DiscountFeeDetailDTO system = getDiscountFeeDetailDTO(DiscountTypeEnum.SYSTEM);
        system.setDiscountFee(AmountCalculationUtil.getSystemDiscountFee(discountRuleBO.getSystemDiscountDTOS(),
                orderDetailRespDTO.getOrderSurplusFee()));
        orderDetailRespDTO.setOrderSurplusFee(orderDetailRespDTO.getOrderSurplusFee().subtract(system.getDiscountFee
                ()));
        log.warn("7.系统省零计算后订单剩余金额：{}，优惠金额：{}", orderDetailRespDTO.getOrderSurplusFee(), system.getDiscountFee());
        discountFeeDetailDTOS.add(system);
        fentanAmt = fentanAmt.add(system.getDiscountFee());

        //8.整单让价
        DiscountFeeDetailDTO concessionalDiscount = getDiscountFeeDetailDTO(DiscountTypeEnum.CONCESSIONAL);
        concessionalDiscount.setDiscountFee(discountRuleBO.getConcessional() == null ? BigDecimal.ZERO : discountRuleBO
                .getConcessional());
        discountFeeDetailDTOS.add(concessionalDiscount);
        orderDetailRespDTO.setDiscountFeeDetailDTOS(discountFeeDetailDTOS);
        log.warn("8.整单让价优惠金额：{}", concessionalDiscount.getDiscountFee());

        //应付金额，优惠金额
        BigDecimal totalDiscountFee = BigDecimal.ZERO;
        for (DiscountFeeDetailDTO discountFeeDetailDTO : discountFeeDetailDTOS) {
            //单品折扣,单品改价不需要再减
            if (discountFeeDetailDTO.getDiscountFee() != null) {
                totalDiscountFee = totalDiscountFee.add(discountFeeDetailDTO.getDiscountFee());
            }
        }
        log.warn("全部优惠：{}", JacksonUtils.writeValueAsString(discountFeeDetailDTOS));
        orderDetailRespDTO.setDiscountFee(totalDiscountFee);
        orderDetailRespDTO.setActuallyPayFee(orderDetailRespDTO.getOrderFee().

                subtract(totalDiscountFee));
        log.warn("优惠金额合计：{},实付金额合计：{}", orderDetailRespDTO.getDiscountFee(), orderDetailRespDTO.getActuallyPayFee());

        //处理让价
        DiscountFeeDetailDTO concessional = new DiscountFeeDetailDTO();
        for (DiscountFeeDetailDTO discountFeeDetailDTO : orderDetailRespDTO.getDiscountFeeDetailDTOS()) {
            if (discountFeeDetailDTO.getDiscountFee() != null && discountFeeDetailDTO.getDiscountType().equals
                    (DiscountTypeEnum.CONCESSIONAL.getCode())) {
                concessional = discountFeeDetailDTO;
            }
        }

        //应付金额小于0时清除整单让价并重新计算优惠和应收
        if (BigDecimalUtil.lessThanZero(orderDetailRespDTO.getActuallyPayFee())) {
            orderDetailRespDTO.setActuallyPayFee(orderDetailRespDTO.getActuallyPayFee().add(concessional
                    .getDiscountFee()));
            orderDetailRespDTO.setDiscountFee(orderDetailRespDTO.getDiscountFee().subtract(concessional
                    .getDiscountFee()));
            concessional.setDiscountFee(BigDecimal.ZERO);
            RetailDiscountDO discountDO = retailTransform.retailDiscountFeeDetailDTO2DiscountDO(concessional);
            retailDiscountService.updateById(discountDO);
            log.warn("应付金额小于0时清除整单让价并重新计算优惠和应收,优惠金额合计：{},实付金额合计：{}", orderDetailRespDTO.getDiscountFee(),
                    orderDetailRespDTO.getActuallyPayFee());
            log.warn("清除整单让价后全部优惠：{}", JacksonUtils.writeValueAsString(discountFeeDetailDTOS));
        }
        fentanAmt = fentanAmt.add(concessional.getDiscountFee());
        retailOrderService.updateById(orderDO);
        discountShareEqually(allItems, fentanAmt);
        log.warn("========================================结束计算订单金额==============================================");
        return orderDetailRespDTO;
    }

    // 优惠均摊算法
    protected void discountShareEqually(List<RetailItemDTO> allItems, BigDecimal fentanAmt) {
        log.info("待分摊金额:" + fentanAmt);
        List<RetailOrderItemDO> retailOrderItemDOS = new ArrayList<>();
        BigDecimal totalActuallyPayFee = allItems.stream().map(a -> a.getItemPrice().subtract(a.getTotalDiscountFee()
        )).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal alreadyFentan = BigDecimal.ZERO;
        for (int i = 0; i < allItems.size(); i++) {
            RetailItemDTO allItem = allItems.get(i);
            if (fentanAmt.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal amt = allItem.getItemPrice().subtract(allItem.getTotalDiscountFee());
                BigDecimal fentan;
                if (totalActuallyPayFee.compareTo(BigDecimal.ZERO) != 0) {
                    //【wuhedong】先乘再除 修复精度问题  2019/11/8
                    fentan = BigDecimalUtil.setScale2(amt.multiply(fentanAmt).divide(totalActuallyPayFee, 2, BigDecimal.ROUND_HALF_UP));
                } else {
                    fentan = BigDecimal.ZERO;
                }
                // 需要分摊，则按销售占比分摊
                if (i == allItems.size() - 1) {
                    //最后一个用总均摊金额减之前所有
                    fentan = fentanAmt.subtract(alreadyFentan);
                } else {
                    alreadyFentan = alreadyFentan.add(fentan);
                }
                allItem.setTotalDiscountFee(allItem.getTotalDiscountFee().add(fentan));
            }
            RetailOrderItemDO retailOrderItemDO = new RetailOrderItemDO();
            retailOrderItemDO.setGuid(Long.valueOf(allItem.getGuid()));
            retailOrderItemDO.setTotalDiscountFee(allItem.getTotalDiscountFee());
            retailOrderItemDOS.add(retailOrderItemDO);

        }
        if (CollectionUtils.isNotEmpty(retailOrderItemDOS)) {
            retailOrderItemService.updateBatchById(retailOrderItemDOS);
        }
    }

    private DiscountFeeDetailDTO getDiscountFeeDetailDTO(DiscountTypeEnum discountTypeEnum) {
        DiscountFeeDetailDTO discountFeeDetailDTO = new DiscountFeeDetailDTO();
        discountFeeDetailDTO.setDiscountType(discountTypeEnum.getCode());
        discountFeeDetailDTO.setDiscountName(discountTypeEnum.getDesc());
        return discountFeeDetailDTO;
    }


    /**
     * 登陆登出逻辑保持原来不变，调用换成新会员的
     * 已经去掉获取会员折扣的代码
     *
     * @param orderDO
     * @param billCalculateReqDTO
     * @param orderDetailRespDTO
     * @return
     */
    private DiscountRuleBO getDiscountRuleBO(RetailOrderDO orderDO, RetailCalculateReqDTO billCalculateReqDTO,
                                             RetailOrderDetailRespDTO orderDetailRespDTO) {

        DiscountRuleBO discountRuleBO = retailTransform.billCalculateReqDTO2DiscountRuleBO(billCalculateReqDTO);

        //处理优惠规则
        dealDiscountRule(discountRuleBO);

        //处理会员
        if (CommonUtil.hasGuid(orderDO.getMemberGuid())) {
            log.info("memberphone={}", orderDO.getMemberPhone());
            ResponseMemberAndCardInfo memberInfoAndCard = retailMemberBaseMsmService.getMemberAndCardInfo(orderDO
                    .getMemberPhone());

            if (memberInfoAndCard.getMemberInfo().getAccountState() == 1) {
                removeMember(orderDO, orderDetailRespDTO);
            } else {
                if (billCalculateReqDTO.getMemberLogin() == -1) {
                    orderDetailRespDTO.setRetailMemberInfoAndCard(memberInfoAndCard);
                    orderDetailRespDTO.setMemberGuid(orderDO.getMemberGuid());
                    orderDetailRespDTO.setMemberCardGuid(orderDO.getMemberCardGuid());
                }
            }
        }
        if (StringUtils.isNotEmpty(billCalculateReqDTO.getMemberPhone())) {
            QueryStoreAndMemberAndCardReqDTO memberLoginDTO = new QueryStoreAndMemberAndCardReqDTO();
            memberLoginDTO.setPhoneNumOrCardNum(billCalculateReqDTO.getMemberPhone());
            memberLoginDTO.setStoreGuid(RequestContext.getStoreGuid());
            memberLoginDTO.setEnterpriseGuid(RequestContext.getEnterpriseGuid());
            log.info("memberphone={}", billCalculateReqDTO.getMemberPhone());
            ResponseMemberAndCardInfo memberInfoAndCard = retailMemberBaseMsmService.getMemberAndCardInfo
                    (billCalculateReqDTO.getMemberPhone());
            log.info("memberInfoAndCard={}", memberInfoAndCard);
            if (memberInfoAndCard.getMemberInfo().getAccountState() == 1) {
                throw new ParameterException("会员不可用");
            } else {
                orderDetailRespDTO.setRetailMemberInfoAndCard(memberInfoAndCard);
                if (billCalculateReqDTO.getMemberLogin() == 1) {
                    orderDO.setMemberGuid(memberInfoAndCard.getMemberInfo().getGuid());
                    orderDO.setMemberName(memberInfoAndCard.getMemberInfo().getNickName());

                    orderDO.setMemberPhone(memberInfoAndCard.getMemberInfo().getPhoneNum());
                    orderDO.setMemberCardGuid(memberInfoAndCard.getCardDetails().getGuid());
                    orderDO.setMemberCardNum(memberInfoAndCard.getCardDetails().getSystemManagementCardNum());

                    orderDetailRespDTO.setMemberGuid(memberInfoAndCard.getMemberInfo().getGuid());
                    orderDetailRespDTO.setMemberCardGuid(memberInfoAndCard.getCardDetails().getGuid());
                    orderDetailRespDTO.setRetailMemberInfoAndCard(memberInfoAndCard);
                    retailOrderService.updateById(orderDO);
                }
            }
        }

        //会员登出
        if (billCalculateReqDTO.getMemberLogin() == 2) {
            log.info("会员登出");
            removeMember(orderDO, orderDetailRespDTO);
        }

        //系统省零规则
        List<SystemDiscountDTO> systemDiscountDTOS = businessClientService.getSystemDiscount(RequestContext
                .getStoreGuid());
        List<com.holderzone.saas.store.dto.retail.SystemDiscountDTO> list = retailTransform.fromList2List
                (systemDiscountDTOS);
        discountRuleBO.setSystemDiscountDTOS(list);
        return discountRuleBO;
    }

    private void dealDiscountRule(DiscountRuleBO discountRuleBO) {
        String retailDiscountRuleKey = RedisKeyUtil.getKey(OrderRedisConstant.RETAIL_DISCOUNT_RULE,
                discountRuleBO.getOrderGuid());
        String retailDiscountRule = redisHelper.get(retailDiscountRuleKey);
        if (retailDiscountRule != null) {
            //前端无操作且redis暂存了之前的优惠规则时用redis里面的规则
            DiscountRuleBO discountRuleBOInRedis = JacksonUtils.toObject(DiscountRuleBO.class, retailDiscountRule);
            if (discountRuleBO.getWholeDiscount() == null && discountRuleBOInRedis.getWholeDiscount() != null) {
                discountRuleBO.setWholeDiscount(discountRuleBOInRedis.getWholeDiscount());
            }
            if (discountRuleBO.getMemberIntegral() == null && discountRuleBOInRedis.getMemberIntegral() != null) {
                discountRuleBO.setMemberIntegral(discountRuleBOInRedis.getMemberIntegral());
            }
            if (discountRuleBO.getConcessional() == null && discountRuleBOInRedis.getConcessional() != null) {
                discountRuleBO.setConcessional(discountRuleBOInRedis.getConcessional());
            }
        }
        redisHelper.setEx(retailDiscountRuleKey, JacksonUtils.writeValueAsString(discountRuleBO), OrderRedisConstant
                .ORDER_HOUR_TIMEOUT, TimeUnit.HOURS);

    }

    private void removeMember(RetailOrderDO orderDO, RetailOrderDetailRespDTO orderDetailRespDTO) {
        orderDO.setMemberGuid("0");
        orderDO.setMemberName(StringUtils.EMPTY);
        orderDO.setMemberPhone(StringUtils.EMPTY);
        orderDO.setCaculatByMemberPrice(0);
        orderDO.setMemberCardNum("0");

        orderDetailRespDTO.setSingleItemUsedMemeberPrice(0);
        orderDetailRespDTO.setMemberGuid("0");
        orderDetailRespDTO.setMemberCardGuid("0");
        orderDetailRespDTO.setMemberInfoAndCard(null);
        retailOrderService.updateById(orderDO);
        //会员登出时调用下会员的接口
        log.info("delDiscount:.memberConsumptionGuid={}", orderDO.getMemberConsumptionGuid());
        if (!"0".equals(orderDO.getMemberConsumptionGuid())) {
            //memberOrderClientService.delDiscount(orderDO.getMemberConsumptionGuid());
        }
    }

}
