package com.holderzone.saas.store.retail.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.erp.erpretail.req.SubstractGoodsReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.SubstractRepertoryForTradeReqDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.item.ItemReturnOrFreeReqDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.retail.bill.request.ItemReqDTO;
import com.holderzone.saas.store.dto.retail.bill.request.ReturnItemReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.CancelFreeItemReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.PriceChangeItemReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailAddGoodsReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.dto.retail.item.BatchItemReturnOrFreeReqDTO;
import com.holderzone.saas.store.retail.context.RequestContext;
import com.holderzone.saas.store.retail.entity.constant.GuidKeyConstant;
import com.holderzone.saas.store.retail.entity.domain.*;
import com.holderzone.saas.store.retail.entity.enums.*;
import com.holderzone.saas.store.retail.helper.DynamicHelper;
import com.holderzone.saas.store.retail.helper.RedisHelper;
import com.holderzone.saas.store.retail.service.DineInItemService;
import com.holderzone.saas.store.retail.service.DineInService;
import com.holderzone.saas.store.retail.service.MemberService;
import com.holderzone.saas.store.retail.service.feign.ErpClientService;
import com.holderzone.saas.store.retail.service.feign.RetailMemberBaseMsmService;
import com.holderzone.saas.store.retail.service.mp.RetailFreeReturnService;
import com.holderzone.saas.store.retail.service.mp.RetailOrderItemService;
import com.holderzone.saas.store.retail.service.mp.RetailOrderService;
import com.holderzone.saas.store.retail.service.mp.RetailTransactionRecordService;
import com.holderzone.saas.store.retail.transform.OrderTransform;
import com.holderzone.saas.store.retail.transform.RetailTransform;
import com.holderzone.saas.store.retail.utils.*;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.retail.entity.constant.GuidKeyConstant.FREE_RETURN_ITEM;
import static com.holderzone.saas.store.retail.entity.constant.GuidKeyConstant.HST_RETAIL_ORDER_ITEM;

/**
 * <AUTHOR>
 * @version 1.0
 * @className
 * @date 2018/09/04 16:10
 * @description //TODO
 * @program holder-saas-store-order
 */
@Log4j2
@Service
public class DineInItemServiceImpl implements DineInItemService {

    private final RetailOrderService retailOrderService;

    private final RetailOrderItemService retailOrderItemService;

    private final RetailFreeReturnService retailFreeReturnService;

    private final DynamicHelper dynamicHelper;

    private final RedisHelper redisHelper;

    private final DineInService dineInService;

    private final ErpClientService erpClientService;

    private final MemberService memberService;

    @Autowired
    private RetailTransactionRecordService retailTransactionRecordService;

    @Autowired
    private RetailMemberBaseMsmService retailMemberBaseMsmService;

    private OrderTransform orderTransform = OrderTransform.INSTANCE;

    private RetailTransform retailTransform = RetailTransform.INSTANCE;


    @Autowired
    public DineInItemServiceImpl(DynamicHelper dynamicHelper, RedisHelper redisHelper,
                                 DineInService dineInService,
                                 RetailOrderService retailOrderService,
                                 RetailOrderItemService retailOrderItemService,
                                 ErpClientService erpClientService,
                                 RetailFreeReturnService retailFreeReturnService,
                                 MemberService memberService) {
        this.memberService = memberService;
        this.dynamicHelper = dynamicHelper;
        this.redisHelper = redisHelper;
        this.dineInService = dineInService;
        this.retailOrderService = retailOrderService;
        this.retailOrderItemService = retailOrderItemService;
        this.erpClientService = erpClientService;
        this.retailFreeReturnService = retailFreeReturnService;
    }


    @Override
    public String addItem(RetailAddGoodsReqDTO retailAddGoodsReqDTO) {
        String orderGuid = retailAddGoodsReqDTO.getGuid();
        RetailOrderDO orderDOInDb = retailOrderService.getById(orderGuid);
        CreateDineInOrderReqDTO createDineInOrderReqDTO = orderTransform.Retail2Create(retailAddGoodsReqDTO);
        if (StringUtils.isEmpty(orderGuid)) {
            RetailOrderDO orderDO = createFastFoodOrder(retailAddGoodsReqDTO);
            createDineInOrderReqDTO.setGuid(String.valueOf(orderDO.getGuid()));
            orderGuid = String.valueOf(orderDO.getGuid());
        } else {
            updateFastFoodOrder(retailAddGoodsReqDTO);
            deleteItems(orderGuid, orderDOInDb);
        }
        if (CollectionUtil.isNotEmpty(createDineInOrderReqDTO.getDineInItemDTOS())) {
            this.batchAddItems(createDineInOrderReqDTO, Boolean.TRUE);
        }
        return orderGuid;
    }

    @Override
    @Transactional
    public EstimateItemRespDTO batchAddItems(CreateDineInOrderReqDTO createDineInOrderReqDTO, Boolean isFastFood) {
        EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
        //同一批次新增的菜品创建时间统一
        LocalDateTime now = LocalDateTime.now();
        Long orderGuid = Long.valueOf(createDineInOrderReqDTO.getGuid());

        List<RetailOrderItemDO> orderItemDOS = new ArrayList<>();
        List<RetailFreeReturnItemDO> freeReturnItemDOS = new ArrayList<>();
        //
        List<DineInItemDTO> dineInItemDTOS = createDineInOrderReqDTO.getDineInItemDTOS();
        if (CollectionUtils.isEmpty(createDineInOrderReqDTO.getDineInItemDTOS())) {
            throw new ParameterException("菜品信息不能为空");
        }
        RetailOrderDO orderDO = retailOrderService.getByIdWithLock(orderGuid);
        if (!OrderUtil.unfinished(orderDO)) {
            throw new ParameterException("订单状态不允许加菜");
        }
        String userGuid = RequestContext.getUserGuid();
        String userName = RequestContext.getUserName();
        //
        for (DineInItemDTO dineInItemDTO : dineInItemDTOS) {
            if (dineInItemDTO.getPriceChangeType() != null && dineInItemDTO.getPriceChangeType().intValue() == 2) {
                dineInItemDTO.setPrice(dineInItemDTO.getOriginalPrice());
            }
            RetailOrderItemDO orderItemDO = orderTransform.dineInItemDTO2RetailOrderItemDO(dineInItemDTO);
            Long orderItemGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_RETAIL_ORDER_ITEM);
            orderItemDO.setGmtCreate(now);
            orderItemDO.setGuid(orderItemGuid);
            orderItemDO.setOrderGuid(orderGuid);
            orderItemDO.setCurrentCount(dineInItemDTO.getCurrentCount());
            orderItemDO.setFreeCount(BigDecimal.ZERO);
            orderItemDO.setReturnCount(BigDecimal.ZERO);
            //操作人 及 guid
            orderItemDO.setCreateStaffGuid(userGuid);
            orderItemDO.setCreateStaffName(userName);
            orderItemDOS.add(orderItemDO);

            dineInItemDTO.setGuid(String.valueOf(orderItemGuid));
            //
            BigDecimal itemPrice = dineInItemDTO.getCurrentCount().multiply(dineInItemDTO.getPrice())
                    .setScale(2, BigDecimal.ROUND_HALF_UP);

            orderDO.setOrderFee(orderDO.getOrderFee().add(itemPrice));

            //处理未下单赠送
            List<FreeItemDTO> freeItemDTOS = dineInItemDTO.getFreeItemDTOS();
            if (!CollectionUtils.isEmpty(freeItemDTOS)) {
                for (FreeItemDTO freeItemDTO : freeItemDTOS) {
                    RetailFreeReturnItemDO freeReturnItemDO = new RetailFreeReturnItemDO();
                    freeReturnItemDO.setGmtCreate(now);
                    freeReturnItemDO.setStaffName(RequestContext.getUserName());
                    freeReturnItemDO.setStaffGuid(RequestContext.getUserGuid());
                    freeReturnItemDO.setGuid(dynamicHelper.generateGuid(GuidKeyConstant.HST_RETAIL_ORDER_ITEM));
                    freeReturnItemDO.setOrderItemGuid(orderItemDO.getGuid());
                    freeReturnItemDO.setOrderGuid(orderGuid);
                    freeReturnItemDO.setCount(freeItemDTO.getCount());
                    freeReturnItemDO.setReason(freeItemDTO.getReason());
                    freeReturnItemDO.setStoreGuid(RequestContext.getStoreGuid());
                    freeReturnItemDO.setStoreName(RequestContext.getStoreName());
                    freeReturnItemDO.setType(FreeReturnTypeEnum.FREE.getCode());
                    freeReturnItemDO.setItemGuid(orderItemDO.getItemGuid());
                    freeReturnItemDO.setItemName(orderItemDO.getItemName());
                    freeReturnItemDO.setPrice(orderItemDO.getOriginalPrice());
                    //未下单赠送不可能是已划
                    freeReturnItemDO.setServeCount(BigDecimal.ZERO);
                    freeReturnItemDO.setItemState(freeItemDTO.getItemState());
                    orderItemDO.setFreeCount(orderItemDO.getFreeCount().add(freeItemDTO.getCount()));
                    BigDecimal freeItemPrice = freeItemDTO.getCount().multiply(dineInItemDTO.getOriginalPrice());
                    orderDO.setOrderFee(orderDO.getOrderFee().add(freeItemPrice));
                    freeReturnItemDOS.add(freeReturnItemDO);
                }
            }

            orderDO.setOrderFee(orderDO.getOrderFee());
        }

        retailOrderService.updateById(orderDO);
        retailOrderItemService.saveBatch(orderItemDOS);

        if (!CollectionUtils.isEmpty(freeReturnItemDOS)) {
            retailFreeReturnService.saveBatch(freeReturnItemDOS);
        }
        estimateItemRespDTO.setResult(Boolean.TRUE);
        return estimateItemRespDTO;
    }

    private void deleteItems(String orderGuid, RetailOrderDO orderDOInDb) {
        retailOrderService.update(new RetailOrderDO(), new UpdateWrapper<RetailOrderDO>().lambda()
                .set(RetailOrderDO::getOrderFee, BigDecimal.ZERO)
                .eq(RetailOrderDO::getGuid, Long.valueOf(orderGuid)));

        List<RetailOrderItemDO> orderItemDOS = retailOrderItemService.listByOrderGuid(Long.valueOf(orderGuid));
        Map<Long, RetailOrderItemDO> itemDOMap = CollectionUtil.toMap(orderItemDOS, "guid");
        ArrayList<Long> orderItemGuids = new ArrayList<>(itemDOMap.keySet());
        if (CollectionUtil.isNotEmpty(orderItemGuids)) {
            //retailOrderItemService.removeByIdsWithDeleteCache(orderItemGuids, orderGuid);
            retailOrderItemService.removeByIds(orderItemGuids);
            retailFreeReturnService.removeByItemGuids(orderItemGuids);
        }
    }

    private void updateFastFoodOrder(RetailAddGoodsReqDTO retailAddGoodsReqDTO) {
        retailOrderService.update(new RetailOrderDO(), new UpdateWrapper<RetailOrderDO>().lambda()
                .set(RetailOrderDO::getRemark, retailAddGoodsReqDTO.getRemark())
                .eq(RetailOrderDO::getGuid, Long.valueOf(retailAddGoodsReqDTO.getGuid())));
    }

    private RetailOrderDO createFastFoodOrder(RetailAddGoodsReqDTO retailAddGoodsReqDTO) {
        RetailOrderDO orderDO = new RetailOrderDO();
        orderDO.setRemark(retailAddGoodsReqDTO.getRemark());
        if (retailAddGoodsReqDTO.getDeviceType() == null) {
            throw new ParameterException("设备类型不能为空");
        }
        Long orderGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_RETAIL_ORDER);
        orderDO.setGuid(orderGuid);
        //放到订单结账的时候生成orderNo
        //orderDO.setOrderNo(redisHelper.generateOrderNo("", RequestContext.getStoreGuid()));
        orderDO.setStoreGuid(RequestContext.getStoreGuid());
        orderDO.setStoreName(RequestContext.getUserInfo().getStoreName());
        orderDO.setDeviceType(retailAddGoodsReqDTO.getDeviceType());
        orderDO.setState(StateEnum.READY.getCode());
        orderDO.setOrderType(OrderTypeEnum.SIMPLE_ORDER.getCode());
        orderDO.setOrderFee(BigDecimal.ZERO);
        retailOrderService.save(orderDO);
        return orderDO;
    }


    @Override
    @Transactional
    public BatchItemReturnOrFreeReqDTO returnOrFreeItem(BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO,
                                                        Boolean isReturn) {
        List<BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq> itemReturnOrFreeReqs = batchItemReturnOrFreeReqDTO
                .getItemReturnOrFreeReqs();
        Map<String, BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq> returnOrFreeReqMap = CollectionUtil.toMap
                (itemReturnOrFreeReqs, "orderItemGuid");
        List<RetailOrderItemDO> orderItemDOS = retailOrderItemService.listByIdsWithLock(returnOrFreeReqMap.keySet());
        Map<Long, RetailOrderItemDO> itemDOMap = CollectionUtil.toMap(orderItemDOS, "guid");

        List<RetailOrderItemDO> returnPrintList = new ArrayList<>();
        List<RetailOrderItemDO> returnEstimateList = new ArrayList<>();
        List<RetailFreeReturnItemDO> saveList = new ArrayList<>();
        List<RetailFreeReturnItemDO> updateList = new ArrayList<>();
        List<RetailOrderItemDO> saveOrderItemDOS = new ArrayList<>();
        List<RetailOrderItemDO> itemUpdateList = new ArrayList<>();
        OrderDO orderDO = null;
        for (BatchItemReturnOrFreeReqDTO.ItemReturnOrFreeReq itemReturnOrFreeReq : itemReturnOrFreeReqs) {
            RetailOrderItemDO orderItemDO = itemDOMap.get(Long.valueOf(itemReturnOrFreeReq.getOrderItemGuid()));
            if (orderDO == null) {
//                orderDO = orderService.getByIdWithLock(orderItemDO.getOrderGuid());
            }
            if (!OrderUtil.unfinished(orderDO)) {
                throw new ParameterException("订单状态异常");
            }
            RetailOrderItemDO returnPrint = orderItemDO;

            List<ItemReturnOrFreeReqDTO> itemReturnOrFreeReqDTOS = itemReturnOrFreeReq.getItemReturnOrFreeReqDTOS();
            Map<String, ItemReturnOrFreeReqDTO> freeReqDTOMap = CollectionUtil.toMap(itemReturnOrFreeReqDTOS,
                    "freeGuid");
            RetailOrderItemDO saveOrderItemDO = null;
            boolean isadd = false;
            Map<Long, RetailFreeReturnItemDO> freeReturnItemDOMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(freeReqDTOMap)) {
                List<RetailFreeReturnItemDO> freeReturnItemDOS = new ArrayList<>(retailFreeReturnService.listByIds(new
                        ArrayList<>(freeReqDTOMap.keySet())));
                freeReturnItemDOMap = CollectionUtil.toMap(freeReturnItemDOS, "guid");
            }
            BigDecimal allItemCount = itemReturnOrFreeReqDTOS.stream().map(ItemReturnOrFreeReqDTO::getCount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            log.info("赠送的数量是{}，总数是{}", allItemCount, orderItemDO.getCurrentCount());
            if (orderItemDO.getPriceChangeType() != null && !isReturn && (orderItemDO.getPriceChangeType().intValue() == 1 || orderItemDO.getPriceChangeType().intValue() == 2)) {
                if (allItemCount.compareTo(orderItemDO.getCurrentCount()) < 0) {
                    isadd = true;
                    //拆分成一个新的orderItem
                    Long itemguid = dynamicHelper.generateGuid(HST_RETAIL_ORDER_ITEM);
                    saveOrderItemDO = JacksonUtils.toObject(RetailOrderItemDO.class, JacksonUtils
                            .writeValueAsString(orderItemDO));
                    saveOrderItemDO.setGuid(itemguid);
                    saveOrderItemDO.setCurrentCount(BigDecimal.ZERO);
                    saveOrderItemDO.setFreeCount(BigDecimal.ZERO);
                    saveOrderItemDO.setReturnCount(BigDecimal.ZERO);
                    saveOrderItemDO.setPrice(orderItemDO.getOriginalPrice());
                    saveOrderItemDO.setPriceChangeType(0);
                    saveOrderItemDOS.add(saveOrderItemDO);
                } else {
                    orderItemDO.setPrice(orderItemDO.getOriginalPrice());
                    orderItemDO.setPriceChangeType(0);
                }
                if (orderItemDO.getPriceChangeType().intValue() == 1) {
                    orderDO.setOrderFee(orderDO.getOrderFee().add(orderItemDO.getOriginalPrice().subtract(orderItemDO.getPrice()).multiply(allItemCount)));
                } else {
//                    orderItemDO.setDiscountPercent(1000);
                    orderDO.setOrderFee(orderDO.getOrderFee().add(orderItemDO.getOriginalPrice()
                            .multiply(new BigDecimal(1000).subtract(new BigDecimal(orderItemDO.getDiscountPercent()))
                                    .divide(new BigDecimal(1000), 2, BigDecimal.ROUND_HALF_UP)
                            ).multiply(allItemCount)));
                }
            }
            for (ItemReturnOrFreeReqDTO itemReturnOrFreeReqDTO : itemReturnOrFreeReqDTOS) {
                BigDecimal count = itemReturnOrFreeReqDTO.getCount();
                // 退菜数量不会很多，暂时不改为批量生成
                Long guid = dynamicHelper.generateGuid(FREE_RETURN_ITEM);
                itemReturnOrFreeReqDTO.setGuid(String.valueOf(guid));
                RetailFreeReturnItemDO freeReturnItemDO = retailTransform.retailOrderItemDO2freeReturnItemDO(orderItemDO);
                freeReturnItemDO.setStaffName(RequestContext.getUserName());
                freeReturnItemDO.setStaffGuid(RequestContext.getUserGuid());
                freeReturnItemDO.setGuid(guid);
                freeReturnItemDO.setOrderItemGuid(orderItemDO.getGuid());
                freeReturnItemDO.setOrderGuid(orderItemDO.getOrderGuid());
                freeReturnItemDO.setCount(count);
                freeReturnItemDO.setReason(itemReturnOrFreeReqDTO.getReason());
                freeReturnItemDO.setStoreGuid(RequestContext.getStoreGuid());
                freeReturnItemDO.setStoreName(RequestContext.getStoreName());
                freeReturnItemDO.setItemGuid(orderItemDO.getItemGuid());
                freeReturnItemDO.setItemName(orderItemDO.getItemName());
                freeReturnItemDO.setPrice(isReturn ? orderItemDO.getPrice() : orderItemDO.getOriginalPrice());
                freeReturnItemDO.setType(isReturn ? FreeReturnTypeEnum.RETURN.getCode() : FreeReturnTypeEnum.FREE
                        .getCode());
                freeReturnItemDO.setIsFree(itemReturnOrFreeReqDTO.getIsFree());
                if (isReturn) {

                    //增加退菜数量
                    orderItemDO.setReturnCount(orderItemDO.getReturnCount().add(count));

                    //退货为赠送
                    if (itemReturnOrFreeReqDTO.getIsFree() != null && itemReturnOrFreeReqDTO.getIsFree() == 1) {
                        //减少赠送数量
                        orderItemDO.setFreeCount(orderItemDO.getFreeCount().subtract(count));
                        RetailFreeReturnItemDO freeReturnItemServiceById = freeReturnItemDOMap.get(Long.valueOf
                                (itemReturnOrFreeReqDTO.getFreeGuid()));
                        freeReturnItemServiceById.setCount(freeReturnItemServiceById.getCount().subtract(count));
                        updateList.add(freeReturnItemServiceById);
                        freeReturnItemDO.setItemState(freeReturnItemServiceById.getItemState());
                    } else {
                        //减少当前数量
                        orderItemDO.setCurrentCount(orderItemDO.getCurrentCount().subtract(count));
                    }

                } else {
                    if (isadd) {
                        saveOrderItemDO.setFreeCount(saveOrderItemDO.getFreeCount().add(count));
                    } else {
                        orderItemDO.setFreeCount(orderItemDO.getFreeCount().add(count));
                    }
                    orderItemDO.setCurrentCount(orderItemDO.getCurrentCount().subtract(count));
                    //判断赠送是否被划菜 后期改为数量之后只有前端传入
                    if (itemReturnOrFreeReqDTO.getIsServe() != null && itemReturnOrFreeReqDTO.getIsServe() == 1) {
                        freeReturnItemDO.setServeCount(freeReturnItemDO.getCount());
                        freeReturnItemDO.setItemState(ItemStateEnum.SERVED.getCode());
                    } else {
                        freeReturnItemDO.setServeCount(BigDecimal.ZERO);
                    }
                }
                saveList.add(freeReturnItemDO);
                //暂存退菜数量
                returnPrint.setCurrentCount(count);
                //挂起菜品退菜时不打印退菜单
                if (!returnPrint.getItemState().equals(ItemStateEnum.HANG_UP.getCode())) {
                    returnPrintList.add(returnPrint);
                }
                returnEstimateList.add(returnPrint);

            }

            orderItemDO.setGmtModified(null);
            if (BigDecimalUtil.lessThanZero(orderItemDO.getCurrentCount()) || BigDecimalUtil.lessThanZero(orderItemDO
                    .getFreeCount())) {
                throw new ParameterException("操作数量超出限制");
            }
            itemUpdateList.add(orderItemDO);
        }

        if (CollectionUtil.isNotEmpty(itemUpdateList)) {
            retailOrderItemService.updateBatchById(itemUpdateList);
        }
        if (CollectionUtil.isNotEmpty(saveList)) {
            retailFreeReturnService.saveBatch(saveList);
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            retailFreeReturnService.updateBatchById(updateList);
        }

        //退货时修改订单金额
        if (orderDO != null) {
            RetailOrderDetailRespDTO orderDetail = dineInService.getOrderDetail(String.valueOf(orderDO.getGuid()));
            BigDecimal orderFee = AmountCalculationUtil.getOrderFee(orderDetail.getDineInItemDTOS(), BigDecimal.ZERO);
            if (BigDecimalUtil.greaterThanZero(orderDO.getAppendFee())) {
                orderFee = orderFee.add(orderDO.getAppendFee());
            }
            orderDO.setOrderFee(orderFee);
//            orderService.updateByIdWithDeleteCache(orderDO);
        }
        return batchItemReturnOrFreeReqDTO;
    }

    @Override
    @Transactional
    public Boolean cancelFree(CancelFreeItemReqDTO cancelFreeItemReqDTO) {
        List<RetailOrderItemDO> updateOrderItemDOS = new ArrayList<>();
        List<RetailOrderItemDO> saveOrderItemDOS = new ArrayList<>();
        List<String> removeIds = new ArrayList<>();
        List<String> freeItemGuids = cancelFreeItemReqDTO.getFreeItemGuids();
        String orderGuid = "";
        List<RetailFreeReturnItemDO> freeReturnItemDOS = new ArrayList<>(retailFreeReturnService.listByIds(freeItemGuids));
        if (CollectionUtil.isNotEmpty(freeReturnItemDOS)) {
            Map<Long, RetailFreeReturnItemDO> freeReturnItemDOMap = CollectionUtil.toMap(freeReturnItemDOS, "guid");
            Map<Long, RetailFreeReturnItemDO> freeReturnItemDOMapByOrderItemGuid = CollectionUtil.toMap(freeReturnItemDOS,
                    "orderItemGuid");
            List<RetailOrderItemDO> orderItemDOS = new ArrayList<>(retailOrderItemService.listByIds(new ArrayList<>
                    (freeReturnItemDOMapByOrderItemGuid.keySet())));
            Map<Long, RetailOrderItemDO> orderItemDOMap = CollectionUtil.toMap(orderItemDOS, "guid");
            for (String freeItemGuid : freeItemGuids) {
                RetailFreeReturnItemDO freeReturnItemDO = freeReturnItemDOMap.get(Long.valueOf(freeItemGuid));
                RetailOrderItemDO orderItemDO = orderItemDOMap.get(freeReturnItemDO.getOrderItemGuid());
                orderItemDO.setFreeCount(orderItemDO.getFreeCount().subtract(freeReturnItemDO.getCount()));

                removeIds.add(freeItemGuid);
                boolean add = !Objects.equals(freeReturnItemDO.getItemState(), orderItemDO.getItemState());
                //状态不一致时拆分菜品
                if (add) {
                    RetailOrderItemDO saveOrderItemDO = JacksonUtils.toObject(RetailOrderItemDO.class, JacksonUtils
                            .writeValueAsString(orderItemDO));
                    Long guid = dynamicHelper.generateGuid(GuidKeyConstant.HST_RETAIL_ORDER_ITEM);
                    saveOrderItemDO.setGuid(guid);
                    saveOrderItemDO.setCurrentCount(freeReturnItemDO.getCount());
                    saveOrderItemDO.setFreeCount(BigDecimal.ZERO);
                    saveOrderItemDO.setReturnCount(BigDecimal.ZERO);
                    saveOrderItemDO.setItemState(freeReturnItemDO.getItemState());
                    saveOrderItemDOS.add(saveOrderItemDO);

                } else {
                    orderItemDO.setCurrentCount(orderItemDO.getCurrentCount().add(freeReturnItemDO.getCount()));
                }
                updateOrderItemDOS.add(orderItemDO);
            }
            retailOrderItemService.removeByIds(removeIds);
            retailOrderItemService.updateBatchById(updateOrderItemDOS);
            retailOrderItemService.saveBatch(saveOrderItemDOS);
        }

        return Boolean.TRUE;
    }


    @Override
    public Boolean changePrice(PriceChangeItemReqDTO priceChangeItemReqDTO) {
        RetailOrderItemDO orderItemDO = retailTransform.priceChangeItemReqDTO2RetailOrderItemDO(priceChangeItemReqDTO);
        String redisKey = RedisKeyUtil.getHstOrderItemKey(priceChangeItemReqDTO.getOrderGuid());
        boolean update = retailOrderItemService.updateById(orderItemDO);
        if (update) {
            redisHelper.delete(redisKey);
        }
        return update;
    }

    /**
     * 隔离级别最高 直接锁完,防止各种读
     * 退货接口
     *
     * @param returnItemReqDTO
     * @return
     */
    @Override
    @Transactional(isolation = Isolation.SERIALIZABLE)
    public Boolean returnItems(ReturnItemReqDTO returnItemReqDTO) {
        RetailOrderDO orderDO = retailOrderService.getById(returnItemReqDTO.getOrderGuid());
        if (orderDO.getState().intValue() != StateEnum.SUCCESS.getCode()) {
            throw new UnsupportedOperationException("订单状态错误，当前订单状态无法退单");
        }
        List<RetailOrderItemDO> retailOrderItemDOS = retailOrderItemService.listByOrderGuid(Long.valueOf
                (returnItemReqDTO.getOrderGuid()));
        Map<Long, RetailOrderItemDO> orderIdWithOrderItemDoMap = retailOrderItemDOS.stream().collect(Collectors.toMap
                (RetailOrderItemDO::getGuid, Function.identity()));
        List<ItemReqDTO> returnItemGuids = returnItemReqDTO.getItemsReqDTOS();
        RetailOrderDO newRetailOrderDO = new RetailOrderDO();
        Long orderGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_RETAIL_ORDER);
        List<RetailOrderItemDO> returnOrderItemDOs = new ArrayList<>();
        List<RetailOrderItemDO> updateOrderItemDOs = new ArrayList<>();
        newRetailOrderDO.setGuid(orderGuid);
        newRetailOrderDO.setReason(returnItemReqDTO.getReason());
        List<Long> itemGuids = dynamicHelper.generateGuids(GuidKeyConstant.HST_RETAIL_ORDER_ITEM, returnItemGuids
                .size());
        SubstractRepertoryForTradeReqDTO saleOutRepertoryDTO = new SubstractRepertoryForTradeReqDTO();
        List<SubstractGoodsReqDTO> saleItemsDTOs = new ArrayList<>();
        saleOutRepertoryDTO.setInvoiceNo(String.valueOf(orderGuid));
        saleOutRepertoryDTO.setInvoiceType(2);
        saleOutRepertoryDTO.setDetailList(saleItemsDTOs);
        boolean isLastItemOfTheOrder = checkIsLastTimeReturnItems(retailOrderItemDOS, returnItemGuids);
        for (ItemReqDTO itemsReqDTO : returnItemGuids) {
            Long itemGuid = itemsReqDTO.getItemGuid();
            RetailOrderItemDO retailOrderItemDO = orderIdWithOrderItemDoMap.get(itemGuid);
            if(retailOrderItemDO == null || retailOrderItemDO.getFreeCount().add(retailOrderItemDO.getCurrentCount()).
                    subtract(retailOrderItemDO.getReturnCount()).compareTo(itemsReqDTO.getCount()) <0){
                throw new RuntimeException("数量超出，没有多余的数量可退");
            }
            RetailOrderItemDO returnRetailOrderItemDO = new RetailOrderItemDO();
            returnRetailOrderItemDO.setGuid(itemGuids.remove(0));
            oldItem2NewItem(retailOrderItemDO, returnRetailOrderItemDO);
            returnRetailOrderItemDO.setOrderGuid(orderGuid);
            if(retailOrderItemDO.getCurrentCount().compareTo(BigDecimal.ZERO) > 0){
                returnRetailOrderItemDO.setCurrentCount(itemsReqDTO.getCount().negate());
                returnRetailOrderItemDO.setPrice(retailOrderItemDO.getPrice().subtract(retailOrderItemDO.getTotalDiscountFee().divide(retailOrderItemDO
                        .getCurrentCount(), 2, BigDecimal.ROUND_HALF_DOWN)));
            }else{
                returnRetailOrderItemDO.setFreeCount(itemsReqDTO.getCount().negate());
                returnRetailOrderItemDO.setPrice(retailOrderItemDO.getPrice());
            }
            returnOrderItemDOs.add(returnRetailOrderItemDO);
            // update the count of old item
            RetailOrderItemDO updateretailOrderItemDO = new RetailOrderItemDO();
            updateretailOrderItemDO.setGuid(retailOrderItemDO.getGuid());
            updateretailOrderItemDO.setReturnCount(retailOrderItemDO.getReturnCount().add(itemsReqDTO.getCount()));
            updateOrderItemDOs.add(updateretailOrderItemDO);
            // add resp
            SubstractGoodsReqDTO saleGoodsDTO = new SubstractGoodsReqDTO();
            saleGoodsDTO.setCount(itemsReqDTO.getCount());
            saleGoodsDTO.setUnitPrice(returnRetailOrderItemDO.getPrice());
            saleGoodsDTO.setGoodsGuid(retailOrderItemDO.getItemGuid());
            saleItemsDTOs.add(saleGoodsDTO);
        }

        oldOrder2ReturnOrder(orderDO, newRetailOrderDO,returnItemReqDTO);

        if (isLastItemOfTheOrder){
            memberService.refoundOrder(orderDO,newRetailOrderDO.getOrderNo(),true,true);
        }
        RetailTransactionRecordDO retailTransaction = getReturnOrderRetailTransaction(newRetailOrderDO);
        //异步？？？
        erpClientService.saleOutRepertory(saleOutRepertoryDTO);
        retailTransactionRecordService.save(retailTransaction);
        retailOrderService.updateById(orderDO);
        retailOrderService.save(newRetailOrderDO);
        retailOrderItemService.updateBatchById(updateOrderItemDOs);
        retailOrderItemService.saveBatch(returnOrderItemDOs);
        return true;
    }


    /**
     * 判断是否是最后一次退货
     * @param retailOrderItemDOS
     * @param itemReqDTOS
     * @return
     */
    public boolean checkIsLastTimeReturnItems(List<RetailOrderItemDO> retailOrderItemDOS,List<ItemReqDTO> itemReqDTOS){
        Map<Long, ItemReqDTO> collect = itemReqDTOS.stream().collect(Collectors.toMap(ItemReqDTO::getItemGuid, Function.identity()));
        for(RetailOrderItemDO retailOrderItemDO : retailOrderItemDOS){
            ItemReqDTO itemReqDTO = collect.get(retailOrderItemDO.getGuid());
            if(itemReqDTO == null){
                if(retailOrderItemDO.getCurrentCount().add(retailOrderItemDO.getFreeCount()).compareTo(retailOrderItemDO.getReturnCount()) > 0){
                    return false;
                }
            }else if (retailOrderItemDO.getCurrentCount().add(retailOrderItemDO.getFreeCount()).subtract(retailOrderItemDO.getReturnCount()).compareTo(itemReqDTO.getCount()) > 0) {
                return false;
            }
        }
        return true;
    }
    /**
     * 隔离级别最高 直接锁完,防止各种读
     * 退单接口
     *
     * @param returnItemReqDTO
     * @return
     */
    @Override
    @Transactional(isolation = Isolation.SERIALIZABLE)
    public Boolean returnOrder(ReturnItemReqDTO returnItemReqDTO) {
        RetailOrderDO orderDO = retailOrderService.getById(returnItemReqDTO.getOrderGuid());
        if (orderDO.getState().intValue() != StateEnum.SUCCESS.getCode()) {
            throw new UnsupportedOperationException("订单状态错误，当前订单状态无法退单");
        }
        List<RetailOrderItemDO> orderItemDOS = retailOrderItemService.listByOrderGuid(Long.valueOf(returnItemReqDTO
                .getOrderGuid()));
        Map<Long, RetailOrderItemDO> orderIdWithOrderItemDoMap = orderItemDOS.stream().collect(Collectors.toMap
                (RetailOrderItemDO::getGuid, Function.identity()));
        RetailOrderDO retailOrderDO = new RetailOrderDO();
        Long orderGuid = dynamicHelper.generateGuid(GuidKeyConstant.HST_RETAIL_ORDER);
        List<RetailOrderItemDO> returnOrderItemDOS = new ArrayList<>();
        List<RetailOrderItemDO> updateOrderItemDOS = new ArrayList<>();
        List<Long> itemGuids = dynamicHelper.generateGuids(HST_RETAIL_ORDER_ITEM, orderItemDOS.size());
        retailOrderDO.setGuid(orderGuid);
        SubstractRepertoryForTradeReqDTO saleOutRepertoryDTO = new SubstractRepertoryForTradeReqDTO();
        List<SubstractGoodsReqDTO> saleGoodsDTOs = new ArrayList<>();
        saleOutRepertoryDTO.setInvoiceNo(String.valueOf(orderGuid));
        saleOutRepertoryDTO.setInvoiceType(2);
        saleOutRepertoryDTO.setDetailList(saleGoodsDTOs);
        for (RetailOrderItemDO oldretailOrderItemDO : orderItemDOS) {
            if (oldretailOrderItemDO.getCurrentCount().compareTo(BigDecimal.ZERO) == 0 && oldretailOrderItemDO.getFreeCount().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            Long itemGuid = oldretailOrderItemDO.getGuid();
            RetailOrderItemDO retailOrderItemDO = orderIdWithOrderItemDoMap.get(itemGuid);
            // deal new Item Start ============================
            RetailOrderItemDO returnRetailOrderItemDo = new RetailOrderItemDO();
            returnRetailOrderItemDo.setGuid(itemGuids.remove(0));
            oldItem2NewItem(retailOrderItemDO, returnRetailOrderItemDo);
            returnRetailOrderItemDo.setOrderGuid(orderGuid);
            returnRetailOrderItemDo.setCurrentCount(oldretailOrderItemDO.getCurrentCount().negate());
            returnRetailOrderItemDo.setFreeCount(oldretailOrderItemDO.getFreeCount().negate());
            if (oldretailOrderItemDO.getCurrentCount().compareTo(BigDecimal.ZERO) > 0) {
                returnRetailOrderItemDo.setPrice(retailOrderItemDO.getPrice().subtract(retailOrderItemDO.getTotalDiscountFee().divide(retailOrderItemDO
                        .getCurrentCount(), 2, BigDecimal.ROUND_HALF_UP)));
            } else {
                returnRetailOrderItemDo.setPrice(retailOrderItemDO.getPrice());
            }
            returnOrderItemDOS.add(returnRetailOrderItemDo);
            // update the count of old item
            RetailOrderItemDO updateretailOrderItemDO = new RetailOrderItemDO();
            updateretailOrderItemDO.setGuid(retailOrderItemDO.getGuid());
            updateretailOrderItemDO.setReturnCount(retailOrderItemDO.getCurrentCount().add(oldretailOrderItemDO.getFreeCount()));
            updateOrderItemDOS.add(updateretailOrderItemDO);
            SubstractGoodsReqDTO saleGoodsDTO = new SubstractGoodsReqDTO();
            saleGoodsDTO.setCount(oldretailOrderItemDO.getCurrentCount().subtract(oldretailOrderItemDO.getReturnCount
                    ()).add(oldretailOrderItemDO.getFreeCount()));
            saleGoodsDTO.setGoodsGuid(retailOrderItemDO.getItemGuid());
            saleGoodsDTO.setUnitPrice(returnRetailOrderItemDo.getPrice());
            saleGoodsDTOs.add(saleGoodsDTO);
        }
        oldOrder2ReturnOrder(orderDO, retailOrderDO, returnItemReqDTO);
        memberService.refoundOrder(orderDO,retailOrderDO.getOrderNo(),true,true);
        //异步？？？
        erpClientService.saleOutRepertory(saleOutRepertoryDTO);
        RetailTransactionRecordDO retailTransaction = getReturnOrderRetailTransaction(retailOrderDO);
        retailTransactionRecordService.save(retailTransaction);
        retailOrderService.updateById(orderDO);
        retailOrderService.save(retailOrderDO);
        retailOrderItemService.updateBatchById(updateOrderItemDOS);
        retailOrderItemService.saveBatch(returnOrderItemDOS);
        return true;
    }

    /**
     * 通过新单生成退款记录
     *
     * @param retailOrderDO
     * @return
     */
    public RetailTransactionRecordDO getReturnOrderRetailTransaction(RetailOrderDO retailOrderDO) {
        Long guid = dynamicHelper.generateGuid(GuidKeyConstant.HST_TRANSACTION_RECORD);
        RetailTransactionRecordDO transactionRecordDO = new RetailTransactionRecordDO();
        transactionRecordDO.setGuid(guid);
        transactionRecordDO.setOrderGuid(Long.valueOf(retailOrderDO.getGuid()));
        transactionRecordDO.setAmount(retailOrderDO.getActuallyPayFee());
        transactionRecordDO.setPaymentType(PaymentTypeEnum.CASH.getCode());
        transactionRecordDO.setPaymentTypeName(PaymentTypeEnum.CASH.getDesc());
        transactionRecordDO.setStaffGuid(RequestContext.getUserGuid());
        transactionRecordDO.setStaffName(RequestContext.getUserName());
        transactionRecordDO.setState(TradeStateEnum.SUCCESS.getCode());
        transactionRecordDO.setStoreGuid(RequestContext.getStoreGuid());
        transactionRecordDO.setStoreName(RequestContext.getStoreName());
        transactionRecordDO.setTradeType(TradeTypeEnum.REFUND_OUT.getCode());
        transactionRecordDO.setBusinessDay(retailOrderDO.getBusinessDay());
        return transactionRecordDO;
    }

    /**
     * 退单的时候  转换订单
     *
     * @param oldOrderDO
     * @param newOrderDo
     */
    public void oldOrder2ReturnOrder(RetailOrderDO oldOrderDO, RetailOrderDO newOrderDo, ReturnItemReqDTO returnItemReqDTO) {
        String orderNo = redisHelper.generateOrderNo("", RequestContext.getStoreGuid());
        newOrderDo.setOrderNo(orderNo);
        newOrderDo.setCheckoutDeviceType(returnItemReqDTO.getDeviceType());
        newOrderDo.setOrderFee(returnItemReqDTO.getShouldReturnAmount().negate());
        newOrderDo.setActuallyPayFee(returnItemReqDTO.getActualReturnAmount().negate());
        newOrderDo.setDeviceType(oldOrderDO.getDeviceType());
        newOrderDo.setBusinessDay(oldOrderDO.getBusinessDay());
        newOrderDo.setState(StateEnum.REFUNDED.getCode());
        newOrderDo.setOrderType(OrderTypeEnum.RETURN_ORDER.getCode());
        newOrderDo.setStoreGuid(oldOrderDO.getStoreGuid());
        newOrderDo.setStoreName(oldOrderDO.getStoreName());
        newOrderDo.setCheckoutTime(LocalDateTime.now());
        newOrderDo.setMemberCardNum(oldOrderDO.getMemberCardNum());
        newOrderDo.setMemberName(oldOrderDO.getMemberName());
        newOrderDo.setMemberGuid(newOrderDo.getMemberGuid());
        newOrderDo.setCheckoutStaffGuid(RequestContext.getUserGuid());
        newOrderDo.setCheckoutStaffName(RequestContext.getUserName());
        newOrderDo.setCheckoutStaffAccount(RequestContext.getAccount());
        newOrderDo.setOriginalOrderGuid(oldOrderDO.getGuid());
    }

    /**
     * 退货的时候Item转换操作
     *
     * @param retailOrderItemDO
     * @param newOrderItemDo
     */
    public void oldItem2NewItem(RetailOrderItemDO retailOrderItemDO, RetailOrderItemDO newOrderItemDo) {
        newOrderItemDo.setItemGuid(retailOrderItemDO.getItemGuid());
        newOrderItemDo.setItemName(retailOrderItemDO.getItemName());
        newOrderItemDo.setCode(retailOrderItemDO.getCode());
        newOrderItemDo.setItemType(retailOrderItemDO.getItemType());
        newOrderItemDo.setItemTypeGuid(retailOrderItemDO.getItemTypeGuid());
        newOrderItemDo.setItemTypeName(retailOrderItemDO.getItemTypeName());
        newOrderItemDo.setSkuGuid(retailOrderItemDO.getSkuGuid());
        newOrderItemDo.setSkuName(retailOrderItemDO.getSkuName());
        newOrderItemDo.setOriginalPrice(retailOrderItemDO.getOriginalPrice());
        newOrderItemDo.setUnit(retailOrderItemDO.getUnit());
        newOrderItemDo.setItemUpc(retailOrderItemDO.getItemUpc());
        newOrderItemDo.setStoreGuid(retailOrderItemDO.getStoreGuid());
        newOrderItemDo.setStoreName(retailOrderItemDO.getStoreName());
        newOrderItemDo.setCreateStaffGuid(RequestContext.getUserGuid());
        newOrderItemDo.setCreateStaffName(RequestContext.getUserName());
    }

    /**
     * 计算应该退的金额
     * case 1 :
     * 如果： 退货的数量 = 商品的数量 - 已经退的数量
     * 则：
     * 退货的价格 = 商品的数量*商品的价格 - 已经退的数量 * （单价 - 商品的总优惠 / 商品的总数）
     * case 2 :
     * 如果： 退货的数量 < 商品的数量 - 已经退的数量
     * 则：
     * 退货的价格 =  （单价 - 商品的总优惠 / 商品的总数量）* 商品的数量
     * case 3 :
     * 如果： 退货的数量 > 商品的数量 - 已经退的数量
     * 则：
     * throw Exception
     *
     * @param retailOrderItemDO
     * @param count
     * @return
     */
    public BigDecimal getReturnMoney(RetailOrderItemDO retailOrderItemDO, BigDecimal count) {
        int compareResult = count.compareTo(retailOrderItemDO.getCurrentCount().subtract(retailOrderItemDO
                .getReturnCount()));
        if (compareResult == 0) {
            return retailOrderItemDO.getCurrentCount().multiply(retailOrderItemDO.getPrice())
                    .subtract(retailOrderItemDO.getReturnCount()
                            .multiply(retailOrderItemDO.getPrice().subtract(retailOrderItemDO.getTotalDiscountFee().divide(retailOrderItemDO
                                    .getCurrentCount(), 2, BigDecimal.ROUND_HALF_DOWN))));
        } else if (compareResult < 0) {
            return retailOrderItemDO.getPrice().subtract(retailOrderItemDO.getTotalDiscountFee().divide(retailOrderItemDO.getCurrentCount(), 2, BigDecimal
                    .ROUND_HALF_DOWN))
                    .multiply(count);
        } else {
            throw new UnsupportedOperationException("数量错误");
        }
    }


}
