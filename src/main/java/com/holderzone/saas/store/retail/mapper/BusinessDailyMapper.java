package com.holderzone.saas.store.retail.mapper;

import com.holderzone.saas.store.dto.business.manage.HandoverPayDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessDailyMapper
 * @date 2019/02/12 17:37
 * @description 营业日报
 * @program holder-saas-store-trade
 */
public interface BusinessDailyMapper {
    List<ItemRespDTO> freeReturn(@Param("dto") DailyReqDTO request, @Param("type") int type);

    List<ItemRespDTO> goods(@Param("dto") DailyReqDTO request);

    List<AttrItemRespDTO> attr(@Param("dto") DailyReqDTO request);

    List<ItemRespDTO> classify(@Param("dto") DailyReqDTO request);

    List<DiningTypeRespDTO> diningType(@Param("dto") DailyReqDTO request);

    MemberConsumeRespDTO memberConsume(@Param("dto") DailyReqDTO request);

    List<GatherRespDTO> gather(@Param("dto") DailyReqDTO request);

    OverviewRespDTO overview(@Param("dto") DailyReqDTO request);

    /**
     * 收款方式分组统计
     *
     * @param request
     * @return
     */
    List<AmountItemDTO> paymentTypeCount(@Param("dto") DailyReqDTO request);

    /**
     * 优惠方式分组统计
     *
     * @param request
     * @return
     */
    List<AmountItemDTO> discountTypeCount(@Param("dto") DailyReqDTO request);

    /**
     * 销售收入统计
     *
     * @param request
     * @return
     */
    BigDecimal[] consumerAmount(@Param("dto") DailyReqDTO request);

    HandoverPayDTO handover(@Param("dto") HandoverPayQueryDTO request);
}
