package com.holderzone.saas.store.retail.config;

import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.extension.injector.LogicSqlInjector;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;


/**
 * <AUTHOR>
 * @version 1.0
 * @className MybatisPlusConfig
 * @date 2019/01/21 10:42
 * @description
 * @program holder-saas-store-trade
 */
@Configuration
@MapperScan("com.holderzone.saas.store.retail.mapper")
public class MybatisPlusConfig {

    /**
     * 分页插件
     * 与sdk中的PageInterceptor冲突了，一旦配置这个，RoutingStatementHandler会创建代理类，PageInterceptor反射获取delegate就会获取不到
     */
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        return new PaginationInterceptor();
    }

    @Bean
    @Profile({"dev", "test"})// 设置 dev test 环境开启
    public ISqlInjector sqlInjector() {
        return new LogicSqlInjector();
    }

//    @Bean
//    @Profile({"dev", "test"})// 设置 dev test 环境开启
//    public PerformanceInterceptor performanceInterceptor() {
//        return new PerformanceInterceptor();
//    }
}