package com.holderzone.saas.store.retail.service.feign;


import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.cmember.app.dto.account.response.MemberAndCardInfoRespDTO;
import com.holderzone.holder.saas.cmember.app.dto.common.DishInfoDTO;
import com.holderzone.holder.saas.cmember.app.dto.order.common.VolumeCalculateAgainDTO;
import com.holderzone.holder.saas.cmember.app.dto.order.request.*;
import com.holderzone.holder.saas.cmember.app.dto.order.response.*;
import com.holderzone.holder.saas.member.dto.account.request.cmember.QueryStoreAndMemberAndCardReqDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberClientService
 * @date 2018/08/06 11:22
 * @description
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-cmember-order-service", fallbackFactory = MemberOrderClientService
        .MemberClientFallback.class)
public interface MemberOrderClientService {

    @GetMapping("/hsmca/member/getMemberInfoAndCard")
    MemberAndCardInfoRespDTO getMemberInfoAndCard(@RequestBody QueryStoreAndMemberAndCardReqDTO
                                                          queryStoreAndMemberAndCardReqDTO);

    @PostMapping("/hsmca/order/pay")
    String pay(@RequestBody ConfirmPayReqDTO confirmPayReqDTO);

    @PostMapping("/hsmca/volume/{volumeCode}/consume")
    VolumeConsumeRespDTO consume(@PathVariable("volumeCode") String volumeCode, @RequestBody VolumeConsumeReqDTO
            volumeConsumeReqDTO);

    @PostMapping("/hsmca/volume/{volumeCode}/calculate")
    VolumeCalculateRespDTO calculate(@PathVariable("volumeCode") String volumeCode, @RequestBody VolumeConsumeReqDTO
            volumeConsumeReqDTO);

    @PostMapping("/hsmca/volume/{volumeCode}/cancel")
    List<DishInfoDTO> cancel(@PathVariable("volumeCode") String volumeCode, @RequestBody VolumeCancelReqDTO
            volumeCancelReqDTO);

    @PostMapping("/hsmca/volume/calculateAgain")
    VolumeCalculateAgainRespDTO calculateAgain(@RequestBody VolumeCalculateAgainDTO volumeCalculateAgainDTO);

    @GetMapping("/hsmca/order/integral/compute")
    IntegralOffsetResultRespDTO compute(@RequestParam("cardGuid") String cardGuid, @RequestParam("orderMoney")
            BigDecimal orderMoney);

    @PostMapping("/hsmca/order/{memberInfoCardGuid}/discount")
    DiscountRespDTO discount(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid, @RequestBody
            DiscountReqDTO discountReqDTO);

    @PostMapping("/hsmca/order/{memberConsumptionGuid}/cancel")
    String cancelPay(@PathVariable("memberConsumptionGuid") String memberConsumptionGuid, @RequestBody
            CancelPayReqDTO cancelPayReqDTO);

    @PostMapping("/hsmca/volume/cancelAll")
    boolean cancelAll(@RequestBody VolumeCancelAllReqDTO volumeCancelAllReqDTO);

    @GetMapping("/hsmca/order/{memberConsumptionGuid}/delDiscount")
    boolean delDiscount(@RequestParam("memberConsumptionGuid") String memberConsumptionGuid);

    @Component
    class MemberClientFallback implements FallbackFactory<MemberOrderClientService> {

        private static final Logger logger = LoggerFactory.getLogger(MemberClientFallback.class);

        @Override
        public MemberOrderClientService create(Throwable throwable) {
            return new MemberOrderClientService() {
                @Override
                public MemberAndCardInfoRespDTO getMemberInfoAndCard(QueryStoreAndMemberAndCardReqDTO
                                                                             queryStoreAndMemberAndCardReqDTO) {
                    logger.error("获取会员信息调用异常e={}，入参：{}", throwable.getMessage(), JacksonUtils.writeValueAsString
                            (queryStoreAndMemberAndCardReqDTO));
                    throw new ParameterException("获取会员信息调用异常");
                }

                @Override
                public String pay(ConfirmPayReqDTO confirmPayReqDTO) {
                    logger.error("会员支付调用异常e={}，入参：{}", throwable.getMessage(), JacksonUtils.writeValueAsString
                            (confirmPayReqDTO));
                    throw new ParameterException("会员支付调用异常");
                }

                @Override
                public VolumeConsumeRespDTO consume(String volumeCode, VolumeConsumeReqDTO volumeConsumeReqDTO) {
                    logger.error("会员验券调用异常e={}，入参：{}", throwable.getMessage(), JacksonUtils.writeValueAsString
                            (volumeConsumeReqDTO));
                    throw new ParameterException("会员验券调用异常");
                }

                @Override
                public VolumeCalculateRespDTO calculate(String volumeCode, VolumeConsumeReqDTO volumeConsumeReqDTO) {
                    logger.error("会员券查询调用异常e={}，入参：{}", throwable.getMessage(), JacksonUtils.writeValueAsString
                            (volumeConsumeReqDTO));
                    throw new ParameterException("会员券查询调用异常");
                }

                @Override
                public List<DishInfoDTO> cancel(String volumeCode, VolumeCancelReqDTO volumeCancelReqDTO) {
                    logger.error("会员撤销验券调用异常e={}，入参：{}", throwable.getMessage(), JacksonUtils.writeValueAsString
                            (volumeCancelReqDTO));
                    throw new ParameterException("会员撤销验券调用异常");
                }

                @Override
                public VolumeCalculateAgainRespDTO calculateAgain(VolumeCalculateAgainDTO volumeCalculateAgainDTO) {
                    logger.error("会员重新验券调用异常e={}，入参：{}", throwable.getMessage(), JacksonUtils.writeValueAsString
                            (volumeCalculateAgainDTO));
                    throw new ParameterException("会员重新验券调用异常");
                }

                @Override
                public IntegralOffsetResultRespDTO compute(String cardGuid, BigDecimal orderMoney) {
                    logger.error("会员计算积分调用异常e={}，入参：{}", throwable.getMessage(), JacksonUtils.writeValueAsString
                            ("cardGuid:" + cardGuid + "orderMoney:" + orderMoney));
                    throw new ParameterException("会员计算积分调用异常");
                }

                @Override
                public DiscountRespDTO discount(String memberInfoCardGuid, DiscountReqDTO discountReqDTO) {
                    logger.error("会员折扣调用异常e={}，入参：{}", throwable.getMessage(), JacksonUtils.writeValueAsString
                            (discountReqDTO) + "memberInfoCardGuid:" + memberInfoCardGuid);
                    throw new ParameterException("会员折扣调用异常");
                }

                @Override
                public String cancelPay(String memberConsumptionGuid, CancelPayReqDTO cancelPayReqDTO) {
                    logger.error("会员撤销支付调用异常e={}，入参：{}", throwable.getMessage(), JacksonUtils.writeValueAsString
                            (memberConsumptionGuid));
                    throw new ParameterException("会员撤销支付调用异常");
                }

                @Override
                public boolean cancelAll(VolumeCancelAllReqDTO volumeCancelAllReqDTO) {
                    logger.error("会员撤销所有优惠调用异常e={}，入参：{}", throwable.getMessage(), JacksonUtils.writeValueAsString
                            (volumeCancelAllReqDTO));
                    throw new ParameterException("会员撤销所有优惠调用异常");
                }

                @Override
                public boolean delDiscount(@RequestParam("memberConsumptionGuid") String memberConsumptionGuid) {
                    logger.error("会员登出删除折异常e={}，入参：{}", throwable.getMessage(), JacksonUtils.writeValueAsString
                            (memberConsumptionGuid));
                    throw new ParameterException("会员登出删除折异常e");
                }
            };
        }
    }

}
