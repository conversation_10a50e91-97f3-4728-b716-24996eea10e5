package com.holderzone.saas.store.retail.service.feign;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillClientService
 * @date 2018/09/06 19:33
 * @description
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = BusinessClientService.FallBack.class)
public interface BusinessClientService {

    @PostMapping("/system/getAll/{storeGuid}")
    List<SystemDiscountDTO> getSystemDiscount(@PathVariable("storeGuid") String storeGuid);

    @PostMapping("surcharge/list_by_area_guid")
    Map<String, List<SurchargeLinkDTO>> listByAreaGuid(@RequestBody List<String> areaList);

    @Component
    class FallBack implements FallbackFactory<BusinessClientService> {
        private static final Logger logger = LoggerFactory.getLogger(FallBack.class);

        @Override
        public BusinessClientService create(Throwable throwable) {
            return new BusinessClientService() {


                @Override
                public List<SystemDiscountDTO> getSystemDiscount(String storeGuid) {
                    logger.error("e={}", throwable.getMessage());
                    throw new ParameterException("异常");
                }

                @Override
                public Map<String, List<SurchargeLinkDTO>> listByAreaGuid(List<String> areaList) {
                    logger.error("获取附加费规则异常，e={}", throwable.getMessage());
                    throw new ParameterException("获取附加费规则异常");
                }

            };
        }
    }


}
