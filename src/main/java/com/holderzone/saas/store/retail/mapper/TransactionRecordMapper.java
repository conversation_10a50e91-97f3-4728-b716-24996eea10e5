package com.holderzone.saas.store.retail.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.trade.HandoverDTO;
import com.holderzone.saas.store.retail.entity.domain.TransactionRecordDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单交易记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface TransactionRecordMapper extends BaseMapper<TransactionRecordDO> {

    List<HandoverDTO> handover(@Param("dto") HandoverPayQueryDTO request);

    @Deprecated
    Integer handoverOrderCount(@Param("dto") HandoverPayQueryDTO request);
}
