package com.holderzone.saas.store.retail.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderTypeEnum
 * @date 2019/10/26 18:17
 * @description //TODO
 * @program IdeaProjects
 */
public enum OrderTypeEnum {

    SIMPLE_ORDER(1, "普通单"),
    RETURN_ORDER(2, "退款单");

    private int code;

    private String desc;

    OrderTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
