package com.holderzone.saas.store.retail.service.impl.mp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.retail.entity.domain.DiscountDO;
import com.holderzone.saas.store.retail.entity.enums.DiscountTypeEnum;
import com.holderzone.saas.store.retail.mapper.DiscountMapper;
import com.holderzone.saas.store.retail.service.mp.DiscountService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 订单优惠记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Service
public class DiscountServiceImpl extends ServiceImpl<DiscountMapper, DiscountDO> implements DiscountService {

    @Override
    public List<DiscountDO> listByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<DiscountDO>().eq(DiscountDO::getOrderGuid, orderGuid));

    }

    @Override
    public void removeByOrderGuids(List<Long> subOrderGuids) {
        remove(new LambdaQueryWrapper<DiscountDO>().in(DiscountDO::getOrderGuid, subOrderGuids));
    }

    @Override
    public DiscountDO getGrouponDiscount(String orderGuid) {
        return getOne(new LambdaQueryWrapper<DiscountDO>().eq(DiscountDO::getOrderGuid, orderGuid).eq
                (DiscountDO::getDiscountType, DiscountTypeEnum.GROUPON.getCode()));
    }
}
