package com.holderzone.saas.store.retail.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_settlement_rules")
public class HstSettlementRulesDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 顺序编号
     */
    private Integer sequenceNumber;

    /**
     * 优惠方式名称
     */
    private String offerName;

    /**
     * 折扣描述
     */
    private String offerDesc;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;


}
