package com.holderzone.saas.store.retail.service.impl.mp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.retail.entity.domain.RetailOrderItemDO;
import com.holderzone.saas.store.retail.helper.RedisHelper;
import com.holderzone.saas.store.retail.mapper.RetailOrderItemMapper;
import com.holderzone.saas.store.retail.service.mp.RetailOrderItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 订单商品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Service
public class RetailOrderItemServiceImpl extends ServiceImpl<RetailOrderItemMapper, RetailOrderItemDO>
        implements RetailOrderItemService {

    @Override
    public List<RetailOrderItemDO> listByOrderGuid(Long orderGuid) {
        return list(new LambdaQueryWrapper<RetailOrderItemDO>().eq(RetailOrderItemDO::getOrderGuid, orderGuid)
                .orderByDesc
                        (RetailOrderItemDO::getGmtCreate));
    }

    @Override
    public List<RetailOrderItemDO> listByIdsWithLock(Collection<? extends Serializable> idList) {
        return list(new LambdaQueryWrapper<RetailOrderItemDO>().in(RetailOrderItemDO::getGuid, idList).last("for " +
                "update"));
    }

    @Override
    public List<RetailOrderItemDO> listByOrderLists(List<String> orderLists) {
        return list(new LambdaQueryWrapper<RetailOrderItemDO>().in(RetailOrderItemDO::getOrderGuid, orderLists));
    }
}
