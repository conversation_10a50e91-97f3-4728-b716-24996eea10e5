package com.holderzone.saas.store.retail.event;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AbstractPrintUtils
 * @date 2019/09/23 17:01
 * @description //TODO
 * @program IdeaProjects
 */

public abstract class AbstractPrintHandler extends PrintUtils implements <PERSON><PERSON><PERSON><PERSON>, IPrintHandler {

    @Override
    public boolean handle(BaseOrderEvent baseOrderEvent) {
        return doPre(baseOrderEvent) && execute(baseOrderEvent) || doAfter(baseOrderEvent);
    }

    @Override
    public abstract boolean execute(BaseOrderEvent event);

    /**
     * 做锁？  做事务？  子类继承？
     *
     * @param event
     * @return
     */
    @Override
    public boolean doPre(BaseOrderEvent event) {
        //返回true才会执行execute
        return true;
    }

    @Override
    public boolean doAfter(BaseOrderEvent event) {
        //必须返回false  不然结果影响handle结果
        return false;
    }


}