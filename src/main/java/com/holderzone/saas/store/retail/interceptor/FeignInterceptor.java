package com.holderzone.saas.store.retail.interceptor;


import com.holderzone.saas.store.retail.context.RequestContext;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FeignInterceptor
 * @date 2018/09/06 16:45
 * @description
 * @program holder-saas-store-trade
 */
@Slf4j
@Configuration
public class FeignInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        try {
            if (StringUtils.hasText(RequestContext.getJsonStr())) {
                template.header(USER_INFO, URLEncoder.encode(RequestContext.getJsonStr(), "utf-8"));
            }
        } catch (UnsupportedEncodingException e) {
            log.error("添加userInfo头出错:{}", e);
        }
    }

}
