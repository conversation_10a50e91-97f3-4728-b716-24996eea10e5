package com.holderzone.saas.store.retail.service.feign;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.organization.StoreBizDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreClientService
 * @date 2018/09/30 11:41
 * @description
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = StoreClientService.FallBack.class)
public interface StoreClientService {

    @PostMapping("store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    @GetMapping("device/get_master_device_by_storeguid/{storeGuid}")
    StoreDeviceDTO getMasterDeviceByStoreGuid(@PathVariable("storeGuid") String storeGuid);

    @PostMapping("store/query_store_biz_by_guid")
    StoreBizDTO queryStoreBizByGuid(@RequestParam("storeGuid") String storeGuid);

    @Component
    class FallBack implements FallbackFactory<StoreClientService> {
        private static final Logger logger = LoggerFactory.getLogger(StoreClientService.FallBack.class);

        @Override
        public StoreClientService create(Throwable throwable) {
            return new StoreClientService() {

                @Override
                public StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid) {
                    logger.error("获取营业日异常e={}", throwable.getMessage());
                    throw new ParameterException("获取营业日失败!");
                }

                @Override
                public StoreDeviceDTO getMasterDeviceByStoreGuid(String storeGuid) {
                    logger.error("获取主机异常e={}", throwable.getMessage());
                    throw new ParameterException("获取主机失败!");
                }


                @Override
                public StoreBizDTO queryStoreBizByGuid(String storeGuid) {
                    logger.error("获取营业日异常e={}", throwable.getMessage());
                    throw new ParameterException("获取营业日失败!");
                }
            };
        }
    }
}
