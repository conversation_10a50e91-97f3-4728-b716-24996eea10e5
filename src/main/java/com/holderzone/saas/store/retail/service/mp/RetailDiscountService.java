package com.holderzone.saas.store.retail.service.mp;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.retail.entity.domain.RetailDiscountDO;

import java.util.List;

/**
 * <p>
 * 订单优惠记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface RetailDiscountService extends IService<RetailDiscountDO> {

    List<RetailDiscountDO> listByOrderGuid(String orderGuid);

    void removeByOrderGuids(List<Long> subOrderGuids);

    RetailDiscountDO getGrouponDiscount(String orderGuid);
}
