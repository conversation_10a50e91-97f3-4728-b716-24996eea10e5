package com.holderzone.saas.store.retail.service.mp;

import com.holderzone.saas.store.retail.entity.domain.DiscountDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 订单优惠记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
public interface DiscountService extends IService<DiscountDO> {

    List<DiscountDO> listByOrderGuid(String orderGuid);

    void removeByOrderGuids(List<Long> subOrderGuids);

    DiscountDO getGrouponDiscount(String orderGuid);
}
