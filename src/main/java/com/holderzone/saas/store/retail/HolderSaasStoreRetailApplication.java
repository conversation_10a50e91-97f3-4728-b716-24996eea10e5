package com.holderzone.saas.store.retail;

import com.holderzone.saas.store.retail.utils.SpringContextUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@SpringBootApplication
@EnableFeignClients
@EnableSwagger2
//@EnableApolloConfig
public class HolderSaasStoreRetailApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext app = SpringApplication.run(HolderSaasStoreRetailApplication.class, args);

        /**
         * 设置Spring容器上下文
         */
        SpringContextUtil.getInstance().setCfgContext(app);

    }

}
