package com.holderzone.saas.store.retail.entity.bo;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayAttachDataBO
 * @date 2019/03/21 10:43
 * @description
 * @program holder-saas-store-trade
 */
@Data
public class AggPayAttachDataBO extends BaseDTO {

    @ApiModelProperty(value = "订单guid")
    private String orderGuid;

    @ApiModelProperty(value = "本次支付唯一标示", required = true)
    private String payGuid;

    @ApiModelProperty(value = "支付金额", required = true)
    private BigDecimal amount;

    @ApiModelProperty(value = "是否最后一次支付", required = true)
    private Boolean last;


}
