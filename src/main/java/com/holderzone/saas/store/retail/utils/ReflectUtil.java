package com.holderzone.saas.store.retail.utils;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReflectUtil
 * @date 2019/02/01 14:37
 * @description 反射相关工具类
 * @program holder-saas-store-trade
 */
class ReflectUtil {

    static Object getValue(Object o, String fieldName) throws NoSuchFieldException, IllegalAccessException {
        Field declaredField = o.getClass().getDeclaredField(fieldName);
        declaredField.setAccessible(true);
        return declaredField.get(o);
    }

}
