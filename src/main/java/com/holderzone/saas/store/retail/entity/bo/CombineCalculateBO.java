package com.holderzone.saas.store.retail.entity.bo;

import com.holderzone.saas.store.retail.entity.domain.DiscountDO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CombineCalculateBO
 * @date 2019/04/13 17:54
 * @description
 * @program holder-saas-store-trade
 */
@Data
public class CombineCalculateBO {

    List<DiscountDO> saveDiscountDOS;

    List<DiscountDO> updateDiscountDOS;
}
