package com.holderzone.saas.store.retail.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.holderzone.saas.store.retail.entity.enums.StateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_retail_order")
public class RetailOrderDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 订单号(前端显示用，门店内唯一，格式************)
     */
    private String orderNo;

    /**
     * 设备类型(订单来源BaseDeviceTypeEnum)
     */
    private Integer deviceType;

    /**
     * 营业日
     */
    private LocalDate businessDay;

    /**
     * 整单备注
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String remark;

    /**
     * 订单金额（商品总额+附加费）
     */
    private BigDecimal orderFee;

    /**
     * 找零（收款-应收金额）
     */
    private BigDecimal changeFee;

    /**
     * 实收金额=订单金额-优惠金额（所有优惠方式金额之和（赠菜优惠+会员优惠+折扣优惠+系统省零+让价优惠））
     * 应收金额=订单金额-优惠金额-订金(押金)
     */
    private BigDecimal actuallyPayFee;

    /**
     * 1：待支付 2：支付中 3：支付失败 4：支付成功 5：退款  6：已作废
     */
    private Integer state;

    public String getStateName() {
        if (state != null) {
            if (StateEnum.READY.getCode() <= state && state <= StateEnum.FAILURE.getCode()) {
                return "未结账";
            }
            if (StateEnum.SUCCESS.getCode() == state) {
                return "已结账";
            }
            if (StateEnum.REFUNDED.getCode() == state) {
                return "已退款";
            }
            if (StateEnum.CANCEL.getCode() == state) {
                return "已作废";
            }
        }
        return null;
    }

    /**
     * {@link com.holderzone.saas.store.retail.entity.enums.OrderTypeEnum}
     * 订单类型1：普通单 2：退款单
     */
    private Integer orderType;

    /**
     * 会员guid
     */
    private String memberGuid;

    /**
     * 会员支付id，反结账传给会员系统
     */
    private String memberConsumptionGuid;

    /**
     * 会员卡guid
     */
    private String memberCardGuid;

    /**
     * 会员卡编号
     */
    private String memberCardNum;

    /**
     * 会员电话
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String memberPhone;

    /**
     * 会员名字
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String memberName;

    /**
     * 结账设备类型（BaseDeviceTypeEnum）
     */
    private Integer checkoutDeviceType;

    /**
     * 计算用会员价 1为用了
     */
    private Integer caculatByMemberPrice;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 结算时间
     */
    private LocalDateTime checkoutTime;

    /**
     * 结账操作人guid
     */
    private String checkoutStaffGuid;


    /**
     * 操作人账号
     */
    private String checkoutStaffAccount;
    /**
     * 结账操作人name
     */
    private String checkoutStaffName;

    /**
     * 退单原单的guid
     */
    private Long originalOrderGuid;

    /**
     * 退货原因
     */
    private String reason;

    public Boolean isUnfinished() {
        if (this.state == null) {
            return Boolean.FALSE;
        }
        return !this.state.equals(StateEnum.READY.getCode()) && !this.state.equals(StateEnum.PENDING
                .getCode()) && !this.state.equals(StateEnum.FAILURE.getCode());
    }
}

