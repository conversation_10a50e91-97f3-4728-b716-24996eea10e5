package com.holderzone.saas.store.retail.service.impl.mp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.retail.entity.domain.RetailFreeReturnItemDO;
import com.holderzone.saas.store.retail.entity.enums.FreeReturnTypeEnum;
import com.holderzone.saas.store.retail.mapper.RetailFreeReturnMapper;
import com.holderzone.saas.store.retail.service.mp.RetailFreeReturnService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 赠送/退货记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Service
public class RetailFreeReturnServiceImpl extends ServiceImpl<RetailFreeReturnMapper, RetailFreeReturnItemDO> implements
        RetailFreeReturnService {

    @Override
    public List<RetailFreeReturnItemDO> listFreeByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<RetailFreeReturnItemDO>().eq(RetailFreeReturnItemDO::getOrderGuid, orderGuid).eq
                (RetailFreeReturnItemDO::getType, FreeReturnTypeEnum.FREE.getCode()));
    }

    @Override
    public List<RetailFreeReturnItemDO> listByOrderGuid(String orderGuid) {
        return list(new LambdaQueryWrapper<RetailFreeReturnItemDO>().eq(RetailFreeReturnItemDO::getOrderGuid, orderGuid));
    }

    @Override
    public void deleteByOrderGuid(String orderGuid) {
        remove(new LambdaQueryWrapper<RetailFreeReturnItemDO>().eq(RetailFreeReturnItemDO::getOrderGuid, orderGuid).ne
                (RetailFreeReturnItemDO::getType, 1));
    }

    @Override
    public void removeByItemGuids(ArrayList<Long> orderItemGuids) {
        remove(new LambdaQueryWrapper<RetailFreeReturnItemDO>().in(RetailFreeReturnItemDO::getOrderItemGuid, orderItemGuids).ne
                (RetailFreeReturnItemDO::getType, 1));
    }
}
