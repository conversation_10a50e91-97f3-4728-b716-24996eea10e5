package com.holderzone.saas.store.retail.execption;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.NotFoundException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.FeignMsgUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.trade.exception.OrderLockException;
import com.netflix.hystrix.exception.HystrixBadRequestException;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import feign.FeignException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GlobalExceptionHandler
 * @date 2019/01/04 8:53
 * @description 全局异常处理
 * @program holder-saas-store-trade
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);


    @ExceptionHandler(value = ParameterException.class)
    public ResponseEntity<String> exception(ParameterException e) {
        if (logger.isErrorEnabled()) {
            logger.error("Retail参数错误：{}", e.getMessage());
        }
        return new ResponseEntity<>(e.getMessage(), HttpStatus.CONFLICT);
    }


    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public ResponseEntity<String> requestValueError(HttpServletRequest request, MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(fieldError -> String.format("%s:%s", fieldError.getField(), fieldError.getDefaultMessage()))
                .collect(Collectors.joining(","));
        return new ResponseEntity<>(message, HttpStatus.PRECONDITION_REQUIRED);
    }

    @ExceptionHandler(value = IllegalArgumentException.class)
    public ResponseEntity<String> illegalArgumentException(HttpServletRequest request, IllegalArgumentException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("Retail服务参数错误：{}", message);
        }
        if (StringUtils.isEmpty(message)) {
            return new ResponseEntity<>("Retail服务参数错误", HttpStatus.PRECONDITION_REQUIRED);
        }
        return new ResponseEntity<>(message, HttpStatus.PRECONDITION_REQUIRED);
    }

    @ExceptionHandler(value = NotFoundException.class)
    public ResponseEntity<String> exception(NotFoundException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("Retail服务未找到：{}", message);
        }
        return new ResponseEntity<>(message, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(value = OrderLockException.class)
    public ResponseEntity<String> exception(OrderLockException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("支付订单锁异常：{}", message);
        }
        return new ResponseEntity<>(message, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(value = BusinessException.class)
    public ResponseEntity<String> exception(BusinessException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("Retail服务业务异常：{}", message);
        }
        return new ResponseEntity<>(message, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(value = HystrixBadRequestException.class)
    public ResponseEntity<String> hystrixBadRequestException(HttpServletRequest request, HystrixBadRequestException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("Retail服务方业务异常：{}", message);
        }
        if (StringUtils.isEmpty(message)) {
            return new ResponseEntity<>("Retail服务方业务异常", HttpStatus.CONFLICT);
        }
        return new ResponseEntity<>(message, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(value = HystrixRuntimeException.class)
    public ResponseEntity<String> hystrixRuntimeException(HttpServletRequest request, HystrixRuntimeException e) {
        String message = e.getCause().getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("Retail服务方系统异常：{}", message);
        }
        message = FeignMsgUtils.parseFeignMsgDetailed(message);
        if (StringUtils.isEmpty(message)) {
            return new ResponseEntity<>("Retail服务方系统异常", HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(value = FeignException.class)
    public ResponseEntity<String> feignException(HttpServletRequest request, FeignException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("Retail服务方系统异常：{}", message);
        }
        message = FeignMsgUtils.parseFeignMsgDetailed(message);
        if (StringUtils.isEmpty(message)) {
            return new ResponseEntity<>("Retail服务方系统异常", HttpStatus.INTERNAL_SERVER_ERROR);
        }
        return new ResponseEntity<>(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(value = ServerException.class)
    public ResponseEntity<String> exception(ServerException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("Retail本地系统异常：{}", message);
        }
        return new ResponseEntity<>(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }


    @ExceptionHandler(value = Exception.class)
    public ResponseEntity<String> exception(Exception e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("Retail本地系统异常：{}", e);
        }
        return new ResponseEntity<>(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }

}
