package com.holderzone.saas.store.retail.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.member.request.MemberConsumeReqDTO;
import com.holderzone.saas.store.dto.member.response.MemberConsumeRespDTO;
import com.holderzone.saas.store.dto.retail.pay.SaasNotifyDTO;
import com.holderzone.saas.store.dto.retail.bill.request.RetailAggPayReqDTO;
import com.holderzone.saas.store.dto.retail.bill.request.RetailPayReqDTO;
import com.holderzone.saas.store.dto.retail.bill.response.RetailAggPayRespDTO;
import com.holderzone.saas.store.retail.entity.domain.OrderDO;
import com.holderzone.saas.store.retail.entity.domain.RetailOrderDO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BillService
 * @date 2018/09/04 16:08
 * @description //
 * @program holder-saas-store-trade
 */
public interface BillService {

    Boolean pay(RetailPayReqDTO billPayReqDTO);

    String memberPayRetail(RetailOrderDO orderDO, RetailPayReqDTO.Payment payment, RetailPayReqDTO billPayReqDTO);

    RetailAggPayRespDTO aggPay(RetailAggPayReqDTO retailAggPayReqDTO);

    String aggCallBack(SaasNotifyDTO saasNotifyDTO);

    Page<MemberConsumeRespDTO> memberConsumeRecords(MemberConsumeReqDTO memberConsumeReqDTO);


}
