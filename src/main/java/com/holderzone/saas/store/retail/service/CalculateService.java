package com.holderzone.saas.store.retail.service;

import com.holderzone.saas.store.dto.retail.bill.request.RetailCalculateReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CalculateService
 * @date 2018/09/04 16:08
 * @description //
 * @program holder-saas-store-trade
 */
public interface CalculateService {

    /**
     * 结账时计算账单金额和获取订单详情
     *
     * @param retailCalculateReqDTO
     * @return
     */
    RetailOrderDetailRespDTO calculate(RetailCalculateReqDTO retailCalculateReqDTO);

}
