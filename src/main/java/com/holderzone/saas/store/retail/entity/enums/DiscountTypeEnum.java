package com.holderzone.saas.store.retail.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DiscountTypeEnum
 * @date 2018/09/04 17:52
 * @description
 * @program holder-saas-store-trade
 */
public enum DiscountTypeEnum {

    MEMBER(1, "会员折扣",2),
    WHOLE(2, "整单折扣",3),
    CONCESSIONAL(3, "整单让价",5),
    SYSTEM(4, "系统省零",4),
    FREE(5, "赠送优惠",0),
    GROUPON(6, "团购验券",1),
    MEMBER_GROUPON(7, "会员代金券",7),
    POINTS_DEDUCTION(8, "积分抵扣",6),
    SINGLE_MEMBER(9, "会员价",8),
    SINGLE_DISCOUNT(10, "单品折扣",9),
    GOODS_GROUPON(11, "会员商品券",10),
    OTHER(-1, "其他优惠",-1),;

    private int code;
    private String desc;
    private int memberCode;

    DiscountTypeEnum(int code, String desc,int memberCode) {
        this.code = code;
        this.desc = desc;
        this.memberCode = memberCode;
    }

    public static String getDesc(int code) {
        for (DiscountTypeEnum c : DiscountTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public static DiscountTypeEnum get(int code) {
        for (DiscountTypeEnum c : DiscountTypeEnum.values()) {
            if (c.getCode() == code) {
                return c;
            }
        }
        return DiscountTypeEnum.OTHER;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getMemberCode() {
        return memberCode;
    }

    public void setMemberCode(int memberCode) {
        this.memberCode = memberCode;
    }
}
