package com.holderzone.saas.store.retail.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 赠送/退货记录
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hst_retail_free_return_item")
public class RetailFreeReturnItemDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId
    private Long guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否删除 0：false,1:true
     */
    @TableLogic
    private Boolean isDelete;

    /**
     * 订单商品guid
     */
    private Long orderItemGuid;

    /**
     * 商品guid
     */
    private String itemGuid;

    /**
     * 商品类型(1.套餐主项，2.规格，3.称重，4.单品 )
     */
    private Integer itemType;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品的规格
     */
    private String skuGuid;

    /**
     * 规格名称
     */
    private String skuName;

    /**
     * sku价格
     */
    private BigDecimal price;

    /**
     * 菜品优惠合计
     */
    private BigDecimal totalDiscountFee;

    /**
     * 订单guid（反结账取新生成的）
     */
    private Long orderGuid;

    /**
     * 类型（1：退货，2：赠送）
     */
    private Integer type;

    /**
     * 催品次数
     */
    private Integer urgeNum;

    /**
     * 商品状态(1.即起，2.挂起，3.叫起，4.待制作，5.制作中，6.待出堂，7.已出堂，8.已上菜(已划菜) ，9.预定)
     */
    private Integer itemState;

    /**
     * 标记退货是否为赠送（0：否，1：是）
     */
    private Integer isFree;

    /**
     * 原因
     */
    private String reason;

    /**
     * 数量
     */
    private BigDecimal count;

    /**
     * 赠送划菜数量
     */
    private BigDecimal serveCount;

    /**
     * 操作人guid
     */
    private String staffGuid;

    /**
     * 操作人名字
     */
    private String staffName;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;


}
