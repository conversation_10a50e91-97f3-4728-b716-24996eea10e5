package com.holderzone.saas.store.retail.entity.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RecoveryTypeEnum
 * @date 2018/10/23 11:54
 * @description
 * @program holder-saas-store-trade
 */
public enum RecoveryTypeEnum {
    GENERAL(1, "普通单"),
    RETURN(4, "退单");

    private int code;
    private String desc;

    RecoveryTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (RecoveryTypeEnum c : RecoveryTypeEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
