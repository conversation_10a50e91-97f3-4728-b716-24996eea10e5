package com.holderzone.saas.store.retail.utils;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.order.common.FreeItemDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.CancelDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnOrderDetailDTO;
import com.holderzone.saas.store.dto.retail.SystemDiscountDTO;
import com.holderzone.saas.store.dto.retail.bill.common.RetailItemDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.ReturnItemDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.order.ItemPriceChangeEnum;
import com.holderzone.saas.store.retail.context.RequestContext;
import com.holderzone.saas.store.retail.entity.bo.DiscountRuleBO;
import com.holderzone.saas.store.retail.entity.domain.*;
import com.holderzone.saas.store.retail.entity.enums.DiscountTypeEnum;
import com.holderzone.saas.store.retail.entity.enums.ItemTypeEnum;
import com.holderzone.saas.store.retail.entity.enums.PaymentTypeEnum;
import com.holderzone.saas.store.retail.transform.OrderTransform;
import com.holderzone.saas.store.retail.transform.RetailTransform;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AmountCalculationUtil
 * @date 2019/02/20 14:23
 * @description //订单金额计算工具类
 * @program holder-saas-store-trade
 */
public class AmountCalculationUtil {

    private static OrderTransform orderTransform = OrderTransform.INSTANCE;

    private static RetailTransform retailTransform = RetailTransform.INSTANCE;

    /**
     * ITEM_DISCOUNT_PERCENT_BASE 字段为商品单品打折的时候的汇率     875代表8.75折   计算价格用 ：
     * 价格     *     折扣
     * ——————————————————————————
     * ITEM_DISCOUNT_PERCENT_BASE
     */
    private static final BigDecimal ITEM_DISCOUNT_PERCENT_BASE = new BigDecimal(1000);

    public static List<RetailItemDTO> buildItem(List<RetailOrderItemDO> orderItemDOS, List
            <RetailFreeReturnItemDO> freeReturnItemDOList) {
        List<RetailItemDTO> retailItemDTOS = new ArrayList<>();
        for (RetailOrderItemDO orderItemDO : orderItemDOS) {
            RetailItemDTO retailItemDTO = retailTransform.retailOrderItemDO2DineInItemDTO(orderItemDO);

            //套餐
            BigDecimal addPriceTotal = BigDecimal.ZERO;

            //计算普通菜品价格小计
            BigDecimal itemPrice = getItemPrice(retailItemDTO, addPriceTotal, retailItemDTO.getCurrentCount(), false);
            retailItemDTO.setItemPrice(itemPrice);
            retailItemDTO.setBeforeItemPrice(itemPrice);
            //有赠送
            if (BigDecimalUtil.greaterThanZero(orderItemDO.getFreeCount())) {
                Map<Object, List<RetailFreeReturnItemDO>> listMap = CollectionUtil.toListMap(freeReturnItemDOList,
                        "orderItemGuid");
                List<RetailFreeReturnItemDO> freeReturnItemDOS = listMap.get(orderItemDO.getGuid());
                List<FreeItemDTO> freeItemDTOS = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(freeReturnItemDOS)) {
                    for (RetailFreeReturnItemDO freeReturnItemDO : freeReturnItemDOS) {
                        FreeItemDTO freeItemDTO = retailTransform.freeReturnItemDO2freeItemDTO(freeReturnItemDO);
                        //计算赠送菜品价格小计
                        BigDecimal freeItemPrice = getItemPrice(retailItemDTO, addPriceTotal, freeReturnItemDO
                                .getCount(), true);
                        freeItemDTO.setItemPrice(freeItemPrice);
                        freeItemDTOS.add(freeItemDTO);
                    }
                }
                retailItemDTO.setFreeItemDTOS(freeItemDTOS);
            }
            retailItemDTOS.add(retailItemDTO);
        }
        return retailItemDTOS;
    }

    public static List<RetailItemDTO> buildItemRetail(List<RetailOrderItemDO> orderItemDOS, List
            <RetailFreeReturnItemDO> freeReturnItemDOList) {
        List<RetailItemDTO> retailItemDTOS = new ArrayList<>();
        for (RetailOrderItemDO orderItemDO : orderItemDOS) {
            RetailItemDTO retailItemDTO = retailTransform.retailOrderItemDO2DineInItemDTO(orderItemDO);

            //套餐
            BigDecimal addPriceTotal = BigDecimal.ZERO;

            //计算普通菜品价格小计
            BigDecimal itemPrice = getItemPrice(retailItemDTO, addPriceTotal, retailItemDTO.getCurrentCount(), false);
            retailItemDTO.setItemPrice(itemPrice);
            retailItemDTO.setBeforeItemPrice(itemPrice);
            //有赠送
            if (BigDecimalUtil.greaterThanZero(orderItemDO.getFreeCount())) {
                Map<Object, List<RetailFreeReturnItemDO>> listMap = CollectionUtil.toListMap(freeReturnItemDOList,
                        "orderItemGuid");
                List<RetailFreeReturnItemDO> freeReturnItemDOS = listMap.get(orderItemDO.getGuid());
                List<FreeItemDTO> freeItemDTOS = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(freeReturnItemDOS)) {
                    for (RetailFreeReturnItemDO freeReturnItemDO : freeReturnItemDOS) {
                        FreeItemDTO freeItemDTO = retailTransform.freeReturnItemDO2freeItemDTO(freeReturnItemDO);
                        //计算赠送菜品价格小计
                        BigDecimal freeItemPrice = getItemPrice(retailItemDTO, addPriceTotal, freeReturnItemDO
                                .getCount(), true);
                        freeItemDTO.setItemPrice(freeItemPrice);
                        freeItemDTOS.add(freeItemDTO);
                    }
                }
                retailItemDTO.setFreeItemDTOS(freeItemDTOS);
            }
            retailItemDTOS.add(retailItemDTO);
        }
        return retailItemDTOS;
    }

    private static BigDecimal getItemPrice(RetailItemDTO retailItemDTO, BigDecimal addPriceTotal, BigDecimal
            currentCount, boolean isFree) {
        //赠送  直接原价  A
        BigDecimal currentPrice = isFree ? retailItemDTO.getOriginalPrice().multiply(currentCount)
                : retailItemDTO.getPrice().multiply(currentCount);

        BigDecimal attrPrice = BigDecimal.ZERO;
        BigDecimal singleItemAttrTotal = retailItemDTO.getSingleItemAttrTotal();

        BigDecimal itemPrice;
        if (!retailItemDTO.getItemType().equals(ItemTypeEnum.GROUP.getCode())) {
            if (BigDecimalUtil.greaterThanZero(currentPrice)) {
                itemPrice = BigDecimalUtil.setScale2(currentPrice.add(attrPrice));
            } else {
                itemPrice = BigDecimal.ZERO;
            }

        } else {
            if (Objects.isNull(singleItemAttrTotal)) {
                singleItemAttrTotal = BigDecimal.ZERO;
            }
            itemPrice = (retailItemDTO.getPrice().add(singleItemAttrTotal))
                    .multiply(currentCount);
            if (BigDecimalUtil.greaterThanZero(currentCount)) {
                itemPrice = itemPrice.add(addPriceTotal);
            }
        }
        return itemPrice;
    }

    private static BigDecimal getSingleAttrTotal(OrderItemDO orderItemDO, Map<Long, List<ItemAttrDO>> itemListMap,
                                                 Map<Long, List<OrderItemDO>> subItemMap) {
        BigDecimal attrTotal = BigDecimal.ZERO;
        if (orderItemDO.getItemType().equals(ItemTypeEnum.GROUP.getCode())) {
            List<OrderItemDO> orderItemDOS = subItemMap.get(orderItemDO.getGuid());
            if (CollectionUtil.isNotEmpty(orderItemDOS)) {
                for (OrderItemDO itemDO : orderItemDOS) {
                    List<ItemAttrDO> itemAttrDOS = itemListMap.get(itemDO.getGuid());
                    if (CollectionUtil.isNotEmpty(itemAttrDOS)) {
                        for (ItemAttrDO itemAttrDO : itemAttrDOS) {
                            BigDecimal subAttrTotal = itemAttrDO.getAttrPrice().multiply(itemDO.getCurrentCount());
                            //处理套餐称重
                            if (!itemDO.getItemType().equals(ItemTypeEnum.WEIGH.getCode())) {
                                subAttrTotal = subAttrTotal.multiply(itemDO.getPackageDefaultCount());
                            }

                            attrTotal = attrTotal.add(subAttrTotal);
                        }
                    }
                }
            }

        } else {
            List<ItemAttrDO> itemAttrDOS = itemListMap.get(orderItemDO.getGuid());
            if (CollectionUtil.isNotEmpty(itemAttrDOS)) {
                for (ItemAttrDO itemAttrDO : itemAttrDOS) {
                    attrTotal = attrTotal.add(itemAttrDO.getAttrPrice());
                }
            }

        }

        return attrTotal;
    }

    private static BigDecimal getAddFeeTotal(RetailItemDTO retailItemDTO, Boolean isReturn) {
        BigDecimal addFeeTotal = BigDecimal.ZERO;
        BigDecimal orderCount;
        if (isReturn) {
            orderCount = retailItemDTO.getReturnCount();
        } else {
            orderCount = retailItemDTO.getCurrentCount().add(retailItemDTO.getFreeCount() == null ? BigDecimal
                    .ZERO : retailItemDTO.getFreeCount());
        }

        //菜品为套餐时
        if (retailItemDTO.getItemType().equals(ItemTypeEnum.GROUP.getCode())) {
            List<PackageSubgroupDTO> packageSubgroupDTOS = retailItemDTO.getPackageSubgroupDTOS();
            for (PackageSubgroupDTO packageSubgroupDTO : packageSubgroupDTOS) {
                List<SubDineInItemDTO> subDineInItemDTOS = packageSubgroupDTO.getSubDineInItemDTOS();
                for (SubDineInItemDTO subDineInItemDTO : subDineInItemDTOS) {
                    if (BigDecimalUtil.greaterThanZero(subDineInItemDTO.getAddPrice())) {
                        BigDecimal subAttrNum = orderCount
                                .multiply(subDineInItemDTO.getCurrentCount());
                        addFeeTotal = addFeeTotal.add(subDineInItemDTO.getAddPrice().multiply(subAttrNum));
                    }

                }

            }
        }
        return addFeeTotal;
    }

    public static BigDecimal getOrderFee(List<RetailItemDTO> retailItemDTOS, BigDecimal appendFee) {
        BigDecimal orderFee = BigDecimal.ZERO;
        //暂时只加菜品小计，没有附加费
        for (RetailItemDTO retailItemDTO : retailItemDTOS) {
            if (BigDecimalUtil.greaterThanZero(retailItemDTO.getCurrentCount())) {
                orderFee = orderFee.add(retailItemDTO.getItemPrice());
            }
            if (BigDecimalUtil.greaterThanZero(retailItemDTO.getFreeCount())) {
                List<FreeItemDTO> freeItemDTOS = retailItemDTO.getFreeItemDTOS();
                for (FreeItemDTO freeItemDTO : freeItemDTOS) {
                    orderFee = orderFee.add(freeItemDTO.getItemPrice());
                }
            }

        }
        return orderFee.add(appendFee);
    }

    /**
     * 获取订单的价格
     *
     * @param retailItemDTOS
     * @param appendFee
     * @return
     */
    public static BigDecimal getOrderFeePuty(List<RetailItemDTO> retailItemDTOS, BigDecimal appendFee) {
        BigDecimal orderFee = BigDecimal.ZERO;
        //暂时只加菜品小计，没有附加费
        for (RetailItemDTO retailItemDTO : retailItemDTOS) {
            if (BigDecimalUtil.greaterThanZero(retailItemDTO.getCurrentCount())) {
                orderFee = orderFee.add(retailItemDTO.getItemPrice());
            }
            if (BigDecimalUtil.greaterThanZero(retailItemDTO.getFreeCount())) {
                List<FreeItemDTO> freeItemDTOS = retailItemDTO.getFreeItemDTOS();
                for (FreeItemDTO freeItemDTO : freeItemDTOS) {
                    orderFee = orderFee.add(freeItemDTO.getItemPrice());
                }
            }

        }
        return orderFee.add(appendFee);
    }

    public static boolean hasFree(List<RetailOrderItemDO> orderItemDOS) {
        boolean hasFree = false;
        for (RetailOrderItemDO orderItemDO : orderItemDOS) {
            if (BigDecimalUtil.greaterThanZero(orderItemDO.getFreeCount())) {
                hasFree = true;
            }
        }
        return hasFree;
    }

    public static boolean hasFreeRetail(List<RetailOrderItemDO> orderItemDOS) {
        boolean hasFree = false;
        for (RetailOrderItemDO orderItemDO : orderItemDOS) {
            if (BigDecimalUtil.greaterThanZero(orderItemDO.getFreeCount())) {
                hasFree = true;
            }
        }
        return hasFree;
    }

    public static void repairCancel(DineinOrderDetailRespDTO orderDetailRespDTO, OrderDO orderDO, List
            <TransactionRecordDO> transactionRecordDOS) {
        //作废信息

        CancelDetailDTO cancelDetailDTO = new CancelDetailDTO();
        cancelDetailDTO.setCancelDeviceName(BaseDeviceTypeEnum.getDesc(orderDO.getCancelDeviceType()));
        cancelDetailDTO.setCancelDeviceType(orderDO.getCancelDeviceType());
        cancelDetailDTO.setCancelReason(orderDO.getCancelReason());
        cancelDetailDTO.setCancelStaffGuid(orderDO.getCancelStaffGuid());
        cancelDetailDTO.setCancelStaffName(orderDO.getCancelStaffName());
        cancelDetailDTO.setCancelTime(orderDO.getCancelTime());
        //退款信息

        if (CollectionUtil.isNotEmpty(transactionRecordDOS)) {
            List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS = orderTransform
                    .transactionRecordDOS2ActuallyPayFeeDetailDTOS(transactionRecordDOS);
            cancelDetailDTO.setActuallyPayFeeDetailDTOS(actuallyPayFeeDetailDTOS);
        }

        orderDetailRespDTO.setCancelDetailDTO(cancelDetailDTO);

    }

    public static List<DiscountFeeDetailDTO> getDiscountFeeDetailDTOS(List<RetailItemDTO> retailItemDTOList,
                                                                      DiscountRuleBO discountRuleBO, List<DiscountDO>
                                                                              discountDOS, Boolean isSub, Boolean
                                                                              isCombine, BigDecimal appendFee,
                                                                      boolean groupon) {
        List<DiscountFeeDetailDTO> discountFeeDetailDTOS = new ArrayList<>();
        DiscountFeeDetailDTO systemDiscountFeeDetailDTO = new DiscountFeeDetailDTO();
        for (DiscountDO discountDO : discountDOS) {
            DiscountFeeDetailDTO discountFeeDetailDTO = orderTransform.discountDO2DiscountFeeDetailDTO(discountDO);
            switch (DiscountTypeEnum.get(discountDO.getDiscountType())) {
                case GROUPON:
                    if (!isCombine) {
                        //团购验券优惠不能抵消附加费
                        BigDecimal shouldPay = getOrderFee(retailItemDTOList, BigDecimal.ZERO).subtract
                                (getFreeDiscountFee
                                        (retailItemDTOList));
                        if (BigDecimalUtil.greaterThanZero(discountDO.getDiscountFee())) {
                            if (BigDecimalUtil.greaterEqual(discountDO.getDiscountFee(), shouldPay)) {
                                discountFeeDetailDTO.setDiscountFee(shouldPay);
                            } else {
                                discountFeeDetailDTO.setDiscountFee(discountDO.getDiscountFee());
                            }
                        }
                    }
                    break;
                case MEMBER:
                    if (!groupon) {
                        discountFeeDetailDTO.setDiscountFee(getMemberDiscountFee(retailItemDTOList, discountRuleBO
                                .getMemberDiscount()));
                    } else {
                        discountFeeDetailDTO.setDiscountFee(BigDecimal.ZERO);
                    }
                    break;
                case WHOLE:
                    if (!groupon) {
                        discountFeeDetailDTO.setDiscountFee(getWholeDiscountFee(retailItemDTOList, discountRuleBO
                                .getWholeDiscount(), discountRuleBO.getMemberDiscount()));
                    } else {
                        discountFeeDetailDTO.setDiscountFee(BigDecimal.ZERO);
                    }
                    break;
                case CONCESSIONAL:
                    if (!isSub) {
                        discountFeeDetailDTO.setDiscountFee(discountRuleBO.getConcessional() == null ? BigDecimal.ZERO :
                                discountRuleBO.getConcessional());
                    } else {
                        discountFeeDetailDTO.setDiscountFee(BigDecimal.ZERO);
                    }
                    break;
                case FREE:
                    discountFeeDetailDTO.setDiscountFee(getFreeDiscountFee(retailItemDTOList));
                    break;
                case SYSTEM:
                    //清空库里的系统省零金额
                    discountFeeDetailDTO.setDiscountFee(BigDecimal.ZERO);
                    systemDiscountFeeDetailDTO = discountFeeDetailDTO;
                    break;
                default:
                    break;
            }
            discountFeeDetailDTOS.add(discountFeeDetailDTO);
        }
        if (!isCombine) {
            systemDiscountFeeDetailDTO.setDiscountFee(getSystemDiscountFee(discountRuleBO.getSystemDiscountDTOS(),
                    getShouldPay(discountFeeDetailDTOS, getOrderFee(retailItemDTOList, appendFee))));
        }
        return discountFeeDetailDTOS;
    }

    public static BigDecimal getWholeDiscountFee(List<RetailItemDTO> retailItemDTOList, BigDecimal wholeDiscount,
                                                 BigDecimal memberDiscount) {
        BigDecimal wholeDiscountFee = BigDecimal.ZERO;
        if (wholeDiscount != null) {
            for (RetailItemDTO retailItemDTO : retailItemDTOList) {
                if (retailItemDTO.getIsWholeDiscount() == 1) {
                    if (retailItemDTO.getIsMemberDiscount() == 1 && memberDiscount != null) {
                        BigDecimal singleMemberDiscount = BigDecimal.ONE;
                        if (BigDecimalUtil.greaterThanZero(memberDiscount)) {
                            singleMemberDiscount = BigDecimalUtil.divide(memberDiscount, BigDecimal.TEN);
                        }
                        wholeDiscountFee = wholeDiscountFee.add(retailItemDTO.getItemPrice().multiply
                                (singleMemberDiscount).multiply(BigDecimalUtil.getRealDiscount(wholeDiscount)));

                    } else {
                        wholeDiscountFee = wholeDiscountFee.add(retailItemDTO.getItemPrice().multiply(BigDecimalUtil
                                .getRealDiscount(wholeDiscount)));
                    }
                }
            }
        }

        return BigDecimalUtil.setScale2(wholeDiscountFee);
    }

    public static BigDecimal getMemberDiscountFee(List<RetailItemDTO> retailItemDTOList, BigDecimal memberDiscount) {
        BigDecimal memberDiscountFee = BigDecimal.ZERO;
        BigDecimal memberItemFee = BigDecimal.ZERO;
        if (memberDiscount != null) {
            for (RetailItemDTO retailItemDTO : retailItemDTOList) {
                if (retailItemDTO.getIsMemberDiscount() == 1 && BigDecimalUtil.greaterThanZero(retailItemDTO
                        .getCurrentCount())) {
                    memberItemFee = memberItemFee.add(retailItemDTO.getItemPrice());
                }
            }
            memberDiscountFee = memberDiscountFee.add(memberItemFee.multiply(BigDecimalUtil
                    .getRealDiscount(memberDiscount)));
        }


        return BigDecimalUtil.setScale2(memberDiscountFee);

    }

    public static BigDecimal getFreeDiscountFee(List<RetailItemDTO> retailItemDTOS) {
        BigDecimal freeDiscount = BigDecimal.ZERO;
        for (RetailItemDTO retailItemDTO : retailItemDTOS) {
            if (BigDecimalUtil.greaterThanZero(retailItemDTO.getFreeCount())) {
                BigDecimal totalDiscountFee = BigDecimal.ZERO;
                List<FreeItemDTO> freeItemDTOS = retailItemDTO.getFreeItemDTOS();
                for (FreeItemDTO freeItemDTO : freeItemDTOS) {
                    freeDiscount = freeDiscount.add(freeItemDTO.getItemPrice());
//                    totalDiscountFee = totalDiscountFee.add(freeItemDTO.getItemPrice());
                }
                retailItemDTO.setTotalDiscountFee(totalDiscountFee);
            }

        }
        return freeDiscount;
    }

    public static BigDecimal getSystemDiscountFee(List<SystemDiscountDTO> systemDiscountDTOS, BigDecimal
            shouldPay) {

        // 系统省零计算
        SystemDiscountDTO systemDiscountDTO = getSystemDiscount(systemDiscountDTOS, shouldPay);
        if (null != systemDiscountDTO) {
            Integer roundType = systemDiscountDTO.getRoundType();
            Integer scale = systemDiscountDTO.getScale();
            if (roundType == 1) {
                return BigDecimalUtil.setScale2(shouldPay.subtract(shouldPay.setScale(scale, RoundingMode.HALF_UP)));
            }
            if (roundType == 2) {
                return BigDecimalUtil.setScale2(shouldPay.subtract(shouldPay.setScale(scale, RoundingMode.FLOOR)));
            }
            return BigDecimalUtil.setScale2(shouldPay.subtract(shouldPay.setScale(scale, RoundingMode.CEILING)));
        }
        return BigDecimal.ZERO;
    }

    private static void addReturnItemPrice(ReturnItemDTO returnItemDTO) {
        BigDecimal count = returnItemDTO.getCount();
        BigDecimal singleItemAttrTotal = returnItemDTO.getRetailItemDTO().getSingleItemAttrTotal();
        // 返回原价
        BigDecimal price = returnItemDTO.getRetailItemDTO().getPrice();
        BigDecimal itemPrice;
        if (!returnItemDTO.getRetailItemDTO().getItemType().equals(ItemTypeEnum.WEIGH.getCode())) {
            BigDecimal add = price.add(singleItemAttrTotal);
            itemPrice = count.multiply(add);
        } else {
            itemPrice = count.multiply(price).add(singleItemAttrTotal);
        }

        if (returnItemDTO.getRetailItemDTO().getItemType().equals(ItemTypeEnum.GROUP.getCode())) {
            itemPrice = itemPrice.add(getAddFeeTotal(returnItemDTO.getRetailItemDTO(), Boolean.TRUE));
        }
        returnItemDTO.setItemPrice(itemPrice);
    }

    public static List<ReturnItemDTO> bulidReturnItemDTOS(List<RetailFreeReturnItemDO> returnItemDOS, Map<String,
            RetailItemDTO> dineInItemDTOMap) {
        List<ReturnItemDTO> returnItemDTOS = new ArrayList<>();
        for (RetailFreeReturnItemDO freeReturnItemDO : returnItemDOS) {
            ReturnItemDTO returnItemDTO = retailTransform.freeReturnItemDO2rerurnItemDTO2(freeReturnItemDO);
            returnItemDTO.setRetailItemDTO(dineInItemDTOMap.get(String.valueOf(freeReturnItemDO.getOrderItemGuid())));
            addReturnItemPrice(returnItemDTO);
            returnItemDTOS.add(returnItemDTO);
        }
        return returnItemDTOS;
    }

    public static SystemDiscountDTO getSystemDiscount(List<SystemDiscountDTO> systemDiscountDTOS, BigDecimal
            shouldPay) {
        return systemDiscountDTOS.stream().filter(systemDiscountDO -> Objects.equals(systemDiscountDO.getState(), 0))
                .sorted(Comparator.comparing(SystemDiscountDTO::getDiscountFee).reversed()).filter(systemDiscountDO
                        -> systemDiscountDO.getDiscountFee().compareTo(shouldPay) <= 0).findFirst().orElse(null);

    }

    public static List<DiscountDO> getDiscountDOS(List<DiscountDO> discountDOS, DiscountRuleBO discountRuleBO) {
        for (DiscountDO discountDO : discountDOS) {
            switch (DiscountTypeEnum.get(discountDO.getDiscountType())) {
                case MEMBER:
                    if (discountRuleBO.getMemberDiscount() == null) {
                        discountRuleBO.setMemberDiscount(discountDO.getDiscount());
                    } else {
                        discountDO.setDiscount(discountRuleBO.getMemberDiscount());
                    }
                    break;
                case WHOLE:
                    if (discountRuleBO.getWholeDiscount() == null) {
                        discountRuleBO.setWholeDiscount(discountDO.getDiscount());
                    } else {
                        discountDO.setDiscount(discountRuleBO.getWholeDiscount());
                    }
                    break;
                case CONCESSIONAL:
                    if (discountRuleBO.getConcessional() == null) {
                        discountRuleBO.setConcessional(discountDO.getDiscountFee());
                    } else {
                        discountDO.setDiscountFee(discountRuleBO.getConcessional());
                    }
                    break;
                case SYSTEM:
                    List<DiscountRuleBO> discountRuleBOS = retailTransform.systemDiscountDTOS2DiscountRuleBOS
                            (discountRuleBO.getSystemDiscountDTOS());
                    discountDO.setRule(JacksonUtils.writeValueAsString(discountRuleBOS));
                    break;
                case POINTS_DEDUCTION:
                    if (discountRuleBO.getMemberIntegral() == null) {
                        if (StringUtils.isNotEmpty(discountDO.getRule())) {
                            discountRuleBO.setMemberIntegral(Integer.valueOf(discountDO.getRule()));
                        }
                    } else {
                        discountDO.setRule(String.valueOf(discountRuleBO.getMemberIntegral()));
                    }
                    break;
                default:
                    break;
            }
        }
        return discountDOS;

    }

    public static List<DiscountDO> getInsertDiscountDOS(List<Long> guids, List<DiscountDO> discountDOS, DiscountRuleBO
            discountRuleBO, String orderGuid) {
        for (DiscountTypeEnum discountTypeEnum : DiscountTypeEnum.values()) {
            if (!discountTypeEnum.equals(DiscountTypeEnum.OTHER)) {
                DiscountDO discountDO = new DiscountDO();
                discountDO.setGuid(guids.get(discountTypeEnum.getCode() - 1));
                discountDO.setDiscountType(discountTypeEnum.getCode());
                discountDO.setDiscountName(discountTypeEnum.getDesc());
                discountDO.setStaffGuid(RequestContext.getUserGuid());
                discountDO.setStaffName(RequestContext.getUserName());
                discountDO.setStoreGuid(RequestContext.getStoreGuid());
                discountDO.setStoreName(RequestContext.getStoreName());
                discountDO.setOrderGuid(Long.valueOf(orderGuid));
                discountDOS.add(discountDO);
            }
        }

        return getDiscountDOS(discountDOS, discountRuleBO);

    }

    /**
     * 兼容阿里云  和  以后规则新增的问题
     *
     * @param guids
     * @param discountDOS
     * @param discountRuleBO
     * @param orderGuid
     * @return
     */
    public static List<DiscountDO> addNeedInsertDiscountDOS(Queue<Long> guids, List<DiscountDO> discountDOS,
                                                            DiscountRuleBO
                                                                    discountRuleBO, String orderGuid) {
        List<Integer> exitsDiscountTypes = discountDOS.stream().map(DiscountDO::getDiscountType).collect(Collectors
                .toList());
        List<DiscountDO> needAdddiscountDOs = Stream.of(DiscountTypeEnum.values()).filter(a -> !a.equals
                (DiscountTypeEnum.OTHER))
                .filter(a -> !exitsDiscountTypes.contains(Integer.valueOf(a.getCode())))
                .map(discountTypeEnum -> {
                            DiscountDO discountDO = new DiscountDO();
                            discountDO.setGuid(guids.remove());
                            discountDO.setDiscountType(discountTypeEnum.getCode());
                            discountDO.setDiscountName(discountTypeEnum.getDesc());
                            discountDO.setStaffGuid(RequestContext.getUserGuid());
                            discountDO.setStaffName(RequestContext.getUserName());
                            discountDO.setStoreGuid(RequestContext.getStoreGuid());
                            discountDO.setStoreName(RequestContext.getStoreName());
                            discountDO.setOrderGuid(Long.valueOf(orderGuid));
                            return discountDO;
                        }
                ).collect(Collectors.toList());
        return needAdddiscountDOs;
    }

    public static BigDecimal getShouldPay(List<DiscountFeeDetailDTO> discountFeeDetailDTOS, BigDecimal orderFee) {
        BigDecimal shouldPay = orderFee;
        for (DiscountFeeDetailDTO discountFeeDetailDTO : discountFeeDetailDTOS) {
            if (discountFeeDetailDTO.getDiscountFee() != null && !discountFeeDetailDTO
                    .getDiscountType().equals(DiscountTypeEnum.CONCESSIONAL.getCode())) {
                shouldPay = shouldPay.subtract(discountFeeDetailDTO.getDiscountFee());
            }
        }
        return shouldPay;
    }

    public static void calculationMainOrderAmount(DineinOrderDetailRespDTO orderDetailRespDTO, DiscountRuleBO
            discountRuleBO) {

        List<DineinOrderDetailRespDTO> subOrderDetails = orderDetailRespDTO.getSubOrderDetails();
        for (DineinOrderDetailRespDTO subOrderDetail : subOrderDetails) {
            orderDetailRespDTO.setOrderFee(orderDetailRespDTO.getOrderFee().add(subOrderDetail.getOrderFee()));
            orderDetailRespDTO.setActuallyPayFee(orderDetailRespDTO.getActuallyPayFee().add(subOrderDetail
                    .getActuallyPayFee()));
            orderDetailRespDTO.setAppendFee(orderDetailRespDTO.getAppendFee().add(subOrderDetail.getAppendFee()));
            if (orderDetailRespDTO.getDiscountFee() == null) {
                orderDetailRespDTO.setDiscountFee(BigDecimal.ZERO);
            }
            if (subOrderDetail.getDiscountFee() == null) {
                subOrderDetail.setDiscountFee(BigDecimal.ZERO);
            }
            orderDetailRespDTO.setDiscountFee(orderDetailRespDTO.getDiscountFee().add(subOrderDetail.getDiscountFee()));
            //优惠明细
            List<DiscountFeeDetailDTO> discountFeeDetailDTOS = orderDetailRespDTO.getDiscountFeeDetailDTOS();
            Map<Integer, DiscountFeeDetailDTO> discountTypeMap = CollectionUtil.toMap(subOrderDetail
                    .getDiscountFeeDetailDTOS(), "discountType");
            for (DiscountFeeDetailDTO discountFeeDetailDTO : discountFeeDetailDTOS) {

                if (discountTypeMap == null || discountTypeMap.size() == 0) {
                    continue;
                }
                DiscountFeeDetailDTO subDiscountFeeDetailDTO = discountTypeMap.get(discountFeeDetailDTO
                        .getDiscountType());
                if (subDiscountFeeDetailDTO == null || subDiscountFeeDetailDTO.getDiscountFee() == null) {
                    continue;
                }
                discountFeeDetailDTO.setDiscountFee(discountFeeDetailDTO.getDiscountFee().add(subDiscountFeeDetailDTO
                        .getDiscountFee()));
            }

        }
        //并单优惠计算时最后计算省零
        if (discountRuleBO != null) {
            List<DiscountFeeDetailDTO> discountFeeDetailDTOS = orderDetailRespDTO.getDiscountFeeDetailDTOS();

            //计算并单省零时忽略让价
            BigDecimal systemDiscountFee = getSystemDiscountFee(discountRuleBO.getSystemDiscountDTOS(),
                    orderDetailRespDTO.getActuallyPayFee().add(discountRuleBO.getConcessional() == null ? BigDecimal
                            .ZERO : discountRuleBO.getConcessional()));
            for (DiscountFeeDetailDTO discountFeeDetailDTO : discountFeeDetailDTOS) {
                if (discountFeeDetailDTO.getDiscountType().equals(DiscountTypeEnum.SYSTEM.getCode())) {
                    discountFeeDetailDTO.setDiscountFee(systemDiscountFee);
                }
            }
            orderDetailRespDTO.setActuallyPayFee(orderDetailRespDTO.getActuallyPayFee().subtract(systemDiscountFee));
        }
    }

    public static void repairAmount(RetailOrderDetailRespDTO orderDetailRespDTO, OrderDO orderDO,
                                    List<TransactionRecordDO> transactionRecordDOS, List<DiscountDO> discountDOS) {
        orderDetailRespDTO.setDiscountFeeDetailDTOS(retailTransform.discountDOS2DiscountFeeDetailDTOS(discountDOS));
        List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS = orderTransform
                .transactionRecordDOS2ActuallyPayFeeDetailDTOS
                        (transactionRecordDOS);
        for (ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO : actuallyPayFeeDetailDTOS) {
            if (actuallyPayFeeDetailDTO.getPaymentType() == 10) {
                actuallyPayFeeDetailDTO.setPaymentTypeName(actuallyPayFeeDetailDTO.getPaymentTypeName());
            } else {
                actuallyPayFeeDetailDTO.setPaymentTypeName(PaymentTypeEnum.getDesc(actuallyPayFeeDetailDTO
                        .getPaymentType
                                ()));
            }

        }
        orderDetailRespDTO.setActuallyPayFeeDetailDTOS(actuallyPayFeeDetailDTOS);
    }


    public static void repairAmount(RetailOrderDetailRespDTO orderDetailRespDTO, RetailOrderDO retailorderDO,
                                    List<RetailTransactionRecordDO> transactionRecordDOS, List<RetailDiscountDO> discountDOS) {
        orderDetailRespDTO.setDiscountFeeDetailDTOS(retailTransform.discountDOS2DiscountFeeDetailDTOS2(discountDOS));
        List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS = retailTransform
                .transactionRecordDOS2ActuallyPayFeeDetailDTOS
                        (transactionRecordDOS);
        for (ActuallyPayFeeDetailDTO actuallyPayFeeDetailDTO : actuallyPayFeeDetailDTOS) {
            if (actuallyPayFeeDetailDTO.getPaymentType() == 10) {
                actuallyPayFeeDetailDTO.setPaymentTypeName(actuallyPayFeeDetailDTO.getPaymentTypeName());
            } else {
                actuallyPayFeeDetailDTO.setPaymentTypeName(PaymentTypeEnum.getDesc(actuallyPayFeeDetailDTO
                        .getPaymentType
                                ()));
            }

        }
        orderDetailRespDTO.setActuallyPayFeeDetailDTOS(actuallyPayFeeDetailDTOS);
    }

    public static void filterZeroItem(RetailOrderDetailRespDTO orderDetailRespDTO) {
        List<RetailItemDTO> retailItemDTOS = orderDetailRespDTO.getDineInItemDTOS();
        for (Iterator<RetailItemDTO> iterator = retailItemDTOS.iterator(); iterator.hasNext(); ) {
            RetailItemDTO retailItemDTO = iterator.next();
            if (BigDecimalUtil.equal(retailItemDTO.getCurrentCount(), BigDecimal.ZERO) && BigDecimalUtil.equal
                    (retailItemDTO.getFreeCount(), BigDecimal.ZERO)) {
                iterator.remove();
            }
            if (BigDecimalUtil.greaterThanZero(retailItemDTO.getFreeCount())) {
                List<FreeItemDTO> freeItemDTOS = retailItemDTO.getFreeItemDTOS();
                freeItemDTOS.removeIf(freeItemDTO -> BigDecimalUtil.equal(freeItemDTO.getCount(), BigDecimal.ZERO));
            }
        }
    }

    public static void repairRecovery(DineinOrderDetailRespDTO orderDetailRespDTO, OrderDO orderDO,
                                      List<TransactionRecordDO> transactionRecordDOS) {

        ReturnOrderDetailDTO returnOrderDetailDTO = new ReturnOrderDetailDTO();
        returnOrderDetailDTO.setRecoveryDeviceTypeName(BaseDeviceTypeEnum.getDesc(orderDO.getRecoveryDeviceType()));
        returnOrderDetailDTO.setRecoveryFee(orderDO.getOrderFee());
        returnOrderDetailDTO.setRecoveryReason(orderDO.getRecoveryReason());
        returnOrderDetailDTO.setRecoveryStaffGuid(orderDO.getRecoveryStaffGuid());
        returnOrderDetailDTO.setRecoveryStaffName(orderDO.getRecoveryStaffName());
        returnOrderDetailDTO.setRecoveryNum(orderDO.getPrintPreBillNum());
        returnOrderDetailDTO.setRecoveryTime(orderDO.getCancelTime());


        if (CollectionUtil.isNotEmpty(transactionRecordDOS)) {
            List<ActuallyPayFeeDetailDTO> actuallyPayFeeDetailDTOS = orderTransform
                    .transactionRecordDOS2ActuallyPayFeeDetailDTOS(transactionRecordDOS);
            returnOrderDetailDTO.setActuallyPayFeeDetailDTOS(actuallyPayFeeDetailDTOS);
        }

        orderDetailRespDTO.setReturnOrderDetailDTO(returnOrderDetailDTO);
    }

    /**
     * 会员价格补偿接口，由于安卓和其他客户端传过来的price是打折和改价后的商品，
     * 当价格有会员的时候  我们需要补偿价格  主要修改{@link RetailItemDTO} 改价主体类
     * 主要根据改价类型{@link ItemPriceChangeEnum}来判断,如果没有会员价直接跳过
     * case 1：
     * 当没有折扣的时候，我们需要补偿的价格为 -->  原价减去会员价
     * case 2:
     * 当使用改价的时候，不用会员价，补偿价格为  --->  0
     * case 3:
     * 当使用折扣的时候，补偿价格为 --->   (原价-会员价)*打折率
     *
     * @param retailItemDTOS 逻辑类实体集合
     * @return 应该补偿的差价
     */
    public static BigDecimal getSingleMemeberDicountFee(List<RetailItemDTO> retailItemDTOS) {
        BigDecimal freeDiscount = BigDecimal.ZERO;
        for (RetailItemDTO retailItemDTO : retailItemDTOS) {
            //有会员价，如果他是1 不支持会员价，如果他是0，直接减去  如果他是2  （原价-会员价）*折扣
            if (retailItemDTO.getMemberPrice() != null) {
                if (ItemPriceChangeEnum.PRICE_CHANGE.getCode() == retailItemDTO.getPriceChangeType().intValue()) {
                    continue;
                }
//                if(retailItemDTO.getIsGoodsReduceDiscount() !=null &&  retailItemDTO.getIsGoodsReduceDiscount()
// .intValue() == 1){
//                    continue;
//                }
                retailItemDTO.setIsCaculatByMemberPrice(1);
                //用了会员价后的差价
                BigDecimal disparityPrice = null;
                if (ItemPriceChangeEnum.NO_CHANGE.getCode() == retailItemDTO.getPriceChangeType().intValue()) {
                    disparityPrice = retailItemDTO.getOriginalPrice().subtract(retailItemDTO.getMemberPrice());
                } else if (ItemPriceChangeEnum.DISCOUNT.getCode() == retailItemDTO.getPriceChangeType().intValue()) {
                    disparityPrice = retailItemDTO.getOriginalPrice().subtract(retailItemDTO.getMemberPrice());
//                            .multiply(new BigDecimal(retailItemDTO.getDiscountPercent())).divide
// (ITEM_DISCOUNT_PERCENT_BASE, 2);
                } else {
                    throw new UnsupportedOperationException();
                }
                if (retailItemDTO.getIsGoodsReduceDiscount() != null) {
                    freeDiscount = freeDiscount.add(disparityPrice.multiply(retailItemDTO.getCurrentCount()
                            .subtract(retailItemDTO.getIsGoodsReduceDiscount())));
                    retailItemDTO.setItemPrice(retailItemDTO.getItemPrice()
                            .subtract(
                                    disparityPrice.multiply(retailItemDTO.getCurrentCount().subtract(retailItemDTO
                                            .getIsGoodsReduceDiscount()))));

                } else {
                    freeDiscount = freeDiscount.add(disparityPrice.multiply(retailItemDTO.getCurrentCount()));
                    retailItemDTO.setItemPrice(retailItemDTO.getItemPrice()
                            .subtract(
                                    disparityPrice.multiply(retailItemDTO.getCurrentCount())));
                }
//                retailItemDTO.setPrice(retailItemDTO.getPrice().subtract(disparityPrice));
//                retailItemDTO.setTotalDiscountFee(retailItemDTO.getTotalDiscountFee().add(disparityPrice.multiply
// (retailItemDTO.getCurrentCount())));
            }
        }
        return BigDecimalUtil.setScale2(freeDiscount);
    }

    /**
     * 由于前端传过来的数据已经经过单品折扣了，这些数据将不会参与总价的计算，只是计算折扣
     * *  所以我们在计算的时候，分两种是否有会员
     * * case 1:
     * *         当有会员的时候，折扣费 = 会员 * (1000-折扣)
     * * case 2:
     * *         当没有会员的，折扣费 = 原价 * (1000-折扣)
     *
     * @param allItems
     * @return
     */
    public static BigDecimal getSingleDiscountFee(List<RetailItemDTO> allItems) {
        BigDecimal discountReduce = BigDecimal.ZERO;
        for (RetailItemDTO retailItemDTO : allItems) {
            if (!(retailItemDTO.getPriceChangeType().intValue() == ItemPriceChangeEnum.DISCOUNT.getCode())) {
                continue;
            }
            BigDecimal singleDiscountPrice = retailItemDTO.getOriginalPrice().multiply(ITEM_DISCOUNT_PERCENT_BASE
                    .subtract(new BigDecimal(retailItemDTO.getDiscountPercent()))).divide(ITEM_DISCOUNT_PERCENT_BASE,
                    2, BigDecimal.ROUND_HALF_DOWN);
            BigDecimal discountPrice = singleDiscountPrice.multiply(retailItemDTO.getCurrentCount());
            if (retailItemDTO.getPriceChangeType() != null && retailItemDTO.getPriceChangeType().intValue() == 2) {
                retailItemDTO.setChangeItemPrice(retailItemDTO.getItemPrice().subtract(discountPrice));
                retailItemDTO.setChangePrice(retailItemDTO.getPrice().subtract(singleDiscountPrice));
            }
            discountReduce = discountReduce.add(discountPrice);
            retailItemDTO.setTotalDiscountFee(retailItemDTO.getTotalDiscountFee().add(discountPrice));
        }
        return BigDecimalUtil.setScale2(discountReduce);
    }


    /**
     * 单品改价  （原价-减价后的价格） *
     *
     * @param allItems
     * @return
     */
    public static BigDecimal getSinglePriceChangeFee(List<RetailItemDTO> allItems) {
        BigDecimal discountReduce = BigDecimal.ZERO;
        for (RetailItemDTO retailItemDTO : allItems) {
            if (!(retailItemDTO.getPriceChangeType().intValue() == ItemPriceChangeEnum.PRICE_CHANGE.getCode())) {
                continue;
            }
            BigDecimal discountPrice = retailItemDTO.getOriginalPrice().subtract(retailItemDTO.getPrice());
            discountReduce = discountReduce.add(discountPrice);
        }
        return discountReduce;
    }

    /**
     * 折扣转换
     *
     * @return
     */
    public static BigDecimal turnDicountPercentToDot(Integer percent) {
        return new BigDecimal(percent).divide(ITEM_DISCOUNT_PERCENT_BASE);
    }
}
