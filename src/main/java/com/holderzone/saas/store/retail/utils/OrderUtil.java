package com.holderzone.saas.store.retail.utils;

import com.holderzone.saas.store.retail.entity.domain.OrderDO;
import com.holderzone.saas.store.retail.entity.domain.RetailOrderDO;
import com.holderzone.saas.store.retail.entity.enums.StateEnum;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderUtil
 * @date 2019/02/20 14:23
 * @description //订单工具类
 * @program holder-saas-store-trade
 */
public class OrderUtil {


    public static boolean unfinished(RetailOrderDO orderDO) {
        return orderDO.getState().equals(StateEnum.READY.getCode()) || orderDO.getState().equals(StateEnum.FAILURE
                .getCode()) || orderDO.getState().equals(StateEnum.PENDING.getCode());
    }

    public static boolean unfinished(OrderDO orderDO) {
        return orderDO.getState().equals(StateEnum.READY.getCode()) || orderDO.getState().equals(StateEnum.FAILURE
                .getCode()) || orderDO.getState().equals(StateEnum.PENDING.getCode());
    }

    /**
     * 订单表state转换交易状态
     * 状态(1：未结账， 2：已结账， 3：已退款，4：已作废)
     */
    public static int fixOrderState(int state) {
        if (StateEnum.SUCCESS.getCode() == state) {
            return 2;
        }
        if (StateEnum.REFUNDED.getCode() == state) {
            return 3;
        }
        if (StateEnum.CANCEL.getCode() == state) {
            return 4;
        }
        return 1;
    }

}
