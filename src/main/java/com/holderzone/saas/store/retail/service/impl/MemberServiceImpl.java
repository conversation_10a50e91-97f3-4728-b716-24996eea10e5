package com.holderzone.saas.store.retail.service.impl;

import com.holderzone.saas.store.retail.entity.domain.RetailOrderDO;
import com.holderzone.saas.store.retail.service.MemberService;
import com.holderzone.saas.store.retail.service.feign.RetailMemberBaseMsmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberService
 * @date 2019/11/05 11:10
 * @description //TODO
 * @program IdeaProjects
 */
@Service
@Slf4j
public class MemberServiceImpl implements MemberService {

    @Autowired
    RetailMemberBaseMsmService retailMemberBaseMsmService;

    @Override
    @Transactional(propagation= Propagation.SUPPORTS)
    public boolean refoundOrder(RetailOrderDO retailOrderDO,String orderNo,Boolean isRefund,Boolean isChargeBack){
        if (retailOrderDO.getMemberConsumptionGuid()!=null && !retailOrderDO.getMemberConsumptionGuid().equals("0")) {
            log.info("会员退货入参:{},orderNo:{}",retailOrderDO.getMemberConsumptionGuid(),orderNo);
            Boolean refund = retailMemberBaseMsmService.refund(retailOrderDO.getMemberConsumptionGuid(),orderNo,isRefund,isChargeBack);
            log.info("会员退货回执:{}", refund);
        }
        return false;
    }

}