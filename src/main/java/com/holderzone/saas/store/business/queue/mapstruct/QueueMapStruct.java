package com.holderzone.saas.store.business.queue.mapstruct;


import com.holderzone.saas.store.business.queue.domain.HolderQueueDO;
import com.holderzone.saas.store.business.queue.domain.HolderQueueTableDO;
import com.holderzone.saas.store.dto.queue.*;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueMapStruct
 * @date 2019/03/27 17:21
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Component
@Mapper(componentModel = "spring")
public interface QueueMapStruct {
    HolderQueueDO toDo(HolderQueueDTO dto);

    HolderQueueDTO toDto(HolderQueueDO d);

    HolderQueueDetailDTO toDetailDto(HolderQueueDO d);


    TreeTableDTO toDto(HolderQueueTableDO tableDO);

    @Mappings({
            @Mapping(target = "tableGuid", source = "guid"),
            @Mapping(target = "tableName", source = "tableCode")
    })
    TreeTableDTO toDto(TableBasicDTO tableBasicDTO);

    ArrayList<TreeTableDTO> toDto(Collection<HolderQueueTableDO> tableDO);

    @Mappings({
            @Mapping(target = "queueGuid", expression = "java(queueGuid)")
    })
    HolderQueueTableDO toDo(TableDTO tableDO, String queueGuid);

    QueueDetailDTO toTableDto(HolderQueueDO d);

    ArrayList<QueueDetailDTO> toTableDto(Collection<HolderQueueDO> ds);
}