package com.holderzone.saas.store.business.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentType
 * @date 2018/08/09 17:49
 * @description
 * @program holder-saas-store-trading-center
 */
public enum PaymentType {

    MEIT_TUAN(-2, "美团外卖支付"),

    E_LE_MA(-1, "饿了么外卖支付"),

    CASH_PAY(0, "现金支付"),

    JH_PAY(1, "聚合支付"),

    BANK_CARD_PAY(2, "银联支付"),

    MEMBER_CARD_PAY(3, "会员余额支付"),

    FACE_TO_PAY(4, "人脸支付"),
    //由于trade服务 存在为5、6、7、10的支付方式
    DEBT_PAY(8, "挂账支付"),

    CANTEEN_CARD_PAY(9, "食堂卡支付"),

    CANTEEN_ELECTRONIC_CARD(11, "食堂电子卡支付"),

    CANTEEN_PHYSICAL_CARD(12, "食堂实体卡支付"),

    THIRD_ACTIVITY(13, "第三方平台活动"),

    LUDOU_MEMBER_PAY(14, "麓豆支付");

    private Integer id;

    private String name;

    PaymentType(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameById(Integer id) {
        return Arrays.stream(PaymentType.values())
                .filter(paymentType -> Objects.equals(paymentType.id, id))
                .map(paymentType -> paymentType.name)
                .findFirst()
                .orElse(null);
    }

    public static List<Integer> getAllId() {
        return Arrays.stream(PaymentType.values())
                .map(PaymentType::getId)
                .collect(Collectors.toList());
    }
}
