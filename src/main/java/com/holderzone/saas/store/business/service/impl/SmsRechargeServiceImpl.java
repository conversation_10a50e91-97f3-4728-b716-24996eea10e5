package com.holderzone.saas.store.business.service.impl;

import com.holderzone.saas.store.business.service.SmsRechargeService;
import com.holderzone.saas.store.business.service.client.SmsRechargeClient;
import com.holderzone.saas.store.dto.business.manage.ChargeDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className SmsRechargeServiceImpl
 * @date 18-9-10 下午4:57
 * @description 短信充值相关接口实现
 * @program holder-saas-store-business
 */
@Service
public class SmsRechargeServiceImpl implements SmsRechargeService {
    @Autowired
    private SmsRechargeClient smsRechargeClient;

    @Override
    public List<ChargeDTO> findAllProduct() {
        return smsRechargeClient.getChargeListForSMS();
    }
}
