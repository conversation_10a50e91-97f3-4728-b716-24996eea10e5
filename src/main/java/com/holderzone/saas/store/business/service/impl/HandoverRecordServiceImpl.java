package com.holderzone.saas.store.business.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.business.constant.GuidKeyConstant;
import com.holderzone.saas.store.business.entity.domain.HandoverPayDetailDO;
import com.holderzone.saas.store.business.entity.domain.HandoverRecordDO;
import com.holderzone.saas.store.business.enums.PaymentType;
import com.holderzone.saas.store.business.mapper.HandoverPayDetailMapper;
import com.holderzone.saas.store.business.mapper.HandoverRecordMapper;
import com.holderzone.saas.store.business.mapper.PaymentTypeMapper;
import com.holderzone.saas.store.business.mapstruct.HandoverRecordMapstruct;
import com.holderzone.saas.store.business.service.HandoverRecordService;
import com.holderzone.saas.store.business.service.client.*;
import com.holderzone.saas.store.business.utils.DynamicHelper;
import com.holderzone.saas.store.business.utils.GuidUtils;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.journaling.req.JournalStoreAppBaseReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.HandOverRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.PaymentReportRespDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.content.PrintHandOverDTO;
import com.holderzone.saas.store.dto.print.content.PrintHandOverNewDTO;
import com.holderzone.saas.store.dto.print.content.nested.InOutRecord;
import com.holderzone.saas.store.dto.print.content.nested.PayRecord;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailHandOverDTO;
import com.holderzone.saas.store.dto.report.query.HandOverReportQueryDTO;
import com.holderzone.saas.store.dto.report.resp.HandoverReportRespDTO;
import com.holderzone.saas.store.dto.report.resp.HandoverReportStaffDTO;
import com.holderzone.saas.store.dto.reserve.ReserveHandoverDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import com.holderzone.saas.store.dto.user.UserDTO;
import com.holderzone.saas.store.dto.user.UserOrgDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import com.holderzone.saas.store.util.BigDecimalUtil;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import redis.clients.jedis.Protocol;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverRecordServiceImpl
 * @date 2018/07/29 上午10:22
 * @description 交接班接口实现
 * @program holder-saas-store-business-center
 */
@Slf4j
@Service
public class HandoverRecordServiceImpl implements HandoverRecordService {

    private final HandoverRecordMapper handoverRecordMapper;

    private final HandoverPayDetailMapper handoverPayDetailMapper;

    private final TradingClient tradingClient;

    private final PrinterClient printerClient;

    private final RedisTemplate redisTemplate;

    private final TakeAwayClientService takeAwayClientService;

    private final ReportRpcService reportRpcService;

    private final ReserveClient reserveClient;

    private final PaymentTypeMapper paymentTypeMapper;

    private final UserClientService userClientService;

    private final OrgFeignClient orgFeignClient;

    private final DynamicHelper dynamicHelper;

    private final MessageService messageService;

    private final static String HANDOVER_FULL = "-";

    private final static String MONEY_FULL = "￥";
    private final ExecutorService executorService = new ThreadPoolExecutor(3, 10, 3L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(20),
            new ThreadFactoryBuilder().setNameFormat("print-%d").build());

    private final ExecutorService executorMessageService = new ThreadPoolExecutor(3, 10, 3L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(20),
            new ThreadFactoryBuilder().setNameFormat("message-%d").build());

    @Autowired
    public HandoverRecordServiceImpl(HandoverRecordMapper handoverRecordMapper, HandoverPayDetailMapper handoverPayDetailMapper,
                                     TradingClient tradingClient, PrinterClient printerClient,
                                     RedisTemplate redisTemplate, TakeAwayClientService takeAwayClientService,
                                     ReportRpcService reportRpcService, ReserveClient reserveClient,
                                     PaymentTypeMapper paymentTypeMapper, UserClientService userClientService,
                                     OrgFeignClient orgFeignClient, DynamicHelper dynamicHelper,
                                     MessageService messageService) {
        this.handoverRecordMapper = handoverRecordMapper;
        this.handoverPayDetailMapper = handoverPayDetailMapper;
        this.tradingClient = tradingClient;
        this.printerClient = printerClient;
        this.redisTemplate = redisTemplate;
        this.takeAwayClientService = takeAwayClientService;
        this.reportRpcService = reportRpcService;
        this.reserveClient = reserveClient;
        this.paymentTypeMapper = paymentTypeMapper;
        this.userClientService = userClientService;
        this.orgFeignClient = orgFeignClient;
        this.dynamicHelper = dynamicHelper;
        this.messageService = messageService;
    }

    @Override
    public void create(HandoverRecordCreateDTO handoverRecordCreateDTO) {
        String lockKey = "create:";
        boolean lockSuccess = false;
        try {
            lockKey += handoverRecordCreateDTO.getStoreGuid() + handoverRecordCreateDTO.getCreateUserGuid() + "0";
            lockSuccess = setNxEx(lockKey, "1", 30);
            if (!lockSuccess) {
                log.info("交接班创建重复调用");
                return;
            }
            // 验证是否存在未交班记录（查询该门店、该用户下的未交班记录）
            HandoverRecordDO handoverRecordDO = HandoverRecordMapstruct.INSTANCE.toHandoverRecordDO(handoverRecordCreateDTO);
            handoverRecordDO.setStatus(0);
            List<HandoverRecordDO> handoverRecordDOList = handoverRecordMapper.queryAll(handoverRecordDO);
            if (!CollectionUtils.isEmpty(handoverRecordDOList)) {
                throw new BusinessException("有未交班记录，无法操作");
            }
            // 设置handoverRecordGuid等
            handoverRecordDO.setGmtCreate(DateTimeUtils.now());
            handoverRecordDO.setGmtModified(DateTimeUtils.now());
            handoverRecordDO.setHandoverRecordGuid(GuidUtils.nextHandoverRecordGuid());
            handoverRecordDO.setRelationRecordGuid(handoverRecordDO.getHandoverRecordGuid());
            if (0 == handoverRecordMapper.insert(handoverRecordDO)) {
                throw new BusinessException("新增交接班记录失败，请联系管理员");
            }
        } finally {
            if (lockSuccess) {
                redisTemplate.delete(lockKey);
            }
        }
    }

    /**
     * 分布式锁
     *
     * @param key        唯一key
     * @param value      值
     * @param expireTime 锁时间 单位 秒
     * @return true、false
     */
    private boolean setNxEx(String key, String value, long expireTime) {
        try {
            return (Boolean) this.redisTemplate.execute((RedisCallback) (connection) -> {
                Object obj = connection.execute("set", new byte[][]{key.getBytes(), value.getBytes(), "NX".getBytes(), "EX".getBytes(), Protocol.toByteArray(expireTime)});
                return obj != null;
            });
        }catch (Exception e){
            log.error("redis连接异常",e);
            throw new BusinessException("系统繁忙，请稍后再试！");
        }

    }

    @Override
    public HandoverPayDTO settle(HandoverPayQueryDTO handoverPayQueryDTO) {
        // 根据门店、用户查询未交班的记录（同一门店、同一用户只存在一条未交班记录）
        HandoverRecordDO handoverRecordDO = handoverRecordMapper.queryByStoreGuidAndUserGuid(handoverPayQueryDTO.getStoreGuid(),
                handoverPayQueryDTO.getUserGuid());
        return calc(handoverRecordDO);
    }

    @Override
    public HandoverPayNewDTO settleNew(HandoverPayQueryDTO handoverPayQueryDTO) {
        // 查询是否开启多人交接班
        StoreDTO storeDTO = orgFeignClient.queryStoreByGuid(handoverPayQueryDTO.getStoreGuid());
        List<HandoverPayQueryDTO> queryList = Lists.newArrayList(handoverPayQueryDTO);
        if (Objects.nonNull(storeDTO.getIsMultiHandover()) && storeDTO.getIsMultiHandover() == 1) {
            queryList = buildHandoverPayQueryList(handoverPayQueryDTO.getStoreGuid());
        } else {
            updateBatchAccount(queryList);
        }
        if (CollectionUtils.isEmpty(queryList)) {
            log.error("查询交接班数据失败,storeDTO:{},handoverPayQueryDTO:{}", JacksonUtils.writeValueAsString(storeDTO),
                    JacksonUtils.writeValueAsString(handoverPayQueryDTO));
            return buildEmptyObj(handoverPayQueryDTO, storeDTO.getIsMultiHandover());
        }
        log.info("查询员工交接班记录请求:{}", JacksonUtils.writeValueAsString(queryList));
        List<HandoverPayNewDTO> settleDataList = Collections.synchronizedList(Lists.newArrayList());
        CountDownLatch countDownLatch = new CountDownLatch(queryList.size());
        ExecutorService executor = Executors.newFixedThreadPool(queryList.size());
        UserContext userContext = UserContextUtils.get();
        for (HandoverPayQueryDTO query : queryList) {
            executor.execute(() -> {
                try {
                    log.info("查询员工交接班记录请求query:{}", JacksonUtils.writeValueAsString(query));
                    dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
                    UserContextUtils.putErp(userContext.getEnterpriseGuid());
                    HandoverRecordDO handoverRecordDO = handoverRecordMapper.queryByStoreGuidAndUserGuid(query.getStoreGuid(),
                            query.getUserGuid());
                    if (handoverRecordDO == null) {
                        throw new BusinessException("不存在开班记录");
                    }
                    UserContext finalUserContext = new UserContext();
                    BeanUtils.copyProperties(userContext, finalUserContext);
                    finalUserContext.setStoreGuid(handoverPayQueryDTO.getStoreGuid());
                    finalUserContext.setAccount(query.getAccount());
                    finalUserContext.setUserGuid(handoverRecordDO.getCreateUserGuid());
                    finalUserContext.setUserName(handoverRecordDO.getCreateUserName());
                    UserContextUtils.put(finalUserContext);
                    HandoverPayNewDTO settleData = getSettleData(handoverRecordDO, query);
                    settleDataList.add(settleData);
                    log.info("查询员工交接班记录请求query返回:{},settleData:{}", JacksonUtils.writeValueAsString(query),
                            JacksonUtils.writeValueAsString(settleData));
                } catch (Exception e) {
                    log.error("查询交接班数据失败,query:{},e:{}", JacksonUtils.writeValueAsString(query), e.getMessage());
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (Exception e) {
            log.error("查询交接班记录失败,e:{}", e.getMessage());
        } finally {
            executor.shutdown();
        }
        if (CollectionUtils.isEmpty(settleDataList)) {
            log.error("查询交接班数据失败,handoverPayQueryDTO:{}", JacksonUtils.writeValueAsString(handoverPayQueryDTO));
            throw new BusinessException("查询交接班数据失败");
        }
        if (Objects.isNull(storeDTO.getIsMultiHandover()) || storeDTO.getIsMultiHandover() == 0) {
            // 单人交接班
            log.info("单人交接班查询返回数据:{}", JacksonUtils.writeValueAsString(settleDataList));
            return settleDataList.get(0);
        }
        // 多人交接班
        return buildMultiSettleData(handoverPayQueryDTO, settleDataList);
    }

    private HandoverPayNewDTO buildMultiSettleData(HandoverPayQueryDTO handoverPayQueryDTO, List<HandoverPayNewDTO> settleDataList) {
        // 多人交接班
        // 获取当前门店所有待交班的员工
        List<UserOrgDTO> allUser = settleDataList.stream()
                .map(h -> new UserOrgDTO().setGuid(h.getUserGuid()).setName(h.getUserName()))
                .sorted(Comparator.comparing(UserOrgDTO::getGuid))
                .collect(Collectors.toList());
        // 过滤前端选择查询的人
        if (CollectionUtils.isNotEmpty(handoverPayQueryDTO.getUserGuids())) {
            settleDataList = settleDataList.stream()
                    .filter(e -> handoverPayQueryDTO.getUserGuids().contains(e.getUserGuid()))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(settleDataList)) {
            log.error("查询交接班数据失败,userGuids:{}", JacksonUtils.writeValueAsString(handoverPayQueryDTO.getUserGuids()));
            throw new BusinessException("数据发生变更，请刷新后重试");
        }
        HandoverPayNewDTO settleData = settleDataList.get(0);
        // 多人交接班开关
        settleData.setIsMultiHandover(1);
        // 交接负责人姓名
        settleData.setHandoverName(UserContextUtils.getUserName());
        if (settleDataList.size() > 1) {
            // 聚合数据
            polymerizeSettleList(settleDataList, settleData);
        }
        settleData.setUserGuids(handoverPayQueryDTO.getUserGuids());
        settleData.setUserOrgDTOList(allUser);
        log.info("多人交接班查询返回数据:{}", JacksonUtils.writeValueAsString(settleData));
        return settleData;
    }

    private HandoverPayNewDTO buildEmptyObj(HandoverPayQueryDTO handoverPayQueryDTO, Integer isMultiHandover) {
        return new HandoverPayNewDTO()
                .setHandOnCash(BigDecimal.ZERO)
                .setSaleCount(0)
                .setSaleIncoming(BigDecimal.ZERO)
                .setRechargeCount(0)
                .setRechargeIncoming(BigDecimal.ZERO)
                .setReserveCount(0)
                .setReserveIncoming(BigDecimal.ZERO)
                .setUserGuid(UserContextUtils.getUserGuid())
                .setUserName("")
                .setHandoverName(UserContextUtils.getUserName())
                .setGmtCreate(Objects.isNull(handoverPayQueryDTO.getGmtCreate()) ? LocalDateTime.now() : handoverPayQueryDTO.getGmtCreate())
                .setGmtModified(LocalDateTime.now())
                .setHandoverRecordGuid("")
                .setUserGuids(handoverPayQueryDTO.getUserGuids())
                .setUserOrgDTOList(Lists.newArrayList())
                .setIsMultiHandover(Objects.isNull(isMultiHandover) ? 0 : isMultiHandover)
                .setTraffic(0)
                .setTotalSeats(0)
                .setOccupancyRatePercent("0.00%")
                .setTableUseCount(0)
                .setTableCount(0)
                .setOpenTableRatePercent("0.00%")
                .setFlipTableRatePercent("0.00%")
                .setTotalDineInTime(0L)
                .setAvgDineInTime(0)
                .setSaleAmount(BigDecimal.ZERO)
                .setDiscountAmount(BigDecimal.ZERO)
                .setRefundAmount(BigDecimal.ZERO);
    }

    /**
     * 聚合交接班数据
     *
     * @param settleDataList 交接班数据
     * @param settleData     返回实体
     */
    private void polymerizeSettleList(List<HandoverPayNewDTO> settleDataList, HandoverPayNewDTO settleData) {
        if (CollectionUtils.isEmpty(settleDataList)) {
            return;
        }
        // 销售订单数
        Integer saleCount = settleDataList.stream()
                .map(HandoverPayNewDTO::getSaleCount)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
        settleData.setSaleCount(saleCount);
        // 销售总额
        BigDecimal saleIncoming = settleDataList.stream()
                .map(HandoverPayNewDTO::getSaleIncoming)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        settleData.setSaleIncoming(saleIncoming);
        // 支付方式
        List<AmountItemDTO> incomingList = settleDataList.stream()
                .filter(i -> CollectionUtils.isNotEmpty(i.getIncomingDetailList()))
                .flatMap(s -> s.getIncomingDetailList().stream())
                .collect(Collectors.toList());
        Map<String, List<AmountItemDTO>> incomingDetailCodeMap = incomingList.stream()
                .filter(i -> Objects.nonNull(i.getName()))
                .collect(Collectors.groupingBy(AmountItemDTO::getName));
        List<AmountItemDTO> incomingDetailNewList = new ArrayList<>();
        for (Map.Entry<String, List<AmountItemDTO>> entry : incomingDetailCodeMap.entrySet()) {
            String name = entry.getKey();
            List<AmountItemDTO> incomingDetailList = entry.getValue();
            BigDecimal payAmount = incomingDetailList.stream()
                    .map(AmountItemDTO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal excessAmount = incomingDetailList.stream()
                    .filter(e -> null != e.getExcessAmount() && BigDecimal.ZERO.compareTo(e.getExcessAmount()) < 0)
                    .map(AmountItemDTO::getExcessAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            AmountItemDTO amountItemDTO = new AmountItemDTO();
            amountItemDTO.setName(name);
            amountItemDTO.setAmount(payAmount);
            amountItemDTO.setExcessAmount(excessAmount);
            amountItemDTO.setCode(incomingDetailList.get(0).getCode());
            // 第三方活动
            List<AmountItemDTO.InnerDetails> innerDetails = new ArrayList<>();
            if (PaymentType.THIRD_ACTIVITY.getName().equals(name)) {
                List<AmountItemDTO.InnerDetails> innerDetailsList = incomingDetailList.stream()
                        .filter(i -> CollectionUtils.isNotEmpty(i.getInnerDetails()))
                        .flatMap(i -> i.getInnerDetails().stream())
                        .collect(Collectors.toList());
                Map<String, List<AmountItemDTO.InnerDetails>> activityTypeMap = innerDetailsList.stream()
                        .collect(Collectors.groupingBy(AmountItemDTO.InnerDetails::getName));
                for (Map.Entry<String, List<AmountItemDTO.InnerDetails>> activityTypeEntry : activityTypeMap.entrySet()) {
                    String thirdName = activityTypeEntry.getKey();
                    List<AmountItemDTO.InnerDetails> innerDetailList = activityTypeEntry.getValue();
                    BigDecimal thirdAmount = innerDetailList.stream()
                            .map(AmountItemDTO.InnerDetails::getAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    AmountItemDTO.InnerDetails details = new AmountItemDTO.InnerDetails();
                    details.setName(thirdName);
                    details.setAmount(thirdAmount);
                    Map<String, List<AmountItemDTO.InnerDetails>> activityMap = innerDetailList.stream()
                            .filter(i -> CollectionUtils.isNotEmpty(i.getInnerDetails()))
                            .flatMap(i -> i.getInnerDetails().stream())
                            .collect(Collectors.groupingBy(AmountItemDTO.InnerDetails::getName));
                    List<AmountItemDTO.InnerDetails> activityInfoList = new ArrayList<>();
                    activityMap.forEach((activityName, activityList) -> {
                        Integer activityCount = activityList.stream()
                                .map(AmountItemDTO.InnerDetails::getCount)
                                .reduce(0, Integer::sum);
                        BigDecimal activityAmount = activityList.stream()
                                .map(AmountItemDTO.InnerDetails::getAmount)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        AmountItemDTO.InnerDetails info = new AmountItemDTO.InnerDetails();
                        info.setName(activityName);
                        info.setAmount(activityAmount);
                        info.setCount(activityCount);
                        activityInfoList.add(info);
                    });
                    details.setInnerDetails(activityInfoList);
                    innerDetails.add(details);
                }
            }
            amountItemDTO.setInnerDetails(innerDetails);
            incomingDetailNewList.add(amountItemDTO);
        }
        settleData.setIncomingDetailList(incomingDetailNewList);

        // 团购验券,目前只有这一种会展示出来,以后如果多了要分组处理
        List<AmountItemDTO> grouponList = settleDataList.stream()
                .filter(i -> CollectionUtils.isNotEmpty(i.getDiscountDetailList()))
                .flatMap(h -> h.getDiscountDetailList().stream())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(grouponList)) {
            List<AmountItemDTO> discountDetailList = new ArrayList<>();
            Map<Integer, List<AmountItemDTO>> groupingByCodeMap = grouponList.stream().collect(Collectors.groupingBy(AmountItemDTO::getCode));
            for (Map.Entry<Integer, List<AmountItemDTO>> entry : groupingByCodeMap.entrySet()) {
                List<AmountItemDTO> grouponItemList = entry.getValue();
                AmountItemDTO grouponItem = grouponItemList.get(0);
                AmountItemDTO discountDTO = new AmountItemDTO();
                discountDTO.setName(grouponItem.getName());
                discountDTO.setAmount(grouponItemList.stream().map(AmountItemDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                discountDTO.setInnerDetails(mergeGrouponDetails(grouponItemList));
                discountDetailList.add(discountDTO);
            }
            settleData.setDiscountDetailList(discountDetailList);
        }

        // 储值订单数，储值金额
        Integer rechargeCount = settleDataList.stream()
                .map(HandoverPayNewDTO::getRechargeCount)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
        settleData.setRechargeCount(rechargeCount);
        BigDecimal rechargeIncoming = settleDataList.stream()
                .map(HandoverPayNewDTO::getRechargeIncoming)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        settleData.setRechargeIncoming(rechargeIncoming);
        List<AmountItemDTO> chargeDetailList = settleDataList.stream()
                .filter(i -> CollectionUtils.isNotEmpty(i.getChargeDetailList()))
                .flatMap(s -> s.getChargeDetailList().stream())
                .collect(Collectors.toList());
        settleData.setChargeDetailList(handleDetailList(chargeDetailList));

        // 预订订单数，订金金额
        Integer reserveCount = settleDataList.stream()
                .map(HandoverPayNewDTO::getReserveCount)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
        settleData.setReserveCount(reserveCount);
        BigDecimal reserveIncoming = settleDataList.stream()
                .map(HandoverPayNewDTO::getReserveIncoming)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        settleData.setReserveIncoming(reserveIncoming);
        List<AmountItemDTO> reservePayDetailList = settleDataList.stream()
                .filter(i -> CollectionUtils.isNotEmpty(i.getReservePayDetailList()))
                .flatMap(s -> s.getReservePayDetailList().stream())
                .collect(Collectors.toList());
        settleData.setReservePayDetailList(handleDetailList(reservePayDetailList));

        // 还款笔数，还挂账金额
        Integer repaymentFeeCount = settleDataList.stream()
                .map(HandoverPayNewDTO::getRepaymentFeeCount)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
        settleData.setRepaymentFeeCount(repaymentFeeCount);
        BigDecimal repaymentFeeTotal = settleDataList.stream()
                .map(HandoverPayNewDTO::getRepaymentFeeTotal)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        settleData.setRepaymentFeeTotal(repaymentFeeTotal);
        List<AmountItemDTO> repaymentList = settleDataList.stream()
                .filter(i -> CollectionUtils.isNotEmpty(i.getRepaymentList()))
                .flatMap(s -> s.getRepaymentList().stream())
                .collect(Collectors.toList());
        settleData.setRepaymentList(handleDetailList(repaymentList));

        // 设置选中员工
        List<UserOrgDTO> userOrgDTOList = settleDataList.stream()
                .map(h -> new UserOrgDTO().setGuid(h.getUserGuid()).setName(h.getUserName()))
                .sorted(Comparator.comparing(UserOrgDTO::getGuid))
                .collect(Collectors.toList());
        settleData.setUserOrgDTOList(userOrgDTOList);
        // 当班员工
        settleData.setUserName(StringUtils.join(settleDataList.stream().map(HandoverPayNewDTO::getUserName).distinct().toArray(), "、"));
        // 交接班时间
        settleData.setGmtCreate(settleDataList.stream().min(Comparator.comparing(HandoverPayNewDTO::getGmtCreate)).get().getGmtCreate());
        settleData.setGmtModified(settleDataList.stream().max(Comparator.comparing(HandoverPayNewDTO::getGmtModified)).get().getGmtModified());
        BigDecimal handOnCash = settleDataList.stream()
                .map(HandoverPayNewDTO::getHandOnCash)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        settleData.setHandOnCash(handOnCash);
        BigDecimal realHandOnCash = settleDataList.stream()
                .map(HandoverPayNewDTO::getRealHandOnCash)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        settleData.setRealHandOnCash(realHandOnCash);
        // 退款总额
        BigDecimal refundAmount = settleDataList.stream()
                .map(HandoverPayNewDTO::getRefundAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        settleData.setRefundAmount(refundAmount);
        handleOptimizationHandoverData(settleDataList, settleData);
    }

    /**
     * 组装多人交接班优化数据：客流量，上座率，开台率，翻台率，平均用餐时长等
     * @param settleDataList
     * @param settleData
     */
    private void handleOptimizationHandoverData(List<HandoverPayNewDTO> settleDataList, HandoverPayNewDTO settleData) {
        // 总优惠总额
        BigDecimal totalDiscountAmount = settleDataList.stream()
                .map(HandoverPayNewDTO::getDiscountAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        settleData.setDiscountAmount(totalDiscountAmount);
        // 销售（已结总金额）
        settleData.setSaleAmount(settleData.getSaleIncoming().add(settleData.getRefundAmount()).add(totalDiscountAmount));
        // 总客流量
        Integer totalTraffic = settleDataList.stream().map(HandoverPayNewDTO::getTraffic)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
        settleData.setTraffic(totalTraffic);
        // 总餐位数
        Integer totalSeats = settleData.getTotalSeats();
        // 总桌台数
        Integer totalTableCount = settleData.getTableCount();
        // 总桌台使用次数
        Integer totalTableUseCount = settleDataList.stream().map(HandoverPayNewDTO::getTableUseCount)
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
        settleData.setTableUseCount(totalTableUseCount);
        // 总用餐时长
        Long totalDineInTime = settleDataList.stream().map(HandoverPayNewDTO::getTotalDineInTime)
                .filter(Objects::nonNull)
                .reduce(0L, Long::sum);
        settleData.setTotalDineInTime(totalDineInTime);
        // 上座率
        setOccupancyRate(settleData, totalSeats, totalTraffic);
        // 开台率
        setOpenTableRate(settleData, totalTableCount, totalTableUseCount);
        // 翻台率
        setFlipTableRate(settleData, totalTableCount, totalTableUseCount);
        // 平均用餐时长
        setAvgDineInTime(settleData, totalTableUseCount, totalDineInTime);
    }

    private static void setAvgDineInTime(HandoverPayNewDTO handoverPayNewDTO, Integer totalTableUseCount, Long totalDineInTime) {
        // 平均用餐时长 = (离座时间 - 入座时间) ÷ 总桌数，单位分钟
        int avgDineInTime;
        if (Objects.isNull(totalTableUseCount) || Objects.isNull(totalDineInTime) || totalTableUseCount == 0) {
            avgDineInTime = 0;
        } else {
            avgDineInTime = (int) Math.floor((double) totalDineInTime / totalTableUseCount + 0.5);
        }
        handoverPayNewDTO.setAvgDineInTime(avgDineInTime);
    }

    private static void setFlipTableRate(HandoverPayNewDTO handoverPayNewDTO, Integer tableCount, Integer tableUseCount) {
        // 翻台率 = (桌台使用次数 - 总桌台数) ÷ 总桌台数 × 100%
        double flipTableRate;
        if (Objects.isNull(tableCount) || Objects.isNull(tableUseCount) || tableCount == 0) {
            flipTableRate = 0;
        } else {
            flipTableRate = (double) (tableUseCount - tableCount) / tableCount;
        }
        // 翻台率
        String flipTableRatePercent = String.format("%.2f%%", flipTableRate * 100);
        handoverPayNewDTO.setFlipTableRatePercent(flipTableRatePercent);
    }

    private static void setOpenTableRate(HandoverPayNewDTO handoverPayNewDTO, Integer tableCount, Integer tableUseCount) {
        // 开台率 = 桌台使用次数 ÷ 总桌台数 × 100%
        double openTableRate;
        if (Objects.isNull(tableCount) || Objects.isNull(tableUseCount) || tableCount == 0) {
            openTableRate = 0;
        } else {
            openTableRate = (double) tableUseCount / tableCount;
        }
        // 开台率
        String openTableRatePercent = String.format("%.2f%%", openTableRate * 100);
        handoverPayNewDTO.setOpenTableRatePercent(openTableRatePercent);
    }

    private static void setOccupancyRate(HandoverPayNewDTO handoverPayNewDTO, Integer totalSeats, Integer traffic) {
        // 上座率 = 客流量 ÷ 总餐位数 × 100%
        double occupancyRate;
        if (Objects.isNull(totalSeats) || Objects.isNull(traffic) || totalSeats == 0) {
            occupancyRate = 0;
        } else {
            occupancyRate = (double) traffic / totalSeats;
        }
        // 上座率
        String occupancyRatePercent = String.format("%.2f%%", occupancyRate * 100);
        handoverPayNewDTO.setOccupancyRatePercent(occupancyRatePercent);
    }

    private List<AmountItemDTO> handleDetailList(List<AmountItemDTO> chargeDetailList) {
        List<AmountItemDTO> chargeDetails = new ArrayList<>();
        Map<String, List<AmountItemDTO>> detailSortMap = chargeDetailList.stream()
                .filter(i -> !StringUtils.isEmpty(i.getName()))
                .collect(Collectors.groupingBy(AmountItemDTO::getName));
        for (Map.Entry<String, List<AmountItemDTO>> entry : detailSortMap.entrySet()) {
            String payName = entry.getKey();
            List<AmountItemDTO> chargeList = entry.getValue();
            BigDecimal payAmount = chargeList.stream()
                    .map(AmountItemDTO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            AmountItemDTO dto = new AmountItemDTO();
            dto.setName(payName);
            dto.setAmount(payAmount);
            chargeDetails.add(dto);
        }
        return chargeDetails;
    }

    private List<AmountItemDTO.InnerDetails> mergeGrouponDetails(List<AmountItemDTO> grouponItemList) {
        List<AmountItemDTO.InnerDetails> grouponDetails = grouponItemList.stream()
                .filter(e -> CollectionUtils.isNotEmpty(e.getInnerDetails()))
                .flatMap(h -> h.getInnerDetails().stream())
                .collect(Collectors.toList());
        Map<String, List<AmountItemDTO.InnerDetails>> grouponDetailMap = grouponDetails.stream()
                .collect(Collectors.groupingBy(AmountItemDTO.InnerDetails::getName));
        List<AmountItemDTO.InnerDetails> result = Lists.newArrayList();
        for (Map.Entry<String, List<AmountItemDTO.InnerDetails>> entry : grouponDetailMap.entrySet()) {
            AmountItemDTO.InnerDetails detail = new AmountItemDTO.InnerDetails();
            detail.setName(entry.getKey());
            detail.setCount(entry.getValue().stream().map(AmountItemDTO.InnerDetails::getCount).reduce(0, Integer::sum));
            detail.setAmount(entry.getValue().stream().map(AmountItemDTO.InnerDetails::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            result.add(detail);
        }
        return result;
    }

    private List<HandoverPayQueryDTO> buildHandoverPayQueryList(String storeGuid) {
        HandOverQueryBO queryDTO = new HandOverQueryBO();
        queryDTO.setStoreGuid(storeGuid);
        queryDTO.setState(0);
        queryDTO.setBusinessStartDateTime(LocalDateTime.now().minusMonths(6));
        LocalDateTime onlineTime = LocalDateTime.parse("2022-10-01 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        if (queryDTO.getBusinessStartDateTime().compareTo(onlineTime) < 0) {
            queryDTO.setBusinessStartDateTime(onlineTime);
        }
        List<HandoverRecordDO> unHandoverRecordList = handoverRecordMapper.queryByCondition(queryDTO);
        List<String> userGuidList = unHandoverRecordList.stream()
                .map(HandoverRecordDO::getCreateUserGuid)
                .distinct()
                .collect(Collectors.toList());
        if (!userGuidList.contains(UserContextUtils.getUserGuid())) {
            HandoverRecordDO recordDO = handoverRecordMapper.queryByStoreGuidAndUserGuid(storeGuid, UserContextUtils.getUserGuid());
            if (Objects.nonNull(recordDO)) {
                unHandoverRecordList.add(recordDO);
            }
        }
        if (CollectionUtils.isEmpty(unHandoverRecordList)) {
            return Lists.newArrayList();
        }
        log.info("查询未交班数据 unHandoverRecordList:{}", JacksonUtils.writeValueAsString(unHandoverRecordList));

        // 查询员工数据
        List<UserDTO> userDTOList = userClientService.listUser(userGuidList);
        Map<String, String> accountMap = userDTOList.stream()
                .collect(Collectors.toMap(UserDTO::getGuid, UserDTO::getAccount, (v1, v2) -> v1));
        UserContext userContext = UserContextUtils.get();
        userContext.setStoreGuid(storeGuid);
        List<HandoverPayQueryDTO> queryList = Lists.newArrayList();
        unHandoverRecordList.forEach(unHandover -> {
            if (StringUtils.isEmpty(accountMap.get(unHandover.getCreateUserGuid()))) {
                return;
            }
            HandoverPayQueryDTO unHandoverPayQueryDTO = new HandoverPayQueryDTO();
            unHandoverPayQueryDTO.setAccount(accountMap.get(unHandover.getCreateUserGuid()));
            unHandoverPayQueryDTO.setStoreGuid(storeGuid);
            unHandoverPayQueryDTO.setUserGuid(unHandover.getCreateUserGuid());
            unHandoverPayQueryDTO.setUserName(unHandover.getCreateUserName());
            unHandoverPayQueryDTO.setGmtModified(LocalDateTime.now());
            queryList.add(unHandoverPayQueryDTO);
        });
        return queryList;
    }

    private void updateBatchAccount(List<HandoverPayQueryDTO> queryList) {
        if (CollectionUtils.isEmpty(queryList)) {
            return;
        }
        Set<String> userGuidList = queryList.stream().map(HandoverPayQueryDTO::getUserGuid).collect(Collectors.toSet());
        // 查询员工数据
        List<UserDTO> userDTOList = userClientService.listUser(new ArrayList<>(userGuidList));
        Map<String, String> accountMap = userDTOList.stream()
                .collect(Collectors.toMap(UserDTO::getGuid, UserDTO::getAccount, (v1, v2) -> v1));
        queryList.forEach(query -> {
            if (StringUtils.isEmpty(accountMap.get(query.getUserGuid()))) {
                return;
            }
            query.setAccount(accountMap.get(query.getUserGuid()));
        });
    }

    private HandoverPayNewDTO getSettleData(HandoverRecordDO handoverRecordDO, HandoverPayQueryDTO handoverPayQueryDTO) {
        handoverPayQueryDTO.setGmtCreate(handoverRecordDO.getGmtCreate());
        // 调用预订服务查询预订数据
        ReserveHandoverDTO reserveHandoverDTO = reserveHandover(handoverPayQueryDTO);
        // 调用trade服务查询销售及充值数据
        HandoverPayDTO handoverPayDTO = tradeHandover(handoverRecordDO, handoverPayQueryDTO);
        // 调用外卖服务查询销售数据
        HandoverPayDTO takeAwayHandoverPayDTO = takeawayHandover(handoverPayQueryDTO, handoverPayDTO);
        // 查询门店下的支付类型排序
        handoverPaySorted(handoverRecordDO, reserveHandoverDTO, handoverPayDTO, takeAwayHandoverPayDTO);
        // 组装返回数据
        HandoverPayNewDTO handoverPayNewDTO = buildHandoverPayNewDTO(handoverRecordDO, reserveHandoverDTO, handoverPayDTO);
        log.info("当班数据查询结果：{}", JacksonUtils.writeValueAsString(handoverPayNewDTO));
        return handoverPayNewDTO;
    }


    private HandoverPayDTO calc(HandoverRecordDO handoverRecordDO) {
        if (handoverRecordDO == null) {
            throw new BusinessException("不存在开班记录");
        }
        handoverRecordDO.setGmtModified(DateTimeUtils.now());

        // 调用支付中心服务获取该班次的收入相关信息
        HandoverPayQueryDTO handoverPayQueryDTO = new HandoverPayQueryDTO();
        handoverPayQueryDTO.setGmtCreate(handoverRecordDO.getGmtCreate());
        handoverPayQueryDTO.setGmtModified(DateTimeUtils.now());
        handoverPayQueryDTO.setStoreGuid(handoverRecordDO.getStoreGuid());
        handoverPayQueryDTO.setUserGuid(handoverRecordDO.getCreateUserGuid());
        HandoverPayDTO handoverPayDTO = tradingClient.query(handoverPayQueryDTO);
        HandoverPayDTO takeAway = takeAwayClientService.getTakeAway(handoverPayQueryDTO);
        if (handoverPayDTO == null) {
            handoverPayDTO = new HandoverPayDTO();
            handoverPayDTO.setCheckedCount(0);
            handoverPayDTO.setBusinessIncoming(BigDecimal.ZERO);
            handoverPayDTO.setPaymentCount(0);
            handoverPayDTO.setChargeIncoming(BigDecimal.ZERO);
            handoverPayDTO.setChargedCount(0);
            handoverPayDTO.setPaymentMoney(BigDecimal.ZERO);
            handoverPayDTO.setSaleIncoming(BigDecimal.ZERO);
            handoverPayDTO.setUserGuid(handoverRecordDO.getCreateUserGuid());
            handoverPayDTO.setUserName(handoverRecordDO.getCreateUserName());
            handoverPayDTO.setGmtCreate(handoverRecordDO.getGmtCreate());
            handoverPayDTO.setGmtModified(LocalDateTime.now());
            handoverPayDTO.setIncomingDetailStr(new HashMap<>());
            handoverPayDTO.setIncomingDetail(new HashMap<>());
        }
        log.info("交班时调用支付服务获取班次内交易信息，入参为：{}，返回结果为：{}", JacksonUtils.writeValueAsString(handoverPayQueryDTO), JacksonUtils.writeValueAsString(handoverPayDTO));
        log.info("交班时调用外卖获取班次内交易信息，入参为：{}，返回结果为：{}", JacksonUtils.writeValueAsString(handoverPayQueryDTO), JacksonUtils.writeValueAsString(takeAway));
        if (null != takeAway) {

            Integer checkedCount = Optional.ofNullable(takeAway.getCheckedCount()).orElse(0);
            BigDecimal saleIncoming = Optional.ofNullable(takeAway.getSaleIncoming()).orElse(BigDecimal.ZERO);


            handoverPayDTO.setPaymentCount(handoverPayDTO.getPaymentCount() == null ?
                    checkedCount : handoverPayDTO.getPaymentCount() + checkedCount);
            handoverPayDTO.setCheckedCount(handoverPayDTO.getCheckedCount() == null ?
                    checkedCount : handoverPayDTO.getCheckedCount() + checkedCount);

            handoverPayDTO.setSaleIncoming(handoverPayDTO.getSaleIncoming() == null ?
                    saleIncoming : handoverPayDTO.getSaleIncoming().add(saleIncoming));

            handoverPayDTO.setBusinessIncoming(handoverPayDTO.getBusinessIncoming() == null ?
                    saleIncoming : handoverPayDTO.getBusinessIncoming().add(saleIncoming));

            handoverPayDTO.setPaymentMoney(handoverPayDTO.getPaymentMoney() == null ?
                    saleIncoming : handoverPayDTO.getPaymentMoney().add(saleIncoming));

            if (null != takeAway.getIncomingDetail()) {
                handoverPayDTO.getIncomingDetail().putAll(takeAway.getIncomingDetail());
            }
            if (null != takeAway.getIncomingDetailStr()) {
                handoverPayDTO.getIncomingDetailStr().putAll(takeAway.getIncomingDetailStr());
            }
        }
        //对接会员营业日报
        handoverPayDTO.setChargeIncoming(handoverPayDTO.getChargeIncoming());
        handoverPayDTO.setChargedCount(handoverPayDTO.getChargedCount());
        return handoverPayDTO;
    }

    @Override
    public void confirm(HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        // 根据门店、用户查询未交班的记录（同一门店、同一用户只存在一条未交班记录）
        HandoverRecordDO handoverRecordDO = handoverRecordMapper.queryByStoreGuidAndUserGuid(handoverRecordConfirmDTO.getStoreGuid(),
                UserContextUtils.getUserGuid());
        HandoverPayDTO handoverPayDTO = calc(handoverRecordDO);
        log.warn("当前值班交接班数据为：{}", JacksonUtils.writeValueAsString(handoverPayDTO));
        // 设置交接班状态(1=已交班)、笔数金额、该班次下的支付详情等并更新数据库
        handoverRecordDO.setPaymentCount(handoverPayDTO.getPaymentCount());
        handoverRecordDO.setChargedCount(handoverPayDTO.getChargedCount());  //充值订单数
        handoverRecordDO.setCheckedCount(handoverPayDTO.getCheckedCount());
        handoverRecordDO.setPaymentMoney(handoverPayDTO.getSaleIncoming());
        handoverRecordDO.setMemberChargeMoney(handoverPayDTO.getChargeIncoming()); //充值收入
        handoverRecordDO.setBusinessInComing(handoverPayDTO.getBusinessIncoming());
        handoverRecordDO.setStatus(1);
        handoverRecordDO.setGmtModified(DateTimeUtils.now());
        handoverRecordDO.setConfirmUserName(UserContextUtils.getUserName());
        handoverRecordDO.setConfirmUserGuid(UserContextUtils.getUserGuid());
        // 处理handover_pay_detail表入库数据
        if (handoverPayDTO.getIncomingDetailStr() != null && handoverPayDTO.getIncomingDetailStr().size() > 0) {
            List<HandoverPayDetailDO> handoverPayDetailDOList = new ArrayList<>(handoverPayDTO.getIncomingDetailStr().size());
            List<Long> payDetailGuids = BatchIdGenerator.buildSnowFlakeGuids(GuidKeyConstant.HAND_OVER_PAY_DETAIL_GUID, handoverPayDTO.getIncomingDetailStr().size());
            List<Map.Entry<String, BigDecimal>> entryList = new ArrayList<>(handoverPayDTO.getIncomingDetailStr().entrySet());
            for (int i = 0; i < entryList.size(); i++) {
                HandoverPayDetailDO payDetailDO = new HandoverPayDetailDO()
                        .setGuid(payDetailGuids.get(i))
                        .setHandoverRecordGuid(handoverRecordDO.getHandoverRecordGuid())
                        .setTerminalId(handoverRecordDO.getTerminalId())
                        .setPayType("")
                        .setPayTypeName(entryList.get(i).getKey())
                        .setPayMoney(entryList.get(i).getValue());
                handoverPayDetailDOList.add(payDetailDO);
            }
            handoverPayDetailMapper.batchInsert(handoverPayDetailDOList);
        }
        if (0 == handoverRecordMapper.update(handoverRecordDO)) {
            throw new BusinessException("修改交接班记录失败，请联系管理员");
        }

        // 打印交班记录（不影响主流程，新开线程处理）
        HandoverRecordQuerySingleDTO dto = new HandoverRecordQuerySingleDTO();
        dto.setHandoverRecordGuid(handoverRecordDO.getHandoverRecordGuid());
        PrintHandOverDTO printHandOverDto = this.toHandlerOverDto(this.query(dto), handoverRecordConfirmDTO.getDeviceId());
        Future<String> future = this.printHandOverRecord(printHandOverDto, UserContextUtils.getJsonStr());
        // 暂时取消对打印结果的判断
        /*try {
            if (!"success".equalsIgnoreCase(future.get())) {
                log.error("交班接口中打印服务调用失败，调用结果为：" + future.get());
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("异步执行后获取执行结果失败，e={}", e.getMessage());
        }*/
    }

    @Override
    public void confirmNew(HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        if (CollectionUtils.isEmpty(handoverRecordConfirmDTO.getUserGuidList())) {
            handoverRecordConfirmDTO.setUserGuidList(Lists.newArrayList(handoverRecordConfirmDTO.getUserGuid()));
        }
        handoverRecordConfirmDTO.getUserGuidList().add(UserContextUtils.getUserGuid());
        String storeGuid = handoverRecordConfirmDTO.getStoreGuid();
        // 根据门店、用户查询未交班的记录（同一门店、同一用户只存在一条未交班记录）
        HandOverQueryBO queryDTO = getHandOverQueryBO(handoverRecordConfirmDTO);
        List<HandoverRecordDO> handoverRecordDOList = handoverRecordMapper.queryByCondition(queryDTO);
        if (CollectionUtils.isEmpty(handoverRecordDOList)) {
            log.error("不存在开班记录,queryDTO={}", JacksonUtils.writeValueAsString(queryDTO));
            return;
        }
        List<HandoverPayNewDTO> settleDataList = Collections.synchronizedList(Lists.newArrayList());
        CountDownLatch countDownLatch = new CountDownLatch(handoverRecordDOList.size());
        ExecutorService executor = Executors.newFixedThreadPool(handoverRecordDOList.size());

        List<String> userGuidList = handoverRecordDOList.stream()
                .map(HandoverRecordDO::getCreateUserGuid)
                .distinct()
                .collect(Collectors.toList());
        if (!userGuidList.contains(UserContextUtils.getUserGuid())) {
            HandoverRecordDO recordDO = handoverRecordMapper.queryByStoreGuidAndUserGuid(storeGuid, UserContextUtils.getUserGuid());
            if (Objects.isNull(recordDO)) {
                log.error("未查询到交班负责人信息,storeGuid={},userGuid={}", storeGuid, UserContextUtils.getUserGuid());
                return;
            }
            handoverRecordDOList.add(recordDO);
        }
        HandoverRecordDO relationRecordDTO = handoverRecordDOList.stream()
                .filter(h -> Objects.equals(h.getCreateUserGuid(), UserContextUtils.getUserGuid()))
                .findFirst().orElse(null);
        if (Objects.isNull(relationRecordDTO)) {
            log.error("数据异常未查询到交班负责人信息");
            return;
        }
        String handoverRecordGuid = relationRecordDTO.getHandoverRecordGuid();
        // 处理头部信息
        List<UserDTO> userDTOList = userClientService.listUser(userGuidList);
        if (CollectionUtils.isEmpty(userDTOList)) {
            log.error("系统员工数据查询异常,userGuidList={}", userGuidList);
            return;
        }
        Map<String, String> accountMap = userDTOList.stream()
                .collect(Collectors.toMap(UserDTO::getGuid, UserDTO::getAccount, (v1, v2) -> v1));
        UserContext userContext = UserContextUtils.get();
        handoverRecordDOList.forEach(handoverRecordDO -> {
            executor.execute(() -> {
                try {
                    log.info("保存员工交接班记录请求,handoverRecordDO={}", JacksonUtils.writeValueAsString(handoverRecordDO));
                    dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
                    UserContext finalUserContext = new UserContext();
                    BeanUtils.copyProperties(userContext, finalUserContext);
                    finalUserContext.setStoreGuid(storeGuid);
                    if (StringUtils.isEmpty(accountMap.get(handoverRecordDO.getCreateUserGuid()))) {
                        log.error("系统员工数据异常,createUserGuid={}", handoverRecordDO.getCreateUserGuid());
                        return;
                    }
                    finalUserContext.setAccount(accountMap.get(handoverRecordDO.getCreateUserGuid()));
                    finalUserContext.setUserGuid(handoverRecordDO.getCreateUserGuid());
                    finalUserContext.setUserName(handoverRecordDO.getCreateUserName());
                    UserContextUtils.put(finalUserContext);

                    //交接班数据查询对象
                    HandoverPayNewDTO handoverPayNewDTO = getHandoverPayNewDTO(handoverRecordConfirmDTO, storeGuid, handoverRecordDO);
                    settleDataList.add(handoverPayNewDTO);

                    // 设置交接班状态(1=已交班)、笔数金额、该班次下的支付详情等并更新数据库
                    handoverRecordHandle(handoverRecordConfirmDTO, handoverRecordGuid, handoverRecordDO, handoverPayNewDTO);
                } catch (Exception e) {
                    log.error("保存交接班数据失败,handoverRecordDO={},", JacksonUtils.writeValueAsString(handoverRecordDO));
                    log.error("保存交接班数据失败, e: ", e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        });
        try {
            countDownLatch.await();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            executor.shutdown();
        }
        log.info("交接班 settleDataList={}", JacksonUtils.writeValueAsString(settleDataList));

        //打印交班记录
        handoverEndHandle(handoverRecordConfirmDTO, storeGuid, handoverRecordDOList, settleDataList, relationRecordDTO);
    }

    private HandOverQueryBO getHandOverQueryBO(HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        HandOverQueryBO queryDTO = new HandOverQueryBO();
        queryDTO.setStoreGuid(handoverRecordConfirmDTO.getStoreGuid());
        queryDTO.setUserGuids(handoverRecordConfirmDTO.getUserGuidList());
        queryDTO.setState(0);
        queryDTO.setBusinessStartDateTime(LocalDateTime.now().minusMonths(6));
        LocalDateTime onlineTime = LocalDateTime.parse("2022-10-01 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        if (queryDTO.getBusinessStartDateTime().isBefore(onlineTime)) {
            queryDTO.setBusinessStartDateTime(onlineTime);
        }
        return queryDTO;
    }

    private HandoverPayNewDTO getHandoverPayNewDTO(HandoverRecordConfirmDTO handoverRecordConfirmDTO, String storeGuid, HandoverRecordDO handoverRecordDO) {
        HandoverPayQueryDTO payQueryDTO = new HandoverPayQueryDTO();
        payQueryDTO.setStoreGuid(storeGuid);
        payQueryDTO.setUserGuid(handoverRecordDO.getCreateUserGuid());
        payQueryDTO.setGmtCreate(handoverRecordDO.getGmtCreate());
        payQueryDTO.setGmtModified(LocalDateTime.now());
        HandoverPayNewDTO handoverPayNewDTO = getSettleData(handoverRecordDO, payQueryDTO);
        if (handoverRecordDO.getCreateUserGuid().equals(handoverRecordConfirmDTO.getUserGuid())) {
            handoverPayNewDTO.setRealHandOnCash(handoverRecordConfirmDTO.getRealHandOnCash());
        }
        return handoverPayNewDTO;
    }

    private void handoverEndHandle(HandoverRecordConfirmDTO handoverRecordConfirmDTO, String storeGuid, List<HandoverRecordDO> handoverRecordDOList, List<HandoverPayNewDTO> settleDataList, HandoverRecordDO relationRecordDTO) {
        HandoverPayNewDTO handoverPayNewDTO = settleDataList.get(0);
        handoverPayNewDTO.setHandoverName(UserContextUtils.getUserName());
        handoverPayNewDTO.setIsMultiHandover(handoverRecordConfirmDTO.getIsMultiHandover());
        if (settleDataList.size() > 1) {
            polymerizeSettleList(settleDataList, handoverPayNewDTO);
        }
        PrintHandOverNewDTO printHandOverDto = toHandoverPrint(handoverPayNewDTO, handoverRecordConfirmDTO.getDeviceId());
        printHandOverRecordNew(printHandOverDto, UserContextUtils.getJsonStr());

        // 多人交接班通知设备下线
        if (null != handoverRecordConfirmDTO.getIsMultiHandover() && 1 == handoverRecordConfirmDTO.getIsMultiHandover()) {
            notifyQuit(storeGuid, handoverRecordDOList, relationRecordDTO.getTerminalId());
        }
    }

    private void handoverRecordHandle(HandoverRecordConfirmDTO handoverRecordConfirmDTO, String handoverRecordGuid,
                                      HandoverRecordDO handoverRecordDO, HandoverPayNewDTO handoverPayNewDTO) {
        handoverRecordDO.setPaymentCount(handoverPayNewDTO.getSaleCount())
                .setChargedCount(handoverPayNewDTO.getRechargeCount())
                .setCheckedCount(handoverPayNewDTO.getSaleCount())
                .setReserveCount(handoverPayNewDTO.getReserveCount())
                .setPaymentMoney(handoverPayNewDTO.getSaleIncoming())
                .setMemberChargeMoney(handoverPayNewDTO.getRechargeIncoming())
                .setReserveMoney(handoverPayNewDTO.getReserveIncoming())
                .setHandonCash(handoverPayNewDTO.getHandOnCash())
                .setRealHandonCash(handoverPayNewDTO.getRealHandOnCash())
                .setBusinessInComing(handoverPayNewDTO.getSaleIncoming().add(handoverPayNewDTO.getReserveIncoming()))
                .setStatus(1)
                .setGmtModified(DateTimeUtils.now())
                .setConfirmUserName(UserContextUtils.getUserName())
                .setConfirmUserGuid(UserContextUtils.getUserGuid())
                .setRepaymentFeeCount(handoverPayNewDTO.getRepaymentFeeCount())
                .setRepaymentFeeTotal(handoverPayNewDTO.getRepaymentFeeTotal())
                .setExcessAmount(handoverPayNewDTO.getExcessAmount())
                .setRelationRecordGuid(handoverRecordGuid)
                .setIsMultiHandover(handoverRecordConfirmDTO.getIsMultiHandover())
                .setTraffic(handoverPayNewDTO.getTraffic())
                .setTotalSeats(handoverPayNewDTO.getTotalSeats())
                .setOccupancyRatePercent(handoverPayNewDTO.getOccupancyRatePercent())
                .setTableUseCount(handoverPayNewDTO.getTableUseCount())
                .setTableCount(handoverPayNewDTO.getTableCount())
                .setOpenTableRatePercent(handoverPayNewDTO.getOpenTableRatePercent())
                .setFlipTableRatePercent(handoverPayNewDTO.getFlipTableRatePercent())
                .setTotalDineInTime(handoverPayNewDTO.getTotalDineInTime())
                .setAvgDineInTime(handoverPayNewDTO.getAvgDineInTime())
                .setSaleAmount(handoverPayNewDTO.getSaleAmount())
                .setDiscountAmount(handoverPayNewDTO.getDiscountAmount())
                .setRefundAmount(handoverPayNewDTO.getRefundAmount())
        ;
        //交接班记录更新
        log.info("交班更新参数:{}", JacksonUtils.writeValueAsString(handoverRecordDO));
        if (handoverRecordMapper.update(handoverRecordDO) == 0) {
            throw new BusinessException("修改交接班记录失败，请联系管理员");
        }
        //处理handover_pay_detail表入库数据
        if (!handoverPayNewDTO.getSaleIncomingDetail().isEmpty()) {
            insertHandoverPayDetail(handoverPayNewDTO, handoverRecordDO);
        }
        if (!handoverPayNewDTO.getRechargeIncomingDetail().isEmpty()) {
            insertHandoverPayDetail(handoverPayNewDTO.getRechargeIncomingDetail(), handoverRecordDO, 2);
        }
        if (!handoverPayNewDTO.getReserveIncomingDetail().isEmpty()) {
            insertHandoverPayDetail(handoverPayNewDTO.getReserveIncomingDetail(), handoverRecordDO, 3);
        }
        if (!handoverPayNewDTO.getRepaymentList().isEmpty()) {
            insertHandoverPayDetail(handoverPayNewDTO.getRepaymentStr(), handoverRecordDO, 4);
        }
    }

    /**
     * 多人交接班通知设备下线
     *
     * @param storeGuid            门店guid
     * @param handoverRecordDOList 通知主体
     * @param relationTerminalId   负责人设备id
     */
    private void notifyQuit(String storeGuid, List<HandoverRecordDO> handoverRecordDOList, String relationTerminalId) {
        BusinessMessageDTO multiHandoverMessageDTO = new BusinessMessageDTO();
        multiHandoverMessageDTO.setMessageType(BusinessMsgTypeEnum.HANDOVER_MESSAGE.getId());
        multiHandoverMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.MULTI_HANDOVER.getId());
        multiHandoverMessageDTO.setSubject(BusinessMsgTypeEnum.MULTI_HANDOVER.getName());
        multiHandoverMessageDTO.setPlatform("2");
        multiHandoverMessageDTO.setStoreGuid(storeGuid);
        // 排除自己
        Set<String> terminalIdList = handoverRecordDOList.stream()
                .map(HandoverRecordDO::getTerminalId)
                .filter(id -> !Objects.equals(relationTerminalId, id))
                .collect(Collectors.toSet());
        UserContext userContext = UserContextUtils.get();
        executorMessageService.execute(() -> {
            dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
            UserContextUtils.put(userContext);
            terminalIdList.forEach(terminalId -> {
                multiHandoverMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.HANDOVER_MESSAGE.getId() + ":" + terminalId);
                HashMap<String, String> map = new HashMap<>();
                map.put("message", "多人交接班通知设备下线");
                map.put("terminalId", terminalId);
                multiHandoverMessageDTO.setContent(JSONObject.toJSONString(map));
                log.info("多人交接班通知设备下线 multiHandoverMessageDTO={}", JacksonUtils.writeValueAsString(multiHandoverMessageDTO));
                messageService.msg(multiHandoverMessageDTO);
            });
        });
    }

    @Override
    public HandoverRecordDTO query(HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO) {
        // 实体转换
        HandoverRecordDO handoverRecordDO = HandoverRecordMapstruct.INSTANCE.toHandoverRecordDO(handoverRecordQuerySingleDTO);
        // 查询HandoverRecordDO
        HandoverRecordDO handoverRecordInSql = handoverRecordMapper.query(handoverRecordDO);
        if (null == handoverRecordInSql) {
            throw new BusinessException("交接班记录[" + handoverRecordDO.getHandoverRecordGuid() + "]不存在");
        }
        // 实体转换，返回
        HandoverRecordDTO dto = HandoverRecordMapstruct.INSTANCE.toHandoverRecordDTO(handoverRecordInSql);
        dto.setPayDetailDTOList(HandoverRecordMapstruct.INSTANCE.toHandoverPayDetailDTOList(
                handoverPayDetailMapper.selectPayDetailById(handoverRecordQuerySingleDTO.getHandoverRecordGuid())));
        return dto;
    }

    @Override
    public HandoverPayNewDTO queryNew(HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO) {
        //查询交接班记录
        List<HandoverRecordDO> handoverRecordDOList = handoverRecordMapper.queryNew(handoverRecordQuerySingleDTO.getHandoverRecordGuid());
        if (CollectionUtils.isEmpty(handoverRecordDOList)) {
            throw new BusinessException("交接班记录[" + handoverRecordQuerySingleDTO.getHandoverRecordGuid() + "]不存在");
        }
        log.info("查询交接班记录结果,handoverRecordDOList={}", JacksonUtils.writeValueAsString(handoverRecordDOList));
        List<HandoverPayNewDTO> settleDataList = new ArrayList<>();
        CountDownLatch countDownLatch = new CountDownLatch(handoverRecordDOList.size());
        ExecutorService executor = Executors.newFixedThreadPool(handoverRecordDOList.size());
        UserContext userContext = UserContextUtils.get();
        handoverRecordDOList.forEach(handoverRecordDO -> {
            executor.execute(() -> {
                try {
                    log.info("查询交接班详情请求,handoverRecordDO={}", JacksonUtils.writeValueAsString(handoverRecordDO));
                    dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
                    UserContextUtils.put(userContext);
                    //组装返回对象
                    HandoverPayNewDTO handoverPayNewDTO = new HandoverPayNewDTO();
                    handoverPayNewDTO.setStoreGuid(handoverRecordDO.getStoreGuid())
                            .setStoreName(handoverRecordDO.getStoreName())
                            .setHandoverRecordGuid(handoverRecordDO.getHandoverRecordGuid())
                            .setUserName(handoverRecordDO.getCreateUserName())
                            .setUserGuid(handoverRecordDO.getCreateUserGuid())
                            .setGmtCreate(handoverRecordDO.getGmtCreate())
                            .setGmtModified(handoverRecordDO.getGmtModified())
                            .setStatus(handoverRecordDO.getStatus())
                            .setHandOnCash(handoverRecordDO.getHandonCash())
                            .setRealHandOnCash(handoverRecordDO.getRealHandonCash())
                            .setSaleCount(handoverRecordDO.getCheckedCount()).setSaleIncoming(handoverRecordDO.getPaymentMoney())
                            .setRechargeCount(handoverRecordDO.getChargedCount()).setRechargeIncoming(handoverRecordDO.getMemberChargeMoney())
                            .setReserveCount(handoverRecordDO.getReserveCount()).setReserveIncoming(handoverRecordDO.getReserveMoney())
                            .setRepaymentFeeCount(handoverRecordDO.getRepaymentFeeCount())
                            .setRepaymentFeeTotal(handoverRecordDO.getRepaymentFeeTotal())
                            .setExcessAmount(handoverRecordDO.getExcessAmount())
                            .setHandoverName(handoverRecordDO.getConfirmUserName())
                            .setTraffic(handoverRecordDO.getTraffic())
                            .setTotalSeats(handoverRecordDO.getTotalSeats())
                            .setOccupancyRatePercent(handoverRecordDO.getOccupancyRatePercent())
                            .setTableUseCount(handoverRecordDO.getTableUseCount())
                            .setTableCount(handoverRecordDO.getTableCount())
                            .setOpenTableRatePercent(handoverRecordDO.getOpenTableRatePercent())
                            .setFlipTableRatePercent(handoverRecordDO.getFlipTableRatePercent())
                            .setTotalDineInTime(handoverRecordDO.getTotalDineInTime())
                            .setAvgDineInTime(handoverRecordDO.getAvgDineInTime())
                            .setSaleAmount(handoverRecordDO.getSaleAmount())
                            .setDiscountAmount(handoverRecordDO.getDiscountAmount())
                            .setRefundAmount(handoverRecordDO.getRefundAmount())
                    ;
                    //查询支付方式
                    List<HandoverPayDetailDO> payDetailDOList = handoverPayDetailMapper
                            .selectPayDetailById(handoverRecordDO.getHandoverRecordGuid());
                    HandoverPayQueryDTO handoverPayQueryDTO = new HandoverPayQueryDTO();
                    handoverPayQueryDTO.setUserGuid(handoverRecordDO.getCreateUserGuid());
                    handoverPayQueryDTO.setStoreGuid(handoverRecordDO.getStoreGuid());
                    handoverPayQueryDTO.setGmtCreate(handoverRecordDO.getGmtCreate());
                    handoverPayQueryDTO.setGmtModified(handoverRecordDO.getGmtModified());
                    if (!CollectionUtils.isEmpty(payDetailDOList)) {
                        //将支付方式明细按销售、储值、预订拆分
                        List<HandoverPayDetailDO> saleDetail = payDetailDOList.stream()
                                .filter(v -> v.getPayBelongType() == 1).collect(Collectors.toList());
                        List<HandoverPayDetailDO> rechargeDetail = payDetailDOList.stream()
                                .filter(v -> v.getPayBelongType() == 2).collect(Collectors.toList());
                        List<HandoverPayDetailDO> reserveDetail = payDetailDOList.stream()
                                .filter(v -> v.getPayBelongType() == 3).collect(Collectors.toList());
                        List<HandoverPayDetailDO> repaymentDetail = payDetailDOList.stream()
                                .filter(v -> v.getPayBelongType() == 4).collect(Collectors.toList());
                        //封装支付Map
                        handoverPayNewDTO.setSaleIncomingDetail(payDetailToMap(saleDetail))
                                .setRechargeIncomingDetail(payDetailToMap(rechargeDetail))
                                .setReserveIncomingDetail(payDetailToMap(reserveDetail))
                                .setRepaymentStr(payDetailToMap(repaymentDetail))
                        ;
                        //查询门店下的支付类型排序
                        List<PaymentTypeDTO> paymentTypeDOList = paymentTypeMapper.getAll(handoverRecordDO.getStoreGuid(), 0);
                        Map<String, Integer> sortMap = paymentTypeDOList.stream().collect(
                                Collectors.toMap(PaymentTypeDTO::getPaymentTypeName, PaymentTypeDTO::getSorting));
                        //封装支付List
                        handoverPayNewDTO.setIncomingDetailList(payDetailToList(saleDetail, sortMap))
                                .setChargeDetailList(payDetailToList(rechargeDetail, sortMap))
                                .setReservePayDetailList(payDetailToList(reserveDetail, sortMap))
                                .setRepaymentList(payDetailToList(repaymentDetail, sortMap))
                        ;

                        // 特殊处理第三方活动  第三方活动金额 = 第三方支付金额 - 余出金额
                        handelThirdActivity(handoverPayNewDTO, handoverPayQueryDTO);
                    } else {
                        handoverPayNewDTO.setSaleIncomingDetail(new HashMap<>()).setRechargeIncomingDetail(new HashMap<>())
                                .setReserveIncomingDetail(new HashMap<>())
                                .setIncomingDetailList(new ArrayList<>()).setChargeDetailList(new ArrayList<>())
                                .setDiscountDetailList(new ArrayList<>())
                                .setReservePayDetailList(new ArrayList<>())
                                .setRepaymentList(new ArrayList<>())
                                .setRepaymentStr(new Hashtable<>())
                        ;
                    }
                    handoverPayNewDTO.setDiscountDetailList(Lists.newArrayList());
                    // 查询优惠方式统计
//                    List<AmountItemDTO> discountDetails = tradingClient.handoverNewByDiscount(handoverPayQueryDTO);
//                    if (CollectionUtils.isNotEmpty(discountDetails)) {
//                        handoverPayNewDTO.setDiscountDetailList(discountDetails);
//                    }
                    settleDataList.add(handoverPayNewDTO);
                } catch (Exception e) {
                    log.error("查询交接班详情失败,handoverRecordDO={},e={}", JacksonUtils.writeValueAsString(handoverRecordDO),
                            e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        });
        try {
            countDownLatch.await();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            executor.shutdown();
        }
        log.info("待处理交接班数据,settleDataList={}", JacksonUtils.writeValueAsString(settleDataList));
        HandoverPayNewDTO handoverPayNewDTO = settleDataList.get(0);
        HandoverRecordDO handoverUserRecord = handoverRecordDOList.stream()
                .filter(e -> e.getHandoverRecordGuid().equals(handoverRecordQuerySingleDTO.getHandoverRecordGuid()))
                .findFirst().orElse(new HandoverRecordDO());
        handoverPayNewDTO.setHandoverName(handoverUserRecord.getCreateUserName());
        handoverPayNewDTO.setIsMultiHandover(handoverUserRecord.getIsMultiHandover());
        if (settleDataList.size() > 1) {
            polymerizeSettleList(settleDataList, handoverPayNewDTO);
        }
        return handoverPayNewDTO;
    }

    private Map<String, BigDecimal> payDetailToMap(List<HandoverPayDetailDO> payDetailDOList) {
        Map<String, BigDecimal> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(payDetailDOList)) {
            payDetailDOList.forEach(v -> map.put(v.getPayTypeName(), v.getPayMoney()));
        }
        return map;
    }

    private List<AmountItemDTO> payDetailToList(List<HandoverPayDetailDO> payDetailDOList, Map<String, Integer> sortMap) {
        List<AmountItemDTO> amountList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(payDetailDOList)) {
            payDetailDOList.forEach(v -> {
                AmountItemDTO amount = new AmountItemDTO();
                amount.setName(v.getPayTypeName()).setAmount(v.getPayMoney())
                        .setSort(Optional.ofNullable(sortMap.get(v.getPayTypeName())).orElse(99));
                amountList.add(amount);
            });
            amountList.sort(Comparator.comparing(AmountItemDTO::getSort));
            return amountList;
        } else {
            return amountList;
        }
    }

    @Override
    public Page<HandoverRecordDTO> queryByPage(HandoverRecordQueryAllDTO handoverRecordQueryAllDTO) {
        // 验证storeGuid有效性
        if (handoverRecordMapper.count(handoverRecordQueryAllDTO.getStoreGuid()) == 0) {
            return new Page<>(handoverRecordQueryAllDTO.getCurrentPage(), handoverRecordQueryAllDTO.getPageSize(), new ArrayList<>());
        }

        handoverRecordQueryAllDTO.setStartDateTime(LocalDate.now().minusDays(3).atTime(LocalTime.MIN));

        // 查询列表
        List<HandoverRecordDTO> handoverRecordDOS = handoverRecordMapper.queryByPageNew(handoverRecordQueryAllDTO);
        // 并返回Page对象
        return new Page<>(handoverRecordQueryAllDTO.getCurrentPage(), handoverRecordQueryAllDTO.getPageSize(),
                handoverRecordDOS.size(), handoverRecordDOS);
    }

    @Override
    public Integer isAllConfirmed(HandoverRecordIsAllConfirmedDTO handoverRecordIsAllConfirmedDTO) {
        // 验证是否存在未交班记录
        HandoverRecordDO handoverRecordDO = new HandoverRecordDO();
        handoverRecordDO.setStoreGuid(handoverRecordIsAllConfirmedDTO.getStoreGuid());
        handoverRecordDO.setStatus(0);
        List<HandoverRecordDO> handoverRecordDOS = handoverRecordMapper.queryAll(handoverRecordDO);
        // 返回结果
        return handoverRecordDOS.isEmpty() ? 1 : 0;
    }

    @Override
    public List<HandoverPayDetailDTO> queryPayDetailById(String handoverRecordGuid) {
        List<HandoverPayDetailDO> list = handoverPayDetailMapper.selectPayDetailById(handoverRecordGuid);
        return HandoverRecordMapstruct.INSTANCE.toHandoverPayDetailDTOList(list);
    }

    @Override
    public Integer isAllConfirmedByList(List<String> storeGuidList) {
        List<HandoverRecordDO> handoverRecordDOS = handoverRecordMapper.queryAllByStoreList(storeGuidList);
        return handoverRecordDOS == null || handoverRecordDOS.isEmpty() ? 1 : 0;
    }

    @Override
    public HandoverRecordDTO queryByUserGuid(HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        // 同一个门店、同一个用户只可能存在一条未交班记录
        List<HandoverRecordDO> recordDOS = handoverRecordMapper.queryByUserGuid(handoverRecordConfirmDTO);
        if (recordDOS == null || recordDOS.isEmpty()) {
            return null;
        }
        return HandoverRecordMapstruct.INSTANCE.toHandoverRecordDTO(recordDOS.get(0));
    }

    @Override
    public HandoverRecordDTO queryByUserStoreGuidAndTerminalId(String storeGuid, String terminalId) {
        return HandoverRecordMapstruct.INSTANCE.toHandoverRecordDTO(handoverRecordMapper.queryByUserStoreGuidAndTerminalId(storeGuid, terminalId));
    }

    @Override
    public void printHandOverRecord(String handoverRecordGuid, String deviceId) {
        // 打印交班记录
        HandoverRecordQuerySingleDTO dto = new HandoverRecordQuerySingleDTO();
        dto.setHandoverRecordGuid(handoverRecordGuid);
        HandoverRecordDTO handoverRecordDTO = this.query(dto);
        if (handoverRecordDTO == null || handoverRecordDTO.getStatus() == 0) {
            throw new BusinessException("不存在该交班记录");
        }
        PrintHandOverDTO printHandOverDto = this.toHandlerOverDto(handoverRecordDTO, deviceId);

        this.printHandOverRecord(printHandOverDto, UserContextUtils.getJsonStr());
    }

    @Override
    public void printHandOverRecordNew(String handoverRecordGuid, String deviceId) {
        // 打印交班记录
        HandoverRecordQuerySingleDTO dto = new HandoverRecordQuerySingleDTO();
        dto.setHandoverRecordGuid(handoverRecordGuid);
        HandoverPayNewDTO handoverPayNewDTO = queryNew(dto);
        if (handoverPayNewDTO == null || handoverPayNewDTO.getStatus() == 0) {
            throw new BusinessException("不存在该交班记录");
        }
        PrintHandOverNewDTO printHandOverNewDTO = toHandoverPrint(handoverPayNewDTO, deviceId);
        printHandOverRecordNew(printHandOverNewDTO, UserContextUtils.getJsonStr());
    }

    @Override
    public void prePrintHandOverRecord(HandoverPayNewDTO handoverPayNewDTO) {
        // 预打印交接班，交班员工处理
        if (StringUtils.isNotEmpty(handoverPayNewDTO.getPrintUserName())) {
            handoverPayNewDTO.setUserName(handoverPayNewDTO.getPrintUserName());
        }
        PrintHandOverNewDTO printHandOverNewDTO = toHandoverPrint(handoverPayNewDTO, handoverPayNewDTO.getDeviceId());
        printHandOverNewDTO.setInvoiceType(InvoiceTypeEnum.HANDOVER_PRE_NEW.getType());
        printHandOverRecordNew(printHandOverNewDTO, UserContextUtils.getJsonStr());
    }


    @Override
    public void printReatilHandOverRecord(String handoverRecordGuid, String deviceId) {
        HandoverRecordQuerySingleDTO dto = new HandoverRecordQuerySingleDTO();
        dto.setHandoverRecordGuid(handoverRecordGuid);
        HandoverRecordDTO handoverRecordDTO = this.query(dto);
        if (handoverRecordDTO == null || handoverRecordDTO.getStatus() == 0) {
            throw new BusinessException("不存在该交班记录");
        }
        PrintRetailHandOverDTO printRetailHandOverDTO = this.convertPrintRetailHandOverDto(handoverRecordDTO, deviceId);
        this.printRetailHandOverRecord(printRetailHandOverDTO, UserContextUtils.getJsonStr());
    }

    /**
     * 根据员工guid查询该员工当前的班次详情列表
     *
     * @param handoverRecordConfirmDTO 员工列表
     * @return 员工班次详情列表
     */
    @Override
    public List<HandoverRecordDTO> listByUserGuid(HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        // 同一个门店、同一个用户只可能存在一条未交班记录
        List<HandoverRecordDO> recordDOS = handoverRecordMapper.queryByUserGuid(handoverRecordConfirmDTO);
        if (recordDOS == null || recordDOS.isEmpty()) {
            return null;
        }
        return HandoverRecordMapstruct.INSTANCE.toHandoverRecordDTOS(recordDOS);
    }


    @Override
    public HandoverRecordDTO queryByStoreGuidAndUserGuid(String storeGuid, String userGuid) {
        return HandoverRecordMapstruct.INSTANCE.toHandoverRecordDTO(handoverRecordMapper.queryByStoreGuidAndUserGuid(storeGuid, userGuid));
    }

    @Override
    public HandoverPayDTO retailSettle(HandoverPayQueryDTO handoverPayQueryDTO) {
        HandoverRecordDO handoverRecordDO = handoverRecordMapper.queryByStoreGuidAndUserGuid(handoverPayQueryDTO.getStoreGuid(), handoverPayQueryDTO.getUserGuid());
        return settleHelper(handoverRecordDO);

    }

    @Override
    public void retailConfirm(HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        HandoverRecordDO handoverRecordDO = handoverRecordMapper.queryByStoreGuidAndUserGuid(handoverRecordConfirmDTO.getStoreGuid(), handoverRecordConfirmDTO.getUserGuid());
        HandoverPayDTO handoverPayDTO = settleHelper(handoverRecordDO);
        log.warn("当前值班交接班数据为：{}", JacksonUtils.writeValueAsString(handoverPayDTO));
        handoverRecordDO = handoverRecordDTOConvertDO(handoverRecordDO, handoverPayDTO);
        handoverRecordDO.setChargedCount(0);
        // 处理handover_pay_detail表入库数据
        if (handoverPayDTO.getIncomingDetailStr() != null && handoverPayDTO.getIncomingDetailStr().size() > 0) {
            confirmSavePayDetail(handoverPayDTO.getIncomingDetailStr(), handoverRecordDO.getHandoverRecordGuid(), handoverRecordDO.getTerminalId());
        }
        if (0 == handoverRecordMapper.update(handoverRecordDO)) {
            throw new BusinessException("修改交接班记录失败，请联系管理员");
        }
        // 打印交班记录（不影响主流程，新开线程处理）
        HandoverRecordQuerySingleDTO dto = new HandoverRecordQuerySingleDTO();
        dto.setHandoverRecordGuid(handoverRecordDO.getHandoverRecordGuid());
        PrintRetailHandOverDTO printRetailHandOverDTO = this.convertPrintRetailHandOverDto(this.query(dto), handoverRecordConfirmDTO.getDeviceId());
        Future<String> future = this.printRetailHandOverRecord(printRetailHandOverDTO, UserContextUtils.getJsonStr());

    }

    @Override
    public Page<HandoverRecordDTO> retailQueryByPage(HandoverRecordQueryAllDTO handoverRecordQueryAllDTO) {
        return queryByPage(handoverRecordQueryAllDTO);
    }


    @Override
    public HandoverRecordDTO retailQuery(HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO) {
        return query(handoverRecordQuerySingleDTO);
    }

    @Override
    public HandoverRecordDTO retailQueryByUserGuid(HandoverRecordConfirmDTO handoverRecordConfirmDTO) {

        return null;
    }


    public HandoverRecordDO handoverRecordDTOConvertDO(HandoverRecordDO handoverRecordDO, HandoverPayDTO handoverPayDTO) {
        // 设置交接班状态(1=已交班)、笔数金额、该班次下的支付详情等并更新数据库
        handoverRecordDO.setRefundCount(handoverPayDTO.getRefundCount());  //退货订单数
        handoverRecordDO.setRefundMoney(handoverPayDTO.getRefundMoney());  //退货金额
        handoverRecordDO.setPaymentCount(handoverPayDTO.getPaymentCount());
        handoverRecordDO.setChargedCount(handoverPayDTO.getChargedCount());  //充值订单数
        handoverRecordDO.setCheckedCount(handoverPayDTO.getCheckedCount());
        handoverRecordDO.setPaymentMoney(handoverPayDTO.getSaleIncoming());
        handoverRecordDO.setMemberChargeMoney(handoverPayDTO.getChargeIncoming()); //充值收入
        handoverRecordDO.setBusinessInComing(handoverPayDTO.getBusinessIncoming());
        handoverRecordDO.setStatus(1);
        handoverRecordDO.setGmtModified(DateTimeUtils.now());
        handoverRecordDO.setConfirmUserName(UserContextUtils.getUserName());
        handoverRecordDO.setConfirmUserGuid(UserContextUtils.getUserGuid());
        return handoverRecordDO;
    }


    /**
     * 保存交接班支付详情
     *
     * @param incomingDetailStr
     * @param handoverRecordGuid
     * @param terminalId
     * @return
     */
    private boolean confirmSavePayDetail(Map<String, BigDecimal> incomingDetailStr, String handoverRecordGuid, String terminalId) {
        List<HandoverPayDetailDO> handoverPayDetailDOList = new ArrayList<>(incomingDetailStr.size());
        List<Long> payDetailGuids = BatchIdGenerator
                .buildSnowFlakeGuids(GuidKeyConstant.HAND_OVER_PAY_DETAIL_GUID, incomingDetailStr.size());
        List<Map.Entry<String, BigDecimal>> entryList = new ArrayList<>(incomingDetailStr.entrySet());
        for (int i = 0; i < entryList.size(); i++) {
            HandoverPayDetailDO payDetailDO = new HandoverPayDetailDO()
                    .setGuid(payDetailGuids.get(i))
                    .setHandoverRecordGuid(handoverRecordGuid)
                    .setTerminalId(terminalId)
                    .setPayType("")
                    .setPayTypeName(entryList.get(i).getKey())
                    .setPayMoney(entryList.get(i).getValue());
            handoverPayDetailDOList.add(payDetailDO);
        }
        return handoverPayDetailMapper.batchInsert(handoverPayDetailDOList) > 0 ? true : false;

    }


    private HandoverPayDTO settleHelper(HandoverRecordDO handoverRecordDO) {
        if (Objects.isNull(handoverRecordDO)) {
            throw new BusinessException("不存在开班记录");
        }
        //获取交易数据
        HandoverPayQueryDTO handoverPayQueryDTO = new HandoverPayQueryDTO();
        handoverPayQueryDTO.setGmtCreate(handoverRecordDO.getGmtCreate());
        handoverPayQueryDTO.setGmtModified(LocalDateTime.now());
        handoverPayQueryDTO.setUserGuid(handoverRecordDO.getCreateUserGuid());
        handoverPayQueryDTO.setStoreGuid(handoverRecordDO.getStoreGuid());
        HandoverPayDTO handoverPayDTO = tradingClient.retailQuery(handoverPayQueryDTO);
        if(handoverPayDTO == null) {
            return null;
        }
        handoverPayDTO.setBusinessIncoming(handoverPayDTO.getSaleIncoming());
        handoverPayDTO.setPaymentCount(handoverPayDTO.getCheckedCount());
        handoverPayDTO.setUserGuid(handoverRecordDO.getCreateUserGuid());
        handoverPayDTO.setUserName(handoverRecordDO.getCreateUserName());
        handoverPayDTO.setGmtCreate(handoverRecordDO.getGmtCreate());
        handoverPayDTO.setGmtModified(LocalDateTime.now());
        return handoverPayDTO;
    }

    /**
     * 转换为打印服务需要的实体(餐饮版)
     *
     * @param handoverRecordDTO dto
     * @param deviceNo          系统设备编号
     * @return dto
     */
    private PrintHandOverDTO toHandlerOverDto(HandoverRecordDTO handoverRecordDTO, String deviceNo) {
        // BasePrint赋值
        PrintHandOverDTO printHandOverDto = new PrintHandOverDTO();
        printHandOverDto.setInvoiceType(InvoiceTypeEnum.HANDOVER.getType());
        printHandOverDto.setStoreName(handoverRecordDTO.getStoreName());
        printHandOverDto.setStoreGuid(handoverRecordDTO.getStoreGuid());
        printHandOverDto.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        printHandOverDto.setPrintUid(handoverRecordDTO.getHandoverRecordGuid());
        printHandOverDto.setOperatorStaffGuid(UserContextUtils.getUserGuid());
        printHandOverDto.setOperatorStaffName(UserContextUtils.getUserName());
        printHandOverDto.setCreateTime(DateTimeUtils.nowMillis());

        // HandlerOverDto赋值
        printHandOverDto.setStaffName(handoverRecordDTO.getConfirmUserName());
        LocalDateTime createTime = handoverRecordDTO.getGmtCreate();
        LocalDateTime entTime = handoverRecordDTO.getGmtModified();
        long nh = 60 * 60; // 每小时秒数
        long nm = 60; // 每分钟秒数

        // 两个时间的秒差值
        long seconds = entTime.toEpochSecond(ZoneOffset.of("+8")) - createTime.toEpochSecond(ZoneOffset.of("+8"));
        printHandOverDto.setDuration(String.valueOf(seconds / nh) + "时"
                + String.valueOf(seconds % nh / nm) + "分"
                + String.valueOf(seconds % nh % nm) + "秒");
        printHandOverDto.setBeginTime(DateTimeUtils.localDateTime2String(createTime));
        printHandOverDto.setOverTime(DateTimeUtils.localDateTime2String(entTime));
        printHandOverDto.setDutyAmount(handoverRecordDTO.getBusinessInComing());
        printHandOverDto.setSaleIncome(handoverRecordDTO.getPaymentMoney());
        printHandOverDto.setRechargeIncome(handoverRecordDTO.getMemberChargeMoney());
        printHandOverDto.setDeviceId(deviceNo);
        // 打印来源先固定为一体机
        printHandOverDto.setPrintSourceEnum(PrintSourceEnum.AIO);
        List<PayRecord> payRecordList = new ArrayList<>(handoverRecordDTO.getPayDetailDTOList().size());
        handoverRecordDTO.getPayDetailDTOList().forEach(p -> {
            PayRecord payRecord = new PayRecord(p.getPayTypeName(), p.getPayMoney(), "");
            payRecordList.add(payRecord);
        });
        printHandOverDto.setPayRecordList(payRecordList);
        return printHandOverDto;
    }

    private PrintHandOverNewDTO toHandoverPrint(HandoverPayNewDTO handoverPayNewDTO, String deviceNo) {
        PrintHandOverNewDTO dto = new PrintHandOverNewDTO();
        // BasePrint赋值
        dto.setInvoiceType(InvoiceTypeEnum.HANDOVER_NEW.getType());
        dto.setStoreName(handoverPayNewDTO.getStoreName());
        dto.setStoreGuid(handoverPayNewDTO.getStoreGuid());
        dto.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        dto.setPrintUid(handoverPayNewDTO.getHandoverRecordGuid());
        dto.setOperatorStaffGuid(UserContextUtils.getUserGuid());
        dto.setOperatorStaffName(UserContextUtils.getUserName());
        dto.setCreateTime(DateTimeUtils.nowMillis());
        //HandoverDTO赋值
        dto.setStaffName(handoverPayNewDTO.getUserName());
        dto.setHandoverName(handoverPayNewDTO.getHandoverName());
        dto.setIsMultiHandover(handoverPayNewDTO.getIsMultiHandover());
        LocalDateTime createTime = handoverPayNewDTO.getGmtCreate();
        LocalDateTime endTime = handoverPayNewDTO.getGmtModified();
        long nh = 60 * 60; // 每小时秒数
        long nm = 60; // 每分钟秒数
        // 两个时间的秒差值
        long seconds = endTime.toEpochSecond(ZoneOffset.of("+8")) - createTime.toEpochSecond(ZoneOffset.of("+8"));
        dto.setDuration(seconds / nh + "时"
                + seconds % nh / nm + "分"
                + seconds % nh % nm + "秒");
        dto.setBeginTime(DateTimeUtils.localDateTime2String(createTime));
        dto.setOverTime(DateTimeUtils.localDateTime2String(endTime));
        //收入数据赋值
        dto.setHandOnCash(handoverPayNewDTO.getHandOnCash());
        dto.setRealHandOnCash(handoverPayNewDTO.getRealHandOnCash());
        dto.setSaleCount(handoverPayNewDTO.getSaleCount());
        dto.setSaleIncome(handoverPayNewDTO.getSaleIncoming());
        dto.setRechargeCount(handoverPayNewDTO.getRechargeCount());
        dto.setRechargeIncome(handoverPayNewDTO.getRechargeIncoming());
        dto.setReserveCount(handoverPayNewDTO.getReserveCount());
        dto.setReserveIncome(handoverPayNewDTO.getReserveIncoming());
        dto.setRepaymentFeeCount(handoverPayNewDTO.getRepaymentFeeCount());
        dto.setRepaymentFeeTotal(handoverPayNewDTO.getRepaymentFeeTotal());
        // 打印来源先固定为一体机
        dto.setPrintSourceEnum(PrintSourceEnum.AIO);
        dto.setDeviceId(deviceNo);
        //转换支付详情
        dto.setSalePayRecordList(amount2PayRecord(handoverPayNewDTO.getIncomingDetailList()));
        dto.setDiscountRecordList(amount2PayRecord(handoverPayNewDTO.getDiscountDetailList()));
        dto.setRechargePayRecordList(amount2PayRecord(handoverPayNewDTO.getChargeDetailList()));
        dto.setReservePayRecordList(amount2PayRecord(handoverPayNewDTO.getReservePayDetailList()));
        dto.setRepaymentList(amount2PayRecord(handoverPayNewDTO.getRepaymentList()));
        // 组装多人交接班优化数据：客流量，上座率，开台率，翻台率，平均用餐时长
        dto.setTraffic(handoverPayNewDTO.getTraffic());
        dto.setOccupancyRatePercent(handoverPayNewDTO.getOccupancyRatePercent());
        dto.setOpenTableRatePercent(handoverPayNewDTO.getOpenTableRatePercent());
        dto.setFlipTableRatePercent(handoverPayNewDTO.getFlipTableRatePercent());
        dto.setAvgDineInTime(handoverPayNewDTO.getAvgDineInTime());
        dto.setSaleAmount(handoverPayNewDTO.getSaleAmount());
        dto.setDiscountAmount(handoverPayNewDTO.getDiscountAmount());
        dto.setRefundAmount(handoverPayNewDTO.getRefundAmount());
        return dto;
    }

    /**
     * 将支付详情的map转换为PayRecordList
     *
     * @param map
     * @return
     */
    private List<PayRecord> map2PayRecord(Map<String, BigDecimal> map) {
        List<PayRecord> payRecordList = new ArrayList<>();
        Set<String> payNameSet = map.keySet();
        for (String payName : payNameSet) {
            PayRecord payRecord = new PayRecord();
            payRecord.setPayName(payName);
            payRecord.setAmount(map.get(payName));
            payRecordList.add(payRecord);
        }
        return payRecordList;
    }

    /**
     * 将List<AmountItemDTO>转为打印需要的List<PayRecord>
     *
     * @param amountList
     * @return
     */
    private List<PayRecord> amount2PayRecord(List<AmountItemDTO> amountList) {
        List<PayRecord> payRecordList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(amountList)) {
            amountList.forEach(v -> {
                PayRecord payRecord = new PayRecord();
                payRecord.setPayName(v.getName());
                payRecord.setAmount(v.getAmount());
                payRecord.setExcessAmount(v.getExcessAmount());

                payRecord.setInnerDetails(v.getInnerDetails());
                payRecordList.add(payRecord);
            });
        }
        return payRecordList;
    }


    /**
     * 转换为打印服务需要的实体(零售版)
     *
     * @param handoverRecordDTO dto
     * @param deviceNo          系统设备编号
     * @return dto
     */
    private PrintRetailHandOverDTO convertPrintRetailHandOverDto(HandoverRecordDTO handoverRecordDTO, String deviceNo) {
        // BasePrint赋值
        PrintRetailHandOverDTO printRetailHandOverDTO = new PrintRetailHandOverDTO();
        printRetailHandOverDTO.setInvoiceType(InvoiceTypeEnum.RETAIL_HANDOVER.getType());
        printRetailHandOverDTO.setStoreName(handoverRecordDTO.getStoreName());
        printRetailHandOverDTO.setStoreGuid(handoverRecordDTO.getStoreGuid());
        printRetailHandOverDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        printRetailHandOverDTO.setPrintUid(handoverRecordDTO.getHandoverRecordGuid());
        printRetailHandOverDTO.setOperatorStaffGuid(UserContextUtils.getUserGuid());
        printRetailHandOverDTO.setOperatorStaffName(UserContextUtils.getUserName());
        printRetailHandOverDTO.setCreateTime(DateTimeUtils.nowMillis());
        // HandlerOverDto赋值
        printRetailHandOverDTO.setStaffName(handoverRecordDTO.getConfirmUserName());
        LocalDateTime createTime = handoverRecordDTO.getGmtCreate();
        LocalDateTime entTime = handoverRecordDTO.getGmtModified();
        long nh = 60 * 60; // 每小时秒数
        long nm = 60; // 每分钟秒数
        // 两个时间的秒差值
        long seconds = entTime.toEpochSecond(ZoneOffset.of("+8")) - createTime.toEpochSecond(ZoneOffset.of("+8"));
        printRetailHandOverDTO.setDuration(String.valueOf(seconds / nh) + "时"
                + String.valueOf(seconds % nh / nm) + "分"
                + String.valueOf(seconds % nh % nm) + "秒");
        printRetailHandOverDTO.setBeginTime(DateTimeUtils.localDateTime2String(createTime));
        printRetailHandOverDTO.setOverTime(DateTimeUtils.localDateTime2String(entTime));
        printRetailHandOverDTO.setDutyAmount(handoverRecordDTO.getBusinessInComing());
        printRetailHandOverDTO.setDeviceId(deviceNo);
        // 打印来源先固定为一体机
        printRetailHandOverDTO.setPrintSourceEnum(PrintSourceEnum.AIO);
        List<PayRecord> payRecordList = new ArrayList<>(handoverRecordDTO.getPayDetailDTOList().size());
        handoverRecordDTO.getPayDetailDTOList().forEach(p -> {
            PayRecord payRecord = new PayRecord(p.getPayTypeName(), p.getPayMoney(), "");
            payRecordList.add(payRecord);
        });
        printRetailHandOverDTO.setInOutRecordList(Arrays.asList(
                InOutRecord
                        .builder()
                        .name("销售统计")
                        .number(handoverRecordDTO.getCheckedCount().longValue())
                        .money(handoverRecordDTO.getPaymentMoney())
                        .build(),
                InOutRecord
                        .builder()
                        .name("退货统计")
                        .number(handoverRecordDTO.getRefundCount() == null ? 0L : handoverRecordDTO.getRefundCount().longValue())
                        .money(BigDecimalUtil.nonNullValue(handoverRecordDTO.getRefundMoney()))
                        .build()
        ));
        printRetailHandOverDTO.setPayRecordList(payRecordList);
        return printRetailHandOverDTO;
    }


    /**
     * 调用打印服务，不影响主业务新开线程处理
     *
     * @param printHandOverDto      dto
     * @param currentThreadUserInfo userInfo
     * @return Future<String>
     */
    private Future<String> printHandOverRecord(PrintHandOverDTO printHandOverDto, String currentThreadUserInfo) {
        return executorService.submit(() -> {
            String result = null;
            UserContextUtils.put(currentThreadUserInfo);
            try {
                result = printerClient.printHandOverRecord(printHandOverDto);
            } catch (Exception e) {
                log.error("交班接口中打印服务调用失败，e={}", e.getMessage());
            } finally {
                // 线程执行任务结束后将ThreadLocal中信息移除，防止线程复用后造成数据混乱
                UserContextUtils.remove();
            }
            return result;
        });
    }

    private Future<String> printHandOverRecordNew(PrintHandOverNewDTO printHandOverDto, String currentThreadUserInfo) {
        return executorService.submit(() -> {
            String result = null;
            UserContextUtils.put(currentThreadUserInfo);
            try {
                result = printerClient.printHandOverRecordNew(printHandOverDto);
            } catch (Exception e) {
                log.error("交班接口中打印服务调用失败，e={}", e.getMessage());
            } finally {
                // 线程执行任务结束后将ThreadLocal中信息移除，防止线程复用后造成数据混乱
                UserContextUtils.remove();
            }
            return result;
        });
    }


    /**
     * 调用打印服务，不影响主业务新开线程处理(零售版)
     *
     * @param printHandOverDto      dto
     * @param currentThreadUserInfo userInfo
     * @return Future<String>
     */
    private Future<String> printRetailHandOverRecord(PrintRetailHandOverDTO printHandOverDto, String currentThreadUserInfo) {
        return executorService.submit(() -> {
            String result = null;
            UserContextUtils.put(currentThreadUserInfo);
            try {
                result = printerClient.printRetailHandOverRecord(printHandOverDto);
            } catch (Exception e) {
                log.error("零售版交班接口中打印服务调用失败，e={}", e.getMessage());
            } finally {
                // 线程执行任务结束后将ThreadLocal中信息移除，防止线程复用后造成数据混乱
                UserContextUtils.remove();
            }
            return result;
        });
    }

    private void insertHandoverPayDetail(Map<String, BigDecimal> payDetail, HandoverRecordDO handoverRecordDO,
                                         int payBelongType) {
        //若已经存在对应记录不保存
        if (handoverPayDetailMapper.countByTypeAndId(handoverRecordDO.getHandoverRecordGuid(), payBelongType) > 0) {
            return;
        }

        if (payDetail != null && payDetail.size() > 0) {
            List<HandoverPayDetailDO> handoverPayDetailDOList = new ArrayList<>(payDetail.size());
            List<Long> payDetailGuids = BatchIdGenerator
                    .buildSnowFlakeGuids(GuidKeyConstant.HAND_OVER_PAY_DETAIL_GUID, payDetail.size());
            List<Map.Entry<String, BigDecimal>> entryList = new ArrayList<>(payDetail.entrySet());
            for (int i = 0; i < entryList.size(); i++) {
                HandoverPayDetailDO payDetailDO = new HandoverPayDetailDO()
                        .setGuid(payDetailGuids.get(i))
                        .setHandoverRecordGuid(handoverRecordDO.getHandoverRecordGuid())
                        .setTerminalId(handoverRecordDO.getTerminalId())
                        .setPayType("")
                        .setPayTypeName(entryList.get(i).getKey())
                        .setPayMoney(entryList.get(i).getValue())
                        .setPayBelongType(payBelongType);
                handoverPayDetailDOList.add(payDetailDO);
            }
            handoverPayDetailMapper.batchInsert(handoverPayDetailDOList);
        }
    }

    /**
     * 销售支付
     */
    private void insertHandoverPayDetail(HandoverPayNewDTO handoverPayNewDTO, HandoverRecordDO handoverRecordDO) {
        // 若已经存在对应记录不保存
        if (handoverPayDetailMapper.countByTypeAndId(handoverRecordDO.getHandoverRecordGuid(), 1) > 0) {
            return;
        }
        List<HandoverPayDetailDO> handoverPayDetailDOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(handoverPayNewDTO.getIncomingDetailList())) {
            List<Long> payDetailGuids = BatchIdGenerator.buildSnowFlakeGuids(GuidKeyConstant.HAND_OVER_PAY_DETAIL_GUID,
                    handoverPayNewDTO.getIncomingDetailList().size());
            for (AmountItemDTO incomingItem : handoverPayNewDTO.getIncomingDetailList()) {
                HandoverPayDetailDO payDetailDO = new HandoverPayDetailDO()
                        .setGuid(payDetailGuids.remove(0))
                        .setHandoverRecordGuid(handoverRecordDO.getHandoverRecordGuid())
                        .setTerminalId(handoverRecordDO.getTerminalId())
                        .setPayType(Objects.isNull(incomingItem.getCode()) ? null : String.valueOf(incomingItem.getCode()))
                        .setPayTypeName(incomingItem.getName())
                        .setPayMoney(incomingItem.getAmount())
                        .setPayBelongType(1);
                handoverPayDetailDOList.add(payDetailDO);
            }
        }
        if (CollectionUtils.isNotEmpty(handoverPayNewDTO.getDiscountDetailList())) {
            List<Long> payDetailGuids = BatchIdGenerator.buildSnowFlakeGuids(GuidKeyConstant.HAND_OVER_PAY_DETAIL_GUID,
                    handoverPayNewDTO.getDiscountDetailList().size());
            for (AmountItemDTO discountItem : handoverPayNewDTO.getDiscountDetailList()) {
                HandoverPayDetailDO payDetailDO = new HandoverPayDetailDO()
                        .setGuid(payDetailGuids.remove(0))
                        .setHandoverRecordGuid(handoverRecordDO.getHandoverRecordGuid())
                        .setTerminalId(handoverRecordDO.getTerminalId())
                        .setPayType(String.valueOf(DiscountTypeEnum.GROUPON.getCode() == discountItem.getCode() ?
                                PaymentTypeEnum.MT_GROUPON.getCode() : discountItem.getCode()))
                        .setPayTypeName(discountItem.getName())
                        .setPayMoney(discountItem.getAmount())
                        .setPayBelongType(1);
                handoverPayDetailDOList.add(payDetailDO);
            }
        }
        if (CollectionUtils.isNotEmpty(handoverPayDetailDOList)) {
            handoverPayDetailMapper.batchInsert(handoverPayDetailDOList);
        }
    }

    @Override
    public List<HandoverReportRespDTO> report(HandOverReportQueryDTO handOverQueryDTO) {
        HandOverQueryBO queryBO = HandoverRecordMapstruct.INSTANCE.handOverQueryDTO2queryBO(handOverQueryDTO);
        List<HandoverReportRespDTO> respDTOList = new ArrayList<>();
        // 根据条件查询交接班记录
        List<HandoverRecordDO> handoverRecordDOList = handoverRecordMapper.queryByCondition(queryBO);
        log.info("根据条件查询交接班记录 handoverRecordDOList={}", JacksonUtils.writeValueAsString(handoverRecordDOList));
        if (CollectionUtils.isEmpty(handoverRecordDOList)) {
            log.warn("未查询到交接班记录");
            return respDTOList;
        }
        ArrayList<HandoverPayNewDTO> settleList = new ArrayList<>();

        // 未交班数据
        notSettleDataHandle(handOverQueryDTO, handoverRecordDOList, settleList);

        // 已交班数据
        settleDataHandle(handoverRecordDOList, settleList);
        log.info("settleList={}", JacksonUtils.writeValueAsString(settleList));
        //通过人员分组
        Map<String, List<HandoverPayNewDTO>> groupSettleList = settleList.stream().collect(Collectors.groupingBy(HandoverPayNewDTO::getUserGuid));
        // 按照原型里的顺序处理报表数据结构
        // 销售（已结）订单数，销售（已结）总额
        saleHandle(respDTOList, settleList, groupSettleList);

        // 支付方式
        payWayHandle(respDTOList, settleList, groupSettleList);

        // 团购验券
        groupHandle(respDTOList, settleList, groupSettleList);

        // 储值订单数，储值金额
        rechargeHandel(respDTOList, settleList, groupSettleList);

        // 预订订单数，订金金额
        reserveHandle(respDTOList, settleList, groupSettleList);

        // 还款笔数，还挂账金额
        repaymentFeeHandle(respDTOList, settleList, groupSettleList);

        //填充默认字符
        respDTOList.forEach(resp -> resp.getStaffStatisticals().forEach(staff -> {
            if (StringUtils.isEmpty(staff.getStatistical())) {
                staff.setStatistical(HANDOVER_FULL);
            }
        }));
        respDTOList.sort(Comparator.comparing(HandoverReportRespDTO::getSort));
        log.info("根据条件查询交接班记录，返回数据：{}", JacksonUtils.writeValueAsString(respDTOList));
        return respDTOList;
    }

    private void saleHandle(List<HandoverReportRespDTO> respDTOList, ArrayList<HandoverPayNewDTO> settleList, Map<String, List<HandoverPayNewDTO>> groupSettleList) {
        Integer saleCount = settleList.stream()
                .map(HandoverPayNewDTO::getSaleCount)
                .reduce(0, Integer::sum);
        if (0 != saleCount) {
            HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
            respDTO.setStatisticalType("销售（已结）订单数");
            respDTO.setStatistical(saleCount.toString());
            List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
            groupSettleList.forEach((userGuid, handoverPays) -> {
                        Integer staticica = handoverPays.stream()
                                .map(HandoverPayNewDTO::getSaleCount)
                                .reduce(0, Integer::sum);
                        staffStatisticals.add(getStaffDTO(userGuid, handoverPays, staticica));
                    }
            );
            respDTO.setStaffStatisticals(staffStatisticals);
            respDTO.setSort(0);
            respDTOList.add(respDTO);
        }
        BigDecimal saleIncoming = settleList.stream()
                .map(HandoverPayNewDTO::getSaleIncoming)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (BigDecimal.ZERO.compareTo(saleIncoming) != 0) {
            HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
            respDTO.setStatisticalType("销售（已结）净额");
            respDTO.setStatistical(MONEY_FULL + saleIncoming.toPlainString());
            List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
            groupSettleList.forEach((userGuid, handoverPays) -> {
                        BigDecimal amount = handoverPays.stream()
                                .map(HandoverPayNewDTO::getSaleIncoming)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        staffStatisticals.add(getStaffDTO(userGuid, handoverPays, amount));
                    }
            );
            respDTO.setStaffStatisticals(staffStatisticals);
            respDTO.setSort(1);
            respDTOList.add(respDTO);
        }
    }

    private void groupHandle(List<HandoverReportRespDTO> respDTOList, ArrayList<HandoverPayNewDTO> settleList, Map<String, List<HandoverPayNewDTO>> groupSettleList) {
        List<AmountItemDTO> groupList = settleList.stream()
                .filter(i -> CollectionUtils.isNotEmpty(i.getDiscountDetailList()))
                .flatMap(h -> h.getDiscountDetailList().stream())
                .filter(e -> DiscountTypeEnum.getPaymentType().contains(e.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(groupList)) {
            Map<Integer, List<AmountItemDTO>> groupingByCodeMap = groupList.stream().collect(Collectors.groupingBy(AmountItemDTO::getCode));
            int sort = 90;
            for (Map.Entry<Integer, List<AmountItemDTO>> entry : groupingByCodeMap.entrySet()) {
                buildGroupBuyHandoverReport(entry, respDTOList, sort, groupSettleList);
                sort = sort + 2;
            }
        }
    }

    private void buildGroupBuyHandoverReport(Map.Entry<Integer, List<AmountItemDTO>> entry, List<HandoverReportRespDTO> respDTOList, Integer sort, Map<String, List<HandoverPayNewDTO>> groupSettleList) {
        List<AmountItemDTO> groupList = entry.getValue();
        BigDecimal groupAmount = groupList.stream()
                .map(AmountItemDTO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (groupAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
        respDTO.setStatisticalType(groupList.get(0).getName());
        respDTO.setStatistical(MONEY_FULL + groupAmount.toPlainString());
        List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
        groupSettleList.forEach((userGuid, payDtos) -> {
            BigDecimal amount = payDtos.stream()
                    .filter(i -> CollectionUtils.isNotEmpty(i.getDiscountDetailList()))
                    .flatMap(h -> h.getDiscountDetailList().stream())
                    .filter(e -> Objects.equals(e.getCode(), entry.getKey()))
                    .map(AmountItemDTO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            staffStatisticals.add(getStaffDTO(userGuid, payDtos, amount));
        });
        log.info("{}团购员工统计明细：{}", groupList.get(0).getName(), JacksonUtils.writeValueAsString(staffStatisticals));
        respDTO.setStaffStatisticals(staffStatisticals);
        respDTO.setSort(sort);
        respDTOList.add(respDTO);
        Map<String, List<AmountItemDTO.InnerDetails>> groupActivityMap = groupList.stream()
                .filter(i -> CollectionUtils.isNotEmpty(i.getInnerDetails()))
                .flatMap(g -> g.getInnerDetails().stream())
                .collect(Collectors.groupingBy(AmountItemDTO.InnerDetails::getName));
        StringBuilder statistical = new StringBuilder();
        groupActivityMap.forEach((activityName, groupActivityList) -> {
            Integer count = groupActivityList.stream()
                    .map(AmountItemDTO.InnerDetails::getCount)
                    .reduce(0, Integer::sum);
            BigDecimal amount = groupActivityList.stream()
                    .map(AmountItemDTO.InnerDetails::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistical.append(activityName).append("*").append(count).append("  ￥").append(amount).append(";");
            HandoverReportRespDTO dto = new HandoverReportRespDTO();
            dto.setStatisticalType("");
            dto.setStatistical(statistical.toString());
            dto.setSort(sort + 1);
            List<HandoverReportStaffDTO> staffStatistis = new ArrayList<>();
            groupSettleList.forEach((userGuid, payDtos) -> {
                StringBuilder userStatistical = new StringBuilder();
                BigDecimal activityAmount = payDtos.stream()
                        .filter(i -> CollectionUtils.isNotEmpty(i.getDiscountDetailList()))
                        .flatMap(h -> h.getDiscountDetailList().stream())
                        .filter(e -> Objects.equals(e.getCode(), entry.getKey()))
                        .filter(i -> CollectionUtils.isNotEmpty(i.getInnerDetails()))
                        .flatMap(g -> g.getInnerDetails().stream())
                        .filter(d -> Objects.equals(d.getName(), activityName))
                        .map(AmountItemDTO.InnerDetails::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                Integer activityCount = payDtos.stream()
                        .filter(i -> CollectionUtils.isNotEmpty(i.getDiscountDetailList()))
                        .flatMap(h -> h.getDiscountDetailList().stream())
                        .filter(e -> Objects.equals(e.getCode(), entry.getKey()))
                        .filter(i -> CollectionUtils.isNotEmpty(i.getInnerDetails()))
                        .flatMap(g -> g.getInnerDetails().stream())
                        .filter(d -> Objects.equals(d.getName(), activityName))
                        .map(AmountItemDTO.InnerDetails::getCount)
                        .reduce(0, Integer::sum);
                if (activityCount > 0) {
                    userStatistical.append(activityName).append("*").append(activityCount).append("  ￥").append(activityAmount).append(";");
                } else {
                    userStatistical.append(HANDOVER_FULL);
                }
                staffStatistis.add(new HandoverReportStaffDTO(userGuid, payDtos.get(0).getUserName(), userStatistical.toString()));
            });
            log.info("团购详情员工统计明细：{}", JacksonUtils.writeValueAsString(staffStatistis));
            dto.setStaffStatisticals(staffStatistis);
            respDTOList.add(dto);
        });
    }

    private void repaymentFeeHandle(List<HandoverReportRespDTO> respDTOList, ArrayList<HandoverPayNewDTO> settleList, Map<String, List<HandoverPayNewDTO>> groupSettleList) {
        Integer repaymentFeeCount = settleList.stream()
                .map(HandoverPayNewDTO::getRepaymentFeeCount)
                .reduce(0, Integer::sum);
        if (0 != repaymentFeeCount) {
            HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
            respDTO.setStatisticalType("还款笔数");
            respDTO.setStatistical(repaymentFeeCount.toString());
            respDTO.setSort(200);
            List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
            groupSettleList.forEach((userGuid, handoverPays) -> {
                        Integer sum = handoverPays.stream()
                                .map(HandoverPayNewDTO::getRepaymentFeeCount)
                                .reduce(0, Integer::sum);
                        staffStatisticals.add(getStaffDTO(userGuid, handoverPays, sum));
                    }
            );
            respDTO.setStaffStatisticals(staffStatisticals);
            respDTOList.add(respDTO);
        }
        BigDecimal repaymentFeeTotal = settleList.stream()
                .map(HandoverPayNewDTO::getRepaymentFeeTotal)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (BigDecimal.ZERO.compareTo(repaymentFeeTotal) < 0) {
            HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
            respDTO.setStatisticalType("还挂账金额");
            respDTO.setStatistical(MONEY_FULL + repaymentFeeTotal.toPlainString());
            respDTO.setSort(201);
            List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
            groupSettleList.forEach((userGuid, handoverPays) -> {
                        BigDecimal amount = handoverPays.stream()
                                .map(HandoverPayNewDTO::getRepaymentFeeTotal)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        staffStatisticals.add(getStaffDTO(userGuid, handoverPays, amount));
                    }
            );
            respDTO.setStaffStatisticals(staffStatisticals);
            respDTOList.add(respDTO);
        }

        repaymentListhandle(respDTOList, settleList, groupSettleList);
    }

    private <T> HandoverReportStaffDTO getStaffDTO(String userGuid, List<HandoverPayNewDTO> handoverPays, T statistical) {
        if (CollectionUtils.isEmpty(handoverPays)) {
            throw new BusinessException("handoverPays为空");
        }
        String statist;
        if (statistical instanceof BigDecimal) {
            statist = ((BigDecimal) statistical).compareTo(BigDecimal.ZERO) == 0 ? HANDOVER_FULL : MONEY_FULL + ((BigDecimal) statistical).toPlainString();
            return new HandoverReportStaffDTO(userGuid, handoverPays.get(0).getUserName(), statist);
        }
        if (statistical instanceof Integer) {
            statist = Objects.equals(0, statistical) ? HANDOVER_FULL : String.valueOf(statistical);
            return new HandoverReportStaffDTO(userGuid, handoverPays.get(0).getUserName(), statist);
        }
        return new HandoverReportStaffDTO();
    }

    private void repaymentListhandle(List<HandoverReportRespDTO> respDTOList, ArrayList<HandoverPayNewDTO> settleList, Map<String, List<HandoverPayNewDTO>> groupSettleList) {
        List<AmountItemDTO> repaymentList = settleList.stream()
                .filter(i -> CollectionUtils.isNotEmpty(i.getRepaymentList()))
                .flatMap(s -> s.getRepaymentList().stream())
                .collect(Collectors.toList());
        Map<Integer, List<AmountItemDTO>> detailSortMap = repaymentList.stream()
                .filter(i -> Objects.nonNull(i.getSort()))
                .collect(Collectors.groupingBy(AmountItemDTO::getSort));
        for (Map.Entry<Integer, List<AmountItemDTO>> entry : detailSortMap.entrySet()) {
            Integer sort = entry.getKey();
            List<AmountItemDTO> chargeList = entry.getValue();
            BigDecimal payAmount = chargeList.stream()
                    .map(AmountItemDTO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (BigDecimal.ZERO.compareTo(payAmount) != 0) {
                HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
                respDTO.setStatisticalType(chargeList.get(0).getName());
                respDTO.setStatistical(MONEY_FULL + payAmount.toPlainString());
                respDTO.setSort(sort + 202);
                List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
                groupSettleList.forEach((userGuid, handoverPays) -> {
                            BigDecimal amount = handoverPays.stream()
                                    .filter(i -> CollectionUtils.isNotEmpty(i.getRepaymentList()))
                                    .flatMap(s -> s.getRepaymentList().stream())
                                    .filter(i -> Objects.nonNull(i.getSort()) && Objects.equals(i.getSort(), sort))
                                    .map(AmountItemDTO::getAmount)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            staffStatisticals.add(getStaffDTO(userGuid, handoverPays, amount));
                        }
                );
                respDTO.setStaffStatisticals(staffStatisticals);
                respDTOList.add(respDTO);
            }
        }
    }

    private void reserveHandle(List<HandoverReportRespDTO> respDTOList, ArrayList<HandoverPayNewDTO> settleList, Map<String, List<HandoverPayNewDTO>> groupSettleList) {
        Integer reserveCount = settleList.stream()
                .map(HandoverPayNewDTO::getReserveCount)
                .reduce(0, Integer::sum);
        if (0 != reserveCount) {
            HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
            respDTO.setStatisticalType("预订订单数");
            respDTO.setStatistical(reserveCount.toString());
            respDTO.setSort(150);
            List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
            groupSettleList.forEach((userGuid, handoverPays) -> {
                        Integer staticica = handoverPays.stream()
                                .map(HandoverPayNewDTO::getReserveCount)
                                .reduce(0, Integer::sum);
                        staffStatisticals.add(getStaffDTO(userGuid, handoverPays, staticica));
                    }
            );
            respDTO.setStaffStatisticals(staffStatisticals);
            respDTOList.add(respDTO);
        }
        BigDecimal reserveIncoming = settleList.stream()
                .map(HandoverPayNewDTO::getReserveIncoming)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (BigDecimal.ZERO.compareTo(reserveIncoming) < 0) {
            HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
            respDTO.setStatisticalType("订金金额");
            respDTO.setStatistical(MONEY_FULL + reserveIncoming.toPlainString());
            respDTO.setSort(151);
            List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
            groupSettleList.forEach((userGuid, handoverPays) -> {
                        BigDecimal amount = handoverPays.stream()
                                .map(HandoverPayNewDTO::getReserveIncoming)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        staffStatisticals.add(getStaffDTO(userGuid, handoverPays, amount));
                    }
            );
            respDTO.setStaffStatisticals(staffStatisticals);
            respDTOList.add(respDTO);
        }
        reservePayDetailListHandle(respDTOList, settleList, groupSettleList);
    }

    private void reservePayDetailListHandle(List<HandoverReportRespDTO> respDTOList, ArrayList<HandoverPayNewDTO> settleList, Map<String, List<HandoverPayNewDTO>> groupSettleList) {
        List<AmountItemDTO> reservePayDetailList = settleList.stream()
                .filter(i -> CollectionUtils.isNotEmpty(i.getReservePayDetailList()))
                .flatMap(s -> s.getReservePayDetailList().stream())
                .collect(Collectors.toList());
        Map<Integer, List<AmountItemDTO>> detailSortMap = reservePayDetailList.stream()
                .filter(i -> Objects.nonNull(i.getSort()))
                .collect(Collectors.groupingBy(AmountItemDTO::getSort));
        for (Map.Entry<Integer, List<AmountItemDTO>> entry : detailSortMap.entrySet()) {
            Integer sort = entry.getKey();
            List<AmountItemDTO> chargeList = entry.getValue();
            BigDecimal payAmount = chargeList.stream()
                    .map(AmountItemDTO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (BigDecimal.ZERO.compareTo(payAmount) != 0) {
                HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
                respDTO.setStatisticalType(chargeList.get(0).getName());
                respDTO.setStatistical(MONEY_FULL + payAmount.toPlainString());
                respDTO.setSort(sort + 152);
                List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
                groupSettleList.forEach((userGuid, handoverPays) -> {
                            BigDecimal amount = handoverPays.stream()
                                    .filter(i -> CollectionUtils.isNotEmpty(i.getReservePayDetailList()))
                                    .flatMap(s -> s.getReservePayDetailList().stream())
                                    .filter(i -> Objects.nonNull(i.getSort()) && Objects.equals(i.getSort(), sort))
                                    .map(AmountItemDTO::getAmount)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            staffStatisticals.add(getStaffDTO(userGuid, handoverPays, amount));
                        }
                );
                respDTO.setStaffStatisticals(staffStatisticals);
                respDTOList.add(respDTO);
            }
        }
    }

    private void rechargeHandel(List<HandoverReportRespDTO> respDTOList, ArrayList<HandoverPayNewDTO> settleList, Map<String, List<HandoverPayNewDTO>> groupSettleList) {
        Integer rechargeCount = settleList.stream()
                .map(HandoverPayNewDTO::getRechargeCount)
                .reduce(0, Integer::sum);
        if (0 != rechargeCount) {
            HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
            respDTO.setStatisticalType("储值订单数");
            respDTO.setStatistical(rechargeCount.toString());
            respDTO.setSort(100);
            List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
            groupSettleList.forEach((userGuid, handoverPays) -> {
                        Integer count = handoverPays.stream()
                                .map(HandoverPayNewDTO::getRechargeCount)
                                .reduce(0, Integer::sum);
                        staffStatisticals.add(getStaffDTO(userGuid, handoverPays, count));
                    }
            );
            respDTO.setStaffStatisticals(staffStatisticals);
            respDTOList.add(respDTO);
        }
        BigDecimal rechargeIncoming = settleList.stream()
                .map(HandoverPayNewDTO::getRechargeIncoming)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (BigDecimal.ZERO.compareTo(rechargeIncoming) < 0) {
            HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
            respDTO.setStatisticalType("储值金额");
            respDTO.setStatistical(MONEY_FULL + rechargeIncoming.toPlainString());
            respDTO.setSort(101);
            List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
            groupSettleList.forEach((userGuid, handoverPays) -> {
                        BigDecimal sum = handoverPays.stream()
                                .map(HandoverPayNewDTO::getRechargeIncoming)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        staffStatisticals.add(getStaffDTO(userGuid, handoverPays, sum));
                    }
            );
            respDTO.setStaffStatisticals(staffStatisticals);
            respDTOList.add(respDTO);
        }
        List<AmountItemDTO> chargeDetailList = settleList.stream()
                .filter(i -> CollectionUtils.isNotEmpty(i.getChargeDetailList()))
                .flatMap(s -> s.getChargeDetailList().stream())
                .collect(Collectors.toList());
        constructResp(respDTOList, chargeDetailList, groupSettleList);
    }

    private void payWayHandle(List<HandoverReportRespDTO> respDTOList, ArrayList<HandoverPayNewDTO> settleList, Map<String, List<HandoverPayNewDTO>> groupSettleList) {
        List<AmountItemDTO.InnerDetails> innerDetails = new ArrayList<>();
        List<AmountItemDTO> incomingList = settleList.stream()
                .filter(i -> CollectionUtils.isNotEmpty(i.getIncomingDetailList()))
                .flatMap(s -> s.getIncomingDetailList().stream())
                .collect(Collectors.toList());
        innerDetailsHandle(respDTOList, innerDetails, incomingList, groupSettleList);

        Map<String, List<AmountItemDTO>> incomingDetailNoSortMap = incomingList.stream()
                .filter(i -> Objects.isNull(i.getSort()))
                .collect(Collectors.groupingBy(AmountItemDTO::getName));
        int payNoSort = 49;
        for (Map.Entry<String, List<AmountItemDTO>> e : incomingDetailNoSortMap.entrySet()) {
            String payName = e.getKey();
            List<AmountItemDTO> incomingDetailList = e.getValue();
            BigDecimal payAmount = incomingDetailList.stream()
                    .map(AmountItemDTO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (BigDecimal.ZERO.compareTo(payAmount) < 0) {
                HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
                respDTO.setStatisticalType(payName);
                respDTO.setStatistical(MONEY_FULL + payAmount.toPlainString());
                List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
                groupSettleList.forEach((userGuid, handoverPays) -> {
                            BigDecimal amount = handoverPays.stream()
                                    .filter(i -> CollectionUtils.isNotEmpty(i.getIncomingDetailList()))
                                    .flatMap(s -> s.getIncomingDetailList().stream())
                                    .filter(amountItemDTO -> amountItemDTO.getName().equals(payName))
                                    .map(AmountItemDTO::getAmount)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            staffStatisticals.add(getStaffDTO(userGuid, handoverPays, amount));
                        }
                );
                respDTO.setStaffStatisticals(staffStatisticals);
                respDTO.setSort(payNoSort--);
                respDTOList.add(respDTO);
            }
        }
        // 第三方活动
        thirdActivityHandle(respDTOList, innerDetails, groupSettleList);
    }

    private void innerDetailsHandle(List<HandoverReportRespDTO> respDTOList, List<AmountItemDTO.InnerDetails> innerDetails,
                                    List<AmountItemDTO> incomingList, Map<String, List<HandoverPayNewDTO>> groupSettleList) {
        Map<String, List<AmountItemDTO>> incomingDetailCodeMap = incomingList.stream()
                .filter(i -> Objects.nonNull(i.getName()))
                .collect(Collectors.groupingBy(AmountItemDTO::getName));
        for (Map.Entry<String, List<AmountItemDTO>> entry : incomingDetailCodeMap.entrySet()) {
            buildIncomingDetails(entry, innerDetails, respDTOList, groupSettleList);
        }
    }

    private void buildIncomingDetails(Map.Entry<String, List<AmountItemDTO>> entry,
                                      List<AmountItemDTO.InnerDetails> innerDetails, List<HandoverReportRespDTO> respDTOList,
                                      Map<String, List<HandoverPayNewDTO>> groupSettleList) {
        List<AmountItemDTO> incomingDetailList = entry.getValue();
        String name = entry.getKey();
        BigDecimal payAmount = incomingDetailList.stream()
                .map(AmountItemDTO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal excessAmount = incomingDetailList.stream()
                .filter(e -> null != e.getExcessAmount() && BigDecimal.ZERO.compareTo(e.getExcessAmount()) < 0)
                .map(AmountItemDTO::getExcessAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        boolean isExcess = Objects.equals(BigDecimal.ZERO, payAmount) && !Objects.equals(BigDecimal.ZERO, excessAmount);
        if ((isExcess || BigDecimal.ZERO.compareTo(payAmount) != 0) && !PaymentTypeEnum.getFilterGrouponPaymentTypeNames().contains(name)) {
            StringBuilder statistical = new StringBuilder();
            statistical.append(MONEY_FULL).append(payAmount.toPlainString());
            if (!Objects.equals(BigDecimal.ZERO, excessAmount)) {
                statistical.append("(余出￥").append(excessAmount.toPlainString()).append(")");
            }
            HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
            respDTO.setStatisticalType(name);
            respDTO.setStatistical(statistical.toString());
            respDTO.setSort(PaymentType.THIRD_ACTIVITY.getId() + 10);
            respDTO.setStaffStatisticals(getHandoverReportStaffDTOS(groupSettleList, name));
            if (PaymentType.THIRD_ACTIVITY.getId() + 10 > 50) {
                respDTO.setSort(49);
            }
            if (PaymentType.THIRD_ACTIVITY.getName().equals(name)) {
                respDTO.setSort(50);
                innerDetails.addAll(incomingDetailList.stream()
                        .filter(i -> CollectionUtils.isNotEmpty(i.getInnerDetails()))
                        .flatMap(i -> i.getInnerDetails().stream())
                        .collect(Collectors.toList()));
            }
            incomingDetailList.forEach(e -> e.setSort(respDTO.getSort()));
            respDTOList.add(respDTO);
        }
    }

    private List<HandoverReportStaffDTO> getHandoverReportStaffDTOS(Map<String, List<HandoverPayNewDTO>> groupSettleList, String name) {
        List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
        groupSettleList.forEach((userGuid, handoverPays) -> {
                    BigDecimal userExcessAmount = handoverPays.stream()
                            .filter(i -> CollectionUtils.isNotEmpty(i.getIncomingDetailList()))
                            .flatMap(s -> s.getIncomingDetailList().stream())
                            .filter(i -> Objects.nonNull(i.getName()) && Objects.equals(i.getName(), name))
                            .filter(e -> null != e.getExcessAmount() && BigDecimal.ZERO.compareTo(e.getExcessAmount()) < 0)
                            .map(AmountItemDTO::getExcessAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal userPayAmount = handoverPays.stream()
                            .filter(i -> CollectionUtils.isNotEmpty(i.getIncomingDetailList()))
                            .flatMap(s -> s.getIncomingDetailList().stream())
                            .filter(i -> Objects.nonNull(i.getName()) && Objects.equals(i.getName(), name))
                            .map(AmountItemDTO::getAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    StringBuilder userStatistical = new StringBuilder();
                    if (!Objects.equals(BigDecimal.ZERO, userPayAmount)) {
                        userStatistical.append(MONEY_FULL).append(userPayAmount.toPlainString());
                    }
                    if (!Objects.equals(BigDecimal.ZERO, userExcessAmount)) {
                        userStatistical.append("(余出￥").append(userExcessAmount.toPlainString()).append(")");
                    }
                    staffStatisticals.add(new HandoverReportStaffDTO(userGuid, handoverPays.get(0).getUserName(), userStatistical.length() > 0 ? userStatistical.toString() : HANDOVER_FULL));
                }
        );
        return staffStatisticals;
    }

    private void thirdActivityHandle(List<HandoverReportRespDTO> respDTOList, List<AmountItemDTO.InnerDetails> innerDetails, Map<String, List<HandoverPayNewDTO>> groupSettleList) {
        if (CollectionUtils.isEmpty(innerDetails)) {
            return;
        }
        int activityCode = 51;
        Map<String, List<AmountItemDTO.InnerDetails>> activityTypeMap = innerDetails.stream()
                .collect(Collectors.groupingBy(AmountItemDTO.InnerDetails::getName));
        for (Map.Entry<String, List<AmountItemDTO.InnerDetails>> entry : activityTypeMap.entrySet()) {
            String typeName = entry.getKey();
            List<AmountItemDTO.InnerDetails> innerDetailList = entry.getValue();
            Map<String, List<AmountItemDTO.InnerDetails>> activityMap = innerDetailList.stream()
                    .filter(i -> CollectionUtils.isNotEmpty(i.getInnerDetails()))
                    .flatMap(i -> i.getInnerDetails().stream())
                    .collect(Collectors.groupingBy(AmountItemDTO.InnerDetails::getName));
            StringBuilder statistical = new StringBuilder();
            activityMap.forEach((name, activityList) -> {
                Integer count = activityList.stream()
                        .map(AmountItemDTO.InnerDetails::getCount)
                        .reduce(0, Integer::sum);
                BigDecimal amount = activityList.stream()
                        .map(AmountItemDTO.InnerDetails::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                statistical.append(name).append("*").append(count).append("  ￥").append(amount).append(";");
            });
            HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
            respDTO.setStatisticalType(typeName);
            respDTO.setStatistical(statistical.toString());
            respDTO.setSort(activityCode++);
            List<HandoverReportStaffDTO> staffStatisticals = getActivityStaffDTOS(groupSettleList, typeName);
            respDTO.setStaffStatisticals(staffStatisticals);
            respDTOList.add(respDTO);
        }
    }

    /**
     * 获取第三方活动员工统计详情
     *
     * @param groupSettleList 员工分组
     * @param typeName        活动名称
     * @return 员工统计详情
     */
    private List<HandoverReportStaffDTO> getActivityStaffDTOS(Map<String, List<HandoverPayNewDTO>> groupSettleList, String typeName) {
        List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
        groupSettleList.forEach((userGuid, handoverPays) -> {
                    StringBuilder staffStatistical = new StringBuilder();
                    Map<String, List<AmountItemDTO.InnerDetails>> staffActivityMap = handoverPays.stream()
                            .filter(i -> CollectionUtils.isNotEmpty(i.getIncomingDetailList()))
                            .flatMap(s -> s.getIncomingDetailList().stream())
                            .filter(i -> Objects.nonNull(i.getName()) && Objects.equals(i.getName(), PaymentType.THIRD_ACTIVITY.getName()))
                            .filter(i -> CollectionUtils.isNotEmpty(i.getInnerDetails()))
                            .flatMap(i -> i.getInnerDetails().stream())
                            .filter(i -> CollectionUtils.isNotEmpty(i.getInnerDetails()) && Objects.equals(i.getName(), typeName))
                            .flatMap(i -> i.getInnerDetails().stream())
                            .collect(Collectors.groupingBy(AmountItemDTO.InnerDetails::getName));
                    log.info("staffActivityMap ：{}", JacksonUtils.writeValueAsString(staffActivityMap));
                    staffActivityMap.forEach((name, activityList) -> {
                        Integer count = activityList.stream()
                                .map(AmountItemDTO.InnerDetails::getCount)
                                .reduce(0, Integer::sum);
                        BigDecimal amount = activityList.stream()
                                .map(AmountItemDTO.InnerDetails::getAmount)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (count > 0) {
                            staffStatistical.append(name).append("*").append(count).append("  ￥").append(amount).append(";");
                        } else {
                            staffStatistical.append(HANDOVER_FULL);
                        }
                    });
                    staffStatisticals.add(new HandoverReportStaffDTO(userGuid, handoverPays.get(0).getUserName(), staffStatistical.toString()));
                }
        );
        return staffStatisticals;
    }

    /**
     * 已交班数据
     */
    private void settleDataHandle(List<HandoverRecordDO> handoverRecordDOList, ArrayList<HandoverPayNewDTO> settleList) {
        List<HandoverRecordDO> settledList = handoverRecordDOList.stream()
                .filter(h -> 1 == h.getStatus()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(settledList)) {
            settledList.forEach(handoverRecordDO -> {
                //组装返回对象
                HandoverPayNewDTO handoverPayNewDTO = new HandoverPayNewDTO();
                handoverPayNewDTO.setStoreGuid(handoverRecordDO.getStoreGuid())
                        .setStoreName(handoverRecordDO.getStoreName())
                        .setHandoverRecordGuid(handoverRecordDO.getHandoverRecordGuid())
                        .setUserGuid(handoverRecordDO.getConfirmUserGuid())
                        .setUserName(handoverRecordDO.getConfirmUserName())
                        .setGmtCreate(handoverRecordDO.getGmtCreate())
                        .setGmtModified(handoverRecordDO.getGmtModified())
                        .setStatus(handoverRecordDO.getStatus())
                        .setHandOnCash(handoverRecordDO.getHandonCash())
                        .setRealHandOnCash(handoverRecordDO.getRealHandonCash())
                        .setSaleCount(handoverRecordDO.getCheckedCount())
                        .setSaleIncoming(handoverRecordDO.getPaymentMoney())
                        .setRechargeCount(handoverRecordDO.getChargedCount())
                        .setRechargeIncoming(handoverRecordDO.getMemberChargeMoney())
                        .setReserveCount(handoverRecordDO.getReserveCount())
                        .setReserveIncoming(handoverRecordDO.getReserveMoney())
                        .setRepaymentFeeCount(handoverRecordDO.getRepaymentFeeCount())
                        .setRepaymentFeeTotal(handoverRecordDO.getRepaymentFeeTotal())
                        .setExcessAmount(handoverRecordDO.getExcessAmount())
                ;
                //查询支付方式
                List<HandoverPayDetailDO> payDetailDOList = handoverPayDetailMapper.selectPayDetailById(
                        handoverRecordDO.getHandoverRecordGuid());
                if (!CollectionUtils.isEmpty(payDetailDOList)) {
                    //将支付方式明细按销售、储值、预订拆分
                    List<HandoverPayDetailDO> saleDetail = payDetailDOList.stream()
                            .filter(v -> v.getPayBelongType() == 1)
                            .filter(v -> Objects.isNull(v.getPayType())
                                    || StringUtils.isBlank(v.getPayType())
                                    || !PaymentTypeEnum.getFilterGrouponPaymentTypes().contains(Integer.valueOf(v.getPayType())))
                            .collect(Collectors.toList());
                    List<HandoverPayDetailDO> rechargeDetail = payDetailDOList.stream()
                            .filter(v -> v.getPayBelongType() == 2).collect(Collectors.toList());
                    List<HandoverPayDetailDO> reserveDetail = payDetailDOList.stream()
                            .filter(v -> v.getPayBelongType() == 3).collect(Collectors.toList());
                    List<HandoverPayDetailDO> repaymentDetail = payDetailDOList.stream()
                            .filter(v -> v.getPayBelongType() == 4).collect(Collectors.toList());
                    //封装支付Map
                    handoverPayNewDTO.setSaleIncomingDetail(payDetailToMap(saleDetail))
                            .setRechargeIncomingDetail(payDetailToMap(rechargeDetail))
                            .setReserveIncomingDetail(payDetailToMap(reserveDetail))
                            .setRepaymentStr(payDetailToMap(repaymentDetail))
                    ;
                    //查询门店下的支付类型排序
                    List<PaymentTypeDTO> paymentTypeDOList = paymentTypeMapper.getAll(handoverRecordDO.getStoreGuid(), 0);
                    Map<String, Integer> sortMap = paymentTypeDOList.stream().collect(
                            Collectors.toMap(PaymentTypeDTO::getPaymentTypeName, PaymentTypeDTO::getSorting));
                    //封装支付List
                    handoverPayNewDTO.setIncomingDetailList(payDetailToList(saleDetail, sortMap))
                            .setChargeDetailList(payDetailToList(rechargeDetail, sortMap))
                            .setReservePayDetailList(payDetailToList(reserveDetail, sortMap))
                            .setRepaymentList(payDetailToList(repaymentDetail, sortMap))
                    ;
                    HandoverPayQueryDTO handoverPayQueryDTO = new HandoverPayQueryDTO();
                    handoverPayQueryDTO.setUserGuid(handoverRecordDO.getCreateUserGuid());
                    handoverPayQueryDTO.setStoreGuid(handoverRecordDO.getStoreGuid());
                    handoverPayQueryDTO.setGmtCreate(handoverRecordDO.getGmtCreate());
                    handoverPayQueryDTO.setGmtModified(handoverRecordDO.getGmtModified());
                    // 特殊处理第三方活动  第三方活动金额 = 第三方支付金额 - 余出金额
                    handelThirdActivity(handoverPayNewDTO, handoverPayQueryDTO);
                    handoverPayNewDTO.setDiscountDetailList(Lists.newArrayList());
                    // 查询优惠方式统计
                    List<AmountItemDTO> discountDetails = tradingClient.handoverNewByDiscount(handoverPayQueryDTO);
                    if (CollectionUtils.isNotEmpty(discountDetails)) {
                        handoverPayNewDTO.setDiscountDetailList(discountDetails);
                    }
                } else {
                    handoverPayNewDTO.setSaleIncomingDetail(new HashMap<>()).setRechargeIncomingDetail(new HashMap<>())
                            .setReserveIncomingDetail(new HashMap<>())
                            .setIncomingDetailList(new ArrayList<>()).setChargeDetailList(new ArrayList<>())
                            .setDiscountDetailList(new ArrayList<>())
                            .setReservePayDetailList(new ArrayList<>())
                            .setRepaymentList(new ArrayList<>())
                            .setRepaymentStr(new Hashtable<>());
                }
                settleList.add(handoverPayNewDTO);
            });
        }
    }

    private void handelThirdActivity(HandoverPayNewDTO handoverPayNewDTO, HandoverPayQueryDTO handoverPayQueryDTO) {
        if (CollectionUtils.isNotEmpty(handoverPayNewDTO.getIncomingDetailList())) {
            handoverPayNewDTO.getIncomingDetailList().forEach(e -> {
                if (PaymentType.THIRD_ACTIVITY.getName().equals(e.getName())) {
                    e.setExcessAmount(handoverPayNewDTO.getExcessAmount());
                    if (Objects.nonNull(e.getAmount()) && Objects.nonNull(e.getExcessAmount())) {
                        e.setAmount(e.getAmount().subtract(e.getExcessAmount()));
                        // 销售总额 = 销售总额 - 余出
                        handoverPayNewDTO.setSaleIncoming(handoverPayNewDTO.getSaleIncoming().subtract(e.getExcessAmount()));
                    }
                    // 第三方活动平台明细
                    List<AmountItemDTO.InnerDetails> innerDetails = tradingClient.handoverNewByThirdActivity(handoverPayQueryDTO);
                    log.info("调试交接班数据,历史交接班记录:{}", JacksonUtils.writeValueAsString(innerDetails));
                    e.setInnerDetails(innerDetails);
                }
            });
        }
    }

    /**
     * 未交班数据
     */
    private void notSettleDataHandle(HandOverReportQueryDTO handOverQueryDTO, List<HandoverRecordDO> handoverRecordDOList, ArrayList<HandoverPayNewDTO> settleList) {
        List<HandoverRecordDO> notSettleList = handoverRecordDOList.stream()
                .filter(h -> 0 == h.getStatus()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notSettleList)) {
            List<String> userGuidList = notSettleList.stream()
                    .map(HandoverRecordDO::getCreateUserGuid)
                    .distinct()
                    .collect(Collectors.toList());
            List<UserDTO> userDTOList = userClientService.listUser(userGuidList);
            if (CollectionUtils.isEmpty(userDTOList)) {
                throw new BusinessException("系统员工数据异常");
            }
            Map<String, String> accountMap = userDTOList.stream()
                    .collect(Collectors.toMap(UserDTO::getGuid, UserDTO::getAccount, (v1, v2) -> v1));
            UserContext userContext = UserContextUtils.get();
            userContext.setStoreGuid(handOverQueryDTO.getStoreGuid());
            notSettleList.forEach(notSettle -> {
                // 不改下游改上游
                userContext.setAccount(accountMap.get(notSettle.getCreateUserGuid()));
                userContext.setUserGuid(notSettle.getCreateUserGuid());
                userContext.setUserName(notSettle.getCreateUserName());
                UserContextUtils.put(userContext);
                HandoverPayQueryDTO handoverPayQueryDTO = new HandoverPayQueryDTO();
                handoverPayQueryDTO.setStoreGuid(handOverQueryDTO.getStoreGuid());
                handoverPayQueryDTO.setUserGuid(notSettle.getCreateUserGuid());
                handoverPayQueryDTO.setUserName(notSettle.getCreateUserName());
                handoverPayQueryDTO.setGmtModified(LocalDateTime.now());
                HandoverPayNewDTO settleData = getSettleData(notSettle, handoverPayQueryDTO);
                settleList.add(settleData);
            });
        }
    }

    private void constructResp(List<HandoverReportRespDTO> respDTOList, List<AmountItemDTO> detailList, Map<String, List<HandoverPayNewDTO>> groupSettleList) {
        Map<Integer, List<AmountItemDTO>> detailSortMap = detailList.stream()
                .filter(i -> Objects.nonNull(i.getSort()))
                .collect(Collectors.groupingBy(AmountItemDTO::getSort));
        for (Map.Entry<Integer, List<AmountItemDTO>> entry : detailSortMap.entrySet()) {
            Integer sort = entry.getKey();
            List<AmountItemDTO> chargeList = entry.getValue();
            BigDecimal payAmount = chargeList.stream()
                    .map(AmountItemDTO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (BigDecimal.ZERO.compareTo(payAmount) != 0) {
                HandoverReportRespDTO respDTO = new HandoverReportRespDTO();
                respDTO.setStatisticalType(chargeList.get(0).getName());
                respDTO.setStatistical(MONEY_FULL + payAmount.toPlainString());
                respDTO.setSort(sort + (Integer) 102);
                List<HandoverReportStaffDTO> staffStatisticals = new ArrayList<>();
                groupSettleList.forEach((userGuid, handoverPays) -> {
                            BigDecimal amount = handoverPays.stream()
                                    .filter(i -> CollectionUtils.isNotEmpty(i.getChargeDetailList()))
                                    .flatMap(s -> s.getChargeDetailList().stream())
                                    .filter(i -> Objects.nonNull(i.getSort()) && Objects.equals(i.getSort(), sort))
                                    .map(AmountItemDTO::getAmount)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            staffStatisticals.add(getStaffDTO(userGuid, handoverPays, amount));
                        }
                );
                respDTO.setStaffStatisticals(staffStatisticals);
                respDTOList.add(respDTO);
            }
        }
    }

    @Override
    public void handleHandoverHistory(HandoverHistoryHandleDTO request) {
        // 头部企业处理
        UserContextUtils.putErp(request.getEnterpriseGuid());
        dynamicHelper.changeDatasource(request.getEnterpriseGuid());
        // 根据企业guid查询企业未交班数据
        HandOverQueryBO queryBO = buildHandOverQueryBO(request);
        List<HandoverRecordDO> handoverRecordDOList = handoverRecordMapper.queryByCondition(queryBO);
        if (CollectionUtils.isEmpty(handoverRecordDOList)) {
            log.warn("企业未交班数据为空,queryBO={}", JacksonUtils.writeValueAsString(queryBO));
            return;
        }
        // 查询员工数据
        List<String> userGuidList = handoverRecordDOList.stream()
                .map(HandoverRecordDO::getCreateUserGuid)
                .distinct()
                .collect(Collectors.toList());
        List<UserDTO> userDTOList = userClientService.listUser(userGuidList);
        Map<String, String> accountMap = userDTOList.stream()
                .collect(Collectors.toMap(UserDTO::getGuid, UserDTO::getAccount, (v1, v2) -> v1));

        // 异步调用
        CountDownLatch countDownLatch = new CountDownLatch(handoverRecordDOList.size());
        ExecutorService executor = Executors.newFixedThreadPool(handoverRecordDOList.size());
        handoverRecordDOList.forEach(handover -> {
            executor.execute(() -> {
                try {
                    log.info("开始处理交接班历史数据,handover={}", JacksonUtils.writeValueAsString(handover));
                    confirmHistory(request, accountMap, handover);
                } catch (Exception e) {
                    log.error("处理交接班历史数据失败,handover={},e={}", JacksonUtils.writeValueAsString(handover), e.getMessage());
                } finally {
                    countDownLatch.countDown();
                }
            });
        });
        try {
            countDownLatch.await();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            executor.shutdown();
        }
        log.info("处理交接班历史数据执行完毕");
    }

    @Override
    public List<HandoverRecordDTO> queryOnDutyStaffs(String storeGuid) {
        HandOverQueryBO query = new HandOverQueryBO();
        query.setStoreGuid(storeGuid);
        query.setState(0);
        query.setBusinessStartDateTime(LocalDateTime.now().minusMonths(6));
        List<HandoverRecordDO> unHandoverRecordList = handoverRecordMapper.queryByCondition(query);
        log.info("查询未交班数据 unHandoverRecordList:{}", JacksonUtils.writeValueAsString(unHandoverRecordList));
        return HandoverRecordMapstruct.INSTANCE.toHandoverRecordDTOS(unHandoverRecordList);
    }

    private void confirmHistory(HandoverHistoryHandleDTO request, Map<String, String> accountMap, HandoverRecordDO handover) {
        // 头部处理
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid(request.getEnterpriseGuid());
        userContext.setUserGuid(handover.getCreateUserGuid());
        userContext.setUserName(handover.getCreateUserName());
        userContext.setAccount(accountMap.get(handover.getCreateUserGuid()));
        userContext.setStoreGuid(handover.getStoreGuid());
        userContext.setStoreName(handover.getStoreName());
        UserContextUtils.put(userContext);
        dynamicHelper.changeDatasource(request.getEnterpriseGuid());

        // 交接班
        log.info("数据处理-交接班头部,userContext={}", JacksonUtils.writeValueAsString(userContext));

        //交接班数据查询对象
        HandoverPayQueryDTO payQueryDTO = new HandoverPayQueryDTO();
        payQueryDTO.setStoreGuid(handover.getStoreGuid());
        payQueryDTO.setUserGuid(handover.getCreateUserGuid());
        payQueryDTO.setGmtCreate(handover.getGmtCreate());
        payQueryDTO.setGmtModified(LocalDateTime.now());
        HandoverPayNewDTO handoverPayNewDTO = getSettleData(handover, payQueryDTO);

        // 设置交接班状态(1=已交班)、笔数金额、该班次下的支付详情等并更新数据库
        handover.setPaymentCount(handoverPayNewDTO.getSaleCount())
                .setChargedCount(handoverPayNewDTO.getRechargeCount())
                .setCheckedCount(handoverPayNewDTO.getSaleCount())
                .setReserveCount(handoverPayNewDTO.getReserveCount())
                .setPaymentMoney(handoverPayNewDTO.getSaleIncoming())
                .setMemberChargeMoney(handoverPayNewDTO.getRechargeIncoming())
                .setReserveMoney(handoverPayNewDTO.getReserveIncoming())
                .setHandonCash(handoverPayNewDTO.getHandOnCash())
                .setRealHandonCash(handoverPayNewDTO.getRealHandOnCash())
                .setBusinessInComing(handoverPayNewDTO.getSaleIncoming().add(handoverPayNewDTO.getReserveIncoming()))
                .setStatus(1)
                .setGmtModified(DateTimeUtils.now())
                .setConfirmUserName(UserContextUtils.getUserName())
                .setConfirmUserGuid(UserContextUtils.getUserGuid())
                .setRepaymentFeeCount(handoverPayNewDTO.getRepaymentFeeCount())
                .setRepaymentFeeTotal(handoverPayNewDTO.getRepaymentFeeTotal())
                .setExcessAmount(handoverPayNewDTO.getExcessAmount())
                .setRelationRecordGuid(handover.getHandoverRecordGuid())
                .setIsMultiHandover(0)
        ;
        //交接班记录更新
        log.info("数据处理-交班更新参数:{}", JacksonUtils.writeValueAsString(handover));
        if (handoverRecordMapper.update(handover) == 0) {
            throw new BusinessException("数据处理-修改交接班记录失败");
        }
        //处理handover_pay_detail表入库数据
        if (!handoverPayNewDTO.getSaleIncomingDetail().isEmpty()) {
            insertHandoverPayDetail(handoverPayNewDTO.getSaleIncomingDetail(), handover, 1);
        }
        if (!handoverPayNewDTO.getRechargeIncomingDetail().isEmpty()) {
            insertHandoverPayDetail(handoverPayNewDTO.getRechargeIncomingDetail(), handover, 2);
        }
        if (!handoverPayNewDTO.getReserveIncomingDetail().isEmpty()) {
            insertHandoverPayDetail(handoverPayNewDTO.getReserveIncomingDetail(), handover, 3);
        }
        if (!handoverPayNewDTO.getRepaymentList().isEmpty()) {
            insertHandoverPayDetail(handoverPayNewDTO.getRepaymentStr(), handover, 4);
        }

        // 新开班（设备推送是新做的功能，不能保证客户一定更新了设备；其次如果同一设备有不同的用户使用，在处理历史数据时会导致正在使用的用户突然被弹出去）
        if (request.getNewHandover()) {
            HandoverRecordCreateDTO createDTO = new HandoverRecordCreateDTO();
            createDTO.setStoreGuid(handover.getStoreGuid());
            createDTO.setStoreName(handover.getStoreName());
            createDTO.setCreateUserGuid(handover.getCreateUserGuid());
            createDTO.setCreateUserName(handover.getCreateUserName());
            createDTO.setTerminalId(handover.getTerminalId());

            log.info("数据处理-新开班入参,createDTO={}", JacksonUtils.writeValueAsString(createDTO));
            this.create(createDTO);
        }
    }

    private HandOverQueryBO buildHandOverQueryBO(HandoverHistoryHandleDTO request) {
        HandOverQueryBO queryBO = new HandOverQueryBO();
        queryBO.setState(0);
        // 处理当前时间一天前的所有未交班数据
        queryBO.setBusinessEndDateTime(LocalDateTime.now().minusDays(1));
        if (!ObjectUtils.isEmpty(request.getBusinessEndDateTime())) {
            queryBO.setBusinessEndDateTime(request.getBusinessEndDateTime());
        }
        if (!ObjectUtils.isEmpty(request.getBusinessStartDateTime())) {
            queryBO.setBusinessStartDateTime(request.getBusinessStartDateTime());
        }
        if (!ObjectUtils.isEmpty(request.getStoreGuid())) {
            queryBO.setStoreGuid(request.getStoreGuid());
        }
        if (!ObjectUtils.isEmpty(request.getCount())) {
            queryBO.setCount(request.getCount());
        }
        return queryBO;
    }

    /**
     * 预定交接班
     */
    private ReserveHandoverDTO reserveHandover(HandoverPayQueryDTO handoverPayQueryDTO) {
        ReserveHandoverDTO reserveHandoverDTO = reserveClient.handover(handoverPayQueryDTO);
        if (reserveHandoverDTO == null) {
            reserveHandoverDTO = new ReserveHandoverDTO();
            reserveHandoverDTO.setReserveCashAmount(BigDecimal.ZERO).setReserveAmount(BigDecimal.ZERO)
                    .setReserveCount(0).setReservePayDetailMap(new HashMap<>())
                    .setReservePayDetailList(new ArrayList<>());
        }
        return reserveHandoverDTO;
    }


    /**
     * 交易交接班
     */
    private HandoverPayDTO tradeHandover(HandoverRecordDO handoverRecordDO, HandoverPayQueryDTO handoverPayQueryDTO) {
        HandoverPayDTO handoverPayDTO = tradingClient.handoverNew(handoverPayQueryDTO);
        log.info("查询交接班数据,trade服务查询返回:{}", JacksonUtils.writeValueAsString(handoverPayDTO));
        if (handoverPayDTO == null) {
            handoverPayDTO = new HandoverPayDTO();
            handoverPayDTO.setCheckedCount(0).setBusinessIncoming(BigDecimal.ZERO).setPaymentCount(0)
                    .setRepaymentFeeCount(0)
                    .setChargeIncoming(BigDecimal.ZERO).setChargedCount(0).setPaymentMoney(BigDecimal.ZERO)
                    .setSaleIncoming(BigDecimal.ZERO).setUserGuid(handoverRecordDO.getCreateUserGuid())
                    .setUserName(handoverRecordDO.getCreateUserName()).setGmtCreate(handoverRecordDO.getGmtCreate())
                    .setGmtModified(LocalDateTime.now()).setIncomingDetailStr(new HashMap<>()).setIncomingDetail(new HashMap<>())
                    .setSaleCash(BigDecimal.ZERO).setChargeCash(BigDecimal.ZERO).setChargeDetailStr(new HashMap<>())
                    .setIncomingDetailList(new ArrayList<>()).setChargeDetailList(new ArrayList<>())
                    .setTraffic(0).setTotalSeats(0).setOccupancyRatePercent("0%").setTableUseCount(0)
                    .setTableCount(0).setOpenTableRatePercent("0%").setFlipTableRatePercent("0%").setTotalDineInTime(0L)
                    .setAvgDineInTime(0).setSaleAmount(BigDecimal.ZERO).setDiscountAmount(BigDecimal.ZERO)
                    .setRefundAmount(BigDecimal.ZERO);
        }
        // 超出金额(第三方活动使用)
        if (Objects.isNull(handoverPayDTO.getIncomingDetailList())) {
            handoverPayDTO.setIncomingDetailList(Lists.newArrayList());
        }
        return handoverPayDTO;
    }

    /**
     * 外卖交接班
     */
    private HandoverPayDTO takeawayHandover(HandoverPayQueryDTO handoverPayQueryDTO, HandoverPayDTO handoverPayDTO) {
        HandoverPayDTO takeAway = takeAwayClientService.getTakeAway(handoverPayQueryDTO);
        log.info("交班时调用外卖获取班次内交易信息，入参为：{}，返回结果为：{}",
                JacksonUtils.writeValueAsString(handoverPayQueryDTO), JacksonUtils.writeValueAsString(takeAway));
        if (takeAway != null) {
            Integer checkedCount = Optional.ofNullable(takeAway.getCheckedCount()).orElse(0);
            BigDecimal saleIncoming = Optional.ofNullable(takeAway.getSaleIncoming()).orElse(BigDecimal.ZERO);
            handoverPayDTO.setPaymentCount(handoverPayDTO.getPaymentCount() == null ?
                    checkedCount : handoverPayDTO.getPaymentCount() + checkedCount);
            handoverPayDTO.setCheckedCount(handoverPayDTO.getCheckedCount() == null ?
                    checkedCount : handoverPayDTO.getCheckedCount() + checkedCount);

            handoverPayDTO.setSaleIncoming(handoverPayDTO.getSaleIncoming() == null ?
                    saleIncoming : handoverPayDTO.getSaleIncoming().add(saleIncoming));

            handoverPayDTO.setBusinessIncoming(handoverPayDTO.getBusinessIncoming() == null ?
                    saleIncoming : handoverPayDTO.getBusinessIncoming().add(saleIncoming));

            handoverPayDTO.setPaymentMoney(handoverPayDTO.getPaymentMoney() == null ?
                    saleIncoming : handoverPayDTO.getPaymentMoney().add(saleIncoming));
            if (null != takeAway.getIncomingDetail()) {
                handoverPayDTO.getIncomingDetail().putAll(takeAway.getIncomingDetail());
            }
            if (null != takeAway.getIncomingDetailStr()) {
                handoverPayDTO.getIncomingDetailStr().putAll(takeAway.getIncomingDetailStr());
            }
        }
        return takeAway;
    }


    private void handoverPaySorted(HandoverRecordDO handoverRecordDO, ReserveHandoverDTO reserveHandoverDTO,
                                   HandoverPayDTO handoverPayDTO, HandoverPayDTO takeAwayHandoverPayDTO) {
        List<PaymentTypeDTO> paymentTypeDOList = paymentTypeMapper.getAll(handoverRecordDO.getStoreGuid(), 0);
        log.info("门店下支付类型：{}", JacksonUtils.writeValueAsString(paymentTypeDOList));
        Map<String, Integer> sortMap = paymentTypeDOList.stream().collect(
                Collectors.toMap(PaymentTypeDTO::getPaymentTypeName, PaymentTypeDTO::getSorting));
        log.info("支付名称排序Map:{}", JacksonUtils.writeValueAsString(sortMap));
        //排序字段赋值并根据此排序
        if (!CollectionUtils.isEmpty(handoverPayDTO.getIncomingDetailList())) {
            log.info("销售收入详情排序前：{}", JacksonUtils.writeValueAsString(handoverPayDTO.getIncomingDetailList()));
            handoverPayDTO.getIncomingDetailList().forEach(v ->
                    v.setSort(Optional.ofNullable(sortMap.get(v.getName())).orElse(99)));
            handoverPayDTO.getIncomingDetailList().sort(Comparator.comparing(AmountItemDTO::getSort));
            log.info("销售收入详情排序后：{}", JacksonUtils.writeValueAsString(handoverPayDTO.getIncomingDetailList()));
        }
        if (!CollectionUtils.isEmpty(handoverPayDTO.getChargeDetailList())) {
            log.info("充值收入详情排序前：{}", JacksonUtils.writeValueAsString(handoverPayDTO.getChargeDetailList()));
            handoverPayDTO.getChargeDetailList().forEach(v ->
                    v.setSort(Optional.ofNullable(sortMap.get(v.getName())).orElse(99)));
            handoverPayDTO.getChargeDetailList().sort(Comparator.comparing(AmountItemDTO::getSort));
            log.info("充值收入详情排序后：{}", JacksonUtils.writeValueAsString(handoverPayDTO.getChargeDetailList()));
        }
        if (!CollectionUtils.isEmpty(reserveHandoverDTO.getReservePayDetailList())) {
            log.info("预订收入详情排序前：{}", JacksonUtils.writeValueAsString(reserveHandoverDTO.getReservePayDetailList()));
            reserveHandoverDTO.getReservePayDetailList().forEach(v ->
                    v.setSort(Optional.ofNullable(sortMap.get(v.getName())).orElse(99)));
            reserveHandoverDTO.getReservePayDetailList().sort(Comparator.comparing(AmountItemDTO::getSort));
            log.info("预订收入详情排序后：{}", JacksonUtils.writeValueAsString(reserveHandoverDTO.getReservePayDetailList()));
        }
        if (!CollectionUtils.isEmpty(handoverPayDTO.getRepaymentList())) {
            log.info("挂账还款收入详情排序前：{}", JacksonUtils.writeValueAsString(handoverPayDTO.getRepaymentList()));
            handoverPayDTO.getRepaymentList().forEach(v ->
                    v.setSort(Optional.ofNullable(sortMap.get(v.getName())).orElse(99)));
            handoverPayDTO.getRepaymentList().sort(Comparator.comparing(AmountItemDTO::getSort));
            log.info("挂账还款收入详情排序后：{}", JacksonUtils.writeValueAsString(handoverPayDTO.getRepaymentList()));
        }
        //外卖销售数据放到最后
        if (takeAwayHandoverPayDTO != null) {
            Map<String, BigDecimal> takeAwayMap = takeAwayHandoverPayDTO.getIncomingDetailStr();
            if (MapUtils.isNotEmpty(takeAwayMap)) {
                for (Map.Entry<String, BigDecimal> entry : takeAwayMap.entrySet()) {
                    AmountItemDTO amount = new AmountItemDTO();
                    amount.setName(entry.getKey()).setAmount(entry.getValue());
                    handoverPayDTO.getIncomingDetailList().add(amount);
                }
            }
        }
    }

    /**
     * 构建当班数据
     */
    private HandoverPayNewDTO buildHandoverPayNewDTO(HandoverRecordDO handoverRecordDO, ReserveHandoverDTO reserveHandoverDTO,
                                                     HandoverPayDTO handoverPayDTO) {
        //组装返回数据
        HandoverPayNewDTO handoverPayNewDTO = new HandoverPayNewDTO();
        AmountItemDTO hasExcessAmount = handoverPayDTO.getIncomingDetailList().stream()
                .filter(e -> Objects.nonNull(e.getExcessAmount()) && e.getExcessAmount().compareTo(BigDecimal.ZERO) > 0)
                .findFirst().orElse(new AmountItemDTO());
        handoverPayNewDTO.setSaleCount(handoverPayDTO.getCheckedCount()).setSaleIncoming(handoverPayDTO.getSaleIncoming())
                .setSaleIncomingDetail(handoverPayDTO.getIncomingDetailStr())
                .setRechargeCount(handoverPayDTO.getChargedCount()).setRechargeIncoming(handoverPayDTO.getChargeIncoming())
                .setRechargeIncomingDetail(handoverPayDTO.getChargeDetailStr())
                .setReserveCount(reserveHandoverDTO.getReserveCount()).setReserveIncoming(reserveHandoverDTO.getReserveAmount())
                .setReserveIncomingDetail(reserveHandoverDTO.getReservePayDetailMap())
                .setHandOnCash(reserveHandoverDTO.getReserveCashAmount()
                        .add(handoverPayDTO.getChargeCash()).add(handoverPayDTO.getSaleCash()))
                .setUserName(handoverRecordDO.getCreateUserName())
                .setUserGuid(handoverRecordDO.getCreateUserGuid())
                .setGmtCreate(handoverRecordDO.getGmtCreate()).setGmtModified(LocalDateTime.now())
                .setHandoverRecordGuid(handoverRecordDO.getHandoverRecordGuid())
                .setStoreGuid(handoverRecordDO.getStoreGuid()).setStoreName(handoverRecordDO.getStoreName())
                .setIncomingDetailList(handoverPayDTO.getIncomingDetailList())
                .setDiscountDetailList(handoverPayDTO.getDiscountDetailList())
                .setChargeDetailList(handoverPayDTO.getChargeDetailList())
                .setReservePayDetailList(reserveHandoverDTO.getReservePayDetailList())
                .setRepaymentFeeTotal(handoverPayDTO.getRepaymentFeeTotal())
                .setRepaymentList(handoverPayDTO.getRepaymentList())
                .setRepaymentFeeCount(handoverPayDTO.getRepaymentFeeCount())
                .setRepaymentStr(handoverPayDTO.getRepaymentStr())
                .setExcessAmount(hasExcessAmount.getExcessAmount())
                .setTraffic(handoverPayDTO.getTraffic()).setTotalSeats(handoverPayDTO.getTotalSeats())
                .setOccupancyRatePercent(handoverPayDTO.getOccupancyRatePercent())
                .setTableUseCount(handoverPayDTO.getTableUseCount()).setTableCount(handoverPayDTO.getTableCount())
                .setOpenTableRatePercent(handoverPayDTO.getOpenTableRatePercent())
                .setFlipTableRatePercent(handoverPayDTO.getFlipTableRatePercent())
                .setTotalDineInTime(handoverPayDTO.getTotalDineInTime())
                .setAvgDineInTime(handoverPayDTO.getAvgDineInTime())
                .setSaleAmount(handoverPayDTO.getSaleAmount())
                .setDiscountAmount(handoverPayDTO.getDiscountAmount())
                .setRefundAmount(handoverPayDTO.getRefundAmount());
        return handoverPayNewDTO;
    }
}