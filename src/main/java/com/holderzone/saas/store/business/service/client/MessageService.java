package com.holderzone.saas.store.business.service.client;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
 * <AUTHOR>
 * @description 消息服务客户端
 * @date 2021/10/18 11:44
 * @className: MessageService
 */
@Component
@FeignClient(name = "holder-saas-store-message", fallbackFactory = MessageService.ServiceFallBack.class)
public interface MessageService {

    /**
     * 推送消息至安卓端
     *
     * @param businessMessageDTO BusinessMessageDTO
     * @return 返回success，表示推送成功
     */
    @ApiOperation(value = "接受消息的接口", notes = "返回success，表示推送成功")
    @PostMapping("/msg")
    String msg(BusinessMessageDTO businessMessageDTO);

    @ApiOperation(value = "批量接受消息", notes = "返回success，表示推送成功")
    @PostMapping("/all/msg")
    String allMsg(@RequestBody List<BusinessMessageDTO> businessMessageDTOS);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MessageService> {

        @Override
        public MessageService create(Throwable throwable) {
            return new MessageService() {
                @Override
                public String msg(BusinessMessageDTO businessMessageDTO) {
                    log.error("推送消息至安卓失败" + throwable.getMessage());
                    throw new BusinessException("推送消息至安卓失败" + throwable.getMessage());
                }

                @Override
                public String allMsg(List<BusinessMessageDTO> businessMessageDTOS) {
                    log.error("批量推送消息至安卓失败" + throwable.getMessage());
                    throw new BusinessException("批量推送消息至安卓失败" + throwable.getMessage());
                }
            };
        }
    }
}