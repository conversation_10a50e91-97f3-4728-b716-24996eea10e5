package com.holderzone.saas.store.business.mapstruct;

import com.holderzone.saas.store.business.entity.domain.CashboxRecordDO;
import com.holderzone.saas.store.dto.business.manage.CashboxRecordCreateDTO;
import com.holderzone.saas.store.dto.business.manage.CashboxRecordDTO;
import com.holderzone.saas.store.dto.business.manage.CashboxRecordListDTO;
import com.holderzone.saas.store.dto.business.manage.CashboxRecordQueryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CashboxRecordMapstruct
 * @date 2018/07/29 下午6:23
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Mapper
public interface CashboxRecordMapstruct {

    CashboxRecordMapstruct INSTANCE = Mappers.getMapper(CashboxRecordMapstruct.class);

    CashboxRecordDO toCashboxRecordDO(CashboxRecordCreateDTO cashboxRecordCreateDTO);

    CashboxRecordDO toCashboxRecordDO(CashboxRecordQueryDTO cashboxRecordQueryDTO);

    CashboxRecordDO toCashboxRecordDO(CashboxRecordListDTO cashboxRecordListDTO);

    CashboxRecordDTO toCashboxRecordDTO(CashboxRecordDO cashboxRecordDO);

    List<CashboxRecordDTO> toCashboxRecordDTOS(List<CashboxRecordDO> cashboxRecordDOS);
}
