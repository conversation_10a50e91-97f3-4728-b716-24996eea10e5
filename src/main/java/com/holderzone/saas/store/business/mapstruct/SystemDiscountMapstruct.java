package com.holderzone.saas.store.business.mapstruct;

import com.holderzone.saas.store.business.entity.domain.SystemDiscountDO;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemDiscountMapstruct
 * @date 2019/10/30 21:45
 * @description
 * @program holder-saas-store
 */
@Mapper
@Component
public interface SystemDiscountMapstruct {
    SystemDiscountMapstruct INSTANT = Mappers.getMapper(SystemDiscountMapstruct.class);

    SystemDiscountDTO do2DTO(SystemDiscountDO systemDiscountDO);

    List<SystemDiscountDTO> doList2DTOList(List<SystemDiscountDO> discountDOList);

    SystemDiscountDO dto2DO(SystemDiscountDTO systemDiscountDTO);
}
