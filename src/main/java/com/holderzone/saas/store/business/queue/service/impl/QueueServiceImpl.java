package com.holderzone.saas.store.business.queue.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.saas.store.business.queue.constant.QueueConst;
import com.holderzone.saas.store.business.queue.domain.*;
import com.holderzone.saas.store.business.queue.mapper.QueueConfigMapper;
import com.holderzone.saas.store.business.queue.mapper.QueueItemMapper;
import com.holderzone.saas.store.business.queue.mapper.QueueMapper;
import com.holderzone.saas.store.business.queue.mapper.QueueTableMapper;
import com.holderzone.saas.store.business.queue.mapstruct.QueueItemMapStruct;
import com.holderzone.saas.store.business.queue.mapstruct.QueueMapStruct;
import com.holderzone.saas.store.business.queue.service.QueueConfigService;
import com.holderzone.saas.store.business.queue.service.QueueItemService;
import com.holderzone.saas.store.business.queue.service.QueueService;
import com.holderzone.saas.store.business.queue.service.remote.BusinessMsgClient;
import com.holderzone.saas.store.business.queue.service.remote.OrganizationClientService;
import com.holderzone.saas.store.business.queue.service.remote.TableClientService;
import com.holderzone.saas.store.business.queue.utils.QueuedUtils;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.queue.*;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueServiceImpl
 * @date 2019/03/27 17:21
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Service
@Slf4j
public class QueueServiceImpl extends ServiceImpl<QueueMapper, HolderQueueDO> implements QueueService {
    @Autowired
    protected TransactionTemplate transactionTemplate;
    private final QueueMapStruct queueMapStruct;
    private final QueueItemMapStruct queueItemMapStruct;
    private final QueueItemMapper queueItemMapper;
    private final QueueItemService queueItemService;
    @Autowired
    private QueueConfigService queueConfigService;
    @Autowired
    private QueueTableMapper queueTableMapper;
    @Autowired
    private BusinessMsgClient businessMsgClient;
    @Autowired
    private QueueConfigMapper configMapper;
    @Autowired
    private QueueTableServiceImpl tableService;
    @Autowired
    private TableClientService tableClientService;
    @Autowired
    private OrganizationClientService organizationClientService;

    public QueueServiceImpl(QueueMapStruct queueMapStruct, QueueItemMapStruct queueItemMapStruct, QueueItemMapper queueItemMapper, QueueItemService queueItemService) {
        this.queueMapStruct = queueMapStruct;
        this.queueItemMapStruct = queueItemMapStruct;
        this.queueItemMapper = queueItemMapper;
        this.queueItemService = queueItemService;
    }

    public <T> T doinTransaction(Supplier<T> supplier) {
        return transactionTemplate.execute((transactionStatus) -> {
            try {
                return supplier.get();
            } catch (Exception e) {
                log.error("fail to excuete biz: ", e);
                transactionStatus.setRollbackOnly();
                throw e;
            }
        });

    }

    @Override
    public String save(HolderQueueDTO dto) {
        UserContext userContext = UserContextUtils.get();
        if (!RedissonLockUtil.tryLock("queue:store:lock:" + dto.getStoreGuid(), 3, 3)) {
            throw new BusinessException("系统繁忙请稍候重试");
        }
        try {
            HolderQueueDO d = doinTransaction(() -> {
                // 人数区间 验证
                Assert.isTrue(dto.getMax() == null || dto.getMax() > dto.getMin(), "人数区间设置不正确!");
                // code 重复校验
                Integer count = baseMapper.selectCount(
                        new LambdaQueryWrapper<HolderQueueDO>()
                                .eq(HolderQueueDO::getStoreGuid, dto.getStoreGuid())
                                .eq(HolderQueueDO::getCode, dto.getCode())
                                .ne(Optional.ofNullable(dto.getGuid()).isPresent(), HolderQueueDO::getGuid, dto.getGuid())
                );
                if (count > 0) {
                    throw new BusinessException("队列编码 重复" + dto.getCode());
                }

                // 名称重复校验
                Integer nameCount = baseMapper.selectCount(
                        new LambdaQueryWrapper<HolderQueueDO>()
                                .eq(HolderQueueDO::getStoreGuid, dto.getStoreGuid())
                                .eq(HolderQueueDO::getName, dto.getName())
                                .ne(Optional.ofNullable(dto.getGuid()).isPresent(), HolderQueueDO::getGuid, dto.getGuid())
                );
                if (nameCount > 0) {
                    throw new BusinessException("队列名称 重复" + dto.getName());
                }
                // 类型转换
                HolderQueueDO queueDO = queueMapStruct.toDo(dto);
                queueDO.setMax(dto.getMax());
                // 逻辑派发
                if (StringUtils.hasText(queueDO.getGuid())) {
                    //修改
                    HolderQueueDO db = baseMapper.selectOne(new LambdaQueryWrapper<HolderQueueDO>()
                            .eq(HolderQueueDO::getGuid, queueDO.getGuid())
                    );
                    if (db == null || db.getIsDeleted()) {
                        throw new BusinessException("had_deleted");
                    }
                    //人数区间是否修改
                    if (Optional.ofNullable(queueDO.getMin()).orElse(Byte.MIN_VALUE).byteValue() != Optional.ofNullable(db.getMin()).orElse(Byte.MIN_VALUE).byteValue()
                            || Optional.ofNullable(queueDO.getMax()).orElse(Byte.MIN_VALUE).byteValue() != Optional.ofNullable(db.getMax()).orElse(Byte.MIN_VALUE).byteValue()) {
                        Integer bigCount = baseMapper.selectCount(
                                new LambdaQueryWrapper<HolderQueueDO>()
                                        .eq(HolderQueueDO::getStoreGuid, queueDO.getStoreGuid())
                                        .ge(dto.getMax() != null, HolderQueueDO::getMin, queueDO.getMin())
                                        .ge(dto.getMax() == null, HolderQueueDO::getMax, queueDO.getMin())
                                        .ne(HolderQueueDO::getGuid, db.getGuid())
                        );
                        Assert.isTrue(bigCount == 0, "只能编辑最后一条队列");
                    }
//                    int itemNums = queueItemMapper.selectCount(new LambdaQueryWrapper<HolderQueueItemDO>().eq(HolderQueueItemDO::getQueueGuid, queueDO.getGuid()));
//
//                    Assert.isTrue(itemNums == 0, "只能修改非空队列");
                    queueDO.setGmtModified(LocalDateTime.now());
                    queueDO.setModifiedStaffGuid(userContext.getUserGuid());
                    update(queueDO, new LambdaQueryWrapper<HolderQueueDO>().eq(HolderQueueDO::getGuid, queueDO.getGuid()));
                } else {
                    Integer bigCount = baseMapper.selectCount(
                            new LambdaQueryWrapper<HolderQueueDO>()
                                    .eq(HolderQueueDO::getStoreGuid, queueDO.getStoreGuid())
                                    .nested((e) -> e.ge(HolderQueueDO::getMax, queueDO.getMin()).or().isNull(HolderQueueDO::getMax))
                    );
                    Assert.isTrue(bigCount == 0, "新增队列最小人数必须大于前一个队列的最大人数");
                    //插入
                    queueDO.setGmtCreate(LocalDateTime.now());
                    queueDO.setGuid(QueuedUtils.nextTypeGuid());
                    queueDO.setIsDeleted(false);
                    queueDO.setIsEnable(true);
                    queueDO.setCreateStaffGuid(userContext.getUserGuid());
                    save(queueDO);
                }
                return queueDO;
            });
            sendMessage(d.getStoreGuid(), null);
            return d.getGuid();
        } catch (Exception e) {
            log.error("fail to save queue ", e);
            throw e;
        } finally {
            RedissonLockUtil.unlock("queue:store:lock:" + dto.getStoreGuid());
        }
    }

    @Override
    public Boolean enable(String guid) {
        return enable(guid, true);
    }

    private Boolean enable(String guid, boolean b) {
        HolderQueueDO queueDO = new HolderQueueDO().setIsEnable(b);
        this.update(queueDO, new UpdateWrapper<HolderQueueDO>().lambda().eq(HolderQueueDO::getGuid, guid));
        return true;
    }

    @Override
    public Boolean disable(String guid) {
        return enable(guid, true);
    }

    @Override
    public Boolean delete(String guid) {
        HolderQueueDO holderQueueDO = baseMapper.selectOne(new LambdaQueryWrapper<HolderQueueDO>().eq(HolderQueueDO::getGuid, guid));
        doinTransaction(() -> {
            if (StringUtils.isEmpty(guid)) {
                throw new BusinessException("队列不存在");
            }
            int itemNums = queueItemMapper.selectCount(new LambdaQueryWrapper<HolderQueueItemDO>().eq(HolderQueueItemDO::getQueueGuid, holderQueueDO.getGuid()));
            Assert.isTrue(itemNums == 0, "只能删除非空队列");
            this.remove(new LambdaQueryWrapper<HolderQueueDO>().eq(HolderQueueDO::getGuid, guid));
            queueItemService.clean(Arrays.asList(holderQueueDO));
            queueTableMapper.delete(new LambdaQueryWrapper<HolderQueueTableDO>().select(HolderQueueTableDO::getTableGuid).eq(HolderQueueTableDO::getQueueGuid, guid));
            return null;
        });
        sendMessage(holderQueueDO.getStoreGuid(), null);
        return true;
    }

    @Override
    public HolderQueueDTO fetchOne(String queueGuid) {
        HolderQueueDO d = baseMapper.selectOne(new LambdaQueryWrapper<HolderQueueDO>().eq(HolderQueueDO::getGuid, queueGuid));
        if (d == null) {
            throw new BusinessException("该队列已删除，请刷新页面");
        }
        return queueMapStruct.toDto(d);
    }

    @Override
    public Boolean remove(List<String> guids) {
        Assert.notEmpty(guids, "请指定需要清空的队列");
        queueItemMapper.delete(new LambdaQueryWrapper<HolderQueueItemDO>().in(HolderQueueItemDO::getQueueGuid, guids));
        return true;
    }

    @Override
    public Boolean removeAll() {
        UserContext userContext = UserContextUtils.get();
        String storeGuid = userContext.getStoreGuid();
        StoreConfigDTO storeConfigDTO = queueConfigService.obtain(storeGuid);
        if (storeConfigDTO == null) {
            storeConfigDTO = StoreConfigDTO.DEFAULT.build(storeGuid);
        }
        if (!storeConfigDTO.getIsEnableManualReset()) {
            throw new BusinessException("该门店不允许手动清空队列");
        }
        clean(storeGuid);
        return true;
    }

    @Override
    public void clean(String storeGuid) {
        List<HolderQueueDO> list = baseMapper.selectList(new LambdaQueryWrapper<HolderQueueDO>().select(HolderQueueDO::getGuid).eq(HolderQueueDO::getStoreGuid, storeGuid));
        queueItemMapper.delete(new LambdaQueryWrapper<HolderQueueItemDO>().eq(HolderQueueItemDO::getStoreGuid, storeGuid));
        queueItemService.clean(list);
        sendMessage(storeGuid, null);
    }

    @Override
    public List<String> availableCode() {
        UserContext userContext = UserContextUtils.get();
        String storeGuid = userContext.getStoreGuid();
        List<HolderQueueDO> cdos = baseMapper.selectList(new LambdaQueryWrapper<HolderQueueDO>()
                .select(HolderQueueDO::getCode)
                .eq(HolderQueueDO::getStoreGuid, storeGuid)
        );
        List<String> codeInUse = cdos.stream().map(HolderQueueDO::getCode).collect(Collectors.toList());
        // 从内置Code中排除已使用Code，然后返回
        return QueueConst.QUEUE_CODE.stream().filter(s -> !codeInUse.contains(s)).collect(Collectors.toList());
    }

    public void sendMessage(String storeGuid, String storeName) {
        BusinessMessageDTO messageDTO = BusinessMessageDTO.builder()
                .subject(BusinessMsgTypeEnum.QUEUE_CHANGED_MSG_TYPE.getName())
                //TODO message
                .messageType(BusinessMsgTypeEnum.QUEUE_CHANGED_MSG_TYPE.getId())
                .detailMessageType(BusinessMsgTypeEnum.QUEUE_CHANGED_MSG_TYPE.getId())
                .content("queue changed")
                .platform("2")
                .storeGuid(storeGuid)
                .storeName(storeName)
                .build();
        Assert.isTrue("success".equals(businessMsgClient.sendMsg(messageDTO)));
    }

    @Override
    public StoreQueueDTO all() {
        UserContext userContext = UserContextUtils.get();
        String storeGuid = userContext.getStoreGuid();
        HolderQueueConfigDO holderQueueConfigDO = configMapper.selectOne(new LambdaQueryWrapper<HolderQueueConfigDO>().eq(HolderQueueConfigDO::getStoreGuid, storeGuid));
        if (holderQueueConfigDO == null) {
            holderQueueConfigDO = HolderQueueConfigDO.defaultConfig();
        }
        List<HolderQueueDO> dos = baseMapper.selectList(new LambdaQueryWrapper<HolderQueueDO>()
                .eq(HolderQueueDO::getStoreGuid, storeGuid)
                .eq(HolderQueueDO::getIsEnable, true)
                .orderByAsc(HolderQueueDO::getMin));
        if (dos == null || dos.isEmpty()) {
            Integer count = baseMapper.selectCountByStoreGuid(storeGuid);
            if (count == null || count == 0) {
                HolderQueueDTO less = HolderQueueDTO.DEFAULT_LESS.less(storeGuid);
                HolderQueueDTO mid = HolderQueueDTO.DEFAULT_MID.mid(storeGuid);
                save(less);
                save(mid);
            }
        }
        dos = baseMapper.selectList(new LambdaQueryWrapper<HolderQueueDO>()
                .eq(HolderQueueDO::getStoreGuid, storeGuid)
                .eq(HolderQueueDO::getIsEnable, true)
                .orderByAsc(HolderQueueDO::getMin));
        List<HolderQueueItemDO> items = queueItemMapper.selectList(new LambdaQueryWrapper<HolderQueueItemDO>()
                        .eq(HolderQueueItemDO::getStoreGuid, storeGuid)
//                .ge(HolderQueueItemDO::getStartTime,queueItemService.currentBusinessStart(storeGuid))
                        .nested((e) ->
                                e.or().eq(HolderQueueItemDO::getStatus, QueueItemStatusEnum.INQUEUE.ordinal())
                                        .or().eq(HolderQueueItemDO::getStatus, QueueItemStatusEnum.CALLING.ordinal())
                        )
                        .orderByAsc(HolderQueueItemDO::getSort)
                        .orderByAsc(HolderQueueItemDO::getGmtModified)
        );
        List<HolderQueueItemDetailDTO> detailDTOS = items.stream().map(queueItemMapStruct::toDetailDto).collect(Collectors.toList());
        Map<String, List<HolderQueueItemDetailDTO>> ref = detailDTOS.stream().peek((e) -> e.setEndTime(LocalDateTime.now())).collect(Collectors.groupingBy(HolderQueueItemDetailDTO::getQueueGuid));
        List<HolderQueueDetailDTO> queueDetailDTOS = dos.stream().map(queueMapStruct::toDetailDto).collect(Collectors.toList());
        Map<String, HolderQueueDetailDTO> detailRef = queueDetailDTOS.stream().collect(Collectors.toMap(HolderQueueDetailDTO::getGuid, e -> e));
        detailRef.forEach((e, a) -> {
            Optional<List<HolderQueueItemDetailDTO>> optional = Optional.ofNullable(ref.get(e));
            if (optional.isPresent()) {
                a.setItems(optional.get());
            } else {
                a.setItems(Collections.emptyList());
            }
        });
        return new StoreQueueDTO(queueDetailDTOS, holderQueueConfigDO.getIsEnableManualReset(), holderQueueConfigDO.getIsEnableEat(), holderQueueConfigDO.getIsEnableRecovery());
    }


    @Override
    public QueueTableDTO saveTables(QueueTableDTO dto) {
        HolderQueueDO holderQueueDO = baseMapper.selectOne(new LambdaQueryWrapper<HolderQueueDO>().eq(HolderQueueDO::getGuid, dto.getQueueGuid()));
        if (!RedissonLockUtil.tryLock("queue:store:lock:" + holderQueueDO.getStoreGuid(), 3, 3)) {
            throw new BusinessException("系统繁忙请稍后重试");
        }
        try {
            QueueTableDTO result = doinTransaction(() -> {
                Collection<HolderQueueTableDO> dos = dto.getTables().stream().map((e) -> queueMapStruct.toDo(e, dto.getQueueGuid())).collect(Collectors.toList());
                List<HolderQueueTableDO> db = queueTableMapper.selectList(new LambdaQueryWrapper<HolderQueueTableDO>().eq(HolderQueueTableDO::getQueueGuid, dto.getQueueGuid()));
                Map<String, HolderQueueTableDO> dbRef = db.stream().collect(Collectors.toMap(HolderQueueTableDO::getTableGuid, e -> e));
                Map<String, HolderQueueTableDO> dbRefCopy = new HashMap(dbRef);
                Map<String, HolderQueueTableDO> dtoRef = dos.stream().collect(Collectors.toMap(HolderQueueTableDO::getTableGuid, e -> e, (e, a) -> {
                    throw new BusinessException("重复的桌台guid");
                }, HashMap::new));
                Map<String, HolderQueueTableDO> dtoRefCopy = new HashMap(dtoRef);

                dbRefCopy.forEach((e, a) -> {
                    dtoRef.remove(e);
                });

                dtoRefCopy.forEach((e, a) -> {
                    dbRef.remove(e);
                });
                Collection<HolderQueueTableDO> add = dtoRef.values();
                if (add != null && !add.isEmpty()) {
                    int count = queueTableMapper.selectCount(new LambdaQueryWrapper<HolderQueueTableDO>().in(HolderQueueTableDO::getTableGuid, dtoRef.keySet()));
                    if (count > 0) {
                        throw new BusinessException("replicate_tables");
                    }
                    List<String> guids = QueuedUtils.nextConfigGuid(add.size());
                    Iterator<HolderQueueTableDO> iterator = add.iterator();
                    guids.forEach(e -> iterator.next().setGuid(e));
                    tableService.saveBatch(add);
                }
                if (dbRef != null && !dbRef.isEmpty()) {
                    queueTableMapper.delete(new LambdaQueryWrapper<HolderQueueTableDO>().in(HolderQueueTableDO::getTableGuid, dbRef.keySet()));
                }
                return dto;
            });
            sendMessage(holderQueueDO.getStoreGuid(), null);
            return result;
        } catch (Exception e) {
            log.error("save tables fail with exception: {}", e);
            throw e;
        } finally {
            RedissonLockUtil.unlock("queue:store:lock:" + holderQueueDO.getStoreGuid());
        }
    }

    @Override
    public Boolean allEmpty(String storeGuid) {
        Integer count = queueItemMapper.selectCount(new LambdaQueryWrapper<HolderQueueItemDO>().eq(HolderQueueItemDO::getStatus, QueueItemStatusEnum.INQUEUE.ordinal()).eq(HolderQueueItemDO::getStoreGuid, storeGuid));
        return count == 0;
    }

    @Override
    public List<HolderQueueDTO> listByStore(String storeGuid) {
        List<QueueSizeDO> sizeDOS = baseMapper.selectQueueSizeByStoreGuid(storeGuid);
        Map<String, Integer> ref = sizeDOS.stream().collect(Collectors.toMap(QueueSizeDO::getQueueGuid, QueueSizeDO::getSize));
        return baseMapper.selectList(
                new LambdaQueryWrapper<HolderQueueDO>()
                        .eq(HolderQueueDO::getStoreGuid, storeGuid)
        )
                .stream()
                .map(queueMapStruct::toDto)
                .peek((e) -> {
                    e.setSize(ref.get(e.getGuid()));
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<QueueDetailDTO> query(String storeGuid) {
        if (com.holderzone.framework.util.StringUtils.isEmpty(storeGuid)) {
            throw new BusinessException("请选择门店");
        }
        List<HolderQueueDO> list = baseMapper.selectList(new LambdaQueryWrapper<HolderQueueDO>().eq(HolderQueueDO::getStoreGuid, storeGuid));
        if (list == null || list.isEmpty()) {
            Integer count = baseMapper.selectCountByStoreGuid(storeGuid);
            if (count == null || count == 0) {
                HolderQueueDTO less = HolderQueueDTO.DEFAULT_LESS.less(storeGuid);
                HolderQueueDTO mid = HolderQueueDTO.DEFAULT_MID.mid(storeGuid);
                less.setGuid(save(less));
                mid.setGuid(save(mid));
                QueueDetailDTO lessDetail = new QueueDetailDTO();
                QueueDetailDTO midDetail = new QueueDetailDTO();

                BeanUtils.copyProperties(less, lessDetail);
                BeanUtils.copyProperties(mid, midDetail);
                lessDetail.setIndex(0);
                midDetail.setIndex(1);
                return Arrays.asList(lessDetail, midDetail);
            }
        }
        Map<String, QueueDetailDTO> ref = list.stream().collect(Collectors.toMap(HolderQueueDO::getGuid, e -> queueMapStruct.toTableDto(e)));
        Collection<String> queueGuids = ref.keySet();
        List<HolderQueueTableDO> tables = queueTableMapper.selectList(new LambdaQueryWrapper<HolderQueueTableDO>().in(HolderQueueTableDO::getQueueGuid, queueGuids));
        List<String> tableGuids = new ArrayList<>();
        Map<String, List<HolderQueueTableDO>> tableQueueRef = tables.stream().peek((e) -> tableGuids.add(e.getTableGuid())).collect(Collectors.groupingBy(HolderQueueTableDO::getQueueGuid));
        TableBasicQueryDTO basicDTO = new TableBasicQueryDTO();
        basicDTO.setStoreGuid(storeGuid);
        basicDTO.setTableGuidList(tableGuids);
        List<TableBasicDTO> allTables = tableClientService.listByWeb(basicDTO);
        Map<String, TableBasicDTO> tableRef = allTables.stream().collect(Collectors.toMap(TableBasicDTO::getGuid, e -> e));
        ref.forEach((e, a) -> {
            List<HolderQueueTableDO> dos = tableQueueRef.get(e);
            if (dos != null) {
                a.setTables(dos.stream().map((s) -> {
                    TableBasicDTO tableBasicDTO = tableRef.get(s.getTableGuid());
                    if (tableBasicDTO != null) {
                        return queueMapStruct.toDto(tableBasicDTO);
                    } else {
                        return null;
                    }
                }).filter(Objects::nonNull).collect(Collectors.toList()));
            }
        });
        List<QueueDetailDTO> result = new ArrayList<>(ref.values());
        result.sort(Comparator.comparingInt(QueueDetailDTO::getMin));
        result.forEach((e) -> {
            e.setIndex(result.indexOf(e));
        });
        return result;
    }


    private List<QueueDetailDTO> dealData(List<StoreDTO> storeList) {
        List<QueueDetailDTO> data = new ArrayList<>();
        for (StoreDTO store : storeList) {
            String storeGuid = store.getGuid();
            List<HolderQueueDO> list = baseMapper.selectList(new LambdaQueryWrapper<HolderQueueDO>().eq(HolderQueueDO::getStoreGuid, storeGuid));
            Map<String, QueueDetailDTO> ref = list.stream().collect(Collectors.toMap(HolderQueueDO::getGuid, e -> queueMapStruct.toTableDto(e)));
            Collection<String> queueGuids = ref.keySet();
            List<HolderQueueTableDO> tables = queueTableMapper.selectList(new LambdaQueryWrapper<HolderQueueTableDO>().in(HolderQueueTableDO::getQueueGuid, queueGuids));
            List<String> tableGuids = new ArrayList<>();
            Map<String, List<HolderQueueTableDO>> tableQueueRef = tables.stream().peek((e) -> tableGuids.add(e.getTableGuid())).collect(Collectors.groupingBy(HolderQueueTableDO::getQueueGuid));
            TableBasicQueryDTO basicDTO = new TableBasicQueryDTO();
            basicDTO.setStoreGuid(storeGuid);
            basicDTO.setTableGuidList(tableGuids);
            List<TableBasicDTO> allTables = tableClientService.listByWeb(basicDTO);
            Map<String, TableBasicDTO> tableRef = allTables.stream().collect(Collectors.toMap(TableBasicDTO::getGuid, e -> e));
            ref.forEach((e, a) -> {
                List<HolderQueueTableDO> dos = tableQueueRef.get(e);
                if (dos != null) {
                    a.setTables(dos.stream().map((s) -> {
                        TableBasicDTO tableBasicDTO = tableRef.get(s.getTableGuid());
                        if (tableBasicDTO != null) {
                            return queueMapStruct.toDto(tableBasicDTO);
                        } else {
                            return null;
                        }
                    }).filter(Objects::nonNull).collect(Collectors.toList()));
                }
            });
            List<QueueDetailDTO> result = new ArrayList<>(ref.values());
            result.sort(Comparator.comparingInt(QueueDetailDTO::getMin));
            result.forEach((e) -> {
                e.setIndex(result.indexOf(e));
            });
            data.addAll(result);
        }
        return data;
    }

    @Override
    public QueueTableDTO obtain(String queueGuid) {
        Collection<HolderQueueTableDO> tables = queueTableMapper.selectList(new LambdaQueryWrapper<HolderQueueTableDO>().eq(HolderQueueTableDO::getQueueGuid, queueGuid));
        QueueTableDTO queueTableDTO = new QueueTableDTO();
        queueTableDTO.setTables(new ArrayList<>(queueMapStruct.toDto(tables)));
        return queueTableDTO;
    }

    @Override
    public List<QueueTableDTO> queryByStore(String storeGuid) {
        List<String> queueGuids = baseMapper.selectList(new LambdaQueryWrapper<HolderQueueDO>().select(HolderQueueDO::getGuid).eq(HolderQueueDO::getStoreGuid, storeGuid)).stream().map(HolderQueueDO::getGuid).collect(Collectors.toList());
        Collection<HolderQueueTableDO> tables = queueTableMapper.selectList(new LambdaQueryWrapper<HolderQueueTableDO>().in(HolderQueueTableDO::getQueueGuid, queueGuids));
        Map<String, List<HolderQueueTableDO>> ref = tables.stream().collect(Collectors.groupingBy(HolderQueueTableDO::getQueueGuid));
        return ref.keySet().stream().map((e) -> {
            QueueTableDTO temp = new QueueTableDTO();
            temp.setQueueGuid(e);
            temp.setTables(new ArrayList<>(queueMapStruct.toDto(Optional.ofNullable(ref.get(e)).orElse(Collections.emptyList()))));
            return temp;
        }).collect(Collectors.toList());
    }

    @Override
    public List<TreeTableDTO> allTables(String storeGuid) {
        TableBasicQueryDTO basicDTO = new TableBasicQueryDTO();
        basicDTO.setStoreGuid(storeGuid);
        List<TableBasicDTO> allTables = tableClientService.listByWeb(basicDTO);
        Map<String, TableBasicDTO> tableBasicRef = allTables.stream().collect(Collectors.toMap(TableBasicDTO::getGuid, Function.identity()));
        Map<String, TreeTableDTO> tableRef = allTables.stream().collect(Collectors.toMap(TableBasicDTO::getGuid, queueMapStruct::toDto));
        List<HolderQueueDO> list = baseMapper.selectList(new LambdaQueryWrapper<HolderQueueDO>().eq(HolderQueueDO::getStoreGuid, storeGuid));
        List<String> queueGuids = list.stream().map(HolderQueueDO::getGuid).collect(Collectors.toList());
        if (!queueGuids.isEmpty()) {
            List<HolderQueueTableDO> tables = queueTableMapper.selectList(new LambdaQueryWrapper<HolderQueueTableDO>().in(HolderQueueTableDO::getQueueGuid, queueGuids));
            tables.forEach(e -> Optional.ofNullable(tableRef.get(e.getTableGuid()))
                    .ifPresent(s -> tableRef.get(e.getTableGuid()).setQueueGuid(e.getQueueGuid()))
            );
        }
        Map<Pair<String, String>, List<TreeTableDTO>> ref = tableRef.values().stream().collect(Collectors.groupingBy((e) -> Pair.of(e.getAreaName(), e.getAreaGuid())));
        return ref.keySet().stream().map(e -> {
            TreeTableDTO treeTableDTO = new TreeTableDTO();
            treeTableDTO.setAreaName(e.getFirst());
            treeTableDTO.setAreaGuid(e.getSecond());
            treeTableDTO.setChildren(
                    Optional.ofNullable(ref.get(e))
                            .map(
                                    d ->
                                            d.stream().sorted(
                                                    Comparator.comparingInt(f -> tableBasicRef.get(f.getTableGuid()).getSort())
                                            ).collect(Collectors.toList())
                            ).orElse(Collections.emptyList())
            );
            return treeTableDTO;
        }).collect(Collectors.toList());
    }
}