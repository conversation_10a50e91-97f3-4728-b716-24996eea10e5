package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AccountRecordHandoverException extends BusinessException {

    private static final long serialVersionUID = -7934642651738503813L;

    public AccountRecordHandoverException() {
        super("还有未交班的设备，无法结束营业");
    }

    public AccountRecordHandoverException(String message) {
        super("还有未交班的设备，无法结束营业["+message+"]");
    }

    public AccountRecordHandoverException(String message, Throwable cause) {
        super(message, cause);
    }
}
