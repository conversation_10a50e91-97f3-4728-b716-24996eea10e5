package com.holderzone.saas.store.business.utils;

import com.holderzone.saas.store.business.entity.domain.ScreenPicTimeDO;
import com.holderzone.saas.store.business.entity.domain.ScreenPictureDO;
import com.holderzone.saas.store.dto.business.manage.ScreenAppRespDTO;
import com.holderzone.saas.store.dto.business.manage.ScreenPicTimeDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPicMapStruct
 * @date 2018/11/16 14:58
 * @description
 * @program holder-saas-store-business
 */
@Mapper
public interface ScreenPicMapStruct {

    ScreenPicMapStruct SCREEN_PIC_MAP_STRUCT = Mappers.getMapper(ScreenPicMapStruct.class);

    List<ScreenAppRespDTO> ScreenPicDosToDtos(List<ScreenPictureDO> screenPictureDOS);

    @Mappings({
            @Mapping(target = "changeMils", source = "changeMills"),
            @Mapping(target = "pxType", source = "pxType"),
            @Mapping(target = "picType", source = "picType"),
    })
    ScreenAppRespDTO ScreenPicDoToDto(ScreenPictureDO screenPictureDO);

    @Mappings({
            @Mapping(target = "storeGuid", source = "selectStoreGuid"),
    })
    ScreenPicTimeDO picTimeDtoToPicTimeDo(ScreenPicTimeDTO screenPicTimeDTO);

    ScreenPicTimeDTO picTimeDoToPicTimeDto(ScreenPicTimeDO screenPicTimeDO);

    List<ScreenPicTimeDTO> picTimeDosToDtos(List<ScreenPicTimeDO> screenPicTimeDOS);
}
