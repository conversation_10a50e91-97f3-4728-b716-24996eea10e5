package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className IllegalParameterException
 * @date 2018/09/04 20:21
 * @description
 * @program holder-saas-store-trading-center
 */
public class IllegalParameterException extends BusinessException {

    public IllegalParameterException(String message) {
        super(message);
    }

    public IllegalParameterException(String message, Throwable cause) {
        super(message, cause);
    }


}
