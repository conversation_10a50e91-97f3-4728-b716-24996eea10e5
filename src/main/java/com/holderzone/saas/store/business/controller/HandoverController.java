package com.holderzone.saas.store.business.controller;

import com.holderzone.framework.repeat.annotation.NoneRepeat;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.business.service.HandoverRecordService;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.report.query.HandOverReportQueryDTO;
import com.holderzone.saas.store.dto.report.resp.HandoverReportRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueController
 * @date 2018/07/27 下午4:34
 * @description 交接班Controller
 * @program holder-saas-store-business-center
 */
@Slf4j
@Api("交接班相关接口")
@RestController
@RequestMapping("/handover")
public class HandoverController {

    private final HandoverRecordService handoverRecordService;

    @Autowired
    public HandoverController(HandoverRecordService handoverRecordService) {
        this.handoverRecordService = handoverRecordService;
    }

    @PostMapping("/create")
//    @NoneRepeat
    @ApiOperation(value = "创建交接班记录（即接班）", notes = "创建交接班记录（即接班）")
    public void create(@RequestBody @Validated HandoverRecordCreateDTO handoverRecordCreateDTO) {
        log.info("创建交接班记录接口入参：{}", JacksonUtils.writeValueAsString(handoverRecordCreateDTO));
        handoverRecordService.create(handoverRecordCreateDTO);
    }

    @PostMapping("/confirm")
    @ApiOperation(value = "交班", notes = "交班")
    @NoneRepeat
    public void confirm(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        log.info("交班接口入参：{}", JacksonUtils.writeValueAsString(handoverRecordConfirmDTO));
        handoverRecordService.confirm(handoverRecordConfirmDTO);
    }

    @PostMapping("/confirmNew")
    @ApiOperation(value = "交班并退出（新）")
    public void confirmNew(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        log.info("交接班退出接口入参,handoverRecordConfirmDTO={}", JacksonUtils.writeValueAsString(handoverRecordConfirmDTO));
        handoverRecordService.confirmNew(handoverRecordConfirmDTO);
    }

    @PostMapping("/settle")
    @ApiOperation(value = "查询当前交接班信息", notes = "查询当前交接班信息")
    public HandoverPayDTO settle(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO) {
        log.info("交班接口入参：{}", JacksonUtils.writeValueAsString(handoverPayQueryDTO));
        return handoverRecordService.settle(handoverPayQueryDTO);
    }

    @PostMapping("/settleNew")
    @ApiOperation(value = "查询当前交接班信息(新)", notes = "增加预订金统计，更改返回数据格式，拆分为销售、储值、预订")
    public HandoverPayNewDTO settleNew(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO) {
        log.info("交班接口入参,handoverPayQueryDTO={}", JacksonUtils.writeValueAsString(handoverPayQueryDTO));
        return handoverRecordService.settleNew(handoverPayQueryDTO);
    }

    @PostMapping("/query")
    @ApiOperation(value = "根据记录GUID查询该交接班记录", notes = "根据记录GUID查询该交接班记录")
    public HandoverRecordDTO query(@RequestBody @Validated HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO) {
        return handoverRecordService.query(handoverRecordQuerySingleDTO);
    }

    @PostMapping("/queryNew")
    @ApiOperation(value = "根据GUID查询交接班记录（新）")
    public HandoverPayNewDTO queryNew(@RequestBody @Validated HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO) {
        log.info("根据GUID查询交接班记录新,handoverRecordQuerySingleDTO={}", JacksonUtils.writeValueAsString(handoverRecordQuerySingleDTO));
        return handoverRecordService.queryNew(handoverRecordQuerySingleDTO);
    }

    @PostMapping("/queryByPage")
    @ApiOperation(value = "根据(storeGuid&terminalId&status)查询交接班记录列表", notes = "根据(storeGuid&terminalId&status)查询交接班记录列表")
    public Page<HandoverRecordDTO> queryByPage(@RequestBody @Validated HandoverRecordQueryAllDTO handoverRecordQueryAllDTO) {
        log.info("查询交接班记录列表,handoverRecordQueryAllDTO={}", JacksonUtils.writeValueAsString(handoverRecordQueryAllDTO));
        return handoverRecordService.queryByPage(handoverRecordQueryAllDTO);
    }

    @PostMapping("/isAllConfirmed")
    @ApiOperation(value = "根据storeGuid查询该门店是否全部设备均已交班（返回值1代表已交班）", notes = "根据storeGuid查询该门店是否全部设备均已交班（返回值1代表已交班）")
    public Integer isAllConfirmed(@RequestBody HandoverRecordIsAllConfirmedDTO handoverRecordIsAllConfirmedDTO) {
        log.info("根据storeGuid查询该门店是否全部设备均已交班接口入参：{}", JacksonUtils.writeValueAsString(handoverRecordIsAllConfirmedDTO));
        return handoverRecordService.isAllConfirmed(handoverRecordIsAllConfirmedDTO);
    }

    @GetMapping("/queryPayDetailById/{handoverRecordGuid}")
    @ApiOperation(value = "根据交接班记录guid查询该班次下的支付详情", notes = "根据交接班记录guid查询该班次下的支付详情")
    public List<HandoverPayDetailDTO> queryPayDetailById(@PathVariable("handoverRecordGuid") String handoverRecordGuid) {
        return handoverRecordService.queryPayDetailById(handoverRecordGuid);
    }

    @PostMapping("/isAllConfirmedByList")
    @ApiOperation(value = "根据storeGuid数组查询所有传入的门店是否已全部交班（返回值1代表所有门店所有设备已交班）"
            , notes = "根据storeGuid数组查询所有传入的门店是否已全部交班（返回值1代表所有门店所有设备已交班）")
    public Integer isAllConfirmedByList(@RequestBody List<String> storeGuidList) {
        log.info("根据storeGuid数组查询所有传入的门店是否已全部交班：{}", JacksonUtils.writeValueAsString(storeGuidList));
        return handoverRecordService.isAllConfirmedByList(storeGuidList);
    }

    @PostMapping("/queryByUserGuid")
    @ApiOperation(value = "根据员工guid查询该员工当前的班次详情", notes = "根据员工guid查询该员工当前的班次详情")
    public HandoverRecordDTO queryByUserGuid(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        return handoverRecordService.queryByUserGuid(handoverRecordConfirmDTO);
    }

    /**
     * 根据员工guid查询该员工当前的班次详情列表
     *
     * @param handoverRecordConfirmDTO 员工列表
     * @return 员工班次详情列表
     */
    @PostMapping("/list_by_user_guid")
    @ApiOperation(value = "根据员工guid查询该员工当前的班次详情列表")
    public List<HandoverRecordDTO> listByUserGuid(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        return handoverRecordService.listByUserGuid(handoverRecordConfirmDTO);
    }

    @PostMapping("/queryAll/{storeGuid}/{terminalId}")
    @ApiOperation(value = "根据(storeGuid&terminalId)查询交接班记录列表", notes = "根据(storeGuid&terminalId)查询交接班记录列表")
    public HandoverRecordDTO queryByUserStoreGuidAndTerminalId(@PathVariable("storeGuid") String storeGuid,
                                                               @PathVariable("terminalId") String terminalId) {
        return handoverRecordService.queryByUserStoreGuidAndTerminalId(storeGuid, terminalId);
    }

    @PostMapping("/printHandOverRecord/{handoverRecordGuid}/{deviceId}")
    @ApiOperation(value = "根据交接班guid重打印交接单", notes = "根据交接班guid重打印交接单")
    public void printHandOverRecord(@PathVariable("handoverRecordGuid") String handoverRecordGuid,
                                    @PathVariable("deviceId") String deviceId) {
        handoverRecordService.printHandOverRecord(handoverRecordGuid, deviceId);
    }

    @PostMapping("/printHandOverRecordNew/{handoverRecordGuid}/{deviceId}")
    @ApiOperation(value = "根据交接班guid重打印交接单", notes = "根据交接班guid重打印交接单")
    public void printHandOverRecordNew(@PathVariable("handoverRecordGuid") String handoverRecordGuid,
                                       @PathVariable("deviceId") String deviceId) {
        handoverRecordService.printHandOverRecordNew(handoverRecordGuid, deviceId);
    }

    @PostMapping("/prePrintHandOverRecord")
    @ApiOperation(value = "预打印交接单", notes = "预打印交接单")
    public void prePrintHandOverRecord(@RequestBody HandoverPayNewDTO handoverPayNewDTO) {
        handoverRecordService.prePrintHandOverRecord(handoverPayNewDTO);
    }

    @PostMapping("/query_by_storeGuid_and_userGuid")
    @ApiOperation(value = "根据storeGuid和userGuid查询开班记录")
    public HandoverRecordDTO queryByStoreGuidAndUserGuid(@RequestParam("storeGuid") String storeGuid, @RequestParam("userGuid") String userGuid) {
        return handoverRecordService.queryByStoreGuidAndUserGuid(storeGuid, userGuid);
    }


    @PostMapping("/retail/settle")
    @ApiOperation(value = "零售版查询当前交接班信息", notes = "零售版查询当前交接班信息")
    public HandoverPayDTO retailSettle(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO) {
        log.info("零售版查询当前交接班信息入参：{}", JacksonUtils.writeValueAsString(handoverPayQueryDTO));
        return handoverRecordService.retailSettle(handoverPayQueryDTO);
    }

    @NoneRepeat
    @PostMapping("/retail/confirm")
    @ApiOperation(value = "零售版交班", notes = "零售版交班")
    public void retailConfirm(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        log.info("零售版交班接口入参：{}", JacksonUtils.writeValueAsString(handoverRecordConfirmDTO));
        handoverRecordService.retailConfirm(handoverRecordConfirmDTO);
    }


    @PostMapping("/retail/queryByPage")
    @ApiOperation(value = "零售版根据(storeGuid&terminalId&status)查询交接班记录列表", notes = "根据(storeGuid&terminalId&status)查询交接班记录列表")
    public Page<HandoverRecordDTO> retailQueryByPage(@RequestBody @Validated HandoverRecordQueryAllDTO handoverRecordQueryAllDTO) {
        return handoverRecordService.retailQueryByPage(handoverRecordQueryAllDTO);
    }

    @PostMapping("/retail/query")
    @ApiOperation(value = "零售版根据记录GUID查询该交接班记录", notes = "零售版根据记录GUID查询该交接班记录")
    public HandoverRecordDTO retailQuery(@RequestBody @Validated HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO) {
        return handoverRecordService.query(handoverRecordQuerySingleDTO);
    }

    @PostMapping("/retail/queryByUserGuid")
    @ApiOperation(value = "零售版根据员工guid查询该员工当前的班次详情", notes = "零售版根据员工guid查询该员工当前的班次详情")
    public HandoverRecordDTO retailQueryByUserGuid(@RequestBody HandoverRecordConfirmDTO handoverRecordConfirmDTO) {
        return handoverRecordService.retailQueryByUserGuid(handoverRecordConfirmDTO);
    }


    @PostMapping("/retail/printHandOverRecord/{handoverRecordGuid}/{deviceId}")
    @ApiOperation(value = "零售版根据交接班guid重打印交接单", notes = "零售版根据交接班guid重打印交接单")
    public void printReatilHandOverRecord(@PathVariable("handoverRecordGuid") String handoverRecordGuid,
                                          @PathVariable("deviceId") String deviceId) {
        handoverRecordService.printReatilHandOverRecord(handoverRecordGuid, deviceId);
    }

    @ApiOperation(value = "交接班报表查询")
    @PostMapping("/report")
    public List<HandoverReportRespDTO> report(@RequestBody HandOverReportQueryDTO handOverQueryDTO) {
        log.info("交接班报表查询入参：{}", JacksonUtils.writeValueAsString(handOverQueryDTO));
        return handoverRecordService.report(handOverQueryDTO);
    }

    /**
     * 处理交接班历史数据
     *
     * @param request 注意控制数量，定长线程池，但是数据库可能遭不住
     */
    @ApiOperation(value = "处理交接班历史数据")
    @PostMapping("/handle_handover_history")
    public void handleHandoverHistory(@RequestBody HandoverHistoryHandleDTO request) {
        log.info("处理交接班历史数据,request={}", JacksonUtils.writeValueAsString(request));
        handoverRecordService.handleHandoverHistory(request);
    }

    /**
     * 查询当前门店未交班所有员工
     */
    @GetMapping("/query_on_duty_staffs/{storeGuid}")
    @ApiOperation(value = "查询当前门店未交班所有员工", notes = "查询当前门店未交班所有员工")
    public List<HandoverRecordDTO> queryOnDutyStaffs(@PathVariable String storeGuid) {
        log.info("查询当前门店未交班所有员工入参,storeGuid:{}", storeGuid);
        return handoverRecordService.queryOnDutyStaffs(storeGuid);
    }
}
