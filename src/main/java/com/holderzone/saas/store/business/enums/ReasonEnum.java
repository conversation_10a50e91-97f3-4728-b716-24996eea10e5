package com.holderzone.saas.store.business.enums;

import com.holderzone.saas.store.dto.business.reason.ReasonDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum ReasonEnum {
    /**
     * 商超内置原因
     */
    GOODSEXPIRED(8,"商品过期"), GOBAD(8,"商品变质"), REPEATPURCHASE(8,"重复购买"),

    PROMOTION(9,"促销赠送"),GIVING(9,"买一送一"),ALLGIVING(9,"满赠原因"),

    REFUSE(3,"客人退单"),NOHANING(3,"商品售罄"),

    NOGOODS(4,"缺货退单"),GOODSEXPARE(4,"商品过期"),DETERIORATION(4,"商品变质"),

    GIVED(5,"商品已送出"),RETUTING(5,"金额已退还");


    private int code;
    private String des;


    public static List<ReasonDTO> getReason(){
        ArrayList<ReasonDTO> list = new ArrayList<>();
        ArrayList<ReasonEnum> reasonEnums = new ArrayList<>(Arrays.asList(ReasonEnum.values()));
        for (ReasonEnum reasonEnum : reasonEnums) {
            ReasonDTO build = ReasonDTO.builder().reason(reasonEnum.getDes()).reasonTypeCode(reasonEnum.getCode()).build();
            list.add(build);
        }
        return list;
    }

    public static void main(String[] args) {
        List<ReasonDTO> reason = getReason();
        System.out.println(reason);
        List<ReasonDTO> collect = ReasonEnum.getReason().stream().peek(x -> x.setStoreGuid("1")).collect(Collectors.toList());
        System.out.println(collect);
    }
}
