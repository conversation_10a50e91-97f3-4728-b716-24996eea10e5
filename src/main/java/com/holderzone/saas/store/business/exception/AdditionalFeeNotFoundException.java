package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.NotFoundException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AdditionalFeeNotFoundException extends NotFoundException {

    private static final long serialVersionUID = -6778502079623616324L;

    public AdditionalFeeNotFoundException() {
        super("该附加费不存在");
    }

    public AdditionalFeeNotFoundException(String message) {
        super("该附加费["+message+"]不存在");
    }

    public AdditionalFeeNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
