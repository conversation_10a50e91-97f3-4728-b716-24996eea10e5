package com.holderzone.saas.store.business.service.client;

import com.holderzone.saas.store.dto.trade.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className JHPayClientService
 * @date 2018/08/14 11:40
 * @description //
 * @program holder-saas-store-trading-center
 */
public interface JHPayClientService {


    /**
     * queryForBusiness
     *
     * @param pollingJHPayDTO
     * @return
     */
    PollingJHPayRespDTO query(PollingJHPayDTO pollingJHPayDTO);

    /**
     * 验证appId是否合法
     *
     * @return
     */
    Boolean check(MchntValidateDTO mchntValidateDTO);

}
