package com.holderzone.saas.store.business.service.client;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.user.UserDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-staff", fallbackFactory = UserClientService.ServiceFallBack.class)
public interface UserClientService {

    @ApiOperation(value = "批量查询用户信息")
    @PostMapping(value = "/user/list_user")
    List<UserDTO> listUser(@RequestBody List<String> userGuidList);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<UserClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public UserClientService create(Throwable cause) {
            return userGuidList -> {
                log.error(HYSTRIX_PATTERN, "listUser", userGuidList, ThrowableUtils.asString(cause));
                throw new ServerException();
            };
        }
    }
}
