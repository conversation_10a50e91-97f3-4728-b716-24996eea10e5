package com.holderzone.saas.store.business.queue.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HolderQueueDO
 * @date 2019/03/27 16:42
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
@Accessors(chain = true)
@TableName("hsq_queue")
public class HolderQueueDO implements Serializable {
    @TableId
    private Long id;
    private String guid;
    private String storeGuid;
    private String name;

    private String code;

    private Byte min;

    @TableField(strategy = FieldStrategy.IGNORED)
    private Byte max;

    /**
     * 是否启用，1-已启用，0-未启用
     */
    private Boolean isEnable;

    /**
     * 是否删除，1-已删除，0-未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建人guid
     */
    private String createStaffGuid;

    /**
     * 更新人guid
     */
    private String modifiedStaffGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;
}