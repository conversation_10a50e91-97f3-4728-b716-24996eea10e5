package com.holderzone.saas.store.business.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.saas.store.enums.business.PicTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPicTimeDO
 * @date 2018/11/27 9:25
 * @description
 * @program holder-saas-store-business
 */
@Data
@TableName("hsb_store_pic_time")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScreenPicTimeDO {

    private Long id;

    @TableId(value = "pic_time_guid", type = IdType.INPUT)
    @ApiModelProperty(value = "业务主键")
    private String picTimeGuid;

    @ApiModelProperty(value = "切换时间")
    @TableField(value = "store_pic_change_time")
    private Integer changeMills;

    /**
     * @see PicTypeEnum
     */
    @ApiModelProperty(value = "图片类型")
    private Integer picType;

    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "设备类型")
    private Integer deviceType;

    @ApiModelProperty(value = "副屏显示内容 1同主屏一致 2自定义")
    private Integer displayType;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

}
