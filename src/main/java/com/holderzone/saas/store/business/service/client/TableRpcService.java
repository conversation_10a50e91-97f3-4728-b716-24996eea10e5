package com.holderzone.saas.store.business.service.client;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.saas.store.dto.table.AreaDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableOrderDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className PrinterClient
 * @date 18-10-20 上午9:42
 * @description
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(value = "holder-saas-store-table", fallbackFactory = TableRpcService.ServiceFallBack.class)
public interface TableRpcService {

    @PostMapping("/area/query/all/{storeGuid}")
    List<AreaDTO> queryArea(@PathVariable("storeGuid") String storeGuid);

    @PostMapping("/table/getAreaGuid/{tableGuid}")
    String getAreaGuidByTableGuid(@PathVariable("tableGuid") String tableGuid);

    @PostMapping("/table/android/query")
    List<TableOrderDTO> listByAndroid(@RequestBody TableBasicQueryDTO tableBasicQueryDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<TableRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TableRpcService create(Throwable cause) {
            return new TableRpcService() {
                @Override
                public List<AreaDTO> queryArea(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryArea", storeGuid, cause.getMessage());
                    throw new ServerException();
                }

                @Override
                public String getAreaGuidByTableGuid(String tableGuid) {
                    log.error(HYSTRIX_PATTERN, "getAreaGuidByTableGuid", tableGuid, cause.getMessage());
                    throw new ServerException();
                }

                @Override
                public List<TableOrderDTO> listByAndroid(TableBasicQueryDTO tableBasicQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "listByAndroid", tableBasicQueryDTO, cause.getMessage());
                    throw new ServerException();
                }

            };
        }
    }
}