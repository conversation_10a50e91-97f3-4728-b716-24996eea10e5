package com.holderzone.saas.store.business.event;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.business.constant.RocketMqConfig;
import com.holderzone.saas.store.business.service.SurchargeService;
import com.holderzone.saas.store.business.utils.ThrowableExtUtils;
import com.holderzone.saas.store.dto.business.manage.SurchargeCreateDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SurchargeStoreCreateListener
 * @date 2019/12/19 15:44
 * @description 附加费门店创建监听
 * @program holder-saas-store
 */
@Component
@Slf4j
@RocketListenerHandler(
        topic = RocketMqConfig.DOWNSTREAM_STORE_TOPIC,
        tags = RocketMqConfig.DOWNSTREAM_STORE_CREATE_TAG,
        consumerGroup = RocketMqConfig.DOWNSTREAM_STORE_CREATE_SURCHARGE_GROUP
)
public class SurchargeStoreCreateListener extends AbstractRocketMqConsumer<RocketMqTopic, StoreDTO> {

    private final SurchargeService surchargeService;

    @Autowired
    public SurchargeStoreCreateListener(SurchargeService surchargeService) {
        this.surchargeService = surchargeService;
    }

    @Override
    public boolean consumeMsg(StoreDTO storeDTO, MessageExt messageExt) {
        UserContextUtils.put(messageExt.getProperty(MqConstant.DOWNSTREAM_CONTEXT));
        EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        try {
            SurchargeCreateDTO surchargeCreateDTO = new SurchargeCreateDTO();
            surchargeCreateDTO.setStoreGuid(storeDTO.getGuid());
            surchargeCreateDTO.setName("餐具费");
            surchargeCreateDTO.setAmount(BigDecimal.ONE);
            surchargeCreateDTO.setType(0);
            surchargeCreateDTO.setTradeModes(Lists.newArrayList(String.valueOf(TradeModeEnum.DINEIN.getCode())));
            surchargeCreateDTO.setAreaGuidList(Lists.newArrayList());
            surchargeService.addSurcharge(surchargeCreateDTO);
            log.info("附加费【{}】 初始化成功", JacksonUtils.writeValueAsString(surchargeCreateDTO));
        } catch (Exception e) {
            log.error("门店【{}】：初始化附加费发生异常：{}，初始化消息已消费",
                    storeDTO.getGuid(), ThrowableExtUtils.asStringIfAbsent(e));
            return true;
        } finally {
            EnterpriseIdentifier.remove();
        }
        return true;
    }
}
