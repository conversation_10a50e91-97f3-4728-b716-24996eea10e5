package com.holderzone.saas.store.business.service.client;

import com.holderzone.saas.store.dto.trade.ShortMsgConfigDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SmsEnterpriseClientService
 * @date 2018/10/15 10:54
 * @description
 * @program holder-saas-store-business
 */
@Component
@FeignClient(name = "holder-saas-cloud-enterprise", fallbackFactory = SmsEnterpriseClientService.SmsEnterpriseFallBackService.class)
public interface SmsEnterpriseClientService {

    @GetMapping("/enterprise/sms/info")
    ShortMsgConfigDTO query(@RequestParam("enterpriseGuid") String enterpriseGuid);

    @PostMapping("/enterprise/sms/update")
    boolean update(@RequestBody ShortMsgConfigDTO shortMsgConfigDTO);

    @Component
    class SmsEnterpriseFallBackService implements FallbackFactory<SmsEnterpriseClientService> {

        private static final Logger LOGGER = LoggerFactory.getLogger(SmsEnterpriseFallBackService.class);

        @Override
        public SmsEnterpriseClientService create(Throwable throwable) {
            return new SmsEnterpriseClientService() {
                @Override
                public ShortMsgConfigDTO query(String enterpriseGuid) {
                    throwable.printStackTrace();
                    LOGGER.error("查询短信异常 t {}", throwable.getMessage());
                    return null;
                }

                @Override
                public boolean update(ShortMsgConfigDTO shortMsgConfigDTO) {
                    throwable.printStackTrace();
                    LOGGER.error("查询短信异常 t {}", throwable.getMessage());
                    return false;
                }
            };
        }
    }

}
