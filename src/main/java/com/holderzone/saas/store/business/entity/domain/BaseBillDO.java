package com.holderzone.saas.store.business.entity.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BaseBillDO
 * @date 2018/08/01 14:51
 * @description
 * @program holder-saas-store-trading-center
 */
@Data
public abstract class BaseBillDO implements Serializable {

    protected Long id;

    /**
     * 关联账单guid
     */
    protected String billGuid;

    /**
     * 订单guid
     */
    protected String orderGuid;

    /**
     * 门店Guid
     */
    protected String storeGuid;

    /**
     * 门店名
     */
    protected String storeName;

    /**
     * 企业guid
     */
    protected String enterpriseGuid;
}
