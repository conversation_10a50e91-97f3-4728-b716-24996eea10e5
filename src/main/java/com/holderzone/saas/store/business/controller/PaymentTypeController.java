package com.holderzone.saas.store.business.controller;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.repeat.annotation.NoneRepeat;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.business.enums.PaymentType;
import com.holderzone.saas.store.business.service.PaymentTypeService;
import com.holderzone.saas.store.dto.journaling.req.PaySerialStatisticsReqDTO;
import com.holderzone.saas.store.dto.trade.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

import static com.holderzone.saas.store.business.config.BillConstant.FAILUER;
import static com.holderzone.saas.store.business.config.BillConstant.SUCCESS;


/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeController
 * @date 2018/08/27 9:14
 * @description
 * @program holder-saas-store-trading-center
 */
@Slf4j
@RestController
@RequestMapping("/pay/type")
public class PaymentTypeController {

    private final PaymentTypeService paymentTypeService;

    @Autowired
    public PaymentTypeController(PaymentTypeService paymentTypeService) {
        this.paymentTypeService = paymentTypeService;
    }

    @ApiOperation(value = "初始化门店支付方式信息")
    @PostMapping("/init")
    public String init(@RequestBody StoreDTO storeDTO) {
        log.info("初始化门店支付方式信息 storeDTO={}", JacksonUtils.writeValueAsString(storeDTO));
        return paymentTypeService.init(storeDTO.getStoreGuid(), storeDTO.getStoreName(), storeDTO.getMchntTypeCode());
    }


    @ApiOperation(value = "如果新增其他支付方式，sorting自动自增1")
    @PostMapping("/add")
    public String addPaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO) {
        log.info("新增支付方式：paymentTypeDTO={}", JacksonUtils.writeValueAsString(paymentTypeDTO));
        try {
            paymentTypeService.add(paymentTypeDTO);
        } catch (Exception e) {
            log.error("新增支付方式失败,e={}", e.getMessage());
            e.printStackTrace();
            return e.getMessage();
        }
        return SUCCESS;
    }

    @PostMapping("/update")
    public String updatePaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO) {
        log.info("更新支付方式：paymentTypeDTO={}", JacksonUtils.writeValueAsString(paymentTypeDTO));
        try {
            if (null == paymentTypeDTO.getStoreDTOS() || paymentTypeDTO.getStoreDTOS().isEmpty()) {
                throw new BusinessException("更新支付方式，门店数据不能为空");
            }
            paymentTypeService.update(paymentTypeDTO);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("更新支付方式失败,e={}", e.getMessage());
            return e.getMessage();
        }
        return SUCCESS;
    }

    @PostMapping("/sort")
    public String sorting(@RequestBody List<PaymentTypeDTO> paymentTypeDTOS) {
        log.info("更新排序，paymentTypeDTOS={}", JacksonUtils.writeValueAsString(paymentTypeDTOS));
        return paymentTypeService.sort(paymentTypeDTOS);
    }

    /**
     * 已废弃
     *
     * @see PaymentTypeController#paymentAccountSave(com.holderzone.saas.store.dto.trade.PaymentAccountReqDTO)
     */
    @NoneRepeat
    @PostMapping("/config")
    public String config(@RequestBody JHConfigDTO jhConfigDTO) {
        log.info("聚合支付配置 jhConfigDTO={}", JacksonUtils.writeValueAsString(jhConfigDTO));
        return paymentTypeService.config(jhConfigDTO);
    }

    @ApiOperation(value = "支付方式解绑")
    @PostMapping("/unbind")
    public String unbind(@RequestBody JHReqDTO jhReqDTO) {
        log.info("解绑 jhConfigDTO={}", JacksonUtils.writeValueAsString(jhReqDTO));
        return paymentTypeService.unbind(jhReqDTO);
    }

    @PostMapping("/detail")
    public PaymentTypeDTO detail(@RequestBody PaymentTypeDTO paymentTypeDTO) {
        log.info("查询详情 paymentTypeDTO={}", JacksonUtils.writeValueAsString(paymentTypeDTO));
        return paymentTypeService.getOne(paymentTypeDTO);
    }

    @PostMapping("/delete")
    public String deletePaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO) {
        log.info("删除支付方式：paymentTypeDTO{}", JacksonUtils.writeValueAsString(paymentTypeDTO));
        if (PaymentType.getAllId().contains(paymentTypeDTO.getPaymentType())) {
            return FAILUER;
        }
        try {
            if (null == paymentTypeDTO.getStoreDTOS() || paymentTypeDTO.getStoreDTOS().isEmpty()) {
                throw new BusinessException("删除支付方式，门店数据不能为空");
            }
            paymentTypeService.delete(paymentTypeDTO.getStoreDTOS().get(0).getStoreGuid(), paymentTypeDTO.getPaymentTypeGuid());
        } catch (Exception e) {
            log.error("删除支付方式失败,e={}", e.getMessage());
            e.printStackTrace();
            return FAILUER;
        }
        return SUCCESS;
    }

    /**
     * web调用的
     *
     * @param paymentTypeQueryDTO
     * @return
     */
    @PostMapping("/getAll")
    public List<PaymentTypeDTO> getAll(@RequestBody PaymentTypeQueryDTO paymentTypeQueryDTO) {
        log.info("查询所有支付方式 paymentTypeQueryDTO={}", JacksonUtils.writeValueAsString(paymentTypeQueryDTO));
        return paymentTypeService.getAll(paymentTypeQueryDTO);
    }

    @PostMapping("/get_all_by_store_guid_list")
    public List<PaymentTypeBatchDTO> getAllByStoreGuidList(@RequestBody PaymentTypeBatchQureyDTO paymentTypeBatchQureyDTO) {
        log.info("查询所有门店所有支付方式 paymentTypeBatchQureyDTO={}", JacksonUtils.writeValueAsString(paymentTypeBatchQureyDTO));
        return paymentTypeService.getAllByStoreGuidList(paymentTypeBatchQureyDTO);
    }

    @PostMapping("/getAll/android")
    public List<PaymentTypeDTO> getAllByAndroid(@RequestBody PaymentTypeQueryDTO paymentTypeQueryDTO) {
        log.info("安卓查询所有支付方式 paymentTypeQueryDTO={}", JacksonUtils.writeValueAsString(paymentTypeQueryDTO));
        return paymentTypeService.getAllByAndroid(paymentTypeQueryDTO);
    }

    @PostMapping("/getAllName")
    public Set<String> getAllName(@RequestBody List<String> storeGuids) {
        log.info("查询多个门店支付方式name storeGuids={}", JacksonUtils.writeValueAsString(storeGuids));
        if (storeGuids.isEmpty()) {
            throw new BusinessException("查询多个门店支付方式name，传入门店为空！！");
        }
        return paymentTypeService.getAllStoreNames(storeGuids);
    }


    @PostMapping("/payType/{storeGuid}")
    public List<PaymentTypeDTO> getAllTypeName(@PathVariable("storeGuid") String storeGuid) {
        return paymentTypeService.getAllTypeName(storeGuid);
    }

    @PostMapping("/get_multi_store_pay_way")
    public Page<PaymentTypeDTO> getMultiStorePayWay(@RequestBody PaySerialStatisticsReqDTO paySerialStatisticsReqDTO) {
        log.info("查询多个门店的支付方式，分页显示，入参={}", JacksonUtils.writeValueAsString(paySerialStatisticsReqDTO));
        return paymentTypeService.getMultiStorePayWay(paySerialStatisticsReqDTO);
    }

    @PostMapping("/init_payment_type/{storeGuid}")
    public Boolean initPaymentType(@PathVariable("storeGuid") String storeGuid) {
        return paymentTypeService.initPaymentType(storeGuid);
    }

    @ApiOperation(value = "查询系统默认支付方式枚举转换")
    @PostMapping("/get_payment_type_list")
    public List<PaymentTypeEnumDTO> getPaymentTypeList() {
        return paymentTypeService.getPaymentTypeList();
    }

    @PostMapping("/edit_payment_type_mode")
    public Boolean editPaymentTypeMode(@RequestBody PaymentTypeModeDTO modeDTO) {
        return paymentTypeService.editPaymentTypeMode(modeDTO);
    }

    /**
     * 聚合支付账户设置
     *
     * @param request 入参
     * @return boolean
     */
    @ApiOperation(value = "聚合支付账户设置")
    @PostMapping("/payment_account_save")
    public Boolean paymentAccountSave(@RequestBody PaymentAccountReqDTO request) {
        log.info("聚合支付账户设置 request={}", JacksonUtils.writeValueAsString(request));
        return paymentTypeService.paymentAccountSave(request);
    }

    /**
     * 获取聚合支付方式信息
     *
     * @param storeGuid 门店guid
     * @return 聚合支付方式信息
     */
    @ApiOperation(value = "获取聚合支付方式信息")
    @GetMapping("/get_jh_payment_type_info")
    public PaymentTypeDTO getJhPaymentTypeInfo(@RequestParam("storeGuid") String storeGuid) {
        log.info("获取聚合支付方式信息 storeGuid={}", storeGuid);
        return paymentTypeService.getJhPaymentTypeInfo(storeGuid);
    }
}
