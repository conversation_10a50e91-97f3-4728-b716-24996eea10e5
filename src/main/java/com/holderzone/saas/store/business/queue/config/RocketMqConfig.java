package com.holderzone.saas.store.business.queue.config;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RocketMqConfig
 * @date 2019/08/09 14:25
 * @description RocketMq 配置
 * @program holder-saas-store
 */
public interface RocketMqConfig {

    String DOWNSTREAM_STORE_TOPIC = "downstream-store-topic";

    String DOWNSTREAM_STORE_CREATE_TAG = "downstream-store-create-tag";

    String DOWNSTREAM_STORE_INIT_QUEUE_GROUP = "downstream-store-init-queue-group";

    String DOWNSTREAM_CONTEXT = "downstream-context";
}
