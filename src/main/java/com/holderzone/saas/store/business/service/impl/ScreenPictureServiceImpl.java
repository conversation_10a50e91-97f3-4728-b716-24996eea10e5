package com.holderzone.saas.store.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.business.entity.domain.ScreenPicTimeDO;
import com.holderzone.saas.store.business.entity.domain.ScreenPictureDO;
import com.holderzone.saas.store.business.mapper.ScreenPictureMapper;
import com.holderzone.saas.store.business.mapstruct.ScreenPicMapstruct;
import com.holderzone.saas.store.business.service.ScreenPicTimeService;
import com.holderzone.saas.store.business.service.ScreenPictureService;
import com.holderzone.saas.store.business.service.client.FileUploadClient;
import com.holderzone.saas.store.business.utils.GuidUtils;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.PictureEnum;
import com.holderzone.saas.store.enums.business.PicTypeEnum;
import net.coobird.thumbnailator.Thumbnails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.business.constant.Constant.FAILURE;
import static com.holderzone.saas.store.business.constant.Constant.SUCCESS;
import static com.holderzone.saas.store.business.utils.GuidUtils.nextScreenPicGuid;
import static com.holderzone.saas.store.business.utils.ScreenPicMapStruct.SCREEN_PIC_MAP_STRUCT;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPictureServiceImpl
 * @date 2018/09/06 17:40
 * @description
 * @program holder-saas-store-business
 */
@Service
public class ScreenPictureServiceImpl extends ServiceImpl<ScreenPictureMapper, ScreenPictureDO> implements ScreenPictureService {

    private static final Logger logger = LoggerFactory.getLogger(ScreenPictureServiceImpl.class);
    private final ScreenPictureMapper screenPictureMapper;
    private final FileUploadClient fileUploadClient;
    private final ScreenPicTimeService screenPicTimeService;

    @Autowired
    public ScreenPictureServiceImpl(FileUploadClient fileUploadClient, ScreenPictureMapper screenPictureMapper,
                                    ScreenPicTimeService screenPicTimeService) {
        this.screenPictureMapper = screenPictureMapper;
        this.fileUploadClient = fileUploadClient;
        this.screenPicTimeService = screenPicTimeService;
    }


    private static double getAccuracy(long size) {
        double accuracy;
        if (size < 900) {
            accuracy = 0.85;
        } else if (size < 2047) {
            accuracy = 0.6;
        } else if (size < 3275) {
            accuracy = 0.44;
        } else {
            accuracy = 0.4;
        }
        return accuracy;
    }

    @Override
    public ScreenPictureConfigDTO getConfig(ScreenPicConfigReqDTO screenPicConfigReqDTO) {
        List<ScreenPictureDO> list = this.list(new LambdaQueryWrapper<ScreenPictureDO>()
                .eq(ScreenPictureDO::getStoreGuid, screenPicConfigReqDTO.getStoreGuid())
                .eq(ScreenPictureDO::getDeviceType, screenPicConfigReqDTO.getDeviceType())
                .eq(ScreenPictureDO::getPicType, screenPicConfigReqDTO.getPicType()));
        List<ScreenPicDTO> screenPicDTOS = ScreenPicMapstruct.INSTANCE.doList2DTOList(list);
        ScreenPicTimeDO one = screenPicTimeService.getOne(new LambdaQueryWrapper<ScreenPicTimeDO>()
                .eq(ScreenPicTimeDO::getStoreGuid, screenPicConfigReqDTO.getStoreGuid())
                .eq(ScreenPicTimeDO::getDeviceType, screenPicConfigReqDTO.getDeviceType())
                .eq(ScreenPicTimeDO::getPicType, screenPicConfigReqDTO.getPicType()));
        return one == null ? new ScreenPictureConfigDTO() :
                ScreenPictureConfigDTO.builder()
                        .changeMills(one.getChangeMills())
                        .deviceType(one.getDeviceType())
                        .picTimeGuid(one.getPicTimeGuid())
                        .picType(one.getPicType())
                        .screenPicDTOList(screenPicDTOS)
                        .storeGuid(one.getStoreGuid())
                        .displayType(one.getDisplayType())
                        .storeName(one.getStoreName()).build();
    }

    @Override
    public String saveConfig(ScreenPictureConfigDTO screenPictureConfigDTO) {
        validateParams(screenPictureConfigDTO);
        if (savePictures(screenPictureConfigDTO)) {
            return saveTime(screenPictureConfigDTO) ? SUCCESS : FAILURE;
        }
        return FAILURE;
    }

    private void validateParams(ScreenPictureConfigDTO screenPictureConfigDTO) {
        if (screenPictureConfigDTO.getDeviceType() == null) {
            throw new BusinessException("设备类型为空");
        }
        List<Integer> integers = Arrays.asList(BaseDeviceTypeEnum.All_IN_ONE.getCode(),
                BaseDeviceTypeEnum.KQS.getCode(),
                BaseDeviceTypeEnum.SELF.getCode());
        if (!integers.contains(screenPictureConfigDTO.getDeviceType())) {
            throw new BusinessException("不支持的设备类型【" + BaseDeviceTypeEnum.getDesc(screenPictureConfigDTO.getDeviceType()) + "】");
        }
        if (screenPictureConfigDTO.getPicType() == null) {
            throw new BusinessException("图片类型为空");
        }
        if (!Arrays.asList(1, 2, 3).contains(screenPictureConfigDTO.getPicType())) {
            throw new BusinessException("不支持的图片类型");
        }
        if (screenPictureConfigDTO.getScreenPicDTOList().size() > 6) {
            screenPictureConfigDTO.getScreenPicDTOList()
                    .forEach(screenPicDTO -> fileUploadClient.delete(screenPicDTO.getOssUrl()));
            throw new BusinessException("最多可以上传6张图片");
        }
    }

    private Boolean savePictures(ScreenPictureConfigDTO screenPictureConfigDTO) {
        List<ScreenPicDTO> screenPicDTOList = screenPictureConfigDTO.getScreenPicDTOList();
        List<ScreenPictureDO> list = this.list(new LambdaQueryWrapper<ScreenPictureDO>()
                .eq(ScreenPictureDO::getStoreGuid, screenPictureConfigDTO.getStoreGuid())
                .eq(ScreenPictureDO::getDeviceType, screenPictureConfigDTO.getDeviceType())
                .eq(ScreenPictureDO::getPicType, screenPictureConfigDTO.getPicType()));
        if (!CollectionUtils.isEmpty(list)) {
            if (CollectionUtils.isEmpty(screenPicDTOList)) {
                list.forEach(o -> fileUploadClient.delete(o.getOssUrl()));
                return this.removeByIds(list.stream().map(ScreenPictureDO::getScreenPictureGuid).collect(Collectors.toList()));
            }
            List<String> collect = list.stream().map(ScreenPictureDO::getOssUrl)
                    .filter(s ->
                            !screenPicDTOList.stream()
                                    .map(ScreenPicDTO::getOssUrl).collect(Collectors.toList()).contains(s)
                    ).collect(Collectors.toList());
            collect.forEach(s -> fileUploadClient.delete(s));
            this.removeByIds(list.stream().map(ScreenPictureDO::getScreenPictureGuid).collect(Collectors.toList()));
        }
        List<ScreenPictureDO> doList = Lists.newArrayList();
        screenPicDTOList.forEach(s -> {
            // 处理一下图片分辨率的逻辑
            if (screenPictureConfigDTO.getDeviceType() == BaseDeviceTypeEnum.All_IN_ONE.getCode()) {
                //一体机分辨率
                if (screenPictureConfigDTO.getPicType() == 1) {
                    s.setPxType(PictureEnum.TYPE_1024_600.getType());
                } else {
                    s.setPxType(PictureEnum.TYPE_635_600.getType());
                }
            } else if (screenPictureConfigDTO.getDeviceType() == BaseDeviceTypeEnum.KQS.getCode()) {
                // TV 分辨率
                if (screenPictureConfigDTO.getPicType() == 1) {
                    s.setPxType(PictureEnum.TYPE_1920_1080.getType());
                } else {
                    s.setPxType(PictureEnum.TYPE_650_690.getType());
                }
            } else {
                // 自主点餐机分辨率
                if (screenPictureConfigDTO.getPicType() == 1) {
                    s.setPxType(PictureEnum.TYPE_1080_1920.getType());
                }
            }
            PictureEnum byType = PictureEnum.getByType(s.getPxType());
            doList.add(ScreenPictureDO.builder()
                    .deviceType(screenPictureConfigDTO.getDeviceType())
                    .changeMills(screenPictureConfigDTO.getChangeMills())
                    .height(byType.getHeight())
                    .ossUrl(s.getOssUrl())
                    .picType(screenPictureConfigDTO.getPicType())
                    .pxType(s.getPxType())
                    .storeGuid(screenPictureConfigDTO.getStoreGuid())
                    .storeName(screenPictureConfigDTO.getStoreName())
                    .weight(byType.getWeight())
                    .screenPictureGuid(IdWorker.getId() + "")
                    .name(s.getName())
                    .build());
        });
        return saveBatch(doList);
    }

    private Boolean saveTime(ScreenPictureConfigDTO screenPictureConfigDTO) {
        ScreenPicTimeDO build = ScreenPicTimeDO.builder()
                .changeMills(screenPictureConfigDTO.getChangeMills())
                .deviceType(screenPictureConfigDTO.getDeviceType())
                .picTimeGuid(StringUtils.hasText(screenPictureConfigDTO.getPicTimeGuid()) ?
                        screenPictureConfigDTO.getPicTimeGuid() : IdWorker.getId() + "")
                .picType(screenPictureConfigDTO.getPicType())
                .storeGuid(screenPictureConfigDTO.getStoreGuid())
                .storeName(screenPictureConfigDTO.getStoreName())
                .displayType(screenPictureConfigDTO.getDisplayType())
                .build();
        return screenPicTimeService.saveOrUpdate(build);
    }

    @Override
    public String save(ScreenPictureDTO screenPictureDTO) {
        PictureInfoDTO pictureInfoDTO = screenPictureDTO.getPictureInfoDTO();
        Integer type = pictureInfoDTO.getPxType();
        Integer picType = pictureInfoDTO.getPicType();
        if (null == type | null == picType) {
            throw new BusinessException("type不能为空");
        }
        int countPics = screenPictureMapper.countPics(screenPictureDTO.getSelectStoreGuid(), picType);
        String screenPicGuid;
        if (StringUtils.isEmpty(screenPictureDTO.getScreenPictureGuid())) {
            screenPicGuid = nextScreenPicGuid();
            if (countPics >= 6) {
                // insert 新增超过6条返回失败 需要删除oss服务图片
                fileUploadClient.delete(screenPictureDTO.getOssUrl());
                return FAILURE;
            }
        } else {
            screenPicGuid = screenPictureDTO.getScreenPictureGuid();
            if (countPics >= 6) {
                ScreenPictureDO screenPictureDO = screenPictureMapper.queryOneByGuid(screenPicGuid);
                // update操作 删除原来oss服务图片
                fileUploadClient.delete(screenPictureDO.getOssUrl());
            }
        }
        ScreenPictureDO build = ScreenPictureDO.builder()
                .ossUrl(screenPictureDTO.getOssUrl())
                .storeGuid(screenPictureDTO.getSelectStoreGuid())
                .changeMills(screenPictureDTO.getChangeMills())
                .storeName(screenPictureDTO.getStoreName())
                .weight(PictureEnum.getByType(type).getWeight())
                .height(PictureEnum.getByType(type).getHeight())
                .pxType(type)
                .picType(picType)
                .build();
        build.setScreenPictureGuid(screenPicGuid);
        screenPictureMapper.insertOrUpdate(build);
        return SUCCESS;
    }

    @Override
    public String setTime(ScreenPicTimeDTO screenPicTimeDTO) {
        ScreenPicTimeDO screenPicTimeDO = SCREEN_PIC_MAP_STRUCT.picTimeDtoToPicTimeDo(screenPicTimeDTO);
        Integer changeMills = screenPicTimeDTO.getChangeMills();
        if (changeMills > 100 || changeMills < 1) {
            throw new BusinessException("输入的时间范围为1-99");
        }
        if (StringUtils.isEmpty(screenPicTimeDO.getPicTimeGuid())) {
            screenPicTimeDO.setPicTimeGuid(GuidUtils.nextScreenPicGuid());
            screenPictureMapper.insertPicTime(screenPicTimeDO);
        } else {
            screenPictureMapper.updatePicTime(screenPicTimeDO);
        }
        return SUCCESS;
    }

    @Override
    public List<ScreenAppRespDTO> queryByWeb(ScreenPicQuery screenPicQuery) {
        List<ScreenPictureDO> query = screenPictureMapper.queryByWeb(screenPicQuery);
        logger.info("查询的门店图片结果 result={}", JacksonUtils.writeValueAsString(query));
        return SCREEN_PIC_MAP_STRUCT.ScreenPicDosToDtos(query);
    }

    @Override
    public List<ScreenPicTimeDTO> getTime(ScreenPicTimeDTO screenPicTimeDTO) {
        List<ScreenPicTimeDO> screenPicTimeDOS = screenPictureMapper.queryPicTime(screenPicTimeDTO);
        return SCREEN_PIC_MAP_STRUCT.picTimeDosToDtos(screenPicTimeDOS);
    }

    @Override
    public ScreenPictureConfigDTO queryStoreConfig(ScreenPicConfigReqDTO query) {
        ScreenPicTimeDO one = screenPicTimeService.getOne(new LambdaQueryWrapper<ScreenPicTimeDO>()
                .eq(ScreenPicTimeDO::getStoreGuid, query.getStoreGuid())
                .eq(ScreenPicTimeDO::getDeviceType, query.getDeviceType())
                .eq(ScreenPicTimeDO::getPicType, PicTypeEnum.DISPLAY.getCode())
        );
        if (ObjectUtils.isEmpty(one)) {
            return null;
        }
        ScreenPictureConfigDTO configDTO = new ScreenPictureConfigDTO();
        configDTO.setScreenPicDTOList(new ArrayList<>());
        configDTO.setStoreGuid(one.getStoreGuid());
        configDTO.setStoreName(one.getStoreName());
        configDTO.setDeviceType(one.getDeviceType());
        configDTO.setPicType(one.getPicType());
        configDTO.setChangeMills(one.getChangeMills());
        configDTO.setPicTimeGuid(one.getPicTimeGuid());
        configDTO.setDisplayType(one.getDisplayType());
        return configDTO;
    }

//    @Override
//    public List<ScreenAppRespDTO> queryByAndroid(String storeGuid) {
//        List<ScreenPictureDO> query = screenPictureMapper.queryByAndroid(storeGuid);
//        query.stream()
//                .min(Comparator.comparing(ScreenPictureDO::getCreateTime))
//                .map(screenPictureDO -> {
//                    screenPictureDO.setAsFirst(true);
//                    return screenPictureDO;
//                });
//        return SCREEN_PIC_MAP_STRUCT.ScreenPicDosToDtos(query);
//    }

    @Override
    public List<ScreenAppRespDTO> queryByAndroid(String storeGuid) {
        String source = UserContextUtils.get().getSource();
        List<ScreenPictureDO> list = list(new LambdaQueryWrapper<ScreenPictureDO>()
                .eq(ScreenPictureDO::getStoreGuid, storeGuid)
                .eq(ScreenPictureDO::getDeviceType, Integer.valueOf(source)));
        list.stream()
                .min(Comparator.comparing(ScreenPictureDO::getCreateTime))
                .map(screenPictureDO -> {
                    screenPictureDO.setAsFirst(true);
                    return screenPictureDO;
                });
        return SCREEN_PIC_MAP_STRUCT.ScreenPicDosToDtos(list);
    }

    @Override
    public String delete(String screenPictureGuid) {
        String ossUrl = screenPictureMapper.getOssUrl(screenPictureGuid);
        screenPictureMapper.delete(screenPictureGuid);
        fileUploadClient.delete(ossUrl);
        return SUCCESS;
    }

    /**
     * 图片像素大小要求1920*1080
     * 图片文件大小小于1M
     *
     * @param file
     * @return
     * @throws IOException
     */
    private byte[] checkFile(MultipartFile file) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        if (file.getSize() > (1024 * 1000)) {
            byte[] bytes = compressPicForScale(file.getBytes(), 1024 * 1000);
            Thumbnails.of(new ByteArrayInputStream(bytes))
                    .forceSize(1920, 1080)
                    .outputQuality(1)
                    .toOutputStream(byteArrayOutputStream);
        } else {
            Thumbnails.of(file.getInputStream())
                    .forceSize(1920, 1080)
                    .outputQuality(1)
                    .toOutputStream(byteArrayOutputStream);
        }
        return byteArrayOutputStream.toByteArray();
    }

    public byte[] compressPicForScale(byte[] imageBytes, long desFileSize) {
        if (imageBytes == null || imageBytes.length <= 0 || imageBytes.length < desFileSize) {
            return imageBytes;
        }
        try {
            while (imageBytes.length > desFileSize) {
                double accuracy = getAccuracy(imageBytes.length);
                ByteArrayInputStream inputStream = new ByteArrayInputStream(imageBytes);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream(imageBytes.length);
                Thumbnails.of(inputStream)
                        .scale(accuracy)
                        .outputQuality(accuracy)
                        .toOutputStream(outputStream);
                imageBytes = outputStream.toByteArray();
            }
        } catch (Exception e) {
            logger.error("压缩图片异常！e={}", e.getMessage());
        }
        return imageBytes;
    }

}