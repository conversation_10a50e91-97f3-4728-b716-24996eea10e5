package com.holderzone.saas.store.business.service;

import com.holderzone.saas.store.dto.business.manage.StoreConfigCreateDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigUpdateDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.config.req.CashSettingReqDTO;
import com.holderzone.saas.store.dto.config.req.DineFoodSettingReqDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreConfigService
 * @date 2018/07/29 下午4:26
 * @description 门店配置接口
 * @program holder-saas-store-business-center
 */
public interface StoreConfigService {

    void create(StoreConfigCreateDTO storeConfigCreateDTO);

    StoreConfigDTO query(StoreConfigQueryDTO storeConfigQueryDTO);

    void update(StoreConfigUpdateDTO storeConfigUpdateDTO);

    List<StoreConfigDTO> list();

    List<StoreConfigDTO> queryByIdList(List<String> stringList);

    void init(BaseDTO baseDTO);

    void updateFinishFood(StoreConfigUpdateDTO storeConfigUpdateDTO);

    String queryPrintItemOrderConfig(StoreConfigQueryDTO storeConfigQueryDTO);

    void updatePrintItemOrderConfig(StoreConfigUpdateDTO storeConfigUpdateDTO);

    void updateDineFoodSetting(DineFoodSettingReqDTO settingReqDTO);

    void initCashSetting(CashSettingReqDTO cashSettingReqDTO);
}
