package com.holderzone.saas.store.business.queue.service.remote;

import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintClient
 * @date 2019/02/15 16:58
 * @description
 * @program holder-saas-store-table
 */
@Component
@FeignClient(value = "holder-saas-store-config", fallbackFactory = ConfigClient.ClientFallBack.class)
public interface ConfigClient {
    @PostMapping("/config/save_config")
    Integer saveConfig(@RequestBody @Valid ConfigReqDTO request);

    @PostMapping("/config/get_config")
    List<ConfigRespDTO> getConfig(ConfigReqDTO configReqDTO);

    @Slf4j
    @Component
    class ClientFallBack implements FallbackFactory<ConfigClient> {

        @Override
        public ConfigClient create(Throwable throwable) {
            return new ConfigClient() {
                @Override
                public Integer saveConfig(@Valid ConfigReqDTO request) {
                    log.error("保存配置失败 e={}", throwable);
                    throw new RuntimeException(throwable);
                }

                @Override
                public List<ConfigRespDTO> getConfig(ConfigReqDTO configReqDTO) {
                    log.error("获取配置失败 e={}", throwable);
                    throw new RuntimeException(throwable);
                }
            };

        }

    }

}
