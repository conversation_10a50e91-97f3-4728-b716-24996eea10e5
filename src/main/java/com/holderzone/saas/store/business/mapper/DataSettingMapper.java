package com.holderzone.saas.store.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.business.entity.domain.DataSettingDO;
import com.holderzone.saas.store.business.entity.domain.DataSettingQueryDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/7
 * @since 1.8
 */
@Mapper
@Repository
public interface DataSettingMapper extends BaseMapper<DataSettingDO> {

    List<DataSettingDO> findDataSetting(@Param("query") DataSettingQueryDO dataSettingDTO);
}
