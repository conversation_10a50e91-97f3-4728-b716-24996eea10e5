package com.holderzone.saas.store.business.queue.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.business.queue.domain.HolderQueueItemDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueItemMapper
 * @date 2019/03/27 16:55
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Repository
public interface QueueItemMapper extends BaseMapper<HolderQueueItemDO> {
    String getMaxOrder(@Param("queueGuid") String queueGuid);

    Integer getPreCount(@Param("queueGuid") String queueGuid, @Param("order") Integer order);
}