package com.holderzone.saas.store.business.queue.service;


import com.holderzone.saas.store.dto.queue.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueService
 * @date 2019/03/27 17:17
 * @description //TODO
 * @program holder-saas-store-queue
 */
public interface QueueService {

    String save(HolderQueueDTO dto);

    List<String> availableCode();

    Boolean enable(String guid);

    Boolean disable(String guid);

    Boolean delete(String guid);

    Boolean remove(List<String> guids);

    HolderQueueDTO fetchOne(String queueGuid);

    Boolean removeAll();

    void clean(String storeGuid);

    StoreQueueDTO all();

    List<QueueDetailDTO> query(String storeGuid);

    List<HolderQueueDTO> listByStore(String storeGuid);

    Boolean allEmpty(String storeGuid);

    QueueTableDTO saveTables(QueueTableDTO dto);

    List<TreeTableDTO> allTables(String storeGuid);

    QueueTableDTO obtain(String queueGuid);

    List<QueueTableDTO> queryByStore(String storeGuid);
}