package com.holderzone.saas.store.business.service.client;

import com.holderzone.saas.store.dto.business.manage.HandoverPayDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.netflix.hystrix.exception.HystrixBadRequestException;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeAwayClientService
 * @date 2018/09/12 14:07
 * @description
 * @program holder-saas-store-trading-center
 */
@Component
@FeignClient(name = "holder-saas-takeaway-consumer", fallbackFactory = TakeAwayClientService.TakeAwayFallBack.class)
public interface TakeAwayClientService {

    @PostMapping("/takeout/get_order_money")
    HandoverPayDTO getTakeAway(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO);

    @Component
    class TakeAwayFallBack implements FallbackFactory<TakeAwayClientService> {

        private static final Logger logger = LoggerFactory.getLogger(TakeAwayFallBack.class);

        @Override
        public TakeAwayClientService create(Throwable throwable) {
            return handoverPayQueryDTO -> {
                logger.error("调用外卖服务获取交接班数据异常！！！");
//                return null;
                throw new HystrixBadRequestException("调用外卖服务获取交接班数据异常");
            };
        }

    }

}
