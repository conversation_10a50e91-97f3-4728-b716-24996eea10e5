package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AccountRecordConfirmAgainException extends BusinessException {

    private static final long serialVersionUID = -3823416612907999142L;

    public AccountRecordConfirmAgainException() {
        super("营业日已扎帐，不能再次操作");
    }

    public AccountRecordConfirmAgainException(String message) {
        super("营业日["+message+"]已扎帐，不能再次操作");
    }

    public AccountRecordConfirmAgainException(String message, Throwable cause) {
        super(message, cause);
    }
}
