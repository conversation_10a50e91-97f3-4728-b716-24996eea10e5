package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FileIllegalException
 * @date 2018/09/06 20:01
 * @description //TODO
 * @program holder-saas-store-business
 */
public class FileIllegalException extends BusinessException {
    public FileIllegalException(String message) {
        super(message);
    }

    public FileIllegalException(String message, Throwable cause) {
        super(message, cause);
    }
}
