package com.holderzone.saas.store.business.mapper;

import com.holderzone.saas.store.business.entity.domain.StoreConfigDO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigDTO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreConfigMapper
 * @date 2018/07/29 下午4:30
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Mapper
@Repository
public interface StoreConfigMapper {

    int insert(StoreConfigDO storeConfigDO);

    StoreConfigDO query(StoreConfigDO storeConfigDO);

    long update(StoreConfigDO storeConfigDO);

    List<StoreConfigDO> queryAll();

    List<StoreConfigDO> queryList(List<String> stringList);
}
