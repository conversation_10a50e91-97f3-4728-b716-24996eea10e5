package com.holderzone.saas.store.business.event;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.business.constant.RocketMqConfig;
import com.holderzone.saas.store.dto.business.queue.MemberOperateRecordPushMqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2023/10/19
 * @description 操作记录
 */
public interface OperateRecordMqService {

    void saveRecord(MemberOperateRecordPushMqDTO operateRecordPushMqDTO);

    @Slf4j
    @Component
    class OperateRecordMqServiceImpl implements OperateRecordMqService {


        private final DefaultRocketMqProducer defaultRocketMqProducer;

        @Autowired
        public OperateRecordMqServiceImpl(DefaultRocketMqProducer defaultRocketMqProducer) {
            this.defaultRocketMqProducer = defaultRocketMqProducer;
        }

        @Override
        public void saveRecord(MemberOperateRecordPushMqDTO operateRecordPushMqDTO) {
            log.info("[会员操作记录]推送MQ消息={}", JacksonUtils.writeValueAsString(operateRecordPushMqDTO));
            Message message = new Message(
                    RocketMqConfig.MEMBER_OPERATE_RECORD_TOPIC,
                    RocketMqConfig.MEMBER_OPERATE_RECORD_SAVE_TAG,
                    JacksonUtils.toJsonByte(operateRecordPushMqDTO)
            );
            defaultRocketMqProducer.sendMessage(message);
        }
    }
}
