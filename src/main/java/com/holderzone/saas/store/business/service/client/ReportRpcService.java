package com.holderzone.saas.store.business.service.client;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.journaling.req.JournalStoreAppBaseReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.BusinessDataRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.HandOverRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.TotalPaymentRespDTO;
import com.holderzone.saas.store.dto.table.AreaDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className PrinterClient
 * @date 18-10-20 上午9:42
 * @description
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(value = "holder-saas-store-trade", fallbackFactory = ReportRpcService.ServiceFallBack.class)
public interface ReportRpcService {

    @PostMapping("/handover_sheet/handoverDaily")
    HandOverRespDTO handoverDaily(JournalStoreAppBaseReqDTO request);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ReportRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        private static final String HYSTRIX_SERVICE = "holder-saas-store-report";

        @Override
        public ReportRpcService create(Throwable cause) {
            return new ReportRpcService() {
                @Override
                public HandOverRespDTO handoverDaily(JournalStoreAppBaseReqDTO request) {
                    log.error(HYSTRIX_PATTERN, HYSTRIX_SERVICE ,JacksonUtils.writeValueAsString(request), cause.getMessage());
                    throw new ServerException();
                }
            };
        }
    }
}