package com.holderzone.saas.store.business.mapstruct;

import com.holderzone.saas.store.business.entity.domain.ScreenPictureDO;
import com.holderzone.saas.store.dto.business.manage.ScreenPicDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPicMapstruct
 * @date 2019/08/19 17:26
 * @description
 * @program holder-saas-store
 */
@Mapper
public interface ScreenPicMapstruct {

    ScreenPicMapstruct INSTANCE = Mappers.getMapper(ScreenPicMapstruct.class);

    ScreenPicDTO do2DTO(ScreenPictureDO screenPictureDO);

    ScreenPictureDO dto2DO(ScreenPicDTO screenPicDTO);

    List<ScreenPicDTO> doList2DTOList(List<ScreenPictureDO> doList);

    List<ScreenPictureDO> dtoList2DOList(List<ScreenPicDTO> dtoList);
}
