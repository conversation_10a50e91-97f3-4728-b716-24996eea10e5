package com.holderzone.saas.store.business.service;

import com.holderzone.saas.store.business.entity.dto.EstimateRecordCreateDTO;
import com.holderzone.saas.store.business.entity.dto.EstimateRecordDTO;
import com.holderzone.saas.store.business.entity.dto.EstimateRecordQuerryDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateService
 * @date 2018/08/06 上午11:56
 * @description //TODO
 * @program holder-saas-store-business
 */
public interface EstimateService {

    void createOrReplace(EstimateRecordCreateDTO estimateRecordCreateDTO);

    EstimateRecordDTO getByBusinessDay(EstimateRecordQuerryDTO estimateRecordQuerryDTO);
}
