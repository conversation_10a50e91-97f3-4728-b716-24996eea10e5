package com.holderzone.saas.store.business.mapper;

import com.holderzone.saas.store.business.entity.domain.HandoverPayDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className HandoverPayDetailMaper
 * @date 18-10-8 下午3:01
 * @description HandoverPayDetail表相关接口
 * @program holder-saas-store-staff
 */
@Mapper
@Repository
public interface HandoverPayDetailMapper {

    int batchInsert(List<HandoverPayDetailDO> handoverPayDetailDOList);

    List<HandoverPayDetailDO> selectPayDetailById(@Param("handoverRecordGuid") String handoverRecordGuid);

    int countByTypeAndId(@Param("handoverRecordGuid") String handoverRecordGuid,@Param("belongType") int belongType);
}
