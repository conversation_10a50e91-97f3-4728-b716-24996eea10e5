package com.holderzone.saas.store.business.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.holderzone.framework.util.ThrowableUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * 自定义线程池提供给扎帐 延迟队列使用
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class ThreadPoolConfig {

    @Bean
    public ThreadPoolExecutor threadPoolExecutor() {
        return new ThreadPoolExecutor(10, 50,
                100, TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(10000),
                Executors.defaultThreadFactory(), new ThreadPoolExecutor.AbortPolicy());
    }

    /**
     * 会员操作推送MQ消息
     */
    @Bean(value = "memberOperateRecordThreadPool")
    public ExecutorService memberOperateRecordThreadPool() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("member-operate-record-thread-%d").build();

        return new CustomThreadPoolExecutor(
                5, 20, 100L, TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(1500), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());
    }

    static class CustomThreadPoolExecutor extends ThreadPoolExecutor {
        public CustomThreadPoolExecutor(
                int corePoolSize,
                int maximumPoolSize,
                long keepAliveTime,
                TimeUnit unit,
                BlockingQueue<Runnable> workQueue,
                ThreadFactory threadFactory,
                RejectedExecutionHandler handler) {
            super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
        }

        @Override
        public void afterExecute(Runnable r, Throwable t) {
            super.afterExecute(r, t);
            if (t != null) {
                log.error("异步线程异常：{}", ThrowableUtils.asString(t));
            }
            // ... Perform task-specific cleanup actions
        }

        @Override
        public void terminated() {
            super.terminated();
            // ... Perform final clean-up actions
        }
    }
}
