package com.holderzone.saas.store.business.service.client;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.manage.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @className SmsRechargeClient
 * @date 18-9-10 下午4:59
 * @description 服务间调用-短信充值服务
 * @program holder-saas-store-business
 */
@Component
@FeignClient(name = "holder-saas-cloud-product", fallbackFactory = SmsRechargeClient.SmsRechargeClientFallBack.class)
public interface SmsRechargeClient {

    @GetMapping(value = "/product/sms/chargelist")
    List<ChargeDTO> getChargeListForSMS();

    @Slf4j
    @Component
    class SmsRechargeClientFallBack implements FallbackFactory<SmsRechargeClient> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public SmsRechargeClient create(Throwable cause) {
            return new SmsRechargeClient() {
                @Override
                public List<ChargeDTO> getChargeListForSMS() {
                    if (log.isWarnEnabled()) {
                        log.warn(HYSTRIX_PATTERN, "getChargeListForSMS", null, ThrowableUtils.asString(cause));
                    }
                    cause.printStackTrace();
                    throw new BusinessException("调用云端异常！");
                }
            };
        }
    }
}
