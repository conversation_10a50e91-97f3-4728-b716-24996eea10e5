package com.holderzone.saas.store.business.controller;

import com.holderzone.saas.store.business.entity.dto.EstimateRecordCreateDTO;
import com.holderzone.saas.store.business.entity.dto.EstimateRecordDTO;
import com.holderzone.saas.store.business.entity.dto.EstimateRecordQuerryDTO;
import com.holderzone.saas.store.business.service.EstimateService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateController
 * @date 2018/08/06 上午11:50
 * @description 估清Controller
 * @program holder-saas-store-business
 */
@RestController
@RequestMapping("/estimate")
public class EstimateController {

    private final EstimateService estimateService;

    @Autowired
    public EstimateController(EstimateService estimateService) {
        this.estimateService = estimateService;
    }

    @PostMapping("/createOrReplace")
    @ApiOperation(value = "创建或替换估清记录（包括该记录下的估清菜品列表）", notes = "创建或替换估清记录（包括该记录下的估清菜品列表）")
    public void createOrReplace(@RequestBody @Validated EstimateRecordCreateDTO estimateRecordCreateDTO) {
        estimateService.createOrReplace(estimateRecordCreateDTO);
    }

    @PostMapping("/getByBusinessDay")
    @ApiOperation(value = "根据营业日获取估清记录（包括该记录下的估清菜品列表）", notes = "根据营业日获取估清记录（包括该记录下的估清菜品列表）")
    public EstimateRecordDTO getByBusinessDay(@RequestBody @Validated EstimateRecordQuerryDTO estimateRecordQuerryDTO) {
        return estimateService.getByBusinessDay(estimateRecordQuerryDTO);
    }
}
