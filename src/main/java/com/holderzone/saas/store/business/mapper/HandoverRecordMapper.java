package com.holderzone.saas.store.business.mapper;

import com.holderzone.saas.store.business.entity.domain.HandoverRecordDO;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.report.query.HandOverReportQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverRecordMapper
 * @date 2018/07/29 上午11:50
 * @description HandoverRecord表相关接口
 * @program holder-saas-store-business-center
 */
@Mapper
@Repository
public interface HandoverRecordMapper {
    int insert(HandoverRecordDO handoverRecordDO);

    long update(HandoverRecordDO handoverRecordDO);

    HandoverRecordDO query(HandoverRecordDO handoverRecordDO);

    List<HandoverRecordDO> queryAll(HandoverRecordDO handoverRecordDO);

    List<HandoverRecordDO> queryByPage(HandoverRecordQueryAllDTO handoverRecordQueryAllDTO);

    List<HandoverRecordDTO> queryByPageNew(HandoverRecordQueryAllDTO handoverRecordQueryAllDTO);

    Long count(@Param("storeGuid") String storeGuid);

    Long countByTerminalIdAndStoreId(@Param("terminalId") String terminalId, @Param("storeGuid") String storeGuid);

    List<HandoverRecordDO> queryAllByStoreList(List<String> storeGuidList);

    HandoverRecordDO queryByUserGuidAndTerminaId(@Param("userGuid") String userGuid, @Param("terminalId") String terminalId,
                                                 @Param("storeGuid") String storeGuid);

    List<HandoverRecordDO> queryByUserGuid(HandoverRecordConfirmDTO handoverRecordConfirmDTO);

    HandoverRecordDO queryByUserStoreGuidAndTerminalId(@Param("storeGuid") String storeGuid, @Param("terminalId") String terminalId);

    HandoverRecordDO queryByStoreGuidAndUserGuid(@Param("storeGuid") String storeGuid, @Param("userGuid") String userGuid);

    List<HandoverRecordDO> queryByCondition(@Param("dto") HandOverQueryBO queryBO);

    List<HandoverRecordDO> queryNew(@Param("handoverRecordGuid") String handoverRecordGuid);
}
