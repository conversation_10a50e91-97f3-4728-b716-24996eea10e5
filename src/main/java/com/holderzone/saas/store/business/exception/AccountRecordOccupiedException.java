package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AccountRecordOccupiedException extends BusinessException {

    private static final long serialVersionUID = 4332622053569292476L;

    public AccountRecordOccupiedException() {
        super("门店有正在营业的记录，不能撤消记录");
    }

    public AccountRecordOccupiedException(String message) {
        super("门店有正在营业的记录["+message+"]，不能撤消记录");
    }

    public AccountRecordOccupiedException(String message, Throwable cause) {
        super(message, cause);
    }
}
