package com.holderzone.saas.store.business.mapper;

import com.holderzone.saas.store.business.entity.domain.EstimateDishDO;
import com.holderzone.saas.store.business.entity.domain.EstimateRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateMapper
 * @date 2018/08/06 下午4:19
 * @description //TODO
 * @program holder-saas-store-business
 */
@Mapper
@Repository
public interface EstimateMapper {

    void insertRecord(EstimateRecordDO estimateRecordDO);

    EstimateRecordDO findByBusinessDay(EstimateRecordDO estimateRecordDO);

    void batchInsertDishes(List<EstimateDishDO> estimateDishDOS);

    void batchDeleteDishes(String estimateRecordGuid);

    List<EstimateDishDO> queryDishesByRecordGuid(String estimateRecordGuid);
}
