package com.holderzone.saas.store.business.queue.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HolderQueueDO
 * @date 2019/03/27 16:42
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
@Accessors(chain = true)
@TableName("hsq_r_queue_table")
public class HolderQueueTableDO implements Serializable {
    @TableId
    private Long id;
    private String guid;
    private String queueGuid;

    private String tableGuid;
    private String tableName;
    private String areaGuid;
    private String areaName;
    private Integer seats;

    /**
     * 是否删除，1-已删除，0-未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建人guid
     */
    private String createStaffGuid;

    /**
     * 更新人guid
     */
    private String modifiedStaffGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;
}