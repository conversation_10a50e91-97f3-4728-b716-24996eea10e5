package com.holderzone.saas.store.business.event;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.saas.store.business.constant.RocketMqConfig;
import com.holderzone.saas.store.business.enums.CateringReasonEnum;
import com.holderzone.saas.store.business.enums.ReasonEnum;
import com.holderzone.saas.store.business.service.impl.ReasonServiceImpl;
import com.holderzone.saas.store.business.utils.ThrowableExtUtils;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.enums.MchntTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreCreateListener
 * @date 2019/10/29 16:36
 * @description 门店创建消息监听器
 * @program holder-saas-store
 */
@Component
@Slf4j
@RocketListenerHandler(
        topic = RocketMqConfig.DOWNSTREAM_STORE_TOPIC,
        tags = RocketMqConfig.DOWNSTREAM_STORE_CREATE_TAG,
        consumerGroup = RocketMqConfig.DOWNSTREAM_STORE_INIT_REASON_GROUP
)
public class ReasonStoreCreateListener extends AbstractRocketMqConsumer<RocketMqTopic, StoreDTO> {

    private final ReasonServiceImpl reasonService;

    @Autowired
    public ReasonStoreCreateListener(ReasonServiceImpl reasonService) {
        this.reasonService = reasonService;
    }

    @Override
    public boolean consumeMsg(StoreDTO storeDTO, MessageExt messageExt) {
        UserContextUtils.put(messageExt.getProperty(MqConstant.DOWNSTREAM_CONTEXT));
        try {
            EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            if (Objects.equals(storeDTO.getMchntTypeCode(), MchntTypeEnum.CATERING.getCode())) {
                CateringReasonEnum.getDefaultReason()
                        .forEach(r -> {
                            r.setStoreGuid(storeDTO.getGuid());
                            reasonService.insertReason(r);
                        });
            } else if (Objects.equals(storeDTO.getMchntTypeCode(), MchntTypeEnum.RETAIL.getCode())) {
                ReasonEnum.getReason().forEach(r -> {
                    r.setStoreGuid(storeDTO.getGuid());
                    reasonService.insertReason(r);
                });
            } else {
                CateringReasonEnum.getDefaultReason().forEach(r -> {
                    r.setStoreGuid(storeDTO.getGuid());
                    reasonService.insertReason(r);
                });
            }
            log.info("门店：{} 初始化原因字典成功", storeDTO.getName());
        } catch (BusinessException e) {
            log.error("门店：{} 初始化原因字典发生错误：{}，初始化消息已消费", storeDTO.getName(), ThrowableExtUtils.asStringIfAbsent(e));
            return true;
        } catch (Exception e) {
            log.error("门店：{} 初始化原因字典发生错误：{}", storeDTO.getName(), ThrowableExtUtils.asStringIfAbsent(e));
            return false;
        } finally {
            EnterpriseIdentifier.remove();
        }
        return true;
    }
}
