package com.holderzone.saas.store.business.entity.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateDishDO
 * @date 2018/08/06 上午11:33
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EstimateDishDO  {

    /**
     * 估清记录guid
     */
    private String estimateRecordGuid;

    /**
     * 菜品id
     */
    private Long dishId;

    /**
     * 估清数量
     */
    private BigDecimal estimateCount;

    /**
     * 报警数量
     */
    private BigDecimal warningCount;

    /**
     * 销售状态
     * 0=停售
     * 1=在售
     */
    private Integer status;
}
