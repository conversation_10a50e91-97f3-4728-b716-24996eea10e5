package com.holderzone.saas.store.business.queue.config;


import lombok.extern.slf4j.Slf4j;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.LocaleResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

@Component
@Slf4j
public class I18nLocalResolver implements LocaleResolver {

    public static final String DEFAULT_PARAM_NAME = "language"; //多语言切换参数
    public static final String DEFAULT_NAME = "zh-CN"; //多语言切换参数

    @Override
    public Locale resolveLocale(HttpServletRequest request) {

        Locale locale = Locale.SIMPLIFIED_CHINESE; //设置默认国际化语言为简体中文
        String lang = request.getHeader(DEFAULT_PARAM_NAME); // 从头部获取语言类型

        if(StringUtils.isEmpty(lang)){
            return locale;  //设置默认国际化语言为简体中文
        }
        //多语言切换参数
        if(!DEFAULT_NAME.equalsIgnoreCase(lang)){
            locale = Locale.US;
        }
        return locale;
    }

    @Override
    public void setLocale(HttpServletRequest request, HttpServletResponse response, Locale locale) {
        // 设置语言环境的上下文
        LocaleContextHolder.setLocale(locale);
    }
}