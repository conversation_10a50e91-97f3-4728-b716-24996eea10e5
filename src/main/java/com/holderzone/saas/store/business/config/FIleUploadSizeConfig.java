package com.holderzone.saas.store.business.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.MultipartConfigElement;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FIleUploadSizeConfig
 * @date 2018/09/07 10:06
 * @description //TODO
 * @program holder-saas-store-business
 */
@Configuration
public class FIleUploadSizeConfig {

    @Value("${file.size}")
    private String uploadSize;

    @Value("${file.total.size}")
    private String uploadTotalSize;


    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        //文件最大
        factory.setMaxFileSize(uploadSize); //KB,MB
        /// 设置总上传数据总大小
        factory.setMaxRequestSize(uploadTotalSize);
        return factory.createMultipartConfig();
    }



}
