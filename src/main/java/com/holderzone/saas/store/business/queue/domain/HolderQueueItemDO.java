package com.holderzone.saas.store.business.queue.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HolderQueueDO
 * @date 2019/03/27 16:42
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
@Accessors(chain = true)
@TableName("hsq_queue_item")
public class HolderQueueItemDO {
    @TableId
    private Long id;
    private String guid;
    private String storeGuid;
    private String queueGuid;

    private Byte status;
    private String contact;
    private String phone;
    private Boolean gender;
    private String remark;
    private Byte peopleNumber;
    private Float sort;
    private String queueCode;
    private String code;

    private Integer callTimes = 0;

    private LocalDateTime startTime;
    @TableField(strategy = FieldStrategy.IGNORED)
    private LocalDateTime endTime;
    /**
     * 二维码链接
     */
    private String qrCodeUrl;

    /**
     * 营业日
     */
    private LocalDate businessDay;
    /**
     * 是否启用，1-已启用，0-未启用
     */
    private Boolean isEnable;

    /**
     * 是否删除，1-已删除，0-未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建人guid
     */
    private String createStaffGuid;

    /**
     * 更新人guid
     */
    private String modifiedStaffGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    private LocalDateTime cancelTime;

    /**
     * "设备类型:PC服务端- 0,PC平板- 1,小店通- 2,一体机- 3,POS机- 4,云平板- 5,点菜宝(M1)- 6,PV1(带刷卡的点菜宝)- 7,微信- 8；",
     */
    private Integer deviceType;

    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 桌台名称
     */
    private String tableName;
    /**
     * 桌台guid
     */
    private String tableGuid;
    private String areaName;
    private String areaGuid;
}