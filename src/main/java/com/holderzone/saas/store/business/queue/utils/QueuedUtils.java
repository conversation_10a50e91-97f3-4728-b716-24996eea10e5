package com.holderzone.saas.store.business.queue.utils;

import com.holderzone.saas.store.business.queue.helper.DynamicHelper;
import com.holderzone.sdk.util.BatchIdGenerator;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueuedUtils
 * @date 2018/07/27 下午5:55
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public final class QueuedUtils {
    private static final String QUEUE_KEY = "queue";
    private static final String QUEUE_ITEM_KEY = "queue_item";
    private static final String QUEUE_CONFIG_KEY = "queue_config";
    private static DynamicHelper dynamicHelper;

    private QueuedUtils() {
    }

    /**
     * 生成唯一队列类型guid
     *
     * @return
     */
    public static String nextTypeGuid() {
        return dynamicHelper().generateGuid(QUEUE_KEY);
    }

    public static String nextConfigGuid() {
        return nextConfigGuid(1).get(0);
    }

    public static List<String> nextConfigGuid(int num) {
        try {
            List<Long> guids = BatchIdGenerator.batchGetGuids(SpringContextUtils.getInstance().getBean("redisTemplate"), QUEUE_CONFIG_KEY, num);
            return guids.stream().map(e -> e.toString()).collect(Collectors.toList());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Collections.emptyList();
    }

    /**
     * 生成唯一队列记录guid
     *
     * @return
     */
    public static String nextRecordGuid() {
        return dynamicHelper().generateGuid(QUEUE_ITEM_KEY);
    }

    public static DynamicHelper dynamicHelper() {
        if (dynamicHelper == null) {
            dynamicHelper = SpringContextUtils.getInstance().getBean("dynamicHelper");
        }
        return dynamicHelper;
    }
}
