package com.holderzone.saas.store.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.business.entity.domain.MemberOperateRecordDO;
import com.holderzone.saas.store.business.mapper.MemberOperateRecordMapper;
import com.holderzone.saas.store.business.service.MemberOperateRecordService;
import com.holderzone.saas.store.business.service.RedisService;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.trade.req.record.MemberOperateRecordReqDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;

/**
 * <p>
 * 会员操作记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Slf4j
@Service
@AllArgsConstructor
public class MemberOperateRecordServiceImpl extends ServiceImpl<MemberOperateRecordMapper, MemberOperateRecordDO> implements MemberOperateRecordService {

    private MemberOperateRecordMapper memberOperateRecordMapper;

    private final RedisService redisService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRecord(MemberOperateRecordReqDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO)) {
            return;
        }

        MemberOperateRecordDO recordDO = buildSaveDO(reqDTO);
        memberOperateRecordMapper.insert(recordDO);
    }

    @NotNull
    private MemberOperateRecordDO buildSaveDO(MemberOperateRecordReqDTO reqDTO) {
        MemberOperateRecordDO recordDO = new MemberOperateRecordDO();
        String guid = String.valueOf(redisService.nextMemberOperateRecordGuid());
        recordDO.setGuid(guid);
        recordDO.setGmtCreate(LocalDateTime.now());
        recordDO.setGmtModified(LocalDateTime.now());
        recordDO.setIsDelete(BooleanEnum.FALSE.getCode());
        recordDO.setDeviceType(reqDTO.getDeviceType());
        recordDO.setModuleType(reqDTO.getModuleType());
        recordDO.setLoginType(reqDTO.getLoginType());
        recordDO.setTradeMode(reqDTO.getTradeMode());
        recordDO.setOperateTime(LocalDateTime.now());
        recordDO.setOperatorGuid(reqDTO.getOperatorGuid());
        recordDO.setOperatorName(reqDTO.getOperatorName());
        String phoneNumOrCardNum = reqDTO.getPhoneNum();
        if (phoneNumOrCardNum.length() == Constant.PHONE_COUNT) {
            // 电话号码
            recordDO.setPhoneNum(phoneNumOrCardNum);
        } else if (phoneNumOrCardNum.length() > Constant.OPENID_COUNT_LIMIT) {
            // OPENID 通过会员微信表查询会员
            recordDO.setMemberOpenid(phoneNumOrCardNum);
        } else {
            // 9位10位12位卡号
            recordDO.setCardNum(phoneNumOrCardNum);
        }
        recordDO.setOrderGuid(reqDTO.getOrderGuid());
        recordDO.setStoreGuid(reqDTO.getStoreGuid());
        recordDO.setStoreName(reqDTO.getStoreName());
        return recordDO;
    }
}
