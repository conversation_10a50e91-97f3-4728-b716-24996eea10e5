package com.holderzone.saas.store.business.queue.service.remote;

import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrganizationClientService
 * @date 2019/02/18 10:30
 * @description //TODO
 * @program ${MODULE_NAME}
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = OrganizationClientService.OrganizationFullback.class)
public interface OrganizationClientService {

    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    @PostMapping("/store/query_store_by_brandlist")
    List<StoreDTO> queryStoreByBrand(List<String> brandList);

    @PostMapping("/brand/query_brand_by_guid")
    BrandDTO queryBrandByGuid(@RequestParam("brandGuid") String brandGuid);

    @Component
    @Slf4j
    class OrganizationFullback implements FallbackFactory<OrganizationClientService> {
        @Override
        public OrganizationClientService create(Throwable throwable) {
            return new OrganizationClientService() {
                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    log.error("通过门店guid查询门店失败,", throwable);
                    throw new RuntimeException("通过门店guid查询门店失败,e:{}" + throwable.getMessage());
                }

                @Override
                public List<StoreDTO> queryStoreByBrand(List<String> brandList) {
                    log.error("通过品牌guid查询门店失败,", throwable);
                    throw new RuntimeException("通过品牌guid查询门店失败,e:{}" + throwable.getMessage());
                }

                @Override
                public BrandDTO queryBrandByGuid(String brandGuid) {
                    log.error("通过品牌guid查询品牌失败,", throwable);
                    throw new RuntimeException("通过品牌guid查询品牌失败,e:{}" + throwable.getMessage());
                }
            };
        }
    }
}
