package com.holderzone.saas.store.business.enums;

import com.holderzone.framework.exception.unchecked.BusinessException;
import lombok.Getter;

/**
 * 时间限制单位枚举
 *
 * <AUTHOR>
 * @since 2025/7/1
 */
@Getter
public enum TimeLimitUnitEnum {

    HOUR(0, "小时"),
    DAY(1, "天");

    private final Integer code;
    private final String desc;

    TimeLimitUnitEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取描述
     *
     * @param code code
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        for (TimeLimitUnitEnum value : TimeLimitUnitEnum.values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }

    /**
     * 根据描述获取code
     *
     * @param desc 描述
     * @return code
     */
    public static Integer getCodeByDesc(String desc) {
        for (TimeLimitUnitEnum value : TimeLimitUnitEnum.values()) {
            if (value.desc.equals(desc)) {
                return value.code;
            }
        }
        return null;
    }

    /**
     * 校验是否是合法的code
     *
     * @param code code
     */
    public static void isValidCode(Integer code) {
        for (TimeLimitUnitEnum value : TimeLimitUnitEnum.values()) {
            if (value.code.equals(code)) {
                return;
            }
        }
        throw new BusinessException("非法的时间限制单位");
    }
}
