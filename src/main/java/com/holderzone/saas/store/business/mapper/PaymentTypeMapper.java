package com.holderzone.saas.store.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.saas.store.business.entity.domain.PaymentTypeDO;
import com.holderzone.saas.store.business.utils.PageAdapter;
import com.holderzone.saas.store.dto.journaling.req.PaySerialStatisticsReqDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeModeDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeMapper
 * @date 2018/08/27 10:40
 * @description
 * @program holder-saas-store-trading-center
 */
@Repository
public interface PaymentTypeMapper extends BaseMapper<PaymentTypeDO> {

    void add(PaymentTypeDO paymentTypeDO);

    void update(PaymentTypeDO paymentTypeDO);

    void delete(@Param("storeGuid") String storeGuid, @Param("paymentTypeGuid") String paymentTypeGuid);

    List<PaymentTypeDTO> getAll(@Param("storeGuid") String storeGuid, @Param("source") Integer source);

    PaymentTypeDTO getPaymentTypeInfo(@Param("storeGuid") String storeGuid, @Param("paymentType") Integer paymentType);

    PaymentTypeDO getMaxSortingAndMaxPaymentType(@Param("storeGuid") String storeGuid);

    void updateAllSort(List<PaymentTypeDTO> paymentTypeDTOS);

    void addAll(List<PaymentTypeDO> paymentTypeDOS);

    PaymentTypeDTO getOne(PaymentTypeDTO paymentTypeDTO);

    Set<String> getAllStorePayNames(List<String> storeGuids);

    Integer countByStoreGuidInAndName(@Param("list") List<String> storeGuids, @Param("name") String name, @Param("guid") String guid);

    Integer queryDefauktPayTypeExist(String storeGuid);

    IPage<PaymentTypeDTO> getMultiStorePayWay(PageAdapter page, @Param("dto") PaySerialStatisticsReqDTO paySerialStatisticsReqDTO);

    void updatePaymentTypeMode(PaymentTypeModeDTO modeDTO);

    void updateShunt(@Param("paymentTypeGuid") String paymentTypeGuid, @Param("paymentShunt") Integer paymentShunt,
                     @Param("storeGuid") String storeGuid);
}
