package com.holderzone.saas.store.business.config;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.sdk.util.BatchIdGenerator;
import org.springframework.stereotype.Component;
import java.util.List;
/**
 * <AUTHOR>
 * @version 1.0
 * @className RedisIDGenerator
 * @date 2018/12/19 15:29
 * @description
 * @program holder-saas-store-trading-center
 */
@Component
public class RedisIDGenerator {


    public List<Long> getBatchIds(long size, String tableName) {

        return BatchIdGenerator.buildSnowFlakeGuids(tableName, size);
    }

    public long getSingle(String tableName) {
        try {
            return BatchIdGenerator.buildSnowFlakeGuids(tableName,1).get(0);
        } catch (Exception e) {
            throw new BusinessException("guid生成失败");
        }
    }

}
