package com.holderzone.saas.store.business.event;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.business.constant.RocketMqConfig;
import com.holderzone.saas.store.business.service.MemberOperateRecordService;
import com.holderzone.saas.store.business.utils.DynamicHelper;
import com.holderzone.saas.store.dto.business.queue.MemberOperateRecordPushMqDTO;
import com.holderzone.saas.store.dto.trade.req.record.MemberOperateRecordReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;


/**
 * <AUTHOR>
 * @date 2023/10/19
 * @description
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.MEMBER_OPERATE_RECORD_TOPIC,
        tags = RocketMqConfig.MEMBER_OPERATE_RECORD_SAVE_TAG,
        consumerGroup = RocketMqConfig.MEMBER_OPERATE_RECORD_GROUP)
@RefreshScope
public class OperateRecordMqListener extends AbstractRocketMqConsumer<RocketMqTopic, MemberOperateRecordPushMqDTO> {

    private final MemberOperateRecordService memberOperateRecordService;

    private final DynamicHelper dynamicHelper;

    @Autowired
    public OperateRecordMqListener(MemberOperateRecordService memberOperateRecordService,
                                   DynamicHelper dynamicHelper) {
        this.memberOperateRecordService = memberOperateRecordService;
        this.dynamicHelper = dynamicHelper;
    }

    @Override
    public boolean consumeMsg(MemberOperateRecordPushMqDTO operateRecordPushMqDTO, MessageExt messageExt) {
        log.info("[会员操作记录]消费Mq={}", JacksonUtils.writeValueAsString(operateRecordPushMqDTO));
        String contextJson = operateRecordPushMqDTO.getUserContextJson();
        MemberOperateRecordReqDTO operateRecordReqDTO = operateRecordPushMqDTO.getOperateRecordReqDTO();
        if (StringUtils.isEmpty(contextJson) || ObjectUtils.isEmpty(operateRecordReqDTO)) {
            log.error("[会员操作记录]消费参数异常");
            return false;
        }
        UserContext userContext = JacksonUtils.toObject(UserContext.class, contextJson);
        UserContextUtils.put(userContext);
        dynamicHelper.changeDatasource(userContext.getEnterpriseGuid());
        memberOperateRecordService.saveRecord(operateRecordReqDTO);
        log.info("[会员操作记录]消费成功");
        return true;
    }


}
