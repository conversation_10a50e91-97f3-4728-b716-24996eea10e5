package com.holderzone.saas.store.business.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.business.service.SurchargeService;
import com.holderzone.saas.store.business.utils.DynamicHelper;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeAggDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeCalculateDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeSyncDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreConfigController
 * @date 2018/07/29 下午4:25
 * @description 附加费Controller
 * @program holder-saas-store-business-center
 */
@Slf4j
@RestController
@RequestMapping("/surcharge")
@Api(description = "附加费相关Api")
public class SurchargeController {

    private final SurchargeService surchargeService;

    private final DynamicHelper dynamicHelper;

    @Autowired
    public SurchargeController(SurchargeService surchargeService, DynamicHelper dynamicHelper) {
        this.surchargeService = surchargeService;
        this.dynamicHelper = dynamicHelper;
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建附加费", notes = "创建附加费")
    public String addSurcharge(@RequestBody @Validated SurchargeCreateDTO surchargeCreateDTO) {
        return surchargeService.addSurcharge(surchargeCreateDTO);
    }

    @PostMapping("/query")
    @ApiOperation(value = "查询附加费", notes = "查询附加费")
    public SurchargeDTO querySurcharge(@RequestBody @Validated SurchargeQueryDTO surchargeQueryDTO) {
        return surchargeService.querySurcharge(surchargeQueryDTO);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新附加费", notes = "更新附加费")
    public void updateSurcharge(@RequestBody @Validated SurchargeUpdateDTO surchargeUpdateDTO) {
        surchargeService.updateSurcharge(surchargeUpdateDTO);
    }

    @PostMapping("/enable")
    @ApiOperation(value = "启用/禁用附加费", notes = "启用/禁用附加费")
    public void enableSurcharge(@RequestBody @Validated SurchargeEnableDTO surchargeEnableDTO) {
        surchargeService.enableSurcharge(surchargeEnableDTO);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除附加费", notes = "删除附加费")
    public void deleteSurcharge(@RequestBody @Validated SurchargeDeleteDTO surchargeDeleteDTO) {
        surchargeService.deleteSurcharge(surchargeDeleteDTO);
    }

    @PostMapping("/batch_enable")
    @ApiOperation(value = "批量启用/禁用附加费", notes = "批量启用/禁用附加费")
    public void batchEnableSurcharge(@RequestBody @Validated SurchargeBatchEnableDTO surchargeBatchEnableDTO) {
        surchargeService.batchEnableSurcharge(surchargeBatchEnableDTO);
    }

    @PostMapping("/batch_delete")
    @ApiOperation(value = "批量删除附加费", notes = "批量删除附加费")
    public void batchDelete(@RequestBody @Validated SurchargeBatchDeleteDTO surchargeBatchDeleteDTO) {
        surchargeService.batchDeleteSurcharge(surchargeBatchDeleteDTO);
    }

    @PostMapping("/list_by_type")
    @ApiOperation(value = "按照附加费类型查询列表", notes = "按照附加费类型查询列表")
    public Page<SurchargeDTO> listByType(@RequestBody @Validated SurchargeListDTO surchargeListDTO) {
        return surchargeService.listByType(surchargeListDTO);
    }

    @PostMapping("/list_by_area_guid")
    @ApiOperation(value = "查询区域内的附加费用", notes = "查询区域内的附加费用")
    public Map<String, List<SurchargeLinkDTO>> listByAreaGuid(@RequestBody List<String> areaList) {
        return surchargeService.listByAreaGuid(areaList, null);
    }

    @PostMapping("/list_by_area_store/{storeGuid}")
    public Map<String, List<SurchargeLinkDTO>> listByAreaGuidAndStore(@PathVariable("storeGuid") String storeGuid, @RequestBody List<String> areaList) {
        return surchargeService.listByAreaGuidAndStore(storeGuid, areaList);
    }

    @PostMapping("/sync")
    @ApiOperation(value = "附加费用同步接口", notes = "附加费用同步接口")
    public SurchargeAggDTO sync(@RequestBody SurchargeSyncDTO surchargeDTO) {
        return surchargeService.sync(surchargeDTO);
    }

    /**
     * 通过门店和桌台查询附加费信息
     *
     * @param surchargeDTO 入参
     * @return 附加费信息
     */
    @PostMapping("/calculate_surcharge")
    @ApiOperation(value = "通过门店和桌台以及人数计算附加费")
    public List<SurchargeLinkDTO> calculateSurcharge(@RequestBody SurchargeCalculateDTO surchargeDTO) {
        log.info("通过门店和桌台以及人数计算附加费入参 surchargeDTO={}", JacksonUtils.writeValueAsString(surchargeDTO));
        UserContextUtils.putErp(surchargeDTO.getEnterpriseGuid());
        dynamicHelper.changeDatasource(surchargeDTO.getEnterpriseGuid());
        return surchargeService.calculateSurcharge(surchargeDTO);
    }

    @PostMapping("/list_by_condition")
    @ApiOperation(value = "根据条件查询收费规则")
    public List<SurchargeLinkDTO> listByCondition(@RequestBody SurchargeConditionQuery query) {
        log.info("[根据条件查询收费规则]入参,query={}", JacksonUtils.writeValueAsString(query));
        return surchargeService.listByCondition(query);
    }

}
