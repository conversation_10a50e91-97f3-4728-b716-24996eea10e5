package com.holderzone.saas.store.business.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateRecordDO
 * @date 2018/08/06 上午11:33
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class EstimateRecordDTO  implements Serializable {

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 估清记录guid
     */
    @ApiModelProperty(value = "估清记录guid")
    private String estimateRecordGuid;

    /**
     * 创建人guid
     */
    @ApiModelProperty(value = "创建人guid")
    private String createUserGuid;

    /**
     * 创建人名字
     */
    @ApiModelProperty(value = "创建人名字")
    private String createUserName;

    /**
     * 营业日日期
     */
    @ApiModelProperty(value = "营业日日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate businessDay;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime gmtModified;

    /**
     * 该估清记录下所设置的估清菜品列表
     */
    @ApiModelProperty(value = "该估清记录下所设置的估清菜品列表")
    private List<EstimateDishDTO> estimateDishes;

    /**
     * 空 估清记录
     *
     * @return
     */
    public static EstimateRecordDTO empty() {
        EstimateRecordDTO estimateRecordDTO = new EstimateRecordDTO();
        estimateRecordDTO.setEstimateDishes(Collections.emptyList());
        return estimateRecordDTO;
    }

    /**
     * 空 菜品估清列表
     * @return
     */
    public EstimateRecordDTO emptyDishes() {
        estimateDishes = Collections.emptyList();
        return this;
    }

    /**
     * 空 菜品估清列表
     * @return
     */
    public EstimateRecordDTO dishes(List<EstimateDishDTO> estimateDishDTOS) {
        estimateDishes = estimateDishDTOS;
        return this;
    }
}
