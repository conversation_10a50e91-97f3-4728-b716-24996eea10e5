package com.holderzone.saas.store.business.entity.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CashboxRecordDO
 * @date 2018/07/29 下午5:42
 * @description CashboxRecord数据库对应DO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CashboxRecordDO  {

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 设备Id
     */
    private String terminalId;

    /**
     * 交接班guid
     */
    private String handoverRecordGuid;

    /**
     * 钱箱记录guid
     */
    private String cashboxRecordGuid;

    /**
     * 用户guid
     */
    private String userGuid;

    /**
     * 用户名字
     */
    private String userName;

    /**
     * 操作类型
     * 0=存入
     * 1=取出
     */
    private Integer operationType;

    /**
     * 来源类型
     * 0=开钱箱存取款
     * 1=收银存取款
     */
    private Integer sourceType;

    /**
     * 现金
     */
    private BigDecimal money;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
