package com.holderzone.saas.store.business.utils;

import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DynamicUtils
 * @date 2018/10/25 15:28
 * @description
 * @program holder-saas-store-business
 */
@Slf4j
@Component
public class DynamicHelper {

    @Value("${self.open-dynamic-datasource}")
    private Boolean openDynamicDatasource;


    public void changeDatasource(String enterpriseGuid) {
        if (openDynamicDatasource) {
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        }
    }

    public void changeRedis(String enterpriseGuid) {
        if (openDynamicDatasource) {
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        }
    }

    public Long generateGuid(String redisKey) {
        long guid;
        try {
            guid = BatchIdGenerator.getGuid(SpringContextUtil.getInstance().getBean("redisTemplate"), redisKey);
        } catch (Exception e) {
            log.error("生成guid失败，e={}", e.getMessage());
            throw new ParameterException("生成guid失败，e=" + e.getMessage());
        }
        return guid;
    }
}