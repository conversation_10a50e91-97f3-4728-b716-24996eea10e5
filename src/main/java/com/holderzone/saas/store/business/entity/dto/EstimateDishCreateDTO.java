package com.holderzone.saas.store.business.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateDishDO
 * @date 2018/08/06 上午11:33
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class EstimateDishCreateDTO implements Serializable {

    /**
     * 菜品id
     */
    @ApiModelProperty(value = "菜品id")
    private Long dishId;

    /**
     * 菜品名字
     */
    @ApiModelProperty(value = "菜品名字")
    private String dishName;

    /**
     * 估清数量
     */
    @ApiModelProperty(value = "估清数量")
    private BigDecimal estimateCount;

    /**
     * 报警数量
     */
    @ApiModelProperty(value = "报警数量")
    private BigDecimal warningCount;

    /**
     * 销售状态
     * 0=停售
     * 1=在售
     */
    @ApiModelProperty(value = "销售状态：0=停售，1=在售")
    private Integer status;
}
