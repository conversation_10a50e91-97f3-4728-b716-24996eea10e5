package com.holderzone.saas.store.business.utils;

import com.holderzone.saas.store.business.entity.domain.PaymentTypeDO;
import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeMapStruct
 * @date 2018/08/27 11:06
 * @description //
 * @program holder-saas-store-trading-center
 */
@Mapper
public interface PaymentTypeMapStruct {

    PaymentTypeMapStruct PAYMENT_TYPE_MAP_STRUCT = Mappers.getMapper(PaymentTypeMapStruct.class);


    @Mappings({
    })
    PaymentTypeDO paymentTypeDtoToDo(PaymentTypeDTO paymentTypeDTO);

    PaymentInfoDTO paymentTypeToJhConfig(PaymentTypeDTO paymentTypeDO);
}
