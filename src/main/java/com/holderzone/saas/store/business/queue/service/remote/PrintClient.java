package com.holderzone.saas.store.business.queue.service.remote;

import com.holderzone.saas.store.dto.print.content.PrintDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintClient
 * @date 2019/02/15 16:58
 * @description
 * @program holder-saas-store-table
 */
@Component
@FeignClient(value = "holder-saas-store-print", fallbackFactory = PrintClient.PrintClientFallBack.class)
public interface PrintClient {

    @PostMapping("/print_record/send")
    String print(PrintDTO printDTO);

    @Slf4j
    @Component
    class PrintClientFallBack implements FallbackFactory<PrintClient> {

        @Override
        public PrintClient create(Throwable throwable) {
            return printDTO -> {
                log.error("打印出错 e={}", throwable);
                return null;
            };

        }

    }

}
