package com.holderzone.saas.store.business.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.report.query.HandOverReportQueryDTO;
import com.holderzone.saas.store.dto.report.resp.HandoverReportRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverRecordService
 * @date 2018/07/29 上午10:21
 * @description 交接班相关接口
 * @program holder-saas-store-business-center
 */
public interface HandoverRecordService {

    void create(HandoverRecordCreateDTO handoverRecordCreateDTO);

    void confirm(HandoverRecordConfirmDTO handoverRecordConfirmDTO);

    void confirmNew(HandoverRecordConfirmDTO handoverRecordConfirmDTO);

    HandoverPayDTO settle(HandoverPayQueryDTO handoverRecordConfirmDTO);
    HandoverPayNewDTO settleNew(HandoverPayQueryDTO handoverPayQueryDTO);

    HandoverRecordDTO query(HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO);
    HandoverPayNewDTO queryNew(HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO);

    Page<HandoverRecordDTO> queryByPage(HandoverRecordQueryAllDTO handoverRecordQueryAllDTO);

    Integer isAllConfirmed(HandoverRecordIsAllConfirmedDTO handoverRecordIsAllConfirmedDTO);

    List<HandoverPayDetailDTO> queryPayDetailById(String handoverRecordGuid);

    Integer isAllConfirmedByList(List<String> storeGuidList);

    HandoverRecordDTO queryByUserGuid(HandoverRecordConfirmDTO handoverRecordConfirmDTO);

    HandoverRecordDTO queryByUserStoreGuidAndTerminalId(String storeGuid, String terminalId);

    void printHandOverRecord(String handoverRecordGuid, String deviceId);

    void printHandOverRecordNew(String handoverRecordGuid, String deviceId);

    void prePrintHandOverRecord(HandoverPayNewDTO handoverPayNewDTO);

    HandoverRecordDTO queryByStoreGuidAndUserGuid(String storeGuid, String userGuid);

    /**
     * 零售版查询交接班
     * @param handoverPayQueryDTO
     * @return
     */
    HandoverPayDTO retailSettle(HandoverPayQueryDTO handoverPayQueryDTO);

    /**
     * 零售版交班
     * @param handoverRecordConfirmDTO
     * @return
     */
    void retailConfirm(HandoverRecordConfirmDTO handoverRecordConfirmDTO);

    /**
     * 零售版查询交接班列表
     * @param handoverRecordQueryAllDTO
     * @return
     */
    Page<HandoverRecordDTO> retailQueryByPage(HandoverRecordQueryAllDTO handoverRecordQueryAllDTO);

    /**
     *从列表中跟进guid获取交接班详情
     * @param handoverRecordQuerySingleDTO
     * @return
     */
    HandoverRecordDTO retailQuery(HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO);

    /**
     * 零售版根据员工guid查询该员工当前的班次详情
     * @param handoverRecordConfirmDTO
     * @return
     */
    HandoverRecordDTO retailQueryByUserGuid(HandoverRecordConfirmDTO handoverRecordConfirmDTO);

    /**
     * 零售版交接班重打
     * @param handoverRecordGuid
     * @param deviceId
     */
    void printReatilHandOverRecord(String handoverRecordGuid, String deviceId);

    /**
     * 根据员工guid查询该员工当前的班次详情列表
     *
     * @param handoverRecordConfirmDTO 员工列表
     * @return 员工班次详情列表
     */
    List<HandoverRecordDTO> listByUserGuid(HandoverRecordConfirmDTO handoverRecordConfirmDTO);

    /**
     * 交接班报表
     * JXU4MDAxJXU2NzdGJXU1OTgyJXU2NzlDJXU1REU1JXU4RDQ0JXU1M0QxJXU1MTY4JXU0RTg2JXU1M0VGJXU0RUU1JXU4MDAzJXU4NjUxJXU0RjE4JXU1MzE2JXU0RTAwJXU0RTBC
     *
     * @param handOverQueryDTO 入参
     * @return List<HandoverReportRespDTO>
     */
    List<HandoverReportRespDTO> report(HandOverReportQueryDTO handOverQueryDTO);

    void handleHandoverHistory(HandoverHistoryHandleDTO request);

    /**
     * 查询当前门店未交班所有员工
     * @param storeGuid 门店guid
     * @return 员工列表
     */
    List<HandoverRecordDTO> queryOnDutyStaffs(String storeGuid);
}
