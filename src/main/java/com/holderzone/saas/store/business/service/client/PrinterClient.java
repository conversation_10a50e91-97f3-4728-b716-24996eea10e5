package com.holderzone.saas.store.business.service.client;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.print.content.PrintHandOverDTO;
import com.holderzone.saas.store.dto.print.content.PrintHandOverNewDTO;
import com.holderzone.saas.store.dto.print.content.retail.PrintRetailHandOverDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className PrinterClient
 * @date 18-10-20 上午9:42
 * @description
 * @program holder-saas-store-staff
 */
@Component
@FeignClient(value = "holder-saas-store-print", fallbackFactory = PrinterClient.ServiceFallBack.class)
public interface PrinterClient {
    /**
     * 打印交班记录(餐饮版)
     *
     * @param printHandOverDto dto
     */
    @PostMapping("/print_record/send")
    String printHandOverRecord(PrintHandOverDTO printHandOverDto);

    @PostMapping("/print_record/send")
    String printHandOverRecordNew(PrintHandOverNewDTO printHandOverNewDTO);


    /**
     * 打印交班记录（零售版）
     *
     * @param PrintRetailHandOverDTO dto
     */
    @PostMapping("/print_record/send")
    String printRetailHandOverRecord(PrintRetailHandOverDTO printHandOverDto);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<PrinterClient> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public PrinterClient create(Throwable cause) {
            return new PrinterClient(){

                @Override
                public String printHandOverRecord(PrintHandOverDTO printHandOverDto) {
                    log.error(HYSTRIX_PATTERN, "queryByAndroid", JacksonUtils.writeValueAsString(printHandOverDto),
                            ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public String printHandOverRecordNew(PrintHandOverNewDTO printHandOverNewDTO) {
                    log.error(HYSTRIX_PATTERN, "printHandOverRecordNew", JacksonUtils.writeValueAsString(printHandOverNewDTO),
                            ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public String printRetailHandOverRecord(PrintRetailHandOverDTO printHandOverDto) {
                    log.error(HYSTRIX_PATTERN, "queryByAndroid", JacksonUtils.writeValueAsString(printHandOverDto),
                            ThrowableUtils.asString(cause));
                    return null;
                }
            };
        }

    }
}