package com.holderzone.saas.store.business.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @className JHResp
 * @date 2018/08/15 11:53
 * @description
 * @program holder-saas-store-trading-center
 */
public enum JHResp {

    READY_JHPAY_RESP("10000", "支付中！！！"),

    SUCCESS_JHPAY_RESP("10000", "SUCCESS！！！"),

    SIGNATURE_FAIL("10002", "签名错误！！！"),

    PAYMENT_TYPE_ERROR("10003", "支付方式错误！！！"),

    REFUND_FAIL("10004","退款失败！！"),

    MONEY_ERROR("10005","应付金额和支付金额不相等！！"),

    CALL_JHPAY_FAIL("10001", "调用聚合支付接口失败！！！");


    private String code;

    private String msg;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    JHResp(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
