package com.holderzone.saas.store.business.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverRecordDO
 * @date 2018/07/29 上午11:17
 * @description 营业中心-交接班管理实体
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class HandoverRecordDO {
    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 设备id
     */
    private String terminalId;

    /**
     * 交接班记录guid
     */
    @TableId("handover_record_guid")
    private String handoverRecordGuid;

    /**
     * 创建人(接班人)guid
     */
    private String createUserGuid;

    /**
     * 创建人(接班人)名字
     */
    private String createUserName;

    /**
     * 当班销售收入（销售收入）
     */
    private BigDecimal paymentMoney;

    /**
     * 当班储值收入（充值收入）
     */
    private BigDecimal memberChargeMoney;

    /**
     * 营业额（销售收入+充值收入-会员卡消费金额）
     */
    private BigDecimal businessInComing;

    /**
     * 确认人(交班人)guid
     */
    private String confirmUserGuid;

    /**
     * 确认人(交班人)名字
     */
    private String confirmUserName;

    /**
     * 交接班状态
     * 0=已接班，未交班
     * 1=已交班
     */
    private Integer status;

    /**
     * 已结账订单数
     */
    private Integer checkedCount;

    /**
     * 充值订单数
     */
    private Integer chargedCount;

    /**
     * 退货订单数
     */
    private Integer refundCount;

    /**
     * 退货金额
     */
    private BigDecimal refundMoney;

    /**
     * 收银笔数
     */
    private Integer paymentCount;

    /**
     * 现金收银
     */
    private BigDecimal collectMoney;

    /**
     * 现金周转
     */
    private BigDecimal flowMoney;

    /**
     * 系统结算
     */
    private BigDecimal sysBalance;

    /**
     * 差额
     */
    private BigDecimal differenceMoney;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 预订单数
     */
    private Integer reserveCount;

    /**
     * 预订金
     */
    private BigDecimal reserveMoney;

    /**
     * 应上缴现金
     */
    private BigDecimal handonCash;

    /**
     * 实际上缴现金
     */
    private BigDecimal realHandonCash;

    /**
     * 还款金额（还挂账金额汇总）
     */
    private BigDecimal repaymentFeeTotal;
    /**
     * 还款单数
     */
    private Integer repaymentFeeCount;
    /**
     * 超出金额
     */
    private BigDecimal excessAmount;

    /**
     * 交接班负责人记录guid
     */
    private String relationRecordGuid;

    /**
     * 是否多人交接班 0 否 1 是
     */
    private Integer isMultiHandover;

    /**
     * 客流量
     */
    private Integer traffic;

    /**
     * 总餐位数
     */
    private Integer totalSeats;

    /**
     * 上座率
     */
    private String occupancyRatePercent;

    /**
     * 桌台使用次数
     */
    private Integer tableUseCount;

    /**
     * 总桌台数
     */
    private Integer tableCount;

    /**
     * 开台率
     */
    private String openTableRatePercent;

    /**
     * 翻台率
     */
    private String flipTableRatePercent;

    /**
     * 总用餐时间，单位分钟
     */
    private Long totalDineInTime;

    /**
     * 平均消费时间，单位分钟
     */
    private Integer avgDineInTime;

    /**
     * 销售（已结总金额）
     */
    private BigDecimal saleAmount;

    /**
     * 优惠总额
     */
    private BigDecimal discountAmount;

    /**
     * 退款总额
     */
    private BigDecimal refundAmount;
}
