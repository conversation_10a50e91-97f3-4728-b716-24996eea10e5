package com.holderzone.saas.store.business.queue.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HolderQueueConfigDO
 * @date 2019/05/09 11:25
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Data
@Accessors(chain = true)
@TableName("hsq_queue_config")
public class HolderQueueConfigDO {
    @TableId
    private Long id;
    private String guid;
    private String storeGuid;
    private Boolean isEnableEat;
    private Boolean isEnableRecovery;
    private Integer recoveryNum;
    private Boolean isEnableManualReset;
    private Boolean isEnableAutoReset;
    @TableField(strategy = FieldStrategy.IGNORED)
    private LocalTime autoResetTiming;
    /**
     * 是否删除，1-已删除，0-未删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建人guid
     */
    private String createStaffGuid;

    /**
     * 更新人guid
     */
    private String modifiedStaffGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 默认配置
     *
     * @return
     */
    public static HolderQueueConfigDO defaultConfig() {
        HolderQueueConfigDO holderQueueConfigDO = new HolderQueueConfigDO();
        holderQueueConfigDO.setIsEnableEat(false);
        holderQueueConfigDO.setIsEnableRecovery(false);
        holderQueueConfigDO.setRecoveryNum(3);
        holderQueueConfigDO.setIsEnableManualReset(false);
        holderQueueConfigDO.setIsEnableAutoReset(false);
        holderQueueConfigDO.setAutoResetTiming(LocalTime.of(5, 0));
        return holderQueueConfigDO;
    }
}