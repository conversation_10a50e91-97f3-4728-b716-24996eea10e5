package com.holderzone.saas.store.business.entity.query;

import com.holderzone.saas.store.dto.common.PageQuery;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateDTO
 * @date 2018/07/28 上午10:14
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AccountRecordPageInterval extends PageQuery {

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 开始时间
     */
    private LocalDateTime begin;

    /**
     * 结束时间
     */
    private LocalDateTime end;
}
