package com.holderzone.saas.store.business.queue.event;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.business.queue.config.RocketMqConfig;
import com.holderzone.saas.store.business.queue.service.QueueConfigService;
import com.holderzone.saas.store.business.queue.utils.ThrowableExtUtils;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.queue.StoreConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreCreateListener
 * @date 2019/08/09 14:22
 * @description 门店创建消息监听
 * @program holder-saas-store
 */
@Component
@Slf4j
@RocketListenerHandler(
        topic = RocketMqConfig.DOWNSTREAM_STORE_TOPIC,
        tags = RocketMqConfig.DOWNSTREAM_STORE_CREATE_TAG,
        consumerGroup = RocketMqConfig.DOWNSTREAM_STORE_INIT_QUEUE_GROUP
)
public class StoreCreateListener extends AbstractRocketMqConsumer<RocketMqTopic, StoreDTO> {
    private final QueueConfigService queueConfigService;

    @Autowired
    public StoreCreateListener(QueueConfigService queueConfigService) {
        this.queueConfigService = queueConfigService;
    }

    @Override
    public boolean consumeMsg(StoreDTO storeDTO, MessageExt messageExt) {
        if (log.isInfoEnabled()) {
            log.info("接收到门店创建消息， storeDTO:{}", JacksonUtils.writeValueAsString(storeDTO));
            log.info("开始初始化门店微信相关配置");
            if (ObjectUtils.isEmpty(storeDTO)) {
                log.info("初始化门店微信相关配置失败，门店信息为空");
                return false;
            }
        }
        UserContextUtils.put(messageExt.getProperty(RocketMqConfig.DOWNSTREAM_CONTEXT));
        try {
            EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            queueConfigService.config(StoreConfigDTO.DEFAULT.build(storeDTO.getGuid()));
        } catch (Exception e) {
            log.error("门店：{} 初始化微信配置发生错误：{}", storeDTO.getName(), ThrowableExtUtils.asStringIfAbsent(e));
            return false;
        } finally {
            UserContextUtils.remove();
            EnterpriseIdentifier.remove();
        }
        return true;
    }
}
