package com.holderzone.saas.store.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.business.entity.domain.BindupAccountsDo;
import com.holderzone.saas.store.business.mapper.BindupAccountsMapper;
import com.holderzone.saas.store.business.service.BindupAccountsService;
import com.holderzone.saas.store.business.service.client.*;
import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.StoreBindUpAccountsDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableOrderDTO;
import com.holderzone.saas.store.enums.common.ConfigEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPicTimeServiceImpl
 * @date 2019/08/19 16:20
 * @description
 * @program holder-saas-store
 */
@Slf4j
@Service
public class BindupAccountsServiceImpl extends ServiceImpl<BindupAccountsMapper, BindupAccountsDo> implements BindupAccountsService {

    @Resource
    private BindupAccountsMapper bindupAccountsMapper;

    @Resource
    private OrgFeignClient orgFeignClient;

    @Resource
    private MessageService messageService;

    @Resource
    private ConfigFeignService configFeignService;

    @Resource
    private TradingClient tradingClient;

    @Resource
    private TableRpcService tableRpcService;


    static SimpleDateFormat aDate = new SimpleDateFormat("HH:mm");
    static DateTimeFormatter dtf= DateTimeFormatter.ofPattern("yyyy-MM-dd");


    @Override
    public List<BindupAccountsDo> queryBindUpAccountsList(String storeGuid, LocalDateTime currentTime) {
        LambdaQueryWrapper<BindupAccountsDo> eq = new QueryWrapper<BindupAccountsDo>().lambda().eq(BindupAccountsDo::getStoreGuid, storeGuid)
                .eq(BindupAccountsDo::getBindupAccounts, currentTime);
        return bindupAccountsMapper.selectList(eq);
    }

    @Override
    public BindupAccountsDo queryBindUpAccountsLast(String storeGuid) {
        return bindupAccountsMapper.queryBindUpAccountsLast(storeGuid);
    }

    @Override
    public BindupAccountsDo saveBindUpAccounts(String storeGuid, String userGuid, String userName,LocalDate buAccounts) {
        LocalDate localDate  = null;
        if (buAccounts == null){
            //执行保存扎帐记录操作 1、获取当前门店
            //查询门店扎帐时间
            BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
            ArrayList<String> str = new ArrayList<>();
            str.add(storeGuid);
            businessDateReqDTO.setStoreGuidList(str);
            localDate = orgFeignClient.queryBusinessDay(businessDateReqDTO);
        }else {
            localDate = buAccounts;
        }
        //保存扎帐信息
        BindupAccountsDo bindupAccountsDo = new BindupAccountsDo();
        bindupAccountsDo.setBindupAccounts(localDate.atStartOfDay());
        bindupAccountsDo.setStoreGuid(storeGuid);
        bindupAccountsDo.setUserGuid(userGuid);
        bindupAccountsDo.setUserName(userName);
        bindupAccountsDo.setGmtCreate(LocalDateTime.now());
        this.save(bindupAccountsDo);
        //修改数据库门店状态
        List<String> toStoreList = new ArrayList<>();
        toStoreList.add(storeGuid);
        StoreBindUpAccountsDTO staDto = new StoreBindUpAccountsDTO();
        staDto.setStoreList(toStoreList);
        staDto.setCanOpenTable(1);
        log.info("更新信息为未轧帐门店列表：{}", JacksonUtils.writeValueAsString(staDto));
        orgFeignClient.batchUpdateBuAccounts(staDto);
        //发送消息给mq
        this.sendMqForCanOpenTable(storeGuid);
        return bindupAccountsDo;
    }

    @Override
    public void sendMqForCanOpenTable(String storeGuid){
        //封装发送信息
        BusinessMessageDTO surchargeMessageDTO = new BusinessMessageDTO();
        surchargeMessageDTO.setMessageType(BusinessMsgTypeEnum.BUSINESS_MSG_TYPE.getId());
        surchargeMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.BIND_UP_ACCOUNTS_STATUS.getId());
        surchargeMessageDTO.setSubject(BusinessMsgTypeEnum.BIND_UP_ACCOUNTS_STATUS.getName());
        surchargeMessageDTO.setPlatform("2");
        surchargeMessageDTO.setStoreGuid(storeGuid);
        surchargeMessageDTO.setContent("1");
        log.info("扎帐业务提示信 sendMessageBindUpAccounts：{}", JacksonUtils.writeValueAsString(surchargeMessageDTO));
        messageService.msg(surchargeMessageDTO);
    }

    @Override
    public void sendMqBindupAccounts() {
        Date currDa = new Date();
        String format = aDate.format(currDa);
        log.info("当前时间：{}",format);
        ConfigReqDTO configReqDTO = new ConfigReqDTO();
        configReqDTO.setDicCode(ConfigEnum.BIND_UP_ACCOUNTS.getCode());
        configReqDTO.setDictValue(format);
        configReqDTO.setIsEnable(0);
        List<ConfigRespDTO> configRespDTOS = configFeignService.getConfig(configReqDTO);
        log.info("查询config公共表数据内容：{}",configRespDTOS);
        if (!CollectionUtils.isEmpty(configRespDTOS)) {
            log.info("进入方法查询config公共表数据内容：{}",configRespDTOS);
            List<String> toStoreList = new ArrayList<>();
            for (ConfigRespDTO configRespDTO : configRespDTOS) {
                String storeGuid = configRespDTO.getStoreGuid();
                String enterpriseGuid = configRespDTO.getEnterpriseGuid();
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
                UserContextUtils.putErp(enterpriseGuid);
                Integer integer = tradingClient.queryOrderNumForStoreGuid(storeGuid);
                if (integer == 0 ) {
                    //无订单，新门店不用要推送
                    continue;
                }
                //判断昨日未扎帐， 判断是否有未关闭的桌台
                BindupAccountsDo bindupAccountsDo = this.queryBindUpAccountsLast(storeGuid);
                //新门店第二天，提示扎帐信息
                if (bindupAccountsDo == null || bindupAccountsDo.getBindupAccounts() == null){
                    sendMessageBindUpAccounts(storeGuid);
                    sendMessageBindUpAccountsStatus(storeGuid,enterpriseGuid);
                    toStoreList.add(configRespDTO.getStoreGuid());
                    continue;
                }
                //门店最新扎帐时间
                LocalDateTime bindupAccounts = bindupAccountsDo.getBindupAccounts();
                LocalDate currlocalData = bindupAccounts.toLocalDate();
                //当前系统扎帐时间
                LocalDate localDate = this.currentTimeDay(storeGuid,null);
                LocalDate yesterdayTime  = localDate.plusDays(-1);
                if (currlocalData.compareTo(yesterdayTime) < 0 ) {
                    //昨日未扎帐
                    sendMessageBindUpAccounts(storeGuid);
                    sendMessageBindUpAccountsStatus(storeGuid,enterpriseGuid);
                    toStoreList.add(configRespDTO.getStoreGuid());
                    continue;
                }
                //执行到这里 昨日扎帐——是否有桌台未关闭，有才执行推送消息
                TableBasicQueryDTO tableBasicQueryDTO = new TableBasicQueryDTO();
                tableBasicQueryDTO.setStoreGuid(storeGuid);
                List<TableOrderDTO> tableOrderDTOS = tableRpcService.listByAndroid(tableBasicQueryDTO);
                List<TableOrderDTO> collect = tableOrderDTOS.stream().filter(tableOrderReserveDTO -> tableOrderReserveDTO.getStatus() == 1).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(collect) ) {
                    sendMessageBindUpAccounts(storeGuid);
                    sendMessageBindUpAccountsStatus(storeGuid,enterpriseGuid);
                    toStoreList.add(configRespDTO.getStoreGuid());
                }
            }
            //修改当前门店信息为未扎帐
            if (!toStoreList.isEmpty()) {
                StoreBindUpAccountsDTO staDto = new StoreBindUpAccountsDTO();
                staDto.setStoreList(toStoreList);
                staDto.setCanOpenTable(0);
                log.info("更新信息为未轧帐门店列表：{}", JacksonUtils.writeValueAsString(staDto));
                orgFeignClient.batchUpdateBuAccounts(staDto);
            }
        }
    }


    /**
     * 提示扎帐信息
     * @param
     */
    public void sendMessageBindUpAccounts( String storeGuid){
        //查询门店扎帐时间
        BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        ArrayList<String> str = new ArrayList<>();
        str.add(storeGuid);
        businessDateReqDTO.setStoreGuidList(str);
        LocalDate localDate = orgFeignClient.queryBusinessDay(businessDateReqDTO);
        //封装发送信息
        BusinessMessageDTO surchargeMessageDTO = new BusinessMessageDTO();
        surchargeMessageDTO.setMessageType(BusinessMsgTypeEnum.BUSINESS_MSG_TYPE.getId());
        surchargeMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.BIND_UP_ACCOUNTS.getId());
        surchargeMessageDTO.setSubject(BusinessMsgTypeEnum.BIND_UP_ACCOUNTS.getName());
        surchargeMessageDTO.setPlatform("2");
        surchargeMessageDTO.setStoreGuid(storeGuid);
        String dateStr = localDate.format(dtf);
        surchargeMessageDTO.setContent(dateStr);
        log.info("扎帐业务提示信 sendMessageBindUpAccounts：{}", JacksonUtils.writeValueAsString(surchargeMessageDTO));
        messageService.msg(surchargeMessageDTO);
    }

    /**
     * 提示不可以开台  content 0 不能开台   1 可以开台
     * @param
     */
    public void sendMessageBindUpAccountsStatus(String storeGuid,String enterpriseGuid){
        //封装发送信息
        BusinessMessageDTO surchargeMessageDTO = new BusinessMessageDTO();
        surchargeMessageDTO.setMessageType(BusinessMsgTypeEnum.BUSINESS_MSG_TYPE.getId());
        surchargeMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.BIND_UP_ACCOUNTS_STATUS.getId());
        surchargeMessageDTO.setSubject(BusinessMsgTypeEnum.BIND_UP_ACCOUNTS_STATUS.getName());
        surchargeMessageDTO.setPlatform("2");
        surchargeMessageDTO.setStoreGuid(storeGuid);
        surchargeMessageDTO.setContent("0");
        log.info("扎帐业务提示信 sendMessageBindUpAccounts：{}", JacksonUtils.writeValueAsString(surchargeMessageDTO));
        messageService.msg(surchargeMessageDTO);
    }


    public LocalDate currentTimeDay(String storeGuid,LocalDateTime localDateTime){
        BusinessDateReqDTO businessDateReqDTO = new BusinessDateReqDTO();
        ArrayList<String> str = new ArrayList<>();
        str.add(storeGuid);
        businessDateReqDTO.setStoreGuidList(str);
        if (localDateTime != null) {
            businessDateReqDTO.setQueryDateTime(localDateTime);
        }
        return orgFeignClient.queryBusinessDay(businessDateReqDTO);
    }
}
