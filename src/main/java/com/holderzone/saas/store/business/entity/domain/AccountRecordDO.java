package com.holderzone.saas.store.business.entity.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordDO
 * @date 2018/07/28 上午10:01
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountRecordDO implements Serializable {

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 营业日guid
     */
    private String accountRecordGuid;

    /**
     * 创建人guid
     */
    private String createUserGuid;

    /**
     * 创建人名字
     */
    private String createUserName;

    /**
     * 扎帐人guid
     */
    private String confirmUserGuid;

    /**
     * 扎栈人名字
     */
    private String confirmUserName;

    /**
     * 撤销人guid
     */
    private String cancelUserGuid;

    /**
     * 撤销人名字
     */
    private String cancelUserName;

    /**
     * 打印人guid
     */
    private String printUserGuid;

    /**
     * 打印人名字
     */
    private String printUserName;

    /**
     * 扎帐状态
     * 0=未扎帐
     * 1=已扎帐
     */
    private Integer status;

    /**
     * 是否出库
     * 0=未出库
     * 1=已出库
     */
    private Integer outbound;

    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    private Integer deleted;

    /**
     * 营业日
     */
    private LocalDate businessDay;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 扎帐时间
     */
    private LocalDateTime gmtConfirmed;

    /**
     * 扎帐时间
     */
    private LocalDateTime gmtCanceled;
}
