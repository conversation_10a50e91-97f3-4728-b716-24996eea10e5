package com.holderzone.saas.store.business.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.business.entity.domain.MemberOperateRecordDO;
import com.holderzone.saas.store.business.service.RedisService;
import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import com.holderzone.sdk.util.BatchIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.holderzone.framework.util.StringUtils.getStr;


/**
 * <AUTHOR>
 * @version 1.0
 * @className RedisServiceImpl
 * @date 2018/08/03 17:53
 * @description
 * @program holder-saas-store-trading-center
 */
@Service
public class RedisServiceImpl implements RedisService {

    @Autowired
    private RedisTemplate redisTemplate;

    private static final String MODULE_NAME = "trading";

    private static final String REGEX = ":";

    private static final String PAY_TYPE = "pay_type";

    private static final String PAYMENT_TYPE = "payment_type";

    private static final String SYS_DIS = "system_dis";

    private static final String PRINT_ITEM_ORDER_CONFIG_KEY = "PRINT_ITEM_ORDER_CONFIG:%s";

    //    private static final int POLLING_TIME = 15;
//    private static final int POLLING_TIME = 1;
    private static final int POLLING_TIME = 60;

    private static final TimeUnit POLLING_TIMEUNIT = TimeUnit.MINUTES;

    private static final String TAG_BUSINESS_SURCHARGE = "business/surcharge";

    private static final String TAG_BUSINESS_SURCHARGE_AREA = "business/surcharge_area";

    @Override
    public void putPaymentType(String storeGuid, List<PaymentTypeDTO> all) {
        String key = getStr(REGEX, MODULE_NAME, PAYMENT_TYPE, storeGuid);
        redisTemplate.opsForList().rightPush(key, all);
        redisTemplate.expire(key, 2, TimeUnit.HOURS);
    }

    @Override
    public List<PaymentTypeDTO> getPaymentType(String storeGuid) {
        String key = getStr(REGEX, MODULE_NAME, PAYMENT_TYPE, storeGuid);
        Long size = redisTemplate.opsForList().size(key);
        List<List<PaymentTypeDTO>> range = redisTemplate.opsForList().range(key, 0, size);
        if (null != range && !range.isEmpty()) {
            return range.get(0);
        }
        return null;
    }

    @Override
    public void deletePaymentType(String storeGuid) {
        String key = getStr(REGEX, MODULE_NAME, PAYMENT_TYPE, storeGuid);
        redisTemplate.delete(key);
    }

    @Override
    public void deletePaymentType(Set<String> storeGuidList) {
        List<String> keys = storeGuidList.stream()
                .map(storeGuid -> getStr(REGEX, MODULE_NAME, PAYMENT_TYPE, storeGuid))
                .collect(Collectors.toList());
        redisTemplate.delete(keys);

    }

    @Override
    public void removeSysDiscount(String storeGuid) {
        redisTemplate.delete(getStr(REGEX, MODULE_NAME, SYS_DIS, storeGuid));
    }

    @Override
    public void putJHPayInfo(PaymentInfoDTO paymentInfoDTO) {
        redisTemplate.opsForValue().set(getStr(REGEX, MODULE_NAME, PAY_TYPE, paymentInfoDTO.getStoreGuid()),
                JacksonUtils.writeValueAsString(paymentInfoDTO), 5, TimeUnit.HOURS);
    }

    @Override
    public void putJHPayInfoList(List<PaymentInfoDTO> paymentInfoDTOList, String storeGuid) {
        redisTemplate.opsForValue().set(getStr(REGEX, MODULE_NAME, PAY_TYPE, storeGuid),
                JacksonUtils.writeValueAsString(paymentInfoDTOList), 5, TimeUnit.HOURS);
    }

    @Override
    public void putJHPayInfoString(String payStr, String storeGuid) {
        redisTemplate.opsForValue().set(getStr(REGEX, MODULE_NAME, PAY_TYPE, storeGuid),
                payStr, 5, TimeUnit.HOURS);
    }

    @Override
    public PaymentInfoDTO getJHPayInfo(String storeGuid) {
        String str = (String) redisTemplate.opsForValue().get(getStr(REGEX, MODULE_NAME, PAY_TYPE, storeGuid));
        if (StringUtils.hasText(str)) {
            return JacksonUtils.toObject(PaymentInfoDTO.class, str);
        }
        return null;
    }

    @Override
    public List<PaymentInfoDTO> getJHPayInfoList(String storeGuid) {
        String str = (String) redisTemplate.opsForValue().get(getStr(REGEX, MODULE_NAME, PAY_TYPE, storeGuid));
        if (StringUtils.hasText(str)) {
            return JacksonUtils.toObjectList(PaymentInfoDTO.class, str);
        }
        return new ArrayList<>();
    }

    /**
     * 删除缓存聚合支付账户信息
     *
     * @param storeGuid 门店guid
     */
    @Override
    public void deleteJHPayInfo(String storeGuid) {
        String key = getStr(REGEX, MODULE_NAME, PAY_TYPE, storeGuid);
        redisTemplate.delete(key);
    }

    @Override
    public Long rawId(String tag) {
        try {
            return BatchIdGenerator.buildSnowFlakeGuids(tag,1).get(0);
        } catch (Exception e) {
            throw new BusinessException("生成Guid失败：" + e.getMessage());
        }
    }

    @Override
    public String singleGuid(String tableName) {
        return String.valueOf(rawId(tableName));
    }

    @Override
    public List<String> nextBatchId(String tag, long count) {
        return BatchIdGenerator.buildSnowFlakeGuids(tag, count)
                .stream().map(String::valueOf).collect(Collectors.toList());
    }

    @Override
    public String nextSurchargeGuid() {
        return singleGuid(TAG_BUSINESS_SURCHARGE);
    }

    @Override
    public String nextSurchargeAreaGuid() {
        return singleGuid(TAG_BUSINESS_SURCHARGE_AREA);
    }

    @Override
    public List<String> nextBatchSurchargeAreaGuid(long count) {
        return nextBatchId(TAG_BUSINESS_SURCHARGE_AREA, count);
    }

    @Override
    public String nextMemberOperateRecordGuid() {
        return singleGuid(MemberOperateRecordDO.class.getSimpleName());
    }

    @Override
    public String getPrintItemOrderConfig(String storeGuid) {
        return (String) redisTemplate.opsForValue().get(String.format(PRINT_ITEM_ORDER_CONFIG_KEY, storeGuid));
    }

    @Override
    public void setPrintItemOrderConfig(String storeGuid, String config) {
        redisTemplate.opsForValue().set(String.format(PRINT_ITEM_ORDER_CONFIG_KEY, storeGuid),
                config, 8, TimeUnit.HOURS);
    }
}
