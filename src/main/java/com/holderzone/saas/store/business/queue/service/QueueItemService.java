package com.holderzone.saas.store.business.queue.service;


import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.business.queue.domain.HolderQueueDO;
import com.holderzone.saas.store.dto.queue.*;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueItemService
 * @date 2019/03/27 17:18
 * @description //TODO
 * @program holder-saas-store-queue
 */
public interface QueueItemService {

    HolderQueueItemDetailDTO inQueue(HolderQueueItemDTO dto);

    HolderQueueItemDetailDTO call(ItemGuidDTO dto);

    HolderQueueItemDetailDTO pass(ItemGuidDTO dto);

    HolderQueueItemDetailDTO confirm(ItemGuidDTO dto);

    TableQueueItemDetailDTO confirmAndTable(TableConfirmDTO dto);

    HolderQueueItemDetailDTO recover(ItemGuidDTO dto);

    HolderQueueQueueRecordDTO obtain(ItemGuidDTO dto);

    Page<HolderQueueItemDetailDTO> page(Page page);

    List<HolderQueueItemDetailDTO> listQueueItemDetails();

    LocalDateTime currentBusinessStart(String storeGuid);

    Boolean cancel(String guid, String enterpriseGuid);

    List<WxQueueListDTO> queryByUser(QueueWechatDTO queueWechatDTO);

    HolderQueueQueueRecordDTO getQueueDetail(@RequestBody ItemGuidDTO dto);

    default void clean(List<HolderQueueDO> list) {

    }

    ;
}