package com.holderzone.saas.store.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.business.constant.GuidKeyConstant;
import com.holderzone.saas.store.business.entity.domain.DataSettingDO;
import com.holderzone.saas.store.business.entity.domain.DataSettingQueryDO;
import com.holderzone.saas.store.business.mapper.DataSettingMapper;
import com.holderzone.saas.store.business.service.DataSettingService;
import com.holderzone.saas.store.business.service.client.OrgFeignClient;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingQueryDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingSaveDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.enums.business.DataSettingEnum;
import com.holderzone.saas.store.enums.business.DinnerCouponItemPriceSettingEnum;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.business.utils.DataSettingMapStruct.DATA_SETTING_MAP_STRUCT;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/7
 * @since 1.8
 */
@Service
@Slf4j
public class DataSettingServiceImpl extends ServiceImpl<DataSettingMapper, DataSettingDO> implements DataSettingService {

    private final DataSettingMapper dataSettingMapper;

    private final OrgFeignClient orgFeignClient;

    @Autowired
    public DataSettingServiceImpl(DataSettingMapper dataSettingMapper, OrgFeignClient orgFeignClient) {
        this.dataSettingMapper = dataSettingMapper;
        this.orgFeignClient = orgFeignClient;
    }

    @Override
    public List<DataSettingDTO> findDataSetting(DataSettingQueryDTO dataSettingQueryDTO) {
        String brandGuid = null;
        if (StringUtils.isBlank(dataSettingQueryDTO.getBrandGuid())) {
            if (StringUtils.isNotBlank(dataSettingQueryDTO.getStoreGuid())) {
                StoreDTO storeDTO = orgFeignClient.queryStoreByGuid(dataSettingQueryDTO.getStoreGuid());
                if (Objects.nonNull(storeDTO) && StringUtils.isNotBlank(storeDTO.getBelongBrandGuid())) {
                    brandGuid = storeDTO.getBelongBrandGuid();
                }
            }
        } else {
            brandGuid = dataSettingQueryDTO.getBrandGuid();
        }
        if (StringUtils.isBlank(brandGuid)) {
            throw new BusinessException("品牌Guid不能为空");
        }
        List<BrandDTO> brandDTOs = orgFeignClient.queryBrandByIdList(Collections.singletonList(brandGuid));
        if (CollectionUtils.isEmpty(brandDTOs)) {
            throw new BusinessException("品牌不存在");
        }
        dataSettingQueryDTO.setBrandGuid(brandGuid);
        DataSettingQueryDO dataSettingQueryDO = DATA_SETTING_MAP_STRUCT.dataSettingQueryDTO2DataSettingQueryDO(dataSettingQueryDTO);
        List<DataSettingDO> dataSettingDOs = dataSettingMapper.findDataSetting(dataSettingQueryDO);
        return buildDataSettingDTORsp(brandDTOs.get(0), dataSettingDOs,
                CollectionUtils.isEmpty(dataSettingQueryDTO.getDataSettingTypeList()) ?
                        new HashSet<>() : new HashSet<>(dataSettingQueryDTO.getDataSettingTypeList()));
    }

    @Override
    public Boolean saveDataSetting(DataSettingSaveDTO dataSettingSaveDTO) {
        String brandGuid = dataSettingSaveDTO.getBrandGuid();
        if (StringUtils.isBlank(brandGuid)) {
            throw new BusinessException("品牌Guid不能为空");
        }
        List<BrandDTO> brandDTOS = orgFeignClient.queryBrandByIdList(Collections.singletonList(brandGuid));
        if (CollectionUtils.isEmpty(brandDTOS)) {
            throw new BusinessException("品牌不存在");
        }
        List<DataSettingDTO> dataSettingDTOs = dataSettingSaveDTO.getDataSettingDTOList();
        if (CollectionUtils.isEmpty(dataSettingDTOs)) {
            return Boolean.TRUE;
        }
        dataSettingDTOs = dataSettingDTOs.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(DataSettingDTO::getDataSettingType, dto -> dto,
                                (existing, replacement) -> replacement
                        ), map -> new ArrayList<>(map.values())));
        List<Integer> dataSettingTypes = dataSettingDTOs.stream().map(DataSettingDTO::getDataSettingType)
                .collect(Collectors.toList());
        DataSettingQueryDO dataSettingQueryDO = new DataSettingQueryDO();
        dataSettingQueryDO.setBrandGuid(brandGuid);
        dataSettingQueryDO.setDataSettingTypeList(dataSettingTypes);
        List<DataSettingDO> dataSettingsInDB = dataSettingMapper.findDataSetting(dataSettingQueryDO);
        Map<Integer, DataSettingDO> dataSettingsGroupedByType = dataSettingsInDB.stream()
                .collect(Collectors.groupingBy(
                        DataSettingDO::getDataSettingType,
                        Collectors.collectingAndThen(Collectors.toList(), list -> list.get(list.size() - 1))
                ));
        List<DataSettingDTO> insertDataSettingDTOs = new ArrayList<>();
        List<DataSettingDTO> updateDataSettingDTOs = new ArrayList<>();
        // 一次遍历完成 null guid 收集
        for (DataSettingDTO item : dataSettingDTOs) {
            item.setBrandGuid(brandGuid);
            if (Objects.isNull(item.getGuid())) {
                if (Objects.nonNull(dataSettingsGroupedByType.get(item.getDataSettingType()))) {
                    item.setGuid(dataSettingsGroupedByType.get(item.getDataSettingType()).getGuid());
                    updateDataSettingDTOs.add(item);
                } else {
                    insertDataSettingDTOs.add(item);
                }
            } else {
                updateDataSettingDTOs.add(item);
            }
        }
        if (!insertDataSettingDTOs.isEmpty()) {
            List<Long> snowFlakeGuids = BatchIdGenerator.buildSnowFlakeGuids(GuidKeyConstant.DATA_SETTING_GUID, insertDataSettingDTOs.size());
            // 分配 GUID
            for (int i = 0; i < insertDataSettingDTOs.size(); i++) {
                insertDataSettingDTOs.get(i).setGuid(snowFlakeGuids.get(i));
            }
            List<DataSettingDO> insertDataSettingDOs = DATA_SETTING_MAP_STRUCT.dataSettingDTOs2DataSettingDOs(insertDataSettingDTOs);
            log.info("insertDataSettingDOs={}", JacksonUtils.writeValueAsString(insertDataSettingDOs));
            this.saveBatch(insertDataSettingDOs);
        }
        if (!updateDataSettingDTOs.isEmpty()) {
            List<DataSettingDO> updateDataSettingDOs = DATA_SETTING_MAP_STRUCT.dataSettingDTOs2DataSettingDOs(updateDataSettingDTOs);
            log.info("updateDataSettingDOs={}", JacksonUtils.writeValueAsString(updateDataSettingDOs));
            this.updateBatchById(updateDataSettingDOs);
        }
        return Boolean.TRUE;
    }

    private List<DataSettingDTO> buildDataSettingDTORsp(BrandDTO brandDTO,
                                                        List<DataSettingDO> dataSettingDOs,
                                                        Set<Integer> dataSettingTypeList) {
        List<DataSettingDTO> dataSettingDTOs = DATA_SETTING_MAP_STRUCT.dataSettingDOs2DataSettingDTOs(dataSettingDOs);

        boolean isQueryMatch = CollectionUtils.isEmpty(dataSettingTypeList)
                || dataSettingTypeList.contains(DataSettingEnum.DINNER_COUPON_ITEM_PRICE_SETTING.getCode());

        boolean isDataContainsMatch = CollectionUtils.isEmpty(dataSettingDTOs)
                || dataSettingDTOs.stream()
                .noneMatch(dataSetting ->
                        StringUtils.equals(dataSetting.getBrandGuid(), brandDTO.getGuid())
                                && dataSetting.getDataSettingType() == DataSettingEnum.DINNER_COUPON_ITEM_PRICE_SETTING.getCode());

        if (isQueryMatch && isDataContainsMatch) {
            DataSettingDTO defaultDataSetting = new DataSettingDTO();
            defaultDataSetting.setBrandGuid(brandDTO.getGuid());
            defaultDataSetting.setDataSettingCode(DataSettingEnum.DINNER_COUPON_ITEM_PRICE_SETTING.getCode());
            defaultDataSetting.setDataSettingType(DinnerCouponItemPriceSettingEnum.ORIGINAL_PRICE.getCode());
            dataSettingDTOs.add(defaultDataSetting);
        }
        dataSettingDTOs.stream().sorted(Comparator.comparing(DataSettingDTO::getDataSettingType))
                .forEach(dataSetting -> dataSetting.setBrandName(brandDTO.getName()));
        return dataSettingDTOs;
    }
}
