package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AccountRecordOrderException extends BusinessException {

    private static final long serialVersionUID = 791440407842650854L;

    public AccountRecordOrderException() {
        super("有账单还未结账，无法结束营业");
    }

    public AccountRecordOrderException(String message) {
        super("有["+message+"]个账单还未结账，无法结束营业");
    }

    public AccountRecordOrderException(String message, Throwable cause) {
        super(message, cause);
    }
}
