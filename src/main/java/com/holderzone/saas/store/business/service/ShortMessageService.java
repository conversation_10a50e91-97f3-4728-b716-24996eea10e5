package com.holderzone.saas.store.business.service;

import com.holderzone.saas.store.dto.business.manage.ChargeDTO;
import com.holderzone.saas.store.dto.business.manage.ProductOrderDTO;
import com.holderzone.saas.store.dto.business.manage.ShortMsgRespDTO;
import com.holderzone.saas.store.dto.trade.ShortMsgConfigDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ShortMessageService
 * @date 2018/09/17 13:43
 * @description 商户短信充值service
 * @program holder-saas-store-business
 */
public interface ShortMessageService {

    /**
     * 查询当前短信充值规则
     *
     * @return
     */
    List<ChargeDTO> getAll();

    /**
     * 调用云端平台短信充值
     *
     * @param productOrderDTO
     * @return
     */
    ShortMsgRespDTO charge(ProductOrderDTO productOrderDTO);

    /**
     * 轮询
     *
     * @param shortMsgRespDTO
     * @return
     */
    ShortMsgRespDTO polling(ShortMsgRespDTO shortMsgRespDTO);

    /**
     * 查询
     *
     * @param
     * @return
     */
    ShortMsgConfigDTO query();

    /**
     * 更新配置
     *
     * @param shortMsgConfigDTO
     * @return
     */
    boolean updateMessageConfig(ShortMsgConfigDTO shortMsgConfigDTO);
}
