package com.holderzone.saas.store.business.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.business.entity.domain.SurchargeDO;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeAggDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeCalculateDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeSyncDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SurchargeService
 * @date 2018/08/02 下午3:35
 * @description //TODO
 * @program holder-saas-store-business
 */
public interface SurchargeService extends IService<SurchargeDO> {

    String addSurcharge(SurchargeCreateDTO surchargeCreateDTO);

    SurchargeDTO querySurcharge(SurchargeQueryDTO surchargeQueryDTO);

    void updateSurcharge(SurchargeUpdateDTO surchargeUpdateDTO);

    void enableSurcharge(SurchargeEnableDTO surchargeEnableDTO);

    void deleteSurcharge(SurchargeDeleteDTO surchargeDeleteDTO);

    void batchEnableSurcharge(SurchargeBatchEnableDTO surchargeBatchEnableDTO);

    void batchDeleteSurcharge(SurchargeBatchDeleteDTO surchargeBatchDeleteDTO);

    Page<SurchargeDTO> listByType(SurchargeListDTO surchargeListDTO);

    Map<String, List<SurchargeLinkDTO>> listByAreaGuid(List<String> areaList, Integer tradeMode);

    Map<String, List<SurchargeLinkDTO>> listByAreaGuidAndStore(String storeGuid, List<String> areaList);

    SurchargeAggDTO sync(SurchargeSyncDTO surchargeDTO);

    /**
     * 通过门店和桌台查询附加费信息
     *
     * @param surchargeDTO 入参
     * @return 附加费信息
     */
    List<SurchargeLinkDTO> calculateSurcharge(SurchargeCalculateDTO surchargeDTO);

    List<SurchargeLinkDTO> listByCondition(SurchargeConditionQuery query);
}
