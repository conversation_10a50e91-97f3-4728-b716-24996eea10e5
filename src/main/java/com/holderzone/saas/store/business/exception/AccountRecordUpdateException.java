package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AccountRecordUpdateException extends BusinessException {

    private static final long serialVersionUID = -7998507146091502505L;

    public AccountRecordUpdateException() {
        super("营业日修改失败");
    }

    public AccountRecordUpdateException(String message) {
        super("营业日["+message+"]修改失败");
    }

    public AccountRecordUpdateException(String message, Throwable cause) {
        super(message, cause);
    }
}
