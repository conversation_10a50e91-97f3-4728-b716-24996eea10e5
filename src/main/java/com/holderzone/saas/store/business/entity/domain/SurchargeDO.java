package com.holderzone.saas.store.business.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AdditionalFeeDO
 * @date 2018/08/02 下午3:00
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("hsb_surcharge")
public class SurchargeDO implements Serializable {

    /**
     * 自增id
     */
    @TableId
    private Long id;

    /**
     * 附加费guid
     */
    private String surchargeGuid;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 附加费名称
     */
    private String name;

    /**
     * 附加费金额
     */
    private BigDecimal amount;

    /**
     * 费用类型
     * 0：按人
     * 1：按桌
     */
    private Integer type;

    /**
     * 场景：0=正餐，1=快餐
     */
    private String tradeMode;

    /**
     * 有效时间
     */
    private Integer effectiveTime;

    /**
     * 是否已启用
     * 0=未启用
     * 1=已启用
     */
    private Boolean isEnable;

    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
