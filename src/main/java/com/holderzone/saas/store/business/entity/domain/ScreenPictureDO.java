package com.holderzone.saas.store.business.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPictureDO
 * @date 2018/09/06 17:30
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("hsb_screen_picture")
public class ScreenPictureDO implements Serializable {

    private Long id;
    @TableId(value = "screen_picture_guid", type = IdType.INPUT)
    private String screenPictureGuid;

    private String name;

    private Integer changeMills;

    private String ossUrl;

    private String storeGuid;

    private String storeName;

    private Integer weight;

    private Integer height;

    private Integer pxType;

    private Integer picType;

    @TableField(exist = false)
    private boolean asFirst;

    @TableField(value = "gmt_create")
    private LocalDateTime createTime;

    @TableField(value = "gmt_modified")
    private LocalDateTime updateTime;

    private Integer deviceType;

}
