package com.holderzone.saas.store.business.service.client;

import com.holderzone.framework.response.LogicResponse;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.business.enums.JHResp;
import com.holderzone.saas.store.dto.trade.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import static org.springframework.http.HttpMethod.POST;

/**
 * <AUTHOR>
 * @version 1.0
 * @className JHPayClientServiceImpl
 * @date 2018/08/14 11:42
 * @description
 * @program holder-saas-store-trading-center
 */
@Service
public class JHPayClientServiceImpl implements JHPayClientService {

    @Value("${jhPay.pay.url}")
    private String jhPayUrl;

    @Value("${jhPay.pay.query.url}")
    private String jhPayQueryUrl;

    @Value("${jhPay.refund.url}")
    private String jhPayRefundUrl;

    @Value("${jhPay.refund.query.url}")
    private String jhPayRefundQueryUrl;

    @Value("${jhPay.pay.polling.url}")
    private String jhPayPollingUrl;


    @Value("${jhPay.mchnt.query}")
    private String mchntQuery;

    @Autowired
    @Qualifier("remoteRestTemplate")
    private RestTemplate restTemplate;

    private static final Logger logger = LoggerFactory.getLogger(JHPayClientServiceImpl.class);



    @Override
    public PollingJHPayRespDTO query(PollingJHPayDTO pollingJHPayDTO) {
        logger.info("查询聚合支付项目");
        PollingJHPayRespDTO pollingJHPayRespDTO;
        try {
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            ResponseEntity<LogicResponse<PollingJHPayRespDTO>> responseEntity = restTemplate.exchange(jhPayPollingUrl, POST,
                    new HttpEntity<>(pollingJHPayDTO,headers),
                    new ParameterizedTypeReference<LogicResponse<PollingJHPayRespDTO>>() {
                    });
            pollingJHPayRespDTO = responseEntity.getBody().getBody();
            logger.info("聚合支付查询Resp={}", JacksonUtils.writeValueAsString(pollingJHPayRespDTO));
        } catch (RestClientException e) {
            logger.error("调用聚合支付查询接口失败，e={}", e.getMessage());
            e.printStackTrace();
            pollingJHPayRespDTO = errorPollingJHResp(JHResp.CALL_JHPAY_FAIL.getCode(), JHResp.CALL_JHPAY_FAIL.getMsg());
            pollingJHPayRespDTO.setAttachData(pollingJHPayDTO.getAttachData());
        }
        return pollingJHPayRespDTO;
    }

    @Override
    public Boolean check(MchntValidateDTO mchntValidateDTO) {
        logger.info("商户配置聚合支付信息,mchntValidateDTO={}", JacksonUtils.writeValueAsString(mchntValidateDTO));
        ResponseEntity<LogicResponse<Boolean>> exchange = null;
        try {
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            HttpEntity reqE = new HttpEntity<>(mchntValidateDTO, headers);
            exchange = restTemplate.exchange(mchntQuery, POST, reqE, new ParameterizedTypeReference<LogicResponse<Boolean>>() {
            });
            LogicResponse<Boolean> body = exchange.getBody();
            return null == body ? false : body.getBody();
        } catch (RestClientException e) {
            e.printStackTrace();
            logger.error("调用聚合支付确认商户合法接口失败！！！");
            return false;
        }

    }


    private PollingJHPayRespDTO errorPollingJHResp(String code, String msg) {
        return PollingJHPayRespDTO.Builder.Builder()
                .code(code)
                .msg(msg)
                .build();
    }

}
