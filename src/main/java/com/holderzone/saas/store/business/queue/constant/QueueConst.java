package com.holderzone.saas.store.business.queue.constant;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueConst
 * @date 2018/07/27 下午4:57
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public final class QueueConst {

    private QueueConst() {
    }

    public static final List<String> QUEUE_CODE = new ArrayList<>();

    public static final int MAX_QUEUE = 10;

    private static final String[] ENGLISH_LETTER_UPPER = {
            "A", "B", "C", "D", "E", "F", "G", "H", "I", "J",
            "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T",
            "U", "V", "W", "X", "Y", "Z"
    };

    static {
        QUEUE_CODE.addAll(Arrays.asList(ENGLISH_LETTER_UPPER).subList(0, MAX_QUEUE));
    }
}
