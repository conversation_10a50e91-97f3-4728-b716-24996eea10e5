package com.holderzone.saas.store.business.entity.query;

import com.holderzone.saas.store.dto.common.PageQuery;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordPageQuery
 * @date 2018/07/28 上午11:49
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountRecordPageQuery extends PageQuery {

    /**
     * 门店guid
     */
    private String storeGuid;
}
