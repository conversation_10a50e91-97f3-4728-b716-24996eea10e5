package com.holderzone.saas.store.business.queue.controller;

import com.holderzone.saas.store.business.queue.service.QueueConfigService;
import com.holderzone.saas.store.dto.queue.StoreConfigDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueConfigController
 * @date 2019/05/09 18:40
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Api("排队 - 门店设置")
@RestController
public class QueueConfigController {
    @Autowired
    QueueConfigService configService;

    @ApiOperation("保存排队设置")
    @PostMapping("/queue/config")
    public StoreConfigDTO config(@Validated @RequestBody StoreConfigDTO dto) {
        return configService.config(dto);
    }

    @ApiOperation("获取队列配置")
    @GetMapping("/queue/config")
    public StoreConfigDTO query(@RequestParam("storeGuid") String storeGuid) {
        return configService.obtain(storeGuid);
    }
}