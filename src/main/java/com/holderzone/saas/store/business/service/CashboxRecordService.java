package com.holderzone.saas.store.business.service;

import com.holderzone.saas.store.dto.business.manage.CashboxRecordCreateDTO;
import com.holderzone.saas.store.dto.business.manage.CashboxRecordDTO;
import com.holderzone.saas.store.dto.business.manage.CashboxRecordListDTO;
import com.holderzone.saas.store.dto.business.manage.CashboxRecordQueryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CashboxRecordService
 * @date 2018/07/29 下午6:06
 * @description 钱箱服务相关接口
 * @program holder-saas-store-business-center
 */
public interface CashboxRecordService {

    void create(CashboxRecordCreateDTO cashboxRecordCreateDTO);

    CashboxRecordDTO query(CashboxRecordQueryDTO cashboxRecordQueryDTO);

    List<CashboxRecordDTO> list(CashboxRecordListDTO cashboxRecordListDTO);
}
