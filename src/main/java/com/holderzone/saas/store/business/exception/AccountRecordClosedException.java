package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AccountRecordClosedException extends BusinessException {

    private static final long serialVersionUID = 772407784451723238L;

    public AccountRecordClosedException() {
        super("营业日已扎帐，无法操作");
    }

    public AccountRecordClosedException(String message) {
        super("营业日["+message+"]已扎帐，无法操作");
    }

    public AccountRecordClosedException(String message, Throwable cause) {
        super(message, cause);
    }
}
