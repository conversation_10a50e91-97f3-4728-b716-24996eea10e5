package com.holderzone.saas.store.business.queue.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.business.queue.domain.HolderQueueConfigDO;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.business.queue.mapper.QueueConfigMapper;
import com.holderzone.saas.store.business.queue.mapstruct.QueueConfigMapStruct;
import com.holderzone.saas.store.business.queue.service.QueueConfigService;
import com.holderzone.saas.store.business.queue.service.remote.ConfigClient;
import com.holderzone.saas.store.business.queue.utils.QueuedUtils;
import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import com.holderzone.saas.store.dto.queue.StoreConfigDTO;
import com.holderzone.saas.store.enums.common.ConfigEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueConfigServiceImpl
 * @date 2019/05/09 18:44
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Service
public class QueueConfigServiceImpl extends ServiceImpl<QueueConfigMapper, HolderQueueConfigDO> implements QueueConfigService {
    private static final DateTimeFormatter TIMING_FORMART = DateTimeFormatter.ofPattern("HH:mm");
    @Autowired
    private QueueConfigMapStruct mapStruct;
    @Autowired
    private ConfigClient configClient;

    @Override
    public StoreConfigDTO config(StoreConfigDTO dto) {
        HolderQueueConfigDO holderQueueConfigDO = mapStruct.toDo(dto);
        HolderQueueConfigDO db = baseMapper.selectOne(new LambdaQueryWrapper<HolderQueueConfigDO>()
                .eq(HolderQueueConfigDO::getStoreGuid, holderQueueConfigDO.getStoreGuid()));
        if (db != null) {
            update(holderQueueConfigDO, db);
        } else {
            /**
             *      加锁？  为什么不在外面加？
             *      ×××-》  考虑到插入的时候会重复调用而且插入的几率远远小于修改，加锁到修改又无效，故加锁在插入部分
             *      修改部分就不加锁
             */
            synchronized (QueueConfigServiceImpl.class) {
                HolderQueueConfigDO db_maybe_exit = baseMapper.selectOne(new LambdaQueryWrapper<HolderQueueConfigDO>()
                        .eq(HolderQueueConfigDO::getStoreGuid, holderQueueConfigDO.getStoreGuid()));
                if(db_maybe_exit!=null){
                    update(holderQueueConfigDO, db);
                }else{
                    insert(holderQueueConfigDO);
                    dto.setGuid(holderQueueConfigDO.getGuid());
                }
            }
        }
        return dto;

    }

    @Override
    public StoreConfigDTO obtain(String storeGuid) {
        if (StringUtils.isEmpty(storeGuid)) {
            throw new BusinessException("请选择门店");
        }
        HolderQueueConfigDO configDO = baseMapper.selectOne(
                new LambdaQueryWrapper<HolderQueueConfigDO>()
                        .eq(HolderQueueConfigDO::getStoreGuid, storeGuid)
        );
        if (configDO == null) {
            return config(StoreConfigDTO.DEFAULT.build(storeGuid));
        }

        return mapStruct.toDto(
                configDO
        );
    }

    private void insert(HolderQueueConfigDO holderQueueConfigDO) {
        holderQueueConfigDO.setGuid(QueuedUtils.nextConfigGuid());
        holderQueueConfigDO.setIsDeleted(false);
        baseMapper.insert(holderQueueConfigDO);
        if (holderQueueConfigDO.getIsEnableAutoReset() != null && holderQueueConfigDO.getIsEnableAutoReset()) {
            configClient.saveConfig(
                    ConfigReqDTO.builder()
                            .dicName(ConfigEnum.QUEUE_CLEAN_TIMING.getDesc())
                            .dicCode(ConfigEnum.QUEUE_CLEAN_TIMING.getCode())
                            .dictValue(holderQueueConfigDO.getAutoResetTiming().format(TIMING_FORMART))
                            .enterpriseGuid(UserContextUtils.getEnterpriseGuid())
                            .storeGuid(holderQueueConfigDO.getStoreGuid())
                            .gmtCreate(LocalDateTime.now())
                            .gmtModified(LocalDateTime.now())
                            .isEnable(1)
                            .build()
            );
        }
    }

    private void update(HolderQueueConfigDO holderQueueConfigDO, HolderQueueConfigDO db) {
        if (holderQueueConfigDO.getIsEnableAutoReset() != null && holderQueueConfigDO.getIsEnableAutoReset()) {
            if (holderQueueConfigDO.getAutoResetTiming() != null && ((db.getAutoResetTiming() == null && holderQueueConfigDO.getAutoResetTiming() != null) || !db.getAutoResetTiming().equals(holderQueueConfigDO.getAutoResetTiming()))) {
                doSave(holderQueueConfigDO, 1);
            }
        } else if ((holderQueueConfigDO.getIsEnableAutoReset() == null || !holderQueueConfigDO.getIsEnableAutoReset())
                && (db.getIsEnableAutoReset() != null && db.getIsEnableAutoReset())) {
            doSave(holderQueueConfigDO, 0);
        }
        baseMapper.update(holderQueueConfigDO, new LambdaQueryWrapper<HolderQueueConfigDO>()
                .eq(HolderQueueConfigDO::getStoreGuid, holderQueueConfigDO.getStoreGuid())
        );
    }

    public void doSave(HolderQueueConfigDO holderQueueConfigDO, Integer enable) {
        List<ConfigRespDTO> configRespDTOS = configClient.getConfig(
                ConfigReqDTO.builder()
                        .dicName(ConfigEnum.QUEUE_CLEAN_TIMING.getDesc())
                        .dicCode(ConfigEnum.QUEUE_CLEAN_TIMING.getCode())
                        .enterpriseGuid(UserContextUtils.getEnterpriseGuid())
                        .storeGuid(holderQueueConfigDO.getStoreGuid())
                        .isEnable(1)
                        .build()
        );
        ConfigRespDTO respDTO;
        if (configRespDTOS != null && !configRespDTOS.isEmpty()) {
            respDTO = configRespDTOS.get(0);
        } else {
            respDTO = new ConfigRespDTO();
            respDTO.setDicName(ConfigEnum.QUEUE_CLEAN_TIMING.getDesc());
            respDTO.setDicCode(ConfigEnum.QUEUE_CLEAN_TIMING.getCode());
            respDTO.setDictValue(Optional.ofNullable(holderQueueConfigDO.getAutoResetTiming()).map(e -> e.format(TIMING_FORMART)).orElse("-"));
            respDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            respDTO.setStoreGuid(holderQueueConfigDO.getStoreGuid());
            respDTO.setGmtCreate(LocalDateTime.now());
            respDTO.setGmtModified(LocalDateTime.now());
            respDTO.setIsEnable(1);
        }
        configClient.saveConfig(
                ConfigReqDTO.builder()
                        .guid(respDTO.getGuid())
                        .dicName(respDTO.getDicName())
                        .dicCode(respDTO.getDicCode())
                        .dictValue(Optional.ofNullable(holderQueueConfigDO.getAutoResetTiming()).map(e -> e.format(TIMING_FORMART)).orElse("-"))
                        .enterpriseGuid(respDTO.getEnterpriseGuid())
                        .storeGuid(respDTO.getStoreGuid())
                        .gmtModified(LocalDateTime.now())
                        .isEnable(enable)
                        .build()
        );
    }
}