package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AccountRecordDuplicatedException extends BusinessException {

    private static final long serialVersionUID = -3823416612907999142L;

    public AccountRecordDuplicatedException() {
        super("该营业日已存在");
    }

    public AccountRecordDuplicatedException(String message) {
        super("该营业日["+message+"]已存在");
    }

    public AccountRecordDuplicatedException(String message, Throwable cause) {
        super(message, cause);
    }
}
