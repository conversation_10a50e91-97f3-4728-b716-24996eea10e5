package com.holderzone.saas.store.business.service.impl;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.business.service.ShortMessageService;
import com.holderzone.saas.store.business.service.client.MarketClient;
import com.holderzone.saas.store.business.service.client.SmsEnterpriseClientService;
import com.holderzone.saas.store.business.service.client.SmsRechargeClient;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.trade.ShortMsgConfigDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.holderzone.saas.store.business.constant.Constant.SUCCESS_CODE;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ShortMessageServiceImpl
 * @date 2018/09/17 13:43
 * @description
 * @program holder-saas-store-business
 */
@Service
public class ShortMessageServiceImpl implements ShortMessageService {

    private static final Logger logger = LoggerFactory.getLogger(ShortMessageServiceImpl.class);

    private final AtomicInteger mCount = new AtomicInteger(0);

    private final ScheduledExecutorService se = Executors.newScheduledThreadPool(3, r -> new Thread(r, "Short_msg_polling-" + mCount.incrementAndGet()));

    private final SmsRechargeClient smsRechargeClient;

    private final MarketClient marketClient;

    private final SmsEnterpriseClientService smsEnterpriseClientService;

    private static final Map<String, ShortMsgRespDTO> MAP = new ConcurrentHashMap<>();

    @Autowired
    public ShortMessageServiceImpl(SmsRechargeClient smsRechargeClient, MarketClient marketClient, SmsEnterpriseClientService smsEnterpriseClientService) {
        this.smsRechargeClient = smsRechargeClient;
        this.marketClient = marketClient;
        this.smsEnterpriseClientService = smsEnterpriseClientService;
    }

    @Override
    public List<ChargeDTO> getAll() {
        return smsRechargeClient.getChargeListForSMS();
    }

    @Override
    public ShortMsgRespDTO charge(ProductOrderDTO productOrderDTO) {
        ShortMsgRespDTO shortMsgRespDTO = new ShortMsgRespDTO();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        String userGuid = UserContextUtils.getUserGuid();
        productOrderDTO.setEnterpriseGuid(enterpriseGuid);
        productOrderDTO.setStaffGuid(userGuid);
        productOrderDTO.setStaffName(UserContextUtils.getUserName());
        productOrderDTO.setStaffAccount(UserContextUtils.getUserName());
        ProductJHPayRespDTO productJHPayRespDTO = marketClient.charge(productOrderDTO);
        if (null != productJHPayRespDTO && SUCCESS_CODE.equals(productJHPayRespDTO.getCode())) {
            shortMsgRespDTO.setCode(productJHPayRespDTO.getCode());
            shortMsgRespDTO.setMsg(productJHPayRespDTO.getMsg());
            String attachData = productJHPayRespDTO.getAttachData();
            String[] split = attachData.split(":");
            if (split.length >= 3) {
                String paymentGuid = split[1];
                String payGuid = split[2];
                shortMsgRespDTO.setPaymentGuid(paymentGuid);
                shortMsgRespDTO.setPayGuid(payGuid);
                shortMsgRespDTO.setPaySt("10");
                MAP.put(shortMsgRespDTO.getPayGuid(), shortMsgRespDTO);
            }
            doPolling(shortMsgRespDTO);
        }
        logger.info("短信充值下单结果，shortMsgRespDTO={}", JacksonUtils.writeValueAsString(shortMsgRespDTO));
        return shortMsgRespDTO;
    }

    @Override
    public ShortMsgRespDTO polling(ShortMsgRespDTO shortMsgRespDTO) {
        ShortMsgRespDTO msgResp = MAP.get(shortMsgRespDTO.getPayGuid());
        if (null == msgResp) {
            ShortMsgPollingRespDTO polling = marketClient.polling(shortMsgRespDTO);
            if (null == polling) {
                return null;
            }
            msgResp = new ShortMsgRespDTO();
            BeanUtils.copyProperties(polling, msgResp);
        }
        return msgResp;
    }

    @Override
    public ShortMsgConfigDTO query() {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        if (StringUtils.hasText(enterpriseGuid)) {
            return smsEnterpriseClientService.query(enterpriseGuid);
        }
        return null;
    }

    @Override
    public boolean updateMessageConfig(ShortMsgConfigDTO shortMsgConfigDTO) {
        return smsEnterpriseClientService.update(shortMsgConfigDTO);
    }

    private void doPolling(ShortMsgRespDTO shortMsgRespDTO) {
        // 开始轮询支付结果
        AtomicInteger atomicInteger = new AtomicInteger(0);
        Map<String, Future> map = new ConcurrentHashMap<>(10);
        // 延迟1s执行，每1s执行一次。必须等上一个线程执行完后才开始下一个任务。
        // 当调用scheduleAtFixedRate()方法时候，即使上一个任务没完成，但是间隔时间到了，依然会发布任务
        ScheduledFuture<?> scheduledFuture = se.scheduleAtFixedRate(() -> {
            try {
                int count = atomicInteger.incrementAndGet();
                if (count == 120) {
                    MAP.remove(shortMsgRespDTO.getPayGuid());
                    map.get("id").cancel(true);
                    return;
                }
                ShortMsgPollingRespDTO polling = marketClient.polling(shortMsgRespDTO);
                logger.info("轮询结果 polling={}", JacksonUtils.writeValueAsString(polling));
                if (null != polling && Objects.equals(SUCCESS_CODE, polling.getCode())) {
                    shortMsgRespDTO.setCodeUrl(polling.getCodeUrl());
                    shortMsgRespDTO.setPaySt(polling.getPaySt());
                    MAP.put(shortMsgRespDTO.getPayGuid(), shortMsgRespDTO);
                    if ("2".equals(polling.getPaySt())
                            || "3".equals(polling.getPaySt())) {
                        logger.info("取消轮询 pollingSt={}", polling.getPaySt());
                        MAP.remove(shortMsgRespDTO.getPayGuid());
                        map.get("id").cancel(true);
                    }
                }
            } catch (Exception ignore) {
                MAP.remove(shortMsgRespDTO.getPayGuid());
            }
        }, 1, 1, TimeUnit.SECONDS);
        map.put("id", scheduledFuture);
    }

}
