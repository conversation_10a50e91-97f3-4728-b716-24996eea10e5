package com.holderzone.saas.store.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.business.entity.domain.ScreenPictureDO;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.common.SingleDataDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPictureService
 * @date 2018/09/06 17:40
 * @description
 * @program holder-saas-store-business
 */
public interface ScreenPictureService extends IService<ScreenPictureDO>{

    ScreenPictureConfigDTO getConfig(ScreenPicConfigReqDTO screenPicConfigReqDTO);

    String saveConfig(ScreenPictureConfigDTO screenPictureConfigDTO);


    /**
     * 文件上传
     *
     * @param screenPictureDTO
     */
    String save(ScreenPictureDTO screenPictureDTO);

    /**
     * 查询
     *
     * @param storeGuid
     * @return
     */
    List<ScreenAppRespDTO> queryByAndroid(String storeGuid);

    /**
     * 删除
     *
     * @param screenPictureGuid
     * @return
     */
    String delete(String screenPictureGuid);

    /**
     * 设置时间
     *
     * @param screenPicTimeDTO
     * @return
     */
    String setTime(ScreenPicTimeDTO screenPicTimeDTO);

    /**
     * 查询
     * @param screenPicQuery
     * @return
     */
    List<ScreenAppRespDTO> queryByWeb(ScreenPicQuery screenPicQuery);

    List<ScreenPicTimeDTO> getTime(ScreenPicTimeDTO screenPicTimeDTO);

    ScreenPictureConfigDTO queryStoreConfig(ScreenPicConfigReqDTO query);
}
