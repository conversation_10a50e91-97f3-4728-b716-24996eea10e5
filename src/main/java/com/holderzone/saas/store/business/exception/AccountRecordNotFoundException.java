package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.NotFoundException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AccountRecordNotFoundException extends NotFoundException {

    private static final long serialVersionUID = -715633124237538670L;

    public AccountRecordNotFoundException() {
        super("该营业日不存在");
    }

    public AccountRecordNotFoundException(String message) {
        super("该营业日["+message+"]不存在");
    }

    public AccountRecordNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
