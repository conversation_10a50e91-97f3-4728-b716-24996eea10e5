package com.holderzone.saas.store.business.controller;

import com.holderzone.saas.store.business.entity.domain.BindupAccountsDo;
import com.holderzone.saas.store.business.service.BindupAccountsService;
import com.holderzone.saas.store.business.service.client.MessageService;
import com.holderzone.saas.store.business.service.client.OrgFeignClient;
import com.holderzone.saas.store.dto.store.store.BindupAccountsDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 扎帐相关业务信息内容
 */
@Slf4j
@Api("扎帐配置相关接口")
@RestController
public class BindupAccountsController {

    @Resource
    private BindupAccountsService bindupAccountsService;

    @Resource
    private OrgFeignClient orgFeignClient;

    @Resource
    private MessageService messageService;

    static DateTimeFormatter dtf= DateTimeFormatter.ofPattern("yyyy-MM-dd");
    SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");


    @PostMapping("/bindupAccount/save")
    @ApiOperation(value = "保存扎帐记录信息", notes = "保存扎帐记录信息")
    public BindupAccountsDTO saveBindUpAccounts(@RequestParam("storeGuid") String storeGuid,
                                               @RequestParam("userGuid") String userGuid,
                                               @RequestParam("userName") String userName,
                                                @RequestParam("buAccounts") LocalDate buAccounts){

        BindupAccountsDo bindupAccountsDo = bindupAccountsService.saveBindUpAccounts(storeGuid, userGuid, userName,buAccounts);
        BindupAccountsDTO bindUpAccountsDto = new BindupAccountsDTO();
        BeanUtils.copyProperties(bindupAccountsDo,bindUpAccountsDto);
        return bindUpAccountsDto;
    }

    /**
     * 根据指定时间查询对应门店的扎帐记录信息
     * @param storeGuid
     * @param currentTime
     * @return
     */
    @PostMapping("/bindupAccount/query")
    @ApiOperation(value = "查询扎帐记录信息", notes = "保存扎帐记录信息")
    public List<BindupAccountsDo> queryBindUpAccounts(@RequestParam("storeGuid") String storeGuid,String currentTime){
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return bindupAccountsService.queryBindUpAccountsList(storeGuid, LocalDateTime.parse(currentTime, fmt));
    }

    /**
     * 获取门店的最新扎帐时间
     * @param storeGuid 门店guid
     * @return
     */
    @PostMapping("/bindupAccount/query/last")
    @ApiOperation(value = "获取门店的最新扎帐时间", notes = "获取门店的最新扎帐时间")
    public BindupAccountsDTO queryBindUpAccountsLast(@RequestParam("storeGuid") String storeGuid){
        BindupAccountsDo bindupAccountsDo = bindupAccountsService.queryBindUpAccountsLast(storeGuid);
        BindupAccountsDTO bindupAccountsDTO = new BindupAccountsDTO();
        if (bindupAccountsDo != null) {
            BeanUtils.copyProperties(bindupAccountsDo,bindupAccountsDTO);
        }
        return bindupAccountsDTO;
    }

    /**
     * 发送mq定时任务信息
     * @return
     */
    @GetMapping("/sendMqBindupAccounts")
    public Boolean sendMqBindupAccounts(){
        bindupAccountsService.sendMqBindupAccounts();
        return true;
    }

    /**
     * 发送mq信息，提示能够开台
     * @param storeGuid
     */
    @GetMapping("/sendmq/canopentable")
    public void sendMqForCanOpenTable(@RequestParam("storeGuid") String storeGuid){
        bindupAccountsService.sendMqForCanOpenTable(storeGuid);
    }


}
