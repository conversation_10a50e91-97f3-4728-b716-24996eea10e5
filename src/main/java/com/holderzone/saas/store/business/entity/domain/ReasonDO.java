package com.holderzone.saas.store.business.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReasonDO
 * @date 2019/08/15 14:55
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName(value = "hsb_reason_reason")
public class ReasonDO implements Serializable {

    private static final long serialVersionUID = -915958693193504136L;

    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "原因guid")
    private String reasonGuid;

    @ApiModelProperty(value = "商家guid")
    private String storeGuid;

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty("原因类型码")
    private Integer reasonTypeCode;

    @ApiModelProperty(value = "创建原因时间")
    private LocalDateTime reasonCreate;

//    @ApiModelProperty(value = "该原因使用次数")
//    @TableField(strategy = FieldStrategy.IGNORED)
//    private int reasonCount;
}