package com.holderzone.saas.store.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.business.entity.domain.BindupAccountsDo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPicTimeMapper
 * @date 2019/08/19 16:18
 * @description
 * @program holder-saas-store
 */
@Mapper
@Repository
public interface BindupAccountsMapper extends BaseMapper<BindupAccountsDo> {

    /**
     *
     * @param storeGuid
     * @return 查询最新数据
     */
    BindupAccountsDo queryBindUpAccountsLast(@Param("storeGuid") String storeGuid);

}
