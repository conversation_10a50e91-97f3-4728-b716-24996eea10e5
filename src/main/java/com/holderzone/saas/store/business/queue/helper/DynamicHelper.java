package com.holderzone.saas.store.business.queue.helper;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.business.queue.utils.SpringContextUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.sdk.util.IdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class DynamicHelper {

    private static final Logger log = LoggerFactory.getLogger(DynamicHelper.class);

    @Value("${self.open-dynamic-datasource}")
    private Boolean openDynamicDatasource;


    public void changeDatasource(String enterpriseGuid) {
        if (openDynamicDatasource) {
            try {
                UserContext userContext = new UserContext();
                userContext.setEnterpriseGuid(enterpriseGuid);
                UserContextUtils.put(userContext);
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
                log.info("手动切库成功，threadLocalCache:{}， enterpriseIdentifier.enterpriseGuid:{}",
                        UserContextUtils.get(), EnterpriseIdentifier.getEnterpriseGuid());
            } catch (Exception e) {
                log.error("手动切换数据源异常！enterpriseGuid:" + enterpriseGuid + ":{}", e.getMessage());
            }
        }
    }

    public void changeRedis(String enterpriseGuid) {
        if (openDynamicDatasource) {
            try {
                EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            } catch (Exception e) {
                log.error("手动切换数据源异常！enterpriseGuid:" + enterpriseGuid + ":{}", e.getMessage());
            }
        }
    }

    public void removeThreadLocalDatabaseInfo() {
        if (openDynamicDatasource) {
            EnterpriseIdentifier.remove();
        }
    }

    public void removeThreadLocalRedisInfo() {
        if (openDynamicDatasource) {
            EnterpriseIdentifier.remove();
        }
    }


    /**
     * 生成guid
     *
     * @return
     */
    public String generateGuid(String redisKey) {
        RedisTemplate redisTemplate = SpringContextUtils.getInstance().getBean("redisTemplate");
        return String.valueOf(IdGenerator.builder(redisTemplate, 5).next(redisKey));
    }

}
