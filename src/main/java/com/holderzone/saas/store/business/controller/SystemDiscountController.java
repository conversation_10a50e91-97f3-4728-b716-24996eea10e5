package com.holderzone.saas.store.business.controller;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.business.service.SystemDiscountService;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.holderzone.saas.store.business.config.BillConstant.FAILUER;
import static com.holderzone.saas.store.business.config.BillConstant.SUCCESS;


/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemDiscountController
 * @date 2018/08/15 18:05
 * @description
 * @program holder-saas-store-trading-center
 */
@RestController
@RequestMapping("/system")
public class SystemDiscountController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SystemDiscountController.class);

    private final SystemDiscountService systemDiscountService;

    @Autowired
    public SystemDiscountController(SystemDiscountService systemDiscountService) {
        this.systemDiscountService = systemDiscountService;
    }

    @PostMapping("/add")
    public String addSystemDiscount(@RequestBody SystemDiscountDTO systemDiscountDTO) {
        logger.info("新增系统省零配置，systemDiscountDTO={}", JacksonUtils.writeValueAsString(systemDiscountDTO));
        try {
            if (null == systemDiscountDTO.getState() || systemDiscountDTO.getState() > 1 || systemDiscountDTO.getState() < 0) {
                throw new BusinessException("state字段异常");
            }
            if (null == systemDiscountDTO.getStoreDTOS() || systemDiscountDTO.getStoreDTOS().isEmpty()) {
                throw new BusinessException("新增系统省零配置，门店数据不能为空");
            }
            systemDiscountService.addSystemDiscount(systemDiscountDTO);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("新增系统省零配置失败,e={}", e.getMessage());
            return "新增失败:" + e.getMessage();
        }
        return SUCCESS;
    }

    @PostMapping("/getAll/{storeGuid}")
    public List<SystemDiscountDTO> queryAll(@PathVariable("storeGuid") String storeGuid) {
        logger.info("查询系统省零，storeGuid={}", storeGuid);
        return systemDiscountService.queryAllSystemDis(storeGuid);
    }

    @PostMapping("/update")
    public String update(@RequestBody SystemDiscountDTO systemDiscountDTO) {
        logger.info("更新系统省零配置，systemDiscountDTO={}", JacksonUtils.writeValueAsString(systemDiscountDTO));
        try {

            if (null != systemDiscountDTO.getState()) {
                if ((systemDiscountDTO.getState() != 0 && systemDiscountDTO.getState() != 1)) {
                    throw new BusinessException("state字段异常 state=" + systemDiscountDTO.getState());
                }
            }
            if (null == systemDiscountDTO.getStoreDTOS() || systemDiscountDTO.getStoreDTOS().isEmpty()) {
                throw new BusinessException("门店数据不能为空");
            }
            systemDiscountService.update(systemDiscountDTO);
        } catch (Exception e) {
            logger.error("更新系统省零配置失败,e={}", e.getMessage());
            e.printStackTrace();
            return "更新失败！";
        }
        return SUCCESS;
    }

    @PostMapping("/delete/{storeGuid}/{systemDiscountGuid}")
    public String delete(@PathVariable("storeGuid") String storeGuid, @PathVariable("systemDiscountGuid") String systemDiscountGuid) {
        logger.info("删除系统省零规则，storeGuid={}，systemDiscountGuid={}", storeGuid, systemDiscountGuid);
        try {
            systemDiscountService.delete(storeGuid, systemDiscountGuid);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("删除系统省零规则失败，storeGuid={}，systemDiscountGuid={}", storeGuid, systemDiscountGuid);
            return FAILUER;
        }
        return SUCCESS;

    }

    @PostMapping("/get_sys_discount")
    public SystemDiscountDTO getByStoreGuid(@RequestBody SystemDiscountDTO systemDiscountDTO) {
        logger.info("获取商超企业系统省零规则入参：{}", JacksonUtils.writeValueAsString(systemDiscountDTO));
        return systemDiscountService.getByStoreGuid(systemDiscountDTO.getStoreGuid());
    }

    @PostMapping("/save_or_update")
    public SystemDiscountDTO saveOrUpdate(@RequestBody SystemDiscountDTO systemDiscountDTO) {
        logger.info("保存商超企业系统省零规则入参：{}", JacksonUtils.writeValueAsString(systemDiscountDTO));
        return systemDiscountService.saveOrUpdate(systemDiscountDTO);
    }


}
