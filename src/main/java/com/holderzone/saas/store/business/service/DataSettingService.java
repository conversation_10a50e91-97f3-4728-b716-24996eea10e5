package com.holderzone.saas.store.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.business.entity.domain.DataSettingDO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingQueryDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingSaveDTO;

import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/7
 * @since 1.8
 */
public interface DataSettingService extends IService<DataSettingDO> {

    List<DataSettingDTO> findDataSetting(DataSettingQueryDTO dataSettingDTO);

    Boolean saveDataSetting(DataSettingSaveDTO dataSettingSaveDTO);
}
