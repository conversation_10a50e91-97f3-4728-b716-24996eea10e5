package com.holderzone.saas.store.business.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.business.service.StoreConfigService;
import com.holderzone.saas.store.constant.Constant;
import com.holderzone.saas.store.dto.business.manage.StoreConfigCreateDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigUpdateDTO;
import com.holderzone.saas.store.dto.config.req.CashSettingReqDTO;
import com.holderzone.saas.store.dto.config.req.DineFoodSettingReqDTO;
import com.holderzone.saas.store.dto.config.req.PrintItemOrderConfigReq;
import com.holderzone.saas.store.dto.config.req.StoreFinishFoodConfigReq;
import com.holderzone.saas.store.dto.config.resp.CashSettingRespDTO;
import com.holderzone.saas.store.dto.config.resp.DineFoodSettingRespDTO;
import com.holderzone.saas.store.dto.config.resp.FinishFoodRespDTO;
import com.holderzone.saas.store.dto.order.request.AutoMarkReqDTO;
import com.holderzone.saas.store.dto.order.response.AutoMarkRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreConfigController
 * @date 2018/07/29 下午4:25
 * @description 门店配置Controller
 * @program holder-saas-store-business-center
 */
@Slf4j
@Api("门店配置相关接口")
@RestController
@RequestMapping("/storeConfig")
public class StoreConfigController {

    private final StoreConfigService storeConfigService;

    @Autowired
    public StoreConfigController(StoreConfigService storeConfigService) {
        this.storeConfigService = storeConfigService;
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建门店配置", notes = "创建门店配置")
    public void create(@RequestBody @Validated StoreConfigCreateDTO storeConfigCreateDTO) {
        log.info("创建门店配置接口入参：{}", JacksonUtils.writeValueAsString(storeConfigCreateDTO));
        storeConfigService.create(storeConfigCreateDTO);
    }

    @PostMapping("/query")
    @ApiOperation(value = "查询门店配置", notes = "查询门店配置")
    public StoreConfigDTO query(@RequestBody @Validated StoreConfigQueryDTO storeConfigQueryDTO) {
        return storeConfigService.query(storeConfigQueryDTO);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新门店配置", notes = "更新门店配置")
    public void update(@RequestBody @Validated StoreConfigUpdateDTO storeConfigUpdateDTO) {
        log.info("更新门店配置接口入参：{}", JacksonUtils.writeValueAsString(storeConfigUpdateDTO));
        storeConfigService.update(storeConfigUpdateDTO);
    }

    @GetMapping("/list")
    @ApiOperation(value = "查询所有门店配置", notes = "查询所有门店配置")
    public List<StoreConfigDTO> list() {
        return storeConfigService.list();
    }

    @PostMapping("/queryByIdList")
    @ApiOperation(value = "根据门店id数组查询门店配置", notes = "根据门店id数组查询门店配置")
    public List<StoreConfigDTO> queryByIdList(@RequestBody List<String> stringList) {
        return storeConfigService.queryByIdList(stringList);
    }

    @ApiOperation(value = "更新自动号牌状态", notes = "更新自动号牌状态")
    @ApiImplicitParam(name = "autoMarkReqDTO", value = "autoMarkReqDTO", required = true, dataType =
            "AutoMarkReqDTO")
    @PostMapping("/update_auto_mark")
    public boolean updateAutoMark(@RequestBody AutoMarkReqDTO autoMarkReqDTO) {
        log.info("order更新自动号牌状态 入参:{}", JacksonUtils.writeValueAsString(autoMarkReqDTO));

        StoreConfigUpdateDTO storeConfigUpdateDTO = new StoreConfigUpdateDTO();
        storeConfigUpdateDTO.setEnableAutoMark(autoMarkReqDTO.getAutoMark());
        storeConfigUpdateDTO.setStoreGuid(autoMarkReqDTO.getStoreGuid());
        if (StringUtils.isEmpty(autoMarkReqDTO.getInitMark())) {
            autoMarkReqDTO.setInitMark(Constant.FAST_FOOD_INTI_MARK);
        }
        storeConfigUpdateDTO.setInitMark(autoMarkReqDTO.getInitMark());
        storeConfigUpdateDTO.setEndMark(autoMarkReqDTO.getEndMark());

        storeConfigService.update(storeConfigUpdateDTO);
        return true;
    }

    @ApiOperation(value = "查询自动号牌状态", notes = "查询自动号牌状态")
    @ApiImplicitParam(name = "autoMarkReqDTO", value = "autoMarkReqDTO", required = true, dataType =
            "AutoMarkReqDTO")
    @PostMapping("/query_auto_mark")
    public Integer queryAutoMark(@RequestBody AutoMarkReqDTO autoMarkReqDTO) {
        log.info("order查询自动号牌状态：{}", JacksonUtils.writeValueAsString(autoMarkReqDTO));
        StoreConfigQueryDTO storeConfigQueryDTO = new StoreConfigQueryDTO();
        storeConfigQueryDTO.setStoreGuid(autoMarkReqDTO.getStoreGuid());
        StoreConfigDTO storeConfigDTO = storeConfigService.query(storeConfigQueryDTO);
        if (ObjectUtils.isEmpty(storeConfigDTO)) {
            storeConfigService.init(autoMarkReqDTO);
            storeConfigDTO = storeConfigService.query(storeConfigQueryDTO);
        }
        return storeConfigDTO.getEnableAutoMark();
    }


    @ApiOperation(value = "查询自动号牌设置", notes = "查询自动号牌设置")
    @ApiImplicitParam(name = "autoMarkReqDTO", value = "autoMarkReqDTO", required = true, dataType = "AutoMarkReqDTO")
    @PostMapping("/auto_mark/query")
    public AutoMarkRespDTO queryAutoMarkResp(@RequestBody AutoMarkReqDTO autoMarkReqDTO) {
        log.info("查询自动号牌设置：{}", JacksonUtils.writeValueAsString(autoMarkReqDTO));
        StoreConfigQueryDTO storeConfigQueryDTO = new StoreConfigQueryDTO();
        storeConfigQueryDTO.setStoreGuid(autoMarkReqDTO.getStoreGuid());
        StoreConfigDTO storeConfigDTO = storeConfigService.query(storeConfigQueryDTO);
        if (ObjectUtils.isEmpty(storeConfigDTO)) {
            storeConfigService.init(autoMarkReqDTO);
            storeConfigDTO = storeConfigService.query(storeConfigQueryDTO);
        }
        AutoMarkRespDTO result = new AutoMarkRespDTO();
        result.setAutoMark(storeConfigDTO.getEnableAutoMark());
        result.setInitMark(storeConfigDTO.getInitMark());
        result.setEndMark(storeConfigDTO.getEndMark());
        return result;
    }

    @ApiOperation(value = "更新出餐设置", notes = "更新出餐设置")
    @PostMapping("/update_finish_food")
    public boolean updateFinishFood(@RequestBody @Validated StoreFinishFoodConfigReq finishFoodConfigReq) {
        log.info("[更新出餐设置]finishFoodConfigReq={}", JacksonUtils.writeValueAsString(finishFoodConfigReq));
        StoreConfigUpdateDTO storeConfigUpdateDTO = new StoreConfigUpdateDTO();
        storeConfigUpdateDTO.setFinishFoodVoiceSwitch(finishFoodConfigReq.getFinishFoodVoiceSwitch());
        storeConfigUpdateDTO.setPrepTime(finishFoodConfigReq.getPrepTime());
        storeConfigUpdateDTO.setStoreGuid(finishFoodConfigReq.getStoreGuid());
        storeConfigService.updateFinishFood(storeConfigUpdateDTO);
        return true;
    }

    @ApiOperation(value = "查询出餐设置", notes = "查询出餐设置")
    @ApiImplicitParam(name = "configQueryDTO", value = "configQueryDTO", required = true, dataType = "StoreConfigQueryDTO")
    @PostMapping("/finish_food/query")
    public FinishFoodRespDTO queryFinishFood(@RequestBody @Validated StoreConfigQueryDTO configQueryDTO) {
        log.info("[查询出餐设置]configQueryDTO={}", JacksonUtils.writeValueAsString(configQueryDTO));
        StoreConfigQueryDTO queryFinishFoodDTO = new StoreConfigQueryDTO();
        queryFinishFoodDTO.setStoreGuid(configQueryDTO.getStoreGuid());
        StoreConfigDTO storeConfigDTO = storeConfigService.query(queryFinishFoodDTO);
        FinishFoodRespDTO respDTO = new FinishFoodRespDTO();
        if (!ObjectUtils.isEmpty(storeConfigDTO)) {
            respDTO.setPrepTime(storeConfigDTO.getPrepTime());
            respDTO.setFinishFoodVoiceSwitch(storeConfigDTO.getFinishFoodVoiceSwitch());
        }
        return respDTO;
    }

    @ApiOperation(value = "查询商品打印顺序配置", notes = "查询商品打印顺序配置")
    @PostMapping("/print_item_order/query")
    public String queryPrintItemOrderConfig(@RequestBody @Validated StoreConfigQueryDTO configQueryDTO) {
        log.info("[查询商品打印顺序配置]configQueryDTO={}", JacksonUtils.writeValueAsString(configQueryDTO));
        return storeConfigService.queryPrintItemOrderConfig(configQueryDTO);
    }

    @ApiOperation(value = "修改商品打印顺序配置", notes = "查询商品打印顺序配置")
    @PostMapping("/print_item_order/update")
    public boolean updatePrintItemOrderConfig(@RequestBody @Validated PrintItemOrderConfigReq configReq) {
        log.info("[修改商品打印顺序配置]configQueryDTO={}", JacksonUtils.writeValueAsString(configReq));
        StoreConfigUpdateDTO storeConfigUpdateDTO = new StoreConfigUpdateDTO();
        storeConfigUpdateDTO.setStoreGuid(configReq.getStoreGuid());
        storeConfigUpdateDTO.setPrintItemOrder(configReq.getPrintItemOrder());
        storeConfigService.updatePrintItemOrderConfig(storeConfigUpdateDTO);
        return true;
    }

    @ApiOperation(value = "查询正餐设置")
    @PostMapping("/dine_food_setting/query")
    public DineFoodSettingRespDTO queryDineFoodSetting(@RequestBody @Validated StoreConfigQueryDTO configQueryDTO) {
        log.info("[查询正餐设置]configQueryDTO={}", JacksonUtils.writeValueAsString(configQueryDTO));
        StoreConfigDTO storeConfigDTO = storeConfigService.query(configQueryDTO);
        DineFoodSettingRespDTO respDTO = new DineFoodSettingRespDTO();
        if (!ObjectUtils.isEmpty(storeConfigDTO)) {
            respDTO.setEnableHandleClose(storeConfigDTO.getEnableHandleClose());
            respDTO.setEnableMultiplePay(storeConfigDTO.getEnableMultiplePay());
            respDTO.setEnableDineInChangeDiffValue(storeConfigDTO.getEnableDineInChangeDiffValue());
            respDTO.setEnableFastChangeDiffValue(storeConfigDTO.getEnableFastChangeDiffValue());
            respDTO.setAutoMark(storeConfigDTO.getEnableAutoMark());
            respDTO.setInitMark(storeConfigDTO.getInitMark());
            respDTO.setEndMark(storeConfigDTO.getEndMark());
        }
        return respDTO;
    }

    @ApiOperation(value = "更新正餐设置")
    @PostMapping("/dine_food_setting/update")
    public void updateDineFoodSetting(@RequestBody @Validated DineFoodSettingReqDTO settingReqDTO) {
        log.info("[更新正餐设置]settingReqDTO={}", JacksonUtils.writeValueAsString(settingReqDTO));
        storeConfigService.updateDineFoodSetting(settingReqDTO);
    }

    @ApiOperation(value = "收银设置", notes = "查询收银设置")
    @PostMapping("/cash_setting/query")
    public CashSettingRespDTO queryCashSetting(@RequestBody @Validated CashSettingReqDTO cashSettingReqDTO) {
        log.info("[查询收银设置]cashSettingReqDTO={}", JacksonUtils.writeValueAsString(cashSettingReqDTO));
        StoreConfigQueryDTO storeConfigQueryDTO = new StoreConfigQueryDTO();
        storeConfigQueryDTO.setStoreGuid(cashSettingReqDTO.getStoreGuid());
        StoreConfigDTO storeConfigDTO = storeConfigService.query(storeConfigQueryDTO);
        CashSettingRespDTO respDTO = new CashSettingRespDTO();
        if (ObjectUtils.isEmpty(storeConfigDTO)) {
            storeConfigService.initCashSetting(cashSettingReqDTO);
            storeConfigDTO = storeConfigService.query(storeConfigQueryDTO);
        }
        respDTO.setEnableHandleClose(storeConfigDTO.getEnableHandleClose());
        respDTO.setEnableMultiplePay(storeConfigDTO.getEnableMultiplePay());
        respDTO.setEnableDineInChangeDiffValue(storeConfigDTO.getEnableDineInChangeDiffValue());
        respDTO.setEnableFastChangeDiffValue(storeConfigDTO.getEnableFastChangeDiffValue());
        respDTO.setAutoMark(storeConfigDTO.getEnableAutoMark());
        respDTO.setInitMark(storeConfigDTO.getInitMark());
        return respDTO;
    }
}
