package com.holderzone.saas.store.business.controller;

import com.holderzone.saas.store.business.service.CashboxRecordService;
import com.holderzone.saas.store.dto.business.manage.CashboxRecordCreateDTO;
import com.holderzone.saas.store.dto.business.manage.CashboxRecordDTO;
import com.holderzone.saas.store.dto.business.manage.CashboxRecordListDTO;
import com.holderzone.saas.store.dto.business.manage.CashboxRecordQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CashboxRecordController
 * @date 2018/07/29 下午6:05
 * @description 钱箱Controller（一期项目钱箱服务只提供交班时记录收入总额的作用！！！）
 * @program holder-saas-store-business-center
 */
@RestController
@RequestMapping("/cashboxRecord")
public class CashboxRecordController {

    private final CashboxRecordService cashboxRecordService;

    @Autowired
    public CashboxRecordController(CashboxRecordService cashboxRecordService) {
        this.cashboxRecordService = cashboxRecordService;
    }

    /**
     * 添加钱箱记录（开钱箱存取款、收银存取款的方式）
     *
     * @param cashboxRecordCreateDTO 请求实体
     * @return 操作结果
     */
    @PostMapping("/create")
    public void create(@RequestBody @Validated CashboxRecordCreateDTO cashboxRecordCreateDTO) {
        cashboxRecordService.create(cashboxRecordCreateDTO);
    }

    @PostMapping("/query")
    public CashboxRecordDTO query(@RequestBody @Validated CashboxRecordQueryDTO cashboxRecordQueryDTO) {
        return cashboxRecordService.query(cashboxRecordQueryDTO);
    }

    @PostMapping("/list")
    public List<CashboxRecordDTO> list(@RequestBody @Validated CashboxRecordListDTO cashboxRecordListDTO) {
        return cashboxRecordService.list(cashboxRecordListDTO);
    }
}
