package com.holderzone.saas.store.business.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.business.entity.domain.BindupAccountsDo;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPicTimeService
 * @date 2019/08/19 16:19
 * @description
 * @program holder-saas-store
 */
public interface BindupAccountsService extends IService<BindupAccountsDo> {

    /**
     * 查询指定门店对应时间的扎帐信息
     * @param storeGuid         门店guid
     * @param currentTime        门店当前登录时间前一天的营业时间
     * @return
     */
    List<BindupAccountsDo> queryBindUpAccountsList(String storeGuid, LocalDateTime currentTime);

    /**
     * 获取门店最新的扎帐时间
     * @param storeGuid
     * @return
     */
    BindupAccountsDo queryBindUpAccountsLast(String storeGuid);

    /**
     * 保存扎帐信息
     * @param storeGuid
     * @param userGuid
     * @param userName
     * @return
     */
    BindupAccountsDo saveBindUpAccounts(String storeGuid, String userGuid, String userName, LocalDate buAccounts);

    /**
     * 创建本
     */
    void sendMqBindupAccounts();

    /**
     * 提示企业能够开台
     * @param storeGuid         门店guid
     */
    void sendMqForCanOpenTable(String storeGuid);
}
