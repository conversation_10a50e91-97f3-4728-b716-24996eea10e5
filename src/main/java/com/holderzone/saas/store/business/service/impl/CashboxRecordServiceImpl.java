package com.holderzone.saas.store.business.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.business.entity.domain.CashboxRecordDO;
import com.holderzone.saas.store.business.mapper.CashboxRecordMapper;
import com.holderzone.saas.store.business.mapstruct.CashboxRecordMapstruct;
import com.holderzone.saas.store.business.service.CashboxRecordService;
import com.holderzone.saas.store.business.service.HandoverRecordService;
import com.holderzone.saas.store.business.utils.GuidUtils;
import com.holderzone.saas.store.dto.business.manage.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CashboxRecordServiceImpl
 * @date 2018/07/29 下午6:06
 * @description 钱箱服务相关接口实现
 * @program holder-saas-store-business-center
 */
@Service
public class CashboxRecordServiceImpl implements CashboxRecordService {

    private final CashboxRecordMapper cashboxRecordMapper;

    private final HandoverRecordService handoverRecordService;

    @Autowired
    public CashboxRecordServiceImpl(CashboxRecordMapper cashboxRecordMapper, @Lazy HandoverRecordService handoverRecordService) {
        this.cashboxRecordMapper = cashboxRecordMapper;
        this.handoverRecordService = handoverRecordService;
    }

    @Override
    public void create(CashboxRecordCreateDTO cashboxRecordCreateDTO) {
        // 实体转换
        CashboxRecordDO cashboxRecordDO = CashboxRecordMapstruct.INSTANCE.toCashboxRecordDO(cashboxRecordCreateDTO);
        // 查询交接班记录
        HandoverRecordQueryAllDTO handoverRecordQueryAllDTO = new HandoverRecordQueryAllDTO();
        handoverRecordQueryAllDTO.setStoreGuid(cashboxRecordCreateDTO.getStoreGuid());
        handoverRecordQueryAllDTO.setDeviceId(cashboxRecordCreateDTO.getTerminalId());
        handoverRecordQueryAllDTO.setStatus(0);
        handoverRecordQueryAllDTO.setPageSize(Integer.MAX_VALUE);
        List<HandoverRecordDTO> handoverRecordDTOS = handoverRecordService.queryByPage(handoverRecordQueryAllDTO).getData();
        if (handoverRecordDTOS.isEmpty()) {
            throw new BusinessException("不存在门店接班记录，无法操作钱箱");
        }
        HandoverRecordDTO handoverRecordDTO = handoverRecordDTOS.get(0);

        cashboxRecordDO.setCashboxRecordGuid(GuidUtils.nextCashboxRecordGuid());
        cashboxRecordDO.setHandoverRecordGuid(handoverRecordDTO.getHandoverRecordGuid());
        cashboxRecordDO.setTerminalId(handoverRecordDTO.getTerminalId());
        // 更新数据库
        if (0 == cashboxRecordMapper.insert(cashboxRecordDO)) {
            throw new BusinessException("钱箱记录保存失败，请联系管理员");
        }
    }

    @Override
    public CashboxRecordDTO query(CashboxRecordQueryDTO cashboxRecordQueryDTO) {
        CashboxRecordDO cashboxRecordDO = CashboxRecordMapstruct.INSTANCE.toCashboxRecordDO(cashboxRecordQueryDTO);
        CashboxRecordDO cashboxRecordInSql = cashboxRecordMapper.query(cashboxRecordDO);
        if (null == cashboxRecordInSql) {
            throw new BusinessException("钱箱记录[" + cashboxRecordQueryDTO.getCashboxRecordGuid() + "]不存在");
        }
        return CashboxRecordMapstruct.INSTANCE.toCashboxRecordDTO(cashboxRecordInSql);
    }

    @Override
    public List<CashboxRecordDTO> list(CashboxRecordListDTO cashboxRecordListDTO) {
        CashboxRecordDO cashboxRecordDO = CashboxRecordMapstruct.INSTANCE.toCashboxRecordDO(cashboxRecordListDTO);
        List<CashboxRecordDO> cashboxRecordDOS = cashboxRecordMapper.queryAll(cashboxRecordDO);
        if (cashboxRecordDOS.isEmpty()) {
            return Collections.emptyList();
        }
        return CashboxRecordMapstruct.INSTANCE.toCashboxRecordDTOS(cashboxRecordDOS);
    }
}
