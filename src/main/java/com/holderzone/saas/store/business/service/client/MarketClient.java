package com.holderzone.saas.store.business.service.client;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.business.manage.ProductJHPayRespDTO;
import com.holderzone.saas.store.dto.business.manage.ProductOrderDTO;
import com.holderzone.saas.store.dto.business.manage.ShortMsgPollingRespDTO;
import com.holderzone.saas.store.dto.business.manage.ShortMsgRespDTO;
import feign.hystrix.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MarketClient
 * @date 2019/03/19 15:35
 * @description
 * @program holder-saas-store-business
 */
@Component
@FeignClient(value = "holder-saas-cloud-market", fallbackFactory = MarketClient.MarketFallBack.class)
public interface MarketClient {


    @PostMapping("/market/sms/order")
    ProductJHPayRespDTO charge(ProductOrderDTO productOrderDTO);

    @PostMapping("/market/jh/polling")
    ShortMsgPollingRespDTO polling(ShortMsgRespDTO shortMsgRespDTO);

    @Component
    class MarketFallBack implements FallbackFactory<MarketClient> {
        @Override
        public MarketClient create(Throwable throwable) {
            return new MarketClient() {

                @Override
                public ShortMsgPollingRespDTO polling(ShortMsgRespDTO shortMsgRespDTO) {
                    throwable.printStackTrace();
                    throw new BusinessException("调用云端异常！");
                }

                @Override
                public ProductJHPayRespDTO charge(ProductOrderDTO productOrderDTO) {
                    throwable.printStackTrace();
                    ProductJHPayRespDTO productJHPayRespDTO = new ProductJHPayRespDTO();
                    productJHPayRespDTO.setMsg(throwable.getMessage());
                    productJHPayRespDTO.setCode("10005");
                    return productJHPayRespDTO;
                }
            };
        }
    }

}
