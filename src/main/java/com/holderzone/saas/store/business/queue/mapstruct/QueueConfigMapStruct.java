package com.holderzone.saas.store.business.queue.mapstruct;

import com.holderzone.saas.store.business.queue.domain.HolderQueueConfigDO;
import com.holderzone.saas.store.dto.queue.StoreConfigDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueMapStruct
 * @date 2019/03/27 17:21
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Component
@Mapper(componentModel = "spring")
public interface QueueConfigMapStruct {
    @Mappings(
            {
                    @Mapping(target = "recoveryNum", expression = "java(configDO.getRecoveryNum()==null?null:configDO.getRecoveryNum().toString())")
            }
    )
    StoreConfigDTO toDto(HolderQueueConfigDO configDO);

    HolderQueueConfigDO toDo(StoreConfigDTO configDO);
}