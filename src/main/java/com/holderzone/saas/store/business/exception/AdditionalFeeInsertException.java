package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AdditionalFeeInsertException extends BusinessException {

    private static final long serialVersionUID = 4119865412247647896L;

    public AdditionalFeeInsertException() {
        super("附加费保存失败，请联系管理员");
    }

    public AdditionalFeeInsertException(String message) {
        super("附加费["+message+"]保存失败，请联系管理员");
    }

    public AdditionalFeeInsertException(String message, Throwable cause) {
        super(message, cause);
    }
}
