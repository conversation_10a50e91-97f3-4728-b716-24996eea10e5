package com.holderzone.saas.store.business.event;

import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.saas.store.business.service.PaymentTypeService;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.business.utils.ThrowableExtUtils;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketListenerHandler(
        topic = MqConstant.DOWNSTREAM_STORE_TOPIC,
        tags = MqConstant.DOWNSTREAM_STORE_CREATE_TAG,
        consumerGroup = MqConstant.DOWNSTREAM_STORE_INIT_PAYMENT_GROUP
)
public class InitPaymentListener extends AbstractRocketMqConsumer<RocketMqTopic, StoreDTO> {

    private final PaymentTypeService paymentTypeService;

    @Autowired
    public InitPaymentListener(PaymentTypeService paymentTypeService) {
        this.paymentTypeService = paymentTypeService;
    }

    @Override
    public boolean consumeMsg(StoreDTO storeDTO, MessageExt messageExt) {
        UserContextUtils.put(messageExt.getProperty(MqConstant.DOWNSTREAM_CONTEXT));
        try {
            EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            paymentTypeService.init(storeDTO.getGuid(), storeDTO.getName(), storeDTO.getMchntTypeCode());
            log.info("门店：{} 初始化支付方式成功", storeDTO.getName());
        } catch (Exception e) {
            log.error("门店：{} 初始化支付方式发生错误：{}", storeDTO.getName(), ThrowableExtUtils.asStringIfAbsent(e));
            return false;
        } finally {
            EnterpriseIdentifier.remove();
        }
        return true;
    }
}
