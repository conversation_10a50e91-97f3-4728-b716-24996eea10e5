package com.holderzone.saas.store.business.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.business.entity.domain.SurchargeAreaDO;
import com.holderzone.saas.store.business.entity.domain.SurchargeDO;
import com.holderzone.saas.store.business.entity.query.SurchargeCountQuery;
import com.holderzone.saas.store.business.mapper.SurchargeMapper;
import com.holderzone.saas.store.business.mapstruct.SurchargeMapstruct;
import com.holderzone.saas.store.business.service.RedisService;
import com.holderzone.saas.store.business.service.SurchargeAreaService;
import com.holderzone.saas.store.business.service.SurchargeService;
import com.holderzone.saas.store.business.service.client.MessageService;
import com.holderzone.saas.store.business.service.client.TableRpcService;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeAggDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeCalculateDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeSyncDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SurchargeServiceImpl
 * @date 2018/08/02 下午3:35
 * @description //TODO
 * @program holder-saas-store-business
 */

@Slf4j
@Service
public class SurchargeServiceImpl extends ServiceImpl<SurchargeMapper, SurchargeDO> implements SurchargeService {

    private final SurchargeAreaService surchargeAreaService;

    private final SurchargeMapstruct surchargeMapstruct;

    private final RedisService redisService;

    private final MessageService messageService;

    private final TableRpcService tableRpcService;

    public static final String ALL_AREA_GUID = "0000000000000000000";

    @Autowired
    public SurchargeServiceImpl(SurchargeAreaService surchargeAreaService,
                                RedisService redisService, SurchargeMapstruct surchargeMapstruct, MessageService messageService, TableRpcService tableRpcService) {
        this.surchargeAreaService = surchargeAreaService;
        this.surchargeMapstruct = surchargeMapstruct;
        this.redisService = redisService;
        this.messageService = messageService;
        this.tableRpcService = tableRpcService;
    }

    private void checkSurchargeName(String storeGuid, String surchargeGuid, String name) {
        LambdaQueryWrapper<SurchargeDO> wrapper = new LambdaQueryWrapper<SurchargeDO>()
                .eq(SurchargeDO::getStoreGuid, storeGuid)
                .eq(SurchargeDO::getName, name);
        if (!StringUtils.isEmpty(surchargeGuid)) {
            wrapper.ne(SurchargeDO::getSurchargeGuid, surchargeGuid);
        }
        if (count(wrapper) > 0) throw new BusinessException("该门店已存在相同名称的附加费名称");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addSurcharge(SurchargeCreateDTO surchargeCreateDTO) {
        if (surchargeCreateDTO.getAreaGuidList() == null || surchargeCreateDTO.getAreaGuidList().size() == 0) {
            List<String> areaList = new ArrayList<String>();
            areaList.add(ALL_AREA_GUID);
            surchargeCreateDTO.setAreaGuidList(areaList);
        }

        // 检查附加费名称
        checkSurchargeName(surchargeCreateDTO.getStoreGuid(), null, surchargeCreateDTO.getName());

        // 添加附加费
        surchargeCreateDTO.setSurchargeGuid(redisService.nextSurchargeGuid());
        SurchargeDO surchargeDO = surchargeMapstruct.fromUpdate(surchargeCreateDTO);
        surchargeDO.setTradeMode(String.join(",", surchargeCreateDTO.getTradeModes()));
        if (Objects.isNull(surchargeDO.getEffectiveTime())) {
            surchargeDO.setEffectiveTime(0);
        }
        save(surchargeDO);

        // 添加附加费的关联区域
        surchargeAreaService.bindSurchargeArea(surchargeCreateDTO);

        return surchargeCreateDTO.getSurchargeGuid();
    }

    @Override
    public SurchargeDTO querySurcharge(SurchargeQueryDTO surchargeQueryDTO) {
        String surchargeGuid = surchargeQueryDTO.getSurchargeGuid();

        // 附加费
        SurchargeDO surchargeInSql = getOne(new LambdaQueryWrapper<SurchargeDO>()
                .eq(SurchargeDO::getSurchargeGuid, surchargeGuid));
        if (surchargeInSql == null) {
            log.error("未找到附加费[{}]", surchargeGuid);
            throw new BusinessException("未找到指定的附加费");
        }
        SurchargeDTO surchargeDTO = surchargeMapstruct.toRespDTO(surchargeInSql);
        if (Objects.isNull(surchargeDTO.getEffectiveTime()) || surchargeDTO.getEffectiveTime() == 0) {
            surchargeDTO.setEffectiveTime(null);
        }
        surchargeDTO.setTradeModes(Arrays.asList(surchargeInSql.getTradeMode().split(",")));

        // 附加费关联区域
        List<SurchargeAreaDTO> surchargeAreaList = surchargeAreaService.findSurchargeArea(
                surchargeGuid, surchargeInSql.getStoreGuid());

        return surchargeDTO.setAreaList(surchargeAreaList);
    }

    @Override
    public void updateSurcharge(SurchargeUpdateDTO surchargeUpdateDTO) {
        if (surchargeUpdateDTO.getAreaGuidList() == null || surchargeUpdateDTO.getAreaGuidList().size() == 0) {
            List<String> areaList = new ArrayList<>();
            areaList.add(ALL_AREA_GUID);
            surchargeUpdateDTO.setAreaGuidList(areaList);
        }
        // 检查附加费名称
        String storeGuid = surchargeUpdateDTO.getStoreGuid();
        checkSurchargeName(storeGuid, surchargeUpdateDTO.getSurchargeGuid(), surchargeUpdateDTO.getName());

        // 更新附加费
        SurchargeDO surchargeDO = surchargeMapstruct.fromUpdate(surchargeUpdateDTO);
        surchargeDO.setTradeMode(String.join(",", surchargeUpdateDTO.getTradeModes()));
        if (Objects.isNull(surchargeDO.getEffectiveTime())) {
            surchargeDO.setEffectiveTime(0);
        }
        update(surchargeDO, new LambdaQueryWrapper<SurchargeDO>()
                .eq(SurchargeDO::getSurchargeGuid, surchargeUpdateDTO.getSurchargeGuid()));

        // 更新附加费的关联区域
        surchargeAreaService.rebindSurchargeArea(surchargeUpdateDTO);

        // 推送消息给pad
        BusinessMessageDTO surchargeMessageDTO = new BusinessMessageDTO();
        surchargeMessageDTO.setMessageType(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getId());
        surchargeMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getId());
        surchargeMessageDTO.setSubject(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getName());
        surchargeMessageDTO.setPlatform("2");
        surchargeMessageDTO.setStoreGuid(storeGuid);
        surchargeMessageDTO.setContent("附加费修改信息");
        log.info("附加费修改信息 surchargeMessageDTO={}", JacksonUtils.writeValueAsString(surchargeMessageDTO));
        messageService.msg(surchargeMessageDTO);
    }

    @Override
    public void enableSurcharge(SurchargeEnableDTO surchargeEnableDTO) {
        Wrapper<SurchargeDO> wrapper = new LambdaQueryWrapper<SurchargeDO>()
                .eq(SurchargeDO::getSurchargeGuid, surchargeEnableDTO.getSurchargeGuid());
        update(new SurchargeDO().setIsEnable(surchargeEnableDTO.getIsEnable()), wrapper);
    }

    @Override
    public void deleteSurcharge(SurchargeDeleteDTO surchargeDeleteDTO) {
        remove(new LambdaQueryWrapper<SurchargeDO>()
                .eq(SurchargeDO::getSurchargeGuid, surchargeDeleteDTO.getSurchargeGuid()));
        surchargeAreaService.removeSurchargeArea(surchargeDeleteDTO.getSurchargeGuid());
    }

    @Override
    public void batchEnableSurcharge(SurchargeBatchEnableDTO surchargeBatchEnableDTO) {
        if (CollectionUtils.isEmpty(surchargeBatchEnableDTO.getSurchargeGuidList())) {
            throw new BusinessException("请选择附加费");
        }
        ;
        Wrapper<SurchargeDO> wrapper = new LambdaQueryWrapper<SurchargeDO>()
                .in(SurchargeDO::getSurchargeGuid, surchargeBatchEnableDTO.getSurchargeGuidList());
        update(new SurchargeDO().setIsEnable(surchargeBatchEnableDTO.getIsEnable()), wrapper);
    }

    @Override
    public void batchDeleteSurcharge(SurchargeBatchDeleteDTO surchargeBatchDeleteDTO) {
        if (CollectionUtils.isEmpty(surchargeBatchDeleteDTO.getSurchargeGuidList())) {
            throw new BusinessException("请选择附加费");
        }
        ;
        remove(new LambdaQueryWrapper<SurchargeDO>()
                .in(SurchargeDO::getSurchargeGuid, surchargeBatchDeleteDTO.getSurchargeGuidList()));
        surchargeAreaService.removeSurchargeArea(surchargeBatchDeleteDTO.getSurchargeGuidList());
    }

    @Override
    public Page<SurchargeDTO> listByType(SurchargeListDTO surchargeListDTO) {
        SurchargeCountQuery surchargeCountQuery = new SurchargeCountQuery();
        surchargeCountQuery.setStoreGuid(surchargeListDTO.getStoreGuid());
        surchargeCountQuery.setType(surchargeListDTO.getType());
        long totalCount = baseMapper.countByType(surchargeCountQuery);
        Page<SurchargeDTO> resultPage = new Page<>(
                surchargeListDTO.getCurrentPage(), surchargeListDTO.getPageSize(), totalCount);
        if (totalCount == 0) return resultPage;
        List<SurchargeDO> surchargeDOList = baseMapper.pageByType(surchargeListDTO);
        if (CollectionUtils.isEmpty(surchargeDOList)) return resultPage;
        List<SurchargeDTO> surchargeRespList = surchargeMapstruct.toRespDTO(surchargeDOList);
        if (!CollectionUtils.isEmpty(surchargeRespList)) {
            surchargeRespList.forEach(e -> e.setTradeModes(Arrays.asList(e.getTradeMode().split(","))));
        }
        resultPage.setData(surchargeRespList);
        return resultPage;
    }

    @Override
    public Map<String, List<SurchargeLinkDTO>> listByAreaGuid(List<String> areaList, Integer tradeMode) {
        if (Objects.isNull(tradeMode)) {
            tradeMode = TradeModeEnum.DINEIN.getCode();
        }
        if (CollectionUtils.isEmpty(areaList)) return Collections.emptyMap();

        List<SurchargeAreaDO> areaSurchargeList = surchargeAreaService.findAreaSurcharge(areaList);

        if (CollectionUtils.isEmpty(areaSurchargeList)) return Collections.emptyMap();

        Set<String> surchargeGuidList = areaSurchargeList.stream()
                .map(SurchargeAreaDO::getSurchargeGuid).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(surchargeGuidList)) {
            return Collections.emptyMap();
        }
        List<SurchargeDO> surchargeDOList = list(new LambdaQueryWrapper<SurchargeDO>()
                .eq(SurchargeDO::getIsEnable, true)
                .like(SurchargeDO::getTradeMode, tradeMode)
                .in(SurchargeDO::getSurchargeGuid, surchargeGuidList)
        );

        if (CollectionUtils.isEmpty(surchargeDOList)) return Collections.emptyMap();

        Map<String, SurchargeDO> surchargeMap = surchargeDOList.stream()
                .collect(Collectors.toMap(SurchargeDO::getSurchargeGuid, Function.identity()));

        return areaSurchargeList.stream()
                .filter(surchargeAreaDO -> surchargeMap.get(surchargeAreaDO.getSurchargeGuid()) != null)
                .map(surchargeAreaDO -> convertToLinkDTO(surchargeAreaDO, surchargeMap.get(surchargeAreaDO.getSurchargeGuid())))
                .collect(Collectors.groupingBy(SurchargeLinkDTO::getAreaGuid));
    }
    @Override
    public Map<String, List<SurchargeLinkDTO>> listByAreaGuidAndStore(String storeGuid, List<String> areaList) {
        if (CollectionUtils.isEmpty(areaList)) {
            return Collections.emptyMap();
        }

        // 查询启用的附加费
        List<SurchargeDO> surchargeDOList = list(new LambdaQueryWrapper<SurchargeDO>()
                .eq(SurchargeDO::getIsEnable, true)
                .eq(SurchargeDO::getIsDeleted, false)
                .like(SurchargeDO::getTradeMode, TradeModeEnum.DINEIN.getCode())
                .eq(SurchargeDO::getStoreGuid, storeGuid));

        if (CollectionUtils.isEmpty(surchargeDOList)) {
            return Collections.emptyMap();
        }

        // 获取附加费区域关联
        List<String> surchargeGuidList = surchargeDOList.stream()
                .map(SurchargeDO::getSurchargeGuid)
                .collect(Collectors.toList());

        List<SurchargeAreaDO> areaSurchargeList = surchargeAreaService
                .findAreaSurchargeBySurchargeGuids(surchargeGuidList);

        if (CollectionUtils.isEmpty(areaSurchargeList)) {
            return Collections.emptyMap();
        }

        // 处理全区域附加费
        List<SurchargeAreaDO> allAreaSurcharges = areaSurchargeList.stream()
                .filter(e -> ALL_AREA_GUID.equals(e.getAreaGuid()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(allAreaSurcharges)) {
            List<SurchargeAreaDO> expandedAreaList = new ArrayList<>();
            for (SurchargeAreaDO allArea : allAreaSurcharges) {
                for (String areaGuid : areaList) {
                    SurchargeAreaDO newArea = new SurchargeAreaDO();
                    BeanUtil.copyProperties(allArea, newArea);
                    newArea.setAreaGuid(areaGuid);
                    expandedAreaList.add(newArea);
                }
            }
            areaSurchargeList.addAll(expandedAreaList);
        }

        // 转换为 DTO
        Map<String, SurchargeDO> surchargeMap = surchargeDOList.stream()
                .collect(Collectors.toMap(SurchargeDO::getSurchargeGuid, Function.identity()));

        return areaSurchargeList.stream()
                .filter(area -> surchargeMap.containsKey(area.getSurchargeGuid()))
                .map(area -> convertToLinkDTO(area, surchargeMap.get(area.getSurchargeGuid())))
                .collect(Collectors.groupingBy(SurchargeLinkDTO::getAreaGuid));
    }

    private SurchargeLinkDTO convertToLinkDTO(SurchargeAreaDO area, SurchargeDO surcharge) {
        SurchargeLinkDTO dto = new SurchargeLinkDTO();
        dto.setSurchargeGuid(surcharge.getSurchargeGuid());
        dto.setAreaGuid(area.getAreaGuid());
        dto.setName(surcharge.getName());
        dto.setAmount(surcharge.getAmount());
        dto.setType(surcharge.getType());
        dto.setEffectiveTime(surcharge.getEffectiveTime());
        return dto;
    }
    @Override
    public SurchargeAggDTO sync(SurchargeSyncDTO surchargeDTO) {
        Assert.notNull(surchargeDTO.getStoreGuid(), "门店Guid不得为空");
        List<SurchargeDO> surcharges = list(new LambdaQueryWrapper<SurchargeDO>()
                .eq(SurchargeDO::getStoreGuid, surchargeDTO.getStoreGuid()));
        if (CollectionUtils.isEmpty(surcharges)) {
            return SurchargeAggDTO.empty();
        }
        List<String> collect = surcharges.stream().map(SurchargeDO::getSurchargeGuid).collect(Collectors.toList());
        List<SurchargeAreaDO> areas = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(collect)) {
            areas = surchargeAreaService.list(new LambdaQueryWrapper<SurchargeAreaDO>()
                    .in(SurchargeAreaDO::getSurchargeGuid, collect));
        }
        return SurchargeAggDTO.full(surchargeMapstruct.toRaw(surcharges), surchargeMapstruct.toAreaRaw(areas));
    }

    /**
     * 通过门店和桌台查询附加费信息
     *
     * @param surchargeDTO 入参
     * @return 附加费信息
     */
    @Override
    public List<SurchargeLinkDTO> calculateSurcharge(SurchargeCalculateDTO surchargeDTO) {
        //查询桌台信息
        String areaGuidByTableGuid = tableRpcService.getAreaGuidByTableGuid(surchargeDTO.getTableGuid());
        //查询附加费信息
        Map<String, List<SurchargeLinkDTO>> stringListMap = this.listByAreaGuid(Lists.newArrayList(areaGuidByTableGuid), surchargeDTO.getTradeMode());
        List<SurchargeLinkDTO> surchargeLinkDTOS = stringListMap.get(areaGuidByTableGuid);
        log.info("通过门店和桌台查询附加费信息，surchargeLinkDTOS={}", JacksonUtils.writeValueAsString(surchargeLinkDTOS));
//        BigDecimal appendFee = BigDecimal.ZERO;
//        if (!CollectionUtils.isEmpty(surchargeLinkDTOS)) {
//            for (SurchargeLinkDTO surcharge : surchargeLinkDTOS) {
//                BigDecimal amount = BigDecimal.ZERO;
//                //按人
//                if (surcharge.getType() == 0) {
//                    amount = surcharge.getAmount().multiply(new BigDecimal(surchargeDTO.getGuestCount()));
//                }
//                //按桌
//                if (surcharge.getType() == 1) {
//                    amount = surcharge.getAmount();
//                }
//                appendFee = appendFee.add(amount);
//            }
//        }
        return surchargeLinkDTOS;
    }

    @Override
    public List<SurchargeLinkDTO> listByCondition(SurchargeConditionQuery query) {
        List<SurchargeDO> surchargeDOList = this.list(new LambdaQueryWrapper<SurchargeDO>()
                .eq(SurchargeDO::getIsEnable, BooleanEnum.TRUE.getCode())
                .eq(!StringUtils.isEmpty(query.getStoreGuid()), SurchargeDO::getStoreGuid, query.getStoreGuid())
                .eq(Objects.nonNull(query.getType()), SurchargeDO::getType, query.getType())
                .like(Objects.nonNull(query.getTradeMode()), SurchargeDO::getTradeMode, query.getTradeMode())
        );
        if (CollectionUtils.isEmpty(surchargeDOList)) {
            return Lists.newArrayList();
        }
        if (!StringUtils.isEmpty(query.getAreaGuid())) {
            // 过滤区域
            surchargeDOList = filterSurchargeArea(surchargeDOList, query.getAreaGuid());
        }
        List<SurchargeLinkDTO> respList = new ArrayList<>();
        surchargeDOList.forEach(surcharge -> {
            SurchargeLinkDTO linkDTO = new SurchargeLinkDTO();
            linkDTO.setSurchargeGuid(surcharge.getSurchargeGuid());
            linkDTO.setName(surcharge.getName());
            linkDTO.setAmount(surcharge.getAmount());
            linkDTO.setType(surcharge.getType());
            linkDTO.setTradeMode(surcharge.getTradeMode());
            linkDTO.setEffectiveTime(surcharge.getEffectiveTime());
            respList.add(linkDTO);
        });
        return respList;
    }

    /**
     * 过滤 附加费区域
     */
    private List<SurchargeDO> filterSurchargeArea(List<SurchargeDO> surchargeList, String areaGuid) {
        if (CollectionUtils.isEmpty(surchargeList) || StringUtils.isEmpty(areaGuid)) {
            return surchargeList;
        }
        List<String> surchargeGuidList = surchargeList.stream().map(SurchargeDO::getSurchargeGuid).collect(Collectors.toList());
        List<SurchargeAreaDO> areaSurchargeList = surchargeAreaService.findAreaSurchargeBySurchargeGuids(surchargeGuidList);
        Map<String, List<SurchargeAreaDO>> areaSurchargeBySurchargeGuidMap = areaSurchargeList.stream()
                .collect(Collectors.groupingBy(SurchargeAreaDO::getSurchargeGuid));
        return surchargeList.stream().filter(e -> {
            List<SurchargeAreaDO> surcharges = areaSurchargeBySurchargeGuidMap.get(e.getSurchargeGuid());
            if (Objects.isNull(surcharges)) {
                return false;
            }
            if (surcharges.size() == 1 && surcharges.get(0).getAreaGuid().equals(ALL_AREA_GUID)) {
                return true;
            }
            List<String> areaGuids = surcharges.stream().map(SurchargeAreaDO::getAreaGuid).collect(Collectors.toList());
            return areaGuids.contains(areaGuid);
        }).collect(Collectors.toList());
    }
}
