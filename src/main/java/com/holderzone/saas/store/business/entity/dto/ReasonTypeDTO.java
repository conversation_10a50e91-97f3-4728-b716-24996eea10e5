package com.holderzone.saas.store.business.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReasonTypeDO
 * @date 2019/08/15 14:49
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReasonTypeDTO implements Serializable {

    private static final long serialVersionUID = -4145834403382353878L;

    /**
     * 原因guid
     */
    @ApiModelProperty(value = "原因guid")
    private String reasonTypeGuid;

    /**
     * 原因类型
     */
    @ApiModelProperty(value = "原因类型")
    private String reasonType;


}