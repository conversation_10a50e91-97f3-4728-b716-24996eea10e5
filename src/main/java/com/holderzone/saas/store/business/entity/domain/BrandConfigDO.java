package com.holderzone.saas.store.business.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 品牌配置表实体类
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("hsb_brand_config")
public class BrandConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局唯一主键
     */
    @TableId(value = "guid", type = IdType.INPUT)
    @ApiModelProperty(value = "全局唯一主键")
    private Long guid;

    /**
     * 品牌guid
     */
    @TableField(value = "brand_guid")
    @ApiModelProperty(value = "品牌guid")
    private String brandGuid;

    /**
     * 反结账时效限制
     */
    @TableField(value = "recovery_time_limit")
    @ApiModelProperty(value = "反结账时效限制")
    private Integer recoveryTimeLimit;

    /**
     * 反结账时效限制单位，0：小时 1：天
     */
    @TableField(value = "recovery_time_limit_unit")
    @ApiModelProperty(value = "反结账时效限制单位，0：小时 1：天")
    private Integer recoveryTimeLimitUnit;

    /**
     * 退款时效限制
     */
    @TableField(value = "refund_time_limit")
    @ApiModelProperty(value = "退款时效限制")
    private Integer refundTimeLimit;

    /**
     * 退款时效限制单位，0：小时 1：天
     */
    @TableField(value = "refund_time_limit_unit")
    @ApiModelProperty(value = "退款时效限制单位，0：小时 1：天")
    private Integer refundTimeLimitUnit;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime gmtModified;
}
