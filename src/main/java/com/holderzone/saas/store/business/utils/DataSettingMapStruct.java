package com.holderzone.saas.store.business.utils;

import com.holderzone.saas.store.business.entity.domain.DataSettingDO;
import com.holderzone.saas.store.business.entity.domain.DataSettingQueryDO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingQueryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/8
 * @since 1.8
 */
@Mapper
public interface DataSettingMapStruct {

    DataSettingMapStruct DATA_SETTING_MAP_STRUCT = Mappers.getMapper(DataSettingMapStruct.class);

    DataSettingDTO dataSettingDO2DataSettingDTO(DataSettingDO dataSettingDO);

    List<DataSettingDTO> dataSettingDOs2DataSettingDTOs(List<DataSettingDO> dataSettingDOs);

    DataSettingDO dataSettingDTO2DataSettingDO(DataSettingDTO dataSettingDTO);

    List<DataSettingDO> dataSettingDTOs2DataSettingDOs(List<DataSettingDTO> dataSettingDTOs);

    DataSettingQueryDO dataSettingQueryDTO2DataSettingQueryDO(DataSettingQueryDTO dataSettingQueryDTO);
}
