package com.holderzone.saas.store.business.queue.service.impl;

import com.holderzone.saas.store.business.queue.domain.HolderQueueDO;
import com.holderzone.saas.store.business.queue.helper.DynamicHelper;
import com.holderzone.saas.store.business.queue.mapper.QueueMapper;
import com.holderzone.saas.store.business.queue.mapstruct.QueueItemMapStruct;
import com.holderzone.saas.store.business.queue.mapstruct.QueueMapStruct;
import com.holderzone.saas.store.business.queue.service.QueueConfigService;
import com.holderzone.saas.store.business.queue.service.remote.*;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RedisOrderQueueItemService
 * @date 2019/04/01 18:51
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Service("redisOrderQueueItemService")
@Primary
public class RedisOrderQueueItemService extends QueueItemServiceImpl {
    private RedisTemplate redisTemplate;

    private static final String REDIS_PREFIX = "queue:order:";


    public RedisOrderQueueItemService(QueueMapper queueMapper,
                                      QueueItemMapStruct queueItemMapStruct,
                                      BusinessMsgClient businessMsgClient,
                                      PrintClient printClient,
                                      OrganizationClientService organizationClientService,
                                      TableClientService tableClientService,
                                      WxClient wxClient,
                                      QueueConfigService queueConfigService,
                                      QueueMapStruct queueMapStruct,
                                      RedisTemplate redisTemplate,
                                      DynamicHelper dynamicHelper) {
        super(queueMapper,
                queueItemMapStruct,
                businessMsgClient,
                printClient,
                organizationClientService,
                tableClientService,
                wxClient,
                queueConfigService,
                queueMapStruct,
                dynamicHelper);
        this.redisTemplate = redisTemplate;
        lock = false;
    }

    @Override
    public Integer order(String queueGuid) {
        redisTemplate.opsForValue().setIfAbsent(REDIS_PREFIX + queueGuid, 0);
        return redisTemplate.opsForValue().increment(REDIS_PREFIX + queueGuid, 1).intValue();
    }

    @Override
    public void clean(List<HolderQueueDO> list) {
        List<String> keys = list.stream().map(HolderQueueDO::getGuid).map(e -> REDIS_PREFIX + e).collect(Collectors.toList());
        redisTemplate.delete(keys);
    }
}