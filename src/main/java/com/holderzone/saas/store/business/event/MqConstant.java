package com.holderzone.saas.store.business.event;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className MqConstant
 * @date 19-1-17 下午3:22
 * @description Mq中相关group、topic、tag定义
 * @program holder-saas-store-staff
 */
public interface MqConstant {

    String DOWNSTREAM_CONTEXT = "downstream-context";

    String DOWNSTREAM_STORE_TOPIC = "downstream-store-topic";

    String DOWNSTREAM_STORE_CREATE_TAG = "downstream-store-create-tag";

    String DOWNSTREAM_STORE_INIT_PAYMENT_GROUP = "downstream-store-init-payment-group";
}
