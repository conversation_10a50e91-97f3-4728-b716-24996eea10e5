package com.holderzone.saas.store.business.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("hsb_surcharge_area")
public class SurchargeAreaDO implements Serializable {

    /**
     * 自增id
     */
    @TableId
    private Long id;

    /**
     * 唯一Guid
     */
    private String guid;

    /**
     * 附加费Guid
     */
    private String surchargeGuid;

    /**
     * 区域Guid
     */
    private String areaGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
