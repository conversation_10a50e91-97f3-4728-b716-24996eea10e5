package com.holderzone.saas.store.business.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AdditionalFeeDO
 * @date 2018/08/02 下午3:00
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SurchargeSortedDTO implements Serializable {

    /**
     * 附加费guid
     */
    @NotNull
    @Size(min = 1, max = 45, message = "附加费guid不得为空，且不得超过45个字符")
    @ApiModelProperty(value = "附加费guid", required = true)
    private String additionalFeeGuid;

    /**
     * 附加费排序
     */
    @NotNull
    @Min(value = 0, message = "附加费排序范围[1-16777215]")
    @Max(value = 16777215, message = "附加费排序范围[1-16777215]")
    @ApiModelProperty(value = "附加费排序", required = true)
    private Integer sort;
}
