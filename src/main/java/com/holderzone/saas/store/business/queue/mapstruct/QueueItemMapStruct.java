package com.holderzone.saas.store.business.queue.mapstruct;

import com.holderzone.saas.store.business.queue.domain.HolderQueueItemDO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.queue.*;
import com.holderzone.saas.store.dto.table.OpenTableDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueMapStruct
 * @date 2019/03/27 17:21
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Component
@Mapper(componentModel = "spring")
public interface QueueItemMapStruct {
    HolderQueueItemDO toDo(HolderQueueItemDTO dto);

    HolderQueueItemDTO toDto(HolderQueueItemDO d);

    HolderQueueItemDetailDTO toDetailDto(HolderQueueItemDO d);

    HolderQueueItemDetailPrintDTO toPrintDto(BaseDTO baseDTO);

    OpenTableDTO toTradeDTO(TableConfirmDTO dto);

    TableQueueItemDetailDTO toTableDTO(HolderQueueItemDetailDTO dto);
}