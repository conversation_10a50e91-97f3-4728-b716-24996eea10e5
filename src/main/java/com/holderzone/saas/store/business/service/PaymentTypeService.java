package com.holderzone.saas.store.business.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.journaling.req.PaySerialStatisticsReqDTO;
import com.holderzone.saas.store.dto.trade.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeService
 * @date 2018/08/27 9:22
 * @description //
 * @program holder-saas-store-trading-center
 */
public interface PaymentTypeService {

    void add(PaymentTypeDTO paymentTypeDTO);

    void update(PaymentTypeDTO paymentTypeDTO);

    void delete(String storeGuid, String paymentTypeGuid);

    List<PaymentTypeDTO> getAll(PaymentTypeQueryDTO storeGuid);

    List<PaymentTypeBatchDTO> getAllByStoreGuidList(PaymentTypeBatchQureyDTO paymentTypeBatchQureyDTO);

    String sort(List<PaymentTypeDTO> paymentTypeDTOS);

    String init(String storeGuid, String storeName, String mchntTypeCode);

    String config(JHConfigDTO jhConfigDTO);

    PaymentTypeDTO getOne(PaymentTypeDTO paymentTypeDTO);

    List<PaymentTypeDTO> getAllTypeName(String storeGuid);

    Set<String> getAllStoreNames(List<String> storeGuids);

    List<PaymentTypeDTO> getAllByAndroid(PaymentTypeQueryDTO paymentTypeQueryDTO);

    String unbind(JHReqDTO jhReqDTO);

    Page<PaymentTypeDTO> getMultiStorePayWay(PaySerialStatisticsReqDTO paySerialStatisticsReqDTO);

    /***
     * 初始化门店默认支付方式
     * @param storeGuid 门店Guid
     * @return
     */
    Boolean initPaymentType(String storeGuid);

    /***
     * 获取系统默认支付方式
     * @return 返回参数
     */
    List<PaymentTypeEnumDTO> getPaymentTypeList();

    /**
     * 功能描述：修改支付方式的支付模式
     * @date 2021/5/7
     * @param modeDTO 支付guid和支付模式
     * @return java.lang.Boolean 修改是否成功
     */
    Boolean editPaymentTypeMode(PaymentTypeModeDTO modeDTO);

    /**
     * 聚合支付账户设置
     *
     * @param request 入参
     * @return boolean
     */
    Boolean paymentAccountSave(PaymentAccountReqDTO request);

    /**
     * 获取聚合支付方式信息
     *
     * @param storeGuid 门店guid
     * @return 聚合支付方式信息
     */
    PaymentTypeDTO getJhPaymentTypeInfo(String storeGuid);
}
