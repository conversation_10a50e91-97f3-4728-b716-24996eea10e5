package com.holderzone.saas.store.business.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReasonDTO
 * @date 2019/08/16 11:54
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReasonDTO implements Serializable {
    private static final long serialVersionUID = -4133754759976045871L;
    /**
     * 原因guid
     */
    @ApiModelProperty(value = "原因guid")
    private String reasonGuid;
    /**
     * 商家guid
     */
    @ApiModelProperty(value = "商家guid")
    private String storeGuid;

    /**
     * 原因类型guid
     */
    @ApiModelProperty(value = "原因类型guid")
    private String reasonTypeGuid;

    /**
     * 原因
     */
    @Size(min = 1,max = 30)
    @ApiModelProperty(value = "原因")
    private String reason;
}