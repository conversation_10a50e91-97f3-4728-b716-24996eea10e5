package com.holderzone.saas.store.business.controller;

import com.holderzone.saas.store.business.service.SmsRechargeService;
import com.holderzone.saas.store.dto.business.manage.ChargeDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @className SmsRechargeController
 * @date 18-9-7 上午9:10
 * @description 短信充值相关业务Controller
 * @program holder-saas-store-business
 */
@RestController
@RequestMapping("/smsRecharge")
public class SmsRechargeController {
    @Autowired
    private SmsRechargeService smsRechargeService;

    @PostMapping(value = "/findAllProduct")
    @ApiOperation(value = "查询短信充值云端支持的规格列表", notes = "查询短信充值云端支持的规格列表")
    public List<ChargeDTO> findAllProduct() {
        return smsRechargeService.findAllProduct();
    }

    // 轮询接口，获取用户支付二维码

    // 轮询接口，获取用户支付结果并返回（使用线程池实现，定时任务）
}
