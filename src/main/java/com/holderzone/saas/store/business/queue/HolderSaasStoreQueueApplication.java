package com.holderzone.saas.store.business.queue;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.holderzone.saas.store.business.queue.utils.QueuedUtils;
import com.holderzone.saas.store.business.queue.utils.SpringContextUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableAsync
@EnableSwagger2
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication
@MapperScan("com.holderzone.saas.store.business.queue.mapper")
@Configuration
@EnableApolloConfig
public class HolderSaasStoreQueueApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext app = SpringApplication.run(HolderSaasStoreQueueApplication.class, args);
        SpringContextUtils.getInstance().setCfgContext(app);
        try {
            String a = QueuedUtils.nextRecordGuid();
            System.out.println(a);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
