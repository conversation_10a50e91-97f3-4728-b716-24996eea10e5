package com.holderzone.saas.store.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.business.entity.domain.ScreenPicTimeDO;
import com.holderzone.saas.store.business.entity.domain.ScreenPictureDO;
import com.holderzone.saas.store.dto.business.manage.ScreenPicQuery;
import com.holderzone.saas.store.dto.business.manage.ScreenPicTimeDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPictureMapper
 * @date 2018/09/06 17:39
 * @description
 * @program holder-saas-store-business
 */
@Mapper
@Repository
public interface ScreenPictureMapper extends BaseMapper<ScreenPictureDO> {

    void insertOrUpdate(ScreenPictureDO screenPictureDO);

    List<ScreenPictureDO> queryByAndroid(@Param("storeGuid") String storeGuid);

    int countPics(@Param("storeGuid") String storeGuid, @Param("picType") Integer picType);

    void delete(@Param("screenPictureGuid") String screenPictureGuid);

    ScreenPictureDO queryOneByGuid(String screenPicGuid);

    String getOssUrl(String screenPictureGuid);

    List<ScreenPictureDO> queryByWeb(ScreenPicQuery screenPicQuery);

    void insertPicTime(ScreenPicTimeDO screenPicTimeDO);

    void updatePicTime(ScreenPicTimeDO screenPicTimeDO);

    List<ScreenPicTimeDO> queryPicTime(ScreenPicTimeDTO screenPicTimeDTO);
}
