package com.holderzone.saas.store.business.mapstruct;

import com.holderzone.saas.store.business.entity.domain.ReasonDO;
import com.holderzone.saas.store.dto.business.reason.ReasonDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReasonMapstruct
 * @date 2019/09/26 9:57
 * @description //TODO
 * @program holder-saas-aggregation-merchant
 */
@Mapper
public interface ReasonMapstruct {

    ReasonMapstruct INSTANCE = Mappers.getMapper(ReasonMapstruct.class);

    List<ReasonDTO> reasonDo2ReasonDto(List<ReasonDO> reasonDO);

    ReasonDO reasonDto2ReasonDo(ReasonDTO reasonDTO);
}