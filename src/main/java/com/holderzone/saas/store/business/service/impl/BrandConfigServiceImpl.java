package com.holderzone.saas.store.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.business.entity.domain.BrandConfigDO;
import com.holderzone.saas.store.business.enums.TimeLimitUnitEnum;
import com.holderzone.saas.store.business.mapper.BrandConfigMapper;
import com.holderzone.saas.store.business.service.BrandConfigService;
import com.holderzone.saas.store.business.service.RedisService;
import com.holderzone.saas.store.business.service.client.OrgFeignClient;
import com.holderzone.saas.store.business.utils.PageAdapter;
import com.holderzone.saas.store.dto.business.brand.BrandConfigDTO;
import com.holderzone.saas.store.dto.business.brand.BrandConfigQueryDTO;
import com.holderzone.saas.store.dto.business.brand.BrandConfigSaveOrUpdateDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 品牌配置Service实现类
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@Service
public class BrandConfigServiceImpl extends ServiceImpl<BrandConfigMapper, BrandConfigDO> implements BrandConfigService {

    private final RedisService redisService;

    private final OrgFeignClient orgFeignClient;

    @Autowired
    public BrandConfigServiceImpl(RedisService redisService, OrgFeignClient orgFeignClient) {
        this.redisService = redisService;
        this.orgFeignClient = orgFeignClient;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdateBrandConfig(BrandConfigSaveOrUpdateDTO saveOrUpdateDTO) {

        // 校验品牌是否存在
        checkBrand(saveOrUpdateDTO.getBrandGuid());

        // 校验时间限制是否合法
        checkTimeLimit(saveOrUpdateDTO);

        BrandConfigDO existConfig = null;

        // 如果传入了guid，先根据guid查询
        if (saveOrUpdateDTO.getGuid() != null) {
            existConfig = getById(saveOrUpdateDTO.getGuid());
        }

        // 如果根据guid没找到，再根据brandGuid查询
        if (existConfig == null) {
            existConfig = getOne(new LambdaQueryWrapper<BrandConfigDO>()
                    .eq(BrandConfigDO::getBrandGuid, saveOrUpdateDTO.getBrandGuid()));
        }

        if (existConfig != null) {
            // 更新现有配置
            updateBrandConfigFields(existConfig, saveOrUpdateDTO);
            existConfig.setGmtModified(LocalDateTime.now());
            updateById(existConfig);
            log.info("品牌配置更新成功，ID：{}", existConfig.getGuid());
            return existConfig.getGuid();
        } else {
            // 创建新配置
            BrandConfigDO brandConfigDO = new BrandConfigDO();

            // 生成主键（如果没有传入guid）
            Long guid = saveOrUpdateDTO.getGuid();
            if (guid == null) {
                guid = redisService.rawId("hsb_brand_config");
            }
            brandConfigDO.setGuid(guid);
            brandConfigDO.setBrandGuid(saveOrUpdateDTO.getBrandGuid());
            brandConfigDO.setGmtCreate(LocalDateTime.now());
            brandConfigDO.setGmtModified(LocalDateTime.now());

            // 设置时效限制字段（包括null值）
            updateBrandConfigFields(brandConfigDO, saveOrUpdateDTO);

            // 保存到数据库
            save(brandConfigDO);
            log.info("品牌配置创建成功，ID：{}", guid);
            return guid;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBrandConfig(Long guid) {
        // 检查记录是否存在
        BrandConfigDO existConfig = getById(guid);
        if (existConfig == null) {
            throw new BusinessException("品牌配置不存在");
        }

        // 删除记录
        removeById(guid);

        log.info("品牌配置删除成功，ID：{}", guid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BrandConfigDTO getBrandConfigById(Long guid) {
        log.info("根据ID查询品牌配置，ID：{}", guid);

        BrandConfigDO brandConfigDO = getById(guid);
        if (brandConfigDO == null) {
            return null;
        }

        return convertToDTO(brandConfigDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BrandConfigDTO getBrandConfigByBrandGuid(String brandGuid) {

        checkBrand(brandGuid);

        BrandConfigDO brandConfigDO = getOne(new LambdaQueryWrapper<BrandConfigDO>()
                .eq(BrandConfigDO::getBrandGuid, brandGuid));
        if (brandConfigDO == null) {
            log.info("品牌配置不存在，brandGuid：{}，创建默认配置记录", brandGuid);
            // 创建一个默认配置记录
            BrandConfigDO defaultBrandConfigDO = createDefaultBrandConfig(brandGuid);
            save(defaultBrandConfigDO);
            log.info("默认配置记录创建成功，brandGuid：{}，ID：{}", brandGuid, defaultBrandConfigDO.getGuid());
            brandConfigDO = defaultBrandConfigDO;
        }

        return convertToDTO(brandConfigDO);
    }

    @Override
    public Page<BrandConfigDTO> listBrandConfigByPage(BrandConfigQueryDTO queryDTO) {
        // 构建查询条件
        LambdaQueryWrapper<BrandConfigDO> queryWrapper = buildQueryWrapper(queryDTO);

        // 分页查询
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<BrandConfigDO> page =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(queryDTO.getCurrentPage(), queryDTO.getPageSize());
        page(page, queryWrapper);

        // 转换结果
        List<BrandConfigDTO> records = page.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        return new PageAdapter<>(page, records);
    }

    @Override
    public List<BrandConfigDTO> listBrandConfig(BrandConfigQueryDTO queryDTO) {
        // 构建查询条件
        LambdaQueryWrapper<BrandConfigDO> queryWrapper = buildQueryWrapper(queryDTO);

        // 查询列表
        List<BrandConfigDO> list = list(queryWrapper);

        // 转换结果
        return list.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public BrandConfigDTO getBrandConfigByStoreGuid(String storeGuid) {
        String brandGuid;
        if (StringUtils.isBlank(storeGuid)) {
            throw new BusinessException("门店Guid不能为空");
        } else {
            StoreDTO storeDTO = orgFeignClient.queryStoreByGuid(storeGuid);
            if (Objects.isNull(storeDTO) || StringUtils.isBlank(storeDTO.getBelongBrandGuid())) {
                throw new BusinessException("门店或品牌不存在");
            }
            brandGuid = storeDTO.getBelongBrandGuid();
        }
        return getBrandConfigByBrandGuid(brandGuid);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<BrandConfigDO> buildQueryWrapper(BrandConfigQueryDTO queryDTO) {
        LambdaQueryWrapper<BrandConfigDO> queryWrapper = new LambdaQueryWrapper<>();

        if (queryDTO.getGuid() != null) {
            queryWrapper.eq(BrandConfigDO::getGuid, queryDTO.getGuid());
        }

        if (StringUtils.isNotBlank(queryDTO.getBrandGuid())) {
            queryWrapper.eq(BrandConfigDO::getBrandGuid, queryDTO.getBrandGuid());
        }

        queryWrapper.orderByDesc(BrandConfigDO::getGmtCreate);

        return queryWrapper;
    }

    /**
     * 更新品牌配置字段（包括null值处理）
     */
    private void updateBrandConfigFields(BrandConfigDO target, BrandConfigSaveOrUpdateDTO source) {
        // 直接设置字段值，包括null值
        target.setRecoveryTimeLimit(source.getRecoveryTimeLimit());
        target.setRecoveryTimeLimitUnit(source.getRecoveryTimeLimitUnit());
        target.setRefundTimeLimit(source.getRefundTimeLimit());
        target.setRefundTimeLimitUnit(source.getRefundTimeLimitUnit());
    }

    /**
     * 转换为DTO
     */
    private BrandConfigDTO convertToDTO(BrandConfigDO brandConfigDO) {
        BrandConfigDTO dto = new BrandConfigDTO();
        BeanUtils.copyProperties(brandConfigDO, dto);
        return dto;
    }

    private void checkBrand(String brandGuid) {
        if (StringUtils.isBlank(brandGuid)) {
            throw new BusinessException("品牌Guid不能为空");
        }
        List<BrandDTO> brandDTOS = orgFeignClient.queryBrandByIdList(Collections.singletonList(brandGuid));
        if (CollectionUtils.isEmpty(brandDTOS)) {
            throw new BusinessException("品牌不存在");
        }
    }

    private void checkTimeLimit(BrandConfigSaveOrUpdateDTO saveOrUpdateDTO) {
        if (saveOrUpdateDTO.getRecoveryTimeLimitUnit() != null) {
            TimeLimitUnitEnum.isValidCode(saveOrUpdateDTO.getRecoveryTimeLimitUnit());
        }
        if (saveOrUpdateDTO.getRefundTimeLimitUnit() != null) {
            TimeLimitUnitEnum.isValidCode(saveOrUpdateDTO.getRefundTimeLimitUnit());
        }
    }

    private BrandConfigDO createDefaultBrandConfig(String brandGuid) {
        BrandConfigDO brandConfigDO = new BrandConfigDO();
        Long guid = redisService.rawId("hsb_brand_config");
        brandConfigDO.setGuid(guid);
        brandConfigDO.setBrandGuid(brandGuid);
        brandConfigDO.setGmtCreate(LocalDateTime.now());
        brandConfigDO.setGmtModified(LocalDateTime.now());
        // 默认设置为24小时
        brandConfigDO.setRefundTimeLimit(24);
        brandConfigDO.setRefundTimeLimitUnit(TimeLimitUnitEnum.HOUR.getCode());
        brandConfigDO.setRecoveryTimeLimit(24);
        brandConfigDO.setRecoveryTimeLimitUnit(TimeLimitUnitEnum.HOUR.getCode());
        return brandConfigDO;
    }
}
