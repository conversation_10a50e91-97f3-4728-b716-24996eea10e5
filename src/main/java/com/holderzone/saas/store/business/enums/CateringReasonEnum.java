package com.holderzone.saas.store.business.enums;

import com.holderzone.saas.store.dto.business.reason.ReasonDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CateringReasonEnum
 * @date 2019/12/19 16:08
 * @description
 * @program holder-saas-store
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CateringReasonEnum {
    // 反结账原因
    WRONG_SETTLE(ReasonTypeEnum.REVERSECHECKOUT.getCode(), "结错账"),
    UN_RETURN_LAST_ITEMS(ReasonTypeEnum.REVERSECHECKOUT.getCode(), "剩余菜品未退"),
    CHANGE_PAY_TYPE(ReasonTypeEnum.REVERSECHECKOUT.getCode(), "修改结账方式"),

    // 作废订单原因
    CUSTOMER_DEPARTURE(ReasonTypeEnum.CANCELORDER.getCode(), "顾客离店"),
    ORDER_DUPLICATION(ReasonTypeEnum.CANCELORDER.getCode(), "订单重复"),
    OTHER_CANCEL(ReasonTypeEnum.CANCELORDER.getCode(), "其他"),

    // 取消预定原因
    TEMPORARY_CANCELLATION(ReasonTypeEnum.CANCELRESERVATION.getCode(), "客户临时取消"),
    NO_TABLE(ReasonTypeEnum.CANCELRESERVATION.getCode(), "已无桌"),
    NOT_ARRIVE(ReasonTypeEnum.CANCELRESERVATION.getCode(), "顾客没来"),

    // 外卖拒单原因
    SALE_OUT_REFUSE(ReasonTypeEnum.REFUSEORDER.getCode(), "商品售罄"),
    CUSTOMER_REFUND_REFUSE(ReasonTypeEnum.REFUSEORDER.getCode(), "顾客退单"),
    UNABLE_TO_DELIVER_REFUSE(ReasonTypeEnum.REFUSEORDER.getCode(), "无法配送"),

    // 外卖退单原因
    SALE_OUT_RETURN(ReasonTypeEnum.RETURNORDER.getCode(), "商品售罄"),
    CUSTOMER_REFUND_RETURN(ReasonTypeEnum.RETURNORDER.getCode(), "顾客退单"),
    UNABLE_TO_DELIVER_RETURN(ReasonTypeEnum.RETURNORDER.getCode(), "无法配送"),

    // 外卖拒绝退款原因
    ALREADY_SEND_OUT(ReasonTypeEnum.REFUSEREFUND.getCode(), "商品已送出"),
    ALREADY_REFUND(ReasonTypeEnum.REFUSEREFUND.getCode(), "金额已退还"),

    // 退菜原因
    WRONG_ORDER(ReasonTypeEnum.RETURNDISH.getCode(), "点错了"),
    TOO_SLOW(ReasonTypeEnum.RETURNDISH.getCode(), "上菜慢"),
    SALE_OUT(ReasonTypeEnum.RETURNDISH.getCode(), "商品售罄"),

    // 赠菜原因
    EVENT_PRESENTATION(ReasonTypeEnum.GIVINGDISH.getCode(), "活动赠送"),
    CONSUMPTION_PRESENTATION(ReasonTypeEnum.GIVINGDISH.getCode(), "消费赠送"),
    GIFT_FROM_BOSS(ReasonTypeEnum.GIVINGDISH.getCode(), "老板赠送");

    private Integer code;

    private String desc;

    public static List<ReasonDTO> getDefaultReason() {
        return Arrays.stream(CateringReasonEnum.values()).map(r -> {
            ReasonDTO reasonDTO = new ReasonDTO();
            reasonDTO.setReason(r.getDesc());
            reasonDTO.setReasonTypeCode(r.getCode());
            return reasonDTO;
        }).collect(Collectors.toList());
    }
}
