package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.NotFoundException;

public class AdditionalFeeTypeNotFoundException extends NotFoundException {

    private static final long serialVersionUID = -6778502079623616324L;

    public AdditionalFeeTypeNotFoundException() {
        super("该附加费不存在");
    }

    public AdditionalFeeTypeNotFoundException(String message) {
        super("该附加费类型[" + message + "]不存在");

    }

    public AdditionalFeeTypeNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
