package com.holderzone.saas.store.business.service.client;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.resource.common.dto.extension.BaseDictionaryDTO;
import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className Enterprise ClientService
 * @date 2018/09/13 20:23
 * @description
 * @program holder-saas-store-trading-center
 */
@Component
@FeignClient(name = "holder-saas-cloud-enterprise", fallbackFactory = EnterpriseClientService.EnterClientFallBack.class)
public interface EnterpriseClientService {

    @PostMapping("/enterprise/find")
    EnterpriseDTO findEnterprise(@RequestBody EnterpriseQueryDTO enterpriseQueryDTO);

    @PostMapping("enterprise/app/info")
    Boolean notify(@RequestBody PaymentInfoDTO paymentInfoDTO);

    @GetMapping("enterprise/app/{enterpriseGuid}/{storeGuid}")
    PaymentInfoDTO getJHInfo(@PathVariable("enterpriseGuid") String enterpriseGuid, @PathVariable("storeGuid") String storeGuid);

    @PutMapping("enterprise/app/update")
    Boolean update(@RequestBody PaymentInfoDTO paymentInfoDTO);

    @PutMapping("enterprise/app/unbind")
    Boolean unbind(@RequestBody PaymentInfoDTO paymentInfoDTO);

    /**
     * 根据企业guid查询企业经营类型
     *
     * @param enterpriseGuid 企业guid
     * @return 数据字典实体
     */
    @ApiOperation("根据企业guid查询企业经营类型")
    @GetMapping("/enterprise/management_type/{enterpriseGuid}")
    BaseDictionaryDTO queryManagementType(@PathVariable(value = "enterpriseGuid") String enterpriseGuid);

    /**
     * 查询商户的所有账户信息
     *
     * @param enterpriseGuid 企业guid
     * @param storeGuid      门店guid
     * @return 商户的所有账户信息
     */
    @ApiOperation(value = "查询商户的所有账户信息")
    @GetMapping("enterprise/app/list_payment_info")
    List<PaymentInfoDTO> listPaymentInfo(@RequestParam("enterpriseGuid") String enterpriseGuid,
                                         @RequestParam("storeGuid") String storeGuid);

    /**
     * 批量设置聚合支付账户信息
     *
     * @param paymentInfoDTOList 聚合支付账户信息列表
     * @return 成功/失败
     */
    @ApiOperation(value = "批量设置聚合支付账户信息")
    @PostMapping("enterprise/app/batch_save_payment_info")
    void batchSaveOrUpdatePaymentInfoList(@RequestBody @Validated List<PaymentInfoDTO> paymentInfoDTOList);

    /**
     * 查询聚合支付账户名称是否存在
     *
     * @param enterpriseGuid 企业guid
     * @param storeGuid      门店guid
     * @param accountName    账户名称
     * @return 是否
     */
    @ApiOperation(value = "查询聚合支付账户名称是否存在")
    @GetMapping("enterprise/app/exist_same_account_name")
    List<PaymentInfoDTO> existSameAccountName(@RequestParam("enterpriseGuid") String enterpriseGuid,
                                              @RequestParam("storeGuid") String storeGuid,
                                              @RequestParam("accountName") String accountName);

    /**
     * 根据支付信息guid删除聚合支付信息
     * 逻辑删除
     *
     * @param toDeleteList 支付信息guid
     */
    @ApiOperation(value = "根据支付信息guid删除聚合支付信息")
    @GetMapping("enterprise/app/delete_pay_info_by_guid_list")
    void deletePayInfoByGuidList(@RequestParam("toDeleteList") List<String> toDeleteList);

    @Component
    class EnterClientFallBack implements FallbackFactory<EnterpriseClientService> {

        private static final Logger logger = LoggerFactory.getLogger(EnterClientFallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public EnterpriseClientService create(Throwable throwable) {

            return new EnterpriseClientService() {
                @Override
                public EnterpriseDTO findEnterprise(EnterpriseQueryDTO enterpriseQueryDTO) {
                    logger.error(HYSTRIX_PATTERN, "findEnterprise", enterpriseQueryDTO, throwable.getMessage());
                    throw new BusinessException("查询企业信息失败");
                }

                @Override
                public Boolean notify(PaymentInfoDTO paymentInfoDTO) {
                    logger.error("通知云端配置聚合支付appId and appSerectKey 失败 paymentInfoDTO={}", JacksonUtils.writeValueAsString(paymentInfoDTO));
                    return false;
                }

                @Override
                public PaymentInfoDTO getJHInfo(String enterpriseGuid, String storeGuid) {
                    logger.error("查询云端配置聚合支付appId and appSerectKey 失败 enterpriseGuid={} storeGuid={} e={}", enterpriseGuid, storeGuid, throwable.getMessage());
//                    throw new BusinessException("查询云端配置聚合支付appId and appSerectKey 失败");
                    return null;
                }

                @Override
                public Boolean update(PaymentInfoDTO paymentInfoDTO) {
                    logger.error("通知云端更新配置失败 paymentInfoDTO={}", JacksonUtils.writeValueAsString(paymentInfoDTO));
                    return false;
                }

                @Override
                public Boolean unbind(PaymentInfoDTO paymentInfoDTO) {
                    logger.error("解绑失败 paymentInfoDTO={}", JacksonUtils.writeValueAsString(paymentInfoDTO));
                    return false;
                }

                @Override
                public BaseDictionaryDTO queryManagementType(String enterpriseGuid) {
                    logger.error(HYSTRIX_PATTERN, "queryManagementType", enterpriseGuid, ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public List<PaymentInfoDTO> listPaymentInfo(String enterpriseGuid, String storeGuid) {
                    logger.error("查询商户的所有账户信息失败 enterpriseGuid={} storeGuid={} e={}", enterpriseGuid, storeGuid,
                            throwable.getMessage());
                    return new ArrayList<>();
                }

                @Override
                public void batchSaveOrUpdatePaymentInfoList(List<PaymentInfoDTO> paymentInfoDTOList) {
                    logger.error("批量设置聚合支付账户信息失败 paymentInfoDTOList={} e={}", JacksonUtils.writeValueAsString(paymentInfoDTOList),
                            throwable.getMessage());
                    throw new BusinessException("设置聚合支付账户信息失败");
                }

                @Override
                public List<PaymentInfoDTO> existSameAccountName(String enterpriseGuid, String storeGuid, String accountName) {
                    logger.error("查询聚合支付账户名称是否存在失败 enterpriseGuid={} storeGuid={} accountName={} e={}",
                            enterpriseGuid, storeGuid, accountName, throwable.getMessage());
                    throw new BusinessException("查询聚合支付账户名称是否存在失败");
                }

                @Override
                public void deletePayInfoByGuidList(List<String> toDeleteList) {
                    logger.error(HYSTRIX_PATTERN, "deletePayInfoByGuidList", toDeleteList, throwable.getMessage());
                    throw new BusinessException("根据支付信息guid删除聚合支付信息失败");
                }
            };
        }
    }
}
