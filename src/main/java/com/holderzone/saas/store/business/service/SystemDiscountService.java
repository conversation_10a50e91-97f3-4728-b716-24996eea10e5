package com.holderzone.saas.store.business.service;

import com.holderzone.saas.store.business.entity.domain.SystemDiscountDO;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemDiscountService
 * @date 2018/08/15 18:09
 * @description //
 * @program holder-saas-store-trading-center
 */
public interface SystemDiscountService {

    /**
     * 新增省零规则配置
     *
     * @param systemDiscountDTO
     */
    void addSystemDiscount(SystemDiscountDTO systemDiscountDTO);

    /**
     * 查询所有省零配置
     *
     * @return
     */
    List<SystemDiscountDTO> queryAllSystemDis(String storeGuid);

    /**
     * 更新
     *
     * @param systemDiscountDTO
     */
    void update(SystemDiscountDTO systemDiscountDTO);

    SystemDiscountDTO getByStoreGuid(String storeGuid);

    SystemDiscountDTO saveOrUpdate(SystemDiscountDTO systemDiscountDTO);

    void delete(String storeGuid, String systemDiscountGuid);

    List<SystemDiscountDO> queryAllSystemDOS(String billGuid);
}
