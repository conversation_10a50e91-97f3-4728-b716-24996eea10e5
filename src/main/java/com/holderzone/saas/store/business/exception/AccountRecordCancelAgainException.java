package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AccountRecordCancelAgainException extends BusinessException {

    private static final long serialVersionUID = -6170068844134647394L;

    public AccountRecordCancelAgainException() {
        super("营业日当前未扎帐，无需撤销扎帐");
    }

    public AccountRecordCancelAgainException(String message) {
        super("营业日["+message+"]当前未扎帐，无需撤销扎帐");
    }

    public AccountRecordCancelAgainException(String message, Throwable cause) {
        super(message, cause);
    }
}
