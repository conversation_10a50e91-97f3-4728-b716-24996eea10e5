package com.holderzone.saas.store.business.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 扎帐信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("hsb_bindup_accounts")
public class BindupAccountsDo {

    private long id;

    /**
     * 扎帐时间
     */
    @ApiModelProperty(value = "扎帐时间")
    @TableField(value = "bindup_accounts")
    private LocalDateTime bindupAccounts;


    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    @TableField(value = "store_guid")
    private String storeGuid;

    /**
     * 用户guid
     */
    @ApiModelProperty(value = "用户id")
    @TableField(value = "user_guid")
    private String userGuid;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    @TableField(value = "user_name")
    private String userName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "gmt_create")
    private LocalDateTime gmtCreate;

}
