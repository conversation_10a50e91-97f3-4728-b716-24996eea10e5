package com.holderzone.saas.store.business.service.impl;

import com.holderzone.saas.store.business.entity.domain.EstimateDishDO;
import com.holderzone.saas.store.business.entity.domain.EstimateRecordDO;
import com.holderzone.saas.store.business.entity.dto.EstimateDishDTO;
import com.holderzone.saas.store.business.entity.dto.EstimateRecordCreateDTO;
import com.holderzone.saas.store.business.entity.dto.EstimateRecordDTO;
import com.holderzone.saas.store.business.entity.dto.EstimateRecordQuerryDTO;
import com.holderzone.saas.store.business.mapper.EstimateMapper;
import com.holderzone.saas.store.business.mapstruct.EstimateMapstruct;
import com.holderzone.saas.store.business.service.EstimateService;
import com.holderzone.saas.store.business.utils.GuidUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateServiceImpl
 * @date 2018/08/06 上午11:56
 * @description //TODO
 * @program holder-saas-store-business
 */
@Service
public class EstimateServiceImpl implements EstimateService {

    private final EstimateMapper estimateMapper;

    @Autowired
    public EstimateServiceImpl(EstimateMapper estimateMapper) {
        this.estimateMapper = estimateMapper;
    }

    @Override
    public void createOrReplace(EstimateRecordCreateDTO estimateRecordCreateDTO) {
        EstimateRecordDO estimateRecordDO = EstimateMapstruct.INSTANCE.toEstimateRecordDO(estimateRecordCreateDTO);
        List<EstimateDishDO> estimateDishDOS = EstimateMapstruct.INSTANCE.toEstimateDishDOS(estimateRecordCreateDTO.getEstimateDishes());
        EstimateRecordDO estimateRecordInSql = estimateMapper.findByBusinessDay(estimateRecordDO);
        String estimateRecordGuid;
        if (estimateRecordInSql != null) {
            estimateRecordGuid = estimateRecordInSql.getEstimateRecordGuid();
            estimateMapper.batchDeleteDishes(estimateRecordGuid);
        } else {
            estimateRecordGuid = GuidUtils.nextEstimateRecordGuid();
            estimateRecordDO.setEstimateRecordGuid(estimateRecordGuid);
            estimateMapper.insertRecord(estimateRecordDO);
        }
        estimateDishDOS.forEach(estimateDishDO -> estimateDishDO.setEstimateRecordGuid(estimateRecordGuid));
        estimateMapper.batchInsertDishes(estimateDishDOS);
    }

    @Override
    public EstimateRecordDTO getByBusinessDay(EstimateRecordQuerryDTO estimateRecordQuerryDTO) {
        EstimateRecordDO estimateRecordDO = EstimateMapstruct.INSTANCE.toEstimateRecordDO(estimateRecordQuerryDTO);
        EstimateRecordDO estimateRecordInSql = estimateMapper.findByBusinessDay(estimateRecordDO);
        if (null == estimateRecordInSql) {
            return EstimateRecordDTO.empty();
        }
        EstimateRecordDTO estimateRecordDTO = EstimateMapstruct.INSTANCE.toEstimateRecordDTO(estimateRecordInSql);
        List<EstimateDishDO> estimateDishDOS = estimateMapper.queryDishesByRecordGuid(estimateRecordInSql.getEstimateRecordGuid());
        if (estimateDishDOS.isEmpty()) {
            return estimateRecordDTO.emptyDishes();
        }
        List<EstimateDishDTO> estimateDishDTOS = EstimateMapstruct.INSTANCE.toEstimateDishDTO(estimateDishDOS);
        return estimateRecordDTO.dishes(estimateDishDTOS);
    }
}
