package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AdditionalFeeNameDuplicatedException extends BusinessException {

    private static final long serialVersionUID = -8016792094248213987L;

    public AdditionalFeeNameDuplicatedException() {
        super("已存在相同名称附加费");
    }

    public AdditionalFeeNameDuplicatedException(String message) {
        super("已存在相同名称附加费["+message+"]");
    }

    public AdditionalFeeNameDuplicatedException(String message, Throwable cause) {
        super(message, cause);
    }
}
