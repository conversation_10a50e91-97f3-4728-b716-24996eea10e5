package com.holderzone.saas.store.business.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.business.service.impl.ReasonServiceImpl;
import com.holderzone.saas.store.dto.business.reason.ReasonCopyReqDTO;
import com.holderzone.saas.store.dto.business.reason.ReasonDTO;
import com.holderzone.saas.store.dto.business.reason.ReasonTypeDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReasonManagerController
 * @date 2019/08/15 14:17
 * @description //TODO
 * @program holder-saas-store-business
 */
@Slf4j
@Api("原因管理业务相关接口")
@RestController
@RequestMapping("/reason")
public class ReasonManagerController {

    private static final Logger logger = LoggerFactory.getLogger(ReasonManagerController.class);
    private final ReasonServiceImpl reasonService;

    @Autowired
    public ReasonManagerController(ReasonServiceImpl reasonService) {
        this.reasonService = reasonService;
    }

    @PostMapping(value = "/findReason")
    @ApiOperation(value = "查询'原因列表',传入商家guid,原因类别guid", notes = "查询'原因列表',传入商家guid,类别guid")
    public List<ReasonDTO> findReason(@RequestBody ReasonDTO reasonDTO) {
        logger.info("查询'原因列表': reasonDTO={}", JacksonUtils.writeValueAsString(reasonDTO));
        return reasonService.findReason(reasonDTO);
    }

    @PostMapping(value = "/insertReason")
    @ApiOperation(value = "新增'原因列表',传入商家guid,类别guid,原因内容", notes = "新增'原因列表',传入商家guid,类别guid,原因内容")
    public void insertReason(@RequestBody @Validated ReasonDTO reasonDTO) {
        logger.info("新增'原因列表': reasonDTO={}", JacksonUtils.writeValueAsString(reasonDTO));
        reasonService.insertReason(reasonDTO);
    }

    @PostMapping(value = "/updateReason")
    @ApiOperation(value = "修改'原因列表',传入'原因列表'的guid,修改的原因内容", notes = "修改'原因列表',传入'原因列表'的guid,修改的原因内容")
    public void updateReason(@RequestBody @Validated ReasonDTO reasonDTO) {
        logger.info("修改'原因列表': reasonDTO={}", JacksonUtils.writeValueAsString(reasonDTO));
        reasonService.updateReason(reasonDTO);
    }

    @PostMapping(value = "/deleteReason")
    @ApiOperation(value = "删除'原因列表',传入'原因列表'的guid", notes = "删除'原因列表',传入'原因列表'的guid")
    public void deleteReason(@RequestBody @Validated ReasonDTO reasonDTO) {
        logger.info("删除'原因列表': reasonDTO={}", JacksonUtils.writeValueAsString(reasonDTO));
        reasonService.deleteReason(reasonDTO);
    }


    @PostMapping(value = "/findReasonType")
    @ApiOperation(value = "餐饮版查询原因类型", notes = "查询原因类型")
    public List<ReasonTypeDTO> findReasonType() {
        return reasonService.findReasonType();
    }


    @PostMapping(value = "/updateCount")
    @ApiOperation(value = "原因使用次数统计,传入'原因列表'的guid集合", notes = "原因使用次数统计,传入'原因列表'的guid集合")
    public void updateReason(@RequestBody List<String> guids) {
        logger.info("修改'原因列统计数量': guids={}", JacksonUtils.writeValueAsString(guids));
        reasonService.updateCount(guids);
    }

    @PostMapping(value = "/findReasonTypeSuperMarket")
    @ApiOperation(value = "商超版查询原因类型", notes = "查询原因类型")
    public List<ReasonTypeDTO> findReasonTypeSuperMarket() {
        return reasonService.findReasonTypeSuperMarket();
    }

    @PostMapping(value = "/copy_reason")
    @ApiOperation(value = "复制'原因列表',传入原因实体（类别code,原因内容）,门店列表")
    public Boolean copyReason(@RequestBody @Validated ReasonCopyReqDTO copyReqDTO) {
        log.info("复制'原因列表': reasonDTO={}", JacksonUtils.writeValueAsString(copyReqDTO));
        return reasonService.copyReason(copyReqDTO);
    }
}