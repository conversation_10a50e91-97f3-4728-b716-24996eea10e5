package com.holderzone.saas.store.business.utils;

import com.holderzone.framework.exception.unchecked.BusinessException;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.ConfigurableApplicationContext;

import java.lang.annotation.Annotation;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2023/10/18
 * @description
 */
public class SpringContextUtil {

    private ConfigurableApplicationContext cfgContext;

    /**
     * 实体对象
     */
    private static final SpringContextUtil INSTANCE = new SpringContextUtil();

    private SpringContextUtil() {
        throw new BusinessException("error");
    }

    public static SpringContextUtil getInstance() {
        return INSTANCE;
    }

    /**
     * 防止序列化产生对象
     *
     * @return 防止序列化
     */
    private Object readResolve() {
        return INSTANCE;
    }

    /**
     * 根据名称获取Bean
     *
     * @param name
     * @return
     */
    @SuppressWarnings("unchecked")
    public <T> T getBean(String name) {
        return (T) cfgContext.getBean(name);
    }

    /**
     * 获取bean的名字
     *
     * @param clazz 类型
     * @return bean名字
     */
    @SuppressWarnings("rawtypes")
    public String getBeanName(Class<T> clazz) {
        return cfgContext.getBeanNamesForType(clazz)[0];
    }

    /**
     * 根据类获取Bean
     *
     * @param clazz
     * @return
     */
    public <T> T getBean(Class<T> clazz) {
        return cfgContext.getBean(clazz);
    }


    /**
     * 动态注册一个Bean动Spring容器中
     *
     * @param beanName  名称
     * @param beanClazz 定义bean
     */
    @SuppressWarnings("rawtypes")
    public void registerBean(String beanName, Class<T> beanClazz, Map<String, Object> propertys) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(beanClazz);
        if (propertys != null) {
            propertys.forEach(builder::addPropertyValue);
        }
        builder.setScope(BeanDefinition.SCOPE_SINGLETON);
        registerBean(beanName, builder.getBeanDefinition());

    }

    /**
     * 动态注册一个Bean动Spring容器中
     *
     * @param beanName
     * @param obj
     */
    public void registerBean(String beanName, Object obj) {
        cfgContext.getBeanFactory().registerSingleton(beanName, obj);
    }

    /**
     * 动态注册一个Bean动Spring容器中
     *
     * @param beanName
     * @param beanDefinition
     */
    public void registerBean(String beanName, BeanDefinition beanDefinition) {
        BeanDefinitionRegistry beanDefinitionRegistry = (BeanDefinitionRegistry) cfgContext.getBeanFactory();
        beanDefinitionRegistry.registerBeanDefinition(beanName, beanDefinition);
    }

    /**
     * 根据枚举类型获取Spring注册的Bean
     *
     * @param annotationType 枚举
     * @return
     */
    public Map<String, Object> getBeanWithAnnotation(Class<? extends Annotation> annotationType) {
        return cfgContext.getBeansWithAnnotation(annotationType);
    }

}
