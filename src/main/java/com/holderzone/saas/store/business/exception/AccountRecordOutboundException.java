package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AccountRecordOutboundException extends BusinessException {

    private static final long serialVersionUID = -419902162100139816L;

    public AccountRecordOutboundException() {
        super("营业日已出库，不能撤销");
    }

    public AccountRecordOutboundException(String message) {
        super("营业日["+message+"]已出库，不能撤销");
    }

    public AccountRecordOutboundException(String message, Throwable cause) {
        super(message, cause);
    }
}
