package com.holderzone.saas.store.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.business.entity.domain.SurchargeDO;
import com.holderzone.saas.store.business.entity.query.SurchargeCountQuery;
import com.holderzone.saas.store.dto.business.manage.SurchargeListDTO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AdditionalFeeMapper
 * @date 2018/08/02 下午3:48
 * @description //TODO
 * @program holder-saas-store-business
 */
@Mapper
@Repository
public interface SurchargeMapper extends BaseMapper<SurchargeDO> {

    long countByType(SurchargeCountQuery surchargeCountQuery);

    List<SurchargeDO> pageByType(SurchargeListDTO surchargeListDTO);

}
