package com.holderzone.saas.store.business.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.business.service.BrandConfigService;
import com.holderzone.saas.store.dto.business.brand.BrandConfigDTO;
import com.holderzone.saas.store.dto.business.brand.BrandConfigQueryDTO;
import com.holderzone.saas.store.dto.business.brand.BrandConfigSaveOrUpdateDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 品牌配置Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api(tags = "品牌配置管理")
@RequestMapping("/brand_config")
public class BrandConfigController {

    private final BrandConfigService brandConfigService;

    @ApiOperation(value = "保存或更新品牌配置", notes = "如果品牌配置已存在则更新，不存在则创建")
    @PostMapping("/save_or_update")
    public Long saveOrUpdateBrandConfig(@RequestBody @Valid BrandConfigSaveOrUpdateDTO saveOrUpdateDTO) {
        log.info("[品牌配置]保存或更新品牌配置，参数：{}", JacksonUtils.writeValueAsString(saveOrUpdateDTO));
        return brandConfigService.saveOrUpdateBrandConfig(saveOrUpdateDTO);
    }

    @ApiOperation(value = "删除品牌配置")
    @PostMapping("/delete/{guid}")
    public void deleteBrandConfig(@PathVariable("guid") Long guid) {
        log.info("[品牌配置]删除品牌配置，ID：{}", guid);
        brandConfigService.deleteBrandConfig(guid);
    }

    @ApiOperation(value = "根据ID查询品牌配置")
    @GetMapping("/get/{guid}")
    public BrandConfigDTO getBrandConfigById(@PathVariable("guid") Long guid) {
        log.info("[品牌配置]根据ID查询品牌配置，ID：{}", guid);
        return brandConfigService.getBrandConfigById(guid);
    }

    @ApiOperation(value = "根据品牌GUID查询品牌配置")
    @GetMapping("/get_by_brand/{brandGuid}")
    public BrandConfigDTO getBrandConfigByBrandGuid(@PathVariable("brandGuid") String brandGuid) {
        log.info("[品牌配置]根据品牌GUID查询品牌配置，brandGuid：{}", brandGuid);
        return brandConfigService.getBrandConfigByBrandGuid(brandGuid);
    }

    @ApiOperation(value = "分页查询品牌配置列表")
    @PostMapping("/list_by_page")
    public Page<BrandConfigDTO> listBrandConfigByPage(@RequestBody @Valid BrandConfigQueryDTO queryDTO) {
        log.info("[品牌配置]分页查询品牌配置列表，参数：{}", JacksonUtils.writeValueAsString(queryDTO));
        return brandConfigService.listBrandConfigByPage(queryDTO);
    }

    @ApiOperation(value = "查询品牌配置列表")
    @PostMapping("/list")
    public List<BrandConfigDTO> listBrandConfig(@RequestBody @Valid BrandConfigQueryDTO queryDTO) {
        log.info("[品牌配置]查询品牌配置列表，参数：{}", JacksonUtils.writeValueAsString(queryDTO));
        return brandConfigService.listBrandConfig(queryDTO);
    }

    @ApiOperation(value = "根据门店GUID查询品牌配置")
    @GetMapping("/get_by_store/{storeGuid}")
    public BrandConfigDTO getBrandConfigByStoreGuid(@PathVariable("storeGuid") String storeGuid) {
        log.info("[品牌配置]根据门店GUID查询品牌配置，storeGuid：{}", storeGuid);
        return brandConfigService.getBrandConfigByStoreGuid(storeGuid);
    }
}
