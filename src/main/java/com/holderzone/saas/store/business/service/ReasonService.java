package com.holderzone.saas.store.business.service;

import com.holderzone.saas.store.dto.business.reason.ReasonCopyReqDTO;
import com.holderzone.saas.store.dto.business.reason.ReasonDTO;
import com.holderzone.saas.store.dto.business.reason.ReasonTypeDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReasonMapper
 * @date 2019/08/15 15:05
 * @description //TODO
 * @program holder-saas-store-business
 */
public interface ReasonService {

    List<ReasonDTO> findReason(ReasonDTO reasonDTO);

    void insertReason(ReasonDTO reasonDTO);

    void deleteReason(ReasonDTO reasonDTO);

    void updateReason(ReasonDTO reasonDTO);

    void updateCount(List<String> guids);

    List<ReasonTypeDTO> findReasonType();

    List<ReasonTypeDTO> findReasonTypeSuperMarket();

    /**
     * 复制'原因列表'
     * @param copyReqDTO 原因实体（类别code,原因内容）,门店列表
     * @return Boolean
     */
    Boolean copyReason(ReasonCopyReqDTO copyReqDTO);
}