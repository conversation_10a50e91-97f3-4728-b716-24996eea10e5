package com.holderzone.saas.store.business.queue.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.business.queue.domain.HolderQueueDO;
import com.holderzone.saas.store.business.queue.domain.HolderQueueItemDO;
import com.holderzone.saas.store.business.queue.domain.QueueItemStatusEnum;
import com.holderzone.saas.store.business.queue.event.enuma.BusinessExceptionsEnum;
import com.holderzone.saas.store.business.queue.helper.DynamicHelper;
import com.holderzone.saas.store.business.queue.mapper.QueueItemMapper;
import com.holderzone.saas.store.business.queue.mapper.QueueMapper;
import com.holderzone.saas.store.business.queue.mapstruct.QueueItemMapStruct;
import com.holderzone.saas.store.business.queue.mapstruct.QueueMapStruct;
import com.holderzone.saas.store.business.queue.service.QueueConfigService;
import com.holderzone.saas.store.business.queue.service.QueueItemService;
import com.holderzone.saas.store.business.queue.service.remote.*;
import com.holderzone.saas.store.business.queue.utils.QueuedUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.content.PrintQueueDTO;
import com.holderzone.saas.store.dto.queue.*;
import com.holderzone.saas.store.dto.table.OpenTableDTO;
import com.holderzone.saas.store.dto.weixin.req.WxQueueInfoReqDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.text.DecimalFormat;
import java.text.Format;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueItemServiceImpl
 * @date 2019/03/27 17:23
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Slf4j
@Service
public class QueueItemServiceImpl extends ServiceImpl<QueueItemMapper, HolderQueueItemDO> implements QueueItemService {
    private static final Format format = new DecimalFormat("000");
    private static final ExecutorService NOTIFY_EXECUTOR = new ThreadPoolExecutor(4, 10, 1, TimeUnit.MINUTES, new SynchronousQueue<Runnable>(), (e) -> new Thread(e, "queue_notify"));
    private static final float STEP = 0.1F;
    protected Boolean lock = true;
    @Autowired
    protected TransactionTemplate transactionTemplate;
    private QueueMapper queueMapper;
    private QueueItemMapStruct queueItemMapStruct;
    private BusinessMsgClient businessMsgClient;
    private PrintClient printClient;
    private OrganizationClientService organizationClientService;
    private TableClientService tableClientService;
    private WxClient wxClient;
    private QueueConfigService queueConfigService;
    private QueueMapStruct queueMapStruct;
    private DynamicHelper dynamicHelper;

    @Autowired
    public QueueItemServiceImpl(QueueMapper queueMapper, QueueItemMapStruct queueItemMapStruct, BusinessMsgClient businessMsgClient
            , PrintClient printClient, OrganizationClientService organizationClientService,
                                TableClientService tableClientService, WxClient wxClient,
                                QueueConfigService queueConfigService, QueueMapStruct queueMapStruct,
                                DynamicHelper dynamicHelper) {
        this.queueMapper = queueMapper;
        this.queueItemMapStruct = queueItemMapStruct;
        this.businessMsgClient = businessMsgClient;
        this.printClient = printClient;
        this.organizationClientService = organizationClientService;
        this.tableClientService = tableClientService;
        this.wxClient = wxClient;
        this.queueConfigService = queueConfigService;
        this.queueMapStruct = queueMapStruct;
        this.dynamicHelper = dynamicHelper;
    }

    public static void main(String[] args) {
        WxQueueListDTO wxQueueListDTO = new WxQueueListDTO();
        wxQueueListDTO.setDate(LocalDate.now());
        System.out.println(JacksonUtils.writeValueAsString(wxQueueListDTO));
    }

    @Override
    public LocalDateTime currentBusinessStart(String storeGuid) {
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(storeGuid);
        return currentBusinessStart(storeDTO.getBusinessStart());
    }

    private LocalDateTime currentBusinessStart(LocalTime time) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime bus = LocalDateTime.of(LocalDateTime.now().toLocalDate(), time);
        if (now.isBefore(bus)) {
            bus = bus.withDayOfMonth(bus.getDayOfMonth() - 1);
        }
        return bus;
    }

    private static final String QUEUE_LOCK = "queue:in:lock:";

    @Override
    public HolderQueueItemDetailDTO inQueue(HolderQueueItemDTO dto) {
        UserContext userContext = UserContextUtils.get();
        List<HolderQueueDO> queueDOList = queueMapper.selectList(new LambdaQueryWrapper<HolderQueueDO>()
                .eq(HolderQueueDO::getIsEnable, true)
                .eq(HolderQueueDO::getStoreGuid, dto.getStoreGuid())
                .orderByAsc(HolderQueueDO::getMax));
        //从所有队列中查询一个队列
        HolderQueueDO queueDO = checkOne(queueDOList,dto.getPeopleNumber());

        log.info("----------------------------" + Thread.currentThread().getName() + " try lock");
        if (!RedissonLockUtil.tryLock(QUEUE_LOCK + queueDO.getGuid(), 3, 10)) {
            throw new BusinessException(BusinessExceptionsEnum.THE_SYSTEM_IS_BUSY.getMessage());
        }
        log.info("----------------------------" + Thread.currentThread().getName() + " locked");

        try {
            HolderQueueItemDetailDTO re = doinTransaction(() -> {
                HolderQueueItemDetailDTO oldResult = queryOldItem(dto.getPhone(),queueDO.getGuid());
                if(oldResult != null){
                    return oldResult;
                }
                HolderQueueItemDetailDTO result = queueItemMapStruct.toDetailDto(buildQueueItem(dto,queueDO,userContext.getUserGuid()));
                result.setBefore(preCount(queueDO.getGuid(), result.getSort()));
                return result;
            });
            //异步发消息并且打印
            CompletableFuture.runAsync(() ->{
                UserContextUtils.put(userContext);
                sendMessage(re, dto);
                print(re, dto);
            },NOTIFY_EXECUTOR).exceptionally(e ->{
                log.error("打印或者发送消息失败",e);
                return null;
            });
            return re;
        }catch (Exception e){
            log.error("排队失败",e);
            throw new BusinessException(BusinessExceptionsEnum.THE_SYSTEM_IS_BUSY.getMessage());
        }finally {
            if (RedissonLockUtil.isLocked(QUEUE_LOCK + queueDO.getGuid())) {
                log.info("----------------------------" + Thread.currentThread().getName() + " release lock");
                RedissonLockUtil.unlock(QUEUE_LOCK + queueDO.getGuid());
                log.info("----------------------------" + Thread.currentThread().getName() + " released lock");
            }
        }
    }

    private HolderQueueItemDetailDTO queryOldItem(String phone, String guid) {
        if (StringUtils.isEmpty(phone)) {
            return null;
        }
        HolderQueueItemDO old = baseMapper.selectOne(new LambdaQueryWrapper<HolderQueueItemDO>()
                .eq(HolderQueueItemDO::getQueueGuid, guid)
                .eq(HolderQueueItemDO::getPhone, phone)
                .eq(HolderQueueItemDO::getStatus, QueueItemStatusEnum.INQUEUE.ordinal()));
        if (old == null) {
            return null;
        }
        HolderQueueItemDetailDTO result = queueItemMapStruct.toDetailDto(old);
        result.setBefore(preCount(guid, result.getSort()));
        return result;
    }

    private HolderQueueItemDO buildQueueItem(HolderQueueItemDTO dto, HolderQueueDO queueDO,String userGuid) {
        HolderQueueItemDO itemDO = queueItemMapStruct.toDo(dto);
        itemDO.setPeopleNumber(dto.getPeopleNumber());
        itemDO.setGuid(QueuedUtils.nextRecordGuid());
        itemDO.setGmtCreate(LocalDateTime.now());
        itemDO.setBusinessDay(LocalDate.now());
        itemDO.setQueueGuid(queueDO.getGuid());
        itemDO.setIsDeleted(false);
        itemDO.setIsEnable(true);
        itemDO.setStatus((byte) QueueItemStatusEnum.INQUEUE.ordinal());
        itemDO.setStartTime(LocalDateTime.now());
        itemDO.setQueueCode(queueDO.getCode());
        itemDO.setCreateStaffGuid(dto.getDeviceType() == BaseDeviceTypeEnum.WECHAT.getCode() ? "wechat" : userGuid);
        itemDO.setStoreGuid(queueDO.getStoreGuid());
        itemDO.setDeviceType(dto.getDeviceType());
        itemDO.setDeviceId(dto.getDeviceId());
        itemDO.setSort(order(queueDO.getGuid()).floatValue());
        itemDO.setCode(format.format(itemDO.getSort()));
        this.save(itemDO);
        return itemDO;
    }

    private HolderQueueDO checkOne(List<HolderQueueDO> queueDOList,Byte peopleNumber) {
        if (CollectionUtil.isEmpty(queueDOList)) {
            throw new BusinessException(BusinessExceptionsEnum.QUEUE_DOES_NOT_EXIST.getMessage());
        }
        HolderQueueDO minQueue = queueDOList.stream().min(Comparator.comparing(HolderQueueDO::getMin)).orElse(null);
        if(minQueue == null){
            throw new BusinessException(BusinessExceptionsEnum.QUEUE_DOES_NOT_EXIST.getMessage());
        }
        if (minQueue.getMin() > peopleNumber) {
            throw new BusinessException(BusinessExceptionsEnum.NOT_ENOUGH_CUSTOMERS.getMessage());
        }
        HolderQueueDO maxQueue = queueDOList.stream().filter(e -> e.getMax() != null).max(Comparator.comparing(HolderQueueDO::getMax)).orElse(null);
        HolderQueueDO nullMaxQueue = queueDOList.stream().filter(e -> e.getMax() == null).findFirst().orElse(null);
        if(maxQueue == null && nullMaxQueue == null){
            throw new BusinessException(BusinessExceptionsEnum.QUEUE_DOES_NOT_EXIST.getMessage());
        }
        if(nullMaxQueue == null && peopleNumber > maxQueue.getMax()){
            throw new BusinessException(BusinessExceptionsEnum.OVER_NUMBER_OF_CUSTOMERS.getMessage());
        }
        //过滤出一条队列
        return filterOneQueue(queueDOList,peopleNumber);
    }

    private HolderQueueDO filterOneQueue(List<HolderQueueDO> queueDOList,Byte peopleNumber){
        HolderQueueDO holderQueue = queueDOList.stream().filter(q -> (q.getMax() == null || peopleNumber <= q.getMax()) && peopleNumber >= q.getMin()).findFirst().orElse(null);
        if(holderQueue == null){
            throw new BusinessException(BusinessExceptionsEnum.QUEUE_DOES_NOT_EXIST.getMessage());
        }
        return holderQueue;
    }

    public <T> T doinTransaction(Supplier<T> supplier) {
        return transactionTemplate.execute((transactionStatus) -> {
            try {
                return supplier.get();
            } catch (Exception e) {
                log.error("fail to excuete biz: ", e);
                transactionStatus.setRollbackOnly();
                throw e;
            }
        });

    }

    public void sendMessage(HolderQueueItemDetailDTO result, BaseDTO dto) {
        BusinessMessageDTO messageDTO = BusinessMessageDTO.builder()
                .subject(BusinessMsgTypeEnum.QUEUE_CHANGED_MSG_TYPE.getName())
                //TODO message type
                .messageType(BusinessMsgTypeEnum.QUEUE_CHANGED_MSG_TYPE.getId())
                .detailMessageType(BusinessMsgTypeEnum.QUEUE_CHANGED_MSG_TYPE.getId())
                .content("queue changed")
                .platform("2")
                .storeGuid(dto.getStoreGuid())
                .storeName(dto.getStoreName())
                .build();
        Assert.isTrue("success".equals(businessMsgClient.sendMsg(messageDTO)));
    }

    public void print(HolderQueueItemDetailDTO result, BaseDTO dto) {
        if (dto.getDeviceType() == BaseDeviceTypeEnum.WECHAT.getCode()) {
            return;
        }
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(result.getStoreGuid());
        String qrCode = buildQrCode(result, dto);
        PrintQueueDTO printQueueDTO = new PrintQueueDTO();
        BeanUtils.copyProperties(dto, printQueueDTO);
        printQueueDTO.setInvoiceType(InvoiceTypeEnum.QUEUE.getType());
        printQueueDTO.setQueueTime(DateTimeUtils.localDateTime2Mills(result.getStartTime()));
        printQueueDTO.setQueueNumber(result.getOrder());
        printQueueDTO.setQueueUrl(qrCode);
        printQueueDTO.setStoreName(storeDTO.getName());
        printQueueDTO.setStoreGuid(dto.getStoreGuid());
        printQueueDTO.setPersonNumber(result.getPeopleNumber().intValue());
        printQueueDTO.setWaitNumber(result.getBefore().longValue());
        printQueueDTO.setTel(storeDTO.getContactTel());
        printQueueDTO.setDeviceId(dto.getDeviceId());
        printQueueDTO.setPrintUid(result.getGuid());
        printQueueDTO.setCreateTime(DateTimeUtils.localDateTime2Mills(LocalDateTime.now()));
        printQueueDTO.setOperatorStaffGuid(UserContextUtils.getUserGuid());
        printQueueDTO.setOperatorStaffName(UserContextUtils.getUserName());
        printQueueDTO.setPrintSourceEnum(PrintSourceEnum.getPrintSourceByDeviceType(result.getDeviceType()));
        printClient.print(printQueueDTO);
    }

    public String buildQrCode(HolderQueueItemDetailDTO result, BaseDTO dto) {
        WxQueueInfoReqDTO wxQueueInfoReqDTO = new WxQueueInfoReqDTO();
        BeanUtils.copyProperties(dto, wxQueueInfoReqDTO);
        wxQueueInfoReqDTO.setQueueGuid(result.getGuid());
        return wxClient.getQueueQrCode(wxQueueInfoReqDTO);
    }

    @Override
    public HolderQueueItemDetailDTO call(ItemGuidDTO dto) {
        UserContext userContext = UserContextUtils.get();
        HolderQueueItemDetailDTO result = doinTransaction(() -> {
            HolderQueueItemDO itemDO = baseMapper.selectOne(new LambdaQueryWrapper<HolderQueueItemDO>()
                    .eq(HolderQueueItemDO::getGuid, dto.getItemGuid())
                    .nested((e) ->
                            e.or().eq(HolderQueueItemDO::getStatus, QueueItemStatusEnum.INQUEUE.ordinal())
                                    .or().eq(HolderQueueItemDO::getStatus, QueueItemStatusEnum.CALLING.ordinal())
                    ));
            if (itemDO == null) {
                throw new BusinessException("记录不存在,不能叫号");
            }
//        HolderQueueItemDO other = baseMapper.selectOne(new LambdaQueryWrapper<HolderQueueItemDO>()
//                .eq(HolderQueueItemDO::getQueueGuid, itemDO.getQueueGuid())
//                .eq(HolderQueueItemDO::getStatus, QueueItemStatusEnum.CALLING.ordinal())
//                .ne(HolderQueueItemDO::getGuid, itemDO.getGuid())
//                .ge(HolderQueueItemDO::getStartTime, currentBusinessStart(itemDO.getStoreGuid()))
//                .last("limit 1")
//        );
//        if (other != null) {
//            throw new BusinessException("不能同时呼叫" + itemDO.getSort() + "-" + other.getSort());
//        }

            HolderQueueItemDO updateDo = new HolderQueueItemDO()
                    .setCallTimes(itemDO.getCallTimes() + 1)
                    .setGmtModified(LocalDateTime.now())
                    .setStatus((byte) QueueItemStatusEnum.CALLING.ordinal())
                    .setGmtModified(LocalDateTime.now())
                    .setModifiedStaffGuid(userContext.getUserGuid());

            int effective = baseMapper.update(updateDo, new UpdateWrapper<HolderQueueItemDO>().lambda()
                    .eq(HolderQueueItemDO::getGuid, dto.getItemGuid())
                    .eq(HolderQueueItemDO::getCallTimes, itemDO.getCallTimes())
            );
            if (effective != 1) {
                throw new BusinessException("叫号失败请稍候重试");
            }
            return queueItemMapStruct.toDetailDto(itemDO).setBefore(0);
        });
        sendMessage(result, dto);
        notify(result);
        return result;
    }

    private void notify(String queueGuid) {
        try {
            notify(convertWechat(fetchByPosition(queueGuid, 0, 3).stream().filter(e -> e.getDeviceType() == BaseDeviceTypeEnum.WECHAT.getCode()).collect(Collectors.toList())));
        } catch (Exception e) {
            log.error("notify fail:{}", e);
        }
    }

    private void notify(HolderQueueItemDetailDTO dto, String queueGuid) {
        try {
            List<ItemGuidDTO> itemGuidDTOS = convertWechat(fetchByPosition(queueGuid, 0, 3).stream().filter(e -> e.getDeviceType() == BaseDeviceTypeEnum.WECHAT.getCode()).collect(Collectors.toList()));
            if (dto.getDeviceType() == BaseDeviceTypeEnum.WECHAT.getCode()) {
                itemGuidDTOS.add(convert(dto));
            }
            notify(itemGuidDTOS);
        } catch (Exception e) {
            log.error("notify fail:{}", e);
        }
    }

    private void notify(HolderQueueItemDetailDTO dto) {
        try {
            if (dto.getDeviceType() == BaseDeviceTypeEnum.WECHAT.getCode()) {
                notify(Arrays.asList(convert(dto)));
            }
        } catch (Exception e) {
            log.error("notify fail:{}", e);
        }
    }

    private ItemGuidDTO convert(HolderQueueItemDetailDTO dto) {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        ItemGuidDTO itemGuidDTO = new ItemGuidDTO();
        itemGuidDTO.setEnterpriseGuid(enterpriseGuid);
        itemGuidDTO.setItemGuid(dto.getGuid());
        return itemGuidDTO;
    }

    private List<ItemGuidDTO> convertWechat(List<HolderQueueItemDO> comings) {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        return comings.stream().filter((e) -> e.getDeviceType() == BaseDeviceTypeEnum.WECHAT.getCode()).map((e) -> {
            ItemGuidDTO temp = new ItemGuidDTO();
            temp.setItemGuid(e.getGuid());
            temp.setEnterpriseGuid(enterpriseGuid);
            return temp;
        }).collect(Collectors.toList());
    }

    private void notify(List<ItemGuidDTO> wechats) {
        if (wechats == null || wechats.isEmpty()) {
            return;
        }
        NOTIFY_EXECUTOR.execute(() -> {
            if (!wechats.isEmpty()) {
                wxClient.callUpNotify(wechats);
            }
        });
    }

    private List<HolderQueueItemDO> fetchByPosition(String queueGuid, int start, int size) {
        return baseMapper.selectList(new LambdaQueryWrapper<HolderQueueItemDO>()
                .eq(HolderQueueItemDO::getQueueGuid, queueGuid)
                .eq(HolderQueueItemDO::getStatus, QueueItemStatusEnum.INQUEUE.ordinal())
                .orderByAsc(HolderQueueItemDO::getSort)
                .last(String.format("limit %s,%s", start, size))
        );
    }

    private HolderQueueItemDO fetchOne(String queueGuid, int start) {
        List<HolderQueueItemDO> holderQueueItemDOS = fetchByPosition(queueGuid, start, 1);
        if (CollectionUtils.isEmpty(holderQueueItemDOS)) {
            return null;
        }
        return holderQueueItemDOS.get(0);
    }

    public Integer order(String queueGuid) {
        return Integer.valueOf(baseMapper.getMaxOrder(queueGuid)) + 1;
    }

    public Integer preCount(String queueGuid, Integer order) {
        return baseMapper.getPreCount(queueGuid, order);
    }

    @Override
    public HolderQueueItemDetailDTO pass(ItemGuidDTO dto) {
        UserContext userContext = UserContextUtils.get();
        HolderQueueItemDetailDTO result = doinTransaction(() -> {
            HolderQueueItemDO itemDO = baseMapper.selectOne(new LambdaQueryWrapper<HolderQueueItemDO>()
                    .eq(HolderQueueItemDO::getGuid, dto.getItemGuid())
                    .nested((e) ->
                            e.or().eq(HolderQueueItemDO::getStatus, QueueItemStatusEnum.INQUEUE.ordinal())
                                    .or().eq(HolderQueueItemDO::getStatus, QueueItemStatusEnum.CALLING.ordinal())
                    ));
            if (itemDO == null) {
                throw new BusinessException("已过号或已就餐,请切换导航刷新查看");
            }
            HolderQueueItemDO updateDo = new HolderQueueItemDO()
                    .setStatus((byte) QueueItemStatusEnum.PASS.ordinal())
                    .setEndTime(LocalDateTime.now())
                    .setGmtModified(LocalDateTime.now())
                    .setModifiedStaffGuid(userContext.getUserGuid());
            baseMapper.update(updateDo, new UpdateWrapper<HolderQueueItemDO>().lambda().eq(HolderQueueItemDO::getGuid, dto.getItemGuid()));
            return queueItemMapStruct.toDetailDto(itemDO).setBefore(0);
        });
        sendMessage(result, dto);
        notify(result, result.getQueueGuid());
        return result;
    }

    @Override
    public HolderQueueItemDetailDTO recover(ItemGuidDTO dto) {
        UserContext userContext = UserContextUtils.get();
        HolderQueueItemDetailDTO result = doinTransaction(() -> {
            StoreConfigDTO storeConfigDTO = queueConfigService.obtain(dto.getStoreGuid());
            if (storeConfigDTO == null) {
                storeConfigDTO = StoreConfigDTO.DEFAULT.build(dto.getStoreGuid());
            }

            if (!storeConfigDTO.getIsEnableRecovery()) {
                throw new BusinessException("该门店不允许恢复至队列");
            }
            HolderQueueItemDO itemDO = baseMapper.selectOne(new LambdaQueryWrapper<HolderQueueItemDO>()
                    .eq(HolderQueueItemDO::getGuid, dto.getItemGuid())
                    .eq(HolderQueueItemDO::getStatus, QueueItemStatusEnum.PASS.ordinal()));

            if (itemDO == null) {
                throw new BusinessException("已恢复至队列,请切换导航刷新查看");
            }
            HolderQueueItemDO updateDo = new HolderQueueItemDO()
                    .setStatus((byte) QueueItemStatusEnum.INQUEUE.ordinal())
                    .setCallTimes(0)
                    .setGmtModified(LocalDateTime.now())
                    .setModifiedStaffGuid(userContext.getUserGuid());
            if (storeConfigDTO.getIsEnableRecovery()) {
                HolderQueueItemDO ref = fetchOne(itemDO.getQueueGuid(), Optional.ofNullable(storeConfigDTO.getRecoveryNum()).orElse(StoreConfigDTO.DEFAULT.build("").getRecoveryNum()));
                HolderQueueItemDO ref2 = fetchOne(itemDO.getQueueGuid(), Optional.ofNullable(storeConfigDTO.getRecoveryNum()).orElse(StoreConfigDTO.DEFAULT.build("").getRecoveryNum()) - 1);
                if (ref != null && ref2 != null) {
                    updateDo.setSort((ref.getSort() + ref2.getSort()) / 2);
                } else if (ref2 == null && ref != null) {
                    updateDo.setSort(ref.getSort() - STEP);
                } else if (ref2 != null && ref == null) {
                    updateDo.setSort(ref2.getSort() + STEP);
                } else {
                    updateDo.setSort(Integer.valueOf(baseMapper.getMaxOrder(itemDO.getQueueGuid())) + STEP);
                }
            }
            baseMapper.update(updateDo, new UpdateWrapper<HolderQueueItemDO>().lambda().eq(HolderQueueItemDO::getGuid, dto.getItemGuid()));
            return queueItemMapStruct.toDetailDto(itemDO).setBefore(0);
        });
        sendMessage(result, dto);
        return result;
    }

    @Override
    public HolderQueueQueueRecordDTO obtain(ItemGuidDTO dto) {
        HolderQueueItemDO itemDO = baseMapper.selectOne(new LambdaQueryWrapper<HolderQueueItemDO>()
                .eq(HolderQueueItemDO::getGuid, dto.getItemGuid()));
        if (itemDO == null) {
            return null;
        }
        HolderQueueDO queueDO = queueMapper.selectOne(new LambdaQueryWrapper<HolderQueueDO>().eq(HolderQueueDO::getGuid, itemDO.getQueueGuid()));
        HolderQueueItemDetailDTO queueItem = queueItemMapStruct.toDetailDto(itemDO);
        HolderQueueDTO queueDTO = queueMapStruct.toDetailDto(queueDO);
        queueItem.setBefore(preCount(itemDO.getQueueGuid(), queueItem.getSort()));
        return new HolderQueueQueueRecordDTO(queueDTO, queueItem);
    }


    @Override
    public HolderQueueItemDetailDTO confirm(ItemGuidDTO dto) {
        UserContext userContext = UserContextUtils.get();
        StoreConfigDTO storeConfigDTO = queueConfigService.obtain(dto.getStoreGuid());
        HolderQueueItemDO itemDO = baseMapper.selectOne(new LambdaQueryWrapper<HolderQueueItemDO>()
                .eq(HolderQueueItemDO::getGuid, dto.getItemGuid())
        );
        if (storeConfigDTO == null) {
            storeConfigDTO = StoreConfigDTO.DEFAULT.build(dto.getStoreGuid());
        }
        if (itemDO.getStatus() == QueueItemStatusEnum.PASS.ordinal() && !storeConfigDTO.getIsEnableEat()) {
            throw new BusinessException("不允许直接就餐操作");
        }
        HolderQueueItemDetailDTO result = doinTransaction(() -> {

            HolderQueueItemDO updateDo = new HolderQueueItemDO()
                    .setStatus((byte) QueueItemStatusEnum.EATED.ordinal())
                    .setEndTime(LocalDateTime.now())
                    .setGmtModified(LocalDateTime.now())
                    .setModifiedStaffGuid(userContext.getUserGuid());
            if (dto instanceof TableConfirmDTO) {
                updateDo.setTableName(((TableConfirmDTO) dto).getDiningTableName());
                updateDo.setTableGuid(((TableConfirmDTO) dto).getDiningTableGuid());
                updateDo.setAreaName(((TableConfirmDTO) dto).getAreaName());
            }
            baseMapper.update(updateDo, new UpdateWrapper<HolderQueueItemDO>().lambda().eq(HolderQueueItemDO::getGuid, dto.getItemGuid()));
            return queueItemMapStruct.toDetailDto(itemDO).setBefore(0);
        });
        sendMessage(result, dto);
        notify(result, result.getQueueGuid());
        return result;
    }

    @Override
    public TableQueueItemDetailDTO confirmAndTable(TableConfirmDTO dto) {
        TableQueueItemDetailDTO result = doinTransaction(() -> {
            HolderQueueItemDetailDTO item = confirm(dto);
            OpenTableDTO orderReqDTO = queueItemMapStruct.toTradeDTO(dto);
            orderReqDTO.setActualGuestsNo(item.getPeopleNumber().intValue());
            orderReqDTO.setTableGuid(dto.getDiningTableGuid());
            orderReqDTO.setTableCode(dto.getDiningTableName());
            orderReqDTO.setAreaName(dto.getAreaName());
            orderReqDTO.setAreaGuid(dto.getAreaGuid());
            String tradeNo = tableClientService.openTable(orderReqDTO);
            TableQueueItemDetailDTO tableQueueItemDetailDTO = queueItemMapStruct.toTableDTO(item);
            tableQueueItemDetailDTO.setOrderNo(tradeNo);
            return tableQueueItemDetailDTO;
        });
        sendMessage(result, dto);
        notify(result, result.getQueueGuid());
        return result;
    }

    @Override
    public List<HolderQueueItemDetailDTO> listQueueItemDetails() {

        List<HolderQueueItemDO> dos = baseMapper.selectList(buildQueryWrapper());
        return convert(dos);
    }

    @Override
    public Page<HolderQueueItemDetailDTO> page(Page page) {
        IPage<HolderQueueItemDO> ipage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(page.getCurrentPage(), page.getPageSize());
        baseMapper.selectPage(ipage, buildQueryWrapper());
        page.setData(convert(ipage.getRecords()));
        page.setTotalCount(ipage.getTotal());
        return page;
    }

    @Override
    public Boolean cancel(String guid, String enterpriseGuid) {
        dynamicHelper.changeDatasource(enterpriseGuid);
        HolderQueueItemDO queueItem = new HolderQueueItemDO().setStatus((byte) QueueItemStatusEnum.CANCEL.ordinal());
        queueItem.setCancelTime(LocalDateTime.now());
        baseMapper.update(queueItem, new UpdateWrapper<HolderQueueItemDO>().lambda()
                .eq(HolderQueueItemDO::getGuid, guid)
        );
        return true;
    }

    @Override
    public List<WxQueueListDTO> queryByUser(QueueWechatDTO queueWechatDTO) {
        dynamicHelper.changeDatasource(queueWechatDTO.getEnterpriseGuid());
        //查询品牌下的所有门店
        BrandDTO brandDTO = organizationClientService.queryBrandByGuid(queueWechatDTO.getBrandGuid());
        log.info("查询到的brandDTO是：{}", JacksonUtils.writeValueAsString(brandDTO));
        if (brandDTO == null) {
            log.error("无品牌【{}】信息", queueWechatDTO.getBrandGuid());
            throw new BusinessException("无品牌信息");
        }
        List<StoreDTO> storeDTOList = organizationClientService.queryStoreByBrand(Lists.newArrayList(queueWechatDTO.getBrandGuid()));
        log.info("查询到的storeDTOList是：{}", JacksonUtils.writeValueAsString(storeDTOList));
        Map<String, StoreDTO> storeDTOMap = storeDTOList.stream().collect(Collectors.toMap(StoreDTO::getGuid, Function.identity()));
        LambdaQueryWrapper<HolderQueueItemDO> eq = new LambdaQueryWrapper<HolderQueueItemDO>().eq(HolderQueueItemDO::getDeviceId, queueWechatDTO.getUserGuid());
        if (!CollectionUtils.isEmpty(storeDTOList)) {
            eq.in(HolderQueueItemDO::getStoreGuid, storeDTOList.stream().map(StoreDTO::getGuid).collect(Collectors.toList()));
        }
        if (!ObjectUtils.isEmpty(queueWechatDTO.getStatus())) {
            eq.eq(HolderQueueItemDO::getStatus, queueWechatDTO.getStatus());
        }
        List<HolderQueueItemDO> list = this.list(eq);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<WxQueueListDTO> respList = Lists.newArrayList();
        list.forEach(o -> {
            WxQueueListDTO wxQueueListDTO = new WxQueueListDTO();
            wxQueueListDTO.setBrandGuid(queueWechatDTO.getBrandGuid());
            wxQueueListDTO.setBrandName(brandDTO.getName());
            wxQueueListDTO.setStoreGuid(o.getStoreGuid());
            StoreDTO storeDTO = storeDTOMap.get(o.getStoreGuid());
            wxQueueListDTO.setStoreName(storeDTO == null ? null : storeDTO.getName());
            wxQueueListDTO.setGuid(o.getGuid());
            wxQueueListDTO.setUserGuid(o.getDeviceId());
            wxQueueListDTO.setStatus(o.getStatus());
            wxQueueListDTO.setCancelTime(o.getCancelTime());
            wxQueueListDTO.initDate(o.getGmtCreate());
            respList.add(wxQueueListDTO);
        });
        return respList;
    }

    @Override
    public HolderQueueQueueRecordDTO getQueueDetail(@RequestBody ItemGuidDTO itemGuidDTO) {
        dynamicHelper.changeDatasource(itemGuidDTO.getEnterpriseGuid());
        //查询品牌下的所有门店
        HolderQueueItemDO itemDO = baseMapper.selectOne(new LambdaQueryWrapper<HolderQueueItemDO>()
                .eq(HolderQueueItemDO::getGuid, itemGuidDTO.getItemGuid()));
        if (itemDO == null) {
            return null;
        }
        HolderQueueDO queueDO = queueMapper.selectOne(new LambdaQueryWrapper<HolderQueueDO>().eq(HolderQueueDO::getGuid, itemDO.getQueueGuid()));
        HolderQueueItemDetailDTO queueItem = queueItemMapStruct.toDetailDto(itemDO);
        HolderQueueDTO queueDTO = queueMapStruct.toDetailDto(queueDO);


        BrandDTO brandDTO = organizationClientService.queryBrandByGuid(itemGuidDTO.getBrandGuid());
        if (brandDTO == null) {
            log.error("无品牌【{}】信息", itemGuidDTO.getBrandGuid());
            throw new BusinessException("无品牌信息");
        }
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(itemDO.getStoreGuid());
        log.info("查询到的storeDto: {}", storeDTO);
        queueDTO.setLogoUrl(brandDTO.getLogoUrl());
        queueDTO.setStoreName(storeDTO.getName());
        queueDTO.setBrandName(brandDTO.getName());
        queueItem.setBefore(preCount(itemDO.getQueueGuid(), queueItem.getSort()));
        return new HolderQueueQueueRecordDTO(queueDTO, queueItem);
    }

    private List<HolderQueueItemDetailDTO> convert(List<HolderQueueItemDO> dos) {
        if (dos == null || dos.isEmpty()) {
            return Collections.emptyList();
        }
        return dos.stream().map((e) -> queueItemMapStruct.toDetailDto(e)).collect(Collectors.toList());
    }

    private LambdaQueryWrapper<HolderQueueItemDO> buildQueryWrapper() {
        return new LambdaQueryWrapper<HolderQueueItemDO>()
                .eq(HolderQueueItemDO::getStoreGuid, UserContextUtils.getStoreGuid())
                .ne(HolderQueueItemDO::getStatus, QueueItemStatusEnum.INQUEUE.ordinal())
                .ne(HolderQueueItemDO::getStatus, QueueItemStatusEnum.CALLING.ordinal())
                .orderByDesc(HolderQueueItemDO::getGmtModified);
    }
}