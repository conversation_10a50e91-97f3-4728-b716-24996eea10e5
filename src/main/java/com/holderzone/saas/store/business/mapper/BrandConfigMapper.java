package com.holderzone.saas.store.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.business.entity.domain.BrandConfigDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * 品牌配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Mapper
@Repository
public interface BrandConfigMapper extends BaseMapper<BrandConfigDO> {

}
