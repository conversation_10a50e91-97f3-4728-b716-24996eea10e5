package com.holderzone.saas.store.business.mapstruct;

import com.holderzone.saas.store.business.entity.domain.StoreConfigDO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigCreateDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigUpdateDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreConfigMapstruct
 * @date 2018/07/29 下午4:32
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Mapper
public interface StoreConfigMapstruct {

    StoreConfigMapstruct INSTANCE = Mappers.getMapper(StoreConfigMapstruct.class);

    StoreConfigDO toStoreConfigDO(StoreConfigCreateDTO storeConfigCreateDTO);

    StoreConfigDO toStoreConfigDO(StoreConfigQueryDTO storeConfigQueryDTO);

    StoreConfigDO toStoreConfigDO(StoreConfigUpdateDTO storeConfigUpdateDTO);

    StoreConfigDTO toStoreConfigDTO(StoreConfigDO storeConfigInSql);

    List<StoreConfigDTO> toStoreConfigDTOS(List<StoreConfigDO> storeConfigDOS);

    List<StoreConfigDTO> toStoreConfigDTOList(List<StoreConfigDO> storeConfigDOList);
}
