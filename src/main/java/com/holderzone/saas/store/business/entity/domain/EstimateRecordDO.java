package com.holderzone.saas.store.business.entity.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateRecordDO
 * @date 2018/08/06 上午11:33
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EstimateRecordDO {

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 估清记录guid
     */
    private String estimateRecordGuid;

    /**
     * 创建人guid
     */
    private String createUserGuid;

    /**
     * 创建人名字
     */
    private String createUserName;

    /**
     * 营业日日期
     */
    private LocalDate businessDay;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
