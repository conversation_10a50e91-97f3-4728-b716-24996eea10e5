package com.holderzone.saas.store.business.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeDO
 * @date 2018/08/27 9:15
 * @description
 * @program holder-saas-store-trading-center
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "hsb_payment_type")
public class PaymentTypeDO {

    private Long id;

    private String paymentTypeGuid;

    private String paymentTypeName;

    private Integer paymentType;

    private Integer state;

    private String storeGuid;

    private String storeName;

    private Integer sorting;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "gmt_create")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "gmt_modified")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "0:全部，1:默认，2:自定义")
    private Integer source;

    @ApiModelProperty(value = "支付模式，0:先登录后支付，1:直接支付")
    private Integer paymentMode;

    @ApiModelProperty(value = "父类支付类型guid")
    private String parentPaymentTypeGuid;

    @ApiModelProperty(value = "收款分流：0关闭 1开启")
    private Integer paymentShunt;

    @ApiModelProperty(value = "是否支持多卡支付")
    private Integer paymentMultiCard;
}
