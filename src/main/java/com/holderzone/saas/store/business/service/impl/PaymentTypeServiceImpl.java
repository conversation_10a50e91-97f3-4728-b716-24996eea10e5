package com.holderzone.saas.store.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.saas.store.business.config.RedisIDGenerator;
import com.holderzone.saas.store.business.entity.domain.PaymentTypeDO;
import com.holderzone.saas.store.business.enums.PaymentType;
import com.holderzone.saas.store.business.mapper.PaymentTypeMapper;
import com.holderzone.saas.store.business.service.PaymentTypeService;
import com.holderzone.saas.store.business.service.RedisService;
import com.holderzone.saas.store.business.service.client.EnterpriseClientService;
import com.holderzone.saas.store.business.service.client.JHPayClientService;
import com.holderzone.saas.store.business.utils.PageAdapter;
import com.holderzone.saas.store.business.utils.PaymentTypeMapStruct;
import com.holderzone.saas.store.dto.journaling.req.PaySerialStatisticsReqDTO;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.enums.MchntTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import redis.clients.jedis.Protocol;

import java.util.*;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.business.config.BillConstant.FAILUER;
import static com.holderzone.saas.store.business.config.BillConstant.SUCCESS;
import static com.holderzone.saas.store.business.utils.PaymentMapStruct.PAYMENT_MAP_STRUCT;


/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeServiceImpl
 * @date 2018/08/27 9:24
 * @description
 * @program holder-saas-store-trading-center
 */
@Slf4j
@Service
public class PaymentTypeServiceImpl extends ServiceImpl<PaymentTypeMapper, PaymentTypeDO> implements PaymentTypeService {

    private static final Integer USER_DEFINED_PAYMENT_TYPE_START = 100;
    private final PaymentTypeMapper paymentTypeMapper;
    private final PaymentTypeMapStruct PAYMENT_TYPE_MAPSTRUCT = PaymentTypeMapStruct.PAYMENT_TYPE_MAP_STRUCT;

    private final RedisService redisService;

    private final EnterpriseClientService enterpriseClientService;

    private final JHPayClientService jhPayClientService;

    private final RedisIDGenerator redisIDGenerator;

    private final RedisTemplate redisTemplate;

    @Autowired
    public PaymentTypeServiceImpl(PaymentTypeMapper paymentTypeMapper, RedisService redisService, EnterpriseClientService enterpriseClientService,
                                  JHPayClientService jhPayClientService, RedisIDGenerator redisIDGenerator, RedisTemplate redisTemplate) {
        this.paymentTypeMapper = paymentTypeMapper;
        this.redisService = redisService;
        this.enterpriseClientService = enterpriseClientService;
        this.jhPayClientService = jhPayClientService;
        this.redisIDGenerator = redisIDGenerator;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public String config(JHConfigDTO jhConfigDTO) {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        // 调用聚合支付确认appId是否合法
        MchntValidateDTO mchntValidateDTO = new MchntValidateDTO();
        mchntValidateDTO.setAppId(jhConfigDTO.getAppId());
        mchntValidateDTO.setAppSecretKey(jhConfigDTO.getAppSecretKey());
        log.info("mchntValidateDTO={}", mchntValidateDTO);
        Boolean flag = jhPayClientService.check(mchntValidateDTO);
        log.info("flag={}", flag);
        if (!flag) {
            return FAILUER;
        }
        PaymentInfoDTO paymentInfoDTO = PAYMENT_MAP_STRUCT.jhConfig(jhConfigDTO);
        paymentInfoDTO.setEnterpriseGuid(enterpriseGuid);
        try {
            log.info("调用云端参数 paymentInfoDTO={}", JacksonUtils.writeValueAsString(paymentInfoDTO));
            Boolean notify = enterpriseClientService.notify(paymentInfoDTO);
            log.info("调用云端返回：{}", notify);
            if (null != notify && notify) {
                redisService.putJHPayInfo(paymentInfoDTO);
                return SUCCESS;
            } else {
                log.info("paymentInfoDTO={}", paymentInfoDTO);
                Boolean update = enterpriseClientService.update(paymentInfoDTO);
                log.info("update={}", update);
                if (update) {
                    return SUCCESS;
                } else {
                    return FAILUER;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return FAILUER;
        }
    }

    @Override
    public String unbind(JHReqDTO jhReqDTO) {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        // 调用聚合支付确认appId是否合法
        PaymentInfoDTO paymentInfoDTO = PAYMENT_MAP_STRUCT.jhConfig(jhReqDTO);
        paymentInfoDTO.setEnterpriseGuid(enterpriseGuid);
        log.info("调用云端解绑商户appid和secretKey paymentInfoDTO={}", JacksonUtils.writeValueAsString(paymentInfoDTO));
        Boolean result = enterpriseClientService.unbind(paymentInfoDTO);
        log.info("result={}", result);
        if (result) {
            redisService.deleteJHPayInfo(paymentInfoDTO.getStoreGuid());
            // 解绑之后修改分流状态为关闭
            paymentTypeMapper.updateShunt(jhReqDTO.getPaymentTypeGuid(), 0, jhReqDTO.getStoreGuid());
            return SUCCESS;
        } else {
            return FAILUER;
        }
    }

    @Override
    public Page<PaymentTypeDTO> getMultiStorePayWay(PaySerialStatisticsReqDTO paySerialStatisticsReqDTO) {
        IPage<PaymentTypeDTO> page = paymentTypeMapper.getMultiStorePayWay(new PageAdapter<>(paySerialStatisticsReqDTO), paySerialStatisticsReqDTO);
        return new PageAdapter<>(page, page.getRecords());
    }

    @Override
    public Boolean initPaymentType(String storeGuid) {
        //查询已初始化完成 默认支付方式
        List<PaymentTypeDTO> paymentTypeDTOList = paymentTypeMapper.getAll(storeGuid, 1);
        if (CollectionUtils.isEmpty(paymentTypeDTOList)) {
            return false;
        }
        int asInt = paymentTypeDTOList.stream().mapToInt(PaymentTypeDTO::getSorting).max().getAsInt();
        Map<Integer, PaymentTypeDTO> oldPayMap = paymentTypeDTOList.stream().collect(Collectors.toMap(PaymentTypeDTO::getPaymentType, e -> e));
        List<PaymentTypeDO> paymentTypeDOS = Lists.newArrayList();
        String storeName = "";
        for (PaymentType value : PaymentType.values()) {
            PaymentTypeDTO paymentTypeDTO = oldPayMap.get(value.getId());
            if (Objects.nonNull(paymentTypeDTO)) {
                storeName = paymentTypeDTO.getStoreName();
                log.info("已初始化！");
                continue;
            }
            if (value.getId() < 0) {
                log.info("小于0");
                continue;
            }
            PaymentTypeDO paymentTypeDO = new PaymentTypeDO();
            paymentTypeDO.setPaymentTypeGuid(String.valueOf(redisIDGenerator.getSingle("hst_payment_type")));
            paymentTypeDO.setSource(1);
            paymentTypeDO.setState(0);
            paymentTypeDO.setPaymentType(value.getId());
            paymentTypeDO.setPaymentTypeName(value.getName());
            paymentTypeDO.setStoreGuid(storeGuid);
            paymentTypeDO.setStoreName(storeName);
            asInt += 1;
            paymentTypeDO.setSorting(asInt);
            paymentTypeDOS.add(paymentTypeDO);
        }
        if (CollectionUtils.isEmpty(paymentTypeDOS)) {
            log.info("无需要初始化的默认支付方式！");
            return false;
        }
        log.info("最终初始化支付方式！参数:{}", JacksonUtils.writeValueAsString(paymentTypeDOS));
        paymentTypeMapper.addAll(paymentTypeDOS);
        return true;
    }

    @Override
    public List<PaymentTypeEnumDTO> getPaymentTypeList() {
        List<PaymentTypeEnumDTO> enumDTOList = Lists.newArrayList();
        for (PaymentType value : PaymentType.values()) {
            if (value.getId() < 5 || value.getId() == 9 || value.getId() == 11 || value.getId() == 12 || value.getId() == 13) {
                continue;
            }
            PaymentTypeEnumDTO enumDTO = new PaymentTypeEnumDTO();
            enumDTO.setPaymentType(value.getId());
            enumDTO.setPaymentTypeName(value.getName());
            enumDTOList.add(enumDTO);
        }
        log.info("系统默认支付方式：{}", JacksonUtils.writeValueAsString(enumDTOList));
        return enumDTOList;
    }

    @Override
    public PaymentTypeDTO getOne(PaymentTypeDTO paymentTypeDTO) {
        PaymentTypeDTO one = paymentTypeMapper.getOne(paymentTypeDTO);
        if (Objects.equals(paymentTypeDTO.getPaymentType(), PaymentType.JH_PAY.getId())) {
            String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
            PaymentInfoDTO jhInfo = enterpriseClientService.getJHInfo(enterpriseGuid, paymentTypeDTO.getStoreDTOS().get(0).getStoreGuid());
            if (null != jhInfo) {
                one.setAppSecretKey(jhInfo.getAppSecret());
                one.setAppId(jhInfo.getAppId());
            }
        }
        return one;
    }

    @Override
    public List<PaymentTypeDTO> getAllTypeName(String storeGuid) {
        return paymentTypeMapper.getAll(storeGuid, null);
    }

    @Override
    public Set<String> getAllStoreNames(List<String> storeGuids) {
        return paymentTypeMapper.getAllStorePayNames(storeGuids);
    }

    @Override
    public String init(String storeGuid, String storeName, String mchntTypeCode) {
        if (MchntTypeEnum.CANTEEN.getCode().equalsIgnoreCase(mchntTypeCode)) {
            //初始化食堂支付
            return initCanteen(storeGuid, storeName);
        } else {
            return initCateringOrRetail(storeGuid, storeName, mchntTypeCode);
        }
    }

    private String initCanteen(String storeGuid, String storeName) {
        Integer integer = paymentTypeMapper.queryDefauktPayTypeExist(storeGuid);
        if (null != integer && integer == 9) {
            throw new BusinessException("该门店已经初始化支付方式");
        }
        //需要初始化的支付方式
        int[] payTypeIds = {0, 1, 2, 3, 4, 11, 12, 14};
        int idLength = payTypeIds.length;
        List<Long> ids = redisIDGenerator.getBatchIds(idLength, "hst_payment_type");
        List<PaymentTypeDO> paymentTypeDOs = new ArrayList<>(idLength);
        //构建父类支付类型
        PaymentTypeDO parentPaymentTypeDO = new PaymentTypeDO();
        parentPaymentTypeDO.setSorting(idLength);
        parentPaymentTypeDO.setSource(1);
        parentPaymentTypeDO.setState(0);
        parentPaymentTypeDO.setPaymentType(PaymentType.CANTEEN_CARD_PAY.getId());
        parentPaymentTypeDO.setPaymentTypeName(PaymentType.CANTEEN_CARD_PAY.getName());
        parentPaymentTypeDO.setPaymentTypeGuid(String.valueOf(redisIDGenerator.getSingle("hst_payment_type")));
        parentPaymentTypeDO.setStoreGuid(storeGuid);
        parentPaymentTypeDO.setStoreName(storeName);

        for (int i = 0; i < idLength; i++) {
            PaymentTypeDO paymentTypeDO = new PaymentTypeDO();
            paymentTypeDO.setPaymentType(payTypeIds[i]);
            paymentTypeDO.setPaymentTypeName(PaymentType.getNameById(payTypeIds[i]));
            paymentTypeDO.setSorting(i);
            paymentTypeDO.setSource(1);
            paymentTypeDO.setState(0);
            if (PaymentType.LUDOU_MEMBER_PAY.getId().equals(paymentTypeDO.getPaymentType())) {
                paymentTypeDO.setState(1);
            }
            if (payTypeIds[i] == 11 || payTypeIds[i] == 12) {
                //设置为直接支付
                paymentTypeDO.setPaymentMode(1);
                //设置父类guid
                paymentTypeDO.setParentPaymentTypeGuid(parentPaymentTypeDO.getPaymentTypeGuid());
            } else {
                paymentTypeDO.setParentPaymentTypeGuid(null);
            }
            paymentTypeDO.setPaymentTypeGuid(String.valueOf(ids.get(i)));
            paymentTypeDO.setStoreGuid(storeGuid);
            paymentTypeDO.setStoreName(storeName);
            paymentTypeDO.setPaymentShunt(0);
            paymentTypeDOs.add(paymentTypeDO);
        }
        paymentTypeDOs.add(parentPaymentTypeDO);
        paymentTypeMapper.addAll(paymentTypeDOs);
        return SUCCESS;
    }

    private String initCateringOrRetail(String storeGuid, String storeName, String mchntTypeCode) {
        Integer integer = paymentTypeMapper.queryDefauktPayTypeExist(storeGuid);
        if (null != integer && integer == 6) {
            throw new BusinessException("该门店已经初始化支付方式");
        }
        List<PaymentTypeDO> paymentTypeDOS = new ArrayList<>(6);
        List<Long> ids = redisIDGenerator.getBatchIds(6, "hst_payment_type");
        for (int i = 0; i <= 4; i++) {
            PaymentTypeDO paymentTypeDO = new PaymentTypeDO();
            paymentTypeDO.setSource(1);
            paymentTypeDO.setState(0);
            setPayMentTypeInfo(i, paymentTypeDO);
            paymentTypeDO.setPaymentTypeGuid(String.valueOf(ids.get(i)));
            paymentTypeDO.setStoreGuid(storeGuid);
            paymentTypeDO.setStoreName(storeName);
            paymentTypeDO.setPaymentShunt(0);
            if (paymentTypeDO.getPaymentType().equals(PaymentType.MEMBER_CARD_PAY.getId())) {
                if (MchntTypeEnum.CATERING.getCode().equalsIgnoreCase(mchntTypeCode)) {
                    paymentTypeDOS.add(paymentTypeDO);
                }
            } else {
                paymentTypeDOS.add(paymentTypeDO);
            }
        }
        paymentTypeMapper.addAll(paymentTypeDOS);
        return SUCCESS;
    }

    private void setPayMentTypeInfo(int i, PaymentTypeDO paymentTypeDO) {
        switch (i) {
            case 0: {
                paymentTypeDO.setPaymentType(PaymentType.CASH_PAY.getId());
                paymentTypeDO.setSorting(0);
                paymentTypeDO.setPaymentTypeName(PaymentType.CASH_PAY.getName());
            }
            break;
            case 1: {
                paymentTypeDO.setPaymentType(PaymentType.JH_PAY.getId());
                paymentTypeDO.setSorting(1);
                paymentTypeDO.setPaymentTypeName(PaymentType.JH_PAY.getName());
            }
            break;
            case 2: {
                paymentTypeDO.setPaymentType(PaymentType.BANK_CARD_PAY.getId());
                paymentTypeDO.setSorting(3);
                paymentTypeDO.setPaymentTypeName(PaymentType.BANK_CARD_PAY.getName());
            }
            break;
            case 3: {
                paymentTypeDO.setPaymentType(PaymentType.MEMBER_CARD_PAY.getId());
                paymentTypeDO.setSorting(2);
                paymentTypeDO.setPaymentTypeName(PaymentType.MEMBER_CARD_PAY.getName());
            }
            break;
            case 4: {
                paymentTypeDO.setPaymentType(PaymentType.FACE_TO_PAY.getId());
                paymentTypeDO.setSorting(4);
                paymentTypeDO.setPaymentTypeName(PaymentType.FACE_TO_PAY.getName());
            }
            break;
            case 5: {
                paymentTypeDO.setPaymentType(PaymentType.DEBT_PAY.getId());
                paymentTypeDO.setSorting(5);
                paymentTypeDO.setPaymentTypeName(PaymentType.DEBT_PAY.getName());
            }
            break;
            case 14: {
                paymentTypeDO.setPaymentType(PaymentType.LUDOU_MEMBER_PAY.getId());
                paymentTypeDO.setSorting(6);
                paymentTypeDO.setState(1);
                paymentTypeDO.setPaymentTypeName(PaymentType.LUDOU_MEMBER_PAY.getName());
            }
            break;
        }
    }

    @Override
    public void add(PaymentTypeDTO paymentTypeDTO) {
        List<StoreDTO> storeDTOS = paymentTypeDTO.getStoreDTOS();
        if (null == storeDTOS || storeDTOS.isEmpty()) {
            throw new BusinessException("新增支付方式，门店guid不能为空！！");
        }
        List<PaymentTypeDO> list = new ArrayList<>();
        List<String> storeGuids = paymentTypeDTO.getStoreDTOS().stream().map(StoreDTO::getStoreGuid).collect(Collectors.toList());
        //默认支付方式 验证是否已存在
        if (paymentTypeDTO.getSource() == 1) {
            List<PaymentTypeDO> paymentTypeDOList = list(new LambdaQueryWrapper<PaymentTypeDO>().in(PaymentTypeDO::getStoreGuid, storeGuids).eq(PaymentTypeDO::getPaymentType, paymentTypeDTO.getPaymentType()));
            if (!CollectionUtils.isEmpty(paymentTypeDOList)) {
                StringBuilder message = new StringBuilder();
                for (int i = 0; i < paymentTypeDOList.size(); i++) {
                    PaymentTypeDO paymentTypeDO = paymentTypeDOList.get(i);
                    message.append(paymentTypeDO.getStoreName());
                    if (i != paymentTypeDOList.size() - 1) {
                        message.append("、");
                    }
                }
                message.append("！已存在：【").append(paymentTypeDTO.getPaymentTypeName()).append("】支付方式！");
                throw new BusinessException(message.toString());
            }
        } else {
            int count = paymentTypeMapper.countByStoreGuidInAndName(storeGuids, paymentTypeDTO.getPaymentTypeName(), null);
            Assert.isTrue(count == 0, "支付方式名称重复");
        }
        List<Long> ids = redisIDGenerator.getBatchIds(storeDTOS.size(), "hst_payment_type");
        for (int i = 0; i < storeDTOS.size(); i++) {
            StoreDTO storeDTO = storeDTOS.get(i);
            PaymentTypeDO paymentTypeDO = PAYMENT_TYPE_MAPSTRUCT.paymentTypeDtoToDo(paymentTypeDTO);
            String storeGuid = storeDTO.getStoreGuid();
            PaymentTypeDO maxSortingAndMaxPaymentType = paymentTypeMapper.getMaxSortingAndMaxPaymentType(storeGuid);
            int maxSorting = maxSortingAndMaxPaymentType.getSorting();
            paymentTypeDO.setSorting(++maxSorting);
            String uuidStr = String.valueOf(ids.get(i));
            paymentTypeDO.setPaymentTypeGuid(uuidStr);
            paymentTypeDO.setPaymentTypeName(paymentTypeDTO.getPaymentTypeName());
            paymentTypeDO.setState(0);
            paymentTypeDO.setStoreGuid(storeGuid);
            paymentTypeDO.setStoreName(storeDTO.getStoreName());
            //默认支付方式 以前端传为准
            if (paymentTypeDTO.getSource() == 1) {
                paymentTypeDO.setPaymentType(paymentTypeDTO.getPaymentType());
            } else {
                int paymentTypeBySorting = USER_DEFINED_PAYMENT_TYPE_START + paymentTypeDO.getSorting();
                int paymentTypeByPaymentType = maxSortingAndMaxPaymentType.getPaymentType() + 1;
                int sorting = Math.max(paymentTypeBySorting, paymentTypeByPaymentType);
                paymentTypeDO.setPaymentType(sorting);
            }
            paymentTypeDO.setSource(paymentTypeDTO.getSource());
            list.add(paymentTypeDO);
            redisService.deletePaymentType(storeGuid);
        }
        saveOrUpdateBatch(list);
    }

    @Override
    public void update(PaymentTypeDTO paymentTypeDTO) {
        PaymentTypeDO paymentTypeDO = PAYMENT_TYPE_MAPSTRUCT.paymentTypeDtoToDo(paymentTypeDTO);
        String storeGuid = paymentTypeDTO.getStoreDTOS().get(0).getStoreGuid();
        paymentTypeDO.setStoreGuid(storeGuid);
        // 校验 支付方式修改
        verifyUpdatePaymentType(paymentTypeDTO);
        paymentTypeMapper.update(paymentTypeDO);
        redisService.deletePaymentType(storeGuid);
    }

    /**
     * 校验 支付方式修改
     */
    private void verifyUpdatePaymentType(PaymentTypeDTO paymentTypeDTO) {
        String storeGuid = paymentTypeDTO.getStoreDTOS().get(0).getStoreGuid();
        int count = paymentTypeMapper.countByStoreGuidInAndName(
                Arrays.asList(storeGuid),
                paymentTypeDTO.getPaymentTypeName(),
                paymentTypeDTO.getPaymentTypeGuid()
        );
        Assert.isTrue(count == 0, "支付方式名称重复");
        if (Objects.equals(paymentTypeDTO.getPaymentType(), PaymentType.LUDOU_MEMBER_PAY.getId())
                && paymentTypeDTO.getState() == 0) {
            // 查询企业是否支持麓豆支付
            EnterpriseQueryDTO queryDTO = new EnterpriseQueryDTO();
            queryDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            queryDTO.setNoCacheFlag(true);
            EnterpriseDTO enterprise = enterpriseClientService.findEnterprise(queryDTO);
            if (Objects.isNull(enterprise) || !Boolean.TRUE.equals(enterprise.getSupportLudouPayFlag())) {
                throw new BusinessException("该企业暂不支持使用麓豆支付");
            }
        }
    }

    @Override
    public void delete(String storeGuid, String paymentTypeGuid) {
        paymentTypeMapper.delete(storeGuid, paymentTypeGuid);
        redisService.deletePaymentType(storeGuid);
    }

    @Override
    public List<PaymentTypeDTO> getAllByAndroid(PaymentTypeQueryDTO paymentTypeQueryDTO) {
        String storeGuid = paymentTypeQueryDTO.getStoreGuid();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        if (StringUtils.isEmpty(enterpriseGuid)) {
            throw new BusinessException("企业guid为空");
        }
        log.info("安卓查询支付方式入参：storeGuid={}，source={}", storeGuid, paymentTypeQueryDTO.getSource());
        List<PaymentTypeDTO> all = redisService.getPaymentType(storeGuid);
        log.info("all={}", all);
        if (null == all) {
            all = paymentTypeMapper.getAll(storeGuid, paymentTypeQueryDTO.getSource());
            redisService.putPaymentType(storeGuid, all);
        }
        return all;
    }

    @Override
    public List<PaymentTypeDTO> getAll(PaymentTypeQueryDTO paymentTypeQueryDTO) {
        String storeGuid = paymentTypeQueryDTO.getStoreGuid();
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        if (StringUtils.isEmpty(enterpriseGuid)) {
            throw new BusinessException("企业guid为空");
        }
        List<PaymentTypeDTO> all = paymentTypeMapper.getAll(storeGuid, paymentTypeQueryDTO.getSource());
        // 添加麓豆支付方式
        appendLudouMemberPayType(all);
        // 排序
        all = all.stream()
                .sorted(Comparator.comparing(PaymentTypeDTO::getSorting)
                        .thenComparing(PaymentTypeDTO::getPaymentTypeGuid))
                .collect(Collectors.toList());
        List<PaymentInfoDTO> jhPayInfoList = getPaymentInfoListByRedis(storeGuid);
        log.info("redis jhPayInfoList={}", JacksonUtils.writeValueAsString(jhPayInfoList));
        if (CollectionUtils.isEmpty(jhPayInfoList)) {
            jhPayInfoList = enterpriseClientService.listPaymentInfo(enterpriseGuid, storeGuid);
            log.info("cloud jhPayInfoList={}", JacksonUtils.writeValueAsString(jhPayInfoList));
        }
        if (!CollectionUtils.isEmpty(jhPayInfoList)) {
            jhPayInfoList.forEach(jhPay -> {
                List<Integer> diversionRule = new ArrayList<>();
                if (StringUtils.hasText(jhPay.getDiversionRules())) {
                    Arrays.asList(jhPay.getDiversionRules().replace(" ", "").split(",")).forEach(jh -> {
                        diversionRule.add(Integer.valueOf(jh));
                    });
                }
                jhPay.setDiversionRule(diversionRule);
            });
        }
        for (PaymentTypeDTO paymentTypeDTO : all) {
            if (Objects.equals(paymentTypeDTO.getPaymentType(), PaymentType.JH_PAY.getId())) {
                paymentTypeDTO.setJhPayInfoList(jhPayInfoList);
            }
        }
        return all;
    }

    /**
     * 添加麓豆支付方式
     */
    private void appendLudouMemberPayType(List<PaymentTypeDTO> all) {
        if (CollectionUtils.isEmpty(all)) {
            log.error("支付方式列表为空, userContext:{}", UserContextUtils.get());
            return;
        }
        PaymentTypeDTO ludouMemberPayType = all.stream()
                .filter(e -> PaymentType.LUDOU_MEMBER_PAY.getId().equals(e.getPaymentType()))
                .findFirst()
                .orElse(null);
        if (Objects.nonNull(ludouMemberPayType)) {
            return;
        }
        PaymentTypeDTO firstPaymentTypeDTO = all.get(0);
        String storeGuid = firstPaymentTypeDTO.getStoreGuid();

        String lockKey = "init:ludouMemberPayType:%s";
        boolean lockSuccess = false;
        try {
            lockSuccess = setNxEx(String.format(lockKey, storeGuid), "1", 30);
            if (!lockSuccess) {
                log.warn("麓豆支付初始化中, storeGuid:{}", storeGuid);
                return;
            }
            PaymentTypeDTO paymentTypeInfo = paymentTypeMapper.getPaymentTypeInfo(storeGuid, PaymentType.LUDOU_MEMBER_PAY.getId());
            if (Objects.nonNull(paymentTypeInfo)) {
                return;
            }
            Integer maxSort = all.stream()
                    .filter(e -> e.getSource() == 1)
                    .map(PaymentTypeDTO::getSorting)
                    .max(Comparator.comparing(e -> e)).orElse(all.get(all.size() - 1).getSorting());
            // 初始化麓豆支付方式
            PaymentTypeDO ludouPaymentTypeDO = new PaymentTypeDO();
            ludouPaymentTypeDO.setPaymentType(PaymentType.LUDOU_MEMBER_PAY.getId());
            ludouPaymentTypeDO.setPaymentTypeName(PaymentType.LUDOU_MEMBER_PAY.getName());
            ludouPaymentTypeDO.setSorting(maxSort);
            ludouPaymentTypeDO.setSource(1);
            ludouPaymentTypeDO.setState(1);
            ludouPaymentTypeDO.setParentPaymentTypeGuid(null);
            ludouPaymentTypeDO.setPaymentTypeGuid(String.valueOf(redisIDGenerator.getSingle("hst_payment_type")));
            ludouPaymentTypeDO.setStoreGuid(firstPaymentTypeDTO.getStoreGuid());
            ludouPaymentTypeDO.setStoreName(firstPaymentTypeDTO.getStoreName());
            ludouPaymentTypeDO.setPaymentShunt(0);
            save(ludouPaymentTypeDO);

            PaymentTypeDTO paymentTypeDTO = new PaymentTypeDTO();
            BeanUtils.copyProperties(ludouPaymentTypeDO, paymentTypeDTO);
            all.add(paymentTypeDTO);
        } finally {
            if (lockSuccess) {
                redisTemplate.delete(lockKey);
            }
        }
    }


    /**
     * 分布式锁
     *
     * @param key        唯一key
     * @param value      值
     * @param expireTime 锁时间 单位 秒
     * @return true、false
     */
    private boolean setNxEx(String key, String value, long expireTime) {
        try {
            return (Boolean) this.redisTemplate.execute((RedisCallback) (connection) -> {
                Object obj = connection.execute("set", new byte[][]{key.getBytes(),
                        value.getBytes(), "NX".getBytes(), "EX".getBytes(), Protocol.toByteArray(expireTime)});
                return obj != null;
            });
        } catch (Exception e) {
            log.error("redis连接异常", e);
            throw new BusinessException("系统繁忙，请稍后再试！");
        }
    }

    @Override
    public List<PaymentTypeBatchDTO> getAllByStoreGuidList(PaymentTypeBatchQureyDTO paymentTypeBatchQureyDTO) {
        List<PaymentTypeBatchDTO> paymentTypeBatchDTOS = Lists.newArrayList();
        paymentTypeBatchQureyDTO.getPaymentTypeQueryDTOList().forEach(paymentTypeQueryDTO -> {
            List<PaymentTypeDTO> paymentTypeDTOS = getAll(paymentTypeQueryDTO);
            PaymentTypeBatchDTO paymentTypeBatchDTO = new PaymentTypeBatchDTO();
            paymentTypeBatchDTO.setStoreGuid(paymentTypeQueryDTO.getStoreGuid());
            paymentTypeBatchDTO.setPaymentTypeDTOList(paymentTypeDTOS);
            paymentTypeBatchDTOS.add(paymentTypeBatchDTO);
        });
        return paymentTypeBatchDTOS;
    }

    @Override
    public String sort(List<PaymentTypeDTO> paymentTypeDTOS) {
        paymentTypeMapper.updateAllSort(paymentTypeDTOS);
        Set<String> storeGuidList = paymentTypeDTOS.stream().map(PaymentTypeDTO::getStoreGuid).collect(Collectors.toSet());
        redisService.deletePaymentType(storeGuidList);
        return SUCCESS;
    }

    @Override
    public Boolean editPaymentTypeMode(PaymentTypeModeDTO modeDTO) {
        PaymentTypeDTO dto = new PaymentTypeDTO();
        dto.setPaymentTypeGuid(modeDTO.getPaymentTypeGuid());
        dto.setStoreGuid(modeDTO.getStoreGuid());
        PaymentTypeDTO one = paymentTypeMapper.getOne(dto);
        if (one == null) {
            throw new BusinessException("门店付款方式不存在");
        }
        try {
            modeDTO.setPaymentMode(Objects.isNull(modeDTO.getPaymentMode()) ? 0 : modeDTO.getPaymentMode());
            modeDTO.setPaymentMultiCard(Objects.isNull(modeDTO.getPaymentMultiCard()) ? 0 : modeDTO.getPaymentMultiCard());
            //更新付款方式的支付模式
            paymentTypeMapper.updatePaymentTypeMode(modeDTO);
            redisService.deletePaymentType(modeDTO.getStoreGuid());
        } catch (Exception e) {
            log.error("【更新付款方式的支付模式失败，{}】", e.toString());
            throw new BusinessException("更新付款方式的支付模式失败");
        }
        return true;
    }

    /**
     * 聚合支付账户设置
     *
     * @param request 入参
     * @return boolean
     */
    @Override
    public Boolean paymentAccountSave(PaymentAccountReqDTO request) {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        if (StringUtils.isEmpty(enterpriseGuid)) {
            throw new BusinessException("头部企业guid不能为空");
        }
        String storeGuid = request.getStoreGuid();
        if (StringUtils.isEmpty(storeGuid)) {
            throw new BusinessException("门店guid为空");
        }
        String paymentTypeGuid = request.getPaymentTypeGuid();
        if (StringUtils.isEmpty(paymentTypeGuid)) {
            throw new BusinessException("支付方式guid为空");
        }
        List<PaymentInfoDTO> jhPayInfoList = request.getJhPayInfoList();
        if (CollectionUtils.isEmpty(jhPayInfoList)) {
            throw new BusinessException("至少需要保存一个支付账户");
        }
        if (jhPayInfoList.size() > 10) {
            throw new BusinessException("最多可添加10个账户");
        }
        if (ObjectUtils.isEmpty(request.getPaymentShunt())) {
            throw new BusinessException("收款分流开关不能为空");
        }
        boolean isShunt = 1 == request.getPaymentShunt();
        if (isShunt) {
            List<PaymentInfoDTO> defaultAccountList = jhPayInfoList.stream()
                    .filter(pay -> 1 == pay.getIsDefaultAccount())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(defaultAccountList)) {
                throw new BusinessException("请设置一个默认账户");
            }
            if (defaultAccountList.size() > 1) {
                throw new BusinessException("只能设置一个默认账户");
            }
        }
        long accountCount = jhPayInfoList.stream().map(PaymentInfoDTO::getAccountName).distinct().count();
        if (isShunt && accountCount != jhPayInfoList.size()) {
            throw new BusinessException("账户名称不可重复");
        }
        long idCount = jhPayInfoList.stream().map(PaymentInfoDTO::getAppId).distinct().count();
        if (isShunt && idCount != jhPayInfoList.size()) {
            throw new BusinessException("账户不可重复");
        }

        StringBuilder illegal = new StringBuilder();
        StringBuilder sameName = new StringBuilder();
        List<String> paymentGuidList = jhPayInfoList.stream()
                .map(PaymentInfoDTO::getPaymentInfoGuid)
                .filter(guid -> !StringUtils.isEmpty(guid))
                .distinct()
                .collect(Collectors.toList());
        List<String> toDeleteList = request.getToDeleteList();
        for (PaymentInfoDTO jhPay : jhPayInfoList) {
            jhPay.setEnterpriseGuid(enterpriseGuid);
            jhPay.setStoreGuid(storeGuid);
            jhPay.setDiversionRules(jhPay.getDiversionRule().toString().replace("[", "")
                    .replace("]", ""));

            // 调用聚合支付确认appId是否合法
            MchntValidateDTO validateDTO = new MchntValidateDTO();
            validateDTO.setAppId(jhPay.getAppId());
            validateDTO.setAppSecretKey(jhPay.getAppSecret());
            Boolean flag = jhPayClientService.check(validateDTO);
            if (!flag) {
                log.warn("appId不合法 validateDTO={}", JacksonUtils.writeValueAsString(validateDTO));
                illegal.append(jhPay.getAccountName()).append("、");
            }

            // 分流才做重名校验，查询聚合支付账户名称是否存在
            if (isShunt) {
                List<PaymentInfoDTO> paymentInfoDTOList = enterpriseClientService.existSameAccountName(enterpriseGuid,
                        storeGuid, jhPay.getAccountName());
                if (!CollectionUtils.isEmpty(paymentInfoDTOList)) {
                    paymentInfoDTOList.removeIf(p -> paymentGuidList.contains(p.getPaymentInfoGuid()));
                    if (!CollectionUtils.isEmpty(toDeleteList)) {
                        paymentInfoDTOList.removeIf(p -> toDeleteList.contains(p.getPaymentInfoGuid()));
                    }
                }
                boolean same = !CollectionUtils.isEmpty(paymentInfoDTOList);
                if (same) {
                    log.warn("聚合支付账户名称存在 enterpriseGuid={} storeGuid={} accountName={}", enterpriseGuid, storeGuid,
                            jhPay.getAccountName());
                    sameName.append(jhPay.getAccountName()).append("、");
                }
            }
        }
        if (StringUtils.hasText(illegal)) {
            String str = illegal.substring(0, illegal.length() - 1);
            throw new BusinessException(str + "支付商户号或key错误，无法保存");
        }
        if (StringUtils.hasText(sameName)) {
            String str = sameName.substring(0, sameName.length() - 1);
            throw new BusinessException(str + "账户名称重复，无法保存");
        }

        // 修改收款分流状态
        PaymentTypeDO paymentTypeDO = paymentTypeMapper.selectOne(new LambdaQueryWrapper<PaymentTypeDO>()
                .eq(PaymentTypeDO::getPaymentTypeGuid, paymentTypeGuid));
        if (ObjectUtils.isEmpty(paymentTypeDO)) {
            log.warn("未查询到支付方式 paymentTypeGuid={}", paymentTypeGuid);
            throw new BusinessException("未查询到支付方式");
        }

        // 调用云平台修改聚合支付账户
        if (!CollectionUtils.isEmpty(toDeleteList)) {
            // 先删除再更新
            enterpriseClientService.deletePayInfoByGuidList(toDeleteList);
        }
        enterpriseClientService.batchSaveOrUpdatePaymentInfoList(jhPayInfoList);

        // 更新redis数据
        List<PaymentInfoDTO> paymentInfoList = enterpriseClientService.listPaymentInfo(enterpriseGuid, storeGuid);
        log.info("cloud paymentInfoList={}", JacksonUtils.writeValueAsString(paymentInfoList));
        redisService.putJHPayInfoList(paymentInfoList, storeGuid);

        paymentTypeDO.setPaymentShunt(request.getPaymentShunt());
        return this.updateById(paymentTypeDO);
    }

    /**
     * 获取聚合支付方式信息
     *
     * @param storeGuid 门店guid
     * @return 聚合支付方式信息
     */
    @Override
    public PaymentTypeDTO getJhPaymentTypeInfo(String storeGuid) {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        if (StringUtils.isEmpty(storeGuid)) {
            throw new BusinessException("门店guid为空");
        }
        if (StringUtils.isEmpty(enterpriseGuid)) {
            throw new BusinessException("企业guid为空");
        }
        PaymentTypeDTO paymentTypeInfo = paymentTypeMapper.getPaymentTypeInfo(storeGuid, PaymentType.JH_PAY.getId());
        List<PaymentInfoDTO> jhPayInfoList = getPaymentInfoListByRedis(storeGuid);
        log.info("redis jhPayInfoList={}", JacksonUtils.writeValueAsString(jhPayInfoList));
        if (CollectionUtils.isEmpty(jhPayInfoList)) {
            jhPayInfoList = enterpriseClientService.listPaymentInfo(enterpriseGuid, storeGuid);
            log.info("cloud jhPayInfoList={}", JacksonUtils.writeValueAsString(jhPayInfoList));
        }
        if (!CollectionUtils.isEmpty(jhPayInfoList)) {
            jhPayInfoList.forEach(jhPay -> {
                List<Integer> diversionRule = new ArrayList<>();
                if (StringUtils.hasText(jhPay.getDiversionRules())) {
                    Arrays.asList(jhPay.getDiversionRules().replace(" ", "").split(",")).forEach(jh -> {
                        diversionRule.add(Integer.valueOf(jh));
                    });
                }
                jhPay.setDiversionRule(diversionRule);
            });
        }
        paymentTypeInfo.setJhPayInfoList(jhPayInfoList);
        return paymentTypeInfo;
    }

    /**
     * 从redis里获取聚合支付账户的信息
     * 由于分流功能上线可能导致redis里的数据过期，会解析失败，所以特殊处理
     *
     * @param storeGuid 门店guid
     * @return 聚合支付账户的信息
     */
    private List<PaymentInfoDTO> getPaymentInfoListByRedis(String storeGuid) {
        List<PaymentInfoDTO> jhPayInfoList = new ArrayList<>();
        try {
            jhPayInfoList = redisService.getJHPayInfoList(storeGuid);
        } catch (Exception e) {
            log.warn("查询聚合支付账户信息异常 e={}", e.getMessage());
            redisService.deleteJHPayInfo(storeGuid);
        }
        return jhPayInfoList;
    }
}
