package com.holderzone.saas.store.business.queue.service.remote;

import com.holderzone.saas.store.dto.table.OpenTableDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessMsgClient
 * @date 2019/01/07 15:18
 * @description
 * @program holder-saas-store-table
 */
@Component
@FeignClient(value = "holder-saas-store-table", fallbackFactory = TableClientService.TableClientFallBack.class)
public interface TableClientService {

    @ApiOperation(value = "创建订单", notes = "创建订单")
    @PostMapping("/table/open")
    String openTable(@RequestBody OpenTableDTO openTableDTO);

    @PostMapping("/table/web/query")
    List<TableBasicDTO> listByWeb(@RequestBody TableBasicQueryDTO tableBasicQueryDTO);

    @Slf4j
    @Component
    class TableClientFallBack implements FallbackFactory<TableClientService> {
        @Override
        public TableClientService create(Throwable throwable) {
            return new TableClientService() {
                @Override
                public String openTable(OpenTableDTO openTableDTO) {
                    log.error("创建订单异常 e={}", throwable);
                    throw new RuntimeException("开台失败,e:{}" + throwable.getMessage());
                }

                @Override
                public List<TableBasicDTO> listByWeb(TableBasicQueryDTO tableBasicQueryDTO) {
                    log.error("查询所有桌台异常 e={}", throwable);
                    throw new RuntimeException("查询所有桌台失败,e:{}" + throwable.getMessage());
                }
            };


        }
    }

}
