package com.holderzone.saas.store.business.queue.service.remote;

import com.holderzone.saas.store.dto.queue.ItemGuidDTO;
import com.holderzone.saas.store.dto.weixin.req.WxQueueInfoReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintClient
 * @date 2019/02/15 16:58
 * @description
 * @program holder-saas-store-table
 */
@Component
@FeignClient(value = "holder-saas-store-weixin", fallbackFactory = WxClient.ClientFallbackFactory.class)
public interface WxClient {

    @PostMapping("/wx_queue/get_queue_qr_code")
    String getQueueQrCode(@RequestBody WxQueueInfoReqDTO wxQueueInfoReqDTO);

    @PostMapping("/wx_queue/call_up_notify")
    void callUpNotify(@RequestBody List<ItemGuidDTO> itemGuidDTO);

    @Slf4j
    @Component
    class ClientFallbackFactory implements FallbackFactory<WxClient> {

        @Override
        public WxClient create(Throwable throwable) {
            return new WxClient() {
                @Override
                public String getQueueQrCode(WxQueueInfoReqDTO wxQueueInfoReqDTO) {
                    log.error("获取二维码错误 e={}", throwable);
                    throw new RuntimeException(throwable);
                }

                @Override
                public void callUpNotify(List<ItemGuidDTO> itemGuidDTO) {
                    log.error("微信通知失败 e={}", throwable);
                    throw new RuntimeException(throwable);
                }
            };

        }

    }

}
