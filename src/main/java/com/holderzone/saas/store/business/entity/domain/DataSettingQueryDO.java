package com.holderzone.saas.store.business.entity.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/8
 * @since 1.8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DataSettingQueryDO {

    private String brandGuid;

    private List<Integer> dataSettingTypeList;
}
