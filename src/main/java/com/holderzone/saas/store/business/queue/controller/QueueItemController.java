package com.holderzone.saas.store.business.queue.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.resource.common.dto.validate.Add;
import com.holderzone.saas.store.business.queue.service.QueueItemService;
import com.holderzone.saas.store.dto.queue.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueItemController
 * @date 2019/03/27 17:06
 * @description //TODO
 * @program holder-saas-store-queue
 */
@RestController
@Api("排队操作")
@Slf4j
public class QueueItemController {

    private QueueItemService queueItemService;

    @Autowired
    public QueueItemController(QueueItemService queueItemService) {
        this.queueItemService = queueItemService;
    }

    @PostMapping("/queue/item/inQueue")
    @ApiOperation("取号")
    public HolderQueueItemDetailDTO inQueue(@Validated(Add.class) @RequestBody HolderQueueItemDTO dto) {
        return queueItemService.inQueue(dto);
    }

    @PostMapping("/queue/item/call")
    @ApiOperation("叫号")
    public HolderQueueItemDetailDTO call(@RequestBody ItemGuidDTO dto) {
        return queueItemService.call(dto);
    }

    @PostMapping("/queue/item/pass")
    @ApiOperation("过号")
    public HolderQueueItemDetailDTO pass(@RequestBody ItemGuidDTO dto) {
        return queueItemService.pass(dto);
    }

    @PostMapping("/queue/item/confirm")
    @ApiOperation("就餐")
    public HolderQueueItemDetailDTO confirm(@RequestBody ItemGuidDTO dto) {
        return queueItemService.confirm(dto);
    }

    @PostMapping("/queue/item/confirmAndTable")
    @ApiOperation("就餐并开台")
    public TableQueueItemDetailDTO confirmAndTable(@RequestBody TableConfirmDTO dto) {
        return queueItemService.confirmAndTable(dto);
    }

    @PostMapping("/queue/item/recover")
    @ApiOperation("撤销")
    public HolderQueueItemDetailDTO recover(@RequestBody ItemGuidDTO dto) {
        return queueItemService.recover(dto);
    }

    @PostMapping("/queue/item/obtain")
    @ApiOperation("查询指定的排队记录")
    public HolderQueueQueueRecordDTO obtain(@RequestBody ItemGuidDTO dto) {
        return queueItemService.obtain(dto);
    }

    @PostMapping("/queue/records")
    @ApiOperation("历史记录")
    public List<HolderQueueItemDetailDTO> records() {
        return queueItemService.listQueueItemDetails();
    }

    @PostMapping("/queue/records/page")
    @ApiOperation("分页历史记录")
    public Page<HolderQueueItemDetailDTO> page(@RequestBody Page page) {
        return queueItemService.page(page);
    }

    //查询当前用户在所有门店下是否有排队详情信息
    @PostMapping("/queue/queryByUser")
    @ApiOperation("根据用户查询排队信息")
    public List<WxQueueListDTO> queryByUser(@RequestBody @Valid QueueWechatDTO queueWechatDTO) {
        log.info("微信端查询用户排队门店信息列表：入参{}", JacksonUtils.writeValueAsString(queueWechatDTO));
        return queueItemService.queryByUser(queueWechatDTO);
    }

    @GetMapping("/queue/cancel")
    @ApiOperation("取消排队")
    public Boolean cancel(@RequestParam("queueGuid") String queueGuid, @RequestParam("enterpriseGuid") String enterpriseGuid) {
        return queueItemService.cancel(queueGuid, enterpriseGuid);
    }

    @PostMapping("/queue/getQueueDetail")
    @ApiOperation("查询排队详情")
    public HolderQueueQueueRecordDTO getQueueDetails(@RequestBody ItemGuidDTO itemGuidDTO){
        log.info("查询排队详情：入参{}",itemGuidDTO);
        return queueItemService.getQueueDetail(itemGuidDTO);
    }

}