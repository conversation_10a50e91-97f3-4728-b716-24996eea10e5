package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AccountRecordDetectedException extends BusinessException {

    private static final long serialVersionUID = 8761925734699224954L;

    public AccountRecordDetectedException() {
        super("检测到营业日，无法操作");
    }

    public AccountRecordDetectedException(String message) {
        super("检测到营业日["+message+"]，无法操作");
    }

    public AccountRecordDetectedException(String message, Throwable cause) {
        super(message, cause);
    }
}
