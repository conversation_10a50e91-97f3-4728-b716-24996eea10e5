package com.holderzone.saas.store.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.business.config.RedisIDGenerator;
import com.holderzone.saas.store.business.entity.domain.ReasonDO;
import com.holderzone.saas.store.business.enums.ReasonTypeEnum;
import com.holderzone.saas.store.business.mapper.ReasonMapper;
import com.holderzone.saas.store.business.mapstruct.ReasonMapstruct;
import com.holderzone.saas.store.business.service.ReasonService;
import com.holderzone.saas.store.dto.business.reason.ReasonCopyReqDTO;
import com.holderzone.saas.store.dto.business.reason.ReasonDTO;
import com.holderzone.saas.store.dto.business.reason.ReasonTypeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.holderzone.saas.store.business.constant.Constant.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReasonMapper
 * @date 2019/08/15 15:05
 * @description
 * @program holder-saas-store-business
 */
@Service
@Slf4j
public class ReasonServiceImpl extends ServiceImpl<ReasonMapper, ReasonDO> implements ReasonService {


    private final ReasonMapper reasonMapper;
    private final RedisIDGenerator redisIDGenerator;

    @Autowired
    public ReasonServiceImpl(ReasonMapper reasonMapper, RedisIDGenerator redisIDGenerator) {
        this.reasonMapper = reasonMapper;
        this.redisIDGenerator = redisIDGenerator;
    }

    @Override
    public List<ReasonDTO> findReason(ReasonDTO reasonDTO) {
        List<ReasonDTO> reason = reasonMapper.findReason(reasonDTO);
        //根据MySQL中查出来的reasonTypeCode从枚举类中查出reasonType并映射到ReasonDTO类中
        reason.stream().forEach(t -> t.setReasonType(ReasonTypeEnum.queryReasonTypeByCode(t.getReasonTypeCode())));
        log.info("查询出的原因集合为{}", reason);
        return reason;
    }

    @Override
    public void insertReason(ReasonDTO reasonDTO) {
        //查询数据库中每个门店下每个原因类型中的原因数量
        int count = count(new LambdaQueryWrapper<ReasonDO>()
                .eq(ReasonDO::getStoreGuid, reasonDTO.getStoreGuid())
                .eq(ReasonDO::getReasonTypeCode, reasonDTO.getReasonTypeCode()));
        log.info("查出来的已存在的原因数量为:{}", count);
        //设置允许存在的最大数量
        if (count >= 10) {
            throw new BusinessException("同一类型下最多创建10个原因");
        }

        //重名校验
        check(reasonDTO);

        ReasonDO reasonDO = ReasonMapstruct.INSTANCE.reasonDto2ReasonDo(reasonDTO);
        reasonDO.setReasonGuid(String.valueOf(redisIDGenerator.getSingle("hsb_reason_reason")));
        reasonDO.setReasonCreate(LocalDateTime.now());
        if (!save(reasonDO)) {
            throw new BusinessException("新增'原因列表'失败");
        }
    }

    @Override
    public void deleteReason(ReasonDTO reasonDTO) {
        //根据guid删除
        remove(new LambdaQueryWrapper<ReasonDO>().eq(ReasonDO::getReasonGuid, reasonDTO.getReasonGuid()));
    }

    @Override
    public void updateReason(ReasonDTO reasonDTO) {
        //重名校验
        check(reasonDTO);
        ReasonDO reasonDO = ReasonMapstruct.INSTANCE.reasonDto2ReasonDo(reasonDTO);
        reasonDO.setReasonCreate(LocalDateTime.now());
        boolean update = update(reasonDO, new LambdaQueryWrapper<ReasonDO>().eq(ReasonDO::getReasonGuid, reasonDO.getReasonGuid()));
        if (!update) {
            throw new BusinessException("修改'原因列表'失败");
        }
    }

    /**
     * 检查传入的数据是否重名
     *
     * @param reasonDTO
     */
    private void check(ReasonDTO reasonDTO) {
        int dupNameCont = count(new LambdaQueryWrapper<ReasonDO>()
                .eq(ReasonDO::getStoreGuid, reasonDTO.getStoreGuid())
                .eq(ReasonDO::getReasonTypeCode, reasonDTO.getReasonTypeCode())
                .eq(ReasonDO::getReason, reasonDTO.getReason())
        );
        if (dupNameCont > 0) {
            throw new BusinessException("禁止重名");
        }
    }

    @Override
    public void updateCount(List<String> guids) {
        reasonMapper.updateCount(guids);

    }

    @Override
    public List<ReasonTypeDTO> findReasonType() {
        return ReasonTypeEnum.queryAllReasonType()
                .stream()
                .map(r ->
                        ReasonTypeDTO.builder()
                                .reasonType(r.getDes())
                                .reasonTypeCode(r.getCode())
                                .build()
                ).collect(Collectors.toList());
    }

    @Override
    public List<ReasonTypeDTO> findReasonTypeSuperMarket() {
        List<ReasonTypeDTO> list = new ArrayList<>();
        List<ReasonTypeEnum> reasonTypes = ReasonTypeEnum.querySuperMarketReasonType();
        for (ReasonTypeEnum reasonType : reasonTypes) {
            ReasonTypeDTO r = ReasonTypeDTO.builder().reasonType(reasonType.getDes()).reasonTypeCode(reasonType.getCode()).build();
            list.add(r);
        }
        return list;
    }

    /**
     * 复制'原因列表'
     *
     * @param copyReqDTO 原因实体（类别code,原因内容）,门店列表
     * @return Boolean
     */
    @Override
    public Boolean copyReason(ReasonCopyReqDTO copyReqDTO) {
        List<String> storeGuidList = copyReqDTO.getStoreGuidList();
        if (CollectionUtils.isEmpty(storeGuidList)) {
            throw new BusinessException(STORE_CANNOT_BE_NULL);
        }

        List<ReasonDTO> reasonDTOList = copyReqDTO.getReasonDTOList();
        checkRequest(reasonDTOList);

        //要复制的原因
        List<String> reasonList = reasonDTOList.stream()
                .map(ReasonDTO::getReason)
                .distinct()
                .collect(Collectors.toList());

        int version = 0;

        //循环每个门店下每条原因，并保存，没有数量限制
        for (String storeGuid : storeGuidList) {
            for (ReasonDTO reasonDTO : reasonDTOList) {
                //只有第一次需要去校验
                if (version == 0) {
                    checkSave(reasonList, storeGuid, reasonDTO);
                }
                version++;
                if (checkName(storeGuid, reasonDTO)) {
                    continue;
                }
                ReasonDO reasonDO = getReasonDO(storeGuid, reasonDTO);
                if (!save(reasonDO)) {
                    throw new BusinessException("复制'原因列表：" + reasonDO.getReason() + "'失败");
                }
            }
        }
        return Boolean.TRUE;
    }

    private ReasonDO getReasonDO(String storeGuid, ReasonDTO reasonDTO) {
        ReasonDO reasonDO = ReasonMapstruct.INSTANCE.reasonDto2ReasonDo(reasonDTO);
        reasonDO.setReasonGuid(
                String.valueOf(redisIDGenerator.getSingle("hsb_reason_reason"))
        );
        reasonDO.setStoreGuid(storeGuid);
        reasonDO.setReasonCreate(LocalDateTime.now());
        return reasonDO;
    }

    private boolean checkName(String storeGuid, ReasonDTO reasonDTO) {
        int reasonCount = count(new LambdaQueryWrapper<ReasonDO>()
                .eq(ReasonDO::getStoreGuid, storeGuid)
                .eq(ReasonDO::getReasonTypeCode, reasonDTO.getReasonTypeCode())
                .eq(ReasonDO::getReason, reasonDTO.getReason())
        );
        if (reasonCount > 0) {
            log.error("名字重复，跳过该数据");
            return true;
        }
        return false;
    }

    private void checkSave(List<String> reasonList, String storeGuid, ReasonDTO reasonDTO) {
        //查询数据库中每个门店下每个原因类型中的原因
        List<ReasonDO> list = list(new LambdaQueryWrapper<ReasonDO>()
                .eq(ReasonDO::getStoreGuid, storeGuid)
                .eq(ReasonDO::getReasonTypeCode, reasonDTO.getReasonTypeCode()));
        int size = list.size();
        log.info("查出来的已存在的原因数量为:{}", size);

        //数据库中的原因
        List<String> reasonList2 = list.stream()
                .map(ReasonDO::getReason)
                .distinct()
                .collect(Collectors.toList());

        //有效数量，排除同名原因
        List<String> collect = new ArrayList<>();
        for (String re : reasonList) {
            if (!reasonList2.contains(re)) {
                collect.add(re);
            }
        }
        int effectiveQuantity = collect.size();

        log.info("要复制原因的有效数量为:{}", effectiveQuantity);

        //设置允许存在的最大数量
        if (size >= 10 || (size + effectiveQuantity) > 10) {
            throw new BusinessException("同一类型下最多创建10个原因");
        }
    }

    private void checkRequest(List<ReasonDTO> reasonDTOList) {
        if (CollectionUtils.isEmpty(reasonDTOList)) {
            throw new BusinessException(REASON_CANNOT_BE_NULL);
        }

        reasonDTOList.forEach(
                reasonDTO -> {
                    if (ObjectUtils.isEmpty(reasonDTO.getReason())) {
                        throw new BusinessException(REASON_NAME_CANNOT_BE_NULL);
                    }
                    if (ObjectUtils.isEmpty(reasonDTO.getReasonTypeCode())) {
                        throw new BusinessException(REASON_TYPE_CODE_CANNOT_BE_NULL);
                    }
                }
        );
    }
}