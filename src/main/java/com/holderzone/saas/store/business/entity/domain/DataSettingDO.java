package com.holderzone.saas.store.business.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/7
 * @since 1.8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName(value = "hsb_data_setting")
public class DataSettingDO {

    /**
     * 全局唯一主键
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private Long guid;

    /**
     * 品牌guid
     */
    @TableField(value = "brand_guid")
    private String brandGuid;

    /**
     * 数据取值的类型，目前只有1
     * 1：正餐点餐页验券加购商品取值
     */
    @TableField(value = "data_setting_type")
    private Integer dataSettingType;

    /**
     * 每个数据取值类型下的code
     * 1 正餐点餐页验券加购商品取值：
     * 1：商品原价
     * 2：商品购买价
     */
    @TableField(value = "data_setting_code")
    private Integer dataSettingCode;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_create")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private LocalDateTime gmtModified;
}
