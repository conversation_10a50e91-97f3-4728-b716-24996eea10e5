package com.holderzone.saas.store.business.queue.service.remote;

import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessMsgClient
 * @date 2019/01/07 15:18
 * @description
 * @program holder-saas-store-table
 */
@Component
@FeignClient(value = "holder-saas-store-message", fallbackFactory = BusinessMsgClient.BusinessMsgClientFallBack.class)
public interface BusinessMsgClient {

    @PostMapping("/msg")
    String sendMsg(BusinessMessageDTO businessMessageDTO);

    @Slf4j
    @Component
    class BusinessMsgClientFallBack implements FallbackFactory<BusinessMsgClient> {

        private String failure = "failure";

        @Override
        public BusinessMsgClient create(Throwable throwable) {
            return businessMessageDTO -> {
                log.error("推送消息服务异常 e={}", throwable);
                return failure;
            };
        }
    }

}
