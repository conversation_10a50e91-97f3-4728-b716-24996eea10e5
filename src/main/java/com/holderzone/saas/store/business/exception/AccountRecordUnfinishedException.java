package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AccountRecordUnfinishedException extends BusinessException {

    private static final long serialVersionUID = -5053337665067651323L;

    public AccountRecordUnfinishedException() {
        super("当前有未扎帐的营业日");
    }

    public AccountRecordUnfinishedException(String message) {
        super("当前有未扎帐的营业日["+message+"]");
    }

    public AccountRecordUnfinishedException(String message, Throwable cause) {
        super(message, cause);
    }
}
