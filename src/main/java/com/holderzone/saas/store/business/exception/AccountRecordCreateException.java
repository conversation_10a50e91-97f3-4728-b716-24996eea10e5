package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AccountRecordCreateException extends BusinessException {

    private static final long serialVersionUID = -7206104690070358873L;

    public AccountRecordCreateException() {
        super("创建营业日失败");
    }

    public AccountRecordCreateException(String message) {
        super("创建营业日["+message+"]失败");
    }

    public AccountRecordCreateException(String message, Throwable cause) {
        super(message, cause);
    }
}
