package com.holderzone.saas.store.business.utils;

import com.holderzone.saas.store.business.entity.domain.SystemDiscountDO;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemDiscountMapStruct
 * @date 2018/08/15 18:13
 * @description //
 * @program holder-saas-store-trading-center
 */
@Mapper
public interface SystemDiscountMapStruct {

    SystemDiscountMapStruct SYSTEM_DISCOUNT_MAP_STRUCT = Mappers.getMapper(SystemDiscountMapStruct.class);


    SystemDiscountDO systemDiscountDtoToDo(SystemDiscountDTO systemDiscountDTO);

}
