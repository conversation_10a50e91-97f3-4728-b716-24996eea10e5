package com.holderzone.saas.store.business.service;


import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RedisService
 * @date 2018/08/03 17:53
 * @description //
 * @program holder-saas-store-trading-center
 */
public interface RedisService {

    void putJHPayInfo(PaymentInfoDTO paymentInfoDTO);

    void putJHPayInfoList(List<PaymentInfoDTO> paymentInfoDTOList, String storeGuid);

    void putJHPayInfoString(String payStr, String storeGuid);

    PaymentInfoDTO getJHPayInfo(String storeGuid);

    List<PaymentInfoDTO> getJHPayInfoList(String storeGuid);

    /**
     * 删除缓存聚合支付账户信息
     *
     * @param storeGuid 门店guid
     */
    void deleteJHPayInfo(String storeGuid);

    void removeSysDiscount(String storeGuid);

    void putPaymentType(String storeGuid, List<PaymentTypeDTO> all);

    List<PaymentTypeDTO> getPaymentType(String storeGuid);

    void deletePaymentType(String storeGuid);

    void deletePaymentType(Set<String> storeGuidList);

    Long rawId(String tag);

    String singleGuid(String tableName);

    List<String> nextBatchId(String tag, long count);

    String nextSurchargeGuid();

    String nextSurchargeAreaGuid();

    List<String> nextBatchSurchargeAreaGuid(long count);

    String nextMemberOperateRecordGuid();

    String getPrintItemOrderConfig(String storeGuid);

    void setPrintItemOrderConfig(String storeGuid, String config);
}
