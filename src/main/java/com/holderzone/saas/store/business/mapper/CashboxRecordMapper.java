package com.holderzone.saas.store.business.mapper;

import com.holderzone.saas.store.business.entity.domain.CashboxRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CashboxRecordMapper
 * @date 2018/07/29 下午6:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Mapper
@Repository
public interface CashboxRecordMapper {

    int insert(CashboxRecordDO cashboxRecordDO);

    CashboxRecordDO queryRecently(CashboxRecordDO cashboxRecordDO);

    CashboxRecordDO query(CashboxRecordDO cashboxRecordDO);

    List<CashboxRecordDO> queryAll(CashboxRecordDO cashboxRecordDO);
}
