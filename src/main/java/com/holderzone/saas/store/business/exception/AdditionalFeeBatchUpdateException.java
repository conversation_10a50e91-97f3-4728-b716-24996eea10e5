package com.holderzone.saas.store.business.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AccountRecordCreateException
 * @date 2018/07/28 上午10:18
 * @description //TODO
 * @program holder-saas-store-business-center
 */
public class AdditionalFeeBatchUpdateException extends BusinessException {

    private static final long serialVersionUID = -1618526827783605747L;

    public AdditionalFeeBatchUpdateException() {
        super("附加费批量更新失败");
    }

    public AdditionalFeeBatchUpdateException(String message) {
        super("附加费["+message+"]批量更新失败");
    }

    public AdditionalFeeBatchUpdateException(String message, Throwable cause) {
        super(message, cause);
    }
}
