package com.holderzone.saas.store.business.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.business.config.RedisIDGenerator;
import com.holderzone.saas.store.business.entity.domain.SystemDiscountDO;
import com.holderzone.saas.store.business.mapper.SystemDiscountMapper;
import com.holderzone.saas.store.business.mapstruct.SystemDiscountMapstruct;
import com.holderzone.saas.store.business.service.RedisService;
import com.holderzone.saas.store.business.service.SystemDiscountService;
import com.holderzone.saas.store.business.utils.SystemDiscountMapStruct;
import com.holderzone.saas.store.dto.trade.StoreDTO;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemDiscountServiceImpl
 * @date 2018/08/15 18:09
 * @description
 * @program holder-saas-store-trading-center
 */
@Service
public class SystemDiscountServiceImpl implements SystemDiscountService {

    private static final SystemDiscountMapStruct SYSTEM_DISCOUNT_MAP_STRUCT = SystemDiscountMapStruct.SYSTEM_DISCOUNT_MAP_STRUCT;
    private final SystemDiscountMapper systemDiscountMapper;
    private final RedisService redisService;
    private final RedisIDGenerator redisIDGenerator;

    @Autowired
    public SystemDiscountServiceImpl(SystemDiscountMapper systemDiscountMapper, RedisService redisService, RedisIDGenerator redisIDGenerator) {
        this.systemDiscountMapper = systemDiscountMapper;
        this.redisService = redisService;
        this.redisIDGenerator = redisIDGenerator;
    }


    @Override
    public void addSystemDiscount(SystemDiscountDTO systemDiscountDTO) {
        List<StoreDTO> storeDTOS = systemDiscountDTO.getStoreDTOS();
        List<SystemDiscountDO> systemDiscountDOS = new ArrayList<>();
        if (null != storeDTOS && !storeDTOS.isEmpty()) {
            List<Long> ids = redisIDGenerator.getBatchIds(storeDTOS.size(), "hst_sys_discount");
            for (int i = 0; i < storeDTOS.size(); i++) {
                StoreDTO storeDTO = storeDTOS.get(i);
                SystemDiscountDO systemDiscountDO = SYSTEM_DISCOUNT_MAP_STRUCT.systemDiscountDtoToDo(systemDiscountDTO);
                int count = systemDiscountMapper.countSameFee(systemDiscountDO.getDiscountFee(), storeDTO.getStoreGuid());
                if (count > 0) {
                    throw new BusinessException("同一门店下不能创建相同的规则");
                }
                systemDiscountDO.setStoreGuid(storeDTO.getStoreGuid());
                systemDiscountDO.setStoreName(storeDTO.getStoreName());
                systemDiscountDO.setSystemDiscountGuid(String.valueOf(ids.get(i)));
                systemDiscountDOS.add(systemDiscountDO);
            }
        }
        if (!systemDiscountDOS.isEmpty()) {
            systemDiscountMapper.insertAll(systemDiscountDOS);
        }
        systemDiscountDTO.getStoreDTOS().forEach(storeDTO -> {
            redisService.removeSysDiscount(storeDTO.getStoreGuid());
        });
    }

    @Override
    public List<SystemDiscountDTO> queryAllSystemDis(String storeGuid) {
        return systemDiscountMapper.getAllSystemDiscount(storeGuid);
    }

    @Override
    public List<SystemDiscountDO> queryAllSystemDOS(String billGuid) {
        return systemDiscountMapper.getAllSystemDOS(billGuid);
    }

    @Override
    public void update(SystemDiscountDTO systemDiscountDTO) {
        SystemDiscountDO systemDiscountDO = SYSTEM_DISCOUNT_MAP_STRUCT.systemDiscountDtoToDo(systemDiscountDTO);
        String storeGuid = systemDiscountDTO.getStoreDTOS().get(0).getStoreGuid();
        systemDiscountDO.setStoreGuid(storeGuid);
        int count = systemDiscountMapper.countSameFee(systemDiscountDO.getDiscountFee(), storeGuid);
        if (count > 0) {
            BigDecimal discountFee = systemDiscountMapper.getDiscountFee(systemDiscountDTO.getSystemDiscountGuid());
            // 自己update什么都不变情况下可以通过
            if (discountFee.compareTo(systemDiscountDTO.getDiscountFee()) == 0) {
                systemDiscountMapper.update(systemDiscountDO);
            } else {
                throw new BusinessException("省零规则重复");
            }
        }
        systemDiscountMapper.update(systemDiscountDO);
        systemDiscountDTO.getStoreDTOS().forEach(storeDTO -> {
            redisService.removeSysDiscount(storeDTO.getStoreGuid());
        });
    }

    @Override
    public SystemDiscountDTO getByStoreGuid(String storeGuid) {
        SystemDiscountDTO systemDiscountDTO = SystemDiscountMapstruct.INSTANT.do2DTO(systemDiscountMapper.getByStoreGuid(storeGuid));
        if (systemDiscountDTO == null) {
            systemDiscountDTO = new SystemDiscountDTO();
            systemDiscountDTO.setStoreGuid(storeGuid);
            systemDiscountDTO.setRoundType(4);
        }
        return systemDiscountDTO;
    }

    @Override
    public SystemDiscountDTO saveOrUpdate(SystemDiscountDTO systemDiscountDTO) {
        if (StringUtils.isEmpty(systemDiscountDTO.getSystemDiscountGuid())) {
            systemDiscountDTO.setSystemDiscountGuid(String.valueOf(redisIDGenerator.getBatchIds(1, "hst_sys_discount").get(0)));
        }
        if (Objects.equals(4, systemDiscountDTO.getRoundType())) {
            systemDiscountMapper.delete(systemDiscountDTO.getStoreGuid(), systemDiscountDTO.getSystemDiscountGuid());
            systemDiscountDTO.setSystemDiscountGuid(null);
            return systemDiscountDTO;
        }
        SystemDiscountDO db = systemDiscountMapper.getById(systemDiscountDTO.getSystemDiscountGuid());
        SystemDiscountDO neo = SystemDiscountMapstruct.INSTANT.dto2DO(systemDiscountDTO);
        if (db != null) {
            systemDiscountMapper.update(neo);
        } else {
            systemDiscountMapper.insertSystemDiscout(neo);
        }
        return systemDiscountDTO;
    }

    @Override
    public void delete(String storeGuid, String systemDiscountGuid) {
        systemDiscountMapper.delete(storeGuid, systemDiscountGuid);
        redisService.removeSysDiscount(storeGuid);
    }
}
