package com.holderzone.saas.store.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.business.entity.domain.ScreenPicTimeDO;
import com.holderzone.saas.store.business.mapper.ScreenPicTimeMapper;
import com.holderzone.saas.store.business.service.ScreenPicTimeService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPicTimeServiceImpl
 * @date 2019/08/19 16:20
 * @description
 * @program holder-saas-store
 */
@Service
public class ScreenPicTimeServiceImpl extends ServiceImpl<ScreenPicTimeMapper, ScreenPicTimeDO> implements ScreenPicTimeService {
}
