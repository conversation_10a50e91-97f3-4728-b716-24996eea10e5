package com.holderzone.saas.store.business.mapstruct;

import com.holderzone.saas.store.business.entity.domain.EstimateDishDO;
import com.holderzone.saas.store.business.entity.domain.EstimateRecordDO;
import com.holderzone.saas.store.business.entity.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateMapstruct
 * @date 2018/08/06 下午4:20
 * @description //TODO
 * @program holder-saas-store-business
 */
@Mapper
public interface EstimateMapstruct {

    EstimateMapstruct INSTANCE = Mappers.getMapper(EstimateMapstruct.class);

    EstimateRecordDO toEstimateRecordDO(EstimateRecordCreateDTO estimateRecordCreateDTO);

    List<EstimateDishDO> toEstimateDishDOS(List<EstimateDishCreateDTO> estimateDishCreateDTOS);

    EstimateRecordDO toEstimateRecordDO(EstimateRecordQuerryDTO estimateRecordQuerryDTO);

    EstimateRecordDTO toEstimateRecordDTO(EstimateRecordDO estimateRecordInSql);

    List<EstimateDishDTO> toEstimateDishDTO(List<EstimateDishDO> estimateDishDOS);
}
