package com.holderzone.saas.store.business.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className Constant
 * @date 2018/09/06 17:46
 * @description
 * @program holder-saas-store-business
 */
public interface Constant {

    String ENTERPRISE_GUID = "enterpriseGuid";

    String FAILURE = "failure";

    String SUCCESS = "success";

    String SUFFIX = ".jpg";

    String SUCCESS_CODE = "10000";

    String STORE_CANNOT_BE_NULL = "门店不能为空";

    String REASON_CANNOT_BE_NULL = "原因不能为空";

    String REASON_NAME_CANNOT_BE_NULL = "原因名字不能为空";

    String REASON_TYPE_CODE_CANNOT_BE_NULL = "原因类型码不能为空";
}
