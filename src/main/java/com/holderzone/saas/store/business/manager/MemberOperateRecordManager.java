package com.holderzone.saas.store.business.manager;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.business.event.OperateRecordMqService;
import com.holderzone.saas.store.dto.business.queue.MemberOperateRecordPushMqDTO;
import com.holderzone.saas.store.dto.trade.req.record.MemberOperateRecordReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @date 2023/10/19
 * @description 会员操作记录实现类
 */
@Slf4j
@Component
public class MemberOperateRecordManager {

    @Resource(name = "memberOperateRecordThreadPool")
    private final ExecutorService memberOperateRecordThreadPool;

    @Resource
    private final OperateRecordMqService operateRecordMqService;


    public MemberOperateRecordManager(ExecutorService memberOperateRecordThreadPool,
                                      OperateRecordMqService operateRecordMqService) {
        this.memberOperateRecordThreadPool = memberOperateRecordThreadPool;
        this.operateRecordMqService = operateRecordMqService;
    }


    public void saveRecord(MemberOperateRecordReqDTO reqDTO) {
        checkParameter(reqDTO);
        UserContext userContext = UserContextUtils.get();
        memberOperateRecordThreadPool.execute(() -> {
            MemberOperateRecordPushMqDTO pushMqDTO = new MemberOperateRecordPushMqDTO();
            pushMqDTO.setOperateRecordReqDTO(reqDTO);
            pushMqDTO.setUserContextJson(JacksonUtils.writeValueAsString(userContext));
            operateRecordMqService.saveRecord(pushMqDTO);
            log.info("会员操作记录推送完成！");
        });

    }

    private static void checkParameter(MemberOperateRecordReqDTO reqDTO) {
        if (ObjectUtils.isEmpty(reqDTO)) {
            throw new ParameterException("入参为空");
        }
        if (StringUtils.isEmpty(reqDTO.getPhoneNum())) {
            throw new ParameterException("会员不能为空");
        }
        if (StringUtils.isEmpty(reqDTO.getOperatorGuid())) {
            throw new ParameterException("操作人Guid不能为空");
        }
        if (StringUtils.isEmpty(reqDTO.getOperatorName())) {
            throw new ParameterException("操作人名称不能为空");
        }
        if (StringUtils.isEmpty(reqDTO.getDeviceType())) {
            throw new ParameterException("设备类型不能为空");
        }
        if (StringUtils.isEmpty(reqDTO.getStoreGuid())) {
            throw new ParameterException("门店guid不能为空");
        }
        if (StringUtils.isEmpty(reqDTO.getStoreName())) {
            throw new ParameterException("门店名称不能为空");
        }
//        if (ObjectUtils.isEmpty(reqDTO.getLoginType())) {
//            throw new ParameterException("登录方式不能为空");
//        }
//        if (ObjectUtils.isEmpty(reqDTO.getModuleType())) {
//            throw new ParameterException("操作模块不能为空");
//        }
    }

}
