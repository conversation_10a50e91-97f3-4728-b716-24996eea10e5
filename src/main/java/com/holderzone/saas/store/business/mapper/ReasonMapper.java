package com.holderzone.saas.store.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.business.entity.domain.ReasonDO;
import com.holderzone.saas.store.dto.business.reason.ReasonDTO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReasonMapper
 * @date 2019/08/15 15:05
 * @description //TODO
 * @program holder-saas-store-business
 */
@Mapper
@Repository
public interface ReasonMapper extends BaseMapper<ReasonDO> {

    List<ReasonDTO> findReason(ReasonDTO reasonDTO);

    int updateCount(List<String> guids);

}