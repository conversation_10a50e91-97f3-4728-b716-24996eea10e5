package com.holderzone.saas.store.business.entity.query;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AdditionalFeeDO
 * @date 2018/08/02 下午3:00
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SurchargeCountQuery {

    private String storeGuid;

    private Integer type;
}

