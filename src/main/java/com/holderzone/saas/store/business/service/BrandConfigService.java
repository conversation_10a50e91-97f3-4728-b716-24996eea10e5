package com.holderzone.saas.store.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.business.entity.domain.BrandConfigDO;
import com.holderzone.saas.store.dto.business.brand.BrandConfigDTO;
import com.holderzone.saas.store.dto.business.brand.BrandConfigQueryDTO;
import com.holderzone.saas.store.dto.business.brand.BrandConfigSaveOrUpdateDTO;

import java.util.List;

/**
 * 品牌配置Service接口
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface BrandConfigService extends IService<BrandConfigDO> {

    /**
     * 保存或更新品牌配置（合并方法）
     * 如果品牌配置已存在则更新，不存在则创建
     *
     * @param saveOrUpdateDTO 保存/更新参数
     * @return 品牌配置ID
     */
    Long saveOrUpdateBrandConfig(BrandConfigSaveOrUpdateDTO saveOrUpdateDTO);

    /**
     * 删除品牌配置
     *
     * @param guid 主键ID
     */
    void deleteBrandConfig(Long guid);

    /**
     * 根据ID查询品牌配置
     *
     * @param guid 主键ID
     * @return 品牌配置信息
     */
    BrandConfigDTO getBrandConfigById(Long guid);

    /**
     * 根据品牌GUID查询品牌配置
     *
     * @param brandGuid 品牌GUID
     * @return 品牌配置信息
     */
    BrandConfigDTO getBrandConfigByBrandGuid(String brandGuid);

    /**
     * 分页查询品牌配置列表
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    Page<BrandConfigDTO> listBrandConfigByPage(BrandConfigQueryDTO queryDTO);

    /**
     * 查询品牌配置列表
     *
     * @param queryDTO 查询参数
     * @return 品牌配置列表
     */
    List<BrandConfigDTO> listBrandConfig(BrandConfigQueryDTO queryDTO);

    /**
     * 根据门店GUID查询品牌配置
     *
     * @param storeGuid 门店GUID
     * @return 品牌配置
     */
    BrandConfigDTO getBrandConfigByStoreGuid(String storeGuid);
}
