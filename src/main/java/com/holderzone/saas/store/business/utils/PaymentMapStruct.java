package com.holderzone.saas.store.business.utils;


import com.holderzone.saas.store.dto.trade.JHConfigDTO;
import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentMapStruct
 * @date 2018/08/10 13:58
 * @description //
 * @program holder-saas-store-trading-center
 */
@Mapper
public interface PaymentMapStruct {

    PaymentMapStruct PAYMENT_MAP_STRUCT = Mappers.getMapper(PaymentMapStruct.class);

    @Mappings({
            @Mapping(target = "appSecret", source = "appSecretKey"),
    })
    PaymentInfoDTO jhConfig(JHConfigDTO jhConfigDTO);

}
