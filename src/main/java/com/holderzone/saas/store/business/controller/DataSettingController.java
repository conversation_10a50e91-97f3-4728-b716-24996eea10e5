package com.holderzone.saas.store.business.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.business.service.DataSettingService;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingQueryDTO;
import com.holderzone.saas.store.dto.business.datasetting.DataSettingSaveDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/5/7
 */
@Slf4j
@Api("数据取值设置")
@RestController
@RequestMapping("/data_setting")
public class DataSettingController {

    private final DataSettingService dataSettingService;

    @Autowired
    public DataSettingController(DataSettingService dataSettingService) {
        this.dataSettingService = dataSettingService;
    }

    @PostMapping(value = "/find_data_setting")
    @ApiOperation("查询数据取值设置列表")
    public List<DataSettingDTO> findDataSetting(@RequestBody @Valid DataSettingQueryDTO dataSettingQueryDTO) {
        log.info("查询'数据取值设置列表': reasonDTO={}", JacksonUtils.writeValueAsString(dataSettingQueryDTO));
        return dataSettingService.findDataSetting(dataSettingQueryDTO);
    }

    @PostMapping(value = "/save_data_setting")
    @ApiOperation("批量保存数据取值设置")
    public Boolean saveDataSetting(@RequestBody @Valid DataSettingSaveDTO dataSettingSaveDTO) {
        log.info("批量更新'数据取值设置': dataSettingDTO={}", JacksonUtils.writeValueAsString(dataSettingSaveDTO));
        return dataSettingService.saveDataSetting(dataSettingSaveDTO);
    }
}
