package com.holderzone.saas.store.business.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.saas.store.business.entity.domain.SurchargeAreaDO;
import com.holderzone.saas.store.business.mapper.SurchargeAreaMapper;
import com.holderzone.saas.store.business.mapstruct.SurchargeMapstruct;
import com.holderzone.saas.store.business.service.RedisService;
import com.holderzone.saas.store.business.service.SurchargeAreaService;
import com.holderzone.saas.store.business.service.client.TableRpcService;
import com.holderzone.saas.store.dto.business.manage.SurchargeAreaDTO;
import com.holderzone.saas.store.dto.business.manage.SurchargeCreateDTO;
import com.holderzone.saas.store.dto.business.manage.SurchargeUpdateDTO;
import com.holderzone.saas.store.dto.table.AreaDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SurchargeAreaServiceImpl
 * @date 2018/08/02 下午3:35
 * @description //TODO
 * @program holder-saas-store-business
 */

@Service
public class SurchargeAreaServiceImpl extends ServiceImpl<SurchargeAreaMapper, SurchargeAreaDO> implements SurchargeAreaService {

    private final RedisService redisService;

    private final TableRpcService tableRpcService;

    private final SurchargeMapstruct surchargeMapstruct;

    @Autowired
    public SurchargeAreaServiceImpl(RedisService redisService, TableRpcService tableRpcService, SurchargeMapstruct surchargeMapstruct) {
        this.redisService = redisService;
        this.tableRpcService = tableRpcService;
        this.surchargeMapstruct = surchargeMapstruct;
    }

    @Override
    public void bindSurchargeArea(SurchargeCreateDTO surchargeCreateDTO) {
        addSurchargeArea(surchargeCreateDTO.getSurchargeGuid(), surchargeCreateDTO.getAreaGuidList());
    }

    @Override
    public List<SurchargeAreaDTO> findSurchargeArea(String surchargeGuid, String storeGuid) {
        List<SurchargeAreaDO> surchargeAreaList = list(new LambdaQueryWrapper<SurchargeAreaDO>()
                .eq(SurchargeAreaDO::getSurchargeGuid, surchargeGuid));
        if (CollectionUtils.isEmpty(surchargeAreaList)) return Collections.emptyList();
        List<String> selectedAreaGuidList = surchargeAreaList.stream()
                .map(SurchargeAreaDO::getAreaGuid).collect(Collectors.toList());

        List<AreaDTO> areaDTOS = tableRpcService.queryArea(storeGuid);
        if (CollectionUtils.isEmpty(areaDTOS)) return Collections.emptyList();

        return areaDTOS.stream()
                .map(areaDTO -> {
                    SurchargeAreaDTO surchargeAreaDTO = new SurchargeAreaDTO();
                    surchargeAreaDTO.setGuid(areaDTO.getGuid());
                    surchargeAreaDTO.setAreaName(areaDTO.getAreaName());
                    surchargeAreaDTO.setIsSelected(selectedAreaGuidList.contains(areaDTO.getGuid()));
                    return surchargeAreaDTO;
                }).collect(Collectors.toList());
    }

    @Override
    public List<SurchargeAreaDO> findAreaSurcharge(List<String> areaGuidList) {
        if (CollectionUtils.isEmpty(areaGuidList)) return Collections.emptyList();
        return list(new LambdaQueryWrapper<SurchargeAreaDO>().in(SurchargeAreaDO::getAreaGuid, areaGuidList));
    }

    @Override
    public List<SurchargeAreaDO> findAreaSurcharge(List<String> surchargeGuidList, String areaGuid) {
        if (CollectionUtils.isEmpty(surchargeGuidList) || StringUtils.isEmpty(areaGuid)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SurchargeAreaDO>()
                .eq(SurchargeAreaDO::getAreaGuid, areaGuid)
                .in(SurchargeAreaDO::getSurchargeGuid, surchargeGuidList));
    }

    @Override
    public List<SurchargeAreaDO> findAreaSurchargeBySurchargeGuids(List<String> surchargeGuidList) {
        return list(new LambdaQueryWrapper<SurchargeAreaDO>()
                .in(SurchargeAreaDO::getSurchargeGuid, surchargeGuidList));
    }

    @Override
    public void rebindSurchargeArea(SurchargeUpdateDTO surchargeUpdateDTO) {
        removeSurchargeArea(surchargeUpdateDTO.getSurchargeGuid());
        addSurchargeArea(surchargeUpdateDTO.getSurchargeGuid(), surchargeUpdateDTO.getAreaGuidList());
    }

    @Override
    public void removeSurchargeArea(String surchargeGuid) {
        remove(new LambdaQueryWrapper<SurchargeAreaDO>().eq(SurchargeAreaDO::getSurchargeGuid, surchargeGuid));
    }

    @Override
    public void removeSurchargeArea(List<String> surchargeGuidList) {
        if (CollectionUtils.isEmpty(surchargeGuidList)) return;
        remove(new LambdaQueryWrapper<SurchargeAreaDO>().in(SurchargeAreaDO::getSurchargeGuid, surchargeGuidList));
    }

    private void addSurchargeArea(String surchargeGuid, List<String> areaGuidList) {
        if (CollectionUtils.isEmpty(areaGuidList)) return;
        List<String> batchGuid = redisService.nextBatchSurchargeAreaGuid(areaGuidList.size());
        List<SurchargeAreaDO> arrayOfSurchargeAreaDO = areaGuidList.stream()
                .map(areaGuid -> new SurchargeAreaDO()
                        .setGuid(batchGuid.remove(batchGuid.size() - 1))
                        .setSurchargeGuid(surchargeGuid)
                        .setAreaGuid(areaGuid))
                .collect(Collectors.toList());
        saveBatch(arrayOfSurchargeAreaDO);
    }
}
