package com.holderzone.saas.store.business.mapstruct;

import com.holderzone.saas.store.business.entity.domain.HandoverPayDetailDO;
import com.holderzone.saas.store.business.entity.domain.HandoverRecordDO;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.report.query.HandOverReportQueryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandoverRecordMapstruct
 * @date 2018/07/29 上午11:53
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Mapper
public interface HandoverRecordMapstruct {

    HandoverRecordMapstruct INSTANCE = Mappers.getMapper(HandoverRecordMapstruct.class);

    HandoverRecordDO toHandoverRecordDO(HandoverRecordCreateDTO handoverRecordCreateDTO);

    @Mappings({
            @Mapping(target = "confirmUserGuid", source = "userGuid"),
            @Mapping(target = "confirmUserName", source = "userName")
    })
    HandoverRecordDO toHandoverRecordDO(HandoverRecordConfirmDTO handoverRecordConfirmDTO);

    HandoverRecordDO toHandoverRecordDO(HandoverRecordQuerySingleDTO handoverRecordQuerySingleDTO);

    @Mappings({
            @Mapping(target = "createGuid", source = "createUserGuid"),
            @Mapping(target = "createName", source = "createUserName"),
            @Mapping(target = "refundMoney", source = "refundMoney"),
            @Mapping(target = "refundCount", source = "refundCount")
    })
    HandoverRecordDTO toHandoverRecordDTO(HandoverRecordDO handoverRecordInSql);

    List<HandoverRecordDTO> toHandoverRecordDTOS(List<HandoverRecordDO> handoverRecordDOS);

    HandoverRecordDO toHandoverRecordDO(BaseDTO baseDTO);

    HandoverPayDetailDTO toHandoverPayDetailDTO(HandoverPayDetailDO handoverPayDetailDO);

    List<HandoverPayDetailDTO> toHandoverPayDetailDTOList(List<HandoverPayDetailDO> handoverPayDetailDOList);

    HandOverQueryBO handOverQueryDTO2queryBO(HandOverReportQueryDTO handOverQueryDTO);
}
