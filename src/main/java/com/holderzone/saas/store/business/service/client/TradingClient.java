package com.holderzone.saas.store.business.service.client;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.manage.HandoverPayDTO;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className TradingClient
 * @date 18-9-17 下午1:46
 * @description 服务间调用-交易中心服务
 * @program holder-saas-store-business
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = TradingClient.ServiceFallBack.class)
public interface TradingClient {
    @PostMapping(value = "/handover_sheet/handover")
    HandoverPayDTO query(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO);

    @PostMapping(value = "/handover_sheet/retail/handover")
    HandoverPayDTO retailQuery(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO);

    @PostMapping(value = "/handover_sheet/handoverNew")
    HandoverPayDTO handoverNew(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO);

    /**
     * 交接班查询第三方活动汇总
     */
    @PostMapping("/handover_sheet/handoverNew/third_activity")
    List<AmountItemDTO.InnerDetails> handoverNewByThirdActivity(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO);

    /**
     * 交接班查询优惠方式汇总
     */
    @PostMapping("/handover_sheet/handoverNew/discount")
    List<AmountItemDTO> handoverNewByDiscount(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO);

    @Deprecated
    @PostMapping(value = "/pay/type/payType/{storeGuid}")
    List<PaymentTypeDTO> getAllTypeName(@PathVariable("storeGuid") String storeGuid);

    @GetMapping(value = "/order_detail/queryordernum")
    Integer queryOrderNumForStoreGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 删除门店自动号牌
     */
    @DeleteMapping("/fast_food/remove/store/auto_mark/{storeGuid}")
    void removeStoreAutoMark(@PathVariable String storeGuid);

    @Component
    @Slf4j
    class ServiceFallBack implements FallbackFactory<TradingClient> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TradingClient create(Throwable cause) {
            return new TradingClient() {
                @Override
                public HandoverPayDTO query(HandoverPayQueryDTO handoverPayQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "queryByAndroid", JacksonUtils.writeValueAsString(handoverPayQueryDTO),
                            ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public HandoverPayDTO retailQuery(HandoverPayQueryDTO handoverPayQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "retailQuery", JacksonUtils.writeValueAsString(handoverPayQueryDTO),
                            ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public HandoverPayDTO handoverNew(HandoverPayQueryDTO handoverPayQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "handoverNew", JacksonUtils.writeValueAsString(handoverPayQueryDTO),
                            ThrowableUtils.asString(cause));
                    throw new BusinessException("查询员工销售数据失败");
                }

                @Override
                public List<AmountItemDTO.InnerDetails> handoverNewByThirdActivity(HandoverPayQueryDTO handoverPayQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "handoverNewByThirdActivity", JacksonUtils.writeValueAsString(handoverPayQueryDTO),
                            ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public List<AmountItemDTO> handoverNewByDiscount(HandoverPayQueryDTO handoverPayQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "handoverNewByDiscount", JacksonUtils.writeValueAsString(handoverPayQueryDTO),
                            ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public List<PaymentTypeDTO> getAllTypeName(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "getAllTypeName", "门店guid为：" + storeGuid, ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public Integer queryOrderNumForStoreGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryOrderNumForStoreGuid", "门店guid为：" + storeGuid, ThrowableUtils.asString(cause));
                    return null;
                }

                @Override
                public void removeStoreAutoMark(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "removeStoreAutoMark", "门店guid为：" + storeGuid, ThrowableUtils.asString(cause));
                }
            };
        }
    }
}
