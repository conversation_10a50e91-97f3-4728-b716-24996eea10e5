package com.holderzone.saas.store.business.mapper;

import com.holderzone.saas.store.business.entity.domain.SystemDiscountDO;
import com.holderzone.saas.store.dto.trade.SystemDiscountDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemDiscountMapper
 * @date 2018/08/15 18:11
 * @description //
 * @program holder-saas-store-trading-center
 */
@Repository
public interface SystemDiscountMapper {

    void insertSystemDiscout(SystemDiscountDO systemDiscountDO);

    List<SystemDiscountDTO> getAllSystemDiscount(@Param("storeGuid") String storeGuid);

    void update(SystemDiscountDO systemDiscountDO);

    void delete(@Param("storeGuid") String storeGuid, @Param("systemDiscountGuid") String systemDiscountGuid);

    List<SystemDiscountDO> getAllSystemDOS(String billGuid);

    void insertAll(List<SystemDiscountDO> systemDiscountDOS);

    int countSameFee(@Param("discountFee") BigDecimal discountFee, @Param("storeGuid") String storeGuid);

    BigDecimal getDiscountFee(String systemDiscountGuid);

    SystemDiscountDO getByStoreGuid(@Param("storeGuid") String storeGuid);

    SystemDiscountDO getById(@Param("systemDiscountGuid") String systemDiscountGuid);
}
