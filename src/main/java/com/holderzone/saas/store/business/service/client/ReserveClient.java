package com.holderzone.saas.store.business.service.client;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.manage.HandoverPayQueryDTO;
import com.holderzone.saas.store.dto.reserve.ReserveHandoverDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "holder-saas-store-reserve", fallbackFactory = ReserveClient.ServiceFallBack.class)
public interface ReserveClient {

    @PostMapping("/reserve/handover")
    ReserveHandoverDTO handover(@RequestBody HandoverPayQueryDTO handoverPayQueryDTO);

    @Component
    @Slf4j
    class ServiceFallBack implements FallbackFactory<ReserveClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ReserveClient create(Throwable throwable) {
            return new ReserveClient() {
                @Override
                public ReserveHandoverDTO handover(HandoverPayQueryDTO handoverPayQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "handover", JacksonUtils.writeValueAsString(handoverPayQueryDTO),
                            ThrowableUtils.asString(throwable));
                    return null;
                }
            };
        }
    }
}
