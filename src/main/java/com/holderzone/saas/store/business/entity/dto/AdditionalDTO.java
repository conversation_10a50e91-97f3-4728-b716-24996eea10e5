package com.holderzone.saas.store.business.entity.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalDTO extends BasePageDTO {

    private static final long serialVersionUID = 8091374849599304216L;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 区域guid
     */
    private String areaGuid;

    /**
     * 附加费guid
     */
    private String additionalFeeGuid;

    /**
     * 费用类型：
     * 0：按人
     * 1：按桌
     */
    private Integer feeType;

    /**
     * 附加费名
     */
    private String name;

    /**
     * 附加费金额
     */
    private BigDecimal amount;

    /**
     * 附加费排序
     */
    private Integer sort;

    /**
     * 附加费备注
     */
    private String remark;

    /**
     * 是否已启用
     * 0=未删启用
     * 1=已启用
     */
    private Integer enable;

    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;


    /**
     * 转换数据库中的偏移index
     * @return 分页偏移量
     */
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    public Integer getOffsetIndex() {
        return this.getPageSize() * (this.getCurrentPage() - 1);
    }


}
