package com.holderzone.saas.store.business.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.business.entity.domain.StoreConfigDO;
import com.holderzone.saas.store.business.mapper.StoreConfigMapper;
import com.holderzone.saas.store.business.mapstruct.StoreConfigMapstruct;
import com.holderzone.saas.store.business.service.RedisService;
import com.holderzone.saas.store.business.service.StoreConfigService;
import com.holderzone.saas.store.business.service.client.OrgFeignClient;
import com.holderzone.saas.store.business.service.client.TradingClient;
import com.holderzone.saas.store.dto.business.manage.StoreConfigCreateDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigUpdateDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.config.req.CashSettingReqDTO;
import com.holderzone.saas.store.dto.config.req.DineFoodSettingReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.enums.business.PrintItemOrderEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreConfigServiceImpl
 * @date 2018/07/29 下午4:26
 * @description 门店配置相关接口
 * @program holder-saas-store-business-center
 */
@Service
public class StoreConfigServiceImpl implements StoreConfigService {

    private final StoreConfigMapper storeConfigMapper;

    private final TradingClient tradingClient;

    private final OrgFeignClient orgFeignClient;

    private final RedisService redisService;

    @Autowired
    public StoreConfigServiceImpl(StoreConfigMapper storeConfigMapper,
                                  TradingClient tradingClient,
                                  OrgFeignClient orgFeignClient,
                                  RedisService redisService) {
        this.storeConfigMapper = storeConfigMapper;
        this.tradingClient = tradingClient;
        this.orgFeignClient = orgFeignClient;
        this.redisService = redisService;
    }

    @Override
    public void create(StoreConfigCreateDTO storeConfigCreateDTO) {
        StoreConfigDO storeConfigDO = StoreConfigMapstruct.INSTANCE.toStoreConfigDO(storeConfigCreateDTO);
        if (0 == storeConfigMapper.insert(storeConfigDO)) {
            throw new BusinessException("新增门店配置失败，请联系管理员");
        }
    }

    @Override
    // @Cacheable(value = "business:manage:storeConfig", key = "#storeConfigQueryDTO.storeGuid")
    public StoreConfigDTO query(StoreConfigQueryDTO storeConfigQueryDTO) {
        StoreConfigDO storeConfigDO = StoreConfigMapstruct.INSTANCE.toStoreConfigDO(storeConfigQueryDTO);
        StoreConfigDO storeConfigInSql = storeConfigMapper.query(storeConfigDO);
        if (null == storeConfigInSql) {
            return null;
        }
        return StoreConfigMapstruct.INSTANCE.toStoreConfigDTO(storeConfigInSql);
    }

    @Override
    // @CacheEvict(value = "business:manage:storeConfig", key = "#storeConfigUpdateDTO.storeGuid")
    public void update(StoreConfigUpdateDTO storeConfigUpdateDTO) {
        updateConfig(storeConfigUpdateDTO);
        // 每次保存，都将门店当前号牌数初始化
        tradingClient.removeStoreAutoMark(storeConfigUpdateDTO.getStoreGuid());
    }

    @Override
    public List<StoreConfigDTO> list() {
        List<StoreConfigDO> storeConfigDOS = storeConfigMapper.queryAll();
        if (storeConfigDOS.isEmpty()) {
            return Collections.emptyList();
        }
        return StoreConfigMapstruct.INSTANCE.toStoreConfigDTOS(storeConfigDOS);
    }

    @Override
    public List<StoreConfigDTO> queryByIdList(List<String> stringList) {
        List<StoreConfigDO> query = storeConfigMapper.queryList(stringList);
        if (query == null || query.isEmpty()) {
            return null;
        }
        List<StoreConfigDTO> list = new ArrayList<>();
        query.forEach(p -> list.add(StoreConfigMapstruct.INSTANCE.toStoreConfigDTO(p)));
        return list;
    }

    @Override
    public void init(BaseDTO baseDTO) {

        StoreConfigDO storeConfigDO = new StoreConfigDO();
        storeConfigDO.setBusinessStartTime(LocalTime.of(0, 0, 0));
        storeConfigDO.setBusinessEndTime(LocalTime.of(23, 59, 59));
        storeConfigDO.setStoreGuid(baseDTO.getStoreGuid());
        StoreDTO storeDTO = orgFeignClient.queryStoreByGuid(baseDTO.getStoreGuid());
        if (!ObjectUtils.isEmpty(storeDTO)) {
            storeConfigDO.setStoreName(storeDTO.getName());
        }
        storeConfigMapper.insert(storeConfigDO);
    }

    @Override
    public void updateFinishFood(StoreConfigUpdateDTO storeConfigUpdateDTO) {
        updateConfig(storeConfigUpdateDTO);
    }

    @Override
    public String queryPrintItemOrderConfig(StoreConfigQueryDTO storeConfigQueryDTO) {
        String printItemOrderConfig = redisService.getPrintItemOrderConfig(storeConfigQueryDTO.getStoreGuid());
        if (!StringUtils.isEmpty(printItemOrderConfig)) {
            return printItemOrderConfig;
        }
        StoreConfigDTO storeConfigDTO = query(storeConfigQueryDTO);
        storeConfigDTO = Optional.ofNullable(storeConfigDTO).orElse(new StoreConfigDTO());
        String printItemOrder = Optional.ofNullable(storeConfigDTO.getPrintItemOrder()).orElse(PrintItemOrderEnum.ASC.name());
        redisService.setPrintItemOrderConfig(storeConfigQueryDTO.getStoreGuid(), printItemOrder);
        return printItemOrder;
    }

    @Override
    public void updatePrintItemOrderConfig(StoreConfigUpdateDTO storeConfigUpdateDTO) {
        updateConfig(storeConfigUpdateDTO);
        // update cache
        redisService.setPrintItemOrderConfig(storeConfigUpdateDTO.getStoreGuid(), storeConfigUpdateDTO.getPrintItemOrder());
    }

    @Override
    public void updateDineFoodSetting(DineFoodSettingReqDTO settingReqDTO) {
        StoreConfigUpdateDTO storeConfigUpdateDTO = new StoreConfigUpdateDTO();
        storeConfigUpdateDTO.setStoreGuid(settingReqDTO.getStoreGuid());
        storeConfigUpdateDTO.setEnableHandleClose(settingReqDTO.getEnableHandleClose());
        storeConfigUpdateDTO.setEnableMultiplePay(settingReqDTO.getEnableMultiplePay());
        storeConfigUpdateDTO.setEnableDineInChangeDiffValue(settingReqDTO.getEnableDineInChangeDiffValue());
        storeConfigUpdateDTO.setEnableFastChangeDiffValue(settingReqDTO.getEnableFastChangeDiffValue());
        updateConfig(storeConfigUpdateDTO);
    }

    @Override
    public void initCashSetting(CashSettingReqDTO cashSettingReqDTO) {
        StoreConfigDO storeConfigDO = new StoreConfigDO();
        storeConfigDO.setBusinessStartTime(LocalTime.of(0, 0, 0));
        storeConfigDO.setBusinessEndTime(LocalTime.of(23, 59, 59));
        storeConfigDO.setStoreGuid(cashSettingReqDTO.getStoreGuid());
        StoreDTO storeDTO = orgFeignClient.queryStoreByGuid(cashSettingReqDTO.getStoreGuid());
        if (!ObjectUtils.isEmpty(storeDTO)) {
            storeConfigDO.setStoreName(storeDTO.getName());
        }
        storeConfigMapper.insert(storeConfigDO);
    }

    private void updateConfig(StoreConfigUpdateDTO storeConfigUpdateDTO) {
        StoreConfigDO storeConfigDO = StoreConfigMapstruct.INSTANCE.toStoreConfigDO(storeConfigUpdateDTO);
        if (storeConfigDO.isAllConfigNull()) {
            throw new BusinessException("请提供至少一个配置修改项");
        }
        // 修改
        if (0 == storeConfigMapper.update(storeConfigDO)) {
            throw new BusinessException("修改门店配置失败，请联系管理员");
        }
    }
}
