package com.holderzone.saas.store.business.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.business.service.ScreenPictureService;
import com.holderzone.saas.store.dto.business.manage.*;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ScreenPictureController
 * @date 2018/09/06 17:41
 * @description
 * @program holder-saas-store-business
 */
@RestController
@RequestMapping("/pic")
public class ScreenPictureController {

    private static final Logger logger = LoggerFactory.getLogger(ScreenPictureController.class);
    private final ScreenPictureService screenPictureService;

    @Autowired
    public ScreenPictureController(ScreenPictureService screenPictureService) {
        this.screenPictureService = screenPictureService;
    }

    @ApiOperation(value = "保存副屏图片配置")
    @PostMapping("/save_config")
    public String saveConfig(@RequestBody ScreenPictureConfigDTO screenPictureConfigDTO) {
        return screenPictureService.saveConfig(screenPictureConfigDTO);
    }

    @ApiOperation(value = "获取当前的副屏图片配置")
    @PostMapping("/get_config")
    public ScreenPictureConfigDTO getConfig(@RequestBody ScreenPicConfigReqDTO screenPicConfigReqDTO) {
        return screenPictureService.getConfig(screenPicConfigReqDTO);
    }

    @PostMapping("/save")
    public String upload(@RequestBody ScreenPictureDTO screenPictureDTO) {
        logger.info("保存图片 screenPictureDTO={}", JacksonUtils.writeValueAsString(screenPictureDTO));
        return screenPictureService.save(screenPictureDTO);
    }

    @PostMapping("/time")
    public String setTime(@RequestBody ScreenPicTimeDTO screenPicTimeDTO) {
        logger.info("设置时间 screenPicTimeDTO={}", JacksonUtils.writeValueAsString(screenPicTimeDTO));
        return screenPictureService.setTime(screenPicTimeDTO);
    }

    @PostMapping("/get/time")
    public List<ScreenPicTimeDTO> getTime(@RequestBody ScreenPicTimeDTO screenPicTimeDTO) {
        logger.info("获取时间 screenPicTimeDTO={}", JacksonUtils.writeValueAsString(UserContextUtils.getJsonStr()));
        return screenPictureService.getTime(screenPicTimeDTO);
    }

    @ApiOperation(value = "安卓查询门店附屏图片", response = ScreenPicRespDTO.class)
    @PostMapping("/query/android/{storeGuid}")
    public List<ScreenAppRespDTO> queryByAndroid(@PathVariable("storeGuid") String storeGuid) {
        logger.info("安卓查询门店附屏图片 storeGuid={}", storeGuid);
        return screenPictureService.queryByAndroid(storeGuid);
    }

    @ApiOperation(value = "安卓查询门店附屏图片", response = ScreenPicRespDTO.class)
    @PostMapping("/query/web")
    public List<ScreenAppRespDTO> queryByWebFronted(@RequestBody ScreenPicQuery screenPicQuery) {
        logger.info("web查询门店附屏图片 screenPicQuery={}", JacksonUtils.writeValueAsString(screenPicQuery));
        return screenPictureService.queryByWeb(screenPicQuery);
    }

    @ApiOperation(value = "查询门店附屏配置")
    @PostMapping("/query_store_config")
    public ScreenPictureConfigDTO queryStoreConfig(@RequestBody ScreenPicConfigReqDTO query) {
        logger.info("[查询门店附屏配置]query={}", JacksonUtils.writeValueAsString(query));
        return screenPictureService.queryStoreConfig(query);
    }

    @ApiOperation(value = "删除图片", response = String.class)
    @PostMapping("delete/{screenPictureGuid}")
    public String delete(@PathVariable("screenPictureGuid") String screenPictureGuid) {
        logger.info("删除图片 screenPictureGuid={}", screenPictureGuid);
        return screenPictureService.delete(screenPictureGuid);
    }

}
