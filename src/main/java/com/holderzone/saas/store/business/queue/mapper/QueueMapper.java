package com.holderzone.saas.store.business.queue.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.saas.store.business.queue.domain.HolderQueueDO;
import com.holderzone.saas.store.business.queue.domain.QueueSizeDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueMapper
 * @date 2019/03/27 16:54
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Repository
public interface QueueMapper extends BaseMapper<HolderQueueDO> {
    List<QueueSizeDO> selectQueueSizeByStoreGuid(@Param("storeGuid") String storeGuid);

    Integer selectCountByStoreGuid(@Param("storeGuid") String storeGuid);
}