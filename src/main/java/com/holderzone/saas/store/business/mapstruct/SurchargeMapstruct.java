package com.holderzone.saas.store.business.mapstruct;

import com.holderzone.saas.store.business.entity.domain.SurchargeAreaDO;
import com.holderzone.saas.store.business.entity.domain.SurchargeDO;
import com.holderzone.saas.store.dto.business.manage.SurchargeCreateDTO;
import com.holderzone.saas.store.dto.business.manage.SurchargeDTO;
import com.holderzone.saas.store.dto.business.manage.SurchargeUpdateDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeAreaRawDTO;
import com.holderzone.saas.store.dto.business.manage.sync.SurchargeRawDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SurchargeMapstruct
 * @date 2018/08/02 下午3:48
 * @description //TODO
 * @program holder-saas-store-business
 */
@Component
@Mapper(componentModel = "spring")
public interface SurchargeMapstruct {

    SurchargeDO fromUpdate(SurchargeCreateDTO surchargeCreateDTO);

    SurchargeDO fromUpdate(SurchargeUpdateDTO surchargeUpdateDTO);

    SurchargeDTO toRespDTO(SurchargeDO surchargeDO);

    List<SurchargeDTO> toRespDTO(List<SurchargeDO> surchargeDOS);

    SurchargeRawDTO toRaw(SurchargeDO surchargeDO);

    List<SurchargeRawDTO> toRaw(List<SurchargeDO> surchargeDO);

    SurchargeAreaRawDTO toAreaRaw(SurchargeAreaDO surchargeAreaDO);

    List<SurchargeAreaRawDTO> toAreaRaw(List<SurchargeAreaDO> surchargeAreaDO);
}
