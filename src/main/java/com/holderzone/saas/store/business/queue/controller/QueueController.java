package com.holderzone.saas.store.business.queue.controller;

import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.saas.store.business.queue.service.QueueService;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import com.holderzone.saas.store.dto.queue.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueController
 * @date 2019/03/27 16:59
 * @description //TODO
 * @program holder-saas-store-queue
 */
@Api("排队 - 队列")
@RestController
@Slf4j
public class QueueController {
    private QueueService queueService;

    @Autowired
    public QueueController(QueueService queueService) {
        this.queueService = queueService;
    }

    @PostMapping("/queue/save")
    @ApiOperation("修改/保存")
    public String save(@Validated @RequestBody HolderQueueDTO dto) {
        return queueService.save(dto);
    }

    @PostMapping("/queue/enable")
    @ApiOperation("禁用/启用")
    public Boolean enable(@RequestParam("guid") String guid) {
        return queueService.enable(guid);
    }

    @DeleteMapping("/queue/delete")
    @ApiOperation("删除")
    public Boolean delete(@RequestParam("guid") String guid) {
        return queueService.delete(guid);
    }

    @PostMapping("/queue/codes")
    @ApiOperation("codes")
    public List<String> codes() {
        return queueService.availableCode();
    }

    @GetMapping("/queue/list")
    @ApiOperation("list")
    public StoreQueueDTO list() {
        return queueService.all();
    }

    @GetMapping("/queue/query")
    @ApiOperation("query")
    public List<QueueDetailDTO> query(@RequestParam("storeGuid") String storeGuid) {
        return queueService.query(storeGuid);
    }



    @PostMapping("/queue/listByStore")
    @ApiOperation("listByStore")
    public List<HolderQueueDTO> listByStore(@RequestBody StoreGuidDTO storeGuid) {
        return queueService.listByStore(storeGuid.getStoreGuid());
    }

    @PostMapping("/queue/allEmpty")
    @ApiOperation("listByStore")
    public Boolean allEmpty(@RequestBody StoreGuidDTO storeGuid) {
        return queueService.allEmpty(storeGuid.getStoreGuid());
    }

    @PostMapping("/queue/obtain")
    @ApiOperation("obtain")
    public HolderQueueDTO fetchOne(@RequestBody QueueGuidDTO dto) {
        return queueService.fetchOne(dto.getQueueGuid());
    }

    @PostMapping("/queue/remove")
    @ApiOperation("remove")
    public Boolean remove(@RequestBody HolderQueueGuidListDTO dto) {
        return queueService.remove(dto.getGuids());
    }

    @PostMapping("/queue/removeAll")
    @ApiOperation("清空")
    public Boolean removeAll() {
        return queueService.removeAll();
    }

    @PostMapping("/queue/clean/auto")
    @ApiOperation("清空")
    public Boolean autoClean(@RequestBody ConfigRespDTO respDTO) {
        EnterpriseIdentifier.setEnterpriseGuid(respDTO.getEnterpriseGuid());
        UserContextUtils.put("{\"enterpriseGuid\":\"" + respDTO.getEnterpriseGuid() + "\"}");
        queueService.clean(respDTO.getStoreGuid());
        return true;
    }


    @PostMapping("/queue/table/save")
    @ApiOperation("保存桌台")
    public QueueTableDTO saveTable(@RequestBody QueueTableDTO tableDTO) {
        return queueService.saveTables(tableDTO);
    }

    @GetMapping("/queue/table/all")
    @ApiOperation("保存桌台")
    public List<TreeTableDTO> allTables(@RequestParam("storeGuid") String storeGuid) {
        return queueService.allTables(storeGuid);
    }

    @PostMapping("/queue/table/obtain")
    @ApiOperation("查询队列对应桌台")
    public QueueTableDTO obtain(@RequestBody QueueGuidDTO queueGuidDTO) {
        return queueService.obtain(queueGuidDTO.getQueueGuid());
    }

    @PostMapping("/queue/table")
    @ApiOperation("查询队列对应桌台")
    public List<QueueTableDTO> obtain(@RequestBody StoreGuidDTO queueGuidDTO) {
        return queueService.queryByStore(queueGuidDTO.getStoreGuid());
    }
}