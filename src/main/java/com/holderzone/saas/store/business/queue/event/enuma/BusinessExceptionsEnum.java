package com.holderzone.saas.store.business.queue.event.enuma;


import com.holderzone.saas.store.business.queue.utils.LocaleUtils;

public enum BusinessExceptionsEnum {

    QUEUE_DOES_NOT_EXIST("队列不存在或者人数超出限制"),

    NOT_ENOUGH_CUSTOMERS("顾客人数小于桌台配置最少人数"),

    OVER_NUMBER_OF_CUSTOMERS("顾客人数超出桌台配置最大人数"),

    THE_SYSTEM_IS_BUSY("系统繁忙请稍后重试"),

;
    private final String message;

    BusinessExceptionsEnum(final String message) {
        this.message = message;
    }

    public final String getMessage() {
        return LocaleUtils.getMessage(name(),message);
    }
}
