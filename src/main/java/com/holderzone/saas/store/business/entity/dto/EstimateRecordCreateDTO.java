package com.holderzone.saas.store.business.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateRecordDO
 * @date 2018/08/06 上午11:33
 * @description //TODO
 * @program holder-saas-store-business
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class EstimateRecordCreateDTO implements Serializable {

    /**
     * 门店guid
     */
    @ApiModelProperty(value = "门店guid")
    private String storeGuid;

    /**
     * 创建人guid
     */
    @ApiModelProperty(value = "创建人guid")
    private String createUserGuid;

    /**
     * 创建人名字
     */
    @ApiModelProperty(value = "创建人名字")
    private String createUserName;

    /**
     * 营业日日期
     */
    @ApiModelProperty(value = "营业日日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate businessDay;

    /**
     * 该记录下的估清菜品列表
     */
    @ApiModelProperty(value = "该记录下的估清菜品列表")
    private List<EstimateDishCreateDTO> estimateDishes;
}
