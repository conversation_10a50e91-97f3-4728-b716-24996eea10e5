package com.holderzone.saas.store.business.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RocketMqConfig
 * @date 2019/03/05 10:34
 * @description 微信Mq配置
 * @program holder-saas-store
 */
public interface RocketMqConfig {

    String DOWNSTREAM_STORE_TOPIC = "downstream-store-topic";

    String DOWNSTREAM_STORE_CREATE_TAG = "downstream-store-create-tag";

    String DOWNSTREAM_STORE_INIT_REASON_GROUP = "downstream-store-init-reason-group";

    String DOWNSTREAM_STORE_CREATE_SURCHARGE_GROUP = "downstream-store-create-surcharge-group";

    /**
     * 会员操作记录
     */
    String MEMBER_OPERATE_RECORD_TOPIC = "member-operate-record-topic";

    String MEMBER_OPERATE_RECORD_SAVE_TAG = "member-operate-record-save-tag";

    String MEMBER_OPERATE_RECORD_GROUP = "member-operate-record-group";

}
