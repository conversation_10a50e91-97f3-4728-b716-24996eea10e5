package com.holderzone.saas.store.business.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.business.entity.domain.SurchargeAreaDO;
import com.holderzone.saas.store.dto.business.manage.SurchargeAreaDTO;
import com.holderzone.saas.store.dto.business.manage.SurchargeCreateDTO;
import com.holderzone.saas.store.dto.business.manage.SurchargeUpdateDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SurchargeAreaService
 * @date 2018/08/02 下午3:35
 * @description //TODO
 * @program holder-saas-store-business
 */
public interface SurchargeAreaService extends IService<SurchargeAreaDO> {

    void bindSurchargeArea(SurchargeCreateDTO surchargeCreateDTO);

    List<SurchargeAreaDTO> findSurchargeArea(String surchargeGuid, String storeGuid);

    List<SurchargeAreaDO> findAreaSurcharge(List<String> areaGuidList);

    List<SurchargeAreaDO> findAreaSurcharge(List<String> surchargeGuidList, String areaGuid);

    List<SurchargeAreaDO> findAreaSurchargeBySurchargeGuids(List<String> surchargeGuidList);

    void rebindSurchargeArea(SurchargeUpdateDTO surchargeUpdateDTO);

    void removeSurchargeArea(String surchargeGuid);

    void removeSurchargeArea(List<String> surchargeGuidList);
}
