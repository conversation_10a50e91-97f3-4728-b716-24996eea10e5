package com.holderzone.saas.store.business.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RestTemplateConfig
 * @date 2018/08/14 11:42
 * @description
 * @program holder-saas-store-trading-center
 */
@Configuration
public class RestTemplateConfig {

    @Bean(name = "remoteRestTemplate")
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
