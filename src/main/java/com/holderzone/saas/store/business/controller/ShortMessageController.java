package com.holderzone.saas.store.business.controller;

import com.holderzone.saas.store.business.service.ShortMessageService;
import com.holderzone.saas.store.dto.business.manage.ChargeDTO;
import com.holderzone.saas.store.dto.business.manage.ProductOrderDTO;
import com.holderzone.saas.store.dto.business.manage.ShortMsgRespDTO;
import com.holderzone.saas.store.dto.trade.ShortMsgConfigDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ShortMessageController
 * @date 2018/09/17 10:40
 * @description
 * @program holder-saas-store-business
 */
@RestController
@RequestMapping("/shortMsg")
public class ShortMessageController {

    private static final Logger logger = LoggerFactory.getLogger(ShortMessageController.class);

    private final ShortMessageService shortMessageService;

    @Autowired
    public ShortMessageController(ShortMessageService shortMessageService) {
        this.shortMessageService = shortMessageService;
    }

    @PostMapping("/index")
    public List<ChargeDTO> index() {
        return shortMessageService.getAll();
    }


    @PostMapping("/charge")
    public ShortMsgRespDTO charge(@RequestBody ProductOrderDTO productOrderDTO) {
        return shortMessageService.charge(productOrderDTO);
    }

    @PostMapping("/polling")
    public ShortMsgRespDTO hasPaid(@RequestBody ShortMsgRespDTO shortMsgRespDTO) {
        return shortMessageService.polling(shortMsgRespDTO);
    }

    @PostMapping("/query")
    public ShortMsgConfigDTO query() {
        return shortMessageService.query();
    }

    @PostMapping("/update")
    public boolean updateMessageConfig(@RequestBody ShortMsgConfigDTO shortMsgConfigDTO) {
        return shortMessageService.updateMessageConfig(shortMsgConfigDTO);
    }
}
