package com.holderzone.saas.store.business.entity.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className HandoverPayDetailDO
 * @date 18-10-8 下午2:44
 * @description 营业中心-交班-班次下的支付详情实体
 * @program holder-saas-store-staff
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class HandoverPayDetailDO {
    /**
     * 主键
     */
    private String id;

    /**
     * 支付记录guid
     */
    private Long guid;

    /**
     * 交接班记录guid
     */
    private String handoverRecordGuid;

    /**
     * 设备id
     */
    private String terminalId;

    /**
     * 支付方式
     */
    private String payType;

    /**
     * 支付方式名称
     */
    private String payTypeName;

    /**
     * 支付金额
     */
    private BigDecimal payMoney;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 支付方式属于哪种类型（1-销售，2-储值，3-预订 , 4-挂账）
     */
    private Integer payBelongType;
}
