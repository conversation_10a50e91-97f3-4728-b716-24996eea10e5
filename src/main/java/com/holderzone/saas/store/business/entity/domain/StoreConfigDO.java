package com.holderzone.saas.store.business.entity.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.holderzone.saas.store.enums.business.PrintItemOrderEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreConfigDO
 * @date 2018/07/29 下午4:05
 * @description //TODO
 * @program holder-saas-store-business-center
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreConfigDO {

    /**
     * 门店guid
     */
    @TableId("store_guid")
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 平板下单是否启用密码
     * 0=未启用
     * 1=已启用
     */
    private Integer enablePadPwd;

    /**
     * 是否启用划菜
     * 0=未启用
     * 1=已启用
     */
    private Integer enableMarkDish;

    /**
     * 是否启用会员价
     * 0=未启用
     * 1=已启用
     */
    private Integer enableMemPrice;

    /**
     * 是否启用交接班
     * 0=未启用
     * 1=已启用
     */
    private Integer enableHandover;

    /**
     * 微信点餐模式
     * 0=堂食
     * 1=快餐
     */
    private Integer wechatOrderMode;

    /**
     * 流水号模式
     * 0=自增
     * 1=随机
     */
    private Integer serialNumberMode;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 门店营业开始时间
     */
    private LocalTime businessStartTime;

    /**
     * 门店营业结束时间
     */
    private LocalTime businessEndTime;

    /**
     * 是否启用自动号牌
     * 0=未启用
     * 1=启用
     */
    private Integer enableAutoMark;

    /**
     * 初始号牌
     */
    private String initMark;

    /**
     * 备餐时间
     */
    private Integer prepTime;

    /**
     * 快餐出餐语音开关：0关闭 1开启
     */
    private Integer finishFoodVoiceSwitch;

    /**
     * 打印小票(点菜单、菜品清单)商品顺序
     * asc
     * desc
     *
     * @see PrintItemOrderEnum
     */
    private String printItemOrder;

    /**
     * 手动清台：0关闭 1开启
     */
    private Integer enableHandleClose;

    /**
     * 同一订单多次使用聚合支付：0关闭 1开启
     */
    private Integer enableMultiplePay;

    /**
     * 正餐支持换高价值或低价值的菜 0不支持 1支持
     */
    private Integer enableDineInChangeDiffValue;

    /**
     * 快餐餐支持换高价值或低价值的菜 0不支持 1支持
     */
    private Integer enableFastChangeDiffValue;

    /**
     * 结束号牌
     */
    private String endMark;

    /**
     * 是否全部设置项为null
     *
     * @return
     */
    public boolean isAllConfigNull() {
        return null == enablePadPwd
                && null == enableMarkDish
                && null == enableMemPrice
                && null == enableHandover
                && null == wechatOrderMode
                && null == serialNumberMode
                && null == businessStartTime
                && null == businessEndTime
                && null == enableAutoMark
                && null == initMark
                && null == prepTime
                && null == finishFoodVoiceSwitch
                && null == printItemOrder
                && null == enableHandleClose
                && null == enableMultiplePay
                && null == enableDineInChangeDiffValue
                && null == enableFastChangeDiffValue
                && null == endMark
                ;
    }
}
