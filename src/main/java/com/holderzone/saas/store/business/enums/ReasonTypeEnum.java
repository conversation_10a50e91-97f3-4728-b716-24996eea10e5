package com.holderzone.saas.store.business.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum ReasonTypeEnum {
    /**
     * 原因类型
     */
    RETURNGOODS(8, "退货原因"),
    GIVE(9, "赠送原因"),
    REVERSECHECKOUT(0, "反结账原因"),
    CANCELORDER(1, "作废订单原因"),
    CANCELRESERVATION(2, "取消预定原因"),
    REFUSEORDER(3, "外卖拒单原因"),
    RETURNORDER(4, "外卖退单原因"),
    REFUSEREFUND(5, "外卖拒绝退款原因"),
    RETURNDISH(6, "退菜原因"),
    GIVINGDISH(7, "赠菜原因"),
    REFUND(10, "退款原因");


    private int code;
    private String des;

    public static List<ReasonTypeEnum> queryAllReasonType() {
        return new ArrayList<>(Arrays.asList(ReasonTypeEnum.values()))
                .stream()
                .filter(x -> x.getCode() != 8 && x.getCode() != 9)
                .collect(Collectors.toList());
    }

    public static List<ReasonTypeEnum> querySuperMarketReasonType() {
        ArrayList<ReasonTypeEnum> reasonTypeEnums = new ArrayList<>(Arrays.asList(ReasonTypeEnum.values()));
        return reasonTypeEnums.stream().filter(x -> x.getCode() == 3 || x.getCode() == 4 || x.getCode() == 5 || x.getCode() == 8 || x.getCode() == 9).collect(Collectors.toList());
    }

    public static String queryReasonTypeByCode(int code) {
        return Arrays.stream(ReasonTypeEnum.values()).filter(t -> Objects.equals(t.getCode(), code)).findFirst().get().getDes();
    }
}
