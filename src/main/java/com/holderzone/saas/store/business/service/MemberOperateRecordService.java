package com.holderzone.saas.store.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.saas.store.business.entity.domain.MemberOperateRecordDO;
import com.holderzone.saas.store.dto.trade.req.record.MemberOperateRecordReqDTO;

/**
 * <p>
 * 会员操作记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
public interface MemberOperateRecordService extends IService<MemberOperateRecordDO> {

    void saveRecord(MemberOperateRecordReqDTO reqDTO);
}
