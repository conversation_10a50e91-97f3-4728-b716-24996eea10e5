package com.holderzone.saas.store.bo.weixin;

import com.holderzone.saas.store.dto.weixin.deal.SpecialsActivityAmountDTO;
import com.holderzone.saas.store.enums.weixin.MinPriceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value = "限时特价计算")
public class SpecialsActivityAmountBO {

    @ApiModelProperty(value = "参与活动的商品数量，已减去单品券")
    private BigDecimal currentCount;

    @ApiModelProperty(value = "商品原价")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "限时特价数额")
    private BigDecimal specialsNumber;

    @ApiModelProperty(value = "优惠限购是否限制")
    private boolean isLimit;

    @ApiModelProperty(value = "优惠限购")
    private BigDecimal limitNumber;

    @ApiModelProperty(value = "原优惠价")
    private BigDecimal minPrice;

    /**
     * 原优惠价类型
     *
     * @see MinPriceTypeEnum
     */
    @ApiModelProperty("原优惠价类型 0原价 1会员价 2会员折扣 3限时特价")
    private Integer minPriceType;

    @ApiModelProperty(value = "限时特价计算结果")
    private SpecialsActivityAmountDTO activityAmountDTO;

    /**
     * 是否首次进入
     */
    @ApiModelProperty(value = "是否首次进入")
    private Boolean isFirst = Boolean.TRUE;

    @ApiModelProperty(value = "是否替换")
    private Boolean isReplace;
}