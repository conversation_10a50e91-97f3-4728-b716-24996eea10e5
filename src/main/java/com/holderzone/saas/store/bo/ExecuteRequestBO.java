package com.holderzone.saas.store.bo;

import com.holderzone.saas.store.dto.takeaway.UnItemBindUnbindReq;
import lombok.Data;

import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @date 2023/11/3
 * @description
 */
@Data
public class ExecuteRequestBO {

    private UnItemBindUnbindReq unItemBindUnbindReq;

    private boolean isBinding;

    private String replyType;

    private String storeGuid;

    private ConcurrentMap<String, String> extendValueMap;

}
