package com.holderzone.saas.store.bo;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.response.ResultEnum;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.enums.common.ResultCodeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UserDefinedCodeBO
 * @date 2019/09/18 15:48
 * @description //自定义code传递对象
 * @program holder-saas-store-dto
 */
@Data
public class UserDefinedCodeBO {

    private Integer definedCode;

    private String definedMessage;

    public static String buildDefindResultJSONString(ResultCodeEnum resultCodeEnum) {
        UserDefinedCodeBO userDefinedCodeBO = new UserDefinedCodeBO();
        userDefinedCodeBO.setDefinedCode(resultCodeEnum.getCode());
        userDefinedCodeBO.setDefinedMessage(resultCodeEnum.getDesc());
        return JSON.toJSONString(userDefinedCodeBO);
    }
}
