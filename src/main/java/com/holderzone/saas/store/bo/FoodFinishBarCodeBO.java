package com.holderzone.saas.store.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/22
 * @description 出餐条码缓存信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "出餐条码缓存信息", description = "出餐条码缓存信息")
public class FoodFinishBarCodeBO {

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "本次订单商品")
    private List<String> orderItemGuidList;

}
