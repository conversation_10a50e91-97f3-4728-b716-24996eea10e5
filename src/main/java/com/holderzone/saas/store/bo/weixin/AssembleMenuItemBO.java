package com.holderzone.saas.store.bo.weixin;

import com.holderzone.holder.saas.member.wechat.dto.activitie.ResponseMarketActivitieRule;
import com.holderzone.saas.store.dto.item.resp.ItemAndTypeForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemEstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.marketing.nth.NthActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemVO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/29
 * @description
 */
@Data
public class AssembleMenuItemBO {

    private ItemAndTypeForAndroidRespDTO item;

    private  WxOrderConfigDTO storeConfig;

    private List<ItemEstimateForAndroidRespDTO> estimateSkuList;

    private ResponseMarketActivitieRule responseMarketActivitieRule;

    private Integer wxPermission;

    private Integer wxAddItemFlag;

    private  List<LimitSpecialsActivityItemVO> activityItemVOList;

    private List<NthActivityDetailsVO> nthActivityList;

    private Integer nthFlag;

}
