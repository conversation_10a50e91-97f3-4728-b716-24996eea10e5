package com.holderzone.saas.gateway.constans;

public interface AuthorizeWhitelist {

    interface PkgBusiness {

        interface BusinessController {
            String TIME = "/business/time";
            String GET_TIME = "/business/get/time";
        }

        interface PaymentTypeController {
            // 暂无
        }

        interface ShortMsgController {
            String INDEX = "/shortMsg/index";
            String CHARGE = "/shortMsg/charge";
            String POLLING = "/shortMsg/polling";
            String QUERY = "/shortMsg/query";
            String UPDATE = "/shortMsg/update";
        }

        interface SystemDiscountController {
            // 暂无
        }
    }

    interface PkgCommon {

        interface FileController {
            String UPLOAD = "/file/upload";
            String DELETE = "/file/delete";
        }

        interface MenuController {
            String GET_MERCHANT_MENU = "/menu/get_merchant_menu";
            String GET_SOURCE_BY_MENU = "/menu/get_source_by_menu";
        }

        interface RegionController {
            String PROVINCE = "/region/province";
            String CITY = "/region/city";
            String DISTRICT = "/region/district";
        }
    }

    interface PkgDevice {
        // 待添加
    }

    interface PkgDish {
        // 暂无
    }

    interface PkgItem {

        interface AttrController {
            // 暂无
        }

        interface ItemController {
            String GET_FIRST_CHAR = "/item/getFirstChar";
            String BRAND_UPDATE_ITEM_SORT = "/item/brand/update_item_sort";
            String STORE_UPDATE_ITEM_SORT = "/item/store/update_item_sort";
            String MAPPING = "/item/mapping";
        }

        interface ItemFileController {
            // 暂无
        }

        interface ItemPkgController {
            // 暂无
        }

        interface TypeController {
            String BRAND_GET_SORT = "/type/brand/get_sort";
            String STORE_GET_SORT = "/type/store/get_sort";
        }
    }

    interface PkgLog {

        interface LogController {
            String MODULE = "/log/module";
        }
    }

    interface PkgMember {

        interface MemberGradeMerchantController {
            // 暂无
        }

        interface MemberListMerchantController {
            String MEMBER_STORES = "/member/memberStores"; // 不清楚怎么用的所以归在这里
            String GET_MEMBER_INTEGRAL_TYPE = "/member/getMemberIntegralType"; // 不清楚怎么用的所以归在这里
        }
    }

    interface PkgOrder {

        interface OrderController {
            String GET_MEMBER_INTEGRAL_TYPE = "/order/getMemberIntegralType";
            String GET_ORDER_SOURCE = "/order/getOrderSource";
        }
    }

    interface PkgOrganization {

        interface OrganizationController {
            String QUERY_EXIST_STORE_ACCOUNT = "/brand/query_exist_store_account";
            String QUERY_EXIST_ORGANIZATION_OR_STORE = "/organization/query_exist_organization_or_store";
            String GET_OPTIONAL_ORGANIZATION = "/organization/get_optional_organization";
            String QUERY_EXIST_ACCOUNT = "/organization/query_exist_account";
        }
    }

    interface PkgReport {
        // 暂无
    }

    interface PkgTable {

        interface AreaController {
            // 暂无
        }

        interface TableController {
            // 暂无
        }
    }

    interface PkgTakeout {

        interface DevController {
            // 暂无
        }

        interface ElemeController {
            // 暂无
        }

        interface MeiTuanController {
            // 暂无
        }

        interface TakeoutController {
            String CALLBACK = "/takeout/callback";
        }
    }

    interface PkgUser {

        interface RoleController {
            // 暂无
        }

        interface RoleDataController {
            // 暂无
        }

        interface UserController {
            String NEW_ACCOUNT = "/user/new_account";
            String NEW_AUTH_CODE = "/user/new_auth_code";
            String NEW_USER_OFFICE = "/user/new_user_office";
            String LIST_USER_OFFICE = "/user/list_user_office";
            String GET_USER_BASE_INFO = "/user/get_user_base_info";
        }

        interface UserDataController {
            String QUERY_STORE_SPINNER = "/user_data/query_store_spinner";
            String QUERY_ORG_SPINNER = "/user_data/query_org_spinner";
            String QUERY_BRAND_SPINNER = "/user_data/query_brand_spinner";
            String QUERY_CONDITION_SPINNER = "/user_data/query_condition_spinner";
            String QUERY_STORE_AND_COND_SPINNER = "/user_data/query_store_and_cond_spinner";
            String QUERY_GENERIC_ORG_SPINNER = "/user_data/query_generic_org_spinner";
            String QUERY_ROLES_DISTRIBUTABLE = "/user_data/query_roles_distributable";
        }
    }

    interface PkgWeixin {

        interface WxStoreConfigController {
            String GET_ORDER_CONFIG_LIST = "/wxStoreOrderConfig/getOrderConfigList";
            String GET_DETAIL_CONFIG = "/wxStoreOrderConfig/getDetailConfig";
            String UPDATE_STORE_CONFIG = "/wxStoreOrderConfig/updateStoreConfig";

        }
    }
}
