package com.holderzone.saas.gateway.utils;

import com.holderzone.saas.gateway.entity.FilterConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.server.ServerWebExchange;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/05 上午 11:08
 * @description
 */
@Slf4j
public class URIFilter {


    private boolean filtered = false;

    private ServerWebExchange exchange;

    private URIFilter() {
    }

    private URIFilter(ServerWebExchange exchange) {
        this.exchange = exchange;
    }


    public static URIFilter createRequestFilter() {
        return new URIFilter();
    }

    public static URIFilter createRequestFilter(ServerWebExchange exchange) {
        return new URIFilter(exchange);
    }

    public URIFilter filterURL(String url) {
        String uri = exchange.getRequest().getURI().toString();
        if (uri.contains(url)) {
            filtered = true;
        }
        return this;
    }

    public URIFilter filterTerminal(String filterSource) {
        List<String> sourceList = exchange.getRequest().getHeaders().get(FilterConstant.SOURCE);
        if (sourceList != null) {
            String source = sourceList.stream().findFirst().orElse("");
            if (filterSource.equals(source)) {
                filtered = true;
            }
        }
        return this;
    }

    public boolean filtered() {
        return this.filtered;
    }

    public boolean urlIsInAuthorizationWhiteList(String url) {
        return isInWhiteList(url, FilterHelper.authorizationUrlWhiteList);
    }

    public boolean urlIsInAuthenticationWhiteList(String url) {
        return isInWhiteList(url, FilterHelper.authenticationUrlWhiteList);
    }

    public boolean isInWhiteList(String url, List<String> list) {
        boolean inWhiteList = false;
        if (!list.isEmpty()) {
            inWhiteList = list.stream().anyMatch(whiteUrl -> {
                if (url.contains(whiteUrl) || url.matches(whiteUrl)) {
                    return true;
                }
                return false;
            });

        }
        return inWhiteList;
    }


}
