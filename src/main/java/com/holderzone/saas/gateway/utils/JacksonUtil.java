package com.holderzone.saas.gateway.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.holderzone.framework.exception.JsonOperationException;
import com.holderzone.framework.util.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2018/09/19 下午 16:42
 * @description
 */
public class JacksonUtil {

    private static ObjectMapper objectMapper = null;

    static {
        objectMapper = new ObjectMapper();

        // 设置时区
        objectMapper.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));

        // 忽略空bean转json的错误
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        //设置输入时忽略JSON字符串中存在而Java对象实际没有的属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        objectMapper.registerModule(new JavaTimeModule());
    }


    /**
     * java对象转为字符串，null对象即为"null"
     *
     * @param obj
     * @return
     * @throws JsonProcessingException
     */
    public static String writeValueAsString(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            throw new JsonOperationException("JSON 转化异常！");
        }
    }

    /**
     * 将对象转换为JSON字符串二进制数组
     *
     * @param obj
     * @return byte[]
     * @throws
     * @Title: toJsonByte
     * @Description: TODO
     */
    public static byte[] toJsonByte(Object obj) {
        try {
            return objectMapper.writeValueAsBytes(obj);
        } catch (Exception e) {
            throw new JsonOperationException("将对象转换为JSON字符串二进制数组错误！！");
        }
    }

    /**
     * 将JSON字符串转换为对象
     *
     * @param clazz
     * @param json
     * @return T
     * @throws
     * @Title: toObject
     * @Description: TODO
     */
    public static <T> T toObject(Class<T> clazz, String json) {
        T obj = null;
        try {
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            obj = (T) objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            throw new JsonOperationException("json字符串转化错误！！");
        }
        return obj;
    }

    /**
     * 将Json字符串转换成List
     *
     * @param clazz
     * @param json
     * @return
     */
    public static <T> List<T> toObjectList(Class<T> clazz, String json) {
        try {
            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, clazz);
            return objectMapper.readValue(json,javaType);
        } catch (IOException e) {
            throw new JsonOperationException("json字符串转为list异常！！");
        }
    }
}
