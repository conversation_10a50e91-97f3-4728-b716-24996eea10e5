package com.holderzone.saas.gateway.utils;

import com.holderzone.framework.response.Result;
import com.holderzone.saas.gateway.entity.MessageEnum;
import org.reactivestreams.Publisher;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2019/06/01 下午 14:45
 * @description
 */
public class ResponseUtil {

    /**
     * 认证失败返回结果
     *
     * @param exchange
     * @return
     */
    public static Mono<Void> unAuthenResponse(ServerWebExchange exchange,GatewayFilterChain chain) {
        Result<Object> result = Result.buildFailResult(HttpStatus.UNAUTHORIZED.value(), MessageEnum.getLocale(MessageEnum.AUTHENTICATION_FAILURE));
        return buildAndReleaseGatewayFilterResult(exchange,result,chain);
    }

    public static Mono<Void> unAuthenResponse(ServerWebExchange exchange,WebFilterChain chain) {
        Result<Object> result = Result.buildFailResult(HttpStatus.UNAUTHORIZED.value(), MessageEnum.getLocale(MessageEnum.AUTHENTICATION_FAILURE));
        return buildAndReleaseWebFilterResult(exchange,result,chain);
    }

    /**
     * 错误请求返回结果
     *
     * @param exchange
     * @param exceptionMessage
     * @return
     */
    public static Mono<Void> badRequestResponse(ServerWebExchange exchange, String exceptionMessage,GatewayFilterChain chain) {
        Result<Object> result = Result.buildFailResult(HttpStatus.BAD_REQUEST.value(), exceptionMessage);
        return buildAndReleaseGatewayFilterResult(exchange,result,chain);
    }

    /**
     * 授权失败返回结果
     *
     * @param exchange
     * @param exceptionMessage
     * @return
     */
    public static Mono<Void> unAuthorResponse(ServerWebExchange exchange, String exceptionMessage,GatewayFilterChain chain) {
        Result<Object> result = Result.buildFailResult(HttpStatus.METHOD_NOT_ALLOWED.value(), exceptionMessage);
        return buildAndReleaseGatewayFilterResult(exchange,result,chain);
    }

    /**
     * 存在敏感词汇的返回结果
     *
     * @param exchange
     * @return
     */
    public static Mono<Void> unSuitableContentResponse(ServerWebExchange exchange,String content,GatewayFilterChain chain) {
        Result<Object> result = Result.buildFailResult(HttpStatus.FORBIDDEN.value(),  content+"为敏感词汇，不能使用");
        return buildAndReleaseGatewayFilterResult(exchange,result,chain);
    }

    private static Mono<Void> buildAndReleaseWebFilterResult(ServerWebExchange exchange,Result<Object> result,WebFilterChain chain){
        ServerHttpResponseDecorator serverHttpResponseDecorator = buildResponseDecorator(exchange,result);
        return chain.filter(exchange.mutate().response(serverHttpResponseDecorator).build());
    }


    private static Mono<Void> buildAndReleaseGatewayFilterResult(ServerWebExchange exchange, Result<Object> result, GatewayFilterChain chain) {
        ServerHttpResponseDecorator serverHttpResponseDecorator = buildResponseDecorator(exchange,result);
        return chain.filter(exchange.mutate().response(serverHttpResponseDecorator).build());
    }

    private static ServerHttpResponseDecorator buildResponseDecorator(ServerWebExchange exchange, Result<Object> result){
        ServerHttpResponse serverHttpResponse = exchange.getResponse();
        DataBufferFactory dataBufferFactory = serverHttpResponse.bufferFactory();
        byte[] resultBytes = JacksonUtil.toJsonByte(result);
        serverHttpResponse.getHeaders().add("Content-Type", "application/json;charset=UTF-8");

        return new ServerHttpResponseDecorator(serverHttpResponse){
            @Override
            public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {

                Flux<? extends DataBuffer> flux = (Flux<? extends DataBuffer>) body;
                return super.writeWith(flux.buffer().map(dataBuffers -> {
                    dataBuffers.forEach(dataBuffer -> {
                        byte[] content = new byte[dataBuffer.readableByteCount()];
                        dataBuffer.read(content);
                        DataBufferUtils.release(dataBuffer);
                    });
                    return dataBufferFactory.wrap(resultBytes);
                }));

            }
        };
    }


}
