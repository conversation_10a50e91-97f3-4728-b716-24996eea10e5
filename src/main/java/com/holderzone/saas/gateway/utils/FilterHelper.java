package com.holderzone.saas.gateway.utils;

import org.apache.commons.lang.text.StrBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/01/02 下午 18:39
 * @description
 */
public class FilterHelper {

    private static final Logger log = LoggerFactory.getLogger(FilterHelper.class);

    public static List<String> authorizationUrlWhiteList = new ArrayList<>();
    public static List<String> authenticationUrlWhiteList = new ArrayList<>();

    public static Map sensitiveWordMap = new HashMap(8);

    /**
     * 判断传入文本是否存在敏感词汇
     *
     * @param txt
     * @return
     */
    public static String existSensitiveWord(String txt) {
        StrBuilder builder = new StrBuilder();
        Map nowMap = sensitiveWordMap;
        Map wordMap = null;
        char word = 0;
        for (int i = 0; i < txt.length(); i++) {
            word = txt.charAt(i);
            wordMap = (Map) nowMap.get(word);
            if (wordMap != null) {
                builder.append(word);
                if (1 == (Integer) wordMap.get("isEnd")) {
                    log.info("存在敏感词：{}", builder.toString());
                    return builder.toString();
                }
                nowMap = wordMap;
            } else {
                nowMap = sensitiveWordMap;
                builder.clear();
                if (sensitiveWordMap.containsKey(word)) {
                    i--;
                }
            }
        }
        return "";
    }
}
