package com.holderzone.saas.gateway.exception;

/**
 * 发生此异常时，放过请求
 *
 * <AUTHOR>
 * @date 2020/01/07 上午 10:40
 * @description
 */
public class FilterRequestException extends RuntimeException {

    public FilterRequestException() {
    }

    public FilterRequestException(String message) {
        super(message);
    }

    public FilterRequestException(String message, Throwable cause) {
        super(message, cause);
    }

    public FilterRequestException(Throwable cause) {
        super(cause);
    }
}
