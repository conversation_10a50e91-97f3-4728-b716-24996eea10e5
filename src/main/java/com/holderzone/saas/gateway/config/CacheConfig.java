package com.holderzone.saas.gateway.config;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.holderzone.saas.gateway.entity.EnterpriseDTO;
import com.holderzone.saas.gateway.entity.OrganizationDTO;
import com.holderzone.saas.gateway.entity.UserInfo;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2018/10/11 上午 11:19
 * @description
 */
@Configuration
public class CacheConfig {

    private static final Long CACHE_TIME = 3L;

    @Bean
    public Cache<String, UserInfo> userInfoCache() {
        return baseCacheAfterWrite(10, TimeUnit.MINUTES, 5000);
    }

    @Bean
    public Cache<String, EnterpriseDTO> enterpriseCache() {
        return baseCacheAfterAccess(10, TimeUnit.MINUTES, 5000);
    }

    @Bean
    public Cache<String, OrganizationDTO> organizationCache() {
        return baseCacheAfterAccess(10, TimeUnit.MINUTES, 5000);
    }

    private Cache baseCacheAfterWrite(long cacheTime, TimeUnit timeUnit, Integer size) {
        return CacheBuilder.newBuilder()
                .maximumSize(size)
                .expireAfterWrite(cacheTime, timeUnit)
                .build();
    }

    private Cache baseCacheAfterAccess(long cacheTime, TimeUnit timeUnit, Integer size) {
        return CacheBuilder.newBuilder()
                .maximumSize(size)
                .expireAfterAccess(cacheTime, timeUnit)
                .build();
    }
}
