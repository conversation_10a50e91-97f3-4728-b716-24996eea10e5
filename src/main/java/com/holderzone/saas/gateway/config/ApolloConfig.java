package com.holderzone.saas.gateway.config;

import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * web app configure
 *
 * <AUTHOR>
 * @date 17-12-5
 */
@Configuration
@EnableApolloConfig
@Profile({"test", "release", "prod"})
public class ApolloConfig implements ApplicationContextAware {

    private static final Logger logger = LoggerFactory.getLogger(ApolloConfig.class);

    private ApplicationContext applicationContext;

    @ApolloConfigChangeListener(value = {
            "application",
            "application-test",
            "application-release",
            "application-prod",
    })
    public void onChange(ConfigChangeEvent changeEvent) {
        logger.info("Refreshing properties!");
        for (String key : changeEvent.changedKeys()) {
            ConfigChange change = changeEvent.getChange(key);
            logger.info("Found change - {}", change.toString());
        }
        applicationContext.publishEvent(new EnvironmentChangeEvent(changeEvent.changedKeys()));
        logger.info("Properties refreshed!");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
