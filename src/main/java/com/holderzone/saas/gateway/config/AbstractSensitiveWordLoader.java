package com.holderzone.saas.gateway.config;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/01/02 下午 18:46
 * @description
 */
public abstract class AbstractSensitiveWordLoader {


    /**
     * 读取敏感词库，将敏感词放入HashSet中，构建一个DFA算法模型
     *
     * @param keyWordSet
     */
    protected Map addSensitiveWordToHashMap(Set<String> keyWordSet) {
        if (keyWordSet.isEmpty()) {
            return new HashMap(1);
        }
        Map sensitiveWordMap = new HashMap<>(keyWordSet.size());
        for (String key : keyWordSet) {
            addSensitiveWord(sensitiveWordMap, key);
        }
        return sensitiveWordMap;
    }

    protected void addSensitiveWord(Map sensitiveWordMap, String key) {
        Map currentMap = sensitiveWordMap;
        Map nextMap = null;
        for (int i = 0; i < key.length(); i++) {
            char keyChar = key.charAt(i);
            Object wordMap = currentMap.get(keyChar);
            if (wordMap != null) {
                currentMap = (Map) wordMap;
            } else {
                nextMap = new HashMap<>(16);
                nextMap.put("isEnd", 0);
                currentMap.put(keyChar, nextMap);
                currentMap = nextMap;
            }

            if (i == key.length() - 1) {
                currentMap.put("isEnd", 1);
            }
        }
    }
}
