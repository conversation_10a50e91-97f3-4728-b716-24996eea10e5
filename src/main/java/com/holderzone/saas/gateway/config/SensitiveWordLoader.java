package com.holderzone.saas.gateway.config;

import com.ctrip.framework.apollo.ConfigFile;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.core.enums.ConfigFileFormat;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.gateway.utils.FilterHelper;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.Set;

import static com.holderzone.saas.gateway.constans.CommonConstant.SENSITIVE_WORD;
import static com.holderzone.saas.gateway.utils.CommonUtil.resolveApolloContent;
import static com.holderzone.saas.gateway.utils.CommonUtil.sleep;

/**
 * 初始化敏感词库，将敏感词加入到HashMap中，构建DFA算法模型
 *
 * <AUTHOR>
 * @date 2019/06/01 下午 13:54
 * @description
 */
@Configuration
@Profile({"test", "release", "prod"})
@EnableApolloConfig(value = SENSITIVE_WORD + ".txt")
public class SensitiveWordLoader extends AbstractSensitiveWordLoader implements ApplicationListener<ApplicationStartedEvent> {

    private static final Logger log = LoggerFactory.getLogger(SensitiveWordLoader.class);

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        log.info("开始加载敏感词配置");
        ConfigFile configFile = ConfigService.getConfigFile(SENSITIVE_WORD, ConfigFileFormat.TXT);
        String content = configFile.getContent();
        if (StringUtils.isEmpty(content)) {
            log.info("没有拉取到apollo的敏感词配置");
            throw new BusinessException("没有拉取到apollo的敏感词配置");
        }
        Set<String> keyWordSet = resolveApolloContent(content);
        FilterHelper.sensitiveWordMap = addSensitiveWordToHashMap(keyWordSet);
    }

    @ApolloConfigChangeListener({SENSITIVE_WORD + ".txt"})
    private void sensitiveWordsOnChange(ConfigChangeEvent changeEvent) {
        log.info(SENSITIVE_WORD + "配置更新,睡眠5秒，等待内存更新......");
        sleep(5000L);
        log.info("加载" + SENSITIVE_WORD + "配置");
        ConfigFile file = ConfigService.getConfigFile(SENSITIVE_WORD, ConfigFileFormat.TXT);
        String content = file.getContent();
        Set<String> contentSet = resolveApolloContent(content);
        FilterHelper.sensitiveWordMap = addSensitiveWordToHashMap(contentSet);
    }



}
