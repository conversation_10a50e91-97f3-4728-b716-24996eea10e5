package com.holderzone.saas.gateway.config;

import com.ctrip.framework.apollo.ConfigFile;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.core.enums.ConfigFileFormat;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.gateway.utils.FilterHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.ArrayList;
import java.util.Set;

import static com.holderzone.saas.gateway.constans.CommonConstant.AUTHENTICATION_WHITELIST;
import static com.holderzone.saas.gateway.constans.CommonConstant.AUTHORIZATION_WHITE;
import static com.holderzone.saas.gateway.utils.CommonUtil.resolveApolloContent;
import static com.holderzone.saas.gateway.utils.CommonUtil.sleep;

/**
 * <AUTHOR>
 * @date 2019/02/26 下午 16:10
 * @description
 */
@Configuration
@Profile({"test", "release", "prod"})
@EnableApolloConfig(value = {AUTHENTICATION_WHITELIST + ".txt", AUTHORIZATION_WHITE + ".txt"})
public class UrlWhiteLoader implements ApplicationListener<ApplicationStartedEvent> {

    private static final Logger log = LoggerFactory.getLogger(UrlWhiteLoader.class);

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        log.info("开始加载白名单配置");
        FilterHelper.authenticationUrlWhiteList.addAll(loadWhiteContent(AUTHENTICATION_WHITELIST));
        FilterHelper.authorizationUrlWhiteList.addAll(loadWhiteContent(AUTHORIZATION_WHITE));

    }

    @ApolloConfigChangeListener({AUTHENTICATION_WHITELIST + ".txt"})
    private void authenticationOnChange(ConfigChangeEvent changeEvent) {
        log.info(AUTHENTICATION_WHITELIST + "配置更新,睡眠5秒，等待内存更新......");
        sleep(5000L);
        log.info("加载" + AUTHENTICATION_WHITELIST + "配置");
        ConfigFile configFile = ConfigService.getConfigFile(AUTHENTICATION_WHITELIST, ConfigFileFormat.TXT);
        String content = configFile.getContent();
        Set<String> set = resolveApolloContent(content);
        FilterHelper.authenticationUrlWhiteList = new ArrayList<>(set);
    }

    @ApolloConfigChangeListener({AUTHORIZATION_WHITE + ".txt"})
    private void authorizationOnChange(ConfigChangeEvent changeEvent) {
        log.info(AUTHORIZATION_WHITE + "配置更新,睡眠5秒，等待内存更新......");
        sleep(5000L);
        log.info("加载" + AUTHORIZATION_WHITE + "的配置");
        ConfigFile configFile = ConfigService.getConfigFile(AUTHORIZATION_WHITE, ConfigFileFormat.TXT);
        String content = configFile.getContent();
        Set<String> set = resolveApolloContent(content);
        FilterHelper.authorizationUrlWhiteList = new ArrayList<>(set);

    }


    /**
     * 加载apollo的白名单内容
     *
     * @param file
     * @return
     */
    private Set<String> loadWhiteContent(String file) {
        ConfigFile configFile = ConfigService.getConfigFile(file, ConfigFileFormat.TXT);
        String content = configFile.getContent();
        if (StringUtils.isEmpty(content)) {
            log.info("没有读取到apollo的" + file + "配置");
            throw new BusinessException("没有读取到apollo的" + file + "配置");
        }
        return resolveApolloContent(content);
    }


}
