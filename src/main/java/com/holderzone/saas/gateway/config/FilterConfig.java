package com.holderzone.saas.gateway.config;

import com.google.common.cache.Cache;
import com.holderzone.saas.gateway.entity.EnterpriseDTO;
import com.holderzone.saas.gateway.entity.OrganizationDTO;
import com.holderzone.saas.gateway.entity.UserInfo;
import com.holderzone.saas.gateway.filter.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * <AUTHOR>
 * @date 2018/09/11 下午 14:53
 * @description
 */
@Configuration
public class FilterConfig {

    @Value("${switch.offline.enabled:false}")
    private Boolean isOfflineEnabled;

    @Value("${switch.offline.gray.include:all}")
    private String offlineGrayInclude;

    @Value("${switch.offline.gray.exclude:}")
    private String offlineGrayExclude;

    @Value("${spring.cloud.gateway.elapsedTime.gateway:false}")
    private Boolean gatewayElapsedTime;


    @Bean
    @ConditionalOnProperty(prefix = "spring.cloud.gateway", value = "author", havingValue = "true", matchIfMissing = false)
    public AuthorizationFilter authorizationFilter(WebClient.Builder webClientBuilder) {
        return new AuthorizationFilter(webClientBuilder);
    }

    @Bean
    @ConditionalOnProperty(prefix = "spring.cloud.gateway", value = "authen", havingValue = "true", matchIfMissing = false)
    public AuthenticationFilter authenticationFilter(Cache<String, UserInfo> userInfoCache, Cache<String, EnterpriseDTO> enterpriseCache,
                                                     Cache<String, OrganizationDTO> organizationCache, WebClient.Builder webClientBuilder) {
        return new AuthenticationFilter(userInfoCache, enterpriseCache, organizationCache, webClientBuilder);
    }

    @Bean
    @ConditionalOnProperty(prefix = "spring.cloud.gateway", value = "offline", havingValue = "true", matchIfMissing = false)
    public OfflineDeviceRequestFilter offlineDeviceRequestFilter(WebClient.Builder webClientBuilder) {
        return new OfflineDeviceRequestFilter(isOfflineEnabled, offlineGrayInclude, offlineGrayExclude, webClientBuilder);
    }

    @Bean
    @ConditionalOnProperty(prefix = "spring.cloud.gateway", value = "sensitive", havingValue = "true", matchIfMissing = false)
    public SensitiveWordFilter sensitiveWordFilter() {
        return new SensitiveWordFilter();
    }

    @Bean
    public StripPrefixFilter stripPrefixFilter() {
        return new StripPrefixFilter();
    }


    @Bean
    public GatewayContextFilter gatewayContextFilter() {
        return new GatewayContextFilter(gatewayElapsedTime);
    }


}
