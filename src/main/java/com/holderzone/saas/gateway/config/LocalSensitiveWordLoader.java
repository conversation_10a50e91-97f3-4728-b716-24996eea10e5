package com.holderzone.saas.gateway.config;

import com.holderzone.saas.gateway.utils.FilterHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.Set;

import static com.holderzone.saas.gateway.constans.CommonConstant.SENSITIVE_WORD;
import static com.holderzone.saas.gateway.utils.CommonUtil.readFile;

/**
 * 初始化敏感词库，将敏感词加入到HashMap中，构建DFA算法模型
 *
 * <AUTHOR>
 * @date 2019/06/01 下午 13:54
 * @description
 */
@Configuration
@Profile({"pc", "dev", "local"})
public class LocalSensitiveWordLoader extends AbstractSensitiveWordLoader implements ApplicationListener<ApplicationStartedEvent> {

    private static final Logger log = LoggerFactory.getLogger(LocalSensitiveWordLoader.class);

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        log.info("开始加载敏感词配置");
        Set<String> keyWordSet = readFile(SENSITIVE_WORD + ".txt");
        FilterHelper.sensitiveWordMap = addSensitiveWordToHashMap(keyWordSet);
    }


}
