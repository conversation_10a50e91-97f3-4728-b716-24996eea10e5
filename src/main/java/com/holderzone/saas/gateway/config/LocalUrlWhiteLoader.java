package com.holderzone.saas.gateway.config;

import com.holderzone.saas.gateway.utils.FilterHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import static com.holderzone.saas.gateway.constans.CommonConstant.AUTHENTICATION_WHITELIST;
import static com.holderzone.saas.gateway.constans.CommonConstant.AUTHORIZATION_WHITE;
import static com.holderzone.saas.gateway.utils.CommonUtil.readFile;

/**
 * <AUTHOR>
 * @date 2019/02/26 下午 16:10
 * @description
 */
@Configuration
@Profile({"pc", "dev", "local"})
public class LocalUrlWhiteLoader implements ApplicationListener<ApplicationStartedEvent> {

    private static final Logger log = LoggerFactory.getLogger(LocalUrlWhiteLoader.class);

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
        log.info("开始加载白名单配置");
        FilterHelper.authenticationUrlWhiteList.addAll(readFile(AUTHENTICATION_WHITELIST+".txt"));
        FilterHelper.authorizationUrlWhiteList.addAll(readFile(AUTHORIZATION_WHITE+".txt"));

    }


}
