package com.holderzone.saas.gateway.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

import static com.holderzone.saas.gateway.utils.ResponseUtil.unAuthenResponse;


/**
 * <AUTHOR>
 * @date 2019/02/27 下午 17:08
 * @description
 */
@Component
public class BasicAuthenInterceptor implements WebFilter {

    @Value("${spring.cloud.gateway.interceptor:/actuator/gateway/.*}")
    private String interceptorStr;
    @Value("${spring.cloud.gateway.basic-auth:Basic YWRtaW46cHVibGlj}")
    private String basicAuthentication;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        String[] interceptorList = interceptorStr.split(",");
        String path = exchange.getRequest().getURI().getPath();
        boolean match = Arrays.stream(interceptorList).anyMatch(regex -> Pattern.matches(regex, path));
        if (match){
            List<String> authorizationList = exchange.getRequest().getHeaders().get("Authorization");
            if (authorizationList == null || authorizationList.isEmpty()){
                return unAuthenResponse(exchange,chain);
            }else{
                if (!basicAuthentication.equals(authorizationList.get(0))){
                    return unAuthenResponse(exchange,chain);
                }
            }
        }
        return chain.filter(exchange);
    }
}
