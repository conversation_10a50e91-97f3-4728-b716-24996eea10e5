package com.holderzone.saas.gateway.config;

import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * <AUTHOR>
 * @date 2020/01/04 下午 18:18
 * @description
 */
@Configuration
public class WebClientConfig {

    @Bean
    @LoadBalanced
    public WebClient.Builder loadBalancedWebClientBuilder() {
        return WebClient.builder();
    }

//    @Bean
//    public WebClient webClient(WebClient.Builder loadBalancedWebClientBuilder) {
//        return loadBalancedWebClientBuilder.clientConnector(new ReactorClientHttpConnector()).build();
//    }
}
