package com.holderzone.saas.gateway.filter;

import com.holderzone.saas.gateway.constans.TerminalConstant;
import com.holderzone.saas.gateway.entity.AuthDTO;
import com.holderzone.saas.gateway.entity.MessageEnum;
import com.holderzone.saas.gateway.entity.UserInfo;
import com.holderzone.saas.gateway.exception.BadRequestException;
import com.holderzone.saas.gateway.utils.URIFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import static com.holderzone.saas.gateway.utils.ResponseUtil.badRequestResponse;
import static com.holderzone.saas.gateway.utils.ResponseUtil.unAuthorResponse;


/**
 * 权限验证的filter
 *
 * <AUTHOR>
 * @date 2018/02/21 下午 14:00
 * @description
 */
public class AuthorizationFilter extends BaseFilter implements GlobalFilter, Ordered {

    private static final Logger log = LoggerFactory.getLogger(AuthorizationFilter.class);

    private static final Integer ORDER = 15;

    private static final String ACCOUNT_ADMIN = "100000";

    private static final String AUTHOR_URL = "http://holder-saas-store-staff/user/check_authorization";

    private WebClient.Builder webClientBuilder;

    public AuthorizationFilter(WebClient.Builder webClientBuilder) {
        this.webClientBuilder = webClientBuilder;
    }


    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        if (filterRequest(exchange)) {
            return chain.filter(exchange);
        }
        AuthDTO authDTO = getAuthDtoFromRequest(exchange);
        if (authDTO == null) {
            return badRequestResponse(exchange, MessageEnum.getLocale(MessageEnum.HEADER_INCORRECT),chain);
        }
        //访问记录
        log.info("访问记录：{}",authDTO);
        return webClientBuilder.build()
                .post()
                .uri(AUTHOR_URL)
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .body(Mono.just(authDTO), AuthDTO.class)
                .retrieve()
                .bodyToMono(Boolean.class)
                .switchIfEmpty(Mono.error(new BadRequestException(MessageEnum.getLocale(MessageEnum.AUTHORIZATION_DATA_EMPTY))))
                .flatMap(validate -> {
                    if (!validate) {
                        log.error("访问记录没有权限");
                        return unAuthorResponse(exchange, MessageEnum.getLocale(MessageEnum.PERMISSION_DENIED),chain);
                    }
                    return chain.filter(exchange);
                })
                .onErrorResume(cause -> handleException(chain, exchange, cause));

    }

    /**
     * 组装authDto信息
     *
     * @param exchange
     * @return
     */
    private AuthDTO getAuthDtoFromRequest(ServerWebExchange exchange) {
        UserInfo userInfo = retrieveUserInfoFromHeader(exchange);
        if (userInfo == null) {
            log.error("用户信息为空");
            return null;
        }
        String terminalCode = retrieveTerminalCodeFromHeader(exchange);
        if (StringUtils.isEmpty(terminalCode)) {
            log.error("terminalCode为空");
            return null;
        }
        String menuGuid = retrieveMenuGuidFromHeader(exchange);
        if (StringUtils.isEmpty(menuGuid)) {
            log.error("menuGuid为空");
            return null;
        }
        String uriPath = exchange.getRequest().getURI().getPath();
        uriPath = uriPath.substring(uriPath.indexOf("/", 1));
        return new AuthDTO(userInfo, terminalCode, menuGuid, uriPath);
    }


    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + ORDER * 1000;
    }

    @Override
    protected boolean filterTerminal(ServerWebExchange exchange) {
        URIFilter uriFilter = URIFilter.createRequestFilter(exchange)
                .filterTerminal(TerminalConstant.TERMINAL_AIO)
                .filterTerminal(TerminalConstant.TERMINAL_POS)
                .filterTerminal(TerminalConstant.TERMINAL_PAD)
                .filterTerminal(TerminalConstant.TERMINAL_MOBILE)
                .filterTerminal(TerminalConstant.TERMINAL_POS_FINANCIAL)
                .filterTerminal(TerminalConstant.TERMINAL_WEIXIN)
                .filterTerminal(TerminalConstant.TERMINAL_KDS)
                .filterTerminal(TerminalConstant.TERMINAL_BOSS)
                .filterTerminal(TerminalConstant.TERMINAL_SELF)
                .filterTerminal(TerminalConstant.TERMINAL_WEIXIN_PROCEDURE)
                .filterTerminal(TerminalConstant.TERMINAL_TV)
                .filterTerminal(TerminalConstant.TERMINAL_AGENT)
                .filterTerminal(TerminalConstant.TERMINAL_CLOUD)
                .filterTerminal(TerminalConstant.ZHUAN_CAN_WEIXIN)
                .filterTerminal(TerminalConstant.ALIPAY_APPLETS);
        return uriFilter.filtered();
    }

    @Override
    protected boolean filterRequest(ServerWebExchange exchange) {
        if (filterTerminal(exchange)) {
            return true;
        }
        boolean isInAuthenticationWhiteList = URIFilter.createRequestFilter()
                .urlIsInAuthenticationWhiteList(exchange.getRequest().getURI().toString());
        if (isInAuthenticationWhiteList) {
            return true;
        }
        boolean isInAuthorizationWhiteList = URIFilter.createRequestFilter()
                .urlIsInAuthorizationWhiteList(exchange.getRequest().getURI().toString());
        if (isInAuthorizationWhiteList) {
            return true;
        }
        return false;
    }


}

