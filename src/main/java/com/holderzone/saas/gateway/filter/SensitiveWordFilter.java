package com.holderzone.saas.gateway.filter;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.gateway.entity.GatewayContext;
import com.holderzone.saas.gateway.utils.FilterHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import static com.holderzone.saas.gateway.entity.GatewayContext.CACHE_GATEWAY_CONTEXT;
import static com.holderzone.saas.gateway.utils.ResponseUtil.unSuitableContentResponse;

/**
 * <AUTHOR>
 * @date 2019/06/01 上午 11:40
 * @description
 */
public class SensitiveWordFilter  implements GlobalFilter, Ordered {

    private static final Logger log = LoggerFactory.getLogger(SensitiveWordFilter.class);

    /**
     * filter的顺序
     */
    private static final Integer ORDER = 12;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        GatewayContext gatewayContext  = exchange.getAttribute(CACHE_GATEWAY_CONTEXT);
        return Mono.just(exchange)
                .flatMap(webExchange -> {
                    String unsuitableContent = "";
                    if(gatewayContext != null && gatewayContext.getCacheBody() != null){
                        unsuitableContent = FilterHelper.existSensitiveWord(JacksonUtils.writeValueAsString(gatewayContext.getCacheBody()));
                    }
                    if (!StringUtils.isEmpty(unsuitableContent)){
                        return unSuitableContentResponse(exchange,unsuitableContent,chain);
                    }else{
                        return chain.filter(webExchange);
                    }
                });
    }



    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + ORDER * 1000;
    }
}
