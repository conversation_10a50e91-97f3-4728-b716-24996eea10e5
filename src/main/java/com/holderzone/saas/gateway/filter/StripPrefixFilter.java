package com.holderzone.saas.gateway.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.stream.Collectors;

import static com.holderzone.saas.gateway.constans.CommonConstant.GATEWAY_START_TIME;
import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR;
import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.addOriginalRequestUrl;

/**
 * 跳过前缀的filter，如/test1/a,经过拦截器后变为/a
 *
 * <AUTHOR>
 * @date 2018/09/11 下午 20:26
 * @description
 */
public class StripPrefixFilter implements GlobalFilter, Ordered {

    private static final Logger log = LoggerFactory.getLogger(StripPrefixFilter.class);

    /**
     * 跳过前缀的数量
     */
    private static final Integer STRIP_PREFIX_COUNT = 1;

    /**
     * filter的顺序
     */
    private static final Integer ORDER = 16;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        addOriginalRequestUrl(exchange, request.getURI());
        String path = request.getURI().getRawPath();
        String newPath = "/" + Arrays.stream(StringUtils.tokenizeToStringArray(path, "/"))
                .skip(STRIP_PREFIX_COUNT).collect(Collectors.joining("/"));
        ServerHttpRequest newRequest = request.mutate()
                .path(newPath)
                .build();

        exchange.getAttributes().put(GATEWAY_REQUEST_URL_ATTR, newRequest.getURI());

        return chain.filter(exchange.mutate().request(newRequest).build());

    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + ORDER * 1000;
    }
}
