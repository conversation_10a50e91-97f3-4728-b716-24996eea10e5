package com.holderzone.saas.gateway.filter;

import com.google.common.cache.Cache;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.gateway.constans.TerminalConstant;
import com.holderzone.saas.gateway.entity.*;
import com.holderzone.saas.gateway.exception.AuthenticationException;
import com.holderzone.saas.gateway.exception.BadRequestException;
import com.holderzone.saas.gateway.utils.CommonUtil;
import com.holderzone.saas.gateway.utils.JacksonUtil;
import com.holderzone.saas.gateway.utils.URIFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

import static com.holderzone.saas.gateway.entity.FilterConstant.USER_INFO;
import static com.holderzone.saas.gateway.utils.ResponseUtil.unAuthenResponse;


/**
 * 验证token是否有效的filter
 *
 * <AUTHOR>
 * @date 2018/09/11 下午 14:49
 * @description
 */
public class AuthenticationFilter extends BaseFilter implements GlobalFilter, Ordered {
    /**
     * filter的顺序
     */
    private static final Integer ORDER = 13;

    private static final Logger log = LoggerFactory.getLogger(AuthenticationFilter.class);

    private Cache<String, UserInfo> userInfoCache;

    private Cache<String, EnterpriseDTO> enterpriseCache;

    private Cache<String, OrganizationDTO> organizationCache;

    private WebClient.Builder webClientBuilder;

    public AuthenticationFilter(Cache<String, UserInfo> userInfoCache, Cache<String, EnterpriseDTO> enterpriseCache,
                                Cache<String, OrganizationDTO> organizationCache, WebClient.Builder webClientBuilder) {
        this.userInfoCache = userInfoCache;
        this.enterpriseCache = enterpriseCache;
        this.organizationCache = organizationCache;
        this.webClientBuilder = webClientBuilder;
    }

    private static final String USER_INFO_URL = "http://holder-saas-cloud-user/user/info?userGuid=%s&enterpriseGuid=%s";
    private static final String ENTERPRISE_INFO_URL = "http://holder-saas-cloud-enterprise/enterprise/find_with_commercial_parsed";
    private static final String STORE_INFO_URL = "http://holder-saas-cloud-enterprise/organization/store/info/";
    private static final String TOKEN_VERIFY_URL = "http://holder-saas-sso/ssoserver";
    private static final String CLOUD_USER_INFO_URL = "http://holder-saas-cloud-system/user/cloud/getUserInfo?userGuid=";
    private static final String CLOUD_AGENT_USER_INFO_URL = "http://holder-saas-cloud-agent/agent_user/";
    private static final String CLOUD_MEMBER_INFO_URL = "http://holder-saas-cloud-enterprise/enterprise/member/info/";
    private static final String CLOUD_MULTI_MEMBER_URL = "http://holder-saas-cloud-enterprise/multi/member/";

    private static final String ALLIANCE_ID = "********************************";
    private static final Integer AIO_SOURCE_VALUE = 3;


    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        HttpHeaders httpHeaders = exchange.getRequest().getHeaders();
        CommonUtil.transferHeaderLanguage(httpHeaders);
        Boolean isAlliance = Boolean.parseBoolean(retrieveValueFromHeader(exchange, FilterConstant.IS_ALLIANCE));
        String operSubjectGuid = retrieveValueFromHeader(exchange, FilterConstant.OPER_SUBJECT_GUID);
        boolean isCheckToken = true;
        if (Objects.nonNull(retrieveValueFromHeader(exchange, FilterConstant.IS_CHECK_TOKEN))) {
            isCheckToken = Boolean.parseBoolean(retrieveValueFromHeader(exchange, FilterConstant.IS_CHECK_TOKEN));
        }
        String enterpriseGuid = retrieveValueFromHeader(exchange, FilterConstant.ENTERPRISE_GUID_UP);
        Boolean multiMemberStatus = Boolean.parseBoolean(retrieveValueFromHeader(exchange, FilterConstant.MULTI_MEMBER_STATUS));
        if (isWeixinTerminal(exchange)) {
            UserInfo newUserInfo = new UserInfo();
            newUserInfo.setAllianceId(ALLIANCE_ID);
            newUserInfo.setOperSubjectGuid(operSubjectGuid);
            newUserInfo.setEnterpriseGuid(enterpriseGuid);
            newUserInfo.setIsAlliance(isAlliance);
            return Mono.just(newUserInfo).flatMap(this::completeMultiMemberStatus)
                    .flatMap(userInfo -> chain.filter(exchange.mutate().request(getNewHttpRequest(exchange, userInfo)).build()))
                    .onErrorResume(cause -> handleException(chain, exchange, cause));
        }
        String url = exchange.getRequest().getURI().toString();

        // 过滤请求
        if (filterRequest(exchange)) {
            return chain.filter(exchange);
        }

        Integer source = retrieveSourceFromHeader(exchange);
        if (source == null) {
            log.error("没有source,{}",url);
            return unAuthenResponse(exchange, chain);
        }

        // 根据业务选择不校验token
        if (!isCheckToken) {
            UserInfo padUserInfo = new UserInfo();
            padUserInfo.setAllianceId(ALLIANCE_ID);
            padUserInfo.setEnterpriseGuid(enterpriseGuid);
            padUserInfo.setOperSubjectGuid(operSubjectGuid);
            padUserInfo.setIsAlliance(isAlliance);
            return Mono.just(padUserInfo).flatMap(this::completeMultiMemberStatus)
                    .flatMap(userInfo -> chain.filter(exchange.mutate().request(getNewHttpRequest(exchange, userInfo)).build()))
                    .onErrorResume(cause -> handleException(chain, exchange, cause));
        }

        //获取header中的token
        String token = validateTokenExist(exchange);
        if (StringUtils.isEmpty(token)) {
            log.error("没有token,url={}",url);
            return unAuthenResponse(exchange, chain);
        }

        Integer loginType = retrieveLoginTypeFromHeader(exchange);
        return Mono.just(token)
                .flatMap(tokenVar -> validateTokenAndGetUserInfo(tokenVar, source, loginType))
                .flatMap(userInfo -> completeUserInfo(userInfo, source, isAlliance, operSubjectGuid, multiMemberStatus))
                .flatMap(this::completeEnterpriseInfoForUser)
                .flatMap(this::completeStoreInfoForUser)
                .flatMap(userInfo -> completeMemberInfoForUser(userInfo, source))
                .flatMap(userInfo -> {
                    ServerHttpRequest request = getNewHttpRequest(exchange, userInfo);
                    return chain.filter(exchange.mutate().request(request).build());
                })
                .onErrorResume(cause -> handleException(chain, exchange, cause));


    }

    /**
     * 校验token是否存在
     *
     * @param exchange
     * @return
     */
    private String validateTokenExist(ServerWebExchange exchange) {
        List<String> tokenList = exchange.getRequest().getHeaders().get(FilterConstant.TOKEN);
        if (tokenList == null || tokenList.isEmpty()) {
            return "";
        }
        String token = tokenList.stream().findFirst().orElse(null);
        if (StringUtils.isEmpty(token)) {
            return "";
        }
        return token;
    }


    /**
     * 添加用户信息到header中,获取新的request
     *
     * @param exchange
     * @param newUserInfo
     * @return
     */
    private ServerHttpRequest getNewHttpRequest(ServerWebExchange exchange, UserInfo newUserInfo) {
        return exchange.getRequest().mutate()
                .headers(httpHeaders -> {
                    try {
                        httpHeaders.set(USER_INFO, URLEncoder.encode(JacksonUtils.writeValueAsString(newUserInfo), "utf-8"));
                        log.info("completeUserInfo函数最终返回User数据:" + JacksonUtil.writeValueAsString(newUserInfo));
                    } catch (UnsupportedEncodingException e) {
                        log.error("添加userInfo头出错", e);
                    }
                })
                .build();
    }

    /**
     * 完善用户信息
     *
     * @param userInfo
     * @param source
     * @param isAlliance
     * @param operSubjectGuid
     * @return
     */
    private Mono<UserInfo> completeUserInfo(UserInfo userInfo, Integer source, Boolean isAlliance, String operSubjectGuid, Boolean multiMemberStatus) {
        Mono<UserInfo> userInfoMono;
        UserInfo newUserInfo = userInfoCache.getIfPresent(userInfo.getUserGuid() + ":" + userInfo.getEnterpriseGuid() + ":" + source);
        if (newUserInfo != null) {
            userInfoMono = Mono.just(newUserInfo);
        } else {
            if (TerminalConstant.TERMINAL_CLOUD.equals(String.valueOf(source))) {
                userInfoMono = webClientBuilder.build().get()
                        .uri(CLOUD_USER_INFO_URL + userInfo.getUserGuid())
                        .accept(MediaType.APPLICATION_JSON_UTF8)
                        .retrieve()
                        .bodyToMono(UserInfo.class);
            } else if (TerminalConstant.TERMINAL_AGENT.equals(String.valueOf(source))) {
                String agentUserUrl = CLOUD_AGENT_USER_INFO_URL + userInfo.getUserGuid();
                log.info("访问agentUserUrl:" + agentUserUrl);
                Mono<AgentUserDTO> agentUserDTOMono = webClientBuilder.build().get()
                        .uri(agentUserUrl).accept(MediaType.APPLICATION_JSON_UTF8).retrieve().bodyToMono(AgentUserDTO.class);
                userInfoMono = agentUserDTOMono.map(agentUser -> {
                    UserInfo ui = new UserInfo();
                    ui.setUserGuid(agentUser.getGuid());
                    ui.setAccount(agentUser.getTel());
                    ui.setName(agentUser.getName());
                    ui.setTel(agentUser.getTel());
                    log.info("根据agentUserDTOMono重新创建用户信息完成");
                    return ui;
                }).defaultIfEmpty(userInfo);

            } else {
                String requestUrl = String.format(USER_INFO_URL, userInfo.getUserGuid(), userInfo.getEnterpriseGuid());
                userInfoMono = webClientBuilder.build().get()
                        .uri(requestUrl)
                        .accept(MediaType.APPLICATION_JSON_UTF8)
                        .retrieve()
                        .bodyToMono(UserInfo.class);
            }
            userInfoMono = userInfoMono
                    .doOnNext(info -> userInfoCache.put(userInfo.getUserGuid() + ":" + userInfo.getEnterpriseGuid() + ":" + source, info))
                    .onErrorResume(cause -> {
                        log.error("请求用户完整信息失败", cause);
                        return Mono.just(userInfo);
                    })
                    .defaultIfEmpty(userInfo);
        }
        return userInfoMono
                .map(info -> {
                    info.setEnterpriseGuid(userInfo.getEnterpriseGuid());
                    info.setStoreNo(userInfo.getStoreNo());
                    info.setDeviceGuid(userInfo.getDeviceGuid());
                    info.setIsAlliance(isAlliance);
                    info.setOperSubjectGuid(operSubjectGuid);
                    info.setMultiMemberStatus(multiMemberStatus);
                    return info;
                });


    }

    /**
     * 完善企业信息
     *
     * @param userInfo
     * @return
     */
    private Mono<UserInfo> completeEnterpriseInfoForUser(UserInfo userInfo) {
        if (StringUtils.isEmpty(userInfo.getEnterpriseGuid())) {
            return Mono.just(userInfo);
        }
        EnterpriseQueryDTO queryDTO = new EnterpriseQueryDTO();
        queryDTO.setEnterpriseGuid(userInfo.getEnterpriseGuid());
        EnterpriseDTO enterpriseDTO = enterpriseCache.getIfPresent(queryDTO.getEnterpriseGuid());
        Mono<EnterpriseDTO> enterpriseDTOMono;
        if (enterpriseDTO != null) {
            enterpriseDTOMono = Mono.just(enterpriseDTO);
        } else {
            enterpriseDTOMono = webClientBuilder.build()
                    .post()
                    .uri(ENTERPRISE_INFO_URL)
                    .contentType(MediaType.APPLICATION_JSON_UTF8)
                    .accept(MediaType.APPLICATION_JSON_UTF8)
                    .body(Mono.just(queryDTO), EnterpriseQueryDTO.class)
                    .retrieve()
                    .bodyToMono(EnterpriseDTO.class)
                    .doOnNext(enterpriseDTOInfo -> enterpriseCache.put(queryDTO.getEnterpriseGuid(), enterpriseDTOInfo));
        }
        return enterpriseDTOMono
                .map(enterpriseDTOInfo -> {
                    userInfo.setEnterpriseName(enterpriseDTOInfo.getName());
                    userInfo.setEnterpriseNo(enterpriseDTOInfo.getUid());
                    userInfo.setCommercialActivities(enterpriseDTOInfo.getCommercialActivities());
                    if (enterpriseDTOInfo.getManagementModel() == EnterpriseDTO.ManagementModel.PLATFORM) {
                        userInfo.setIsAlliance(true);
                    } else {
                        userInfo.setIsAlliance(false);
                    }
                    return userInfo;
                })
                .defaultIfEmpty(userInfo)
                .onErrorResume(cause -> {
                    log.error("请求企业信息失败", cause);
                    return Mono.just(userInfo);
                });
    }


    /**
     * 完善storeInfo
     *
     * @param userInfo
     * @return
     */
    private Mono<UserInfo> completeStoreInfoForUser(UserInfo userInfo) {
        String storeNo = userInfo.getStoreNo();
        if (StringUtils.isEmpty(storeNo)) {
            return Mono.just(userInfo);
        }
        OrganizationDTO organizationDTO = organizationCache.getIfPresent(storeNo);
        Mono<OrganizationDTO> organizationDTOMono;
        if (organizationDTO != null) {
            organizationDTOMono = Mono.just(organizationDTO);
        } else {
            organizationDTOMono = webClientBuilder.build().get()
                    .uri(STORE_INFO_URL + storeNo)
                    .accept(MediaType.APPLICATION_JSON_UTF8)
                    .retrieve()
                    .bodyToMono(OrganizationDTO.class)
                    .doOnNext(organizationDTOInfo -> organizationCache.put(storeNo, organizationDTOInfo));
        }
        return organizationDTOMono
                .map(organizationDTOInfo -> {
                    String storeGuid = Optional.ofNullable(organizationDTOInfo).map(OrganizationDTO::getOrganizationGuid).orElse(null);
                    userInfo.setStoreGuid(storeGuid);
                    String storeName = Optional.ofNullable(organizationDTOInfo).map(OrganizationDTO::getName).orElse(null);
                    userInfo.setStoreName(storeName);
                    return userInfo;
                })
                .defaultIfEmpty(userInfo)
                .onErrorResume(cause -> {
                    log.error("请求门店信息失败", cause);
                    return Mono.just(userInfo);
                });
    }

    /**
     * 完善会员Info
     *
     * @param userInfo
     * @return
     */

    private Mono<UserInfo> completeMemberInfoForUser(UserInfo userInfo, Integer source) {
        String operSubjectGuid = userInfo.getOperSubjectGuid();
        //不是一体机或者已经存在运营主体id,则直接跳过
        if (!StringUtils.isEmpty(operSubjectGuid)) {
            return completeMultiMemberStatus(userInfo);
        }
        String storeGuid = userInfo.getStoreGuid();
        return webClientBuilder.build().get()
                .uri(CLOUD_MEMBER_INFO_URL + storeGuid)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(MultiMemberDTO.class)
                .map(multiMemberDTO -> {
                    userInfo.setOperSubjectGuid(multiMemberDTO.getMultiMemberGuid());
                    if (userInfo.getIsAlliance() == null) {
                        userInfo.setIsAlliance(false);
                    }
                    userInfo.setMultiMemberStatus(multiMemberDTO.getEnabled());
                    return userInfo;
                }).defaultIfEmpty(userInfo);
    }

    private Mono<UserInfo> completeMultiMemberStatus(UserInfo userInfo) {
        //网关查询运营主体状态，当无法查询到运营主体时，清空当前的运营主体
        UserInfo userInfo1 = new UserInfo();
        BeanUtils.copyProperties(userInfo, userInfo1);
        userInfo1.setOperSubjectGuid("");
        userInfo1.setMultiMemberStatus(Boolean.FALSE);

        return webClientBuilder.build().get()
                .uri(CLOUD_MULTI_MEMBER_URL + userInfo.getOperSubjectGuid())
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(MultiMemberDTO.class)
                .map(multiMemberDTO -> {
                    userInfo.setMultiMemberStatus(multiMemberDTO.getEnabled());
                    return userInfo;
                }).defaultIfEmpty(userInfo1);
    }


    /**
     * 验证token，并获取UserInfo
     *
     * @param token
     * @param source
     * @param loginType
     * @return
     */
    private Mono<UserInfo> validateTokenAndGetUserInfo(String token, Integer source, Integer loginType) {
        Map<String, Object> map = new HashMap<>(3);
        map.put("token", token);
        map.put("source", source);
        map.put("loginType", loginType);
        return webClientBuilder.build().post()
                .uri(TOKEN_VERIFY_URL)
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .body(Mono.just(map), Map.class)
                .retrieve()
                .bodyToMono(Result.class)
                .map(result -> {
                    if (result.getCode() != 0 || result.getTData() == null) {
                        log.warn("认证失败,返回结果: {}", JacksonUtil.writeValueAsString(result));
                        throw new AuthenticationException(MessageEnum.getLocale(MessageEnum.AUTHENTICATION_FAILURE));
                    }
                    try {
                        return JacksonUtil.toObject(UserInfo.class, JacksonUtils.writeValueAsString(result.getTData()));
                    } catch (Exception e) {
                        log.error("转换sso返回结果失败,{}", result, e);
                        throw new BadRequestException(MessageEnum.getLocale(MessageEnum.SSO_REQUEST_FAILED));
                    }
                })
                .switchIfEmpty(Mono.error(new BadRequestException(MessageEnum.getLocale(MessageEnum.SSO_RESPONSE_EMPTY))));
    }


    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + ORDER * 1000;
    }

    @Override
    protected boolean filterTerminal(ServerWebExchange exchange) {
        URIFilter uriFilter = URIFilter.createRequestFilter(exchange)
                .filterTerminal(TerminalConstant.TERMINAL_WEIXIN)
                .filterTerminal(TerminalConstant.ZHUAN_CAN_WEIXIN)
                .filterTerminal(TerminalConstant.ALIPAY_APPLETS);
        return uriFilter.filtered();
    }

    @Override
    protected boolean filterRequest(ServerWebExchange exchange) {
        boolean terminalFiltered = filterTerminal(exchange);
        if (!terminalFiltered) {
            boolean whiteListFiltered = URIFilter.createRequestFilter(exchange)
                    .urlIsInAuthenticationWhiteList(exchange.getRequest().getURI().toString());
            if (!whiteListFiltered) {
                return false;
            }
        }
        return true;
    }
}
