package com.holderzone.saas.gateway.filter;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.gateway.constans.TerminalConstant;
import com.holderzone.saas.gateway.entity.CommercialActivityEnum;
import com.holderzone.saas.gateway.entity.StoreDeviceDTO;
import com.holderzone.saas.gateway.entity.UserInfo;
import com.holderzone.saas.gateway.exception.BadRequestException;
import com.holderzone.saas.gateway.exception.FilterRequestException;
import com.holderzone.saas.gateway.utils.JacksonUtil;
import com.holderzone.saas.gateway.utils.URIFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.codec.DecodingException;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Optional;

import static com.holderzone.saas.gateway.utils.ResponseUtil.badRequestResponse;
import static com.holderzone.saas.gateway.utils.ResponseUtil.unAuthorResponse;

/**
 * 离线模式下拦截非主机一体机的请求
 *
 * <AUTHOR>
 * @date 2019/11/18 下午 17:00
 * @description
 */
public class OfflineDeviceRequestFilter extends BaseFilter implements GlobalFilter, Ordered {

    private static final Logger log = LoggerFactory.getLogger(OfflineDeviceRequestFilter.class);

    private static final String STORE_DEVICE_INFO_URL = "http://holder-saas-store-organization/device/get_master_device/%s/%s";

    private static final String HEARTBEAT_URL = "http://holder-saas-cloud-heartbeat-server/device/is_online/%s/%d/%s";

    /**
     * filter的顺序
     */
    private static final Integer ORDER = 14;

    private Boolean isOfflineEnabled;

    private String offlineGrayInclude;

    private String offlineGrayExclude;

    private WebClient.Builder webClientBuilder;

    public OfflineDeviceRequestFilter(Boolean isOfflineEnabled, String offlineGrayInclude, String offlineGrayExclude, WebClient.Builder webClientBuilder) {
        this.isOfflineEnabled = isOfflineEnabled;
        this.offlineGrayInclude = offlineGrayInclude;
        this.offlineGrayExclude = offlineGrayExclude;
        this.webClientBuilder = webClientBuilder;
    }


    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        if (filterRequest(exchange)) {
            return chain.filter(exchange);
        }
        // 获取用户信息
        UserInfo userInfo = retrieveUserInfoFromHeader(exchange);
        if (userInfo == null) {
            log.info("userInfo为空没有权限：{}", JacksonUtil.writeValueAsString(exchange));
            return unAuthorResponse(exchange, "没有权限",chain);
        }
        // 微信终端，完善用户信息
        completeWeixinUserInfo(exchange, userInfo);
        String[] deviceArray = new String[1];
        return getDeviceGuidByUserInfo(userInfo)
                .doOnNext(deviceGuid -> deviceArray[0] = deviceGuid)
                .flatMap(deviceGuid -> isOnLine(deviceGuid, userInfo.getEnterpriseGuid()))
                .flatMap(isOnLine -> {
                    String deviceGuid = deviceArray[0];
                    if (isOnLine) {
                        log.info("门店[{}]主机[{}]请求，直接放行！", userInfo.getStoreGuid(), deviceGuid);
                        return chain.filter(exchange);
                    }
                    if (isAioTerminal(exchange, deviceGuid, userInfo)) {
                        return chain.filter(exchange);
                    }
                    return badRequestResponse(exchange, "离线模式已开启，当前仅能操作主机一体机！",chain);
                })
                .onErrorResume(cause -> handleException(chain, exchange, cause));

    }

    /**
     * 判断离线设备是否是否为一体机
     *
     * @param exchange
     * @param deviceGuid
     * @param userInfo
     * @return
     */
    private boolean isAioTerminal(ServerWebExchange exchange, String deviceGuid, UserInfo userInfo) {
        Integer deviceType = retrieveSourceFromHeader(exchange);
        if (deviceType == null) {
            throw new BadRequestException("没有source");
        }
        if (TerminalConstant.TERMINAL_AIO.equals("" + deviceType)
            && deviceGuid.equals(userInfo.getDeviceGuid())) {
            log.info("门店[{}]主机[{}]请求，直接放行！", userInfo.getStoreGuid(), deviceGuid);
            return true;
        }
        // 否则，拒绝请求
        log.info("门店[{}]从机[{}]请求，主机[{}]离线，拒绝放行！",
                userInfo.getStoreGuid(), userInfo.getDeviceGuid(), deviceGuid);
        return false;
    }

    /**
     * 获取deviceGuid
     *
     * @param userInfo
     * @return
     */
    private Mono<String> getDeviceGuidByUserInfo(UserInfo userInfo) {
        return webClientBuilder.build()
                .get()
                .uri(String.format(STORE_DEVICE_INFO_URL, userInfo.getEnterpriseGuid(), userInfo.getStoreGuid()))
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(StoreDeviceDTO.class)
                .map(storeDeviceDTO -> {
                    String deviceGuid = storeDeviceDTO.getDeviceGuid();
                    if (StringUtils.isEmpty(deviceGuid)) {
                        log.info("deviceGuid为空，门店：{}", userInfo.getStoreGuid());
                        throw new FilterRequestException("deviceGuid为空");
                    }
                    return deviceGuid;
                })
                .switchIfEmpty(Mono.error(new FilterRequestException("查询设备信息为空")));
    }

    /**
     * 判断设备是否在线
     *
     * @param deviceGuid
     * @param enterpriseGuid
     * @return
     */
    private Mono<Boolean> isOnLine(String deviceGuid, String enterpriseGuid) {
        return webClientBuilder.build()
                .get()
                .uri(String.format(HEARTBEAT_URL, enterpriseGuid,
                        Integer.valueOf(TerminalConstant.TERMINAL_AIO), deviceGuid))
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(Boolean.class)
                .onErrorMap(cause -> cause instanceof DecodingException, cause -> new BadRequestException("请求设备是否在线接口报错", cause))
                .switchIfEmpty(Mono.error(new BadRequestException("请求设备是否在线返回信息为空")));
    }

    @Override
    protected boolean filterTerminal(ServerWebExchange exchange) {
        URIFilter uriFilter = URIFilter.createRequestFilter(exchange)
                .filterTerminal(TerminalConstant.TERMINAL_AIO)
                .filterTerminal(TerminalConstant.TERMINAL_POS)
                .filterTerminal(TerminalConstant.TERMINAL_PAD)
                .filterTerminal(TerminalConstant.TERMINAL_MOBILE)
                .filterTerminal(TerminalConstant.TERMINAL_POS_FINANCIAL)
                .filterTerminal(TerminalConstant.TERMINAL_WEIXIN)
                .filterTerminal(TerminalConstant.TERMINAL_WEIXIN_PROCEDURE)
                .filterTerminal(TerminalConstant.ERP_WEB)
                .filterTerminal(TerminalConstant.ERP_NOT_WEB)
                .filterTerminal(TerminalConstant.ZHUAN_CAN_WEIXIN)
                .filterTerminal(TerminalConstant.ALIPAY_APPLETS);
        return uriFilter.filtered();
    }

    @Override
    protected boolean filterRequest(ServerWebExchange exchange) {
        if (!isOfflineEnabled) {
            return true;
        }
        // 非门店漫游终端直接放行
        boolean isStoreTerminal = filterTerminal(exchange);
        if (!isStoreTerminal) {
            return true;
        }
        // 认证白名单接口直接放行
        boolean isInAuthenticationWhiteList = URIFilter.createRequestFilter()
                .urlIsInAuthenticationWhiteList(exchange.getRequest().getURI().toString());
        if (isInAuthenticationWhiteList) {
            return true;
        }
        UserInfo userInfo = retrieveUserInfoFromHeader(exchange);
        String enterpriseGuid = null;
        String storeGuid = null;
        if (isWeixinTerminal(exchange)) {
            enterpriseGuid = retrieveEnterpriseGuidFromHeader(exchange);
            storeGuid = retrieveStoreGuidFromHeader(exchange);
        } else {
            enterpriseGuid = Optional.ofNullable(userInfo).map(UserInfo::getEnterpriseGuid).orElse("");
            storeGuid = Optional.ofNullable(userInfo).map(UserInfo::getStoreGuid).orElse("");
        }
        if (!StringUtils.hasText(enterpriseGuid)
                || "null".equalsIgnoreCase(enterpriseGuid)
                || !StringUtils.hasText(storeGuid)
                || "null".equalsIgnoreCase(storeGuid)) {
            log.info("无门店信息，e！");
            return true;
        }
        // 非离线化版本灰度商家，直接放行
        if (!isGrayMerchant(enterpriseGuid)) {
            return true;
        }

        if (userInfo != null) {
            // 非餐饮版，直接放行（兼容脏数据，null认为是餐饮）
            String commercialActivities = userInfo.getCommercialActivities();
            return commercialActivities != null && !commercialActivities.equals(CommercialActivityEnum.FOOD.getCode());
        }

        return false;
    }


    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + ORDER * 1000;
    }


    /**
     * 完善用户信息
     *
     * @param exchange
     * @param userInfo
     */
    private void completeWeixinUserInfo(ServerWebExchange exchange, UserInfo userInfo) {
        if (!isWeixinTerminal(exchange)) {
            return;
        }
        userInfo.setEnterpriseGuid(retrieveEnterpriseGuidFromHeader(exchange));
        userInfo.setStoreGuid(retrieveStoreGuidFromHeader(exchange));
    }

    private boolean isGrayMerchant(String enterpriseGuid) {
        if ("all".equalsIgnoreCase(offlineGrayExclude)) {
            return false;
        }
        if (null != offlineGrayExclude && offlineGrayExclude.contains(enterpriseGuid)) {
            return false;
        }
        if (!StringUtils.hasText(offlineGrayInclude)) {
            return false;
        }
        if ("all".equalsIgnoreCase(offlineGrayInclude) || offlineGrayInclude.contains(enterpriseGuid)) {
            return true;
        }
        return false;
    }
}
