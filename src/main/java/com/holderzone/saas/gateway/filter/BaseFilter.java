package com.holderzone.saas.gateway.filter;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.gateway.constans.TerminalConstant;
import com.holderzone.saas.gateway.entity.FilterConstant;
import com.holderzone.saas.gateway.entity.UserInfo;
import com.holderzone.saas.gateway.exception.AuthenticationException;
import com.holderzone.saas.gateway.exception.AuthorizationException;
import com.holderzone.saas.gateway.exception.BadRequestException;
import com.holderzone.saas.gateway.exception.FilterRequestException;
import com.holderzone.saas.gateway.utils.JacksonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

import static com.holderzone.saas.gateway.utils.ResponseUtil.*;

/**
 * <AUTHOR>
 * @date 2019/02/26 下午 18:14
 * @description
 */
public abstract class BaseFilter {

    private static final Logger log = LoggerFactory.getLogger(BaseFilter.class);

    /**
     * 过滤某些终端
     *
     * @param exchange
     * @return
     */
    protected boolean filterTerminal(ServerWebExchange exchange) {
        return false;
    }

    /**
     * 过滤请求
     *
     * @param exchange
     * @return
     */
    protected boolean filterRequest(ServerWebExchange exchange) {
        return false;
    }

    /**
     * 从header中获取userInfo
     *
     * @param exchange
     * @return
     */
    protected UserInfo retrieveUserInfoFromHeader(ServerWebExchange exchange) {
        List<String> userInfoHeaders = exchange.getRequest().getHeaders().get(FilterConstant.USER_INFO);
        if (CollectionUtils.isEmpty(userInfoHeaders)) {
            return null;
        }
        String userInfoStr;
        try {
            userInfoStr = URLDecoder.decode(userInfoHeaders.get(0), "utf-8");
        } catch (UnsupportedEncodingException e) {
            log.error("解析userInfo错误", e);
            return null;
        }
        return JacksonUtils.toObject(UserInfo.class, userInfoStr);
    }


    /**
     * 从header中获取source
     *
     * @param exchange
     * @return
     */
    protected Integer retrieveSourceFromHeader(ServerWebExchange exchange) {
        List<String> sourceList = exchange.getRequest().getHeaders().get(FilterConstant.SOURCE);
        if (CollectionUtils.isEmpty(sourceList)) {
            return null;
        }
        return Integer.valueOf(sourceList.get(0));
    }

    /**
     * 从header中获取terminalCode
     *
     * @param exchange
     * @return
     */
    protected String retrieveTerminalCodeFromHeader(ServerWebExchange exchange) {
        List<String> terminalCodeHeaders = exchange.getRequest().getHeaders().get(FilterConstant.TERMINAL_CODE);
        if (CollectionUtils.isEmpty(terminalCodeHeaders)) {
            return null;
        }
        return terminalCodeHeaders.get(0);
    }

    /**
     * 从header中获取数据
     *
     * @param exchange
     * @return
     */
    protected String retrieveValueFromHeader(ServerWebExchange exchange, String code) {
        List<String> terminalCodeHeaders = exchange.getRequest().getHeaders().get(code);
        if (CollectionUtils.isEmpty(terminalCodeHeaders)) {
            return null;
        }
        return terminalCodeHeaders.get(0);
    }

    /**
     * 从header中获取menuGuid
     *
     * @param exchange
     * @return
     */
    protected String retrieveMenuGuidFromHeader(ServerWebExchange exchange) {
        List<String> menuGuidHeaders = exchange.getRequest().getHeaders().get(FilterConstant.MENU_GUID);
        if (CollectionUtils.isEmpty(menuGuidHeaders)) {
            return null;
        }
        return menuGuidHeaders.get(0);
    }

    /**
     * 从header中获取enterpriseGuid
     *
     * @param exchange
     * @return
     */
    protected String retrieveEnterpriseGuidFromHeader(ServerWebExchange exchange) {
        List<String> erpGuidList = exchange.getRequest().getHeaders().get(FilterConstant.ENTERPRISE_GUID);
        if (CollectionUtils.isEmpty(erpGuidList)) {
            return null;
        }
        return erpGuidList.get(0);
    }

    /**
     * 从header中获取storeGuid
     *
     * @param exchange
     * @return
     */
    protected String retrieveStoreGuidFromHeader(ServerWebExchange exchange) {
        List<String> storeGuidList = exchange.getRequest().getHeaders().get(FilterConstant.STORE_GUID);
        if (CollectionUtils.isEmpty(storeGuidList)) {
            return null;
        }
        return storeGuidList.get(0);
    }

    /**
     * 从request中获取loginType
     *
     * @param exchange
     * @return
     */
    protected Integer retrieveLoginTypeFromHeader(ServerWebExchange exchange) {
        List<String> loginTypeList = exchange.getRequest().getHeaders().get(FilterConstant.LOGIN_TYPE);
        Integer loginType = 1;
        if (loginTypeList != null && !loginTypeList.isEmpty()) {
            loginType = loginTypeList.stream().findFirst().map(Integer::parseInt).orElse(1);
        }
        return loginType;
    }

    /**
     * 检查是否是微信端
     *
     * @param exchange
     * @return
     */
    protected boolean isWeixinTerminal(ServerWebExchange exchange) {
        List<String> sourceList = exchange.getRequest().getHeaders().get(FilterConstant.SOURCE);
        if (sourceList != null) {
            String source = sourceList.stream().findFirst().orElse("");
            return TerminalConstant.TERMINAL_WEIXIN.equals(source) || TerminalConstant.ZHUAN_CAN_WEIXIN.equals(source)
                    || TerminalConstant.ALIPAY_APPLETS.equals(source);
        }
        return false;
    }

    /**
     * 处理异常
     *
     * @param chain
     * @param exchange
     * @param cause
     * @return
     */
    protected Mono<Void> handleException(GatewayFilterChain chain, ServerWebExchange exchange, Throwable cause) {
        if (cause instanceof AuthenticationException) {
            AuthenticationException authenticationException = (AuthenticationException) cause;
            log.warn(authenticationException.getMessage(), authenticationException);
            return unAuthenResponse(exchange, chain);
        }
        if (cause instanceof AuthorizationException) {
            AuthorizationException authorizationException = (AuthorizationException) cause;
            log.error(authorizationException.getMessage(), authorizationException);
            return unAuthorResponse(exchange, authorizationException.getMessage(), chain);
        }
        if (cause instanceof FilterRequestException) {
            FilterRequestException filterRequestException = (FilterRequestException) cause;
            log.info(filterRequestException.getMessage());
            return chain.filter(exchange);
        }
        if (cause instanceof BadRequestException) {
            BadRequestException badRequestException = (BadRequestException) cause;
            log.error(badRequestException.getMessage(), badRequestException);
            return badRequestResponse(exchange, badRequestException.getMessage(), chain);
        }
        log.error("未知错误 {}, 请求的URI : {} , httpHeaders : {} ", cause.getStackTrace(),
                exchange.getRequest().getURI().toString(),
                JacksonUtil.writeValueAsString(exchange.getRequest().getHeaders()));
        return badRequestResponse(exchange, "未知错误", chain);
    }
}
