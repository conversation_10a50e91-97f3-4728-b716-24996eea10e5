package com.holderzone.saas.gateway.entity;


import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className StoreTerminalDTO
 * @date 18-9-6 上午10:37
 * @description 门店-设备DTO
 * @program holder-saas-store-dto
 */
public class StoreDeviceDTO implements Serializable {

    private static final long serialVersionUID = 2998556334602497964L;

    /**
     * 关联的企业guid
     */
    private String enterpriseGuid;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 厂商设备编号
     */
    private String deviceNo;

    /**
     * 系统设备编号
     */
    private String deviceGuid;

    /**
     * 设备是否绑定
     */
    private Boolean binding;

    /**
     * 设备是否注册
     */
    private Boolean register;

    /**
     * 设备类型
     * PC服务端- 0
     * PC平板- 1
     * 小店通- 2
     * 一体机- 3
     * POS机- 4
     * 云平板- 5
     * 点菜宝(M1)- 6
     * PV1(带刷卡的点菜宝)- 7
     * 厨房显示系统- 9
     * 厨房取餐屏- 10
     */
    private Integer deviceType;

    /**
     * 设备类型名称
     */
    private String deviceName;

    /**
     * 打印设备排序（仅设备类型为一体机有效，默认为0。）
     */
    private Integer sort;

    /**
     * 创建人guid
     */
    private String createUserGuid;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 设备解绑时间
     */
    private LocalDateTime gmtUnbind;

    public String getEnterpriseGuid() {
        return enterpriseGuid;
    }

    public void setEnterpriseGuid(String enterpriseGuid) {
        this.enterpriseGuid = enterpriseGuid;
    }

    public String getStoreNo() {
        return storeNo;
    }

    public void setStoreNo(String storeNo) {
        this.storeNo = storeNo;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getDeviceNo() {
        return deviceNo;
    }

    public void setDeviceNo(String deviceNo) {
        this.deviceNo = deviceNo;
    }

    public String getDeviceGuid() {
        return deviceGuid;
    }

    public void setDeviceGuid(String deviceGuid) {
        this.deviceGuid = deviceGuid;
    }

    public Boolean getBinding() {
        return binding;
    }

    public void setBinding(Boolean binding) {
        this.binding = binding;
    }

    public Boolean getRegister() {
        return register;
    }

    public void setRegister(Boolean register) {
        this.register = register;
    }

    public Integer getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getCreateUserGuid() {
        return createUserGuid;
    }

    public void setCreateUserGuid(String createUserGuid) {
        this.createUserGuid = createUserGuid;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public LocalDateTime getGmtUnbind() {
        return gmtUnbind;
    }

    public void setGmtUnbind(LocalDateTime gmtUnbind) {
        this.gmtUnbind = gmtUnbind;
    }
}
