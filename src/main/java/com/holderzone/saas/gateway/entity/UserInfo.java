package com.holderzone.saas.gateway.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2018/09/11 下午 15:16
 * @description
 */
@Data
public class UserInfo {

    /**
     * 商户Guid
     */
    private String enterpriseGuid;

    /**
     * 商户编码
     */
    private String enterpriseNo;
    /**
     * 商户名称
     */
    private String enterpriseName;

    /**
     * 商户经营类型
     */
    private String commercialActivities;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 门店编码
     */
    private String storeNo;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 设备Guid
     */
    private String deviceGuid;

    /**
     * 用户Guid
     */
    private String userGuid;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 用户电话
     */
    private String tel;

    /**
     * 用户账号
     */
    private String account;

    /**
     * 联盟ID
     */
    private String allianceId;

    /**
     * 是否联盟
     */
    private Boolean isAlliance;

    /**
     * 运营主体guid
     */
    private String operSubjectGuid;
    /**
     * 运营主体状态
     */
    private Boolean multiMemberStatus;
}
