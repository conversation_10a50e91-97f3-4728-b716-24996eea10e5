package com.holderzone.saas.gateway.entity;

public enum CommercialActivityEnum {

    FOOD("10001", "餐饮版"),

    RETAIL("10002", "零售版")

    ;

    private String code;

    private String desc;

    CommercialActivityEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(String code) {
        for (CommercialActivityEnum value : values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value.getDesc();
            }
        }
        return "未知版本";
    }
}
