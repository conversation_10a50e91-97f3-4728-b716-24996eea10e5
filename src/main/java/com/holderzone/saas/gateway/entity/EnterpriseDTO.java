package com.holderzone.saas.gateway.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2018/11/06 下午 14:30
 * @description
 */
@Data
public class EnterpriseDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 非0开头，8位纯数字作为商户编号，唯一
     */
    private String uid;

    /**
     * 企业的guid
     */
    private String enterpriseGuid;

    /**
     * 名字
     */
    private String name;

    /**
     * 注册电话
     */
    private String regTel;

    /**
     * 注册邮箱
     */
    private String regEmail;

    /**
     * 注册时间
     */
    private long regTime;

    /**
     * 注册方式
     */
    private String regType;

    /**
     * 有效起始时间
     */
    private long validStartTime;

    /**
     * 有效截止时间
     */
    private long validEndTime;

    /**
     * 地址
     */
    private String address;

    /**
     * 是否删除
     */
    private Integer deleted;

    /**
     * 是否可用，默认可用
     */
    private Integer enabled;

    /**
     * 法人名字
     */
    private String corporationName;

    /**
     * 法人身份证号码
     */
    private String corporationIDCard;

    /**
     * 法人身份证正面照片
     */
    private String corporationIDCardFaceImg;

    /**
     * 法人身份证反面照片
     */
    private String corporationIDCardBackImg;

    /**
     * 营业执照编号
     */
    private String businessLicence;

    /**
     * 营业执照照片
     */
    private String businessLicenceImg;

    /**
     * 企业名称（以营业执照为准）
     */
    private String enterpriseName;

    /**
     * 企业类型（以营业执照为准）
     */
    private String enterpriseType;

    /**
     * 企业地址（以营业执照为准）
     */
    private String enterpriseAddr;

    /**
     * 经营期限（以营业执照为准）
     */
    private long businessTimeStart;
    private long businessTimeEnd;

    /**
     * 状态
     */
    private int state;

    /**
     * 软件版本
     */
    private String dictionaryKey;

    /**
     * 软件规格
     */
    private String dictionaryStandard;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 软件对照企业类型
     */
    private String type;

    private int storeCount;

    private int staffCount;

    private int brandCount;
    private String standardInfo;
    private String commercialActivities;

    /**
     * 经营模式
     */
    private EnterpriseDTO.ManagementModel managementModel;

    public enum ManagementModel {
        /**
         * 单店
         */
        SINGLE,
        /**
         * 连锁
         */
        CHAIN,
        /**
         * 平台
         */
        PLATFORM
    }
}
