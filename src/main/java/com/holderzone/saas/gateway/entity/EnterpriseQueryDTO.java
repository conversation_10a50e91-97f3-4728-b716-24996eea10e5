package com.holderzone.saas.gateway.entity;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/11/06 下午 14:35
 * @description
 */
public class EnterpriseQueryDTO {

    private String enterpriseGuid;
    private String uid;
    private String regTel;
    private long beginTime;
    private long endTime;
    private String dictionaryKey;
    private int state;
    private String searchConditions;
    private int pageNo;
    private int pageSize;
    private int start;

    public String getEnterpriseGuid() {
        return enterpriseGuid;
    }

    public void setEnterpriseGuid(String enterpriseGuid) {
        this.enterpriseGuid = enterpriseGuid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getRegTel() {
        return regTel;
    }

    public void setRegTel(String regTel) {
        this.regTel = regTel;
    }

    public long getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(long beginTime) {
        this.beginTime = beginTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public String getDictionaryKey() {
        return dictionaryKey;
    }

    public void setDictionaryKey(String dictionaryKey) {
        this.dictionaryKey = dictionaryKey;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getSearchConditions() {
        return searchConditions;
    }

    public void setSearchConditions(String searchConditions) {
        this.searchConditions = searchConditions;
    }

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EnterpriseQueryDTO that = (EnterpriseQueryDTO) o;
        return getBeginTime() == that.getBeginTime() &&
                getEndTime() == that.getEndTime() &&
                getState() == that.getState() &&
                getPageNo() == that.getPageNo() &&
                getPageSize() == that.getPageSize() &&
                getStart() == that.getStart() &&
                Objects.equals(getEnterpriseGuid(), that.getEnterpriseGuid()) &&
                Objects.equals(getUid(), that.getUid()) &&
                Objects.equals(getRegTel(), that.getRegTel()) &&
                Objects.equals(getDictionaryKey(), that.getDictionaryKey()) &&
                Objects.equals(getSearchConditions(), that.getSearchConditions());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getEnterpriseGuid(), getUid(), getRegTel(), getBeginTime(), getEndTime(), getDictionaryKey(), getState(), getSearchConditions(), getPageNo(), getPageSize(), getStart());
    }
}
