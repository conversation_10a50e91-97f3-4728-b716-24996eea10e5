package com.holderzone.saas.gateway.entity;

import java.io.Serializable;

public class AuthDTO implements Serializable{

    private String enterpriseGuid;

    private String userGuid;

    private String userAccount;

    private String terminalCode;

    private String menuGuid;

    private String requestUri;

    public AuthDTO(){
    }

    public AuthDTO(UserInfo userInfo, String terminalCode, String menuGuid, String requestUri){
        this.enterpriseGuid = userInfo.getEnterpriseGuid();
        this.userGuid = userInfo.getUserGuid();
        this.userAccount = userInfo.getAccount();
        this.terminalCode = terminalCode;
        this.menuGuid = menuGuid;
        this.requestUri = requestUri;
    }

    public AuthDTO(String enterpriseGuid, String userGuid, String userAccount, String terminalCode, String menuGuid,
                   String requestUri){
        this.enterpriseGuid = enterpriseGuid;
        this.userGuid = userGuid;
        this.userAccount = userAccount;
        this.terminalCode = terminalCode;
        this.menuGuid = menuGuid;
        this.requestUri = requestUri;
    }

    public String getEnterpriseGuid(){
        return enterpriseGuid;
    }

    public void setEnterpriseGuid(String enterpriseGuid){
        this.enterpriseGuid = enterpriseGuid;
    }

    public String getUserGuid(){
        return userGuid;
    }

    public void setUserGuid(String userGuid){
        this.userGuid = userGuid;
    }

    public String getUserAccount(){
        return userAccount;
    }

    public void setUserAccount(String userAccount){
        this.userAccount = userAccount;
    }

    public String getTerminalCode(){
        return terminalCode;
    }

    public void setTerminalCode(String terminalCode){
        this.terminalCode = terminalCode;
    }

    public String getMenuGuid(){
        return menuGuid;
    }

    public void setMenuGuid(String menuGuid){
        this.menuGuid = menuGuid;
    }

    public String getRequestUri(){
        return requestUri;
    }

    public void setRequestUri(String requestUri){
        this.requestUri = requestUri;
    }

    public static Builder builder(){
        return new Builder();
    }

    public static class Builder{
        private String enterpriseGuid;

        private String userGuid;

        private String userAccount;

        private String terminalCode;

        private String menuGuid;

        private String requestUri;

        private Builder(){

        }

        public Builder enterpriseGuid(String enterpriseGuid){
            this.enterpriseGuid = enterpriseGuid;
            return this;
        }

        public Builder userGuid(String userGuid){
            this.userGuid = userGuid;
            return this;
        }

        public Builder userAccount(String userAccount){
            this.userAccount = userAccount;
            return this;
        }

        public Builder terminalCode(String terminalCode){
            this.terminalCode = terminalCode;
            return this;
        }

        public Builder menuGuid(String menuGuid){
            this.menuGuid = menuGuid;
            return this;
        }

        public Builder requestUri(String requestUri){
            this.requestUri = requestUri;
            return this;
        }

        public AuthDTO build(){
            return new AuthDTO(enterpriseGuid, userGuid, userAccount, terminalCode, menuGuid, requestUri);
        }

    }

    @Override
    public String toString(){
        return "AuthDTO{" + "enterpriseGuid='" + enterpriseGuid + '\'' + ", userGuid='" + userGuid + '\'' + ", " +
                "userAccount='" + userAccount + '\'' + ", terminalCode='" + terminalCode + '\'' + ", menuGuid='" + menuGuid + '\'' + ", requestUri='" + requestUri + '\'' + '}';
    }
}
