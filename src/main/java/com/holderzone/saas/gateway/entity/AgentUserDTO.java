package com.holderzone.saas.gateway.entity;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @version 1.0
 * @className AgentUserDTO
 * @date 2020/03/16 9:11
 * @description 代理商登录用户实体
 * @program saas-platform-enter
 */
public class AgentUserDTO implements Serializable {
    private String guid;
    private String name;
    private String tel;
    private String email;
    private String agentGuid;
    private String agentTypeCode;
    private Integer enabled;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAgentGuid() {
        return agentGuid;
    }

    public void setAgentGuid(String agentGuid) {
        this.agentGuid = agentGuid;
    }

    public String getAgentTypeCode() {
        return agentTypeCode;
    }

    public void setAgentTypeCode(String agentTypeCode) {
        this.agentTypeCode = agentTypeCode;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }
}
