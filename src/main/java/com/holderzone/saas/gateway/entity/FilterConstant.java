package com.holderzone.saas.gateway.entity;

/**
 * <AUTHOR>
 * @date 2018/09/11 下午 16:05
 * @description
 */
public class FilterConstant {

    private FilterConstant() {
    }

    public static final String TOKEN = "token";
    public static final String USER_INFO = "userInfo";
    public static final String ENTERPRISE_GUID = "enterpriseGuid";
    public static final String STORE_GUID = "storeGuid";
    public static final String ALLIANCEID = "allianceid";
    public static final String RESOURCE_LINK_GUID = "resourceLinkGuid";
    public static final String SOURCE = "source";
    public static final String LOGIN_TYPE = "loginType";
    public static final String TERMINAL_CODE = "terminalCode";
    public static final String MENU_GUID = "menuGuid";
    public static final String SOURCE_CODE = "sourceCode";
    public static final String IS_ALLIANCE = "isAlliance";
    public static final String OPER_SUBJECT_GUID = "operSubjectGuid";
    public static final String MULTI_MEMBER_STATUS = "multiMemberStatus";
    public static final String IS_CHECK_TOKEN = "isCheckToken";
    public static final String STORE_NO = "storeNo";
    public static final String USER_GUID = "userGuid";
    public static final String DEVICE_GUID = "deviceGuid";
    /**
     * 来自一体机等设备的头部信息
     */
    public static final String ENTERPRISE_GUID_UP = "EnterpriseGuid";

    /**
     * 不需要校验source的接口
     */
    public static final String PAD_PAY_QR_CALLBACK = "/pad_pay_qr_callback";
}
