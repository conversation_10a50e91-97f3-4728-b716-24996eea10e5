package com.holderzone.saas.gateway.entity;

import lombok.Getter;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 * @create 2023-07-19
 * @description
 */
@Getter
public enum MessageEnum {

    PERMISSION_DENIED("没有权限"),

    HEADER_INCORRECT("头信息不正确"),

    AUTHENTICATION_FAILURE("认证失败"),

    AUTHORIZATION_DATA_EMPTY("请求授权数据为空"),
    SSO_REQUEST_FAILED("认证失败,请求sso服务失败"),
    SSO_RESPONSE_EMPTY("请求sso服务返回结果为空"),


    ;


    private final String message;

    MessageEnum(String message){
        this.message = message;
    }

    public static String getLocale(MessageEnum localeMessageEnum) {
        try {
            if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
                return localeMessageEnum.getMessage();
            }
            ResourceBundle bundle = ResourceBundle.getBundle("i18n/messages", LocaleContextHolder.getLocale());
            return bundle.getString(localeMessageEnum.name());
        } catch (Exception e) {
            return localeMessageEnum.getMessage();
        }
    }


}
