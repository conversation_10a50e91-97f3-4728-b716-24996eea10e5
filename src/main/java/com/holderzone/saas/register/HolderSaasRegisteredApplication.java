package com.holderzone.saas.register;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.server.EnableEurekaServer;

@SpringBootApplication
@EnableEurekaServer
public class HolderSaasRegisteredApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasRegisteredApplication.class, args);
    }
}
