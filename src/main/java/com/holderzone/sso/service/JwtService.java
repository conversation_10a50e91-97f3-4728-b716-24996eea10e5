package com.holderzone.sso.service;

import com.holderzone.sso.entity.TokenRequestBO;

import javax.crypto.SecretKey;

public interface JwtService {

    /**
     * 生成jwtToken的secret
     */
    SecretKey generateKey();

    /**
     * 生成token
     *
     * @param nowMillis      生成Token的时间
     * @param tokenRequestBO
     * @return
     * @throws Exception 生成Token的异常
     */
    String createJWT(long nowMillis, TokenRequestBO tokenRequestBO);


    String createErpJWT(long currentTimeMillis, TokenRequestBO tokenRequestBO);
}
