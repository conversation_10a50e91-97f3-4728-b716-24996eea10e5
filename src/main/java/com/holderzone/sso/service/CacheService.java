package com.holderzone.sso.service;

import com.holderzone.sso.entity.LoginSource;
import com.holderzone.sso.entity.LoginType;
import com.holderzone.sso.entity.TokenRequestBO;

public interface CacheService {

    /**
     * 为客户端生成Token,并缓存
     *
     * @param tokenRequestBO
     * @param loginType
     * @param source
     * @return
     */
    String saveTokenForClient(TokenRequestBO tokenRequestBO, LoginType loginType, LoginSource source);

    /**
     * 根据Token值判断server端是否存在token
     *
     * @param token
     * @param source
     * @param loginType
     * @return
     */
    boolean isServerExistToken(String token, String source, Integer loginType);

    /**
     * 根据Token的值清除指定source来源的token
     *
     * @param token
     */
    boolean cleanToken(String token, String source) throws Exception;


    /**
     * 更新用户token失效时间
     *
     * @param token
     */
    void updateTokenExpire(String token, LoginSource source) throws Exception;

    /**
     * 根据用户GUID删除用户的所有token
     *
     * @param userGuid
     * @throws Exception
     */
    void cleanTokenByUser(String userGuid);

    /**
     * 缓存用户短信验证码
     *
     * @param uuid
     * @param authCode
     * @param userGuid
     */
    void saveUserAuthCode(String uuid, String authCode, String userGuid);

    /**
     * 验证短信验证码
     *
     * @param uuid
     * @param authCode
     * @return
     */
    Boolean validateSmsCode(String uuid, String authCode);

    Boolean validateSmsUser(String uuid, String userGuid);

    String sendVCode(String tel);

    void cleanVCode(String vid);

    /**
     * 清除登录次数缓存
     *
     * @param userInfoKey
     */
    void cleanLoginCount(String userInfoKey);

    /**
     * 增加登录次数
     *
     * @param userInfoKey
     */
    Integer incrementLoginCount(String userInfoKey);

    /**
     * 查询登录次数
     *
     * @param userInfoKey
     * @return
     */
    Integer selectLoginCount(String userInfoKey);
}
