package com.holderzone.sso.service.feign;

import com.holderzone.resource.common.dto.user.UserDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 登录服务
 *
 * <AUTHOR>
 * @date 2018-06-27
 */
@FeignClient(value = "holder-saas-cloud-user", fallbackFactory = UserFeignService.UserServiceFallbackFactory.class)
public interface UserFeignService {

    Logger LOGGER = LoggerFactory.getLogger(UserFeignService.class);

    /**
     * 登录验证
     *
     * @param merchantNo
     * @param username
     * @param password
     * @return
     */
    @PostMapping("login")
    UserDTO loginUser(@RequestParam(value = "merchantNo", required = false) String merchantNo, @RequestParam("account") String username, @RequestParam("password") String password);

    @PostMapping("/user/forget")
    @ApiOperation(value = "忘记密码,发送短信验证码")
    UserDTO forgetPassword(@RequestBody UserDTO userDTO);

    @PostMapping("/loginTel")
    @ApiOperation(value = "根据手机号登录")
    UserDTO loginUserByTel(@RequestParam(value = "tel", required = false) String tel, @RequestParam("password") String password,@RequestParam(value = "enterpriseGuid", required = false) String enterpriseGuid);

    @PostMapping("/user/reset")
    @ApiOperation(value = "忘记密码")
    Boolean resetPassword(@RequestBody UserDTO userDTO);

    @Component
    class UserServiceFallbackFactory implements FallbackFactory<UserFeignService> {

        @Override
        public UserFeignService create(Throwable cause) {
            return new UserFeignService() {
                @Override
                public UserDTO loginUser(String merchantNo, String username, String password) {
                    LOGGER.error("调用holder-saas-cloud-user:{}失败", "/login", cause);
                    return null;
                }

                @Override
                public UserDTO forgetPassword(UserDTO userDTO) {
                    LOGGER.error("调用用户服务的忘记密码失败", cause);
                    return null;
                }

                @Override
                public UserDTO loginUserByTel(String tel, String password,String enterpriseGuid) {
                    LOGGER.error("根据手机号验证密码失败", cause);
                    return null;
                }

                @Override
                public Boolean resetPassword(UserDTO userDTO) {
                    LOGGER.error("重置密码失败", cause);
                    return null;
                }
            };
        }
    }

}
