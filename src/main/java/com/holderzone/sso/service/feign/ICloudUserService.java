package com.holderzone.sso.service.feign;

import com.holderzone.resource.common.dto.user.UserDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2018/11/26 17:15
 */
@FeignClient(value = "holder-saas-cloud-system", fallbackFactory = ICloudUserService.ICloudUserServiceFactory.class)
public interface ICloudUserService {

    Logger LOGGER = LoggerFactory.getLogger(ICloudUserService.class);

    /**
     * 登录时查询用户信息
     *
     * @param userName
     * @param password
     * @return
     */
    @PostMapping("/user/cloud/loginUser")
    UserDTO loginUser(@RequestParam("userName") String userName, @RequestParam("password") String password);

    /**
     * 登录超过一定次数后禁用用户
     *
     * @param account
     */
    @PostMapping("/user/cloud/disableUserAtLogin")
    void disableUserAtLogin(@RequestParam("account") String account);

    @Component
    class ICloudUserServiceFactory implements FallbackFactory<ICloudUserService> {

        @Override
        public ICloudUserService create(Throwable cause) {
            return new ICloudUserService() {

                @Override
                public UserDTO loginUser(String userName, String password) {
                    LOGGER.error("调用holder-saas-cloud-system:{}失败", "/loginUser", cause);
                    return null;
                }

                @Override
                public void disableUserAtLogin(String account) {
                    LOGGER.error("调用holder-saas-cloud-system:{}失败", "/loginDisableUser", cause);

                }
            };
        }
    }
}
