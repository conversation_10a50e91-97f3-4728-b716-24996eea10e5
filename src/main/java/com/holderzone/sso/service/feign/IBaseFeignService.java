package com.holderzone.sso.service.feign;

import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.base.dto.log.LogDTO;
import com.holderzone.framework.base.dto.log.LogQuery;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.base.dto.region.RegionDTO;
import com.holderzone.framework.util.Page;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/09/14 14:12
 */
@Component
@FeignClient(value = "base-service", fallbackFactory = IBaseFeignService.BaseServiceFallbackFactory.class)
public interface IBaseFeignService {

    Logger LOGGER = LoggerFactory.getLogger(IBaseFeignService.class);

    @PostMapping("verifycode")
    Boolean verifyCodeValid(@RequestParam("vc_id") String id, @RequestParam("vc_code") String vcCode);

    @PostMapping("file")
    String upload(@RequestBody FileDto fileDto);

    /**
     * @description 获取省份列表
     */
    @GetMapping("/region/province")
    List<RegionDTO> listProvince();

    /**
     * @description 根据省获取城市列表
     */
    @GetMapping("/region/city")
    List<RegionDTO> listCity(@RequestParam(name = "pCode") String pCode);

    /**
     * @description 根据城市获取地区列表
     */
    @GetMapping("/region/district")
    List<RegionDTO> listDistrict(@RequestParam(name = "pCode") String pCode);

    /**
     * 生成验证码
     *
     * <AUTHOR>
     */
    @GetMapping("verifycode")
    @ApiOperation(value = "验证码生成")
    byte[] verifycodeImg(@RequestParam(name = "vc_id") String id);

    @PostMapping("log/search")
    Page<LogDTO> search(@RequestBody LogQuery logQuery);

    /**
     * 发送短信
     * @param message
     */
    @PostMapping("/message/sendMessage")
    void sendMessage(@RequestBody MessageDTO message);

    @Component
    class BaseServiceFallbackFactory implements FallbackFactory<IBaseFeignService> {

        @Override
        public IBaseFeignService create(Throwable cause) {
            return new IBaseFeignService() {
                @Override
                public Boolean verifyCodeValid(String id, String vcCode) {
                    LOGGER.error("调用base服务校验验证码失败", cause.getMessage());
                    return true;
                }

                @Override
                public String upload(FileDto fileDto) {
                    LOGGER.error("调用base服务上传文件失败", cause.getMessage());
                    return null;
                }

                @Override
                public List<RegionDTO> listProvince() {
                    LOGGER.error("调用base服务查询省份失败", cause.getMessage());
                    return null;
                }

                @Override
                public List<RegionDTO> listCity(String pCode) {
                    LOGGER.error("调用base服务查询城市失败", cause.getMessage());
                    return null;
                }

                @Override
                public List<RegionDTO> listDistrict(String pCode) {
                    LOGGER.error("调用base服查询地区失败", cause.getMessage());
                    return null;
                }

                @Override
                public byte[] verifycodeImg(String id) {
                    LOGGER.error("调用base服查生成验证码失败", cause.getMessage());
                    return null;
                }

                @Override
                public Page<LogDTO> search(LogQuery logQuery) {
                    LOGGER.error("查询BASE日志出错", cause);
                    return new Page<>();
                }

                @Override
                public void sendMessage(MessageDTO message) {
                    LOGGER.error("短信发送失败", cause);
                }

            };
        }
    }
}
