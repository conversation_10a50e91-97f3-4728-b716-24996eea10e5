package com.holderzone.sso.service.feign;

import com.holderzone.sso.dto.WxQueryThirdPartUserInfoReqDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * 小程序登录调用接口
 *
 * <AUTHOR> <chenz<PERSON><PERSON><EMAIL>>
 * @since 2020-09-16
 */
@Component
@FeignClient(value = "HOLDER-SAAS-AGGREGATION-WEIXIN", fallbackFactory = MinAPPFeignService.MinAPPFeignServiceFallBackFactory.class)
//@FeignClient(value = "HOLDER-SAAS-CLOUD-ENTERPRISE", fallbackFactory = MinAPPFeignService.MinAPPFeignServiceFallBackFactory.class)
public interface MinAPPFeignService {
    /**
     * 用户通过openId登录
     *
     * @param userInfoReqDTO
     * @param enterpriseGuid
     * @return
     */
    @PostMapping("/wx_open/check_third_part_user_info")
    WxQueryThirdPartUserInfoReqDTO loginByDto(@RequestBody WxQueryThirdPartUserInfoReqDTO userInfoReqDTO,
                                              @RequestHeader("enterpriseGuid") String enterpriseGuid);

    @GetMapping("/minapp/disable_user_login")
    Map<String, Object> disableUserLogin(String openId);

    @Component
    class MinAPPFeignServiceFallBackFactory implements FallbackFactory<MinAPPFeignService> {
        private static final Logger LOG = LoggerFactory.getLogger(MinAPPFeignServiceFallBackFactory.class);

        private static final String ERROR_INFO = "小程序【MinAPP】登录熔断";

        @Override
        public MinAPPFeignService create(Throwable throwable) {
            return new MinAPPFeignService() {
                @Override
                public WxQueryThirdPartUserInfoReqDTO loginByDto(WxQueryThirdPartUserInfoReqDTO userInfoReqDTO, String enterpriseGuid) {
                    LOG.error(ERROR_INFO);
                    return null;
                }

                @Override
                public Map<String, Object> disableUserLogin(String openId) {
                    LOG.error(ERROR_INFO);
                    return null;
                }
            };
        }
    }
}
