package com.holderzone.sso.service.feign;

import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2018/09/12 10:52
 */
@Component
@FeignClient(value = "holder-saas-cloud-enterprise", fallbackFactory = EnterpriseFeignService.EnterpriseServiceFallBackFactory.class)
public interface EnterpriseFeignService {

    Logger LOG = LoggerFactory.getLogger(EnterpriseFeignService.class);

    @RequestMapping(value = "/organization/store/code", method = RequestMethod.GET)
    String findEnterPriseByStore(@RequestParam("storeCode") String storeCode);

    @ApiOperation(value = "根据门店code查询商户号(未过期的)")
    @GetMapping("/enterprise/check/{storeCode}")
    String checkEnterprise(@PathVariable("storeCode") String storeCode);

    @GetMapping("/enterprise/check/uid/{uid}")
    Boolean isEffectiveByUid(@PathVariable("uid") String uid);

    /**
     * 查询企业信息
     *
     * @param query
     * @return
     */
    @PostMapping("/enterprise/find")
    EnterpriseDTO findEnterprise(@RequestBody EnterpriseQueryDTO query);

    @Component
    class EnterpriseServiceFallBackFactory implements FallbackFactory<EnterpriseFeignService> {
        @Override
        public EnterpriseFeignService create(Throwable cause) {
            return new EnterpriseFeignService() {
                @Override
                public String findEnterPriseByStore(String storeCode) {
                    LOG.error("调用企业服务查询企业信息失败", cause);
                    return null;
                }

                @Override
                public String checkEnterprise(String storeCode) {
                    LOG.error("调用企业服务查询商户号失败", cause);
                    return null;
                }

                @Override
                public Boolean isEffectiveByUid(String uid) {
                    LOG.error("调用企业服务查询认证产品失败", cause);
                    return null;
                }

                @Override
                public EnterpriseDTO findEnterprise(EnterpriseQueryDTO query) {
                    LOG.error("调用企业服务查询企业信息失败", cause);
                    return null;
                }
            };
        }
    }
}
