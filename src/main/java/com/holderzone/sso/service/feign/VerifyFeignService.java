package com.holderzone.sso.service.feign;

import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 验证码调用服务
 *
 * <AUTHOR>
 * @date 2018-07-20
 */
@FeignClient(value = "base-service", fallbackFactory = VerifyFeignService.BaseServerFallbackFactory.class)
public interface VerifyFeignService {
    Logger LOGGER = LoggerFactory.getLogger(VerifyFeignService.class);

    /**
     * 验证码验证
     *
     * @param vcCode
     * @return
     */
    @PostMapping("verifycode")
    Boolean verifyCodeValid(@RequestParam("vc_id") String id, @RequestParam("vc_code") String vcCode);

    @Component
    class BaseServerFallbackFactory implements FallbackFactory<VerifyFeignService> {

        @Override
        public VerifyFeignService create(Throwable cause) {
            return new VerifyFeignService() {
                @Override
                public Boolean verifyCodeValid(String id, String vcCode) {
                    LOGGER.error("调用base-service:{}失败", "verifycode", cause);
                    return false;
                }
            };
        }
    }

}
