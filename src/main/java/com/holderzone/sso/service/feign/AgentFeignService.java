package com.holderzone.sso.service.feign;

import com.holderzone.resource.common.dto.agent.AgentUserDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @className AgentFeignService
 * @date 2019-08-21 17:27:36
 * @description
 * @program holder-saas-cloud-resource
 */
@Component
@FeignClient(value = "holder-saas-cloud-agent", fallbackFactory = AgentFeignService.AgentFeignServiceFallBackFactory.class)
public interface AgentFeignService {

    /**
     * 通过手机号获取账号信息
     */
    @GetMapping("/agent_user/tel/{guid}")
    AgentUserDTO getAgentUserByTel(@PathVariable("guid") String tel);

    /**
     * 合作商用户绑定openID
     */
    @PostMapping("/agent_user/bind/openid")
    Boolean bindOpenIdForUser(@RequestBody AgentUserDTO user);

    /**
     * 检验账户所属合作商是否正常可用
     */
    @GetMapping("/agent/status/{agentGuid}")
    boolean checkAgentStatus(@PathVariable("agentGuid") String agentGuid);

    /**
     * 用户解绑openId
     */
    @PutMapping("/agent_user/unbind/{guid}")
    Boolean unbindOpenIdForUser(@PathVariable("guid") String guid);

    @Component
    class AgentFeignServiceFallBackFactory implements FallbackFactory<AgentFeignService> {

        private static final Logger LOGGER = LoggerFactory.getLogger(AgentFeignServiceFallBackFactory.class);

        @Override
        public AgentFeignService create(Throwable throwable) {
            return new AgentFeignService() {

                @Override
                public AgentUserDTO getAgentUserByTel(String tel) {
                    return null;
                }

                @Override
                public Boolean bindOpenIdForUser(AgentUserDTO user) {
                    return null;
                }

                @Override
                public boolean checkAgentStatus(String agentGuid) {
                    return false;
                }

                @Override
                public Boolean unbindOpenIdForUser(String userGuid) {
                    return false;
                }
            };
        }
    }
}
