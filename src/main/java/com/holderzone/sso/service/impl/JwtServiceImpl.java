package com.holderzone.sso.service.impl;

import com.holderzone.framework.exception.DecoderException;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.security.SecurityManager;
import com.holderzone.sso.entity.TokenRequestBO;
import com.holderzone.sso.service.JwtService;
import com.holderzone.sso.util.CustomJwtEncoder;
import com.holderzone.sso.util.JwtParser;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Service
public class JwtServiceImpl implements JwtService {

    /**
     * 服务端指定秘钥Key
     */
    @Value("${token.server.secret}")
    private String javaJsonWebProfile;

    /**
     * token失效时间
     */
    @Value("${token.ttl}")
    public int JWT_REFRESH_TTL;
    /**
     * 秘钥key
     */
    public static final String JWT_SECRET = "7786df7fc3a34e26a61c034d5ec8245d";

    @Override
    public SecretKey generateKey() {
        try {
            return Keys.hmacShaKeyFor(SecurityManager.decryptBase64ToByte(javaJsonWebProfile + JWT_SECRET));
        } catch (DecoderException e) {
            throw new BusinessException("生成密钥错误", e);
        }

    }

    @Override
    public String createJWT(long nowMillis, TokenRequestBO tokenRequestBO) {
        Claims claims = getCommonClaims(nowMillis, tokenRequestBO);
        return createJWT(claims);

    }

    @Override
    public String createErpJWT(long nowMillis, TokenRequestBO tokenRequestBO) {
        Claims claims = getErpClaims(nowMillis, tokenRequestBO);
        return createJWT(claims);
    }

    /**
     * 创建jwt
     *
     * @param claims
     * @return
     */
    private String createJWT(Claims claims) {
        SecretKey key = generateKey();
        JwtBuilder builder = Jwts.builder()
                .base64UrlEncodeWith(new CustomJwtEncoder())
                .setHeaderParam("alg", "HS256")
                .setHeaderParam("typ", "JWT")
                .setClaims(claims)
                .setIssuer("Holder.com")
                .signWith(key);
        return builder.compact();
    }


    /**
     * 除erp之外的来源的payload的内容
     *
     * @param nowMillis
     * @param tokenRequestBO
     * @return
     */
    private Claims getCommonClaims(long nowMillis, TokenRequestBO tokenRequestBO) {
        Claims claims = Jwts.claims();
        claims.put(JwtParser.ENTERPRISE_GUID, tokenRequestBO.getEnterpriseGuid() == null ? "" : tokenRequestBO.getEnterpriseGuid());
        claims.put(JwtParser.STORE_NO, tokenRequestBO.getStoreNo() == null ? "" : tokenRequestBO.getStoreNo());
        claims.put(JwtParser.DEVICE_GUID, tokenRequestBO.getDeviceGuid() == null ? "" : tokenRequestBO.getDeviceGuid());
        claims.put(JwtParser.USER_GUID, tokenRequestBO.getUserGuid());
        claims.setIssuedAt(new Date(nowMillis));
        return claims;
    }

    /**
     * erp来源的payload的内容
     *
     * @param nowMillis
     * @param tokenRequestBO
     * @return
     */
    private Claims getErpClaims(long nowMillis, TokenRequestBO tokenRequestBO) {
        Claims claims = Jwts.claims();
        claims.put(JwtParser.ENTERPRISE_GUID, tokenRequestBO.getEnterpriseGuid() == null ? "" : tokenRequestBO.getEnterpriseGuid());
        claims.put(JwtParser.MERCHANT_NO, tokenRequestBO.getMerchantNo() == null ? "" : tokenRequestBO.getMerchantNo());
        claims.put(JwtParser.ENTERPRISE_NAME, tokenRequestBO.getEnterpriseName() == null ? "" : tokenRequestBO.getEnterpriseName());
        claims.put(JwtParser.ACCOUNT, tokenRequestBO.getAccount() == null ? "" : tokenRequestBO.getAccount());
        claims.put(JwtParser.USER_NAME, tokenRequestBO.getUserName() == null ? "" : tokenRequestBO.getUserName());
        claims.put(JwtParser.TEL, tokenRequestBO.getTel() == null ? "" : tokenRequestBO.getTel());
        claims.put(JwtParser.USER_GUID, tokenRequestBO.getUserGuid() == null ? "" : tokenRequestBO.getUserGuid());
        claims.setIssuedAt(new Date(nowMillis));
        return claims;
    }

}
