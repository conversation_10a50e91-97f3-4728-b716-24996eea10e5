package com.holderzone.sso.service.impl;

import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.base.dto.message.ShortMessageDTO;
import com.holderzone.framework.base.dto.message.ShortMessageType;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.sso.entity.LoginSource;
import com.holderzone.sso.entity.LoginType;
import com.holderzone.sso.entity.TokenRequestBO;
import com.holderzone.sso.service.CacheService;
import com.holderzone.sso.service.JwtService;
import com.holderzone.sso.service.feign.AgentFeignService;
import com.holderzone.sso.service.feign.IBaseFeignService;
import com.holderzone.sso.util.GeneratorCode;
import com.holderzone.sso.util.JwtParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.holderzone.sso.entity.LoginSource.AGENT;


@Service
public class CacheServiceImpl implements CacheService {

    @Autowired
    JwtService jwtService;

    @Autowired
    JwtParser jwtParser;

    @Autowired
    IBaseFeignService baseFeignService;

    @Autowired
    private AgentFeignService agentFeignService;

    @Autowired
    RedisTemplate redisTemplate;

    @Value("${token.ttl}")
    public long JWT_REFRESH_TTL;

    @Value("${token.app.ttl}")
    public long JWT_REFRESH_APP_TTL;

    @Value("${token.agent.ttl}")
    public long JWT_REFRESH_AGENT_TTL;

    private static final String TOKEN_PREFIX = "token_";

    private static final String VC_CODE = "SMS_CODE_";

    private static final String VC_USER = "VC_USER_";

    private static final String USER_LOGIN_COUNT = "USER_LOGIN_COUNT:";

    private static final Logger LOGGER = LoggerFactory.getLogger(CacheServiceImpl.class);


    @Override
    public void updateTokenExpire(String token, LoginSource source) {
        String userGuid = jwtParser.acquireUserFromToken(token);
        String enterpriseGuid = jwtParser.acquireEnterpriseFromToken(token);
        long ttl = JWT_REFRESH_TTL;
        if (source == LoginSource.AGENT) {
            ttl = JWT_REFRESH_AGENT_TTL;
        } else if (source != LoginSource.MERCHANT && source != LoginSource.CLOUD && source != LoginSource.ERP_WEB) {
            ttl = JWT_REFRESH_APP_TTL;
        }
        String cacheKey = TOKEN_PREFIX + userGuid + ":" + source.code();
        if (!StringUtils.isEmpty(enterpriseGuid) && enterpriseGuid.length() < 18) {
            return;
        }
        redisTemplate.expire(cacheKey, ttl, TimeUnit.MILLISECONDS);
    }

    @Override
    public void cleanTokenByUser(String userGuid) {
        Set<String> keys = new HashSet<>();
        for (LoginSource loginSource : LoginSource.values()) {
            String code = loginSource.code();
            String key = TOKEN_PREFIX + userGuid + ":" + code;
            keys.add(key);
        }
        redisTemplate.delete(keys);
    }

    @Override
    public void saveUserAuthCode(String uuid, String authCode, String userGuid) {
        //缓存短信验证码,过期时间五分钟
        redisTemplate.opsForValue().set(VC_CODE + uuid, authCode, 5, TimeUnit.MINUTES);
        redisTemplate.opsForValue().set(VC_USER + uuid,userGuid,5, TimeUnit.MINUTES);
    }

    @Override
    public Boolean validateSmsCode(String uuid, String authCode) {
        String code = (String) redisTemplate.opsForValue().get(VC_CODE + uuid);
        boolean right = !StringUtils.isEmpty(code) && code.equals(authCode);
        if (right) {
            redisTemplate.delete(VC_CODE + uuid);
        }
        return right;
    }

    @Override
    public Boolean validateSmsUser(String uuid, String userGuid) {
        String code = (String) redisTemplate.opsForValue().get(VC_USER + uuid);
        boolean right = !StringUtils.isEmpty(code) && code.equals(userGuid);
        if (right) {
            redisTemplate.delete(VC_USER + uuid);
        }
        return right;
    }

    @Override
    public String sendVCode(String tel) {
        String code = String.valueOf(GeneratorCode.generatorCode(6));
        String key = IDUtils.nextId();
        redisTemplate.opsForValue().set(VC_CODE + key + tel, code, 5, TimeUnit.MINUTES);
        MessageDTO messageDTO = new MessageDTO();
        ShortMessageDTO shortMessageDTO = new ShortMessageDTO();
        HashMap<String, String> map = new HashMap<>();
        map.put("Code", code);
        shortMessageDTO.setParams(map);
        shortMessageDTO.setPhoneNumber(tel);
        shortMessageDTO.setShortMessageType(ShortMessageType.COMMON_VERCODE);
        messageDTO.setShortMessage(shortMessageDTO);
        messageDTO.setMessageType(com.holderzone.framework.base.dto.message.MessageType.SHORT_MESSAGE);
        try {
            baseFeignService.sendMessage(messageDTO);
        } catch (Exception e) {
            throw new BusinessException("短信发送失败");
        }
        return key;
    }

    @Override
    public void cleanVCode(String vid) {
        redisTemplate.delete(VC_CODE + vid);
    }

    @Override
    public void cleanLoginCount(String userInfoKey) {
        redisTemplate.delete(USER_LOGIN_COUNT + userInfoKey);
    }

    @Override
    public Integer incrementLoginCount(String userInfoKey) {
        int loginCount = redisTemplate.opsForValue().increment(USER_LOGIN_COUNT + userInfoKey, 1).intValue();
        redisTemplate.expire(USER_LOGIN_COUNT + userInfoKey, 5, TimeUnit.MINUTES);
        return loginCount;
    }

    @Override
    public Integer selectLoginCount(String userInfoKey) {
        Integer loginCount = (Integer) redisTemplate.opsForValue().get(USER_LOGIN_COUNT + userInfoKey);
        return loginCount == null ? 0 : loginCount;
    }

    @Override
    public boolean cleanToken(String token, String source) throws Exception {
        String userGuid = jwtParser.acquireUserFromToken(token);
        String enterpriseGuid = jwtParser.acquireEnterpriseFromToken(token);
        String key = TOKEN_PREFIX + userGuid + ":" + source;
        if (!StringUtils.isEmpty(enterpriseGuid) && enterpriseGuid.length() < 18) {
            key = key + ":" + enterpriseGuid;
        }
        if (AGENT.code().equals(source)) {
            agentFeignService.unbindOpenIdForUser(userGuid);
        }
        return redisTemplate.delete(key);
    }

    @Override
    public String saveTokenForClient(TokenRequestBO tokenRequestBO, LoginType loginType, LoginSource source) {
        String token;
        if (source == LoginSource.ERP_WEB || source == LoginSource.ERP_NOT_WEB) {
            token = jwtService.createErpJWT(System.currentTimeMillis(), tokenRequestBO);
        } else {
            token = jwtService.createJWT(System.currentTimeMillis(), tokenRequestBO);
        }

        String cacheKey = TOKEN_PREFIX + tokenRequestBO.getUserGuid() + ":" + source.code();
        /*
        if (loginType == LoginType.WEB) {
            cacheKey = TOKEN_PREFIX + tokenRequestBO.getUserGuid();
        }
        */
        long ttl = JWT_REFRESH_TTL;
        if (source == LoginSource.AGENT) {
            ttl = JWT_REFRESH_AGENT_TTL;
        } else if (source != LoginSource.MERCHANT && source != LoginSource.CLOUD && source != LoginSource.ERP_WEB) {
            ttl = JWT_REFRESH_APP_TTL;
        }
        String enterpriseGuid = tokenRequestBO.getEnterpriseGuid();
        if (!StringUtils.isEmpty(enterpriseGuid) && enterpriseGuid.length() < 18) {
            cacheKey = cacheKey + ":" + tokenRequestBO.getEnterpriseGuid();
            redisTemplate.opsForValue().set(cacheKey, token);
            return token;
        }
        if (source == LoginSource.BI || source == LoginSource.PHONE) {
            redisTemplate.opsForValue().set(cacheKey, token);
            return token;
        }
        redisTemplate.opsForValue().set(cacheKey, token, ttl, TimeUnit.MILLISECONDS);
        LOGGER.info("保存token到redis,userGUID = {},JWT_REFRESH_TTL={}", tokenRequestBO.getUserGuid(), ttl);
        return token;
    }


    @Override
    public boolean isServerExistToken(String token, String source, Integer loginType) {
        if (StringUtils.isEmpty(token)) {
            return false;
        }
        try {
            String userGuid = jwtParser.acquireUserFromToken(token);
            String enterpriseGuid = jwtParser.acquireEnterpriseFromToken(token);
            String cacheKey = TOKEN_PREFIX + userGuid + ":" + source;
            /*
            if (LoginType.WEB.getType().equals(loginType)) {
                cacheKey = TOKEN_PREFIX + userGuid;
            }
            */
            if (!StringUtils.isEmpty(enterpriseGuid) && enterpriseGuid.length() < 18) {
                cacheKey = cacheKey + ":" + enterpriseGuid;
            }
            Object serverToken = redisTemplate.opsForValue().get(cacheKey);
            return serverToken != null && serverToken.equals(token);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

}
