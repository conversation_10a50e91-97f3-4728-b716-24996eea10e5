package com.holderzone.sso.service.impl;

import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.base.dto.message.ShortMessageDTO;
import com.holderzone.framework.base.dto.message.ShortMessageType;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.sso.entity.dto.LoginDTO;
import com.holderzone.sso.service.BusinessService;
import com.holderzone.sso.service.feign.IBaseFeignService;
import org.apache.rocketmq.common.message.Message;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2019/03/02 14:31
 */
@Service
public class BusinessServiceImpl implements BusinessService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BusinessServiceImpl.class);

    @Autowired
    IBaseFeignService iBaseFeignService;
    @Autowired
    private DefaultRocketMqProducer producer;


    private static final String STORE_LOGIN_TIME_TOPIC = "enterprise-store-login-time-topic";
    private static final String STORE_LOGIN_TIME_TAG = "enterprise-store-login-time-tag";


    /**
     * 发送登录消息
     *
     * @param loginDTO
     */
    @Override
    public void sendMessage(LoginDTO loginDTO) {
        //门店号为空表示非门店登录,不用发送登录消息
        if (StringUtils.isEmpty(loginDTO.getStoreNo())) {
            return;
        }
        UnMessage<Long> unMessage = new UnMessage<>();
        unMessage.setEnterpriseGuid(loginDTO.getMerchantNo());
        unMessage.setMessageType(loginDTO.getStoreNo());
        unMessage.setMessage(System.currentTimeMillis());
        unMessage.setStoreGuid(loginDTO.getStoreNo());
        try {
            producer.sendMessage(new Message(STORE_LOGIN_TIME_TOPIC, STORE_LOGIN_TIME_TAG, JacksonUtils.toJsonByte(unMessage)));
        } catch (Exception e) {
            LOGGER.error("发送登录时间更新消息到mq失败", e);
            e.printStackTrace();
        }
    }


    @Override
    public void sendShortMsg(String tel , String authCode) {
        MessageDTO messageDTO = new MessageDTO();
        ShortMessageDTO shortMessageDTO = new ShortMessageDTO();
        HashMap<String, String> map = new HashMap<>(1);
        map.put("Code", authCode);
        shortMessageDTO.setParams(map);
        shortMessageDTO.setPhoneNumber(tel);
        shortMessageDTO.setShortMessageType(ShortMessageType.COMMON_VERCODE);
        messageDTO.setShortMessage(shortMessageDTO);
        messageDTO.setMessageType(com.holderzone.framework.base.dto.message.MessageType.SHORT_MESSAGE);
        iBaseFeignService.sendMessage(messageDTO);

    }
}
