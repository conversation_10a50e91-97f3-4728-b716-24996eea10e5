package com.holderzone.sso.handler;

import com.holderzone.framework.response.Result;
import com.holderzone.resource.common.dto.user.UserDTO;
import com.holderzone.sso.entity.TokenRequestBO;
import com.holderzone.sso.entity.dto.LoginDTO;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.handler.entity.transform.LoginTransform;
import com.holderzone.sso.service.BusinessService;
import com.holderzone.sso.service.CacheService;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 15:40
 * @description
 */
public abstract class AbstractLoginHandler {


    public CacheService cacheService;

    public BusinessService businessService;

    public LoginTransform loginTransform = LoginTransform.INSTANCE;

    private Boolean verifyEnable;

    public AbstractLoginHandler(CacheService cacheService, BusinessService businessService, Boolean verifyEnable) {
        this.cacheService = cacheService;
        this.businessService = businessService;
        this.verifyEnable = verifyEnable;
    }

    /**
     * 用户认证
     *
     * @param userInfo
     * @return
     */
    public abstract AuthResultBO auth(LoginUserInfoBO userInfo);

    /**
     * 判断是否校验验证码
     *
     * @param userInfoBO 登录的用户信息
     * @return
     */
    public boolean isValidateVerifyCode(LoginUserInfoBO userInfoBO) {
        return verifyEnable;
    }

    /**
     * 验证码校验
     *
     * @param userInfo
     * @param selectedUserInfoMap
     * @return
     */
    public boolean verifyCodeValidate(LoginUserInfoBO userInfo, Map<String, Object> selectedUserInfoMap) {
        return true;
    }

    /**
     * 登录后处理事项，组装返回实体
     *
     * @param loginUserInfo
     * @param selectedUserInfoMap
     * @return
     */
    public Result afterLogin(LoginUserInfoBO loginUserInfo, Map<String, Object> selectedUserInfoMap) {
        String enterpriseGuid = (String) selectedUserInfoMap.get("enterpriseGuid");
        String userGuid = (String) selectedUserInfoMap.get("userGuid");
        TokenRequestBO tokenRequestBO = new TokenRequestBO(enterpriseGuid, loginUserInfo.getStoreNo(), loginUserInfo.getDeviceGuid(), userGuid);
        String token = cacheService.saveTokenForClient(tokenRequestBO, loginUserInfo.getLoginType(), loginUserInfo.getLoginSource());
        LoginDTO loginDTO = loginTransform.userInfoBoToDto(loginUserInfo);
        businessService.sendMessage(loginDTO);
        return Result.buildSuccessResult(token);
    }


    /**
     * 处理登录失败
     *
     * @param loginUserInfoBO
     * @param reason
     */
    public void handleAuthFailure(LoginUserInfoBO loginUserInfoBO, String reason) {
    }

    /**
     * 重置密码
     *
     * @param userGuid
     * @param password
     * @return
     */
    public void resetPassword(String userGuid, String password) {
    }

    /**
     * 查询用户信息
     *
     * @param userDTO
     * @return
     */
    public Map<String, Object> findUserInfo(UserDTO userDTO) {
        return null;
    }

    /**
     * 获取手机号
     *
     * @param userInfoMap
     * @return
     */
    public String getTel(Map<String, Object> userInfoMap) {
        return "";
    }

    /**
     * 获取userGuid
     *
     * @param userInfoMap
     * @return
     */
    public String getUserGuid(Map<String, Object> userInfoMap) {
        return "";
    }
}
