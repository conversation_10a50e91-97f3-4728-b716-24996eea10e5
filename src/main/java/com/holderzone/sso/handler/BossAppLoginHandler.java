package com.holderzone.sso.handler;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.sso.handler.auth.MerchantNumAuthHandler;
import com.holderzone.sso.handler.auth.PhoneNumAuthHandler;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.BusinessService;
import com.holderzone.sso.service.CacheService;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:13
 * @description
 */
public class BossAppLoginHandler extends AbstractLoginHandler {

    private MerchantNumAuthHandler merchantNumAuthHandler;

    private PhoneNumAuthHandler phoneNumAuthHandler;

    public BossAppLoginHandler(CacheService cacheService, BusinessService businessService,
                               MerchantNumAuthHandler merchantNumAuthHandler, PhoneNumAuthHandler phoneNumAuthHandler,
                               Boolean verifyEnable) {
        super(cacheService, businessService, verifyEnable);
        this.merchantNumAuthHandler = merchantNumAuthHandler;
        this.phoneNumAuthHandler = phoneNumAuthHandler;
    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        if (!StringUtils.isEmpty(userInfo.getTel())) {
            return phoneNumAuthHandler.auth(userInfo);
        }
        return merchantNumAuthHandler.auth(userInfo);
    }

    @Override
    public boolean isValidateVerifyCode(LoginUserInfoBO userInfoBO) {
        return false;
    }

}
