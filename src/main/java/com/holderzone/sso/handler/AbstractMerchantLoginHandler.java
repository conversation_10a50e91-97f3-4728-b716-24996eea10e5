package com.holderzone.sso.handler;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.user.UserDTO;
import com.holderzone.sso.service.BusinessService;
import com.holderzone.sso.service.CacheService;
import com.holderzone.sso.service.feign.UserFeignService;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/12/23 下午 17:53
 * @description
 */
public abstract class AbstractMerchantLogin<PERSON>and<PERSON> extends AbstractLoginHandler {

    private UserFeignService userFeignService;

    public AbstractMerchantLoginHandler(CacheService cacheService, BusinessService businessService, Boolean verifyEnable,
                                        UserFeignService userFeignService) {
        super(cacheService, businessService, verifyEnable);
        this.userFeignService = userFeignService;
    }

    @Override
    public void resetPassword(String userGuid, String password) {
        UserDTO userDTO = new UserDTO();
        userDTO.setUserGuid(userGuid);
        userDTO.setPassword(password);
        userFeignService.resetPassword(userDTO);
    }

    @Override
    public Map<String, Object> findUserInfo(UserDTO userDTO) {
        UserDTO user = userFeignService.forgetPassword(userDTO);
        if (user == null) {
            return null;
        }
        return JacksonUtils.toMap(JacksonUtils.writeValueAsString(user));
    }

    @Override
    public String getTel(Map<String, Object> userInfoMap) {
        return (String) userInfoMap.get("tel");
    }

    @Override
    public String getUserGuid(Map<String, Object> userInfoMap) {
        return (String) userInfoMap.get("userGuid");
    }
}
