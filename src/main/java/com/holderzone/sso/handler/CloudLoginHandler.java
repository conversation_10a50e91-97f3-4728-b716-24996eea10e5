package com.holderzone.sso.handler;

import com.holderzone.framework.response.Result;
import com.holderzone.sso.handler.auth.CloudAuthHandler;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.BusinessService;
import com.holderzone.sso.service.CacheService;
import com.holderzone.sso.service.feign.IBaseFeignService;
import com.holderzone.sso.service.feign.ICloudUserService;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:09
 * @description
 */
public class CloudLoginHandler extends AbstractLoginHandler {


    private CloudAuthHandler cloudUserHandler;

    private IBaseFeignService baseFeignService;

    private ICloudUserService cloudUserService;

    private static final Integer VERIFY_CODE_LOGIN_COUNT = 3;

    private static final Integer DISABLE_USER_LOGIN_COUNT = 10;

    public CloudLoginHandler(CloudAuthHandler cloudUserHandler, IBaseFeignService baseFeignService,
                             ICloudUserService cloudUserService, BusinessService businessService, CacheService cacheService,
                             Boolean verifyEnable) {
        super(cacheService, businessService, verifyEnable);
        this.cloudUserHandler = cloudUserHandler;
        this.baseFeignService = baseFeignService;
        this.cloudUserService = cloudUserService;
    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        return cloudUserHandler.auth(userInfo);
    }

    @Override
    public boolean isValidateVerifyCode(LoginUserInfoBO userInfoBO) {
        Integer loginCount = cacheService.selectLoginCount(userInfoBO.getUsername());
        return loginCount >= VERIFY_CODE_LOGIN_COUNT;
    }

    @Override
    public boolean verifyCodeValidate(LoginUserInfoBO userInfo, Map<String, Object> selectedUserInfoMap) {
        return baseFeignService.verifyCodeValid(userInfo.getVid(), userInfo.getvCode());
    }

    @Override
    public Result afterLogin(LoginUserInfoBO loginUserInfo, Map<String, Object> selectedUserInfoMap) {
        cacheService.cleanLoginCount(loginUserInfo.getUsername());
        return super.afterLogin(loginUserInfo, selectedUserInfoMap);
    }

    @Override
    public void handleAuthFailure(LoginUserInfoBO loginUserInfoBO, String reason) {
        Integer loginCount = cacheService.incrementLoginCount(loginUserInfoBO.getUsername());
        if (loginCount > DISABLE_USER_LOGIN_COUNT) {
            cloudUserService.disableUserAtLogin(loginUserInfoBO.getUsername());
        }
    }

}
