package com.holderzone.sso.handler;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.sso.handler.auth.PhoneNumAuthHandler;
import com.holderzone.sso.handler.auth.StoreNumAuthHandler;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.BusinessService;
import com.holderzone.sso.service.CacheService;
import com.holderzone.sso.service.feign.UserFeignService;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:13
 * @description
 */
public class MerchantNotWebLoginHandler extends AbstractMerchantLoginHandler {

    private StoreNumAuthHandler storeNumAuthHandler;

    private PhoneNumAuthHandler phoneNumAuthHandler;

    public MerchantNotWebLoginHandler(CacheService cacheService, BusinessService businessService,
                                      StoreNumAuthHandler storeNumAuth<PERSON>and<PERSON>, PhoneNumAuthHandler phoneNumAuth<PERSON>and<PERSON>,
                                      <PERSON><PERSON>an verifyEnable, UserFeignService userFeignService) {
        super(cacheService, businessService, verifyEnable, userFeignService);
        this.storeNumAuthHandler = storeNumAuthHandler;
        this.phoneNumAuthHandler = phoneNumAuthHandler;
    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        if (!StringUtils.isEmpty(userInfo.getTel())) {
            return phoneNumAuthHandler.auth(userInfo);
        }
        return storeNumAuthHandler.auth(userInfo);

    }

    @Override
    public boolean isValidateVerifyCode(LoginUserInfoBO userInfoBO) {
        return false;
    }


}
