package com.holderzone.sso.handler;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.security.SecurityManager;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.user.UserDTO;
import com.holderzone.sso.entity.ErpHttpResultBO;
import com.holderzone.sso.entity.ErpUserBO;
import com.holderzone.sso.entity.TokenRequestBO;
import com.holderzone.sso.entity.dto.ErpRequestDTO;
import com.holderzone.sso.entity.dto.ErpUserQueryDTO;
import com.holderzone.sso.entity.dto.ErpUserUpdateDTO;
import com.holderzone.sso.handler.auth.ErpMerchantNumAuthHandler;
import com.holderzone.sso.handler.auth.ErpTelAuthHandler;
import com.holderzone.sso.handler.config.MdmProperties;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.BusinessService;
import com.holderzone.sso.service.CacheService;
import com.holderzone.sso.service.feign.IBaseFeignService;
import com.holderzone.sso.util.SignatureUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

import static com.holderzone.sso.entity.LoginConstant.MDM_SUCCESS_CODE;

/**
 * <AUTHOR>
 * @date 2019/12/03 下午 16:06
 * @description
 */
public abstract class AbstractErpLoginHandler extends AbstractLoginHandler {

    private static final Logger log = LoggerFactory.getLogger(AbstractErpLoginHandler.class);

    protected IBaseFeignService baseFeignService;

    protected ErpMerchantNumAuthHandler erpMerchantNumAuthHandler;

    protected ErpTelAuthHandler erpTelAuthHandler;

    protected RestTemplate restTemplate;

    protected MdmProperties mdmProperties;

    public AbstractErpLoginHandler(IBaseFeignService baseFeignService, BusinessService businessService, CacheService cacheService,
                                   ErpMerchantNumAuthHandler erpMerchantNumAuthHandler, ErpTelAuthHandler erpTelAuthHandler,
                                   RestTemplate restTemplate, MdmProperties mdmProperties) {
        super(cacheService, businessService, true);
        this.baseFeignService = baseFeignService;
        this.erpMerchantNumAuthHandler = erpMerchantNumAuthHandler;
        this.erpTelAuthHandler = erpTelAuthHandler;
        this.restTemplate = restTemplate;
        this.mdmProperties = mdmProperties;
    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        if (!StringUtils.isEmpty(userInfo.getTel())) {
            return erpTelAuthHandler.auth(userInfo);
        }
        return erpMerchantNumAuthHandler.auth(userInfo);
    }

    @Override
    public boolean verifyCodeValidate(LoginUserInfoBO userInfo, Map<String, Object> selectedUserInfoMap) {
        return baseFeignService.verifyCodeValid(userInfo.getVid(), userInfo.getvCode());
    }

    @Override
    public Result afterLogin(LoginUserInfoBO loginUserInfo, Map<String, Object> selectedUserInfoMap) {
        TokenRequestBO tokenRequestBO = new TokenRequestBO();
        tokenRequestBO.setEnterpriseGuid((String) selectedUserInfoMap.get("enterpriseGuid"));
        tokenRequestBO.setMerchantNo((String) selectedUserInfoMap.get("merchantNo"));
        tokenRequestBO.setEnterpriseName((String) selectedUserInfoMap.get("enterpriseName"));
        tokenRequestBO.setAccount((String) selectedUserInfoMap.get("account"));
        tokenRequestBO.setUserName((String) selectedUserInfoMap.get("name"));
        tokenRequestBO.setTel((String) selectedUserInfoMap.get("tel"));
        tokenRequestBO.setUserGuid((String) selectedUserInfoMap.get("thirdNo"));
        String token = cacheService.saveTokenForClient(tokenRequestBO, loginUserInfo.getLoginType(), loginUserInfo.getLoginSource());
        return Result.buildSuccessResult(token);
    }

    @Override
    public void resetPassword(String userGuid, String password) {
        ErpUserBO userBO = requestUserByUserGuidOrTel(userGuid, null);
        if (userBO == null) {
            throw new BusinessException("用户不存在");
        }
        updateUserPassword(userGuid, userBO.getThirdNo(), password, userBO.getEnterpriseGuid());
    }

    @Override
    public Map<String, Object> findUserInfo(UserDTO userDTO) {
        ErpUserBO userBO = null;
        if (!StringUtils.isEmpty(userDTO.getTel())) {
            userBO = requestUserByUserGuidOrTel(null, userDTO.getTel());
        } else if (!StringUtils.isEmpty(userDTO.getMerchantNo()) && !StringUtils.isEmpty(userDTO.getAccount())) {
            userBO = requestUserByAccount(userDTO.getMerchantNo(), userDTO.getAccount());
        } else {
            throw new BusinessException("手机号或者商户号，账号为空");
        }
        return userBO == null ? null : JacksonUtils.toMap(JacksonUtils.writeValueAsString(userBO));
    }

    @Override
    public String getTel(Map<String, Object> userInfoMap) {
        return (String) userInfoMap.get("tel");
    }

    @Override
    public String getUserGuid(Map<String, Object> userInfoMap) {
        return (String) userInfoMap.get("guid");
    }

    /**
     * 更新用户密码
     *
     * @param userGuid
     * @param password
     */
    private void updateUserPassword(String userGuid, String thirdNo, String password, String enterpriseGuid) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("Content-Type", "application/json;charset=utf-8");
        httpHeaders.set("enterpriseGuid", enterpriseGuid);

        ErpUserUpdateDTO updateDTO = new ErpUserUpdateDTO();
        updateDTO.setGuid(userGuid);
        updateDTO.setThirdNo(thirdNo);
        updateDTO.setPassword(SecurityManager.entryptMd5(password, true));

        ErpRequestDTO<ErpUserUpdateDTO> requestDTO = new ErpRequestDTO<>();
        requestDTO.setRequest(updateDTO);
        requestDTO.setDeveloperId(mdmProperties.getDeveloperId());
        requestDTO.setSignature(SignatureUtil.syncSign(requestDTO, mdmProperties.getSignature()));

        HttpEntity<ErpRequestDTO<ErpUserUpdateDTO>> requestEntity = new HttpEntity<>(requestDTO, httpHeaders);
        String resultStr = restTemplate.exchange(mdmProperties.getUpdateUserUrl(), HttpMethod.PUT, requestEntity, String.class).getBody();
        log.info("请求mdm结果: {}", resultStr);
        ErpHttpResultBO resultBO = JacksonUtils.toObject(ErpHttpResultBO.class, resultStr);
        String code = resultBO.getCode();
        if (!MDM_SUCCESS_CODE.equals(code)) {
            log.error("更新用户失败，mdm内部错误:{}", resultStr);
            throw new BusinessException("更新用户失败");
        }

    }

    /**
     * 根据商户号和账号查询用户信息
     *
     * @param merchantNo
     * @param account
     * @return
     */
    private ErpUserBO requestUserByAccount(String merchantNo, String account) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("Content-Type", "application/json;charset=utf-8");

        ErpUserQueryDTO queryDTO = new ErpUserQueryDTO();
        queryDTO.setMerchantNo(merchantNo);
        queryDTO.setAccount(account);

        ErpRequestDTO<ErpUserQueryDTO> requestDTO = new ErpRequestDTO<>();
        requestDTO.setRequest(queryDTO);
        requestDTO.setDeveloperId(mdmProperties.getDeveloperId());
        requestDTO.setSignature(SignatureUtil.syncSign(requestDTO, mdmProperties.getSignature()));

        HttpEntity<ErpRequestDTO<ErpUserQueryDTO>> requestEntity = new HttpEntity<>(requestDTO, httpHeaders);
        String resultStr = restTemplate.exchange(mdmProperties.getFindUserByAccountUrl(), HttpMethod.POST, requestEntity, String.class).getBody();
        log.info("请求mdm结果: {}", resultStr);
        ErpHttpResultBO resultBO = JacksonUtils.toObject(ErpHttpResultBO.class, resultStr);
        String code = resultBO.getCode();
        if (!MDM_SUCCESS_CODE.equals(code)) {
            log.error("请求用户失败，mdm内部错误:{}", resultStr);
            throw new BusinessException("请求用户失败");
        }
        return resultBO.getData();
    }

    /**
     * 根据userGuid或者tel查询用户信息
     *
     * @param userGuid
     * @param tel
     * @return
     */
    private ErpUserBO requestUserByUserGuidOrTel(String userGuid, String tel) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("Content-Type", "application/json;charset=utf-8");

        ErpUserQueryDTO queryDTO = new ErpUserQueryDTO();
        if (!StringUtils.isEmpty(userGuid)) {
            queryDTO.setGuid(userGuid);
        }
        if (!StringUtils.isEmpty(tel)) {
            queryDTO.setTel(tel);
        }

        ErpRequestDTO<ErpUserQueryDTO> requestDTO = new ErpRequestDTO<>();
        requestDTO.setRequest(queryDTO);
        requestDTO.setDeveloperId(mdmProperties.getDeveloperId());
        requestDTO.setSignature(SignatureUtil.syncSign(requestDTO, mdmProperties.getSignature()));

        HttpEntity<ErpRequestDTO<ErpUserQueryDTO>> requestEntity = new HttpEntity<>(requestDTO, httpHeaders);
        String resultStr = restTemplate.exchange(mdmProperties.getFindUserByUserGuidOrTelUrl(), HttpMethod.POST, requestEntity, String.class).getBody();
        log.info("请求mdm结果: {}", resultStr);
        ErpHttpResultBO resultBO = JacksonUtils.toObject(ErpHttpResultBO.class, resultStr);
        String code = resultBO.getCode();
        if (!MDM_SUCCESS_CODE.equals(code)) {
            log.error("请求用户失败，mdm内部错误:{}", resultStr);
            throw new BusinessException("请求用户失败");
        }
        return resultBO.getData();
    }
}
