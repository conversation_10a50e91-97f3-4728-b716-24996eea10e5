package com.holderzone.sso.handler;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.sso.handler.auth.ErpMerchantNumAuthHandler;
import com.holderzone.sso.handler.auth.ErpTelAuthHandler;
import com.holderzone.sso.handler.config.MdmProperties;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.BusinessService;
import com.holderzone.sso.service.CacheService;
import com.holderzone.sso.service.feign.IBaseFeignService;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:09
 * @description
 */
public class ErpWebLoginHandler extends AbstractErpLoginHandler {


    public ErpWebLoginHandler(IBaseFeignService baseFeignService, BusinessService businessService, CacheService cacheService,
                              ErpMerchantNumAuthHandler erpMerchantNumAuthHandler, ErpTelAuthHandler erpTelAuthHandler,
                              RestTemplate restTemplate , MdmProperties mdmProperties) {
        super(baseFeignService, businessService, cacheService, erpMerchantNumAuthHandler, erpTelAuthHandler, restTemplate, mdmProperties);
    }


    @Override
    public boolean isValidateVerifyCode(LoginUserInfoBO userInfoBO) {
        if (!StringUtils.isEmpty(userInfoBO.getTel())) {
            return false;
        }
        return super.isValidateVerifyCode(userInfoBO);
    }


}
