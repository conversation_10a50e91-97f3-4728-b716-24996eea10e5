package com.holderzone.sso.handler.auth;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.user.UserDTO;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.feign.EnterpriseFeignService;
import com.holderzone.sso.service.feign.UserFeignService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:08
 * @description
 */
public class PhoneNumAuthHandler extends AuthHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(PhoneNumAuthHandler.class);

    private UserFeignService userFeignService;

    private EnterpriseFeignService enterpriseFeignService;

    public PhoneNumAuthHandler(EnterpriseFeignService enterpriseFeignService, UserFeignService userFeignService) {
        this.userFeignService = userFeignService;
        this.enterpriseFeignService = enterpriseFeignService;
    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        if (StringUtils.isEmpty(userInfo.getTel())) {
            return AuthResultBO.buildPhoneNumEmptyResult();
        }
        if (StringUtils.isEmpty(userInfo.getPassword())) {
            return AuthResultBO.buildPasswordEmptyResult();
        }
        UserDTO userDTO = userFeignService.loginUserByTel(userInfo.getTel(), userInfo.getPassword(), userInfo.getEnterpriseGuid());
        if (StringUtils.isEmpty(userInfo.getEnterpriseGuid())) {
            // 非holder登录
            AuthResultBO authResultBO = validateUserDto(userDTO);
            if (authResultBO.isSuccess()) {
                EnterpriseDTO enterpriseDTO = validateEnterprise(enterpriseFeignService, userDTO.getMerchantNo(), null);
                if (Objects.isNull(enterpriseDTO)) {
                    return AuthResultBO.buildAuthFailedResult("商户不可用");
                }
            }
            return authResultBO;
        } else {
            // holder登录
            if (userDTO == null) {
                // 您的账号未同步到当前应用，请联系管理员在应用中先同步账号
                return AuthResultBO.buildHolderUserInfoErrorResult();
            }
            if ("0".equals(userDTO.getIsEnabled())) {
                // 您的账号在应用内已被禁用，不支持打开应用
                return AuthResultBO.buildHolderUserUnableResult();
            }
            return AuthResultBO.buildSuccessResult(userDTO);
        }
    }
}
