package com.holderzone.sso.handler.auth;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:08
 * @description
 */
public class ErpTelAuthHandler extends ErpAuthHandler {


    public ErpTelAuthHandler(RestTemplate restTemplate, String mdmUrl) {
        super(restTemplate, mdmUrl);
    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        if (StringUtils.isEmpty(userInfo.getTel())) {
            return AuthResultBO.buildPhoneNumEmptyResult();
        }
        if (StringUtils.isEmpty(userInfo.getPassword())) {
            return AuthResultBO.buildPasswordEmptyResult();
        }
        Map<String, String> map = new HashMap<>(2);
        map.put("tel", userInfo.getTel());
        map.put("password", userInfo.getPassword());
        map.put("enterpriseGuid", userInfo.getEnterpriseGuid());
        return requestMdm(map);
    }
}
