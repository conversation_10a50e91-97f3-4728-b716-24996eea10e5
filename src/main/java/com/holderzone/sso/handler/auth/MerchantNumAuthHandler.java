package com.holderzone.sso.handler.auth;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.user.UserDTO;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.feign.EnterpriseFeignService;
import com.holderzone.sso.service.feign.UserFeignService;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:08
 * @description
 */
public class MerchantNumAuthHandler extends AuthHandler {

    private EnterpriseFeignService enterpriseFeignService;

    private UserFeignService userFeignService;

    public MerchantNumAuthHandler(EnterpriseFeignService enterpriseFeignService, UserFeignService userFeignService) {
        this.enterpriseFeignService = enterpriseFeignService;
        this.userFeignService = userFeignService;
    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        if (StringUtils.isEmpty(userInfo.getMerchantNo())) {
            return AuthResultBO.buildMerchantNumEmptyResult();
        }
        if (StringUtils.isEmpty(userInfo.getUsername())) {
            return AuthResultBO.buildUserNameEmptyResult();
        }
        if (StringUtils.isEmpty(userInfo.getPassword())) {
            return AuthResultBO.buildPasswordEmptyResult();
        }
        EnterpriseDTO enterpriseDTO = validateEnterprise(enterpriseFeignService, userInfo.getMerchantNo(), null);
        if (Objects.isNull(enterpriseDTO)) {
            return AuthResultBO.buildAuthFailedResult("商户已被删除");
        }
        UserDTO userDTO = userFeignService.loginUser(userInfo.getMerchantNo(), userInfo.getUsername(), userInfo.getPassword());
        return validateUserDto(userDTO);
    }


}
