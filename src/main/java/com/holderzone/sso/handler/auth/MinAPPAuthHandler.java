package com.holderzone.sso.handler.auth;

import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.sso.dto.WxQueryThirdPartUserInfoReqDTO;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.feign.EnterpriseFeignService;
import com.holderzone.sso.service.feign.MinAPPFeignService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;

/**
 * <AUTHOR>  <chenz<PERSON><PERSON><EMAIL>>
 * @since 2020-09-16
 */
public class MinAPPAuthHandler extends AuthHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(MinAPPAuthHandler.class);

    private final MinAPPFeignService minAPPFeignService;

    private final EnterpriseFeignService enterpriseFeignService;

    public MinAPPAuthHandler(MinAPPFeignService minAPPFeignService, EnterpriseFeignService enterpriseFeignService) {
        this.minAPPFeignService = minAPPFeignService;
        this.enterpriseFeignService = enterpriseFeignService;
    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        LOGGER.info("微信小程序登录 , 请求参数为 : {}", userInfo);
        String openId = userInfo.getOpenId();
        String tel = userInfo.getTel();
        String password = userInfo.getPassword();
        String merchantNo = userInfo.getMerchantNo();
        String storeNo = userInfo.getStoreNo();
        // 做必要的数据验证工作
        if (null == openId) {
            // TODO 处理是否需要必填
        }
        if (null == tel) {
            // TODO 处理是否需要必填
        }
        if (null == password) {
            // TODO 处理是否需要必填
        }
        if (null == merchantNo) {
            return AuthResultBO.buildMerchantNumEmptyResult();
        }
        EnterpriseQueryDTO enterpriseQueryDTO = new EnterpriseQueryDTO();
        enterpriseQueryDTO.setUid(merchantNo);
        // 通过商户号获取enterpriseGuid
        EnterpriseDTO enterprise = enterpriseFeignService.findEnterprise(enterpriseQueryDTO);
        if (null == enterprise) {
            LOGGER.error("获取企业参数出错 , 请求参数 : {}", enterpriseQueryDTO.getUid());
            return AuthResultBO.buildMinAppInfoErrorResult();
        }
        String enterpriseGuid = enterprise.getEnterpriseGuid();
        WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO = new WxQueryThirdPartUserInfoReqDTO();
        wxQueryThirdPartUserInfoReqDTO.setOpenId(openId);
        wxQueryThirdPartUserInfoReqDTO.setPassword(password);
        wxQueryThirdPartUserInfoReqDTO.setTel(tel);
        // 如果通不过数据验证,则直接返回
        WxQueryThirdPartUserInfoReqDTO resDTO = minAPPFeignService.loginByDto(wxQueryThirdPartUserInfoReqDTO, enterpriseGuid);
        if (resDTO == null) {
            LOGGER.error("微信小程序登录错误 , 请求参数 : {} , 响应数据 : {}", userInfo, resDTO);
            return AuthResultBO.buildMinAppInfoErrorResult();
        }
        // 做一些其他的校验,转换数据到指定的结果
        HashMap<String, Object> map = new HashMap<>();
        map.putIfAbsent("userGuid", resDTO.getGuid());
        map.putIfAbsent("enterpriseGuid", enterpriseGuid);
        map.putIfAbsent("openId", userInfo.getOpenId());
        return AuthResultBO.buildSuccessResult(map);
    }
}
