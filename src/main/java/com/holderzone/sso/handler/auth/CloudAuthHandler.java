package com.holderzone.sso.handler.auth;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.user.UserDTO;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.feign.ICloudUserService;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:08
 * @description
 */
public class CloudAuthHandler extends AuthHandler {

    private ICloudUserService cloudUserService;

    public CloudAuthHandler(ICloudUserService cloudUserService) {
        this.cloudUserService = cloudUserService;
    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        if (StringUtils.isEmpty(userInfo.getUsername())) {
            return AuthResultBO.buildUserNameEmptyResult();
        }
        if (StringUtils.isEmpty(userInfo.getPassword())) {
            return AuthResultBO.buildPasswordEmptyResult();
        }
        UserDTO userDTO = cloudUserService.loginUser(userInfo.getUsername(), userInfo.getPassword());
        return validateUserDto(userDTO);
    }
}
