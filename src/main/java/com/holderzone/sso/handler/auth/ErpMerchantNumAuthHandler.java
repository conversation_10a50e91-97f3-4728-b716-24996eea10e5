package com.holderzone.sso.handler.auth;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:08
 * @description
 */
public class ErpMerchantNumAuthHandler extends ErpAuthHandler {


    public ErpMerchantNumAuthHandler(RestTemplate restTemplate, String mdmUrl) {
        super(restTemplate, mdmUrl);

    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        if (StringUtils.isEmpty(userInfo.getMerchantNo())) {
            return AuthResultBO.buildMerchantNumEmptyResult();
        }
        if (StringUtils.isEmpty(userInfo.getUsername())) {
            return AuthResultBO.buildUserNameEmptyResult();
        }
        if (StringUtils.isEmpty(userInfo.getPassword())) {
            return AuthResultBO.buildPasswordEmptyResult();
        }

        Map<String, String> map = new HashMap<>(3);
        map.put("merchantNo", userInfo.getMerchantNo());
        map.put("account", userInfo.getUsername());
        map.put("password", userInfo.getPassword());
        return requestMdm(map);
    }
}
