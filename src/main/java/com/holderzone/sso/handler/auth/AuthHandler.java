package com.holderzone.sso.handler.auth;

import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import com.holderzone.resource.common.dto.user.UserDTO;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.feign.EnterpriseFeignService;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 15:58
 * @description
 */
public abstract class AuthHandler {


    /**
     * 用户认证
     *
     * @param userInfo
     * @return
     */
    public abstract AuthResultBO auth(LoginUserInfoBO userInfo);


    /**
     * 校验企业信息
     *
     * @return
     */
    protected EnterpriseDTO validateEnterprise(EnterpriseFeignService enterpriseFeignService, String merchantNo, String enterpriseGuid) {
        EnterpriseQueryDTO enterpriseQueryDTO = new EnterpriseQueryDTO();
        enterpriseQueryDTO.setUid(merchantNo);
        enterpriseQueryDTO.setEnterpriseGuid(enterpriseGuid);
        return enterpriseFeignService.findEnterprise(enterpriseQueryDTO);
    }

    /**
     * 校验用户信息
     *
     * @param userDTO
     * @return
     */
    protected AuthResultBO validateUserDto(UserDTO userDTO) {
        if (userDTO == null) {
            return AuthResultBO.buildUserInfoErrorResult();
        }
        if ("0".equals(userDTO.getIsEnabled())) {
            return AuthResultBO.buildUserUnableResult();
        }
        return AuthResultBO.buildSuccessResult(userDTO);
    }

}
