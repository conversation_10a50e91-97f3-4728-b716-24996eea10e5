package com.holderzone.sso.handler.auth;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.agent.AgentUserDTO;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.feign.AgentFeignService;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:08
 * @description
 */
public class Agent<PERSON>uth<PERSON>andler extends AuthHandler {

    private AgentFeignService agentFeignService;

    public AgentAuthHandler(AgentFeignService agentFeignService) {
        this.agentFeignService = agentFeignService;
    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        if (StringUtils.isEmpty(userInfo.getTel())) {
            return AuthResultBO.buildAuthFailedResult("手机号为空");
        }
        AgentUserDTO user = agentFeignService.getAgentUserByTel(userInfo.getTel());
        if (user == null) {
            return AuthResultBO.buildAuthFailedResult("手机号未注册");
        }
        if (user.getEnabled() != 1) {
            return AuthResultBO.buildUserUnableResult();
        }
        boolean status = agentFeignService.checkAgentStatus(user.getAgentGuid());
        if (!status) {
            return AuthResultBO.buildAuthFailedResult("账号所属合作商或上级合作商已被冻结");
        }
        return AuthResultBO.buildSuccessResult(user);
    }
}
