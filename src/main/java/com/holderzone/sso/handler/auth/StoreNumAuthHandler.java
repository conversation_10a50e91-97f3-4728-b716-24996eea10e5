package com.holderzone.sso.handler.auth;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.user.UserDTO;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.feign.EnterpriseFeignService;
import com.holderzone.sso.service.feign.UserFeignService;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:08
 * @description
 */
public class StoreNumAuthHandler extends AuthHandler {

    private UserFeignService userFeignService;

    private EnterpriseFeignService enterpriseFeignService;

    public StoreNumAuthHandler( EnterpriseFeignService enterpriseFeignService , UserFeignService userFeignService) {
        this.userFeignService = userFeignService;
        this.enterpriseFeignService = enterpriseFeignService;
    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        if (StringUtils.isEmpty(userInfo.getStoreNo())) {
            return AuthResultBO.buildStoreNumEmptyResult();
        }
        if (StringUtils.isEmpty(userInfo.getUsername())) {
            return AuthResultBO.buildUserNameEmptyResult();
        }
        if (StringUtils.isEmpty(userInfo.getPassword())) {
            return AuthResultBO.buildPasswordEmptyResult();
        }
        String merchantNo = enterpriseFeignService.findEnterPriseByStore(userInfo.getStoreNo());
        if (StringUtils.isEmpty(merchantNo)) {
            return AuthResultBO.buildAuthFailedResult("未找到相关的商户门店信息");
        }
        UserDTO userDTO = userFeignService.loginUser(merchantNo, userInfo.getUsername(), userInfo.getPassword());
        return validateUserDto(userDTO);
    }
}
