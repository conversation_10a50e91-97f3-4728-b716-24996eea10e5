package com.holderzone.sso.handler.auth;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.sso.entity.ErpHttpResultBO;
import com.holderzone.sso.entity.ErpUserBO;
import com.holderzone.sso.handler.entity.AuthResultBO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

import static com.holderzone.sso.entity.LoginConstant.MDM_SUCCESS_CODE;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:08
 * @description
 */
public abstract class ErpAuthHandler extends AuthHandler {

    private static final Logger log = LoggerFactory.getLogger(ErpAuthHandler.class);


    private RestTemplate restTemplate;

    private String mdmUrl;

    public ErpAuthHandler(RestTemplate restTemplate, String mdmUrl) {
        this.restTemplate = restTemplate;
        this.mdmUrl = mdmUrl;
    }

    protected AuthResultBO requestMdm(Map<String, String> requestBody) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("Content-Type", "application/json;charset=utf-8");
        HttpEntity<Map> requestEntity = new HttpEntity<>(requestBody, httpHeaders);
        ErpUserBO erpUserBO;
        try {
            String resultStr = restTemplate.exchange(mdmUrl, HttpMethod.POST, requestEntity, String.class).getBody();
            log.info("请求mdm结果: {}", resultStr);
            ErpHttpResultBO resultBO = JacksonUtils.toObject(ErpHttpResultBO.class, resultStr);
            String code = resultBO.getCode();
            if (!MDM_SUCCESS_CODE.equals(code)) {
                log.error("请求用户失败，mdm内部错误:{}", resultStr);
                return AuthResultBO.buildAuthFailedResult("请求用户失败，内部错误");
            }
            erpUserBO = resultBO.getData();
        } catch (Exception e) {
            log.error("请求用户失败，mdm内部错误", e);
            return AuthResultBO.buildAuthFailedResult("请求用户失败，内部错误");
        }
        String enterpriseGuid = requestBody.get("enterpriseGuid");
        if (StringUtils.isEmpty(enterpriseGuid)) {
            // web登录
            if (erpUserBO == null) {
                return AuthResultBO.buildUserInfoErrorResult();
            }
            if (erpUserBO.getEnabled() == null || erpUserBO.getEnabled() == 0) {
                return AuthResultBO.buildUserUnableResult();
            }
        } else {
            // holder登录
            if (erpUserBO == null) {
                return AuthResultBO.buildHolderUserInfoErrorResult();
            }
            if (erpUserBO.getEnabled() == null || erpUserBO.getEnabled() == 0) {
                return AuthResultBO.buildHolderUserUnableResult();
            }
        }
        return AuthResultBO.buildSuccessResult(erpUserBO);
    }

}
