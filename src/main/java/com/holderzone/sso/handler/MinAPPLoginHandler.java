package com.holderzone.sso.handler;

import com.holderzone.framework.response.Result;
import com.holderzone.sso.handler.auth.MinAPPAuthHandler;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.BusinessService;
import com.holderzone.sso.service.CacheService;
import com.holderzone.sso.service.feign.EnterpriseFeignService;
import com.holderzone.sso.service.feign.IBaseFeignService;
import com.holderzone.sso.service.feign.MinAPPFeignService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * 处理实际的小程序登录
 *
 * <AUTHOR> <chenz<PERSON><EMAIL>>
 * @since 2020-09-16
 */
public class MinAPPLoginHandler extends AbstractLoginHandler {
    private static final Integer VERIFY_CODE_LOGIN_COUNT = 3;

    private static final Integer DISABLE_USER_LOGIN_COUNT = 10;

    private final MinAPPAuthHandler minAPPAuthHandler;

    private final IBaseFeignService baseFeignService;

    private final EnterpriseFeignService enterpriseFeignService;

    @Autowired
    private MinAPPFeignService minAPPFeignService;

    public MinAPPLoginHandler(IBaseFeignService baseFeignService, MinAPPAuthHandler minAPPAuthHandler, EnterpriseFeignService enterpriseFeignService,
                              BusinessService businessService, CacheService cacheService,
                              Boolean verifyEnable) {
        super(cacheService, businessService, verifyEnable);
        this.minAPPAuthHandler = minAPPAuthHandler;
        this.baseFeignService = baseFeignService;
        this.enterpriseFeignService = enterpriseFeignService;
    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        return minAPPAuthHandler.auth(userInfo);
    }

    @Override
    public boolean isValidateVerifyCode(LoginUserInfoBO userInfoBO) {
        Integer loginCount = cacheService.selectLoginCount(userInfoBO.getUsername());
        return loginCount >= VERIFY_CODE_LOGIN_COUNT;
    }

    @Override
    public boolean verifyCodeValidate(LoginUserInfoBO userInfo, Map<String, Object> selectedUserInfoMap) {
        return baseFeignService.verifyCodeValid(userInfo.getVid(), userInfo.getvCode());
    }

    @Override
    public Result afterLogin(LoginUserInfoBO loginUserInfo, Map<String, Object> selectedUserInfoMap) {
        cacheService.cleanLoginCount(loginUserInfo.getUsername());
        return super.afterLogin(loginUserInfo, selectedUserInfoMap);
    }

    @Override
    public void handleAuthFailure(LoginUserInfoBO loginUserInfoBO, String reason) {
        Integer loginCount = cacheService.incrementLoginCount(loginUserInfoBO.getUsername());
        if (loginCount > DISABLE_USER_LOGIN_COUNT) {
            String openId = "1111";
            minAPPFeignService.disableUserLogin(openId);
        }
    }
}
