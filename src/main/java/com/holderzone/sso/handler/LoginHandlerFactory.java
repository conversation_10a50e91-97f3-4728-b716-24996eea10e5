package com.holderzone.sso.handler;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.sso.entity.LoginSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.HashMap;
import java.util.Map;

import static com.holderzone.sso.entity.LoginSource.*;
import static com.holderzone.sso.handler.config.LoginHandlerConfig.*;

/**
 * <AUTHOR>
 * @date 2019/11/23 下午 13:52
 * @description
 */
public class LoginHandlerFactory implements ApplicationContextAware {

    private static final Logger log = LoggerFactory.getLogger(LoginHandlerFactory.class);

    private Map<String, AbstractLoginHandler> loginHandlerMap = new HashMap<>(16);

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.loginHandlerMap = applicationContext.getBeansOfType(AbstractLoginHandler.class);
    }

    /**
     * 根据登录来源获取登录处理器
     */
    public AbstractLoginHandler getLoginHandler(LoginSource loginSource) {
        switch (loginSource) {
            case AIO:
            case POS:
            case PAD:
            case M:
            case V:
            case KDS:
            case SELF:
            case TV:
                return loginHandlerMap.get(MERCHANT_NOT_WEB_LOGIN_HANDLER);
            case WECHAT:
                throw new BusinessException("不支持微信公众号登录");
            case MERCHANT:
                return loginHandlerMap.get(MERCHANT_WEB_LOGIN_HANDLER);
            case MINAPP:
                return loginHandlerMap.get(MIN_APP_LOGIN_HANDLER);
            case PHONE:
            case BI:
                return loginHandlerMap.get(BOSS_APP_LOGIN_HANDLER);
            case CLOUD:
                return loginHandlerMap.get(CLOUD_LOGIN_HANDLER);
            case AGENT:
                return loginHandlerMap.get(AGENT_LOGIN_HANDLER);
            case ERP_WEB:
                return loginHandlerMap.get(ERP_WEB_LOGIN_HANDLER);
            case ERP_NOT_WEB:
                return loginHandlerMap.get(ERP_NOT_WEB_LOGIN_HANDLER);
            default:
                throw new BusinessException("不支持的登录来源, source: " + loginSource);
        }
    }
}
