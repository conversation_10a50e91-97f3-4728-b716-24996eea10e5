package com.holderzone.sso.handler.config;

import com.holderzone.sso.handler.auth.*;
import com.holderzone.sso.service.feign.AgentFeignService;
import com.holderzone.sso.service.feign.EnterpriseFeignService;
import com.holderzone.sso.service.feign.ICloudUserService;
import com.holderzone.sso.service.feign.UserFeignService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2019/11/23 上午 11:57
 * @description
 */
@Configuration
@EnableConfigurationProperties(MdmProperties.class)
public class AuthHandlerConfig {


    @Bean
    public AgentAuthHandler agentAuthHandler(AgentFeignService agentFeignService) {
        return new AgentAuthHandler(agentFeignService);
    }

    @Bean
    public CloudAuthHandler cloudAuthHandler(ICloudUserService cloudUserService) {
        return new CloudAuthHandler(cloudUserService);
    }

    @Bean
    public MerchantNumAuthHandler merchantNumAuthHandler(EnterpriseFeignService enterpriseFeignService,
                                                         UserFeignService userFeignService) {
        return new MerchantNumAuthHandler(enterpriseFeignService, userFeignService);
    }

    @Bean
    public PhoneNumAuthHandler phoneNumAuthHandler(EnterpriseFeignService enterpriseFeignService,
                                                   UserFeignService userFeignService) {
        return new PhoneNumAuthHandler(enterpriseFeignService, userFeignService);
    }

    @Bean
    public StoreNumAuthHandler storeNumAuthHandler(EnterpriseFeignService enterpriseFeignService,
                                                   UserFeignService userFeignService) {
        return new StoreNumAuthHandler(enterpriseFeignService, userFeignService);
    }

    @Bean
    public ErpMerchantNumAuthHandler erpMerchantNumAuthHandler(@Qualifier("ssoRestTemplate") RestTemplate restTemplate, MdmProperties mdmProperties) {
        return new ErpMerchantNumAuthHandler(restTemplate, mdmProperties.getLoginUrl());
    }

    @Bean
    public ErpTelAuthHandler erpTelAuthHandler(@Qualifier("ssoRestTemplate") RestTemplate restTemplate, MdmProperties mdmProperties) {
        return new ErpTelAuthHandler(restTemplate, mdmProperties.getLoginUrl());
    }
}
