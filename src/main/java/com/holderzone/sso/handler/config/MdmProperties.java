package com.holderzone.sso.handler.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @date 2019/12/23 下午 15:57
 * @description
 */
@ConfigurationProperties(prefix = "mdm")
public class MdmProperties {

    private String signature;

    private String developerId;

    private String loginUrl;

    private String findUserByUserGuidOrTelUrl;

    private String findUserByAccountUrl;

    private String updateUserUrl;

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getDeveloperId() {
        return developerId;
    }

    public void setDeveloperId(String developerId) {
        this.developerId = developerId;
    }

    public String getLoginUrl() {
        return loginUrl;
    }

    public void setLoginUrl(String loginUrl) {
        this.loginUrl = loginUrl;
    }

    public String getFindUserByUserGuidOrTelUrl() {
        return findUserByUserGuidOrTelUrl;
    }

    public void setFindUserByUserGuidOrTelUrl(String findUserByUserGuidOrTelUrl) {
        this.findUserByUserGuidOrTelUrl = findUserByUserGuidOrTelUrl;
    }

    public String getFindUserByAccountUrl() {
        return findUserByAccountUrl;
    }

    public void setFindUserByAccountUrl(String findUserByAccountUrl) {
        this.findUserByAccountUrl = findUserByAccountUrl;
    }

    public String getUpdateUserUrl() {
        return updateUserUrl;
    }

    public void setUpdateUserUrl(String updateUserUrl) {
        this.updateUserUrl = updateUserUrl;
    }
}
