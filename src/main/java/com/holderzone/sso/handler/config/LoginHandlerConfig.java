package com.holderzone.sso.handler.config;

import com.holderzone.sso.config.RestTemplateConfig;
import com.holderzone.sso.handler.*;
import com.holderzone.sso.handler.auth.*;
import com.holderzone.sso.service.BusinessService;
import com.holderzone.sso.service.CacheService;
import com.holderzone.sso.service.feign.*;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:19
 * @description
 */
@Configuration
@AutoConfigureAfter({AuthHandlerConfig.class, RestTemplateConfig.class})
@EnableConfigurationProperties(MdmProperties.class)
public class LoginHandlerConfig {

    public static final String AGENT_LOGIN_HANDLER = "agentLoginHandler";

    public static final String CLOUD_LOGIN_HANDLER = "cloudLoginHandler";

    public static final String MERCHANT_WEB_LOGIN_HANDLER = "merchantWebLoginHandler";

    public static final String MERCHANT_NOT_WEB_LOGIN_HANDLER = "merchantNotWebLoginHandler";

    public static final String BOSS_APP_LOGIN_HANDLER = "bossAppLoginHandler";

    public static final String ERP_WEB_LOGIN_HANDLER = "erpWebLoginHandler";

    public static final String ERP_NOT_WEB_LOGIN_HANDLER = "erpNotWebLoginHandler";

    public static final String MIN_APP_LOGIN_HANDLER = "minAPPLoginHandler";

    @Value("${verify.enable:true}")
    private Boolean verifyEnable;

    @Bean(name = AGENT_LOGIN_HANDLER)
    public AgentLoginHandler agentLoginHandler(CacheService cacheService, BusinessService businessService,
                                               AgentFeignService agentFeignService) {
        AgentAuthHandler agentAuthHandler = new AgentAuthHandler(agentFeignService);
        return new AgentLoginHandler(cacheService, businessService, agentAuthHandler, agentFeignService, verifyEnable);
    }

    @Bean(name = CLOUD_LOGIN_HANDLER)
    public CloudLoginHandler cloudLoginHandler(IBaseFeignService baseFeignService, ICloudUserService cloudUserService,
                                               BusinessService businessService, CacheService cacheService) {
        CloudAuthHandler cloudAuthHandler = new CloudAuthHandler(cloudUserService);
        return new CloudLoginHandler(cloudAuthHandler, baseFeignService, cloudUserService,
                businessService, cacheService, verifyEnable);
    }


    @Bean(name = MERCHANT_WEB_LOGIN_HANDLER)
    public MerchantWebLoginHandler merchantWebLoginHandler(CacheService cacheService, BusinessService businessService,
                                                           IBaseFeignService baseFeignService, MerchantNumAuthHandler merchantNumAuthHandler,
                                                           PhoneNumAuthHandler phoneNumAuthHandler, UserFeignService userFeignService) {
        return new MerchantWebLoginHandler(cacheService, businessService, merchantNumAuthHandler,
                phoneNumAuthHandler, baseFeignService, verifyEnable, userFeignService);
    }

    @Bean(name = MERCHANT_NOT_WEB_LOGIN_HANDLER)
    public MerchantNotWebLoginHandler merchantNotWebLoginHandler(CacheService cacheService, BusinessService businessService,
                                                                 StoreNumAuthHandler storeNumAuthHandler,
                                                                 PhoneNumAuthHandler phoneNumAuthHandler, UserFeignService userFeignService) {
        return new MerchantNotWebLoginHandler(cacheService, businessService,
                storeNumAuthHandler, phoneNumAuthHandler, verifyEnable, userFeignService);
    }

    @Bean(name = BOSS_APP_LOGIN_HANDLER)
    public BossAppLoginHandler bossAppLoginHandler(CacheService cacheService, BusinessService businessService,
                                                   MerchantNumAuthHandler merchantNumAuthHandler, PhoneNumAuthHandler phoneNumAuthHandler) {

        return new BossAppLoginHandler(cacheService, businessService,
                merchantNumAuthHandler, phoneNumAuthHandler, verifyEnable);
    }

    @Bean(name = ERP_WEB_LOGIN_HANDLER)
    public ErpWebLoginHandler erpWebLoginHandler(CacheService cacheService, BusinessService businessService, IBaseFeignService baseFeignService,
                                                 ErpMerchantNumAuthHandler erpMerchantNumAuthHandler, ErpTelAuthHandler erpTelAuthHandler,
                                                 @Qualifier("ssoRestTemplate") RestTemplate restTemplate, MdmProperties mdmProperties) {
        return new ErpWebLoginHandler(baseFeignService, businessService, cacheService, erpMerchantNumAuthHandler, erpTelAuthHandler,
                restTemplate, mdmProperties);
    }

    @Bean(name = ERP_NOT_WEB_LOGIN_HANDLER)
    public ErpNotWebLoginHandler erpNotWebLoginHandler(CacheService cacheService, BusinessService businessService, IBaseFeignService baseFeignService,
                                                       ErpMerchantNumAuthHandler erpMerchantNumAuthHandler, ErpTelAuthHandler erpTelAuthHandler,
                                                       @Qualifier("ssoRestTemplate") RestTemplate restTemplate, MdmProperties mdmProperties) {
        return new ErpNotWebLoginHandler(baseFeignService, businessService, cacheService,
                erpMerchantNumAuthHandler, erpTelAuthHandler, restTemplate, mdmProperties);
    }

    @Bean(name = MIN_APP_LOGIN_HANDLER)
    public MinAPPLoginHandler minAPPLoginHandler(IBaseFeignService baseFeignService, MinAPPFeignService minAPPFeignService,EnterpriseFeignService enterpriseFeignService,
                                                 BusinessService businessService, CacheService cacheService) {
        MinAPPAuthHandler minAPPAuthHandler = new MinAPPAuthHandler(minAPPFeignService,enterpriseFeignService);
        return new MinAPPLoginHandler(baseFeignService, minAPPAuthHandler,enterpriseFeignService, businessService, cacheService, verifyEnable);
    }
}
