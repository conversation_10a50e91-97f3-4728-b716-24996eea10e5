package com.holderzone.sso.handler.config;

import com.holderzone.sso.handler.LoginHandlerFactory;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2019/11/23 下午 14:33
 * @description
 */
@Configuration
@AutoConfigureAfter(LoginHandlerConfig.class)
public class LoginHandlerFactoryConfig {

    @Bean
    public LoginHandlerFactory loginHandlerFactory() {
        return new LoginHandlerFactory();
    }
}
