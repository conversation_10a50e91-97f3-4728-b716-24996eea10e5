package com.holderzone.sso.handler;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.agent.AgentUserDTO;
import com.holderzone.sso.entity.LoginType;
import com.holderzone.sso.entity.TokenRequestBO;
import com.holderzone.sso.handler.auth.AgentAuthHandler;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.BusinessService;
import com.holderzone.sso.service.CacheService;
import com.holderzone.sso.service.feign.AgentFeignService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:13
 * @description
 */
public class AgentLoginHandler extends AbstractLoginHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(AgentLoginHandler.class);
    private AgentAuthHandler agentAuthHandler;

    private AgentFeignService agentFeignService;

    public AgentLoginHandler(CacheService cacheService, BusinessService businessService,
                             AgentAuthHandler agentAuthHandler, AgentFeignService agentFeignService, Boolean verifyEnable) {
        super(cacheService, businessService, verifyEnable);
        this.agentAuthHandler = agentAuthHandler;
        this.agentFeignService = agentFeignService;

    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        return agentAuthHandler.auth(userInfo);
    }


    @Override
    public boolean verifyCodeValidate(LoginUserInfoBO userInfo, Map<String, Object> selectedUserInfoMap) {
        boolean valid = true;
        String openId = (String) selectedUserInfoMap.get("openId");
        if (StringUtils.isEmpty(openId) || !openId.equals(userInfo.getOpenId())) {
            valid = cacheService.validateSmsCode(userInfo.getVid() + userInfo.getTel(), userInfo.getvCode());
            AgentUserDTO userDTO = JacksonUtils.toObject(AgentUserDTO.class, JacksonUtils.writeValueAsString(selectedUserInfoMap));
            userDTO.setOpenId(userInfo.getOpenId());
            agentFeignService.bindOpenIdForUser(userDTO);
        }
        return valid;
    }

    @Override
    public Result afterLogin(LoginUserInfoBO loginUserInfo, Map<String, Object> selectedUserInfoMap) {
        String guid = (String) selectedUserInfoMap.get("guid");
        cacheService.cleanTokenByUser(guid);
        cacheService.cleanVCode(loginUserInfo.getVid() + loginUserInfo.getTel());

        String token = cacheService.saveTokenForClient(new TokenRequestBO("", "", null, guid),
                LoginType.NOT_WEB, loginUserInfo.getLoginSource());
        Map<String, String> resultMap = new HashMap<>(2);
        resultMap.put("token", token);
        resultMap.put("agentGuid", (String) selectedUserInfoMap.get("agentGuid"));
        return Result.buildSuccessResult(resultMap);
    }
}
