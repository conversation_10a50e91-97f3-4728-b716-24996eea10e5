package com.holderzone.sso.handler.entity;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.sso.enums.ReasonEnum;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 15:53
 * @description
 */
public class AuthResultBO {

    /**
     * 认证是否成功
     */
    private Boolean success;

    /**
     * 认证失败原因
     */
    private String reason;

    /**
     * 用户信息Map
     */
    private Map<String, Object> userInfoMap;

    public AuthResultBO(Boolean success, String reason) {
        this.success = success;
        this.reason = reason;
    }

    public AuthResultBO(Boolean success, Map<String, Object> userInfoMap) {
        this.success = success;
        this.userInfoMap = userInfoMap;
    }

    public static AuthResultBO buildUserInfoErrorResult() {
        return new AuthResultBO(false, ReasonEnum.getLocale(ReasonEnum.ACCOUNT_OR_PASSWORD_ERROR));
    }

    public static AuthResultBO buildHolderUserInfoErrorResult() {
        return new AuthResultBO(false, ReasonEnum.getLocale(ReasonEnum.ACCOUNT_NOT_SYNCHRONIZED_APPLICATION));
    }

    public static AuthResultBO buildMinAppInfoErrorResult() {
        return new AuthResultBO(false, ReasonEnum.getLocale(ReasonEnum.WECHAT_LOGIN_ERROR));
    }

    public static AuthResultBO buildUserUnableResult() {
        return new AuthResultBO(false, ReasonEnum.getLocale(ReasonEnum.ACCOUNT_DISABLED));
    }

    public static AuthResultBO buildHolderUserUnableResult() {
        return new AuthResultBO(false, ReasonEnum.getLocale(ReasonEnum.ACCOUNT_DISABLED_APPLICATION));
    }

    public static AuthResultBO buildAuthFailedResult(String reason) {
        return new AuthResultBO(false, reason);
    }

    public static AuthResultBO buildUserNameEmptyResult() {
        return new AuthResultBO(false, ReasonEnum.getLocale(ReasonEnum.ACCOUNT_NULL));
    }

    public static AuthResultBO buildPasswordEmptyResult() {
        return new AuthResultBO(false, ReasonEnum.getLocale(ReasonEnum.PASSWORD_NULL));
    }

    public static AuthResultBO buildMerchantNumEmptyResult() {
        return new AuthResultBO(false, ReasonEnum.getLocale(ReasonEnum.MERCHANT_NULL));
    }

    public static AuthResultBO buildPhoneNumEmptyResult() {
        return new AuthResultBO(false, ReasonEnum.getLocale(ReasonEnum.PHONE_NULL));
    }

    public static AuthResultBO buildStoreNumEmptyResult() {
        return new AuthResultBO(false, ReasonEnum.getLocale(ReasonEnum.STORE_NULL));
    }

    public static AuthResultBO buildSuccessResult(Object obj) {
        Map<String, Object> userInfoMap = JacksonUtils.toMap(JacksonUtils.writeValueAsString(obj));
        return new AuthResultBO(true, userInfoMap);
    }

    public Boolean isSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Map<String, Object> getUserInfoMap() {
        return userInfoMap;
    }

    public void setUserInfoMap(Map<String, Object> userInfoMap) {
        this.userInfoMap = userInfoMap;
    }

}
