package com.holderzone.sso.handler.entity.transform;

import com.holderzone.sso.entity.dto.LoginDTO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2018/09/08 上午 11:13
 * @description
 */
@Mapper
public interface LoginTransform {

    LoginTransform INSTANCE = Mappers.getMapper(LoginTransform.class);

    /**
     * LoginUserInfoBO 转 LoginDTO
     *
     * @param loginUserInfo
     * @return
     */
    LoginDTO userInfoBoToDto(LoginUserInfoBO loginUserInfo);

    /**
     * LoginDTO 转 LoginUserInfoBO
     *
     * @param loginDTO
     * @return
     */
    LoginUserInfoBO loginDtoToLoginUserInfoBo(LoginDTO loginDTO);
}
