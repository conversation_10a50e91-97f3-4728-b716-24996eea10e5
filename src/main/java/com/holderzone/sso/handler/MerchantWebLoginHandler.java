package com.holderzone.sso.handler;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.sso.handler.auth.MerchantNumAuthHandler;
import com.holderzone.sso.handler.auth.PhoneNumAuthHandler;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.service.BusinessService;
import com.holderzone.sso.service.CacheService;
import com.holderzone.sso.service.feign.IBaseFeignService;
import com.holderzone.sso.service.feign.UserFeignService;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/22 下午 16:13
 * @description
 */
public class MerchantWebLoginHandler extends AbstractMerchantLoginHandler {

    private MerchantNumAuthHandler merchantNumAuthHandler;

    private PhoneNumAuthHandler phoneNumAuthHandler;

    private IBaseFeignService baseFeignService;

    public MerchantWebLoginHandler(CacheService cacheService, BusinessService businessService,
                                   MerchantNumAuthHandler merchantNumAuthHandler, PhoneNumAuthHandler phoneNumAuthHandler,
                                   IBaseFeignService baseFeignService, Boolean verifyEnable,
                                   UserFeignService userFeignService) {
        super(cacheService, businessService, verifyEnable, userFeignService);
        this.merchantNumAuthHandler = merchantNumAuthHandler;
        this.phoneNumAuthHandler = phoneNumAuthHandler;
        this.baseFeignService = baseFeignService;
    }

    @Override
    public AuthResultBO auth(LoginUserInfoBO userInfo) {
        if (!StringUtils.isEmpty(userInfo.getTel())) {
            return phoneNumAuthHandler.auth(userInfo);
        }
        return merchantNumAuthHandler.auth(userInfo);
    }

    @Override
    public boolean isValidateVerifyCode(LoginUserInfoBO userInfoBO) {
        if (!StringUtils.isEmpty(userInfoBO.getTel())) {
            return false;
        }
        return super.isValidateVerifyCode(userInfoBO);
    }

    @Override
    public boolean verifyCodeValidate(LoginUserInfoBO userInfo, Map<String, Object> selectedUserInfoMap) {
        return baseFeignService.verifyCodeValid(userInfo.getVid(), userInfo.getvCode());
    }

}
