package com.holderzone.sso.util;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.sso.entity.TokenRequestBO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.Base64;

import static com.holderzone.sso.util.CustomJwtEncoder.UTF_8;

/**
 * <AUTHOR>
 * @date 2017.10.25
 */
@Component
public class JwtParser {

    public static final String ENTERPRISE_GUID = "enterpriseGuid";

    public static final String ENTERPRISE_NAME = "enterpriseName";

    public static final String MERCHANT_NO = "merchantNo";

    public static final String STORE_NO = "storeNo";

    public static final String DEVICE_GUID = "deviceGuid";

    public static final String USER_GUID = "userGuid";

    public static final String USER_NAME = "userName";

    public static final String ACCOUNT = "account";

    public static final String TEL = "tel";

    @Value("${token.ttl}")
    public int JWT_REFRESH_TTL;

    /**
     * 解析签名(jwt的第三部分)
     *
     * @param token
     * @return
     */
    public String acquireSign(String token) {
        return token.split("\\.")[2];
    }

    public TokenRequestBO acquirePayload(String token) {
        try {
            byte[] decode = Base64.getDecoder().decode((token.split("\\.")[1]).getBytes(UTF_8));
            return JacksonUtils.toObject(TokenRequestBO.class, decode);
        } catch (UnsupportedEncodingException e) {
            throw new BusinessException("编码错误", e);
        }
    }

    public String acquireUserFromToken(String token) {
        TokenRequestBO tokenRequestBO = acquirePayload(token);
        return tokenRequestBO.getUserGuid();
    }

    public String acquireEnterpriseFromToken(String token) {
        TokenRequestBO tokenRequestBO = acquirePayload(token);
        return tokenRequestBO.getEnterpriseGuid();
    }

    public String acquireStoreFromToken(String token) {
        TokenRequestBO tokenRequestBO = acquirePayload(token);
        return tokenRequestBO.getStoreNo();
    }

    public String acquireDeviceFromToken(String token) {
        TokenRequestBO tokenRequestBO = acquirePayload(token);
        return tokenRequestBO.getDeviceGuid();
    }

}
