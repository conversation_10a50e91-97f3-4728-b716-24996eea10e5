package com.holderzone.sso.controller;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.efk.utils.CurrentRequestContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.resource.common.dto.user.UserDTO;
import com.holderzone.sso.controller.validator.SsoValidator;
import com.holderzone.sso.entity.LoginSource;
import com.holderzone.sso.entity.LoginType;
import com.holderzone.sso.entity.dto.LoginDTO;
import com.holderzone.sso.entity.dto.TokenValidateDTO;
import com.holderzone.sso.enums.ReasonEnum;
import com.holderzone.sso.handler.AbstractLoginHandler;
import com.holderzone.sso.handler.LoginHandlerFactory;
import com.holderzone.sso.handler.entity.AuthResultBO;
import com.holderzone.sso.handler.entity.LoginUserInfoBO;
import com.holderzone.sso.handler.entity.transform.LoginTransform;
import com.holderzone.sso.service.BusinessService;
import com.holderzone.sso.service.CacheService;
import com.holderzone.sso.service.feign.IBaseFeignService;
import com.holderzone.sso.util.GeneratorCode;
import com.holderzone.sso.util.JwtParser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.holderzone.sso.entity.LoginSource.AGENT;


/**
 * SSOServer控制器 分别对应登录、登出两个请求接口
 *
 * <AUTHOR>
 * @date 2018/8/22
 */
@RequestMapping("/")
@RestController
@Api(value = "SSO单点相关登录服务", description = "SSO单点相关登录服务")
public class SsoServerController {
    private static final Logger LOGGER = LoggerFactory.getLogger(SsoServerController.class);
    private final LoginTransform loginTransform = LoginTransform.INSTANCE;
    @Autowired
    BusinessService businessService;
    @Autowired
    private JwtParser jwtParser;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private IBaseFeignService iBaseFeignService;
    @Autowired
    private SsoValidator ssoValidator;
    @Autowired
    private LoginHandlerFactory loginHandlerFactory;

    /**
     * 校验token
     *
     * @param tokenValidateDTO
     * @return
     * @throws Exception
     */
    @PostMapping("ssoserver")
    @ApiOperation("token验证接口")
    public Result validate(@RequestBody TokenValidateDTO tokenValidateDTO) throws Exception {
        ssoValidator.tokenValidate(tokenValidateDTO);
        String token = tokenValidateDTO.getToken();
        String source = tokenValidateDTO.getSource();
        Integer loginType = tokenValidateDTO.getLoginType();
        Map<String, String> map = new HashMap<>(8);
        if ("null".equals(token)) {
            map.put("loginSource", "holder");
            map.put("enterpriseGuid", "holder");
            return Result.buildSuccessResult(map);
        }

        if (cacheService.isServerExistToken(token, source, loginType)) {
            LoginSource loginSource = LoginSource.getSourceByCode(source);
            try {
                cacheService.updateTokenExpire(token, loginSource);
                map.put("enterpriseGuid", jwtParser.acquireEnterpriseFromToken(token));
                map.put("storeNo", jwtParser.acquireStoreFromToken(token));
                map.put("deviceGuid", jwtParser.acquireDeviceFromToken(token));
                map.put("userGuid", jwtParser.acquireUserFromToken(token));
                return Result.buildSuccessResult(map);
            } catch (Exception e) {
                cacheService.cleanToken(token, source);
                LOGGER.error("token验证异常: {}", JacksonUtils.writeValueAsString(tokenValidateDTO), e);
                return Result.buildOpFailedResult("token验证内部异常");
            }
        }
        return Result.buildOpFailedResult("token验证失败");
    }


    @PostMapping("/validateTokenWithNoResponse")
    @ApiOperation("token校验接口，没有token解析的信息")
    public Result validateTokenWithNoResponse(@RequestBody TokenValidateDTO tokenValidateDTO) throws Exception {
        ssoValidator.tokenValidate(tokenValidateDTO);
        String token = tokenValidateDTO.getToken();
        String source = tokenValidateDTO.getSource();
        Integer loginType = tokenValidateDTO.getLoginType();
        if (cacheService.isServerExistToken(token, source, loginType)) {
            LoginSource loginSource = LoginSource.getSourceByCode(source);
            try {
                cacheService.updateTokenExpire(token, loginSource);
                return Result.buildSuccessResult(true);
            } catch (Exception e) {
                cacheService.cleanToken(token, source);
                LOGGER.error("token验证异常: {}", JacksonUtils.writeValueAsString(tokenValidateDTO), e);
                return Result.buildOpFailedResult("token验证内部异常");
            }
        }
        return Result.buildOpFailedResult("token验证失败");
    }


    @PostMapping("login")
    @ApiOperation("登录接口")
    public Result login(@RequestBody LoginDTO loginDTO) {
        LOGGER.info("登录：loginDTO:{}", JacksonUtils.writeValueAsString(loginDTO));
        LoginDTO loginDTOForLogging = new LoginDTO();
        BeanUtils.copyProperties(loginDTO, loginDTOForLogging);
        String tel = loginDTOForLogging.getTel();
        String password = loginDTOForLogging.getPassword();
        String openId = loginDTOForLogging.getOpenId();
        if (StringUtils.hasText(password)) {
            loginDTOForLogging.setPassword("******");
        }
        if (StringUtils.hasText(tel)) {
            if (tel.length() == 11) {
                loginDTOForLogging.setTel(tel.substring(0, 3) + "****" + tel.substring(7, 11));
            } else {
                loginDTOForLogging.setTel("********");
            }
        }
        if (StringUtils.hasText(openId) && openId.length() > 6) {
            openId = openId.substring(0, 6) + "*******";
            loginDTOForLogging.setOpenId(openId);
        }
        LOGGER.info("登录接口入参： {}", JacksonUtils.writeValueAsString(loginDTOForLogging));

        LoginSource loginSource = getSource();
        LoginType loginType = getLoginType();
        validateLoginDto(loginDTO, loginSource);

        AbstractLoginHandler loginHandler = loginHandlerFactory.getLoginHandler(loginSource);
        LoginUserInfoBO loginUserInfoBO = loginTransform.loginDtoToLoginUserInfoBo(loginDTO);
        loginUserInfoBO.setLoginSource(loginSource);
        loginUserInfoBO.setLoginType(loginType);
        AuthResultBO authResultBO = loginHandler.auth(loginUserInfoBO);
        if (!authResultBO.isSuccess()) {
            loginHandler.handleAuthFailure(loginUserInfoBO, authResultBO.getReason());
            throw new BusinessException(ReasonEnum.getLocaleByMessage(authResultBO.getReason()));
        }
        boolean validateVerifyCode = loginHandler.isValidateVerifyCode(loginUserInfoBO);
        if (validateVerifyCode) {
            if (StringUtils.isEmpty(loginUserInfoBO.getVid()) || StringUtils.isEmpty(loginUserInfoBO.getvCode())) {
                throw new ParameterException("验证码为空");
            }
            boolean valid = loginHandler.verifyCodeValidate(loginUserInfoBO, authResultBO.getUserInfoMap());
            if (!valid) {
                throw new BusinessException("验证码错误");
            }
        }
        return loginHandler.afterLogin(loginUserInfoBO, authResultBO.getUserInfoMap());
    }


    @ApiOperation("查询登录次数")
    @PostMapping("/selectLoginCount")
    @EFKOperationLogAop(action = OperatorType.SELECT, actionName = "查询登录次数", description = "查询登录次数", moduleName = ModuleNameType.HOLDER_SAAS_SSO, PLATFORM = Platform.CLOUD)
    public Result selectLoginCount(String account) {
        return Result.buildSuccessResult(cacheService.selectLoginCount(account));
    }

    @PostMapping(value = "logout")
    @ApiOperation(value = "登出api", notes = "提供单点登出服务")
    @EFKOperationLogAop(action = OperatorType.LOGOUT, actionName = "登出", description = "登出系统", moduleName = ModuleNameType.HOLDER_SAAS_SSO, PLATFORM = Platform.CLOUD)
    public Result logout() throws Exception {
        String source = CurrentRequestContextUtils.getRequest().getHeader("source");
        String token = CurrentRequestContextUtils.getRequest().getHeader("token");
        LOGGER.info("登出接口入参:token={},source={}", token, source);
        cacheService.cleanToken(token, source);
        LOGGER.info("no jwt info found, return to login page");
        return Result.buildSuccessResult("success");
    }

    @ApiOperation(value = "踢出在线用户api")
    @DeleteMapping("/kickout/{userGuid}")
    @EFKOperationLogAop(action = OperatorType.LOGOUT, actionName = "踢出", description = "踢出系统", moduleName = ModuleNameType.HOLDER_SAAS_SSO, PLATFORM = Platform.CLOUD)
    public void delToken(@PathVariable("userGuid") String userGuid) {
        LOGGER.info("踢出在线用户入参:userGuid={}", userGuid);
        cacheService.cleanTokenByUser(userGuid);
    }

    /**
     * 生成验证码
     *
     * <AUTHOR>
     */
    @GetMapping("verifycode")
    @ApiOperation(value = "验证码生成")
    public void verifyCodeImg(@RequestParam(name = "vc_id") String id, HttpServletResponse response) throws IOException {
        LOGGER.info("请求验证码");
        byte[] bytes = iBaseFeignService.verifycodeImg(id);
        ByteArrayInputStream inputStream = null;
        if (bytes != null) {
            try {
                inputStream = new ByteArrayInputStream(bytes);
                BufferedImage bufferedImage = ImageIO.read(inputStream);
                ImageIO.write(bufferedImage, "jpg", response.getOutputStream());
            } finally {
                if (inputStream != null) {
                    inputStream.close();
                }
            }
        }
    }

    /**
     * 获取登录来源
     *
     * @return
     */
    private LoginSource getSource() {
        String source = CurrentRequestContextUtils.getRequest().getHeader("source");
        if (StringUtils.isEmpty(source)) {
            throw new BusinessException("没有登录来源");
        }
        LoginSource loginSource = LoginSource.getSourceByCode(source);
        if (loginSource == null) {
            throw new BusinessException("登录来源不允许");
        }
        return loginSource;
    }


    /**
     * 获取登录类型
     *
     * @return
     */
    private LoginType getLoginType() {
        String typeStr = CurrentRequestContextUtils.getRequest().getHeader("loginType");
        if (StringUtils.isEmpty(typeStr)) {
            return LoginType.NOT_WEB;
        }
        try {
            Integer type = Integer.parseInt(typeStr);
            LoginType loginType = LoginType.getTypeEnum(type);
            if (loginType == null) {
                throw new BusinessException("登录类型不允许");
            }
            return loginType;
        } catch (Exception e) {
            throw new BusinessException("登录类型不允许");
        }

    }


    /**
     * 校验LoginDTO参数
     *
     * @param loginDTO
     * @param loginSource
     */
    private void validateLoginDto(LoginDTO loginDTO, LoginSource loginSource) {
        if (AGENT.equals(loginSource) && StringUtils.isEmpty(loginDTO.getExtra())) {
            if (StringUtils.isEmpty(loginDTO.getOpenId())) {
                throw new ParameterException("openId不能为空");
            }

        }
    }


    @PostMapping("forget/sms")
    @ApiOperation(value = "发送忘记密码短信")
    @EFKOperationLogAop(action = OperatorType.UPDATE, actionName = "发送忘记密码短信", description = "发送忘记密码短信", moduleName = ModuleNameType.HOLDER_SAAS_SSO, PLATFORM = Platform.CLOUD)
    public Result forgetPassword(@RequestBody UserDTO userDTO) {
        LOGGER.info("发送忘记密码短信 -> {}", JacksonUtils.writeValueAsString(userDTO));
        LoginSource source = getSource();
        AbstractLoginHandler loginHandler = loginHandlerFactory.getLoginHandler(source);
        Map<String, Object> userInfoMap;
        try {
            userInfoMap = loginHandler.findUserInfo(userDTO);
        }catch (Exception e){
            throw new BusinessException(ReasonEnum.getLocaleByMessage(e.getMessage()));
        }
        if (userInfoMap == null) {
            throw new BusinessException("没有找到用户信息");
        }
        String tel = loginHandler.getTel(userInfoMap);
        String userGuid = loginHandler.getUserGuid(userInfoMap);
        if (StringUtils.isEmpty(tel) || StringUtils.isEmpty(userGuid)) {
            LOGGER.error("查询的用户手机号或者userGuid为空");
            throw new BusinessException("没有找到用户信息");
        }
        String authCode = String.valueOf(GeneratorCode.generatorCode(6));
        cacheService.saveUserAuthCode(userDTO.getVcid(), authCode,userGuid);
        //发送短信
        businessService.sendShortMsg(tel, authCode);
        return Result.buildSuccessResult(userGuid);
    }


    @PostMapping("validate/sms")
    @ApiOperation(value = "短信验证码验证")
    @EFKOperationLogAop(action = OperatorType.UPDATE, actionName = "短信验证码验证", description = "短信验证码验证", moduleName = ModuleNameType.HOLDER_SAAS_SSO, PLATFORM = Platform.CLOUD)
    public Result validateSmsCode(String vcid, String vcCode) {
        LOGGER.info("短信验证码验证入参:vcid={},vcCode={}", vcid, vcCode);
        Boolean isValidate = cacheService.validateSmsCode(vcid, vcCode);
        LOGGER.info("短信验证码验证结果:{}", isValidate);
        return isValidate ? Result.buildSuccessResult(ReasonEnum.getLocale(ReasonEnum.SUCCESSFUL))
                : Result.buildOpFailedResult(ReasonEnum.getLocale(ReasonEnum.INCORRECT_VERIFICATION_CODE));
    }

    @PostMapping("forget/reset")
    @ApiOperation(value = "重置密码")
    @EFKOperationLogAop(action = OperatorType.UPDATE, actionName = "重置密码", description = "重置密码", moduleName = ModuleNameType.HOLDER_SAAS_SSO, PLATFORM = Platform.CLOUD)
    public Result resetPassword(@RequestBody UserDTO userDTO) {
        if (StringUtils.isEmpty(userDTO.getUserGuid()) || StringUtils.isEmpty(userDTO.getPassword())|| StringUtils.isEmpty(userDTO.getVcid())) {
            throw new BusinessException(ReasonEnum.getLocale(ReasonEnum.FAILED_RESET_PASSWORD));
        }
        //判断用户是否正确
        Boolean isValidate = cacheService.validateSmsUser(userDTO.getVcid(), userDTO.getUserGuid());
        if(!isValidate){
            throw new BusinessException(ReasonEnum.getLocale(ReasonEnum.INCORRECT_USER));
        }
        LoginSource source = getSource();
        AbstractLoginHandler loginHandler = loginHandlerFactory.getLoginHandler(source);
        loginHandler.resetPassword(userDTO.getUserGuid(), userDTO.getPassword());
        return Result.buildSuccessMsg("成功");
    }


    @ApiOperation(value = "发送短信验证码")
    @PostMapping("/vcode")
    @EFKOperationLogAop(action = OperatorType.UPDATE, actionName = "发送短信验证码", description = "发送短信验证码", moduleName = ModuleNameType.HOLDER_SAAS_SSO, PLATFORM = Platform.CLOUD)
    public Result sendVCode(String tel) {
        LOGGER.info("发送短信验证码入参：tel:{}", tel);
        return Result.buildSuccessResult(cacheService.sendVCode(tel));
    }


}
