package com.holderzone.sso.controller.validator;

import com.holderzone.sso.entity.dto.TokenValidateDTO;
import com.holderzone.sso.util.Validator;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/11/27 下午 14:19
 * @description
 */
@Component
public class SsoValidator {




    public void tokenValidate(TokenValidateDTO tokenValidateDTO) {
        Validator validator = Validator.create()
                .notBlank(tokenValidateDTO.getSource(), "来源")
                .notBlank(tokenValidateDTO.getToken(), "token");
        validator.validate();
    }
}
