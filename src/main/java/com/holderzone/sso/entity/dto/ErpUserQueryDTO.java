package com.holderzone.sso.entity.dto;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * <AUTHOR>
 * @date 2019/12/26 下午 16:05
 * @description
 */
@JsonPropertyOrder(alphabetic = true)
public class ErpUserQueryDTO {

    private String guid;

    private String tel;

    private String merchantNo;

    private String account;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }
}
