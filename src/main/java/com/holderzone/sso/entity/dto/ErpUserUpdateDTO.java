package com.holderzone.sso.entity.dto;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * <AUTHOR>
 * @date 2019/12/26 下午 16:08
 * @description
 */
@JsonPropertyOrder(alphabetic = true)
public class ErpUserUpdateDTO {

    private String guid;

    private String password;

    private String thirdNo;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getThirdNo() {
        return thirdNo;
    }

    public void setThirdNo(String thirdNo) {
        this.thirdNo = thirdNo;
    }
}
