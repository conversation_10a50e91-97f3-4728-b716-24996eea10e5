package com.holderzone.sso.enums;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 * @create 2023-07-19
 * @description
 */
public enum ReasonEnum {

    ACCOUNT_OR_PASSWORD_ERROR("用户名或密码错误"),

    ACCOUNT_NOT_SYNCHRONIZED_APPLICATION("您的账号未同步到当前应用，请联系管理员在应用中先同步账号"),

    WECHAT_LOGIN_ERROR("微信小程序登录出错"),

    ACCOUNT_DISABLED("账号已被禁用"),

    ACCOUNT_DISABLED_APPLICATION("您的账号在应用内已被禁用，不支持打开应用"),

    ACCOUNT_NULL("用户名为空"),

    PASSWORD_NULL("密码为空"),

    MERCHANT_NULL("商户号为空"),

    PHONE_NULL("手机号为空"),

    STORE_NULL("门店号为空"),

    LOGIN_ERROR_MULTIPLE_PHONE("登录业务错误,存在多个手机号"),

    ACCOUNT_NOT_EXIST("账号不存在"),

    SUCCESSFUL("成功"),

    INCORRECT_VERIFICATION_CODE("验证码错误"),

    INCORRECT_USER("用户验证失败"),

    FAILED_RESET_PASSWORD("修改密码失败,请检查参数"),

    ;

    public String getMessage() {
        return message;
    }

    private final String message;

    ReasonEnum(String message){
        this.message = message;
    }

    public static String getLocale(ReasonEnum localeMessageEnum) {
        try {
            if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
                return localeMessageEnum.getMessage();
            }
            ResourceBundle bundle = ResourceBundle.getBundle("i18n/messages", LocaleContextHolder.getLocale());
            return bundle.getString(localeMessageEnum.name());
        } catch (Exception e) {
            return localeMessageEnum.getMessage();
        }
    }

    public static String getLocaleByMessage(String message) {
        if(StringUtils.isEmpty(message)){
            return message;
        }
        for (ReasonEnum reasonEnum : ReasonEnum.values()){
            if(reasonEnum.getMessage().equals(message)){
                return getLocale(reasonEnum);
            }
        }
        return message;
    }

}
