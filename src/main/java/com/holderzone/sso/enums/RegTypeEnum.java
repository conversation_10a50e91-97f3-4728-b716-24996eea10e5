package com.holderzone.sso.enums;


/**
 * 企业注册方式
 */
public enum RegTypeEnum {

    HOLDER_CLIENT("HOLDER_CLIENT", "0", "holder客户端创建"),
    HOLDER_PLATFORM("HOLDER_PLATFORM", "1", "holder运营平台"),
    CLOUD_ADMIN("云平台", null, "云平台"),
    ;

    private String type;
    private String holderCode;
    private String desc;

    RegTypeEnum(String type, String holderCode, String desc) {
        this.type = type;
        this.holderCode = holderCode;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getHolderCode() {
        return holderCode;
    }

    public void setHolderCode(String holderCode) {
        this.holderCode = holderCode;
    }

    public static RegTypeEnum getByHolderCode(String holderCode) {
        for (RegTypeEnum value : RegTypeEnum.values()) {
            if (value.getHolderCode().equals(holderCode)) {
                return value;
            }
        }
        return RegTypeEnum.CLOUD_ADMIN;
    }

    public static String transferRegType(String regType) {
        for (RegTypeEnum value : RegTypeEnum.values()) {
            if (value.getType().equals(regType)) {
                return value.getDesc();
            }
        }
        return regType;
    }
}
