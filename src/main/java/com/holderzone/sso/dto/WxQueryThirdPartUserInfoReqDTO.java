package com.holderzone.sso.dto;

import io.swagger.annotations.ApiModelProperty;

public class WxQueryThirdPartUserInfoReqDTO {
    @ApiModelProperty(value = "主键")
    private String guid;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "第三方openId")
    private String openId;

    private String tel;

    @ApiModelProperty(value = "用户来源：易慧天下:0 赚餐:1")
    private Integer source;

    @ApiModelProperty(value = "密码")
    private String password;

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    @Override
    public String toString() {
        return "WxQueryThirdPartUserInfoReqDTO{" +
                "guid='" + guid + '\'' +
                ", nickName='" + nickName + '\'' +
                ", openId='" + openId + '\'' +
                ", tel='" + tel + '\'' +
                ", source=" + source +
                ", password='" + password + '\'' +
                '}';
    }
}