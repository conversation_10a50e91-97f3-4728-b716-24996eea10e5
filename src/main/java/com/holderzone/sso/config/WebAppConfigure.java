package com.holderzone.sso.config;

import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * web app configure
 *
 * <AUTHOR>
 * @date 17-12-5
 */
//@Configuration
public class WebAppConfigure implements WebMvcConfigurer {


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(getCrossDomainInterceptor()).addPathPatterns("/**");
    }

    @Bean
    public CrossDomainInterceptor getCrossDomainInterceptor() {
        return new CrossDomainInterceptor();
    }
}
