package com.holderzone.sso.config;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.FeignMsgUtils;
import com.netflix.client.ClientException;
import com.netflix.hystrix.exception.HystrixBadRequestException;
import feign.FeignException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.sql.SQLException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GlobalExceptionHandler
 * @date 2018/8/2 14:08
 * @description 全局异常处理
 * @program
 */
@ControllerAdvice
public class GlobalExceptionHandler {
    private final static Logger LOGGER = LoggerFactory.getLogger(GlobalExceptionHandler.class);


    @ExceptionHandler(value = ParameterException.class)
    @ResponseBody
    public Result<String> handleParameterException(ParameterException e) {
        String message = e.getMessage();
        if (LOGGER.isWarnEnabled()) {
            LOGGER.warn(message, e);
        }
        return Result.buildFailResult(400, message);
    }

    @ExceptionHandler(value = BusinessException.class)
    @ResponseBody
    public Result<String> handleBusinessException(BusinessException e) {
        String message = e.getMessage();
        if (LOGGER.isWarnEnabled()) {
            LOGGER.warn(message, e);
        }
        return Result.buildFailResult(400, message);
    }

    @ExceptionHandler(SQLException.class)
    @ResponseBody
    public Result<String> handleSQLException(SQLException e) {
        String message = e.getMessage();
        if (LOGGER.isErrorEnabled()) {
            LOGGER.error(message, e);
        }
        return Result.buildFailResult(400, "sql错误");
    }


    @ExceptionHandler(value = ClientException.class)
    @ResponseBody
    public Result<String> handleClientException(ClientException e) {
        String message = e.getMessage();
        if (LOGGER.isErrorEnabled()) {
            LOGGER.error(message, e);
        }
        return Result.buildFailResult(400, "找不到服务");
    }


    @ExceptionHandler(value = HystrixBadRequestException.class)
    @ResponseBody
    public Result<String> handleException(HystrixBadRequestException e) {
        String message = e.getMessage();
        if (LOGGER.isWarnEnabled()) {
            LOGGER.warn(message, e);
        }
        return Result.buildFailResult(400, message);
    }


    @ExceptionHandler(value = FeignException.class)
    @ResponseBody
    public Result<String> handleFeignException(FeignException e) {
        String msg = FeignMsgUtils.parseFeignMsg(e.getMessage());
        if (LOGGER.isErrorEnabled()) {
            LOGGER.error(e.getMessage(), msg);
        }

        return Result.buildFailResult(400, msg);
    }

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Result<String> handleException(RuntimeException e) {
        String message = e.getMessage();
        if (LOGGER.isErrorEnabled()) {
            LOGGER.error(message, e);
        }
        return Result.buildOpFailedResult(message);
    }


}
