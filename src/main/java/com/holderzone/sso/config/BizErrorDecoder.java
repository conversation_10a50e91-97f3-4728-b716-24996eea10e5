package com.holderzone.sso.config;

import com.netflix.hystrix.exception.HystrixBadRequestException;
import feign.Response;
import feign.Util;
import feign.codec.ErrorDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BizExceptionErrorDecoder
 * @date 2018/07/10 下午7:39
 * @description 业务异常FeignErrorDecoder
 * @program framework-payment-fmbk
 */
@Configuration
public class BizErrorDecoder extends ErrorDecoder.Default {
private final static Logger LOGGER = LoggerFactory.getLogger(BizErrorDecoder.class);
    private static final int BIZ_CODE_LOWER_BOUNDARY_INCLUDE = 400;

    private static final int BIZ_CODE_UPPER_BOUNDARY_EXCLUDE = 500;

    @Override
    public Exception decode(String methodKey, Response response) {
        int status = response.status();
        if (BIZ_CODE_LOWER_BOUNDARY_INCLUDE <= status
                && status < BIZ_CODE_UPPER_BOUNDARY_EXCLUDE) {
            String body = "未知业务错误";
            try {
                body = Util.toString(response.body().asReader());
            } catch (IOException ignore) {
            }
            return new HystrixBadRequestException(body);
        }
        return super.decode(methodKey, response);
    }
}
