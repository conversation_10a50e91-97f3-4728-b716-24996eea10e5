package com.holderzone.sso.config;

import com.holderzone.framework.response.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.servlet.http.HttpServletRequest;
import java.io.File;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MyResponseBodyAdvice
 * @date 2018/8/2 10:11
 * @description 结果统一装配
 * @program
 */
@RestControllerAdvice(basePackages = "com.holderzone.sso.controller")
public class ResponseAdvice implements ResponseBodyAdvice<Object> {
    private final static Logger logger = LoggerFactory.getLogger(ResponseAdvice.class);

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        logger.debug("MyResponseBodyAdvice==>supports:" + converterType);
        logger.debug("MyResponseBodyAdvice==>supports:" + returnType.getClass());
        logger.debug("MyResponseBodyAdvice==>supports:"
                + MappingJackson2HttpMessageConverter.class.isAssignableFrom(converterType));
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        String requestURI = request.getRequestURI();
        if (requestURI.contains("/order/notify")) {
            return false;
        }
//        MappingJackson2HttpMessageConverter.class.isAssignableFrom(converterType);
        return true;
    }

    /**
     * 如果有String类型的返回值，就有可能遇到类型不匹配的问题。
     * HttpMessageConverter是根据Controller的原始返回值类型进行处理的，而我们在ResponseAdvisor中改变了返回值的类型。
     * 如果HttpMessageConverter处理的目标类型是Object还好说，如果是其它类型就会出现问题，其中最容易出现问题的就是String类型，
     * 因为在所有的HttpMessageConverter实例集合中，StringHttpMessageConverter要比其它的Converter排得靠前一些。
     * 我们需要尝试将处理Object类型的HttpMessageConverter放得靠前一些
     *
     * @param body
     * @param returnType
     * @param selectedContentType
     * @param selectedConverterType
     * @param serverHttpRequest
     * @param serverHttpResponse
     * @return
     */
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
        if (null == body) {
            return Result.buildEmptySuccess();
        }
        if (body instanceof Result) {
            logger.debug("MyResponseBodyAdvice==>beforeBodyWrite:" + returnType + ", " + body);
            return body;
        }
        if (body instanceof File) {
            return body;
        }
        logger.debug("MyResponseBodyAdvice==>beforeBodyWrite:" + returnType + ", " + body);
        body = Result.buildSuccessResult(body);
        return body;
    }
}
