package com.holderzone.sso.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2019/04/09 上午 9:46
 * @description
 */
@Configuration
public class RestTemplateConfig {

    @Bean
    @ConditionalOnProperty(prefix = "mdm",value = "skipSSL",havingValue = "true")
    public CustomSSL simpleClientHttpRequestFactory() {
        CustomSSL factory = new CustomSSL();
        factory.setReadTimeout(10000);
        factory.setConnectTimeout(10000);
        return factory;
    }

    @Bean(name = "ssoRestTemplate")
    @ConditionalOnProperty(prefix = "mdm",value = "skipSSL",havingValue = "false" , matchIfMissing = true)
    public RestTemplate restTemplateWithSSL(RestTemplateBuilder builder){
        builder.setReadTimeout(10000);
        builder.setConnectTimeout(10000);
        return builder.build();
    }

    @Bean(name = "ssoRestTemplate")
    @ConditionalOnBean(CustomSSL.class)
    public RestTemplate restTemplateWithSkipSSL(CustomSSL  factory) {
        return new RestTemplate(factory);
    }



}
