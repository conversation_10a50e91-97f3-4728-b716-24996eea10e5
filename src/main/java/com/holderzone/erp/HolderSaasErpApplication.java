package com.holderzone.erp;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableSwagger2
@SpringBootApplication(exclude = {JpaRepositoriesAutoConfiguration.class})
@MapperScan("com.holderzone.erp.dao")
@EnableFeignClients
@EnableApolloConfig
public class HolderSaasErpApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasErpApplication.class, args);
    }

}
