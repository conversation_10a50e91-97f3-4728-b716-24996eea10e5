package com.holderzone.erp.controller;

import com.holderzone.erp.service.SuppliersService;
import com.holderzone.erp.validate.PricingSchemesValidator;
import com.holderzone.erp.validate.SuppliersValidator;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @className SuppliersController
 * @date 2019-04-26 10:50:52
 * @description
 * @program holder-saas-store-erp
 */
@Api(tags = "供应商管理")
@RestController
public class SuppliersController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SuppliersController.class);
    private final SuppliersService suppliersService;

    @Autowired
    public SuppliersController(SuppliersService suppliersService) {
        this.suppliersService = suppliersService;
    }

    @ApiOperation(value = "新建供应商")
    @PostMapping("/suppliers")
    public String createSuppliers(@RequestBody SuppliersReqDTO reqDTO) {
        LOGGER.info("ERP系统(新建供应商)-> SuppliersReqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
        SuppliersValidator.validateCreateSuppliers(reqDTO);
        return suppliersService.createSuppliers(reqDTO);
    }

    @ApiOperation(value = "更新供应商")
    @PutMapping("/suppliers")
    public String updateSuppliers(@RequestBody SuppliersReqDTO reqDTO) {
        LOGGER.info("ERP系统(更新供应商)-> SuppliersReqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
        SuppliersValidator.validateUpdateSuppliers(reqDTO);
        return suppliersService.updateSuppliers(reqDTO);
    }

    @ApiOperation(value = "查询供应商信息")
    @GetMapping("/suppliers/{guid}")
    public SuppliersDTO getSuppliersByGuid(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统(查询供应商信息)-> guid:{}", guid);
        return suppliersService.getSuppliersByGuid(guid);
    }

    @ApiOperation(value = "启禁用供应商")
    @PutMapping("/suppliers/enable/{guid}")
    public Boolean enableOrDisableSuppliers(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统(启禁用供应商)-> guid:{}", guid);
        return suppliersService.enableOrDisableSuppliers(guid);
    }

    @ApiOperation(value = "删除供应商")
    @DeleteMapping("/suppliers/{guid}")
    public Boolean deleteSuppliers(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统(删除供应商)-> guid:{}", guid);
        return suppliersService.deleteSuppliers(guid);
    }

    @ApiOperation(value = "供应商列表")
    @PostMapping("/suppliers/query/list")
    public Page<SuppliersDTO> getSuppliersList(@RequestBody SuppliersQueryDTO queryDTO) {
        LOGGER.info("ERP系统(供应商列表)-> guid:{}", JacksonUtils.writeValueAsString(queryDTO));
        SuppliersValidator.validateGetSuppliersList(queryDTO);
        return suppliersService.getSuppliersList(queryDTO);
    }

    @ApiOperation(value = "供应商下拉列表")
    @PostMapping("/suppliers/query/all")
    public List<SuppliersDTO> getAllOfSuppliersList(@RequestBody SuppliersQueryDTO queryDTO) {
        LOGGER.info("ERP系统(供应商下拉列表)-> guid:{}", JacksonUtils.writeValueAsString(queryDTO));
        SuppliersValidator.validateGetAllOfSuppliersList(queryDTO);
        return suppliersService.getAllOfSuppliersList(queryDTO);
    }

    @ApiOperation(value = "查询供应商正常状态供货列表")
    @PostMapping("/suppliers/category/all")
    public List<CategoryDTO> getSuppliersMaterialListAll(@RequestBody SuppliersMaterialQueryDTO queryDTO) {
        LOGGER.info("ERP系统(查询供应商正常状态供货列表)-> SuppliersMaterialQueryDTO:{}", JacksonUtils.writeValueAsString(queryDTO));
        SuppliersValidator.validateGetSuppliersMaterialListAll(queryDTO);
        return suppliersService.getSuppliersMaterialListAll(queryDTO);
    }
}
