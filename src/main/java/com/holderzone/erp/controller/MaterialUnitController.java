package com.holderzone.erp.controller;

import com.holderzone.erp.service.IMaterialUnitService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.erp.MaterialUnitDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/27 15:25
 */
@Api(tags = "物料单位相关Api")
@RestController
@RequestMapping("materialUnit")
public class MaterialUnitController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MaterialUnitController.class);
    @Autowired
    IMaterialUnitService materialUnitService;

    @PostMapping
    @ApiOperation("增加单位")
    public void add(@RequestBody MaterialUnitDTO materialUnitDTO) {
        LOGGER.info("添加单位入参:->{}", JacksonUtils.writeValueAsString(materialUnitDTO));
        materialUnitService.add(materialUnitDTO);
    }

    @GetMapping
    @ApiOperation("获取单位列表")
    public List<MaterialUnitDTO> findList() {
        return materialUnitService.list();
    }
}
