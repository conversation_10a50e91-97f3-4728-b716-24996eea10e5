package com.holderzone.erp.controller;

import com.holderzone.erp.service.MaterialStockService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.InOutDocumentSelectDTO;
import com.holderzone.saas.store.dto.erp.MaterialBySupplierQueryDTO;
import com.holderzone.saas.store.dto.erp.MaterialDTO;
import com.holderzone.saas.store.dto.erp.StockQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 库存接口
 *
 * <AUTHOR>
 * @date 2019/05/10 11:01
 */
@Slf4j
@Api(tags = "库存查询接口")
@RestController
@RequestMapping("stock")
public class StockController {

    @Autowired
    MaterialStockService stockService;

    @PostMapping("findByCondition")
    @ApiOperation("条件查询库存列表")
    public Page findByCondition(@RequestBody StockQueryDTO stockQueryDTO) {
        log.info("库存查询接口入参:{}", JacksonUtils.writeValueAsString(stockQueryDTO));
        Page page = new Page(stockQueryDTO.getCurrentPage(), stockQueryDTO.getPageSize());
        List<MaterialDTO> dataList = stockService.findStockPage(stockQueryDTO, page);
        page.setData(dataList);
        return page;
    }

    @PostMapping("listByCondition")
    @ApiOperation("条件查询库存列表")
    public List<MaterialDTO> listByCondition(@RequestBody StockQueryDTO stockQueryDTO) {
        log.info("[条件查询库存列表]入参={}", JacksonUtils.writeValueAsString(stockQueryDTO));
        return stockService.listByCondition(stockQueryDTO);
    }

    @ApiOperation("查询供货商的商品")
    @PostMapping("query_material_by_supplier")
    public List<InOutDocumentSelectDTO> queryMaterialBySupplier(@RequestBody MaterialBySupplierQueryDTO queryDTO) {
        log.info("[查询供货商的商品]入参={}", JacksonUtils.writeValueAsString(queryDTO));
        return stockService.queryMaterialBySupplier(queryDTO);
    }

}
