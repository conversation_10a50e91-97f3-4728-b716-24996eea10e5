package com.holderzone.erp.controller;

import com.holderzone.erp.service.GoodsSerialService;
import com.holderzone.erp.service.GoodsService;
import com.holderzone.erp.service.InRepertoryService;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.GoodsExportDTO;
import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import com.holderzone.saas.store.dto.erp.erpretail.RepertorySumDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.*;
import com.holderzone.saas.store.dto.erp.erpretail.resp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/21 下午 15:53
 * @description
 */

@Slf4j
@Api(tags = "库存信息")
@RestController
@RequestMapping("/repertory")
public class RepertoryController {

    @Autowired
    private InRepertoryService inRepertoryService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private GoodsSerialService goodsSerialService;


    @ApiOperation("新建库存单")
    @PostMapping("/insert_repertory")
    public boolean insertRepertory(@RequestBody @Validated CreateRepertoryReqDTO createRepertoryReqDTO) {
        log.info("新建库存单入参：{}", JacksonUtils.writeValueAsString(createRepertoryReqDTO));

        // 制单时间
        if (createRepertoryReqDTO.getInvoiceMakeTime() == null) {
            createRepertoryReqDTO.setInvoiceMakeTime(DateTimeUtils.nowString());
        }

        // 经办人
        if (StringUtils.isEmpty(createRepertoryReqDTO.getOperatorName())) {
            createRepertoryReqDTO.setOperatorName(UserContextUtils.getUserName());
        }
        return inRepertoryService.insertRepertoryAndDetail(createRepertoryReqDTO);
    }

    @ApiOperation("销售出库")
    @PostMapping("/sale_out_repertory")
    public boolean saleOutRepertory(@RequestBody SubstractRepertoryForTradeReqDTO substractRepertoryForTradeReqDTO) {
        log.info("销售出库入参：{}", JacksonUtils.writeValueAsString(substractRepertoryForTradeReqDTO));
        return inRepertoryService.saleOutRepertory(substractRepertoryForTradeReqDTO);
    }

    @ApiOperation("查询商品库存汇总信息")
    @PostMapping("/query_goods_repertory_sum_info")
    public Page<GoodsSumInfoRespDTO> queryGoodsRepertorySumInfo(@RequestBody @Validated QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO) {
        log.info("查询商品库存汇总信息入参：{}", JacksonUtils.writeValueAsString(queryGoodsSumInfoReqDTO));
        return inRepertoryService.queryGoodsRepertorySumInfo(queryGoodsSumInfoReqDTO);
    }

    @ApiOperation("查询商品出入库流水")
    @PostMapping("/query_goods_serial")
    public Page<GoodsSerialRespDTO> queryGoodsSerial(@RequestBody @Validated QueryGoodsSerialReqDTO queryGoodsSerialReqDTO) {
        log.info("查询商品出入库流水：{}", JacksonUtils.writeValueAsString(queryGoodsSerialReqDTO));
        return goodsSerialService.queryGoodsSerial(queryGoodsSerialReqDTO);
    }

    @ApiOperation("查询出入库单据及其明细(仅查看时使用)")
    @PostMapping("/query_repertory_detail")
    public RepertoryDetailInfoRespDTO queryRepertoryDetail(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("查询出入库单据及其明细(仅查看时使用)入参：{}", JacksonUtils.writeValueAsString(singleDataDTO));
        if (StringUtils.isEmpty(singleDataDTO.getData())) {
            throw new ParameterException("单据guid不能为空");
        }
        return inRepertoryService.queryRepertoryDetail(singleDataDTO.getData());
    }

    @ApiOperation("查询出入库列表(分页)")
    @PostMapping("/query_in_out_repertory_list")
    public Page<RepertoryManageRespDTO> queryInOutRepertoryList(@RequestBody @Validated QueryRepertoryManageReqDTO queryRepertoryManageReqDTO) {
        log.info("查询出入库列表(分页)入参: {}", JacksonUtils.writeValueAsString(queryRepertoryManageReqDTO));
        return inRepertoryService.queryRepertoryManageList(queryRepertoryManageReqDTO);
    }

    @ApiOperation("作废库存单")
    @PostMapping("/invalid_repertory")
    public boolean invalidRepertory(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("作废库存单入参: {}", JacksonUtils.writeValueAsString(singleDataDTO));
        return inRepertoryService.invalidRepertory(singleDataDTO);
    }

    @ApiOperation("查询商品列表，仅包含开启库存的商品")
    @PostMapping("/query_goods_list")
    public List<GoodsClassifyAndItemRespDTO> queryGoodsList(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("查询商品列表入参：{}", JacksonUtils.writeValueAsString(singleDataDTO));
        return goodsService.queryGoodsList(singleDataDTO);
    }

    @ApiOperation("修改商品分类名称")
    @PostMapping("/modify_goods_classify")
    public boolean modifyGoodsClassify(@RequestParam("classifyGuid") String classifyGuid, @RequestParam("classifyName") String classifyName) {
        log.info("修改商品分类名称入参：{}", classifyGuid);
        return goodsService.modifyClassifyName(classifyGuid, classifyName);
    }

    @ApiOperation("修改商品各项信息")
    @PostMapping("/modify_goods_info")
    public boolean modifyGoodsInfo(@RequestBody InOutGoodsDTO inOutGoodsDTO) {
        log.info("修改商品各项信息入参：{}", JacksonUtils.writeValueAsString(inOutGoodsDTO));
        return goodsService.modifyGoodsInfo(inOutGoodsDTO);
    }

    @ApiOperation("商品取消关联库存")
    @PostMapping("/cancel_relate_repertory")
    public boolean cancelRelateRepertory(@RequestParam("goodsGuid") String goodsGuid) {
        log.info("商品取消关联库存入参：{}", goodsGuid);
        return goodsService.cancelRelateRepertory(goodsGuid);
    }

    @ApiOperation("查询商品信息")
    @PostMapping("/query_goods_info")
    public InOutGoodsDTO queryGoodsInfo(@RequestParam("goodsGuid") String goodsGuid) {
        log.info("查询商品信息入参：{}", goodsGuid);
        return goodsService.queryGoodsInfo(goodsGuid);
    }

    @ApiOperation("查询导出商品信息")
    @PostMapping("/query_export_goods_list")
    public List<GoodsExportDTO> queryExportGoodsList(@RequestBody List<String> goodsGuids) {
        log.info("查询导出商品信息：{}", goodsGuids);
        return goodsService.queryExportGoodsList(goodsGuids);
    }


    @ApiOperation("查询商品库存总数，动销")
    @PostMapping("/query_repertory_sum")
    public RepertorySumDTO queryGoodsInfo(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("查询商品信息入参：{}", singleDataDTO);
        return goodsService.queryRepertorySum(singleDataDTO);
    }
}
