package com.holderzone.erp.controller;

import com.holderzone.erp.service.CheckoutDocumentService;
import com.holderzone.erp.validate.CheckoutDocumentValidator;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.erp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.holderzone.erp.utils.DateUtil.getCurrentDate;

/**
 * <AUTHOR>
 * @date 2019/05/07 下午 18:39
 * @description
 */
@Api(tags = "盘点单相关接口")
@RestController
@RequestMapping("/checkoutDocument")
public class CheckoutDocumentController {

    private static final Logger log = LoggerFactory.getLogger(CheckoutDocumentController.class);

    @Autowired
    private CheckoutDocumentValidator validator;

    @Autowired
    private CheckoutDocumentService checkoutDocumentService;

    @ApiOperation("添加或者更新盘点单")
    @PostMapping("/addOrUpdateCheckoutDocument")
    public String addOrUpdateCheckoutDocument(@RequestBody CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO) {
        log.info("添加或者更新盘点单入参: {}", JacksonUtils.writeValueAsString(addOrUpdateDTO));
        validator.addOrUpdateCheckoutDocumentValidate(addOrUpdateDTO);
        if (StringUtils.isEmpty(addOrUpdateDTO.getOperatorGuid())) {
            addOrUpdateDTO.setOperatorGuid(addOrUpdateDTO.getUserGuid());
        }
        if (StringUtils.isEmpty(addOrUpdateDTO.getOperatorName())){
            addOrUpdateDTO.setOperatorName(addOrUpdateDTO.getUserName());
        }
        if (addOrUpdateDTO.getDocumentDate() == null) {
            addOrUpdateDTO.setDocumentDate(getCurrentDate());
        }
        String guid;
        if (StringUtils.isEmpty(addOrUpdateDTO.getGuid())) {
            guid = IDUtils.nextId();
            addOrUpdateDTO.setGuid(guid);
            checkoutDocumentService.insertCheckoutDocumentAndDetail(addOrUpdateDTO);
        } else {
            guid = addOrUpdateDTO.getGuid();
            checkoutDocumentService.updateCheckoutDocumentAndDetail(addOrUpdateDTO);
        }
        return guid;
    }

    @ApiOperation("查询物料信息(新增物料时使用)")
    @PostMapping("/selectDocumentDetailForAdd")
    public List<CheckoutDocumentDetailSelectDTO> selectDocumentDetailForAdd(@RequestBody CheckoutDocumentDetailQueryDTO queryDTO) {
        log.info("查询盘点单物料信息(新增物料时使用)入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        validator.selectDocumentDetailForAddValidate(queryDTO);
        return checkoutDocumentService.selectDocumentDetailForAdd(queryDTO);
    }

    @ApiOperation("查询盘点单及其明细(查看时使用)")
    @PostMapping("/selectDocumentAndDetailForSelect")
    public CheckoutDocumentSelectDTO selectDocumentAndDetailForSelect(String documentGuid) {
        log.info("查询盘点单及其明细(查看时使用)入参: {}", documentGuid);
        if (StringUtils.isEmpty(documentGuid)) {
            throw new ParameterException("单据guid不能为空");
        }
        return checkoutDocumentService.selectDocumentAndDetailForSelect(documentGuid);
    }

    @ApiModelProperty("查询盘点单及其明细(编辑时使用)")
    @PostMapping("/selectDocumentAndDetailForUpdate")
    public CheckoutDocumentSelectDTO selectDocumentAndDetailForUpdate(String documentGuid){
        log.info("查询盘点单及其明细(编辑时使用)入参：{}",documentGuid);
        if (StringUtils.isEmpty(documentGuid)) {
            throw new ParameterException("单据guid不能为空");
        }
        CheckoutDocumentSelectDTO selectDTO = checkoutDocumentService.selectDocument(documentGuid);
        List<CheckoutDocumentDetailSelectDTO> detailDTOList = checkoutDocumentService.selectDocumentDetailForUpdate(documentGuid);
        selectDTO.setDetailList(detailDTOList);
        return selectDTO;
    }

    @ApiOperation("删除盘点单")
    @PostMapping("/deleteDocument")
    public void deleteDocument(String documentGuid) {
        log.info("删除盘点单入参: {}", documentGuid);
        validator.deleteDocumentValidate(documentGuid);
        checkoutDocumentService.deleteCheckoutDocumentAndDetail(documentGuid);
    }

    @ApiOperation("提交盘点单")
    @PostMapping("/submitCheckoutDocument")
    public String submitCheckoutDocument(@RequestBody CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO) {
        log.info("提交盘点单入参: {}" , JacksonUtils.writeValueAsString(addOrUpdateDTO));
        validator.addOrUpdateCheckoutDocumentValidate(addOrUpdateDTO);
        if (StringUtils.isEmpty(addOrUpdateDTO.getOperatorGuid())) {
            addOrUpdateDTO.setOperatorGuid(addOrUpdateDTO.getUserGuid());
        }
        if (StringUtils.isEmpty(addOrUpdateDTO.getOperatorName())){
            addOrUpdateDTO.setOperatorName(addOrUpdateDTO.getUserName());
        }
        if (addOrUpdateDTO.getDocumentDate() == null) {
            addOrUpdateDTO.setDocumentDate(getCurrentDate());
        }

        if (StringUtils.isEmpty(addOrUpdateDTO.getGuid())){
            return checkoutDocumentService.insertAndSubmitCheckoutDocument(addOrUpdateDTO);
        }else{
            checkoutDocumentService.updateAndSubmitCheckoutDocumentWithLock(addOrUpdateDTO);
            return addOrUpdateDTO.getGuid();
        }
    }

    @ApiOperation("分页查询盘点单列表")
    @PostMapping("/selectCheckoutDocumentForPage")
    public Page<CheckoutDocumentSelectDTO> selectCheckoutDocumentForPage(@RequestBody CheckoutDocumentQueryDTO queryDTO){
        log.info("分页查询盘点单列表入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        validator.selectCheckoutDocumentForPageValidate(queryDTO);
        return checkoutDocumentService.selectCheckoutDocumentForPage(queryDTO);
    }
}
