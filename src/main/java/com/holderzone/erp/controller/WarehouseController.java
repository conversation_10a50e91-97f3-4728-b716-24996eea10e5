package com.holderzone.erp.controller;

import com.holderzone.erp.service.WarehouseService;
import com.holderzone.erp.validate.WarehouseValidator;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.WarehouseDTO;
import com.holderzone.saas.store.dto.erp.WarehouseQueryDTO;
import com.holderzone.saas.store.dto.erp.WarehouseReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @className WarehouseController
 * @date 2019-04-24 14:37:44
 * @description
 * @program holder-saas-store-erp
 */
@Api(tags = "仓库管理")
@RestController
public class WarehouseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(WarehouseController.class);
    private final WarehouseService warehouseService;

    @Autowired
    public WarehouseController(WarehouseService warehouseService) {
        this.warehouseService = warehouseService;
    }

    @ApiOperation(value = "创建仓库")
    @PostMapping("/warehouse")
    public String createWarehouse(@RequestBody WarehouseReqDTO reqDTO) {
        LOGGER.info("ERP系统(创建仓库)-> WarehouseReqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
        WarehouseValidator.validateCreateWarehouse(reqDTO);
        return warehouseService.createWarehouse(reqDTO);
    }

    @ApiOperation(value = "更新仓库")
    @PutMapping("/warehouse")
    public String updateWarehouse(@RequestBody WarehouseReqDTO reqDTO) {
        LOGGER.info("ERP系统(更新仓库)-> WarehouseReqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
        WarehouseValidator.validateUpdateWarehouse(reqDTO);
        return warehouseService.updateWarehouse(reqDTO);
    }

    @ApiOperation(value = "更新门店仓库名称")
    @PutMapping("/warehouse/store")
    public String updateStoreWarehouse(@RequestBody WarehouseReqDTO reqDTO) {
        LOGGER.info("ERP系统(更新仓库)-> WarehouseReqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
        return warehouseService.updateStoreWarehouse(reqDTO);
    }

    @ApiOperation(value = "查询仓库信息")
    @GetMapping("/warehouse")
    public WarehouseDTO getWarehouseByGuid(String guid) {
        LOGGER.info("ERP系统(查询仓库信息)-> guid:{}", guid);
        return warehouseService.getWarehouseByGuid(guid);
    }

    @ApiOperation(value = "查询仓库列表")
    @PostMapping("/warehouse/list")
    public Page<WarehouseDTO> getWarehouseList(@RequestBody WarehouseQueryDTO queryDTO) {
        LOGGER.info("ERP系统(查询仓库列表)-> WarehouseQueryDTO:{}", JacksonUtils.writeValueAsString(queryDTO));
        WarehouseValidator.validateGetWarehouseList(queryDTO);
        return warehouseService.getWarehouseList(queryDTO);
    }

    @ApiOperation(value = "仓库下拉列表")
    @PostMapping("/warehouse/name")
    public List<WarehouseDTO> getWarehouseListByName(@RequestBody WarehouseQueryDTO queryDTO) {
        LOGGER.info("ERP系统(仓库下拉列表)-> name:{}", JacksonUtils.writeValueAsString(queryDTO));
        return warehouseService.getWarehouseListByName(queryDTO);
    }

    @ApiOperation(value = "启禁用仓库")
    @PutMapping("/warehouse/enable/{guid}")
    public Boolean enableOrDisableWarehouse(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统(启禁用仓库)-> guid:{}", guid);
        return warehouseService.enableOrDisableWarehouse(guid);
    }

    @ApiOperation(value = "仓库解锁或锁定")
    @PutMapping("/warehouse/lock/{guid}")
    public Boolean lockOrUnlockWarehouse(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统(仓库解锁或锁定)-> guid:{}", guid);
        return warehouseService.lockOrUnlockWarehouse(guid);
    }

    @ApiOperation(value = "校验仓库是否锁定")
    @GetMapping("/warehouse/lock/{guid}")
    public Boolean isLock(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统(校验仓库是否锁定)-> guid:{}", guid);
        return warehouseService.isLock(guid);
    }

    @ApiOperation(value = "删除仓库", notes = "此方法暂不对外开放")
    @DeleteMapping("/warehouse/delete/{guid}")
    public Boolean deleteWarehouse(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统(删除仓库)-> guid:{}", guid);
        return warehouseService.deleteWarehouse(guid);
    }

    @ApiOperation(value = "生成仓库编号")
    @GetMapping("/warehouse/code")
    public String warehouseCode() {
        return warehouseService.warehouseCode();
    }

}
