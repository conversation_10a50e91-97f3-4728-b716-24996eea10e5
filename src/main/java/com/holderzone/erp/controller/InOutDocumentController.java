package com.holderzone.erp.controller;

import com.holderzone.erp.service.InOutDocumentService;
import com.holderzone.erp.service.WarehouseService;
import com.holderzone.erp.validate.InOutDocumentValidator;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.erp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

import static com.holderzone.erp.entity.enumeration.DocumentTypeEnum.OUT_RETURN;
import static com.holderzone.erp.utils.DateUtil.getCurrentDate;

/**
 * <AUTHOR>
 * @date 2019/04/29 上午 11:00
 * @description
 */
@Api(tags = "出入库单据")
@RestController
@RequestMapping("/inOutDocument")
public class InOutDocumentController {

    private static final Logger log = LoggerFactory.getLogger(InOutDocumentController.class);

    @Autowired
    private InOutDocumentValidator validator;

    @Autowired
    private InOutDocumentService inOutDocumentService;

    @Autowired
    private WarehouseService warehouseService;

    @ApiOperation("插入或者更新入库单据及其明细")
    @PostMapping("/insertOrUpdate")
    public String insertOrUpdateInOutDocument(@RequestBody InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        log.info("插入或者更新入库单据及其明细入参：{}", JacksonUtils.writeValueAsString(inOutDocumentDTO));
        validator.insertOrUpdateInOutDocumentValidate(inOutDocumentDTO);
        if (warehouseService.isLock(inOutDocumentDTO.getWarehouseGuid())) {
            throw new BusinessException("仓库被锁盘，不能添加或者编辑出入库");
        }
        if (inOutDocumentDTO.getDocumentDate() == null) {
            inOutDocumentDTO.setDocumentDate(getCurrentDate());
        }
        if (StringUtils.isEmpty(inOutDocumentDTO.getOperatorGuid())) {
            inOutDocumentDTO.setOperatorGuid(inOutDocumentDTO.getUserGuid());
        }
        if (StringUtils.isEmpty(inOutDocumentDTO.getOperatorName())) {
            inOutDocumentDTO.setOperatorName(inOutDocumentDTO.getUserName());
        }
        String guid;
        if (StringUtils.isEmpty(inOutDocumentDTO.getGuid())) {
            guid = IDUtils.nextId();
            inOutDocumentDTO.setGuid(guid);
            inOutDocumentService.insertInOutDocumentAndDetail(inOutDocumentDTO);
        } else {
            guid = inOutDocumentDTO.getGuid();
            inOutDocumentService.updateInOutDocumentAndDetail(inOutDocumentDTO);
        }
        return guid;
    }


    @ApiOperation("查询出入库单据中的物料信息(新添物料使用)")
    @PostMapping("/selectMaterialListForAdd")
    public List<InOutDocumentDetailSelectDTO> selectMaterialListForAdd(@RequestBody InOutDocumentDetailQueryDTO materialQueryDTO) {
        log.info("查询出入库单据中的物料信息(新添物料使用)入参: {}", JacksonUtils.writeValueAsString(materialQueryDTO));
        validator.selectMaterialListValidate(materialQueryDTO);
        return inOutDocumentService.selectMaterialListForAdd(materialQueryDTO);
    }

    @ApiOperation("查询对应入库单的退货数量小于入库数量的物料明细(新增退货出库单并有关联单据时使用)")
    @PostMapping("/selectMaterialListForReturn")
    public List<InOutDocumentDetailSelectDTO> selectMaterialListForReturn(@RequestParam("contactDocumentGuid") String contactDocumentGuid) {
        log.info("查询对应入库单的退货数量小于入库数量的物料明细(新增退货出库单时使用)入参：{}", contactDocumentGuid);
        if (StringUtils.isEmpty(contactDocumentGuid)) {
            throw new ParameterException("单据guid不能为空");
        }
        return inOutDocumentService.selectMaterialListForReturn(contactDocumentGuid);
    }

    @ApiOperation("提交出入库单")
    @PostMapping("/submitInOutDocument")
    public String submitInOutDocument(@RequestBody InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        log.info("提交出入库单据入参： {}", JacksonUtils.writeValueAsString(inOutDocumentDTO));
        validator.insertOrUpdateInOutDocumentValidate(inOutDocumentDTO);
        if (warehouseService.isLock(inOutDocumentDTO.getWarehouseGuid())) {
            throw new BusinessException("仓库被锁盘，不能提交出入库单");
        }
        if (inOutDocumentDTO.getDocumentDate() == null) {
            inOutDocumentDTO.setDocumentDate(getCurrentDate());
        }
        if (StringUtils.isEmpty(inOutDocumentDTO.getOperatorGuid())) {
            inOutDocumentDTO.setOperatorGuid(inOutDocumentDTO.getUserGuid());
        }
        if (StringUtils.isEmpty(inOutDocumentDTO.getOperatorName())) {
            inOutDocumentDTO.setOperatorName(inOutDocumentDTO.getUserName());
        }

        if (StringUtils.isEmpty(inOutDocumentDTO.getGuid())) {
            if (OUT_RETURN.getType().equals(inOutDocumentDTO.getType()) &&
                    !StringUtils.isEmpty(inOutDocumentDTO.getContactDocumentGuid())) {
                return inOutDocumentService.insertAndSubmitInOutDocumentWithLock(inOutDocumentDTO);
            } else {
                return inOutDocumentService.insertAndSubmitInOutDocument(inOutDocumentDTO);
            }

        } else {
            inOutDocumentService.updateAndSubmitInOutDocumentWithLock(inOutDocumentDTO);
            return inOutDocumentDTO.getGuid();
        }
    }

    @ApiOperation("删除出入库单")
    @PostMapping("/deleteDocument")
    public void deleteDocument(@RequestParam("documentGuid") String documentGuid) {
        if (StringUtils.isEmpty(documentGuid)) {
            throw new ParameterException("单据guid不能为空");
        }
        log.info("删除出入库单入参: {}", documentGuid);
        String warehouseGuid = inOutDocumentService.selectWarehouseGuidByDocumentGuid(documentGuid);
        if (StringUtils.isEmpty(warehouseGuid)) {
            throw new BusinessException("出入库单据不存在");
        }
        if (warehouseService.isLock(warehouseGuid)) {
            throw new BusinessException("仓库被锁盘，不能进行删除操作");
        }
        inOutDocumentService.deleteDocument(documentGuid);
    }

    @ApiOperation("查询关联单据")
    @PostMapping("/selectDocumentGuidList")
    public List<String> selectDocumentGuidList(@RequestBody InOutContactDocumentQueryDTO queryDTO) {
        log.info("查询关联单据入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        validator.selectDocumentGuidListValidate(queryDTO);
        return inOutDocumentService.selectDocumentGuidList(queryDTO);
    }

    @ApiOperation("查询出入库单据及其明细(编辑时使用)")
    @PostMapping("/selectDocumentAndDetailForUpdate")
    public InOutDocumentSelectDTO selectDocumentAndDetailForUpdate(@RequestParam("documentGuid") String documentGuid) {
        log.info("查询出入库单据及其明细(编辑时使用)入参：{}", documentGuid);
        if (StringUtils.isEmpty(documentGuid)) {
            throw new ParameterException("单据guid不能为空");
        }
        InOutDocumentSelectDTO document = inOutDocumentService.selectDocumentForUpdate(documentGuid);
        List<InOutDocumentDetailSelectDTO> detailList = inOutDocumentService.selectMaterialListForUpdate(documentGuid);
        document.setDetailList(detailList);
        return document;
    }

    @ApiOperation("查询出入库单据及其明细(仅查看时使用)")
    @PostMapping("/selectDocumentAndDetailForSelect")
    public InOutDocumentSelectDTO selectDocumentAndDetailForSelect(@RequestParam("documentGuid") String documentGuid) {
        log.info("查询出入库单据及其明细(仅查看时使用)入参：{}", documentGuid);
        if (StringUtils.isEmpty(documentGuid)) {
            throw new ParameterException("单据guid不能为空");
        }
        return inOutDocumentService.selectDocumentAndDetailForSelect(documentGuid);
    }

    @ApiOperation("查询出入库单详情列表")
    @PostMapping("/query_document_detail_list")
    public List<InOutDocumentSelectDTO> queryDocumentDetailList(@RequestBody DocumentDetailListQueryDTO queryDTO) {
        log.info("[查询出入库单详情列表]queryDTO={}", JacksonUtils.writeValueAsString(queryDTO));
        if (CollectionUtils.isEmpty(queryDTO.getDocumentGuidList())) {
            throw new ParameterException("单据guid不能为空");
        }
        return inOutDocumentService.queryDocumentDetailList(queryDTO);
    }

    @ApiOperation("查询出入库列表(分页)")
    @PostMapping("/selectDocumentListForPage")
    public Page<InOutDocumentSelectDTO> selectDocumentListForPage(@RequestBody InOutDocumentQueryDTO queryDTO) {
        log.info("查询出入库列表(分页)入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        validator.selectDocumentListForPageValidate(queryDTO);
        return inOutDocumentService.selectDocumentListForPage(queryDTO);
    }

    @ApiOperation("供应商对账表(分页)")
    @PostMapping("/reconciliation")
    public Page<InOutDocumentSelectDTO> selectSuppliersReconciliation(@RequestBody SuppliersReconciliationQueryDTO queryDTO) {
        log.info("供应商对账表(分页)入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        validator.selectSuppliersReconciliationValidate(queryDTO);
        return inOutDocumentService.selectSuppliersReconciliation(queryDTO);
    }

    @ApiOperation("结算总金额")
    @PostMapping("/reconciliation/total")
    public BigDecimal selectSuppliersReconciliationTotalAmount(@RequestBody SuppliersReconciliationQueryDTO queryDTO) {
        log.info("结算总金额入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        validator.selectSuppliersReconciliationValidate(queryDTO);
        return inOutDocumentService.selectSuppliersReconciliationTotalAmount(queryDTO);
    }

    @ApiOperation("结算")
    @PostMapping("/reconciliation/settle")
    public Boolean settleSuppliersReconciliation(@RequestBody List<String> list) {
        log.info("结算总金额入参: {}", JacksonUtils.writeValueAsString(list));
        validator.settleSuppliersReconciliationValidate(list);
        return inOutDocumentService.settleSuppliersReconciliation(list);
    }

    @ApiOperation("出入库流水明细(分页)")
    @PostMapping("/selectFlowDetailListForPage")
    public Page<InOutDocumentFolwDetailSelectDTO> selectFlowDetailListForPage(@RequestBody InOutDocumentFlowDetailQueryDTO queryDTO) {
        log.info("出入库流水明细(分页)入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        validator.selectFlowDetailListForPage(queryDTO);
        return inOutDocumentService.selectFlowDetailListForPage(queryDTO);
    }


    @ApiOperation("下订单时生成出入库单并扣减库存")
    @PostMapping("/reduceStockForOrder")
    public void reduceStockForOrder(@RequestBody OrderSkuDTO orderSkuDTO) {
        log.info("下订单时扣减库存接口入参: {}", JacksonUtils.writeValueAsString(orderSkuDTO));
        if (orderSkuDTO.getSkuList() == null || orderSkuDTO.getSkuList().isEmpty()) {
            return;
        }
        validator.reduceStockForOrderValidate(orderSkuDTO);
        inOutDocumentService.publishOrderReduceStockMsg(orderSkuDTO);
    }

    @ApiOperation("下订单时生成出入库单并扣减库存(批量)")
    @PostMapping("/reduceStockForOrderBatch")
    public void reduceStockForOrderBatch(@RequestBody List<OrderSkuDTO> orderSkuDTO) {
        log.info("下订单时扣减库存接口入参(批量): {}", JacksonUtils.writeValueAsString(orderSkuDTO));
        orderSkuDTO.forEach(sku -> {
            if (sku.getSkuList() == null || sku.getSkuList().isEmpty()) {
                log.info("下订单时扣减库存接口入参(批量) skuList 空！！！");
                return;
            }
        });
        validator.reduceStockForOrderValidateBatch(orderSkuDTO);
        inOutDocumentService.publishOrderReduceStockMsgBatch(orderSkuDTO);
    }

    @ApiOperation("新建出入库单据时批量导入物料")
    @PostMapping("/importMaterialList")
    public List<InOutDocumentDetailSelectDTO> importMaterialList(@RequestBody InOutDocumentMaterialImportDTO inOutDocumentMaterialImportDTO){
        log.info("新建出入库单据时批量导入物料: {}", JacksonUtils.writeValueAsString(inOutDocumentMaterialImportDTO));
        validator.importMaterialListValidate(inOutDocumentMaterialImportDTO);
        return inOutDocumentService.importMaterialList(inOutDocumentMaterialImportDTO);
    }
}
