package com.holderzone.erp.controller;

import com.holderzone.erp.service.IMaterialCategoryService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.CategoryDTO;
import com.holderzone.saas.store.dto.erp.CategoryListQueryDTO;
import com.holderzone.saas.store.dto.erp.MaterialCategoryDTO;
import com.holderzone.saas.store.dto.erp.MaterialCategoryQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("materialCategory")
@Api(tags = "物料分类Api")
public class MaterialCategoryController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MaterialCategoryController.class);
    @Autowired
    IMaterialCategoryService materialCategoryService;

    @PostMapping
    @ApiOperation("添加物料分类")
    public Boolean add(@RequestBody MaterialCategoryDTO materialCategoryDTO) {
        LOGGER.info("添加物料分类入参:->{}", JacksonUtils.writeValueAsString(materialCategoryDTO));
        if (materialCategoryService.countByCode(materialCategoryDTO) > 0) {
            throw new BusinessException("分类已存在");
        }
        materialCategoryService.add(materialCategoryDTO);
        return true;
    }

    @PutMapping
    @ApiOperation("修改物料分类")
    public Boolean update(@RequestBody MaterialCategoryDTO materialCategoryDTO) {
        LOGGER.info("修改物料分类入参:->{}", JacksonUtils.writeValueAsString(materialCategoryDTO));
        if (materialCategoryService.countByCode(materialCategoryDTO) > 0) {
            throw new BusinessException("分类已存在");
        }
        materialCategoryService.update(materialCategoryDTO);
        return true;
    }

    @GetMapping("{guid}")
    @ApiOperation("根据GUID查询分类信息")
    public MaterialCategoryDTO findByGuid(@PathVariable("guid") String guid) {
        MaterialCategoryDTO materialCategoryDTO = materialCategoryService.findByGuid(guid);
        return materialCategoryDTO;
    }

    @PostMapping("findByCondition")
    @ApiOperation("条件查询分类信息列表,分页")
    public Page<MaterialCategoryDTO> findByCondition(@RequestBody MaterialCategoryQueryDTO queryDTO) {
        LOGGER.info("条件查询分类信息列表入参:->{}", JacksonUtils.writeValueAsString(queryDTO));
        Page page = new Page(queryDTO.getCurrentPage(), queryDTO.getPageSize());
        List<MaterialCategoryDTO> dataList = materialCategoryService.findByCondition(queryDTO, page);
        page.setData(dataList);
        return page;
    }

    @PostMapping("list")
    @ApiOperation("查询所有的分类下拉框信息,不包括物料")
    public List<MaterialCategoryDTO> findList(@RequestBody CategoryListQueryDTO categoryListQueryDTO) {
        LOGGER.info("查询所有的分类下拉框信息,不包括物料入参:->{}", JacksonUtils.writeValueAsString(categoryListQueryDTO));
        return materialCategoryService.findList(categoryListQueryDTO);
    }

    @GetMapping("countCategory/{guid}")
    @ApiOperation("统计某分类下的物料使用数量")
    public Long countCategory(@PathVariable("guid") String guid) {
        LOGGER.info("统计某分类下的物料使用数量入参:guid=",guid);
        return materialCategoryService.countMaterialByCategory(guid);
    }

    @PostMapping("listCategory")
    @ApiOperation("查询所有的分类物料信息")
    public List<CategoryDTO> findCategoryAndMaterial(@RequestBody CategoryListQueryDTO categoryListQueryDTO) {
        LOGGER.info("查询所有的分类物料信息入参:->{}",JacksonUtils.writeValueAsString(categoryListQueryDTO));
        return materialCategoryService.findCategoryAndMaterial(categoryListQueryDTO.getStoreGuid(), categoryListQueryDTO.getSearchConditions());
    }

    @DeleteMapping("{guid}")
    @ApiOperation("删除物料分类")
    public Boolean delete(@PathVariable("guid") String guid) {
        LOGGER.info("删除物料分类入参:->{}", guid);
        materialCategoryService.delete(guid);
        return true;
    }
}