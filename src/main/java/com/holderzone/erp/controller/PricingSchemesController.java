package com.holderzone.erp.controller;

import com.holderzone.erp.service.PricingSchemesService;
import com.holderzone.erp.validate.PricingSchemesValidator;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.erp.PricingReqDTO;
import com.holderzone.saas.store.dto.erp.PricingSchemesDTO;
import com.holderzone.saas.store.dto.erp.PricingSchemesQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @className PricingSchemesController
 * @date 2019-04-27 10:15:10
 * @description
 * @program holder-saas-store-erp
 */
@Api(tags = "供应商报价方案")
@RestController
public class PricingSchemesController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PricingSchemesController.class);
    private final PricingSchemesService pricingSchemesService;

    @Autowired
    public PricingSchemesController(PricingSchemesService pricingSchemesService) {
        this.pricingSchemesService = pricingSchemesService;
    }

    @ApiOperation(value = "保存报价方案")
    @PostMapping("/pricing")
    public Boolean savePricingSchemes(@RequestBody PricingReqDTO pricingReqDTO) {
        LOGGER.info("ERP系统(保存报价方案)-> PricingReqDTO:{}", JacksonUtils.writeValueAsString(pricingReqDTO));
        PricingSchemesValidator.validateSavePricingSchemes(pricingReqDTO);
        return pricingSchemesService.savePricingSchemes(pricingReqDTO);
    }

    @ApiOperation(value = "报价方案列表(含停止供应)")
    @GetMapping("/pricing/{suppliersGuid}")
    public List<PricingSchemesDTO> getPricingSchemesListIncludeDisabled(@PathVariable("suppliersGuid") String suppliersGuid) {
        LOGGER.info("ERP系统(报价方案列表(含停止供应))-> suppliersGuid:{}", suppliersGuid);
        return pricingSchemesService.getPricingSchemesList(suppliersGuid);
    }

    @ApiOperation(value = "删除物料报价信息")
    @DeleteMapping("/pricing/{guid}")
    public Boolean deletePricingSchemes(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统(删除物料报价信息)-> guid:{}", guid);
        return pricingSchemesService.deletePricingSchemes(Collections.singletonList(guid));
    }

    @ApiOperation(value = "启禁用物料报价")
    @PutMapping("/pricing/{guid}")
    public Boolean enableOrDisablePricingSchemes(@PathVariable("guid") String guid) {
        LOGGER.info("ERP系统(启禁用物料报价)-> guid:{}", guid);
        return pricingSchemesService.enableOrDisablePricingSchemes(guid);
    }

    @ApiOperation(value = "批量查询物料协议单价")
    @PostMapping("/pricing/batch")
    public List<PricingSchemesDTO> batchQueryPricingSchemesList(@RequestBody PricingSchemesQueryDTO queryDTO) {
        LOGGER.info("ERP系统(批量查询物料协议单价)-> PricingSchemesQueryDTO:{}", JacksonUtils.writeValueAsString(queryDTO));
        return pricingSchemesService.batchQueryPricingSchemesList(queryDTO);
    }

}
