package com.holderzone.erp.controller;

import com.holderzone.erp.service.InventoryService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.CreateInventoryReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.InventoryOverviewReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.InventoryDetailRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.InventoryManageRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2019/10/21 下午 15:53
 * @description
 */

@Slf4j
@Api(tags = "盘点管理")
@RestController
@RequestMapping("/inventory")
public class InventoryController {

    @Autowired
    private InventoryService inventoryService;

    @ApiOperation("新建盘点单")
    @PostMapping("/create_inventory")
    public String createInventory(@RequestBody CreateInventoryReqDTO createInventoryReqDTO) {
        log.info("新建盘点单入参：{}", JacksonUtils.writeValueAsString(createInventoryReqDTO));
        return inventoryService.createInventory(createInventoryReqDTO);
    }

    @ApiOperation("查询盘点单")
    @PostMapping("/query_inventory_detail")
    public InventoryDetailRespDTO queryInventoryDetail(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("查询盘点单入参：{}", JacksonUtils.writeValueAsString(singleDataDTO));
        if (StringUtils.isEmpty(singleDataDTO.getData())) {
            throw new BusinessException("盘点记录Guid不得为空");
        }
        return inventoryService.queryInventoryDetail(singleDataDTO);
    }

    @ApiOperation("查询盘点单概览")
    @PostMapping("/query_inventory_overview")
    public Page<InventoryManageRespDTO> queryInventoryOverView(@RequestBody @Validated InventoryOverviewReqDTO inventoryOverviewReqDTO) {
        log.info("查询盘点单概览：{}", JacksonUtils.writeValueAsString(inventoryOverviewReqDTO));
        return inventoryService.queryInventoryOverview(inventoryOverviewReqDTO);
    }

    @ApiOperation("作废盘点单")
    @PostMapping("/invalid_inventory")
    public boolean invalidInventory(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("查询盘点单概览：{}", JacksonUtils.writeValueAsString(singleDataDTO));
        if (StringUtils.isEmpty(singleDataDTO.getData())) {
            throw new BusinessException("为找到对应的盘点单据信息");
        }
        return inventoryService.invalidInventory(singleDataDTO.getData());
    }


}
