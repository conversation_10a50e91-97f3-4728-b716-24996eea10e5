package com.holderzone.erp.controller;

import com.holderzone.erp.service.IBomService;
import com.holderzone.erp.service.IMaterialService;
import com.holderzone.erp.service.InOutDocumentService;
import com.holderzone.erp.utils.GeneratorCode;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import com.holderzone.saas.store.dto.erp.util.Add;
import com.holderzone.saas.store.dto.erp.util.Update;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/25 15:22
 */
@RestController
@RequestMapping("material")
@Api(tags = "物料信息API")
public class MaterialController {

    private static final Logger LOGGER = LoggerFactory.getLogger(MaterialController.class);

    @Autowired
    IMaterialService materialService;

    @Autowired
    IBomService bomService;

    @Autowired
    InOutDocumentService inOutDocumentService;

    @PostMapping
    @ApiOperation("添加物料信息")
    public boolean add(@RequestBody @Validated(Add.class) MaterialDTO materialDTO) {
        LOGGER.info("添加物料入参:->{}", JacksonUtils.writeValueAsString(materialDTO));
        materialService.checkNameOrCode(materialDTO);
        materialService.add(materialDTO);
        return true;
    }

    @PutMapping
    @ApiOperation("修改物料信息")
    public boolean update(@RequestBody @Validated(Update.class) MaterialDTO materialDTO) {
        LOGGER.info("修改物料入参:->{}", JacksonUtils.writeValueAsString(materialDTO));
        materialService.checkNameOrCode(materialDTO);
        materialService.update(materialDTO);
        return true;
    }

    @PutMapping("changeStatus")
    @ApiOperation("启用禁用物料信息")
    public boolean changeStatus(@RequestBody @Validated(Update.class) MaterialDTO materialDTO) {
        LOGGER.info("启用禁用物料入参:->{}", JacksonUtils.writeValueAsString(materialDTO));
        materialService.changeStatus(materialDTO);
        return true;
    }

    @PostMapping("findByCondition")
    @ApiOperation("条件分页查询物料信息")
    public Page<MaterialDTO> findByCondition(@RequestBody MaterialQueryDTO queryDTO) {
        LOGGER.info("查询物料入参:->{}", JacksonUtils.writeValueAsString(queryDTO));
        Page page = new Page(queryDTO.getCurrentPage(), queryDTO.getPageSize());
        List<MaterialDTO> byCondition = materialService.findByCondition(queryDTO, page);
        page.setData(byCondition);
        return page;
    }

    @PostMapping("findListByCondition")
    @ApiOperation("条件查询物料信息不分页")
    public List<MaterialDTO> findListByCondition(@RequestBody MaterialQueryDTO queryDTO) {
        LOGGER.info("查询物料入参:->{}", JacksonUtils.writeValueAsString(queryDTO));
        List<MaterialDTO> byCondition = materialService.findByCondition(queryDTO, null);
        return byCondition;
    }

    @GetMapping("{guid}")
    @ApiOperation("根据GUID查询物料信息")
    public MaterialDTO findByGuid(@PathVariable("guid") String guid) {
        LOGGER.info("根据GUID查询物料入参:->{}", guid);
        return materialService.findByGuid(guid);
    }

    @PostMapping("findByGuidList")
    @ApiOperation("根据物料GUID列表查询物料信息")
    public List<MaterialDTO> findList(@RequestBody MaterialListQuery materialListQuery) {
        LOGGER.info("根据GUID列表查询物料入参:->{}", JacksonUtils.writeValueAsString(materialListQuery));
        return materialService.findAllList(materialListQuery.getStoreGuid(), materialListQuery.getMaterialGuidList());
    }

    @GetMapping("countBom/{materialGuid}")
    @ApiOperation("根据GUID查询物料配置的bom数量")
    public Long countMaterialBom(@PathVariable("materialGuid") String materialGuid) {
        return bomService.countBomByMaterial(materialGuid);
    }

    @DeleteMapping("{guid}")
    @ApiOperation("根据GUID删除物料信息")
    public Boolean delete(@PathVariable("guid") String guid) {
        LOGGER.info("根据GUID删除物料入参:->{}", guid);
        if (inOutDocumentService.selectInOutDocumentCountByMaterial(guid)) {
            materialService.delete(guid);
            return true;
        }
        throw new BusinessException("物料绑定了出库或入库单,不能删除");
    }

    @PostMapping("batch")
    @ApiOperation("批量添加物料信息")
    public Boolean batchAdd(@RequestBody List<MaterialDTO> materialDTOList) {
        LOGGER.info("批量添加物料入参:->{}", JacksonUtils.writeValueAsString(materialDTOList));
        //填充code,GUID
        materialService.fillCodeAndGuid(materialDTOList);
        //检查name是否合法
        materialService.batchCheckName(materialDTOList);
        try {
            //检查code
            materialService.batchCheckCode(materialDTOList);
        } catch (BusinessException businessException) {
            throw new BusinessException("自动生成物料code失败");
        }
        materialService.addBatch(materialDTOList);
        return true;
    }

    @GetMapping("generatorCode")
    @ApiOperation("生成物料code")
    public String generatorCode() {
        String code = String.valueOf(GeneratorCode.generatorCode());
        MaterialDTO materialDTO = new MaterialDTO();
        materialDTO.setCode(code);
        int count = 0;
        do {
            count++;
            try {
                materialService.checkNameOrCode(materialDTO);
                return code;
            } catch (BusinessException exception) {
                LOGGER.warn(exception.getMessage());
            }
        } while (count < 100);
        throw new BusinessException("获取编码失败");
    }


    @PostMapping("materialConsumeSum")
    @ApiOperation("物料耗用汇总")
    public Page<MaterialConsumeRespDTO> materialConsumeSum(@RequestBody MaterialConsumeReqDTO materialConsumeReqDTO) {
        LOGGER.info("物料耗用汇总入参:->{}", JacksonUtils.writeValueAsString(materialConsumeReqDTO));
        return materialService.materialConsumeSum(materialConsumeReqDTO);
    }

}
