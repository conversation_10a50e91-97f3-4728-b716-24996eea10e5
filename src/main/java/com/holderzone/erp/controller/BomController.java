package com.holderzone.erp.controller;

import com.holderzone.erp.service.IBomService;
import com.holderzone.erp.service.feign.ItemFeignService;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.erp.GoodsBomConfigDTO;
import com.holderzone.saas.store.dto.erp.GoodsBomDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Bom配置相关
 *
 * <AUTHOR>
 * @date 2019/04/29 15:21
 */
@RestController
@RequestMapping("bom")
@Api(tags = "bom配置相关Api")
public class BomController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BomController.class);
    @Autowired
    IBomService iBomService;

    @Autowired
    ItemFeignService itemFeignService;

    @PostMapping
    @ApiOperation("添加bom配置")
    public Boolean add(@RequestBody GoodsBomConfigDTO goodsBomDTO) {
        LOGGER.info("bom配置入参:->{}", JacksonUtils.writeValueAsString(goodsBomDTO));
        iBomService.add(goodsBomDTO);
        return true;
    }

    @GetMapping("{goodsGuid}/{goodsSku}")
    @ApiOperation("查询商品bom配置")
    public List<GoodsBomDTO> findBomByGoods(@PathVariable("goodsGuid") String goodsGuid, @PathVariable("goodsSku") String goodsSku) {
        LOGGER.info("查询商品bom配置入参:goodsGuid={},goodsSku={}", goodsGuid,goodsSku);
        return iBomService.findBomByGoods(goodsGuid, goodsSku);
    }

    @PostMapping("findGoodsBomCount")
    @ApiOperation("查询商品bom配置的种类数量")
    public List<GoodsBomDTO> findGoodsList(@RequestBody List<String> goodsSkuList) {
        LOGGER.info("查询商品bom配置的种类数量入参:->{}", JacksonUtils.writeValueAsString(goodsSkuList));
        if (goodsSkuList==null||goodsSkuList.isEmpty()){
            return Collections.emptyList();
        }
        return iBomService.countBomTypeBySkuList(goodsSkuList);
    }
}
