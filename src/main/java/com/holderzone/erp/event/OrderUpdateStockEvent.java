package com.holderzone.erp.event;

import com.holderzone.erp.entity.bo.OrderSkuBO;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2019/05/17 下午 16:43
 * @description
 */
public class OrderUpdateStockEvent extends ApplicationEvent {

    private OrderSkuBO orderSkuBO;

    public OrderUpdateStockEvent(Object source) {
        super(source);
    }

    public OrderUpdateStockEvent(Object source, OrderSkuBO orderSkuBO) {
        super(source);
        this.orderSkuBO = orderSkuBO;
    }

    public OrderSkuBO getOrderSkuBO() {
        return orderSkuBO;
    }

    public void setOrderSkuBO(OrderSkuBO orderSkuBO) {
        this.orderSkuBO = orderSkuBO;
    }
}
