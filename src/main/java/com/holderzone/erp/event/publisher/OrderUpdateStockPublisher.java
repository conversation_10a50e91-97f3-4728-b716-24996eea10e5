package com.holderzone.erp.event.publisher;

import com.holderzone.erp.entity.bo.OrderSkuBO;
import com.holderzone.erp.event.OrderUpdateStockEvent;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/05/17 下午 16:51
 * @description
 */
@EnableAsync
@Component
public class OrderUpdateStockPublisher implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public void publish(OrderSkuBO orderSkuBO) {
        applicationContext.publishEvent(new OrderUpdateStockEvent(this, orderSkuBO));
    }
}
