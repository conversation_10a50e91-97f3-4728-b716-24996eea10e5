package com.holderzone.erp.event.consumer;

import com.holderzone.erp.dao.InOutDocumentRedoMapper;
import com.holderzone.erp.entity.bo.*;
import com.holderzone.erp.entity.domain.InOutDocumentRedoDO;
import com.holderzone.erp.event.OrderUpdateStockEvent;
import com.holderzone.erp.mapperstruct.InOutDocumentTransform;
import com.holderzone.erp.service.IBomService;
import com.holderzone.erp.service.InOutDocumentService;
import com.holderzone.erp.service.WarehouseService;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.erp.InOutDocumentAddOrUpdateDTO;
import com.holderzone.saas.store.dto.erp.WarehouseDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

import static com.holderzone.erp.entity.enumeration.DocumentInOutTypeEnum.IN_DOCUMENT;
import static com.holderzone.erp.entity.enumeration.DocumentInOutTypeEnum.OUT_DOCUMENT;
import static com.holderzone.erp.entity.enumeration.DocumentTypeEnum.IN_RECOBERY_ORDER;
import static com.holderzone.erp.entity.enumeration.DocumentTypeEnum.OUT_PURCHASE;
import static com.holderzone.erp.utils.DateUtil.getCurrentDate;

/**
 * <AUTHOR>
 * @date 2019/05/17 下午 16:55
 * @description
 */
@Component
public class OrderUpdateStockConsumer extends AbstractOrderUpdateStockStrategy implements ApplicationListener<OrderUpdateStockEvent> {

    private static final Logger log = LoggerFactory.getLogger(OrderUpdateStockConsumer.class);

    private InOutDocumentTransform transform = InOutDocumentTransform.INSTANCE;

    @Autowired
    private InOutDocumentService inOutDocumentService;

    @Autowired
    private InOutDocumentRedoMapper redoMapper;

    @Autowired
    private IBomService bomService;

    @Autowired
    private WarehouseService warehouseService;

    @Async(value = "orderUpdateStockConsumerExecutor")
    @Override
    public void onApplicationEvent(OrderUpdateStockEvent event) {
        OrderSkuBO orderSkuBO = event.getOrderSkuBO();
        EnterpriseIdentifier.setEnterpriseGuid(orderSkuBO.getEnterpriseGuid());

        List<WarehouseDTO> warehouseList = warehouseService.getWarehouseByStoreGuid(orderSkuBO.getStoreGuid());
            if (warehouseList.isEmpty()){
            log.error("该门店下没有仓库: storeGuid:{}", orderSkuBO.getStoreGuid());
            return;
        }
        WarehouseDTO warehouseDTO = warehouseList.get(0);
        if (warehouseService.isLock(warehouseDTO.getGuid())){
            log.error("仓库已被锁盘,不能扣减库存,warehouseGuid:{}", warehouseDTO.getGuid());
            return;
        }
        try {
            InOutDocumentBO inOutDocumentBO = getBaseInOutDocument(orderSkuBO, warehouseDTO);
            InOutDocumentBomQueryBO queryBO = transform.orderSkuBoToInOutDocumentQueryBo(orderSkuBO);
            List<InOutDocumentBomBO> inOutDocumentBomBOList = bomService.parseGoodsByBom(queryBO);
            if (inOutDocumentBomBOList.isEmpty()) {
                log.error("无法扣减库存，所有菜品都没有配置bom,入参：{}", JacksonUtils.writeValueAsString(orderSkuBO));
                return;
            }
            List<InOutDocumentDetailBO> detailList = inOutDocumentService.completeMaterialInfo(orderSkuBO.getStoreGuid(), warehouseDTO.getGuid(), inOutDocumentBomBOList);
            inOutDocumentBO.setDetailList(detailList);
            OrderUpdateStockStrategyEntity strategyEntity = new OrderUpdateStockStrategyEntity(inOutDocumentBO);
            execute(strategyEntity);
        } finally {
            EnterpriseIdentifier.remove();
        }

    }

    /**
     * 下订单减少库存时的基础InOutDocumentBO实体
     *
     * @param orderSkuBO
     * @return
     */
    private InOutDocumentBO getBaseInOutDocument(OrderSkuBO orderSkuBO, WarehouseDTO warehouseDTO) {
        String warehouseGuid = warehouseDTO.getGuid();
        String warehouseName = warehouseDTO.getName();

        InOutDocumentBO inOutDocumentBO = new InOutDocumentBO();
        inOutDocumentBO.setWarehouseGuid(warehouseGuid);
        inOutDocumentBO.setWarehouseName(warehouseName);
        inOutDocumentBO.setDocumentDate(getCurrentDate());
        //反结账入库  和  销售出库
        inOutDocumentBO.setType(orderSkuBO.getOut()?OUT_PURCHASE.getType(): IN_RECOBERY_ORDER.getType());
        inOutDocumentBO.setInOutType(orderSkuBO.getOut()?OUT_DOCUMENT.getType():IN_DOCUMENT.getType());
//        inOutDocumentBO.setType(OUT_PURCHASE.getType());
//        inOutDocumentBO.setInOutType(OUT_DOCUMENT.getType());
        inOutDocumentBO.setTotalAmount(BigDecimal.ZERO);
        inOutDocumentBO.setShouldPayAmount(BigDecimal.ZERO);
        inOutDocumentBO.setStoreGuid(orderSkuBO.getStoreGuid());
        inOutDocumentBO.setOperatorGuid(orderSkuBO.getOperatorGuid());
        inOutDocumentBO.setOperatorName(orderSkuBO.getOperatorName());
        inOutDocumentBO.setCreateStaffGuid(orderSkuBO.getOperatorGuid());
        inOutDocumentBO.setCreateStaffName(orderSkuBO.getOperatorName());

        return inOutDocumentBO;
    }

    @Override
    public void execute(OrderUpdateStockStrategyEntity strategyEntity) {
        InOutDocumentBO inOutDocumentBO = strategyEntity.getInOutDocumentBO();
        for (int i = 0; i < RETRY_COUNT + 1; i++) {
            try {
                InOutDocumentAddOrUpdateDTO addOrUpdateDTO = transform.inOutDocumentBoToAddorUpdateDto(inOutDocumentBO);
                inOutDocumentService.insertAndSubmitInOutDocument(addOrUpdateDTO);
                break;
            } catch (Exception e) {
                if (i < RETRY_COUNT) {
                    log.error("订单更新库存失败，开始第" + (i + 1) + "次重试", e);
                } else {
                    log.error("订单更新库存失败，重试失败，进入处理失败流程", e);
                    handleFailure(strategyEntity);
                }
            }
        }

    }

    @Override
    protected void handleFailure(OrderUpdateStockStrategyEntity strategyEntity) {
        InOutDocumentRedoDO inOutDocumentRedoDO = new InOutDocumentRedoDO();
        inOutDocumentRedoDO.setData(JacksonUtils.writeValueAsString(strategyEntity.getInOutDocumentBO()));
        inOutDocumentRedoDO.setGuid(IDUtils.nextId());
        redoMapper.insertInOutDocumentRedo(inOutDocumentRedoDO);
    }
}
