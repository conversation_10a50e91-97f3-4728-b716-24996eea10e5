package com.holderzone.erp.event.consumer;

import com.holderzone.erp.entity.bo.InOutDocumentBO;

/**
 * <AUTHOR>
 * @date 2019/05/18 下午 16:48
 * @description
 */
public class OrderUpdateStockStrategyEntity {

    private InOutDocumentBO inOutDocumentBO;

    public OrderUpdateStockStrategyEntity() {
    }

    public OrderUpdateStockStrategyEntity( InOutDocumentBO inOutDocumentBO) {
        this.inOutDocumentBO = inOutDocumentBO;
    }

    public InOutDocumentBO getInOutDocumentBO() {
        return inOutDocumentBO;
    }

    public void setInOutDocumentBO(InOutDocumentBO inOutDocumentBO) {
        this.inOutDocumentBO = inOutDocumentBO;
    }
}
