package com.holderzone.erp.event.consumer;

import com.holderzone.erp.service.*;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.saas.store.dto.erp.*;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Component
@RocketListenerHandler(
        topic = "enterprise-init-data-topic",
        tags = {
                "enterprise-init-data-tag"
        },
        consumerGroup = "warehouse-init-data-group"
)
public class AkeyDeploymentConsumer extends AbstractRocketMqConsumer<RocketMqTopic, UnMessage> {

    @Autowired
    private IMaterialUnitService iMaterialUnitService;

    @Autowired
    private IMaterialService materialService;

    @Autowired
    private IMaterialCategoryService materialCategoryService;

    @Autowired
    private SuppliersService suppliersService;

    @Override
    @Transactional
    public boolean consumeMsg(UnMessage unMessage, MessageExt messageExt) {
        String tags = messageExt.getTags();
        switch (tags) {
            case "enterprise-init-data-tag":
                log.info("一键部署，erp 收到初始化信息：{}", unMessage.getEnterpriseGuid());

                EnterpriseIdentifier.setEnterpriseGuid(unMessage.getEnterpriseGuid());
                UserContextUtils.putErp(unMessage.getEnterpriseGuid());

                // 物料分类
                MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
                materialCategoryDTO.setName("肉类");
                materialCategoryDTO.setEnterpriseGuid(unMessage.getEnterpriseGuid());
                materialCategoryService.add(materialCategoryDTO);
                log.info("创建物料分类成功");

                // 默认物料
                MaterialDTO materialDTO = new MaterialDTO();
                materialDTO.setName("鸡肉");
                List<MaterialUnitDTO> list = iMaterialUnitService.list();
                materialDTO.setUnit(list.stream().filter(materialUnitDTO -> materialUnitDTO.getName().equals("kg")).findFirst().get().getGuid());
                materialDTO.setUnitName("kg");
                materialDTO.setType("1");
                materialDTO.setCode(String.valueOf((int) ((Math.random() * 9 + 1) * 100000)));
                materialDTO.setEnterpriseGuid(unMessage.getEnterpriseGuid());
                materialService.add(materialDTO);
                log.info("创建默认物料成功");

                // 供应商
                SuppliersReqDTO suppliersReqDTO = new SuppliersReqDTO();
                suppliersReqDTO.setName("默认供应商");
                suppliersReqDTO.setEnabled(1);
                suppliersReqDTO.setForeignKey(unMessage.getEnterpriseGuid());
                suppliersReqDTO.setEnterpriseGuid(unMessage.getEnterpriseGuid());
                suppliersService.createSuppliers(suppliersReqDTO);
                log.info("添加默认供应商成功");

                break;
            default:
                log.error("unknown mq tag : {}, message：{}", messageExt.getTags(), unMessage.getEnterpriseGuid());
                break;
        }
        return true;
    }
}
