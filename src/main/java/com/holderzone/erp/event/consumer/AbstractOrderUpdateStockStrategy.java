package com.holderzone.erp.event.consumer;

/**
 * <AUTHOR>
 * @date 2019/05/17 下午 17:01
 * @description
 */
public abstract class AbstractOrderUpdateStockStrategy {

    protected static final Integer RETRY_COUNT = 3;

    /**
     * 执行更新库存操作
     *
     * @param strategyEntity
     */
    public abstract void execute(OrderUpdateStockStrategyEntity strategyEntity);

    /**
     * 处理失败逻辑
     *
     * @param strategyEntity
     */
    protected abstract void handleFailure(OrderUpdateStockStrategyEntity strategyEntity);
}
