package com.holderzone.erp.dao;

import com.holderzone.erp.entity.domain.MaterialUnitDO;
import com.holderzone.erp.entity.domain.MaterialUnitDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MaterialUnitDOMapper {
    long countByExample(MaterialUnitDOExample example);

    int deleteByExample(MaterialUnitDOExample example);

    int insert(MaterialUnitDO record);

    int insertSelective(MaterialUnitDO record);

    List<MaterialUnitDO> selectByExample(MaterialUnitDOExample example);

    int updateByExampleSelective(@Param("record") MaterialUnitDO record, @Param("example") MaterialUnitDOExample example);

    int updateByExample(@Param("record") MaterialUnitDO record, @Param("example") MaterialUnitDOExample example);
}