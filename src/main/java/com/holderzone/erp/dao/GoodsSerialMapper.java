package com.holderzone.erp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.erp.entity.domain.GoodsSerialDO;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.saas.store.dto.erp.erpretail.req.QueryGoodsSerialReqDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

@Component
public interface GoodsSerialMapper extends BaseMapper<GoodsSerialDO> {
    IPage<GoodsSerialDO> queryGoodsSerial(PageAdapter page, @Param("dto") QueryGoodsSerialReqDTO queryGoodsSerialReqDTO);
}
