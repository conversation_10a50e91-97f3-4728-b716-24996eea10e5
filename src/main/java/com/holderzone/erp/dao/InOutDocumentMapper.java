package com.holderzone.erp.dao;

import com.holderzone.erp.entity.domain.InOutContactDocumentQuery;
import com.holderzone.erp.entity.domain.InOutDocumentDO;
import com.holderzone.erp.entity.domain.InOutDocumentQuery;
import com.holderzone.erp.entity.domain.SuppliersReconciliationQueryDO;
import com.holderzone.saas.store.dto.erp.DocumentDetailListQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/29 上午 11:15
 * @description
 */
public interface InOutDocumentMapper {

    /**
     * 添加出入库单据
     *
     * @param inOutDocumentDO
     */
    void insertInOutDocument(InOutDocumentDO inOutDocumentDO);

    /**
     * 删除出入库单据和对应的明细
     *
     * @param inOutDocumentGuid
     */
    void deleteInOutDocumentAndDetail(String inOutDocumentGuid);

    /**
     * 查询此入库单据是否存在
     *
     * @param contactDocumentGuid
     * @return
     */
    int inDocumentCount(String contactDocumentGuid);


    /**
     * 查询出入库单据所在仓库
     *
     * @param documentGuid
     * @return
     */
    String selectWarehouseGuid(String documentGuid);

    /**
     * 查询单据的状态和出入库类型
     *
     * @param documentGuid
     * @return
     */
    InOutDocumentDO selectDocumentStatusAndInOutType(String documentGuid);


    /**
     * 提交出入库单
     *
     * @param documentGuid
     */
    void submitInOutDocument(String documentGuid);

    /**
     * 查询单据的提交状态
     *
     * @param contactDocumentGuid
     * @return
     */
    InOutDocumentDO selectInOutDocumentStatus(String contactDocumentGuid);

    /**
     * 查询单据的类型
     *
     * @param documentGuid
     * @return
     */
    InOutDocumentDO selectDocumentType(String documentGuid);

    /**
     * 查询关联单据的单据号的集合
     *
     * @param query
     * @return
     */
    List<String> selectDocumentGuidList(InOutContactDocumentQuery query);

    /**
     * 查询出入库单据
     *
     * @param documentGuid
     * @return
     */
    InOutDocumentDO selectDocument(String documentGuid);

    /**
     * 查询出入库单据及其明细
     *
     * @param documentGuid
     * @return
     */
    InOutDocumentDO selectDocumentAndDetail(String documentGuid);

    /**
     * 查询关联指定文档的所有文档Guid
     *
     * @param documentGuid
     * @return
     */
    List<String> selectContactDocumentGuidList(String documentGuid);

    /**
     * 分页查询出入库单据
     *
     * @param inOutDocumentQuery
     * @return
     */
    List<InOutDocumentDO> selectDocumentListForPage(InOutDocumentQuery inOutDocumentQuery);

    /**
     * 根据条件查询出入库单据数量
     *
     * @param inOutDocumentQuery
     * @return
     */
    long selectDocumentCount(InOutDocumentQuery inOutDocumentQuery);

    /**
     * 供应商对账表总数
     */
    long selectSuppliersReconciliationTotal(SuppliersReconciliationQueryDO queryDO);

    /**
     * 供应商对账表(分页)
     */
    List<InOutDocumentDO> selectSuppliersReconciliationList(SuppliersReconciliationQueryDO queryDO);

    /**
     * 结算总金额
     */
    BigDecimal selectSuppliersReconciliationTotalAmount(SuppliersReconciliationQueryDO queryDO);

    /**
     * 结算
     */
    void settleSuppliersReconciliation(List<String> list);

    /**
     * 查询单据的门店guid和仓库guid
     *
     * @param documentGuid
     * @return
     */
    String selectStoreGuid(String documentGuid);

    /**
     * 查询对应仓库是否有出入库单
     *
     * @param warehouseGuid
     * @return
     */
    int selectCountByWarehouseGuid(String warehouseGuid);

    /**
     * 查询对应供应商是否有出入库单
     *
     * @param supplierGuid
     * @return
     */
    int selectCountBySupplierGuid(String supplierGuid);

    /**
     * 查询单据的仓库guid
     *
     * @param documentGuid
     * @return
     */
    String selectWarehouseGuidByDocumentGuid(String documentGuid);

    /**
     * 为入库单据查询关联单据
     *
     * @param documentGuid
     * @return
     */
    List<String> selectContactDocumentGuidListForInDocument(String documentGuid);

    /**
     * 查询单据的门店和仓库guid
     *
     * @param documentGuid
     * @return
     */
    InOutDocumentDO selectStoreGuidAndWarehouseGuid(String documentGuid);

    List<InOutDocumentDO> queryDocumentDetailList(@Param("queryDTO") DocumentDetailListQueryDTO queryDTO);

}
