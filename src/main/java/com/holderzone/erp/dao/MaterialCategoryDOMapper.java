package com.holderzone.erp.dao;

import com.holderzone.erp.entity.domain.CategoryDO;
import com.holderzone.erp.entity.domain.MaterialCategoryDO;
import com.holderzone.erp.entity.domain.MaterialCategoryDOExample;
import com.holderzone.saas.store.dto.erp.CategoryDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MaterialCategoryDOMapper {
    long countByExample(MaterialCategoryDOExample example);

    int deleteByExample(MaterialCategoryDOExample example);

    int insertSelective(MaterialCategoryDO record);

    List<MaterialCategoryDO> selectByExample(MaterialCategoryDOExample example);

    int updateByExampleSelective(@Param("record") MaterialCategoryDO record, @Param("example") MaterialCategoryDOExample example);

    List<CategoryDO> getMaterialCategoryListAll(@Param("storeGuid") String storeGuid, @Param("searchName") String searchName);

    List<CategoryDTO> listByGuidList(@Param("dto") SingleListDTO dto);
}