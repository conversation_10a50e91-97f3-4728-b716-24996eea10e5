package com.holderzone.erp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.erp.entity.domain.InventoryDO;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.saas.store.dto.erp.erpretail.req.InventoryOverviewReqDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

@Component
public interface InventoryMapper extends BaseMapper<InventoryDO> {
    IPage<InventoryDO> queryInventoryOverview(PageAdapter<InventoryDO> pageAdapter, @Param("dto") InventoryOverviewReqDTO inventoryOverviewReqDTO);
}
