package com.holderzone.erp.dao;

import com.holderzone.erp.entity.domain.CategoryDO;
import com.holderzone.erp.entity.domain.SuppliersDO;
import com.holderzone.erp.entity.domain.SuppliersQueryDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @className SuppliersMapper
 * @date 2019-04-26 11:16:34
 * @description
 * @program holder-saas-store-erp
 */
@Repository
public interface SuppliersMapper {

    void createSuppliers(SuppliersDO suppliersDO);

    void updateSuppliers(SuppliersDO suppliersDO);

    SuppliersDO getSuppliersByGuid(@Param("guid") String guid);

    void enableOrDisableSuppliers(@Param("guid") String guid);

    void deleteSuppliers(@Param("guid") String guid);

    List<SuppliersDO> getSuppliersList(SuppliersQueryDO queryDO);

    long getSuppliersListTotal(SuppliersQueryDO queryDO);

    int verifyNameRepeat(@Param("name") String name, @Param("foreignKey") String foreignKey, @Param("guid") String guid);

    List<CategoryDO> getSuppliersMaterialListAll(@Param("suppliersGuid") String suppliersGuid, @Param("searchName") String searchName);

    /**
     * 查询供应商状态
     *
     * @param supplierGuid
     * @return
     */
    SuppliersDO selectSupplierStatus(String supplierGuid);
}
