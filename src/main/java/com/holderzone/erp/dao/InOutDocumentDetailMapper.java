package com.holderzone.erp.dao;

import com.holderzone.erp.entity.domain.InOutDocumentDetailDO;
import com.holderzone.erp.entity.domain.InOutDocumentFlowDetailDO;
import com.holderzone.erp.entity.domain.InOutDocumentFlowDetailQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/29 上午 11:18
 * @description
 */
public interface InOutDocumentDetailMapper {

    /**
     * 批量添加出入库物料
     *
     * @param detailDOList
     */
    void insertInOutDocumentDetailList(List<InOutDocumentDetailDO> detailDOList);

    /**
     * 查询单据物料的入库和退货数量
     *
     * @param contactDocumentGuid
     * @return
     */
    List<InOutDocumentDetailDO> selectDocumentInAndReturnCount(String contactDocumentGuid);

    /**
     * 查询出入库单据物料明细
     *
     * @param documentGuid
     * @return
     */
    List<InOutDocumentDetailDO> selectInOutDocumentDetailList(String documentGuid);

    /**
     * 查询入库单据物料数量
     *
     * @param documentGuid
     * @return
     */
    List<InOutDocumentDetailDO> selectInDocumentMaterialCount(String documentGuid);

    /**
     * 查询出库单据物料数量
     *
     * @param documentGuid
     * @return
     */
    List<InOutDocumentDetailDO> selectOutDocumentMaterialCount(String documentGuid);

    /**
     * 退货时，更新入库单的退货数量
     *
     * @param contactDocumentGuid
     * @param detailList
     */
    void updateInDocumentDetailReturnCount(@Param("contactDocumentGuid") String contactDocumentGuid, @Param("detailList") List<InOutDocumentDetailDO> detailList);

    /**
     * 查询出入库单流水明细
     *
     * @param query
     * @return
     */
    List<InOutDocumentFlowDetailDO> selectInOutDocumentFlowDetailList(InOutDocumentFlowDetailQuery query);

    /**
     * 查询出入库单据流水数量
     *
     * @param query
     * @return
     */
    long selectInOutDocumentFlowDetailCount(InOutDocumentFlowDetailQuery query);

    /**
     * 根据物料查询出入库单数量
     *
     * @param materialGuid
     * @return
     */
    int selectInOutDocumentCountByMaterial(String materialGuid);

}
