package com.holderzone.erp.dao;

import com.holderzone.erp.entity.domain.CheckoutDocumentDO;
import com.holderzone.erp.entity.domain.CheckoutDocumentQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/29 上午 11:18
 * @description
 */
public interface CheckoutDocumentMapper {

    /**
     * 添加盘点单
     *
     * @param checkoutDocumentDO
     */
    void insertCheckoutDocument(CheckoutDocumentDO checkoutDocumentDO);

    /**
     * 删除盘点单
     *
     * @param documentGuid
     */
    void deleteCheckoutDocumentAndDetail(String documentGuid);

    /**
     * 查询盘点单及其明细
     *
     * @param documentGuid
     * @return
     */
    CheckoutDocumentDO selectDocumentAndDetail(String documentGuid);

    /**
     * 查询单据的数量
     *
     * @param documentGuid
     * @return
     */
    int selectDocumentCount(String documentGuid);

    /**
     * 查询单据的状态
     *
     * @param documentGuid
     * @return
     */
    CheckoutDocumentDO selectDocumentStatus(String documentGuid);

    /**
     * 修改盘点单的状态为已提交
     *
     * @param guid
     */
    void submitCheckoutDocument(String guid);

    /**
     * 根据条件查询盘点单
     *
     * @param query
     * @return
     */
    List<CheckoutDocumentDO> selectCheckoutDocumentList(CheckoutDocumentQuery query);

    /**
     * 根据条件查询盘点单的数量
     *
     * @param query
     * @return
     */
    long selectDocumentListCount(CheckoutDocumentQuery query);


    /**
     * 根据guid查询盘点单的仓库和制单人
     *
     * @param guid
     * @return
     */
    CheckoutDocumentDO selectDocumentWarehouseAndStoreAndCreateStaff(String guid);

    /**
     * 根据单据guid查询单据
     *
     * @param documentGuid
     * @return
     */
    CheckoutDocumentDO selectDocument(String documentGuid);

    /**
     * 查询单据的仓库guid
     *
     * @param documentGuid
     * @return
     */
    String selectDocumentWarehouseGuid(String documentGuid);

    /**
     * 查询单据的门店guid
     *
     * @param documentGuid
     * @return
     */
    String selectDocumentStoreGuid(String documentGuid);

    /**
     * 查询单据的门店guid和仓库guid
     *
     * @param documentGuid
     * @return
     */
    CheckoutDocumentDO selectDocumentStoreGuidAndWarehouseGuid(String documentGuid);

    /**
     * 查询盘点单数量(根据仓库)
     * @param warehouseGuid
     * @return
     */
    int selectCountByWarehouseGuid(String warehouseGuid);
}
