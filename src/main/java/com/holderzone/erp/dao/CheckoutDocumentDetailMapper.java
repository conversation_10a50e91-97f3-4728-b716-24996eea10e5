package com.holderzone.erp.dao;

import com.holderzone.erp.entity.domain.CheckoutDocumentDetailDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/29 上午 11:21
 * @description
 */
public interface CheckoutDocumentDetailMapper {

    /**
     * 添加盘点单明细
     *
     * @param detailDOList
     */
    void insertCheckoutDocumentDetailList(List<CheckoutDocumentDetailDO> detailDOList);

    /**
     * 查询单据的物料明细
     *
     * @param documentGuid
     * @return
     */
    List<CheckoutDocumentDetailDO> selectDocumentDetailList(String documentGuid);
}
