package com.holderzone.erp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.erp.entity.domain.GoodsDO;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.saas.store.dto.erp.erpretail.req.QueryGoodsSumInfoReqDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

@Component
public interface GoodsMapper extends BaseMapper<GoodsDO> {
    IPage<GoodsDO> queryGoodsSumInfo(PageAdapter page, @Param("dto") QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO);
}
