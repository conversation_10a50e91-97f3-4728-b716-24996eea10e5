package com.holderzone.erp.dao;

import com.holderzone.erp.entity.domain.WarehouseDO;
import com.holderzone.erp.entity.domain.WarehouseQueryDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @className WarehouseMapper
 * @date 2019-04-24 14:49:49
 * @description
 * @program holder-saas-store-erp
 */
@Repository
public interface WarehouseMapper {

    void createWarehouse(WarehouseDO warehouseDO);

    WarehouseDO getWarehouseByGuid(@Param("guid") String guid);

    void updateWarehouse(WarehouseDO warehouseDO);

    List<WarehouseDO> getWarehouseList(WarehouseQueryDO queryDO);

    List<WarehouseDO> getWarehouseListByName(WarehouseQueryDO queryDO);

    void enableOrDisableWarehouse(@Param("guid") String guid);

    void lockOrUnlockWarehouse(@Param("guid") String guid);

    void deleteWarehouse(@Param("guid") String guid);

    long getWarehouseListTotal(WarehouseQueryDO queryDO);

    String getMaximumCode();

    int checkCodeRepeat(@Param("code") String code);

    List<WarehouseDO> getWarehouseByStoreGuid(@Param("storeGuid") String storeGuid);

    int checkNameRepeat(@Param("name") String name, @Param("guid") String guid);

    void updateStoreWarehouse(@Param("name") String name, @Param("guid") String guid);
}
