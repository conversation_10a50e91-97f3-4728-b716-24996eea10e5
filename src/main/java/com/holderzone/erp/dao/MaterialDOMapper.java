package com.holderzone.erp.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.erp.entity.domain.MaterialDO;
import com.holderzone.erp.entity.domain.MaterialDOExample;
import com.holderzone.erp.entity.domain.read.MaterialConsumeQuery;
import com.holderzone.erp.entity.domain.read.MaterialConsumeReadDO;
import com.holderzone.erp.entity.domain.read.MaterialDocDetailReadDO;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.MaterialConsumeReqDTO;
import com.holderzone.saas.store.dto.erp.MaterialQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MaterialDOMapper {
    long countByExample(MaterialDOExample example);

    int deleteByExample(MaterialDOExample example);

    int insertSelective(MaterialDO record);

    List<MaterialDO> selectByExample(MaterialDOExample example);

    int updateByExampleSelective(@Param("record") MaterialDO record, @Param("example") MaterialDOExample example);

    List<MaterialDO> findByCondition(@Param("record") MaterialQueryDTO materialQueryDTO, Page page);

    List<MaterialDO> findByWarehouseOrGuidList(@Param("warehouseGuid") String warehouseGuid, @Param("storeGuid") String storeGuid, @Param("materialGuidList") List<String> materialGuidList);

    List<MaterialDO> findAllByWarehouseOrGuidList(@Param("storeGuid") String storeGuid, @Param("materialGuidList") List<String> materialGuidList);

    int addBatch(List<MaterialDO> materialDOS);

    IPage<MaterialConsumeReadDO> materialConsumeSum(PageAdapter pageAdapter, @Param("dto") MaterialConsumeReqDTO materialConsumeReqDTO);

    List<MaterialDocDetailReadDO> queryDetailList(@Param("query") MaterialConsumeQuery materialConsumeQuery);

    List<MaterialDO> findByWarehouseOrCodeList(@Param("warehouseGuid") String warehouseGuid, @Param("storeGuid") String storeGuid, @Param("materialCodeList") List<String> materialCodeList);

    List<MaterialDO> findAllByWarehouseOrCodeList(@Param("storeGuid") String storeGuid, @Param("materialCodeList") List<String> materialCodeList);
}