package com.holderzone.erp.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.erp.entity.domain.RepertoryDO;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.saas.store.dto.erp.erpretail.req.QueryRepertoryManageReqDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

@Component
public interface RepertoryMapper extends BaseMapper<RepertoryDO> {
    IPage<RepertoryDO> queryInOutRepertoryList(PageAdapter adapter, @Param("dto") QueryRepertoryManageReqDTO queryRepertoryManageReqDTO);
}
