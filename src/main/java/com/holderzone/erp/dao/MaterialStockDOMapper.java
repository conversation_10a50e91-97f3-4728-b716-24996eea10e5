package com.holderzone.erp.dao;

import com.holderzone.erp.entity.bo.UpdateStockBO;
import com.holderzone.erp.entity.domain.InOutDocumentDO;
import com.holderzone.erp.entity.domain.MaterialDO;
import com.holderzone.erp.entity.domain.MaterialStockDO;
import com.holderzone.erp.entity.domain.MaterialStockDOExample;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.MaterialBySupplierQueryDTO;
import com.holderzone.saas.store.dto.erp.StockQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface MaterialStockDOMapper {
    long countByExample(MaterialStockDOExample example);

    int deleteByExample(MaterialStockDOExample example);

    int insert(MaterialStockDO record);

    int insertSelective(MaterialStockDO record);

    List<MaterialStockDO> selectByExample(MaterialStockDOExample example);

    int updateByExampleSelective(@Param("record") MaterialStockDO record, @Param("example") MaterialStockDOExample example);

    int updateByExample(@Param("record") MaterialStockDO record, @Param("example") MaterialStockDOExample example);

    int addStock(@Param("materialGuid") String materialGuid, @Param("stock") BigDecimal stock, @Param("warehouseGuid") String warehouseGuid);

    int reduceStock(@Param("materialGuid") String materialGuid, @Param("stock") BigDecimal stock, @Param("warehouseGuid") String warehouseGuid);

    int addStockBatch(List<UpdateStockBO> updateStockBOS);

    int insertStockBatch(List<MaterialStockDO> materialStockDOS);

    int reduceStockBatch(List<UpdateStockBO> updateStockBOList);

    /**
     * 入库单价：入库单库存单价*数量的总和除以总数量
     */
    List<MaterialDO> findStockPage(@Param("stockQueryDTO") StockQueryDTO stockQueryDTO, Page page);

    List<InOutDocumentDO> queryMaterialBySupplier(@Param("queryDTO") MaterialBySupplierQueryDTO queryDTO);

}