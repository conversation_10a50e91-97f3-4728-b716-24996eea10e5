package com.holderzone.erp.dao;

import com.holderzone.erp.entity.domain.GoodsBomDO;
import com.holderzone.erp.entity.domain.GoodsBomDOExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GoodsBomDOMapper {
    long countByExample(GoodsBomDOExample example);

    int deleteByExample(GoodsBomDOExample example);

    int insert(GoodsBomDO record);

    int insertSelective(GoodsBomDO record);

    List<GoodsBomDO> selectByExample(GoodsBomDOExample example);

    int updateByExampleSelective(@Param("record") GoodsBomDO record, @Param("example") GoodsBomDOExample example);

    int updateByExample(@Param("record") GoodsBomDO record, @Param("example") GoodsBomDOExample example);

    int insertBatch(List<GoodsBomDO> goodsBomDOS);

    List<GoodsBomDO> findGoodsBom(@Param("goodsGuid") String goodsGuid, @Param("goodsSku") String goodsSku);

    List<GoodsBomDO> countMaterialTypeBySkuList(List<String> skuList);

    List<GoodsBomDO> findBomBySku(List<String> skuList);
}