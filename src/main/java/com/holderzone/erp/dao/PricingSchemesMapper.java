package com.holderzone.erp.dao;

import com.holderzone.erp.entity.domain.PricingSchemesDO;
import com.holderzone.erp.entity.domain.PricingSchemesLogDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @className PricingSchemesMapper
 * @date 2019-04-27 10:47:17
 * @description
 * @program holder-saas-store-erp
 */
@Repository
public interface PricingSchemesMapper {


    void deletePricingSchemes(List<String> list);

    List<PricingSchemesDO> getPricingSchemesListBySuppliersGuid(@Param("suppliersGuid") String suppliersGuid);

    void savePricingSchemesLog(List<PricingSchemesLogDO> list);

    void savePricingSchemes(List<PricingSchemesDO> list);

    void enableOrDisablePricingSchemes(@Param("guid") String guid);

    List<PricingSchemesDO> batchQueryPricingSchemesList(@Param("suppliersGuid") String suppliersGuid,@Param("list") List<String> list);

    List<PricingSchemesDO> getPricingSchemesByMaterialGuid(@Param("materialGuid") String materialGuid);

    void changeMaterialUnitInPricingSchemes(List<PricingSchemesDO> list);
}
