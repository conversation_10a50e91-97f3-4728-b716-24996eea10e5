package com.holderzone.erp.entity.enumeration;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @date 2019/10/25 下午 11:39
 * @description
 */
public enum InventoryTypeEnum {

    PURCHASE_IN(1, "日盘"),

    REFOUND_IN(2, "月盘"),

    INVENTORY_PROFIT_IN(3, "周盘");

    private Integer code;

    private String des;

    InventoryTypeEnum(Integer code, String des) {
        this.code = code;
        this.des = des;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getDes() {
        return des;
    }

    public static InventoryTypeEnum ofMode(Integer code) {
        for (InventoryTypeEnum invoiceTypeEnum : InventoryTypeEnum.values()) {
            if (invoiceTypeEnum.code.equals(code)) {
                return invoiceTypeEnum;
            }
        }
        throw new BusinessException("不支持的类型，code：" + code);
    }
}
