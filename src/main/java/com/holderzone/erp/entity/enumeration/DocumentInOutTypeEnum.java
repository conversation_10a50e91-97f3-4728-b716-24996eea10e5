package com.holderzone.erp.entity.enumeration;

/**
 * <AUTHOR>
 * @date 2019/05/06 上午 11:48
 * @description
 */
public enum  DocumentInOutTypeEnum {

    /**
     * 入库
     */
    IN_DOCUMENT(0),

    /**
     * 出库
     */
    OUT_DOCUMENT(1);

    private Integer type;

    DocumentInOutTypeEnum(Integer type){
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
