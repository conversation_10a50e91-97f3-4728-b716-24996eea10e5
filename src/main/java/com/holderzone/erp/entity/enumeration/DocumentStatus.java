package com.holderzone.erp.entity.enumeration;

/**
 * <AUTHOR>
 * @date 2019/05/06 上午 11:41
 * @description
 */
public enum DocumentStatus {

    /**
     * 未提交
     */
    UN_SUBMIT(0),

    /**
     * 已提交
     */
    SUBMIT(1);

    private Integer status;

    DocumentStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
