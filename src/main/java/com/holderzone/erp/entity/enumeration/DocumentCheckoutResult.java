package com.holderzone.erp.entity.enumeration;

/**
 * <AUTHOR>
 * @date 2019/05/08 下午 18:16
 * @description
 */
public enum DocumentCheckoutResult {

    BALANCE(0),

    PROFIT(1),

    DEFICIT(2);

    private Integer result;

    DocumentCheckoutResult(Integer result){
        this.result = result;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }
}
