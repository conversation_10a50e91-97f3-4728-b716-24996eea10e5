package com.holderzone.erp.entity.enumeration;

/**
 * <AUTHOR>
 * @date 2019/04/29 下午 14:42
 * @description
 */
public enum DocumentTypeEnum {

    /**
     * 采购入库
     */
    IN_PURCHASE(0),

    /**
     * 其他入库
     */
    IN_OTHER(1),

    /**
     * 盘盈入库
     */
    IN_CHECK_PROFIT(2),

    /**
     * 反结账入库
     */
    IN_RECOBERY_ORDER(3),
    /**
     * 销售出库
     */
    OUT_PURCHASE(10),

    /**
     * 退货出库
     */
    OUT_RETURN(11),

    /**
     * 其他出库
     */
    OUT_OTHER(12),

    /**
     * 盘亏出库
     */
    OUT_CHECK_DEFICIT(13);

    private Integer type;

    private DocumentTypeEnum(Integer type){
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
