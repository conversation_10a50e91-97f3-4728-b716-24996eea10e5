package com.holderzone.erp.entity.enumeration;

import com.holderzone.framework.exception.unchecked.BusinessException;

/**
 * <AUTHOR>
 * @date 2019/10/24 下午 18:16
 * @description
 */
public enum InvoiceTypeEnum {

    PURCHASE_IN(1, "采购入库"),

    REFOUND_IN(2, "顾客退货"),

    SALE_OUT(3, "销售"),

    INVENTORY_PROFIT_IN(4, "盘盈"),

    INVENTORY_LOSSES_OUT(5, "盘亏"),

    START_PERIOD_IN(6, "期初"),

    OTHER_IN(7, "其他入库"),

    REFOUND_OUT(8, "退货出库"),

    OTHER_OUT(9, "其他出库");

    private Integer code;

    private String des;

    InvoiceTypeEnum(Integer code, String des) {
        this.code = code;
        this.des = des;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getDes() {
        return des;
    }

    public static InvoiceTypeEnum ofMode(Integer code) {
        for (InvoiceTypeEnum invoiceTypeEnum : InvoiceTypeEnum.values()) {
            if (invoiceTypeEnum.code.equals(code)) {
                return invoiceTypeEnum;
            }
        }
        throw new BusinessException("不支持的类型，code：" + code);
    }
}
