package com.holderzone.erp.entity.domain;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/05/17 下午 17:50
 * @description
 */
public class InOutDocumentRedoDO {

    private String guid;

    private String data;

    private Integer executeCount;

    private Date nextExecuteTime;

    private Boolean deleted;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public Integer getExecuteCount() {
        return executeCount;
    }

    public void setExecuteCount(Integer executeCount) {
        this.executeCount = executeCount;
    }

    public Date getNextExecuteTime() {
        return nextExecuteTime;
    }

    public void setNextExecuteTime(Date nextExecuteTime) {
        this.nextExecuteTime = nextExecuteTime;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}
