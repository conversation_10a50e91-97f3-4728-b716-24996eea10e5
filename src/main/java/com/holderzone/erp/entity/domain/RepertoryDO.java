package com.holderzone.erp.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("hse_repertory")
public class RepertoryDO {
    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 单据类型(0:采购入库，1：其他入库，2:盘盈入库单，10:销售出库, 11:退货出库, 12:其他出库，13:盘亏出库单)
     */
    private int invoiceType;

    /**
     * 单据编号
     */
    private String invoiceNo;

    /**
     * 出入库类型，0：入库，1：出库
     */
    private Integer inOut;

    /**
     * 经办人Guid
     */
    private String operatorGuid;

    /**
     * 经办人名称
     */
    private String operatorName;

    /**
     * 创入库单的时间
     */
    private LocalDateTime invoiceMakeTime;

    /**
     * 制单人
     */
    private String invoiceMaker;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 应付金额
     */
    private BigDecimal shouldPayOrReceive;

    /**
     * 备注
     */
    private String remark;

    /**
     * 单据状态，1：未作废，2：已作废
     */
    private String status;

    /**
     * 存库单总价
     */
    private BigDecimal totalAmount;
}
