package com.holderzone.erp.entity.domain;

import com.holderzone.framework.util.Page;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MaterialCategoryDOExample extends Page {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MaterialCategoryDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andGuidIsNull() {
            addCriterion("guid is null");
            return (Criteria) this;
        }

        public Criteria andGuidIsNotNull() {
            addCriterion("guid is not null");
            return (Criteria) this;
        }

        public Criteria andGuidEqualTo(String value) {
            addCriterion("guid =", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidNotEqualTo(String value) {
            addCriterion("guid <>", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidGreaterThan(String value) {
            addCriterion("guid >", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidGreaterThanOrEqualTo(String value) {
            addCriterion("guid >=", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidLessThan(String value) {
            addCriterion("guid <", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidLessThanOrEqualTo(String value) {
            addCriterion("guid <=", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidLike(String value) {
            addCriterion("guid like", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidNotLike(String value) {
            addCriterion("guid not like", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidIn(List<String> values) {
            addCriterion("guid in", values, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidNotIn(List<String> values) {
            addCriterion("guid not in", values, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidBetween(String value1, String value2) {
            addCriterion("guid between", value1, value2, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidNotBetween(String value1, String value2) {
            addCriterion("guid not between", value1, value2, "guid");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGuidIsNull() {
            addCriterion("enterprise_guid is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGuidIsNotNull() {
            addCriterion("enterprise_guid is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGuidEqualTo(String value) {
            addCriterion("enterprise_guid =", value, "enterpriseGuid");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGuidNotEqualTo(String value) {
            addCriterion("enterprise_guid <>", value, "enterpriseGuid");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGuidGreaterThan(String value) {
            addCriterion("enterprise_guid >", value, "enterpriseGuid");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGuidGreaterThanOrEqualTo(String value) {
            addCriterion("enterprise_guid >=", value, "enterpriseGuid");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGuidLessThan(String value) {
            addCriterion("enterprise_guid <", value, "enterpriseGuid");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGuidLessThanOrEqualTo(String value) {
            addCriterion("enterprise_guid <=", value, "enterpriseGuid");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGuidLike(String value) {
            addCriterion("enterprise_guid like", value, "enterpriseGuid");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGuidNotLike(String value) {
            addCriterion("enterprise_guid not like", value, "enterpriseGuid");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGuidIn(List<String> values) {
            addCriterion("enterprise_guid in", values, "enterpriseGuid");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGuidNotIn(List<String> values) {
            addCriterion("enterprise_guid not in", values, "enterpriseGuid");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGuidBetween(String value1, String value2) {
            addCriterion("enterprise_guid between", value1, value2, "enterpriseGuid");
            return (Criteria) this;
        }

        public Criteria andEnterpriseGuidNotBetween(String value1, String value2) {
            addCriterion("enterprise_guid not between", value1, value2, "enterpriseGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidIsNull() {
            addCriterion("store_guid is null");
            return (Criteria) this;
        }

        public Criteria andStoreGuidIsNotNull() {
            addCriterion("store_guid is not null");
            return (Criteria) this;
        }

        public Criteria andStoreGuidEqualTo(String value) {
            addCriterion("store_guid =", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidNotEqualTo(String value) {
            addCriterion("store_guid <>", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidGreaterThan(String value) {
            addCriterion("store_guid >", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidGreaterThanOrEqualTo(String value) {
            addCriterion("store_guid >=", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidLessThan(String value) {
            addCriterion("store_guid <", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidLessThanOrEqualTo(String value) {
            addCriterion("store_guid <=", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidLike(String value) {
            addCriterion("store_guid like", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidNotLike(String value) {
            addCriterion("store_guid not like", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidIn(List<String> values) {
            addCriterion("store_guid in", values, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidNotIn(List<String> values) {
            addCriterion("store_guid not in", values, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidBetween(String value1, String value2) {
            addCriterion("store_guid between", value1, value2, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidNotBetween(String value1, String value2) {
            addCriterion("store_guid not between", value1, value2, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidIsNull() {
            addCriterion("warehouse_guid is null");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidIsNotNull() {
            addCriterion("warehouse_guid is not null");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidEqualTo(String value) {
            addCriterion("warehouse_guid =", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidNotEqualTo(String value) {
            addCriterion("warehouse_guid <>", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidGreaterThan(String value) {
            addCriterion("warehouse_guid >", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidGreaterThanOrEqualTo(String value) {
            addCriterion("warehouse_guid >=", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidLessThan(String value) {
            addCriterion("warehouse_guid <", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidLessThanOrEqualTo(String value) {
            addCriterion("warehouse_guid <=", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidLike(String value) {
            addCriterion("warehouse_guid like", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidNotLike(String value) {
            addCriterion("warehouse_guid not like", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidIn(List<String> values) {
            addCriterion("warehouse_guid in", values, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidNotIn(List<String> values) {
            addCriterion("warehouse_guid not in", values, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidBetween(String value1, String value2) {
            addCriterion("warehouse_guid between", value1, value2, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidNotBetween(String value1, String value2) {
            addCriterion("warehouse_guid not between", value1, value2, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}