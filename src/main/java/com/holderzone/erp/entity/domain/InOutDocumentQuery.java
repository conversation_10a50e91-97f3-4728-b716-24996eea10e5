package com.holderzone.erp.entity.domain;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/07 下午 14:53
 * @description
 */
public class InOutDocumentQuery {

    private List<String> warehouseGuidList;

    private Integer status;

    private Integer type;

    private Integer inOutType;

    private String supplierGuid;

    private String guid;

    private Integer currentPage;

    private Integer pageSize;

    private Integer offset;

    private Date startDate;

    private Date endDate;

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public List<String> getWarehouseGuidList() {
        return warehouseGuidList;
    }

    public void setWarehouseGuidList(List<String> warehouseGuidList) {
        this.warehouseGuidList = warehouseGuidList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getInOutType() {
        return inOutType;
    }

    public void setInOutType(Integer inOutType) {
        this.inOutType = inOutType;
    }

    public String getSupplierGuid() {
        return supplierGuid;
    }

    public void setSupplierGuid(String supplierGuid) {
        this.supplierGuid = supplierGuid;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
}
