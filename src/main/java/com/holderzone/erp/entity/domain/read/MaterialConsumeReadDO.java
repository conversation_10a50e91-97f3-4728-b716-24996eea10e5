package com.holderzone.erp.entity.domain.read;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/11/14 20:10
 */
@Data
public class MaterialConsumeReadDO {

    @ApiModelProperty("物料Guid")
    private String guid;

    @ApiModelProperty("物料编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("分类")
    private String classify;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("理论耗用")
    private BigDecimal count;

}
