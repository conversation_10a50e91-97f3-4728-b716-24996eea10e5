package com.holderzone.erp.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("hse_goods")
public class GoodsDO {
    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 商品Code
     */
    private String goodsCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品条码
     */
    private String barCode;

    /**
     * 拼音简码
     */
    private String pinyin;

    /**
     * 商品图片地址
     */
    private String goodsImage;

    /**
     * 剩余库存数量
     */
    private BigDecimal remainRepertoryNum;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 分类Guid
     */
    private String goodsClassifyGuid;

    /**
     * 分类名称
     */
    private String goodsClassifyName;

    /**
     * 是否开启库存，0：否，1：是
     */
    private Integer isOpenStock;

    /**
     * 1.套餐（不称重，无规格），2.规格商品（不称重），3.称重商品（单商品，称重） 4:单品")
     */
    private int itemType;

    /**
     * 安全库存数量
     */
    private BigDecimal safeNum;
}
