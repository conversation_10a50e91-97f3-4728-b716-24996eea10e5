package com.holderzone.erp.entity.domain;

import com.holderzone.erp.entity.domain.MaterialDO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @className CategoryDO
 * @date 2019-04-27 16:53:10
 * @description
 * @program holder-saas-store-erp
 */
public class CategoryDO {

    private String categoryGuid;
    private String categoryName;
    private LocalDateTime gmtCreate;
    private List<MaterialDO> materialDOList;

    public String getCategoryGuid() {
        return categoryGuid;
    }

    public void setCategoryGuid(String categoryGuid) {
        this.categoryGuid = categoryGuid;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public List<MaterialDO> getMaterialDOList() {
        return materialDOList;
    }

    public void setMaterialDOList(List<MaterialDO> materialDOList) {
        this.materialDOList = materialDOList;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }
}
