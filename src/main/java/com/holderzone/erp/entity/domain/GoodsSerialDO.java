package com.holderzone.erp.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("hse_goods_serial")
public class GoodsSerialDO {
    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 商品Guid
     */
    private String goodsGuid;


    /**
     * 业务类型
     */
    private Integer invoiceType;

    /**
     * 变化量
     */
    private BigDecimal changeNum;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 关联单号
     */
    private String invoiceNo;

}
