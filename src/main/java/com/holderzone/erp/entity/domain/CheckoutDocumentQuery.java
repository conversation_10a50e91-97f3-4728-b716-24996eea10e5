package com.holderzone.erp.entity.domain;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/09 上午 10:56
 * @description
 */
public class CheckoutDocumentQuery {

    private List<String> warehouseGuidList;

    private Date startDate;

    private Date endDate;

    private Integer type;

    private String searchContent;

    private Integer currentPage;

    private Integer pageSize;

    private Integer offset;

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public List<String> getWarehouseGuidList() {
        return warehouseGuidList;
    }

    public void setWarehouseGuidList(List<String> warehouseGuidList) {
        this.warehouseGuidList = warehouseGuidList;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSearchContent() {
        return searchContent;
    }

    public void setSearchContent(String searchContent) {
        this.searchContent = searchContent;
    }
}
