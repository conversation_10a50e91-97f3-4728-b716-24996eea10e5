package com.holderzone.erp.entity.domain;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GoodsBomDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public GoodsBomDOExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andGuidIsNull() {
            addCriterion("guid is null");
            return (Criteria) this;
        }

        public Criteria andGuidIsNotNull() {
            addCriterion("guid is not null");
            return (Criteria) this;
        }

        public Criteria andGuidEqualTo(String value) {
            addCriterion("guid =", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidNotEqualTo(String value) {
            addCriterion("guid <>", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidGreaterThan(String value) {
            addCriterion("guid >", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidGreaterThanOrEqualTo(String value) {
            addCriterion("guid >=", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidLessThan(String value) {
            addCriterion("guid <", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidLessThanOrEqualTo(String value) {
            addCriterion("guid <=", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidLike(String value) {
            addCriterion("guid like", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidNotLike(String value) {
            addCriterion("guid not like", value, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidIn(List<String> values) {
            addCriterion("guid in", values, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidNotIn(List<String> values) {
            addCriterion("guid not in", values, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidBetween(String value1, String value2) {
            addCriterion("guid between", value1, value2, "guid");
            return (Criteria) this;
        }

        public Criteria andGuidNotBetween(String value1, String value2) {
            addCriterion("guid not between", value1, value2, "guid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidIsNull() {
            addCriterion("warehouse_guid is null");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidIsNotNull() {
            addCriterion("warehouse_guid is not null");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidEqualTo(String value) {
            addCriterion("warehouse_guid =", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidNotEqualTo(String value) {
            addCriterion("warehouse_guid <>", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidGreaterThan(String value) {
            addCriterion("warehouse_guid >", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidGreaterThanOrEqualTo(String value) {
            addCriterion("warehouse_guid >=", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidLessThan(String value) {
            addCriterion("warehouse_guid <", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidLessThanOrEqualTo(String value) {
            addCriterion("warehouse_guid <=", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidLike(String value) {
            addCriterion("warehouse_guid like", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidNotLike(String value) {
            addCriterion("warehouse_guid not like", value, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidIn(List<String> values) {
            addCriterion("warehouse_guid in", values, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidNotIn(List<String> values) {
            addCriterion("warehouse_guid not in", values, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidBetween(String value1, String value2) {
            addCriterion("warehouse_guid between", value1, value2, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andWarehouseGuidNotBetween(String value1, String value2) {
            addCriterion("warehouse_guid not between", value1, value2, "warehouseGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidIsNull() {
            addCriterion("store_guid is null");
            return (Criteria) this;
        }

        public Criteria andStoreGuidIsNotNull() {
            addCriterion("store_guid is not null");
            return (Criteria) this;
        }

        public Criteria andStoreGuidEqualTo(String value) {
            addCriterion("store_guid =", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidNotEqualTo(String value) {
            addCriterion("store_guid <>", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidGreaterThan(String value) {
            addCriterion("store_guid >", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidGreaterThanOrEqualTo(String value) {
            addCriterion("store_guid >=", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidLessThan(String value) {
            addCriterion("store_guid <", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidLessThanOrEqualTo(String value) {
            addCriterion("store_guid <=", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidLike(String value) {
            addCriterion("store_guid like", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidNotLike(String value) {
            addCriterion("store_guid not like", value, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidIn(List<String> values) {
            addCriterion("store_guid in", values, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidNotIn(List<String> values) {
            addCriterion("store_guid not in", values, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidBetween(String value1, String value2) {
            addCriterion("store_guid between", value1, value2, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andStoreGuidNotBetween(String value1, String value2) {
            addCriterion("store_guid not between", value1, value2, "storeGuid");
            return (Criteria) this;
        }

        public Criteria andGoodsGuidIsNull() {
            addCriterion("goods_guid is null");
            return (Criteria) this;
        }

        public Criteria andGoodsGuidIsNotNull() {
            addCriterion("goods_guid is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsGuidEqualTo(String value) {
            addCriterion("goods_guid =", value, "goodsGuid");
            return (Criteria) this;
        }

        public Criteria andGoodsGuidNotEqualTo(String value) {
            addCriterion("goods_guid <>", value, "goodsGuid");
            return (Criteria) this;
        }

        public Criteria andGoodsGuidGreaterThan(String value) {
            addCriterion("goods_guid >", value, "goodsGuid");
            return (Criteria) this;
        }

        public Criteria andGoodsGuidGreaterThanOrEqualTo(String value) {
            addCriterion("goods_guid >=", value, "goodsGuid");
            return (Criteria) this;
        }

        public Criteria andGoodsGuidLessThan(String value) {
            addCriterion("goods_guid <", value, "goodsGuid");
            return (Criteria) this;
        }

        public Criteria andGoodsGuidLessThanOrEqualTo(String value) {
            addCriterion("goods_guid <=", value, "goodsGuid");
            return (Criteria) this;
        }

        public Criteria andGoodsGuidLike(String value) {
            addCriterion("goods_guid like", value, "goodsGuid");
            return (Criteria) this;
        }

        public Criteria andGoodsGuidNotLike(String value) {
            addCriterion("goods_guid not like", value, "goodsGuid");
            return (Criteria) this;
        }

        public Criteria andGoodsGuidIn(List<String> values) {
            addCriterion("goods_guid in", values, "goodsGuid");
            return (Criteria) this;
        }

        public Criteria andGoodsGuidNotIn(List<String> values) {
            addCriterion("goods_guid not in", values, "goodsGuid");
            return (Criteria) this;
        }

        public Criteria andGoodsGuidBetween(String value1, String value2) {
            addCriterion("goods_guid between", value1, value2, "goodsGuid");
            return (Criteria) this;
        }

        public Criteria andGoodsGuidNotBetween(String value1, String value2) {
            addCriterion("goods_guid not between", value1, value2, "goodsGuid");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIsNull() {
            addCriterion("goods_sku is null");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIsNotNull() {
            addCriterion("goods_sku is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuEqualTo(String value) {
            addCriterion("goods_sku =", value, "goodsSku");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuNotEqualTo(String value) {
            addCriterion("goods_sku <>", value, "goodsSku");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuGreaterThan(String value) {
            addCriterion("goods_sku >", value, "goodsSku");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuGreaterThanOrEqualTo(String value) {
            addCriterion("goods_sku >=", value, "goodsSku");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuLessThan(String value) {
            addCriterion("goods_sku <", value, "goodsSku");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuLessThanOrEqualTo(String value) {
            addCriterion("goods_sku <=", value, "goodsSku");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuLike(String value) {
            addCriterion("goods_sku like", value, "goodsSku");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuNotLike(String value) {
            addCriterion("goods_sku not like", value, "goodsSku");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuIn(List<String> values) {
            addCriterion("goods_sku in", values, "goodsSku");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuNotIn(List<String> values) {
            addCriterion("goods_sku not in", values, "goodsSku");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuBetween(String value1, String value2) {
            addCriterion("goods_sku between", value1, value2, "goodsSku");
            return (Criteria) this;
        }

        public Criteria andGoodsSkuNotBetween(String value1, String value2) {
            addCriterion("goods_sku not between", value1, value2, "goodsSku");
            return (Criteria) this;
        }

        public Criteria andMaterialGuidIsNull() {
            addCriterion("material_guid is null");
            return (Criteria) this;
        }

        public Criteria andMaterialGuidIsNotNull() {
            addCriterion("material_guid is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialGuidEqualTo(String value) {
            addCriterion("material_guid =", value, "materialGuid");
            return (Criteria) this;
        }

        public Criteria andMaterialGuidNotEqualTo(String value) {
            addCriterion("material_guid <>", value, "materialGuid");
            return (Criteria) this;
        }

        public Criteria andMaterialGuidGreaterThan(String value) {
            addCriterion("material_guid >", value, "materialGuid");
            return (Criteria) this;
        }

        public Criteria andMaterialGuidGreaterThanOrEqualTo(String value) {
            addCriterion("material_guid >=", value, "materialGuid");
            return (Criteria) this;
        }

        public Criteria andMaterialGuidLessThan(String value) {
            addCriterion("material_guid <", value, "materialGuid");
            return (Criteria) this;
        }

        public Criteria andMaterialGuidLessThanOrEqualTo(String value) {
            addCriterion("material_guid <=", value, "materialGuid");
            return (Criteria) this;
        }

        public Criteria andMaterialGuidLike(String value) {
            addCriterion("material_guid like", value, "materialGuid");
            return (Criteria) this;
        }

        public Criteria andMaterialGuidNotLike(String value) {
            addCriterion("material_guid not like", value, "materialGuid");
            return (Criteria) this;
        }

        public Criteria andMaterialGuidIn(List<String> values) {
            addCriterion("material_guid in", values, "materialGuid");
            return (Criteria) this;
        }

        public Criteria andMaterialGuidNotIn(List<String> values) {
            addCriterion("material_guid not in", values, "materialGuid");
            return (Criteria) this;
        }

        public Criteria andMaterialGuidBetween(String value1, String value2) {
            addCriterion("material_guid between", value1, value2, "materialGuid");
            return (Criteria) this;
        }

        public Criteria andMaterialGuidNotBetween(String value1, String value2) {
            addCriterion("material_guid not between", value1, value2, "materialGuid");
            return (Criteria) this;
        }

        public Criteria andUsageIsNull() {
            addCriterion("usage is null");
            return (Criteria) this;
        }

        public Criteria andUsageIsNotNull() {
            addCriterion("usage is not null");
            return (Criteria) this;
        }

        public Criteria andUsageEqualTo(BigDecimal value) {
            addCriterion("usage =", value, "usage");
            return (Criteria) this;
        }

        public Criteria andUsageNotEqualTo(BigDecimal value) {
            addCriterion("usage <>", value, "usage");
            return (Criteria) this;
        }

        public Criteria andUsageGreaterThan(BigDecimal value) {
            addCriterion("usage >", value, "usage");
            return (Criteria) this;
        }

        public Criteria andUsageGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("usage >=", value, "usage");
            return (Criteria) this;
        }

        public Criteria andUsageLessThan(BigDecimal value) {
            addCriterion("usage <", value, "usage");
            return (Criteria) this;
        }

        public Criteria andUsageLessThanOrEqualTo(BigDecimal value) {
            addCriterion("usage <=", value, "usage");
            return (Criteria) this;
        }

        public Criteria andUsageIn(List<BigDecimal> values) {
            addCriterion("usage in", values, "usage");
            return (Criteria) this;
        }

        public Criteria andUsageNotIn(List<BigDecimal> values) {
            addCriterion("usage not in", values, "usage");
            return (Criteria) this;
        }

        public Criteria andUsageBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("usage between", value1, value2, "usage");
            return (Criteria) this;
        }

        public Criteria andUsageNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("usage not between", value1, value2, "usage");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}