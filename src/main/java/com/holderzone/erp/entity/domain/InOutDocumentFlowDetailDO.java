package com.holderzone.erp.entity.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/05/10 上午 11:18
 * @description
 */
public class InOutDocumentFlowDetailDO {

    private String guid;

    private String materialGuid;

    private String materialCode;

    private String materialName;

    private BigDecimal count;

    private String unitGuid;

    private String unitName;

    private String documentGuid;

    private Integer type;

    private Integer inOutType;

    private Date documentDate;

    public Integer getInOutType() {
        return inOutType;
    }

    public void setInOutType(Integer inOutType) {
        this.inOutType = inOutType;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDocumentGuid() {
        return documentGuid;
    }

    public void setDocumentGuid(String documentGuid) {
        this.documentGuid = documentGuid;
    }

    public Date getDocumentDate() {
        return documentDate;
    }

    public void setDocumentDate(Date documentDate) {
        this.documentDate = documentDate;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public String getUnitGuid() {
        return unitGuid;
    }

    public void setUnitGuid(String unitGuid) {
        this.unitGuid = unitGuid;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }
}
