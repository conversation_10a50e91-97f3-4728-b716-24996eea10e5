package com.holderzone.erp.entity.domain;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

public class MaterialDO {
    private String guid;

    private String enterpriseGuid;

    private String storeGuid;

    private String warehouseGuid;

    private String property;

    private String name;

    private String simpleName;

    private String unit;

    private String unitName;

    private String auxiliaryUnit;

    private String auxiliaryUnitName;

    private String category;

    private String type;

    private String code;

    private String barCode;

    private String specs;

    private BigDecimal conversionMain;

    private BigDecimal conversionAuxiliary;

    private BigDecimal lowestStock;

    private BigDecimal stock;

    @ApiModelProperty(value = "入库单价")
    private BigDecimal inUnitPrice;

    private BigDecimal salesPrice;

    private BigDecimal costPrice;

    private Integer effectiveDate;

    private String storageMethod;

    private String image;

    private Boolean enabled;

    private Boolean deleted;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    private String remark;

    public BigDecimal getInUnitPrice() {
        return inUnitPrice;
    }

    public void setInUnitPrice(BigDecimal inUnitPrice) {
        this.inUnitPrice = inUnitPrice;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid == null ? null : guid.trim();
    }

    public String getEnterpriseGuid() {
        return enterpriseGuid;
    }

    public void setEnterpriseGuid(String enterpriseGuid) {
        this.enterpriseGuid = enterpriseGuid == null ? null : enterpriseGuid.trim();
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid == null ? null : storeGuid.trim();
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid == null ? null : warehouseGuid.trim();
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property == null ? null : property.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getSimpleName() {
        return simpleName;
    }

    public void setSimpleName(String simpleName) {
        this.simpleName = simpleName == null ? null : simpleName.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public String getAuxiliaryUnit() {
        return auxiliaryUnit;
    }

    public void setAuxiliaryUnit(String auxiliaryUnit) {
        this.auxiliaryUnit = auxiliaryUnit == null ? null : auxiliaryUnit.trim();
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category == null ? null : category.trim();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode == null ? null : barCode.trim();
    }

    public String getSpecs() {
        return specs;
    }

    public void setSpecs(String specs) {
        this.specs = specs == null ? null : specs.trim();
    }

    public BigDecimal getConversionMain() {
        return conversionMain;
    }

    public void setConversionMain(BigDecimal conversionMain) {
        this.conversionMain = conversionMain;
    }

    public BigDecimal getConversionAuxiliary() {
        return conversionAuxiliary;
    }

    public void setConversionAuxiliary(BigDecimal conversionAuxiliary) {
        this.conversionAuxiliary = conversionAuxiliary;
    }

    public BigDecimal getLowestStock() {
        return lowestStock;
    }

    public void setLowestStock(BigDecimal lowestStock) {
        this.lowestStock = lowestStock;
    }

    public BigDecimal getStock() {
        return stock;
    }

    public void setStock(BigDecimal stock) {
        this.stock = stock;
    }

    public BigDecimal getSalesPrice() {
        return salesPrice;
    }

    public void setSalesPrice(BigDecimal salesPrice) {
        this.salesPrice = salesPrice;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public Integer getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Integer effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public String getStorageMethod() {
        return storageMethod;
    }

    public void setStorageMethod(String storageMethod) {
        this.storageMethod = storageMethod == null ? null : storageMethod.trim();
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image == null ? null : image.trim();
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getAuxiliaryUnitName() {
        return auxiliaryUnitName;
    }

    public void setAuxiliaryUnitName(String auxiliaryUnitName) {
        this.auxiliaryUnitName = auxiliaryUnitName;
    }
}