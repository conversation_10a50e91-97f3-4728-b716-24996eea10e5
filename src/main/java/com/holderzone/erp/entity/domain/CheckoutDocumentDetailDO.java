package com.holderzone.erp.entity.domain;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/04/29 上午 11:47
 * @description
 */
public class CheckoutDocumentDetailDO {

    private String guid;

    private String documentGuid;

    private String materialGuid;

    private String materialCode;

    private String materialName;

    private BigDecimal stock;

    private String unitGuid;

    private String unitName;

    private BigDecimal checkCount;

    /**
     * 盘点结果(0：无盈亏，1：盘盈，2：盘亏)
     */
    private Integer checkoutResult;

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getDocumentGuid() {
        return documentGuid;
    }

    public void setDocumentGuid(String documentGuid) {
        this.documentGuid = documentGuid;
    }

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public BigDecimal getStock() {
        return stock;
    }

    public void setStock(BigDecimal stock) {
        this.stock = stock;
    }

    public String getUnitGuid() {
        return unitGuid;
    }

    public void setUnitGuid(String unitGuid) {
        this.unitGuid = unitGuid;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getCheckCount() {
        return checkCount;
    }

    public void setCheckCount(BigDecimal checkCount) {
        this.checkCount = checkCount;
    }

    public Integer getCheckoutResult() {
        return checkoutResult;
    }

    public void setCheckoutResult(Integer checkoutResult) {
        this.checkoutResult = checkoutResult;
    }
}
