package com.holderzone.erp.entity.domain.read;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MaterialDocDetailReadDO
 * @date 2019/11/15 14:15
 * @description 物料出入库流水明细查询结果
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MaterialDocDetailReadDO {

    /**
     * 物料Guid
     */
    private String materialGuid;

    /**
     * 出入库单类型
     */
    private Integer docType;

    /**
     * 出入库单GUID
     */
    private String docGuid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    private BigDecimal stock;

    private BigDecimal count;

    private BigDecimal startStock;

    private BigDecimal endStock = BigDecimal.ZERO;

    private BigDecimal changeStock = BigDecimal.ZERO;
}
