package com.holderzone.erp.entity.domain;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/29 上午 11:43
 * @description
 */
public class CheckoutDocumentDO {

    private String guid;

    private Date gmtCreate;

    private String code;

    private String warehouseGuid;

    private String warehouseName;

    private String storeGuid;

    /**
     * 盘点类型(0：日盘，1：周盘， 2：月盘)
     */
    private Integer type;

    private String operatorGuid;

    private String operatorName;

    private Date documentDate;

    /**
     * 状态(0:未提交，1：已提交)
     */
    private Integer status;

    private String remark;

    /**
     * 是否锁盘
     */
    private Boolean lock;

    private String createStaffGuid;

    private String createStaffName;

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    private List<CheckoutDocumentDetailDO> detailList;

    public List<CheckoutDocumentDetailDO> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<CheckoutDocumentDetailDO> detailList) {
        this.detailList = detailList;
    }

    public Date getDocumentDate() {
        return documentDate;
    }

    public void setDocumentDate(Date documentDate) {
        this.documentDate = documentDate;
    }

    public String getCreateStaffGuid() {
        return createStaffGuid;
    }

    public void setCreateStaffGuid(String createStaffGuid) {
        this.createStaffGuid = createStaffGuid;
    }

    public String getCreateStaffName() {
        return createStaffName;
    }

    public void setCreateStaffName(String createStaffName) {
        this.createStaffName = createStaffName;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOperatorGuid() {
        return operatorGuid;
    }

    public void setOperatorGuid(String operatorGuid) {
        this.operatorGuid = operatorGuid;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Boolean getLock() {
        return lock;
    }

    public void setLock(Boolean lock) {
        this.lock = lock;
    }
}
