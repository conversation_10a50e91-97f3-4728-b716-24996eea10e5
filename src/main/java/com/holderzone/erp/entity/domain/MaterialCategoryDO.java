package com.holderzone.erp.entity.domain;

import java.time.LocalDateTime;

public class MaterialCategoryDO {
    private String guid;

    private String enterpriseGuid;

    private String storeGuid;

    private String warehouseGuid;

    private String name;

    private Boolean deleted;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid == null ? null : guid.trim();
    }

    public String getEnterpriseGuid() {
        return enterpriseGuid;
    }

    public void setEnterpriseGuid(String enterpriseGuid) {
        this.enterpriseGuid = enterpriseGuid == null ? null : enterpriseGuid.trim();
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid == null ? null : storeGuid.trim();
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid == null ? null : warehouseGuid.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }
}