package com.holderzone.erp.entity.domain;

/**
 * <AUTHOR>
 * @className SuppliersQueryDO
 * @date 2019-04-26 14:31:15
 * @description
 * @program holder-saas-store-erp
 */
public class SuppliersQueryDO {

    private String searchConditions;
    private int pageSize;
    private int currentPage;
    private String foreignKey;
    private Integer enabled;
    private int start;

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public String getSearchConditions() {
        return searchConditions;
    }

    public void setSearchConditions(String searchConditions) {
        this.searchConditions = searchConditions;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public String getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(String foreignKey) {
        this.foreignKey = foreignKey;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }
}
