package com.holderzone.erp.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("hse_inventory_goods")
public class GoodsOfInventoryDO {
    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 商品Guid
     */
    private String goodsGuid;

    /**
     * 盘点单Guid
     */
    private String inventoryGuid;

    /**
     * 盘点数量
     */
    private BigDecimal inventoryCount;

    /**
     * 库存快照
     */
    private BigDecimal repertorySnapshot;

}
