package com.holderzone.erp.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("hse_inventory")
public class InventoryDO {
    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 单据类型
     */
    private int invoiceType;

    /**
     * 单据编号
     */
    private String invoiceNo;

    /**
     * 盘点日期
     */
    private LocalDateTime inventoryDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 盘点人员
     */
    private String operator;

    /**
     * 状态，标记单据是否被废除,1:正常，2：正常
     */
    private int status;

    /**
     * 制单人
     */
    private String invoiceMaker;

}
