package com.holderzone.erp.entity.domain;

import java.util.List;

/**
 * <AUTHOR>
 * @className WarehouseQueryDO
 * @date 2019-04-25 14:56:02
 * @description
 * @program holder-saas-store-erp
 */
public class WarehouseQueryDO {

    private String searchConditions;
    private int pageSize;
    private int currentPage;
    private int start;
    private List<String> list;

    public List<String> getList() {
        return list;
    }

    public void setList(List<String> list) {
        this.list = list;
    }

    public String getSearchConditions() {
        return searchConditions;
    }

    public void setSearchConditions(String searchConditions) {
        this.searchConditions = searchConditions;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }
}
