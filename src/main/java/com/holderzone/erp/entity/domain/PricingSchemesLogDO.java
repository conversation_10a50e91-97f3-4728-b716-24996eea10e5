package com.holderzone.erp.entity.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @className PricingSchemesLogDO
 * @date 2019-04-27 11:28:44
 * @description
 * @program holder-saas-store-erp
 */
public class PricingSchemesLogDO {

    private String guid;
    private String pricingSchemesGuid;
    private BigDecimal lastDealPrice;
    private BigDecimal currentDealPrice;
    private String operator;
    private Integer enabled;
    private Integer deleted;
    private LocalDateTime gmtCreate;
    private LocalDateTime gmtModified;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getPricingSchemesGuid() {
        return pricingSchemesGuid;
    }

    public void setPricingSchemesGuid(String pricingSchemesGuid) {
        this.pricingSchemesGuid = pricingSchemesGuid;
    }

    public BigDecimal getLastDealPrice() {
        return lastDealPrice;
    }

    public void setLastDealPrice(BigDecimal lastDealPrice) {
        this.lastDealPrice = lastDealPrice;
    }

    public BigDecimal getCurrentDealPrice() {
        return currentDealPrice;
    }

    public void setCurrentDealPrice(BigDecimal currentDealPrice) {
        this.currentDealPrice = currentDealPrice;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }
}
