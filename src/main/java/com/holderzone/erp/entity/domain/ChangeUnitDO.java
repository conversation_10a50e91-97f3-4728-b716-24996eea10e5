package com.holderzone.erp.entity.domain;

/**
 * <AUTHOR>
 * @className ChangeUnitDo
 * @date 2019-05-23 15:20:11
 * @description
 * @program holder-saas-store-erp
 */
public class ChangeUnitDO {

    private String materialGuid;
    private String originalMainUnit;
    private String originalAuxiliaryUnit;
    private String newMainUnit;
    private String newAuxiliaryUnit;

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public String getOriginalMainUnit() {
        return originalMainUnit;
    }

    public void setOriginalMainUnit(String originalMainUnit) {
        this.originalMainUnit = originalMainUnit;
    }

    public String getOriginalAuxiliaryUnit() {
        return originalAuxiliaryUnit;
    }

    public void setOriginalAuxiliaryUnit(String originalAuxiliaryUnit) {
        this.originalAuxiliaryUnit = originalAuxiliaryUnit;
    }

    public String getNewMainUnit() {
        return newMainUnit;
    }

    public void setNewMainUnit(String newMainUnit) {
        this.newMainUnit = newMainUnit;
    }

    public String getNewAuxiliaryUnit() {
        return newAuxiliaryUnit;
    }

    public void setNewAuxiliaryUnit(String newAuxiliaryUnit) {
        this.newAuxiliaryUnit = newAuxiliaryUnit;
    }
}
