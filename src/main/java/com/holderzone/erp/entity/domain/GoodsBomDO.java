package com.holderzone.erp.entity.domain;

import java.math.BigDecimal;
import java.util.Date;

public class GoodsBomDO {
    private String guid;

    private String warehouseGuid;

    private String storeGuid;

    private String goodsGuid;

    private String goodsSku;

    private String materialGuid;

    private BigDecimal usage;

    private String unit;

    private String unitName;

    private String materialName;
    private Date gmtCreate;

    private Date gmtModified;
    /**
     * 物料配比数量扩展字段
     */
    private Integer materialCategoryCount;

    private String auxiliaryUnit;

    private String auxiliaryUnitName;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid == null ? null : guid.trim();
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid == null ? null : warehouseGuid.trim();
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid == null ? null : storeGuid.trim();
    }

    public String getGoodsGuid() {
        return goodsGuid;
    }

    public void setGoodsGuid(String goodsGuid) {
        this.goodsGuid = goodsGuid == null ? null : goodsGuid.trim();
    }

    public String getGoodsSku() {
        return goodsSku;
    }

    public void setGoodsSku(String goodsSku) {
        this.goodsSku = goodsSku == null ? null : goodsSku.trim();
    }

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid == null ? null : materialGuid.trim();
    }

    public BigDecimal getUsage() {
        return usage;
    }

    public void setUsage(BigDecimal usage) {
        this.usage = usage;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public Integer getMaterialCategoryCount() {
        return materialCategoryCount;
    }

    public void setMaterialCategoryCount(Integer materialCategoryCount) {
        this.materialCategoryCount = materialCategoryCount;
    }

    public String getAuxiliaryUnit() {
        return auxiliaryUnit;
    }

    public void setAuxiliaryUnit(String auxiliaryUnit) {
        this.auxiliaryUnit = auxiliaryUnit;
    }

    public String getAuxiliaryUnitName() {
        return auxiliaryUnitName;
    }

    public void setAuxiliaryUnitName(String auxiliaryUnitName) {
        this.auxiliaryUnitName = auxiliaryUnitName;
    }
}