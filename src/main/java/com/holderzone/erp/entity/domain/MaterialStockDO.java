package com.holderzone.erp.entity.domain;

import java.math.BigDecimal;
import java.util.Date;

public class MaterialStockDO {
    private String guid;

    private String materialGuid;

    private String warehouseGuid;

    private String unit;

    private BigDecimal stock;

    private Date gmtCreate;

    private Date gmtModified;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid == null ? null : guid.trim();
    }

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid == null ? null : materialGuid.trim();
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid == null ? null : warehouseGuid.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public BigDecimal getStock() {
        return stock;
    }

    public void setStock(BigDecimal stock) {
        this.stock = stock;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }
}