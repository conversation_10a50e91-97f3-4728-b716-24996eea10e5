package com.holderzone.erp.entity.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @className PricingSchemesDO
 * @date 2019-04-27 10:54:23
 * @description
 * @program holder-saas-store-erp
 */
public class PricingSchemesDO {

    private String guid;
    private String materialName;
    private String suppliersGuid;
    private String materialGuid;
    private BigDecimal dealPrice;
    private String dealUnit;
    private Integer enabled;
    private Integer deleted;
    private LocalDateTime gmtCreate;
    private LocalDateTime gmtModified;
    private String unit;
    private String unitName;
    private String auxiliaryUnit;
    private String auxiliaryUnitName;
    private BigDecimal conversionMain;
    private BigDecimal conversionAuxiliary;

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getAuxiliaryUnit() {
        return auxiliaryUnit;
    }

    public void setAuxiliaryUnit(String auxiliaryUnit) {
        this.auxiliaryUnit = auxiliaryUnit;
    }

    public String getAuxiliaryUnitName() {
        return auxiliaryUnitName;
    }

    public void setAuxiliaryUnitName(String auxiliaryUnitName) {
        this.auxiliaryUnitName = auxiliaryUnitName;
    }

    public BigDecimal getConversionMain() {
        return conversionMain;
    }

    public void setConversionMain(BigDecimal conversionMain) {
        this.conversionMain = conversionMain;
    }

    public BigDecimal getConversionAuxiliary() {
        return conversionAuxiliary;
    }

    public void setConversionAuxiliary(BigDecimal conversionAuxiliary) {
        this.conversionAuxiliary = conversionAuxiliary;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getSuppliersGuid() {
        return suppliersGuid;
    }

    public void setSuppliersGuid(String suppliersGuid) {
        this.suppliersGuid = suppliersGuid;
    }

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public BigDecimal getDealPrice() {
        return dealPrice;
    }

    public void setDealPrice(BigDecimal dealPrice) {
        this.dealPrice = dealPrice;
    }

    public String getDealUnit() {
        return dealUnit;
    }

    public void setDealUnit(String dealUnit) {
        this.dealUnit = dealUnit;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }
}
