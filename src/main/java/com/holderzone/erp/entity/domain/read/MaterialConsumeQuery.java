package com.holderzone.erp.entity.domain.read;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MertialConsumeQuery
 * @date 2019/11/15 14:32
 * @description 物料流水查询入参
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MaterialConsumeQuery {

    private LocalDateTime startDateTime;

    private LocalDateTime endDateTime;

    private String warehouseGuid;

    private List<String> materialGuidList;
}
