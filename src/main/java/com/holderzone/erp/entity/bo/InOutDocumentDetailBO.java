package com.holderzone.erp.entity.bo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/05/06 下午 18:01
 * @description
 */
public class InOutDocumentDetailBO {

    private String guid;

    private String documentGuid;

    private String materialGuid;

    private String materialCode;

    private String materialName;

    private BigDecimal stock;

    private BigDecimal count;

    private String unitGuid;

    private String unitName;

    private BigDecimal unitPrice;

    private String mainUnitGuid;

    private String mainUnitName;

    private BigDecimal mainUnitCount;

    private BigDecimal totalAmount;

    /**
     * 退货数量
     */
    private BigDecimal returnCount;

    public BigDecimal getStock() {
        return stock;
    }

    public void setStock(BigDecimal stock) {
        this.stock = stock;
    }

    public String getMainUnitGuid() {
        return mainUnitGuid;
    }

    public void setMainUnitGuid(String mainUnitGuid) {
        this.mainUnitGuid = mainUnitGuid;
    }

    public String getMainUnitName() {
        return mainUnitName;
    }

    public void setMainUnitName(String mainUnitName) {
        this.mainUnitName = mainUnitName;
    }

    public BigDecimal getMainUnitCount() {
        return mainUnitCount;
    }

    public void setMainUnitCount(BigDecimal mainUnitCount) {
        this.mainUnitCount = mainUnitCount;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getDocumentGuid() {
        return documentGuid;
    }

    public void setDocumentGuid(String documentGuid) {
        this.documentGuid = documentGuid;
    }

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }


    public String getUnitGuid() {
        return unitGuid;
    }

    public void setUnitGuid(String unitGuid) {
        this.unitGuid = unitGuid;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public BigDecimal getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(BigDecimal returnCount) {
        this.returnCount = returnCount;
    }
}
