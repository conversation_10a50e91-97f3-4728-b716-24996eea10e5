package com.holderzone.erp.entity.bo;

import java.math.BigDecimal;

/**
 * 单位转换
 *
 * <AUTHOR>
 * @date 2019/05/10 17:26
 */
public class UnitConvertBO {
    private String materialGuid;
    private String unitGuid;
    private BigDecimal count;
    private String unitName;

    public UnitConvertBO() {
    }

    public UnitConvertBO(String materialGuid, String unitGuid, BigDecimal count) {
        this.materialGuid = materialGuid;
        this.unitGuid = unitGuid;
        this.count = count;
    }

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public String getUnitGuid() {
        return unitGuid;
    }

    public void setUnitGuid(String unitGuid) {
        this.unitGuid = unitGuid;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }
}
