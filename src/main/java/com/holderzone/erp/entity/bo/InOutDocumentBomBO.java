package com.holderzone.erp.entity.bo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/05/17 13:56
 */
public class InOutDocumentBomBO {
    private String materialGuid;
    private BigDecimal usage;
    private String unit;

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public BigDecimal getUsage() {
        return usage;
    }

    public void setUsage(BigDecimal usage) {
        this.usage = usage;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}
