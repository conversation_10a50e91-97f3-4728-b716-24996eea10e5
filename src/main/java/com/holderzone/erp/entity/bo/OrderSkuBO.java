package com.holderzone.erp.entity.bo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/17 下午 16:30
 * @description
 */
public class OrderSkuBO {

    private String enterpriseGuid;

    private String storeGuid;

    private List<SkuInfoBO> skuList;

    private String operatorGuid;

    private String operatorName;

    //是否出库，标记字段
    private transient Boolean isOut;

    public Boolean getOut() {
        return isOut;
    }

    public void setOut(Boolean out) {
        isOut = out;
    }

    public String getEnterpriseGuid() {
        return enterpriseGuid;
    }

    public void setEnterpriseGuid(String enterpriseGuid) {
        this.enterpriseGuid = enterpriseGuid;
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public List<SkuInfoBO> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<SkuInfoBO> skuList) {
        this.skuList = skuList;
    }

    public String getOperatorGuid() {
        return operatorGuid;
    }

    public void setOperatorGuid(String operatorGuid) {
        this.operatorGuid = operatorGuid;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }
}
