package com.holderzone.erp.entity.bo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/17 13:55
 */
public class InOutDocumentBomQueryBO {

    private String storeGuid;

    List<GoodsInfo> goodsInfoList = new ArrayList<>();

    public InOutDocumentBomQueryBO() {
    }

    public InOutDocumentBomQueryBO(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public void addGoods(String sku, BigDecimal count) {
        GoodsInfo goodsInfo = new GoodsInfo();
        goodsInfo.setGoodsSku(sku);
        goodsInfo.setCount(count);
        goodsInfoList.add(goodsInfo);
    }

    public String getStoreGuid() {
        return storeGuid;
    }

    public void setStoreGuid(String storeGuid) {
        this.storeGuid = storeGuid;
    }

    public List<GoodsInfo> getGoodsInfoList() {
        return goodsInfoList;
    }

    public void setGoodsInfoList(List<GoodsInfo> goodsInfoList) {
        this.goodsInfoList = goodsInfoList;
    }
}
