package com.holderzone.erp.entity.bo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/05/06 下午 14:23
 * @description
 */
public class UpdateStockBO {

    /**
     * 物料guid
     */
    private String materialGuid;

    /**
     * 修改的数量
     */
    private BigDecimal count;

    /**
     * 物料单位
     */
    private String materialUnit;
    /**
     * 仓库GUID
     */
    private String warehouseGuid;

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public BigDecimal getCount() {
        return count;
    }

    public void setCount(BigDecimal count) {
        this.count = count;
    }

    public String getMaterialUnit() {
        return materialUnit;
    }

    public void setMaterialUnit(String materialUnit) {
        this.materialUnit = materialUnit;
    }

    public String getWarehouseGuid() {
        return warehouseGuid;
    }

    public void setWarehouseGuid(String warehouseGuid) {
        this.warehouseGuid = warehouseGuid;
    }
}
