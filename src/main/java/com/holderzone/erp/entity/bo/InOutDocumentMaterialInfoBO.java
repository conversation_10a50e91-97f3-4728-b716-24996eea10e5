package com.holderzone.erp.entity.bo;

import com.holderzone.framework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/30 上午 11:42
 * @description
 */
public class InOutDocumentMaterialInfoBO {

    /**
     * 物料guid
     */
    private String materialGuid;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 库存
     */
    private BigDecimal stock;

    /**
     * 主单位guid
     */
    private String masterUnitGuid;

    /**
     * 主单位名称
     */
    private String masterUnitName;

    /**
     * 辅单位guid
     */
    private String auxiliaryUnitGuid;

    /**
     * 辅单位名称
     */
    private String auxiliaryUnitName;

    /**
     * 转换关系中主单位数量
     */
    private Long masterUnitCount;

    /**
     * 转换关系中辅单位数量
     */
    private Long auxiliaryUnitCount;

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public BigDecimal getStock() {
        return stock;
    }

    public void setStock(BigDecimal stock) {
        this.stock = stock;
    }

    public String getMasterUnitGuid() {
        return masterUnitGuid;
    }

    public void setMasterUnitGuid(String masterUnitGuid) {
        this.masterUnitGuid = masterUnitGuid;
    }

    public String getMasterUnitName() {
        return masterUnitName;
    }

    public void setMasterUnitName(String masterUnitName) {
        this.masterUnitName = masterUnitName;
    }

    public String getAuxiliaryUnitGuid() {
        return auxiliaryUnitGuid;
    }

    public void setAuxiliaryUnitGuid(String auxiliaryUnitGuid) {
        this.auxiliaryUnitGuid = auxiliaryUnitGuid;
    }

    public String getAuxiliaryUnitName() {
        return auxiliaryUnitName;
    }

    public void setAuxiliaryUnitName(String auxiliaryUnitName) {
        this.auxiliaryUnitName = auxiliaryUnitName;
    }

    public Long getMasterUnitCount() {
        return masterUnitCount;
    }

    public void setMasterUnitCount(Long masterUnitCount) {
        this.masterUnitCount = masterUnitCount;
    }

    public Long getAuxiliaryUnitCount() {
        return auxiliaryUnitCount;
    }

    public void setAuxiliaryUnitCount(Long auxiliaryUnitCount) {
        this.auxiliaryUnitCount = auxiliaryUnitCount;
    }

    public List<InOutDocumentMaterialUnitBO> retrieveMaterialUnitList() {
        List<InOutDocumentMaterialUnitBO> materialUnitList = new ArrayList<>();
        InOutDocumentMaterialUnitBO masterUnit = new InOutDocumentMaterialUnitBO();
        masterUnit.setUnitGuid(masterUnitGuid);
        masterUnit.setUnitName(masterUnitName);
        materialUnitList.add(masterUnit);

        if (!StringUtils.isEmpty(auxiliaryUnitGuid)){
            InOutDocumentMaterialUnitBO auxiliaryUnit = new InOutDocumentMaterialUnitBO();
            auxiliaryUnit.setUnitGuid(auxiliaryUnitGuid);
//            String unitSwitchStr = retrieveUnitSwitchStr();
//            auxiliaryUnit.setUnitName(auxiliaryUnitName + "(" + unitSwitchStr + ")");
            auxiliaryUnit.setUnitName(auxiliaryUnitName);
            materialUnitList.add(auxiliaryUnit);
        }

        return materialUnitList;
    }

    private String retrieveUnitSwitchStr() {
        StringBuilder builder = new StringBuilder();
        builder.append(auxiliaryUnitCount);
        builder.append(auxiliaryUnitName);
        builder.append(" = ");
        builder.append(masterUnitCount);
        builder.append(masterUnitName);
        return builder.toString();
    }

}
