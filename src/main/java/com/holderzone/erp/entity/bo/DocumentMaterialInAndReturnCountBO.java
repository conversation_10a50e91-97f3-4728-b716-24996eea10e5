package com.holderzone.erp.entity.bo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/05/05 下午 13:48
 * @description
 */
public class DocumentMaterialInAndReturnCountBO {

    private String guid;

    private String materialGuid;

    private BigDecimal inCount;

    private BigDecimal returnCount;

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public BigDecimal getInCount() {
        return inCount;
    }

    public void setInCount(BigDecimal inCount) {
        this.inCount = inCount;
    }

    public BigDecimal getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(BigDecimal returnCount) {
        this.returnCount = returnCount;
    }
}
