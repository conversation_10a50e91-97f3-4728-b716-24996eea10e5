package com.holderzone.erp.entity.bo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/04/30 下午 14:08
 * @description
 */
public class InOutDocumentMaterialUnitPriceBO {

    /**
     * 物料Guid
     */
    private String materialGuid;

    /**
     * 单位Guid
     */
    private String unitGuid;

    /**
     * 物料协议单价
     */
    private BigDecimal unitPrice;

    public String getUnitGuid() {
        return unitGuid;
    }

    public void setUnitGuid(String unitGuid) {
        this.unitGuid = unitGuid;
    }

    public String getMaterialGuid() {
        return materialGuid;
    }

    public void setMaterialGuid(String materialGuid) {
        this.materialGuid = materialGuid;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }
}
