package com.holderzone.erp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.erp.entity.domain.GoodsSerialDO;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.erpretail.SaleSkuSumDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.InsertGoodsSerialReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.QueryGoodsSerialReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSerialRespDTO;

import java.util.List;

public interface GoodsSerialService extends IService<GoodsSerialDO> {

    Page<GoodsSerialRespDTO> queryGoodsSerial(QueryGoodsSerialReqDTO queryGoodsSerialReqDTO);

    boolean insertGoodsSerial(List<InsertGoodsSerialReqDTO> insertGoodsSerialReqDTOList);

    SaleSkuSumDTO queryGoodsSaleSkuSum();
}
