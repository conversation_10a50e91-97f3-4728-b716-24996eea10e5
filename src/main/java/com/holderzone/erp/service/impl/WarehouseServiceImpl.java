package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.WarehouseMapper;
import com.holderzone.erp.entity.domain.WarehouseDO;
import com.holderzone.erp.entity.domain.WarehouseQueryDO;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.erp.service.IMaterialCategoryService;
import com.holderzone.erp.service.IMaterialService;
import com.holderzone.erp.service.InOutDocumentService;
import com.holderzone.erp.service.WarehouseService;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.erp.WarehouseDTO;
import com.holderzone.saas.store.dto.erp.WarehouseQueryDTO;
import com.holderzone.saas.store.dto.erp.WarehouseReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicInteger;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className WarehouseServiceImpl
 * @date 2019-04-24 14:49:22
 * @description
 * @program holder-saas-store-erp
 */
@Service
@Slf4j
public class WarehouseServiceImpl implements WarehouseService {

    private final static Logger LOGGER = LoggerFactory.getLogger(WarehouseServiceImpl.class);

    private final RedisTemplate redisTemplate;
    private final ErpModuleMapper moduleMapper;
    private final WarehouseMapper warehouseMapper;
    private InOutDocumentService inOutDocumentService;

    @Lazy
    @Autowired
    public WarehouseServiceImpl(RedisTemplate redisTemplate,
                                ErpModuleMapper moduleMapper,
                                WarehouseMapper warehouseMapper,
                                InOutDocumentService inOutDocumentService) {
        this.redisTemplate = redisTemplate;
        this.moduleMapper = moduleMapper;
        this.warehouseMapper = warehouseMapper;
        this.inOutDocumentService = inOutDocumentService;
    }

    @Override
    public String createWarehouse(WarehouseReqDTO reqDTO) {
        String guid = IDUtils.nextId();
        WarehouseDO warehouseDO = moduleMapper.mapToWarehouseDO(reqDTO);
        warehouseDO.setGuid(guid);
        if (StringUtils.isEmpty(warehouseDO.getCode())) {
            log.info("编码为空，自动生成编号");
            warehouseDO.setCode(warehouseCode());
        }
        int count = warehouseMapper.checkCodeRepeat(warehouseDO.getCode());
        log.info("编号重复数量：{}", count);
        if (count > 0) {
            throw new BusinessException("仓库编号已存在，请重新生成");
        }
        checkNameRepeat(warehouseDO.getName(), null);
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        if (warehouseDO.getType() == 0) {
            warehouseDO.setForeignKey(enterpriseGuid);
        }
        warehouseDO.setEnterpriseGuid(enterpriseGuid);
        warehouseMapper.createWarehouse(warehouseDO);
        return guid;
    }



    private void checkNameRepeat(String name, String guid) {
        int nameRepeat = warehouseMapper.checkNameRepeat(name, guid);
        if (nameRepeat > 0) {
            throw new BusinessException("仓库名称已存在，请重命名");
        }
    }

    @Override
    public WarehouseDTO getWarehouseByGuid(String guid) {
        WarehouseDO warehouseDO = warehouseMapper.getWarehouseByGuid(guid);
        if (warehouseDO == null) {
            throw new BusinessException("仓库不存在");
        }
        return moduleMapper.mapToWarehouseDTO(warehouseDO);
    }

    @Override
    public List<WarehouseDTO> getWarehouseByStoreGuid(String storeGuid) {
        List<WarehouseDO> warehouseDOList = warehouseMapper.getWarehouseByStoreGuid(storeGuid);
//        if (warehouseDOList.isEmpty()) {
//            throw new BusinessException("此门店下不存在仓库");
//        }
        return moduleMapper.mapToWarehouseDtoList(warehouseDOList);
    }

    @Override
    public String updateWarehouse(WarehouseReqDTO reqDTO) {
        WarehouseDO warehouseDO = moduleMapper.mapToWarehouseDO(reqDTO);
        checkNameRepeat(warehouseDO.getName(), warehouseDO.getGuid());
        warehouseMapper.updateWarehouse(warehouseDO);
        return warehouseDO.getGuid();
    }

    @Override
    public Page<WarehouseDTO> getWarehouseList(WarehouseQueryDTO queryDTO) {
        WarehouseQueryDO queryDO = moduleMapper.mapToWarehouseQueryDO(queryDTO);
        queryDO.setStart((queryDO.getCurrentPage() - 1) * queryDO.getPageSize());
        long total = warehouseMapper.getWarehouseListTotal(queryDO);
        if (total == 0) {
            return new Page<>(queryDO.getCurrentPage(), queryDO.getPageSize(), 0, Collections.emptyList());
        }
        List<WarehouseDO> warehouseDoList = warehouseMapper.getWarehouseList(queryDO);
        List<WarehouseDTO> warehouseDTOS = moduleMapper.mapToWarehouseDtoList(warehouseDoList);
        return new Page<>(queryDO.getCurrentPage(), queryDO.getPageSize(), total, warehouseDTOS);
    }

    @Override
    public List<WarehouseDTO> getWarehouseListByName(WarehouseQueryDTO queryDTO) {
        WarehouseQueryDO queryDO = moduleMapper.mapToWarehouseQueryDO(queryDTO);
        List<WarehouseDO> warehouseDoList = warehouseMapper.getWarehouseListByName(queryDO);
        warehouseDoList = warehouseDoList.stream().peek(warehouseDO -> {
            if (warehouseDO.getType() == 0) {
                warehouseDO.setName(warehouseDO.getName() + "(企业)");
            } else if (warehouseDO.getType() == 1) {
                warehouseDO.setName(warehouseDO.getName() + "(门店)");
            }
        }).collect(Collectors.toList());
        return moduleMapper.mapToWarehouseDtoList(warehouseDoList);
    }

    @Override
    public Boolean enableOrDisableWarehouse(String guid) {
        warehouseMapper.enableOrDisableWarehouse(guid);
        return true;
    }

    @Override
    public Boolean lockOrUnlockWarehouse(String guid) {
        warehouseMapper.lockOrUnlockWarehouse(guid);
        return true;
    }

    @Override
    public Boolean deleteWarehouse(String guid) {
        if (inOutDocumentService.existDocumentOfWarehouse(guid)) {
            throw new BusinessException("仓库下存在数据，无法删除");
        }
        warehouseMapper.deleteWarehouse(guid);
        return true;
    }

    @Override
    public Boolean isLock(String guid) {
        WarehouseDO warehouse = warehouseMapper.getWarehouseByGuid(guid);
        return warehouse.getStatus() == 0;
    }

    @Override
    public String warehouseCode() {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        String key = "warehouseCode:" + enterpriseGuid;
        Integer warehouseCode = (Integer) redisTemplate.opsForValue().get(key);
        LOGGER.info("缓存获取到的code：{}", warehouseCode);
        int currentMax = 0;
        if (warehouseCode == null) {
            String maximumCode = warehouseMapper.getMaximumCode();
            currentMax = StringUtils.isEmpty(maximumCode) ? 0 : Integer.parseInt(maximumCode);
        }
        RedisAtomicInteger id = new RedisAtomicInteger(key, redisTemplate.getConnectionFactory());
        id.set(warehouseCode == null ? currentMax : warehouseCode);
        warehouseCode = id.incrementAndGet();
        if (String.valueOf(warehouseCode).length() > 6) {
            throw new BusinessException("编号超出长度限制");
        }
        return String.format("%06d", warehouseCode);
    }

    @Override
    public String updateStoreWarehouse(WarehouseReqDTO reqDTO) {
        WarehouseDO warehouseDO = moduleMapper.mapToWarehouseDO(reqDTO);
        checkNameRepeat(warehouseDO.getName(), warehouseDO.getGuid());
        warehouseMapper.updateStoreWarehouse(warehouseDO.getName(), warehouseDO.getForeignKey());
        return warehouseDO.getGuid();
    }

}
