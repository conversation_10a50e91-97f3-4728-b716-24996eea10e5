package com.holderzone.erp.service.impl;

import com.holderzone.erp.service.DistributedIdService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DistributedIdServiceImpl implements DistributedIdService {

    private static final String TAG_REPERTORY = "repertory/item";
    private static final String TAG_INVENTORY = "inventory/item";
    private static final String TAG_GOODS_BATCH = "repertory/goodsbatch";
    private static final String TAG_GOODS_SERIAL_BATCH = "repertory/goodsSerialbatch";
//    private static final String TAG_OPERATION = "operation/item";
//    private static final String TAG_OPERATION_GOODS = "operation/operation_goods";
//    private static final String TAG_GOODS_CLASSIFY = "deposit/goods";
//    private static final String TAG_DEPOSIT_REMIND = "deposit/remind";

    private final RedisTemplate redisTemplate;


    @Autowired
    public DistributedIdServiceImpl(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public Long rawId(String tag) {
        try {
            return BatchIdGenerator.getGuid(redisTemplate, tag);
        } catch (IOException e) {
            throw new BusinessException("生成Guid失败：" + e.getMessage());
        }
    }

    @Override
    public String nextId(String tag) {
        return String.valueOf(rawId(tag));
    }

    @Override
    public String nextRepertoryGuid() {
        return nextId(TAG_REPERTORY);
    }

    @Override
    public String nextInventoryGuid() {
        return nextId(TAG_INVENTORY);
    }

    @Override
    public List<String> nextBatchGoodsItemGuid(long count) {
        return nextBatchId(TAG_GOODS_BATCH, count);
    }

    @Override
    public List<String> nextBatchGoodsSerialGuid(long count) {
        return nextBatchId(TAG_GOODS_SERIAL_BATCH, count);
    }

    @Override
    public List<String> nextBatchId(String tag, long count) {
        return BatchIdGenerator.batchGetGuids(redisTemplate, tag, count)
                .stream().map(String::valueOf).collect(Collectors.toList());
    }

}
