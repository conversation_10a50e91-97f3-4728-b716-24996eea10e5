package com.holderzone.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.erp.dao.InventoryMapper;
import com.holderzone.erp.entity.domain.GoodsDO;
import com.holderzone.erp.entity.domain.GoodsOfInventoryDO;
import com.holderzone.erp.entity.domain.InventoryDO;
import com.holderzone.erp.entity.enumeration.InventoryTypeEnum;
import com.holderzone.erp.mapperstruct.InventoryMapstruct;
import com.holderzone.erp.service.*;
import com.holderzone.erp.utils.DateUtil;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.CreateInventoryReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.InsertGoodsSerialReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.InventoryOverviewReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSumInfoRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.InventoryDetailRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.InventoryManageRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InventoryServiceImpl extends ServiceImpl<InventoryMapper, InventoryDO> implements InventoryService {

    @Autowired
    private DistributedIdService distributedIdService;

    @Autowired
    private InventoryMapstruct inventoryMapstruct;

    @Autowired
    private GoodsOfInventoryService goodsOfInventoryService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private InventoryMapper inventoryMapper;

    @Autowired
    private GoodsSerialService goodsSerialService;

    @Override
    @Transactional
    public String createInventory(CreateInventoryReqDTO createInventoryReqDTO) {

        // 存储盘点单
        InventoryDO inventoryDO = inventoryMapstruct.fromCreateInventoryReqDTOTOInventoryDO(createInventoryReqDTO);
        String guid = distributedIdService.nextInventoryGuid();
        inventoryDO.setGuid(guid);
        inventoryDO.setInvoiceMaker(UserContextUtils.getUserName());
        inventoryDO.setInvoiceNo(guid);
        inventoryDO.setStatus(1);
        if (StringUtils.isEmpty(inventoryDO.getOperator())) {
            inventoryDO.setOperator(UserContextUtils.getUserName());
        }
        if (ObjectUtils.isEmpty(createInventoryReqDTO.getInventoryDate())) {
            inventoryDO.setInventoryDate(DateTimeUtils.now());
        } else {
            inventoryDO.setInventoryDate(DateTimeUtils.string2LocalDateTime(createInventoryReqDTO.getInventoryDate(), "yyyy-MM-dd HH:mm:ss"));
        }
        save(inventoryDO);

        // 存储盘点单的商品盘点数据
        List<String> guids = distributedIdService.nextBatchGoodsItemGuid(createInventoryReqDTO.getGoodsList().size());

        List<GoodsOfInventoryDO> list = createInventoryReqDTO.getGoodsList().stream()
                .map(goodsRepertoryInfoSumRespDTO -> {
                    GoodsOfInventoryDO goodsOfInventoryDO = new GoodsOfInventoryDO();
                    goodsOfInventoryDO.setGuid(guids.remove(guids.size() - 1));
                    goodsOfInventoryDO.setGoodsGuid(goodsRepertoryInfoSumRespDTO.getGoodsGuid());
                    goodsOfInventoryDO.setInventoryGuid(guid);
                    goodsOfInventoryDO.setRepertorySnapshot(goodsRepertoryInfoSumRespDTO.getCount());
                    goodsOfInventoryDO.setInventoryCount(goodsRepertoryInfoSumRespDTO.getInventoryCount());

                    if (goodsRepertoryInfoSumRespDTO.getCount() != goodsRepertoryInfoSumRespDTO.getInventoryCount()) { // 盘亏
                        goodsService.updateGoodsRepertoryNum(goodsRepertoryInfoSumRespDTO.getGoodsGuid(), goodsRepertoryInfoSumRespDTO.getInventoryCount());
                    }

                    return goodsOfInventoryDO;
                }).collect(Collectors.toList());

        // 库存单对应的商品插入库中
        goodsOfInventoryService.insertGoodsOfInventory(list);

        // 插入商品流水
        goodsSerialService.insertGoodsSerial(createInventoryReqDTO.getGoodsList().stream()
                .map(goodsRepertoryInfoSumRespDTO -> {
                    InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
                    // 4:盘盈入库
                    if (goodsRepertoryInfoSumRespDTO.getInventoryCount().compareTo(goodsRepertoryInfoSumRespDTO.getCount()) > 0) {
                        insertGoodsSerialReqDTO.setInvoiceType(4);
                    } else { // 5:盘亏出库
                        insertGoodsSerialReqDTO.setInvoiceType(5);
                    }
                    insertGoodsSerialReqDTO.setChangeNum(goodsRepertoryInfoSumRespDTO.getInventoryCount().subtract(goodsRepertoryInfoSumRespDTO.getCount()));
                    insertGoodsSerialReqDTO.setGoodsGuid(goodsRepertoryInfoSumRespDTO.getGoodsGuid());
                    insertGoodsSerialReqDTO.setInvoiceNo(inventoryDO.getGuid());
                    insertGoodsSerialReqDTO.setUnitName(goodsRepertoryInfoSumRespDTO.getUnitName());
                    return insertGoodsSerialReqDTO;
                }).collect(Collectors.toList()));
        return guid;
    }

    @Override
    @Transactional
    public InventoryDetailRespDTO queryInventoryDetail(SingleDataDTO singleDataDTO) {
        LambdaQueryWrapper wrapper = new LambdaQueryWrapper<InventoryDO>()
                .eq(InventoryDO::getGuid, singleDataDTO.getData());
        InventoryDO inventoryDO = getOne(wrapper);
        InventoryDetailRespDTO inventoryDetailRespDTO = inventoryMapstruct.fromInventoryDOTODetailRespDTO(inventoryDO);
        inventoryDetailRespDTO.setInvoiceTime(DateTimeUtils.localDateTime2String(inventoryDO.getGmtCreate(), "yyyy-MM-dd HH:mm:ss"));
        inventoryDetailRespDTO.setDate(DateTimeUtils.localDateTime2String(inventoryDO.getInventoryDate()));
        List<GoodsOfInventoryDO> list = goodsOfInventoryService.queryGoodsOfInventory(singleDataDTO.getData());
        List<String> goodsGuidList = list.stream().map(GoodsOfInventoryDO::getGoodsGuid).collect(Collectors.toList());
        // 从Goods表中查询商品的存库数量、名字、单位
        inventoryDetailRespDTO.setGoodsList(list.stream().map(goodsOfInventoryDO -> {
            GoodsSumInfoRespDTO goodsSumInfoRespDTO = new GoodsSumInfoRespDTO();
            goodsSumInfoRespDTO.setCount(goodsOfInventoryDO.getRepertorySnapshot());
            InOutGoodsDTO inOutGoodsDTO = goodsService.queryGoodsInfo(goodsOfInventoryDO.getGoodsGuid());
            goodsSumInfoRespDTO.setGoodsName(inOutGoodsDTO.getGoodsName());
            goodsSumInfoRespDTO.setUnitName(inOutGoodsDTO.getUnitName());
            goodsSumInfoRespDTO.setInventoryCount(goodsOfInventoryDO.getInventoryCount());
            return goodsSumInfoRespDTO;
        }).collect(Collectors.toList()));
        inventoryDetailRespDTO.setInvoiceName(InventoryTypeEnum.ofMode(Integer.valueOf(inventoryDO.getInvoiceType())).getDes());
        log.info("盘点详情返回结果：{}", JacksonUtils.writeValueAsString(inventoryDetailRespDTO));
        return inventoryDetailRespDTO;
    }

    @Override
    @Transactional
    public Page<InventoryManageRespDTO> queryInventoryOverview(InventoryOverviewReqDTO inventoryOverviewReqDTO) {
        inventoryOverviewReqDTO.setStartDateTime(inventoryOverviewReqDTO.getStartDate() + " 00:00:00");
        inventoryOverviewReqDTO.setEndDateTime(inventoryOverviewReqDTO.getEndDate() + " 23:59:59");
        IPage<InventoryDO> page = inventoryMapper.queryInventoryOverview(new PageAdapter<>(inventoryOverviewReqDTO), inventoryOverviewReqDTO);
        return new PageAdapter<>(page, page.getRecords().stream().map(inventoryDO -> {
            InventoryManageRespDTO inventoryManageRespDTO = inventoryMapstruct.fromInventoryDOTOManageRespDTO(inventoryDO);
            inventoryManageRespDTO.setInvoiceName(InventoryTypeEnum.ofMode(inventoryDO.getInvoiceType()).getDes());
            return inventoryManageRespDTO;
        }).collect(Collectors.toList()));
    }

    @Override
    @Transactional
    public boolean invalidInventory(String inventoryGuid) {
        LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper<InventoryDO>().eq(InventoryDO::getGuid, inventoryGuid);
        if (count(lambdaQueryWrapper) == 0) {
            throw new BusinessException("单号不存在");
        }
        InventoryDO inventoryDO = getOne(lambdaQueryWrapper);
        inventoryDO.setStatus(2);
        update(inventoryDO, lambdaQueryWrapper);

        // 盘点单对应的商品列表
        List<GoodsOfInventoryDO> goodsOfInventoryDOList = goodsOfInventoryService.queryGoodsOfInventory(inventoryGuid);

        List<GoodsDO> list = goodsOfInventoryDOList.stream()
                .filter(goodsOfInventoryDO -> goodsOfInventoryDO.getRepertorySnapshot() != goodsOfInventoryDO.getInventoryCount())
                .map(goodsOfInventoryDO -> {
                    GoodsDO goodsDO = goodsService.getOne(new LambdaQueryWrapper<GoodsDO>().eq(GoodsDO::getGuid, goodsOfInventoryDO.getGoodsGuid()));
                    goodsDO.setRemainRepertoryNum(goodsDO.getRemainRepertoryNum().add(goodsOfInventoryDO.getRepertorySnapshot()).subtract(goodsOfInventoryDO.getInventoryCount()));
                    return goodsDO;
                }).collect(Collectors.toList());

        return goodsService.saveOrUpdateBatch(list);
    }
}
