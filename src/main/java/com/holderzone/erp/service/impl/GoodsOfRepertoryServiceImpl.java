package com.holderzone.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.erp.dao.GoodsOfRepertoryMapper;
import com.holderzone.erp.entity.domain.GoodsOfRepertoryDO;
import com.holderzone.erp.service.GoodsOfRepertoryService;
import com.holderzone.framework.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class GoodsOfRepertoryServiceImpl extends ServiceImpl<GoodsOfRepertoryMapper, GoodsOfRepertoryDO> implements GoodsOfRepertoryService {

    @Autowired
    private GoodsOfRepertoryMapper goodsOfRepertoryMapper;


    @Override
    public void insertGoods(GoodsOfRepertoryDO goodsOfRepertoryDO) {
        save(goodsOfRepertoryDO);
    }

    @Override
    public void insertBatchGoods(List<GoodsOfRepertoryDO> goodsOfRepertoryDO) {
        log.info("插入库存单对饮的商品：{}", JacksonUtils.writeValueAsString(goodsOfRepertoryDO));
        saveBatch(goodsOfRepertoryDO);

    }

    @Override
    public List<GoodsOfRepertoryDO> queryGoodsOfRepertory(String repertoryGuid) {
        LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper<GoodsOfRepertoryDO>()
                .eq(GoodsOfRepertoryDO::getRepertoryGuid, repertoryGuid);
        return list(lambdaQueryWrapper);
    }
}
