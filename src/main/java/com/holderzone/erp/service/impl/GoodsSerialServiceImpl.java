package com.holderzone.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.erp.dao.GoodsSerialMapper;
import com.holderzone.erp.entity.domain.GoodsDO;
import com.holderzone.erp.entity.domain.GoodsSerialDO;
import com.holderzone.erp.entity.domain.InventoryDO;
import com.holderzone.erp.entity.domain.RepertoryDO;
import com.holderzone.erp.entity.enumeration.InvoiceTypeEnum;
import com.holderzone.erp.mapperstruct.GoodsSerialMapstruct;
import com.holderzone.erp.service.*;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.erpretail.SaleSkuSumDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsClassifyAndItemRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSerialRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.InsertGoodsSerialReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.QueryGoodsSerialReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GoodsSerialServiceImpl extends ServiceImpl<GoodsSerialMapper, GoodsSerialDO> implements GoodsSerialService {

    @Autowired
    private GoodsSerialMapper goodsSerialMapper;

    @Autowired
    private DistributedIdService distributedIdService;

    @Autowired
    private GoodsSerialMapstruct goodsSerialMapstruct;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    InRepertoryService inRepertoryService;

    @Autowired
    private InventoryService inventoryService;


    @Override
    public Page<GoodsSerialRespDTO> queryGoodsSerial(QueryGoodsSerialReqDTO queryGoodsSerialReqDTO) {
        queryGoodsSerialReqDTO.setStartDateTime(queryGoodsSerialReqDTO.getStartDate() + " 00:00:00");
        queryGoodsSerialReqDTO.setEndDateTime(queryGoodsSerialReqDTO.getEndDate() + " 23:59:59");
        IPage<GoodsSerialDO> page = goodsSerialMapper.queryGoodsSerial(new PageAdapter<GoodsSerialDO>(queryGoodsSerialReqDTO), queryGoodsSerialReqDTO);

        List<GoodsSerialRespDTO> list = page.getRecords().stream()
                .map(goodsSerialDO -> {
                            GoodsSerialRespDTO goodsSerialRespDTO = goodsSerialMapstruct.fromGoodsSerialDO(goodsSerialDO);
                            goodsSerialRespDTO.setInvoiceName(InvoiceTypeEnum.ofMode(goodsSerialDO.getInvoiceType()).getDes());
                            goodsSerialRespDTO.setInvoiceType(goodsSerialDO.getInvoiceType());
                            goodsSerialRespDTO.setSafeNum(goodsService.queryGoodsInfo(goodsSerialDO.getGoodsGuid()).getSafeNum());
                            goodsSerialRespDTO.setInvoiceTime(DateTimeUtils.localDateTime2String(goodsSerialDO.getGmtCreate(), "yyyy-MM-dd HH:mm:ss"));
                            // 2：顾客退货，3:销售出库 6:期初入库; 信息单据作废标记
                            // 1:采购入库，2：顾客退货，3:销售出库，4:盘盈入库, 5:盘亏出库, 6:期初入库，7:其他入库，8：退货出库，9：其他出库)
                            if (goodsSerialDO.getInvoiceType() == 1 || goodsSerialDO.getInvoiceType() == 7 || goodsSerialDO.getInvoiceType() == 8 || goodsSerialDO.getInvoiceType() == 9) {
                                if (inRepertoryService.getOne(new LambdaQueryWrapper<RepertoryDO>().eq(RepertoryDO::getGuid, goodsSerialDO.getInvoiceNo())).getStatus().equals("2")) {
                                    goodsSerialRespDTO.setInvalid(true);
                                } else {
                                    goodsSerialRespDTO.setInvalid(false);
                                }
                            } else if (goodsSerialDO.getInvoiceType() == 4 || goodsSerialDO.getInvoiceType() == 5) {
                                if (inventoryService.getOne(new LambdaQueryWrapper<InventoryDO>().eq(InventoryDO::getGuid, goodsSerialDO.getInvoiceNo())).getStatus() == 2) {
                                    goodsSerialRespDTO.setInvalid(true);
                                } else {
                                    goodsSerialRespDTO.setInvalid(false);
                                }
                            } else {
                                goodsSerialRespDTO.setInvalid(false);
                            }
                            return goodsSerialRespDTO;
                        }
                ).collect(Collectors.toList());
        log.info("查询商品流水返回值：{}", JacksonUtils.writeValueAsString(list));
        return new PageAdapter<>(page, list);
    }

    @Override
    public boolean insertGoodsSerial(List<InsertGoodsSerialReqDTO> insertGoodsSerialReqDTOList) {
        // 插入商品流水信息
        List<String> guids = distributedIdService.nextBatchGoodsSerialGuid(insertGoodsSerialReqDTOList.size());
        List<GoodsSerialDO> list = insertGoodsSerialReqDTOList.stream()
                .map(goodsSerialRespDTO -> {
                    GoodsSerialDO goodsSerialDO = goodsSerialMapstruct.fromInsertGoodsSerialDTO(goodsSerialRespDTO);
                    goodsSerialDO.setGuid(guids.remove(guids.size() - 1));
                    return goodsSerialDO;
                }).collect(Collectors.toList());

        log.info("插入商品流水，待插入列表：{}", JacksonUtils.writeValueAsString(list));

        return saveBatch(list);
    }

    @Override
    public SaleSkuSumDTO queryGoodsSaleSkuSum() {
        LocalDateTime now = DateTimeUtils.now();
        LocalDateTime sevenDayBefore = now.minus(7, ChronoUnit.DAYS);
        LocalDateTime thirtyDayBefore = now.minus(30, ChronoUnit.DAYS);
        LambdaQueryWrapper sevenDayWrapper = new LambdaQueryWrapper<GoodsSerialDO>()
                .between(GoodsSerialDO::getGmtCreate, sevenDayBefore, now);
        LambdaQueryWrapper thirtyDayWrapper = new LambdaQueryWrapper<GoodsSerialDO>()
                .between(GoodsSerialDO::getGmtCreate, thirtyDayBefore, now);
        Map<String, List<GoodsSerialDO>> sevenMap = (Map<String, List<GoodsSerialDO>>) list(sevenDayWrapper).stream().collect(Collectors.groupingBy(GoodsSerialDO::getGoodsGuid));
        Map<String, List<GoodsSerialDO>> thirtyMap = (Map<String, List<GoodsSerialDO>>) list(thirtyDayWrapper).stream().collect(Collectors.groupingBy(GoodsSerialDO::getGoodsGuid));
        SaleSkuSumDTO saleSkuSumDTO = new SaleSkuSumDTO();
        saleSkuSumDTO.setSevenDaySkuSum(sevenMap.size());
        saleSkuSumDTO.setThirtyDaySkuSum(thirtyMap.size());

        return saleSkuSumDTO;
    }
}
