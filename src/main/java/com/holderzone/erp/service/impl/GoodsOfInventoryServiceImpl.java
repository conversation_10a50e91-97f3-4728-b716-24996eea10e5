package com.holderzone.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.erp.dao.GoodsOfInventoryMapper;
import com.holderzone.erp.entity.domain.GoodsOfInventoryDO;
import com.holderzone.erp.service.GoodsOfInventoryService;
import com.holderzone.erp.service.GoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GoodsOfInventoryServiceImpl extends ServiceImpl<GoodsOfInventoryMapper, GoodsOfInventoryDO> implements GoodsOfInventoryService {

    @Autowired
    private GoodsService goodsService;

    @Override
    public void insertGoodsOfInventory(List<GoodsOfInventoryDO> list) {
        saveBatch(list);
    }

    @Override
    public List<GoodsOfInventoryDO> queryGoodsOfInventory(String inventoryGuid) {
        LambdaQueryWrapper wrapper = new LambdaQueryWrapper<GoodsOfInventoryDO>().eq(GoodsOfInventoryDO::getInventoryGuid, inventoryGuid);
        return list(wrapper);
    }
}
