package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.MaterialUnitDOMapper;
import com.holderzone.erp.entity.domain.MaterialUnitDO;
import com.holderzone.erp.entity.domain.MaterialUnitDOExample;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.erp.service.IMaterialUnitService;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.saas.store.dto.erp.MaterialUnitDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/27 15:29
 */
@Service
@Transactional
public class MaterialUnitServiceImpl implements IMaterialUnitService {
    @Autowired
    MaterialUnitDOMapper unitDOMapper;
    @Autowired
    ErpModuleMapper moduleMapper;

    @Override
    public boolean add(MaterialUnitDTO materialUnitDTO) {
        MaterialUnitDO materialUnitDO = moduleMapper.mapToUnitDO(materialUnitDTO);
        materialUnitDO.setGuid(IDUtils.nextId());
        return unitDOMapper.insertSelective(materialUnitDO) > 0;
    }

    @Override
    public List<MaterialUnitDTO> list() {
        List<MaterialUnitDO> materialUnitDOS = unitDOMapper.selectByExample(new MaterialUnitDOExample());
        return moduleMapper.mapToUnitDTOList(materialUnitDOS);
    }
}
