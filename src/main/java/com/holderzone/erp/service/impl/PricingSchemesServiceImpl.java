package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.PricingSchemesMapper;
import com.holderzone.erp.entity.domain.ChangeUnitDO;
import com.holderzone.erp.entity.domain.PricingSchemesDO;
import com.holderzone.erp.entity.domain.PricingSchemesLogDO;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.erp.service.PricingSchemesService;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.erp.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className PricingSchemesServiceImpl
 * @date 2019-04-27 10:18:04
 * @description
 * @program holder-saas-store-erp
 */
@Service
public class PricingSchemesServiceImpl implements PricingSchemesService {

    private ErpModuleMapper moduleMapper;
    private PricingSchemesMapper pricingSchemesMapper;

    @Autowired
    public PricingSchemesServiceImpl(ErpModuleMapper moduleMapper, PricingSchemesMapper pricingSchemesMapper) {
        this.moduleMapper = moduleMapper;
        this.pricingSchemesMapper = pricingSchemesMapper;
    }

    @Transactional
    @Override
    public Boolean savePricingSchemes(PricingReqDTO pricingReqDTO) {
        List<PricingSchemesReqDTO> pricingSchemesList = pricingReqDTO.getPricingSchemesList();
        if (pricingSchemesList != null && !pricingSchemesList.isEmpty()) {
            List<PricingSchemesReqDTO> pricingSchemesReqDTOList = pricingReqDTO.getPricingSchemesList().stream()
                    .peek(pricingSchemes -> {
                        if (StringUtils.isEmpty(pricingSchemes.getGuid())) {
                            pricingSchemes.setGuid(IDUtils.nextId());
                        }
                        pricingSchemes.setSuppliersGuid(pricingReqDTO.getSuppliersGuid());
                    }).collect(Collectors.toList());
            List<PricingSchemesDO> pricingSchemesDOList = moduleMapper.mapToPricingSchemesDoList(pricingSchemesReqDTOList);
            // 校验价格是否有变动
            List<PricingSchemesDO> originalPricingSchemesList = pricingSchemesMapper.getPricingSchemesListBySuppliersGuid(pricingReqDTO.getSuppliersGuid());
            Map<String, PricingSchemesDO> pricingSchemesMap = new HashMap<>();
            originalPricingSchemesList.forEach(originalPricingSchemesDO -> pricingSchemesMap.put(originalPricingSchemesDO.getGuid(), originalPricingSchemesDO));
            if (!pricingSchemesMap.isEmpty()) {
                List<PricingSchemesLogDO> logDOList = new ArrayList<>();
                pricingSchemesDOList.forEach(pricingSchemes -> {
                    PricingSchemesDO originalPricingSchemesDO = pricingSchemesMap.get(pricingSchemes.getGuid());
                    if (originalPricingSchemesDO != null && pricingSchemes.getDealPrice().compareTo(originalPricingSchemesDO.getDealPrice()) != 0) {
                        PricingSchemesLogDO logDO = new PricingSchemesLogDO();
                        logDO.setGuid(IDUtils.nextId());
                        logDO.setPricingSchemesGuid(pricingSchemes.getGuid());
                        logDO.setCurrentDealPrice(pricingSchemes.getDealPrice());
                        logDO.setLastDealPrice(originalPricingSchemesDO.getDealPrice());
                        String userGuid = UserContextUtils.getUserGuid();
                        logDO.setOperator(userGuid == null ? "" : userGuid);
                        logDOList.add(logDO);
                    }
                });
                if (!logDOList.isEmpty()) {
                    pricingSchemesMapper.savePricingSchemesLog(logDOList);
                }
            }
            pricingSchemesMapper.savePricingSchemes(pricingSchemesDOList);
        }
        // 删除物料报价信息
        List<String> deleteList = pricingReqDTO.getDeleteList();
        if (deleteList != null && !deleteList.isEmpty()) {
            deletePricingSchemes(deleteList);
        }
        return true;
    }

    @Override
    public List<PricingSchemesDTO> getPricingSchemesList(String suppliersGuid) {
        List<PricingSchemesDO> pricingSchemesList= pricingSchemesMapper.getPricingSchemesListBySuppliersGuid(suppliersGuid);
        Map<String, List<MaterialUnitDTO>> unitListMap = new HashMap<>();
        pricingSchemesList.forEach(pricingSchemesDO -> {
            List<MaterialUnitDTO> unitList = new ArrayList<>();
            MaterialUnitDTO unitDTO = new MaterialUnitDTO();
            unitDTO.setGuid(pricingSchemesDO.getUnit());
            unitDTO.setName(pricingSchemesDO.getUnitName());
            unitList.add(unitDTO);
            if (!StringUtils.isEmpty(pricingSchemesDO.getAuxiliaryUnit())) {
                MaterialUnitDTO auxiliaryUnitDTO = new MaterialUnitDTO();
                auxiliaryUnitDTO.setGuid(pricingSchemesDO.getAuxiliaryUnit());
                auxiliaryUnitDTO.setName(pricingSchemesDO.getAuxiliaryUnitName());
                unitList.add(auxiliaryUnitDTO);
            }
            unitListMap.put(pricingSchemesDO.getGuid(), unitList);
        });
        List<PricingSchemesDTO> pricingSchemesDTOList = moduleMapper.mapToPricingSchemesDtoList(pricingSchemesList);
        pricingSchemesDTOList = pricingSchemesDTOList.stream()
                .peek(pricingSchemesDTO -> pricingSchemesDTO.setUnitList(unitListMap.get(pricingSchemesDTO.getGuid())))
                .collect(Collectors.toList());
        return pricingSchemesDTOList;
    }

    @Override
    public Boolean changeMaterialUnitInPricingSchemes(ChangeUnitDO changeUnitDO) {
        List<PricingSchemesDO> pricingSchemesDOList = pricingSchemesMapper.getPricingSchemesByMaterialGuid(changeUnitDO.getMaterialGuid());
        if (pricingSchemesDOList.isEmpty()) {
            return true;
        }
        pricingSchemesDOList = pricingSchemesDOList.stream().peek(pricingSchemesDO -> {
            String originalDealUnit = pricingSchemesDO.getDealUnit();
            String newDealUnit;
            if (originalDealUnit.equalsIgnoreCase(changeUnitDO.getOriginalMainUnit())) {
                newDealUnit = changeUnitDO.getNewMainUnit();
            }else {
                newDealUnit = StringUtils.isEmpty(changeUnitDO.getNewAuxiliaryUnit()) ? changeUnitDO.getNewMainUnit() : changeUnitDO.getNewAuxiliaryUnit();
            }
            pricingSchemesDO.setDealUnit(newDealUnit);
        }).collect(Collectors.toList());
        pricingSchemesMapper.changeMaterialUnitInPricingSchemes(pricingSchemesDOList);
        return true;
    }

    @Override
    public Boolean deletePricingSchemes(List<String> list) {
        if (list.isEmpty()) {
            throw new BusinessException("报价方案guid不能为空");
        }
        pricingSchemesMapper.deletePricingSchemes(list);
        return true;
    }

    @Override
    public Boolean enableOrDisablePricingSchemes(String guid) {
        pricingSchemesMapper.enableOrDisablePricingSchemes(guid);
        return true;
    }

    @Override
    public List<PricingSchemesDTO> batchQueryPricingSchemesList(PricingSchemesQueryDTO queryDTO) {
        List<PricingSchemesDO> pricingSchemesDOS = pricingSchemesMapper.batchQueryPricingSchemesList(queryDTO.getSuppliersGuid(), queryDTO.getList());
        return moduleMapper.mapToPricingSchemesDtoList(pricingSchemesDOS);
    }

}
