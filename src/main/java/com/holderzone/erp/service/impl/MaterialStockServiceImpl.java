package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.MaterialDOMapper;
import com.holderzone.erp.dao.MaterialStockDOMapper;
import com.holderzone.erp.entity.bo.UpdateStockBO;
import com.holderzone.erp.entity.domain.*;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.erp.mapperstruct.InOutDocumentTransform;
import com.holderzone.erp.service.IMaterialCategoryService;
import com.holderzone.erp.service.MaterialStockService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.erp.*;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/05/10 15:42
 */
@Service
public class MaterialStockServiceImpl implements MaterialStockService {
    @Autowired
    MaterialStockDOMapper stockMapper;
    @Autowired
    MaterialDOMapper materialDOMapper;
    @Autowired
    ErpModuleMapper moduleMapper;

    @Autowired
    IMaterialCategoryService categoryService;

    private InOutDocumentTransform inOutDocumentTransform = InOutDocumentTransform.INSTANCE;

    @Override
    public List<MaterialDTO> findStockPage(StockQueryDTO stockQueryDTO, Page page) {
        List<MaterialDO> stockPage = stockMapper.findStockPage(stockQueryDTO, page);
        DecimalFormat df = new DecimalFormat("0.00");
        stockPage.forEach(stock -> df.format(stock.getInUnitPrice()));
        return moduleMapper.mapToMaterialDTOList(stockPage);
    }

    @Override
    public List<MaterialDTO> listByCondition(StockQueryDTO stockQueryDTO) {
        List<MaterialDTO> stockList = this.findStockPage(stockQueryDTO, null);
        if (CollectionUtils.isEmpty(stockList)) {
            return stockList;
        }
        List<String> categoryList = stockList.stream()
                .map(MaterialDTO::getCategory)
                .filter(category -> !StringUtils.isEmpty(category))
                .distinct()
                .collect(Collectors.toList());
        SingleListDTO dto = new SingleListDTO();
        dto.setList(categoryList);
        List<CategoryDTO> categoryDTOList = categoryService.listByGuidList(dto);
        Map<String, String> categoryNameMap = new HashMap<>();
        categoryNameMap.put("", "默认分类");
        if (!CollectionUtils.isEmpty(categoryDTOList)) {
            categoryNameMap = categoryDTOList.stream()
                    .collect(Collectors.toMap(CategoryDTO::getCategoryGuid, CategoryDTO::getCategoryName, (v1, v2) -> v1));
        }
        for (MaterialDTO stock : stockList) {
            String name = categoryNameMap.get(stock.getCategory());
            if (StringUtils.isEmpty(name)) {
                name = "默认分类";
            }
            stock.setCategoryName(name);
        }
        return stockList;
    }

    /**
     * 查询供货商的商品
     */
    @Override
    public List<InOutDocumentSelectDTO> queryMaterialBySupplier(MaterialBySupplierQueryDTO queryDTO) {
        List<InOutDocumentDO> inOutDocumentDOList = stockMapper.queryMaterialBySupplier(queryDTO);
        return inOutDocumentTransform.inOutDocumentDosToSelectDtos(inOutDocumentDOList);
    }

    @Override
    public boolean addStock(UpdateStockBO updateStockBO) {
        //适配单位转换关系,将库存转为主单位对应的库存
        unitAdapter(updateStockBO);
        return updateStock(updateStockBO);
    }

    @Override
    public boolean batchAddStock(List<UpdateStockBO> updateStockBOS) {
        //适配单位转换关系,将库存转为主单位对应的库存
        if (updateStockBOS == null || updateStockBOS.isEmpty()) {
            return false;
        }
        validateBOList(updateStockBOS);
        unitAdapterList(updateStockBOS);
        return updateStockList(updateStockBOS);
    }

    @Override
    public boolean reduceStock(UpdateStockBO updateStockBO) {
        unitAdapter(updateStockBO);
        MaterialStockDOExample example = new MaterialStockDOExample();
        example.createCriteria().andMaterialGuidEqualTo(updateStockBO.getMaterialGuid()).andWarehouseGuidEqualTo(updateStockBO.getWarehouseGuid());
        List<MaterialStockDO> materialStockDOS = stockMapper.selectByExample(example);
        if (materialStockDOS.isEmpty()) {
            throw new BusinessException("库存信息不存在");
        }
        MaterialStockDO stockDO = materialStockDOS.get(0);
        //库存检查
//        checkStock(updateStockBO.getCount(), stockDO.getCount());
        //库存扣减
        return stockMapper.reduceStock(updateStockBO.getMaterialGuid(), updateStockBO.getCount(), updateStockBO.getWarehouseGuid()) > 0;
    }

    @Override
    public boolean batchReduceStock(List<UpdateStockBO> updateStockBOList) {
        if (updateStockBOList == null || updateStockBOList.isEmpty()) {
            return false;
        }
        validateBOList(updateStockBOList);
        unitAdapterList(updateStockBOList);
        List<String> materialGuidList = updateStockBOList.stream().map(UpdateStockBO::getMaterialGuid).collect(Collectors.toList());
        MaterialStockDOExample example = new MaterialStockDOExample();
        example.createCriteria().andMaterialGuidIn(materialGuidList).andWarehouseGuidEqualTo(updateStockBOList.get(0).getWarehouseGuid());
        List<MaterialStockDO> materialStockDOS = stockMapper.selectByExample(example);
        List<String> existGuidList = materialStockDOS.stream().map(MaterialStockDO::getMaterialGuid).collect(Collectors.toList());
        //过滤存在的库存列表
        List<UpdateStockBO> existBO = updateStockBOList.stream().filter(updateStockBO -> existGuidList.contains(updateStockBO.getMaterialGuid())).collect(Collectors.toList());
        //过滤获取不存在库存表中的物料
        List<UpdateStockBO> notExistBO = updateStockBOList.stream().filter(s -> !existGuidList.contains(s.getMaterialGuid())).collect(Collectors.toList());
        //存在的库存直接扣减
        if (!existBO.isEmpty()) {
            stockMapper.reduceStockBatch(existBO);
        }
        if (!notExistBO.isEmpty()) {
            notExistBO.forEach(updateStockBO -> updateStockBO.setCount(BigDecimal.ZERO.subtract(updateStockBO.getCount())));
            List<MaterialStockDO> materialStockDOList = buildStockDOList(notExistBO);
            stockMapper.insertStockBatch(materialStockDOList);
        }
        return true;
    }

    /**
     * 将给定单位的数量适配转换为对应主单位的数量
     */
    private void unitAdapter(UpdateStockBO updateStockBO) {
        MaterialDOExample example = new MaterialDOExample();
        example.createCriteria().andGuidEqualTo(updateStockBO.getMaterialGuid()).andDeletedEqualTo(false);
        List<MaterialDO> materialDOS = materialDOMapper.selectByExample(example);
        if (materialDOS.isEmpty()) {
            throw new BusinessException("物料不存在");
        }
        MaterialDO materialDO = materialDOS.get(0);
        stockAdapterUnit(updateStockBO, materialDO);
    }

    private void unitAdapterList(List<UpdateStockBO> stockBOList) {
        List<String> materialGuidList = stockBOList.stream().map(UpdateStockBO::getMaterialGuid).collect(Collectors.toList());
        MaterialDOExample example = new MaterialDOExample();
        example.createCriteria().andGuidIn(materialGuidList).andDeletedEqualTo(false);
        //根据GUID查询出匹配的物料信息
        List<MaterialDO> materialDOS = materialDOMapper.selectByExample(example);
        //过滤出不存在的物料
        List<String> existGuidList = materialDOS.stream().map(MaterialDO::getGuid).collect(Collectors.toList());
        List<String> notExistGuidList = materialGuidList.stream().filter(s -> !existGuidList.contains(s)).collect(Collectors.toList());
        if (!notExistGuidList.isEmpty()) {
            throw new BusinessException("存在已删除物料");
        }
        //对物料遍历进行单位转换
        for (MaterialDO materialDO : materialDOS) {
            //匹配获取相应物料的BO
            List<UpdateStockBO> collect = stockBOList.stream()
                    .filter(updateStockBO -> updateStockBO.getMaterialGuid().equals(materialDO.getGuid()))
                    .collect(Collectors.toList());
            if (!collect.isEmpty()) {
                UpdateStockBO updateStockBO = collect.get(0);
                stockAdapterUnit(updateStockBO, materialDO);
            }
        }
    }

    /**
     * 根据单位换算关系将数量转换为对应主单位的数量
     *
     * @param updateStockBO 更新库存入参实体
     * @param materialDO    原始DO
     */
    private void stockAdapterUnit(UpdateStockBO updateStockBO, MaterialDO materialDO) {
        if (updateStockBO.getMaterialUnit().equals(materialDO.getAuxiliaryUnit())) {
            //辅单位处理:主单位/辅单位*数量
            if (materialDO.getConversionAuxiliary().compareTo(BigDecimal.ZERO) == 0) {
                throw new BusinessException("单位转换关系不存在");
            }
            BigDecimal divide = materialDO.getConversionMain().divide(materialDO.getConversionAuxiliary());
            BigDecimal multiply = divide.multiply(updateStockBO.getCount());
            //设置转换为主单位后对应的库存
            updateStockBO.setCount(multiply);
        } else if (!materialDO.getUnit().equals(updateStockBO.getMaterialUnit())) {
            //也不为主单位
            throw new BusinessException("单位不存在");
        }
    }

    /**
     * 更新库存,若不存在则新增,存在则更新
     */
    private boolean updateStock(UpdateStockBO updateStockBO) {
        MaterialStockDOExample example = new MaterialStockDOExample();
        String materialGuid = updateStockBO.getMaterialGuid();
        example.createCriteria().andMaterialGuidEqualTo(materialGuid).andWarehouseGuidEqualTo(updateStockBO.getWarehouseGuid());
        if (stockMapper.countByExample(example) > 0) {
            //存在则直接更新库存
            return stockMapper.addStock(materialGuid, updateStockBO.getCount(), updateStockBO.getWarehouseGuid()) > 0;
        }
        MaterialStockDO materialStockDO = buildStockDO(updateStockBO);
        //不存在则插入新的库存记录
        return stockMapper.insertSelective(materialStockDO) > 0;
    }

    /**
     * 批量更新库存
     *
     * @param updateStockBOS 更新库存入参实体列表
     * @return true/false
     */
    private boolean updateStockList(List<UpdateStockBO> updateStockBOS) {
        MaterialStockDOExample example = new MaterialStockDOExample();
        List<String> materialGuidList = updateStockBOS.stream().map(UpdateStockBO::getMaterialGuid).collect(Collectors.toList());
        example.createCriteria().andMaterialGuidIn(materialGuidList).andWarehouseGuidEqualTo(updateStockBOS.get(0).getWarehouseGuid());
        //查询已经存在库存的物料列表
        List<MaterialStockDO> existMaterialList = stockMapper.selectByExample(example);
        List<String> existMaterialGuidList = existMaterialList.stream().map(MaterialStockDO::getMaterialGuid).collect(Collectors.toList());
        List<UpdateStockBO> existMaterial = updateStockBOS.stream().filter(updateStockBO -> existMaterialGuidList.contains(updateStockBO.getMaterialGuid())).collect(Collectors.toList());
        List<UpdateStockBO> notExistMaterial = updateStockBOS.stream().filter(updateStockBO -> !existMaterialGuidList.contains(updateStockBO.getMaterialGuid())).collect(Collectors.toList());
        int result = 0;
        if (!existMaterial.isEmpty()) {
            /*try (SqlSession sqlSession = sqlSessionTemplate.getSqlSessionFactory().openSession(ExecutorType.BATCH)) {
                MaterialStockDOMapper mapper = sqlSession.getMapper(MaterialStockDOMapper.class);
                existMaterial.forEach(updateStockBO -> {
                    mapper.addStock(updateStockBO.getMaterialGuid(),updateStockBO.getCount());
                });
                List<BatchResult> batchResults = sqlSession.flushStatements();
                System.out.println("");
            }*/
            //存在则直接更新库存
            result += stockMapper.addStockBatch(existMaterial);
        }
        List<MaterialStockDO> materialStockDOS = buildStockDOList(notExistMaterial);
        //不存在则插入新的库存记录
        if (!materialStockDOS.isEmpty()) {
            result += stockMapper.insertStockBatch(materialStockDOS);
        }
        return result > 0;
    }

    /**
     * 构建一个新的MaterialStockDO
     *
     * @return MaterialStockDO
     */
    private MaterialStockDO buildStockDO(UpdateStockBO updateStockBO) {
        MaterialStockDO materialStockDO = new MaterialStockDO();
        materialStockDO.setGuid(IDUtils.nextId());
        materialStockDO.setStock(updateStockBO.getCount());
        materialStockDO.setWarehouseGuid(updateStockBO.getWarehouseGuid());
        materialStockDO.setMaterialGuid(updateStockBO.getMaterialGuid());
        materialStockDO.setUnit(updateStockBO.getMaterialUnit());
        return materialStockDO;
    }

    /**
     * 批量购将stockDO
     *
     * @param updateStockBOList 更新库存入参实体列表
     * @return 构建的stockDO列表
     */
    private List<MaterialStockDO> buildStockDOList(List<UpdateStockBO> updateStockBOList) {
        List<MaterialStockDO> list = new ArrayList<>(updateStockBOList.size());
        for (UpdateStockBO updateStockBO : updateStockBOList) {
            list.add(buildStockDO(updateStockBO));
        }
        return list;
    }

    private void validateBOList(List<UpdateStockBO> updateStockBOList) {
        updateStockBOList.forEach(updateStockBO -> {
            if (StringUtils.isEmpty(updateStockBO.getWarehouseGuid())) {
                throw new ParameterException("仓库GUID不能为空");
            }
            if (StringUtils.isEmpty(updateStockBO.getMaterialGuid())) {
                throw new ParameterException("物料GUID不能为空");
            }
            if (StringUtils.isEmpty(updateStockBO.getMaterialUnit())) {
                throw new ParameterException("单位GUID不能为空");
            }
        });
    }

}
