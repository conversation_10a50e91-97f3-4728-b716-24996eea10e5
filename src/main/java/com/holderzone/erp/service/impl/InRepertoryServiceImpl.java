package com.holderzone.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.erp.dao.RepertoryMapper;
import com.holderzone.erp.entity.domain.GoodsDO;
import com.holderzone.erp.entity.domain.GoodsOfRepertoryDO;
import com.holderzone.erp.entity.domain.RepertoryDO;
import com.holderzone.erp.entity.enumeration.InvoiceTypeEnum;
import com.holderzone.erp.mapperstruct.GoodsMapstruct;
import com.holderzone.erp.mapperstruct.RepertoryMapstruct;
import com.holderzone.erp.service.*;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.*;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSumInfoRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.RepertoryDetailInfoRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.RepertoryManageRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

import static com.holderzone.erp.utils.DateUtil.getCurrentDate;

@Service
@Slf4j
public class InRepertoryServiceImpl extends ServiceImpl<RepertoryMapper, RepertoryDO> implements InRepertoryService {

    @Autowired
    private RepertoryMapstruct repertoryMapstruct;

    @Autowired
    private GoodsMapstruct goodsMapstruct;

    @Autowired
    private DistributedIdService distributedIdService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private GoodsOfRepertoryService goodsOfRepertoryService;

    @Autowired
    private RepertoryMapper repertoryMapper;

    @Autowired
    private GoodsSerialService goodsSerialService;

    @Override
    public RepertoryDetailInfoRespDTO queryRepertoryDetail(String repertoryGuid) {
        RepertoryDO repertoryDO = getOne(new LambdaQueryWrapper<RepertoryDO>().eq(RepertoryDO::getGuid, repertoryGuid));
        RepertoryDetailInfoRespDTO repertoryDetailInfoRespDTO = repertoryMapstruct.fromRepertoryDO(repertoryDO);
        List<InOutGoodsDTO> list = goodsMapstruct.fromGoodsListToGoodsOfRepertoryList(goodsOfRepertoryService.queryGoodsOfRepertory(repertoryGuid));
        repertoryDetailInfoRespDTO.setDetailList(list.stream().map(inOutGoodsDTO -> {
            GoodsDO goodsDO = goodsService.getOne(new LambdaQueryWrapper<GoodsDO>().eq(GoodsDO::getGuid, inOutGoodsDTO.getGoodsGuid()));
            inOutGoodsDTO.setGoodsName(goodsDO.getGoodsName());
            inOutGoodsDTO.setUnitName(goodsDO.getUnitName());
            return inOutGoodsDTO;
        }).collect(Collectors.toList()));
        repertoryDetailInfoRespDTO.setInvoiceName(InvoiceTypeEnum.ofMode(Integer.valueOf(repertoryDO.getInvoiceType())).getDes());
        log.info("查询库存详情返回结果：{}", JacksonUtils.writeValueAsString(repertoryDetailInfoRespDTO));
        return repertoryDetailInfoRespDTO;
    }

    @Override
    public Page<RepertoryManageRespDTO> queryRepertoryManageList(QueryRepertoryManageReqDTO queryRepertoryManageReqDTO) {

        IPage<RepertoryDO> page = repertoryMapper.queryInOutRepertoryList(new PageAdapter<RepertoryDO>(queryRepertoryManageReqDTO), queryRepertoryManageReqDTO);
        List<RepertoryManageRespDTO> list = page.getRecords().stream().map(repertoryDO -> {
            RepertoryManageRespDTO repertoryManageRespDTO = repertoryMapstruct.repertoryDOToRepertoryManageRespDTO(repertoryDO);
            repertoryManageRespDTO.setInvoiceName(InvoiceTypeEnum.ofMode(Integer.valueOf(repertoryDO.getInvoiceType())).getDes());
            return repertoryManageRespDTO;
        }).collect(Collectors.toList());
        log.info("库存管理列表返回结果：{}", JacksonUtils.writeValueAsString(list));
        return new PageAdapter<>(page, list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean invalidRepertory(SingleDataDTO singleDataDTO) {
        LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper<RepertoryDO>().eq(RepertoryDO::getGuid, singleDataDTO.getData());
        if (count(lambdaQueryWrapper) == 0) {
            throw new BusinessException("单号不存在");
        }
        RepertoryDO repertoryDO = getOne(lambdaQueryWrapper);
        repertoryDO.setStatus("2");
        log.info("作废库存单，更细库存记录status状态：{}", JacksonUtils.writeValueAsString(repertoryDO));
        update(repertoryDO, lambdaQueryWrapper);

        List<GoodsOfRepertoryDO> goodsOfInventoryDOList = goodsOfRepertoryService.queryGoodsOfRepertory(singleDataDTO.getData());
        List<String> goodsGuidList = goodsOfRepertoryService.queryGoodsOfRepertory(singleDataDTO.getData()).stream().map(GoodsOfRepertoryDO::getGoodsGuid).collect(Collectors.toList());
        List<GoodsDO> list = goodsService.queryGoods(goodsGuidList);
        list.stream().map(goodsDO -> {
            if (repertoryDO.getInOut() == 0) { // 作废入库操作，减库存
                goodsDO.setRemainRepertoryNum(goodsDO.getRemainRepertoryNum()
                        .subtract(goodsOfInventoryDOList.stream()
                                .filter(goodsOfRepertoryDO -> goodsDO.getGuid().equals(goodsOfRepertoryDO.getGoodsGuid()))
                                .findAny()
                                .get()
                                .getCount())
                );
            } else {
                goodsDO.setRemainRepertoryNum(goodsDO.getRemainRepertoryNum()
                        .add(goodsOfInventoryDOList.stream()
                                .filter(goodsOfRepertoryDO -> goodsDO.getGuid().equals(goodsOfRepertoryDO.getGoodsGuid()))
                                .findAny()
                                .get()
                                .getCount())
                );
            }
            return goodsDO;
        }).collect(Collectors.toList());

        log.info("作废库存单，回滚商品数量，需要更新的商品列表：{}", JacksonUtils.writeValueAsString(list));

        return goodsService.saveOrUpdateBatch(list);
    }

    @Override
    public Page<GoodsSumInfoRespDTO> queryGoodsRepertorySumInfo(QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO) {
        return goodsService.queryGoods(queryGoodsSumInfoReqDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean insertRepertoryAndDetail(CreateRepertoryReqDTO createRepertoryReqDTO) {

        // 插入库存单
        String guid = distributedIdService.nextRepertoryGuid();
        RepertoryDO repertoryDO = repertoryMapstruct.fromCreateRepertoryReqDTO(createRepertoryReqDTO);
        repertoryDO.setGuid(guid);
        repertoryDO.setStoreGuid(createRepertoryReqDTO.getStoreGuid());
        repertoryDO.setInvoiceNo(guid);
        repertoryDO.setInvoiceMaker(UserContextUtils.getUserName());
        repertoryDO.setStatus("1"); // 已完成状态
        log.info("插入库存单信息：{}", JacksonUtils.writeValueAsString(repertoryDO));
        save(repertoryDO);

        // 库存单对应的商品插入库中
        List<String> guidList = distributedIdService.nextBatchGoodsItemGuid(createRepertoryReqDTO.getDetailList().size());
        List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = createRepertoryReqDTO.getDetailList().stream()
                .map(inOutGoodsDTO -> {
                    GoodsOfRepertoryDO goodsOfRepertoryDO = goodsMapstruct.fromInOutGoodsOfRepertoryDTO(inOutGoodsDTO);
                    goodsOfRepertoryDO.setGuid(guidList.remove(guidList.size() - 1));
                    goodsOfRepertoryDO.setRepertoryGuid(guid);
                    return goodsOfRepertoryDO;
                }).collect(Collectors.toList());
        goodsOfRepertoryService.insertBatchGoods(goodsOfRepertoryDOS);

        // 插入商品流水
        log.info("插入商品流水记录");
        goodsSerialService.insertGoodsSerial(
                createRepertoryReqDTO
                        .getDetailList()
                        .stream()
                        .map(inOutGoodsDTO -> {
                            InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
                            insertGoodsSerialReqDTO.setInvoiceType(createRepertoryReqDTO.getInvoiceType());
                            if (createRepertoryReqDTO.getInOut() == 0) { // 入库
                                insertGoodsSerialReqDTO.setChangeNum(inOutGoodsDTO.getCount());
                            } else { // 出库
                                insertGoodsSerialReqDTO.setChangeNum(inOutGoodsDTO.getCount().negate());
                            }
                            insertGoodsSerialReqDTO.setGoodsGuid(inOutGoodsDTO.getGoodsGuid());
                            if (createRepertoryReqDTO.getInvoiceType() == 6) { // 6:期初入库
                                insertGoodsSerialReqDTO.setInvoiceNo("-");
                            } else {
                                insertGoodsSerialReqDTO.setInvoiceNo(guid);
                            }
                            insertGoodsSerialReqDTO.setUnitName(inOutGoodsDTO.getUnitName());
                            log.info("待插入的商品流水：{}", JacksonUtils.writeValueAsString(insertGoodsSerialReqDTO));
                            return insertGoodsSerialReqDTO;
                        }).collect(Collectors.toList())
        );

        return goodsService.insertBatchGoods(goodsMapstruct.fromGoodsDTOListToGoodsDOList(createRepertoryReqDTO.getDetailList()), createRepertoryReqDTO.getInOut(), createRepertoryReqDTO.getStoreGuid());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saleOutRepertory(SubstractRepertoryForTradeReqDTO substractRepertoryForTradeReqDTO) {

        List<String> inGoodsGuidList = substractRepertoryForTradeReqDTO.getDetailList().stream().map(SubstractGoodsReqDTO::getGoodsGuid).collect(Collectors.toList());
        List<GoodsDO> goodsInSQL = goodsService.list(new LambdaQueryWrapper<GoodsDO>().in(GoodsDO::getGuid, inGoodsGuidList));
        List<String> goodsInSQLGuidList = goodsInSQL.stream().map(GoodsDO::getGuid).collect(Collectors.toList());
        List<SubstractGoodsReqDTO> goodsInSQLDTOList = substractRepertoryForTradeReqDTO.getDetailList().stream().filter(substractGoodsReqDTO -> goodsInSQLGuidList.contains(substractGoodsReqDTO.getGoodsGuid())).collect(Collectors.toList());

        if (goodsInSQLDTOList.size() > 0) {

            RepertoryDO repertoryDO = new RepertoryDO();
            String repertoryGuid = distributedIdService.nextRepertoryGuid();
            repertoryDO.setInOut(Integer.valueOf(substractRepertoryForTradeReqDTO.getInvoiceType()) == 2 ? 0 : 1);
            repertoryDO.setInvoiceNo(substractRepertoryForTradeReqDTO.getInvoiceNo());
            repertoryDO.setGuid(repertoryGuid);
            repertoryDO.setStoreGuid(UserContextUtils.getStoreGuid());
            repertoryDO.setInvoiceMakeTime(DateTimeUtils.now());
            repertoryDO.setInvoiceType(substractRepertoryForTradeReqDTO.getInvoiceType());
            repertoryDO.setStatus("1"); // 已完成状态
            save(repertoryDO);

            List<String> guidList = distributedIdService.nextBatchGoodsItemGuid(goodsInSQLDTOList.size());
            List<GoodsOfRepertoryDO> goodsOfRepertoryDOS = goodsInSQLDTOList.stream()
                    .map(saleGoodsDTO -> {
                        GoodsOfRepertoryDO goodsOfRepertoryDO = new GoodsOfRepertoryDO();
                        goodsOfRepertoryDO.setGuid(guidList.remove(guidList.size() - 1));
                        goodsOfRepertoryDO.setCount(saleGoodsDTO.getCount());
                        goodsOfRepertoryDO.setGoodsGuid(saleGoodsDTO.getGoodsGuid());
                        goodsOfRepertoryDO.setRepertoryGuid(repertoryGuid);
                        goodsOfRepertoryDO.setUnitPrice(saleGoodsDTO.getUnitPrice());
                        return goodsOfRepertoryDO;
                    }).collect(Collectors.toList());

            // 库存单对应的商品插入库中
            goodsOfRepertoryService.insertBatchGoods(goodsOfRepertoryDOS);
            goodsSerialService.insertGoodsSerial(goodsInSQLDTOList.stream()
                    .map(saleGoodsDTO -> {
                        InsertGoodsSerialReqDTO insertGoodsSerialReqDTO = new InsertGoodsSerialReqDTO();
                        insertGoodsSerialReqDTO.setInvoiceType(substractRepertoryForTradeReqDTO.getInvoiceType());
                        if (substractRepertoryForTradeReqDTO.getInvoiceType() == 2) { //2：顾客退货
                            insertGoodsSerialReqDTO.setChangeNum(saleGoodsDTO.getCount());
                        } else if (substractRepertoryForTradeReqDTO.getInvoiceType() == 3) {// 3:销售出库
                            insertGoodsSerialReqDTO.setChangeNum(saleGoodsDTO.getCount().negate());
                        }
                        insertGoodsSerialReqDTO.setGoodsGuid(saleGoodsDTO.getGoodsGuid());
                        insertGoodsSerialReqDTO.setInvoiceNo(substractRepertoryForTradeReqDTO.getInvoiceNo());
                        insertGoodsSerialReqDTO.setUnitName(goodsService.queryGoodsInfo(saleGoodsDTO.getGoodsGuid()).getUnitName());
                        return insertGoodsSerialReqDTO;
                    }).collect(Collectors.toList()));
            return goodsService.modifyGoodsRepertoryNum(goodsInSQLDTOList, repertoryDO.getInOut());
        } else {
            log.info("没有可扣库存的商品！");
            return true;
        }
    }

}
