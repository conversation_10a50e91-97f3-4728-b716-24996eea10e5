package com.holderzone.erp.service.impl;

import com.alibaba.fastjson.serializer.BigDecimalCodec;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.erp.dao.GoodsMapper;
import com.holderzone.erp.entity.domain.GoodsDO;
import com.holderzone.erp.mapperstruct.GoodsMapstruct;
import com.holderzone.erp.service.GoodsSerialService;
import com.holderzone.erp.service.GoodsService;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.framework.dds.starter.utils.JacksonUtil;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.*;
import com.holderzone.saas.store.dto.erp.erpretail.req.QueryGoodsSumInfoReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.SubstractGoodsReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsClassifyAndItemRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSumInfoRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GoodsServiceImpl extends ServiceImpl<GoodsMapper, GoodsDO> implements GoodsService {

    @Autowired
    private GoodsMapper goodsMapper;

    @Autowired
    private GoodsMapstruct goodsMapstruct;

    @Autowired
    private GoodsSerialService goodsSerialService;


    @Override
    public boolean insertBatchGoods(List<GoodsDO> goodsDOS, int inOut, String storeGuid) {

        List<String> goodsGuidList = goodsDOS.stream().map(GoodsDO::getGuid).collect(Collectors.toList());
        List<GoodsDO> goodsInSQL = list(new LambdaQueryWrapper<GoodsDO>().in(GoodsDO::getGuid, goodsGuidList));
        List<String> goodsInSQLGuidList = goodsInSQL.stream().map(GoodsDO::getGuid).collect(Collectors.toList());

        if (goodsInSQL.size() > 0) {
            log.info("更新已经存在于商品表的商品信息：{}", JacksonUtils.writeValueAsString(goodsInSQL));
            List<GoodsDO> goodsDOList = goodsInSQL.stream()
                    .filter(goodsDO -> goodsInSQLGuidList.contains(goodsDO.getGuid()))
                    .map(goodsDOInSQL -> {
                        GoodsDO goodsDO = goodsDOS.stream().filter(goods -> goods.getGuid().equals(goodsDOInSQL.getGuid())).findFirst().get();
                        log.info("从传入商品中找到其对应于的数据库中的记录：{}", JacksonUtils.writeValueAsString(goodsDO));
                        if (goodsDOInSQL.getIsOpenStock() == 0) { // 若该商品被关闭了库存
                            log.info("该商品存在于数据库，但是被关闭了库存：{}", JacksonUtils.writeValueAsString(goodsDO));
                            goodsDOInSQL.setIsOpenStock(1);
                        }
                        if (inOut == 0) { // 入库
                            if (goodsDOInSQL.getRemainRepertoryNum().add(goodsDO.getRemainRepertoryNum()).compareTo(new BigDecimal(9999999.999)) > 0) {
                                throw new BusinessException(goodsDO.getGoodsName() + "商品库存数量已爆仓，请修改" + goodsDO.getGoodsName() + "入库数量");
                            }
                            goodsDOInSQL.setRemainRepertoryNum(goodsDOInSQL.getRemainRepertoryNum().add(goodsDO.getRemainRepertoryNum()));
                        } else { // 出库
                            goodsDOInSQL.setRemainRepertoryNum(goodsDOInSQL.getRemainRepertoryNum().subtract(goodsDO.getRemainRepertoryNum()));
                        }
                        goodsDOInSQL.setSafeNum(goodsDO.getSafeNum());
                        goodsDOInSQL.setStoreGuid(storeGuid);
                        return goodsDOInSQL;
                    }).collect(Collectors.toList());
            saveOrUpdateBatch(goodsDOList);
        }

        if (goodsGuidList.size() > goodsInSQL.size()) { // 有一部分已经在数据库中，还有一部分是新增商品
            log.info("将新增的部分商品插入数据库：{}", JacksonUtils.writeValueAsString(goodsInSQL));
            for (GoodsDO goodsDO : goodsDOS) {
                if (!goodsInSQLGuidList.contains(goodsDO.getGuid())) {
                    goodsDO.setStoreGuid(storeGuid);
                    if (goodsDO.getRemainRepertoryNum().compareTo(new BigDecimal(9999999.999)) > 0) {
                        throw new BusinessException(goodsDO.getGoodsName() + "商品库存数量已爆仓，请修改" + goodsDO.getGoodsName() + "入库数量");
                    }
                    save(goodsDO);
                }
            }
        }
        return true;
    }

    @Override
    public boolean modifyGoodsRepertoryNum(List<SubstractGoodsReqDTO> goodsList, int inOut) {
        log.info("修改已经开启库存的商品的数量:{},inOut={}", JacksonUtils.writeValueAsString(goodsList), inOut);
        List<String> goodsGuidList = goodsList.stream().map(SubstractGoodsReqDTO::getGoodsGuid).collect(Collectors.toList());
        List<GoodsDO> goodsInSQL = list(new LambdaQueryWrapper<GoodsDO>().in(GoodsDO::getGuid, goodsGuidList).eq(GoodsDO::getIsOpenStock, 1));
        //Bugfixed:22016当商品关闭库存的时候，直接返回失败，做好判空。
        if (ObjectUtils.isEmpty(goodsInSQL)) {
            log.info("goodsInSQL为空");
            return false;
        }
        List<GoodsDO> goodSaveOrUpdate = goodsInSQL.stream()
                .filter(goodsDOInSQL -> goodsDOInSQL.getIsOpenStock() != 0 && count(new LambdaQueryWrapper<GoodsDO>().eq(GoodsDO::getGuid, goodsDOInSQL.getGuid())) > 0)
                .map(goodsDOInSQL -> {
                    if (inOut == 0) { // 入库
                        goodsDOInSQL.setRemainRepertoryNum(goodsDOInSQL.getRemainRepertoryNum().add(goodsList.stream().filter(goods -> goods.getGoodsGuid().equals(goodsDOInSQL.getGuid())).findFirst().get().getCount()));
                    } else { // 出库
                        goodsDOInSQL.setRemainRepertoryNum(goodsDOInSQL.getRemainRepertoryNum().subtract(goodsList.stream().filter(goods -> goods.getGoodsGuid().equals(goodsDOInSQL.getGuid())).findFirst().get().getCount()));
                    }
                    return goodsDOInSQL;

                }).collect(Collectors.toList());

        //Bugfixed:22016当商品关闭库存的时候，直接返回失败，做好判空。
        if (ObjectUtils.isEmpty(goodSaveOrUpdate)) {
            log.info("goodSaveOrUpdate为空");
            return false;
        } else {
            return saveOrUpdateBatch(goodSaveOrUpdate);
        }
    }


    @Override
    public Page<GoodsSumInfoRespDTO> queryGoods(QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO) {
        IPage<GoodsDO> page = goodsMapper.queryGoodsSumInfo(new PageAdapter<GoodsDO>(queryGoodsSumInfoReqDTO), queryGoodsSumInfoReqDTO);
        List<GoodsSumInfoRespDTO> list = goodsMapstruct.fromGoodsListToGoodsRepertorySumList(page.getRecords());
        log.info("商品库存汇总返回结果:{}", JacksonUtils.writeValueAsString(list));
        return new PageAdapter<GoodsSumInfoRespDTO>(page, list);
    }

    @Override
    public List<GoodsDO> queryGoods(List<String> goodsGuidList) {
        return list(new LambdaQueryWrapper<GoodsDO>()
                .in(GoodsDO::getGuid, goodsGuidList));
    }

    @Override
    public List<GoodsClassifyAndItemRespDTO> queryGoodsList(SingleDataDTO singleDataDTO) {
        List<GoodsDO> list = list(new LambdaQueryWrapper<GoodsDO>().eq(GoodsDO::getIsOpenStock, 1)
                .like(singleDataDTO.getData() != null, GoodsDO::getGoodsName, singleDataDTO.getData()));
        Map<String, List<GoodsDO>> map = list.stream().collect(Collectors.groupingBy(GoodsDO::getGoodsClassifyGuid));
        List<GoodsClassifyAndItemRespDTO> resultList = map.entrySet().stream().map(entry -> {
            GoodsClassifyAndItemRespDTO goodsClassifyAndItemRespDTO = new GoodsClassifyAndItemRespDTO();
            goodsClassifyAndItemRespDTO.setGoodsClassifyGuid(entry.getKey());
            goodsClassifyAndItemRespDTO.setGoodsClassifyName(entry.getValue().get(0).getGoodsClassifyName());
            goodsClassifyAndItemRespDTO.setGoodsList(goodsMapstruct.fromGoodsList(entry.getValue()));
            return goodsClassifyAndItemRespDTO;
        }).collect(Collectors.toList());
        log.info("查询商品列表返回结果：{}", JacksonUtil.writeValueAsString(resultList));
        return resultList;
    }

    @Override
    public boolean modifyClassifyName(String classifyGuid, String classifyName) {
        LambdaQueryWrapper<GoodsDO> lambdaQueryWrapper = new LambdaQueryWrapper<GoodsDO>()
                .eq(GoodsDO::getGoodsClassifyGuid, classifyGuid);
        if (count(lambdaQueryWrapper) == 0) {
            log.info("新建寄存记录入参:{}", "未找到对应的商品分类Guid");
            return false;
        }
        return saveOrUpdateBatch(list(lambdaQueryWrapper).stream().map(goodsDO -> {
            goodsDO.setGoodsClassifyName(classifyName);
            return goodsDO;
        }).collect(Collectors.toList()));
    }

    @Override
    @Transactional
    public boolean modifyGoodsInfo(InOutGoodsDTO inOutGoodsDTO) {
        GoodsDO goodsDO = goodsMapstruct.fromInOutGoodsDTO(inOutGoodsDTO);
        LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper<GoodsDO>().eq(GoodsDO::getGuid, goodsDO.getGuid());
        if (count(lambdaQueryWrapper) == 0) {
            log.info("库存中未找到对应商品信息:{}", JacksonUtils.writeValueAsString(inOutGoodsDTO));
        }
        if (goodsDO.getIsOpenStock() == 0) {
            cancelRelateRepertory(goodsDO.getGuid());
        }
        return update(goodsDO, lambdaQueryWrapper);
    }

    @Override
    public boolean cancelRelateRepertory(String goodsGuid) {
        LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper<GoodsDO>().eq(GoodsDO::getGuid, goodsGuid);
        if (count(lambdaQueryWrapper) == 0) {
            log.info("取消关联库存的商品:{}", JacksonUtils.writeValueAsString(goodsGuid));
            return false;
        }

        GoodsDO goodsDO = getOne(lambdaQueryWrapper);
        goodsDO.setIsOpenStock(0);
        goodsDO.setRemainRepertoryNum(new BigDecimal(0));
        goodsDO.setSafeNum(null);

        return update(goodsDO, lambdaQueryWrapper);
    }

    @Override
    public boolean updateGoodsRepertoryNum(String goodsGuid, BigDecimal num) {
        LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper<GoodsDO>().eq(GoodsDO::getGuid, goodsGuid);
        if (count(lambdaQueryWrapper) == 0) {
            throw new BusinessException("未找到对应的商品");
        }
        GoodsDO goodsDO = getOne(lambdaQueryWrapper);
        goodsDO.setRemainRepertoryNum(num);
        return updateById(goodsDO);
    }

    @Override
    public InOutGoodsDTO queryGoodsInfo(String goodsGuid) {
        LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper<GoodsDO>().eq(GoodsDO::getGuid, goodsGuid);
        if (count(lambdaQueryWrapper) == 0) {
            throw new BusinessException("未找到对应的商品");
        }
        return goodsMapstruct.fromGoodsDO(getOne(lambdaQueryWrapper));
    }

    @Override
    public List<GoodsExportDTO> queryExportGoodsList(List<String> goodsGuids){
        if(CollectionUtils.isEmpty(goodsGuids)){
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<GoodsDO> queryWrapper = new LambdaQueryWrapper<GoodsDO>().in(GoodsDO::getGuid, goodsGuids);
        return goodsMapstruct.fromGoodsDOList(this.list(queryWrapper));
    }

    @Override
    public RepertorySumDTO queryRepertorySum(SingleDataDTO singleDataDTO) {

        RepertorySumDTO repertorySumDTO = new RepertorySumDTO();

        LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper<GoodsDO>().eq(GoodsDO::getStoreGuid, singleDataDTO.getData());
        List<GoodsDO> goodsDOS = list(lambdaQueryWrapper);

        int skuSum = count(lambdaQueryWrapper);

        SaleSkuSumDTO saleSkuSum = goodsSerialService.queryGoodsSaleSkuSum();
        repertorySumDTO.setRepertorySum(BigDecimal.valueOf(goodsDOS.stream().mapToDouble(goodsDO -> goodsDO.getRemainRepertoryNum().doubleValue()).sum()));
        repertorySumDTO.setSkuSum(skuSum);
        if (skuSum == 0) {
            repertorySumDTO.setSevenDaySkuSum("0");
            repertorySumDTO.setThirtyDaySkuSum("0");
        } else {
            repertorySumDTO.setSevenDaySkuSum(saleSkuSum.getSevenDaySkuSum() / skuSum * 100 + "%");
            repertorySumDTO.setThirtyDaySkuSum(saleSkuSum.getThirtyDaySkuSum() / skuSum * 100 + "%");
        }

        return repertorySumDTO;
    }
}
