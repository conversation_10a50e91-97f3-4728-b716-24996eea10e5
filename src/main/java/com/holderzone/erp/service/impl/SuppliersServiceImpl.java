package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.SuppliersMapper;
import com.holderzone.erp.entity.domain.CategoryDO;
import com.holderzone.erp.entity.domain.SuppliersDO;
import com.holderzone.erp.entity.domain.SuppliersQueryDO;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.erp.service.SuppliersService;
import com.holderzone.erp.service.InOutDocumentService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @className SuppliersServiceImpl
 * @date 2019-04-26 10:53:51
 * @description
 * @program holder-saas-store-erp
 */
@Service
public class SuppliersServiceImpl implements SuppliersService {

    private final ErpModuleMapper moduleMapper;
    private final SuppliersMapper suppliersMapper;
    private final InOutDocumentService inOutDocumentService;

    @Autowired
    public SuppliersServiceImpl(ErpModuleMapper moduleMapper, SuppliersMapper suppliersMapper,
                                InOutDocumentService inOutDocumentService) {
        this.moduleMapper = moduleMapper;
        this.suppliersMapper = suppliersMapper;
        this.inOutDocumentService = inOutDocumentService;
    }

    @Override
    public String createSuppliers(SuppliersReqDTO reqDTO) {
        verifyNameRepeat(reqDTO);
        String guid = IDUtils.nextId();
        SuppliersDO suppliersDO = moduleMapper.mapToSuppliersDO(reqDTO);
        suppliersDO.setGuid(guid);
        suppliersMapper.createSuppliers(suppliersDO);
        return guid;
    }

    @Override
    public String updateSuppliers(SuppliersReqDTO reqDTO) {
        verifyNameRepeat(reqDTO);
        SuppliersDO suppliersDO = moduleMapper.mapToSuppliersDO(reqDTO);
        suppliersMapper.updateSuppliers(suppliersDO);
        return suppliersDO.getGuid();
    }

    private void verifyNameRepeat(SuppliersReqDTO reqDTO) {
        int isRepeat = suppliersMapper.verifyNameRepeat(reqDTO.getName().trim(), reqDTO.getForeignKey(), reqDTO.getGuid());
        if (isRepeat> 0) {
            throw new BusinessException("供应商名称已存在");
        }
    }

    @Override
    public SuppliersDTO getSuppliersByGuid(String guid) {
        SuppliersDO suppliersDO = suppliersMapper.getSuppliersByGuid(guid);
        if (suppliersDO == null) {
            throw new BusinessException("供应商不存在");
        }
        return moduleMapper.mapToSuppliersDTO(suppliersDO);
    }

    @Override
    public Boolean enableOrDisableSuppliers(String guid) {
        suppliersMapper.enableOrDisableSuppliers(guid);
        return true;
    }

    @Override
    public Boolean deleteSuppliers(String guid) {
        if (inOutDocumentService.existDocumentOfSupplier(guid)) {
            throw new BusinessException("供应商存在单据引用，无法删除");
        }
        suppliersMapper.deleteSuppliers(guid);
        return true;
    }

    @Override
    public Page<SuppliersDTO> getSuppliersList(SuppliersQueryDTO queryDTO) {
        SuppliersQueryDO queryDO = moduleMapper.mapToSuppliersQueryDO(queryDTO);
        queryDO.setStart((queryDO.getCurrentPage() - 1) * queryDO.getPageSize());
        long total = suppliersMapper.getSuppliersListTotal(queryDO);
        if (total == 0) {
            return new Page<>(queryDO.getCurrentPage(), queryDO.getPageSize(), 0, Collections.emptyList());
        }
        List<SuppliersDO> suppliersDoList = suppliersMapper.getSuppliersList(queryDO);
        List<SuppliersDTO> suppliersDTOS = moduleMapper.mapToSuppliersDtoList(suppliersDoList);
        return new Page<>(queryDO.getCurrentPage(), queryDO.getPageSize(), total, suppliersDTOS);
    }

    @Override
    public List<SuppliersDTO> getAllOfSuppliersList(SuppliersQueryDTO queryDTO) {
        SuppliersQueryDO queryDO = moduleMapper.mapToSuppliersQueryDO(queryDTO);
        queryDO.setEnabled(1);
        queryDO.setStart(-1);
        List<SuppliersDO> suppliersDoList = suppliersMapper.getSuppliersList(queryDO);
        return moduleMapper.mapToSuppliersDtoList(suppliersDoList);
    }

    @Override
    public List<CategoryDTO> getSuppliersMaterialListAll(SuppliersMaterialQueryDTO queryDTO) {
        List<CategoryDO> categoryDOList = suppliersMapper.getSuppliersMaterialListAll(queryDTO.getSuppliersGuid(), queryDTO.getSearchName());
        return moduleMapper.mapToCategoryDTO(categoryDOList);
    }

    @Override
    public SuppliersDTO getSuppliersStatus(String supplierGuid) {
        SuppliersDO suppliersDO = suppliersMapper.selectSupplierStatus(supplierGuid);
        return moduleMapper.mapToSuppliersDTO(suppliersDO);
    }
}
