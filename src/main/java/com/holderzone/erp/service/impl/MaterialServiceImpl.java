package com.holderzone.erp.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.erp.dao.GoodsBomDOMapper;
import com.holderzone.erp.dao.MaterialDOMapper;
import com.holderzone.erp.entity.bo.UnitConvertBO;
import com.holderzone.erp.entity.domain.ChangeUnitDO;
import com.holderzone.erp.entity.domain.GoodsBomDOExample;
import com.holderzone.erp.entity.domain.MaterialDO;
import com.holderzone.erp.entity.domain.MaterialDOExample;
import com.holderzone.erp.entity.domain.read.MaterialConsumeQuery;
import com.holderzone.erp.entity.domain.read.MaterialConsumeReadDO;
import com.holderzone.erp.entity.domain.read.MaterialDocDetailReadDO;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.erp.mapperstruct.MaterialMapstruct;
import com.holderzone.erp.service.IBomService;
import com.holderzone.erp.service.IMaterialService;
import com.holderzone.erp.service.PricingSchemesService;
import com.holderzone.erp.utils.GeneratorCode;
import com.holderzone.erp.utils.PageAdapter;
import com.holderzone.erp.validate.Validator;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.erp.MaterialConsumeReqDTO;
import com.holderzone.saas.store.dto.erp.MaterialConsumeRespDTO;
import com.holderzone.saas.store.dto.erp.MaterialDTO;
import com.holderzone.saas.store.dto.erp.MaterialQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/04/25 15:22
 */
@Service
@Transactional
@Slf4j
public class MaterialServiceImpl implements IMaterialService {
    //    @Autowired
//    SqlSessionTemplate sqlSessionTemplate;
    private final MaterialDOMapper materialDOMapper;
    private final ErpModuleMapper moduleMapper;
    private final GoodsBomDOMapper goodsBomDOMapper;

    private final IBomService bomService;
    private final PricingSchemesService pricingSchemesService;

    private final MaterialMapstruct materialMapstruct;

    @Autowired
    public MaterialServiceImpl(MaterialDOMapper materialDOMapper,
                               ErpModuleMapper moduleMapper,
                               GoodsBomDOMapper goodsBomDOMapper,
                               @Lazy IBomService bomService,
                               PricingSchemesService pricingSchemesService,
                               MaterialMapstruct materialMapstruct) {
        this.materialDOMapper = materialDOMapper;
        this.moduleMapper = moduleMapper;
        this.goodsBomDOMapper = goodsBomDOMapper;
        this.bomService = bomService;
        this.pricingSchemesService = pricingSchemesService;
        this.materialMapstruct = materialMapstruct;
    }

    @Override
    public boolean add(MaterialDTO materialDTO) {
        log.info("添加物料：{}", JacksonUtils.writeValueAsString(materialDTO));
        MaterialDO materialDO = moduleMapper.mapToMaterialDO(materialDTO);
        materialDO.setGuid(IDUtils.nextId());
        int count = materialDOMapper.insertSelective(materialDO);
        log.info("物料入库数量：{}", count);
        return count > 0;
    }

    @Override
    public boolean update(MaterialDTO materialDTO) {
        MaterialDO materialDO = moduleMapper.mapToMaterialDO(materialDTO);
        MaterialDOExample example = new MaterialDOExample();
        example.createCriteria().andGuidEqualTo(materialDO.getGuid());
        List<MaterialDO> materialDOList = materialDOMapper.selectByExample(example);
        if (!materialDOList.isEmpty()) {
            //更新bom相关的单位信息
            bomService.updateBomUnit(materialDTO, materialDOList.get(0));
            pricingSchemesService.changeMaterialUnitInPricingSchemes(buildChangeUnitDo(materialDTO, materialDO));
        }
        if (StringUtils.isEmpty(materialDO.getLowestStock())) {
            materialDO.setLowestStock(BigDecimal.ZERO);
        }
        if (StringUtils.isEmpty(materialDO.getConversionMain())) {
            materialDO.setConversionMain(BigDecimal.ZERO);
        }
        return materialDOMapper.updateByExampleSelective(materialDO, example) > 0;
    }

    @Override
    public boolean delete(String guid) {
        deleteBomByMaterial(guid);
        MaterialDOExample example = new MaterialDOExample();
        example.createCriteria().andGuidEqualTo(guid);
        MaterialDO materialDO = new MaterialDO();
        materialDO.setEnabled(false);
        materialDO.setDeleted(true);
        return materialDOMapper.updateByExampleSelective(materialDO, example) > 0;
    }

    private ChangeUnitDO buildChangeUnitDo(MaterialDTO materialDTO, MaterialDO materialDO) {
        ChangeUnitDO unitDO = new ChangeUnitDO();
        unitDO.setMaterialGuid(materialDO.getGuid());
        unitDO.setNewMainUnit(materialDTO.getUnit());
        unitDO.setNewAuxiliaryUnit(materialDTO.getAuxiliaryUnit());
        unitDO.setOriginalMainUnit(materialDO.getUnit());
        unitDO.setOriginalAuxiliaryUnit(materialDO.getAuxiliaryUnit());
        return unitDO;
    }

    @Override
    public List<MaterialDTO> findByCondition(MaterialQueryDTO queryDTO, Page page) {
        if (page != null && page.getCurrentPage() <= 0) {
            page.setCurrentPage(1);
        }
        List<MaterialDO> data = materialDOMapper.findByCondition(queryDTO, page);
        return moduleMapper.mapToMaterialDTOList(data);
    }

    @Override
    public MaterialDTO findByGuid(String guid) {
        List<MaterialDO> materialDOList = materialDOMapper.findAllByWarehouseOrGuidList(null, Arrays.asList(guid));
        if (materialDOList.isEmpty()) {
            return null;
        }
        MaterialDO materialDO = materialDOList.get(0);
        //转换数据库中的默认值
        if (materialDO.getLowestStock().compareTo(BigDecimal.ZERO) == 0) {
            materialDO.setLowestStock(null);
        }
        if (materialDO.getConversionMain().compareTo(BigDecimal.ZERO) == 0) {
            materialDO.setConversionMain(null);
        }
        return moduleMapper.mapToMaterialDTO(materialDO);
    }

    @Override
    public void checkNameOrCode(MaterialDTO materialDTO) {
        if (!StringUtils.isEmpty(materialDTO.getCode()) || !StringUtils.isEmpty(materialDTO.getName())) {
            checkCode(materialDTO);
            checkName(materialDTO);
        }
    }

    /**
     * 校验code
     *
     * @param materialDTO
     */
    private void checkCode(MaterialDTO materialDTO) {
        if (!StringUtils.isEmpty(materialDTO.getCode())) {
            materialDTO.setCode(materialDTO.getCode().replaceAll(" ", ""));
            MaterialDOExample example = new MaterialDOExample();
            MaterialDOExample.Criteria criteria = example.createCriteria().andCodeEqualTo(materialDTO.getCode()).andDeletedEqualTo(false);
            buildCriteriaByCondition(materialDTO, criteria);
            if (materialDOMapper.countByExample(example) > 0) {
                throw new BusinessException("编码已存在");
            }
        }
    }

    /**
     * 校验名称
     *
     * @param materialDTO
     */
    private void checkName(MaterialDTO materialDTO) {
        if (!StringUtils.isEmpty(materialDTO.getName())) {
            MaterialDOExample example = new MaterialDOExample();
            materialDTO.setName(materialDTO.getName().replaceAll(" ", ""));
            MaterialDOExample.Criteria criteria = example.createCriteria().andNameEqualTo(materialDTO.getName()).andDeletedEqualTo(false);
            buildCriteriaByCondition(materialDTO, criteria);
            if (materialDOMapper.countByExample(example) > 0) {
                throw new BusinessException("名称已存在");
            }
        }
    }

    @Override
    public void batchCheckNameOrCode(List<MaterialDTO> materialDTOList) {
        batchCheckCode(materialDTOList);
        batchCheckName(materialDTOList);
    }

    @Override
    public void batchCheckCode(List<MaterialDTO> materialDTOList) {
        List<String> collect = materialDTOList.stream().map(MaterialDTO::getCode).map(s -> s.replaceAll(" ", "")).collect(Collectors.toList());
        if (!collect.isEmpty()) {
            MaterialDOExample example = new MaterialDOExample();
            MaterialDOExample.Criteria criteria = example.createCriteria().andCodeIn(collect).andDeletedEqualTo(false);
            buildCriteriaByCondition(materialDTOList.get(0), criteria);
            List<MaterialDO> materialDOS = materialDOMapper.selectByExample(example);
            if (!materialDOS.isEmpty()) {
                throw new BusinessException("编码:" + materialDOS.get(0).getCode() + "已存在");
            }
        }
    }

    @Override
    public void batchCheckName(List<MaterialDTO> materialDTOList) {
        List<String> collect = materialDTOList.stream().map(MaterialDTO::getName).map(s -> s.replaceAll(" ", "")).collect(Collectors.toList());
        if (!collect.isEmpty()) {
            MaterialDOExample example = new MaterialDOExample();
            MaterialDOExample.Criteria criteria = example.createCriteria().andNameIn(collect).andDeletedEqualTo(false);
            buildCriteriaByCondition(materialDTOList.get(0), criteria);
            List<MaterialDO> materialDOS = materialDOMapper.selectByExample(example);
            if (!materialDOS.isEmpty()) {
                throw new BusinessException("物料名称:" + materialDOS.get(0).getName() + "已存在");
            }
        }
    }

    @Override
    public List<MaterialDTO> findList(String storeGuid, String warehouseGuid, List<String> materialGuidList) {
        if (materialGuidList == null || materialGuidList.isEmpty()) {
            return Collections.emptyList();
        }
        List<MaterialDO> byWarehouseList = materialDOMapper.findByWarehouseOrGuidList(warehouseGuid, storeGuid, materialGuidList);
        List<String> guidList = byWarehouseList.stream().map(MaterialDO::getGuid).collect(Collectors.toList());
        List<String> notExistGuidList = materialGuidList.stream().filter(s -> !guidList.contains(s)).collect(Collectors.toList());
        //存在仓库下没有相应的物料信息
        if (!notExistGuidList.isEmpty()) {
            List<MaterialDO> notExistMaterialList = materialDOMapper.findAllByWarehouseOrGuidList(storeGuid, notExistGuidList);
            byWarehouseList.addAll(notExistMaterialList);
        }
        return moduleMapper.mapToMaterialDTOList(byWarehouseList);
    }

    @Override
    public List<MaterialDTO> findListByMaterialCodeList(String storeGuid, String warehouseGuid, List<String> materialCodeList) {
        if (materialCodeList == null || materialCodeList.isEmpty()) {
            return Collections.emptyList();
        }
        List<MaterialDO> byWarehouseList = materialDOMapper.findByWarehouseOrCodeList(warehouseGuid, storeGuid, materialCodeList);
        List<String> codeList = byWarehouseList.stream().map(MaterialDO::getCode).collect(Collectors.toList());
        List<String> notExistCodeList = materialCodeList.stream().filter(s -> !codeList.contains(s)).collect(Collectors.toList());
        //存在仓库下没有相应的物料信息
        if (!notExistCodeList.isEmpty()) {
            List<MaterialDO> notExistMaterialList = materialDOMapper.findAllByWarehouseOrCodeList(storeGuid, notExistCodeList);
            byWarehouseList.addAll(notExistMaterialList);
        }
        return moduleMapper.mapToMaterialDTOList(byWarehouseList);
    }

    @Override
    public List<MaterialDTO> findAllList(String storeGuid, List<String> materialGuidList) {
        List<MaterialDO> byWarehouseOrGuidList = materialDOMapper.findAllByWarehouseOrGuidList(storeGuid, materialGuidList);
        return moduleMapper.mapToMaterialDTOList(byWarehouseOrGuidList);
    }

    @Override
    public void fillCodeAndGuid(List<MaterialDTO> materialDTOList) {
        for (MaterialDTO materialDTO : materialDTOList) {
            materialDTO.setCode(String.valueOf(GeneratorCode.generatorCode()));
            materialDTO.setGuid(IDUtils.nextId());
        }
    }

    @Override
    public void addBatch(List<MaterialDTO> materialDTOList) {
        List<MaterialDO> materialDOS = moduleMapper.mapToMaterialDOList(materialDTOList);
        materialDOMapper.addBatch(materialDOS);
    }

    @Override
    public List<UnitConvertBO> unitAdapterMain(List<UnitConvertBO> unitConvertBOList) {
        unitAdapterMainValidate(unitConvertBOList);
        List<UnitConvertBO> resultList = new ArrayList<>(unitConvertBOList.size());
        List<String> materialGuidList = unitConvertBOList.stream().map(UnitConvertBO::getMaterialGuid).collect(Collectors.toList());
        if (materialGuidList.isEmpty()) {
            return Collections.emptyList();
        }
        List<MaterialDO> materialDOS = materialDOMapper.findAllByWarehouseOrGuidList(null, materialGuidList);
        List<String> notExitGuid = materialGuidList.stream().filter(materialGuid -> !materialDOS.stream().map(MaterialDO::getGuid).collect(Collectors.toList()).contains(materialGuid))
                .collect(Collectors.toList());
        if (!notExitGuid.isEmpty()) {
            throw new BusinessException("存在已删除物料");
        }
        for (UnitConvertBO unitConvertBO : unitConvertBOList) {
            MaterialDO material = materialDOS.stream().filter(materialDO -> unitConvertBO.getMaterialGuid().equals(materialDO.getGuid()))
                    .findFirst().orElse(null);
            if (material == null) {
                throw new BusinessException("存在已删除物料");
            }
            UnitConvertBO result = adapterMainUnit(unitConvertBO, material);
            resultList.add(result);
        }
        return resultList;
    }

    @Override
    public void changeStatus(MaterialDTO materialDTO) {
        MaterialDO materialDO = moduleMapper.mapToMaterialDO(materialDTO);
        MaterialDOExample example = new MaterialDOExample();
        example.createCriteria().andGuidEqualTo(materialDTO.getGuid());
        materialDOMapper.updateByExampleSelective(materialDO, example);
    }

    @Override
    public Page<MaterialConsumeRespDTO> materialConsumeSum(MaterialConsumeReqDTO materialConsumeReqDTO) {
        materialConsumeReqDTO.setType(10);
        IPage<MaterialConsumeReadDO> page = materialDOMapper.materialConsumeSum(new PageAdapter<>(materialConsumeReqDTO), materialConsumeReqDTO);

        if (CollectionUtils.isEmpty(page.getRecords())) {
            return new PageAdapter<>(materialConsumeReqDTO);
        }

        materialConsumeReqDTO.setType(3);
        IPage<MaterialConsumeReadDO> page2 = materialDOMapper.materialConsumeSum(new PageAdapter<>(materialConsumeReqDTO), materialConsumeReqDTO);
        Map<String, MaterialConsumeReadDO> collect1 = page2.getRecords().stream()
                .collect(Collectors.toMap(MaterialConsumeReadDO::getGuid, Function.identity()));

        for (MaterialConsumeReadDO record : page.getRecords()) {
            MaterialConsumeReadDO materialConsumeReadDO = collect1.get(record.getGuid());
            if (materialConsumeReadDO != null && materialConsumeReadDO.getCount() != null) {
                record.setCount(record.getCount().subtract(materialConsumeReadDO.getCount()));
            }
        }
        List<MaterialConsumeReadDO> records = page.getRecords();
        List<String> mGuidList = records.stream().map(MaterialConsumeReadDO::getGuid).collect(Collectors.toList());
        List<MaterialDocDetailReadDO> materialConsumeReadDOS = materialDOMapper.queryDetailList(
                new MaterialConsumeQuery(getTime(false, materialConsumeReqDTO.getStartDate()),
                        getTime(true, materialConsumeReqDTO.getEndDate()),
                        materialConsumeReqDTO.getWarehouseGuid(),
                        mGuidList));
        Map<String, Integer> docTypeMap = new HashMap<>();
        Map<String, MaterialDocDetailReadDO> collect = materialConsumeReadDOS.stream()
                .collect(Collectors.toMap(
                        MaterialDocDetailReadDO::getMaterialGuid, o -> {
                            o.setStartStock(o.getStock());
                            o.setChangeStock(o.getCount());
                            if (o.getDocType() == 0 || o.getDocType() == 1 || o.getDocType() == 2 || o.getDocType() == 3) {
                                o.setEndStock(o.getStock().add(o.getCount()));
                            } else {
                                o.setEndStock(o.getStock().subtract(o.getCount()));
                            }
                            return o;
                        },
                        (o, o2) -> {
//                            if (o.getStartStock() == null) {
//                                o.setStartStock(o2.getStock());
//                            }
                            switch (o2.getDocType()) {
                                case 2: // 盘盈入库
                                case 3: // 反结账入库
                                case 10: // 销售出库
                                case 13: // 盘亏出库
                                    break;
                                case 0: // 采购入库
                                case 1: // 其他入库
                                    o.setChangeStock(o.getChangeStock().add(o2.getCount()));
                                    break;
                                default: // 其他出库
                                    o.setChangeStock(o.getChangeStock().subtract(o2.getCount()));
                                    break;
                            }
                            docTypeMap.put(o2.getMaterialGuid(), o2.getDocType()); // 记录该物料最后一个出入库单的类型
                            o.setEndStock(o2.getEndStock());
                            return o;
                        }
                ));

        List<MaterialConsumeRespDTO> respList = page.getRecords().stream().map(materialMapstruct::fromMaterialConsumeReadDO).collect(Collectors.toList());
        respList.forEach(r -> {
            MaterialDocDetailReadDO materialDocDetailReadDO = collect.get(r.getMaterialGuid());
            Integer docType = docTypeMap.get(r.getMaterialGuid());
            if (StringUtils.isEmpty(r.getClassify())) { // 查出来的数据分类字段不可能为null 如果是null那一定是脏数据
                r.setClassify("默认分类");
            }
            if (materialDocDetailReadDO == null ||
                    (!Objects.equals(13, docType) && !Objects.equals(2, docType))) {
                // 如果最后一单不是盘点单 或者说没有除了销售单意以外的其他出入库订单 那么就将实际销售值设为 "-"
                r.setActualCount("-");
                r.setDiff("-");
                r.setDiffRate("-");
            } else {
                BigDecimal actualCount = materialDocDetailReadDO.getStartStock()
                        .add(materialDocDetailReadDO.getChangeStock())
                        .subtract(materialDocDetailReadDO.getEndStock());
                r.setActualCount(actualCount.toString());
                BigDecimal diff = actualCount.subtract(r.getCount());
                r.setDiff(diff.toString());
                String diffRate = r.getCount().compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        diff.divide(r.getCount(), 2, BigDecimal.ROUND_HALF_UP) + "%";
                r.setDiffRate(diffRate);
            }
        });
        return new PageAdapter<>(page, respList);
    }

    private void unitAdapterMainValidate(List<UnitConvertBO> unitConvertBOList) {
        if (unitConvertBOList == null || unitConvertBOList.isEmpty()) {
            throw new ParameterException("单位转换实体集合不能为空");
        }
        Validator validator = Validator.create();
        for (UnitConvertBO unitConvertBO : unitConvertBOList) {
            validator.notBlank(unitConvertBO.getMaterialGuid(), "物料guid不能为空")
                    .notBlank(unitConvertBO.getUnitGuid(), "单位guid不能为空")
                    .notNull(unitConvertBO.getCount(), "物料数量不能为空");
        }
        if (!validator.isValid()) {
            throw new ParameterException(validator.getMessage());
        }
    }

    /**
     * 根据主辅单位关系换算数量
     *
     * @param unitConvertBO
     * @param materialDO
     */
    private UnitConvertBO adapterMainUnit(UnitConvertBO unitConvertBO, MaterialDO materialDO) {
        UnitConvertBO result = new UnitConvertBO();
        result.setMaterialGuid(unitConvertBO.getMaterialGuid());
        result.setUnitName(materialDO.getUnitName());
        result.setUnitGuid(materialDO.getUnit());
        if (unitConvertBO.getUnitGuid().equals(materialDO.getAuxiliaryUnit())) {
            //辅单位处理:主单位/辅单位*数量
            if (materialDO.getConversionAuxiliary().compareTo(BigDecimal.ZERO) == 0) {
                throw new BusinessException("单位转换关系不存在");
            }
            BigDecimal divide = materialDO.getConversionMain().divide(materialDO.getConversionAuxiliary());
            BigDecimal multiply = divide.multiply(unitConvertBO.getCount());
            //设置转换为主单位后对应的库存
            result.setCount(multiply);
        } else if (!materialDO.getUnit().equals(unitConvertBO.getUnitGuid())) {
            //也不为主单位
            throw new BusinessException("单位不存在");
        } else {
            result.setCount(unitConvertBO.getCount());
        }
        return result;
    }

    /**
     * 根据物料guid删除bom配置
     *
     * @param materialGuid 物料GUID
     */
    private void deleteBomByMaterial(String materialGuid) {
        GoodsBomDOExample example = new GoodsBomDOExample();
        example.createCriteria().andMaterialGuidEqualTo(materialGuid);
        goodsBomDOMapper.deleteByExample(example);
    }

    private void buildCriteriaByCondition(MaterialDTO materialDTO, MaterialDOExample.Criteria criteria) {
        criteria = StringUtils.isEmpty(materialDTO.getStoreGuid()) ? criteria.andStoreGuidEqualTo("") : criteria.andStoreGuidEqualTo(materialDTO.getStoreGuid());
        if (!StringUtils.isEmpty(materialDTO.getGuid())) {
            criteria.andGuidNotEqualTo(materialDTO.getGuid());
        }
        if (!StringUtils.isEmpty(materialDTO.getStoreGuid())) {
            criteria.andStoreGuidEqualTo(materialDTO.getStoreGuid());
        }
    }

    private LocalDateTime getTime(boolean isMax, String date) {
        LocalDate parse = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return parse.atTime(isMax ? LocalTime.MAX : LocalTime.MIN);

    }
}
