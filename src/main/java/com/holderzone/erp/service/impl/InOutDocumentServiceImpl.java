package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.InOutDocumentDetailMapper;
import com.holderzone.erp.dao.InOutDocumentMapper;
import com.holderzone.erp.entity.bo.*;
import com.holderzone.erp.entity.domain.*;
import com.holderzone.erp.event.publisher.OrderUpdateStockPublisher;
import com.holderzone.erp.mapperstruct.InOutDocumentTransform;
import com.holderzone.erp.service.*;
import com.holderzone.erp.utils.CommonUtils;
import com.holderzone.erp.utils.DateUtil;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.erp.*;
import org.redisson.RedissonMultiLock;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.erp.entity.enumeration.DocumentInOutTypeEnum.IN_DOCUMENT;
import static com.holderzone.erp.entity.enumeration.DocumentInOutTypeEnum.OUT_DOCUMENT;
import static com.holderzone.erp.entity.enumeration.DocumentStatus.SUBMIT;
import static com.holderzone.erp.entity.enumeration.DocumentStatus.UN_SUBMIT;
import static com.holderzone.erp.entity.enumeration.DocumentTypeEnum.OUT_RETURN;

/**
 * <AUTHOR>
 * @date 2019/04/29 上午 11:18
 * @description
 */
@Service
public class InOutDocumentServiceImpl implements InOutDocumentService {

    private static final Logger log = LoggerFactory.getLogger(InOutDocumentServiceImpl.class);

    @Autowired
    private InOutDocumentMapper inOutDocumentMapper;

    @Autowired
    private InOutDocumentDetailMapper detailMapper;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private IMaterialService materialService;

    @Autowired
    private MaterialStockService materialStockService;

    @Autowired
    private PricingSchemesService pricingSchemesService;

    @Autowired
    private InOutDocumentService inOutDocumentService;

    @Autowired
    @Lazy
    private SuppliersService suppliersService;

    @Autowired
    private CheckoutDocumentService checkoutDocumentService;


    private InOutDocumentTransform inOutDocumentTransform = InOutDocumentTransform.INSTANCE;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertInOutDocumentAndDetail(InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        insertInOutDocument(inOutDocumentDTO);
        for (InOutDocumentDetailAddOrUpdateDTO detail : inOutDocumentDTO.getDetailList()) {
            detail.setGuid(IDUtils.nextId());
            detail.setDocumentGuid(inOutDocumentDTO.getGuid());
        }
        insertInOutDocumentDetail(inOutDocumentDTO.getDetailList());
    }

    @Override
    public void documentDetailOutCountValidate(String contactDocumentGuid, List<InOutDocumentDetailBO> detailBOList) {
        inDocumentStatusValidate(contactDocumentGuid);
        List<DocumentMaterialInAndReturnCountBO> materialInAndReturnCountList = selectDocumentInAndReturnCount(contactDocumentGuid);
        if (materialInAndReturnCountList.isEmpty()) {
            throw new ParameterException("没有对应的入库单物料信息");
        }
        for (InOutDocumentDetailBO detailBO : detailBOList) {
            DocumentMaterialInAndReturnCountBO materialInAndReturnCount = materialInAndReturnCountList.stream()
                    .filter(materialInAndReturn -> detailBO.getMaterialGuid().equals(materialInAndReturn.getMaterialGuid()))
                    .findFirst().orElse(null);
            if (materialInAndReturnCount == null) {
                throw new ParameterException("关联入库单没有此物料");
            }
            BigDecimal stock = materialInAndReturnCount.getInCount().subtract(materialInAndReturnCount.getReturnCount());
            if (stock.doubleValue() < detailBO.getCount().doubleValue()) {
                throw new ParameterException("物料：" + detailBO.getMaterialName() + "退货数量超过了入库数量");
            }
        }
    }

    @Override
    public void inDocumentStatusValidate(String documentGuid) {
        InOutDocumentDO inOutDocumentDO =
                inOutDocumentMapper.selectInOutDocumentStatus(documentGuid);
        if (inOutDocumentDO == null) {
            throw new ParameterException("关联单据号有误");
        }
        if (UN_SUBMIT.getStatus().equals(inOutDocumentDO.getStatus())) {
            throw new ParameterException("关联单据未提交");
        }
    }


    @Override
    public void insertInOutDocument(InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        InOutDocumentDO inOutDocumentDO = inOutDocumentTransform.inOutDocumentDtoToDo(inOutDocumentDTO);
        inOutDocumentMapper.insertInOutDocument(inOutDocumentDO);
    }

    @Override
    public void insertInOutDocumentDetail(List<InOutDocumentDetailAddOrUpdateDTO> detailList) {
        List<InOutDocumentDetailDO> detailDOList = inOutDocumentTransform.inOutDocumentDetailAddOrUpdateDtosToDos(detailList);
        addMainUnitCount(detailDOList);
        detailMapper.insertInOutDocumentDetailList(detailDOList);
    }

    /**
     * 给物料添加主单位数量
     *
     * @param detailDOList
     */
    private void addMainUnitCount(List<InOutDocumentDetailDO> detailDOList) {
        List<UnitConvertBO> unitConvertList = new ArrayList<>();
        for (InOutDocumentDetailDO detail : detailDOList) {
            UnitConvertBO unitConvertBO = new UnitConvertBO(detail.getMaterialGuid(), detail.getUnitGuid(), detail.getCount());
            unitConvertList.add(unitConvertBO);
        }
        List<UnitConvertBO> unitConvertResultList = materialService.unitAdapterMain(unitConvertList);
        if (detailDOList.size() != unitConvertResultList.size()) {
            throw new ParameterException("存在已删除物料");
        }
        for (InOutDocumentDetailDO detail : detailDOList) {
            unitConvertResultList.stream()
                    .filter(result -> result.getMaterialGuid().equals(detail.getMaterialGuid()))
                    .findFirst().ifPresent(result -> {
                detail.setMainUnitGuid(result.getUnitGuid());
                detail.setMainUnitName(result.getUnitName());
                detail.setMainUnitCount(result.getCount());
            });
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateInOutDocumentAndDetail(InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        deleteInOutDocumentAndDetail(inOutDocumentDTO.getGuid());
        insertInOutDocumentAndDetail(inOutDocumentDTO);
    }

    @Override
    public void deleteInOutDocumentAndDetail(String inOutDocumentGuid) {
        inOutDocumentMapper.deleteInOutDocumentAndDetail(inOutDocumentGuid);
    }

    @Override
    public List<InOutDocumentMaterialInfoBO> selectStoreMaterialInfo(String storeGuid, String warehouseGuid, List<String> materialGuidList) {
        List<MaterialDTO> materialInfoList = materialService.findList(storeGuid, warehouseGuid, materialGuidList);
        if (materialInfoList == null || materialInfoList.isEmpty()) {
            throw new BusinessException("没有查询到物料的信息");
        }
        if (materialGuidList.size() != materialInfoList.size()) {
            throw new BusinessException("部分物料不存在");
        }
        return inOutDocumentTransform.materialDtosToInOutDocumentMaterialInfoBos(materialInfoList);
    }

    @Override
    public List<InOutDocumentMaterialUnitPriceBO> selectSupplierMaterialUnitPrice(String supplierGuid, List<String> materGuidList) {
        PricingSchemesQueryDTO pricingSchemesQueryDTO = new PricingSchemesQueryDTO();
        pricingSchemesQueryDTO.setSuppliersGuid(supplierGuid);
        pricingSchemesQueryDTO.setList(materGuidList);
        List<PricingSchemesDTO> pricingSchemesList = pricingSchemesService.batchQueryPricingSchemesList(pricingSchemesQueryDTO);
        return inOutDocumentTransform.pricingSchemesDtosToInOutDocumentMaterialUnitPriceBOs(pricingSchemesList);
    }

    @Override
    public List<InOutDocumentDetailSelectDTO> selectMaterialListForAdd(InOutDocumentDetailQueryDTO materialQueryDTO) {
        List<InOutDocumentMaterialInfoBO> materialInfoList =
                selectStoreMaterialInfo(materialQueryDTO.getStoreGuid(), materialQueryDTO.getWarehouseGuid(), materialQueryDTO.getMaterialGuidList());

        List<InOutDocumentDetailSelectDTO> detailSelectList = materialInfoBOListToInOutDocumentDetailSelectDto(materialInfoList);
        if (!StringUtils.isEmpty(materialQueryDTO.getSupplierGuid())) {
            List<InOutDocumentMaterialUnitPriceBO> unitPriceList =
                    selectSupplierMaterialUnitPrice(materialQueryDTO.getSupplierGuid(), materialQueryDTO.getMaterialGuidList());
            setMaterialUnitPrice(detailSelectList, unitPriceList);
        } else {
            for (InOutDocumentDetailSelectDTO detailSelectDTO : detailSelectList) {
                InOutDocumentMaterialUnitDTO defaultUnit = detailSelectDTO.getMaterialUnitList().get(0);
                detailSelectDTO.setUnitGuid(Optional.ofNullable(defaultUnit).map(InOutDocumentMaterialUnitDTO::getUnitGuid).orElse(null));
                detailSelectDTO.setUnitName(Optional.ofNullable(defaultUnit).map(InOutDocumentMaterialUnitDTO::getUnitName).orElse(null));
                detailSelectDTO.setUnitPrice(BigDecimal.ZERO);
            }
        }
        for (InOutDocumentDetailSelectDTO detailSelectDTO : detailSelectList) {
            detailSelectDTO.setCount(BigDecimal.ONE);
        }
        return detailSelectList;
    }

    @Override
    public List<DocumentMaterialInAndReturnCountBO> selectDocumentInAndReturnCount(String contactDocumentGuid) {
        List<InOutDocumentDetailDO> detailList =
                detailMapper.selectDocumentInAndReturnCount(contactDocumentGuid);
        return inOutDocumentTransform.inOutDocumentDetailDosToDocumentMaterialInAndReturnCountBos(detailList);
    }


    @Override
    public List<InOutDocumentDetailSelectDTO> selectMaterialListForUpdate(String documentGuid) {
        List<InOutDocumentDetailDO> detailList = detailMapper.selectInOutDocumentDetailList(documentGuid);
        if (detailList.isEmpty()) {
            return Collections.emptyList();
        }
        List<InOutDocumentDetailSelectDTO> materialList = inOutDocumentTransform.inOutDocumentDetailDosToDetailSelectDtos(detailList);
        setStockAndUnitList(documentGuid, materialList);
        return materialList;
    }

    /**
     * 设置物料的库存和单位集合
     *
     * @param documentGuid
     * @param materialList
     */
    private void setStockAndUnitList(String documentGuid, List<InOutDocumentDetailSelectDTO> materialList) {
        InOutDocumentDO inOutDocumentDO = inOutDocumentMapper.selectStoreGuidAndWarehouseGuid(documentGuid);
        List<String> materialGuidList = materialList.stream()
                .map(InOutDocumentDetailSelectDTO::getMaterialGuid).collect(Collectors.toList());
        List<InOutDocumentMaterialInfoBO> warehouseMaterialInfoList =
                selectStoreMaterialInfo(inOutDocumentDO.getStoreGuid(), inOutDocumentDO.getWarehouseGuid(), materialGuidList);
        List<InOutDocumentDetailSelectDTO> materialStockAndUnitList = materialInfoBOListToInOutDocumentDetailSelectDto(warehouseMaterialInfoList);
        for (InOutDocumentDetailSelectDTO material : materialList) {
            InOutDocumentDetailSelectDTO materialStockAndUnit = materialStockAndUnitList.stream()
                    .filter(stockAndUnit -> material.getMaterialGuid().equals(stockAndUnit.getMaterialGuid()))
                    .findFirst().orElse(null);
            if (materialStockAndUnit != null) {
                material.setStock(materialStockAndUnit.getStock());
                material.setMaterialUnitList(materialStockAndUnit.getMaterialUnitList());
            }
        }
    }


    @Override
    public List<InOutDocumentDetailSelectDTO> selectMaterialListForReturn(String contactDocumentGuid) {
        inDocumentStatusValidate(contactDocumentGuid);
        List<InOutDocumentDetailDO> detailList = detailMapper.selectInOutDocumentDetailList(contactDocumentGuid);
        if (detailList.isEmpty()) {
            throw new BusinessException("没有查询到关联单据的物料信息");
        }
        detailList.forEach(detail -> {
            detail.setGuid(null);
            detail.setDocumentGuid(null);
        });
        detailList.removeIf(detail -> (detail.getCount().doubleValue() - detail.getReturnCount().doubleValue()) <= 0);
        if (detailList.isEmpty()) {
            return Collections.emptyList();
        }
        List<InOutDocumentDetailSelectDTO> documentDetailSelectDTOList =
                inOutDocumentTransform.inOutDocumentDetailDosToDetailSelectDtos(detailList);
        setStockAndUnitList(contactDocumentGuid, documentDetailSelectDTOList);
        return documentDetailSelectDTOList;
    }


    @Override
    public void submitInoutDocument(Integer inOutType, String documentGuid) {
        if (IN_DOCUMENT.getType().equals(inOutType)) {
            addStockByDocument(documentGuid);
        } else if (OUT_DOCUMENT.getType().equals(inOutType)) {
            reduceStockByDocument(documentGuid);
            InOutDocumentDO documentInOutType = inOutDocumentMapper.selectDocumentType(documentGuid);
            if (OUT_RETURN.getType().equals(documentInOutType.getType()) &&
                    !StringUtils.isEmpty(documentInOutType.getContactDocumentGuid())) {
                updateContactDocumentReturnCount(documentGuid, documentInOutType.getContactDocumentGuid());
            }
        } else {
            throw new BusinessException("单据出入库类型无效");
        }
        inOutDocumentMapper.submitInOutDocument(documentGuid);
    }


    @Override
    public void updateContactDocumentReturnCountWithLock(String documentGuid, String contactDocumentGuid) {
        RLock lock = redisson.getLock(contactDocumentGuid);
        boolean res = false;
        try {
            res = lock.tryLock(60 * 3, 20, TimeUnit.SECONDS);
            if (res) {
                inOutDocumentService.updateContactDocumentReturnCount(documentGuid, contactDocumentGuid);
            } else {
                throw new BusinessException("系统繁忙");
            }
        } catch (InterruptedException e) {
            throw new BusinessException("更新关联单退货数量错误", e);
        } finally {
            if (res) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.error("解锁错误", e);
                }
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateContactDocumentReturnCount(String documentGuid, String contactDocumentGuid) {
        List<InOutDocumentDetailDO> materialReturnCountList =
                detailMapper.selectOutDocumentMaterialCount(documentGuid);
        List<InOutDocumentDetailBO> detailBOList = inOutDocumentTransform.inOutDocumentDetailDosToBos(materialReturnCountList);
        documentDetailOutCountValidate(contactDocumentGuid, detailBOList);
        detailMapper.updateInDocumentDetailReturnCount(contactDocumentGuid, materialReturnCountList);
    }


    @Override
    public boolean reduceStockByDocument(String documentGuid) {
        List<InOutDocumentDetailDO> detailList = detailMapper.selectOutDocumentMaterialCount(documentGuid);
        List<UpdateStockBO> updateStockBOList = inOutDocumentTransform.inOutDocumentDetailDosToUpdateStockBos(detailList);
        return materialStockService.batchReduceStock(updateStockBOList);

    }


    @Override
    public boolean addStockByDocument(String documentGuid) {
        List<InOutDocumentDetailDO> detailList = detailMapper.selectInDocumentMaterialCount(documentGuid);
        List<UpdateStockBO> updateStockBOList = inOutDocumentTransform.inOutDocumentDetailDosToUpdateStockBos(detailList);
        return materialStockService.batchAddStock(updateStockBOList);
    }

    @Override
    public void deleteDocument(String documentGuid) {
        InOutDocumentDO inOutDocumentDO = inOutDocumentMapper.selectInOutDocumentStatus(documentGuid);

        if (SUBMIT.getStatus().equals(inOutDocumentDO.getStatus())) {
            throw new BusinessException("已提交的单子无法删除");
        }
        deleteInOutDocumentAndDetail(documentGuid);
    }

    @Override
    public List<String> selectDocumentGuidList(InOutContactDocumentQueryDTO queryDTO) {
        InOutContactDocumentQuery query = inOutDocumentTransform.inOutContactDocumentQueryDtoToQuery(queryDTO);
        return inOutDocumentMapper.selectDocumentGuidList(query);
    }

    @Override
    public InOutDocumentSelectDTO selectDocumentForUpdate(String documentGuid) {
        InOutDocumentDO inOutDocumentDO = inOutDocumentMapper.selectDocument(documentGuid);
        InOutDocumentSelectDTO inOutDocumentSelectDTO = inOutDocumentTransform.inOutDocumentDoToSelectDto(inOutDocumentDO);
        SuppliersDTO suppliersStatus = suppliersService.getSuppliersStatus(inOutDocumentSelectDTO.getSupplierGuid());
        if (suppliersStatus == null || suppliersStatus.getEnabled() == 0 || suppliersStatus.getDeleted() == 1) {
            inOutDocumentSelectDTO.setSupplierGuid(null);
            inOutDocumentSelectDTO.setSupplierName(null);
        }
        return inOutDocumentSelectDTO;
    }

    @Override
    public InOutDocumentSelectDTO selectDocumentAndDetailForSelect(String documentGuid) {
        InOutDocumentDO inOutDocumentDO = inOutDocumentMapper.selectDocumentAndDetail(documentGuid);
        if (inOutDocumentDO == null) {
            return null;
        }
        InOutDocumentSelectDTO inOutDocumentSelectDTO = inOutDocumentTransform.inOutDocumentDoToSelectDto(inOutDocumentDO);
        List<String> contactDocumentGuidList = null;
        if (IN_DOCUMENT.getType().equals(inOutDocumentSelectDTO.getInOutType())) {
            contactDocumentGuidList = inOutDocumentMapper.selectContactDocumentGuidListForInDocument(documentGuid);
        } else {
            contactDocumentGuidList = Collections.singletonList(inOutDocumentSelectDTO.getContactDocumentGuid());
        }
        inOutDocumentSelectDTO.setContactDocumentGuidList(contactDocumentGuidList);
        return inOutDocumentSelectDTO;
    }


    @Override
    public boolean existDocumentOfWarehouse(String warehouseGuid) {
        int count = inOutDocumentMapper.selectCountByWarehouseGuid(warehouseGuid);
        count += checkoutDocumentService.selectCountByWarehouseGuid(warehouseGuid);
        if (count <= 0) {
            return false;
        }
        return true;
    }

    @Override
    public boolean existDocumentOfSupplier(String supplierGuid) {
        int count = inOutDocumentMapper.selectCountBySupplierGuid(supplierGuid);
        if (count <= 0) {
            return false;
        }
        return true;
    }

    @Override
    public Page<InOutDocumentSelectDTO> selectDocumentListForPage(InOutDocumentQueryDTO queryDTO) {
        InOutDocumentQuery inOutDocumentQuery = inOutDocumentTransform.inOutDocumentQueryDtoToQuery(queryDTO);
        List<InOutDocumentDO> documentDOList = inOutDocumentMapper.selectDocumentListForPage(inOutDocumentQuery);
        List<InOutDocumentSelectDTO> documentSelectDtoList = inOutDocumentTransform.inOutDocumentDosToSelectDtos(documentDOList);
        long totalCount = inOutDocumentMapper.selectDocumentCount(inOutDocumentQuery);
        Page<InOutDocumentSelectDTO> page = new Page<>();
        page.setCurrentPage(queryDTO.getCurrentPage());
        page.setPageSize(queryDTO.getPageSize());
        page.setTotalCount(totalCount);
        page.setData(documentSelectDtoList);
        return page;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public String insertAndSubmitInOutDocument(InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        String guid = IDUtils.nextId();
        inOutDocumentDTO.setGuid(guid);
        insertInOutDocumentAndDetail(inOutDocumentDTO);
        submitInoutDocument(inOutDocumentDTO.getInOutType(), guid);
        return guid;
    }

    @Override
    public String insertAndSubmitInOutDocumentWithLock(InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        RLock lock = redisson.getLock(inOutDocumentDTO.getContactDocumentGuid());
        boolean res = false;
        try {
            res = lock.tryLock(60 * 3, 20, TimeUnit.SECONDS);
            if (res) {
                return inOutDocumentService.insertAndSubmitInOutDocument(inOutDocumentDTO);
            } else {
                throw new BusinessException("系统繁忙");
            }
        } catch (InterruptedException e) {
            throw new BusinessException("添加并提交出入库单错误", e);
        } finally {
            if (res) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.error("解锁错误", e);
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateAndSubmitInOutDocument(InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        InOutDocumentDO documentStatusAndInOutType = inOutDocumentMapper.selectDocumentStatusAndInOutType(inOutDocumentDTO.getGuid());
        if (documentStatusAndInOutType == null) {
            throw new BusinessException("不存在该单据");
        }
        if (documentStatusAndInOutType.getStatus().equals(SUBMIT.getStatus())) {
            throw new BusinessException("该单据已提交");
        }
        updateInOutDocumentAndDetail(inOutDocumentDTO);
        submitInoutDocument(inOutDocumentDTO.getInOutType(), inOutDocumentDTO.getGuid());
    }

    @Override
    public void updateAndSubmitInOutDocumentWithLock(InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        RedissonMultiLock lock = getUpdateAndSubmitInOutDocumentLock(inOutDocumentDTO);
        boolean res = false;
        try {
            res = lock.tryLock(60 * 3, 20, TimeUnit.SECONDS);
            if (res) {
                inOutDocumentService.updateAndSubmitInOutDocument(inOutDocumentDTO);
            } else {
                throw new BusinessException("系统繁忙");
            }
        } catch (InterruptedException e) {
            throw new BusinessException("提交失败", e);
        } finally {
            if (res) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.error("解锁错误", e);
                }
            }
        }
    }

    /**
     * 首先根据提交单据guid加锁，
     * 其次如果是退货单，根据关联单据加锁，防止退货数量大于入库数量
     *
     * @param inOutDocumentDTO
     * @return
     */
    private RedissonMultiLock getUpdateAndSubmitInOutDocumentLock(InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        List<RLock> lockList = new ArrayList<>();
        RLock lock = redisson.getLock(inOutDocumentDTO.getGuid());
        lockList.add(lock);
        if (OUT_RETURN.getType().equals(inOutDocumentDTO.getType()) &&
                !StringUtils.isEmpty(inOutDocumentDTO.getContactDocumentGuid())) {
            lockList.add(redisson.getLock(inOutDocumentDTO.getContactDocumentGuid()));
        }
        return new RedissonMultiLock(lockList.toArray(new RLock[0]));
    }

    @Override
    public Page<InOutDocumentSelectDTO> selectSuppliersReconciliation(SuppliersReconciliationQueryDTO queryDTO) {
        SuppliersReconciliationQueryDO queryDO = inOutDocumentTransform.suppliersReconciliationQueryDTOToDO(queryDTO);
        queryDO.setStart((queryDO.getCurrentPage() - 1) * queryDO.getPageSize());
        queryDO.setStartDate(DateUtil.dateFormat(queryDO.getStartDate()));
        queryDO.setEndDate(DateUtil.dateFormat(queryDO.getEndDate()));
        long total = inOutDocumentMapper.selectSuppliersReconciliationTotal(queryDO);
        if (total == 0) {
            return new Page<>(queryDO.getCurrentPage(), queryDO.getPageSize(), 0, Collections.emptyList());
        }
        List<InOutDocumentDO> inOutDocumentDOS = inOutDocumentMapper.selectSuppliersReconciliationList(queryDO);
        List<InOutDocumentSelectDTO> result = inOutDocumentTransform.inOutDocumentDosToSelectDtos(inOutDocumentDOS);
        return new Page<>(queryDO.getCurrentPage(), queryDO.getPageSize(), total, result);
    }

    @Override
    public Page<InOutDocumentFolwDetailSelectDTO> selectFlowDetailListForPage(InOutDocumentFlowDetailQueryDTO queryDTO) {
        InOutDocumentFlowDetailQuery query = inOutDocumentTransform.inOutDocumentFlowDetailQueryDtoToQuery(queryDTO);
        List<InOutDocumentFlowDetailDO> flowDetailDoList = detailMapper.selectInOutDocumentFlowDetailList(query);
        List<InOutDocumentFolwDetailSelectDTO> selectDTOList = inOutDocumentTransform.inOutDocumentFlowDetailsToSelectDtos(flowDetailDoList);
        long totalCount = detailMapper.selectInOutDocumentFlowDetailCount(query);
        Page<InOutDocumentFolwDetailSelectDTO> page = new Page<>();
        page.setCurrentPage(queryDTO.getCurrentPage());
        page.setPageSize(queryDTO.getPageSize());
        page.setTotalCount(totalCount);
        page.setData(selectDTOList);
        return page;
    }

    @Override
    public InOutDocumentDO selectDocumentStatus(String guid) {
        return inOutDocumentMapper.selectInOutDocumentStatus(guid);
    }

    @Override
    public BigDecimal selectSuppliersReconciliationTotalAmount(SuppliersReconciliationQueryDTO queryDTO) {
        SuppliersReconciliationQueryDO queryDO = inOutDocumentTransform.suppliersReconciliationQueryDTOToDO(queryDTO);
        BigDecimal total = inOutDocumentMapper.selectSuppliersReconciliationTotalAmount(queryDO);
        return CommonUtils.formatNumber(total, 2);
    }

    @Transactional
    @Override
    public Boolean settleSuppliersReconciliation(List<String> list) {
        inOutDocumentMapper.settleSuppliersReconciliation(list);
        return true;
    }

    @Autowired
    private OrderUpdateStockPublisher publisher;

    @Override
    public void publishOrderReduceStockMsg(OrderSkuDTO orderSkuDTO) {
        OrderSkuBO orderSkuBO = inOutDocumentTransform.orderSkuDtoToBo(orderSkuDTO);
        publisher.publish(orderSkuBO);
    }

    @Override
    public void publishOrderReduceStockMsgBatch(List<OrderSkuDTO> orderSkuDTOList) {
        List<OrderSkuBO> orderSkuBOList = inOutDocumentTransform.orderSkuDtoToBoBatch(orderSkuDTOList);
        for (OrderSkuBO orderSkuBO : orderSkuBOList) {
            publisher.publish(orderSkuBO);
        }
    }

    @Override
    public List<InOutDocumentDetailBO> completeMaterialInfo(String storeGuid, String warehouseGuid, List<InOutDocumentBomBO> inOutDocumentBomBOList) {

        List<String> materialGuidList = inOutDocumentBomBOList.stream()
                .map(InOutDocumentBomBO::getMaterialGuid).collect(Collectors.toList());
        List<InOutDocumentMaterialInfoBO> materialInfoBOS = selectStoreMaterialInfo(storeGuid, warehouseGuid, materialGuidList);
        List<InOutDocumentDetailBO> detailBOList = new ArrayList<>();
        for (InOutDocumentMaterialInfoBO materialInfoBO : materialInfoBOS) {
            InOutDocumentDetailBO detailBO = inOutDocumentTransform.inOutDocumentMaterialInfoBoToDetailBO(materialInfoBO);
            detailBO.setUnitPrice(BigDecimal.ZERO);
            detailBO.setTotalAmount(BigDecimal.ZERO);

            InOutDocumentBomBO inOutDocumentBom = inOutDocumentBomBOList.stream()
                    .filter(inOutDocumentBomBO -> inOutDocumentBomBO.getMaterialGuid().equals(materialInfoBO.getMaterialGuid()))
                    .findFirst().orElse(null);
            if (inOutDocumentBom == null) {
                detailBO.setCount(BigDecimal.ZERO);
                detailBO.setMainUnitCount(BigDecimal.ZERO);
            } else {
                detailBO.setCount(inOutDocumentBom.getUsage());
                detailBO.setMainUnitCount(inOutDocumentBom.getUsage());
            }
            detailBOList.add(detailBO);

        }
        return detailBOList;
    }

    @Override
    public String selectWarehouseGuidByDocumentGuid(String documentGuid) {
        return inOutDocumentMapper.selectWarehouseGuidByDocumentGuid(documentGuid);
    }

    @Override
    public boolean selectInOutDocumentCountByMaterial(String materialGuid) {
        int count = detailMapper.selectInOutDocumentCountByMaterial(materialGuid);
        if (count <= 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 查询出入库单详情列表
     */
    @Override
    public List<InOutDocumentSelectDTO> queryDocumentDetailList(DocumentDetailListQueryDTO queryDTO) {
        List<InOutDocumentDO> inOutDocumentDOList = inOutDocumentMapper.queryDocumentDetailList(queryDTO);
        return inOutDocumentTransform.inOutDocumentDosToSelectDtos(inOutDocumentDOList);
    }

    @Override
    public List<InOutDocumentDetailSelectDTO> importMaterialList(InOutDocumentMaterialImportDTO inOutDocumentMaterialImportDTO) {
        if (CollectionUtils.isEmpty(inOutDocumentMaterialImportDTO.getInOutDocumentMaterialDetailImportDTOList())) {
            return Collections.emptyList();
        }
        List<InOutDocumentMaterialDetailImportDTO> detailImportDTOList = inOutDocumentMaterialImportDTO.getInOutDocumentMaterialDetailImportDTOList();
        List<String> materialCodeList = new ArrayList<>(detailImportDTOList.size());
        for (int i = 0; i < detailImportDTOList.size(); i++) {
            String materialCode = detailImportDTOList.get(i).getMaterialCode();
            if (StringUtils.isEmpty(materialCode)) {
                throw new BusinessException("第" + (i + 3) + "行的编码为空");
            }
            materialCodeList.add(materialCode);
        }
        List<InOutDocumentDetailSelectDTO> inOutDocumentDetailSelectDTOS = selectMaterialListForImport(inOutDocumentMaterialImportDTO, materialCodeList);
        if (CollectionUtils.isEmpty(inOutDocumentDetailSelectDTOS)) {
            throw new BusinessException("没有查询到物料的信息");
        }
        Set<String> alreadyAddMaterialGuids = getAlreadyAddMaterialGuids(inOutDocumentMaterialImportDTO);
        // 获取数据库中的物料信息
        Map<String, InOutDocumentDetailSelectDTO> materialDTOMap = inOutDocumentDetailSelectDTOS.stream()
                .collect(Collectors.toMap(InOutDocumentDetailSelectDTO::getMaterialCode, Function.identity(),
                        (existing, replacement) -> replacement));
        // 组装返回的物料信息
        Map<String, InOutDocumentDetailSelectDTO> resultMap = new LinkedHashMap<>();
        for (int i = 0; i < detailImportDTOList.size(); i++) {
            InOutDocumentMaterialDetailImportDTO dto = detailImportDTOList.get(i);
            String materialCode = dto.getMaterialCode();
            InOutDocumentDetailSelectDTO materialDTO = materialDTOMap.get(materialCode);
            if (Objects.isNull(materialDTO)) {
                throw new BusinessException("第" + (i + 3) + "行编码为" + materialCode + "的物料不存在");
            }
            BigDecimal count = convertToBigDecimal(i, dto.getCount(), BigDecimal.ONE, BigDecimal.valueOf(999999), "入库数量", 3);
            if (Objects.isNull(dto.getUnitName())) {
                throw new BusinessException("第" + (i + 3) + "行的入库单位为空");
            }
            if (!org.apache.commons.lang3.StringUtils.equals(dto.getUnitName(), materialDTO.getUnitName())) {
                throw new BusinessException("第" + (i + 3) + "行的入库单位为" + dto.getUnitName() + "，与系统不一致");
            }
            BigDecimal unitPrice = convertToBigDecimal(i, dto.getUnitPrice(), BigDecimal.ZERO, BigDecimal.valueOf(999999.99), "入库单价", 2);
            if (Objects.nonNull(alreadyAddMaterialGuids) && alreadyAddMaterialGuids.contains(materialDTO.getMaterialGuid())) {
                continue;
            }
            if (resultMap.containsKey(materialCode)) {
                continue;
            }
            materialDTO.setCount(count);
            materialDTO.setUnitPrice(unitPrice);
            materialDTO.setTotalAmount(count.multiply(unitPrice).setScale(2, RoundingMode.HALF_UP));
            resultMap.put(materialCode, materialDTO);
        }
        return new ArrayList<>(resultMap.values());
    }

    private Set<String> getAlreadyAddMaterialGuids(InOutDocumentMaterialImportDTO inOutDocumentMaterialImportDTO) {
        if (!StringUtils.isEmpty(inOutDocumentMaterialImportDTO.getMaterialGuidList())) {
            return Arrays.stream(inOutDocumentMaterialImportDTO.getMaterialGuidList().split(",")).collect(Collectors.toSet());
        }
        return new HashSet<>();
    }

    private BigDecimal convertToBigDecimal(int row, String num, BigDecimal min, BigDecimal max, String errColum, int scale) {
        if (StringUtils.isEmpty(num)) {
            throw new BusinessException("第" + (row + 3) + "行的" + errColum + "为空");
        }
        BigDecimal bigDecimalValue;
        try {
            bigDecimalValue = new BigDecimal(num);
        } catch (NumberFormatException e) {
            throw new BusinessException("第" + (row + 3) + "行的" + errColum + "为：" + num + "，请输入正确的数量");
        }
        // 检查数值是否在 1 ~ 999999 范围内
        if (bigDecimalValue.compareTo(min) < 0 || bigDecimalValue.compareTo(max) > 0) {
            throw new BusinessException("第" + (row + 3) + "行的" + errColum + "必须在 "+ min +" 到 " + max + " 之间，当前值为：" + num);
        }
        return bigDecimalValue.setScale(scale, RoundingMode.DOWN);
    }

    private List<InOutDocumentDetailSelectDTO> selectMaterialListForImport(InOutDocumentMaterialImportDTO importDTO, List<String> materialCodeList) {
        List<MaterialDTO> materialDTOList = materialService.findListByMaterialCodeList(importDTO.getStoreGuid(),
                importDTO.getWarehouseGuid(), materialCodeList);
        List<InOutDocumentMaterialInfoBO> materialInfoList =
                inOutDocumentTransform.materialDtosToInOutDocumentMaterialInfoBos(materialDTOList);

        List<InOutDocumentDetailSelectDTO> detailSelectList = materialInfoBOListToInOutDocumentDetailSelectDto(materialInfoList);
        List<String> guidList = detailSelectList.stream().map(InOutDocumentDetailSelectDTO::getGuid).collect(Collectors.toList());
        if (!StringUtils.isEmpty(importDTO.getSupplierGuid())) {
            List<InOutDocumentMaterialUnitPriceBO> unitPriceList =
                    selectSupplierMaterialUnitPrice(importDTO.getSupplierGuid(), guidList);
            setMaterialUnitPrice(detailSelectList, unitPriceList);
        } else {
            for (InOutDocumentDetailSelectDTO detailSelectDTO : detailSelectList) {
                InOutDocumentMaterialUnitDTO defaultUnit = detailSelectDTO.getMaterialUnitList().get(0);
                detailSelectDTO.setUnitGuid(Optional.ofNullable(defaultUnit).map(InOutDocumentMaterialUnitDTO::getUnitGuid).orElse(null));
                detailSelectDTO.setUnitName(Optional.ofNullable(defaultUnit).map(InOutDocumentMaterialUnitDTO::getUnitName).orElse(null));
                detailSelectDTO.setUnitPrice(BigDecimal.ZERO);
            }
        }
        for (InOutDocumentDetailSelectDTO detailSelectDTO : detailSelectList) {
            detailSelectDTO.setCount(BigDecimal.ONE);
        }
        return detailSelectList;
    }

    private void setMaterialUnitPrice(List<InOutDocumentDetailSelectDTO> detailSelectList, List<InOutDocumentMaterialUnitPriceBO> unitPriceList) {
        for (InOutDocumentDetailSelectDTO detailSelectDTO : detailSelectList) {
            InOutDocumentMaterialUnitPriceBO unitPriceBO = unitPriceList.stream()
                    .filter(unitPrice -> detailSelectDTO.getMaterialGuid().equals(unitPrice.getMaterialGuid()))
                    .findFirst().orElse(null);
            if (unitPriceBO == null) {
                InOutDocumentMaterialUnitDTO defaultUnit = detailSelectDTO.getMaterialUnitList().get(0);
                detailSelectDTO.setUnitGuid(Optional.ofNullable(defaultUnit).map(InOutDocumentMaterialUnitDTO::getUnitGuid).orElse(null));
                detailSelectDTO.setUnitName(Optional.ofNullable(defaultUnit).map(InOutDocumentMaterialUnitDTO::getUnitName).orElse(null));
                detailSelectDTO.setUnitPrice(BigDecimal.ZERO);
            } else {
                detailSelectDTO.setUnitGuid(unitPriceBO.getUnitGuid());
                detailSelectDTO.setUnitPrice(unitPriceBO.getUnitPrice());
                if (detailSelectDTO.getMaterialUnitList() != null) {
                    String unitName = detailSelectDTO.getMaterialUnitList().stream()
                            .filter(unit -> detailSelectDTO.getUnitGuid().equals(unit.getUnitGuid()))
                            .findFirst().map(InOutDocumentMaterialUnitDTO::getUnitName).orElse(null);
                    detailSelectDTO.setUnitName(unitName);
                }
            }
        }
    }

    /**
     * List<InOutDocumentMaterialInfoBO> 转 List<InOutDocumentDetailSelectDTO>
     *
     * @param materialInfoList
     * @return
     */
    private List<InOutDocumentDetailSelectDTO> materialInfoBOListToInOutDocumentDetailSelectDto(List<InOutDocumentMaterialInfoBO> materialInfoList) {
        List<InOutDocumentDetailSelectDTO> detailSelectDTOList = new ArrayList<>();
        for (InOutDocumentMaterialInfoBO inOutDocumentMaterialInfoBO : materialInfoList) {
            InOutDocumentDetailSelectDTO detailSelectDTO =
                    inOutDocumentTransform.inOutDocumentMaterialInfoBoToInOutDocumentDetailSelectDTO(inOutDocumentMaterialInfoBO);

            //设置物料的单位列表
            List<InOutDocumentMaterialUnitBO> materialUnitBOList = inOutDocumentMaterialInfoBO.retrieveMaterialUnitList();
            List<InOutDocumentMaterialUnitDTO> materialUnitDtoList = inOutDocumentTransform.materialUnitBosToMaterialUnitDtos(materialUnitBOList);
            detailSelectDTO.setMaterialUnitList(materialUnitDtoList);
            detailSelectDTOList.add(detailSelectDTO);
        }
        return detailSelectDTOList;
    }

    private static InOutDocumentDetailQueryDTO inOutDocumentMaterialImportDTOToInOutDocumentDetailQueryDTO(InOutDocumentMaterialImportDTO inOutDocumentMaterialImportDTO) {
        InOutDocumentDetailQueryDTO inOutDocumentDetailQueryDTO = new InOutDocumentDetailQueryDTO();
        inOutDocumentDetailQueryDTO.setStoreGuid(inOutDocumentMaterialImportDTO.getStoreGuid());
        inOutDocumentDetailQueryDTO.setWarehouseGuid(inOutDocumentMaterialImportDTO.getWarehouseGuid());
        inOutDocumentDetailQueryDTO.setSupplierGuid(inOutDocumentMaterialImportDTO.getSupplierGuid());
        inOutDocumentDetailQueryDTO.setMaterialGuidList(Arrays.asList(inOutDocumentMaterialImportDTO.getMaterialGuidList().split(",")));
        return inOutDocumentDetailQueryDTO;
    }
}
