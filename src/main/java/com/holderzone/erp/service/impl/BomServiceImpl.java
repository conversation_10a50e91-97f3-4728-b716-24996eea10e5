package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.GoodsBomDOMapper;
import com.holderzone.erp.entity.bo.GoodsInfo;
import com.holderzone.erp.entity.bo.InOutDocumentBomBO;
import com.holderzone.erp.entity.bo.InOutDocumentBomQueryBO;
import com.holderzone.erp.entity.bo.UnitConvertBO;
import com.holderzone.erp.entity.domain.GoodsBomDO;
import com.holderzone.erp.entity.domain.GoodsBomDOExample;
import com.holderzone.erp.entity.domain.MaterialDO;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.erp.service.IBomService;
import com.holderzone.erp.service.IMaterialService;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.erp.GoodsBomConfigDTO;
import com.holderzone.saas.store.dto.erp.GoodsBomDTO;
import com.holderzone.saas.store.dto.erp.MaterialDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/04/29 16:17
 */
@Service
@Transactional
public class BomServiceImpl implements IBomService {
    @Autowired
    GoodsBomDOMapper goodsBomDOMapper;
    @Autowired
    ErpModuleMapper moduleMapper;

    @Autowired
    IMaterialService materialService;

    @Override
    public boolean add(GoodsBomConfigDTO goodsBom) {
        List<GoodsBomDTO> bomList = goodsBom.getBomList();
        //刪除以前的bom配置
        deleteBom(goodsBom.getGoodsGuid(), goodsBom.getGoodsSku());
        List<GoodsBomDO> goodsBomDOS = moduleMapper.mapToBomDOList(bomList);
        if (!goodsBomDOS.isEmpty()) {
            goodsBomDOS.forEach(goodsBomDO -> {
                goodsBomDO.setGuid(IDUtils.nextId());
                goodsBomDO.setGoodsGuid(goodsBom.getGoodsGuid());
                goodsBomDO.setGoodsSku(goodsBom.getGoodsSku());
            });
            return goodsBomDOMapper.insertBatch(goodsBomDOS) > 0;
        }
        return false;
    }

    @Override
    public List<GoodsBomDTO> findBomByGoods(String goodsGuid, String goodsSku) {
        List<GoodsBomDO> goodsBom = goodsBomDOMapper.findGoodsBom(goodsGuid, goodsSku);
        return moduleMapper.mapToBomDTOList(goodsBom);
    }

    @Override
    public Long countBomByMaterial(String materialGuid) {
        GoodsBomDOExample example = new GoodsBomDOExample();
        example.createCriteria().andMaterialGuidEqualTo(materialGuid);
        return goodsBomDOMapper.countByExample(example);
    }

    @Override
    public List<GoodsBomDTO> countBomTypeBySkuList(List<String> skuList) {
        List<GoodsBomDO> goodsBomDOS = goodsBomDOMapper.countMaterialTypeBySkuList(skuList);
        return moduleMapper.mapToBomDTOList(goodsBomDOS);
    }

    @Override
    public List<InOutDocumentBomBO> parseGoodsByBom(InOutDocumentBomQueryBO inOutDocumentBomQueryBO) {
//        if (StringUtils.isEmpty(inOutDocumentBomQueryBO.getStoreGuid())) {
//            throw new BusinessException("门店GUID不能为空");
//        }
        if (!inOutDocumentBomQueryBO.getGoodsInfoList().isEmpty()) {
            List<GoodsInfo> goodsInfoList = inOutDocumentBomQueryBO.getGoodsInfoList();
            //获取sku列表
            List<String> skuList = goodsInfoList.stream().map(GoodsInfo::getGoodsSku).collect(Collectors.toList());
            //插叙sku对应的bom配置信息
            List<GoodsBomDO> bomList = findBomBySku(skuList);
            if (!bomList.isEmpty()) {
                //结果集
                List<InOutDocumentBomBO> resultList = new ArrayList<>(skuList.size());
                //按照物料分组:key=materialGuid,value=该物料配置的bom信息列表
                Map<String, List<GoodsBomDO>> collect = bomList.stream().collect(Collectors.groupingBy(GoodsBomDO::getMaterialGuid));
                collect.forEach((materialGuid, goodsBomDOS) -> {
                    //计算出物料的使用总量
                    InOutDocumentBomBO inOutDocumentBomBO = calculateMaterialUsage(materialGuid, goodsBomDOS, goodsInfoList);
                    if (inOutDocumentBomBO != null) {
                        resultList.add(inOutDocumentBomBO);
                    }
                });
                return resultList;
            }
        }
        return Collections.emptyList();
    }

    @Override
    public void updateBomUnit(MaterialDTO materialDTO, MaterialDO materialDO) {
        GoodsBomDOExample example = new GoodsBomDOExample();
        GoodsBomDO goodsBomDO = new GoodsBomDO();
        //更新主单位入库的bom单位
        if (!StringUtils.isEmpty(materialDTO.getUnit())) {
            example.createCriteria().andMaterialGuidEqualTo(materialDTO.getGuid()).andUnitEqualTo(materialDO.getUnit());
            goodsBomDO.setUnit(materialDTO.getUnit());
            goodsBomDOMapper.updateByExampleSelective(goodsBomDO, example);
        }
        example.clear();
        //更新辅单位入库的bom单位
        if (!StringUtils.isEmpty(materialDTO.getAuxiliaryUnit()) && !StringUtils.isEmpty(materialDO.getAuxiliaryUnit())) {
            goodsBomDO.setUnit(materialDTO.getAuxiliaryUnit());
            example.createCriteria().andMaterialGuidEqualTo(materialDTO.getGuid()).andUnitEqualTo(materialDO.getAuxiliaryUnit());
            goodsBomDOMapper.updateByExampleSelective(goodsBomDO, example);
        }
    }

    /**
     * 根据sku查询bom配置
     *
     * @return
     */
    private List<GoodsBomDO> findBomBySku(List<String> skuList) {
        if (!skuList.isEmpty()) {
            return goodsBomDOMapper.findBomBySku(skuList);
        }
        return Collections.emptyList();
    }

    /**
     * 根据bom配置计算商品对物料的使用量
     *
     * @param materialGuid  物料GUID
     * @param bomDOList     该物料的所有bom配置信息
     * @param goodsInfoList sku信息列表
     */
    private InOutDocumentBomBO calculateMaterialUsage(String materialGuid, List<GoodsBomDO> bomDOList, List<GoodsInfo> goodsInfoList) {
        //过滤查找出使用该物料的sku商品信息列表
        List<GoodsInfo> goodsInfos = goodsInfoList.stream()
                .filter(goodsInfo -> bomDOList.stream().map(GoodsBomDO::getGoodsSku).collect(Collectors.toList()).contains(goodsInfo.getGoodsSku()))
                .collect(Collectors.toList());
        //将所有的使用量转换为主单位对应的使用量
        List<UnitConvertBO> unitConvertBOList = adapterUnitToMain(materialGuid, goodsInfos, bomDOList);
        if (unitConvertBOList.isEmpty()) {
            return null;
        }
        //计算总数
        double sum = unitConvertBOList.stream().mapToDouble(value -> value.getCount().doubleValue()).sum();
        InOutDocumentBomBO inOutDocumentBomBO = new InOutDocumentBomBO();
        inOutDocumentBomBO.setMaterialGuid(materialGuid);
        inOutDocumentBomBO.setUsage(BigDecimal.valueOf(sum));
        inOutDocumentBomBO.setUnit(unitConvertBOList.get(0).getUnitGuid());
        return inOutDocumentBomBO;
    }

    /**
     * 单位转换,根据bom配置将使用量转换为主单位对应的使用量
     *
     * @param materialGuid
     * @param goodsInfoList
     * @param bomDOList
     * @return
     */
    private List<UnitConvertBO> adapterUnitToMain(String materialGuid, List<GoodsInfo> goodsInfoList, List<GoodsBomDO> bomDOList) {
        List<UnitConvertBO> unitConvertBOList = new ArrayList<>();
        goodsInfoList.forEach(goodsInfo -> {
            //过滤该sku商品在指定物料下的bom配置信息,一个sku下只有唯一一个物料的配置信息
            List<GoodsBomDO> collect = bomDOList.stream().filter(goodsBomDO -> goodsBomDO.getGoodsSku().equals(goodsInfo.getGoodsSku())).collect(Collectors.toList());
            GoodsBomDO goodsBomDO = collect.get(0);
            UnitConvertBO unitConvertBO = new UnitConvertBO();
            unitConvertBO.setMaterialGuid(materialGuid);
            //根据bom配置的使用量计算总的数量
            unitConvertBO.setCount(goodsInfo.getCount().multiply(goodsBomDO.getUsage()));
            unitConvertBO.setUnitGuid(goodsBomDO.getUnit());
            unitConvertBOList.add(unitConvertBO);
        });
        if (!unitConvertBOList.isEmpty()) {
            return materialService.unitAdapterMain(unitConvertBOList);
        }
        return Collections.emptyList();
    }

    /**
     * 根据商品的sku删除原有的bom配置
     *
     * @param goodsGuid
     */
    private void deleteBom(String goodsGuid, String goodsSku) {
        GoodsBomDOExample example = new GoodsBomDOExample();
        example.createCriteria().andGoodsGuidEqualTo(goodsGuid).andGoodsSkuEqualTo(goodsSku);
        goodsBomDOMapper.deleteByExample(example);
    }
}
