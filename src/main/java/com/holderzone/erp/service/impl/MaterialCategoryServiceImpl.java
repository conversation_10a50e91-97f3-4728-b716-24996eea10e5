package com.holderzone.erp.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.erp.dao.MaterialCategoryDOMapper;
import com.holderzone.erp.dao.MaterialDOMapper;
import com.holderzone.erp.entity.domain.*;
import com.holderzone.erp.mapperstruct.ErpModuleMapper;
import com.holderzone.erp.service.IMaterialCategoryService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.erp.CategoryDTO;
import com.holderzone.saas.store.dto.erp.CategoryListQueryDTO;
import com.holderzone.saas.store.dto.erp.MaterialCategoryDTO;
import com.holderzone.saas.store.dto.erp.MaterialCategoryQueryDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/25 17:57
 */
@Service
@Transactional
public class MaterialCategoryServiceImpl implements IMaterialCategoryService {
    @Autowired
    MaterialCategoryDOMapper categoryDOMapper;
    @Autowired
    ErpModuleMapper moduleMapper;

    @Autowired
    MaterialDOMapper materialDOMapper;

    @Override
    public boolean add(MaterialCategoryDTO materialCategoryDTO) {
        MaterialCategoryDO materialCategoryDO = moduleMapper.mapToMaterialCategoryDO(materialCategoryDTO);
        materialCategoryDO.setGuid(IDUtils.nextId());
        categoryDOMapper.insertSelective(materialCategoryDO);
        return true;
    }

    @Override
    public boolean update(MaterialCategoryDTO materialCategoryDTO) {
        MaterialCategoryDO materialCategoryDO = moduleMapper.mapToMaterialCategoryDO(materialCategoryDTO);
        MaterialCategoryDOExample example = new MaterialCategoryDOExample();
        example.createCriteria().andGuidEqualTo(materialCategoryDTO.getGuid());
        categoryDOMapper.updateByExampleSelective(materialCategoryDO, example);
        return true;
    }

    @Override
    public MaterialCategoryDTO findByGuid(String guid) {
        MaterialCategoryDOExample example = new MaterialCategoryDOExample();
        example.createCriteria().andGuidEqualTo(guid);
        List<MaterialCategoryDO> materialCategoryDOS = categoryDOMapper.selectByExample(example);
        return materialCategoryDOS.isEmpty() ? null : moduleMapper.mapToMaterialCategoryDTO(materialCategoryDOS.get(0));
    }

    @Override
    public List<MaterialCategoryDTO> findByCondition(MaterialCategoryQueryDTO queryDTO, Page page) {
        MaterialCategoryDOExample example = new MaterialCategoryDOExample();
        example.setCurrentPage(queryDTO.getCurrentPage() <= 0 ? 1 : queryDTO.getCurrentPage());
        example.setPageSize(queryDTO.getPageSize());
        MaterialCategoryDOExample.Criteria criteria = example.createCriteria().andDeletedEqualTo(false);
        if (!StringUtils.isEmpty(queryDTO.getSearchConditions())) {
            criteria.andNameLike("%" + queryDTO.getSearchConditions() + "%");
        }
        if (!StringUtils.isEmpty(queryDTO.getStoreGuid())) {
            criteria.andStoreGuidEqualTo(queryDTO.getStoreGuid());
        } else {
            //storeGuid为空代表查询企业下的分类
            criteria.andStoreGuidEqualTo("");
        }
        example.setOrderByClause("gmt_create desc");
        List<MaterialCategoryDO> materialCategoryDOS = categoryDOMapper.selectByExample(example);
        page.setTotalCount(example.getTotalCount());
        return moduleMapper.mapToMaterialCategoryDTOList(materialCategoryDOS);
    }

    @Override
    public long countByCode(MaterialCategoryDTO materialCategoryDTO) {
        if (StringUtils.isEmpty(materialCategoryDTO.getName())) {
            throw new BusinessException("分类名称不能为空");
        }
        MaterialCategoryDOExample example = new MaterialCategoryDOExample();
        MaterialCategoryDOExample.Criteria criteria = example.createCriteria().andNameEqualTo(materialCategoryDTO.getName().replaceAll(" ", ""))
                .andDeletedEqualTo(false);
        criteria = StringUtils.isEmpty(materialCategoryDTO.getStoreGuid()) ? criteria.andStoreGuidEqualTo("") : criteria.andStoreGuidEqualTo(materialCategoryDTO.getStoreGuid());
        if (!StringUtils.isEmpty(materialCategoryDTO.getGuid())) {
            criteria.andGuidNotEqualTo(materialCategoryDTO.getGuid());
        }
        if (!StringUtils.isEmpty(materialCategoryDTO.getWarehouseGuid())) {
            criteria.andWarehouseGuidEqualTo(materialCategoryDTO.getWarehouseGuid());
        }
        return categoryDOMapper.countByExample(example);
    }

    @Override
    public List<MaterialCategoryDTO> findList(CategoryListQueryDTO categoryListQueryDTO) {
        MaterialCategoryDOExample example = new MaterialCategoryDOExample();
        MaterialCategoryDOExample.Criteria criteria = example.createCriteria().andDeletedEqualTo(false);
        if (categoryListQueryDTO.getStoreGuid() != null) {
            //storeGuid为null代表查询所有
            criteria.andStoreGuidEqualTo(categoryListQueryDTO.getStoreGuid());
        }
        example.setPageSize(1000);
        example.setOrderByClause("gmt_create desc");
        List<MaterialCategoryDO> materialCategoryDOS = categoryDOMapper.selectByExample(example);
        List<MaterialCategoryDTO> materialCategoryDTOS = moduleMapper.mapToMaterialCategoryDTOList(materialCategoryDOS);
        materialCategoryDTOS.add(0, buildDefaultCategory());
        return materialCategoryDTOS;
    }

    @Override
    public long countMaterialByCategory(String guid) {
        MaterialDOExample example = new MaterialDOExample();
        example.createCriteria().andCategoryEqualTo(guid).andDeletedEqualTo(false);
        return materialDOMapper.countByExample(example);
    }

    @Override
    public List<CategoryDTO> findCategoryAndMaterial(String storeGuid, String searchName) {
        List<CategoryDO> materialCategoryListAll = categoryDOMapper.getMaterialCategoryListAll(storeGuid, searchName);
        materialCategoryListAll.sort((o1, o2) -> {
            if (o2.getGmtCreate() == null) {
                return 1;
            } else {
                return o2.getGmtCreate().compareTo(o1.getGmtCreate());
            }
        });
        materialCategoryListAll.forEach(categoryDO -> categoryDO.getMaterialDOList().sort(Comparator.comparing(MaterialDO::getGmtCreate).reversed()));
        return moduleMapper.mapToCategoryDTO(materialCategoryListAll);
    }

    @Override
    public boolean delete(String guid) {
        updateMaterial(guid);
        MaterialCategoryDO materialCategoryDO = new MaterialCategoryDO();
        MaterialCategoryDOExample example = new MaterialCategoryDOExample();
        example.createCriteria().andGuidEqualTo(guid);
        materialCategoryDO.setDeleted(true);
        return categoryDOMapper.updateByExampleSelective(materialCategoryDO, example) > 0;
    }

    @Override
    public List<CategoryDTO> listByGuidList(SingleListDTO dto) {
        if (CollectionUtils.isEmpty(dto.getList())) {
            return Lists.newArrayList();
        }
        return categoryDOMapper.listByGuidList(dto);
    }

    /**
     * 更新物料关联到默认分类
     *
     * @param guid 物料GUID
     */
    private void updateMaterial(String guid) {
        MaterialDOExample materialDOExample = new MaterialDOExample();
        materialDOExample.createCriteria().andCategoryEqualTo(guid);
        MaterialDO materialDO = new MaterialDO();
        materialDO.setCategory("");
        materialDOMapper.updateByExampleSelective(materialDO, materialDOExample);
    }

    private MaterialCategoryDTO buildDefaultCategory() {
        MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        materialCategoryDTO.setGuid("");
        materialCategoryDTO.setName("默认分类");
        return materialCategoryDTO;
    }
}
