package com.holderzone.erp.service.impl;

import com.holderzone.erp.dao.CheckoutDocumentDetailMapper;
import com.holderzone.erp.dao.CheckoutDocumentMapper;
import com.holderzone.erp.entity.domain.CheckoutDocumentDO;
import com.holderzone.erp.entity.domain.CheckoutDocumentDetailDO;
import com.holderzone.erp.entity.domain.CheckoutDocumentQuery;
import com.holderzone.erp.mapperstruct.CheckoutDocumentTransform;
import com.holderzone.erp.service.CheckoutDocumentService;
import com.holderzone.erp.service.IMaterialService;
import com.holderzone.erp.service.InOutDocumentService;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.holderzone.erp.entity.enumeration.DocumentCheckoutResult.DEFICIT;
import static com.holderzone.erp.entity.enumeration.DocumentCheckoutResult.PROFIT;
import static com.holderzone.erp.entity.enumeration.DocumentInOutTypeEnum.IN_DOCUMENT;
import static com.holderzone.erp.entity.enumeration.DocumentInOutTypeEnum.OUT_DOCUMENT;
import static com.holderzone.erp.entity.enumeration.DocumentStatus.SUBMIT;
import static com.holderzone.erp.entity.enumeration.DocumentTypeEnum.IN_CHECK_PROFIT;
import static com.holderzone.erp.entity.enumeration.DocumentTypeEnum.OUT_CHECK_DEFICIT;
import static com.holderzone.erp.utils.DateUtil.getCurrentDate;

/**
 * <AUTHOR>
 * @date 2019/04/29 上午 11:24
 * @description
 */
@Service
public class CheckoutDocumentServiceImpl implements CheckoutDocumentService {

    private static final Logger log = LoggerFactory.getLogger(CheckoutDocumentServiceImpl.class);

    private CheckoutDocumentTransform transform = CheckoutDocumentTransform.INSTANCE;

    @Autowired
    private CheckoutDocumentMapper checkoutDocumentMapper;

    @Autowired
    private CheckoutDocumentDetailMapper detailMapper;

    @Autowired
    private IMaterialService materialService;

    @Autowired
    private InOutDocumentService inOutDocumentService;

    @Autowired
    private CheckoutDocumentService checkoutDocumentService;

    @Autowired
    private RedissonClient redisson;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertCheckoutDocumentAndDetail(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO) {
        insertCheckoutDocument(addOrUpdateDTO);
        for (CheckoutDocumentDetailAddOrUpdateDTO detail : addOrUpdateDTO.getDetailList()) {
            detail.setGuid(IDUtils.nextId());
            detail.setDocumentGuid(addOrUpdateDTO.getGuid());
        }
        insertCheckoutDocumentDetail(addOrUpdateDTO.getDetailList());
    }

    @Override
    public void insertCheckoutDocument(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO) {
        CheckoutDocumentDO checkoutDocumentDO = transform.checkoutDocumentAddOrUpdateDtoToDo(addOrUpdateDTO);
        checkoutDocumentMapper.insertCheckoutDocument(checkoutDocumentDO);
    }

    @Override
    public void insertCheckoutDocumentDetail(List<CheckoutDocumentDetailAddOrUpdateDTO> detailList) {
        List<CheckoutDocumentDetailDO> detailDOList = transform.checkoutDocumentDetailAddOrUpdateDtosToDos(detailList);
        //计算盘点结果
        detailDOList.forEach(detail -> {
            int checkoutResult = 0;
            if (detail.getCheckCount().doubleValue() > detail.getStock().doubleValue()) {
                checkoutResult = 1;
            } else if (detail.getCheckCount().doubleValue() < detail.getStock().doubleValue()) {
                checkoutResult = 2;
            }
            detail.setCheckoutResult(checkoutResult);
        });
        detailMapper.insertCheckoutDocumentDetailList(detailDOList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCheckoutDocumentAndDetail(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO) {
        deleteCheckoutDocumentAndDetail(addOrUpdateDTO.getGuid());
        insertCheckoutDocumentAndDetail(addOrUpdateDTO);
    }

    @Override
    public void deleteCheckoutDocumentAndDetail(String documentGuid) {
        checkoutDocumentMapper.deleteCheckoutDocumentAndDetail(documentGuid);
    }

    @Override
    public List<CheckoutDocumentDetailSelectDTO> selectDocumentDetailForAdd(CheckoutDocumentDetailQueryDTO queryDTO) {
        List<MaterialDTO> materialDtoList = materialService.findList(queryDTO.getStoreGuid(),queryDTO.getWarehouseGuid(), queryDTO.getMaterialGuidList());
        if (materialDtoList == null || materialDtoList.isEmpty()) {
            throw new BusinessException("没有查询到物料的信息");
        }
        if (materialDtoList.size() != queryDTO.getMaterialGuidList().size()) {
            throw new ParameterException("部分物料不存在");
        }
        return transform.materialDtosToCheckoutDocumentDetailSelectDtos(materialDtoList);
    }

    @Override
    public CheckoutDocumentSelectDTO selectDocumentAndDetailForSelect(String documentGuid) {
        CheckoutDocumentDO checkoutDocumentDO = checkoutDocumentMapper.selectDocumentAndDetail(documentGuid);
        return transform.checkoutDocumentDoToSelectDto(checkoutDocumentDO);
    }

    @Override
    public int existDocument(String documentGuid) {
        return checkoutDocumentMapper.selectDocumentCount(documentGuid);
    }

    @Override
    public CheckoutDocumentDO selectDocumentStatus(String documentGuid) {
        return checkoutDocumentMapper.selectDocumentStatus(documentGuid);
    }


    @Override
    public void submitCheckoutDocument(String guid) {
        CheckoutDocumentDO checkoutDocument = checkoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(guid);
        checkoutDocumentMapper.submitCheckoutDocument(checkoutDocument.getGuid());

        List<CheckoutDocumentDetailDO> detailDOList = detailMapper.selectDocumentDetailList(checkoutDocument.getGuid());
        InOutDocumentAddOrUpdateDTO inDocument = resolvedInDocument(checkoutDocument, detailDOList);
        if (inDocument != null) {
            inOutDocumentService.insertAndSubmitInOutDocument(inDocument);
        }
        InOutDocumentAddOrUpdateDTO outDocument = resolvedOutDocument(checkoutDocument, detailDOList);
        if (outDocument != null) {
            inOutDocumentService.insertAndSubmitInOutDocument(outDocument);
        }

    }


    @Override
    public String insertAndSubmitCheckoutDocument(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO) {
        String guid = IDUtils.nextId();
        addOrUpdateDTO.setGuid(guid);
        insertCheckoutDocumentAndDetail(addOrUpdateDTO);
        submitCheckoutDocument(addOrUpdateDTO.getGuid());
        return guid;
    }

    @Override
    public Page<CheckoutDocumentSelectDTO> selectCheckoutDocumentForPage(CheckoutDocumentQueryDTO queryDTO) {
        CheckoutDocumentQuery query = transform.checkoutDocumentQueryDtoToQuery(queryDTO);
        List<CheckoutDocumentDO> checkoutDocumentDOList = checkoutDocumentMapper.selectCheckoutDocumentList(query);
        List<CheckoutDocumentSelectDTO> selectDTOList = transform.checkoutDocumentDosToSelectDtos(checkoutDocumentDOList);
        long totalCount = checkoutDocumentMapper.selectDocumentListCount(query);
        Page<CheckoutDocumentSelectDTO> page = new Page<>();
        page.setCurrentPage(queryDTO.getCurrentPage());
        page.setPageSize(queryDTO.getPageSize());
        page.setTotalCount(totalCount);
        page.setData(selectDTOList);
        return page;
    }

    @Override
    public void updateAndSubmitCheckoutDocumentWithLock(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO) {
        RLock lock = redisson.getLock(addOrUpdateDTO.getGuid());
        boolean res = false;
        try {
            res = lock.tryLock(60*3, 20, TimeUnit.SECONDS);
            if (res) {
                checkoutDocumentService.updateAndSubmitCheckoutDocument(addOrUpdateDTO);
            } else {
                throw new BusinessException("系统繁忙");
            }
        } catch (InterruptedException e) {
            throw new BusinessException("提交盘点单出错", e);
        } finally {
            if (res) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.error("解锁错误", e);
                }

            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateAndSubmitCheckoutDocument(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO) {
        CheckoutDocumentDO checkoutDocument = checkoutDocumentMapper.selectDocumentWarehouseAndStoreAndCreateStaff(addOrUpdateDTO.getGuid());
        if (checkoutDocument == null) {
            throw new ParameterException("该单据不存在");
        }
        if (SUBMIT.getStatus().equals(checkoutDocument.getStatus())) {
            throw new ParameterException("该单据已提交");
        }
        updateCheckoutDocumentAndDetail(addOrUpdateDTO);
        submitCheckoutDocument(checkoutDocument.getGuid());
    }

    @Override
    public CheckoutDocumentSelectDTO selectDocument(String documentGuid) {
        CheckoutDocumentDO documentDO = checkoutDocumentMapper.selectDocument(documentGuid);
        return transform.checkoutDocumentDoToSelectDto(documentDO);
    }

    @Override
    public List<CheckoutDocumentDetailSelectDTO> selectDocumentDetailForUpdate(String documentGuid) {
        List<CheckoutDocumentDetailDO> detailDoList = detailMapper.selectDocumentDetailList(documentGuid);
        List<CheckoutDocumentDetailSelectDTO> detailSelectDtoList = transform.checkoutDocumentDetailDosToSelectDtos(detailDoList);
        setStock(documentGuid, detailSelectDtoList);
        return detailSelectDtoList;
    }

    @Override
    public int selectCountByWarehouseGuid(String warehouseGuid) {
        return checkoutDocumentMapper.selectCountByWarehouseGuid(warehouseGuid);

    }

    /**
     * 设置盘点物料的实时库存量
     *
     * @param documentGuid
     * @param detailSelectDtoList
     */
    private void setStock(String documentGuid, List<CheckoutDocumentDetailSelectDTO> detailSelectDtoList) {
        List<String> materialGuidList = detailSelectDtoList.stream()
                .map(CheckoutDocumentDetailSelectDTO::getMaterialGuid).collect(Collectors.toList());
        CheckoutDocumentDO checkoutDocumentDO = checkoutDocumentMapper.selectDocumentStoreGuidAndWarehouseGuid(documentGuid);
        List<MaterialDTO> materialDtoList =
                materialService.findList(checkoutDocumentDO.getStoreGuid(),checkoutDocumentDO.getWarehouseGuid(), materialGuidList);
        if (materialGuidList.size() != materialDtoList.size()) {
            throw new ParameterException("部分物料不存在");
        }
        for (CheckoutDocumentDetailSelectDTO detailSelectDTO : detailSelectDtoList) {
            MaterialDTO materialDTO = materialDtoList.stream()
                    .filter(detail -> detailSelectDTO.getMaterialGuid().equals(detail.getGuid()))
                    .findFirst().orElse(null);
            if (materialDTO == null) {
                throw new ParameterException("物料 " + detailSelectDTO.getMaterialName() + "不存在");
            }
            detailSelectDTO.setStock(materialDTO.getStock());
            detailSelectDTO.setUnitGuid(materialDTO.getUnit());
            detailSelectDTO.setUnitName(materialDTO.getUnitName());
        }
    }


    private InOutDocumentAddOrUpdateDTO resolvedOutDocument(CheckoutDocumentDO checkoutDocumentDO, List<CheckoutDocumentDetailDO> detailDOList) {
        List<InOutDocumentDetailAddOrUpdateDTO> outDocumentDetailList = new ArrayList<>();
        for (CheckoutDocumentDetailDO detailDO : detailDOList) {
            if (DEFICIT.getResult().equals(detailDO.getCheckoutResult())) {
                InOutDocumentDetailAddOrUpdateDTO outDocumentDetail = getBaseInOutDocumentDetailByCheckResult(detailDO);
                outDocumentDetail.setCount(detailDO.getStock().subtract(detailDO.getCheckCount()));
                outDocumentDetailList.add(outDocumentDetail);
            }
        }
        if (outDocumentDetailList.isEmpty()) {
            return null;
        }
        InOutDocumentAddOrUpdateDTO outDocument = getBaseInOutDocumentByCheckResult(checkoutDocumentDO);
        outDocument.setType(OUT_CHECK_DEFICIT.getType());
        outDocument.setInOutType(OUT_DOCUMENT.getType());
        outDocument.setDetailList(outDocumentDetailList);
        return outDocument;
    }

    /**
     * 根据盘点结果生成基本的出入库单明细的信息
     *
     * @param detailDO
     * @return
     */
    private InOutDocumentDetailAddOrUpdateDTO getBaseInOutDocumentDetailByCheckResult(CheckoutDocumentDetailDO detailDO) {
        InOutDocumentDetailAddOrUpdateDTO outDocumentDetail = new InOutDocumentDetailAddOrUpdateDTO();
        outDocumentDetail.setMaterialGuid(detailDO.getMaterialGuid());
        outDocumentDetail.setMaterialCode(detailDO.getMaterialCode());
        outDocumentDetail.setMaterialName(detailDO.getMaterialName());
        outDocumentDetail.setUnitGuid(detailDO.getUnitGuid());
        outDocumentDetail.setUnitName(detailDO.getUnitName());
        outDocumentDetail.setUnitPrice(BigDecimal.ZERO);
        outDocumentDetail.setTotalAmount(BigDecimal.ZERO);
        outDocumentDetail.setStock(detailDO.getStock());
        return outDocumentDetail;
    }

    /**
     * 根据盘点结果生成基本的出入库单信息
     *
     * @param checkoutDocumentDO
     * @return
     */
    private InOutDocumentAddOrUpdateDTO getBaseInOutDocumentByCheckResult(CheckoutDocumentDO checkoutDocumentDO) {
        InOutDocumentAddOrUpdateDTO inOutDocument = new InOutDocumentAddOrUpdateDTO();
        inOutDocument.setWarehouseGuid(checkoutDocumentDO.getWarehouseGuid());
        inOutDocument.setWarehouseName(checkoutDocumentDO.getWarehouseName());
        inOutDocument.setDocumentStoreGuid(checkoutDocumentDO.getStoreGuid());
        inOutDocument.setUserGuid(checkoutDocumentDO.getCreateStaffGuid());
        inOutDocument.setUserName(checkoutDocumentDO.getCreateStaffName());
        inOutDocument.setTotalAmount(BigDecimal.ZERO);
        inOutDocument.setDocumentDate(getCurrentDate());
        inOutDocument.setOperatorGuid(checkoutDocumentDO.getCreateStaffGuid());
        inOutDocument.setOperatorName(checkoutDocumentDO.getCreateStaffName());
        return inOutDocument;
    }


    /**
     * 从盘点结果中解析盘盈单
     *
     * @param checkoutDocumentDO
     * @param detailDOList
     * @return
     */
    private InOutDocumentAddOrUpdateDTO resolvedInDocument(CheckoutDocumentDO checkoutDocumentDO, List<CheckoutDocumentDetailDO> detailDOList) {
        List<InOutDocumentDetailAddOrUpdateDTO> inDocumentDetailList = new ArrayList<>();
        for (CheckoutDocumentDetailDO detailDO : detailDOList) {
            if (PROFIT.getResult().equals(detailDO.getCheckoutResult())) {
                InOutDocumentDetailAddOrUpdateDTO inDocumentDetail = getBaseInOutDocumentDetailByCheckResult(detailDO);
                inDocumentDetail.setCount(detailDO.getCheckCount().subtract(detailDO.getStock()));
                inDocumentDetailList.add(inDocumentDetail);
            }
        }
        if (inDocumentDetailList.isEmpty()) {
            return null;
        }
        InOutDocumentAddOrUpdateDTO inDocument = getBaseInOutDocumentByCheckResult(checkoutDocumentDO);
        inDocument.setInOutType(IN_DOCUMENT.getType());
        inDocument.setType(IN_CHECK_PROFIT.getType());
        inDocument.setDetailList(inDocumentDetailList);

        return inDocument;
    }


}
