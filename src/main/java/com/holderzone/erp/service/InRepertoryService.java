package com.holderzone.erp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.erp.entity.domain.RepertoryDO;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.CreateRepertoryReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.QueryGoodsSumInfoReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.QueryRepertoryManageReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.SubstractRepertoryForTradeReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSumInfoRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.RepertoryDetailInfoRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.RepertoryManageRespDTO;
import org.springframework.web.bind.annotation.RequestBody;

public interface InRepertoryService extends IService<RepertoryDO> {

    /**
     * 添加出入库单据及其明细
     *
     * @param createRepertoryReqDTO
     */
    boolean insertRepertoryAndDetail(CreateRepertoryReqDTO createRepertoryReqDTO);

    /**
     * 销售出库
     *
     * @param substractRepertoryForTradeReqDTO
     * @return
     */
    boolean saleOutRepertory(SubstractRepertoryForTradeReqDTO substractRepertoryForTradeReqDTO);

    /**
     * @param repertoryGuid
     * @return
     */
    RepertoryDetailInfoRespDTO queryRepertoryDetail(String repertoryGuid);

    /**
     * @param queryRepertoryManageReqDTO
     * @return
     */
    Page<RepertoryManageRespDTO> queryRepertoryManageList(@RequestBody QueryRepertoryManageReqDTO queryRepertoryManageReqDTO);

    /**
     * @param singleDataDTO
     * @return
     */
    boolean invalidRepertory(@RequestBody SingleDataDTO singleDataDTO);

    /**
     * @param queryGoodsSumInfoReqDTO
     * @return
     */
    Page<GoodsSumInfoRespDTO> queryGoodsRepertorySumInfo(QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO);


}
