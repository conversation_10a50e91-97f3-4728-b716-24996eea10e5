package com.holderzone.erp.service;

import com.holderzone.erp.entity.bo.*;
import com.holderzone.erp.entity.domain.InOutDocumentDO;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/29 上午 11:17
 * @description
 */
public interface InOutDocumentService {

    /**
     * 添加出入库单据及其明细
     *
     * @param inOutDocumentDTO
     */
    void insertInOutDocumentAndDetail(InOutDocumentAddOrUpdateDTO inOutDocumentDTO);


    /**
     * 在退货时，检查退货数量不能超过入库数量
     *
     * @param contactDocumentGuid
     * @param detailBOList
     */
    void documentDetailOutCountValidate(String contactDocumentGuid, List<InOutDocumentDetailBO> detailBOList);

    /**
     * 入库单的状态校验
     *
     * @param documentGuid
     */
    void inDocumentStatusValidate(String documentGuid);

    /**
     * 添加出入库单据
     *
     * @param inOutDocumentDTO
     */
    void insertInOutDocument(InOutDocumentAddOrUpdateDTO inOutDocumentDTO);

    /**
     * 添加出入库单明细
     *
     * @param detailList
     */
    void insertInOutDocumentDetail(List<InOutDocumentDetailAddOrUpdateDTO> detailList);

    /**
     * 更新出入库单据及其明细
     *
     * @param inOutDocumentDTO
     */
    void updateInOutDocumentAndDetail(InOutDocumentAddOrUpdateDTO inOutDocumentDTO);

    /**
     * 删除出入库单据及其明细
     *
     * @param inOutDocumentGuid
     */
    void deleteInOutDocumentAndDetail(String inOutDocumentGuid);

    /**
     * 查询出入库单据中物料的信息
     *
     * @param storeGuid
     * @param warehouseGuid
     * @param materialGuidList
     * @return
     */
    List<InOutDocumentMaterialInfoBO> selectStoreMaterialInfo(String storeGuid, String warehouseGuid, List<String> materialGuidList);

    /**
     * 查询供应商物料的单价
     *
     * @param supplierGuid
     * @param materGuidList
     * @return
     */
    List<InOutDocumentMaterialUnitPriceBO> selectSupplierMaterialUnitPrice(String supplierGuid, List<String> materGuidList);

    /**
     * 查询出入库单据的物料信息
     *
     * @param materialQueryDTO
     * @return
     */
    List<InOutDocumentDetailSelectDTO> selectMaterialListForAdd(InOutDocumentDetailQueryDTO materialQueryDTO);

    /**
     * 查询单据物料的入库和退货数量
     *
     * @param contactDocumentGuid
     * @return
     */
    List<DocumentMaterialInAndReturnCountBO> selectDocumentInAndReturnCount(String contactDocumentGuid);


    /**
     * 查询出入库单据的物料信息(编辑出入库单据时)
     *
     * @param documentGuid
     * @return
     */
    List<InOutDocumentDetailSelectDTO> selectMaterialListForUpdate(String documentGuid);

    /**
     * 退货时，查询关联的入库单的物料信息，排除入库数量小于等于出库数量的物料
     *
     * @param contactDocumentGuid
     * @return
     */
    List<InOutDocumentDetailSelectDTO> selectMaterialListForReturn(String contactDocumentGuid);


    /**
     * 提交出入库单据
     *
     * @param inOutType
     * @param documentGuid
     */
    void submitInoutDocument(Integer inOutType, String documentGuid);

    /**
     * 退货后，更新相应入库单的退货数量
     *
     * @param documentGuid
     * @param contactDocumentGuid
     */
    void updateContactDocumentReturnCount(String documentGuid, String contactDocumentGuid);

    /**
     * 退货后，更新相应入库单的退货数量(加锁，防止退货数量大于入库数量)
     *
     * @param documentGuid
     * @param contactDocumentGuid
     */
    void updateContactDocumentReturnCountWithLock(String documentGuid, String contactDocumentGuid);


    /**
     * 提交出库单据后，减少库存
     *
     * @param documentGuid
     */
    boolean reduceStockByDocument(String documentGuid);

    /**
     * 提交入库单据后，添加库存
     *
     * @param documentGuid
     */
    boolean addStockByDocument(String documentGuid);

    /**
     * 删除出入库单
     *
     * @param documentGuid
     */
    void deleteDocument(String documentGuid);

    /**
     * 查询关联单据，只能查询入库单据
     *
     * @param queryDTO
     * @return
     */
    List<String> selectDocumentGuidList(InOutContactDocumentQueryDTO queryDTO);

    /**
     * 查询出入库单据
     *
     * @param documentGuid
     * @return
     */
    InOutDocumentSelectDTO selectDocumentForUpdate(String documentGuid);

    /**
     * 查询出入库单据及其明细(仅查看时使用)
     *
     * @param documentGuid
     * @return
     */
    InOutDocumentSelectDTO selectDocumentAndDetailForSelect(String documentGuid);

    /**
     * 查询指定仓库时候有出入库单或者盘点单
     *
     * @param warehouseGuid
     * @return
     */
    boolean existDocumentOfWarehouse(String warehouseGuid);

    /**
     * 查询指定仓库下是否有或者盘点单
     *
     * @param supplierGuid
     * @return
     */
    boolean existDocumentOfSupplier(String supplierGuid);

    /**
     * 按照条件查询出入库单据的集合
     *
     * @param queryDTO
     * @return
     */
    Page<InOutDocumentSelectDTO> selectDocumentListForPage(InOutDocumentQueryDTO queryDTO);

    /**
     * 添加并且提交出入库单据
     *
     * @param inOutDocumentDTO
     */
    String insertAndSubmitInOutDocument(InOutDocumentAddOrUpdateDTO inOutDocumentDTO);

    /**
     * 添加并且提交出入库单据(加锁，防止添加出库单时，出库数量大于入库数量)
     *
     * @param inOutDocumentDTO
     * @return
     */
    String insertAndSubmitInOutDocumentWithLock(InOutDocumentAddOrUpdateDTO inOutDocumentDTO);

    /**
     * 更新并提交出入库单据
     *
     * @param inOutDocumentDTO
     */
    void updateAndSubmitInOutDocument(InOutDocumentAddOrUpdateDTO inOutDocumentDTO);

    /**
     * 更新并提交出入库单(加锁，防止重复提交)
     *
     * @param inOutDocumentDTO
     */
    void updateAndSubmitInOutDocumentWithLock(InOutDocumentAddOrUpdateDTO inOutDocumentDTO);

    /**
     * 查询出入库流水明细(分页)
     *
     * @param queryDTO
     * @return
     */
    Page<InOutDocumentFolwDetailSelectDTO> selectFlowDetailListForPage(InOutDocumentFlowDetailQueryDTO queryDTO);

    /**
     * 查询单据状态
     *
     * @param guid
     * @return
     */
    InOutDocumentDO selectDocumentStatus(String guid);

    /**
     * 供应商对账表(分页)
     *
     * @param queryDTO
     * @return
     */
    Page<InOutDocumentSelectDTO> selectSuppliersReconciliation(SuppliersReconciliationQueryDTO queryDTO);

    /**
     * 结算总金额
     */
    BigDecimal selectSuppliersReconciliationTotalAmount(SuppliersReconciliationQueryDTO queryDTO);

    /**
     * 结算
     */
    Boolean settleSuppliersReconciliation(List<String> list);


    /**
     * 发布因下单而引起的扣减库存消息
     *
     * @param orderSkuDTO
     */
    void publishOrderReduceStockMsg(OrderSkuDTO orderSkuDTO);

    /**
     * 发布因下单而引起的扣减库存消息(批量)
     *
     * @param orderSkuDTOList
     */
    void publishOrderReduceStockMsgBatch(List<OrderSkuDTO> orderSkuDTOList);

    /**
     * 完善物料信息
     *
     * @param storeGuid
     * @param warehouseGuid
     * @param inOutDocumentBomBOList
     * @return
     */
    List<InOutDocumentDetailBO> completeMaterialInfo(String storeGuid, String warehouseGuid, List<InOutDocumentBomBO> inOutDocumentBomBOList);

    /**
     * 查詢出入库单据的仓库
     *
     * @param documentGuid
     * @return
     */
    String selectWarehouseGuidByDocumentGuid(String documentGuid);

    /**
     * 根据物料guid查询出入库的数量
     *
     * @param materialGuid
     * @return
     */
    boolean selectInOutDocumentCountByMaterial(String materialGuid);

    /**
     * 查询出入库单详情列表
     */
    List<InOutDocumentSelectDTO> queryDocumentDetailList(DocumentDetailListQueryDTO queryDTO);

    /**
     * 新建出入库单据时批量导入物料
     *
     * @param inOutDocumentMaterialImportDTO
     * @return
     */
    List<InOutDocumentDetailSelectDTO> importMaterialList(InOutDocumentMaterialImportDTO inOutDocumentMaterialImportDTO);
}
