package com.holderzone.erp.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;

import java.util.List;

/**
 * <AUTHOR>
 * @className SuppliersService
 * @date 2019-04-26 10:53:32
 * @description
 * @program holder-saas-store-erp
 */
public interface SuppliersService {

    /**
     * 新建供应商
     */
    String createSuppliers(SuppliersReqDTO reqDTO);

    /**
     * 更新供应商
     */
    String updateSuppliers(SuppliersReqDTO reqDTO);

    /**
     * 查询供应商信息
     */
    SuppliersDTO getSuppliersByGuid(String guid);

    /**
     * 启禁用供应商
     */
    Boolean enableOrDisableSuppliers(String guid);

    /**
     * 删除供应商
     */
    Boolean deleteSuppliers(String guid);

    /**
     * 供应商列表
     */
    Page<SuppliersDTO> getSuppliersList(SuppliersQueryDTO queryDTO);

    /**
     * 供应商下拉列表
     */
    List<SuppliersDTO> getAllOfSuppliersList(SuppliersQueryDTO queryDTO);

    /**
     * 查询供应商正常状态供货列表
     */
    List<CategoryDTO> getSuppliersMaterialListAll(SuppliersMaterialQueryDTO queryDTO);

    /**
     * 根据供应商guid查询供应商状态
     *
     * @param supplierGuid
     * @return
     */
    SuppliersDTO getSuppliersStatus(String supplierGuid);
}
