package com.holderzone.erp.service;

import com.holderzone.erp.entity.bo.UnitConvertBO;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.MaterialConsumeReqDTO;
import com.holderzone.saas.store.dto.erp.MaterialConsumeRespDTO;
import com.holderzone.saas.store.dto.erp.MaterialDTO;
import com.holderzone.saas.store.dto.erp.MaterialQueryDTO;

import java.util.List;

/**
 * 物料service
 *
 * <AUTHOR>
 * @date 2019/04/25 15:10
 */
public interface IMaterialService {

    boolean add(MaterialDTO materialDTO);

    boolean update(MaterialDTO materialDTO);

    /**
     * 逻辑删除物料信息
     *
     * @param guid
     * @return
     */
    boolean delete(String guid);

    /**
     * 根据条件查询物料信息
     *
     * @param queryDTO
     * @param page
     */
    List<MaterialDTO> findByCondition(MaterialQueryDTO queryDTO, Page page);

    /**
     * 根据GUID查询物料信息
     *
     * @param guid
     */
    MaterialDTO findByGuid(String guid);

    /**
     * 检查给定code或者name是否合法
     *
     * @return
     */
    void checkNameOrCode(MaterialDTO materialDTO);


    void batchCheckName(List<MaterialDTO> materialDTOList);

    void batchCheckCode(List<MaterialDTO> materialDTOList);

    void batchCheckNameOrCode(List<MaterialDTO> materialDTOList);

    /**
     * 根据门店或者物料GUID列表查询物料信息
     *
     * @param storeGuid
     * @param materialGuidList
     * @return
     */
    List<MaterialDTO> findList(String storeGuid,String warehouseGuid, List<String> materialGuidList);

    /**
     * 根据门店或者物料编码列表查询物料信息
     *
     * @param storeGuid
     * @param materialCodeList
     * @return
     */
    List<MaterialDTO> findListByMaterialCodeList(String storeGuid, String warehouseGuid, List<String> materialCodeList);

    /**
     * 根据门店或者物料GUID列表查询物料信息
     *
     * @param storeGuid
     * @param materialGuidList
     * @return
     */
    List<MaterialDTO> findAllList(String storeGuid, List<String> materialGuidList);

    /**
     * 根据自动生成的编码填充物料code,guid属性
     *
     * @param materialDTOList
     */
    void fillCodeAndGuid(List<MaterialDTO> materialDTOList);

    /**
     * 批量添加物料
     *
     * @param materialDTOList
     */
    void addBatch(List<MaterialDTO> materialDTOList);

    /**
     * 单位转换,根据给定的单位转换为主单位对应的参数
     * @param unitConvertBOList
     * @return
     */
    List<UnitConvertBO> unitAdapterMain(List<UnitConvertBO> unitConvertBOList);

    /**
     * 启用/禁用物料信息
     * @param materialDTO
     */
    void changeStatus(MaterialDTO materialDTO);

    /**
     * 根据门店或者物料GUID列表查询物料信息
     *
     * @param storeGuid
     * @param materialGuidList
     * @return
     */
//    List<MaterialDTO> findList(String storeGuid,String warehouseGuid, List<String> materialGuidList);

    /**
     * 物料耗用汇总
     *
     * @param materialConsumeReqDTO
     * @return
     */
    Page<MaterialConsumeRespDTO> materialConsumeSum(MaterialConsumeReqDTO materialConsumeReqDTO);
}
