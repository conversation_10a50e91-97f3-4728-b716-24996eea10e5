package com.holderzone.erp.service;

import com.holderzone.erp.entity.domain.CheckoutDocumentDO;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/29 上午 11:22
 * @description
 */
public interface CheckoutDocumentService {

    /**
     * 添加盘点单及其明细
     *
     * @param addOrUpdateDTO
     */
    void insertCheckoutDocumentAndDetail(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO);

    /**
     * 添加盘点单
     *
     * @param addOrUpdateDTO
     */
    void insertCheckoutDocument(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO);

    /**
     * 添加明细
     *
     * @param detailList
     */
    void insertCheckoutDocumentDetail(List<CheckoutDocumentDetailAddOrUpdateDTO> detailList);

    /**
     * 更新盘点单及其明细
     *
     * @param addOrUpdateDTO
     */
    void updateCheckoutDocumentAndDetail(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO);

    /**
     * 删除盘点单及其明细
     *
     * @param documentGuid
     */
    void deleteCheckoutDocumentAndDetail(String documentGuid);

    /**
     * 查询物料信息(新增物料时使用)
     *
     * @param queryDTO
     * @return
     */
    List<CheckoutDocumentDetailSelectDTO> selectDocumentDetailForAdd(CheckoutDocumentDetailQueryDTO queryDTO);


    /**
     * 查询盘点单及其明细(查看时使用)
     *
     * @param documentGuid
     * @return
     */
    CheckoutDocumentSelectDTO selectDocumentAndDetailForSelect(String documentGuid);

    /**
     * 单据是否存在
     *
     * @param documentGuid
     * @return
     */
    int existDocument(String documentGuid);

    /**
     * 根据单据guid查询单据
     *
     * @param documentGuid
     * @return
     */
    CheckoutDocumentDO selectDocumentStatus(String documentGuid);

    /**
     * 提交盘点单
     *
     * @param guid
     */
    void submitCheckoutDocument(String guid);

    /**
     * 更新并提交盘点单(加锁，防止重复提交)
     *
     * @param addOrUpdateDTO
     */
    void updateAndSubmitCheckoutDocumentWithLock(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO);

    /**
     * 保存并且提交盘点单
     *
     * @param addOrUpdateDTO
     * @return
     */
    String insertAndSubmitCheckoutDocument(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO);

    /**
     * 分页查询盘点单
     *
     * @param queryDTO
     * @return
     */
    Page<CheckoutDocumentSelectDTO> selectCheckoutDocumentForPage(CheckoutDocumentQueryDTO queryDTO);

    /**
     * 更新并且提交盘点单
     *
     * @param addOrUpdateDTO
     */
    void updateAndSubmitCheckoutDocument(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO);

    /**
     * 根据单据guid查询单据
     *
     * @param documentGuid
     * @return
     */
    CheckoutDocumentSelectDTO selectDocument(String documentGuid);

    /**
     * 查询物料明细(编辑时使用)
     *
     * @param documentGuid
     * @return
     */
    List<CheckoutDocumentDetailSelectDTO> selectDocumentDetailForUpdate(String documentGuid);

    /**
     * 查询指定仓库时候有出入库单或者盘点单
     *
     * @param warehouseGuid
     * @return
     */
    int selectCountByWarehouseGuid(String warehouseGuid);
}
