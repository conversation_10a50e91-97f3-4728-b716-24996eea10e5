package com.holderzone.erp.service.feign;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.item.req.ErpTypeItemQueryDTO;
import com.holderzone.saas.store.dto.item.resp.ErpItemDTO;
import com.holderzone.saas.store.dto.item.resp.ErpTypeDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/30 11:19
 */
@FeignClient("holder-saas-store-item")
public interface ItemFeignService {
    @PostMapping("erp/list_type_of_store")
    /**获取分类列表*/
    List<ErpTypeDTO> listTypeStore(@RequestBody ErpTypeItemQueryDTO erpTypeItemQueryDTO);
    @PostMapping("erp/list_item_of_type")
    /**获取分类下商品分页列表*/
    Page<ErpItemDTO> listItemStore(@RequestBody ErpTypeItemQueryDTO erpTypeItemQueryDTO);
    @PostMapping("erp/list_item_of_store")
    /**获取门店下商品分页列表*/
    Page<ErpItemDTO> listAllItemStore(@RequestBody ErpTypeItemQueryDTO erpTypeItemQueryDTO);
    @PostMapping("erp/list_item_by_name")
    /**获取门店下商品分页列表*/
    Page<ErpItemDTO> listItemByName(@RequestBody ErpTypeItemQueryDTO erpTypeItemQueryDTO);
}
