package com.holderzone.erp.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.CategoryDTO;
import com.holderzone.saas.store.dto.erp.CategoryListQueryDTO;
import com.holderzone.saas.store.dto.erp.MaterialCategoryDTO;
import com.holderzone.saas.store.dto.erp.MaterialCategoryQueryDTO;
import com.holderzone.saas.store.dto.order.common.SingleListDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/25 17:56
 */
public interface IMaterialCategoryService {

    boolean add(MaterialCategoryDTO materialCategoryDTO);

    boolean update(MaterialCategoryDTO materialCategoryDTO);

    /**
     * 根据guid查询对应的分类信息
     *
     * @param guid
     */
    MaterialCategoryDTO findByGuid(String guid);

    /**
     * 条件查询物料分类列表
     *
     * @param queryDTO
     * @param page
     * @return
     */
    List<MaterialCategoryDTO> findByCondition(MaterialCategoryQueryDTO queryDTO, Page page);

    long countByCode(MaterialCategoryDTO materialCategoryDTO);

    /**
     * 查询所有的物料分类列表
     *
     * @param categoryListQueryDTO
     */
    List<MaterialCategoryDTO> findList(CategoryListQueryDTO categoryListQueryDTO);

    /**
     * 根据分类查询该分类下的物料数
     *
     * @param guid
     * @return
     */
    long countMaterialByCategory(String guid);

    /**
     * 查询各分类下的物料关系
     *
     * @param storeGuid
     * @param searchName
     * @return
     */
    List<CategoryDTO> findCategoryAndMaterial(String storeGuid, String searchName);

    /**
     * 删除分类
     *
     * @param guid
     * @return
     */
    boolean delete(String guid);

    List<CategoryDTO> listByGuidList(SingleListDTO dto);
}
