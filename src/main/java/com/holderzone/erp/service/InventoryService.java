package com.holderzone.erp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.erp.entity.domain.InventoryDO;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.CreateInventoryReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.InventoryOverviewReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.InventoryDetailRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.InventoryManageRespDTO;

public interface InventoryService extends IService<InventoryDO> {

    /**
     * 新建盘点单
     *
     * @param createInventoryReqDTO
     */
    String createInventory(CreateInventoryReqDTO createInventoryReqDTO);

    /**
     * 查询盘点单
     *
     * @param singleDataDTO
     */
    InventoryDetailRespDTO queryInventoryDetail(SingleDataDTO singleDataDTO);

    /**
     * 查询盘点单
     *
     * @param inventoryOverviewReqDTO
     */
    Page<InventoryManageRespDTO> queryInventoryOverview(InventoryOverviewReqDTO inventoryOverviewReqDTO);

    /**
     * 作废
     *
     * @param inventoryGuid
     */
    boolean invalidInventory(String inventoryGuid);
}
