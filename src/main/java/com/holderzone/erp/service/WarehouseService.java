package com.holderzone.erp.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.WarehouseDTO;
import com.holderzone.saas.store.dto.erp.WarehouseQueryDTO;
import com.holderzone.saas.store.dto.erp.WarehouseReqDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @className WarehouseService
 * @date 2019-04-24 14:49:07
 * @description
 * @program holder-saas-store-erp
 */
public interface WarehouseService {

    /**
     * 创建仓库
     */
    String createWarehouse(WarehouseReqDTO reqDTO);

    /**
     * 查询仓库信息
     */
    WarehouseDTO getWarehouseByGuid(String guid);

    /**
     * 根据门店guid查询仓库信息
     */
    List<WarehouseDTO> getWarehouseByStoreGuid(String storeGuid);

    /**
     * 更新仓库
     */
    String updateWarehouse(WarehouseReqDTO reqDTO);

    /**
     * 查询仓库列表
     */
    Page<WarehouseDTO> getWarehouseList(WarehouseQueryDTO queryDTO);

    /**
     * 仓库下拉列表
     */
    List<WarehouseDTO> getWarehouseListByName(WarehouseQueryDTO queryDTO);

    /**
     * 启禁用仓库
     */
    Boolean enableOrDisableWarehouse(String guid);

    /**
     * 仓库解锁或锁定
     */
    Boolean lockOrUnlockWarehouse(String guid);

    /**
     * 删除仓库
     */
    Boolean deleteWarehouse(String guid);

    /**
     * 校验仓库是否锁定
     */
    Boolean isLock(String guid);

    /**
     * 生成仓库编号
     */
    String warehouseCode();

    /**
     * 更新门店仓库名称
     */
    String updateStoreWarehouse(WarehouseReqDTO reqDTO);
}
