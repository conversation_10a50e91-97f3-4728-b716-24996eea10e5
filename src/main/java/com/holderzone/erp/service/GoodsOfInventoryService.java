package com.holderzone.erp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.erp.entity.domain.GoodsOfInventoryDO;

import java.util.List;

public interface GoodsOfInventoryService extends IService<GoodsOfInventoryDO> {

    /**
     * 插入盘点单据对应的商品列表
     *
     * @param list
     */
    void insertGoodsOfInventory(List<GoodsOfInventoryDO> list);

    /**
     * 查询盘点单据对应的商品列表
     *
     * @param inventoryGuid
     */
    List<GoodsOfInventoryDO> queryGoodsOfInventory(String inventoryGuid);

}
