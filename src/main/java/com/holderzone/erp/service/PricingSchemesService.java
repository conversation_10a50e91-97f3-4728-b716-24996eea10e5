package com.holderzone.erp.service;

import com.holderzone.erp.entity.domain.ChangeUnitDO;
import com.holderzone.saas.store.dto.erp.PricingReqDTO;
import com.holderzone.saas.store.dto.erp.PricingSchemesDTO;
import com.holderzone.saas.store.dto.erp.PricingSchemesQueryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @className PricingSchemesService
 * @date 2019-04-27 10:17:53
 * @description
 * @program holder-saas-store-erp
 */
public interface PricingSchemesService {

    /**
     * 保存报价方案
     */
    Boolean savePricingSchemes(PricingReqDTO pricingReqDTO);

    /**
     * 报价方案列表(含停止供应)
     */
    List<PricingSchemesDTO> getPricingSchemesList(String suppliersGuid);

    /**
     * 修改报价方案中物料单位
     */
    Boolean changeMaterialUnitInPricingSchemes(ChangeUnitDO changeUnitDO);

    /**
     * 删除报价方案
     */
    Boolean deletePricingSchemes(List<String> list);

    /**
     * 启禁用物料报价
     */
    Boolean enableOrDisablePricingSchemes(String guid);

    /**
     * 批量查询物料协议单价
     */
    List<PricingSchemesDTO> batchQueryPricingSchemesList(PricingSchemesQueryDTO queryDTO);
}
