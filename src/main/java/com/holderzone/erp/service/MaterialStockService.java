package com.holderzone.erp.service;

import com.holderzone.erp.entity.bo.UpdateStockBO;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.erp.InOutDocumentSelectDTO;
import com.holderzone.saas.store.dto.erp.MaterialBySupplierQueryDTO;
import com.holderzone.saas.store.dto.erp.MaterialDTO;
import com.holderzone.saas.store.dto.erp.StockQueryDTO;

import java.util.List;

/**
 * 库存service
 *
 * <AUTHOR>
 * @date 2019/05/10 15:41
 */
public interface MaterialStockService {
    /**
     * 添加库存
     *
     * @param updateStockBO
     * @return
     */
    boolean addStock(UpdateStockBO updateStockBO);

    /**
     * 批量添加库存
     *
     * @param updateStockBOS
     * @return
     */
    boolean batchAddStock(List<UpdateStockBO> updateStockBOS);

    /**
     * 扣减库存
     *
     * @param updateStockBO
     * @return
     */
    boolean reduceStock(UpdateStockBO updateStockBO);

    /**
     * 批量扣减库存
     *
     * @param updateStockBOList
     * @return
     */
    boolean batchReduceStock(List<UpdateStockBO> updateStockBOList);

    /**
     * 查询库存列表,分页
     *
     * @param stockQueryDTO
     * @param page
     * @return
     */
    List<MaterialDTO> findStockPage(StockQueryDTO stockQueryDTO, Page page);

    List<MaterialDTO> listByCondition(StockQueryDTO stockQueryDTO);

    /**
     * 查询供货商的商品
     */
    List<InOutDocumentSelectDTO> queryMaterialBySupplier(MaterialBySupplierQueryDTO queryDTO);
}
