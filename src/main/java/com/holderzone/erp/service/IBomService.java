package com.holderzone.erp.service;

import com.holderzone.erp.entity.bo.InOutDocumentBomBO;
import com.holderzone.erp.entity.bo.InOutDocumentBomQueryBO;
import com.holderzone.erp.entity.domain.MaterialDO;
import com.holderzone.saas.store.dto.erp.GoodsBomConfigDTO;
import com.holderzone.saas.store.dto.erp.GoodsBomDTO;
import com.holderzone.saas.store.dto.erp.MaterialDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/29 16:15
 */
public interface IBomService {
    boolean add(GoodsBomConfigDTO goodsBomList);

    /**
     * 查询指定商品的bom配置
     *
     * @param goodsGuid
     */
    List<GoodsBomDTO> findBomByGoods(String goodsGuid, String goodsSku);

    /**
     * 查询指定物料的bom配置数量
     *
     * @param materialGuid
     * @return
     */
    Long countBomByMaterial(String materialGuid);

    /**
     * 根据sku统计bom配比中物料的配比种类数量
     *
     * @param skuList
     */
    List<GoodsBomDTO> countBomTypeBySkuList(List<String> skuList);

    /**
     * 根据商品信息和bom配置列表计算商品的物料使用量
     * @param inOutDocumentBomQueryBO
     * @return
     */
    List<InOutDocumentBomBO> parseGoodsByBom(InOutDocumentBomQueryBO inOutDocumentBomQueryBO);

    /**
     * 更新bom相关的单位
     */
    void updateBomUnit(MaterialDTO materialDTO,MaterialDO materialDO);
}
