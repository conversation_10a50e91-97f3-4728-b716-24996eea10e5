package com.holderzone.erp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.erp.entity.domain.GoodsOfRepertoryDO;


import java.util.List;

public interface GoodsOfRepertoryService extends IService<GoodsOfRepertoryDO> {

    /**
     * 添加入库商品
     *
     * @param goodsOfRepertoryDO
     */
    void insertGoods(GoodsOfRepertoryDO goodsOfRepertoryDO);

    /**
     * 批量添加入库商品
     *
     * @param goodsOfRepertoryDOS
     */
    void insertBatchGoods(List<GoodsOfRepertoryDO> goodsOfRepertoryDOS);

    /**
     * 查询库存单对应的商品
     */
    List<GoodsOfRepertoryDO> queryGoodsOfRepertory(String repertoryGuid);
}
