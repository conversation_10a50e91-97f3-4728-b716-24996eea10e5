package com.holderzone.erp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.erp.entity.domain.GoodsDO;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.*;
import com.holderzone.saas.store.dto.erp.erpretail.req.QueryGoodsSumInfoReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.SubstractGoodsReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsClassifyAndItemRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSumInfoRespDTO;
import rx.Single;

import java.math.BigDecimal;
import java.util.List;

public interface GoodsService extends IService<GoodsDO> {

    /**
     * 批量添加入库商品
     *
     * @param goodsDOS
     */
    boolean insertBatchGoods(List<GoodsDO> goodsDOS, int inOut, String storeGuid);

    /**
     * @param goodsGuidList
     * @param inOut
     */
    boolean modifyGoodsRepertoryNum(List<SubstractGoodsReqDTO> goodsGuidList, int inOut);

    /**
     * 查询库存单对应的商品
     */
    Page<GoodsSumInfoRespDTO> queryGoods(QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO);

    /**
     * 查询商品信息
     */
    List<GoodsDO> queryGoods(List<String> goodsGuidList);

    /**
     * 查询所有商品列表
     */
    List<GoodsClassifyAndItemRespDTO> queryGoodsList(SingleDataDTO singleDataDTO);

    /**
     * 修改商品分类名称
     */
    boolean modifyClassifyName(String classifyGuid, String classifyName);


    boolean modifyGoodsInfo(InOutGoodsDTO inOutGoodsDTO);

    /**
     * 取消关联库存
     */
    boolean cancelRelateRepertory(String goodsGuid);

    /**
     * 盘点数量不一，更新商品库存
     */
    boolean updateGoodsRepertoryNum(String goodsGuid, BigDecimal num);

    InOutGoodsDTO queryGoodsInfo(String goodsGuid);

    List<GoodsExportDTO> queryExportGoodsList(List<String> goodsGuids);

    /**
     * 查询库存汇总信息
     *
     * @param singleDataDTO
     * @return
     */
    RepertorySumDTO queryRepertorySum(SingleDataDTO singleDataDTO);
}
