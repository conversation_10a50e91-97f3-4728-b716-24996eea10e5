package com.holderzone.erp.utils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/04/29 下午 16:13
 * @description
 */
public class DateUtil {

    private static final String ZONE_ID = "Asia/Shanghai";

    public static Date getCurrentDate() {
        LocalDate localDate = LocalDate.now(ZoneId.of(ZONE_ID));
        ZonedDateTime zdt = localDate.atStartOfDay(ZoneId.of(ZONE_ID));
        return Date.from(zdt.toInstant());
    }

    public static long dateFormat(Long timestamp) {
        LocalDate localDate = Instant.ofEpochMilli(timestamp).atZone(ZoneId.of(ZONE_ID)).toLocalDate();
        Date date = Date.from(localDate.atStartOfDay(ZoneId.of(ZONE_ID)).toInstant());
        return date.getTime();
    }

}
