package com.holderzone.erp.utils;

import com.holderzone.framework.exception.unchecked.ParameterException;
import net.bytebuddy.implementation.bytecode.Throw;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @className CommonUtils
 * @date 2019-05-07 17:25:11
 * @description
 * @program holder-saas-store-erp
 */
public class CommonUtils {

    public static BigDecimal formatNumber(BigDecimal num, int digit) {
        if (num == null) {
            return BigDecimal.ZERO;
        }
        return num.divide(BigDecimal.ONE, digit, BigDecimal.ROUND_HALF_UP);
    }

}
