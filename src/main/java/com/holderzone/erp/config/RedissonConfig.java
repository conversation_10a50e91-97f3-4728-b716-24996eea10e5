package com.holderzone.erp.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2019/05/14 上午 11:12
 * @description
 */
@Configuration
public class RedissonConfig {

    @Value("${redisson.host}")
    private String host;

    @Value("${redisson.database}")
    private Integer database;

    @Value("${redisson.password}")
    private String password;

    @Bean
    public RedissonClient redissonClient(){
        Config config = new Config();
        config.useSingleServer()
                .setAddress(host)
                .setDatabase(database)
                .setPassword(password)
                .setConnectionMinimumIdleSize(1);
        return Redisson.create(config);
    }
}
