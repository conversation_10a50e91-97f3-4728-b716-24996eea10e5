package com.holderzone.erp.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RedisConfig
 * @date 2018/07/03 上午 10:38
 * @description
 * @program framework-dynamic-datasource-sdk
 */
@Configuration
public class RedisConfig {

    private static final Logger log = LoggerFactory.getLogger(RedisConfig.class);

    @Bean
    public RedisTemplate redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        setSerializeForRedisTemplate(template);
        template.afterPropertiesSet();
        return template;
    }

    private void setSerializeForRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        redisTemplate.setValueSerializer(new GenericToStringSerializer(Integer.class));
        redisTemplate.setKeySerializer(RedisSerializer.string());
    }

}
