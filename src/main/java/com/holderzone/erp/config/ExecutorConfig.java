package com.holderzone.erp.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019/05/17 下午 17:15
 * @description
 */
@Configuration
public class ExecutorConfig {

    @Bean("orderUpdateStockConsumerExecutor")
    public Executor orderUpdateStockConsumerExecutor(){
       return new ThreadPoolExecutor(200, 200,
               1L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(),
               new ThreadFactoryBuilder().setNameFormat("erp-order-update-stock-consumer-%d").build(),
               new ThreadPoolExecutor.CallerRunsPolicy());
    }

}
