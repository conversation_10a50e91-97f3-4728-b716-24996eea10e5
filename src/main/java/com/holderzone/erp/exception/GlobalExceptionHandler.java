package com.holderzone.erp.exception;

import com.holderzone.feign.spring.boot.exception.ExceptionHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLException;

/**
 * <AUTHOR>
 * @description 全局异常处理
 * @date 2019-02-19 14:26:50
 */
@Slf4j
@Configuration
@RestControllerAdvice
public class GlobalExceptionHandler extends ExceptionHandlerAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(value = {
            UncategorizedSQLException.class,
            BadSqlGrammarException.class,
            DataIntegrityViolationException.class,
            SQLException.class
    })
    public ResponseEntity handleSQLException(RuntimeException e) {
        String message = e.getMessage();
        log.error(message, e);
        return new ResponseEntity<>("sql错误", HttpStatus.BAD_REQUEST);
    }
}
