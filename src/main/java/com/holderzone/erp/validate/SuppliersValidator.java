package com.holderzone.erp.validate;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.erp.SuppliersMaterialQueryDTO;
import com.holderzone.saas.store.dto.erp.SuppliersQueryDTO;
import com.holderzone.saas.store.dto.erp.SuppliersReqDTO;

/**
 * <AUTHOR>
 * @className WarehouseValidator
 * @date 2019-04-25 16:35:40
 * @description
 * @program holder-saas-store-erp
 */
public class SuppliersValidator extends BaseValidator {

    public static void validateCreateSuppliers(SuppliersReqDTO reqDTO) {
        if (reqDTO == null) {
            throw new ParameterException("请求参数不能为空");
        }
        Validator validator = validateSuppliers(reqDTO);
        validate(validator);
    }

    public static void validateUpdateSuppliers(SuppliersReqDTO reqDTO) {
        if (reqDTO == null) {
            throw new ParameterException("请求参数不能为空");
        }
        Validator validator = validateSuppliers(reqDTO);
        validator.notBlank(reqDTO.getGuid(), "guid不能为空");
        validate(validator);
    }

    private static Validator validateSuppliers(SuppliersReqDTO reqDTO) {
        Validator validator = Validator.create()
                .notBlank(reqDTO.getName(), "供应商名称不能为空")
                .dynamicLength(reqDTO.getName(), 1, 40, "供应商名称长度为1-40位")
                .ifNotBlankLength(reqDTO.getOfficeTel() == null? null : reqDTO.getOfficeTel().replaceAll("-", ""), 1, 12, "单位电话长度不能超过12位")
                .ifNotBlankLength(reqDTO.getContactName(), 1, 10, "联系人名字长度不能超过10位")
                .ifNotBlankLength(reqDTO.getContactTel(), 1, 11, "联系人电话长度不能超过11位")
                .ifNotBlankLength(reqDTO.getAddr(), 1, 30, "地址长度30字以内")
                .ifNotBlankLength(reqDTO.getRemark(), 1, 100, "备注长度100字以内")
                .notNull(reqDTO.getEnabled(), "状态不能为空")
                .notBlank(reqDTO.getForeignKey(), "关联信息不能为空");
        if (reqDTO.getSettlementInterval() != null) {
            validator.patten(reqDTO.getSettlementInterval(), "^[1-9]|[1-9][0-9]|[1-3][0-6][0-5]$", "结算周期只能为1-365");
        }
        return validator;
    }

    public static void validateGetSuppliersList(SuppliersQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new ParameterException("请求参数不能为空");
        }
        Validator validator = Validator.create()
                .notBlank(queryDTO.getForeignKey(), "关联信息不能为空")
                .notNull(queryDTO.getCurrentPage(), "页码不能为空")
                .notNull(queryDTO.getPageSize(), "分页大小不能为空");
        validate(validator);
    }

    public static void validateGetAllOfSuppliersList(SuppliersQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new ParameterException("请求参数不能为空");
        }
    }

    public static void validateGetSuppliersMaterialListAll(SuppliersMaterialQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new ParameterException("请求参数不能为空");
        }
        Validator validator = Validator.create()
                .notBlank(queryDTO.getSuppliersGuid(), "供应商guid不能为空");
        validate(validator);
    }
}
