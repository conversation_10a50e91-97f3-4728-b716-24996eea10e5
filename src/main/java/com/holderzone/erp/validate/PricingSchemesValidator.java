package com.holderzone.erp.validate;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.erp.PricingReqDTO;

/**
 * <AUTHOR>
 * @className WarehouseValidator
 * @date 2019-04-25 16:35:40
 * @description
 * @program holder-saas-store-erp
 */
public class PricingSchemesValidator extends BaseValidator {

    public static void validateSavePricingSchemes(PricingReqDTO pricingReqDTO) {
        if (pricingReqDTO == null) {
            throw new ParameterException("请求参数不能为空");
        }
        Validator validator = Validator.create()
                .notBlank(pricingReqDTO.getSuppliersGuid(), "供应商guid不能为空");
//                .notEmpty(pricingReqDTO.getPricingSchemesList(), "报价方案信息不能为空");
        validate(validator);
        pricingReqDTO.getPricingSchemesList().forEach(reqDTO -> {
            Validator validate = Validator.create()
                    .notBlank(reqDTO.getMaterialGuid(), "物料guid不能为空")
                    .notNull(reqDTO.getDealPrice(), "协议价格不能为空")
                    .patten(reqDTO.getDealPrice(), "^(([1-9][\\d]{0,8}(\\.[\\d]{1,2})?)|(0\\.[0-9][1-9])|(0\\.[1-9][0-9]))$", "协议价范围为0.01-999999999.99，最多两位小数")
                    .notNull(reqDTO.getDealUnit(), "单位不能为空");
            validate(validate);
        });
    }
}
