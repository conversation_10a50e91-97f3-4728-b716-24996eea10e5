package com.holderzone.erp.validate;

import com.holderzone.erp.entity.domain.CheckoutDocumentDO;
import com.holderzone.erp.entity.enumeration.DocumentStatus;
import com.holderzone.erp.service.CheckoutDocumentService;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.erp.CheckoutDocumentAddOrUpdateDTO;
import com.holderzone.saas.store.dto.erp.CheckoutDocumentDetailAddOrUpdateDTO;
import com.holderzone.saas.store.dto.erp.CheckoutDocumentDetailQueryDTO;
import com.holderzone.saas.store.dto.erp.CheckoutDocumentQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/08 上午 9:49
 * @description
 */
@Component
public class CheckoutDocumentValidator extends BaseValidator {

    @Autowired
    private CheckoutDocumentService checkoutDocumentService;


    public void addOrUpdateCheckoutDocumentValidate(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO) {
        Validator validator = Validator.create()
                .ifNotBlankLength(addOrUpdateDTO.getCode(), 1, 30, "纸质单编号不能超过30字")
                .notNull(addOrUpdateDTO.getType(), "盘点类型不能为空")
                .ifNotBlankLength(addOrUpdateDTO.getRemark(), 1, 100, "备注不能超过100字")
                .notNull(addOrUpdateDTO.getWarehouseGuid(),"仓库guid不能为空")
                .notNull(addOrUpdateDTO.getWarehouseName(),"仓库名称不能为空")
                .notNull(addOrUpdateDTO.getLock(),"是否锁盘状态为空");
        validate(validator);
        checkoutDocumentDetailValidate(addOrUpdateDTO);
    }

    private void checkoutDocumentDetailValidate(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO) {
        List<CheckoutDocumentDetailAddOrUpdateDTO> detailList = addOrUpdateDTO.getDetailList();
        if (detailList == null || detailList.isEmpty()) {
            throw new ParameterException("无法保存，请添加原料");
        }
        Validator validator = Validator.create();
        for (CheckoutDocumentDetailAddOrUpdateDTO detail : detailList) {
            validator.notBlank(detail.getMaterialGuid(), "物料guid不能为空")
                    .notBlank(detail.getMaterialCode(), "物料编码不能为空")
                    .notBlank(detail.getMaterialName(), "物料名称不能为空")
                    .notNull(detail.getStock(), "物料库存量不能为空")
                    .notBlank(detail.getUnitGuid(), "物料单位guid不能为空")
                    .notBlank(detail.getUnitName(), "物料单位名称不能为空")
                    .notNull(detail.getCheckCount(), "物料盘点数量不能为空");
        }
        validate(validator);
        for (CheckoutDocumentDetailAddOrUpdateDTO detail : detailList) {
            if (detail.getCheckCount().doubleValue() < 0 || detail.getCheckCount().doubleValue() > 999999.999) {
                throw new ParameterException("盘点数量必须在0-999999.999之内");
            }
        }
        if (!StringUtils.isEmpty(addOrUpdateDTO.getGuid())) {
            for (CheckoutDocumentDetailAddOrUpdateDTO detail : detailList) {
                if (StringUtils.isEmpty(detail.getDocumentGuid())) {
                    throw new ParameterException("物料的主单据guid不能为空");
                }
                if (!detail.getDocumentGuid().equals(addOrUpdateDTO.getGuid())) {
                    throw new ParameterException("物料的主单据guid非法");
                }
            }
        }


    }

    public void selectDocumentDetailForAddValidate(CheckoutDocumentDetailQueryDTO queryDTO) {
        Validator validator = Validator.create()
                .notEmpty(queryDTO.getMaterialGuidList(), "物料guid的集合不能为空");
        validate(validator);
    }

    public void deleteDocumentValidate(String documentGuid) {
        if (StringUtils.isEmpty(documentGuid)){
            throw new ParameterException("单据guid不能为空");
        }
        CheckoutDocumentDO documentDO = checkoutDocumentService.selectDocumentStatus(documentGuid);
        if (documentDO == null){
            throw new ParameterException("单据不存在");
        }
        if (DocumentStatus.SUBMIT.getStatus().equals(documentDO.getStatus())){
            throw new ParameterException("提交的单据不能删除");
        }
    }

    public void selectCheckoutDocumentForPageValidate(CheckoutDocumentQueryDTO queryDTO) {
        Validator validator = Validator.create()
                .notNull(queryDTO.getStartDate(), "开始日期不能为空")
                .notNull(queryDTO.getEndDate(), "结束日期不能为空")
                .notNull(queryDTO.getCurrentPage(), "当前页不能为空")
                .notNull(queryDTO.getPageSize(), "每页条数不能为空")
                .notEmpty(queryDTO.getWarehouseGuidList(),"仓库guid不能为空");
        validate(validator);
        if (queryDTO.getCurrentPage() < 1){
            throw new ParameterException("当前页不能小于1");
        }
        if (queryDTO.getPageSize() < 1 && queryDTO.getPageSize() > 100){
            throw new ParameterException("每页展示条数必须在1 - 100之内");
        }
    }
}
