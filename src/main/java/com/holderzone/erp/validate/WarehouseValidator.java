package com.holderzone.erp.validate;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.erp.WarehouseQueryDTO;
import com.holderzone.saas.store.dto.erp.WarehouseReqDTO; /**
 * <AUTHOR>
 * @className WarehouseValidator
 * @date 2019-04-25 16:35:40
 * @description
 * @program holder-saas-store-erp
 */
public class WarehouseValidator extends BaseValidator {

    public static void validateCreateWarehouse(WarehouseReqDTO reqDTO) {
        if (reqDTO == null) {
            throw new ParameterException("请求参数不能为空");
        }
        Validator validator = Validator.create()
                .notBlank(reqDTO.getName(), "仓库名称不能为空")
                .length(reqDTO.getCode(), 6, "仓库编号长度必须为6")
                .ifNotBlankLength(reqDTO.getAddr(), 1, 30, "仓库地址长度为1-30位")
                .ifNotBlankLength(reqDTO.getPic(), 1, 10, "负责人名字长度为1-10位")
                .ifNotBlankLength(reqDTO.getTel(), 11, 11, "手机号长度必须为11位")
                .ifNotBlankLength(reqDTO.getRemark(), 1, 200, "备注长度200字以内");
        if (reqDTO.getType() == 1) {
            validator.notBlank(reqDTO.getForeignKey(), "关联的企业或门店guid不能为空");
        }else {
            validator.dynamicLength(reqDTO.getName(), 1, 45, "仓库名称长度为1-45位");
        }
        validate(validator);
    }

    public static void validateUpdateWarehouse(WarehouseReqDTO reqDTO) {
        if (reqDTO == null) {
            throw new ParameterException("请求参数不能为空");
        }
        Validator validator = Validator.create()
                .notBlank(reqDTO.getGuid(), "仓库guid不能为空")
                .notBlank(reqDTO.getName(), "仓库名称不能为空")
                .dynamicLength(reqDTO.getName(), 1, 45, "仓库名称长度为1-45位")
                .length(reqDTO.getCode(), 6, "仓库编号长度必须为6")
                .ifNotBlankLength(reqDTO.getAddr(), 1, 30, "仓库地址长度为1-30位")
                .ifNotBlankLength(reqDTO.getPic(), 1, 10, "负责人名字长度为1-10位")
                .ifNotBlankLength(reqDTO.getTel(), 11, 11, "手机号长度必须为11位")
                .ifNotBlankLength(reqDTO.getRemark(), 1, 200, "备注长度200字以内");
        if (reqDTO.getType() == 1) {
            validator.notBlank(reqDTO.getForeignKey(), "关联的企业或门店guid不能为空");
        }
        validate(validator);
    }

    public static void validateGetWarehouseList(WarehouseQueryDTO queryDTO) {
        if (queryDTO == null) {
            throw new ParameterException("请求参数不能为空");
        }
        Validator validator = Validator.create()
                .notNull(queryDTO.getCurrentPage(), "页码不能为空")
                .notNull(queryDTO.getPageSize(), "页面size不能为空");
        validate(validator);
    }

}
