package com.holderzone.erp.validate;

import com.holderzone.erp.entity.bo.InOutDocumentDetailBO;
import com.holderzone.erp.entity.domain.InOutDocumentDO;
import com.holderzone.erp.mapperstruct.InOutDocumentTransform;
import com.holderzone.erp.service.InOutDocumentService;
import com.holderzone.erp.service.SuppliersService;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.erp.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.holderzone.erp.entity.enumeration.DocumentInOutTypeEnum.OUT_DOCUMENT;
import static com.holderzone.erp.entity.enumeration.DocumentStatus.SUBMIT;
import static com.holderzone.erp.entity.enumeration.DocumentTypeEnum.*;

/**
 * <AUTHOR>
 * @date 2019/04/29 下午 14:34
 * @description
 */
@Component
public class InOutDocumentValidator extends BaseValidator {

    @Autowired
    private InOutDocumentService inOutDocumentService;

    @Autowired
    private SuppliersService suppliersService;

    private InOutDocumentTransform inOutDocumentTransform = InOutDocumentTransform.INSTANCE;


    public void insertOrUpdateInOutDocumentValidate(InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        if (inOutDocumentDTO.getInOutType() == null) {
            throw new ParameterException("单据出入库类型为空");
        }
        Validator validator = Validator.create()
                .notBlank(inOutDocumentDTO.getWarehouseGuid(), "仓库guid不能为空")
                .notBlank(inOutDocumentDTO.getWarehouseName(), "仓库名称不能为空")
                .notNull(inOutDocumentDTO.getTotalAmount(), "物料总金额不能为空")
                .ifNotBlankLength(inOutDocumentDTO.getCode(), 1, 30, "纸质编号不能超过30个字")
                .ifNotBlankLength(inOutDocumentDTO.getRemark(), 1, 100, "备注不能超过100字");
        if (IN_PURCHASE.getType().equals(inOutDocumentDTO.getType())
                || OUT_RETURN.getType().equals(inOutDocumentDTO.getType())) {
            validator.notBlank(inOutDocumentDTO.getSupplierGuid(), "供应商Guid不能为空")
                    .notBlank(inOutDocumentDTO.getSupplierName(), "供应商名称不能为空");
        } else {
            if (!StringUtils.isEmpty(inOutDocumentDTO.getSupplierGuid())) {
                throw new ParameterException("不能有供应商Guid");
            }
            if (!StringUtils.isEmpty(inOutDocumentDTO.getSupplierName())) {
                throw new ParameterException("不能有供应商名称");
            }

        }
        validate(validator);

        if (inOutDocumentDTO.getShouldPayAmount() != null) {
            if (inOutDocumentDTO.getShouldPayAmount().doubleValue() < 0.01
                    || inOutDocumentDTO.getShouldPayAmount().doubleValue() > 999999999.99) {
                throw new ParameterException("应付金额需要在0.01和99999.99之内");
            }
        }

        if (OUT_RETURN.getType().equals(inOutDocumentDTO.getType()) &&
                !StringUtils.isEmpty(inOutDocumentDTO.getContactDocumentGuid())) {
            documentDetailOutCountValidate(inOutDocumentDTO);
        }
        if (!StringUtils.isEmpty(inOutDocumentDTO.getGuid())) {
            documentStatusValidate(inOutDocumentDTO.getGuid());
        }

        //校验供应商状态
        if (!StringUtils.isEmpty(inOutDocumentDTO.getSupplierGuid())) {
            inOutDocumentSuppliersStatusValidate(inOutDocumentDTO.getSupplierGuid());
        }
        //校验物料明细字段信息
        inOutDocumentDetailListValidate(inOutDocumentDTO, inOutDocumentDTO.getDetailList());
    }

    private void inOutDocumentSuppliersStatusValidate(String supplierGuid) {
        SuppliersDTO suppliersStatus = suppliersService.getSuppliersStatus(supplierGuid);
        if (suppliersStatus == null) {
            throw new ParameterException("没有此供应商");
        }
        if (suppliersStatus.getEnabled() == 0) {
            throw new ParameterException("供应商被禁用");
        }
        if (suppliersStatus.getDeleted() == 1) {
            throw new ParameterException("供应商被删除");
        }
    }

    /**
     * 单据状态校验，如果已提交，则不能修改
     *
     * @param guid
     */
    private void documentStatusValidate(String guid) {
        InOutDocumentDO inOutDocumentDO = inOutDocumentService.selectDocumentStatus(guid);
        if (inOutDocumentDO == null) {
            throw new ParameterException("该单据不存在");
        }
        if (SUBMIT.getStatus().equals(inOutDocumentDO.getStatus())) {
            throw new ParameterException("该单据已提交，不能修改");
        }
    }

    /**
     * 如果退货单有关联单据，则退货数量不能超过入库的数量
     *
     * @param inOutDocumentDTO
     */
    void documentDetailOutCountValidate(InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        List<InOutDocumentDetailBO> detailBOList =
                inOutDocumentTransform.inOutDocumentDetailAddOrUpdateDtosToBos(inOutDocumentDTO.getDetailList());
        inOutDocumentService.documentDetailOutCountValidate(inOutDocumentDTO.getContactDocumentGuid(), detailBOList);
    }

    /**
     * 校验出入库物料明细list
     *
     * @param detailList
     */
    private void inOutDocumentDetailListValidate(InOutDocumentAddOrUpdateDTO inOutDocumentDTO, List<InOutDocumentDetailAddOrUpdateDTO> detailList) {
        if (detailList == null || detailList.isEmpty()) {
            throw new ParameterException("无法保存，请添加物料");
        }
        for (InOutDocumentDetailAddOrUpdateDTO detail : detailList) {
            inOutDocumentDetailValidate(inOutDocumentDTO, detail);
        }
    }

    /**
     * 校验出入库物料明细
     *
     * @param detail
     */
    private void inOutDocumentDetailValidate(InOutDocumentAddOrUpdateDTO inOutDocumentDTO, InOutDocumentDetailAddOrUpdateDTO detail) {
        Validator validator = Validator.create()
                .notBlank(detail.getMaterialCode(), "物料编码不能为空")
                .notBlank(detail.getMaterialName(), "物料名称不能为空")
                .notNull(detail.getCount(), "物料数量不能为空")
                .notBlank(detail.getUnitGuid(), "物料单位Guid不能为空")
                .notBlank(detail.getUnitName(), "物料单位名称不能为空")
                .notNull(detail.getUnitPrice(), "物料单价不能为空")
                .notNull(detail.getTotalAmount(), "物料总价不能为空");
        validate(validator);
        if (detail.getCount().doubleValue() < 0.01 || detail.getCount().doubleValue() > 99999999) {
            throw new ParameterException("物料数量应该在0.01至99999999之间");
        }
        if (!IN_CHECK_PROFIT.getType().equals(inOutDocumentDTO.getType()) &&
                !OUT_CHECK_DEFICIT.getType().equals(inOutDocumentDTO.getType())) {
            if (detail.getUnitPrice().doubleValue() < 0 || detail.getUnitPrice().doubleValue() > 999999999.99) {
                throw new ParameterException("物料单价应该在0至999999999.99之间");
            }
        }

        if (OUT_DOCUMENT.getType().equals(inOutDocumentDTO.getInOutType())) {
            if (detail.getStock() == null) {
                throw new ParameterException("物料库存不能为空");
            }
        }


    }


    public void detailDocumentGuidValidate(InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        String guid = inOutDocumentDTO.getGuid();
        for (InOutDocumentDetailAddOrUpdateDTO detail : inOutDocumentDTO.getDetailList()) {
            if (!detail.getDocumentGuid().equals(guid)) {
                throw new ParameterException("物料主单据号不正确");
            }
        }
    }

    public void selectMaterialListValidate(InOutDocumentDetailQueryDTO materialQueryDTO) {
        Validator validator = Validator.create()
                .notEmpty(materialQueryDTO.getMaterialGuidList(), "物料Guid不能为空")
                .notBlank(materialQueryDTO.getWarehouseGuid(), "仓库Guid不能为空");
        validate(validator);
    }

    public void selectDocumentListForPageValidate(InOutDocumentQueryDTO queryDTO) {
        Validator validator = Validator.create()
                .notNull(queryDTO.getCurrentPage(), "当前页数不能为空")
                .notNull(queryDTO.getPageSize(), "每页展示条数不能为空")
                .notNull(queryDTO.getInOutType(), "单据出入库类型不能为空");
        validate(validator);
        if (queryDTO.getCurrentPage() <= 0) {
            throw new ParameterException("当前页不能小于1");
        }
        if (queryDTO.getPageSize() <= 0 && queryDTO.getPageSize() >= 100) {
            throw new ParameterException("每页展示条数必须在1 - 100之内");
        }
    }

    public void selectSuppliersReconciliationValidate(SuppliersReconciliationQueryDTO queryDTO) {
        Validator validator = Validator.create()
                .notNull(queryDTO.getCurrentPage(), "当前页数不能为空")
                .notNull(queryDTO.getPageSize(), "每页展示条数不能为空")
                .notNull(queryDTO.getGuid(), "企业或门店guid不能为空");
        validate(validator);
        if (queryDTO.getCurrentPage() <= 0) {
            throw new ParameterException("当前页码不能小于1");
        }
    }

    public void selectDocumentGuidListValidate(InOutContactDocumentQueryDTO queryDTO) {
        Validator validator = Validator.create()
                .notBlank(queryDTO.getDocumentGuid(), "单据guid不能为空")
                .notBlank(queryDTO.getWarehouseGuid(), "仓库guid不能为空");
        validate(validator);
    }

    public void selectFlowDetailListForPage(InOutDocumentFlowDetailQueryDTO queryDTO) {
        Validator validator = Validator.create()
                .notNull(queryDTO.getStartDate(), "单据开始日期不能为空")
                .notNull(queryDTO.getEndDate(), "单据结束日期不能为空")
                .notEmpty(queryDTO.getWarehouseGuidList(), "仓库guid的集合不能为空")
                .notNull(queryDTO.getCurrentPage(), "当前页数不能为空")
                .notNull(queryDTO.getPageSize(), "每页显示条数不能为空");
        validate(validator);
    }

    public void settleSuppliersReconciliationValidate(List<String> list) {
        Validator validator = Validator.create()
                .notEmpty(list, "结算单号不能为空");
        validate(validator);
    }

    public void reduceStockForOrderValidate(OrderSkuDTO orderSkuDTO) {
        Validator validator = Validator.create()
                .notBlank(orderSkuDTO.getStoreGuid(), "门店guid不能为空")
                .notBlank(orderSkuDTO.getOperatorGuid(), "操作人guid不能为空")
                .notBlank(orderSkuDTO.getOperatorName(), "操作人名称不能为空");
        for (SkuInfo skuInfo : orderSkuDTO.getSkuList()) {
            validator.notBlank(skuInfo.getSkuGuid(), "skuGuid不能为空")
                    .notNull(skuInfo.getCount(), "sku的用量不能为空");
        }
        validate(validator);
    }

    public void reduceStockForOrderValidateBatch(List<OrderSkuDTO> orderSkuDTOList) {
        Validator validator = Validator.create();
        for (OrderSkuDTO orderSkuDTO : orderSkuDTOList) {

            validator.notBlank(orderSkuDTO.getStoreGuid(), "门店guid不能为空")
                    .notBlank(orderSkuDTO.getOperatorGuid(), "操作人guid不能为空")
                    .notBlank(orderSkuDTO.getOperatorName(), "操作人名称不能为空");

            for (SkuInfo skuInfo : orderSkuDTO.getSkuList()) {
                validator.notBlank(skuInfo.getSkuGuid(), "skuGuid不能为空")
                        .notNull(skuInfo.getCount(), "sku的用量不能为空");
            }
        }
        validate(validator);
    }

    public void importMaterialListValidate(InOutDocumentMaterialImportDTO inOutDocumentMaterialImportDTO) {
        Validator validator = Validator.create()
                .notBlank(inOutDocumentMaterialImportDTO.getStoreGuid(), "门店guid不能为空")
                .notBlank(inOutDocumentMaterialImportDTO.getWarehouseGuid(), "仓库guid不能为空");
        validate(validator);
    }
}
