package com.holderzone.erp.mapperstruct;

import com.holderzone.erp.entity.domain.GoodsOfRepertoryDO;
import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;



@Component
@Mapper(componentModel = "spring")
public interface InventoryGoodsMapstruct {

    GoodsOfRepertoryDO fromInOutGoodsOfRepertoryDTO(InOutGoodsDTO inOutGoodsDTO);

}