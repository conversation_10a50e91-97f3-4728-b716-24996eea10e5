package com.holderzone.erp.mapperstruct;

import com.holderzone.framework.util.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-07-18
 */
public class MapperHelper {

    public static Boolean mapBoolean(Object obj) {
        if (StringUtils.isEmpty(obj)) {
            return null;
        }
        return obj.equals(0) ? false : true;
    }

    public static Integer mapBooleanToString(Boolean obj) {
        if (StringUtils.isEmpty(obj)) {
            return null;
        }
        return obj ? 1 : 0;
    }

    public static Long mapTimestamp(Date date) {
        if (ObjectUtils.isEmpty(date)) {
            return null;
        }
        return date.getTime();
    }

    public static Date mapDate(Long timestamp) {
        if (ObjectUtils.isEmpty(timestamp)) {
            return null;
        }
        return new Date(timestamp);
    }
}
