package com.holderzone.erp.mapperstruct;

import com.holderzone.erp.entity.domain.*;
import com.holderzone.saas.store.dto.erp.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-08-17 14:34:08
 */
@Mapper(componentModel = "spring")
public interface ErpModuleMapper {

    WarehouseDO mapToWarehouseDO(WarehouseReqDTO reqDTO);

    WarehouseDTO mapToWarehouseDTO(WarehouseDO warehouseDO);

    List<WarehouseDTO> mapToWarehouseDtoList(List<WarehouseDO> warehouseDoList);

    WarehouseQueryDO mapToWarehouseQueryDO(WarehouseQueryDTO queryDTO);

    @Mappings({@Mapping(target = "enabled", expression = "java(MapperHelper.mapBoolean(materialDTO.getEnabled()))")
            , @Mapping(target = "deleted", expression = "java(MapperHelper.mapBoolean(materialDTO.getDeleted()))")})
    MaterialDO mapToMaterialDO(MaterialDTO materialDTO);

    @Mappings({@Mapping(target = "enabled", expression = "java(MapperHelper.mapBooleanToString(materialDO.getEnabled()))")
            , @Mapping(target = "deleted", expression = "java(MapperHelper.mapBooleanToString(materialDO.getDeleted()))")})
    MaterialDTO mapToMaterialDTO(MaterialDO materialDO);

    List<MaterialDTO> mapToMaterialDTOList(List<MaterialDO> list);

    List<MaterialDO> mapToMaterialDOList(List<MaterialDTO> list);

    MaterialCategoryDO mapToMaterialCategoryDO(MaterialCategoryDTO materialCategoryDTO);

    MaterialCategoryDTO mapToMaterialCategoryDTO(MaterialCategoryDO materialCategoryDO);

    List<MaterialCategoryDTO> mapToMaterialCategoryDTOList(List<MaterialCategoryDO> materialCategoryDOList);

    SuppliersDO mapToSuppliersDO(SuppliersReqDTO reqDTO);

    SuppliersDTO mapToSuppliersDTO(SuppliersDO suppliersDO);

    List<SuppliersDTO> mapToSuppliersDtoList(List<SuppliersDO> suppliersDoList);

    SuppliersQueryDO mapToSuppliersQueryDO(SuppliersQueryDTO queryDTO);

    List<PricingSchemesDO> mapToPricingSchemesDoList(List<PricingSchemesReqDTO> pricingSchemesReqDTOList);

    PricingSchemesDO mapToPricingSchemesDo(PricingSchemesReqDTO pricingSchemesReqDTO);

    List<PricingSchemesDTO> mapToPricingSchemesDtoList(List<PricingSchemesDO> list);

    PricingSchemesDTO mapToPricingSchemesDTO(PricingSchemesDO pricingSchemesDO);

    @Mapping(target = "materialDTOList", source = "materialDOList")
    CategoryDTO mapToCategoryDTO(CategoryDO categoryDO);

    List<CategoryDTO> mapToCategoryDTO(List<CategoryDO> categoryDOList);

    @Mappings({@Mapping(target = "gmtCreate", ignore = true), @Mapping(target = "gmtModified", ignore = true)})
    MaterialUnitDO mapToUnitDO(MaterialUnitDTO unitDTO);

    MaterialUnitDTO mapToUnitDTO(MaterialUnitDO unitDO);

    List<MaterialUnitDTO> mapToUnitDTOList(List<MaterialUnitDO> list);

    List<GoodsBomDTO> mapToBomDTOList(List<GoodsBomDO> list);

    List<GoodsBomDO> mapToBomDOList(List<GoodsBomDTO> list);
}
