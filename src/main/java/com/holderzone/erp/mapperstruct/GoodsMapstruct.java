package com.holderzone.erp.mapperstruct;

import com.holderzone.erp.entity.domain.GoodsDO;
import com.holderzone.erp.entity.domain.GoodsOfRepertoryDO;

import com.holderzone.saas.store.dto.erp.erpretail.GoodsExportDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSumInfoRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.InOutGoodsDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@Mapper(componentModel = "spring")
public interface GoodsMapstruct {

    GoodsOfRepertoryDO fromInOutGoodsOfRepertoryDTO(InOutGoodsDTO inOutGoodsDTO);

    @Mappings({
            @Mapping(source = "guid", target = "goodsGuid"),
            @Mapping(source = "remainRepertoryNum", target = "count")
    })
    GoodsSumInfoRespDTO fromGoodsDOToGoodsRepertoryInfoSumRespDTO(GoodsDO goodsDO);

    @Mappings({
            @Mapping(source = "goodsGuid", target = "guid"),
            @Mapping(source = "count", target = "remainRepertoryNum")
    })
    GoodsDO fromInOutGoodsDTO(InOutGoodsDTO inOutGoodsDTO);

    @Mappings({
            @Mapping(source = "guid", target = "goodsGuid"),
            @Mapping(source = "remainRepertoryNum", target = "count")
    })
    InOutGoodsDTO fromGoodsDO(GoodsDO goodsDO);

    List<GoodsExportDTO> fromGoodsDOList(List<GoodsDO> goodsDO);

    @Mappings({
            @Mapping(source = "guid", target = "goodsGuid"),
            @Mapping(source = "remainRepertoryNum", target = "count")
    })
    GoodsExportDTO exportDtoFromGoodsDO(GoodsDO goodsDO);

    List<InOutGoodsDTO> fromGoodsListToGoodsOfRepertoryList(List<GoodsOfRepertoryDO> goodsOfRepertoryDOList);

    List<GoodsSumInfoRespDTO> fromGoodsListToGoodsRepertorySumList(List<GoodsDO> goodsDOList);

    List<GoodsDO> fromGoodsDTOListToGoodsDOList(List<InOutGoodsDTO> inOutGoodsDTOList);

    List<InOutGoodsDTO> fromGoodsList(List<GoodsDO> goodsDOList);
}