package com.holderzone.erp.mapperstruct;

import com.holderzone.erp.entity.domain.GoodsSerialDO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.GoodsSerialRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.InsertGoodsSerialReqDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;


@Component
@Mapper(componentModel = "spring")
public interface GoodsSerialMapstruct {

    GoodsSerialDO fromGoodsSerialDTO(GoodsSerialRespDTO goodsSerialRespDTO);

    GoodsSerialDO fromInsertGoodsSerialDTO(InsertGoodsSerialReqDTO insertGoodsSerialReqDTO);

    GoodsSerialRespDTO fromGoodsSerialDO(GoodsSerialDO goodsSerialDO);

}