package com.holderzone.erp.mapperstruct;

import com.holderzone.erp.entity.domain.read.MaterialConsumeReadDO;
import com.holderzone.saas.store.dto.erp.MaterialConsumeRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;


@Component
@Mapper(componentModel = "spring")
public interface MaterialMapstruct {

    @Mappings({
            @Mapping(target = "materialGuid", source = "guid"),
            @Mapping(target = "count", source = "count" ,defaultValue = "0")
    })
    MaterialConsumeRespDTO fromMaterialConsumeReadDO(MaterialConsumeReadDO materialConsumeReadDO);

}