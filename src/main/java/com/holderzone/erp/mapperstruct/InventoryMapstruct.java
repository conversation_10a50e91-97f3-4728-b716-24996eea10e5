package com.holderzone.erp.mapperstruct;

import com.holderzone.erp.entity.domain.InventoryDO;
import com.holderzone.saas.store.dto.erp.erpretail.req.CreateInventoryReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.InventoryDetailRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.InventoryManageRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.stereotype.Component;


@Component
@Mapper(componentModel = "spring")
public interface InventoryMapstruct {

    @Mapping(source = "inventoryDate", target = "inventoryDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    InventoryDO fromCreateInventoryReqDTOTOInventoryDO(CreateInventoryReqDTO createInventoryReqDTO);

    @Mapping(source = "inventoryDate", target = "inventoryDate", dateFormat = "yyyy-MM-dd HH:mm:ss")
    InventoryManageRespDTO fromInventoryDOTOManageRespDTO(InventoryDO inventoryDO);

    @Mapping(source = "gmtCreate", target = "invoiceTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    InventoryDetailRespDTO fromInventoryDOTODetailRespDTO(InventoryDO inventoryDO);
}