package com.holderzone.erp.mapperstruct;

import com.holderzone.erp.entity.bo.*;
import com.holderzone.erp.entity.domain.*;
import com.holderzone.saas.store.dto.erp.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/04/29 下午 16:10
 * @description
 */
@Mapper
public interface InOutDocumentTransform {

    InOutDocumentTransform INSTANCE = Mappers.getMapper(InOutDocumentTransform.class);

    /**
     * InOutDocumentAddOrUpdateDTO 转 WarehouseInOutDocumentDO
     *
     * @param inOutDocumentDTO
     * @return
     */
    @Mappings({
            @Mapping(target = "createStaffGuid", source = "userGuid"),
            @Mapping(target = "createStaffName", source = "userName"),
            @Mapping(target = "storeGuid", source = "documentStoreGuid")
    })
    InOutDocumentDO inOutDocumentDtoToDo(InOutDocumentAddOrUpdateDTO inOutDocumentDTO);

    /**
     * InOutDocumentDetailAddOrUpdateDTO 转 WarehouseInOutDocumentDetailDO
     *
     * @param addOrUpdateDTO
     * @return
     */
    InOutDocumentDetailDO inOutDocumentDetailAddOrUpdateDtoToDo(InOutDocumentDetailAddOrUpdateDTO addOrUpdateDTO);

    /**
     * List<InOutDocumentDetailAddOrUpdateDTO> 转 List<WarehouseInOutDocumentDetailDO>
     *
     * @param list
     * @return
     */
    List<InOutDocumentDetailDO> inOutDocumentDetailAddOrUpdateDtosToDos(List<InOutDocumentDetailAddOrUpdateDTO> list);

    /**
     * MaterialDTO 转 InOutDocumentMaterialInfoBO
     *
     * @param materialDTO
     * @return
     */
    @Mappings({
            @Mapping(target = "materialGuid", source = "guid"),
            @Mapping(target = "materialCode", source = "code"),
            @Mapping(target = "materialName", source = "name"),
            @Mapping(target = "stock", source = "stock"),
            @Mapping(target = "masterUnitGuid", source = "unit"),
            @Mapping(target = "masterUnitName", source = "unitName"),
            @Mapping(target = "auxiliaryUnitGuid", source = "auxiliaryUnit"),
            @Mapping(target = "auxiliaryUnitName", source = "auxiliaryUnitName"),
            @Mapping(target = "masterUnitCount", source = "conversionMain"),
            @Mapping(target = "auxiliaryUnitCount", source = "conversionAuxiliary")
    })
    InOutDocumentMaterialInfoBO materialToInOutDocumentMaterialInfoBO(MaterialDTO materialDTO);

    /**
     * List<MaterialDTO> 转 List<InOutDocumentMaterialInfoBO>
     *
     * @param materialInfoList
     * @return
     */
    List<InOutDocumentMaterialInfoBO> materialDtosToInOutDocumentMaterialInfoBos(List<MaterialDTO> materialInfoList);

    /**
     * PricingSchemesDTO 转 InOutDocumentMaterialUnitPriceBO
     *
     * @param pricingSchemes
     * @return
     */
    @Mappings({
            @Mapping(target = "materialGuid", source = "materialGuid"),
            @Mapping(target = "unitGuid", source = "dealUnit"),
            @Mapping(target = "unitPrice", source = "dealPrice")
    })
    InOutDocumentMaterialUnitPriceBO pricingSchemesDtoToInOutDocumentMaterialUnitPriceBO(PricingSchemesDTO pricingSchemes);

    /**
     * List<PricingSchemesDTO> 转 List<InOutDocumentMaterialUnitPriceBO>
     *
     * @param pricingSchemesList
     * @return
     */
    List<InOutDocumentMaterialUnitPriceBO> pricingSchemesDtosToInOutDocumentMaterialUnitPriceBOs(List<PricingSchemesDTO> pricingSchemesList);

    /**
     * InOutDocumentMaterialInfoBO 转 InOutDocumentDetailSelectDTO
     *
     * @param inOutDocumentMaterialInfoBO
     * @return
     */
    @Mappings({
            @Mapping(target = "materialGuid", source = "materialGuid"),
            @Mapping(target = "materialCode", source = "materialCode"),
            @Mapping(target = "materialName", source = "materialName"),
            @Mapping(target = "stock", source = "stock"),
            @Mapping(target = "unitGuid", ignore = true),
            @Mapping(target = "unitName", ignore = true),
            @Mapping(target = "unitPrice", ignore = true),
            @Mapping(target = "totalAmount", ignore = true),
            @Mapping(target = "materialUnitList", ignore = true)
    })
    InOutDocumentDetailSelectDTO inOutDocumentMaterialInfoBoToInOutDocumentDetailSelectDTO(InOutDocumentMaterialInfoBO inOutDocumentMaterialInfoBO);

    /**
     * InOutDocumentMaterialUnitBO 转 InOutDocumentMaterialUnitDTO
     *
     * @param inOutDocumentMaterialUnitBO
     * @return
     */
    @Mappings({
            @Mapping(target = "unitGuid", source = "unitGuid"),
            @Mapping(target = "unitName", source = "unitName")
    })
    InOutDocumentMaterialUnitDTO materialUnitBoToMaterialUnitDto(InOutDocumentMaterialUnitBO inOutDocumentMaterialUnitBO);

    /**
     * List<InOutDocumentMaterialUnitBO> 转 List<InOutDocumentMaterialUnitDTO>
     *
     * @param materialUnitBOList
     * @return
     */
    List<InOutDocumentMaterialUnitDTO> materialUnitBosToMaterialUnitDtos(List<InOutDocumentMaterialUnitBO> materialUnitBOList);

    /**
     * WarehouseInOutDocumentDetailDO 转 DocumentMaterialInAndReturnCountBO
     *
     * @param detail
     * @return
     */
    @Mappings({
            @Mapping(target = "guid", source = "guid"),
            @Mapping(target = "inCount", source = "count"),
            @Mapping(target = "returnCount", source = "returnCount")
    })
    DocumentMaterialInAndReturnCountBO inOutDocumentDetailDoToDocumentMaterialInAndReturnCountBo(InOutDocumentDetailDO detail);

    /**
     * List<WarehouseInOutDocumentDetailDO> 转 List<DocumentMaterialInAndReturnCountBO>
     *
     * @param detailList
     * @return
     */
    List<DocumentMaterialInAndReturnCountBO> inOutDocumentDetailDosToDocumentMaterialInAndReturnCountBos(List<InOutDocumentDetailDO> detailList);

    /**
     * WarehouseInOutDocumentDetailDO 转 InOutDocumentDetailSelectDTO
     *
     * @param detailDO
     * @return
     */
    InOutDocumentDetailSelectDTO inOutDocumentDetailDoToDetailSelectDto(InOutDocumentDetailDO detailDO);

    /**
     * List<WarehouseInOutDocumentDetailDO> 转 List<InOutDocumentDetailSelectDTO>
     *
     * @param detailList
     * @return
     */
    List<InOutDocumentDetailSelectDTO> inOutDocumentDetailDosToDetailSelectDtos(List<InOutDocumentDetailDO> detailList);

    /**
     * InOutDocumentDetailSelectDTO 转 InOutDocumentDetailAddOrUpdateDTO
     *
     * @param detailSelectDTO
     * @return
     */
    InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailSelectDtoToAddOrUpdateDto(InOutDocumentDetailSelectDTO detailSelectDTO);

    /**
     * List<InOutDocumentDetailSelectDTO> 转 List<InOutDocumentDetailAddOrUpdateDTO>
     *
     * @param detailList
     * @return
     */
    List<InOutDocumentDetailAddOrUpdateDTO> inOutDocumentDetailSelectDtosToAddOrUpdateDtos(List<InOutDocumentDetailSelectDTO> detailList);

    /**
     * WarehouseInOutDocumentDetailDO 转 UpdateStockBO
     *
     * @param detailDO
     * @return
     */
    @Mappings({
            @Mapping(target = "materialGuid", source = "materialGuid"),
            @Mapping(target = "count", source = "count"),
            @Mapping(target = "materialUnit", source = "unitGuid")
    })
    UpdateStockBO inOutDocumentDetailDoToUpdateStockBo(InOutDocumentDetailDO detailDO);

    /**
     * List<WarehouseInOutDocumentDetailDO> 转 List<UpdateStockBO>
     *
     * @param detailList
     * @return
     */
    List<UpdateStockBO> inOutDocumentDetailDosToUpdateStockBos(List<InOutDocumentDetailDO> detailList);

    /**
     * InOutDocumentDetailAddOrUpdateDTO 转 InOutDocumentDetailBO
     *
     * @param detailAddOrUpdateDTO
     * @return
     */
    InOutDocumentDetailBO inOutDocumentDetailAddOrUpdateDtoToBo(InOutDocumentDetailAddOrUpdateDTO detailAddOrUpdateDTO);

    /**
     * List<InOutDocumentDetailAddOrUpdateDTO> 转 List<InOutDocumentDetailBO>
     *
     * @param detailList
     * @return
     */
    List<InOutDocumentDetailBO> inOutDocumentDetailAddOrUpdateDtosToBos(List<InOutDocumentDetailAddOrUpdateDTO> detailList);

    /**
     * WarehouseInOutDocumentDetailDO 转 InOutDocumentDetailBO
     *
     * @param detailDO
     * @return
     */
    InOutDocumentDetailBO inOutDocumentDetailDoToBo(InOutDocumentDetailDO detailDO);

    /**
     * List<WarehouseInOutDocumentDetailDO> 转 List<InOutDocumentDetailBO>
     *
     * @param materialReturnCountList
     * @return
     */
    List<InOutDocumentDetailBO> inOutDocumentDetailDosToBos(List<InOutDocumentDetailDO> materialReturnCountList);

    /**
     * InOutDocumentDetailBO 转 WarehouseInOutDocumentDetailDO
     * 用于退货时，更改入库单的退货数量
     *
     * @param detailBO
     * @return
     */
    @Mappings({
            @Mapping(target = "guid", ignore = true),
            @Mapping(target = "documentGuid", ignore = true),
            @Mapping(target = "materialGuid", source = "materialGuid"),
            @Mapping(target = "materialCode", ignore = true),
            @Mapping(target = "materialName", ignore = true),
            @Mapping(target = "count", ignore = true),
            @Mapping(target = "unitGuid", ignore = true),
            @Mapping(target = "unitName", ignore = true),
            @Mapping(target = "unitPrice", ignore = true),
            @Mapping(target = "totalAmount", ignore = true),
            @Mapping(target = "returnCount", source = "count")
    })
    InOutDocumentDetailDO returnDocumentDetailBoToInDocumentDetailDo(InOutDocumentDetailBO detailBO);

    /**
     * List<InOutDocumentDetailBO> 转 List<WarehouseInOutDocumentDetailDO>
     * 用于退货时，更改入库单的退货数量
     *
     * @param detailBOList
     * @return
     */
    List<InOutDocumentDetailDO> returnDocumentDetailBosToInDocumentDetailDos(List<InOutDocumentDetailBO> detailBOList);

    /**
     * WarehouseInOutDocumentDO 转 InOutDocumentSelectDTO
     *
     * @param inOutDocumentDO
     * @return
     */
    @Mappings({
            @Mapping(target = "documentStoreGuid", source = "storeGuid")
    })
    InOutDocumentSelectDTO inOutDocumentDoToSelectDto(InOutDocumentDO inOutDocumentDO);

    /**
     * InOutDocumentQueryDTO 转 InOutDocumentQuery
     *
     * @param queryDTO
     * @return
     */
    @Mappings({
            @Mapping(target = "offset", expression = "java((queryDTO.getCurrentPage() - 1) * queryDTO.getPageSize())")
    })
    InOutDocumentQuery inOutDocumentQueryDtoToQuery(InOutDocumentQueryDTO queryDTO);

    /**
     * List<WarehouseInOutDocumentDO> 转 List<InOutDocumentSelectDTO>
     *
     * @param documentDOList
     * @return
     */
    List<InOutDocumentSelectDTO> inOutDocumentDosToSelectDtos(List<InOutDocumentDO> documentDOList);

    /**
     * InOutContactDocumentQueryDTO 转 InOutContactDocumentQuery
     *
     * @param queryDTO
     * @return
     */
    InOutContactDocumentQuery inOutContactDocumentQueryDtoToQuery(InOutContactDocumentQueryDTO queryDTO);

    /**
     * InOutDocumentFlowDetailQueryDTO 转 InOutDocumentFlowDetailQuery
     *
     * @param queryDTO
     * @return
     */
    @Mappings({
            @Mapping(target = "offset", expression = "java((queryDTO.getCurrentPage() - 1) * queryDTO.getPageSize())")
    })
    InOutDocumentFlowDetailQuery inOutDocumentFlowDetailQueryDtoToQuery(InOutDocumentFlowDetailQueryDTO queryDTO);

    /**
     * InOutDocumentFlowDetailDO 转 InOutDocumentFolwDetailSelectDTO
     *
     * @param inOutDocumentFlowDetailDO
     * @return
     */
    @Mappings({
            @Mapping(target = "modifiedCount", expression = "java(inOutDocumentFlowDetailDO.getInOutType() == 0 ? \"+\" + inOutDocumentFlowDetailDO.getCount() :" +
                    "\"-\" + inOutDocumentFlowDetailDO.getCount())")
    })
    InOutDocumentFolwDetailSelectDTO inOutDocumentFlowDetailToSelectDto(InOutDocumentFlowDetailDO inOutDocumentFlowDetailDO);

    /**
     * List<InOutDocumentFlowDetailDO> 转 List<InOutDocumentFolwDetailSelectDTO>
     *
     * @param flowDetailDoList
     * @return
     */
    List<InOutDocumentFolwDetailSelectDTO> inOutDocumentFlowDetailsToSelectDtos(List<InOutDocumentFlowDetailDO> flowDetailDoList);

    SuppliersReconciliationQueryDO suppliersReconciliationQueryDTOToDO(SuppliersReconciliationQueryDTO queryDTO);

    /**
     * InOutDocumentSelectDTO 转 InOutDocumentAddOrUpdateDTO
     *
     * @param selectDTO
     * @return
     */
    InOutDocumentAddOrUpdateDTO inOutDocumentSelectDtoToAddOrUpdateDto(InOutDocumentSelectDTO selectDTO);

    /**
     * SkuInfo 转 GoodsInfo
     *
     * @param skuInfo
     * @return
     */
    @Mappings({
            @Mapping(target = "goodsSku", source = "skuGuid"),
            @Mapping(target = "count", source = "count")
    })
    GoodsInfo skuInfoToGoodsInfo(SkuInfo skuInfo);

    /**
     * List<SkuInfo> 转 List<InOutDocumentBomQueryBO.GoodsInfo>
     *
     * @param skuInfoList
     * @return
     */
    List<GoodsInfo> skuInfosToGoodsInfos(List<SkuInfo> skuInfoList);

    /**
     * OrderSkuDTO 转 InOutDocumentBomQueryBO
     *
     * @param orderSkuDTO
     * @return
     */
    @Mappings({
            @Mapping(target = "storeGuid", source = "storeGuid"),
            @Mapping(target = "goodsInfoList", source = "skuList")
    })
    InOutDocumentBomQueryBO orderSkuDtoToInOutDocumentBomQueryBo(OrderSkuDTO orderSkuDTO);

    /**
     * InOutDocumentDetailBO 转 InOutDocumentDetailAddOrUpdateDTO
     *
     * @param detailBO
     * @return
     */
    InOutDocumentDetailAddOrUpdateDTO inOutDocumentDetailBoToAddOrUpdateDto(InOutDocumentDetailBO detailBO);

    /**
     * List<InOutDocumentDetailBO> 转 List<InOutDocumentDetailAddOrUpdateDTO>
     *
     * @param detailList
     * @return
     */
    List<InOutDocumentDetailAddOrUpdateDTO> inOutDocumentDetailBosToAddOrUpdateDtos(List<InOutDocumentDetailBO> detailList);

    /**
     * InOutDocumentBO 转 InOutDocumentAddOrUpdateDTO
     *
     * @param inOutDocumentBO
     * @return
     */
    @Mappings({
            @Mapping(target = "documentStoreGuid", source = "storeGuid"),
            @Mapping(target = "userGuid", source = "operatorGuid"),
            @Mapping(target = "userName", source = "operatorName")
    })
    InOutDocumentAddOrUpdateDTO inOutDocumentBoToAddorUpdateDto(InOutDocumentBO inOutDocumentBO);

    /**
     * SkuInfo 转 SkuInfoBO
     *
     * @param skuInfo
     * @return
     */
    SkuInfoBO skuInfoToBo(SkuInfo skuInfo);

    /**
     * List<SkuInfo> 转 List<SkuInfoBO>
     *
     * @param skuInfoList
     * @return
     */
    List<SkuInfoBO> skuInfosToBos(List<SkuInfo> skuInfoList);

    /**
     * OrderSkuDTO 转 OrderSkuBO
     *
     * @param orderSkuDTO
     * @return
     */
    OrderSkuBO orderSkuDtoToBo(OrderSkuDTO orderSkuDTO);


    List<OrderSkuBO> orderSkuDtoToBoBatch(List<OrderSkuDTO> orderSkuDTO);

    /**
     * InOutDocumentMaterialInfoBO 转 InOutDocumentDetailDO
     *
     * @param materialInfoBO
     * @return
     */
    @Mappings({
            @Mapping(target = "materialGuid", source = "materialGuid"),
            @Mapping(target = "materialCode", source = "materialCode"),
            @Mapping(target = "materialName", source = "materialName"),
            @Mapping(target = "stock", source = "stock"),
            @Mapping(target = "unitGuid", source = "masterUnitGuid"),
            @Mapping(target = "unitName", source = "masterUnitName"),
            @Mapping(target = "mainUnitGuid", source = "masterUnitGuid"),
            @Mapping(target = "mainUnitName", source = "masterUnitName"),

    })
    InOutDocumentDetailDO inOutDocumentMaterialInfoBoToDetailDO(InOutDocumentMaterialInfoBO materialInfoBO);

    /**
     * InOutDocumentMaterialInfoBO 转 InOutDocumentDetailBO
     *
     * @param materialInfoBO
     * @return
     */
    @Mappings({
            @Mapping(target = "materialGuid", source = "materialGuid"),
            @Mapping(target = "materialCode", source = "materialCode"),
            @Mapping(target = "materialName", source = "materialName"),
            @Mapping(target = "stock", source = "stock"),
            @Mapping(target = "unitGuid", source = "masterUnitGuid"),
            @Mapping(target = "unitName", source = "masterUnitName"),
            @Mapping(target = "mainUnitGuid", source = "masterUnitGuid"),
            @Mapping(target = "mainUnitName", source = "masterUnitName"),

    })
    InOutDocumentDetailBO inOutDocumentMaterialInfoBoToDetailBO(InOutDocumentMaterialInfoBO materialInfoBO);


    /**
     * SkuInfoBO 转 GoodsInfo
     *
     * @param skuInfoBO
     * @return
     */
    @Mappings({
            @Mapping(target = "goodsSku", source = "skuGuid"),
            @Mapping(target = "count", source = "count")
    })
    GoodsInfo skuInfoBoToGoodsInfo(SkuInfoBO skuInfoBO);

    /**
     * OrderSkuBO 转 InOutDocumentBomQueryBO
     *
     * @param orderSkuBO
     * @return
     */
    @Mappings({
            @Mapping(target = "storeGuid", source = "storeGuid"),
            @Mapping(target = "goodsInfoList", source = "skuList")
    })
    InOutDocumentBomQueryBO orderSkuBoToInOutDocumentQueryBo(OrderSkuBO orderSkuBO);
}
