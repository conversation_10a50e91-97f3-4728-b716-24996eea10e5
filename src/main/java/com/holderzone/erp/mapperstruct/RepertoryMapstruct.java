package com.holderzone.erp.mapperstruct;

import com.holderzone.erp.entity.domain.RepertoryDO;
import com.holderzone.saas.store.dto.erp.erpretail.req.CreateRepertoryReqDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.RepertoryDetailInfoRespDTO;
import com.holderzone.saas.store.dto.erp.erpretail.resp.RepertoryManageRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.stereotype.Component;


@Component
@Mapper(componentModel = "spring")
public interface RepertoryMapstruct {

    @Mapping(source = "invoiceMakeTime", target = "invoiceMakeTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    RepertoryDO fromCreateRepertoryReqDTO(CreateRepertoryReqDTO inRepertoryDTO);

    @Mapping(source = "invoiceMakeTime", target = "invoiceMakeTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    RepertoryDetailInfoRespDTO fromRepertoryDO(RepertoryDO repertoryDO);

    @Mapping(source = "invoiceMakeTime", target = "invoiceMakeTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    RepertoryManageRespDTO repertoryDOToRepertoryManageRespDTO(RepertoryDO repertoryDO);
}