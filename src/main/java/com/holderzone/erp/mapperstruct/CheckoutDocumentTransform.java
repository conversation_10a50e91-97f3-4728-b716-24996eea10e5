package com.holderzone.erp.mapperstruct;

import com.holderzone.erp.entity.domain.CheckoutDocumentDO;
import com.holderzone.erp.entity.domain.CheckoutDocumentDetailDO;
import com.holderzone.erp.entity.domain.CheckoutDocumentQuery;
import com.holderzone.saas.store.dto.erp.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/08 上午 10:42
 * @description
 */
@Mapper
public interface CheckoutDocumentTransform {

    CheckoutDocumentTransform INSTANCE = Mappers.getMapper(CheckoutDocumentTransform.class);

    /**
     * CheckoutDocumentAddOrUpdateDTO 转 CheckoutDocumentDO
     *
     * @param addOrUpdateDTO
     * @return
     */
    @Mappings({
            @Mapping(target = "createStaffGuid", source = "userGuid"),
            @Mapping(target = "createStaffName", source = "userName"),
            @Mapping(target = "storeGuid", source = "documentStoreGuid")
    })
    CheckoutDocumentDO checkoutDocumentAddOrUpdateDtoToDo(CheckoutDocumentAddOrUpdateDTO addOrUpdateDTO);

    /**
     * CheckoutDocumentDetailAddOrUpdateDTO 转 CheckoutDocumentDetailDO
     *
     * @param addOrUpdateDTO
     * @return
     */
    CheckoutDocumentDetailDO checkoutDocumentDetailAddOrUpdateDtoToDo(CheckoutDocumentDetailAddOrUpdateDTO addOrUpdateDTO);

    /**
     * List<CheckoutDocumentDetailAddOrUpdateDTO> 转 List<CheckoutDocumentDetailDO>
     *
     * @param detailList
     * @return
     */
    List<CheckoutDocumentDetailDO> checkoutDocumentDetailAddOrUpdateDtosToDos(List<CheckoutDocumentDetailAddOrUpdateDTO> detailList);

    /**
     * MaterialDTO 转 CheckoutDocumentDetailSelectDTO
     *
     * @param materialDTO
     * @return
     */
    @Mappings({
            @Mapping(target = "materialGuid", source = "guid"),
            @Mapping(target = "materialCode", source = "code"),
            @Mapping(target = "materialName", source = "name"),
            @Mapping(target = "stock", source = "stock"),
            @Mapping(target = "unitGuid", source = "unit"),
            @Mapping(target = "unitName", source = "unitName"),
            @Mapping(target = "checkCount", source = "stock"),
            @Mapping(target = "checkoutResult", ignore = true),
            @Mapping(target = "guid", ignore = true)
    })
    CheckoutDocumentDetailSelectDTO materialDtoToCheckoutDocumentDetailSelectDto(MaterialDTO materialDTO);

    /**
     * List<MaterialDTO> 转 List<CheckoutDocumentDetailSelectDTO>
     *
     * @param materialDtoList
     * @return
     */
    List<CheckoutDocumentDetailSelectDTO> materialDtosToCheckoutDocumentDetailSelectDtos(List<MaterialDTO> materialDtoList);

    /**
     * CheckoutDocumentDetailDO 转 CheckoutDocumentDetailSelectDTO
     *
     * @param detailDO
     * @return
     */
    CheckoutDocumentDetailSelectDTO checkoutDocumentDetailDoToSelectDto(CheckoutDocumentDetailDO detailDO);

    /**
     * List<CheckoutDocumentDetailDO> 转 List<CheckoutDocumentDetailSelectDTO>
     *
     * @param detailList
     * @return
     */
    List<CheckoutDocumentDetailSelectDTO> checkoutDocumentDetailDosToSelectDtos(List<CheckoutDocumentDetailDO> detailList);

    /**
     * CheckoutDocumentDO 转 CheckoutDocumentSelectDTO
     *
     * @param checkoutDocumentDO
     * @return
     */
    @Mappings({
            @Mapping(target = "documentStoreGuid", source = "storeGuid")
    })
    CheckoutDocumentSelectDTO checkoutDocumentDoToSelectDto(CheckoutDocumentDO checkoutDocumentDO);

    /**
     * CheckoutDocumentQueryDTO 转 CheckoutDocumentQuery
     *
     * @param queryDTO
     * @return
     */
    @Mappings({
            @Mapping(target = "offset", expression = "java((queryDTO.getCurrentPage() - 1) * queryDTO.getPageSize())")
    })
    CheckoutDocumentQuery checkoutDocumentQueryDtoToQuery(CheckoutDocumentQueryDTO queryDTO);

    /**
     * List<CheckoutDocumentDO> 转 List<CheckoutDocumentSelectDTO>
     *
     * @param checkoutDocumentDOList
     * @return
     */
    List<CheckoutDocumentSelectDTO> checkoutDocumentDosToSelectDtos(List<CheckoutDocumentDO> checkoutDocumentDOList);

    /**
     * CheckoutDocumentDetailSelectDTO 转 CheckoutDocumentDetailAddOrUpdateDTO
     *
     * @param selectDTO
     * @return
     */
    CheckoutDocumentDetailAddOrUpdateDTO checkoutDocumentDetailSelectDtoToAddOrUpdateDto(CheckoutDocumentDetailSelectDTO selectDTO);

    /**
     * List<CheckoutDocumentDetailSelectDTO> 转 List<CheckoutDocumentDetailAddOrUpdateDTO>
     *
     * @param detailList
     * @return
     */
    List<CheckoutDocumentDetailAddOrUpdateDTO> checkoutDocumentDetailSelectDtosToAddOrUpdateDtos(List<CheckoutDocumentDetailSelectDTO> detailList);

    /**
     * CheckoutDocumentSelectDTO 转 CheckoutDocumentAddOrUpdateDTO
     *
     * @param documentSelectDTO
     * @return
     */
    CheckoutDocumentAddOrUpdateDTO checkoutDocumentSelectDtoToAddOrUpdateDto(CheckoutDocumentSelectDTO documentSelectDTO);
}
