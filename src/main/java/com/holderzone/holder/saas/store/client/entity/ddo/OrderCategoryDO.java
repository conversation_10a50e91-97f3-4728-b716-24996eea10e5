package com.holderzone.holder.saas.store.client.entity.ddo;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderCategoryDO {

    public static OrderCategoryDO DEFAULT = new OrderCategoryDO(0, 0);
    @ApiModelProperty(value = "点餐分类 0-堂食，1-自取改为预点单，2-外卖，3-券 其它")
    private Integer categoryType;
    @ApiModelProperty(value = "顾客数")
    private Integer customerCount;


    public static OrderCategoryDO INSTANCE() {
        return new OrderCategoryDO(0, 0);
    }
}
