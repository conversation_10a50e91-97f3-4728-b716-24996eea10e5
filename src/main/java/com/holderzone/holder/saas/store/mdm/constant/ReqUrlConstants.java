package com.holderzone.holder.saas.store.mdm.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReqUrlConstants
 * @date 2019/11/18 14:56
 * @description
 * @program holder-saas-store
 */
public interface ReqUrlConstants {


    interface Type {
        /**
         * 批量添加分类（post）
         */
        String BATCH_CREATE_TYPE_URL = "/item_type/add";

        /**
         * 更新分类（put）
         */
        String UPDATE_TYPE_URL = "/item_type/update";

        /**
         * 批量删除分类（delete）
         */
        String BATCH_DELETE_TYPE_URL = "/item_type/delete";
    }

    interface Item {
        /**
         * 批量添加商品（post）
         */
        String BATCH_CREATE_ITEM_URL = "/item/addItemList";

        /**
         * 更新商品（put）
         */
        String UPDATE_ITEM_URL = "/item/updateItem";

        /**
         * 批量删除商品（delete）
         */
        String BATCH_DELETE_ITEM_URL = "/item/deleteItemList";
    }

    interface Sku {
        /**
         * 批量添加商品规格（post）
         */
        String BATCH_CREATE_SKU_URL = "/item_sku/add";

        /**
         * 更新商品规格（put）
         */
        String UPDATE_SKU_URL = "/item_sku/update";

        /**
         * 批量删除商品规格（delete）
         */
        String BATCH_DELETE_SKU_URL = "/item_sku/delete";
    }

    /**
     * 添加商品（post）
     */
    String ADD_ITEM_URL = "/item/addItem";

    /**
     * 批量添加商品（post）
     */
    String ADD_ITEM_LIST_URL = "/item/addItemList";

    /**
     * 删除商品（post）
     */
    String DELETE_ITEM = "/item/deleteItem";

    /**
     * 批量删除商品（post）
     */
    String DELETE_ITEM_BATCH = "/item/deleteItem";


    /**
     * 查询商品（post）
     */
    String SELECT_ITEM_URL = "/item/selectItemList";

    /**
     * 更新商品（post）
     */
    String UPDATE_ITEM_URL = "/item/updateItem";


    /**
     * 批量添加SKU（post）
     */
    String ADD_SKU_LIST_URL = "";

    /**
     * 删除sku（post）
     */
    String DELETE_SKU = "";

    /**
     * 更新sku（post）
     */
    String UPDATE_SKU_URL = "";

    /**
     * 查询sku（post）
     */
    String SELECT_SKU_URL = "";


    /**
     * 批量添加分类（post）
     */
    String ADD_TYPE_LIST_URL = "";

    /**
     * 删除分类（post）
     */
    String DELETE_TYPE = "";

    /**
     * 更新分类（post）
     */
    String UPDATE_TYPE_URL = "";

    /**
     * 查询分类（post）
     */
    String SELECT_TYPE_URL = "";


    /**
     * 添加物料（post）
     */
    String ADD_MATERIAL = "/material";

    /**
     * 批量添加物料（post）
     */
    String ADD_MATERIAL_BATCH = "/material/batch";

    /**
     * 修改物料信息（put）
     */
    String UPDATE_MATERIAL_URL = "/material";

    /**
     * 删除物料（delete）
     */
    String DEL_MATERIAL_URL = "/material";

    /**
     * 根据GUID查询物料信息（post）
     */
    String FIND_MATERIAL_URL = "/material/findByGuid";

    interface User {

        /**
         * 添加用户（post）,更新用户（put）,删除用户（delete）
         */
        String USER_URL = "/user";

        /**
         * 批量添加用户（post）
         */
        String BATCH_CREATE_USER_URL = "/user/batch";

        /**
         * 根据GUID查询用户（post）
         */
        String FIND_USER_URL = "/user/find";

        /**
         * 根据条件查询用户（post）
         */
        String FIND_USER_BY_CONDITION_URL = "/user/query";
    }

    /**
     * 创建组织（post）
     */
    String CREATE_ORGANIZATION_URL = "/organization/create";

    /**
     * 更新组织
     */
    String UPDATE_ORGANIZATION_URL = "/organization/update";

    /**
     * 删除组织
     */
    String DELETE_ORGANIZATION_URL = "/organization/delete";

    /**
     * 查询组织
     */
    String QUERY_ORGANIZATION_URL = "/organization/query";

    /**
     * 创建品牌
     */
    String CREATE_BRAND_URL = "/brand/create";

    /**
     * 更新品牌
     */
    String UPDATE_BRAND_URL = "/brand/update";

    /**
     * 删除品牌
     */
    String DELETE_BRAND_URL = "/brand/delete";
}
