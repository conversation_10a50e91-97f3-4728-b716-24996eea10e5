package com.holderzone.holder.saas.store.message.listener;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.message.config.RocketMqConfig;
import com.holderzone.holder.saas.store.message.service.MessageService;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessMsgListener
 * @date 2018/09/04 17:22
 * @description
 * @program holder-saas-bussiness-message
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.BUSINESS_MSG_TOPIC,
        tags = RocketMqConfig.BUSINESS_MSG_TAG,
        consumerGroup = RocketMqConfig.BUSINESS_MSG_CONSUMER)
public class BusinessMsgListener extends AbstractRocketMqConsumer<RocketMqTopic, BusinessMessageDTO> {

    private final MessageService messageService;

    @Autowired
    public BusinessMsgListener(MessageService messageService) {
        this.messageService = messageService;
    }

    @Override
    public boolean consumeMsg(BusinessMessageDTO messageDTO, MessageExt messageExt) {
        UserContextUtils.put(messageExt.getProperty("message-context"));
        EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        log.info("业务系统推送消息，messageDTO={}", JacksonUtils.writeValueAsString(messageDTO));
        try {
            messageService.insert(messageDTO);
        } finally {
            UserContextUtils.remove();
            EnterpriseIdentifier.remove();
        }
        return true;
    }
}
