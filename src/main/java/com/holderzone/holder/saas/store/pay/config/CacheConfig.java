package com.holderzone.holder.saas.store.pay.config;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019/05/06 上午 18:10
 * @description
 */
@Configuration
public class CacheConfig {

    @Bean
    public Cache<String, PaymentInfoDTO> paymentInfoCache() {
        return CacheBuilder.newBuilder()
                .maximumSize(500)
                .expireAfterAccess(30, TimeUnit.MINUTES)
                .build();
    }
}
