package com.holderzone.holder.saas.store.report.mapper;

import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.DebtTotalStatisticsDTO;
import com.holderzone.saas.store.dto.report.resp.DebtUnitRecordDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 挂账还款
 */
@Mapper
public interface TradeDebtMapper {

    DebtTotalStatisticsDTO statistics(@Param("query") ReportQueryVO query);

    Integer count(@Param("query") ReportQueryVO query);

    List<DebtUnitRecordDTO> pageInfo(@Param("query") ReportQueryVO query);

}
