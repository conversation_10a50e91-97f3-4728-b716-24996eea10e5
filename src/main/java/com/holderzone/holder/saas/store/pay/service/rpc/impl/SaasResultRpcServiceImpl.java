package com.holderzone.holder.saas.store.pay.service.rpc.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.pay.constant.PayConstant;
import com.holderzone.holder.saas.store.pay.entity.AggPayAttachDataVO;
import com.holderzone.holder.saas.store.pay.entity.HandlerPayBO;
import com.holderzone.holder.saas.store.pay.entity.domain.PayRecordDO;
import com.holderzone.holder.saas.store.pay.mapstruct.AggPayMapstruct;
import com.holderzone.holder.saas.store.pay.service.PayRecordService;
import com.holderzone.holder.saas.store.pay.service.rpc.SaasResultRpcService;
import com.holderzone.holder.saas.store.pay.utils.ThrowableExtUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.pay.constant.AggPayStateEnum;
import com.holderzone.saas.store.dto.pay.constant.AggRefundStateEnum;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;
import static org.springframework.http.HttpMethod.POST;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DispatchResultServiceImpl
 * @date 2019/03/14 15:51
 * @description 分发支付结果service
 * @program holder-saas-store-trading-center
 */
@Slf4j
@Service
public class SaasResultRpcServiceImpl implements SaasResultRpcService {

    private final RestTemplate restTemplate;

    private final AggPayMapstruct aggPayMapStruct;

    private final PayRecordService payRecordService;

    @Autowired
    public SaasResultRpcServiceImpl(RestTemplate restTemplate,AggPayMapstruct aggPayMapStruct,
                                    PayRecordService payRecordService) {
        this.restTemplate = restTemplate;
        this.aggPayMapStruct = aggPayMapStruct;
        this.payRecordService = payRecordService;
    }

    /**
     * {
     * "code": "10000",
     * "msg": "success",
     * "amount": 0,
     * "subject": "会员充值",
     * "body": "会员充值",
     * "orderHolderNo": "1905311703000410003",
     * "payPowerId": "32",
     * "orderNo": "15592933807712664790",
     * "paySt": "2",
     * "bankTransactionId": "18********22001436761040955820",
     * "orderGUID": "6540150451451074561",
     * "payGUID": "6540150451451074561",
     * "orderDt": "********",
     * "timePaid": "2019-05-31 17:03:01",
     * "description": "会员充值",
     * "fee": "0",
     * "extra": "会员充值",
     * "created": "2019-05-31 17:03:01",
     * "payChannelId": "7",
     * "attachData": "6511901269374959617:********170300270000",
     * "signature": "0e7db128fe70581f62d3db935d763357d363778f"
     * }
     *
     * @param handlerPayBO
     * @param pollingRespDTO
     */
    @Override
    public void handlePayResult(HandlerPayBO handlerPayBO, AggPayPollingRespDTO pollingRespDTO) {
        log.info("handlePayResult:handlerPayBO:{},AggPayPollingRespDTO:{}",handlerPayBO,pollingRespDTO);
        if (handlerPayBO==null){
            return;
        }
        AggPayAttachDataVO aggPayAttachDataVO = JacksonUtils.toObject(AggPayAttachDataVO.class, pollingRespDTO.getAttachData());
        if (Boolean.TRUE.equals(handlerPayBO.getIsQuickReceipt())
                && AggPayStateEnum.SUCCESS.getId().equals(pollingRespDTO.getPaySt())) {
            // 持久化支付记录
            String enterpriseGuid = aggPayAttachDataVO.getEnterpriseGuid();
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            PayRecordDO payRecordDO = aggPayMapStruct.createPayRecord(pollingRespDTO);
            payRecordDO.setStoreGuid(aggPayAttachDataVO.getStoreGuid());
            payRecordDO.setAmount(payRecordDO.getAmount()
                    .divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
            payRecordDO.setPayPowerName(PayPowerId.getNameById(pollingRespDTO.getPayPowerId()));
            try {
                payRecordDO.setGmtTimePaid(DateTimeUtils.string2LocalDateTime(pollingRespDTO.getTimePaid()));
            } catch (Exception e) {
                log.error("结账时间解析失败, timePaid：{}", pollingRespDTO.getTimePaid());
                payRecordDO.setGmtTimePaid(LocalDateTime.now());
            }
            int count = payRecordService.count(new QueryWrapper<PayRecordDO>().lambda().eq(PayRecordDO::getPayGuid, payRecordDO.getPayGuid()));
            if(count == 0){
                payRecordService.save(payRecordDO);
            }
            return;
        }
        String callBackUrl = handlerPayBO.getInnerCallBackUrl();
        if (StringUtils.isEmpty(callBackUrl)) {
            log.info("callBackUrl is null");
            return;
        }
        try {
            BaseInfo baseInfo = handlerPayBO.getBaseInfo();
            HttpHeaders headers = buildHttpHeaders(baseInfo);
            pollingRespDTO.setIsLast(aggPayAttachDataVO != null && Boolean.TRUE.equals(aggPayAttachDataVO.getIsLast()));
            pollingRespDTO.setCheckoutSuccessFlag(aggPayAttachDataVO != null && Boolean.TRUE.equals(aggPayAttachDataVO.getCheckoutSuccessFlag()));
            SaasNotifyDTO saasNotifyDTO = buildCallbackDTO(baseInfo, pollingRespDTO);
            log.info("回调内部服务：{}，以更新其支付结果，参数：{}", callBackUrl, JacksonUtils.writeValueAsString(saasNotifyDTO));
            doRequest(callBackUrl, headers, saasNotifyDTO);
        } catch (Exception e) {
            // todo 是否补偿回调 retry
            log.info("回调内部服务：{} 失败：{},orderGUid:{}", callBackUrl, ThrowableExtUtils.asStringIfAbsent(e),pollingRespDTO.getOrderGUID());
            throw new RuntimeException("调用服务失败 重试");
        }
    }

    @Override
    public void handleRefundResult(SaasAggRefundDTO saasAggRefundDTO, AggRefundPollingRespDTO aggRefundPollingRespDTO) {
        if (Boolean.TRUE.equals(saasAggRefundDTO.getIsQuickReceipt())) {
            // 将当前退款结果写入Database
            List<AggRefundDetailRespDTO> refundOrderDetail = aggRefundPollingRespDTO.getRefundOrderDetial();
            if (!CollectionUtils.isEmpty(refundOrderDetail)) {
                AggRefundDetailRespDTO aggRefundDetailRespDTO = refundOrderDetail.get(refundOrderDetail.size() - 1);
                if (AggRefundStateEnum.REFUND_SUCCESS.getState().equals(aggRefundDetailRespDTO.getStatus())) {
                    String enterpriseGuid = aggRefundPollingRespDTO.getAttachData();
                    EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
                    PayRecordDO payRecordDO = new PayRecordDO();
                    payRecordDO.setPaySt("4");
                    payRecordDO.setRefOrderNo(aggRefundDetailRespDTO.getRefOrderno());
                    payRecordDO.setGmtRefund(DateTimeUtils.now());
                    payRecordService.update(payRecordDO, new LambdaQueryWrapper<PayRecordDO>()
                            .eq(PayRecordDO::getOrderHolderNo, aggRefundPollingRespDTO.getMchntOrderNo()));
                }
            }
            return;
        }
        String callBackUrl = saasAggRefundDTO.getSaasCallBackUrl();
        if (StringUtils.isEmpty(callBackUrl)) {
            return;
        }
        try {
            BaseInfo baseInfo = aggPayMapStruct.createBaseInfo(saasAggRefundDTO);
            HttpHeaders headers = buildHttpHeaders(baseInfo);
            SaasNotifyDTO saasNotifyDTO = buildCallbackDTO(baseInfo, aggRefundPollingRespDTO);
            log.info("回调内部服务：{}，以更新其退款结果，参数：{}", callBackUrl, JacksonUtils.writeValueAsString(saasNotifyDTO));
            doRequest(callBackUrl, headers, saasNotifyDTO);
        } catch (Exception e) {
            log.info("回调内部服务：{} 失败：{}", callBackUrl, ThrowableExtUtils.asStringIfAbsent(e));
        }
    }

    private HttpHeaders buildHttpHeaders(BaseInfo baseInfo) throws UnsupportedEncodingException {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        UserInfoDTO userInfo = aggPayMapStruct.createUserInfo(baseInfo);
        String json = JacksonUtils.writeValueAsString(userInfo);
        headers.set(USER_INFO, URLEncoder.encode(json, "utf-8"));
        return headers;
    }

    private SaasNotifyDTO buildCallbackDTO(BaseInfo baseInfo, AggPayPollingRespDTO pollingRespDTO) {
        SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        saasNotifyDTO.setBaseInfo(baseInfo);
        if (null != pollingRespDTO.getAmount()) {
            // 将分转为元
            pollingRespDTO.setAmount(pollingRespDTO.getAmount()
                    .divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
        }
        saasNotifyDTO.setAggPayPollingRespDTO(pollingRespDTO);
        return saasNotifyDTO;
    }

    private SaasNotifyDTO buildCallbackDTO(BaseInfo baseInfo, AggRefundPollingRespDTO aggRefundPollingRespDTO) {
        SaasNotifyDTO saasNotifyDTO = new SaasNotifyDTO();
        saasNotifyDTO.setBaseInfo(baseInfo);
        saasNotifyDTO.setAggRefundPollingRespDTO(aggRefundPollingRespDTO);
        return saasNotifyDTO;
    }

    private void doRequest(String callBackUrl, HttpHeaders headers, SaasNotifyDTO saasNotifyDTO) {
        ResponseEntity<String> result = restTemplate.exchange(
                callBackUrl, POST,
                new HttpEntity<>(saasNotifyDTO, headers),
                new ParameterizedTypeReference<String>() {
                }
        );
        String body = result.getBody();
        if (PayConstant.SUCCESS_CALL_BACK.equalsIgnoreCase(body)) {
            log.info("回调内部服务：{}，成功", callBackUrl);
        } else {
            log.info("回调内部服务：{}，失败", callBackUrl);
        }
    }
}
