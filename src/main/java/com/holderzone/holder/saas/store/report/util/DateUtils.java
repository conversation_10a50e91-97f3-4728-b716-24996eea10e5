package com.holderzone.holder.saas.store.report.util;

import com.holderzone.framework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.*;

import static com.holderzone.holder.saas.store.report.constant.EsConstant.ZONE_OF;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DateUtils
 * @date 2019/06/13 14:02
 * @description
 * @program holder-saas-store-report
 */
public class DateUtils {


    /**
     * @param billStr yyyyMMdd
     * @return
     */
    public static String billDateStrToDateStr(String billStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdfTimeStamp = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = sdf.parse(billStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return Objects.isNull(date) ? null : sdfTimeStamp.format(date);
    }

    public static LocalDate billDateStrToLocalDate(String billStr) {
        return LocalDate.parse(billDateStrToDateStr(billStr));
    }

    public static Long dateToTimestamp(LocalDate date, boolean useMax) {
        return date.atTime(useMax ? LocalTime.MAX : LocalTime.MIN).toInstant(ZoneOffset.of(ZONE_OF)).toEpochMilli();
    }

    public static LocalDateTime toDateTime(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
    }

    public static Date stringToDate(String dateStr) {
        if (StringUtils.isEmpty(dateStr))
            return null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = sdf.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    public static String dateToEsString(Date date) {
        if (StringUtils.isEmpty(date))
            return null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        return sdf.format(date);
    }

    public static Date stringToEsDate(String dateStr) {
        if (StringUtils.isEmpty(dateStr))
            return null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        Date date = null;
        try {
            date = sdf.parse(dateStr);
            return date;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    public static String stringToDateToString(String dateStr) {
        if (StringUtils.isEmpty(dateStr))
            return null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = stringToEsDate(dateStr);
        return sdf.format(date);

    }

    public static List<LocalDate> convertToDateList(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dateList = new ArrayList<>();
        while (!startDate.isAfter(endDate)) {
            dateList.add(startDate);
            startDate = startDate.plusDays(1);
        }
        Collections.reverse(dateList);
        return dateList;
    }

}
