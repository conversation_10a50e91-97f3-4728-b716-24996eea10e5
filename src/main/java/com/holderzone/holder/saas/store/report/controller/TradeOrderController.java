package com.holderzone.holder.saas.store.report.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Assert;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.report.constant.ErrorConstant;
import com.holderzone.holder.saas.store.report.helper.ExceptionHelper;
import com.holderzone.holder.saas.store.report.service.TradeOrderService;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.OrderItemTypeRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 订单相关
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/trade/order")
public class TradeOrderController {

    private final TradeOrderService tradeOrderService;

    /**
     * 订单商品类型
     */
    @PostMapping("/item/types")
    public List<OrderItemTypeRespDTO> queryItemTypes(@RequestBody ReportQueryVO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeOrderService.queryItemTypes(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "订单商品类型", JacksonUtils.writeValueAsString(query)));
        }
    }

    /**
     * 订单商品分类
     */
    @PostMapping("/item/categories")
    public List<String> queryItemCategories(@RequestBody ReportQueryVO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeOrderService.queryItemCategories(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "订单商品分类", JacksonUtils.writeValueAsString(query)));
        }
    }
}
