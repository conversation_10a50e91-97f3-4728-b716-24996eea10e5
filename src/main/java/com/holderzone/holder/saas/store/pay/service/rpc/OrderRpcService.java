package com.holderzone.holder.saas.store.pay.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @description 订单服务远程调用
 * @date 2021/11/26 15:05
 * @className: OrderRpcService
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = OrderRpcService.FastFoodFallBack.class)
public interface OrderRpcService {

    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @GetMapping("/order_detail/find_by_order_guid")
    OrderDTO findByOrderGuid(@RequestParam("orderGuid") String orderGuid);

    @Component
    @Slf4j
    class FastFoodFallBack implements FallbackFactory<OrderRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OrderRpcService create(Throwable throwable) {
            return new OrderRpcService() {

                @Override
                public OrderDTO findByOrderGuid(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "findByOrderGuid", orderGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }
}
