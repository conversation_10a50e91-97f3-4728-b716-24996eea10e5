package com.holderzone.holder.saas.store.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-07-03
 * @description 退菜明细导出
 */
@Data
public class ReturnDetailItemExcelDTO implements Serializable {


    /**
     * 门店名称
     */
    @Excel(name = "门店名称", orderNum = "1", width = 15)
    public String storeName;

    /**
     * 订单编号
     */
    @Excel(name = "订单编号", orderNum = "2", width = 25)
    public String orderNo;

    /**
     * 就餐类型
     */
    @Excel(name = "就餐类型", orderNum = "3", width = 10)
    public String cateringType;

    /**
     * 菜品SKU码
     */
    @Excel(name = "菜品SKU码", orderNum = "4", width = 25)
    public String skuGuid;

    /**
     * 商品名称
     */
    @Excel(name = "商品名称", orderNum = "5", width = 15)
    public String goodsName;

    /**
     * 商品分类
     */
    @Excel(name = "商品分类", orderNum = "6", width = 15)
    public String goodsCategories;

    /**
     * 退菜数量
     */
    @Excel(name = "退菜数量", orderNum = "7", width = 15)
    public BigDecimal returnQuantity;

    /**
     * 退菜金额
     */
    @Excel(name = "退菜金额", orderNum = "8", width = 15)
    public BigDecimal refund;

    /**
     * 退菜时间
     */
    @Excel(name = "退菜时间", orderNum = "9", width = 25)
    public String returnTime;

    /**
     * 下单时间
     */
    @Excel(name = "下单时间", orderNum = "10", width = 25)
    public String orderTime;

    /**
     * 退菜原因
     */
    @Excel(name = "退菜原因", orderNum = "11", width = 15)
    public String reason;

    /**
     * 退菜操作人
     */
    @Excel(name = "退菜操作员", orderNum = "12", width = 15)
    public String staff;

    /**
     * 操作节点
     */
    @Excel(name = "操作节点", orderNum = "13", width = 15)
    public String operatorNode;

    /**
     * 桌台
     */
    @Excel(name = "桌台", orderNum = "14", width = 15)
    public String table;

    /**
     * 售卖单价
     */
    @Excel(name = "售卖单价", orderNum = "15", width = 15)
    public BigDecimal salePrice;

    /**
     * 实退金额
     */
    @Excel(name = "实退金额", orderNum = "16", width = 15)
    public BigDecimal actuallyRefundFee;

    /**
     * 授权人员
     */
    @Excel(name = "授权人员", orderNum = "17", width = 15)
    public String authorityUser;
}
