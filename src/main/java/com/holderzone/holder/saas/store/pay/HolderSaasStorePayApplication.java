package com.holderzone.holder.saas.store.pay;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableEurekaClient
@EnableFeignClients
@SpringBootApplication
@EnableApolloConfig
//@EnableSwagger2
public class HolderSaasStorePayApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasStorePayApplication.class, args);
    }

}
