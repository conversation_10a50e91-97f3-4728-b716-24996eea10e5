package com.holderzone.holder.saas.store.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/3/7
 * @description 销售明细导出
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "销售明细导出", description = "销售明细导出")
public class SalesDetailExcelDTO implements Serializable {

    private static final long serialVersionUID = -4921080092687085074L;

    @Excel(name = "序号", orderNum = "1", width = 15)
    private String serialNumber;

    @Excel(name = "商品名称", orderNum = "2", width = 15)
    private String itemName;

    @Excel(name = "商品分类", orderNum = "3", width = 15)
    private String itemTypeName;

    @Excel(name = "商品类型", orderNum = "4", width = 15)
    private String itemType;

    @Excel(name = "操作", orderNum = "5", width = 15)
    private String operation;

    @Excel(name = "点餐数量", orderNum = "6", width = 15)
    private Double orderNumber;

    @Excel(name = "记数单位", orderNum = "7", width = 15)
    private String unit;

    @Excel(name = "金额", orderNum = "8", width = 15)
    private String price;

    @Excel(name = "属性加价", orderNum = "9", width = 15)
    private String attrPrice;

    @Excel(name = "订单号", orderNum = "10", width = 15)
    private String orderNo;

    @Excel(name = "下单时间", orderNum = "11", width = 15)
    private String createTime;

    @Excel(name = "结账时间", orderNum = "12", width = 15)
    private String checkoutTime;

}
