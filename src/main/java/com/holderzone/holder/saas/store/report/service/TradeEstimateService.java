package com.holderzone.holder.saas.store.report.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.report.query.EstimateReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.EstimateReportRespDTO;

/**
 * 估清报表服务
 *
 * <AUTHOR>
 * @since 2025/7/16
 * @since 1.8
 */
public interface TradeEstimateService {

    /**
     * 估清报表分页查询
     *
     * @param query 查询参数
     * @return 估清报表结果
     */
    Page<EstimateReportRespDTO> page(EstimateReportQueryVO query);

    /**
     * 估清报表导出
     *
     * @param query 估清报表查询参数
     * @return 导出结果
     */
    String export(EstimateReportQueryVO query);
}
