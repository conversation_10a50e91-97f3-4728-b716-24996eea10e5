package com.holderzone.holder.saas.store.client.entity.ddo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@ApiModel("昨日订单数据")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CommodityAnalysisItemDO {

    public static CommodityAnalysisItemDO DEFAULT = new CommodityAnalysisItemDO( "", 0,BigDecimal.ZERO);
    @ApiModelProperty(value = "商品名称")
    private String goodsName;
    @ApiModelProperty(value = "销售量")
    private Integer salesVolume;
    @ApiModelProperty(value = "销售总额")
    private BigDecimal salesAmount;
    public static CommodityAnalysisItemDO INSTANCE() {
        return new CommodityAnalysisItemDO( "", 0,BigDecimal.ZERO);
    }
}
