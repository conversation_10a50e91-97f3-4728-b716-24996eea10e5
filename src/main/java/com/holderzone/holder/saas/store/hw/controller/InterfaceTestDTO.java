package com.holderzone.holder.saas.store.hw.controller;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ResultDTO
 * @date 2019/05/16 11:24
 * @description //TODO
 * @program holder-saas-store-hw
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "响应类")
public class InterfaceTestDTO<T> {

    @ApiModelProperty(value = "状态码：0成功，失败时返回对应的错误码，详细错误码待定义", required = true)
    private Integer returnCode;

    @ApiModelProperty(value = "错误描述：成功是为空", required = true)
    private String returnMsg;

    @ApiModelProperty(value = "业务实体：根据业务入参会不一样，后续会输出详细业务对应入参")
    private T params;

    public InterfaceTestDTO(Integer returnCode, String returnMsg) {
        this.returnCode = returnCode;
        this.returnMsg = returnMsg;
    }
}