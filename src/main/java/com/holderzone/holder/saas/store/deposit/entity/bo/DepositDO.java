package com.holderzone.holder.saas.store.deposit.entity.bo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import groovy.transform.Field;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 可根据寄存单号和客户联系方式进行搜索
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("hse_deposit_deposit")
public class DepositDO {

    private static final long serialVersionUID = 8424514095125620853L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 寄存单号
     */
    private String depositOrderId;

    /**
     * 存入总量
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private Integer depositNum;

    /**
     * 剩余数量
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private Integer residueNum;

    /**
     * 会员的guid (关联客户信息)
     */
    private String userGuid;

    /**
     * 客户头像
     */
    private String headPortrait;

    /**
     * 用户姓名
     */
    private String customerName;

    /**
     * 手机号码
     */
    private String phoneNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 过期
     */
    private String expireTime;

    /**
     * 短信发送状态，0：未发送，1，发送
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private Integer messageStatus;

    /**
     * 排序字段，后端排序使用
     */
    private LocalDateTime sorted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    private String createUser;
}
