package com.holderzone.holder.saas.store.deposit.mapstruct;

import com.holderzone.holder.saas.store.deposit.entity.bo.GoodsDO;
import com.holderzone.saas.store.dto.deposit.resp.GoodsRespDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring")
public interface GoodsMapstruct {

    GoodsDO fromGoodsDTO(GoodsRespDTO goodsRespDTO);

    List<GoodsRespDTO> fromGoodsList(List<GoodsDO> goodsDOList);
}
