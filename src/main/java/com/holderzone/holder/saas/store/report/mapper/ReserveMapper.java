package com.holderzone.holder.saas.store.report.mapper;

import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ReserveRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预定单明细
 */
@Mapper
public interface ReserveMapper {

    Long count(@Param("query") ReportQueryVO query);

    List<ReserveRespDTO> pageInfo(@Param("query") ReportQueryVO query);

    List<ReserveRespDTO> list(@Param("query") ReportQueryVO query);

}
