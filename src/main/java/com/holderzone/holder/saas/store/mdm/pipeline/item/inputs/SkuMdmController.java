package com.holderzone.holder.saas.store.mdm.pipeline.item.inputs;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MDMResult;
import com.holderzone.holder.saas.store.mdm.entity.MDMSynDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.SkuSyncDTO;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MdmItemController
 * @date 2019/11/23 下午7:43
 * @description //
 * @program holder-saas-store-mdm
 */

@Slf4j
@RestController
@RequestMapping("/sync_sku")
public class SkuMdmController {


    private final MqUtils mqUtils;

    public SkuMdmController(MqUtils mqUtils) {
        this.mqUtils = mqUtils;
    }

    @PostMapping("/create")
    @LogBefore(value = "外部系统批量创建规格", logLevel = LogLevel.INFO)
    public MDMResult<String> skuCreateBatchSyn(@Validated(SkuSyncDTO.Create.class)
                                               @RequestBody MDMSynDTO<List<SkuSyncDTO>> mdmSynDTO) {
        for (SkuSyncDTO skuSyncDTO : mdmSynDTO.getRequest()) {
            if(skuSyncDTO != null) {
                mqUtils.sendMessage(
                        RocketMqConfig.MainConfig.MAIN_MDM_SKU_TOPIC,
                        RocketMqConfig.MainConfig.MAIN_MDM_SKU_CREATE_TAG,
                        skuSyncDTO, UserContextUtils.getEnterpriseGuid()
                );
            }
        }
        return MDMResult.success();
    }

    @PostMapping("/update")
    @LogBefore(value = "外部系统批量更新规格", logLevel = LogLevel.INFO)
    public MDMResult<String> skuUpdateSyn(@Validated(SkuSyncDTO.Update.class)
                                          @RequestBody MDMSynDTO<List<SkuSyncDTO>> mdmSynDTO) {
        for (SkuSyncDTO skuSyncDTO : mdmSynDTO.getRequest()) {
            if (skuSyncDTO != null){
                mqUtils.sendMessage(
                        RocketMqConfig.MainConfig.MAIN_MDM_SKU_TOPIC,
                        RocketMqConfig.MainConfig.MAIN_MDM_SKU_UPDATE_TAG,
                        skuSyncDTO, UserContextUtils.getEnterpriseGuid()
                );
            }
        }

        return MDMResult.success();
    }

    @PostMapping("/delete")
    @LogBefore(value = "外部系统批量删除规格", logLevel = LogLevel.INFO)
    public MDMResult<String> skuDeleteSyn(@Validated(SkuSyncDTO.Delete.class)
                                          @RequestBody MDMSynDTO<List<SkuSyncDTO>> mdmSynDTO) {
        for (SkuSyncDTO skuSyncDTO : mdmSynDTO.getRequest()) {
            if (skuSyncDTO != null) {
                mqUtils.sendMessage(
                        RocketMqConfig.MainConfig.MAIN_MDM_SKU_TOPIC,
                        RocketMqConfig.MainConfig.MAIN_MDM_SKU_DELETE_TAG,
                        skuSyncDTO, UserContextUtils.getEnterpriseGuid()
                );
            }
        }

        return MDMResult.success();
    }

}
