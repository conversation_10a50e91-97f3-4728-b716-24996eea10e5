package com.holderzone.holder.saas.store.pay.service.rpc;

import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EnterpriseClient
 * @date 2019/03/14 11:33
 * @description 云端企业服务
 * @program holder-saas-store-trading-center
 */
@Component
@FeignClient(name = "holder-saas-cloud-enterprise", fallbackFactory = EnterpriseRpcService.EnterClientFallBack.class)
public interface EnterpriseRpcService {

    @GetMapping("enterprise/app/{enterpriseGuid}/{storeGuid}")
    PaymentInfoDTO getJHInfo(@PathVariable("enterpriseGuid") String enterpriseGuid, @PathVariable("storeGuid") String storeGuid);

    /**
     * 查询商户的所有账户信息
     *
     * @param enterpriseGuid 企业guid
     * @param storeGuid      门店guid
     * @return 商户的所有账户信息
     */
    @ApiOperation(value = "查询商户的所有账户信息")
    @GetMapping("enterprise/app/list_payment_info")
    List<PaymentInfoDTO> listPaymentInfo(@RequestParam("enterpriseGuid") String enterpriseGuid,
                                         @RequestParam("storeGuid") String storeGuid);

    /**
     * 根据支付信息guid查询聚合支付信息
     *
     * @param paymentInfoGuid 支付信息guid
     * @return 聚合支付信息
     */
    @ApiOperation(value = "根据支付信息guid查询聚合支付信息")
    @GetMapping("enterprise/app/find_pay_info_by_guid")
    PaymentInfoDTO findPayInfoByGuid(@RequestParam("paymentInfoGuid") String paymentInfoGuid);

    /**
     * 根据支付appid查询聚合支付信息
     *
     * @param paymentAppId 支付appid
     * @return 聚合支付信息列表
     */
    @ApiOperation(value = "根据支付appid查询聚合支付信息")
    @GetMapping("enterprise/app/list_pay_app_id")
    List<PaymentInfoDTO> listPayAppId(@RequestParam("paymentAppId") String paymentAppId,
                                      @RequestParam("enterpriseGuid") String enterpriseGuid,
                                      @RequestParam("storeGuid") String storeGuid);

    @Component
    class EnterClientFallBack implements FallbackFactory<EnterpriseRpcService> {

        private static final Logger logger = LoggerFactory.getLogger(EnterClientFallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public EnterpriseRpcService create(Throwable throwable) {

            return new EnterpriseRpcService() {

                @Override
                public PaymentInfoDTO getJHInfo(String enterpriseGuid, String storeGuid) {
                    logger.error("查询云端配置聚合支付appId and appSerectKey 失败 enterpriseGuid={} storeGuid={} e={}",
                            enterpriseGuid, storeGuid, throwable.getMessage());
                    return null;
                }

                @Override
                public List<PaymentInfoDTO> listPaymentInfo(String enterpriseGuid, String storeGuid) {
                    logger.error("查询商户的所有账户信息失败 enterpriseGuid={} storeGuid={} e={}", enterpriseGuid, storeGuid,
                            throwable.getMessage());
                    return new ArrayList<>();
                }

                @Override
                public PaymentInfoDTO findPayInfoByGuid(String paymentInfoGuid) {
                    logger.error(HYSTRIX_PATTERN, "findPayInfoByGuid", paymentInfoGuid, throwable.getMessage());
                    return null;
                }

                @Override
                public List<PaymentInfoDTO> listPayAppId(String paymentAppId, String enterpriseGuid, String storeGuid) {
                    logger.error("根据支付appid查询聚合支付信息失败 enterpriseGuid={} storeGuid={} e={}", enterpriseGuid, storeGuid,
                            throwable.getMessage());
                    return new ArrayList<>();
                }
            };
        }
    }
}
