package com.holderzone.holder.saas.store.report.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Assert;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.report.constant.ErrorConstant;
import com.holderzone.holder.saas.store.report.helper.ExceptionHelper;
import com.holderzone.holder.saas.store.report.service.TradeChangeService;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ChangeDetailDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 换菜明细报表
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/trade/change")
public class TradeChangeController {

    private final TradeChangeService tradeChangeService;

    /**
     * 换菜明细报表
     */
    @PostMapping("/list")
    public Message<ChangeDetailDTO> list(@RequestBody ReportQueryVO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeChangeService.list(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "套餐换菜明细列表", JacksonUtils.writeValueAsString(query)));
        }
    }


    @PostMapping("/export")
    public String export(@RequestBody ReportQueryVO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeChangeService.export(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "导出套餐换菜明细", JacksonUtils.writeValueAsString(query)));
        }
    }
}
