package com.holderzone.holder.saas.store.pay.service.rpc;

import com.holderzone.framework.base.dto.file.FileDto;
import com.netflix.hystrix.exception.HystrixBadRequestException;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FileUploadClient
 * @date 2018/09/03 11:27
 * @description
 * @program holder-saas-store-trading-center
 */
@Component
@FeignClient(name = "base-service", fallbackFactory = FileUploadRpcService.FileUploadFallBack.class)
public interface FileUploadRpcService {

    @PostMapping("/file")
    String upload(@RequestBody FileDto fileDto);

    @DeleteMapping("/file")
    void delete(@RequestParam("fileUrl") String fileUrl);

    @Component
    class FileUploadFallBack implements FallbackFactory<FileUploadRpcService> {

        private static final Logger logger = LoggerFactory.getLogger(FileUploadFallBack.class);

        @Override
        public FileUploadRpcService create(Throwable throwable) {

            return new FileUploadRpcService() {
                @Override
                public String upload(FileDto fileDto) {
                    logger.error("e={}", throwable.getMessage());
                    throw new HystrixBadRequestException("调用oss文件上传异常! " + throwable.getMessage());
                }

                @Override
                public void delete(String fileUrl) {
                    logger.error("e={}", throwable.getMessage());
                    throw new HystrixBadRequestException("调用oss文件删除异常! " + throwable.getMessage());
                }
            };
        }
    }
}
