package com.holderzone.holder.saas.store.pay.service.impl;

import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.pay.service.RedisService;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.AggRefundPollingRespDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RedisServiceImpl
 * @date 2019/03/19 18:18
 * @description
 * @program holder-saas-store-trading-center
 */
@Service
public class RedisServiceImpl implements RedisService {

    private static final String AGG_PAY_POLLING = "agg_pay_polling";

    //callBack和pulling都可以标记为正确的结果 防止重复回调  加上此参数
    private static final String AGG_PAY_CALLBACK = "agg_pay_callback";

    private static final String REGEX = ":";

    private static final String CODE_URL = "code_url";

    private static final String REFUND_RESP = "refund_resp";

    private static final int CODE_EXPIRE_TIME = 15;

    private static final int POLLING_RESP_EXPIRE_TIME = 10;

    private final ReactiveRedisTemplate<String, String> reactiveRedisTemplateString;

    private final ReactiveRedisTemplate<String, AggPayPollingRespDTO> reactiveRedisTemplateAggPayPolling;

    private final ReactiveRedisTemplate<String, AggRefundPollingRespDTO> reactiveRedisTemplateAggRefundPolling;


    @Autowired
    public RedisServiceImpl(ReactiveRedisTemplate<String, String> reactiveRedisTemplateString,
                            ReactiveRedisTemplate<String, AggPayPollingRespDTO> reactiveRedisTemplateAggPayPolling,
                            ReactiveRedisTemplate<String, AggRefundPollingRespDTO> reactiveRedisTemplateAggRefundPolling) {
        this.reactiveRedisTemplateString = reactiveRedisTemplateString;
        this.reactiveRedisTemplateAggPayPolling = reactiveRedisTemplateAggPayPolling;
        this.reactiveRedisTemplateAggRefundPolling = reactiveRedisTemplateAggRefundPolling;
    }

    @Override
    public void putPollingResp(String orderGuid, String payGuid, AggPayPollingRespDTO pollingRespDTO) {
        if(pollingRespDTO==null) {
            return;
        }
        String key = StringUtils.getStr(REGEX, AGG_PAY_POLLING, orderGuid, payGuid);
        Duration duration = Duration.ofSeconds(POLLING_RESP_EXPIRE_TIME);
        reactiveRedisTemplateAggPayPolling.opsForValue().set(key, pollingRespDTO, duration).subscribe();
    }

    @Override
    public Mono<AggPayPollingRespDTO> getPollingResp(SaasPollingDTO saasPollingDTO) {
        String key = StringUtils.getStr(REGEX, AGG_PAY_POLLING, saasPollingDTO.getOrderGuid(), saasPollingDTO.getPayGuid());
        return reactiveRedisTemplateAggPayPolling.opsForValue().get(key).publishOn(Schedulers.elastic());
    }

    @Override
    public void putCallBackResp(String orderGuid, String payGuid, String state) {
        String key = StringUtils.getStr(REGEX, AGG_PAY_CALLBACK, orderGuid, payGuid);
        Duration duration = Duration.ofSeconds(POLLING_RESP_EXPIRE_TIME);
        reactiveRedisTemplateString.opsForValue().set(key, state, duration).subscribe();
    }

    @Override
    public Mono<String> getCallBackResp(String orderGuid, String payGuid) {
        String key = StringUtils.getStr(REGEX, AGG_PAY_CALLBACK, orderGuid, payGuid);
        return reactiveRedisTemplateString.opsForValue().get(key).publishOn(Schedulers.elastic());
    }

    @Override
    public Mono<AggRefundPollingRespDTO> getRefundPollingResp(SaasPollingDTO saasPollingDTO) {
        String key = StringUtils.getStr(REGEX, REFUND_RESP, saasPollingDTO.getOrderGuid(), saasPollingDTO.getPayGuid());
        return reactiveRedisTemplateAggRefundPolling.opsForValue().get(key).publishOn(Schedulers.elastic());
    }

    @Override
    public String getQrCodeDownloadUrl(String orderGUID) {
        String key = StringUtils.getStr(REGEX, CODE_URL, orderGUID);
        return reactiveRedisTemplateString.opsForValue().get(key).block();
    }

    @Override
    public void putQrCodeDownloadUrl(String orderGUID, String downLoadUrl) {
        String key = StringUtils.getStr(REGEX, CODE_URL, orderGUID);
        reactiveRedisTemplateString.opsForValue().set(key, downLoadUrl, Duration.ofMinutes(CODE_EXPIRE_TIME)).subscribe();
    }

    @Override
    public void putRefundPollingResp(String orderGuid, String payGuid, AggRefundPollingRespDTO aggRefundPollingRespDTO) {
        String key = StringUtils.getStr(REGEX, REFUND_RESP, orderGuid, payGuid);
        Duration duration = Duration.ofSeconds(POLLING_RESP_EXPIRE_TIME);
        reactiveRedisTemplateAggRefundPolling.opsForValue().set(key, aggRefundPollingRespDTO, duration).subscribe();
    }
}
