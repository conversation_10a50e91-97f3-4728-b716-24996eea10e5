package com.holderzone.holder.saas.store.deposit;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableSwagger2
@EnableEurekaClient
@EnableFeignClients
@SpringBootApplication/*(exclude={DataSourceAutoConfiguration.class})*/
@MapperScan(basePackages = "com.holderzone.holder.saas.store.deposit.mapper")
@EnableScheduling
@EnableApolloConfig
public class HolderSaasStoreDepositApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasStoreDepositApplication.class, args);
    }

}
