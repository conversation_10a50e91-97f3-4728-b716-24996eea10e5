package com.holderzone.holder.saas.store.pay.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.AggRefundPollingRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.ReactiveKeyCommands;
import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;
import org.springframework.data.redis.connection.ReactiveStringCommands;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import javax.annotation.PreDestroy;

/**
 * 异步化的配置参见如下
 * https://github.com/eugenp/tutorials/blob/master/persistence-modules/spring-data-redis/src/main/java/com/baeldung/spring/data/reactive/redis/config/RedisConfig.java
 *
 * <AUTHOR>
 * @version 1.0
 * @className RedisConfig
 * @date 2019/03/19 17:42
 * @description
 * @program holder-saas-store-trading-center
 */
@Configuration
@EnableCaching
@SuppressWarnings("Duplicates")
public class RedisConfig extends CachingConfigurerSupport {

    private final RedisConnectionFactory factory;

    @Autowired
    public RedisConfig(RedisConnectionFactory factory) {
        this.factory = factory;
    }

    @Bean
    public ReactiveRedisTemplate<String, Object> reactiveRedisTemplate(ReactiveRedisConnectionFactory connectionFactory) {
        // ValueSerializer
        Jackson2JsonRedisSerializer<Object> valueSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        valueSerializer.setObjectMapper(objectMapper());
        // RedisSerializationContext
        RedisSerializationContext.RedisSerializationContextBuilder<String, Object> builder =
                RedisSerializationContext.newSerializationContext(new StringRedisSerializer());
        RedisSerializationContext<String, Object> serializationContext = builder.value(valueSerializer).build();
        return new ReactiveRedisTemplate<>(connectionFactory, serializationContext);
    }

    @Bean
    public ReactiveRedisTemplate<String, String> reactiveRedisTemplateString(ReactiveRedisConnectionFactory connectionFactory) {
        return new ReactiveRedisTemplate<>(connectionFactory, RedisSerializationContext.string());
    }

    @Bean
    public ReactiveRedisTemplate<String, AggPayPollingRespDTO> reactiveRedisTemplateAggPayPolling(ReactiveRedisConnectionFactory connectionFactory) {
        // ValueSerializer
        Jackson2JsonRedisSerializer<AggPayPollingRespDTO> valueSerializer = new Jackson2JsonRedisSerializer<>(AggPayPollingRespDTO.class);
        valueSerializer.setObjectMapper(objectMapper());
        // RedisSerializationContext
        RedisSerializationContext.RedisSerializationContextBuilder<String, AggPayPollingRespDTO> builder =
                RedisSerializationContext.newSerializationContext(new StringRedisSerializer());
        RedisSerializationContext<String, AggPayPollingRespDTO> serializationContext = builder.value(valueSerializer).build();
        return new ReactiveRedisTemplate<>(connectionFactory, serializationContext);
    }

    @Bean
    public ReactiveRedisTemplate<String, AggRefundPollingRespDTO> reactiveRedisTemplateAggRefundPolling(ReactiveRedisConnectionFactory connectionFactory) {
        // ValueSerializer
        Jackson2JsonRedisSerializer<AggRefundPollingRespDTO> valueSerializer = new Jackson2JsonRedisSerializer<>(AggRefundPollingRespDTO.class);
        valueSerializer.setObjectMapper(objectMapper());
        // RedisSerializationContext
        RedisSerializationContext.RedisSerializationContextBuilder<String, AggRefundPollingRespDTO> builder =
                RedisSerializationContext.newSerializationContext(new StringRedisSerializer());
        RedisSerializationContext<String, AggRefundPollingRespDTO> serializationContext = builder.value(valueSerializer).build();
        return new ReactiveRedisTemplate<>(connectionFactory, serializationContext);
    }


    @Bean
    public ReactiveKeyCommands keyCommands(final ReactiveRedisConnectionFactory reactiveRedisConnectionFactory) {
        return reactiveRedisConnectionFactory.getReactiveConnection().keyCommands();
    }

    @Bean
    public ReactiveStringCommands stringCommands(final ReactiveRedisConnectionFactory reactiveRedisConnectionFactory) {
        return reactiveRedisConnectionFactory.getReactiveConnection().stringCommands();
    }

    @PreDestroy
    public void cleanRedis() {
        factory.getConnection().flushDb();
    }

    private ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }
}
