package com.holderzone.holder.saas.store.mdm.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SyncConfig
 * @date 2019/12/09 16:50
 * @description
 * @program holder-saas-store
 */
@Slf4j
@Component
public class SyncConfig {

    @Value(value = "${mdm.secretKey:null}")
    private String secretKey;

    @Value(value = "${mdm.initAgainAllowed:false}")
    private Boolean initAgainAllowed;

    @Value(value = "${mdm.triggerAgainAllowed:false}")
    private Boolean triggerAgainAllowed;

    public boolean shouldTriggerAgain(String secretKey) {
        log.info("triggerAgainAllowed：{}", triggerAgainAllowed);
        if (!triggerAgainAllowed) {
            return false;
        }
        boolean keyMatched = secretKey.equalsIgnoreCase(this.secretKey);
        log.info("secretKey {}，input：{}，system：{}", keyMatched, secretKey, this.secretKey);
        return keyMatched;
    }

    public boolean shouldInitAgain(String sql) {
        log.info("initAgainAllowed：{}", initAgainAllowed);
        if (!initAgainAllowed) {
            return false;
        }
        Optional<String> sqlMatched = Stream.of(
                "add column `external_version`",
                "add column external_version",
                "change `external_version`",
                "change external_version",
                "change column `external_version`",
                "change column external_version",
                "modify `external_version`",
                "modify external_version",
                "modify column `external_version`",
                "modify column external_version"
        ).filter(s -> sql.toLowerCase().contains(s)).findFirst();
        log.info("sqlMatched：{}", sqlMatched.orElse(null));
        return sqlMatched.isPresent();
//        return initAgainAllowed && Stream.of(
//                "add column `external_version`",
//                "add column external_version",
//                "change `external_version`",
//                "change external_version",
//                "change column `external_version`",
//                "change column external_version",
//                "modify `external_version`",
//                "modify external_version",
//                "modify column `external_version`",
//                "modify column external_version"
//        ).anyMatch(s -> sql.toLowerCase().contains(s));
    }
}
