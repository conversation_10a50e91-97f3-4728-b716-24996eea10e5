package com.holderzone.holder.saas.store.mdm.service;

import com.holderzone.holder.saas.store.mdm.entity.MDMSynDTO;
import com.holderzone.holder.saas.store.mdm.entity.MdmRespResult;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MDMSynSevice
 * @date 2019/11/18 10:10
 * @description
 * @program holder-saas-store
 */
public interface MdmOperation {

    /**
     * http request
     *
     * @param reqType ReqTypeConstant
     * @param reqUrl  ReqUrlConstants
     * @param o       param
     */
    MdmRespResult<Object> doRequest(String reqType, String reqUrl, Object o);

    void checkSign(MDMSynDTO<?> mdmSynDTO);
}
