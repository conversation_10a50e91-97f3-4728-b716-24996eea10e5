package com.holderzone.holder.saas.store.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 团购订单结算明细
 */
@Data
public class GrouponTradeDetailExcelDTO implements Serializable {

    private static final long serialVersionUID = -1667368987764335434L;

    @Excel(name = "券来源", orderNum = "1", width = 15)
    private String couponSource;

    @Excel(name = "券号", orderNum = "2", width = 15)
    private String couponCode;

    /**
     * 券码使用时间
     */
    @Excel(name = "验券时间", orderNum = "3", width = 22)
    private String couponUseTime;

    @Excel(name = "门店名称", orderNum = "4", width = 15)
    private String storeName;

    @Excel(name = "美团门店名称", orderNum = "5", width = 15)
    private String thirdStoreName;

    @Excel(name = "美团门店ID", orderNum = "6", width = 15)
    private String thirdStoreGuid;

    @Excel(name = "项目名称", orderNum = "7", width = 18)
    private String dealTitle;

    @Excel(name = "项目ID", orderNum = "8", width = 15)
    private String dealId;

    /**
     * 是否量贩：0：不是，1：是
     */
    @Excel(name = "是否量贩", orderNum = "9", width = 15, replace = {"是_1", "否_0"})
    private Integer volume;

    /**
     * 量贩项目的单张券原价（普通券单张券原价与项目总价相同）
     */
    @Excel(name = "量贩项目的单张券原价", orderNum = "10", width = 20)
    private String singleValue;

    /**
     * 市场价(券面值)
     */
    @Excel(name = "券面值", orderNum = "11", width = 15)
    private BigDecimal dealValue;

    /**
     * 券售价金额
     */
    @Excel(name = "券金额", orderNum = "12", width = 15)
    private BigDecimal couponSellPrice;

    /**
     * 商家促销金额
     */
    @Excel(name = "商家促销金额", orderNum = "13", width = 15)
    private String bizCost;

    /**
     * 团购券码购买价格
     */
    @Excel(name = "用户购买价", orderNum = "14", width = 15)
    private BigDecimal couponBuyPrice;

    /**
     * 商家预计应得金额
     */
    @Excel(name = "商家预计应得金额", orderNum = "15", width = 16, isStatistics = true)
    private String due;

    /**
     * 业务系统订单单号
     */
    @Excel(name = "门店核销订单", orderNum = "16", width = 20)
    private String orderNo;
}
