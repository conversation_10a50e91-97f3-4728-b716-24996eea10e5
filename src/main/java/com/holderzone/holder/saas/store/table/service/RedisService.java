package com.holderzone.holder.saas.store.table.service;

import com.holderzone.holder.saas.store.table.domain.TableDO;
import com.holderzone.holder.saas.store.table.domain.bo.TableInfoBO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RedisService
 * @date 2018/12/27 11:12
 * @description redis Service
 * @program holder-saas-store-table
 */
public interface RedisService {

    String singleGuid(String tableName);

    List<String> batchGuid(int size, String tableName);

    void lockSingleTable(String tableGuid, String deviceId, String orderGuid, Integer deviceType);

    void lockMultiTable(List<TableInfoBO> tableInfoBOS, Integer deviceType);

    boolean isTableLockedByOthers(String deviceId, String tableGuid);

    boolean isTableLockedByOthers(String deviceId, List<String> tableGuidList);

    boolean isUnlockAllowed(String tableGuid, String deviceId, String orderGuid);

    void unlockMultiTable(List<String> tableGuidList);

    Integer getCombineTimes(String storeGuid);

    Integer getAssociatedTimes(String storeGuid);

    Integer rollBackAssociatedTimes(String storeGuid);

    @Deprecated
    void putTableDoList(List<TableDO> tableDOList, String storeGuid);

    TableInfoBO getTableLockStatus(String tableGuid);

    List<TableInfoBO> getTableLockStatus(List<String> tableGuidList);

}
