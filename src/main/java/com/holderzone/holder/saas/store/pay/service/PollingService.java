package com.holderzone.holder.saas.store.pay.service;

import com.holderzone.holder.saas.store.pay.entity.HandlerPayBO;
import com.holderzone.saas.store.dto.pay.AggPayPollingDTO;
import com.holderzone.saas.store.dto.pay.AggRefundPollingDTO;
import com.holderzone.saas.store.dto.pay.SaasAggRefundDTO;
import reactor.core.publisher.Mono;

public interface PollingService {

    void startPrePayPolling(AggPayPollingDTO aggPayPollingDTO, HandlerPayBO handlerPayBO);


    Mono<String> compareStatWithCache(String orderGuid, String payGuid, String paySt);

    void startRefundPolling(SaasAggRefundDTO saasAggRefundDTO, AggRefundPollingDTO aggRefundPollingDTO);

    boolean handlePollingResult(AggPayPollingDTO pollingJHPayDTO, HandlerPayBO handlerPayBO, boolean useMq);
}
