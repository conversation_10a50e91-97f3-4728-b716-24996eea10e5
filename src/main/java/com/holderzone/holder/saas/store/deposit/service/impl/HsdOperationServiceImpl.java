package com.holderzone.holder.saas.store.deposit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.deposit.entity.bo.OperationDO;
import com.holderzone.holder.saas.store.deposit.mapper.HsdOperationMapper;
import com.holderzone.holder.saas.store.deposit.mapstruct.OperationMapstruct;
import com.holderzone.holder.saas.store.deposit.service.DistributedIdService;
import com.holderzone.holder.saas.store.deposit.service.IHsdOperationGoodsService;
import com.holderzone.holder.saas.store.deposit.service.IHsdOperationService;
import com.holderzone.holder.saas.store.deposit.util.PageAdapter;
import com.holderzone.saas.store.dto.deposit.req.OperationCreateReqDTO;
import com.holderzone.saas.store.dto.deposit.req.OperationHistoryQueryReqDTO;
import com.holderzone.saas.store.dto.deposit.resp.GoodsSimpleRespDTO;
import com.holderzone.saas.store.dto.deposit.resp.OperationQueryRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-02
 */
@Slf4j
@Service
public class HsdOperationServiceImpl extends ServiceImpl<HsdOperationMapper, OperationDO> implements IHsdOperationService {

    private final IHsdOperationGoodsService iHsdOperationGoodsService;

    private final OperationMapstruct operationMapstruct;

    @Autowired
    public HsdOperationServiceImpl(IHsdOperationGoodsService iHsdOperationGoodsService,
                                   DistributedIdService distributedIdService,
                                   OperationMapstruct operationMapstruct) {
        this.operationMapstruct = operationMapstruct;
        this.iHsdOperationGoodsService = iHsdOperationGoodsService;
    }

    @Override
    public Boolean createOperationRecord(OperationCreateReqDTO operationCreateReqDTO, String operationGuid, String depositGuid) {

        OperationDO operationDO = operationMapstruct.fromOperationDTO(operationCreateReqDTO);
        operationDO.setDepositGuid(depositGuid);
        operationDO.setGuid(operationGuid);
        return save(operationDO);
    }

    @Override
    public Page<OperationQueryRespDTO> queryOperationHistory(OperationHistoryQueryReqDTO operationHistoryQueryReqDTO) {

        if (StringUtils.isEmpty(operationHistoryQueryReqDTO.getDepositGuid())) {
            throw new BusinessException("depositGuid 不得为空");
        }

        IPage<OperationDO> page = page(new PageAdapter<>(operationHistoryQueryReqDTO),
                new LambdaQueryWrapper<OperationDO>().eq(OperationDO::getDepositGuid, operationHistoryQueryReqDTO.getDepositGuid())
                        .orderByDesc(OperationDO::getGmtCreate)
        );

        return new PageAdapter<>(page, page.getRecords().stream()
                .map(operationDO -> {
                            OperationQueryRespDTO operationQueryRespDTO = new OperationQueryRespDTO();
                            operationQueryRespDTO.setOperator(operationDO.getOperator());
                            operationQueryRespDTO.setOperationTime(DateTimeUtils.localDateTime2String(operationDO.getGmtModified(), "yyyy-MM-dd HH:mm:ss"));
                            operationQueryRespDTO.setOperationWay(operationDO.getOperationWay());
                            operationQueryRespDTO.setRemark(operationDO.getRemark());

                            List<GoodsSimpleRespDTO> goodsSimpleRespDTOList = iHsdOperationGoodsService.queryGoodsOfOperation(operationDO.getGuid(), operationDO.getDepositGuid());
                            operationQueryRespDTO.setGoodsList(goodsSimpleRespDTOList);
                            return operationQueryRespDTO;
                        }
                ).collect(Collectors.toList()));
    }
}
