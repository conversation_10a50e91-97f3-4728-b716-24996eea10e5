package com.holderzone.holder.saas.store.table.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.table.domain.TableAssociatedDO;
import com.holderzone.holder.saas.store.table.mapper.TableAssociatedMapper;
import com.holderzone.holder.saas.store.table.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class TableAssociatedServiceImpl extends ServiceImpl<TableAssociatedMapper, TableAssociatedDO> implements TableAssociatedService {

    private final RedisService redisService;

    @Override
    public List<TableAssociatedDO> listByTableGuid(String tableGuid) {
        // 先查询子桌
        LambdaQueryWrapper<TableAssociatedDO> associatedTableQw = new LambdaQueryWrapper<TableAssociatedDO>()
                .eq(TableAssociatedDO::getAssociatedTableGuid, tableGuid);
        List<TableAssociatedDO> associatedTableList = list(associatedTableQw);
        if (CollectionUtils.isNotEmpty(associatedTableList)) {
            // tableGuid为子桌
            tableGuid = associatedTableList.get(0).getTableGuid();
        }
        // 查询主桌
        LambdaQueryWrapper<TableAssociatedDO> tableQw = new LambdaQueryWrapper<TableAssociatedDO>()
                .eq(TableAssociatedDO::getTableGuid, tableGuid);
        return list(tableQw);
    }

    @Override
    public boolean isExist(List<String> tableGuids) {
        LambdaQueryWrapper<TableAssociatedDO> qw = new LambdaQueryWrapper<TableAssociatedDO>()
                .in(TableAssociatedDO::getTableGuid, tableGuids)
                .or()
                .in(TableAssociatedDO::getAssociatedTableGuid, tableGuids);
        return CollectionUtils.isNotEmpty(list(qw));
    }

    @Override
    public void remove(String tableGuid) {
        LambdaUpdateWrapper<TableAssociatedDO> uw = new LambdaUpdateWrapper<TableAssociatedDO>()
                .eq(TableAssociatedDO::getTableGuid, tableGuid)
                .or()
                .eq(TableAssociatedDO::getAssociatedTableGuid, tableGuid);
        remove(uw);
    }

    @Override
    public void removeBatch(List<String> tableGuids) {
        LambdaUpdateWrapper<TableAssociatedDO> uw = new LambdaUpdateWrapper<TableAssociatedDO>()
                .in(TableAssociatedDO::getTableGuid, tableGuids)
                .or()
                .in(TableAssociatedDO::getAssociatedTableGuid, tableGuids);
        remove(uw);
    }

    @Override
    public void saveBatch(String tableGuid, List<String> associatedTableGuids) {
        List<String> copyAssociatedTableGuids = new ArrayList<>(associatedTableGuids);
        copyAssociatedTableGuids.remove(tableGuid);
        if (StringUtils.isEmpty(tableGuid) || CollectionUtils.isEmpty(copyAssociatedTableGuids)) {
            return;
        }
        List<TableAssociatedDO> tableAssociatedList = build(tableGuid, copyAssociatedTableGuids);
        this.saveBatch(tableAssociatedList);
    }


    private List<TableAssociatedDO> build(String tableGuid, List<String> associatedTableGuids) {
        List<String> guids = redisService.batchGuid(associatedTableGuids.size(), TableAssociatedDO.class.getSimpleName());
        String userGuid = UserContextUtils.getUserGuid();
        String userName = UserContextUtils.getUserName();
        return associatedTableGuids.stream().map(e -> {
            TableAssociatedDO tableAssociatedDO = new TableAssociatedDO();
            tableAssociatedDO.setGuid(guids.remove(0));
            tableAssociatedDO.setTableGuid(tableGuid);
            tableAssociatedDO.setAssociatedTableGuid(e);
            tableAssociatedDO.setOpenStaffGuid(userGuid);
            tableAssociatedDO.setOpenStaffName(userName);
            return tableAssociatedDO;
        }).collect(Collectors.toList());
    }
}
