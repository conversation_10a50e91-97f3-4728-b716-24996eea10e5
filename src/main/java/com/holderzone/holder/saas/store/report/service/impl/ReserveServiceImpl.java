package com.holderzone.holder.saas.store.report.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.report.constant.ReserveRecordStateEnum;
import com.holderzone.holder.saas.store.report.dto.ReserveExcelDTO;
import com.holderzone.holder.saas.store.report.mapper.ReserveMapper;
import com.holderzone.holder.saas.store.report.service.ReserveService;
import com.holderzone.holder.saas.store.report.util.ExcelUtils;
import com.holderzone.saas.store.dto.report.query.ReserveReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ReserveRespDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.reserve.ReserveDeviceTypeEnum;
import joptsimple.internal.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReserveServiceImpl implements ReserveService {

    private static final String YYYY_MM_DD = "yyyy-MM-dd";

    private static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

    private final ReserveMapper reserveMapper;

    private final OssClient ossClient;

    @Override
    public Page<ReserveRespDTO> page(ReserveReportQueryVO query) {
        // 查询前置处理
        queryBeforeHandler(query);
        Long total = reserveMapper.count(query);
        if (total == 0) {
            return new Page<>(query.getCurrentPage(), query.getPageSize(), 0, null);
        }
        List<ReserveRespDTO> detailResp = reserveMapper.pageInfo(query);
        // 预定区域
        detailResp.forEach(e -> {
            e.setAreaName(buildAreaName(e));
            e.setStateName(ReserveRecordStateEnum.getClientStateByCode(Integer.parseInt(e.getState()), e.getIsDelay()));
        });
        return new Page<>(query.getCurrentPage(), query.getPageSize(), total, detailResp);
    }

    @Override
    public String export(ReserveReportQueryVO query) {
        // 查询前置处理
        queryBeforeHandler(query);
        Long total = reserveMapper.count(query);
        if (total > 20000) {
            throw new BusinessException("最多只支持2万条数据导出");
        }
        List<ReserveRespDTO> detailResp = reserveMapper.list(query);
        List<ReserveExcelDTO> excelDTOList = buildReserveExcelDTOList(detailResp);
        try {
            String sheetName = "预定明细";
            String fileName = sheetName + LocalDateTime.now().format(DateTimeFormatter.ofPattern(YYYY_MM_DD));
            return ExcelUtils.exportExcel(excelDTOList, null, sheetName, ReserveExcelDTO.class, fileName, ossClient);
        } catch (Exception e) {
            throw new BusinessException("导出失败");
        }
    }


    /**
     * 查询前置处理
     */
    private void queryBeforeHandler(ReserveReportQueryVO query) {
        List<Integer> reserveStates = query.getReserveStates();
        if (CollectionUtils.isEmpty(reserveStates)) {
            return;
        }
        if (reserveStates.contains(ReserveRecordStateEnum.PASS.getCode())) {
            query.setIsDelay(false);
        }
        if (reserveStates.contains(ReserveRecordStateEnum.DELAY.getCode())) {
            reserveStates.add(ReserveRecordStateEnum.PASS.getCode());
            query.setIsDelay(true);
        }
    }

    /**
     * 构建导出excel
     */
    private List<ReserveExcelDTO> buildReserveExcelDTOList(List<ReserveRespDTO> detailResp) {
        List<ReserveExcelDTO> reserveExcelList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(detailResp)) {
            return Lists.newArrayList();
        }
        for (ReserveRespDTO reserveRespDTO : detailResp) {
            ReserveExcelDTO reserveExcelDTO = new ReserveExcelDTO();
            BeanUtils.copyProperties(reserveRespDTO, reserveExcelDTO);
            reserveExcelDTO.setReserveStartTime(reserveRespDTO.getReserveStartTime().format(DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM)));
            reserveExcelDTO.setGmtCreate(reserveRespDTO.getGmtCreate().format(DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM)));
            reserveExcelDTO.setReserveAmount(Objects.nonNull(reserveRespDTO.getReserveAmount()) ?
                    reserveRespDTO.getReserveAmount().stripTrailingZeros().toPlainString() : null);
            reserveExcelDTO.setRefundAmount(Objects.nonNull(reserveRespDTO.getRefundAmount()) ?
                    reserveRespDTO.getRefundAmount().stripTrailingZeros().toPlainString() : null);
            String gender = reserveRespDTO.getGender() == 1 ? "先生" : "女士";
            reserveExcelDTO.setName(reserveRespDTO.getName() + " (" + gender + ")");
            reserveExcelDTO.setNumberStr(reserveRespDTO.getNumber() + "人");
            reserveExcelDTO.setStateName(ReserveRecordStateEnum.getClientStateByCode(Integer.parseInt(reserveRespDTO.getState()), reserveRespDTO.getIsDelay()));
            reserveExcelDTO.setDeviceTypeName(ReserveDeviceTypeEnum.getReserveDeviceTypeNameByCode(reserveRespDTO.getDeviceType()));
            if (Objects.equals(PaymentTypeEnum.AGG.getCode(), reserveRespDTO.getPaymentType())) {
                reserveExcelDTO.setPaymentTypeName("微信支付");
            }
            buildDefaultValue(reserveExcelDTO, reserveRespDTO);
            reserveExcelList.add(reserveExcelDTO);
        }
        return reserveExcelList;
    }

    /**
     * 构建预定区域
     */
    private String buildAreaName(ReserveRespDTO reserveRespDTO) {
        String areaName = reserveRespDTO.getAreaName();
        String tables = reserveRespDTO.getTables();
        if (StringUtils.isNotEmpty(tables) && !"-".equals(tables)) {
            return tables;
        }
        if (StringUtils.isNotEmpty(areaName)) {
            return areaName;
        }
        return Strings.EMPTY;
    }

    private void buildDefaultValue(ReserveExcelDTO reserveExcelDTO, ReserveRespDTO reserveRespDTO) {
        // 预定区域
        String areaName = buildAreaName(reserveRespDTO);
        reserveExcelDTO.setAreaName(StringUtils.isEmpty(areaName) ? "-" : areaName);
        reserveExcelDTO.setRemark(StringUtils.isEmpty(reserveRespDTO.getRemark()) ? "-" : reserveExcelDTO.getRemark());
        reserveExcelDTO.setName(StringUtils.isEmpty(reserveRespDTO.getName()) ? "-" : reserveExcelDTO.getName());
        reserveExcelDTO.setPaymentTypeName(StringUtils.isEmpty(reserveRespDTO.getPaymentTypeName()) ? "-" : reserveExcelDTO.getPaymentTypeName());
    }
}
