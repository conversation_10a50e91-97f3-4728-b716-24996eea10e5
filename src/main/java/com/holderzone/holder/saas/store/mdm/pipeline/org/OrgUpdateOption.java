package com.holderzone.holder.saas.store.mdm.pipeline.org;

import com.holderzone.framework.canal.starter.core.CanalMsg;
import com.holderzone.framework.canal.starter.extension.RowChangeBody;
import com.holderzone.framework.canal.starter.extension.RowDataBody;
import com.holderzone.framework.canal.starter.option.anno.dml.UpdateListenOption;
import com.holderzone.framework.canal.starter.registry.DbBodyOption;
import com.holderzone.framework.util.JacksonUtils;

@Deprecated
@UpdateListenOption(
        schema = "hss_staff_*_db",
        table = "hss_user",
        monitored = {
                "name",
                "external_version"
        },
        ignored = "is_deleted"
)
public class OrgUpdateOption implements DbBodyOption {

    @Override
    public void doOption(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        System.out.println("======================注解方式（更新数据操作）==========================");
        for (RowDataBody rowDataBody : rowChangeBody.getRowDataBodyList()) {
            System.out.println(JacksonUtils.writeValueAsString(rowDataBody.getAfterData()));
            System.out.println("\n======================================================");
        }
        System.out.println("\n======================================================");
    }
}
