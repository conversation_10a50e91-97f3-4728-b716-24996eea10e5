package com.holderzone.holder.saas.store.table.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.holder.saas.store.table.client.PrintRpcClient;
import com.holderzone.holder.saas.store.table.service.PrintService;
import com.holderzone.saas.store.dto.print.content.PrintTurnTableDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.holderzone.holder.saas.store.table.utils.TableMapStruct.TABLE_MAP_STRUCT;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintServiceImpl
 * @date 2019/02/15 16:59
 * @description
 * @program holder-saas-store-table
 */
@Slf4j
@Service
public class PrintServiceImpl implements PrintService {

    private final PrintRpcClient printRpcClient;

    private static final ExecutorService executorService =
            Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() + 1);

    @Autowired
    public PrintServiceImpl(PrintRpcClient printRpcClient) {
        this.printRpcClient = printRpcClient;
    }

    @Override
    public void printTurnTable(TurnTableDTO turnTableDTO) {
        executorService.submit(() -> {
            try {
                PrintTurnTableDTO printTurnTableDTO = TABLE_MAP_STRUCT.turnTableDto2PrintTurnDto(turnTableDTO);
                String enterpriseGuid = turnTableDTO.getEnterpriseGuid();
                UserContextUtils.putErp(enterpriseGuid);
                printTurnTableDTO.setInvoiceType(InvoiceTypeEnum.TURN_TABLE.getType());
                printTurnTableDTO.setPrintSourceEnum(PrintSourceEnum.getPrintSourceByDeviceType(turnTableDTO.getDeviceType()));
                long millis = DateTimeUtils.nowMillis();
                printTurnTableDTO.setTurnTime(millis);
                printTurnTableDTO.setCreateTime(millis);
                printTurnTableDTO.setSrcTableName(turnTableDTO.getOriginTableAreaName() + "-" + turnTableDTO.getOriginTableCode());
                printTurnTableDTO.setDestTableName(turnTableDTO.getNewTableAreaName() + "-" + turnTableDTO.getNewTableCode());
                String frontResult = printRpcClient.print(printTurnTableDTO);
                printTurnTableDTO.setInvoiceType(InvoiceTypeEnum.TURN_TABLE_ITEM.getType());
                String result = printRpcClient.print(printTurnTableDTO);
                log.info("打印转台结果 frontResult={},result={}", frontResult, result);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                UserContextUtils.remove();
            }
        });
    }
}
