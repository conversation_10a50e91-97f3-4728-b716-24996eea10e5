package com.holderzone.holder.saas.store.report.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.report.config.OldCouponCalculateConfig;
import com.holderzone.holder.saas.store.report.constant.CommonConstant;
import com.holderzone.holder.saas.store.report.dto.GroupItemTypeExcelDTO;
import com.holderzone.holder.saas.store.report.dto.ItemTypeExcelDTO;
import com.holderzone.holder.saas.store.report.dto.SalesDetailExcelDTO;
import com.holderzone.holder.saas.store.report.mapper.TradeItemMapper;
import com.holderzone.holder.saas.store.report.mapper.TradeOrderMapper;
import com.holderzone.holder.saas.store.report.service.TradeItemService;
import com.holderzone.holder.saas.store.report.util.ExcelUtils;
import com.holderzone.saas.store.dto.journaling.req.SalesVolumeReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeStoreRespDTO;
import com.holderzone.saas.store.dto.report.query.GoodsSalesVO;
import com.holderzone.saas.store.dto.report.query.SalesDetailQO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesDTO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesStatisticDTO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesTotalDTO;
import com.holderzone.saas.store.dto.report.resp.SalesDetailRespDTO;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年08月07日 16:15
 * @description
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TradeItemServiceImpl implements TradeItemService {

    private final TradeItemMapper tradeItemMapper;

    private final TradeOrderMapper tradeOrderMapper;

    private final OssClient ossClient;

    private final Executor reportQueryExecutor;

    private final OldCouponCalculateConfig oldCouponCalculateConfig;

    @Override
    public GoodsSalesStatisticDTO queryItemTypeStatistics(GoodsSalesVO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        // 查询总条数
        CompletableFuture<Long> totalCountFuture = CompletableFuture.supplyAsync(() -> {
            PageMethod.startPage(query.getCurrentPage(), query.getPageSize());
            return tradeItemMapper.queryItemTypeStatisticsCount(query);
        }, reportQueryExecutor);

        CompletableFuture<com.github.pagehelper.Page<GoodsSalesDTO>> goodsCategoriesSalesListFuture = CompletableFuture.supplyAsync(() -> {
            PageMethod.startPage(query.getCurrentPage(), query.getPageSize());
            return tradeItemMapper.queryItemTypeStatistics(query);
        }, reportQueryExecutor);

        CompletableFuture<GoodsSalesTotalDTO> goodsSalesTotalFuture = CompletableFuture.supplyAsync(() -> {
            PageMethod.startPage(query.getCurrentPage(), query.getPageSize());
            return tradeItemMapper.queryItemTypeSalesTotal(query);
        }, reportQueryExecutor);

        CompletableFuture<Void> all = CompletableFuture.allOf(totalCountFuture, goodsCategoriesSalesListFuture, goodsSalesTotalFuture);
        try {
            all.join();
            Long totalCount = totalCountFuture.get();
            com.github.pagehelper.Page<GoodsSalesDTO> goodsCategoriesSalesList = goodsCategoriesSalesListFuture.get();
            GoodsSalesTotalDTO goodsSalesTotal = goodsSalesTotalFuture.get();
            goodsCategoriesSalesList.setTotal(totalCount);
            return new GoodsSalesStatisticDTO(new PageInfo<>(goodsCategoriesSalesList), goodsSalesTotal);
        } catch (Exception e) {
            log.error("查询异常", e);
            throw new BusinessException("查询超时，稍后再试");
        }
    }

    @Override
    public GoodsSalesStatisticDTO queryGroupItemSaleStatistics(GoodsSalesVO query) {

        SalesVolumeReqDTO reqDTO = new SalesVolumeReqDTO();
        reqDTO.setItemName(query.getGoodsNames());
        reqDTO.setCateringType(query.getCateringType());
        reqDTO.setStartDate(query.getStartTime());
        reqDTO.setEndDate(query.getEndTime());
        reqDTO.setStoreGuids(query.getStoreGuid());
        reqDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        reqDTO.setCurrentPage(query.getCurrentPage());
        reqDTO.setPageSize(query.getPageSize());

        Long orderCount = tradeOrderMapper.queryOrderCount(reqDTO);
        log.info("[queryGroupItemSaleStatistics]总订单数：{}", orderCount);
        if (orderCount == 0) {
            GoodsSalesTotalDTO goodsSalesTotalDTO = new GoodsSalesTotalDTO();
            goodsSalesTotalDTO.setTotalReceivedSumPrice(BigDecimal.ZERO);
            goodsSalesTotalDTO.setTotalSalesVolume(BigDecimal.ZERO);
            return new GoodsSalesStatisticDTO(new PageInfo<>(new ArrayList<>()), goodsSalesTotalDTO);
        }
        query.setTotalOrderCount(orderCount);
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());

        CompletableFuture<Long> totalCountFuture = CompletableFuture.supplyAsync(() -> {
            PageMethod.startPage(query.getCurrentPage(), query.getPageSize());
            return tradeItemMapper.queryGroupItemSaleStatisticsCount(query);
        }, reportQueryExecutor);

        CompletableFuture<com.github.pagehelper.Page<GoodsSalesDTO>> groupItemSaleListFuture = CompletableFuture.supplyAsync(() -> {
            PageMethod.startPage(query.getCurrentPage(), query.getPageSize());
            return tradeItemMapper.queryGroupItemSaleStatistics(query);
        }, reportQueryExecutor);

        CompletableFuture<GoodsSalesTotalDTO> goodsSalesTotalFuture = CompletableFuture.supplyAsync(() -> {
            PageMethod.startPage(query.getCurrentPage(), query.getPageSize());
            return tradeItemMapper.getGroupItemSaleTotal(query);
        }, reportQueryExecutor);

        CompletableFuture<Void> all = CompletableFuture.allOf(totalCountFuture, groupItemSaleListFuture, goodsSalesTotalFuture);
        try {
            all.join();
            Long totalCount = totalCountFuture.get();
            com.github.pagehelper.Page<GoodsSalesDTO> groupItemSaleList = groupItemSaleListFuture.get();
            GoodsSalesTotalDTO goodsSalesTotal = goodsSalesTotalFuture.get();
            groupItemSaleList.setTotal(totalCount);
            return new GoodsSalesStatisticDTO(new PageInfo<>(groupItemSaleList), goodsSalesTotal);
        } catch (Exception e) {
            throw new BusinessException("查询超时，稍后再试");
        }
    }

    @Override
    public Page<SalesVolumeRespDTO> pageStoreSaleStatistics(SalesVolumeReqDTO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        query.setStartDate(query.getBusinessStartDateTime().toLocalDate());
        query.setEndDate(query.getBusinessEndDateTime().toLocalDate());
        Long total = tradeItemMapper.countStoreSaleStatistics(query);
        if (total == 0) {
            log.warn("总数为0");
            return new Page<>(query.getCurrentPage(), query.getPageSize(), 0, Lists.newArrayList());
        }
        // 查询堂食订单数
        Long orderCount = tradeOrderMapper.queryDineInOrderCount(query);
        log.info("[pageStoreSaleStatistics]总订单数：{}", orderCount);
        if (Objects.isNull(orderCount) || orderCount <= 0) {
            return new Page<>(query.getCurrentPage(), query.getPageSize(), 0, Lists.newArrayList());
        }
        query.setTotalOrderCount(orderCount);
        List<SalesVolumeRespDTO> detailResp = listStoreSaleStatistics(query);
        oldSalesVolumeRespDTOAmountHandler(query, detailResp);
        return new Page<>(query.getCurrentPage(), query.getPageSize(), total, detailResp);
    }

    @Override
    public Page<SalesVolumeStoreRespDTO> pageGroupByStoreSaleStatistics(SalesVolumeReqDTO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        query.setStartDate(query.getBusinessStartDateTime().toLocalDate());
        query.setEndDate(query.getBusinessEndDateTime().toLocalDate());
        Long total = tradeItemMapper.countStoreSaleStatistics(query);
        if (total == 0) {
            log.warn("总数为0");
            return new Page<>(query.getCurrentPage(), query.getPageSize(), 0, Lists.newArrayList());
        }
        // 查询堂食订单数
        List<SalesVolumeRespDTO> salesVolumeRespList = tradeOrderMapper.queryGroupByStoreDineInOrderCount(query);
        log.info("[pageStoreSaleStatistics]总订单数：{}", JacksonUtils.writeValueAsString(salesVolumeRespList));
        if (CollectionUtils.isEmpty(salesVolumeRespList)) {
            return new Page<>(query.getCurrentPage(), query.getPageSize(), 0, Lists.newArrayList());
        }
        List<SalesVolumeRespDTO> detailResp = listStoreSaleStatistics(query);
        // 根据门店合并
        Map<String, Double> salesVolumeOrderCountMap = salesVolumeRespList.stream()
                .collect(Collectors.toMap(SalesVolumeRespDTO::getStoreGuid, SalesVolumeRespDTO::getOrderCount, (key1, key2) -> key1));
        Map<String, List<SalesVolumeRespDTO>> detailMap = detailResp.stream().collect(Collectors.groupingBy(SalesVolumeRespDTO::getStoreGuid));
        List<String> sortedStoreGuids = detailResp.stream().map(SalesVolumeRespDTO::getStoreGuid).distinct().collect(Collectors.toList());
        List<SalesVolumeStoreRespDTO> groupByStoreResp = Lists.newArrayList();
        for (String storeGuid : sortedStoreGuids) {
            List<SalesVolumeRespDTO> salesVolumeRespDTOList = detailMap.get(storeGuid);
            if (CollectionUtils.isEmpty(salesVolumeRespDTOList)) {
                continue;
            }
            SalesVolumeStoreRespDTO salesVolumeStoreRespDTO = new SalesVolumeStoreRespDTO();
            SalesVolumeRespDTO salesVolumeRespDTO = salesVolumeRespDTOList.get(0);
            salesVolumeStoreRespDTO.setStoreGuid(storeGuid);
            salesVolumeStoreRespDTO.setStoreName(salesVolumeRespDTO.getStoreName());
            salesVolumeStoreRespDTO.setBrandGuid(salesVolumeRespDTO.getBrandGuid());
            salesVolumeStoreRespDTO.setBrandName(salesVolumeRespDTO.getBrandName());
            // 销售额占比、点单率、堂食收入占比重新计算
            salesVolumeRespDTOAmountHandler(storeGuid, salesVolumeRespDTOList, salesVolumeOrderCountMap);
            oldSalesVolumeRespDTOAmountHandler(query, salesVolumeRespDTOList);
            salesVolumeStoreRespDTO.setItems(salesVolumeRespDTOList);
            groupByStoreResp.add(salesVolumeStoreRespDTO);
        }
        return new Page<>(query.getCurrentPage(), query.getPageSize(), total, groupByStoreResp);
    }

    private void salesVolumeRespDTOAmountHandler(String storeGuid,
                                                 List<SalesVolumeRespDTO> salesVolumeRespDTOList,
                                                 Map<String, Double> salesVolumeOrderCountMap) {
        // 销售额占比、点单率、堂食收入占比重新计算
        BigDecimal totalSalesAmount = salesVolumeRespDTOList.stream()
                .map(SalesVolumeRespDTO::getSalesAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        final Double spotRate = 0.0;
        Double totalOrderCount = salesVolumeOrderCountMap.getOrDefault(storeGuid, 0d);
        BigDecimal totalDineInDiscountPrice = salesVolumeRespDTOList.stream()
                .map(SalesVolumeRespDTO::getDineInDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        salesVolumeRespDTOList.forEach(detail -> {
            BigDecimal salesProportion = BigDecimal.ZERO;
            if (BigDecimalUtil.greaterThanZero(totalSalesAmount)) {
                salesProportion = detail.getSalesAmount().divide(totalSalesAmount, 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
            }
            detail.setSalesProportion(Double.valueOf(String.valueOf(salesProportion)));
            detail.setSpotRate(spotRate);
            detail.setSalesDineInProportion(spotRate);
            // 点单率：含有该商品的订单数/所有订单数
            if (detail.getOrderCount() > 0 && totalOrderCount > 0) {
                Double currentSpotRate = (detail.getOrderCount() / totalOrderCount) * 100;
                NumberFormat nf = NumberFormat.getNumberInstance();
                nf.setMaximumFractionDigits(2);
                nf.setRoundingMode(RoundingMode.HALF_UP);
                detail.setSpotRate(Double.valueOf(nf.format(currentSpotRate)));
            }
            // 堂食收入占比：商品堂食收入金额/门店总商品堂食收入金额
            if (BigDecimalUtil.greaterThanZero(totalDineInDiscountPrice)
                    && BigDecimalUtil.greaterThanZero(detail.getDineInDiscountPrice())) {
                detail.setSalesDineInProportion(detail.getDineInDiscountPrice()
                        .divide(totalDineInDiscountPrice, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).doubleValue());
            }
        });
    }

    private void oldSalesVolumeRespDTOAmountHandler(SalesVolumeReqDTO query, List<SalesVolumeRespDTO> groupByStoreResp) {
        if (CollectionUtils.isEmpty(groupByStoreResp)) {
            return;
        }
        LocalDateTime businessStartDateTime = query.getBusinessStartDateTime();
        if (Objects.isNull(oldCouponCalculateConfig) || businessStartDateTime.isAfter(oldCouponCalculateConfig.getOldCalculateMaxDateTime())) {
            return;
        }
        log.info("old date:{}", DateTimeUtils.localDateTime2String(businessStartDateTime));
        BigDecimal unMeaningValue = BigDecimal.ONE.negate();
        groupByStoreResp.forEach(e -> {
            e.setDiscountPrice(unMeaningValue);
            e.setDineInDiscountPrice(unMeaningValue);
            e.setSalesDineInProportion(-1D);
        });
    }

    private List<SalesVolumeRespDTO> listStoreSaleStatistics(SalesVolumeReqDTO query) {
        // 查询堂食销售总额
        GoodsSalesTotalDTO storeSalesTotal = tradeItemMapper.getStoreSalesTotal(query);
        query.setTotalSalesAmount(storeSalesTotal.getTotalReceivedSumPrice());
        query.setTotalDineInDiscountPrice(storeSalesTotal.getTotalDineInDiscountPrice());
        List<SalesVolumeRespDTO> detailResp;
        if (Boolean.TRUE.equals(query.getGroupByStoreFlag())) {
            detailResp = tradeItemMapper.pageGroupByStoreSaleStatistics(query);
        } else {
            detailResp = tradeItemMapper.pageStoreSaleStatistics(query);
        }
        log.info("[pageStoreSaleStatistics]查询db数据：{}", JacksonUtils.writeValueAsString(detailResp));
        return detailResp;
    }

    @Override
    public GoodsSalesTotalDTO storeSaleStatisticsTotal(SalesVolumeReqDTO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        query.setStartDate(query.getBusinessStartDateTime().toLocalDate());
        query.setEndDate(query.getBusinessEndDateTime().toLocalDate());
        GoodsSalesTotalDTO storeSalesTotal = tradeItemMapper.getStoreSalesTotal(query);
        oldGoodsSalesTotalDTOAmountHandler(query, storeSalesTotal);
        return storeSalesTotal;
    }

    private void oldGoodsSalesTotalDTOAmountHandler(SalesVolumeReqDTO query, GoodsSalesTotalDTO storeSalesTotal) {
        if (Objects.isNull(storeSalesTotal)) {
            return;
        }
        LocalDateTime businessStartDateTime = query.getBusinessStartDateTime();
        if (Objects.isNull(oldCouponCalculateConfig) || businessStartDateTime.isAfter(oldCouponCalculateConfig.getOldCalculateMaxDateTime())) {
            return;
        }
        BigDecimal unMeaningValue = BigDecimal.ONE.negate();
        storeSalesTotal.setTotalDiscountPrice(unMeaningValue);
        storeSalesTotal.setTotalDineInDiscountPrice(unMeaningValue);
    }

    @Override
    public String exportItemTypeStatistics(GoodsSalesVO query) {
        query.setPageSize(CommonConstant.MAX_EXPORT);
        GoodsSalesStatisticDTO goodsSalesStatisticDTO = queryItemTypeStatistics(query);
        PageInfo<GoodsSalesDTO> pageInfo = goodsSalesStatisticDTO.getPageInfo();
        List<ItemTypeExcelDTO> excelList = Lists.newArrayList();
        if (Objects.nonNull(pageInfo)) {
            List<GoodsSalesDTO> list = pageInfo.getList();
            if (list.size() > CommonConstant.MAX_EXPORT) {
                throw new BusinessException(CommonConstant.MAX_EXPORT_TITLE);
            }
            excelList = list.stream().map(e -> {
                ItemTypeExcelDTO excelDTO = new ItemTypeExcelDTO();
                BeanUtils.copyProperties(e, excelDTO);
                excelDTO.setSalesVolume(e.getSalesVolume().stripTrailingZeros().toPlainString());
                excelDTO.setActualReceivedPrice(e.getActualReceivedPrice().stripTrailingZeros().toPlainString());
                excelDTO.setDiscountPrice(e.getDiscountPrice().stripTrailingZeros().toPlainString());
                excelDTO.setGrossProfitAmount(e.getGrossProfitAmount().stripTrailingZeros().toPlainString());
                return excelDTO;
            }).collect(Collectors.toList());
        }
        try {
            String sheetName = "商品分类统计";
            String fileName = sheetName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return ExcelUtils.exportExcel(excelList,
                    null, sheetName, ItemTypeExcelDTO.class, fileName, ossClient);
        } catch (Exception e) {
            throw new BusinessException("导出失败");
        }
    }

    @Override
    public String exportGroupItemSaleStatistics(GoodsSalesVO query) {
        query.setPageSize(CommonConstant.MAX_EXPORT);
        GoodsSalesStatisticDTO goodsSalesStatisticDTO = queryGroupItemSaleStatistics(query);
        PageInfo<GoodsSalesDTO> pageInfo = goodsSalesStatisticDTO.getPageInfo();
        List<GroupItemTypeExcelDTO> excelList = Lists.newArrayList();
        if (Objects.nonNull(pageInfo)) {
            List<GoodsSalesDTO> list = pageInfo.getList();
            if (list.size() > CommonConstant.MAX_EXPORT) {
                throw new BusinessException(CommonConstant.MAX_EXPORT_TITLE);
            }
            excelList = list.stream().map(e -> {
                GroupItemTypeExcelDTO excelDTO = new GroupItemTypeExcelDTO();
                BeanUtils.copyProperties(e, excelDTO);
//                excelDTO.setSellingPrise(e.getSellingPrise().stripTrailingZeros().toPlainString());
                excelDTO.setSalesVolume(e.getSalesVolume().stripTrailingZeros().toPlainString());
                excelDTO.setActualReceivedPrice(e.getActualReceivedPrice().stripTrailingZeros().toPlainString());
                excelDTO.setDiscountPrice(e.getDiscountPrice().stripTrailingZeros().toPlainString());
                return excelDTO;
            }).collect(Collectors.toList());
        }
        try {
            String sheetName = "套餐销量统计";
            String fileName = sheetName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return ExcelUtils.exportExcel(excelList,
                    null, sheetName, GroupItemTypeExcelDTO.class, fileName, ossClient);
        } catch (Exception e) {
            throw new BusinessException("导出失败");
        }
    }

    @Override
    public List<String> pageStoreSaleStatisticsType(SalesVolumeReqDTO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        query.setStartDate(query.getBusinessStartDateTime().toLocalDate());
        query.setEndDate(query.getBusinessEndDateTime().toLocalDate());
        return tradeItemMapper.pageStoreSaleStatisticsType(query);
    }

    @Override
    public Page<SalesDetailRespDTO> pageSaleDetail(SalesDetailQO query) {
        Long total = tradeItemMapper.countSaleDetail(query);
        if (total == 0) {
            log.warn("[pageSaleDetail]总数为0");
            return new Page<>(query.getCurrentPage(), query.getPageSize(), 0, Lists.newArrayList());
        }
        List<SalesDetailRespDTO> saleDetailPage = tradeItemMapper.pageSaleDetail(query);
        return new Page<>(query.getCurrentPage(), query.getPageSize(), total, saleDetailPage);
    }

    @Override
    public String exportSaleDetail(SalesDetailQO query) {
        Page<SalesDetailRespDTO> respDTOPage = pageSaleDetail(query);
        List<SalesDetailRespDTO> respDTOList = respDTOPage.getData();
        String sheetName = "销售明细";
        String fileName = sheetName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        List<SalesDetailExcelDTO> excelDTOList = buildSaleDetailDTOList(respDTOList, query);
        try {
            return ExcelUtils.exportExcel(excelDTOList, null, sheetName, SalesDetailExcelDTO.class, fileName, ossClient);
        } catch (Exception e) {
            throw new BusinessException("销售明细导出失败");
        }
    }

    private List<SalesDetailExcelDTO> buildSaleDetailDTOList(List<SalesDetailRespDTO> respDTOList, SalesDetailQO query) {
        if (org.springframework.util.CollectionUtils.isEmpty(respDTOList)) {
            return Lists.newArrayList();
        }
        int startIndex = (query.getCurrentPage() - 1) * query.getPageSize();
        List<SalesDetailExcelDTO> excelDTOList = new ArrayList<>();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (int i = 0; i < respDTOList.size(); i++) {
            SalesDetailRespDTO respDTO = respDTOList.get(i);
            SalesDetailExcelDTO excelDTO = new SalesDetailExcelDTO();
            String serialNumber = String.valueOf(startIndex + i + 1);
            excelDTO.setSerialNumber(serialNumber);
            excelDTO.setItemName(respDTO.getItemName());
            excelDTO.setItemTypeName(respDTO.getItemTypeName());
            excelDTO.setItemType(respDTO.getItemType());
            excelDTO.setOperation(respDTO.getOperation());
            excelDTO.setOrderNumber(respDTO.getOrderNumber());
            excelDTO.setUnit(respDTO.getUnit());
            excelDTO.setPrice(respDTO.getPrice().toPlainString());
            excelDTO.setAttrPrice(respDTO.getAttrPrice().toPlainString());
            excelDTO.setOrderNo(respDTO.getOrderNo());
            excelDTO.setCreateTime(respDTO.getCreateTime().format(df));
            excelDTO.setCheckoutTime(respDTO.getCheckoutTime().format(df));
            excelDTOList.add(excelDTO);
        }
        return excelDTOList;
    }

    @Override
    public List<GoodsSalesDTO> pageStoreSaleStatisticsGroup(SalesVolumeReqDTO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        query.setStartDate(query.getBusinessStartDateTime().toLocalDate());
        query.setEndDate(query.getBusinessEndDateTime().toLocalDate());
        tradeItemMapper.pageStoreSaleStatisticsType(query);
        return tradeItemMapper.pageStoreSaleStatisticsGroup(query);
    }

}
