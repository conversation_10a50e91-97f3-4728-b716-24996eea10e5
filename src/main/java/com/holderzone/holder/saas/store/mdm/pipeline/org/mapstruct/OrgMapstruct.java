package com.holderzone.holder.saas.store.mdm.pipeline.org.mapstruct;

import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.BrandInfoDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.OrganizationInfoDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.domain.BrandDO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.domain.OrganizationDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UserMapstruct
 * @date 2019/11/18 17:09
 * @description
 * @program holder-saas-store
 */
@Component
@Mapper(componentModel = "spring")
public interface OrgMapstruct {

    OrgMapstruct INSTANCE = Mappers.getMapper(OrgMapstruct.class);

    @Mappings({
            @Mapping(source = "guid", target = "thirdNo"),
//            @Mapping(source = "businessStart", target = "businessTime", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(source = "businessStart", target = "businessTime", dateFormat = "HH:mm:ss"),
            @Mapping(source = "parentIds", target = "fid"),
            @Mapping(source = "provinceCode", target = "province"),
            @Mapping(source = "cityCode", target = "city"),
            @Mapping(source = "countyCode", target = "district"),
            @Mapping(source = "countyName", target = "districtName"),
            @Mapping(source = "addressDetail", target = "address"),
            @Mapping(source = "description", target = "remark"),
            @Mapping(target = "enabled", expression = "java(organizationDO.getIsEnable()?1:0)"),
    })
    OrganizationInfoDTO organizationDO2InfoDTO(OrganizationDO organizationDO);

    @Mappings({
            @Mapping(source = "guid", target = "thirdNo"),
            @Mapping(source = "logoUrl", target = "logo"),
    })
    BrandInfoDTO brandDO2InfoDTO(BrandDO brandDO);

    @Mappings({
            @Mapping(source = "thirdNo", target = "guid"),
            @Mapping(source = "businessTime", target = "businessStart"),
            @Mapping(source = "fid", target = "parentIds"),
            @Mapping(source = "province", target = "provinceCode"),
            @Mapping(source = "city", target = "cityCode"),
            @Mapping(source = "district", target = "countyCode"),
            @Mapping(source = "districtName", target = "countyName"),
            @Mapping(source = "address", target = "addressDetail"),
            @Mapping(source = "remark", target = "description"),
            @Mapping(target = "isEnable", expression = "java(java.util.Objects.equals(organizationInfoDTO.getEnabled(),1))")
    })
    OrganizationDO organizationInfoDTO2DO(OrganizationInfoDTO organizationInfoDTO);
}
