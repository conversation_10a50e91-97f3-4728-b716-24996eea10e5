package com.holderzone.holder.saas.store.hw.service;

import com.holderzone.saas.store.dto.hw.*;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDetailDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveService
 * @date 2019/05/28 16:21
 * @description //TODO
 * @program holder-saas-store-reserve
 */
public interface ReserveService {

    ResultDTO validateReservePhone(MchntValidateDTO mchntValidateDTO);

    ResultDTO validateDateReturnInterval(ReserveDateDTO reserveDateDTO);

    ResultDTO validateIntervalReturnTime(ReserveIntervalDTO reserveIntervalDTO);

    ResultDTO validateDateTimeReturnNothing(ReserveDateTimeDTO reserveDateTimeDTO);

    ResultDTO validatePeopleReturnNothing(ReservePeopleDTO reservePeopleDTO);

    ResultDTO validateRoomReturnRoom(ReserveRoomDTO reserveRoomDTO);

    ResultDTO validateAllReturnSuccess(ReserveAllDTO reserveAllDTO);

    ReserveRecordDetailDTO launch(ReserveLaunchDTO reserveLaunchDTO);
}