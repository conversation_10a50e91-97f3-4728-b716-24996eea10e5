package com.holderzone.holder.saas.store.pay.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holder.saas.store.pay.entity.domain.PayRecordDO;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.pay.QuickPayStatisticsReqDTO;
import com.holderzone.saas.store.dto.pay.QuickPayStatisticsRespDTO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备配置 Mapper 接口
 *
 * <AUTHOR>
 * @since 2019-03-29
 */
public interface PayRecordMapper extends BaseMapper<PayRecordDO> {

    BigDecimal getStatisticsTotalAmount(@Param("basePageDTO") BasePageDTO basePageDTO, @Param("startTime") LocalDateTime startTime);

    List<QuickPayStatisticsRespDTO> queryQuickPayStatistics(@Param("request") QuickPayStatisticsReqDTO request);

}
