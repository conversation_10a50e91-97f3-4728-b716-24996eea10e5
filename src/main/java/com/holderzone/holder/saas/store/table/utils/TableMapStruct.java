package com.holderzone.holder.saas.store.table.utils;

import com.holderzone.holder.saas.store.table.domain.TableBasicDO;
import com.holderzone.holder.saas.store.table.domain.TableDO;
import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.holder.saas.store.table.domain.TableTagDO;
import com.holderzone.holder.saas.store.table.domain.bo.DelayAutoUnlockBO;
import com.holderzone.holder.saas.store.table.domain.bo.TableBO;
import com.holderzone.holder.saas.store.table.domain.bo.TableInfoBO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.print.content.PrintTurnTableDTO;
import com.holderzone.saas.store.dto.table.*;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/12/27 11:28
 */
@Mapper
public interface TableMapStruct {

    TableMapStruct TABLE_MAP_STRUCT = Mappers.getMapper(TableMapStruct.class);


    TableBasicDO tableBo2BasicDo(TableBO initTable);

    TableBasicDO tableBasicDto2Do(TableBasicDTO tableBasicDTO);

    @Mappings({
            @Mapping(target = "subStatus", source = "subStatusStr"),
    })
    TableOrderDO tableBo2OrderDo(TableBO initTable);

    @Mappings({
            @Mapping(target = "tableGuid", source = "guid"),
    })
    TableOrderDO tableBasicDo2OrderDo(TableBasicDO tableBasicDO);

    List<TableBasicDTO> tableBasicDos2BasicDto(List<TableBasicDO> tableBasicDOS);

    TableBasicDTO tableBasicDo2BasicDto(TableBasicDO tableBasicDO);

    TableBasicDO tableBatch2TableDo(TableBatchCreateDTO tableBatchCreateDTO);

//    List<TableOrderDTO> tableDoList2DtoList(List<TableDO> tableDOList);
//
//    @Mappings({
//            @Mapping(target = "tableGuid", source = "guid"),
//            @Mapping(target = "tableGuid", source = "subStatus")
//    })
//    TableOrderDTO tableDo2Dto(TableDO tableDO);

    @Mappings({
            @Mapping(target = "openStaffGuid", source = "userGuid"),
            @Mapping(target = "openStaffName", source = "userName"),
    })
    TableOrderDO openTableDto2OrderDo(OpenTableDTO openTableDTO);

    List<TableBO> tableDoList2BoList(List<TableDO> tableDOList);

    @Mappings({
            @Mapping(target = "subStatusStr", source = "subStatus"),
            @Mapping(target = "subStatus", ignore = true),
    })
    TableBO tableDo2Bo(TableDO tableDO);

    List<TableOrderDTO> tableBoList2DtoList(List<TableBO> tableBOS);

    @Mappings({
            @Mapping(target = "tableGuid", source = "guid"),
    })
    TableOrderDTO tableBo2Dto(TableBO tableBO);

    @Mappings(
            {
                    @Mapping(target = "tableInfoDTOS", ignore = true)
            }
    )
    TableOrderCombineDTO tableCombine(TableCombineDTO tableCombineDTO);

    List<TableInfoDTO> tableDoList2InfoDtoList(List<TableOrderDO> tableOrderDOS);

    @Mappings(
            {
                    @Mapping(source = "tableGuid", target = "tableGuid"),
                    @Mapping(source = "orderGuid", target = "orderGuid")
            }
    )
    TableInfoDTO tableDo2InfoDto(TableOrderDO tableOrderDO);

    List<TableInfoBO> tableDoList2InfoBoList(List<TableOrderDO> tableOrderDOS);

    TableInfoBO tableDo2InfoBo(TableOrderDO tableOrderDO);

    @Mappings(
            {
                    @Mapping(target = "tableInfoBos", ignore = true)
            }
    )
    DelayAutoUnlockBO tableLockDto2ReleaseLockBo(TableLockDTO tableLockDTO);


    @Mappings(
            {
                    @Mapping(target = "tableName", source = "newTableCode"),
                    @Mapping(target = "areaName", source = "newTableAreaName"),
                    @Mapping(target = "tableGuid", source = "newTableGuid")
            }
    )
    TradeTableDTO turnTableDto2tradeTableDto(TurnTableDTO turnTableDTO);

    @Mappings(
            {
                    @Mapping(target = "srcTableName", source = "originTableCode"),
                    @Mapping(target = "destTableName", source = "newTableCode"),
                    @Mapping(target = "deviceId", source = "deviceId"),
                    @Mapping(target = "areaGuid", source = "originTableAreaGuid"),
                    @Mapping(target = "tableGuid", source = "originTableGuid"),
                    @Mapping(target = "printUid", source = "originTableAreaGuid"),
                    @Mapping(target = "operatorStaffGuid", source = "userGuid"),
                    @Mapping(target = "operatorStaffName", source = "userName"),
            }
    )
    PrintTurnTableDTO turnTableDto2PrintTurnDto(TurnTableDTO turnTableDTO);

    @Mappings({
            @Mapping(target = "openStaffGuid", source = "userGuid"),
            @Mapping(target = "openStaffName", source = "userName"),
    })
    TableOrderDO recheck2OpenTableDo(BaseTableDTO baseTableDTO);


    CreateDineInOrderReqDTO tableCombine2CreateOrder(TableCombineDTO tableCombineDTO);

    CreateDineInOrderReqDTO openTableDTO2CreateDineInOrderReqDTO(OpenTableDTO openTableDTO);

    CreateDineInOrderReqDTO openAssociatedTableDTO2CreateDineInOrderReqDTO(OpenAssociatedTableDTO openAssociatedTableDTO);

    TableOrderDO localizeTableDTO2TableOrderDO(LocalizeTableDTO localizeTableDTO);

    @Mappings({
            @Mapping(target="storeGuid",expression = "java(tableTagDTO.getStoreGuid())")
    })
    TableTagDO createTableDO(TableTagDTO tableTagDTO);

    @Mappings({
            @Mapping(target="guid",expression = "java(tableTagDO.getGuid().toString())"),
    })
    TableTagDTO createTableDTO(TableTagDO tableTagDO);

    List<TableTagDTO> turnTagList(List<TableTagDO> tableTagDOS);
}
