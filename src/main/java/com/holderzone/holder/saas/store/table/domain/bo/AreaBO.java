package com.holderzone.holder.saas.store.table.domain.bo;

import com.holderzone.saas.store.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

import static com.holderzone.holder.saas.store.table.constant.Constant.INIT_AREA_NAME;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/12/27 11:03
 * 区域业务对象
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class AreaBO extends BaseBO {

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("排序字段")
    private Integer sort;

    @ApiModelProperty("0-启用，1-禁用")
    private Integer enable = 0;

    /**
     * 是否删除
     */
    @ApiModelProperty("0-未删除，1-删除")
    private Integer deleted;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime gmtModified;

    public static AreaBO createInitArea(BaseDTO baseDTO) {
        AreaBO build = AreaBO.builder()
                .areaName(INIT_AREA_NAME)
                .sort(0)
                .enable(0)
                .deleted(0)
                .build();
        build.setStoreGuid(baseDTO.getStoreGuid());
        build.setStoreName(baseDTO.getStoreName());
        return build;
    }

}
