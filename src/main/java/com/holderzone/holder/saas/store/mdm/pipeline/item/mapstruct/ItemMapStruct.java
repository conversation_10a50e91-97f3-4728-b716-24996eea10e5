package com.holderzone.holder.saas.store.mdm.pipeline.item.mapstruct;

import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.ItemSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.SkuSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.TypeSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.ItemDO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.SkuDO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.TypeDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

@Component
@Mapper(componentModel = "spring")
public interface ItemMapStruct {

    ItemMapStruct INSTANCE = Mappers.getMapper(ItemMapStruct.class);

    @Mappings({
            @Mapping(target = "itemFrom", expression = "java(java.util.Objects.equals(1,itemDO.getItemFrom()) ? 1 : 0)"),
            @Mapping(target = "isEnable", expression = "java(itemDO.getIsEnable() ? 1 : 0)")
    })
    ItemSyncDTO itemDo2itemSynDTO(ItemDO itemDO);

    @Mapping(target = "isEnable",
            expression = "java(java.util.Objects.equals(1,mdmItemSynDTO.getIsEnable()) ? Boolean.TRUE : Boolean.FALSE)")
    ItemDO itemSynDTO2itemDO(ItemSyncDTO mdmItemSynDTO);


    @Mapping(target = "typeFrom", expression = "java(java.util.Objects.equals(1,typeDO.getTypeFrom()) ? 1 : java.util.Objects.equals(5,typeDO.getTypeFrom()) ? 5 : 0)")
    TypeSyncDTO typeDO2typeSynDTO(TypeDO typeDO);

    TypeDO typeSynDTO2typeDO(TypeSyncDTO mdmTypeSynDTO);

    @Mappings({
            @Mapping(target = "skuFrom", expression = "java(java.util.Objects.equals(1,skuDO.getSkuFrom()) ? 1 : 0)"),
            @Mapping(target = "isEnable", expression = "java(skuDO.getIsEnable() ? 1 : 0)")
    })
    SkuSyncDTO skuDo2skuSynDTO(SkuDO skuDO);

    @Mapping(target = "isEnable",
            expression = "java(java.util.Objects.equals(1,mdmSkuSynDTO.getIsEnable()) ? Boolean.TRUE : Boolean.FALSE)")
    SkuDO skuSynDTO2skuDO(SkuSyncDTO mdmSkuSynDTO);

}
