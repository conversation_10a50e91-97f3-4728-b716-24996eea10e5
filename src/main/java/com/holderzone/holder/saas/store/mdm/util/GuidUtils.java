package com.holderzone.holder.saas.store.mdm.util;

import java.util.Random;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className GuidUtils
 * @date 2018/10/25 15:28
 * @description id生成工具
 * @program holder-saas-store-organization
 */
public final class GuidUtils {

    /**
     * 生成组织code，7位唯一
     *
     * @return Long
     */
    public static String nextOrganizationGuid() {
        int guid = new Random().nextInt(9999999);
        if (guid < 1000000) {
            guid += 1000000;
        }
        return String.valueOf(guid);
    }
}
