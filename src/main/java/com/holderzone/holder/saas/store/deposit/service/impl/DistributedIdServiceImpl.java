package com.holderzone.holder.saas.store.deposit.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.deposit.service.DistributedIdService;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
public class DistributedIdServiceImpl implements DistributedIdService {

    private static final String TAG_DEPOSIT = "deposit/item";
    private static final String TAG_OPERATION = "operation/item";
    private static final String TAG_OPERATION_GOODS = "operation/operation_goods";
    private static final String TAG_GOODS_CLASSIFY = "deposit/goods";
    private static final String TAG_DEPOSIT_REMIND = "deposit/remind";

    private final RedisTemplate redisTemplate;


    @Autowired
    public DistributedIdServiceImpl(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public Long rawId(String tag) {
        try {
            return BatchIdGenerator.getGuid(redisTemplate, tag);
        } catch (IOException e) {
            throw new BusinessException("生成Guid失败：" + e.getMessage());
        }
    }

    @Override
    public String nextId(String tag) {
        return String.valueOf(rawId(tag));
    }

    @Override
    public String nextDepositItemGuid() {
        return nextId(TAG_DEPOSIT);
    }

    @Override
    public String nextGoodsGuid() {
        return nextId(TAG_GOODS_CLASSIFY);
    }

    @Override
    public String nextOperationGuid() {
        return nextId(TAG_OPERATION);
    }

    @Override
    public String nextOperationGoodsGuid() {
        return nextId(TAG_OPERATION_GOODS);
    }

    @Override
    public String nextRemindGuid() {
        return nextId(TAG_DEPOSIT_REMIND);
    }
}
