package com.holderzone.holder.saas.store.message.util;


import com.holderzone.holder.saas.store.message.domain.MessageDO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.message.SystemMessageDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MessageMapStruct
 * @date 2018/09/04 17:00
 * @description
 * @program holder-saas-bussiness-message
 */
@Mapper
public interface MessageMapStruct {


    MessageMapStruct MESSAGE_MAP_STRUCT = Mappers.getMapper(MessageMapStruct.class);

    MessageDO msgDtoToDo(BusinessMessageDTO businessMessageDTO);

    @Mappings({

    })
    MessageDO systemMsgToBusinessMsg(SystemMessageDTO messageDTO);

    List<MessageDO> msgDtosToDos(List<BusinessMessageDTO> businessMessageDTOS);
}
