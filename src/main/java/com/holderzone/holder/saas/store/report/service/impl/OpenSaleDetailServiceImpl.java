package com.holderzone.holder.saas.store.report.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.report.mapper.TradeDetailMapper;
import com.holderzone.holder.saas.store.report.mapper.TradeItemMapper;
import com.holderzone.holder.saas.store.report.mapper.TradeOrderMapper;
import com.holderzone.holder.saas.store.report.service.OpenSaleDetailService;
import com.holderzone.saas.store.dto.report.openapi.*;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailRespDTO;
import com.holderzone.saas.store.dto.report.openapi.SalePayDetailRespDTO;
import com.holderzone.saas.store.dto.report.openapi.SaleProductDetailRespDTO;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.report.ReceiptTypeEnum;
import joptsimple.internal.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class OpenSaleDetailServiceImpl implements OpenSaleDetailService {
    public static final int DEFAULT_LIMIT = 500;
    public static final int MAX_LIMIT = 1000;

    private final TradeDetailMapper tradeDetailMapper;

    private final TradeOrderMapper tradeOrderMapper;

    private final TradeItemMapper tradeItemMapper;

    private final Executor openapiReportQueryExecutor;

    @Override
    public SaleDetailLimitRespDTO query(SaleDetailQueryDTO saleDetailQueryDTO) {
        // 参数校验
        checkSaleDetailQueryParams(saleDetailQueryDTO);
        // 填充参数默认值
        fullQueryParamsDefaultValue(saleDetailQueryDTO);
        // 查询符合要求的订单
        List<SaleDetailRespDTO> saleDetailRespList = tradeOrderMapper.querySaleDetail(saleDetailQueryDTO);
        if (CollectionUtils.isEmpty(saleDetailRespList)) {
            return SaleDetailLimitRespDTO.buildEmpty();
        }
        // 分页游标
        String cursor = null;
        SaleDetailRespDTO minSaleDetailRespDTO = saleDetailRespList.stream()
                .min(Comparator.comparing(SaleDetailRespDTO::getGmtCreate).thenComparing(SaleDetailRespDTO::getOrderGuid))
                .orElse(null);
        if (Objects.nonNull(minSaleDetailRespDTO) && saleDetailRespList.size() == saleDetailQueryDTO.getLimit()) {
            cursor = minSaleDetailRespDTO.getOrderGuid();
            saleDetailRespList.removeIf(e -> e.getOrderGuid().equals(minSaleDetailRespDTO.getOrderGuid()));
        }
        List<Long> orderGuidList = saleDetailRespList.stream()
                .map(e -> Long.valueOf(e.getOrderGuid()))
                .distinct()
                .collect(Collectors.toList());
        // 查询商品明细
        CompletableFuture<List<SaleProductDetailRespDTO>> saleProductDetailRespListFuture = CompletableFuture
                .supplyAsync(() ->
                        tradeItemMapper.querySaleProductDetail(saleDetailQueryDTO.getEnterpriseGuid(), orderGuidList), openapiReportQueryExecutor)
                .exceptionally(throwable -> {
                    log.error("查询商品明细列表失败", throwable);
                    return null;
                });
        // 查询交易明细
        CompletableFuture<List<SalePayDetailRespDTO>> salePayDetailRespListFuture = CompletableFuture
                .supplyAsync(() ->
                        tradeDetailMapper.querySalePayDetail(saleDetailQueryDTO.getEnterpriseGuid(), orderGuidList), openapiReportQueryExecutor)
                .exceptionally(throwable -> {
                    log.error("查询交易明细列表失败", throwable);
                    return null;
                });
        CompletableFuture<Void> all = CompletableFuture.allOf(saleProductDetailRespListFuture, salePayDetailRespListFuture);
        try {
            all.get();
            // 返回订单处理
            respHandler(saleDetailRespList, saleProductDetailRespListFuture.get(), salePayDetailRespListFuture.get());
            return SaleDetailLimitRespDTO.buildResult(saleDetailRespList, cursor);
        } catch (Exception e) {
            log.error("开放接口查询销售数据列表失败", e);
            Thread.currentThread().interrupt();
            throw new BusinessException("系统繁忙稍后再试");
        }
    }

    /**
     * 参数校验
     */
    private void checkSaleDetailQueryParams(SaleDetailQueryDTO saleDetailQueryDTO) {
        String enterpriseGuid = saleDetailQueryDTO.getEnterpriseGuid();
        if (StringUtils.isEmpty(enterpriseGuid)) {
            throw new BusinessException("企业guid必填");
        }
        Integer limit = saleDetailQueryDTO.getLimit();
        if (Objects.nonNull(limit)) {
            if (limit <= 0) {
                saleDetailQueryDTO.setLimit(DEFAULT_LIMIT);
            }
            if (limit > MAX_LIMIT) {
                saleDetailQueryDTO.setLimit(MAX_LIMIT);
            }
        }
    }

    /**
     * 填充参数默认值
     */
    private void fullQueryParamsDefaultValue(SaleDetailQueryDTO saleDetailQueryDTO) {
        Integer limit = saleDetailQueryDTO.getLimit();
        if (Objects.isNull(limit)) {
            saleDetailQueryDTO.setLimit(DEFAULT_LIMIT);
        }
        // Limit= Limit + 1
        saleDetailQueryDTO.setLimit(saleDetailQueryDTO.getLimit() + 1);
    }


    /**
     * 处理返回信息
     */
    private void respHandler(List<SaleDetailRespDTO> saleDetailRespList,
                             List<SaleProductDetailRespDTO> saleProductDetailRespList,
                             List<SalePayDetailRespDTO> salePayDetailRespList) {
        Map<String, List<SaleProductDetailRespDTO>> saleProductDetailRespMap = saleProductDetailRespList.stream()
                .collect(Collectors.groupingBy(SaleProductDetailRespDTO::getOrderGuid));
        Map<String, List<SalePayDetailRespDTO>> salePayDetailRespMap = salePayDetailRespList.stream()
                .collect(Collectors.groupingBy(SalePayDetailRespDTO::getOrderGuid));
        // 订单明细返回处理
        saleDetailRespHandler(saleDetailRespList, saleProductDetailRespMap, salePayDetailRespMap);
    }


    /**
     * 订单明细返回处理
     */
    private void saleDetailRespHandler(List<SaleDetailRespDTO> saleDetailRespList,
                                       Map<String, List<SaleProductDetailRespDTO>> saleProductDetailRespMap,
                                       Map<String, List<SalePayDetailRespDTO>> salePayDetailRespMap) {
        if (CollectionUtils.isEmpty(saleDetailRespList)) {
            return;
        }
        for (SaleDetailRespDTO respDTO : saleDetailRespList) {
            if (StringUtils.isEmpty(respDTO.getRemark())) {
                respDTO.setRemark(Strings.EMPTY);
            }
            respDTO.setSaleCount(1);
            // 商品明细
            List<SaleProductDetailRespDTO> productDetailRespList = saleProductDetailRespMap.getOrDefault(respDTO.getOrderGuid(), Lists.newArrayList());
            respDTO.setProductDetails(saleProductDetailRespHandler(respDTO, productDetailRespList));
            // 支付明细
            List<SalePayDetailRespDTO> payDetailRespList = salePayDetailRespMap.getOrDefault(respDTO.getOrderGuid(), Lists.newArrayList());
            respDTO.setPayDetails(salePayDetailRespHandler(payDetailRespList));
        }
    }


    /**
     * 支付明细返回处理
     */
    private List<SalePayDetailRespDTO> salePayDetailRespHandler(List<SalePayDetailRespDTO> payDetailRespList) {
        // 排序
        payDetailRespList = payDetailRespList.stream()
                .sorted(Comparator.comparing(SalePayDetailRespDTO::getGmtCreate))
                .collect(Collectors.toList());
        for (int i = 0; i < payDetailRespList.size(); i++) {
            SalePayDetailRespDTO payRespDTO = payDetailRespList.get(i);
            payRespDTO.setIndex(i + 1);
        }
        return payDetailRespList;
    }

    /**
     * 订单商品明细返回处理
     */
    private List<SaleProductDetailRespDTO> saleProductDetailRespHandler(SaleDetailRespDTO respDTO, List<SaleProductDetailRespDTO> productDetailRespList) {
        // 排序
        productDetailRespList = productDetailRespList.stream()
                .sorted(Comparator.comparing(SaleProductDetailRespDTO::getGmtCreate))
                .collect(Collectors.toList());
        for (int i = 0; i < productDetailRespList.size(); i++) {
            SaleProductDetailRespDTO productRespDTO = productDetailRespList.get(i);
            productRespDTO.setIndex(i + 1);
            if (respDTO.getReceiptType() == ReceiptTypeEnum.REFUND.getCode()) {
                productRespDTO.setPrice(-productRespDTO.getPrice());
                productRespDTO.setTotalPrice(-productRespDTO.getTotalPrice());
                productRespDTO.setDiscountFee(-productRespDTO.getDiscountFee());
                productRespDTO.setActuallyPayFee(-productRespDTO.getActuallyPayFee());
            }
        }
        return productDetailRespList;
    }
}
