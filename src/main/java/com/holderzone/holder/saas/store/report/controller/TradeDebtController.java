package com.holderzone.holder.saas.store.report.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Assert;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.report.constant.ErrorConstant;
import com.holderzone.holder.saas.store.report.helper.ExceptionHelper;
import com.holderzone.holder.saas.store.report.service.TradeDebtService;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.DebtUnitRecordDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 挂账明细报表
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/trade/debt")
public class TradeDebtController {

    private final TradeDebtService tradeDebtService;

    /**
     * 挂账明细报表
     */
    @PostMapping("/list")
    public Message<DebtUnitRecordDTO> list(@RequestBody ReportQueryVO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeDebtService.list(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "挂账明细", JacksonUtils.writeValueAsString(query)));
        }
    }

    @PostMapping("/export")
    public String export(@RequestBody ReportQueryVO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeDebtService.export(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "导出挂账明细", JacksonUtils.writeValueAsString(query)));
        }
    }
}
