package com.holderzone.holder.saas.store.table.service;

import com.holderzone.saas.store.dto.order.request.OrderTableBillReqDTO;
import com.holderzone.saas.store.dto.table.OpenTableByOrderDTO;
import com.holderzone.saas.store.dto.table.OpenTableOrderDTO;
import com.holderzone.saas.store.dto.trade.OrderTableDTO;
import com.holderzone.saas.store.dto.trade.OrderTableVO;

public interface TableOrderOpenService {

    /**
     * 根据订单号查询桌台并开台
     */
    OpenTableOrderDTO openTableByOrderGuid(OpenTableByOrderDTO dto);

    /**
     * 修改订单关联的桌台
     */
    OrderTableVO updateTableOrder(OrderTableDTO orderTableDTO);

    /**
     * 手动结账关台
     */
    boolean dealClose(OrderTableBillReqDTO orderTableBillReqDTO);
}
