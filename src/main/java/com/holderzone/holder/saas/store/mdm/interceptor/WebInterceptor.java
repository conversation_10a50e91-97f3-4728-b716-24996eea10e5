package com.holderzone.holder.saas.store.mdm.interceptor;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WebInterceptor
 * @date 2019/02/14 09:00
 * @description 固定UserInfo，调试使用！
 * 如需关闭，请注释掉：
 * FakeInterceptor的“@Configuration”
 * WebConfig#addInterceptors的“registry.addInterceptor(new FakeInterceptor()).order(-1);”
 * @program holder-saas-store-print
 */
@Configuration
public class WebInterceptor implements HandlerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(WebInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String enterpriseGuid = request.getHeader("enterpriseGuid");
        if (StringUtils.hasText(enterpriseGuid)) {
            UserContextUtils.putErp(enterpriseGuid);
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            log.info("enterpriseGuid:{} has been put into userContext", enterpriseGuid);
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        UserContextUtils.remove();
        EnterpriseIdentifier.remove();
        log.info("userContext has been removed");
    }
}
