package com.holderzone.holder.saas.store.table.service;

import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.ReserveBatchCreateOrderReqDTO;
import com.holderzone.saas.store.dto.table.TableCombineDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradeService
 * @date 2019/01/23 11:39
 * @description
 * @program holder-saas-store-table
 */
public interface TradeService {

    void closeOrder(CancelOrderReqDTO cancelOrderReqDTO);

    void notifyTradeCombine(TableOrderCombineDTO tableOrderCombineDTO);

    void separate(TableOrderCombineDTO tableOrderCombineDTO);

    String openTable(CreateDineInOrderReqDTO createDineInOrderReqDTO);

    String bacthopenTable(ReserveBatchCreateOrderReqDTO batchCreateOrderReqDTO);

    void notifyTradeTurn(TradeTableDTO turnTableDTO);

    /**
     * 撤销子桌订单的第三方活动
     *
     * @param tableCombineDTO 入参
     */
    void batchRevokeThirdActivity(TableCombineDTO tableCombineDTO);

    /**
     * 查询子桌订单的第三方平台活动/团购验券
     */
    List<String> queryHasThirdActivityOrder(TableCombineDTO tableCombineDTO);
}
