package com.holderzone.holder.saas.store.message.util;


import cn.hutool.core.lang.UUID;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.sdk.util.BatchIdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DynamicUtils
 * @date 2018/10/25 15:28
 * @description
 * @program holder-saas-store-order
 */
@Component
public class DynamicHelper {

    private static final Logger log = LoggerFactory.getLogger(DynamicHelper.class);

    public void changeDatasource(String enterpriseGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
    }

    public void clear() {
        EnterpriseIdentifier.remove();
    }

    /**
     * 生成guid
     */
    public Long generateGuid() {

        try {
            return SnowflakeKeyGeneratorUtil.generateKey();
        } catch (Exception e) {
            log.error("生成guid失败，e={}", e.getMessage());
        }
        return Math.abs(UUID.fastUUID().getLeastSignificantBits());
    }
}
