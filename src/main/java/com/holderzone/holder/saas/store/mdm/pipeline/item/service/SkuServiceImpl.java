package com.holderzone.holder.saas.store.mdm.pipeline.item.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.dynamic.datasource.starter.anno.SwitchServerCodeAnno;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.SkuSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.SkuDO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.mapper.SkuMapper;
import com.holderzone.holder.saas.store.mdm.pipeline.item.mapstruct.ItemMapStruct;
import com.holderzone.holder.saas.store.mdm.service.DistributedIdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SkuServiceImpl
 * @date 2019/11/23 下午6:29
 * @description //
 * @program holder
 */
@Slf4j
@Service
public class SkuServiceImpl extends ServiceImpl<SkuMapper, SkuDO> implements SkuService {


    private final DistributedIdService distributedIdService;

    private final ItemMapStruct itemMapStruct;

    @Autowired
    public SkuServiceImpl(DistributedIdService distributedIdService, ItemMapStruct itemMapStruct) {
        this.distributedIdService = distributedIdService;
        this.itemMapStruct = itemMapStruct;
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public void insertSku(SkuSyncDTO mdmSkuSynDTO) {
        SkuDO skuDO = itemMapStruct.skuSynDTO2skuDO(mdmSkuSynDTO);
        skuDO.setMinOrderNum(BigDecimal.ONE);
        skuDO.setIsMemberDiscount(0);
        skuDO.setIsWholeDiscount(1);
        skuDO.setIsRack(1);
        skuDO.setIsJoinBuffet(1);
        skuDO.setIsJoinWeChat(1);
        skuDO.setIsJoinElm(0);
        skuDO.setIsJoinMt(0);
        skuDO.setIsOpenStock(0);
        this.save(skuDO);
        log.info("insert 智慧门店保存 sku = {} ,success！", JacksonUtils.writeValueAsString(skuDO));
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public void updateSkuByGuid(SkuSyncDTO mdmSkuSynDTO) {
        SkuDO skuDO = itemMapStruct.skuSynDTO2skuDO(mdmSkuSynDTO);
        this.update(skuDO, new LambdaQueryWrapper<SkuDO>().eq(SkuDO::getGuid, skuDO.getGuid()));
        log.info("update 智慧门店更新 sku = {} ,success！", JacksonUtils.writeValueAsString(skuDO));
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public void deleteSkuByGuid(String guid) {
        this.remove(new LambdaQueryWrapper<SkuDO>().eq(SkuDO::getGuid, guid));
        log.info("delete 智慧门店删除 sku = {} ,success！", guid);
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public SkuDO getLocalRecord(Wrapper<SkuDO> queryWrapper) {
        return getOne(queryWrapper);
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public List<SkuDO> shouldInitSkuDOS() {
        return this.list(new LambdaQueryWrapper<>());
    }
}
