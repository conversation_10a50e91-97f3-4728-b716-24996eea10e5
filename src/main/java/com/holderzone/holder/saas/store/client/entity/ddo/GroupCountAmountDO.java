package com.holderzone.holder.saas.store.client.entity.ddo;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GroupCountAmountDO {

    public static GroupCountAmountDO DEFAULT = new GroupCountAmountDO("", BigDecimal.ZERO);
    @ApiModelProperty(value = "时间段")
    private String timeQuantum;
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    public static GroupCountAmountDO INSTANCE() {
        return new GroupCountAmountDO("", BigDecimal.ZERO);
    }
}
