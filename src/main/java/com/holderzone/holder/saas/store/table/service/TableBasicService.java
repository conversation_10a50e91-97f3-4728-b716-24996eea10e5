package com.holderzone.holder.saas.store.table.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.store.table.domain.TableBasicDO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.store.table.StoreAndTableDTO;
import com.holderzone.saas.store.dto.table.PadAreaDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableBatchCreateDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTableCombineDTO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-26
 */
public interface TableBasicService extends IService<TableBasicDO> {

    /**
     * 新增桌台
     *
     * @param tableBasicDTO
     * @return
     */
    String create(TableBasicDTO tableBasicDTO);

    /**
     * 更新桌台
     *
     * @param tableBasicDTO
     * @return
     */
    String update(TableBasicDTO tableBasicDTO);

    /**
     * 批量生成桌位
     *
     * @param tableBatchCreateDTO
     * @return
     */
    String batchCreate(TableBatchCreateDTO tableBatchCreateDTO);

    /**
     * 批量删除
     *
     * @param guids
     * @return
     */
    List<String> batchDelete(List<String> guids);

    /**
     * web前端查询门店桌位信息
     *
     * @param tableBasicQueryDTO
     * @return
     */
    List<TableBasicDTO> listTable(TableBasicQueryDTO tableBasicQueryDTO);

    /**
     * 查询区域下是否有被占用桌台
     *
     * @param guid
     * @return
     */
    boolean isTableOccupied(String guid);

    /**
     * 查询桌台信息
     *
     * @param tableGuid 桌台信息
     * @return 桌台信息
     */
    TableBasicDTO queryTableInfo(String tableGuid);


    /**
     * 查询门店下未绑定的桌台信息
     * @param bindingTableGuids 已经绑定的桌台guid
     * @param storeGuid 门店guid
     * @return
     */
    List<PadAreaDTO> queryUnBindingTableInfo(List<String> bindingTableGuids, String storeGuid);

    /**
     * 根据门店列表查询桌台Guid列表
     *
     * @param singleDataDTO datas必传，门店guid
     * @return 门店和桌台的guid
     */
    List<StoreAndTableDTO> listTableByStoreGuid(SingleDataDTO singleDataDTO);
}
