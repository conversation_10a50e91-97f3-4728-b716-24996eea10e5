package com.holderzone.holder.saas.store.mdm.pipeline.org.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.dynamic.datasource.starter.anno.SwitchServerCodeAnno;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.holder.saas.store.mdm.constant.FieldValutConstants;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.BrandInfoDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.domain.BrandDO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.mapper.BrandMapper;
import com.holderzone.holder.saas.store.mdm.pipeline.org.mapper.StoreBrandMapper;
import com.holderzone.holder.saas.store.mdm.pipeline.org.mapstruct.BrandMapstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 品牌表 服务实现类
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
@SuppressWarnings("unchecked")
@Slf4j
@Service
public class BrandServiceImpl extends ServiceImpl<BrandMapper, BrandDO> implements BrandService {

    private final BrandMapper brandMapper;

    private final BrandMapstruct brandMapstruct;

    private final StoreBrandMapper storeBrandMapper;

    @Autowired
    public BrandServiceImpl(BrandMapper brandMapper,
                            BrandMapstruct brandMapstruct,
                            StoreBrandMapper storeBrandMapper) {
        this.brandMapper = brandMapper;
        this.brandMapstruct = brandMapstruct;
        this.storeBrandMapper = storeBrandMapper;
    }

    @Override
    public void createLocalBrand() {
        List<BrandInfoDTO> brandInfoDTOS = this.list(new LambdaQueryWrapper<>())
                .stream()
                .map(BrandMapstruct.INSTANCE::brandDO2InfoDTO)
                .collect(Collectors.toList());
        brandInfoDTOS.forEach(this::createLocalBrand);
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_organization")
    public boolean createLocalBrand(BrandInfoDTO brandInfoDTO) {
        if (count(new LambdaQueryWrapper<BrandDO>()
                .eq(BrandDO::getGuid, brandInfoDTO.getThirdNo())) > 0) {
            return false;
        }
        BrandDO brandDO = brandMapstruct.brandInfoDTO2DO(brandInfoDTO)
                .setCreateUserGuid(FieldValutConstants.DEFAULT_STAFF_GUID)
                .setModifiedUserGuid(FieldValutConstants.DEFAULT_STAFF_GUID)
                .setGmtCreate(DateTimeUtils.now())
                .setGmtModified(DateTimeUtils.now());
        return this.save(brandDO);
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_organization")
    public boolean updateLocalBrand(BrandInfoDTO brandInfoDTO) {
        BrandDO brandDO = brandMapstruct.brandInfoDTO2DO(brandInfoDTO)
                .setModifiedUserGuid(FieldValutConstants.DEFAULT_STAFF_GUID)
                .setGmtModified(DateTimeUtils.now());

        return this.update(brandDO, new LambdaQueryWrapper<BrandDO>().eq(BrandDO::getGuid, brandDO.getGuid()));
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_organization")
    public void deleteLocalBrand(String brandGuid) {
        this.remove(new LambdaQueryWrapper<BrandDO>().eq(BrandDO::getGuid, brandGuid));
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_organization")
    public List<BrandDO> shouldInitBrandDOS() {
        return list(new LambdaQueryWrapper<>());
    }
}
