package com.holderzone.holder.saas.store.mdm.util;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.slf4j.starter.extension.LogContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.PropertyConfig;
import com.holderzone.holder.saas.store.mdm.constant.ReqTypeConstant;
import com.holderzone.holder.saas.store.mdm.entity.MDMSynDTO;
import com.holderzone.holder.saas.store.mdm.entity.MdmRespResult;
import com.holderzone.holder.saas.store.mdm.exception.RepeatedException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HttpClientUtils
 * @date 2019/11/18 15:29
 * @description
 * @program holder-saas-store
 */
@Component
@Slf4j
public class HttpClientUtils {

    private final RestTemplate restTemplate;

    private final PropertyConfig propertyConfig;

    @Autowired
    private HttpClientUtils(@Qualifier("remoteRestTemplate") RestTemplate restTemplate, PropertyConfig propertyConfig) {
        this.restTemplate = restTemplate;
        this.propertyConfig = propertyConfig;
    }

    public MdmRespResult<Object> doReq(String reqType, MDMSynDTO<?> synDTO, String url) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.put("Content-Type", Collections.singletonList("application/json; charset=utf-8"));
        httpHeaders.add("enterpriseGuid", UserContextUtils.getEnterpriseGuid());
        log.info("{}，发起请求,{}：erpGuid: {}，requestBody: {}", LogContextUtils.getBizName(), url,
                UserContextUtils.getEnterpriseGuid(), JacksonUtils.writeValueAsString(synDTO));
        HttpEntity<MDMSynDTO> multiValueMapHttpEntity = new HttpEntity<>(synDTO, httpHeaders);
        ResponseEntity<MdmRespResult<Object>> responseEntity = restTemplate.exchange(
                URI.create(url),
                ReqTypeConstant.ReqTypeEnum.getMethodByCode(reqType),
                multiValueMapHttpEntity,
                new ParameterizedTypeReference<MdmRespResult<Object>>() {
                });

        boolean statusOk = HttpStatus.OK.equals(responseEntity.getStatusCode());
        if (!statusOk) {
            log.error("{}，返回失败，erpGuid: {}，statusCode: {}，requestBody: {}", LogContextUtils.getBizName(),
                    UserContextUtils.getEnterpriseGuid(),
                    responseEntity.getStatusCodeValue(), JacksonUtils.writeValueAsString(synDTO));
            throw new ServerException("服务器错误：" + responseEntity.getStatusCodeValue());
        }

        boolean businessOk = responseEntity.getBody() != null && "MD000000".equals(responseEntity.getBody().getCode());
        if (!businessOk) {
            log.error("{}，返回失败，erpGuid: {}，responseBody: {}，requestBody: {}",
                    LogContextUtils.getBizName(), UserContextUtils.getEnterpriseGuid(),
                    JacksonUtils.writeValueAsString(responseEntity.getBody()), JacksonUtils.writeValueAsString(synDTO));
            String code = Optional.ofNullable(responseEntity.getBody())
                    .map(MdmRespResult::getCode).orElse(null);
            List<String> mdmRepeatedCode = propertyConfig.getRepeatedCode();
            if (code != null && mdmRepeatedCode != null && mdmRepeatedCode.contains(code)) {
                throw new RepeatedException(code, Optional.ofNullable(responseEntity.getBody())
                        .map(MdmRespResult::getMessage).orElse("响应体缺失"));
            } else {
                throw new BusinessException(Optional.ofNullable(responseEntity.getBody())
                        .map(MdmRespResult::getMessage).orElse("响应体缺失"));
            }
        }

        log.info("{}，返回成功，erpGuid: {}，responseBody: {}", LogContextUtils.getBizName(),
                UserContextUtils.getEnterpriseGuid(), JacksonUtils.writeValueAsString(responseEntity.getBody()));

        return responseEntity.getBody();
    }
}
