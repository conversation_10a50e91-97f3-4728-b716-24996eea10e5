package com.holderzone.holder.saas.store.client.entity.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderOfTheWaterDTO {
    private String orderNo;
    private String gmtCreate;
    private String nickName;
    private String enterpriseName;
    private String type;
    private String state;
    private BigDecimal orderFee;
    private BigDecimal discountsFee;
    private BigDecimal actuallyPayFee;
    private BigDecimal withholdingDistributionFee;
    private String createStaffName;



}
