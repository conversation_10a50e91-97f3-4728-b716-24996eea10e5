package com.holderzone.holder.saas.store.report.mapper;

import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.RefundDetailDTO;
import com.holderzone.saas.store.dto.report.resp.TotalStatisticsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单退款
 */
@Mapper
public interface TradeRefundMapper {

    TotalStatisticsDTO statistics(@Param("query") ReportQueryVO query);

    Integer count(@Param("query") ReportQueryVO query);

    List<RefundDetailDTO> pageInfo(@Param("query") ReportQueryVO query);

}
