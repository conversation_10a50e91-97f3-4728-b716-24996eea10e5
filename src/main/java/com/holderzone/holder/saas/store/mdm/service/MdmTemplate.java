package com.holderzone.holder.saas.store.mdm.service;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.mdm.entity.MDMSynDTO;
import com.holderzone.holder.saas.store.mdm.entity.MdmRespResult;
import com.holderzone.holder.saas.store.mdm.util.CommonUtils;
import com.holderzone.holder.saas.store.mdm.util.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @version 1.0
 * @className MdmOperationImpl
 * @date 2019/11/18 10:11
 * @description
 * @program holder-saas-store
 */
@Slf4j
@Service
public class MdmTemplate implements MdmOperation {

    private final HttpClientUtils httpClientUtils;

    @Value("${mdm.developerId}")
    private String developerId;
    @Value("${mdm.developerKey}")
    private String developerKey;
    @Value("${mdm.reqUrl}")
    private String reqUrl;

    @Autowired
    private MdmTemplate(HttpClientUtils httpClientUtils) {
        this.httpClientUtils = httpClientUtils;
    }

    @Override
    public MdmRespResult<Object> doRequest(String reqType, String reqUrl, Object o) {
        MDMSynDTO<Object> synDTO = getDTO(o);
        return httpClientUtils.doReq(reqType, synDTO, this.reqUrl + reqUrl);
    }


    private MDMSynDTO<Object> getDTO(Object obj) {
        MDMSynDTO<Object> synDTO = new MDMSynDTO<>();
        synDTO.setDeveloperId(developerId);
        synDTO.setRequest(obj);
        String result = CommonUtils.syncSign(synDTO, developerKey);
        log.info("签名结果：{}", result);
        synDTO.setSignature(result);
        return synDTO;
    }


    @Override
    public void checkSign(MDMSynDTO<?> mdmSynDTO) {
        String localSignature = CommonUtils.syncSign(mdmSynDTO, developerKey);
        log.info("request signature:{}, local signature:{}", mdmSynDTO.getSignature(), localSignature);
        if (!mdmSynDTO.getSignature().equalsIgnoreCase(localSignature)) {
            throw new BusinessException("非法请求，验签失败");
        }
    }
}
