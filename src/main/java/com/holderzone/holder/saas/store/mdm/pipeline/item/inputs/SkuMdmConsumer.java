package com.holderzone.holder.saas.store.mdm.pipeline.item.inputs;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.pipeline.item.agg.SkuAggService;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.SkuSyncDTO;
import com.holderzone.holder.saas.store.mdm.event.AbsErpRocketMqConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MainMdmItemListener
 * @date 2019/11/23 下午7:34
 * @description //
 * @program holder
 */

@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.MainConfig.MAIN_MDM_SKU_TOPIC,
        tags = {
                RocketMqConfig.MainConfig.MAIN_MDM_SKU_CREATE_TAG,
                RocketMqConfig.MainConfig.MAIN_MDM_SKU_UPDATE_TAG,
                RocketMqConfig.MainConfig.MAIN_MDM_SKU_DELETE_TAG
        },
        consumerGroup = RocketMqConfig.MainConfig.MAIN_MDM_SKU_GROUP
)
public class SkuMdmConsumer extends AbsErpRocketMqConsumer<RocketMqTopic, String> {

    private final SkuAggService mdmItemService;

    @Autowired
    public SkuMdmConsumer(SkuAggService mdmItemService) {
        this.mdmItemService = mdmItemService;
    }

    @Override
    protected boolean doConsumeMsg(String json, MessageExt messageExt) {
        String tags = messageExt.getTags();
        switch (tags) {
            case RocketMqConfig.MainConfig.MAIN_MDM_SKU_CREATE_TAG:
                mdmItemService.createLocalSku(JacksonUtils.toObject(SkuSyncDTO.class, json));
                break;
            case RocketMqConfig.MainConfig.MAIN_MDM_SKU_UPDATE_TAG:
                mdmItemService.updateLocalSku(JacksonUtils.toObject(SkuSyncDTO.class, json));
                break;
            case RocketMqConfig.MainConfig.MAIN_MDM_SKU_DELETE_TAG:
                mdmItemService.deleteLocalSku(JacksonUtils.toObject(SkuSyncDTO.class, json));
                break;
            default:
                log.error("unknown mq tag : {}, message：{}",
                        messageExt.getTags(),
                        json);
                break;
        }
        return true;
    }
}
