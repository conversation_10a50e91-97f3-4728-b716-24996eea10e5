package com.holderzone.holder.saas.store.report.service;


import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ReturnDetailItemDTO;
import com.holderzone.saas.store.dto.report.resp.ReturnItemDTO;

public interface TradeReturnService {

    Message<ReturnItemDTO> list(ReportQueryVO query);

    Message<ReturnDetailItemDTO> listDetail(ReportQueryVO query);

    String exportReturnDetail(ReportQueryVO query);
}
