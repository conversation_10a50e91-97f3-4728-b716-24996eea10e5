package com.holderzone.holder.saas.store.deposit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.deposit.entity.bo.GoodsDO;
import com.holderzone.holder.saas.store.deposit.mapper.HsdGoodsMapper;
import com.holderzone.holder.saas.store.deposit.mapstruct.GoodsMapstruct;
import com.holderzone.holder.saas.store.deposit.service.DistributedIdService;
import com.holderzone.holder.saas.store.deposit.service.IHsdGoodsService;
import com.holderzone.holder.saas.store.deposit.service.rpc.ItemRpcService;
import com.holderzone.holder.saas.store.deposit.util.PageAdapter;
import com.holderzone.saas.store.dto.deposit.req.DepositQueryReqDTO;
import com.holderzone.saas.store.dto.deposit.req.DepositQueryReqForWebDTO;
import com.holderzone.saas.store.dto.deposit.resp.GoodsSummaryRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-02
 */
@Slf4j
@Service
public class HsdGoodsServiceImpl extends ServiceImpl<HsdGoodsMapper, GoodsDO> implements IHsdGoodsService {

    private final ItemRpcService itemRpcService;

    private final DistributedIdService distributedIdService;

    private final GoodsMapstruct goodsMapstruct;

    @Autowired
    public HsdGoodsServiceImpl(ItemRpcService itemRpcService,
                               DistributedIdService distributedIdService,
                               GoodsMapstruct goodsMapstruct) {
        this.itemRpcService = itemRpcService;
        this.distributedIdService = distributedIdService;
        this.goodsMapstruct = goodsMapstruct;
    }

    @Override
    public Boolean createGoodsItem(GoodsDO goodsDO) {
        return save(goodsDO);
    }

    @Override
    public List<GoodsDO> queryGoodsList(String depositGuid) {
        List<GoodsDO> goodsDOList = list(new LambdaQueryWrapper<GoodsDO>()
                .eq(GoodsDO::getDepositGuid, depositGuid));
        return goodsDOList;
    }

    @Override
    public GoodsDO queryGoodsItem(String depositGuid, String goodsGuid) {
        GoodsDO goodsDO = getOne(new LambdaQueryWrapper<GoodsDO>()
                .eq(GoodsDO::getDepositGuid, depositGuid)
                .eq(GoodsDO::getGuid, goodsGuid));
        return goodsDO;
    }

    @Override
    public Page<GoodsSummaryRespDTO> queryGoodsSummary(DepositQueryReqForWebDTO depositQueryReqDTO) {

        LambdaQueryWrapper wrapper = new LambdaQueryWrapper<GoodsDO>()
                .eq(GoodsDO::getStoreGuid, depositQueryReqDTO.getStoreGuid())
                .select(GoodsDO::getGoodsName)
                .groupBy(GoodsDO::getGoodsName)
                .like(StringUtils.hasText(depositQueryReqDTO.getCondition()), GoodsDO::getGoodsName, depositQueryReqDTO.getCondition());

        IPage<GoodsDO> page = page(new PageAdapter<>(depositQueryReqDTO), wrapper);

        return new PageAdapter<>(page,
                page.getRecords().stream().collect(Collectors.toSet()).stream()
                        .map(goodsDO -> {
                                    GoodsSummaryRespDTO goodsSummaryRespDTO = new GoodsSummaryRespDTO();
                                    goodsSummaryRespDTO.setSum(list(new LambdaQueryWrapper<GoodsDO>().eq(GoodsDO::getGoodsName, goodsDO.getGoodsName())).stream().mapToInt(GoodsDO::getResidueNum).sum());
                                    goodsSummaryRespDTO.setGoodsName(goodsDO.getGoodsName());
                                    return goodsSummaryRespDTO;
                                }
                        ).collect(Collectors.toList()));
    }

    @Override
    public List<GoodsDO> queryExpireGoods(String depositGuid) {
//
//        LambdaQueryWrapper wrapper = new LambdaQueryWrapper<GoodsDO>()
//                .eq(GoodsDO::getDepositGuid, depositGuid)
//                .eq(GoodsDO::getMessageStatus, 0);

        return null;
    }

}
