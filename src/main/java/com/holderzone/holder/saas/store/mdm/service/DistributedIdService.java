package com.holderzone.holder.saas.store.mdm.service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DistributedIdService
 * @date 2018/02/14 09:00
 * @description
 * @program holder-saas-store-print
 */
public interface DistributedIdService {

    Long rawId(String tag);

    String nextId(String tag);

    List<String> nextBatchId(String tag, long count);

    @Deprecated
    String nextUserGuid();

    @Deprecated
    String nextItemGuid();

    @Deprecated
    String nextSkuGuid();

    @Deprecated
    String nextTypeGuid();

    String nextStoreBrandGuid();
}
