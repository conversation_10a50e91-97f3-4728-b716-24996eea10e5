package com.holderzone.holder.saas.store.deposit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.deposit.entity.bo.GoodsDO;
import com.holderzone.saas.store.dto.deposit.req.DepositQueryReqForWebDTO;
import com.holderzone.saas.store.dto.deposit.resp.GoodsSummaryRespDTO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-22
 */
public interface IHsdGoodsService extends IService<GoodsDO> {
    /**
     * 新建商品记录
     *
     * @param goodsDO
     * @return
     */
    Boolean createGoodsItem(GoodsDO goodsDO);

    /**
     * 查询寄存商品
     *
     * @param depositGuid
     * @return
     */
    List<GoodsDO> queryGoodsList(String depositGuid);

    /**
     * 查询指定寄存商品
     *
     * @param depositGuid
     * @return
     */
    GoodsDO queryGoodsItem(String depositGuid,String goodsGuid);

    /**
     * 查询商品汇总信息
     *
     * @param depositQueryReqForWebDTO
     * @return
     */
    Page<GoodsSummaryRespDTO> queryGoodsSummary(DepositQueryReqForWebDTO depositQueryReqForWebDTO);

    /**
     * 查询所有过期的商品
     */
    List<GoodsDO> queryExpireGoods(String depositGuid);
}
