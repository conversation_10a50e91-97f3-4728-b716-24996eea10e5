package com.holderzone.holder.saas.store.message.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MessageDO
 * @date 2018/09/04 16:45
 * @description
 * @program holder-saas-bussiness-message
 */
@Data
public class MessageDO implements Serializable {

    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "消息GUID")
    private String messageGuid;

    @ApiModelProperty(value = "企业GUID")
    private String enterpriseGuid;

    @ApiModelProperty(value = "消息标题")
    private String subject;

    @ApiModelProperty(value = "消息体")
    private String content;

    @ApiModelProperty(value = "推送的消息类型,由推送方决定")
    private Integer messageType;

    @ApiModelProperty(value = "推送消息详细类型")
    private Integer detailMessageType;

    @ApiModelProperty(value = "平台，1:云端平台,2:其他业务平台")
    private String platform;

    @ApiModelProperty(value = "消息级别：1/一般通知, 2/重要通知, 3/紧急通知, 4/强制通知")
    private String messageLevel;

    @ApiModelProperty(value = "门店Guid")
    private String storeGuid;

    @ApiModelProperty(value = "门店名")
    private String storeName;

    @ApiModelProperty(value = "状态 0:未读，1:已读")
    private Integer state;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime pushTime;

}
