package com.holderzone.holder.saas.store.report.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Assert;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.report.constant.ErrorConstant;
import com.holderzone.holder.saas.store.report.helper.ExceptionHelper;
import com.holderzone.holder.saas.store.report.service.ReserveService;
import com.holderzone.saas.store.dto.report.query.ReserveReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ReserveRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 预定相关
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/reserve")
public class ReserveController {

    private final ReserveService reserveService;


    /**
     * 预定单列表
     */
    @PostMapping("/list")
    public Page<ReserveRespDTO> query(@RequestBody ReserveReportQueryVO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return reserveService.page(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "预定明细", JacksonUtils.writeValueAsString(query)));
        }
    }

    /**
     * 预定单导出
     */
    @PostMapping("/export")
    public String export(@RequestBody ReserveReportQueryVO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return reserveService.export(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "导出预定明细", JacksonUtils.writeValueAsString(query)));
        }
    }

}
