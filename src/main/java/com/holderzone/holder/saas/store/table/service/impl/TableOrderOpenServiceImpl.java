package com.holderzone.holder.saas.store.table.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.table.client.TradeRpcClient;
import com.holderzone.holder.saas.store.table.constant.Constant;
import com.holderzone.holder.saas.store.table.domain.BaseDO;
import com.holderzone.holder.saas.store.table.domain.TableBasicDO;
import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.holder.saas.store.table.mapper.TableBasicMapper;
import com.holderzone.holder.saas.store.table.mapper.TableOrderMapper;
import com.holderzone.holder.saas.store.table.service.RedisService;
import com.holderzone.holder.saas.store.table.service.TableBasicService;
import com.holderzone.holder.saas.store.table.service.TableOrderOpenService;
import com.holderzone.holder.saas.store.table.service.TableOrderService;
import com.holderzone.saas.store.dto.order.request.OrderTableBillReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.table.*;
import com.holderzone.saas.store.dto.trade.OrderTableDTO;
import com.holderzone.saas.store.dto.trade.OrderTableGuestDTO;
import com.holderzone.saas.store.dto.trade.OrderTableVO;
import com.holderzone.saas.store.enums.table.TableStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TableOrderOpenServiceImpl implements TableOrderOpenService {

    @Resource
    private TradeRpcClient tradeRpcClient;

    @Resource
    private TableOrderService tableOrderServices;

    @Resource
    private TableOrderMapper tableOrderMapper;

    @Resource
    private TableBasicMapper tableBasicMapper;

    @Resource
    private RedisService redisService;

    /**
     * ORDER_TABLE_KEY
     */
    private static final String ORDER_TABLE_KEY = "hst_order";

    @Override
    public OpenTableOrderDTO openTableByOrderGuid(OpenTableByOrderDTO dto) {
        OpenTableOrderDTO openTableOrderDTO = new OpenTableOrderDTO();
        //根据订单查询桌台信息
        OrderTableDTO orderTableDTO = tradeRpcClient.findTableByOrderGuid(dto.getOrderGuid());
        log.info("orderTableDTO={}", JacksonUtils.writeValueAsString(orderTableDTO));

        if (orderTableDTO.getOrderTableType() == 0) {
            //直接开台
            OpenTableDTO openTableDTO = new OpenTableDTO();
            BeanUtils.copyProperties(dto, openTableDTO);
            openTableDTO.setTableGuid(orderTableDTO.getTableGuidList().get(0));
            openTableDTO.setActualGuestsNo(orderTableDTO.getGuestCount());
            log.info("openTableDTO={}", JacksonUtils.writeValueAsString(openTableDTO));
            String orderGuid = tableOrderServices.open(openTableDTO, 0);
            openTableOrderDTO.setOrderGuid(orderGuid);
            return openTableOrderDTO;
        } else if (orderTableDTO.getOrderTableType() == 1) {
            OpenAssociatedTableDTO openAssociatedTableDTO = new OpenAssociatedTableDTO();
            BeanUtils.copyProperties(dto, openAssociatedTableDTO);
            openAssociatedTableDTO.setTableGuids(orderTableDTO.getTableGuidList());
            openAssociatedTableDTO.setMainTableGuid(orderTableDTO.getTableGuid());
            openAssociatedTableDTO.setActualGuestsNo(orderTableDTO.getGuestCount());
            String orderGuid = tableOrderServices.associatedOpen(openAssociatedTableDTO);
            openTableOrderDTO.setOrderGuid(orderGuid);
            return openTableOrderDTO;
        } else {
            //先开台
            OpenTableDTO openTableDTO = new OpenTableDTO();
            BeanUtils.copyProperties(dto, openTableDTO);
            TableCombineDTO tableCombineDTO = new TableCombineDTO();
            List<String> tableGuids = new ArrayList<>();
            for (OrderTableGuestDTO orderTableGuestDTO : orderTableDTO.getOrderTableGuestDTOS()) {
                openTableDTO.setTableGuid(orderTableGuestDTO.getTableGuid());
                openTableDTO.setActualGuestsNo(orderTableGuestDTO.getGuestCount());
                log.info("并台开台openTableDTO={}", JacksonUtils.writeValueAsString(openTableDTO));
                String orderGuid = tableOrderServices.open(openTableDTO, 0);

                tableGuids.add(orderTableGuestDTO.getTableGuid());

                if (orderTableGuestDTO.getTableGuid().equals(orderTableDTO.getTableGuid())) {
                    tableCombineDTO.setMainOrderGuid(orderGuid);
                }
            }

            tableCombineDTO.setMainTableGuid(orderTableDTO.getTableGuid());
            tableCombineDTO.setTableGuidList(tableGuids);
            List<String> tableGuidList = tableOrderServices.combine(tableCombineDTO, 0);
            openTableOrderDTO.setTableGuidList(tableGuidList);
        }
        return openTableOrderDTO;
    }

    @Override
    public OrderTableVO updateTableOrder(OrderTableDTO orderTableDTO) {
        OrderTableVO orderTableVO = new OrderTableVO();
        List<String> tableGuidList = new ArrayList<>();
        orderTableVO.setOrderTableType(orderTableDTO.getOrderTableType());
        if (orderTableDTO.getOrderTableType() == 2) {
            tableGuidList.addAll(orderTableDTO.getOrderTableGuestDTOS().stream().map(OrderTableGuestDTO::getTableGuid)
                    .collect(Collectors.toList()));
        } else {
            tableGuidList.addAll(orderTableDTO.getTableGuidList());
        }

        if (CollUtil.isEmpty(tableGuidList)) {
            return null;
        }

        //查询桌台基础信息
        List<TableBasicDO> tableBasicDOS = tableBasicMapper.selectList(new LambdaQueryWrapper<TableBasicDO>()
                .in(BaseDO::getGuid, tableGuidList));

        if (CollUtil.isEmpty(tableBasicDOS)) {
            return null;
        }
        List<OrderTableGuestDTO> orderTableGuestDTOS = new ArrayList<>();
        Map<String, TableBasicDO> tableMap = tableBasicDOS.stream().collect(Collectors.toMap(TableBasicDO::getGuid, distanceDish -> distanceDish, (key1, key2) -> key2));
        // 转到的桌台是不是空闲状态
        List<TableOrderDO> tableOrderDOS = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                .in(TableOrderDO::getTableGuid, tableGuidList));
        log.info("tableOrderDOS={}", JacksonUtils.writeValueAsString(tableOrderDOS));

        //确定生成订单数量
        dealOrderTable(orderTableDTO, tableOrderDOS, tableMap, orderTableGuestDTOS, orderTableVO);
        orderTableVO.setOrderTableGuestDTOS(orderTableGuestDTOS);
        return orderTableVO;
    }

    @Override
    public boolean dealClose(OrderTableBillReqDTO orderTableBillReqDTO) {
        log.info("dealClose orderTableBillReqDTO:{}", JacksonUtils.writeValueAsString(orderTableBillReqDTO));
        CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        BeanUtils.copyProperties(orderTableBillReqDTO, cancelOrderReqDTO);
        if (orderTableBillReqDTO.getOrderTableType() == 2) {
            List<String> tableList = orderTableBillReqDTO.getOrderTableGuestDTOS().stream().map(OrderTableGuestDTO::getTableGuid)
                    .collect(Collectors.toList());
            //获取桌台订单
            List<TableOrderDO> tableOrderDOS = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                    .in(TableOrderDO::getTableGuid, tableList));
            Map<String, TableOrderDO> tableOrderDOMap = tableOrderDOS.stream()
                    .collect(Collectors.toMap(TableOrderDO::getTableGuid, Function.identity(), (obj1, obj2) -> obj1));
            for (OrderTableGuestDTO orderTableGuestDTO : orderTableBillReqDTO.getOrderTableGuestDTOS()) {
                cancelOrderReqDTO.setOrderGuid(orderTableGuestDTO.getOrderGuid());
                cancelOrderReqDTO.setTableGuid(orderTableGuestDTO.getTableGuid());
                TableOrderDO tableOrderDO = tableOrderDOMap.get(orderTableGuestDTO.getTableGuid());
                if (StringUtils.isEmpty(tableOrderDO.getOrderGuid())
                        || tableOrderDO.getOrderGuid().equals(orderTableGuestDTO.getOrderGuid())) {
                    boolean isExist = tableOrderServices.close(cancelOrderReqDTO);
                    log.info("并台手动关台结果:{}", isExist);
                }
            }
        } else {
            TableOrderDO tableOrderDO = tableOrderMapper.selectOne(new LambdaQueryWrapper<TableOrderDO>()
                    .eq(TableOrderDO::getTableGuid, orderTableBillReqDTO.getTableGuid()));
            cancelOrderReqDTO.setOrderGuid(orderTableBillReqDTO.getOrderGuid());
            cancelOrderReqDTO.setTableGuid(orderTableBillReqDTO.getTableGuid());
            if (StringUtils.isEmpty(tableOrderDO.getOrderGuid())
                    || tableOrderDO.getOrderGuid().equals(orderTableBillReqDTO.getOrderGuid())) {
                boolean isExist = tableOrderServices.close(cancelOrderReqDTO);
                log.info("手动关台结果:{}", isExist);
            }
        }
        return true;
    }

    private void dealOrderTable(OrderTableDTO orderTableDTO,
                                List<TableOrderDO> tableOrderDOS,
                                Map<String, TableBasicDO> tableMap,
                                List<OrderTableGuestDTO> orderTableGuestDTOS,
                                OrderTableVO orderTableVO) {
        if (orderTableDTO.getOrderTableType() == 2) {
            dealMergeTable(orderTableDTO, tableOrderDOS, tableMap, orderTableGuestDTOS);
        } else {
            dealSingleOrder(orderTableDTO, tableOrderDOS, tableMap, orderTableGuestDTOS, orderTableVO);
        }
    }

    private void dealSingleOrder(OrderTableDTO orderTableDTO, List<TableOrderDO> tableOrderDOS, Map<String, TableBasicDO> tableMap, List<OrderTableGuestDTO> orderTableGuestDTOS, OrderTableVO orderTableVO) {
        String orderGuid = redisService.singleGuid(ORDER_TABLE_KEY);
        orderTableVO.setOrderGuid(orderGuid);
        Set<Integer> subStatus = new HashSet<>();
        subStatus.add(TableStatusEnum.TAKE_UP_WITHOUT_ORDER_DISH.getStatus());
        subStatus.add(TableStatusEnum.TO_BE_CLOSE.getStatus());
        for (TableOrderDO tableOrderDO : tableOrderDOS) {
            OrderTableGuestDTO tableGuestDTO = new OrderTableGuestDTO();
            tableOrderDO.setOrderGuid(orderGuid);
            TableBasicDO tableBasicDO = tableMap.get(tableOrderDO.getTableGuid());
            if (Objects.nonNull(tableBasicDO)) {
                tableGuestDTO.setDiningTableName(tableBasicDO.getTableCode());
                tableGuestDTO.setAreaName(tableBasicDO.getAreaName());
                tableGuestDTO.setAreaGuid(tableBasicDO.getAreaGuid());
            }
            tableGuestDTO.setTableGuid(tableOrderDO.getTableGuid());

            tableGuestDTO.setGuestCount(orderTableDTO.getGuestCount());
            if (orderTableDTO.getOrderTableType() == 1) {
                subStatus.add(TableStatusEnum.TAKE_UP_ASSOCIATED.getStatus());
                tableOrderDO.setMainOrderGuid(orderGuid);
            }

            //主台
            if (orderTableDTO.getTableGuid().equals(tableOrderDO.getTableGuid())) {
                tableGuestDTO.setIsMain(1);
                if (orderTableDTO.getOrderTableType() == 1 && Objects.nonNull(tableBasicDO)) {
                    tableGuestDTO.setAssociatedTimes(redisService.getAssociatedTimes(tableBasicDO.getStoreGuid()));
                }
            } else {
                tableGuestDTO.setIsMain(0);
            }
            if (tableOrderDO.makeSubStatus().contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus())) {
                subStatus.add(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
            }
            tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(subStatus));
            orderTableGuestDTOS.add(tableGuestDTO);

            //修改关联
            int updateResult = tableOrderMapper.update(tableOrderDO, new LambdaQueryWrapper<TableOrderDO>()
                    .eq(TableOrderDO::getTableGuid, tableOrderDO.getTableGuid()));
            log.info("updateResult={}", updateResult);
        }
    }

    private void dealMergeTable(OrderTableDTO orderTableDTO,
                                List<TableOrderDO> tableOrderDOS,
                                Map<String, TableBasicDO> tableMap,
                                List<OrderTableGuestDTO> orderTableGuestDTOS) {
        String mainOrderGuid = redisService.singleGuid(ORDER_TABLE_KEY);
        Map<String, OrderTableGuestDTO> orderTableGuestDTOMap = orderTableDTO.getOrderTableGuestDTOS()
                .stream()
                .collect(Collectors.toMap(OrderTableGuestDTO::getTableGuid, distanceDish -> distanceDish, (key1, key2) -> key2));

        Set<Integer> subStatus = new HashSet<>();
        subStatus.add(TableStatusEnum.TAKE_UP_WITHOUT_ORDER_DISH.getStatus());
        subStatus.add(TableStatusEnum.TAKE_UP_COMBINE.getStatus());
        subStatus.add(TableStatusEnum.TO_BE_CLOSE.getStatus());
        for (TableOrderDO tableOrderDO : tableOrderDOS) {
            OrderTableGuestDTO tableGuestDTO = new OrderTableGuestDTO();
            tableOrderDO.setMainOrderGuid(mainOrderGuid);
            if (tableOrderDO.makeSubStatus().contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus())) {
                subStatus.add(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
            }
            tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(subStatus));
            TableBasicDO tableBasicDO = tableMap.get(tableOrderDO.getTableGuid());
            if (Objects.nonNull(tableBasicDO)) {
                tableGuestDTO.setDiningTableName(tableBasicDO.getTableCode());
                tableGuestDTO.setAreaName(tableBasicDO.getAreaName());
                tableGuestDTO.setAreaGuid(tableBasicDO.getAreaGuid());
            }
            tableGuestDTO.setTableGuid(tableOrderDO.getTableGuid());
            tableGuestDTO.setOrderGuid(tableOrderDO.getOrderGuid());

            tableGuestDTO.setGuestCount(orderTableGuestDTOMap.get(tableOrderDO.getTableGuid()).getGuestCount());

            //主台
            if (orderTableDTO.getTableGuid().equals(tableOrderDO.getTableGuid())) {
                tableGuestDTO.setIsMain(1);
                tableOrderDO.setOrderGuid(mainOrderGuid);
                tableGuestDTO.setOrderGuid(mainOrderGuid);
            } else {
                tableGuestDTO.setIsMain(0);
                String orderGuid = redisService.singleGuid(ORDER_TABLE_KEY);
                tableOrderDO.setOrderGuid(orderGuid);
                tableGuestDTO.setOrderGuid(orderGuid);
            }

            orderTableGuestDTOS.add(tableGuestDTO);

            //修改关联
            int updateResult = tableOrderMapper.update(tableOrderDO, new LambdaQueryWrapper<TableOrderDO>()
                    .eq(TableOrderDO::getTableGuid, tableOrderDO.getTableGuid()));
            log.info("updateResult={}", updateResult);
        }
    }
}
