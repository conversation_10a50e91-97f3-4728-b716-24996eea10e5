package com.holderzone.holder.saas.store.mdm.pipeline.org.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.holder.saas.store.mdm.entity.BaseDO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 品牌表
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
@Data
@Accessors(chain = true)
@TableName(value = "hso_brand")
public class BrandDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id，自增
     */
    @TableId
    private Long id;

    /**
     * 品牌guid
     */
    private String guid;

    /**
     * 品牌名称
     */
    private String name;

    /**
     * 品牌介绍
     */
    private String description;

    /**
     * 品牌logo（oss下载地址）
     */
    private String logoUrl;

    /**
     * 是否启用（默认为1）
     */
    private Boolean isEnable;

    /**
     * 是否删除（默认为0）
     */
    @TableLogic
    private Boolean isDeleted;


    /**
     * 创建人guid
     */
    private String createUserGuid;

    /**
     * 修改人guid
     */
    private String modifiedUserGuid;

    /**
     * 创建时间
     */
    @JsonIgnore
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @JsonIgnore
    private LocalDateTime gmtModified;
}
