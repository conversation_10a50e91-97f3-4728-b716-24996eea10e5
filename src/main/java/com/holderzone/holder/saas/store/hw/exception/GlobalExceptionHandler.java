package com.holderzone.holder.saas.store.hw.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.FeignMsgUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.common.exception.ServerException;
import com.holderzone.saas.store.dto.hw.ResultDTO;
import com.netflix.hystrix.exception.HystrixBadRequestException;
import com.netflix.hystrix.exception.HystrixRuntimeException;
import feign.FeignException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GlobalExceptionHandler
 * @date 2018/12/19 20:48
 * @description 全局异常处理
 * @program holder-saas-store-print
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    public ResultDTO handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("参数错误:{}", message);
        }
        if (StringUtils.isEmpty(message)) {
            return new ResultDTO("500", "参数异常");
        }
        return new ResultDTO("500", message);
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public ResultDTO validatedError(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(fieldError -> String.format("%s:%s", fieldError.getField(), fieldError.getDefaultMessage()))
                .collect(Collectors.joining(","));
        logger.warn("参数校验失败:{}", message);
        return new ResultDTO("500", message.split(":")[1]);
    }

    @ExceptionHandler(value = ParameterException.class)
    public ResultDTO handleParameterException(ParameterException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("参数错误:{}", message);
        }
        if (StringUtils.isEmpty(message)) {
            return new ResultDTO("500", "参数异常");
        }
        return new ResultDTO("500", message);
    }

    @ExceptionHandler(value = HwCodeException.class)
    public ResultDTO businessError(HttpServletRequest request, HwCodeException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("业务异常:{}", message);
        }
        return new ResultDTO(e.getCode(), message);
    }

    @ExceptionHandler(value = BusinessException.class)
    public ResultDTO businessError(HttpServletRequest request, BusinessException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("业务异常:{}", message);
        }
        return new ResultDTO("500", message);
    }

    @ExceptionHandler(value = ServerException.class)
    public ResultDTO serversException(ServerException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("业务异常:{}", message);
        }
        return new ResultDTO("500", message);
    }

    @ExceptionHandler(value = HystrixBadRequestException.class)
    public ResultDTO hystrixBadRequestException(HttpServletRequest request, HystrixBadRequestException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("服务方业务异常：{}", message);
        }
        if (StringUtils.isEmpty(message)) {
            return new ResultDTO("500", "系统异常");
        }
        return new ResultDTO("500", message);
    }

    @ExceptionHandler(value = HystrixRuntimeException.class)
    public ResultDTO hystrixRuntimeException(HttpServletRequest request, HystrixRuntimeException e) {
        String message = e.getCause().getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("服务方系统异常：{}", message);
        }
        message = FeignMsgUtils.parseFeignMsgDetailed(message);
        if (StringUtils.isEmpty(message)) {
            return new ResultDTO("500", "系统异常");
        }
        return new ResultDTO("500", message);
    }

    @ExceptionHandler(value = FeignException.class)
    public ResultDTO feignException(HttpServletRequest request, FeignException e) {
        String message = e.getMessage();
        if (logger.isErrorEnabled()) {
            logger.error("服务方系统异常：{}", message);
        }
        message = FeignMsgUtils.parseFeignMsgDetailed(message);
        if (StringUtils.isEmpty(message)) {
            return new ResultDTO("500", "系统异常");
        }
        return new ResultDTO("500", message);
    }

    /**
     * 拦截所有异常信息
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = Exception.class)
    public ResultDTO exception(Exception e) {
        if (logger.isErrorEnabled()) {
            logger.error("app聚合层系统异常", e);
        }
        return new ResultDTO("500", "系统异常");
    }
}
