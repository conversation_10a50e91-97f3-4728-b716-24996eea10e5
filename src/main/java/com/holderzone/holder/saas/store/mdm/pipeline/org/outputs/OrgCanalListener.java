package com.holderzone.holder.saas.store.mdm.pipeline.org.outputs;

import com.alibaba.otter.canal.protocol.CanalEntry;
import com.holderzone.framework.canal.starter.core.CanalMsg;
import com.holderzone.framework.canal.starter.extension.RowChangeBody;
import com.holderzone.framework.canal.starter.point.CanalListenerHandler;
import com.holderzone.framework.canal.starter.point.anno.ddl.AlertTableListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.DeleteListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.InsertListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.UpdateListenPoint;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.config.SyncConfig;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.DeleteOrgReqDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.OrganizationInfoDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.domain.OrganizationDO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.mapstruct.OrgMapstruct;
import com.holderzone.holder.saas.store.mdm.util.DataConvertUtils;
import com.holderzone.holder.saas.store.mdm.util.ErpUtils;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CanalListenerHandler
@Slf4j
public class OrgCanalListener {

    private final MqUtils mqUtils;

    private final OrgMapstruct orgMapstruct;

    private final SyncConfig syncConfig;

    @Autowired
    public OrgCanalListener(MqUtils mqUtils, OrgMapstruct orgMapstruct, SyncConfig syncConfig) {
        this.mqUtils = mqUtils;
        this.orgMapstruct = orgMapstruct;
        this.syncConfig = syncConfig;
    }

    @LogBefore(value = "内部系统初始化组织", logLevel = LogLevel.INFO)
    @AlertTableListenPoint(schema = "hso_organization_*_db", table = "hso_organization")
    public void onEventAlertTable(CanalMsg canalMsg, CanalEntry.RowChange rowChange) {
        if (syncConfig.shouldInitAgain(rowChange.getSql())) {
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_INIT_TAG,
                    ErpUtils.getErpGuid(canalMsg), ErpUtils.getErpGuid(canalMsg)
            );
        }
    }

    @LogBefore(value = "内部系统创建组织", logLevel = LogLevel.INFO)
    @InsertListenPoint(schema = "hso_organization_*_db", table = "hso_organization")
    public void onEventInsertData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        List<OrganizationInfoDTO> orgInfoList = getAfterDataDTO(rowChangeBody);
        if (CollectionUtils.isEmpty(orgInfoList)) return;

        mqUtils.sendMessage(
                RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_TOPIC,
                RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_CREATE_TAG,
                orgInfoList, ErpUtils.getErpGuid(canalMsg)
        );

        log.info("内部系统创建组织，投递消息内容：{}", JacksonUtils.writeValueAsString(orgInfoList));
    }

    @LogBefore(value = "内部系统修改或删除组织", logLevel = LogLevel.INFO)
    @UpdateListenPoint(schema = "hso_organization_*_db", table = "hso_organization")
    public void onEventUpdateData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        Map<Boolean, List<OrganizationInfoDTO>> orgMap = getAfterDataDtoMap(rowChangeBody);

        // 删除组织
        List<OrganizationInfoDTO> orgToDelete = orgMap.get(true);
        if (null != orgToDelete) {
            orgToDelete.stream()
                    .collect(Collectors.groupingBy(OrganizationInfoDTO::getType))
                    .entrySet().stream()
                    .map(entry -> new DeleteOrgReqDTO(entry.getKey(), entry.getValue()))
                    .forEach(deleteOrgReqDTO -> {
                        mqUtils.sendMessage(
                                RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_TOPIC,
                                RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_DELETE_TAG,
                                deleteOrgReqDTO, ErpUtils.getErpGuid(canalMsg)
                        );
                        log.info("内部系统删除组织，投递消息内容：{}", JacksonUtils.writeValueAsString(deleteOrgReqDTO));
                    });
        }

        // 修改组织
        List<OrganizationInfoDTO> orgToUpdate = orgMap.getOrDefault(false, Collections.emptyList());
        for (OrganizationInfoDTO organizationInfoDTO : orgToUpdate) {
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_UPDATE_TAG,
                    organizationInfoDTO, ErpUtils.getErpGuid(canalMsg)
            );
            log.info("内部系统修改组织，投递消息内容：{}", JacksonUtils.writeValueAsString(organizationInfoDTO));
        }
    }

    @LogBefore(value = "内部系统删除组织", logLevel = LogLevel.INFO)
    @DeleteListenPoint(schema = "hso_organization_*_db", table = "hso_organization")
    public void onEventDeleteData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        List<OrganizationInfoDTO> list = getBeforeDataDTO(rowChangeBody);
        if (CollectionUtils.isEmpty(list)) return;

        List<DeleteOrgReqDTO> deleteOrgReqList = list.stream()
                .collect(Collectors.groupingBy(OrganizationInfoDTO::getType))
                .entrySet().stream()
                .map(entry -> new DeleteOrgReqDTO(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());

        for (DeleteOrgReqDTO deleteOrgReqDTO : deleteOrgReqList) {
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_DELETE_TAG,
                    deleteOrgReqDTO, ErpUtils.getErpGuid(canalMsg)
            );

            log.info("内部系统删除组织，投递消息内容：{}", JacksonUtils.writeValueAsString(deleteOrgReqDTO));
        }
    }

    private List<OrganizationInfoDTO> getAfterDataDTO(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterDataDTO(rowChangeBody, OrganizationDO.class, orgMapstruct::organizationDO2InfoDTO);
    }

    private List<OrganizationDO> getAfterDataOrg(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterData(rowChangeBody, OrganizationDO.class);
    }

    private Map<Boolean, List<OrganizationInfoDTO>> getAfterDataDtoMap(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterDataDtoMap(rowChangeBody,
                OrganizationDO.class, OrganizationDO::getIsDeleted, orgMapstruct::organizationDO2InfoDTO);
    }

    @Deprecated
    private Map<String, List<OrganizationInfoDTO>> getAfterDataDtoMapOfLogic(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterDataDtoMapOfLogic(
                rowChangeBody, OrganizationDO.class, orgMapstruct::organizationDO2InfoDTO);
    }

    private List<OrganizationInfoDTO> getBeforeDataDTO(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getBeforeDataDTO(rowChangeBody, OrganizationDO.class, orgMapstruct::organizationDO2InfoDTO);
    }
}
