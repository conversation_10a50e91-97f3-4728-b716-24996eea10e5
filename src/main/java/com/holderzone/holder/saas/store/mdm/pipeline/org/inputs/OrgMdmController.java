package com.holderzone.holder.saas.store.mdm.pipeline.org.inputs;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.aop.RequireSign;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.constant.ReqUrlConstants;
import com.holderzone.holder.saas.store.mdm.entity.MDMResult;
import com.holderzone.holder.saas.store.mdm.entity.MDMSynDTO;
import com.holderzone.holder.saas.store.mdm.entity.MdmRespResult;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.CommonQueryDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.DeleteOrgReqDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.OrgTypeEnum;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.OrganizationInfoDTO;
import com.holderzone.holder.saas.store.mdm.service.MdmOperation;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import com.holderzone.saas.store.dto.organization.OrganizationDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.holderzone.holder.saas.store.mdm.constant.ReqTypeConstant.POST;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className OrganizationController
 * @date 19-1-3 下午4:50
 * @description Controller-组织
 * @program holder-saas-store-organization
 */
@Slf4j
@RestController
@RequestMapping("/sync_organization")
@Api(description = "组织相关接口")
public class OrgMdmController {

    private final MqUtils mqUtils;

    private final MdmOperation mdmOperation;

    @Autowired
    public OrgMdmController(MqUtils mqUtils, MdmOperation mdmOperation) {
        this.mqUtils = mqUtils;
        this.mdmOperation = mdmOperation;
    }

    /**
     * 创建组织
     *
     * @param mdmOrgSynDTO DTO
     * @return true-成功，false-失败
     */
    @RequireSign
    @PostMapping("/create")
    @ApiOperation("创建组织")
    @LogBefore(value = "外部系统创建组织", logLevel = LogLevel.INFO)
    public MDMResult<String> createOrganization(@Validated(OrganizationInfoDTO.Create.class)
                                                @RequestBody MDMSynDTO<List<OrganizationInfoDTO>> mdmOrgSynDTO) {

        validateFidAndBrand(mdmOrgSynDTO.getRequest());

        for (OrganizationInfoDTO organizationInfoDTO : mdmOrgSynDTO.getRequest()) {
            if (organizationInfoDTO == null) {
                continue;
            }
            if (OrgTypeEnum.ERP.getType().equals(organizationInfoDTO.getType())) {
                continue;
            }
            mqUtils.sendMessage(
                    RocketMqConfig.MainConfig.MAIN_MDM_ORGANIZATION_TOPIC,
                    RocketMqConfig.MainConfig.MAIN_MDM_ORGANIZATION_CREATE_TAG,
                    organizationInfoDTO, UserContextUtils.getEnterpriseGuid()
            );
        }

        return MDMResult.success();
    }

    /**
     * 编辑组织
     *
     * @param mdmOrgSynDTO DTO
     * @return true-成功，false-失败
     */
    @RequireSign
    @PostMapping("/update")
    @ApiOperation("修改组织")
    @LogBefore(value = "外部系统修改组织", logLevel = LogLevel.INFO)
    public MDMResult<String> updateOrganization(@Validated(OrganizationInfoDTO.Update.class)
                                                @RequestBody MDMSynDTO<List<OrganizationInfoDTO>> mdmOrgSynDTO) {

        validateFidAndBrand(mdmOrgSynDTO.getRequest());

        for (OrganizationInfoDTO organizationInfoDTO : mdmOrgSynDTO.getRequest()) {
            if (organizationInfoDTO == null) {
                continue;
            }
            if (OrgTypeEnum.ERP.getType().equals(organizationInfoDTO.getType())) {
                continue;
            }
            mqUtils.sendMessage(
                    RocketMqConfig.MainConfig.MAIN_MDM_ORGANIZATION_TOPIC,
                    RocketMqConfig.MainConfig.MAIN_MDM_ORGANIZATION_UPDATE_TAG,
                    organizationInfoDTO, UserContextUtils.getEnterpriseGuid()
            );
        }

        return MDMResult.success();
    }

    /**
     * 删除组织
     *
     * @param mdmOrgSynDTO guid
     * @return true-成功，false-失败
     */
    @RequireSign
    @PostMapping("/delete")
    @ApiOperation("删除组织")
    @LogBefore(value = "外部系统删除组织", logLevel = LogLevel.INFO)
    public MDMResult<String> deleteOrganization(@Validated(OrganizationInfoDTO.Delete.class)
                                                @RequestBody MDMSynDTO<DeleteOrgReqDTO> mdmOrgSynDTO) {

        DeleteOrgReqDTO request = mdmOrgSynDTO.getRequest();
        for (OrganizationInfoDTO organizationInfoDTO : request.getList()) {
            if (organizationInfoDTO == null) {
                continue;
            }
            organizationInfoDTO.setType(mdmOrgSynDTO.getRequest().getType());
            if (OrgTypeEnum.ERP.getType().equals(organizationInfoDTO.getType())) {
                continue;
            }
            mqUtils.sendMessage(
                    RocketMqConfig.MainConfig.MAIN_MDM_ORGANIZATION_TOPIC,
                    RocketMqConfig.MainConfig.MAIN_MDM_ORGANIZATION_DELETE_TAG,
                    organizationInfoDTO, UserContextUtils.getEnterpriseGuid()
            );
        }

        return MDMResult.success();
    }


    @GetMapping("/query")
    @LogBefore(value = "查询组织", logLevel = LogLevel.INFO)
    public MDMResult<OrganizationDTO> queryOrganization(String thirdNo) {
        CommonQueryDTO commonQueryDTO = new CommonQueryDTO();
        commonQueryDTO.setThirdNo(thirdNo);
        MdmRespResult<Object> mdmRespResult = mdmOperation.doRequest(POST, ReqUrlConstants.QUERY_ORGANIZATION_URL, commonQueryDTO);
        return MDMResult.success(JacksonUtils.toObject(OrganizationDTO.class, JacksonUtils.writeValueAsString(mdmRespResult.getData())));
    }

    private void validateFidAndBrand(List<OrganizationInfoDTO> request) {
        for (OrganizationInfoDTO organizationInfoDTO : request) {
            if (organizationInfoDTO == null) {
                continue;
            }
            if (OrgTypeEnum.ERP.getType().equals(organizationInfoDTO.getType())) {
                continue;
            }
            Assert.notNull(organizationInfoDTO.getFid(), "父级组织不得为空");
            if (OrgTypeEnum.STORE.getType().equals(organizationInfoDTO.getType())) {
                Assert.notNull(organizationInfoDTO.getBrandGuid(), "门店品牌不得为空");
            }
        }
    }
}