package com.holderzone.holder.saas.store.config.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.config.service.HscCommonConfigService;
import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqQueryDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReverseQueryDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ConfigController
 * @date 2019/05/14 17:42
 * @description //TODO 商户配置 控制器
 * @program holder-saas-store-config
 */
@RestController
@RequestMapping("/config")
@Slf4j
public class ConfigController {

    @Autowired
    HscCommonConfigService configService;

    public class Test {
        @NotBlank(message = "名字不得为空")
        private String name;
    }

    @PostMapping("/test")
    public String test(@RequestBody @Valid Test request) {
        return request.name;
    }

    /**
     * 保存、更新门店估清置满时间配置
     *
     * @param request
     * @return
     */
    @PostMapping("/save_config")
    public Integer saveConfig(@RequestBody @Valid ConfigReqDTO request) {
        log.info("门店配置新增or更新接口入参,request={}", JacksonUtils.writeValueAsString(request));
        return configService.saveConfig(request) ? 1 : 0;
    }

    @PostMapping("/delete_config")
    public void deleteConfig(@RequestBody @Valid ConfigReqDTO request) {
        log.info("删除门店配置接口入参,request={}", JacksonUtils.writeValueAsString(request));
        configService.deleteConfig(request);
    }

    /**
     * 根据code获取到门店相关配置
     *
     * @param request
     * @return
     */
    @PostMapping("/get_config_by_code")
    public ConfigRespDTO getConfigByCode(@RequestBody @Valid ConfigReqQueryDTO request) {
        log.info("查询门店配置接口入参,request={}", JacksonUtils.writeValueAsString(request));
        ConfigRespDTO configRespDTO = configService.getConfig(request);
        log.info("result object = {}", JacksonUtils.writeValueAsString(configRespDTO));
        return configRespDTO;
    }

    /**
     *  条件查询配置返回list
     * @param request
     * @return
     */
    @PostMapping("/get_config")
    public List<ConfigRespDTO> getConfig(@RequestBody ConfigReqDTO request) {
        log.info("定时job获取估清置满时间接口入参,request={}", JacksonUtils.writeValueAsString(request));
        List<ConfigRespDTO> configRespDTOS = configService.getConfig(request);
        log.info("定时job获取估清置满时间返回结果,result={}", JacksonUtils.writeValueAsString(configRespDTOS));
        return configRespDTOS;
    }

    @PostMapping("/get_reverse_config")
    public ConfigRespDTO queryReserveConfig(@RequestBody @Valid ConfigReverseQueryDTO request) {
        log.info("查询门店配置接口入参,request={}", JacksonUtils.writeValueAsString(request));
        ConfigRespDTO configRespDTO = configService.getConfigByCodeValue(request);
        log.info("result object = {}", JacksonUtils.writeValueAsString(configRespDTO));
        return configRespDTO;
    }
}
