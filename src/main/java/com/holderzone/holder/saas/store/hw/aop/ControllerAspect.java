package com.holderzone.holder.saas.store.hw.aop;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.holderzone.holder.saas.store.hw.util.TradingUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ControllerAspect
 * @date 2018/10/16 上午10:56
 * @description //controller切面
 * @program holder-saas-aggregation-merchant
 */
//@Aspect
//@Component
@Deprecated
public class ControllerAspect {
    private Logger logger = LoggerFactory.getLogger(ControllerAspect.class);
    private ObjectMapper objectMapper = new ObjectMapper();
    @Value("${secret.key}")
    private String key;

    @Pointcut("execution(* com.holderzone.holder.saas.store.hw.controller.*.*(..))")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        if (!Arrays.stream(args).anyMatch(e -> e instanceof SignatureDTO)) {
            return joinPoint.proceed();
        }
        SignatureDTO signatureDTO = (SignatureDTO) Arrays.stream(args).filter(s -> s instanceof SignatureDTO).findFirst().get();
        String pSignature = signatureDTO.getSignature();
        try {
            String cSignature = TradingUtils.getSignature(signatureDTO,key);
            if (!cSignature.equals(pSignature)) {
                throw new RuntimeException("验签失败");
            }
            return joinPoint.proceed();
        } catch (Throwable throwable) {
            throw new RuntimeException(throwable);
        }
    }

    private Map<String, Object> sort(Map<String, Object> map) {
        List<Map.Entry<String, Object>> list = new ArrayList(map.entrySet());
        list.sort(Comparator.comparing(Map.Entry::getKey,String::compareTo));
        LinkedHashMap<String, Object> linkedHashMap = new LinkedHashMap<>();
        list.forEach((e) -> {
            linkedHashMap.put(e.getKey(), e.getValue());
        });
        return linkedHashMap;
    }
}
