package com.holderzone.holder.saas.store.client.entity.ddo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@ApiModel("今日数据")
@NoArgsConstructor
@Builder
public class TodayDataDO {

    public static TodayDataDO DEFAULT = new TodayDataDO(0, 0, 0, BigDecimal.ZERO, new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
    @ApiModelProperty(value = "访客")
    private Integer visitorCount;
    @ApiModelProperty(value = "成交")
    private Integer dealCount;
    @ApiModelProperty(value = "支付订单数")
    private Integer payOrderCount;
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payAmount;
    @ApiModelProperty(value = "成交人数时段数据")
    private List<GroupCountDO> groupDealCounts;
    @ApiModelProperty(value = "支付订单数时段数据")
    private List<GroupCountDO> groupOrderCounts;
    @ApiModelProperty(value = "支付金额时段数据")
    private List<GroupCountAmountDO> groupAmountCounts;


    public static TodayDataDO INSTANCE() {
        return new TodayDataDO(0, 0, 0, BigDecimal.ZERO, new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
    }
}
