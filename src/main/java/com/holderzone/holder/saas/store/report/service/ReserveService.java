package com.holderzone.holder.saas.store.report.service;


import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.report.query.ReserveReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.*;


/**
 * 预定服务
 */
public interface ReserveService {

    /**
     * 预定明细列表
     */
    Page<ReserveRespDTO> page(ReserveReportQueryVO query);

    /**
     * 导出明细
     */
    String export(ReserveReportQueryVO query);

}
