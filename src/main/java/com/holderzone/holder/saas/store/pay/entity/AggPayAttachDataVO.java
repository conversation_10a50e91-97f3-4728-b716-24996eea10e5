package com.holderzone.holder.saas.store.pay.entity;

import com.holderzone.saas.store.dto.common.BaseDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayAttachDataVO
 * @date 2019/12/03 19:00
 * @description //TODO
 * @program IdeaProjects
 */
@Data
public class AggPayAttachDataVO extends BaseDTO {

    private String saasCallBackUrl;

    private Boolean isQuickReceipt;

    private Boolean isLast;

    /**
     * 是否最后结账
     */
    private Boolean checkoutSuccessFlag;

    /**
     * 支付平台来源
     */
    private String platformSource;
}