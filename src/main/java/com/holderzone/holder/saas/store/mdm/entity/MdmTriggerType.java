/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:MdmTriggerType.java
 * Date:2019-12-29
 * Author:terry
 */

package com.holderzone.holder.saas.store.mdm.entity;

import com.holderzone.framework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2019-12-29 下午7:39
 */
public enum MdmTriggerType {

    CREATE("create"),

    UPDATE("update"),

    ;

    private String type;

    MdmTriggerType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static MdmTriggerType ofType(String type) {
        if (!StringUtils.hasText(type)) {
            return CREATE;
        }
        for (MdmTriggerType value : values()) {
            if (value.type.equalsIgnoreCase(type)) {
                return value;
            }
        }
        return CREATE;
    }
}
