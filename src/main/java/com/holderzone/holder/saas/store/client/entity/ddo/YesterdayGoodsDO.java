package com.holderzone.holder.saas.store.client.entity.ddo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@ApiModel("昨日商品数据")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class YesterdayGoodsDO {


    @ApiModelProperty(value = "总销量")
    private Integer totalSalesCount;
    @ApiModelProperty(value = "点餐分类")
    private List<SalesGoodsCategoryDO> salesGoodsCategories;


    public static YesterdayGoodsDO INSTANCE() {
        return new YesterdayGoodsDO( 0, new ArrayList<>());
    }
}
