package com.holderzone.holder.saas.store.mdm.pipeline.item.outputs;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.event.AbsErpRocketMqConsumer;
import com.holderzone.holder.saas.store.mdm.pipeline.item.agg.TypeAggService;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.TypeSyncDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.StoreConfig.STORE_MDM_TYPE_TOPIC,
        tags = {
                RocketMqConfig.StoreConfig.STORE_MDM_TYPE_INIT_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_TYPE_CREATE_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_TYPE_UPDATE_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_TYPE_DELETE_TAG
        },
        consumerGroup = RocketMqConfig.StoreConfig.STORE_MDM_TYPE_GROUP
)
@SuppressWarnings("unchecked")
public class TypeCanalConsumer extends AbsErpRocketMqConsumer<RocketMqTopic, String> {

    private final TypeAggService typeAggService;

    @Autowired
    public TypeCanalConsumer(TypeAggService typeAggService) {
        this.typeAggService = typeAggService;
    }

    @Override
    public boolean doConsumeMsg(String json, MessageExt messageExt) {
        String tags = messageExt.getTags();
        switch (tags) {
            case RocketMqConfig.StoreConfig.STORE_MDM_TYPE_INIT_TAG:
                typeAggService.triggerRemoteType(MdmTriggerType.ofType(json));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_TYPE_CREATE_TAG:
                typeAggService.createRemoteType(JacksonUtils.toObjectList(TypeSyncDTO.class, json));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_TYPE_UPDATE_TAG:
                typeAggService.updateRemoteType(JacksonUtils.toObject(TypeSyncDTO.class, json));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_TYPE_DELETE_TAG:
                typeAggService.deleteRemoteType(JacksonUtils.toObjectList(TypeSyncDTO.class, json));
                break;
            default:
                log.error("unknown mq tag : {}, message：{}",
                        messageExt.getTags(),
                        JacksonUtils.writeValueAsString(json));
                break;
        }
        return true;
    }
}
