package com.holderzone.holder.saas.store.pay.service.rpc.impl;


import com.holderzone.framework.response.LogicResponse;
import com.holderzone.framework.response.LogicStatus;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.pay.config.AggPayConfig;
import com.holderzone.holder.saas.store.pay.constant.PayConstant;
import com.holderzone.holder.saas.store.pay.entity.AggPayReserveVO;
import com.holderzone.holder.saas.store.pay.service.rpc.AggPayRpcService;
import com.holderzone.saas.store.dto.pay.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.netty.http.client.HttpClient;

import static com.holderzone.holder.saas.store.pay.constant.PayConstant.*;
import static org.springframework.http.HttpMethod.GET;
import static org.springframework.http.HttpMethod.POST;

/**
 * https://docs.spring.io/spring/docs/current/spring-framework-reference/web-reactive.html#webflux-client
 *
 * <AUTHOR>
 * @version 1.0
 * @className AggPayClientServiceImpl
 * @date 2019/03/14 14:43
 * @description
 * @program holder-saas-store-trading-center
 */
@Slf4j
@Service
public class AggPayRpcServiceImpl implements AggPayRpcService {

    private final AggPayConfig aggPayConfig;

    private final RestTemplate remoteRestTemplate;

    private final HttpClient customHttpClient;

    @Autowired
    public AggPayRpcServiceImpl(AggPayConfig aggPayConfig, @Qualifier("remoteRestTemplate") RestTemplate remoteRestTemplate, @Qualifier("customHttpClient")HttpClient customHttpClient) {
        this.aggPayConfig = aggPayConfig;
        this.remoteRestTemplate = remoteRestTemplate;
        this.customHttpClient = customHttpClient;
    }

    @Override
    public AggPayRespDTO preTrading(AggPayPreTradingReqDTO payPreTradingReqDTO) {
        log.info("向聚合支付平台发起预下单请求，请求参数：{}", JacksonUtils.writeValueAsString(payPreTradingReqDTO));
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/json; charset=UTF-8"));
            ResponseEntity<LogicResponse<AggPayRespDTO>> responseEntity = remoteRestTemplate.exchange(
                    aggPayConfig.getPay(), POST,
                    new HttpEntity<>(payPreTradingReqDTO, headers),
                    new ParameterizedTypeReference<LogicResponse<AggPayRespDTO>>() {
                        // to get generic type
                    }
            );
            return resp("预下单", responseEntity.getBody(),
                    AggPayRespDTO.errorPaymentResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL));
        } catch (RestClientException e) {
            log.error("调用聚合支付预下单接口异常：{}", e.getMessage());
            return AggPayRespDTO.errorPaymentResp(PLATFORM_EXCEPTION, "调用聚合支付预下单接口失败");
        }
    }

    @Override
    public Mono<AggPayRespDTO> preTradingAsync(AggPayPreTradingReqDTO aggPayPreTradingReqDTO) {
        return WebClient.create().post()
                .uri(aggPayConfig.getPay())
                .headers(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8))
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .syncBody(aggPayPreTradingReqDTO)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<LogicResponse<AggPayRespDTO>>() {
                })
                .publishOn(Schedulers.elastic())
                .map(logicResponse -> resp("预下单", logicResponse,
                        AggPayRespDTO.errorPaymentResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL)))
                .doOnSubscribe(subscription -> log.info("向聚合支付平台发起预下单请求，请求参数：{}",
                        JacksonUtils.writeValueAsString(aggPayPreTradingReqDTO)));
    }

    @Override
    public AggPayPollingRespDTO doPolling(AggPayPollingDTO pollingJHPayDTO) {
        log.info("向聚合支付平台发起下单轮询请求，请求参数：{}", JacksonUtils.writeValueAsString(pollingJHPayDTO));
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/json; charset=UTF-8"));
            ResponseEntity<LogicResponse<AggPayPollingRespDTO>> responseEntity = remoteRestTemplate.exchange(
                    aggPayConfig.getPolling(), POST,
                    new HttpEntity<>(pollingJHPayDTO, headers),
                    new ParameterizedTypeReference<LogicResponse<AggPayPollingRespDTO>>() {
                        // to get generic type
                    }
            );
            return resp("下单轮询", responseEntity.getBody(),
                    AggPayPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL, pollingJHPayDTO.getAttachData()));
        } catch (RestClientException e) {
            log.error("调用聚合支付下单轮询接口异常：{}", e.getMessage());
            return AggPayPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL, pollingJHPayDTO.getAttachData());
        }
    }

    @Override
    public Mono<AggPayPollingRespDTO> doPollingAsync(AggPayPollingDTO pollingJHPayDTO) {
        return WebClient.create().post()
                .uri(aggPayConfig.getPolling())
                .headers(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8))
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .syncBody(pollingJHPayDTO)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<LogicResponse<AggPayPollingRespDTO>>() {
                })
                .publishOn(Schedulers.elastic())
                .map(logicResponse -> resp("下单轮询", logicResponse,
                        AggPayPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL)))
                .doOnSubscribe(subscription -> log.info("向聚合支付平台发起下单轮询请求，请求参数：{}",
                        JacksonUtils.writeValueAsString(pollingJHPayDTO)));
    }

    @Override
    public AggPayPollingRespDTO prePayQueryBank(AggPayPollingDTO pollingJHPayDTO) {
        log.info(PLACE_ORDER_QUERY_BANK_REQUEST, JacksonUtils.writeValueAsString(pollingJHPayDTO));
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/json; charset=UTF-8"));
            ResponseEntity<LogicResponse<AggPayPollingRespDTO>> responseEntity = remoteRestTemplate.exchange(
                    aggPayConfig.getQuery(), POST,
                    new HttpEntity<>(pollingJHPayDTO, headers),
                    new ParameterizedTypeReference<LogicResponse<AggPayPollingRespDTO>>() {
                        // to get generic type
                    }
            );
            return resp(PLACE_AN_ORDER_TO_CHECK_THE_BANK, responseEntity.getBody(),
                    AggPayPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL, pollingJHPayDTO.getAttachData()));
        } catch (RestClientException e) {
            log.error("调用聚合支付下单查询银行接口异常：{}", e.getMessage());
            return AggPayPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL, pollingJHPayDTO.getAttachData());
        }
    }

    @Override
    public Mono<AggPayPollingRespDTO> prePayQueryBankAsync(AggPayPollingDTO pollingJHPayDTO) {
        return WebClient.create().post()
                .uri(aggPayConfig.getQuery())
                .headers(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8))
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .syncBody(pollingJHPayDTO)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<LogicResponse<AggPayPollingRespDTO>>() {
                })
                .publishOn(Schedulers.elastic())
                .map(logicResponse -> resp(PLACE_AN_ORDER_TO_CHECK_THE_BANK, logicResponse,
                        AggPayPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL)))
                .doOnSubscribe(subscription -> log.info(PLACE_ORDER_QUERY_BANK_REQUEST,
                        JacksonUtils.writeValueAsString(pollingJHPayDTO)));
    }

    @Override
    public Mono<AggPayPollingRespDTO> payPayQueryBankAsync(AggPayPollingDTO pollingJHPayDTO) {
        return WebClient.create().post()
                .uri(aggPayConfig.getQueryPaySt())
                .headers(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8))
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .syncBody(pollingJHPayDTO)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<LogicResponse<AggPayPollingRespDTO>>() {
                })
                .publishOn(Schedulers.elastic())
                .map(logicResponse -> resp(PLACE_AN_ORDER_TO_CHECK_THE_BANK, logicResponse,
                        AggPayPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL)))
                .doOnSubscribe(subscription -> log.info(PLACE_ORDER_QUERY_BANK_REQUEST,
                        JacksonUtils.writeValueAsString(pollingJHPayDTO)));
    }

    @Override
    public AggRefundRespDTO refund(AggRefundReqDTO aggRefundReqDTO) {
        log.info("向聚合支付平台发起退款请求，请求参数：{}", JacksonUtils.writeValueAsString(aggRefundReqDTO));
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/json; charset=UTF-8"));
            ResponseEntity<LogicResponse<AggRefundRespDTO>> responseEntity = remoteRestTemplate.exchange(
                    aggPayConfig.getRefund(), POST,
                    new HttpEntity<>(aggRefundReqDTO, headers),
                    new ParameterizedTypeReference<LogicResponse<AggRefundRespDTO>>() {
                        // to get generic type
                    }
            );
            return resp("退款", responseEntity.getBody(),
                    AggRefundRespDTO.errorPaymentResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL));
        } catch (RestClientException e) {
            log.error("调用聚合支付退款接口异常：{}", e.getMessage());
            return AggRefundRespDTO.errorPaymentResp(PLATFORM_EXCEPTION, "调用聚合支付退款接口失败");
        }
    }

    @Override
    public Mono<AggRefundRespDTO> refundAsync(AggRefundReqDTO aggRefundReqDTO) {
        return WebClient.builder().clientConnector(new ReactorClientHttpConnector(customHttpClient)).build()
                .post()
                .uri(aggPayConfig.getRefund())
                .headers(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8))
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .syncBody(aggRefundReqDTO)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<LogicResponse<AggRefundRespDTO>>() {
                })
                .publishOn(Schedulers.elastic())
                .map(logicResponse -> resp("退款", logicResponse,
                        AggRefundRespDTO.errorPaymentResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL)))
                .doOnSubscribe(subscription -> log.info("向聚合支付平台发起退款请求，请求参数：{}",
                        JacksonUtils.writeValueAsString(aggRefundReqDTO)));
    }

    @Override
    public AggRefundPollingRespDTO doRefundPolling(AggRefundPollingDTO aggRefundPollingDTO) {
        log.info("向聚合支付平台发起退款轮询请求，请求参数：{}", JacksonUtils.writeValueAsString(aggRefundPollingDTO));
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            ResponseEntity<LogicResponse<AggRefundPollingRespDTO>> responseEntity = remoteRestTemplate.exchange(
                    aggPayConfig.getRefundPolling(), POST,
                    new HttpEntity<>(aggRefundPollingDTO, headers),
                    new ParameterizedTypeReference<LogicResponse<AggRefundPollingRespDTO>>() {
                        // to get generic type
                    }
            );
            return resp("退款轮询", responseEntity.getBody(),
                    AggRefundPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL, aggRefundPollingDTO.getAttachData())
            );
        } catch (RestClientException e) {
            log.error("调用聚合支付退款轮询接口异常：{}", e.getMessage());
            return AggRefundPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL, aggRefundPollingDTO.getAttachData());
        }
    }

    @Override
    public Mono<AggRefundPollingRespDTO> doRefundPollingAsync(AggRefundPollingDTO aggRefundPollingDTO) {
        return WebClient.create().post()
                .uri(aggPayConfig.getRefundPolling())
                .headers(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8))
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .syncBody(aggRefundPollingDTO)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<LogicResponse<AggRefundPollingRespDTO>>() {
                })
                .publishOn(Schedulers.elastic())
                .map(logicResponse -> resp("退款轮询", logicResponse,
                        AggRefundPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL)))
                .doOnSubscribe(subscription -> log.info("向聚合支付平台发起退款请求，请求参数：{}",
                        JacksonUtils.writeValueAsString(aggRefundPollingDTO)));
    }

    @Override
    public String weChatPublicAccountPay(AggWeChatPublicAccountPayDTO accountPayDTO) {
        log.info("向聚合支付平台发起微信公众号支付请求，请求参数：{}", JacksonUtils.writeValueAsString(accountPayDTO));
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            ResponseEntity<String> responseEntity = remoteRestTemplate.exchange(
                    aggPayConfig.getWechath5pay(), POST,
                    new HttpEntity<>(accountPayDTO, headers),
                    new ParameterizedTypeReference<String>() {
                        // to get generic type
                    }
            );
            log.info("聚合支付平台返回微信公众号支付结果：{}", responseEntity.getBody());
            JSONObject jsonObject = JacksonUtils.toJSONObject(responseEntity.getBody());
            return (String) jsonObject.get(PayConstant.REDIRECT_URI);
        } catch (Exception e) {
            log.error("调用聚合支付微信公众号支付接口异常：{}", e.getMessage());
            return null;
        }
    }

    @Override
    public Mono<String> weChatPublicAccountPayAsync(AggWeChatPublicAccountPayDTO accountPayDTO) {
        log.info("weChatPublicAccountPayAsync：{}", accountPayDTO);
        return WebClient.create().post()
                .uri(aggPayConfig.getWechath5pay())
                .headers(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8))
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .syncBody(accountPayDTO)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<String>() {
                })
                .publishOn(Schedulers.elastic())
                .map(response -> {
                    log.info("聚合支付平台返回微信公众号支付结果：{}", response);
                    JSONObject jsonObject = JacksonUtils.toJSONObject(response);
                    return (String) jsonObject.get(PayConstant.REDIRECT_URI);
                })
                .doOnSubscribe(subscription -> log.info("向聚合支付平台发起微信公众号支付请求，请求参数：{}",
                        JacksonUtils.writeValueAsString(accountPayDTO))
                );
    }

    @Override
    public AggPayPollingRespDTO doWeChatPublicAccountPolling(String orderGuid) {
        log.info("向聚合支付平台发起微信公众号支付轮询请求，请求参数：orderGuid={}", orderGuid);
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            ResponseEntity<LogicResponse<AggPayPollingRespDTO>> responseEntity = remoteRestTemplate.exchange(aggPayConfig.getWechath5polling() + orderGuid, GET,
                    new HttpEntity<>(headers),
                    new ParameterizedTypeReference<LogicResponse<AggPayPollingRespDTO>>() {
                        // to get generic type
                    });
            return resp("微信公众号支付轮询", responseEntity.getBody(),
                    AggPayPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL));
        } catch (RestClientException e) {
            log.error("调用聚合支付微信公众号支付轮询接口异常：{}", e.getMessage());
            return AggPayPollingRespDTO.errorResp(PLATFORM_EXCEPTION, "轮询异常");
        }
    }

    @Override
    public Mono<AggPayPollingRespDTO> doWeChatPublicAccountPollingAsync(String orderGUID) {
        return WebClient.create().get()
                .uri(aggPayConfig.getWechath5polling() + orderGUID)
                .headers(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8))
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<LogicResponse<AggPayPollingRespDTO>>() {
                })
                .publishOn(Schedulers.elastic())
                .map(logicResponse -> resp("微信公众号支付轮询", logicResponse,
                        AggPayPollingRespDTO.errorResp(PLATFORM_EXCEPTION, AGGREGATE_PAYMENT_PLATFORM_ABNORMAL)))
                .doOnSubscribe(subscription -> log.info(
                        "向聚合支付平台发起微信公众号支付轮询请求，请求参数：{}", orderGUID));
    }

    @Override
    public Mono<AggPayReserveResultDTO> doAggPayReserve(AggPayReserveVO accountPayDTO) {
        return WebClient.create().post()
                .uri(aggPayConfig.getReserve())
                .headers(httpHeaders -> httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8))
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .syncBody(accountPayDTO)
                .accept(MediaType.APPLICATION_JSON_UTF8)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<LogicResponse<AggPayReserveResultDTO>>() {
                })
                .publishOn(Schedulers.elastic())
                .map(response -> {
                    log.info("聚合支付平台返回撤销支付结果：{}", response.getBody());
                    log.info("聚合支付平台url：{}", aggPayConfig.getReserve());
                    return response.getBody();
                });
    }

    private <T> T resp(String msg, LogicResponse<T> logicResponse, T errorResp) {
        if (logicResponse == null) {
            log.error("聚合支付平台返回" + msg + "失败：{}", "logicResponse为空");
            return errorResp;
        }
        if (logicResponse.getStatus() == null) {
            log.error("聚合支付平台返回" + msg + "失败：{}", "logicStatus为空");
            return errorResp;
        }
        if (logicResponse.getStatus().getValue() != LogicStatus.SUCCESS.getValue()) {
            log.error("聚合支付平台返回" + msg + "失败，状态值：{}", JacksonUtils.writeValueAsString(logicResponse.getStatus()));
            return errorResp;
        }
        if (logicResponse.getBody() == null) {
            log.error("聚合支付平台返回" + msg + "失败：{}", "业务实体为空");
            return errorResp;
        }
        log.info("聚合支付平台返回" + msg + "结果：{}", JacksonUtils.writeValueAsString(logicResponse));
        return logicResponse.getBody();
    }
}
