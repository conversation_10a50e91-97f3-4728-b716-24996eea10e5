package com.holderzone.holder.saas.store.deposit.config;

import com.holderzone.holder.saas.store.deposit.interceptor.FakeInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * web app configure
 *
 * <AUTHOR>
 * @date 17-12-5
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new FakeInterceptor()).order(-1);
    }
}
