package com.holderzone.holder.saas.store.table.service.impl;

import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.holder.saas.store.table.service.LocalizeService;
import com.holderzone.holder.saas.store.table.service.TableOrderService;
import com.holderzone.holder.saas.store.table.utils.TableMapStruct;
import com.holderzone.saas.store.dto.table.LocalizeTableOrderReqDTO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/13 15:27
 */
@Service
@AllArgsConstructor
public class LocalizeServiceImpl implements LocalizeService {

    private final TableOrderService tableOrderService;

    @Override
    public boolean uploadLocalData(LocalizeTableOrderReqDTO localizeTableOrderReqDTO) {
        List<TableOrderDO> tableOrderDos = localizeTableOrderReqDTO.getLocalTableOrderList()
                .stream()
                .map(TableMapStruct.TABLE_MAP_STRUCT::localizeTableDTO2TableOrderDO)
                .collect(Collectors.toList());
        return tableOrderService.updateBatchByTableOrderGuid(tableOrderDos);
    }
}