package com.holderzone.holder.saas.store.hw.util;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.hw.exception.DateFormatException;
import com.holderzone.holder.saas.store.hw.exception.GuestNumberException;
import com.holderzone.holder.saas.store.hw.exception.TimeFormatException;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import com.holderzone.saas.store.dto.hw.*;
import com.holderzone.saas.store.reserve.api.dto.VoiceQueryDTO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

public final class RequestMapUtils {

    private RequestMapUtils() {
    }

    public static VoiceQueryDTO of(ConfigRespDTO reserveConfig, ReserveDateDTO reserveDateDTO) {
        VoiceQueryDTO voiceQueryDTO = new VoiceQueryDTO();
        voiceQueryDTO.setStoreGuid(reserveConfig.getStoreGuid());
        assertDateLegal(reserveDateDTO.getReserveDate());
        voiceQueryDTO.setReserveDate(reserveDateDTO.getReserveDate());
        voiceQueryDTO.setPeopleTotal(adaptPeopleTotal(reserveDateDTO.getPeopleTotal()));
        voiceQueryDTO.setRoomType(adaptRoomType(reserveDateDTO.getRoomType()));
        return voiceQueryDTO;
    }

    public static VoiceQueryDTO of(ConfigRespDTO reserveConfig, ReserveIntervalDTO reserveIntervalDTO) {
        VoiceQueryDTO voiceQueryDTO = new VoiceQueryDTO();
        voiceQueryDTO.setStoreGuid(reserveConfig.getStoreGuid());
        assertDateLegal(reserveIntervalDTO.getReserveDate());
        voiceQueryDTO.setReserveDate(reserveIntervalDTO.getReserveDate());
        voiceQueryDTO.setReserveInterval(reserveIntervalDTO.getReserveInterval());
        voiceQueryDTO.setPeopleTotal(adaptPeopleTotal(reserveIntervalDTO.getPeopleTotal()));
        voiceQueryDTO.setRoomType(adaptRoomType(reserveIntervalDTO.getRoomType()));
        return voiceQueryDTO;
    }

    public static VoiceQueryDTO of(ConfigRespDTO reserveConfig, ReserveDateTimeDTO reserveDateTimeDTO) {
        VoiceQueryDTO voiceQueryDTO = new VoiceQueryDTO();
        voiceQueryDTO.setStoreGuid(reserveConfig.getStoreGuid());
        assertDateTimeLegal(
                reserveDateTimeDTO.getReserveDate(),
                reserveDateTimeDTO.getReserveTime()
        );
        voiceQueryDTO.setReserveTime(LocalDateTime.of(
                reserveDateTimeDTO.getReserveDate(),
                reserveDateTimeDTO.getReserveTime()
        ));
        voiceQueryDTO.setPeopleTotal(adaptPeopleTotal(reserveDateTimeDTO.getPeopleTotal()));
        voiceQueryDTO.setRoomType(adaptRoomType(reserveDateTimeDTO.getRoomType()));
        return voiceQueryDTO;
    }

    public static VoiceQueryDTO of(ConfigRespDTO reserveConfig, ReservePeopleDTO reservePeopleDTO) {
        VoiceQueryDTO voiceQueryDTO = new VoiceQueryDTO();
        voiceQueryDTO.setStoreGuid(reserveConfig.getStoreGuid());
        assertDateTimeLegal(
                reservePeopleDTO.getReserveDate(),
                reservePeopleDTO.getReserveTime()
        );
        voiceQueryDTO.setReserveTime(LocalDateTime.of(
                reservePeopleDTO.getReserveDate(),
                reservePeopleDTO.getReserveTime()
        ));
        voiceQueryDTO.setPeopleTotal(reservePeopleDTO.getPeopleTotal());
        voiceQueryDTO.setRoomType(adaptRoomType(reservePeopleDTO.getRoomType()));
        return voiceQueryDTO;
    }

    public static VoiceQueryDTO of(ConfigRespDTO reserveConfig, ReserveRoomDTO reserveRoomDTO) {
        LocalDate reserveDate = adaptReserveDate(reserveRoomDTO.getReserveDate());
        Integer reserveInterval = adaptReserveInterval(reserveRoomDTO.getReserveInterval());
        LocalTime reserveTime = adaptReserveTime(reserveRoomDTO.getReserveTime());
        LocalDateTime reserveDateTime;
        if (reserveDate == null) {
            reserveInterval = null;
            reserveDateTime = null;
        } else {
            assertDateLegal(reserveDate);
            if (reserveTime != null) {
                reserveDateTime = LocalDateTime.of(reserveDate, reserveTime);
                assertDateTimeLegal(reserveDate, reserveTime);
            } else {
                reserveDateTime = null;
            }
        }
        VoiceQueryDTO voiceQueryDTO = new VoiceQueryDTO();
        voiceQueryDTO.setStoreGuid(reserveConfig.getStoreGuid());
        voiceQueryDTO.setReserveDate(reserveDate);
        voiceQueryDTO.setReserveInterval(reserveInterval);
        voiceQueryDTO.setReserveTime(reserveDateTime);
        voiceQueryDTO.setPeopleTotal(adaptPeopleTotal(reserveRoomDTO.getPeopleTotal()));
        return voiceQueryDTO;
    }

    public static VoiceQueryDTO of(ConfigRespDTO reserveConfig, ReserveAllDTO reserveAllDTO) {
        VoiceQueryDTO voiceQueryDTO = new VoiceQueryDTO();
        voiceQueryDTO.setStoreGuid(reserveConfig.getStoreGuid());
        assertDateTimeLegal(
                reserveAllDTO.getReserveDate(),
                reserveAllDTO.getReserveTime()
        );
        voiceQueryDTO.setReserveTime(LocalDateTime.of(
                reserveAllDTO.getReserveDate(),
                reserveAllDTO.getReserveTime()
        ));
        voiceQueryDTO.setPeopleTotal(reserveAllDTO.getPeopleTotal());
        voiceQueryDTO.setRoomType(Optional.ofNullable(reserveAllDTO.getRoomType()).orElse(0));
        return voiceQueryDTO;
    }

    public static VoiceQueryDTO of(ConfigRespDTO reserveConfig, ReserveLaunchDTO reserveLaunchDTO) {
        VoiceQueryDTO voiceQueryDTO = new VoiceQueryDTO();
        voiceQueryDTO.setStoreGuid(reserveConfig.getStoreGuid());
        assertDateTimeLegal(
                reserveLaunchDTO.getReserveDate(),
                reserveLaunchDTO.getReserveTime()
        );
        voiceQueryDTO.setReserveTime(LocalDateTime.of(
                reserveLaunchDTO.getReserveDate(),
                reserveLaunchDTO.getReserveTime()
        ));
        voiceQueryDTO.setPeopleTotal(reserveLaunchDTO.getPeopleTotal());
        voiceQueryDTO.setRoomType(Optional.ofNullable(reserveLaunchDTO.getRoomType()).orElse(0));
        return voiceQueryDTO;
    }

    private static final String EMPTY_JSON = "{}";

    private static DateTimeFormatter DATE = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static DateTimeFormatter TIME = DateTimeFormatter.ofPattern("HH:mm");

    private static boolean isSourceNull(String str) {
        if (!StringUtils.hasText(str)) {
            return true;
        }
        return EMPTY_JSON.equals(str);
    }

    private static LocalDate adaptReserveDate(String reserveDate) {
        if (isSourceNull(reserveDate)) {
            return null;
        }
        try {
            return LocalDate.parse(reserveDate, DATE);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static void assertDateLegal(LocalDate reserveDate) {
        if (reserveDate.compareTo(LocalDate.now()) < 0) {
            throw new DateFormatException();
        }
    }

    private static void assertDateTimeLegal(LocalDate reserveDate, LocalTime reserveTime) {
        assertDateLegal(reserveDate);
        if (reserveDate.compareTo(LocalDate.now()) == 0 && reserveTime.compareTo(LocalTime.now()) < 0) {
            throw new TimeFormatException();
        }
    }

    private static Integer adaptReserveInterval(String reserveInterval) {
        if (isSourceNull(reserveInterval)) {
            return null;
        }
        try {
            int value = Integer.parseInt(reserveInterval);
            if (value < 0 || value > 1) {
                throw new BusinessException("预定区间：0=中午，1=晚上");
            }
            return value;
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static LocalTime adaptReserveTime(String reserveTime) {
        if (isSourceNull(reserveTime)) {
            return null;
        }
        try {
            return LocalTime.parse(reserveTime, TIME);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static Integer adaptPeopleTotal(String peopleTotal) {
        if (isSourceNull(peopleTotal)) {
            return null;
        }
        try {
            int value = Integer.parseInt(peopleTotal);
            if (value < 0 || value > 99) {
                throw new GuestNumberException();
            }
            return value;
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static Integer adaptRoomType(String roomType) {
        if (isSourceNull(roomType)) {
            return null;
        }
        try {
            int value = Integer.parseInt(roomType);
            if (value < 0 || value > 1) {
                throw new BusinessException("桌台类型：0=大厅，1=包房");
            }
            return value;
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return null;
    }
}
