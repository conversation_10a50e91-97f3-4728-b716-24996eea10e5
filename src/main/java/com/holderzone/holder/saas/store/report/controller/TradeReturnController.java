package com.holderzone.holder.saas.store.report.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Assert;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.report.constant.ErrorConstant;
import com.holderzone.holder.saas.store.report.helper.ExceptionHelper;
import com.holderzone.holder.saas.store.report.service.TradeReturnService;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ReturnDetailItemDTO;
import com.holderzone.saas.store.dto.report.resp.ReturnItemDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 退货报表
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/trade/return")
public class TradeReturnController {

    private final TradeReturnService tradeReturnService;

    /**
     * 退货商品列表 及 合计
     */
    @PostMapping("/list")
    public Message<ReturnItemDTO> list(@RequestBody ReportQueryVO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeReturnService.list(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "退货商品列表", JacksonUtils.writeValueAsString(query)));
        }
    }

    /**
     * 退货商品列表明细及合计
     */
    @PostMapping("/list_detail")
    public Message<ReturnDetailItemDTO> listDetail(@RequestBody ReportQueryVO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeReturnService.listDetail(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "退菜明细列表", JacksonUtils.writeValueAsString(query)));
        }
    }

    @PostMapping("/detail_export")
    public String detailExport(@RequestBody ReportQueryVO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeReturnService.exportReturnDetail(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "导出退菜明细", JacksonUtils.writeValueAsString(query)));
        }
    }
}
