package com.holderzone.holder.saas.store.table.client;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.store.store.BindupAccountsDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemDiscountClientService
 * @date 2018/09/10 16:15
 * @description
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = BindupAccountsClientService.BindUpAccountsClientServiceFallBack.class)
public interface BindupAccountsClientService {

    /**
     * 保存扎帐信息
     *
     * @param storeGuid
     * @param userGuid
     * @param userName
     * @return
     */
    @PostMapping("/bindupAccount/save")
    BindupAccountsDTO saveBindUpAccounts(@RequestParam("storeGuid") String storeGuid,
                                         @RequestParam("userGuid") String userGuid,
                                         @RequestParam("userName") String userName,
                                         @RequestParam("buAccounts") LocalDate buAccounts);

    /**
     * 获取门店的最新扎帐时间
     *
     * @param storeGuid 门店guid
     * @return
     */
    @PostMapping("/bindupAccount/query/last")
    BindupAccountsDTO queryBindUpAccountsLast(@RequestParam("storeGuid") String storeGuid);

    /**
     * 发送mq信息，提示能够开台
     *
     * @param storeGuid
     */
    @GetMapping("/sendmq/canopentable")
    void sendMqForCanOpenTable(@RequestParam("storeGuid") String storeGuid);

    @Component
    class BindUpAccountsClientServiceFallBack implements FallbackFactory<BindupAccountsClientService> {

        private static final Logger logger = LoggerFactory.getLogger(BindUpAccountsClientServiceFallBack.class);

        @Override
        public BindupAccountsClientService create(Throwable throwable) {
            return new BindupAccountsClientService() {
                @Override
                public BindupAccountsDTO saveBindUpAccounts(String storeGuid, String userGuid, String userName, LocalDate buAccounts) {
                    logger.error("保存扎帐信息 e={}", throwable.getMessage());
                    throw new BusinessException("保存扎帐信息" + throwable.getMessage());
                }

                @Override
                public BindupAccountsDTO queryBindUpAccountsLast(String storeGuid) {
                    logger.error("获取门店的最新扎帐时间 e={}", throwable.getMessage());
                    throw new BusinessException("获取门店的最新扎帐时间" + throwable.getMessage());
                }

                @Override
                public void sendMqForCanOpenTable(String storeGuid) {
                    logger.error("提示mq信息，能够开台操作 e={}", throwable.getMessage());
                    throw new BusinessException("提示mq信息，能够开台操作" + throwable.getMessage());
                }
            };
        }

    }


}
