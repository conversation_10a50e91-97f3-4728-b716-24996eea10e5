package com.holderzone.holder.saas.store.mdm.pipeline.org.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.UserSyncDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
@Accessors(chain = true)
@JsonPropertyOrder(alphabetic = true)
public class BrandInfoDTO {

    /**
     * 主键
     */
    @JsonProperty("guid")
    @ApiModelProperty(value = "MDM生成的唯一标识", required = false)
    private String uuid;

    /**
     * 第三方唯一标识
     */
    @NotBlank(message = "第三方标识不得为空", groups = {Create.class, Update.class, Delete.class})
    private String thirdNo;

    /**
     * 品牌名称
     */
    @NotBlank(message = "品牌名称不得为空", groups = {Create.class, Update.class})
    private String name;

    /**
     * 品牌介绍
     */
    @Nullable
    private String description;

    /**
     * 图标
     */
    @Nullable
    private String logo;

    /**
     * 上级组织(默认为企业)
     */
    private String organizationGuid;

    public interface Create extends Default {

    }

    public interface Update extends Default {

    }

    public interface Delete extends Default {

    }
}
