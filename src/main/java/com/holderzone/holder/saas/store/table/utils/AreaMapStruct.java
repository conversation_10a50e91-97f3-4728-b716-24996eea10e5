package com.holderzone.holder.saas.store.table.utils;

import com.holderzone.holder.saas.store.table.domain.AreaDO;
import com.holderzone.holder.saas.store.table.domain.bo.AreaBO;
import com.holderzone.saas.store.dto.table.AreaDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/12/27 11:10
 */
@Mapper
public interface AreaMapStruct {

    AreaMapStruct AREA_MAP_STRUCT = Mappers.getMapper(AreaMapStruct.class);

    AreaDO areaBo2Do(AreaBO areaBo);

    @Mappings({
            @Mapping(source = "gmtCreate", target = "gmtCreate", ignore = true),
            @Mapping(source = "gmtModified", target = "gmtModified", ignore = true),
    })
    AreaDO areaDto2Do(AreaDTO areaDto);


    List<AreaDTO> areaDoList2DtoList(List<AreaDO> areaDos);

    AreaDTO areaDo2Dto(AreaDO areaDO);
}
