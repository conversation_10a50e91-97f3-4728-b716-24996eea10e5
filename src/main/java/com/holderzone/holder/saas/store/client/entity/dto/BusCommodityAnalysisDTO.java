package com.holderzone.holder.saas.store.client.entity.dto;

import com.holderzone.holder.saas.store.client.entity.ddo.CommodityAnalysisItemDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


@Data
@ApiModel("报表-经营数据-商品分析响应实体")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusCommodityAnalysisDTO {

    public static BusCommodityAnalysisDTO DEFAULT = new BusCommodityAnalysisDTO(0,BigDecimal.ZERO,BigDecimal.ZERO,new ArrayList<>());
    @ApiModelProperty(value = "销售总量")
    private Integer totalSalesVolume;
    @ApiModelProperty(value = "销售总额")
    private BigDecimal totalSalesAmount;
    @ApiModelProperty(value = "毛利")
    private BigDecimal grossProfitRate;
    @ApiModelProperty(value = "详情列表")
    private List<CommodityAnalysisItemDO> commodityAnalysisItemDO;
    public static BusCommodityAnalysisDTO INSTANCE() {
        return new BusCommodityAnalysisDTO(0,BigDecimal.ZERO,BigDecimal.ZERO, new ArrayList<>());
    }
}
