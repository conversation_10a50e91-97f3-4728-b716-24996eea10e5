package com.holderzone.holder.saas.store.table.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/12/26 10:41
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("桌台DO")
@TableName("hst_table_basic")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TableBasicDO extends BaseDO {

    private static final long serialVersionUID = 4164971730144889827L;

    /**
     * 区域guid
     */
    private String areaGuid;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 桌台编号
     */
    private String tableCode;

    /**
     * 桌台座位数
     */
    private Integer seats;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否已启用
     * 0=已启用
     * 1=未启用
     * 默认启用
     */
    private Integer enable = 0;

    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    @TableLogic
    private Integer deleted;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;



}
