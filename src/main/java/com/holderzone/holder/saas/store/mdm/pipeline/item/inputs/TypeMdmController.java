package com.holderzone.holder.saas.store.mdm.pipeline.item.inputs;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MDMResult;
import com.holderzone.holder.saas.store.mdm.entity.MDMSynDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.TypeSyncDTO;
import com.holderzone.holder.saas.store.mdm.service.MdmOperation;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MdmItemController
 * @date 2019/11/23 下午7:43
 * @description //
 * @program holder-saas-store-mdm
 */

@RestController
@RequestMapping("/sync_type")
public class TypeMdmController {


    private final MqUtils mqUtils;

    public TypeMdmController(MqUtils mqUtils) {
        this.mqUtils = mqUtils;
    }

    @LogBefore(value = "外部系统批量创建分类", logLevel = LogLevel.INFO)
    @PostMapping("/create")
    public MDMResult<String> typeCreateBatchSync(@Validated(TypeSyncDTO.Create.class)
                                              @RequestBody MDMSynDTO<List<TypeSyncDTO>> mdmSynDTO) {
        for (TypeSyncDTO typeSyncDTO : mdmSynDTO.getRequest()) {
            if (typeSyncDTO != null) {
                mqUtils.sendMessage(
                        RocketMqConfig.MainConfig.MAIN_MDM_TYPE_TOPIC,
                        RocketMqConfig.MainConfig.MAIN_MDM_TYPE_CREATE_TAG,
                        typeSyncDTO, UserContextUtils.getEnterpriseGuid()
                );
            }
        }
        return MDMResult.success();
    }

    @LogBefore(value = "外部系统批量更新分类", logLevel = LogLevel.INFO)
    @PostMapping("/update")
    public MDMResult<String> typeUpdateSync(@Validated(TypeSyncDTO.Update.class)
                                         @RequestBody MDMSynDTO<List<TypeSyncDTO>> mdmSynDTO) {
        for (TypeSyncDTO typeSyncDTO : mdmSynDTO.getRequest()) {
            if (typeSyncDTO != null) {
                mqUtils.sendMessage(
                        RocketMqConfig.MainConfig.MAIN_MDM_TYPE_TOPIC,
                        RocketMqConfig.MainConfig.MAIN_MDM_TYPE_UPDATE_TAG,
                        typeSyncDTO, UserContextUtils.getEnterpriseGuid()
                );
            }
        }
        return MDMResult.success();
    }

    @LogBefore(value = "外部系统批量删除分类", logLevel = LogLevel.INFO)
    @PostMapping("/delete")
    public MDMResult<String> typeDeleteSync(@Validated(TypeSyncDTO.Delete.class)
                                         @RequestBody MDMSynDTO<List<TypeSyncDTO>> mdmSynDTO) {
        for (TypeSyncDTO typeSyncDTO : mdmSynDTO.getRequest()) {
            if (typeSyncDTO != null) {
                mqUtils.sendMessage(
                        RocketMqConfig.MainConfig.MAIN_MDM_TYPE_TOPIC,
                        RocketMqConfig.MainConfig.MAIN_MDM_TYPE_DELETE_TAG,
                        typeSyncDTO, UserContextUtils.getEnterpriseGuid()
                );
            }
        }

        return MDMResult.success();
    }

}
