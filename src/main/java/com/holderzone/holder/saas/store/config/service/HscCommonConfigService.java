package com.holderzone.holder.saas.store.config.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.store.config.entity.domain.HscCommonConfigDO;
import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqQueryDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReverseQueryDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;

import java.util.List;

/**
 * <p>
 * 商户配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-08
 */
public interface HscCommonConfigService extends IService<HscCommonConfigDO> {


    /**
     * 配置估清置满时间
     *
     * @param request
     * @return
     */
    Boolean saveConfig(ConfigReqDTO request);

    /**
     * 获取配置详情
     *
     * @param request
     * @return
     */
    ConfigRespDTO getConfig(ConfigReqQueryDTO request);

    void deleteConfig(ConfigReqDTO request);

    /**
     * 定时job获取估清置满时间
     *
     * @param request
     * @return
     */
    List<ConfigRespDTO> getConfig(ConfigReqDTO request);

    /**
     *
     * @param request
     * @return
     */
    ConfigRespDTO getConfigByCodeValue(ConfigReverseQueryDTO request);
}
