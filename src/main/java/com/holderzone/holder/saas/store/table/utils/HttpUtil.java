package com.holderzone.holder.saas.store.table.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description http工具类
 * @date 2022/3/15 18:27
 * @className: HttpUtil
 */
@Slf4j
public class HttpUtil {

    private HttpUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * HTTP post請求
     *
     * @param url        路径
     * @param jsonString 请求json字符串
     * @return String
     * @throws IOException IO异常
     */
    public static String doPostJson(String url, String jsonString) throws IOException {
        log.info("httpRequest:---> request-url:{},requestBody:{}", url, jsonString);
        HttpClient httpClient = HttpClientBuilder.create().build();
        StringEntity stringEntity = new StringEntity(jsonString, "utf-8");
        stringEntity.setContentType("application/json");
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(stringEntity);
        ResponseHandler<String> responseHandler = new BasicResponseHandler();
        String execute = httpClient.execute(httpPost, responseHandler);
        log.info("httppostresult：{}", execute);
        return execute;
    }

    /**
     * HTTP post請求
     *
     * @param url        路径
     * @param jsonString 请求json字符串
     * @param headerMap  要设置的头部信息
     * @return String
     * @throws IOException IO异常
     */
    public static String doPostJsonHeader(String url, String jsonString, Map<String, String> headerMap) throws IOException {
        log.info("httpRequest:---> request-url:{},requestBody:{} headerMap={}", url, jsonString, headerMap);
        HttpClient httpClient = HttpClientBuilder.create().build();
        StringEntity stringEntity = new StringEntity(jsonString, "utf-8");
        stringEntity.setContentType("application/json");
        HttpPost httpPost = new HttpPost(url);
        httpPost.setEntity(stringEntity);
        for (Map.Entry<String, String> entry : headerMap.entrySet()) {
            String name = entry.getKey();
            String value = entry.getValue();
            httpPost.setHeader(name, value);
        }
        ResponseHandler<String> responseHandler = new BasicResponseHandler();
        String execute = httpClient.execute(httpPost, responseHandler);
        log.info("httppostresult：{}", execute);
        return execute;
    }

    /**
     * 发送 GET 请求（HTTP），不带输入数据
     *
     * @param url 请求地址
     * @return 返回json结果
     */
    public static String doGet(String url) throws IOException, URISyntaxException {
        return doGet(url, new HashMap<String, Object>(), new HashMap<String, String>());
    }

    /**
     * 发送 GET 请求（HTTP），K-V形式
     *
     * @param url    请求地址
     * @param params 请求参数，K-V形式
     * @return 返回json结果
     */
    public static String doGet(String url, Map<String, Object> params, Map<String, String> header)
            throws IOException, URISyntaxException {
        log.info("httpRequest:---> requestUrl={},requestBody={} headerMap={}", url, params, header);
        URIBuilder uriBuilder = new URIBuilder(url);
        for (Map.Entry<String, Object> key : params.entrySet()) {
            uriBuilder.setParameter(key.getKey(), key.getValue().toString());
        }

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(uriBuilder.build());
        for (Map.Entry<String, String> entry : header.entrySet()) {
            String name = entry.getKey();
            String value = entry.getValue();
            httpGet.setHeader(name, value);
        }
        ResponseHandler<String> responseHandler = new BasicResponseHandler();
        String result = httpClient.execute(httpGet, responseHandler);
        log.info("http get result={}", result);
        return result;
    }
}