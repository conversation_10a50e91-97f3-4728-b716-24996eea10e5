package com.holderzone.holder.saas.store.config.utils;

import com.holderzone.holder.saas.store.config.entity.domain.HscCommonConfigDO;
import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MapStructUtils
 * @date 2019/01/19 下午3:28
 * @description //TODO
 * @program holder-saas-store-item
 */
@Mapper
public interface MapStructUtils {

    MapStructUtils INSTANCE = Mappers.getMapper(MapStructUtils.class);

    HscCommonConfigDO configReqDTO2CommonConfigDO(ConfigReqDTO configReqDTO);

    ConfigRespDTO commonConfigDO2configRespDTO(HscCommonConfigDO configDO);

    List<ConfigRespDTO> commonConfigDO2configRespDTOs(List<HscCommonConfigDO> configDOS);
}
