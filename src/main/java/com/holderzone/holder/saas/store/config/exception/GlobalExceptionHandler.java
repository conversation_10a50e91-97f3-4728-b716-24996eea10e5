package com.holderzone.holder.saas.store.config.exception;

import com.holderzone.feign.spring.boot.exception.ExceptionHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRocketListener
 * @date 2018/11/15 12:00
 * @description
 * @program holder-saas-store-takeaway
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler extends ExceptionHandlerAdapter {

}
