package com.holderzone.holder.saas.store.table.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/01/21 11:15
 */
@Component
@ConfigurationProperties(prefix = "delay-release-table-lock")
public class DelayReleaseLockConfig {

    private Integer level;

    private Long time;

    private Long size;

    private Long wxTime;

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public Long getWxTime() {
        return wxTime;
    }

    public void setWxTime(Long wxTime) {
        this.wxTime = wxTime;
    }
}
