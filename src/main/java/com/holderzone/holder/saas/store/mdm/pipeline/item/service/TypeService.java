package com.holderzone.holder.saas.store.mdm.pipeline.item.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.TypeSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.TypeDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeService
 * @date 2019/11/23 下午6:41
 * @description //
 * @program holder
 */

public interface TypeService extends IService<TypeDO> {
    /**
     * mdm推送本地同步创建分类
     *
     * @param typeSyncDTO
     * @return
     */
    void insertType(TypeSyncDTO typeSyncDTO);

    /**
     * mdm推送本地同步更新分类
     *
     * @param typeSyncDTO
     * @return
     */
    void updateTypeByGuid(TypeSyncDTO typeSyncDTO);

    /**
     * mdm推送本地同步删除分类
     *
     * @param guid
     * @return
     */
    void deleteTypeByGuid(String guid);


    TypeDO getLocalRecord(Wrapper<TypeDO> queryWrapper);

    List<TypeDO> shouldInitTypeDOS();

}