/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:TriggerLogUtils.java
 * Date:2019-12-19
 * Author:terry
 */

package com.holderzone.holder.saas.store.mdm.util;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.holder.saas.store.mdm.exception.RepeatedException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2019-12-19 下午3:05
 */
@Slf4j
public class TriggerLogUtils {

    public static void pre(String name, long size) {
        log.info("| {} |     需要同步{}条{}数据：", UserContextUtils.getEnterpriseGuid(), name, size);
    }

    public static void process(String name, long size) {
        log.info("| {} |     {}条{}数据同步完成；", UserContextUtils.getEnterpriseGuid(), name, size);
    }

    public static void post(String name, long size) {
        log.info("| {} |     所有{}条{}数据同步完成！", UserContextUtils.getEnterpriseGuid(), name, size);
    }

    public static void stepFailed(String name, Exception e) {
        log.warn("| {} |     步长创建{}发生错误：message={}，即将批量重试！！！",
                UserContextUtils.getEnterpriseGuid(), name, e.getMessage());
    }

    public static void batchFailed(String name, Exception e) {
        log.warn("| {} |     批量创建{}发生错误：message={}，即将逐个重试！！！",
                UserContextUtils.getEnterpriseGuid(), name, e.getMessage());
    }

    public static void singleFailed(String name, Exception e) {
        log.warn("| {} |     单个更新{}发生错误：message={}，即将重试！！！",
                UserContextUtils.getEnterpriseGuid(), name, e.getMessage());
    }

    public static void batchRepeated(String name, RepeatedException e) {
        log.warn("| {} |     批量创建{}发生重复数据错误：code={}，message={}，即将逐个重试！！！",
                UserContextUtils.getEnterpriseGuid(), name, e.getCode(), e.getMessage());
    }

    public static void singleRepeated(String name) {
        log.warn("| {} |     单个创建{}发生重复数据错误，忽略该条数据！！！", UserContextUtils.getEnterpriseGuid(), name);
    }
}
