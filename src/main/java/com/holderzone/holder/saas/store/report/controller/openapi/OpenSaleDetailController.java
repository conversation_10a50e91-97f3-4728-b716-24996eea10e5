package com.holderzone.holder.saas.store.report.controller.openapi;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.report.service.OpenSaleDetailService;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailLimitRespDTO;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailQueryDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 销售明细
 */
@Slf4j
@RestController
@RequestMapping("/openapi/sale_detail")
@RequiredArgsConstructor
public class OpenSaleDetailController {

    private final OpenSaleDetailService openSaleDetailService;

    /**
     * 查询销售明细
     */
    @PostMapping
    public SaleDetailLimitRespDTO query(@RequestBody SaleDetailQueryDTO saleDetailQueryDTO) {
        log.info("销售明细报表请求入参：{}", JacksonUtils.writeValueAsString(saleDetailQueryDTO));
        return openSaleDetailService.query(saleDetailQueryDTO);
    }
}
