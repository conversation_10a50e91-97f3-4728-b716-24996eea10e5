package com.holderzone.holder.saas.store.pay.event;


import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.pay.config.AggPayConfig;
import com.holderzone.holder.saas.store.pay.config.RocketMqConfig;
import com.holderzone.holder.saas.store.pay.constant.PayConstant;
import com.holderzone.holder.saas.store.pay.entity.HandlerPayBO;
import com.holderzone.holder.saas.store.pay.service.PollingService;
import com.holderzone.saas.store.dto.pay.AggPayPollingDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PollingJHPayRocket
 * @date 2018/08/30 19:36
 * @description 轮询聚合支付队列监听
 * @program holder-saas-store-trading-center
 */
@Component
@RocketListenerHandler(topic = RocketMqConfig.JH_PAY_POLLING_TOPIC,
        tags = RocketMqConfig.JH_PAY_POLLING_TAG,
        consumerGroup = RocketMqConfig.JH_PAY_POLLING_CONSUMER_GROUP,
        consumeThreadMin = 16,
        consumeThreadMax = 64)
@Slf4j
public class PollingListener extends AbstractRocketMqConsumer<RocketMqTopic, AggPayPollingDTO> {

    private final AggPayConfig aggPayConfig;

    private final PollingService pollingService;

    @Autowired
    public PollingListener(AggPayConfig aggPayConfig, PollingService pollingService) {
        this.aggPayConfig = aggPayConfig;
        this.pollingService = pollingService;
    }

    @Override
    public boolean consumeMsg(AggPayPollingDTO pollingJHPayDTO, MessageExt messageExt) {
        Map<String, String> properties = messageExt.getProperties();
        String handlerPayJson = properties.get(PayConstant.HANDLER_PAY);
        HandlerPayBO handlerPayBO = JacksonUtils.toObject(HandlerPayBO.class, handlerPayJson);
        if (handlerPayBO.getTimes() > aggPayConfig.getPollingTimes()) {
            // 投递时已做了上限次数边界控制，此处理论上执行不到
            log.info("轮询超过默认次数 pollingTimes={}", handlerPayBO.getTimes());
            return true;
        }
        try {
            return pollingService.handlePollingResult(pollingJHPayDTO, handlerPayBO, true);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
