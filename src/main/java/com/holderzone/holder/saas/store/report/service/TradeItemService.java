package com.holderzone.holder.saas.store.report.service;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.journaling.req.SalesVolumeReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeStoreRespDTO;
import com.holderzone.saas.store.dto.report.query.GoodsSalesVO;
import com.holderzone.saas.store.dto.report.query.SalesDetailQO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesDTO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesStatisticDTO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesTotalDTO;
import com.holderzone.saas.store.dto.report.resp.SalesDetailRespDTO;

import java.util.List;

public interface TradeItemService {

    GoodsSalesStatisticDTO queryItemTypeStatistics(GoodsSalesVO query);

    GoodsSalesStatisticDTO queryGroupItemSaleStatistics(GoodsSalesVO query);

    Page<SalesVolumeRespDTO> pageStoreSaleStatistics(SalesVolumeReqDTO query);

    Page<SalesVolumeStoreRespDTO> pageGroupByStoreSaleStatistics(SalesVolumeReqDTO query);

    GoodsSalesTotalDTO storeSaleStatisticsTotal(SalesVolumeReqDTO query);

    String exportItemTypeStatistics(GoodsSalesVO query);

    String exportGroupItemSaleStatistics(GoodsSalesVO query);

    List<String> pageStoreSaleStatisticsType(SalesVolumeReqDTO query);

    Page<SalesDetailRespDTO> pageSaleDetail(SalesDetailQO query);

    String exportSaleDetail(SalesDetailQO query);

    List<GoodsSalesDTO> pageStoreSaleStatisticsGroup(SalesVolumeReqDTO query);
}
