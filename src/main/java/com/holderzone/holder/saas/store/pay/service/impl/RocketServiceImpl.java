package com.holderzone.holder.saas.store.pay.service.impl;


import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.pay.config.AggPayConfig;
import com.holderzone.holder.saas.store.pay.config.RocketMqConfig;
import com.holderzone.holder.saas.store.pay.constant.PayConstant;
import com.holderzone.holder.saas.store.pay.entity.HandlerPayBO;
import com.holderzone.holder.saas.store.pay.service.RocketService;
import com.holderzone.saas.store.dto.pay.AggPayPollingDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RocketServiceImpl
 * @date 2019/03/14 15:06
 * @description
 * @program holder-saas-store-trading-center
 */
@Slf4j
@Service
public class RocketServiceImpl implements RocketService {

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    private final AggPayConfig aggPayConfig;

    @Autowired
    public RocketServiceImpl(DefaultRocketMqProducer defaultRocketMqProducer, AggPayConfig aggPayConfig) {
        this.defaultRocketMqProducer = defaultRocketMqProducer;
        this.aggPayConfig = aggPayConfig;
    }

    @Override
    public boolean polling(AggPayPollingDTO pollingJHPayDTO, HandlerPayBO handlerPayBO) {
        int curDeliveryTimes = handlerPayBO.getTimes() + 1;
        if (curDeliveryTimes > aggPayConfig.getPollingTimes()) {
            log.warn("当前投递次数：{}，超过上限：{}，停止投递", curDeliveryTimes, aggPayConfig.getPollingTimes());
            return true;
        }
        log.info("当前投递次数：{}，上限：{}", curDeliveryTimes, aggPayConfig.getPollingTimes());
        handlerPayBO.setTimes(curDeliveryTimes);
        Message message = new Message(
                RocketMqConfig.JH_PAY_POLLING_TOPIC,
                RocketMqConfig.JH_PAY_POLLING_TAG,
                JacksonUtils.toJsonByte(pollingJHPayDTO)
        );
        message.setDelayTimeLevel(1);
        message.getProperties().put(PayConstant.HANDLER_PAY, JacksonUtils.writeValueAsString(handlerPayBO));
        return defaultRocketMqProducer.sendMessage(message);
    }
}
