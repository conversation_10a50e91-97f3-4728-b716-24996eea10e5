package com.holderzone.holder.saas.store.table.utils;

import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.saas.store.dto.weixin.WxStoreTableCombineDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/5/17
 */
@Mapper
public interface WxStoreTableCombineMapStruct {

	WxStoreTableCombineMapStruct WX_STORE_TABLE_COMBINE_MAP_STRUCT = Mappers.getMapper(WxStoreTableCombineMapStruct.class);

	WxStoreTableCombineDTO getWxStoreTableCombine(TableOrderDO tableOrderDO);

	List<WxStoreTableCombineDTO> getAllWxStoreTableCombine(List<TableOrderDO> tableDos);
}
