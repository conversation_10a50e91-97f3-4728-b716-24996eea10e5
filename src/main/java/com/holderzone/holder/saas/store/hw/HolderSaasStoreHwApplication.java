package com.holderzone.holder.saas.store.hw;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 * @version 1.0
 * @className Application
 * @date 2019/05/16 11:04
 * @description //TODO
 * @program holder-saas-store-hw
 */
@EnableSwagger2
@EnableEurekaClient
@ComponentScan(basePackages = "com.holderzone")
@EnableFeignClients(basePackages = "com.holderzone")
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class})
public class HolderSaasStoreHwApplication {

    public static void main(String[] args) throws Exception {
        SpringApplication.run(HolderSaasStoreHwApplication.class,args);
    }

}