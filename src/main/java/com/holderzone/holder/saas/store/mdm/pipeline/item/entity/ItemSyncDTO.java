package com.holderzone.holder.saas.store.mdm.pipeline.item.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemSyncDTO
 * @date 2019/11/29 上午10:54
 * @description //mdm商品同步DTO
 * @program holder
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("mdm商品同步DTO")
@JsonPropertyOrder(alphabetic = true)
public class ItemSyncDTO {

    /**
     * 主键
     */
    @JsonProperty("guid")
    @ApiModelProperty(value = "MDM生成的唯一标识", required = false)
    private String uuid;

    /**
     * 第三方唯一标识
     */
    @JsonProperty("thirdNo")
    @NotBlank(message = "第三方标识不得为空", groups = {Create.class, Update.class, Delete.class})
    @ApiModelProperty(value = "第三方唯一标识", required = true)
    private String guid;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称", groups = {Create.class, Update.class})
    @ApiModelProperty(value = "第三方唯一标识", required = true)
    private String name;

    /**
     * 商品关联的分类GUID
     */
    @NotBlank(message = "商品关联的分类GUID", groups = {Create.class, Update.class})
    @ApiModelProperty(value = "商品关联的分类GUID", required = true)
    private String typeGuid;

    /**
     * 门店GUID
     */
    @Nullable
    @ApiModelProperty(value = "门店GUID", required = false)
    private String storeGuid;

    /**
     * 品牌GUID
     */
    @Nullable
    @ApiModelProperty(value = "品牌GUID", required = false)
    private String brandGuid;

    /**
     * 商品来源（0：门店，1：品牌）
     */
    @NotNull(message = "商品来源（0：门店，1：品牌）", groups = {Create.class, Update.class})
    @ApiModelProperty(value = "商品来源（0：门店，1：品牌）", required = true)
    private Integer itemFrom;

    /**
     * 商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品。
     */
    @NotNull(message = "商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品。", groups = {Create.class, Update.class})
    @ApiModelProperty(value = "商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品。", required = true)
    private Integer itemType;

    /**
     * 拼音简码
     */
    @NotBlank(message = "拼音简码", groups = {Create.class, Update.class})
    @ApiModelProperty(value = "品牌GUID", required = false)
    private String pinyin;

    /**
     * 图片路径数组json
     */
    @ApiModelProperty(value = "图片路径数组json", required = false)
    private String pictureUrl;

    /**
     *  是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Integer isEnable;

    /**
     * 父实体GUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的实体，则该字段为品牌库对应的实体GUID。
     */
    @ApiModelProperty(value = "父实体GUID")
    protected String parentGuid;

    public interface Create extends Default {

    }

    public interface Update extends Default {

    }

    public interface Delete extends Default {

    }
}
