package com.holderzone.holder.saas.store.table.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.table.domain.TableTagDO;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.table.TableTagDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/04 16:32
 */
public interface TableTagService extends IService<TableTagDO> {
    boolean createTag(TableTagDTO tableTagDTO);

    Page<TableTagDTO> listTag(BasePageDTO basePageDTO);

    boolean updateTag(TableTagDTO tableTagDTO);

    boolean deleteTag(TableTagDTO tableTagDTO);

    Map<String,TableTagDTO> getTagWithGuidMap(List<String> tableTagGuidList);
}
