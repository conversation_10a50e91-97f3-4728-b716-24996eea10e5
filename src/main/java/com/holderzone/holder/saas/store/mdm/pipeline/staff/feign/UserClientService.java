package com.holderzone.holder.saas.store.mdm.pipeline.staff.feign;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UserClientService
 * @date 2019/11/26 17:27
 * @description
 * @program holder-saas-store
 */
@Component
@FeignClient(value = "holder-saas-store-staff", fallbackFactory = UserClientService.ServiceClientFallback.class)
public interface UserClientService {

    @PostMapping("/user/delete_down_stream/{userGuid}")
    boolean deleteUserDownStreamOp(@PathVariable("userGuid") String userGuid);

    @Component
    @Slf4j
    class ServiceClientFallback implements FallbackFactory<UserClientService> {

        @Override
        public UserClientService create(Throwable throwable) {
            return new UserClientService() {

                @Override
                public boolean deleteUserDownStreamOp(String userGuid) {
                    return false;
                }
            };
        }
    }
}
