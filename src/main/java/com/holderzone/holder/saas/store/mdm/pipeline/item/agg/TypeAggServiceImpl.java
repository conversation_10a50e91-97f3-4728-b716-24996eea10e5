package com.holderzone.holder.saas.store.mdm.pipeline.item.agg;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.exception.RepeatedException;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.TypeSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.TypeDO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.mapstruct.ItemMapStruct;
import com.holderzone.holder.saas.store.mdm.pipeline.item.service.TypeService;
import com.holderzone.holder.saas.store.mdm.service.MdmOperation;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import com.holderzone.holder.saas.store.mdm.util.SplitUtils;
import com.holderzone.holder.saas.store.mdm.util.TriggerLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.holderzone.holder.saas.store.mdm.constant.ReqTypeConstant.*;
import static com.holderzone.holder.saas.store.mdm.constant.ReqUrlConstants.Type.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MdmTypeServiceImpl
 * @date 2019/11/23 下午4:39
 * @description //
 * @program holder
 */
@Slf4j
@Service
public class TypeAggServiceImpl implements TypeAggService {

    private final TypeService typeService;

    private final MdmOperation mdmOperation;

    private final MqUtils mqUtils;

    @Value("${mdm.initSyncStep:50}")
    private int initSyncStep;

    @Autowired
    public TypeAggServiceImpl(TypeService typeService, MdmOperation mdmOperation, MqUtils mqUtils) {
        this.typeService = typeService;
        this.mdmOperation = mdmOperation;
        this.mqUtils = mqUtils;
    }

    @Override
    public void triggerRemoteType(MdmTriggerType mdmTriggerType) {
        List<TypeSyncDTO> typeSyncDTOList = typeService.shouldInitTypeDOS()
                .stream()
                .filter(typeDO -> typeDO.getIsDelete() == 0)
                .map(ItemMapStruct.INSTANCE::typeDO2typeSynDTO)
                .collect(Collectors.toList());

        TriggerLogUtils.pre("分类", typeSyncDTOList.size());

        switch (mdmTriggerType) {
            case CREATE:
                SplitUtils.splitList(typeSyncDTOList, initSyncStep).forEach(typeSyncDTOS -> {
                    try {
                        createRemoteType(typeSyncDTOS);
                    } catch (Exception e) {
                        TriggerLogUtils.stepFailed("分类", e);
                        mqUtils.sendMessage(
                                RocketMqConfig.StoreConfig.STORE_MDM_TYPE_TOPIC,
                                RocketMqConfig.StoreConfig.STORE_MDM_TYPE_CREATE_TAG,
                                typeSyncDTOS, UserContextUtils.getEnterpriseGuid()
                        );
                    }
                    TriggerLogUtils.process("分类", typeSyncDTOS.size());
                });
                break;
            case UPDATE:
                typeSyncDTOList.forEach(typeSync -> {
                    try {
                        updateRemoteType(typeSync);
                    } catch (Exception e) {
                        TriggerLogUtils.singleFailed("分类", e);
                        mqUtils.sendMessage(
                                RocketMqConfig.StoreConfig.STORE_MDM_TYPE_TOPIC,
                                RocketMqConfig.StoreConfig.STORE_MDM_TYPE_UPDATE_TAG,
                                typeSync, UserContextUtils.getEnterpriseGuid()
                        );
                    }
                });
                TriggerLogUtils.process("分类", typeSyncDTOList.size());
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + mdmTriggerType);
        }

        TriggerLogUtils.post("分类", typeSyncDTOList.size());
    }

    @Override
    public void createRemoteType(List<TypeSyncDTO> typeSyncDTOList) {
        try {
            mdmOperation.doRequest(POST, BATCH_CREATE_TYPE_URL, typeSyncDTOList);
        } catch (RepeatedException e) {
            if (typeSyncDTOList.size() == 1) {
                TriggerLogUtils.singleRepeated("分类");
            } else {
                TriggerLogUtils.batchRepeated("分类", e);
                for (TypeSyncDTO typeSyncDTO : typeSyncDTOList) {
                    mqUtils.sendMessage(
                            RocketMqConfig.StoreConfig.STORE_MDM_TYPE_TOPIC,
                            RocketMqConfig.StoreConfig.STORE_MDM_TYPE_CREATE_TAG,
                            Collections.singleton(typeSyncDTO), UserContextUtils.getEnterpriseGuid()
                    );
                }
            }
        } catch (BusinessException e) {
            if (typeSyncDTOList.size() == 1) {
                throw e;
            } else {
                TriggerLogUtils.batchFailed("分类", e);
                for (TypeSyncDTO typeSyncDTO : typeSyncDTOList) {
                    mqUtils.sendMessage(
                            RocketMqConfig.StoreConfig.STORE_MDM_TYPE_TOPIC,
                            RocketMqConfig.StoreConfig.STORE_MDM_TYPE_CREATE_TAG,
                            Collections.singleton(typeSyncDTO), UserContextUtils.getEnterpriseGuid()
                    );
                }
            }
        }
    }

    @Override
    public void updateRemoteType(TypeSyncDTO typeSyncDTO) {
        mdmOperation.doRequest(PUT, UPDATE_TYPE_URL, typeSyncDTO);
    }

    @Override
    public void deleteRemoteType(List<TypeSyncDTO> typeSyncDTOList) {
        mdmOperation.doRequest(DELETE, BATCH_DELETE_TYPE_URL, typeSyncDTOList);
    }

    @Override
    public void createLocalType(TypeSyncDTO typeSyncDTO) {
        TypeDO dbType = typeService.getLocalRecord(new LambdaQueryWrapper<TypeDO>().eq(TypeDO::getGuid, typeSyncDTO.getGuid()));
        if (Objects.nonNull(dbType)) {
            log.info("第三方创建分类信息同步，分类数据已存在");
            log.info("第三方数据：{}", JacksonUtils.writeValueAsString(typeSyncDTO));
            log.info("本地DB数据：{}", JacksonUtils.writeValueAsString(dbType));
            return;
        }
        typeService.insertType(typeSyncDTO);
    }

    @Override
    public void updateLocalType(TypeSyncDTO typeSyncDTO) {
        TypeDO dbType = typeService.getLocalRecord(new LambdaQueryWrapper<TypeDO>().eq(TypeDO::getGuid, typeSyncDTO.getGuid()));
        if (Objects.isNull(dbType)) {
            log.info("第三方修改分类信息同步，分类信息不存在，第三方数据：{}", JacksonUtils.writeValueAsString(typeSyncDTO));
            typeService.insertType(typeSyncDTO);
            return;
        }
        typeService.updateTypeByGuid(typeSyncDTO);
    }

    @Override
    public void deleteLocalType(TypeSyncDTO typeSyncDTO) {
        TypeDO dbType = typeService.getLocalRecord(new LambdaQueryWrapper<TypeDO>().eq(TypeDO::getGuid, typeSyncDTO.getGuid()));
        if (Objects.isNull(dbType)) {
            log.info("第三方删除分类信息同步，分类信息不存在，第三方数据：{}", JacksonUtils.writeValueAsString(typeSyncDTO));
            return;
        }
        typeService.deleteTypeByGuid(typeSyncDTO.getGuid());
    }
}
