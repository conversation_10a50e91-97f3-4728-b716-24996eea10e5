package com.holderzone.holder.saas.store.table.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.table.domain.TableBasicDO;
import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.holder.saas.store.table.mapper.TableBasicMapper;
import com.holderzone.holder.saas.store.table.service.RedisService;
import com.holderzone.holder.saas.store.table.service.TableBasicService;
import com.holderzone.holder.saas.store.table.service.TableOrderService;
import com.holderzone.holder.saas.store.table.service.TableTagRelationService;
import com.holderzone.holder.saas.store.table.utils.ThrowableExtUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.store.table.StoreAndTableDTO;
import com.holderzone.saas.store.dto.table.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.holderzone.holder.saas.store.table.constant.Constant.*;
import static com.holderzone.holder.saas.store.table.utils.TableMapStruct.TABLE_MAP_STRUCT;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableBasicServiceImpl
 * @date 2019/01/02 16:47
 * @description
 * @program holder-saas-store-table
 */
@Slf4j
@Service
public class TableBasicServiceImpl extends ServiceImpl<TableBasicMapper, TableBasicDO> implements TableBasicService {

    private final TableBasicMapper tableBasicMapper;

    private final TableOrderService tableOrderService;

    private final RedisService redisService;

    private final TableTagRelationService tableTagRelationService;

    @Autowired
    public TableBasicServiceImpl(TableBasicMapper tableBasicMapper, TableOrderService tableOrderService,
                                 RedisService redisService, TableTagRelationService tableTagRelationService) {
        this.tableBasicMapper = tableBasicMapper;
        this.tableOrderService = tableOrderService;
        this.redisService = redisService;
        this.tableTagRelationService = tableTagRelationService;
    }

    /**
     * 验证桌号是否重复
     *
     * @param storeGuid
     * @param areaGuid
     * @param tableCode
     * @param oriTableCode
     */
    private void assertTableNameAvailable(String storeGuid, String areaGuid, String tableCode, String oriTableCode) {
        if (!Objects.equals(oriTableCode, tableCode)) {
            Integer count = tableBasicMapper.selectCount(new LambdaQueryWrapper<TableBasicDO>()
                    .eq(TableBasicDO::getStoreGuid, storeGuid)
                    .eq(TableBasicDO::getAreaGuid, areaGuid)
                    .eq(TableBasicDO::getTableCode, tableCode)
            );
            if (null != count && count > 0) {
                throw new BusinessException("当前区域下已存在该桌台");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(TableBasicDTO tableBasicDTO) {
        assertTableNameAvailable(tableBasicDTO.getStoreGuid(), tableBasicDTO.getAreaGuid(),
                tableBasicDTO.getTableCode(), null);
        try {
            // 桌台
            TableBasicDO tableBasicDO = TABLE_MAP_STRUCT.tableBasicDto2Do(tableBasicDTO);
            tableBasicDO.setGuid(redisService.singleGuid(TABLE_BASIC));
            tableBasicDO.setDeleted(0);
            tableBasicDO.setEnable(0);
            tableBasicMapper.insert(tableBasicDO);
            // 桌台订单
            TableOrderDO tableOrderDO = TABLE_MAP_STRUCT.tableBasicDo2OrderDo(tableBasicDO);
            tableOrderDO.setGuid(redisService.singleGuid(TABLE_ORDER));
            if (null == tableBasicDTO.getSort()) {
                Integer maxSort = tableBasicMapper.maxSort(
                        tableBasicDTO.getStoreGuid(), tableBasicDTO.getAreaGuid());
                tableBasicDO.setSort(maxSort + 1);
            }
            tableOrderService.save(tableOrderDO);
            //桌台标签
            tableTagRelationService.createTags(tableBasicDTO.getTagList(), tableBasicDO.getGuid());
        } catch (Exception e) {
            log.error("新增桌台失败：{}", ThrowableExtUtils.asStringIfAbsent(e));
            throw new BusinessException("新增桌台失败！");
        }
        return SUCCESS;
    }

    @Override
    public String update(TableBasicDTO tableBasicDTO) {
        TableBasicDO tableInSql = tableBasicMapper.selectOne(new LambdaQueryWrapper<TableBasicDO>()
                .eq(TableBasicDO::getGuid, tableBasicDTO.getGuid()));
        assertTableNameAvailable(tableBasicDTO.getStoreGuid(), tableBasicDTO.getAreaGuid(),
                tableBasicDTO.getTableCode(), tableInSql.getTableCode());
        try {
            TableBasicDO tableBasicDO = TABLE_MAP_STRUCT.tableBasicDto2Do(tableBasicDTO);
            tableBasicMapper.update(tableBasicDO, new LambdaQueryWrapper<TableBasicDO>()
                    .eq(TableBasicDO::getGuid, tableBasicDTO.getGuid()));
            //标签处理
            List<String> tagList = tableBasicDTO.getTagList();
            if (tagList == null) {
                //如果标签没做任何修改 直接不传
                return SUCCESS;
            }
            tableTagRelationService.removeTagByTableIds(tableBasicDTO.getGuid());
            tableTagRelationService.createTags(tagList, tableBasicDTO.getGuid());
        } catch (Exception e) {
            log.error("更新桌台失败：{}", ThrowableExtUtils.asStringIfAbsent(e));
            throw new BusinessException("更新桌台失败！");
        }
        return SUCCESS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> batchDelete(List<String> guids) {
        // 非空闲状态的桌位,不能删除的桌位
        List<String> tableOccupied = tableOrderService.listTableOccupied(guids);
        guids.removeAll(tableOccupied);
        // 删除空闲的桌位
        if (!guids.isEmpty()) {
            tableBasicMapper.deleteAll(guids);
            //删除标签
            tableTagRelationService.removeTagByTableIds(guids);
        }
        // 不能删除的为空表示全部删除成功，返回空集合
        if (tableOccupied.isEmpty()) {
            return Collections.emptyList();
        }
        // 返回不能删除的桌号
        return tableBasicMapper.selectList(new LambdaQueryWrapper<TableBasicDO>()
                .in(TableBasicDO::getGuid, tableOccupied))
                .stream()
                .map(TableBasicDO::getTableCode)
                .collect(Collectors.toList());
    }

    @Override
    public List<TableBasicDTO> listTable(TableBasicQueryDTO tableBasicQueryDTO) {
        if ("-1".equals(tableBasicQueryDTO.getAreaGuid())) {
            tableBasicQueryDTO.setAreaGuid(null);
        }
        List<TableBasicDO> tableBasicDOS = tableBasicMapper.selectList(
                new LambdaQueryWrapper<TableBasicDO>()
                        .eq(TableBasicDO::getStoreGuid, tableBasicQueryDTO.getStoreGuid())
                        .eq(
                                StringUtils.hasText(tableBasicQueryDTO.getAreaGuid()),
                                TableBasicDO::getAreaGuid,
                                tableBasicQueryDTO.getAreaGuid()
                        )
                        .in(
                                tableBasicQueryDTO.getTableGuidList() != null && !tableBasicQueryDTO.getTableGuidList().isEmpty(),
                                TableBasicDO::getGuid,
                                tableBasicQueryDTO.getTableGuidList()
                        )
                        .orderByAsc(TableBasicDO::getSort)
        );
        List<TableBasicDTO> tableBasicDTOS = TABLE_MAP_STRUCT.tableBasicDos2BasicDto(tableBasicDOS);
        //转入tag信息
        Map<String, List<TableTagDTO>> tagInfoByTableInfos =
                tableTagRelationService.getTagInfoByTableInfos(tableBasicDOS.stream().map(TableBasicDO::getGuid).collect(Collectors.toList()));
        tableBasicDTOS.forEach(e -> e.setTableTagDTOS(tagInfoByTableInfos.get(e.getGuid())));
        return tableBasicDTOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchCreate(TableBatchCreateDTO tableBatchCreateDTO) {
        String fixedFirstWord = StringUtils.isEmpty(tableBatchCreateDTO.getFixedFirstWord()) ? "" : tableBatchCreateDTO.getFixedFirstWord();
        Integer seatsPerTable = tableBatchCreateDTO.getSeatsPerTable();
        Integer startNum = tableBatchCreateDTO.getStartNum();
        Integer total = tableBatchCreateDTO.getTotal();
        DecimalFormat decimalFormat = validatorTableNum(tableBatchCreateDTO, total);
        List<TableBasicDO> tableBasicDOS = new ArrayList<>();
        for (int i = startNum; i < total + startNum; i++) {
            TableBasicDO tableBasicDO = TABLE_MAP_STRUCT.tableBatch2TableDo(tableBatchCreateDTO);
            tableBasicDO.setSeats(seatsPerTable);
            tableBasicDO.setTableCode(fixedFirstWord + decimalFormat.format(i));
            tableBasicDOS.add(tableBasicDO);
        }
        List<String> createTableCode = tableBasicDOS.stream()
                .map(TableBasicDO::getTableCode)
                .collect(Collectors.toList());
        // 过滤当前门店，当前区域下桌台是否重名
        List<String> existTableCode = tableBasicMapper.selectList(new LambdaQueryWrapper<TableBasicDO>()
                .eq(TableBasicDO::getStoreGuid, tableBatchCreateDTO.getStoreGuid())
                .eq(TableBasicDO::getAreaGuid, tableBatchCreateDTO.getAreaGuid())
                .in(TableBasicDO::getTableCode, createTableCode))
                .stream()
                .map(TableBasicDO::getTableCode)
                .collect(Collectors.toList());
        List<TableBasicDO> insertBasicList = tableBasicDOS.stream()
                .filter(tableBasicDO -> !existTableCode.contains(tableBasicDO.getTableCode()))
                .collect(Collectors.toList());
        List<TableOrderDO> insertOrderList = new ArrayList<>();
        if (insertBasicList.isEmpty()) {
            return EXISTED;
        }
        List<String> tableBasicGuid = redisService.batchGuid(insertBasicList.size(), TABLE_BASIC);
        insertBasicList.forEach(tableBasicDO -> tableBasicDO.setGuid(tableBasicGuid.remove(tableBasicGuid.size() - 1)));
        List<String> tableOrderGuid = redisService.batchGuid(insertBasicList.size(), TABLE_ORDER);
        for (TableBasicDO tableBasicDO : insertBasicList) {
            TableOrderDO tableOrderDO = TABLE_MAP_STRUCT.tableBasicDo2OrderDo(tableBasicDO);
            tableOrderDO.setGuid(tableOrderGuid.remove(tableOrderGuid.size() - 1));
            insertOrderList.add(tableOrderDO);
        }
        if (!insertBasicList.isEmpty()) {
            Integer maxSort = Optional.ofNullable(tableBasicMapper.maxSort(tableBatchCreateDTO.getStoreGuid(), tableBatchCreateDTO.getAreaGuid()))
                    .orElse(0);
            for (TableBasicDO tableBasicDO : insertBasicList) {
                tableBasicDO.setSort(++maxSort);
            }
            this.saveBatch(insertBasicList);
            tableOrderService.saveBatch(insertOrderList, insertOrderList.size());
            //创建标签
            List<String> tableGuids = insertBasicList.stream().map(TableBasicDO::getGuid).collect(Collectors.toList());
            tableTagRelationService.createTags(tableBatchCreateDTO.getTagList(), tableGuids);
        }
        return SUCCESS;
    }

    private DecimalFormat validatorTableNum(TableBatchCreateDTO tableBatchCreateDTO, Integer total) {
        if (null == total || total < 0) {
            throw new BusinessException("桌台数量不能小于0");
        }
        DecimalFormat df = null;
        Integer startNum = tableBatchCreateDTO.getStartNum();
        int end = startNum + total - 1;
        switch (tableBatchCreateDTO.getBatchTableEnum()) {
            case FIXED_2: {
                if (total > 99 || end > 99) {
                    throw new BusinessException("最多数量为99");
                }
                df = new DecimalFormat("00");
                break;
            }
            case FIXED_3: {
                if (total > 999 || end > 999) {
                    throw new BusinessException("最多数量为999");
                }
                df = new DecimalFormat("000");
                break;
            }
            case FIXED_4: {
                if (total > 9999 || end > 9999) {
                    throw new BusinessException("最多数量为9999");
                }
                df = new DecimalFormat("0000");
                break;
            }
        }
        return df;
    }

    @Override
    public boolean isTableOccupied(String areaGuid) {
        Integer tableCount = tableBasicMapper.selectCount(new LambdaQueryWrapper<TableBasicDO>()
                .eq(TableBasicDO::getAreaGuid, areaGuid)
        );
        return null != tableCount && tableCount > 0;
    }

    /**
     * 查询桌台信息
     *
     * @param tableGuid 桌台信息
     * @return 桌台信息
     */
    @Override
    public TableBasicDTO queryTableInfo(String tableGuid) {
        TableBasicDO tableBasicDO = this.getOne(new LambdaQueryWrapper<TableBasicDO>().eq(TableBasicDO::getGuid, tableGuid));
        if (Objects.isNull(tableBasicDO)) {
            log.warn("桌台信息null tableGuid={}", tableGuid);
            return null;
        }
        return TABLE_MAP_STRUCT.tableBasicDo2BasicDto(tableBasicDO);
    }

    @Override
    public List<PadAreaDTO> queryUnBindingTableInfo(List<String> bindingTableGuids, String storeGuid) {
        List<PadAreaDTO> padAreaDTOList = new ArrayList<>();
        List<TableBasicDO> tableBasicDOList = tableBasicMapper.selectList(new LambdaQueryWrapper<TableBasicDO>()
                .eq(TableBasicDO::getStoreGuid, storeGuid)
                .notIn(CollectionUtils.isNotEmpty(bindingTableGuids), TableBasicDO::getGuid, bindingTableGuids));
        if (CollectionUtils.isEmpty(tableBasicDOList)) {
            return new ArrayList<>();
        }

        // 只返回未开台
//        List<String> tableGuidList = tableBasicDOList.stream()
//                .map(TableBasicDO::getGuid)
//                .collect(Collectors.toList());
//        List<TableOrderDO> tableOrderDOList = tableOrderService.list(new LambdaQueryWrapper<TableOrderDO>()
//                .in(CollectionUtils.isEmpty(tableGuidList), TableOrderDO::getTableGuid, tableGuidList)
//                .eq(TableOrderDO::getStatus, 0)
//        );
//        List<String> notOpenTableGuid = tableOrderDOList.stream()
//                .map(TableOrderDO::getTableGuid)
//                .collect(Collectors.toList());
//        tableBasicDOList.removeIf(t -> !notOpenTableGuid.contains(t.getGuid()));
//        if (CollectionUtils.isEmpty(tableBasicDOList)) {
//            log.info("桌台信息为空");
//            return padAreaDTOList;
//        }

        Map<String, List<TableBasicDO>> areaMap = tableBasicDOList.stream()
                .collect(Collectors.groupingBy(TableBasicDO::getAreaGuid));
        Set<Map.Entry<String, List<TableBasicDO>>> entrySet = areaMap.entrySet();
        for (Map.Entry<String, List<TableBasicDO>> entry : entrySet) {
            PadAreaDTO padAreaDTO = new PadAreaDTO();
            String areaGuid = entry.getKey();
            padAreaDTO.setAreaGuid(areaGuid);
            List<TableBasicDO> tableList = areaMap.get(areaGuid);
            padAreaDTO.setAreaName(tableList.get(0).getAreaName());
            List<TableInfoDTO> tableIDTOList = new ArrayList<>();
            for (TableBasicDO tableBasicDO : tableList) {
                TableInfoDTO infoDTO = new TableInfoDTO();
                infoDTO.setTableName(tableBasicDO.getTableCode());
                infoDTO.setTableGuid(tableBasicDO.getGuid());
                infoDTO.setSort(tableBasicDO.getSort());
                tableIDTOList.add(infoDTO);
            }
            padAreaDTO.setTableIDTOList(tableIDTOList);
            padAreaDTOList.add(padAreaDTO);
        }
        return padAreaDTOList;
    }

    /**
     * 根据门店列表查询桌台Guid列表
     *
     * @param singleDataDTO datas必传，门店guid
     * @return 门店和桌台的guid
     */
    @Override
    public List<StoreAndTableDTO> listTableByStoreGuid(SingleDataDTO singleDataDTO) {
        List<String> storeGuidList = singleDataDTO.getDatas();
        if (org.springframework.util.CollectionUtils.isEmpty(storeGuidList)) {
            throw new BusinessException("storeGuidList不能为空");
        }
        List<TableBasicDO> tableBasicDOList = this.list(new LambdaQueryWrapper<TableBasicDO>()
                .in(TableBasicDO::getStoreGuid, storeGuidList)
                .eq(TableBasicDO::getEnable, 0)
                .eq(TableBasicDO::getDeleted, 0)
        );
        Map<String, List<TableBasicDO>> storeMap = tableBasicDOList.stream()
                .collect(Collectors.groupingBy(TableBasicDO::getStoreGuid));
        List<StoreAndTableDTO> response = new ArrayList<>();
        for (Map.Entry<String, List<TableBasicDO>> entry : storeMap.entrySet()) {
            String storeGuid = entry.getKey();
            List<TableBasicDO> tableList = entry.getValue();
            StoreAndTableDTO storeAndTableDTO = new StoreAndTableDTO();
            storeAndTableDTO.setStoreGuid(storeGuid);
            storeAndTableDTO.setTableGuidList(tableList.stream().map(TableBasicDO::getGuid).collect(Collectors.toList()));
            response.add(storeAndTableDTO);
        }
        return response;
    }
}
