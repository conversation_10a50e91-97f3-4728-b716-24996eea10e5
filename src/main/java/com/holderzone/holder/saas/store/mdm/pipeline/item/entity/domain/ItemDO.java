package com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hsi_item")
@NoArgsConstructor
public class ItemDO extends BasePushDO {



    /**
     * 商品关联的分类GUID
     */
    private String typeGuid;


    /**
     * 商品来源（0：门店自己创建的商品，1：品牌自己创建的商品,2:被推送过来的商品）
     */
    private Integer itemFrom;

    /**
     * 商品类型：1.套餐（不称重，无规格），2多规格商品（多商品，不称重），3.称重商品（单商品，称重），4.单品。
     */
    private Integer itemType;

    /**
     * 是否售罄:0 否 1 是
     */
    private Integer isSoldOut;

    /**
     * 属性组状态:0：无属性; 1:有属性; 2:有必选属性组(如果是套餐，则该字段=0)
     */
    private Integer hasAttr;

    /**
     * 拼音简码
     */
    private String pinyin;

    /**
     * 商品名称简写
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String nameAbbr;

    /**
     * 商品描述
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String description;

    /**
     * 图片路径数组json
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String pictureUrl;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否产生订单：0：否，1：是。
     */
    private Integer inOrder;

    /**
     * 是否是新品（0：否，1：是）(新品)
     */
    private Integer isNew;

    /**
     * 是否热销：0：否，1：是
     */
    private Integer isBestseller;

    /**
     * 是否是招牌：0：否，1：是
     */
    private Integer isSign;

    /**
     * 是否因重复而改名：0：否，1：是
     */
    private Integer nameChange;

    /**
     * 是否逻辑删除  0：否 1：是
     */
    @TableLogic
    private  Integer isDelete;

    /**
     *  是否启用
     */
    private Boolean isEnable;

    /**
     * 父实体GUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的实体，则该字段为品牌库对应的实体GUID。
     */
    @ApiModelProperty(value = "父实体GUID")
    protected String parentGuid;
}
