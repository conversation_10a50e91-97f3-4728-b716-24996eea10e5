package com.holderzone.holder.saas.store.table.service.impl;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.table.client.ReserveClientService;
import com.holderzone.holder.saas.store.table.service.ReserveService;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/9
 * @description 预定实现
 */
@Slf4j
@Service
@AllArgsConstructor
public class ReserveServiceImpl implements ReserveService {

    private ReserveClientService reserveClientService;

    @Override
    public void separate(TableOrderCombineDTO tableOrderCombineDTO) {
        log.info("[预定服务拆台]tableOrderCombineDTO={}", JacksonUtils.writeValueAsString(tableOrderCombineDTO));
        // 不影响正常业务
        try {
            reserveClientService.separate(tableOrderCombineDTO);
        } catch (Exception e) {
            log.error("[预定拆台]异常：{},e=｛｝", e.getMessage(), e);
        }
    }

    @Override
    public void notifyTurn(TradeTableDTO tradeTableDTO) {
        log.info("[通知转台]tradeTableDTO={}", JacksonUtils.writeValueAsString(tradeTableDTO));
        // 不影响正常业务
        try {
            reserveClientService.notifyTurn(tradeTableDTO);
        } catch (Exception e) {
            log.error("[通知转台]异常：{},e=｛｝", e.getMessage(), e);
        }
    }

    @Override
    public void combine(TableOrderCombineDTO tableOrderCombineDTO) {
        log.info("[预订并台]tableOrderCombineDTO={}", JacksonUtils.writeValueAsString(tableOrderCombineDTO));
        // 不影响正常业务
        try {
            reserveClientService.combine(tableOrderCombineDTO);
        } catch (Exception e) {
            log.error("[预订并台]异常：{},e=｛｝", e.getMessage(), e);
        }
    }
}
