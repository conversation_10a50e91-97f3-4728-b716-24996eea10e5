package com.holderzone.holder.saas.store.table.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.table.domain.AreaDO;
import com.holderzone.holder.saas.store.table.domain.TableBasicDO;
import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.holder.saas.store.table.domain.bo.AreaBO;
import com.holderzone.holder.saas.store.table.domain.bo.TableBO;
import com.holderzone.holder.saas.store.table.mapper.AreaMapper;
import com.holderzone.holder.saas.store.table.mapper.TableBasicMapper;
import com.holderzone.holder.saas.store.table.service.AreaService;
import com.holderzone.holder.saas.store.table.service.RedisService;
import com.holderzone.holder.saas.store.table.service.TableBasicService;
import com.holderzone.holder.saas.store.table.service.TableOrderService;
import com.holderzone.holder.saas.store.table.utils.ThrowableExtUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.AreaDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.holderzone.holder.saas.store.table.constant.Constant.*;
import static com.holderzone.holder.saas.store.table.utils.AreaMapStruct.AREA_MAP_STRUCT;
import static com.holderzone.holder.saas.store.table.utils.TableMapStruct.TABLE_MAP_STRUCT;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-26
 */
@Slf4j
@Service
public class AreaServiceImpl extends ServiceImpl<AreaMapper, AreaDO> implements AreaService {

    private final AreaMapper areaMapper;

    private final RedisService redisService;

    private final TableBasicService tableBasicService;

    private final TableOrderService tableOrderService;

    private final TableBasicMapper tableBasicMapper;


    @Autowired
    public AreaServiceImpl(AreaMapper areaMapper, RedisService redisService,
                           TableBasicService tableBasicService, TableOrderService tableOrderService,
                           TableBasicMapper tableBasicMapper) {
        this.areaMapper = areaMapper;
        this.redisService = redisService;
        this.tableBasicService = tableBasicService;
        this.tableOrderService = tableOrderService;
        this.tableBasicMapper = tableBasicMapper;
    }

    @Override
    @Transactional
    public String initArea(BaseDTO baseDTO) {
        try {
            // 桌台区域初始化
            AreaBO areaBO = AreaBO.createInitArea(baseDTO);
            AreaDO areaDO = AREA_MAP_STRUCT.areaBo2Do(areaBO);
            areaDO.setGuid(redisService.singleGuid(AREA_TABLE));
            areaDO.setSort(1);
            areaMapper.insert(areaDO);
            // 桌台初始化
            List<String> tableBasicGuids = redisService.batchGuid(10, TABLE_BASIC);
            List<String> tableOrderGuids = redisService.batchGuid(10, TABLE_ORDER);
            List<TableBasicDO> tableBasicDOS = new ArrayList<>();
            List<TableOrderDO> tableOrderDOS = new ArrayList<>();
            for (int i = 1; i <= 10; i++) {
                TableBO initTable = TableBO.createInitTable(baseDTO, areaDO);
                // 桌台基础信息
                TableBasicDO tableBasicDO = TABLE_MAP_STRUCT.tableBo2BasicDo(initTable);
                tableBasicDO.setGuid(tableBasicGuids.remove(tableBasicGuids.size() - 1));
                String num = i != 10 ? "0" + i : i + "";
                tableBasicDO.setTableCode("A" + num);
                tableBasicDO.setSort(i);
                tableBasicDOS.add(tableBasicDO);
                // 桌台订单
                TableOrderDO tableOrderDO = TABLE_MAP_STRUCT.tableBo2OrderDo(initTable);
                tableOrderDO.setGuid(tableOrderGuids.remove(tableOrderGuids.size() - 1));
                tableOrderDO.setTableGuid(tableBasicDO.getGuid());
                tableOrderDOS.add(tableOrderDO);
            }
            tableBasicService.saveBatch(tableBasicDOS, tableBasicDOS.size());
            tableOrderService.saveBatch(tableOrderDOS, tableOrderDOS.size());
        } catch (Exception e) {
            log.error("初始化门店区域、桌台异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
            throw new BusinessException("初始化门店区域、桌台异常!");
        }
        return SUCCESS;
    }

    @Override
    public String createArea(AreaDTO areaDTO) {
        AreaDO areaDO = AREA_MAP_STRUCT.areaDto2Do(areaDTO);
        assertAreaNameAvailable(areaDTO.getStoreGuid(), areaDTO.getAreaName(), null);
        areaDO.setGuid(redisService.singleGuid(AREA_TABLE));
        areaDO.setDeleted(0);
        try {
            if (null == areaDTO.getSort()) {
                areaDO.setSort(areaMapper.maxSort(areaDTO.getStoreGuid()));
            }
            this.save(areaDO);
        } catch (Exception e) {
            log.error("新增区域异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
            throw new BusinessException("新增区域异常");
        }
        return SUCCESS;
    }

    @Override
    public String updateArea(AreaDTO areaDTO) {
        Wrapper<AreaDO> identifyWrapper = new LambdaQueryWrapper<AreaDO>()
                .eq(AreaDO::getGuid, areaDTO.getGuid());
        AreaDO areaInSql = areaMapper.selectOne(identifyWrapper);

        assertAreaNameAvailable(areaDTO.getStoreGuid(), areaDTO.getAreaName(), areaInSql.getAreaName());
        AreaDO areaDO = AREA_MAP_STRUCT.areaDto2Do(areaDTO);
        if (0 == areaMapper.update(areaDO, identifyWrapper)) {
            throw new BusinessException("更新区域失败");
        }
        if (StringUtils.isNotEmpty(areaDO.getAreaName()) && !StringUtils.equals(areaInSql.getAreaName(), areaDO.getAreaName())) {
            TableBasicDO tableBasicDO = new TableBasicDO();
            tableBasicDO.setAreaName(areaDO.getAreaName());
            tableBasicMapper.update(tableBasicDO, new LambdaQueryWrapper<TableBasicDO>()
                    .eq(TableBasicDO::getStoreGuid, areaDTO.getStoreGuid())
                    .eq(TableBasicDO::getAreaGuid, areaDO.getGuid()));
        }
        return SUCCESS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteArea(String guid) {
        boolean tableOccupied = tableBasicService.isTableOccupied(guid);
        if (tableOccupied) throw new BusinessException("该区域下存在桌台，无法删除");
        Wrapper<AreaDO> identifyWrapper = new LambdaQueryWrapper<AreaDO>().eq(AreaDO::getGuid, guid);
        if (0 == areaMapper.delete(identifyWrapper)) {
            throw new BusinessException("删除区域失败");
        }
        return SUCCESS;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<AreaDTO> queryAll(String storeGuid) {
        List<AreaDO> areaDOS = areaMapper.selectList(new LambdaQueryWrapper<AreaDO>()
                .eq(AreaDO::getStoreGuid, storeGuid)
                .orderByAsc(AreaDO::getSort));
        return AREA_MAP_STRUCT.areaDoList2DtoList(areaDOS);
    }

    @Override
    public List<AreaDTO> batchQueryAll(List<String> storeGuids) {
        List<AreaDO> areaDOS = areaMapper.selectList(new LambdaQueryWrapper<AreaDO>()
                .in(AreaDO::getStoreGuid, storeGuids)
                .orderByAsc(AreaDO::getSort));
        return AREA_MAP_STRUCT.areaDoList2DtoList(areaDOS);
    }

    /**
     * 根据桌台查询桌位
     *
     * @param areaGuid 桌台guid
     * @return 区域信息
     */
    @Override
    public AreaDTO queryAreaByTable(String areaGuid) {
        if (Objects.isNull(areaGuid)) {
            throw new BusinessException("桌台guid不能为空");
        }
        AreaDO areaDO = this.getOne(new LambdaQueryWrapper<AreaDO>().eq(AreaDO::getGuid, areaGuid));
        if (Objects.isNull(areaDO)) {
            log.warn("未查询到区域信息 areaGuid={}", areaGuid);
            return null;
        }
        return AREA_MAP_STRUCT.areaDo2Dto(areaDO);
    }

    @Override
    public List<AreaDTO> queryBatchAreaByTable(List<String> areaGuidList) {
        if (CollectionUtils.isEmpty(areaGuidList)) {
            return Lists.newArrayList();
        }
        List<AreaDO> areaDOList = this.list(new LambdaQueryWrapper<AreaDO>().in(AreaDO::getGuid, areaGuidList));
        if (CollectionUtils.isEmpty(areaDOList)) {
            return Lists.newArrayList();
        }
        return AREA_MAP_STRUCT.areaDoList2DtoList(areaDOList);
    }

    /**
     * 验证桌台区域名是否重复
     */
    private void assertAreaNameAvailable(String storeGuid, String areaName, String oriAreaName) {
        if (!Objects.equals(oriAreaName, areaName)) {
            Integer count = areaMapper.selectCount(new LambdaQueryWrapper<AreaDO>()
                    .eq(AreaDO::getStoreGuid, storeGuid)
                    .eq(AreaDO::getAreaName, areaName));
            if (null != count && count > 0) {
                throw new BusinessException("该区域名称已经存在");
            }
        }
    }
}
