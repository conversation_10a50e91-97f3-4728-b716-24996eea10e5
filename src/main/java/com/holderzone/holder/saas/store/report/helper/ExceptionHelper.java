package com.holderzone.holder.saas.store.report.helper;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.report.constant.ErrorConstant;
import lombok.extern.slf4j.Slf4j;

/**
 * 报表查询数仓
 * 判断数仓是否存在表
 * 如果不存在表，则提示请联系管理员开通
 * 如果存在表，抛出原有异常
 * 备注： 没有做全局异常，怕影响到其他接口，先指定接口处理
 */
@Slf4j
public class ExceptionHelper {

    private ExceptionHelper() {
    }


    /**
     * @param e     原有异常
     * @param table 查询目标
     * @param query 查询参数
     */
    public static String throwException(Exception e, String table, String query) {
        String errorMsg = e.getMessage();
        log.error("错误信息, e:", e);
        log.error("错误信息:{}", errorMsg);
        if (errorMsg.contains(ErrorConstant.DATABASE_DOT_EXIST)) {
            log.error("{}相关数据库不存在,入参:{}", table, JacksonUtils.writeValueAsString(query));
            return ErrorConstant.NOT_QUERY_AUTH;
        }
        return e.getMessage();
    }

}
