package com.holderzone.holder.saas.store.report.service.impl;

import com.holderzone.holder.saas.store.report.mapper.TradeOrderMapper;
import com.holderzone.holder.saas.store.report.service.TradeOrderService;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.OrderItemTypeRespDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@RequiredArgsConstructor
public class TradeOrderServiceImpl implements TradeOrderService {

    private final TradeOrderMapper tradeOrderMapper;


    @Override
    public List<OrderItemTypeRespDTO> queryItemTypes(ReportQueryVO query) {
        return tradeOrderMapper.queryItemTypes(query);
    }

    @Override
    public List<String> queryItemCategories(ReportQueryVO query) {
        return tradeOrderMapper.queryItemCategories(query);
    }
}
