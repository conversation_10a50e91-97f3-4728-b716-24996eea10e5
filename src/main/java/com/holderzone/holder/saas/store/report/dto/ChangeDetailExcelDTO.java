package com.holderzone.holder.saas.store.report.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 换菜明细
 */
@Data
public class ChangeDetailExcelDTO implements Serializable {

    private static final long serialVersionUID = -4374335766385207822L;

    /**
     * 品牌名称
     */
    @Excel(name = "品牌", orderNum = "1", width = 15)
    private String brandName;

    /**
     * 门店名称
     */
    @Excel(name = "门店", orderNum = "2", width = 15)
    private String storeName;

    /**
     * 换菜节点
     */
    @Excel(name = "操作类型", orderNum = "3", width = 10, replace = {"撤销_1", "换菜_0"})
    private Integer cancelFlag;

    /**
     * 换菜节点
     */
    @Excel(name = "换菜节点", orderNum = "4", width = 10)
    private String changeNode;

    /**
     * 换菜时间
     */
    @Excel(name = "操作时间", orderNum = "5", width = 25)
    private String changeTime;

    /**
     * 订单编号
     */
    @Excel(name = "订单编号", orderNum = "6", width = 25)
    private String orderNo;

    /**
     * 套餐名称
     */
    @Excel(name = "套餐名称", orderNum = "7", width = 20)
    private String subgroupItemName;

    /**
     * 原子菜品
     */
    @Excel(name = "原子菜品", orderNum = "8", width = 20)
    private String originalItemName;

    /**
     * 原子菜品单价
     */
    @Excel(name = "原子菜品单价", orderNum = "9", width = 15)
    private String originalItemPrice;

    /**
     * 原子菜品数量
     */
    @Excel(name = "更换数量", orderNum = "10", width = 15)
    private String originalItemCount;

    /**
     * 原子菜品合计
     */
    @Excel(name = "原菜品合计", orderNum = "11", width = 15)
    private String originalItemTotalPrice;

    /**
     * 更换菜品
     */
    @Excel(name = "更换菜品", orderNum = "12", width = 20)
    private String changeItemName;

    /**
     * 更换菜品数量
     */
    @Excel(name = "更换数量", orderNum = "13", width = 20)
    private String changeItemCount;

    /**
     * 更换菜品单价
     */
    @Excel(name = "更换菜品单价", orderNum = "14", width = 15)
    private String changeItemPrice;

    /**
     * 更换菜品价格
     */
    @Excel(name = "更换菜品合计", orderNum = "15", width = 15)
    private String changeItemTotalPrice;

    /**
     * 总差额
     */
    @Excel(name = "总差额", orderNum = "16", width = 15)
    private String changePrice;

    /**
     * 操作员
     */
    @Excel(name = "操作员", orderNum = "17", width = 15)
    private String createStaffName;
}
