package com.holderzone.holder.saas.store.message.listener;


import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.message.config.RocketMqConfig;
import com.holderzone.holder.saas.store.message.domain.MessageDO;
import com.holderzone.holder.saas.store.message.service.MessageService;
import com.holderzone.holder.saas.store.message.util.DynamicHelper;
import com.holderzone.saas.store.dto.message.EnterpriseDTO;
import com.holderzone.saas.store.dto.message.SystemMessageDTO;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.holderzone.holder.saas.store.message.util.MessageMapStruct.MESSAGE_MAP_STRUCT;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SystemMsgListener
 * @date 2018/09/04 17:22
 * @description
 * @program holder-saas-bussiness-message
 */
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.SYSTEM_MSG_TOPIC,
        tags = RocketMqConfig.SYSTEM_MSG_TAG,
        consumerGroup = RocketMqConfig.SYSTEM_MSG_CONSUMER)
public class SystemMsgListener extends AbstractRocketMqConsumer<RocketMqTopic, SystemMessageDTO> {

    private static final Logger logger = LoggerFactory.getLogger(SystemMsgListener.class);

    private final MessageService messageService;

    private final AtomicInteger mCount = new AtomicInteger(0);

    private final ExecutorService executorService = new ThreadPoolExecutor(5, 10,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(),
            r -> new Thread(r, "doSaveSystemMsg-" + mCount.incrementAndGet()));

    private final DynamicHelper dynamicHelper;

    @Autowired
    public SystemMsgListener(MessageService messageService, DynamicHelper dynamicHelper) {
        this.messageService = messageService;
        this.dynamicHelper = dynamicHelper;
    }

    @Override
    public boolean consumeMsg(SystemMessageDTO messageDTO, MessageExt messageExt) {
        logger.info("接收到系统消息，messageDTO={}", JacksonUtils.writeValueAsString(messageDTO));
        List<EnterpriseDTO> enterpriseList = messageDTO.getEnterpriseList();
        enterpriseList.forEach(enterpriseDTO -> {
            executorService.submit(() -> {
                // 手动切割数据源
                try {
                    String enterpriseGuid = enterpriseDTO.getEnterpriseGuid();
                    UserContextUtils.putErp(enterpriseGuid);
                    dynamicHelper.changeDatasource(enterpriseGuid);
                    List<MessageDO> messageDOList = new ArrayList<>();
                    List<String> storeList = enterpriseDTO.getStoreList();
                    if (StringUtils.isEmpty(enterpriseDTO.getEnterpriseGuid())) {
                        logger.error("系统消息参数异常，无企业guid！！！");
                        return;
                    }
                    if (null != storeList && !storeList.isEmpty()) {
                        storeList.forEach(storeGuid -> {
                            MessageDO messageDO = MESSAGE_MAP_STRUCT.systemMsgToBusinessMsg(messageDTO);
                            messageDO.setState(0);
                            messageDO.setMessageLevel(messageDTO.getLevel());
                            messageDO.setMessageType(BusinessMsgTypeEnum.SYSTEM_TYPE.getId());
                            messageDO.setStoreGuid(storeGuid);
                            messageDO.setMessageGuid(String.valueOf(dynamicHelper.generateGuid()));
                            messageDO.setEnterpriseGuid(enterpriseDTO.getEnterpriseGuid());
                            messageDOList.add(messageDO);
                        });
                    }
                    if (!messageDOList.isEmpty()) {
                        messageService.insertSystemMsg(messageDOList);
                    }
                } catch (Exception e) {
                    logger.error("处理系统消息未知异常 e={}", e.getMessage());
                    e.printStackTrace();
                } finally {
                    // 清除userInfo
                    UserContextUtils.remove();
                    dynamicHelper.clear();
                }
            });
        });
        return true;
    }
}
