package com.holderzone.holder.saas.store.mdm.pipeline.staff.outputs;

import com.alibaba.otter.canal.protocol.CanalEntry;
import com.holderzone.framework.canal.starter.core.CanalMsg;
import com.holderzone.framework.canal.starter.extension.RowChangeBody;
import com.holderzone.framework.canal.starter.point.CanalListenerHandler;
import com.holderzone.framework.canal.starter.point.anno.ddl.AlertTableListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.DeleteListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.InsertListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.UpdateListenPoint;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.config.SyncConfig;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.UserSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.domain.UserDO;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.mapstruct.UserMapstruct;
import com.holderzone.holder.saas.store.mdm.util.DataConvertUtils;
import com.holderzone.holder.saas.store.mdm.util.ErpUtils;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@CanalListenerHandler
@Slf4j
public class UserCanalListener {

    private final MqUtils mqUtils;

    private final UserMapstruct userMapstruct;

    private final SyncConfig syncConfig;

    @Autowired
    public UserCanalListener(MqUtils mqUtils, UserMapstruct userMapstruct, SyncConfig syncConfig) {
        this.mqUtils = mqUtils;
        this.userMapstruct = userMapstruct;
        this.syncConfig = syncConfig;
    }

    @LogBefore(value = "内部系统初始化员工", logLevel = LogLevel.INFO)
    @AlertTableListenPoint(schema = "hss_staff_*_db", table = "hss_user")
    public void onEventAlertTable(CanalMsg canalMsg, CanalEntry.RowChange rowChange) {
        if (syncConfig.shouldInitAgain(rowChange.getSql())) {
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_USER_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_USER_INIT_TAG,
                    ErpUtils.getErpGuid(canalMsg), ErpUtils.getErpGuid(canalMsg)
            );
        }
    }

    @LogBefore(value = "内部系统创建员工", logLevel = LogLevel.INFO)
    @InsertListenPoint(schema = "hss_staff_*_db", table = "hss_user")
    public void onEventInsertData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        List<UserSyncDTO> afterDataDTO = null;
        try {
            afterDataDTO = getAfterDataDTO(rowChangeBody);
            // holder的员工不同步到mdm
            afterDataDTO = afterDataDTO.stream().filter(e -> !e.getIntegrateFlag()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(afterDataDTO)) return;
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_USER_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_USER_CREATE_TAG,
                    afterDataDTO, ErpUtils.getErpGuid(canalMsg)
            );
        } catch (Exception e) {
            log.error("canalMsg:{},afterDataDTO:{}", canalMsg, afterDataDTO, e);
        }

    }

    @LogBefore(value = "内部系统修改或删除员工", logLevel = LogLevel.INFO)
    @UpdateListenPoint(schema = "hss_staff_*_db", table = "hss_user")
    public void onEventUpdateData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        List<UserDO> list = getAfterData(rowChangeBody);
        list = list.stream().filter(e -> !e.getIntegrateFlag()).collect(Collectors.toList());
        for (UserDO userDO : list) {
            if (userDO.getIsDeleted()) {
                mqUtils.sendMessage(
                        RocketMqConfig.StoreConfig.STORE_MDM_USER_TOPIC,
                        RocketMqConfig.StoreConfig.STORE_MDM_USER_DELETE_TAG,
                        Collections.singleton(userDO.getGuid()), ErpUtils.getErpGuid(canalMsg)
                );
            } else {
                mqUtils.sendMessage(
                        RocketMqConfig.StoreConfig.STORE_MDM_USER_TOPIC,
                        RocketMqConfig.StoreConfig.STORE_MDM_USER_UPDATE_TAG,
                        userMapstruct.do2DTO(userDO), ErpUtils.getErpGuid(canalMsg)
                );
            }
        }
    }

    @LogBefore(value = "内部系统删除员工", logLevel = LogLevel.INFO)
    @DeleteListenPoint(schema = "hss_staff_*_db", table = "hss_user")
    public void onEventDeleteData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        List<String> list = getBeforeGuid(rowChangeBody);
        if (CollectionUtils.isEmpty(list)) return;
        mqUtils.sendMessage(
                RocketMqConfig.StoreConfig.STORE_MDM_USER_TOPIC,
                RocketMqConfig.StoreConfig.STORE_MDM_USER_DELETE_TAG,
                list, ErpUtils.getErpGuid(canalMsg)
        );
    }

    private List<UserSyncDTO> getAfterDataDTO(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterDataDTO(rowChangeBody, UserDO.class, userMapstruct::do2DTO);
    }

    private List<UserDO> getAfterData(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterData(rowChangeBody, UserDO.class);
    }

    private List<String> getBeforeGuid(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getBeforeGuid(rowChangeBody, map -> map.get("guid"));
    }
}
