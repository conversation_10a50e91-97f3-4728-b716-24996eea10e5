package com.holderzone.holder.saas.store.mdm.pipeline.org.outputs;

import com.alibaba.otter.canal.protocol.CanalEntry;
import com.holderzone.framework.canal.starter.core.CanalMsg;
import com.holderzone.framework.canal.starter.extension.RowChangeBody;
import com.holderzone.framework.canal.starter.point.CanalListenerHandler;
import com.holderzone.framework.canal.starter.point.anno.ddl.AlertTableListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.DeleteListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.InsertListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.UpdateListenPoint;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.config.SyncConfig;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.BrandInfoDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.domain.BrandDO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.mapstruct.BrandMapstruct;
import com.holderzone.holder.saas.store.mdm.util.DataConvertUtils;
import com.holderzone.holder.saas.store.mdm.util.ErpUtils;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CanalListenerHandler
@Slf4j
public class BrandCanalListener {

    private final MqUtils mqUtils;

    private final BrandMapstruct brandMapstruct;

    private final SyncConfig syncConfig;

    @Autowired
    public BrandCanalListener(MqUtils mqUtils, BrandMapstruct brandMapstruct, SyncConfig syncConfig) {
        this.mqUtils = mqUtils;
        this.brandMapstruct = brandMapstruct;
        this.syncConfig = syncConfig;
    }

    @LogBefore(value = "内部系统创建品牌", logLevel = LogLevel.INFO)
    @AlertTableListenPoint(schema = "hso_organization_*_db", table = "hso_brand")
    public void onEventAlertTable(CanalMsg canalMsg, CanalEntry.RowChange rowChange) {
        if (syncConfig.shouldInitAgain(rowChange.getSql())) {
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_BRAND_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_BRAND_INIT_TAG,
                    ErpUtils.getErpGuid(canalMsg), ErpUtils.getErpGuid(canalMsg)
            );
        }
    }

    @LogBefore(value = "内部系统创建品牌", logLevel = LogLevel.INFO)
    @InsertListenPoint(schema = "hso_organization_*_db", table = "hso_brand")
    public void onBrandInsertData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        List<BrandInfoDTO> afterDataDTO = getAfterDataDTO(rowChangeBody);
        if (CollectionUtils.isEmpty(afterDataDTO)) return;

        mqUtils.sendMessage(
                RocketMqConfig.StoreConfig.STORE_MDM_BRAND_TOPIC,
                RocketMqConfig.StoreConfig.STORE_MDM_BRAND_CREATE_TAG,
                afterDataDTO, ErpUtils.getErpGuid(canalMsg)
        );

        log.info("内部系统创建品牌，投递消息内容：{}", JacksonUtils.writeValueAsString(afterDataDTO));
    }

    @LogBefore(value = "内部系统修改或删除品牌", logLevel = LogLevel.INFO)
    @UpdateListenPoint(schema = "hso_organization_*_db", table = "hso_brand")
    public void onBrandUpdateData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        Map<Boolean, List<BrandInfoDTO>> brandMap = getAfterDataDtoMap(rowChangeBody);
        // 删除品牌
        List<BrandInfoDTO> brandToDelete = brandMap.get(true);
        if (null != brandToDelete) {
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_BRAND_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_BRAND_DELETE_TAG,
                    brandToDelete, ErpUtils.getErpGuid(canalMsg)
            );
            log.info("内部系统删除品牌，投递消息内容：{}", JacksonUtils.writeValueAsString(brandToDelete));
        }
        // 修改品牌
        List<BrandInfoDTO> brandToUpdate = brandMap.getOrDefault(false, Collections.emptyList());
        for (BrandInfoDTO brandInfoDTO : brandToUpdate) {
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_BRAND_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_BRAND_UPDATE_TAG,
                    brandInfoDTO, ErpUtils.getErpGuid(canalMsg)
            );
            log.info("内部系统修改品牌，投递消息内容：{}", JacksonUtils.writeValueAsString(brandInfoDTO));
        }
    }

    @LogBefore(value = "内部系统删除品牌", logLevel = LogLevel.INFO)
    @DeleteListenPoint(schema = "hso_organization_*_db", table = "hso_brand")
    public void onBrandDeleteData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        List<String> list = getBeforeGuid(rowChangeBody);
        if (CollectionUtils.isEmpty(list)) return;

        List<BrandInfoDTO> brandInfoList = list.stream()
                .map(s -> new BrandInfoDTO().setThirdNo(s))
                .collect(Collectors.toList());

        mqUtils.sendMessage(
                RocketMqConfig.StoreConfig.STORE_MDM_BRAND_TOPIC,
                RocketMqConfig.StoreConfig.STORE_MDM_BRAND_DELETE_TAG,
                brandInfoList, ErpUtils.getErpGuid(canalMsg)
        );

        log.info("内部系统删除品牌，投递消息内容：{}", JacksonUtils.writeValueAsString(list));
    }

    private List<BrandInfoDTO> getAfterDataDTO(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterDataDTO(rowChangeBody, BrandDO.class, brandMapstruct::brandDO2InfoDTO);
    }

    private Map<Boolean, List<BrandInfoDTO>> getAfterDataDtoMap(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterDataDtoMap(rowChangeBody,
                BrandDO.class, BrandDO::getIsDeleted, brandMapstruct::brandDO2InfoDTO);
    }

    @Deprecated
    private Map<String, List<BrandInfoDTO>> getAfterDataDtoMapOfLogic(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterDataDtoMapOfLogic(
                rowChangeBody, BrandDO.class, brandMapstruct::brandDO2InfoDTO);
    }

    private List<String> getBeforeGuid(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getBeforeGuid(rowChangeBody, map -> map.get("guid"));
    }
}
