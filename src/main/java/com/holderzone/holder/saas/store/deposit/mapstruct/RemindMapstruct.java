package com.holderzone.holder.saas.store.deposit.mapstruct;

import com.holderzone.holder.saas.store.deposit.entity.bo.RemindDO;
import com.holderzone.saas.store.dto.deposit.req.MessageRemindReqDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

@Component
@Mapper(componentModel = "spring")
public interface RemindMapstruct {
    RemindDO fromRemindDTO(MessageRemindReqDTO messageRemindReqDTO);
    MessageRemindReqDTO fromRemindDO(RemindDO remindDO);
}
