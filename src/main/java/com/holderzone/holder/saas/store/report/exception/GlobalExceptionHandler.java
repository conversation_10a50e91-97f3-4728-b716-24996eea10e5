package com.holderzone.holder.saas.store.report.exception;

import com.holderzone.feign.spring.boot.exception.ExceptionHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GlobalExceptionHandler
 * @date 2019/05/29 15:13
 * @description
 * @program holder-saas-store-report
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler extends ExceptionHandlerAdapter {

    @ExceptionHandler(value = IOException.class)
    public ResponseEntity<String> exception(IOException e) {
        String message = e.getMessage();
        log.error("IOException:" + e);
        if (message.contains("Connection reset by peer")) {
            message = "业务繁忙，请稍后再试";
        }
        return new ResponseEntity<>(message, HttpStatus.NOT_ACCEPTABLE);
    }
}
