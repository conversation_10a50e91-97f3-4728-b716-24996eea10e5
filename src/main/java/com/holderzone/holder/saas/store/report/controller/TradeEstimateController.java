package com.holderzone.holder.saas.store.report.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Assert;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.report.constant.ErrorConstant;
import com.holderzone.holder.saas.store.report.helper.ExceptionHelper;
import com.holderzone.holder.saas.store.report.service.TradeEstimateService;
import com.holderzone.saas.store.dto.report.query.EstimateReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.EstimateReportRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 估清报表
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/trade/estimate")
public class TradeEstimateController {

    private final TradeEstimateService tradeEstimateService;

    /**
     * 估清报表列表
     */
    @PostMapping("/list")
    public Page<EstimateReportRespDTO> list(@RequestBody EstimateReportQueryVO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeEstimateService.page(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "估清报表", JacksonUtils.writeValueAsString(query)));
        }
    }

    /**
     * 估清报表导出
     */
    @PostMapping("/export")
    public String export(@RequestBody EstimateReportQueryVO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeEstimateService.export(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "导出估清报表", JacksonUtils.writeValueAsString(query)));
        }
    }
}
