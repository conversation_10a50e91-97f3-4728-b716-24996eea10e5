/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:PropertyConfig.java
 * Date:2019-12-24
 * Author:terry
 */

package com.holderzone.holder.saas.store.mdm.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@ConfigurationProperties("mdm")
public class PropertyConfig {

    private List<String> repeatedCode;

    public List<String> getRepeatedCode() {
        return repeatedCode;
    }

    public void setRepeatedCode(List<String> repeatedCode) {
        this.repeatedCode = repeatedCode;
    }
}