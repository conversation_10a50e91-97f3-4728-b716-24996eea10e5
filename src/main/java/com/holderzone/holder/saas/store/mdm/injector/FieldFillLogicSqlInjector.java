/*
 * Copyright (c) 2011-2020, hubin (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package com.holderzone.holder.saas.store.mdm.injector;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.AbstractSqlInjector;
import com.baomidou.mybatisplus.core.injector.methods.Insert;
import com.holderzone.holder.saas.store.mdm.injector.method.LogicDeleteBatchByIdsWithFill;
import com.holderzone.holder.saas.store.mdm.injector.method.LogicDeleteByIdWithFill;
import com.holderzone.holder.saas.store.mdm.injector.method.LogicDeleteByMapWithFill;
import com.holderzone.holder.saas.store.mdm.injector.method.LogicDeleteWithFill;
import com.baomidou.mybatisplus.extension.injector.methods.*;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <p>
 * SQL 逻辑删除注入器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-06-12
 */
public class FieldFillLogicSqlInjector extends AbstractSqlInjector {


    @Override
    public List<AbstractMethod> getMethodList() {
        return Stream.of(
                new Insert(),
                new LogicDeleteWithFill(),
                new LogicDeleteByMapWithFill(),
                new LogicDeleteByIdWithFill(),
                new LogicDeleteBatchByIdsWithFill(),
                new LogicUpdate(),
                new LogicUpdateById(),
                new LogicSelectById(),
                new LogicSelectBatchByIds(),
                new LogicSelectByMap(),
                new LogicSelectOne(),
                new LogicSelectCount(),
                new LogicSelectMaps(),
                new LogicSelectMapsPage(),
                new LogicSelectObjs(),
                new LogicSelectList(),
                new LogicSelectPage()
        ).collect(Collectors.toList());
    }

}
