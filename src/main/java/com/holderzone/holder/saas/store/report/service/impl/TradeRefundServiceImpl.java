package com.holderzone.holder.saas.store.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.report.constant.CommonConstant;
import com.holderzone.holder.saas.store.report.dto.RefundDetailExcelDTO;
import com.holderzone.holder.saas.store.report.mapper.TradeRefundMapper;
import com.holderzone.holder.saas.store.report.service.TradeRefundService;
import com.holderzone.holder.saas.store.report.util.ExcelUtils;
import com.holderzone.saas.store.dto.order.response.daily.AmountItemDTO;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.base.Pager;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.RefundDetailDTO;
import com.holderzone.saas.store.dto.report.resp.TotalStatisticsDTO;
import com.holderzone.saas.store.enums.order.RefundTypeEnum;
import joptsimple.internal.Strings;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
public class TradeRefundServiceImpl implements TradeRefundService {

    private final TradeRefundMapper tradeRefundMapper;

    private final OssClient ossClient;

    @Override
    public Message<RefundDetailDTO> list(ReportQueryVO query) {
        checkParams(query);
        Message<RefundDetailDTO> refundDetailMessage = new Message<>();
        // 合计
        TotalStatisticsDTO statistics = tradeRefundMapper.statistics(query);
        if (Objects.isNull(statistics) || statistics.getTotalQuantity().compareTo(BigDecimal.ZERO) == 0) {
            Pager pager = new Pager(query.getCurrentPage(), query.getPageSize(), 0);
            refundDetailMessage.setPager(pager);
            refundDetailMessage.setList(Lists.newArrayList());
            refundDetailMessage.setData(JacksonUtils.toMap(JacksonUtils.writeValueAsString(TotalStatisticsDTO.DEFAULT)));
            return refundDetailMessage;
        }
        // 查询列表
        List<RefundDetailDTO> list = tradeRefundMapper.pageInfo(query);
        refundDetailsHandler(list);
        Pager pager = new Pager(query.getCurrentPage(), query.getPageSize(), Integer.parseInt(statistics.getTotalQuantity().toPlainString()));
        refundDetailMessage.setPager(pager);
        refundDetailMessage.setList(list);
        refundDetailMessage.setData(JacksonUtils.toMap(JacksonUtils.writeValueAsString(statistics)));
        return refundDetailMessage;
    }


    @Override
    public String export(ReportQueryVO query) {
        Integer count = tradeRefundMapper.count(query);
        if (count > CommonConstant.MAX_EXPORT) {
            throw new BusinessException(CommonConstant.MAX_EXPORT_TITLE);
        }
        List<RefundDetailExcelDTO> excelList = Lists.newArrayList();
        if (count > 0) {
            query.setPageSize(CommonConstant.MAX_EXPORT);
            tradeRefundMapper.pageInfo(query).forEach(e -> {
                exportHandler(e);
                RefundDetailExcelDTO refundDetailExcel = new RefundDetailExcelDTO();
                BeanUtil.copyProperties(e, refundDetailExcel);
                refundDetailExcel.setActuallyPayFee(e.getActuallyPayFee().stripTrailingZeros().toPlainString());
                refundDetailExcel.setRefundAmount(e.getRefundAmount().stripTrailingZeros().toPlainString());
                excelList.add(refundDetailExcel);
            });
        }
        try {
            String sheetName = "退款明细";
            String fileName = sheetName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return ExcelUtils.exportExcel(excelList,
                    null, sheetName, RefundDetailExcelDTO.class, fileName, ossClient);
        } catch (Exception e) {
            throw new BusinessException("导出失败");
        }
    }

    private void checkParams(ReportQueryVO refundQuery) {
        if (ObjectUtil.isNull(refundQuery.getStartTime()) || ObjectUtil.isNull(refundQuery.getEndTime())) {
            throw new BusinessException(CommonConstant.REQUEST_TIME_NOT_EMPTY);
        }
        long days = refundQuery.getEndTime().toEpochDay() - refundQuery.getStartTime().toEpochDay();
        if (days > CommonConstant.QUERY_MAX_DAY) {
            throw new BusinessException(CommonConstant.REQUEST_TIME_EXCEED_A_MONTH);
        }
    }

    /**
     * 导出字段处理
     */
    private void exportHandler(RefundDetailDTO refundDetail) {
        refundDetailsInnerHandler(refundDetail);
        if (StringUtils.isEmpty(refundDetail.getAuthStaffName())) {
            refundDetail.setAuthStaffName("-");
        }
        if (StringUtils.isEmpty(refundDetail.getMemberPhone())) {
            refundDetail.setMemberPhone("-");
        }
    }

    /**
     * 退款 - 退款方式处理
     */
    private void refundDetailsHandler(List<RefundDetailDTO> refundDetailList) {
        if (CollectionUtils.isEmpty(refundDetailList)) {
            return;
        }
        for (RefundDetailDTO refundDetail : refundDetailList) {
            refundDetailsInnerHandler(refundDetail);
        }
    }


    private void refundDetailsInnerHandler(RefundDetailDTO refundDetail) {
        if (refundDetail.getType() != 0) {
            return;
        }
        if (StringUtils.isEmpty(refundDetail.getRefundDetails())) {
            return;
        }
        if (RefundTypeEnum.OFFLINE_REFUND.getDesc().equals(refundDetail.getRefundDetails())) {
            return;
        }
        List<AmountItemDTO> amountItemList = JacksonUtils.toObjectList(AmountItemDTO.class, refundDetail.getRefundDetails());
        if (CollectionUtils.isEmpty(amountItemList)) {
            refundDetail.setRefundDetails(Strings.EMPTY);
            return;
        }
        amountItemList = amountItemList.stream().sorted(Comparator.comparing(AmountItemDTO::getCode)).collect(Collectors.toList());
        StringBuilder sb = new StringBuilder();
        for (AmountItemDTO amountItem : amountItemList) {
            sb.append(amountItem.getName()).append("￥").append(amountItem.getAmount().abs().toPlainString()).append("、");
        }
        refundDetail.setRefundDetails(sb.substring(0, sb.length() - 1));
    }
}
