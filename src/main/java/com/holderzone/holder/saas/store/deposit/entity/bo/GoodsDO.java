package com.holderzone.holder.saas.store.deposit.entity.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("hse_deposit_goods")
public class GoodsDO {

    private static final long serialVersionUID = 8424514095125620853L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * 寄存记录 Guid
     */
    private String depositGuid;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品规格guid
     */
    private String skuGuid;

    /**
     * 商品规格名称
     */
    private String skuName;

    /**
     * 商品单位
     */
    private String goodsUnit;

    /**
     * 商品分类
     */
    private int goodsClassify;

    /**
     * 存储位置
     */
    private String storePosition;

    /**
     * 过期时间 格式 2019-12-12
     */
    private String expireTime;

    /**
     * 寄存剩余数量
     */
    private int residueNum;

    /**
     * 初始寄存数量
     */
    private int depositNum;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
