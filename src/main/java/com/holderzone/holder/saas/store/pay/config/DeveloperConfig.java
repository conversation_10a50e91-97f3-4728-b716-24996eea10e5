package com.holderzone.holder.saas.store.pay.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayStaticConfig
 * @date 2019/03/14 11:42
 * @description
 * @program holder-saas-store-trading-center
 */
@Data
@Component
@ConfigurationProperties(prefix = "developer")
public class DeveloperConfig {

    private String id;

    private String key;
}
