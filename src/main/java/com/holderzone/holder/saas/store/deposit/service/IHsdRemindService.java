package com.holderzone.holder.saas.store.deposit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.deposit.entity.bo.OperationDO;
import com.holderzone.holder.saas.store.deposit.entity.bo.RemindDO;
import com.holderzone.saas.store.dto.deposit.req.MessageRemindReqDTO;
import com.holderzone.saas.store.dto.deposit.req.OperationCreateReqDTO;
import com.holderzone.saas.store.dto.deposit.req.OperationHistoryQueryReqDTO;
import com.holderzone.saas.store.dto.deposit.resp.OperationQueryRespDTO;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-22
 */
public interface IHsdRemindService extends IService<RemindDO> {

    /**
     * 创建一条操作记录
     *
     * @param messageRemindReqDTO
     * @return
     */
    Boolean createRemindRecord(MessageRemindReqDTO messageRemindReqDTO);

    /**
     * 查询短信提醒设置
     *
     * @return
     */
    MessageRemindReqDTO queryRemindRecord(String storeGuid);
}
