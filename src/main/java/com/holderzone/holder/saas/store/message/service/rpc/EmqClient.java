/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:EmqClient.java
 * Date:2020-1-10
 * Author:terry
 */

package com.holderzone.holder.saas.store.message.service.rpc;

import com.holderzone.saas.store.dto.message.EmqMessageDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EmqClient
 * @date 2018/09/08 9:36
 * @description
 * @program holder-saas-bussiness-message
 */
@Component
@FeignClient(name = "base-service", fallbackFactory = EmqClient.EmqClientFallback.class)
public interface EmqClient {

    @PostMapping("/message/sendMessage")
    void sendMsg(@RequestBody EmqMessageDTO emqMessageDTO);

    @Component
    class EmqClientFallback implements FallbackFactory<EmqClient> {

        private static final Logger logger = LoggerFactory.getLogger(EmqClientFallback.class);

        @Override
        public EmqClient create(Throwable throwable) {
            return new EmqClient() {
                @Override
                public void sendMsg(EmqMessageDTO emqMessageDTO) {
                    throwable.printStackTrace();
                    logger.error("发送消息失败！！e={}", throwable.getMessage());
                }
            };
        }
    }
}
