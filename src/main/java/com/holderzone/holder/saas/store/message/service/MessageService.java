package com.holderzone.holder.saas.store.message.service;


import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.message.domain.MessageDO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.message.MsgInfoRespDTO;
import com.holderzone.saas.store.dto.message.MsgQuery;
import com.holderzone.saas.store.dto.message.MsgRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MessageService
 * @date 2018/09/04 16:54
 * @description //TODO
 * @program holder-saas-bussiness-message
 */
public interface MessageService {

    /**
     * @param businessMessageDTO
     * @return
     */
    String insert(BusinessMessageDTO businessMessageDTO);

    /**
     * 分页查询
     *
     * @param msgQuery
     * @return
     */
    Page<MsgInfoRespDTO> queryAll(MsgQuery msgQuery);

    /**
     * 查询详情
     *
     * @param messageGuid
     * @param state
     * @return
     */
    MsgRespDTO queryDetail(String messageGuid, Integer state);

    /**
     * 批量插入
     *
     * @param businessMessageDTOS
     * @return
     */
    String insertAll(List<BusinessMessageDTO> businessMessageDTOS);

    /**
     *
     * @param messageDOList
     */
    void insertSystemMsg(List<MessageDO> messageDOList);

    /**
     *
     * @param msgQuery
     * @return
     */
    int getCount(MsgQuery msgQuery);

    /**
     *
     * @return
     */
    String clean();

    void readAll(String storeGuid, Integer messageType);

}
