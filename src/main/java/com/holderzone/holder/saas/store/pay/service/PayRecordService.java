package com.holderzone.holder.saas.store.pay.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.store.pay.entity.domain.PayRecordDO;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.member.pay.MemberQuickPayDTO;
import com.holderzone.saas.store.dto.pay.QuickPayStatisticsReqDTO;
import com.holderzone.saas.store.dto.pay.QuickPayStatisticsRespDTO;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支付记录
 */
public interface PayRecordService extends IService<PayRecordDO> {

    /**
     * 获取支付记录合计金额
     */
    BigDecimal getStatisticsTotalAmount(BasePageDTO basePageDTO);

    /**
     * 保存会员快速收款记录
     */
    Mono<String> saveMemberQuickPayRecord(MemberQuickPayDTO memberQuickPayDTO);

    /**
     * 会员快速收款退款
     */
    Mono<String>  refundMemberQuickPayRecord(MemberQuickPayDTO memberQuickPayDTO);

    /**
     * 快速收款统计
     */
    Mono<List<QuickPayStatisticsRespDTO>> queryQuickPayStatistics(QuickPayStatisticsReqDTO request);
}
