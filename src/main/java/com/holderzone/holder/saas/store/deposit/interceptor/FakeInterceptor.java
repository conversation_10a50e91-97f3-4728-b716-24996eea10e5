package com.holderzone.holder.saas.store.deposit.interceptor;

import com.alibaba.fastjson.util.ThreadLocalCache;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import io.undertow.servlet.spec.HttpServletRequestImpl;
import io.undertow.util.HeaderMap;
import io.undertow.util.HttpString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

import static com.holderzone.saas.store.dto.common.CommonConstant.USER_INFO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WebInterceptor
 * @date 2019/02/14 09:00
 * @description 固定UserInfo，调试使用！
 * 如需关闭，请注释掉：
 * FakeInterceptor的“@Configuration”
 * WebConfig#addInterceptors的“registry.addInterceptor(new FakeInterceptor()).order(-1);”
 * @program holder-saas-store-print
 */
//@Configuration
public class FakeInterceptor implements HandlerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(FakeInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (StringUtils.hasText(request.getHeader(USER_INFO))) {
            String userInfo = URLDecoder.decode(request.getHeader(USER_INFO), "utf-8");
            log.info("[{}] 原始HEADER信息--><HEADER>{}</HEADER>", userInfo);
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }
}
