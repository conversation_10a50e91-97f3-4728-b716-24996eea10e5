package com.holderzone.holder.saas.store.mdm.pipeline.org.outputs;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.event.AbsErpRocketMqConsumer;
import com.holderzone.holder.saas.store.mdm.pipeline.org.agg.OrganizationAggService;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.DeleteOrgReqDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.OrganizationInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MDMUserListener
 * @date 2019/11/18 11:24
 * @description
 * @program holder-saas-store
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_TOPIC,
        tags = {
                RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_INIT_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_CREATE_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_UPDATE_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_DELETE_TAG
        },
        consumerGroup = RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_GROUP
)
public class OrgCanalConsumer extends AbsErpRocketMqConsumer<RocketMqTopic, String> {

    private final OrganizationAggService organizationAggService;

    @Autowired
    public OrgCanalConsumer(OrganizationAggService organizationAggService) {
        this.organizationAggService = organizationAggService;
    }

    @Override
    protected boolean doConsumeMsg(String json, MessageExt messageExt) {
        switch (messageExt.getTags()) {
            case RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_INIT_TAG:
                organizationAggService.triggerRemoteOrganization(MdmTriggerType.ofType(json));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_CREATE_TAG:
                organizationAggService.createRemoteOrganization(
                        JacksonUtils.toObjectList(OrganizationInfoDTO.class, json)
                );
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_UPDATE_TAG:
                organizationAggService.updateRemoteOrganization(
                        JacksonUtils.toObject(OrganizationInfoDTO.class, json)
                );
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_DELETE_TAG:
                organizationAggService.deleteRemoteOrganization(
                        JacksonUtils.toObject(DeleteOrgReqDTO.class, json)
                );
                break;
            default:
                log.error("unknown mq tag : {}, message：{}",
                        messageExt.getTags(),
                        JacksonUtils.writeValueAsString(json));
                break;
        }
        return true;


    }
}
