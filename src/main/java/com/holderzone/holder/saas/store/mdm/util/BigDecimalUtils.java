/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:BigDecimalUtils.java
 * Date:2019-12-19
 * Author:terry
 */

package com.holderzone.holder.saas.store.mdm.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2019-12-19 下午6:11
 */
public class BigDecimalUtils {

    public static BigDecimal quantityTrimmed(BigDecimal quantity) {
        BigDecimal moneyScale0 = quantity.setScale(0, RoundingMode.HALF_UP);
        if (quantity.compareTo(moneyScale0) == 0) return moneyScale0;
        BigDecimal moneyScale1 = quantity.setScale(1, RoundingMode.HALF_UP);
        if (quantity.compareTo(moneyScale1) == 0) return moneyScale1;
        BigDecimal moneyScale2 = quantity.setScale(2, RoundingMode.HALF_UP);
        if (quantity.compareTo(moneyScale2) == 0) return moneyScale2;
        return quantity.setScale(3, RoundingMode.HALF_UP);
    }
}
