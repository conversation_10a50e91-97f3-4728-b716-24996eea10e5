package com.holderzone.holder.saas.store.deposit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.deposit.entity.bo.OperationGoodsDO;
import com.holderzone.holder.saas.store.deposit.mapper.HsdOperationGoodsMapper;
import com.holderzone.holder.saas.store.deposit.mapstruct.OperationMapstruct;
import com.holderzone.holder.saas.store.deposit.service.IHsdOperationGoodsService;
import com.holderzone.saas.store.dto.deposit.resp.GoodsSimpleRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-02
 */
@Slf4j
@Service
public class HsdOperationGoodsServiceImpl extends ServiceImpl<HsdOperationGoodsMapper, OperationGoodsDO> implements IHsdOperationGoodsService {

    private final OperationMapstruct operationMapstruct;

    @Autowired
    public HsdOperationGoodsServiceImpl(OperationMapstruct operationMapstruct) {
        this.operationMapstruct = operationMapstruct;
    }

    @Override
    public List<GoodsSimpleRespDTO> queryGoodsOfOperation(String operationGuid, String depositGuid) {

        if (StringUtils.isEmpty(operationGuid) || StringUtils.isEmpty(depositGuid)) {
            throw new BusinessException("操作记录/寄存记录 guid 不得为空");
        }

        LambdaQueryWrapper<OperationGoodsDO> wrapper = new LambdaQueryWrapper<OperationGoodsDO>()
                .eq(OperationGoodsDO::getGuid, operationGuid)
                .eq(OperationGoodsDO::getDepositGuid, depositGuid);

        List<OperationGoodsDO> list = list(wrapper);

        return operationMapstruct.fromGoodsOfOperation(list);
    }
}
