package com.holderzone.holder.saas.store.deposit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.deposit.entity.bo.DepositDO;
import com.holderzone.saas.store.dto.deposit.req.*;
import com.holderzone.saas.store.dto.deposit.resp.*;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-22
 */
public interface IHsdDepositService extends IService<DepositDO> {

    /**
     * 创建寄存记录
     *
     * @param depositCreateReqDTO
     * @return
     */
    Boolean createDepositRecord(DepositCreateReqDTO depositCreateReqDTO);

    /**
     * 查询寄存记录
     *
     * @param depositQueryReqDTO
     * @return
     */
    Page<DepositQueryRespDTO> queryDepositRecord(DepositQueryReqDTO depositQueryReqDTO);

    /**
     * 查询寄存详情--一体机
     *
     * @param depositQueryReqDTO
     * @return
     */
    List<GoodsRespDTO> queryDepositDetail(QueryDepositDetailReqDTO depositQueryReqDTO);

    /**
     * 查询寄存详情--Pos
     *
     * @param depositQueryReqDTO
     * @return
     */
    DepositDetailForPosRespDTO queryDepositDetailForPos(QueryDepositDetailReqDTO depositQueryReqDTO);

    /**
     * 取出商品
     *
     * @param depositGetReqDTO
     * @return
     */
    boolean getDeposit(DepositGetReqDTO depositGetReqDTO);

    /**
     * 取出商品
     *
     * @param operationHistoryQueryReqDTO
     * @return
     */
    Page<OperationQueryRespDTO> queryOperationHistory(OperationHistoryQueryReqDTO operationHistoryQueryReqDTO);

    /**
     * 取出商品
     *
     * @param depositQueryReqForWebDTO
     * @return
     */
    Page<GoodsSummaryRespDTO> queryGoodsSummary(DepositQueryReqForWebDTO depositQueryReqForWebDTO);

    /**
     * 发送到期提示
     */

    void sendExpireRemindMessage();

    /**
     * 设置提醒
     */
    Boolean remindSet(MessageRemindReqDTO messageRemindReqDTO);

    /**
     * 获取短信设置提醒
     */
    MessageRemindReqDTO queryRemind(String storeGuid);

}
