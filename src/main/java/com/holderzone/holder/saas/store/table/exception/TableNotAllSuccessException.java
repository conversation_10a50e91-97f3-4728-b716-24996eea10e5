package com.holderzone.holder.saas.store.table.exception;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.table.ReserveOpenTableDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/05/31 16:17
 */
public class TableNotAllSuccessException extends BusinessException {

    private ReserveOpenTableDTO reserveOpenTableDTO;

    public ReserveOpenTableDTO getReserveOpenTableDTO() {
        return reserveOpenTableDTO;
    }

    public TableNotAllSuccessException(String message, ReserveOpenTableDTO reserveOpenTableDTO) {
        super(message);
        this.reserveOpenTableDTO = reserveOpenTableDTO;
    }
//
//    public TableNotAllSuccessException(String message, Throwable cause, ReserveOpenTableDTO reserveOpenTableDTO) {
//        super(message, cause);
//        this.reserveOpenTableDTO = reserveOpenTableDTO;
//    }
}