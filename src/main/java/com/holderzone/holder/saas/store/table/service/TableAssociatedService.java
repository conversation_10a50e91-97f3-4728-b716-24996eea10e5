package com.holderzone.holder.saas.store.table.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.store.table.domain.TableAssociatedDO;

import java.util.List;


/**
 * 桌台联台
 */
public interface TableAssociatedService extends IService<TableAssociatedDO> {

    List<TableAssociatedDO> listByTableGuid(String tableGuid);

    boolean isExist(List<String> tableGuids);

    void remove(String tableGuid);

    void removeBatch(List<String> tableGuids);

    void saveBatch(String tableGuid, List<String> associatedTableGuids);
}
