/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:CloudDatabaseClient.java
 * Date:2020-1-10
 * Author:terry
 */

package com.holderzone.holder.saas.store.message.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.message.domain.DBPropertyDO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CloudClient
 * @date 2019/03/08 14:32
 * @description
 * @program holder-saas-store-message
 */
@Component
@FeignClient(value = "holder-saas-cloud-data", fallbackFactory = CloudDatabaseClient.CloudDataFallBack.class)
public interface CloudDatabaseClient {

    @PostMapping("/enterpriseDatabase/selectDatabasesByServerCode")
    List<DBPropertyDO> getDatabaseInfo(List<String> serverCode);

    @Component
    @Slf4j
    class CloudDataFallBack implements FallbackFactory<CloudDatabaseClient> {
        @Override
        public CloudDatabaseClient create(Throwable throwable) {
            return serverCode -> {
                log.error("调用云端获取数据库信息失败 e={}", throwable.getMessage());
                throw new BusinessException("调用云端获取数据库信息失败");
            };
        }
    }
}
