package com.holderzone.holder.saas.store.report.mapper;

import com.holderzone.saas.store.dto.report.openapi.SalePayDetailRespDTO;
import com.holderzone.saas.store.dto.report.query.AggPayServiceChargeQueryDTO;
import com.holderzone.saas.store.dto.report.query.CloudPayConstituteQueryDTO;
import com.holderzone.saas.store.dto.report.query.PaymentConstituteQueryDTO;
import com.holderzone.saas.store.dto.report.query.TradeDetailQueryDTO;
import com.holderzone.saas.store.dto.report.resp.CloudPayConstituteRespDTO;
import com.holderzone.saas.store.dto.report.resp.GrouponTradeDetailRespDTO;
import com.holderzone.saas.store.dto.report.resp.PaymentConstituteDTO;
import com.holderzone.saas.store.dto.report.resp.TakeawayTradeDetailRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 结算明细
 */
@Mapper
public interface TradeDetailMapper {

    Long countTakeaway(@Param("query") TradeDetailQueryDTO query);

    Long countGroupon(@Param("query") TradeDetailQueryDTO query);

    List<GrouponTradeDetailRespDTO> listGroupon(@Param("query") TradeDetailQueryDTO query);

    List<GrouponTradeDetailRespDTO> pageGroupon(@Param("query") TradeDetailQueryDTO query);

    List<TakeawayTradeDetailRespDTO> listTakeaway(@Param("query") TradeDetailQueryDTO query);

    List<TakeawayTradeDetailRespDTO> pageTakeaway(@Param("query") TradeDetailQueryDTO query);

    List<PaymentConstituteDTO> summarizingPaymentConstitute(@Param("query") PaymentConstituteQueryDTO query);

    List<PaymentConstituteDTO> singlePaymentConstitute(@Param("query") PaymentConstituteQueryDTO query);

    List<PaymentConstituteDTO> singleDaySinglePaymentConstitute(@Param("query") PaymentConstituteQueryDTO query);

    List<SalePayDetailRespDTO> querySalePayDetail(@Param("enterpriseGuid") String enterpriseGuid,
                                                            @Param("orderGuidList") List<Long> orderGuidList);

    CloudPayConstituteRespDTO cloudPayConstitute(@Param("query") CloudPayConstituteQueryDTO query);

    BigDecimal queryAggPayServiceCharge(@Param("query") AggPayServiceChargeQueryDTO query);

}
