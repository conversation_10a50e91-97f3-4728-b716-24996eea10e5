package com.holderzone.holder.saas.store.mdm.pipeline.item.outputs;

import com.alibaba.otter.canal.protocol.CanalEntry;
import com.holderzone.framework.canal.starter.core.CanalMsg;
import com.holderzone.framework.canal.starter.extension.RowChangeBody;
import com.holderzone.framework.canal.starter.point.CanalListenerHandler;
import com.holderzone.framework.canal.starter.point.anno.ddl.AlertTableListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.DeleteListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.InsertListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.UpdateListenPoint;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.config.SyncConfig;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.TypeSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.TypeDO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.mapstruct.ItemMapStruct;
import com.holderzone.holder.saas.store.mdm.util.DataConvertUtils;
import com.holderzone.holder.saas.store.mdm.util.ErpUtils;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@CanalListenerHandler
public class TypeCanalListener {

    private final MqUtils mqUtils;

    private final ItemMapStruct itemMapStruct;

    private final SyncConfig syncConfig;

    @Autowired
    public TypeCanalListener(MqUtils mqUtils, ItemMapStruct itemMapStruct, SyncConfig syncConfig) {
        this.mqUtils = mqUtils;
        this.itemMapStruct = itemMapStruct;
        this.syncConfig = syncConfig;
    }

    @LogBefore(value = "内部系统初始化分类", logLevel = LogLevel.INFO)
    @AlertTableListenPoint(schema = "hsi_item_*_db", table = "hsi_type")
    public void onEventAlertTable(CanalMsg canalMsg, CanalEntry.RowChange rowChange) {
        if (syncConfig.shouldInitAgain(rowChange.getSql())) {
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_TYPE_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_TYPE_INIT_TAG,
                    ErpUtils.getErpGuid(canalMsg),
                    ErpUtils.getErpGuid(canalMsg)
            );
        }
    }

    @LogBefore(value = "内部系统创建分类", logLevel = LogLevel.INFO)
    @InsertListenPoint(schema = "hsi_item_*_db", table = "hsi_type")
    public void onEventInsertData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        List<TypeSyncDTO> afterDataDTO = getAfterDataDTO(rowChangeBody);
        if (CollectionUtils.isEmpty(afterDataDTO)) {
            return;
        }
        mqUtils.sendMessage(
                RocketMqConfig.StoreConfig.STORE_MDM_TYPE_TOPIC,
                RocketMqConfig.StoreConfig.STORE_MDM_TYPE_CREATE_TAG,
                afterDataDTO, ErpUtils.getErpGuid(canalMsg)
        );
    }

    @LogBefore(value = "内部系统更新或删除分类", logLevel = LogLevel.INFO)
    @UpdateListenPoint(schema = "hsi_item_*_db", table = "hsi_type")
    public void onEventUpdateData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        Map<Integer, List<TypeSyncDTO>> listMap = getAfterDatatoMap(rowChangeBody);
        // 删除分类
        List<TypeSyncDTO> typeSyncDTOSToDelete = listMap.get(1);
        if (null != typeSyncDTOSToDelete) {
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_TYPE_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_TYPE_DELETE_TAG,
                    typeSyncDTOSToDelete, ErpUtils.getErpGuid(canalMsg)
            );
        }
        // 修改分类
        List<TypeSyncDTO> typeSyncDTOSToUpdate = listMap.getOrDefault(0, Collections.emptyList());
        typeSyncDTOSToUpdate.forEach(o -> {
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_TYPE_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_TYPE_UPDATE_TAG,
                    o, ErpUtils.getErpGuid(canalMsg)
            );
        });
    }


    @LogBefore(value = "内部系统删除分类", logLevel = LogLevel.INFO)
    @DeleteListenPoint(schema = "hsi_item_*_db", table = "hsi_type")
    public void onEventDeleteData(RowChangeBody rowChangeBody, CanalMsg canalMsg) {
        List<String> list = getBeforeGuid(rowChangeBody);
        if (CollectionUtils.isEmpty(list)) return;
        List<TypeSyncDTO> typeSyncDTOList = list.stream()
                .map(s -> new TypeSyncDTO().setGuid(s))
                .collect(Collectors.toList());
        mqUtils.sendMessage(
                RocketMqConfig.StoreConfig.STORE_MDM_TYPE_TOPIC,
                RocketMqConfig.StoreConfig.STORE_MDM_TYPE_DELETE_TAG,
                typeSyncDTOList, ErpUtils.getErpGuid(canalMsg)
        );
    }


    private List<TypeSyncDTO> getAfterDataDTO(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterDataDTO(rowChangeBody, TypeDO.class, itemMapStruct::typeDO2typeSynDTO);
    }

    private Map<Integer, List<TypeSyncDTO>> getAfterDatatoMap(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterDataDtoMap(rowChangeBody, TypeDO.class, TypeDO::getIsDelete, itemMapStruct::typeDO2typeSynDTO);
    }

    private List<String> getBeforeGuid(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getBeforeGuid(rowChangeBody, map -> map.get("guid"));
    }
}
