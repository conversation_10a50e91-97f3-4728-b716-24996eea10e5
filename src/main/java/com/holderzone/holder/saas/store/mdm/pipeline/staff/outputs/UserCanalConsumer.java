package com.holderzone.holder.saas.store.mdm.pipeline.staff.outputs;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.event.AbsErpRocketMqConsumer;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.agg.UserAggService;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.UserSyncDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MDMUserListener
 * @date 2019/11/18 11:24
 * @description
 * @program holder-saas-store
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.StoreConfig.STORE_MDM_USER_TOPIC,
        tags = {
                RocketMqConfig.StoreConfig.STORE_MDM_USER_INIT_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_USER_CREATE_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_USER_DELETE_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_USER_UPDATE_TAG
        },
        consumerGroup = RocketMqConfig.StoreConfig.STORE_MDM_USER_GROUP
)
@SuppressWarnings("unchecked")
public class UserCanalConsumer extends AbsErpRocketMqConsumer<RocketMqTopic, String> {

    private final UserAggService userAggService;

    @Autowired
    public UserCanalConsumer(UserAggService userAggService) {
        this.userAggService = userAggService;
    }

    @Override
    public boolean doConsumeMsg(String json, MessageExt messageExt) {
        String tags = messageExt.getTags();
        switch (tags) {
            case RocketMqConfig.StoreConfig.STORE_MDM_USER_INIT_TAG:
                userAggService.triggerRemoteUser(MdmTriggerType.ofType(json));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_USER_CREATE_TAG:
                userAggService.createRemoteUser(JacksonUtils.toObjectList(UserSyncDTO.class, json));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_USER_UPDATE_TAG:
                userAggService.updateRemoteUser(JacksonUtils.toObject(UserSyncDTO.class, json));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_USER_DELETE_TAG:
                userAggService.deleteRemoteUser(JacksonUtils.toObjectList(String.class, json));
                break;
            default:
                log.error("unknown mq tag : {}, message：{}",
                        messageExt.getTags(),
                        JacksonUtils.writeValueAsString(json));
                break;
        }
        return true;
    }
}
