package com.holderzone.holder.saas.store.table.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/12/27 10:47
 */
public interface Constant {

    String INIT_AREA_NAME = "大厅";

    String AREA_TABLE = "hst_area";

    String TABLE_BASIC = "hst_table_basic";

    String TABLE_ORDER = "hst_table_order";

    String  TABLE_TAG = "hst_table_tag";

    String  TABLE_TAG_RELATION = "hst_table_tag_relation";

    String SUCCESS = "SUCCESS";

    String EXISTED = "桌位已存在";

    String TABLE_ASSOCIATED_EXIST = "联台失败，桌台已占用";

    String VERIFY_COMBINE_USE_GROUPON_TIPS = "此桌台已经验过券，并台会撤销已验的券，请问是否继续并台？";

    String VERIFY_COMBINE_USE_GROUPON_NOTICE = "注：如确认并台，请在并台后重新验券！";
}
