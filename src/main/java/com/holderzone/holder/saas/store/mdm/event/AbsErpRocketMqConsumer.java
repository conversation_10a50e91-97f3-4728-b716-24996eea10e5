package com.holderzone.holder.saas.store.mdm.event;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.slf4j.starter.extension.LogContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public abstract class AbsErpRocketMqConsumer<T extends RocketMqTopic, C> extends AbstractRocketMqConsumer<T, C> {

    @Autowired
    private MqUtils mqUtils;

    @Override
    public boolean consumeMsg(C c, MessageExt messageExt) {
        try {
            putErp(mqUtils.parseGuid(messageExt));
            putBizName(mqUtils.parseBizName(messageExt));
            logInfo(c, messageExt.getTags());
            return doConsumeMsg(c, messageExt);
        } catch (Exception e) {
            logError(e, messageExt.getTags());
            return false;
        } finally {
            removeErp();
            removeBizName();
        }
    }

    private void logInfo(C c, String tag) {
        log.info("业务名称：【MQ消费，{}】，TAG：【{}】，接收参数：【{}】",
                LogContextUtils.getBizName(), tag, JacksonUtils.writeValueAsString(c));
    }

    private void logError(Exception e, String tag) {
        log.error("业务名称：【MQ消费，{}】，TAG：【{}】，发生异常：【{}】",
                LogContextUtils.getBizName(), tag, ThrowableUtils.asString(e));
    }

    private void putErp(String erpGuid) {
        UserContextUtils.putErp(erpGuid);
        EnterpriseIdentifier.setEnterpriseGuid(erpGuid);
    }

    private void putBizName(String bizName) {
        LogContextUtils.putBizName(bizName);
    }

    private void removeErp() {
        UserContextUtils.remove();
        EnterpriseIdentifier.remove();
    }

    private void removeBizName() {
        LogContextUtils.clear();
    }

    protected abstract boolean doConsumeMsg(C c, MessageExt messageExt);
}
