package com.holderzone.holder.saas.store.mdm.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpMethod;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReqTypeConstant
 * @date 2019/11/18 15:19
 * @description
 * @program holder-saas-store
 */
public interface ReqTypeConstant {

    String POST = "post";

    String DELETE = "delete";

    String GET = "get";

    String PUT = "put";

    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    enum ReqTypeEnum {

        POST("post", HttpMethod.POST),

        PUT("put", HttpMethod.PUT),

        DELETE("delete", HttpMethod.DELETE),

        GET("get", HttpMethod.GET),

        ;

        private String code;

        private HttpMethod method;

        public static HttpMethod getMethodByCode(String code) {
            return Arrays.stream(ReqTypeEnum.values())
                    .filter(r -> r.code.equals(code))
                    .findFirst()
                    .orElseThrow(() ->
                            new IllegalStateException(String.format("unknown reqType:%s", code))
                    )
                    .method;
        }
    }
}
