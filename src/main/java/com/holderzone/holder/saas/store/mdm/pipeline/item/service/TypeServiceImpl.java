package com.holderzone.holder.saas.store.mdm.pipeline.item.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.dynamic.datasource.starter.anno.SwitchServerCodeAnno;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.TypeSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.TypeDO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.mapper.TypeMapper;
import com.holderzone.holder.saas.store.mdm.pipeline.item.mapstruct.ItemMapStruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeServiceImpl
 * @date 2019/11/23 下午6:51
 * @description //
 * @program holder
 */
@Slf4j
@Service
public class TypeServiceImpl extends ServiceImpl<TypeMapper, TypeDO> implements TypeService {


    private final ItemMapStruct itemMapStruct;

    @Autowired
    public TypeServiceImpl(ItemMapStruct itemMapStruct) {

        this.itemMapStruct = itemMapStruct;
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public void insertType(TypeSyncDTO typeSyncDTO) {
        TypeDO typeDO = itemMapStruct.typeSynDTO2typeDO(typeSyncDTO);
        typeDO.setSort(999);
        typeDO.setIsEnable(1);
        this.save(typeDO);
        log.info("insert 智慧门店保存 type = {} ,success！", JacksonUtils.writeValueAsString(typeDO));
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public void updateTypeByGuid(TypeSyncDTO typeSyncDTO) {
        TypeDO typeDO = itemMapStruct.typeSynDTO2typeDO(typeSyncDTO);
        this.update(typeDO, new LambdaQueryWrapper<TypeDO>().eq(TypeDO::getGuid, typeDO.getGuid()));
        log.info("update 智慧门店更新 type = {} ,success！", JacksonUtils.writeValueAsString(typeDO));
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public void deleteTypeByGuid(String guid) {
        this.remove(new LambdaQueryWrapper<TypeDO>().eq(TypeDO::getGuid, guid));
        log.info("delete 智慧门店删除 guid = {} ,success！", guid);
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public TypeDO getLocalRecord(Wrapper<TypeDO> queryWrapper) {
        return super.getOne(queryWrapper);
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public List<TypeDO> shouldInitTypeDOS() {
        return this.list(new LambdaQueryWrapper<>());
    }
}
