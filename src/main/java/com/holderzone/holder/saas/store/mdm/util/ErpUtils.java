package com.holderzone.holder.saas.store.mdm.util;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.canal.starter.core.CanalMsg;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ErpUtils
 * @date 2019/11/18 17:36
 * @description
 * @program holder-saas-store
 */
public class ErpUtils {

    public static void createUserContext(CanalMsg canalMsg) {
        String erpGuid = getErpGuid(canalMsg);
        UserContextUtils.putErp(erpGuid);
    }

    public static String getErpGuid(CanalMsg canalMsg) {
        String schemaName = canalMsg.getSchemaName();
        String enterpriseGuid;
        if (!schemaName.contains("_db")) {
            throw new RuntimeException("数据库名称格式错误");
        } else {
            schemaName = schemaName.replace("_db", "");
            String[] schemaNames = schemaName.split("_");
            if (schemaNames.length == 0) {
                throw new RuntimeException("找不到企业Guid");
            } else {
                String enterpriseGuid1 = schemaNames[schemaNames.length - 1];
                String enterpriseGuid2 = schemaNames[schemaNames.length - 2];
                //若enterpriseGuid1是纯数字则默认为企业guid
                boolean numeric = StringUtils.isNumeric(enterpriseGuid1);
                if(numeric){
                    enterpriseGuid = enterpriseGuid1;
                }else {
                    enterpriseGuid = enterpriseGuid1.length() > enterpriseGuid2.length() ? enterpriseGuid1 : enterpriseGuid2;

                }
            }
        }
        return enterpriseGuid;
    }
}
