package com.holderzone.holder.saas.store.table.service;

import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.TableCombineDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/01/16 17:09
 */
public interface BizMsgService {


    String sendMsg(BaseDTO openTableDTO, String content);

    String sendMsg(BaseDTO openTableDTO, List<String> tableToNotify);

    /**
     * 并台发送消息给pad除自己的各个桌台
     * content：当前桌台已于大厅-A05进行并桌，桌台订单将同步合并
     *
     * @param tableCombineDTO 桌台并单信息
     */
    void sendMsg(TableCombineDTO tableCombineDTO);

    /**
     * 主动转台/被动转台 发送消息给对应的pad设备
     * content：您的订单已转至大厅-A03桌台，请使用大厅-A03桌台的PAD进行点餐，继续使用将产生新的订单
     *
     * @param turnTableDTO 转台信息
     */
    void sendMsg(TurnTableDTO turnTableDTO);

    /**
     * 非PAD关台后推送消息给pad
     *
     * @param storeGuid 门店guid
     * @param storeName 门店名称
     * @param tableGuid 桌台guid
     */
    void sendCloseMsg(String storeGuid, String storeName, String tableGuid);

    /**
     * pad拆台发送消息给所有设备
     *
     * @param tableOrderCombineDTO 拆台信息
     */
    void sendPadMsg(TableOrderCombineDTO tableOrderCombineDTO, List<String> tableToNotify);
}
