package com.holderzone.holder.saas.store.message;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.holderzone.holder.saas.store.message.util.SpringContextUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@SpringBootApplication
@EnableEurekaClient
@EnableSwagger2
@EnableFeignClients
@EnableApolloConfig
public class HolderSaasStoreMessageApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext app = SpringApplication.run(HolderSaasStoreMessageApplication.class, args);
        SpringContextUtils.getInstance().setCfgContext(app);
    }

}
