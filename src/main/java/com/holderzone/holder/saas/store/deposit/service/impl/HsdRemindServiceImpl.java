package com.holderzone.holder.saas.store.deposit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.deposit.entity.bo.RemindDO;
import com.holderzone.holder.saas.store.deposit.mapper.HsdRemindMapper;
import com.holderzone.holder.saas.store.deposit.mapstruct.RemindMapstruct;
import com.holderzone.holder.saas.store.deposit.service.DistributedIdService;
import com.holderzone.holder.saas.store.deposit.service.IHsdRemindService;
import com.holderzone.holder.saas.store.deposit.service.rpc.StoreRpcService;
import com.holderzone.saas.store.dto.deposit.req.MessageRemindReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-02
 */
@Slf4j
@Service
public class HsdRemindServiceImpl extends ServiceImpl<HsdRemindMapper, RemindDO> implements IHsdRemindService {

    private final DistributedIdService distributedIdService;

    private final RemindMapstruct remindMapstruct;

    private final StoreRpcService storeRpcService;

    @Autowired
    public HsdRemindServiceImpl(DistributedIdService distributedIdService,
                                StoreRpcService storeRpcService,
                                RemindMapstruct remindMapstruct) {
        this.remindMapstruct = remindMapstruct;
        this.distributedIdService = distributedIdService;
        this.storeRpcService = storeRpcService;
    }

    @Override
    public Boolean createRemindRecord(MessageRemindReqDTO messageRemindReqDTO) {

        LambdaQueryWrapper<RemindDO> wrapper = new LambdaQueryWrapper<RemindDO>()
                .eq(RemindDO::getStoreGuid, messageRemindReqDTO.getStoreGuid());
        RemindDO remindDO = remindMapstruct.fromRemindDTO(messageRemindReqDTO);
        if (count(wrapper) == 0) {
            remindDO.setGuid(distributedIdService.nextRemindGuid());
            save(remindDO);
        } else {
            update(remindDO, wrapper);
        }
        return true;
    }

    @Override
    public MessageRemindReqDTO queryRemindRecord(String storeGuid) {
        RemindDO remindDO;
        LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper<RemindDO>().eq(RemindDO::getStoreGuid, storeGuid);
        if (count(lambdaQueryWrapper) > 0) {
            remindDO = getOne(lambdaQueryWrapper);
            log.info("查询短信配置记录:{}", JacksonUtils.writeValueAsString(remindDO));
            StoreDTO storeDTO = storeRpcService.queryStoreByGuid(storeGuid);
            remindDO.setStoreName(storeDTO.getName());
            log.info("成功获取到门店信息:{}", JacksonUtils.writeValueAsString(storeDTO));
            return remindMapstruct.fromRemindDO(remindDO);
        } else {
            log.info("门店信息获取失败");
            return null;
        }
    }
}
