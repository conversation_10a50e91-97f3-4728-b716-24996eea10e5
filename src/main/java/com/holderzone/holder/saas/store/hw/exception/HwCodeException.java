package com.holderzone.holder.saas.store.hw.exception;

public class HwCodeException extends RuntimeException {

    private static final long serialVersionUID = 973721658024308852L;

    private String code = "400";

    public HwCodeException(String message) {
        super(message);
    }

    public HwCodeException(String code, String message) {
        this(message);
        this.code = code;
    }

    public HwCodeException(String message, Throwable cause) {
        super(message, cause);
    }

    public HwCodeException(String code, String message, Throwable cause) {
        this(message, cause);
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
