package com.holderzone.holder.saas.store.mdm.pipeline.org.mapstruct;

import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.BrandInfoDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.domain.BrandDO;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className BrandMapstruct
 * @date 19-1-2 下午5:23
 * @description Mapstruct-品牌
 * @program holder-saas-store-organization
 */
@Component
@Mapper(componentModel = "spring")
public interface BrandMapstruct {

    BrandMapstruct INSTANCE = Mappers.getMapper(BrandMapstruct.class);

    @Mappings({
            @Mapping(source = "logo", target = "logoUrl"),
            @Mapping(source = "thirdNo", target = "guid")
    })
    BrandDO brandInfoDTO2DO(BrandInfoDTO brandInfoDTO);

    @Mappings({
            @Mapping(source = "logoUrl", target = "logo"),
            @Mapping(source = "guid", target = "thirdNo")
    })
    BrandInfoDTO brandDO2InfoDTO(BrandDO brandDO);

    BrandDTO brandDO2DTO(BrandDO brandDO);
}
