package com.holderzone.holder.saas.store.table.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holder.saas.store.table.domain.TableDO;
import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.saas.store.dto.table.CompensationTableDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTableCombineDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-26
 */
@Repository
public interface TableOrderMapper extends BaseMapper<TableOrderDO> {

    /**
     * @param tableBasicQueryDTO  桌台查询条件
     * @return  桌台列表
     */
    List<TableDO> selectAllTable(TableBasicQueryDTO tableBasicQueryDTO);

    TableOrderDO querySyn(String tableGuid);

    void removeCombine(List<TableOrderDO> updateList);

    void compensationStatus(List<CompensationTableDTO> list);

    int closeTable(@Param("tableGuid") String tableGuid, @Param("orderGuid") String orderGuid,@Param("subStatus")String subStatus);

    TableDO selectFullInfo(String tableGuid);

    void updateAll(List<TableOrderDO> tableOrderDos);

    List<WxStoreTableCombineDTO> tableCombineList(@Param("orderGuid")String orderGuid,@Param("tableGuid") String tableGuid);
}
