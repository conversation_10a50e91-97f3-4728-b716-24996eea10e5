package com.holderzone.holder.saas.store.deposit.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.base.dto.message.MessageType;
import com.holderzone.framework.base.dto.message.ShortMessageDTO;
import com.holderzone.framework.base.dto.message.ShortMessageType;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.*;
import com.holderzone.holder.saas.cmember.app.dto.account.response.SimpleMemberInfoDTO;
import com.holderzone.holder.saas.store.deposit.entity.bo.DepositDO;
import com.holderzone.holder.saas.store.deposit.entity.bo.GoodsDO;
import com.holderzone.holder.saas.store.deposit.entity.bo.OperationGoodsDO;
import com.holderzone.holder.saas.store.deposit.mapper.HsdDepositMapper;
import com.holderzone.holder.saas.store.deposit.mapstruct.GoodsMapstruct;
import com.holderzone.holder.saas.store.deposit.service.*;
import com.holderzone.holder.saas.store.deposit.service.rpc.ItemRpcService;
import com.holderzone.holder.saas.store.deposit.service.rpc.MemberRpcService;
import com.holderzone.holder.saas.store.deposit.util.TextUtils;
import com.holderzone.holder.saas.store.deposit.util.GenerateDepositOrderID;
import com.holderzone.holder.saas.store.deposit.util.PageAdapter;
import com.holderzone.holder.saas.store.deposit.util.SendMessageUtil;
import com.holderzone.resource.common.dto.enterprise.DeductShortMessageDTO;
import com.holderzone.saas.store.dto.deposit.req.*;
import com.holderzone.saas.store.dto.deposit.resp.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-02
 */
@Slf4j
@Service
public class HsdDepositServiceImpl extends ServiceImpl<HsdDepositMapper, DepositDO> implements IHsdDepositService {

    private final ItemRpcService itemRpcService;

    private final GenerateDepositOrderID generateDepositOrderID;

    private final DistributedIdService distributedIdService;

    private final IHsdGoodsService iHsdGoodsService;

    private final IHsdOperationService iHsdOperationService;

    private final IHsdOperationGoodsService iHsdOperationGoodsService;

    private final IHsdRemindService iHsdRemindService;

    private final MemberRpcService memberRpcService;

    private final GoodsMapstruct goodsMapstruct;

    @Autowired
    private HsdDepositMapper hsdDepositMapper;

    @Autowired
    private SendMessageUtil sendMessageUtil;

    @Resource(name = "threadPool")
    private ThreadPoolTaskExecutor taskExecutor;

    @Value("${erp.host}")
    private String erpHost;
    /**
     * 初始时间，默认永久有效，即为null
     */
    private String lastLocalDateTime = null;

    @Autowired
    public HsdDepositServiceImpl(ItemRpcService itemRpcService,
                                 DistributedIdService distributedIdService,
                                 IHsdGoodsService iHsdGoodsService,
                                 IHsdOperationService iHsdOperationService,
                                 IHsdOperationGoodsService iHsdOperationGoodsService,
                                 MemberRpcService memberRpcService,
                                 IHsdRemindService iHsdRemindService,
                                 GenerateDepositOrderID generateDepositOrderID,
                                 GoodsMapstruct goodsMapstruct) {
        this.itemRpcService = itemRpcService;
        this.distributedIdService = distributedIdService;
        this.iHsdGoodsService = iHsdGoodsService;
        this.goodsMapstruct = goodsMapstruct;
        this.iHsdOperationService = iHsdOperationService;
        this.iHsdOperationGoodsService = iHsdOperationGoodsService;
        this.iHsdRemindService = iHsdRemindService;
        this.generateDepositOrderID = generateDepositOrderID;
        this.memberRpcService = memberRpcService;
    }

    @Override
    @Transactional
    public Boolean createDepositRecord(DepositCreateReqDTO depositCreateReqDTO) {

        if (StringUtils.isEmpty(depositCreateReqDTO.getStoreGuid())) {
            throw new BusinessException("门店Guid不得为空");
        }

        if (StringUtils.isEmpty(depositCreateReqDTO.getMemberGuid())) {
            throw new BusinessException("memberGuid不得为空");
        }

        List<GoodsRespDTO> goods = depositCreateReqDTO.getGoods();

        if (CollectionUtils.isEmpty(goods)) {
            throw new BusinessException("商品集合不得为空");
        }
        String depositGuid = distributedIdService.nextDepositItemGuid();

        // 生成一条寄存记录
        DepositDO depositDO = new DepositDO();
        depositDO.setStoreGuid(depositCreateReqDTO.getStoreGuid());
        depositDO.setHeadPortrait(depositCreateReqDTO.getHeadPortrait());
        depositDO.setDepositOrderId(generateDepositOrderID.generateDepositID(depositCreateReqDTO.getStoreGuid()));

        // 设置会员的 guid
        depositDO.setUserGuid(depositCreateReqDTO.getMemberGuid());

        depositDO.setRemark(depositCreateReqDTO.getRemark());
        depositDO.setGuid(depositGuid);
        depositDO.setCustomerName(depositCreateReqDTO.getCustomerName());
        depositDO.setPhoneNum(depositCreateReqDTO.getPhoneNum());
        depositDO.setCreateUser(UserContextUtils.getUserName());
        List<GoodsDO> goodsDOS = new ArrayList<>();
        goods.forEach(goodsDTO -> {
                GoodsDO goodsDO = goodsMapstruct.fromGoodsDTO(goodsDTO);
                goodsDO.setGuid(distributedIdService.nextGoodsGuid());
                goodsDO.setResidueNum(goodsDTO.getDepositNum());
                goodsDO.setDepositNum(goodsDTO.getDepositNum());
                goodsDO.setDepositGuid(depositGuid);
                goodsDO.setStoreGuid(depositCreateReqDTO.getStoreGuid());
                goodsDO.setGoodsUnit(goodsDTO.getGoodsUnit());
                goodsDO.setSkuGuid(goodsDTO.getGuid());

                if (!StringUtils.isEmpty(goodsDTO.getExpireTime())) {

                    // 过期时间，默认拼接当天最晚时间
                    goodsDTO.setExpireTime(goodsDTO.getExpireTime() + " 23:59:59");
                    if (StringUtils.isEmpty(lastLocalDateTime)) {
                        lastLocalDateTime = goodsDTO.getExpireTime();
                    } else if (DateTimeUtils.string2LocalDateTime(lastLocalDateTime, "yyyy-MM-dd HH:mm:ss").isBefore(DateTimeUtils.string2LocalDateTime(goodsDTO.getExpireTime(), "yyyy-MM-dd HH:mm:ss"))) {
                        lastLocalDateTime = goodsDTO.getExpireTime();
                    }

                    goodsDO.setExpireTime(goodsDTO.getExpireTime());

                } else {
                    goodsDO.setExpireTime("永久有效");
                }

                goodsDO.setGoodsClassify(0);
                goodsDOS.add(goodsDO);
        });
        iHsdGoodsService.saveBatch(goodsDOS);

        int depositTotal = goods.stream().mapToInt(GoodsRespDTO::getDepositNum).sum();
        depositDO.setDepositNum(depositTotal);
        depositDO.setResidueNum(depositTotal);
        depositDO.setSorted(depositDO.getResidueNum() == 0 ? DateTimeUtils.string2LocalDateTime("1970-01-01 00:00:00") : DateTimeUtils.now());
        if (StringUtils.hasText(lastLocalDateTime)) {
            depositDO.setExpireTime(lastLocalDateTime);
        } else {
            depositDO.setExpireTime("永久有效");
        }

        save(depositDO);
        LambdaQueryWrapper<DepositDO> wrapper = new LambdaQueryWrapper<DepositDO>().eq(DepositDO::getGuid, depositDO.getGuid());
        DepositDO tempDepositDO = getOne(wrapper);
        tempDepositDO.setSorted(depositDO.getResidueNum() == 0 ? DateTimeUtils.string2LocalDateTime("1970-01-01 00:00:00") : depositDO.getGmtModified());
        saveOrUpdate(tempDepositDO);

        MessageRemindReqDTO messageRemindReqDTO = queryRemind(depositDO.getStoreGuid());
        // 开启了短信提醒才发送短信（1，开启。0，关闭）,发送寄存提醒短信
        if (!ObjectUtils.isEmpty(messageRemindReqDTO) && messageRemindReqDTO.getDepositRemind() == 1) {
            String productDetails = getProductDetails(depositCreateReqDTO.getGoods(),true);
            sendMessage(depositDO.getPhoneNum(), messageRemindReqDTO.getStoreName(),
                    depositDO.getCustomerName(), ShortMessageType.NEW_DEPOSIT, productDetails);
        }

        OperationCreateReqDTO operationCreateReqDTO = new OperationCreateReqDTO();
        operationCreateReqDTO.setRemark(depositCreateReqDTO.getRemark());
        operationCreateReqDTO.setOperator(UserContextUtils.getUserName());
        operationCreateReqDTO.setUserId(depositCreateReqDTO.getMemberGuid());
        operationCreateReqDTO.setOperationWay(0);

        String operationGuid = distributedIdService.nextOperationGuid();

        // 创建一条操作记录
        iHsdOperationService.createOperationRecord(operationCreateReqDTO, operationGuid, depositGuid);

        // 操作记录关联的商品
        List<OperationGoodsDO> operationGoodsList = goodsDOS.stream()
                .map(goodsDTO -> {
                            OperationGoodsDO operationGoodsDO = new OperationGoodsDO();
                            operationGoodsDO.setGuid(operationGuid);
                            operationGoodsDO.setDepositGuid(depositGuid);
                            operationGoodsDO.setGoodsName(goodsDTO.getGoodsName());
                            operationGoodsDO.setSkuGuid(goodsDTO.getSkuGuid());
                            operationGoodsDO.setSkuName(goodsDTO.getSkuName());
                            operationGoodsDO.setOperatorNum(goodsDTO.getDepositNum());
                            operationGoodsDO.setResidueNum(goodsDTO.getDepositNum());
                            operationGoodsDO.setDepositGoodGuid(goodsDTO.getGuid());
                            return operationGoodsDO;
                        }
                ).collect(Collectors.toList());
        // 批量存入数据
        iHsdOperationGoodsService.saveBatch(operationGoodsList);

        lastLocalDateTime = null;

        //erp库存操作
        modifyErpRepertory(depositDO, operationGoodsList, true);
        return true;

    }

    private String getProductDetails(List<GoodsRespDTO> goods,boolean takeIn) {
        String productDetails = null;
        if (!CollectionUtils.isEmpty(goods)) {
            StringBuilder stringBuilder = new StringBuilder();
            for (GoodsRespDTO good : goods) {
                stringBuilder.append(good.getGoodsName());
                if (!StringUtils.isEmpty(good.getSkuName())) {
                    stringBuilder.append("(").append(good.getSkuName()).append(")");
                }
                stringBuilder.append("*")
                        .append(takeIn ? good.getDepositNum() : good.getTakeOutNum())
                        .append("、");
            }
            productDetails = stringBuilder.deleteCharAt(stringBuilder.length() - 1).toString();
        }
        return productDetails;
    }

    /**
     * 寄存商品库存操作
     *
     * @param depositDO 寄存记录
     * @param goodsDOS  商品信息
     * @param isDeposit 是否寄存 true 寄存  false 取出
     */
    private void modifyErpRepertory(DepositDO depositDO, List<OperationGoodsDO> goodsDOS, Boolean isDeposit) {
        if (CollectionUtils.isEmpty(goodsDOS)) {
            return;
        }
        UserContext userContext = UserContextUtils.get();
        taskExecutor.execute(() -> {
            UserContextUtils.putErpAndStore(userContext.getEnterpriseGuid(), userContext.getStoreGuid());
            List<String> itemNames = goodsDOS.stream().map(OperationGoodsDO::getGoodsName).collect(Collectors.toList());
            //寄存记录未存商品规格guid 临时通过商品名称查询规格。
            Map<String, String> skus = itemRpcService.findSkusByItemName(itemNames);
            List<DepositDish> dishes = new ArrayList<>();
            goodsDOS.forEach(goodsDO -> {
                if (!StringUtils.isEmpty(skus.get(goodsDO.getGoodsName()))) {
                    DepositDish dish = new DepositDish();
                    dish.setSkuId(skus.get(goodsDO.getGoodsName()));
                    dish.setSkuCount(BigDecimal.valueOf(goodsDO.getOperatorNum()));
                    dishes.add(dish);
                }
            });
            if (CollectionUtils.isEmpty(dishes)) {
                return;
            }
            DepositErpSyncDTO erpSyncDTO = new DepositErpSyncDTO();
            erpSyncDTO.setIsDeposit(isDeposit);
            erpSyncDTO.setThirdNo(depositDO.getGuid());
            erpSyncDTO.setStoreId(depositDO.getStoreGuid());
            erpSyncDTO.setUserGuid(userContext.getUserGuid());
            erpSyncDTO.setUsername(userContext.getUserName());
            erpSyncDTO.setDepositDishes(dishes);
            String url = erpHost + "/api/inventory/deposit/add";
            log.info("寄存商品调用erp,url:{}，请求参数：{}", url, JSONUtil.parse(erpSyncDTO));
            String result = HttpRequest.post(url)
                    .header("enterpriseGuid", userContext.getEnterpriseGuid())
                    .body(JSONUtil.parse(erpSyncDTO))
                    .execute().body();
            log.info("寄存商品调用erp,返回结果：{}", result);
        });
    }

    @Override
    @Transactional
    public Page<DepositQueryRespDTO> queryDepositRecord(DepositQueryReqDTO depositQueryReqDTO) {

        IPage<DepositDO> page = null;
        List<SimpleMemberInfoDTO> memberInfoList = Collections.emptyList();
        if (depositQueryReqDTO.getCondition().length() == 12) { // 寄存单号或会员卡号
            LambdaQueryWrapper wrapper = new LambdaQueryWrapper<DepositDO>().eq(DepositDO::getDepositOrderId, depositQueryReqDTO.getCondition());
            if (count(wrapper) == 0) {
                log.info("会员卡号查询会员：{}", depositQueryReqDTO.getCondition());
                memberInfoList = memberRpcService.getMemberInfo(depositQueryReqDTO.getCondition());
                log.info("会员卡号会员结果返回：{}", JacksonUtils.writeValueAsString(memberInfoList));
            } else {
                page = hsdDepositMapper.queryDepositRecordFromOrderId(new PageAdapter<>(depositQueryReqDTO), depositQueryReqDTO);
                log.info("寄存单号查询会员结果返回：{}", JacksonUtils.writeValueAsString(page.getRecords()));
            }
        } else {
            memberInfoList = memberRpcService.getMemberInfo(depositQueryReqDTO.getCondition());
            log.info("查询条件 {} 对应的会员信息：{}", depositQueryReqDTO.getCondition(), JacksonUtils.writeValueAsString(memberInfoList));
        }

        // 更新会员电话号码
        for (int i = 0; i < memberInfoList.size(); i++) {
            if (!StringUtils.isEmpty(memberInfoList.get(i).getPhoneNum())) {
                DepositDO depositDO = new DepositDO();
                depositDO.setPhoneNum(memberInfoList.get(i).getPhoneNum());
                LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper<DepositDO>().eq(DepositDO::getUserGuid, memberInfoList.get(i).getMemberInfoGuid());
                update(depositDO, lambdaQueryWrapper);
            }
        }

        if (memberInfoList.size() == 0 && page == null) {
            page = hsdDepositMapper.queryDepositRecordFromOrderId(new PageAdapter<>(depositQueryReqDTO), depositQueryReqDTO);
            return new PageAdapter<>(page, Collections.emptyList());
        } else if (memberInfoList.size() == 1) { // 仅通过一种方式注册会员，则通过guid去查询
            DepositQueryReqDTO depositQueryReqDTOPhoneGuid = new DepositQueryReqDTO();
            depositQueryReqDTOPhoneGuid.setStoreGuid(depositQueryReqDTO.getStoreGuid());
            depositQueryReqDTOPhoneGuid.setPhoneGuid(memberInfoList.get(0).getMemberInfoGuid());
            page = hsdDepositMapper.queryDepositRecordFromMemberGuid(new PageAdapter<>(depositQueryReqDTO), depositQueryReqDTOPhoneGuid);
        } else if (memberInfoList.size() == 2) { // 微信、电话两种方式注册了会员,则通过电话号码查询
            DepositQueryReqDTO depositQueryReqDTOAllGuid = new DepositQueryReqDTO();
            depositQueryReqDTOAllGuid.setStoreGuid(depositQueryReqDTO.getStoreGuid());
            depositQueryReqDTOAllGuid.setPhoneGuid(memberInfoList.get(0).getMemberInfoGuid());
            depositQueryReqDTOAllGuid.setWxGuid(memberInfoList.get(1).getMemberInfoGuid());
            page = hsdDepositMapper.queryDepositRecordFromMemberGuid(new PageAdapter<>(depositQueryReqDTO), depositQueryReqDTOAllGuid);
        }

        return new PageAdapter<>(page, page.getRecords().stream()
                .map(depositBO -> {
                            DepositQueryRespDTO depositQueryRespDTO = new DepositQueryRespDTO();
                            depositQueryRespDTO.setGuid(depositBO.getGuid());
                            depositQueryRespDTO.setDepositOrderId(depositBO.getDepositOrderId());
                            depositQueryRespDTO.setPhoneNum(depositBO.getPhoneNum());

//                            depositQueryRespDTO.setRemark(depositBO.getRemark());

                            depositQueryRespDTO.setHeadPortrait(depositBO.getHeadPortrait());
                            depositQueryRespDTO.setCustomerName(depositBO.getCustomerName());
                            depositQueryRespDTO.setSaveTime(DateTimeUtils.localDateTime2String(depositBO.getGmtCreate(), "yyyy/MM/dd HH:mm:ss"));
                            List<GoodsDO> goodsDOList = iHsdGoodsService.queryGoodsList(depositBO.getGuid());
//                            List<GoodsRespDTO> goodsDTOList = goodsMapstruct.fromGoodsList(goodsDOList);
//                            depositQueryRespDTO.setGoodsList(goodsDTOList.stream().map(goodsDTO -> {
//                                // 计算剩余天数
//                                goodsDTO.setResidueDay((int) ChronoUnit.DAYS.between(LocalDate.now(), TimeUtils.parseLocalDate(goodsDTO.getExpireTime())));
//                                return goodsDTO;
//                            }).collect(Collectors.toList()));


                            depositQueryRespDTO.setResidueNum(goodsDOList.stream().mapToInt(GoodsDO::getResidueNum).sum());
                            depositQueryRespDTO.setDepositNum(depositBO.getDepositNum());
                            if (depositBO.getExpireTime().equals("永久有效")) {
                                depositQueryRespDTO.setExpireTime("永久有效");
                            } else {
                                // 拼接时间格式
                                depositQueryRespDTO.setExpireTime(depositBO.getExpireTime().substring(0, 10).replace("-", "/"));
                            }
                            return depositQueryRespDTO;
                        }
                ).collect(Collectors.toList()));
    }

    @Override
    public List<GoodsRespDTO> queryDepositDetail(QueryDepositDetailReqDTO depositQueryReqDTO) {
        List<GoodsDO> goodsDOList = iHsdGoodsService.queryGoodsList(depositQueryReqDTO.getDepositGuid());
        return goodsMapstruct.fromGoodsList(goodsDOList);
    }

    @Override
    @Transactional
    public DepositDetailForPosRespDTO queryDepositDetailForPos(QueryDepositDetailReqDTO depositQueryReqDTO) {
        LambdaQueryWrapper wrapper = new LambdaQueryWrapper<DepositDO>().eq(DepositDO::getGuid, depositQueryReqDTO.getDepositGuid());
        DepositDO depositDO = getOne(wrapper);

//        ReturnMemberAndCardInfoRespDTO returnMemberAndCardInfoRespDTO = memberRpcService.getMemberInfo(depositDO.getUserGuid());

        DepositDetailForPosRespDTO depositDetailForPosRespDTO = new DepositDetailForPosRespDTO();
        depositDetailForPosRespDTO
                .setHeadPortrait(depositDO.getHeadPortrait())
                .setCustomerName(depositDO.getCustomerName())
                .setPhoneNum(depositDO.getPhoneNum())
                .setDepositOrderId(depositDO.getDepositOrderId())
                .setSaveTime(DateTimeUtils.localDateTime2String(depositDO.getGmtCreate(), "yyyy-MM-dd"))
                .setRemark(depositDO.getRemark())
                .setGoodsRespDTOS(goodsMapstruct.fromGoodsList(iHsdGoodsService.queryGoodsList(depositQueryReqDTO.getDepositGuid())));
        return depositDetailForPosRespDTO;
    }

    @Override
    @Transactional
    public boolean getDeposit(DepositGetReqDTO depositGetReqDTO) {

        List<GoodsRespDTO> goodsRespDTOList = depositGetReqDTO.getGoodsList();

        if (StringUtils.isEmpty(depositGetReqDTO.getDepositGuid())) {
            throw new BusinessException("depositGuid 不得为空");
        }

        LambdaQueryWrapper<DepositDO> depositWrapper = new LambdaQueryWrapper<DepositDO>()
                .eq(DepositDO::getGuid, depositGetReqDTO.getDepositGuid());

        // 获取寄存记录
        DepositDO depositDO = getOne(depositWrapper);

        OperationCreateReqDTO operationCreateReqDTO = new OperationCreateReqDTO();
        if (depositGetReqDTO.getRemark().length() <= 30) {
            operationCreateReqDTO.setRemark(depositGetReqDTO.getRemark());
        } else {
            throw new BusinessException("备注信息不能超过30个字符");
        }
        operationCreateReqDTO.setOperator(UserContextUtils.getUserName());
        operationCreateReqDTO.setUserId(depositGetReqDTO.getUserGuid());
        operationCreateReqDTO.setOperationWay(1);

        String operationGuid = distributedIdService.nextOperationGuid();

        // 创建一条操作记录
        iHsdOperationService.createOperationRecord(operationCreateReqDTO, operationGuid, depositGetReqDTO.getDepositGuid());
        final LocalDateTime now = LocalDateTime.now();
        StringBuilder errMsg = new StringBuilder();
        //查询对应的商品记录
        List<String> depositGoodGuidList = goodsRespDTOList.stream().map(GoodsRespDTO::getGuid).collect(Collectors.toList());
        List<GoodsDO> depositGoodList = iHsdGoodsService.list(new LambdaQueryWrapper<GoodsDO>().in(GoodsDO::getGuid, depositGoodGuidList));
        if(CollectionUtils.isEmpty(depositGoodList)){
            throw new BusinessException("寄存商品不存在");
        }
        Map<String, GoodsDO> depositGoodMap = depositGoodList.stream().collect(Collectors.toMap(GoodsDO::getGuid, Function.identity(), (obj, obj1) -> obj));
        List<GoodsDO> goodsDOList = goodsRespDTOList.stream().map(goodsDTO -> {
            GoodsDO goodsDO = depositGoodMap.get(goodsDTO.getGuid());
            goodsDTO.setSkuName(goodsDO.getSkuName());
            goodsDTO.setSkuGuid(goodsDO.getSkuGuid());
            if (!"永久有效".equals(goodsDO.getExpireTime()) && DateTimeUtils.string2LocalDateTime(goodsDO.getExpireTime()).isBefore(now)) {
                errMsg.append(goodsDO.getGoodsName()).append("已过期，");
                return goodsDO;
            }
            if (goodsDTO.getTakeOutNum() > goodsDO.getResidueNum() || goodsDTO.getTakeOutNum() < 0) {
                errMsg.append(goodsDO.getGoodsName()).append("取出数量不能大于寄存数量，");
                return goodsDO;
            }
            if (goodsDTO.getTakeOutNum() == goodsDO.getResidueNum()) { // 删除寄存记录
                goodsDO.setResidueNum(0);
            } else {
                // 减去取出的商品数量，之后再更新数据
                goodsDO.setResidueNum(goodsDO.getResidueNum() - goodsDTO.getTakeOutNum());
            }
            return goodsDO;
        }).collect(Collectors.toList());
        if (errMsg.length() > 0) {
            errMsg.setLength(errMsg.length() - 1);
            throw new BusinessException(errMsg.toString());
        }

        iHsdGoodsService.saveOrUpdateBatch(goodsDOList);

        MessageRemindReqDTO messageRemindReqDTO = queryRemind(depositDO.getStoreGuid());

        log.info("短信配置：{}", JacksonUtils.writeValueAsString(messageRemindReqDTO));

        // 开启了短信提醒才发送短信（1，开启。0，关闭）
        if (!ObjectUtils.isEmpty(messageRemindReqDTO) && messageRemindReqDTO.getGetRemind() == 1) {
            String productDetails = getProductDetails(depositGetReqDTO.getGoodsList(),false);
            sendMessage(depositDO.getPhoneNum(), messageRemindReqDTO.getStoreName(),
                    depositDO.getCustomerName(), ShortMessageType.NEW_FETCH, productDetails);
        }

        List<OperationGoodsDO> operationGoodsList = goodsRespDTOList.stream()
                .map(goodsDTO -> {
                            GoodsDO goodsDO = iHsdGoodsService.queryGoodsItem(depositDO.getGuid(), goodsDTO.getGuid());
                            OperationGoodsDO operationGoodsDO = new OperationGoodsDO();
                            operationGoodsDO.setGuid(operationGuid);
                            operationGoodsDO.setDepositGuid(depositGetReqDTO.getDepositGuid());
                            operationGoodsDO.setGoodsName(goodsDTO.getGoodsName());
                            operationGoodsDO.setSkuName(goodsDTO.getSkuName());
                            operationGoodsDO.setSkuGuid(goodsDTO.getSkuGuid());
                            operationGoodsDO.setOperatorNum(goodsDTO.getTakeOutNum());
                            operationGoodsDO.setResidueNum(goodsDO.getResidueNum());
                            operationGoodsDO.setDepositGoodGuid(goodsDTO.getGuid());
                            return operationGoodsDO;
                        }
                ).collect(Collectors.toList());

        // 批量存入数据
        iHsdOperationGoodsService.saveBatch(operationGoodsList);

        depositDO.setResidueNum(iHsdGoodsService.queryGoodsList(depositGetReqDTO.getDepositGuid()).stream().mapToInt(GoodsDO::getResidueNum).sum());
        update(depositDO, depositWrapper);

        LambdaQueryWrapper<DepositDO> wrapper = new LambdaQueryWrapper<DepositDO>().eq(DepositDO::getGuid, depositDO.getGuid());
        DepositDO tempDepositDO = getOne(wrapper);
        tempDepositDO.setSorted(depositDO.getResidueNum() == 0 ? DateTimeUtils.string2LocalDateTime("1970-01-01 00:00:00") : tempDepositDO.getGmtModified());

        update(tempDepositDO, depositWrapper);
        modifyErpRepertory(depositDO, operationGoodsList, false);
        return true;
    }

    @Override
    public Page<OperationQueryRespDTO> queryOperationHistory(OperationHistoryQueryReqDTO operationHistoryQueryReqDTO) {

        return iHsdOperationService.queryOperationHistory(operationHistoryQueryReqDTO);
    }

    @Override
    public Page<GoodsSummaryRespDTO> queryGoodsSummary(DepositQueryReqForWebDTO depositQueryReqForWebDTO) {
        return iHsdGoodsService.queryGoodsSummary(depositQueryReqForWebDTO);
    }

    @Override
    public void sendExpireRemindMessage() {
        //查询有过期时间且未提醒过的寄存记录
        LambdaQueryWrapper<DepositDO> depositWrapper = new LambdaQueryWrapper<DepositDO>()
                .eq(DepositDO::getMessageStatus, 0)
                .ne(DepositDO::getExpireTime, "永久有效");
        List<DepositDO> depositDOList = list(depositWrapper);
        for (DepositDO depositDO : depositDOList) {
            MessageRemindReqDTO messageRemindReqDTO = queryRemind(depositDO.getStoreGuid());
            if (ObjectUtils.isEmpty(messageRemindReqDTO)) {
                continue;
            }
            //未开启过期
            if (messageRemindReqDTO.getExpireRemind() == 0) {
                continue;
            }
            //计算过期时间和当前时间时间差
            long expireTime = Duration.between(LocalDateTime.now(), DateTimeUtils.string2LocalDateTime(depositDO.getExpireTime(), "yyyy-MM-dd HH:mm:ss")).toHours();
            //已过期 或 未到提前提醒设置时间 无须提醒
            if (expireTime < 0 || expireTime > messageRemindReqDTO.getAdvanceDays() * 24L + 13) {
                continue;
            }
            //商品已被取出
            if (null == depositDO.getResidueNum() || depositDO.getResidueNum() == 0) {
                continue;
            }
            //获取商品
            List<GoodsRespDTO> goods = transferGoods(depositDO.getGuid());
            String productDetails = getProductDetails(goods,true);
            log.info("过期寄存记录:{}", JacksonUtils.writeValueAsString(depositDO));
            //短信发送
            MessageDTO messageDTO = new MessageDTO();
            //设置信息类别
            messageDTO.setMessageType(MessageType.SHORT_MESSAGE);
            ShortMessageDTO shortMessageDTO = new ShortMessageDTO();
            shortMessageDTO.setShortMessageType(ShortMessageType.NEW_LOSE_EFFICACY);
            shortMessageDTO.setPhoneNumber(depositDO.getPhoneNum());
            Map<String, String> paramMap = new HashMap<>(3);
            paramMap.put("StoreName", messageRemindReqDTO.getStoreName());
            paramMap.put("Name", depositDO.getCustomerName());
            if (!StringUtils.isEmpty(productDetails)){
                paramMap.put("ProductDetails", productDetails);
            }
            shortMessageDTO.setParams(paramMap);
            messageDTO.setShortMessage(shortMessageDTO);
            log.info("寄存过期提醒发送短信之前的请求入参 {} ",JacksonUtils.writeValueAsString(shortMessageDTO));
            if (messageDTO.getMessageType() != null) {
                sendMessageUtil.sendMessage(messageDTO, UserContextUtils.getEnterpriseGuid());
            }
            depositDO.setMessageStatus(1);
            update(depositDO, new LambdaQueryWrapper<DepositDO>().eq(DepositDO::getGuid, depositDO.getGuid()));
        }
    }

    private List<GoodsRespDTO> transferGoods(String guid) {
        List<GoodsDO> goodsDOS = iHsdGoodsService.queryGoodsList(guid);
        List<GoodsRespDTO> goods = Lists.newArrayList();
        for (GoodsDO goodsDO : goodsDOS) {
            GoodsRespDTO goodsRespDTO = new GoodsRespDTO();
            goodsRespDTO.setDepositNum(goodsDO.getDepositNum());
            goodsRespDTO.setGoodsName(goodsDO.getGoodsName());
            goodsRespDTO.setSkuName(goodsDO.getSkuName());
            goods.add(goodsRespDTO);
        }
        return goods;
    }


    /**
     * @param phoneNum
     * @param "6506431195651982337"
     */
    private void sendMessage(String phoneNum, String storeName, String userName, ShortMessageType messageType, String productDetails) {
        //短信发送
        MessageDTO messageDTO = new MessageDTO();
        //设置信息类别
        messageDTO.setMessageType(MessageType.SHORT_MESSAGE);
        ShortMessageDTO shortMessageDTO = new ShortMessageDTO();
        shortMessageDTO.setShortMessageType(messageType);
        shortMessageDTO.setPhoneNumber(phoneNum);
        Map<String, String> paramMap = new HashMap<>(3);
        paramMap.put("StoreName", storeName);
        paramMap.put("Name", TextUtils.filterIllegalCharacter(userName, "*"));
        paramMap.put("ProductDetails", productDetails);
        shortMessageDTO.setParams(paramMap);
        messageDTO.setShortMessage(shortMessageDTO);
        List<DeductShortMessageDTO> dslist = new ArrayList<>();
        DeductShortMessageDTO ds = new DeductShortMessageDTO();
        ds.setDeductCount(1);
        ds.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        dslist.add(ds);

        log.info("寄存发送短信之前的请求入参 {} " + JacksonUtils.writeValueAsString(shortMessageDTO));
        if (messageDTO.getMessageType() != null) {
            sendMessageUtil.sendMessage(messageDTO, UserContextUtils.getEnterpriseGuid());
        }
    }

    @Override
    public Boolean remindSet(MessageRemindReqDTO messageRemindReqDTO) {
        return iHsdRemindService.createRemindRecord(messageRemindReqDTO);
    }

    @Override
    public MessageRemindReqDTO queryRemind(String storeGuid) {
        return iHsdRemindService.queryRemindRecord(storeGuid);
    }
}
