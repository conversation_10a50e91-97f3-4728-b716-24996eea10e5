package com.holderzone.holder.saas.store.table;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 */
@EnableSwagger2
@EnableFeignClients
@EnableEurekaClient
@SpringBootApplication
@MapperScan("com.holderzone.holder.saas.store.table.mapper")
@EnableApolloConfig
public class HolderSaasStoreTableApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasStoreTableApplication.class, args);
    }

}
