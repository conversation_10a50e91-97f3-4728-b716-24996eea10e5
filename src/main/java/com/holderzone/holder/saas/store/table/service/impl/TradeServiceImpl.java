package com.holderzone.holder.saas.store.table.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.table.client.TradeRpcClient;
import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.holder.saas.store.table.mapper.TableOrderMapper;
import com.holderzone.holder.saas.store.table.service.TradeService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.ReserveBatchCreateOrderReqDTO;
import com.holderzone.saas.store.dto.table.TableCombineDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradeServiceImpl
 * @date 2019/01/23 11:40
 * @description
 * @program holder-saas-store-table
 */
@Slf4j
@Service
public class TradeServiceImpl implements TradeService {

    private final TradeRpcClient tradeRpcClient;

    private final TableOrderMapper tableOrderMapper;

    @Autowired
    public TradeServiceImpl(TradeRpcClient tradeRpcClient, TableOrderMapper tableOrderMapper) {
        this.tradeRpcClient = tradeRpcClient;
        this.tableOrderMapper = tableOrderMapper;
    }

    @Override
    public void closeOrder(CancelOrderReqDTO cancelOrderReqDTO) {
        cancelOrderReqDTO.setTable(true);
        cancelOrderReqDTO.setIsCalc(-1);
        boolean b = tradeRpcClient.tradeClient(cancelOrderReqDTO);
        if (!b) {
            throw new BusinessException("作废订单失败");
        }
    }

    @Override
    public void notifyTradeCombine(TableOrderCombineDTO tableOrderCombineDTO) {
        if (!tradeRpcClient.notifyTradeCombine(tableOrderCombineDTO)) {
            throw new BusinessException("调用订单服务并桌异常");
        }
    }

    @Override
    public void separate(TableOrderCombineDTO tableOrderCombineDTO) {
        if (!tradeRpcClient.split(tableOrderCombineDTO)) {
            throw new BusinessException("调用订单服务拆单异常");
        }
    }

    @Override
    public String openTable(CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        CreateDineInOrderReqDTO result = tradeRpcClient.openTable(createDineInOrderReqDTO);
        return result.getGuid();
    }

    @Override
    public String bacthopenTable(ReserveBatchCreateOrderReqDTO batchCreateOrderReqDTO) {
        return tradeRpcClient.batchCreateOrder(batchCreateOrderReqDTO);
    }

    @Override
    public void notifyTradeTurn(TradeTableDTO tradeTableDTO) {
        if (!tradeRpcClient.notifyTradeTurn(tradeTableDTO)) {
            throw new BusinessException("通知订单服务转台异常");
        }
    }

    /**
     * 撤销子桌订单的第三方活动
     *
     * @param tableCombineDTO 入参
     */
    @Override
    public void batchRevokeThirdActivity(TableCombineDTO tableCombineDTO) {
        List<String> tableGuidList = tableCombineDTO.getTableGuidList();
        List<TableOrderDO> tableOrderDOList = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                .in(TableOrderDO::getTableGuid, tableGuidList));
        if (CollectionUtils.isEmpty(tableOrderDOList)) {
            log.warn("未查询到桌台信息,tableGuidList={}", tableGuidList);
            return;
        }
        List<String> subOrderGuidList = tableOrderDOList.stream()
                .map(TableOrderDO::getOrderGuid)
                .distinct()
                .collect(Collectors.toList());
        subOrderGuidList.removeIf(order -> Objects.equals(tableCombineDTO.getMainOrderGuid(), order));
        SingleDataDTO dto = new SingleDataDTO();
        dto.setDatas(subOrderGuidList);
        tradeRpcClient.batchRevokeThirdActivityRecord(dto);
    }

    @Override
    public List<String> queryHasThirdActivityOrder(TableCombineDTO tableCombineDTO) {
        List<String> tableGuidList = tableCombineDTO.getTableGuidList();
        List<TableOrderDO> tableOrderDOList = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                .in(TableOrderDO::getTableGuid, tableGuidList));
        if (CollectionUtils.isEmpty(tableOrderDOList)) {
            log.warn("未查询到桌台信息,tableGuidList={}", tableGuidList);
            return Lists.newArrayList();
        }
        List<String> subOrderGuidList = tableOrderDOList.stream()
                .map(TableOrderDO::getOrderGuid)
                .distinct()
                .collect(Collectors.toList());
        subOrderGuidList.removeIf(order -> Objects.equals(tableCombineDTO.getMainOrderGuid(), order));
        SingleDataDTO dto = new SingleDataDTO();
        dto.setDatas(subOrderGuidList);
        return tradeRpcClient.hasThirdActivityOrderGuids(dto);
    }
}
