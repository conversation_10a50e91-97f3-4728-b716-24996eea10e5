package com.holderzone.holder.saas.store.pay.service;


import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.pay.utils.PageAdapter;
import com.holderzone.saas.store.dto.pay.AggPayCallbackDTO;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.pay.*;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayRpcService
 * @date 2019/03/14 11:26
 * @description
 * @program holder-saas-store-trading-center
 */
public interface AggPayService {

    Mono<AggPayRespDTO> prePay(SaasAggPayDTO saasAggPayDTO);

    Mono<AggPayPollingRespDTO> prePayPolling(SaasPollingDTO saasPollingDTO);

    Mono<AggPayPollingRespDTO> prePayH5Polling(SaasPollingDTO saasPollingDTO);

    Mono<AggPayPollingRespDTO> queryPrePayResult(SaasPollingDTO saasPollingDTO);

    /**
     * 聚合支付查询结果
     *
     * @param saasPollingDTO
     * @return
     */
    Mono<AggPayPollingRespDTO> queryPayState(SaasPollingDTO saasPollingDTO);

    Mono<AggRefundRespDTO> refund(SaasAggRefundDTO saasAggRefundDTO);

    Mono<AggRefundPollingRespDTO> refundPolling(SaasPollingDTO saasPollingDTO);

    Mono<AggRefundPollingRespDTO> queryRefundResult(SaasPollingDTO saasPollingDTO);

    Mono<String> weChatPublic(SaasAggWeChatPublicAccountPayDTO saasAggWeChatPublicAccountPayDTO);

    Mono<AggPayPollingRespDTO> weChatPublicPolling(SaasPollingDTO saasPollingDTO);

    Mono<AggPayPollingRespDTO> queryWxPubAccResult(SaasPollingDTO saasPollingDTO);

    Mono<Page<AggPayRecordDTO>> queryPayRecord(BasePageDTO basePageDTO);

    Mono<AggPayStatisticsDTO> queryPayStatistics(BasePageDTO basePageDTO);

    Mono<String> callback(AggPayCallbackDTO aggPayCallbackDTO);

    Mono<AggPayReserveResultDTO> reservePay(SaasPollingDTO saasPollingDTO);
}
