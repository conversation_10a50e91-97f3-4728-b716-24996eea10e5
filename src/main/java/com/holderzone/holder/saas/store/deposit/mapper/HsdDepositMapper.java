package com.holderzone.holder.saas.store.deposit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.holder.saas.store.deposit.entity.bo.DepositDO;
import com.holderzone.holder.saas.store.deposit.util.PageAdapter;
import com.holderzone.saas.store.dto.deposit.req.DepositQueryReqDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-02
 */
@Component
public interface HsdDepositMapper extends BaseMapper<DepositDO> {

    IPage<DepositDO> queryDepositRecordFromOrderId(PageAdapter page, @Param("dto") DepositQueryReqDTO depositQueryReqDTO);

    IPage<DepositDO> queryDepositRecordFromMemberGuid(PageAdapter page, @Param("dto") DepositQueryReqDTO depositQueryReqDTO);

}
