package com.holderzone.holder.saas.store.client.entity.ddo;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SalesGoodsCategoryDO {

    public static SalesGoodsCategoryDO DEFAULT = new SalesGoodsCategoryDO("", 0);
    @ApiModelProperty(value = "商品名称")
    private String goodsName;
    @ApiModelProperty(value = "顾客数")
    private Integer salesCount;


    public static SalesGoodsCategoryDO INSTANCE() {
        return new SalesGoodsCategoryDO("", 0);
    }
}
