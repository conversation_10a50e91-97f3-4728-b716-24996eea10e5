package com.holderzone.holder.saas.store.deposit.util;

import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * emoji表情工具类
 */
@Slf4j
public class TextUtils {

    /**
     * 过滤非法字符串
     */
    public static String filterIllegalCharacter(String source, String replaceStr) {
        source = source.replaceAll("[\\ud800\\udc00-\\udbff\\udfff\\ud800-\\udfff]", "*");
        StringBuilder newSource = new StringBuilder();
        int len = source.length();
        for (int i = 0; i < len; i++) {
            char codePoint = source.charAt(i);
            try {
                if (!isNotEmojiCharacter(codePoint) || isDecodeText(codePoint)) {
                    newSource.append(replaceStr);
                } else {
                    newSource.append(codePoint);
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("emoji表情查询失败,source:{},codePoint:{},e:{}", source, codePoint, e.getMessage());
            }
        }
        return newSource.toString();
    }


    public static boolean isDecodeText(char codePoint) {
        Pattern p = Pattern.compile("[^A-Za-z0-9 \\u4e00-\\u9fa5]");
        Matcher m = p.matcher(String.valueOf(codePoint));
        return m.find();
    }

    public static String decodeEmoji(String source) throws UnsupportedEncodingException {
        return URLDecoder.decode(source, "utf-8");
    }

    /**
     * 判断是否为非Emoji字符
     *
     * @param codePoint 比较的单个字符
     * @return
     */
    private static boolean isNotEmojiCharacter(char codePoint) {
        return codePoint == 0x0 || codePoint == 0x9 || codePoint == 0xA || codePoint == 0xD || codePoint >= 0x20 && codePoint <= 0xD7FF || codePoint >= 0xE000 && codePoint <= 0xFFFD;
    }


    public static void main(String[] args) {
        String s = "🐠☀☹☺♂☾☽dA123测试";
        System.out.println(TextUtils.filterIllegalCharacter(s, "*")); // *******dA123测试
    }
}
