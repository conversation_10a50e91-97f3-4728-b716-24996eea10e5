package com.holderzone.holder.saas.store.pay.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.pay.config.AggPayConfig;
import com.holderzone.holder.saas.store.pay.config.DeveloperConfig;
import com.holderzone.holder.saas.store.pay.constant.PayConstant;
import com.holderzone.holder.saas.store.pay.entity.HandlerPayBO;
import com.holderzone.holder.saas.store.pay.service.PollingService;
import com.holderzone.holder.saas.store.pay.service.RedisService;
import com.holderzone.holder.saas.store.pay.service.RocketService;
import com.holderzone.holder.saas.store.pay.service.rpc.AggPayRpcService;
import com.holderzone.holder.saas.store.pay.service.rpc.FileUploadRpcService;
import com.holderzone.holder.saas.store.pay.service.rpc.SaasResultRpcService;
import com.holderzone.holder.saas.store.pay.utils.CodeUrlUtils;
import com.holderzone.holder.saas.store.pay.utils.TradingUtils;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.pay.constant.AggPayStateEnum;
import com.holderzone.saas.store.dto.pay.constant.AggRefundStateEnum;
import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class PollingServiceImpl implements PollingService {

    private final DeveloperConfig developerConfig;

    private final AggPayConfig aggPayConfig;

    private final RocketService rocketService;

    private final RedisService redisService;

    private final AggPayRpcService aggPayRpcService;

    private final FileUploadRpcService fileUploadRpcService;

    private final SaasResultRpcService saasResultRpcService;

    private static final ScheduledExecutorService se = Executors.newScheduledThreadPool(2);

    @Autowired
    public PollingServiceImpl(DeveloperConfig developerConfig, AggPayConfig aggPayConfig,
                              RocketService rocketService, RedisService redisService,
                              AggPayRpcService aggPayRpcService, FileUploadRpcService fileUploadRpcService,
                              SaasResultRpcService saasResultRpcService) {
        this.developerConfig = developerConfig;
        this.aggPayConfig = aggPayConfig;
        this.rocketService = rocketService;
        this.redisService = redisService;
        this.aggPayRpcService = aggPayRpcService;
        this.fileUploadRpcService = fileUploadRpcService;
        this.saasResultRpcService = saasResultRpcService;
    }

    @Override
    public void startPrePayPolling(AggPayPollingDTO aggPayPollingDTO, HandlerPayBO handlerPayBO) {
        if (!rocketService.polling(aggPayPollingDTO, handlerPayBO)) {
            // mq发送失败时使用定时线程池补偿
            doPrePayPollingByScheduledTask(handlerPayBO, aggPayPollingDTO);
        }
    }

    /**
     * 和redis、中数据做比较
     *
     * @return if same, the data same as cache,don,t need callback,return data;
     * else: need callback ,return  null data
     * 如果和redis中比对相同，返回数据，不需要回调，
     * 返回空：需要回调
     */
    @Override
    public Mono<String> compareStatWithCache(String orderGuid, String payGuid, String paySt) {
        //查redis上一次或者
        return redisService.getCallBackResp(orderGuid, payGuid)
                //筛选状态不对的
                .filter(state -> AggPayStateEnum.isFinish(paySt))
                //存redis
                .doOnNext(state -> redisService.putCallBackResp(orderGuid, payGuid, paySt))
                //返回result结果
                .flatMap(state -> Mono.just("SUCCESS"));
    }

    @Override
    public void startRefundPolling(SaasAggRefundDTO saasAggRefundDTO, AggRefundPollingDTO aggRefundPollingDTO) {
        doRefundPollingByScheduledTask(saasAggRefundDTO, aggRefundPollingDTO);
    }

    @Override
    public boolean handlePollingResult(AggPayPollingDTO pollingJHPayDTO, HandlerPayBO handlerPayBO, boolean useMq) {
        String orderGUID = pollingJHPayDTO.getOrderGUID();
        AggPayPollingRespDTO pollingRespDTO = handlerPayBO.getHandlerType() == 0 || PayPowerId.YL_WX_PUBLIC_NO.getId().equals(handlerPayBO.getPayPowerId())
                ? aggPayRpcService.doPolling(pollingJHPayDTO)
                : aggPayRpcService.doWeChatPublicAccountPolling(orderGUID);
        // 公众号支付存在signature为空的场景，表示当前时刻操作暂时失败（如：订单尚不存在），应该继续轮询
        PaymentInfoDTO paymentInfoDTO = handlerPayBO.getPaymentInfoDTO();
        if (!StringUtils.hasText(pollingRespDTO.getSignature())) {
            log.warn("轮询聚合支付结果业务报警：{}", pollingRespDTO.getMsg());
            if (useMq) {
                // 重新投递消息，并返回当前消息消费成功标识true
                rocketService.polling(pollingJHPayDTO, handlerPayBO);
                return true;
            }
            // 返回支付状态未终结标识false，使得定时任务得以继续执行
            return false;
        }
        // 验证签名
        if (!validatedSignature(pollingRespDTO, paymentInfoDTO.getAppSecret())) {
            log.error("轮询聚合支付结果验签失败：{}", JacksonUtils.writeValueAsString(pollingRespDTO));
            return false;
        }
        // 是否需要回调
        boolean needCallBack = compareStatWithCache(pollingRespDTO.getOrderGUID(), pollingRespDTO.getPayGUID(), pollingRespDTO.getPaySt()).block() == null;
        if (!needCallBack) {
            return true;
        }
        return afterPollingHandler(pollingRespDTO, pollingJHPayDTO, handlerPayBO, useMq);
    }


    private boolean afterPollingHandler(AggPayPollingRespDTO pollingRespDTO, AggPayPollingDTO pollingJHPayDTO, HandlerPayBO handlerPayBO, boolean useMq) {
        String orderGuid = pollingJHPayDTO.getOrderGUID();
        String payGuid = pollingJHPayDTO.getPayGUID();
        // 以下逻辑同时处理了条码、二维码的场景
        if (PayConstant.SUCCESS_CODE.equals(pollingRespDTO.getCode())) {
            // 如果是二维码扫码支付，处理图片url
            String qrCodeContents = pollingRespDTO.getCodeUrl();
            boolean isQrCode = StringUtils.hasText(qrCodeContents);
            if (isQrCode) {
                String qrCodeDownloadUrl = generateQrCode(orderGuid, qrCodeContents);
                pollingRespDTO.setCodeUrl(qrCodeDownloadUrl);
                redisService.putPollingResp(orderGuid, payGuid, pollingRespDTO);
            }
            if (!PayPowerId.YL_WX_PUBLIC_NO.getId().equals(handlerPayBO.getPayPowerId())) {
                redisService.putPollingResp(orderGuid, payGuid, pollingRespDTO);
            }
            // 支付状态不为“终结状态（成功、失败、关闭）”时：继续轮询
            String paySt = pollingRespDTO.getPaySt();
            if (!Lists.newArrayList(AggPayStateEnum.SUCCESS.getId(), AggPayStateEnum.FAILURE.getId(), AggPayStateEnum.CLOSED.getId(), "8").contains(paySt)) {
                if (failureHandler(pollingRespDTO, pollingJHPayDTO, handlerPayBO)) {
                    return true;
                }
                if (useMq) {
                    // 重新投递消息，并返回当前消息消费成功标识true
                    rocketService.polling(pollingJHPayDTO, handlerPayBO);
                    return true;
                }
                // 返回支付状态未终结标识false，使得定时任务得以继续执行
                return false;
            }
        }
        // 响应失败、支付状态“终结”情况下：缓存结果，停止轮询
        // (useMq时) 仅返回消费成功，不重新投递
        // (useThreadPool时) 返回执行成功，停止定时任务
        redisService.putPollingResp(orderGuid, payGuid, pollingRespDTO);
        saasResultRpcService.handlePayResult(handlerPayBO, pollingRespDTO);
        return true;
    }


    private boolean failureHandler(AggPayPollingRespDTO pollingRespDTO, AggPayPollingDTO pollingJHPayDTO, HandlerPayBO handlerPayBO) {
        String orderGuid = pollingJHPayDTO.getOrderGUID();
        String payGuid = pollingJHPayDTO.getPayGUID();
        String paySt = pollingRespDTO.getPaySt();
        boolean isGoOnPolling = handlerPayBO.getHandlerType() == 0 && (AggPayStateEnum.READY.getId().equals(paySt) || AggPayStateEnum.PENDING.getId().equals(paySt));
        if (!isGoOnPolling) {
            return false;
        }
        //暂时只处理聚合支付的延时问题，微信业务再看,
        //问题：  polling接口一直返回{"body":{"code":"10000","msg":"支付中，请稍等！","paySt":"10","attachData":"1","signature":"33e1c05c22ac815afd28901d77ee65f59b90502d"},"status":{"value":200,"reasonPhrase":"成功"}}]
        //解决方案： 轮询的时候去查缓存，缓存中可能有失败或者成功的结果，，成功不做处理，失败做处理
        //时间：2019年9月11日16:21:08   -wuhedong
        SaasPollingDTO saasPollingDTO = new SaasPollingDTO();
        saasPollingDTO.setOrderGuid(orderGuid);
        saasPollingDTO.setPayGuid(payGuid);
        AggPayPollingRespDTO aggPayPollingRespDTO = redisService.getPollingResp(saasPollingDTO).block();
        boolean isFailure = aggPayPollingRespDTO != null && aggPayPollingRespDTO.getPaySt() != null && aggPayPollingRespDTO.getPaySt().equals(AggPayStateEnum.FAILURE.getId());
        if (isFailure) {
            saasResultRpcService.handlePayResult(handlerPayBO, aggPayPollingRespDTO);
            return true;
        }
        return false;
    }

    private String generateQrCode(String orderGuid, String qrCodeContents) {
        String qrCodeDownloadUrl = redisService.getQrCodeDownloadUrl(orderGuid);
        if (StringUtils.isEmpty(qrCodeDownloadUrl)) {
            try {
                // 将二维码图片发送到oss服务
                FileDto fileDto = new FileDto();
                fileDto.setFileName(orderGuid + ".jpg");
                fileDto.setFileContent(CodeUrlUtils.writeToString(
                        qrCodeContents, 500, 500, new ByteArrayOutputStream()
                ));
                qrCodeDownloadUrl = fileUploadRpcService.upload(fileDto);
                log.info("上传QrCode到OSS成功，qrCodeDownloadUrl={}", qrCodeDownloadUrl);
                redisService.putQrCodeDownloadUrl(orderGuid, qrCodeDownloadUrl);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("上传QrCode到OSS失败：e={}", e.getMessage());
            }
        }
        return qrCodeDownloadUrl;
    }

    private void doPrePayPollingByScheduledTask(HandlerPayBO handlerPayBO, AggPayPollingDTO pollingJHPayDTO) {
        log.error("消息投递失败，进入补偿措施，pollingJHPayDTO：{}，handlerPayBO：{}",
                JacksonUtils.writeValueAsString(pollingJHPayDTO),
                JacksonUtils.writeValueAsString(handlerPayBO));
        // 补偿措施
        Integer maxPollingTimes = aggPayConfig.getPollingTimes();
        Map<String, Future> map = new HashMap<>(1);
        AtomicInteger pollingTimes = new AtomicInteger(0);
        ScheduledFuture<?> scheduledFuture = se.scheduleAtFixedRate(() -> {
            int curDeliveryTimes = pollingTimes.incrementAndGet();
            if (curDeliveryTimes > maxPollingTimes) {
                log.warn("当前投递次数：{}，超过上限：{}，停止投递", curDeliveryTimes, maxPollingTimes);
                map.get("key").cancel(true);
                map.clear();
                return;
            }
            log.info("当前投递次数：{}，上限：{}", curDeliveryTimes, maxPollingTimes);
            if (handlePollingResult(pollingJHPayDTO, handlerPayBO, false)) {
                map.get("key").cancel(true);
            }
        }, 2, 1, TimeUnit.SECONDS);
        map.put("key", scheduledFuture);
    }

    private void doRefundPollingByScheduledTask(SaasAggRefundDTO saasAggRefundDTO,
                                                AggRefundPollingDTO aggRefundPollingDTO) {
        Integer maxPollingTimes = aggPayConfig.getPollingTimes();
        Map<String, Future> map = new HashMap<>(1);
        AtomicInteger pollingTimes = new AtomicInteger(0);
        ScheduledFuture<?> scheduledFuture = se.scheduleAtFixedRate(() -> {
            int curDeliveryTimes = pollingTimes.incrementAndGet();
            if (curDeliveryTimes > aggPayConfig.getPollingTimes()) {
                log.warn("当前投递次数：{}，超过上限：{}，停止投递", curDeliveryTimes, maxPollingTimes);
                map.get("key").cancel(true);
                map.clear();
                return;
            }
            log.info("当前投递次数：{}，上限：{}", curDeliveryTimes, maxPollingTimes);
            AggRefundPollingRespDTO aggRefundPollingRespDTO = aggPayRpcService.doRefundPolling(aggRefundPollingDTO);
            if (PayConstant.SUCCESS_CODE.equals(aggRefundPollingRespDTO.getCode())) {
                List<AggRefundDetailRespDTO> refundOrderDetail = aggRefundPollingRespDTO.getRefundOrderDetial();
                AggRefundDetailRespDTO aggRefundDetailRespDTO = refundOrderDetail.get(refundOrderDetail.size() - 1);
                if (AggRefundStateEnum.REFUND_SUCCESS.getState().equals(aggRefundDetailRespDTO.getStatus())
                        || AggRefundStateEnum.REFUND_FAILURE.getState().equals(aggRefundDetailRespDTO.getStatus())) {
                    // handle result
                    redisService.putRefundPollingResp(saasAggRefundDTO.getAggRefundReqDTO().getOrderGUID(),
                            saasAggRefundDTO.getAggRefundReqDTO().getPayGUID(), aggRefundPollingRespDTO);
                    saasResultRpcService.handleRefundResult(saasAggRefundDTO, aggRefundPollingRespDTO);
                    // 业务执行完后才取消定时任务，否则数据库获取连接时会被interrupt
                    map.get("key").cancel(true);
                }
            }
        }, 1, 1, TimeUnit.SECONDS);
        map.put("key", scheduledFuture);
    }

    private boolean validatedSignature(AggPayPollingRespDTO pollingJHPayRespDTO, String appSecret) {
        String signature = pollingJHPayRespDTO.getSignature();
        String signatureForCheck = TradingUtils.getSignatureForCheck(
                pollingJHPayRespDTO, developerConfig.getKey(), appSecret);
        return Objects.equals(signature, signatureForCheck);
    }
}
