package com.holderzone.holder.saas.store.report.service;


import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ChangeDetailDTO;

public interface TradeChangeService {

    /**
     * 换菜明细报表
     */
    Message<ChangeDetailDTO> list(ReportQueryVO query);

    /**
     * 换菜明细导出
     */
    String export(ReportQueryVO query);
}
