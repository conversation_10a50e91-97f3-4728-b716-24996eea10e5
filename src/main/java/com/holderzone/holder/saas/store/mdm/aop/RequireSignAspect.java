package com.holderzone.holder.saas.store.mdm.aop;

import com.holderzone.holder.saas.store.mdm.entity.MDMSynDTO;
import com.holderzone.holder.saas.store.mdm.service.MdmOperation;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Aspect
@Component
public class RequireSignAspect implements Ordered {

    private final MdmOperation mdmOperation;

    @Value("${mdm.requireSign:true}")
    private Boolean isRequireSign;

    @Autowired
    public RequireSignAspect(MdmOperation mdmOperation) {
        this.mdmOperation = mdmOperation;
    }

    @Before(value = "@annotation(requireSign)")
    public void switchContext(JoinPoint joinPoint, RequireSign requireSign) {
        if (isRequireSign) {
            Arrays.stream(joinPoint.getArgs())
                    .filter(MDMSynDTO.class::isInstance)
                    .map(o -> (MDMSynDTO) o)
                    .findFirst().ifPresent(mdmOperation::checkSign);
        }
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
