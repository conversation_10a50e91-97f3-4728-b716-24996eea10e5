package com.holderzone.holder.saas.store.mdm.pipeline.org.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.OrganizationInfoDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.domain.OrganizationDO;

import java.util.List;

/**
 * 组织表 服务类
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
public interface OrganizationService extends IService<OrganizationDO> {

    List<OrganizationDO> shouldInitOrgList();

    boolean createOrganization(OrganizationInfoDTO organizationInfoDTO);

    boolean updateOrganization(OrganizationInfoDTO organizationDTO);

    void deleteOrganization(OrganizationInfoDTO organizationInfoDTO);
}
