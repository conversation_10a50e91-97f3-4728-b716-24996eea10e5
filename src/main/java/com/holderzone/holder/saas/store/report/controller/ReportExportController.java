package com.holderzone.holder.saas.store.report.controller;

import com.holderzone.holder.saas.store.report.service.ExportService;
import com.holderzone.saas.store.dto.report.query.ReportExportDTO;
import com.holderzone.saas.store.dto.report.resp.ExportRespDTO;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
/**
 * <AUTHOR>
 * @version 1.0
 * @className ReportExportController
 * @date 2018/10/11 10:04
 * @description
 * @program holder-saas-store-report
 */
@Api(description = "报表导出功能")
@RestController
@RequestMapping("/journal")
@Slf4j
public class ReportExportController {

    private final ExportService exportService;

    @Autowired
    public ReportExportController(ExportService exportService) {
        this.exportService = exportService;
    }

    @PostMapping("/export")
    public ExportRespDTO export(@RequestBody ReportExportDTO reportExportDTO) {
        return exportService.export(reportExportDTO.getExportType(), reportExportDTO.getJsonStr());
    }

}
