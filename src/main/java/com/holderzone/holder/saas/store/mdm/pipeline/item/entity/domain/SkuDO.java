package com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>
 * 商品规格表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("hsi_sku")
public class SkuDO  extends BasePushDO {

    /**
     * 商品GUID
     */
    private String itemGuid;

    /**
     * upc商品条码
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String upc;

    /**
     * 当日库存
     */
    private BigDecimal stock;

    /**
     * 总库存
     */
    private BigDecimal totalStock;


    /**
     * 编号
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String code;

    /**
     * 售卖价格
     */
    private BigDecimal salePrice;

    /**
     * 成本价格
     */
    private BigDecimal  costPrice;

    /**
     * 会员价格
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private BigDecimal memberPrice;

    /**
     * 商品规格单位
     */
    private String unit;

    /**
     * 称重单位对应的编号
     */
    private Integer unitCode;

    /**
     * 起卖数(非称重即为整数，称重即为小数)
     */
    private BigDecimal minOrderNum;

    /**
     * 是否参与会员折扣（0：否，1：是）
     */
    private Integer isMemberDiscount;

    /**
     * 是否加入整单折扣(0：否，1：是)
     */
    private Integer isWholeDiscount;

    /**
     * 是否开启库存（0：否 ， 1：是）
     */
    private Integer isOpenStock;

    /**
     * 是否上架(0：否，1：是)
     */
    private Integer isRack;


    /**
     * 是否参与自助点餐机（0：否，1：是）
     */
    private Integer isJoinBuffet;

    /**
     * 是否参与微信点餐（0：否，1：是）
     */
    private Integer isJoinWeChat;


    /**
     * 是否参与美团外卖（0：否，1：是）
     */
    private Integer isJoinMt;

    /**
     * 是否参与饿了么外卖（0：否，1：是）
     */
    private Integer isJoinElm;

    /**
     * 美团sku
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String mtSku;

    /**
     * 饿了么sku
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String elmSku;


    /**
     * 规格来源（0：门店自己创建的规格，1：品牌自己创建的规格,2:被推送过来的规格）
     */
    private Integer skuFrom;

    /**
     *  是否启用
     */
    private Boolean isEnable;

    /**
     * 品牌库对应的SKUGUID：如果是自己创建的内容，则此字段为skuGuid，如果是被推送过来的商品，则该字段为原skuGUID。
     */
    private String parentGuid;

}
