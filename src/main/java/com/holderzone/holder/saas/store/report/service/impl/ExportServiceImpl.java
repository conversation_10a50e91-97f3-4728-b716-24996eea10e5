package com.holderzone.holder.saas.store.report.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.report.service.ExportService;
import com.holderzone.holder.saas.store.report.service.TradeItemService;
import com.holderzone.holder.saas.store.report.service.rpc.business.HandoverClientService;
import com.holderzone.saas.store.dto.journaling.eum.JournalExportTypeEnum;
import com.holderzone.saas.store.dto.journaling.req.*;
import com.holderzone.saas.store.dto.journaling.resp.*;
import com.holderzone.saas.store.dto.report.query.HandOverReportQueryDTO;
import com.holderzone.saas.store.dto.report.resp.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ExportServiceImpl
 * @date 2019/6/6 10:18
 * @description
 * @program holder-saas-store-report
 */
@Service
@Slf4j
public class ExportServiceImpl implements ExportService {

    private final HandoverClientService handoverClientService;

    private final TradeItemService tradeItemService;

    @Autowired
    public ExportServiceImpl(HandoverClientService handoverClientService,
                             TradeItemService tradeItemService) {
        this.handoverClientService = handoverClientService;
        this.tradeItemService = tradeItemService;
    }

    @Override
    public ExportRespDTO export(Integer exportType, String paramString) {
        if (StringUtils.isEmpty(exportType)) {
            throw new BusinessException("导出类型为null");
        }
        if (StringUtils.isEmpty(paramString)) {
            throw new BusinessException("参数为null");
        }
        Class<?> resultClzzByType = JournalExportTypeEnum.getResultClzzByType(exportType);
        ExportRespDTO exportRespDTO = new ExportRespDTO<>();
        exportRespDTO.setHead(JournalExportTypeEnum.getHeadByType(exportType));
        exportRespDTO.setClzz(resultClzzByType);
        switch (JournalExportTypeEnum.getEnum(exportType)) {
            case SALES_VOLUME_TYPE:
                salesVolumeType(paramString, exportRespDTO);
                break;
            case SALES_STORE_VOLUME_TYPE:
                salesStoreVolumeType(paramString, exportRespDTO);
                break;
            case HANDOVER_TYPE:
                handoverType(paramString, exportRespDTO);
                break;
            default:
                log.warn("导出类型错误,exportType={}", exportType);
                break;
        }
        return exportRespDTO;
    }

    @SuppressWarnings("unchecked")
    private void handoverType(String paramString, ExportRespDTO exportRespDTO) {
        exportRespDTO.setExcelName(JournalExportTypeEnum.HANDOVER_TYPE.getName());
        exportRespDTO.setClzz(Map.class);
        HandOverReportQueryDTO handOverReportQueryDTO =
                (HandOverReportQueryDTO) JacksonUtils.toObject(JournalExportTypeEnum.HANDOVER_TYPE.getQueryClzz(), paramString);
        List<HandoverReportRespDTO> reports = handoverClientService.report(handOverReportQueryDTO);
        if (CollectionUtils.isEmpty(reports)) {
            return;
        }
        //拼接表头
        StringBuilder head = new StringBuilder(JournalExportTypeEnum.HANDOVER_TYPE.getHead());
        reports.get(0).getStaffStatisticals()
                .forEach(user -> head.append(user.getUserGuid()).append(":").append(user.getUserName()).append(","));
        exportRespDTO.setHead(head.toString());
        List<Map<String, String>> datas = new ArrayList<>();
        //拼接表格数据
        reports.forEach(report -> {
            Map<String, String> data = new HashMap<>();
            data.put("statisticalType", report.getStatisticalType());
            data.put("statistical", report.getStatistical());
            //员工统计
            report.getStaffStatisticals().forEach(user -> data.put(user.getUserGuid(), user.getStatistical()));
            datas.add(data);
        });
        exportRespDTO.setList(datas);
    }

    private void salesVolumeType(String paramString, ExportRespDTO exportRespDTO) {
        SalesVolumeReqDTO salesVolumeReqDTO =
                (SalesVolumeReqDTO) JacksonUtils.toObject(JournalExportTypeEnum.SALES_VOLUME_TYPE.getQueryClzz(), paramString);
        salesVolumeReqDTO.setCurrentPage(1);
        salesVolumeReqDTO.setPageSize(Integer.MAX_VALUE);
        Page<SalesVolumeRespDTO> salesVolumeRespDTOPage = tradeItemService.pageStoreSaleStatistics(salesVolumeReqDTO);
        List<SalesVolumeStrRespDTO> respDTOList = new ArrayList<>();
        for (SalesVolumeRespDTO data : salesVolumeRespDTOPage.getData()) {
            SalesVolumeStrRespDTO dto = new SalesVolumeStrRespDTO();
            BeanUtils.copyProperties(data, dto);
            dto.setDiscountPrice(data.getDiscountPrice().setScale(2, RoundingMode.HALF_UP).toPlainString());
            dto.setDineInDiscountPrice(data.getDineInDiscountPrice().setScale(2, RoundingMode.HALF_UP).toPlainString());
            dto.setSalesProportion(data.getSalesProportion() + "%");
            dto.setSpotRate(data.getSpotRate() + "%");
            dto.setSalesDineInProportion(data.getSalesDineInProportion() + "%");
            BigDecimal unMeaningValue = BigDecimal.ONE.negate();
            if (data.getDiscountPrice().compareTo(unMeaningValue) == 0) {
                dto.setDiscountPrice("-");
                dto.setDineInDiscountPrice("-");
                dto.setSalesDineInProportion("-");
            }
            respDTOList.add(dto);
        }
        exportRespDTO.setExcelName(JournalExportTypeEnum.SALES_VOLUME_TYPE.getName());
        exportRespDTO.setList(respDTOList);
    }

    private void salesStoreVolumeType(String paramString, ExportRespDTO<SalesVolumeStoreStrRespDTO> exportRespDTO) {
        SalesVolumeReqDTO salesVolumeReqDTO =
                (SalesVolumeReqDTO) JacksonUtils.toObject(JournalExportTypeEnum.SALES_VOLUME_TYPE.getQueryClzz(), paramString);
        salesVolumeReqDTO.setCurrentPage(1);
        salesVolumeReqDTO.setPageSize(Integer.MAX_VALUE);
        salesVolumeReqDTO.setGroupByStoreFlag(true);
        // 按门店统计
        Page<SalesVolumeStoreRespDTO> salesStoreVolumeRespDTOPage = tradeItemService.pageGroupByStoreSaleStatistics(salesVolumeReqDTO);
        List<SalesVolumeStoreStrRespDTO> respDTOList = new ArrayList<>();
        for (SalesVolumeStoreRespDTO data : salesStoreVolumeRespDTOPage.getData()) {
            for (SalesVolumeRespDTO item : data.getItems()) {
                SalesVolumeStoreStrRespDTO dto = new SalesVolumeStoreStrRespDTO();
                BeanUtils.copyProperties(item, dto);
                dto.setDiscountPrice(item.getDiscountPrice().setScale(2, RoundingMode.HALF_UP).toPlainString());
                dto.setDineInDiscountPrice(item.getDineInDiscountPrice().setScale(2, RoundingMode.HALF_UP).toPlainString());
                dto.setSalesProportion(item.getSalesProportion() + "%");
                dto.setSpotRate(item.getSpotRate() + "%");
                dto.setSalesDineInProportion(item.getSalesDineInProportion() + "%");
                BigDecimal unMeaningValue = BigDecimal.ONE.negate();
                if (item.getDiscountPrice().compareTo(unMeaningValue) == 0) {
                    dto.setDiscountPrice("-");
                    dto.setDineInDiscountPrice("-");
                    dto.setSalesDineInProportion("-");
                }
                respDTOList.add(dto);
            }
        }
        exportRespDTO.setExcelName(JournalExportTypeEnum.SALES_VOLUME_TYPE.getName());
        exportRespDTO.setList(respDTOList);
    }
}
