/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:RepeatedException.java
 * Date:2019-12-24
 * Author:terry
 */

package com.holderzone.holder.saas.store.mdm.exception;

public class RepeatedException extends RuntimeException {

    private String code;

    private static final long serialVersionUID = -1365667284393659484L;

    public RepeatedException(String code, String message) {
        super(message);
        this.code = code;
    }

    public RepeatedException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
