package com.holderzone.holder.saas.store.mdm.pipeline.item.inputs;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.ItemSyncDTO;
import com.holderzone.holder.saas.store.mdm.event.AbsErpRocketMqConsumer;
import com.holderzone.holder.saas.store.mdm.pipeline.item.agg.ItemAggService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MainMdmItemListener
 * @date 2019/11/23 下午7:34
 * @description //
 * @program holder
 */

@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.MainConfig.MAIN_MDM_ITEM_TOPIC,
        tags = {
                RocketMqConfig.MainConfig.MAIN_MDM_ITEM_CREATE_TAG,
                RocketMqConfig.MainConfig.MAIN_MDM_ITEM_UPDATE_TAG,
                RocketMqConfig.MainConfig.MAIN_MDM_ITEM_DELETE_TAG
        },
        consumerGroup = RocketMqConfig.MainConfig.MAIN_MDM_ITEM_GROUP
)
public class ItemMdmConsumer extends AbsErpRocketMqConsumer<RocketMqTopic, String> {

    private final ItemAggService mdmItemService;

    @Autowired
    public ItemMdmConsumer(ItemAggService mdmItemService) {
        this.mdmItemService = mdmItemService;
    }

    @Override
    protected boolean doConsumeMsg(String json, MessageExt messageExt) {
        String tags = messageExt.getTags();
        switch (tags) {
            case RocketMqConfig.MainConfig.MAIN_MDM_ITEM_CREATE_TAG:
                mdmItemService.createLocalItem(JacksonUtils.toObject(ItemSyncDTO.class, json));
                break;
            case RocketMqConfig.MainConfig.MAIN_MDM_ITEM_UPDATE_TAG:
                mdmItemService.updateLocalItem(JacksonUtils.toObject(ItemSyncDTO.class, json));
                break;
            case RocketMqConfig.MainConfig.MAIN_MDM_ITEM_DELETE_TAG:
                mdmItemService.deleteLocalItem(JacksonUtils.toObject(ItemSyncDTO.class, json));
                break;
            default:
                log.error("unknown mq tag : {}, message：{}",
                        messageExt.getTags(),
                        json);
                break;
        }
        return true;
    }
}
