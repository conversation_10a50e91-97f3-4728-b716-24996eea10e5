package com.holderzone.holder.saas.store.mdm.pipeline.org.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.domain.BrandDO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.BrandInfoDTO;

import java.util.List;

/**
 * 品牌 服务类
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
public interface BrandService extends IService<BrandDO> {

    void createLocalBrand();

    boolean createLocalBrand(BrandInfoDTO brandInfoDTO);

    boolean updateLocalBrand(BrandInfoDTO brandInfoDTO);

    void deleteLocalBrand(String brandGuid);

    List<BrandDO> shouldInitBrandDOS();
}
