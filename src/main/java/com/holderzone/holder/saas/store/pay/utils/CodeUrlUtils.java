package com.holderzone.holder.saas.store.pay.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.holderzone.framework.security.SecurityManager;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CodeUrlUtils
 * @date 2018/08/21 14:46
 * @description 二维码链接生成二维码地址
 * @program holder-saas-store-trading-center
 */
public class CodeUrlUtils {

    private static final int BLACK = 0xFF000000;

    private static final int WHITE = 0xFFFFFFFF;

    private static final String FORMAT = "jpg";

    private static final String CONTENT = "utf-8";

    private CodeUrlUtils() {
    }

    public static BufferedImage toBufferedImage(BitMatrix matrix) {
        int width = matrix.getWidth();
        int height = matrix.getHeight();
        BufferedImage image = new BufferedImage(width, height,
                BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, matrix.get(x, y) ? BLACK : WHITE);
            }
        }
        return image;
    }

    public static void writeToFile(BitMatrix matrix, String format, File file)
            throws IOException {
        BufferedImage image = toBufferedImage(matrix);
        if (!ImageIO.write(image, format, file)) {
            throw new IOException("Could not write an image of format "
                    + format + " to " + file);
        }
    }

    public static InputStream writeToStream(BitMatrix matrix, String format,
                                            OutputStream stream) throws IOException {
        BufferedImage image = toBufferedImage(matrix);
        if (!ImageIO.write(image, format, stream)) {
            throw new IOException("Could not write an image of format " + format);
        }
        return parseToInputStream(stream);
    }

    public static byte[] writeToByteArray(BitMatrix matrix, String format, OutputStream stream) throws IOException {
        BufferedImage image = toBufferedImage(matrix);
        if (!ImageIO.write(image, format, stream)) {
            throw new IOException("Could not write an image of format " + format);
        }
        return parseToByteArray(stream);
    }

    public static InputStream parseToInputStream(OutputStream out) {
        ByteArrayOutputStream baos = (ByteArrayOutputStream) out;
        return new ByteArrayInputStream(baos.toByteArray());
    }

    public static byte[] parseToByteArray(OutputStream out) {
        ByteArrayOutputStream baos = (ByteArrayOutputStream) out;
        return baos.toByteArray();
    }

    public static File writeToFile(String codeUrl, int height, int width, File file) {
        HashMap<EncodeHintType, String> hints = new HashMap<EncodeHintType, String>();
        hints.put(EncodeHintType.CHARACTER_SET, CONTENT); // 内容所使用字符集编码
        try {
            BitMatrix bitMatrix = new MultiFormatWriter().encode(codeUrl, BarcodeFormat.QR_CODE, width, height, hints);
            CodeUrlUtils.writeToFile(bitMatrix, FORMAT, file);
            return file;
        } catch (WriterException | IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static InputStream writeToInputStream(String codeUrl, int height, int width, OutputStream outputStream) throws Exception {
        HashMap<EncodeHintType, String> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, CONTENT); // 内容所使用字符集编码
        try {
            BitMatrix bitMatrix = new MultiFormatWriter().encode(codeUrl, BarcodeFormat.QR_CODE, width, height, hints);
            return CodeUrlUtils.writeToStream(bitMatrix, FORMAT, outputStream);
        } catch (WriterException e) {
            e.printStackTrace();
        } finally {
            outputStream.close();
        }
        return null;
    }

    public static String writeToString(String codeUrl, int height, int width, OutputStream outputStream) {
        HashMap<EncodeHintType, String> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, CONTENT); // 内容所使用字符集编码
        try {
            BitMatrix bitMatrix = new MultiFormatWriter().encode(codeUrl, BarcodeFormat.QR_CODE, width, height, hints);
            byte[] bytes = writeToByteArray(bitMatrix, FORMAT, outputStream);
            return SecurityManager.entryptBase64(bytes);
        } catch (WriterException | IOException e) {
            e.printStackTrace();
        } finally {
            try {
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
