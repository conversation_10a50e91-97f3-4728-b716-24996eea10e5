package com.holderzone.holder.saas.store.mdm.pipeline.org.inputs;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.holder.saas.store.mdm.aop.RequireSign;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MDMResult;
import com.holderzone.holder.saas.store.mdm.entity.MDMSynDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.BrandInfoDTO;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/sync_brand")
@Api(description = "品牌相关接口")
public class BrandMdmController {

    private final MqUtils mqUtils;

    @Autowired
    public BrandMdmController(MqUtils mqUtils) {
        this.mqUtils = mqUtils;
    }

    /**
     * 创建品牌
     *
     * @param mdmOrgSynDTO DTO
     * @return true-成功，false-失败
     */
    @RequireSign
    @ApiOperation("创建品牌")
    @PostMapping("/create")
    @LogBefore(value = "外部系统创建品牌", logLevel = LogLevel.INFO)
    public MDMResult<String> createBrand(@Validated(BrandInfoDTO.Create.class)
                                         @RequestBody MDMSynDTO<List<BrandInfoDTO>> mdmOrgSynDTO) {
        for (BrandInfoDTO brandInfoDTO : mdmOrgSynDTO.getRequest()) {
            if (brandInfoDTO == null) {
                continue;
            }
            mqUtils.sendMessage(
                    RocketMqConfig.MainConfig.MAIN_MDM_BRAND_TOPIC,
                    RocketMqConfig.MainConfig.MAIN_MDM_BRAND_CREATE_TAG,
                    brandInfoDTO, UserContextUtils.getEnterpriseGuid()
            );
        }

        return MDMResult.success();
    }

    /**
     * 更新品牌
     *
     * @param mdmOrgSynDTO
     * @return true-成功，false-失败
     */
    @RequireSign
    @ApiOperation("修改品牌")
    @PostMapping("/update")
    @LogBefore(value = "外部系统修改品牌", logLevel = LogLevel.INFO)
    public MDMResult<String> updateBrand(@Validated(BrandInfoDTO.Update.class)
                                         @RequestBody MDMSynDTO<List<BrandInfoDTO>> mdmOrgSynDTO) {
        for (BrandInfoDTO brandInfoDTO : mdmOrgSynDTO.getRequest()) {
            if (brandInfoDTO == null) {
                continue;
            }
            mqUtils.sendMessage(
                    RocketMqConfig.MainConfig.MAIN_MDM_BRAND_TOPIC,
                    RocketMqConfig.MainConfig.MAIN_MDM_BRAND_UPDATE_TAG,
                    brandInfoDTO, UserContextUtils.getEnterpriseGuid()
            );
        }

        return MDMResult.success();

    }

    /**
     * 删除
     *
     * @param mdmOrgSynDTO
     * @return true-成功，false-失败
     */
    @RequireSign
    @ApiOperation("删除品牌")
    @PostMapping("/delete")
    @LogBefore(value = "外部系统删除品牌", logLevel = LogLevel.INFO)
    public MDMResult<String> deleteBrand(@Validated(BrandInfoDTO.Delete.class)
                                         @RequestBody MDMSynDTO<List<BrandInfoDTO>> mdmOrgSynDTO) {
        for (BrandInfoDTO brandInfoDTO : mdmOrgSynDTO.getRequest()) {
            if (brandInfoDTO == null) {
                continue;
            }
            mqUtils.sendMessage(
                    RocketMqConfig.MainConfig.MAIN_MDM_BRAND_TOPIC,
                    RocketMqConfig.MainConfig.MAIN_MDM_BRAND_DELETE_TAG,
                    brandInfoDTO, UserContextUtils.getEnterpriseGuid()
            );
        }

        return MDMResult.success();
    }
}
