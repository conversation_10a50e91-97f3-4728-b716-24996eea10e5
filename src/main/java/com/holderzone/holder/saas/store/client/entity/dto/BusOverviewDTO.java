package com.holderzone.holder.saas.store.client.entity.dto;

import com.holderzone.holder.saas.store.client.entity.ddo.TodayDataDO;
import com.holderzone.holder.saas.store.client.entity.ddo.YesterdayCustomerDO;
import com.holderzone.holder.saas.store.client.entity.ddo.YesterdayGoodsDO;
import com.holderzone.holder.saas.store.client.entity.ddo.YesterdayOrderDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@ApiModel("报表-经营数据-营业概况响应实体")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BusOverviewDTO {

    public static BusOverviewDTO DEFAULT = new BusOverviewDTO(TodayDataDO.INSTANCE(), YesterdayCustomerDO.INSTANCE(), YesterdayGoodsDO.INSTANCE(), YesterdayOrderDO.INSTANCE());
    @ApiModelProperty(value = "今日数据")
    private TodayDataDO todayDataDO;
    @ApiModelProperty(value = "昨日客户数据")
    private YesterdayCustomerDO yesterdayCustomerDO;
    @ApiModelProperty(value = "昨日商品数据")
    private YesterdayGoodsDO yesterdayGoodsDO;
    @ApiModelProperty(value = "昨日订单数据")
    private YesterdayOrderDO yesterdayOrderDO;


    public static BusOverviewDTO INSTANCE() {
        return new BusOverviewDTO(TodayDataDO.INSTANCE(), YesterdayCustomerDO.INSTANCE(), YesterdayGoodsDO.INSTANCE(), YesterdayOrderDO.INSTANCE());
    }
}
