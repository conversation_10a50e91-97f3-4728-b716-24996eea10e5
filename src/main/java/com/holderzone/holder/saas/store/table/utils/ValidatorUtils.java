package com.holderzone.holder.saas.store.table.utils;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.validate.Validator;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.table.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/12/27 10:54
 */
public final class ValidatorUtils {

    public static void validatorInitArea(BaseDTO baseDTO) {
        Validator validator = Validator.create()
                .notBlank(baseDTO.getStoreGuid(), "门店Guid")
                .notBlank(baseDTO.getStoreName(), "门店名称");
        validator(validator);
    }

    public static void validatorAreaDTO(AreaDTO areaDTO) {
        Validator validator = Validator.create()
                .notBlank(areaDTO.getAreaName(), "区域名称")
                .notBlank(areaDTO.getStoreName(), "门店名称")
                .notBlank(areaDTO.getStoreGuid(), "门店信息");
        validator(validator);
    }

    public static void validatorUpdateAreaDTO(AreaDTO areaDTO) {
        Validator validator = Validator.create()
                .notBlank(areaDTO.getGuid(), "区域guid")
                .notBlank(areaDTO.getAreaName(), "区域名称")
                .notBlank(areaDTO.getStoreGuid(), "门店信息");
        validator(validator);
    }

    public static void validatorCreateTable(TableBasicDTO tableBasicDTO) {
        Validator validator = Validator.create()
                .notBlank(tableBasicDTO.getAreaGuid(), "桌台对应的区域")
                .notBlank(tableBasicDTO.getAreaName(), "桌台对应的区域名")
                .notNull(tableBasicDTO.getSeats(), "桌台人数")
                .notBlank(tableBasicDTO.getStoreGuid(), "门店guid")
                .notBlank(tableBasicDTO.getStoreName(), "门店名称")
                .notBlank(tableBasicDTO.getTableCode(), "桌号");
        validator(validator);
    }

    public static void validatorBatchCreateTable(TableBatchCreateDTO tableBatchCreateDTO) {
        Validator validator = Validator.create()
                .notBlank(tableBatchCreateDTO.getAreaGuid(), "桌台对应的区域")
                .notBlank(tableBatchCreateDTO.getAreaName(), "桌台对应的区域名")
                .notNull(tableBatchCreateDTO.getStartNum(), "开始数字")
                .notNull(tableBatchCreateDTO.getTotal(), "总数量");
        validator(validator);
    }

    public static void validatorOpenTable(OpenTableDTO openTableDTO) {
        Validator validator = Validator.create()
                .notNull(openTableDTO.getActualGuestsNo(), "实际就餐人数");
        validator(validator);
    }

    public static void validatorOpenAssociatedTable(OpenAssociatedTableDTO openAssociatedTableDTO) {
        Validator validator = Validator.create()
                .notNull(openAssociatedTableDTO.getActualGuestsNo(), "实际就餐人数")
                .notEmpty(openAssociatedTableDTO.getTableGuids(), "联台桌台列表");
        validator(validator);
    }


    public static void validatorCombineTable(TableCombineDTO tableCombineDTO) {
        Validator validator = Validator.create()
                .notBlank(tableCombineDTO.getMainTableGuid(), "主桌号")
                .notNull(tableCombineDTO.getMainOrderGuid(), "主单号")
                .notEmpty(tableCombineDTO.getTableGuidList(), "桌台信息");
        validator(validator);
    }

    public static void validatorUpdateTable(TableBasicDTO tableBasicDTO) {
        Validator validator = Validator.create()
                .notBlank(tableBasicDTO.getGuid(), "业务主键");
        validator(validator);
    }

    public static void validatorCloseTable(CancelOrderReqDTO cancelOrderReqDTO) {
        Validator validator = Validator.create()
                .notBlank(cancelOrderReqDTO.getTableGuid(), "桌台号")
                .notBlank(cancelOrderReqDTO.getOrderGuid(), "订单号");
        validator(validator);
    }

    public static void validatorTurnTable(TurnTableDTO turnTableDTO) {
        Validator validator = Validator.create()
                .notBlank(turnTableDTO.getNewTableGuid(), "转到的桌台guid")
                .notBlank(turnTableDTO.getNewTableAreaGuid(), "转到的桌台区域guid")
                .notBlank(turnTableDTO.getNewTableAreaName(), "转到的桌台区域名")
                .notBlank(turnTableDTO.getNewTableCode(), "转到的桌台号")
                .notBlank(turnTableDTO.getOriginTableGuid(), "原桌台guid")
                .notBlank(turnTableDTO.getOriginTableAreaGuid(), "原区域guid")
                .notBlank(turnTableDTO.getOriginTableAreaName(), "原区域名称")
                .notBlank(turnTableDTO.getOriginTableCode(), "原桌台号");
        validator(validator);
    }

    public static void validatorSeparateTable(TableOrderCombineDTO tableOrderCombineDTO) {
        Validator validator = Validator.create()
                .notBlank(tableOrderCombineDTO.getMainTableGuid(), "主桌guid")
                .notBlank(tableOrderCombineDTO.getMainOrderGuid(), "主桌订单号");
        validator(validator);
    }

    private static void validator(Validator validator) {
        if (!validator.isValid()) {
            throw new ParameterException(validator.getMessage());
        }
    }

}
