package com.holderzone.holder.saas.store.table.controller;

import com.holderzone.holder.saas.store.table.service.LocalizeService;
import com.holderzone.saas.store.dto.table.LocalizeTableOrderReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/13 14:52
 */
@Slf4j
@Api("本地化接口")
@RequestMapping("/localize")
@RestController
@AllArgsConstructor
public class LocalizeController {

    private final LocalizeService localizeService;

    @ApiOperation("本地化上传数据")
    @PostMapping("/upload/tableOrder")
    public boolean uploadTableOrderStatus(@RequestBody LocalizeTableOrderReqDTO localizeTableOrderReqDTO){
        log.info("本地上传数据ing,数据量为：{}",localizeTableOrderReqDTO.getLocalTableOrderList().size());
        return localizeService.uploadLocalData(localizeTableOrderReqDTO);
    }

}