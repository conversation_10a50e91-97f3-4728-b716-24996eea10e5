package com.holderzone.holder.saas.store.mdm.util;

import com.holderzone.framework.canal.starter.extension.RowChangeBody;
import com.holderzone.framework.canal.starter.extension.RowDataBody;
import com.holderzone.framework.canal.starter.util.StringUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.entity.BaseDO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;

public final class DataConvertUtils {

    public static final String INNER_INSERT = "inner_insert";
    public static final String INNER_UPDATE = "inner_update";
    public static final String INNER_DELETE = "inner_delete";
    public static final String OUTER_OPTION = "outer_option";

    private DataConvertUtils() {
    }

    public static boolean shouldSync(String sql) {
        return Stream.of(
                "add column `external_version`",
                "add column external_version",
                "change column `external_version`",
                "change column external_version",
                "modify `external_version`",
                "modify external_version"
        ).anyMatch(s -> sql.toLowerCase().contains(s));
    }

    public static List<String> getBeforeGuid(RowChangeBody rowChangeBody, Function<Map<String, Object>, Object> identifierFunc) {
        return getGuid(rowChangeBody, RowDataBody::getBeforeData, identifierFunc);
    }

    @Deprecated
    public static List<String> getAfterGuid(RowChangeBody rowChangeBody, Function<Map<String, Object>, Object> identifierFunc) {
        return getGuid(rowChangeBody, RowDataBody::getAfterData, identifierFunc);
    }

    public static List<String> getGuid(RowChangeBody rowChangeBody,
                                       Function<RowDataBody, Map<String, Object>> dataSourceFunc,
                                       Function<Map<String, Object>, Object> identifierFunc) {
        return rowChangeBody.getRowDataBodyList().stream()
                .map(dataSourceFunc)
                .map(map -> identifierFunc.apply(map).toString())
                .collect(Collectors.toList());
    }

    @Deprecated
    public static <T extends BaseDO> List<T> getBeforeData(RowChangeBody rowChangeBody, Class<T> clazz) {
        return getData(rowChangeBody, RowDataBody::getBeforeData, clazz);
    }

    public static <T extends BaseDO> List<T> getAfterData(RowChangeBody rowChangeBody, Class<T> clazz) {
        return getData(rowChangeBody, RowDataBody::getAfterData, clazz);
    }

    private static <T extends BaseDO, R> List<R> getData(RowChangeBody rowChangeBody,
                                                         Function<RowDataBody, Map<String, Object>> function,
                                                         Class<T> clazz,
                                                         Function<T, R> toDtoFunc) {
        return getDataStream(rowChangeBody, function, clazz).map(toDtoFunc).collect(Collectors.toList());
    }

    public static <T extends BaseDO, R> List<R> getBeforeDataDTO(RowChangeBody rowChangeBody,
                                                                 Class<T> clazz, Function<T, R> function) {
        return getData(rowChangeBody, RowDataBody::getBeforeData, clazz, function);
    }

    public static <T extends BaseDO, R> List<R> getAfterDataDTO(RowChangeBody rowChangeBody,
                                                                Class<T> clazz, Function<T, R> function) {
        return rowChangeBody.getRowDataBodyList().stream()
                .filter(DataConvertUtils::isInternalOperation)
                .filter(DataConvertUtils::isDataNotDeleted)
                .map(((Function<RowDataBody, Map<String, Object>>) RowDataBody::getAfterData)
                        .andThen(data -> mapToEntity(clazz, data)))
                .map(function).collect(Collectors.toList());
    }

    private static <T extends BaseDO> List<T> getData(RowChangeBody rowChangeBody,
                                                      Function<RowDataBody, Map<String, Object>> function,
                                                      Class<T> clazz) {
        return getDataStream(rowChangeBody, function, clazz).collect(Collectors.toList());
    }

    private static <T extends BaseDO> Stream<T> getDataStream(RowChangeBody rowChangeBody,
                                                              Function<RowDataBody, Map<String, Object>> function,
                                                              Class<T> clazz) {
        return rowChangeBody.getRowDataBodyList().stream()
                .filter(DataConvertUtils::isInternalOperation)
                .map(function.andThen(data -> mapToEntity(clazz, data)));
    }

    public static <T extends BaseDO, R, K> Map<K, List<R>> getAfterDataDtoMap(RowChangeBody rowChangeBody,
                                                                              Class<T> clazz,
                                                                              Function<T, K> groupKeyFunc,
                                                                              Function<T, R> toDtoFunc) {
        return getDataStream(rowChangeBody, RowDataBody::getAfterData, clazz)
                .collect(Collectors.groupingBy(groupKeyFunc, mapping(toDtoFunc, toList())));
    }

    @Deprecated
    public static <T extends BaseDO, R> Map<String, List<R>> getAfterDataDtoMapOfLogic(RowChangeBody rowChangeBody,
                                                                                       Class<T> clazz, Function<T, R> toDtoFunc) {
        return rowChangeBody.getRowDataBodyList().stream()
                .collect(Collectors.groupingBy(
                        DataConvertUtils::logicOperationKey,
                        mapping(rowDataBody -> toDtoFunc.apply(mapToEntity(clazz, rowDataBody.getAfterData())), toList())
                ));
    }

    private static boolean isInternalOperation(RowDataBody r) {
        Boolean externalVersionUpdated = r.getDataUpdated().get("external_version");
        Integer externalVersionValue = (Integer) r.getAfterData().get("external_version");
        return !externalVersionUpdated || externalVersionValue == 0;
    }

    private static boolean isDataNotDeleted(RowDataBody r) {
        Object isDeleted = r.getAfterData().get("is_deleted");
        if (isDeleted == null) {
            isDeleted = r.getAfterData().get("is_delete");
        }
        if (isDeleted instanceof Boolean) {
            return !((Boolean) isDeleted);
        }
        if (isDeleted instanceof Integer) {
            return 0 == (Integer) isDeleted;
        }
        if (isDeleted instanceof Long) {
            return 0 == (Long) isDeleted;
        }
        if (isDeleted instanceof String) {
            return "0".equals(isDeleted);
        }
        return false;
    }

    private static <T extends BaseDO> T mapToEntity(Class<T> clazz, Map<String, Object> data) {
        Map<String, Object> result = new HashMap<>(data.size());
        data.forEach((key, value) -> result.put(StringUtils.underlineToCamel(key), value));
        return JacksonUtils.toObject(clazz, JacksonUtils.writeValueAsString(result));
    }

    @Deprecated
    private static String logicOperationKey(RowDataBody r) {
        Boolean externalVersionUpdated = r.getDataUpdated().get("external_version");
        Integer externalVersionValue = (Integer) r.getAfterData().get("external_version");
        Boolean isDeleted = (Boolean) r.getAfterData().get("is_deleted");
        if (isDeleted == null) {
            isDeleted = (Boolean) r.getAfterData().get("is_delete");
        }
        if (!externalVersionUpdated) {
            return isDeleted ? INNER_DELETE : INNER_UPDATE;
        }
        if (externalVersionValue == 0 && !isDeleted) {
            return INNER_INSERT;
        }
        return OUTER_OPTION;
    }
}
