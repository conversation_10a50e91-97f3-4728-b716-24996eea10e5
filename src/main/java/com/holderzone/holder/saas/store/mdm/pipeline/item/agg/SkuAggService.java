package com.holderzone.holder.saas.store.mdm.pipeline.item.agg;

import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.SkuSyncDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SkuAggService
 * @date 2019/11/23 下午4:33
 * @description //
 * @program holder
 */


public interface SkuAggService {

    void triggerRemoteSku(MdmTriggerType mdmTriggerType);

    /**
     * 智慧门店批量创建商品SKU同步创建至MDM
     * @param mdmSkuSynDTOS
     */
    void createRemoteSku(List<SkuSyncDTO>  mdmSkuSynDTOS);

    /**
     * 智慧门店修改商品SKU同步修改MDM商品SKU
     * @param mdmSkuSynDTO
     */
    void updateRemoteSku(SkuSyncDTO mdmSkuSynDTO);

    /**
     * 智慧门店删除商品SKU同步删除MDM商品SKU
     * @param mdmSkuSynDTOS
     */
    void deleteRemoteSku(List<SkuSyncDTO>  mdmSkuSynDTOS);

    /**
     * MDM主数据创建SKU推送至智慧门店本地同步创建
     * @param mdmSkuSynDTO
     */
    void createLocalSku(SkuSyncDTO mdmSkuSynDTO);

    /**
     * MDM主数据更新SKU推送至智慧门店本地同步更新
     * @param mdmSkuSynDTO
     */
    void updateLocalSku(SkuSyncDTO mdmSkuSynDTO);

    /**
     * MDM主数据删除SKU推送至智慧门店本地同步删除
     * @param mdmSkuSynDTO
     */
    void deleteLocalSku(SkuSyncDTO mdmSkuSynDTO);
}
