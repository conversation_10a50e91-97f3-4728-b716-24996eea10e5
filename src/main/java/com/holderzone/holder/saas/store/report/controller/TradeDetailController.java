package com.holderzone.holder.saas.store.report.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Assert;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.report.constant.ErrorConstant;
import com.holderzone.holder.saas.store.report.helper.ExceptionHelper;
import com.holderzone.holder.saas.store.report.service.TradeDetailService;
import com.holderzone.saas.store.dto.report.query.AggPayServiceChargeQueryDTO;
import com.holderzone.saas.store.dto.report.query.CloudPayConstituteQueryDTO;
import com.holderzone.saas.store.dto.report.query.PaymentConstituteQueryDTO;
import com.holderzone.saas.store.dto.report.query.TradeDetailQueryDTO;
import com.holderzone.saas.store.dto.report.resp.CloudPayConstituteRespDTO;
import com.holderzone.saas.store.dto.report.resp.GrouponTradeDetailRespDTO;
import com.holderzone.saas.store.dto.report.resp.PaymentConstituteRespDTO;
import com.holderzone.saas.store.dto.report.resp.TakeawayTradeDetailRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;


/**
 * 结算明细
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/trade/detail")
public class TradeDetailController {

    private final TradeDetailService tradeDetailService;

    @PostMapping("/groupon")
    public Page<GrouponTradeDetailRespDTO> pageGroupon(@RequestBody TradeDetailQueryDTO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeDetailService.pageGroupon(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "团购结算明细", JacksonUtils.writeValueAsString(query)));
        }
    }

    @PostMapping("/groupon/export")
    public String grouponExport(@RequestBody TradeDetailQueryDTO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeDetailService.exportGroupon(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "团购结算明细导出", JacksonUtils.writeValueAsString(query)));
        }
    }

    @PostMapping("/takeaway")
    public Page<TakeawayTradeDetailRespDTO> pageTakeaway(@RequestBody TradeDetailQueryDTO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeDetailService.pageTakeaway(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "外卖结算明细", JacksonUtils.writeValueAsString(query)));
        }
    }

    @PostMapping("/takeaway/export")
    public String takeawayExport(@RequestBody TradeDetailQueryDTO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeDetailService.exportTakeaway(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "外卖结算明细导出", JacksonUtils.writeValueAsString(query)));
        }
    }

    @ApiOperation(value = "收款构成")
    @PostMapping("/payment/constitute")
    public Page<PaymentConstituteRespDTO> paymentConstitute(@RequestBody @Valid PaymentConstituteQueryDTO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeDetailService.paymentConstitute(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "收款构成", JacksonUtils.writeValueAsString(query)));
        }
    }

    @ApiOperation(value = "收款构成导出")
    @PostMapping("/payment/constitute/export")
    public String paymentConstituteExport(@RequestBody @Valid PaymentConstituteQueryDTO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeDetailService.exportPaymentConstitute(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "收款构成导出", JacksonUtils.writeValueAsString(query)));
        }
    }

    @ApiOperation(value = "云收款统计")
    @PostMapping("/cloud/pay/constitute")
    public CloudPayConstituteRespDTO cloudPayConstitute(@RequestBody CloudPayConstituteQueryDTO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeDetailService.cloudPayConstitute(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "云收款统计", JacksonUtils.writeValueAsString(query)));
        }
    }

    @ApiOperation(value = "聚合支付手续费")
    @PostMapping("/query_agg_pay_service_charge")
    public BigDecimal queryAggPayServiceCharge(@RequestBody AggPayServiceChargeQueryDTO query) {
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeDetailService.queryAggPayServiceCharge(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "聚合支付手续费", JacksonUtils.writeValueAsString(query)));
        }
    }

}
