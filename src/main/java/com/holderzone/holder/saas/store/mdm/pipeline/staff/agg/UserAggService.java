package com.holderzone.holder.saas.store.mdm.pipeline.staff.agg;

import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.UserSyncDTO;

import java.util.List;

public interface UserAggService {

    void createLocalUser(UserSyncDTO userSyncDTO);

    void updateLocalUser(UserSyncDTO userSyncDTO);

    void deleteLocalUser(UserSyncDTO userSyncDTO);

    void triggerRemoteUser(MdmTriggerType mdmTriggerType);

    void createRemoteUser(List<UserSyncDTO> userSyncDTOList);

    void updateRemoteUser(UserSyncDTO userSyncDTO);

    void deleteRemoteUser(List<String> guidList);
}
