package com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.holderzone.holder.saas.store.mdm.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("hss_user")
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UserDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 用户GUID
     */
    private String guid;

    /**
     * 企业编码
     */
    private String enterpriseNo;

    /**
     * 员工账号
     */
    private String account;

    /**
     * 员工密码
     */
    private String password;

    /**
     * 授权码
     */
    private String authCode;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 员工所属组织，单组织节点Guid
     */
    private String orgGuid;

    /**
     * 员工职位（暂未使用，目前使用数据字典形式，只增查不删改，故guid暂时无用）
     */
    private Integer officeCode;

    /**
     * 员工职位名称
     */
    private String officeName;

    /**
     * 员工身份证号码
     */
    private String idCardNo;

    /**
     * 员工身份证地址
     */
    private String idCardAddress;

    /**
     * 员工居住地址
     */
    private String address;

    /**
     * 员工生日
     */
    private LocalDateTime birthday;

    /**
     * 员工入职时间
     */
    private LocalDateTime onBoardingTime;

    /**
     * 整单折扣阈值
     */
    private BigDecimal discountThreshold;

    /**
     * 整单让价阈值
     */
    private BigDecimal allowanceThreshold;

    /**
     * 单品折扣阈值
     */
    private BigDecimal productDiscountThreshold;

    /**
     * 退款金额差值阈值
     */
    private BigDecimal refundThreshold;

    /**
     * 用户可分配的角色id，逗号分割
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String rolesDistributable;

    /**
     * 创建人GUID
     */
    private String createStaffGuid;

    /**
     * 更新人GUID
     */
    private String updateStaffGuid;

    /**
     * 是否启用：0=禁用，1=启用
     */
    private Boolean isEnable;

    /**
     * 是否删除：0=未删除，1=已删除
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 记录创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 记录修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * @see com.holderzone.holder.saas.store.mdm.constant.RegTypeEnum
     * 注册来源
     */
    private String regType;

    /**
     * 是否一体化
     */
    private Boolean integrateFlag;
}
