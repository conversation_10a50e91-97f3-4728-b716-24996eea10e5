package com.holderzone.holder.saas.store.table.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.table.constant.Constant;
import com.holderzone.holder.saas.store.table.domain.TableTagDO;
import com.holderzone.holder.saas.store.table.mapper.TableTagMapper;
import com.holderzone.holder.saas.store.table.service.RedisService;
import com.holderzone.holder.saas.store.table.service.TableTagService;
import com.holderzone.holder.saas.store.table.utils.PageAdapter;
import com.holderzone.holder.saas.store.table.utils.TableMapStruct;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.table.TableTagDTO;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/04 16:33
 */
@Service
public class TableTagServiceImpl extends ServiceImpl<TableTagMapper, TableTagDO> implements TableTagService {

    private final RedisService redisService;

    public TableTagServiceImpl(RedisService redisService) {
        this.redisService = redisService;
    }

    /**
     * 创建标签
     *
     * @param tableTagDTO
     * @return
     */
    @Override
    public boolean createTag(TableTagDTO tableTagDTO) {
        Assert.notNull(tableTagDTO.getStoreGuid(), "门店GUID不能为空");
        Assert.isTrue(!hasSameNameTag(tableTagDTO), "重复名字标签");
        tableTagDTO.setGuid(redisService.singleGuid(Constant.TABLE_TAG));
        TableTagDO tableTagDO = TableMapStruct.TABLE_MAP_STRUCT.createTableDO(tableTagDTO);
        return save(tableTagDO);
    }

    /**
     * 标签去重
     *
     * @param tableTagDTO
     * @return
     */
    public boolean hasSameNameTag(TableTagDTO tableTagDTO) {
        int count = count(new QueryWrapper<TableTagDO>().lambda().eq(TableTagDO::getStoreGuid, tableTagDTO.getStoreGuid())
                .eq(TableTagDO::getTagName, tableTagDTO.getTagName()));
        return count != 0;
    }

    /**
     * 查询标签
     *
     * @param basePageDTO
     * @return
     */
    @Override
    public Page<TableTagDTO> listTag(BasePageDTO basePageDTO) {
        Assert.notNull(basePageDTO.getStoreGuid(), "门店GUID不能为空");
        IPage<TableTagDO> page = page(new PageAdapter(basePageDTO), new QueryWrapper<TableTagDO>().lambda()
                .eq(TableTagDO::getStoreGuid, basePageDTO.getStoreGuid())
                .orderByDesc(TableTagDO::getSort, TableTagDO::getGmtModified));
        List<TableTagDTO> list = TableMapStruct.TABLE_MAP_STRUCT.turnTagList(page.getRecords());
        return new PageAdapter<>(page, list);
    }

    /**
     * 更改标签
     *
     * @param tableTagDTO
     * @return 更改标签结果
     */
    @Override
    public boolean updateTag(TableTagDTO tableTagDTO) {
        Assert.notNull(tableTagDTO.getStoreGuid(), "门店GUID不能为空");
        boolean updateResult = update(new TableTagDO(), new UpdateWrapper<TableTagDO>().lambda()
                .eq(TableTagDO::getGuid, tableTagDTO.getGuid())
                .eq(TableTagDO::getStoreGuid, tableTagDTO.getStoreGuid())
                .set(TableTagDO::getTagName, tableTagDTO.getTagName()));
        if (!updateResult) {
            TableTagDO one = getOne(new QueryWrapper<TableTagDO>().lambda().eq(TableTagDO::getGuid, tableTagDTO.getGuid()));
            Assert.isNull(one, "门店GUID传入错误");
        }
        return updateResult;
    }

    /**
     * 删除标签
     *
     * @param tableTagDTO
     * @return 删除标签结果
     */
    @Override
    public boolean deleteTag(TableTagDTO tableTagDTO) {
        Assert.notNull(tableTagDTO.getStoreGuid(), "门店GUID不能为空");
        Assert.notNull(tableTagDTO.getGuid(), "Tag GUID不能为空");
        boolean removeResult = remove(new UpdateWrapper<TableTagDO>().lambda()
                .eq(TableTagDO::getGuid, tableTagDTO.getGuid())
                .eq(TableTagDO::getStoreGuid, tableTagDTO.getStoreGuid()));
        if (!removeResult) {
            TableTagDO one = getOne(new QueryWrapper<TableTagDO>().lambda().eq(TableTagDO::getGuid, tableTagDTO.getGuid()));
            Assert.isNull(one, "门店GUID传入错误");
        }
        return removeResult;
    }

    /**
     * 根据桌台标签列表查询标签信息，返回DTO map
     *
     * @param tableTagGuidList
     * @return guid和实体之间的映射
     */
    @Override
    public Map<String, TableTagDTO> getTagWithGuidMap(List<String> tableTagGuidList) {
        if (CollectionUtils.isEmpty(tableTagGuidList)) {
            return Maps.newHashMap();
        }
        List<TableTagDO> tableTagList = list(new QueryWrapper<TableTagDO>().lambda().in(TableTagDO::getGuid, tableTagGuidList));
        List<TableTagDTO> tableTagDTOS = TableMapStruct.TABLE_MAP_STRUCT.turnTagList(tableTagList);
        return tableTagDTOS.stream().collect(Collectors.toMap(TableTagDTO::getGuid, Function.identity()));
    }

}