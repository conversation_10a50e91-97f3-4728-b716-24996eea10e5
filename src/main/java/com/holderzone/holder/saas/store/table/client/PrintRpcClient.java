package com.holderzone.holder.saas.store.table.client;

import com.holderzone.saas.store.dto.print.content.PrintDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/02/15 16:58
 */
@Component
@FeignClient(value = "holder-saas-store-print", fallbackFactory = PrintRpcClient.PrintClientFallBack.class)
public interface PrintRpcClient {

    /**
     * 打印，连接打印服务
     * @param printDTO  打印请求参数
     * @return  打印结果
     */
    @PostMapping("/print_record/send")
    String print(PrintDTO printDTO);

    @Slf4j
    @Component
    class PrintClientFallBack implements FallbackFactory<PrintRpcClient> {

        @Override
        public PrintRpcClient create(Throwable throwable) {
            return printDTO -> {
                log.error("打印出错 e={}", throwable.getMessage());
                return null;
            };

        }

    }

}
