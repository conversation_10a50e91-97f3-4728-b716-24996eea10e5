package com.holderzone.holder.saas.store.mdm.pipeline.item.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.dynamic.datasource.starter.anno.SwitchServerCodeAnno;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.ItemSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.ItemDO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.mapper.ItemMapper;
import com.holderzone.holder.saas.store.mdm.pipeline.item.mapstruct.ItemMapStruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemServiceImpl
 * @date 2019/11/23 下午5:02
 * @description //
 * @program holder
 */
@Slf4j
@Service
public class ItemServiceImpl extends ServiceImpl<ItemMapper, ItemDO> implements ItemService {


    private final ItemMapStruct itemMapStruct;

    @Autowired
    public ItemServiceImpl(ItemMapStruct itemMapStruct) {
        this.itemMapStruct = itemMapStruct;
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public void insertItem(ItemSyncDTO itemSyncDTO) {
        ItemDO itemDO = itemMapStruct.itemSynDTO2itemDO(itemSyncDTO);
        itemDO.setHasAttr(0);
        itemDO.setSort(999);
        itemDO.setIsNew(0);
        itemDO.setIsBestseller(0);
        itemDO.setIsSign(0);
        itemDO.setNameChange(0);
        this.save(itemDO);
        log.info("insert 智慧门店保存 item = {} ,success！", JacksonUtils.writeValueAsString(itemDO));
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public void updateItemByGuid(ItemSyncDTO itemSyncDTO) {
        ItemDO itemDO = itemMapStruct.itemSynDTO2itemDO(itemSyncDTO);
        this.update(itemDO, new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getGuid, itemDO.getGuid()));
        log.info("update 智慧门店更新 item = {} ,success！", JacksonUtils.writeValueAsString(itemDO));
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public void deleteItemByGuid(String guid) {
        this.remove(new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getGuid, guid));
        log.info("delete 智慧门店删除 item = {} ,success！", guid);
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public ItemDO getLocalRerocd(Wrapper<ItemDO> queryWrapper) {
        return getOne(queryWrapper);
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_item")
    public List<ItemDO> shouldInitItemDOS() {
        return this.list(new LambdaQueryWrapper<ItemDO>()
                .ne(ItemDO::getItemType, 1)
                .ne(ItemDO::getItemType, 5)
        );
    }
}
