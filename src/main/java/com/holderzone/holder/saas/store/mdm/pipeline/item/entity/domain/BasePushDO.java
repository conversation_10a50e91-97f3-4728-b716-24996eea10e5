package com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.holder.saas.store.mdm.entity.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BasePushDO
 * @date 2019/02/25 下午2:34
 * @description BasePushDO
 * @program holder-saas-store-item
 */
@Data
public class BasePushDO extends BaseDO {

    /**
     * 主键
     */
    private Long id;

    @JsonIgnore
    private LocalDateTime gmtCreate;

    @JsonIgnore
    private LocalDateTime gmtModified;

    @TableLogic
    private Integer isDelete;

    /**
     * 分类/商品/sku GUID
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;


    /**
     * 品牌GUID
     */
    private String brandGuid;

    /**
     * 门店GUID
     */
    protected String storeGuid;

    /**
     * 父实体GUID：如果是自己创建的内容，则此字段为空，如果是被推送过来的实体，则该字段为品牌库对应的实体GUID。
     */
    protected String parentGuid;

    /**
     * 名称
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    protected String name;

}
