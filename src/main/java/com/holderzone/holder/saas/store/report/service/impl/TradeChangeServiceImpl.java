package com.holderzone.holder.saas.store.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.holder.saas.store.report.constant.CommonConstant;
import com.holderzone.holder.saas.store.report.dto.ChangeDetailExcelDTO;
import com.holderzone.holder.saas.store.report.mapper.TradeChangeMapper;
import com.holderzone.holder.saas.store.report.service.TradeChangeService;
import com.holderzone.holder.saas.store.report.util.ExcelUtils;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.base.Pager;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ChangeDetailDTO;
import com.holderzone.saas.store.enums.order.OrderItemChangeNodeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;


@Service
@RequiredArgsConstructor
public class TradeChangeServiceImpl implements TradeChangeService {

    private final TradeChangeMapper tradeChangeMapper;

    private final OssClient ossClient;

    private static final String MONEY_FULL = "￥";

    @Override
    public Message<ChangeDetailDTO> list(ReportQueryVO query) {
        checkParams(query);
        Message<ChangeDetailDTO> changeDetailMessage = new Message<>();
        Integer count = tradeChangeMapper.count(query);
        if (count == 0) {
            Pager pager = new Pager(query.getCurrentPage(), query.getPageSize(), 0);
            changeDetailMessage.setPager(pager);
            changeDetailMessage.setList(Lists.newArrayList());
            changeDetailMessage.setData(null);
            return changeDetailMessage;
        }
        // 查询列表
        List<ChangeDetailDTO> list = tradeChangeMapper.pageInfo(query);
        // 列表处理
        list.forEach(this::changeDetailsInnerHandler);
        Pager pager = new Pager(query.getCurrentPage(), query.getPageSize(), count);
        changeDetailMessage.setPager(pager);
        changeDetailMessage.setList(list);
        changeDetailMessage.setData(null);
        return changeDetailMessage;
    }


    @Override
    public String export(ReportQueryVO query) {
        Integer count = tradeChangeMapper.count(query);
        if (count > CommonConstant.MAX_EXPORT) {
            throw new BusinessException(CommonConstant.MAX_EXPORT_TITLE);
        }
        List<ChangeDetailExcelDTO> excelList = Lists.newArrayList();
        if (count > 0) {
            query.setPageSize(CommonConstant.MAX_EXPORT);
            tradeChangeMapper.pageInfo(query).forEach(e -> {
                // 列表处理
                changeDetailsInnerHandler(e);
                ChangeDetailExcelDTO changeDetailExcel = new ChangeDetailExcelDTO();
                BeanUtil.copyProperties(e, changeDetailExcel);
                // 导出字段处理
                changeDetailsExportHandler(e, changeDetailExcel);
                changeDetailExcel.setChangeTime(e.getChangeTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                excelList.add(changeDetailExcel);
            });
        }
        try {
            String sheetName = "套餐换菜明细";
            String fileName = sheetName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return ExcelUtils.exportExcel(excelList,
                    null, sheetName, ChangeDetailExcelDTO.class, fileName, ossClient);
        } catch (Exception e) {
            throw new BusinessException("导出失败");
        }
    }

    /**
     * 参数校验
     */
    private void checkParams(ReportQueryVO query) {
        if (ObjectUtil.isNull(query.getStartTime()) || ObjectUtil.isNull(query.getEndTime())) {
            throw new BusinessException(CommonConstant.REQUEST_TIME_NOT_EMPTY);
        }
        long days = query.getEndTime().toEpochDay() - query.getStartTime().toEpochDay();
        if (days > CommonConstant.QUERY_MAX_DAY) {
            throw new BusinessException(CommonConstant.REQUEST_TIME_EXCEED_A_MONTH);
        }
    }

    /**
     * 列表字段处理
     */
    private void changeDetailsInnerHandler(ChangeDetailDTO changeDetailDTO) {
        // 原菜品名称
        changeDetailDTO.setOriginalItemName(changeDetailDTO.getOriginalItemName().replace("\\n", "\n"));
        // 原菜品单价
        changeDetailDTO.setOriginalItemPrice(changeDetailDTO.getOriginalItemPrice().replace("\\n", "\n"));
        // 原菜品更换数量
        changeDetailDTO.setOriginalItemCount(changeDetailDTO.getOriginalItemCount().replace("\\n", "\n"));
        // 更换菜品
        changeDetailDTO.setChangeItemName(changeDetailDTO.getChangeItemName().replace("\\n", "\n"));
        // 更换数量
        changeDetailDTO.setChangeItemCount(changeDetailDTO.getChangeItemCount().replace("\\n", "\n"));
        // 更换单价
        changeDetailDTO.setChangeItemPrice(changeDetailDTO.getChangeItemPrice().replace("\\n", "\n"));
        // 换菜节点
        changeDetailDTO.setChangeNode(OrderItemChangeNodeEnum.getDescByNode(changeDetailDTO.getChangeNode()));
        // 总差额
        changeDetailDTO.setChangePrice(changeDetailDTO.getOriginalItemTotalPrice().subtract(changeDetailDTO.getChangeItemTotalPrice()));
    }

    /**
     * 导出字段处理
     */
    private void changeDetailsExportHandler(ChangeDetailDTO changeDetailDTO, ChangeDetailExcelDTO changeDetailExcelDTO) {
        // 原菜品合计
        changeDetailExcelDTO.setOriginalItemTotalPrice(MONEY_FULL + changeDetailDTO.getOriginalItemTotalPrice().stripTrailingZeros().toPlainString());
        // 更换菜品合计
        changeDetailExcelDTO.setChangeItemTotalPrice(MONEY_FULL + changeDetailDTO.getChangeItemTotalPrice().stripTrailingZeros().toPlainString());
        // 总差额
        if (changeDetailDTO.getChangePrice().compareTo(BigDecimal.ZERO) >= 0) {
            changeDetailExcelDTO.setChangePrice(MONEY_FULL + changeDetailDTO.getChangePrice().stripTrailingZeros().toPlainString());
        } else {
            changeDetailExcelDTO.setChangePrice("-" + MONEY_FULL + changeDetailDTO.getChangePrice().abs().stripTrailingZeros().toPlainString());
        }
    }
}
