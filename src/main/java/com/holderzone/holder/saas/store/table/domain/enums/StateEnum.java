package com.holderzone.holder.saas.store.table.domain.enums;


/**
 * 如果有修改状态类型的定义，请修改BusinessOrderStatisticsQueryDTO中的定义，目前枚举类不在dto项目，考虑迁移影响大。请悉知。
 *
 * <AUTHOR>
 * @description 订单状态枚举
 * @date 2021/10/12 22:12
 * @className: StateEnum
 */
public enum StateEnum {

    READY(1, "待支付"),
    PENDING(2, "支付中"),
    FAILURE(3, "支付失败"),
    SUCCESS(4, "支付成功"),
    REFUNDED(5, "退款"),
    CANCEL(6, "已作废"),
    ANTI_SETTLEMENT(7, "反结账");

    private int code;
    private String desc;

    StateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (StateEnum c : StateEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
