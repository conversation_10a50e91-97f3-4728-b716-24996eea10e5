package com.holderzone.holder.saas.store.table.utils;

import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/10/25 15:28
 */
@Component
public class DynamicHelper {

    @Value("${self.open-dynamic-datasource}")
    private Boolean openDynamicDatasource;

    public void changeDatasource(String enterpriseGuid) {
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
    }

    public void clear(){
        EnterpriseIdentifier.remove();
    }
}
