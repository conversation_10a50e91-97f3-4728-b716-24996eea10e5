package com.holderzone.holder.saas.store.report.mapper;

import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.FreeItemDTO;
import com.holderzone.saas.store.dto.report.resp.TotalStatisticsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单赠菜
 */
@Mapper
public interface TradeFreeMapper {

    TotalStatisticsDTO statistics(@Param("query") ReportQueryVO query);

    List<FreeItemDTO> pageInfo(@Param("query") ReportQueryVO query);

    Integer count(@Param("query") ReportQueryVO query);

}
