package com.holderzone.holder.saas.store.deposit.mapstruct;

import com.holderzone.holder.saas.store.deposit.entity.bo.OperationDO;
import com.holderzone.holder.saas.store.deposit.entity.bo.OperationGoodsDO;
import com.holderzone.saas.store.dto.deposit.req.OperationCreateReqDTO;
import com.holderzone.saas.store.dto.deposit.resp.GoodsSimpleRespDTO;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper(componentModel = "spring")
public interface OperationMapstruct {

    OperationDO fromOperationDTO(OperationCreateReqDTO operationCreateReqDTO);

    List<GoodsSimpleRespDTO> fromGoodsOfOperation(List<OperationGoodsDO> list);

}
