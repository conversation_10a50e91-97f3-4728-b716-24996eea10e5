package com.holderzone.holder.saas.store.report.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SwaggerConfig
 * @date 2018/09/26 15:38
 * @description
 * @program holder-saas-report
 */
@Configuration
public class SwaggerConfig {

    /**
     swagger2的配置文件，这里可以配置swagger2的一些基本的内容，比如扫描的包等等
     *
     */
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                //为当前包路径
                .apis(RequestHandlerSelectors.basePackage("com.holderzone.holder.saas.store.report.controller"))
                .paths(PathSelectors.any())
                .build();
    }

    /**
     *构建 api文档的详细信息函数,注意这里的注解引用的是哪个
     * @return
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                //页面标题
                .title("商户后台 报表 API")
                //版本号
                .version("1.0")
                //描述
                .description("API 描述")
                .build();
    }
}
