package com.holderzone.holder.saas.store.table.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.store.table.domain.TableTagRelationDO;
import com.holderzone.saas.store.dto.table.TableTagDTO;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableTagRelationService
 * @date 2019/12/05 16:23
 * @description //TODO
 * @program IdeaProjects
 */
public interface TableTagRelationService extends IService<TableTagRelationDO> {

    @Transactional(propagation = Propagation.SUPPORTS)
    void removeTagByTableIds(String... tableids);

    @Transactional(propagation = Propagation.SUPPORTS)
    void removeTagByTableIds(List<String> tableids);

    @Transactional(propagation = Propagation.SUPPORTS)
    boolean removeTagByTagGuid(String tagGuid);

    @Transactional(propagation = Propagation.SUPPORTS)
    void createTags(List<String> tagGuidList, String tableGuid);

    @Transactional(propagation = Propagation.SUPPORTS)
    void createTags(List<String> tagGuidList, List<String> tableGuids);

    Map<String, List<TableTagDTO>> getTagInfoByTableInfos(List<String> tableGuids);

}
