package com.holderzone.holder.saas.store.deposit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.store.deposit.entity.bo.OperationGoodsDO;
import com.holderzone.saas.store.dto.deposit.resp.GoodsSimpleRespDTO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-22
 */
public interface IHsdOperationGoodsService extends IService<OperationGoodsDO> {

    /**
     * 查询菜品
     *
     * @param operationGuid,depositGuid
     * @return
     */
    List<GoodsSimpleRespDTO> queryGoodsOfOperation(String operationGuid,String depositGuid);

}
