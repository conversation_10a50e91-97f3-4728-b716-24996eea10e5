package com.holderzone.holder.saas.store.table.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.table.client.BindupAccountsClientService;
import com.holderzone.holder.saas.store.table.client.OrganizationClientService;
import com.holderzone.holder.saas.store.table.service.BindUpAccountsService;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.store.store.BindupAccountsDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 扎帐相关，校验是否可以开台
 */
@Slf4j
@Service
public class BindUpAccountsServiceImpl implements BindUpAccountsService {

    @Autowired
    private OrganizationClientService organizationClientService;

    @Autowired
    private BindupAccountsClientService bindupAccountsClientService;

    /**
     * 当前时间的营业日
     */
    @Override
    public LocalDate currentTimeDay(String storeGuid, LocalDateTime localDateTime) {
        BusinessDateReqDTO reqDTO = new BusinessDateReqDTO();
        reqDTO.setStoreGuidList(Lists.newArrayList(storeGuid));
        if (localDateTime != null) {
            reqDTO.setQueryDateTime(localDateTime);
        }
        return organizationClientService.queryBusinessDay(reqDTO);
    }

    @Override
    public void sendMqForCanOpenTable(String storeGuid) {
        bindupAccountsClientService.sendMqForCanOpenTable(storeGuid);
    }

    /**
     * 昨日是否已扎帐
     * 桌台状态信息，是否已扎帐
     */
    @Override
    public boolean checkBindUpAccountStatus(String storeGuid) {
        StoreDTO storeDTO = organizationClientService.queryStoreByGuid(storeGuid);
        log.info("获取门店信息：{}", JacksonUtils.writeValueAsString(storeDTO));
        Integer isBuAccounts = storeDTO.getIsBuAccounts();
        //是否强制扎帐
        if (isBuAccounts == 0) {
            return false;
        }
        //最新扎帐日期
        BindupAccountsDTO lastBuAccounts = bindupAccountsClientService.queryBindUpAccountsLast(storeGuid);
        log.info("获取最新扎帐日期信息：{}", JacksonUtils.writeValueAsString(lastBuAccounts));
        //从未扎帐
        if (lastBuAccounts == null || lastBuAccounts.getBindupAccounts() == null) {
            return false;
        }
        LocalDate localDate = this.currentTimeDay(storeGuid, null);
        LocalDate yesterdayTime = localDate.plusDays(-1);
        LocalDateTime bindupAccounts = lastBuAccounts.getBindupAccounts();
        LocalDate storeLastBuAccounts = bindupAccounts.toLocalDate();
        log.info("扎帐时间对比storeLastBuAccounts：{}", JacksonUtils.writeValueAsString(storeLastBuAccounts));
        log.info("扎帐时间对比yesterdayTime：{}", JacksonUtils.writeValueAsString(yesterdayTime));
        // 昨日未扎帐
        return storeLastBuAccounts.compareTo(yesterdayTime) >= 0;
    }
}
