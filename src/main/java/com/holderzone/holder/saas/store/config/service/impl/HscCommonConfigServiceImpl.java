package com.holderzone.holder.saas.store.config.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.config.entity.domain.HscCommonConfigDO;
import com.holderzone.holder.saas.store.config.mapper.HscCommonConfigMapper;
import com.holderzone.holder.saas.store.config.service.HscCommonConfigService;
import com.holderzone.holder.saas.store.config.utils.DynamicHelper;
import com.holderzone.holder.saas.store.config.utils.MapStructUtils;
import com.holderzone.saas.store.dto.config.req.ConfigReqDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReqQueryDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReverseQueryDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 商户配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-08
 */
@Service
@Slf4j
public class HscCommonConfigServiceImpl extends ServiceImpl<HscCommonConfigMapper, HscCommonConfigDO> implements HscCommonConfigService {

    @Autowired
    private DynamicHelper dynamicHelper;

    @Override
    public Boolean saveConfig(ConfigReqDTO request) {
        log.info("request object={}", JacksonUtils.writeValueAsString(request));
        HscCommonConfigDO commonConfigDO = MapStructUtils.INSTANCE.configReqDTO2CommonConfigDO(request);
        log.info("commonConfigDO object={}", JacksonUtils.writeValueAsString(commonConfigDO));
        HscCommonConfigDO dbConfig = getOne(new LambdaQueryWrapper<HscCommonConfigDO>()
                .eq(HscCommonConfigDO::getStoreGuid, request.getStoreGuid())
                .eq(HscCommonConfigDO::getDicCode, request.getDicCode()));
        if (!Optional.ofNullable(commonConfigDO).map(HscCommonConfigDO::getGuid).isPresent() && Objects.isNull(dbConfig)) {
            String guid = dynamicHelper.generateGuid();
            commonConfigDO.setGuid(guid);
            commonConfigDO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        }else{
            commonConfigDO.setGuid(dbConfig.getGuid());
        }
        log.info("insert db object={}", JacksonUtils.writeValueAsString(commonConfigDO));
        return super.saveOrUpdate(commonConfigDO);
    }

    @Override
    public ConfigRespDTO getConfig(ConfigReqQueryDTO request) {
        HscCommonConfigDO configDO = getOne(new LambdaQueryWrapper<HscCommonConfigDO>()
                .eq(HscCommonConfigDO::getDicCode, request.getDicCode())
                .eq(HscCommonConfigDO::getStoreGuid, request.getStoreGuid())
                .eq(HscCommonConfigDO::getEnterpriseGuid, UserContextUtils.getEnterpriseGuid()));
        ConfigRespDTO configRespDTO = MapStructUtils.INSTANCE.commonConfigDO2configRespDTO(configDO);
        return configRespDTO;
    }

    @Override
    public void deleteConfig(ConfigReqDTO request) {
        remove(new LambdaQueryWrapper<HscCommonConfigDO>()
                .eq(HscCommonConfigDO::getDicCode, request.getDicCode())
                .eq(HscCommonConfigDO::getStoreGuid, request.getStoreGuid())
                .eq(HscCommonConfigDO::getEnterpriseGuid, UserContextUtils.getEnterpriseGuid()));
    }

    @Override
    public List<ConfigRespDTO> getConfig(ConfigReqDTO request) {
        LambdaQueryWrapper<HscCommonConfigDO> lambdaQueryWrapper = new LambdaQueryWrapper<HscCommonConfigDO>();
        //查询类型
        lambdaQueryWrapper.eq(HscCommonConfigDO::getDicCode, request.getDicCode());
        //启用状态
        lambdaQueryWrapper.eq(HscCommonConfigDO::getIsEnable, request.getIsEnable());
        if (Optional.ofNullable(request).map(ConfigReqDTO::getEnterpriseGuid).isPresent()) {
            lambdaQueryWrapper.eq(HscCommonConfigDO::getEnterpriseGuid, request.getEnterpriseGuid());
        }
        if (Optional.ofNullable(request).map(ConfigReqDTO::getStoreGuid).isPresent()) {
            lambdaQueryWrapper.eq(HscCommonConfigDO::getStoreGuid, request.getStoreGuid());
        }
        if (Optional.ofNullable(request).map(ConfigReqDTO::getDictValue).isPresent()) {
            lambdaQueryWrapper.eq(HscCommonConfigDO::getDictValue, request.getDictValue());
        }
        List<HscCommonConfigDO> configDOS = list(lambdaQueryWrapper);
        List<ConfigRespDTO> configRespDTOs = MapStructUtils.INSTANCE.commonConfigDO2configRespDTOs(configDOS);
        return configRespDTOs;
    }

    @Override
    public ConfigRespDTO getConfigByCodeValue(ConfigReverseQueryDTO request) {
        List<HscCommonConfigDO> configDO = list(new LambdaQueryWrapper<HscCommonConfigDO>()
                .eq(HscCommonConfigDO::getDicCode, request.getDicCode())
                .eq(HscCommonConfigDO::getDictValue, request.getDicValue()));
        if (CollectionUtils.isEmpty(configDO)) {
            return null;
        }
        if (configDO.size() > 1) {
            String collect = configDO.stream()
                    .map(hscCommonConfigDO -> hscCommonConfigDO.getEnterpriseGuid()
                            + "/" + hscCommonConfigDO.getStoreGuid())
                    .collect(Collectors.joining("，"));
            log.error("以下多个门店云呼预订号码重复：{}", collect);
            removeByIds(configDO.stream()
                    .map(HscCommonConfigDO::getId)
                    .collect(Collectors.toList()));
            return null;
        }
        return MapStructUtils.INSTANCE.commonConfigDO2configRespDTO(configDO.get(0));
    }

}
