package com.holderzone.holder.saas.store.pay.config;
import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import reactor.netty.http.client.HttpClient;
import java.time.Duration;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-07-02
 * @description HttpClient配置
 */
@Slf4j
@Configuration
public class HttpClientConfig {

    @Bean
    public HttpClient customHttpClient(){

        return HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 8000)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .responseTimeout(Duration.ofSeconds(8))
                .keepAlive(true)
                //连接成功
                .doOnConnected(connection -> connection.addHandlerLast(new ReadTimeoutHandler(8))
                        .addHandlerLast(new WriteTimeoutHandler(8)))
                //每次请求后执行flush，防止服务器主动断开连接
                .doAfterRequest((httpClientRequest, connection) -> {
                    connection.channel().alloc().buffer().release();
                    connection.channel().flush();
                    connection.channel().pipeline().flush();
                });
    }

    @Bean
    @ConditionalOnMissingBean
    public HttpMessageConverters messageConverters(ObjectProvider<HttpMessageConverter<?>> converters){
        return new HttpMessageConverters(converters.orderedStream().collect(Collectors.toList()));
    }
}
