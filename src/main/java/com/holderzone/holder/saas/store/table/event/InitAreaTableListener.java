package com.holderzone.holder.saas.store.table.event;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.holder.saas.store.table.constant.RocketMqConstant;
import com.holderzone.holder.saas.store.table.service.AreaService;
import com.holderzone.holder.saas.store.table.utils.ThrowableExtUtils;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/04 16:18
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConstant.DOWNSTREAM_STORE_TOPIC,
        tags = RocketMqConstant.DOWNSTREAM_STORE_CREATE_TAG,
        consumerGroup = RocketMqConstant.DOWNSTREAM_STORE_INIT_TABLE_GROUP
)
public class InitAreaTableListener extends AbstractRocketMqConsumer<RocketMqTopic, StoreDTO> {

    private final AreaService areaService;

    @Autowired
    public InitAreaTableListener(AreaService areaService) {
        this.areaService = areaService;
    }

    @Override
    public boolean consumeMsg(StoreDTO storeDTO, MessageExt messageExt) {
        UserContextUtils.put(messageExt.getProperty(RocketMqConstant.DOWNSTREAM_CONTEXT));
        try {
            EnterpriseIdentifier.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
            BaseDTO baseDTO = new BaseDTO();
            baseDTO.setStoreGuid(storeDTO.getGuid());
            baseDTO.setStoreName(storeDTO.getName());
            areaService.initArea(baseDTO);
            log.info("门店：{} 初始化区域桌台成功", storeDTO.getName());
        } catch (Exception e) {
            log.error("门店：{} 初始化区域桌台发生错误：{}", storeDTO.getName(), ThrowableExtUtils.asStringIfAbsent(e));
            return false;
        } finally {
            EnterpriseIdentifier.remove();
        }
        return true;
    }
}
