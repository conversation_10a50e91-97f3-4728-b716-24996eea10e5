package com.holderzone.holder.saas.store.mdm.pipeline.org.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.anno.SwitchServerCodeAnno;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.mdm.constant.FieldValutConstants;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.OrgTypeEnum;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.OrganizationInfoDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.domain.OrganizationDO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.domain.StoreBrandDO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.feign.ErpRpcClient;
import com.holderzone.holder.saas.store.mdm.pipeline.org.feign.OrgRpcClient;
import com.holderzone.holder.saas.store.mdm.pipeline.org.mapper.OrganizationMapper;
import com.holderzone.holder.saas.store.mdm.pipeline.org.mapper.StoreBrandMapper;
import com.holderzone.holder.saas.store.mdm.pipeline.org.mapstruct.OrgMapstruct;
import com.holderzone.holder.saas.store.mdm.service.DistributedIdService;
import com.holderzone.holder.saas.store.mdm.util.GuidUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalTime;
import java.util.List;

/**
 * 组织 服务实现类
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
@Slf4j
@Service
public class OrganizationServiceImpl extends ServiceImpl<OrganizationMapper, OrganizationDO> implements OrganizationService {

    private final OrganizationMapper organizationMapper;

    private final StoreBrandMapper storeBrandMapper;

    private final OrgMapstruct orgMapstruct;

    private final OrgRpcClient orgRpcClient;

    private final DistributedIdService distributedIdService;

    private final ErpRpcClient erpRpcClient;

    @Autowired
    public OrganizationServiceImpl(OrganizationMapper organizationMapper,
                                   StoreBrandMapper storeBrandMapper, OrgMapstruct orgMapstruct, OrgRpcClient orgRpcClient, DistributedIdService distributedIdService, ErpRpcClient erpRpcClient) {
        this.organizationMapper = organizationMapper;
        this.storeBrandMapper = storeBrandMapper;
        this.orgMapstruct = orgMapstruct;
        this.orgRpcClient = orgRpcClient;
        this.distributedIdService = distributedIdService;
        this.erpRpcClient = erpRpcClient;
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_organization")
    public List<OrganizationDO> shouldInitOrgList() {
        return this.list(new LambdaQueryWrapper<>());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_organization")
    public boolean createOrganization(OrganizationInfoDTO organizationInfoDTO) {
        // 组织名称、组织深度由发送方保证数据安全
        OrganizationDO orgInSql = getOne(new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getGuid, organizationInfoDTO.getThirdNo()));
        if (orgInSql != null) {
            return false;
        }
        if (!UserContextUtils.getEnterpriseGuid().equals(organizationInfoDTO.getFid())) {
            OrganizationDO parent = getOne(new LambdaQueryWrapper<OrganizationDO>()
                    .eq(OrganizationDO::getGuid, organizationInfoDTO.getFid()));
            if (parent != null) {
                organizationInfoDTO.setFid(parent.getParentIds() + "," + organizationInfoDTO.getFid());
            } else {
                organizationInfoDTO.setFid(UserContextUtils.getEnterpriseGuid());
            }
        }

        // 设置门店编码
        if (OrgTypeEnum.STORE.getType().equals(organizationInfoDTO.getType())) {
            if (!StringUtils.hasText(organizationInfoDTO.getCode())) {
                organizationInfoDTO.setCode(this.getOnlyOrganizationCode());
            }
        }

        this.save(orgMapstruct.organizationInfoDTO2DO(organizationInfoDTO)
                .setCreateUserGuid(FieldValutConstants.DEFAULT_STAFF_GUID)
                .setModifiedUserGuid(FieldValutConstants.DEFAULT_STAFF_GUID)
                .setBusinessStart((organizationInfoDTO.getBusinessTime() == null
                        || organizationInfoDTO.getBusinessTime().isEmpty()) ? LocalTime.of(00, 00, 00) : DateTimeUtils.string2LocalDateTime(organizationInfoDTO.getBusinessTime(), "yyyy-MM-dd HH:mm:ss").toLocalTime())
                .setBusinessEnd(LocalTime.of(23, 59, 59))
                .setGmtCreate(DateTimeUtils.now())
                .setGmtModified(DateTimeUtils.now()));

        // 如果是门店
        if (OrgTypeEnum.STORE.getType().equals(organizationInfoDTO.getType())) {
            // 创建门店品牌
            StoreBrandDO storeBrandDO = new StoreBrandDO()
                    .setGuid(distributedIdService.nextStoreBrandGuid())
                    .setStoreGuid(organizationInfoDTO.getThirdNo())
                    .setBrandGuid(organizationInfoDTO.getBrandGuid())
                    .setCreateUserGuid(FieldValutConstants.DEFAULT_STAFF_GUID)
                    .setModifiedUserGuid(FieldValutConstants.DEFAULT_STAFF_GUID)
                    .setGmtCreate(DateTimeUtils.now())
                    .setGmtModified(DateTimeUtils.now());
            storeBrandMapper.insert(storeBrandDO);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_organization")
    public boolean updateOrganization(OrganizationInfoDTO organizationInfoDTO) {
        // 组织名称、组织深度由发送方保证数据安全
        OrganizationDO originalDO = this.getOne(new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getGuid, organizationInfoDTO.getThirdNo()));
        if (originalDO == null) {
            return false;
        }
        // 处理当前组织的上下级
        if (originalDO.getParentIds().endsWith(organizationInfoDTO.getFid())) {
            organizationInfoDTO.setFid(originalDO.getParentIds());
        } else {
            // 计算fid
            String fid;
            if (UserContextUtils.getEnterpriseGuid().equals(organizationInfoDTO.getFid())) {
                fid = organizationInfoDTO.getFid();
            } else {
                OrganizationDO parent = getOne(new LambdaQueryWrapper<OrganizationDO>()
                        .eq(OrganizationDO::getGuid, organizationInfoDTO.getFid()));
                if (parent != null) {
                    fid = parent.getParentIds() + "," + organizationInfoDTO.getFid();
                } else {
                    fid = UserContextUtils.getEnterpriseGuid();
                }

            }
            // 修改当前组织的fid
            organizationInfoDTO.setFid(fid);
            // 修改当前组织的下级组织
            List<OrganizationDO> children = organizationMapper.selectList(new LambdaQueryWrapper<OrganizationDO>()
                    .like(OrganizationDO::getParentIds, organizationInfoDTO.getThirdNo())
            );
            if (!CollectionUtils.isEmpty(children)) {
                children.forEach(p -> {
                    int idxOfCur = p.getParentIds().indexOf(organizationInfoDTO.getThirdNo());
                    p.setParentIds(fid + p.getParentIds().substring(idxOfCur - 1));
                });
                this.updateBatchById(children);
            }
        }

        OrganizationDO organizationDO = orgMapstruct.organizationInfoDTO2DO(organizationInfoDTO)
                .setGmtModified(DateTimeUtils.now())
                .setModifiedUserGuid(FieldValutConstants.DEFAULT_STAFF_GUID);
        this.update(organizationDO, new LambdaQueryWrapper<OrganizationDO>()
                .eq(OrganizationDO::getGuid, organizationDO.getGuid()));


        // 如果是门店
        if (OrgTypeEnum.STORE.getType().equals(organizationInfoDTO.getType())) {
            // 更新门店品牌
            if (StringUtils.hasText(organizationInfoDTO.getBrandGuid())) {
                StoreBrandDO storeBrandDO = new StoreBrandDO();
                storeBrandDO.setBrandGuid(organizationInfoDTO.getBrandGuid());
                storeBrandDO.setModifiedUserGuid(FieldValutConstants.DEFAULT_STAFF_GUID);
                storeBrandMapper.update(storeBrandDO, new LambdaQueryWrapper<StoreBrandDO>()
                        .eq(StoreBrandDO::getStoreGuid, organizationInfoDTO.getThirdNo()));
            }
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_organization")
    public void deleteOrganization(OrganizationInfoDTO organizationInfoDTO) {
        // 删除组织逻辑：不存在下级组织和下级门店才能删除，由发送方保证
        List<OrganizationDO> children = list(new LambdaQueryWrapper<OrganizationDO>()
                .like(OrganizationDO::getParentIds, organizationInfoDTO.getThirdNo()));
        if (!CollectionUtils.isEmpty(children)) {
            log.error("该组织[{}]存在下级组织，无法删除！", organizationInfoDTO.getThirdNo());
        } else {
            remove(new LambdaQueryWrapper<OrganizationDO>()
                    .eq(OrganizationDO::getGuid, organizationInfoDTO.getThirdNo()));
            if (OrgTypeEnum.STORE.getType().equals(organizationInfoDTO.getType())) {
                // 删除门店品牌
                storeBrandMapper.delete(new LambdaQueryWrapper<StoreBrandDO>()
                        .eq(StoreBrandDO::getStoreGuid, organizationInfoDTO.getThirdNo()));
            }
        }
    }

    private String getOnlyOrganizationCode() {
        return getOnlyOrganizationCode(0);
    }

    private String getOnlyOrganizationCode(int tryCount) {
        if (++tryCount > 10) {
            return null;
        }
        String code = GuidUtils.nextOrganizationGuid();
        Boolean result = erpRpcClient.checkCode(code);
        if (result == null || !result) {
            return this.getOnlyOrganizationCode(tryCount);
        }
        return code;
    }
}
