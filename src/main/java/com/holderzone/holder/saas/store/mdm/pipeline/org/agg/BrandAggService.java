package com.holderzone.holder.saas.store.mdm.pipeline.org.agg;


import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.BrandInfoDTO;

import java.util.List;

/**
 * 品牌 服务类
 *
 * <AUTHOR>
 * @since 2019-11-23
 */
public interface BrandAggService {

    void createLocalBrand(BrandInfoDTO brandInfoDTO);

    void updateLocalBrand(BrandInfoDTO brandInfoDTO);

    void deleteLocalBrand(BrandInfoDTO brandInfoDTO);

    void triggerRemoteBrand(MdmTriggerType mdmTriggerType);

    void createRemoteBrand(List<BrandInfoDTO> brandInfoList);

    void updateRemoteBrand(BrandInfoDTO brandInfoDTO);

    void deleteRemoteBrand(List<BrandInfoDTO> brandInfoList);

}
