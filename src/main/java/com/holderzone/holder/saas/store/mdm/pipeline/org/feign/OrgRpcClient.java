package com.holderzone.holder.saas.store.mdm.pipeline.org.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className OrganizationClient
 * @description 服务间调用-MDM
 * @program holder-saas-store-organization
 */
@Component
@FeignClient(value = "holder-saas-store-organization", fallbackFactory = OrgRpcClient.ServiceFallback.class)
public interface OrgRpcClient {

    @PostMapping(value = "/store/query_store_by_guid")
    StoreDTO queryBrandGuidOfOrganization(@RequestParam("storeGuid") String storeGuid);

    @PostMapping(value = "/store/create_by_mdm")
    void createStoreByMdm(@RequestBody StoreDTO storeDTO);

    @PostMapping(value = "/store/update_by_mdm")
    void updateStoreByMdm(@RequestBody StoreDTO storeDTO);

    @Slf4j
    @Component
    class ServiceFallback implements FallbackFactory<OrgRpcClient> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OrgRpcClient create(Throwable cause) {
            return new OrgRpcClient() {
                @Override
                public StoreDTO queryBrandGuidOfOrganization(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "createBrand", ThrowableUtils.asString(cause));
                    throw new BusinessException("查询门店信息熔断");
                }

                @Override
                public void createStoreByMdm(StoreDTO storeDTO) {
                    log.error(HYSTRIX_PATTERN, "createStoreByMdm", ThrowableUtils.asString(cause));
                    throw new BusinessException("创建门店下游接口");
                }

                @Override
                public void updateStoreByMdm(StoreDTO storeDTO) {
                    log.error(HYSTRIX_PATTERN, "updateStoreByMdm", ThrowableUtils.asString(cause));
                    throw new BusinessException("修改门店下游接口");
                }
            };
        }
    }
}