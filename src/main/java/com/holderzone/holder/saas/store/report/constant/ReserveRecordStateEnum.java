package com.holderzone.holder.saas.store.report.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum ReserveRecordStateEnum {
    /**
     * 32
     */
    COMMIT(0b100000, "提交", "待确认"),
    /**
     * 35
     */
    CANCLE(0b100011, "取消", "已取消"),
    /**
     * 39
     */
    NO_PAY(0b100111, "未付定金", "待支付"),
    /**
     * 47
     */
    NO_PAY_CANCEL(0b101111, "支付失敗或者未付定金而取消", "已取消"),
    /**
     * 3
     */
    SYSTEM_CANCLE(0b000011, "系统自动取消", "已取消"),
    /**
     * 37
     */
    DELAY(0b100101, "已逾期", "已逾期"),
    /**
     * 38
     */
    PASS(0b100110, "通过", "预定中"),
    /**
     * 62
     */
    OPEN_TABLE(0b111110, "到店开台", "已到店"),
    /**
     * 54
     */
    PICK_TABLE(0b110110, "到店选台", "已到店"),
    /**
     * 56
     */
    FINISH(0b111000, "已结账", "已结账"),
    ;
    private final int code;

    private final String message;

    private final String clientState;

    public static String getClientStateByCode(int code, Boolean isDelay) {
        for (ReserveRecordStateEnum value : values()) {
            if (value.getCode() == code) {
                if (PASS.getCode() == code && Boolean.TRUE.equals(isDelay)) {
                    return DELAY.getClientState();
                }
                return value.getClientState();
            }
        }
        return null;
    }

}