package com.holderzone.holder.saas.store.table.utils;

import com.holderzone.framework.util.ThrowableUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/06 14:26
 */
public class ThrowableExtUtils {

    public static String asStringIfAbsent(Throwable throwable) {
        String message = throwable.getMessage();
        return message != null ? message : ThrowableUtils.asString(throwable);
    }
}
