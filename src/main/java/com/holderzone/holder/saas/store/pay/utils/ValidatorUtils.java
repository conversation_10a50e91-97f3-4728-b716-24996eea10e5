package com.holderzone.holder.saas.store.pay.utils;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.validate.Validator;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.pay.*;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AreaValidator
 * @date 2018/12/27 10:54
 * @description
 * @program holder-saas-store-table
 */
public final class ValidatorUtils {

    public static final List<String> SUPPORT_PAY_POWER_ID_LIST = Lists.newArrayList("1", "3", "8", "10", "29", "31", "34", "51", "52");

    public static void validatePrePay(SaasAggPayDTO saasAggPayDTO) {
        Validator validator = Validator.create()
                .notBlank(saasAggPayDTO.getEnterpriseGuid(), "企业Guid")
                .notBlank(saasAggPayDTO.getStoreGuid(), "门店Guid")
                .notNull(saasAggPayDTO.getReqDTO(), "支付请求实体");
        if (saasAggPayDTO.getReqDTO() != null) {
            AggPayPreTradingReqDTO reqDTO = saasAggPayDTO.getReqDTO();
            validator.notBlank(reqDTO.getOrderGUID(), "商户方订单编号")
                    .notBlank(reqDTO.getPayGUID(), "支付唯一标示")
                    .notNull(reqDTO.getAmount(), "支付金额")
                    .notBlank(reqDTO.getGoodsName(), "商品名称")
                    .notBlank(reqDTO.getBody(), "订单描述")
                    .notBlank(reqDTO.getStoreName(), "门店名称");
            String payPowerId = reqDTO.getPayPowerId();
            if (!StringUtils.hasText(payPowerId)
                    || "2".equals(payPowerId) || "9".equals(payPowerId)) {
                validator.notBlank(reqDTO.getAuthCode(), "条码支付授权码")
                        .notBlank(reqDTO.getTerminalId(), "条码支付设备终端号");
            } else {
                if (!SUPPORT_PAY_POWER_ID_LIST.contains(payPowerId)) {
                    throw new ParameterException("无效的payPowerId:" + payPowerId);
                }
            }
        }
        validator(validator);
    }

    public static void validatePolling(SaasPollingDTO saasPollingDTO) {
        validateSaasPollingDTO(saasPollingDTO);
    }

    public static void validateRefund(SaasAggRefundDTO saasAggRefundDTO) {
        Validator validator = Validator.create()
                .notBlank(saasAggRefundDTO.getEnterpriseGuid(), "企业Guid")
                .notBlank(saasAggRefundDTO.getStoreGuid(), "门店Guid")
                .notNull(saasAggRefundDTO.getAggRefundReqDTO(), "退款请求实体");
        if (saasAggRefundDTO.getAggRefundReqDTO() != null) {
            AggRefundReqDTO aggRefundReqDTO = saasAggRefundDTO.getAggRefundReqDTO();
            validator.notBlank(aggRefundReqDTO.getPayGUID(), "支付Guid")
                    .notBlank(aggRefundReqDTO.getOrderGUID(), "订单Guid")
                    .notNull(aggRefundReqDTO.getRefundType(), "退款类型")
                    .notNull(aggRefundReqDTO.getRefundFee(), "退款金额")
                    .notNull(aggRefundReqDTO.getReason(), "退款理由");
        }
        validator(validator);
    }

    public static void validateRefundPolling(SaasPollingDTO saasPollingDTO) {
        validateSaasPollingDTO(saasPollingDTO);
    }

    public static void validateQueryResult(SaasPollingDTO saasPollingDTO) {
        validateSaasPollingDTO(saasPollingDTO);
    }

    public static void validatorWxPubAccPay(SaasAggWeChatPublicAccountPayDTO publicAccountPayDTO) {
        Validator validator = Validator.create()
                .notBlank(publicAccountPayDTO.getEnterpriseGuid(), "企业Guid")
                .notBlank(publicAccountPayDTO.getStoreGuid(), "门店Guid")
                .notNull(publicAccountPayDTO.getPublicAccountPayDTO(), "支付请求实体");
        if (publicAccountPayDTO.getPublicAccountPayDTO() != null) {
            AggWeChatPublicAccountPayDTO aggWeChatPublicAccountPayDTO = publicAccountPayDTO.getPublicAccountPayDTO();
            validator.notBlank(aggWeChatPublicAccountPayDTO.getPayGUID(), "支付Guid")
                    .notBlank(aggWeChatPublicAccountPayDTO.getOrderGUID(), "订单Guid")
                    .notNull(aggWeChatPublicAccountPayDTO.getAmount(), "支付金额")
                    .notBlank(aggWeChatPublicAccountPayDTO.getGoodsName(), "商品名称")
                    .notBlank(aggWeChatPublicAccountPayDTO.getBody(), "订单描述")
                    .notBlank(aggWeChatPublicAccountPayDTO.getStoreName(), "门店名称")
                    .notBlank(aggWeChatPublicAccountPayDTO.getEnterpriseName(), "企业名称");
        }
        validator(validator);
    }

    public static void validatorWxPubAccPolling(SaasPollingDTO saasPollingDTO) {
        validateSaasPollingDTO(saasPollingDTO);
    }

    public static void validatorPayRecord(BasePageDTO basePageDTO) {
        Validator validator = Validator.create()
                .notBlank(basePageDTO.getEnterpriseGuid(), "企业Guid")
                .notBlank(basePageDTO.getStoreGuid(), "门店Guid");
        validator(validator);
    }

    private static void validateSaasPollingDTO(SaasPollingDTO saasPollingDTO) {
        Validator validator = Validator.create()
                .notBlank(saasPollingDTO.getEnterpriseGuid(), "企业Guid")
                .notBlank(saasPollingDTO.getStoreGuid(), "门店Guid")
                .notBlank(saasPollingDTO.getPayGuid(), "支付Guid")
                .notBlank(saasPollingDTO.getOrderGuid(), "订单Guid");
        validator(validator);
    }

    private static void validator(Validator validator) {
        if (!validator.isValid()) {
            throw new ParameterException(validator.getMessage());
        }
    }

}
