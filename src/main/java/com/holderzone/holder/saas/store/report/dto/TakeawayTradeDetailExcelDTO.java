package com.holderzone.holder.saas.store.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 外卖订单结算明细
 */
@Data
public class TakeawayTradeDetailExcelDTO implements Serializable {

    private static final long serialVersionUID = -1667368987764335434L;

    @Excel(name = "门店ID", orderNum = "1", width = 15)
    private String storeGuid;

    @Excel(name = "门店名称", orderNum = "2", width = 15)
    private String storeName;

    @Excel(name = "账单日期", orderNum = "3", width = 15)
    private String orderCreateDate;

    /**
     * 订单来源 0:美团 1：饿了么
     */
    @Excel(name = "订单来源", orderNum = "4", width = 15, replace = {"美团外卖_0", "饿了么外卖_1"})
    private Integer takeoutOrderType;

    /**
     * 订单状态
     * 1 用户已提交订单,2 可推送到APP方平台也可推送到商家,3 商家已收到,4 商家已确认,8 已完成,9 已取消
     */
    @Excel(name = "订单状态", orderNum = "5", width = 15, replace = {"用户已提交订单_1", "可推送到APP方平台也可推送到商家_2", "商家已收到_3",
            "商家已确认_4", "已完成_8", "已取消_9"})
    private Integer status;

    @Excel(name = "接单序号", orderNum = "6", width = 15)
    private String orderDaySn;

    @Excel(name = "订单编号", orderNum = "7", width = 20)
    private String orderId;

    @Excel(name = "订单创建时间", orderNum = "8", width = 15)
    private String orderCreateTime;

    @Excel(name = "订单完成时间", orderNum = "9", width = 15)
    private String orderCompleteTime;

    @Excel(name = "商品金额(菜品原价＋餐盒费＋赠品原价)", orderNum = "10", width = 25)
    private BigDecimal foodAmount;

    @Excel(name = "配送费", orderNum = "11", width = 15)
    private BigDecimal shippingAmount;

    @Excel(name = "打包袋价格", orderNum = "12", width = 15)
    private BigDecimal packageBagMoney;

    @Excel(name = "顾客单买单送费", orderNum = "13", width = 15)
    private BigDecimal singleIncreaseAmount;

    @Excel(name = "顾客实际支付", orderNum = "14", width = 15)
    private BigDecimal userPayTotalAmount;

    /**
     * 支付类型（1:货到付款；2:在线支付）
     */
    @Excel(name = "支付类型", orderNum = "15", width = 15, replace = {"货到付款_1", "在线支付_2"})
    private Integer payType;

    @Excel(name = "顾客支付给商家", orderNum = "16", width = 15)
    private BigDecimal phfPayTotalAmountForPoi;

    @Excel(name = "总活动款", orderNum = "17", width = 15)
    private BigDecimal totalActivityAmount;

    @Excel(name = "活动成本明细", orderNum = "18", width = 15)
    private String activityDetails;

    @Excel(name = "抽佣金额", orderNum = "19", width = 15)
    private BigDecimal commisionAmount;

    @Excel(name = "平台活动补贴", orderNum = "20", width = 15)
    private BigDecimal platformPayForPoiAmount;

    @Excel(name = "商家费用合计", orderNum = "21", width = 15)
    private BigDecimal totalMerchantFees;

    @Excel(name = "美团应付给商家", orderNum = "22", width = 15, isStatistics = true)
    private BigDecimal offlineOrderSkPayAmount;

    @Excel(name = "结算金额", orderNum = "23", width = 15, isStatistics = true)
    private BigDecimal settleAmount;

    /**
     * 配送类型
     * <p>
     * 0000
     * <p>
     * 商家自配送
     * <p>
     * 0002
     * <p>
     * 趣活
     * <p>
     * 0016
     * <p>
     * 达达
     * <p>
     * 0033
     * <p>
     * E代送
     * <p>
     * 1001
     * <p>
     * 美团专送-加盟
     * <p>
     * 1002
     * <p>
     * 美团专送-自建
     * <p>
     * 1003
     * <p>
     * 美团配送-众包
     * <p>
     * 1004
     * <p>
     * 美团专送-城市代理
     * <p>
     * 2001
     * <p>
     * 角马
     * <p>
     * 2002
     * <p>
     * 快送
     * <p>
     * 2010
     * <p>
     * 全城送
     * <p>
     * 3001
     * <p>
     * 混合送，即美团专送+美团快送，使用方式相同
     */
    @Excel(name = "配送方式", orderNum = "24", width = 15, replace = {"商家自配送_0000", "趣活_0002", "达达_0016", "E代送_0033", "美团专送-加盟_1001",
            "美团专送-自建_1002", "美团配送-众包_1003", "美团专送-城市代理_1004", "角马_2001", "快送_2002", "全城送_2010", "混合送_3001"})
    private String shippingType;

    @Excel(name = "备注", orderNum = "24", width = 10)
    private String remark;
}
