/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:MdmTriggerDTO.java
 * Date:2019-12-19
 * Author:terry
 */

package com.holderzone.holder.saas.store.mdm.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-12-19 上午10:41
 */
@Data
public class MdmTriggerDTO {

    @NotBlank(message = "secretKey不得为空")
    @ApiModelProperty(value = "secretKey", required = true)
    private String secretKey;

    @NotEmpty(message = "需要触发的商户列表不得为空")
    @ApiModelProperty(value = "需要触发的商户列表，* 代表所有商户", required = true)
    private List<String> erpGuidList;

    @NotEmpty(message = "需要触发的数据库表列表不得为空")
    @ApiModelProperty(value = "需要触发的数据库表列表，* 代表所有表", required = true)
    private List<String> dbTableList;

    @Nullable
    @ApiModelProperty(value = "需要排除触发的商户列表")
    private List<String> erpExcludeList;

    @Nullable
    @ApiModelProperty(value = "触发类型：创建=create，更新=update")
    private String triggerType;
}
