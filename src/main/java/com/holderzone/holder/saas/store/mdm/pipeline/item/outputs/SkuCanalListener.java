package com.holderzone.holder.saas.store.mdm.pipeline.item.outputs;

import com.alibaba.otter.canal.protocol.CanalEntry;
import com.holderzone.framework.canal.starter.core.CanalMsg;
import com.holderzone.framework.canal.starter.extension.RowChangeBody;
import com.holderzone.framework.canal.starter.point.CanalListenerHandler;
import com.holderzone.framework.canal.starter.point.anno.ddl.AlertTableListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.DeleteListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.InsertListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.UpdateListenPoint;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.config.SyncConfig;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.SkuSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.SkuDO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.mapstruct.ItemMapStruct;
import com.holderzone.holder.saas.store.mdm.util.DataConvertUtils;
import com.holderzone.holder.saas.store.mdm.util.ErpUtils;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CanalListenerHandler
public class SkuCanalListener {


    private final MqUtils mqUtils;

    private final ItemMapStruct itemMapStruct;

    private final SyncConfig syncConfig;

    @Autowired
    public SkuCanalListener(MqUtils mqUtils, ItemMapStruct itemMapStruct, SyncConfig syncConfig) {
        this.mqUtils = mqUtils;
        this.itemMapStruct = itemMapStruct;
        this.syncConfig = syncConfig;
    }

    @LogBefore(value = "内部系统创建商品规格", logLevel = LogLevel.INFO)
    @AlertTableListenPoint(schema = "hsi_item_*_db", table = "hsi_sku")
    public void onEventAlertTable(CanalMsg canalMsg, CanalEntry.RowChange rowChange) {
        if (syncConfig.shouldInitAgain(rowChange.getSql())) {
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_SKU_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_SKU_INIT_TAG,
                    ErpUtils.getErpGuid(canalMsg), ErpUtils.getErpGuid(canalMsg)
            );
        }
    }

    @LogBefore(value = "内部系统创建商品规格", logLevel = LogLevel.INFO)
    @InsertListenPoint(schema = "hsi_item_*_db", table = "hsi_sku")
    public void onEventInsertData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        List<SkuSyncDTO> afterDataDTO = getAfterDataDTO(rowChangeBody);
        if (CollectionUtils.isEmpty(afterDataDTO)) {
            return;
        }
        mqUtils.sendMessage(
                RocketMqConfig.StoreConfig.STORE_MDM_SKU_TOPIC,
                RocketMqConfig.StoreConfig.STORE_MDM_SKU_CREATE_TAG,
                afterDataDTO, ErpUtils.getErpGuid(canalMsg)
        );
    }

    @LogBefore(value = "内部系统更新或删除商品规格", logLevel = LogLevel.INFO)
    @UpdateListenPoint(schema = "hsi_item_*_db", table = "hsi_sku")
    public void onEventUpdateData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        Map<Integer, List<SkuSyncDTO>> listMap = getAfterDatatoMap(rowChangeBody);
        // 删除分类
        List<SkuSyncDTO> skuSyncDTOSToDelete = listMap.get(1);
        if (null != skuSyncDTOSToDelete) {
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_SKU_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_SKU_DELETE_TAG,
                    skuSyncDTOSToDelete, ErpUtils.getErpGuid(canalMsg)
            );
        }
        // 修改分类
        List<SkuSyncDTO> skuSyncDTOSToUpdate = listMap.getOrDefault(0, Collections.emptyList());
        skuSyncDTOSToUpdate.forEach(o -> {
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_SKU_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_SKU_UPDATE_TAG,
                    o, ErpUtils.getErpGuid(canalMsg)
            );
        });
    }


    @LogBefore(value = "内部系统删除商品规格", logLevel = LogLevel.INFO)
    @DeleteListenPoint(schema = "hsi_item_*_db", table = "hsi_sku")
    public void onEventDeleteData(RowChangeBody rowChangeBody, CanalMsg canalMsg) {
        List<String> list = getBeforeGuid(rowChangeBody);
        if (CollectionUtils.isEmpty(list)) return;
        List<SkuSyncDTO> skuSyncDTOList = list.stream()
                .map(s -> new SkuSyncDTO().setGuid(s))
                .collect(Collectors.toList());
        mqUtils.sendMessage(
                RocketMqConfig.StoreConfig.STORE_MDM_SKU_TOPIC,
                RocketMqConfig.StoreConfig.STORE_MDM_SKU_DELETE_TAG,
                skuSyncDTOList, ErpUtils.getErpGuid(canalMsg)
        );
    }

    private List<SkuSyncDTO> getAfterDataDTO(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterDataDTO(rowChangeBody, SkuDO.class, itemMapStruct::skuDo2skuSynDTO);
    }

    private Map<Integer, List<SkuSyncDTO>> getAfterDatatoMap(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterDataDtoMap(rowChangeBody, SkuDO.class, SkuDO::getIsDelete, itemMapStruct::skuDo2skuSynDTO);
    }

    private List<String> getBeforeGuid(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getBeforeGuid(rowChangeBody, map -> map.get("guid"));
    }
}
