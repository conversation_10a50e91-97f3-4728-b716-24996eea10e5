package com.holderzone.holder.saas.store.report.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPoolConfig {

    @Bean(value = "reportQueryExecutor")
    public ExecutorService reportQueryExecutor() {
        return new ThreadPoolExecutor(5, 10, 5L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("report-data-query-pool-%d").build());
    }

    @Bean(value = "openapiReportQueryExecutor")
    public ExecutorService openapiReportQueryExecutor() {
        return new ThreadPoolExecutor(5, 10, 5L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(50), new ThreadFactoryBuilder().setNameFormat("openapi-report-data-query-pool-%d").build());
    }
}