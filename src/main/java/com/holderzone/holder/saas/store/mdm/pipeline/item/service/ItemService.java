package com.holderzone.holder.saas.store.mdm.pipeline.item.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.ItemSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.ItemDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemService
 * @date 2019/11/23 下午5:02
 * @description //
 * @program holder
 */


public interface ItemService extends IService<ItemDO> {
    /**
     * mdm推送本地同步创建商品
     * @param itemSyncDTO
     * @return
     */
    void insertItem(ItemSyncDTO itemSyncDTO);

    /**
     * mdm推送本地同步更新商品
     * @param itemSyncDTO
     * @return
     */
    void updateItemByGuid(ItemSyncDTO itemSyncDTO);

    /**
     * mdm推送本地同步删除商品
     * @param guid
     * @return
     */
    void deleteItemByGuid(String guid);


     ItemDO getLocalRerocd(Wrapper<ItemDO> queryWrapper);

     List<ItemDO> shouldInitItemDOS();
}
