package com.holderzone.holder.saas.store.mdm.pipeline.org.agg;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.exception.RepeatedException;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.DeleteOrgReqDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.OrgTypeEnum;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.OrganizationInfoDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.feign.OrgRpcClient;
import com.holderzone.holder.saas.store.mdm.pipeline.org.mapstruct.OrgMapstruct;
import com.holderzone.holder.saas.store.mdm.pipeline.org.service.OrganizationService;
import com.holderzone.holder.saas.store.mdm.service.MdmOperation;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import com.holderzone.holder.saas.store.mdm.util.SplitUtils;
import com.holderzone.holder.saas.store.mdm.util.TriggerLogUtils;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.resource.common.util.MessageType;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.holderzone.holder.saas.store.mdm.constant.ReqTypeConstant.*;
import static com.holderzone.holder.saas.store.mdm.constant.ReqUrlConstants.*;

/**
 * 品牌表 服务实现类
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
@SuppressWarnings("unchecked")
@Slf4j
@Service
public class OrganizationAggServiceImpl implements OrganizationAggService {

    private final OrganizationService organizationService;

    private final MdmOperation mdmOperation;

    private final OrgRpcClient orgRpcClient;

    private final MqUtils mqUtils;

    @Value("${mdm.initSyncStep:50}")
    private int initSyncStep;

    @Autowired
    public OrganizationAggServiceImpl(OrganizationService organizationService,
                                      MdmOperation mdmOperation, OrgRpcClient orgRpcClient, MqUtils mqUtils) {
        this.organizationService = organizationService;
        this.mdmOperation = mdmOperation;
        this.orgRpcClient = orgRpcClient;
        this.mqUtils = mqUtils;
    }

    @Override
    public void createLocalOrganization(OrganizationInfoDTO organizationInfoDTO) {
        boolean succeed = organizationService.createOrganization(organizationInfoDTO);

        if (succeed) {
            if (OrgTypeEnum.STORE.getType().equals(organizationInfoDTO.getType())) {
                // REST调用创建门店下游接口
                StoreDTO storeDTO = new StoreDTO();
                storeDTO.setGuid(organizationInfoDTO.getThirdNo());
                storeDTO.setName(organizationInfoDTO.getName());
                storeDTO.setBelongBrandGuid(organizationInfoDTO.getBrandGuid());
                if (organizationInfoDTO.getBusinessTime() == null || organizationInfoDTO.getBusinessTime().isEmpty()) {
                    storeDTO.setBusinessStart(LocalTime.of(00, 00, 00));
                } else {
                    storeDTO.setBusinessStart(DateTimeUtils.string2LocalDateTime(organizationInfoDTO.getBusinessTime(), "yyyy-MM-dd HH:mm:ss").toLocalTime());
                }
                storeDTO.setBusinessEnd(LocalTime.of(23, 59, 59));
                orgRpcClient.createStoreByMdm(storeDTO);
            }
        }
    }

    @Override
    public void updateLocalOrganization(OrganizationInfoDTO organizationInfoDTO) {
        if (!organizationService.updateOrganization(organizationInfoDTO)) {
            createLocalOrganization(organizationInfoDTO);
        } else {
            if (OrgTypeEnum.STORE.getType().equals(organizationInfoDTO.getType())) {
                // REST调用修改门店下游接口
                StoreDTO storeDTO = new StoreDTO();
                storeDTO.setGuid(organizationInfoDTO.getThirdNo());
                storeDTO.setName(organizationInfoDTO.getName());
                storeDTO.setBelongBrandGuid(organizationInfoDTO.getBrandGuid());
                orgRpcClient.updateStoreByMdm(storeDTO);
            }
        }
    }

    @Override
    public void deleteLocalOrganization(OrganizationInfoDTO organizationInfoDTO) {
        organizationService.deleteOrganization(organizationInfoDTO);

        if (OrgTypeEnum.STORE.getType().equals(organizationInfoDTO.getType())) {
            // 如果是门店，需要用MQ同步到云平台
            UnMessage<String> unMessage = new UnMessage<>(
                    MessageType.DELETE.code(),
                    UserContextUtils.getEnterpriseGuid(),
                    organizationInfoDTO.getThirdNo(),
                    organizationInfoDTO.getThirdNo()
            );
            mqUtils.sendMessage(
                    RocketMqConfig.CloudConfig.MERCHANT_SYNC_ORGANIZATION_TOPIC,
                    RocketMqConfig.CloudConfig.MERCHANT_SYNC_ORGANIZATION_TAG,
                    unMessage,
                    UserContextUtils.getEnterpriseGuid());
        }
    }

    @Override
    public void triggerRemoteOrganization(MdmTriggerType mdmTriggerType) {
        List<OrganizationInfoDTO> organizationInfoDTOList = organizationService.shouldInitOrgList()
                .stream()
                .filter(organizationDO -> !organizationDO.getIsDeleted())
                .map(OrgMapstruct.INSTANCE::organizationDO2InfoDTO)
                .collect(Collectors.toList());

        TriggerLogUtils.pre("组织", organizationInfoDTOList.size());

        switch (mdmTriggerType) {
            case CREATE:
                SplitUtils.splitList(organizationInfoDTOList, initSyncStep).forEach(org -> {
                    try {
                        createRemoteOrganization(org);
                    } catch (Exception e) {
                        TriggerLogUtils.stepFailed("组织", e);
                        mqUtils.sendMessage(
                                RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_TOPIC,
                                RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_CREATE_TAG,
                                org, UserContextUtils.getEnterpriseGuid()
                        );
                    }
                    TriggerLogUtils.process("组织", org.size());
                });
                break;
            case UPDATE:
                organizationInfoDTOList.forEach(org -> {
                    try {
                        updateRemoteOrganization(org);
                    } catch (Exception e) {
                        TriggerLogUtils.singleFailed("组织", e);
                        mqUtils.sendMessage(
                                RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_TOPIC,
                                RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_UPDATE_TAG,
                                org, UserContextUtils.getEnterpriseGuid()
                        );
                    }
                });
                TriggerLogUtils.process("组织", organizationInfoDTOList.size());
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + mdmTriggerType);
        }

        TriggerLogUtils.post("组织", organizationInfoDTOList.size());
    }

    @Override
    public void createRemoteOrganization(List<OrganizationInfoDTO> organizationInfoList) {
        organizationInfoList.forEach(this::processParentAndBrand);
        try {
            mdmOperation.doRequest(POST, CREATE_ORGANIZATION_URL, organizationInfoList);
        } catch (RepeatedException e) {
            if (organizationInfoList.size() == 1) {
                TriggerLogUtils.singleRepeated("组织");
            } else {
                TriggerLogUtils.batchRepeated("组织", e);
                for (OrganizationInfoDTO organizationInfoDTO : organizationInfoList) {
                    mqUtils.sendMessage(
                            RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_TOPIC,
                            RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_CREATE_TAG,
                            Collections.singleton(organizationInfoDTO), UserContextUtils.getEnterpriseGuid()
                    );
                }
            }
        } catch (BusinessException e) {
            if (organizationInfoList.size() == 1) {
                throw e;
            } else {
                TriggerLogUtils.batchFailed("组织", e);
                for (OrganizationInfoDTO organizationInfoDTO : organizationInfoList) {
                    mqUtils.sendMessage(
                            RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_TOPIC,
                            RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_CREATE_TAG,
                            Collections.singleton(organizationInfoDTO), UserContextUtils.getEnterpriseGuid()
                    );
                }
            }
        }
    }

    @Override
    public void updateRemoteOrganization(OrganizationInfoDTO organizationInfoDTO) {
        // 当修改了门店的品牌时，storeBrand和organization会有数据变动
        // 然后此处收到org变动监听，会去关联查询门店的最新品牌
        // 但如果org监听执行先于storeBrand事物提交，会查询到错误的品牌，尴尬
        // 应该额外监听一下storeBrand表，然后查询关联门店，推送正确品牌的门店，即使这样会增大网络开销
        // 同理，创建门店也有这样的问题
        // 实际中，上述情况概率很小，canal没那么快的。。此处就先这样吧，再说了 FIXME
        processParentAndBrand(organizationInfoDTO);
        mdmOperation.doRequest(PUT, UPDATE_ORGANIZATION_URL, organizationInfoDTO);
    }

    @Override
    public void deleteRemoteOrganization(DeleteOrgReqDTO deleteOrgReqDTO) {
        mdmOperation.doRequest(DELETE, DELETE_ORGANIZATION_URL, deleteOrgReqDTO);
    }

    private void processParentAndBrand(OrganizationInfoDTO organizationInfoDTO) {
        // 处理上级组织
        String[] parent = organizationInfoDTO.getFid().split(",");
        organizationInfoDTO.setFid(parent[parent.length - 1]);
        // 处理门店电话、门店品牌
        if (OrgTypeEnum.STORE.getType().equals(organizationInfoDTO.getType())) {
          /*  if (organizationInfoDTO.getContactTel() == null) {
                organizationInfoDTO.setContactTel("18000000000");
            }*/
            StoreDTO storeDTO = orgRpcClient.queryBrandGuidOfOrganization(organizationInfoDTO.getThirdNo());
            organizationInfoDTO.setBrandGuid(storeDTO.getBelongBrandGuid());
        }
    }
}
