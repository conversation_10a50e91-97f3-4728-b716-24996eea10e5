package com.holderzone.holder.saas.store.table.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/01/17 14:20
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TableStatusBO {

    private String tableGuid;

    private String orderGuid;

    private String mainOrderGuid;

    private Set<Integer> subStatus;

    private Integer status;
}
