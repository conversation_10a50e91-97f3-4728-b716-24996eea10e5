package com.holderzone.holder.saas.store.pay.service.rpc;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeQueryDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @description business服务支付方式
 * @date 2021/11/25 15:04
 * @className: public interface PaymentTypeRpcService {
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = PaymentTypeRpcService.PayTypeFallBack.class)
public interface PaymentTypeRpcService {

    /**
     * 获取聚合支付方式信息
     *
     * @param storeGuid 门店guid
     * @return 聚合支付方式信息
     */
    @ApiOperation(value = "获取聚合支付方式信息")
    @GetMapping("/pay/type/get_jh_payment_type_info")
    PaymentTypeDTO getJhPaymentTypeInfo(@RequestParam("storeGuid") String storeGuid);

    @Slf4j
    @Component
    class PayTypeFallBack implements FallbackFactory<PaymentTypeRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public PaymentTypeRpcService create(Throwable throwable) {
            return new PaymentTypeRpcService() {

                @Override
                public PaymentTypeDTO getJhPaymentTypeInfo(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "getJhPaymentTypeInfo", storeGuid, ThrowableUtils.asString(throwable));
                    return null;
                }

            };
        }
    }
}
