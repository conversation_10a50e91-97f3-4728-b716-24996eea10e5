package com.holderzone.holder.saas.store.pay.service;

import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EnterPriseConfigService
 * @date 2019/03/14 11:36
 * @description
 * @program holder-saas-store-trading-center
 */
public interface ErpConfigService {

    PaymentInfoDTO getPaymentInfo(String enterpriseGuid, String storeGuid);

    PaymentInfoDTO getPaymentInfo(String enterpriseGuid, String storeGuid, String orderGuid);

    /**
     * 获取聚合支付分流账户信息
     * 如果分流规则匹配不上账户，但是该账户还绑定在门店上，可以查询到进行退款
     *
     * @param enterpriseGuid 企业guid
     * @param storeGuid      门店guid
     * @param orderGuid      订单guid
     * @return 聚合支付账户信息
     */
    PaymentInfoDTO getShuntPaymentInfo(String enterpriseGuid, String storeGuid, String orderGuid);

    Mono<PaymentInfoDTO> getPaymentInfoAsync(String enterpriseGuid, String storeGuid);

    /**
     * 聚合支付分流账户处理
     * 账户支付的钱只能退回该账户，需要特殊处理：
     * 如果分流规则匹配不上账户，但是该账户还绑定在门店上，可以查询到进行退款
     *
     * @param enterpriseGuid 企业guid
     * @param storeGuid      门店guid
     * @param orderGuid      订单guid
     * @return 聚合支付账户信息
     */
    Mono<PaymentInfoDTO> getShuntPaymentInfoAsync(String enterpriseGuid, String storeGuid, String orderGuid);

}
