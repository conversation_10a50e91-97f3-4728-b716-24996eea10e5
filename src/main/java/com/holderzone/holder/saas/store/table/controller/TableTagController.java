package com.holderzone.holder.saas.store.table.controller;

import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.table.service.TableTagRelationService;
import com.holderzone.holder.saas.store.table.service.TableTagService;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.table.TableTagDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/04 16:18
 */
@Slf4j
@Api("区域controller")
@RequestMapping("/tag")
@RestController
public class TableTagController {

    private final TableTagService tableTagService;

    private final TableTagRelationService tableTagRelationService;

    public TableTagController(TableTagService tableTagService, TableTagRelationService tableTagRelationService) {
        this.tableTagService = tableTagService;
        this.tableTagRelationService = tableTagRelationService;
    }

    @ApiOperation("新建标签")
    @PostMapping("/add")
    public boolean createTag(@RequestBody @Validated TableTagDTO tableTagDTO){
        log.info("新建标签,log：{}",tableTagDTO);
        return tableTagService.createTag(tableTagDTO);
    }

    @ApiOperation("标签列表")
    @PostMapping("/list")
    public Page<TableTagDTO> listTag(@RequestBody BasePageDTO basePageDTO){
        log.info("查询标签,log：{}", basePageDTO);
        return tableTagService.listTag(basePageDTO);
    }

    @ApiOperation("更新标签")
    @PostMapping("/update")
    public boolean updateTag(@RequestBody @Validated  TableTagDTO tableTagDTO){
        log.info("更新标签,log：{}",tableTagDTO);
        return tableTagService.updateTag(tableTagDTO);
    }

    @ApiOperation("删除标签")
    @PostMapping("/delete")
    public boolean deleteTag(@RequestBody TableTagDTO tableTagDTO){
        log.info("删除标签,log：{}",tableTagDTO);
        return tableTagService.deleteTag(tableTagDTO) && tableTagRelationService.removeTagByTagGuid(tableTagDTO.getGuid());
    }

}