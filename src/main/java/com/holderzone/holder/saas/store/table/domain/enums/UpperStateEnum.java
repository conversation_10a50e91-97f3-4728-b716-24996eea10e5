package com.holderzone.holder.saas.store.table.domain.enums;

/**
 * <AUTHOR>
 * @description 并台相关枚举
 * @date 2021/10/12 21:48
 * @className: UpperStateEnum
 */
public enum UpperStateEnum {
    GENERAL(0, "无并单"),
    MAIN(1, "主单"),
    SUB(2, "子单"),
    ;

    private int code;
    private String desc;

    UpperStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(int code) {
        for (UpperStateEnum c : UpperStateEnum.values()) {
            if (c.getCode() == code) {
                return c.desc;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
