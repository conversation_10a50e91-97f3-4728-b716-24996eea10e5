package com.holderzone.holder.saas.store.mdm.pipeline.staff.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MdmSynDTO
 * @date 2019/11/14 11:14
 * @description mdm员工同步dto
 * @program holder-saas-store
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("MDM用户信息同步DTO")
@JsonPropertyOrder(alphabetic = true)
public class UserSyncDTO {

    @JsonProperty("guid")
    @ApiModelProperty(value = "MDM生成的唯一标识", required = false)
    private String uuid;

    @JsonProperty("thirdNo")
    @NotBlank(message = "第三方标识不得为空", groups = {Create.class, Update.class, Delete.class})
    @ApiModelProperty(value = "第三方唯一标识", required = true)
    private String guid;

    @ApiModelProperty(value = "账号", required = true)
    @NotBlank(message = "账号不得为空", groups = {Create.class, Update.class})
    private String account;

    @ApiModelProperty(value = "密码", required = true)
    @NotBlank(message = "密码不得为空", groups = {Create.class, Update.class})
    private String password;

    @ApiModelProperty(value = "名字", required = true)
    @NotBlank(message = "名字不得为空", groups = {Create.class, Update.class})
    private String name;

    @JsonProperty("merchantNo")
    @ApiModelProperty(value = "商户号", required = true)
    @NotBlank(message = "商户号不得为空", groups = {Create.class, Update.class})
    private String enterpriseNo;

    @JsonProperty("tel")
    @ApiModelProperty(value = "电话号码", required = true)
    @NotBlank(message = "电话号码不得为空", groups = {Create.class, Update.class})
    private String phone;

    @Nullable
    @ApiModelProperty(value = "邮箱", required = false)
    private String email;

    @ApiModelProperty(value = "是否可用（0/禁用,1/启用）", required = true)
    @NotBlank(message = "是否可用不得为空", groups = {Create.class, Update.class})
    private String enabled;

    @ApiModelProperty(value = "注册来源", required = true)
    private String regType;

    @ApiModelProperty(value = "是否一体化", required = true)
    private Boolean integrateFlag;

    public interface Create extends Default {

    }

    public interface Update extends Default {

    }

    public interface Delete extends Default {

    }
}
