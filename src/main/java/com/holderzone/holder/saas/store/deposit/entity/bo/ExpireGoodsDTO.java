package com.holderzone.holder.saas.store.deposit.entity.bo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor

public class ExpireGoodsDTO implements Serializable {


    private static final long serialVersionUID = -2773042054434843563L;

    /**
     * 菜品 guid
     */
    private String guid;
    /**
     * 寄存操作记录Guid
     */
    private String depositGuid;
    /**
     * 过期时间
     */
    private String expireTime;
}
