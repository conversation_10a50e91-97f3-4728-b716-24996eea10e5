package com.holderzone.holder.saas.store.mdm.pipeline.org.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.domain.OrganizationDO;
import com.holderzone.saas.store.dto.organization.QueryStoreDTO;
import com.holderzone.saas.store.dto.organization.RegionDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.organization.StoreParserDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 组织表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
@Mapper
@Component
public interface OrganizationMapper extends BaseMapper<OrganizationDO> {

}
