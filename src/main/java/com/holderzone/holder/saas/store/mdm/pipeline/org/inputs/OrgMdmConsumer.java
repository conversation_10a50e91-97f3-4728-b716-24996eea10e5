package com.holderzone.holder.saas.store.mdm.pipeline.org.inputs;

import com.fasterxml.jackson.core.type.TypeReference;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.DeleteOrgReqDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.OrganizationInfoDTO;
import com.holderzone.holder.saas.store.mdm.event.AbsErpRocketMqConsumer;
import com.holderzone.holder.saas.store.mdm.pipeline.org.agg.OrganizationAggService;
import com.holderzone.holder.saas.store.mdm.util.JacksonExtUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MDMUserListener
 * @date 2019/11/23 18:04
 * @description
 * @program holder-saas-store
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.MainConfig.MAIN_MDM_ORGANIZATION_TOPIC,
        tags = {
                RocketMqConfig.MainConfig.MAIN_MDM_ORGANIZATION_CREATE_TAG,
                RocketMqConfig.MainConfig.MAIN_MDM_ORGANIZATION_DELETE_TAG,
                RocketMqConfig.MainConfig.MAIN_MDM_ORGANIZATION_UPDATE_TAG,
        },
        consumerGroup = RocketMqConfig.MainConfig.MAIN_MDM_ORGANIZATION_GROUP
)
public class OrgMdmConsumer extends AbsErpRocketMqConsumer<RocketMqTopic, String> {

    private final OrganizationAggService organizationAggService;

    @Autowired
    public OrgMdmConsumer(OrganizationAggService organizationAggService) {
        this.organizationAggService = organizationAggService;
    }

    @Override
    public boolean doConsumeMsg(String json, MessageExt messageExt) {
        String tags = messageExt.getTags();
        switch (tags) {
            case RocketMqConfig.MainConfig.MAIN_MDM_ORGANIZATION_CREATE_TAG:
                organizationAggService.createLocalOrganization(JacksonUtils.toObject(OrganizationInfoDTO.class, json));
                break;
            case RocketMqConfig.MainConfig.MAIN_MDM_ORGANIZATION_UPDATE_TAG:
                organizationAggService.updateLocalOrganization(JacksonUtils.toObject(OrganizationInfoDTO.class, json));
                break;
            case RocketMqConfig.MainConfig.MAIN_MDM_ORGANIZATION_DELETE_TAG:
                organizationAggService.deleteLocalOrganization(JacksonUtils.toObject(OrganizationInfoDTO.class, json));
                break;
            default:
                log.error("unknown mq tag : {}, message：{}", messageExt.getTags(), json);
                break;
        }
        return true;
    }
}
