package com.holderzone.holder.saas.store.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;


/**
 * 挂账明细列表导出
 */
@Data
public class DebtUnitRecordExcelDTO implements Serializable {

    private static final long serialVersionUID = -7268651757454110822L;

    @Excel(name = "挂账单位", orderNum = "1", width = 25)
    private String unitName;

    @Excel(name = "挂账票据编号", orderNum = "2", width = 25)
    private String debtInvoiceCode;

    @Excel(name = "挂账门店", orderNum = "3", width = 25)
    private String storeName;

    @Excel(name = "还款状态", orderNum = "4", width = 15, replace = {"-_null", "未还款_0", "已还款_1"})
    private Integer repaymentStatus;

    @Excel(name = "挂账金额", orderNum = "5", width = 15)
    private String debtFee;

    @Excel(name = "还款金额", orderNum = "6", width = 15)
    private String repaymentFee;

    @Excel(name = "结算方式", orderNum = "7", width = 15, replace = {"-_null", "现金_0", "支付宝_1", "微信支付_2", "银行卡_3", "支票_4", "退款_30"})
    private Integer paymentType;

    @Excel(name = "挂账时间", orderNum = "8", width = 25)
    private String gmtCreate;

    @Excel(name = "还款时间", orderNum = "9", width = 25)
    private String gmtModified;

    @Excel(name = "挂账操作人", orderNum = "10", width = 15)
    private String createStaffName;

    @Excel(name = "还款操作人", orderNum = "11", width = 15)
    private String updateStaffName;
}
