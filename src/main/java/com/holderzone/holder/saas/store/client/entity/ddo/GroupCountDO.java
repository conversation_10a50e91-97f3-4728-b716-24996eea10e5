package com.holderzone.holder.saas.store.client.entity.ddo;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GroupCountDO {

    public static GroupCountDO DEFAULT = new GroupCountDO("", 0);
    @ApiModelProperty(value = "时间段")
    private String timeQuantum;
    @ApiModelProperty(value = "成交")
    private Integer dealCount;

    public static GroupCountDO INSTANCE() {
        return new GroupCountDO("", 0);
    }
}
