package com.holderzone.holder.saas.store.mdm.util;

import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.slf4j.starter.extension.LogContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MqUtils
 * @date 2019/11/19 10:22
 * @description
 * @program holder-saas-store
 */
@Component
public class MqUtils {

    private static final Logger log = LoggerFactory.getLogger(MqUtils.class);

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    @Autowired
    public MqUtils(DefaultRocketMqProducer defaultRocketMqProducer) {
        this.defaultRocketMqProducer = defaultRocketMqProducer;
    }

    public void sendMessage(String topic, String tag, Object obj, String erpGuid) {
//        log.info("MQUtils sendMessage topic={},tag={},obj={},erpGuid={}", topic, tag, JacksonUtils.writeValueAsString(obj), erpGuid);
        Message message = new Message(
                topic, tag,
                JacksonUtils.toJsonByte(obj)
        );
        Map<String, String> properties = message.getProperties();
        properties.put(
                RocketMqConfig.MESSAGE_CONTEXT,
                Objects.requireNonNull(erpGuid)
        );
        properties.put(
                RocketMqConfig.MSG_LOG_TRACE_CONTEXT,
                LogContextUtils.getBizName()
        );
        defaultRocketMqProducer.sendMessage(message);
    }

    public String parseGuid(MessageExt messageExt) {
        return messageExt.getProperty(RocketMqConfig.MESSAGE_CONTEXT);
    }

    public String parseBizName(MessageExt messageExt) {
        return messageExt.getProperty(RocketMqConfig.MSG_LOG_TRACE_CONTEXT);
    }
}
