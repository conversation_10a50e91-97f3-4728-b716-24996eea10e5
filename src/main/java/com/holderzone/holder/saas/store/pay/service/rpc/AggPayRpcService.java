package com.holderzone.holder.saas.store.pay.service.rpc;

import com.holderzone.holder.saas.store.pay.entity.AggPayReserveVO;
import com.holderzone.saas.store.dto.pay.*;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayRpcService
 * @date 2019/03/14 14:42
 * @description 聚合支付平台接口
 * @program holder-saas-store-trading-center
 */
public interface AggPayRpcService {

    AggPayRespDTO preTrading(AggPayPreTradingReqDTO payPreTradingReqDTO);

    Mono<AggPayRespDTO> preTradingAsync(AggPayPreTradingReqDTO aggPayPreTradingReqDTO);

    AggPayPollingRespDTO doPolling(AggPayPollingDTO pollingJHPayDTO);

    Mono<AggPayPollingRespDTO> doPollingAsync(AggPayPollingDTO pollingJHPayDTO);

    AggPayPollingRespDTO prePayQueryBank(AggPayPollingDTO pollingJHPayDTO);

    Mono<AggPayPollingRespDTO> prePayQueryBankAsync(AggPayPollingDTO pollingJHPayDTO);

    Mono<AggPayPollingRespDTO> payPayQueryBankAsync(AggPayPollingDTO pollingJHPayDTO);

    AggRefundRespDTO refund(AggRefundReqDTO aggRefundReqDTO);

    Mono<AggRefundRespDTO> refundAsync(AggRefundReqDTO aggRefundReqDTO);

    AggRefundPollingRespDTO doRefundPolling(AggRefundPollingDTO aggRefundPollingDTO);

    Mono<AggRefundPollingRespDTO> doRefundPollingAsync(AggRefundPollingDTO aggRefundPollingDTO);

    String weChatPublicAccountPay(AggWeChatPublicAccountPayDTO accountPayDTO);

    Mono<String> weChatPublicAccountPayAsync(AggWeChatPublicAccountPayDTO accountPayDTO);

    AggPayPollingRespDTO doWeChatPublicAccountPolling(String orderGUID);

    Mono<AggPayPollingRespDTO> doWeChatPublicAccountPollingAsync(String orderGUID);

    Mono<AggPayReserveResultDTO> doAggPayReserve(AggPayReserveVO accountPayDTO);
}

