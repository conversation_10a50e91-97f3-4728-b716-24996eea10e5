package com.holderzone.holder.saas.store.mdm.pipeline.staff.inputs;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.aop.RequireSign;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.constant.ReqUrlConstants;
import com.holderzone.holder.saas.store.mdm.entity.MDMResult;
import com.holderzone.holder.saas.store.mdm.entity.MDMSynDTO;
import com.holderzone.holder.saas.store.mdm.entity.MdmRespResult;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.UserSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.domain.UserFindDTO;
import com.holderzone.holder.saas.store.mdm.service.MdmOperation;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import com.holderzone.saas.store.dto.user.UserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.holderzone.holder.saas.store.mdm.constant.ReqTypeConstant.POST;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MDMUserSynController
 * @date 2019/11/20 11:44
 * @description 用户同步控制器
 * @program holder-saas-store
 */
@Slf4j
@RestController
@RequestMapping("/sync_user")
public class UserMdmController {

    private final MdmOperation mdmOperation;

    private final MqUtils mqUtils;

    @Autowired
    public UserMdmController(MdmOperation mdmOperation, MqUtils mqUtils) {
        this.mdmOperation = mdmOperation;
        this.mqUtils = mqUtils;
    }

    @RequireSign
    @PostMapping("/create")
    @LogBefore(value = "外部系统批量创建员工", logLevel = LogLevel.INFO)
    public MDMResult<String> userCreateBatchSyn(@Validated(UserSyncDTO.Create.class)
                                                @RequestBody MDMSynDTO<List<UserSyncDTO>> mdmSynDTO) {
        for (UserSyncDTO userSyncDTO : mdmSynDTO.getRequest()) {
            if (userSyncDTO != null) {
                mqUtils.sendMessage(
                        RocketMqConfig.MainConfig.MAIN_MDM_USER_TOPIC,
                        RocketMqConfig.MainConfig.MAIN_MDM_USER_CREATE_TAG,
                        userSyncDTO, UserContextUtils.getEnterpriseGuid()
                );
            }
        }

        return MDMResult.success();
    }

    @RequireSign
    @PostMapping("/update")
    @LogBefore(value = "外部系统更新员工", logLevel = LogLevel.INFO)
    public MDMResult<String> userUpdateSyn(@Validated(UserSyncDTO.Update.class)
                                           @RequestBody MDMSynDTO<List<UserSyncDTO>> mdmSynDTO) {
        for (UserSyncDTO userSyncDTO : mdmSynDTO.getRequest()) {
            if (userSyncDTO != null) {
                mqUtils.sendMessage(
                        RocketMqConfig.MainConfig.MAIN_MDM_USER_TOPIC,
                        RocketMqConfig.MainConfig.MAIN_MDM_USER_UPDATE_TAG,
                        userSyncDTO, UserContextUtils.getEnterpriseGuid()
                );
            }
        }

        return MDMResult.success();
    }

    @RequireSign
    @PostMapping("/delete")
    @LogBefore(value = "外部系统删除员工", logLevel = LogLevel.INFO)
    public MDMResult<String> userDeleteSyn(@Validated(UserSyncDTO.Delete.class)
                                           @RequestBody MDMSynDTO<List<UserSyncDTO>> mdmSynDTO) {
        for (UserSyncDTO userSyncDTO : mdmSynDTO.getRequest()) {
            if (userSyncDTO != null) {
                mqUtils.sendMessage(
                        RocketMqConfig.MainConfig.MAIN_MDM_USER_TOPIC,
                        RocketMqConfig.MainConfig.MAIN_MDM_USER_DELETE_TAG,
                        userSyncDTO, UserContextUtils.getEnterpriseGuid()
                );
            }
        }

        return MDMResult.success();
    }

    @GetMapping("/find")
    @LogBefore(value = "查询员工", logLevel = LogLevel.INFO)
    public MDMResult<UserDTO> findUser(String userGuid) {
        UserFindDTO userFindDTO = new UserFindDTO();
        userFindDTO.setThirdNo(userGuid);
        MdmRespResult<Object> mdmRespResult = mdmOperation.doRequest(POST, ReqUrlConstants.User.FIND_USER_URL, userFindDTO);
        return MDMResult.success(JacksonUtils.toObject(UserDTO.class, JacksonUtils.writeValueAsString(mdmRespResult.getData())));
    }
}
