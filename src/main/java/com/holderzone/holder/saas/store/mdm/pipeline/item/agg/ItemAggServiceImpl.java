package com.holderzone.holder.saas.store.mdm.pipeline.item.agg;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.exception.RepeatedException;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.ItemSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.ItemDO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.mapstruct.ItemMapStruct;
import com.holderzone.holder.saas.store.mdm.pipeline.item.service.ItemService;
import com.holderzone.holder.saas.store.mdm.service.MdmOperation;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import com.holderzone.holder.saas.store.mdm.util.SplitUtils;
import com.holderzone.holder.saas.store.mdm.util.TriggerLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.holderzone.holder.saas.store.mdm.constant.ReqTypeConstant.POST;
import static com.holderzone.holder.saas.store.mdm.constant.ReqUrlConstants.Item.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MdmItemServiceImpl
 * @date 2019/11/23 下午4:20
 * @description //
 * @program holder
 */

@Slf4j
@Service
public class ItemAggServiceImpl implements ItemAggService {

    private final ItemService itemService;

    private final MdmOperation mdmOperation;

    private final MqUtils mqUtils;

    @Value("${mdm.initSyncStep:50}")
    private int initSyncStep;

    @Autowired
    public ItemAggServiceImpl(ItemService itemService, MdmOperation mdmOperation, MqUtils mqUtils) {
        this.itemService = itemService;
        this.mdmOperation = mdmOperation;
        this.mqUtils = mqUtils;
    }

    @Override
    public void triggerRemoteItem(MdmTriggerType mdmTriggerType) {
        List<ItemSyncDTO> itemSyncDTO = itemService.shouldInitItemDOS()
                .stream()
                .filter(itemDO -> itemDO.getIsDelete() == 0)
                .map(ItemMapStruct.INSTANCE::itemDo2itemSynDTO)
                .collect(Collectors.toList());

        TriggerLogUtils.pre("商品", itemSyncDTO.size());
        switch (mdmTriggerType) {
            case CREATE:
                SplitUtils.splitList(itemSyncDTO, initSyncStep).forEach(itemSyncDTOList -> {
                    try {
                        createRemoteItem(itemSyncDTOList);
                    } catch (Exception e) {
                        TriggerLogUtils.stepFailed("商品", e);
                        mqUtils.sendMessage(
                                RocketMqConfig.StoreConfig.STORE_MDM_ITEM_TOPIC,
                                RocketMqConfig.StoreConfig.STORE_MDM_ITEM_CREATE_TAG,
                                itemSyncDTOList, UserContextUtils.getEnterpriseGuid()
                        );
                    }
                    TriggerLogUtils.process("商品", itemSyncDTOList.size());
                });
                break;
            case UPDATE:
                itemSyncDTO.forEach(itemSync -> {
                    try {
                        updateRemoteItem(itemSync);
                    } catch (Exception e) {
                        TriggerLogUtils.singleFailed("商品", e);
                        mqUtils.sendMessage(
                                RocketMqConfig.StoreConfig.STORE_MDM_ITEM_TOPIC,
                                RocketMqConfig.StoreConfig.STORE_MDM_ITEM_UPDATE_TAG,
                                itemSync, UserContextUtils.getEnterpriseGuid()
                        );
                    }
                });
                TriggerLogUtils.process("商品", itemSyncDTO.size());
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + mdmTriggerType);
        }

        TriggerLogUtils.post("商品", itemSyncDTO.size());
    }

    @Override
    public void createRemoteItem(List<ItemSyncDTO> itemSyncDTO) {
        try {
            log.info("向MDM推送创建商品信息：{}", itemSyncDTO);
            mdmOperation.doRequest(POST, BATCH_CREATE_ITEM_URL, itemSyncDTO);
        } catch (RepeatedException e) {
            if (itemSyncDTO.size() == 1) {
                TriggerLogUtils.singleRepeated("商品");
            } else {
                TriggerLogUtils.batchRepeated("商品", e);
                for (ItemSyncDTO syncDTO : itemSyncDTO) {
                    mqUtils.sendMessage(
                            RocketMqConfig.StoreConfig.STORE_MDM_ITEM_TOPIC,
                            RocketMqConfig.StoreConfig.STORE_MDM_ITEM_CREATE_TAG,
                            Collections.singleton(syncDTO), UserContextUtils.getEnterpriseGuid()
                    );
                }
            }
        } catch (BusinessException e) {
            if (itemSyncDTO.size() == 1) {
                throw e;
            } else {
                TriggerLogUtils.batchFailed("商品", e);
                for (ItemSyncDTO syncDTO : itemSyncDTO) {
                    mqUtils.sendMessage(
                            RocketMqConfig.StoreConfig.STORE_MDM_ITEM_TOPIC,
                            RocketMqConfig.StoreConfig.STORE_MDM_ITEM_CREATE_TAG,
                            Collections.singleton(syncDTO), UserContextUtils.getEnterpriseGuid()
                    );
                }
            }
        }
    }

    @Override
    public void updateRemoteItem(ItemSyncDTO mdmItemSynDTO) {
        log.info("向MDM推送修改商品信息：{}", mdmItemSynDTO);
        mdmOperation.doRequest(POST, UPDATE_ITEM_URL, mdmItemSynDTO);
    }

    @Override
    public void deleteRemoteItem(List<ItemSyncDTO> itemSyncDTO) {
        log.info("向MDM推送删除商品信息：{}", itemSyncDTO);
        mdmOperation.doRequest(POST, BATCH_DELETE_ITEM_URL, itemSyncDTO);
    }

    @Override
    public void createLocalItem(ItemSyncDTO itemSyncDTO) {
        ItemDO dbItem = itemService.getLocalRerocd(new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getGuid, itemSyncDTO.getGuid()));
        if (Objects.nonNull(dbItem)) {
            log.info("第三方创建商品信息同步，商品数据已存在");
            log.info("第三方数据：{}", JacksonUtils.writeValueAsString(itemSyncDTO));
            log.info("本地DB数据：{}", JacksonUtils.writeValueAsString(dbItem));
            return;
        }
        itemService.insertItem(itemSyncDTO);
    }

    @Override
    public void updateLocalItem(ItemSyncDTO itemSyncDTO) {
        ItemDO dbItem = itemService.getLocalRerocd(new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getGuid, itemSyncDTO.getGuid()));
        if (Objects.isNull(dbItem)) {
            log.info("第三方修改商品信息同步，商品信息不存在，第三方数据：{}", JacksonUtils.writeValueAsString(itemSyncDTO));
            itemService.insertItem(itemSyncDTO);
            return;
        }
        itemService.updateItemByGuid(itemSyncDTO);
    }

    @Override
    public void deleteLocalItem(ItemSyncDTO itemSyncDTO) {
        ItemDO dbItem = itemService.getLocalRerocd(new LambdaQueryWrapper<ItemDO>().eq(ItemDO::getGuid, itemSyncDTO.getGuid()));
        if (Objects.isNull(dbItem)) {
            log.info("第三方删除商品信息同步，商品信息不存在，第三方数据：{}", JacksonUtils.writeValueAsString(itemSyncDTO));
            return;
        }
        itemService.deleteItemByGuid(itemSyncDTO.getGuid());
    }
}




