package com.holderzone.holder.saas.store.report.dto;


import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 退款明细
 */
@Data
public class RefundDetailExcelDTO implements Serializable {

    private static final long serialVersionUID = -4374335766385207822L;

    /**
     * 门店名称
     */
    @Excel(name = "门店名称", orderNum = "1", width = 15)
    private String storeName;

    /**
     * 结账时间
     */
    @Excel(name = "结账时间", orderNum = "2", width = 25)
    private String checkOutTime;

    /***
     * 退款时间
     */
    @Excel(name = "退款时间", orderNum = "3", width = 25)
    private String refundTime;

    /**
     * 订单编号
     */
    @Excel(name = "订单编号", orderNum = "4", width = 25)
    private String orderNo;

    /**
     * @see com.holderzone.saas.store.enums.order.TradeModeEnum
     * 就餐类型
     */
    @Excel(name = "就餐类型", orderNum = "5", width = 15, replace = {"正餐_0", "快餐_1"})
    private Integer tradeMode;

    /**
     * 退款类型
     */
    @Excel(name = "退款类型", orderNum = "6", width = 15, replace = {"退款_0", "反结账_1"})
    private Integer type;

    /**
     * 区域 + 桌台号
     */
    @Excel(name = "桌台", orderNum = "7", width = 15)
    private String diningTableName;

    /**
     * 订单实收金额
     */
    @Excel(name = "订单实付金额", orderNum = "8", width = 15)
    private String actuallyPayFee;

    /**
     * 实退金额
     */
    @Excel(name = "实退金额", orderNum = "9", width = 15)
    private String refundAmount;

    /**
     * 退款原因
     */
    @Excel(name = "退款原因", orderNum = "10", width = 15)
    private String refundReason;

    /**
     * 退款操作员
     */
    @Excel(name = "退款操作员", orderNum = "11", width = 15)
    private String createStaffName;

    /**
     * 退款授权员
     */
    @Excel(name = "授权人员", orderNum = "12", width = 15)
    private String authStaffName;

    /**
     * 退款方式
     */
    @Excel(name = "退款方式", orderNum = "13", width = 15)
    private String refundDetails;

    /**
     * 会员号
     */
    @Excel(name = "会员", orderNum = "14", width = 15)
    private String memberPhone;
}
