package com.holderzone.holder.saas.store.table.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holder.saas.store.table.domain.TableBasicDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-26
 */
@Repository
public interface TableBasicMapper extends BaseMapper<TableBasicDO> {

//    void inertOneForSortAutoIncrease(TableBasicDO tableBasicDO);

    /**
     *  逻辑删除桌台
     * @param freeTableGuid 桌台GUIDS
     */
    void deleteAll(List<String> freeTableGuid);

    /**
     * 区域桌台最大的排序值
     * @param storeGuid  店铺GUID
     * @param areaGuid  区域GUID
     * @return  返回最大的排序值
     */
    Integer maxSort(@Param("storeGuid") String storeGuid, @Param("areaGuid") String areaGuid);
}
