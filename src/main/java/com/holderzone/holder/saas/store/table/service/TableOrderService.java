package com.holderzone.holder.saas.store.table.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.OrderTableBillReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.*;
import com.holderzone.saas.store.dto.weixin.WxStoreTableCombineDTO;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableOrderService
 * @date 2019/01/02 16:46
 * @description
 * @program holder-saas-store-table
 */
public interface TableOrderService extends IService<TableOrderDO> {

    /**
     * 安卓查询所有桌台信息
     *
     * @param tableBasicQueryDTO
     * @return
     */
    List<TableOrderDTO> listTable(TableBasicQueryDTO tableBasicQueryDTO);

    /**
     * 查询被占用的桌台
     *
     * @param guids
     * @return
     */
    List<String> listTableOccupied(List<String> guids);

    /**
     * 开台
     *
     * @param openTableDTO
     * @return
     */
    String open(OpenTableDTO openTableDTO,Integer tag);

    ReserveOpenTableDTO batchOpen(BatchOpenTableDTO batchOpenTableDTO);

    /**
     * 联台
     */
    String associatedOpen(OpenAssociatedTableDTO openAssociatedTableDTO);


    List<String> reserve(List<String> tableGUids);
    void prepare(List<String> add,List<String> del);
    List<String> cancleReserve(List<String> tableGUids);

    /**
     * 桌台转台接口
     *
     * @param turnTableDTO
     * @return
     */
    boolean turn(TurnTableDTO turnTableDTO);

    /**
     * 并桌操作
     *
     * @param tableCombineDTO
     * @return
     */
    List<String> combine(TableCombineDTO tableCombineDTO, Integer tag);

    /**
     * 并桌校验提示
     */
    TableCombineVerifyRespDTO verifyCombine(TableCombineDTO tableCombineDTO);

    @Transactional(rollbackFor = Exception.class)
    TableCombineRespDTO combine_v2(TableCombineDTO tableCombineDTO, Integer tag);

    /**
     * 桌台拆台接口
     *
     * @param tableOrderCombineDTO
     * @return
     */
    boolean separate(TableOrderCombineDTO tableOrderCombineDTO);

    /**
     * 关台操作
     *
     * @param cancelOrderReqDTO
     * @return
     */
    boolean close(CancelOrderReqDTO cancelOrderReqDTO);



    /**
     * 校验当前桌台是否能够开台
     *
     * @param openTableDTO
     * @return
     */
    boolean couldOpen(OpenTableDTO openTableDTO);

    /**
     * 反结账是否能开台
     *
     * @param baseTableDTO
     * @return
     */
    TableWhetherOpenDTO recheckOpen(BaseTableDTO baseTableDTO);

    /**
     * 尝试开台
     *
     * @param openTableDTO
     * @return
     */
    String tryOpen(OpenTableDTO openTableDTO);

    /**
     * 校验当前桌台是否被占用
     *
     * @param deviceId
     * @param tableGuid
     * @return 返回true表示被占用
     */
    boolean isLocked(String deviceId, String tableGuid);

    /**
     * 校验当前桌台状态并加锁接口
     *
     * @param tableLockDTO
     * @return
     */
    boolean tryLock(TableLockDTO tableLockDTO);

    /**
     * 释放当前桌台的占用锁
     *
     * @param tableLockDTO
     * @return
     */
    boolean tryUnlock(TableLockDTO tableLockDTO);

    /**
     * 桌台结账
     *
     * @param tableStatusChangeDTO
     * @return
     */
    boolean statusSync(TableStatusChangeDTO tableStatusChangeDTO);

    /**
     * 桌台状态补偿接口
     *
     * @param list
     */
    void compensationStatus(List<CompensationTableDTO> list);

    /**
     * @param tableGuid
     * @return
     */
    String queryAreaGuidByTableGuid(String tableGuid);

    String queryOrderGuidByTableGuid(String tableGuid);

	List<WxStoreTableCombineDTO> tableList(String tableGuid);

	TableDTO getFullTableByGuid(String tableGuid);

    @Transactional(rollbackFor = Exception.class)
    boolean updateBatchByTableOrderGuid(Collection<TableOrderDO> entityList);

    List<WxStoreTableCombineDTO> tableCombineList(String mainOrderGuid,String tableGuid);

    List<TableBasicDTO> queryCombineListByMainOrder(SingleDataDTO singleDataDTO);

    void sendTableChangeMsg(SingleDataDTO singleDataDTO);

    List<TableBasicDTO> queryTableByOrderGuid(SingleDataDTO singleDataDTO);
}
