package com.holderzone.holder.saas.store.table.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.table.config.DelayReleaseLockConfig;
import com.holderzone.holder.saas.store.table.domain.TableDO;
import com.holderzone.holder.saas.store.table.domain.bo.TableInfoBO;
import com.holderzone.holder.saas.store.table.service.RedisService;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.holderzone.framework.util.StringUtils.getStr;

/**
 * 官方文档 https://redis.io/commands/eval
 * 中翻文档 https://www.cnblogs.com/PatrickLiu/p/8656675.html
 * <p>
 * JacksonUtils.writeValueAsString()
 * Integer类型 eg: 1
 * String类型 eg: "1" note: 引号也存进redis了
 * Object类型 eg: ["com.holder.saas.print.entity.domain.PrintRecordDO",{"deviceId":"123456"}]
 * List类型 eg: ["java.util.Arrays$ArrayList",[["com.holder.saas.print.entity.domain.PrintRecordDO",{"deviceId":"123456"}]]]
 * List嵌套类型 eg: ["java.util.Arrays$ArrayList",[["java.util.Arrays$ArrayList",[["com.holder.saas.print.entity.domain.PrintRecordDO",{"deviceId":"123456"}]]]]]
 * 以上即是 lua 脚本中如下代码的编写原因：
 * cjson.decode(ARGV[?]) 针对String和包装类型
 * cjson.decode(ARGV[?])[2] 针对Object、List等(非String和包装类型)类型
 *
 * <AUTHOR>
 * @version 1.0
 * @className RedisServiceImpl
 * @date 2018/12/27 11:12
 * @description 缓存实现类
 * @program holder-saas-store-table
 */
@Slf4j
@Service
@SuppressWarnings("unchecked")
public class RedisServiceImpl implements RedisService {

    private final RedisTemplate redisTemplate;

    private final DelayReleaseLockConfig releaseLockConfig;

    private static final String SEPARATOR = ":";

    private static final String COMBINE_TIMES = "combine_times";

    private static final String ASSOCIATED_TIMES = "associated_times";

    private static final String TABLE_LOCK = "table_lock";

    private static final String TABLE_INFO = "table_info";

    private static final String TABLE_GUID = "tableGuid";

    private static final String ORDER_GUID = "orderGuid";

    private static final String DEVICE_ID = "deviceId";

    /**
     * 语法解释
     * cjson.decode c语言的json反序列化，结果是带class信息的对象
     * table.getn 获取 array 的长度
     * ipairs 迭代
     * hmset 设置 hash 数据结构的值
     * expire 设置指定 key 的过期时间
     * <p>
     * 反序列化 fields List<String>
     * 反序列化 multiValues List<List<String>>
     * 判断 keys 长度 和 multiValues 一维长度 是否相等，如果不等，说明参数错误
     * 对 multiValues 进行迭代
     * 为 KEYS[i] 设置多个 field 的值
     */
    private static final String LOCK_TABLE_SCRIPT = " " +
            " local fields = cjson.decode(ARGV[1]) " +
            " local multiValues = cjson.decode(ARGV[2]) " +
            " if(table.getn(KEYS) ~= table.getn(multiValues[2])) " +
            "    then " +
            "       return 1 " +
            "    end " +
            " for i, values in ipairs(multiValues[2]) " +
            "   do " +
            "      redis.call('hmset', KEYS[i], fields[2][1], values[2][1], fields[2][2], values[2][2], fields[2][3], values[2][3]) " +
            "      redis.call('expire', KEYS[i], fields[2][4])" +
            "   end " +
            " return 0";

    /**
     * ARGV[1]/ARGV[2]/ARGV[3] 是带引号的字符串
     * 所以需要通过 cjson.decode 去掉引号，才能从 hmget 中 获取到相应 field 的值
     * <p>
     * table_result[1]/table_result[2]/table_result[3] 是数值类型（redis会将可解析为数值的字符串以数值存储）
     * 所以需要通过 cjson.encode 加上引号，才能返回 String 类型的列表
     */
    private static final String TABLE_STATUS_SCRIPT = "" +
            "local result = {} " +
            "local tableGuid = cjson.decode(ARGV[1]) " +
            "local deviceId = cjson.decode(ARGV[2]) " +
            "local orderGuid = cjson.decode(ARGV[3]) " +
            "for i, j in ipairs(KEYS) " +
            "   do " +
            "       local table_result = redis.call('hmget', j, tableGuid, deviceId, orderGuid) " +
            "       table_result[1] = cjson.encode(table_result[1]) " +
            "       table_result[2] = cjson.encode(table_result[2]) " +
            "       table_result[3] = cjson.encode(table_result[3]) " +
            "       if table_result[1] ~= 'false' and table_result[2] ~= 'false' and table_result[3] ~= 'false'" +
            "           then " +
            "               table.insert(result, table_result) " +
            "           end " +
            "   end " +
            "return result";

    /**
     * 指令解释
     * incr: 如果 key 不存在，那么 key 的值会先被初始化为 0 ，然后再执行 INCR 操作
     * del: 删除指定的 key
     * <p>
     * 边界
     * >99 重置为0
     * ==1 设置过期时间
     * <p>
     * 返回值
     * incr 之后的值
     */
    private static final String COMBINE_TIMES_SCRIPT = "" +
            "local times = redis.call('incr', KEYS[1]) " +
            "if times > 99 then " +
            "    redis.call('del', KEYS[1]) " +
            "    times = redis.call('incr', KEYS[1]) " +
            "end " +
            "if times == 1 then " +
            "    redis.call('expire',KEYS[1], tonumber(ARGV[1])) " +
            "end " +
            "return times";

    private static final String COMBINE_TIMES_ROLLBACK_SCRIPT = "" +
            "local times = redis.call('decr', KEYS[1]) " +
            "if times < 0 then " +
            "    redis.call('del', KEYS[1]) " +
            "end " +
            "return times";

    @Autowired
    public RedisServiceImpl(RedisTemplate redisTemplate, DelayReleaseLockConfig releaseLockConfig) {
        this.redisTemplate = redisTemplate;
        this.releaseLockConfig = releaseLockConfig;
    }

    @Override
    public String singleGuid(String tableName) {
        List<Long> guidList = BatchIdGenerator.buildSnowFlakeGuids(tableName, 1);
        return String.valueOf(guidList.get(0));
    }

    @Override
    public List<String> batchGuid(int size, String tableName) {
        try {
            List<Long> longs = BatchIdGenerator.batchGetGuids(redisTemplate, tableName, size);
            return longs.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());
        } catch (ParameterException e) {
            e.printStackTrace();
            throw new BusinessException("批量生成guid失败");
        }
    }

    @Override
    public void lockSingleTable(String tableGuid, String deviceId, String orderGuid, Integer deviceType) {
        TableInfoBO tableInfoBO = new TableInfoBO(tableGuid, deviceId, orderGuid);
        lockMultiTable(Collections.singletonList(tableInfoBO), deviceType);
    }

    @Override
    public void lockMultiTable(List<TableInfoBO> tableInfoBOS, Integer deviceType) {
        DefaultRedisScript<Long> longDefaultRedisScript = new DefaultRedisScript<>();
        longDefaultRedisScript.setResultType(Long.class);
        longDefaultRedisScript.setScriptText(LOCK_TABLE_SCRIPT);
        List<String> keys = new ArrayList<>();
        List<String> fields = new ArrayList<>();
        fields.add(TABLE_GUID);
        fields.add(DEVICE_ID);
        fields.add(ORDER_GUID);
        if(Objects.equals(12, deviceType)) {
        	fields.add(String.valueOf(releaseLockConfig.getWxTime()));
		} else {
			fields.add(String.valueOf(releaseLockConfig.getTime()));
		}
        List<List<String>> values = new ArrayList<>();
        for (TableInfoBO tableInfoBO : tableInfoBOS) {
            String tableGuid = tableInfoBO.getTableGuid();
            String deviceId = tableInfoBO.getDeviceId();
            String orderGuid = tableInfoBO.getOrderGuid();
            keys.add(getStr(SEPARATOR, TABLE_LOCK, tableGuid));
            values.add(Arrays.asList(tableGuid, deviceId, orderGuid));
        }
        Object result = redisTemplate.execute(longDefaultRedisScript, keys, fields, values);
        if (null != result && (Long) result != 0) {
            throw new BusinessException("不能同时锁定桌台");
        }
    }

    @Override
    public boolean isTableLockedByOthers(String deviceId, String tableGuid) {
        TableInfoBO tableInfoBO = getTableLockStatus(tableGuid);
        return Objects.nonNull(tableInfoBO)
                && StringUtils.hasText(tableInfoBO.getDeviceId())
                && !tableInfoBO.getDeviceId().equalsIgnoreCase(deviceId);
    }

    @Override
    public boolean isTableLockedByOthers(String deviceId, List<String> tableGuidList) {
        List<TableInfoBO> tableList = getTableLockStatus(tableGuidList);
        if (CollectionUtils.isEmpty(tableList)) {
            return false;
        }
        for (TableInfoBO tableInfoBO : tableList) {
            if (StringUtils.hasText(tableInfoBO.getDeviceId())
                    && !tableInfoBO.getDeviceId().equalsIgnoreCase(deviceId)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isUnlockAllowed(String tableGuid, String deviceId, String orderGuid) {
        TableInfoBO tableInfoBO = getTableLockStatus(tableGuid);
        return Objects.nonNull(tableInfoBO)
                && StringUtils.hasText(tableInfoBO.getDeviceId())
                && tableInfoBO.getDeviceId().equalsIgnoreCase(deviceId)
                && StringUtils.hasText(tableInfoBO.getOrderGuid())
                && tableInfoBO.getOrderGuid().equalsIgnoreCase(orderGuid);
    }

    @Override
    public void unlockMultiTable(List<String> tableGuidList) {
        List<String> keys = tableGuidList.stream()
                .map(tableGuid -> getStr(SEPARATOR, TABLE_LOCK, tableGuid))
                .collect(Collectors.toList());
        redisTemplate.delete(keys);
    }

    @Override
    public Integer getCombineTimes(String storeGuid) {
        DefaultRedisScript<Long> longDefaultRedisScript = new DefaultRedisScript<>();
        longDefaultRedisScript.setResultType(Long.class);
        longDefaultRedisScript.setScriptText(COMBINE_TIMES_SCRIPT);
        String key = StringUtils.getStr(SEPARATOR, COMBINE_TIMES, storeGuid);
        LocalDateTime now = DateTimeUtils.now();
        LocalDateTime endTimeOfDay = DateTimeUtils.endTimeOfDay(now);
        long seconds = Duration.between(now, endTimeOfDay).getSeconds();
        Object execute = redisTemplate.execute(longDefaultRedisScript, Collections.singletonList(key), seconds);
        Long times = Optional.ofNullable((Long) execute).orElse(0L);
        return times.intValue();
    }

    @Override
    public Integer getAssociatedTimes(String storeGuid) {
        DefaultRedisScript<Long> longDefaultRedisScript = new DefaultRedisScript<>();
        longDefaultRedisScript.setResultType(Long.class);
        longDefaultRedisScript.setScriptText(COMBINE_TIMES_SCRIPT);
        String key = StringUtils.getStr(SEPARATOR, ASSOCIATED_TIMES, storeGuid);
        LocalDateTime now = DateTimeUtils.now();
        LocalDateTime endTimeOfDay = DateTimeUtils.endTimeOfDay(now);
        long seconds = Duration.between(now, endTimeOfDay).getSeconds();
        Object execute = redisTemplate.execute(longDefaultRedisScript, Collections.singletonList(key), seconds);
        Long times = Optional.ofNullable((Long) execute).orElse(0L);
        return times.intValue();
    }

    @Override
    public Integer rollBackAssociatedTimes(String storeGuid) {
        DefaultRedisScript<Long> longDefaultRedisScript = new DefaultRedisScript<>();
        longDefaultRedisScript.setResultType(Long.class);
        longDefaultRedisScript.setScriptText(COMBINE_TIMES_ROLLBACK_SCRIPT);
        String key = StringUtils.getStr(SEPARATOR, ASSOCIATED_TIMES, storeGuid);
        LocalDateTime now = DateTimeUtils.now();
        LocalDateTime endTimeOfDay = DateTimeUtils.endTimeOfDay(now);
        long seconds = Duration.between(now, endTimeOfDay).getSeconds();
        Object execute = redisTemplate.execute(longDefaultRedisScript, Collections.singletonList(key), seconds);
        Long times = Optional.ofNullable((Long) execute).orElse(0L);
        return times.intValue();
    }

    @Override
    @Deprecated
    public void putTableDoList(List<TableDO> tableDOList, String storeGuid) {
        String key = getStr(SEPARATOR, TABLE_INFO, storeGuid);
        redisTemplate.opsForList().rightPushAll(key, tableDOList);
    }

    @Override
    public TableInfoBO getTableLockStatus(String tableGuid) {
        List<TableInfoBO> tableList = getTableLockStatus(Collections.singletonList(tableGuid));
        if (CollectionUtils.isEmpty(tableList)) {
            return null;
        }
        return tableList.get(0);
    }

    @Override
    public List<TableInfoBO> getTableLockStatus(List<String> tableGuidList) {
        DefaultRedisScript<String> longDefaultRedisScript = new DefaultRedisScript<>();
        longDefaultRedisScript.setResultType(String.class);
        longDefaultRedisScript.setScriptText(TABLE_STATUS_SCRIPT);
        List<String> keys = tableGuidList.stream()
                .map(tableGuid -> StringUtils.getStr(SEPARATOR, TABLE_LOCK, tableGuid))
                .collect(Collectors.toList());
        long startTime = System.currentTimeMillis();
        List<List<String>> tableList = (List<List<String>>) redisTemplate.execute(
                longDefaultRedisScript, keys, TABLE_GUID, DEVICE_ID, ORDER_GUID
        );
        log.info("getTableLockStatus lua 耗时:{}", System.currentTimeMillis() - startTime);
        if (CollectionUtils.isEmpty(tableList)) {
            return Collections.emptyList();
        }
        return tableList.stream()
                .map(table -> new TableInfoBO(table.get(0), table.get(1), table.get(2)))
                .collect(Collectors.toList());
    }
}
