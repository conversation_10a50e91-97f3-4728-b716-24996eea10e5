package com.holderzone.holder.saas.store.pay.config;

import com.netflix.hystrix.exception.HystrixBadRequestException;
import feign.Response;
import feign.Util;
import feign.codec.ErrorDecoder;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BizErrorDecoder
 * @date 2018/11/06 13:55
 * @description
 * @program holder-saas-store-trading-center
 */
@Configuration
public class BizErrorDecoder extends ErrorDecoder.Default {

    private static final int BIZ_CODE_LOWER_BOUNDARY_INCLUDE = 400;

    private static final int BIZ_CODE_UPPER_BOUNDARY_EXCLUDE = 500;

    @Override
    public Exception decode(String methodKey, Response response) {
        int status = response.status();
        if (BIZ_CODE_LOWER_BOUNDARY_INCLUDE <= status
                && status < BIZ_CODE_UPPER_BOUNDARY_EXCLUDE) {
            String body = "未知业务错误";
            try {
                body = Util.toString(response.body().asReader());
            } catch (IOException ignore) {

            }
            throw new HystrixBadRequestException(body);
        }
        return super.decode(methodKey, response);
    }
}
