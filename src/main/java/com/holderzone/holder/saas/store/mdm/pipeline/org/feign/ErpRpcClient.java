package com.holderzone.holder.saas.store.mdm.pipeline.org.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className EnterpeiseClient
 * @date 19-1-4 下午5:21
 * @description
 * @program holder-saas-store-organization
 */
@Component
@FeignClient(value = "holder-saas-cloud-enterprise", fallbackFactory = ErpRpcClient.ServiceFallBack.class)
public interface ErpRpcClient {

    /**
     * 查询organizationCode是否重复
     *
     * @param code organizationCode
     * @return true-不存在，该code可用，false-已存在，该code不可用
     */

    @GetMapping("/organization/check/{code}")
    Boolean checkCode(@PathVariable("code") String code);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ErpRpcClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";


        @Override
        public ErpRpcClient create(Throwable cause) {
            return code -> {
                log.error(HYSTRIX_PATTERN, "checkCode", "组织code为：" + code,
                          ThrowableUtils.asString(cause));
                throw new BusinessException("enterprise-service服务熔断，调用checkcode方法失败");
            };
        }
    }
}
