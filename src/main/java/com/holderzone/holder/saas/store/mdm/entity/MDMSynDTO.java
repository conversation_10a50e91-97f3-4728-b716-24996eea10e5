package com.holderzone.holder.saas.store.mdm.entity;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MDMSynDTO
 * @date 2019/11/14 11:25
 * @description mdm同步请求参数
 * @program holder-saas-store
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonPropertyOrder(alphabetic = true)
public class MDMSynDTO<T> {

    @NotBlank(message = "开发者ID不得为空")
    private String developerId;

    @NotBlank(message = "签名不得为空")
    private String signature;

//    @NotBlank(message = "数据来源")
    private String source;

    @Valid
    @NotNull(message = "请求体不得为空")
    private T request;
}
