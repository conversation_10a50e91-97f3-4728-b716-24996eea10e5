package com.holderzone.holder.saas.store.deposit.controller;


import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.deposit.service.IHsdDepositService;
import com.holderzone.holder.saas.store.deposit.service.IHsdGoodsService;
import com.holderzone.saas.store.dto.deposit.req.*;
import com.holderzone.saas.store.dto.deposit.resp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-21
 */
@Slf4j
@Api("寄存接口")
@RestController
@RequestMapping("/deposit")
public class DepositController {

    private final IHsdDepositService iHsdDepositService;
    private final IHsdGoodsService iHsdGoodsService;

    @Autowired
    public DepositController(IHsdDepositService iHsdDepositService,
                             IHsdGoodsService iHsdGoodsService) {
        this.iHsdDepositService = iHsdDepositService;
        this.iHsdGoodsService = iHsdGoodsService;

    }


    @PostMapping("/create_deposit_item")
    @ApiOperation(value = "新建寄存记录")
    public Boolean createDepositItem(@RequestBody @Validated DepositCreateReqDTO depositCreateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("新建寄存记录入参:{}", JacksonUtils.writeValueAsString(depositCreateReqDTO));
        }
        return iHsdDepositService.createDepositRecord(depositCreateReqDTO);
    }

    @PostMapping("/query_deposit_item")
    @ApiOperation(value = "查询寄存记录")
    public Page<DepositQueryRespDTO> queryDepositItem(@RequestBody @Validated DepositQueryReqDTO depositQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询寄存记录入参:{}", JacksonUtils.writeValueAsString(depositQueryReqDTO));
        }
        return iHsdDepositService.queryDepositRecord(depositQueryReqDTO);
    }

    @PostMapping("/query_deposit_detail")
    @ApiOperation(value = "查询寄存记录详情")
    public List<GoodsRespDTO> queryDepositDetail(@RequestBody @Validated QueryDepositDetailReqDTO depositQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询寄存记详情入参:{}", JacksonUtils.writeValueAsString(depositQueryReqDTO));
        }
        return iHsdDepositService.queryDepositDetail(depositQueryReqDTO);
    }

    @PostMapping("/query_deposit_detail_for_pos")
    @ApiOperation(value = "查询寄存记录详情")
    public DepositDetailForPosRespDTO queryDepositDetailForPos(@RequestBody @Validated QueryDepositDetailReqDTO depositQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询寄存记详情入参:{}", JacksonUtils.writeValueAsString(depositQueryReqDTO));
        }
        return iHsdDepositService.queryDepositDetailForPos(depositQueryReqDTO);
    }

    @PostMapping("/get_deposit_goods")
    @ApiOperation(value = "取出寄存商品")
    public boolean getDepositGoods(@RequestBody @Validated DepositGetReqDTO depositGetReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("取出寄存商品:{}", JacksonUtils.writeValueAsString(depositGetReqDTO));
        }
        return iHsdDepositService.getDeposit(depositGetReqDTO);
    }

    @PostMapping("/query_operation_history")
    @ApiOperation(value = "查询操作历史记录")
    public Page<OperationQueryRespDTO> queryOperationHistory(@RequestBody @Validated OperationHistoryQueryReqDTO operationHistoryQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询操作历史记录:{}", JacksonUtils.writeValueAsString(operationHistoryQueryReqDTO));
        }
        return iHsdDepositService.queryOperationHistory(operationHistoryQueryReqDTO);
    }

    @PostMapping("/query_goods_summary")
    @ApiOperation(value = "商品寄存汇总")
    public Page<GoodsSummaryRespDTO> queryGoodsSummary(@RequestBody @Validated DepositQueryReqForWebDTO depositQueryReqForWebDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询商品寄存汇总入参:{}", JacksonUtils.writeValueAsString(depositQueryReqForWebDTO));
        }
        return iHsdDepositService.queryGoodsSummary(depositQueryReqForWebDTO);
    }

    @PostMapping("/deposit_expire_remind")
    @ApiOperation(value = "寄存商品到期提醒")
    public Boolean depositExpireRemind() {
        if (log.isInfoEnabled()) {
            log.info("寄存商品到期提醒:{}", DateTimeUtils.nowString());
        }
        iHsdDepositService.sendExpireRemindMessage();
        return true;
    }

    @PostMapping("/remind_set")
    @ApiOperation(value = "短信提示设置")
    public Boolean remindSet(@RequestBody @Validated MessageRemindReqDTO messageRemindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("短信提示设置:{}", messageRemindReqDTO);
        }
        return iHsdDepositService.remindSet(messageRemindReqDTO);
    }

    @PostMapping("/query_remind")
    @ApiOperation(value = "获取短信提示设置")
    public MessageRemindReqDTO queryRemind(@RequestBody @Validated ModifyRemindReqDTO modifyRemindReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("获取短信提示设置:{}", modifyRemindReqDTO);
        }
        return iHsdDepositService.queryRemind(modifyRemindReqDTO.getStoreGuid());
    }
}
