package com.holderzone.holder.saas.store.message.listener;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.message.constant.MqConstants;
import com.holderzone.holder.saas.store.message.service.MessageService;
import com.holderzone.holder.saas.store.message.util.DynamicHelper;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReceiveMsgListener
 * @date 2020/03/06 上午10:21
 * @description //消息监听器
 * @program holder-saas-store
 */

@Component
@RocketListenerHandler(
        topic = MqConstants.MSG_TOPIC,
        tags = {MqConstants.MSG_TAG, MqConstants.BATCH_MSG_TAG},
        consumerGroup = MqConstants.MAIN_MSG_GROUP
)
public class MsgListener extends AbstractRocketMqConsumer<RocketMqTopic, UnMessage<String>> {

    private final DynamicHelper dynamicHelper;

    private final MessageService messageService;

    @Autowired
    public MsgListener(DynamicHelper dynamicHelper, MessageService messageService) {
        this.dynamicHelper = dynamicHelper;
        this.messageService = messageService;
    }

    @Override
    public boolean consumeMsg(UnMessage<String> unMessage, MessageExt messageExt) {
        String enterpriseGuid = unMessage.getEnterpriseGuid();
        if (StringUtils.isEmpty(enterpriseGuid)) {
            logger.info("当前消息：{} 无企业GUID", JacksonUtils.writeValueAsString(unMessage));
            return false;
        }
        try {
            dynamicHelper.changeDatasource(enterpriseGuid);

            String message = unMessage.getMessage();
            switch (messageExt.getTags()) {
                case MqConstants.BATCH_MSG_TAG:
                    logger.info("批量接收消息：【{}】", message);
                    messageService.insertAll(JacksonUtils.toObjectList(BusinessMessageDTO.class, message));
                    break;
                case MqConstants.MSG_TAG:
                    logger.info("接收单条消息：【{}】", message);
                    messageService.insert(JacksonUtils.toObject(BusinessMessageDTO.class, message));
                    break;
                default:
                    logger.info("Unknown Msg TAG :【{}】", messageExt.getTags());
                    return false;
            }
        } catch (Exception e) {
            logger.error("Failed to consume message, error msg :\n " + e);
            return false;
        }
        return true;
    }
}
