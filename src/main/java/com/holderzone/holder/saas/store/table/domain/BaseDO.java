package com.holderzone.holder.saas.store.table.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/12/26 11:08
 */
@Data
public abstract class BaseDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    protected Long id;

    protected String guid;

    protected String storeGuid;

    protected String storeName;

}
