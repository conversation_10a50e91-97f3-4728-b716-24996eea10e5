package com.holderzone.holder.saas.store.pay.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PayConstant
 * @date 2019/03/14 15:03
 * @description
 * @program holder-saas-store-trading-center
 */
public interface PayConstant {

    String SUCCESS_CODE = "10000";

    String SUCCESS_DESC = "success";

    String PROCESSED = "20045";

    String PLATFORM_EXCEPTION = "-10000";

    String SUCCESS_CALL_BACK = "success";

    String HANDLER_PAY = "handler_pay";

    String REDIRECT_URI = "redirectUri";

    String PLACE_AN_ORDER_TO_CHECK_THE_BANK = "下单查询银行";

    String AGGREGATE_PAYMENT_PLATFORM_ABNORMAL = "聚合支付平台异常";

    String PLACE_ORDER_QUERY_BANK_REQUEST = "向聚合支付平台发起下单查询银行请求，请求参数：{}";
//
//    String INNER_CALLBACK_URL = "http://holder-saas-store-trade/dine_in_bill/callback";
//
//    String WEIXIN_INNER_CALLBACK_URL = "http://holder-saas-store-weixin/wx_store_pay/result_operation";
}
