package com.holderzone.holder.saas.store.report.service.rpc.business;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.report.query.HandOverReportQueryDTO;
import com.holderzone.saas.store.dto.report.resp.HandoverReportRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = HandoverClientService.BusinessFallBack.class)
public interface HandoverClientService {

    @ApiOperation(value = "交接班报表查询")
    @PostMapping("/handover/report")
    List<HandoverReportRespDTO> report(@RequestBody HandOverReportQueryDTO handOverQueryDTO);

    @Component
    class BusinessFallBack implements FallbackFactory<HandoverClientService> {

        private static final Logger logger = LoggerFactory.getLogger(BusinessFallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public HandoverClientService create(Throwable throwable) {
            return handOverQueryDTO -> {
                logger.error(HYSTRIX_PATTERN, "report", JacksonUtils.writeValueAsString(handOverQueryDTO),
                        ThrowableUtils.asString(throwable));
                throw new ServerException();
            };
        }
    }

}
