package com.holderzone.holder.saas.store.deposit.service.rpc;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(name = "holder-saas-store-organization",fallbackFactory = StoreRpcService.FallBack.class)
public interface StoreRpcService {

    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    @Component
    class FallBack implements FallbackFactory<StoreRpcService> {
        private static final Logger logger = LoggerFactory.getLogger(StoreRpcService.FallBack.class);

        @Override
        public StoreRpcService create(Throwable throwable) {
            return new StoreRpcService() {
                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    logger.error("发送短信失败,message={}",throwable.getMessage());
                    throw new ParameterException(throwable.getMessage());
                }
            };
        }
    }

}
