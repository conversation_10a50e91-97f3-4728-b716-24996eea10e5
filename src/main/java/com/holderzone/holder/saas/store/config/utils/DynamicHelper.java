package com.holderzone.holder.saas.store.config.utils;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.sdk.util.BatchIdGenerator;
import com.holderzone.sdk.util.IdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;


import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DynamicUtils
 * @date 2018/10/25 15:28
 * @description //TODO
 * @program holder-saas-store-order
 */
@Component
public class DynamicHelper {

    private static final Logger log = LoggerFactory.getLogger(DynamicHelper.class);

    @Value("${self.open-dynamic-datasource}")
    private Boolean openDynamicDatasource;

    private String redisKey = "config";


    /**
     * 生成guid
     *
     * @return
     */
    public String generateGuid() {
        RedisTemplate redisTemplate = SpringContextUtils.getInstance().getBean("redisTemplate");
        return String.valueOf(IdGenerator.builder(redisTemplate, 5).next(redisKey));
    }


    /**
     * 生成guid
     *
     * @param redisKey
     * @return
     */
    public List<Long> generateGuids(String redisKey, long count) {

        RedisTemplate redisTemplate = SpringContextUtils.getInstance().getBean("redisTemplate");
        List<Long> list = null;
        try {
            list = BatchIdGenerator.batchGetGuids(redisTemplate, redisKey, count);
        } catch (Exception e) {
            log.error("生成guid失败，e={}", e.getMessage());
            throw new ParameterException("生成guid失败，e=" + e.getMessage());
        }
        return list;
    }

}
