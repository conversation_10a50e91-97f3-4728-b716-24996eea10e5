package com.holderzone.holder.saas.store.table.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.store.table.domain.AreaDO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.table.AreaDTO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-26
 */
public interface AreaService extends IService<AreaDO> {

    /**
     * 初始化门店区域已经当前区域下的桌台
     *
     * @param baseDTO 请求头dto
     * @return 返回结果
     */
    String initArea(BaseDTO baseDTO);

    /**
     * 新增门店区域
     *
     * @param areaDTO 区域DTO
     * @return 区域结果
     */
    String createArea(AreaDTO areaDTO);

    /**
     * 更新区域
     *
     * @param areaDTO 区域DTO
     * @return 结果
     */
    String updateArea(AreaDTO areaDTO);

    /**
     * 删除区域
     *
     * @param guid 区域GUID
     * @return 结果
     */
    String deleteArea(String guid);

    /**
     * 查询当前门店区域
     *
     * @param storeGuid 店铺GUID
     * @return 区域列表
     */
    List<AreaDTO> queryAll(String storeGuid);


    /**
     * 批量查询桌台区域列表
     */
    List<AreaDTO> batchQueryAll(List<String> storeGuids);

    /**
     * 根据桌台查询桌位
     *
     * @param areaGuid 桌台guid
     * @return 区域信息
     */
    AreaDTO queryAreaByTable(String areaGuid);

    /**
     * 批量根据桌台查询桌位
     *
     * @param areaGuidList 桌台guid
     * @return 区域信息
     */
    List<AreaDTO> queryBatchAreaByTable(List<String> areaGuidList);
}
