package com.holderzone.holder.saas.store.report.mapper;

import com.holderzone.saas.store.dto.report.openapi.MemberCardRespDTO;
import com.holderzone.saas.store.dto.report.openapi.MemberDetailRespDTO;
import com.holderzone.saas.store.dto.report.openapi.MemberFundingDetailQueryDTO;
import com.holderzone.saas.store.dto.report.openapi.MemberFundingDetailRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会员
 */
@Mapper
public interface MemberMapper {

    /**
     * 查询资金变动明细
     */
    List<MemberFundingDetailRespDTO> queryFundingDetail(@Param("query") MemberFundingDetailQueryDTO query);

    /**
     * 查询会员
     */
    List<MemberDetailRespDTO> queryMember(@Param("list") List<String> list);

    /**
     * 查询会员卡
     */
    List<MemberCardRespDTO> queryMemberCard(@Param("list") List<String> list);
}
