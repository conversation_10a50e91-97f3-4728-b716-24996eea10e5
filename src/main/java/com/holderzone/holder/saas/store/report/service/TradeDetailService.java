package com.holderzone.holder.saas.store.report.service;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.report.query.AggPayServiceChargeQueryDTO;
import com.holderzone.saas.store.dto.report.query.CloudPayConstituteQueryDTO;
import com.holderzone.saas.store.dto.report.query.PaymentConstituteQueryDTO;
import com.holderzone.saas.store.dto.report.query.TradeDetailQueryDTO;
import com.holderzone.saas.store.dto.report.resp.CloudPayConstituteRespDTO;
import com.holderzone.saas.store.dto.report.resp.GrouponTradeDetailRespDTO;
import com.holderzone.saas.store.dto.report.resp.PaymentConstituteRespDTO;
import com.holderzone.saas.store.dto.report.resp.TakeawayTradeDetailRespDTO;

import java.math.BigDecimal;


public interface TradeDetailService {

    Page<GrouponTradeDetailRespDTO> pageGroupon(TradeDetailQueryDTO query);

    Page<TakeawayTradeDetailRespDTO> pageTakeaway(TradeDetailQueryDTO query);

    String exportGroupon(TradeDetailQueryDTO query);

    String exportTakeaway(TradeDetailQueryDTO query);

    Page<PaymentConstituteRespDTO> paymentConstitute(PaymentConstituteQueryDTO query);

    String exportPaymentConstitute(PaymentConstituteQueryDTO query);

    CloudPayConstituteRespDTO cloudPayConstitute(CloudPayConstituteQueryDTO query);

    BigDecimal queryAggPayServiceCharge(AggPayServiceChargeQueryDTO query);

}
