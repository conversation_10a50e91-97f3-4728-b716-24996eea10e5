package com.holderzone.holder.saas.store.mdm.pipeline.item.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.UserSyncDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TypeSyncDTO
 * @date 2019/11/19 下午5:59
 * @description //MDM 商品分类同步dto
 * @program holder
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("mdm商品type同步DTO")
@JsonPropertyOrder(alphabetic = true)
public class TypeSyncDTO {

    /**
     * 主键
     */
    @JsonProperty("guid")
    @ApiModelProperty(value = "MDM生成的唯一标识", required = false)
    private String uuid;

    /**
     * 第三方唯一标识
     */
    @JsonProperty("thirdNo")
    @NotNull(message = "第三方标识不得为空", groups = {Create.class, Update.class, Delete.class})
    @ApiModelProperty(value = "第三方唯一标识", required = true)
    private String guid;


    /**
     * 门店GUID
     */
    @Nullable
    @ApiModelProperty(value = "门店guid", required = false)
    private String storeGuid;

    /**
     * 品牌GUID
     */
    @Nullable
    @ApiModelProperty(value = "品牌guid", required = false)
    private String brandGuid;
    /**
     * 名称
     */
    @NotBlank(message = "分类名称", groups = {Create.class, Update.class})
    @ApiModelProperty(value = "分类名称", required = true)
    private String name;

    /**
     * 分类描述
     */
    @Nullable
    @ApiModelProperty(value = "分类描述", required = false)
    private String description;

    /**
     * 图标
     */
    @Nullable
    @ApiModelProperty(value = "图标", required = false)
    private String iconUrl;

    /**
     * 分类来源（0：门店，1：品牌）
     */
    @NotNull(message = "分类来源（0：门店，1：品牌）", groups = {Create.class, Update.class})
    @ApiModelProperty(value = "分类来源（0：门店，1：品牌）", required = true)
    private Integer typeFrom;

    public interface Create extends Default {

    }

    public interface Update extends Default {

    }

    public interface Delete extends Default {

    }
}
