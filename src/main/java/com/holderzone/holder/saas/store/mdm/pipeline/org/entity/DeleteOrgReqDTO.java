package com.holderzone.holder.saas.store.mdm.pipeline.org.entity;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
@Accessors(chain = true)
@JsonPropertyOrder(alphabetic = true)
public class DeleteOrgReqDTO {

    /**
     * 删除类型
     */
    @NotNull(message = "组织类型不得为空")
    private Integer type;

    /**
     * 组织列表
     */
    @Valid
    @NotEmpty(message = "组织列表不得为空")
    private List<OrganizationInfoDTO> list;
}
