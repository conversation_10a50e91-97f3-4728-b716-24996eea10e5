package com.holderzone.holder.saas.store.report.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Assert;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.report.constant.ErrorConstant;
import com.holderzone.holder.saas.store.report.helper.ExceptionHelper;
import com.holderzone.holder.saas.store.report.service.TradeItemService;
import com.holderzone.saas.store.dto.journaling.req.SalesVolumeReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeStoreRespDTO;
import com.holderzone.saas.store.dto.report.query.GoodsSalesVO;
import com.holderzone.saas.store.dto.report.query.SalesDetailQO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesDTO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesStatisticDTO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesTotalDTO;
import com.holderzone.saas.store.dto.report.resp.SalesDetailRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;


/**
 * 订单商品报表
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/trade/item")
public class TradeItemController {

    private final TradeItemService tradeItemService;

    @ApiOperation(value = "商品分类统计")
    @PostMapping("/type/statistics")
    public GoodsSalesStatisticDTO queryItemTypeStatistics(@RequestBody @Valid GoodsSalesVO query) {
        log.info("[商品分类统计]请求入参：{}", JacksonUtils.writeValueAsString(query));
        try {
            return tradeItemService.queryItemTypeStatistics(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "商品分类统计", JacksonUtils.writeValueAsString(query)));
        }
    }

    @ApiOperation(value = "商品分类统计导出")
    @PostMapping("/type/statistics/export")
    public String exportItemTypeStatistics(@RequestBody @Valid GoodsSalesVO query) {
        log.info("[商品分类统计导出]请求入参：{}", JacksonUtils.writeValueAsString(query));
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeItemService.exportItemTypeStatistics(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "导出商品分类统计", JacksonUtils.writeValueAsString(query)));
        }
    }

    @ApiOperation(value = "套餐销量统计")
    @PostMapping("/group/sale/statistics")
    public GoodsSalesStatisticDTO queryGroupItemSaleStatistics(@RequestBody @Valid GoodsSalesVO query) {
        log.info("[套餐销量统计]请求入参：{}", JacksonUtils.writeValueAsString(query));
        try {
            return tradeItemService.queryGroupItemSaleStatistics(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "套餐销量统计", JacksonUtils.writeValueAsString(query)));
        }
    }


    @ApiOperation(value = "套餐销量统计导出")
    @PostMapping("/group/sale/statistics/export")
    public String exportGroupItemSaleStatistics(@RequestBody @Valid GoodsSalesVO query) {
        log.info("[套餐销量统计导出]请求入参：{}", JacksonUtils.writeValueAsString(query));
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeItemService.exportGroupItemSaleStatistics(query);
        } catch (Exception e) {
            log.error("[导出套餐销量统计]异常,e=", e);
            throw new BusinessException(ExceptionHelper.throwException(e, "导出套餐销量统计", JacksonUtils.writeValueAsString(query)));
        }
    }

    @ApiOperation(value = "门店商品销量分类查询")
    @PostMapping("/store/sale/statistics/type")
    public List<String> pageStoreSaleStatisticsType(@RequestBody SalesVolumeReqDTO query) {
        log.info("[门店商品销量分类查询]请求入参：{}", JacksonUtils.writeValueAsString(query));
        try {
            return tradeItemService.pageStoreSaleStatisticsType(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "门店商品销量分类查询", JacksonUtils.writeValueAsString(query)));
        }
    }

    @ApiOperation(value = "门店商品销量套餐查询")
    @PostMapping("/store/sale/statistics/group")
    public List<GoodsSalesDTO> pageStoreSaleStatisticsGroup(@RequestBody SalesVolumeReqDTO query) {
        log.info("[门店商品销量套餐查询]请求入参：{}", JacksonUtils.writeValueAsString(query));
        try {
            return tradeItemService.pageStoreSaleStatisticsGroup(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "门店商品销量套餐查询", JacksonUtils.writeValueAsString(query)));
        }
    }

    @ApiOperation(value = "门店商品销量")
    @PostMapping("/store/sale/statistics")
    public Page<SalesVolumeRespDTO> pageStoreSaleStatistics(@RequestBody SalesVolumeReqDTO query) {
        log.info("[门店商品销量]请求入参：{}", JacksonUtils.writeValueAsString(query));
        try {
            return tradeItemService.pageStoreSaleStatistics(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "门店商品销量", JacksonUtils.writeValueAsString(query)));
        }
    }

    @ApiOperation(value = "门店商品销量 - 按门店统计")
    @PostMapping("/group_by_store/sale/statistics")
    public Page<SalesVolumeStoreRespDTO> pageGroupByStoreSaleStatistics(@RequestBody SalesVolumeReqDTO query) {
        log.info("[门店商品销量]请求入参：{}", JacksonUtils.writeValueAsString(query));
        try {
            query.setGroupByStoreFlag(true);
            return tradeItemService.pageGroupByStoreSaleStatistics(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "门店商品销量", JacksonUtils.writeValueAsString(query)));
        }
    }

    @ApiOperation(value = "门店商品销量合计")
    @PostMapping("/store/sale/statistics/total")
    public GoodsSalesTotalDTO storeSaleStatisticsTotal(@RequestBody SalesVolumeReqDTO query) {
        log.info("[门店商品销量合计]请求入参：{}", JacksonUtils.writeValueAsString(query));
        try {
            return tradeItemService.storeSaleStatisticsTotal(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "门店商品销量合计", JacksonUtils.writeValueAsString(query)));
        }
    }

    @ApiOperation(value = "销售明细")
    @PostMapping("/sale/detail/page")
    public Page<SalesDetailRespDTO> pageSaleDetail(@RequestBody @Valid SalesDetailQO query) {
        log.info("[销售明细]query={}", JacksonUtils.writeValueAsString(query));
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeItemService.pageSaleDetail(query);
        } catch (Exception e) {
            throw new BusinessException(ExceptionHelper.throwException(e, "销售明细", JacksonUtils.writeValueAsString(query)));
        }
    }


    @ApiOperation(value = "销售明细导出")
    @PostMapping("/sale/detail/export")
    public String exportSaleDetail(@RequestBody @Valid SalesDetailQO query) {
        log.info("[销售明细导出]query={}", JacksonUtils.writeValueAsString(query));
        query.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Assert.notBlank(query.getEnterpriseGuid(), ErrorConstant.CURRENT_THREAD_NOT_ENTERPRISE_GUID);
        try {
            return tradeItemService.exportSaleDetail(query);
        } catch (Exception e) {
            log.error("[销售明细导出]异常,e=", e);
            throw new BusinessException(ExceptionHelper.throwException(e, "销售明细导出", JacksonUtils.writeValueAsString(query)));
        }
    }

}
