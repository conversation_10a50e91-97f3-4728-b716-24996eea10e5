package com.holderzone.holder.saas.store.pay.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.pay.config.AggPayConfig;
import com.holderzone.holder.saas.store.pay.config.DeveloperConfig;
import com.holderzone.holder.saas.store.pay.constant.PayConstant;
import com.holderzone.holder.saas.store.pay.entity.AggPayAttachDataVO;
import com.holderzone.holder.saas.store.pay.entity.AggPayReserveVO;
import com.holderzone.holder.saas.store.pay.entity.HandlerPayBO;
import com.holderzone.holder.saas.store.pay.entity.domain.PayRecordDO;
import com.holderzone.holder.saas.store.pay.mapstruct.AggPayMapstruct;
import com.holderzone.holder.saas.store.pay.service.*;
import com.holderzone.holder.saas.store.pay.service.rpc.AggPayRpcService;
import com.holderzone.holder.saas.store.pay.service.rpc.SaasResultRpcService;
import com.holderzone.holder.saas.store.pay.utils.PageAdapter;
import com.holderzone.holder.saas.store.pay.utils.RespFriendlyUtils;
import com.holderzone.holder.saas.store.pay.utils.TradingUtils;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.pay.constant.AggPayStateEnum;
import com.holderzone.saas.store.dto.pay.constant.AggRefundStateEnum;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayClientServiceImpl
 * @date 2019/03/14 11:26
 * @description
 * @program holder-saas-store-trading-center
 */
@Slf4j
@Service
public class AggPayServiceImpl implements AggPayService {

    private final DeveloperConfig developerConfig;

    private final ErpConfigService erpConfigService;

    private final RedisService redisService;

    private final AggPayRpcService aggPayRpcService;

    private final PollingService pollingService;

    private final AggPayMapstruct aggPayMapStruct;

    private final RespFriendlyUtils respFriendlyUtils;

    private final PayRecordService payRecordService;

    private final SaasResultRpcService saasResultRpcService;

    private final AggPayConfig aggPayConfig;


    @Autowired
    public AggPayServiceImpl(DeveloperConfig developerConfig, ErpConfigService erpConfigService,
                             RedisService redisService, AggPayRpcService aggPayRpcService,
                             PollingService pollingService,
                             RespFriendlyUtils respFriendlyUtils, AggPayMapstruct aggPayMapstruct,
                             PayRecordService payRecordService, SaasResultRpcService saasResultRpcService, AggPayConfig aggPayConfig) {
        this.developerConfig = developerConfig;
        this.erpConfigService = erpConfigService;
        this.aggPayRpcService = aggPayRpcService;
        this.redisService = redisService;
        this.pollingService = pollingService;
        this.respFriendlyUtils = respFriendlyUtils;
        this.aggPayMapStruct = aggPayMapstruct;
        this.payRecordService = payRecordService;
        this.saasResultRpcService = saasResultRpcService;
        this.aggPayConfig = aggPayConfig;
    }

    @Override
    public Mono<AggPayRespDTO> prePay(SaasAggPayDTO saasAggPayDTO) {
        return Mono.just(saasAggPayDTO)
                // 查询 appId, appSecret
                .flatMap(req -> erpConfigService.getShuntPaymentInfoAsync(req.getEnterpriseGuid(), req.getStoreGuid(),
                        req.getReqDTO().getOrderGUID()))
                // 预下单、后台轮询
                .flatMap(paymentInfoDTO -> executePrePayThenPolling(saasAggPayDTO, paymentInfoDTO))
                // 为空处理
                .defaultIfEmpty(AggPayRespDTO.errorPaymentResp("10005", "聚合支付下单返回空"))
                // 错误处理
                .onErrorResume(respFriendlyUtils::convertPay2Friendly)
                // 中文友好化处理
                .doOnNext(respFriendlyUtils::convert2Chinese);
    }

    @Override
    public Mono<AggPayPollingRespDTO> prePayPolling(SaasPollingDTO saasPollingDTO) {
        return Flux.concat(redisService.getPollingResp(saasPollingDTO), queryPrePayResult(saasPollingDTO)).next();
    }

    @Override
    public Mono<AggPayPollingRespDTO> prePayH5Polling(SaasPollingDTO saasPollingDTO) {
        return queryPrePayResult(saasPollingDTO);
    }

    @Override
    public Mono<AggPayPollingRespDTO> queryPrePayResult(SaasPollingDTO saasPollingDTO) {
        return Mono.just(saasPollingDTO)
                // 查询 appId, appSecret
                .flatMap(req -> erpConfigService.getShuntPaymentInfoAsync(req.getEnterpriseGuid(), req.getStoreGuid(),
                        req.getOrderGuid()))
                // 前台主动轮询最新支付结果
                .flatMap(paymentInfoDTO -> aggPayRpcService.prePayQueryBankAsync(buildPrePayQueryReq(saasPollingDTO, paymentInfoDTO)))
                // 错误处理
                .onErrorResume(e -> respFriendlyUtils.convertPayPolling2Friendly(e, "聚合支付查询异常"))
                // 中文友好化处理
                .doOnNext(respFriendlyUtils::convert2Chinese)
                //存redis,以供轮询插用
                .doOnNext(a -> redisService.putPollingResp(saasPollingDTO.getOrderGuid(), saasPollingDTO.getPayGuid(), a));
    }

    @Override
    public Mono<AggPayPollingRespDTO> queryPayState(SaasPollingDTO saasPollingDTO) {
        return Mono.just(saasPollingDTO)
                // 查询 appId, appSecret
                .flatMap(req -> erpConfigService.getShuntPaymentInfoAsync(req.getEnterpriseGuid(), req.getStoreGuid(),
                        req.getOrderGuid()))
                // 前台主动轮询最新支付结果
                .flatMap(paymentInfoDTO -> aggPayRpcService.payPayQueryBankAsync(buildPrePayQueryReq(saasPollingDTO, paymentInfoDTO)))
                // 错误处理
                .onErrorResume(e -> respFriendlyUtils.convertPayPolling2Friendly(e, "聚合支付查询状态异常"))
                // 中文友好化处理
                .doOnNext(respFriendlyUtils::convert2Chinese)
                //存redis,以供轮询插用
                .doOnNext(a -> redisService.putPollingResp(saasPollingDTO.getOrderGuid(), saasPollingDTO.getPayGuid(), a));
    }

    @Override
    public Mono<AggRefundRespDTO> refund(SaasAggRefundDTO saasAggRefundDTO) {
        return Mono.just(saasAggRefundDTO)
                // 查询 appId, appSecret
                .flatMap(req -> erpConfigService.getShuntPaymentInfoAsync(req.getEnterpriseGuid(), req.getStoreGuid(),
                        req.getAggRefundReqDTO().getOrderGUID()))
                // 执行退款
                .flatMap(paymentInfoDTO -> executeRefundThenPolling(saasAggRefundDTO, paymentInfoDTO))
                // 错误处理
                .onErrorResume(respFriendlyUtils::convertRefund2Friendly)
                // 中文友好化处理
                .doOnNext(respFriendlyUtils::convert2Chinese);
    }

    @Override
    public Mono<AggRefundPollingRespDTO> refundPolling(SaasPollingDTO saasPollingDTO) {
        return Flux.concat(redisService.getRefundPollingResp(saasPollingDTO), queryRefundResult(saasPollingDTO)).next();
    }

    @Override
    public Mono<AggRefundPollingRespDTO> queryRefundResult(SaasPollingDTO saasPollingDTO) {
        return Mono.just(saasPollingDTO)
                // 查询 appId, appSecret
                .flatMap(req -> erpConfigService.getShuntPaymentInfoAsync(req.getEnterpriseGuid(), req.getStoreGuid(),
                        req.getOrderGuid()))
                // 前台主动轮询最新退款结果
                .flatMap(paymentInfoDTO -> aggPayRpcService.doRefundPollingAsync(buildRefundQueryReq(saasPollingDTO, paymentInfoDTO)));
    }

    @Override
    public Mono<String> weChatPublic(SaasAggWeChatPublicAccountPayDTO saasAggWxPubAccPayDTO) {
        return Mono.just(saasAggWxPubAccPayDTO)
                // 查询 appId, appSecret
                .flatMap(req -> erpConfigService.getShuntPaymentInfoAsync(req.getEnterpriseGuid(), req.getStoreGuid(),
                        req.getPublicAccountPayDTO().getOrderGUID()))
                // 预下单、后台轮询
                .flatMap(paymentInfoDTO -> executeWxPubAccPrePayThenPolling(saasAggWxPubAccPayDTO, paymentInfoDTO));
    }

    @Override
    public Mono<AggPayPollingRespDTO> weChatPublicPolling(SaasPollingDTO saasPollingDTO) {
        return Flux.concat(redisService.getPollingResp(saasPollingDTO), queryWxPubAccResult(saasPollingDTO)).next();
    }

    @Override
    public Mono<String> callback(AggPayCallbackDTO aggPayCallbackDTO) {
        //先判断redis  间接防止线程安全问题  和 减少订单的回调请求量
        return Flux.concat(pollingService.compareStatWithCache(aggPayCallbackDTO.getOrderGUID(), aggPayCallbackDTO.getPayGUID(), aggPayCallbackDTO.getPaySt()),
                doCallBack(aggPayCallbackDTO)).next();
    }

    @Override
    public Mono<AggPayReserveResultDTO> reservePay(SaasPollingDTO saasPollingDTO) {
        return Mono.just(saasPollingDTO)
                // 查询 appId, appSecret
                .flatMap(req -> erpConfigService.getShuntPaymentInfoAsync(req.getEnterpriseGuid(), req.getStoreGuid(),
                        req.getOrderGuid()))
                // add data for rpc
                .map(paymentInfoDTO -> makeReserveRequestData(paymentInfoDTO, saasPollingDTO))
                // rpc
                .flatMap(aggPayRpcService::doAggPayReserve)
                // stop polling after the the rpc success;
                .doOnNext(aggPayReserveResultDTO -> dealTheReserveBackDTO(aggPayReserveResultDTO, saasPollingDTO));
    }

    private AggPayReserveVO makeReserveRequestData(PaymentInfoDTO paymentInfoDTO, SaasPollingDTO saasPollingDTO) {
        AggPayReserveVO aggPayReserveVO = new AggPayReserveVO();
        aggPayReserveVO.setAppId(paymentInfoDTO.getAppId());
        aggPayReserveVO.setAttachData(null);
        aggPayReserveVO.setDeveloperId(developerConfig.getId());
        aggPayReserveVO.setOrderGUID(saasPollingDTO.getOrderGuid());
        aggPayReserveVO.setPayGUID(saasPollingDTO.getPayGuid());
        aggPayReserveVO.setTimestamp(DateTimeUtils.nowMillis());
        aggPayReserveVO.setSignature(TradingUtils.getSignature(
                aggPayReserveVO, developerConfig.getKey(), paymentInfoDTO.getAppSecret()));
        return aggPayReserveVO;
    }

    /**
     * deal the result
     */
    private void dealTheReserveBackDTO(AggPayReserveResultDTO aggPayReserveResultDTO, SaasPollingDTO saasPollingDTO) {
        boolean success = aggPayReserveResultDTO.getCode().equals(PayConstant.SUCCESS_CODE);
        if (success) {
            redisService.putCallBackResp(saasPollingDTO.getOrderGuid(), saasPollingDTO.getPayGuid(), AggPayStateEnum.CLOSED.getId());
        }
    }

    @Override
    public Mono<AggPayPollingRespDTO> queryWxPubAccResult(SaasPollingDTO saasPollingDTO) {
        return Mono.just(saasPollingDTO)
                // 查询 appId, appSecret
                .flatMap(req -> aggPayRpcService.doWeChatPublicAccountPollingAsync(req.getOrderGuid()))
                // 错误处理
                .onErrorResume(e -> respFriendlyUtils.convertPayPolling2Friendly(e, "微信公众号查询异常"))
                // 中文友好化处理
                .doOnNext(respFriendlyUtils::convert2Chinese);
    }

    /**
     * 回调内部服务
     *
     * @param aggPayCallbackDTO
     * @return
     */
    private Mono<String> doCallBack(AggPayCallbackDTO aggPayCallbackDTO) {
        return Mono.just(aggPayCallbackDTO)
                //转换callback DTO
                .flatMap(callbackDTO -> Mono.just(aggPayMapStruct.makecallBackDto(callbackDTO)))
                // 处理 返回true
                .doOnNext(pollingRespDTO -> saasResultRpcService.handlePayResult(buildHandlerPayDo(aggPayCallbackDTO), pollingRespDTO))
                // 存redis，更新支付结果
                .doOnNext(aggPayPollingRespDTO -> redisService.putPollingResp(aggPayCallbackDTO.getOrderGUID(), aggPayCallbackDTO.getPayGUID(), aggPayPollingRespDTO))
                //返回result
                .flatMap(pollingRespDTO -> Mono.just("SUCCESS"))
                //fail
                .onErrorReturn(e -> e instanceof Exception, "FAIL");

    }


    @Override
    public Mono<Page<AggPayRecordDTO>> queryPayRecord(BasePageDTO basePageDTO) {
        return Mono.fromCallable(() -> {
            EnterpriseIdentifier.setEnterpriseGuid(basePageDTO.getEnterpriseGuid());
            IPage<PayRecordDO> page = payRecordService.page(new PageAdapter<>(basePageDTO),
                    new LambdaQueryWrapper<PayRecordDO>()
                            .eq(PayRecordDO::getStoreGuid, basePageDTO.getStoreGuid())
                            .ge(PayRecordDO::getGmtTimePaid, LocalDateTime.now().minusDays(1))
                            .orderByDesc(PayRecordDO::getGmtTimePaid));
            return new PageAdapter<>(page, aggPayMapStruct.createPayRecordResp(page.getRecords()));
        });
    }

    @Override
    public Mono<AggPayStatisticsDTO> queryPayStatistics(BasePageDTO basePageDTO) {
        return Mono.fromCallable(() -> {
            EnterpriseIdentifier.setEnterpriseGuid(basePageDTO.getEnterpriseGuid());
            IPage<PayRecordDO> page = payRecordService.page(new PageAdapter<>(basePageDTO),
                    new LambdaQueryWrapper<PayRecordDO>()
                            .eq(PayRecordDO::getStoreGuid, basePageDTO.getStoreGuid())
                            .ge(PayRecordDO::getGmtTimePaid, LocalDateTime.now().minusDays(1))
                            .orderByDesc(PayRecordDO::getGmtTimePaid));
            Page<AggPayRecordDTO> recordPage = new PageAdapter<>(page, aggPayMapStruct.createPayRecordResp(page.getRecords()));
            BigDecimal totalAmount = null;
            if (Objects.nonNull(basePageDTO.getCurrentPage()) && basePageDTO.getCurrentPage() == 1) {
                totalAmount = payRecordService.getStatisticsTotalAmount(basePageDTO);
            }
            return new AggPayStatisticsDTO(recordPage, totalAmount);
        });
    }

    private HandlerPayBO buildHandlerPayDo(AggPayCallbackDTO aggPayCallbackDTO) {
        log.info("aggPayCallbackDTO:{}", aggPayCallbackDTO);
        HandlerPayBO handlerPayBO = new HandlerPayBO();
        String attachData = aggPayCallbackDTO.getAttachData();
        if (attachData == null || !attachData.startsWith("{")) {
            log.error("attchData is not json: {}", attachData);
            return null;
        }
        //检查时候有签名
        if (!StringUtils.hasText(aggPayCallbackDTO.getSignature())) {
            log.warn("轮询聚合支付结果业务报警：无签名");
            return null;
        }
        AggPayAttachDataVO aggPayAttachDataVO = JacksonUtils.toObject(AggPayAttachDataVO.class, attachData);
        PaymentInfoDTO paymentInfo = erpConfigService.getPaymentInfo(aggPayAttachDataVO.getEnterpriseGuid(),
                aggPayAttachDataVO.getStoreGuid(), aggPayCallbackDTO.getOrderGUID());
        if (paymentInfo == null) {
            log.error("获取支付配置失败");
            return null;
        }
        if (aggPayAttachDataVO.getDeviceType() != null
                && aggPayAttachDataVO.getDeviceType().equals(BaseDeviceTypeEnum.TCD.getCode())) {
            //TODO 通吃岛暂时不验签，因为一直验签失败 20200429
            log.info("通吃岛暂时不验签");
        } else {
            String signatureForCheck = TradingUtils.getSignatureForCheck(aggPayCallbackDTO, developerConfig.getKey(), paymentInfo.getAppSecret());
            // 验证签名
            if (!Objects.equals(aggPayCallbackDTO.getSignature(), signatureForCheck)) {
                log.error("回调聚合支付结果验签失败：{}", JacksonUtils.writeValueAsString(aggPayCallbackDTO));
                return null;
            }
        }
        EnterpriseIdentifier.setEnterpriseGuid(aggPayAttachDataVO.getEnterpriseGuid());
        handlerPayBO.setInnerCallBackUrl(aggPayAttachDataVO.getSaasCallBackUrl());
        handlerPayBO.setIsQuickReceipt(aggPayAttachDataVO.getIsQuickReceipt() == null ? false : aggPayAttachDataVO.getIsQuickReceipt());
        BaseInfo baseInfo = aggPayMapStruct.createBaseInfo(aggPayAttachDataVO);
        handlerPayBO.setBaseInfo(baseInfo);
        log.info("buildHandlerPayDo:{}", handlerPayBO);
        return handlerPayBO;
    }

    private AggPayPreTradingReqDTO buildPrePayRequest(SaasAggPayDTO saasAggPayDTO, PaymentInfoDTO paymentInfoDTO) {
        AggPayPreTradingReqDTO payPreTradingReqDTO = saasAggPayDTO.getReqDTO();
        payPreTradingReqDTO.setAppId(paymentInfoDTO.getAppId());
        payPreTradingReqDTO.setDeveloperId(developerConfig.getId());
        //店员号
        payPreTradingReqDTO.setMerchantUserId(paymentInfoDTO.getEmpId());
        payPreTradingReqDTO.setTimestamp(DateTimeUtils.nowMillis());
        payPreTradingReqDTO.setAmount(payPreTradingReqDTO.getAmount()
                // 聚合支付是以分为单位
                .multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP)
        );
        payPreTradingReqDTO.setOutNotifyUrl(aggPayConfig.getCallBack());
        AggPayAttachDataVO aggPayAttachDataVO = aggPayMapStruct.makeAttachVo(saasAggPayDTO);
        aggPayAttachDataVO.setSaasCallBackUrl(saasAggPayDTO.getSaasCallBackUrl());
        aggPayAttachDataVO.setIsQuickReceipt(saasAggPayDTO.getIsQuickReceipt());
        aggPayAttachDataVO.setIsLast(saasAggPayDTO.getIsLast());
        aggPayAttachDataVO.setCheckoutSuccessFlag(saasAggPayDTO.getCheckoutSuccessFlag());
        aggPayAttachDataVO.setPlatformSource("掌控者智慧系统");
        payPreTradingReqDTO.setAttachData(JacksonUtils.writeValueAsString(aggPayAttachDataVO));
        payPreTradingReqDTO.setSignature(TradingUtils.getSignature(
                payPreTradingReqDTO, developerConfig.getKey(), paymentInfoDTO.getAppSecret()
        ));
        return payPreTradingReqDTO;
    }

    private Mono<AggPayRespDTO> executePrePayThenPolling(SaasAggPayDTO saasAggPayDTO, PaymentInfoDTO paymentInfoDTO) {
        AggPayPreTradingReqDTO payPreTradingReqDTO = buildPrePayRequest(saasAggPayDTO, paymentInfoDTO);
        return aggPayRpcService.preTradingAsync(payPreTradingReqDTO)
                .doOnNext(aggPayRespDTO -> {
                    // 预下单成功则开启后台轮询最新支付结果
                    if (PayConstant.SUCCESS_CODE.equals(aggPayRespDTO.getCode())) {
                        // 填充payGuid和orderGuid到结果中
                        aggPayRespDTO.setPayGuid(payPreTradingReqDTO.getPayGUID());
                        aggPayRespDTO.setOrderGuid(payPreTradingReqDTO.getOrderGUID());
                        aggPayRespDTO.setPaymentAppId(paymentInfoDTO.getAppId());
                        // 将当前支付结果写入Redis，供调用方轮询
                        cachePrePayRespForPolling(aggPayRespDTO);
                        // 后台轮询最新支付结果（使用MQ或定时任务）
                        postMsgToStartInnerPolling(saasAggPayDTO, paymentInfoDTO, payPreTradingReqDTO);
                    }
                });
    }

    private void postMsgToStartInnerPolling(SaasAggPayDTO saasAggPayDTO, PaymentInfoDTO paymentInfoDTO,
                                            AggPayPreTradingReqDTO payPreTradingReqDTO) {
        // 后台轮询最新支付结果（先消息队列后定时任务）
        AggPayPollingDTO pollingJHPayDTO = aggPayMapStruct.createPollingDto(payPreTradingReqDTO);
        pollingJHPayDTO.setSignature(TradingUtils.getSignature(
                pollingJHPayDTO, developerConfig.getKey(), paymentInfoDTO.getAppSecret()
        ));
        HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .baseInfo(aggPayMapStruct.createBaseInfo(saasAggPayDTO))
                .paymentInfoDTO(paymentInfoDTO)
                .enterpriseGuid(saasAggPayDTO.getEnterpriseGuid())
                .isQuickReceipt(saasAggPayDTO.getIsQuickReceipt())
                .innerCallBackUrl(saasAggPayDTO.getSaasCallBackUrl())
                .handlerType(PayPowerId.YL_WX_PUBLIC_NO.getId().equals(payPreTradingReqDTO.getPayPowerId()) ? 1 : 0)
                .payPowerId(payPreTradingReqDTO.getPayPowerId())
                .times(0).build();
        pollingService.startPrePayPolling(pollingJHPayDTO, handlerPayBO);
    }

    private void cachePrePayRespForPolling(AggPayRespDTO aggPayRespDTO) {
        AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode(aggPayRespDTO.getCode());
        pollingRespDTO.setMsg(aggPayRespDTO.getMsg());
        pollingRespDTO.setPaySt(AggPayStateEnum.READY.getId());
        redisService.putPollingResp(aggPayRespDTO.getOrderGuid(), aggPayRespDTO.getPayGuid(), pollingRespDTO);
    }

    private AggPayPollingDTO buildPrePayQueryReq(SaasPollingDTO saasPollingDTO, PaymentInfoDTO paymentInfoDTO) {
        AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO();
        pollingJHPayDTO.setAppId(paymentInfoDTO.getAppId());
        pollingJHPayDTO.setDeveloperId(developerConfig.getId());
        pollingJHPayDTO.setTimestamp(DateTimeUtils.nowMillis());
        pollingJHPayDTO.setPayGUID(saasPollingDTO.getPayGuid());
        pollingJHPayDTO.setOrderGUID(saasPollingDTO.getOrderGuid());
        pollingJHPayDTO.setSignature(TradingUtils.getSignature(
                pollingJHPayDTO, developerConfig.getKey(), paymentInfoDTO.getAppSecret()
        ));
        return pollingJHPayDTO;
    }

    private Mono<AggRefundRespDTO> executeRefundThenPolling(SaasAggRefundDTO saasAggRefundDTO,
                                                            PaymentInfoDTO paymentInfoDTO) {
        AggRefundReqDTO aggRefundReqDTO = buildAggRefundRequest(saasAggRefundDTO, paymentInfoDTO);
        // 退款
        return aggPayRpcService.refundAsync(aggRefundReqDTO)
                .doOnNext(aggRefundRespDTO -> {
                    // 退款处理中则开启后台轮询最新退款结果
                    if (PayConstant.SUCCESS_CODE.equals(aggRefundRespDTO.getCode())) {
                        if (AggRefundStateEnum.REFUND_SUCCESS.getState().equals(aggRefundRespDTO.getState())
                                && saasAggRefundDTO.getIsQuickReceipt()) {
                            // 将当前退款结果写入Database
                            EnterpriseIdentifier.setEnterpriseGuid(aggRefundReqDTO.getAttachData());
                            PayRecordDO payRecordDO = new PayRecordDO();
                            payRecordDO.setPaySt("4");
                            payRecordDO.setRefOrderNo(aggRefundRespDTO.getRefOrderNo());
                            payRecordDO.setGmtRefund(DateTimeUtils.now());
                            payRecordService.update(payRecordDO, new LambdaQueryWrapper<PayRecordDO>()
                                    .eq(PayRecordDO::getOrderHolderNo, aggRefundRespDTO.getMchntOrderNo()));
                        }
                        if (AggRefundStateEnum.REFUND_PROCESSED.getState().equals(aggRefundRespDTO.getState())) {
                            // 将当前退款结果写入Redis，供调用方轮询
                            cacheRefundRespForPolling(aggRefundReqDTO);
                            // 后台轮询最新退款结果（使用定时任务）
                            postMsgToStartRefundInnerPolling(saasAggRefundDTO, paymentInfoDTO, aggRefundReqDTO);
                        }
                    }
                });
    }

    private void cacheRefundRespForPolling(AggRefundReqDTO aggRefundReqDTO) {
        AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode(PayConstant.SUCCESS_CODE);
        aggRefundPollingRespDTO.setMsg("退款中");
        redisService.putRefundPollingResp(
                aggRefundReqDTO.getOrderGUID(), aggRefundReqDTO.getPayGUID(), aggRefundPollingRespDTO
        );
    }

    private void postMsgToStartRefundInnerPolling(SaasAggRefundDTO saasAggRefundDTO, PaymentInfoDTO paymentInfoDTO, AggRefundReqDTO aggRefundReqDTO) {
        AggRefundPollingDTO aggRefundPollingDTO = aggPayMapStruct.createRefundPollingDto(aggRefundReqDTO);
        aggRefundPollingDTO.setSignature(TradingUtils.getSignature(
                aggRefundPollingDTO, developerConfig.getKey(), paymentInfoDTO.getAppSecret()
        ));
        pollingService.startRefundPolling(saasAggRefundDTO, aggRefundPollingDTO);
    }

    private AggRefundReqDTO buildAggRefundRequest(SaasAggRefundDTO saasAggRefundDTO, PaymentInfoDTO paymentInfoDTO) {
        AggRefundReqDTO aggRefundReqDTO = saasAggRefundDTO.getAggRefundReqDTO();
        aggRefundReqDTO.setDeveloperId(developerConfig.getId());
        aggRefundReqDTO.setAppId(paymentInfoDTO.getAppId());
        aggRefundReqDTO.setTimestamp(DateTimeUtils.nowMillis());
        aggRefundReqDTO.setRefundFee(aggRefundReqDTO.getRefundFee()
                // 聚合支付是以分为单位
                .multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP)
        );
        aggRefundReqDTO.setSignature(TradingUtils.getSignature(
                aggRefundReqDTO, developerConfig.getKey(), paymentInfoDTO.getAppSecret()
        ));
        return aggRefundReqDTO;
    }

    private AggRefundPollingDTO buildRefundQueryReq(SaasPollingDTO saasPollingDTO, PaymentInfoDTO paymentInfoDTO) {
        AggRefundPollingDTO aggRefundPollingDTO = new AggRefundPollingDTO();
        aggRefundPollingDTO.setPayGUID(saasPollingDTO.getPayGuid());
        aggRefundPollingDTO.setOrderGUID(saasPollingDTO.getOrderGuid());
        aggRefundPollingDTO.setAppId(paymentInfoDTO.getAppId());
        aggRefundPollingDTO.setDeveloperId(developerConfig.getId());
        aggRefundPollingDTO.setTimestamp(DateTimeUtils.nowMillis());
        aggRefundPollingDTO.setSignature(TradingUtils.getSignature(
                aggRefundPollingDTO, developerConfig.getKey(), paymentInfoDTO.getAppSecret()
        ));
        return aggRefundPollingDTO;
    }

    private AggWeChatPublicAccountPayDTO buildWxPubAccPrePayRequest(SaasAggWeChatPublicAccountPayDTO publicAccountPayDTO,
                                                                    PaymentInfoDTO paymentInfoDTO) {
        AggWeChatPublicAccountPayDTO accountPayDTO = publicAccountPayDTO.getPublicAccountPayDTO();
        accountPayDTO.setAppId(paymentInfoDTO.getAppId());
        accountPayDTO.setAppSecret(paymentInfoDTO.getAppSecret());
        accountPayDTO.setOutNotifyUrl(aggPayConfig.getCallBack());
        accountPayDTO.setAmount(accountPayDTO.getAmount()
                // 聚合支付是以分为单位
                .multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP)
        );
        AggPayAttachDataVO aggPayAttachDataVO = aggPayMapStruct.makeAttachVo(publicAccountPayDTO);
        aggPayAttachDataVO.setSaasCallBackUrl(publicAccountPayDTO.getSaasCallBackUrl());
        aggPayAttachDataVO.setIsQuickReceipt(false);
        aggPayAttachDataVO.setIsLast(true);
        accountPayDTO.setAttachData(JacksonUtils.writeValueAsString(aggPayAttachDataVO));
        accountPayDTO.setMchntName(accountPayDTO.getEnterpriseName());
        return accountPayDTO;
    }

    private Mono<String> executeWxPubAccPrePayThenPolling(SaasAggWeChatPublicAccountPayDTO publicAccountPayDTO,
                                                          PaymentInfoDTO paymentInfoDTO) {
        AggWeChatPublicAccountPayDTO accountPayDTO = buildWxPubAccPrePayRequest(publicAccountPayDTO, paymentInfoDTO);
        // 微信公众号支付，返回重定向url
        return aggPayRpcService.weChatPublicAccountPayAsync(accountPayDTO)
                // 开启后台任务轮询最新重定向url，并写入redis，供调用方轮询
                .doOnNext(redirectUrl -> {
                    if (publicAccountPayDTO.getDeviceType() != null
                            && publicAccountPayDTO.getDeviceType().equals(BaseDeviceTypeEnum.TCD.getCode())) {
                        log.info("通吃岛赚餐通过微信下单不走轮询");
                    } else if (StringUtils.hasText(redirectUrl)) {
                        postMsgToStartWxPubAccInnerPolling(accountPayDTO, publicAccountPayDTO, paymentInfoDTO);
                        cacheWxPubAccPrePayRespForPolling(accountPayDTO);
                    } else {
                        log.error("微信公众号支付redirectUrl为空");
                    }
                });
    }

    private void postMsgToStartWxPubAccInnerPolling(AggWeChatPublicAccountPayDTO accountPayDTO,
                                                    SaasAggWeChatPublicAccountPayDTO publicAccountPayDTO,
                                                    PaymentInfoDTO paymentInfoDTO) {
        // 微信公众号轮询不需要签名
        AggPayPollingDTO pollingJHPayDTO = new AggPayPollingDTO().setOrderGUID(accountPayDTO.getOrderGUID());
        // 预下单成功，开始H5轮询
        HandlerPayBO handlerPayBO = HandlerPayBO.builder()
                .baseInfo(aggPayMapStruct.createBaseInfo(publicAccountPayDTO))
                .paymentInfoDTO(paymentInfoDTO)
                .enterpriseGuid(publicAccountPayDTO.getEnterpriseGuid())
                .innerCallBackUrl(publicAccountPayDTO.getSaasCallBackUrl())
                .handlerType(1).times(0).build();
        // 不设置 payPowerId
        //handlerPayBO.getBaseInfo()
        //handlerPayBO.getBaseInfo().setDeviceType(accountPayDTO.getde);
        pollingService.startPrePayPolling(pollingJHPayDTO, handlerPayBO);
    }

    private void cacheWxPubAccPrePayRespForPolling(AggWeChatPublicAccountPayDTO accountPayDTO) {
        AggPayPollingRespDTO pollingRespDTO = new AggPayPollingRespDTO();
        pollingRespDTO.setCode(PayConstant.SUCCESS_CODE);
        pollingRespDTO.setMsg(PayConstant.SUCCESS_DESC);
        pollingRespDTO.setPaySt(AggPayStateEnum.READY.getId());
        redisService.putPollingResp(accountPayDTO.getOrderGUID(), accountPayDTO.getPayGUID(), pollingRespDTO);
    }
}
