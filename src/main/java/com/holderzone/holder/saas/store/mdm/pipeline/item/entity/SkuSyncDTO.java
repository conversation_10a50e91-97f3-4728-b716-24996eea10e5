package com.holderzone.holder.saas.store.mdm.pipeline.item.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SkuSyncDTO
 * @date 2019/11/19 下午6:00
 * @description //MDM sku 同步dto
 * @program holder
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("mdm商品sku同步DTO")
@JsonPropertyOrder(alphabetic = true)
public class SkuSyncDTO {


    /**
     * 主键
     */
    @JsonProperty("guid")
    @ApiModelProperty(value = "MDM生成的唯一标识", required = false)
    private String uuid;

    /**
     * 第三方唯一标识
     */
    @JsonProperty("thirdNo")
    @NotBlank(message = "第三方标识不得为空", groups = {ItemSyncDTO.Create.class, ItemSyncDTO.Update.class, ItemSyncDTO.Delete.class})
    @ApiModelProperty(value = "第三方唯一标识", required = true)
    private String guid;

    /**
     * 品牌GUID
     */
    @Nullable
    @ApiModelProperty(value = "品牌GUID", required = false)
    private String brandGuid;

    /**
     * 关联的门店GUID
     */
    @Nullable
    @ApiModelProperty(value = "门店GUID", required = false)
    private String storeGuid;

    /**
     * 商品GUID
     */
    @NotBlank(message = "商品GUID", groups = {Create.class, Update.class})
    @ApiModelProperty(value = "关联的商品GUID门店GUID", required = true)
    private String itemGuid;

    /**
     * upc商品条码
     */
    @ApiModelProperty(value = "upc商品条码", required = false)
    private String upc;

    /**
     * 规格名称(单品规格名称为空字符串"")
     */
    @ApiModelProperty(value = "规格名称", required = true)
    private String name;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号", required = false)
    private String code;

    /**
     * 售卖价格（参考价）
     */
    @NotNull(message = "售卖价格（参考价）", groups = {Create.class, Update.class})
    @ApiModelProperty(value = "售卖价格（参考价）", required = true)
    private BigDecimal salePrice;

    /**
     * 商品规格单位
     */
    @NotBlank(message = "商品规格单位", groups = {Create.class, Update.class})
    @ApiModelProperty(value = "商品规格单位", required = true)
    private String unit;


    /**
     * 规格来源（0：门店，1：品牌）
     */
    @NotNull(message = "规格来源（0：门店，1：品牌）", groups = {Create.class, Update.class})
    @ApiModelProperty(value = "规格来源（0：门店，1：品牌）", required = true)
    private Integer skuFrom;

    /**
     *  是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Integer isEnable;

    /**
     * 品牌库对应的SKUGUID：如果是自己创建的内容，则此字段为skuGuid，如果是被推送过来的商品，则该字段为原skuGUID。
     */
    @ApiModelProperty(value = "品牌库对应的SKUGUID")
    private String parentGuid;

    /**
     * 是否上架(0：否，1：是)
     */
    @ApiModelProperty(value = "是否上架(0：否，1：是)")
    private Integer isRack;


    public interface Create extends Default {

    }

    public interface Update extends Default {

    }

    public interface Delete extends Default {

    }


}
