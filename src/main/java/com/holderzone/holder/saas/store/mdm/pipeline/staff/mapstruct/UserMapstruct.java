package com.holderzone.holder.saas.store.mdm.pipeline.staff.mapstruct;

import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.UserSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.domain.UserDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UserMapstruct
 * @date 2019/11/18 17:09
 * @description
 * @program holder-saas-store
 */
@Component
@Mapper(componentModel = "spring")
public interface UserMapstruct {

    @Mappings({
            @Mapping(target = "enabled", expression = "java(userDO.getIsEnable() ? \"1\" : \"0\")")
    })
    UserSyncDTO do2DTO(UserDO userDO);

    @Mappings({
            @Mapping(target = "isEnable", expression = "java(java.util.Objects.equals(\"1\", userSynDTO.getEnabled()))")
    })
    UserDO DTO2DO(UserSyncDTO userSynDTO);
}
