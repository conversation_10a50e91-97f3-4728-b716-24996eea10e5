package com.holderzone.holder.saas.store.table.client;

import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.reserve.ReserveRecordGuidDTO;
import com.holderzone.saas.store.dto.reserve.ReserveRefundDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/9/9
 * @description 预定调用
 */
@Component
@FeignClient(name = "holder-saas-store-reserve", fallbackFactory = ReserveClientService.ReserveClientServiceFallBack.class)
public interface ReserveClientService {

    @PostMapping("/reserve/getItems")
    List<DineInItemDTO> getItems(@RequestBody ReserveRecordGuidDTO guidDTO);

    @PostMapping("/refund")
    String refund(@RequestBody SingleDataDTO singleDataDTO);

    /**
     * 部分退款
     */
    @PostMapping("/part_refund")
    BigDecimal partRefund(@RequestBody ReserveRefundDTO reserveRefundDTO);

    /**
     * 查询指定预订记录来源
     */
    @PostMapping("/reserve/obtainDeviceType")
    Integer obtainDeviceType(@RequestBody ReserveRecordGuidDTO guidDTO);

    @ApiOperation("预定拆台")
    @PostMapping("/reserve/separate")
    void separate(@RequestBody TableOrderCombineDTO tableOrderCombineDTO);

    @ApiOperation("通知转台")
    @PostMapping("/reserve/notifyTurn")
    void notifyTurn(@RequestBody TradeTableDTO tradeTableDTO);

    @ApiOperation("预定拆台")
    @PostMapping("/reserve/combine")
    void combine(TableOrderCombineDTO tableOrderCombineDTO);

    @Slf4j
    @Component
    class ReserveClientServiceFallBack implements FallbackFactory<ReserveClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ReserveClientService create(Throwable throwable) {

            return new ReserveClientService() {
                @Override
                public List<DineInItemDTO> getItems(ReserveRecordGuidDTO guidDTO) {
                    log.error("获取预订信息失败 guidDTO={}", JacksonUtils.writeValueAsString(guidDTO));
                    throw new ParameterException("获取预订信息失败!");
                }

                @Override
                public String refund(SingleDataDTO singleDataDTO) {
                    log.error("退预定金失败 guidDTO={}", JacksonUtils.writeValueAsString(singleDataDTO));
                    throw new ParameterException("退预定金失败!");
                }

                @Override
                public BigDecimal partRefund(ReserveRefundDTO reserveRefundDTO) {
                    log.error("部分退预定金失败 reserveRefundDTO={}", JacksonUtils.writeValueAsString(reserveRefundDTO));
                    throw new ParameterException("部分退预定金失败!");
                }

                @Override
                public Integer obtainDeviceType(ReserveRecordGuidDTO guidDTO) {
                    log.error("查询预定单来源失败 guidDTO={}", JacksonUtils.writeValueAsString(guidDTO));
                    throw new ParameterException("查询预定单来源失败!");
                }

                @Override
                public void separate(TableOrderCombineDTO tableOrderCombineDTO) {
                    log.error(HYSTRIX_PATTERN, "separate", JacksonUtils.writeValueAsString(tableOrderCombineDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void notifyTurn(TradeTableDTO tradeTableDTO) {
                    log.error(HYSTRIX_PATTERN, "notifyTurn", JacksonUtils.writeValueAsString(tradeTableDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void combine(TableOrderCombineDTO tableOrderCombineDTO) {
                    log.error(HYSTRIX_PATTERN, "combine", JacksonUtils.writeValueAsString(tableOrderCombineDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

            };
        }
    }

}
