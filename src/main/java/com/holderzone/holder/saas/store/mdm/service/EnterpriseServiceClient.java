package com.holderzone.holder.saas.store.mdm.service;

import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.Collections;
import java.util.List;

@Component
@FeignClient(name = "holder-saas-cloud-enterprise", fallbackFactory = EnterpriseServiceClient.FallBack.class)
public interface EnterpriseServiceClient {

    @GetMapping("/enterprise/all")
    List<EnterpriseDTO> getAllEnterprise();

    @Component
    class FallBack implements FallbackFactory<EnterpriseServiceClient> {
        private static final Logger logger = LoggerFactory.getLogger(EnterpriseServiceClient.FallBack.class);

        @Override
        public EnterpriseServiceClient create(Throwable throwable) {
            return new EnterpriseServiceClient() {
                @Override
                public List<EnterpriseDTO> getAllEnterprise() {
                    logger.error("获取企业信息失败,msg={}", throwable.getMessage());
                    return Collections.emptyList();
                }
            };
        }
    }

}