package com.holderzone.holder.saas.store.pay.entity;

import com.holderzone.saas.store.dto.trade.BaseInfo;
import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandlerPayBO
 * @date 2019/03/15 10:01
 * @description
 * @program holder-saas-store-trading-center
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HandlerPayBO {

    /**
     * 基础信息
     */
    private BaseInfo baseInfo;

    /**
     * 支付配置信息
     */
    private PaymentInfoDTO paymentInfoDTO;

    /**
     * 当前轮询次数
     */
    private Integer times;

    /**
     * 企业Guid
     */
    private String enterpriseGuid;

    /**
     * 是否是快速收款
     */
    private Boolean isQuickReceipt;

    /**
     * 内部服务回调url
     */
    private String innerCallBackUrl;

    /**
     * 0 --> 默认是扫码或者条码支付
     * 1 --> H5支付
     */
    private Integer handlerType;

    private Boolean isLast;


    @ApiModelProperty(value = "支付方式")
    private String payPowerId;
}
