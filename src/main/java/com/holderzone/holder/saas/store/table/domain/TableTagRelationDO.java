package com.holderzone.holder.saas.store.table.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/05 16:06
 */
@AllArgsConstructor
@NoArgsConstructor
@TableName("hst_table_tag_relation")
@Data
public class TableTagRelationDO {

    private String guid;

    private String tableGuid;

    private String tagGuid;

    private LocalDateTime gmtCreate;


}