package com.holderzone.holder.saas.store.pay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.pay.entity.domain.PayRecordDO;
import com.holderzone.holder.saas.store.pay.mapper.PayRecordMapper;
import com.holderzone.holder.saas.store.pay.service.PayRecordService;
import com.holderzone.holder.saas.store.pay.utils.PageAdapter;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.member.pay.MemberQuickPayDTO;
import com.holderzone.saas.store.dto.pay.AggPayRecordDTO;
import com.holderzone.saas.store.dto.pay.AggPayStatisticsDTO;
import com.holderzone.saas.store.dto.pay.QuickPayStatisticsReqDTO;
import com.holderzone.saas.store.dto.pay.QuickPayStatisticsRespDTO;
import com.holderzone.saas.store.dto.pay.constant.AggPayStateEnum;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * 支付记录
 */
@Slf4j
@Service
public class PayRecordServiceImpl extends ServiceImpl<PayRecordMapper, PayRecordDO> implements PayRecordService {

    @Autowired
    private PayRecordMapper payRecordMapper;

    @Override
    public BigDecimal getStatisticsTotalAmount(BasePageDTO basePageDTO) {
        return baseMapper.getStatisticsTotalAmount(basePageDTO, LocalDateTime.now().minusDays(1));
    }

    @Override
    public Mono<String> saveMemberQuickPayRecord(MemberQuickPayDTO memberQuickPayDTO) {
        return Mono.fromCallable(() -> {
            EnterpriseIdentifier.setEnterpriseGuid(memberQuickPayDTO.getEnterpriseGuid());
            UserContextUtils.putErp(memberQuickPayDTO.getEnterpriseGuid());
            PayRecordDO payRecordDO = buildMemberQuickPayRecord(memberQuickPayDTO);
            save(payRecordDO);
            return "SUCCESS";
        });
    }

    @Override
    public Mono<String> refundMemberQuickPayRecord(MemberQuickPayDTO memberQuickPayDTO) {
        return Mono.fromCallable(() -> {
            EnterpriseIdentifier.setEnterpriseGuid(memberQuickPayDTO.getEnterpriseGuid());
            UserContextUtils.putErp(memberQuickPayDTO.getEnterpriseGuid());
            LambdaUpdateWrapper<PayRecordDO> uw = new LambdaUpdateWrapper<PayRecordDO>()
                    .eq(PayRecordDO::getOrderGuid, memberQuickPayDTO.getOrderGuid())
                    .eq(PayRecordDO::getPayPowerId, PayPowerId.MEMBER_PAY.getId())
                    .set(PayRecordDO::getPaySt, AggPayStateEnum.REFUNDED.getId())
                    .set(PayRecordDO::getGmtRefund, memberQuickPayDTO.getRefundTime());
            update(uw);
            return "SUCCESS";
        });
    }

    @Override
    public Mono<List<QuickPayStatisticsRespDTO>> queryQuickPayStatistics(QuickPayStatisticsReqDTO request) {
        return Mono.fromCallable(() -> {
            EnterpriseIdentifier.setEnterpriseGuid(request.getEnterpriseGuid());
            UserContextUtils.putErp(request.getEnterpriseGuid());
            return payRecordMapper.queryQuickPayStatistics(request);
        });
    }


    /**
     * 构建会员快速收款记录
     */
    private PayRecordDO buildMemberQuickPayRecord(MemberQuickPayDTO memberQuickPayDTO) {
        PayRecordDO payRecordDO = new PayRecordDO();
        payRecordDO.setStoreGuid(memberQuickPayDTO.getStoreGuid());
        payRecordDO.setPayPowerId(PayPowerId.MEMBER_PAY.getId());
        payRecordDO.setPayPowerName(PayPowerId.MEMBER_PAY.getName());
        payRecordDO.setOrderGuid(memberQuickPayDTO.getOrderGuid());
        payRecordDO.setOrderNo(memberQuickPayDTO.getOrderNo());
        payRecordDO.setOrderHolderNo(memberQuickPayDTO.getOrderNo());
        payRecordDO.setAmount(memberQuickPayDTO.getAmount());
        payRecordDO.setPaySt(AggPayStateEnum.SUCCESS.getId());
        payRecordDO.setGmtTimePaid(memberQuickPayDTO.getGmtCreate());
        payRecordDO.setPhoneNum(memberQuickPayDTO.getPhoneNum());
        return payRecordDO;
    }
}
