package com.holderzone.holder.saas.store.table.config;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.table.domain.bo.DelayAutoUnlockBO;
import com.holderzone.holder.saas.store.table.domain.bo.TableInfoBO;
import com.holderzone.holder.saas.store.table.service.BizMsgService;
import com.holderzone.holder.saas.store.table.utils.DynamicHelper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/01/25 17:31
 */
@Slf4j
@Component
public class AutoDelayRelease {

    private static final ScheduledExecutorService SE =
            Executors.newScheduledThreadPool(Runtime.getRuntime().availableProcessors() + 1);

    private final DelayReleaseLockConfig delayReleaseLockConfig;

    private final DynamicHelper dynamicHelper;

    private final BizMsgService bizMsgService;

    @Autowired
    public AutoDelayRelease(DelayReleaseLockConfig delayReleaseLockConfig, DynamicHelper dynamicHelper,
                            BizMsgService bizMsgService) {
        this.delayReleaseLockConfig = delayReleaseLockConfig;
        this.dynamicHelper = dynamicHelper;
        this.bizMsgService = bizMsgService;
    }

    public void doRelease(DelayAutoUnlockBO delayautounlockbo) {
        Map<String, Future> map = new HashMap<>();
        AtomicInteger atomicInteger = new AtomicInteger(0);
        ScheduledFuture<?> scheduledFuture = SE.scheduleAtFixedRate(() -> {
            if (atomicInteger.incrementAndGet() == 2) {
                map.get("key").cancel(true);
                map.clear();
                return;
            }
            String enterpriseGuid = delayautounlockbo.getEnterpriseGuid();
            dynamicHelper.changeDatasource(enterpriseGuid);
            UserContextUtils.putErp(enterpriseGuid);
            log.info("自动解锁触发 delayautounlockbo={}", JacksonUtils.writeValueAsString(delayautounlockbo));
            try {
                List<String> tableGuidList = delayautounlockbo.getTableInfoBos().stream()
                        .map(TableInfoBO::getTableGuid)
                        .collect(Collectors.toList());
                String result = bizMsgService.sendMsg(delayautounlockbo, JacksonUtils.writeValueAsString(tableGuidList));
                log.info("发送消息服务结果 result={}", result);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("自动解锁触发通知消息服务异常 e={}", e.getMessage());
            } finally {
                dynamicHelper.clear();
                UserContextUtils.remove();
            }
        }, delayReleaseLockConfig.getTime(), delayReleaseLockConfig.getLevel(), TimeUnit.SECONDS);
        map.put("key", scheduledFuture);
    }

    /**
     * 微信加锁时的自动解锁机制
     */
    public void wxDoRelease(DelayAutoUnlockBO delayautounlockbo) {
        Map<String, Future> map = new HashMap<>();
        AtomicInteger atomicInteger = new AtomicInteger(0);
        ScheduledFuture<?> scheduledFuture = SE.scheduleAtFixedRate(() -> {
            if (atomicInteger.incrementAndGet() == 2) {
                map.get("key").cancel(true);
                map.clear();
                return;
            }
            String enterpriseGuid = delayautounlockbo.getEnterpriseGuid();
            dynamicHelper.changeDatasource(enterpriseGuid);
            UserContextUtils.putErp(enterpriseGuid);
            log.info("自动解锁触发 delayautounlockbo={}", JacksonUtils.writeValueAsString(delayautounlockbo));
            try {
                List<String> tableGuidList = delayautounlockbo.getTableInfoBos().stream()
                        .map(TableInfoBO::getTableGuid)
                        .collect(Collectors.toList());
                String result = bizMsgService.sendMsg(delayautounlockbo, JacksonUtils.writeValueAsString(tableGuidList));
                log.info("发送消息服务结果 result={}", result);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("自动解锁触发通知消息服务异常 e={}", e.getMessage());
            } finally {
                dynamicHelper.clear();
                UserContextUtils.remove();
            }
        }, delayReleaseLockConfig.getWxTime(), delayReleaseLockConfig.getLevel(), TimeUnit.SECONDS);
        map.put("key", scheduledFuture);
    }
}
