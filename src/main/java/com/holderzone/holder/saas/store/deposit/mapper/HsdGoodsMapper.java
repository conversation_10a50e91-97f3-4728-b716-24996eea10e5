package com.holderzone.holder.saas.store.deposit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holder.saas.store.deposit.entity.bo.ExpireGoodsDTO;
import com.holderzone.holder.saas.store.deposit.entity.bo.GoodsDO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-09-02
 */
@Component
public interface HsdGoodsMapper extends BaseMapper<GoodsDO> {
    /**
     * 根据打印记录Guid查询该条打印记录，包括其路由的打印机
     *
     * @param
     * @return
     */
    List<ExpireGoodsDTO> queryExpireGoods();
}
