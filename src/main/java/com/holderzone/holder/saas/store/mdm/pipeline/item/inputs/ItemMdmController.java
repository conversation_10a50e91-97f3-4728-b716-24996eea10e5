package com.holderzone.holder.saas.store.mdm.pipeline.item.inputs;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MDMResult;
import com.holderzone.holder.saas.store.mdm.entity.MDMSynDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.ItemSyncDTO;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MdmItemController
 * @date 2019/11/23 下午7:43
 * @description //
 * @program holder-saas-store-mdm
 */

@Slf4j
@RestController
@RequestMapping("/sync_item")
public class ItemMdmController {


    private final MqUtils mqUtils;

    public ItemMdmController(MqUtils mqUtils) {

        this.mqUtils = mqUtils;
    }


    @PostMapping("/create")
    @LogBefore(value = "外部系统批量创建商品", logLevel = LogLevel.INFO)
    public MDMResult<String> itemCreateBatchSyn(@Validated(ItemSyncDTO.Create.class)
                                                @RequestBody MDMSynDTO<List<ItemSyncDTO>> mdmSynDTO) {
        for (ItemSyncDTO itemSyncDTO : mdmSynDTO.getRequest()) {
            if (itemSyncDTO != null) {
                mqUtils.sendMessage(
                        RocketMqConfig.MainConfig.MAIN_MDM_ITEM_TOPIC,
                        RocketMqConfig.MainConfig.MAIN_MDM_ITEM_CREATE_TAG,
                        itemSyncDTO, UserContextUtils.getEnterpriseGuid()
                );
            }
        }
        return MDMResult.success();
    }

    @PostMapping("/update")
    @LogBefore(value = "外部系统批量更新商品", logLevel = LogLevel.INFO)
    public MDMResult<String> itemUpdateSyn(@Validated(ItemSyncDTO.Update.class)
                                           @RequestBody MDMSynDTO<List<ItemSyncDTO>> mdmSynDTO) {
        for (ItemSyncDTO itemSyncDTO : mdmSynDTO.getRequest()) {
            if (itemSyncDTO != null) {
                mqUtils.sendMessage(
                        RocketMqConfig.MainConfig.MAIN_MDM_ITEM_TOPIC,
                        RocketMqConfig.MainConfig.MAIN_MDM_ITEM_UPDATE_TAG,
                        itemSyncDTO, UserContextUtils.getEnterpriseGuid()
                );
            }
        }
        return MDMResult.success();
    }

    @PostMapping("/delete")
    @LogBefore(value = "外部系统批量删除商品", logLevel = LogLevel.INFO)
    public MDMResult<String> itemDeleteSyn(@Validated(ItemSyncDTO.Delete.class)
                                           @RequestBody MDMSynDTO<List<ItemSyncDTO>> mdmSynDTO) {
        for (ItemSyncDTO itemSyncDTO : mdmSynDTO.getRequest()) {
            if (itemSyncDTO != null) {
                mqUtils.sendMessage(
                        RocketMqConfig.MainConfig.MAIN_MDM_ITEM_TOPIC,
                        RocketMqConfig.MainConfig.MAIN_MDM_ITEM_DELETE_TAG,
                        itemSyncDTO, UserContextUtils.getEnterpriseGuid()
                );
            }
        }
        return MDMResult.success();
    }

}
