package com.holderzone.holder.saas.store.mdm.pipeline.org.inputs;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.event.AbsErpRocketMqConsumer;
import com.holderzone.holder.saas.store.mdm.pipeline.org.agg.BrandAggService;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.BrandInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MDMUserListener
 * @date 2019/11/23 18:04
 * @description
 * @program holder-saas-store
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.MainConfig.MAIN_MDM_BRAND_TOPIC,
        tags = {
                RocketMqConfig.MainConfig.MAIN_MDM_BRAND_CREATE_TAG,
                RocketMqConfig.MainConfig.MAIN_MDM_BRAND_DELETE_TAG,
                RocketMqConfig.MainConfig.MAIN_MDM_BRAND_UPDATE_TAG,
        },
        consumerGroup = RocketMqConfig.MainConfig.MAIN_MDM_BRAND_GROUP
)
public class BrandMdmConsumer extends AbsErpRocketMqConsumer<RocketMqTopic, String> {

    private final BrandAggService brandAggService;

    @Autowired
    public BrandMdmConsumer(BrandAggService brandAggService) {
        this.brandAggService = brandAggService;
    }

    @Override
    public boolean doConsumeMsg(String json, MessageExt messageExt) {
        String tags = messageExt.getTags();
        switch (tags) {
            case RocketMqConfig.MainConfig.MAIN_MDM_BRAND_CREATE_TAG:
                brandAggService.createLocalBrand(JacksonUtils.toObject(BrandInfoDTO.class, json));
                break;
            case RocketMqConfig.MainConfig.MAIN_MDM_BRAND_UPDATE_TAG:
                brandAggService.updateLocalBrand(JacksonUtils.toObject(BrandInfoDTO.class, json));
                break;
            case RocketMqConfig.MainConfig.MAIN_MDM_BRAND_DELETE_TAG:
                brandAggService.deleteLocalBrand(JacksonUtils.toObject(BrandInfoDTO.class, json));
                break;
            default:
                log.error("unknown mq tag : {}, message：{}", messageExt.getTags(), json);
                break;
        }
        return true;
    }
}
