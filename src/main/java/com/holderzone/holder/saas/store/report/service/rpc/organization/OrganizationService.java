package com.holderzone.holder.saas.store.report.service.rpc.organization;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrganizationService
 * @date 2019/05/31 15:34
 * @description 组织相关服务
 * @program holder-saas-store-report
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = OrganizationService.ServiceFallBack.class)
public interface OrganizationService {

    @PostMapping("/store/query_store_and_brand_by_id_list")
    List<StoreDTO> queryStoreAndBrandByIdList(@RequestBody List<String> storeGuidList);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<OrganizationService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OrganizationService create(Throwable cause) {
            return storeGuidList -> {
                log.error(HYSTRIX_PATTERN, "queryStoreAndBrandByIdList", JacksonUtils.writeValueAsString(storeGuidList), ThrowableUtils.asString(cause));
                throw new BusinessException("获取门店列表信息和品牌信息");
            };
        }
    }
}
