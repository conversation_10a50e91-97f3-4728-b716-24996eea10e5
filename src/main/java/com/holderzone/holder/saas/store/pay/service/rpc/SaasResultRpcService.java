package com.holderzone.holder.saas.store.pay.service.rpc;


import com.holderzone.holder.saas.store.pay.entity.HandlerPayBO;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.AggRefundPollingRespDTO;
import com.holderzone.saas.store.dto.pay.SaasAggRefundDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HandService
 * @date 2019/03/14 15:50
 * @description
 * @program holder-saas-store-trading-center
 */
public interface SaasResultRpcService {

    void handlePayResult(HandlerPayBO handlerPayBO, AggPayPollingRespDTO pollingRespDTO);

    void handleRefundResult(SaasAggRefundDTO saasAggRefundDTO, AggRefundPollingRespDTO aggRefundPollingRespDTO);
}
