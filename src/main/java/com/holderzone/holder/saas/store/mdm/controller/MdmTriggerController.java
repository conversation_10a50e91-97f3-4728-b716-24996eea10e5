/*
 * Copyright (c) 2018-2028 成都掌控者科技有限公司 All Rights Reserved.
 * ProjectName:saas-platform
 * FileName:MdmThiggerController.java
 * Date:2019-12-19
 * Author:terry
 */

package com.holderzone.holder.saas.store.mdm.controller;

import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.config.SyncConfig;
import com.holderzone.holder.saas.store.mdm.entity.MDMResult;
import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerDTO;
import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.service.EnterpriseServiceClient;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import com.holderzone.resource.common.dto.enterprise.EnterpriseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-12-19 上午10:39
 */
@Slf4j
@RestController
@RequestMapping("/sync")
public class MdmTriggerController {

    private final MqUtils mqUtils;

    private final SyncConfig syncConfig;

    private final EnterpriseServiceClient enterpriseServiceClient;

    public MdmTriggerController(MqUtils mqUtils, SyncConfig syncConfig, EnterpriseServiceClient enterpriseServiceClient) {
        this.mqUtils = mqUtils;
        this.syncConfig = syncConfig;
        this.enterpriseServiceClient = enterpriseServiceClient;
    }

    @PostMapping("/trigger")
    @LogBefore(value = "触发同步", logLevel = LogLevel.INFO)
    public MDMResult<String> trigger(@RequestBody @Validated MdmTriggerDTO mdmTriggerDTO) {
        if (syncConfig.shouldTriggerAgain(mdmTriggerDTO.getSecretKey())) {
            List<String> erpGuidList = mdmTriggerDTO.getErpGuidList();
            List<String> dbTableList = mdmTriggerDTO.getDbTableList();
            List<String> erpExcludeList = mdmTriggerDTO.getErpExcludeList();
            String triggerType = MdmTriggerType.ofType(mdmTriggerDTO.getTriggerType()).getType();
            if (erpGuidList.contains("*")) {
                List<EnterpriseDTO> allEnterprise = enterpriseServiceClient.getAllEnterprise();
                log.info("所有企业列表：{}", JacksonUtils.writeValueAsString(allEnterprise));
                erpGuidList = allEnterprise.stream().map(EnterpriseDTO::getEnterpriseGuid).collect(Collectors.toList());
            }
            if (erpExcludeList != null && !erpExcludeList.isEmpty()) {
                log.info("移除企业列表：{}", JacksonUtils.writeValueAsString(erpExcludeList));
                erpGuidList.removeAll(erpExcludeList);
            }
            log.info("待同步企业列表：{}", JacksonUtils.writeValueAsString(erpGuidList));
            if (dbTableList.contains("*")) {
                dbTableList = Arrays.asList("hso_brand", "hso_organization",
                        "hsi_type", "hsi_item", "hsi_sku", "hss_user");
            }
            log.info("待同步DbTable列表：{}", JacksonUtils.writeValueAsString(dbTableList));
            for (String erpGuid : erpGuidList) {
                for (String dbTable : dbTableList) {
                    switch (dbTable) {
                        case "hso_brand":
                            mqUtils.sendMessage(
                                    RocketMqConfig.StoreConfig.STORE_MDM_BRAND_TOPIC,
                                    RocketMqConfig.StoreConfig.STORE_MDM_BRAND_INIT_TAG,
                                    triggerType, erpGuid
                            );
                            break;
                        case "hso_organization":
                            mqUtils.sendMessage(
                                    RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_TOPIC,
                                    RocketMqConfig.StoreConfig.STORE_MDM_ORGANIZATION_INIT_TAG,
                                    triggerType, erpGuid
                            );
                            break;
                        case "hsi_type":
                            mqUtils.sendMessage(
                                    RocketMqConfig.StoreConfig.STORE_MDM_TYPE_TOPIC,
                                    RocketMqConfig.StoreConfig.STORE_MDM_TYPE_INIT_TAG,
                                    triggerType, erpGuid
                            );
                            break;
                        case "hsi_item":
                            mqUtils.sendMessage(
                                    RocketMqConfig.StoreConfig.STORE_MDM_ITEM_TOPIC,
                                    RocketMqConfig.StoreConfig.STORE_MDM_ITEM_INIT_TAG,
                                    triggerType, erpGuid
                            );
                            break;
                        case "hsi_sku":
                            mqUtils.sendMessage(
                                    RocketMqConfig.StoreConfig.STORE_MDM_SKU_TOPIC,
                                    RocketMqConfig.StoreConfig.STORE_MDM_SKU_INIT_TAG,
                                    triggerType, erpGuid
                            );
                            break;
                        case "hss_user":
                            mqUtils.sendMessage(
                                    RocketMqConfig.StoreConfig.STORE_MDM_USER_TOPIC,
                                    RocketMqConfig.StoreConfig.STORE_MDM_USER_INIT_TAG,
                                    triggerType, erpGuid
                            );
                            break;
                        default:
                            throw new IllegalStateException("Unexpected value: " + dbTable);
                    }
                }
            }
        }
        return MDMResult.success();
    }
}
