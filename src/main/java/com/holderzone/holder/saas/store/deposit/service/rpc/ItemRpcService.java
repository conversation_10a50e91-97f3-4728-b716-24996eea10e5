package com.holderzone.holder.saas.store.deposit.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.resp.MappingRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@Component
@FeignClient(value = "holder-saas-store-item", fallbackFactory = ItemRpcService.ServiceFallBack.class)
public interface ItemRpcService {

    @PostMapping("/item/kds_mapping")
    List<MappingRespDTO> kdsMapping(@RequestBody ItemSingleDTO itemSingleDTO);

    @PostMapping("/item/findSkus")
    Map<String, String> findSkusByItemName(@RequestBody List<String> itemNames);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ItemRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ItemRpcService create(Throwable throwable) {
            return new ItemRpcService() {

                @Override
                public List<MappingRespDTO> kdsMapping(ItemSingleDTO itemSingleDTO) {
                    log.error(HYSTRIX_PATTERN, "kdsMapping",
                            JacksonUtils.writeValueAsString(itemSingleDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Map<String, String> findSkusByItemName(List<String> itemNames) {
                    log.error(HYSTRIX_PATTERN, "findSkusByItemName",
                            JacksonUtils.writeValueAsString(itemNames),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}