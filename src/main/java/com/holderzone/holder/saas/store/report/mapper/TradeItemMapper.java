package com.holderzone.holder.saas.store.report.mapper;

import com.github.pagehelper.Page;
import com.holderzone.saas.store.dto.journaling.req.SalesVolumeReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeRespDTO;
import com.holderzone.saas.store.dto.report.openapi.SaleProductDetailRespDTO;
import com.holderzone.saas.store.dto.report.query.GoodsSalesVO;
import com.holderzone.saas.store.dto.report.query.SalesDetailQO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesDTO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesTotalDTO;
import com.holderzone.saas.store.dto.report.resp.SalesDetailRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单商品
 */
@Mapper
public interface TradeItemMapper {

    Long countStoreSaleStatistics(@Param("query") SalesVolumeReqDTO query);

    List<SalesVolumeRespDTO> pageStoreSaleStatistics(@Param("query") SalesVolumeReqDTO query);

    List<SalesVolumeRespDTO> pageGroupByStoreSaleStatistics(@Param("query") SalesVolumeReqDTO query);

    List<GoodsSalesDTO> queryItemTypeStatistics2(@Param("query") GoodsSalesVO query);

    GoodsSalesTotalDTO getStoreSalesTotal(@Param("query") SalesVolumeReqDTO query);

    GoodsSalesTotalDTO getGoodsSalesTotal(@Param("query") GoodsSalesVO query);

    Page<GoodsSalesDTO> queryItemTypeStatistics(@Param("query") GoodsSalesVO query);

    Long queryItemTypeStatisticsCount(@Param("query") GoodsSalesVO query);

    GoodsSalesTotalDTO queryItemTypeSalesTotal(@Param("query") GoodsSalesVO query);

    Page<GoodsSalesDTO> queryGroupItemSaleStatistics(@Param("query") GoodsSalesVO query);

    Long queryGroupItemSaleStatisticsCount(@Param("query") GoodsSalesVO query);

    GoodsSalesTotalDTO getGroupItemSaleTotal(@Param("query") GoodsSalesVO query);

    List<String> pageStoreSaleStatisticsType(@Param("query") SalesVolumeReqDTO query);

    /**
     * 销售明细总数查询
     */
    Long countSaleDetail(@Param("query") SalesDetailQO query);

    /**
     * 销售明细数据查询
     */
    List<SalesDetailRespDTO> pageSaleDetail(@Param("query") SalesDetailQO query);

    List<SaleProductDetailRespDTO> querySaleProductDetail(@Param("enterpriseGuid") String enterpriseGuid,
                                                                    @Param("orderGuidList") List<Long> orderGuidList);

    List<GoodsSalesDTO> pageStoreSaleStatisticsGroup(@Param("query") SalesVolumeReqDTO query);
}
