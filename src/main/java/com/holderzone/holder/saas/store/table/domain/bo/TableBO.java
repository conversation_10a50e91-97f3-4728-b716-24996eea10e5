package com.holderzone.holder.saas.store.table.domain.bo;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.table.domain.AreaDO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import lombok.*;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/12/27 11:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TableBO extends BaseBO {

    /**
     * 区域guid
     */
    private String areaGuid;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 桌台编号
     */
    private String tableCode;

    /**
     * 桌台座位数
     */
    private Integer seats;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否已启用
     * 0=已启用
     * 1=未启用
     * 默认启用
     */
    private Integer enable;

    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    private Integer deleted;

    /**
     * 主单号
     */
    private String mainOrderGuid;

    /**
     * 订单号
     */
    private String orderGuid;

    /**
     * 桌台状态
     * -1=暂停使用
     * 0=空闲
     * 1=占用
     * 2=预定
     * 3=待清台
     */
    private Integer status;

    /**
     * 10=占用空台
     * 11=占用锁定
     * 12=占用并台
     * 20=预定未锁定
     * 21=预定已锁定
     */
    private Set<Integer> subStatus;

    private Integer combineTimes;

    private Integer associatedTimes;

    private String openStaffGuid;

    private String openStaffName;

    private LocalDateTime openTableTime;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    private String subStatusStr;

    public Set<Integer> getSubStatus() {
        if (null != this.subStatusStr && !"".equals(this.subStatusStr)) {
            return new HashSet<>(JacksonUtils.toObjectList(Integer.class, this.subStatusStr));
        }
        return this.subStatus;
    }

    public static TableBO createInitTable(BaseDTO baseDTO, AreaDO areaDO) {
        TableBO build = TableBO.builder()
                .areaGuid(String.valueOf(areaDO.getGuid()))
                .areaName(areaDO.getAreaName())
                .deleted(0)
                .enable(0)
                .seats(4)
                .build();
        build.setStoreGuid(baseDTO.getStoreGuid());
        build.setStoreName(baseDTO.getStoreName());
        return build;
    }

}
