package com.holderzone.holder.saas.store.mdm.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MqMessageDTO
 * @date 2019/11/19 10:34
 * @description mq消息实体
 * @program holder-saas-store
 */
@Data
@Deprecated
@NoArgsConstructor
@AllArgsConstructor
public class MqMessageDTO<T> {

    /**
     * 消息对象
     */
    private T t;

    /**
     * enterpriseGuid
     */
    private String erpGuid;
}

