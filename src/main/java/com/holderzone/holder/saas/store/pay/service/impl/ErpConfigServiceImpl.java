package com.holderzone.holder.saas.store.pay.service.impl;


import com.google.common.cache.CacheLoader;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.pay.service.ErpConfigService;
import com.holderzone.holder.saas.store.pay.service.rpc.EnterpriseRpcService;
import com.holderzone.holder.saas.store.pay.service.rpc.OrderRpcService;
import com.holderzone.holder.saas.store.pay.service.rpc.PaymentTypeRpcService;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.PaymentInfoDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ErpConfigServiceImpl
 * @date 2019/03/14 11:36
 * @description
 * @program holder-saas-store-trading-center
 */
@Slf4j
@Service
public class ErpConfigServiceImpl implements ErpConfigService {

    private final EnterpriseRpcService enterpriseRpcService;

    private final PaymentTypeRpcService paymentTypeRpcService;

    private final OrderRpcService orderRpcService;

    @Autowired
    public ErpConfigServiceImpl(EnterpriseRpcService enterpriseRpcService, PaymentTypeRpcService paymentTypeRpcService,
                                OrderRpcService orderRpcService) {
        this.enterpriseRpcService = enterpriseRpcService;
        this.paymentTypeRpcService = paymentTypeRpcService;
        this.orderRpcService = orderRpcService;
    }

    @Override
    public PaymentInfoDTO getPaymentInfo(String enterpriseGuid, String storeGuid) {
        try {
//            return paymentInfoCache.get(enterpriseGuid + ":" + storeGuid,
//                    () -> enterpriseRpcService.getJHInfo(enterpriseGuid, storeGuid)
//            );
            log.info("查询商户聚合支付配置入参,enterpriseGuid:{},storeGuid:{}", enterpriseGuid, storeGuid);
            PaymentInfoDTO jhInfo = enterpriseRpcService.getJHInfo(enterpriseGuid, storeGuid);
            log.info("查询商户聚合支付配置返回:{}", jhInfo);
            return jhInfo;
//        } catch (ExecutionException e) {
//            return enterpriseRpcService.getJHInfo(enterpriseGuid, storeGuid);
        } catch (CacheLoader.InvalidCacheLoadException e) {
            log.error("未查询到商户聚合支付配置：{}", enterpriseGuid + ":" + storeGuid);
            return null;
        }
    }

    /**
     * 聚合支付分流处理
     *
     * @param enterpriseGuid 企业guid
     * @param storeGuid      门店guid
     * @param orderGuid      订单guid
     * @return 聚合支付账户信息
     */
    @Override
    public PaymentInfoDTO getPaymentInfo(String enterpriseGuid, String storeGuid, String orderGuid) {
        try {
            log.info("获取聚合支付方式信息入参,enterpriseGuid:{},storeGuid:{} orderGuid={}", enterpriseGuid, storeGuid, orderGuid);
            UserContextUtils.putErp(enterpriseGuid);
            PaymentTypeDTO jhPaymentTypeInfo = paymentTypeRpcService.getJhPaymentTypeInfo(storeGuid);
            log.info("获取聚合支付方式信息返回 jhPaymentTypeInfo={}", JacksonUtils.writeValueAsString(jhPaymentTypeInfo));
            List<PaymentInfoDTO> jhPayInfoList = jhPaymentTypeInfo.getJhPayInfoList();
            if (CollectionUtils.isEmpty(jhPayInfoList)) {
                return null;
            }
            if (0 == jhPaymentTypeInfo.getPaymentShunt()) {
                return jhPayInfoList.get(0);
            }

            // 默认账户
            List<PaymentInfoDTO> defaultPaymentInfo = jhPayInfoList.stream()
                    .filter(jh -> 1 == jh.getIsDefaultAccount())
                    .collect(Collectors.toList());

            // 聚合支付分流：根据后台设置订单编号尾号数字绑定的账户进行聚合支付账户进行收款
            OrderDTO orderDTO = orderRpcService.findByOrderGuid(orderGuid);
            log.info("orderDTO={}", JacksonUtils.writeValueAsString(orderDTO));
            if (ObjectUtils.isEmpty(orderDTO)) {
                return defaultPaymentInfo.get(0);
            }
            if (!StringUtils.isEmpty(orderDTO.getPaymentAppId()) && !Objects.equals(3, orderDTO.getRecoveryType())) {
                log.info("根据支付appid查询聚合支付信息 paymentInfoGuid={}", orderDTO.getPaymentAppId());
                List<PaymentInfoDTO> paymentInfoDTOList = enterpriseRpcService.listPayAppId(orderDTO.getPaymentAppId(),
                        enterpriseGuid, storeGuid);
                log.info("根据支付appid查询聚合支付信息 paymentInfoDTOList={}", JacksonUtils.writeValueAsString(paymentInfoDTOList));
                if (CollectionUtils.isEmpty(paymentInfoDTOList)) {
                    return null;
                }
                return paymentInfoDTOList.get(0);
            }

            String endNum = orderDTO.getOrderNo().substring(orderDTO.getOrderNo().length() - 1);
            log.info("endNum={}", endNum);
            List<PaymentInfoDTO> shuntPaymentInfo = jhPayInfoList.stream()
                    .filter(jh -> jh.getDiversionRules().contains(endNum))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(shuntPaymentInfo)) {
                return defaultPaymentInfo.get(0);
            }
            return shuntPaymentInfo.get(0);
        } catch (CacheLoader.InvalidCacheLoadException e) {
            log.error("未查询到商户聚合支付配置：{}", enterpriseGuid + ":" + storeGuid);
            return null;
        }
    }

    /**
     * 获取聚合支付分流账户信息
     * 如果分流规则匹配不上账户，但是该账户还绑定在门店上，可以查询到进行退款
     *
     * @param enterpriseGuid 企业guid
     * @param storeGuid      门店guid
     * @param orderGuid      订单guid
     * @return 聚合支付账户信息
     */
    @Override
    public PaymentInfoDTO getShuntPaymentInfo(String enterpriseGuid, String storeGuid, String orderGuid) {
        PaymentInfoDTO paymentInfoDTO = getPaymentInfo(enterpriseGuid, storeGuid, orderGuid);
        if (ObjectUtils.isEmpty(paymentInfoDTO)) {
            OrderDTO orderDTO = orderRpcService.findByOrderGuid(orderGuid);
            if (StringUtils.isEmpty(orderDTO.getPaymentAppId()) || Objects.equals(3, orderDTO.getRecoveryType())) {
                return null;
            }
            log.info("===查询分流的支付信息===");
            List<PaymentInfoDTO> paymentInfoDTOList = enterpriseRpcService.listPayAppId(orderDTO.getPaymentAppId(),
                    enterpriseGuid, storeGuid);
            log.info("根据支付appid查询聚合支付信息 paymentInfoDTOList={}", JacksonUtils.writeValueAsString(paymentInfoDTOList));
            if (CollectionUtils.isEmpty(paymentInfoDTOList)) {
                return null;
            }
            return paymentInfoDTOList.get(0);
        }
        return paymentInfoDTO;
    }

    @Override
    public Mono<PaymentInfoDTO> getPaymentInfoAsync(String enterpriseGuid, String storeGuid) {
        return Mono.create(paymentInfoDTOMonoSink -> {
            PaymentInfoDTO paymentInfoDTO = getPaymentInfo(enterpriseGuid, storeGuid);
            if (null != paymentInfoDTO) {
                paymentInfoDTOMonoSink.success(paymentInfoDTO);
            } else {
                paymentInfoDTOMonoSink.error(new BusinessException("商户聚合支付配置异常"));
            }
        });
    }

    /**
     * 聚合支付分流账户处理
     * 账户支付的钱只能退回该账户，需要特殊处理：
     * 如果分流规则匹配不上账户，但是该账户还绑定在门店上，可以查询到进行退款
     *
     * @param enterpriseGuid 企业guid
     * @param storeGuid      门店guid
     * @param orderGuid      订单guid
     * @return 聚合支付账户信息
     */
    @Override
    public Mono<PaymentInfoDTO> getShuntPaymentInfoAsync(String enterpriseGuid, String storeGuid, String orderGuid) {
        return Mono.create(paymentInfoDTOMonoSink -> {
            PaymentInfoDTO paymentInfoDTO = getPaymentInfo(enterpriseGuid, storeGuid, orderGuid);
            if (null != paymentInfoDTO) {
                paymentInfoDTOMonoSink.success(paymentInfoDTO);
            } else {
                paymentInfoDTOMonoSink.error(new BusinessException("商户聚合支付配置异常"));
            }
        });
    }

}