package com.holderzone.holder.saas.store.deposit.util;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.holder.saas.store.deposit.service.SendMessageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SendMessageUtil
 * @date 2018/11/01 11:18
 * @description //TODO
 * @program holder-saas-store-order
 */
@Component
public class SendMessageUtil {

    private ExecutorService executorService = new ThreadPoolExecutor(5, 20,
            5L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(20),
            new ThreadFactoryBuilder().setNameFormat("message-pool-%d").build());

    private static final Logger logger = LoggerFactory.getLogger(SendMessageUtil.class);

    @Autowired
    private SendMessageService sendMessageService;

    public void sendMessage(MessageDTO m1, String entGuid) {
        executorService.submit(() -> {
            try {
                sendMessageService.sendMessage(m1, entGuid);
            } catch (Exception e) {
                logger.error("发送短信失败,msg={}", e.getMessage());
            }
        });
    }
}
