package com.holderzone.holder.saas.store.deposit.entity.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("hse_deposit_operation")
public class OperationDO implements Serializable {

    private static final long serialVersionUID = 8424514095125620853L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    private String guid;

    private String depositGuid;

    private String userId;

    private String operator;

    private String remark;

    /**
     * 操作方式：0：取出，1：存入
     */
    private int operationWay;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}

