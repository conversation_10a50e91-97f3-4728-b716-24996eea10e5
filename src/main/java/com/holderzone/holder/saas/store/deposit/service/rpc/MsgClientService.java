package com.holderzone.holder.saas.store.deposit.service.rpc;

import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.exception.unchecked.ParameterException;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "base-service",fallbackFactory = MsgClientService.FallBack.class)
public interface MsgClientService {

    @PostMapping("/message/sendMessage")
    void sendMessage(@RequestBody MessageDTO message);

    @Component
    class FallBack implements FallbackFactory<MsgClientService> {
        private static final Logger logger = LoggerFactory.getLogger(MsgClientService.FallBack.class);

        @Override
        public MsgClientService create(Throwable throwable) {
            return new MsgClientService() {


                @Override
                public void sendMessage(MessageDTO message) {
                    logger.error("发送短信失败,message={}",throwable.getMessage());
                    throw new ParameterException(throwable.getMessage());
                }
            };
        }
    }

}
