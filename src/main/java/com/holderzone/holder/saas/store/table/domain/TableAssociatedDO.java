package com.holderzone.holder.saas.store.table.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 桌台联台
 */
@Data
@TableName("hst_table_associated")
public class TableAssociatedDO implements Serializable {

    private static final long serialVersionUID = 26706998690311066L;

    private Long id;

    /**
     * 业务主键
     */
    @TableId(value = "guid", type = IdType.INPUT)
    private String guid;

    /**
     * 桌号外键
     */
    private String tableGuid;

    /**
     * 关联桌台guid
     */
    private String associatedTableGuid;

    /**
     * 开台人guid
     */
    private String openStaffGuid;

    /**
     * 开台人name
     */
    private String openStaffName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;


    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    @TableLogic
    private Integer deleted;

}
