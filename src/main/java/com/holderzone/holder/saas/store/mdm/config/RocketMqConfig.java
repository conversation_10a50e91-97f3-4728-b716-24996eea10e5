package com.holderzone.holder.saas.store.mdm.config;

public interface RocketMqConfig {

    String MESSAGE_CONTEXT = "message-context";

    String MSG_LOG_TRACE_CONTEXT = "msg-log-trace-context";

    interface StoreConfig {

        String STORE_MDM_ITEM_TOPIC = "store-mdm-item-topic";

        String STORE_MDM_ITEM_GROUP = "store-mdm-item-group";

        String STORE_MDM_ITEM_INIT_TAG = "store-mdm-item-init-tag";

        String STORE_MDM_ITEM_CREATE_TAG = "store-mdm-item-create-tag";

        String STORE_MDM_ITEM_UPDATE_TAG = "store-mdm-item-update-tag";

        String STORE_MDM_ITEM_DELETE_TAG = "store-mdm-item-delete-tag";

        String STORE_MDM_TYPE_TOPIC = "store-mdm-type-topic";

        String STORE_MDM_TYPE_GROUP = "store-mdm-type-group";


        String STORE_MDM_TYPE_INIT_TAG = "store-mdm-type-init-tag";

        String STORE_MDM_TYPE_CREATE_TAG = "store-mdm-type-create-tag";

        String STORE_MDM_TYPE_UPDATE_TAG = "store-mdm-type-update-tag";

        String STORE_MDM_TYPE_DELETE_TAG = "store-mdm-type-delete-tag";


        String STORE_MDM_SKU_TOPIC = "store-mdm-sku-topic";

        String STORE_MDM_SKU_GROUP = "store-mdm-sku-group";

        String STORE_MDM_SKU_INIT_TAG = "store-mdm-sku-init-tag";

        String STORE_MDM_SKU_CREATE_TAG = "store-mdm-sku-create-tag";

        String STORE_MDM_SKU_UPDATE_TAG = "store-mdm-sku-update-tag";

        String STORE_MDM_SKU_DELETE_TAG = "store-mdm-sku-delete-tag";


        String STORE_MDM_BRAND_TOPIC = "store-mdm-brand-topic";

        String STORE_MDM_BRAND_INIT_TAG = "store-mdm-brand-init-tag";

        String STORE_MDM_BRAND_CREATE_TAG = "store-mdm-brand-create-tag";

        String STORE_MDM_BRAND_UPDATE_TAG = "store-mdm-brand-update-tag";

        String STORE_MDM_BRAND_DELETE_TAG = "store-mdm-brand-delete-tag";

        String STORE_MDM_BRAND_GROUP = "store-mdm-brand-group";


        String STORE_MDM_ORGANIZATION_TOPIC = "store-mdm-organization-topic";

        String STORE_MDM_ORGANIZATION_INIT_TAG = "store-mdm-organization-init-tag";

        String STORE_MDM_ORGANIZATION_CREATE_TAG = "store-mdm-organization-create-tag";

        String STORE_MDM_ORGANIZATION_UPDATE_TAG = "store-mdm-organization-update-tag";

        String STORE_MDM_ORGANIZATION_DELETE_TAG = "store-mdm-organization-delete-tag";

        String STORE_MDM_ORGANIZATION_GROUP = "store-mdm-organization-group";


        String STORE_MDM_USER_TOPIC = "store-mdm-user-topic";

        String STORE_MDM_USER_INIT_TAG = "store-mdm-user-init-tag";

        String STORE_MDM_USER_CREATE_TAG = "store-mdm-user-create-tag";

        String STORE_MDM_USER_UPDATE_TAG = "store-mdm-user-update-tag";

        String STORE_MDM_USER_DELETE_TAG = "store-mdm-user-delete-tag";

        String STORE_MDM_USER_GROUP = "store-mdm-user-group";
    }

    interface MainConfig {

        String MAIN_MDM_USER_TOPIC = "main-mdm-user-topic";

        String MAIN_MDM_USER_CREATE_TAG = "main-mdm-user-create-tag";

        String MAIN_MDM_USER_UPDATE_TAG = "main-mdm-user-update-tag";

        String MAIN_MDM_USER_DELETE_TAG = "main-mdm-user-delete-tag";

        String MAIN_MDM_USER_GROUP = "main-mdm-user-group-2";


        String MAIN_MDM_ITEM_TOPIC = "main-mdm-item-topic";

        String MAIN_MDM_ITEM_CREATE_TAG = "main-mdm-item-create-tag";

        String MAIN_MDM_ITEM_UPDATE_TAG = "main-mdm-item-update-tag";

        String MAIN_MDM_ITEM_DELETE_TAG = "main-mdm-item-delete-tag";

        String MAIN_MDM_ITEM_GROUP = "main-mdm-item-group";


        String MAIN_MDM_TYPE_TOPIC = "main-mdm-type-topic";

        String MAIN_MDM_TYPE_CREATE_TAG = "main-mdm-type-create-tag";

        String MAIN_MDM_TYPE_UPDATE_TAG = "main-mdm-type-update-tag";

        String MAIN_MDM_TYPE_DELETE_TAG = "main-mdm-type-delete-tag";

        String MAIN_MDM_TYPE_GROUP = "main-mdm-type-group";


        String MAIN_MDM_SKU_TOPIC = "main-mdm-sku-topic";

        String MAIN_MDM_SKU_CREATE_TAG = "main-mdm-sku-create-tag";

        String MAIN_MDM_SKU_UPDATE_TAG = "main-mdm-sku-update-tag";

        String MAIN_MDM_SKU_DELETE_TAG = "main-mdm-sku-delete-tag";

        String MAIN_MDM_SKU_GROUP = "main-mdm-sku-group";


        String MAIN_MDM_ORGANIZATION_TOPIC = "main-mdm-organization-topic";

        String MAIN_MDM_ORGANIZATION_CREATE_TAG = "main-mdm-organization-create-tag";

        String MAIN_MDM_ORGANIZATION_UPDATE_TAG = "main-mdm-organization-update-tag";

        String MAIN_MDM_ORGANIZATION_DELETE_TAG = "main-mdm-organization-delete-tag";

        String MAIN_MDM_ORGANIZATION_GROUP = "main-mdm-organization-group";


        String MAIN_MDM_BRAND_TOPIC = "main-mdm-brand-topic";

        String MAIN_MDM_BRAND_CREATE_TAG = "main-mdm-brand-create-tag";

        String MAIN_MDM_BRAND_UPDATE_TAG = "main-mdm-brand-update-tag";

        String MAIN_MDM_BRAND_DELETE_TAG = "main-mdm-brand-delete-tag";

        String MAIN_MDM_BRAND_GROUP = "main-mdm-brand-group";
    }

    interface CloudConfig {

        String USER_SYNC_TOPIC = "user-sync-topic";

        String USER_SYNC_UPLOAD_TAG = "user-sync-upload-tag";

        String MERCHANT_SYNC_ORGANIZATION_TOPIC = "merchant-organization-topic";

        String MERCHANT_SYNC_ORGANIZATION_TAG = "merchant-organization-tag";
    }
}
