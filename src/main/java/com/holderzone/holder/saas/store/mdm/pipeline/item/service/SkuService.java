package com.holderzone.holder.saas.store.mdm.pipeline.item.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.SkuSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.SkuDO;

import java.util.List;

public interface SkuService extends IService<SkuDO> {

    /**
     * mdm推送本地同步创建Sku
     * @param mdmSkuSynDTO
     * @return
     */
    void insertSku(SkuSyncDTO mdmSkuSynDTO);

    /**
     * mdm推送本地同步更新Sku
     * @param mdmSkuSynDTO
     * @return
     */
    void updateSkuByGuid(SkuSyncDTO mdmSkuSynDTO);

    /**
     * mdm推送本地同步删除Sku
     * @param guid
     * @return
     */
    void deleteSkuByGuid(String guid);

    SkuDO getLocalRecord(Wrapper<SkuDO> queryWrapper);

    List<SkuDO> shouldInitSkuDOS();
}
