package com.holderzone.holder.saas.store.pay.utils;

import com.holderzone.holder.saas.store.pay.constant.PayConstant;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.AggPayRespDTO;
import com.holderzone.saas.store.dto.pay.AggRefundPollingRespDTO;
import com.holderzone.saas.store.dto.pay.AggRefundRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.regex.Pattern;

@Slf4j
@Component
public final class RespFriendlyUtils {

    private static final Pattern PATTERN = Pattern.compile("[a-zA-z]");

    public void convert2Chinese(AggPayRespDTO resp) {
        if (!StringUtils.equals(resp.getCode(), PayConstant.SUCCESS_CODE)
                && StringUtils.isNotBlank(resp.getMsg())
                && PATTERN.matcher(resp.getMsg()).find()) {
            log.error("交易失败：code={}, msg={}", resp.getCode(), resp.getMsg());
            resp.setMsg("交易失败");
        }
    }

    public void convert2Chinese(AggPayPollingRespDTO resp) {
        if (!StringUtils.equals(resp.getCode(), PayConstant.SUCCESS_CODE)
                && StringUtils.isNotBlank(resp.getMsg())
                && PATTERN.matcher(resp.getMsg()).find()) {
            log.error("交易失败：code={}, msg={}", resp.getCode(), resp.getMsg());
            resp.setMsg("交易失败");
        }
    }

    public void convert2Chinese(AggRefundRespDTO resp) {
        if (!StringUtils.equals(resp.getCode(), PayConstant.SUCCESS_CODE)
                && StringUtils.isNotBlank(resp.getMsg())
                && PATTERN.matcher(resp.getMsg()).find()) {
            log.error("交易失败：code={}, msg={}", resp.getCode(), resp.getMsg());
            resp.setMsg("交易失败");
        }
    }

    public void convert2Chinese(AggRefundPollingRespDTO resp) {
        if (!StringUtils.equals(resp.getCode(), PayConstant.SUCCESS_CODE)
                && StringUtils.isNotBlank(resp.getMsg())
                && PATTERN.matcher(resp.getMsg()).find()) {
            log.error("交易失败：code={}, msg={}", resp.getCode(), resp.getMsg());
            resp.setMsg("交易失败");
        }
    }

    public Mono<AggPayRespDTO> convertPay2Friendly(Throwable throwable) {
        log.error("聚合支付预下单异常：{}", throwable.getMessage());
//        return Mono.just(AggPayRespDTO.errorPaymentResp("10008", throwable.getMessage()));
        return Mono.just(AggPayRespDTO.errorPaymentResp("10008", "交易失败"));
    }

    public Mono<AggPayPollingRespDTO> convertPayPolling2Friendly(Throwable throwable, String msgTemplate) {
        log.error(msgTemplate + "：{}", throwable.getMessage());
        return Mono.just(AggPayPollingRespDTO.errorResp("10007", throwable.getMessage()));
    }

    public Mono<AggRefundRespDTO> convertRefund2Friendly(Throwable throwable) {
        log.error("聚合支付退款异常：{}", throwable.getMessage());
        return Mono.just(AggRefundRespDTO.errorPaymentResp("10007", throwable.getMessage()));
    }

    public Mono<AggRefundPollingRespDTO> convertRefundPolling2Friendly(Throwable throwable) {
        log.error("聚合支付退款轮询异常：{}", throwable.getMessage());
        return Mono.just(AggRefundPollingRespDTO.errorResp("10008", throwable.getMessage()));
    }
}
