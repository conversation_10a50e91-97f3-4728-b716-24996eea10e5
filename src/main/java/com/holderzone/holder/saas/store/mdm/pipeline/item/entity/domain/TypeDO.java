package com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 商品分类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hsi_type")
@NoArgsConstructor
public class TypeDO extends BasePushDO {


    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否启用（0：否，1：是）
     */
    private Integer isEnable;

    /**
     * 分类描述
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String description;

    /**
     * 图标
     */
    @TableField(strategy = FieldStrategy.NOT_NULL)
    private String iconUrl;

    /**
     * 关联该分类的商品数
     */
    private Integer itemNum;

    /**
     * 是否因重复而改名：0：否，1：是
     */
    private Integer nameChange;

    /**
     * 分类来源（0：门店自己创建的分类，1：品牌自己创建的分类,2:被推送过来的分类）
     */
    private Integer typeFrom;

    /**
     * 是否逻辑删除  0：否 1：是
     */
    @TableLogic
    private  Integer isDelete;
}
