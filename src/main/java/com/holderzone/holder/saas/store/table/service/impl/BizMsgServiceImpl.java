package com.holderzone.holder.saas.store.table.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.table.client.BizMsgRpcClient;
import com.holderzone.holder.saas.store.table.client.OrganizationClientService;
import com.holderzone.holder.saas.store.table.client.TradeRpcClient;
import com.holderzone.holder.saas.store.table.domain.TableBasicDO;
import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.holder.saas.store.table.mapper.TableBasicMapper;
import com.holderzone.holder.saas.store.table.mapper.TableOrderMapper;
import com.holderzone.holder.saas.store.table.service.BizMsgService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.table.TableCombineDTO;
import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.TurnMessageDTO;
import com.holderzone.saas.store.dto.table.TurnTableDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @className BizMsgServiceImpl
 * @date 2019/01/16 17:09
 * @description
 * @program holder-saas-store-table
 */
@Slf4j
@Service
@AllArgsConstructor
public class BizMsgServiceImpl implements BizMsgService {

    private final BizMsgRpcClient bizMsgRpcClient;

    private final OrganizationClientService organizationClientService;

    private final TableBasicMapper tableBasicMapper;

    private final TableOrderMapper tableOrderMapper;

    private final TradeRpcClient tradeRpcClient;

    private static final String TABLE_TURN = "table_turn:";

    @Override
    public String sendMsg(BaseDTO baseDTO, String content) {
        BusinessMessageDTO messageDTO = BusinessMessageDTO.builder()
                .subject("桌位状态变化通知")
                .messageType(BusinessMsgTypeEnum.TABLE_STATE_MSG_TYPE.getId())
                .detailMessageType(BusinessMsgTypeEnum.TABLE_CHANGED.getId())
                .content(content)
                .platform("2")
                .storeGuid(baseDTO.getStoreGuid())
                .storeName(baseDTO.getStoreName())
                .build();
        return bizMsgRpcClient.sendMsg(messageDTO);
    }

    @Override
    public String sendMsg(BaseDTO openTableDTO, List<String> tableToNotify) {
        return sendMsg(openTableDTO, JacksonUtils.writeValueAsString(tableToNotify));
    }

    /**
     * 发送消息给pad的各个桌台
     * content：当前桌台已于大厅-A05进行并桌，桌台订单将同步合并
     *
     * @param tableCombineDTO 桌台并单信息
     */
    @Override
    public void sendMsg(TableCombineDTO tableCombineDTO) {
        String storeGuid = tableCombineDTO.getStoreGuid();
        String mainTableGuid = tableCombineDTO.getMainTableGuid();
        BusinessMessageDTO combineMessageDTO = new BusinessMessageDTO();
        combineMessageDTO.setSubject(BusinessMsgTypeEnum.MERGE_TABLE.getName());

        // 当前桌台已于大厅-A05进行并桌，桌台订单将同步合并
        TableBasicDO tableBasicDO = tableBasicMapper.selectOne(new LambdaQueryWrapper<TableBasicDO>()
                .eq(TableBasicDO::getGuid, mainTableGuid));
        if (Objects.isNull(tableBasicDO)) {
            log.warn("桌台信息null tableGuid={}", mainTableGuid);
            throw new BusinessException("桌台信息null");
        }
        StringBuilder buffer = new StringBuilder();
        buffer.append("当前桌台已与")
                .append(tableBasicDO.getAreaName())
                .append("-")
                .append(tableBasicDO.getTableCode())
                .append("进行并桌，桌台订单将同步合并");
        combineMessageDTO.setContent(buffer.toString());

        combineMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        combineMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.MERGE_TABLE.getId());
        combineMessageDTO.setPlatform("2");
        combineMessageDTO.setStoreGuid(storeGuid);
        combineMessageDTO.setStoreName(tableCombineDTO.getStoreName());

        // 查询子桌设备号
        PadOrderTypeReqDTO reqDTO = new PadOrderTypeReqDTO();
        reqDTO.setStoreGuid(storeGuid);
        // 排除主桌推送
        List<String> tableGuidList = tableCombineDTO.getTableGuidList();
        tableGuidList.removeIf(t -> Objects.equals(mainTableGuid, t));
        reqDTO.setTableGuidList(tableGuidList);
        List<StoreDeviceDTO> storeDeviceDTOList = organizationClientService.listDeviceByStoreTable(reqDTO);
        if (!CollectionUtils.isEmpty(storeDeviceDTOList)) {
            storeDeviceDTOList.forEach(device -> {
                combineMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + device.getDeviceNo());
                log.info("并台子桌消息 combineMessageDTO={}", JacksonUtils.writeValueAsString(combineMessageDTO));
                bizMsgRpcClient.sendMsg(combineMessageDTO);
            });
        }

        // 主桌并台推送订单状态变化
        BusinessMessageDTO mainTableMessageDTO = new BusinessMessageDTO();
        mainTableMessageDTO.setSubject(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getName());
        mainTableMessageDTO.setContent("主桌并台消息");
        mainTableMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        mainTableMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getId());
        mainTableMessageDTO.setPlatform("2");

        // 查询门店桌台对应设备信息
        PadOrderTypeReqDTO mainReqDTO = new PadOrderTypeReqDTO();
        mainReqDTO.setStoreGuid(storeGuid);
        mainReqDTO.setTableGuid(mainTableGuid);
        StoreDeviceDTO storeDeviceDTO = organizationClientService.queryDeviceByStoreTable(mainReqDTO);
        if (ObjectUtils.isEmpty(storeDeviceDTO)) {
            log.error("未查询到设备信息 reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
            return;
        }
        mainTableMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + storeDeviceDTO.getDeviceNo());
        mainTableMessageDTO.setStoreGuid(storeGuid);
        mainTableMessageDTO.setStoreName(tableCombineDTO.getStoreName());
        log.info("并台主桌消息 mainTableMessageDTO={}", JacksonUtils.writeValueAsString(mainTableMessageDTO));
        bizMsgRpcClient.sendMsg(mainTableMessageDTO);
    }

    /**
     * 主动转台/被动转台 发送消息给对应的pad设备
     * content：您的订单已转至大厅-A03桌台，请使用大厅-A03桌台的PAD进行点餐，继续使用将产生新的订单
     *
     * @param turnTableDTO 转台信息
     */
    @Override
    public void sendMsg(TurnTableDTO turnTableDTO) {
        String originTableGuid = turnTableDTO.getOriginTableGuid();
        String newTableGuid = turnTableDTO.getNewTableGuid();
        BusinessMessageDTO turnMessageDTO = new BusinessMessageDTO();
        turnMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        turnMessageDTO.setPlatform("2");
        turnMessageDTO.setStoreGuid(turnTableDTO.getStoreGuid());
        turnMessageDTO.setStoreName(turnTableDTO.getStoreName());

        // 根据桌台查询orderGuid
        TableOrderDO tableOrderDO = tableOrderMapper.selectOne(new LambdaQueryWrapper<TableOrderDO>()
                .eq(TableOrderDO::getTableGuid, newTableGuid));
        String orderGuid = Optional.ofNullable(tableOrderDO)
                .map(x -> StringUtils.isEmpty(x.getMainOrderGuid()) ? x.getOrderGuid() : x.getMainOrderGuid())
                .orElse(null);
        if (StringUtils.isEmpty(orderGuid)) {
            log.warn("未查询到订单guid orderGuid={}", orderGuid);
            throw new BusinessException("未查询到订单guid");
        }

        // 订单:旧:新
        String redisKey = TABLE_TURN + orderGuid + ":" + originTableGuid + ":" + newTableGuid;

        // 查询俩桌设备号
        PadOrderTypeReqDTO reqDTO = new PadOrderTypeReqDTO();
        reqDTO.setStoreGuid(turnTableDTO.getStoreGuid());
        List<String> tableGuidList = new ArrayList<>();
        tableGuidList.add(newTableGuid);
        tableGuidList.add(originTableGuid);
        reqDTO.setTableGuidList(tableGuidList);
        List<StoreDeviceDTO> storeDeviceDTOList = organizationClientService.listDeviceByStoreTable(reqDTO);
        if (!CollectionUtils.isEmpty(storeDeviceDTOList)) {
            storeDeviceDTOList.forEach(device -> {
                // 主动转台
                if (Objects.equals(originTableGuid, device.getTableGuid())) {
                    turnMessageDTO.setSubject(BusinessMsgTypeEnum.TRANSFER_TABLE_POSITIVE.getName());
                    turnMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.TRANSFER_TABLE_POSITIVE.getId());

                    // 您的订单已转至大厅-A03桌台，请使用大厅-A03桌台的PAD进行点餐，继续使用将产生新的订单
                    TableBasicDO originTableBasicDO = tableBasicMapper.selectOne(new LambdaQueryWrapper<TableBasicDO>()
                            .eq(TableBasicDO::getGuid, originTableGuid));
                    if (Objects.isNull(originTableBasicDO)) {
                        log.warn("旧桌台信息null tableGuid={}", originTableGuid);
                        throw new BusinessException("旧桌台信息null");
                    }
                    TableBasicDO newTableBasicDO = tableBasicMapper.selectOne(new LambdaQueryWrapper<TableBasicDO>()
                            .eq(TableBasicDO::getGuid, newTableGuid));
                    if (Objects.isNull(newTableBasicDO)) {
                        log.warn("旧桌台信息null tableGuid={}", newTableGuid);
                        throw new BusinessException("旧桌台信息null");
                    }

                    StringBuilder buffer = new StringBuilder();
                    buffer.append("您的订单已转至")
                            .append(originTableBasicDO.getAreaName())
                            .append("-")
                            .append(originTableBasicDO.getTableCode())
                            .append("桌台，请使用")
                            .append(newTableBasicDO.getAreaName())
                            .append("-")
                            .append(newTableBasicDO.getTableCode())
                            .append("桌台的PAD进行点餐，继续使用将产生新的订单");
                    TurnMessageDTO dto = new TurnMessageDTO();
                    dto.setContent(buffer.toString());
                    dto.setRedisKey(redisKey);
                    turnMessageDTO.setContent(JacksonUtils.writeValueAsString(dto));
                    turnMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + device.getDeviceNo());
                }

                // 被动转台
                if (Objects.equals(newTableGuid, device.getTableGuid())) {

                    turnMessageDTO.setSubject(BusinessMsgTypeEnum.TRANSFER_TABLE_PASSIVE.getName());
                    turnMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.TRANSFER_TABLE_PASSIVE.getId());

                    TurnMessageDTO messageDTO = new TurnMessageDTO();
                    messageDTO.setRedisKey(redisKey);

                    messageDTO.setOrderGuid(orderGuid);
                    OrderDTO orderDTO = tradeRpcClient.findByOrderGuid(orderGuid);
                    messageDTO.setActualGuestsNo(orderDTO.getGuestCount());
                    turnMessageDTO.setContent(JacksonUtils.writeValueAsString(messageDTO));
                    turnMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + device.getDeviceNo());
                }

                log.info("转台 turnMessageDTO={}", JacksonUtils.writeValueAsString(turnMessageDTO));
                bizMsgRpcClient.sendMsg(turnMessageDTO);
            });
        }
    }

    /**
     * 非PAD关台后推送消息给pad
     *
     * @param storeGuid 门店guid
     * @param storeName 门店名称
     * @param tableGuid 桌台guid
     */
    @Override
    public void sendCloseMsg(String storeGuid, String storeName, String tableGuid) {
        BusinessMessageDTO closeMessageDTO = new BusinessMessageDTO();
        closeMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        closeMessageDTO.setPlatform("2");
        closeMessageDTO.setStoreGuid(storeGuid);
        closeMessageDTO.setStoreName(storeName);

        // 非PAD关台消息
        closeMessageDTO.setSubject(BusinessMsgTypeEnum.NON_PAD_SHUTDOWN.getName());
        closeMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.NON_PAD_SHUTDOWN.getId());

        closeMessageDTO.setContent("当前桌台已清台");

        // 查询门店桌台对应设备信息
        PadOrderTypeReqDTO reqDTO = new PadOrderTypeReqDTO();
        reqDTO.setStoreGuid(storeGuid);
        reqDTO.setTableGuid(tableGuid);
        StoreDeviceDTO storeDeviceDTO = organizationClientService.queryDeviceByStoreTable(reqDTO);
        if (ObjectUtils.isEmpty(storeDeviceDTO)) {
            log.warn("未查询到门店桌台对应设备信息 reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
            return;
        }
        closeMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + storeDeviceDTO.getDeviceNo());

        log.info("非PAD关台 closeMessageDTO={}", JacksonUtils.writeValueAsString(closeMessageDTO));
        bizMsgRpcClient.sendMsg(closeMessageDTO);
    }

    /**
     * pad拆台发送消息给所有设备
     *
     * @param tableOrderCombineDTO 拆台信息
     */
    @Override
    public void sendPadMsg(TableOrderCombineDTO tableOrderCombineDTO, List<String> tableToNotify) {
        BusinessMessageDTO separateMessageDTO = new BusinessMessageDTO();
        separateMessageDTO.setSubject(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getName());
        separateMessageDTO.setContent("一体机拆台消息");
        separateMessageDTO.setMessageType(BusinessMsgTypeEnum.PAD_MESSAGE.getId());
        separateMessageDTO.setDetailMessageType(BusinessMsgTypeEnum.ORDER_CHANGED_MSG_TYPE.getId());
        separateMessageDTO.setPlatform("2");
        separateMessageDTO.setStoreGuid(tableOrderCombineDTO.getStoreGuid());
        separateMessageDTO.setStoreName(tableOrderCombineDTO.getStoreName());

        // 查询并台所有设备号
        PadOrderTypeReqDTO reqDTO = new PadOrderTypeReqDTO();
        reqDTO.setStoreGuid(tableOrderCombineDTO.getStoreGuid());
        List<String> tableGuidList = new ArrayList<>();
        tableGuidList.add(tableOrderCombineDTO.getMainTableGuid());
        tableGuidList.addAll(tableToNotify);
        reqDTO.setTableGuidList(tableGuidList);
        List<StoreDeviceDTO> storeDeviceDTOList = organizationClientService.listDeviceByStoreTable(reqDTO);
        if (CollectionUtils.isEmpty(storeDeviceDTOList)) {
            log.error("门店桌台对应设备信息列表为空 reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
            return;
        }
        storeDeviceDTOList.forEach(device -> {
            separateMessageDTO.setMessageTypeStr(BusinessMsgTypeEnum.PAD_MESSAGE.getId() + ":" + device.getDeviceNo());
            log.info("拆台 separateMessageDTO={}", JacksonUtils.writeValueAsString(separateMessageDTO));
            bizMsgRpcClient.sendMsg(separateMessageDTO);
        });
    }
}
