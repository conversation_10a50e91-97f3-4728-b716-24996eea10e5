package com.holderzone.holder.saas.store.pay.service;

import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.AggRefundPollingRespDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RedisService
 * @date 2019/03/19 18:17
 * @description
 * @program holder-saas-store-trading-center
 */
public interface RedisService {

    void putPollingResp(String orderGuid, String payGuid, AggPayPollingRespDTO pollingRespDTO);

    Mono<AggPayPollingRespDTO> getPollingResp(SaasPollingDTO saasPollingDTO);

    void putRefundPollingResp(String orderGuid, String payGuid, AggRefundPollingRespDTO aggRefundPollingRespDTO);

    void putCallBackResp(String orderGuid, String payGuid, String state);

    Mono<String> getCallBackResp(String orderGuid, String payGuid);

    Mono<AggRefundPollingRespDTO> getRefundPollingResp(SaasPollingDTO saasPollingDTO);

    String getQrCodeDownloadUrl(String orderGUID);

    void putQrCodeDownloadUrl(String orderGUID, String downLoadUrl);
}
