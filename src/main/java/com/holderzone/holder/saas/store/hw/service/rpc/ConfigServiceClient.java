package com.holderzone.holder.saas.store.hw.service.rpc;

import com.holderzone.saas.store.dto.config.req.ConfigReverseQueryDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableServiceClient
 * @date 2019/01/07 10:22
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(value = "holder-saas-store-config", fallbackFactory = ConfigServiceClient.FallBack.class)
public interface ConfigServiceClient {

    @PostMapping("/config/get_reverse_config")
    ConfigRespDTO getReservePhone(ConfigReverseQueryDTO configReqDTO);

    @Slf4j
    @Component
    class FallBack implements FallbackFactory<ConfigServiceClient> {
        @Override
        public ConfigServiceClient create(Throwable throwable) {
            return new ConfigServiceClient() {

                @Override
                public ConfigRespDTO getReservePhone(ConfigReverseQueryDTO configReqDTO) {
                    log.error("查询预定云呼号码异常：{}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }
}
