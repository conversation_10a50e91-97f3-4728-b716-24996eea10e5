package com.holderzone.holder.saas.store.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.holderzone.holder.saas.store.config.utils.SpringContextUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;

@EnableEurekaClient
@EnableFeignClients
@SpringBootApplication
@EnableApolloConfig
public class HolderSaasStoreConfigApplication {

	public static void main(String[] args) {
		ConfigurableApplicationContext app = SpringApplication.run(HolderSaasStoreConfigApplication.class, args);
		/**
		 * 设置Spring容器上下文
		 */
		SpringContextUtils.getInstance().setCfgContext(app);
	}
}
