package com.holderzone.holder.saas.store.mdm.pipeline.staff.agg;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.utils.ServerCodeIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.exception.RepeatedException;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.UserSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.domain.UserDO;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.feign.UserClientService;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.mapstruct.UserMapstruct;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.service.UserService;
import com.holderzone.holder.saas.store.mdm.service.MdmOperation;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import com.holderzone.holder.saas.store.mdm.util.RandomCodeUtils;
import com.holderzone.holder.saas.store.mdm.util.SplitUtils;
import com.holderzone.holder.saas.store.mdm.util.TriggerLogUtils;
import com.holderzone.resource.common.dto.mq.UnMessage;
import com.holderzone.resource.common.dto.user.UserDTO;
import com.holderzone.resource.common.util.MessageType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.holderzone.holder.saas.store.mdm.constant.ReqTypeConstant.*;
import static com.holderzone.holder.saas.store.mdm.constant.ReqUrlConstants.User.BATCH_CREATE_USER_URL;
import static com.holderzone.holder.saas.store.mdm.constant.ReqUrlConstants.User.USER_URL;

@Slf4j
@Service
public class UserAggServiceImpl implements UserAggService {

    private final UserService userService;

    private final MdmOperation mdmOperation;

    private final MqUtils mqUtils;

    private final UserClientService userClientService;

    private final UserMapstruct userMapstruct;

    @Value("${mdm.initSyncStep:50}")
    private int initSyncStep;

    @Autowired
    public UserAggServiceImpl(UserService userService, MdmOperation mdmOperation,
                              MqUtils mqUtils, UserClientService userClientService, UserMapstruct userMapstruct) {
        this.userService = userService;
        this.mdmOperation = mdmOperation;
        this.mqUtils = mqUtils;
        this.userClientService = userClientService;
        this.userMapstruct = userMapstruct;
    }

    @Override
    public void createLocalUser(UserSyncDTO userSyncDTO) {
        if (StringUtils.isNotBlank(userSyncDTO.getAccount()) && userSyncDTO.getAccount().length() > 6) {
            log.info("不同步erp！");
            return;
        }
        UserDO one = userService.getLocalRecord(new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userSyncDTO.getGuid()));
        if (one != null) {
            log.info("第三方创建用户信息同步，用户数据已存在");
            log.info("第三方数据：{}", JacksonUtils.writeValueAsString(userSyncDTO));
            log.info("本地数据：{}", JacksonUtils.writeValueAsString(one));
            return;
        }
        UserDO userDO = userService.insertUser(userSyncDTO);

        uploadUser(userDO, MessageType.ADD.code());
    }

    @Override
    public void updateLocalUser(UserSyncDTO userSyncDTO) {
        if (StringUtils.isNotBlank(userSyncDTO.getAccount()) && userSyncDTO.getAccount().length() > 6) {
            log.info("不同步erp！");
            return;
        }
        if (Objects.equals("100000", userSyncDTO.getAccount())) {
            log.info("默认管理员不允许修改！");
            return;
        }
        UserDO one = userService.getLocalRecord(new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userSyncDTO.getGuid()));
        if (one == null) {
            log.info("第三方修改用户信息同步，用户信息不存在，第三方数据：{}", JacksonUtils.writeValueAsString(userSyncDTO));
            UserDO userDO = userService.insertUser(userSyncDTO);
            uploadUser(userDO, MessageType.ADD.code());
            return;
        }
        userService.updateUserByGuid(userSyncDTO);
        UserDO userDO = userService.getLocalRecord(new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userSyncDTO.getGuid()));
        uploadUser(userDO, MessageType.UPDATE.code());
    }

    @Override
    public void deleteLocalUser(UserSyncDTO userSyncDTO) {
        if (StringUtils.isNotBlank(userSyncDTO.getAccount()) && userSyncDTO.getAccount().length() > 6) {
            log.info("不同步erp！");
            return;
        }
        if (Objects.equals("100000", userSyncDTO.getAccount())) {
            log.info("默认管理员不允许删除！");
            return;
        }
        UserDO one = userService.getLocalRecord(new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userSyncDTO.getGuid()));
        if (one == null) {
            log.info("第三方修改用户信息同步，用户信息不存在，第三方数据：{}", JacksonUtils.writeValueAsString(userSyncDTO));
            return;
        }
        userService.deleteUserByGuid(userSyncDTO.getGuid());

        // 删除员工后 下游数据操作
        userClientService.deleteUserDownStreamOp(userSyncDTO.getGuid());
    }

    @Override
    public void triggerRemoteUser(MdmTriggerType mdmTriggerType) {
        List<UserSyncDTO> userSyncDTOList = userService.shouldInitUserList()
                .stream()
                .filter(userDO -> !userDO.getIsDeleted())
                .map(userMapstruct::do2DTO)
                .collect(Collectors.toList());

        TriggerLogUtils.pre("用户", userSyncDTOList.size());

        switch (mdmTriggerType) {
            case CREATE:
                SplitUtils.splitList(userSyncDTOList, initSyncStep).forEach(user -> {
                    try {
                        createRemoteUser(user);
                    } catch (Exception e) {
                        TriggerLogUtils.stepFailed("用户", e);
                        mqUtils.sendMessage(
                                RocketMqConfig.StoreConfig.STORE_MDM_USER_TOPIC,
                                RocketMqConfig.StoreConfig.STORE_MDM_USER_CREATE_TAG,
                                user, UserContextUtils.getEnterpriseGuid()
                        );
                    }
                    TriggerLogUtils.process("用户", user.size());
                });
                break;
            case UPDATE:
                userSyncDTOList.forEach(user -> {
                    try {
                        updateRemoteUser(user);
                    } catch (Exception e) {
                        TriggerLogUtils.singleFailed("用户", e);
                        mqUtils.sendMessage(
                                RocketMqConfig.StoreConfig.STORE_MDM_USER_TOPIC,
                                RocketMqConfig.StoreConfig.STORE_MDM_USER_UPDATE_TAG,
                                user, UserContextUtils.getEnterpriseGuid()
                        );
                    }
                });
                TriggerLogUtils.process("用户", userSyncDTOList.size());
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + mdmTriggerType);
        }

        TriggerLogUtils.post("用户", userSyncDTOList.size());
    }

    @Override
    public void createRemoteUser(List<UserSyncDTO> userSyncDTOList) {
        userSyncDTOList = userSyncDTOList.stream().filter(e -> StringUtils.isNotBlank(e.getAccount()) && e.getAccount().length() <= 6)
                .collect(Collectors.toList());
        try {
            mdmOperation.doRequest(POST, BATCH_CREATE_USER_URL, userSyncDTOList);
        } catch (RepeatedException e) {
            if (userSyncDTOList.size() == 1) {
                TriggerLogUtils.singleRepeated("用户");
            } else {
                TriggerLogUtils.batchRepeated("用户", e);
                for (UserSyncDTO userSyncDTO : userSyncDTOList) {
                    mqUtils.sendMessage(
                            RocketMqConfig.StoreConfig.STORE_MDM_USER_TOPIC,
                            RocketMqConfig.StoreConfig.STORE_MDM_USER_CREATE_TAG,
                            Collections.singleton(userSyncDTO), UserContextUtils.getEnterpriseGuid()
                    );
                }
            }
        } catch (BusinessException e) {
            if (userSyncDTOList.size() == 1) {
                throw e;
            } else {
                TriggerLogUtils.batchFailed("用户", e);
                for (UserSyncDTO userSyncDTO : userSyncDTOList) {
                    mqUtils.sendMessage(
                            RocketMqConfig.StoreConfig.STORE_MDM_USER_TOPIC,
                            RocketMqConfig.StoreConfig.STORE_MDM_USER_CREATE_TAG,
                            Collections.singleton(userSyncDTO), UserContextUtils.getEnterpriseGuid()
                    );
                }
            }
        }
    }

    @Override
    public void updateRemoteUser(UserSyncDTO userSyncDTO) {
        if (StringUtils.isNotBlank(userSyncDTO.getAccount()) && userSyncDTO.getAccount().length() > 6) {
            log.info("不同步erp！");
            return;
        }
        mdmOperation.doRequest(PUT, USER_URL, userSyncDTO);
    }

    @Override
    public void deleteRemoteUser(List<String> guidList) {
        List<UserSyncDTO> collect = guidList.stream()
                .map(guid -> {
                    UserSyncDTO userSyncDTO = new UserSyncDTO();
                    userSyncDTO.setGuid(guid);
                    return userSyncDTO;
                })
                .collect(Collectors.toList());
        mdmOperation.doRequest(DELETE, USER_URL, collect);
    }

    private void uploadUser(UserDO mdmUserSynDTO, String typeCode) {
        // userGuid, newAccount, name ,password, newAuthCode, tel, isEnabled, enterpriseGuid, merchantNo 必传
        UserDTO userDTO = new UserDTO();
        userDTO.setUserGuid(mdmUserSynDTO.getGuid());
        userDTO.setAccount(mdmUserSynDTO.getAccount());
        userDTO.setName(mdmUserSynDTO.getName());
        userDTO.setPassword(mdmUserSynDTO.getPassword());
        userDTO.setTel(mdmUserSynDTO.getPhone());
        // 生成可用授权码
        ServerCodeIdentifier.setServerCode("holder_saas_store_staff");
        Predicate<String> authCodePredicate =
                authCode -> userService.count(new LambdaQueryWrapper<UserDO>().eq(UserDO::getAuthCode, authCode)) == 0;
        Integer authCode = RandomCodeUtils.generate(authCodePredicate, 3);
        userDTO.setAuthCode(Optional.ofNullable(authCode).map(String::valueOf).orElse(null));
        userDTO.setIsEnabled(mdmUserSynDTO.getIsEnable() ? "1" : "0");
        userDTO.setIsDeleted("0");
        userDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        userDTO.setMerchantNo(mdmUserSynDTO.getEnterpriseNo());
        userDTO.setCreateStaffGuid(UserContextUtils.getUserGuid());
        userDTO.setUpdateStaffGuid(UserContextUtils.getUserGuid());
        userDTO.setUpdateStaffName(UserContextUtils.getUserName());
        userDTO.setUpdateStaffAccount(UserContextUtils.getUserAccount());
        UnMessage<UserDTO> unMessage = new UnMessage<>();
        unMessage.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        unMessage.setStoreGuid(UserContextUtils.getStoreGuid());
        unMessage.setMessageType(typeCode);
        unMessage.setMessage(userDTO);
        log.info("MDM用户{}操作同步至云端，消息内容：{}",
                MessageTypeEnum.getNameByCode(typeCode),
                JacksonUtils.writeValueAsString(unMessage));
        mqUtils.sendMessage(RocketMqConfig.CloudConfig.USER_SYNC_TOPIC,
                RocketMqConfig.CloudConfig.USER_SYNC_UPLOAD_TAG,
                unMessage,
                UserContextUtils.getEnterpriseGuid());
        log.info("同步至云端消息发送成功");
    }

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    private enum MessageTypeEnum {
        ADD("1", "新增"),
        UPDATE("0", "修改"),
        DELETE("-1", "删除"),
        OTHER("2", "未知");

        private String code;

        private String name;

        public static String getNameByCode(String code) {
            return Arrays.stream(MessageTypeEnum.values())
                    .filter(o -> Objects.equals(o.getCode(), code))
                    .findFirst().orElse(OTHER).getName();
        }
    }
}
