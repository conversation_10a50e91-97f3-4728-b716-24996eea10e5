package com.holderzone.holder.saas.store.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 预定明细 导出
 */
@Data
public class ReserveExcelDTO implements Serializable {

    private static final long serialVersionUID = -1667368987764335434L;

    @Excel(name = "预定时间", orderNum = "1", width = 25)
    private String reserveStartTime;

    @Excel(name = "预定门店", orderNum = "2", width = 20)
    private String storeName;

    @Excel(name = "姓名", orderNum = "3", width = 20)
    private String name;

    @Excel(name = "手机号", orderNum = "4", width = 15)
    private String phone;

    @Excel(name = "人数", orderNum = "5", width = 10)
    private String numberStr;

    @Excel(name = "预定区域", orderNum = "6", width = 15)
    private String areaName;

    @Excel(name = "定金", orderNum = "7", width = 10)
    private String reserveAmount;

    @Excel(name = "状态", orderNum = "8", width = 15)
    private String stateName;

    @Excel(name = "来源", orderNum = "9", width = 15)
    private String deviceTypeName;

    @Excel(name = "需求备注", orderNum = "10", width = 20)
    private String remark;

    @Excel(name = "下单时间", orderNum = "11", width = 25)
    private String gmtCreate;

    @Excel(name = "支付方式", orderNum = "12", width = 15)
    private String paymentTypeName;

    @Excel(name = "预付金回退", orderNum = "13", width = 10)
    private String refundAmount;
}
