package com.holderzone.holder.saas.store.mdm.pipeline.item.outputs;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.event.AbsErpRocketMqConsumer;
import com.holderzone.holder.saas.store.mdm.pipeline.item.agg.SkuAggService;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.SkuSyncDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.StoreConfig.STORE_MDM_SKU_TOPIC,
        tags = {
                RocketMqConfig.StoreConfig.STORE_MDM_SKU_INIT_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_SKU_CREATE_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_SKU_UPDATE_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_SKU_DELETE_TAG
        },
        consumerGroup = RocketMqConfig.StoreConfig.STORE_MDM_SKU_GROUP)
public class SkuCanalConsumer extends AbsErpRocketMqConsumer<RocketMqTopic, String> {

    private final SkuAggService skuAggService;

    @Autowired
    public SkuCanalConsumer(SkuAggService skuAggService) {
        this.skuAggService = skuAggService;
    }

    @Override
    public boolean doConsumeMsg(String json, MessageExt messageExt) {
        String tags = messageExt.getTags();
        switch (tags) {
            case RocketMqConfig.StoreConfig.STORE_MDM_SKU_INIT_TAG:
                skuAggService.triggerRemoteSku(MdmTriggerType.ofType(json));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_SKU_CREATE_TAG:
                skuAggService.createRemoteSku(JacksonUtils.toObjectList(SkuSyncDTO.class,json));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_SKU_UPDATE_TAG:
                skuAggService.updateRemoteSku(JacksonUtils.toObject(SkuSyncDTO.class, json));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_SKU_DELETE_TAG:
                skuAggService.deleteRemoteSku(JacksonUtils.toObjectList(SkuSyncDTO.class,json));
                break;
            default:
                log.error("unknown mq tag : {}, message：{}",
                        messageExt.getTags(),
                        JacksonUtils.writeValueAsString(json));
                break;
        }
        return true;
    }
}
