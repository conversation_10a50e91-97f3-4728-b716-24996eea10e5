package com.holderzone.holder.saas.store.message.config;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RocketMqConfig
 * @date 2018/09/04 17:15
 * @description
 * @program holder-saas-bussiness-message
 */
public class RocketMqConfig {

    public static final String SYSTEM_MSG_TOPIC = "system-message-topic";

    public static final String SYSTEM_MSG_TAG = "system-message-tag";

    public static final String SYSTEM_MSG_CONSUMER ="system-message-consumer-group";

    public static final String BUSINESS_MSG_TOPIC = "business-message-topic";

    public static final String BUSINESS_MSG_TAG = "business-message-tag";

    public static final String BUSINESS_MSG_CONSUMER ="business-message-consumer-group";
}
