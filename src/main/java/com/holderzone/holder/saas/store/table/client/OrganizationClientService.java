package com.holderzone.holder.saas.store.table.client;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.organization.BusinessDateReqDTO;
import com.holderzone.saas.store.dto.organization.PadOrderTypeReqDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrganizationClientService
 * @date 2021/9/23 18:03
 * @description 组织服务
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = OrganizationClientService.OrganizationFullback.class)
public interface OrganizationClientService {

    /**
     * 查询门店桌台对应设备信息
     *
     * @param padOrderTypeReqDTO 门店guid，桌台guid
     * @return 设备信息
     */
    @ApiOperation(value = "查询门店桌台对应设备信息")
    @PostMapping(value = "/device/query_device_by_store_table")
    StoreDeviceDTO queryDeviceByStoreTable(@RequestBody PadOrderTypeReqDTO padOrderTypeReqDTO);

    /**
     * 查询门店桌台对应设备信息列表
     *
     * @param padOrderTypeReqDTO 门店guid，桌台guidList
     * @return 设备信息列表
     */
    @ApiOperation(value = "查询门店桌台对应设备信息列表")
    @PostMapping(value = "/device/list_device_by_store_table")
    List<StoreDeviceDTO> listDeviceByStoreTable(@RequestBody PadOrderTypeReqDTO padOrderTypeReqDTO);

    /**
     * 根据门店guid查询门店详细信息
     *
     * @param storeGuid 门店guid
     * @return StoreDTO
     */
    @ApiOperation("根据门店guid查询门店详细信息")
    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    /**
     * 根据门店guid集合及日期时间获取所属营业日期
     */
    @PostMapping("/store/query_business_day")
    LocalDate queryBusinessDay(@RequestBody BusinessDateReqDTO businessDateReqDTO);

    @Component
    @Slf4j
    class OrganizationFullback implements FallbackFactory<OrganizationClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OrganizationClientService create(Throwable throwable) {
            return new OrganizationClientService() {
                @Override
                public StoreDeviceDTO queryDeviceByStoreTable(PadOrderTypeReqDTO padOrderTypeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryDeviceByStoreTable", JacksonUtils.writeValueAsString(padOrderTypeReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询门店桌台对应设备信息接口熔断");
                }

                @Override
                public List<StoreDeviceDTO> listDeviceByStoreTable(PadOrderTypeReqDTO padOrderTypeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "listDeviceByStoreTable", JacksonUtils.writeValueAsString(padOrderTypeReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("查询门店桌台对应设备信息列表接口熔断");
                }

                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryStoreByGuid", storeGuid,
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("根据门店guid查询门店详细信息接口熔断");
                }

                @Override
                public LocalDate queryBusinessDay(BusinessDateReqDTO businessDateReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryBusinessDay", JacksonUtils.writeValueAsString(businessDateReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new BusinessException("根据门店guid集合及日期时间获取所属营业日期接口熔断");
                }
            };
        }
    }
}
