package com.holderzone.holder.saas.store.mdm.pipeline.item.outputs;

import com.alibaba.otter.canal.protocol.CanalEntry;
import com.holderzone.framework.canal.starter.core.CanalMsg;
import com.holderzone.framework.canal.starter.extension.RowChangeBody;
import com.holderzone.framework.canal.starter.point.CanalListenerHandler;
import com.holderzone.framework.canal.starter.point.anno.ddl.AlertTableListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.DeleteListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.InsertListenPoint;
import com.holderzone.framework.canal.starter.point.anno.dml.UpdateListenPoint;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.config.SyncConfig;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.ItemSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.ItemDO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.mapstruct.ItemMapStruct;
import com.holderzone.holder.saas.store.mdm.util.DataConvertUtils;
import com.holderzone.holder.saas.store.mdm.util.ErpUtils;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CanalListenerHandler
public class ItemCanalListener {

    private final MqUtils mqUtils;

    private final ItemMapStruct itemMapStruct;

    private final SyncConfig syncConfig;

    @Autowired
    public ItemCanalListener(MqUtils mqUtils, ItemMapStruct itemMapStruct, SyncConfig syncConfig) {
        this.mqUtils = mqUtils;
        this.itemMapStruct = itemMapStruct;
        this.syncConfig = syncConfig;
    }

    @LogBefore(value = "内部系统创建商品", logLevel = LogLevel.INFO)
    @AlertTableListenPoint(schema = "hsi_item_*_db", table = "hsi_item")
    public void onEventAlertTable(CanalMsg canalMsg, CanalEntry.RowChange rowChange) {
        if (syncConfig.shouldInitAgain(rowChange.getSql())) {
            mqUtils.sendMessage(
                    RocketMqConfig.StoreConfig.STORE_MDM_ITEM_TOPIC,
                    RocketMqConfig.StoreConfig.STORE_MDM_ITEM_INIT_TAG,
                    ErpUtils.getErpGuid(canalMsg), ErpUtils.getErpGuid(canalMsg)
            );
        }
    }

    @LogBefore(value = "内部系统创建商品", logLevel = LogLevel.INFO)
    @InsertListenPoint(schema = "hsi_item_*_db", table = "hsi_item")
    public void onEventInsertData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        List<ItemSyncDTO> afterDataDTO = filterNonPkg(getAfterDataDTO(rowChangeBody));
        if (CollectionUtils.isEmpty(afterDataDTO)) {
            return;
        }
        mqUtils.sendMessage(
                RocketMqConfig.StoreConfig.STORE_MDM_ITEM_TOPIC,
                RocketMqConfig.StoreConfig.STORE_MDM_ITEM_CREATE_TAG,
                afterDataDTO, ErpUtils.getErpGuid(canalMsg)
        );
    }

    @LogBefore(value = "内部系统更新或删除商品", logLevel = LogLevel.INFO)
    @UpdateListenPoint(schema = "hsi_item_*_db", table = "hsi_item")
    public void onEventUpdateData(CanalMsg canalMsg, RowChangeBody rowChangeBody) {
        Map<Integer, List<ItemSyncDTO>> listMap = getAfterDatatoMap(rowChangeBody);
        // 删除商品
        List<ItemSyncDTO> itemSyncToDelete = listMap.get(1);
        if (null != itemSyncToDelete) {
            List<ItemSyncDTO> nonPkgItemToDelete = filterNonPkg(itemSyncToDelete);
            if (!nonPkgItemToDelete.isEmpty()) {
                mqUtils.sendMessage(
                        RocketMqConfig.StoreConfig.STORE_MDM_ITEM_TOPIC,
                        RocketMqConfig.StoreConfig.STORE_MDM_ITEM_DELETE_TAG,
                        nonPkgItemToDelete, ErpUtils.getErpGuid(canalMsg)
                );
            }
        }
        // 修改商品
        List<ItemSyncDTO> itemSyncToUpdate = listMap.get(0);
        if (null != itemSyncToUpdate) {
            List<ItemSyncDTO> nonPkgItemToUpdate = filterNonPkg(itemSyncToUpdate);
            if (!nonPkgItemToUpdate.isEmpty()) {
                nonPkgItemToUpdate.forEach(o -> {
                    mqUtils.sendMessage(
                            RocketMqConfig.StoreConfig.STORE_MDM_ITEM_TOPIC,
                            RocketMqConfig.StoreConfig.STORE_MDM_ITEM_UPDATE_TAG,
                            o, ErpUtils.getErpGuid(canalMsg)
                    );
                });
            }
        }
    }

    @LogBefore(value = "内部系统删除商品", logLevel = LogLevel.INFO)
    @DeleteListenPoint(schema = "hsi_item_*_db", table = "hsi_item")
    public void onEventDeleteData(RowChangeBody rowChangeBody, CanalMsg canalMsg) {
        List<String> list = getBeforeGuid(rowChangeBody);
        if (CollectionUtils.isEmpty(list)) return;
        List<ItemSyncDTO> itemSyncDTOList = list.stream()
                .map(s -> new ItemSyncDTO().setGuid(s))
                .collect(Collectors.toList());
        mqUtils.sendMessage(
                RocketMqConfig.StoreConfig.STORE_MDM_ITEM_TOPIC,
                RocketMqConfig.StoreConfig.STORE_MDM_ITEM_DELETE_TAG,
                itemSyncDTOList, ErpUtils.getErpGuid(canalMsg)
        );
    }

    private List<ItemSyncDTO> getAfterDataDTO(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterDataDTO(rowChangeBody, ItemDO.class, itemMapStruct::itemDo2itemSynDTO);
    }

    private Map<Integer, List<ItemSyncDTO>> getAfterDatatoMap(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getAfterDataDtoMap(rowChangeBody, ItemDO.class, ItemDO::getIsDelete, itemMapStruct::itemDo2itemSynDTO);
    }

    private List<String> getBeforeGuid(RowChangeBody rowChangeBody) {
        return DataConvertUtils.getBeforeGuid(rowChangeBody, map -> map.get("guid"));
    }

    private List<ItemSyncDTO> filterNonPkg(List<ItemSyncDTO> itemSyncList) {
        return itemSyncList.stream()
                .filter(itemSyncDTO -> itemSyncDTO.getItemType() != 1
                        && itemSyncDTO.getItemType() != 5)
                .collect(Collectors.toList());
    }
}
