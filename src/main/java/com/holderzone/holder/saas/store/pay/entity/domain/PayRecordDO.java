package com.holderzone.holder.saas.store.pay.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("hsp_pay_record")
public class PayRecordDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 门店Guid
     */
    private String storeGuid;

    /**
     * @see PayPowerId
     * 支付功能的id：
     * -1: 会员余额支付
     * 31：支付宝二维码支付(客户扫我们代理商返回给的二维码)
     * 2 ：支付宝条码支付(扫码枪)
     * 3 ：支付宝公众号支付
     * 51：微信二维码支付
     * 9 ：微信条码支付
     * 10：微信公众号支付
     */
    private String payPowerId;

    /**
     * 通道名称
     */
    private String payPowerName;

    /**
     * 通道ID
     */
    private String payChannelId;

    /**
     * 商户自己订单号
     */
    private String orderGuid;

    /**
     * 本次支付唯一标识
     */
    private String payGuid;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 聚合支付平台订单号
     */
    private String orderHolderNo;

    /**
     * 银行订单编号
     */
    private String orderNo;

    /**
     * 订单支付状态：
     * 待支付      0
     * 支付中      1
     * 支付成功    2
     * 支付失败    3
     * 退款        4
     * 已关闭      5
     * 撤销        6
     * 待支付      10
     */
    private String paySt;

    /**
     * 银行交易流水号
     */
    private String bankTransactionId;

    /**
     * 结帐时间
     * yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime gmtTimePaid;

    /**
     * 退款单号
     */
    private String refOrderNo;

    /**
     * 退款时间
     * yyyy-MM-dd HH:mm:ss
     */
    private LocalDateTime gmtRefund;

    /**
     * 手机号
     */
    private String phoneNum;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

}
