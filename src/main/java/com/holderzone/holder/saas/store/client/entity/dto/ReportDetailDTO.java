package com.holderzone.holder.saas.store.client.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("销售、订单、收入明细")
public class ReportDetailDTO {
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("类型：1-销售明细；2-订单明细；3-收款明细")
    private Integer type;
    @ApiModelProperty("门店guid，以逗号的字符串隔开")
    private String storeGuid;
}