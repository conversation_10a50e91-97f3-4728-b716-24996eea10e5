package com.holderzone.holder.saas.store.report.service;


import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.RefundDetailDTO;

public interface TradeRefundService {

    /**
     * 退款报表
     */
    Message<RefundDetailDTO> list(ReportQueryVO query);

    /**
     * 退款导出
     */
    String export(ReportQueryVO query);
}
