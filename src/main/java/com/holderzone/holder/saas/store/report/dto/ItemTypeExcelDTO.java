package com.holderzone.holder.saas.store.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel("商品分类统计导出")
public class ItemTypeExcelDTO implements Serializable {

    private static final long serialVersionUID = 5169386987774638951L;

    @ApiModelProperty(value = "品牌")
    @Excel(name = "品牌", orderNum = "1", width = 15)
    private String brandName;

    @ApiModelProperty(value = "门店")
    @Excel(name = "门店", orderNum = "2", width = 15)
    private String storeName;

    @ApiModelProperty(value = "商品分类")
    @Excel(name = "商品分类", orderNum = "3", width = 15)
    private String goodsCategories;

    @Excel(name = "销售金额", orderNum = "5", width = 15)
    private String actualReceivedPrice;

    @ApiModelProperty(value = "销量")
    @Excel(name = "销量", orderNum = "4", width = 15)
    private String salesVolume;

    @Excel(name = "实付金额", orderNum = "6", width = 15)
    private String discountPrice;

    @Excel(name = "毛利润", orderNum = "7", width = 15)
    private String grossProfitAmount;

    @Excel(name = "销售金额占比", orderNum = "8", width = 15)
    private String chosenRate;
}
