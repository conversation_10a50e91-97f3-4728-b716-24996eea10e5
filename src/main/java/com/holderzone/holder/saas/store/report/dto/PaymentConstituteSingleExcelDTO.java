package com.holderzone.holder.saas.store.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/2/26
 * @description 收款构成单店导出
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "收款构成单店导出", description = "收款构成单店导出")
public class PaymentConstituteSingleExcelDTO implements Serializable {

    private static final long serialVersionUID = -7493004483385078755L;

    @Excel(name = "品牌", orderNum = "1", width = 15)
    private String brandName;

    @Excel(name = "门店", orderNum = "2", width = 15)
    private String storeName;

    @Excel(name = "收款方式", orderNum = "3", width = 15)
    private String payMethod;

    @Excel(name = "收款方式占比", orderNum = "4", width = 15)
    private String salesRevenueProportion;

    @Excel(name = "销售收入", orderNum = "5", width = 15)
    private String salesRevenue;

    @Excel(name = "会员充值收入", orderNum = "6", width = 15)
    private String memberRecharge;

    @Excel(name = "预订订金", orderNum = "7", width = 15)
    private String deposit;

    @Excel(name = "合计", orderNum = "8", width = 15)
    private String total;

    @Excel(name = "手续费", orderNum = "9", width = 15)
    private String serviceCharge;

    @Excel(name = "预计实收", orderNum = "10", width = 15)
    private String actualIncome;

}
