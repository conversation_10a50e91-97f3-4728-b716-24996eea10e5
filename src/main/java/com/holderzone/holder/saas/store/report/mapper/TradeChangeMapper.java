package com.holderzone.holder.saas.store.report.mapper;

import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ChangeDetailDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 换菜明细
 */
@Mapper
public interface TradeChangeMapper {

    Integer count(@Param("query") ReportQueryVO query);

    List<ChangeDetailDTO> pageInfo(@Param("query") ReportQueryVO query);

}
