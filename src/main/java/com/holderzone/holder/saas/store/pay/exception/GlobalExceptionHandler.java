package com.holderzone.holder.saas.store.pay.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className GlobalExceptionHandler
 * @date 2019/01/04 8:53
 * @description 全局异常处理
 * @program holder-saas-store-trade
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler{

    @ExceptionHandler({MethodArgumentNotValidException.class})
    public ResponseEntity<String> requestValueError(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream().map(fieldError ->
            String.format("%s:%s", fieldError.getField(), fieldError.getDefaultMessage())
        ).collect(Collectors.joining(","));
        log.error("本地参数错误：{}", message);
        return new ResponseEntity(StringUtils.hasText(message) ? message : "参数错误", HttpStatus.PRECONDITION_REQUIRED);
    }

}
