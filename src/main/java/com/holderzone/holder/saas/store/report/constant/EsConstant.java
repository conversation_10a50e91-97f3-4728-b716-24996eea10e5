package com.holderzone.holder.saas.store.report.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EsConstant
 * @date 2019/05/31 10:33
 * @description es检索字段名定义
 * @program holder-saas-store-report
 */
public interface EsConstant {

    /**
     * 索引名
     */

    String HST_ORDER = "hst_order";

    String HST_ORDER_ITEM = "hst_order_item";

    String HST_ORDER_ITEM_RECORD = "hst_order_item_record";

    String HST_ITEM_ATTR = "hst_item_attr";

    String HSI_TYPE = "hsi_type";

    String HST_DISCOUNT = "hst_discount";

    String HST_TRANSACTION_RECORD = "hst_transaction_record";

    String HST_RETAIL_ORDER = "hst_retail_order";

    String HST_RETAIL_ORDER_ITEM = "hst_retail_order_item";

    String HST_RETAIL_DISCOUNT = "hst_retail_discount";

    String HST_RETAIL_FREE_RETURN_ITEM = "hst_retail_free_return_item";

    String HST_RETAIL_TRANSACTION_RECORD = "hst_retail_transaction_record";

    /***
     * 销售明细最终数据索引
     */
    String HST_SALE_DETAIL = "hst_sale_detail";


    /**
     * es关键字
     */

    String DEFAULT_SEARCHTYPE = "_doc";
    String DEFAULT_SEARCH_TYPE = "query_then_fetch";
    String DEFAULT_SORT_KEY = "_key";
    String DATE_HISTOGRAM_INTERVAL_DAY = "day";
    String KEY_WORD = ".keyword";

    String STAFF_NAME = "staff_name";
    String STAFF_GUID = "staff_guid";

    /**
     * hst_order 字段
     */
    String GUID = "guid";
    String ENTERPRISE_GUID = "enterprise_guid";
    String STORE_GUID = "store_guid";
    String STORE_NAME = "store_name";
    String CHECKOUT_TIME = "checkout_time";
    String GMT_CREATE = "gmt_create";
    String GMT_MODIFIED = "gmt_modified";
    String STATE = "state";
    String IS_DELETE = "is_delete";
    String RECOVERY_TYPE = "recovery_type";
    String ORDER_FEE = "order_fee";
    String ORDER_NO = "order_no";
    String ACTUALLY_PAY_FEE = "actually_pay_fee";
    String GUEST_COUNT = "guest_count";
    String UPPER_STATE = "upper_state";
    String CREATE_STAFF_GUID = "create_staff_guid";
    String CREATE_STAFF_NAME = "create_staff_name";
    String TRADE_MODE = "trade_mode";
    String APPEND_FEE = "append_fee";
    String DEVICE_TYPE = "device_type";
    String PAYMENT_TYPE_NAME = "payment_type_name";

    String CANCEL_STAFF_GUID = "cancel_staff_guid";
    String CANCEL_STAFF_NAME = "cancel_staff_name";
    String CHECKOUT_STAFF_ACCOUNT = "checkout_staff_account";
    String CHECKOUT_STAFF_GUID = "checkout_staff_guid";
    String CANCEL_REASON = "cancel_reason";
    String RECOVERY_STAFF_GUID = "recovery_staff_guid";
    String RECOVERY_STAFF_NAME = "recovery_staff_name";
    String RECOVERY_REASON = "recovery_reason";
    String ORIGINAL_ORDER_GUID = "original_order_guid";
    String[] SOURCE = {"store_guid", "store_name"};
    String[] ORDER_GUID_SOURCE = {"order_guid"};

    String TRADE_TYPE = "trade_type";
    String TRADE_TYPE_GROUP = "tradeTypeGroup";
    String ORDER_TYPE_GROUP = "orderTypeGroup";

    String ORDER_GUID = "order_guid";

    String DISCOUNT = "discount";
    String DISCOUNT_TYPE = "discount_type";
    String DISCOUNT_NAME = "discount_name";
    String DISCOUNT_FEE = "discount_fee";


    String ZONE_OF = "+8";

    String ORDER_TYPE = "order_type";

    String TOTAL_ACTUALLY_PAY_FEE = "totalActuallyPayFee";

    String TOTAL_DISCOUNT_FEE = "total_discount_fee";

    String PARENT_ITEM_GUID = "parent_item_guid";

    String SALE_GROUP = "saleGroup";

    String TOTAL_SALE_AMOUNT = "totalSaleAmount";

    String TOTAL_SALE_COUNT = "totalSaleCount";

    String TOTAL_FREE_COUNT = "totalFreeCount";

    String ITEM_GUID = "item_guid";

    String ITEM_TYPE_GUID = "item_type_guid";

    String ITEM_NAME = "item_name";

    String ITEM_TYPE_NAME = "item_type_name";

    String ITEM_TYPE = "item_type";

    String RETURN_COUNT = "return_count";

    String FREE_COUNT = "free_count";

    String CURRENT_COUNT = "current_count";

    String UNIT = "unit";

    String PRICE = "price";


    /**
     * 商品销量统计相关
     */
    String ITEM_AGG = "itemAgg";

    String ITEM_TOP_HITS = "itemTopHits";

    String SALES_VOLUME_GROUP = "salesVolumeGroup";

    String REFUND_COUNT_GROUP = "refundCountGroup";

    String FREE_COUNT_GROUP = "freeCountGroup";

    String TOTAL_SALES_AMOUNT_GROUP = "totalSalesAmountGroup";

    String SALES_PROPORTION_GROUP = "salesProportionGroup";

    String SPOT_RATE_GROUP = "spotRateGroup";

    /**
     * hst_transaction_record 字段
     */
    String PAY_RECORD_PAID_TIME = "paid_time";
    String PAY_RECORD_PAYMENT_TYPE = "payment_type";
    String PAY_RECORD_PAYMENT_TYPE_NAME = "payment_type_name";
    String PAY_RECORD_STATE = "state";
    String PAY_RECORD_TRADE_TYPE = "trade_type";
    String PAY_RECORD_AMOUNT = "amount";

    /**
     * 何师相关的报表
     */
    String SALE_DETAIL_HESHI_PRODUCT = "sale_detail_heshi_product";
    String ORDER_DETAIL_HESHI_PRODUCT = "order_detail_heshi_product";
    String PROCEEDS_DETAIL_HESHI_PRODUCT = "proceeds_detail_heshi_product";
    String CHECKOUT_TIME_HESHI = "checkout_time";
    String GMT_CREATE_HESHI = "gmt_create";
    String STORE_GUID_HESHI = "store_guid";


}
