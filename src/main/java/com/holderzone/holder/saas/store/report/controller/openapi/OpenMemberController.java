package com.holderzone.holder.saas.store.report.controller.openapi;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.report.service.OpenMemberService;
import com.holderzone.saas.store.dto.report.openapi.MemberFundingDetailLimitRespDTO;
import com.holderzone.saas.store.dto.report.openapi.MemberFundingDetailQueryDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 会员相关报表
 */
@Slf4j
@RestController
@RequestMapping("/openapi/member")
@RequiredArgsConstructor
public class OpenMemberController {

    private final OpenMemberService openMemberService;

    /**
     * 查询资金变动明细
     */
    @PostMapping(value = "/funding_detail")
    public MemberFundingDetailLimitRespDTO queryFundingDetail(@RequestBody MemberFundingDetailQueryDTO queryDTO) {
        log.info("查询会员资金变动明细报表请求入参：{}", JacksonUtils.writeValueAsString(queryDTO));
        return openMemberService.queryFundingDetail(queryDTO);
    }
}
