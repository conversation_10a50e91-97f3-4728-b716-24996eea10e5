package com.holderzone.holder.saas.store.deposit.entity.bo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 可根据寄存单号和客户联系方式进行搜索
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("hse_deposit_remind")
public class RemindDO {

    private static final long serialVersionUID = 8424514095125620853L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 唯一GUID
     */
    private String guid;

    /**
     * storeGuid
     */
    private String storeGuid;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 提前几天发送短信
     */
    private int advanceDays;

    /**
     * 寄存提醒
     */
    private int depositRemind;

    /**
     * 取出商品提醒
     */
    private int getRemind;

    /**
     * 过期提醒
     */
    private int expireRemind;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
