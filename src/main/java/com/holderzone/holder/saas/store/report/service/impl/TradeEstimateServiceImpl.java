package com.holderzone.holder.saas.store.report.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.report.dto.EstimateExcelDTO;
import com.holderzone.holder.saas.store.report.mapper.TradeEstimateMapper;
import com.holderzone.holder.saas.store.report.service.TradeEstimateService;
import com.holderzone.holder.saas.store.report.util.ExcelUtils;
import com.holderzone.saas.store.dto.report.query.EstimateReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.EstimateReportRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.item.CancelEstimateTypeEnum;
import com.holderzone.saas.store.enums.item.EstimateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * desc
 *
 * <AUTHOR>
 * @date 2025/7/16
 * @since 1.8
 */
@Slf4j
@Service
public class TradeEstimateServiceImpl implements TradeEstimateService {

    private static final String YMD_FORMAT = "yyyy-MM-dd";

    private static final String YMD_HMS_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private final TradeEstimateMapper tradeEstimateMapper;

    private final OssClient ossClient;

    @Autowired
    public TradeEstimateServiceImpl(TradeEstimateMapper tradeEstimateMapper, OssClient ossClient) {
        this.tradeEstimateMapper = tradeEstimateMapper;
        this.ossClient = ossClient;
    }

    @Override
    public Page<EstimateReportRespDTO> page(EstimateReportQueryVO query) {
        Long total = tradeEstimateMapper.count(query);
        if (total == 0) {
            return new Page<>(query.getCurrentPage(), query.getPageSize(), 0, null);
        }
        List<EstimateReportRespDTO> estimateReportGuids = tradeEstimateMapper.pageInfo(query);
        List<String> guidList = estimateReportGuids.stream().map(EstimateReportRespDTO::getGuid).collect(Collectors.toList());
        query.setGuidList(guidList);
        List<EstimateReportRespDTO> estimateReportRespDTOS = tradeEstimateMapper.listEstimate(query);
        handleEstimateReportData(estimateReportGuids, estimateReportRespDTOS);
        return new Page<>(query.getCurrentPage(), query.getPageSize(), total, estimateReportGuids);
    }

    @Override
    public String export(EstimateReportQueryVO query) {
        Long total = tradeEstimateMapper.count(query);
        if (total > 20000) {
            throw new BusinessException("最多只支持2万条数据导出");
        }
        List<EstimateReportRespDTO> estimateReportGuids = tradeEstimateMapper.list(query);
        if (CollectionUtils.isNotEmpty(estimateReportGuids)) {
            List<String> guidList = estimateReportGuids.stream().map(EstimateReportRespDTO::getGuid).collect(Collectors.toList());
            query.setGuidList(guidList);
            List<EstimateReportRespDTO> estimateReportRespDTOS = tradeEstimateMapper.listEstimate(query);
            handleEstimateReportData(estimateReportGuids, estimateReportRespDTOS);
        }

        List<EstimateExcelDTO> excelDTOList = buildEstimateExcelDTOList(estimateReportGuids);
        log.info("估清报表数据：{}", JacksonUtils.writeValueAsString(excelDTOList));
        try {
            String sheetName = "估清报表";
            String fileName = sheetName + LocalDateTime.now().format(DateTimeFormatter.ofPattern(YMD_FORMAT));
            return ExcelUtils.exportExcel(excelDTOList, null, sheetName, EstimateExcelDTO.class, fileName, ossClient);
        } catch (Exception e) {
            throw new BusinessException("导出失败");
        }
    }

    private List<EstimateExcelDTO> buildEstimateExcelDTOList(List<EstimateReportRespDTO> estimateReports) {
        List<EstimateExcelDTO> reserveExcelList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(estimateReports)) {
            return reserveExcelList;
        }
        for (EstimateReportRespDTO estimateReport : estimateReports) {
            EstimateExcelDTO reserveExcelDTO = new EstimateExcelDTO();
            BeanUtils.copyProperties(estimateReport, reserveExcelDTO);
            reserveExcelDTO.setEstimateType(EstimateTypeEnum.getDescByCode(estimateReport.getEstimateType()));
            if (estimateReport.getEstimateTime() != null) {
                reserveExcelDTO.setEstimateTime(estimateReport.getEstimateTime().format(DateTimeFormatter.ofPattern(YMD_HMS_FORMAT)));
            }
            reserveExcelDTO.setCancelEstimateType(CancelEstimateTypeEnum.getDescByCode(estimateReport.getCancelEstimateType()));
            if (estimateReport.getCancelEstimateTime() != null) {
                reserveExcelDTO.setCancelEstimateTime(estimateReport.getCancelEstimateTime().format(DateTimeFormatter.ofPattern(YMD_HMS_FORMAT)));
            }
            reserveExcelList.add(reserveExcelDTO);
        }
        return reserveExcelList;
    }

    private void handleEstimateReportData(List<EstimateReportRespDTO> estimateReportGuids,
                                          List<EstimateReportRespDTO> estimateReportRespDTOS) {
        Map<String, List<EstimateReportRespDTO>> estimateReportRespDTOMap = estimateReportRespDTOS.stream()
                .collect(Collectors.groupingBy(EstimateReportRespDTO::getGuid));
        log.info("handleEstimateReportData estimateReportGuids: {}", JacksonUtils.writeValueAsString(estimateReportGuids));
        log.info("handleEstimateReportData estimateReportRespDTOMap: {}", JacksonUtils.writeValueAsString(estimateReportRespDTOMap));
        for (EstimateReportRespDTO estimateReportGuid : estimateReportGuids) {
            String guid = estimateReportGuid.getGuid();
            List<EstimateReportRespDTO> dtoList = estimateReportRespDTOMap.get(guid);
            Optional<EstimateReportRespDTO> createOplogOptional = dtoList.stream()
                    .filter(dto -> "CREATE".equals(String.valueOf(dto.getOpType()).trim()))
                    .max(Comparator.comparing(EstimateReportRespDTO::getOpTime));
            if (!createOplogOptional.isPresent()) {
                continue;
            }
            EstimateReportRespDTO createOplog = createOplogOptional.get();
            estimateReportGuid.setBrandName(createOplog.getBrandName());
            estimateReportGuid.setEstimateType(createOplog.getEstimateType());
            estimateReportGuid.setStoreName(createOplog.getStoreName());
            estimateReportGuid.setItemName(createOplog.getItemName());
            estimateReportGuid.setEstimateTime(createOplog.getOpTime());
            estimateReportGuid.setEstimateOperatorName(createOplog.getOpLogOperatorName());
            if (createOplog.getOpLogDeviceType() != null) {
                estimateReportGuid.setEstimateDeviceType(BaseDeviceTypeEnum.getDesc(createOplog.getOpLogDeviceType()));
            }

            Optional<EstimateReportRespDTO> schedulingOplogOptional = dtoList.stream()
                    .filter(dto -> "SCHEDULING".equals(String.valueOf(dto.getOpType()).trim()))
                    .max(Comparator.comparing(EstimateReportRespDTO::getOpTime));
            if (schedulingOplogOptional.isPresent()) {
                EstimateReportRespDTO schedulingOplog = schedulingOplogOptional.get();
                estimateReportGuid.setCancelEstimateType(2);
                estimateReportGuid.setCancelEstimateTime(schedulingOplog.getOpTime());
                estimateReportGuid.setCancelEstimateOperatorName(schedulingOplog.getOpLogOperatorName());
                if (schedulingOplog.getOpLogDeviceType() != null) {
                    estimateReportGuid.setCancelEstimateDeviceType(BaseDeviceTypeEnum.getDesc(schedulingOplog.getOpLogDeviceType()));
                }
                handleEstimatedDuration(estimateReportGuid, schedulingOplog);
                continue;
            }

            Optional<EstimateReportRespDTO> batchCancelOplogOptional = dtoList.stream()
                    .filter(dto -> "BATCH_CANCEL".equals(String.valueOf(dto.getOpType()).trim()))
                    .max(Comparator.comparing(EstimateReportRespDTO::getOpTime));
            if (batchCancelOplogOptional.isPresent()) {
                EstimateReportRespDTO batchCancelOplog = batchCancelOplogOptional.get();
                estimateReportGuid.setCancelEstimateType(3);
                estimateReportGuid.setCancelEstimateTime(batchCancelOplog.getOpTime());
                if (batchCancelOplog.getOpLogDeviceType() != null) {
                    estimateReportGuid.setCancelEstimateDeviceType(BaseDeviceTypeEnum.getDesc(batchCancelOplog.getOpLogDeviceType()));
                }
                estimateReportGuid.setCancelEstimateOperatorName(batchCancelOplog.getOpLogOperatorName());
                handleEstimatedDuration(estimateReportGuid, batchCancelOplog);
                continue;
            }

            Optional<EstimateReportRespDTO> removeOplogOptional = dtoList.stream()
                    .filter(dto -> "REMOVE".equals(String.valueOf(dto.getOpType()).trim()))
                    .max(Comparator.comparing(EstimateReportRespDTO::getOpTime));
            if (removeOplogOptional.isPresent()) {
                EstimateReportRespDTO removeOplog = removeOplogOptional.get();
                estimateReportGuid.setCancelEstimateType(3);
                estimateReportGuid.setCancelEstimateTime(removeOplog.getOpTime());
                if (removeOplog.getOpLogDeviceType() != null) {
                    estimateReportGuid.setCancelEstimateDeviceType(BaseDeviceTypeEnum.getDesc(removeOplog.getOpLogDeviceType()));
                }
                estimateReportGuid.setCancelEstimateOperatorName(removeOplog.getOpLogOperatorName());
                handleEstimatedDuration(estimateReportGuid, removeOplog);
                continue;
            }

            estimateReportGuid.setCancelEstimateType(1);
        }
    }

    private void handleEstimatedDuration(EstimateReportRespDTO estimateReportGuid, EstimateReportRespDTO schedulingOplog) {
        LocalDateTime startTime = estimateReportGuid.getEstimateTime();
        LocalDateTime endTime = schedulingOplog.getOpTime();
        if (startTime != null && endTime != null) {
            if (!endTime.isBefore(startTime)) {
                long days = ChronoUnit.DAYS.between(startTime, endTime);
                long hours = ChronoUnit.HOURS.between(startTime.plusDays(days), endTime);
                long minutes = ChronoUnit.MINUTES.between(startTime.plusDays(days).plusHours(hours), endTime);
                String duration = String.format("%d天%d小时%d分", days, hours, minutes);
                estimateReportGuid.setEstimatedDuration(duration);
            } else {
                estimateReportGuid.setEstimatedDuration("0天0小时0分");
            }
        }
    }

}
