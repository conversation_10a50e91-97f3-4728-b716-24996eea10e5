package com.holderzone.holder.saas.store.hw.service.impl;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.hw.exception.*;
import com.holderzone.holder.saas.store.hw.service.ReserveService;
import com.holderzone.holder.saas.store.hw.service.rpc.ConfigServiceClient;
import com.holderzone.holder.saas.store.hw.service.rpc.EnterpriseRpcService;
import com.holderzone.holder.saas.store.hw.service.rpc.OrganizationService;
import com.holderzone.holder.saas.store.hw.service.rpc.TableServiceClient;
import com.holderzone.holder.saas.store.hw.util.RequestMapUtils;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.config.req.ConfigReverseQueryDTO;
import com.holderzone.saas.store.dto.config.resp.ConfigRespDTO;
import com.holderzone.saas.store.dto.hw.*;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.enums.common.ConfigEnum;
import com.holderzone.saas.store.reserve.api.ReserveConfigApi;
import com.holderzone.saas.store.reserve.api.ReserveRecordApi;
import com.holderzone.saas.store.reserve.api.ReserveTableApi;
import com.holderzone.saas.store.reserve.api.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveServiceImpl
 * @date 2019/06/05 14:24
 * @description //TODO
 * @program holder-saas-store-reserve
 */
@Service
public class ReserveServiceImpl implements ReserveService {

    private final ReserveTableApi reserveTableApi;

    private final ReserveConfigApi reserveConfigApi;

    private final ReserveRecordApi reserveRecordApi;

    private final TableServiceClient tableServiceClient;

    private final ConfigServiceClient configServiceClient;

    private final OrganizationService organizationService;

    private final EnterpriseRpcService enterpriseRpcService;

    @Autowired
    public ReserveServiceImpl(ReserveTableApi reserveTableApi,
                              ReserveConfigApi reserveConfigApi,
                              ReserveRecordApi reserveRecordApi,
                              TableServiceClient tableServiceClient,
                              ConfigServiceClient configServiceClient,
                              OrganizationService organizationService,
                              EnterpriseRpcService enterpriseRpcService) {
        this.reserveTableApi = reserveTableApi;
        this.reserveConfigApi = reserveConfigApi;
        this.reserveRecordApi = reserveRecordApi;
        this.tableServiceClient = tableServiceClient;
        this.configServiceClient = configServiceClient;
        this.organizationService = organizationService;
        this.enterpriseRpcService = enterpriseRpcService;
    }

    @Override
    public ResultDTO validateReservePhone(MchntValidateDTO mchntValidateDTO) {
        ConfigRespDTO reserveConfig = resolveReservePhone(mchntValidateDTO.getMerchantPhone());
        ReserveConfigDTO reserveConfigDTO = resolveReserveConfig(resolveDefaultUser(reserveConfig));
        StoreDTO storeDTO = fetchTheStore(reserveConfigDTO.getStoreGuid());
        return new ResultDTO<>("200", storeDTO.getName());
    }

    @Override
    public ResultDTO validateDateReturnInterval(ReserveDateDTO reserveDateDTO) {
        ConfigRespDTO reserveConfig = resolveReservePhone(reserveDateDTO.getMerchantPhone());
        VoiceQueryDTO voiceQueryDTO = RequestMapUtils.of(reserveConfig, reserveDateDTO);
        UserContextUtils.put(resolveDefaultUser(reserveConfig));
        VoiceQueryResultDTO voiceQueryResultDTO = reserveTableApi.queryDate(voiceQueryDTO);
        Boolean isNoonAvailable = voiceQueryResultDTO.getIsNoonAvailable();
        Boolean isNightAvailable = voiceQueryResultDTO.getIsNightAvailable();
        if (!isNoonAvailable && !isNightAvailable) {
            throw new FullIntervalException();
        }
        if (!isNightAvailable) {
            return new ResultDTO<>("200", "中午吗");
        }
        if (!isNoonAvailable) {
            return new ResultDTO<>("201", "晚上吗");
        }
        return new ResultDTO<>("202", "中午还是晚上呢");
    }

    @Override
    public ResultDTO validateIntervalReturnTime(ReserveIntervalDTO reserveIntervalDTO) {
        ConfigRespDTO reserveConfig = resolveReservePhone(reserveIntervalDTO.getMerchantPhone());
        VoiceQueryDTO voiceQueryDTO = RequestMapUtils.of(reserveConfig, reserveIntervalDTO);
        UserContextUtils.put(resolveDefaultUser(reserveConfig));
        VoiceQueryResultDTO voiceQueryResultDTO = reserveTableApi.queryInterval(voiceQueryDTO);
        List<TimingSegmentDTO> timingSegments = voiceQueryResultDTO.getTimingSegments();
        if (CollectionUtils.isEmpty(timingSegments)) {
            throw new FullSegmentException();
        }
        String result = timingSegments.stream()
                .map(TimingSegmentDTO::getStart)
                .map(localTime -> {
                    StringBuilder time = new StringBuilder();
                    int hour = localTime.getHour();
                    time.append(hour > 12 ? hour % 12 : hour).append("点");
                    if (localTime.getMinute() > 0) {
                        time.append("半");
                    }
                    return time.toString();
                })
                .collect(Collectors.joining("、"));
        return new ResultDTO<>("200", result);
    }

    @Override
    public ResultDTO validateDateTimeReturnNothing(ReserveDateTimeDTO reserveDateTimeDTO) {
        ConfigRespDTO reserveConfig = resolveReservePhone(reserveDateTimeDTO.getMerchantPhone());
        VoiceQueryDTO voiceQueryDTO = RequestMapUtils.of(reserveConfig, reserveDateTimeDTO);
        UserContextUtils.put(resolveDefaultUser(reserveConfig));
        VoiceQueryResultDTO voiceQueryResultDTO = reserveTableApi.queryDateTime(voiceQueryDTO);
        if (!voiceQueryResultDTO.getIsDateTimeAvailable()) {
            throw new HwCodeException("400", "时段已满，无法预订");
        }
        return new ResultDTO<>("200", "当前时段可以预订");
    }

    @Override
    public ResultDTO validatePeopleReturnNothing(ReservePeopleDTO reservePeopleDTO) {
        ConfigRespDTO reserveConfig = resolveReservePhone(reservePeopleDTO.getMerchantPhone());
        VoiceQueryDTO voiceQueryDTO = RequestMapUtils.of(reserveConfig, reservePeopleDTO);
        UserContextUtils.put(resolveDefaultUser(reserveConfig));
        VoiceQueryResultDTO voiceQueryResultDTO = reserveTableApi.queryPeople(voiceQueryDTO);
        if (!voiceQueryResultDTO.getIsPeopleTotalAvailable()) {
            throw new FullTableException();
        }
        return new ResultDTO<>("200", "当前时段有满足人数的桌位，可以预订");
    }

    @Override
    public ResultDTO validateRoomReturnRoom(ReserveRoomDTO reserveRoomDTO) {
        ConfigRespDTO reserveConfig = resolveReservePhone(reserveRoomDTO.getMerchantPhone());
        VoiceQueryDTO voiceQueryDTO = RequestMapUtils.of(reserveConfig, reserveRoomDTO);
        UserContextUtils.put(resolveDefaultUser(reserveConfig));
        VoiceQueryResultDTO voiceQueryResultDTO = reserveTableApi.queryRoomType(voiceQueryDTO);
        if (!voiceQueryResultDTO.getIsEnablePrivateRoom()) {
            throw new RoomNotProvidedException();
        }
        if (voiceQueryResultDTO.getIsTableTypeAvailable()) {
            return new ResultDTO<>("200", "有包房");
        }
        return new ResultDTO<>("201", "包房已满");
    }

    @Override
    public ResultDTO validateAllReturnSuccess(ReserveAllDTO reserveAllDTO) {
        ConfigRespDTO reserveConfig = resolveReservePhone(reserveAllDTO.getMerchantPhone());
        VoiceQueryDTO voiceQueryDTO = RequestMapUtils.of(reserveConfig, reserveAllDTO);
        UserContextUtils.put(resolveDefaultUser(reserveConfig));
        VoiceQueryResultDTO voiceQueryResultDTO = reserveTableApi.queryAllCond(voiceQueryDTO);
        if (!voiceQueryResultDTO.getIsReserveAvailable()) {
            throw new ReserveDisableException();
        }
        return new ResultDTO<>("200", "可以预订");
    }

    @Override
    public ReserveRecordDetailDTO launch(ReserveLaunchDTO reserveLaunchDTO) {
        ConfigRespDTO reserveConfig = resolveReservePhone(reserveLaunchDTO.getMerchantPhone());
        VoiceQueryDTO voiceQueryDTO = RequestMapUtils.of(reserveConfig, reserveLaunchDTO);
        UserContextUtils.put(resolveDefaultUser(reserveConfig));
        VoiceQueryResultDTO voiceQueryResultDTO = reserveTableApi.fetchMatchedTable(voiceQueryDTO);
        TableDTO matchedTable = voiceQueryResultDTO.getMatchedTable();
        if (matchedTable == null) {
            throw new ReserveFailedException();
        }
        ReserveRecordDTO recordDTO = new ReserveRecordDTO();
        recordDTO.setGender(Byte.valueOf("1"));
        if (!Pattern.matches("^1[0-9]{10}$", reserveLaunchDTO.getCustomerPhone())) {
            throw new PhoneFormatException();
        }
        recordDTO.setName(reserveLaunchDTO.getCustomerPhone());
        recordDTO.setPhone(reserveLaunchDTO.getCustomerPhone());
        recordDTO.setNumber(reserveLaunchDTO.getPeopleTotal());
        recordDTO.setReserveStartTime(LocalDateTime.of(reserveLaunchDTO.getReserveDate(), reserveLaunchDTO.getReserveTime()));
        recordDTO.setTables(Collections.singletonList(matchedTable));
        recordDTO.setStoreGuid(reserveConfig.getStoreGuid());
        recordDTO.setDeviceId("merchant");
        recordDTO.setDeviceType(0);
        return reserveRecordApi.launch(recordDTO);
    }

    private ConfigRespDTO resolveReservePhone(String merchantPhone) {
        ConfigReverseQueryDTO configReqQueryDTO = new ConfigReverseQueryDTO();
        configReqQueryDTO.setDicCode(ConfigEnum.RESERVE_MERCHANT_PHONE.getCode());
        configReqQueryDTO.setDicValue(merchantPhone);
        ConfigRespDTO reservePhone = configServiceClient.getReservePhone(configReqQueryDTO);
        if (reservePhone == null) {
            throw new ServiceNotProvidedException();
        }
        return reservePhone;
    }

    private ReserveConfigDTO resolveReserveConfig(UserContext userContext) {
        UserContextUtils.put(userContext);
        StoreGuidDTO storeGuidDTO = new StoreGuidDTO();
        storeGuidDTO.setStoreGuid(userContext.getStoreGuid());
        ReserveConfigDTO reserveConfigDTO = reserveConfigApi.query(storeGuidDTO);
        if (reserveConfigDTO == null
                || CollectionUtils.isEmpty(reserveConfigDTO.getSegments())) {
            throw new SegmentNotProvidedException();
        }
        return reserveConfigDTO;
    }

    private StoreDTO fetchTheStore(String storeGuid) {
        int maxTry = 3;
        int curTry = 0;
        StoreDTO storeDTO = null;
        while (storeDTO == null && curTry++ < maxTry) {
            try {
                storeDTO = organizationService.queryStoreByGuid(storeGuid);
            } catch (Exception ignore) {
            }
        }
        if (storeDTO == null) {
            throw new HwCodeException("500", "系统错误，请联系管理员");
        }
        return storeDTO;
    }

    private UserContext resolveDefaultUser(ConfigRespDTO org) {
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid(org.getEnterpriseGuid());
        userContext.setStoreGuid(org.getStoreGuid());
        userContext.setStoreName(org.getStoreGuid());
        userContext.setUserGuid("system");
        userContext.setUserName("system");
        return userContext;
    }
}