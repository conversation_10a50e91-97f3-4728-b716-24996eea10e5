package com.holderzone.holder.saas.store.message.util;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import java.util.Calendar;

/**
 * <AUTHOR>
 */
@Slf4j
public class SnowflakeKeyGeneratorUtil {

    private SnowflakeKeyGeneratorUtil() {
        throw new UnsupportedOperationException();
    }

    public static long EPOCH;

    private static final long WORKER_ID_BITS = 10L;

    static {
        Calendar calendar = Calendar.getInstance();
        calendar.set(2019, Calendar.FEBRUARY, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        EPOCH = calendar.getTimeInMillis();
    }
    
    private static byte sequenceOffset;
    
    private static long sequence;
    
    private static long lastMilliseconds;

    /**
     * Generate key.
     *
     * @return key type is @{@link Long}.
     */
    public static synchronized Long generateKey() {
        long currentMilliseconds = System.currentTimeMillis();
        if (waitTolerateTimeDifferenceIfNeed(currentMilliseconds)) {
            currentMilliseconds = System.currentTimeMillis();
        }
        long sequenceBits = 12L;
        if (lastMilliseconds == currentMilliseconds) {
            long sequenceMask = (1 << sequenceBits) - 1;
            if (0L == (sequence = (sequence + 1) & sequenceMask)) {
                currentMilliseconds = waitUntilNextTime(currentMilliseconds);
            }
        } else {
            vibrateSequenceOffset();
            sequence = sequenceOffset;
        }
        lastMilliseconds = currentMilliseconds;
        long timestampLeftShiftBits = sequenceBits + WORKER_ID_BITS;
        long workerId = 1;
        return ((currentMilliseconds - EPOCH) << timestampLeftShiftBits) | (workerId << sequenceBits) | sequence;
    }

    @SneakyThrows
    private static boolean waitTolerateTimeDifferenceIfNeed(final long currentMilliseconds) {
        if (lastMilliseconds <= currentMilliseconds) {
            return false;
        }
        long timeDifferenceMilliseconds = lastMilliseconds - currentMilliseconds;
        int maxTolerateTimeDifferenceMilliseconds = 1500;
        if (timeDifferenceMilliseconds >= maxTolerateTimeDifferenceMilliseconds) {
            log.error(String.format("Clock is moving backwards, last time is %d milliseconds, current time is %d milliseconds", lastMilliseconds, currentMilliseconds));
        }
        Thread.sleep(timeDifferenceMilliseconds);
        return true;
    }
    
    private static long waitUntilNextTime(final long lastTime) {
        long result = System.currentTimeMillis();
        while (result <= lastTime) {
            result = System.currentTimeMillis();
        }
        return result;
    }
    
    private static void vibrateSequenceOffset() {
        sequenceOffset = (byte) (~sequenceOffset & 1);
    }
    
}
