package com.holderzone.holder.saas.store.deposit.service.impl;

import com.holderzone.framework.base.dto.message.MessageDTO;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.deposit.service.SendMessageService;
import com.holderzone.holder.saas.store.deposit.service.rpc.EntServiceClient;
import com.holderzone.holder.saas.store.deposit.service.rpc.MsgClientService;
import com.holderzone.resource.common.dto.enterprise.DeductShortMessageDTO;
import com.holderzone.resource.common.dto.enterprise.MessageConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SendMessageServiceImpl
 * @date 2018/11/01 16:51
 * @description //TODO
 * @program holder-saas-store-member
 */
@Service
@Slf4j
public class SendMessageServiceImpl implements SendMessageService {
    private static final Logger logger = LoggerFactory.getLogger(SendMessageServiceImpl.class);
    @Autowired
    private EntServiceClient es;
    @Autowired
    private MsgClientService ms;

    @Override
    public void sendMessage(MessageDTO m1, String entGuid) {
        MessageConfigDTO messageInfo = es.getMessageInfo(entGuid);
        // 寄存商品
        if (messageInfo != null) {
            log.info("寄存相关发送短信 ");
            messageInfo.setResidueCount(messageInfo.getResidueCount() == -1 ? Integer.MAX_VALUE : messageInfo.getResidueCount());
            List<DeductShortMessageDTO> ds = new ArrayList<>();
            DeductShortMessageDTO d = new DeductShortMessageDTO();
            d.setDeductCount(1);
            d.setEnterpriseGuid(entGuid);
            ds.add(d);

            if (messageInfo.getResidueCount() >= 1 && es.deductShortMessage(ds)) {
                ms.sendMessage(m1);
                DateTimeFormatter ftf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String formatTime = ftf.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(System.currentTimeMillis()), ZoneId.systemDefault()));
                logger.info("寄存相关短信发送请求时间:" + formatTime);
            }
        }

    }
}
