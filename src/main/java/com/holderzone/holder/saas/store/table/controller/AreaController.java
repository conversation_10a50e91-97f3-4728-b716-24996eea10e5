package com.holderzone.holder.saas.store.table.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.table.service.AreaService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.table.AreaDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.holderzone.holder.saas.store.table.utils.ValidatorUtils.*;

/**
 * ┏┓　　　┏┓
 * ┏┛┻━━━┛┻┓
 * ┃　　　　　　　┃
 * ┃　　　━　　　┃
 * ┃　┳┛　┗┳　┃
 * ┃　　　　　　　┃
 * ┃　　　┻　　　┃
 * ┃　　　　　　　┃
 * ┗━┓　　　┏━┛
 * 　　┃　　　┃神兽保佑
 * 　　┃　　　┃代码无BUG！
 * 　　┃　　　┗━━━┓
 * 　　┃　　　　　┣┓
 * 　　┃　　　　┏┛
 * 　　┗┓┓┏━┳┓┏┛
 * 　　  ┃┫┫　┃┫┫
 * 　　　┗┻┛　┗┻┛
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/12/27 10:22
 */
@Slf4j
@Api("区域controller")
@RequestMapping("/area")
@RestController
public class AreaController {

    private final AreaService areaService;

    @Autowired
    public AreaController(AreaService areaService) {
        this.areaService = areaService;
    }

    @ApiOperation(value = "门店初始化调用，初始化大厅以及对应桌台")
    @PostMapping("/init")
    public String initArea(@RequestBody BaseDTO baseDTO) {
        if (log.isInfoEnabled()) {
            log.info("初始化门店桌台区域入参：{}", JacksonUtils.writeValueAsString(baseDTO));
        }
        validatorInitArea(baseDTO);
        return areaService.initArea(baseDTO);
    }

    @ApiOperation(value = "新增桌台区域")
    @PostMapping("/add")
    public String addArea(@RequestBody AreaDTO areaDTO) {
        if (log.isInfoEnabled()) {
            log.info("新增桌台区域入参：{}", JacksonUtils.writeValueAsString(areaDTO));
        }
        validatorAreaDTO(areaDTO);
        return areaService.createArea(areaDTO);
    }

    @ApiOperation(value = "更新桌台区域")
    @PostMapping("/update")
    public String updateArea(@RequestBody AreaDTO areaDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新桌台区域入参：{}", JacksonUtils.writeValueAsString(areaDTO));
        }
        validatorUpdateAreaDTO(areaDTO);
        return areaService.updateArea(areaDTO);
    }

    @ApiOperation(value = "删除桌台区域")
    @PostMapping("/delete/{guid}")
    public String delete(@PathVariable("guid") String guid) {
        if (log.isInfoEnabled()) {
            log.info("删除桌台区域入参：{}", guid);
        }
        return areaService.deleteArea(guid);
    }

    @ApiOperation(value = "查询桌台区域列表")
    @PostMapping("/query/all/{storeGuid}")
    public List<AreaDTO> query(@PathVariable("storeGuid") String storeGuid) {
        if (log.isInfoEnabled()) {
            log.info("查询桌台区域列表入参：{}", storeGuid);
        }
        return areaService.queryAll(storeGuid);
    }

    @ApiOperation(value = "批量查询桌台区域列表")
    @PostMapping("/batch_query/all")
    public List<AreaDTO> batchQuery(@RequestBody SingleDataDTO singleDataDTO) {
        if (log.isInfoEnabled()) {
            log.info("批量查询桌台区域列表入参：{}", JacksonUtils.writeValueAsString(singleDataDTO));
        }
        return areaService.batchQueryAll(singleDataDTO.getDatas());
    }

    /**
     * 查询区域
     *
     * @param areaGuid 区域guid
     * @return 区域信息
     */
    @ApiOperation(value = "查询区域信息")
    @GetMapping("/query_area/{areaGuid}")
    public AreaDTO queryAreaByTable(@PathVariable("areaGuid") String areaGuid) {
        if (log.isInfoEnabled()) {
            log.info("根据桌台查询桌位入参：{}", areaGuid);
        }
        return areaService.queryAreaByTable(areaGuid);
    }
}
