package com.holderzone.holder.saas.store.hw.util;

import com.holderzone.framework.util.Bean2Map;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradingUtils
 * @date 2018/07/25 16:13
 * @description //
 * @program holder-saas-store-trading-center
 */
public final class TradingUtils {


    private static final String KEY_APP_SECRET = "appSecret";

    private static final String KEY_SIGNATURE = "signature";


    public static Map<String, Object> beanToMapWithoutSignature(Object obj) {
        Map<String, Object> map = Bean2Map.bean2map(obj);
        map.remove(KEY_SIGNATURE);
        return map;
    }

    public static String getSignatureForCheck(Object obj,  String appSecret) {
        return getSignature(beanToMapWithoutSignature(obj), appSecret);
    }

    public static String getSignature(Object obj, String appSecret) {
        return getSignature(Bean2Map.bean2map(obj), appSecret);
    }

    public static String getSignature(Map<String, Object> map,  String appSecret) {
        // 字典排序map
        Map<String, Object> filteredSortedMap = map.entrySet().stream()
                .filter(stringObjectEntry -> validate(stringObjectEntry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (u, v) -> {
                            throw new IllegalStateException(String.format("Duplicate key %s", u));
                        }, TreeMap::new)
                );
        // 移除signature字段，
        filteredSortedMap.remove(KEY_SIGNATURE);
        // 参数拼接
        String joinedParam = filteredSortedMap.entrySet().stream()
                .map(stringObjectEntry -> String.format("%s=%s", stringObjectEntry.getKey(), stringObjectEntry.getValue()))
                .collect(Collectors.joining("&"));

        // appSecret拼接
        String joinedParamWithKey = String.format("%s&%s=%s",joinedParam, KEY_APP_SECRET, appSecret);
        return DigestUtils.sha1Hex(joinedParamWithKey.getBytes());
    }

    private static boolean validate(Object object) {
        return object != null && (!(object instanceof String) || StringUtils.hasText((String) object));
    }
}
