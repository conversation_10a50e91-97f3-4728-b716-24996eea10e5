package com.holderzone.holder.saas.store.client.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("客户分析")
public class CustomerAnalysisDTO {
    @ApiModelProperty("访客数")
    private Integer visitCount;
    @ApiModelProperty("散客数")
    private Integer individualCount;
    @ApiModelProperty("会员数")
    private Integer memberCount;
    @ApiModelProperty("新增数")
    private Integer newCount;
    @ApiModelProperty("会员充值")
    private BigDecimal memberPrepaid;
    @ApiModelProperty("会员消费")
    private BigDecimal memberConsume;

    @ApiModelProperty("访客期间增长率")
    private String visitRateOfIncrease;
    @ApiModelProperty("散客期间增长率")
    private String individualRateOfIncrease;
    @ApiModelProperty("会员期间增长率")
    private String memberRateOfIncrease;
    @ApiModelProperty("新增期间增长率")
    private String newRateOfIncrease;
    @ApiModelProperty("会员充值期间增长率")
    private String memberPrepaidRateOfIncrease;
    @ApiModelProperty("会员消费期间增长率")
    private String memberConsumeRateOfIncrease;
}
