package com.holderzone.holder.saas.store.mdm.service.impl;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.mdm.service.DistributedIdService;
import com.holderzone.sdk.util.BatchIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DistributedIdServiceImpl
 * @date 2018/02/14 09:00
 * @description 分布式id服务实现类
 * @program holder-saas-store-print
 */
@Slf4j
@Service
public class DistributedIdServiceImpl implements DistributedIdService {

    private static final String TAG_STAFF_USER = "staff/user";

    private static final String TAG_ITEM_ITEM = "item/item";
    private static final String TAG_ITEM_SKU = "item/sku";
    private static final String TAG_ITEM_TYPE = "item/type";

    private static final String TAG_STORE_BRAND = "store_brand";

    private final RedisTemplate redisTemplate;

    @Autowired
    public DistributedIdServiceImpl(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public Long rawId(String tag) {
        try {
            return BatchIdGenerator.getGuid(redisTemplate, tag);
        } catch (IOException e) {
            throw new BusinessException("生成Guid失败：" + e.getMessage());
        }
    }

    @Override
    public String nextId(String tag) {
        return String.valueOf(rawId(tag));
    }

    @Override
    public List<String> nextBatchId(String tag, long count) {
        return BatchIdGenerator.batchGetGuids(redisTemplate, tag, count)
                .stream().map(String::valueOf).collect(Collectors.toList());
    }

    @Override
    public String nextUserGuid() {
        return nextId(TAG_STAFF_USER);
    }

    @Override
    public String nextItemGuid() {
        return nextId(TAG_ITEM_ITEM);
    }

    @Override
    public String nextSkuGuid() {
        return nextId(TAG_ITEM_SKU);
    }

    @Override
    public String nextTypeGuid() {
        return nextId(TAG_ITEM_TYPE);
    }

    @Override
    public String nextStoreBrandGuid() {
        return nextId(TAG_STORE_BRAND);
    }
}
