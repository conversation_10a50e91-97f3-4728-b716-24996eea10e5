package com.holderzone.holder.saas.store.report.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.report.mapper.MemberMapper;
import com.holderzone.holder.saas.store.report.service.OpenMemberService;
import com.holderzone.saas.store.dto.report.openapi.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class OpenMemberServiceImpl implements OpenMemberService {
    public static final int DEFAULT_LIMIT = 500;
    public static final int MAX_LIMIT = 1000;

    private final MemberMapper memberMapper;

    private final Executor openapiReportQueryExecutor;

    @Override
    public MemberFundingDetailLimitRespDTO queryFundingDetail(MemberFundingDetailQueryDTO queryDTO) {
        // 参数校验
        checkSaleDetailQueryParams(queryDTO);
        // 填充参数默认值
        fullQueryParamsDefaultValue(queryDTO);
        // 查询列表数据
        List<MemberFundingDetailRespDTO> memberFundingDetailRespList = memberMapper.queryFundingDetail(queryDTO);
        if (CollectionUtils.isEmpty(memberFundingDetailRespList)) {
            return MemberFundingDetailLimitRespDTO.buildEmpty();
        }

        // 分页游标
        String cursor = null;
        MemberFundingDetailRespDTO minMemberFundingDetailRespDTO = memberFundingDetailRespList.stream()
                .min(Comparator.comparing(MemberFundingDetailRespDTO::getGmtCreate).thenComparing(MemberFundingDetailRespDTO::getGuid))
                .orElse(null);
        if (Objects.nonNull(minMemberFundingDetailRespDTO) && memberFundingDetailRespList.size() == queryDTO.getLimit()) {
            cursor = minMemberFundingDetailRespDTO.getGuid();
            memberFundingDetailRespList.removeIf(e -> e.getGuid().equals(minMemberFundingDetailRespDTO.getGuid()));
        }
        List<String> memberInfoGuids = memberFundingDetailRespList.stream()
                .map(MemberFundingDetailRespDTO::getMemberInfoGuid)
                .distinct()
                .collect(Collectors.toList());
        List<String> memberInfoCardGuids = memberFundingDetailRespList.stream()
                .map(MemberFundingDetailRespDTO::getMemberInfoCardGuid)
                .distinct()
                .collect(Collectors.toList());
        // 查询会员信息
        CompletableFuture<List<MemberDetailRespDTO>> memberDetailRespListFuture = CompletableFuture
                .supplyAsync(() ->
                        memberMapper.queryMember(memberInfoGuids), openapiReportQueryExecutor)
                .exceptionally(throwable -> {
                    log.error("查询会员信息失败", throwable);
                    return null;
                });
        // 查询变动会员卡信息
        CompletableFuture<List<MemberCardRespDTO>> memberCardRespListFuture = CompletableFuture
                .supplyAsync(() ->
                        memberMapper.queryMemberCard(memberInfoCardGuids), openapiReportQueryExecutor)
                .exceptionally(throwable -> {
                    log.error("查询会员卡信息失败", throwable);
                    return null;
                });
        CompletableFuture<Void> all = CompletableFuture.allOf(memberDetailRespListFuture, memberCardRespListFuture);
        try {
            all.get();
            // 返回订单处理
            respHandler(memberFundingDetailRespList, memberDetailRespListFuture.get(), memberCardRespListFuture.get());
            return MemberFundingDetailLimitRespDTO.buildResult(memberFundingDetailRespList, cursor);
        } catch (Exception e) {
            log.error("开放接口查询会员数据列表失败", e);
            Thread.currentThread().interrupt();
            throw new BusinessException("系统繁忙稍后再试");
        }
    }

    /**
     * 参数校验
     */
    private void checkSaleDetailQueryParams(MemberFundingDetailQueryDTO queryDTO) {
        String enterpriseGuid = queryDTO.getEnterpriseGuid();
        if (StringUtils.isEmpty(enterpriseGuid)) {
            throw new BusinessException("企业guid必填");
        }
        Integer limit = queryDTO.getLimit();
        if (Objects.nonNull(limit)) {
            if (limit <= 0) {
                queryDTO.setLimit(DEFAULT_LIMIT);
            }
            if (limit > MAX_LIMIT) {
                queryDTO.setLimit(MAX_LIMIT);
            }
        }
    }

    /**
     * 填充参数默认值
     */
    private void fullQueryParamsDefaultValue(MemberFundingDetailQueryDTO queryDTO) {
        Integer limit = queryDTO.getLimit();
        if (Objects.isNull(limit)) {
            queryDTO.setLimit(DEFAULT_LIMIT);
        }
        // Limit= Limit + 1
        queryDTO.setLimit(queryDTO.getLimit() + 1);
    }


    /**
     * 处理返回信息
     */
    private void respHandler(List<MemberFundingDetailRespDTO> memberFundingDetailRespList,
                             List<MemberDetailRespDTO> memberDetailRespList,
                             List<MemberCardRespDTO> memberCardRespList) {
        Map<String, MemberDetailRespDTO> memberDetailRespMap = Optional.ofNullable(memberDetailRespList).orElse(Lists.newArrayList()).stream()
                .collect(Collectors.toMap(MemberDetailRespDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        Map<String, MemberCardRespDTO> memberCardRespMap = Optional.ofNullable(memberCardRespList).orElse(Lists.newArrayList()).stream()
                .collect(Collectors.toMap(MemberCardRespDTO::getMemberInfoCardGuid, Function.identity(), (key1, key2) -> key1));
        // 会员资金变动明细返回处理
        memberFundingDetailRespHandler(memberFundingDetailRespList, memberDetailRespMap, memberCardRespMap);
    }


    /**
     * 会员资金变动明细返回处理
     */
    private void memberFundingDetailRespHandler(List<MemberFundingDetailRespDTO> memberFundingDetailRespList,
                                                Map<String, MemberDetailRespDTO> memberDetailRespMap,
                                                Map<String, MemberCardRespDTO> memberCardRespMap) {
        if (CollectionUtils.isEmpty(memberFundingDetailRespList)) {
            return;
        }
        for (MemberFundingDetailRespDTO respDTO : memberFundingDetailRespList) {
            // 会员信息
            MemberDetailRespDTO memberDetailRespDTO = memberDetailRespMap.get(respDTO.getMemberInfoGuid());
            if (Objects.nonNull(memberDetailRespDTO)) {
                respDTO.setMemberName(memberDetailRespDTO.getMemberName());
                respDTO.setPhoneNum(memberDetailRespDTO.getPhoneNum());
            }
            // 会员卡信息
            MemberCardRespDTO memberCardRespDTO = memberCardRespMap.get(respDTO.getMemberInfoCardGuid());
            if (Objects.nonNull(memberCardRespDTO)) {
                respDTO.setCardLevelName(memberCardRespDTO.getCardLevelName());
                respDTO.setCardNum(memberCardRespDTO.getCardNum());
            }
        }
    }
}
