package com.holderzone.holder.saas.store.mdm.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
public class BaseDO implements Serializable {

    private static final long serialVersionUID = 5525695802096275847L;

    /**
     * 外部版本号
     */
    @TableField(update = "%s+1", fill = FieldFill.INSERT_UPDATE)
    private Integer externalVersion = 1;
}
