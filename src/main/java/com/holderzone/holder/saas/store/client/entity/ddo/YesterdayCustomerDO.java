package com.holderzone.holder.saas.store.client.entity.ddo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("昨日客户数据")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class YesterdayCustomerDO {

    public static YesterdayCustomerDO DEFAULT = new YesterdayCustomerDO( 0, new ArrayList<>());

    @ApiModelProperty(value = "成交客户人数")
    private Integer dealCount;
    @ApiModelProperty(value = "点餐分类")
    private List<OrderCategoryDO> orderCategories;


    public static YesterdayCustomerDO INSTANCE() {
        return new YesterdayCustomerDO( 0, new ArrayList<>());
    }
}
