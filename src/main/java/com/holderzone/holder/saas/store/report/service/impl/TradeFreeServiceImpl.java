package com.holderzone.holder.saas.store.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.report.constant.CommonConstant;
import com.holderzone.holder.saas.store.report.dto.FreeExcelDTO;
import com.holderzone.holder.saas.store.report.mapper.TradeFreeMapper;
import com.holderzone.holder.saas.store.report.service.TradeFreeService;
import com.holderzone.holder.saas.store.report.util.ExcelUtils;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.base.Pager;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.FreeItemDTO;
import com.holderzone.saas.store.dto.report.resp.TotalStatisticsDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;


@Service
@RequiredArgsConstructor
public class TradeFreeServiceImpl implements TradeFreeService {

    private final TradeFreeMapper tradeFreeMapper;

    private final OssClient ossClient;

    @Override
    public Message<FreeItemDTO> list(ReportQueryVO query) {
        Message<FreeItemDTO> message = new Message<>();
        // 合计
        TotalStatisticsDTO statistics = tradeFreeMapper.statistics(query);
        if (Objects.isNull(statistics) || statistics.getTotalQuantity().compareTo(BigDecimal.ZERO) == 0) {
            Pager pager = new Pager(query.getCurrentPage(), query.getPageSize(), 0);
            message.setPager(pager);
            message.setList(Lists.newArrayList());
            message.setData(JacksonUtils.toMap(JacksonUtils.writeValueAsString(TotalStatisticsDTO.DEFAULT)));
            return message;
        }
        // 查询总条数
        Integer totalCount = tradeFreeMapper.count(query);

        Pager pager = new Pager(query.getCurrentPage(), query.getPageSize(), totalCount);
        message.setPager(pager);
        message.setList(listPure(query,statistics.getTotalQuantity()));
        message.setData(JacksonUtils.toMap(JacksonUtils.writeValueAsString(statistics)));
        return message;
    }

    private List<FreeItemDTO> listPure(ReportQueryVO query,BigDecimal totalReturnQuantity) {
        // 查询列表
        List<FreeItemDTO> list = tradeFreeMapper.pageInfo(query);
        Iterator<FreeItemDTO> iterator = list.iterator();
        while (iterator.hasNext()) {
            FreeItemDTO freeRecord = iterator.next();
            // 计算增菜率
            BigDecimal freeQuantity = freeRecord.getGivenQuantity();
            if (Objects.isNull(freeQuantity) || freeQuantity.compareTo(BigDecimal.ZERO) == 0) {
                iterator.remove();
                continue;
            }
            BigDecimal rate = freeQuantity.multiply(new BigDecimal(100)).divide(totalReturnQuantity, 2, RoundingMode.HALF_UP);
            freeRecord.setGivenRate(rate.toPlainString() + "%");
        }
        return list;
    }

    @Override
    public String export(ReportQueryVO query) {
        // 查询总条数
        Integer totalCount = tradeFreeMapper.count(query);
        if(totalCount > CommonConstant.MAX_EXPORT){
            throw  new BusinessException(CommonConstant.MAX_EXPORT_TITLE);
        }
        query.setPageSize(1);
        query.setPageSize(CommonConstant.MAX_EXPORT);
        List<FreeItemDTO> freeItemList = listPure(query, tradeFreeMapper.statistics(query).getTotalQuantity());
        List<FreeExcelDTO> freeExcelList = Lists.newArrayList();
        if(CollectionUtil.isNotEmpty(freeItemList)){
            freeItemList.forEach(f ->{
                FreeExcelDTO freeExcel = new FreeExcelDTO();
                BeanUtil.copyProperties(f,freeExcel);
                freeExcel.setGivenQuantity(freeExcel.getGivenQuantity().setScale(0,RoundingMode.HALF_UP));
                freeExcel.setGivenRefund(freeExcel.getGivenRefund().setScale(2,RoundingMode.HALF_UP));
                freeExcelList.add(freeExcel);
            });
        }
        try {
            String sheetName = "赠菜报表";
            String fileName = sheetName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return ExcelUtils.exportExcel(freeExcelList,
                    null, sheetName, FreeExcelDTO.class, fileName, ossClient);
        } catch (Exception e) {
            throw new BusinessException("导出失败");
        }
    }
}
