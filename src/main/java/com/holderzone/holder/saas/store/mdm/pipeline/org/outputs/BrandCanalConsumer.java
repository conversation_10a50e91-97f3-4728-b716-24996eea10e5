package com.holderzone.holder.saas.store.mdm.pipeline.org.outputs;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.event.AbsErpRocketMqConsumer;
import com.holderzone.holder.saas.store.mdm.pipeline.org.agg.BrandAggService;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.BrandInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MDMUserListener
 * @date 2019/11/18 11:24
 * @description
 * @program holder-saas-store
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.StoreConfig.STORE_MDM_BRAND_TOPIC,
        tags = {
                RocketMqConfig.StoreConfig.STORE_MDM_BRAND_INIT_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_BRAND_CREATE_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_BRAND_UPDATE_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_BRAND_DELETE_TAG
        },
        consumerGroup = RocketMqConfig.StoreConfig.STORE_MDM_BRAND_GROUP
)
public class BrandCanalConsumer extends AbsErpRocketMqConsumer<RocketMqTopic, String> {

    private final BrandAggService brandAggService;

    @Autowired
    public BrandCanalConsumer(BrandAggService brandAggService) {
        this.brandAggService = brandAggService;
    }

    @Override
    protected boolean doConsumeMsg(String obj, MessageExt messageExt) {
        String tags = messageExt.getTags();
        switch (tags) {
            case RocketMqConfig.StoreConfig.STORE_MDM_BRAND_INIT_TAG:
                brandAggService.triggerRemoteBrand(MdmTriggerType.ofType(obj));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_BRAND_CREATE_TAG:
                brandAggService.createRemoteBrand(JacksonUtils.toObjectList(BrandInfoDTO.class, obj));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_BRAND_UPDATE_TAG:
                brandAggService.updateRemoteBrand(JacksonUtils.toObject(BrandInfoDTO.class, obj));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_BRAND_DELETE_TAG:
                brandAggService.deleteRemoteBrand(JacksonUtils.toObjectList(BrandInfoDTO.class, obj));
                break;
            default:
                log.error("unknown mq tag : {}, message：{}",
                        messageExt.getTags(),
                        JacksonUtils.writeValueAsString(obj));
                break;
        }
        return true;
    }
}
