package com.holderzone.holder.saas.store.table.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/12/26 15:47
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("区域DO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("hst_area")
public class AreaDO extends BaseDO {

    @ApiModelProperty("区域名称")
    private String areaName;

    @ApiModelProperty("排序字段")
    private Integer sort;

    @ApiModelProperty("0-启用，1-禁用")
    private Integer enable = 0;

    /**
     * 是否删除
     */
    @TableLogic
    @ApiModelProperty("0-未删除，1-删除")
    private Integer deleted = 0;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime gmtModified;

}
