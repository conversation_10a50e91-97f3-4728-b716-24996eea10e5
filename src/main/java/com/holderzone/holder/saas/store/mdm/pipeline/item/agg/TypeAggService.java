package com.holderzone.holder.saas.store.mdm.pipeline.item.agg;

import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.TypeSyncDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SkuAggService
 * @date 2019/11/23 下午4:33
 * @description //
 * @program holder
 */


public interface TypeAggService {

    void triggerRemoteType(MdmTriggerType mdmTriggerType);

    /**
     * 智慧门店批量创建商品分类同步创建至MDM
     * @param mdmTypeSynDTOS
     */
    void createRemoteType(List<TypeSyncDTO> mdmTypeSynDTOS);

    /**
     * 智慧门店修改商品分类同步修改MDM商品分类
     * @param mdmTypeSynDTO
     */
    void updateRemoteType(TypeSyncDTO mdmTypeSynDTO);

    /**
     * 智慧门店删除商品分类同步删除MDM商品分类
     * @param mdmTypeSynDTOS
     */
    void deleteRemoteType(List<TypeSyncDTO> mdmTypeSynDTOS);


    /**
     * MDM主数据创建分类推送至智慧门店本地同步创建
     * @param mdmTypeSynDTO
     */
    void createLocalType(TypeSyncDTO mdmTypeSynDTO);

    /**
     * MDM主数据更新分类推送至智慧门店本地同步更新
     * @param mdmTypeSynDTO
     */
    void updateLocalType(TypeSyncDTO mdmTypeSynDTO);

    /**
     * MDM主数据删除分类推送至智慧门店本地同步删除
     * @param mdmTypeSynDTO
     */
    void deleteLocalType(TypeSyncDTO mdmTypeSynDTO);
}
