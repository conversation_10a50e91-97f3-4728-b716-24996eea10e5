package com.holderzone.holder.saas.store.table.event;

import com.holderzone.feign.spring.boot.util.MdcContextUtils;
import com.holderzone.feign.spring.boot.util.TraceContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.common.AbstractRocketMqConsumer;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.table.constant.RocketMqConstant;
import com.holderzone.holder.saas.store.table.service.TableOrderService;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.holder.saas.store.table.utils.TableLogUtils;
import com.holderzone.holder.saas.store.table.utils.ThrowableExtUtils;
import com.holderzone.saas.store.dto.table.TableStatusChangeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/09/18 20:47
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConstant.TABLE_STATUS_CHANGE_MQ_TOPIC,
        tags = RocketMqConstant.TABLE_STATUS_CHANGE_MQ_TAG,
        consumerGroup = RocketMqConstant.TABLE_STATUS_CHANGE_MQ_GROUP)

public class TableMessageListener extends AbstractRocketMqConsumer<RocketMqTopic, TableStatusChangeDTO> {

    private final TableOrderService tableOrderService;

    @Autowired
    public TableMessageListener(TableOrderService tableOrderService) {
        this.tableOrderService = tableOrderService;
    }

    @Override
    public boolean consumeMsg(TableStatusChangeDTO tableStatusChangeDTO, MessageExt messageExt) {
        try {
            String enterpriseGuid = tableStatusChangeDTO.getEnterpriseGuid();
            String storeGuid = tableStatusChangeDTO.getStoreGuid();
            String tableGuid = tableStatusChangeDTO.getTableGuid();
            String orderGuid = tableStatusChangeDTO.getOrderGuid();

            TraceContextUtils.setTraceId(messageExt.getMsgId());
            UserContextUtils.putOrByErpAndStore(messageExt.getProperty(RocketMqConstant.MESSAGE_CONTEXT), enterpriseGuid, storeGuid);
            MdcContextUtils.fillByPreContext();
            MdcContextUtils.fillByCusContext("searchKey", TableLogUtils.searchKey(orderGuid, tableGuid));

            log.info("桌台状态变化同步消费Mq，{}", JacksonUtils.writeValueAsString(tableStatusChangeDTO));
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
            //BugFixed:20283，确保传递了tableGuid，才进行后续逻辑的处理
            if (!StringUtils.isEmpty(tableGuid)) {
                tableOrderService.statusSync(tableStatusChangeDTO);
            }
        } catch (Exception e) {
            log.warn("桌台状态变化同步消费Mq,消息消费异常：{}", ThrowableExtUtils.asStringIfAbsent(e));
            return false;
        } finally {
            EnterpriseIdentifier.setEnterpriseGuid(null);
        }
        return true;
    }
}
