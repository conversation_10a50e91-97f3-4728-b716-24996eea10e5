package com.holderzone.holder.saas.store.report.mapper;

import com.holderzone.saas.store.dto.report.query.EstimateReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.EstimateReportRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 估清报表
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Mapper
public interface TradeEstimateMapper {

    Long count(@Param("query") EstimateReportQueryVO query);

    List<EstimateReportRespDTO> list(@Param("query") EstimateReportQueryVO query);

    List<EstimateReportRespDTO> pageInfo(@Param("query") EstimateReportQueryVO query);

    List<EstimateReportRespDTO> listEstimate(@Param("query") EstimateReportQueryVO query);
}
