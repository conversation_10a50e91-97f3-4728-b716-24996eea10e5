package com.holderzone.holder.saas.store.mdm.util;

import com.holderzone.framework.util.Bean2Map;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-08-15 14:43:54
 */
public class CommonUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonUtils.class);

    private static final String KEY_APP_SECRET = "developerKey";
    private static final String KEY_SIGNATURE = "signature";

    public static BigDecimal formatNumber(BigDecimal num, int digit) {
        return num.divide(BigDecimal.ONE, digit, BigDecimal.ROUND_HALF_UP);
    }

    private static Map<String, Object> beanToMapWithoutSignature(Object obj) {
        Map<String, Object> map =  Bean2Map.bean2map(obj);
        map.remove(KEY_SIGNATURE);
        return map;
    }

    private static boolean validate(Object object) {
        return object != null && (!(object instanceof String) || StringUtils.hasText((String) object));
    }

    public static String syncSign(Object obj, String developerKey) {
        Map<String, Object> map = beanToMapWithoutSignature(obj);
        // 有序字段map
        Map<String, Object> filteredSortedMap = sortMap(map);
        // 参数拼接
        String joinedParam = filteredSortedMap.entrySet().stream()
                .map(entry -> String.format("%s=%s", entry.getKey(), "request".equalsIgnoreCase(entry.getKey()) ? JacksonUtils.writeValueAsString(entry.getValue()) : entry.getValue()))
                .collect(Collectors.joining("&"));
        // developerKey拼接
        String joinedParamWithKey = String.format("%s&%s=%s", joinedParam, KEY_APP_SECRET, developerKey);
        LOGGER.info("待签名字符串： {}", joinedParamWithKey);
        // 我方计算signature
        return DigestUtils.md5Hex(joinedParamWithKey.getBytes());
    }

    private static Map<String, Object> sortMap(Map<String, Object> map) {
        return map.entrySet().stream()
                .filter(stringObjectEntry -> validate(stringObjectEntry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                        (u, v) -> {
                            throw new IllegalStateException(String.format("Duplicate key %s", u));
                        }, TreeMap::new)
                );
    }


}
