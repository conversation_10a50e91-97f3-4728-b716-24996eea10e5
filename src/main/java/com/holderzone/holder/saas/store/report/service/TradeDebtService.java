package com.holderzone.holder.saas.store.report.service;


import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.DebtUnitRecordDTO;

public interface TradeDebtService {

    /**
     * 挂账明细
     */
    Message<DebtUnitRecordDTO> list(ReportQueryVO query);

    /**
     * 导出挂账明细
     */
    String export(ReportQueryVO query);
}
