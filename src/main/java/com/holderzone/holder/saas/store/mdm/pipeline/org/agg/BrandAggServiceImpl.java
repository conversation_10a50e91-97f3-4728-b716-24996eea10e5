package com.holderzone.holder.saas.store.mdm.pipeline.org.agg;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.exception.RepeatedException;
import com.holderzone.holder.saas.store.mdm.pipeline.item.feign.ItemClient;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.BrandInfoDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.mapstruct.BrandMapstruct;
import com.holderzone.holder.saas.store.mdm.pipeline.org.service.BrandService;
import com.holderzone.holder.saas.store.mdm.service.MdmOperation;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import com.holderzone.holder.saas.store.mdm.util.SplitUtils;
import com.holderzone.holder.saas.store.mdm.util.TriggerLogUtils;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.holderzone.holder.saas.store.mdm.constant.ReqTypeConstant.*;
import static com.holderzone.holder.saas.store.mdm.constant.ReqTypeConstant.DELETE;
import static com.holderzone.holder.saas.store.mdm.constant.ReqUrlConstants.*;

/**
 * 品牌表 服务实现类
 *
 * <AUTHOR>
 * @since 2019-01-02
 */
@Slf4j
@Service
@SuppressWarnings("unchecked")
public class BrandAggServiceImpl implements BrandAggService {

    private final BrandService brandService;

    private final MdmOperation mdmOperation;

    private final ItemClient itemClient;

    private final MqUtils mqUtils;

    @Value("${mdm.initSyncStep:50}")
    private int initSyncStep;

    @Autowired
    public BrandAggServiceImpl(BrandService brandService,
                               MdmOperation mdmOperation, ItemClient itemClient, MqUtils mqUtils) {
        this.brandService = brandService;
        this.mdmOperation = mdmOperation;
        this.itemClient = itemClient;
        this.mqUtils = mqUtils;
    }

    @Override
    public void createLocalBrand(BrandInfoDTO brandInfoDTO) {
        boolean succeed = brandService.createLocalBrand(brandInfoDTO);

        if (succeed) {
            // 初始化品牌下的商品信息
            ItemSingleDTO itemSingleDTO = new ItemSingleDTO();
            itemSingleDTO.setData(brandInfoDTO.getThirdNo());
            itemClient.addDefaultAttr(itemSingleDTO);
        }
    }

    @Override
    public void updateLocalBrand(BrandInfoDTO brandInfoDTO) {
        if (!brandService.updateLocalBrand(brandInfoDTO)) {
            createLocalBrand(brandInfoDTO);
        }
    }

    @Override
    public void deleteLocalBrand(BrandInfoDTO brandInfoDTO) {
        brandService.deleteLocalBrand(brandInfoDTO.getThirdNo());
    }

    @Override
    public void triggerRemoteBrand(MdmTriggerType mdmTriggerType) {
        List<BrandInfoDTO> brandInfoList = brandService.shouldInitBrandDOS()
                .stream()
                .filter(brandDO -> !brandDO.getIsDeleted())
                .map(BrandMapstruct.INSTANCE::brandDO2InfoDTO)
                .collect(Collectors.toList());

        TriggerLogUtils.pre("品牌", brandInfoList.size());

        switch (mdmTriggerType) {
            case CREATE:
                SplitUtils.splitList(brandInfoList, initSyncStep).forEach(brand -> {
                    try {
                        createRemoteBrand(brand);
                    } catch (Exception e) {
                        TriggerLogUtils.stepFailed("品牌", e);
                        mqUtils.sendMessage(
                                RocketMqConfig.StoreConfig.STORE_MDM_BRAND_TOPIC,
                                RocketMqConfig.StoreConfig.STORE_MDM_BRAND_CREATE_TAG,
                                brand, UserContextUtils.getEnterpriseGuid()
                        );
                    }
                    TriggerLogUtils.process("品牌", brand.size());
                });
                break;
            case UPDATE:
                brandInfoList.forEach(brand -> {
                    try {
                        updateRemoteBrand(brand);
                    } catch (Exception e) {
                        TriggerLogUtils.singleFailed("品牌", e);
                        mqUtils.sendMessage(
                                RocketMqConfig.StoreConfig.STORE_MDM_BRAND_TOPIC,
                                RocketMqConfig.StoreConfig.STORE_MDM_BRAND_UPDATE_TAG,
                                brand, UserContextUtils.getEnterpriseGuid()
                        );
                    }
                });
                TriggerLogUtils.process("品牌", brandInfoList.size());
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + mdmTriggerType);
        }

        TriggerLogUtils.post("品牌", brandInfoList.size());
    }

    @Override
    public void createRemoteBrand(List<BrandInfoDTO> brandInfoList) {
        try {
            mdmOperation.doRequest(POST, CREATE_BRAND_URL, brandInfoList.stream()
                    .map(brandInfoDTO -> brandInfoDTO.setOrganizationGuid(UserContextUtils.getEnterpriseGuid()))
                    .collect(Collectors.toList()));
        } catch (RepeatedException e) {
            if (brandInfoList.size() == 1) {
                TriggerLogUtils.singleRepeated("品牌");
            } else {
                TriggerLogUtils.batchRepeated("品牌", e);
                for (BrandInfoDTO brandInfoDTO : brandInfoList) {
                    mqUtils.sendMessage(
                            RocketMqConfig.StoreConfig.STORE_MDM_BRAND_TOPIC,
                            RocketMqConfig.StoreConfig.STORE_MDM_BRAND_CREATE_TAG,
                            Collections.singleton(brandInfoDTO), UserContextUtils.getEnterpriseGuid()
                    );
                }
            }
        } catch (BusinessException e) {
            if (brandInfoList.size() == 1) {
                throw e;
            } else {
                TriggerLogUtils.batchFailed("品牌", e);
                for (BrandInfoDTO brandInfoDTO : brandInfoList) {
                    mqUtils.sendMessage(
                            RocketMqConfig.StoreConfig.STORE_MDM_BRAND_TOPIC,
                            RocketMqConfig.StoreConfig.STORE_MDM_BRAND_CREATE_TAG,
                            Collections.singleton(brandInfoDTO), UserContextUtils.getEnterpriseGuid()
                    );
                }
            }
        }
    }

    @Override
    public void updateRemoteBrand(BrandInfoDTO brandInfoDTO) {
        mdmOperation.doRequest(PUT, UPDATE_BRAND_URL, brandInfoDTO.setOrganizationGuid(UserContextUtils.getEnterpriseGuid()));
    }

    @Override
    public void deleteRemoteBrand(List<BrandInfoDTO> brandGuidList) {
        mdmOperation.doRequest(DELETE, DELETE_BRAND_URL, brandGuidList);
    }
}
