package com.holderzone.holder.saas.store.mdm.pipeline.org.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.holderzone.holder.saas.store.mdm.entity.BaseDO;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 组织表
 *
 * <AUTHOR>
 * @since 2019-01-04
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "hso_organization")
public class OrganizationDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id，自增
     */
    @TableId
    private Long id;

    /**
     * guid
     */
    private String guid;

    /**
     * 门店编码
     */
    private String code;

    /**
     * 类型（1-组织，2-门店）
     */
    private Integer type;

    /**
     * 名称
     */
    private String name;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人电话
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String contactTel;

    /**
     * 上级组织id（由最上级组织到直属上级的guid组成，逗号隔开）
     */
    private String parentIds;

    /**
     * 省份code
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市code
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String cityCode;

    /**
     * 城市名称
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String cityName;

    /**
     * 区县code
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String countyCode;

    /**
     * 区县名称
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String countyName;

    /**
     * 详细地址
     */
    private String addressDetail;


    /**
     * 描述
     */
    private String description;

    /**
     * 品牌Logo（oss下载地址）
     */
    private String icon;

    /**
     * 是否启用（默认为1-启用）
     */
    private Boolean isEnable;

    /**
     * 是否删除（默认为0-未删除）
     */
    @TableLogic
    private Boolean isDeleted;

    /**
     * 门店营业开始时间
     */

    private LocalTime businessStart;

    /**
     * 门店营业结束时间
     */
    @JsonIgnore
    private LocalTime businessEnd;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;


    /**
     * 创建人guid
     */
    private String createUserGuid;

    /**
     * 修改人guid
     */
    private String modifiedUserGuid;

    /**
     * 创建时间
     */
    @JsonIgnore
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    @JsonIgnore
    private LocalDateTime gmtModified;

    /**
     * 相册
     */
    private String photos;
}
