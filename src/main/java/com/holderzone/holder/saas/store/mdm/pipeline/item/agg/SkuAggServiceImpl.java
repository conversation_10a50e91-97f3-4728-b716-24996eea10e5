package com.holderzone.holder.saas.store.mdm.pipeline.item.agg;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.exception.RepeatedException;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.SkuSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.domain.SkuDO;
import com.holderzone.holder.saas.store.mdm.pipeline.item.mapstruct.ItemMapStruct;
import com.holderzone.holder.saas.store.mdm.pipeline.item.service.SkuService;
import com.holderzone.holder.saas.store.mdm.service.MdmOperation;
import com.holderzone.holder.saas.store.mdm.util.BigDecimalUtils;
import com.holderzone.holder.saas.store.mdm.util.MqUtils;
import com.holderzone.holder.saas.store.mdm.util.SplitUtils;
import com.holderzone.holder.saas.store.mdm.util.TriggerLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.holderzone.holder.saas.store.mdm.constant.ReqTypeConstant.POST;
import static com.holderzone.holder.saas.store.mdm.constant.ReqUrlConstants.Sku.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MdmSkuServiceImpl
 * @date 2019/11/23 下午4:33
 * @description //
 * @program holder
 */
@Slf4j
@Service
public class SkuAggServiceImpl implements SkuAggService {

    private final SkuService skuService;

    private final MdmOperation mdmOperation;

    private final MqUtils mqUtils;

    @Value("${mdm.initSyncStep:50}")
    private int initSyncStep;

    @Autowired
    public SkuAggServiceImpl(SkuService skuService, MdmOperation mdmOperation, MqUtils mqUtils) {
        this.skuService = skuService;
        this.mdmOperation = mdmOperation;
        this.mqUtils = mqUtils;
    }

    @Override
    public void triggerRemoteSku(MdmTriggerType mdmTriggerType) {
        List<SkuSyncDTO> skuSyncDTOS = skuService.shouldInitSkuDOS()
                .stream()
                .filter(skuDO -> skuDO.getIsDelete() == 0)
                .map(ItemMapStruct.INSTANCE::skuDo2skuSynDTO)
                .collect(Collectors.toList());

        TriggerLogUtils.pre("规格", skuSyncDTOS.size());

        switch (mdmTriggerType) {
            case CREATE:
                SplitUtils.splitList(skuSyncDTOS, initSyncStep).forEach(skuSyncDTOS1 -> {
                    try {
                        createRemoteSku(skuSyncDTOS1);
                    } catch (Exception e) {
                        TriggerLogUtils.stepFailed("规格", e);
                        mqUtils.sendMessage(
                                RocketMqConfig.StoreConfig.STORE_MDM_SKU_TOPIC,
                                RocketMqConfig.StoreConfig.STORE_MDM_SKU_CREATE_TAG,
                                skuSyncDTOS1, UserContextUtils.getEnterpriseGuid()
                        );
                    }
                    TriggerLogUtils.process("规格", skuSyncDTOS1.size());
                });
                break;
            case UPDATE:
                skuSyncDTOS.forEach(skuSync -> {
                    try {
                        updateRemoteSku(skuSync);
                    } catch (Exception e) {
                        TriggerLogUtils.singleFailed("规格", e);
                        mqUtils.sendMessage(
                                RocketMqConfig.StoreConfig.STORE_MDM_SKU_TOPIC,
                                RocketMqConfig.StoreConfig.STORE_MDM_SKU_UPDATE_TAG,
                                skuSync, UserContextUtils.getEnterpriseGuid()
                        );
                    }
                });
                TriggerLogUtils.process("规格", skuSyncDTOS.size());
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + mdmTriggerType);
        }

        TriggerLogUtils.post("规格", skuSyncDTOS.size());
    }

    @Override
    public void createRemoteSku(List<SkuSyncDTO> skuSyncDTOS) {
        for (SkuSyncDTO skuSyncDTO : skuSyncDTOS) {
            Optional.ofNullable(skuSyncDTO.getSalePrice())
                    .ifPresent(value -> skuSyncDTO.setSalePrice(BigDecimalUtils.quantityTrimmed(value)));
        }
        try {
            mdmOperation.doRequest(POST, BATCH_CREATE_SKU_URL, skuSyncDTOS);
        } catch (RepeatedException e) {
            if (skuSyncDTOS.size() == 1) {
                TriggerLogUtils.singleRepeated("规格");
            } else {
                TriggerLogUtils.batchRepeated("规格", e);
                for (SkuSyncDTO skuSyncDTO : skuSyncDTOS) {
                    mqUtils.sendMessage(
                            RocketMqConfig.StoreConfig.STORE_MDM_SKU_TOPIC,
                            RocketMqConfig.StoreConfig.STORE_MDM_SKU_CREATE_TAG,
                            Collections.singleton(skuSyncDTO), UserContextUtils.getEnterpriseGuid()
                    );
                }
            }
        } catch (BusinessException e) {
            if (skuSyncDTOS.size() == 1) {
                throw e;
            } else {
                TriggerLogUtils.batchFailed("规格", e);
                for (SkuSyncDTO skuSyncDTO : skuSyncDTOS) {
                    mqUtils.sendMessage(
                            RocketMqConfig.StoreConfig.STORE_MDM_SKU_TOPIC,
                            RocketMqConfig.StoreConfig.STORE_MDM_SKU_CREATE_TAG,
                            Collections.singleton(skuSyncDTO), UserContextUtils.getEnterpriseGuid()
                    );
                }
            }
        }
    }

    @Override
    public void updateRemoteSku(SkuSyncDTO skuSyncDTO) {
        Optional.ofNullable(skuSyncDTO.getSalePrice())
                .ifPresent(value -> skuSyncDTO.setSalePrice(BigDecimalUtils.quantityTrimmed(value)));
        mdmOperation.doRequest(POST, UPDATE_SKU_URL, skuSyncDTO);
    }

    @Override
    public void deleteRemoteSku(List<SkuSyncDTO> mdmSkuSynDTOS) {
        mdmOperation.doRequest(POST, BATCH_DELETE_SKU_URL, mdmSkuSynDTOS);

    }

    @Override
    public void createLocalSku(SkuSyncDTO skuSyncDTO) {
        SkuDO dbSku = skuService.getLocalRecord(new LambdaQueryWrapper<SkuDO>().eq(SkuDO::getGuid, skuSyncDTO.getGuid()));
        if (Objects.nonNull(dbSku)) {
            log.info("第三方创建规格信息同步，规格数据已存在");
            log.info("第三方数据：{}", JacksonUtils.writeValueAsString(skuSyncDTO));
            log.info("本地DB数据：{}", JacksonUtils.writeValueAsString(dbSku));
            return;
        }
        skuService.insertSku(skuSyncDTO);
    }

    @Override
    public void updateLocalSku(SkuSyncDTO skuSyncDTO) {
        SkuDO dbSku = skuService.getLocalRecord(new LambdaQueryWrapper<SkuDO>().eq(SkuDO::getGuid, skuSyncDTO.getGuid()));
        if (Objects.isNull(dbSku)) {
            log.info("第三方修改规格信息同步，规格信息不存在，第三方数据：{}", JacksonUtils.writeValueAsString(skuSyncDTO));
            skuService.insertSku(skuSyncDTO);
            return;
        }
        skuService.updateSkuByGuid(skuSyncDTO);
    }

    @Override
    public void deleteLocalSku(SkuSyncDTO skuSyncDTO) {
        SkuDO dbSku = skuService.getLocalRecord(new LambdaQueryWrapper<SkuDO>().eq(SkuDO::getGuid, skuSyncDTO.getGuid()));
        if (Objects.isNull(dbSku)) {
            log.info("第三方修改规格信息同步，规格信息不存在，第三方数据：{}", JacksonUtils.writeValueAsString(skuSyncDTO));
            return;
        }
        skuService.deleteSkuByGuid(skuSyncDTO.getGuid());

    }
}
