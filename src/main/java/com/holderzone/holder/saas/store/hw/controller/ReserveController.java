package com.holderzone.holder.saas.store.hw.controller;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.hw.service.ReserveService;
import com.holderzone.saas.store.dto.hw.*;
import com.holderzone.saas.store.reserve.api.dto.ReserveRecordDetailDTO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ReserveController
 * @date 2019/05/16 11:14
 * @description //TODO
 * @program holder-saas-store-hw
 */
@Slf4j
@RestController
@RequestMapping(value = "/reserve")
public class ReserveController {

    private final ReserveService reserveService;

    @Autowired
    public ReserveController(ReserveService reserveService) {
        this.reserveService = reserveService;
    }

    @PostMapping("/test_ivr_integer_0")
    @ApiOperation("测试IVR解码能力")
    public InterfaceTestDTO testIvrInteger0(@RequestBody @Validated RequestDTO<MchntValidateDTO> requestDTO) {
        log.info("test_ivr_integer_0，接口入参：{}", JacksonUtils.writeValueAsString(requestDTO));
        return new InterfaceTestDTO(0, "张敏宣的门店");
    }

    @PostMapping("/test_ivr_integer_200")
    @ApiOperation("测试IVR解码能力")
    public InterfaceTestDTO testIvrInteger200(@RequestBody @Validated RequestDTO<MchntValidateDTO> requestDTO) {
        log.info("test_ivr_integer_200，接口入参：{}", JacksonUtils.writeValueAsString(requestDTO));
        return new InterfaceTestDTO(200, "张敏宣的门店");
    }

    @PostMapping("/test_ivr_string_0")
    @ApiOperation("测试IVR解码能力")
    public ResultDTO testIvrString0(@RequestBody @Validated RequestDTO<MchntValidateDTO> requestDTO) {
        log.info("test_ivr_string_0，接口入参：{}", JacksonUtils.writeValueAsString(requestDTO));
        return new ResultDTO("0", "张敏宣的门店");
    }

    /**
     * TODO
     * 餐饮商户开户申请：流程如何？在哪里申请？线下or线上(商户后台)？需要什么资料？
     * 开通Saas业务帐号
     * 智能预订配置
     * SIP话机配置
     * 欢迎语语音配置
     * <p>
     * 能否为掌控者分配一个测试账户？
     *
     * @param requestDTO
     * @return
     */
    @PostMapping("/validate_service")
    @ApiOperation("验证商户是否开通智能预订服务")
    public ResultDTO validateService(@RequestBody @Validated RequestDTO<MchntValidateDTO> requestDTO) {
        log.info("验证商户是否开通智能预订服务，接口入参：{}", JacksonUtils.writeValueAsString(requestDTO));
        return reserveService.validateReservePhone(requestDTO.getParams());
    }

    @PostMapping("/validate_date")
    @ApiOperation("验证日期是否可用")
    public ResultDTO validateDateReturnInterval(@RequestBody @Validated RequestDTO<ReserveDateDTO> requestDTO) {
        log.info("验证日期是否可用，接口入参：{}", JacksonUtils.writeValueAsString(requestDTO));
        return reserveService.validateDateReturnInterval(requestDTO.getParams());
    }

    @PostMapping("/validate_interval")
    @ApiOperation("验证日期区间是否可用")
    public ResultDTO validateIntervalReturnTime(@RequestBody @Validated RequestDTO<ReserveIntervalDTO> requestDTO) {
        log.info("验证日期区间是否可用，接口入参：{}", JacksonUtils.writeValueAsString(requestDTO));
        return reserveService.validateIntervalReturnTime(requestDTO.getParams());
    }

    @PostMapping("/validate_date_time")
    @ApiOperation("验证日期时间是否可用")
    public ResultDTO validateDateTimeReturnNothing(@RequestBody @Validated RequestDTO<ReserveDateTimeDTO> requestDTO) {
        log.info("验证日期时间是否可用，接口入参：{}", JacksonUtils.writeValueAsString(requestDTO));
        return reserveService.validateDateTimeReturnNothing(requestDTO.getParams());
    }

    @PostMapping("/validate_people")
    @ApiOperation("验证人数是否可用")
    public ResultDTO validatePeopleReturnNothing(@RequestBody @Validated RequestDTO<ReservePeopleDTO> requestDTO) {
        log.info("验证人数是否可用，接口入参：{}", JacksonUtils.writeValueAsString(requestDTO));
        return reserveService.validatePeopleReturnNothing(requestDTO.getParams());
    }

    @PostMapping("/validate_room")
    @ApiOperation("验证房型是否可用")
    public ResultDTO validateRoomReturnRoom(@RequestBody @Validated RequestDTO<ReserveRoomDTO> requestDTO) {
        log.info("验证房型是否可用，接口入参：{}", JacksonUtils.writeValueAsString(requestDTO));
        return reserveService.validateRoomReturnRoom(requestDTO.getParams());
    }

    @PostMapping("/validate_all")
    @ApiOperation("验证所有条件")
    public ResultDTO validateAllReturnSuccess(@RequestBody @Validated RequestDTO<ReserveAllDTO> requestDTO) {
        log.info("验证所有条件，接口入参：{}", JacksonUtils.writeValueAsString(requestDTO));
        return reserveService.validateAllReturnSuccess(requestDTO.getParams());
    }

    @PostMapping("/launch")
    @ApiOperation("执行预订请求")
    public ResultDTO<ReserveRecordDetailDTO> launch(@RequestBody @Validated RequestDTO<ReserveLaunchDTO> requestDTO) {
        log.info("执行预订请求，接口入参：{}", JacksonUtils.writeValueAsString(requestDTO));
        ReserveRecordDetailDTO launch = reserveService.launch(requestDTO.getParams());
        return new ResultDTO<>("200", "预订成功", launch);
    }
}