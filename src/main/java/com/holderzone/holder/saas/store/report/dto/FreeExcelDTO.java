package com.holderzone.holder.saas.store.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2023-07-10
 * @description
 */
@Data
public class FreeExcelDTO {

    @Excel(name = "品牌", orderNum = "1", width = 15)
    public String brandName;

    @Excel(name = "门店", orderNum = "2", width = 15)
    public String storeName;

    @Excel(name = "商品名称", orderNum = "3", width = 15)
    public String goodsName;

    @Excel(name = "商品分类", orderNum = "4", width = 15)
    public String goodsCategories;

    @Excel(name = "赠送数量", orderNum = "5", width = 15)
    public BigDecimal givenQuantity;

    @Excel(name = "赠送金额", orderNum = "6", width = 15)
    public BigDecimal givenRefund;

    @Excel(name = "赠送占比", orderNum = "7", width = 15)
    public String givenRate;
}
