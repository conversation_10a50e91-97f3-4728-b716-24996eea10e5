package com.holderzone.holder.saas.store.table.service;

import com.holderzone.saas.store.dto.table.TableOrderCombineDTO;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;

/**
 * <AUTHOR>
 * @date 2024/9/9
 * @description 预定
 */
public interface ReserveService {

    /**
     * 预定拆台
     */
    void separate(TableOrderCombineDTO tableOrderCombineDTO);

    /**
     * 通知转台
     */
    void notifyTurn(TradeTableDTO tradeTableDTO);

    /**
     * 预订并台
     */
    void combine(TableOrderCombineDTO tableOrderCombineDTO);

}
