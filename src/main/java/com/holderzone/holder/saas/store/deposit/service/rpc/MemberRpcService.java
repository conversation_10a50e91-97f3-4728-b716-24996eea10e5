package com.holderzone.holder.saas.store.deposit.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.cmember.app.dto.account.response.SimpleMemberInfoDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(value = "holder-saas-member-terminal", fallbackFactory = MemberRpcService.ServiceFallBack.class)
public interface MemberRpcService {

    @GetMapping("/hsmca/member/getSimpleMemberInfo")
    List<SimpleMemberInfoDTO> getMemberInfo(@RequestParam("memberGuidOrCardNum") String memberGuidOrCardNum);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MemberRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public MemberRpcService create(Throwable throwable) {
            return new MemberRpcService() {
                @Override
                public List<SimpleMemberInfoDTO> getMemberInfo(String memberInfoGuid) {
                    log.error(HYSTRIX_PATTERN, "MemberRpcService",
                            JacksonUtils.writeValueAsString(memberInfoGuid),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}