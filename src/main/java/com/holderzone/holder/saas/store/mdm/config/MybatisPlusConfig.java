package com.holderzone.holder.saas.store.mdm.config;

import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.plugins.PerformanceInterceptor;
import com.holderzone.holder.saas.store.mdm.injector.FieldFillLogicSqlInjector;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
@MapperScan("com.holder.saas.print.mapper")
public class MybatisPlusConfig {

    /**
     * 分页插件
     * 与sdk中的PageInterceptor冲突了，一旦配置这个：
     * RoutingStatementHandler会创建代理类，PageInterceptor反射获取delegate就会获取不到
     */
//    @Bean
    public PaginationInterceptor paginationInterceptor() {
        return new PaginationInterceptor();
    }

    @Bean
    public FieldFillLogicSqlInjector sqlInjector() {
        return new FieldFillLogicSqlInjector();
    }

    //fixme 后期如果需要可以加上
//    @Bean
    @Profile({"dev", "test", "release"})
    public PerformanceInterceptor performanceInterceptor() {
        return new PerformanceInterceptor().setMaxTime(100).setFormat(true);
    }
}