package com.holderzone.holder.saas.store.mdm.pipeline.staff.inputs;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.slf4j.starter.anno.LogBefore;
import com.holderzone.framework.slf4j.starter.anno.LogLevel;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.UserSyncDTO;
import com.holderzone.holder.saas.store.mdm.event.AbsErpRocketMqConsumer;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.agg.UserAggService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MDMUserListener
 * @date 2019/11/18 11:24
 * @description
 * @program holder-saas-store
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.MainConfig.MAIN_MDM_USER_TOPIC,
        tags = {
                RocketMqConfig.MainConfig.MAIN_MDM_USER_CREATE_TAG,
                RocketMqConfig.MainConfig.MAIN_MDM_USER_UPDATE_TAG,
                RocketMqConfig.MainConfig.MAIN_MDM_USER_DELETE_TAG
        },
        consumerGroup = RocketMqConfig.MainConfig.MAIN_MDM_USER_GROUP
)
public class UserMdmConsumer extends AbsErpRocketMqConsumer<RocketMqTopic, String> {

    private final UserAggService userAggService;

    @Autowired
    public UserMdmConsumer(UserAggService userAggService) {
        this.userAggService = userAggService;
    }

    @Override
    @LogBefore(value = "测试", logLevel = LogLevel.INFO)
    public boolean doConsumeMsg(String json, MessageExt messageExt) {
        String tags = messageExt.getTags();
        switch (tags) {
            case RocketMqConfig.MainConfig.MAIN_MDM_USER_CREATE_TAG:
                userAggService.createLocalUser(JacksonUtils.toObject(UserSyncDTO.class, json));
                break;
            case RocketMqConfig.MainConfig.MAIN_MDM_USER_UPDATE_TAG:
                userAggService.updateLocalUser(JacksonUtils.toObject(UserSyncDTO.class, json));
                break;
            case RocketMqConfig.MainConfig.MAIN_MDM_USER_DELETE_TAG:
                userAggService.deleteLocalUser(JacksonUtils.toObject(UserSyncDTO.class, json));
                break;
            default:
                log.error("unknown mq tag : {}, message：{}", messageExt.getTags(), json);
                break;
        }
        return true;
    }
}
