package com.holderzone.holder.saas.store.client.entity.ddo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@ApiModel("昨日订单数据")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class YesterdayOrderDO {

    public static YesterdayOrderDO DEFAULT = new YesterdayOrderDO( 0, BigDecimal.ZERO,BigDecimal.ZERO);
    @ApiModelProperty(value = "支付订单数")
    private Integer payOrderCount;
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payAmount;
    @ApiModelProperty(value = "支付转化率")
    private BigDecimal paymentConversionRate;
    public static YesterdayOrderDO INSTANCE() {
        return new YesterdayOrderDO( 0, BigDecimal.ZERO,BigDecimal.ZERO);
    }
}
