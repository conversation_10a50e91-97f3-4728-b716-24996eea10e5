package com.holderzone.holder.saas.store.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.report.constant.CommonConstant;
import com.holderzone.holder.saas.store.report.dto.ReturnDetailItemExcelDTO;
import com.holderzone.holder.saas.store.report.mapper.TradeReturnMapper;
import com.holderzone.holder.saas.store.report.service.TradeReturnService;
import com.holderzone.holder.saas.store.report.util.ExcelUtils;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.base.Pager;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ReturnDetailItemDTO;
import com.holderzone.saas.store.dto.report.resp.ReturnItemDTO;
import com.holderzone.saas.store.dto.report.resp.TotalStatisticsDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;


@Service
@RequiredArgsConstructor
@Slf4j
public class TradeReturnServiceImpl implements TradeReturnService {

    private final TradeReturnMapper tradeReturnMapper;

    private final OssClient ossClient;

    private final ExecutorService reportQueryExecutor;

    @Override
    public Message<ReturnItemDTO> list(ReportQueryVO query) {
        Message<ReturnItemDTO> message = new Message<>();
        // 合计
        TotalStatisticsDTO statistics = tradeReturnMapper.statistics(query);
        if (Objects.isNull(statistics) || statistics.getTotalQuantity().compareTo(BigDecimal.ZERO) == 0) {
            Pager pager = new Pager(query.getCurrentPage(), query.getPageSize(), 0);
            message.setPager(pager);
            message.setList(Lists.newArrayList());
            message.setData(JacksonUtils.toMap(JacksonUtils.writeValueAsString(TotalStatisticsDTO.DEFAULT)));
            return message;
        }
        CompletableFuture<Integer> countFuture = CompletableFuture.supplyAsync(() -> {
            // 查询总条数
            return tradeReturnMapper.count(query);
        }, reportQueryExecutor).exceptionally(throwable -> {
            log.error("查询总条数失败", throwable);
            throw new BusinessException(throwable.getMessage());
        });

        CompletableFuture<List<ReturnItemDTO>> returnItemFuture = CompletableFuture.supplyAsync(() -> {
            // 查询列表
            return tradeReturnMapper.pageInfo(query);
        }, reportQueryExecutor).exceptionally(throwable -> {
            log.error("查询总条数失败", throwable);
            throw new BusinessException(throwable.getMessage());
        });
        CompletableFuture<Void> all = CompletableFuture.allOf(countFuture,returnItemFuture);
        try {
            all.get();
            List<ReturnItemDTO> list = returnItemFuture.get();
            BigDecimal totalReturnQuantity = statistics.getTotalQuantity();
            Iterator<ReturnItemDTO> iterator = list.iterator();
            while (iterator.hasNext()) {
                ReturnItemDTO returnRecord = iterator.next();
                // 计算退货率
                BigDecimal returnQuantity = returnRecord.getReturnQuantity();
                if (Objects.isNull(returnQuantity) || returnQuantity.compareTo(BigDecimal.ZERO) == 0) {
                    iterator.remove();
                    continue;
                }
                BigDecimal rate = returnQuantity.multiply(new BigDecimal(100)).divide(totalReturnQuantity, 2, RoundingMode.HALF_UP);
                returnRecord.setRefundRate(rate.toPlainString() + "%");
            }
            Pager pager = new Pager(query.getCurrentPage(), query.getPageSize(), countFuture.get());
            message.setPager(pager);
            message.setList(list);
            message.setData(JacksonUtils.toMap(JacksonUtils.writeValueAsString(statistics)));
            return message;
        }catch (Exception e){
            log.error("查询退款列表失败",e);
            return message;
        }
    }

    @Override
    public Message<ReturnDetailItemDTO> listDetail(ReportQueryVO query){
        Message<ReturnDetailItemDTO> message = new Message<>();
        checkParams(query);
        Integer count = tradeReturnMapper.countReturnDetail(query);
        if(count == 0){
            Pager pager = new Pager(query.getCurrentPage(), query.getPageSize(), count);
            message.setPager(pager);
            message.setList(Lists.newArrayList());
            message.setData(JacksonUtils.toMap(JacksonUtils.writeValueAsString(TotalStatisticsDTO.DEFAULT)));
            return message;
        }
        // 合计
        // 与产品测试共识：
        // 1.反结账后退原来的菜，未结账前后实退金额都是0，反结账前的团购券加商品除外，实退金额为券的面值
        TotalStatisticsDTO statistics = tradeReturnMapper.statistics(query);
        List<ReturnDetailItemDTO> returnDetailItemList = tradeReturnMapper.listReturnDetail(query);

        Pager pager = new Pager(query.getCurrentPage(), query.getPageSize(), count);
        message.setPager(pager);
        message.setList(returnDetailItemList);
        message.setData(JacksonUtils.toMap(JacksonUtils.writeValueAsString(statistics)));
        return message;
    }

    @Override
    public String exportReturnDetail(ReportQueryVO query){
        Integer count = tradeReturnMapper.countReturnDetail(query);
        if(count > CommonConstant.MAX_EXPORT){
            throw new BusinessException(CommonConstant.MAX_EXPORT_TITLE);
        }
        List<ReturnDetailItemExcelDTO> excelList = Lists.newArrayList();
        if(count > 0){
            query.setPageSize(CommonConstant.MAX_EXPORT);
            tradeReturnMapper.listReturnDetail(query).forEach(e -> {
                ReturnDetailItemExcelDTO returnDetailItemExcel = new ReturnDetailItemExcelDTO();
                BeanUtil.copyProperties(e,returnDetailItemExcel);
                returnDetailItemExcel.setReturnQuantity(returnDetailItemExcel.getReturnQuantity().setScale(0, RoundingMode.HALF_UP));
                returnDetailItemExcel.setRefund(returnDetailItemExcel.getRefund().setScale(2,RoundingMode.HALF_UP));
                excelList.add(returnDetailItemExcel);
            });
        }
        try {
            String sheetName = "退菜明细";
            String fileName = sheetName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return ExcelUtils.exportExcel(excelList,
                    null, sheetName, ReturnDetailItemExcelDTO.class, fileName, ossClient);
        } catch (Exception e) {
            throw new BusinessException("导出失败");
        }
    }

    private static final long MAX_DAY = 30;
    private static void checkParams(ReportQueryVO query) {
        if(ObjectUtil.isNull(query.getStartTime()) || ObjectUtil.isNull(query.getEndTime())){
            throw new BusinessException("请求时间不能为空");
        }
        long days =  query.getEndTime().toEpochDay() - query.getStartTime().toEpochDay();
        if(days > MAX_DAY){
            throw new BusinessException("请求时间不能超过一个月");
        }
    }
}
