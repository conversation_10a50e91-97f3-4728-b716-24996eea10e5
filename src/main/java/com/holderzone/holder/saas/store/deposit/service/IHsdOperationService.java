package com.holderzone.holder.saas.store.deposit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.deposit.entity.bo.OperationDO;
import com.holderzone.saas.store.dto.deposit.req.OperationCreateReqDTO;
import com.holderzone.saas.store.dto.deposit.req.OperationHistoryQueryReqDTO;
import com.holderzone.saas.store.dto.deposit.resp.OperationQueryRespDTO;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-22
 */
public interface IHsdOperationService extends IService<OperationDO> {

    /**
     * 创建操作记录
     *
     * @param operationCreateReqDTO
     * @return
     */
    Boolean createOperationRecord(OperationCreateReqDTO operationCreateReqDTO, String operationGuid,String depositGuid);

    /**
     * 查询操作记录
     *
     * @param operationHistoryQueryReqDTO
     * @return
     */
    Page<OperationQueryRespDTO> queryOperationHistory(OperationHistoryQueryReqDTO operationHistoryQueryReqDTO);
}
