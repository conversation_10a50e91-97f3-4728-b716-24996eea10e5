package com.holderzone.holder.saas.store.message.controller;


import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.message.service.MessageService;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.message.MsgInfoRespDTO;
import com.holderzone.saas.store.dto.message.MsgQuery;
import com.holderzone.saas.store.dto.message.MsgRespDTO;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MessageController
 * @date 2018/09/04 16:55
 * @description
 * @program holder-saas-bussiness-message
 */
@RestController
@RequestMapping
public class MessageController {

    private static final Logger logger = LoggerFactory.getLogger(MessageController.class);

    private final MessageService messageService;

    @Autowired
    public MessageController(MessageService messageService) {
        this.messageService = messageService;
    }

    @PostMapping("/msg")
    @ApiOperation(value = "接受消息的接口", notes = "返回success，表示推送成功", response = String.class)
    public String msg(@RequestBody BusinessMessageDTO businessMessageDTO) {
        logger.info("业务系统推送消息，message={}", JacksonUtils.writeValueAsString(businessMessageDTO));
        return messageService.insert(businessMessageDTO);
    }

    @PostMapping("/all/msg")
    @ApiOperation(value = "批量接受消息", notes = "返回success，表示推送成功", response = String.class)
    public String allMsg(@RequestBody List<BusinessMessageDTO> businessMessageDTOS) {
        logger.info("业务系统推送批量消息,message={}", JacksonUtils.writeValueAsString(businessMessageDTOS));
        return messageService.insertAll(businessMessageDTOS);
    }

    @PostMapping("/all")
    @ApiOperation(value = "查询所有消息的info", response = MsgInfoRespDTO.class)
    public Page<MsgInfoRespDTO> msg(@RequestBody MsgQuery msgQuery) {
        logger.info("查询消息列表 msgQuery={}", JacksonUtils.writeValueAsString(msgQuery));
        return messageService.queryAll(msgQuery);
    }

    @PostMapping("/detail")
    @ApiOperation(value = "查询详情", notes = "必须传入guid和state，查询一次标记为已读状态", response = MsgRespDTO.class)
    public MsgRespDTO detail(@RequestBody MsgQuery msgQuery) {
        logger.info("查询详情，msgQuery{}", JacksonUtils.writeValueAsString(msgQuery));
        return messageService.queryDetail(msgQuery.getMessageGuid(), msgQuery.getState());
    }

    @PostMapping("/count")
    public int getCount(@RequestBody MsgQuery msgQuery) {
        logger.info("查询门店未读消息数量，msgQuery{}", JacksonUtils.writeValueAsString(msgQuery));
        return messageService.getCount(msgQuery);
    }

    @PostMapping("/clean")
    public String clean() {
        logger.info("定期删除消息触发 time={}", DateTimeUtils.now().toString());
        return messageService.clean();
    }

    @GetMapping("/read_all")
    @ApiOperation(value = "一键已读")
    public void readAll(@RequestParam("storeGuid") String storeGuid,
                        @RequestParam("messageType") Integer messageType) {
        messageService.readAll(storeGuid, messageType);
    }

}
