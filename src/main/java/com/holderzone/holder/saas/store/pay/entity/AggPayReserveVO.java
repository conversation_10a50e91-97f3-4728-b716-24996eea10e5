package com.holderzone.holder.saas.store.pay.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * {@link AggPayReserveVO}
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/2/25 下午12:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AggPayReserveVO {
    private String appId;

    private String attachData;

    private String developerId;

    private String orderGUID;

    private String payGUID;

    private String signature;

    private Long timestamp;
}
