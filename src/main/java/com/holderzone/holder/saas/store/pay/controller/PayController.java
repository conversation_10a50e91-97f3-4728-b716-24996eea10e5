package com.holderzone.holder.saas.store.pay.controller;

import com.holderzone.framework.util.IDUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.pay.service.PayRecordService;
import com.holderzone.saas.store.dto.member.pay.MemberQuickPayDTO;
import com.holderzone.saas.store.dto.pay.AggPayCallbackDTO;
import com.holderzone.holder.saas.store.pay.service.AggPayService;
import com.holderzone.holder.saas.store.pay.utils.ValidatorUtils;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.pay.*;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/08/08 16:40
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/agg")
public class PayController {

    private final AggPayService aggPayService;

    private final PayRecordService payRecordService;


    @PostMapping("/pay")
    @ApiOperation(value = "聚合支付预下单接口")
    public Mono<AggPayRespDTO> pay(@RequestBody SaasAggPayDTO saasAggPayDTO) {
        log.info("聚合支付预下单入参：{}", JacksonUtils.writeValueAsString(saasAggPayDTO));
        if (saasAggPayDTO.getIsQuickReceipt()) {
            // 必传 amount, authCode, terminalId
            AggPayPreTradingReqDTO reqDTO = saasAggPayDTO.getReqDTO();
            reqDTO.setOrderGUID(IDUtils.nextId());
            reqDTO.setPayGUID(IDUtils.nextId());
            reqDTO.setGoodsName("快速收款");
            reqDTO.setBody("快速收款");
            reqDTO.setDescription("快速收款");
            reqDTO.setExtra("快速收款");
            reqDTO.setCurrency("RMB");
            reqDTO.setAttachData(saasAggPayDTO.getEnterpriseGuid() + ":" + saasAggPayDTO.getStoreGuid());
            reqDTO.setEnterpriseName(saasAggPayDTO.getEnterpriseName());
            reqDTO.setStoreName(saasAggPayDTO.getStoreName());
        }
        ValidatorUtils.validatePrePay(saasAggPayDTO);
        return aggPayService.prePay(saasAggPayDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/polling")
    @ApiOperation(value = "聚合支付轮询接口")
    public Mono<AggPayPollingRespDTO> polling(@RequestBody SaasPollingDTO saasPollingDTO) {
        log.info("聚合支付轮询入参：{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        ValidatorUtils.validatePolling(saasPollingDTO);
        return aggPayService.prePayPolling(saasPollingDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/h5/polling")
    @ApiOperation(value = "聚合支付轮询接口")
    public Mono<AggPayPollingRespDTO> h5Polling(@RequestBody SaasPollingDTO saasPollingDTO) {
        log.info("h5支付聚合支付轮询入参：{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        ValidatorUtils.validatePolling(saasPollingDTO);
        return aggPayService.prePayH5Polling(saasPollingDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/callback")
    @ApiOperation(value = "聚合支付回调")
    public Mono<String> callback(@RequestBody @Validated AggPayCallbackDTO aggPayCallbackDTO) {
        log.info("聚合支付回调入参：{}", JacksonUtils.writeValueAsString(aggPayCallbackDTO));
        return aggPayService.callback(aggPayCallbackDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/reserve")
    @ApiOperation(value = "聚合支付撤销接口")
    public Mono<AggPayReserveResultDTO> reserve(@RequestBody @Validated SaasPollingDTO saasPollingDTO) {
        log.info("聚合支付撤销入参：{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        return aggPayService.reservePay(saasPollingDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/refund")
    @ApiOperation(value = "聚合支付退款接口")
    public Mono<AggRefundRespDTO> refund(@RequestBody SaasAggRefundDTO saasAggRefundDTO) {
        log.info("聚合支付退款入参：{}", JacksonUtils.writeValueAsString(saasAggRefundDTO));
        if (Boolean.TRUE.equals(saasAggRefundDTO.getIsQuickReceipt())) {
            // 必传 payGUID, orderGUID, refundFee
            AggRefundReqDTO aggRefundReqDTO = saasAggRefundDTO.getAggRefundReqDTO();
            aggRefundReqDTO.setRefundType(0);
            aggRefundReqDTO.setReason("快速退款");
            aggRefundReqDTO.setAttachData(saasAggRefundDTO.getEnterpriseGuid());
        }
        ValidatorUtils.validateRefund(saasAggRefundDTO);
        return aggPayService.refund(saasAggRefundDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/refund/polling")
    @ApiOperation(value = "聚合支付退款轮询接口：" +
            "这个列表为空，表示退款中\n" +
            "这个列表不为空，且最后一个index所对应实体中的status=1表示退款成功，status=2表示退款失败，status=3表示退款中")
    public Mono<AggRefundPollingRespDTO> refundPolling(@RequestBody SaasPollingDTO saasPollingDTO) {
        log.info("聚合支付退款轮询入参：{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        ValidatorUtils.validateRefundPolling(saasPollingDTO);
        return aggPayService.refundPolling(saasPollingDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/query")
    @ApiOperation(value = "聚合支付查询支付结果接口")
    public Mono<AggPayPollingRespDTO> query(@RequestBody SaasPollingDTO saasPollingDTO) {
        log.info("聚合支付查询支付结果入参：{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        ValidatorUtils.validateQueryResult(saasPollingDTO);
        return aggPayService.queryPrePayResult(saasPollingDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/queryPaySt")
    @ApiOperation(value = "聚合支付查询支付状态接口")
    public Mono<AggPayPollingRespDTO> queryPaySt(@RequestBody SaasPollingDTO saasPollingDTO) {
        log.info("聚合支付查询支付结果入参：{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        ValidatorUtils.validateQueryResult(saasPollingDTO);
        return aggPayService.queryPayState(saasPollingDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/wechat/public")
    @ApiOperation(value = "微信公众号支付接口")
    public Mono<String> weChatPublic(@RequestBody SaasAggWeChatPublicAccountPayDTO publicAccountPayDTO) {
        log.info("微信公众号支付入参：{}", JacksonUtils.writeValueAsString(publicAccountPayDTO));
        ValidatorUtils.validatorWxPubAccPay(publicAccountPayDTO);
        return aggPayService.weChatPublic(publicAccountPayDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/wechat/public/polling")
    @ApiOperation(value = "微信公众号支付轮询接口")
    public Mono<AggPayPollingRespDTO> pollingWeChatPublic(@RequestBody SaasPollingDTO saasPollingDTO) {
        log.info("微信公众号支付轮询入参：{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        ValidatorUtils.validatorWxPubAccPolling(saasPollingDTO);
        return aggPayService.weChatPublicPolling(saasPollingDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/wechat/public/query")
    @ApiOperation(value = "微信公众号支付查询支付结果接口")
    public Mono<AggPayPollingRespDTO> queryWeChatPublic(@RequestBody SaasPollingDTO saasPollingDTO) {
        log.info("微信公众号支付查询支付结果入参：{}", JacksonUtils.writeValueAsString(saasPollingDTO));
        ValidatorUtils.validatorWxPubAccPolling(saasPollingDTO);
        return aggPayService.queryWxPubAccResult(saasPollingDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/record")
    @ApiOperation(value = "支付记录接口")
    public Mono<Page<AggPayRecordDTO>> queryPayRecord(@RequestBody BasePageDTO basePageDTO) {
        log.info("支付记录接口入参：{}", JacksonUtils.writeValueAsString(basePageDTO));
        ValidatorUtils.validatorPayRecord(basePageDTO);
        return aggPayService.queryPayRecord(basePageDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/record/statistics")
    @ApiOperation(value = "支付记录接口 包含支付记录和统计总金额")
    public Mono<AggPayStatisticsDTO> queryPayStatistics(@RequestBody BasePageDTO basePageDTO) {
        log.info("支付记录接口入参：{}", JacksonUtils.writeValueAsString(basePageDTO));
        ValidatorUtils.validatorPayRecord(basePageDTO);
        return aggPayService.queryPayStatistics(basePageDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/member/record")
    @ApiOperation(value = "保存会员快速收款记录")
    public Mono<String> saveMemberQuickPayRecord(@RequestBody MemberQuickPayDTO memberQuickPayDTO) {
        log.info("保存会员快速收款记录入参：{}", JacksonUtils.writeValueAsString(memberQuickPayDTO));
        return payRecordService.saveMemberQuickPayRecord(memberQuickPayDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/member/record/refund")
    @ApiOperation(value = "会员快速收款退款")
    public Mono<String> refundMemberQuickPayRecord(@RequestBody MemberQuickPayDTO memberQuickPayDTO) {
        log.info("会员快速收款退款入参：{}", JacksonUtils.writeValueAsString(memberQuickPayDTO));
        return payRecordService.refundMemberQuickPayRecord(memberQuickPayDTO).subscribeOn(Schedulers.elastic());
    }

    @PostMapping("/record/quickPay/statistics")
    @ApiOperation(value = "快速收款统计")
    public Mono<List<QuickPayStatisticsRespDTO>> queryQuickPayStatistics(@RequestBody QuickPayStatisticsReqDTO request) {
        log.info("[快速收款统计]入参,request={}", JacksonUtils.writeValueAsString(request));
        return payRecordService.queryQuickPayStatistics(request).subscribeOn(Schedulers.elastic());
    }

}
