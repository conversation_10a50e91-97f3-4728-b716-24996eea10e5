package com.holderzone.holder.saas.store.hw.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className OrganizationService
 * @date 19-1-8 下午4:02
 * @description 服务间调用-组织相关服务
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-organization", fallbackFactory = OrganizationService.ServiceFallBack.class)
public interface OrganizationService {

    @PostMapping("/store/query_store_by_guid")
    StoreDTO queryStoreByGuid(@RequestParam("storeGuid") String storeGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<OrganizationService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public OrganizationService create(Throwable cause) {
            return new OrganizationService() {

                @Override
                public StoreDTO queryStoreByGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "queryStoreByGuid", storeGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}