package com.holderzone.holder.saas.store.report.mapper;

import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.ReturnDetailItemDTO;
import com.holderzone.saas.store.dto.report.resp.ReturnItemDTO;
import com.holderzone.saas.store.dto.report.resp.TotalStatisticsDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单退菜
 */
@Mapper
public interface TradeReturnMapper {

    TotalStatisticsDTO statistics(@Param("query") ReportQueryVO query);

    List<ReturnItemDTO> pageInfo(@Param("query") ReportQueryVO query);

    Integer count(@Param("query") ReportQueryVO query);

    List<ReturnDetailItemDTO> listReturnDetail(@Param("query") ReportQueryVO query);

    Integer countReturnDetail(@Param("query") ReportQueryVO query);

}
