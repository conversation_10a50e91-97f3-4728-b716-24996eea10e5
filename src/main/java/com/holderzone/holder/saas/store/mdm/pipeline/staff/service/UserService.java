package com.holderzone.holder.saas.store.mdm.pipeline.staff.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.UserSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.domain.UserDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UserService
 * @date 2019/11/20 13:44
 * @description 用户信息同步Service
 * @program holder-saas-store
 */
public interface UserService extends IService<UserDO> {

    UserDO insertUser(UserSyncDTO userSyncDTO);

    void updateUserByGuid(UserSyncDTO userSyncDTO);

    void deleteUserByGuid(String guid);

    UserDO getLocalRecord(Wrapper<UserDO> queryWrapper);

    List<UserDO> shouldInitUserList();

}
