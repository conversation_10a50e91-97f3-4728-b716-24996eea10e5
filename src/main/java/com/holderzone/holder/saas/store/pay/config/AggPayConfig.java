package com.holderzone.holder.saas.store.pay.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPayConfig
 * @date 2019/03/14 14:49
 * @description 请求聚合支付的path配置
 * @program holder-saas-store-trading-center
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "agg-pay")
public class AggPayConfig {

    private String pay;

    private String polling;

    private String query;

    private String refund;

    private String reserve;

    private String refundPolling;

    private String refundQuery;

    private String mchntQuery;

    private Integer pollingTimes;

    private String wechath5pay;

    private String wechath5polling;

    private String callBack;

    private String queryPaySt;
}
