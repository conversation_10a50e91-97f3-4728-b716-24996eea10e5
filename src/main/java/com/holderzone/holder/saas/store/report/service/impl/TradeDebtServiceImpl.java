
package com.holderzone.holder.saas.store.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.report.constant.CommonConstant;
import com.holderzone.holder.saas.store.report.dto.DebtUnitRecordExcelDTO;
import com.holderzone.holder.saas.store.report.mapper.TradeDebtMapper;
import com.holderzone.holder.saas.store.report.service.TradeDebtService;
import com.holderzone.holder.saas.store.report.util.ExcelUtils;
import com.holderzone.saas.store.dto.report.base.Message;
import com.holderzone.saas.store.dto.report.base.Pager;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;


@Service
@RequiredArgsConstructor
public class TradeDebtServiceImpl implements TradeDebtService {

    private final TradeDebtMapper tradeDebtMapper;

    private final OssClient ossClient;

    private static final String MONEY_FULL = "￥";

    private static final String EMPTY_FULL = "-";

    @Override
    public Message<DebtUnitRecordDTO> list(ReportQueryVO query) {
        checkParams(query);
        Message<DebtUnitRecordDTO> debtUnitRecordMessage = new Message<>();
        // 合计
        DebtTotalStatisticsDTO statistics = tradeDebtMapper.statistics(query);
        if (Objects.isNull(statistics) || statistics.getTotal() == 0) {
            Pager pager = new Pager(query.getCurrentPage(), query.getPageSize(), 0);
            debtUnitRecordMessage.setPager(pager);
            debtUnitRecordMessage.setList(Lists.newArrayList());
            debtUnitRecordMessage.setData(JacksonUtils.toMap(JacksonUtils.writeValueAsString(DebtTotalStatisticsDTO.buildEmpty())));
            return debtUnitRecordMessage;
        }
        // 查询列表
        List<DebtUnitRecordDTO> list = tradeDebtMapper.pageInfo(query);
        Pager pager = new Pager(query.getCurrentPage(), query.getPageSize(), statistics.getTotal());
        debtUnitRecordMessage.setPager(pager);
        debtUnitRecordMessage.setList(list);
        debtUnitRecordMessage.setData(JacksonUtils.toMap(JacksonUtils.writeValueAsString(statistics)));
        return debtUnitRecordMessage;
    }


    @Override
    public String export(ReportQueryVO query) {
        Integer count = tradeDebtMapper.count(query);
        if (count > CommonConstant.MAX_EXPORT) {
            throw new BusinessException(CommonConstant.MAX_EXPORT_TITLE);
        }
        List<DebtUnitRecordExcelDTO> excelList = Lists.newArrayList();
        if (count > 0) {
            query.setCurrentPage(1);
            query.setPageSize(CommonConstant.MAX_EXPORT);
            tradeDebtMapper.pageInfo(query).forEach(e -> {
                DebtUnitRecordExcelDTO debtUnitRecordExcelDTO = new DebtUnitRecordExcelDTO();
                BeanUtil.copyProperties(e, debtUnitRecordExcelDTO);
                // 导出字段处理
                debtRecordExportHandler(e, debtUnitRecordExcelDTO);
                excelList.add(debtUnitRecordExcelDTO);
            });
        }
        try {
            String sheetName = "挂账明细";
            String fileName = sheetName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return ExcelUtils.exportExcel(excelList,
                    null, sheetName, DebtUnitRecordExcelDTO.class, fileName, ossClient);
        } catch (Exception e) {
            throw new BusinessException("导出失败");
        }
    }

    /**
     * 参数校验
     */
    private void checkParams(ReportQueryVO query) {
        if (ObjectUtil.isNull(query.getStartTime()) || ObjectUtil.isNull(query.getEndTime())) {
            throw new BusinessException(CommonConstant.REQUEST_TIME_NOT_EMPTY);
        }
        long days = query.getEndTime().toEpochDay() - query.getStartTime().toEpochDay();
        if (days > CommonConstant.QUERY_MAX_DAY) {
            throw new BusinessException(CommonConstant.REQUEST_TIME_EXCEED_A_MONTH);
        }
    }

    /**
     * 导出字段处理
     */
    private void debtRecordExportHandler(DebtUnitRecordDTO debtUnitRecordDTO, DebtUnitRecordExcelDTO debtUnitRecordExcelDTO) {
        debtUnitRecordExcelDTO.setDebtFee(MONEY_FULL + debtUnitRecordDTO.getDebtFee().stripTrailingZeros().toPlainString());
        debtUnitRecordExcelDTO.setRepaymentFee(EMPTY_FULL);
        debtUnitRecordExcelDTO.setGmtCreate(EMPTY_FULL);
        debtUnitRecordExcelDTO.setGmtModified(EMPTY_FULL);
        debtUnitRecordExcelDTO.setCreateStaffName(EMPTY_FULL);
        debtUnitRecordExcelDTO.setUpdateStaffName(EMPTY_FULL);
        if (debtUnitRecordDTO.getRepaymentStatus() == 1) {
            debtUnitRecordExcelDTO.setRepaymentFee(MONEY_FULL + debtUnitRecordDTO.getRepaymentFee().stripTrailingZeros().toPlainString());
        }
        if (Objects.nonNull(debtUnitRecordDTO.getGmtCreate())) {
            debtUnitRecordExcelDTO.setGmtCreate(debtUnitRecordDTO.getGmtCreate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (Objects.nonNull(debtUnitRecordDTO.getGmtModified())) {
            debtUnitRecordExcelDTO.setGmtModified(debtUnitRecordDTO.getGmtModified().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (StringUtils.isNotEmpty(debtUnitRecordDTO.getCreateStaffName())) {
            debtUnitRecordExcelDTO.setCreateStaffName(debtUnitRecordDTO.getCreateStaffName());
        }
        if (StringUtils.isNotEmpty(debtUnitRecordDTO.getUpdateStaffName())) {
            debtUnitRecordExcelDTO.setUpdateStaffName(debtUnitRecordDTO.getUpdateStaffName());
        }
    }
}
