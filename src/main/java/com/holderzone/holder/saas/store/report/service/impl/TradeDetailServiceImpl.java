package com.holderzone.holder.saas.store.report.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.oss.sdk.facde.OssClient;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.report.constant.CommonConstant;
import com.holderzone.holder.saas.store.report.constant.ErrorConstant;
import com.holderzone.holder.saas.store.report.dto.GrouponTradeDetailExcelDTO;
import com.holderzone.holder.saas.store.report.dto.PaymentConstituteSingleDaySingleExcelDTO;
import com.holderzone.holder.saas.store.report.dto.PaymentConstituteSingleExcelDTO;
import com.holderzone.holder.saas.store.report.dto.PaymentConstituteSummarizingExcelDTO;
import com.holderzone.holder.saas.store.report.dto.TakeawayTradeDetailExcelDTO;
import com.holderzone.holder.saas.store.report.mapper.TradeDetailMapper;
import com.holderzone.holder.saas.store.report.service.TradeDetailService;
import com.holderzone.holder.saas.store.report.service.rpc.organization.OrganizationService;
import com.holderzone.holder.saas.store.report.util.BigDecimalUtil;
import com.holderzone.holder.saas.store.report.util.DateUtils;
import com.holderzone.holder.saas.store.report.util.ExcelUtils;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.report.query.AggPayServiceChargeQueryDTO;
import com.holderzone.saas.store.dto.report.query.CloudPayConstituteQueryDTO;
import com.holderzone.saas.store.dto.report.query.PaymentConstituteQueryDTO;
import com.holderzone.saas.store.dto.report.query.TradeDetailQueryDTO;
import com.holderzone.saas.store.dto.report.resp.*;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderActivityDetailDTO;
import com.holderzone.saas.store.dto.trade.constant.PayPowerId;
import com.holderzone.saas.store.enums.report.CouponSourceEnum;
import com.holderzone.saas.store.enums.report.ShowTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class TradeDetailServiceImpl implements TradeDetailService {

    private static final String YYYY_MM_DD = "yyyy-MM-dd";

    private final TradeDetailMapper tradeDetailMapper;

    private final OssClient ossClient;

    private final OrganizationService organizationService;

    @Override
    public Page<GrouponTradeDetailRespDTO> pageGroupon(TradeDetailQueryDTO query) {
        Long total = tradeDetailMapper.countGroupon(query);
        if (total == 0) {
            return new Page<>(query.getCurrentPage(), query.getPageSize(), 0, null);
        }
        List<GrouponTradeDetailRespDTO> detailResp = tradeDetailMapper.pageGroupon(query);
        setCouponSellPrice(detailResp);
        return new Page<>(query.getCurrentPage(), query.getPageSize(), total, detailResp);
    }

    @Override
    public Page<TakeawayTradeDetailRespDTO> pageTakeaway(TradeDetailQueryDTO query) {
        Long total = tradeDetailMapper.countTakeaway(query);
        if (total == 0) {
            return new Page<>(query.getCurrentPage(), query.getPageSize(), 0, null);
        }
        List<TakeawayTradeDetailRespDTO> detailResp = tradeDetailMapper.pageTakeaway(query);
        return new Page<>(query.getCurrentPage(), query.getPageSize(), total, detailResp);
    }

    @Override
    public String exportGroupon(TradeDetailQueryDTO query) {
        Long total = tradeDetailMapper.countGroupon(query);
        if (total > 20000) {
            throw new BusinessException("最多只支持2万条数据导出");
        }
        List<GrouponTradeDetailRespDTO> detailResp = tradeDetailMapper.listGroupon(query);
        setCouponSellPrice(detailResp);
        List<GrouponTradeDetailExcelDTO> excelDTOList = buildGrouponExcelDTOList(detailResp);
        try {
            String sheetName = "团购结算明细";
            String fileName = sheetName + LocalDateTime.now().format(DateTimeFormatter.ofPattern(YYYY_MM_DD));
            return ExcelUtils.exportExcel(excelDTOList, null, sheetName, GrouponTradeDetailExcelDTO.class, fileName, ossClient);
        } catch (Exception e) {
            throw new BusinessException("导出失败");
        }
    }

    private void setCouponSellPrice(List<GrouponTradeDetailRespDTO> detailResp) {
        detailResp.forEach(resp -> {
            if (Objects.nonNull(resp.getCouponBuyPrice())
                    && !StringUtils.isEmpty(resp.getBizCost())
                    && !resp.getBizCost().equals("-")) {
                BigDecimal bizCost = BigDecimal.ZERO;
                try {
                    bizCost = new BigDecimal(resp.getBizCost());
                } catch (Exception e) {
                    log.error("bizCost转化BigDecimal失败", e);
                }
                resp.setCouponSellPrice(resp.getCouponBuyPrice().add(bizCost));
            }
        });
    }

    @Override
    public String exportTakeaway(TradeDetailQueryDTO query) {
        Long total = tradeDetailMapper.countTakeaway(query);
        if (total > 20000) {
            throw new BusinessException("最多只支持2万条数据导出");
        }
        List<TakeawayTradeDetailRespDTO> detailResp = tradeDetailMapper.listTakeaway(query);
        List<TakeawayTradeDetailExcelDTO> excelDTOList = buildTakeawayExcelDTOList(detailResp);
        try {
            String sheetName = "外卖结算明细";
            String fileName = sheetName + LocalDateTime.now().format(DateTimeFormatter.ofPattern(YYYY_MM_DD));
            return ExcelUtils.exportExcel(excelDTOList, null, sheetName, TakeawayTradeDetailExcelDTO.class, fileName, ossClient);
        } catch (Exception e) {
            throw new BusinessException("导出失败");
        }
    }


    private List<GrouponTradeDetailExcelDTO> buildGrouponExcelDTOList(List<GrouponTradeDetailRespDTO> detailResp) {
        List<GrouponTradeDetailExcelDTO> excelDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(detailResp)) {
            return excelDTOList;
        }
        for (GrouponTradeDetailRespDTO grouponTradeDetailRespDTO : detailResp) {
            GrouponTradeDetailExcelDTO excelDTO = new GrouponTradeDetailExcelDTO();
            BeanUtils.copyProperties(grouponTradeDetailRespDTO, excelDTO);
            excelDTO.setCouponSource(CouponSourceEnum.getDesc(grouponTradeDetailRespDTO.getSource()));
            excelDTOList.add(excelDTO);
        }
        return excelDTOList;
    }

    private List<TakeawayTradeDetailExcelDTO> buildTakeawayExcelDTOList(List<TakeawayTradeDetailRespDTO> detailResp) {
        List<TakeawayTradeDetailExcelDTO> excelDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(detailResp)) {
            return excelDTOList;
        }
        for (TakeawayTradeDetailRespDTO takeawayTradeDetailRespDTO : detailResp) {
            TakeawayTradeDetailExcelDTO excelDTO = new TakeawayTradeDetailExcelDTO();
            BeanUtils.copyProperties(takeawayTradeDetailRespDTO, excelDTO);
            if (!StringUtils.isEmpty(excelDTO.getActivityDetails())) {
                List<TakeoutOrderActivityDetailDTO> activities = JacksonUtils.toObjectList(TakeoutOrderActivityDetailDTO.class, excelDTO.getActivityDetails());
                StringBuilder sb = new StringBuilder();
                for (TakeoutOrderActivityDetailDTO activity : activities) {
                    sb.append("活动名称：").append(activity.getChargeDesc()).append(", 活动金额：").append(activity.getChargeAmount()).append(" \n");
                }
                excelDTO.setActivityDetails(sb.toString());
            }
            excelDTO.setRemark("--");
            excelDTOList.add(excelDTO);
        }
        return excelDTOList;
    }

    /**
     * 由于返回的数据受所选时间段和传入门店影响，分页为手动处理
     */
    @Override
    public Page<PaymentConstituteRespDTO> paymentConstitute(PaymentConstituteQueryDTO query) {
        List<PaymentConstituteRespDTO> respDTOList = new ArrayList<>();
        int startIndex = (int) ((query.getCurrentPage() - 1) * query.getPageSize());
        if (ShowTypeEnum.SUMMARIZING.getCode() == query.getShowType()) {
            return getSummarizingPaymentConstitutePage(query, startIndex, respDTOList);
        }
        if (ShowTypeEnum.SINGLE.getCode() == query.getShowType()) {
            return getSinglePaymentConstitutePage(query, startIndex, respDTOList);
        }
        return getSingleDaySinglePaymentConstitutePage(query, startIndex, respDTOList);
    }

    private Page<PaymentConstituteRespDTO> getSingleDaySinglePaymentConstitutePage(PaymentConstituteQueryDTO query,
                                                                                   int startIndex,
                                                                                   List<PaymentConstituteRespDTO> respDTOList) {
        List<String> storeGuidList = query.getStoreGuidList();
        List<LocalDate> dateList = DateUtils.convertToDateList(query.getStartTime(), query.getEndTime());
        List<Object[]> storeGuidAndDateList = storeGuidList.stream().flatMap(storeGuid -> dateList.stream()
                        .map(date -> new Object[]{storeGuid, date}))
                .collect(Collectors.toList());
        int totalSize = storeGuidAndDateList.size();
        int endIndex = (int) Math.min(startIndex + query.getPageSize(), totalSize);
        storeGuidAndDateList = storeGuidAndDateList.subList(startIndex, endIndex);
        storeGuidList = storeGuidAndDateList.stream().map(item -> (String) item[0]).distinct().collect(Collectors.toList());
        List<StoreDTO> storeDTOList = organizationService.queryStoreAndBrandByIdList(storeGuidList);
        if (org.springframework.util.CollectionUtils.isEmpty(storeDTOList)) {
            log.warn(ErrorConstant.STORE_INFO_EMPTY, storeGuidList);
            return new Page<>(query.getCurrentPage(), query.getPageSize(), 0, respDTOList);
        }
        Map<String, StoreDTO> storeDTOMap = storeDTOList.stream()
                .collect(Collectors.toMap(StoreDTO::getGuid, Function.identity(), (v1, v2) -> v2));
        // 构造分页后的单日单店查询对象
        PaymentConstituteQueryDTO singleDaySingleQuery = buildSingleQuery(query, storeGuidList);
        List<PaymentConstituteDTO> singleDaySinglePaymentDTOList = tradeDetailMapper.singleDaySinglePaymentConstitute(singleDaySingleQuery);
        handleAggPayMethod(singleDaySinglePaymentDTOList, ShowTypeEnum.SINGLE_DAY.getCode());
        log.info("[单日单店查询收款数据]singleDaySinglePaymentDTOList={}", JacksonUtils.writeValueAsString(singleDaySinglePaymentDTOList));
        Map<String, Map<LocalDate, List<PaymentConstituteDTO>>> storeGuidAndDateMap = singleDaySinglePaymentDTOList.stream()
                .collect(Collectors.groupingBy(PaymentConstituteDTO::getStoreGuid,
                        Collectors.groupingBy(PaymentConstituteDTO::getBusinessDate)));
        handleSingleDaySingleData(respDTOList, storeGuidAndDateList, storeDTOMap, storeGuidAndDateMap);
        // 处理合计
        handleTotalData(respDTOList);
        return new Page<>(query.getCurrentPage(), query.getPageSize(), totalSize, respDTOList);
    }

    private void handleAggPayMethod(List<PaymentConstituteDTO> paymentDTOList, int showType) {
        if (CollectionUtils.isEmpty(paymentDTOList)) {
            return;
        }

        // 用于存储聚合支付和其他支付方式的数据
        Map<String, PaymentConstituteDTO> aggregatedMap = new HashMap<>();
        List<PaymentConstituteDTO> nonAggregatedList = new ArrayList<>();

        for (PaymentConstituteDTO dto : paymentDTOList) {
            if (StringUtils.isEmpty(dto.getPayMethod()) || !"聚合支付".equals(dto.getPayMethod())) {
                // 非聚合支付，直接保留
                nonAggregatedList.add(dto);
                continue;
            }

            // 构造带平台信息和日期的 payMethod
            String platForm = PayPowerId.getPlatFormById(dto.getPayPowerId());
            String combinedPayMethod = "聚合支付-" + platForm;
            // 根据showType确定聚合的KEY
            String key = getKey(showType, dto, combinedPayMethod);

            // 如果 map 中已存在该平台和日期的聚合支付记录，则累加金额
            if (aggregatedMap.containsKey(key)) {
                PaymentConstituteDTO existing = aggregatedMap.get(key);
                existing.setSalesRevenue(add(existing.getSalesRevenue(), dto.getSalesRevenue()));
                existing.setMemberRecharge(add(existing.getMemberRecharge(), dto.getMemberRecharge()));
                existing.setDeposit(add(existing.getDeposit(), dto.getDeposit()));
                existing.setTotal(add(existing.getTotal(), dto.getTotal()));
                existing.setServiceCharge(add(existing.getServiceCharge(), dto.getServiceCharge()));
                existing.setActualIncome(add(existing.getActualIncome(), dto.getActualIncome()));
            } else {
                // 第一次遇到该平台和日期的聚合支付，初始化
                PaymentConstituteDTO newDto = new PaymentConstituteDTO();
                newDto.setBusinessDate(dto.getBusinessDate());
                newDto.setStoreGuid(dto.getStoreGuid());
                newDto.setPaymentType(dto.getPaymentType());
                newDto.setPayMethod(combinedPayMethod);
                newDto.setSalesRevenue(copyOf(dto.getSalesRevenue()));
                newDto.setMemberRecharge(copyOf(dto.getMemberRecharge()));
                newDto.setTotal(copyOf(dto.getTotal()));
                newDto.setDeposit(copyOf(dto.getDeposit()));
                newDto.setServiceCharge(copyOf(dto.getServiceCharge()));
                newDto.setActualIncome(copyOf(dto.getActualIncome()));
                aggregatedMap.put(key, newDto);
            }
        }

        // 清空原列表，加入合并后的聚合支付 + 原始非聚合支付数据
        paymentDTOList.clear();
        paymentDTOList.addAll(aggregatedMap.values());
        paymentDTOList.addAll(nonAggregatedList);
    }

    private String getKey(int showType, PaymentConstituteDTO dto, String combinedPayMethod) {
        String key;
        if (showType == ShowTypeEnum.SUMMARIZING.getCode()) {
            key = combinedPayMethod + "-" + dto.getBusinessDate();
        } else if (showType == ShowTypeEnum.SINGLE.getCode()) {
            key = combinedPayMethod + "-" + (dto.getStoreGuid() != null ? dto.getStoreGuid() : "");
        } else {
            key = combinedPayMethod + "-" + dto.getBusinessDate().toString() + "-" + (dto.getStoreGuid() != null ? dto.getStoreGuid() : "");
        }
        return key;
    }

    // 辅助方法：安全地相加 BigDecimal
    private BigDecimal add(BigDecimal a, BigDecimal b) {
        return Optional.ofNullable(a).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(b).orElse(BigDecimal.ZERO));
    }

    // 辅助方法：安全复制 BigDecimal
    private BigDecimal copyOf(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : new BigDecimal(value.toString());
    }

    private void handleSingleDaySingleData(List<PaymentConstituteRespDTO> respDTOList,
                                           List<Object[]> storeGuidAndDateList,
                                           Map<String, StoreDTO> storeDTOMap,
                                           Map<String, Map<LocalDate, List<PaymentConstituteDTO>>> storeGuidAndDateMap) {
        storeGuidAndDateList.forEach(storeGuidAndDate -> {
            String storeGuid = (String) storeGuidAndDate[0];
            StoreDTO storeDTO = storeDTOMap.get(storeGuid);
            if (ObjectUtils.isEmpty(storeDTO)) {
                log.warn(ErrorConstant.NOT_MATCH_STORE_INFO, storeGuid);
                return;
            }
            LocalDate date = (LocalDate) storeGuidAndDate[1];
            PaymentConstituteRespDTO respDTO = new PaymentConstituteRespDTO();
            respDTO.setStoreGuid(storeGuid);
            respDTO.setStoreName(storeDTO.getName());
            respDTO.setBrandGuid(storeDTO.getBelongBrandGuid());
            respDTO.setBrandName(storeDTO.getBelongBrandName());
            respDTO.setBusinessDate(date);
            List<PaymentMethodRespDTO> paymentList = respDTO.getPaymentList();
            if (ObjectUtils.isEmpty(storeGuidAndDateMap.get(storeGuid))
                    || ObjectUtils.isEmpty(storeGuidAndDateMap.get(storeGuid).get(date))) {
                paymentList.add(getDefaultPaymentMethodRespDTO());
                respDTOList.add(respDTO);
                return;
            }
            handlePaymentData(storeGuidAndDateMap.get(storeGuid).get(date), paymentList);
            respDTOList.add(respDTO);
        });
    }

    private Page<PaymentConstituteRespDTO> getSinglePaymentConstitutePage(PaymentConstituteQueryDTO query,
                                                                          int startIndex,
                                                                          List<PaymentConstituteRespDTO> respDTOList) {
        List<String> storeGuidList = query.getStoreGuidList();
        int storeSize = storeGuidList.size();
        int endIndex = (int) Math.min(startIndex + query.getPageSize(), storeSize);
        storeGuidList = storeGuidList.subList(startIndex, endIndex);
        List<StoreDTO> storeDTOList = organizationService.queryStoreAndBrandByIdList(storeGuidList);
        if (org.springframework.util.CollectionUtils.isEmpty(storeDTOList)) {
            log.warn(ErrorConstant.STORE_INFO_EMPTY, storeGuidList);
            return new Page<>(query.getCurrentPage(), query.getPageSize(), 0, respDTOList);
        }
        Map<String, StoreDTO> storeDTOMap = storeDTOList.stream()
                .collect(Collectors.toMap(StoreDTO::getGuid, Function.identity(), (v1, v2) -> v2));

        // 构造分页后的单店查询对象
        PaymentConstituteQueryDTO singleQuery = buildSingleQuery(query, storeGuidList);
        List<PaymentConstituteDTO> singlePaymentDTOList = tradeDetailMapper.singlePaymentConstitute(singleQuery);
        handleAggPayMethod(singlePaymentDTOList, ShowTypeEnum.SINGLE.getCode());
        log.info("[单店查询收款数据]singlePaymentDTOList={}", JacksonUtils.writeValueAsString(singlePaymentDTOList));
        Map<String, List<PaymentConstituteDTO>> storeGuidMap = singlePaymentDTOList.stream()
                .collect(Collectors.groupingBy(PaymentConstituteDTO::getStoreGuid));
        handleSingleData(respDTOList, storeGuidList, storeDTOMap, storeGuidMap);
        // 处理合计
        handleTotalData(respDTOList);
        return new Page<>(query.getCurrentPage(), query.getPageSize(), storeSize, respDTOList);
    }

    private void handleSingleData(List<PaymentConstituteRespDTO> respDTOList,
                                  List<String> storeGuidList,
                                  Map<String, StoreDTO> storeDTOMap,
                                  Map<String, List<PaymentConstituteDTO>> storeGuidMap) {
        storeGuidList.forEach(storeGuid -> {
            PaymentConstituteRespDTO respDTO = new PaymentConstituteRespDTO();
            respDTO.setStoreGuid(storeGuid);
            StoreDTO storeDTO = storeDTOMap.get(storeGuid);
            if (ObjectUtils.isEmpty(storeGuid)) {
                log.warn(ErrorConstant.NOT_MATCH_STORE_INFO, storeGuid);
                return;
            }
            respDTO.setStoreName(storeDTO.getName());
            respDTO.setBrandGuid(storeDTO.getBelongBrandGuid());
            respDTO.setBrandName(storeDTO.getBelongBrandName());

            List<PaymentMethodRespDTO> paymentList = respDTO.getPaymentList();
            List<PaymentConstituteDTO> singleDTOList = storeGuidMap.get(storeGuid);
            if (org.springframework.util.CollectionUtils.isEmpty(singleDTOList)) {
                paymentList.add(getDefaultPaymentMethodRespDTO());
                respDTOList.add(respDTO);
                return;
            }
            handlePaymentData(singleDTOList, paymentList);

            respDTOList.add(respDTO);
        });
    }

    private Page<PaymentConstituteRespDTO> getSummarizingPaymentConstitutePage(PaymentConstituteQueryDTO query,
                                                                               int startIndex,
                                                                               List<PaymentConstituteRespDTO> respDTOList) {
        List<LocalDate> dateList = DateUtils.convertToDateList(query.getStartTime(), query.getEndTime());
        int dateSize = dateList.size();
        int endIndex = (int) Math.min(startIndex + query.getPageSize(), dateSize);
        dateList = dateList.subList(startIndex, endIndex);
        // 构造分页后的汇总查询对象
        PaymentConstituteQueryDTO summarizingQuery = buildSummarizingQuery(query, dateList);
        List<PaymentConstituteDTO> summarizingPaymentDTOList = tradeDetailMapper.summarizingPaymentConstitute(summarizingQuery);
        handleAggPayMethod(summarizingPaymentDTOList, ShowTypeEnum.SUMMARIZING.getCode());
        log.info("[汇总查询收款数据]summarizingPaymentDTOList={}", JacksonUtils.writeValueAsString(summarizingPaymentDTOList));
        Map<LocalDate, List<PaymentConstituteDTO>> businessDateMap = summarizingPaymentDTOList.stream()
                .collect(Collectors.groupingBy(PaymentConstituteDTO::getBusinessDate));
        handleSummarizingData(respDTOList, dateList, businessDateMap);
        // 处理合计
        handleTotalData(respDTOList);
        return new Page<>(query.getCurrentPage(), query.getPageSize(), dateSize, respDTOList);
    }

    private void handleSummarizingData(List<PaymentConstituteRespDTO> respDTOList,
                                       List<LocalDate> dateList,
                                       Map<LocalDate, List<PaymentConstituteDTO>> businessDateMap) {
        dateList.forEach(date -> {
            PaymentConstituteRespDTO respDTO = new PaymentConstituteRespDTO();
            respDTO.setBusinessDate(date);
            List<PaymentMethodRespDTO> paymentList = respDTO.getPaymentList();

            List<PaymentConstituteDTO> summarizingDTOList = businessDateMap.get(date);
            if (org.springframework.util.CollectionUtils.isEmpty(summarizingDTOList)) {
                paymentList.add(getDefaultPaymentMethodRespDTO());
                respDTOList.add(respDTO);
                return;
            }
            handlePaymentData(summarizingDTOList, paymentList);

            respDTOList.add(respDTO);
        });
    }

    private void handlePaymentData(List<PaymentConstituteDTO> paymentDTOList, List<PaymentMethodRespDTO> paymentList) {
        // 获取总销售额
        BigDecimal totalSalesRevenue = paymentDTOList.stream()
                .map(PaymentConstituteDTO::getSalesRevenue)
                .filter(salesRevenue -> Objects.nonNull(salesRevenue) && salesRevenue.compareTo(BigDecimal.ZERO) >= 0)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        paymentDTOList.sort(Comparator.comparing(PaymentConstituteDTO::getPaymentType));
        paymentDTOList.forEach(paymentDTO -> {
            PaymentMethodRespDTO methodRespDTO = new PaymentMethodRespDTO();
            methodRespDTO.setPayMethod(paymentDTO.getPayMethod());
            BigDecimal salesRevenue = Optional.ofNullable(paymentDTO.getSalesRevenue()).orElse(BigDecimal.ZERO);
            methodRespDTO.setSalesRevenue(salesRevenue);
            BigDecimal memberRecharge = Optional.ofNullable(paymentDTO.getMemberRecharge()).orElse(BigDecimal.ZERO);
            methodRespDTO.setMemberRecharge(memberRecharge);
            BigDecimal deposit = Optional.ofNullable(paymentDTO.getDeposit()).orElse(BigDecimal.ZERO);
            methodRespDTO.setDeposit(deposit);
            BigDecimal serviceCharge = Optional.ofNullable(paymentDTO.getServiceCharge()).orElse(BigDecimal.ZERO);
            methodRespDTO.setServiceCharge(serviceCharge);
            BigDecimal total = salesRevenue.add(memberRecharge).add(deposit).subtract(serviceCharge);
            methodRespDTO.setTotal(total);
            BigDecimal actualIncome = Optional.ofNullable(paymentDTO.getActualIncome()).orElse(BigDecimal.ZERO);
            methodRespDTO.setActualIncome(actualIncome);
            // 计算占比
            if (totalSalesRevenue.compareTo(BigDecimal.ZERO) > 0) {
                if (salesRevenue.compareTo(BigDecimal.ZERO) >= 0) {
                    double ratio = salesRevenue.divide(totalSalesRevenue, 4, RoundingMode.HALF_UP).doubleValue(); // 保留四位小数进行计算
                    int roundedRatio = (int) Math.round(ratio * 100); // 四舍五入到整数
                    methodRespDTO.setSalesRevenueProportion(roundedRatio + "%");
                } else {
                    methodRespDTO.setSalesRevenueProportion("-");
                }
            } else {
                methodRespDTO.setSalesRevenueProportion("0%");
            }
            // 如果金额都为0则过滤
            if (BigDecimalUtil.equelZero(salesRevenue) && BigDecimalUtil.equelZero(deposit) && BigDecimalUtil.equelZero(memberRecharge)) {
                return;
            }
            paymentList.add(methodRespDTO);
        });
        if (org.springframework.util.CollectionUtils.isEmpty(paymentList)) {
            paymentList.add(getDefaultPaymentMethodRespDTO());
        }
    }

    /**
     * 处理总数据，计算各种支付方式的总金额。
     */
    private void handleTotalData(List<PaymentConstituteRespDTO> respDTOList) {
        // 将所有响应DTO中的支付方式列表合并为一个大列表
        List<PaymentMethodRespDTO> allPaymentList = respDTOList.stream()
                .flatMap(resp -> resp.getPaymentList().stream())
                .collect(Collectors.toList());

        // 创建一个用于存放总计的响应DTO
        PaymentConstituteRespDTO respTotalDTO = new PaymentConstituteRespDTO();
        List<PaymentMethodRespDTO> totalPaymentList = respTotalDTO.getPaymentList();

        // 创建一个用于存放总计数值的支付方式DTO
        PaymentMethodRespDTO totalDTO = new PaymentMethodRespDTO();

        // 计算所有支付方式的销售总额
        BigDecimal salesRevenue = allPaymentList.stream()
                .map(PaymentMethodRespDTO::getSalesRevenue)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        totalDTO.setSalesRevenue(salesRevenue);

        // 计算所有支付方式的会员充值总额
        BigDecimal memberRecharge = allPaymentList.stream()
                .map(PaymentMethodRespDTO::getMemberRecharge)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        totalDTO.setMemberRecharge(memberRecharge);

        // 计算所有支付方式的预订总额
        BigDecimal deposit = allPaymentList.stream()
                .map(PaymentMethodRespDTO::getDeposit)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        totalDTO.setDeposit(deposit);

        // 计算所有支付方式的总交易额
        BigDecimal total = allPaymentList.stream()
                .map(PaymentMethodRespDTO::getTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        totalDTO.setTotal(total);

        BigDecimal serviceCharge = allPaymentList.stream()
                .map(PaymentMethodRespDTO::getServiceCharge)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        totalDTO.setServiceCharge(serviceCharge);

        BigDecimal actualIncome = allPaymentList.stream()
                .map(PaymentMethodRespDTO::getActualIncome)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        totalDTO.setActualIncome(actualIncome);
        if (Objects.nonNull(totalDTO.getSalesRevenue()) && totalDTO.getSalesRevenue().compareTo(BigDecimal.ZERO) > 0) {
            totalDTO.setSalesRevenueProportion("100%");
        } else {
            totalDTO.setSalesRevenueProportion("-");
        }
        // 将总计DTO添加到总计响应DTO的支付方式列表中
        totalPaymentList.add(totalDTO);
        // 将总计响应DTO添加到原始响应DTO列表中
        respDTOList.add(respTotalDTO);
    }

    /**
     * 构造分页后的单店查询对象
     */
    private PaymentConstituteQueryDTO buildSingleQuery(PaymentConstituteQueryDTO query, List<String> storeGuidList) {
        PaymentConstituteQueryDTO singleQuery = new PaymentConstituteQueryDTO();
        singleQuery.setEnterpriseGuid(query.getEnterpriseGuid());
        singleQuery.setStartTime(query.getStartTime());
        singleQuery.setEndTime(query.getEndTime());
        singleQuery.setBrandGuid(query.getBrandGuid());
        singleQuery.setShowType(query.getShowType());
        singleQuery.setStoreGuidList(storeGuidList);
        return singleQuery;
    }

    /**
     * 构造分页后的汇总查询对象
     */
    private PaymentConstituteQueryDTO buildSummarizingQuery(PaymentConstituteQueryDTO query, List<LocalDate> dateList) {
        PaymentConstituteQueryDTO summarizingQuery = new PaymentConstituteQueryDTO();
        summarizingQuery.setEnterpriseGuid(query.getEnterpriseGuid());
        summarizingQuery.setStartTime(dateList.get(dateList.size() - 1));
        summarizingQuery.setEndTime(dateList.get(0));
        summarizingQuery.setBrandGuid(query.getBrandGuid());
        summarizingQuery.setShowType(query.getShowType());
        summarizingQuery.setStoreGuidList(query.getStoreGuidList());
        return summarizingQuery;
    }

    private PaymentMethodRespDTO getDefaultPaymentMethodRespDTO() {
        PaymentMethodRespDTO methodRespDTO = new PaymentMethodRespDTO();
        methodRespDTO.setPayMethod("-");
        methodRespDTO.setDeposit(BigDecimal.ZERO);
        methodRespDTO.setTotal(BigDecimal.ZERO);
        methodRespDTO.setMemberRecharge(BigDecimal.ZERO);
        methodRespDTO.setSalesRevenue(BigDecimal.ZERO);
        methodRespDTO.setSalesRevenueProportion("-");
        methodRespDTO.setServiceCharge(BigDecimal.ZERO);
        methodRespDTO.setActualIncome(BigDecimal.ZERO);
        return methodRespDTO;
    }

    @Override
    public String exportPaymentConstitute(PaymentConstituteQueryDTO query) {
        query.setCurrentPage(CommonConstant.MIN_EXPORT);
        query.setPageSize(CommonConstant.MAX_EXPORT);
        Page<PaymentConstituteRespDTO> respDTOPage = paymentConstitute(query);
        List<PaymentConstituteRespDTO> respDTOList = respDTOPage.getData();
        // 收款构成20240206
        String sheetName = "收款构成";
        String fileName = sheetName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (ShowTypeEnum.SUMMARIZING.getCode() == query.getShowType()) {
            List<PaymentConstituteSummarizingExcelDTO> excelDTOList = buildSummarizingExcelDTOList(respDTOList);
            try {
                return ExcelUtils.exportExcel(excelDTOList, null, sheetName, PaymentConstituteSummarizingExcelDTO.class, fileName, ossClient);
            } catch (Exception e) {
                throw new BusinessException("收款构成汇总导出失败");
            }
        }
        if (ShowTypeEnum.SINGLE.getCode() == query.getShowType()) {
            List<PaymentConstituteSingleExcelDTO> excelDTOList = buildSingleExcelDTOList(respDTOList);
            try {
                return ExcelUtils.exportExcel(excelDTOList, null, sheetName, PaymentConstituteSingleExcelDTO.class, fileName, ossClient);
            } catch (Exception e) {
                throw new BusinessException("收款构成单店导出失败");
            }
        }
        List<PaymentConstituteSingleDaySingleExcelDTO> excelDTOList = buildSingleDaySingleExcelDTOList(respDTOList);
        try {
            return ExcelUtils.exportExcel(excelDTOList, null, sheetName, PaymentConstituteSingleDaySingleExcelDTO.class, fileName, ossClient);
        } catch (Exception e) {
            throw new BusinessException("收款构成单日单店导出失败");
        }
    }

    private List<PaymentConstituteSingleDaySingleExcelDTO> buildSingleDaySingleExcelDTOList(List<PaymentConstituteRespDTO> respDTOList) {
        List<PaymentConstituteSingleDaySingleExcelDTO> excelDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(respDTOList)) {
            return excelDTOList;
        }
        for (PaymentConstituteRespDTO respDTO : respDTOList) {
            List<PaymentMethodRespDTO> paymentMethod = respDTO.getPaymentList();
            if (org.springframework.util.CollectionUtils.isEmpty(paymentMethod)) {
                log.warn(ErrorConstant.PAYMENT_METHOD_EMPTY);
                PaymentConstituteSingleDaySingleExcelDTO singleExcelDTO = getDefaultSingleDaySingleExcelDTO();
                excelDTOList.add(singleExcelDTO);
                continue;
            }
            paymentMethod.forEach(paymentMethodRespDTO -> {
                PaymentConstituteSingleDaySingleExcelDTO singleExcelDTO = new PaymentConstituteSingleDaySingleExcelDTO();
                String businessDate = "";
                if (!ObjectUtils.isEmpty(respDTO.getBusinessDate())) {
                    businessDate = respDTO.getBusinessDate().format(DateTimeFormatter.ofPattern(YYYY_MM_DD));
                }
                singleExcelDTO.setBusinessDate(businessDate);
                singleExcelDTO.setBrandName(respDTO.getBrandName());
                singleExcelDTO.setStoreName(respDTO.getStoreName());
                singleExcelDTO.setPayMethod(paymentMethodRespDTO.getPayMethod());
                singleExcelDTO.setSalesRevenue(paymentMethodRespDTO.getSalesRevenue().toPlainString());
                singleExcelDTO.setSalesRevenueProportion(paymentMethodRespDTO.getSalesRevenueProportion());
                singleExcelDTO.setMemberRecharge(paymentMethodRespDTO.getMemberRecharge().toPlainString());
                singleExcelDTO.setDeposit(paymentMethodRespDTO.getDeposit().toPlainString());
                singleExcelDTO.setTotal(paymentMethodRespDTO.getTotal().toPlainString());
                singleExcelDTO.setServiceCharge(paymentMethodRespDTO.getServiceCharge().toPlainString());
                singleExcelDTO.setActualIncome(paymentMethodRespDTO.getActualIncome().toPlainString());
                excelDTOList.add(singleExcelDTO);
            });
        }
        return excelDTOList;
    }

    private PaymentConstituteSingleDaySingleExcelDTO getDefaultSingleDaySingleExcelDTO() {
        PaymentConstituteSingleDaySingleExcelDTO singleExcelDTO = new PaymentConstituteSingleDaySingleExcelDTO();
        singleExcelDTO.setPayMethod("");
        singleExcelDTO.setSalesRevenue("0");
        singleExcelDTO.setMemberRecharge("0");
        singleExcelDTO.setDeposit("0");
        singleExcelDTO.setTotal("0");
        singleExcelDTO.setServiceCharge("0");
        singleExcelDTO.setActualIncome("0");
        return singleExcelDTO;
    }

    @Override
    public CloudPayConstituteRespDTO cloudPayConstitute(CloudPayConstituteQueryDTO query) {
        return tradeDetailMapper.cloudPayConstitute(query);
    }

    @Override
    public BigDecimal queryAggPayServiceCharge(AggPayServiceChargeQueryDTO query) {
        return tradeDetailMapper.queryAggPayServiceCharge(query);
    }

    private List<PaymentConstituteSingleExcelDTO> buildSingleExcelDTOList(List<PaymentConstituteRespDTO> respDTOList) {
        List<PaymentConstituteSingleExcelDTO> excelDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(respDTOList)) {
            return excelDTOList;
        }
        for (PaymentConstituteRespDTO respDTO : respDTOList) {
            List<PaymentMethodRespDTO> paymentMethod = respDTO.getPaymentList();
            if (org.springframework.util.CollectionUtils.isEmpty(paymentMethod)) {
                log.warn(ErrorConstant.PAYMENT_METHOD_EMPTY);
                PaymentConstituteSingleExcelDTO singleExcelDTO = getDefaultSingleExcelDTO();
                excelDTOList.add(singleExcelDTO);
                continue;
            }
            paymentMethod.forEach(paymentMethodRespDTO -> {
                PaymentConstituteSingleExcelDTO singleExcelDTO = new PaymentConstituteSingleExcelDTO();
                singleExcelDTO.setBrandName(respDTO.getBrandName());
                singleExcelDTO.setStoreName(respDTO.getStoreName());
                singleExcelDTO.setPayMethod(paymentMethodRespDTO.getPayMethod());
                singleExcelDTO.setSalesRevenue(paymentMethodRespDTO.getSalesRevenue().toPlainString());
                singleExcelDTO.setSalesRevenueProportion(paymentMethodRespDTO.getSalesRevenueProportion());
                singleExcelDTO.setMemberRecharge(paymentMethodRespDTO.getMemberRecharge().toPlainString());
                singleExcelDTO.setDeposit(paymentMethodRespDTO.getDeposit().toPlainString());
                singleExcelDTO.setTotal(paymentMethodRespDTO.getTotal().toPlainString());
                singleExcelDTO.setServiceCharge(paymentMethodRespDTO.getServiceCharge().toPlainString());
                singleExcelDTO.setActualIncome(paymentMethodRespDTO.getActualIncome().toPlainString());
                excelDTOList.add(singleExcelDTO);
            });
        }
        return excelDTOList;
    }

    private PaymentConstituteSingleExcelDTO getDefaultSingleExcelDTO() {
        PaymentConstituteSingleExcelDTO singleExcelDTO = new PaymentConstituteSingleExcelDTO();
        singleExcelDTO.setPayMethod("");
        singleExcelDTO.setSalesRevenue("0");
        singleExcelDTO.setMemberRecharge("0");
        singleExcelDTO.setDeposit("0");
        singleExcelDTO.setTotal("0");
        singleExcelDTO.setServiceCharge("0");
        singleExcelDTO.setActualIncome("0");
        return singleExcelDTO;
    }

    private List<PaymentConstituteSummarizingExcelDTO> buildSummarizingExcelDTOList(List<PaymentConstituteRespDTO> respDTOList) {
        List<PaymentConstituteSummarizingExcelDTO> excelDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(respDTOList)) {
            return excelDTOList;
        }
        for (PaymentConstituteRespDTO respDTO : respDTOList) {
            List<PaymentMethodRespDTO> paymentMethod = respDTO.getPaymentList();
            if (org.springframework.util.CollectionUtils.isEmpty(paymentMethod)) {
                log.warn(ErrorConstant.PAYMENT_METHOD_EMPTY);
                PaymentConstituteSummarizingExcelDTO summarizingExcelDTO = getDefaultSummarizingExcelDTO();
                excelDTOList.add(summarizingExcelDTO);
                continue;
            }
            paymentMethod.forEach(paymentMethodRespDTO -> {
                PaymentConstituteSummarizingExcelDTO summarizingExcelDTO = new PaymentConstituteSummarizingExcelDTO();
                String businessDate = "";
                if (!ObjectUtils.isEmpty(respDTO.getBusinessDate())) {
                    businessDate = respDTO.getBusinessDate().format(DateTimeFormatter.ofPattern(YYYY_MM_DD));
                }
                summarizingExcelDTO.setBusinessDate(businessDate);
                summarizingExcelDTO.setPayMethod(paymentMethodRespDTO.getPayMethod());
                summarizingExcelDTO.setSalesRevenue(paymentMethodRespDTO.getSalesRevenue().toPlainString());
                summarizingExcelDTO.setSalesRevenueProportion(paymentMethodRespDTO.getSalesRevenueProportion());
                summarizingExcelDTO.setMemberRecharge(paymentMethodRespDTO.getMemberRecharge().toPlainString());
                summarizingExcelDTO.setDeposit(paymentMethodRespDTO.getDeposit().toPlainString());
                summarizingExcelDTO.setTotal(paymentMethodRespDTO.getTotal().toPlainString());
                summarizingExcelDTO.setServiceCharge(paymentMethodRespDTO.getServiceCharge().toPlainString());
                summarizingExcelDTO.setActualIncome(paymentMethodRespDTO.getActualIncome().toPlainString());
                excelDTOList.add(summarizingExcelDTO);
            });
        }
        return excelDTOList;
    }

    private PaymentConstituteSummarizingExcelDTO getDefaultSummarizingExcelDTO() {
        PaymentConstituteSummarizingExcelDTO summarizingExcelDTO = new PaymentConstituteSummarizingExcelDTO();
        summarizingExcelDTO.setPayMethod("");
        summarizingExcelDTO.setSalesRevenue("0");
        summarizingExcelDTO.setMemberRecharge("0");
        summarizingExcelDTO.setDeposit("0");
        summarizingExcelDTO.setTotal("0");
        summarizingExcelDTO.setServiceCharge("0");
        summarizingExcelDTO.setActualIncome("0");
        return summarizingExcelDTO;
    }

}
