package com.holderzone.holder.saas.store.mdm.pipeline.staff.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.framework.dynamic.datasource.starter.anno.SwitchServerCodeAnno;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.constant.FieldValutConstants;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.UserSyncDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.domain.UserDO;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.mapper.UserMapper;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.mapstruct.UserMapstruct;
import com.holderzone.holder.saas.store.mdm.util.RandomCodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UserSynServiceImpl
 * @date 2019/11/20 14:00
 * @description
 * @program holder-saas-store
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, UserDO> implements UserService {

    private final UserMapstruct userMapstruct;

    @Autowired
    public UserServiceImpl(UserMapstruct userMapstruct) {
        this.userMapstruct = userMapstruct;
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_staff")
    public UserDO insertUser(UserSyncDTO userSyncDTO) {
        UserDO userDO = userMapstruct.DTO2DO(userSyncDTO);
        log.info("UserSyncDTO:{} 已转换为 userDO:{}",
                JacksonUtils.writeValueAsString(userSyncDTO),
                JacksonUtils.writeValueAsString(userDO));
        log.info("user enable:{}", Objects.equals("1", userSyncDTO.getEnabled()));
        Predicate<String> authCodePredicate =
                authCode -> count(new LambdaQueryWrapper<UserDO>().eq(UserDO::getAuthCode, authCode)) == 0;
        String authCode = String.valueOf(RandomCodeUtils.generate(authCodePredicate, 3));
        userDO.setAuthCode(authCode);
        userDO.setCreateStaffGuid(FieldValutConstants.DEFAULT_STAFF_GUID);
        userDO.setUpdateStaffGuid(FieldValutConstants.DEFAULT_STAFF_GUID);
        userDO.setDiscountThreshold(BigDecimal.TEN);
        userDO.setAllowanceThreshold(BigDecimal.ZERO);
        userDO.setProductDiscountThreshold(BigDecimal.TEN);
        userDO.setRefundThreshold(BigDecimal.ZERO);
        userDO.setIsDeleted(false);
        this.save(userDO);
        return userDO;
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_staff")
    public void updateUserByGuid(UserSyncDTO userSyncDTO) {
        UserDO userDO = userMapstruct.DTO2DO(userSyncDTO);
        log.info("UserSyncDTO:{} 已转换为 userDO:{}",
                JacksonUtils.writeValueAsString(userSyncDTO),
                JacksonUtils.writeValueAsString(userDO));
        log.info("user enable:{}", Objects.equals("1", userSyncDTO.getEnabled()));
        this.update(userDO, new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, userDO.getGuid()));
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_staff")
    public void deleteUserByGuid(String guid) {
        this.remove(new LambdaQueryWrapper<UserDO>().eq(UserDO::getGuid, guid));
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_staff")
    public UserDO getLocalRecord(Wrapper<UserDO> queryWrapper) {
        return super.getOne(queryWrapper);
    }

    @Override
    @SwitchServerCodeAnno(serverCode = "holder_saas_store_staff")
    public List<UserDO> shouldInitUserList() {
        return this.list(new LambdaQueryWrapper<>());
    }
}
