package com.holderzone.holder.saas.store.report.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 估清报表 导出
 *
 * <AUTHOR>
 * @since 2025/7/18
 */
@Data
public class EstimateExcelDTO {

    @Excel(name = "品牌", orderNum = "1", width = 25)
    private String brandName;

    @Excel(name = "门店", orderNum = "2", width = 25)
    private String storeName;

    @Excel(name = "商品名称", orderNum = "3", width = 25)
    private String itemName;

    @Excel(name = "估清类型", orderNum = "4", width = 25)
    private String estimateType;

    @Excel(name = "估清时间", orderNum = "5", width = 25)
    private String estimateTime;

    @Excel(name = "估清操作员", orderNum = "6", width = 25)
    private String estimateOperatorName;

    @Excel(name = "估清设备终端", orderNum = "7", width = 25)
    private String estimateDeviceType;

    @Excel(name = "解估类型", orderNum = "8", width = 25)
    private String cancelEstimateType;

    @Excel(name = "解估时间", orderNum = "9", width = 25)
    private String cancelEstimateTime;

    @Excel(name = "解估操作员", orderNum = "10", width = 25)
    private String cancelEstimateOperatorName;

    @Excel(name = "解估设备终端", orderNum = "11", width = 25)
    private String cancelEstimateDeviceType;

    @Excel(name = "估清时长", orderNum = "12", width = 25)
    private String estimatedDuration;
}
