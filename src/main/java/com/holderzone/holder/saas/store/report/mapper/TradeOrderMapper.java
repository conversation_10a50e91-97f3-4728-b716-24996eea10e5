package com.holderzone.holder.saas.store.report.mapper;

import com.holderzone.saas.store.dto.journaling.req.SalesVolumeReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeRespDTO;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailQueryDTO;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailRespDTO;
import com.holderzone.saas.store.dto.report.query.ReportQueryVO;
import com.holderzone.saas.store.dto.report.resp.OrderItemTypeRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单
 */
@Mapper
public interface TradeOrderMapper {

    List<OrderItemTypeRespDTO> queryItemTypes(@Param("query") ReportQueryVO query);

    List<String> queryItemCategories(@Param("query") ReportQueryVO query);

    Long queryOrderCount(@Param("query") SalesVolumeReqDTO query);

    Long queryDineInOrderCount(@Param("query") SalesVolumeReqDTO query);

    List<SalesVolumeRespDTO> queryGroupByStoreDineInOrderCount(@Param("query") SalesVolumeReqDTO query);

    List<SaleDetailRespDTO> querySaleDetail(@Param("query") SaleDetailQueryDTO query);
}
