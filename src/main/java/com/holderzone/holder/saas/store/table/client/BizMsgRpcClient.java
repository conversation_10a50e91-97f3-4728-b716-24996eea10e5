package com.holderzone.holder.saas.store.table.client;

import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/01/07 15:18
 */
@Component
@FeignClient(value = "holder-saas-store-message", fallbackFactory = BizMsgRpcClient.BusinessMsgClientFallBack.class)
public interface BizMsgRpcClient {

    @PostMapping("/msg")
    String sendMsg(BusinessMessageDTO businessMessageDTO);

    @Slf4j
    @Component
    class BusinessMsgClientFallBack implements FallbackFactory<BizMsgRpcClient> {

        private String failure = "failure";

        @Override
        public BizMsgRpcClient create(Throwable throwable) {
            return businessMessageDTO -> {
                log.error("推送消息服务异常 e={}", throwable.getMessage());
                return failure;
            };
        }
    }

}
