package com.holderzone.holder.saas.store.table.constant;

/**
 * rocketMq tag  or  topic
 * <AUTHOR>
 * @version 1.0
 * @date 2018/12/27 10:47
 */
public class RocketMqConstant {

    public static final String TABLE_STATUS_CHANGE_MQ_TOPIC = "table-status-change-topic";

    public static final String TABLE_STATUS_CHANGE_MQ_TAG = "table-status-change-tag";

    public static final String TABLE_STATUS_CHANGE_MQ_GROUP = "table-status-change-group";

    public static final String TABLE_STATUS_CHANGE_MQ_TURN = "table-status-change-turn";

    public static final String TABLE_STATUS_CHANGE_MQ_COMBINE = "table-status-change-combine";

    public static final String TABLE_STATUS_CHANGE_MQ_SEPARATE = "table-status-change-separate";

    public static final String TABLE_STATUS_CHANGE_MQ_TABLE = "table-status-change-table";

    public static final String DOWNSTREAM_CONTEXT = "downstream-context";

    public static final String DOWNSTREAM_STORE_TOPIC = "downstream-store-topic";

    public static final String DOWNSTREAM_STORE_CREATE_TAG = "downstream-store-create-tag";

    public static final String DOWNSTREAM_STORE_INIT_TABLE_GROUP = "downstream-store-init-table-group";

    public static final String MESSAGE_CONTEXT = "message-context";

}
