package com.holderzone.holder.saas.store.pay.config;

/**
 * <AUTHOR>
 * @version 1-0
 * @className RocketMqConfig
 * @date 2018/08/30 19:32
 * @description
 * @program holder-saas-store-trading-center
 */
public class RocketMqConfig {

    public static final String JH_PAY_POLLING_TOPIC = "saas-agg-pay-topic";

    public static final String JH_PAY_POLLING_TAG = "saas-agg-pay-tag";

    public static final String JH_PAY_POLLING_CONSUMER_GROUP = "saas-agg-pay-consumer-group";
}
