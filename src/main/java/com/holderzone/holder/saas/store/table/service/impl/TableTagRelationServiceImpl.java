package com.holderzone.holder.saas.store.table.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.holder.saas.store.table.domain.TableTagRelationDO;
import com.holderzone.holder.saas.store.table.mapper.TableTagRelationMapper;
import com.holderzone.holder.saas.store.table.service.RedisService;
import com.holderzone.holder.saas.store.table.service.TableTagRelationService;
import com.holderzone.holder.saas.store.table.service.TableTagService;
import com.holderzone.holder.saas.store.table.utils.ConstructorFunction;
import com.holderzone.saas.store.dto.table.TableTagDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.holderzone.holder.saas.store.table.constant.Constant.TABLE_TAG_RELATION;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableTagRelationServiceImpl
 * @date 2019/12/05 16:23
 * @description //TODO
 * @program IdeaProjects
 */
@Service
public class TableTagRelationServiceImpl extends ServiceImpl<TableTagRelationMapper, TableTagRelationDO> implements TableTagRelationService {

    private final RedisService redisService;

    private final TableTagService tableTagService;

    public TableTagRelationServiceImpl(RedisService redisService, TableTagService tableTagService) {
        this.redisService = redisService;
        this.tableTagService = tableTagService;
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public void removeTagByTableIds(String... tableids) {
        remove(new QueryWrapper<TableTagRelationDO>().lambda()
                .in(TableTagRelationDO::getTableGuid, tableids));
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public void removeTagByTableIds(List<String> tableids) {
        remove(new QueryWrapper<TableTagRelationDO>().lambda()
                .in(TableTagRelationDO::getTableGuid, tableids));
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public boolean removeTagByTagGuid(String tagGuid) {
        return remove(new QueryWrapper<TableTagRelationDO>().lambda()
                .eq(TableTagRelationDO::getTagGuid, tagGuid));
    }

    /**
     * 单个联系创建
     *
     * @param tagGuidList
     * @param tableGuid
     */
    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public void createTags(List<String> tagGuidList, String tableGuid) {
        createTags(tagGuidList, Arrays.asList(tableGuid));
    }

    /**
     * 创建tagrelation
     *
     * @param tagGuidList
     * @param tableGuids
     */
    @Override
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    public void createTags(List<String> tagGuidList, List<String> tableGuids) {
        if (CollectionUtils.isEmpty(tagGuidList)) {
            return;
        }
        List<String> relationIds = redisService.batchGuid(tagGuidList.size() * tableGuids.size(), TABLE_TAG_RELATION).stream().collect(Collectors.toList());
        List<TableTagRelationDO> tableTagRelationDOS = new ArrayList<>();
        BiConsumer<List<TableTagRelationDO>, TableTagRelationDO> biConsumer = List::add;
        ConstructorFunction<TableTagRelationDO, String, String, String, LocalDateTime> constructor = TableTagRelationDO::new;
        tableGuids.forEach(a ->
                tagGuidList.forEach(b ->
                        biConsumer.accept(tableTagRelationDOS, constructor.apply(relationIds.remove(relationIds.size() - 1), a, b, null))));
        saveBatch(tableTagRelationDOS);
    }

    /**
     * 通过桌台GUID 获取标签信息
     *
     * @param tableGuids
     * @return 桌台对应的标签集合
     */
    @Override
    public Map<String, List<TableTagDTO>> getTagInfoByTableInfos(List<String> tableGuids) {
        if (CollectionUtils.isEmpty(tableGuids)) {
            return new HashMap<>();
        }
        //获取table和对应关系集合
        List<TableTagRelationDO> list = list(new QueryWrapper<TableTagRelationDO>().lambda().in(TableTagRelationDO::getTableGuid, tableGuids));
        //获取tag标签和tag信息的映射
        List<String> tagGuids = list.stream().map(TableTagRelationDO::getTagGuid).distinct().collect(Collectors.toList());
        Map<String, TableTagDTO> tagWithGuidMap = tableTagService.getTagWithGuidMap(tagGuids);
        //获取桌台和标签关系映射
        return list.stream().filter(tableTagRelationDO -> tagWithGuidMap.containsKey(tableTagRelationDO.getTagGuid()))
                .collect(Collectors.groupingBy(TableTagRelationDO::getTableGuid,
                        Collectors.mapping(t -> tagWithGuidMap.get(t.getTagGuid()), Collectors.toList())
                        )
                );
    }

}