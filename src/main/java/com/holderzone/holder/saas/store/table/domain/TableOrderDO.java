package com.holderzone.holder.saas.store.table.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/01/02 16:35
 */
@AllArgsConstructor
@NoArgsConstructor
@TableName("hst_table_order")
@Data
public class TableOrderDO implements Serializable {

    private Long id;

    /**
     * 业务主键
     */
    private String guid;

    /**
     * 桌号外键
     */
    private String tableGuid;

    /**
     * 主单号
     */
    private String mainOrderGuid;

    /**
     * 订单号
     */
    private String orderGuid;

    /**
     * 桌台状态
     * -1=暂停使用
     * 0=空闲
     * 1=占用
     * 2=预定
     * 3=待清台
     */
    private Integer status;

    /**
     * 10=占用空台
     * 11=占用锁定
     * 12=占用并台
     * 20=预定未锁定
     * 21=预定已锁定
     */
    private String subStatus;

    /**
     * 并台次数
     */
    private Integer combineTimes;

    /**
     * 联台次数
     */
    private Integer associatedTimes;

    private String openStaffGuid;

    private String openStaffName;

    private LocalDateTime openTableTime;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

    public Set<Integer> makeSubStatus() {
        if (StringUtils.hasText(this.subStatus)) {
            return new HashSet<>(JacksonUtils.toObjectList(Integer.class, this.subStatus));
        }
        return new HashSet<>();
    }
}
