package com.holderzone.holder.saas.store.table.controller;


import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dds.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.table.constant.RocketMqConstant;
import com.holderzone.holder.saas.store.table.exception.TableNotAllSuccessException;
import com.holderzone.holder.saas.store.table.service.TableBasicService;
import com.holderzone.holder.saas.store.table.service.TableOrderOpenService;
import com.holderzone.holder.saas.store.table.service.TableOrderService;
import com.holderzone.holder.saas.store.table.utils.ValidatorUtils;
import com.holderzone.saas.store.dto.boss.req.BossTableQueryDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.OrderTableBillReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.store.table.StoreAndTableDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.*;
import com.holderzone.saas.store.dto.trade.OrderTableDTO;
import com.holderzone.saas.store.dto.trade.OrderTableVO;
import com.holderzone.saas.store.dto.weixin.WxStoreTableCombineDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.holderzone.holder.saas.store.table.utils.ValidatorUtils.*;

/**
 * ┏┓　　　┏┓
 * ┏┛┻━━━┛┻┓
 * ┃　　　　　　　┃
 * ┃　　　━　　　┃
 * ┃　┳┛　┗┳　┃
 * ┃　　　　　　　┃
 * ┃　　　┻　　　┃
 * ┃　　　　　　　┃
 * ┗━┓　　　┏━┛
 * 　　┃　　　┃神兽保佑
 * 　　┃　　　┃代码无BUG！
 * 　　┃　　　┗━━━┓
 * 　　┃　　　　　　　┣┓
 * 　　┃　　　　　　　┏┛
 * 　　┗┓┓┏━┳┓┏┛
 * 　　　┃┫┫　┃┫┫
 * 　　　┗┻┛　┗┻┛
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/12/27 10:22
 */
@Slf4j
@Api("桌台服务")
@RestController
@RequestMapping("/table")
public class TableController {

    private final TableBasicService tableBasicService;

    private final TableOrderService tableOrderService;

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    private final TableOrderOpenService tableOrderOpenService;

    @Autowired
    public TableController(TableBasicService tableBasicService, TableOrderService tableOrderService, DefaultRocketMqProducer defaultRocketMqProducer, TableOrderOpenService tableOrderOpenService) {
        this.tableBasicService = tableBasicService;
        this.tableOrderService = tableOrderService;
        this.defaultRocketMqProducer = defaultRocketMqProducer;

        this.tableOrderOpenService = tableOrderOpenService;
    }

    @ApiOperation("创建桌台")
    @PostMapping("/add")
    public String add(@RequestBody TableBasicDTO tableBasicDTO) {
        if (log.isInfoEnabled()) {
            log.info("创建桌台入参：{}", JacksonUtils.writeValueAsString(tableBasicDTO));
        }
        validatorCreateTable(tableBasicDTO);
        return tableBasicService.create(tableBasicDTO);
    }

    @ApiOperation("更新桌台")
    @PostMapping("/update")
    public String update(@RequestBody TableBasicDTO tableBasicDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新桌台入参：{}", JacksonUtils.writeValueAsString(tableBasicDTO));
        }
        validatorUpdateTable(tableBasicDTO);
        return tableBasicService.update(tableBasicDTO);
    }

    @ApiOperation("删除桌台")
    @PostMapping("/delete")
    public List<String> delete(@RequestBody List<String> guidList) {
        if (log.isInfoEnabled()) {
            log.info("删除桌台入参：{}", JacksonUtils.writeValueAsString(guidList));
        }
        if (CollectionUtils.isEmpty(guidList)) {
            throw new BusinessException("未选择需要删除的桌号");
        }
        return tableBasicService.batchDelete(guidList);
    }

    @ApiOperation("批量创建桌台")
    @PostMapping("/batch/create")
    public String batchCreate(@RequestBody TableBatchCreateDTO tableBatchCreateDTO) {
        if (log.isInfoEnabled()) {
            log.info("批量创建桌台入参：{}", JacksonUtils.writeValueAsString(tableBatchCreateDTO));
        }
        validatorBatchCreateTable(tableBatchCreateDTO);
        return tableBasicService.batchCreate(tableBatchCreateDTO);
    }

    @ApiOperation("批量删除桌台")
    @PostMapping("/deleteAll")
    public List<String> batchDelete(@RequestBody List<String> guidList) {
        if (log.isInfoEnabled()) {
            log.info("批量删除桌台入参：{}", JacksonUtils.writeValueAsString(guidList));
        }
        if (CollectionUtils.isEmpty(guidList)) {
            throw new BusinessException("未选择需要删除的桌号");
        }
        return tableBasicService.batchDelete(guidList);
    }

    @ApiOperation("查询桌台列表")
    @PostMapping("/web/query")
    public List<TableBasicDTO> listByWeb(@RequestBody TableBasicQueryDTO tableBasicQueryDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询桌台列表入参：{}", JacksonUtils.writeValueAsString(tableBasicQueryDTO));
        }
        return tableBasicService.listTable(tableBasicQueryDTO);
    }

    @ApiOperation("查询桌台订单列表")
    @PostMapping("/android/query")
    public List<TableOrderDTO> listByAndroid(@RequestBody TableBasicQueryDTO tableBasicQueryDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询桌台订单列表入参：{}", JacksonUtils.writeValueAsString(tableBasicQueryDTO));
        }
        return tableOrderService.listTable(tableBasicQueryDTO);
    }

    @ApiOperation("查询桌台订单列表")
    @PostMapping("/boss/query")
    public List<TableOrderDTO> queryTable(@RequestBody BossTableQueryDTO queryDTO) {
        log.info("查询桌台订单列表入参：{}", JacksonUtils.writeValueAsString(queryDTO));
        if (StringUtils.hasText(queryDTO.getEnterpriseGuid())) {
            UserContextUtils.putErp(queryDTO.getEnterpriseGuid());
            EnterpriseIdentifier.setEnterpriseGuid(queryDTO.getEnterpriseGuid());
        }
        // 参数
        TableBasicQueryDTO basicQueryDTO = new TableBasicQueryDTO();
        basicQueryDTO.setStoreGuid(queryDTO.getStoreGuid());
        basicQueryDTO.setAreaGuid(queryDTO.getAreaGuid());
        basicQueryDTO.setTableGuidList(queryDTO.getTableGuidList());
        return tableOrderService.listTable(basicQueryDTO);
    }

    @ApiOperation("开台")
    @PostMapping("/open")
    public String open(@RequestBody OpenTableDTO openTableDTO) {
        if (log.isInfoEnabled()) {
            log.info("开台入参：{}", JacksonUtils.writeValueAsString(openTableDTO));
        }
        validatorOpenTable(openTableDTO);
        return tableOrderService.open(openTableDTO, 0);
    }

    @ApiOperation("开台")
    @PostMapping("/open/batch")
    public ReserveOpenTableDTO batch(@RequestBody BatchOpenTableDTO batchOpenTableDTO) {
        if (log.isInfoEnabled()) {
            log.info("开台入参：{}", JacksonUtils.writeValueAsString(batchOpenTableDTO));
        }
        try {
            return tableOrderService.batchOpen(batchOpenTableDTO);
        } catch (TableNotAllSuccessException e) {
            return e.getReserveOpenTableDTO();
        }
    }

    @ApiOperation("联台")
    @PostMapping("/associated/open")
    public String associatedOpen(@RequestBody OpenAssociatedTableDTO openAssociatedTableDTO) {
        if (log.isInfoEnabled()) {
            log.info("联台入参：{}", JacksonUtils.writeValueAsString(openAssociatedTableDTO));
        }
        validatorOpenAssociatedTable(openAssociatedTableDTO);
        return tableOrderService.associatedOpen(openAssociatedTableDTO);
    }

    @ApiOperation("开台")
    @PostMapping("/reserve/open")
    public String reserveOpen(@RequestBody OpenTableDTO openTableDTO) {
        if (log.isInfoEnabled()) {
            log.info("开台入参：{}", JacksonUtils.writeValueAsString(openTableDTO));
        }
        validatorOpenTable(openTableDTO);
        return tableOrderService.open(openTableDTO, 1);
    }

    @ApiOperation("转台")
    @PostMapping("/turn")
    public boolean turn(@RequestBody TurnTableDTO turnTableDTO) {
        if (log.isInfoEnabled()) {
            log.info("转台入参：{}", JacksonUtils.writeValueAsString(turnTableDTO));
        }
        ValidatorUtils.validatorTurnTable(turnTableDTO);
        return tableOrderService.turn(turnTableDTO);
    }

    @ApiOperation("并台")
    @PostMapping("/combine")
    public List<String> combine(@RequestBody TableCombineDTO tableCombineDTO) {
        if (log.isInfoEnabled()) {
            log.info("并台入参：{}", JacksonUtils.writeValueAsString(tableCombineDTO));
        }
        validatorCombineTable(tableCombineDTO);
        return tableOrderService.combine(tableCombineDTO, 0);
    }

    @ApiOperation("并台校验(第三方活动提示)")
    @PostMapping("/combine_verify")
    public TableCombineVerifyRespDTO verifyCombine(@RequestBody TableCombineDTO tableCombineDTO) {
        if (log.isInfoEnabled()) {
            log.info("并台校验入参：{}", JacksonUtils.writeValueAsString(tableCombineDTO));
        }
        validatorCombineTable(tableCombineDTO);
        return tableOrderService.verifyCombine(tableCombineDTO);
    }

    /**
     * 新接口，本地化需要转化数据
     */
    @ApiOperation("并台")
    @PostMapping("/combine_v2")
    public TableCombineRespDTO combineV2(@RequestBody TableCombineDTO tableCombineDTO) {
        if (log.isInfoEnabled()) {
            log.info("并台入参V2：{}", JacksonUtils.writeValueAsString(tableCombineDTO));
        }
        validatorCombineTable(tableCombineDTO);
        return tableOrderService.combine_v2(tableCombineDTO, 0);
    }

    @ApiOperation("预定锁定")
    @PostMapping("/reserve")
    public List<String> reserve(@RequestBody List<String> tableGuids) {

        return tableOrderService.reserve(tableGuids);
    }

    @ApiOperation("预定锁定")
    @PostMapping("/reserve/prepare")
    public void prepare(@RequestBody ReservePreparedDTO dto) {
        log.info("预订锁定入参：{}", dto);
        tableOrderService.prepare(dto.getAdd(), dto.getDel());
    }

    @ApiOperation("预定锁定")
    @PostMapping("/reserve/cancle")
    public List<String> reserveCancle(@RequestBody List<String> tableGuids) {
        log.info("预订取消入参：{}", tableGuids);
        return tableOrderService.cancleReserve(tableGuids);
    }

    @ApiOperation("并台")
    @PostMapping("/reserve/combine")
    public List<String> reserveCombine(@RequestBody TableCombineDTO tableCombineDTO) {
        if (log.isInfoEnabled()) {
            log.info("并台入参：{}", JacksonUtils.writeValueAsString(tableCombineDTO));
        }
        validatorCombineTable(tableCombineDTO);
        return tableOrderService.combine(tableCombineDTO, 1);
    }

    @ApiOperation("拆台")
    @PostMapping("/separate")
    public boolean separate(@RequestBody TableOrderCombineDTO tableOrderCombineDTO) {
        if (log.isInfoEnabled()) {
            log.info("拆台入参：{}", JacksonUtils.writeValueAsString(tableOrderCombineDTO));
        }
        ValidatorUtils.validatorSeparateTable(tableOrderCombineDTO);
        return tableOrderService.separate(tableOrderCombineDTO);
    }

    @ApiOperation("关台")
    @PostMapping("/close")
    public boolean close(@RequestBody CancelOrderReqDTO cancelOrderReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("关台入参：{}", JacksonUtils.writeValueAsString(cancelOrderReqDTO));
        }
        validatorCloseTable(cancelOrderReqDTO);
        return tableOrderService.close(cancelOrderReqDTO);
    }

    @ApiOperation("校验桌台是否能开台接口：处于空闲状态，未被业务锁定")
    @PostMapping("/whether/open")
    public boolean couldOpen(@RequestBody OpenTableDTO openTableDTO) {
        if (log.isInfoEnabled()) {
            log.info("开台校验入参：{}", JacksonUtils.writeValueAsString(openTableDTO));
        }
        return tableOrderService.couldOpen(openTableDTO);
    }

    @ApiOperation("校验桌台反结账是否能开台接口：处于空闲状态，未被业务锁定")
    @PostMapping("/recheck/open")
    public TableWhetherOpenDTO recheckOpen(@RequestBody BaseTableDTO baseTableDTO) {
        if (log.isInfoEnabled()) {
            log.info("反结账校验入参：{}", JacksonUtils.writeValueAsString(baseTableDTO));
        }
        return tableOrderService.recheckOpen(baseTableDTO);
    }

    @ApiOperation("尝试开台：如果空闲执行开台，如果占用则不操作。最后返回OrderGuid。")
    @PostMapping("/try_open")
    public String tryOpen(@RequestBody OpenTableDTO openTableDTO) {
        if (log.isInfoEnabled()) {
            log.info("尝试开台入参：{}", JacksonUtils.writeValueAsString(openTableDTO));
        }
        return tableOrderService.tryOpen(openTableDTO);
    }

    @ApiOperation("校验桌台是否被业务锁定")
    @PostMapping("/whether/lock/{deviceId}/{tableGuid}")
    public boolean isLocked(@PathVariable("deviceId") String deviceId,
                            @PathVariable("tableGuid") String tableGuid) {
        log.info("校验桌台是否被锁入参：deviceId={}, tableGuid={}", deviceId, tableGuid);
        return tableOrderService.isLocked(deviceId, tableGuid);
    }

    @ApiOperation("桌台加锁")
    @PostMapping("/lock")
//    @PostMapping("/try_lock")
//    @PostMapping("/try_lock_throw")
    public boolean tryLock(@RequestBody TableLockDTO tableLockDTO) {
        if (log.isInfoEnabled()) {
            log.info("桌台加锁入参：{}", JacksonUtils.writeValueAsString(tableLockDTO));
        }
        return tableOrderService.tryLock(tableLockDTO);
    }

    @ApiOperation("释放桌台锁")
    @PostMapping("/release/lock")
//    @PostMapping("/try_unlock")
//    @PostMapping("/try_unlock_throw")
    public boolean tryUnlock(@RequestBody TableLockDTO tableLockDTO) {
        if (log.isInfoEnabled()) {
            log.info("桌台解锁入参：{}", JacksonUtils.writeValueAsString(tableLockDTO));
        }
        return tableOrderService.tryUnlock(tableLockDTO);
    }

    @ApiOperation("桌台状态补偿接口")
    @PostMapping("/compensation")
    public void tableStatusCompensation(@RequestBody CompensationTableReqDTO reqDTO) {
        if (log.isInfoEnabled()) {
            log.info("桌台状态补偿接口入参,reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        }
        if (StringUtils.hasText(reqDTO.getEnterpriseGuid())) {
            UserContextUtils.putErp(reqDTO.getEnterpriseGuid());
            EnterpriseIdentifier.setEnterpriseGuid(reqDTO.getEnterpriseGuid());
        }
        tableOrderService.compensationStatus(reqDTO.getList());
    }

    @ApiOperation("桌台状态变化同步")
    @PostMapping("/status/change")
//    @PostMapping("/status/sync")
    public boolean tableStatusChange(@RequestBody TableStatusChangeDTO tableStatusChangeDTO) {
        if (log.isInfoEnabled()) {
            log.info("桌台状态变化同步入参：{}", JacksonUtils.writeValueAsString(tableStatusChangeDTO));
        }
        return tableOrderService.statusSync(tableStatusChangeDTO);
    }


    @ApiOperation("桌台状态变化同步Mq")
    @PostMapping("/status/change_with_mq")
    public boolean tableStatusChangeWithMq(@RequestBody TableStatusChangeDTO tableStatusChangeDTO) {
        if (log.isInfoEnabled()) {
            log.info("桌台状态变化同步入参Mq：{}", JacksonUtils.writeValueAsString(tableStatusChangeDTO));
        }
        tableStatusChangeDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        Message message = new Message(
                RocketMqConstant.TABLE_STATUS_CHANGE_MQ_TOPIC,
                RocketMqConstant.TABLE_STATUS_CHANGE_MQ_TAG,
                JacksonUtils.toJsonByte(tableStatusChangeDTO)
        );
        return defaultRocketMqProducer.sendMessage(message);
    }


    @ApiOperation("根据桌台Guid查询区域Guid")
    @PostMapping("/getAreaGuid/{tableGuid}")
//    @PostMapping("/area_guid/{tableGuid}")
    public String getAreaGuidByTableGuid(@PathVariable("tableGuid") String tableGuid) {
        log.info("根据桌台Guid查询区域Guid入参：tableGuid={}", tableGuid);
        return tableOrderService.queryAreaGuidByTableGuid(tableGuid);
    }

    @ApiOperation("根据桌台Guid查询订单Guid")
    @PostMapping("/getOrderGuid/{tableGuid}")
    public String getOrderGuidByTableGuid(@PathVariable("tableGuid") String tableGuid) {
        log.info("根据桌台Guid查询订单Guid入参：tableGuid={}", tableGuid);
        return tableOrderService.queryOrderGuidByTableGuid(tableGuid);
    }

    @ApiOperation("清台")
    @PostMapping("/clean")
    public boolean tableClean(@RequestBody TableStatusChangeDTO tableStatusChangeDTO) {
        if (log.isInfoEnabled()) {
            log.info("清台入参：{}", JacksonUtils.writeValueAsString(tableStatusChangeDTO));
        }
        return this.tableStatusChangeWithMq(tableStatusChangeDTO);
    }

    @ApiModelProperty("根据当前桌台，查出所有并桌")
    @PostMapping("/table_list/{tableGuid}")
    public List<WxStoreTableCombineDTO> tableList(@PathVariable("tableGuid") String tableGuid) {
        log.info("根据当前桌台，查出所有并桌入参:{}", tableGuid);
        return tableOrderService.tableList(tableGuid);
    }

    @ApiModelProperty("根据桌台guid，查桌台详情")
    @PostMapping("/details/{tableGuid}")
    public TableDTO getFullTableByGuid(@PathVariable("tableGuid") String tableGuid) {
        log.info("根据桌台guid，查桌台详情:{}", tableGuid);
        return tableOrderService.getFullTableByGuid(tableGuid);
    }

    @ApiModelProperty("根据当前桌台，查出所有并桌")
    @PostMapping("/tableCombineList")
    public List<WxStoreTableCombineDTO> tableCombineList(@RequestParam("tableGuid") String tableGuid
            , @RequestParam("mainOrderGuid") String mainOrderGuid) {
        log.info("根据当前桌台，查出所有并桌入参:{}", tableGuid);
        return tableOrderService.tableCombineList(mainOrderGuid, tableGuid);
    }

    /**
     * 查询桌台信息
     *
     * @param tableGuid 桌台信息
     * @return 桌台信息
     */
    @ApiModelProperty("查询桌台信息")
    @GetMapping("/query_table_info")
    public TableBasicDTO queryTableInfo(@RequestParam("tableGuid") String tableGuid) {
        log.info("查询桌台信息 入参={}", tableGuid);
        return tableBasicService.queryTableInfo(tableGuid);
    }

    @ApiModelProperty("查询门店下未绑定的桌台信息")
    @PostMapping("/query_un_binding_table_info")
    public List<PadAreaDTO> queryUnBindingTableInfo(@RequestBody List<String> bindingTableGuids,
                                                    @RequestParam("tableGuid") String storeGuid) {
        log.info("查询门店下未绑定的桌台信息 已绑定guid={}，门店guid={}", bindingTableGuids, storeGuid);
        return tableBasicService.queryUnBindingTableInfo(bindingTableGuids, storeGuid);
    }

    /**
     * 根据门店列表查询桌台Guid列表
     *
     * @param singleDataDTO datas必传，门店guid
     * @return 门店和桌台的guid
     */
    @ApiOperation("根据门店列表查询桌台Guid列表")
    @PostMapping("/list_table_by_store_guid")
    public List<StoreAndTableDTO> listTableByStoreGuid(@RequestBody SingleDataDTO singleDataDTO) {
        if (log.isInfoEnabled()) {
            log.info("根据门店列表查询桌台列表 入参 singleDataDTO={}", JacksonUtils.writeValueAsString(singleDataDTO));
        }
        return tableBasicService.listTableByStoreGuid(singleDataDTO);
    }

    @ApiModelProperty("根据主单guid查出所有并桌")
    @PostMapping("/query_combine_List_by_main_order")
    public List<TableBasicDTO> queryCombineListByMainOrder(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("[根据主单guid查出所有并桌]singleDataDTO={}", singleDataDTO);
        return tableOrderService.queryCombineListByMainOrder(singleDataDTO);
    }

    @ApiModelProperty("发送桌台状态变化消息")
    @PostMapping("/send_table_change_msg")
    public void sendTableChangeMsg(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("发送桌台状态变化消息入参:{}", singleDataDTO);
        tableOrderService.sendTableChangeMsg(singleDataDTO);
    }

    @ApiModelProperty("根据订单guid查询桌台")
    @PostMapping("/query_table_by_order_guid")
    public List<TableBasicDTO> queryTableByOrderGuid(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("[根据订单guid查询桌台]singleDataDTO={}", singleDataDTO);
        return tableOrderService.queryTableByOrderGuid(singleDataDTO);
    }

    /**
     * 根据订单号查询桌台并开台
     */
    @ApiModelProperty("根据订单号查询桌台并开台")
    @PostMapping("/open_table_by_order_guid")
    public OpenTableOrderDTO openTableByOrderGuid(@RequestBody OpenTableByOrderDTO dto) {
        log.info("[根据订单号查询桌台并开台]singleDataDTO={}", JacksonUtils.writeValueAsString(dto));
        return tableOrderOpenService.openTableByOrderGuid(dto);
    }

    /**
     * 修改订单关联的桌台
     */
    @ApiModelProperty("修改订单关联的桌台")
    @PostMapping("/update_table_order")
    public OrderTableVO updateTableOrder(@RequestBody OrderTableDTO orderTableDTO) {
        log.info("修改订单关联的桌台入参={}", JacksonUtils.writeValueAsString(orderTableDTO));
        return tableOrderOpenService.updateTableOrder(orderTableDTO);
    }

    /**
     * 手动结账关台
     */
    @ApiModelProperty("修改订单关联的桌台")
    @PostMapping("/deal_handle_close")
    boolean dealClose(@RequestBody OrderTableBillReqDTO orderTableBillReqDTO){
        return tableOrderOpenService.dealClose(orderTableBillReqDTO);
    }

}
