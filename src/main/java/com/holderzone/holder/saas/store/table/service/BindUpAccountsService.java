package com.holderzone.holder.saas.store.table.service;


import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 扎帐相关，校验是否可以开台
 */
public interface BindUpAccountsService {

    /**
     * 检查是否最后一台桌台，并判断昨日是否扎帐
     */
    boolean checkBindUpAccountStatus(String storeGuid);

    /**
     * 当前时间的营业日
     */
    LocalDate currentTimeDay(String storeGuid, LocalDateTime localDateTime);

    void sendMqForCanOpenTable(String storeGuid);
}
