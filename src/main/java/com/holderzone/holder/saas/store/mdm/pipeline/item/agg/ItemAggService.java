package com.holderzone.holder.saas.store.mdm.pipeline.item.agg;


import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.ItemSyncDTO;

import java.util.List;

public interface ItemAggService {

    void triggerRemoteItem(MdmTriggerType mdmTriggerType);

    /**
     * 智慧门店批量创建商品同步创建至MDM
     * @param mdmItemSynDTOS
     */
    void createRemoteItem(List<ItemSyncDTO> mdmItemSynDTOS);

    /**
     * 智慧门店修改商品同步修改MDM商品
     * @param mdmItemSynDTO
     */
    void updateRemoteItem(ItemSyncDTO mdmItemSynDTO);

    /**
     * 智慧门店删除商品同步删除MDM商品
     * @param mdmItemSynDTOS
     */
    void deleteRemoteItem(List<ItemSyncDTO> mdmItemSynDTOS);

    /**
     * MDM主数据创建商品推送至智慧门店本地同步创建
     * @param mdmItemSynDTO
     */
    void createLocalItem(ItemSyncDTO mdmItemSynDTO);

    /**
     * MDM主数据更新商品推送至智慧门店本地同步更新
     * @param mdmItemSynDTO
     */
    void updateLocalItem(ItemSyncDTO mdmItemSynDTO);

    /**
     * MDM主数据删除商品推送至智慧门店本地同步删除
     * @param mdmItemSynDTO
     */
    void deleteLocalItem(ItemSyncDTO mdmItemSynDTO);
}
