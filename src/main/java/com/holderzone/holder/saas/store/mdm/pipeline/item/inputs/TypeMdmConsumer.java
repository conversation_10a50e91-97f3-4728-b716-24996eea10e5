package com.holderzone.holder.saas.store.mdm.pipeline.item.inputs;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.TypeSyncDTO;
import com.holderzone.holder.saas.store.mdm.event.AbsErpRocketMqConsumer;
import com.holderzone.holder.saas.store.mdm.pipeline.item.agg.TypeAggService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MainMdmItemListener
 * @date 2019/11/23 下午7:34
 * @description //
 * @program holder
 */
@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.MainConfig.MAIN_MDM_TYPE_TOPIC,
        tags = {
                RocketMqConfig.MainConfig.MAIN_MDM_TYPE_CREATE_TAG,
                RocketMqConfig.MainConfig.MAIN_MDM_TYPE_UPDATE_TAG,
                RocketMqConfig.MainConfig.MAIN_MDM_TYPE_DELETE_TAG
        },
        consumerGroup = RocketMqConfig.MainConfig.MAIN_MDM_TYPE_GROUP
)
public class TypeMdmConsumer extends AbsErpRocketMqConsumer<RocketMqTopic, String> {

    private final TypeAggService typeAggService;

    @Autowired
    public TypeMdmConsumer(TypeAggService typeAggService) {
        this.typeAggService = typeAggService;
    }

    @Override
    protected boolean doConsumeMsg(String json, MessageExt messageExt) {
        String tags = messageExt.getTags();
        switch (tags) {
            case RocketMqConfig.MainConfig.MAIN_MDM_TYPE_CREATE_TAG:
                typeAggService.createLocalType(JacksonUtils.toObject(TypeSyncDTO.class, json));
                break;
            case RocketMqConfig.MainConfig.MAIN_MDM_TYPE_UPDATE_TAG:
                typeAggService.updateLocalType(JacksonUtils.toObject(TypeSyncDTO.class, json));
                break;
            case RocketMqConfig.MainConfig.MAIN_MDM_TYPE_DELETE_TAG:
                typeAggService.deleteLocalType(JacksonUtils.toObject(TypeSyncDTO.class, json));
                break;
            default:
                log.error("unknown mq tag : {}, message：{}",
                        messageExt.getTags(),
                        json);
                break;
        }
        return true;
    }
}
