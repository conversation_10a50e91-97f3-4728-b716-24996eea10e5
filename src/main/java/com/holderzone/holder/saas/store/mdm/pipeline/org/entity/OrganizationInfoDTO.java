package com.holderzone.holder.saas.store.mdm.pipeline.org.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.holderzone.holder.saas.store.mdm.pipeline.staff.entity.UserSyncDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
@Accessors(chain = true)
@JsonPropertyOrder(alphabetic = true)
public class OrganizationInfoDTO {

    /**
     * 主键
     */
    @JsonProperty("guid")
    @ApiModelProperty(value = "MDM生成的唯一标识", required = false)
    private String uuid;

    /**
     * 第三方唯一标识
     */
    @NotBlank(message = "第三方标识不得为空", groups = {Create.class, Update.class, Delete.class})
    private String thirdNo;

    /**
     * 编号
     */
    @Nullable
    private String code;

    /**
     * 名称
     */
    @NotBlank(message = "名称不得为空", groups = {Create.class, Update.class})
    private String name;

    /**
     * 营业时间(yyyy-MM-dd HH:mm:ss)
     */
    @Nullable
    private String businessTime;

    /**
     * 所属品牌（type=2时，必填）
     */
    @Nullable
    private String brandGuid;

    /**
     * 所属组织（默认为企业,type=0时，不填）
     */
    @Nullable
    private String fid;

    /**
     * 联系人
     */
    @Nullable
    private String contactName;

    /**
     * 联系电话
     */
    @Nullable
    private String contactTel;

    /**
     * 省(使用高德code码)
     */
    @Nullable
    private String province;

    private String provinceName;

    /**
     * 市(使用高德code码)
     */
    @Nullable
    private String city;

    private String cityName;

    /**
     * 区(使用高德code码)
     */
    @Nullable
    private String district;

    @Nullable
    private String districtName;

    /**
     * 详细地址
     */
    @Nullable
    private String address;
    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 组织类型，0：企业，1：组织，2：门店
     */
    @NotNull(message = "组织类型不得为空", groups = {Create.class, Update.class})
    private Integer type;

    /**
     * 组织描述
     */
    @Nullable
    private String remark;

    /**
     * 是否禁用，0 禁用，1 可用
     */
    private Integer enabled;

    /**
     * 相册集
     */
    private String photos;

    public interface Create extends Default {

    }

    public interface Update extends Default {

    }

    public interface Delete extends Default {

    }
}
