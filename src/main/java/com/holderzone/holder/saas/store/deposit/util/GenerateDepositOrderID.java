package com.holderzone.holder.saas.store.deposit.util;

import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

@Component
public class GenerateDepositOrderID {

    private final StringRedisTemplate redisTemplate;

    @Autowired
    public GenerateDepositOrderID(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public String generateDepositID(String storeGuid) {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + Strings.padStart(String.valueOf(
                generate(getKey("depositID", storeGuid), getTodayEndTime())), 4, '0');
    }

    private long generate(String key, Date expireTime) {
        RedisAtomicLong counter = new RedisAtomicLong(key, redisTemplate.getConnectionFactory());
        counter.expireAt(expireTime);
        return counter.incrementAndGet();
    }

    private static String getKey(String businessGroup, String businessKey) {
        StringBuffer buffer = new StringBuffer();
        buffer.append("deposit").append(":");
        buffer.append(businessGroup).append(":");
        buffer.append(businessKey);
        return buffer.toString();
    }

    /**
     * 获取当天结束时间
     *
     * @return
     */
    private static Date getTodayEndTime() {
        Calendar todayEnd = Calendar.getInstance();
        todayEnd.set(Calendar.HOUR_OF_DAY, 23);
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 999);
        return todayEnd.getTime();
    }

}
