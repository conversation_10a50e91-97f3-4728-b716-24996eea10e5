package com.holderzone.holder.saas.store.mdm.pipeline.org.agg;


import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.DeleteOrgReqDTO;
import com.holderzone.holder.saas.store.mdm.pipeline.org.entity.OrganizationInfoDTO;

import java.util.List;

/**
 * 组织 服务类
 *
 * <AUTHOR>
 * @since 2019-11-23
 */
public interface OrganizationAggService {

    void createLocalOrganization(OrganizationInfoDTO organizationInfoDTO);

    void updateLocalOrganization(OrganizationInfoDTO organizationInfoDTO);

    void deleteLocalOrganization(OrganizationInfoDTO organizationInfoDTO);

    void triggerRemoteOrganization(MdmTriggerType mdmTriggerType);

    void createRemoteOrganization(List<OrganizationInfoDTO> organizationInfoList);

    void updateRemoteOrganization(OrganizationInfoDTO organizationInfoDTO);

    void deleteRemoteOrganization(DeleteOrgReqDTO deleteOrgReqDTO);

}
