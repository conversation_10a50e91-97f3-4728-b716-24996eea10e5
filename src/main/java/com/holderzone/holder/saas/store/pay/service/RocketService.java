package com.holderzone.holder.saas.store.pay.service;


import com.holderzone.holder.saas.store.pay.entity.HandlerPayBO;
import com.holderzone.saas.store.dto.pay.AggPayPollingDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @className RocketService
 * @date 2019/03/14 15:05
 * @description mq消息service
 * @program holder-saas-store-trading-center
 */
public interface RocketService {

    boolean polling(AggPayPollingDTO aggPayPollingDTO, HandlerPayBO handlerPayBO);

}
