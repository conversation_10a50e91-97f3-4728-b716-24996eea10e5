package com.holderzone.holder.saas.store.table.domain.bo;

import com.holderzone.saas.store.dto.common.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/01/25 10:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DelayAutoUnlockBO extends BaseDTO {

    private List<TableInfoBO> tableInfoBos;
}
