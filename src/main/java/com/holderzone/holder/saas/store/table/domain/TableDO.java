package com.holderzone.holder.saas.store.table.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/01/04 15:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TableDO extends BaseDO {

    /**
     * 区域guid
     */
    private String areaGuid;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 桌台编号
     */
    private String tableCode;

    /**
     * 桌台座位数
     */
    private Integer seats;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否已启用
     * 0=已启用
     * 1=未启用
     * 默认启用
     */
    private Integer enable;

    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    private Integer deleted;

    /**
     * 主单号
     */
    private String mainOrderGuid;

    /**
     * 订单号
     */
    private String orderGuid;

    /**
     * 桌台状态
     * -1=暂停使用
     * 0=空闲
     * 1=占用
     * 2=预定
     * 3=待清台
     */
    private Integer status;

    /**
     * 10=占用空台
     * 11=占用锁定
     * 12=占用并台
     * 20=预定未锁定
     * 21=预定已锁定
     * 数据库存储的是一个json的集合
     */
    private String subStatus;

    private Integer combineTimes;

    private Integer associatedTimes;

    private String openStaffGuid;

    private String openStaffName;

    private LocalDateTime openTableTime;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
//
//    private Set<Integer> makeSubStatus() {
//        if (StringUtils.hasText(this.subStatus)) {
//            return new HashSet<>(JacksonUtils.toObjectList(Integer.class, this.subStatus));
//        }
//        return new HashSet<>();
//    }
}
