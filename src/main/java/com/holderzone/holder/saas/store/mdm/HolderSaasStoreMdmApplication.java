package com.holderzone.holder.saas.store.mdm;

import com.holderzone.framework.canal.starter.EnableCanalConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableSwagger2
@EnableCanalConfig
@EnableFeignClients
@EnableEurekaClient
@SpringBootApplication
@MapperScan({
        "com.holderzone.holder.saas.store.mdm.pipeline.item.mapper",
        "com.holderzone.holder.saas.store.mdm.pipeline.org.mapper",
        "com.holderzone.holder.saas.store.mdm.pipeline.staff.mapper",
})
public class HolderSaasStoreMdmApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasStoreMdmApplication.class, args);
    }

}
