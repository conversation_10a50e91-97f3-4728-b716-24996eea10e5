package com.holderzone.holder.saas.store.mdm.pipeline.item.outputs;

import com.holderzone.framework.rocketmq.anno.RocketListenerHandler;
import com.holderzone.framework.rocketmq.constants.RocketMqTopic;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.store.mdm.config.RocketMqConfig;
import com.holderzone.holder.saas.store.mdm.entity.MdmTriggerType;
import com.holderzone.holder.saas.store.mdm.event.AbsErpRocketMqConsumer;
import com.holderzone.holder.saas.store.mdm.pipeline.item.agg.ItemAggService;
import com.holderzone.holder.saas.store.mdm.pipeline.item.entity.ItemSyncDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketListenerHandler(
        topic = RocketMqConfig.StoreConfig.STORE_MDM_ITEM_TOPIC,
        tags = {
                RocketMqConfig.StoreConfig.STORE_MDM_ITEM_INIT_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_ITEM_CREATE_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_ITEM_UPDATE_TAG,
                RocketMqConfig.StoreConfig.STORE_MDM_ITEM_DELETE_TAG
        },
        consumerGroup = RocketMqConfig.StoreConfig.STORE_MDM_ITEM_GROUP)
public class ItemCanalConsumer extends AbsErpRocketMqConsumer<RocketMqTopic, String> {

    private final ItemAggService itemAggService;

    @Autowired
    public ItemCanalConsumer(ItemAggService itemAggService) {
        this.itemAggService = itemAggService;
    }

    @Override
    public boolean doConsumeMsg(String json, MessageExt messageExt) {
        String tags = messageExt.getTags();
        log.info("ItemCanalConsumer doConsumeMsg tags={},json={}", tags, json);
        switch (tags) {
            case RocketMqConfig.StoreConfig.STORE_MDM_ITEM_INIT_TAG:
                itemAggService.triggerRemoteItem(MdmTriggerType.ofType(json));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_ITEM_CREATE_TAG:
                itemAggService.createRemoteItem(JacksonUtils.toObjectList(ItemSyncDTO.class, json));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_ITEM_UPDATE_TAG:
                itemAggService.updateRemoteItem(JacksonUtils.toObject(ItemSyncDTO.class, json));
                break;
            case RocketMqConfig.StoreConfig.STORE_MDM_ITEM_DELETE_TAG:
                itemAggService.deleteRemoteItem(JacksonUtils.toObjectList(ItemSyncDTO.class, json));
                break;
            default:
                log.error("unknown mq tag : {}, message：{}",
                        messageExt.getTags(),
                        JacksonUtils.writeValueAsString(json));
                break;
        }
        return true;
    }
}
