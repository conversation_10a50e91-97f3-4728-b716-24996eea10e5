package com.holderzone.holder.saas.store.table.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.rocketmq.common.DefaultRocketMqProducer;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.store.table.client.TradeRpcClient;
import com.holderzone.holder.saas.store.table.config.AutoDelayRelease;
import com.holderzone.holder.saas.store.table.constant.Constant;
import com.holderzone.holder.saas.store.table.constant.RocketMqConstant;
import com.holderzone.holder.saas.store.table.domain.TableAssociatedDO;
import com.holderzone.holder.saas.store.table.domain.TableBasicDO;
import com.holderzone.holder.saas.store.table.domain.TableDO;
import com.holderzone.holder.saas.store.table.domain.TableOrderDO;
import com.holderzone.holder.saas.store.table.domain.bo.DelayAutoUnlockBO;
import com.holderzone.holder.saas.store.table.domain.bo.TableBO;
import com.holderzone.holder.saas.store.table.domain.bo.TableInfoBO;
import com.holderzone.holder.saas.store.table.domain.enums.StateEnum;
import com.holderzone.holder.saas.store.table.domain.enums.UpperStateEnum;
import com.holderzone.holder.saas.store.table.exception.TableNotAllSuccessException;
import com.holderzone.holder.saas.store.table.mapper.TableBasicMapper;
import com.holderzone.holder.saas.store.table.mapper.TableOrderMapper;
import com.holderzone.holder.saas.store.table.service.*;
import com.holderzone.holder.saas.store.table.utils.HttpUtil;
import com.holderzone.saas.store.dto.call.TurnCallMemberDTO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.OrderTableBillReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.ReserveBatchCreateOrderReqDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.*;
import com.holderzone.saas.store.dto.table.trade.TradeTableDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreTableCombineDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.table.TableStatusChangeEnum;
import com.holderzone.saas.store.enums.table.TableStatusEnum;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.session.SqlSession;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.holderzone.holder.saas.store.table.utils.TableMapStruct.TABLE_MAP_STRUCT;
import static com.holderzone.holder.saas.store.table.utils.WxStoreTableCombineMapStruct.WX_STORE_TABLE_COMBINE_MAP_STRUCT;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableOrderServceImpl
 * @date 2019/01/02 16:47
 * @description
 * @program holder-saas-store-table
 */
@Slf4j
@Service
public class TableOrderServiceImpl extends ServiceImpl<TableOrderMapper, TableOrderDO> implements TableOrderService {

    @Value("${member.center.hostUrl}")
    private String memberCenterHostUrl;

    private final TableOrderMapper tableOrderMapper;

    private final RedisService redisService;

    private final TradeService tradeService;

    private final ReserveService reserveService;

    private final BizMsgService bizMsgService;

    private final AutoDelayRelease release;

    private final PrintService printService;

    private final DefaultRocketMqProducer defaultRocketMqProducer;

    private final TradeRpcClient tradeRpcClient;

    private final BindUpAccountsService bindUpAccountsService;

    private final TableBasicMapper tableBasicMapper;

    private final TableAssociatedService tableAssociatedService;

    @Resource
    @Lazy
    private AreaService areaService;

    @Autowired
    public TableOrderServiceImpl(TableOrderMapper tableOrderMapper,
                                 RedisService redisService,
                                 TradeService tradeService,
                                 ReserveService reserveService,
                                 BizMsgService bizMsgService,
                                 AutoDelayRelease release,
                                 PrintService printService,
                                 DefaultRocketMqProducer defaultRocketMqProducer,
                                 TradeRpcClient tradeRpcClient,
                                 BindUpAccountsService bindUpAccountsService,
                                 TableBasicMapper tableBasicMapper,
                                 TableAssociatedService tableAssociatedService) {
        this.tableOrderMapper = tableOrderMapper;
        this.redisService = redisService;
        this.tradeService = tradeService;
        this.reserveService = reserveService;
        this.bizMsgService = bizMsgService;
        this.release = release;
        this.printService = printService;
        this.defaultRocketMqProducer = defaultRocketMqProducer;
        this.tradeRpcClient = tradeRpcClient;
        this.bindUpAccountsService = bindUpAccountsService;
        this.tableBasicMapper = tableBasicMapper;
        this.tableAssociatedService = tableAssociatedService;
    }

    @Override
    public List<TableOrderDTO> listTable(TableBasicQueryDTO tableBasicQueryDTO) {
        // 桌台基础信息
        List<TableDO> tableDOList = tableOrderMapper.selectAllTable(tableBasicQueryDTO);
        List<TableBO> tableBOS = TABLE_MAP_STRUCT.tableDoList2BoList(tableDOList);
        return TABLE_MAP_STRUCT.tableBoList2DtoList(tableBOS);
    }

    @Override
    public List<String> listTableOccupied(List<String> guids) {
        if (guids.isEmpty()) {
            return Collections.emptyList();
        }
        List<TableOrderDO> tableOrderOccupied = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                .select(TableOrderDO::getTableGuid)
                .in(TableOrderDO::getTableGuid, guids)
                .ne(TableOrderDO::getStatus, TableStatusEnum.FREE.getStatus())
        );
        if (CollectionUtils.isEmpty(tableOrderOccupied)) return Collections.emptyList();
        return tableOrderOccupied.stream().map(TableOrderDO::getTableGuid).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public String open(OpenTableDTO openTableDTO, Integer tag) {
        // 开台
        TableOrderDO db = selectOneByTableGuid(openTableDTO.getTableGuid());
        TableOrderDO tableOrderDO = TABLE_MAP_STRUCT.openTableDto2OrderDo(openTableDTO);
        tableOrderDO.setStatus(TableStatusEnum.TAKE_UP.getStatus());
        tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(new HashSet<Integer>() {{
            // todo 可能涉及到子状态的清除和覆盖 比如预定，并桌情况
            add(TableStatusEnum.TAKE_UP_WITHOUT_ORDER_DISH.getStatus());
            if (db.makeSubStatus().contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus())) {
                add(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
            }
        }}));
        tableOrderDO.setOpenTableTime(DateTimeUtils.now());
        String orderGuid = redisService.singleGuid("hst_order");
        tableOrderDO.setOrderGuid(orderGuid);
        open(openTableDTO, tableOrderDO, tag);
        // 调用订单服务创建订单
        CreateDineInOrderReqDTO createDineInOrderReqDTO =
                TABLE_MAP_STRUCT.openTableDTO2CreateDineInOrderReqDTO(openTableDTO);
        createDineInOrderReqDTO.setGuid(orderGuid);
        createDineInOrderReqDTO.setAreaName(openTableDTO.getAreaName());
        createDineInOrderReqDTO.setDiningTableGuid(openTableDTO.getTableGuid());
        createDineInOrderReqDTO.setDiningTableName(openTableDTO.getTableCode());
        createDineInOrderReqDTO.setGuestCount(openTableDTO.getActualGuestsNo());
        createDineInOrderReqDTO.setAreaGuid(openTableDTO.getAreaGuid());
//		createDineInOrderReqDTO.setAreaGuid("6574169003189600256");
        tradeService.openTable(createDineInOrderReqDTO);
        return orderGuid;
    }

    @Override
    @Transactional
    public ReserveOpenTableDTO batchOpen(BatchOpenTableDTO batchOpenTableDTO) {
        List<OpenTableDTO> dtos = batchOpenTableDTO.getOpenTableDTOS();
        List<String> tables = dtos.stream().map(OpenTableDTO::getTableGuid).distinct().collect(Collectors.toList());
        TableBasicQueryDTO query = new TableBasicQueryDTO();
        String storeGuid = UserContextUtils.get().getStoreGuid();
        query.setStoreGuid(storeGuid);
        query.setTableGuidList(tables);
        List<TableOrderDTO> tableOrderDTOS = listTable(query);
        List<String> tableDbGuids = tableOrderDTOS.stream().map(TableOrderDTO::getTableGuid).distinct().collect(Collectors.toList());
        if (tableDbGuids.size() != tables.size()) {
            tables.removeAll(tableDbGuids);
            return new ReserveOpenTableDTO(null, null, tables, false, null, null);
        }
        List<String> fail = tableOrderDTOS.stream().filter((e) ->
                e.getStatus() != TableStatusEnum.FREE.getStatus().intValue()
                        && e.getStatus() != TableStatusEnum.RESERVATION.getStatus().intValue()).map(TableOrderDTO::getTableGuid).collect(Collectors.toList());
        Map<String, TableOrderDTO> ref = tableOrderDTOS.stream().collect(Collectors.toMap(TableOrderDTO::getTableGuid, Function.identity()));
        if (!fail.isEmpty()) {
            return new ReserveOpenTableDTO(null, fail, null, false, null, null);
        }
        List<String> fails = new ArrayList<>();
        List<BaseTableDTO> baseTableDTOS = new ArrayList<>();
        List<String> orderGuids = redisService.batchGuid(dtos.size(), "hst_order");
        dtos.stream().forEach((openTableDTO) -> {
            String orderGuid = orderGuids.get(dtos.indexOf(openTableDTO));
            // 开台
            TableOrderDO tableOrderDO = TABLE_MAP_STRUCT.openTableDto2OrderDo(openTableDTO);
            tableOrderDO.setStatus(TableStatusEnum.TAKE_UP.getStatus());
            tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(new HashSet<Integer>() {{
                // todo 可能涉及到子状态的清除和覆盖 比如预定，并桌情况
                add(TableStatusEnum.TAKE_UP_WITHOUT_ORDER_DISH.getStatus());
                TableOrderDTO db = ref.get(openTableDTO.getTableGuid());
                if (db.getSubStatus() != null && db.getSubStatus().contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus())) {
                    add(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
                }
            }}));
            tableOrderDO.setOpenTableTime(DateTimeUtils.now());
            tableOrderDO.setOrderGuid(orderGuid);
            try {
                open(openTableDTO, tableOrderDO, 1);
                baseTableDTOS.add(new BaseTableDTO(tableOrderDO.getTableGuid(), orderGuid, false));
            } catch (BusinessException e) {
                fails.add(openTableDTO.getTableGuid());
            }
        });

        if (!fails.isEmpty()) {
            throw new TableNotAllSuccessException("partition fail", new ReserveOpenTableDTO(null, fails, null, false, null, null));
        }
        ReserveBatchCreateOrderReqDTO reserveBatchCreateOrderReqDTO = new ReserveBatchCreateOrderReqDTO();
        List<CreateDineInOrderReqDTO> createDineInOrderReqDTOS = dtos.stream().map((openTableDTO) -> {
            String orderGuid = orderGuids.get(dtos.indexOf(openTableDTO));
            CreateDineInOrderReqDTO createDineInOrderReqDTO =
                    TABLE_MAP_STRUCT.openTableDTO2CreateDineInOrderReqDTO(openTableDTO);
            createDineInOrderReqDTO.setGuid(orderGuid);
            createDineInOrderReqDTO.setAreaName(openTableDTO.getAreaName());
            createDineInOrderReqDTO.setDiningTableGuid(openTableDTO.getTableGuid());
            createDineInOrderReqDTO.setDiningTableName(openTableDTO.getTableCode());
            createDineInOrderReqDTO.setAreaGuid(openTableDTO.getTableGuid());
            createDineInOrderReqDTO.setGuestCount(openTableDTO.getActualGuestsNo());
            return createDineInOrderReqDTO;
        }).collect(Collectors.toList());
        String mainOrderGuid = orderGuids.size() > 0 ? orderGuids.get(0) : null;
        String mainTableGuid = dtos.size() > 0 ? dtos.get(0).getTableGuid() : null;
        reserveBatchCreateOrderReqDTO.setReservePhone(batchOpenTableDTO.getReservePhone());
        reserveBatchCreateOrderReqDTO.setCreateDineInOrderReqDTOS(createDineInOrderReqDTOS);
        reserveBatchCreateOrderReqDTO.setReserveGuid(batchOpenTableDTO.getReserveGuid());
        reserveBatchCreateOrderReqDTO.setContainDish(batchOpenTableDTO.getContainDish());
        reserveBatchCreateOrderReqDTO.setReserveFee(batchOpenTableDTO.getReserveAmount());
        reserveBatchCreateOrderReqDTO.setMainOrderGuid(mainOrderGuid);
        tradeService.bacthopenTable(reserveBatchCreateOrderReqDTO);
        if (tables.size() > 1) {
            TableCombineDTO tableCombineDTO = new TableCombineDTO();
            BeanUtils.copyProperties(dtos.get(0), tableCombineDTO);
            tableCombineDTO.setMainOrderGuid(mainOrderGuid);
            tableCombineDTO.setMainTableGuid(mainTableGuid);
            tables.remove(tableCombineDTO.getMainTableGuid());
            tableCombineDTO.setTableGuidList(tables);
            combine(tableCombineDTO, 1);
        }
        tables.removeAll(fails);
        if (!tables.isEmpty()) {
            bizMsgService.sendMsg(new BaseDTO() {{
                setStoreGuid(UserContextUtils.get().getStoreGuid());
            }}, tables);
        }
        return new ReserveOpenTableDTO(orderGuids, fails, Lists.newArrayList(), true, mainOrderGuid, mainTableGuid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String associatedOpen(OpenAssociatedTableDTO openAssociatedTableDTO) {
        // 联台
        TableBasicQueryDTO query = new TableBasicQueryDTO();
        String storeGuid = UserContextUtils.get().getStoreGuid();
        // 查询需要联台的所有桌台列表
        query.setStoreGuid(storeGuid);
        query.setTableGuidList(openAssociatedTableDTO.getTableGuids());
        List<TableOrderDTO> tableOrderDTOList = listTable(query);
        // 校验联台桌台是否能开台
        String fail = verifyAssociatedOpenTable(openAssociatedTableDTO, tableOrderDTOList);
        if (!StringUtils.isEmpty(fail)) {
            // 主桌开台失败 直接返回 -1
            log.error("主桌开台失败, 返回前端code成功, data返回-1");
            return fail;
        }
        // 生成订单guid
        String orderGuid = redisService.singleGuid("hst_order");
        Integer associatedTimes = redisService.getAssociatedTimes(storeGuid);
        log.info("门店:{} redis计算联台次数：{}", storeGuid, associatedTimes);
        try {
            // 构建需要更新的联台桌台列表
            List<TableOrderDO> tableOrderList = buildAssociatedTableOrderList(orderGuid, associatedTimes, tableOrderDTOList);
            // 联台信息保存
            associatedOpen(openAssociatedTableDTO, tableOrderList);
            // 调用订单服务创建订单
            openAssociatedTable(openAssociatedTableDTO, orderGuid, associatedTimes, tableOrderDTOList);
            // 通知mqtt告知其他门店设备该桌位已经开台
            String result = bizMsgService.sendMsg(openAssociatedTableDTO, openAssociatedTableDTO.getTableGuids());
            log.info("发送联台消息结果，result={}", result);
        } catch (Exception e) {
            log.error("联台失败, e:", e);
            // 回滚associatedTimes
            redisService.rollBackAssociatedTimes(storeGuid);
            throw new BusinessException(e.getMessage());
        }
        return orderGuid;
    }

    /**
     * 校验联台桌台是否能开台
     * 预校验
     */
    private String verifyAssociatedOpenTable(OpenAssociatedTableDTO openAssociatedTableDTO, List<TableOrderDTO> tableOrderDTOList) {
        List<String> tableDbGuids = tableOrderDTOList.stream()
                .map(TableOrderDTO::getTableGuid)
                .distinct()
                .collect(Collectors.toList());
        if (tableDbGuids.size() != openAssociatedTableDTO.getTableGuids().size()) {
            log.error("查询桌台数量和传入数量不一致");
            throw new BusinessException(Constant.TABLE_ASSOCIATED_EXIST);
        }
        // 判断主桌是否能开台
        TableOrderDTO mainTableOrderDTO = tableOrderDTOList.stream()
                .filter(e -> e.getTableGuid().equals(openAssociatedTableDTO.getMainTableGuid()))
                .findFirst().orElse(null);
        if (Objects.isNull(mainTableOrderDTO) ||
                (mainTableOrderDTO.getStatus() != TableStatusEnum.FREE.getStatus().intValue()
                        && mainTableOrderDTO.getStatus() != TableStatusEnum.RESERVATION.getStatus().intValue())) {
            return "-1";
        }
        List<String> openedTableList = tableOrderDTOList.stream()
                .filter(e -> e.getStatus() != TableStatusEnum.FREE.getStatus().intValue()
                        && e.getStatus() != TableStatusEnum.RESERVATION.getStatus().intValue())
                .map(TableOrderDTO::getTableGuid)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(openedTableList)) {
            throw new BusinessException(Constant.TABLE_ASSOCIATED_EXIST);
        }
        boolean associatedExist = tableAssociatedService.isExist(tableDbGuids);
        if (associatedExist) {
            throw new BusinessException(Constant.TABLE_ASSOCIATED_EXIST);
        }
        return null;
    }

    /**
     * 创建联台单
     */
    private void openAssociatedTable(OpenAssociatedTableDTO openAssociatedTableDTO, String orderGuid,
                                     Integer associatedTimes, List<TableOrderDTO> tableOrderDTOList) {
        // 调用订单服务创建订单
        Map<String, TableOrderDTO> tableOrderMap = tableOrderDTOList.stream()
                .collect(Collectors.toMap(TableOrderDTO::getTableGuid, Function.identity(), (key1, key2) -> key1));
        // 主单
        TableOrderDTO mainTableOrderDTO = tableOrderMap.get(openAssociatedTableDTO.getTableGuids().get(0));
        CreateDineInOrderReqDTO createDineInOrderReqDTO =
                TABLE_MAP_STRUCT.openAssociatedTableDTO2CreateDineInOrderReqDTO(openAssociatedTableDTO);
        createDineInOrderReqDTO.setGuid(orderGuid);
        createDineInOrderReqDTO.setAssociatedFlag(true);
        createDineInOrderReqDTO.setAssociatedSn(String.valueOf(associatedTimes));
        createDineInOrderReqDTO.setAreaName(mainTableOrderDTO.getAreaName());
        createDineInOrderReqDTO.setDiningTableGuid(mainTableOrderDTO.getTableGuid());
        createDineInOrderReqDTO.setDiningTableName(mainTableOrderDTO.getTableCode());
        createDineInOrderReqDTO.setGuestCount(openAssociatedTableDTO.getActualGuestsNo());
        createDineInOrderReqDTO.setAreaGuid(mainTableOrderDTO.getAreaGuid());
        // 联台桌台列表
        Map<String, String> associatedTableMap = tableOrderDTOList.stream().collect(Collectors.toMap(TableOrderDTO::getTableGuid,
                i -> i.getAreaName() + "-" + i.getTableCode()));
        createDineInOrderReqDTO.setAssociatedTableGuids(Lists.newArrayList());
        createDineInOrderReqDTO.setAssociatedTableNames(Lists.newArrayList());
        for (String tableGuid : openAssociatedTableDTO.getTableGuids()) {
            createDineInOrderReqDTO.getAssociatedTableGuids().add(tableGuid);
            createDineInOrderReqDTO.getAssociatedTableNames().add(associatedTableMap.get(tableGuid));
        }
        log.info("联台下单入参:{}", JacksonUtils.writeValueAsString(createDineInOrderReqDTO));
        tradeService.openTable(createDineInOrderReqDTO);
    }


    @Override
    @Transactional
    public List<String> reserve(List<String> tableGuids) {
        List<String> result = new ArrayList<>();
        TableBasicQueryDTO query = new TableBasicQueryDTO();
        String storeGuid = UserContextUtils.get().getStoreGuid();
        query.setStoreGuid(storeGuid);
        query.setTableGuidList(tableGuids);
        List<TableOrderDTO> tableOrderDTOS = listTable(query);
        Map<String, TableOrderDTO> ref = tableOrderDTOS.stream().collect(Collectors.toMap(TableOrderDTO::getTableGuid, Function.identity()));
        // 为保证原子性，需判断状态TableStatusEnum.FREE
        tableGuids.forEach((e) -> {
            TableOrderDO tableOrderDO = new TableOrderDO();
            tableOrderDO.setTableGuid(e);
            tableOrderDO.setStatus(TableStatusEnum.RESERVATION.getStatus());
            tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(new HashSet<Integer>() {{
                // todo 可能涉及到子状态的清除和覆盖 比如预定，并桌情况
                add(TableStatusEnum.RESERVATION_LOCK.getStatus());
                TableOrderDTO db = ref.get(e);
                if (db.getSubStatus() != null && db.getSubStatus().contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus())) {
                    add(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
                }
            }}));
            int updateResult = tableOrderMapper.update(tableOrderDO, new LambdaQueryWrapper<TableOrderDO>()
                    .eq(TableOrderDO::getTableGuid, tableOrderDO.getTableGuid())
                    .eq(TableOrderDO::getStatus, TableStatusEnum.FREE.getStatus())
            );
            if (updateResult == 0) {
                result.add(e);
            }
        });
        tableGuids.removeAll(result);

        if (!tableGuids.isEmpty()) {
            bizMsgService.sendMsg(new BaseDTO() {{
                setStoreGuid(UserContextUtils.get().getStoreGuid());
            }}, tableGuids);
        }
        return result;
    }

    @Override
    @Transactional
    public void prepare(List<String> add, List<String> del) {
        List<String> all = new ArrayList<>(add);
        all.addAll(del);
        List<TableOrderDO> allDo = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>().in(TableOrderDO::getTableGuid, all));
        allDo.forEach((e) -> {
            if (add.contains(e.getTableGuid())) {
                doPrepare(e);
            }
            if (del.contains(e.getTableGuid())) {
                doCanclePrepare(e);
            }
        });
    }

    private void doPrepare(TableOrderDO d) {
        Set<Integer> subStatus = d.makeSubStatus();
        if (!subStatus.contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus())) {
            subStatus.add(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
            TableOrderDO tableOrderDO = new TableOrderDO();
            tableOrderDO.setTableGuid(d.getTableGuid());
            tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(subStatus));
            int updateResult = tableOrderMapper.update(tableOrderDO,
                    new LambdaQueryWrapper<TableOrderDO>()
                            .eq(TableOrderDO::getTableGuid, tableOrderDO.getTableGuid())
                            .eq(Objects.nonNull(d.getSubStatus()), TableOrderDO::getSubStatus, d.getSubStatus())
            );
            if (updateResult == 0) {
                doPrepare(tableOrderMapper.selectOne(new LambdaQueryWrapper<TableOrderDO>()
                        .eq(TableOrderDO::getTableGuid, d.getTableGuid())));
            } else {
                BaseDTO prepareBaseDTO = new BaseDTO();
                prepareBaseDTO.setStoreGuid(UserContextUtils.get().getStoreGuid());
                List<String> tableGuids = Lists.newArrayList(d.getTableGuid());
                List<String> associatedTableGuids = getAssociatedTableGuids(d);
                if (CollectionUtils.isNotEmpty(associatedTableGuids)) {
                    tableGuids.addAll(associatedTableGuids);
                }
                tableGuids = tableGuids.stream().distinct().collect(Collectors.toList());
                bizMsgService.sendMsg(prepareBaseDTO, tableGuids);
            }
        }
    }

    private void doCanclePrepare(TableOrderDO d) {
        Set<Integer> subStatus = d.makeSubStatus();
        if (subStatus.contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus())) {
            subStatus.remove(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
            TableOrderDO tableOrderDO = new TableOrderDO();
            tableOrderDO.setTableGuid(d.getTableGuid());
            tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(subStatus));
            int updateResult = tableOrderMapper.update(tableOrderDO,
                    new LambdaQueryWrapper<TableOrderDO>()
                            .eq(TableOrderDO::getTableGuid, tableOrderDO.getTableGuid())
                            .eq(TableOrderDO::getSubStatus, d.getSubStatus())
            );
            if (updateResult == 0) {
                doCanclePrepare(tableOrderMapper.selectOne(new LambdaQueryWrapper<TableOrderDO>()
                        .eq(TableOrderDO::getTableGuid, d.getTableGuid())));
            } else {
                BaseDTO cancelBaseDTO = new BaseDTO();
                cancelBaseDTO.setStoreGuid(UserContextUtils.get().getStoreGuid());
                List<String> tableGuids = Lists.newArrayList(d.getTableGuid());
                List<String> associatedTableGuids = getAssociatedTableGuids(d);
                if (CollectionUtils.isNotEmpty(associatedTableGuids)) {
                    tableGuids.addAll(associatedTableGuids);
                }
                tableGuids = tableGuids.stream().distinct().collect(Collectors.toList());
                bizMsgService.sendMsg(cancelBaseDTO, tableGuids);
            }
        }
    }

    /**
     * 查询联台桌台列表
     */
    private List<String> getAssociatedTableGuids(TableOrderDO tableOrderDO) {
        Set<Integer> subStatus = tableOrderDO.makeSubStatus();
        if (!subStatus.contains(TableStatusEnum.TAKE_UP_ASSOCIATED.getStatus())) {
            return Lists.newArrayList();
        }
        // 查询联台桌台
        List<TableAssociatedDO> tableAssociatedList = tableAssociatedService.listByTableGuid(tableOrderDO.getTableGuid());
        if (CollectionUtils.isEmpty(tableAssociatedList)) {
            return Lists.newArrayList();
        }
        List<String> associatedTableGuidList = tableAssociatedList.stream()
                .map(TableAssociatedDO::getAssociatedTableGuid)
                .distinct()
                .collect(Collectors.toList());
        associatedTableGuidList.add(tableAssociatedList.get(0).getTableGuid());
        return associatedTableGuidList;
    }

    @Override
    @Transactional
    public List<String> cancleReserve(List<String> tableGuids) {
        List<String> result = new ArrayList<>();
        TableBasicQueryDTO query = new TableBasicQueryDTO();
        String storeGuid = UserContextUtils.get().getStoreGuid();
        query.setStoreGuid(storeGuid);
        query.setTableGuidList(tableGuids);
        List<TableOrderDTO> tableOrderDTOS = listTable(query);
        Map<String, TableOrderDTO> ref = tableOrderDTOS.stream().collect(Collectors.toMap(TableOrderDTO::getTableGuid, Function.identity()));
        // 为保证原子性，需判断状态TableStatusEnum.FREE
        tableGuids.forEach((e) -> {
            TableOrderDO tableOrderDO = new TableOrderDO();
            tableOrderDO.setTableGuid(e);
            tableOrderDO.setStatus(TableStatusEnum.FREE.getStatus());
            tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(new HashSet<Integer>() {{
                TableOrderDTO db = ref.get(e);
                if (db.getSubStatus() != null && db.getSubStatus().contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus())) {
                    add(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
                }
            }}));
            int updateResult = tableOrderMapper.update(tableOrderDO, new LambdaQueryWrapper<TableOrderDO>()
                    .eq(TableOrderDO::getTableGuid, tableOrderDO.getTableGuid())
                    .eq(TableOrderDO::getStatus, TableStatusEnum.RESERVATION.getStatus())
            );
            if (updateResult == 0) {
                result.add(e);
            }
        });

        tableGuids.removeAll(result);

        if (!tableGuids.isEmpty()) {
            bizMsgService.sendMsg(new BaseDTO() {{
                setStoreGuid(UserContextUtils.get().getStoreGuid());
            }}, tableGuids);
        }
        return result;
    }

    /**
     * 资源分析：
     * 源桌台、目标桌台
     * 锁分析
     * rds锁：锁定newTableGuid，
     * biz锁
     *
     * @param turnTableDTO
     * @return
     */
    @Override
    public boolean turn(TurnTableDTO turnTableDTO) {
        // 源桌台锁定判断
        List<String> tableGuidList = Arrays.asList(turnTableDTO.getNewTableGuid(), turnTableDTO.getOriginTableGuid());
        if (redisService.isTableLockedByOthers(turnTableDTO.getDeviceId(), tableGuidList)) {
            throw new BusinessException("桌台已被锁定");
        }
        // 转到的桌台是不是空闲状态
        List<TableOrderDO> tableOrderDOS = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                .in(TableOrderDO::getTableGuid, tableGuidList));
        TableOrderDO oriTable = null;
        TableOrderDO newTable = null;
        for (TableOrderDO orderDO : tableOrderDOS) {
            if (Objects.equals(orderDO.getTableGuid(), turnTableDTO.getNewTableGuid())) {
                newTable = orderDO;
            } else {
                oriTable = orderDO;
            }
        }
        if (null == oriTable || null == newTable) {
            throw new BusinessException("未找到相应桌台");
        }
        if (!Objects.equals(newTable.getStatus(), TableStatusEnum.FREE.getStatus())) {
            throw new BusinessException("被转的桌台不处于空闲状态");
        }
        // 复制桌台信息
        turnCopyTable(oriTable, newTable);

        tableOrderMapper.updateAll(Arrays.asList(oriTable, newTable));
        // 通知订单服务转台
        TradeTableDTO tradeTableDTO = TABLE_MAP_STRUCT.turnTableDto2tradeTableDto(turnTableDTO);
        tradeTableDTO.setOrderGuid(newTable.getOrderGuid());
        tradeService.notifyTradeTurn(tradeTableDTO);
        // 通知预定服务转台
        TableBasicDO tableBasicDO = tableBasicMapper.selectOne(new LambdaQueryWrapper<TableBasicDO>()
                .eq(TableBasicDO::getGuid, tradeTableDTO.getTableGuid()));
        if (Objects.nonNull(tableBasicDO)) {
            tradeTableDTO.setAreaGuid(tableBasicDO.getAreaGuid());
        }
        reserveService.notifyTurn(tradeTableDTO);
        // 通知打印服务，打印转台单
        queryAndSetTableName(turnTableDTO);
        printService.printTurnTable(turnTableDTO);
        String string = bizMsgService.sendMsg(turnTableDTO, tableGuidList);
        log.info("发送消息服务result={}", string);
        TableStatusChangeMQDTO tableStatusChangeMQDTO = new TableStatusChangeMQDTO(UserContextUtils.getEnterpriseGuid(), RocketMqConstant.TABLE_STATUS_CHANGE_MQ_TURN, turnTableDTO);
        log.info("转台发送消息:{}", tableStatusChangeMQDTO);
        defaultRocketMqProducer.sendMessage(new Message(RocketMqConstant.TABLE_STATUS_CHANGE_MQ_TABLE, RocketMqConstant.TABLE_STATUS_CHANGE_MQ_TURN,
                JacksonUtils.toJsonByte(tableStatusChangeMQDTO)));

        // 转台发送消息给pad
        bizMsgService.sendMsg(turnTableDTO);

        // 转台后通知到会员
        notifyMember(turnTableDTO, newTable);
        return true;
    }


    /**
     * 查询桌台名称
     */
    private void queryAndSetTableName(TurnTableDTO turnTableDTO) {
        List<AreaDTO> areaList = areaService.queryBatchAreaByTable(Lists.newArrayList(turnTableDTO.getOriginTableAreaGuid(),
                turnTableDTO.getNewTableAreaGuid()));
        if (CollectionUtils.isEmpty(areaList)) {
            return;
        }
        Map<String, String> areaMap = areaList.stream()
                .collect(Collectors.toMap(AreaDTO::getGuid, AreaDTO::getAreaName, (key1, key2) -> key1));
        turnTableDTO.setOriginTableAreaName(areaMap.getOrDefault(turnTableDTO.getOriginTableAreaGuid(), turnTableDTO.getOriginTableAreaName()));
        turnTableDTO.setNewTableAreaName(areaMap.getOrDefault(turnTableDTO.getNewTableAreaGuid(), turnTableDTO.getNewTableAreaName()));
    }

    /**
     * 转台后通知到会员
     *
     * @param turnTableDTO 转台入参
     * @param newTable     新桌信息
     */
    private void notifyMember(TurnTableDTO turnTableDTO, TableOrderDO newTable) {
        OrderDTO orderDTO = tradeRpcClient.findByOrderGuid(newTable.getOrderGuid());
        TurnCallMemberDTO turnCallMemberDTO = new TurnCallMemberDTO();
        turnCallMemberDTO.setOrderNumber(newTable.getOrderGuid());
        turnCallMemberDTO.setStoreGuid(turnTableDTO.getStoreGuid());
        turnCallMemberDTO.setTableGuid(turnTableDTO.getNewTableGuid());
        turnCallMemberDTO.setOrderSource(orderDTO.getDeviceType());
        turnCallMemberDTO.setOrderState(orderDTO.getState());
        turnCallMemberDTO.setOperSubjectGuid(UserContextUtils.get().getOperSubjectGuid());
        String httpRequestUrl = String.format("%s/hsa_follow/order_rotary_table_call_back", memberCenterHostUrl).intern();
        log.info("转台后通知到会员 httpRequestUrl={}", httpRequestUrl);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("source", "2");
        try {
            String result = HttpUtil.doPostJsonHeader(httpRequestUrl, JacksonUtils.writeValueAsString(turnCallMemberDTO), headerMap);
            log.info("转台后通知到会员result={}" + result);
        } catch (IOException e) {
            log.error("转台后通知到会员异常:{}", e.toString(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> combine(TableCombineDTO tableCombineDTO, Integer tag) {
        // 主桌锁定判断
        String deviceId = tableCombineDTO.getDeviceId();
        String mainTableGuid = tableCombineDTO.getMainTableGuid();
        if (redisService.isTableLockedByOthers(deviceId, mainTableGuid)) {
            throw new BusinessException("主桌已被锁定");
        }
        // 查询包含主桌在内的所有桌台
        List<String> tableGuidList = tableCombineDTO.getTableGuidList();
        tableGuidList.add(mainTableGuid);
        List<TableOrderDO> tableOrderDOS = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                .in(TableOrderDO::getTableGuid, tableGuidList));

        //查询出主单
        List<String> mainOrderGuidList =
                tableOrderDOS.stream()
                        .filter(obj -> ObjectUtil.isNotNull(obj.getMainOrderGuid()))
                        .distinct().map(TableOrderDO::getMainOrderGuid).collect(Collectors.toList());

        // 如果存在不能被并台的桌台，返回给安卓进行数据更新
        List<String> tableCombineNotAllowed = getTableCombineNotAllowed(tableOrderDOS, tableCombineDTO, tag);
        if (!tableCombineNotAllowed.isEmpty()) {
            return tableCombineNotAllowed;
        }
        // 获取并台次数，如果不存在则初始化
        Integer combineTimes = tableCombineDTO.getCombineTimes();
        if (null == combineTimes) {
            combineTimes = redisService.getCombineTimes(tableCombineDTO.getStoreGuid());
            log.info("redis计算并台次数：{}", combineTimes);
            tableCombineDTO.setCombineTimes(combineTimes);
        }
        // 对可以并台的桌台进行处理
        for (TableOrderDO tableOrderDO : tableOrderDOS) {
            Set<Integer> subStatus = tableOrderDO.makeSubStatus();
            subStatus.add(TableStatusEnum.TAKE_UP_COMBINE.getStatus());
            boolean isFree = Objects.equals(tableOrderDO.getStatus(), TableStatusEnum.FREE.getStatus());
            boolean isReserveLocked = Objects.equals(tableOrderDO.getStatus(), TableStatusEnum.RESERVATION.getStatus());
            // 如果被并桌台处于空闲或预定未锁定状态，自动开台
            if (isFree || isReserveLocked) {
                // 设置正确的subStatus
                subStatus.add(TableStatusEnum.TAKE_UP_WITHOUT_ORDER_DISH.getStatus());
                if (isReserveLocked) {
                    subStatus.remove(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
                }
                // 更新tableOrder的各项值
                tableOrderDO.setStatus(TableStatusEnum.TAKE_UP.getStatus());
                tableOrderDO.setOpenStaffGuid(tableCombineDTO.getUserGuid());
                tableOrderDO.setOpenStaffName(tableCombineDTO.getUserName());
                tableOrderDO.setOpenTableTime(DateTimeUtils.now());
                String orderGuid = redisService.singleGuid("hst_order");
                tableOrderDO.setOrderGuid(orderGuid);
                // 调用订单服务创建订单
                TableDO tableDO = tableOrderMapper.selectFullInfo(tableOrderDO.getTableGuid());
                CreateDineInOrderReqDTO createDineInOrderReqDTO = TABLE_MAP_STRUCT.tableCombine2CreateOrder(tableCombineDTO);
                createDineInOrderReqDTO.setDiningTableGuid(tableDO.getGuid());
                createDineInOrderReqDTO.setDiningTableName(tableDO.getTableCode());
                createDineInOrderReqDTO.setGuestCount(tableDO.getSeats());
                createDineInOrderReqDTO.setAreaName(tableDO.getAreaName());
                createDineInOrderReqDTO.setGuid(orderGuid);
                tradeService.openTable(createDineInOrderReqDTO);
            }
            tableOrderDO.setCombineTimes(combineTimes);
            tableOrderDO.setMainOrderGuid(tableCombineDTO.getMainOrderGuid());
            tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(subStatus));
        }
        // 批量更新桌台并台信息
        boolean updateResult = this.updateBatchById(tableOrderDOS, tableOrderDOS.size());
        if (!updateResult) throw new BusinessException("并桌失败");
        // 通知订单服务桌台已经并桌
        TableOrderCombineDTO tableOrderCombineDTO = TABLE_MAP_STRUCT.tableCombine(tableCombineDTO);
        List<TableInfoDTO> tableInfoDTOS = TABLE_MAP_STRUCT.tableDoList2InfoDtoList(tableOrderDOS);
        tableInfoDTOS.removeIf(tableInfoDTO -> Objects.equals(tableInfoDTO.getTableGuid(), mainTableGuid));
        tableOrderCombineDTO.setTableInfoDTOS(tableInfoDTOS);
        tradeService.notifyTradeCombine(tableOrderCombineDTO);
        // 预订并台
        replenishTable(tableGuidList, tableOrderCombineDTO);
        reserveService.combine(tableOrderCombineDTO);

        // 清除子桌第三方活动
        tradeService.batchRevokeThirdActivity(tableCombineDTO);

        // 通知其他桌台拉取相应信息
        if (CollectionUtil.isNotEmpty(mainOrderGuidList)) {
            List<TableOrderDO> tableOrderList = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                    .in(TableOrderDO::getMainOrderGuid, mainOrderGuidList));
            List<String> extraTableGuidList = tableOrderList.stream()
                    .filter(obj -> !tableGuidList.contains(obj.getTableGuid()))
                    .map(TableOrderDO::getTableGuid)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(extraTableGuidList)) {
                tableGuidList.addAll(extraTableGuidList);
                tableCombineDTO.setTableGuidList(tableGuidList);
            }
        }
        String result = bizMsgService.sendMsg(tableCombineDTO, tableGuidList);
        log.info("发送消息服务桌位结账结果 result={}", result);
        defaultRocketMqProducer.sendMessage(new Message(RocketMqConstant.TABLE_STATUS_CHANGE_MQ_TABLE, RocketMqConstant.TABLE_STATUS_CHANGE_MQ_COMBINE,
                JacksonUtils.toJsonByte(new TableStatusChangeMQDTO(UserContextUtils.getEnterpriseGuid(), RocketMqConstant.TABLE_STATUS_CHANGE_MQ_COMBINE, tableCombineDTO))));
        log.info("并台发送消息:{}", tableCombineDTO);

        // pad并台发送消息
        bizMsgService.sendMsg(tableCombineDTO);
        return Collections.emptyList();
    }

    @Override
    public TableCombineVerifyRespDTO verifyCombine(TableCombineDTO tableCombineDTO) {
        TableCombineVerifyRespDTO verifyRespDTO = TableCombineVerifyRespDTO.builder()
                .needTips(false)
                .build();
        // 查询并台的子单是否使用了第三方平台活动或者团购验券
        List<String> orderGuids = tradeService.queryHasThirdActivityOrder(tableCombineDTO);
        if (CollectionUtils.isNotEmpty(orderGuids)) {
            verifyRespDTO.setNeedTips(true);
            verifyRespDTO.setTips(Constant.VERIFY_COMBINE_USE_GROUPON_TIPS);
            verifyRespDTO.setNotice(Constant.VERIFY_COMBINE_USE_GROUPON_NOTICE);
        }
        return verifyRespDTO;
    }

    private List<String> getTableCombineNotAllowed(List<TableOrderDO> tableOrderDOS, TableCombineDTO tableCombineDTO, Integer tag) {
        String deviceId = tableCombineDTO.getDeviceId();
        String mainTableGuid = tableCombineDTO.getMainTableGuid();
        return tableOrderDOS.stream()
                .filter(tableOrderDO -> {
                    if (mainTableGuid.equals(tableOrderDO.getTableGuid())) {
                        return false;
                    }
                    Set<Integer> subStatus = tableOrderDO.makeSubStatus();
                    // 占用并台、预定锁定、业务锁被锁定时不能并桌
                    return CollectionUtils.containsAny(subStatus, TableStatusEnum.MULTI_TABLE_STATUS)
                            || tag == 0 && subStatus.contains(TableStatusEnum.RESERVATION_LOCK.getStatus())
                            || redisService.isTableLockedByOthers(deviceId, tableOrderDO.getTableGuid());
                })
                .map(TableOrderDO::getTableGuid)
                .collect(Collectors.toList());
    }

    private void replenishTable(List<String> tableGuidList, TableOrderCombineDTO tableOrderCombineDTO) {
        List<TableBasicDO> tableBasicDOList = tableBasicMapper.selectList(new LambdaQueryWrapper<TableBasicDO>()
                .in(TableBasicDO::getGuid, tableGuidList));
        Map<String, TableBasicDO> tableBasicDOMap = tableBasicDOList.stream()
                .collect(Collectors.toMap(TableBasicDO::getGuid, Function.identity(), (v1, v2) -> v1));
        tableOrderCombineDTO.getTableInfoDTOS().forEach(tableInfoDTO -> {
            TableBasicDO tableBasicDO = tableBasicDOMap.get(tableInfoDTO.getTableGuid());
            if (!ObjectUtils.isEmpty(tableBasicDO)) {
                tableInfoDTO.setTableName(tableBasicDO.getTableCode());
                tableInfoDTO.setAreaGuid(tableBasicDO.getAreaGuid());
                tableInfoDTO.setAreaName(tableBasicDO.getAreaName());
            }
        });
    }

    /**
     * 并台变参数
     *
     * @param tableCombineDTO
     * @param tag
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TableCombineRespDTO combine_v2(TableCombineDTO tableCombineDTO, Integer tag) {
        TableCombineRespDTO tableCombineRespDTO = new TableCombineRespDTO();
        Map<String, String> freeTableToOrderGuidMap = new HashMap<>();
        // 主桌锁定判断
        String deviceId = tableCombineDTO.getDeviceId();
        String mainTableGuid = tableCombineDTO.getMainTableGuid();
        if (redisService.isTableLockedByOthers(deviceId, mainTableGuid)) {
            throw new BusinessException("主桌已被锁定");
        }
        // 查询包含主桌在内的所有桌台
        List<String> tableGuidList = tableCombineDTO.getTableGuidList();
        tableGuidList.add(mainTableGuid);
        List<TableOrderDO> tableOrderDOS = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                .in(TableOrderDO::getTableGuid, tableGuidList));
        // 如果存在不能被并台的桌台，返回给安卓进行数据更新
        List<String> tableCombineNotAllowed = getTableCombineNotAllowed(tableOrderDOS, tableCombineDTO, tag);
        if (!tableCombineNotAllowed.isEmpty()) {
            tableCombineRespDTO.setFailTableArray(tableCombineNotAllowed);
            return tableCombineRespDTO;
        }
        // 获取并台次数，如果不存在则初始化
        Integer combineTimes = tableCombineDTO.getCombineTimes();
        if (null == combineTimes) {
            combineTimes = redisService.getCombineTimes(tableCombineDTO.getStoreGuid());
            tableCombineDTO.setCombineTimes(combineTimes);
        }
        // 对可以并台的桌台进行处理
        for (TableOrderDO tableOrderDO : tableOrderDOS) {
            Set<Integer> subStatus = tableOrderDO.makeSubStatus();
            subStatus.add(TableStatusEnum.TAKE_UP_COMBINE.getStatus());
            boolean isFree = Objects.equals(tableOrderDO.getStatus(), TableStatusEnum.FREE.getStatus());
            boolean isReserveLocked = Objects.equals(tableOrderDO.getStatus(), TableStatusEnum.RESERVATION.getStatus());
            // 如果被并桌台处于空闲或预定未锁定状态，自动开台
            if (isFree || isReserveLocked) {
                // 设置正确的subStatus
                subStatus.add(TableStatusEnum.TAKE_UP_WITHOUT_ORDER_DISH.getStatus());
                if (isReserveLocked) {
                    subStatus.remove(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
                }
                // 更新tableOrder的各项值
                tableOrderDO.setStatus(TableStatusEnum.TAKE_UP.getStatus());
                tableOrderDO.setOpenStaffGuid(tableCombineDTO.getUserGuid());
                tableOrderDO.setOpenStaffName(tableCombineDTO.getUserName());
                tableOrderDO.setOpenTableTime(DateTimeUtils.now());
                String orderGuid = redisService.singleGuid("hst_order");
                tableOrderDO.setOrderGuid(orderGuid);
                // 调用订单服务创建订单
                TableDO tableDO = tableOrderMapper.selectFullInfo(tableOrderDO.getTableGuid());
                CreateDineInOrderReqDTO createDineInOrderReqDTO = TABLE_MAP_STRUCT.tableCombine2CreateOrder(tableCombineDTO);
                createDineInOrderReqDTO.setDiningTableGuid(tableDO.getGuid());
                createDineInOrderReqDTO.setDiningTableName(tableDO.getTableCode());
                createDineInOrderReqDTO.setGuestCount(tableDO.getSeats());
                createDineInOrderReqDTO.setAreaName(tableDO.getAreaName());
                createDineInOrderReqDTO.setGuid(orderGuid);
                freeTableToOrderGuidMap.put(tableDO.getGuid(), orderGuid);
                tradeService.openTable(createDineInOrderReqDTO);
            }
            tableOrderDO.setCombineTimes(combineTimes);
            tableOrderDO.setMainOrderGuid(tableCombineDTO.getMainOrderGuid());
            tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(subStatus));
        }
        // 批量更新桌台并台信息
        boolean updateResult = this.updateBatchById(tableOrderDOS, tableOrderDOS.size());
        if (!updateResult) throw new BusinessException("并桌失败");
        // 通知订单服务桌台已经并桌
        TableOrderCombineDTO tableOrderCombineDTO = TABLE_MAP_STRUCT.tableCombine(tableCombineDTO);
        List<TableInfoDTO> tableInfoDTOS = TABLE_MAP_STRUCT.tableDoList2InfoDtoList(tableOrderDOS);
        tableInfoDTOS.removeIf(tableInfoDTO -> Objects.equals(tableInfoDTO.getTableGuid(), mainTableGuid));
        tableOrderCombineDTO.setTableInfoDTOS(tableInfoDTOS);
        tradeService.notifyTradeCombine(tableOrderCombineDTO);
        // 通知其他桌台拉取相应信息
        String result = bizMsgService.sendMsg(tableCombineDTO, tableGuidList);
        log.info("发送消息服务桌位结账结果 result={}", result);
        defaultRocketMqProducer.sendMessage(new Message(RocketMqConstant.TABLE_STATUS_CHANGE_MQ_TABLE, RocketMqConstant.TABLE_STATUS_CHANGE_MQ_COMBINE,
                JacksonUtils.toJsonByte(new TableStatusChangeMQDTO(UserContextUtils.getEnterpriseGuid(), RocketMqConstant.TABLE_STATUS_CHANGE_MQ_COMBINE, tableCombineDTO))));
        log.info("并台发送消息:{}", tableCombineDTO);
        tableCombineRespDTO.setSuccessResult(freeTableToOrderGuidMap);
        return tableCombineRespDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean separate(TableOrderCombineDTO tableOrderCombineDTO) {
        List<String> subTableGuidList = tableOrderCombineDTO.getTableInfoDTOS().stream()
                .map(BaseTableDTO::getTableGuid).collect(Collectors.toList());
        List<String> allTableGuidList = new ArrayList<String>() {{
            add(tableOrderCombineDTO.getMainTableGuid());
            addAll(subTableGuidList);
        }};
        if (redisService.isTableLockedByOthers(tableOrderCombineDTO.getDeviceId(), allTableGuidList)) {
            throw new BusinessException("有桌台被锁定中");
        }
        //fixme 加storeguid
        List<TableOrderDO> allTableOrderList = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                .eq(TableOrderDO::getMainOrderGuid, tableOrderCombineDTO.getMainOrderGuid()));
        // 拆子桌，且拆完后剩余桌台数量大于1(即还存在子桌)，只拆指定桌台；否则，拆主桌，不对tableOrderDOS做处理(即全部桌台)
        if (!subTableGuidList.isEmpty() && allTableOrderList.size() - subTableGuidList.size() > 1) {
            allTableOrderList = allTableOrderList.stream()
                    .filter(tableOrderDO -> subTableGuidList.contains(tableOrderDO.getTableGuid()))
                    .collect(Collectors.toList());
        }
        for (TableOrderDO tableOrderDO : allTableOrderList) {
            Set<Integer> status = tableOrderDO.makeSubStatus();
            status.remove(TableStatusEnum.TAKE_UP_COMBINE.getStatus());
            tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(status));
            tableOrderDO.setMainOrderGuid(null);
            tableOrderDO.setCombineTimes(null);
        }
        if (allTableOrderList.isEmpty()) {
            throw new BusinessException("不能拆除所有的桌台");
        }
        tableOrderMapper.removeCombine(allTableOrderList);
        // 订单服务拆单
        tradeService.separate(tableOrderCombineDTO);
        // 预定服务拆台
        reserveService.separate(tableOrderCombineDTO);
        List<String> tableToNotify = allTableOrderList.stream().map(TableOrderDO::getTableGuid).collect(Collectors.toList());
        String result = bizMsgService.sendMsg(tableOrderCombineDTO, tableToNotify);
        log.info("发送消息服务 result={}", result);
        defaultRocketMqProducer.sendMessage(new Message(RocketMqConstant.TABLE_STATUS_CHANGE_MQ_TABLE, RocketMqConstant.TABLE_STATUS_CHANGE_MQ_SEPARATE,
                JacksonUtils.toJsonByte(new TableStatusChangeMQDTO(UserContextUtils.getEnterpriseGuid()
                        , RocketMqConstant.TABLE_STATUS_CHANGE_MQ_SEPARATE, tableOrderCombineDTO))));
        log.info("拆台发送消息:{}", tableOrderCombineDTO);

        // pad拆台发送消息
        bizMsgService.sendPadMsg(tableOrderCombineDTO, tableToNotify);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean close(CancelOrderReqDTO cancelOrderReqDTO) {
        String tableGuid = cancelOrderReqDTO.getTableGuid();
        String orderGuid = cancelOrderReqDTO.getOrderGuid();
        TableOrderDO tableOrderDO = selectOneByTableGuid(tableGuid);
        // 只有占用空台（开台后未点餐）才可以关台
        Set<Integer> subStatus = tableOrderDO.makeSubStatus();
        String closedSubStatus = null;

        if (!subStatus.contains(TableStatusEnum.TAKE_UP_WITHOUT_ORDER_DISH.getStatus())) {
            throw new BusinessException("桌台占用中");
        }
        if (subStatus.contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus())) {
            closedSubStatus = JacksonUtils.writeValueAsString(new HashSet<Integer>() {
                {
                    add(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
                }
            });
        }

        OrderDTO orderDTO = tradeRpcClient.findByOrderGuid(orderGuid);
        if (!ObjectUtils.isEmpty(orderDTO) && BigDecimalUtil.greaterThanZero(orderDTO.getReserveFee())) {
            throw new BusinessException("当前存在预付金，请在一体机先回退");
        }

        // 定义需要通知的桌台列表
        List<String> tableToNotify = new ArrayList<>();
        tableToNotify.add(tableGuid);
        // mainOrderGuid有值代表是并台情况，需要更新并台关系
        if (StringUtils.hasText(tableOrderDO.getMainOrderGuid())) {
            List<TableOrderDO> allTableOrderList = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                    .eq(TableOrderDO::getMainOrderGuid, tableOrderDO.getMainOrderGuid()));
            if (Objects.equals(orderGuid, tableOrderDO.getMainOrderGuid())) {
                updateCombineWhenCloseMainTable(tableGuid, allTableOrderList, tableToNotify);
            } else {
                updateCombineWhenCloseNotMainTable(tableGuid, allTableOrderList, tableToNotify);
            }
        }
        // 关台
        //TODO 关台清除 子状态
        int updateResult = tableOrderMapper.closeTable(tableGuid, orderGuid, closedSubStatus);
        if (updateResult == 0) {
            throw new BusinessException("桌台与订单号不匹配");
        }
        if (tableOrderDO.makeSubStatus().contains(TableStatusEnum.TAKE_UP_ASSOCIATED.getStatus())) {
            closeAssociatedTableDb(cancelOrderReqDTO);
        }

        // 非PAD关台后推送消息给pad，要在删除之前就做推送，不然数据更改后会影响推送

        //若订单已作废且桌台未关闭的情况
        if (orderDTO.getState().equals(StateEnum.CANCEL.getCode())) {
            return true;
        }
        // 提前校验
        boolean unfinished = orderDTO.getState().equals(StateEnum.READY.getCode()) || orderDTO.getState().equals(StateEnum.FAILURE
                .getCode()) || orderDTO.getState().equals(StateEnum.PENDING.getCode());
        if (!unfinished) {
            throw new ParameterException("只有未结账订单可以作废");
        }
        // 关台 并台的订单处理
        boolean returnFlag = closeForCombineHandler(cancelOrderReqDTO, orderDTO);
        if (returnFlag) {
            return true;
        }
        // 订单服务作废订单
        tradeService.closeOrder(cancelOrderReqDTO);
        // 发送消息通知
        sendMessage(cancelOrderReqDTO, tableGuid, orderGuid, tableToNotify);
        return true;
    }

    /**
     * 关台 并台的订单处理
     */
    private boolean closeForCombineHandler(CancelOrderReqDTO cancelOrderReqDTO, OrderDTO orderDTO) {
        if (!Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), cancelOrderReqDTO.getDeviceType()) &&
                Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), orderDTO.getDeviceType())) {
            if (Objects.equals(UpperStateEnum.SUB.getCode(), orderDTO.getUpperState())) {
                // 是并台的子单
                List<OrderDTO> orderDTOList = tradeRpcClient.listOrderByCombineOrderGuid(orderDTO.getMainOrderGuid());
                // 加上主单
                OrderDTO mainOrderDTO = tradeRpcClient.findByOrderGuid(orderDTO.getMainOrderGuid());
                orderDTOList.add(mainOrderDTO);
                orderDTOList.forEach(order -> bizMsgService.sendCloseMsg(order.getStoreGuid(), order.getStoreName(),
                        order.getDiningTableGuid()));
                return true;
            } else if (Objects.equals(UpperStateEnum.MAIN.getCode(), orderDTO.getUpperState())) {
                List<OrderDTO> orderDTOList = tradeRpcClient.listOrderByCombineOrderGuid(orderDTO.getGuid());
                if (!CollectionUtils.isEmpty(orderDTOList)) {
                    // 是并台的主单
                    orderDTOList.add(orderDTO);
                    orderDTOList.forEach(order -> bizMsgService.sendCloseMsg(order.getStoreGuid(), order.getStoreName(),
                            order.getDiningTableGuid()));
                    return true;
                }
            }
            bizMsgService.sendCloseMsg(cancelOrderReqDTO.getStoreGuid(), cancelOrderReqDTO.getStoreName(),
                    cancelOrderReqDTO.getTableGuid());
        }
        return false;
    }

    /**
     * 关 联台的桌台 数据库持久化处理
     */
    private void closeAssociatedTableDb(CancelOrderReqDTO cancelOrderReqDTO) {
        String orderGuid = cancelOrderReqDTO.getOrderGuid();
        // 查询关联桌台
        List<TableOrderDO> tableOrderDOS = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                .eq(TableOrderDO::getOrderGuid, orderGuid));
        List<TableOrderDO> normalTable = tableOrderDOS.stream()
                .filter(e -> !e.makeSubStatus().contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(normalTable)) {
            List<String> normalTableGuids = normalTable.stream()
                    .map(TableOrderDO::getTableGuid)
                    .collect(Collectors.toList());
            tableOrderMapper.update(new TableOrderDO(), new UpdateWrapper<TableOrderDO>().lambda()
                    .set(TableOrderDO::getOrderGuid, null)
                    .set(TableOrderDO::getOpenTableTime, null)
                    .set(TableOrderDO::getOpenStaffGuid, null)
                    .set(TableOrderDO::getOpenStaffName, null)
                    .set(TableOrderDO::getMainOrderGuid, null)
                    .set(TableOrderDO::getCombineTimes, null)
                    .set(TableOrderDO::getAssociatedTimes, null)
                    .set(TableOrderDO::getSubStatus, null)
                    .set(TableOrderDO::getStatus, TableStatusEnum.FREE.getStatus())
                    .in(TableOrderDO::getTableGuid, normalTableGuids));
        }
        List<TableOrderDO> table = tableOrderDOS.stream()
                .filter(e -> e.makeSubStatus().contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(table)) {
            List<String> tableGuids = table.stream()
                    .map(TableOrderDO::getTableGuid)
                    .collect(Collectors.toList());
            Set<Integer> subStatus = Sets.newHashSet(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
            tableOrderMapper.update(new TableOrderDO(), new UpdateWrapper<TableOrderDO>().lambda()
                    .set(TableOrderDO::getOrderGuid, null)
                    .set(TableOrderDO::getOpenTableTime, null)
                    .set(TableOrderDO::getOpenStaffGuid, null)
                    .set(TableOrderDO::getOpenStaffName, null)
                    .set(TableOrderDO::getMainOrderGuid, null)
                    .set(TableOrderDO::getCombineTimes, null)
                    .set(TableOrderDO::getAssociatedTimes, null)
                    .set(TableOrderDO::getSubStatus, JacksonUtils.writeValueAsString(subStatus))
                    .set(TableOrderDO::getStatus, TableStatusEnum.FREE.getStatus())
                    .in(TableOrderDO::getTableGuid, tableGuids));
        }
        List<String> associatedTableGuids = tableOrderDOS.stream()
                .map(TableOrderDO::getTableGuid)
                .distinct()
                .collect(Collectors.toList());
        tableAssociatedService.removeBatch(associatedTableGuids);
    }

    private void sendMessage(CancelOrderReqDTO cancelOrderReqDTO, String tableGuid, String orderGuid, List<String> tableToNotify) {
        String string = bizMsgService.sendMsg(cancelOrderReqDTO, tableToNotify);
        log.info("发送消息服务result={}", string);

        TableStatusChangeDTO tableStatusChangeDTO = new TableStatusChangeDTO();
        tableStatusChangeDTO.setOrderGuid(orderGuid);
        tableStatusChangeDTO.setStoreGuid(cancelOrderReqDTO.getStoreGuid());
        tableStatusChangeDTO.setTableGuid(tableGuid);
        tableStatusChangeDTO.setTableStatusChange(TableStatusChangeEnum.CLOSE_WEIXIN_NOTIFY.getId());
        tableStatusChangeDTO.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        tableStatusChangeDTO.setDeviceType(cancelOrderReqDTO.getDeviceType());
        tableStatusChangeDTO.setUserGuid(cancelOrderReqDTO.getUserGuid());
        tableStatusChangeDTO.setUserName(cancelOrderReqDTO.getUserName());
        defaultRocketMqProducer.sendMessage(new Message(
                RocketMqConstant.TABLE_STATUS_CHANGE_MQ_TOPIC,
                RocketMqConstant.TABLE_STATUS_CHANGE_MQ_TAG,
                JacksonUtils.toJsonByte(tableStatusChangeDTO)));
    }

    @Override
    public boolean couldOpen(OpenTableDTO openTableDTO) {
        if (redisService.isTableLockedByOthers(openTableDTO.getDeviceId(), openTableDTO.getTableGuid())) {
            throw new BusinessException("该桌台已被锁定");
        }
        TableOrderDO tableOrderDO = selectOneByTableGuid(openTableDTO.getTableGuid());
        Integer tableStatus = tableOrderDO.getStatus();
        // 只有空闲和预定锁定状态能开台
        return Objects.equals(tableStatus, TableStatusEnum.FREE.getStatus()) ||
                Objects.equals(tableStatus, TableStatusEnum.RESERVATION_LOCK.getStatus());
    }

    @Override
    public TableWhetherOpenDTO recheckOpen(BaseTableDTO baseTableDTO) {
        TableDO tableDO = tableOrderMapper.selectFullInfo(baseTableDTO.getTableGuid());
        if (tableDO == null) {
            throw new BusinessException("未找到对应桌台");
        }
        Integer tableStatus = tableDO.getStatus();
        TableWhetherOpenDTO tableWhetherOpenDTO = new TableWhetherOpenDTO();
        // 只有空闲和预定锁定状态能开台
        if (Objects.equals(tableStatus, TableStatusEnum.FREE.getStatus()) ||
                Objects.equals(tableStatus, TableStatusEnum.RESERVATION_LOCK.getStatus())) {
            tableWhetherOpenDTO.setNewTableCode(tableDO.getTableCode());
            tableWhetherOpenDTO.setNewTableAreaName(tableDO.getAreaName());
            tableWhetherOpenDTO.setWhetherOpened(true);
            // 开台操作
            TableOrderDO tableOrderDO = TABLE_MAP_STRUCT.recheck2OpenTableDo(baseTableDTO);
            tableOrderDO.setStatus(TableStatusEnum.TAKE_UP.getStatus());
            tableOrderDO.setOpenTableTime(DateTimeUtils.now());
            Set<Integer> subStatus = new HashSet<>();
            // todo 可能涉及到子状态的清除和覆盖 比如预定，并桌情况
            subStatus.add(TableStatusEnum.TAKE_UP_EATING.getStatus());
            tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(subStatus));
            open(baseTableDTO, tableOrderDO, 0);
        }
        return tableWhetherOpenDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String tryOpen(OpenTableDTO openTableDTO) {
        TableOrderDO tableOrderDO = TABLE_MAP_STRUCT.openTableDto2OrderDo(openTableDTO);
        tableOrderDO.setStatus(TableStatusEnum.TAKE_UP.getStatus());
        tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(new HashSet<Integer>() {{
            // todo 可能涉及到子状态的清除和覆盖 比如预定，并桌情况
            add(TableStatusEnum.TAKE_UP_WITHOUT_ORDER_DISH.getStatus());
        }}));
        tableOrderDO.setOpenTableTime(DateTimeUtils.now());
        String orderGuid = redisService.singleGuid("hst_order");
        tableOrderDO.setOrderGuid(orderGuid);
        // 为保证原子性，需判断状态TableStatusEnum.FREE
        int updateResult = tableOrderMapper.update(tableOrderDO, new LambdaQueryWrapper<TableOrderDO>()
                .eq(TableOrderDO::getStatus, TableStatusEnum.FREE.getStatus())
                .eq(TableOrderDO::getTableGuid, tableOrderDO.getTableGuid()));
        if (updateResult == 0) {
            TableOrderDO updateAfterTableOrderDO = selectOneByTableGuid(openTableDTO.getTableGuid());
            String tableOrderGuid = updateAfterTableOrderDO.getOrderGuid();
            if (!isOpenTableBy2c(openTableDTO, tableOrderGuid)) {
                return tableOrderGuid;
            }
        }
        // 通知mqtt告知其他门店设备该桌位已经开台
        String result = bizMsgService.sendMsg(openTableDTO, Collections.singletonList(tableOrderDO.getTableGuid()));
        log.info("发送开台消息结果，result={}", result);
        // 调用订单服务创建订单
        CreateDineInOrderReqDTO createDineInOrderReqDTO =
                TABLE_MAP_STRUCT.openTableDTO2CreateDineInOrderReqDTO(openTableDTO);
        createDineInOrderReqDTO.setGuid(orderGuid);
        createDineInOrderReqDTO.setAreaName(openTableDTO.getAreaName());
        createDineInOrderReqDTO.setDiningTableGuid(openTableDTO.getTableGuid());
        createDineInOrderReqDTO.setDiningTableName(openTableDTO.getTableCode());
        createDineInOrderReqDTO.setGuestCount(openTableDTO.getActualGuestsNo());
        tradeService.openTable(createDineInOrderReqDTO);
        return orderGuid;
    }

    private boolean isOpenTableBy2c(OpenTableDTO openTableDTO, String tableOrderGuid) {
        return (StringUtils.isEmpty(tableOrderGuid)
                && (BaseDeviceTypeEnum.isApplet(openTableDTO.getDeviceType()) || BaseDeviceTypeEnum.WECHAT.getCode() == openTableDTO.getDeviceType()));
    }

    @Override
    public boolean isLocked(String deviceId, String tableGuid) {
        return redisService.isTableLockedByOthers(deviceId, tableGuid);
    }

    @Override
    public boolean tryLock(TableLockDTO tableLockDTO) {
        String tableGuid = tableLockDTO.getTableGuid();
        String deviceId = tableLockDTO.getDeviceId();
        String orderGuid = tableLockDTO.getOrderGuid();
        TableOrderDO tableOrderDO = tableOrderMapper.selectOne(new LambdaQueryWrapper<TableOrderDO>()
                .eq(TableOrderDO::getTableGuid, tableLockDTO.getTableGuid())
                .eq(TableOrderDO::getOrderGuid, tableLockDTO.getOrderGuid()));
        if (null == tableOrderDO) {
            throw new BusinessException("桌台订单不匹配");
        }
        List<String> tableGuidToNotify;
        DelayAutoUnlockBO autoReleaseLockBO = TABLE_MAP_STRUCT.tableLockDto2ReleaseLockBo(tableLockDTO);
        if (!StringUtils.hasText(tableOrderDO.getMainOrderGuid())) {
            redisService.lockSingleTable(tableGuid, deviceId, orderGuid, tableLockDTO.getDeviceType());
            tableGuidToNotify = Collections.singletonList(tableGuid);
            autoReleaseLockBO.setTableInfoBos(Collections.singletonList(new TableInfoBO(tableGuid, deviceId, orderGuid)));
        } else {
            List<TableOrderDO> tableOrderDOS = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                    .eq(TableOrderDO::getMainOrderGuid, tableOrderDO.getMainOrderGuid()));
            List<TableInfoBO> tableInfoBOS = TABLE_MAP_STRUCT.tableDoList2InfoBoList(tableOrderDOS);
            tableInfoBOS.forEach(tableInfoBO -> tableInfoBO.setDeviceId(deviceId));
            redisService.lockMultiTable(tableInfoBOS, tableLockDTO.getDeviceType());
            tableGuidToNotify = tableInfoBOS.stream()
                    .map(TableInfoBO::getTableGuid)
                    .collect(Collectors.toList());
            autoReleaseLockBO.setTableInfoBos(tableInfoBOS);
        }
        // 通知桌台更新锁定状态
        bizMsgService.sendMsg(tableLockDTO, tableGuidToNotify);
        // 添加一个自动解锁trigger，通知安卓拉取最新桌台消息
        if (Objects.equals(12, tableLockDTO.getDeviceType())) {
            release.wxDoRelease(autoReleaseLockBO);
        } else {
            release.doRelease(autoReleaseLockBO);
        }

        return true;
    }

    @Override
    public boolean tryUnlock(TableLockDTO tableLockDTO) {
        String tableGuid = tableLockDTO.getTableGuid();
        TableOrderDO tableOrderDO = selectOneByTableGuid(tableGuid);
        List<String> tableGuidList;
        if (!StringUtils.hasText(tableOrderDO.getMainOrderGuid())) {
            tableGuidList = Collections.singletonList(tableGuid);
        } else {
            List<TableOrderDO> tableOrderDOS = selectAllByMainOrderGuid(tableOrderDO.getMainOrderGuid());
            tableGuidList = tableOrderDOS.stream().map(TableOrderDO::getTableGuid).collect(Collectors.toList());
        }
        if (redisService.isUnlockAllowed(tableGuid, tableLockDTO.getDeviceId(), tableLockDTO.getOrderGuid())) {
            redisService.unlockMultiTable(tableGuidList);
            bizMsgService.sendMsg(tableLockDTO, tableGuidList);
            return true;
        }
        return false;
    }

    @Override
    public boolean statusSync(TableStatusChangeDTO tableStatusChangeDTO) {
        Integer tableStatusChange = tableStatusChangeDTO.getTableStatusChange();
        TableOrderDO tableOrderDO = selectOneByTableGuid(tableStatusChangeDTO.getTableGuid());
        TableStatusChangeEnum tableStatusChangeEnum = TableStatusChangeEnum.getById(tableStatusChange);
        if (Objects.isNull(tableStatusChangeEnum)) {
            log.error("待处理的状态:{}", tableStatusChange);
            return true;
        }
        switch (tableStatusChangeEnum) {
            case ORDER_DISH_CHANGE:
                // 菜品变动，客人用餐中,只会影响subStatus状态，可能由占用空台变为占用用餐中
                return orderDishChangeHandler(tableStatusChangeDTO, tableOrderDO);
            case NUMBER_OF_MEALS_CHANGE:
                // 如果只是修改了就餐人数，只通知客户端拉取相应桌台信息，
                return orderNumberOfMealsChangeHandler(tableStatusChangeDTO, tableOrderDO);
            case ORDER_DISH_REFUND_ALL:
                // 菜品退完，桌台状态变为占用空闲
                return orderDishRefundAllHandler(tableStatusChangeDTO, tableOrderDO);
            case DISABLED:
                // 作废订单
                return orderDisabledHandler(tableStatusChangeDTO, tableOrderDO);
            case CHECKOUT:
                // 订单结账完成释放桌台
                return orderCheckoutHandler(tableStatusChangeDTO, tableOrderDO);
            case CLOSE_WEIXIN_NOTIFY:
                // 关台
                return orderCloseHandler(tableStatusChangeDTO);
            default:
                return true;
        }
    }


    /**
     * 订单菜品变化(加菜，退菜)
     */
    private boolean orderDishChangeHandler(TableStatusChangeDTO tableStatusChangeDTO, TableOrderDO tableOrderDO) {
        Set<Integer> subStatus = tableOrderDO.makeSubStatus();
        subStatus.remove(TableStatusEnum.TAKE_UP_WITHOUT_ORDER_DISH.getStatus());
        subStatus.remove(TableStatusEnum.TO_BE_CLOSE.getStatus());
        subStatus.add(TableStatusEnum.TAKE_UP_EATING.getStatus());
        return updateTableOrderDO(tableStatusChangeDTO, tableOrderDO, subStatus);
    }

    /**
     * 订单修改就餐人数(多单结账并且就餐人数增加)
     */
    private boolean orderGuestChangeHandler(TableStatusChangeDTO tableStatusChangeDTO, TableOrderDO tableOrderDO) {
        Set<Integer> subStatus = tableOrderDO.makeSubStatus();
        if (Boolean.TRUE.equals(tableStatusChangeDTO.getCheckoutSuccessFlag())) {
            // 已结清
            subStatus.add(TableStatusEnum.TO_BE_CLOSE.getStatus());
        } else {
            // 未结清
            subStatus.remove(TableStatusEnum.TO_BE_CLOSE.getStatus());
            subStatus.remove(TableStatusEnum.TAKE_UP_WITHOUT_ORDER_DISH.getStatus());
            subStatus.add(TableStatusEnum.TAKE_UP_EATING.getStatus());
        }
        return updateTableOrderDO(tableStatusChangeDTO, tableOrderDO, subStatus);
    }

    /**
     * 如果只是修改了就餐人数，只通知客户端拉取相应桌台信息，
     */
    private boolean orderNumberOfMealsChangeHandler(TableStatusChangeDTO tableStatusChangeDTO, TableOrderDO tableOrderDO) {
        if (Objects.equals(BooleanEnum.TRUE.getCode(), tableStatusChangeDTO.getEnableManualClear())) {
            return orderGuestChangeHandler(tableStatusChangeDTO, tableOrderDO);
        }
        Set<Integer> subStatus = tableOrderDO.makeSubStatus();
        if (subStatus.contains(TableStatusEnum.TAKE_UP_ASSOCIATED.getStatus())) {
            Set<String> tableGuids = Sets.newHashSet(tableOrderDO.getTableGuid());
            // 联台桌台需要同步所有桌台状态
            List<TableAssociatedDO> tableAssociatedList = tableAssociatedService.listByTableGuid(tableOrderDO.getTableGuid());
            if (CollectionUtils.isNotEmpty(tableAssociatedList)) {
                List<String> tableAssociatedTableGuidList = tableAssociatedList.stream()
                        .map(TableAssociatedDO::getAssociatedTableGuid)
                        .distinct()
                        .collect(Collectors.toList());
                tableGuids.addAll(tableAssociatedTableGuidList);

                List<String> tableTableGuidList = tableAssociatedList.stream()
                        .map(TableAssociatedDO::getTableGuid)
                        .distinct()
                        .collect(Collectors.toList());
                tableGuids.addAll(tableTableGuidList);
            }
            dealUpdateBatchResult(tableStatusChangeDTO, new ArrayList<>(tableGuids));
        } else {
            dealUpdateResult(tableStatusChangeDTO, 1);
        }
        return true;
    }

    /**
     * 菜品退完，桌台状态变为占用空闲
     */
    private boolean orderDishRefundAllHandler(TableStatusChangeDTO tableStatusChangeDTO, TableOrderDO tableOrderDO) {
        Set<Integer> subStatus = tableOrderDO.makeSubStatus();
        subStatus.remove(TableStatusEnum.TAKE_UP_EATING.getStatus());
        subStatus.add(TableStatusEnum.TAKE_UP_WITHOUT_ORDER_DISH.getStatus());
        if (Objects.equals(BooleanEnum.TRUE.getCode(), tableStatusChangeDTO.getEnableManualClear())) {
            subStatus.add(TableStatusEnum.TO_BE_CLOSE.getStatus());
        }
        return updateTableOrderDO(tableStatusChangeDTO, tableOrderDO, subStatus);
    }

    /**
     * 作废订单
     */
    private boolean orderDisabledHandler(TableStatusChangeDTO tableStatusChangeDTO, TableOrderDO tableOrderDO) {
        Set<Integer> subStatus = tableOrderDO.makeSubStatus();
        List<String> removeCacheList = new ArrayList<>();
        removeCacheList.add(tableStatusChangeDTO.getTableGuid());
        // 该作废订单是并桌的订单
        if (subStatus.contains(TableStatusEnum.TAKE_UP_COMBINE.getStatus())) {
            // 并台逻辑处理
            // 该桌是并桌中的主桌
            List<TableOrderDO> tableOrderDOS = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                    .eq(TableOrderDO::getMainOrderGuid, tableOrderDO.getMainOrderGuid()));
            if (Objects.equals(tableOrderDO.getMainOrderGuid(), tableOrderDO.getOrderGuid())) {
                updateCombineWhenCloseMainTable(tableOrderDO.getTableGuid(), tableOrderDOS, removeCacheList);
            } else {
                // 该桌不是并桌中的主桌
                updateCombineWhenCloseNotMainTable(tableOrderDO.getTableGuid(), tableOrderDOS, removeCacheList);
            }
        }
        if (subStatus.contains(TableStatusEnum.TAKE_UP_ASSOCIATED.getStatus())) {
            // 联台逻辑处理
            orderCheckoutHandler(tableStatusChangeDTO, tableOrderDO);
            return true;
        }
        // 这里结账完成可能变为待清台状态（门店配置是否自定清台），清台完成后变为空闲状态
        int updateResult = getUpdateResult(tableStatusChangeDTO);
        if (updateResult != 1) {
            return false;
        }
        // 通知mqtt,安卓客户端可以拉取最新桌台消息
        String result = bizMsgService.sendMsg(tableStatusChangeDTO, removeCacheList);
        log.info("发送消息服务桌位结账结果 result={}", result);
        // 扎帐开台状态
        sendMqForCanOpenTable(tableStatusChangeDTO.getStoreGuid());
        return true;
    }


    /**
     * 订单结账完成释放桌台
     */
    private boolean orderCheckoutHandler(TableStatusChangeDTO tableStatusChangeDTO, TableOrderDO tableOrderDO) {
        // 判断是否是并桌结账
        if (CollectionUtils.containsAny(tableOrderDO.makeSubStatus(), TableStatusEnum.MULTI_TABLE_STATUS)) {
            List<TableOrderDO> tableOrderDOS = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                    .eq(TableOrderDO::getMainOrderGuid, tableStatusChangeDTO.getOrderGuid()));
            if (CollectionUtils.isEmpty(tableOrderDOS)) {
                List<TableOrderDO> tableOrderList = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                        .eq(TableOrderDO::getOrderGuid, tableStatusChangeDTO.getOrderGuid()));
                List<String> mainOrderGuidList = tableOrderList.stream().distinct().map(TableOrderDO::getMainOrderGuid).collect(Collectors.toList());
                tableOrderDOS = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                        .in(TableOrderDO::getMainOrderGuid, mainOrderGuidList));
            }
            if (tableOrderDO.makeSubStatus().contains(TableStatusEnum.TAKE_UP_ASSOCIATED.getStatus())) {
                List<String> associatedTableGuids = tableOrderDOS.stream()
                        .map(TableOrderDO::getTableGuid)
                        .distinct()
                        .collect(Collectors.toList());
                tableAssociatedService.removeBatch(associatedTableGuids);
            }
            orderNotifyCloseAllTable(tableOrderDOS, tableStatusChangeDTO);
            List<String> tableGuidList = tableOrderDOS.stream().map(TableOrderDO::getTableGuid).collect(Collectors.toList());
            redisService.unlockMultiTable(tableGuidList);
            String result = bizMsgService.sendMsg(tableStatusChangeDTO, tableGuidList);
            log.info("通知消息服务 result={},request:{}", result, tableGuidList);
            // 扎帐开台状态
            sendMqForCanOpenTable(tableStatusChangeDTO.getStoreGuid());
            return true;
        } else {
            int updateResult = getUpdateResult(tableStatusChangeDTO);
            dealUpdateResult(tableStatusChangeDTO, updateResult);
            // 扎帐开台状态
            sendMqForCanOpenTable(tableStatusChangeDTO.getStoreGuid());
            if (updateResult == 1) {
                // 删除锁
                redisService.unlockMultiTable(Collections.singletonList(tableStatusChangeDTO.getTableGuid()));
                return true;
            }
            return false;
        }
    }

    /**
     * 关台
     */
    private boolean orderCloseHandler(TableStatusChangeDTO tableStatusChangeDTO) {
        // 扎帐开台状态
        sendMqForCanOpenTable(tableStatusChangeDTO.getStoreGuid());
        return true;
    }

    /**
     * 更新桌台订单状态
     */
    private boolean updateTableOrderDO(TableStatusChangeDTO tableStatusChangeDTO, TableOrderDO tableOrderDO, Set<Integer> subStatus) {
        if (subStatus.contains(TableStatusEnum.TAKE_UP_ASSOCIATED.getStatus())) {
            Set<String> tableGuids = Sets.newHashSet(tableOrderDO.getTableGuid());
            // 联台桌台需要同步所有桌台状态
            List<TableAssociatedDO> tableAssociatedList = tableAssociatedService.listByTableGuid(tableOrderDO.getTableGuid());
            if (CollectionUtils.isNotEmpty(tableAssociatedList)) {
                List<String> tableAssociatedTableGuidList = tableAssociatedList.stream()
                        .map(TableAssociatedDO::getAssociatedTableGuid)
                        .distinct()
                        .collect(Collectors.toList());
                tableGuids.addAll(tableAssociatedTableGuidList);

                List<String> tableTableGuidList = tableAssociatedList.stream()
                        .map(TableAssociatedDO::getTableGuid)
                        .distinct()
                        .collect(Collectors.toList());
                tableGuids.addAll(tableTableGuidList);
            }
            tableOrderMapper.update(new TableOrderDO(), new UpdateWrapper<TableOrderDO>().lambda()
                    .set(TableOrderDO::getSubStatus, JacksonUtils.writeValueAsString(subStatus))
                    .eq(TableOrderDO::getOrderGuid, tableStatusChangeDTO.getOrderGuid())
                    .in(TableOrderDO::getTableGuid, tableGuids));
            dealUpdateBatchResult(tableStatusChangeDTO, new ArrayList<>(tableGuids));
            return true;
        } else {
            int updateResult = tableOrderMapper.update(new TableOrderDO(), new UpdateWrapper<TableOrderDO>().lambda()
                    .set(TableOrderDO::getSubStatus, JacksonUtils.writeValueAsString(subStatus))
                    .eq(TableOrderDO::getOrderGuid, tableStatusChangeDTO.getOrderGuid())
                    .eq(TableOrderDO::getTableGuid, tableStatusChangeDTO.getTableGuid()));
            dealUpdateResult(tableStatusChangeDTO, updateResult);
            return updateResult == 1;
        }
    }

    @Override
    public void compensationStatus(List<CompensationTableDTO> list) {
        tableOrderMapper.compensationStatus(list);
    }

    @Override
    public String queryAreaGuidByTableGuid(String tableGuid) {
        TableDO tableDO = tableOrderMapper.selectFullInfo(tableGuid);
        return Optional.ofNullable(tableDO).map(TableDO::getAreaGuid)
                .orElseThrow(() -> new BusinessException("未找到对应桌台!"));
    }

    @Override
    public String queryOrderGuidByTableGuid(String tableGuid) {
        TableOrderDO tableOrderDO = tableOrderMapper.selectOne(new LambdaQueryWrapper<TableOrderDO>()
                .eq(TableOrderDO::getTableGuid, tableGuid));
        return Optional.ofNullable(tableOrderDO)
                .map(x -> StringUtils.isEmpty(x.getMainOrderGuid()) ? x.getOrderGuid() : x.getMainOrderGuid())
                .orElse(null);
    }

    @Override
    public List<WxStoreTableCombineDTO> tableList(String tableGuid) {
        LambdaQueryWrapper<TableOrderDO> wrapper = new LambdaQueryWrapper<TableOrderDO>().eq(TableOrderDO::getTableGuid, tableGuid);
        TableOrderDO tableOrderDO = getOne(wrapper);
        if (ObjectUtils.isEmpty(tableOrderDO)) {
            return null;
        }
        if (!ObjectUtils.isEmpty(tableOrderDO.getMainOrderGuid())) {
            wrapper = new LambdaQueryWrapper<TableOrderDO>().in(TableOrderDO::getMainOrderGuid);
            List<TableOrderDO> list = list(wrapper);
            return WX_STORE_TABLE_COMBINE_MAP_STRUCT.getAllWxStoreTableCombine(list);
        }
        return Collections.singletonList(WX_STORE_TABLE_COMBINE_MAP_STRUCT.getWxStoreTableCombine(tableOrderDO));
    }

    @Override
    public TableDTO getFullTableByGuid(String tableGuid) {
        TableDO tableDO = tableOrderMapper.selectFullInfo(tableGuid);
        TableDTO tableDTO = TableDTO.builder().code(tableDO.getTableCode()).name(tableDO.getTableCode()).tableGuid(tableDO.getGuid())
                .areaGuid(tableDO.getAreaGuid()).areaName(tableDO.getAreaName())
                .storeName(tableDO.getStoreName()).storeGuid(tableDO.getStoreGuid())
                .orderGuid(tableDO.getOrderGuid()).mainOrderGuid(tableDO.getMainOrderGuid())
                .build();
        if (!StringUtils.isEmpty(tableDO.getOrderGuid())) {
            OrderDTO orderDTO = tradeRpcClient.findByOrderGuid(tableDO.getOrderGuid());
            if (ObjectUtils.isEmpty(orderDTO)) {
                log.error("未查询到订单 orderGuid={}", tableDO.getOrderGuid());
                throw new BusinessException("未查询到订单");
            }
            tableDTO.setGuestCount(orderDTO.getGuestCount());
        }
        return tableDTO;
    }

    private TableOrderDO selectOneByTableGuid(String tableGuid) {
        TableOrderDO tableOrderDO = tableOrderMapper.selectOne(new LambdaQueryWrapper<TableOrderDO>()
                .eq(TableOrderDO::getTableGuid, tableGuid));
        return Optional.ofNullable(tableOrderDO).orElseThrow(() -> new BusinessException("未找到对应桌台!"));
    }

    private List<TableOrderDO> selectAllByMainOrderGuid(String mainOrderGuid) {
        List<TableOrderDO> tableOrderDOS = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                .eq(TableOrderDO::getMainOrderGuid, mainOrderGuid));
        if (CollectionUtils.isEmpty(tableOrderDOS)) {
            throw new BusinessException("未找到对应桌台!");
        }
        return tableOrderDOS;
    }

    private void open(BaseDTO baseDTO, TableOrderDO tableOrderDO, Integer tag) {
        // 为保证原子性，需判断状态TableStatusEnum.FREE
        int updateResult = tableOrderMapper.update(tableOrderDO, new LambdaQueryWrapper<TableOrderDO>()
                .eq(TableOrderDO::getTableGuid, tableOrderDO.getTableGuid())
                .nested((e) -> {
                            if (tag == 0) {
                                e.eq(TableOrderDO::getStatus, TableStatusEnum.FREE.getStatus());
                            } else {
                                e.eq(TableOrderDO::getStatus, TableStatusEnum.FREE.getStatus())
                                        .or().eq(TableOrderDO::getStatus, TableStatusEnum.RESERVATION.getStatus());
                            }
                        }
                )
        );
        if (updateResult == 0) {
            throw new BusinessException("开台失败，桌台已占用");
        }
        // 通知mqtt告知其他门店设备该桌位已经开台
        String result = bizMsgService.sendMsg(baseDTO, Collections.singletonList(tableOrderDO.getTableGuid()));
        log.info("发送开台消息结果，result={}", result);
    }

    /**
     * 构建需要更新的联台桌台列表
     */
    private List<TableOrderDO> buildAssociatedTableOrderList(String orderGuid, Integer associatedTimes, List<TableOrderDTO> tableOrderDTOList) {
        LocalDateTime now = DateTimeUtils.now();
        return tableOrderDTOList.stream().map(e -> {
            TableOrderDO tableOrderDO = new TableOrderDO();
            tableOrderDO.setStatus(TableStatusEnum.TAKE_UP.getStatus());
            Set<Integer> subStatus = Sets.newHashSet();
            subStatus.add(TableStatusEnum.TAKE_UP_WITHOUT_ORDER_DISH.getStatus());
            // 可能涉及到子状态的清除和覆盖 比如预定，并桌情况
            subStatus.add(TableStatusEnum.TAKE_UP_ASSOCIATED.getStatus());
            if (Objects.nonNull(e.getSubStatus())
                    && e.getSubStatus().contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus())) {
                subStatus.add(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
            }
            tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(subStatus));
            tableOrderDO.setTableGuid(e.getTableGuid());
            tableOrderDO.setOpenTableTime(now);
            tableOrderDO.setMainOrderGuid(orderGuid);
            tableOrderDO.setOrderGuid(orderGuid);
            tableOrderDO.setAssociatedTimes(associatedTimes);
            return tableOrderDO;
        }).collect(Collectors.toList());
    }

    private void associatedOpen(OpenAssociatedTableDTO openAssociatedTableDTO, List<TableOrderDO> tableOrderList) {
        // 为保证原子性，需判断状态TableStatusEnum.FREE
        for (TableOrderDO tableOrderDO : tableOrderList) {
            updateBatchStatus(tableOrderDO);
        }
        // 保存关联桌台信息
        List<String> tableGuids = openAssociatedTableDTO.getTableGuids();
        String mainTableGuid = openAssociatedTableDTO.getMainTableGuid();
        tableAssociatedService.saveBatch(mainTableGuid, tableGuids);
    }

    private void updateBatchStatus(TableOrderDO tableOrderDO) {
        int updateResult = tableOrderMapper.update(tableOrderDO, new LambdaQueryWrapper<TableOrderDO>()
                .eq(TableOrderDO::getTableGuid, tableOrderDO.getTableGuid())
                .eq(TableOrderDO::getStatus, TableStatusEnum.FREE.getStatus())
        );
        if (updateResult == 0) {
            throw new BusinessException(Constant.TABLE_ASSOCIATED_EXIST);
        }
    }

    private List<String> updateCombineWhenCloseMainTable(String tableGuid, List<TableOrderDO> tableOrderDOS, List<String> removeCacheTableGuid) {
        List<TableOrderDO> orderDOS = tableOrderDOS.stream()
                .filter(tableOrderDO -> !Objects.equals(tableOrderDO.getTableGuid(), tableGuid))
                .peek(tableOrderDO -> {
                    Set<Integer> status = tableOrderDO.makeSubStatus();
                    status.remove(TableStatusEnum.TAKE_UP_COMBINE.getStatus());
                    status.remove(TableStatusEnum.TAKE_UP_ASSOCIATED.getStatus());
                    tableOrderDO.setMainOrderGuid(null);
                    tableOrderDO.setCombineTimes(null);
                    tableOrderDO.setAssociatedTimes(null);
                    tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(status));
                    removeCacheTableGuid.add(tableOrderDO.getTableGuid());
                })
                .collect(Collectors.toList());
        tableOrderMapper.removeCombine(orderDOS);
        return removeCacheTableGuid;
    }

    private List<String> updateCombineWhenCloseNotMainTable(String tableGuid, List<TableOrderDO> tableOrderDOS, List<String> removeCacheTableGuid) {
        if (tableOrderDOS.size() == 2) {
            for (TableOrderDO tableOrderDO : tableOrderDOS) {
                if (!Objects.equals(tableOrderDO.getTableGuid(), tableGuid)) {
                    // 将主桌的并桌状态取消
                    Set<Integer> status = tableOrderDO.makeSubStatus();
                    status.remove(TableStatusEnum.TAKE_UP_COMBINE.getStatus());
                    status.remove(TableStatusEnum.TAKE_UP_ASSOCIATED.getStatus());
                    tableOrderDO.setSubStatus(JacksonUtils.writeValueAsString(status));
                    tableOrderMapper.update(tableOrderDO, new UpdateWrapper<TableOrderDO>().lambda()
                            .set(TableOrderDO::getCombineTimes, null)
                            .set(TableOrderDO::getAssociatedTimes, null)
                            .set(TableOrderDO::getMainOrderGuid, null)
                            .eq(TableOrderDO::getId, tableOrderDO.getId()));
                    removeCacheTableGuid.add(tableOrderDO.getTableGuid());
                }
            }
        }
        return removeCacheTableGuid;
    }

    private void turnCopyTable(TableOrderDO origin, TableOrderDO newTable) {
        newTable.setMainOrderGuid(origin.getMainOrderGuid());
        newTable.setOrderGuid(origin.getOrderGuid());
        newTable.setCombineTimes(origin.getCombineTimes());
        newTable.setAssociatedTimes(origin.getAssociatedTimes());
        newTable.setOpenTableTime(origin.getOpenTableTime());
        newTable.setOpenStaffGuid(origin.getOpenStaffGuid());
        newTable.setOpenStaffName(origin.getOpenStaffName());
        newTable.setStatus(origin.getStatus());

        Set<Integer> originSubStatus = origin.makeSubStatus();
        Set<Integer> originCopy = new HashSet<>(originSubStatus);
        Set<Integer> targetSubStatus = newTable.makeSubStatus();
        Set<Integer> targetCopy = new HashSet<>(targetSubStatus);

        originSubStatus.remove(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
        if (targetCopy.contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus())) {
            originSubStatus.add(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
        }
        newTable.setSubStatus(CollectionUtils.isEmpty(originSubStatus) ? null : JacksonUtils.writeValueAsString(originSubStatus));

        if (originCopy.contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus())) {
            origin.setSubStatus(JacksonUtils.writeValueAsString(new HashSet<Integer>() {{
                add(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
            }}));
        } else {
            origin.setSubStatus(null);
        }
        origin.setStatus(TableStatusEnum.FREE.getStatus());

        origin.setOpenStaffName(null);
        origin.setOpenStaffGuid(null);
        origin.setOpenTableTime(null);
        origin.setOrderGuid(null);
        origin.setMainOrderGuid(null);
        origin.setCombineTimes(null);
        origin.setAssociatedTimes(null);
    }

    private void orderNotifyCloseAllTable(List<TableOrderDO> tableOrderDOS, TableStatusChangeDTO tableStatusChangeDTO) {
//        List<TableOrderDO> tableOrderDOS = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
//                .eq(TableOrderDO::getMainOrderGuid, tableStatusChangeDTO.getOrderGuid()));
        List<TableOrderDO> normalTable = tableOrderDOS.stream()
                .filter(e -> !e.makeSubStatus().contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus()))
                .collect(Collectors.toList());
        if (!normalTable.isEmpty()) {
            List<String> normalTableGuids = normalTable.stream()
                    .map(TableOrderDO::getTableGuid)
                    .collect(Collectors.toList());
            tableOrderMapper.update(new TableOrderDO(), new UpdateWrapper<TableOrderDO>().lambda()
                    .set(TableOrderDO::getOrderGuid, null)
                    .set(TableOrderDO::getOpenTableTime, null)
                    .set(TableOrderDO::getOpenStaffGuid, null)
                    .set(TableOrderDO::getOpenStaffName, null)
                    .set(TableOrderDO::getMainOrderGuid, null)
                    .set(TableOrderDO::getCombineTimes, null)
                    .set(TableOrderDO::getAssociatedTimes, null)
                    .set(TableOrderDO::getSubStatus, null)
                    .set(TableOrderDO::getStatus, TableStatusEnum.FREE.getStatus())
                    .in(TableOrderDO::getTableGuid, normalTableGuids));

            // 非PAD关台后推送消息给pad
            batchPushCloseMsg(tableStatusChangeDTO, normalTable);
        }

        List<TableOrderDO> table = tableOrderDOS.stream()
                .filter(e -> e.makeSubStatus().contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus()))
                .collect(Collectors.toList());
        if (!table.isEmpty()) {
            List<String> tableGuids = table.stream()
                    .map(TableOrderDO::getTableGuid)
                    .collect(Collectors.toList());
            tableOrderMapper.update(new TableOrderDO(), new UpdateWrapper<TableOrderDO>().lambda()
                    .set(TableOrderDO::getOrderGuid, null)
                    .set(TableOrderDO::getOpenTableTime, null)
                    .set(TableOrderDO::getOpenStaffGuid, null)
                    .set(TableOrderDO::getOpenStaffName, null)
                    .set(TableOrderDO::getMainOrderGuid, null)
                    .set(TableOrderDO::getCombineTimes, null)
                    .set(TableOrderDO::getAssociatedTimes, null)
                    .set(TableOrderDO::getSubStatus, JacksonUtils.writeValueAsString(
                            new HashSet<Integer>() {{
                                add(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
                            }}
                    ))
                    .set(TableOrderDO::getStatus, TableStatusEnum.FREE.getStatus())
                    .in(TableOrderDO::getTableGuid, tableGuids));

            // 非PAD关台后推送消息给pad
            batchPushCloseMsg(tableStatusChangeDTO, table);
        }
    }

    /**
     * 非PAD关台后推送消息给pad
     * 批量操作
     *
     * @param tableStatusChangeDTO 设备信息
     * @param table                桌台信息列表
     */
    private void batchPushCloseMsg(TableStatusChangeDTO tableStatusChangeDTO, List<TableOrderDO> table) {
        table.forEach(t -> {
            OrderDTO orderDTO = tradeRpcClient.findByOrderGuid(t.getOrderGuid());
            if (!Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), tableStatusChangeDTO.getDeviceType()) &&
                    Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), orderDTO.getDeviceType())) {
                bizMsgService.sendCloseMsg(tableStatusChangeDTO.getStoreGuid(), tableStatusChangeDTO.getStoreName(),
                        t.getTableGuid());
            }
        });
    }

    private int getUpdateResult(TableStatusChangeDTO tableStatusChangeDTO) {
        int updateResult;
        TableOrderDO db = tableOrderMapper.selectOne(new LambdaQueryWrapper<TableOrderDO>()
                .eq(TableOrderDO::getOrderGuid, tableStatusChangeDTO.getOrderGuid())
                .eq(TableOrderDO::getTableGuid, tableStatusChangeDTO.getTableGuid())
        );
        if (db == null) {
            //这个时候  可能在查询桌台的时候已经被重置了  ，app层有该业务
            //{@link com.holderzone.holder.saas.aggregation.app.service.impl.TableServiceImpl.queryTable}  这个方法如果查询到 会调用重置接口
            return 1;
        }
        Set<Integer> subStatus = null;
        if (db.makeSubStatus().contains(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus())) {
            subStatus = new HashSet<Integer>() {{
                add(TableStatusEnum.RESERVATION_WITHOUT_LOCK.getStatus());
            }};
        }
        updateResult = tableOrderMapper.update(new TableOrderDO(), new UpdateWrapper<TableOrderDO>().lambda()
                .set(TableOrderDO::getOrderGuid, null)
                .set(TableOrderDO::getOpenTableTime, null)
                .set(TableOrderDO::getOpenStaffGuid, null)
                .set(TableOrderDO::getOpenStaffName, null)
                .set(TableOrderDO::getMainOrderGuid, null)
                .set(TableOrderDO::getCombineTimes, null)
                .set(TableOrderDO::getAssociatedTimes, null)
                .set(TableOrderDO::getSubStatus, subStatus == null ? null : JacksonUtils.writeValueAsString(subStatus))
                .set(TableOrderDO::getStatus, TableStatusEnum.FREE.getStatus())
                .eq(TableOrderDO::getOrderGuid, tableStatusChangeDTO.getOrderGuid())
                .eq(TableOrderDO::getTableGuid, tableStatusChangeDTO.getTableGuid()));

        // 非PAD关台后推送消息给pad
        OrderDTO orderDTO = tradeRpcClient.findByOrderGuid(tableStatusChangeDTO.getOrderGuid());
        if (!Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), tableStatusChangeDTO.getDeviceType()) &&
                Objects.equals(BaseDeviceTypeEnum.CLOUD_PANEL.getCode(), orderDTO.getDeviceType())) {
            bizMsgService.sendCloseMsg(tableStatusChangeDTO.getStoreGuid(), tableStatusChangeDTO.getStoreName(),
                    tableStatusChangeDTO.getTableGuid());
        }
        return updateResult;
    }

    private void dealUpdateResult(TableStatusChangeDTO tableStatusChangeDTO, int updateResult) {
        if (updateResult == 1) {
            // 通知mqtt,安卓客户端可以拉取最新桌台消息
            List<String> tableToNotify = Collections.singletonList(tableStatusChangeDTO.getTableGuid());
            String result = bizMsgService.sendMsg(tableStatusChangeDTO, tableToNotify);
            log.info("发送消息服务桌位变化结果 result={}", result);
        }
    }

    private void dealUpdateBatchResult(TableStatusChangeDTO tableStatusChangeDTO, List<String> tableAssociatedTableGuidList) {
        // 通知mqtt,安卓客户端可以拉取最新桌台消息
        List<String> tableToNotify = Lists.newArrayList(tableStatusChangeDTO.getTableGuid());
        tableToNotify.addAll(tableAssociatedTableGuidList);
        tableToNotify = tableToNotify.stream().distinct().collect(Collectors.toList());
        String result = bizMsgService.sendMsg(tableStatusChangeDTO, tableToNotify);
        log.info("发送消息服务桌位变化结果 result={}", result);
    }


    /**
     * @param entityList
     * @return
     * @See {@link com.baomidou.mybatisplus.extension.service.impl.ServiceImpl#updateBatchById(java.util.Collection, int)}
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateBatchByTableOrderGuid(Collection<TableOrderDO> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return true;
        }
        int i = 0;
        String sqlStatement = sqlStatement(SqlMethod.UPDATE);
        try (SqlSession batchSqlSession = sqlSessionBatch()) {
            for (TableOrderDO anEntityList : entityList) {
                MapperMethod.ParamMap param = new MapperMethod.ParamMap<>();
                param.put(Constants.ENTITY, new TableOrderDO());
                param.put(Constants.WRAPPER, new UpdateWrapper<TableOrderDO>().lambda()
                        .set(TableOrderDO::getMainOrderGuid, anEntityList.getMainOrderGuid())
                        .set(TableOrderDO::getStatus, anEntityList.getStatus())
                        .set(TableOrderDO::getOrderGuid, anEntityList.getOrderGuid())
                        .set(TableOrderDO::getSubStatus, anEntityList.getSubStatus())
                        .set(TableOrderDO::getCombineTimes, anEntityList.getCombineTimes())
                        .set(TableOrderDO::getOpenTableTime, anEntityList.getOpenTableTime())
                        .eq(TableOrderDO::getTableGuid, anEntityList.getTableGuid()));
                batchSqlSession.update(sqlStatement, param);
                if (i >= 1 && i % 1000 == 0) {
                    batchSqlSession.flushStatements();
                }
                i++;
            }
            batchSqlSession.flushStatements();
        }
        return true;
    }

    @Override
    public List<WxStoreTableCombineDTO> tableCombineList(String orderGuid, String tableGuid) {
		/*if(StringUtils.isEmpty(orderGuid)){
			LambdaQueryWrapper<TableOrderDO> wrapper = new LambdaQueryWrapper<TableOrderDO>().eq(TableOrderDO::getTableGuid, tableGuid);
			TableOrderDO tableOrderDO = getOne(wrapper);
			if (ObjectUtils.isEmpty(tableOrderDO)) {
				return null;
			}
			if (!ObjectUtils.isEmpty(tableOrderDO.getMainOrderGuid())) {
				orderGuid = tableOrderDO.getMainOrderGuid();
			}
		}*/
        return tableOrderMapper.tableCombineList(orderGuid, tableGuid);
    }

    @Override
    public List<TableBasicDTO> queryCombineListByMainOrder(SingleDataDTO singleDataDTO) {
        String mainOrderGuid = singleDataDTO.getData();
        if (!StringUtils.hasText(mainOrderGuid)) {
            log.warn("参数主单guid为空");
            return Lists.newArrayList();
        }
        List<TableOrderDO> tableOrderDOList = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                .eq(TableOrderDO::getMainOrderGuid, mainOrderGuid));
        if (org.springframework.util.CollectionUtils.isEmpty(tableOrderDOList)) {
            log.warn("主单关系查询为空，mainOrderGuid={}", mainOrderGuid);
            return Lists.newArrayList();
        }
        List<String> tableGuidList = tableOrderDOList.stream()
                .map(TableOrderDO::getTableGuid)
                .distinct()
                .collect(Collectors.toList());
        List<TableBasicDO> tableBasicDOList = tableBasicMapper.selectList(new LambdaQueryWrapper<TableBasicDO>()
                .in(TableBasicDO::getGuid, tableGuidList));
        return TABLE_MAP_STRUCT.tableBasicDos2BasicDto(tableBasicDOList);
    }

    @Override
    public void sendTableChangeMsg(SingleDataDTO singleDataDTO) {
        String tableGuid = singleDataDTO.getData();
        List<String> tableGuids = singleDataDTO.getDatas();
        if (StringUtils.isEmpty(tableGuid) && CollectionUtils.isEmpty(tableGuids)) {
            return;
        }
        // merge
        List<String> tableToNotify = Lists.newArrayList();
        if (!StringUtils.isEmpty(tableGuid)) {
            tableToNotify.add(tableGuid);
        }
        if (CollectionUtils.isNotEmpty(tableGuids)) {
            tableToNotify.addAll(tableGuids);
        }
        tableToNotify = tableToNotify.stream().distinct().collect(Collectors.toList());
        // 查询桌台状态
        List<TableOrderDO> tableOrderDOList = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                .in(TableOrderDO::getTableGuid, tableToNotify));
        if (CollectionUtils.isEmpty(tableOrderDOList)) {
            return;
        }
        List<TableOrderDO> associatedTableOrderList = tableOrderDOList.stream()
                .filter(e -> e.makeSubStatus().contains(TableStatusEnum.TAKE_UP_ASSOCIATED.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(associatedTableOrderList)) {
            // 如果是联台单 则查询联台桌台
            List<String> associatedTableGuidList = associatedTableOrderList.stream()
                    .map(TableOrderDO::getTableGuid)
                    .distinct()
                    .collect(Collectors.toList());
            for (String associatedTableGuid : associatedTableGuidList) {
                List<TableAssociatedDO> tableAssociatedList = tableAssociatedService.listByTableGuid(associatedTableGuid);
                if (CollectionUtils.isNotEmpty(tableAssociatedList)) {
                    List<String> tableAssociatedGuidList = tableAssociatedList.stream()
                            .map(TableAssociatedDO::getAssociatedTableGuid)
                            .collect(Collectors.toList());
                    tableToNotify.addAll(tableAssociatedGuidList);
                    tableToNotify = tableToNotify.stream().distinct().collect(Collectors.toList());
                }
            }
        }
        bizMsgService.sendMsg(singleDataDTO, tableToNotify);
    }

    @Override
    public List<TableBasicDTO> queryTableByOrderGuid(SingleDataDTO singleDataDTO) {
        String orderGuid = singleDataDTO.getData();
        if (!StringUtils.hasText(orderGuid)) {
            log.warn("参数订单guid为空");
            return Lists.newArrayList();
        }
        OrderDTO orderDTO = tradeRpcClient.findByOrderGuid(orderGuid);
        List<TableOrderDO> tableOrderDOList;
        if (Objects.equals(UpperStateEnum.GENERAL.getCode(), orderDTO.getUpperState())) {
            tableOrderDOList = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                    .eq(TableOrderDO::getOrderGuid, orderDTO.getGuid()));
        } else if (Objects.equals(UpperStateEnum.MAIN.getCode(), orderDTO.getUpperState())) {
            tableOrderDOList = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                    .eq(TableOrderDO::getMainOrderGuid, orderDTO.getGuid()));
        } else {
            tableOrderDOList = tableOrderMapper.selectList(new LambdaQueryWrapper<TableOrderDO>()
                    .eq(TableOrderDO::getMainOrderGuid, orderDTO.getMainOrderGuid()));
        }
        if (org.springframework.util.CollectionUtils.isEmpty(tableOrderDOList)) {
            log.warn("[桌台查询为空],orderGuid={},upperState={}", orderGuid, orderDTO.getUpperState());
            return Lists.newArrayList();
        }
        List<String> tableGuidList = tableOrderDOList.stream()
                .map(TableOrderDO::getTableGuid)
                .distinct()
                .collect(Collectors.toList());
        List<TableBasicDO> tableBasicDOList = tableBasicMapper.selectList(new LambdaQueryWrapper<TableBasicDO>()
                .in(TableBasicDO::getGuid, tableGuidList));
        return TABLE_MAP_STRUCT.tableBasicDos2BasicDto(tableBasicDOList);
    }

    /**
     * 发送扎帐功能可开台状态发送mq信息
     */
    private void sendMqForCanOpenTable(String storeGuid) {
        try {
            if (isCanOpenTable(storeGuid)) {
                log.info("扎帐功能可开台状态发送mq信息内容：{}", JacksonUtils.writeValueAsString(storeGuid));
                // 不存在昨日桌台,发送mq信息给一体机
                bindUpAccountsService.sendMqForCanOpenTable(storeGuid);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("扎帐功能可开台状态发送mq信息内容失败,e:{}", e.getMessage());
        }
    }


    /**
     * 校验桌台是否可以开台 (扎帐)
     */
    private boolean isCanOpenTable(String storeGuid) {
        boolean isBindUpAccountStatus = bindUpAccountsService.checkBindUpAccountStatus(storeGuid);
        if (!isBindUpAccountStatus) {
            return false;
        }
        // 昨日已扎帐，需要判断扎帐之后有无新开桌台
        TableBasicQueryDTO tableQueryDTO = new TableBasicQueryDTO();
        tableQueryDTO.setStoreGuid(storeGuid);
        List<TableOrderDTO> tableOrderList = listTable(tableQueryDTO);
        if (CollectionUtils.isEmpty(tableOrderList)) {
            return false;
        }
        // 判断当前桌台是否存在占用情况  拿到桌台判断是否是
        List<TableOrderDTO> unableOpenTableList = tableOrderList.stream()
                .filter(tableOrderDTO -> tableOrderDTO.getStatus() == 1)
                .collect(Collectors.toList());
        log.info("门店未关台列表：{}", JacksonUtils.writeValueAsString(unableOpenTableList));
        if (CollectionUtils.isNotEmpty(unableOpenTableList)) {
            LocalDate localDate = bindUpAccountsService.currentTimeDay(storeGuid, null);
            for (TableOrderDTO table : unableOpenTableList) {
                // 桌台开台时间
                LocalDateTime openTableTime = table.getOpenTableTime();
                LocalDate tableOpenTime = bindUpAccountsService.currentTimeDay(storeGuid, openTableTime);
                // 存在有昨日未关台桌台
                if (tableOpenTime.compareTo(localDate) < 0) {
                    return false;
                }
            }
        }
        return true;
    }
}
