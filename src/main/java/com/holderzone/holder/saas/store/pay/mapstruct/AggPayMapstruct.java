package com.holderzone.holder.saas.store.pay.mapstruct;

import com.holderzone.holder.saas.store.pay.entity.AggPayAttachDataVO;
import com.holderzone.saas.store.dto.pay.AggPayCallbackDTO;
import com.holderzone.holder.saas.store.pay.entity.domain.PayRecordDO;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.UserInfoDTO;
import com.holderzone.saas.store.dto.pay.*;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className AggPaymentMapStruct
 * @date 2019/03/14 15:16
 * @description
 * @program holder-saas-store-trading-center
 */
@Component
@Mapper(componentModel = "spring")
public interface AggPayMapstruct {

    @Mappings({
            @Mapping(target = "signature", ignore = true)
    })
    AggPayPollingDTO createPollingDto(AggPayPreTradingReqDTO payPreTradingReqDTO);

    @Mappings({
            @Mapping(target = "signature", ignore = true)
    })
    AggRefundPollingDTO createRefundPollingDto(AggRefundReqDTO aggRefundReqDTO);

    BaseInfo createBaseInfo(BaseDTO baseDTO);

    UserInfoDTO createUserInfo(BaseDTO baseInfo);

    @Mappings({
            @Mapping(target = "orderGuid", source = "orderGUID"),
            @Mapping(target = "payGuid", source = "payGUID"),
    })
    PayRecordDO createPayRecord(AggPayPollingRespDTO pollingRespDTO);

    @Mappings({
            @Mapping(target = "orderGUID", source = "orderGuid"),
            @Mapping(target = "payGUID", source = "payGuid"),
    })
    AggPayRecordDTO createPayRecordResp(PayRecordDO payRecordDO);

    List<AggPayRecordDTO> createPayRecordResp(List<PayRecordDO> payRecordDO);

//    @Mappings({
//            @Mapping(target = "timePaid", source = "paidTime", qualifiedByName = "paidTime")
//    })
    AggPayPollingRespDTO makecallBackDto(AggPayCallbackDTO aggPayCallbackDTO);

    AggPayAttachDataVO makeAttachVo(BaseDTO baseDTO);

//    @Named("paidTime")
//    default String paidTimeToTimePaid(String paidTime) {
//        if (paidTime == null) {
//            return null;
//        }
//        try {
//            Date parse = new SimpleDateFormat("yyyyMMddHHmmss").parse(paidTime);
//            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(parse);
//        } catch (ParseException e) {
//            e.printStackTrace();
//            return null;
//        }
//    }
}
