package com.holderzone.holder.saas.store.table.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.holderzone.holder.saas.store.table.domain.AreaDO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-26
 */
@Repository
public interface AreaMapper extends BaseMapper<AreaDO> {

    /**
     * select IFNULL(max(sort),0) from hst_area where store_guid = #{storeGuid} and deleted = 0
     * 回去最大的排序值
     * @param storeGuid  店铺GUID
     * @return  返回最大的sort
     */
    int maxSort(@Param("storeGuid") String storeGuid);
}
