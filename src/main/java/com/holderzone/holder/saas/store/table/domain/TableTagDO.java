package com.holderzone.holder.saas.store.table.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/04 18:15
 */
@AllArgsConstructor
@NoArgsConstructor
@TableName("hst_table_tag")
@Data
public class TableTagDO {

    /**
     * 区域guid
     */
    private String guid;

    /**
     * 排序
     */
    private Integer sort;


    /**
     * 是否已删除
     * 0=未删除
     * 1=已删除
     */
    private Integer deleted;


    /**
     * 商店guid
     */
    private String storeGuid;

    private String tagName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

}