package com.holderzone.holder.saas.store.message.mapper;


import com.holderzone.holder.saas.store.message.domain.MessageDO;
import com.holderzone.saas.store.dto.message.MsgInfoRespDTO;
import com.holderzone.saas.store.dto.message.MsgQuery;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MessageMapper
 * @date 2018/09/04 16:53
 * @description //TODO
 * @program holder-saas-bussiness-message
 */
@Repository
public interface MessageMapper {

    void insert(MessageDO messageDO);

    void insertAll(List<MessageDO> messageDOS);

    int queryCount(MsgQuery msgQuery);

    List<MsgInfoRespDTO> queryAll(MsgQuery msgQuery);

    MessageDO queryDetail(String messageGuid);

    void markMsgHasRead(@Param("messageGuid") String messageGuid);

    int countUnReadMsg(@Param("storeGuid") String storeGuid,
                       @Param("begin") LocalDateTime begin, @Param("end") LocalDateTime end);

    @Deprecated
    int countUnReadPrintErrorMsg(String storeGuid);

    void readAll(@Param("storeGuid") String storeGuid,
                 @Param("messageType") Integer messageType);
}
