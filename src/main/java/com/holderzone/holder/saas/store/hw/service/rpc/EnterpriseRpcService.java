package com.holderzone.holder.saas.store.hw.service.rpc;

import com.holderzone.resource.common.dto.enterprise.OrganizationDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EnterpriseClient
 * @date 2019/03/14 11:33
 * @description 云端企业服务
 * @program holder-saas-store-trading-center
 */
@Component
@FeignClient(name = "holder-saas-cloud-enterprise", fallbackFactory = EnterpriseRpcService.EnterClientFallBack.class)
public interface EnterpriseRpcService {

    @ApiOperation(value = "根据手机号查询门店")
    @GetMapping("/organization/store/tel")
    OrganizationDTO getStoreInfoByTel(@RequestParam("tel") String tel);

    @Component
    class EnterClientFallBack implements FallbackFactory<EnterpriseRpcService> {

        private static final Logger logger = LoggerFactory.getLogger(EnterClientFallBack.class);

        @Override
        public EnterpriseRpcService create(Throwable throwable) {

            return (tel) -> {
                logger.error("根据手机号查询门店失败 tel--{} , exception {}", tel, throwable);
                return null;
            };
        }
    }

}
