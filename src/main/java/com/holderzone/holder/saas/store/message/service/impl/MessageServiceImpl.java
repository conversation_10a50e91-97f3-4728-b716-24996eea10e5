package com.holderzone.holder.saas.store.message.service.impl;


import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.base.dto.message.MessageType;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.store.message.domain.DBPropertyDO;
import com.holderzone.holder.saas.store.message.domain.MessageDO;
import com.holderzone.holder.saas.store.message.mapper.MessageMapper;
import com.holderzone.holder.saas.store.message.service.MessageService;
import com.holderzone.holder.saas.store.message.service.rpc.CloudDatabaseClient;
import com.holderzone.holder.saas.store.message.service.rpc.EmqClient;
import com.holderzone.holder.saas.store.message.util.DynamicHelper;
import com.holderzone.saas.store.dto.message.*;
import com.holderzone.saas.store.enums.msg.BusinessMsgTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static com.holderzone.holder.saas.store.message.util.MessageMapStruct.MESSAGE_MAP_STRUCT;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MessageServiceImpl
 * @date 2018/09/04 16:54
 * @description
 * @program holder-saas-bussiness-message
 */
@Service
public class MessageServiceImpl implements MessageService {

    private static final Logger logger = LoggerFactory.getLogger(MessageServiceImpl.class);

    private final MessageMapper messageMapper;

    private final EmqClient emqClient;

    private final CloudDatabaseClient cloudDatabaseClient;

    private final DynamicHelper dynamicHelper;

    @Value("${dynamic.server.server-code}")
    private String serverCode;

    @Autowired
    public MessageServiceImpl(MessageMapper messageMapper, EmqClient emqClient, CloudDatabaseClient
            cloudDatabaseClient, DynamicHelper dynamicHelper) {
        this.messageMapper = messageMapper;
        this.emqClient = emqClient;
        this.cloudDatabaseClient = cloudDatabaseClient;
        this.dynamicHelper = dynamicHelper;
    }

    @Override
    public String insert(BusinessMessageDTO businessMessageDTO) {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        MessageDO messageDO = MESSAGE_MAP_STRUCT.msgDtoToDo(businessMessageDTO);
        messageDO.setMessageGuid(String.valueOf(dynamicHelper.generateGuid()));
        messageDO.setEnterpriseGuid(enterpriseGuid);
        messageDO.setPushTime(DateTimeUtils.now());
        if (!BusinessMsgTypeEnum.TABLE_STATE_MSG_TYPE.getId().equals(businessMessageDTO.getMessageType())) {
            if (BusinessMsgTypeEnum.TAKEOUT_STATE_MSG_TYPE.getId().equals(businessMessageDTO.getMessageType())) {
                messageDO.setState(1);
                messageMapper.insert(messageDO);
            } else {
                messageDO.setState(0);
                if (messageDO.getContent().length() < 200) {
                    messageMapper.insert(messageDO);
                }
                // 推送小铃铛
                pushUnReadMsgCount(messageDO);
            }
        } else {
            messageDO.setState(1);
        }
        // 推送给emq服务
        EmqMessageDTO emqMessageDTO = getEmqMessageDTO(enterpriseGuid, messageDO, businessMessageDTO);
        emqClient.sendMsg(emqMessageDTO);
        logger.info("推送业务消息 emqMessageDTO={}", JacksonUtils.writeValueAsString(emqMessageDTO));
        return "success";
    }

    private EmqMessageDTO getEmqMessageDTO(String enterpriseGuid, MessageDO messageDO, BusinessMessageDTO businessMessageDTO) {
        EmqMessageDTO emqMessageDTO = new EmqMessageDTO();
        PushMessageDTO pushMessageDTO = new PushMessageDTO();
        pushMessageDTO.setTopicType(TopicType.BUSINESS);
        pushMessageDTO.setData(JacksonUtils.writeValueAsString(messageDO));
        BusinessMessage businessMessage = new BusinessMessage();
        businessMessage.setEnterpriseGuid(enterpriseGuid);
        businessMessage.setStoreGuid(messageDO.getStoreGuid());
        businessMessage.setBusinessType(String.valueOf(messageDO.getMessageType()));
        // pad特殊处理为拼接messageType
        if (!ObjectUtils.isEmpty(businessMessageDTO) && !ObjectUtils.isEmpty(businessMessageDTO.getMessageType()) &&
                (Objects.equals(BusinessMsgTypeEnum.PAD_MESSAGE.getId(), businessMessageDTO.getMessageType()))) {
            businessMessage.setBusinessType(businessMessageDTO.getMessageTypeStr());
        }
        pushMessageDTO.setBusinessMessage(businessMessage);
        emqMessageDTO.setPushMessage(pushMessageDTO);
        emqMessageDTO.setMessageType(MessageType.PUSH);
        return emqMessageDTO;
    }

    /**
     * fixme 批量插入消息时，目前逻辑是“每一条消息推送一次未读消息数量”，需要修改为只推送一次“未读消息数量”
     *
     * @param businessMessageDTOS
     * @return
     */
    @Override
    public String insertAll(List<BusinessMessageDTO> businessMessageDTOS) {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        List<MessageDO> messageDOS = MESSAGE_MAP_STRUCT.msgDtosToDos(businessMessageDTOS);
        List<EmqMessageDTO> emqMessageDTOS = new ArrayList<>();
        messageDOS.forEach(messageDO -> {
            messageDO.setMessageGuid(String.valueOf(dynamicHelper.generateGuid()));
            messageDO.setEnterpriseGuid(enterpriseGuid);
            messageDO.setState(0);
            EmqMessageDTO emqMessageDTO = getEmqMessageDTO(enterpriseGuid, messageDO, null);
            emqMessageDTOS.add(emqMessageDTO);
        });
        messageMapper.insertAll(messageDOS);
        // 推送给emq服务
        emqMessageDTOS.forEach(emqClient::sendMsg);
        return "success";
    }

    @Override
    public void insertSystemMsg(List<MessageDO> messageDOList) {
        messageMapper.insertAll(messageDOList);
        // 推送系统消息
        List<EmqMessageDTO> emqMessageDTOS = new ArrayList<>();
        messageDOList.forEach(messageDO -> {
            EmqMessageDTO emqMessageDTO = getEmqMessageDTO(messageDO.getEnterpriseGuid(), messageDO, null);
            emqMessageDTOS.add(emqMessageDTO);
        });
        emqMessageDTOS.forEach(emqClient::sendMsg);
        // 推送小铃铛
        messageDOList.forEach(this::pushUnReadMsgCount);
        logger.info("插入系统消息，messageDOList={}", JacksonUtils.writeValueAsString(messageDOList));
    }

    @Override
    public Page<MsgInfoRespDTO> queryAll(MsgQuery msgQuery) {
        msgQuery.setBeginTime(DateTimeUtils.beginTimeOfDay(DateTimeUtils.now()));
        msgQuery.setEndTime(DateTimeUtils.endTimeOfDay(DateTimeUtils.now()));
        int count = messageMapper.queryCount(msgQuery);
        if (count == 0) {
            return new Page<>();
        }
        List<MsgInfoRespDTO> messageDOS = messageMapper.queryAll(msgQuery);
        LocalDateTime now = DateTimeUtils.now();
        messageDOS.forEach(msgInfoRespDTO -> msgInfoRespDTO.setCurrentTime(now));
        return new Page<>(msgQuery.getCurrentPage(), msgQuery.getPageSize(), count, messageDOS);
    }

    @Override
    public MsgRespDTO queryDetail(String messageGuid, Integer state) {
        MessageDO messageDO = messageMapper.queryDetail(messageGuid);
        MsgRespDTO msgResp = new MsgRespDTO();
        BeanUtils.copyProperties(messageDO, msgResp);
        msgResp.setCurrentTime(DateTimeUtils.now());
        if (state == 0) {
            messageMapper.markMsgHasRead(messageGuid);
        }
        // 每次查询后通知该门店有多少消息未读
        pushUnReadMsgCount(messageDO);
        return msgResp;
    }

    private void pushUnReadMsgCount(MessageDO messageDO) {
        EmqMessageDTO emqMessageDTO = new EmqMessageDTO();
        PushMessageDTO pushMessageDTO = new PushMessageDTO();
        pushMessageDTO.setTopicType(TopicType.BUSINESS);
        BusinessMessage businessMessage = new BusinessMessage();
        LocalDateTime begin = DateTimeUtils.beginTimeOfDay(DateTimeUtils.now());
        LocalDateTime end = DateTimeUtils.endTimeOfDay(DateTimeUtils.now());
        int data = messageMapper.countUnReadMsg(messageDO.getStoreGuid(), begin, end);
        pushMessageDTO.setData("" + data);
        businessMessage.setBusinessType(BusinessMsgTypeEnum.UN_REDAN.getId() + "");

        businessMessage.setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
        businessMessage.setStoreGuid(messageDO.getStoreGuid());
        pushMessageDTO.setBusinessMessage(businessMessage);
        emqMessageDTO.setPushMessage(pushMessageDTO);
        emqMessageDTO.setMessageType(MessageType.PUSH);
        emqClient.sendMsg(emqMessageDTO);
        logger.info("推送小铃铛 emqMessageDTO={}", JacksonUtils.writeValueAsString(emqMessageDTO));
    }

    @Override
    public int getCount(MsgQuery msgQuery) {
        LocalDateTime begin = DateTimeUtils.beginTimeOfDay(DateTimeUtils.now());
        LocalDateTime end = DateTimeUtils.endTimeOfDay(DateTimeUtils.now());
        int i = messageMapper.countUnReadMsg(msgQuery.getStoreGuid(), begin, end);
        return i;
    }

    private final AtomicInteger mCount = new AtomicInteger(0);

    private final ExecutorService executorService = new ThreadPoolExecutor(3, 5,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(5000),
            r -> new Thread(r, "deleteMsg-" + mCount.incrementAndGet()));

    /**
     * fixme
     * 定期清除历史数据，目前实现方法是“拉取云平台数据库的企业DB信息，并依次切换数据源”，需要使用动态数据源实现方法逻辑，同时云平台也需要关闭该接口的提供，避免出现安全问题
     * 定期清除历史数据，目前清除策略是“每过14天清除一次所有数据”，需要修改为“历史数据TTL可动态配置生效”
     *
     * @return
     */
    @Override
    public String clean() {
        //清除半个月前的数据
        LocalDateTime localDateTime = LocalDateTime.now().minusDays(15L);
        String nowTime = DateTimeUtils.parseLong2Str(DateTimeUtils.localDateTime2Mills(localDateTime), "yyyy-MM-dd HH:mm:ss");
        List<DBPropertyDO> databaseInfo = cloudDatabaseClient.getDatabaseInfo(Collections.singletonList(serverCode));
        final CountDownLatch countDownLatch = new CountDownLatch(databaseInfo.size());
        databaseInfo.forEach(dbPropertyDO -> {
            executorService.submit(() -> {
                PreparedStatement stat = null;
                Connection conn = null;
                try {
                    Class.forName("com.mysql.jdbc.Driver");
                    String url = getJDBCUrl(dbPropertyDO.getDatabaseHost(), dbPropertyDO.getPort(), dbPropertyDO
                            .getEnterpriseServerDatasourceName());
                    conn = DriverManager.getConnection(url, dbPropertyDO.getDatabaseUser(), dbPropertyDO
                            .getDatabasePassword());
                    conn.setAutoCommit(false);
                    String sql = "delete from hsm_message where gmt_create <= ?";
                    stat = conn.prepareStatement(sql);
                    stat.setString(1, nowTime);
                    int count = stat.executeUpdate();
                    conn.commit();
                    logger.info("删除行数为 count={}", count);
                } catch (ClassNotFoundException | SQLException e) {
                    e.printStackTrace();
                    try {
                        if (null != conn)
                            conn.rollback();
                    } catch (SQLException e1) {
                        e1.printStackTrace();
                    }
                } finally {
                    countDownLatch.countDown();
                    try {
                        if (null != stat)
                            stat.close();
                        if (null != conn)
                            conn.close();
                    } catch (SQLException e) {
                        e.printStackTrace();
                        logger.error("删除异常 e={}", e.getMessage());
                    }
                }
            });
        });
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            logger.error("线程异常 e={}", e.getMessage());
            e.printStackTrace();
        }
        return "success";
    }

    @Override
    public void readAll(String storeGuid,
                        Integer messageType) {
        messageMapper.readAll(storeGuid, messageType);
        // 通知该门店有多少消息未读
        MessageDO messageDO = new MessageDO();
        messageDO.setStoreGuid(storeGuid);
        pushUnReadMsgCount(messageDO);
    }

    private String getJDBCUrl(String url, String port, String dataBaseName) {
        return "jdbc:mysql://" + url + ":" + port + "/" + dataBaseName;
    }
}
