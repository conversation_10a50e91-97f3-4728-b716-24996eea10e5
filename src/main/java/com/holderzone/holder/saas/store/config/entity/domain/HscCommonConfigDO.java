package com.holderzone.holder.saas.store.config.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 商户配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hsc_common_config")
public class HscCommonConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * guid
     */
    @TableId(value = "guid",type = IdType.INPUT)
    private String guid;

    /**
     * 商户guid
     */
    private String enterpriseGuid;

    /**
     * 门店guid
     */
    private String storeGuid;

    /**
     * 配置类型
     * @see com.holderzone.saas.store.enums.common.ConfigEnum
     */
    private Integer dicCode;

    /**
     * 配置类型名称
     */
    private String dicName;

    /**
     * 配置项value ，多个值可自定义或存json字符串
     */
    private String dictValue;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否启用  0：启用  1：不启用
     */
    private Integer isEnable;


}
