package com.holderzone.holder.saas.aggregation.app.manage;

import com.holderzone.holder.saas.aggregation.app.service.feign.OrganizationClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.PrintClientService;
import com.holderzone.saas.store.dto.emq.EmqReconnectDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class EmqReconnectManage {

    @Autowired
    private PrintClientService printClientService;

    @Autowired
    private OrganizationClientService organizationClientService;

    /**
     * emq客户端重连通知
     */
    public void notifyClient(EmqReconnectDTO emqReconnectDTO) {
    }
}
