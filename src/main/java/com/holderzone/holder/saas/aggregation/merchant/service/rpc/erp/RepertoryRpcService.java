package com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.*;
import com.holderzone.saas.store.dto.erp.erpretail.req.*;
import com.holderzone.saas.store.dto.erp.erpretail.resp.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-erp", fallbackFactory = RepertoryRpcService.FallbackFactoryImpl.class)
public interface RepertoryRpcService {

    @PostMapping("/repertory/insert_repertory")
    boolean insertRepertory(@RequestBody CreateRepertoryReqDTO createRepertoryReqDTO);

    @PostMapping("/repertory/query_goods_repertory_sum_info")
    Page<GoodsSumInfoRespDTO> queryGoodsRepertorySumInfo(@RequestBody QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO);

    @PostMapping("/repertory/query_goods_serial")
    Page<GoodsSerialRespDTO> queryGoodsSerial(@RequestBody @Validated QueryGoodsSerialReqDTO queryGoodsSerialReqDTO);

    @PostMapping("/repertory/query_repertory_detail")
    RepertoryDetailInfoRespDTO queryRepertoryDetail(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping("/repertory/query_in_out_repertory_list")
    Page<RepertoryManageRespDTO> queryInOutRepertoryList(@RequestBody QueryRepertoryManageReqDTO queryRepertoryManageReqDTO);

    @PostMapping("/repertory/invalid_repertory")
    boolean invalidRepertory(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping("/inventory/create_inventory")
    String createInventory(@RequestBody CreateInventoryReqDTO inventoryDTO);

    @PostMapping("/inventory/query_inventory_detail")
    InventoryDetailRespDTO queryInventoryDetail(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping("/inventory/query_inventory_overview")
    Page<InventoryManageRespDTO> queryInventoryOverView(@RequestBody @Validated InventoryOverviewReqDTO inventoryOverviewReqDTO);

    @PostMapping("/inventory/invalid_inventory")
    boolean invalidInventory(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping("/repertory/query_goods_list")
    List<GoodsClassifyAndItemRespDTO> queryGoodsList(SingleDataDTO singleDataDTO);

    @Slf4j
    @Component
    class FallbackFactoryImpl implements FallbackFactory<RepertoryRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public RepertoryRpcService create(Throwable throwable) {

            return new RepertoryRpcService() {

                @Override
                public boolean insertRepertory(CreateRepertoryReqDTO createRepertoryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "insertRepertory",
                            JacksonUtils.writeValueAsString(createRepertoryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<GoodsSumInfoRespDTO> queryGoodsRepertorySumInfo(QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryGoodsRepertorySumInfo",
                            JacksonUtils.writeValueAsString(queryGoodsSumInfoReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<GoodsSerialRespDTO> queryGoodsSerial(QueryGoodsSerialReqDTO queryGoodsSerialReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryGoodsInOutRepertoryRecord",
                            JacksonUtils.writeValueAsString(queryGoodsSerialReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public RepertoryDetailInfoRespDTO queryRepertoryDetail(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "queryRepertoryDetail",
                            JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<RepertoryManageRespDTO> queryInOutRepertoryList(QueryRepertoryManageReqDTO queryRepertoryManageReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryInOutRepertoryList",
                            JacksonUtils.writeValueAsString(queryRepertoryManageReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean invalidRepertory(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "invalidRepertory",
                            JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public String createInventory(CreateInventoryReqDTO createInventoryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "invalidRepertory",
                            JacksonUtils.writeValueAsString(createInventoryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public InventoryDetailRespDTO queryInventoryDetail(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "invalidRepertory",
                            JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<InventoryManageRespDTO> queryInventoryOverView(InventoryOverviewReqDTO inventoryOverviewReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryInventoryOverView",
                            JacksonUtils.writeValueAsString(inventoryOverviewReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean invalidInventory(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "invalidInventory",
                            JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<GoodsClassifyAndItemRespDTO> queryGoodsList(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "queryGoodsList", ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}