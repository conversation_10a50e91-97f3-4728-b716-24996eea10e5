package com.holderzone.holder.saas.aggregation.app.controller.trade;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.app.config.IPassInvoiceConfig;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.entity.OrderLocaleEnum;
import com.holderzone.holder.saas.aggregation.app.service.InvoiceService;
import com.holderzone.holder.saas.aggregation.app.service.PrintService;
import com.holderzone.holder.saas.aggregation.app.service.feign.WeiXinClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.business.PaymentTypeClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.NewMemberInfoClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderDetailsClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderItemClientService;
import com.holderzone.holder.saas.aggregation.app.utils.SignatureUtil;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.OrderLockDTO;
import com.holderzone.saas.store.dto.order.request.OrderDetailQueryDTO;
import com.holderzone.saas.store.dto.order.request.dinein.*;
import com.holderzone.saas.store.dto.order.response.bill.ActuallyPayFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineInOrderListRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.ReturnOrderDetailDTO;
import com.holderzone.saas.store.dto.trade.*;
import com.holderzone.saas.store.dto.trade.req.UpdateOrderInvoiceStateReqDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOperationDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOperateReqDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.PaymentTypeEnum;
import com.holderzone.saas.store.enums.locale.ReasonLocaleEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.util.BigDecimalUtil;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInOrderAppController
 * @date 2018/09/04 11:26
 * @description app聚合层订单接口
 * @program holder-saas-aggregation-app
 */
@RestController
@RequestMapping("/dine_in_order")
@Api(tags = "订单接口")
@Slf4j
public class DineInOrderAppController {

    /**
     * 签名头
     */
    public static final String X_SIGNATURE = "X-Signature";
    /**
     * api key
     */
    public static final String X_API_KEY = "X-API-Key";

    private final DineInOrderClientService dineInOrderClientService;

    private final NewMemberInfoClientService memberInfoClientService;

    private final WeiXinClientService weiXinClientService;

    private final OrderItemClientService orderItemClientService;

    private final OrderDetailsClientService orderDetailsClientService;

    private final PrintService printService;

    private final PaymentTypeClientService paymentTypeClientService;

    @Resource
    private InvoiceService invoiceService;

    @Resource
    private IPassInvoiceConfig iPassInvoiceConfig;

    @Autowired
    public DineInOrderAppController(DineInOrderClientService dineInOrderClientService,
                                    NewMemberInfoClientService memberInfoClientService,
                                    WeiXinClientService weiXinClientService,
                                    OrderItemClientService orderItemClientService,
                                    OrderDetailsClientService orderDetailsClientService,
                                    PrintService printService, PaymentTypeClientService paymentTypeClientService) {
        this.dineInOrderClientService = dineInOrderClientService;
        this.memberInfoClientService = memberInfoClientService;
        this.weiXinClientService = weiXinClientService;
        this.orderItemClientService = orderItemClientService;
        this.orderDetailsClientService = orderDetailsClientService;
        this.printService = printService;
        this.paymentTypeClientService = paymentTypeClientService;
    }

    @ApiOperation(value = "订单详情", notes = "订单详情")
    @PostMapping(value = "/get_order_detail", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "订单详情", action = OperatorType.SELECT)
    public Result<DineinOrderDetailRespDTO> getOrderDetail(@RequestBody @Validated OrderDetailQueryDTO orderDetailQueryDTO) {
        log.info("订单详情入参：{}", JacksonUtils.writeValueAsString(orderDetailQueryDTO));
        DineinOrderDetailRespDTO dine = dineInOrderClientService.getOrderDetail(orderDetailQueryDTO);
        log.info("订单详情返回：{}", JacksonUtils.writeValueAsString(dine));
        if (StringUtils.isNotBlank(dine.getMemberPhone())) {
            handleMemberPhone(dine);
        }
        if (!ObjectUtils.isEmpty(dine) && ObjectUtils.isEmpty(dine.getIntegralType())) {
            dine.setIntegralType(0);
        }
        //设置本地化支付方式
        localePayFeeType(dine.getActuallyPayFeeDetailDTOS());

        if (CollectionUtil.isNotEmpty(dine.getDiscountFeeDetailDTOS())) {
            dine.getDiscountFeeDetailDTOS().forEach(d -> d.setDiscountName(DiscountTypeEnum.getLocaleDescByName(d.getDiscountName())));
        }
        ReturnOrderDetailDTO returnOrderDetail = dine.getReturnOrderDetailDTO();
        if (returnOrderDetail != null) {
            returnOrderDetail.setRecoveryDeviceTypeName(BaseDeviceTypeEnum.getLocaleDesc(returnOrderDetail.getRecoveryDeviceTypeName()));
            localePayFeeType(returnOrderDetail.getActuallyPayFeeDetailDTOS());
            returnOrderDetail.setRecoveryReason(ReasonLocaleEnum.getLocale(returnOrderDetail.getRecoveryReason()));
        }
        if (dine.getCancelDetailDTO() != null) {
            dine.getCancelDetailDTO().setCancelDeviceName(BaseDeviceTypeEnum.getLocaleDesc(dine.getCancelDetailDTO().getCancelDeviceName()));
            dine.getCancelDetailDTO().setCancelStaffName(BaseDeviceTypeEnum.getLocaleDesc(dine.getCancelDetailDTO().getCancelStaffName()));
            dine.getCancelDetailDTO().setCancelReason(ReasonLocaleEnum.getLocale(dine.getCancelDetailDTO().getCancelReason()));
        }
        dine.setDeviceTypeName(BaseDeviceTypeEnum.getLocaleDesc(dine.getDeviceTypeName()));
        dine.setCheckoutDeviceTypeName(BaseDeviceTypeEnum.getLocaleDesc(dine.getCheckoutDeviceTypeName()));
        dine.setTradeModeName(TradeModeEnum.getLocaleDesc(dine.getTradeMode()));
        // 预付金大于订单金额情况展示新的方式
        if (!CollectionUtils.isEmpty(dine.getActuallyPayFeeDetailDTOS())) {
            dine.getActuallyPayFeeDetailDTOS().removeIf(pay -> BigDecimalUtil.lessThanZero(pay.getAmount()));
        }
        return Result.buildSuccessResult(dine);
    }

    private void localePayFeeType(List<ActuallyPayFeeDetailDTO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        list.forEach(p -> {
            String localeName = PaymentTypeEnum.PaymentType.getLocaleName(p.getPaymentTypeName());
            if (StringUtils.isNotEmpty(localeName)) {
                p.setPaymentTypeName(localeName);
            }
        });
    }

    private void handleMemberPhone(DineinOrderDetailRespDTO dine) {
        RequestQueryStoreAndMemberAndCard param = new RequestQueryStoreAndMemberAndCard();
        param.setPhoneNumOrCardNum(dine.getMemberPhone());
        log.info("查询会员电话号入参：{}", JacksonUtils.writeValueAsString(param));
        try {
            ResponseMemberAndCardInfoDTO memberAndCardInfoRespDTO = memberInfoClientService.getMemberInfoAndCard(param);
            if (!ObjectUtils.isEmpty(memberAndCardInfoRespDTO.getMemberInfoDTO())) {
                log.info("查询会员电话号返回：{}", JacksonUtils.writeValueAsString(memberAndCardInfoRespDTO.getMemberInfoDTO()));
                String mobile = memberAndCardInfoRespDTO.getMemberInfoDTO().getPhoneNum();
                if (!StringUtils.isEmpty(mobile)) {
                    mobile = mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
                }
                dine.setMemberPhone(mobile);
            } else {
                log.info("查询会员电话号返回：{}", JacksonUtils.writeValueAsString(memberAndCardInfoRespDTO));
            }
        } catch (Exception e) {
            //查询出错只打印错误，不影响查询订单流程
            log.info("查询会员出错，{}", e.getMessage());
        }
    }

    @ApiOperation(value = "点餐列表的订单详情", notes = "点餐列表的订单详情")
    @PostMapping(value = "/get_order_detail_4_add_item", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "桌台旁边订单详情",action = OperatorType.SELECT)
    public Result<DineinOrderDetailRespDTO> getOrderDetail4AddItem(@RequestBody @Validated SingleDataDTO singleDataDTO) {
        log.info("点餐列表的订单详情 singleDataDTO={}", JacksonUtils.writeValueAsString(singleDataDTO));
        return Result.buildSuccessResult(dineInOrderClientService.getOrderDetail4AddItem(singleDataDTO));
    }

    @ApiOperation(value = "收款方式下拉", notes = "收款方式下拉")
    @GetMapping("/list_payment_type_name")
    public Result<List<String>> listPaymentTypeName() {
        PaymentTypeQueryDTO paymentTypeQueryDTO = new PaymentTypeQueryDTO();
        paymentTypeQueryDTO.setStoreGuid(UserContextUtils.getStoreGuid());
        List<PaymentTypeDTO> all = paymentTypeClientService.getAll(paymentTypeQueryDTO);
        List<String> paymentName = Lists.newArrayList();
        if(CollectionUtil.isNotEmpty(all)){
            all.forEach(p ->{
                if(p.getState() == 1){
                    return;
                }
                String localeName = PaymentTypeEnum.PaymentType.getLocaleNameById(p.getPaymentType());
                if (StringUtils.isEmpty(localeName)) {
                    paymentName.add(p.getPaymentTypeName());
                }else {
                    paymentName.add(localeName);
                }
            });
        }
        return Result.buildSuccessResult(paymentName);
    }

    @ApiOperation(value = "订单列表", notes = "订单列表")
    @PostMapping("/order_list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "订单列表", action = OperatorType.SELECT)
    public Result<Page<DineInOrderListRespDTO>> orderList(@RequestBody DineInOrderListReqDTO dineInOrderListReqDTO) {
        Page<DineInOrderListRespDTO> page = dineInOrderClientService.orderList(dineInOrderListReqDTO);
        if (CollectionUtil.isNotEmpty(page.getData())) {
            page.getData().forEach(d -> d.setStateName(OrderLocaleEnum.getOrderStatusLocale(d.getStateName())));
        }
        return Result.buildSuccessResult(page);
    }

    @ApiOperation(value = "修改整单备注", notes = "修改整单备注")
    @PostMapping("/update_remark")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "修改整单备注")
    public Result updateRemark(@RequestBody CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        if (dineInOrderClientService.updateRemark(createDineInOrderReqDTO)) {
            return Result.buildEmptySuccess();
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));

    }

    @ApiOperation(value = "修改就餐人数", notes = "修改就餐人数")
    @PostMapping("/update_guest_count")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "修改就餐人数")
    public Result updateGuestCount(@RequestBody CreateDineInOrderReqDTO createDineInOrderReqDTO) {
        if (dineInOrderClientService.updateGuestCount(createDineInOrderReqDTO)) {
            return Result.buildEmptySuccess();
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "作废订单", notes = "作废订单")
    @PostMapping("/cancel")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "作废订单")
    public Result cancelOrder(@RequestBody CancelOrderReqDTO cancelOrderReqDTO) {
        log.info("作废订单入参：{}", JacksonUtils.writeValueAsString(cancelOrderReqDTO));
        if (dineInOrderClientService.cancelOrder(cancelOrderReqDTO)) {
            return Result.buildSuccessResult(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "打印预结单", notes = "打印预结单")
    @PostMapping("/print_pre_bill")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "打印预结单", action = OperatorType.SELECT)
    public Result printPreBill(@RequestBody SingleDataDTO singleDataDTO) {
        if (dineInOrderClientService.printPreBill(singleDataDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "打印菜品清单", notes = "打印菜品清单")
    @PostMapping("/print_item_detail")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "打印菜品清单", action = OperatorType.SELECT)
    public Result printItemDetail(@RequestBody SingleDataDTO singleDataDTO) {
        if (dineInOrderClientService.printItemDetail(singleDataDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "打印菜品复单", notes = "打印菜品复单")
    @PostMapping("/print_item_repeat_order")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_PRINT, description = "打印菜品复单", action = OperatorType.SELECT)
    public Result<Void> printItemRepeatOrder(@RequestBody CreateDineInOrderReqDTO itemRepeatOrderReqDTO) {
        log.info("[打印菜品复单]itemRepeatOrderReqDTO={}", JacksonUtils.writeValueAsString(itemRepeatOrderReqDTO));
        if (dineInOrderClientService.printItemRepeatOrder(itemRepeatOrderReqDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "打印结账单", notes = "打印结账单")
    @PostMapping("/print_check_out")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "打印结账单", action = OperatorType.SELECT)
    public Result printCheckOut(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("[打印结账单]singleDataDTO={}", JacksonUtils.writeValueAsString(singleDataDTO));
        if (dineInOrderClientService.printCheckOut(singleDataDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));

    }

    @ApiOperation(value = "加订单锁", notes = "加订单锁")
    @PostMapping("/order/lock")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "加订单锁", action = OperatorType.SELECT)
    public Result lockorder(@RequestBody OrderLockDTO orderLockDto) {
        log.info("加订单锁入参：{}", JacksonUtils.writeValueAsString(orderLockDto));
        boolean b = dineInOrderClientService.lockOrder(orderLockDto);
        return b ? Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL)) : Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "解订单锁", notes = "解订单锁")
    @PostMapping("/order/unlock")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "解订单锁", action = OperatorType.SELECT)
    public Result unLockOrder(@RequestBody OrderLockDTO orderLockDto) {
        log.info("解订单锁入参：{}", JacksonUtils.writeValueAsString(orderLockDto));
        dineInOrderClientService.unlockOrder(orderLockDto);
        return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
    }

    @ApiOperation(value = "批量保存离线订单", notes = "批量保存离线订单")
    @PostMapping("/batch_save_offline_order")
    public Result<BatchFastOfflineOrderResp> batchSaveOfflineOrder(@RequestBody BatchCreateFastFoodReqDTO fastFoodReqDTO) {
        log.info("批量保存离线订单：{}", JacksonUtils.writeValueAsString(fastFoodReqDTO));
        return Result.buildSuccessResult(dineInOrderClientService.batchSaveFastOrder(fastFoodReqDTO));

    }


    @ApiOperation(value = "本地化数据拉取", notes = "本地化数据拉取")
    @PostMapping("/order/local")
    public Result<LocalDTO> orderLocal(@RequestBody LocalQuery localQuery) {
        log.info("本地化数据拉取入参：{}", JacksonUtils.writeValueAsString(localQuery));
        return Result.buildSuccessResult(dineInOrderClientService.getLocal(localQuery));
    }


    @ApiOperation(value = "本地化数据上传", notes = "本地化数据上传")
    @PostMapping("/order/save_local")
    public Result<Map<String, String>> orderLocal(@RequestBody LocalDTO localDTO) {
        if (!ObjectUtils.isEmpty(localDTO.getOrderList())) {
            Map<String, BigDecimal> orderFeeMap = new HashMap<>();
            for (OrderDTO orderDTO : localDTO.getOrderList()) {
                orderFeeMap.put(orderDTO.getGuid(), orderDTO.getOrderFee());
            }
            log.info("本地化数据上传入参：orderFeeMap={}", JacksonUtils.writeValueAsString(orderFeeMap));
        }
        Map<String, String> result = dineInOrderClientService.saveLocal(localDTO);
        log.info("本地化数据上传返回：{}", JacksonUtils.writeValueAsString(result));
        Set keys = result.keySet();
        Iterator it = keys.iterator();
        while (it.hasNext()) {
            if (it.next().equals("-1")) {
                return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
            }
        }
        return Result.buildSuccessResult(result);
    }


    @ApiOperation(value = "本地化单条数据拉取", notes = "本地化单条数据拉取")
    @PostMapping("/order/single")
    public Result<LocalDTO> orderLocal(@RequestBody SingleQuery singleQuery) {
        log.info("本地化单条数据拉取入参：{}", JacksonUtils.writeValueAsString(singleQuery));
        return Result.buildSuccessResult(dineInOrderClientService.getSingle(singleQuery));
    }

    @ApiOperation(value = "校验服务是否可用（给安卓端定时调用）", notes = "校验服务是否可用（给安卓端定时调用）")
    @GetMapping("/order/service")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "解订单锁")
    public Result<String> orderService() {
        String result = dineInOrderClientService.getService();
        return Result.buildSuccessResult(result);
    }

    @ApiOperation("商户处理订单，商户接单")
    @PostMapping(value = "/operate")
    public Result<WxStoreMerchantOperationDTO> operationMerchantOrder(@RequestBody WxOperateReqDTO wxOperateReqDTO) {
        log.info("获取当前门店所有待处理订单入参wxOperateReqDTO:{}", wxOperateReqDTO);
        WxStoreMerchantOperationDTO wxStoreMerchantOperationDTO = weiXinClientService.operationMerchantOrder(wxOperateReqDTO);
        return Result.buildSuccessResult(wxStoreMerchantOperationDTO);
    }

    /**
     * 获取订单详情
     *
     * @param singleDataDTO 订单Guid
     * @return 订单详情
     */
    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @PostMapping("/find_by_order_guid")
    public Result<OrderDTO> findByOrderGuid(@RequestBody SingleDataDTO singleDataDTO) {
        log.info("获取订单详情 singleDataDTO={}", JacksonUtils.writeValueAsString(singleDataDTO));
        return Result.buildSuccessResult(orderItemClientService.findByOrderGuid(singleDataDTO.getData()));
    }

    @ApiOperation(value = "增加版本号", notes = "增加版本号")
    @PostMapping("/add/version")
    public Result<Boolean> addVersion(@RequestBody String orderGuid) {
        log.info("增加版本号：{}", JacksonUtils.writeValueAsString(orderGuid));
        return Result.buildSuccessResult(dineInOrderClientService.addVersion(orderGuid));
    }

    @ApiOperation(value = "更新订单开票状态")
    @PostMapping("/update_order_invoice_state")
    public Result<Void> updateOrderInvoiceState(@RequestBody UpdateOrderInvoiceStateReqDTO reqDTO,
                                                HttpServletRequest request) throws NoSuchAlgorithmException, InvalidKeyException {
        log.info("[更新订单开票状态]reqDTO={}", JacksonUtils.writeValueAsString(reqDTO));
        String signature = request.getHeader(X_SIGNATURE);
        if (!SignatureUtil.valid(JacksonUtils.writeValueAsString(reqDTO), iPassInvoiceConfig.getApiSecret(), signature)) {
            throw new BusinessException("验签失败");
        }
        orderDetailsClientService.updateOrderInvoiceState(reqDTO);
        return Result.buildEmptySuccess();
    }

}
