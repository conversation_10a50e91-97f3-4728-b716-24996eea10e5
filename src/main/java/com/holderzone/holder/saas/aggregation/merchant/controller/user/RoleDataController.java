package com.holderzone.holder.saas.aggregation.merchant.controller.user;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.CommonLocaleEnum;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.MerchantMenuLocaleEnum;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.user.RoleFeignService;
import com.holderzone.holder.saas.aggregation.merchant.util.MenuLocaleUtil;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.user.DefaultMenuChecked;
import com.holderzone.saas.store.dto.user.RoleTerminalQueryDTO;
import com.holderzone.saas.store.dto.user.TerminalDTO;
import com.holderzone.saas.store.dto.user.TerminalSourceDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className RoleController
 * @date 19-1-15 下午5:07
 * @description
 * @program holder-saas-store-staff
 */
@RestController
@RequestMapping("/role_data")
@Api(tags = "角色授权相关接口", description = "商户后台二期角色授权相关服务接口")
public class RoleDataController {
    private final RoleFeignService roleFeignService;

    @Autowired
    public RoleDataController(RoleFeignService roleFeignService) {
        this.roleFeignService = roleFeignService;
    }

    @ApiOperation(value = "根据角色guid查询终端信息")
    @PostMapping("/query_role_terminal")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "根据角色guid查询终端信息")
    public Result<List<TerminalDTO>> queryRoleTerminal(@ApiParam("实体中的业务请求参数为角色guid") @RequestBody SingleDataDTO singleDataDTO) {
        List<TerminalDTO> terminalList = roleFeignService.queryRoleTerminal(singleDataDTO.getData());
        if(CollectionUtil.isNotEmpty(terminalList)){
            terminalList.forEach(e -> e.setTerminalName(CommonLocaleEnum.getLocale(e.getTerminalName())));
        }
        return Result.buildSuccessResult(terminalList);
    }

    @ApiOperation(value = "根据终端guid与角色guid查询模块/菜单及服务资源")
    @PostMapping("/query_role_terminal_data")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "根据终端guid与角色guid查询模块/菜单及服务资源")
    public Result<DefaultMenuChecked> queryRoleData(@RequestBody RoleTerminalQueryDTO queryDTO) {
        DefaultMenuChecked defaultMenuList = roleFeignService.queryRoleTerminalData(queryDTO.getRoleGuid(), queryDTO.getTerminalGuid());
        if(CollectionUtil.isNotEmpty(defaultMenuList.getMenuDTOList())){
            MenuLocaleUtil.transferMerchantMenu(defaultMenuList.getMenuDTOList());
        }
        return Result.buildSuccessResult(defaultMenuList);
    }

    @ApiOperation(value = "保存角色授权信息（多个终端分开保存）")
    @PostMapping("/save")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_STAFF,description = "保存角色授权信息（多个终端分开保存）")
    public Result<Boolean> saveRoleData(@RequestBody TerminalSourceDTO terminalSourceDTO) {
        return Result.buildSuccessResult(roleFeignService.saveRoleData(terminalSourceDTO));
    }
}