package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.member.terminal.dto.feign.FeignModel;
import com.holderzone.holder.saas.member.wechat.dto.activitie.ConsumptionGiftDetailDTO;
import com.holderzone.holder.saas.member.wechat.dto.activitie.MemberConsumptionGiftDTO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemQO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemVO;
import com.holderzone.saas.store.dto.marketing.UniteActivityQO;
import com.holderzone.saas.store.dto.marketing.UniteActivityVO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * 新会员 营销中心
 **/
@Component
@FeignClient(name = "holder-member-marketing", fallbackFactory = MemberMarketingClientService.ServiceFallBack.class, url = "${member.marketing.host}")
public interface MemberMarketingClientService {

    /**
     * 企微 - 用户授权
     */
    @GetMapping(value = "/marketing/wx_cp/query/authorize_url")
    FeignModel<String> queryWxCpAuthorizeUrl(@RequestParam("operSubjectGuid") String operSubjectGuid,
                                             @RequestParam("params") String params);

    /**
     * 计算消费赠送处理
     *
     * @param memberConsumptionGiftQO memberConsumptionGiftQO
     */
    @PostMapping("/marketing/consumption/activity/deal/consumption/gift")
    void dealConsumptionGift(@RequestBody MemberConsumptionGiftDTO memberConsumptionGiftQO);

    /**
     * 查询订单优惠
     *
     * @param orderNumber
     * @return
     */
    @GetMapping("/marketing/consumption/activity/get/order/gift")
    ConsumptionGiftDetailDTO getOrderGift(@RequestParam("orderNumber") String orderNumber);

    /**
     * 撤销送礼
     *
     * @param orderNumber 订单号
     */
    @GetMapping("/marketing/consumption/activity/revocation/consumption/gift")
    void revocationConsumptionGift(@RequestParam(value = "orderNumber") String orderNumber);

    @ApiOperation("查询会员能参与的活动的相关商品及活动信息")
    @PostMapping("/marketing/limit_specials_activity/query_activity_commodity")
    List<LimitSpecialsActivityItemVO> queryActivityCommodity(@RequestBody LimitSpecialsActivityItemQO query);

    @ApiOperation("营销活动统一查询")
    @PostMapping("/marketing/unite/activity/query")
    FeignModel<UniteActivityVO> query(@RequestBody @Validated UniteActivityQO query);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MemberMarketingClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public MemberMarketingClientService create(Throwable throwable) {

            return new MemberMarketingClientService() {

                @Override
                public FeignModel<String> queryWxCpAuthorizeUrl(String operSubjectGuid, String params) {
                    log.info(HYSTRIX_PATTERN, "方法：queryWxCpAuthorizeUrl， 调用会员营销服务失败, 请求入参，volumeCode:{}, throwable :{}"
                            , operSubjectGuid, params, throwable);
                    throw new BusinessException("企微 - 用户授权失败");
                }

                @Override
                public void dealConsumptionGift(MemberConsumptionGiftDTO memberConsumptionGiftQO) {
                    log.info(HYSTRIX_PATTERN, "方法：dealConsumptionGift， 调用会员营销服务失败, 请求入参，volumeCode:{}, throwable :{}"
                            , JacksonUtils.writeValueAsString(memberConsumptionGiftQO), throwable);
                    throw new BusinessException("计算消费有礼赠送处理失败");
                }

                @Override
                public ConsumptionGiftDetailDTO getOrderGift(String orderNumber) {
                    log.info(HYSTRIX_PATTERN, "方法：getOrderGift， 调用会员营销服务失败, 请求入参，volumeCode:{}, throwable :{}"
                            , orderNumber, throwable);
                    throw new BusinessException("查询订单优惠处理失败");
                }

                @Override
                public void revocationConsumptionGift(String orderNumber) {
                    log.info(HYSTRIX_PATTERN, "方法：revocationConsumptionGift， 调用会员营销服务失败, 请求入参，volumeCode:{}, throwable :{}"
                            , orderNumber, throwable);
                    throw new BusinessException("撤销送礼处理失败");
                }

                @Override
                public List<LimitSpecialsActivityItemVO> queryActivityCommodity(LimitSpecialsActivityItemQO query) {
                    log.info(HYSTRIX_PATTERN, "queryActivityCommodity", JacksonUtils.writeValueAsString(query),
                            throwable.getCause());
                    throw new ServerException();
                }

                @Override
                public FeignModel<UniteActivityVO> query(UniteActivityQO query) {
                    log.info(HYSTRIX_PATTERN, "query", JacksonUtils.writeValueAsString(query),
                            throwable.getCause());
                    return null;
                }
            };
        }
    }
}
