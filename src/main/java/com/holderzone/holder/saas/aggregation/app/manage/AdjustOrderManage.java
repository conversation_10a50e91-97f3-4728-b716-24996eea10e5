package com.holderzone.holder.saas.aggregation.app.manage;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.aggregation.app.builder.AdjustByOrderRespDTOBuilder;
import com.holderzone.holder.saas.aggregation.app.service.feign.takeout.TakeoutClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustByOrderItemQuery;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderQueryDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderReqDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustTakeoutOrderReqDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustByOrderRespDTO;
import com.holderzone.saas.store.dto.trade.resp.adjust.AdjustOrderDetailRespDTO;
import com.holderzone.saas.store.enums.print.TradeModeEnum;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AdjustOrderManage {

    @Autowired
    private DineInOrderClientService dineInOrderClientService;

    @Autowired
    private TakeoutClientService takeoutClientService;

    /**
     * 构建调整单详情
     */
    public AdjustByOrderRespDTO query(AdjustOrderQueryDTO queryDTO) {
        AdjustOrderDetailRespDTO adjustOrder = dineInOrderClientService.queryAdjustOrder(queryDTO);
        // 查询相关订单
        AdjustByOrderItemQuery itemQuery = new AdjustByOrderItemQuery()
                .setOrderGuid(String.valueOf(adjustOrder.getOrderGuid()))
                .setTradeMode(adjustOrder.getTradeMode());
        AdjustByOrderRespDTO respDTO;
        if (Objects.nonNull(adjustOrder.getTradeMode()) && TradeModeEnum.TAKEOUT.getMode().equals(adjustOrder.getTradeMode())) {
            respDTO = takeoutClientService.listOrderItem(itemQuery);
        } else {
            respDTO = dineInOrderClientService.listOrderItem(itemQuery);
        }
        log.info("调整单查询详情-订单查询返回结果:{}", respDTO);
        AdjustByOrderRespDTOBuilder.build(respDTO, adjustOrder);
        return respDTO;
    }


    /**
     * 新增调整单
     */
    @GlobalTransactional(rollbackFor = RuntimeException.class)
    public Long create(AdjustOrderReqDTO reqDTO) {
        // 校验参数
        for (AdjustOrderReqDTO.AdjustOrderItem adjustOrderItem : reqDTO.getOrderItemList()) {
            if (CollectionUtils.isEmpty(adjustOrderItem.getItemList())) {
                throw new BusinessException("参数有误，orderItemGuid: " + adjustOrderItem.getOrderItemGuid());
            }
        }

        if (TradeModeEnum.TAKEOUT.getMode().equals(reqDTO.getTradeMode())) {
            // 外卖调整单编辑是否调整
            List<String> orderItemGuidList = reqDTO.getOrderItemList().stream().map(AdjustOrderReqDTO.AdjustOrderItem::getOrderItemGuid)
                    .collect(Collectors.toList());
            takeoutClientService.adjustOrder(new AdjustTakeoutOrderReqDTO(String.valueOf(reqDTO.getOrderGuid()), orderItemGuidList));
        }
        return dineInOrderClientService.createAdjustOrder(reqDTO);
    }

}
