package com.holderzone.holder.saas.aggregation.weixin.service;


import com.holderzone.saas.store.dto.business.manage.SurchargeLinkDTO;
import com.holderzone.saas.store.dto.order.inside.OrderGuidsDTO;
import com.holderzone.saas.store.dto.trade.OrderDetailPushMqDTO;

import java.util.List;

/**
 * 微信订单 接口
 */
public interface WxStoreTradeOrderService {

    /**
     * 获取订单详情
     */
    OrderDetailPushMqDTO findByAppletOrderGuid(OrderGuidsDTO orderGuidsDTO);

    /**
     * 修改快餐点餐人数
     *
     * @param count 点餐人数
     */
    void updateFastGuestCount(Integer count);

    /**
     * 删除快餐点餐人数
     */
    void removeFastGuestCount(String diningTableGuid, String openId);

    /**
     * 获取快餐点餐人数
     *
     * @param diningTableGuid 桌台guid
     * @param openId          微信openId
     * @return 点餐人数
     */
    Integer getFastGuestCount(String diningTableGuid, String openId);

    /**
     * 查询桌台 有时限的附加费
     */
    List<SurchargeLinkDTO> filterTimeLimitTableSurchargeList(String tableGuid, List<SurchargeLinkDTO> surchargeLinkList);

    /**
     * 查询附加费列表
     *
     * @param orderModel      正餐 or 快餐
     * @param areaGuid        区域guid
     * @param diningTableGuid
     */
    List<SurchargeLinkDTO> querySurchargeList(Integer orderModel, String areaGuid, String diningTableGuid);

    /**
     * 订单附加费缓存 -> 桌台附加费缓存
     */
    void transformSurchargeCache(String orderGuid);

    /**
     * 保存 当前桌台当前人加菜前所需要的附加费明细
     *
     * @param surchargeLinkList 附加费明细
     */
    void saveAddItemBeforeSurcharges(List<SurchargeLinkDTO> surchargeLinkList);

    /**
     * 查询 当前桌台当前人加菜前所需要的附加费明细
     *
     * @return 附加费明细
     */
    List<SurchargeLinkDTO> queryAddItemBeforeSurcharges();

    /**
     * 查询桌台附加费
     */
    List<SurchargeLinkDTO> querySurchargeListByRedis(String tableGuid);

}
