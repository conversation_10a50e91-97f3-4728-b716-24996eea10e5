package com.holderzone.holder.saas.aggregation.merchant.controller.common;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.entity.pojo.FileUploadDTO;
import com.holderzone.holder.saas.aggregation.merchant.exception.FileIllegalException;
import com.holderzone.holder.saas.aggregation.merchant.service.FileUploadService;
import com.holderzone.holder.saas.aggregation.merchant.util.UploadValidateUtil;
import com.holderzone.saas.store.dto.file.FileDeleteDTO;
import com.holderzone.saas.store.enums.PictureEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FileController
 * @date 2018/09/13 10:46
 * @description
 * @program holder-saas-aggregation-merchant
 */
@RestController
@RequestMapping("/file")
@Api(description = "文件接口")
public class FileController {

    private static final Logger logger = LoggerFactory.getLogger(FileController.class);

    private final FileUploadService fileUploadService;

    @Autowired
    public FileController(FileUploadService fileUploadService) {
        this.fileUploadService = fileUploadService;
    }

    @ApiOperation(value = "文件上传接口,文件上传name 必须为file, 不传入type或者为空表示不作处理",
            notes = "单个图片大小不能超过20M，上传成功返回图片的地址，失败返回空，w*h type=1表示1080*1920,大小为20M," +
                    "type=3表示1024*600,大小为20M,type=4表示635*600,大小为20M," +
                    "type=2表示800*800,大小为0.5M,type=5表示1920*1080,大小为20M," +
                    "type=6表示650*690，大小为20M")
    @PostMapping("/upload")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_OTHER, description = "文件上传接口")
    public Result<String> upload(@RequestParam(value = "file") MultipartFile file, @RequestParam(value = "type", required = false)
            Integer type) {
        logger.info("文件上传， fileName={},type={}", file.getOriginalFilename(), type);
        FileUploadDTO fileUploadDTO = null;
        // 仅校验商品图片
        String fileName = file.getOriginalFilename();
        String fileType = fileName != null && fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".") + 1,
                fileName.length()) : null;
        if (UploadValidateUtil.typeNotValidate(fileType)) {
            logger.info("当前文件类型为：{}", fileType);
            throw new FileIllegalException("文件格式必须为 jpg/png/bmp/jpeg/gif 格式！！！");
        }
        if (!UploadValidateUtil.whetherImage(file)) {
            throw new FileIllegalException("请上传正确的图片");
        }
        if (null != type) {
            PictureEnum byType = PictureEnum.getByType(type);//根据type查找图片大小标准
            if (null != byType) {
                Integer height = byType.getHeight();
                Integer weight = byType.getWeight();
                Long size = byType.getSize();
                fileUploadDTO = new FileUploadDTO();
                fileUploadDTO.setHeight(height);
                fileUploadDTO.setWeight(weight);
                fileUploadDTO.setSize(size);
                // 仅修改商品上传图片大小限制
                if (null != size && file.getSize() > size) { // 判断图片是否超过限制
                    throw new FileIllegalException("文件大小超过限制");
                }
            }
        }
        return Result.buildSuccessResult(fileUploadService.upload(fileUploadDTO, file));
    }

    @ApiOperation(value = "文件上传接口,文件上传name 必须为file",
            notes = "单个图片大小不能超过3M，上传成功返回图片的地址，失败返回空")
    @PostMapping("/batch/upload")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_OTHER, description = "文件上传接口,文件上传name 必须为file")
    public Result<List<String>> upload(@RequestParam(value = "file") List<MultipartFile> fileList) {
        logger.info("文件批量上传");
        List<String> urls = fileList.stream()
                .map(file -> fileUploadService.upload(null, file))
                .collect(Collectors.toList());
        return Result.buildSuccessResult(urls);
    }

    @ApiOperation(value = "OSS根据URL删除文件")
    @PostMapping("/delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_OTHER, description = "OSS根据URL删除文件")
    public Result delete(@RequestBody FileDeleteDTO fileDeleteDTO) {
        logger.info("OSS根据URL删除文件,fileUrl:{}", fileDeleteDTO.getFileUrl());
        fileUploadService.deleteFileOnOssByUrl(fileDeleteDTO.getFileUrl());
        return Result.buildEmptySuccess();
    }

}
