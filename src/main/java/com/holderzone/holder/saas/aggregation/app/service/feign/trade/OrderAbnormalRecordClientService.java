package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.pay.AggPayPollingRespDTO;
import com.holderzone.saas.store.dto.pay.AggPayReserveResultDTO;
import com.holderzone.saas.store.dto.pay.SaasPollingDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalListReqDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalRecordReqDTO;
import com.holderzone.saas.store.dto.trade.OrderAbnormalRecordRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = OrderAbnormalRecordClientService.AbnormalOrderFallBack.class)
public interface OrderAbnormalRecordClientService {

    /**
     * 插入异常单
     * @param orderAbnormalRecordReqDTO
     * @return
     */
    @PostMapping("/order_abnormal_record/save_abnormal_record_order")
    Integer saveAbnormalRecordOrder(@RequestBody OrderAbnormalRecordReqDTO orderAbnormalRecordReqDTO);

    /**
     * 异常单列表
     * @param orderAbnormalListReqDTO
     * @return
     */
    @PostMapping("/order_abnormal_record/abnormal_order_list")
    Page<OrderAbnormalRecordRespDTO> listAbnormalOrders(@RequestBody OrderAbnormalListReqDTO orderAbnormalListReqDTO);

    /**
     * 获取支付结果
     * @param saasPollingDTO
     * @return
     */
    @PostMapping("/order_abnormal_record/query_payment_result")
    AggPayPollingRespDTO getPaymentResult(@RequestBody SaasPollingDTO saasPollingDTO);

    /**
     * 取消支付
     * @param saasPollingDTO
     * @return
     */
    @PostMapping("/order_abnormal_record/cancel_payment")
    AggPayReserveResultDTO cancelPayment(@RequestBody SaasPollingDTO saasPollingDTO);

    @Component
    @Slf4j
    class AbnormalOrderFallBack implements FallbackFactory<OrderAbnormalRecordClientService> {

        @Override
        public OrderAbnormalRecordClientService create(Throwable throwable) {
            return new OrderAbnormalRecordClientService() {
                @Override
                public Integer saveAbnormalRecordOrder(OrderAbnormalRecordReqDTO orderAbnormalRecordReqDTO) {
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public Page<OrderAbnormalRecordRespDTO> listAbnormalOrders(OrderAbnormalListReqDTO orderAbnormalListReqDTO) {
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public AggPayPollingRespDTO getPaymentResult(SaasPollingDTO saasPollingDTO) {
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public AggPayReserveResultDTO cancelPayment(SaasPollingDTO saasPollingDTO) {
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
