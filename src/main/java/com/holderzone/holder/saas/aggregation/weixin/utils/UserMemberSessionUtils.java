package com.holderzone.holder.saas.aggregation.weixin.utils;

import com.holderzone.framework.dynamic.datasource.starter.utils.JacksonUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.constant.RedisConstants;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.ItemClientService;
import com.holderzone.holder.saas.aggregation.weixin.utils.map.MemberCardItemMAP;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListOwned;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberCardCacheDTO;
import com.holderzone.saas.store.dto.weixin.deal.UserMemberSessionDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

//TODO 重复的类starter
@Component
@Slf4j
public class UserMemberSessionUtils {

	private final RedisUtils redisUtils;

	private final ItemClientService itemClientService;
	@Resource
	ExecutorService  executorService;

	@Autowired
	public UserMemberSessionUtils(RedisUtils redisUtils, ItemClientService itemClientService) {
		this.redisUtils = redisUtils;
		this.itemClientService = itemClientService;
	}

	/**
	 * @param cardList 会员卡列表
	 */
	public List<UserMemberCardCacheDTO> memberCardUck(List<ResponseMemberCardListOwned> cardList, UserMemberSessionDTO userMemberSession, Boolean enableMemberPrice) {
		String openId = WeixinUserThreadLocal.getOpenId();
		log.info("查询会员卡时openId为:{}", WeixinUserThreadLocal.get());
		if (!ObjectUtils.isEmpty(cardList) && !StringUtils.isEmpty(openId)) {
			// 排除冻结卡
			cardList.removeIf(e -> Objects.nonNull(e.getIsFreeze()) && !e.getIsFreeze());
			List<UserMemberCardCacheDTO> userMemberCardCacheList = MemberCardItemMAP.INSTANCE.toUserMemberCacheListNew(cardList);
			log.info("转换出的会员卡数据{}", JacksonUtils.writeValueAsString(userMemberCardCacheList));
			// 重新设置 缓存中的会员卡id
			reSetMemberSessionCardInfo(userMemberCardCacheList, userMemberSession);
			userMemberSession.setIsLogin(true);
			userMemberSession.setMemberIntegral(1);
			userMemberSession.setWhetherSupportMemberPrice(enableMemberPrice);
			addUserMemberSession(userMemberSession);
			addCardList(WeixinUserThreadLocal.getStoreGuid(), openId, userMemberCardCacheList);
			return userMemberCardCacheList;
		}else{
			userMemberSession.setMemberInfoCardGuid("-1");
			userMemberSession.setWhetherSupportMemberPrice(false);
			addUserMemberSession(userMemberSession);
			return Collections.emptyList();
		}
	}

	/**
	 * 重新设置 缓存中的会员卡id
	 */
	public void reSetMemberSessionCardInfo(List<UserMemberCardCacheDTO> cardList, UserMemberSessionDTO userMemberSession) {
		if (CollectionUtils.isEmpty(cardList) || !StringUtils.isEmpty(userMemberSession.getMemberInfoCardGuid())) {
			return;
		}
		UserMemberCardCacheDTO defaultCard = cardList.stream().filter(e -> e.getCardType() == 1).findFirst().orElse(cardList.get(0));
		// 查询上次使用会员卡
		Object memberCardGuidObj = redisUtils.get(String.format(RedisConstants.MEMBER_LAST_CARD_GUID, WeixinUserThreadLocal.get().getOperSubjectGuid(),
				WeixinUserThreadLocal.getOpenId()));
		if (Objects.nonNull(memberCardGuidObj) && !StringUtils.isEmpty(memberCardGuidObj.toString())) {
			defaultCard = cardList.stream()
					.filter(e -> e.getMemberInfoCardGuid().equals(memberCardGuidObj.toString()))
					.findFirst()
					.orElse(null);
		}
		log.info("默认选择卡信息:{}", JacksonUtils.writeValueAsString(defaultCard));
		if (Objects.nonNull(defaultCard) && (Objects.isNull(defaultCard.getIsFreeze()) || defaultCard.getIsFreeze())) {
			userMemberSession.setMemberInfoCardGuid(defaultCard.getMemberInfoCardGuid());
			userMemberSession.setCardGuid(defaultCard.getCardGuid());
		} else {
			userMemberSession.setMemberInfoCardGuid(null);
			userMemberSession.setCardGuid(null);
		}
	}

	public void addUserMemberSession(UserMemberSessionDTO userMemberSessionDTO) {
		log.info("更新缓存中的addUserMemberSession:{}", JacksonUtil.writeValueAsString(userMemberSessionDTO));
		redisUtils.setEx(CacheName.USER_MEMBER_SESSION+":"+userMemberSessionDTO.getOpenId(),userMemberSessionDTO,10, TimeUnit.HOURS);
	}

	public UserMemberSessionDTO getUserMemberSession(String openId) {
		UserMemberSessionDTO userMemberSessionDTO = Optional.ofNullable((UserMemberSessionDTO)
				redisUtils.get(CacheName.USER_MEMBER_SESSION+":"+openId)).orElse(new UserMemberSessionDTO());
		userMemberSessionDTO.setOpenId(openId);
		return  userMemberSessionDTO;
	}

	static String orderCouponCheckedKey = "coupon:";
    /*public Set<String> getCheckedCoupon(String openId,String orderRecordGuid) {
	    String key = orderCouponCheckedKey+orderRecordGuid+":"+openId;
        Object value = redisUtils.get(key);
        if(value==null){
        	return Collections.emptySet();
		}
		return (HashSet)value;
    }*/

    public void saveCheckedCoupons(String openId,String orderRecordGuid,Set<String> couponCodes ) {
		String key =orderCouponCheckedKey+orderRecordGuid+":"+openId;
    	if(CollectionUtils.isEmpty(couponCodes)){
    		redisUtils.delete(key);
			return;
		}

		redisUtils.setEx(key,couponCodes,5,TimeUnit.HOURS);
    }


	public void addCardList(String storeGuid,String openId, List<UserMemberCardCacheDTO> userMemberSessionCardItemDTOS) {
    	try {
			redisUtils.setEx(CacheName.USER_MEMBER_CARD+":"+storeGuid+":"+openId,userMemberSessionCardItemDTOS,1, TimeUnit.MINUTES);
		}catch (Exception e){
    		log.error("微信会员卡Redis数据缓存失败",e);
		}
	}

	public List<UserMemberCardCacheDTO> getCardList(String storeGuid,String openId) {
		return (List<UserMemberCardCacheDTO>) redisUtils.get(CacheName.USER_MEMBER_CARD+":"+storeGuid+":"+openId);
	}

	public void delCardList(String storeGuid, String openId) {
		log.info("支付后删除会员卡:门店id:{},openId:{}",storeGuid,openId);
		redisUtils.delete(CacheName.USER_MEMBER_CARD+":"+storeGuid+":"+openId);
	}

	public void delTableCardList(String storeGuid, Set<String> openIDs) {
		if (!CollectionUtils.isEmpty(openIDs)) {
			List<String> collect = openIDs.stream().map(x -> CacheName.USER_MEMBER_CARD + ":" + storeGuid + ":" + x)
					.collect(Collectors.toList());
			redisUtils.delete(collect);
		}
	}

	public void delAllUserSession(Set<String> openIDS) {
		log.info("删除session:{}", openIDS);
		if (!CollectionUtils.isEmpty(openIDS)) {
			List<String> collect = openIDS.stream().map(x -> CacheName.USER_MEMBER_SESSION + ":" + x).collect(Collectors.toList());
			log.info("删除session集合:{}",collect);
			redisUtils.delete(collect);
		}
	}


	public void expireUserSession(Set<String> openIDS) {
		log.info("续时session:{}", openIDS);
		if (CollectionUtils.isEmpty(openIDS)) {
			return;
		}
		List<String> collect = openIDS.stream().map(x -> CacheName.USER_MEMBER_SESSION + ":" + x).collect(Collectors.toList());
		log.info("删除session集合:{}", collect);
		for (String redisKey : collect) {
			redisUtils.expire(redisKey, 15, TimeUnit.MINUTES);
		}
	}



}
