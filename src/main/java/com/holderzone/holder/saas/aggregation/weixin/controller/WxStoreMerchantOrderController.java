package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreMerchantOrderClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestQueryMemberInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberInfo;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOperationDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreMerchantOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxOperateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreMerchantOrderRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WxStoreMerchantOrderController
 * @date 2019/4/10
 */
@RestController
@RequestMapping(value = "/wx_store_merchant_order")
@Slf4j
@Api("微信门店接单")
public class WxStoreMerchantOrderController {

    private final WxStoreMerchantOrderClientService wxStoreMerchantOrderClientService;

//	@Resource
//	private WxHsmMemberBasicService wxHsmMemberBasicService;

    @Resource
    private HsaBaseClientService hsaBaseClientService;

    public WxStoreMerchantOrderController(WxStoreMerchantOrderClientService wxStoreMerchantOrderClientService) {
        this.wxStoreMerchantOrderClientService = wxStoreMerchantOrderClientService;
    }

    @ApiOperation("获取当前门店所有订单")
    @PostMapping("/get_pend")
    public Result<WxStoreMerchantOrderRespDTO> getWxStoreMerchantOrderResp(@RequestBody WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO) {
        log.info("获取当前门店所有待处理订单入参wxStoreMerchantOrderReqDTO:{}", wxStoreMerchantOrderReqDTO);
        return Result.buildSuccessResult(wxStoreMerchantOrderClientService.getWxStoreMerchantOrderResp(wxStoreMerchantOrderReqDTO));
    }

    @ApiOperation("获取订单详情")
    @PostMapping("/get_detail_pend")
    public Result<WxStoreMerchantOrderDTO> getDetailPend(String guid) {
        log.info("获取订单详情请求入参，guid:{}", guid);
        WxStoreMerchantOrderReqDTO wxStoreMerchantOrderReqDTO = new WxStoreMerchantOrderReqDTO();
        wxStoreMerchantOrderReqDTO.setGuid(guid);
        WxStoreMerchantOrderDTO detailPend = wxStoreMerchantOrderClientService.getDetailPend(wxStoreMerchantOrderReqDTO);
        if (detailPend == null) {
            return Result.buildSuccessResult(detailPend);
        }
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        String openId = detailPend.getOpenId();
        if (StringUtils.isEmpty(enterpriseGuid) || StringUtils.isEmpty(openId)) {
            return Result.buildSuccessResult(detailPend);
        }
        ResponseMemberInfo memberInfo = null;
        try {
            RequestQueryMemberInfo requestQueryMemberInfo = new RequestQueryMemberInfo();
            requestQueryMemberInfo.setOpenId(openId);
            memberInfo = hsaBaseClientService.getMemberInfo(requestQueryMemberInfo).getData();
            log.info("查询到会员信息:{}", memberInfo);
        } catch (Exception e) {
            log.error("查询会员失败:{}", e);
        }
        if (memberInfo != null) {
            String phoneNum = memberInfo.getPhoneNum();
            detailPend.setPhoneNum(phoneNum);
        }
        return Result.buildSuccessResult(detailPend);
    }

    @ApiOperation("商户处理订单，商户接单")
    @PostMapping(value = "/operate")
    public Result<WxStoreMerchantOperationDTO> operationMerchantOrder(@RequestBody WxOperateReqDTO wxOperateReqDTO) {
        log.info("获取当前门店所有待处理订单入参wxOperateReqDTO:{}", wxOperateReqDTO);
        WxStoreMerchantOperationDTO wxStoreMerchantOperationDTO = wxStoreMerchantOrderClientService.operationMerchantOrder(wxOperateReqDTO);
        return Result.buildSuccessResult(wxStoreMerchantOperationDTO);
    }
}
