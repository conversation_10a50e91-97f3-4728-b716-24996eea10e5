package com.holderzone.holder.saas.aggregation.merchant.service;

import com.holderzone.saas.store.dto.report.openapi.MemberFundingDetailLimitOpenRespDTO;
import com.holderzone.saas.store.dto.report.openapi.MemberFundingDetailQueryDTO;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailLimitOpenRespDTO;
import com.holderzone.saas.store.dto.report.openapi.SaleDetailQueryDTO;

public interface ReportService {

    SaleDetailLimitOpenRespDTO<?> querySaleDetail(SaleDetailQueryDTO query);

    MemberFundingDetailLimitOpenRespDTO<?> queryMemberFundingDetail(MemberFundingDetailQueryDTO query);
}
