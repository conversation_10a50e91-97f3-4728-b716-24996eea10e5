package com.holderzone.holder.saas.aggregation.merchant.service.rpc;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.trade.JHConfigDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeDTO;
import com.holderzone.saas.store.dto.trade.PaymentTypeQueryDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeClientService
 * @date 2018/09/12 19:24
 * @description
 * @program holder-saas-aggregation-merchant
 *
 * 弃用 改为{@link com.holderzone.holder.saas.aggregation.merchant.service.rpc.business.PaymentTypeClientService}
 */
/*@Deprecated
@Component
@FeignClient(name = "holder-saas-store-trading-center", fallbackFactory = PaymentTypeClientService.PayTypeFallBack.class)
public interface PaymentTypeClientService {

    @PostMapping("/pay/type/add")
    String addPaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO);

    @PostMapping("/pay/type/update")
    String updatePaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO);

    @PostMapping("/pay/type/sort")
    String sorting(@RequestBody List<PaymentTypeDTO> paymentTypeDTOS);

    @PostMapping("/pay/type/delete")
    String deletePaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO);

    @PostMapping("/pay/type/getAll")
    List<PaymentTypeDTO> getAll(@RequestBody PaymentTypeQueryDTO paymentTypeQueryDTO);

    @PostMapping("/pay/type/config")
    String config(@RequestBody JHConfigDTO jhConfigDTO);

    @PostMapping("/pay/type/detail")
    PaymentTypeDTO getOne(PaymentTypeDTO paymentTypeDTO);

    @Component
    class PayTypeFallBack implements FallbackFactory<PaymentTypeClientService> {

        private static final Logger logger = LoggerFactory.getLogger(PayTypeFallBack.class);

        @Override
        public PaymentTypeClientService create(Throwable throwable) {
            return new PaymentTypeClientService() {
                @Override
                public String addPaymentType(PaymentTypeDTO paymentTypeDTO) {
                    logger.error("新增失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e"+throwable.getMessage());
                }

                @Override
                public PaymentTypeDTO getOne(PaymentTypeDTO paymentTypeDTO) {
                    return null;
                }

                @Override
                public String updatePaymentType(PaymentTypeDTO paymentTypeDTO) {
                    logger.error("updatePaymentType失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e"+throwable.getMessage());
                }

                @Override
                public String sorting(List<PaymentTypeDTO> paymentTypeDTOS) {
                    logger.error("sorting失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e"+throwable.getMessage());
                }

                @Override
                public String deletePaymentType(PaymentTypeDTO paymentTypeDTO) {
                    logger.error("deletePaymentType失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e"+throwable.getMessage());
                }

                @Override
                public List<PaymentTypeDTO> getAll( PaymentTypeQueryDTO paymentTypeQueryDTO) {
                    logger.error("getAll失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e"+throwable.getMessage());
                }

                @Override
                public String config(JHConfigDTO jhConfigDTO) {
                    logger.error("config失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e"+throwable.getMessage());
                }
            };
        }
    }
}*/
