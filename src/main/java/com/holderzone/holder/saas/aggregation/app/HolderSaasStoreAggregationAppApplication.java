package com.holderzone.holder.saas.aggregation.app;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableSwagger2
@EnableApolloConfig
@EnableEurekaClient
@EnableDiscoveryClient
@ComponentScan(basePackages = "com.holderzone")
@EnableFeignClients(basePackages = "com.holderzone")
@SpringBootApplication(exclude={DataSourceAutoConfiguration.class,HibernateJpaAutoConfiguration.class})
public class HolderSaasStoreAggregationAppApplication {

	public static void main(String[] args) {
		SpringApplication.run(HolderSaasStoreAggregationAppApplication.class, args);
	}

}
