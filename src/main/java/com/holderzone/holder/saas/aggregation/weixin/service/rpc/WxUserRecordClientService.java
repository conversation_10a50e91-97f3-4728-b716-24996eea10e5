package com.holderzone.holder.saas.aggregation.weixin.service.rpc;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import com.holderzone.saas.store.dto.weixin.WxUserRecordDTO;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Service
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxUserRecordClientService.FallBackClass.class)
public interface WxUserRecordClientService {

	@GetMapping("/wx_user_record/getUserByOpenId/{openId}")
	WxUserRecordDTO getUserByOpenId(@PathVariable(value = "openId") String openId);

	@PostMapping(value = "/wx_user_record/bind_phone")
	@ApiOperation(value="绑定手机号")
	Boolean bindPhoneNum(@RequestParam("phoneNum") String phoneNum);

	@ApiOperation("根据手机号查询")
	@GetMapping(value = "/wx_user_record/getByPhone")
	WxUserRecordDTO obtainByPhone(@RequestParam("phone") String phone);

	@ApiOperation("根据手机号查询")
	@GetMapping(value = "/wx_user_record/loginWxUserRecordAndFlushCard")
	public void loginWxUserRecordAndFlushCard(
			@RequestParam("enterpriseGuid")String enterpriseGuid,
			@RequestParam("phoneNum")String phoneNum,@RequestParam("openId")String openId);

	@Component
	@Slf4j
	class FallBackClass implements FallbackFactory<WxUserRecordClientService> {
		@Override
		public WxUserRecordClientService create(Throwable throwable) {
			return new WxUserRecordClientService() {
				@Override
				public WxUserRecordDTO getUserByOpenId(String openId) {
					log.info("查询用户失败,openid={}", openId);
					return null;
				}

				@Override
				public Boolean bindPhoneNum(String phoneNum) {
					log.error("绑定手机号失败:{},用户:{}",phoneNum, UserContextUtils.get());
					return false;
				}

				@Override
				public WxUserRecordDTO obtainByPhone(String phone) {
					log.error("根据手机号查询用户失败:{}",phone);
					return null;
				}

				@Override
				public void loginWxUserRecordAndFlushCard(
						@RequestParam("enterpriseGuid")String enterpriseGuid,
						@RequestParam("phoneNum")String phoneNum,@RequestParam("openId")String openId){
					log.error("loginWxUserRecordAndFlushCard error,phoneNum:{},openId:{},enterpriseGuid:{}",phoneNum,openId,enterpriseGuid);
				}
			};
		}
	}
}