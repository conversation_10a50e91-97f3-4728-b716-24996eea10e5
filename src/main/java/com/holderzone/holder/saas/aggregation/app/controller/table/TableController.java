package com.holderzone.holder.saas.aggregation.app.controller.table;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.redisson.sdk.lock.RedissonLockUtil;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.BindUpAccountsService;
import com.holderzone.holder.saas.aggregation.app.service.TableService;
import com.holderzone.holder.saas.aggregation.app.service.feign.table.TableClientService;
import com.holderzone.saas.store.dto.common.BaseDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.OrderTableBillReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.store.table.StoreAndTableDTO;
import com.holderzone.saas.store.dto.table.*;
import com.holderzone.saas.store.reserve.api.dto.TableOrderReserveDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableController
 * @date 2019/01/07 11:21
 * @description
 * @program holder-saas-aggregation-app
 */
@Slf4j
@RestController
@RequestMapping
@Api(tags = "安卓桌台服务Api")
public class TableController {

    private final TableService tableService;

    private final TableClientService tableClientService;

    private final BindUpAccountsService bindUpAccountsService;

    @Autowired
    public TableController(TableService tableService, TableClientService tableClientService, BindUpAccountsService bindUpAccountsService) {
        this.tableService = tableService;
        this.tableClientService = tableClientService;
        this.bindUpAccountsService = bindUpAccountsService;
    }

    @ApiOperation("查询桌台区域")
    @PostMapping("/area/query")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "查询桌台区域", action = OperatorType.SELECT)
    public Result<List<AreaDTO>> queryArea(@RequestBody BaseDTO storeDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询桌台区域列表入参：{}", JacksonUtils.writeValueAsString(storeDTO));
        }
        return Result.buildSuccessResult(tableService.queryArea(storeDTO.getStoreGuid()));
    }

    @ApiOperation("查询桌台列表")
    @PostMapping("/table/query")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "查询桌台列表", action = OperatorType.SELECT)
    public Result<List<TableOrderReserveDTO>> queryTable(@RequestBody TableBasicQueryDTO tableBasicQueryDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询桌位列表入参：{}", JacksonUtils.writeValueAsString(tableBasicQueryDTO));
        }
        Long requestTimestamp = tableBasicQueryDTO.getRequestTimestamp();
        if (requestTimestamp != null) {
            long currentTimeMillis = System.currentTimeMillis();
            log.warn("企业：" + UserContextUtils.getEnterpriseName() + "门店：" + UserContextUtils.getStoreName() +
                    "请求发起时间：" + requestTimestamp + "收到请求时间：" + currentTimeMillis + "请求过程耗时：" + (currentTimeMillis -
                    requestTimestamp));
        }
        StopWatch stopWatch = new StopWatch();
        List<TableOrderReserveDTO> tableOrderReserveDTOS = tableService.queryTable(tableBasicQueryDTO, stopWatch);
        stopWatch.stop();
        log.warn(stopWatch.prettyPrint());
        log.warn("查询桌台列表-tableOrderReserveDTOS={}", JacksonUtils.writeValueAsString(tableOrderReserveDTOS));
        return Result.buildSuccessResult(tableOrderReserveDTOS);
    }

    @ApiOperation("开台接口")
    @PostMapping("/table/open")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "开台接口", action = OperatorType.SELECT)
    public Result<String> openTable(@RequestBody OpenTableDTO openTableDTO) {
        if (log.isInfoEnabled()) {
            log.info("开台入参：{}", JacksonUtils.writeValueAsString(openTableDTO));
        }
        String result = tableService.openTable(openTableDTO);
        return Result.buildSuccessResult(result);
    }

    @ApiOperation("联台")
    @PostMapping("/table/associated/open")
    @EFKOperationLogAop(PLATFORM = Platform.CASHIERSYSTEM, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "联台接口", action = OperatorType.ADD)
    public Result<String> associatedOpen(@RequestBody @Validated OpenAssociatedTableDTO openAssociatedTableDTO) {
        if (log.isInfoEnabled()) {
            log.info("联台入参：{}", JacksonUtils.writeValueAsString(openAssociatedTableDTO));
        }
        String storeGuid = UserContextUtils.getStoreGuid();
        String lockKey = String.format("associated_table_open:%s", storeGuid);
        try {
            boolean lockResult = RedissonLockUtil.tryLock(lockKey, 3, 10);
            if (!lockResult) {
                throw new BusinessException("联台中,请稍后再试");
            }
            return Result.buildSuccessResult(tableService.associatedOpenTable(openAssociatedTableDTO));
        } catch (Throwable throwable) {
            log.error("联台异常 e={}", throwable.getMessage());
            throw new BusinessException(throwable.getMessage());
        } finally {
            try {
                RedissonLockUtil.unlock(lockKey);
            } catch (Exception e) {
                log.info("并发解锁异常 e:", e);
            }
        }
    }

    @ApiOperation("清台接口")
    @PostMapping("/table/clean")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "清台接口", action = OperatorType.SELECT)
    public Result<String> cleanTable(@RequestBody TableStatusChangeDTO tableStatusChangeDTO) {
        if (log.isInfoEnabled()) {
            log.info("开台入参：{}", JacksonUtils.writeValueAsString(tableStatusChangeDTO));
        }
        String result = tableService.cleanTable(tableStatusChangeDTO);
        return Result.buildSuccessResult(result);
    }

    @ApiOperation(value = "转台接口", notes = "转台接口")
    @PostMapping("/table/turn")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "转台接口", action = OperatorType.SELECT)
    public Result<Boolean> turnTale(@RequestBody TurnTableDTO turnTableDTO) {
        if (log.isInfoEnabled()) {
            log.info("转台入参：{}", JacksonUtils.writeValueAsString(turnTableDTO));
        }
        return Result.buildSuccessResult(tableService.turnTable(turnTableDTO));
    }

    @ApiOperation(value = "并台接口", notes = "如果已经并桌的桌台传入已经并桌次数 combineTimes，如果第一次并桌不用传")
    @PostMapping("/table/combine")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "并台接口", action = OperatorType.SELECT)
    public Result<List<String>> combine(@RequestBody TableCombineDTO tableCombineDTO) {
        if (log.isInfoEnabled()) {
            log.info("并台入参：{}", JacksonUtils.writeValueAsString(tableCombineDTO));
        }
        return Result.buildSuccessResult(tableService.combine(tableCombineDTO));
    }

    @ApiOperation(value = "并台校验提示接口")
    @PostMapping("/table/combine_verify")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "并台校验提示接口", action = OperatorType.SELECT)
    public Result<TableCombineVerifyRespDTO> verifyCombine(@RequestBody TableCombineDTO tableCombineDTO) {
        if (log.isInfoEnabled()) {
            log.info("并台校验提示入参：{}", JacksonUtils.writeValueAsString(tableCombineDTO));
        }
        return Result.buildSuccessResult(tableService.verifyCombine(tableCombineDTO));
    }

    @ApiOperation(value = "并台接口", notes = "如果已经并桌的桌台传入已经并桌次数 combineTimes，如果第一次并桌不用传")
    @PostMapping("/table/combine_v2")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "并台接口v2", action = OperatorType.SELECT)
    public Result<TableCombineRespDTO> combineV2(@RequestBody TableCombineDTO tableCombineDTO) {
        if (log.isInfoEnabled()) {
            log.info("并台入参：{}", JacksonUtils.writeValueAsString(tableCombineDTO));
        }
        return Result.buildSuccessResult(tableService.combineV2(tableCombineDTO));
    }

    @ApiOperation("拆台接口")
    @PostMapping("/table/separate")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "拆台接口", action = OperatorType.SELECT)
    public Result<String> separateTable(@RequestBody TableOrderCombineDTO tableOrderCombineDTO) {
        if (log.isInfoEnabled()) {
            log.info("拆台入参：{}", JacksonUtils.writeValueAsString(tableOrderCombineDTO));
        }
        boolean result = tableService.separateTable(tableOrderCombineDTO);
        if (result) {
            return Result.buildSuccessResult("拆台成功");
        }
        return Result.buildOpFailedResult("拆台失败");
    }

    @ApiOperation("关台接口")
    @PostMapping("/table/close")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "关台接口", action = OperatorType.SELECT)
    public Result<String> closeTable(@RequestBody CancelOrderReqDTO cancelOrderReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("关台入参：{}", JacksonUtils.writeValueAsString(cancelOrderReqDTO));
        }
        boolean result = tableService.closeTable(cancelOrderReqDTO);
        if (result) {
            return Result.buildSuccessResult("关台成功");
        }
        return Result.buildOpFailedResult("关台失败");
    }

    @ApiOperation(value = "桌台加锁", notes = "当安卓端选择结账时候，先调用此接口对桌台进行加锁处理，发挥true表示加锁成功")
    @PostMapping("/table/lock")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "桌台加锁", action = OperatorType.SELECT)
//    @PostMapping("/table/try_lock")
    public Result<Boolean> tryLock(@RequestBody TableLockDTO tableLockDTO) {
        if (log.isInfoEnabled()) {
            log.info("桌台加锁入参：{}", JacksonUtils.writeValueAsString(tableLockDTO));
        }
        return Result.buildSuccessResult(tableService.tryLock(tableLockDTO));
    }

    @ApiOperation(value = "桌台解锁", notes = "当从结账页面退出时候，调用此接口，释放桌台锁")
    @PostMapping("/release/lock")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE, description = "桌台解锁", action = OperatorType.SELECT)
//    @PostMapping("/table/release_lock")
    public Result<Boolean> releaseLock(@RequestBody TableLockDTO tableLockDTO) {
        if (log.isInfoEnabled()) {
            log.info("桌台解锁入参：{}", JacksonUtils.writeValueAsString(tableLockDTO));
        }
        return Result.buildSuccessResult(tableService.releaseLock(tableLockDTO));
    }

    /**
     * 根据门店列表查询桌台Guid列表
     *
     * @param singleDataDTO datas必传，门店guid
     * @return 门店和桌台的guid
     */
    @ApiOperation("根据门店列表查询桌台Guid列表")
    @PostMapping("/table/list_table_by_store_guid")
    public List<StoreAndTableDTO> listTableByStoreGuid(@RequestBody SingleDataDTO singleDataDTO) {
        if (log.isInfoEnabled()) {
            log.info("根据门店列表查询桌台列表 入参 singleDataDTO={}", JacksonUtils.writeValueAsString(singleDataDTO));
        }
        return tableClientService.listTableByStoreGuid(singleDataDTO);
    }

    /**
     * 根据订单号查询桌台并开台
     */
    @ApiModelProperty("根据订单号查询桌台并开台")
    @PostMapping("/open_table_by_order_guid")
    public Result<OpenTableOrderDTO> openTableByOrderGuid(@RequestBody OpenTableByOrderDTO dto) {
        if (log.isInfoEnabled()) {
            log.info("根据订单号查询桌台并开台 入参 singleDataDTO={}", JacksonUtils.writeValueAsString(dto));
        }
        return Result.buildSuccessResult(tableClientService.openTableByOrderGuid(dto));
    }

    /**
     * 根据订单号查询桌台并开台
     */
    @ApiModelProperty("根据桌台guid，查桌台关联订单guid")
    @GetMapping("/table/getOrderGuid/{tableGuid}")
    public Result<String> getTableByGuid(@PathVariable("tableGuid") String tableGuid) {
        log.info("根据桌台guid，查桌台关联订单guid:{}", tableGuid);
        return Result.buildSuccessResult(tableClientService.getOrderGuidByTableGuid(tableGuid));
    }

    /**
     * 手动结账关台
     */
    @ApiModelProperty("修改订单关联的桌台")
    @PostMapping("/deal_handle_close")
    public Result<Boolean> dealClose(@RequestBody OrderTableBillReqDTO orderTableBillReqDTO) {
        log.info("修改订单关联的桌台入参={}", JacksonUtils.writeValueAsString(orderTableBillReqDTO));
        throw new BusinessException("订单已结账,如需继续使用正餐手动清台请更新一体机到最新版本");
    }
}
