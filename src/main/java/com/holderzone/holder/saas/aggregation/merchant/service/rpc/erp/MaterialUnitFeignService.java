package com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp;

import com.holderzone.saas.store.dto.erp.MaterialUnitDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/05/05 17:32
 */
@FeignClient("holder-saas-store-erp")
public interface MaterialUnitFeignService {
    /**
     * 添加单位
     */
    @PostMapping("materialUnit")
    void add(@RequestBody MaterialUnitDTO materialUnitDTO);

    /**
     * 获取单位列表
     */
    @GetMapping("materialUnit")
    List<MaterialUnitDTO> findList();
}
