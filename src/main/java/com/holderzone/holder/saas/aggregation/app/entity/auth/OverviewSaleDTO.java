package com.holderzone.holder.saas.aggregation.app.entity.auth;

import com.holderzone.holder.saas.aggregation.app.anno.DataAuthFieldControl;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 营业概况统计
 */
@Data
public class OverviewSaleDTO implements Serializable {

    private static final long serialVersionUID = 4262458180598111837L;

    /**
     * 订单数
     */
    @DataAuthFieldControl("overview_sale_order_count")
    private String orderCount;

    /**
     * 客流量
     */
    @DataAuthFieldControl("overview_sale_guest_count")
    private String guestCount;

    /**
     * 销售额
     */
    @DataAuthFieldControl("overview_sale_amount")
    private String consumerAmount;

    /**
     * 销售总净额
     */
    @DataAuthFieldControl("overview_sale_actually_amount")
    private String gatherAmount;

    /**
     * 销售总净额明细
     */
    @DataAuthFieldControl(isListClear = true)
    private List<AmountItemDTO> gatherItems;

    /**
     * 优惠总额
     */
    @DataAuthFieldControl("overview_sale_discount_amount")
    private String discountAmount;

    /**
     * 优惠总额明细
     */
    @DataAuthFieldControl(isListClear = true)
    private List<DiscountAmountItemDTO> discountItems;

    /**
     * 收银员
     */
    private List<String> checkoutStaffs;

    /**
     * 预计应得金额
     */
    @ApiModelProperty(value = "预计应得金额")
    @DataAuthFieldControl("overview_sale_estimated_amount")
    private String estimatedAmount;

    /**
     * 正餐客流量
     */
    @ApiModelProperty(value = "（正餐）客流量")
    @DataAuthFieldControl("overview_sale_guest_count")
    private String dineInGuestCount;

    /**
     * 正餐上座率
     */
    @ApiModelProperty(value = "（正餐）上座率")
    @DataAuthFieldControl("overview_sale_occupancy_rate_percent")
    private String occupancyRatePercent;

    /**
     * 正餐开台率
     */
    @ApiModelProperty(value = "（正餐）开台率")
    @DataAuthFieldControl("overview_sale_open_table_rate_percent")
    private String openTableRatePercent;

    /**
     * 正餐翻台率
     */
    @ApiModelProperty(value = "（正餐）翻台率")
    @DataAuthFieldControl("overview_sale_flip_table_rate_percent")
    private String flipTableRatePercent;

    /**
     * 正餐平均用餐时间，单位分钟
     */
    @ApiModelProperty(value = "（正餐）平均用餐时长（分钟）")
    @DataAuthFieldControl("overview_sale_avg_dine_in_time")
    private String avgDineInTime;

    @ApiModelProperty("会员充值金额")
    private String rechargeMoney;

    @ApiModelProperty(value = "退款总额")
    private String refundAmount;
}