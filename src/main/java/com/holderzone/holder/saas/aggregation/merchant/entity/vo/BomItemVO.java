package com.holderzone.holder.saas.aggregation.merchant.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;

/**
 * bom商品VO对象
 *
 * <AUTHOR>
 * @date 2019/05/13 14:23
 */
public class BomItemVO {
    private String guid;
    @ApiModelProperty("商品名称（商品规格）")
    private String name;
    @ApiModelProperty("商品规格ID")
    private String skuId;
    @ApiModelProperty("商品图片")
    private String icon;
    @ApiModelProperty("原料种类配比")
    private Integer materialType=0;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getMaterialType() {
        return materialType;
    }

    public void setMaterialType(Integer materialType) {
        this.materialType = materialType;
    }
}
