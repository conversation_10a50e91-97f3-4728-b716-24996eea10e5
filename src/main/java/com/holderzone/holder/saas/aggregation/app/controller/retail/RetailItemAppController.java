package com.holderzone.holder.saas.aggregation.app.controller.retail;


import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.service.feign.retail.RetailItemClientService;
import com.holderzone.saas.store.dto.retail.bill.request.ReturnItemReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.CancelFreeItemReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailAddGoodsReqDTO;
import com.holderzone.saas.store.dto.retail.item.BatchItemReturnOrFreeReqDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderItemAppController
 * @date 2018/09/04 11:26
 * @description app聚合层订单商品接口
 * @program holder-saas-aggregation-app
 */
@RestController
@RequestMapping("/retail_order_item")
@Api(description = "订单商品接口")
public class RetailItemAppController {

    private static final Logger log = LoggerFactory.getLogger(RetailItemAppController.class);

    private final RetailItemClientService orderItemClientService;


    @Autowired
    public RetailItemAppController(RetailItemClientService orderItemClientService) {
        this.orderItemClientService = orderItemClientService;
    }

    @ApiOperation(value = "商品新增接口", notes = "商品新增接口")
    @PostMapping("/add_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "商品新增接口")
    public Result<String> addItem(@RequestBody RetailAddGoodsReqDTO retailAddGoodsReqDTO) {
        String result = orderItemClientService.retailAddItem(retailAddGoodsReqDTO);
        if (!StringUtils.isEmpty(result)) {
            return Result.buildSuccessResult(result);
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "商品赠送接口", notes = "商品赠送接口")
    @PostMapping("/free_item")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "商品新增接口")
    public Result freeItem(@RequestBody BatchItemReturnOrFreeReqDTO batchItemReturnOrFreeReqDTO) {
        BatchItemReturnOrFreeReqDTO result = orderItemClientService.freeItem(batchItemReturnOrFreeReqDTO);
        if (result != null) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "商品取消赠送接口", notes = "商品取消赠送接口")
    @PostMapping("/cancel_free")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "商品新增接口")
    public Result cancelFree(@RequestBody CancelFreeItemReqDTO cancelFreeItemReqDTO) {
        boolean result = orderItemClientService.cancelFree(cancelFreeItemReqDTO);
        if (result) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "退货接口", notes = "批量退货接口")
    @PostMapping("/return_items")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "批量退货接口")
    public Result returnItems(@Validated @RequestBody ReturnItemReqDTO returnItemReqDTO) {

        boolean result = orderItemClientService.returnItems(returnItemReqDTO);
        if (result) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

    @ApiOperation(value = "退单接口", notes = "退单接口")
    @PostMapping("/return_order")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "退单接口")
    public Result returnOrder(@Validated @RequestBody ReturnItemReqDTO returnItemReqDTO){
        boolean result = orderItemClientService.returnOrder(returnItemReqDTO);
        if (result) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constant.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }
}
