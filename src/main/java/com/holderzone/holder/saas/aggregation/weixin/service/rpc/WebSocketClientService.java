package com.holderzone.holder.saas.aggregation.weixin.service.rpc;


import com.holderzone.saas.store.dto.weixin.WxStoreAdvanceConsumerReqDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @className WebSocketClientService
 * @date 2019/3/22
 */
@Deprecated
@Component
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WebSocketClientService.WebSocketFallBack.class)
public interface WebSocketClientService {

	String URI_PREFIX = "/wx_store_socket_session";

	@PostMapping(value = URI_PREFIX + "/create")
	void createSession(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	@PostMapping(value = URI_PREFIX + "/find_session")
	List<WxStoreAdvanceConsumerReqDTO> getWebSocketUser(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	@ApiOperation("获取单个会话")
	@PostMapping(value = URI_PREFIX + "/single_session")
	WxStoreAdvanceConsumerReqDTO getPersonSocketUser(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO);

	@ApiOperation("获取当前桌台所有会话")
	@PostMapping(value =URI_PREFIX+ "/table_user")
	List<WxStoreAdvanceConsumerReqDTO> getWebSocketUser(@RequestBody String tableGuid);

	@ApiOperation("获取单个会话")
	@PostMapping(value =URI_PREFIX+ "/single_user")
	WxStoreAdvanceConsumerReqDTO getPersonSocketUser(@RequestBody String openId);

	@Slf4j
	@Component
	class WebSocketFallBack implements FallbackFactory<WebSocketClientService> {
		@Override
		public WebSocketClientService create(Throwable throwable) {
			return new WebSocketClientService() {

				@Override
				public void createSession(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
					log.error("远程调用失败", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public List<WxStoreAdvanceConsumerReqDTO> getWebSocketUser(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
					log.error("远程调用失败", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public WxStoreAdvanceConsumerReqDTO getPersonSocketUser(WxStoreAdvanceConsumerReqDTO wxStoreAdvanceConsumerReqDTO) {
					log.error("远程调用失败", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public List<WxStoreAdvanceConsumerReqDTO> getWebSocketUser(String tableGuid) {
					log.error("远程调用失败", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

				@Override
				public WxStoreAdvanceConsumerReqDTO getPersonSocketUser(String openId) {
					log.error("远程调用失败", throwable.getMessage());
					throw new RuntimeException(throwable.getMessage());
				}

			};
		}
	}
}
