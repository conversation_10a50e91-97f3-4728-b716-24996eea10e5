package com.holderzone.holder.saas.aggregation.weixin.aop;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.holder.dingding.handler.DingWarningHandler;
import com.holderzone.saas.store.dto.common.BaseDTO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ControllerAspect
 * @date 2019/01/21 18:28
 * @description controller切面
 * @program holder-saas-aggregation-weixin
 */
@Aspect
@Slf4j
@Component
public class ControllerAspect {
    @Resource
    AopLogger aopLogger ;

    @Pointcut("execution(* com.holderzone.holder.saas.aggregation.weixin.controller..*.*(..))")
    public void pointCut() {

    }

    @Deprecated
    @Before("pointCut()")
    public void doBefore(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            for (Object arg : args) {
                if (arg instanceof BaseDTO) {
                    if (UserContextUtils.getEnterpriseGuid() != null) {
                        ((BaseDTO) arg).setEnterpriseGuid(UserContextUtils.getEnterpriseGuid());
                    }
                    if (UserContextUtils.getUserGuid() != null) {
                        ((BaseDTO) arg).setUserGuid(UserContextUtils.getUserGuid());
                    }
                    if (UserContextUtils.getUserName() != null) {
                        ((BaseDTO) arg).setUserName(UserContextUtils.getUserName());
                    }
                    if (UserContextUtils.getUserAccount() != null) {
                        ((BaseDTO) arg).setAccount(UserContextUtils.getUserAccount());
                    }
                    if (UserContextUtils.getStoreGuid() != null) {
                        ((BaseDTO) arg).setStoreGuid(UserContextUtils.getStoreGuid());
                    }
                }
            }
        }
    }

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        return aopLogger.around(point,false);
    }



    public static void main(String[] args) {
        String[] a = null;
        for (String s : a) {
            System.out.println(s);
        }
    }
}
