package com.holderzone.holder.saas.aggregation.app.controller.invoice;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.InvoiceService;
import com.holderzone.saas.store.dto.invoice.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberInvoiceController
 * @description 电子发票接口
 */
@RestController
@RequestMapping("/invoice")
@Api(tags = "电子发票接口")
@Slf4j
public class MemberInvoiceController {

    @Resource
    private InvoiceService invoiceService;

    @ApiOperation(value = "开票校验", notes = "开票校验")
    @PostMapping("/receipt_validate")
    public Result<ResponseVerifyInvoiceDTO> receiptValidate(@RequestBody RequestValidateDTO requestValidateDTO) {
        log.info("开票校验入参：{}", JacksonUtils.writeValueAsString(requestValidateDTO));
        return Result.buildSuccessResult(invoiceService.receiptValidate(requestValidateDTO));
    }

    /**
     * 短信登录
     * @return boolean
     */
    @ApiOperation(value = "短信登录", notes = "短信登录")
    @PostMapping("/note_login")
    Result<Boolean> noteLogin(@RequestBody RequestValidateDTO requestValidateDTO) {
        log.info("短信登录入参：{}", JacksonUtils.writeValueAsString(requestValidateDTO));
        return Result.buildSuccessResult(invoiceService.noteLogin(requestValidateDTO));
    }


    /**
     * 发送短信验证码
     * @return boolean
     */
    @ApiOperation(value = "发送短信验证码", notes = "发送短信验证码")
    @PostMapping("/send_note")
    Result<Boolean> sendNote(@RequestBody RequestValidateDTO requestValidateDTO) {
        log.info("发送短信验证码入参：{}", JacksonUtils.writeValueAsString(requestValidateDTO));
        return Result.buildSuccessResult(invoiceService.noteLogin(requestValidateDTO));
    }

    /**
     * 生成认证二维码
     * @param requestAuthenticationDTO requestAuthenticationDTO
     */
    @ApiOperation(value = "生成认证二维码", notes = "生成认证二维码")
    @PostMapping("/authentication_url")
    Result<ResponseAuthenticationUrlDTO> authenticationUrl(@RequestBody RequestAuthenticationDTO requestAuthenticationDTO) {
        log.info("生成认证二维码入参：{}", JacksonUtils.writeValueAsString(requestAuthenticationDTO));
        return Result.buildSuccessResult(invoiceService.authenticationUrl(requestAuthenticationDTO));
    }

    /**
     * 查询认证结果
     * @param requestAuthenticationDTO requestAuthenticationDTO
     * @return IpassQueryAuthResultDTO
     */
    @ApiOperation(value = "查询认证结果", notes = "查询认证结果")
    @PostMapping("/query_auth_result")
    Result<ResponseQueryAuthDTO> queryAuthResult(@RequestBody RequestAuthenticationDTO requestAuthenticationDTO) {
        log.info("查询认证结果入参：{}", JacksonUtils.writeValueAsString(requestAuthenticationDTO));
        return Result.buildSuccessResult(invoiceService.queryAuthResult(requestAuthenticationDTO));
    }
}
