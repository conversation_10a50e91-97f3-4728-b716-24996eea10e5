package com.holderzone.holder.saas.aggregation.app.service.feign.retail;


import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.retail.HangOrderDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderDetailRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderListReqDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailOrderListRespDTO;
import com.holderzone.saas.store.dto.retail.dinein.RetailRemarkReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DineInOrderClientService
 * @date 2018/09/06 15:02
 * @description 正餐订单远程调用
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-retail", fallbackFactory = RetailOrderClientService.FastFoodFallBack.class)
public interface RetailOrderClientService {


    @PostMapping("/retail_order/update_remark")
    Boolean updateRemark(@RequestBody RetailRemarkReqDTO retailRemarkReqDTO);

    @PostMapping("/retail_order/get_order_detail")
    RetailOrderDetailRespDTO getOrderDetail(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping("/retail_order/order_list")
    Page<RetailOrderListRespDTO> orderList(@RequestBody RetailOrderListReqDTO retailOrderListReqDTO);

    @PostMapping("/retail_order/hang_order")
    String hangOrder(@RequestBody HangOrderDTO hangOrderDTO);

    @PostMapping("/retail_order/hang_order_list")
    Map<String, String> hangOrderList(@RequestBody HangOrderDTO hangOrderDTO);

    @PostMapping("/retail_order/gain_order")
    Boolean gainOrder(@RequestBody HangOrderDTO hangOrderDTO);

    @PostMapping("/retail_order/print_check_out")
    Boolean printCheckOut(SingleDataDTO singleDataDTO);


    @Component
    @Slf4j
    class FastFoodFallBack implements FallbackFactory<RetailOrderClientService> {

        @Override
        public RetailOrderClientService create(Throwable throwable) {
            return new RetailOrderClientService() {

                @Override
                public Boolean updateRemark(RetailRemarkReqDTO retailRemarkReqDTO) {
                    log.error("更新整单备注FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("更新整单备注失败!" + throwable.getMessage());
                }

                @Override
                public RetailOrderDetailRespDTO getOrderDetail(SingleDataDTO singleDataDTO) {
                    log.error("获取订单详情FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("获取订单详情失败!" + throwable.getMessage());
                }

                @Override
                public Page<RetailOrderListRespDTO> orderList(RetailOrderListReqDTO retailOrderListReqDTO) {
                    log.error("订单列表FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("订单列表失败!" + throwable.getMessage());
                }


                @Override
                public String hangOrder(HangOrderDTO hangOrderDTO) {
                    log.error("挂起订单FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("挂起订单失败!" + throwable.getMessage());
                }

                @Override
                public Map<String, String> hangOrderList(HangOrderDTO hangOrderDTO) {
                    log.error("挂起订单列表FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("挂起订单列表失败!" + throwable.getMessage());
                }

                @Override
                public Boolean gainOrder(HangOrderDTO hangOrderDTO) {
                    log.error("删除挂起订单FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("删除挂起订单失败!" + throwable.getMessage());
                }

                @Override
                public Boolean printCheckOut(SingleDataDTO singleDataDTO) {
                    log.error("打印结账单FallBack，throwable={}", throwable.getMessage());
                    throw new BusinessException("打印结账单失败!" + throwable.getMessage());
                }
            };
        }
    }
}
