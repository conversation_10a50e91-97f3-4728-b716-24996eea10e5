package com.holderzone.holder.saas.aggregation.app.entity.auth;

import com.holderzone.holder.saas.aggregation.app.anno.DataAuthFieldControl;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponsePayWayDetail;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 会员消费统计
 */
@Data
public class MemberConsumeSaleDTO implements Serializable {

    private static final long serialVersionUID = 6697051794843925951L;

    /**
     * 消费单数
     */
    @DataAuthFieldControl("member_consume_count")
    private String consumptionNum;

    /**
     * 消费人数
     */
    @DataAuthFieldControl("member_consume_number")
    private String consumptionMemberNum;

    /**
     * 消费金额
     */
    @DataAuthFieldControl("member_consume_amount")
    private String consumptionAmount;

    /**
     * 消费支付方式明细
     */
    @DataAuthFieldControl("member_consume_amount")
    private List<ResponsePayWayDetail> payWayDetailList;

}
