package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member;

import com.holderzone.saas.store.dto.store.store.OrganizeDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberStoreClientService
 * @date 2018/09/29 15:51
 * @description //TODO
 * @program holder-saas-store-member
 */
@Component
@FeignClient(name = "holder-saas-store-deprecated")
public interface MemberStoreClientService {
    @Deprecated
    @GetMapping(value = "store/findStoreListByUserGuid/{userGuid}")
    List<OrganizeDTO> getStoreList(@PathVariable("userGuid") String userGuid);
}
