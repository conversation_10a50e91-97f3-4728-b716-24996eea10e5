package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.market;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.market.SettlementRuleClientService.SettlementRuleClientServiceFallback;
import com.holderzone.holder.saas.member.dto.marketing.response.SettlementRuleDetailsRespDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SettlementRuleClientService
 * @date 2019/05/30 13:57
 * @description 结算规则
 * @program holder-saas-member-account
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = SettlementRuleClientServiceFallback.class)
public interface SettlementRuleClientService {


    /**
     * 获取模板规则详情
     *
     * @return 模板规则
     */
    @RequestMapping(value = "/hsm_settlement_rule/getDetails", produces = "application/json;charset=utf-8")
    SettlementRuleDetailsRespDTO getDetails();

    @Component
    class SettlementRuleClientServiceFallback implements
            FallbackFactory<SettlementRuleClientService> {

        private static final Logger LOGGER = LoggerFactory
                .getLogger(SettlementRuleClientServiceFallback.class);

        @Override
        public SettlementRuleClientService create(Throwable throwable) {
            return () -> {
                LOGGER.error("获取会员详情失败：{}", ThrowableUtils.asString(throwable));
                throw new BusinessException("获取会员详情失败");
            };
        }
    }
}
