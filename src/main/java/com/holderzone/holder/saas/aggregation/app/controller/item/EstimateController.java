package com.holderzone.holder.saas.aggregation.app.controller.item;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.service.feign.item.ItemClientService;
import com.holderzone.holder.saas.aggregation.app.utils.RedisLock;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.req.estimate.EstimateForAndroidReqDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateForAndroidRespDTO;
import com.holderzone.saas.store.dto.item.resp.estimate.EstimateItemRespDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description 估清接口
 * @date 2022/4/14 9:28
 * @className: EstimateController
 */
@Slf4j
@RestController
@Api(tags = "估清接口")
@RequestMapping("/estimate")
public class EstimateController {

    private final ItemClientService itemClientService;

    private final RedisLock redisLock;

    @Autowired
    public EstimateController(ItemClientService itemClientService, RedisLock redisLock) {
        this.itemClientService = itemClientService;
        this.redisLock = redisLock;
    }

    /**
     * 新的一体机估清接口
     * 老估清接口也在使用
     *
     * @param request 入参
     * @return Boolean
     */
    @ApiOperation(value = "一体机设置估清*新")
    @PostMapping("/save_sold_out")
    public Result<Boolean> saveSoldOut(@RequestBody @Valid EstimateForAndroidReqDTO request) {
        log.info("一体机手动设置菜品估清状态*新接口 入参,request={}", JacksonUtils.writeValueAsString(request));
        // 防重复提交
        boolean getLock;
        boolean result = false;
        try {
            // 判断是否获取了锁
            getLock = redisLock.lock(request.getStoreGuid(), JacksonUtils.writeValueAsString(request));
            if (getLock) {
                result = itemClientService.saveSoldOut(request);
            }
        } finally {
            redisLock.delete(JacksonUtils.writeValueAsString(request));
        }
        if (getLock) {
            return Result.buildSuccessResult(result);
        } else {
            return Result.buildOpFailedResult("正在保存中，请稍等...");
        }
    }

    /**
     * 一体机估清商品列表
     *
     * @param request 门店guid
     * @return 一体机估清商品列表
     */
    @ApiOperation(value = "一体机估清商品列表")
    @PostMapping("/list_estimate")
    public Result<EstimateForAndroidRespDTO> listEstimate(@RequestBody SingleDataDTO request) {
        log.info("一体机估清商品列表 入参，request={}", JacksonUtils.writeValueAsString(request));
        EstimateForAndroidRespDTO estimate = itemClientService.listEstimate(request.getStoreGuid());
        if(estimate != null && estimate.getResumeSaleTime().contains(Constant.RESUME_SALES)){
            estimate.setResumeSaleTime(estimate.getResumeSaleTime().replace(Constant.RESUME_SALES, LocaleUtil.getMessage("RESUME_SALES")));
        }
        return Result.buildSuccessResult(estimate);
    }

    /**
     * 批量取消估清
     *
     * @param request 规格Guid列表
     * @return Boolean
     */
    @ApiOperation(value = "批量取消估清")
    @PostMapping("/batch_cancel_estimate")
    public Result<Boolean> batchCancelEstimate(@RequestBody SingleDataDTO request) {
        log.info("批量取消估清 入参,request={}", JacksonUtils.writeValueAsString(request));
        return Result.buildSuccessResult(itemClientService.batchCancelEstimate(request));
    }

    /**
     * 批量停售
     *
     * @param request 规格Guid列表
     * @return Boolean
     */
    @ApiOperation(value = "批量停售")
    @PostMapping("/batch_stop_sell")
    public Result<Boolean> batchStopSell(@RequestBody SingleDataDTO request) {
        log.info("批量停售 入参,request={}", JacksonUtils.writeValueAsString(request));
        return Result.buildSuccessResult(itemClientService.batchStopSell(request));
    }

    /**
     * 根据商品guid查询估清商品详情
     *
     * @param request 商品Guid
     * @return 估清商品详情
     */
    @ApiOperation(value = "根据商品guid查询估清商品详情")
    @PostMapping("/list_estimate_by_item")
    public Result<EstimateItemRespDTO> listEstimateByItem(@RequestBody SingleDataDTO request) {
        log.info("根据商品guid查询估清商品详情 入参,request={}", JacksonUtils.writeValueAsString(request));
        EstimateItemRespDTO respDTO = itemClientService.listEstimateByItem(request);
        if (ObjectUtils.isEmpty(respDTO)) {
            return Result.buildEmptySuccess();
        }
        return Result.buildSuccessResult(respDTO);
    }
}
