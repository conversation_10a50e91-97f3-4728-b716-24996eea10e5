package com.holderzone.holder.saas.aggregation.weixin.entity.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;
import java.time.Period;
import java.util.stream.Stream;

@AllArgsConstructor
@Getter
public enum ReserveWeekEnum {

	MONDAY(1, "周一"),
	TUESDAY(2, "周二"),
	WEDNESDAY(3, "周三"),
	THURSDAY(4, "周四"),
	FRIDAY(5, "周五"),
	SATURDAY(6, "周六"),
	SUNDAY(7, "周日"),

	TODAY(100, "今天"),
	TOMORROW(101, "明天"),
	THE_DAY_AFTER_TOMORROW(102, "后天");

	private int code;

	private String msg;

	public static String getMsgWithCode(int code) {
		return Stream.of(ReserveWeekEnum.values()).filter(x -> x.getCode() == code).findAny().map(ReserveWeekEnum::getMsg).orElse(null);
	}

	public static String getMsgWithTime(LocalDate today, LocalDate time) {
		int days = Period.between(today, time).getDays();
		switch (days) {
			case 0:
				return TODAY.getMsg();
			case 1:
				return TOMORROW.getMsg();
			case 2:
				return THE_DAY_AFTER_TOMORROW.getMsg();
			default:
				return getMsgWithCode(time.getDayOfWeek().getValue());
		}
	}


}
