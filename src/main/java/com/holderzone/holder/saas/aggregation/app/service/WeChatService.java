package com.holderzone.holder.saas.aggregation.app.service;

import com.holderzone.saas.store.dto.member.response.PadLoginMemberRespDTO;
import com.holderzone.saas.store.dto.weixin.auth.WeChatAuthParamDTO;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description 微信接口层
 * @date 2021/9/6 18:13
 * @className: WeChatService
 */
public interface WeChatService {

    /**
     * 获取微信授权参数
     * 不需要参数
     *
     * @return 微信授权所需参数
     */
    WeChatAuthParamDTO getWeChatAuthParam() throws IOException;

    /**
     * 获取微信用户信息
     *
     * @param authCode 授权码
     * @return 登录结果
     */
    PadLoginMemberRespDTO getWechatUserInfo(String authCode) throws IOException;
}
