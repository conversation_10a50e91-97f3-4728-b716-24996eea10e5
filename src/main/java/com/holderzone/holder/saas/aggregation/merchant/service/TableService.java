package com.holderzone.holder.saas.aggregation.merchant.service;

import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.table.TableBatchCreateDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableService
 * @date 2019/01/07 10:17
 * @description
 * @program holder-saas-aggregation-merchant
 */
public interface TableService {

    String add(TableBasicDTO tableBasicDTO);

    String updateTable(TableBasicDTO tableBasicDTO);

    List<String> deleteAll(List<String> guids);

    List<TableBasicDTO> query(TableBasicQueryDTO tableBasicQueryDTO);

    String batchCreateTable(TableBatchCreateDTO tableBatchCreateDTO);

    List<String> delete(List<String> guids);
}
