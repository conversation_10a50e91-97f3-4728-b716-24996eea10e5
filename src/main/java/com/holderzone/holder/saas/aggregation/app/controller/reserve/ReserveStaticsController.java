package com.holderzone.holder.saas.aggregation.app.controller.reserve;

import com.holderzone.framework.response.Result;
import com.holderzone.saas.store.dto.reserve.*;
import com.holderzone.saas.store.reserve.api.ReserveRecordApi;
import com.holderzone.saas.store.reserve.api.ReserveRecordStaticsApi;
import com.holderzone.saas.store.reserve.api.dto.*;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


/**
 * 预定统计
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/reserve")
public class ReserveStaticsController {

    private final ReserveRecordApi reserveRecordApi;

    private final ReserveRecordStaticsApi reserveRecordStaticsApi;

    @ApiOperation("统计信息")
    @GetMapping("/statistics")
    public Result<StatisticsDTO> statistics(PhoneDTO queryDTO) {
        return Result.buildSuccessResult(reserveRecordApi.statistics(queryDTO));
    }

    @ApiOperation("预点餐商品统计")
    @PostMapping("/itemStatistics")
    public Result<ReserveReportTotalDataDTO> itemStatistics(@RequestBody ReserveReportParamDTO paramDTO) {
        return Result.buildSuccessResult(reserveRecordApi.itemCount(paramDTO));
    }

    @ApiOperation("查询预定总待处理订单数")
    @GetMapping("/statistics/commit")
    public Result<ReserveCommitStaticsDTO> commitStatistics() {
        return Result.buildSuccessResult(reserveRecordStaticsApi.commitStatistics());
    }

    @ApiOperation("查询近30天预定待处理订单数")
    @GetMapping("/statistics/by_day/commit")
    public Result<ReserveCommitStaticsDTO> commitStatisticsByDay() {
        return Result.buildSuccessResult(reserveRecordStaticsApi.commitStatisticsByDay());
    }

    @ApiOperation("查询时间范围内待处理订单")
    @PostMapping("/statistics/commit/by_scope")
    public Result<ReserveCommitStaticsDTO> commitStatisticsByScope(@RequestBody CommitStatisticsReqDTO reqDTO) {
        return Result.buildSuccessResult(reserveRecordStaticsApi.commitStatisticsByScope(reqDTO));
    }

}