package com.holderzone.holder.saas.aggregation.merchant.controller.business;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.manage.SurchargeClientService;
import com.holderzone.saas.store.dto.business.manage.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Api(value = "附加费接口")
@RestController
@RequestMapping("/surcharge")
public class SurchargeController {

    private final SurchargeClientService surchargeClientService;

    @Autowired
    public SurchargeController(SurchargeClientService surchargeClientService) {
        this.surchargeClientService = surchargeClientService;
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建附加费")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "创建附加费")
    public Result<String> create(@RequestBody SurchargeCreateDTO surchargeCreateDTO) {
        log.info("创建附加费，入参：{}", JacksonUtils.writeValueAsString(surchargeCreateDTO));
        return Result.buildSuccessResult(surchargeClientService.create(surchargeCreateDTO));
    }

    @PostMapping("/query")
    @ApiOperation(value = "查询附加费")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "查询附加费")
    public Result<SurchargeDTO> query(@RequestBody SurchargeQueryDTO surchargeQueryDTO) {
        log.info("查询附加费，入参：{}", JacksonUtils.writeValueAsString(surchargeQueryDTO));
        return Result.buildSuccessResult(surchargeClientService.query(surchargeQueryDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新附加费")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "更新附加费")
    public Result<Boolean> update(@RequestBody SurchargeUpdateDTO surchargeUpdateDTO) {
        log.info("更新附加费，入参：{}", JacksonUtils.writeValueAsString(surchargeUpdateDTO));
        surchargeClientService.update(surchargeUpdateDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/enable")
    @ApiOperation(value = "启用禁用附加费")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "启用禁用附加费")
    public Result<Void> enable(@RequestBody SurchargeEnableDTO surchargeEnableDTO) {
        log.info("启用禁用附加费，入参：{}", JacksonUtils.writeValueAsString(surchargeEnableDTO));
        surchargeClientService.enable(surchargeEnableDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除附加费")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "删除附加费")
    public Result<Void> delete(@RequestBody SurchargeDeleteDTO surchargeDeleteDTO) {
        log.info("删除附加费，入参：{}", JacksonUtils.writeValueAsString(surchargeDeleteDTO));
        surchargeClientService.delete(surchargeDeleteDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/batch_enable")
    @ApiOperation(value = "批量启用禁用附加费")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "批量启用禁用附加费")
    public Result<Void> batchEnable(@RequestBody SurchargeBatchEnableDTO surchargeBatchEnableDTO) {
        log.info("批量启用禁用附加费，入参：{}", JacksonUtils.writeValueAsString(surchargeBatchEnableDTO));
        surchargeClientService.batchEnable(surchargeBatchEnableDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/batch_delete")
    @ApiOperation(value = "批量删除附加费")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "批量删除附加费")
    public Result<Void> batchDelete(@RequestBody SurchargeBatchDeleteDTO surchargeBatchDeleteDTO) {
        log.info("批量删除附加费，入参：{}", JacksonUtils.writeValueAsString(surchargeBatchDeleteDTO));
        surchargeClientService.batchDelete(surchargeBatchDeleteDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "根据附加费类型查询分页列表")
    @PostMapping("/list_by_type")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_BUSINESS,description = "根据附加费类型查询分页列表")
    public Result<Page<SurchargeDTO>> listByFeeType(@RequestBody SurchargeListDTO surchargeListDTO) {
        log.info("根据附加费类型查询分页列表，入参：{}", JacksonUtils.writeValueAsString(surchargeListDTO));
        return Result.buildSuccessResult(surchargeClientService.listByType(surchargeListDTO));
    }
}
