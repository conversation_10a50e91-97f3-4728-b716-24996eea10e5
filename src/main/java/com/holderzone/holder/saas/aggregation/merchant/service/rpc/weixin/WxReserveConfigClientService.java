package com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.weixin.req.WxReserveConfigDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxReserveConfigClientService
 * @date 2019/12/16 18:57
 * @description
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-store-weixin", fallbackFactory = WxReserveConfigClientService.WxReserveConfigFallBack.class)
public interface WxReserveConfigClientService {
    @PostMapping("/wx_reserve/list_config")
    Page<WxReserveConfigDTO> listConfig(@RequestBody WxStorePageReqDTO wxStorePageReqDTO);

    @PostMapping("/wx_reserve/update_config")
    Boolean updateConfig(@RequestBody WxReserveConfigDTO wxReserveConfigDTO);

    @PostMapping("/wx_reserve/get_config")
    WxReserveConfigDTO getConfig(@RequestBody SingleDataDTO singleDataDTO);


    @Component
    @Slf4j
    class WxReserveConfigFallBack implements FallbackFactory<WxReserveConfigClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public WxReserveConfigClientService create(Throwable throwable) {
            return new WxReserveConfigClientService() {

                @Override
                public Page<WxReserveConfigDTO> listConfig(WxStorePageReqDTO wxStorePageReqDTO) {
                    log.error(HYSTRIX_PATTERN, "微信预定：listConfig",
                            JacksonUtils.writeValueAsString(wxStorePageReqDTO), throwable);
                    throw new RuntimeException("微信预定配置异常");
                }

                @Override
                public Boolean updateConfig(WxReserveConfigDTO wxReserveConfigDTO) {
                    log.error(HYSTRIX_PATTERN, "微信预定：updateConfig",
                            JacksonUtils.writeValueAsString(wxReserveConfigDTO), throwable);
                    throw new RuntimeException("微信预定配置异常");
                }

                @Override
                public WxReserveConfigDTO getConfig(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "微信预定：getConfig",
                            JacksonUtils.writeValueAsString(singleDataDTO), throwable);
                    throw new RuntimeException("微信预定配置异常");
                }
            };
        }
    }
}
