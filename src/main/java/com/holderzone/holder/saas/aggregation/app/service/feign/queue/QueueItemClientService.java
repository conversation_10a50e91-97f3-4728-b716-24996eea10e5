package com.holderzone.holder.saas.aggregation.app.service.feign.queue;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.order.request.dinein.CreateDineInOrderReqDTO;
import com.holderzone.saas.store.dto.queue.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className QueueItemController
 * @date 2019/03/27 17:06
 * @description //TODO
 * @program holder-saas-store-queue
 */
@FeignClient(name = "holder-saas-store-queue", fallbackFactory = QueueItemClientService.ServiceFallBack.class)
@Api("排队操作")
public interface QueueItemClientService {

    @PostMapping("/queue/item/inQueue")
    @ApiOperation("取号")
    HolderQueueItemDetailDTO inQueue(@Valid @RequestBody HolderQueueItemDTO dto);

    @PostMapping("/queue/item/call")
    @ApiOperation("叫号")
    HolderQueueItemDetailDTO call(@RequestBody ItemGuidDTO dto);

    @PostMapping("/queue/item/pass")
    @ApiOperation("过号")
    HolderQueueItemDetailDTO pass(@RequestBody ItemGuidDTO dto);

    @PostMapping("/queue/item/confirm")
    @ApiOperation("就餐")
    HolderQueueItemDetailDTO confirm(@RequestBody ItemGuidDTO dto);

    @PostMapping("/queue/item/confirmAndTable")
    TableQueueItemDetailDTO confirmAndTable(@RequestBody TableConfirmDTO dto);

    @PostMapping("/queue/item/recover")
    @ApiOperation("撤销")
    HolderQueueItemDetailDTO recover(@RequestBody ItemGuidDTO dto);

    @PostMapping("/queue/records")
    @ApiOperation("历史记录")
     List<HolderQueueItemDetailDTO> records();

    @PostMapping("/queue/records/page")
    @ApiOperation("分页历史记录")
    public Page<HolderQueueItemDetailDTO> page(@RequestBody Page page);

    @Component
    class ServiceFallBack implements FallbackFactory<QueueItemClientService> {
        private static final Logger logger = LoggerFactory.getLogger(QueueItemClientService.class);
        @Override
        public QueueItemClientService create(Throwable throwable) {
            return new QueueItemClientService() {
                @Override
                public Page<HolderQueueItemDetailDTO> page(Page page) {
                    logger.error("分页历史记录异常 ，e={}", throwable);
                    throw new BusinessException("分页历史记录异常!!" + throwable.getMessage());
                }

                @Override
                public HolderQueueItemDetailDTO inQueue(@Valid HolderQueueItemDTO dto) {
                    logger.error("入队异常 ，e={}", throwable);
                    throw new BusinessException("入队异常!!" + throwable.getMessage());
                }

                @Override
                public HolderQueueItemDetailDTO call(ItemGuidDTO dto) {
                    logger.error("叫号异常 ，e={}", throwable);
                    throw new BusinessException("叫号异常!!" + throwable.getMessage());
                }

                @Override
                public TableQueueItemDetailDTO confirmAndTable(TableConfirmDTO dto) {
                    logger.error("就餐并开台异常 ，e={}", throwable);
                    throw new BusinessException("就餐并开台异常!!" + throwable.getMessage());
                }

                @Override
                public HolderQueueItemDetailDTO pass(ItemGuidDTO dto) {
                    logger.error("过号异常 ，e={}", throwable);
                    throw new BusinessException("过号异常!!" + throwable.getMessage());
                }

                @Override
                public HolderQueueItemDetailDTO confirm(ItemGuidDTO dto) {
                    logger.error("确认就餐异常 ，e={}", throwable);
                    throw new BusinessException("确认就餐异常!!" + throwable.getMessage());
                }

                @Override
                public HolderQueueItemDetailDTO recover(ItemGuidDTO dto) {
                    logger.error("恢复异常 ，e={}", throwable);
                    throw new BusinessException("恢复异常!!" + throwable.getMessage());
                }

                @Override
                public List<HolderQueueItemDetailDTO> records() {
                    logger.error("查询历史记录异常 ，e={}", throwable);
                    throw new BusinessException("查询历史记录异常!!" + throwable.getMessage());
                }
            };
        }
    }
}