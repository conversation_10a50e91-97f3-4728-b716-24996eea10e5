package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.entity.auth.*;
import com.holderzone.holder.saas.aggregation.app.service.BusinessDailyService;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;


/**
 * 营业日报接口
 * 商品销售报表
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/business_daily")
@Slf4j
public class GoodsBusinessController {

    private final BusinessDailyService businessDailyService;

    /**
     * old
     */
    @ApiOperation(value = "分类销售统计", notes = "分类销售统计")
    @PostMapping("classify")
    public Result<List<ItemRespDTO>> classify(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("分类销售统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        return Result.buildSuccessResult(businessDailyService.classify(request));
    }

    /**
     * new 受权限控制
     */
    @ApiOperation(value = "分类销售统计", notes = "分类销售统计")
    @PostMapping("/classify_sale")
    public Result<List<CategorySaleDTO>> classifySale(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("分类销售统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        return Result.buildSuccessResult(businessDailyService.classifySale(request));
    }

    /**
     * old
     */
    @ApiOperation(value = "商品销售统计", notes = "商品销售统计")
    @PostMapping("goods")
    public Result<List<ItemRespDTO>> goods(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("商品销售统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        return Result.buildSuccessResult(businessDailyService.goods(request));
    }

    /**
     * new 受权限控制
     */
    @ApiOperation(value = "商品销售统计", notes = "商品销售统计")
    @PostMapping("/goods_sale")
    public Result<List<GoodsSaleDTO>> goodsSale(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("商品销售统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        return Result.buildSuccessResult(businessDailyService.goodsSale(request));
    }

    /**
     * old
     */
    @ApiOperation(value = "属性销售统计", notes = "属性销售统计")
    @PostMapping("attr")
    public Result<PropStatsDTO> attr(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("属性销售统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        return Result.buildSuccessResult(businessDailyService.attr(request));
    }

    /**
     * new 受权限控制
     */
    @ApiOperation(value = "属性销售统计", notes = "属性销售统计")
    @PostMapping("/attr_sale")
    public Result<PropStatsSaleDTO> attrSale(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("属性销售统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        return Result.buildSuccessResult(businessDailyService.attrSale(request));
    }

    /**
     * old
     */
    @ApiOperation(value = "退菜统计", notes = "退菜统计")
    @PostMapping("return_vegetables")
    public Result<List<ItemRespDTO>> returnVegetables(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("退菜统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        return Result.buildSuccessResult(businessDailyService.returnVegetables(request));
    }

    /**
     * new 受权限控制
     */
    @ApiOperation(value = "退菜统计", notes = "退菜统计")
    @PostMapping("/return_vegetables_sale")
    public Result<List<ReturnSaleDTO>> returnVegetablesSale(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("退菜统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        return Result.buildSuccessResult(businessDailyService.returnVegetablesSale(request));
    }

    /**
     * old
     */
    @ApiOperation(value = "赠菜统计", notes = "赠菜统计")
    @PostMapping("dish_giving")
    public Result<List<ItemRespDTO>> dishGiving(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("赠菜统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        return Result.buildSuccessResult(businessDailyService.dishGiving(request));
    }

    /**
     * new 受权限控制
     */
    @ApiOperation(value = "赠菜统计", notes = "赠菜统计")
    @PostMapping("/dish_giving_sale")
    public Result<List<GiftSaleDTO>> dishGivingSale(@RequestBody @Valid DailyReqDTO request) {
        if (log.isInfoEnabled()) {
            log.info("赠菜统计入参：{}", JacksonUtils.writeValueAsString(request));
        }
        return Result.buildSuccessResult(businessDailyService.dishGivingSale(request));
    }
}