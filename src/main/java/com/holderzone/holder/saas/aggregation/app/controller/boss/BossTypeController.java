package com.holderzone.holder.saas.aggregation.app.controller.boss;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.item.ItemClientService;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.TypeReqDTO;
import com.holderzone.saas.store.dto.item.resp.TypeWebRespDTO;
import com.holderzone.saas.store.enums.item.ModuleEntranceEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 老板助手-商品分类接口
 * @date 2022/8/27 14:53
 * @className: BossTypeController
 */
@Slf4j
@RestController
@AllArgsConstructor
@Api(tags = "老板助手-商品分类接口")
@RequestMapping("/boss/type")
public class BossTypeController {

    private final ItemClientService itemClientService;

    /**
     * 老板助手-门店查询分类接口
     *
     * @param itemSingleDTO data字段内容为门店GUID
     * @return 分类
     */
    @ApiOperation(value = "老板助手-门店查询分类接口", notes = "data字段内容为门店GUID")
    @PostMapping("/query_store_type")
    public Result<List<TypeWebRespDTO>> queryType(@RequestBody @Valid ItemSingleDTO itemSingleDTO) {
        log.info("老板助手-门店查询分类接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(ModuleEntranceEnum.STORE.code());
        return Result.buildSuccessResult(itemClientService.queryType(itemSingleDTO));
    }

    /**
     * 老板助手-保存分类接口
     *
     * @param typeReqDTO 入参
     * @return Void
     */
    @ApiOperation(value = "老板助手-保存分类接口")
    @PostMapping("/save")
    public Result<Void> save(@RequestBody @Valid TypeReqDTO typeReqDTO) {
        log.info("老板助手-保存分类接口入参,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTO));
        typeReqDTO.setFrom(0);
        Integer num = itemClientService.save(typeReqDTO);
        if (num == 1) {
            return Result.buildEmptySuccess();
        } else {
            return Result.buildOpFailedResult("新增分类失败");
        }
    }

    /**
     * 老板助手-修改分类接口
     *
     * @param typeReqDTO 入参
     * @return Void
     */
    @ApiOperation(value = "老板助手-修改分类接口")
    @PostMapping("/update")
    public Result<Void> updateFromStore(@RequestBody @Valid TypeReqDTO typeReqDTO) {
        log.info("老板助手-修改分类接口入参,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTO));
        typeReqDTO.setFrom(0);
        Integer update = itemClientService.update(typeReqDTO);
        if (update != 1) {
            return Result.buildOpFailedResult("分类修改失败");
        } else {
            return Result.buildEmptySuccess();
        }
    }

    /**
     * 老板助手-删除分类接口
     *
     * @param itemSingleDTO 分类guid
     * @return Void
     */
    @ApiOperation(value = "老板助手-删除分类接口")
    @PostMapping("/delete")
    public Result<Void> deleteFromStore(@RequestBody ItemSingleDTO itemSingleDTO) {
        log.info("老板助手-删除分类接口入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(0);
        Integer delete = itemClientService.delete(itemSingleDTO);
        if (delete != 1) {
            return Result.buildOpFailedResult("分类删除失败");
        } else {
            return Result.buildEmptySuccess();
        }
    }

    /**
     * 批量修改分类顺序
     *
     * @param typeReqDTOList 里面只有sort和typeGuid
     * @return 1
     */
    @ApiOperation(value = "老板助手-批量修改分类顺序")
    @PostMapping("/batch_modify_sort")
    public Result<Integer> batchModifySort(@RequestBody List<TypeReqDTO> typeReqDTOList) {
        log.info("老板助手-批量修改分类顺序入参,typeReqDTO={}", JacksonUtils.writeValueAsString(typeReqDTOList));
        return Result.buildSuccessResult(itemClientService.batchModifySort(typeReqDTOList));
    }
}
