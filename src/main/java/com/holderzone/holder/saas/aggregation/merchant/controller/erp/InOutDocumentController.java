package com.holderzone.holder.saas.aggregation.merchant.controller.erp;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.entity.dto.InOutDocumentExportDTO;
import com.holderzone.holder.saas.aggregation.merchant.entity.dto.InOutDocumentFlowDetailExportDTO;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.InOutDocumentEnum;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.InOutDocumentFeignService;
import com.holderzone.holder.saas.aggregation.merchant.util.DateUtil;
import com.holderzone.holder.saas.aggregation.merchant.util.ExcelUtil;
import com.holderzone.holder.saas.aggregation.merchant.util.MaterialExcelUtil;
import com.holderzone.saas.store.dto.erp.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/04/29 上午 11:00
 * @description
 */
@Api(tags = "出入库单据")
@RestController
@RequestMapping("/inOutDocument")
public class InOutDocumentController {

    private static final Logger log = LoggerFactory.getLogger(InOutDocumentController.class);

    @Autowired
    private InOutDocumentFeignService inOutDocumentService;

    @ApiOperation("插入或者更新入库单据及其明细")
    @PostMapping("/insertOrUpdate")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "插入或者更新入库单据及其明细")
    public Result<String> insertOrUpdateInOutDocument(@RequestBody InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        log.info("插入或者更新入库单据及其明细入参：{}", JacksonUtils.writeValueAsString(inOutDocumentDTO));
        return Result.buildSuccessResult(inOutDocumentService.insertOrUpdateInOutDocument(inOutDocumentDTO));
    }

    @ApiOperation("查询出入库单据中的物料信息(新添物料使用)")
    @PostMapping("/selectMaterialListForAdd")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "插入或者更新入库单据及其明细")
    public Result<List<InOutDocumentDetailSelectDTO>> selectMaterialListForAdd(@RequestBody InOutDocumentDetailQueryDTO materialQueryDTO) {
        log.info("查询出入库单据中的物料信息(新添物料使用)入参: {}", JacksonUtils.writeValueAsString(materialQueryDTO));
        return Result.buildSuccessResult(inOutDocumentService.selectMaterialListForAdd(materialQueryDTO));
    }

    @ApiOperation("查询对应入库单的退货数量小于入库数量的物料明细(新增退货出库单并有关联单据时使用)")
    @PostMapping("/selectMaterialListForReturn")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询对应入库单的退货数量小于入库数量的物料明细(新增退货出库单并有关联单据时使用)",action = OperatorType.SELECT)
    public Result<List<InOutDocumentDetailSelectDTO>> selectMaterialListForReturn(@RequestParam("contactDocumentGuid") String contactDocumentGuid) {
        log.info("查询对应入库单的退货数量小于入库数量的物料明细(新增退货出库单时使用)入参：{}", contactDocumentGuid);
        return Result.buildSuccessResult(inOutDocumentService.selectMaterialListForReturn(contactDocumentGuid));
    }

    @ApiOperation("提交出入库单")
    @PostMapping("/submitInOutDocument")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "提交出入库单",action = OperatorType.SELECT)
    public Result submitInOutDocument(@RequestBody InOutDocumentAddOrUpdateDTO inOutDocumentDTO) {
        log.info("提交出入库单据入参： {}", JacksonUtils.writeValueAsString(inOutDocumentDTO));
        inOutDocumentService.submitInOutDocument(inOutDocumentDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation("删除出入库单")
    @PostMapping("/deleteDocument")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "删除出入库单",action = OperatorType.DELETE)
    public Result deleteDocument(@RequestParam("documentGuid") String documentGuid) {
        log.info("删除出入库单入参: {}", documentGuid);
        inOutDocumentService.deleteDocument(documentGuid);
        return Result.buildEmptySuccess();
    }

    @ApiOperation("查询关联单据")
    @PostMapping("/selectDocumentGuidList")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询关联单据",action = OperatorType.SELECT)
    public Result<List<String>> selectDocumentGuidList(@RequestBody InOutContactDocumentQueryDTO queryDTO) {
        log.info("查询关联单据入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(inOutDocumentService.selectDocumentGuidList(queryDTO));
    }

    @ApiOperation("查询出入库单据及其明细(编辑时使用)")
    @PostMapping("/selectDocumentAndDetailForUpdate")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "询出入库单据及其明细(编辑时使用)",action = OperatorType.SELECT)
    public Result<InOutDocumentSelectDTO> selectDocumentAndDetailForUpdate(@RequestParam("documentGuid") String documentGuid) {
        log.info("查询出入库单据及其明细(编辑时使用)入参：{}", documentGuid);
        return Result.buildSuccessResult(inOutDocumentService.selectDocumentAndDetailForUpdate(documentGuid));
    }

    @ApiOperation("查询出入库单据及其明细(仅查看时使用)")
    @PostMapping("/selectDocumentAndDetailForSelect")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询出入库单据及其明细(仅查看时使用)",action = OperatorType.SELECT)
    public Result<InOutDocumentSelectDTO> selectDocumentAndDetailForSelect(@RequestParam("documentGuid") String documentGuid) {
        log.info("查询出入库单据及其明细(仅查看时使用)入参：{}", documentGuid);
        return Result.buildSuccessResult(inOutDocumentService.selectDocumentAndDetailForSelect(documentGuid));
    }

    @ApiOperation("查询出入库列表(分页)")
    @PostMapping("/selectDocumentListForPage")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "查询出入库列表(分页)",action = OperatorType.SELECT)
    public Result<Page<InOutDocumentSelectDTO>> selectDocumentListForPage(@RequestBody InOutDocumentQueryDTO queryDTO) {
        log.info("查询出入库列表(分页)入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(inOutDocumentService.selectDocumentListForPage(queryDTO));
    }

    @PostMapping("/documentListExport")
    public void documentListExport(@RequestBody InOutDocumentQueryDTO queryDTO, HttpServletResponse response) {
        log.info("导出出入库列表入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        queryDTO.setCurrentPage(1);
        queryDTO.setPageSize(Constants.MAX_EXPORT_SIZE);
        Page<InOutDocumentSelectDTO> inOutDocumentSelectDTOPage = inOutDocumentService.selectDocumentListForPage(queryDTO);
        List<InOutDocumentExportDTO> exportList = transferExportByPageData(inOutDocumentSelectDTOPage);
        try {
            ExcelUtil<InOutDocumentExportDTO> excelUtil = new ExcelUtil<>();
            response.setContentType("application/msexcel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(System.currentTimeMillis() + FILE_NAME[queryDTO.getInOutType()], "UTF-8"));
            ServletOutputStream outputStream = response.getOutputStream();
            excelUtil.exportExcel(DOCUMENT_HEADERS[queryDTO.getInOutType()], exportList, outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("导出出入库列表入参异常",e);
        }
    }

    @ApiOperation("下载新建出入库单据时批量导入物料的模板")
    @PostMapping("/importMaterialList/template")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "下载新建出入库单据时批量导入物料的模板")
    public void importMaterialListTemplate() throws Exception {
        HttpServletResponse resp = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        XSSFWorkbook xssfWorkbook = MaterialExcelUtil.getDownloadWorkBook("downloadExcel/downloadInOutDocumentMaterial.xlsx");
        MaterialExcelUtil.exportFile(resp, xssfWorkbook, "批量导入物料模板");
    }

    @ApiOperation("新建出入库单据时批量导入物料")
    @PostMapping("/importMaterialList")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "新建出入库单据时批量导入物料")
    public Result<List<InOutDocumentDetailSelectDTO>> importMaterialList(@RequestParam("storeGuid") String storeGuid,
                                                                         @RequestParam("warehouseGuid") String warehouseGuid,
                                                                         @RequestParam(value = "supplierGuid", required = false) String supplierGuid,
                                                                         @RequestParam(value = "materialGuidList", required = false) String materialGuidList,
                                                                         @RequestParam("file") MultipartFile multipartFile) throws IOException {
        log.info("新建出入库单据时批量导入物料入参storeGuid: {}, warehouseGuid: {}, supplierGuid: {}", storeGuid, warehouseGuid, supplierGuid);
        List<InOutDocumentMaterialDetailImportDTO> inOutDocumentMaterialDetailImportDTOList = MaterialExcelUtil.getInOutDocumentMaterialDataByFile(multipartFile);
        if (CollectionUtils.isEmpty(inOutDocumentMaterialDetailImportDTOList)) {
            return Result.buildEmptySuccess();
        }
        InOutDocumentMaterialImportDTO inOutDocumentMaterialImportDTO = new InOutDocumentMaterialImportDTO();
        inOutDocumentMaterialImportDTO.setStoreGuid(storeGuid);
        inOutDocumentMaterialImportDTO.setWarehouseGuid(warehouseGuid);
        inOutDocumentMaterialImportDTO.setSupplierGuid(supplierGuid);
        inOutDocumentMaterialImportDTO.setMaterialGuidList(materialGuidList);
        inOutDocumentMaterialImportDTO.setInOutDocumentMaterialDetailImportDTOList(inOutDocumentMaterialDetailImportDTOList);
        return Result.buildSuccessResult(inOutDocumentService.importMaterialList(inOutDocumentMaterialImportDTO));
    }

    private final static String[][] DOCUMENT_HEADERS = {
            {"序号", "入库单编码", "状态", "供应商", "入库仓库", "入库类型", "日期", "备注", "纸质单编号", "商品编号", "物料名称", "入库单位", "入库数量", "入库单价", "入库总金额（元）"},
            {"序号", "出库单编码", "状态", "供应商", "出库仓库", "出库类型", "日期", "备注", "纸质单编号", "商品编号", "物料名称", "出库单位", "出库数量", "出库单价", "出库总金额（元）"}
    };
    private final static String[] FILE_NAME = {"_入库列表.xls","_出库列表.xls"};

    private List<InOutDocumentExportDTO> transferExportByPageData(Page<InOutDocumentSelectDTO> inOutDocumentSelectDTOPage) {
        List<InOutDocumentExportDTO> exportList = Lists.newArrayList();
        if(inOutDocumentSelectDTOPage == null || CollUtil.isEmpty(inOutDocumentSelectDTOPage.getData())){
            return exportList;
        }
        List<String> documentGuidList = inOutDocumentSelectDTOPage.getData().stream()
                .map(InOutDocumentSelectDTO::getGuid)
                .distinct()
                .collect(Collectors.toList());
        DocumentDetailListQueryDTO detailListQueryDTO = new DocumentDetailListQueryDTO();
        detailListQueryDTO.setDocumentGuidList(documentGuidList);
        log.info("[查询出入库单详情列表]detailListQueryDTO={}", JacksonUtils.writeValueAsString(detailListQueryDTO));
        List<InOutDocumentSelectDTO> documentDetailList = inOutDocumentService.queryDocumentDetailList(detailListQueryDTO);
        log.info("[查询出入库单详情列表]documentDetailList={}", JacksonUtils.writeValueAsString(documentDetailList));
        for (InOutDocumentSelectDTO documentDetail : documentDetailList) {
            exportList.addAll(InOutDocumentExportDTO.build(documentDetail, 1 + exportList.size()));
        }
        // 总计
        InOutDocumentExportDTO exportDTO = new InOutDocumentExportDTO();
        BigDecimal all = exportList.stream()
                .map(InOutDocumentExportDTO::getTotalAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        exportDTO.setTotalAmount(all);
        exportList.add(exportDTO);
        return exportList;
    }

    @ApiOperation("供应商对账表(分页)")
    @PostMapping("/reconciliation")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "供应商对账表(分页)",action = OperatorType.SELECT)
    public Result<Page<InOutDocumentSelectDTO>> selectSuppliersReconciliation(@RequestBody SuppliersReconciliationQueryDTO queryDTO) {
        log.info("供应商对账表(分页)入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(inOutDocumentService.selectSuppliersReconciliation(queryDTO));
    }

    @ApiOperation("结算总金额")
    @PostMapping("/reconciliation/total")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "结算总金额",action = OperatorType.SELECT)
    public Result<BigDecimal> selectSuppliersReconciliationTotalAmount(@RequestBody SuppliersReconciliationQueryDTO queryDTO) {
        log.info("结算总金额入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(inOutDocumentService.selectSuppliersReconciliationTotalAmount(queryDTO));
    }

    @ApiOperation("结算")
    @PostMapping("/reconciliation/settle")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "结算",action = OperatorType.SELECT)
    public Result<Boolean> settleSuppliersReconciliation(@RequestBody List<String> list) {
        log.info("结算总金额入参: {}", JacksonUtils.writeValueAsString(list));
        return Result.buildSuccessResult(inOutDocumentService.settleSuppliersReconciliation(list));
    }

    @ApiOperation("出入库流水明细(分页)")
    @PostMapping("/selectFlowDetailListForPage")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP,description = "出入库流水明细(分页)",action = OperatorType.SELECT)
    public Result<Page<InOutDocumentFolwDetailSelectDTO>> selectFlowDetailListForPage(@RequestBody InOutDocumentFlowDetailQueryDTO queryDTO) {
        log.info("出入库流水明细(分页)入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(inOutDocumentService.selectFlowDetailListForPage(queryDTO));
    }

    private final static String[] FLOW_DETAIL_HEADERS = {"编码","物料名称","关联单据","单据类型","单据日期","变化量","单位"};

    @PostMapping("/flowDetailListExport")
    public void flowDetailListExport(@RequestBody InOutDocumentFlowDetailQueryDTO queryDTO, HttpServletResponse response) {
        log.info("导出出入库流水明细入参: {}", JacksonUtils.writeValueAsString(queryDTO));
        queryDTO.setCurrentPage(1);
        queryDTO.setPageSize(Constants.MAX_EXPORT_SIZE);
        Page<InOutDocumentFolwDetailSelectDTO> flowDetailListPage = inOutDocumentService.selectFlowDetailListForPage(queryDTO);
        List<InOutDocumentFlowDetailExportDTO> exportList = transferExportByFlowDetailListPageData(flowDetailListPage);
        try {
            ExcelUtil<InOutDocumentFlowDetailExportDTO> excelUtil = new ExcelUtil<>();
            response.setContentType("application/msexcel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(System.currentTimeMillis() + "出入库流水明细", "UTF-8"));
            ServletOutputStream outputStream = response.getOutputStream();
            excelUtil.exportExcel(FLOW_DETAIL_HEADERS, exportList, outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            log.error("导出出入库流水明细异常",e);
        }
    }

    private List<InOutDocumentFlowDetailExportDTO> transferExportByFlowDetailListPageData(Page<InOutDocumentFolwDetailSelectDTO> flowDetailListPage) {
        List<InOutDocumentFlowDetailExportDTO> exportList = Lists.newArrayList();
        if(flowDetailListPage == null || CollUtil.isEmpty(flowDetailListPage.getData())){
            return exportList;
        }
        //转换
        return flowDetailListPage.getData().stream().map(e ->{
            InOutDocumentFlowDetailExportDTO exportDTO = new InOutDocumentFlowDetailExportDTO();
            exportDTO.setMaterialCode(e.getMaterialCode());
            exportDTO.setMaterialName(e.getMaterialName());
            exportDTO.setDocumentGuid(e.getDocumentGuid());
            exportDTO.setInOutTypeName(InOutDocumentEnum.getNameByCode(e.getType()));
            exportDTO.setDocumentDate(DateUtil.formatDate(e.getDocumentDate()));
            exportDTO.setModifiedCount(e.getModifiedCount());
            exportDTO.setUnitName(e.getUnitName());
            return exportDTO;
        }).collect(Collectors.toList());
    }
}

