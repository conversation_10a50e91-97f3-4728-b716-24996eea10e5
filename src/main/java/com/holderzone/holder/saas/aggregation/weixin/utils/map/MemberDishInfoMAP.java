package com.holderzone.holder.saas.aggregation.weixin.utils.map;

import com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberDishInfoMAP {

    MemberDishInfoMAP INSTANCE = Mappers.getMapper(MemberDishInfoMAP.class);

    com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo toMemberTerminalDishDTO(RequestDishInfo discountFeeDetailDTO);

    List<com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo> toMemberTerminalDishDTOList(List<RequestDishInfo> discountFeeDetailDTOList);

    RequestDishInfo toMemberWechatDishDTO(com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo discountFeeDetailDTO);

    List<RequestDishInfo> toMemberWechatDishDTOList(List<com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo> discountFeeDetailDTOList);
}
