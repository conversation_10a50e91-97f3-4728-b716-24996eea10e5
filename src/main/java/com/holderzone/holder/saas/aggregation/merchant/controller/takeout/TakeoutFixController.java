package com.holderzone.holder.saas.aggregation.merchant.controller.takeout;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutConsumerService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutProducerService;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixItemDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutFixRecordDTO;
import com.holderzone.saas.store.dto.takeaway.UnItemBindUnbindReq;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutRecordQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/takeout/fix")
@Api(description = "修复外卖绑定映射关系")
public class TakeoutFixController {

    @Autowired
    private TakeoutConsumerService takeoutConsumerService;
    @Autowired
    private TakeoutProducerService takeoutProducerService;


    @ApiOperation(value = "修复审核列表")
    @PostMapping("/page")
    public Result<Page<TakeoutFixRecordDTO>> pageInfo(@RequestBody TakeoutRecordQueryDTO queryDTO) {
        return Result.buildSuccessResult(takeoutConsumerService.pageInfo(queryDTO));
    }

    @ApiOperation(value = "查询审核记录详情")
    @GetMapping("/query")
    public Result<List<TakeoutFixItemDTO>> listByRecordId(Long recordId) {
        return Result.buildSuccessResult(takeoutConsumerService.listByRecordId(recordId));
    }

    @ApiOperation(value = "修复数据")
    @PostMapping
    public Result<String> fix(@RequestBody @Valid TakeoutFixDTO takeoutFixDTO) {
        log.info("聚合层修复外卖绑定映射关系-修复入参：{}", JacksonUtils.writeValueAsString(takeoutFixDTO));
        takeoutConsumerService.fix(takeoutFixDTO);
        return Result.buildSuccessResult("修复成功");
    }

    @PostMapping("/bind")
    public void bindItem(@RequestBody @Validated UnItemBindUnbindReq unItemBindUnbindReq) {
        log.info("收到ERP发送的设置菜品映射消息，unItemBindUnbindReq: {}", JacksonUtils.writeValueAsString(unItemBindUnbindReq));
        takeoutProducerService.bindItem(unItemBindUnbindReq);
    }
}
