package com.holderzone.holder.saas.aggregation.app.service.feign.business;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.config.req.CashSettingReqDTO;
import com.holderzone.saas.store.dto.config.req.DineFoodSettingReqDTO;
import com.holderzone.saas.store.dto.config.req.PrintItemOrderConfigReq;
import com.holderzone.saas.store.dto.config.req.StoreFinishFoodConfigReq;
import com.holderzone.saas.store.dto.config.resp.CashSettingRespDTO;
import com.holderzone.saas.store.dto.config.resp.DineFoodSettingRespDTO;
import com.holderzone.saas.store.dto.config.resp.FinishFoodRespDTO;
import com.holderzone.saas.store.dto.order.request.AutoMarkReqDTO;
import com.holderzone.saas.store.dto.order.response.AutoMarkRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className StoreConfigClientService
 * @date 2019/1/23 11:27
 * @description 请求business-manage.StoreConfigController
 * @program holder-saas-store-business
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = StoreConfigClientService.StoreConfigFallback.class)
public interface StoreConfigClientService {

    /**
     * 更新自动号牌
     *
     * @param autoMarkReqDTO autoMarkReqDTO
     * @return boolean
     */
    @PostMapping("/storeConfig/update_auto_mark")
    Boolean updateAutoMark(AutoMarkReqDTO autoMarkReqDTO);

    /**
     * 查询门店自动号牌
     *
     * @param autoMarkReqDTO autoMarkReqDTO
     * @return int
     */
    @PostMapping("/storeConfig/query_auto_mark")
    Integer queryAutoMark(AutoMarkReqDTO autoMarkReqDTO);

    /**
     * 查询门店快餐自动号牌
     */
    @PostMapping("/storeConfig/auto_mark/query")
    AutoMarkRespDTO queryAutoMarkResp(AutoMarkReqDTO autoMarkReqDTO);

    @ApiOperation(value = "更新出餐设置", notes = "更新出餐设置")
    @PostMapping("/storeConfig/update_finish_food")
    boolean updateFinishFood(@RequestBody @Validated StoreFinishFoodConfigReq finishFoodConfigReq);

    @ApiOperation(value = "查询出餐设置", notes = "查询出餐设置")
    @ApiImplicitParam(name = "configQueryDTO", value = "configQueryDTO", required = true, dataType = "StoreConfigQueryDTO")
    @PostMapping("/storeConfig/finish_food/query")
    FinishFoodRespDTO queryFinishFood(@RequestBody @Validated StoreConfigQueryDTO configQueryDTO);

    @ApiOperation(value = "查询商品打印顺序配置", notes = "查询商品打印顺序配置")
    @PostMapping("/storeConfig/print_item_order/query")
    String queryPrintItemOrderConfig(@RequestBody @Validated StoreConfigQueryDTO configQueryDTO);

    @ApiOperation(value = "修改商品打印顺序配置", notes = "查询商品打印顺序配置")
    @PostMapping("/storeConfig/print_item_order/update")
    boolean updatePrintItemOrderConfig(@RequestBody @Validated PrintItemOrderConfigReq configReq);

    @ApiOperation(value = "查询正餐设置")
    @PostMapping("/storeConfig/dine_food_setting/query")
    DineFoodSettingRespDTO queryDineFoodSetting(@RequestBody @Validated StoreConfigQueryDTO configQueryDTO);

    @ApiOperation(value = "更新正餐设置")
    @PostMapping("/storeConfig/dine_food_setting/update")
    void updateDineFoodSetting(@RequestBody @Validated DineFoodSettingReqDTO settingReqDTO);

    @ApiOperation(value = "收银设置", notes = "查询收银设置")
    @PostMapping("/storeConfig/cash_setting/query")
    CashSettingRespDTO queryCashSetting(@RequestBody @Validated CashSettingReqDTO cashSettingReqDTO);

    @Slf4j
    @Component
    class StoreConfigFallback implements FallbackFactory<StoreConfigClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public StoreConfigClientService create(Throwable throwable) {
            return new StoreConfigClientService() {
                @Override
                public Boolean updateAutoMark(AutoMarkReqDTO autoMarkReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateAutoMark", JacksonUtils.writeValueAsString(autoMarkReqDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public Integer queryAutoMark(AutoMarkReqDTO autoMarkReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryAutoMark", JacksonUtils.writeValueAsString(autoMarkReqDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public AutoMarkRespDTO queryAutoMarkResp(AutoMarkReqDTO autoMarkReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryAutoMarkResp", JacksonUtils.writeValueAsString(autoMarkReqDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public boolean updateFinishFood(StoreFinishFoodConfigReq finishFoodConfigReq) {
                    log.error(HYSTRIX_PATTERN, "updateFinishFood", JacksonUtils.writeValueAsString(finishFoodConfigReq),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public FinishFoodRespDTO queryFinishFood(StoreConfigQueryDTO configQueryDTO) {
                    log.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public String queryPrintItemOrderConfig(StoreConfigQueryDTO configQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "queryPrintItemOrderConfig", JacksonUtils.writeValueAsString(configQueryDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public boolean updatePrintItemOrderConfig(PrintItemOrderConfigReq configReq) {
                    log.error(HYSTRIX_PATTERN, "updatePrintItemOrderConfig", JacksonUtils.writeValueAsString(configReq),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public DineFoodSettingRespDTO queryDineFoodSetting(StoreConfigQueryDTO configQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "queryDineFoodSetting", JacksonUtils.writeValueAsString(configQueryDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public void updateDineFoodSetting(DineFoodSettingReqDTO settingReqDTO) {
                    log.error(HYSTRIX_PATTERN, "updateDineFoodSetting", JacksonUtils.writeValueAsString(settingReqDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

                @Override
                public CashSettingRespDTO queryCashSetting(CashSettingReqDTO cashSettingReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryCashSetting", JacksonUtils.writeValueAsString(cashSettingReqDTO),
                            throwable.getMessage());
                    throw new ServerException();
                }

            };
        }
    }
}