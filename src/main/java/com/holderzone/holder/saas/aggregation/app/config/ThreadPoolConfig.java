package com.holderzone.holder.saas.aggregation.app.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class ThreadPoolConfig {



    @Bean(value = "reportExecutor")
    public ExecutorService asyncOrderTcdExecutor() {
        return new ThreadPoolExecutor(10, 100, 10L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(500), new ThreadFactoryBuilder().setNameFormat("report-executor-%d").build());
    }
}