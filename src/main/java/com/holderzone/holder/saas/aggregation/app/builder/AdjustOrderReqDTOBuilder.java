package com.holderzone.holder.saas.aggregation.app.builder;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.saas.store.dto.item.resp.SkuTakeawayInfoRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubItemSkuWebRespDTO;
import com.holderzone.saas.store.dto.item.resp.SubgroupWebRespDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.common.PackageSubgroupDTO;
import com.holderzone.saas.store.dto.order.common.SubDineInItemDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.takeaway.TakeoutOrderDTO;
import com.holderzone.saas.store.dto.trade.req.adjust.AdjustOrderReqDTO;
import com.holderzone.saas.store.enums.item.ItemTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class AdjustOrderReqDTOBuilder {


    /**
     * 正餐快餐请求DTO预处理
     */
    public static void preHandleReqDTO(AdjustOrderReqDTO reqDTO, DineinOrderDetailRespDTO orderRespDTO) {
        // 查询订单信息
        reqDTO.setOrderNo(orderRespDTO.getOrderNo());
        reqDTO.setBusinessDay(orderRespDTO.getBusinessDay());
        reqDTO.setState(orderRespDTO.getState());
        reqDTO.setDineInItemDTOS(orderRespDTO.getDineInItemDTOS());

        // 特殊处理数量调整的商品，如果没有则不处理
        List<AdjustOrderReqDTO.AdjustOrderItem> orderItemList = reqDTO.getOrderItemList();
        List<AdjustOrderReqDTO.AdjustOrderItem> adjustCountOrderItemList = orderItemList.stream().filter(e -> e.getAdjustType() == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adjustCountOrderItemList)) {
            return;
        }
        Map<String, DineInItemDTO> itemMap = orderRespDTO.getDineInItemDTOS().stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        if (CollectionUtils.isNotEmpty(orderRespDTO.getSubOrderDetails())) {
            Map<String, DineInItemDTO> subItemMap = orderRespDTO.getSubOrderDetails().stream()
                    .flatMap(e -> e.getDineInItemDTOS().stream())
                    .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
            itemMap.putAll(subItemMap);
        }
        for (AdjustOrderReqDTO.AdjustOrderItem itemReq : adjustCountOrderItemList) {
            List<DineInItemDTO> itemList = Lists.newArrayList();
            DineInItemDTO item = itemMap.get(itemReq.getOrderItemGuid());
            if (Objects.isNull(item)) {
                continue;
            }
            DineInItemDTO adjustItem = new DineInItemDTO();
            BeanUtils.copyProperties(item, adjustItem);
            adjustItem.setCurrentCount(itemReq.getAdjustCount());
            adjustItem.setGmtCreate(null);
            itemList.add(adjustItem);
            itemReq.setItemList(itemList);
        }
    }


    /**
     * 外卖请求DTO预处理
     */
    public static void preHandleReqDTO(AdjustOrderReqDTO reqDTO, TakeoutOrderDTO takeoutOrderDetail) {
        log.info("外卖订单查询结果:{}", JacksonUtils.writeValueAsString(takeoutOrderDetail));
        // 查询订单信息
        reqDTO.setTakeoutOrderDetail(takeoutOrderDetail);
        reqDTO.setOrderNo(takeoutOrderDetail.getOrderViewId());
        reqDTO.setBusinessDay(takeoutOrderDetail.getBusinessDay());

        // 组装外卖映射商品明细
        List<DineInItemDTO> dineInItemDTOS = Lists.newArrayList();
        List<TakeoutOrderDTO.OrderItemDTO> arrayOfItem = takeoutOrderDetail.getArrayOfItem();
        for (TakeoutOrderDTO.OrderItemDTO orderItemDTO : arrayOfItem) {
            SkuTakeawayInfoRespDTO skuInfo = orderItemDTO.getSkuTakeawayInfoRespDTO();
            if (Objects.isNull(skuInfo)) {
                continue;
            }

            DineInItemDTO itemDTO = new DineInItemDTO();
            BeanUtils.copyProperties(skuInfo, itemDTO);
            itemDTO.setGuid(orderItemDTO.getItemGuid());
            itemDTO.setItemName(orderItemDTO.getItemName());
            itemDTO.setItemType(0);
            itemDTO.setSkuName("");
            itemDTO.setUnit("");
            itemDTO.setItemType(!StringUtils.isEmpty(orderItemDTO.getItemSpec()) ? ItemTypeEnum.MULTI_SKU.getCode() :
                    ItemTypeEnum.SINGLE_UNWEIGH.getCode());
            itemDTO.setCurrentCount(orderItemDTO.getItemCount());
            itemDTO.setPrice(orderItemDTO.getItemPrice());
            itemDTO.setMappingCount(orderItemDTO.getActualItemCount());

            // 套餐商品
            List<SubgroupWebRespDTO> subgroupList = skuInfo.getSubgroupList();
            if (CollectionUtils.isNotEmpty(subgroupList)) {
                List<PackageSubgroupDTO> packageSubgroupList = Lists.newArrayList();
                for (SubgroupWebRespDTO subgroupWebRespDTO : subgroupList) {
                    PackageSubgroupDTO packageSubgroup = new PackageSubgroupDTO();
                    BeanUtils.copyProperties(subgroupWebRespDTO, packageSubgroup);

                    List<SubItemSkuWebRespDTO> subItemSkuList = subgroupWebRespDTO.getSubItemSkuList();
                    if (CollectionUtils.isNotEmpty(subItemSkuList)) {
                        List<SubDineInItemDTO> subDineInItemList = Lists.newArrayList();
                        for (SubItemSkuWebRespDTO subItemSkuWebRespDTO : subItemSkuList) {
                            SubDineInItemDTO subDineInItemDTO = new SubDineInItemDTO();
                            BeanUtils.copyProperties(subItemSkuWebRespDTO, subDineInItemDTO);
                            subDineInItemDTO.setCurrentCount(new BigDecimal(subItemSkuWebRespDTO.getDefaultNum()));
                            subDineInItemDTO.setPackageDefaultCount(subItemSkuWebRespDTO.getItemNum());

                            subDineInItemList.add(subDineInItemDTO);
                        }
                        packageSubgroup.setSubDineInItemDTOS(subDineInItemList);
                    }
                    packageSubgroupList.add(packageSubgroup);
                }
                itemDTO.setPackageSubgroupDTOS(packageSubgroupList);
            }
            dineInItemDTOS.add(itemDTO);
        }
        reqDTO.setDineInItemDTOS(dineInItemDTOS);
        log.info("外卖订单查询结果-dineInItemDTOS:{}", JacksonUtils.writeValueAsString(dineInItemDTOS));

        // 特殊处理数量调整的商品，如果没有则不处理
        List<AdjustOrderReqDTO.AdjustOrderItem> orderItemList = reqDTO.getOrderItemList();
        List<AdjustOrderReqDTO.AdjustOrderItem> adjustCountOrderItemList = orderItemList.stream().filter(e -> e.getAdjustType() == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(adjustCountOrderItemList)) {
            return;
        }
        Map<String, DineInItemDTO> itemMap = dineInItemDTOS.stream()
                .collect(Collectors.toMap(DineInItemDTO::getGuid, Function.identity(), (key1, key2) -> key1));
        for (AdjustOrderReqDTO.AdjustOrderItem itemReq : adjustCountOrderItemList) {
            List<DineInItemDTO> itemList = Lists.newArrayList();
            DineInItemDTO item = itemMap.get(itemReq.getOrderItemGuid());
            if (Objects.isNull(item)) {
                continue;
            }
            DineInItemDTO adjustItem = new DineInItemDTO();
            BeanUtils.copyProperties(item, adjustItem);
            adjustItem.setCurrentCount(itemReq.getAdjustCount());
            adjustItem.setMappingCount(item.getMappingCount().divide(item.getCurrentCount(), 2, BigDecimal.ROUND_DOWN).multiply(itemReq.getAdjustCount()));
            adjustItem.setGmtCreate(null);
            itemList.add(adjustItem);
            itemReq.setItemList(itemList);
        }
    }
}
