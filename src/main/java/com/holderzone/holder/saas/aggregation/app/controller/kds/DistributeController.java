package com.holderzone.holder.saas.aggregation.app.controller.kds;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.kds.DsitributeRpcService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.DstAreaRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DstBindDetailsRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DstBindStatusRespDTO;
import com.holderzone.saas.store.dto.kds.resp.DstTypeBindRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Api("KDS设备接口")
@RequestMapping("/distribute")
public class DistributeController {

    private final DsitributeRpcService dsitributeRpcService;

    @Autowired
    public DistributeController(DsitributeRpcService dsitributeRpcService) {
        this.dsitributeRpcService = dsitributeRpcService;
    }

    @PostMapping("/query_binding_preview")
    @ApiOperation(value = "查询出堂口绑定关系预览")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "查询出堂口绑定关系预览",action = OperatorType.SELECT)
    public Result<DstBindStatusRespDTO> queryBindingPreview(@RequestBody DeviceQueryReqDTO deviceQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询出堂口绑定关系预览入参:{}", JacksonUtils.writeValueAsString(deviceQueryReqDTO));
        }
        return Result.buildSuccessResult(dsitributeRpcService.queryBindingPreview(deviceQueryReqDTO));
    }

    @PostMapping("/query_binding_details")
    @ApiOperation(value = "查询出堂口绑定关系详情（包括预览和已绑定商品列表）")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "查询出堂口绑定关系详情",action = OperatorType.SELECT)
    public Result<DstBindDetailsRespDTO> queryBindingDetails(@RequestBody DstItemQueryReqDTO dstItemQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询出堂口绑定关系详情入参:{}", JacksonUtils.writeValueAsString(dstItemQueryReqDTO));
        }
        return Result.buildSuccessResult(dsitributeRpcService.queryBindingDetails(dstItemQueryReqDTO));
    }

    @PostMapping("/query_bound_area")
    @ApiOperation(value = "查询出堂口绑定区域")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "查询出堂口绑定区域",action = OperatorType.SELECT)
    public Result<DstAreaRespDTO> queryBoundArea(@RequestBody DstBoundAreaReqDTO dstBoundAreaReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询出堂口绑定区域入参:{}", JacksonUtils.writeValueAsString(dstBoundAreaReqDTO));
        }
        return Result.buildSuccessResult(dsitributeRpcService.queryBoundArea(dstBoundAreaReqDTO));
    }

    @PostMapping("/bind_area")
    @ApiOperation(value = "出堂口绑定区域")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS, description = "出堂口绑定区域", action = OperatorType.SELECT)
    public Result bindArea(@RequestBody DstBindAreaReqDTO dstBindAreaReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("出堂口绑定区域入参:{}", JacksonUtils.writeValueAsString(dstBindAreaReqDTO));
        }
        dsitributeRpcService.bindArea(dstBindAreaReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/query_bound_item_of_device")
    @ApiOperation(value = "查询当前出堂口绑定商品")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "查询当前出堂口绑定商品",action = OperatorType.SELECT)
    public Result<List<DstTypeBindRespDTO>> queryBoundItemOfDevice(@RequestBody DstItemQueryReqDTO dstItemQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询当前出堂口绑定商品入参:{}", JacksonUtils.writeValueAsString(dstItemQueryReqDTO));
        }
        return Result.buildSuccessResult(dsitributeRpcService.queryBoundItemOfDevice(dstItemQueryReqDTO));
    }

    @PostMapping("/query_all_item_of_store")
    @ApiOperation(value = "查询所有出堂口绑定商品")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS,description = "查询所有出堂口绑定商品",action = OperatorType.SELECT)
    public Result<List<DstTypeBindRespDTO>> queryAllItemOfStore(@RequestBody DstItemQueryReqDTO dstItemQueryReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询所有出堂口绑定商品入参:{}", JacksonUtils.writeValueAsString(dstItemQueryReqDTO));
        }
        return Result.buildSuccessResult(dsitributeRpcService.queryAllItemOfStore(dstItemQueryReqDTO));
    }

    @PostMapping("/bind_item")
    @ApiOperation(value = "KDS出堂口绑定菜品")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS, description = "KDS出堂口绑定菜品", action = OperatorType.SELECT)
    public Result bindItem(@RequestBody DstBindItemReqDTO dstBindItemReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("KDS出堂口绑定菜品入参:{}", JacksonUtils.writeValueAsString(dstBindItemReqDTO));
        }
        dsitributeRpcService.bindItem(dstBindItemReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/unbind_item")
    @ApiOperation(value = "KDS出堂口解绑菜品")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS, description = "KDS出堂口解绑菜品")
    public Result unbindItem(@RequestBody DstBindItemReqDTO dstBindItemReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("KDS出堂口解绑菜品入参:{}", JacksonUtils.writeValueAsString(dstBindItemReqDTO));
        }
        dsitributeRpcService.unbindItem(dstBindItemReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/update_basic_config")
    @ApiOperation(value = "更新设备基础配置")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS, description = "更新设备基础配置")
    public Result updateBasicConfig(@RequestBody DeviceBasicConfUpdateReqDTO deviceBasicConfUpdateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新设备基础配置入参:{}", JacksonUtils.writeValueAsString(deviceBasicConfUpdateReqDTO));
        }
        dsitributeRpcService.updateDstBasicConfig(deviceBasicConfUpdateReqDTO);
        return Result.buildEmptySuccess();
    }

    @PostMapping("/update_advanced_config")
    @ApiOperation(value = "更新设备高级配置")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_KDS, description = "更新设备高级配置")
    public Result updateAdvancedConfig(@RequestBody DeviceAdvanConfUpdateReqDTO deviceAdvanConfUpdateReqDTO) {
        if (log.isInfoEnabled()) {
            log.info("更新设备高级配置入参:{}", JacksonUtils.writeValueAsString(deviceAdvanConfUpdateReqDTO));
        }
        dsitributeRpcService.updateDstAdvancedConfig(deviceAdvanConfUpdateReqDTO);
        return Result.buildEmptySuccess();
    }
}

