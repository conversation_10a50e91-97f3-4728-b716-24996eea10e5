package com.holderzone.holder.saas.aggregation.weixin.utils;

import lombok.Data;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <pre>针对List集合做的排序工具:
 * 使用方法，调用SortedUtils.sorted(List<T> list, SortedAttribute... sortedAttributes)方法，
 * 该方法有两个参数，第一个是list需要排序的集合对象。SortedAttribute对象为 SortedUtils
 * 的public static SortedAttribute内部类，该对象有两个需要赋值的元素，一个为name，一个为排序枚举SortedType，
 * SortedType枚举类：升序-ASC,降序-DESC;顾名思义，name表示List&lt;T&gt;中的T的其中的属性名称，选择该属性值
 * 时，请务必选择以下数据类型，String、Number、Boolean、Date、LocalTime、LocalDateTime、LocalDate等。
 * Number类型只要实现了它的子类都可以进行比较计算。
 * 举个例子：
 *  public class Student{
 *
 *         private String name;
 *
 *         private int age;
 *
 *         private int classes;
 *
 *         private int height;
 *
 * 		// getter、setter
 * }
 * ------------------------------分割线------------------------------
 * Student{name='柯素', age=15, classes=1, height=159}
 * Student{name='孙戚', age=12, classes=10, height=179}
 * Student{name='蒋雨', age=14, classes=1, height=163}
 * Student{name='黄美', age=14, classes=2, height=161}
 * Student{name='碧义', age=14, classes=4, height=165}
 * Student{name='黄土', age=14, classes=6, height=169}
 * Student{name='彤迎', age=13, classes=8, height=153}
 * Student{name='柯区', age=14, classes=10, height=160}
 * Student{name='孔戚', age=15, classes=8, height=175}
 * Student{name='彤隼', age=13, classes=1, height=168}
 * ------------------------------分割线------------------------------
 * List<Student>  students = ...;//Student数据集合，上面为原始数据集。
 *  ---------------------------------------------------------------------------------
 * | 下面排序规则为，对students数据集排序，以classes作为分组排序第一优先级，降序排序，
 * | 以height为第二优先级，升序排序。
 * | SortedAttribute参数为可变现数组，排序优先级数组中index越小，优先级越高。
 * | 当SortedAttribute为空时，传入的T必须是String、Number、Boolean、Date、LocalTime、
 * | LocalDateTime、LocalDate类型。
 *  ----------------------------------------------------------------------------------
 * List<Student> sortedList = SortedUtils.sorted(students,
 *                                      new SortedAttribute("classes",SortedType.DESC),
 *                                      new SortedAttribute("height"));
 *
 * 或者使用简化 sortedBySimpleKeys(List,String[]); ↓
 * List<Student> sortedList2 = SortedUtils.sorted(students,
 *                                      "classes=DESC","height");
 * ------------------------------分割线------------------------------
 * 排序后的结果如下所示：
 * Student{name='柯区', age=14, classes=10, height=160}
 * Student{name='孙戚', age=12, classes=10, height=179}
 * Student{name='彤迎', age=13, classes=8, height=153}
 * Student{name='孔戚', age=15, classes=8, height=175}
 * Student{name='黄土', age=14, classes=6, height=169}
 * Student{name='碧义', age=14, classes=4, height=165}
 * Student{name='黄美', age=14, classes=2, height=161}
 * Student{name='柯素', age=15, classes=1, height=159}
 * Student{name='蒋雨', age=14, classes=1, height=163}
 * Student{name='彤隼', age=13, classes=1, height=168}
 * ------------------------------分割线------------------------------
 * </pre>
 * <AUTHOR>
 * @version 1.0
 * @className SortedUtils
 * @date 2019/12/16 14:39
 * @description 排序工具
 * @program weixin
 */
public class SortedUtils<T> {

    //确保属性值万无一失
    private  SortedUtils(SortedAttribute[] sortedAttributes) {
        if(sortedAttributes != null && sortedAttributes.length != 0){
            int count = 0;
            SortedAttribute[] temp = new SortedAttribute[sortedAttributes.length];
            for (SortedAttribute s : sortedAttributes){
                if( s != null){
                    temp[count] = s;
                    count++;
                }
            }
            if(count == sortedAttributes.length){
                this.sortedAttributes = sortedAttributes;
            }else if(count > 0) {
                SortedAttribute[] result = new SortedAttribute[count];
                System.arraycopy(temp,0,result,0,count);
                this.sortedAttributes = result;
            }
        }

    }

    /** 排序属性*/
    private SortedAttribute[] sortedAttributes;

    /**
     * <pre>
     * 定义keys格式：name=DESC 或name=ASC，name代表要输入的属性名称。
     * 该方法为 {@linkplain List}&lt;T&gt; sorted({@linkplain List}&lt;T&gt; list, {@linkplain SortedAttribute}... sortedAttributes)
     * 方法的简化。
     * </pre>
     * @param list T集合
     * @param keys 键值对
     * @param <T> 转换对象
     * @return T排序后的新集合
     */
    public static <T> List<T> sortedBySimpleKeys(List<T> list, String... keys){{

        return sorted(list,keysToSortedAttribute(keys));
    }}

    /**
     * 定义keys格式，name=DESC 或name=ASC，name代表要输入的属性名称，
     * 当key只为name时，默认为ASC排序
     * @param keys 键值对
     * @return SortedAttribute
     */
    private static SortedAttribute[] keysToSortedAttribute(String[] keys){
        if(keys != null && keys.length > 0){
            SortedAttribute[] data = new SortedAttribute[keys.length];
            int counts = 0;
            for(String key : keys){
                if(   !isEmpty( key )   ){
                    if(key.contains("=")){
                        String[] values = key.split("=");
                        data[counts++] = SortedType.DESC.name().equalsIgnoreCase(values[1])
                                            ? new SortedAttribute(key,SortedType.DESC) : new SortedAttribute(key);
                    }else{
                        data[counts++] = new SortedAttribute(key);
                    }
                }
            }

            int finalCounts = counts;
            return counts  == keys.length ? data : get(()->{
                        SortedAttribute[] result = new SortedAttribute[finalCounts];
                        System.arraycopy(data,0,result,0, finalCounts);
                        return result;
                    }
                );
        }
        return null;
    }



    private static SortedAttribute[] get(Operator s){
        return s.get();
    }

    private static boolean isEmpty(String str){
        return (str == null || "".equals(str.trim()));
    }

    public static <T> List<T> sorted(List<T> list, SortedAttribute... sortedAttributes){

        return new SortedUtils<T>(sortedAttributes).sorted(list);
    }

    private  List<T> sorted(List<T> list){
        return new SortedList().sorted(list);
    }

    @FunctionalInterface
    private interface Operator{
        SortedAttribute[] get();
    }

    private class SortedList{
        Node            first;        //第一个元素
        Node            current;      //当前元素
        int             size;         //排序集合的长度

        //排序
        List<T> sorted(List<T> list){
            if(list == null || list.size() == 0){
                return null;
            }
            size         = list.size();

            assertAttributes(list.get(0));
            list.forEach(this::insert);

            return read();
        }

        //读取排序后的结果，读一次就没了
        private List<T> read(){
            List<T> data = new ArrayList<>(size);
            Node    currentNode = first;
            while (currentNode != null){
                data.add(   currentNode.value   );
                currentNode.previous    =      null; // 读取后清空。
                currentNode.value       =      null;
                Node temp               =      currentNode.next;
                currentNode.next        =      null;
                currentNode             =      temp;
            }
            return data;
        }

        //插入元素，插入过程进行排序
        private void insert(T t){
            //判断是否是第一个插入元素
            if(first == null){
                first       =       new Node(t,null);
                current     =       first;
            }else{
                //创建新元素存储t值
                Node        newLink                 =       new Node(t,null);
                //寻着链表上下查询对比值
                lookForInsert(current,newLink);
                //将新插入的值设置为当前元素，不论该元素处于链表的哪个位置都以新插入的元素为判断起点。
                current                             =       newLink;
            }
        }

        //寻找该元素应该插入的节点并插入
        private void lookForInsert(Node node,Node tagNode){
            //将要对比的元素进行比较，返回值为1、0、-1三个值，1表示node元素比tagNode元素大，0表示相等，-1表示比它小。
            int         flag                =       compare(node.value,tagNode.value);

            //当比较两个元素相等时，就把当前元素插入到目标元素的后面。
            if( flag == 0 ){
                Node    nextNode            =       node.next;
                        tagNode.next        =       nextNode;
                        tagNode.previous    =       node;
                        node.next           =       tagNode;
                if( nextNode != null ){
                    nextNode.previous       =       tagNode;
                }

            //比较元素比目标元素大时，将目标元素向前推进，与比较元素的前一个继续比较。
            }else if(   flag == -1    ){
                Node preNode                =       node.previous;
                //当前一个元素为空时，表面比较元素是第一个元素，这时将目标元素插入在比较元素之前
                if( preNode == null ){
                    node.previous           =       tagNode;
                    tagNode.next            =       node;
                }else{
                    //不为空时，继续比较
                    int  g                  =       compare(preNode.value,tagNode.value);
                    //比较结果如果为0或-1，则表示目标元素值在比较元素与前一个元素值之间，这时就插入当前目标元素。
                    if( g == 0 || g == 1){
                        node.previous       =       tagNode;
                        preNode.next        =       tagNode;

                        tagNode.next        =       node;
                        tagNode.previous    =       preNode;
                    }else{
                        //如果为1，则继续向前比较
                        lookForInsert(preNode,tagNode);
                    }
                }

            //比较元素比目标元素小时，将目标元素向后推进，与比较元素的后一个继续比较。 插入逻辑与flag = 1时相反。
            }else if(   flag == 1    ){
                Node nextNode               =       node.next;
                if( nextNode == null ){
                    node.next               =       tagNode;
                    tagNode.previous        =       node;
                }else{
                    int  g                  =       compare(nextNode.value,tagNode.value);
                    if( g == 0 || g == -1){
                        node.next           =       tagNode;
                        nextNode.previous   =       tagNode;

                        tagNode.next        =       nextNode;
                        tagNode.previous    =       node;
                    }else{
                        lookForInsert(nextNode,tagNode);
                    }
                }

            }

            //当当前插入元素的前一个元素为空时，则表明该元素跑到了第一个位置，这时将first元素替换为当前目标元素。
            if(tagNode.previous == null){
                first = tagNode;
            }
        }

        //根据属性升序降序来比较
        private int compare(T srcObj,T tagObj){

            if(sortedAttributes == null){
               return compareTo(srcObj,tagObj);
            }

            for (SortedAttribute sort : sortedAttributes){
                int r = compareTo(srcObj,tagObj,sort.name);
                if (sort.kind == 2) {
                    r = 0 - r;
                } else {
                    if (sort.type == SortedType.ASC) {
                        //升序反转
                        r = 0 - r;
                    }
                }
                if(r != 0){
                    return r;
                }
            }
            return 0;
        }

        //获取元素对应属性的的值
        private Object getAttrValue(T srcObj,String attr){
            if(srcObj == null){return null;}
            try {
                Field srcField = srcObj.getClass().getDeclaredField(attr);
                srcField.setAccessible(true);
                return srcField.get(srcObj);
            } catch (Exception e) {
//                e.printStackTrace();
                return null;
            }
        }

        //根据属性比较对象
        private int compareTo(T srcObj,T tagObj,String attr){
            return compareTo(getAttrValue(srcObj,attr),getAttrValue(tagObj,attr));
        }

        //单纯判断对于两者为null的情况下的大小
        private int compareObj(Object srcObj,Object tagObj){
            if(srcObj == null && tagObj == null){
                return 0;
            }else if(srcObj == null){
                return -1;
            }else if(tagObj == null){
                return 1;
            }
            if(srcObj == tagObj){
                return 0;
            }
            return -3;
        }

        //比较两个对象的大小
        private int compareTo(Object srcObj,Object tagObj){
            int judge = compare0(srcObj,tagObj);
            return Integer.compare(judge,0);
        }

        //基础数据类型比较
        private int compare0(Object srcObj,Object tagObj){
            int preJudge = compareObj(srcObj,tagObj);
            if(preJudge != -3){
                return preJudge;
            }
            if(srcObj instanceof String){
                return ((String) srcObj).compareTo( (String)tagObj  );
            }else if(srcObj instanceof Number){
                double v = (((Number) srcObj).doubleValue() - ((Number) tagObj).doubleValue());
                return v > 0 ? 1 : (v == 0 ? 0 : -1);
            }else if(srcObj instanceof Boolean){
                boolean sf = (Boolean)srcObj;
                boolean tf = (Boolean)tagObj;
                return sf ? (tf ? 0 : 1) : (tf ? -1 : 0);
            }else if(srcObj instanceof Date){
                return ((Date)srcObj).compareTo((Date)tagObj);
            }else if(srcObj instanceof LocalTime){
                return ((LocalTime)srcObj).compareTo((LocalTime)tagObj);
            }else if(srcObj instanceof LocalDate){
                return ((LocalDate)srcObj).compareTo((LocalDate)tagObj);
            }else if(srcObj instanceof LocalDateTime){
                return ((LocalDateTime)srcObj).compareTo((LocalDateTime)tagObj);
            }else if(srcObj instanceof Character){
                return ((Character)srcObj).compareTo((Character)tagObj);
            }
            return 0;
        }

        /**
         * 确保输入的属性是类T所必有的属性。
         * @param t 非null对象
         */
        private void assertAttributes(T t){
            if(sortedAttributes == null){
                if(!(t instanceof String || t instanceof Number || t instanceof Character || t instanceof Boolean)){
                    throw new RuntimeException("非基本数据类型需设置判断属性！");
                }
            }else{
                Class clazz = t.getClass();

                for(SortedAttribute a : sortedAttributes){
                    try {
                        assert a.name != null;
                        clazz.getDeclaredField(a.name);
                    } catch (NoSuchFieldException e) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(clazz.getSimpleName()).append("无此").append(a.name).append("属性！");
                        throw new RuntimeException(sb.toString());
                    }
                }

            }
        }

        SortedList() {
            this.first  = null;
        }
    }

    //双向链表
    private class Node{
        T value;                            //数据
        Node                next;           //下一个元素
        Node                previous;       //前一个元素

        Node(T value, Node next) {
            this.value      =   value;
            this.next       =   next;
        }

        Node() {
        }
    }




    /** 按照属性名称以及排序类型设置排序算法，设置要排序的对象属性名以及是升序排序还是降序排序。*/
    public static class SortedAttribute{
        final String        name;
        final SortedType    type;
        final int           kind;

        public SortedAttribute(String name, SortedType type) {
            this.name = name;
            this.type = type;
            this.kind = 1;
        }

        public SortedAttribute(SortedType type) {
            this.name = null;
            this.type = type;
            this.kind = 3;
        }

        public SortedAttribute(String name) {
            this.name = name;
            this.type = SortedType.ASC;
            this.kind = 2;
        }

    }

    public enum SortedType{
        /**升序*/
        ASC,
        /**降序*/
        DESC;
    }


/*

    //需求对Student进行排序，按照班级从低到高，然后每个班级按身高排序，身高从高到底。
    //姓 名
    private static final String[] x = {"张","王","李","孙","冯","陈","朱","季","苟","许","姜","蒋","司","戚","碧","黄","孔","彤","柯","关"};
    private static final String[] n = {"飞","羽","林","素","灵","尧","隼","戚","美","区","义","逢","迎","水","风","雨","雷","电","土","火"};
    private static List<Student> randomList(int len){
        List<Student> students = new ArrayList<>(len);
        for(int i = 0 ; i < len ; i++){
            Student s = Student.builder()
                    .age(random(4,12))
                    .name(randomName())
                    .classes(random(10,1))
                    .height(random(30,150))
                    .build();
            students.add(s);
        }
        return students;
    }

    private static String randomName(){
        return x[random(x.length)] + n[random(n.length)];
    }

    private static int random(int len,int start){
        return (int)Math.floor(Math.random()*len) + start;
    }

    private static int random(int len){
        return random(len,0);
    }

    @lombok.Builder
    @lombok.Data
    public static class Student{
        //姓名
        private String name;
        //年龄
        private int age;
        //班级
        private int classes;
        //身高
        private int height;

        public Student() {

        }

        public Student(String name, int age, int classes, int height) {
            this.name = name;
            this.age = age;
            this.classes = classes;
            this.height = height;
        }

        @Override
        public String toString() {
            return "Student{" +
                    "name='" + name + '\'' +
                    ", age=" + age +
                    ", classes=" + classes +
                    ", height=" + height +
                    '}';
        }
    }


    public static void main(String[] args) {

        //排序案例测试，随机生成一系列的学生对象
        List<Student>  students = randomList(10);
        for (Student s : students){
            System.out.println(s);
        }

        System.out.println("-----------------------------------------------");

        //对学生集合排序，生成新的排序集合，原始集合不做改变
        List<Student> sortedList = SortedUtils.sorted(students,new SortedAttribute("classes",SortedType.DESC),new SortedAttribute("height"));
        for (Student s : sortedList){
            System.out.println(s);
        }

        List<String> list = new ArrayList<>(10);
        list.add("sss");
        System.out.println(list.size());
        String[] srcArray = {"1","2","3",null,null,null};
        String[] ds       = new String[3];
        System.arraycopy(srcArray,0,ds,0,3);
        for (String s : ds){
            System.out.println(s);
        }

    }
*/


}
