package com.holderzone.holder.saas.aggregation.merchant.controller.member.account;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.MemberListClientNewService;
import com.holderzone.holder.saas.member.dto.account.request.MemberListQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberSaveReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberListRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@Api(description = "会员列表基础相关接口")
@RequestMapping("/api/hsm_member_list_new")
@RestController
public class HsmMemberListNewController {

	private static final Logger log = LoggerFactory.getLogger(HsmMemberListNewController.class);
	@Autowired
	private MemberListClientNewService memberListClientNewService;
	/**
	 * 会员列表展示接口
	 *
	 * @param queryDTO 带条件封装的dto实体
	 */
	@ApiOperation(value = "员列表展示接口", notes = "员列表展示接口")
	@ApiImplicitParam(name = "MemberListQueryReqDTO", value = "员列表展示接口", required = true, dataType = "MemberListQueryReqDTO")
	@PostMapping(value = "/memberPageList", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "员列表展示接口")
	public Result<Page<MemberListRespDTO>> memberPageList(@RequestBody MemberListQueryReqDTO queryDTO) {
		log.info("会员列表展示接口入参,queryDTO={}", queryDTO);
		return Result.buildSuccessResult(memberListClientNewService.listByCondition(queryDTO));
	}
	/**
	 * 会员禁用
	 *
	 * @param memberGuid 会员的guid
	 * @param reason     禁用原因
	 */
	@ApiOperation(value = "会员禁用", notes = "会员禁用")
	@ApiImplicitParams({ @ApiImplicitParam(name = "memberGuid", value = "会员guid", required = true, dataType = "String"),
            @ApiImplicitParam(name = "reason", value = "禁用原因", dataType = "String")})
	@GetMapping(value = "/disableMember", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "会员禁用")
	public Result<String> disableMember(String memberGuid, String reason) {
		log.info("会员禁用接口入参,memberGuid={}", memberGuid);
		log.info("会员禁用接口入参,reason={}", reason);
		boolean disable = memberListClientNewService.disable(memberGuid, reason);
		if (disable) {
			return Result.buildEmptySuccess();
		}
		return Result.buildSystemErrResult("会员禁用失败！请重试！");
	}
	/**
	 * 会员启用
	 *
	 * @param memberGuid 会员的guid
	 */
	@ApiOperation(value = "会员启用", notes = "会员启用")
	@ApiImplicitParams({ @ApiImplicitParam(name = "memberGuid", value = "会员guid", required = true, dataType = "String"),
			@ApiImplicitParam(name = "reasonForEnable", value = "会员启用原因（可不填）", required = false, dataType = "String") })
	@GetMapping(value = "/enableMember", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "会员启用")
	public Result<String> enableMember(@RequestParam(value = "memberGuid") String memberGuid,
			@RequestParam(value = "reasonForEnable") String reasonForEnable) {
		log.info("会员启用接口入参,memberGuid={}", memberGuid);
		boolean disable = memberListClientNewService.usable(memberGuid, reasonForEnable);
		if (disable) {
			return Result.buildEmptySuccess();
		}
		return Result.buildSystemErrResult("会员启用失败！请重试！");
	}
	/**
	 * 会员删除
	 *
	 * @param memberGuid 会员的guid
	 */
	@ApiOperation(value = "会员删除", notes = "会员删除")
	@ApiImplicitParams({ @ApiImplicitParam(name = "memberGuid", value = "会员guid", required = true, dataType = "String"),
			@ApiImplicitParam(name = "reasonForDel", value = "会员删除原因", required = true, dataType = "String") })
	@GetMapping(value = "/deleteMember", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "会员删除")
	public Result<String> deleteMember(String memberGuid, String reasonForDel) {
		log.info("会员删除接口入参,memberGuid={}", memberGuid);
		boolean disable = memberListClientNewService.delete(memberGuid, reasonForDel);
		if (disable) {
			return Result.buildEmptySuccess();
		}
		return Result.buildSystemErrResult("会员删除失败！请重试！");
	}
	/**
	 * 新增会员
	 *
	 * @param memberSaveReqDTO 会员的guid
	 */
	@ApiOperation(value = "新增会员", notes = "新增会员")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "MemberSaveReqDTO", value = "会员基础信息dto", required = true, dataType = "MemberSaveReqDTO") })
	@PostMapping(value = "/addMember", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "新增会员")
	public Result<String> addMember(@RequestBody MemberSaveReqDTO memberSaveReqDTO) {
		log.info("新增会员接口入参,memberSaveReqDTO={}", memberSaveReqDTO);
		boolean disable = memberListClientNewService.save(memberSaveReqDTO);
		if (disable) {
			return Result.buildEmptySuccess();
		}
		return Result.buildSystemErrResult("会员增加失败！请重试！");
	}
}
