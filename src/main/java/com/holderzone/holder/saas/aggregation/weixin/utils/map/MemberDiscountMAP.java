package com.holderzone.holder.saas.aggregation.weixin.utils.map;

import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.weixin.member.MemberDiscountDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MemberDiscountMAP {

	MemberDiscountMAP INSTANCE = Mappers.getMapper(MemberDiscountMAP.class);

	MemberDiscountDTO toMemberDiscount(DiscountFeeDetailDTO discountFeeDetailDTO);

	List<MemberDiscountDTO> toMemberDiscountList(List<DiscountFeeDetailDTO> discountFeeDetailDTOS);
}
