package com.holderzone.holder.saas.aggregation.merchant.controller.retail;

import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.retail.RetailClientService;
import com.holderzone.saas.store.dto.order.SettlementRulesDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TradeMerchantController
 * @date 2019/10/10 17:23
 * @description //TODO
 * @program IdeaProjects
 */
@RestController
@RequestMapping("/retail")
@Api(description = "trade端相关接口")
public class RetailMerchantController {

    @Autowired
    RetailClientService retailClientService;

    @ApiOperation(value = "获取所有规则", notes = "订单详情")
    @GetMapping("/hst_settlement_rules/get_settlement_rules")
    public Result<List<SettlementRulesDTO>> getSettlementRules() {
        return Result.buildSuccessResult(retailClientService.getSettlementRules());
    }
}