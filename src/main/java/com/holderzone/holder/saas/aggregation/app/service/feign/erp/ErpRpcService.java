package com.holderzone.holder.saas.aggregation.app.service.feign.erp;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.erp.erpretail.RepertorySumDTO;
import com.holderzone.saas.store.dto.erp.erpretail.req.*;
import com.holderzone.saas.store.dto.erp.erpretail.resp.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-erp", fallbackFactory = ErpRpcService.FallbackFactoryImpl.class)
public interface ErpRpcService {

    @PostMapping("/repertory/query_repertory_sum")
    RepertorySumDTO queryRepertorySum(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping("/repertory/insert_repertory")
    boolean insertRepertory(@RequestBody @Validated CreateRepertoryReqDTO createRepertoryReqDTO);

    @PostMapping("/repertory/sale_out_repertory")
    boolean saleOutRepertory(@RequestBody SubstractRepertoryForTradeReqDTO substractRepertoryForTradeReqDTO);

    @PostMapping("/repertory/query_goods_repertory_sum_info")
    Page<GoodsSumInfoRespDTO> queryGoodsRepertorySumInfo(@RequestBody @Validated QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO);

    @PostMapping("/repertory/query_goods_serial")
    Page<GoodsSerialRespDTO> queryGoodsSerial(@RequestBody @Validated QueryGoodsSerialReqDTO queryGoodsSerialReqDTO);

    @PostMapping("/repertory/query_repertory_detail")
    RepertoryDetailInfoRespDTO queryRepertoryDetail(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping("/repertory/query_in_out_repertory_list")
    Page<RepertoryManageRespDTO> queryInOutRepertoryList(@RequestBody @Validated QueryRepertoryManageReqDTO queryRepertoryManageReqDTO);

    @PostMapping("/repertory/invalid_repertory")
    boolean invalidRepertory(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping("/repertory/query_goods_list")
    List<GoodsClassifyAndItemRespDTO> queryGoodsList(@RequestBody SingleDataDTO singleDataDTO);


    @Slf4j
    @Component
    class FallbackFactoryImpl implements FallbackFactory<ErpRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ErpRpcService create(Throwable throwable) {

            return new ErpRpcService() {

                @Override
                public RepertorySumDTO queryRepertorySum(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "queryRepertorySum",
                            JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean insertRepertory(CreateRepertoryReqDTO createRepertoryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "insertRepertory",
                            JacksonUtils.writeValueAsString(createRepertoryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean saleOutRepertory(SubstractRepertoryForTradeReqDTO substractRepertoryForTradeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "saleOutRepertory",
                            JacksonUtils.writeValueAsString(substractRepertoryForTradeReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<GoodsSumInfoRespDTO> queryGoodsRepertorySumInfo(QueryGoodsSumInfoReqDTO queryGoodsSumInfoReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryGoodsRepertorySumInfo",
                            JacksonUtils.writeValueAsString(queryGoodsSumInfoReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<GoodsSerialRespDTO> queryGoodsSerial(QueryGoodsSerialReqDTO queryGoodsSerialReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryGoodsSerial",
                            JacksonUtils.writeValueAsString(queryGoodsSerialReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public RepertoryDetailInfoRespDTO queryRepertoryDetail(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "queryRepertoryDetail",
                            JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<RepertoryManageRespDTO> queryInOutRepertoryList(QueryRepertoryManageReqDTO queryRepertoryManageReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryInOutRepertoryList",
                            JacksonUtils.writeValueAsString(queryRepertoryManageReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public boolean invalidRepertory(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "invalidRepertory",
                            JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<GoodsClassifyAndItemRespDTO> queryGoodsList(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "queryGoodsList",
                            JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}