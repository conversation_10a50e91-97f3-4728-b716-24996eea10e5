package com.holderzone.holder.saas.aggregation.weixin.controller;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.MemberService;
import com.holderzone.holder.saas.aggregation.weixin.service.WxOpenService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxMpMessageHandlerClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxOpenMessageHandleClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxStoreTableClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.WxClientService;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.weixin.req.WxPortalReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxQueryThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxThirdPartUserInfoReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxThirdPartUserInfoRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yu Ren
 * @date 2020/9/15 12:29
 * @description
 */
@Api("微信对外开放API")
@Slf4j
@RestController
@RequestMapping("/wx_open")
public class WxOpenController {

    @Autowired
    private WxStoreTableClientService wxStoreTableClientService;

    @Autowired
    private WxClientService wxClientService;

    @Autowired
    private WxOpenMessageHandleClientService wxMessageHandleClientService;

    @Autowired
    private WxMpMessageHandlerClientService wxMpMessageHandlerClientService;

    @Autowired
    private WxOpenService wxOpenService;

    @Autowired
    private MemberService memberService;


    @ApiOperation("根据条件查询桌台（可查门店下所有）")
    @PostMapping(value = "/query_table_by_web")
    public Result<List<TableBasicDTO>> queryTableByWeb(@RequestBody TableBasicQueryDTO tableBasicQueryDTO) {
        log.info("查询桌台入参{}", JacksonUtils.writeValueAsString(tableBasicQueryDTO));
        EnterpriseIdentifier.setEnterpriseGuid(tableBasicQueryDTO.getEnterpriseGuid());
        List<TableBasicDTO> tableBasicDTOS = wxStoreTableClientService.queryTableByWeb(tableBasicQueryDTO);
        log.info("查询桌台数据：{}", tableBasicDTOS);
        return Result.buildSuccessResult(tableBasicDTOS);
    }


    @PostMapping("/save_or_update_third_part_user_info")
    @ApiOperation(value = "新增或更新第三方会员信息")
    public Result<Boolean> saveOrUpdateThirdPartUserInfo(@RequestBody WxThirdPartUserInfoReqDTO wxThirdPartUserInfoReqDTO) {
        log.info("新增或更新第三方会员信息请求入参：wxThirdPartUserInfoReqDTO{}", JacksonUtils.writeValueAsString(wxThirdPartUserInfoReqDTO));
        return Result.buildSuccessResult(wxClientService.saveOrUpdateThirdPartUserInfo(wxThirdPartUserInfoReqDTO));
    }


    @PostMapping("/check_third_part_user_info")
    @ApiOperation(value = "校验第三方会员信息")
    public Result<WxThirdPartUserInfoRespDTO> checkThirdPartUserInfo(@RequestBody WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO) {
        log.info("校验第三方会员信息请求入参：wxQueryThirdPartUserInfoReqDTO{}", JacksonUtils.writeValueAsString(wxQueryThirdPartUserInfoReqDTO));
        return Result.buildSuccessResult(wxClientService.checkThirdPartUserInfo(wxQueryThirdPartUserInfoReqDTO));
    }

    @PostMapping("/query_third_part_user_info")
    @ApiOperation(value = "查询第三方会员信息")
    public Result<WxThirdPartUserInfoRespDTO> queryThirdPartUserInfo(@RequestBody WxQueryThirdPartUserInfoReqDTO wxQueryThirdPartUserInfoReqDTO) {
        log.info("查询第三方会员信息请求入参：wxQueryThirdPartUserInfoReqDTO{}", JacksonUtils.writeValueAsString(wxQueryThirdPartUserInfoReqDTO));
        return Result.buildSuccessResult(wxClientService.queryThirdPartUserInfo(wxQueryThirdPartUserInfoReqDTO));
    }

    /**
     * 会员个人中心
     * 外部开放
     */
    @GetMapping("/member_login")
    public void openMemberLogin(String t, String accessToken,
                                @RequestParam(value = "thirdOpenId", required = false) String thirdOpenId,
                                @RequestParam(value = "thirdAppId", required = false) String thirdAppId, HttpServletResponse response) {
        log.info("会员个人中心登录请求入参：{}, accessToken:{}, thirdOpenId:{}, thirdAppId:{}", t, accessToken,
                thirdOpenId, thirdAppId);
        // t: 2506261041145830008,2506261041250640009,7345287049672392704
        String[] splitParams = t.split(",");
        if (splitParams.length != 3) {
            throw new BusinessException("参数有误");
        }
        if (StringUtils.isEmpty(accessToken)) {
            throw new BusinessException("参数有误");
        }
        String enterpriseGuid = splitParams[0];
        String operSubjectGuid = splitParams[1];
        String memberInfoGuid = splitParams[2];
        UserContext userContext = UserContextUtils.get();
        userContext.setEnterpriseGuid(enterpriseGuid);
        userContext.setOperSubjectGuid(operSubjectGuid);
        UserContextUtils.put(userContext);
        EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
        // 验证accessToken是否有效
        wxOpenService.verifyAccessToken(enterpriseGuid, accessToken);
        // 验证会员是否存在
        memberService.verifyMemberExist(operSubjectGuid, memberInfoGuid);
        String redirectUrl = wxMessageHandleClientService.openMemberLogin(WxPortalReqDTO.builder()
                .enterpriseGuid(enterpriseGuid)
                .memberInfoGuid(memberInfoGuid)
                .operSubjectGuid(operSubjectGuid)
                .thirdOpenId(thirdOpenId)
                .thirdAppId(thirdAppId)
                .build());
        try {
            log.info("会员个人中心重定向跳转,redirectUrl：{}", redirectUrl);
            response.sendRedirect(redirectUrl);
        } catch (IOException e) {
            log.error("会员个人中心重定向失败,redirectUrl：“{}", redirectUrl, e);
        }
    }

    /**
     * 扫码点餐首页
     * 外部开放
     */
    @GetMapping("/qr")
    @ApiOperation(value = "扫码点餐重定向")
    public void redirect(String t, String accessToken,
                         @RequestParam(value = "thirdOpenId", required = false) String thirdOpenId,
                         @RequestParam(value = "thirdAppId", required = false) String thirdAppId, HttpServletResponse response) throws IOException {
        log.info("扫码默认二维码重定向入参：{}, accessToken:{}, thirdOpenId:{}, thirdAppId:{}", t, accessToken,
                thirdOpenId, thirdAppId);
        // t: 2506261041145830008,28,2506261041250640009,7345287049672392704
        String[] splitParams = t.split(",");
        if (splitParams.length != 4) {
            throw new BusinessException("参数有误");
        }
        if (StringUtils.isEmpty(accessToken)) {
            throw new BusinessException("参数有误");
        }
        String enterpriseGuid = splitParams[0];
        String operSubjectGuid = splitParams[2];
        String memberInfoGuid = splitParams[3];
        UserContext userContext = UserContextUtils.get();
        userContext.setEnterpriseGuid(enterpriseGuid);
        userContext.setOperSubjectGuid(operSubjectGuid);
        UserContextUtils.put(userContext);
        EnterpriseIdentifier.setEnterpriseGuid(userContext.getEnterpriseGuid());
        // 验证accessToken是否有效
        wxOpenService.verifyAccessToken(enterpriseGuid, accessToken);
        // 验证会员是否存在
        memberService.verifyMemberExist(operSubjectGuid, memberInfoGuid);
        // replace
        t = rebuildQrRedirectParams(splitParams, thirdAppId, thirdOpenId);
        log.info("扫码默认二维码重定向t：{}", t);
        String redirectUrl = wxMpMessageHandlerClientService.qrRedirect(t);
        log.info("默认二维码重定向：{}", redirectUrl);
        response.sendRedirect(redirectUrl);
    }


    private String rebuildQrRedirectParams(String[] splitParams, String thirdAppId, String thirdOpenId) {
        List<String> newParams = Arrays.stream(splitParams).collect(Collectors.toList());
        if (!StringUtils.isEmpty(thirdAppId)) {
            newParams.add(thirdAppId);
        }
        if (!StringUtils.isEmpty(thirdOpenId)) {
            newParams.add(thirdOpenId);
        }
        return String.join(",", newParams);
    }
}
