package com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.takeaway.*;
import com.holderzone.saas.store.dto.takeaway.request.*;
import com.holderzone.saas.store.dto.takeaway.response.*;
import com.holderzone.saas.store.dto.trade.ItemPageQuery;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayClientService
 * @date 2018/09/08 11:15
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-takeaway-consumer", fallbackFactory = TakeoutConsumerService.ServiceFallBack.class)
public interface TakeoutConsumerService {

    @PostMapping("/takeout/query_auth_by_type")
    List<StoreAuthDTO> queryAuthByType(@RequestBody StoreAuthByTypeReqDTO storeAuthByTypeReqDTO);

    @PostMapping("/takeout/query_takeout_auth_by_store")
    List<StoreAuthDTO> queryTakeoutAuthByStore(@RequestBody StoreAuthByStoreReqDTO storeAuthByStoreReqDTO);

    @PostMapping("/takeout/query_tuangou_auth_by_store")
    List<StoreAuthDTO> queryTuanGouAuthByStore(@RequestBody StoreAuthByStoreReqDTO storeAuthByStoreReqDTO);

    @PostMapping("/takeout/queryDish")
    String queryDish(@RequestBody QueryDishReqDTO queryDishReqDTO);

    @PostMapping("/takeout/list_order")
    List<TakeoutOrderDTO> listOrder(@RequestBody AppInitializeReqDTO appInitializeReqDTO);

    @PostMapping("/takeout/get_order")
    TakeoutOrderDTO getOrderDetail(@RequestBody AppInitializeReqDTO appInitializeReqDTO);

    @PostMapping("/takeout/accept_order")
    Result<Void> acceptOrder(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/cancel_order")
    Result<Void> cancelOrder(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/agree_cancel_req")
    Result<Void> agreeCancelReq(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/disagree_cancel_req")
    Result<Void> disAgreeCancelReq(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/agree_refund_req")
    Result<Void> agreeRefundReq(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/disagree_refund_req")
    Result<Void> disagreeRefundReq(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/reply_remind_order")
    Result<Void> replyRemindOrder(@RequestBody TakeoutOrderDTO takeawayOrderDTO);

    @PostMapping("/takeout/order_update")
    OwnCallbackResponse orderUpdate(@RequestBody SalesUpdateDTO salesUpdateDTO);

    @PostMapping("/item_mapping/query")
    TakeoutItemMappingRespDTO getItemBinding(@RequestBody TakeoutItemMappingReqDTO takeoutItemMappingReqDTO);

    @PostMapping("/item_mapping/query_items")
    TakeawayBatchMappingResult getItems(@RequestBody UnItemQueryReq unItemQueryReq);

    @PostMapping("/item_mapping/update-bind-extend-info/{storeGuid}/{takeoutType}")
    void updateBindExtendInfo(@PathVariable("storeGuid") String storeGuid, @PathVariable("takeoutType") String takeoutType, @RequestBody UnMappedItem item);

    @PostMapping("/item_mapping/own/query")
    TakeoutItemMappingRespDTO getOwnItemBinding(@RequestBody TakeoutItemMappingReqDTO takeoutItemMappingReqDTO);

    @PostMapping("/item_mapping/bind")
    void bindItem(@RequestBody UnItemBindUnbindReq unItemBindUnbindReq);

    @ApiOperation(value = "批量绑定外卖商品")
    @PostMapping("/item_mapping/batch_bind")
    void batchBind(@RequestBody UnItemBatchBindUnbindReq unItemBatchBindUnbindReq);

    @PostMapping("/item_mapping/unbind")
    void unbindItem(@RequestBody UnItemBindUnbindReq unItemBindUnbindReq);

    @ApiOperation(value = "批量解绑外卖商品")
    @PostMapping("/item_mapping/batch_unbind_out")
    void batchUnbind(@RequestBody UnItemBatchBindUnbindReq batchBindUnbindReq);

    @PostMapping("/item_mapping/batch_unbind")
    void batchUnbindItem(@RequestBody UnItemBatchUnbindReq unItemBatchUnbindReq);

    @PostMapping("/takeout/update_delivery")
    Boolean updateDelivery(@RequestBody StoreAuthDTO storeAuthDTO);

    @PostMapping("/takeout/delivery_change")
    void deliveryChange(@RequestBody TakeoutDeliveryChange takeoutDeliveryChange);

    @PostMapping("/takeout/delivery_location")
    void deliveryLocation(@RequestBody TakeoutDeliveryChange takeoutDeliveryChange);

    @PostMapping("/order_report/takeoutOrder/page")
    Page<BusinessTakeoutOrderRespDTO> getTakeoutOrderPage(@RequestBody @Validated BusinessTakeoutOrderReqDTO reqDTO);

    @ApiOperation(value = "统计订单 订单明细")
    @GetMapping("/order_report/takeoutOrder/detail")
    BusinessTakeoutOrderDetailRespDTO getTakeoutOrderDetail(@RequestParam("orderGuid") String orderGuid);

    @PostMapping("/item_mapping/tcd/sync")
    void syncTcdItemMappingCount(@RequestBody TcdSyncItemMappingDTO syncItemMappingDTO);

    @PostMapping("/takeout/page_order_item_problem")
    Page<TakeoutOrderItemProblemDTO> pageOrderItemProblem(@RequestBody ItemPageQuery query);

    @PostMapping("/takeout/update_batch_item_problem")
    void updateBatchItem(@RequestBody List<TakeoutOrderItemProblemDTO> itemDOList);

    @PostMapping("/takeout/fix/page")
    Page<TakeoutFixRecordDTO> pageInfo(@RequestBody TakeoutRecordQueryDTO queryDTO);

    @GetMapping("/takeout/fix/query")
    List<TakeoutFixItemDTO> listByRecordId(@RequestParam("recordId") Long recordId);

    @PostMapping("/takeout/fix")
    void fix(@RequestBody TakeoutFixDTO takeoutFixDTO);

    /**
     * 外卖异常数据列表
     *
     * @param reqDTO 外卖异常数据列表请求
     * @return 外卖异常数据列表
     */
    @ApiOperation(value = "外卖异常数据列表")
    @PostMapping("/takeout/abnormal_data_page")
    Page<TakeoutItemAbnormalDataRespDTO> pageAbnormalData(@RequestBody TakeoutItemAbnormalDataReqDTO reqDTO);

    /**
     * 数据修复列表
     *
     * @param reqDTO 数据修复列表请求
     * @return 数据修复列表
     */
    @ApiOperation(value = "数据修复列表")
    @PostMapping("/takeout/data_fix_list")
    List<TakeoutItemDataFixRespDTO> listDataFix(@RequestBody TakeoutItemDataFixReqDTO reqDTO);


    @GetMapping("/takeout/fix/query/storeGuids")
    List<String> queryStoreGuids(@RequestParam("recordId") Long recordId);

    /**
     * 外卖异常数据分页查询
     *
     * @param reqDTO 外卖异常数据分页列表请求
     * @return 外卖异常数据分页列表
     */
    @ApiOperation(value = "外卖异常数据分页查询")
    @PostMapping("/abnormal_data/page")
    Page<TakeoutItemAbnormalDataRespDTO> page(@RequestBody @Valid TakeoutItemAbnormalDataReqDTO reqDTO);

    /**
     * 数据修复列表
     *
     * @param reqDTO 数据修复列表请求
     * @return 数据修复列表
     */
    @ApiOperation(value = "数据修复列表")
    @PostMapping("/abnormal_data/data_fix_list")
    List<TakeoutItemDataFixRespDTO> listDataFix2(@RequestBody @Valid TakeoutItemDataFixReqDTO reqDTO);

    /**
     * 外卖商品迁移异常数据
     */
    @ApiOperation(value = "外卖商品迁移异常数据")
    @PostMapping("/abnormal_data/move")
    void move(MoveDTO moveDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<TakeoutConsumerService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TakeoutConsumerService create(Throwable cause) {
            return new TakeoutConsumerService() {

                @Override
                public List<StoreAuthDTO> queryAuthByType(StoreAuthByTypeReqDTO storeAuthByTypeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryAuthByType", JacksonUtils.writeValueAsString(storeAuthByTypeReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<StoreAuthDTO> queryTakeoutAuthByStore(StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryTakeoutAuthByStore", JacksonUtils.writeValueAsString(storeAuthByStoreReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<StoreAuthDTO> queryTuanGouAuthByStore(StoreAuthByStoreReqDTO storeAuthByStoreReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryTuanGouAuthByStore", JacksonUtils.writeValueAsString(storeAuthByStoreReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public String queryDish(QueryDishReqDTO queryDishReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryDish", JacksonUtils.writeValueAsString(queryDishReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<TakeoutOrderDTO> listOrder(AppInitializeReqDTO appInitializeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "listOrder", JacksonUtils.writeValueAsString(appInitializeReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutOrderDTO getOrderDetail(AppInitializeReqDTO appInitializeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "GetOrderDetailGroup", JacksonUtils.writeValueAsString(appInitializeReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Result<Void> acceptOrder(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "acceptOrder", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Result<Void> cancelOrder(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "cancelOrder", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public OwnCallbackResponse orderUpdate(SalesUpdateDTO salesUpdateDTO) {
                    log.error(HYSTRIX_PATTERN, "orderUpdate", JacksonUtils.writeValueAsString(salesUpdateDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Result<Void> agreeCancelReq(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "agreeCancelReq", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Result<Void> disAgreeCancelReq(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "disAgreeCancelReq", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Result<Void> agreeRefundReq(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "agreeRefundReq", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Result<Void> disagreeRefundReq(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "disagreeRefundReq", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Result<Void> replyRemindOrder(TakeoutOrderDTO takeawayOrderDTO) {
                    log.error(HYSTRIX_PATTERN, "replyRemindOrder", JacksonUtils.writeValueAsString(takeawayOrderDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutItemMappingRespDTO getItemBinding(TakeoutItemMappingReqDTO takeoutItemMappingReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getItemBinding", JacksonUtils.writeValueAsString(takeoutItemMappingReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeawayBatchMappingResult getItems(UnItemQueryReq unItemQueryReq) {
                    log.error(HYSTRIX_PATTERN, "getItems", JacksonUtils.writeValueAsString(unItemQueryReq),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void updateBindExtendInfo(String storeGuid, String takeoutType, UnMappedItem item) {
                    log.error(HYSTRIX_PATTERN, "updateItemBindExtendInfo", storeGuid + "-" + takeoutType + "-" + JacksonUtils.writeValueAsString(item),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutItemMappingRespDTO getOwnItemBinding(TakeoutItemMappingReqDTO takeoutItemMappingReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getOwnItemBinding", JacksonUtils.writeValueAsString(takeoutItemMappingReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void bindItem(UnItemBindUnbindReq unItemBindUnbindReq) {
                    log.error(HYSTRIX_PATTERN, "bindItem", JacksonUtils.writeValueAsString(unItemBindUnbindReq),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void batchBind(UnItemBatchBindUnbindReq unItemBatchBindUnbindReq) {
                    log.error(HYSTRIX_PATTERN, "batchBind", JacksonUtils.writeValueAsString(unItemBatchBindUnbindReq),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void unbindItem(UnItemBindUnbindReq unItemBindUnbindReq) {
                    log.error(HYSTRIX_PATTERN, "unbindItem", JacksonUtils.writeValueAsString(unItemBindUnbindReq),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void batchUnbind(UnItemBatchBindUnbindReq batchBindUnbindReq) {
                    log.error(HYSTRIX_PATTERN, "batchUnbind", JacksonUtils.writeValueAsString(batchBindUnbindReq),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void batchUnbindItem(UnItemBatchUnbindReq unItemBatchUnbindReq) {
                    log.error(HYSTRIX_PATTERN, "batchUnbindItem", JacksonUtils.writeValueAsString(unItemBatchUnbindReq),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Boolean updateDelivery(StoreAuthDTO storeAuthDTO) {
                    log.error(HYSTRIX_PATTERN, "updateDelivery", JacksonUtils.writeValueAsString(storeAuthDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void deliveryChange(TakeoutDeliveryChange takeoutDeliveryChange) {
                    log.error(HYSTRIX_PATTERN, "deliveryChange", JacksonUtils.writeValueAsString(takeoutDeliveryChange),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void deliveryLocation(TakeoutDeliveryChange takeoutDeliveryChange) {
                    log.error(HYSTRIX_PATTERN, "deliveryLocation", JacksonUtils.writeValueAsString(takeoutDeliveryChange),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Page<BusinessTakeoutOrderRespDTO> getTakeoutOrderPage(BusinessTakeoutOrderReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "getTakeoutOrderPage", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public BusinessTakeoutOrderDetailRespDTO getTakeoutOrderDetail(String orderGuid) {
                    log.error(HYSTRIX_PATTERN, "getTakeoutOrderDetail", orderGuid,
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void syncTcdItemMappingCount(TcdSyncItemMappingDTO syncItemMappingDTO) {
                    log.error(HYSTRIX_PATTERN, "syncTcdItemMappingCount", JacksonUtils.writeValueAsString(syncItemMappingDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Page<TakeoutOrderItemProblemDTO> pageOrderItemProblem(ItemPageQuery query) {
                    log.error(HYSTRIX_PATTERN, "pageOrderItemProblem", JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void updateBatchItem(List<TakeoutOrderItemProblemDTO> itemDOList) {
                    log.error(HYSTRIX_PATTERN, "updateBatchItem", JacksonUtils.writeValueAsString(itemDOList),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Page<TakeoutFixRecordDTO> pageInfo(TakeoutRecordQueryDTO queryDTO) {
                    log.error(HYSTRIX_PATTERN, "pageInfo", JacksonUtils.writeValueAsString(queryDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<TakeoutFixItemDTO> listByRecordId(Long recordId) {
                    log.error(HYSTRIX_PATTERN, "listByRecordId", recordId, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void fix(TakeoutFixDTO takeoutFixDTO) {
                    log.error(HYSTRIX_PATTERN, "fix", JacksonUtils.writeValueAsString(takeoutFixDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Page<TakeoutItemAbnormalDataRespDTO> pageAbnormalData(TakeoutItemAbnormalDataReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "pageAbnormalData", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<TakeoutItemDataFixRespDTO> listDataFix(TakeoutItemDataFixReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "listDataFix", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<String> queryStoreGuids(Long recordId) {
                    log.error(HYSTRIX_PATTERN, "queryStoreGuids", recordId, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Page<TakeoutItemAbnormalDataRespDTO> page(@Valid TakeoutItemAbnormalDataReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "page", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<TakeoutItemDataFixRespDTO> listDataFix2(@Valid TakeoutItemDataFixReqDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "listDataFix2", JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void move(MoveDTO moveDTO) {
                    log.error(HYSTRIX_PATTERN, "move", JacksonUtils.writeValueAsString(moveDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
