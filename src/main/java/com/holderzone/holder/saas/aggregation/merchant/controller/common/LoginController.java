package com.holderzone.holder.saas.aggregation.merchant.controller.common;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.response.ResultEnum;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.BaseService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.cloud.CloudAggClientService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 * @version 1.0
 * @className LoginController
 * @date 2018/10/12 17:35
 * @description V2.0中商户后台登陆直接调用holder-saas-sso服务，该类被废弃
 * @program holder-saas-aggregation-merchant
 */
@Deprecated
@Api(description = "登录/登出")
@RestController
@RequestMapping("/sso")
@Slf4j
public class LoginController {

    private final CloudAggClientService cloudAggClientService;

    private final BaseService baseService;

    @Value("${validate.verify-code-img:true}")
    private boolean validateVerifyCodeImg;

    @Autowired
    public LoginController(CloudAggClientService cloudAggClientService, BaseService baseService) {
        this.cloudAggClientService = cloudAggClientService;
        this.baseService = baseService;
    }

/*    @ApiOperation(value = "登录接口")
    @PostMapping("/login")
    public Result login(@RequestBody LoginDTO loginDTO) {
        if (validateVerifyCodeImg && !baseService.validateVerifyCodeImg(loginDTO.getVid(), loginDTO.getvCode())) {
            return Result.buildFailResult(ResultEnum.INVALID_PARAMETER.getResultCode(), "验证码错误");
        }
        String result = cloudAggClientService.login(loginDTO);
        log.info("用户登录，loginDTO：{}，云端返回的登陆结果为：{}", JacksonUtils.writeValueAsString(loginDTO), result);
        if (!StringUtils.hasText(result)) {
            return Result.buildOpFailedResult("商户号或用户名或密码错误");
        }
        return Result.buildSuccessResult(result);
    }*/

    @ApiOperation(value = "登出接口")
    @PostMapping("/logout")
    public Result logout(HttpServletRequest request) {
        boolean result = cloudAggClientService.logout(request.getHeader("token"), request.getHeader("source"));
        if (!result) {
            return Result.buildFailResult(ResultEnum.NO_SUCH_APPLICATION_EXISTS.getResultCode(), "退出失败");
        }
        return Result.buildSuccessResult(true);
    }

    @ApiOperation("获取验证码接口")
    @GetMapping("/verifycode")
    public byte[] verifycodeImg(@ApiParam("32位字母或数字") @RequestParam(name = "id") String id) {
        return baseService.getVerifyCodeImg(id);
    }
}