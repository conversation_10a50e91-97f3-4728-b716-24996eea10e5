package com.holderzone.holder.saas.aggregation.merchant.aop;

import cn.hutool.json.JSONUtil;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.config.OpenApiConfig;
import com.holderzone.holder.saas.aggregation.merchant.entity.vo.EnterpriseDTO;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.erp.EnterpriseRpcService;
import com.holderzone.holder.saas.aggregation.merchant.util.SignatureUtil;
import com.holderzone.resource.common.dto.enterprise.EnterpriseOpenApiConfigDTO;
import com.holderzone.resource.common.dto.enterprise.EnterpriseQueryDTO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * openapi
 * 验证签名
 */
@Component
@Aspect
@Slf4j
public class OpenApiRequestAspect {


    @Resource
    private OpenApiConfig openApiConfig;

    @Resource
    private EnterpriseRpcService enterpriseRpcService;

    @Pointcut("execution(* com.holderzone.holder.saas.aggregation.merchant.controller.openapi.*.*(..))")
    public void pointcut() {
    }

    @Around("pointcut()")
    public Object handle(ProceedingJoinPoint joinPoint) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        final String[] parameterNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
        if (parameterNames == null) {
            log.info("接口请求:{},入参无", request.getRequestURI());
            return joinPoint.proceed();
        }
        Object[] args = joinPoint.getArgs();
        Map<String, Object> params = new HashMap<>();
        for (int i = 0; i < parameterNames.length; i++) {
            Object arg = args[i];
            Map<String, Object> valueMap = JacksonUtils.toMap(JacksonUtils.writeValueAsString(arg));
            params.putAll(valueMap);
        }
        Object signatureObj = params.get(SignatureUtil.KEY_SIGNATURE);
        Object now = params.get(SignatureUtil.KEY_NOW);
        if (Objects.isNull(signatureObj) || Objects.isNull(now)) {
            throw new BusinessException("缺少必传参数！");
        }
        //判断接口请求时间是否符合(10s内有效)
        LocalDateTime threadTime = DateTimeUtils.mills2LocalDateTime(Long.parseLong(now.toString()));
        if (threadTime.plusSeconds(10).isBefore(LocalDateTime.now())) {
            throw new BusinessException("请求时间已过期！");
        }
        // 验签
        verifySignature(params);
        log.info("接口请求:{},入参 => {}", request.getRequestURI(), JSONUtil.toJsonStr(params));
        return joinPoint.proceed();
    }

    private void verifySignature(Map<String, Object> params) {
        Object enterpriseGuidObj = params.get(SignatureUtil.ENTERPRISE_GUID);
        Object signatureObj = params.get(SignatureUtil.KEY_SIGNATURE);
        String apiKey = openApiConfig.getApiKey();
        String apiSecret = openApiConfig.getApiSecret();
        if (Objects.nonNull(enterpriseGuidObj)) {
            // 根据企业验签
            EnterpriseQueryDTO enterpriseQueryDTO = new EnterpriseQueryDTO();
            enterpriseQueryDTO.setEnterpriseGuid(String.valueOf(enterpriseGuidObj));
            EnterpriseDTO enterprise = enterpriseRpcService.findEnterprise(enterpriseQueryDTO);
            if (Objects.isNull(enterprise)) {
                throw new BusinessException("enterpriseGuid有误！");
            }
            if (Boolean.FALSE.equals(enterprise.getSupportOpenApi())) {
                throw new BusinessException("未开通开放接口，请联系管理员开通！");
            }
            EnterpriseOpenApiConfigDTO cloudOpenApiConfig = enterprise.getOpenApiConfig();
            if (Objects.isNull(cloudOpenApiConfig)) {
                throw new BusinessException("开放API配置异常，请联系管理员！");
            }
            apiKey = cloudOpenApiConfig.getApiKey();
            apiSecret = cloudOpenApiConfig.getApiSecret();
        }
        log.info("当前企业apiKey：{}, apiSecret:{}", apiKey, apiSecret);
        String signature = SignatureUtil.getSignature(apiKey, apiSecret, params);
        if (!Objects.equals(signature, signatureObj)) {
            log.error("参数签名错误, 参数:{}", JacksonUtils.writeValueAsString(params));
            throw new BusinessException("参数签名错误！");
        }
    }

}