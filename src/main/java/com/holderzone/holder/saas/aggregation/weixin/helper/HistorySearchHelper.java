package com.holderzone.holder.saas.aggregation.weixin.helper;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.holderzone.holder.saas.aggregation.weixin.entity.cmember.HistorySearchDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2024-09-12
 * @description 搜索历史工具
 */
@Component
@RequiredArgsConstructor
public class HistorySearchHelper {

    private static final int MAX_SEARCH_SIZE = 20;

    private static final long MAX_TIMEOUT = 30;

    private final StringRedisTemplate redisTemplate;

    public void putHistorySearchRecord(HistorySearchDTO historySearch) {
        historySearch.verifyParams();
        ZSetOperations<String, String> zSetOperations = redisTemplate.opsForZSet();
        //添加历史记录
        String key = historySearch.cacheKey();
        zSetOperations.add(key,historySearch.getContent(),System.currentTimeMillis());
        redisTemplate.expire(key,MAX_TIMEOUT, TimeUnit.DAYS);
        //判断存入得长度
        Long size = zSetOperations.size(key);
        if(size == null){
            return;
        }
        if(size > MAX_SEARCH_SIZE){
            zSetOperations.removeRange(key,0,size - MAX_SEARCH_SIZE - 1);
        }
    }

    public List<String> getHistorySearchRecord(HistorySearchDTO historySearch) {
        List<String> historySearchList = Lists.newArrayList();
        ZSetOperations<String, String> zSetOperations = redisTemplate.opsForZSet();
        //取出最近20条数据并按时间排序
        Set<ZSetOperations.TypedTuple<String>> tuples = zSetOperations.reverseRangeWithScores(historySearch.cacheKey(), 0, MAX_SEARCH_SIZE - 1);
        if(tuples == null || CollectionUtil.isEmpty(tuples)){
            return historySearchList;
        }
        tuples.forEach(tuple -> historySearchList.add(tuple.getValue()));
        return historySearchList;
    }

    public void removeHistorySearchRecord(HistorySearchDTO historySearch) {
        redisTemplate.delete(historySearch.cacheKey());
    }
}
