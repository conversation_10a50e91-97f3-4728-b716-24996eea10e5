package com.holderzone.holder.saas.aggregation.merchant.controller.weixin;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin.WxReserveConfigClientService;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.weixin.req.WxReserveConfigDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxReserveConfigController
 * @date 2019/12/16 17:52
 * @description
 * @program holder-saas-store
 */
@RestController
@Slf4j
@RequestMapping("/wx_reserve")
@Api("微信预定配置")
public class WxReserveConfigController {

    private final WxReserveConfigClientService wxReserveConfigClientService;

    @Autowired
    public WxReserveConfigController(WxReserveConfigClientService wxReserveConfigClientService) {
        this.wxReserveConfigClientService = wxReserveConfigClientService;
    }

    @PostMapping("/list_config")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN)
    public Result<Page<WxReserveConfigDTO>> listConfig(@RequestBody WxStorePageReqDTO wxStorePageReqDTO) {
        return Result.buildSuccessResult(wxReserveConfigClientService.listConfig(wxStorePageReqDTO));
    }

    @PostMapping("/update_config")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN)
    public Result<Boolean> updateConfig(@RequestBody WxReserveConfigDTO wxReserveConfigDTO) {
        return Result.buildSuccessResult(wxReserveConfigClientService.updateConfig(wxReserveConfigDTO));
    }

    @PostMapping("/get_config")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN)
    public Result<WxReserveConfigDTO> getConfig(@RequestBody SingleDataDTO singleDataDTO) {
        return Result.buildSuccessResult(wxReserveConfigClientService.getConfig(singleDataDTO));
    }
}
