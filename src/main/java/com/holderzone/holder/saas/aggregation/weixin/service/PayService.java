package com.holderzone.holder.saas.aggregation.weixin.service;

import com.holderzone.holder.saas.aggregation.weixin.entity.dto.DealMemberPayReqDTO;
import com.holderzone.saas.store.dto.weixin.WxPrepayRespDTO;
import com.holderzone.saas.store.dto.weixin.deal.MemberPayReusltRespDTO;
import com.holderzone.saas.store.dto.weixin.deal.WeChatCancelPayReqDTO;
import com.holderzone.saas.store.dto.weixin.deal.WeChatH5PayReqDTO;
import com.holderzone.saas.store.dto.weixin.deal.ZeroPayResultRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.PayWayRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;

import javax.servlet.http.HttpServletRequest;

public interface PayService {

	WxPrepayRespDTO prepay(String orderGuid);

	PayWayRespDTO getAllPayWay(String orderGuid);

	ZeroPayResultRespDTO zeroPay(DealMemberPayReqDTO dealMemberPayReqDTO);

	MemberPayReusltRespDTO memberPay(DealMemberPayReqDTO dealMemberPayReqDTO);

	WxPayRespDTO aggPay(WeChatH5PayReqDTO weChatPayReqDTO, HttpServletRequest request);

	void appletCancelPay(WeChatCancelPayReqDTO weChatCancelPayReqDTO);
}
