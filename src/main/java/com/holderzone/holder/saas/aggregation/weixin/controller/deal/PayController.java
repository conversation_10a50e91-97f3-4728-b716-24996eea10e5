package com.holderzone.holder.saas.aggregation.weixin.controller.deal;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.DealMemberPayReqDTO;
import com.holderzone.holder.saas.aggregation.weixin.service.CalculateService;
import com.holderzone.holder.saas.aggregation.weixin.service.PayService;
import com.holderzone.saas.store.dto.weixin.WxPrepayRespDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.resp.CalculateOrderRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.PayWayRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;


/**
 * 小程序/H5支付
 */
@Slf4j
@RestController
@RequestMapping("/deal/pay")
@RequiredArgsConstructor
public class PayController {

    private final PayService payService;

    private final CalculateService calculateService;

    @ApiOperation("计算金额")
    @PostMapping("/calculate")
    public Result<CalculateOrderRespDTO> calculate(@RequestBody CalculateOrderDTO queryDTO) {
        log.info("计算金额入参:{}", JacksonUtils.writeValueAsString(queryDTO));
        return Result.buildSuccessResult(calculateService.calculate(queryDTO));
    }

    @ApiOperation("获取所有支付方式")
    @GetMapping("/way")
    public Result<PayWayRespDTO> getAllPayWay(@RequestParam("orderGuid") String orderGuid) {
        log.info("支付方式入参:{}", orderGuid);
        return Result.buildSuccessResult(payService.getAllPayWay(orderGuid));
    }

    @ApiOperation("支付验证")
    @GetMapping(value = "/prepay")
    public Result<WxPrepayRespDTO> prepay(@RequestParam("orderGuid") String orderGuid) {
        log.info("买单支付入参:{}", orderGuid);
        return Result.buildSuccessResult(payService.prepay(orderGuid));
    }

    @ApiOperation("0元支付")
    @GetMapping("/zero")
    public Result<ZeroPayResultRespDTO> zeroPay(@RequestParam("orderGuid") String orderGuid) {
        log.info("0元支付入参:{}", orderGuid);
        DealMemberPayReqDTO dealMemberPayReqDTO = new DealMemberPayReqDTO();
        dealMemberPayReqDTO.setOrderGuid(orderGuid);
        dealMemberPayReqDTO.setPayAmount(BigDecimal.ZERO);
        return Result.buildSuccessResult(payService.zeroPay(dealMemberPayReqDTO));
    }

    @ApiOperation("0元支付")
    @PostMapping("/applet/zero")
    public Result<ZeroPayResultRespDTO> appletZeroPay(@RequestBody DealMemberPayReqDTO dealMemberPayReqDTO) {
        log.info("小程序0元支付入参:{}", JacksonUtils.writeValueAsString(dealMemberPayReqDTO));
        dealMemberPayReqDTO.setPayAmount(BigDecimal.ZERO);
        return Result.buildSuccessResult(payService.zeroPay(dealMemberPayReqDTO));
    }

    @ApiOperation(value = "会员支付", notes = "支付")
    @PostMapping("/member")
    public Result<MemberPayReusltRespDTO> memberPay(@RequestBody DealMemberPayReqDTO dealMemberPayReqDTO) {
        log.info("会员支付入参：{}", dealMemberPayReqDTO);
        return Result.buildSuccessResult(payService.memberPay(dealMemberPayReqDTO));
    }

    @ApiOperation("小程序支付 (收益余额支付/储值金额支付)")
    @PostMapping(value = "/applet/member")
    public Result<MemberPayReusltRespDTO> appletMemberPay(@RequestBody DealMemberPayReqDTO dealMemberPayReqDTO) {
        log.info("小程序支付(收益余额支付/储值金额支付)入参:{}", JacksonUtils.writeValueAsString(dealMemberPayReqDTO));
        return Result.buildSuccessResult(payService.memberPay(dealMemberPayReqDTO));
    }

    @ApiOperation("微信支付 (H5支付)")
    @PostMapping(value = "/we_chat")
    public Result<WxPayRespDTO> weChatH5Pay(@RequestBody WeChatH5PayReqDTO weChatH5PayReqDTO) {
        log.info("微信支付入参:{}", JacksonUtils.writeValueAsString(weChatH5PayReqDTO));
        return Result.buildSuccessResult(payService.aggPay(weChatH5PayReqDTO, null));
    }

    @ApiOperation("小程序支付 (微信支付)")
    @PostMapping(value = "/applet/wechat")
    public Result<WxPayRespDTO> wechatAppletPay(@RequestBody WeChatH5PayReqDTO weChatPayReqDTO, HttpServletRequest request) {
        log.info("小程序支付(微信支付)入参:{}", JacksonUtils.writeValueAsString(weChatPayReqDTO));
        weChatPayReqDTO.setDeviceType(BaseDeviceTypeEnum.TCD.getCode());
        return Result.buildSuccessResult(payService.aggPay(weChatPayReqDTO, request));
    }

    @ApiOperation("小程序支付 (支付宝支付)")
    @PostMapping(value = "/applet/alipay")
    public Result<WxPayRespDTO> aliAppletPay(@RequestBody AliPayReqDTO aliPayReqDTO, HttpServletRequest request) {
        log.info("小程序支付(支付宝支付)入参:{}", JacksonUtils.writeValueAsString(aliPayReqDTO));
        aliPayReqDTO.setDeviceType(BaseDeviceTypeEnum.ALI.getCode());
        WeChatH5PayReqDTO weChatPayReqDTO = JacksonUtils.toObject(WeChatH5PayReqDTO.class, JacksonUtils.writeValueAsString(aliPayReqDTO));
        return Result.buildSuccessResult(payService.aggPay(weChatPayReqDTO, request));
    }

    @ApiOperation("微信小程序取消订单")
    @PostMapping(value = "/applet/cancel")
    public Result<Void> wechatAppletCancelPay(@RequestBody WeChatCancelPayReqDTO weChatCancelPay) {
        log.info("微信小程序取消订单入参:{}", JacksonUtils.writeValueAsString(weChatCancelPay));
        weChatCancelPay.setDeviceType(BaseDeviceTypeEnum.TCD.getCode());
        payService.appletCancelPay(weChatCancelPay);
        return Result.buildEmptySuccess();
    }

    @ApiOperation("支付宝小程序取消订单")
    @PostMapping(value = "/applet/alipay/cancel")
    public Result<Void> aliAppletCancelPay(@RequestBody WeChatCancelPayReqDTO weChatCancelPay) {
        log.info("支付宝小程序取消订单入参:{}", JacksonUtils.writeValueAsString(weChatCancelPay));
        weChatCancelPay.setDeviceType(BaseDeviceTypeEnum.ALI.getCode());
        payService.appletCancelPay(weChatCancelPay);
        return Result.buildEmptySuccess();
    }
}
