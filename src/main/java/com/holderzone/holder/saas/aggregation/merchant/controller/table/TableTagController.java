package com.holderzone.holder.saas.aggregation.merchant.controller.table;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.table.TableTagClientService;
import com.holderzone.saas.store.dto.common.BasePageDTO;
import com.holderzone.saas.store.dto.table.TableTagDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableTagController
 * @date 2019/12/06 9:36
 * @description //TODO
 * @program IdeaProjects
 */
@Slf4j
@RestController
@RequestMapping("tag")
@Api(description = "安卓桌台服务Api")
public class TableTagController {

    private final TableTagClientService tableTagClientService;

    public TableTagController(TableTagClientService tableTagClientService) {
        this.tableTagClientService = tableTagClientService;
    }


    @ApiOperation("新建标签")
    @PostMapping("/add")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE,description = "新建标签",action = OperatorType.ADD)
    public Result<Boolean> createTag(@RequestBody @Validated TableTagDTO tableTagDTO){
        log.info("新建标签,log：{}",tableTagDTO);
        return Result.buildSuccessResult(tableTagClientService.createTag(tableTagDTO));
    }

    @ApiOperation("标签列表")
    @PostMapping("/list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE,description = "查询标签",action = OperatorType.SELECT)
    public Result listTag(@RequestBody BasePageDTO basePageDTO){
        log.info("查询标签,log：{}", basePageDTO);
        return  Result.buildSuccessResult(tableTagClientService.listTag(basePageDTO));
    }

    @ApiOperation("更新标签")
    @PostMapping("/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE,description = "更新标签",action = OperatorType.UPDATE)
    public Result<Boolean> updateTag(@RequestBody @Validated  TableTagDTO tableTagDTO){
        log.info("更新标签,log：{}",tableTagDTO);
        return Result.buildSuccessResult(tableTagClientService.updateTag(tableTagDTO));
    }

    @ApiOperation("删除标签")
    @PostMapping("/delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TABLE,description = "删除标签",action = OperatorType.DELETE)
    public Result<Boolean> deleteTag(@RequestBody TableTagDTO tableTagDTO){
        log.info("删除标签,log：{}",tableTagDTO);
        return Result.buildSuccessResult(tableTagClientService.deleteTag(tableTagDTO));
    }

}