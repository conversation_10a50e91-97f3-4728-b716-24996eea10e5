package com.holderzone.holder.saas.aggregation.app.service.feign.member;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.app.utils.MemberResult;
import com.holderzone.holder.saas.member.merchant.dto.card.RequestCanteenCard;
import com.holderzone.holder.saas.member.merchant.dto.card.ResponseCanteenCard;
import com.holderzone.holder.saas.member.merchant.dto.member.RequestQueryMemberConsumption;
import com.holderzone.holder.saas.member.merchant.dto.member.ResponseMemberConsumption;
import com.holderzone.holder.saas.member.merchant.dto.member.ResponseOperationMemberInfo;
import com.holderzone.holder.saas.member.merchant.dto.physical.*;
import com.holderzone.saas.store.dto.member.common.PageDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HssMemberMerchantClientService
 * @date 2021/5/12 17:34
 * @description 会员调用服务接口
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-member-merchant", fallbackFactory = HssMemberMerchantClientService.MemberFallback.class)
public interface HssMemberMerchantClientService {

    /**
     * 校验卡号是否存在
     *
     * @param physicalCardNum 实体卡号
     * @return Boolean
     */
    @ApiOperation("校验卡号是否存在")
    @GetMapping(value = "/hsa_physical_card/checkPhysicalCardNum")
    MemberResult<String> checkPhysicalCardNum(@RequestParam("physicalCardNum") String physicalCardNum);


    /**
     * 根据会员电话查询会员信息
     *
     * @param phone 实体卡号
     * @return ResponseOperationMemberInfo
     */
    @ApiOperation("根据会员电话查询会员信息")
    @GetMapping(value = "/hsa-member/getMemberInfoByPhone")
    MemberResult<ResponseOperationMemberInfo> getMemberInfoByPhone(@RequestParam("phone") String phone);


    /**
     * 校验卡号存在、错误、重复、是否匹配绑定码
     *
     * @param requestMemberInfoPhysicalCardDtoList
     * @return Boolean
     */
    @ApiOperation("校验卡号存在、错误、重复、是否匹配绑定码")
    @PostMapping(value = "/hsa_physical_card/checkPhysicalCardNumPairBindingNum")
    MemberResult<Boolean> checkPhysicalCardNumPairBindingNum(@RequestBody List<PhysicalCardCode> requestMemberInfoPhysicalCardDtoList);


    /**
     * 实体卡绑定会员
     *
     * @param memberInfoPhysicalCardGuid 会员guid
     * @param phone                      电话
     * @param userName                   userName
     * @return ResponseMemberInfoPhysicalCardDto
     */
    @ApiOperation(value = "实体卡绑定会员")
    @PostMapping("/hsa_physical_card/binding/physical_card_member")
    MemberResult<Boolean> physicalCardBindingMember(@RequestParam("memberInfoPhysicalCardGuid") String memberInfoPhysicalCardGuid,
                                                    @RequestParam("phone") String phone,
                                                    @RequestParam("userName") String userName);

    /**
     * 校验手机号是否可用
     *
     * @param physicalCardPhoneNum 实体卡号
     * @return Boolean
     */
    @ApiOperation("校验手机号是否可用")
    @GetMapping(value = "/hsa_physical_card/checkPhysicalCardPhoneNum")
    MemberResult<Boolean> checkPhysicalCardPhoneNum(@RequestParam("physicalCardPhoneNum") String physicalCardPhoneNum);

    /**
     * 批量开卡或单独开卡绑定账户信息
     *
     * @param dto RequestMemberInfoPhysicalCardDto
     * @return ResponseMemberInfoPhysicalCardDto
     */
    @ApiOperation(value = "保存或编辑开卡信息")
    @PostMapping("/hsa_physical_card/save_or_update/physical_card")
    MemberResult<Boolean> saveOrUpdatePhysicalCardInfo(@RequestBody RequestSavePhysicalCardDto dto);

    /**
     * 开卡押金策略查询接口
     *
     * @param query 基础设置公共查询入参
     * @return 企业下所有的开卡押金策略
     */
    @ApiOperation(value = "查询企业下所有的开卡押金策略")
    @PostMapping("/hsa_basic_setup/list_deposit_strategy")
    MemberResult<List<ResponseDepositStrategy>> listDepositStrategy(@RequestBody(required = false) BasicSetupQuery query);

    @PostMapping("/hsa_canteen_pay/get_canteen_card")
    MemberResult<ResponseCanteenCard> getCanteenCard(@RequestBody RequestCanteenCard requestCanteenCard);

    /**
     * 更改实体卡状态
     *
     * @param memberInfoPhysicalCardGuid MemberInfoPhysicalCardGuid
     * @param physicalCardState          physicalCardState
     * @return ResponseMemberInfoPhysicalCardDto
     */
    @ApiOperation(value = "更改实体卡状态")
    @PostMapping("/hsa_physical_card/update/physical_card_state")
    MemberResult<Boolean> updatePhysicalCardState(@ApiParam("实体卡持卡GUID") @RequestParam("memberInfoPhysicalCardGuid") String memberInfoPhysicalCardGuid,
                                                  @ApiParam("卡状态：0 未激活  1 正常 2 已挂失") @RequestParam("physicalCardState") Integer physicalCardState);

    /**
     * 批量激活实体卡
     *
     * @param memberInfoPhysicalCardGuid MemberInfoPhysicalCardGuid
     * @return Boolean
     */
    @ApiOperation(value = "批量激活实体卡")
    @PostMapping("/hsa_physical_card/batch/activate_physical_card")
    MemberResult<Boolean> activationPhysicalCard(@ApiParam("实体卡持卡GUID集合") @RequestBody List<String> memberInfoPhysicalCardGuid);

    /**
     * 余额使用规则保存和编辑接口
     * 如果用户没有设置商户后台的规则，就全部默认禁用
     *
     * @param reqDTO 这里只需要余额使用规则就行,编辑要传rechargeRuleGuid
     * @return 充值规则guid
     */
    @ApiOperation(value = "余额使用规则保存和编辑接口")
    @PostMapping("/hsa_basic_setup/save_or_update_balance_usage_rule")
    MemberResult<String> saveOrUpdateBalanceUsageRule(@RequestBody RequestRechargeRule reqDTO);

    /**
     * 批量生成卡密
     *
     * @param request request
     * @return Boolean
     */
    @ApiOperation(value = "批量生成卡密")
    @PostMapping("/hsa_make_physical_card_record/save/physical_card_dense")
    MemberResult<ResponseMakePhysicalCardRecordDto> batchGenerationPhysicalCard(@RequestBody RequestMakePhysicalCardRecordDto request);

    /**
     * 记录一体机写卡结果
     *
     * @param requestUpdateMakePhysicalCardType requestUpdateMakePhysicalCardType
     */
    @ApiOperation(value = "记录一体机写卡结果")
    @PostMapping("/hsa_make_physical_card_record/updateMakePhysicalCardType")
    MemberResult<Boolean> updateMakePhysicalCardType(@RequestBody RequestUpdateMakePhysicalCardType requestUpdateMakePhysicalCardType);

    /**
     * 余额规则保存和编辑接口
     *
     * @param reqDTO 余额规则保存和编辑信息
     * @return 余额规则guid
     */
    @ApiOperation(value = "余额规则保存和编辑接口")
    @PostMapping("/hsa_basic_setup/save_balance_rule")
    MemberResult<String> saveOrUpdateBalanceRule(@RequestBody RequestBalanceRule reqDTO);

    /**
     * 余额规则查询接口
     *
     * @param query 基础设置公共查询入参
     * @return 企业下的余额规则
     */
    @ApiOperation(value = "余额规则查询接口")
    @PostMapping("/hsa_basic_setup/get_balance_rule")
    MemberResult<ResponseBalanceRule> getBalanceRule(@RequestBody(required = false) BasicSetupQuery query);

    /**
     * 充值规则查询接口
     *
     * @param query 基础设置公共查询入参
     * @return 企业下的充值规则
     */
    @ApiOperation(value = "充值规则查询接口")
    @PostMapping("/hsa_basic_setup/get_recharge_rule")
    MemberResult<ResponseRechargeRule> getRechargeRule(@RequestBody(required = false) BasicSetupQuery query);

    /**
     * 查询完善信息
     *
     * @param memberInfoGuid memberInfoGuid
     * @return Integer
     */
    @ApiOperation(value = "查询完善信息")
    @GetMapping("/hsa-member/checkMemberInfo")
    MemberResult<Integer> checkMemberInfo(@RequestParam("memberInfoGuid") String memberInfoGuid);

    @PostMapping("/hsa-member/getMemberConsumptionPage")
    MemberResult<PageDTO<ResponseMemberConsumption>> getMemberConsumptionPage(@RequestBody RequestQueryMemberConsumption request);

    @Slf4j
    @Component
    class MemberFallback implements FallbackFactory<HssMemberMerchantClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public HssMemberMerchantClientService create(Throwable throwable) {
            return new HssMemberMerchantClientService() {

                @Override
                public MemberResult<ResponseOperationMemberInfo> getMemberInfoByPhone(String phone) {
                    log.error(HYSTRIX_PATTERN, "getMemberInfoByPhone",
                            phone, ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public MemberResult<String> checkPhysicalCardNum(String physicalCardNum) {
                    log.error(HYSTRIX_PATTERN, "checkPhysicalCardNum",
                            physicalCardNum, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberResult<Boolean> checkPhysicalCardPhoneNum(String physicalCardPhoneNum) {
                    log.error(HYSTRIX_PATTERN, "checkPhysicalCardPhoneNum",
                            physicalCardPhoneNum, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberResult<Boolean> checkPhysicalCardNumPairBindingNum(List<PhysicalCardCode> requestMemberInfoPhysicalCardDtoList) {
                    log.error(HYSTRIX_PATTERN, "checkPhysicalCardNumPairBindingNum",
                            requestMemberInfoPhysicalCardDtoList, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberResult<Boolean> physicalCardBindingMember(String memberInfoPhysicalCardGuid, String phone, String userName) {
                    log.error(HYSTRIX_PATTERN, "physicalCardBindingMember",
                            memberInfoPhysicalCardGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberResult<Boolean> saveOrUpdatePhysicalCardInfo(RequestSavePhysicalCardDto dto) {
                    log.error(HYSTRIX_PATTERN, "saveOrUpdatePhysicalCardInfo",
                            JacksonUtils.writeValueAsString(dto), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberResult<List<ResponseDepositStrategy>> listDepositStrategy(BasicSetupQuery query) {
                    log.error(HYSTRIX_PATTERN, "listDepositStrategy",
                            JacksonUtils.writeValueAsString(query), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberResult<ResponseCanteenCard> getCanteenCard(RequestCanteenCard requestCanteenCard) {
                    log.error(HYSTRIX_PATTERN, "getCanteenCard",
                            JacksonUtils.writeValueAsString(requestCanteenCard), ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public MemberResult<Boolean> updatePhysicalCardState(String memberInfoPhysicalCardGuid, Integer physicalCardState) {
                    log.error(HYSTRIX_PATTERN, "updatePhysicalCardState", memberInfoPhysicalCardGuid + "-" + physicalCardState
                            , ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public MemberResult<Boolean> activationPhysicalCard(List<String> memberInfoPhysicalCardGuid) {
                    log.error(HYSTRIX_PATTERN, "updatePhysicalCardState", memberInfoPhysicalCardGuid
                            , ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public MemberResult<String> saveOrUpdateBalanceUsageRule(RequestRechargeRule reqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveOrUpdateBalanceUsageRule", JacksonUtils.writeValueAsString(reqDTO)
                            , ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public MemberResult<ResponseMakePhysicalCardRecordDto> batchGenerationPhysicalCard(RequestMakePhysicalCardRecordDto request) {
                    log.error(HYSTRIX_PATTERN, "batchGenerationPhysicalCard", JacksonUtils.writeValueAsString(request)
                            , ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public MemberResult<Boolean> updateMakePhysicalCardType(RequestUpdateMakePhysicalCardType requestUpdateMakePhysicalCardType) {
                    log.error(HYSTRIX_PATTERN, "updateMakePhysicalCardType", JacksonUtils.writeValueAsString(requestUpdateMakePhysicalCardType)
                            , ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public MemberResult<String> saveOrUpdateBalanceRule(RequestBalanceRule reqDTO) {
                    log.error(HYSTRIX_PATTERN, "saveOrUpdateBalanceRule", JacksonUtils.writeValueAsString(reqDTO)
                            , ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public MemberResult<ResponseBalanceRule> getBalanceRule(BasicSetupQuery query) {
                    log.error(HYSTRIX_PATTERN, "getBalanceRule", JacksonUtils.writeValueAsString(query)
                            , ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public MemberResult<ResponseRechargeRule> getRechargeRule(BasicSetupQuery query) {
                    log.error(HYSTRIX_PATTERN, "getRechargeRule", JacksonUtils.writeValueAsString(query)
                            , ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public MemberResult<Integer> checkMemberInfo(String query) {
                    log.error(HYSTRIX_PATTERN, "checkMemberInfo", JacksonUtils.writeValueAsString(query)
                            , ThrowableUtils.asString(throwable));
                    return null;
                }

                @Override
                public MemberResult<PageDTO<ResponseMemberConsumption>> getMemberConsumptionPage(RequestQueryMemberConsumption request) {
                    log.error(HYSTRIX_PATTERN, "getMemberConsumptionPage", JacksonUtils.writeValueAsString(request)
                            , ThrowableUtils.asString(throwable));
                    return null;
                }

            };
        }
    }
}
