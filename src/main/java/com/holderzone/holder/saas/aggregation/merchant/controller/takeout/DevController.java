package com.holderzone.holder.saas.aggregation.merchant.controller.takeout;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutProducerService;
import com.holderzone.saas.store.dto.takeaway.EleCallbackResponse;
import com.holderzone.saas.store.dto.takeaway.MtCallbackResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/takeout/callback")
public class DevController {

    private final TakeoutProducerService takeoutProducerService;

    @Autowired
    public DevController(TakeoutProducerService takeoutProducerService) {
        this.takeoutProducerService = takeoutProducerService;
    }

    /**
     * 饿了么绑定回调是Get方法，所以不能再定义测试方法
     */
    public EleCallbackResponse testBindCallback() {
        return EleCallbackResponse.SUCCESS;
    }

    @GetMapping("/ele/order")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY)
    public EleCallbackResponse testOrderCallback() {
        return takeoutProducerService.testEleOrderCallback();
    }

    @GetMapping("/mt/order/{path}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY)
    public MtCallbackResponse orderCreatedCallback(@PathVariable(value = "path") String path) {
        return takeoutProducerService.testMtOrderCallback(path);
    }

    @GetMapping("/mt/privacy_degrade")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY)
    public MtCallbackResponse privacyDegradeCallback() {
        return takeoutProducerService.testMtPrivacyDegradeCallback();
    }

    @GetMapping("/mt/bind")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY)
    public MtCallbackResponse bindCallback() {
        return takeoutProducerService.testMtBindCallback();
    }

    @GetMapping("/mt/unbind")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY)
    public MtCallbackResponse unbindCallback() {
        return takeoutProducerService.testMtUnbindCallback();
    }

    @GetMapping("/mt/heartbeat")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY)
    public MtCallbackResponse heartbeatCallback() {
        return takeoutProducerService.testMtHeartbeatCallback();
    }
}
