package com.holderzone.holder.saas.aggregation.app.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RedisLock {

    public static final int LOCK_EXPIRE = 30;

    public static final String REDIS_LOCK = "redis_lock:";

    public static final String STORE_GUID = "store_guid:";

    @Autowired
    private RedisTemplate<String, String> redisTemplate;


    /**
     * 分布式锁
     *
     * @param key key值
     * @return 是否获取到
     */
    public boolean lock(String key) {
        String encrypt = AesEncryptUtils.aesEncrypt(key);
        String lockKey = REDIS_LOCK + encrypt;
        try {
            Boolean hasKey = redisTemplate.hasKey(lockKey);
            if (hasKey) {
                log.info("加锁失败 锁已存在");
                return false;
            }
            redisTemplate.opsForValue().set(lockKey, key, LOCK_EXPIRE, TimeUnit.SECONDS);
            log.info("加锁成功");
        } catch (Exception e) {
            log.error("加锁失败 e={}", e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 分布式锁
     *
     * @param key key值
     * @return 是否获取到
     */
    public boolean lock(String storeGuid, String key) {
        String encrypt = AesEncryptUtils.aesEncrypt(key);
        String lockKey = REDIS_LOCK + storeGuid + ":" + encrypt;
        try {
            Boolean hasKey = redisTemplate.hasKey(lockKey);
            if (hasKey) {
                log.info("加锁失败 锁已存在");
                return false;
            }
            redisTemplate.opsForValue().set(lockKey, key, LOCK_EXPIRE, TimeUnit.SECONDS);
            log.info("加锁成功");
        } catch (Exception e) {
            log.error("加锁失败 e={}", e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 删除锁
     *
     * @param key
     */
    public boolean delete(String key) {
        return redisTemplate.delete(key);
    }
}