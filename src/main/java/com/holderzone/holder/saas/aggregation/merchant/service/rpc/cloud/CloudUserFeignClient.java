package com.holderzone.holder.saas.aggregation.merchant.service.rpc.cloud;


import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.resource.common.dto.holder.HolderUserDTO;
import com.holderzone.resource.common.dto.user.HolderAdjustUserDTO;
import com.holderzone.resource.common.dto.user.UserDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @description 服务间调用-云端用户服务
 * @program holder-saas-cloud-user
 * @date 2021/3/11 11:20
 */
@Component
@FeignClient(value = "holder-saas-cloud-user", fallbackFactory = CloudUserFeignClient.ServiceFallBack.class)
public interface CloudUserFeignClient {

    /**
     * 修改密码（同步holder账号密码）
     */
    @PutMapping("/user/holder/pwd/sync")
    void syncHolderUserPwd(@RequestBody HolderUserDTO holderUserDTO);

    /**
     * 移交企业超级管理员
     */
    @PutMapping("/user/account/holder/admin/sync")
    void syncHolderAdminUser(@RequestBody HolderAdjustUserDTO userDTO);


    @GetMapping("/user/{userGuid}")
    @ApiOperation(value = "根据用户GUID查询用户信息")
    UserDTO findUserById(@PathVariable("userGuid") String userGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<CloudUserFeignClient> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}";

        @Override
        public CloudUserFeignClient create(Throwable throwable) {
            return new CloudUserFeignClient() {

                @Override
                public void syncHolderUserPwd(HolderUserDTO holderUserDTO) {
                    log.error(HYSTRIX_PATTERN, "syncHolderUserPwd", holderUserDTO, throwable);
                }

                @Override
                public void syncHolderAdminUser(HolderAdjustUserDTO userDTO) {
                    log.error(HYSTRIX_PATTERN, "syncHolderAdminUser", userDTO, throwable);
                }

                @Override
                public UserDTO findUserById(String userGuid) {
                    log.error(HYSTRIX_PATTERN, "findUserById", userGuid, throwable);
                    throw new BusinessException("查询用户失败");
                }
            };
        }
    }
}