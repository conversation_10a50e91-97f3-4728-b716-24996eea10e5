package com.holderzone.holder.saas.aggregation.app.service.feign.takeout;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsDTO;
import com.holderzone.saas.store.dto.business.manage.TakeoutStatsQueryDTO;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.ItemRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeoutDailyClientService
 * @date 2019/02/23 16:35
 * @description 外卖日报接口
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-takeaway-consumer", fallbackFactory = TakeoutDailyClientService.ServiceFallBack.class)
public interface TakeoutDailyClientService {
    /**
     * 查询营业概况
     * @param takeoutStatsQueryDTO
     * @return
     */
    @PostMapping(value = "/takeout/get_op_stats")
    TakeoutStatsDTO getOpStats(@RequestBody TakeoutStatsQueryDTO takeoutStatsQueryDTO);

    /**
     * 查询收款统计
     * @param takeoutStatsQueryDTO
     * @return
     */
    @PostMapping(value = "/takeout/get_receipt_stats")
    TakeoutStatsDTO getReceiptStats(@RequestBody TakeoutStatsQueryDTO takeoutStatsQueryDTO);

    /**
     * 查询用餐类型统计
     * @param takeoutStatsQueryDTO
     * @return
     */
    @PostMapping(value = "/takeout/get_trade_stats")
    TakeoutStatsDTO getTradeStats(@RequestBody TakeoutStatsQueryDTO takeoutStatsQueryDTO);

    @PostMapping(value = "/takeout/list_item_sale")
    List<ItemRespDTO> listItemSale(@RequestBody DailyReqDTO takeoutStatsQueryDTO);

    @PostMapping(value = "/takeout/list_goods")
    List<ItemRespDTO> goods(@RequestBody DailyReqDTO takeoutStatsQueryDTO);


    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<TakeoutDailyClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TakeoutDailyClientService create(Throwable cause) {
            return new TakeoutDailyClientService() {

                @Override
                public TakeoutStatsDTO getOpStats(TakeoutStatsQueryDTO takeoutStatsQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "getOpStats", JacksonUtils.writeValueAsString(takeoutStatsQueryDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutStatsDTO getReceiptStats(TakeoutStatsQueryDTO takeoutStatsQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "getReceiptStats", JacksonUtils.writeValueAsString(takeoutStatsQueryDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public TakeoutStatsDTO getTradeStats(TakeoutStatsQueryDTO takeoutStatsQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "getTradeStats", JacksonUtils.writeValueAsString(takeoutStatsQueryDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<ItemRespDTO> listItemSale(DailyReqDTO takeoutStatsQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "listItemSale", JacksonUtils.writeValueAsString(takeoutStatsQueryDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<ItemRespDTO> goods(DailyReqDTO takeoutStatsQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "goods", JacksonUtils.writeValueAsString(takeoutStatsQueryDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
