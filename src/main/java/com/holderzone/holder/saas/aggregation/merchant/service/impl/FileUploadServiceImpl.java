package com.holderzone.holder.saas.aggregation.merchant.service.impl;

import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.security.SecurityManager;
import com.holderzone.holder.saas.aggregation.merchant.entity.pojo.FileUploadDTO;
import com.holderzone.holder.saas.aggregation.merchant.exception.FileIllegalException;
import com.holderzone.holder.saas.aggregation.merchant.service.FileUploadService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.BaseService;
import com.holderzone.holder.saas.aggregation.merchant.util.UploadValidateUtil;
import com.holderzone.holder.saas.store.media.dto.entity.MediaDTO;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FileUploadServiceImpl
 * @date 2018/09/13 11:12
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Service
public class FileUploadServiceImpl implements FileUploadService {

    private static final Logger logger = LoggerFactory.getLogger(FileUploadServiceImpl.class);

    private final BaseService baseService;

    @Autowired
    public FileUploadServiceImpl(BaseService baseService) {
        this.baseService = baseService;
    }

    public static String getRandomFileName() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 5);
    }

    private static double getAccuracy(long size) {
        double accuracy;
        if (size < 900) {
            accuracy = 0.85;
        } else if (size < 2047) {
            accuracy = 0.6;
        } else if (size < 3275) {
            accuracy = 0.44;
        } else {
            accuracy = 0.4;
        }
        return accuracy;
    }

    @Override
    public String upload(FileUploadDTO fileUploadDTO, MultipartFile file) {
//        Integer height = null;
//        Integer weight = null;
//        Long size = null;
//        if (null != fileUploadDTO) {
//            height = fileUploadDTO.getHeight();
//            weight = fileUploadDTO.getWeight();
//            size = fileUploadDTO.getSize();
//        }
        String fileName = file.getOriginalFilename();
        String type = fileName != null && fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".") + 1,
                fileName.length()) : null;
        if (UploadValidateUtil.typeNotValidate(type)) {
            throw new FileIllegalException("文件格式必须为 jpg/jpeg/bmp/gif/png 格式！！！");
        }
        try {
//            byte[] bytes = checkFile(file, weight, height, size);
            FileDto fileDto = new FileDto();
            // 0.8-1.0版本过渡决定 不对图片进行压缩处理
            fileDto.setFileContent(SecurityManager.entryptBase64(file.getBytes()));
            fileDto.setFileName(getRandomFileName() + "." + type);
            String upload = baseService.upload(fileDto);
            logger.info("图片上传下载路径 upload={}", upload);
            return Optional.ofNullable(upload).orElse(null);
        } catch (IOException e) {
            logger.error("文件上传失败 file={}", file.getOriginalFilename());
            e.printStackTrace();
        }
        return null;
    }


    @Override
    public MediaDTO uploadForMedia(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        String type = fileName != null && fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".") + 1,
                fileName.length()) : null;
        if (UploadValidateUtil.typeNotValidate(type)) {
            throw new FileIllegalException("文件格式必须为 jpg/jpeg/bmp/gif/png 格式！！！");
        }
        try {
//            byte[] bytes = checkFile(file, weight, height, size);
            FileDto fileDto = new FileDto();
            // 0.8-1.0版本过渡决定 不对图片进行压缩处理
            fileDto.setFileContent(SecurityManager.entryptBase64(file.getBytes()));
            fileDto.setFileName(getRandomFileName() + "." + type);
            String upload = baseService.upload(fileDto);
            logger.info("图片上传下载路径 upload={}", upload);
            MediaDTO mediaDTO=new MediaDTO();
            mediaDTO.setName(StringUtils.remove(fileName, "."+type)).setFileUrl(upload).setIsFolder(false)
                    .setFileSize(file.getSize() / 1024).setFileType(1);
            return mediaDTO;
        } catch (IOException e) {
            logger.error("文件上传失败 file={}", file.getOriginalFilename());
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String uploadDishInputModel(File file) {
        try (FileInputStream in = new FileInputStream(file)) {
            byte[] bytes = new byte[in.available()];
            in.read(bytes);
            FileDto fileDto = new FileDto();
            fileDto.setFileContent(SecurityManager.entryptBase64(bytes));
            fileDto.setFileName("商品导入模板" + ".xls");
            String upload = baseService.upload(fileDto);
            logger.info("菜品导入模板上传下载路径 upload={}", upload);
            return Optional.ofNullable(upload).orElse(null);
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("文件输入流获取错误");
        }
        return null;
    }

    @Override
    public void deleteFileOnOssByUrl(String url) {
        baseService.delete(url);
    }

    /**
     * @param file
     * @return
     * @throws IOException
     */
    private byte[] checkFile(MultipartFile file, Integer weight, Integer height, Long size) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        if (null != size && file.getSize() > size) {
            byte[] bytes = compressPicForScale(file.getBytes(), size);
            if (null != weight && null != height) {
                Thumbnails.of(new ByteArrayInputStream(bytes))
                        .forceSize(weight, height)
                        .outputQuality(1)
                        .toOutputStream(byteArrayOutputStream);
                return byteArrayOutputStream.toByteArray();
            }
            return bytes;
        } else {
            if (null != weight && null != height) {
                Thumbnails.of(file.getInputStream())
                        .forceSize(weight, height)
                        .outputQuality(1)
                        .toOutputStream(byteArrayOutputStream);
                return byteArrayOutputStream.toByteArray();
            }
            return file.getBytes();
        }
    }

    public byte[] compressPicForScale(byte[] imageBytes, long desFileSize) {
        if (imageBytes == null || imageBytes.length <= 0 || imageBytes.length < desFileSize) {
            return imageBytes;
        }
        try {
            while (imageBytes.length > desFileSize) {
                double accuracy = getAccuracy(imageBytes.length);
                ByteArrayInputStream inputStream = new ByteArrayInputStream(imageBytes);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream(imageBytes.length);
                Thumbnails.of(inputStream)
                        .scale(accuracy)
                        .outputQuality(accuracy)
                        .toOutputStream(outputStream);
                imageBytes = outputStream.toByteArray();
            }
        } catch (Exception e) {
            logger.error("压缩图片异常！e={}", e.getMessage());
        }
        return imageBytes;
    }
}
