package com.holderzone.holder.saas.aggregation.merchant.controller.openapi;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.AreaService;
import com.holderzone.holder.saas.aggregation.merchant.service.TableService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.organization.OrganizationService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.trade.TradeClientService;
import com.holderzone.saas.store.dto.open.*;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.organization.StoreListReq;
import com.holderzone.saas.store.dto.table.AreaDTO;
import com.holderzone.saas.store.dto.table.TableBasicDTO;
import com.holderzone.saas.store.dto.table.TableBasicQueryDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsDetailRespDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsQueryDTO;
import com.holderzone.saas.store.dto.trade.BusinessOrderStatisticsRespDTO;
import com.holderzone.sdk.annotation.RateLimit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-07-04
 * @description 对接聚卡惠开放接口
 */
@Slf4j
@RestController
@RequestMapping("/openapi/kahui")
@RequiredArgsConstructor
public class KaHuiOpenController {

    private final OrganizationService organizationService;

    private final AreaService areaService;

    private final TableService tableService;

    private final TradeClientService tradeClientService;



    /**
     * 获取企业下的所有门店信息
     * @return 门店列表
     */
    @RateLimit(limitType = "kahuiStoreList", limitCount = 100)
    @PostMapping(value = "/storeList")
    public Result<List<StoreDTO>> storeList(@RequestBody BaseReq baseReq) {
        log.info("查询门店信息，{}", JacksonUtils.writeValueAsString(baseReq));
        return Result.buildSuccessResult(organizationService.list(new StoreListReq()));
    }

    /**
     * 获取企业下的所有区域信息
     * @return 区域列表
     */
    @RateLimit(limitType = "kahuiAreaList", limitCount = 100)
    @PostMapping("/areaList")
    public Result<List<AreaDTO>> areaList(@RequestBody AreaListReq areaListReq) {
        log.info("查询区域信息 storeDTO={}", JacksonUtils.writeValueAsString(areaListReq));
        return Result.buildSuccessResult(areaService.queryAll(areaListReq.getStoreGuid()));
    }

    /**
     * 获取门店下的桌台信息
     * @return 桌台列表
     */
    @RateLimit(limitType = "kahuiTableList", limitCount = 100)
    @PostMapping("/tableList")
    public Result<List<TableBasicDTO>> tableList(@RequestBody TableListReq tableListReq) {
        log.info("查询桌位信息 tableBasicQueryDTO={}", JacksonUtils.writeValueAsString(tableListReq));
        TableBasicQueryDTO tableBasicQuery = new TableBasicQueryDTO();
        tableBasicQuery.setAreaGuid(tableListReq.getAreaGuid());
        tableBasicQuery.setStoreGuid(tableListReq.getStoreGuid());
        return Result.buildSuccessResult(tableService.query(tableBasicQuery));
    }

    /**
     * 获取订单统计分页数据
     * @return 订单列表分页
     */
    @RateLimit(limitType = "kahuiOrderPage", limitCount = 100)
    @PostMapping("/orderPage")
    public Result<Page<BusinessOrderStatisticsRespDTO>> getOrderStatisticsPage(@RequestBody OrderPageReq orderPageReq) {
        BusinessOrderStatisticsQueryDTO businessOrderStatisticsQuery = new BusinessOrderStatisticsQueryDTO();
        businessOrderStatisticsQuery.setCurrentPage(orderPageReq.getCurrentPage());
        businessOrderStatisticsQuery.setPageSize(orderPageReq.getPageSize());
        businessOrderStatisticsQuery.setStartTime(orderPageReq.getStartTime());
        businessOrderStatisticsQuery.setEndTime(orderPageReq.getEndTime());
        businessOrderStatisticsQuery.setSearchKey(orderPageReq.getSearchKey());
        businessOrderStatisticsQuery.setOrderState(orderPageReq.getOrderState());
        businessOrderStatisticsQuery.setState(orderPageReq.getState());
        businessOrderStatisticsQuery.setStoreGuidList(orderPageReq.getStoreGuidList());
        businessOrderStatisticsQuery.setDiningTableGuids(orderPageReq.getDiningTableGuids());
        Page<BusinessOrderStatisticsRespDTO> orderStatisticsPage = tradeClientService.getOrderStatisticsPage(businessOrderStatisticsQuery);
        return Result.buildSuccessResult(orderStatisticsPage);
    }

    /**
     * 获取订单详情数据
     * @return 订单详情
     */
    @RateLimit(limitType = "kahuiOrderDetail", limitCount = 100)
    @PostMapping("/orderDetail")
    public Result<BusinessOrderStatisticsDetailRespDTO> getOrderStatisticsDetail(@RequestBody OrderDetailReq orderDetailReq) {
        BusinessOrderStatisticsDetailRespDTO orderStatisticsDetail = tradeClientService.getOrderStatisticsDetail(orderDetailReq.getOrderGuid());
        return Result.buildSuccessResult(orderStatisticsDetail);
    }
}
