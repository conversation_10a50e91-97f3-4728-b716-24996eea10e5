package com.holderzone.holder.saas.aggregation.app.service.feign.table;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.app.utils.ThrowableExtUtils;
import com.holderzone.saas.store.dto.boss.req.BossTableQueryDTO;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.request.OrderTableBillReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.store.table.StoreAndTableDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableServiceClient
 * @date 2019/01/07 11:24
 * @description
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(value = "holder-saas-store-table", fallbackFactory = TableClientService.TableServiceClientFallBack.class)
public interface TableClientService {

    /**
     * 手动结账关台
     */
    @ApiModelProperty("修改订单关联的桌台")
    @PostMapping("/table/deal_handle_close")
    boolean dealClose(@RequestBody OrderTableBillReqDTO orderTableBillReqDTO);

    /**
     * 根据订单号查询桌台并开台
     */
    @ApiModelProperty("根据订单号查询桌台并开台")
    @PostMapping("/table/open_table_by_order_guid")
    OpenTableOrderDTO openTableByOrderGuid(@RequestBody OpenTableByOrderDTO dto);

    @PostMapping("/table/getOrderGuid/{tableGuid}")
    String getOrderGuidByTableGuid(@PathVariable("tableGuid") String tableGuid);

    @PostMapping("/area/query/all/{storeGuid}")
    List<AreaDTO> queryArea(@PathVariable("storeGuid") String storeGuid);

    @PostMapping("/table/android/query")
    List<TableOrderDTO> queryTable(TableBasicQueryDTO tableBasicQueryDTO);

    @ApiOperation("查询桌台订单列表")
    @PostMapping("/table/boss/query")
    List<TableOrderDTO> queryTable(@RequestBody BossTableQueryDTO queryDTO);

    @PostMapping("/table/web/query")
    List<TableBasicDTO> listByWeb(@RequestBody TableBasicQueryDTO tableBasicQueryDTO);

    @PostMapping("/table/open")
    String openTable(OpenTableDTO openTableDTO);

    @PostMapping("/table/associated/open")
    String associatedOpenTable(@RequestBody OpenAssociatedTableDTO openAssociatedTableDTO);

    @PostMapping("/table/clean")
    String cleanTable(TableStatusChangeDTO tableStatusChangeDTO);

    @PostMapping("/table/turn")
    boolean turnTale(TurnTableDTO turnTableDTO);

    @PostMapping("/table/combine")
    List<String> combine(TableCombineDTO tableCombineDTO);

    @PostMapping("/table/combine_verify")
    TableCombineVerifyRespDTO verifyCombine(TableCombineDTO tableCombineDTO);

    @PostMapping("/table/combine_v2")
    TableCombineRespDTO combinev2(TableCombineDTO tableCombineDTO);

    @PostMapping("/table/separate")
    boolean separateTable(TableOrderCombineDTO tableOrderCombineDTO);

    @PostMapping("/table/close")
    boolean closeTable(CancelOrderReqDTO cancelOrderReqDTO);

    @PostMapping("/table/pay_close")
    boolean payClose(@RequestBody CancelOrderReqDTO cancelOrderReqDTO);

    @PostMapping("/table/lock")
    boolean tableLock(TableLockDTO tableLockDTO);

    @PostMapping("/table/release/lock")
    boolean releaseLock(TableLockDTO tableLockDTO);

    @PostMapping("/table/compensation")
    void compensationTableStatus(@RequestBody CompensationTableReqDTO reqDTO);

    @PostMapping("/table/whether/lock/{deviceId}/{tableGuid}")
    boolean checkTableIsLocked(@PathVariable("deviceId") String deviceId, @PathVariable("tableGuid") String tableGuid);

    @PostMapping("/localize/upload/tableOrder")
    boolean uploadTableOrderStatus(LocalizeTableOrderReqDTO localizeTableOrderReqDTO);

    @ApiModelProperty("根据桌台guid，查桌台详情")
    @PostMapping("/table/details/{tableGuid}")
    TableDTO getTableByGuid(@PathVariable("tableGuid") String tableGuid);

    /**
     * 根据门店列表查询桌台Guid列表
     *
     * @param singleDataDTO datas必传，门店guid
     * @return 门店和桌台的guid
     */
    @ApiOperation("根据门店列表查询桌台Guid列表")
    @PostMapping("/table/list_table_by_store_guid")
    List<StoreAndTableDTO> listTableByStoreGuid(@RequestBody SingleDataDTO singleDataDTO);

    @ApiModelProperty("根据主单guid查出所有并桌")
    @PostMapping("/table/query_combine_List_by_main_order")
    List<TableBasicDTO> queryCombineListByMainOrder(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping("/table/status/change_with_mq")
    boolean tableStatusChange(@RequestBody TableStatusChangeDTO tableStatusChangeDTO);

    @ApiModelProperty("根据订单guid查询桌台")
    @PostMapping("/table/query_table_by_order_guid")
    List<TableBasicDTO> queryTableByOrderGuid(@RequestBody SingleDataDTO singleDataDTO);

    @Slf4j
    @Component
    class TableServiceClientFallBack implements FallbackFactory<TableClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public TableClientService create(Throwable cause) {

            return new TableClientService() {
                @Override
                public List<TableBasicDTO> listByWeb(TableBasicQueryDTO tableBasicQueryDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "查询桌台列表",
                                JacksonUtils.writeValueAsString(tableBasicQueryDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new BusinessException("查询桌台列表异常");
                }

                @Override
                public boolean dealClose(OrderTableBillReqDTO orderTableBillReqDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "手动结账",
                                JacksonUtils.writeValueAsString(orderTableBillReqDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new BusinessException("手动结账异常");
                }

                @Override
                public OpenTableOrderDTO openTableByOrderGuid(OpenTableByOrderDTO dto) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "根据订单号查询桌台并开台",
                                JacksonUtils.writeValueAsString(dto), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new BusinessException("根据订单号查询桌台并开台");
                }

                @Override
                public String getOrderGuidByTableGuid(String tableGuid) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "根据桌台guid，查桌台关联订单guid",
                                tableGuid, ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new BusinessException("查询订单失败");
                }

                @Override
                public List<AreaDTO> queryArea(String storeGuid) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "查询桌台区域列表",
                                JacksonUtils.writeValueAsString(storeGuid), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new BusinessException("查询桌台区域列表异常");
                }

                @Override
                public List<TableOrderDTO> queryTable(TableBasicQueryDTO tableBasicQueryDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "查询桌台列表",
                                JacksonUtils.writeValueAsString(tableBasicQueryDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new BusinessException("查询桌台基础数据异常");
                }

                @Override
                public List<TableOrderDTO> queryTable(BossTableQueryDTO queryDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "queryTable",
                                JacksonUtils.writeValueAsString(queryDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new BusinessException("查询桌台基础数据异常");
                }

                @Override
                public String openTable(OpenTableDTO openTableDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "开台接口",
                                JacksonUtils.writeValueAsString(openTableDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    return null;
                }

                @Override
                public String associatedOpenTable(OpenAssociatedTableDTO openAssociatedTableDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "联台接口",
                                JacksonUtils.writeValueAsString(openAssociatedTableDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    return null;
                }

                @Override
                public String cleanTable(TableStatusChangeDTO tableStatusChangeDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "清台接口",
                                JacksonUtils.writeValueAsString(tableStatusChangeDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    return null;
                }

                @Override
                public boolean turnTale(TurnTableDTO turnTableDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "转台接口",
                                JacksonUtils.writeValueAsString(turnTableDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    return false;
                }

                @Override
                public List<String> combine(TableCombineDTO tableCombineDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "并桌异常",
                                JacksonUtils.writeValueAsString(tableCombineDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    return null;
                }

                @Override
                public TableCombineVerifyRespDTO verifyCombine(TableCombineDTO tableCombineDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "verifyCombine",
                                JacksonUtils.writeValueAsString(tableCombineDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    throw new BusinessException("并台校验提示异常");
                }

                @Override
                public TableCombineRespDTO combinev2(TableCombineDTO tableCombineDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "并桌异常v2",
                                JacksonUtils.writeValueAsString(tableCombineDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    return null;
                }

                @Override
                public boolean separateTable(TableOrderCombineDTO tableOrderCombineDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "拆台接口异常",
                                JacksonUtils.writeValueAsString(tableOrderCombineDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    return false;
                }

                @Override
                public boolean closeTable(CancelOrderReqDTO cancelOrderReqDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "关台接口",
                                JacksonUtils.writeValueAsString(cancelOrderReqDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    return false;
                }

                @Override
                public boolean payClose(CancelOrderReqDTO cancelOrderReqDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "聚合支付关台接口",
                                JacksonUtils.writeValueAsString(cancelOrderReqDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    return false;
                }

                @Override
                public boolean tableLock(TableLockDTO tableLockDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "桌台加锁",
                                JacksonUtils.writeValueAsString(tableLockDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    return false;
                }

                @Override
                public boolean releaseLock(TableLockDTO tableLockDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "释放桌台锁",
                                JacksonUtils.writeValueAsString(tableLockDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    return false;
                }

                @Override
                public void compensationTableStatus(CompensationTableReqDTO reqDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "补偿桌台状态",
                                JacksonUtils.writeValueAsString(reqDTO), ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                }

                @Override
                public boolean checkTableIsLocked(String guid, String tableGuid) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "校验桌台是否被锁",
                                JacksonUtils.writeValueAsString("guid=" + guid + ", tableGuid=" + tableGuid),
                                ThrowableExtUtils.asStringIfAbsent(cause));
                    }
                    return false;
                }

                @Override
                public boolean uploadTableOrderStatus(LocalizeTableOrderReqDTO localizeTableOrderReqDTO) {
                    if (log.isErrorEnabled()) {
                        log.error(HYSTRIX_PATTERN, "桌台本地化数据上传");
                    }
                    return false;
                }

                @Override
                public TableDTO getTableByGuid(String tableGuid) {
                    log.error(HYSTRIX_PATTERN, "getTableByGuid", tableGuid,
                            ThrowableUtils.asString(cause));
                    throw new BusinessException("查桌台详情接口熔断");
                }

                @Override
                public List<StoreAndTableDTO> listTableByStoreGuid(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "listTableByStoreGuid", JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(cause));
                    throw new BusinessException("根据门店列表查询桌台列表接口熔断");
                }

                @Override
                public List<TableBasicDTO> queryCombineListByMainOrder(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "queryCombineListByMainOrder", JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public boolean tableStatusChange(TableStatusChangeDTO tableStatusChangeDTO) {
                    log.error(HYSTRIX_PATTERN, "tableStatusChange", JacksonUtils.writeValueAsString(tableStatusChangeDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<TableBasicDTO> queryTableByOrderGuid(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "queryTableByOrderGuid", JacksonUtils.writeValueAsString(singleDataDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
