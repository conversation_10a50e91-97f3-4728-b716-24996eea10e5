package com.holderzone.holder.saas.aggregation.weixin.interceptor;

import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.TraceContextUtils;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxUserRecordClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.WxHsmMemberBasicBufferService;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseAccountStatus;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.holder.saas.weixin.utils.WxMemberSessionUtil;
import com.holderzone.resource.common.util.LoginSource;
import com.holderzone.saas.store.dto.common.CommonConstant;
import com.holderzone.saas.store.dto.weixin.WxUserRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WebInterceptor
 * @date 2018/10/29 16:26
 * @description
 * @program holder-saas-aggregation-weixin
 */
@Configuration
@Slf4j
public class WeixinSessionInterceptor implements HandlerInterceptor {
    @Resource
    RedisUtils redisUtils;

    @Resource
    @Lazy
    WxUserRecordClientService wxUserRecordClientService;

    @Resource
    @Lazy
    WxHsmMemberBasicBufferService wxHsmMemberBasicBufferService;

    private static final String NULL_STR = "null";


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        String enterpriseGuid = request.getHeader(CommonConstant.ENTERPRISEGUID_KEY);
        log.info("当前enterpriseGuid:{}", enterpriseGuid);
        if (StringUtils.hasText(enterpriseGuid)) {
            EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        } else {
            log.warn("当前enterpriseGuid为空");
            return true;
        }
        String openId = request.getHeader(CommonConstant.WX_OPENID);
        String wxtoken = request.getHeader(CommonConstant.WX_SESSION_TOKEN);
        String operSubjectGuid = request.getHeader(CommonConstant.OPERSUBJECTGUID_KEY);
        String phone = request.getHeader(CommonConstant.PHONE);
        log.info("header头中的openId:{}", openId);
        log.info("header头中的wxtoken:{}", wxtoken);
        log.info("header头中的运营主体{}", operSubjectGuid);
        log.info("header头中的手机号:{}", phone);
        WxMemberSessionDTO memberSession = null;
        if (StringUtils.hasText(wxtoken) && !NULL_STR.equals(wxtoken)) {
            log.info("根据wxToken从缓存中获取memberSession, wxtoken:{}", wxtoken);
            memberSession = WxMemberSessionUtil.getMember(redisUtils, wxtoken);
        } else if (StringUtils.hasText(openId) && !NULL_STR.equals(openId)) {
            // openid
            log.info("根据openId从缓存中获取memberSession, openId:{}", openId);
            memberSession = WxMemberSessionUtil.getMemberByOpenId(redisUtils, openId);
            if (!checkMemberSessionIsNull(memberSession, openId, phone, enterpriseGuid)) {
                return false;
            }
        }
        if (memberSession != null && !StringUtils.isEmpty(phone) && !NULL_STR.equals(phone)) {
            memberSession.setPhoneNum(phone);
        }
        UserContext userContext = buildUserContext(enterpriseGuid, operSubjectGuid, memberSession);
        log.info("userContext拦截器设置后:{}", JacksonUtils.writeValueAsString(userContext));
        UserContextUtils.put(userContext);
        afterSetWxUserThreadLocal(memberSession);
        log.info("WeixinUserThreadLocal拦截器设置后:{}", memberSession);
        WeixinUserThreadLocal.set(memberSession);
        return true;
    }


    private boolean checkMemberSessionIsNull(WxMemberSessionDTO memberSession, String openId, String phone, String enterpriseGuid) {
        if (memberSession != null) {
            log.info("WeixinUserThreadLocal设置，获取redis:{}", JacksonUtils.writeValueAsString(memberSession));
            return true;
        }
        WxUserRecordDTO wxUserRecordDTO = wxUserRecordClientService.getUserByOpenId(openId);
        if (wxUserRecordDTO == null) {
            log.error("openId:{}对应的用户不存在", openId);
            return false;
        }
        WxUserInfoDTO wxUserInfoDTO = new WxUserInfoDTO();
        memberSession = new WxMemberSessionDTO();
        memberSession.setEnterpriseGuid(enterpriseGuid);
        wxUserInfoDTO.setOpenId(openId);
        wxUserInfoDTO.setNickname(wxUserRecordDTO.getNickName());
        wxUserInfoDTO.setHeadImgUrl(wxUserRecordDTO.getHeadImgUrl());
        wxUserInfoDTO.setIsLogin(wxUserRecordDTO.getIsLogin());
        wxUserInfoDTO.setSex(wxUserRecordDTO.getSex());
        memberSession.setWxUserInfoDTO(wxUserInfoDTO);
        //赚餐通过header头将手机号传过来
        if (!StringUtils.isEmpty(phone) && !NULL_STR.equals(phone)) {
            memberSession.setPhoneNum(phone);
        }
        log.info("memberSession初始化设置redis:{}", JacksonUtils.writeValueAsString(memberSession));
        WxMemberSessionUtil.initMemberSessionWithOpenId(redisUtils, memberSession);
        return true;
    }

    private void afterSetWxUserThreadLocal(WxMemberSessionDTO memberSession) {
        if (Objects.isNull(memberSession)) {
            return;
        }
        WxUserInfoDTO wxUserInfoDTO = memberSession.getWxUserInfoDTO();
        if (Objects.isNull(wxUserInfoDTO)) {
            return;
        }
        if (Objects.isNull(wxUserInfoDTO.getOpenId()) || !Boolean.TRUE.equals(wxUserInfoDTO.getIsLogin())) {
            return;
        }
        String phoneNumOrOpenid = wxUserInfoDTO.getOpenId();
        UserContext userContext = UserContextUtils.get();
        ResponseAccountStatus memberAccount = wxHsmMemberBasicBufferService.getMemberAccount(phoneNumOrOpenid,
                userContext.getEnterpriseGuid(), userContext.getOperSubjectGuid());
        log.info("preHandle查询出的会员状态:{}", memberAccount);
        memberSession.setForbidden(false);
        wxUserInfoDTO.setIsLogin(false);
        if (Objects.nonNull(memberAccount)) {
            // 0正常,1冻结,2不存在
            memberSession.setForbidden(memberAccount.getAccountState() != 0);
            if (!memberSession.isForbidden()) {
                wxUserInfoDTO.setIsLogin(true);
            }
            // 会员guid
            memberSession.setMemberInfoGuid(memberAccount.getMemberInfoGuid());
            wxUserInfoDTO.setWorkName(memberAccount.getWorkName());
            wxUserInfoDTO.setContactAddress(memberAccount.getContactAddress());
        }
    }

    private UserContext buildUserContext(String enterpriseGuid, String operSubjectGuid, WxMemberSessionDTO memberSession) {
        UserContext context = UserContextUtils.get();
        context.setAllianceId("1fb529b8da78459ca64187f94dc3ae3e");
        if (Objects.isNull(memberSession)) {
            memberSession = new WxMemberSessionDTO();
        }
        if (memberSession.getEnterpriseGuid() != null) {
            context.setEnterpriseGuid(memberSession.getEnterpriseGuid());
        }
        WxUserInfoDTO wxUserInfoDTO = memberSession.getWxUserInfoDTO();
        if (Objects.nonNull(wxUserInfoDTO)) {
            context.setUserGuid(StringUtils.isEmpty(context.getUserGuid()) ? wxUserInfoDTO.getOpenId() : context.getUserGuid());
            context.setUserName(StringUtils.isEmpty(context.getUserName()) ? wxUserInfoDTO.getNickname() : context.getUserName());
        }
        context.setStoreName(StringUtils.isEmpty(context.getStoreName()) ? memberSession.getStoreName() : context.getStoreName());
        context.setStoreGuid(StringUtils.isEmpty(context.getStoreGuid()) ? memberSession.getStoreGuid() : context.getStoreGuid());
        context.setSource(StringUtils.isEmpty(context.getSource()) ? LoginSource.WECHAT.code() : context.getSource());
        if (context.getEnterpriseGuid() == null && enterpriseGuid != null) {
            context.setEnterpriseGuid(enterpriseGuid);
        }
        if (context.getEnterpriseGuid() == null) {
            context.setEnterpriseGuid(EnterpriseIdentifier.getEnterpriseGuid());
        }
        context.setOperSubjectGuid(operSubjectGuid);
        if (StringUtils.isEmpty(operSubjectGuid)) {
            context.setOperSubjectGuid(memberSession.getOperSubjectGuid());
        }
        // context.set
        return context;
    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        WeixinUserThreadLocal.remove();
        UserContextUtils.remove();
        EnterpriseIdentifier.remove();
        TraceContextUtils.remove();
        MDC.clear();
    }
}
