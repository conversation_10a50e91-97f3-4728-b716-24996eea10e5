package com.holderzone.holder.saas.aggregation.weixin.controller.memberCenter;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.WxOrderRecordClientService;
import com.holderzone.saas.store.dto.weixin.req.WxBrandUserOrderReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxBrandUserOrderDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(description = "会员中心：订单")
@RestController
@RequestMapping("/wx_member_center_dine")
@Slf4j
public class WxMemberCenterDineController {

	private final WxOrderRecordClientService wxOrderRecordClientService;

	@Autowired
	public WxMemberCenterDineController(WxOrderRecordClientService wxOrderRecordClientService) {
		this.wxOrderRecordClientService = wxOrderRecordClientService;
	}

	@ApiOperation("查询并封装我的订单记录")
	@PostMapping(value = "/brand_user_order")
	public Result<WxBrandUserOrderDTO> getWxBrandUserOrder(@RequestBody WxBrandUserOrderReqDTO wxBrandUserOrderReqDTO) {
		log.info("查询用户品牌订单记录入参:{}", JacksonUtils.writeValueAsString(wxBrandUserOrderReqDTO));
		return Result.buildSuccessResult(wxOrderRecordClientService.getWxBrandUserOrder(wxBrandUserOrderReqDTO));
	}
}
