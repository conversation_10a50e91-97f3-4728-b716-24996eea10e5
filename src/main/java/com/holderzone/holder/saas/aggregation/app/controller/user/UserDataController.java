package com.holderzone.holder.saas.aggregation.app.controller.user;

import com.holderzone.framework.base.dto.file.FileDto;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.service.feign.BaseFeignService;
import com.holderzone.holder.saas.aggregation.app.service.feign.staff.UserClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.OrderWaiterClientService;
import com.holderzone.saas.store.dto.order.request.waiter.OrderWaiterInfoDTO;
import com.holderzone.saas.store.dto.order.request.waiter.OrderWaiterReqDTO;
import com.holderzone.saas.store.dto.user.UserDTO;
import com.holderzone.saas.store.dto.user.UserQueryDTO;
import com.holderzone.saas.store.dto.user.UserSpinnerDTO;
import com.holderzone.saas.store.dto.user.req.AuthorizationReqDTO;
import com.holderzone.saas.store.dto.user.req.UserFaceInputReqDTO;
import com.holderzone.saas.store.dto.user.resp.UserAuthorityBriefDTO;
import com.holderzone.saas.store.dto.user.resp.UserFaceDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> R
 * @date 2020/11/18 9:52
 * @description 员工操作管理
 */
@Slf4j
@RestController
@RequestMapping("/user")
@Api(value = "员工管理接口")
public class UserDataController {

    @Autowired
    private UserClientService userClientService;



    @Autowired
    private OrderWaiterClientService orderWaiterClientService;

    @ApiOperation(value = "门店查询员工信息")
    @PostMapping(value = "/page_query")
    public Result<List<UserDTO>> pageQuery(@Validated @RequestBody UserQueryDTO userQueryDTO) {
        if (log.isInfoEnabled()) {
            log.info("门店查询员工信息入参：{}", JacksonUtils.writeValueAsString(userQueryDTO));
        }
        return Result.buildSuccessResult(userClientService.findByStoreGuid(userQueryDTO));
    }

    @ApiOperation(value = "录入服务员接口", notes = "录入服务员接口")
    @PostMapping("/add_order_waiter")
    public Result<Boolean> addOrderWaiter(@RequestBody OrderWaiterReqDTO orderWaiterReqDTO) {
        log.info("录入服务员入参：{}", JacksonUtils.writeValueAsString(orderWaiterReqDTO));
        return Result.buildSuccessResult(orderWaiterClientService.addOrderWaiter(orderWaiterReqDTO));
    }

    @ApiOperation(value = "订单查询服务员接口", notes = "订单查询服务员接口")
    @GetMapping("/get_order_waiter/{orderGuid}")
    public Result<List<OrderWaiterInfoDTO>> getOrderWaiter(@PathVariable("orderGuid") String orderGuid) {
        log.info("查询服务员入参：{}", orderGuid);
        return Result.buildSuccessResult(orderWaiterClientService.getOrderWaiter(orderGuid));
    }

    @ApiOperation(value = "aio授权")
    @PostMapping(value = "/authorize")
    public Result<Boolean> authorize(@RequestBody AuthorizationReqDTO reqDTO) {
        log.info("aio授权接口入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        boolean authorize = userClientService.authorize(reqDTO);
        if (authorize) {
            return Result.buildSuccessResult("授权成功", Boolean.TRUE);
        }
        return Result.buildOpFailedResult("授权失败", Boolean.FALSE);
    }

    @ApiOperation(value = "aio授权并返回授权人信息")
    @PostMapping(value = "/authorize/user")
    public Result<UserAuthorityBriefDTO> authorizeUser(@RequestBody AuthorizationReqDTO reqDTO) {
        log.info("aio授权并返回授权人信息接口入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(userClientService.authorizeUser(reqDTO));
    }

    @ApiOperation(value = "查询门店Spinner")
    @PostMapping(value = "/query_store_spinner")
    public Result<UserSpinnerDTO> queryStoreSpinner() {
        return Result.buildSuccessResult(userClientService.queryStoreSpinner());
    }

    @ApiOperation(value = "录入人脸")
    @PostMapping(value = "/inputFace")
    public Result<Void> inputFace(@RequestBody UserFaceInputReqDTO reqDTO) {
        log.info("[录入人脸]入参={}", JacksonUtils.writeValueAsString(reqDTO));
        userClientService.inputFace(reqDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "人脸授权")
    @PostMapping(value = "/authorize_face")
    public Result<UserFaceDTO> authorizeFace(@RequestBody @Validated AuthorizationReqDTO reqDTO) {
        log.info("[人脸授权]接口入参：{}", JacksonUtils.writeValueAsString(reqDTO));
        return Result.buildSuccessResult(userClientService.authorizeFace(reqDTO));
    }
}
