package com.holderzone.holder.saas.aggregation.app.service;

import com.holderzone.saas.store.dto.boss.req.BossOrderItemQueryDTO;
import com.holderzone.saas.store.dto.boss.resp.BossOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.trade.OrderInfoRespDTO;

public interface OrderDetailsService {

    /**
     * 订单信息脱敏
     */
    OrderInfoRespDTO sensitization(OrderInfoRespDTO orderInfoRespDTO);

    /**
     * 老板助手
     * 查询订单商品详情
     */
    BossOrderDetailRespDTO queryOrderItemInfo(BossOrderItemQueryDTO queryDTO);

}
