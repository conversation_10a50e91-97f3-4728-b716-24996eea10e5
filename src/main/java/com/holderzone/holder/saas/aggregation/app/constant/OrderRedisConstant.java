package com.holderzone.holder.saas.aggregation.app.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @className OrderRedisConstant
 * @date 2018-07-25 18:25:11
 * @description
 * @program holder-saas-store-trade
 */
public class OrderRedisConstant {

    public static final String ORDER_GROUP = "order";

    public static final String HANG_GROUP = "hang";

    public static final String ORDER_NO_GROUP = "orderNo";

    public static final String HST_ORDER_GROUP = "hstOrder";

    public static final String HST_ORDER_ITEM_GROUP = "hstOrderItem";

    public static final String HST_ORDER_TRADE_GROUP = "hstOrderTrade";

    public static final String HST_ORDER_VERSION_GROUP = "hstOrderVersion";

    public static final String WE_CHAT_USER_INFO = "weChatUserInfo:";

    public static final String INCR_GROUP = "incr";

    public static final int ORDER_MINUTES_TIMEOUT = 60;

}
