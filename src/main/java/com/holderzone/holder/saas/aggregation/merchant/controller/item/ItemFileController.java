package com.holderzone.holder.saas.aggregation.merchant.controller.item;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ParameterException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.ExportLocaleEnum;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.item.ItemClientService;
import com.holderzone.holder.saas.aggregation.merchant.util.ExcelUtil;
import com.holderzone.saas.store.dto.item.common.ItemDoubleParamDTO;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.ItemExcelTemplateReqDTO;
import com.holderzone.saas.store.dto.item.resp.ItemBatchImportTempRespDTO;
import com.holderzone.saas.store.dto.item.resp.ItemExcelTemplateRespDTO;
import com.holderzone.saas.store.enums.item.ModuleEntranceEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.UnsupportedFileFormatException;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemExportController
 * @date 2019/1/28 14:45
 * @description 菜品文件接口
 * @program holder-saas-store-business
 */
@Api(tags = "商品文件接口")
@Controller
@RequestMapping("item_file")
@Slf4j
public class ItemFileController {

    @Autowired
    private ItemClientService itemClientService;

    @Autowired
    private ItemHelper itemHelper;
    
    private final static String NOT_EDIT="M_LOCALE_307";


    @ApiOperation("门店商品导出到excel")
    @PostMapping("/store/download_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "门店商品导出到excel", action = OperatorType.SELECT)
    public void itemExport(@RequestBody ItemSingleDTO itemSingleDTO, HttpServletResponse response) {
        log.info("下载菜品excel文件入参:{}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(ModuleEntranceEnum.STORE.code());
        response.setContentType("application/msexcel;charset=UTF-8");
        String substring = "item_" + System.currentTimeMillis() + ".xls";
        response.setHeader("Content-Disposition", "attachment;fileName=" + substring);
        List<ItemExcelTemplateRespDTO> collData = itemClientService.getDownloadItemData(itemSingleDTO);
        if(CollectionUtil.isNotEmpty(collData)){
            collData.forEach(c ->{
                c.setIsWholeDiscount(ExportLocaleEnum.getLocale(c.getIsWholeDiscount()));
                c.setItemType(ExportLocaleEnum.getLocale(c.getItemType()));
                if(Constants.DEFAULT_CATEGORY.equals(c.getTypeName())){
                    c.setTypeName(LocaleUtil.getMessage("DEFAULT_CATEGORY"));
                }
            });
        }

        ExcelUtil<ItemExcelTemplateRespDTO> excelUtil = new ExcelUtil<>();
        try {
            ServletOutputStream outputStream = response.getOutputStream();
            excelUtil.exportExcel(ExportLocaleEnum.listItemHeader(), collData, outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "Q3阶段：流程优化—>批量导入门店商品 p1:文件上传解析验证")
    @PostMapping("/store/batch_import_item")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "Q3阶段：流程优化—>批量导入门店商品 p1:文件上传解析验证", action = OperatorType.SELECT)
    public Result<List<ItemBatchImportTempRespDTO>> storeItemBatchImport(@RequestParam("file") MultipartFile file, @RequestParam("data") String storeGuid) {
        log.info("Q3阶段 ： 批量导入门店商品入参：storeGuid={}", storeGuid);
        //解析excel为list商品
        List<ItemExcelTemplateReqDTO> itemExcelTemplateReqDTOList = convertFile2Data(file, 0);
        //批量验证解析出的商品
        List<ItemBatchImportTempRespDTO> itemBatchImportTempRespDTOS = itemHelper.itemBatchVerify(itemExcelTemplateReqDTOList, storeGuid, 0, 0);
        return Result.buildSuccessResult(itemBatchImportTempRespDTOS);
    }


    @ApiOperation(value = "Q3阶段：流程优化—>批量导入品牌商品 p1:文件上传解析验证")
    @PostMapping("/brand/batch_import_item")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "Q3阶段：流程优化—>批量导入品牌商品 p1:文件上传解析验证", action = OperatorType.SELECT)
    public Result<List<ItemBatchImportTempRespDTO>> brandItemBatchImport(@RequestParam("file") MultipartFile file, @RequestParam("data") String brandGuid) {
        log.info("Q3阶段 ： 批量导入品牌商品入参：brandGuid={}", brandGuid);
        //解析excel为list商品
        List<ItemExcelTemplateReqDTO> itemExcelTemplateReqDTOList = convertFile2Data(file, 0);
        //批量验证解析出的商品
        List<ItemBatchImportTempRespDTO> itemBatchImportTempRespDTOS = itemHelper.itemBatchVerify(itemExcelTemplateReqDTOList, brandGuid, 1, 0);
        return Result.buildSuccessResult(itemBatchImportTempRespDTOS);
    }


    //新版本批量导入商品  file  convert to datas

    /**
     * @param file
     * @param type 0:餐饮版  1：零售版
     * @return
     */
    private List<ItemExcelTemplateReqDTO> convertFile2Data(MultipartFile file, Integer type) {
        List<ItemExcelTemplateReqDTO> itemExcelTemplateReqDTOList;
        try {
            InputStream inputStream = file.getInputStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            // 只要第一个sheet
            XSSFSheet sheet = workbook.getSheetAt(0);
            Integer firstDataRowIndex = Objects.equals(type, 0) ? 3 : 2;
            List<List<String>> excelData = ExcelUtil.getSheetData(sheet, firstDataRowIndex, 0);
            List<List<String>> data = new ArrayList<>();
            excelData.forEach(s -> {
                if (s.stream().allMatch(x -> x == "" || x == null)) {
                } else {
                    data.add(s);
                }
            });
            if (CollectionUtils.isEmpty(data)) {
                throw new ParameterException(LocaleUtil.getMessage("M_LOCALE_300"));
            }
            itemExcelTemplateReqDTOList = new ArrayList<>(data.size());
            try {
                data.forEach(o -> {
                    List<String> o1 = new ArrayList<>(6);
                    CollectionUtils.addAll(o1, new Object[o.size() + 3]);
                    Collections.copy(o1, o);
                    ItemExcelTemplateReqDTO itemExcelTemplateReqDTO = new ItemExcelTemplateReqDTO();
                    itemExcelTemplateReqDTO.setTypeName(ObjectUtils.isEmpty(o1.get(0)) ? null : ExportLocaleEnum.transfer2Cn(o1.get(0)));
                    itemExcelTemplateReqDTO.setItemName(ObjectUtils.isEmpty(o1.get(1)) ? null : o1.get(1));
                    itemExcelTemplateReqDTO.setUnit(ObjectUtils.isEmpty(o1.get(3)) ? null : o1.get(3));
                    if (ObjectUtils.nullSafeEquals(type, 1)) { //新零售版本解析
                        itemExcelTemplateReqDTO.setSalePrice(ObjectUtils.isEmpty(o1.get(4)) ? null : new BigDecimal(o1.get(4)));
                        itemExcelTemplateReqDTO.setCostPrice(ObjectUtils.isEmpty(o1.get(5)) ? null : new BigDecimal(o1.get(5))); //成本价
                        int isWholeDiscount = Objects.equals(ExportLocaleEnum.transfer2Cn(o1.get(6)), "是") ? 1 : 0;
                        itemExcelTemplateReqDTO.setIsWholeDiscount(ObjectUtils.isEmpty(o1.get(6)) ? null : isWholeDiscount); //是否参与整单折扣 0：否 1：是
                        itemExcelTemplateReqDTO.setItemType(ObjectUtils.isEmpty(o1.get(2)) ? null : "计重".equals(o1.get(2).trim()) ? "3" : "计数".equals(o1.get(2).trim()) ? "4" : "2");
                        itemExcelTemplateReqDTO.setCode(ObjectUtils.isEmpty(o1.get(7)) ? null : o1.get(7));  //货号
                        itemExcelTemplateReqDTO.setUpc(ObjectUtils.isEmpty(o1.get(8)) ? null : o1.get(8));  //商品条码
                        itemExcelTemplateReqDTO.setIsOpenStock(ObjectUtils.isEmpty(o1.get(9)) ? null : Objects.equals(o1.get(9), "开启") ? 1 : 0);  //是否开启库存 0：否 1：是
                        itemExcelTemplateReqDTO.setSafeStock(!ObjectUtils.isEmpty(o1.get(10)) ? new BigDecimal(o1.get(10)) : itemExcelTemplateReqDTO.getIsOpenStock() == 1 ? BigDecimal.ZERO : null);  //安全库存
                        itemExcelTemplateReqDTO.setTotalStock(!ObjectUtils.isEmpty(o1.get(11)) ? new BigDecimal(o1.get(11)) : itemExcelTemplateReqDTO.getIsOpenStock() == 1 ? BigDecimal.ZERO : null);  //库存数量
                    } else {  //餐饮版解析
                        itemExcelTemplateReqDTO.setItemType(transferItemType(o1.get(2)));
                        // sku简码
                        itemExcelTemplateReqDTO.setCode(ObjectUtils.isEmpty(o1.get(4)) ? null : o1.get(4));
                        itemExcelTemplateReqDTO.setSalePrice(ObjectUtils.isEmpty(o1.get(5)) ? null : new BigDecimal(o1.get(5)));
                        itemExcelTemplateReqDTO.setCostPrice(ObjectUtils.isEmpty(o1.get(6)) ? null : new BigDecimal(o1.get(6))); //成本价
                        int reqWholeDiscount = Objects.equals(ExportLocaleEnum.transfer2Cn(o1.get(7)), "是") ? 1 : 0;
                        Integer isWholeDiscount = ObjectUtils.isEmpty(o1.get(7)) ? null : reqWholeDiscount;
                        itemExcelTemplateReqDTO.setIsWholeDiscount(isWholeDiscount); //是否参与整单折扣 0：否 1：是
                        itemExcelTemplateReqDTO.setMemberPrice(ObjectUtils.isEmpty(o1.get(8)) ? null : new BigDecimal(o1.get(8))); //会员价
                        int reqRack = Objects.equals(ExportLocaleEnum.transfer2Cn(o1.get(9)), "是") ? 1 : 0;
                        itemExcelTemplateReqDTO.setIsRack(ObjectUtils.isEmpty(o1.get(9)) ? 1 : reqRack); //是否上架掌控者设备 0：否 1：是
                        int reqJoinWechat = Objects.equals(ExportLocaleEnum.transfer2Cn(o1.get(10)), "是") ? 1 : 0;
                        itemExcelTemplateReqDTO.setIsJoinWechat(ObjectUtils.isEmpty(o1.get(10)) ? null : reqJoinWechat); //是否上架微信端 0：否 1：是
                        int reqJoinBuffet = Objects.equals(ExportLocaleEnum.transfer2Cn(o1.get(11)), "是") ? 1 : 0;
                        itemExcelTemplateReqDTO.setIsJoinBuffet(ObjectUtils.isEmpty(o1.get(11)) ? null : reqJoinBuffet); //是否上架自助点餐机 0：否 1：是
                        itemExcelTemplateReqDTO.setPictureUrl(ObjectUtils.isEmpty(o1.get(12)) ? null : o1.get(12)); // 图片地址
                    }
                    itemExcelTemplateReqDTOList.add(itemExcelTemplateReqDTO);
                });
            } catch (RuntimeException e) {
                log.error("文件或数据格式错误");
                throw new BusinessException(LocaleUtil.getMessage(NOT_EDIT));
            }
        } catch (IOException e) {
            log.error("获取文件流错误", e);
            throw new BusinessException(LocaleUtil.getMessage(NOT_EDIT));
        } catch (UnsupportedFileFormatException e) {
            log.error("文件格式错误", e);
            throw new BusinessException(LocaleUtil.getMessage(NOT_EDIT));
        }
        return itemExcelTemplateReqDTOList;
    }
    private String transferItemType(String itemType) {
        if(ObjectUtils.isEmpty(itemType)){
            return null;
        }
        if("称重".equals(ExportLocaleEnum.transfer2Cn(itemType.trim()))){
            return "3";
        }
        return "普通".equals(ExportLocaleEnum.transfer2Cn(itemType.trim())) ? "4" : "2";
    }

    @ApiOperation(value = "批量导入门店商品")
    @PostMapping("/store/batch_import")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "批量导入门店商品", action = OperatorType.ADD)
    public Result batchImportStoreItem(@RequestParam("file") MultipartFile file, @RequestParam("data") String storeGuid) {
        log.info("批量导入门店商品入参：storeGuid={}", storeGuid);
        List<ItemExcelTemplateReqDTO> itemExcelTemplateReqDTOList = conversionFile2Data(file);
        ItemDoubleParamDTO<List<ItemExcelTemplateReqDTO>, String> itemDoubleParamDTO = new ItemDoubleParamDTO<>();
        itemDoubleParamDTO.setFirstData(itemExcelTemplateReqDTOList);
        itemDoubleParamDTO.setSecondData(storeGuid);
        itemDoubleParamDTO.setFrom(ModuleEntranceEnum.STORE.code());
        int importItemSize = itemClientService.batchImportItem(itemDoubleParamDTO);
        if (importItemSize == 0) {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.IMPORT_FAILED));
        } else {
            return Result.buildSuccessMsg(String.format(LocaleUtil.getMessage(Constants.IMPORTED_PRODUCTS),importItemSize,itemExcelTemplateReqDTOList.size() - importItemSize));
        }
    }

    @ApiOperation(value = "批量导入品牌库商品")
    @PostMapping("/brand/batch_import")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "批量导入品牌库商品", action = OperatorType.ADD)
    public Result batchImportBrandItem(@RequestParam("file") MultipartFile file, @RequestParam("data") String brandGuid) {
        log.info("批量导入品牌库商品入参：brandGuid={}", brandGuid);
        List<ItemExcelTemplateReqDTO> itemExcelTemplateReqDTOList = conversionFile2Data(file);
        ItemDoubleParamDTO<List<ItemExcelTemplateReqDTO>, String> itemDoubleParamDTO = new ItemDoubleParamDTO<>();
        itemDoubleParamDTO.setFirstData(itemExcelTemplateReqDTOList);
        itemDoubleParamDTO.setSecondData(brandGuid);
        itemDoubleParamDTO.setFrom(ModuleEntranceEnum.BRAND.code());
        int importItemSize = itemClientService.batchImportItem(itemDoubleParamDTO);
        if (importItemSize == 0) {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.IMPORT_FAILED));
        } else {
            return Result.buildSuccessMsg(String.format(LocaleUtil.getMessage(Constants.IMPORTED_PRODUCTS),importItemSize,itemExcelTemplateReqDTOList.size() - importItemSize));
        }
    }


    @ApiOperation(value = "零售版—>批量导入门店商品 p1:文件上传解析验证")
    @PostMapping("/retail/store/batch_import_item")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM, description = "零售版—>批量导入门店商品 p1:文件上传解析验证", action = OperatorType.SELECT)
    public Result<List<ItemBatchImportTempRespDTO>> storeRetailItemBatchImport(@RequestParam("file") MultipartFile file, @RequestParam("data") String storeGuid) {
        log.info("零售版—>批量导入门店商品 p1:文件上传解析验证入参：storeGuid={}", storeGuid);
        //解析excel为list商品
        List<ItemExcelTemplateReqDTO> itemExcelTemplateReqDTOList = convertFile2Data(file, 1);
        //批量验证解析出的商品
        List<ItemBatchImportTempRespDTO> itemBatchImportTempRespDTOS = itemHelper.itemBatchVerify(itemExcelTemplateReqDTOList, storeGuid, 0, 1);
        return Result.buildSuccessResult(itemBatchImportTempRespDTOS);
    }

    private List<ItemExcelTemplateReqDTO> conversionFile2Data(MultipartFile file) {
        List<ItemExcelTemplateReqDTO> itemExcelTemplateReqDTOList;
        try {
            InputStream inputStream = file.getInputStream();

            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            // 只要第一个sheet
            XSSFSheet sheet = workbook.getSheetAt(0);

            List<List<String>> data = ExcelUtil.getSheetData(sheet, 2, 0);
            if (CollectionUtils.isEmpty(data)) {
                throw new ParameterException("导入0个商品");
            }
            itemExcelTemplateReqDTOList = new ArrayList<>(data.size());

            try {
                data.forEach(list -> {
                    if (CollectionUtils.isEmpty(list)) {
                        return;
                    }
                    // 必填值未填的项，直接pass
                    ItemExcelTemplateReqDTO itemExcelTemplateReqDTO = new ItemExcelTemplateReqDTO();
                    //分类名称
                    if (ObjectUtils.isEmpty(list.get(0))) {
                        return;
                    }
                    itemExcelTemplateReqDTO.setTypeName(list.get(0));
                    //商品名称
                    if (ObjectUtils.isEmpty(list.get(1))) {
                        return;
                    }
                    itemExcelTemplateReqDTO.setItemName(list.get(1));
                    // 选填 商品简称
                    itemExcelTemplateReqDTO.setNameAbbr(list.get(2));
                    //商品类型
                    if (ObjectUtils.isEmpty(list.get(3))) {
                        return;
                    }
                    itemExcelTemplateReqDTO.setItemType(list.get(3));
                    //起卖数
                    if (ObjectUtils.isEmpty(list.get(4))) {
                        return;
                    }
                    itemExcelTemplateReqDTO.setMinOrderNum(new BigDecimal(list.get(4)));
                    //固定参考价
                    if (ObjectUtils.isEmpty(list.get(5))) {
                        return;
                    }
                    itemExcelTemplateReqDTO.setSalePrice(new BigDecimal(list.get(5)));
                    itemExcelTemplateReqDTOList.add(itemExcelTemplateReqDTO);
                });
                if (CollectionUtils.isEmpty(itemExcelTemplateReqDTOList)) {
                    throw new ParameterException("可解析商品数为：0个商品，请检查");
                }

            } catch (RuntimeException e) {
                log.error("文件或数据格式错误");
                throw new BusinessException(LocaleUtil.getMessage(NOT_EDIT));
            }
        } catch (IOException e) {
            log.error("获取文件流错误", e);
            throw new BusinessException(LocaleUtil.getMessage(NOT_EDIT));
        } catch (UnsupportedFileFormatException e) {
            log.error("文件格式错误", e);
            throw new BusinessException(LocaleUtil.getMessage(NOT_EDIT));
        }
        return itemExcelTemplateReqDTOList;
    }


}
