package com.holderzone.holder.saas.aggregation.app.controller.message;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.app.entity.TerminalNoticeLocaleEnum;
import com.holderzone.holder.saas.aggregation.app.service.feign.MessageClientService;
import com.holderzone.saas.store.dto.message.MsgInfoRespDTO;
import com.holderzone.saas.store.dto.message.MsgQuery;
import com.holderzone.saas.store.dto.message.MsgRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MessageController
 * @date 2018/09/25 11:05
 * @description
 * @program holder-saas-aggregation-app
 */
@Api(value = "消息接口")
@RestController
@RequestMapping("/msg")
public class MessageController {

    private static final Logger logger = LoggerFactory.getLogger(MessageController.class);
    public static final int TWO_THOUSAND = 2000;

    private final MessageClientService messageClientService;

    @Autowired
    public MessageController(MessageClientService messageClientService) {
        this.messageClientService = messageClientService;
    }

    @ApiOperation(value = "查询消息列表")
    @PostMapping("/all")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MESSAGE,description = "查询消息列表")
    public Result<Page<MsgInfoRespDTO>> all(@RequestBody MsgQuery msgQuery) {
        if (msgQuery.getPageSize()>= TWO_THOUSAND){
            msgQuery.setPageSize(TWO_THOUSAND);
        }
        logger.info("查询消息列表 msgQuery={}", JacksonUtils.writeValueAsString(msgQuery));
        Page<MsgInfoRespDTO> msgs = messageClientService.queryAllInfo(msgQuery);
        if(CollectionUtil.isNotEmpty(msgs.getData())){
            msgs.getData().forEach(d -> d.setSubject(TerminalNoticeLocaleEnum.getLocale(d.getSubject())));
        }
        return Result.buildSuccessResult(msgs);
    }

    @ApiOperation(value = "查询某条消息详情，需要传入guid和state")
    @PostMapping("/detail")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MESSAGE,description = "查询某条消息详情")
    public Result<MsgRespDTO> detail(@RequestBody MsgQuery msgQuery) {
        logger.info("查询消息详情 msgQuery={}", JacksonUtils.writeValueAsString(msgQuery));
        return Result.buildSuccessResult(messageClientService.detail(msgQuery));
    }

    @PostMapping("/count")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MESSAGE,description = "查询门店消息未读详情")
    public Result<Integer> getCount(@RequestBody MsgQuery msgQuery){
        logger.info("查询门店消息未读详情，msgQuery{}", msgQuery);
        return Result.buildSuccessResult(messageClientService.count(msgQuery));
    }

    @GetMapping("/read_all")
    @ApiOperation(value = "一键已读")
    public Result<Void> readAll(@RequestParam("storeGuid") String storeGuid,
                                @RequestParam("messageType") Integer messageType) {
        messageClientService.readAll(storeGuid, messageType);
        return Result.buildEmptySuccess();
    }

}
