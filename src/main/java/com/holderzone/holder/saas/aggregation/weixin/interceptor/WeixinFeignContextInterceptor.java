package com.holderzone.holder.saas.aggregation.weixin.interceptor;

import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.common.CommonConstant;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;


/**
 * <AUTHOR>
 * @version 1.0
 * @program 调用方
 */
@Slf4j
@Configuration
@ConditionalOnClass(RequestInterceptor.class)
@ConditionalOnProperty(prefix = "hystrix.command.default.execution.isolation", name = "strategy", havingValue = "SEMAPHORE")
public class WeixinFeignContextInterceptor implements RequestInterceptor {

	@Override
	public void apply(RequestTemplate template) {
		WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
		String enterpriseGuid = WeixinUserThreadLocal.getEnterpriseGuid();
		if (!StringUtils.hasText(enterpriseGuid)) {
			enterpriseGuid = EnterpriseIdentifier.getEnterpriseGuid();
		}
		if (StringUtils.hasText(enterpriseGuid)) {
			template.header(CommonConstant.ENTERPRISEGUID_KEY, enterpriseGuid);
		}
		if (wxMemberSessionDTO!=null) {
			String wxmember = JacksonUtils.writeValueAsString(wxMemberSessionDTO);
			try {
				wxmember = URLEncoder.encode(wxmember, "utf-8");
				template.header(CommonConstant.WX_SESSION_USER, wxmember);
			} catch (UnsupportedEncodingException e) {
			    log.error("添加WX_SESSION_USER头出错", e);
			}
		}
	}
}
