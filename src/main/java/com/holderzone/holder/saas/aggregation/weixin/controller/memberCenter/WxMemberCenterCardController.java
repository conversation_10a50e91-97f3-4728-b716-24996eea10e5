package com.holderzone.holder.saas.aggregation.weixin.controller.memberCenter;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.WxBrandService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.memberCenter.WxMemberCenterCardService;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseNotOpenCardLevelRights;
import com.holderzone.holder.saas.member.wechat.dto.member.*;
import com.holderzone.saas.store.dto.organization.BrandDTO;
import com.holderzone.saas.store.dto.weixin.WxMemberCenterCardRespDTO;
import com.holderzone.saas.store.dto.weixin.WxStoreConsumerDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Api(description = "会员中心：卡")
@RestController
@RequestMapping("/wx_member_card")
@Slf4j
public class WxMemberCenterCardController {

//	@Resource
//	private IHsmMemberInfoCardService hsmMemberInfoCardService;

    @Resource
    private HsaBaseClientService hsaBaseClientService;

    @Autowired
    WxBrandService wxBrandService;

    private final WxMemberCenterCardService wxMemberCenterCardService;

    @Autowired
    public WxMemberCenterCardController(WxMemberCenterCardService wxMemberCenterCardService) {
        this.wxMemberCenterCardService = wxMemberCenterCardService;
    }

    @ApiOperation("查询会员主卡信息")
    @GetMapping("/getMemberMainCard")
    public Result<ResponseMemberAndCardInfo> getMemberMainCardByMemberInfoGuid(@RequestParam("memberInfoGuid") String memberInfoGuid) {
        return Result.buildSuccessResult(hsaBaseClientService.getMemberMainCardByMemberInfoGuid(memberInfoGuid).getData());
    }

    @ApiOperation("默认卡列表")
    @PostMapping("/card_list")
    public Result<WxMemberCenterCardRespDTO> cardList(@RequestBody WxStoreConsumerDTO wxStoreConsumerDTO) {
        log.info("会员卡列表入参:{}", JacksonUtils.writeValueAsString(wxStoreConsumerDTO));
        return Result.buildSuccessResult(wxMemberCenterCardService.cardList(wxStoreConsumerDTO));
    }

    @ApiOperation("已开通会员卡列表")
    @PostMapping("/member_card_list")
    public Result<List<ResponseMemberCardListOwned>> memberCardList(@RequestBody RequestQueryMemberCardList memberCardListQueryReqDTO) {
        log.info("已开通会员卡列表:{}", JacksonUtils.writeValueAsString(memberCardListQueryReqDTO));
//		List<MemberCardListOwnedRespDTO> memberCardList = hsmMemberInfoCardService.getMemberCard(memberCardListQueryReqDTO).getMemberCardList();
        List<ResponseMemberCardListOwned> openedCardList = hsaBaseClientService.getMemberCard(memberCardListQueryReqDTO).getData().getOpenedCardList();
        BrandDTO brandDetail = wxBrandService.getBrandDetail(memberCardListQueryReqDTO);
        return Result.buildSuccessResult(ObjectUtils.isEmpty(openedCardList) ? Collections.emptyList() : openedCardList.stream().map(a -> a.setCardLogo(brandDetail.getLogoUrl())).peek(x -> {
            if (x.getCardIcon() == null) {
                x.setCardIcon("");
            }
        }).collect(Collectors.toList()));
    }

    @ApiOperation("未开通的会员卡信息列表")
    @PostMapping("/no_open_memberCard")
    public Result<List<ResponseMemberCardListUnowned>> getMemberCard(@RequestBody @Validated RequestQueryMemberCardList memberCardListQueryReqDTO) {
//		List<MemberCardListUnownedRespDTO> cardList = hsmMemberInfoCardService.getMemberCard(memberCardListQueryReqDTO).getCardList();
        List<ResponseMemberCardListUnowned> nonactivatedCardList = hsaBaseClientService.getMemberCard(memberCardListQueryReqDTO).getData().getNonactivatedCardList();
        BrandDTO brandDetail = wxBrandService.getBrandDetail(memberCardListQueryReqDTO);
        return Result.buildSuccessResult(ObjectUtils.isEmpty(nonactivatedCardList) ? Collections.emptyList() : nonactivatedCardList.stream().map(a -> a.setCardLogo(brandDetail.getLogoUrl())).peek(x -> {
            if (x.getCardIcon() == null) {
                x.setCardIcon("");
            }
        }).collect(Collectors.toList()));
    }

    @ApiOperation("新的查询会员卡权益等级概要信息")
    @PostMapping("/getNewCardLevelSummaryInfo")
    public Result<ResponseCardLevelSummaryNew> getNewCardLevelSummaryInfo(@RequestBody RequestMemberCardSummaryQuery memberCardSummaryQueryReqDTO) {
        ResponseCardLevelSummaryNew newCardLevelSummaryInfo = hsaBaseClientService.getNewCardLevelSummaryInfo(memberCardSummaryQueryReqDTO).getData();

        //过滤会员价权益
        /*if (CollectionUtils.isNotEmpty(newCardLevelSummaryInfo.getCardLevelList())) {
            List<CardLevelList> collect = newCardLevelSummaryInfo.getCardLevelList().stream().peek(x -> {
                List<CardRightsList> cardRightList = x.getCardRightList();
                List<CardRightsList> collect1 = cardRightList.stream().filter(m -> m.getRightsTypeCode() != 3).collect(Collectors.toList());
                x.setCardRightList(collect1);
            }).collect(Collectors.toList());
            newCardLevelSummaryInfo.setCardLevelList(collect);
        }*/
        return Result.buildSuccessResult(newCardLevelSummaryInfo);
    }

    @ApiOperation("根据卡Guid查询卡等级即每个等级权益信息")
    @GetMapping("/getCardLevelAndRightInfo")
    public Result<List<CardLevelList>> getCardLevelAndRightInfo(@RequestParam("cardGuid") String cardGuid) {
//		List<CardLevelListDTO> cardLevelAndRightInfo = hsmMemberInfoCardService.getCardLevelAndRightInfo(cardGuid);
        List<CardLevelList> cardLevelAndRightInfo = hsaBaseClientService.getCardLevelAndRightInfo(cardGuid).getData();
        log.info("返回等级权益:{}", cardLevelAndRightInfo);
        /*if (!ObjectUtils.isEmpty(cardLevelAndRightInfo)) {
            cardLevelAndRightInfo = cardLevelAndRightInfo.stream().peek(x -> {
                List<CardRightsList> cardRightList = x.getCardRightList();
                if (!ObjectUtils.isEmpty(cardRightList)) {
                    x.setCardRightList(cardRightList.stream().filter(k -> 3 != k.getRightsTypeCode()).collect(Collectors.toList()));
                }
            }).collect(Collectors.toList());
            log.info("过滤会员价等级权益:{}", cardLevelAndRightInfo);
            return Result.buildSuccessResult(cardLevelAndRightInfo);
        }*/
        return Result.buildSuccessResult(cardLevelAndRightInfo);
//        return Result.buildSuccessResult(Collections.emptyList());
    }

    @ApiOperation("未开通卡的等级及所有权益")
    @PostMapping("/getNotOpenCardLevelRights")
    public Result<ResponseNotOpenCardLevelRights> getNotOpenCardLevelRights(@RequestBody @Validated RequestMemberCardSummaryQuery cardSummaryQueryReqDTO) {
//        NotOpenCardLevelRightsRespDto notOpenCardLevelRights = hsmMemberInfoCardService.getNotOpenCardLevelRights(cardSummaryQueryReqDTO);
        ResponseNotOpenCardLevelRights notOpenCardLevelRights = hsaBaseClientService.getNotOpenCardLevelRights(cardSummaryQueryReqDTO).getData();
        log.info("getNotOpenCardLevelRights 查询出的会员卡权益信息{}", JacksonUtils.writeValueAsString(notOpenCardLevelRights));
        ResponseNotOpenCardLevelRights responseNotOpenCardLevelRightsDTO = new ResponseNotOpenCardLevelRights();
        //过滤会员价权益
        /*if (CollectionUtils.isNotEmpty(notOpenCardLevelRights.getAllLevelCardRightList())) {
            List<CardLevelRight> allLevelCardRightList = notOpenCardLevelRights.getAllLevelCardRightList();
            List<CardLevelRight> collect = allLevelCardRightList.stream().filter(x -> x.getRightsTypeCode() != 3).collect(Collectors.toList());
            notOpenCardLevelRights.setAllLevelCardRightList(collect);
        }*/
        return Result.buildSuccessResult(responseNotOpenCardLevelRightsDTO);
    }
}
