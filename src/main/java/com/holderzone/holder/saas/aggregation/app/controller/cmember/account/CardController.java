package com.holderzone.holder.saas.aggregation.app.controller.cmember.account;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.CardClientService;
import com.holderzone.holder.saas.member.terminal.dto.card.RequestBaseCardInfo;
import com.holderzone.holder.saas.member.terminal.dto.card.RequestMemberCardRecharge;
import com.holderzone.holder.saas.member.terminal.dto.card.ResponseCardRechargeRule;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2019/6/19 16:00
 */
@Slf4j
@RequestMapping("/hsmca/card")
@RestController
@Api(description = "会员卡controller")
public class CardController {
    @Autowired
    private CardClientService cardClientService;

    /**
     * @param memberInfoCardGuid
     * @param rechargeReqDTO
     * @return com.holderzone.framework.response.Result
     * @Description 卡充值
     * <AUTHOR>
     * @Date 2019/6/19 16:20
     */
    @ApiOperation(value = "卡充值")
    @PostMapping("/{memberInfoCardGuid}/recharge")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "卡充值", action = OperatorType.UPDATE)
    public Result cardCharge(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid, @RequestBody @Validated RequestMemberCardRecharge rechargeReqDTO) {

        return Result.buildSuccessResult(cardClientService.cardCharge(memberInfoCardGuid, rechargeReqDTO));
    }

    /**
     * @param
     * @return com.holderzone.framework.response.Result
     * @Description 通过持卡人guid 查询当前卡等级对应的在某门店充值规则
     * <AUTHOR>
     * @Date 2019/6/17 10:03
     */
    @GetMapping("/{memberInfoCardGuid}/rechargeRule")
    @ApiOperation(value = "通过持卡人guid 查询当前卡等级对应的在某门店充值规则", response = Map.class)
    @ApiImplicitParam(value = "持卡人guid", required = true, dataType = "String", paramType = "path")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "通过持卡人guid 查询当前卡等级对应的在某门店充值规则",action = OperatorType.SELECT)
    public Result getRechargeRuleByMemberInfoCardGuid(@PathVariable("memberInfoCardGuid") String memberInfoCardGuid) {
        Map map = new HashMap<String, String>(1);
        map.put("rechargeRule", cardClientService.getRechargeRuleByMemberInfoCardGuid(memberInfoCardGuid));
        return Result.buildSuccessResult(map);
    }

    /**
     * 通过持卡人guid查询当前卡等级对应的在某门店充值规则
     *
     * @param memberInfoCardGuid 持卡人guid
     * @return 充值规则
     */
    @ApiOperation(value = "通过持卡人guid查询当前卡等级对应的在某门店充值规则", notes = "通过持卡人guid查询当前卡等级对应的在某门店充值规则")
    @GetMapping(value = "/cardRechargeRule", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(value = "持卡人guid", required = true, dataType = "String", paramType = "path")
    public Result<ResponseCardRechargeRule> memberCardRechargeRule(@RequestParam("memberInfoCardGuid") String memberInfoCardGuid) {
        log.info("通过持卡人guid查询当前卡等级对应的在某门店充值规则，memberInfoCardGuid=" + memberInfoCardGuid);
        return Result.buildSuccessResult(cardClientService.memberCardRechargeRule(memberInfoCardGuid));
    }

    @ApiOperation("会员卡开通")
    @PostMapping("/open")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "会员卡开通", action = OperatorType.UPDATE)
    public Result open(@Validated @RequestBody RequestBaseCardInfo cardInfo) {
        cardClientService.open(cardInfo);
        return Result.buildEmptySuccess();
    }

    @ApiOperation("顾客或会员开通权益卡")
    @GetMapping(value = "/openEquityCard", produces = "application/json;charset=utf-8")
    public void openEquityCard(@ApiParam(name = "guid", value = "顾客或会员GUID", required = true)
                               @RequestParam("guid") String guid,
                               @ApiParam(name = "equityCardGuid", value = "权益卡GUID", required = true)
                               @RequestParam("equityCardGuid") String equityCardGuid,
                               @ApiParam(name = "equityCardExpirationGuid", value = "权益卡规格GUID", required = true)
                               @RequestParam(value = "equityCardExpirationGuid") String equityCardExpirationGuid,
                               @ApiParam(name = "sourceType", value = "终端类型 (1:后台;2:一体机;3:微信公众号;4:POS)", required = true)
                               @RequestParam(value = "sourceType") Integer sourceType) {
        cardClientService.openEquityCard(guid, equityCardGuid, equityCardExpirationGuid, sourceType);
    }
}
