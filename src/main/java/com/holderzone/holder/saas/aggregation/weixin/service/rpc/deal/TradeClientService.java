package com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseVolumeList;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.order.OrderDTO;
import com.holderzone.saas.store.dto.order.OrderWechatDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.request.item.UpdateOrderItemInfoDTO;
import com.holderzone.saas.store.dto.order.request.member.MemberCouponListReqDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.groupon.GrouponListRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.trade.req.UpdateOrderMemberInfoReqDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

@Service
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = TradeClientService.TradeClientServiceFallBack.class)
public interface TradeClientService {

    @PostMapping("/dine_in_order/get_order_detail")
    DineinOrderDetailRespDTO getOrderDetail(SingleDataDTO singleDataDTO);

    @ApiOperation(value = "获取微信订单详情", notes = "获取微信订单详情")
    @PostMapping("/we_chat/get_order")
    OrderWechatDTO getOrder(@RequestBody String orderGuid);

    @ApiOperation(value = "计算账单优惠", notes = "计算账单优惠")
    @PostMapping("/dine_in_bill/calculate")
    DineinOrderDetailRespDTO calculate(BillCalculateReqDTO billCalculateReqDTO);

    @PostMapping("/dine_in_bill/pay")
    EstimateItemRespDTO pay(@RequestBody BillPayReqDTO billPayReqDTO);

    @ApiOperation(value = "批量验券置灰")
    @PostMapping("/we_chat/check_volume")
    DineinOrderDetailRespDTO checkVolume(BillCalculateReqDTO billCalculateReqDTO);

    @ApiOperation(value = "已登陆会员优惠券列表", notes = "已登陆会员优惠券列表")
    @PostMapping("/member/coupon_list")
    List<ResponseVolumeList> verify(@RequestBody MemberCouponListReqDTO memberCouponListReqDTO);

    @ApiOperation(value = "获取订单详情", notes = "获取订单详情")
    @GetMapping("/dine_in_order/get_order_detail/{orderGuid}")
    DineinOrderDetailRespDTO getOrderDetail(@PathVariable("orderGuid") String orderGuid);

    @ApiOperation(value = "绑定会员消费guid", notes = "绑定会员消费guid")
    @PostMapping("/fast_food/update_member_consumption_guid")
    void updateMemberConsumptionGuid(@RequestBody OrderDTO orderDTO);


    @ApiOperation(value = "更新订单商品信息")
    @PostMapping("/fast_food/update_order_item_info")
    void updateOrderItemInfo(@RequestBody UpdateOrderItemInfoDTO orderItemInfoDTO);

    @ApiOperation(value = "更新订单会员信息")
    @PostMapping("/order_detail/update_order_member_info")
    void updateOrderMemberInfo(@RequestBody UpdateOrderMemberInfoReqDTO reqDTO);

    @GetMapping("/groupon/list")
    List<GrouponListRespDTO> useGrouponList(@RequestParam("orderGuid") String orderGuid,
                                            @RequestParam(value = "grouponType", required = false) Integer grouponType);

    @Slf4j
    @Component
    class TradeClientServiceFallBack implements FallbackFactory<TradeClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";
        @Override
        public TradeClientService create(Throwable throwable) {
            return new TradeClientService() {
                @Override
                public DineinOrderDetailRespDTO getOrderDetail(SingleDataDTO singleDataDTO) {
                    log.error("查询订单详情失败:{}", singleDataDTO);
                    return null;
                }

                @Override
                public OrderWechatDTO getOrder(String orderGuid) {
                    log.error("查询订单状态失败:{}", orderGuid);
                    return null;
                }

                @Override
                public DineinOrderDetailRespDTO calculate(BillCalculateReqDTO billCalculateReqDTO) {
                    log.error("计算账单优惠失败:{}", JacksonUtils.writeValueAsString(billCalculateReqDTO));
                    throwable.printStackTrace();
                    return null;
                }

                @Override
                public EstimateItemRespDTO pay(BillPayReqDTO billPayReqDTO) {
                    log.error("支付失败:{}", billPayReqDTO);
                    EstimateItemRespDTO estimateItemRespDTO = new EstimateItemRespDTO();
                    estimateItemRespDTO.setResult(false);
                    return estimateItemRespDTO;
                }

                @Override
                public DineinOrderDetailRespDTO checkVolume(BillCalculateReqDTO billCalculateReqDTO) {
                    log.error("批量验券置灰失败：{}", billCalculateReqDTO);
                    return null;
                }

                @Override
                public List<ResponseVolumeList> verify(MemberCouponListReqDTO memberCouponListReqDTO) {
                    log.error("查询已登陆会员优惠券列表失败:{}", memberCouponListReqDTO);
                    return Collections.emptyList();
                }

                @Override
                public DineinOrderDetailRespDTO getOrderDetail(String orderGuid) {
                    log.error("获取订单详情失败，orderGuid={}", orderGuid);
                    return null;
                }

                @Override
                public void updateMemberConsumptionGuid(OrderDTO orderDTO) {
                    log.error("绑定会员消费guid失败，orderDTO:{}", JacksonUtils.writeValueAsString(orderDTO));
                }

                @Override
                public void updateOrderItemInfo(UpdateOrderItemInfoDTO orderItemInfoDTO) {
                    log.error("更新订单商品信息失败，orderItemInfoDTO:{}", JacksonUtils.writeValueAsString(orderItemInfoDTO));
                }

                @Override
                public void updateOrderMemberInfo(UpdateOrderMemberInfoReqDTO reqDTO) {
                    log.error("更新订单会员信息失败，reqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
                }

                @Override
                public List<GrouponListRespDTO> useGrouponList(String orderGuid, Integer grouponType) {
                    log.error(HYSTRIX_PATTERN, "useGrouponList", orderGuid + "-" + grouponType,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
