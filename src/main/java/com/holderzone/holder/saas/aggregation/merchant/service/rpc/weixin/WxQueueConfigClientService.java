package com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin;

import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.weixin.req.WxQueueConfigUpdateBatchReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxQueueConfigDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxQueueConfigClientService
 * @date 2019/05/13 10:42
 * @description 门店微信线上排队client
 * @program holder-saas-store
 */
@Component
@FeignClient(value = "holder-saas-store-weixin", fallbackFactory = WxQueueConfigClientService.FallBackClass.class)
public interface WxQueueConfigClientService {

    @PostMapping("/wx_queue/page_config")
    Page<WxQueueConfigDTO> pageConfig(WxStorePageReqDTO wxStorePageReqDTO);

    @PostMapping("/wx_queue/get_by_guid")
    WxQueueConfigDTO getByGuid(WxQueueConfigDTO wxQueueConfigDTO);

    @PostMapping("/wx_queue/update_by_guid")
    Boolean updateByGuid(WxQueueConfigDTO wxQueueConfigDTO);

    @PostMapping("/wx_queue/batch_update")
    Boolean batchUpdate(WxQueueConfigUpdateBatchReqDTO wxQueueConfigUpdateBatchReqDTO);

    @Component
    @Slf4j
    class FallBackClass implements FallbackFactory<WxQueueConfigClientService> {
        @Override
        public WxQueueConfigClientService create(Throwable throwable) {
            return new WxQueueConfigClientService() {
                @Override
                public Page<WxQueueConfigDTO> pageConfig(WxStorePageReqDTO wxStorePageReqDTO) {
                    log.error("分页查询排队配置，调用微信服务异常,入参: {}，e: {}", wxStorePageReqDTO, throwable.getMessage());
                    throw new RuntimeException("分页查询排队配置，调用微信服务异常,e: {}", throwable.getCause());
                }

                @Override
                public WxQueueConfigDTO getByGuid(WxQueueConfigDTO wxQueueConfigDTO) {
                    log.error("查询排队配置详情，调用微信服务异常,入参: {}，e: {}", wxQueueConfigDTO, throwable.getMessage());
                    throw new RuntimeException("查询排队配置详情，调用微信服务异常,e: {}", throwable.getCause());
                }

                @Override
                public Boolean updateByGuid(WxQueueConfigDTO wxQueueConfigDTO) {
                    log.error("修改排队配置，调用微信服务异常,入参: {}，e: {}", wxQueueConfigDTO, throwable.getMessage());
                    throw new RuntimeException("修改排队配置，调用微信服务异常,e: {}", throwable.getCause());
                }

                @Override
                public Boolean batchUpdate(WxQueueConfigUpdateBatchReqDTO wxQueueConfigUpdateBatchReqDTO) {
                    log.error("批量修改排队配置，调用微信服务异常,入参: {}，e: {}", wxQueueConfigUpdateBatchReqDTO, throwable.getMessage());
                    throw new RuntimeException("批量修改排队配置，调用微信服务异常,e: {}", throwable.getCause());
                }
            };
        }
    }
}
