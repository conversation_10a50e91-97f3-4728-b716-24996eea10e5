package com.holderzone.holder.saas.aggregation.merchant.controller.weixin;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin.WxTempMsgClientService;
import com.holderzone.saas.store.dto.weixin.open.WxMpTemplateDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @date 2024/5/13
 * @description 微信公众号消息模板
 */
@Api("微信公众号消息模板")
@RequestMapping("/wx_temp")
@RestController
@Slf4j
public class WxTempMsgController {

    @Autowired
    WxTempMsgClientService wxTempMsgClientService;

    @PostMapping("/add_msg_template")
    @ApiOperation("手动添加消息模版")
    public void addMsgTemplate(@RequestBody WxMpTemplateDTO wxMpTemplateDTO) {
        log.info("[手动添加消息模版]sendMessageReqDTO={}", JacksonUtils.writeValueAsString(wxMpTemplateDTO));
        wxTempMsgClientService.addMsgTemplate(wxMpTemplateDTO);
    }

}
