package com.holderzone.holder.saas.aggregation.app.entity.auth;

import com.holderzone.holder.saas.aggregation.app.anno.DataAuthFieldControl;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用餐方式统计
 */
@Data
public class DiningTypeSaleDTO implements Serializable {

    private static final long serialVersionUID = -1762052726741840809L;

    /**
     * 用餐类型
     * 1正餐 2快餐 3外卖
     */
    private String typeCode;

    /**
     * 用餐类型名称
     */
    @DataAuthFieldControl("dining_sale_type_name")
    private String typeName;

    /**
     * 订单数
     */
    @DataAuthFieldControl("dining_sale_order_count")
    private String orderCount;

    /**
     * 消费人数
     */
    @DataAuthFieldControl("dining_sale_guest_count")
    private String guestCount;

    /**
     * 销售净额
     */
    @DataAuthFieldControl("dining_sale_actually_amount")
    private String amount;

    /**
     * 单均消费
     */
    @DataAuthFieldControl("dining_sale_order_price")
    private String orderPrice;

    /**
     * 人均消费
     */
    @DataAuthFieldControl("dining_sale_guest_price")
    private String guestPrice;

    /**
     * 外卖细则
     */
    @DataAuthFieldControl
    private List<DiningTypeSaleDTO> subDiningTypes;

    /**
     * 是否合计项,0否 1是
     */
    private int isTotal = 0;

}