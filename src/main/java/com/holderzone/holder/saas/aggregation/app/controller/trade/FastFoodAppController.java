package com.holderzone.holder.saas.aggregation.app.controller.trade;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.FastFoodClientService;
import com.holderzone.saas.store.dto.order.common.HangOrderDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CreateFastFoodReqDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.enums.locale.LocaleMessageEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @className FastFoodAppController
 * @date 2018/09/04 11:26
 * @description app聚合层快餐订单接口
 * @program holder-saas-aggregation-app
 */
@RestController
@RequestMapping("/fast_food")
@Api(description = "快餐接口")
@Slf4j
public class FastFoodAppController {

    private final FastFoodClientService fastFoodClientService;

    @Autowired
    public FastFoodAppController(FastFoodClientService fastFoodClientService) {
        this.fastFoodClientService = fastFoodClientService;
    }

    @ApiOperation(value = "去结账", notes = "去结账")
    @PostMapping("/add_item")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "去结账", action = OperatorType.SELECT)
    public Result addItem(@RequestBody CreateFastFoodReqDTO createFastFoodReqDTO) {
        log.info("去结账入参：{}", JacksonUtils.writeValueAsString(createFastFoodReqDTO));
        EstimateItemRespDTO estimateResult = fastFoodClientService.addItem(createFastFoodReqDTO);
        if (estimateResult.getEstimate()) {
            return Result.buildOpFailedResult(LocaleMessageEnum.getLocale(estimateResult.getEstimateInfo())).setTData(estimateResult.getOrderGuid());
        }
        return Result.buildSuccessResult(estimateResult.getOrderGuid());

    }

    @ApiOperation(value = "挂起订单", notes = "挂起订单")
    @ApiImplicitParam(name = "hangOrderDTO", value = "hangOrderDTO", required = true, dataType =
            "HangOrderDTO")
    @PostMapping("/hang_order")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "挂起订单")
    public Result<String> hangOrder(@RequestBody HangOrderDTO hangOrderDTO) {
        return Result.buildSuccessResult(fastFoodClientService.hangOrder(hangOrderDTO));
    }

    @ApiOperation(value = "挂起订单列表", notes = "挂起订单列表")
    @ApiImplicitParam(name = "hangOrderDTO", value = "hangOrderDTO", required = true, dataType =
            "HangOrderDTO")
    @PostMapping("/hang_order_list")
//    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE,description = "挂起订单列表")
    public Result<Map<String, String>> hangOrderList(@RequestBody HangOrderDTO hangOrderDTO) {
        return Result.buildSuccessResult(fastFoodClientService.hangOrderList(hangOrderDTO));
    }

    @ApiOperation(value = "删除挂起订单", notes = "删除挂起订单")
    @ApiImplicitParam(name = "hangOrderDTO", value = "hangOrderDTO", required = true, dataType =
            "HangOrderDTO")
    @PostMapping("/gain_order")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_TRADE, description = "删除挂起订单", action = OperatorType.DELETE)
    public Result gainOrder(@RequestBody HangOrderDTO hangOrderDTO) {
        if (fastFoodClientService.gainOrder(hangOrderDTO)) {
            return Result.buildSuccessResult("删除挂起订单成功");
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constant.OPERATION_FAILED));
    }

}
