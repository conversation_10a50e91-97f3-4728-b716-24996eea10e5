package com.holderzone.holder.saas.aggregation.app.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @className LockTableInRedisson
 * @date 2019/01/16 11:34
 * @description 在Redisson中将桌台锁住（只做Redisson分布式锁） 需要将带有锁定字段的实体，作为函数的第一入参
 * 默认是tableGuid字段，如果在字段上打了@LockFiled注解会锁住该字段
 * @program holder-saas-store-table
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireRdsLock {

}
