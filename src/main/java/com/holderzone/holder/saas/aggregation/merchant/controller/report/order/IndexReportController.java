package com.holderzone.holder.saas.aggregation.merchant.controller.report.order;


import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.order.IndexService;
import com.holderzone.saas.store.dto.report.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/index")
@Api(description = "聚合层首页报表",hidden = true)
@Deprecated
public class IndexReportController {

    private static final Logger logger = LoggerFactory.getLogger(IndexReportController.class);

    @Autowired
    private IndexService indexService;

    @PostMapping(value = "/queryInComeOverview")
    @ApiOperation(value = "收入概览")
    @Deprecated
    public Result<IndexInComeOverviewDTO> queryInComeOverview(@RequestBody IndexQuery indexQuery){

        return Result.buildSuccessResult(indexService.queryInComeOverview(indexQuery));
    }

    @PostMapping(value = "/queryCustomerOverview")
    @ApiOperation(value = "顾客概览")
    @Deprecated
    public Result<IndexCustomerDTO> queryCustomerOverview(@RequestBody IndexQuery indexQuery){

        return Result.buildSuccessResult(indexService.queryCustomerOverview(indexQuery));
    }

    @PostMapping(value = "/queryStoreSore")
    @ApiOperation(value = "门店收入排名")
    @Deprecated
    public Result<Page<IndexStoreSortDTO>> queryStoreSore(@RequestBody IndexQuery indexQuery){

        return Result.buildSuccessResult(indexService.queryStoreSore(indexQuery));
    }

    @PostMapping(value = "/orderOverview")
    @ApiOperation(value = "订单概览")
    @Deprecated
    public Result<IndexOrderOverviewDto> indexOrderOverview(@RequestBody IndexQuery indexQuery){
        return Result.buildSuccessResult(indexService.orderOverview(indexQuery));
    }

}
