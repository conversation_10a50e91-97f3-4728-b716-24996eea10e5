package com.holderzone.holder.saas.aggregation.weixin.service.impl;

import com.google.common.collect.Lists;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.constant.RedisConstants;
import com.holderzone.holder.saas.aggregation.weixin.entity.dto.DealMemberPayReqDTO;
import com.holderzone.holder.saas.aggregation.weixin.service.MenuItemService;
import com.holderzone.holder.saas.aggregation.weixin.service.PayService;
import com.holderzone.holder.saas.aggregation.weixin.service.WxStoreTradeOrderService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.*;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.MemberMerchantClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.DineInOrderClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.TradeClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal.WxClientService;
import com.holderzone.holder.saas.aggregation.weixin.utils.AesEncryptUtils;
import com.holderzone.holder.saas.aggregation.weixin.utils.HttpUtils;
import com.holderzone.holder.saas.aggregation.weixin.utils.UserMemberSessionUtils;
import com.holderzone.holder.saas.member.merchant.dto.label.RequestManualLabel;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseIntegralOffset;
import com.holderzone.holder.saas.weixin.common.CacheName;
import com.holderzone.holder.saas.weixin.entry.dto.OrderDetailDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.entry.dto.WxUserInfoDTO;
import com.holderzone.holder.saas.weixin.entry.dto.req.WxOrderDetailReqDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WebsocketUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.saas.store.dto.business.manage.HandoverRecordDTO;
import com.holderzone.saas.store.dto.business.manage.StoreConfigQueryDTO;
import com.holderzone.saas.store.dto.config.resp.DineFoodSettingRespDTO;
import com.holderzone.saas.store.dto.order.IslandUserAmountDTO;
import com.holderzone.saas.store.dto.order.ShopOrderIslandUserAmountDTO;
import com.holderzone.saas.store.dto.order.common.DineInItemDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillCalculateReqDTO;
import com.holderzone.saas.store.dto.order.request.bill.BillPayReqDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountFeeDetailDTO;
import com.holderzone.saas.store.dto.order.response.bill.DiscountRuleDTO;
import com.holderzone.saas.store.dto.order.response.dinein.DineinOrderDetailRespDTO;
import com.holderzone.saas.store.dto.order.response.item.EstimateItemRespDTO;
import com.holderzone.saas.store.dto.pay.AggPayRespDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;
import com.holderzone.saas.store.dto.trade.req.UpdateOrderMemberInfoReqDTO;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;
import com.holderzone.saas.store.dto.weixin.WxPrepayRespDTO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.req.WxMemberTradeNotifyReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.PayWayRespDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxOrderConfigDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxPayRespDTO;
import com.holderzone.saas.store.enums.BaseDeviceTypeEnum;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.order.TradeModeEnum;
import com.holderzone.saas.store.enums.weixin.WxAppletMemberPayTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PayServiceImpl implements PayService {

    private final WxClientService wxClientService;
    private final TradeClientService tradeClientService;
    private final UserMemberSessionUtils userMemberSessionUtils;
    private final WxOrderRecordClientService wxOrderRecordClientService;
    private final RedisUtils redisUtils;
    private final WebsocketUtils websocketUtils;
    @Resource
    MenuItemService menuItemService;
    @Value("${memberPay.key}")
    private String memberPayKey;
    @Value("${zhuancan.host}")
    private String zhuancanRequestHost;
    public static final String QUERY_ISLAND_USER_URL = "%s/island/api/island-user-by-memberInGuid";
    public static final String ISLAND_USER_PAY_URL = "%s/shop/api/order-resolve/island-user-amount-pay";
    @Resource
    private WxStorePayClientService wxStorePayClientService;
    @Resource
    private HsaBaseClientService hsaBaseClientService;
    @Resource
    private MemberMerchantClientService merchantClientService;
    @Resource
    private BusinessClientService businessClientService;
    @Resource
    private StoreOrganizationClientService storeOrganizationClientService;
    @Resource
    private WxStoreTradeOrderService wxStoreTradeOrderService;
    @Resource
    private DineInOrderClientService dineInOrderClientService;
    @Resource
    private TradeOrderService tradeOrderService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private MemberMarketingClientService memberMarketingClientService;

    @Autowired
    public PayServiceImpl(WxClientService wxClientService, TradeClientService tradeClientService, UserMemberSessionUtils userMemberSessionUtils, WxOrderRecordClientService wxOrderRecordClientService, RedisUtils redisUtils, WebsocketUtils websocketUtils) {
        this.wxClientService = wxClientService;
        this.tradeClientService = tradeClientService;
        this.userMemberSessionUtils = userMemberSessionUtils;
        this.wxOrderRecordClientService = wxOrderRecordClientService;
        this.redisUtils = redisUtils;
        this.websocketUtils = websocketUtils;
    }

    @Override
    public WxPrepayRespDTO prepay(String orderRecordGuid) {
        return orderChange(orderRecordGuid) ? WxPrepayRespDTO.changeFailed() : WxPrepayRespDTO.emptySuccess();
    }

    @Override
    public PayWayRespDTO getAllPayWay(String orderGuid) {
        String merchantOrderGuid = getMerchantOrderGuid(orderGuid);
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        PayWayRespDTO payWayRespDTO = new PayWayRespDTO();
        if (StringUtils.isEmpty(merchantOrderGuid) || wxMemberSessionDTO == null) {
            return PayWayRespDTO.changeFailed();
        }
        UserContext userContext = UserContextUtils.get();
        userContext.setOperSubjectGuid(wxMemberSessionDTO.getOperSubjectGuid());
        // 20掌控者微信公众号、21XXX公众号
        // 来自：com.holderzone.holder.saas.common.dto.HeaderUserInfo
        userContext.setSource("20");
        UserContextUtils.put(userContext);
        BillCalculateReqDTO billCalculateReqDTO = volumeQuery(merchantOrderGuid, false);
        DineinOrderDetailRespDTO calculate = null;
        try {
            calculate = tradeClientService.calculate(billCalculateReqDTO);
        } catch (Exception e) {
            log.error("获取支付方式时算价异常", e);
        }
        if (calculate == null) {
            return PayWayRespDTO.changeFailed();
        }
        payWayRespDTO.setOriginAmount(calculate.getActuallyPayFee());
        UserMemberSessionDTO userMemberSessionDTO = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
        userMemberSessionDTO.setPayAmount(calculate.getActuallyPayFee());
        payWayRespDTO.setPayAmount(calculate.getActuallyPayFee());

        String memberInfoCardGuid = userMemberSessionDTO.getMemberInfoCardGuid();
        if (WeixinUserThreadLocal.getIsLogin() && !StringUtils.isEmpty(memberInfoCardGuid) && !"0".equals(memberInfoCardGuid) && !"-1".equals(memberInfoCardGuid)) {
            //List<UserMemberCardCacheDTO> cardList = userMemberSessionUtils.getCardList(WeixinUserThreadLocal.getOpenId());
            List<UserMemberCardCacheDTO> cardList = menuItemService.cardList();
            if (!ObjectUtils.isEmpty(cardList)) {
                UserMemberCardCacheDTO cardItemDTO = cardList.stream().filter(x -> x.getMemberInfoCardGuid().equals(memberInfoCardGuid)).findFirst().orElse(null);
                log.info("getAllPayWay会员卡信息{}", JacksonUtils.writeValueAsString(cardItemDTO));
                if (cardItemDTO != null) {

                    payWayRespDTO.setCardName(cardItemDTO.getCardName());
                    payWayRespDTO.setSupportMember(1);
                    payWayRespDTO.setMemberBalance(cardItemDTO.getCardMoney());
                    //验证会员卡是否被冻结 true:未冻结 false：已冻结
                    if (cardItemDTO.getIsFreeze()) {
                        payWayRespDTO.setEnableMember(cardItemDTO.getCardMoney().compareTo(calculate.getActuallyPayFee()) >= 0 ? 1 : 0);
                    } else {
                        payWayRespDTO.setEnableMember(0);
                    }
                    List<DiscountFeeDetailDTO> discountFeeDetailDTOS = calculate.getDiscountFeeDetailDTOS();
                    if (!ObjectUtils.isEmpty(discountFeeDetailDTOS)) {
                        DiscountFeeDetailDTO feeDetailDTO = discountFeeDetailDTOS.stream().filter(x -> x.getDiscountType() == 1).findFirst().orElse(null);
                        if (feeDetailDTO != null && feeDetailDTO.getDiscountFee() != null) {
                            BigDecimal discountFee = feeDetailDTO.getDiscountFee();
                            payWayRespDTO.setOriginAmount(calculate.getActuallyPayFee().add(discountFee));
                            payWayRespDTO.setSupportPreference(1);
                        }
                    }
                }
            }
        }
        userMemberSessionUtils.addUserMemberSession(userMemberSessionDTO);
        return payWayRespDTO;
    }

    /**
     * @param merchantOrderGuid 商户订单id
     * @return 只查不验券
     */
    private BillCalculateReqDTO volumeQuery(String merchantOrderGuid, Boolean verify) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
        BillCalculateReqDTO billCalculateReqDTO = new BillCalculateReqDTO();
        billCalculateReqDTO.setOrderGuid(merchantOrderGuid);
        billCalculateReqDTO.setEnterpriseGuid(wxMemberSessionDTO.getEnterpriseGuid());
        billCalculateReqDTO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        billCalculateReqDTO.setStoreName(wxMemberSessionDTO.getStoreName());
        billCalculateReqDTO.setUserGuid(WeixinUserThreadLocal.getOpenId());
        billCalculateReqDTO.setUserName(WeixinUserThreadLocal.getNickName());
        billCalculateReqDTO.setDeviceId(WeixinUserThreadLocal.getOpenId());
        billCalculateReqDTO.setMemberLogin(2);
        billCalculateReqDTO.setDeviceType(12);
        if (!wxMemberSessionDTO.getWxUserInfoDTO().getIsLogin()) {
            log.info("计算优惠入参:{}", billCalculateReqDTO);
            return billCalculateReqDTO;
        }
        String volumeCode = userMemberSession.getVolumeCode();
        String memberInfoCardGuid = userMemberSession.getMemberInfoCardGuid();
        Integer enableIntegral = userMemberSession.getMemberIntegral();
        billCalculateReqDTO.setMemberInfoCardGuid(StringUtils.isEmpty(memberInfoCardGuid) || memberInfoCardGuid.length() < 5 ? null : memberInfoCardGuid);
        billCalculateReqDTO.setMemberIntegral(enableIntegral != null && enableIntegral == 1 ? 1 : 2);
        billCalculateReqDTO.setVolumeCode(StringUtils.isEmpty(volumeCode) || volumeCode.length() < 5 ? null : volumeCode);
        billCalculateReqDTO.setVerify(StringUtils.isEmpty(volumeCode) || volumeCode.length() < 5 ? null : verify ? 1 : 3);
        billCalculateReqDTO.setMemberLogin(!org.springframework.util.StringUtils.isEmpty(billCalculateReqDTO.getMemberInfoCardGuid())
                || !org.springframework.util.StringUtils.isEmpty(billCalculateReqDTO.getVolumeCode()) ? 1 : 2);
        billCalculateReqDTO.setMemberPhone(billCalculateReqDTO.getMemberLogin() == 1 ? WeixinUserThreadLocal.getOpenId() : null);
        log.info("计算优惠入参:{}", billCalculateReqDTO);
        return billCalculateReqDTO;
    }

    @Override
    public ZeroPayResultRespDTO zeroPay(DealMemberPayReqDTO dealMemberPayReqDTO) {
        String merchantOrderGuid = getMerchantOrderGuid(dealMemberPayReqDTO.getOrderGuid());
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        if (StringUtils.isEmpty(merchantOrderGuid) || wxMemberSessionDTO == null) {
            return ZeroPayResultRespDTO.changeFailed();
        }
        // 计算订单
        DineinOrderDetailRespDTO calculate = calculateZeroOrderDetail(merchantOrderGuid);
        // 校验订单
        MemberPayReusltRespDTO payResultRespDTO = verifyOrder(dealMemberPayReqDTO, calculate);
        if (Objects.nonNull(payResultRespDTO)) {
            return new ZeroPayResultRespDTO(payResultRespDTO.getResult(), payResultRespDTO.getErrorMsg());
        }
        log.info("订单0元支付，当前订单详情:{}", JacksonUtils.writeValueAsString(calculate));
        return zeroPay(calculate, dealMemberPayReqDTO);
    }

    private ZeroPayResultRespDTO zeroPay(DineinOrderDetailRespDTO calculate, DealMemberPayReqDTO dealMemberPayReqDTO) {
        // 支付入参 校验金额
        if (verifyPayAmount(calculate, dealMemberPayReqDTO)) {
            return ZeroPayResultRespDTO.payAmountFailed();
        }
        // 校验订单会员信息
        verifyOrderMemberInfo(calculate, dealMemberPayReqDTO);
        // 构建支付参数
        BillPayReqDTO billPayReqDTO = buildBillPayReqDTO(calculate, dealMemberPayReqDTO);
        BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(calculate.getActuallyPayFee());
        String memberCardGuid = calculate.getMemberCardGuid();
        if (StringUtils.isEmpty(memberCardGuid) || "1".equals(memberCardGuid) || "0".equals(memberCardGuid)) {
            payment.setPaymentType(10);
            payment.setPaymentTypeName("在线支付");
        } else {
            payment.setPaymentType(4);
            payment.setPaymentTypeName("会员卡支付");
            billPayReqDTO.setMemberInfoCardGuid(memberCardGuid);
            ResponseIntegralOffset integralOffsetResultRespDTO = calculate.getIntegralOffsetResultRespDTO();
            billPayReqDTO.setUseIntegral(!ObjectUtils.isEmpty(integralOffsetResultRespDTO) && !ObjectUtils.isEmpty(integralOffsetResultRespDTO.getUseIntegral()) && integralOffsetResultRespDTO.getUseIntegral() > 0 ? integralOffsetResultRespDTO.getUseIntegral() : null);
            billPayReqDTO.setIntegralDiscountMoney(!ObjectUtils.isEmpty(integralOffsetResultRespDTO) && !ObjectUtils.isEmpty(integralOffsetResultRespDTO.getUseIntegral()) && integralOffsetResultRespDTO.getUseIntegral() > 0 ? integralOffsetResultRespDTO.getDeductionMoney() : null);
        }
        billPayReqDTO.setPayments(Collections.singletonList(payment));
        log.info("会员支付传参:{}", billPayReqDTO);

        try {
            EstimateItemRespDTO pay = tradeClientService.pay(billPayReqDTO);
            if (Boolean.TRUE.equals(pay.getResult())) {
                log.info("0元支付成功:{}", JacksonUtils.writeValueAsString(pay));
                // 后置处理
                successPayOrderHandler(calculate);
                return ZeroPayResultRespDTO.success();
            } else if (Boolean.TRUE.equals(pay.getEstimate())) {
                return ZeroPayResultRespDTO.soldOut();
            }
        } catch (Exception e) {
            if (e.getCause().toString().contains("根据菜单guid")) {
                return ZeroPayResultRespDTO.itemSynchronize();
            }
        }
        return ZeroPayResultRespDTO.payFailed();
    }


    /**
     * 封装结账人信息
     *
     * @param billPayReqDTO 支付入参
     */
    private void wrapStaffInfo(BillPayReqDTO billPayReqDTO) {
        //查询是否为特殊二维码（二维码关联员工信息）
        UserBriefDTO userBriefDTO = (UserBriefDTO) redisUtils.get("WX:TABLE:STAFF:" + WeixinUserThreadLocal.getEnterpriseGuid() + ":" + billPayReqDTO.getOrderGuid());
        if (Objects.nonNull(userBriefDTO)) {
            billPayReqDTO.setUserName(userBriefDTO.getUserName());
            billPayReqDTO.setUserGuid(userBriefDTO.getUserGuid());
            return;
        }
        // 非特殊二维码 需要查询当班员工
        UserBriefDTO onDutyUserBrief = queryOnDutyStaff();
        if (Objects.nonNull(onDutyUserBrief)) {
            log.info("随机结账员工:{}", JacksonUtils.writeValueAsString(onDutyUserBrief));
            billPayReqDTO.setUserName(onDutyUserBrief.getUserName());
            billPayReqDTO.setUserGuid(onDutyUserBrief.getUserGuid());
            return;
        }
        billPayReqDTO.setUserGuid(WeixinUserThreadLocal.getOpenId());
        billPayReqDTO.setUserName(WeixinUserThreadLocal.getNickName());
    }


    /**
     * 查询当班员工
     */
    private UserBriefDTO queryOnDutyStaff() {
        String enterpriseGuid = WeixinUserThreadLocal.getEnterpriseGuid();
        String storeGuid = WeixinUserThreadLocal.getStoreGuid();
        // 查询当班所有员工
        List<HandoverRecordDTO> handoverRecordList = businessClientService.queryOnDutyStaffs(storeGuid);
        log.info("查询到当前门店当班员工结果,门店guid:{},未交班列表:{}", storeGuid, JacksonUtils.writeValueAsString(handoverRecordList));
        if (CollectionUtils.isEmpty(handoverRecordList)) {
            return null;
        }
        // 查询主机设备
        StoreDeviceDTO masterDeviceByStoreGuid = storeOrganizationClientService.getMasterDeviceByStoreGuid(enterpriseGuid, storeGuid);
        if (Objects.nonNull(masterDeviceByStoreGuid)) {
            log.info("查询到当前门店主机设备信息, 门店guid:{}, 主机信息:{}", storeGuid, JacksonUtils.writeValueAsString(masterDeviceByStoreGuid));
            handoverRecordList = handoverRecordList.stream().filter(e -> e.getTerminalId().equals(masterDeviceByStoreGuid.getDeviceNo())).collect(Collectors.toList());
        }
        // 主机当班人员未找到，则找其他设备的当班人员
        HandoverRecordDTO randomHandoverRecord = getRandomHandoverRecord(handoverRecordList);
        return copyProperties(randomHandoverRecord);
    }


    /**
     * 当班员工列表中随机获取一位员工
     */
    private HandoverRecordDTO getRandomHandoverRecord(List<HandoverRecordDTO> handoverRecordList) {
        if (CollectionUtils.isEmpty(handoverRecordList)) {
            return null;
        }
        return handoverRecordList.get(new SecureRandom().nextInt(handoverRecordList.size()));
    }

    private UserBriefDTO copyProperties(HandoverRecordDTO handoverRecordDTO) {
        if (Objects.isNull(handoverRecordDTO)) {
            return null;
        }
        UserBriefDTO userBriefDTO = new UserBriefDTO();
        userBriefDTO.setUserGuid(handoverRecordDTO.getCreateGuid());
        userBriefDTO.setUserName(handoverRecordDTO.getCreateName());
        return userBriefDTO;
    }


    /**
     * 会员支付
     */
    private MemberPayReusltRespDTO tradePay(DealMemberPayReqDTO dealMemberPayReqDTO, DineinOrderDetailRespDTO calculate) {
        // 校验订单会员信息
        verifyOrderMemberInfo(calculate, dealMemberPayReqDTO);
        BillPayReqDTO billPayReqDTO = buildBillPayReqDTO(calculate, dealMemberPayReqDTO);
        BillPayReqDTO.Payment payment = new BillPayReqDTO.Payment();
        payment.setAmount(dealMemberPayReqDTO.getPayAmount());
        payment.setPaymentType(4);
        payment.setPaymentTypeName("会员卡支付");
        if (WxAppletMemberPayTypeEnum.INCOME_AMOUNT.getType().equals(dealMemberPayReqDTO.getPayType())) {
            payment.setPaymentType(10);
            payment.setPaymentTypeName(WxAppletMemberPayTypeEnum.INCOME_AMOUNT.getMessage());
        }
        billPayReqDTO.setPayments(Collections.singletonList(payment));
        billPayReqDTO.setMemberInfoCardGuid(calculate.getMemberCardGuid());
        billPayReqDTO.setMemberPassWord(aesDecryptPassword(dealMemberPayReqDTO.getMemberPassWord()));
        log.info("会员支付传参:{}", JacksonUtils.writeValueAsString(billPayReqDTO));
        try {
            EstimateItemRespDTO pay = tradeClientService.pay(billPayReqDTO);
            if (Boolean.TRUE.equals(pay.getResult())) {
                log.info("支付成功，order:{}", calculate.getGuid());
                // 如果是收益余额支付，扣除赚餐收益余额账户
                reduceZhuancanUserAmount(dealMemberPayReqDTO, calculate);
                // 后置处理
                successPayOrderHandler(calculate);
                return MemberPayReusltRespDTO.success();
            } else if (Boolean.TRUE.equals(pay.getEstimate())) {
                return MemberPayReusltRespDTO.soldOut();
            }
            return MemberPayReusltRespDTO.payFailed();
        } catch (Exception e) {
            log.error("支付失败, e:", e);
            return buildExceptionResult(e);
        }
    }

    /**
     * 会员支付密码解密
     */
    private String aesDecryptPassword(String password) {
        if (StringUtils.isNotBlank(password)) {
            try {
                password = AesEncryptUtils.aesDecrypt(password, memberPayKey);
            } catch (Exception e) {
                log.error("会员支付密码解密失败:{}", e.getMessage());
            }
        }
        return password;
    }


    @Override
    public MemberPayReusltRespDTO memberPay(DealMemberPayReqDTO dealMemberPayReqDTO) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        if (wxMemberSessionDTO == null) {
            log.error("会员余额支付失败，wxMemberSessionDTO为空");
            return MemberPayReusltRespDTO.changeFailed();
        }
        log.info("会员余额支付, wxMemberSessionDTO:{}", JacksonUtils.writeValueAsString(wxMemberSessionDTO));
        if (WxAppletMemberPayTypeEnum.STORED_AMOUNT.getType().equals(dealMemberPayReqDTO.getPayType())) {
            if (org.springframework.util.StringUtils.hasText(dealMemberPayReqDTO.getMemberInfoCardGuid())) {
                UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
                userMemberSession.setMemberInfoCardGuid(dealMemberPayReqDTO.getMemberInfoCardGuid());
                userMemberSessionUtils.addUserMemberSession(userMemberSession);
            }
        } else if (WxAppletMemberPayTypeEnum.INCOME_AMOUNT.getType().equals(dealMemberPayReqDTO.getPayType())) {
            // 如果是收益余额支付， 则需要查询赚餐是否余额充足
            BigDecimal islandUserAmount = queryZhuancanUserAmount(wxMemberSessionDTO);
            log.info("小程序会员余额支付, 赚餐收益余额:{}", islandUserAmount);
            if (dealMemberPayReqDTO.getPayAmount().compareTo(islandUserAmount) > 0) {
                return MemberPayReusltRespDTO.payBalanceFailed();
            }
        }
        try {
            String orderGuid = dealMemberPayReqDTO.getOrderGuid();
            String merchantOrderGuid = getMerchantOrderGuid(orderGuid);
            if (StringUtils.isEmpty(merchantOrderGuid)) {
                return MemberPayReusltRespDTO.changeFailed();
            }
            // 计算订单
            DineinOrderDetailRespDTO calculate = calculateOrderDetail(dealMemberPayReqDTO, merchantOrderGuid);
            if (Objects.isNull(calculate)) {
                log.error("会员支付失败, calculate is null");
                return MemberPayReusltRespDTO.payFailed();
            }
            // 订单校验
            MemberPayReusltRespDTO payResultRespDTO = verifyOrder(dealMemberPayReqDTO, calculate);
            if (Objects.nonNull(payResultRespDTO)) {
                return payResultRespDTO;
            }
            log.info("订单支付，当前订单详情:{}", JacksonUtils.writeValueAsString(calculate));
            return tradePay(dealMemberPayReqDTO, calculate);
        } catch (Exception e) {
            log.error("会员支付失败, e:", e);
            return MemberPayReusltRespDTO.payFailed();
        }
    }


    /**
     * 订单校验
     */
    private MemberPayReusltRespDTO verifyOrder(DealMemberPayReqDTO dealMemberPayReqDTO, DineinOrderDetailRespDTO calculate) {
        if (Objects.isNull(calculate)) {
            log.error("微信支付失败, calculate is null");
            return MemberPayReusltRespDTO.payFailed();
        }
        log.info("支付订单详情:{}", JacksonUtils.writeValueAsString(calculate));
        if (Boolean.TRUE.equals(calculate.getIsMultipleAggPay())) {
            log.error("微信支付失败, calculate is null");
            return new MemberPayReusltRespDTO(1, "该订单已支付部分金额，请前往收银台结账，谢谢！");
        }
        // 支付入参 校验金额
        if (verifyPayAmount(calculate, dealMemberPayReqDTO)) {
            return MemberPayReusltRespDTO.payAmountFailed();
        }
        return null;
    }


    /**
     * 计算订单
     */
    private DineinOrderDetailRespDTO calculateOrderDetail(DealMemberPayReqDTO dealMemberPayReqDTO, String merchantOrderGuid) {
        DineinOrderDetailRespDTO calculate;
        if (Objects.equals(TradeModeEnum.DINEIN.getCode(), dealMemberPayReqDTO.getTradeMode())) {
            UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
            log.info("会员支付session:{}", userMemberSession);
            BillCalculateReqDTO billCalculateReqDTO = buildBillCalculateReqDTO(merchantOrderGuid, userMemberSession);
            try {
                calculate = tradeClientService.calculate(billCalculateReqDTO);
            } catch (Exception e) {
                String message = e.getMessage();
                if (message.contains("优惠券正在使用中")
                        && !StringUtils.isEmpty(billCalculateReqDTO.getVolumeCode())
                        && billCalculateReqDTO.getVolumeCode().length() > 5) {
                    billCalculateReqDTO.setVerify(2);
                    calculate = tradeClientService.calculate(billCalculateReqDTO);
                    log.info("会员支付撤销券结果:{}", calculate);
                }
                if (message.contains("优惠劵已被使用")) {
                    userMemberSession.setVolumeCode("0");
                    userMemberSessionUtils.addUserMemberSession(userMemberSession);
                    return null;
                }
                log.error("会员支付接口算价异常.memberPayCalculate.billCalculateReqDTO:{}", billCalculateReqDTO, e);
                return null;
            }
        } else {
            // 快餐
            calculate = tradeClientService.getOrderDetail(merchantOrderGuid);
            calculate.setMemberCardGuid(dealMemberPayReqDTO.getMemberInfoCardGuid());
        }
        return calculate;
    }

    /**
     * 计算订单
     */
    private DineinOrderDetailRespDTO calculateZeroOrderDetail(String merchantOrderGuid) {
        DineinOrderDetailRespDTO calculate = tradeClientService.getOrderDetail(merchantOrderGuid);
        if (Objects.equals(TradeModeEnum.DINEIN.getCode(), calculate.getTradeMode())) {
            // 如果是正餐
            UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
            log.info("会员支付session:{}", userMemberSession);
            BillCalculateReqDTO billZeroCalculateReqDTO = buildBillCalculateReqDTO(merchantOrderGuid, userMemberSession);
            try {
                calculate = tradeClientService.calculate(billZeroCalculateReqDTO);
            } catch (Exception e) {
                String message = e.getMessage();
                if (message.contains("优惠券正在使用中")
                        && !StringUtils.isEmpty(billZeroCalculateReqDTO.getVolumeCode())
                        && billZeroCalculateReqDTO.getVolumeCode().length() > 5) {
                    billZeroCalculateReqDTO.setVerify(2);
                    calculate = tradeClientService.calculate(billZeroCalculateReqDTO);
                    log.info("会员支付撤销券结果:{}", calculate);
                }
                if (message.contains("优惠劵已被使用")) {
                    userMemberSession.setVolumeCode("0");
                    userMemberSessionUtils.addUserMemberSession(userMemberSession);
                    return null;
                }
                log.error("会员支付接口算价异常.memberPayCalculate.billCalculateReqDTO:{}", billZeroCalculateReqDTO, e);
                return null;
            }
        }
        return calculate;
    }

    /**
     * 构建支付请求入参
     */
    private BillPayReqDTO buildBillPayReqDTO(DineinOrderDetailRespDTO calculate, DealMemberPayReqDTO dealMemberPayReqDTO) {
        BillPayReqDTO billPayReqDTO = new BillPayReqDTO();
        billPayReqDTO.setVersion(calculate.getVersion());
        billPayReqDTO.setOrderGuid(calculate.getGuid());
        billPayReqDTO.setFastFood(calculate.getTradeMode() == 1);
        billPayReqDTO.setOrderFee(calculate.getOrderFee());
        billPayReqDTO.setAppendFee(calculate.getAppendFee());
        billPayReqDTO.setActuallyPayFee(dealMemberPayReqDTO.getPayAmount());
        billPayReqDTO.setDiscountFee(calculate.getDiscountFee());
        billPayReqDTO.setChangeFee(calculate.getChangeFee());
        billPayReqDTO.setDiscountFeeDetailDTOS(calculate.getDiscountFeeDetailDTOS());
        billPayReqDTO.setEnterpriseGuid(WeixinUserThreadLocal.getEnterpriseGuid());
        billPayReqDTO.setStoreGuid(WeixinUserThreadLocal.getStoreGuid());
        billPayReqDTO.setDeviceId(WeixinUserThreadLocal.getOpenId());
        billPayReqDTO.setDeviceType(BaseDeviceTypeEnum.WECHAT.getCode());
        if (Objects.nonNull(dealMemberPayReqDTO.getDeviceType())) {
            billPayReqDTO.setDeviceType(dealMemberPayReqDTO.getDeviceType());
        }
        // 结账员工信息
        wrapStaffInfo(billPayReqDTO);
        // 是否结账不清台
        billPayReqDTO.setCloseTableFlag(queryEnableHandleClose(billPayReqDTO)
                ? BooleanEnum.TRUE.getCode() : BooleanEnum.FALSE.getCode());
        return billPayReqDTO;
    }


    /**
     * 是否配置结账不清台
     */
    private boolean queryEnableHandleClose(BillPayReqDTO billPayReqDTO) {
        if (billPayReqDTO.isFastFood()) {
            return false;
        }
        if (!Objects.equals(BaseDeviceTypeEnum.WECHAT.getCode(), billPayReqDTO.getDeviceType())) {
            return false;
        }
        String storeGuid = WeixinUserThreadLocal.getStoreGuid();
        WxStoreReqDTO wxStoreReqDTO = new WxStoreReqDTO();
        wxStoreReqDTO.setStoreGuid(storeGuid);
        WxOrderConfigDTO wxOrderConfigDTO = wxClientService.getDetailConfig(wxStoreReqDTO);
        log.info("查询微信门店配置:{}", wxOrderConfigDTO);
        if (Objects.nonNull(wxOrderConfigDTO.getIsCheckoutUnCloseTable())
                && wxOrderConfigDTO.getIsCheckoutUnCloseTable() == 1) {
            return true;
        }
        // 如果扫码点餐没有开启结账不清台，则需要查询门店一体机配置
        StoreConfigQueryDTO storeConfigQueryDTO = new StoreConfigQueryDTO();
        storeConfigQueryDTO.setStoreGuid(storeGuid);
        DineFoodSettingRespDTO dineFoodSettingRespDTO = businessClientService.queryDineFoodSetting(storeConfigQueryDTO);
        log.info("查询一体机正餐配置:{}", dineFoodSettingRespDTO);
        return Objects.nonNull(dineFoodSettingRespDTO) && Objects.nonNull(dineFoodSettingRespDTO.getEnableHandleClose())
                && dineFoodSettingRespDTO.getEnableHandleClose() == 1;
    }

    private BillCalculateReqDTO buildBillCalculateReqDTO(String merchantOrderGuid, UserMemberSessionDTO userMemberSession) {
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        BillCalculateReqDTO reqDTO = new BillCalculateReqDTO();
        reqDTO.setOrderGuid(merchantOrderGuid);
        reqDTO.setEnterpriseGuid(wxMemberSessionDTO.getEnterpriseGuid());
        reqDTO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        reqDTO.setStoreName(wxMemberSessionDTO.getStoreName());
        reqDTO.setUserGuid(WeixinUserThreadLocal.getOpenId());
        reqDTO.setUserName(WeixinUserThreadLocal.getNickName());
        reqDTO.setDeviceId(WeixinUserThreadLocal.getOpenId());
        reqDTO.setMemberLogin(2);
        reqDTO.setDeviceType(12);
        if (!wxMemberSessionDTO.getWxUserInfoDTO().getIsLogin()) {
            log.info("trade计算接口入参:{}", reqDTO);
            return reqDTO;
        }
        String volumeCode = userMemberSession.getVolumeCode();
        String memberInfoCardGuid = userMemberSession.getMemberInfoCardGuid();
        Integer enableIntegral = userMemberSession.getMemberIntegral();
        reqDTO.setMemberInfoCardGuid(StringUtils.isEmpty(memberInfoCardGuid) || memberInfoCardGuid.length() < 5 ? null : memberInfoCardGuid);
        reqDTO.setMemberIntegral(enableIntegral != null && enableIntegral == 1 ? 1 : 2);
        reqDTO.setVolumeCode(StringUtils.isEmpty(volumeCode) || volumeCode.length() < 5 ? null : volumeCode);
        reqDTO.setVerify(StringUtils.isEmpty(volumeCode) || volumeCode.length() < 5 ? null : 1);
        reqDTO.setMemberLogin(!org.springframework.util.StringUtils.isEmpty(reqDTO.getMemberInfoCardGuid())
                || !StringUtils.isEmpty(reqDTO.getVolumeCode()) ? 1 : 2);
        reqDTO.setMemberPhone(reqDTO.getMemberLogin() == 1 ? WeixinUserThreadLocal.getOpenId() : null);
        log.info("trade计算接口入参:{}", reqDTO);
        return reqDTO;
    }

    private MemberPayReusltRespDTO buildExceptionResult(Exception e) {
        log.error("会员支付异常=", e);
        if (e.getMessage().contains("密码不正确")) {
            return MemberPayReusltRespDTO.errorPasswd();
        }
        if (e.getMessage().contains("余额不足")) {
            return MemberPayReusltRespDTO.payBalanceFailed();
        }
        if (e.getMessage().contains("会员卡已被冻结")) {
            return MemberPayReusltRespDTO.memberCardFrozen();
        }
        if (e.getMessage().contains("该会员卡已禁用")) {
            return MemberPayReusltRespDTO.memberCardDisable();
        }
        if (e.getCause().toString().contains("根据菜单guid")) {
            return MemberPayReusltRespDTO.itemSynchronize();
        }
        return MemberPayReusltRespDTO.payFailed();
    }

    /**
     * 支付更新订单
     *
     * @param orderGuid 微信订单id
     * @param calculate 商户订单
     */
    private void payBackOrder(String orderGuid, DineinOrderDetailRespDTO calculate) {
        PayBackOrderRecordDTO payBack = new PayBackOrderRecordDTO();
        payBack.setGuid(orderGuid);
        payBack.setActuallyPayFee(calculate.getActuallyPayFee());
        payBack.setUnMemberPrice(unMemberFee(calculate));
        payBack.setIsLogin(WeixinUserThreadLocal.getIsLogin());
        boolean memberCard = !StringUtils.isEmpty(calculate.getMemberGuid())
                && !StringUtils.isEmpty(calculate.getMemberCardGuid());
        payBack.setMemberInfoCardGuid(memberCard ? calculate.getMemberCardGuid() : null);
        payBack.setOrderState(2);
        payBack.setOrderStateName("已支付");
        payBack.setItemName(itemName(calculate));
        payBack.setStoreGuid(calculate.getStoreGuid());
        payBack.setOrderGuid(calculate.getGuid());
        if (calculate.getTradeMode() == 0) {
            redisUtils.delete(CacheName.USER_COUNT + ":" + WeixinUserThreadLocal.getDiningTableGuid());
        }
        payBack.setOrderMode(calculate.getTradeMode());
        wxClientService.payBackOrder(payBack);
    }


    private static final String WE_CHAT_PRE_PAY = "WE_CHAT_PRE_PAY:";

    private boolean preCheckWeChatPay(String orderGuid) {
        Boolean success = stringRedisTemplate.opsForValue().setIfAbsent(WE_CHAT_PRE_PAY + orderGuid, "1");
        if (Boolean.FALSE.equals(success)) {
            return false;
        }
        stringRedisTemplate.expire(WE_CHAT_PRE_PAY + orderGuid, 5, TimeUnit.SECONDS);
        return true;
    }

    private void afterCheckWeChatPay(AggPayRespDTO aggPayResp, String orderGuid) {
        if (aggPayResp == null || !Objects.equals(aggPayResp.getCode(), "10000") || !Objects.equals(aggPayResp.getResult(), "success")) {
            stringRedisTemplate.delete(WE_CHAT_PRE_PAY + orderGuid);
        }
    }

    @Override
    public WxPayRespDTO aggPay(WeChatH5PayReqDTO weChatPayReqDTO, HttpServletRequest request) {
        //校验是否在短时间内发起支付
        if (!preCheckWeChatPay(weChatPayReqDTO.getOrderGuid())) {
            return WxPayRespDTO.payFailed(0, "操作过于频繁！");
        }
        // 支付前置处理
        payBeforeHandler(weChatPayReqDTO, request);
        WxPayRespDTO wxPayRespDTO = wxClientService.aggPay(weChatPayReqDTO);
        //预下单后置处理
        afterCheckWeChatPay(wxPayRespDTO.getResult(), weChatPayReqDTO.getOrderGuid());
        return wxPayRespDTO;
    }

    private void payBeforeHandler(WeChatH5PayReqDTO weChatPayReqDTO, HttpServletRequest request) {
        // 小程序支付前置处理
        if (BaseDeviceTypeEnum.isApplet(weChatPayReqDTO.getDeviceType())) {
            appletPayBeforeHandler(weChatPayReqDTO, request);
        }
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        if (StringUtils.isEmpty(wxMemberSessionDTO.getWxtoken())) {
            return;
        }
        String cacheKey = String.format(RedisConstants.H5_WECHAT_THIRD_OPENID_APP_ID_KEY, wxMemberSessionDTO.getWxtoken());
        Object cacheObj = redisUtils.get(cacheKey);
        if (Objects.nonNull(cacheObj)) {
            String[] split = cacheObj.toString().split(",");
            weChatPayReqDTO.setThirdAppId(split[0]);
            weChatPayReqDTO.setThirdOpenId(split[1]);
        }
    }

    /**
     * 小程序支付前置处理
     */
    private void appletPayBeforeHandler(WeChatH5PayReqDTO weChatPayReqDTO, HttpServletRequest request) {
        weChatPayReqDTO.setClientIp(HttpUtils.getRequestIp(request));
        // 避免为空
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        if (Objects.isNull(wxMemberSessionDTO)) {
            wxMemberSessionDTO = new WxMemberSessionDTO();
        }
        if (Objects.isNull(wxMemberSessionDTO.getWxUserInfoDTO())) {
            wxMemberSessionDTO.setWxUserInfoDTO(new WxUserInfoDTO());
        }
        wxMemberSessionDTO.getWxUserInfoDTO().setIsLogin(true);
    }

    @Override
    public void appletCancelPay(WeChatCancelPayReqDTO weChatCancelPayReqDTO) {
        UserContext userContext = new UserContext();
        userContext.setEnterpriseGuid(weChatCancelPayReqDTO.getEnterpriseGuid());
        UserContextUtils.put(userContext);

        CancelOrderReqDTO cancelOrderReqDTO = new CancelOrderReqDTO();
        cancelOrderReqDTO.setFastFood(weChatCancelPayReqDTO.getFastFood());
        cancelOrderReqDTO.setReason(weChatCancelPayReqDTO.getReason());
        cancelOrderReqDTO.setDeviceType(weChatCancelPayReqDTO.getDeviceType());
        // 查询订单信息
        String merchantOrderGuid = getMerchantOrderGuid(weChatCancelPayReqDTO.getOrderGuid());
        OrderDTO orderDTO = tradeOrderService.findByOrderGuid(merchantOrderGuid);
        cancelOrderReqDTO.setStoreGuid(orderDTO.getStoreGuid());
        cancelOrderReqDTO.setStoreName(orderDTO.getStoreName());
        cancelOrderReqDTO.setOrderGuid(merchantOrderGuid);
        try {
            log.info("小程序取消订单入参:{}", JacksonUtils.writeValueAsString(cancelOrderReqDTO));
            Boolean result = dineInOrderClientService.cancelOrder(cancelOrderReqDTO);
            log.info("小程序取消订单返回:{}", result);
        } catch (Exception e) {
            log.info("小程序取消订单返回异常, e:{}", e.getMessage());
        }
    }

    /**
     * @param orderRecordGuid 订单id
     * @return 订单详情是否发生变化
     */
    private Boolean orderChange(String orderRecordGuid) {
        /*String merchantOrderGuid = wxClientService.getMerchantOrderGuid(orderRecordGuid);
        if (StringUtils.isEmpty(merchantOrderGuid)) {
            return true;
        }
        OrderWechatDTO order = tradeClientService.getOrder(merchantOrderGuid);*/
        WxOrderDetailReqDTO orderDetailDTO = new WxOrderDetailReqDTO();
        orderDetailDTO.setGuid(orderRecordGuid);
        orderDetailDTO.setMemberLogin(-1);
        if (WeixinUserThreadLocal.getIsLogin()) {
            UserMemberSessionDTO userMemberSession = userMemberSessionUtils.getUserMemberSession(WeixinUserThreadLocal.getOpenId());
            log.info("userMemberSession:{}", userMemberSession);
            if (userMemberSession != null) {
                String volumeCode = userMemberSession.getVolumeCode();
                if (volumeCode != null && volumeCode.length() > 5) {
                    orderDetailDTO.setVolumeCode(volumeCode);
                }
                orderDetailDTO.setMemberInfoCardGuid(userMemberSession.getMemberInfoCardGuid());
                orderDetailDTO.setMemberIntegral(Objects.nonNull(userMemberSession.getMemberIntegral())
                        ? userMemberSession.getMemberIntegral() : -1);
            }
        }
        Result<OrderDetailDTO> orderDetailResult = wxOrderRecordClientService.detailCalculate(orderDetailDTO);
        log.info("订单校验,orderDetailDTO={}:res={}", orderDetailDTO, orderDetailResult);
        OrderDetailDTO orderDetail = orderDetailResult.getTData();
        return orderDetail == null || orderDetail.getCode() != 0;
    }

    /**
     * @param orderRecordGuid 微信订单id
     * @return 商户订单id
     */
    private String getMerchantOrderGuid(String orderRecordGuid) {
        return Optional.ofNullable(wxClientService.getMerchantOrderGuid(orderRecordGuid)).orElse(null);
    }

    /**
     * @param orderDetail 计单
     * @return 非会员小计
     */
    private BigDecimal unMemberFee(DineinOrderDetailRespDTO orderDetail) {
        if (orderDetail != null) {
            List<DiscountFeeDetailDTO> discountDTOS = orderDetail.getDiscountFeeDetailDTOS();
            if (!ObjectUtils.isEmpty(discountDTOS)) {
                BigDecimal unMemberFee = discountDTOS.stream()
                        .filter(x -> Arrays.asList(1, 7, 8, 9, 11).contains(x.getDiscountType()))
                        .map(x -> Optional.ofNullable(x.getDiscountFee()).orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                return unMemberFee.compareTo(BigDecimal.ZERO) > 0 ? unMemberFee : null;
            }
        }
        return null;
    }

    private String itemName(DineinOrderDetailRespDTO orderDetail) {
        if (orderDetail != null) {
            List<DineInItemDTO> dineInItemDTOS = orderDetail.getDineInItemDTOS();
            if (!ObjectUtils.isEmpty(dineInItemDTOS)) {
                return dineInItemDTOS.stream().map(DineInItemDTO::getItemName).collect(Collectors.joining(","));
            }
        }
        return null;
    }

    /**
     * 订单支付成功 后置处理
     */
    private void successPayOrderHandler(DineinOrderDetailRespDTO calculate) {
        payBackOrder(calculate.getGuid(), calculate);
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        // 保存会员最后使用会员卡记录
        redisUtils.set(String.format(RedisConstants.MEMBER_LAST_CARD_GUID, wxMemberSessionDTO.getOperSubjectGuid(),
                WeixinUserThreadLocal.getOpenId()), calculate.getMemberCardGuid());
        if (calculate.getTradeMode() == 0) {
            Set<String> openIDS = redisUtils.hKeyList(CacheName.DINE_USER + ":hash:" + calculate.getDiningTableGuid());
            userMemberSessionUtils.delAllUserSession(openIDS);
            userMemberSessionUtils.delTableCardList(calculate.getStoreGuid(), openIDS);
            websocketUtils.clear(calculate.getDiningTableGuid());
            redisUtils.delete("autoAccept:" + calculate.getDiningTableGuid());
        } else {
            // 续时15分钟，防止进入订单详情支付失败
            userMemberSessionUtils.expireUserSession(Collections.singleton(WeixinUserThreadLocal.getOpenId()));
            userMemberSessionUtils.delCardList(calculate.getStoreGuid(), WeixinUserThreadLocal.getOpenId());
            // 删除点餐人数
            wxStoreTradeOrderService.removeFastGuestCount(calculate.getDiningTableGuid(), WeixinUserThreadLocal.getOpenId());
            if (StringUtils.isNotEmpty(calculate.getUserWxPublicOpenId())
                    && !calculate.getUserWxPublicOpenId().equals(WeixinUserThreadLocal.getOpenId())) {
                wxStoreTradeOrderService.removeFastGuestCount(calculate.getDiningTableGuid(), calculate.getUserWxPublicOpenId());
            }
            // 订单附加费缓存 -> 桌台附加费缓存
            wxStoreTradeOrderService.transformSurchargeCache(calculate.getGuid());
            // 设置会员标签
            setMemberLabel(calculate);
        }
        try {
            WxMemberTradeNotifyReqDTO wxMemberTradeNotifyReqDTO = new WxMemberTradeNotifyReqDTO();
            wxMemberTradeNotifyReqDTO.setEnterpriseGuid(WeixinUserThreadLocal.getEnterpriseGuid());
            wxMemberTradeNotifyReqDTO.setActuallyPayFee(calculate.getActuallyPayFee());
            wxMemberTradeNotifyReqDTO.setBrandGuid(WeixinUserThreadLocal.getBrandGuid());
            wxMemberTradeNotifyReqDTO.setMemberInfoCardGuid(calculate.getMemberCardGuid());
            wxMemberTradeNotifyReqDTO.setOpenId(WeixinUserThreadLocal.getOpenId());
            wxMemberTradeNotifyReqDTO.setOrderGuid(calculate.getGuid());
            wxMemberTradeNotifyReqDTO.setStoreGuid(calculate.getStoreGuid());
            wxMemberTradeNotifyReqDTO.setStoreName(calculate.getStoreName());
            log.info("订单完成微信模板消息：{}", wxMemberTradeNotifyReqDTO);
            wxStorePayClientService.sendWeixinNotifyMessage(wxMemberTradeNotifyReqDTO);
        } catch (Exception e) {
            log.error("微信订单完成模板消息发送失败", e);
        }
    }

    /**
     * 设置会员标签
     */
    private void setMemberLabel(DineinOrderDetailRespDTO calculate) {
        if (CollectionUtils.isNotEmpty(calculate.getDiscountFeeDetailDTOS())) {
            DiscountFeeDetailDTO specialsDiscountDTO = calculate.getDiscountFeeDetailDTOS().stream()
                    .filter(d -> d.getDiscountType().equals(DiscountTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode()))
                    .findFirst()
                    .orElse(null);
            if (!ObjectUtils.isEmpty(specialsDiscountDTO) && org.springframework.util.StringUtils.hasText(specialsDiscountDTO.getRule())) {
                String rule = specialsDiscountDTO.getRule();
                DiscountRuleDTO discountRuleDTO = JacksonUtils.toObject(DiscountRuleDTO.class, rule);
                if (!ObjectUtils.isEmpty(discountRuleDTO) && CollectionUtils.isNotEmpty(discountRuleDTO.getLabelGuidList())) {
                    RequestManualLabel addManualLabel = new RequestManualLabel();
                    addManualLabel.setMemberInfoGuidArray(calculate.getMemberGuid());
                    addManualLabel.setLabelSettingGuid(String.join(",", discountRuleDTO.getLabelGuidList()));
                    merchantClientService.batchMemberManualLabel(addManualLabel);
                    log.info("[会员标签设置完毕]addManualLabel={}", JacksonUtils.writeValueAsString(addManualLabel));
                }
            }
        }
    }

    /**
     * 收益余额支付
     */
    private void reduceZhuancanUserAmount(DealMemberPayReqDTO dealMemberPayReqDTO, DineinOrderDetailRespDTO calculate) {
        if (!WxAppletMemberPayTypeEnum.INCOME_AMOUNT.getType().equals(dealMemberPayReqDTO.getPayType())) {
            return;
        }
        try {
            WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
            ShopOrderIslandUserAmountDTO shopOrderIslandUserAmountDTO = new ShopOrderIslandUserAmountDTO();
            shopOrderIslandUserAmountDTO.setEnterpriseGuid(wxMemberSessionDTO.getEnterpriseGuid());
            shopOrderIslandUserAmountDTO.setOperSubjectGuid(wxMemberSessionDTO.getOperSubjectGuid());
            shopOrderIslandUserAmountDTO.setMemberInfoCardGuid(calculate.getMemberCardGuid());
            shopOrderIslandUserAmountDTO.setMemberInfoGuid(wxMemberSessionDTO.getMemberInfoGuid());
            shopOrderIslandUserAmountDTO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
            shopOrderIslandUserAmountDTO.setAmount(dealMemberPayReqDTO.getPayAmount());
            shopOrderIslandUserAmountDTO.setOrderRecordGuid(dealMemberPayReqDTO.getOrderGuid());
            // 查询商品明细
            shopOrderIslandUserAmountDTO.setSkuList(Lists.newArrayList());
            List<DineInItemDTO> itemList = wxClientService.getItemListByOrderGuid(calculate.getGuid());
            if (CollectionUtils.isNotEmpty(itemList)) {
                for (DineInItemDTO itemDTO : itemList) {
                    ShopOrderIslandUserAmountDTO.InnerSkuDTO skuDTO = new ShopOrderIslandUserAmountDTO.InnerSkuDTO();
                    skuDTO.setNum(itemDTO.getCurrentCount().stripTrailingZeros().toPlainString());
                    skuDTO.setName(itemDTO.getItemName());
                    if (StringUtils.isNotEmpty(itemDTO.getSkuName())) {
                        skuDTO.setName(skuDTO.getName() + "(" + itemDTO.getSkuName() + ")");
                    }
                    shopOrderIslandUserAmountDTO.getSkuList().add(skuDTO);
                }
            }
            log.info("用户收益余额支付请求参数:{}", JacksonUtils.writeValueAsString(shopOrderIslandUserAmountDTO));
            String url = String.format(ISLAND_USER_PAY_URL, zhuancanRequestHost);
            String resultStr = HttpUtils.post(url, JacksonUtils.writeValueAsString(shopOrderIslandUserAmountDTO));
            log.info("用户收益余额支付返回参数:{}", resultStr);
        } catch (Exception e) {
            log.error("用户收益余额支付失败,e:{}", e.getMessage());
        }
    }


    /**
     * 查询赚餐用户收益余额账户金额
     */
    private BigDecimal queryZhuancanUserAmount(WxMemberSessionDTO wxMemberSessionDTO) {
        String phoneNum = wxMemberSessionDTO.getPhoneNum();
        if (StringUtils.isEmpty(phoneNum)) {
            log.error("当前会话无法获取会员PhoneNum");
            return BigDecimal.ZERO;
        }
        try {
            ShopOrderIslandUserAmountDTO shopOrderIslandUserAmountDTO = new ShopOrderIslandUserAmountDTO();
            shopOrderIslandUserAmountDTO.setEnterpriseGuid(wxMemberSessionDTO.getEnterpriseGuid());
            shopOrderIslandUserAmountDTO.setOperSubjectGuid(wxMemberSessionDTO.getOperSubjectGuid());
            shopOrderIslandUserAmountDTO.setPhone(phoneNum);
            String url = String.format(QUERY_ISLAND_USER_URL, zhuancanRequestHost);
            String resultStr = HttpUtils.post(url, JacksonUtils.writeValueAsString(shopOrderIslandUserAmountDTO));
            log.info("查询赚餐用户收益余额账户金额返回参数:{}", resultStr);
            IslandUserAmountDTO islandUserAmountDTO = JacksonUtils.toObject(IslandUserAmountDTO.class, resultStr);
            return islandUserAmountDTO.getAmount();
        } catch (Exception e) {
            log.error("查询赚餐用户收益余额账户金额失败,e:{}", e.getMessage());
        }
        return BigDecimal.ZERO;
    }


    /**
     * 支付入参 校验金额
     */
    private boolean verifyPayAmount(DineinOrderDetailRespDTO calculate, DealMemberPayReqDTO dealMemberPayReqDTO) {
        // 传入的支付金额
        BigDecimal payAmount = dealMemberPayReqDTO.getPayAmount();
        BigDecimal orderFee = calculate.getOrderFee();
        BigDecimal discountFee = Optional.ofNullable(calculate.getDiscountFee()).orElse(BigDecimal.ZERO);
        return payAmount.compareTo(orderFee.subtract(discountFee)) != 0;
    }

    /**
     * 校验订单会员信息
     */
    private void verifyOrderMemberInfo(DineinOrderDetailRespDTO calculate, DealMemberPayReqDTO dealMemberPayReqDTO) {
        // 当前订单上的memberGuid
        String memberGuid = calculate.getMemberGuid();
        if (StringUtils.isNotEmpty(memberGuid) && !"0".equals(memberGuid)) {
            return;
        }
        WxMemberSessionDTO wxMemberSessionDTO = WeixinUserThreadLocal.get();
        String memberInfoGuid = wxMemberSessionDTO.getMemberInfoGuid();
        if (StringUtils.isEmpty(memberInfoGuid) || "0".equals(memberInfoGuid)) {
            return;
        }
        if (!memberGuid.equals(memberInfoGuid)) {
            UpdateOrderMemberInfoReqDTO reqDTO = new UpdateOrderMemberInfoReqDTO();
            reqDTO.setOrderGuid(calculate.getGuid());
            reqDTO.setMemberGuid(memberInfoGuid);
            reqDTO.setMemberCardGuid(dealMemberPayReqDTO.getMemberInfoCardGuid());
            reqDTO.setMemberPhone(wxMemberSessionDTO.getPhoneNum());
            reqDTO.setMemberName(Optional.ofNullable(wxMemberSessionDTO.getWxUserInfoDTO()).orElse(new WxUserInfoDTO()).getNickname());
            log.info("更新订单会员信息, reqDTO:{}", JacksonUtils.writeValueAsString(reqDTO));
            tradeClientService.updateOrderMemberInfo(reqDTO);
        }
    }
}
