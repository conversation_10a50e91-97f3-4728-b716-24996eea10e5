package com.holderzone.holder.saas.aggregation.merchant.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/7/23 10:34
 * @description
 */
@Data
@ApiModel("云平台运营主体相关信息")
public class OrganizationNewDTO {
    @ApiModelProperty("关联guid")
    private String linkGuid;
    @ApiModelProperty("组织guid")
    private String organizationGuid;
    @ApiModelProperty("组织guid")
    private String organizationCode;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("多会员体系guid")
    private String multiMemberGuid;
    @ApiModelProperty("多会员体系名称")
    private String multiMemberName;
    @ApiModelProperty("地址")
    private String address;
    @ApiModelProperty("省份")
    private String province;
    @ApiModelProperty("市")
    private String city;
    @ApiModelProperty("地区")
    private String district;
    @ApiModelProperty("多会员体系状态")
    private Boolean enabled;
}
