package com.holderzone.holder.saas.aggregation.merchant.controller.organization;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.builder.HolderOrganizationResultDTOBuilder;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.DisplayRuleTypeEnum;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.cloud.CloudEnterpriseFeignClient;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.item.ItemClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.kds.KdsClientService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.organization.OrganizationService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutProducerService;
import com.holderzone.resource.common.dto.enterprise.MultiMemberQueryDTO;
import com.holderzone.resource.common.dto.holder.organization.HolderOrganizationResultDTO;
import com.holderzone.resource.common.enums.HolderOrganizationTypeEnum;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.item.resp.BrandAndPlanRespDTO;
import com.holderzone.saas.store.dto.kds.req.DisplayRuleQueryDTO;
import com.holderzone.saas.store.dto.kds.resp.DisplayStoreRespDTO;
import com.holderzone.saas.store.dto.organization.*;
import com.holderzone.saas.store.dto.store.store.BindupAccountsSaveDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className Organization
 * @date 19-1-8 下午1:43
 * @description 组织服务相关
 * @program holder-saas-aggregation-merchant
 */
@RestController
@Api(tags = "品牌、组织、门店相关接口", description = "商户后台二期品牌、组织、门店相关服务接口")
@Slf4j
public class OrganizationController {

    private final OrganizationService organizationService;

    private final ItemClientService itemClientService;

    private final KdsClientService kdsClientService;

    private final CloudEnterpriseFeignClient cloudEnterpriseFeignClient;

    private final TakeoutProducerService takeoutProducerService;

    @Autowired
    public OrganizationController(OrganizationService organizationService, ItemClientService itemClientService, KdsClientService kdsClientService, CloudEnterpriseFeignClient cloudEnterpriseFeignClient, TakeoutProducerService takeoutProducerService) {
        this.organizationService = organizationService;
        this.itemClientService = itemClientService;
        this.kdsClientService = kdsClientService;
        this.cloudEnterpriseFeignClient = cloudEnterpriseFeignClient;
        this.takeoutProducerService = takeoutProducerService;
    }

    @ApiOperation(value = "创建品牌", notes = "创建品牌")
    @PostMapping(value = "/brand/create")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "创建品牌")
    public Result<BrandDTO> createBrand(@RequestBody @Validated BrandDTO brandDTO) {
        return Result.buildSuccessResult(organizationService.createBrand(brandDTO));
    }

    @ApiOperation(value = "更新品牌", notes = "更新品牌")
    @PostMapping(value = "/brand/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "更新品牌")
    public Result<Boolean> updateBrand(@RequestBody @Validated(BrandDTO.Update.class) BrandDTO brandDTO) {
        if (organizationService.updateBrand(brandDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.UPDATE_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.UPDATE_FAILED));
    }

    @ApiOperation(value = "删除品牌", notes = "删除品牌")
    @PostMapping(value = "/brand/delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "删除品牌")
    public Result<Boolean> deleteBrand(@RequestBody SingleDataDTO singleDataDTO) {
        if (organizationService.deleteBrand(singleDataDTO.getData())) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.DELETION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.DELETION_FAILED));
    }

    /**
     * 根据品牌guid查询品牌信息
     *
     * @param brandGuid 品牌guid
     * @return 品牌信息
     */
    @ApiOperation("根据品牌guid查询品牌信息")
    @PostMapping("/query_brand_by_guid")
    public Result<BrandDTO> queryBrandByGuid(@RequestParam("brandGuid") String brandGuid) {
        return Result.buildSuccessResult(organizationService.queryBrandByGuid(brandGuid));
    }

    @ApiOperation(value = "查询企业下所有品牌", notes = "查询企业下所有品牌")
    @PostMapping(value = "/brand/query_list")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "查询企业下所有品牌")
    public Result<List<BrandDTO>> queryBrandList(@RequestBody(required = false) QueryBrandDTO queryBrandDTO) {
        if (log.isInfoEnabled()) {
            log.info("查询企业品牌请求入参：{}", JacksonUtils.writeValueAsString(queryBrandDTO));
        }
        if (queryBrandDTO == null) {
            queryBrandDTO = new QueryBrandDTO();
        }
        List<BrandDTO> data = organizationService.queryBrandList(queryBrandDTO);
        if (CollUtil.isEmpty(data)) {
            return Result.buildSuccessResult(Lists.newArrayList());
        }
        //设置京东授权信息
        List<String> authBrandList = takeoutProducerService.authBrand(data.stream().map(BrandDTO::getGuid).collect(Collectors.toList()));
        if (CollUtil.isNotEmpty(authBrandList)) {
            data.forEach(brandDTO -> brandDTO.setJdAuth(authBrandList.contains(brandDTO.getGuid())));
        }
        return Result.buildSuccessResult(data);
    }

    @ApiOperation(value = "查询是否门店下存在帐号", notes = "查询是否门店下存在帐号")
    @PostMapping(value = "/brand/query_exist_store_account")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "查询是否门店下存在帐号")
    public Result<Boolean> queryExistStoreAccount(@RequestBody SingleDataDTO singleDataDTO) {
        return Result.buildSuccessResult(organizationService.queryExistStoreAccount(singleDataDTO.getData()));
    }

    @ApiOperation(value = "创建门店", notes = "创建门店")
    @PostMapping(value = "/store/create")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "创建门店")
    public Result<Boolean> createStore(@RequestBody @Validated StoreDTO storeDTO) {
        if (organizationService.createStore(storeDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.CREATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.CREATION_FAILED));
    }

    @ApiOperation(value = "更新门店", notes = "更新门店")
    @PostMapping(value = "/store/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "更新门店")
    public Result<Boolean> updateStore(@RequestBody @Validated StoreDTO storeDTO) {
        if (organizationService.updateStore(storeDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.UPDATE_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.UPDATE_FAILED));
    }

    @ApiOperation(value = "启用门店", notes = "启用门店")
    @PostMapping(value = "/store/enable")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "启用门店")
    public Result enableStore(@RequestBody SingleDataDTO singleDataDTO) {
        if (organizationService.enableOrDisableStore(singleDataDTO.getData())) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.OPERATION_FAILED));
    }

    @ApiOperation(value = "禁用门店", notes = "禁用门店")
    @PostMapping(value = "/store/disable")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "禁用门店")
    public Result disableStore(@RequestBody SingleDataDTO singleDataDTO) {
        if (organizationService.enableOrDisableStore(singleDataDTO.getData())) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.OPERATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.OPERATION_FAILED));
    }

    @ApiOperation(value = "删除门店", notes = "删除门店")
    @PostMapping(value = "/store/delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "删除门店")
    public Result<Boolean> deleteStore(@RequestBody SingleDataDTO singleDataDTO) {
        if (organizationService.deleteStore(singleDataDTO.getData())) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.DELETION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.DELETION_FAILED));
    }

    @ApiOperation(value = "根据条件查询门店（分页）", notes = "根据条件分页查询门店（分页）")
    @PostMapping(value = "/store/query_by_condition")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "根据条件查询门店（分页）")
    public Result<Page<StoreDTO>> queryStoreByCondition(@RequestBody QueryStoreDTO queryStoreDTO) {
        return Result.buildSuccessResult(organizationService.queryStoreByCondition(queryStoreDTO));
    }

    @ApiOperation(value = "根据条件查询门店（不分页）", notes = "根据条件查询门店（不分页）")
    @PostMapping(value = "/store/query_by_condition_no_page")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "根据条件查询门店（不分页）")
    public Result<List<StoreDTO>> queryStoreByConditionNoPage(@RequestBody StoreParserDTO storeParserDTO) {
        return Result.buildSuccessResult(organizationService.queryStoreByConditionNoPage(storeParserDTO));
    }

    @ApiOperation(value = "获取企业下的所有门店", notes = "获取企业下的所有门店")
    @PostMapping(value = "/store/query_all_store")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "获取企业下的所有门店")
    public Result<List<StoreDTO>> queryAllStore() {
        return Result.buildSuccessResult(organizationService.queryAllStore().stream()
                .filter(storeDTO -> !storeDTO.getIsDeleted())
                .collect(Collectors.toList()));
    }

    @ApiOperation(value = "创建组织", notes = "创建组织")
    @PostMapping(value = "/organization/create")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "创建组织")
    public Result<OrganizationDTO> createOrganization(@RequestBody @Validated OrganizationDTO organizationDTO) {
        OrganizationDTO dto = organizationService.createOrganization(organizationDTO);
        if (dto != null) {
            return Result.buildSuccessResult(dto);
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.CREATION_FAILED));
    }

    @ApiOperation(value = "编辑组织", notes = "编辑组织")
    @PostMapping(value = "/organization/update")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "编辑组织")
    public Result<Boolean> updateOrganization(@RequestBody @Validated(OrganizationDTO.Update.class) OrganizationDTO organizationDTO) {
        if (organizationService.updateOrganization(organizationDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.RESULT_MODIFICATION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.RESULT_MODIFICATION_FAILED));
    }

    @ApiOperation(value = "删除组织", notes = "删除组织")
    @PostMapping(value = "/organization/delete")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "删除组织")
    public Result<Boolean> deleteOrganization(@RequestBody SingleDataDTO singleDataDTO) {
        if (organizationService.deleteOrganization(singleDataDTO.getData())) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.DELETION_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.DELETION_FAILED));
    }

    @ApiOperation(value = "根据guid查询是否存在下级组织或门店", notes = "根据guid查询是否存在下级组织或门店")
    @PostMapping(value = "/organization/query_exist_organization_or_store")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "根据guid查询是否存在下级组织或门店")
    public Result<Boolean> queryExistOrganizationOrStore(@RequestBody SingleDataDTO singleDataDTO) {
        return Result.buildSuccessResult(organizationService.queryExistOrganizationOrStore(singleDataDTO.getData()));
    }

    @ApiOperation(value = "根据组织guid查询该组织可选的上级组织", notes = "根据组织guid查询该组织可选的上级组织")
    @PostMapping(value = "/organization/get_optional_organization")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "根据组织guid查询该组织可选的上级组织")
    public Result<List<OrganizationDTO>> getOptionalOrganization(@RequestBody SingleDataDTO singleDataDTO) {
        return Result.buildSuccessResult(organizationService.getOptionalOrganization(singleDataDTO.getData()));
    }

    @ApiOperation(value = "获取企业及企业下的所有组织", notes = "获取企业及企业下的所有组织")
    @PostMapping(value = "/organization/query_enterprise_and_organization")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "获取企业及企业下的所有组织")
    public Result<List<OrganizationDTO>> queryEnterpriseAndOrganization() {
        return Result.buildSuccessResult(organizationService.queryEnterpriseAndOrganization());
    }

    @ApiOperation(value = "获取企业下的所有组织", notes = "获取企业下的所有组织")
    @PostMapping(value = "/organization/query_all_organization")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "获取企业下的所有组织")
    public Result<List<OrgGeneralDTO>> queryAllOrganization() {
        return Result.buildSuccessResult(organizationService.queryAllOrganization());
    }

    @ApiOperation(value = "查询组织下是否存在帐号")
    @PostMapping(value = "/organization/query_exist_account")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "查询组织下是否存在帐号")
    public Result<Boolean> queryExistAccount(@RequestBody SingleDataDTO singleDataDTO) {
        return Result.buildSuccessResult(organizationService.queryExistAccount(singleDataDTO.getData()));
    }

    @ApiOperation(value = "查询门店级企业的门店信息和品牌信息")
    @PostMapping(value = "/organization/query_store_enterprise_info")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "查询门店级企业的门店信息和品牌信息")
    public Result<StoreEnterpriseDTO> queryStoreEnterpriseInfo() {
        List<StoreDTO> storeDTOS = organizationService.queryAllStore();
        List<BrandDTO> brandDTOS = organizationService.queryBrandList(new QueryBrandDTO());
        if (CollectionUtils.isEmpty(storeDTOS)) {
            throw new BusinessException("该企业未激活门店，请联系售后人员。");
        }
        return Result.buildSuccessResult(new StoreEnterpriseDTO(storeDTOS.get(0), brandDTOS.get(0)));
    }

    /**
     * 根据品牌列表查询品牌列表下的所有门店
     *
     * @param storeParserPageDTO 品牌guid数组
     * @return 门店列表
     */
    @ApiOperation("根据品牌列表查询品牌列表下的所有门店")
    @PostMapping("/query_store_by_brand_list")
    public Result<List<StoreDTO>> queryStoreByBrandList(@RequestBody StoreParserPageDTO storeParserPageDTO) {
        log.info("根据品牌列表查询品牌列表下的所有门店入参 品牌guid:{}", storeParserPageDTO.getBrandGuidList());
        List<StoreDTO> storeDTOList = organizationService.queryStoreByBrandList(storeParserPageDTO.getBrandGuidList());
        if (CollectionUtils.isEmpty(storeDTOList)) {
            log.warn("品牌下门店为空");
            return Result.buildEmptySuccess();
        }
        DisplayRuleQueryDTO queryDTO = new DisplayRuleQueryDTO();
        queryDTO.setRuleType(storeParserPageDTO.getRuleType());
        queryDTO.setBrandGuid(storeParserPageDTO.getBrandGuidList().get(0));

        if (!ObjectUtils.isEmpty(storeParserPageDTO.getRuleGuid())) {
            queryDTO.setRuleGuid(storeParserPageDTO.getRuleGuid());
        }
        if (DisplayRuleTypeEnum.ITEM_SUMMARY.getCode().equals(storeParserPageDTO.getRuleType())) {
            Boolean hasAllStore = kdsClientService.queryHasAllStore(queryDTO);
            if (hasAllStore) {
                log.warn("有门店使用全部门店");
                return Result.buildSuccessResult("有门店使用全部门店", new ArrayList<>());
            }
            List<DisplayStoreRespDTO> storeRespDTOList = kdsClientService.listStore(queryDTO);
            log.info("查询kds门店列表结果, displayItemDTOList={}", JacksonUtils.writeValueAsString(storeRespDTOList));
            List<String> storeGuidList = storeRespDTOList.stream()
                    .map(DisplayStoreRespDTO::getStoreGuid)
                    .collect(Collectors.toList());
            storeDTOList.removeIf(storeDTO -> storeGuidList.contains(storeDTO.getGuid()));
        }

        return Result.buildSuccessResult(storeDTOList);
    }

    /**
     * 更改销售模式
     *
     * @param brandDTO DTO
     * @return true-成功，false-失败
     */
    @ApiOperation("更改销售模式")
    @PostMapping("/brand/update_sales_model")
    public Result<Boolean> updateSalesModel(@RequestBody BrandDTO brandDTO) {
        log.info("更改销售模式入参 brandDTO:{}", JacksonUtils.writeValueAsString(brandDTO));
        Boolean updateSalesModel = organizationService.updateSalesModel(brandDTO);
        if (updateSalesModel) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.SALES_MODE_CHANGE_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SALES_MODE_CHANGE_FAILED));
    }

    /**
     * 根据门店guid查询门店关联的品牌信息以及方案
     *
     * @param storeGuid 门店guid
     * @return 品牌信息以及方案
     */
    @ApiOperation(value = "根据门店guid查询门店关联的品牌信息以及方案")
    @GetMapping("/query_brand_by_store_guid")
    public Result<BrandAndPlanRespDTO> queryBrandAndPlanByStoreGuid(@RequestParam("storeGuid") String storeGuid) {
        BrandAndPlanRespDTO respDTO = new BrandAndPlanRespDTO();
        respDTO.setBrandDTO(organizationService.queryBrandByStoreGuid(storeGuid));
        respDTO.setPlanDTOList(itemClientService.queryBelongPlansByStoreGuid(storeGuid));
        return Result.buildSuccessResult(respDTO);
    }

    /**
     * 查询运营主体下的门店列表
     *
     * @param queryDTO 关联企业guid，运营主体guid
     * @return 门店列表
     */
    @ApiOperation(value = "查询运营主体下的门店列表", notes = "必传 关联企业guid，运营主体guid")
    @PostMapping("/organization/store_list_multiMemberGuid")
    public Result<List<com.holderzone.resource.common.dto.enterprise.OrganizationDTO>> getStoreByMultiMemberGuid(@RequestBody MultiMemberQueryDTO queryDTO) {
        log.info("查询运营主体下的门店列表 入参:{}", JSON.toJSONString(queryDTO));
        return Result.buildSuccessResult(cloudEnterpriseFeignClient.getStoreByMultiMemberGuid(queryDTO));
    }

    /**
     * 根据门店guid查询门店关联的品牌信息
     *
     * @param storeGuid 门店guid
     * @return 品牌信息
     */
    @ApiOperation(value = "根据门店guid查询门店关联的品牌信息", notes = "若门店未关联到品牌则返回为null，后期一个门店可关联多个品牌")
    @RequestMapping("/store/query_brand_by_storeguid")
    public Result<BrandDTO> queryBrandByStoreGuid(@RequestParam("storeGuid") String storeGuid) {
        log.info("根据门店guid查询门店关联的品牌信息 入参：{}", storeGuid);
        return Result.buildSuccessResult(organizationService.queryBrandByStoreGuid(storeGuid));
    }

    /**
     * 查询该企业绑定的holder组织机构
     */
    @ApiOperation(value = "查询该企业绑定的holder组织机构", notes = "查询该企业绑定的holder组织机构")
    @GetMapping("/organization/holder/list")
    public Result<HolderOrganizationResultDTO> listOrganizationByHolder(String keyword) {
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        HolderOrganizationResultDTO organizationResult = cloudEnterpriseFeignClient.listTreeOrganizationByHolder(enterpriseGuid);
        if (Objects.nonNull(organizationResult) && Objects.nonNull(organizationResult.getId())) {
            if (StringUtils.isNotBlank(keyword)) {
                if (CollectionUtils.isNotEmpty(organizationResult.getChilds())) {
                    List<HolderOrganizationResultDTO> searchOrganizationList = HolderOrganizationResultDTOBuilder
                            .searchOrganization(organizationResult.getChilds(), keyword);
                    organizationResult.setChilds(searchOrganizationList);
                }
            }
            // 查询该企业组织机构及门店
            List<OrganizationDTO> organizationList = organizationService.queryOrganizationList();
            if (CollectionUtils.isNotEmpty(organizationList)) {
                List<Long> organizationGuids = organizationList.stream().map(OrganizationDTO::getGuid)
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                // 封装返回参数
                HolderOrganizationResultDTOBuilder.relation(organizationResult, organizationGuids);
            }
        }
        return Result.buildSuccessResult(organizationResult);
    }

    @ApiOperation("批量新增或修改组织")
    @PostMapping("/organization/batch/create")
    public Result<Boolean> createBatchOrganization(@RequestBody @Valid OrganizationBatchCreateDTO createDTO) {
        log.info("聚合层-批量新增组织入参:{}", JacksonUtils.writeValueAsString(createDTO));
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        if (createDTO.getSyncArea() == 0) {
            // 导入全部组织
            List<HolderOrganizationResultDTO> holderOrganizationResultDTOList = cloudEnterpriseFeignClient.listAllOrganizationByHolder(enterpriseGuid);
            holderOrganizationResultDTOList = holderOrganizationResultDTOList.stream()
                    .filter(e -> HolderOrganizationTypeEnum.DEPT.getType().equals(e.getType())).collect(Collectors.toList());
            createDTO.setHolderOrganizationResultList(holderOrganizationResultDTOList);
        } else {
            // 调整各个层级的parentIds
            HolderOrganizationResultDTOBuilder.setParentIds(createDTO.getHolderOrganizationResultList(), enterpriseGuid);
            // 防止前端childs传的有值
            createDTO.getHolderOrganizationResultList().forEach(e -> e.setChilds(new ArrayList<>()));
        }
        organizationService.createBatchOrganization(createDTO);
        return Result.buildEmptySuccess();
    }


    @ApiOperation("刷新当前组织机构")
    @GetMapping("/organization/refresh")
    public Result<Boolean> refreshOrganization() {
        log.info("聚合层-刷新当前组织机构");
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        // 查询门店已有的组织机构
        List<OrganizationDTO> organizationList = organizationService.queryOrganizationList();
        if (CollectionUtils.isEmpty(organizationList)) {
            return Result.buildEmptySuccess();
        }
        // 查询holder组织机构
        HolderOrganizationResultDTO organizationResult = cloudEnterpriseFeignClient.listTreeOrganizationByHolder(enterpriseGuid);
        // 添加绑定关系
        if (Objects.nonNull(organizationResult) && Objects.nonNull(organizationResult.getId())) {
            List<Long> organizationGuids = organizationList.stream().map(OrganizationDTO::getGuid)
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            HolderOrganizationResultDTOBuilder.relation(organizationResult, organizationGuids);
        }
        // 筛选需要刷新的组织机构
        List<HolderOrganizationResultDTO> importOrganizationList = Lists.newArrayList();
        HolderOrganizationResultDTOBuilder.findListByIsRelation(organizationResult, importOrganizationList);
        // 导入
        OrganizationBatchCreateDTO createDTO = new OrganizationBatchCreateDTO();
        createDTO.setHolderOrganizationResultList(importOrganizationList);
        organizationService.createBatchOrganization(createDTO);
        return Result.buildEmptySuccess();
    }


    @ApiOperation("同步holder组织机构名称")
    @GetMapping("/organization/holder/sync")
    public Result<Boolean> syncHolderOrganization() {
        log.info("聚合层-同步holder组织机构名称");
        String enterpriseGuid = UserContextUtils.getEnterpriseGuid();
        cloudEnterpriseFeignClient.syncHolderOrganization(enterpriseGuid);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "查询该企业下是否存在组织机构", notes = "查询该企业下是否存在组织机构")
    @GetMapping(value = "/organization/is_exist_organization")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "查询该企业下是否存在组织机构")
    public Result<Boolean> isExistOrganization() {
        return Result.buildSuccessResult(organizationService.isExistOrganization());
    }


    @ApiOperation("更新门店扎帐设置")
    @PostMapping("/store/update_by_accountcard")
    public Result updateStoreByAccountAndShowCard(@RequestBody @Valid BindupAccountsSaveDTO bindupAccountsSaveDTO) {
        log.info("聚合层-更新门店扎帐设置入参:{}", JacksonUtils.writeValueAsString(bindupAccountsSaveDTO));
        organizationService.updateStoreByAccountAndShowCard(bindupAccountsSaveDTO);
        return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.STORE_SETTLEMENT_SETTINGS_SUCCESSFUL));
    }
}