package com.holderzone.holder.saas.aggregation.weixin.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

@ApiModel("预订可使用时间范围请求入参")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class ReserveTimeReqDTO {

	@ApiModelProperty(value = "门店id",required = true)
	@NotBlank(message = "门店id不能为空")
	private String storeGuid;
}
