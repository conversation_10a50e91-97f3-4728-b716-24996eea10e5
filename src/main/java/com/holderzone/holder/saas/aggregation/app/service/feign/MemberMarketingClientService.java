package com.holderzone.holder.saas.aggregation.app.service.feign;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.marketing.portrayal.MemberPortrayalDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemQO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemVO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/12/23
 * @description 新会员营销中心
 */
@Component
@FeignClient(name = "holder-member-marketing", fallbackFactory = MemberMarketingClientService.ServiceFallBack.class, url = "${member.marketing.host}")
public interface MemberMarketingClientService {

    @ApiOperation("查询会员能参与的活动的相关商品及活动信息")
    @PostMapping("/marketing/limit_specials_activity/query_activity_commodity")
    List<LimitSpecialsActivityItemVO> queryActivityCommodity(@RequestBody LimitSpecialsActivityItemQO query);

    @ApiOperation("根据运营主体查询会员画像")
    @GetMapping("/marketing/member_portrayal/query_apply_setting")
    MemberPortrayalDetailsVO queryApplySetting(@RequestParam("operSubjectGuid") String operSubjectGuid);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<MemberMarketingClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public MemberMarketingClientService create(Throwable throwable) {

            return new MemberMarketingClientService() {

                @Override
                public List<LimitSpecialsActivityItemVO> queryActivityCommodity(LimitSpecialsActivityItemQO query) {
                    log.info(HYSTRIX_PATTERN, "queryActivityCommodity", JacksonUtils.writeValueAsString(query),
                            throwable.getCause());
                    throw new ServerException();
                }

                @Override
                public MemberPortrayalDetailsVO queryApplySetting(String operSubjectGuid) {
                    log.info(HYSTRIX_PATTERN, "queryApplySetting", operSubjectGuid,
                            throwable.getCause());
                    throw new ServerException();
                }

            };
        }
    }
}
