package com.holderzone.holder.saas.aggregation.merchant.controller.member.account;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberBalanceClientService;
import com.holderzone.holder.saas.member.dto.account.request.MemberBalanceChangeReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberBalanceRecordQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.request.MemberBalanceStatisticalReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberBalanceRecordListRespDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberSourceTypeRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberBalanceController
 * @date 2019/05/30 14:51
 * @description 会员余额
 * @program holder-saas-member-account
 */
@RestController
@Api(description="会员余额相关操作")
@RequestMapping(value = "/hsm_member_balance")
public class HsmMemberBalanceController {

    @Resource
    private HsmMemberBalanceClientService memberBalanceService;


    /**
     * 改变会员余额
     *
     * @param memberBalanceChangeReqDTO 会员余额改变请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/changeBalance", produces = "application/json;charset=utf-8")
    @ApiOperation("改变会员余额")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "改变会员余额")
    public Result<Boolean> changeBalance(@RequestBody MemberBalanceChangeReqDTO memberBalanceChangeReqDTO) {
        return Result
                .buildSuccessResult(memberBalanceService.changeBalance(memberBalanceChangeReqDTO));
    }


    /**
     * 来源类型
     *
     * @return 集合
     */
    @GetMapping(value = "/listSourceType", produces = "application/json;charset=utf-8")
    @ApiOperation("获取余额来源类型")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "获取余额来源类型")
    public Result<List<MemberSourceTypeRespDTO>> listSourceType() {
        return Result.buildSuccessResult(memberBalanceService.listSourceType());
    }


    /**
     * 通过条件分页查询
     *
     * @param queryReqDTO 查询条件
     * @return 查询结果
     */
    @PostMapping(value = "/listByCondition", produces = "application/json;charset=utf-8")
    @ApiOperation("通过条件分页查询")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "通过条件分页查询")
    public Result<Page<MemberBalanceRecordListRespDTO>> listByCondition(
            @RequestBody MemberBalanceRecordQueryReqDTO queryReqDTO) {
        return Result.buildSuccessResult(memberBalanceService.listByCondition(queryReqDTO));
    }


    /**
     * 统计会员持有的卡下的余额
     *
     * @param memberCardGuid 会员卡guid
     * @return 统计结果
     */
    @GetMapping(value = "/statistical", produces = "application/json;charset=utf-8")
    @ApiOperation("统计会员持有的卡下的余额")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "统计会员持有的卡下的余额")
    public Result<MemberBalanceStatisticalReqDTO> statistical(
            @RequestParam("memberCardGuid") String memberCardGuid) {
        return Result.buildSuccessResult(memberBalanceService.statistical(memberCardGuid));
    }


}

