package com.holderzone.holder.saas.aggregation.merchant.service.rpc.trade;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.Page;
import com.holderzone.saas.store.dto.trade.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> R
 * @date 2020/12/16 17:20
 * @description
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = DebtClientService.DebtFallBack.class)
public interface DebtClientService {
    @PostMapping("/debt/unit/save")
    @ApiOperation(value = "新建挂账单位")
    Boolean saveUnit(@Validated @RequestBody DebtUnitSaveReqDTO reqDTO);

    @PostMapping("/debt/unit/page")
    @ApiOperation(value = "分页查询单位列表")
    Page<DebtUnitPageRespDTO> unitPage(@RequestBody DebtUnitPageReqDTO reqDTO);

    @GetMapping("/debt/unit/delete")
    @ApiOperation(value = "删除挂账单位")
    Boolean unitDelete(@RequestParam("unitGuid") String unitGuid);

    @PostMapping("/debt/unit/page_debt_unit_record")
    @ApiOperation(value = "分页查询挂账单位流水")
    Page<DebtUnitRecordPageRespDTO> pageDebtUnitRecord(@Validated @RequestBody DebtUnitRecordPageReqDTO reqDTO);

    @GetMapping("/debt/unit/query_debt_unit_total")
    @ApiOperation(value = "查询挂账单位汇总")
    DebtUnitRecordTotalDTO queryDebtUnitTotal(@RequestParam("unitCode") String unitCode);

    @PostMapping("/debt/unit/update_debt_unit")
    @ApiOperation(value = "挂账管理还款")
    Boolean updateDebtUnit(@RequestBody DebtUnitRecordUpdateReqDTO updateReqDTO);

    @GetMapping("/debt/unit/query_debt_unit_list")
    @ApiOperation(value = "查询挂账单位")
    List<DebtUnitDropdownListDTO> queryDebtUnitList();

    @GetMapping("/debt/unit/query_debt_unit_total_by_guid")
    @ApiOperation(value = "根据单位Guid查询挂账单位汇总")
    DebtUnitRecordTotalDTO queryDebtUnitTotalByUnitGuid(@RequestParam("unitGuid") String unitGuid);

    @PostMapping("/debt/unit/h5login")
    @ApiOperation(value = "h5挂账页面登录")
    Boolean h5Login(@Validated @RequestBody DebtUnitLoginH5ReqDTO reqDTO);

    @PostMapping("/debt/record/h5query")
    @ApiOperation(value = "H5查询挂账还款记录")
    DebtRecordH5RespDTO queryDebtRecordH5(@Validated @RequestBody DebtUnitLoginH5ReqDTO reqDTO);

    @Component
    @Slf4j
    class DebtFallBack implements FallbackFactory<DebtClientService> {
        private static final Logger logger = LoggerFactory.getLogger(DebtClientService.DebtFallBack.class);

        @Override
        public DebtClientService create(Throwable throwable) {
            return new DebtClientService() {


                @Override
                public Boolean saveUnit(DebtUnitSaveReqDTO reqDTO) {
                    log.error("新建挂账单位接口FallBack，throwable={}", throwable.getMessage());
                    return Boolean.FALSE;
                }

                @Override
                public Page<DebtUnitPageRespDTO> unitPage(DebtUnitPageReqDTO reqDTO) {
                    log.error("分页查询单位列表接口FallBack，throwable={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean unitDelete(String unitGuid) {
                    log.error("删除挂账单位FallBack，throwable={}", throwable.getMessage());
                    return Boolean.FALSE;
                }

                @Override
                public Page<DebtUnitRecordPageRespDTO> pageDebtUnitRecord(DebtUnitRecordPageReqDTO reqDTO) {
                    log.error("分页查询挂账单位流水接口FallBack，throwable={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public DebtUnitRecordTotalDTO queryDebtUnitTotal(String unitGuid) {
                    log.error("查询挂账单位汇总接口FallBack，throwable={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean updateDebtUnit(DebtUnitRecordUpdateReqDTO updateReqDTO) {
                    log.error("挂账管理还款接口FallBack，throwable={}", throwable.getMessage());
                    return Boolean.FALSE;
                }

                @Override
                public List<DebtUnitDropdownListDTO> queryDebtUnitList() {
                    log.error("查询挂账单位接口FallBack，throwable={}", throwable.getMessage());
                    return Lists.newArrayList();
                }

                @Override
                public DebtUnitRecordTotalDTO queryDebtUnitTotalByUnitGuid(String unitGuid) {
                    log.error("根据单位Guid查询挂账单位汇总FallBack，throwable={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }

                @Override
                public Boolean h5Login(DebtUnitLoginH5ReqDTO reqDTO) {
                    log.error("h5挂账页面登录Fallback，throwable={}", throwable.getMessage());
                    return Boolean.FALSE;
                }

                @Override
                public DebtRecordH5RespDTO queryDebtRecordH5(DebtUnitLoginH5ReqDTO reqDTO) {
                    log.error("h5挂账页面查询挂账记录Fallback，throwable={}", throwable.getMessage());
                    throw new RuntimeException(throwable.getMessage());
                }
            };
        }
    }
}
