package com.holderzone.holder.saas.aggregation.merchant.service.rpc.business;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.manage.HandoverHistoryHandleDTO;
import com.holderzone.saas.store.dto.report.query.HandOverReportQueryDTO;
import com.holderzone.saas.store.dto.report.resp.HandoverReportRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = HandoverClientService.BusinessFallBack.class)
public interface HandoverClientService {

    @ApiOperation(value = "交接班报表查询")
    @PostMapping("/handover/report")
    List<HandoverReportRespDTO> report(@RequestBody HandOverReportQueryDTO handOverQueryDTO);

    @ApiOperation(value = "处理交接班历史数据")
    @PostMapping("/handover/handle_handover_history")
    void handleHandoverHistory(@RequestBody HandoverHistoryHandleDTO request);

    @Component
    class BusinessFallBack implements FallbackFactory<HandoverClientService> {

        private static final Logger logger = LoggerFactory.getLogger(BusinessFallBack.class);

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public HandoverClientService create(Throwable throwable) {
            return new HandoverClientService() {

                @Override
                public List<HandoverReportRespDTO> report(HandOverReportQueryDTO handOverQueryDTO) {
                    logger.error(HYSTRIX_PATTERN, "report", JacksonUtils.writeValueAsString(handOverQueryDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void handleHandoverHistory(HandoverHistoryHandleDTO request) {
                    logger.error(HYSTRIX_PATTERN, "handleHandoverHistory", JacksonUtils.writeValueAsString(request),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }

}
