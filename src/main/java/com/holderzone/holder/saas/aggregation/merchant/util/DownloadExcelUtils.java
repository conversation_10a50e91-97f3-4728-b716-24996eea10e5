package com.holderzone.holder.saas.aggregation.merchant.util;

import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.entity.enums.ExportLocaleEnum;
import com.holderzone.saas.store.dto.journaling.resp.OrderDetailRespDTO;
import com.holderzone.saas.store.dto.report.resp.ExportPayRespDTO;
import com.holderzone.saas.store.dto.report.resp.ExportRespDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import jxl.Workbook;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.UnderlineStyle;
import jxl.write.Number;
import jxl.write.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.beans.PropertyDescriptor;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DownloadExcelUtils
 * @date 2018/10/09 14:48
 * @description
 * @program holder-saas-store-report
 */
public class DownloadExcelUtils {
    private final static Logger logger = LoggerFactory.getLogger(DownloadExcelUtils.class);
    /**
     * 定义输出cell常量
     */
    private final static int CELLNUM = 60000;

    static WritableFont wfTitle = null;// 定义格式 字体 下划线 斜体 粗体 颜色
    static WritableFont wfCon = null;// 定义格式 字体 下划线 斜体 粗体 颜色
    static jxl.write.NumberFormat nf = null;
    static WritableCellFormat wcfTitle = null;
    static WritableCellFormat wcfTitleNum = null;//带数字格式
    static WritableCellFormat wcfCon = null;
    static WritableCellFormat wcfConNum = null;//带数字格式
    static int titleRowHeight = 700;
    static int extraColWidth = 5; //自适应额外列宽
    static int conRowHeight = 400;
    static int titleLengthV = 3;//标题列长度X设置列宽的参数
    static Border border = null;
    static BorderLineStyle bls = null;

    private static void InitParam() {
        nf = new jxl.write.NumberFormat("#0.00"); // 设置数字格式,保留两位小数
        wfTitle = new WritableFont(WritableFont.TAHOMA, 11,
                WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE,
                jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
        wcfTitle = new WritableCellFormat(wfTitle); // 单元格定义
        wcfTitleNum = new WritableCellFormat(wfTitle, nf); // 单元格定义
        border = Border.ALL;
        bls = BorderLineStyle.THIN;

        wfCon = new WritableFont(WritableFont.TAHOMA, 10,
                WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE,
                jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
        wcfCon = new WritableCellFormat(wfCon); // 单元格定义

        wcfConNum = new WritableCellFormat(wfCon, nf); // 单元格定义

        try {
            wcfTitle.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
            wcfTitle.setAlignment(jxl.format.Alignment.CENTRE); // 设置对齐方式
            wcfTitle.setBackground(jxl.format.Colour.GREY_25_PERCENT);
            wcfTitle.setBorder(border, bls);

            wcfTitleNum.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
            wcfTitleNum.setAlignment(jxl.format.Alignment.CENTRE); // 设置对齐方式
            wcfTitleNum.setBackground(jxl.format.Colour.GREY_25_PERCENT);
            wcfTitleNum.setBorder(border, bls);

            wcfCon.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
            wcfCon.setAlignment(jxl.format.Alignment.CENTRE); // 设置对齐方式
            wcfCon.setBorder(border, bls);

            wcfConNum.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE);
            wcfConNum.setAlignment(jxl.format.Alignment.CENTRE); // 设置对齐方式
            wcfConNum.setBorder(border, bls);

        } catch (WriteException e) {
            e.printStackTrace();
        }

    }

    /**
     * @param response
     * @param excelName
     * @param data      定义excel结构的数据
     * @param collData  要填充excel的数据
     * @param clazz     要填充的对象
     * @return void
     * @throws Exception
     * @throws
     * @Title: fullExcel
     * @Description:
     */
    @SuppressWarnings("rawtypes")
    public static void fullExcel(HttpServletRequest request, HttpServletResponse response, String excelName, String data, Collection<?> collData, Class<?> clazz) throws Exception {
        InitParam();
        String agent = request.getHeader("User-agent");
        // 如果是IE浏览器,则进行编码转换
        if (agent.contains("MSIE") || agent.contains("Trident")) {
            excelName = URLEncoder.encode(excelName, "UTF-8");
        } else {
            excelName = new String(excelName.getBytes(), "ISO-8859-1");
        }
        // 替换文件名中的空格
        excelName = excelName.replace("+", "%20");

        response.setCharacterEncoding("UTF-8");//设置相应内容的编码格式
        response.setContentType("application/msexcel;charset=UTF-8");//定义输出类型
        response.setHeader("Content-Disposition", "attachment;filename=" + excelName + DateTimeUtils.nowMillis() + ".xls");

        /**控制数据填充在哪一行的变量*/
        int contentNum = 1;
        /**定义输出sheet*/
        int sheetNum = 1;
        /**为防止null引发异常*/
        collData = collData == null ? new ArrayList() : collData;
        /**如果数据在一个sheet中显示不完，增加sheet数*/
        if (collData.size() > CELLNUM) {
            sheetNum = collData.size() % CELLNUM == 0 ? collData.size() / CELLNUM : collData.size() / CELLNUM + 1;
        }
        Iterator<?> it = collData.iterator();
        /**创建文件输出流*/
        OutputStream out = response.getOutputStream();
        try {
            /**通过输出流创建输出对象*/
            WritableWorkbook book = Workbook.createWorkbook(out);
            for (int i = 1; i <= sheetNum; i++) {
                /**创建输出sheet*/
                WritableSheet sheet = book.createSheet("第" + i + "页", i);
                /**填充表头列*/
                Map<String, Integer> map = creatHeadLabel(sheet, data);
                while (it.hasNext()) {
                    if (contentNum > CELLNUM) {
                        contentNum = 1;
                        break;
                    }
                    Object obj = it.next();
                    for (String name : map.keySet()) {
                        String fdName_up = "get" + name.substring(0, 1).toUpperCase() + name.substring(1, name.length());
                        try {
                            Method setter = clazz.getMethod(fdName_up, null);
                            Object con = setter.invoke(obj);
                            Label label = new Label(map.get(name), contentNum, ObjectToString(con), wcfCon);
                            sheet.addCell(label);

                        } catch (Exception e) {
                            logger.error("找不到" + fdName_up + "方法", e);
                            continue;
                        }
                    }
                    sheet.setRowView(contentNum, conRowHeight);
                    contentNum++;
                }
            }
            book.write();
            book.close();
            out.flush();
            out.close();
        } catch (Exception e) {
            logger.error("导出excel出错", e);
        }
    }

    public static void fullPayExcel(HttpServletResponse response, ExportPayRespDTO exportPayRespDTO) throws Exception {
        ExportRespDTO exportRespDTO = exportPayRespDTO.getExportRespDTO();
        String excelName = exportRespDTO.getExcelName();
        List collData = exportRespDTO.getList();
        String data = exportRespDTO.getHead();
        Class clazz = exportRespDTO.getClzz();
        InitParam();
        response.reset();
        response.setCharacterEncoding("UTF-8");//设置相应内容的编码格式
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");//定义输出类型
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(excelName, "UTF-8") + DateTimeUtils.localDate2String(DateTimeUtils.now().toLocalDate()) + ".xls");
        logger.info("response:{}" + response);
        /**控制数据填充在哪一行的变量*/
        int contentNum = 1;
        /**定义输出sheet*/
        int sheetNum = 1;
        /**为防止null引发异常*/
        collData = collData == null ? new ArrayList() : collData;
        /**如果数据在一个sheet中显示不完，增加sheet数*/
        if (collData.size() > CELLNUM) {
            sheetNum = collData.size() % CELLNUM == 0 ? collData.size() / CELLNUM : collData.size() / CELLNUM + 1;
        }
        Iterator<?> it = collData.iterator();
        /**创建文件输出流*/
        OutputStream out = response.getOutputStream();
        try {
            /**通过输出流创建输出对象*/
            WritableWorkbook book = Workbook.createWorkbook(out);
            for (int i = 1; i <= sheetNum; i++) {
                /**创建输出sheet*/
                WritableSheet sheet = book.createSheet("第" + i + "页", i);

                /**填充表头列*/
                Map<String, Integer> map = creatHeadLabel(sheet, data);
                while (it.hasNext()) {

                    if (contentNum > CELLNUM) {
                        contentNum = 1;
                        break;
                    }
                    Object obj = it.next();
                    for (String name : map.keySet()) {
                        try {
                            Field field = clazz.getDeclaredField(name);
                            PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clazz);
                            Method readMethod = pd.getReadMethod();
                            obj = JacksonUtils.toObject(clazz, JacksonUtils.writeValueAsString(obj));
                            Object rt = readMethod.invoke(obj);
                            if (rt instanceof LocalDateTime) {
                                rt = DateTimeUtils.localDateTime2String((LocalDateTime) rt, "yyyy-MM-dd HH:mm:ss");
                            }
                            if (rt instanceof LocalDate) {
                                rt = DateTimeUtils.localDate2String((LocalDate) rt, "yyyy-MM-dd");
                            }
                            if (rt instanceof BigDecimal) {
                                rt = ((BigDecimal) rt).setScale(2, BigDecimal.ROUND_HALF_UP);
                                Number numberCell = new Number(map.get(name), contentNum, rt != null ? Double.parseDouble(rt.toString()) : null, wcfConNum);
                                sheet.addCell(numberCell);
                                continue;
                            }
                            Label label = new Label(map.get(name), contentNum, rt != null ? rt.toString() : null, wcfCon);
                            sheet.addCell(label);
                        } catch (Exception e) {
                            logger.error("找不到" + name + "属性", e);
                        }
                    }
                    sheet.setRowView(contentNum, exportRespDTO.getHeight());
                    contentNum++;
                }
                logger.info("sheet:{}" + sheet.toString());
            }

            WritableSheet[] sheets = book.getSheets();
            WritableSheet sheet = sheets[sheets.length - 1];

            //将字符串格式转换为数字
            Double consumeSubtotal = Double.valueOf(exportPayRespDTO.getConsumeSubtotal());
            Double storedAmountSubtotal = Double.valueOf(exportPayRespDTO.getStoredAmountSubtotal());
            Double preOrderAmountSubtotal = Double.valueOf(exportPayRespDTO.getPreOrderAmountSubtotal());
            Double refundSubtotal = Double.valueOf(exportPayRespDTO.getRefundSubtotal());
            Double inoutSubtotal = Double.valueOf(exportPayRespDTO.getInoutSubtotal());
            Double cspTotal = Double.valueOf(exportPayRespDTO.getCspTotal());
            Double refundTotal = Double.valueOf(exportPayRespDTO.getRefundTotal());
            Double inoutTotal = Double.valueOf(exportPayRespDTO.getInoutTotal());

            Label subTotalTopic = new Label(0, contentNum, "小计：", wcfTitle);
            Number consumeSubtotalStr = new Number(1, contentNum, consumeSubtotal, wcfTitleNum);
            Number storedAmountSubtotalStr = new Number(2, contentNum, storedAmountSubtotal, wcfTitleNum);
            Number preOrderAmountSubtotalStr = new Number(3, contentNum, preOrderAmountSubtotal, wcfTitleNum);
//            Number refundSubtotalStr = new Number(4, contentNum, refundSubtotal, wcfTitleNum);
            Number inoutSubtotalStr = new Number(4, contentNum, inoutSubtotal, wcfTitleNum);
//            sheet.mergeCells(1, contentNum, 2, contentNum);
//            sheet.mergeCells(3, contentNum, 4, contentNum);
//            sheet.mergeCells(5, contentNum, 6, contentNum);
//            sheet.mergeCells(7, contentNum, 8, contentNum);
            sheet.addCell(subTotalTopic);
            sheet.addCell(consumeSubtotalStr);
            sheet.addCell(storedAmountSubtotalStr);
            sheet.addCell(preOrderAmountSubtotalStr);
//            sheet.addCell(refundSubtotalStr);
            sheet.addCell(inoutSubtotalStr);

            //隐藏列
//            sheet.setColumnView(2,0);
//            sheet.setColumnView(4,0);
//            sheet.setColumnView(6,0);
//            sheet.setColumnView(8,0);
//            sheet.mergeCells(1, contentNum + 1, 3, contentNum + 1);
//            sheet.mergeCells(7, contentNum + 1, 8, contentNum + 1);
//            Label totalTopic = new Label(0, contentNum + 1, "合计：", wcfTitle);
//            Number cspTotalStr = new Number(1, contentNum + 1, cspTotal, wcfTitleNum);
//            Number refundTotalStr = new Number(4, contentNum + 1, refundTotal, wcfTitleNum);
//            Number inoutTotalStr = new Number(5, contentNum + 1, inoutTotal, wcfTitleNum);
//            sheet.addCell(totalTopic);
//            sheet.addCell(cspTotalStr);
//            sheet.addCell(refundTotalStr);
//            sheet.addCell(inoutTotalStr);

            book.write();
            book.close();
            out.flush();
            out.close();
        } catch (Exception e) {
            logger.error("导出excel出错", e);
        }
    }

    private static String ObjectToString(Object con) {
        if (con instanceof LocalDateTime) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return df.format((LocalDateTime) con);
        }
        if (con instanceof LocalDate) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            return df.format((LocalDate) con);
        }
        return null == con ? "" : String.valueOf(con);
    }

    /**
     * @param response
     * @param excelName
     * @param data      定义excel结构的数据
     * @param collData  要填充excel的数据
     * @param clazz     要填充的对象
     * @return void
     * @throws Exception
     * @throws
     * @Title: fullExcel
     * @Description:
     */
    @SuppressWarnings("rawtypes")
    public static void fullExcel(HttpServletResponse response, String excelName, String data, Collection<?> collData, Class<?> clazz, int conRowHeight) throws Exception {
        InitParam();
        response.reset();
        response.setCharacterEncoding("UTF-8");//设置相应内容的编码格式
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");//定义输出类型
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(ExportLocaleEnum.getLocale(excelName), "UTF-8") + DateTimeUtils.localDate2String(DateTimeUtils.now().toLocalDate()) + ".xls");
        logger.info("response:{}" + response);
        /**控制数据填充在哪一行的变量*/
        int contentNum = 1;
        /**定义输出sheet*/
        int sheetNum = 1;
        /**为防止null引发异常*/
        collData = collData == null ? new ArrayList() : collData;
        /**如果数据在一个sheet中显示不完，增加sheet数*/
        if (collData.size() > CELLNUM) {
            sheetNum = collData.size() % CELLNUM == 0 ? collData.size() / CELLNUM : collData.size() / CELLNUM + 1;
        }
        Iterator<?> it = collData.iterator();
        /**创建文件输出流*/
        OutputStream out = response.getOutputStream();
        try {
            /**通过输出流创建输出对象*/
            WritableWorkbook book = Workbook.createWorkbook(out);
            for (int i = 1; i <= sheetNum; i++) {
                /**创建输出sheet*/
                WritableSheet sheet = book.createSheet(String.format(LocaleUtil.getMessage("EXPORT_PAGE"), i), i);

                /**填充表头列*/
                Map<String, Integer> map = creatHeadLabel(sheet, data);
                while (it.hasNext()) {
                    if (contentNum > CELLNUM) {
                        contentNum = 1;
                        break;
                    }
                    Object obj = it.next();
                    for (String name : map.keySet()) {
                        try {
                            Object rt;
                            //如果数据列是Map类型 直接获取
                            if (Map.class.isAssignableFrom(clazz)) {
                                rt = ((Map) obj).get(name);
                            } else {
                                //其他对象类型通过反射获取属性值
                                Field field = clazz.getDeclaredField(name);
                                PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clazz);
                                Method readMethod = pd.getReadMethod();
                                obj = JacksonUtils.toObject(clazz, JacksonUtils.writeValueAsString(obj));
                                rt = readMethod.invoke(obj);
                            }
                            if (rt instanceof LocalDateTime) {
                                rt = DateTimeUtils.localDateTime2String((LocalDateTime) rt, "yyyy-MM-dd HH:mm:ss");
                            }
                            if (rt instanceof LocalDate) {
                                rt = DateTimeUtils.localDate2String((LocalDate) rt, "yyyy-MM-dd");
                            }
                            if (rt instanceof BigDecimal) {
                                rt = ((BigDecimal) rt).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                                Number label = new Number(map.get(name), contentNum, rt != null ? Double.parseDouble(rt.toString()) : null, wcfConNum);
                                sheet.addCell(label);
                                continue;
                            }
                            if (rt instanceof Integer) {
                                Number label = new Number(map.get(name), contentNum, rt != null ? Integer.parseInt(rt.toString()) : null, wcfCon);
                                sheet.addCell(label);
                                continue;
                            }
                            if (rt instanceof Double) {
                                Number label = new Number(map.get(name), contentNum, rt != null ? Double.parseDouble(rt.toString()) : null, wcfConNum);
                                sheet.addCell(label);
                                continue;
                            }
                            Label label = new Label(map.get(name), contentNum, rt != null ? ExportLocaleEnum.getLocale(rt.toString()) : null, wcfCon);
                            sheet.addCell(label);

                        } catch (Exception e) {
                            logger.error("找不到" + name + "属性", e);
                        }
                    }
                    sheet.setRowView(contentNum, conRowHeight);
                    contentNum++;
                }
                logger.info("sheet:{}", JacksonUtils.writeValueAsString(sheet));
            }
            book.write();
            book.close();
            out.flush();
            out.close();
        } catch (Exception e) {
            logger.error("导出excel出错", e);
        }
    }

    public static void orderDetailExcel(HttpServletResponse response, String excelName, String head, List<OrderDetailRespDTO> collData, Map<String, String> paymentMap) throws Exception {
        InitParam();
        response.reset();
        response.setCharacterEncoding("UTF-8");//设置相应内容的编码格式
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");//定义输出类型
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(excelName, "UTF-8") + DateTimeUtils.localDate2String(DateTimeUtils.now().toLocalDate()) + ".xls");
        logger.info("response:{}" + response);
        /**控制数据填充在哪一行的变量*/
        int contentNum = 2;
        /**定义输出sheet*/
        int sheetNum = 1;
        /**为防止null引发异常*/
        collData = collData == null ? new ArrayList() : collData;
        /**如果数据在一个sheet中显示不完，增加sheet数*/
        if (collData.size() > CELLNUM) {
            sheetNum = collData.size() % CELLNUM == 0 ? collData.size() / CELLNUM : collData.size() / CELLNUM + 1;
        }
        /**创建文件输出流*/
        OutputStream out = response.getOutputStream();
        try {
            /**通过输出流创建输出对象*/
            WritableWorkbook book = Workbook.createWorkbook(out);
            for (int i = 1; i <= sheetNum; i++) {
                /**创建输出sheet*/
                WritableSheet sheet = book.createSheet("第" + i + "页", i);

                /**填充表头列*/
                Map<String, Integer> map = creatHeadLabelOrderDetail(sheet, head, paymentMap);
                for (OrderDetailRespDTO collDatum : collData) {
                    if (contentNum > CELLNUM) {
                        contentNum = 1;
                        break;
                    }
                    Label label0 = new Label(map.get("orderNo"), contentNum, collDatum.getOrderNo(), wcfCon);
                    Label label1 = new Label(map.get("stateName"), contentNum, collDatum.getStateName(), wcfCon);
                    Label label2 = new Label(map.get("tradeModeName"), contentNum, collDatum.getTradeModeName(), wcfCon);

                    Number label3 = new Number(map.get("guestCount"), contentNum, collDatum.getGuestCount(), wcfCon);
                    Number label4 = new Number(map.get("itemTotalFee"), contentNum, Double.parseDouble((collDatum.getItemTotalFee()).setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label5 = new Number(map.get("appendFee"), contentNum, Double.parseDouble((collDatum.getAppendFee()).setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label6 = new Number(map.get("orderFee"), contentNum, Double.parseDouble((collDatum.getOrderFee()).setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label7 = new Number(map.get("savingZero"), contentNum, Double.parseDouble((collDatum.getSavingZero()).setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label8 = new Number(map.get("memberDiscount"), contentNum, Double.parseDouble((collDatum.getMemberDiscount()).setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label9 = new Number(map.get("memberCoupons"), contentNum, Double.parseDouble((collDatum.getMemberCoupons()).setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label10 = new Number(map.get("itemPresent"), contentNum, Double.parseDouble((collDatum.getItemPresent()).setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label11 = new Number(map.get("fullSinglePrice"), contentNum, Double.parseDouble((collDatum.getFullSinglePrice()).setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label12 = new Number(map.get("groupCoupons"), contentNum, Double.parseDouble((collDatum.getGroupCoupons()).setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label13 = new Number(map.get("wholeDiscount"), contentNum, Double.parseDouble((collDatum.getWholeDiscount()).setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label14 = new Number(map.get("goodsGrouper"), contentNum, Double.parseDouble((collDatum.getGoodsGrouper()).setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label15 = new Number(map.get("campaign"), contentNum, Double.parseDouble((collDatum.getCampaign()).setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label16 = new Number(map.get("actuallyPayFee"), contentNum, Double.parseDouble((collDatum.getActuallyPayFee()).setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);

                    //订单来源
                    Label label17 = new Label(map.get("deviceTypeName"), contentNum, collDatum.getDeviceTypeName(), wcfCon);
                    //操作人
                    Label label18 = new Label(map.get("operationAccount"), contentNum, collDatum.getOperationAccount(), wcfCon);
                    //门店
                    Label label19 = new Label(map.get("storeName"), contentNum, collDatum.getStoreName(), wcfCon);
                    Label label20 = new Label(map.get("gmtCreate"), contentNum, DateTimeUtils.localDateTime2String(collDatum.getGmtCreate(), "yyyy-MM-dd HH:mm:ss"), wcfCon);

                    Number label21 = new Number(map.get("points"), contentNum, Double.parseDouble(collDatum.getPoints().setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label22 = new Number(map.get("memberPrice"), contentNum, Double.parseDouble(collDatum.getMemberPrice().setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label23 = new Number(map.get("singleDiscount"), contentNum, Double.parseDouble(collDatum.getSingleDiscount().setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Number label24 = new Number(map.get("goodsGrouper"), contentNum, Double.parseDouble(collDatum.getGoodsGrouper().setScale(2, BigDecimal.ROUND_HALF_UP).toString()), wcfConNum);
                    Label label25 = new Label(map.get("checkOutTime"), contentNum, DateTimeUtils.localDateTime2String(collDatum.getCheckOutTime(), "yyyy-MM-dd HH:mm:ss"), wcfCon);
                    sheet.addCell(label0);
                    sheet.addCell(label1);
                    sheet.addCell(label2);
                    sheet.addCell(label3);
                    sheet.addCell(label4);
                    sheet.addCell(label5);
                    sheet.addCell(label6);
                    sheet.addCell(label7);
                    sheet.addCell(label8);
                    sheet.addCell(label9);
                    sheet.addCell(label10);
                    sheet.addCell(label11);
                    sheet.addCell(label12);
                    sheet.addCell(label13);
                    sheet.addCell(label14);
                    sheet.addCell(label15);
                    sheet.addCell(label16);
                    sheet.addCell(label17);
                    sheet.addCell(label18);
                    sheet.addCell(label19);
                    sheet.addCell(label20);
                    sheet.addCell(label21);
                    sheet.addCell(label22);
                    sheet.addCell(label23);
                    sheet.addCell(label24);
                    sheet.addCell(label25);
                    Map<String, String> paymentTypeMap = collDatum.getPaymentTypeMap();
                    if (MapUtils.isEmpty(paymentTypeMap)) {
                        for (String s : paymentMap.keySet()) {
                            Label label = new Label(map.get(s) - 1, contentNum, "-", wcfCon);
                            sheet.addCell(label);
                        }
                    } else {
                        for (String key : paymentMap.keySet()) {
                            if (Objects.nonNull(map.get(key))) {
                                Number label = new Number(map.get(key) - 1, contentNum, paymentTypeMap.get(key) == null ? 0 : Double.parseDouble(paymentTypeMap.get(key)), wcfConNum);
                                sheet.addCell(label);
                            }
                        }
                    }
                    sheet.setRowView(contentNum, conRowHeight);
                    contentNum++;
                }
                logger.info("sheet:{}" + sheet.toString());
            }
            book.write();
            book.close();
            out.flush();
            out.close();
        } catch (Exception e) {
            logger.error("导出excel出错", e);
        }
    }

    /**
     * 解析data填充表头列并生成map
     *
     * @param sheet
     * @param data
     * @param fromRow 从第几行添加列头
     * @return
     * @throws Exception
     */
    private static Map<String, Integer> creatHeadLabel(WritableSheet sheet, String data, int fromRow) throws Exception {
        //data = data.substring(data.indexOf(",")+1);
        /**map<字段名,列>*/
        Map<String, Integer> map = new HashMap<String, Integer>();
        String cols[] = data.split(",");

        for (int i = 0; i < cols.length; i++) {
            if (!StringUtils.isEmpty(cols[i])) {
                String[] ac = cols[i].split(":");
                map.put(ac[0], i);
                //添加第一行标题
                Label label = new Label(i, fromRow, ac[1], wcfTitle);
                sheet.addCell(label);
                sheet.setColumnView(i, ac[1].length() * titleLengthV);//设置列宽
                if (i == 0) {
                    sheet.setColumnView(i, 13);
                }
            } else {
                logger.error("前台数据源解析出错", data);
                throw new Exception("前台数据源【" + data + "】解析出错");
            }
        }
        sheet.setRowView(fromRow, titleRowHeight);//设置行高
        return map;
    }

    /**
     * 解析data填充表头列并生成map
     *
     * @param sheet
     * @param data
     * @return
     * @throws Exception
     */
    private static Map<String, Integer> creatHeadLabel(WritableSheet sheet, String data) throws Exception {
        //data = data.substring(data.indexOf(",")+1);
        /**map<字段名,列>*/
        Map<String, Integer> map = new HashMap<>();
        String cols[] = data.split(",");
        for (int i = 0; i < cols.length; i++) {
            if (!StringUtils.isEmpty(cols[i])) {
                String[] ac = cols[i].split(":");
                String header = getLocaleHead(ac[0], ac[1]);
                map.put(ac[0], i);
                //添加第一行标题
                sheet.setColumnView(i, header.getBytes().length + extraColWidth);//设置列宽
                Label label = new Label(i, 0, header, wcfTitle);
                sheet.addCell(label);


            } else {
                logger.error("前台数据源解析出错", data);
                throw new Exception("前台数据源【" + data + "】解析出错");
            }
        }
        sheet.setRowView(0, titleRowHeight);//设置行高
        return map;
    }

    private static String getLocaleHead(String en, String cn) {
        return LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE ? cn : en;
    }

    /**
     * 解析data填充表头列并生成map
     *
     * @param sheet
     * @param data
     * @return
     * @throws Exception
     */
    private static Map<String, Integer> creatHeadLabelOrderDetail(WritableSheet sheet, String data, Map<String, String> paymentMap) throws Exception {
        //data = data.substring(data.indexOf(",")+1);
        /**map<字段名,列>*/
        Map<String, Integer> map = new HashMap<>();
        String cols[] = data.split(",");
        int paySize = paymentMap.size() - 1;
        int size = cols.length + paySize;
        logger.info("支付方式{}列，总的列数{}", paySize, size);
        for (int i = 0; i < cols.length; i++) {
            if (!StringUtils.isEmpty(cols[i])) {
                String[] ac = cols[i].split(":");
                if (i > 19) {
                    map.put(ac[0], i + paySize);
                    //添加第一行标题
                    sheet.setColumnView(i + paySize, ac[1].getBytes().length + extraColWidth);//设置列宽
                    Label label = new Label(i + paySize, 0, ac[1], wcfTitle);
                    sheet.addCell(label);
                } else {
                    map.put(ac[0], i);
                    //添加第一行标题
                    sheet.setColumnView(i, ac[1].getBytes().length + extraColWidth);//设置列宽
                    Label label = new Label(i, 0, ac[1], wcfTitle);
                    sheet.addCell(label);
                }
            } else {
                logger.error("前台数据源解析出错", data);
                throw new Exception("前台数据源【" + data + "】解析出错");
            }
        }
        int column = 19;
        for (String key : paymentMap.keySet()) {
            Label label = new Label(column, 1, paymentMap.get(key), wcfTitle);
            sheet.addCell(label);
            column++;
            map.put(key, column);
        }
        sheet.mergeCells(19, 0, column - 1, 0);
        sheet.mergeCells(0, 0, 0, 1);
        sheet.mergeCells(1, 0, 1, 1);
        sheet.mergeCells(2, 0, 2, 1);
        sheet.mergeCells(3, 0, 3, 1);
        sheet.mergeCells(4, 0, 4, 1);
        sheet.mergeCells(5, 0, 5, 1);
        sheet.mergeCells(6, 0, 6, 1);
        sheet.mergeCells(7, 0, 7, 1);
        sheet.mergeCells(8, 0, 8, 1);
        sheet.mergeCells(9, 0, 9, 1);
        sheet.mergeCells(10, 0, 10, 1);
        sheet.mergeCells(11, 0, 11, 1);
        sheet.mergeCells(12, 0, 12, 1);
        sheet.mergeCells(13, 0, 13, 1);
        sheet.mergeCells(14, 0, 14, 1);
        sheet.mergeCells(15, 0, 15, 1);
        sheet.mergeCells(16, 0, 16, 1);
        sheet.mergeCells(17, 0, 17, 1);
        sheet.mergeCells(18, 0, 18, 1);
        for (int i = 0; i <= paySize; i++) {
            sheet.mergeCells(column + i, 0, column + i, 1);
        }
        sheet.setRowView(0, titleRowHeight);//设置行高
        return map;
    }

}
