package com.holderzone.holder.saas.aggregation.merchant.controller.report;

import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.ReportClientService;
import com.holderzone.saas.store.dto.journaling.req.SalesVolumeReqDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeRespDTO;
import com.holderzone.saas.store.dto.journaling.resp.SalesVolumeStoreRespDTO;
import com.holderzone.saas.store.dto.report.query.GoodsSalesVO;
import com.holderzone.saas.store.dto.report.query.SalesDetailQO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesDTO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesStatisticDTO;
import com.holderzone.saas.store.dto.report.resp.GoodsSalesTotalDTO;
import com.holderzone.saas.store.dto.report.resp.SalesDetailRespDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;



/**
 * 订单商品报表
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/report/trade/item")
public class TradeItemController {

    private final ReportClientService reportClientService;

    @ApiOperation(value = "商品分类统计")
    @PostMapping("/type/statistics")
    public Result<GoodsSalesStatisticDTO> queryItemTypeStatistics(@RequestBody @Valid GoodsSalesVO query) {
        log.info("[商品分类统计]请求入参：{}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(reportClientService.queryItemTypeStatistics(query));
    }

    @ApiOperation(value = "套餐销量统计")
    @PostMapping("/group/sale/statistics")
    public Result<GoodsSalesStatisticDTO> queryGroupItemSaleStatistics(@RequestBody @Valid GoodsSalesVO query) {
        log.info("[套餐销量统计]请求入参：{}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(reportClientService.queryGroupItemSaleStatistics(query));
    }

    @ApiOperation(value = "门店商品销量")
    @PostMapping("/store/sale/statistics")
    public Result<Page<SalesVolumeRespDTO>> pageStoreSaleStatistics(@RequestBody SalesVolumeReqDTO query) {
        log.info("[门店商品销量]请求入参：{}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(reportClientService.pageStoreSaleStatistics(query));
    }

    @ApiOperation(value = "门店商品销量 - 按门店统计")
    @PostMapping("/group_by_store/sale/statistics")
    public Result<Page<SalesVolumeStoreRespDTO>> pageGroupByStoreSaleStatistics(@RequestBody SalesVolumeReqDTO query) {
        log.info("[门店商品销量]请求入参：{}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(reportClientService.pageGroupByStoreSaleStatistics(query));
    }

    @ApiOperation(value = "门店商品销量合计")
    @PostMapping("/store/sale/statistics/total")
    public Result<GoodsSalesTotalDTO> storeSaleStatisticsTotal(@RequestBody SalesVolumeReqDTO query) {
        log.info("[门店商品销量合计]请求入参：{}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(reportClientService.storeSaleStatisticsTotal(query));
    }

    @ApiOperation(value = "门店商品销量分类查询")
    @PostMapping("/store/sale/statistics/type")
    public Result<List<String>> pageStoreSaleStatisticsType(@RequestBody SalesVolumeReqDTO query) {
        log.info("[门店商品销量分类查询]请求入参：{}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(reportClientService.pageStoreSaleStatisticsType(query));
    }

    @ApiOperation(value = "门店商品销量套餐查询")
    @PostMapping("/store/sale/statistics/group")
    public Result<List<GoodsSalesDTO>> pageStoreSaleStatisticsGroup(@RequestBody SalesVolumeReqDTO query) {
        log.info("[门店商品销量套餐查询]请求入参：{}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(reportClientService.pageStoreSaleStatisticsGroup(query));
    }

    @ApiOperation(value = "商品分类统计导出")
    @PostMapping("/type/statistics/export")
    public Result<String> exportItemTypeStatistics(@RequestBody @Valid GoodsSalesVO query) {
        log.info("[商品分类统计导出]请求入参：{}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(reportClientService.exportItemTypeStatistics(query));
    }

    @ApiOperation(value = "套餐销量统计")
    @PostMapping("/group/sale/statistics/export")
    public Result<String> exportGroupItemSaleStatistics(@RequestBody @Valid GoodsSalesVO query) {
        log.info("[套餐销量统计]请求入参：{}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(reportClientService.exportGroupItemSaleStatistics(query));
    }

    @ApiOperation(value = "销售明细")
    @PostMapping("/sale/detail/page")
    public Result<Page<SalesDetailRespDTO>> pageSaleDetail(@RequestBody @Valid SalesDetailQO query) {
        log.info("[销售明细]query={}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(reportClientService.pageSaleDetail(query));
    }

    @ApiOperation(value = "销售明细导出")
    @PostMapping("/sale/detail/export")
    public Result<String> exportSaleDetail(@RequestBody @Valid SalesDetailQO query) {
        log.info("[销售明细导出]query={}", JacksonUtils.writeValueAsString(query));
        return Result.buildSuccessResult(reportClientService.exportSaleDetail(query));
    }
}
