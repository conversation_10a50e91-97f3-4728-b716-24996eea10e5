package com.holderzone.holder.saas.aggregation.app.service.feign.groupbuy;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouPonReqDTO;
import com.holderzone.saas.store.dto.takeaway.request.CouponDelReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponDoCheckRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtDelCouponRespDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TakeawayClientService
 * @date 2018/09/08 11:15
 * @description //TODO
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-takeaway-consumer", fallbackFactory = GroupBuyClientService.ServiceFallBack.class)
public interface GroupBuyClientService {

    @PostMapping("/groupbuy/check_ticket")
    MtCouponDoCheckRespDTO checkTicket(@RequestBody CouPonReqDTO couPonReqDTO);


    @PostMapping("/groupbuy/do_check")
    MtCouponDoCheckRespDTO doCheck(@RequestBody CouPonReqDTO couPonReqDTO);

    @PostMapping("/groupbuy/pre_check")
    MtCouponPreRespDTO preCheck(@RequestBody CouPonPreReqDTO couPonPreReqDTO);

    @PostMapping("/groupbuy/cancel_ticket")
    MtDelCouponRespDTO cancelCheckTicket(@RequestBody CouponDelReqDTO couponDelReqDTO);


    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<GroupBuyClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public GroupBuyClientService create(Throwable cause) {
            return new GroupBuyClientService() {

                @Override
                public MtCouponDoCheckRespDTO checkTicket(CouPonReqDTO couPonReqDTO) {
                    log.error(HYSTRIX_PATTERN, "checkTicket", JacksonUtils.writeValueAsString(couPonReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtDelCouponRespDTO cancelCheckTicket(CouponDelReqDTO couponDelReqDTO) {
                    log.error(HYSTRIX_PATTERN, "cancelCheckTicket", JacksonUtils.writeValueAsString(couponDelReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }


                @Override
                public MtCouponDoCheckRespDTO doCheck(CouPonReqDTO couPonReqDTO) {
                    log.error(HYSTRIX_PATTERN, "doCheck", JacksonUtils.writeValueAsString(couPonReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public MtCouponPreRespDTO preCheck(CouPonPreReqDTO couPonPreReqDTO) {
                    log.error(HYSTRIX_PATTERN, "preCheck", JacksonUtils.writeValueAsString(couPonPreReqDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }


            };


        }
    }
}