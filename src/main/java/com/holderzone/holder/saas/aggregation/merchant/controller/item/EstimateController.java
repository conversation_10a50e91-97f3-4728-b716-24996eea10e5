package com.holderzone.holder.saas.aggregation.merchant.controller.item;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.mapper.EstimateItemResidueMemchantRespFixDTO;
import com.holderzone.holder.saas.aggregation.merchant.mapper.EstimateMerchantConfigRespFixDTO;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.item.ItemClientService;
import com.holderzone.saas.store.dto.item.req.EstimateMerchantReqDTO;
import com.holderzone.saas.store.dto.item.req.EstimateReqDTO;
import com.holderzone.saas.store.dto.item.resp.EstimateItemResidueMemchantRespDTO;
import com.holderzone.saas.store.dto.item.resp.EstimateMerchantConfigRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @className EstimateController
 * @date 2019/05/14 14:53
 * @description //TODO
 * @program holder-saas-aggregation-merchant
 */
@RestController
@RequestMapping("/estimate")
@Api(description = "估清接口")
@Slf4j
public class EstimateController {

    @Autowired
    ItemClientService itemClientService;

    /**
     * 商户后台菜品设置估清
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "门店配置商品sku估清")
    @PostMapping("/save")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "门店配置商品sku估清",action = OperatorType.ADD)
    public Result saveEstimate(@RequestBody @Valid EstimateReqDTO request) {
        log.info("估清新增or更新接口入参,request={}", JacksonUtils.writeValueAsString(request));
        Integer num = itemClientService.saveOrUpdateEstimate(request);
        return Integer.valueOf(1).equals(num) ? Result.buildEmptySuccess() : Result.buildOpFailedResult("保存/更新失败");
    }

    /**
     * 商户后台菜品设置估清
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "条件获取门店全部商品sku列表")
    @PostMapping("/select_estimate_list_store")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "条件获取门店全部商品sku列表",action = OperatorType.SELECT)
    public Result<Page<EstimateMerchantConfigRespFixDTO>> selectEstimateListStore(@RequestBody @Valid EstimateMerchantReqDTO request) {
        log.info("商户后台-估清菜品列表接口入参,request={}", JacksonUtils.writeValueAsString(request));
        Page<EstimateMerchantConfigRespFixDTO> estimateMerchantConfigRespDTOS = itemClientService.selectItemEstimates(request);
        estimateMerchantConfigRespDTOS.getData().forEach(s->{
            //用于数据兼容
            //FIXME: 需要再会员上线后移除
            s.setItmeName(s.getItemName());
        });
        return Result.buildSuccessResult(estimateMerchantConfigRespDTOS);
    }

    /**
     * 商户后台菜品设置估清
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "条件获取门店估清剩余库存sku列表")
    @RequestMapping(value = {
            "/select_estimate_item_residue_store","/select_estimate_itme_residue_store"
    },method = RequestMethod.POST)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "条件获取门店估清剩余库存sku列表",action = OperatorType.SELECT)
    public Result<Page<EstimateItemResidueMemchantRespFixDTO>> selectEstimateItemResidueStore(@RequestBody @Valid EstimateMerchantReqDTO request) {
        log.info("商户后台-估清菜品剩余可售数接口入参,request={}", JacksonUtils.writeValueAsString(request));
        Page<EstimateItemResidueMemchantRespFixDTO> estimateItemResidueMerchantRespDTOS = itemClientService.selectEstimateItemResidueStore(request);
        estimateItemResidueMerchantRespDTOS.getData().forEach(s->{
            //用于数据兼容
            //FIXME: 需要再会员上线后移除
            s.setItmeName(s.getItemName());
        });
        return Result.buildSuccessResult(estimateItemResidueMerchantRespDTOS);
    }






}
