package com.holderzone.holder.saas.aggregation.weixin.utils.map;

import com.holderzone.holder.saas.aggregation.weixin.entity.dto.ReserveTimeRespDTO;
import com.holderzone.saas.store.reserve.api.dto.TimingSegmentDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ReserveTimeMAP {

	ReserveTimeMAP INSTANCE = Mappers.getMapper(ReserveTimeMAP.class);

	@Mappings({
			@Mapping(target = "reserveStartTime",source = "start"),
			@Mapping(target = "reserveEndTime",source = "end")
	})
	ReserveTimeRespDTO toReserveTime(TimingSegmentDTO timingSegmentDTO);

	List<ReserveTimeRespDTO> toReserveTimeList(List<TimingSegmentDTO> timingSegmentDTOS);
}
