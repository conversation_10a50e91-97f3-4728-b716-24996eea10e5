package com.holderzone.holder.saas.aggregation.app.service.feign.cmember.order;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.terminal.dto.common.RequestDishInfo;
import com.holderzone.holder.saas.member.terminal.dto.volume.*;
import com.holderzone.saas.store.dto.order.request.member.RequestQueryVolumeDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2019/6/18 20:36
 */
@Component
@FeignClient(name = "holder-saas-member-terminal",fallbackFactory = VolumeClientService.VolumeClientServiceFallBack.class )
public interface VolumeClientService {
    /**
     * 优惠劵的使用
     * @param volumeCode
     * @param volumeConsumeReqDTO
     * @return
     */
    @PostMapping("hsmca/volume/{volumeCode}/consume")
    List<RequestDishInfo> consume(@PathVariable("volumeCode") String volumeCode, @RequestBody RequestVolumeConsume volumeConsumeReqDTO);
    /**
     * 取消优惠劵的使用
     * @param
     * @return
     */
    @PostMapping( "hsmca/volume/{volumeCode}/cancel")
    List<RequestDishInfo> cancelByVolumeCode(@PathVariable("volumeCode")String volumeCode, @RequestBody RequestVolumeCancel volumeCancelReqDTO);

    /**
     * 劵码校验
     * @param volumeCode
     * @param volumeCheckOutInfoReqDTO
     * @return
     */
    @PostMapping("hsmca/volume/{volumeCode}/checkout")
    ResponseVolumeCheckOut checkoutByVolumeCode(@PathVariable(value ="volumeCode" )String volumeCode, @RequestBody RequestVolumeCheckOut volumeCheckOutInfoReqDTO);

    @GetMapping("hsmca/volume/consumeVolumeList")
    List<ResponseVolumeList> consumeVolumeList(@RequestParam("orderNumber") String orderNumber);

    @GetMapping("hsmca/volume/memberVolumeList")
    List<ResponseVolumeList> memberVolumeList(@RequestParam("memberInfoGuid") String memberInfoGuid);

    /**
     * 单张券验证（new）
     * @param request RequestVolumeCheckOut
     * @return ResponseVolumeCheckOut
     */
    @PostMapping("hsmca/volume/checkSingleVolumeRedeem")
    ResponseCheckSingleVolumeRedeem checkSingleVolumeRedeem(@RequestBody RequestCheckSingleVolumeRedeem request);

    @Slf4j
    @Component
    class VolumeClientServiceFallBack implements FallbackFactory<VolumeClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public VolumeClientService create(Throwable throwable) {
            return new VolumeClientService() {

                @Override
                public List<RequestDishInfo> consume(String volumeCode, RequestVolumeConsume volumeConsumeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "consume",volumeCode+ JacksonUtils.writeValueAsString(volumeConsumeReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("优惠劵的确认消费");
                }

                @Override
                public  List<RequestDishInfo> cancelByVolumeCode(String volumeCode, RequestVolumeCancel volumeCancelReqDTO) {
                    log.error(HYSTRIX_PATTERN, "getRechargeRuleByMemberInfoCardGuid",volumeCode+ JacksonUtils.writeValueAsString(volumeCancelReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("取消优惠劵的使用");
                }

                @Override
                public ResponseVolumeCheckOut checkoutByVolumeCode(String volumeCode, RequestVolumeCheckOut volumeCheckOutInfoReqDTO) {
                    log.error(HYSTRIX_PATTERN, "checkoutByVolumeCode",volumeCode+ JacksonUtils.writeValueAsString(volumeCheckOutInfoReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException("优惠劵的验证");
                }

                @Override
                public List<ResponseVolumeList> consumeVolumeList(String orderNumber) {
                    log.error("查看优惠劵的已校验列表错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public List<ResponseVolumeList> memberVolumeList(String memberInfoGuid) {
                    log.error("查看可用优惠劵列表错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseCheckSingleVolumeRedeem checkSingleVolumeRedeem(RequestCheckSingleVolumeRedeem request) {
                    log.error("验单券错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
