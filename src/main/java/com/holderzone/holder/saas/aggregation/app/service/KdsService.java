package com.holderzone.holder.saas.aggregation.app.service;

import com.holderzone.saas.store.dto.kds.req.ScanFinishFoodReqDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemDTO;
import com.holderzone.saas.store.dto.kds.resp.PrdDstItemTableDTO;
import com.holderzone.saas.store.dto.trade.OrderDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/20
 * @description kds聚合层业务
 */
public interface KdsService {

    /**
     * 扫码出餐
     *
     * @param request data为订单号必传
     */
    void scanFinishFood(ScanFinishFoodReqDTO request);

    /**
     * 发送叫号通知
     *
     * @param orderDTO 订单信息
     */
    void sendCallMessage(OrderDTO orderDTO);

    /**
     * 批量发送叫号通知
     *
     * @param prdDstItemList 出堂商品
     */
    void distributeBatchSendCallMessage(List<PrdDstItemDTO> prdDstItemList);
}
