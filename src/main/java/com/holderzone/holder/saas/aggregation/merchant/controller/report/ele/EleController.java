package com.holderzone.holder.saas.aggregation.merchant.controller.report.ele;

import com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.ele.EleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2018/11/02 下午 14:49
 * @description
 */
@Api(tags = "饿了么相关接口",hidden = true)
@RestController
@RequestMapping("/ele")
@Deprecated
public class EleController {

    private static final Logger log = LoggerFactory.getLogger(EleController.class);

    @Autowired
    private EleService eleService;

    @GetMapping("/getEnterpriseGuidByShopId/{shopId}")
    @ApiOperation(value = "查询enterpriseGuid")
    @Deprecated
    public String getEnterpriseGuidByShopId(@PathVariable("shopId") Long shopId) {
        log.info("查询enterpriseGuid入参： {}", shopId);
        return eleService.getEnterpriseGuidByShopId(shopId);
    }
}
