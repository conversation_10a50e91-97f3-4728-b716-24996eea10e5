package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberCouponClientService.MemberCouponClientServiceFallback;
import com.holderzone.holder.saas.member.dto.account.request.MemberCouponQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberCouponDetailRespDTO;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberCouponClientService
 * @date 2019/05/30 14:18
 * @description 会员持劵
 * @program holder-saas-member-account
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = MemberCouponClientServiceFallback.class)
public interface HsmMemberCouponClientService {

    /**
     * 分页查询用户的优惠券信息
     *
     * @param queryReqDTO 查询条件
     * @return 查询结果
     */
    @PostMapping(value = "/hsm_member_coupon/listByCondition", produces = "application/json;charset=utf-8")
    MemberCouponDetailRespDTO listByCondition(
        @RequestBody MemberCouponQueryReqDTO queryReqDTO);

    @Component
    class MemberCouponClientServiceFallback implements
            FallbackFactory<HsmMemberCouponClientService> {

        private static final Logger LOGGER = LoggerFactory
                .getLogger(MemberCouponClientServiceFallback.class);

        @Override
        public HsmMemberCouponClientService create(Throwable throwable) {
            return new HsmMemberCouponClientService() {
                @Override
                public MemberCouponDetailRespDTO listByCondition(
                        MemberCouponQueryReqDTO queryReqDTO) {
                    LOGGER.error("查询会员持劵记录错误:{}", ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}

