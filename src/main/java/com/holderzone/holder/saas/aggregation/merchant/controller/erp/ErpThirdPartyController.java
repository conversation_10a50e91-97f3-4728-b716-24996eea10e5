package com.holderzone.holder.saas.aggregation.merchant.controller.erp;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.ErpThirdPartyService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


/**
 * 第三方 erp
 */
@Slf4j
@RestController
@RequestMapping("/erp/third_party")
@RequiredArgsConstructor
public class ErpThirdPartyController {

    private final ErpThirdPartyService erpThirdPartyService;

    @ApiOperation(value = "查询第三方跳转链接")
    @GetMapping("/jump_url")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ERP, description = "查询第三方跳转链接")
    public Result<String> getLoginUrl() {
        log.info("ERP查询第三方跳转链接");
        return Result.buildSuccessResult(erpThirdPartyService.getLoginUrl());
    }

}
