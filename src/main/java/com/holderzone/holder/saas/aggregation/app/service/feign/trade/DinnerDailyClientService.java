package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className DinnerDailyClientService
 * @date 2019/02/15 10:25
 * @description 正餐 营业日报接口
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = DinnerDailyClientService.DinnerDailyFallBack.class)
public interface DinnerDailyClientService {

    @PostMapping("/daily/classify")
    List<ItemRespDTO> classify(DailyReqDTO request);

    @PostMapping("/daily/goods")
    List<ItemRespDTO> goods(DailyReqDTO request);

    @PostMapping("/daily/attr")
    List<AttrItemRespDTO> attr(DailyReqDTO request);

    @PostMapping("/daily/return_vegetables")
    List<ItemRespDTO> returnVegetables(DailyReqDTO request);

    @PostMapping("/daily/dish_giving")
    List<ItemRespDTO> dishGiving(DailyReqDTO request);

    @PostMapping("/daily/dining_type")
    List<DiningTypeRespDTO> diningType(DailyReqDTO request);

    @PostMapping("/daily/member_consumer")
    MemberConsumeRespDTO memberConsume(DailyReqDTO request);

    @PostMapping("/daily/gather")
    List<GatherRespDTO> gather(DailyReqDTO request);

    @PostMapping("/daily/overview")
    OverviewRespDTO overview(DailyReqDTO request);

    @GetMapping(value = "/order_detail/queryordernum")
    Integer queryOrderNumForStoreGuid(@RequestParam("storeGuid") String storeGuid);

    @GetMapping(value = "/order_detail/getfristorder")
    LocalDateTime getfristorderForStoreGuid(@RequestParam("storeGuid") String storeGuid);

    @ApiOperation(value = "查询券信息", notes = "查询券信息")
    @PostMapping("/groupon/list_by_request")
    List<AmountItemDTO> listByRequest(@RequestBody DailyReqDTO request);

    @PostMapping("/daily/refund")
    RefundRespDTO refund(@RequestBody DailyReqDTO request);

    @Slf4j
    @Component
    class DinnerDailyFallBack implements FallbackFactory<DinnerDailyClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";
        private static final String FORMAT = "失败，throwable={}";

        @Override
        public DinnerDailyClientService create(Throwable throwable) {
            return new DinnerDailyClientService() {

                @Override
                public List<ItemRespDTO> classify(DailyReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "classify",
                            JacksonUtils.writeValueAsString(request), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemRespDTO> goods(DailyReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "goods",
                            JacksonUtils.writeValueAsString(request), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<AttrItemRespDTO> attr(DailyReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "attr",
                            JacksonUtils.writeValueAsString(request), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemRespDTO> returnVegetables(DailyReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "returnVegetables",
                            JacksonUtils.writeValueAsString(request), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<ItemRespDTO> dishGiving(DailyReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "dishGiving",
                            JacksonUtils.writeValueAsString(request), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<DiningTypeRespDTO> diningType(DailyReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "diningType",
                            JacksonUtils.writeValueAsString(request), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public MemberConsumeRespDTO memberConsume(DailyReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "memberConsume",
                            JacksonUtils.writeValueAsString(request), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<GatherRespDTO> gather(DailyReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "gather",
                            JacksonUtils.writeValueAsString(request), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public OverviewRespDTO overview(DailyReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "overview",
                            JacksonUtils.writeValueAsString(request), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Integer queryOrderNumForStoreGuid(String storeGuid) {
                    log.error("查询数量失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("查询数量失败!!" + throwable.getMessage());
                }

                @Override
                public LocalDateTime getfristorderForStoreGuid(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "getfristorderForStoreGuid",
                            storeGuid, ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<AmountItemDTO> listByRequest(DailyReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "listByRequest",
                            JacksonUtils.writeValueAsString(request), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public RefundRespDTO refund(DailyReqDTO request) {
                    log.error(HYSTRIX_PATTERN, "refund",
                            JacksonUtils.writeValueAsString(request), ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}