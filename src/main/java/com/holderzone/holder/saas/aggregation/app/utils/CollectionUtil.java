package com.holderzone.holder.saas.aggregation.app.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Array;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className CollectionUtil
 * @date 2019/02/01 14:37
 * @description //集合/数组相关工具类
 * @program holder-saas-store-trade
 */
public class CollectionUtil {
    public static final long[] LONG_EMPTY_ARRAY = new long[0];
    public static final int[] INT_EMPTY_ARRAY = new int[0];

    /**
     * 提取集合中每项的字段值，并且去重
     *
     * @param col      集合
     * @param keyField key字段名称，多个字段使用加号'+'连接，但此时必须要求TKey为String
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <TKey> Set<TKey> getDisctinctFieldValueList(Collection<?> col, String keyField) {
        if (isEmpty(col)) {
            return Sets.newLinkedHashSet();
        }
        return new LinkedHashSet<>((List<TKey>) getFieldValueList(col, keyField));
    }

    /**
     * 提取集合中每项的字段值，忽略Null值，并且去重
     *
     * @param col      集合
     * @param keyField key字段名称，多个字段使用加号'+'连接，但此时必须要求TKey为String
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <TKey> Set<TKey> getDisctinctFieldValueListIgnoreNullKey(Collection<?> col, String keyField) {
        if (isEmpty(col)) {
            return Sets.newLinkedHashSet();
        }
        return new LinkedHashSet<>((List<TKey>) getFieldValueListIgnoreNullKey(col, keyField));
    }

    /**
     * 提取集合中每项的字段值
     *
     * @param col      集合
     * @param keyField key字段名称，多个字段使用加号'+'连接，但此时必须要求TKey为String
     * @return
     */
    public static <TKey> List<TKey> getFieldValueList(Collection<?> col, String keyField) {
        if (isEmpty(col)) {
            return Lists.newArrayList();
        }

        List<TKey> list = new ArrayList<>();
        for (Object item : col) {
            TKey key = getKeyValue(keyField, item);
            list.add(key);
        }
        return list;
    }

    /**
     * 提取集合中每项的字段值，忽略Null值
     *
     * @param col      集合
     * @param keyField key字段名称，多个字段使用加号'+'连接，但此时必须要求TKey为String
     * @return
     */
    public static <TKey> List<TKey> getFieldValueListIgnoreNullKey(Collection<?> col, String keyField) {
        if (isEmpty(col)) {
            return Lists.newArrayList();
        }

        List<TKey> list = new ArrayList<>();
        for (Object item : col) {
            TKey key = getKeyValue(keyField, item);
            if (key != null) {
                list.add(key);
            }
        }
        return list;
    }

    /**
     * 将集合转换为Map，当KEY为null时将被忽略
     *
     * @param col      集合
     * @param keyField key字段名称，多个字段使用加号'+'连接，但此时必须要求TKey为String
     */
    public static <TKey, TItem> Map<TKey, TItem> toMapIgnoreNullKey(Collection<TItem> col, String keyField) {
        if (isEmpty(col)) {
            return Maps.newLinkedHashMap();
        }
        Map<TKey, TItem> map = new LinkedHashMap<>();
        for (TItem item : col) {
            TKey key = getKeyValue(keyField, item);
            if (key != null) {
                map.put(key, item);
            }
        }
        return map;
    }

    /**
     * 将集合转换为Map
     *
     * @param col      集合
     * @param keyField key字段名称，多个字段使用加号'+'连接，但此时必须要求TKey为String
     */
    public static <TKey, TItem> Map<TKey, TItem> toMap(Collection<TItem> col, String keyField) {
        if (isEmpty(col)) {
            return Maps.newLinkedHashMap();
        }
        Map<TKey, TItem> map = new LinkedHashMap<>();
        for (TItem item : col) {
            TKey key = getKeyValue(keyField, item);
            map.put(key, item);
        }
        return map;
    }

    @SuppressWarnings("unchecked")
    private static <TKey, TItem> TKey getKeyValue(String keyField, TItem item) {
        if (keyField.contains("+")) {
            String[] keyArr = keyField.split("\\+");
            if (keyArr.length > 1) {
                StringBuilder builder = new StringBuilder();
                for (String k : keyArr) {
                    if (StringUtils.isBlank(k)) {
                        continue;
                    }
                    try {
                        Object key = ReflectUtil.getValue(item, StringUtils.trim(k));
                        if (builder.length() > 0) {
                            builder.append("+");
                        }
                        builder.append(key);
                    } catch (SecurityException | NoSuchFieldException | IllegalArgumentException |
                            IllegalAccessException e) {
                        throw new RuntimeException("字段" + keyField + "获取失败", e);
                    }
                }
                return (TKey) builder.toString();
            }
        }
        TKey key;
        try {
            key = (TKey) ReflectUtil.getValue(item, StringUtils.trim(keyField));
        } catch (SecurityException | NoSuchFieldException | IllegalArgumentException | IllegalAccessException e) {
            throw new RuntimeException("字段" + keyField + "获取失败", e);
        }
        return key;
    }

    /**
     * 将集合转换为Map，Value为List
     *
     * @param col      集合
     * @param keyField key字段名称，多个字段使用加号'+'连接，但此时必须要求TKey为String
     */
    public static <TKey, TItem> Map<TKey, List<TItem>> toListMap(Collection<TItem> col, String keyField) {
        if (isEmpty(col)) {
            return Maps.newLinkedHashMap();
        }
        Map<TKey, List<TItem>> map = new LinkedHashMap<>();
        for (TItem item : col) {
            TKey key = getKeyValue(keyField, item);
            List<TItem> l = map.get(key);
            if (l == null) {
                map.put(key, l = new ArrayList<>());
            }
            l.add(item);
        }
        return map;
    }

    /**
     * 转换为List
     */
    public static <T> List<T> asArrayListFromIterable(Iterable<T> iter) {
        if (iter == null) {
            return null;
        }
        List<T> list = new ArrayList<>();
        Iterator<T> it = iter.iterator();
        while (it.hasNext()) {
            T e = it.next();
            if (e != null) {
                list.add(e);
            }
        }
        return list;
    }

    /**
     * 转换为List
     */
    @SafeVarargs
    public static <T> List<T> asArrayList(T... elements) {
        if (elements == null) {
            return null;
        }
        List<T> list = new ArrayList<>(elements.length);
        for (T e : elements) {
            if (e != null) {
                list.add(e);
            }
        }
        return list;
    }

    /**
     * 转换为Set
     */
    public static <T> Set<T> asHashSetFromIterable(Iterable<T> iter) {
        if (iter == null) {
            return null;
        }
        Set<T> set = new HashSet<>();
        Iterator<T> it = iter.iterator();
        while (it.hasNext()) {
            T e = it.next();
            if (e != null) {
                set.add(e);
            }
        }
        return set;
    }

    /**
     * 转换为Set
     */
    @SafeVarargs
    public static <T> Set<T> asHashSet(T... elements) {
        if (elements == null) {
            return null;
        }
        Set<T> set = new HashSet<>(elements.length);
        for (T e : elements) {
            if (e != null) {
                set.add(e);
            }
        }
        return set;
    }

    /**
     * 转换为Set
     */
    @SafeVarargs
    public static <T> Set<T> asLinkedHashSet(T... elements) {
        if (elements == null) {
            return null;
        }
        Set<T> set = new LinkedHashSet<>(elements.length);
        for (T e : elements) {
            if (e != null) {
                set.add(e);
            }
        }
        return set;
    }

    /**
     * 合并数组
     */
    @SuppressWarnings("unchecked")
    public static <T> T[] mergeArray(T[]... arrs) {
        int len = 0;
        Class<?> componentType = null;
        for (T[] arr : arrs) {
            if (arr != null) {
                len += arr.length;
                componentType = arr.getClass().getComponentType();
            }
        }
        //
        T[] retArr = (T[]) Array.newInstance(componentType, len);
        int idx = 0;
        for (T[] arr : arrs) {
            if (arr != null) {
                for (int i = 0; i < arr.length; i++) {
                    retArr[idx++] = arr[i];
                }
            }
        }

        return retArr;
    }

    public static long[] toLongArray(Collection<Long> col) {
        if (col == null) {
            return null;
        } else if (col.size() == 0) {
            return LONG_EMPTY_ARRAY;
        }
        // 过滤null值
        Iterator<Long> it = col.iterator();
        while (it.hasNext()) {
            Long val = it.next();
            if (val == null) {
                it.remove();
            }
        }
        long[] arr = new long[col.size()];
        int idx = 0;
        for (long l : col) {
            arr[idx++] = l;
        }
        return arr;
    }

    /**
     * 转换为Long类型List
     */
    public static List<Long> toLongList(long... elements) {
        if (elements == null) {
            return null;
        }
        List<Long> list = new ArrayList<>(elements.length);
        for (long e : elements) {
            list.add(e);
        }
        return list;
    }

    /**
     * 转换为Long类型HashSet
     */
    public static Set<Long> toLongSet(long... elements) {
        if (elements == null) {
            return null;
        }
        Set<Long> set = new HashSet<>(elements.length);
        for (long e : elements) {
            set.add(e);
        }
        return set;
    }

    public static int[] toIntegerArray(Collection<Integer> col) {
        if (col == null) {
            return null;
        } else if (col.size() == 0) {
            return INT_EMPTY_ARRAY;
        }
        // 过滤null值
        Iterator<Integer> it = col.iterator();
        while (it.hasNext()) {
            Integer val = it.next();
            if (val == null) {
                it.remove();
            }
        }
        int[] arr = new int[col.size()];
        int idx = 0;
        for (int l : col) {
            arr[idx++] = l;
        }
        return arr;
    }

    /**
     * 转换为Integer类型List
     */
    public static List<Integer> toIntegerList(int... elements) {
        if (elements == null) {
            return null;
        }
        List<Integer> list = new ArrayList<>(elements.length);
        for (int e : elements) {
            list.add(e);
        }
        return list;
    }

    /**
     * 切分成Long集合
     *
     * @param text
     * @param separator
     * @return
     */
    public static List<Long> splitAsLong(String text, String separator) {
        if (StringUtils.isBlank(separator)) {
            return Lists.newArrayList();
        }
        if (StringUtils.isNotBlank(text)) {
            String[] arr = text.split(separator);
            List<Long> list = new ArrayList<>(arr.length);
            for (String v : arr) {
                list.add(Long.parseLong(StringUtils.trim(v)));
            }
            return list;
        }
        return Lists.newArrayList();
    }

    /**
     * 切分并去空格
     *
     * @param text
     * @param separator
     * @return
     */
    public static List<String> splitAndTrim(String text, String separator) {
        if (StringUtils.isBlank(separator)) {
            return Lists.newArrayList();
        }
        if (StringUtils.isNotBlank(text)) {
            String[] arr = text.split(separator);
            List<String> list = new ArrayList<>(arr.length);
            for (String v : arr) {
                list.add(StringUtils.trim(v));
            }
            return list;
        }
        return Lists.newArrayList();
    }

    /**
     * 判断集合是否为空
     */
    public static <T> boolean isEmpty(Collection<T> col) {
        return col == null || col.isEmpty();
    }

    /**
     * 判断集合非空
     */
    public static <T> boolean isNotEmpty(Collection<T> col) {
        return !isEmpty(col);
    }

    /**
     * 判断数组是否为空
     */
    public static boolean isEmpty(int[] arr) {
        return arr == null || arr.length == 0;
    }

    /**
     * 判断数组是否为空
     */
    public static boolean isEmpty(long[] arr) {
        return arr == null || arr.length == 0;
    }

    /**
     * 判断数组是否为空
     */
    public static <T> boolean isEmpty(T[] arr) {
        return arr == null || arr.length == 0;
    }

    /**
     * 判断数组非空
     */
    public static <T> boolean isNotEmpty(T[] arr) {
        return !isEmpty(arr);
    }

    /**
     * 判断Map是否为空
     */
    public static <K, V> boolean isEmpty(Map<K, V> map) {
        return map == null || map.isEmpty();
    }

    /**
     * 判断Map非空
     */
    public static <K, V> boolean isNotEmpty(Map<K, V> map) {
        return !isEmpty(map);
    }

    /**
     * 判断集合1是否包含集合2中的任一元素
     */
    public static <T> boolean containsAny(Collection<T> col1, Collection<T> col2) {
        if (isEmpty(col1)) {
            return false;
        } else if (isEmpty(col2)) {
            return true;
        }
        for (T item : col2) {
            if (col1.contains(item)) {
                return true;
            }
        }
        return false;
    }

}
