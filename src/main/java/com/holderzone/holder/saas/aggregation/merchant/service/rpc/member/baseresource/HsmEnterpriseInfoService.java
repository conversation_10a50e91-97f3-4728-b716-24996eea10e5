package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmEnterpriseReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.HsmEnterpriseRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HsmEnterpriseInfoService
 * @date 2019/05/31 14:38
 * @description 商家(企业)
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = HsmEnterpriseInfoService.HsmEnterpriseInfoServiceFallBack.class)
public interface HsmEnterpriseInfoService {


    /**
     * 同步企业
     *
     * @param hsmEnterpriseReqDTO 列表
     */
    @ApiOperation("同步企业")
    @PostMapping("/hsm/enterprise/syc")
    HsmEnterpriseRespDTO syc(@RequestBody HsmEnterpriseReqDTO hsmEnterpriseReqDTO);


    /**
     * 同步企业
     *
     * @param hsmEnterpriseReqDTOs 列表
     */
    @ApiOperation("同步企业")
    @PostMapping("/hsm/enterprise/syc/list")
    List<HsmEnterpriseRespDTO> sycList(@RequestBody List<HsmEnterpriseReqDTO> hsmEnterpriseReqDTOs);

    /**
     * 保存企业
     *
     * @param hsmEnterpriseReqDTO
     * @return
     */
    @ApiOperation("保存企业")
    @PostMapping("/hsm/enterprise/save")
    boolean save(@RequestBody HsmEnterpriseReqDTO hsmEnterpriseReqDTO);

    /**
     * 修改企业
     *
     * @param hsmEnterpriseReqDTO
     * @return
     */
    @ApiOperation("修改企业")
    @PostMapping("/hsm/enterprise/modify")
    boolean modify(@RequestBody HsmEnterpriseReqDTO hsmEnterpriseReqDTO);


    @GetMapping("/hsm/enterprise/have/system")
    @ApiOperation(value = "根据登录用户的企业Guid查询是否关联体系")
    boolean isHaveSystem();

    /**
     * @param enterpriseKey 外部商家主键
     * @param allianceid    联盟ID
     * @return
     * <AUTHOR>
     * 根据Guid删除商家(企业)信息
     */
    @ApiOperation(
            value = "根据Guid删除商家(企业)信息", notes = "根据Guid删除商家(企业)信息"
    )
    @DeleteMapping("/hsm/enterprise/{enterpriseKey}/{allianceid}")
    boolean removeHsmEnterprise(@ApiParam("enterpriseKey") @PathVariable("enterpriseKey") String enterpriseKey, @ApiParam("allianceid") @PathVariable("allianceid") String allianceid);

    @Component
    @Slf4j
    class HsmEnterpriseInfoServiceFallBack implements FallbackFactory<HsmEnterpriseInfoService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public HsmEnterpriseInfoService create(Throwable cause) {
            return new HsmEnterpriseInfoService() {
                @Override
                public HsmEnterpriseRespDTO syc(HsmEnterpriseReqDTO hsmEnterpriseReqDTO) {
                    log.error(HYSTRIX_PATTERN, "syc",
                        JSONObject.toJSON(hsmEnterpriseReqDTO), ThrowableUtils
                            .asString(cause));
                    throw new BusinessException("同步企业数据失败");
                }

                @Override
                public List<HsmEnterpriseRespDTO> sycList(List<HsmEnterpriseReqDTO> hsmEnterpriseReqDTOs) {
                    log.error(HYSTRIX_PATTERN, "sycList",
                            JSONObject.toJSON(hsmEnterpriseReqDTOs), ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("同步企业列表数据失败");
                }

                @Override
                public boolean save(HsmEnterpriseReqDTO hsmEnterpriseReqDTO) {
                    log.error(HYSTRIX_PATTERN, "save",
                            JSONObject.toJSON(hsmEnterpriseReqDTO), ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("保存企业数据失败");
                }

                @Override
                public boolean modify(HsmEnterpriseReqDTO hsmEnterpriseReqDTO) {
                    log.error(HYSTRIX_PATTERN, "modify",
                            JSONObject.toJSON(hsmEnterpriseReqDTO), ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("修改企业数据失败");
                }

                @Override
                public boolean removeHsmEnterprise(String enterpriseKey, String allianceid) {
                    log.error(HYSTRIX_PATTERN, "removeHsmEnterprise",
                           enterpriseKey+","+allianceid, ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("删除企业数据失败");
                }

                @Override
                public boolean isHaveSystem() {
                    log.error(HYSTRIX_PATTERN, "isHaveSystem",
                           ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("根据登录用户的企业Guid查询是否关联体系");
                }
            };
        }
    }
}
