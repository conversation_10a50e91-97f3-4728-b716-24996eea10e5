package com.holderzone.holder.saas.aggregation.merchant.controller.device;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.manage.StoreDeviceService;
import com.holderzone.saas.store.dto.terminal.StoreDeviceDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceQueryDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceSortDTO;
import com.holderzone.saas.store.dto.terminal.StoreDeviceUnbindDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @className StoreDeviceController
 * @date 18-9-13 下午3:09
 * @description 门店-设备相关接口
 * @program holder-saas-aggregation-merchant
 */
@RestController
@RequestMapping("/storeDevice")
@Api(description = "门店设备相关服务接口")
public class StoreDeviceController {

    private final StoreDeviceService storeDeviceService;

    @Autowired
    public StoreDeviceController(StoreDeviceService storeDeviceService) {
        this.storeDeviceService = storeDeviceService;
    }

    @ApiOperation(value = "建立设备门店绑定关系", notes = "建立设备门店绑定关系")
    @PostMapping(value = "/create", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT,description = "建立设备门店绑定关系")
    public Result create(@RequestBody StoreDeviceDTO storeDeviceDTO) {
        if (storeDeviceService.create(storeDeviceDTO)) {
            return Result.buildEmptySuccess();
        }
        return Result.buildOpFailedResult("门店失败");
    }

    @ApiOperation(value = "根据门店guid或设备类型查询设备列表", notes = "根据门店guid或设备类型查询设备列表")
    @PostMapping(value = "/findStoreDevice", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT,description = "根据门店guid或设备类型查询设备列表")
    public Result<List<StoreDeviceDTO>> findStoreDevice(@RequestBody @Validated StoreDeviceQueryDTO storeDeviceQueryDTO) {
        return Result.buildSuccessResult(storeDeviceService.findStoreDevice(storeDeviceQueryDTO));
    }

    @ApiOperation(value = "设置主机")
    @PostMapping("/set_master/{deviceGuid}")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT,description = "设置主机")
    public void setMasterDevice(@PathVariable("deviceGuid") String deviceGuid) {
        storeDeviceService.setMasterDevice(deviceGuid);
    }

    @ApiOperation(value = "设备排序", notes = "设备排序")
    @PostMapping(value = "/sort", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT,description = "设备排序")
    public Result sort(@RequestBody @Validated List<StoreDeviceSortDTO> storeDeviceSortDTOS) {
        if (storeDeviceService.sort(storeDeviceSortDTOS)) {
            return Result.buildEmptySuccess();
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.DEVICE_SORTING_FAILED));
    }

    @ApiOperation(value = "设备解绑", notes = "设备解绑")
    @PostMapping(value = "/unbind", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT,description = "设备解绑")
    public Result unbind(@RequestBody @Validated StoreDeviceUnbindDTO storeDeviceUnbindDTO) {
        if (storeDeviceService.unbind(storeDeviceUnbindDTO)) {
            return Result.buildSuccessMsg(LocaleUtil.getMessage(Constants.DEVICE_UNBINDING_SUCCESSFUL));
        }
        return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.DEVICE_UNBINDING_FAILED));
    }

    @ApiOperation(value = "根据设备Id查询设备的绑定状态", notes = "根据设备Id查询设备的绑定状态")
    @GetMapping(value = "/findDeviceStatus/{deviceId}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ORGANIZAT, description = "根据设备Id查询设备的绑定状态")
    public Result<StoreDeviceDTO> findDeviceStatus(@ApiParam(value = "设备id") @PathVariable("deviceId") String deviceId) {
        return Result.buildSuccessResult(storeDeviceService.findDeviceStatus(deviceId));
    }
}
