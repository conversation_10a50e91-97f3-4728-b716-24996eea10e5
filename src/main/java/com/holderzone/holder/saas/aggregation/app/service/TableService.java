package com.holderzone.holder.saas.aggregation.app.service;

import com.holderzone.holder.saas.aggregation.app.anno.RequireRdsLock;
import com.holderzone.saas.store.dto.boss.req.BossTableQueryDTO;
import com.holderzone.saas.store.dto.boss.resp.BossTableRespDTO;
import com.holderzone.saas.store.dto.order.request.dinein.CancelOrderReqDTO;
import com.holderzone.saas.store.dto.store.table.TableDTO;
import com.holderzone.saas.store.dto.table.*;
import com.holderzone.saas.store.reserve.api.dto.TableOrderReserveDTO;
import org.springframework.util.StopWatch;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className TableService
 * @date 2019/01/07 11:23
 * @description
 * @program holder-saas-aggregation-app
 */
public interface TableService {

    List<AreaDTO> queryArea(String storeGuid);

    List<TableOrderReserveDTO> queryTable(TableBasicQueryDTO tableBasicQueryDTO, StopWatch stopWatch);

    String openTable(OpenTableDTO openTableDTO);

    String associatedOpenTable(OpenAssociatedTableDTO openAssociatedTableDTO);

    String cleanTable(TableStatusChangeDTO tableStatusChangeDTO);

    boolean turnTable(TurnTableDTO turnTableDTO);

    List<String> combine(TableCombineDTO tableCombineDTO);

    TableCombineVerifyRespDTO verifyCombine(TableCombineDTO tableCombineDTO);

    @RequireRdsLock
    TableCombineRespDTO combineV2(TableCombineDTO tableCombineDTO);

    boolean separateTable(TableOrderCombineDTO tableOrderCombineDTO);

    boolean closeTable(CancelOrderReqDTO cancelOrderReqDTO);

    boolean payCloseTable(CancelOrderReqDTO cancelOrderReqDTO);

    boolean tryLock(TableLockDTO tableLockDTO);

    boolean releaseLock(TableLockDTO tableLockDTO);

    /**
     * 根据桌台guid，查桌台详情
     * 订单状态:只要接了一个单都是接单，否则没有
     *
     * @param tableGuid 桌台guid
     * @return 桌台详情
     */
    TableDTO queryTableByGuid(String tableGuid);

    /**
     * 老板助手
     * 桌台列表
     */
    List<BossTableRespDTO> listTable(BossTableQueryDTO queryDTO);

    /**
     * 刷新桌台
     */
    void refresh(TableRefreShDTO refreShDTO);
}
