package com.holderzone.holder.saas.aggregation.app.aop;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.config.ApiVersionControlConfig;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * api 版本控制
 */
@Slf4j
@Aspect
@Component
public class ApiVersionControlAspect {


    @Autowired
    private ApiVersionControlConfig apiVersionControlConfig;


    @Pointcut("@annotation(com.holderzone.holder.saas.aggregation.app.anno.ApiVersionControl)")
    public void pointCut() {

    }

    /**
     * api 版本控制
     */
    @Before("pointCut()")
    public void doBefore(JoinPoint joinPoint) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (Objects.isNull(attributes)) {
            return;
        }
        HttpServletRequest request = attributes.getRequest();
        String currentApiVersion = request.getHeader(Constant.API_VERSION);
        if (StringUtils.isEmpty(currentApiVersion)) {
            log.error("当前请求无版本信息, uri:{}, params:{}", request.getRequestURI(), JacksonUtils.writeValueAsString(joinPoint.getArgs()));
            throw new BusinessException(Constant.API_VERSION_ERROR);
        }
        String requestURI = request.getRequestURI();
        log.info("api version control uri:{}", requestURI);
        List<ApiVersionControlConfig.InnerControl> controls = apiVersionControlConfig.getControl();
        if (CollectionUtils.isEmpty(controls)) {
            return;
        }
        Integer minVersion = getInControlUriMinVersion(requestURI, controls);
        if (Objects.nonNull(minVersion) &&  Integer.parseInt(currentApiVersion) < minVersion) {
            throw new BusinessException(Constant.API_VERSION_ERROR);
        }
    }


    private Integer getInControlUriMinVersion(String uri, List<ApiVersionControlConfig.InnerControl> controls) {
        if (CollectionUtils.isEmpty(controls)) {
            return null;
        }
        // 排序
        List<ApiVersionControlConfig.InnerControl> sortedControls = controls.stream()
                .sorted(Comparator.comparing(ApiVersionControlConfig.InnerControl::getMin).reversed())
                .collect(Collectors.toList());
        return sortedControls.stream().filter(control -> {
            String controlUri = control.getUri();
            for (String innerUri : controlUri.split(",")) {
                if (uri.contains(innerUri) || uri.matches(innerUri)) {
                    return true;
                }
            }
            return false;
        }).findFirst().orElse(new ApiVersionControlConfig.InnerControl()).getMin();
    }

}