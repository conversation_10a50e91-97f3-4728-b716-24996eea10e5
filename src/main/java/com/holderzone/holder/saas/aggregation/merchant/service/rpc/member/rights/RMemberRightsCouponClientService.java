package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.rights;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;

/**
 * <AUTHOR>
 * @description
 * @date 2019/5/20 17:01
 */
@Component
@FeignClient(name = "holder-saas-member-account",fallbackFactory=RMemberRightsCouponClientService.RMemberRightsCouponClientServiceFallBack.class)
public interface RMemberRightsCouponClientService {
   /**
    * 根据guid 删除
    *
    * @param guid
    * @return
    */
   @DeleteMapping(value = "/hsm-rmember-rights-coupon/{guid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
   boolean delByGuid(String guid);

   @Slf4j
   @Component
   class RMemberRightsCouponClientServiceFallBack implements FallbackFactory<RMemberRightsCouponClientService> {
      private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

      @Override
      public RMemberRightsCouponClientService create(Throwable throwable) {
         return new RMemberRightsCouponClientService() {
            @Override
            public boolean delByGuid(String guid) {
               log.error(HYSTRIX_PATTERN, "delByGuid", guid, ThrowableUtils.asString(throwable));
               throw new BusinessException("删除优惠劵失败");
            }
         };
      }
   }
}
