package com.holderzone.holder.saas.aggregation.app.service.feign;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.message.BusinessMessageDTO;
import com.holderzone.saas.store.dto.message.MsgInfoRespDTO;
import com.holderzone.saas.store.dto.message.MsgQuery;
import com.holderzone.saas.store.dto.message.MsgRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MessageClientService
 * @date 2018/09/25 11:06
 * @description
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-message", fallbackFactory = MessageClientService.MsgFallBack.class)
public interface MessageClientService {

    @PostMapping("/all")
    Page<MsgInfoRespDTO> queryAllInfo(MsgQuery msgQuery);

    @PostMapping("/detail")
    MsgRespDTO detail(@RequestBody MsgQuery msgQuery);

    @PostMapping("/count")
    int count(MsgQuery msgQuery);

    /**
     * 推送消息至安卓端
     *
     * @param businessMessageDTO 消息
     * @return success
     */
    @ApiOperation(value = "接受消息的接口", notes = "返回success，表示推送成功")
    @PostMapping("/msg")
    String msg(BusinessMessageDTO businessMessageDTO);

    @GetMapping("/read_all")
    @ApiOperation(value = "一键已读")
    void readAll(@RequestParam("storeGuid") String storeGuid, @RequestParam("messageType") Integer messageType);

    @Component
    class MsgFallBack implements FallbackFactory<MessageClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        private static final Logger logger = LoggerFactory.getLogger(MsgFallBack.class);

        @Override
        public MessageClientService create(Throwable throwable) {
            return new MessageClientService() {
                @Override
                public Page<MsgInfoRespDTO> queryAllInfo(MsgQuery msgQuery) {
                    logger.error("查询消息info异常 ，e={}", throwable.getMessage());
                    throw new BusinessException("查询消息info异常!!" + throwable.getMessage());
                }

                @Override
                public MsgRespDTO detail(MsgQuery msgQuery) {
                    logger.error("查询消息详情异常 ，e={}", throwable.getMessage());
                    throw new BusinessException("查询消息详情异常!!" + throwable.getMessage());
                }

                @Override
                public int count(MsgQuery msgQuery) {
                    logger.error("查询消息count异常 ，e={}", throwable.getMessage());
                    throw new BusinessException("查询消息count异常!!" + throwable.getMessage());
                }

                @Override
                public String msg(BusinessMessageDTO businessMessageDTO) {
                    logger.error("推送消息至安卓失败 {}", throwable.getMessage());
                    throw new BusinessException("推送消息至安卓失败" + throwable.getMessage());
                }

                @Override
                public void readAll(String storeGuid, Integer messageType) {
                    logger.error(HYSTRIX_PATTERN, "readAll", storeGuid + "-" + messageType,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
