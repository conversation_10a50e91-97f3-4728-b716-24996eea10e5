package com.holderzone.holder.saas.aggregation.app.service;

import com.holderzone.saas.store.dto.takeaway.request.CouPonPreReqDTO;
import com.holderzone.saas.store.dto.takeaway.response.MtCouponPreRespDTO;

import java.util.List;

public interface GroupBuyService {

    List<MtCouponPreRespDTO> preCheck(CouPonPreReqDTO couPonPreReqDTO);

    List<MtCouponPreRespDTO> preCheckQueryItem(CouPonPreReqDTO couPonPreReqDTO);

}
