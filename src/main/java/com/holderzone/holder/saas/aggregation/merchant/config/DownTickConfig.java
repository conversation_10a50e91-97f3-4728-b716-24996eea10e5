package com.holderzone.holder.saas.aggregation.merchant.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/4/23 10:05
 * @description 赚餐小程序桌贴相关配置
 */
@RefreshScope
@Component
@Data
public class DownTickConfig {
    @Value("${applets.downTick}")
    private String downTick;
}
