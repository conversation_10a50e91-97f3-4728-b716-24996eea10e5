package com.holderzone.holder.saas.aggregation.app.service.feign.staff;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.user.ThemeReqDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @className UserService
 * @date 18-9-17 上午11:48
 * @description 服务间调用-登陆验证服务
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-staff", fallbackFactory = ProductClientService.ServiceFallBack.class)
public interface ProductClientService {

    @PostMapping(value = "/product/query_theme")
    String queryThemeCode(@RequestBody ThemeReqDTO themeReqDTO);

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<ProductClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ProductClientService create(Throwable cause) {
            return new ProductClientService() {
                @Override
                public String queryThemeCode(ThemeReqDTO themeReqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryThemeCode",
                            JacksonUtils.writeValueAsString(themeReqDTO), ThrowableUtils.asString(cause));
                    throw new BusinessException("查询主题失败");
                }
            };
        }
    }
}
