package com.holderzone.holder.saas.aggregation.app.service;

import com.holderzone.holder.saas.member.terminal.dto.volume.RequestCheckVolumeRedeem;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseCheckVolumeRedeem;
import com.holderzone.holder.saas.member.terminal.dto.volume.ResponseQueryVolumeDTO;
import com.holderzone.saas.store.dto.order.request.member.RequestQueryVolumeDTO;

public interface VolumeService {

    ResponseQueryVolumeDTO queryMemberVolumeList(RequestQueryVolumeDTO request);



    ResponseCheckVolumeRedeem checkVolumeRedeem(RequestCheckVolumeRedeem request);

}


