package com.holderzone.holder.saas.aggregation.merchant.controller.takeout;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutConsumerService;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutProducerService;
import com.holderzone.saas.store.dto.takeaway.TcdCallbackResponse;
import com.holderzone.saas.store.dto.takeaway.TcdSyncItemMappingDTO;
import com.holderzone.saas.store.dto.takeaway.request.TakeoutTcdOrderReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/takeout/callback/tcd")
@Api(description = "赚餐外卖回调接口")
public class TcdController {

    private final TakeoutProducerService takeoutProducerService;

    private final TakeoutConsumerService takeoutConsumerService;

    @Autowired
    public TcdController(TakeoutProducerService takeoutProducerService, TakeoutConsumerService takeoutConsumerService) {
        this.takeoutProducerService = takeoutProducerService;
        this.takeoutConsumerService = takeoutConsumerService;
    }

    @PostMapping(value = "/order")
    @ApiOperation(value = "赚餐外卖订单推送", notes = "赚餐外卖订单推送")
    @EFKOperationLogAop(
            PLATFORM = Platform.MERCHANTBACK,
            description = "赚餐外卖订单推送",
            moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY)
    public TcdCallbackResponse orderCallback(@RequestBody TakeoutTcdOrderReqDTO takeoutTcdOrderReqDTO) {
        log.info("聚合层赚餐外卖订单");
        if (log.isInfoEnabled()) {
            log.info("赚餐订单回调入参：{}", JacksonUtils.writeValueAsString(takeoutTcdOrderReqDTO));
        }
        return takeoutProducerService.tcdOrderCallback(takeoutTcdOrderReqDTO);
    }

    @PostMapping(value = "/item/sync")
    @ApiOperation(value = "赚餐同步门店商品", notes = "赚餐同步门店商品")
    @EFKOperationLogAop(
            PLATFORM = Platform.MERCHANTBACK,
            description = "赚餐同步门店商品",
            moduleName = ModuleNameType.HOLDER_SAAS_STORE_TAKEAWAY)
    public Result<Boolean> syncTcdItemMappingCount(@RequestBody TcdSyncItemMappingDTO tcdSyncItemMappingDTO) {
        log.info("赚餐同步门店商品入参：{}", JacksonUtils.writeValueAsString(tcdSyncItemMappingDTO));
        takeoutConsumerService.syncTcdItemMappingCount(tcdSyncItemMappingDTO);
        return Result.buildEmptySuccess();
    }
}
