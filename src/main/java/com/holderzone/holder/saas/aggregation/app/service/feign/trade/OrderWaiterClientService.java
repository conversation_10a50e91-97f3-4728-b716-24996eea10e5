package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.google.common.collect.Lists;
import com.holderzone.saas.store.dto.order.request.waiter.OrderWaiterInfoDTO;
import com.holderzone.saas.store.dto.order.request.waiter.OrderWaiterReqDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR> R
 * @date 2020/11/19 14:58
 * @description
 */
@Component
@FeignClient(name = "holder-saas-store-trade", fallbackFactory = OrderWaiterClientService.OrderWaiterFallBack.class)
public interface OrderWaiterClientService {

    @ApiOperation(value = "录入服务员接口", notes = "录入服务员接口")
    @PostMapping("/order_waiter/add_order_waiter")
    Boolean addOrderWaiter(@RequestBody OrderWaiterReqDTO orderWaiterReqDTO);

    @ApiOperation(value = "订单查询服务员接口", notes = "订单查询服务员接口")
    @GetMapping("/order_waiter/get_order_waiter/{orderGuid}")
    List<OrderWaiterInfoDTO> getOrderWaiter(@PathVariable("orderGuid") String orderGuid);

    @Component
    @Slf4j
    class OrderWaiterFallBack implements FallbackFactory<OrderWaiterClientService> {
        private static final Logger logger = LoggerFactory.getLogger(OrderWaiterClientService.OrderWaiterFallBack.class);

        @Override
        public OrderWaiterClientService create(Throwable throwable) {
            return new OrderWaiterClientService() {


                @Override
                public Boolean addOrderWaiter(OrderWaiterReqDTO orderWaiterReqDTO) {
                    log.error("录入服务员接口FallBack，throwable={}", throwable.getMessage());
                    return Boolean.FALSE;
                }

                @Override
                public List<OrderWaiterInfoDTO> getOrderWaiter(String orderGuid) {
                    log.error("订单查询服务员接口FallBack，throwable={}", throwable.getMessage());
                    return Lists.newArrayList();
                }
            };
        }
    }
}
