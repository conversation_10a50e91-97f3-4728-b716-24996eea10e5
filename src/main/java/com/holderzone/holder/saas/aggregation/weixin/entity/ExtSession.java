package com.holderzone.holder.saas.aggregation.weixin.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.websocket.Session;

/**
 * @description 
 * <AUTHOR>
 * @version 1.0
 * @className ExtSession
 * @date 2019/5/13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtSession {
	private Session session;
	private long lastTime;
	private short resetTime;
	private String tableGuid;
	private String openID;

	public ExtSession(Session session) {
		this.session=session;
		this.lastTime=System.currentTimeMillis();
		this.resetTime=0;
	}

	public void refreshTime(){
		this.lastTime=System.currentTimeMillis();
		this.resetTime=0;
	}

	public Session getSession() {
		refreshTime();
		return session;
	}

	public boolean checkAndClose(){
		if(resetTime>=3){
			return true;
		}else if(System.currentTimeMillis()-lastTime>5*10000){
			this.resetTime++;
			lastTime=System.currentTimeMillis();
			return false;
		}
		return false;
	}
}
