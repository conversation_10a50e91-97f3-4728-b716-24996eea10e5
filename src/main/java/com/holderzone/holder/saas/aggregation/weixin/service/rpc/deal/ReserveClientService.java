package com.holderzone.holder.saas.aggregation.weixin.service.rpc.deal;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.reserve.api.dto.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "holder-saas-store-reserve", fallbackFactory = ReserveClientService.ReserveClientServiceFallback.class)
public interface ReserveClientService {

    /**
     * 查询可预订的门店列表
     */
    @PostMapping("/reserve/applet/launch")
    List<ReserveAvailableStoreDTO> getAvailableStoreList(@RequestBody ReserveAppletQueryDTO queryDTO);

    /**
     * 查询可预订的门店详情
     */
    @PostMapping("/reserve/applet/available_store")
    ReserveAvailableStoreConfigDTO getAvailableStore(@RequestBody ReserveAppletQueryDTO queryDTO);

    /**
     * 发起预定
     */
    @PostMapping("/reserve/applet/launch")
    String launch(@Valid @RequestBody ReserveRecordDTO reserveRecordDTO);

    @Slf4j
    @Component
    class ReserveClientServiceFallback implements FallbackFactory<ReserveClientService> {

        @Override
        public ReserveClientService create(Throwable throwable) {
            return new ReserveClientService() {

                @Override
                public List<ReserveAvailableStoreDTO> getAvailableStoreList(ReserveAppletQueryDTO queryDTO) {
                    log.error("reserve get available store list fail with param: {}", JacksonUtils.writeValueAsString(queryDTO));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ReserveAvailableStoreConfigDTO getAvailableStore(ReserveAppletQueryDTO queryDTO) {
                    log.error("reserve get available store fail with param: {}", JacksonUtils.writeValueAsString(queryDTO));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public String launch(ReserveRecordDTO reserveRecordDTO) {
                    log.error("reserve launch fail with param: {}", reserveRecordDTO);
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
