package com.holderzone.holder.saas.aggregation.merchant.controller.member.account;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.member.HsmMemberCouponClientService;
import com.holderzone.holder.saas.member.dto.account.request.MemberCouponQueryReqDTO;
import com.holderzone.holder.saas.member.dto.account.response.MemberCouponDetailRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberCouponController
 * @date 2019/05/30 15:02
 * @description 会员持劵
 * @program holder-saas-member-account
 */
@RestController
@Api(description = "会员持券相关操作")
@RequestMapping(value = "/hsm_member_coupon")
public class HsmMemberCouponController {

    @Resource
    private HsmMemberCouponClientService hsmMemberCouponClientService;


    /**
     * 分页查询用户的优惠券信息
     *
     * @param queryReqDTO 查询条件
     * @return 查询结果
     */
    @PostMapping(value = "/listByCondition", produces = "application/json;charset=utf-8")
    @ApiOperation("分页查询用户的优惠券信息")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER,description = "分页查询用户的优惠券信息")
    public Result<MemberCouponDetailRespDTO> listByCondition(
            @Validated @RequestBody MemberCouponQueryReqDTO queryReqDTO) {
        return Result.buildSuccessResult(hsmMemberCouponClientService.listByCondition(queryReqDTO));
    }

}

