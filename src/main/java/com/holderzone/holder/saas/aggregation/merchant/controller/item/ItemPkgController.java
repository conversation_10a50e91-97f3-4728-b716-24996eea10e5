package com.holderzone.holder.saas.aggregation.merchant.controller.item;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.constant.Constants;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.item.ItemClientService;
import com.holderzone.saas.store.dto.item.common.ItemSingleDTO;
import com.holderzone.saas.store.dto.item.req.ItemGroupMealSaveReqDTO;
import com.holderzone.saas.store.dto.item.req.ItemPkgSaveReqDTO;
import com.holderzone.saas.store.dto.item.resp.TypeSkuRespDTO;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import static com.holderzone.holder.saas.aggregation.merchant.constant.Constants.DISTRIBUTE_FAIL;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ItemController
 * @date 2018/09/12 上午9:53
 * @description //TODO
 * @program holder-saas-store-dish
 */
@RestController
@RequestMapping("/item_pkg")
@Api(description = "商品套餐相关接口")
@ResponseBody
public class ItemPkgController {
    private static final Logger logger = LoggerFactory.getLogger(ItemPkgController.class);

    @Autowired
    private ItemClientService itemClientService;

    @ApiOperation(value = "门店套餐获取商品列表接口",notes = "必填参数：data:当前新增套餐所属门店或品牌GUID")
    @PostMapping("/store/select_sku_list")
    @ResponseBody
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "门店套餐获取商品列表接口",action = OperatorType.SELECT)
    public Result<List<TypeSkuRespDTO>> selectSkuListForPkg(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("套餐获取商品列表接口查询入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(0);
        List<TypeSkuRespDTO> typeSkuRespDTOList = itemClientService.selectSkuListForPkg(itemSingleDTO);
        return Result.buildSuccessResult(typeSkuRespDTOList);
    }

    @ApiOperation(value = "品牌套餐获取商品列表接口",notes = "必填参数：data:当前新增套餐所属门店或品牌GUID")
    @PostMapping("/brand/select_sku_list")
    @ResponseBody@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "品牌套餐获取商品列表接口",action = OperatorType.SELECT)
    public Result<List<TypeSkuRespDTO>> selectSkuListForPkgFromBrand(@RequestBody ItemSingleDTO itemSingleDTO) {
        logger.info("套餐获取商品列表接口查询入参,itemSingleDTO={}", JacksonUtils.writeValueAsString(itemSingleDTO));
        itemSingleDTO.setFrom(1);
        List<TypeSkuRespDTO> typeSkuRespDTOList = itemClientService.selectSkuListForPkg(itemSingleDTO);
        return Result.buildSuccessResult(typeSkuRespDTOList);
    }

    @ApiOperation(value = "门店套餐新增接口")
    @PostMapping("/store/save_pkg")
    @ResponseBody@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "门店套餐新增接口",action = OperatorType.ADD)
    public Result savePkg(@RequestBody @Valid ItemPkgSaveReqDTO itemPkgSaveReqDTO) {
        logger.info("套餐新增接口查询入参,itemPkgSaveReqDTO={}", JacksonUtils.writeValueAsString(itemPkgSaveReqDTO));
        itemPkgSaveReqDTO.setFrom(0);
        Integer save = itemClientService.savePkg(itemPkgSaveReqDTO);
        if (Integer.valueOf(1).equals(save)){
            return Result.buildEmptySuccess();
        }else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
        }
    }

    @ApiOperation(value = "品牌套餐新增接口")
    @PostMapping("/brand/save_pkg")
    @ResponseBody@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "品牌套餐新增接口",action = OperatorType.ADD)
    public Result savePkgFromBrand(@RequestBody @Valid ItemPkgSaveReqDTO itemPkgSaveReqDTO) {
        logger.info("套餐新增接口查询入参,itemPkgSaveReqDTO={}", JacksonUtils.writeValueAsString(itemPkgSaveReqDTO));
        itemPkgSaveReqDTO.setFrom(1);
        Integer save = itemClientService.savePkg(itemPkgSaveReqDTO);
        return dishtributeReturn(save);
    }

    @ApiOperation(value = "门店套餐更新接口")
    @PostMapping("/store/update_pkg")
    @ResponseBody@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "门店套餐更新接口")
    public Result updatePkg(@RequestBody @Valid ItemPkgSaveReqDTO itemPkgSaveReqDTO) {
        logger.info("套餐update接口查询入参,itemPkgSaveReqDTO={}", JacksonUtils.writeValueAsString(itemPkgSaveReqDTO));
        itemPkgSaveReqDTO.setFrom(0);
        Integer update = itemClientService.updatePkg(itemPkgSaveReqDTO);
        if (Integer.valueOf(1).equals(update)){
            return Result.buildEmptySuccess();
        }else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.RESULT_MODIFICATION_FAILED));
        }
    }

    @ApiOperation(value = "品牌套餐更新接口")
    @PostMapping("/brand/update_pkg")
    @ResponseBody@EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_ITEM,description = "品牌套餐更新接口")
    public Result updatePkgFromBrand(@RequestBody @Valid ItemPkgSaveReqDTO itemPkgSaveReqDTO) {
        logger.info("套餐update接口查询入参,itemPkgSaveReqDTO={}", JacksonUtils.writeValueAsString(itemPkgSaveReqDTO));
        itemPkgSaveReqDTO.setFrom(1);
        Integer update = itemClientService.updatePkg(itemPkgSaveReqDTO);
        return dishtributeReturn(update);
    }


    @ApiOperation(value = "保存/更新团餐")
    @PostMapping("/save_group_meal_pkg")
    public Result saveOrUpdateGroupMealPkg(@RequestBody @Valid ItemGroupMealSaveReqDTO request){
        logger.info("门店团餐save or update 接口入参，request = {}",JacksonUtils.writeValueAsString(request));
        Integer flag  = itemClientService.saveOrUpdateGroupMealPkg(request);
        return dishtributeReturn(flag);
    }

    /**
     * 带推送功能的返回
     * @param num
     * @return
     */
    private Result dishtributeReturn(Integer num) {
        if (Integer.valueOf(3).equals(num)){
            return Result.buildSuccessMsg(DISTRIBUTE_FAIL);
        }
        if (Integer.valueOf(1).equals(num)){
            return Result.buildEmptySuccess();
        }else {
            return Result.buildOpFailedResult(LocaleUtil.getMessage(Constants.SAVE_FAILED));
        }
    }

}
