package com.holderzone.holder.saas.aggregation.weixin.helper;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.MemberMarketingClientService;
import com.holderzone.holder.saas.aggregation.weixin.service.rpc.account.HsaBaseClientService;
import com.holderzone.holder.saas.aggregation.weixin.utils.StringMessageUtils;
import com.holderzone.holder.saas.aggregation.weixin.utils.UserMemberSessionUtils;
import com.holderzone.holder.saas.common.enums.ActivitiePeriodTypeEnum;
import com.holderzone.holder.saas.member.terminal.dto.feign.FeignModel;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseActivitiePeriod;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseActivitiePeriodMonth;
import com.holderzone.holder.saas.member.terminal.dto.order.ResponseActivitiePeriodWeek;
import com.holderzone.holder.saas.member.wechat.dto.activitie.*;
import com.holderzone.holder.saas.member.wechat.dto.card.ResponseProductDiscount;
import com.holderzone.holder.saas.member.wechat.dto.coupon.ResponseVolumeInfo;
import com.holderzone.holder.saas.member.wechat.dto.enums.MemberRightsType;
import com.holderzone.holder.saas.member.wechat.dto.label.ResponseMemberLabel;
import com.holderzone.holder.saas.member.wechat.dto.member.RequestDishInfo;
import com.holderzone.holder.saas.member.wechat.dto.member.ResponseMemberCardListOwned;
import com.holderzone.holder.saas.weixin.entry.dto.WxMemberSessionDTO;
import com.holderzone.holder.saas.weixin.utils.RedisUtils;
import com.holderzone.holder.saas.weixin.utils.WeixinUserThreadLocal;
import com.holderzone.holder.saas.weixin.utils.WxMemberSessionUtil;
import com.holderzone.saas.store.constant.RedisKeyConstant;
import com.holderzone.saas.store.dto.marketing.UniteActivityQO;
import com.holderzone.saas.store.dto.marketing.UniteActivityVO;
import com.holderzone.saas.store.dto.marketing.nth.NthActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.nth.NthActivityItemDTO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityDetailsVO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemDTO;
import com.holderzone.saas.store.dto.marketing.specials.LimitSpecialsActivityItemVO;
import com.holderzone.saas.store.dto.weixin.deal.*;
import com.holderzone.saas.store.dto.weixin.resp.StoreActivityRespDTO;
import com.holderzone.saas.store.enums.common.BooleanEnum;
import com.holderzone.saas.store.enums.marketing.QueryTypeEnum;
import com.holderzone.saas.store.enums.marketing.RechargeGiftTypeEnum;
import com.holderzone.saas.store.enums.member.MarketActivityUnableTipEnum;
import com.holderzone.saas.store.enums.member.VolumeStoreTypeEnum;
import com.holderzone.saas.store.enums.order.DiscountTypeEnum;
import com.holderzone.saas.store.enums.weixin.ActivityTypeEnum;
import com.holderzone.saas.store.enums.weixin.MinPriceTypeEnum;
import com.holderzone.saas.store.enums.weixin.PreferentialTypeEnum;
import com.holderzone.saas.store.util.BigDecimalUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/27
 * @description 营销活动辅助类
 */
@Slf4j
@Component
@AllArgsConstructor
public class MarketingActivityHelper {

    private final MemberMarketingClientService marketingClientService;

    private final HsaBaseClientService wechatClientService;

    private final RedisUtils redisUtils;

    private final UserMemberSessionUtils userMemberSessionUtils;

    private final PriceCalculateHelper priceCalculateHelper;

    private final MemberRightHelper memberRightHelper;

    /**
     * 通过门店和token查询限时特价活动列表
     *
     * @param wxMemberSessionDTO 微信用户信息
     * @return 限时特价活动列表
     */
    public List<LimitSpecialsActivityDetailsVO> querySpecialsActivityList(WxMemberSessionDTO wxMemberSessionDTO) {
        String redisKey = String.format(RedisKeyConstant.LIMIT_SPECIALS_ACTIVITY, wxMemberSessionDTO.getStoreGuid(),
                wxMemberSessionDTO.getWxtoken());
        String limitSpecialsJson = (String) redisUtils.get(redisKey);
        log.info("[限时特价活动]门店:{},缓存={}", wxMemberSessionDTO.getStoreGuid(), limitSpecialsJson);
        if (StringUtils.isEmpty(limitSpecialsJson)) {
            List<LimitSpecialsActivityDetailsVO> specialsActivityList = querySpecialsActivity(wxMemberSessionDTO);
            redisUtils.setEx(redisKey, JacksonUtils.writeValueAsString(specialsActivityList), 30, TimeUnit.MINUTES);
            log.warn("[数据库][活动进行中过滤][最终返回活动]specialsActivityList={}", JacksonUtils.writeValueAsString(specialsActivityList));
            return specialsActivityList;
        }
        List<LimitSpecialsActivityDetailsVO> activityDetailsVOList = JacksonUtils.toObjectList(LimitSpecialsActivityDetailsVO.class, limitSpecialsJson);
        LocalDateTime now = LocalDateTime.now();
        activityDetailsVOList.removeIf(a -> now.isBefore(a.getStartTime()) || now.isAfter(a.getEndTime()));
        log.warn("[缓存][活动进行中过滤][最终返回活动]activityDetailsVOList={}", JacksonUtils.writeValueAsString(activityDetailsVOList));
        return activityDetailsVOList;
    }

    /**
     * 第N份优惠活动
     */
    public List<NthActivityDetailsVO> queryNthActivityList(WxMemberSessionDTO wxMemberSessionDTO) {
        String redisKey = String.format(RedisKeyConstant.NTH_ACTIVITY, wxMemberSessionDTO.getStoreGuid(),
                wxMemberSessionDTO.getWxtoken());
        String nthActivityJson = (String) redisUtils.get(redisKey);
        log.info("[第N份优惠活动]门店={},缓存={}", wxMemberSessionDTO.getStoreGuid(), nthActivityJson);
        if (StringUtils.isEmpty(nthActivityJson)) {
            List<NthActivityDetailsVO> nthActivityList = queryNthActivity(wxMemberSessionDTO);
            redisUtils.setEx(redisKey, JacksonUtils.writeValueAsString(nthActivityList), 30, TimeUnit.MINUTES);
            return nthActivityList;
        }
        List<NthActivityDetailsVO> activityDetailsVOList = JacksonUtils.toObjectList(NthActivityDetailsVO.class, nthActivityJson);
        LocalDateTime now = LocalDateTime.now();
        activityDetailsVOList.removeIf(a -> now.isBefore(a.getStartTime()) || now.isAfter(a.getEndTime()));
        log.warn("[活动进行中过滤]最终返回活动activityDetailsVOList={}", JacksonUtils.writeValueAsString(activityDetailsVOList));
        return activityDetailsVOList;
    }

    /**
     * 查询统一接口
     *
     * @param wxMemberSessionDTO 微信用户信息
     * @return 限时特价活动信息
     */
    private List<LimitSpecialsActivityDetailsVO> querySpecialsActivity(WxMemberSessionDTO wxMemberSessionDTO) {
        FeignModel<UniteActivityVO> feignModel = queryUniteActivityVO(wxMemberSessionDTO, QueryTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode());
        if (!ObjectUtils.isEmpty(feignModel) && !ObjectUtils.isEmpty(feignModel.getData())) {
            List<LimitSpecialsActivityDetailsVO> limitSpecialsList = feignModel.getData().getLimitSpecialsList();
            // 会员校验
            checkMember(wxMemberSessionDTO.getMemberInfoGuid(), limitSpecialsList);
            return limitSpecialsList;
        }
        return Lists.newArrayList();
    }

    /**
     * 查询营销活动统一接口
     *
     * @param wxMemberSessionDTO 微信用户信息
     * @param queryType          QueryTypeEnum
     * @return 活动信息
     */
    private FeignModel<UniteActivityVO> queryUniteActivityVO(WxMemberSessionDTO wxMemberSessionDTO, int queryType) {
        UniteActivityQO uniteActivityQO = new UniteActivityQO();
        uniteActivityQO.setOperSubjectGuid(wxMemberSessionDTO.getOperSubjectGuid());
        uniteActivityQO.setQueryType(queryType);
        uniteActivityQO.setStoreGuid(wxMemberSessionDTO.getStoreGuid());
        uniteActivityQO.setOrderState(wxMemberSessionDTO.getOrderState());
        log.info("[营销活动统一查询]uniteActivityQO={}", JacksonUtils.writeValueAsString(uniteActivityQO));
        FeignModel<UniteActivityVO> feignModel = marketingClientService.query(uniteActivityQO);
        log.info("[营销活动统一查询]feignModel={}", JacksonUtils.writeValueAsString(feignModel));
        return feignModel;
    }

    /**
     * 第N份优惠活动
     */
    private List<NthActivityDetailsVO> queryNthActivity(WxMemberSessionDTO wxMemberSessionDTO) {
        FeignModel<UniteActivityVO> feignModel = queryUniteActivityVO(wxMemberSessionDTO, QueryTypeEnum.NTH_ACTIVITY.getCode());
        if (!ObjectUtils.isEmpty(feignModel) && !ObjectUtils.isEmpty(feignModel.getData())) {
            List<NthActivityDetailsVO> nthActivityList = feignModel.getData().getNthActivityList();
            // 会员校验
            if (!com.holderzone.framework.util.StringUtils.hasText(wxMemberSessionDTO.getMemberInfoGuid())) {
                // 未注册
                nthActivityList.removeIf(a -> !Objects.equals(RechargeGiftTypeEnum.RECHARGE_UNRESTRICTED.getCode(), a.getGroupType()));
            }
            return nthActivityList;
        }
        return Lists.newArrayList();
    }

    /**
     * 会员校验
     */
    private void checkMember(String memberInfoGuid,
                             List<LimitSpecialsActivityDetailsVO> limitSpecialsList) {
        LocalDateTime now = LocalDateTime.now();
        limitSpecialsList.removeIf(a -> now.isBefore(a.getStartTime()) || now.isAfter(a.getEndTime()));
        if (CollectionUtils.isEmpty(limitSpecialsList)) {
            log.warn("[活动进行中过滤]没有活动处于进行中");
            return;
        }
        if (com.holderzone.framework.util.StringUtils.hasText(memberInfoGuid)) {
            // 指定会员
            limitSpecialsList.removeIf(a ->
                    Objects.equals(RechargeGiftTypeEnum.RECHARGE_LABEL_MEMBER.getCode(), a.getGroupType())
                            && !ObjectUtils.isEmpty(a.getMemberDTO())
                            && !a.getMemberDTO().getGuidList().contains(memberInfoGuid)
            );
            if (CollectionUtils.isEmpty(limitSpecialsList)) {
                log.warn("[指定会员]过滤完成");
                return;
            }

            // 会员等级,产品说只看处理主卡
            ResponseMemberCardListOwned defaultCard = wechatClientService.getDefaultCard(memberInfoGuid).getData();
            if (!ObjectUtils.isEmpty(defaultCard)) {
                String cardLevelGuid = defaultCard.getCardLevelGuid();
                limitSpecialsList.removeIf(a ->
                        Objects.equals(RechargeGiftTypeEnum.RECHARGE_LABEL_MEMBER.getCode(), a.getGroupType())
                                && !ObjectUtils.isEmpty(a.getGradeDTO())
                                && !a.getGradeDTO().getGuidList().contains(cardLevelGuid)
                );
                if (org.springframework.util.CollectionUtils.isEmpty(limitSpecialsList)) {
                    log.warn("[会员等级]过滤完成");
                    return;
                }
            }

            // 会员标签
            checkMemberLabel(limitSpecialsList, memberInfoGuid);
        } else {
            // 未注册
            limitSpecialsList.removeIf(a -> !Objects.equals(RechargeGiftTypeEnum.RECHARGE_UNRESTRICTED.getCode(), a.getGroupType()));
        }
    }

    /**
     * 校验会员标签
     *
     * @param limitSpecialsList 限时特价活动
     * @param memberInfoGuid    会员id
     */
    private void checkMemberLabel(List<LimitSpecialsActivityDetailsVO> limitSpecialsList, String memberInfoGuid) {
        List<ResponseMemberLabel> labelList = wechatClientService.getMemberInfoLabel(memberInfoGuid).getData();
        if (!org.springframework.util.CollectionUtils.isEmpty(labelList)) {
            List<String> labelGuidList = labelList.stream()
                    .map(ResponseMemberLabel::getLabelGuid)
                    .distinct()
                    .collect(Collectors.toList());
            limitSpecialsList.removeIf(a -> {
                if (Objects.equals(RechargeGiftTypeEnum.RECHARGE_LABEL_MEMBER.getCode(), a.getGroupType())
                        && !ObjectUtils.isEmpty(a.getLabelDTO())) {
                    labelGuidList.retainAll(a.getLabelDTO().getGuidList());
                    return labelGuidList.isEmpty();
                }
                return false;
            });
        }
    }

    /**
     * 查询选中的活动
     *
     * @param activitySelectList 活动选择列表
     * @return 选中的活动详情
     */
    public LimitSpecialsActivityDetailsVO querySelectSpecialsActivityDetailsVO(List<ActivitySelectDTO> activitySelectList) {
        // 查询限时特价活动
        List<LimitSpecialsActivityDetailsVO> specialsActivityList = querySpecialsActivityList(WeixinUserThreadLocal.get());

        // 选中的活动信息
        String selectActivityGuid = activitySelectList.stream()
                .filter(s -> Objects.equals(DiscountTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode(), s.getActivityType()))
                .map(ActivitySelectDTO::getActivityGuid)
                .findFirst()
                .orElse(null);
        return specialsActivityList.stream()
                .filter(a -> Objects.equals(selectActivityGuid, a.getGuid()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 查询会员权益，优先缓存，没有查库
     *
     * @param wxToken 微信Token
     * @return 查询会员权益
     */
    public ResponseProductDiscount queryMemberRights(String wxToken) {
        return memberRightHelper.queryMemberRights(wxToken);
    }

    /**
     * 计算满减满折
     *
     * @param req 前置计算参数
     * @return 计算结果
     */
    public ResponseMarketActivityUse calculateFullDiscountAndReduction(RequestMarketActivityUse req) {
        // 商品列表
        List<RequestDishInfo> dishInfoDTOList = req.getDishInfoDTOList();

        // 优惠计算返回信息
        ResponseMarketActivityUse respDTO = new ResponseMarketActivityUse();
        respDTO.setDishInfoDTOList(dishInfoDTOList);
        respDTO.setActivityGuid(req.getActivityGuid());
        respDTO.setDiscountMoney(BigDecimal.ZERO);

        // 查询当前门店营销活动列表
        List<ResponseClientMarketActivity> marketActivityList = queryFullDiscountAndReductionActivityList(req, WeixinUserThreadLocal.get());
        if (CollectionUtils.isEmpty(marketActivityList)) {
            log.warn("[该门店该订单没有命中活动]req={}", JacksonUtils.writeValueAsString(req));
            respDTO.setActivitiesList(Lists.newArrayList());
            return respDTO;
        }
        respDTO.setActivitiesList(marketActivityList);

        // 计算每个活动的优惠金额
        calculateActivityDiscountPrice(req, respDTO);

        // 商品为空 直接返回
        if (CollectionUtils.isEmpty(dishInfoDTOList)) {
            return respDTO;
        }

        // 活动guid为空表示未选择活动
        if (StringUtils.isEmpty(req.getActivityGuid())) {
            respDTO.setActivityGuid(null);
            return respDTO;
        }

        // 选中活动
        ResponseClientMarketActivity marketActivity = marketActivityList.stream()
                .filter(a -> Objects.equals(a.getGuid(), req.getActivityGuid()))
                .findFirst()
                .orElse(null);
        if (ObjectUtils.isEmpty(marketActivity)) {
            log.warn("[选中活动不存在]activityGuid={}", req.getActivityGuid());
            return respDTO;
        }

        // 校验活动是否共享
        if (isMutuallyExclusive(marketActivity, req)) {
            log.warn("[活动不共享,activityGuid清空], activityGuid={}", req.getActivityGuid());
            respDTO.setActivityGuid(null);
            return respDTO;
        }

        // 计算商品最终优惠价
        priceCalculateHelper.calculateItemDiscountPrice(marketActivity, respDTO);
        return respDTO;
    }

    /**
     * 计算每个活动的优惠金额
     */
    private void calculateActivityDiscountPrice(RequestMarketActivityUse req,
                                                ResponseMarketActivityUse respDTO) {
        List<ResponseClientMarketActivity> marketActivityList = respDTO.getActivitiesList();
        if (CollectionUtils.isEmpty(marketActivityList)) {
            log.warn("营销活动列表为空");
            return;
        }

        // 计算每个活动的优惠金额
        for (ResponseClientMarketActivity activity : marketActivityList) {
            // 校验活动是否互斥
            if (isMutuallyExclusive(activity, req)) {
                log.warn("[活动不共享]activityGuid={}", activity.getGuid());
                activity.setUseAble(false);
                activity.setUnUseReason(MarketActivityUnableTipEnum.OTHER_DISCOUNT.getTips());
                continue;
            }

            // 计算优惠价
            priceCalculateHelper.calculatePreferentialPrice(activity, respDTO);
        }
    }

    /**
     * 查询满减满折活动
     *
     * @param req 会员卡id
     * @return 满减满折活动
     */
    public List<ResponseClientMarketActivity> queryFullDiscountAndReductionActivityList(RequestMarketActivityUse req,
                                                                                        WxMemberSessionDTO wxMemberSessionDTO) {
        String redisKey = String.format(RedisKeyConstant.FULL_DISCOUNT_AND_REDUCTION_ACTIVITY_LIST,
                wxMemberSessionDTO.getStoreGuid(), wxMemberSessionDTO.getWxtoken());
        String marketActivityJson = (String) redisUtils.get(redisKey);
        log.info("[满减满折活动]门店:{},redisKey={},缓存={}", wxMemberSessionDTO.getStoreGuid(), redisKey, marketActivityJson);
        if (StringUtils.isEmpty(marketActivityJson)) {
            RequestMarketActivityUse activityUse = new RequestMarketActivityUse();
            activityUse.setMemberInfoCardGuid(req.getMemberInfoCardGuid());
            List<ResponseClientMarketActivity> marketActivityList = wechatClientService.queryMarket(activityUse).getData();
            redisUtils.setEx(redisKey, JacksonUtils.writeValueAsString(marketActivityList), 30, TimeUnit.MINUTES);
            return marketActivityList;
        }
        return JacksonUtils.toObjectList(ResponseClientMarketActivity.class, marketActivityJson);
    }

    /**
     * 判断活动与其它优惠是否互斥
     */
    private boolean isMutuallyExclusive(ResponseClientMarketActivity activity, RequestMarketActivityUse reqDTO) {
        String volumeGuid = reqDTO.getVolumeGuid();
        if (StringUtils.isNotEmpty(volumeGuid)) {
            // 判断优惠券是否互斥
            ResponseVolumeInfo volumeInfo = wechatClientService.queryUseConditions(volumeGuid).getData();
            // 优惠券勾选了互斥时，isUseAlone=1
            boolean volumeUseAlone = Optional.ofNullable(volumeInfo.getIsUseAlone()).orElse(0).equals(1);
            if (volumeUseAlone) {
                // 如果优惠券勾选了互斥，不管营销活动是否互斥，都不能共享
                return true;
            }
            // 该活动是否共享 0才是共享
            return !activity.getIsShare().equals(0);
        }
        if (Objects.nonNull(reqDTO.getIsMemberDiscount()) && Boolean.TRUE.equals(reqDTO.getIsMemberDiscount())) {
            // 该活动是否共享 0才是共享
            return !activity.getIsShare().equals(0);
        }
        return false;
    }

    /**
     * 商品列表增加限时特价活动信息
     *
     * @param activityItemVOList 限时特价活动
     * @param itemInfoDTOS       商品列表
     */
    public static void buildLimitSpecialsActivity(List<LimitSpecialsActivityItemVO> activityItemVOList,
                                                  List<ItemInfoDTO> itemInfoDTOS) {
        if (CollectionUtils.isEmpty(activityItemVOList)) {
            log.warn("限时特价活动为空");
            return;
        }
        Map<String, LimitSpecialsActivityItemVO> activityItemVOMap = activityItemVOList.stream()
                .collect(Collectors.toMap(LimitSpecialsActivityItemVO::getCommodityId, Function.identity(), (v1, v2) -> v1));
        itemInfoDTOS.forEach(itemInfoDTO -> {
            String itemMapKey = com.holderzone.framework.util.StringUtils.hasText(itemInfoDTO.getParentGuid()) ?
                    itemInfoDTO.getParentGuid() : itemInfoDTO.getItemGuid();
            LimitSpecialsActivityItemVO activityItemVO = activityItemVOMap.get(itemMapKey);
            if (ObjectUtils.isEmpty(activityItemVO)) {
                log.warn("[商品没有特价活动]ItemGuid={}", itemInfoDTO.getItemGuid());
                return;
            }
            // 返回给前端计算的字段
            ItemMarketingActivityDTO activityDTO = new ItemMarketingActivityDTO();
            activityDTO.setSpecialsType(activityItemVO.getSpecialsType());
            activityDTO.setSpecialsNumber(activityItemVO.getSpecialsNumber());
            activityDTO.setLimitNumber(activityItemVO.getLimitNumber());
            itemInfoDTO.setMarketingActivityDTO(activityDTO);
            itemInfoDTO.getPreferentialTypeList().add(PreferentialTypeEnum.LIMITED_TIME_SPECIAL.getCode());
            BigDecimal specialsPrice = PriceCalculateHelper.getSpecialsPrice(itemInfoDTO, activityItemVO, false);
            if (BigDecimalUtil.lessThan(specialsPrice, itemInfoDTO.getMinPrice())) {
                itemInfoDTO.setMinPrice(specialsPrice);
                itemInfoDTO.setMinPriceType(MinPriceTypeEnum.LIMITED_SPECIAL_ACTIVITY.getCode());
                List<ActivityRuleDescDTO> activityRuleDescList = itemInfoDTO.getActivityRuleDescList();
                ActivityRuleDescDTO descDTO = new ActivityRuleDescDTO();
                descDTO.setActivityType(ActivityTypeEnum.LIMIT_SPECIALS_ACTIVITY.getCode());
                String activityRule = getActivityRule(itemInfoDTO.getOriginalPrice(), specialsPrice, activityItemVO.getLimitNumber());
                descDTO.setActivityRuleDesc(activityRule);
                descDTO.setActivityGuid(activityItemVO.getActivityGuid());
                descDTO.setRelationRule(activityItemVO.getRelationRule());
                activityRuleDescList.add(descDTO);
            }
        });
    }

    public static String getActivityRule(BigDecimal originalPrice,
                                         BigDecimal specialsPrice,
                                         Integer limitNumber) {
        BigDecimal divide = calculateSpecialsDiscount(specialsPrice, originalPrice);
        String activityRule = "限时特价";
        // 折扣大于0（即限时特价大于0）小于0.01，不显示折扣
        if (BigDecimalUtil.lessThan(divide, BigDecimal.valueOf(0.1)) && BigDecimalUtil.greaterThanZero(specialsPrice)) {
            if (Objects.nonNull(limitNumber)) {
                activityRule = activityRule + String.format(" | 限%s份", limitNumber);
            }
            return activityRule;
        }
        activityRule = activityRule + String.format("%s折", divide.stripTrailingZeros().toPlainString());
        if (Objects.nonNull(limitNumber)) {
            activityRule = activityRule + String.format(" | 限%s份", limitNumber);
        }
        return activityRule;
    }

    /**
     * 计算折扣力度
     *
     * @param specialsPrice 特价
     * @param originalPrice 原价
     * @return 折扣
     */
    @NotNull
    public static BigDecimal calculateSpecialsDiscount(BigDecimal specialsPrice,
                                                       BigDecimal originalPrice) {
        BigDecimal divide = specialsPrice.divide(originalPrice, 4, RoundingMode.DOWN);
        return divide.multiply(BigDecimal.TEN).setScale(2, RoundingMode.DOWN);
    }

    /**
     * 商品列表增加第N份优惠活动信息
     *
     * @param nthActivityList 第N份优惠
     * @param itemInfoDTOS    商品列表
     */
    public static void buildNthActivity(List<NthActivityDetailsVO> nthActivityList,
                                        List<ItemInfoDTO> itemInfoDTOS) {
        if (CollectionUtils.isEmpty(nthActivityList)) {
            log.warn("第N份优惠活动为空");
            return;
        }
        List<NthActivityItemDTO> activityItemVOList = nthActivityList.stream()
                .flatMap(activity -> activity.getItemDTOList().stream())
                .collect(Collectors.toList());
        Map<String, NthActivityDetailsVO> nthActivityDetailsVOMap = nthActivityList.stream()
                .collect(Collectors.toMap(NthActivityDetailsVO::getGuid, Function.identity(), (v1, v2) -> v1));
        Map<String, List<NthActivityItemDTO>> activityItemVOMap = activityItemVOList.stream()
                .collect(Collectors.groupingBy(NthActivityItemDTO::getCommodityId));
        itemInfoDTOS.forEach(itemInfoDTO -> {
            List<NthActivityItemDTO> nthActivityItemDTOList = activityItemVOMap.get(itemInfoDTO.getItemGuid());
            if (CollectionUtils.isEmpty(nthActivityItemDTOList)) {
                nthActivityItemDTOList = activityItemVOMap.get(itemInfoDTO.getParentGuid());
            }
            if (CollectionUtils.isEmpty(nthActivityItemDTOList)) {
                log.warn("[商品没有第N份优惠活动]ItemGuid={}", itemInfoDTO.getItemGuid());
                return;
            }
            nthActivityItemDTOList.forEach(nthActivityItemDTO -> {
                NthActivityDetailsVO nthActivity = nthActivityDetailsVOMap.get(nthActivityItemDTO.getActivityGuid());
                if (ObjectUtils.isEmpty(nthActivity)) {
                    log.warn("[第N份优惠活动][数据异常]activityGuid={}", nthActivityItemDTO.getActivityGuid());
                }
                List<ActivityRuleDescDTO> activityRuleDescList = itemInfoDTO.getActivityRuleDescList();
                ActivityRuleDescDTO descDTO = new ActivityRuleDescDTO();
                descDTO.setActivityType(ActivityTypeEnum.NTH_ACTIVITY.getCode());
                String activityDesc = buildActivityDesc(nthActivity);
                descDTO.setActivityRuleDesc(activityDesc);
                descDTO.setActivityGuid(nthActivityItemDTO.getActivityGuid());
                descDTO.setRelationRule(nthActivity.getRelationRule());
                activityRuleDescList.add(descDTO);
            });
        });
    }

    public static String buildActivityDesc(NthActivityDetailsVO nthActivity) {
        String activityDesc;
        if (BigDecimalUtil.equelZero(nthActivity.getPerDiscount())) {
            // 第3件免单
            activityDesc = String.format("第%s件免单", nthActivity.getPerCount());
        } else {
            // 第2件8.8折
            activityDesc = String.format("第%s件%s折", nthActivity.getPerCount(),
                    nthActivity.getPerDiscount().stripTrailingZeros().toPlainString());
        }
        return activityDesc;
    }

    public static void nthFilterItem(List<String> itemGuidList,
                                     List<NthActivityDetailsVO> nthActivityList,
                                     Integer nthFlag) {
        if (CollectionUtils.isEmpty(nthActivityList)) {
            log.warn("[第N份优惠活动]活动商品过滤前为空");
            return;
        }
        if (CollectionUtils.isEmpty(itemGuidList)) {
            nthActivityList.clear();
            log.warn("[第N份优惠活动]门店商品为空");
            return;
        }
        List<String> toDeleteActivity = new ArrayList<>();
        nthActivityList.forEach(activity -> {
            List<String> commodityIdList = activity.getItemDTOList().stream()
                    .map(NthActivityItemDTO::getCommodityId)
                    .distinct()
                    .collect(Collectors.toList());
            commodityIdList.retainAll(itemGuidList);
            if (CollectionUtils.isEmpty(commodityIdList)) {
                toDeleteActivity.add(activity.getGuid());
            }
        });
        if (!CollectionUtils.isEmpty(toDeleteActivity)) {
            nthActivityList.removeIf(a -> toDeleteActivity.contains(a.getGuid()));
        }
        if (BooleanEnum.FALSE.getCode() == nthFlag) {
            nthActivityList.clear();
            log.warn("不展示第N份优惠活动");
        }
    }

    public static void addNthActivityList(List<NthActivityDetailsVO> nthActivityList,
                                          List<StoreActivityRespDTO> storeActivityList) {
        if (CollectionUtils.isEmpty(nthActivityList)) {
            return;
        }
        nthActivityList.forEach(nthActivity -> {
            StoreActivityRespDTO activityRespDTO = new StoreActivityRespDTO();
            activityRespDTO.setLabel("N");
            activityRespDTO.setName("第N份优惠");
            String activityDesc = MarketingActivityHelper.buildActivityDesc(nthActivity);
            activityRespDTO.setActivityDescList(Lists.newArrayList(activityDesc));
            storeActivityList.add(activityRespDTO);
        });
    }

    public static void filterNthActivityList(List<ActivityRuleDescDTO> activityRuleDescList,
                                             ItemInfoDTO item) {
        List<ActivityRuleDescDTO> nthActivityList = activityRuleDescList.stream()
                .filter(e -> ActivityTypeEnum.NTH_ACTIVITY.getCode() == e.getActivityType())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nthActivityList)) {
            log.warn("没有第N份优惠 默认返回共享");
            return;
        }
        boolean nthActivityShareFlag = nthActivityList.stream()
                .anyMatch(e -> Objects.equals(BooleanEnum.TRUE.getCode(), e.getRelationRule()));
        if (!nthActivityShareFlag) {
            // 不共享
            activityRuleDescList.removeIf(e -> ActivityTypeEnum.FULL_MINUS.getCode() == e.getActivityType()
                    || ActivityTypeEnum.FULL_DISCOUNT.getCode() == e.getActivityType());
            for (ItemInfoSkuDTO skuDTO : item.getSkuList()) {
                List<ActivityRuleDescDTO> skuActivityRuleDescList = skuDTO.getActivityRuleDescList();
                skuActivityRuleDescList.removeIf(e -> ActivityTypeEnum.FULL_MINUS.getCode() == e.getActivityType()
                        || ActivityTypeEnum.FULL_DISCOUNT.getCode() == e.getActivityType());
            }
        }
    }

    /**
     * 查询选中的第N份优惠活动
     *
     * @param activitySelectList 选中的活动信息
     * @return 第N份优惠活动
     */
    public NthActivityDetailsVO querySelectNthActivityDetailsVO(List<ActivitySelectDTO> activitySelectList) {
        // 查询第N份优惠活动
        List<NthActivityDetailsVO> nthActivityList = queryNthActivityList(WeixinUserThreadLocal.get());

        // 选中的活动信息
        String selectActivityGuid = activitySelectList.stream()
                .filter(s -> Objects.equals(DiscountTypeEnum.NTH_ACTIVITY.getCode(), s.getActivityType()))
                .map(ActivitySelectDTO::getActivityGuid)
                .findFirst()
                .orElse(null);
        return nthActivityList.stream()
                .filter(a -> Objects.equals(selectActivityGuid, a.getGuid()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 构建商品满减满折活动
     */
    public static void buildItemMarketActivity(List<ItemInfoDTO> itemInfoList,
                                               ResponseMarketActivitieRule responseMarketActivitieRule) {
        if (Objects.isNull(responseMarketActivitieRule)) {
            responseMarketActivitieRule = new ResponseMarketActivitieRule();
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(responseMarketActivitieRule.getMarketActivitieResponseDTOList())) {
            responseMarketActivitieRule.setMarketActivitieResponseDTOList(Lists.newArrayList());
        }
        log.info("小程序满减满折活动:{}", JacksonUtils.writeValueAsString(responseMarketActivitieRule));
        List<ResponseMarketActivitieOneRule> marketActivityList = responseMarketActivitieRule.getMarketActivitieResponseDTOList();
        for (ItemInfoDTO itemInfoDTO : itemInfoList) {
            List<ItemInfoSkuDTO> skuList = itemInfoDTO.getSkuList();
            for (ItemInfoSkuDTO sku : skuList) {
                buildSkuMarketActivity(sku, marketActivityList);
            }
            // spu 满减满折活动
            buildSpuMarketActivity(itemInfoDTO);
        }
    }

    /**
     * 构建商品 spu满减满折活动
     */
    public static void buildSpuMarketActivity(ItemInfoDTO itemInfoDTO) {
        try {
            // 2024.06.21，经前后端+测试讨论，产品确定：商品列表上满减满折活动标签展示规则为展示金额所对应的规格的满减满折，如果价格相同，则取uck（默认选中）为1的规格
            ItemInfoSkuDTO infoSkuDTO = PriceCalculateHelper.getUckSku(itemInfoDTO);
            // sku 规格
            List<ActivityRuleDescDTO> skuActivityRuleDescList = infoSkuDTO.getActivityRuleDescList();
            List<ActivityRuleDescDTO> innerSkuActivityRuleDescList = skuActivityRuleDescList.stream()
                    .filter(e -> ActivityTypeEnum.FULL_MINUS.getCode() == e.getActivityType()
                            || ActivityTypeEnum.FULL_DISCOUNT.getCode() == e.getActivityType())
                    .collect(Collectors.toList());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(innerSkuActivityRuleDescList)) {
                itemInfoDTO.getActivityRuleDescList().addAll(innerSkuActivityRuleDescList);
            }
        } catch (Exception e) {
            log.error("构建满减满折商品活动失败", e);
        }

    }

    /**
     * 构建规格商品满减满折活动
     */
    public static void buildSkuMarketActivity(ItemInfoSkuDTO sku,
                                              List<ResponseMarketActivitieOneRule> marketActivityList) {
        if (Objects.isNull(sku.getFullDiscountStrList())) {
            sku.setFullDiscountStrList(Lists.newArrayList());
        }
        if (Objects.isNull(sku.getFullReductionStrList())) {
            sku.setFullReductionStrList(Lists.newArrayList());
        }
        for (ResponseMarketActivitieOneRule activityOneRule : marketActivityList) {
            sku.setActivityRuleDescList(CollectionUtils.isEmpty(sku.getActivityRuleDescList())
                    ? Lists.newArrayList() : sku.getActivityRuleDescList());
            if (VolumeStoreTypeEnum.VOLUME_STORE_TYPE_ALL_PRODUCT.getCode() == activityOneRule.getAllType()) {
                // 全部商品
                // 添加sku满减满折活动
                handleMarketActivity(activityOneRule, activityOneRule.getFullReductionStrList(),
                        activityOneRule.getFullDiscountStrList(), sku.getActivityRuleDescList());
            } else {
                // 部分商品
                List<ResponseStoreProduct> storeProductList = activityOneRule.getStoreProductDTOList();
                List<String> productSpecGuidList = storeProductList.stream()
                        .map(ResponseStoreProduct::getProductSpecGuid)
                        .distinct().collect(Collectors.toList());
                if (productSpecGuidList.contains(sku.getSkuGuid()) || productSpecGuidList.contains(sku.getParentGuid())) {
                    // 添加sku满减满折活动
                    handleMarketActivity(activityOneRule, activityOneRule.getFullReductionStrList(),
                            activityOneRule.getFullDiscountStrList(), sku.getActivityRuleDescList());
                }
            }
        }
    }

    public static void handleMarketActivity(ResponseMarketActivitieOneRule activityOneRule,
                                            List<String> fullReductionStrList,
                                            List<String> fullDiscountStrList,
                                            List<ActivityRuleDescDTO> activityRuleDescList) {
        // 满减
        List<String> fullReductionList = new ArrayList<>();
        fullReductionStrList.forEach(fullReductionStr -> fullReductionList.addAll(Lists.newArrayList(fullReductionStr.split(","))));
        List<String> mergedFullReduction = mergeMarketActivity(fullReductionList);
        mergedFullReduction.forEach(r -> {
            ActivityRuleDescDTO descDTO = new ActivityRuleDescDTO();
            descDTO.setActivityGuid(activityOneRule.getMarketActivitieGuid());
            descDTO.setActivityType(ActivityTypeEnum.FULL_MINUS.getCode());
            descDTO.setActivityRuleDesc(r);
            descDTO.setRelationRule(Objects.equals(BooleanEnum.TRUE.getCode(), activityOneRule.getFullShare()) ?
                    BooleanEnum.FALSE.getCode() : BooleanEnum.TRUE.getCode());
            activityRuleDescList.add(descDTO);
        });
        // 满折
        List<String> fullDiscountList = new ArrayList<>();
        fullDiscountStrList.forEach(fullDiscountStr -> fullDiscountList.addAll(Lists.newArrayList(fullDiscountStr.split(","))));
        List<String> mergedFullDiscount = mergeMarketActivity(fullDiscountList);
        mergedFullDiscount.forEach(d -> {
            ActivityRuleDescDTO descDTO = new ActivityRuleDescDTO();
            descDTO.setActivityGuid(activityOneRule.getMarketActivitieGuid());
            descDTO.setActivityType(ActivityTypeEnum.FULL_DISCOUNT.getCode());
            descDTO.setActivityRuleDesc(d);
            descDTO.setRelationRule(Objects.equals(BooleanEnum.TRUE.getCode(), activityOneRule.getFullShare()) ?
                    BooleanEnum.FALSE.getCode() : BooleanEnum.TRUE.getCode());
            activityRuleDescList.add(descDTO);
        });
    }

    /**
     * 合并商品满减满折活动
     */
    public static List<String> mergeMarketActivity(List<String> fullStrList) {
        if (CollectionUtils.isEmpty(fullStrList)) {
            return fullStrList;
        }
        // 去重
        fullStrList = fullStrList.stream().distinct().collect(Collectors.toList());
        // 排序
        Map<BigDecimal, String> map = Maps.newHashMap();
        for (String fullStr : fullStrList) {
            String firstNumber = StringMessageUtils.getFirstNumber(fullStr);
            if (StringUtils.isNotEmpty(firstNumber)) {
                map.put(new BigDecimal(firstNumber), fullStr);
            }
        }
        List<BigDecimal> numberSortedList = map.keySet()
                .stream()
                .sorted(Comparator.comparing(BigDecimal::doubleValue))
                .collect(Collectors.toList());
        return numberSortedList.stream().map(map::get).collect(Collectors.toList());
    }

    /**
     * 活动时段过滤
     */
    public boolean periodFilter(ResponseMarketActivitieOneRule marketActivity,
                                LocalDateTime orderDate) {
        Integer periodType = marketActivity.getPeriodType();
        String periodJson = marketActivity.getPeriodJson();
        if (periodType == ActivitiePeriodTypeEnum.PERIOD_UNLIMITED.getCode()) {
            // 不限至时段
            return true;
        }
        // 限制了时段
        String minute = (orderDate.getMinute() + "").length() == 1 ? "0" + orderDate.getMinute() : orderDate.getMinute() + "";
        String second = (orderDate.getSecond() + "").length() == 1 ? "0" + orderDate.getSecond() : orderDate.getSecond() + "";
        int currentTime = Integer.parseInt(orderDate.getHour() + minute + second);
        List<ResponseActivitiePeriod> responseActivitiePeriodList = JacksonUtils.toObjectList(ResponseActivitiePeriod.class, periodJson);
        if (periodType == ActivitiePeriodTypeEnum.PERIOD_DAY.getCode()) {
            return isFullPeriodDayRule(responseActivitiePeriodList, currentTime);
        } else if (periodType == ActivitiePeriodTypeEnum.PERIOD_WEEK.getCode()) {
            return isFullPeriodWeekRule(responseActivitiePeriodList, currentTime);
        } else {
            return isFullPeriodMonthRule(responseActivitiePeriodList, currentTime);
        }
    }

    /**
     * 是否满足 限制每周
     */
    public boolean isFullPeriodMonthRule(List<ResponseActivitiePeriod> responseActivitiePeriodList,
                                         int currentTime) {
        int month = LocalDateTime.now().getDayOfMonth();
        for (ResponseActivitiePeriod activitiePeriod : responseActivitiePeriodList) {
            List<ResponseActivitiePeriodMonth> periodMonthList = activitiePeriod.getPeriodMonth();
            List<ResponseActivitiePeriodMonth> hasMonthList = periodMonthList.stream()
                    .filter(m -> m.getMonthIndex() == month)
                    .collect(Collectors.toList());
            if (!org.springframework.util.CollectionUtils.isEmpty(hasMonthList)) {
                boolean isAdd = periodFilter(activitiePeriod, currentTime);
                if (isAdd) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 是否满足 限制每周
     */
    public boolean isFullPeriodWeekRule(List<ResponseActivitiePeriod> responseActivitiePeriodList,
                                        int currentTime) {
        int week = LocalDateTime.now().getDayOfWeek().getValue();
        for (ResponseActivitiePeriod activitiePeriod : responseActivitiePeriodList) {
            List<ResponseActivitiePeriodWeek> periodWeekList = activitiePeriod.getPeriodWeek();
            List<ResponseActivitiePeriodWeek> hasWeekList = periodWeekList.stream()
                    .filter(w -> w.getWeekIndex() == week)
                    .collect(Collectors.toList());
            if (!org.springframework.util.CollectionUtils.isEmpty(hasWeekList)) {
                boolean isAdd = periodFilter(activitiePeriod, currentTime);
                if (isAdd) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 是否满足 限制每日
     */
    public boolean isFullPeriodDayRule(List<ResponseActivitiePeriod> responseActivitiePeriodList,
                                       int currentTime) {
        // 限制每日
        for (ResponseActivitiePeriod activitiePeriod : responseActivitiePeriodList) {
            boolean isAdd = periodFilter(activitiePeriod, currentTime);
            if (isAdd) {
                return true;
            }
        }
        return false;
    }

    /**
     * 活动时段过滤
     */
    public boolean periodFilter(ResponseActivitiePeriod activitiePeriod,
                                int currentTime) {
        int periodStart = Integer.parseInt(activitiePeriod.getPeriodHourStart().replace(":", ""));
        int periodEnd = Integer.parseInt(activitiePeriod.getPeriodHourEnd().replace(":", ""));
        if (periodStart == 0 && periodEnd == 0) {
            return true;
        }
        return currentTime >= periodStart && currentTime <= periodEnd;
    }

    public void checkItem(List<String> itemGuidList,
                          List<LimitSpecialsActivityDetailsVO> specialsActivityList) {
        if (org.springframework.util.CollectionUtils.isEmpty(specialsActivityList)) {
            log.warn("活动商品过滤前为空");
            return;
        }
        if (org.springframework.util.CollectionUtils.isEmpty(itemGuidList)) {
            specialsActivityList.clear();
            log.warn("门店商品为空");
            return;
        }
        List<String> toDeleteActivity = new ArrayList<>();
        specialsActivityList.forEach(activity -> {
            List<String> commodityIdList = activity.getItemDTOList().stream()
                    .map(LimitSpecialsActivityItemDTO::getCommodityId)
                    .distinct()
                    .collect(Collectors.toList());
            commodityIdList.retainAll(itemGuidList);
            if (org.springframework.util.CollectionUtils.isEmpty(commodityIdList)) {
                toDeleteActivity.add(activity.getGuid());
            }
        });
        if (!org.springframework.util.CollectionUtils.isEmpty(toDeleteActivity)) {
            specialsActivityList.removeIf(a -> toDeleteActivity.contains(a.getGuid()));
        }
    }
}
