package com.holderzone.holder.saas.aggregation.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.feign.spring.boot.pojo.UserContext;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.app.config.ZhuanCanConfig;
import com.holderzone.holder.saas.aggregation.app.converter.VolumeConverter;
import com.holderzone.holder.saas.aggregation.app.service.OrderDetailsService;
import com.holderzone.holder.saas.aggregation.app.service.TradeService;
import com.holderzone.holder.saas.aggregation.app.service.VolumeService;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.order.VolumeClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInBillClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.trade.DineInOrderClientService;
import com.holderzone.holder.saas.aggregation.app.utils.HttpsClientUtils;
import com.holderzone.holder.saas.common.enums.BooleanEnum;
import com.holderzone.holder.saas.member.terminal.dto.volume.*;
import com.holderzone.saas.store.dto.order.request.member.MemberCouponListReqDTO;
import com.holderzone.saas.store.dto.order.request.member.RequestQueryVolumeDTO;
import com.holderzone.saas.store.dto.order.request.member.RequestRedeemCodeApplyDTO;
import com.holderzone.saas.store.enums.order.OrderStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class VolumeServiceImpl implements VolumeService {

    @Resource
    private VolumeClientService volumeClientService;

    @Resource
    private DineInOrderClientService dineInOrderClientService;

    @Resource
    private ZhuanCanConfig zhuanCanConfig;

    @Resource
    private DineInBillClientService dineInBillClientService;

    @Override
    public ResponseQueryVolumeDTO queryMemberVolumeList(RequestQueryVolumeDTO requestQueryVolumeDTO) {
        UserContext userContext = UserContextUtils.get();
        userContext.setStoreGuid(requestQueryVolumeDTO.getStoreGuid());
        UserContextUtils.put(userContext);

        log.info("查询会员可用优惠券列表入参=}", JSON.toJSONString(requestQueryVolumeDTO));

        ResponseQueryVolumeDTO responseQueryVolumeDTO = new ResponseQueryVolumeDTO();

        //查询已校验的券
        List<ResponseVolumeList> checkedVolumeLists = volumeClientService.consumeVolumeList(requestQueryVolumeDTO.getOrderGuid());

        //查询拥有的券
        MemberCouponListReqDTO memberCouponListReqDTO = new MemberCouponListReqDTO();
        BeanUtils.copyProperties(requestQueryVolumeDTO, memberCouponListReqDTO);
        log.info("查询会员可用优惠券列表入参={}", JSON.toJSONString(memberCouponListReqDTO));

        List<ResponseVolumeList> usableVolumeLists = dineInOrderClientService.couponList(memberCouponListReqDTO);

        responseQueryVolumeDTO.setUsableVolumeLists(usableVolumeLists);

        responseQueryVolumeDTO.setCheckedVolumeLists(checkedVolumeLists);
        return responseQueryVolumeDTO;
    }

    @Override
    public ResponseCheckVolumeRedeem checkVolumeRedeem(RequestCheckVolumeRedeem request) {
        ResponseCheckVolumeRedeem responseCheckVolumeRedeem = new ResponseCheckVolumeRedeem();
        log.info("一体机优惠券兑换入参：{}", JacksonUtils.writeValueAsString(request));

        UserContext userContext = UserContextUtils.get();
        log.info("userContext={}", JSON.toJSONString(userContext));
        userContext.setStoreGuid(request.getStoreGuid());
        userContext.setEnterpriseGuid(request.getEnterpriseGuid());
        userContext.setOperSubjectGuid(request.getOperSubjectGuid());
        UserContextUtils.put(userContext);

        //拦截已结账订单兑换
        Integer orderState = dineInOrderClientService.getOrderStateByGuid(request.getOrderGuid());
        if (orderState == OrderStateEnum.CHECKED_OUT.getCode()) {
            throw new BusinessException("订单状态已变更，无法兑换");
        }

        //优先校验券码
        RequestCheckSingleVolumeRedeem checkSingleVolumeRedeem = getRequestCheckSingleVolumeRedeem(request);

        ResponseCheckSingleVolumeRedeem volumeRedeem = volumeClientService.checkSingleVolumeRedeem(checkSingleVolumeRedeem);
        log.info("券码兑换返回参数={}", JacksonUtils.writeValueAsString(volumeRedeem));
        //兑换成功
        if (StringUtils.isEmpty(volumeRedeem.getMessage())) {
            VolumeConverter.responseCheckVolumeRedeemConverter(responseCheckVolumeRedeem, volumeRedeem);
            log.info("券码兑换成功返参={}", responseCheckVolumeRedeem);
            return responseCheckVolumeRedeem;
        }

        //赚餐兑换包
        RequestRedeemCodeApplyDTO redeemCodeApplyDTO = getRequestRedeemCodeApplyDTO(request, userContext);
        String zcResponse = HttpsClientUtils.doPost(zhuanCanConfig.getMemberRedeem(), JacksonUtils.writeValueAsString(redeemCodeApplyDTO));

        log.info("一体机优惠券兑换返回：{}", zcResponse);
        if (Objects.isNull(zcResponse)) {
            throw new BusinessException("一体机优惠券兑换调用异常");
        }
        JSONObject jsonObject = JSON.parseObject(zcResponse);
        Object causeMessage = jsonObject.get("causeMessage");
        if (Objects.nonNull(causeMessage)) {
            String zhuanCanMessage = causeMessage.toString();
            //适配重复码
            if (zhuanCanMessage.equals("兑换码有误")) {
                throw new BusinessException(volumeRedeem.getMessage());
            } else {
                //若重复码 则优先抛出券包兑换码错误
                throw new BusinessException(zhuanCanMessage);
            }
        }
        responseCheckVolumeRedeem.setResultState(BooleanEnum.TRUE.getCode());
        log.info("赚餐兑换成功返参={}", responseCheckVolumeRedeem);

        dineInBillClientService.updateOrderIsUpdatedEs(request.getOrderGuid());
        return responseCheckVolumeRedeem;
    }

    @NotNull
    private static RequestCheckSingleVolumeRedeem getRequestCheckSingleVolumeRedeem(RequestCheckVolumeRedeem request) {
        RequestCheckSingleVolumeRedeem checkSingleVolumeRedeem = new RequestCheckSingleVolumeRedeem();
        checkSingleVolumeRedeem.setVolumeCode(request.getRedeemCodeVal());
        checkSingleVolumeRedeem.setStoreGuid(request.getStoreGuid());

        if (StringUtils.isNotEmpty(request.getMemberInfoGuid())) {
            checkSingleVolumeRedeem.setMemberInfoGuid(request.getMemberInfoGuid());
        } else {
            checkSingleVolumeRedeem.setOrderGuid(request.getOrderGuid());
        }
        return checkSingleVolumeRedeem;
    }

    @NotNull
    private static RequestRedeemCodeApplyDTO getRequestRedeemCodeApplyDTO(RequestCheckVolumeRedeem
                                                                                  request, UserContext userContext) {
        RequestRedeemCodeApplyDTO redeemCodeApplyDTO = new RequestRedeemCodeApplyDTO();
        redeemCodeApplyDTO.setSource(Integer.valueOf(userContext.getSource()));
        redeemCodeApplyDTO.setStoreGuid(userContext.getStoreGuid());
        redeemCodeApplyDTO.setStoreName(userContext.getStoreName());
        redeemCodeApplyDTO.setMemberInfoGuid(request.getMemberInfoGuid());
        redeemCodeApplyDTO.setRedeemCodeVal(request.getRedeemCodeVal());
        redeemCodeApplyDTO.setPhoneNum(request.getPhoneNum());
        redeemCodeApplyDTO.setMemberName(request.getMemberName());
        redeemCodeApplyDTO.setOperSubjectGuid(request.getOperSubjectGuid());
        redeemCodeApplyDTO.setOrderGuid(request.getOrderGuid());
        redeemCodeApplyDTO.setEnterpriseGuid(userContext.getEnterpriseGuid());
        return redeemCodeApplyDTO;
    }
}
