package com.holderzone.holder.saas.aggregation.app.entity.auth;

import com.holderzone.holder.saas.aggregation.app.anno.DataAuthFieldControl;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/10/30
 * @description 日报统计金额项
 */
@Data
@ApiModel
@Accessors(chain = true)
public class AmountItemDTO {

    @ApiModelProperty(value = "编码")
    private Integer code;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "金额")
    @DataAuthFieldControl("overview_sale_actually_amount")
    private String amount;

    @ApiModelProperty(value = "商家预计应得金额")
    private String amountStr;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    @ApiModelProperty(value = "数量")
    private Integer count;

    @ApiModelProperty(value = "超出金额，第三方活动使用")
    @DataAuthFieldControl("overview_sale_actually_amount")
    private String excessAmount;

    @ApiModelProperty(value = "顺序")
    private Integer sort;

    @ApiModelProperty(value = "使用明细")
    @DataAuthFieldControl(value = "overview_sale_actually_amount", isListClear = true)
    private List<InnerDetails> innerDetails;

    @ApiModelProperty(value = "商家预计应得金额")
    @DataAuthFieldControl("overview_sale_estimated_amount")
    private String estimatedAmount;

    @ApiModelProperty(value = "订单笔数")
    private String orderCount;

    @ApiModelProperty(value = "团购券张数")
    private String grouponCount;

    @ApiModelProperty(value = "是否团购")
    private Boolean isGroupon;

    @Data
    public static class InnerDetails {
        @ApiModelProperty("明细")
        private String name;

        @ApiModelProperty("统计金额")
        private BigDecimal amount;

        @ApiModelProperty(value = "优惠金额")
        private BigDecimal discountAmount;

        @ApiModelProperty("使用数量")
        private Integer count;

        @ApiModelProperty(value = "订单笔数")
        private String orderCount;

        @ApiModelProperty(value = "团购券张数")
        private String grouponCount;

        @ApiModelProperty(value = "是否团购")
        private Boolean isGroupon;

        @ApiModelProperty(value = "使用明细")
        private List<InnerDetails> subInnerDetails;
    }


    public AmountItemDTO() {

    }

    public AmountItemDTO(Integer code, String name, String amount, String excessAmount) {
        this.code = code;
        this.name = name;
        this.amount = amount;
        this.excessAmount = excessAmount;
    }
}