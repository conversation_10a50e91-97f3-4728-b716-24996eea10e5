package com.holderzone.holder.saas.aggregation.app.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 检测工具类
 *
 * <AUTHOR>
 * @date 2025/7/31 10:43
 */
@Slf4j
public class ValidateUtil {

    /**
     * 检测订单是否显示提交调整单按钮
     *
     * @param checkoutTime 判断时间
     * @return 查询结果
     */
    public static boolean checkOrderAdjust(LocalDateTime checkoutTime) {
        if (Objects.isNull(checkoutTime)) {
            return false;
        }
        LocalDate checkDate = checkoutTime.toLocalDate();
        // 当前时间
        LocalDateTime now = LocalDateTime.now();
        log.info("调整单-时间比对, checkDate:{}, now:{}", checkDate, now);
        // 当月数据始终可以操作
        if (checkYearMonth(now.toLocalDate(), checkDate)) {
            return true;
        }
        // 当前时间和当月1日8点进行对比
        LocalDateTime oneTime = LocalDateTime.of(now.getYear(), now.getMonthValue(), 1, 8, 0, 0);
        // 如果在指定时间之前，则可以操作上个月和这个月的数据
        if (now.isBefore(oneTime)) {
            LocalDate lastMonthDate = now.toLocalDate().minusMonths(1);
            return checkYearMonth(checkDate, lastMonthDate);
        }
        return false;
    }

    /**
     * 比对年月，不比对日
     *
     * @param localDate1 时间1
     * @param localDate2 时间2
     * @return 查询结果
     */
    private static boolean checkYearMonth(LocalDate localDate1, LocalDate localDate2) {
        return localDate1.getYear() == localDate2.getYear() && localDate1.getMonth() == localDate2.getMonth();
    }
}
