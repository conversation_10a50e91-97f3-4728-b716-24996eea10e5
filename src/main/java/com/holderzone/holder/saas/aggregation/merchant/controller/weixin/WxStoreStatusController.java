package com.holderzone.holder.saas.aggregation.merchant.controller.weixin;

import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.weixin.WxStoreStatusClientService;
import com.holderzone.saas.store.dto.weixin.req.WxStorePageReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreReqDTO;
import com.holderzone.saas.store.dto.weixin.req.WxStoreStatusUpdateReqDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxCouldEditStoreDTO;
import com.holderzone.saas.store.dto.weixin.resp.WxStoreStatusRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className WxStoreStatusController
 * @date 2019/02/21 19:15
 * @description 微信门店业务状态Controller
 * @program holder-saas-store-weixin
 */
@RestController
@RequestMapping("/wx_store_status")
@Api(value = "微信门店业务状态Controller", description = "微信门店业务状态Controller")
@Slf4j
public class WxStoreStatusController {

    @Autowired
    WxStoreStatusClientService wxStoreStatusClientService;

    @PostMapping("/page_wx_store_status")
    @ApiOperation(value = "获取微信门店业务配置状态")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "获取微信门店业务配置状态")
    public Result<Page<WxStoreStatusRespDTO>> getWxStoreStatus(@RequestBody WxStorePageReqDTO wxStorePageReqDTO) {
        log.info("已获取到查询微信门店业务状态请求参数：{}", wxStorePageReqDTO.toString());
        return Result.buildSuccessResult(wxStoreStatusClientService.pageWxStoreStatus(wxStorePageReqDTO));
    }

    @PostMapping("/update_status_by_guid")
    @ApiOperation(value = "修改门店微信功能状态byGuid")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "修改门店微信功能状态byGuid")
    public Result updateStatusByGuid(@RequestBody WxStoreStatusUpdateReqDTO wxStoreStatusUpdateReqDTO) {
        log.info("已获取到修改门店微信功能状态修改请求参数：{}", JacksonUtils.writeValueAsString(wxStoreStatusUpdateReqDTO));
        wxStoreStatusClientService.updateStatusByGuid(wxStoreStatusUpdateReqDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "当前可编辑的门店列表")
    @PostMapping("/list_could_edit_store")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_WEIXIN, description = "当前可编辑的门店列表")
    public Result<List<WxCouldEditStoreDTO>> listCouldEditStore(@RequestBody WxStoreReqDTO wxStoreReqDTO) {
        log.info("merchant聚合层：正在获取当前可编辑门店列表");
        return Result.buildSuccessResult(wxStoreStatusClientService.listCouldEditStore(wxStoreReqDTO));
    }
}
