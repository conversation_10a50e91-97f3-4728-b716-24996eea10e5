package com.holderzone.holder.saas.aggregation.app.service.feign.kds;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.kds.req.*;
import com.holderzone.saas.store.dto.kds.resp.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(name = "holder-saas-store-kds", fallbackFactory = KitchenItemRpcService.FallbackFactoryImpl.class)
public interface KitchenItemRpcService {

    @PostMapping("/kitchen_item/prd_query")
    PrdDstRespDTO prdQuery(@RequestBody PrdItemStatusReqDTO prdItemStatusReqDTO);

    @PostMapping("/kitchen_item/dst_query")
    PrdDstRespDTO dstQuery(@RequestBody DstItemStatusReqDTO dstItemStatusReqDTO);

    @PostMapping("/kitchen_item/cooking")
    void cooking(@RequestBody ItemStateTransReqDTO itemStateTransReqDTO);

    @PostMapping("/kitchen_item/complete")
    void complete(@RequestBody ItemStateTransReqDTO itemStateTransReqDTO);

    @PostMapping("/kitchen_item/cancelComplete")
    void cancelComplete(@RequestBody ItemStateTransReqDTO itemStateTransReqDTO);

    @PostMapping("/kitchen_item/distribute")
    List<PrdDstItemDTO> distribute(@RequestBody ItemStateTransReqDTO itemStateTransReqDTO);

    @PostMapping("/kitchen_item/distribute_batch")
    @ApiOperation(value = "循环菜品出堂，参数要求同制作菜品")
    void batchDistribute(@RequestBody @Validated List<ItemStateTransReqDTO> reqDTOList);

    @PostMapping("/kitchen_item/prd_history")
    Page<KitchenItemDTO> prdHistory(@RequestBody PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO);

    @PostMapping("/kitchen_item/dst_history")
    Page<KitchenItemDTO> dstHistory(@RequestBody PrdDstItemHistoryReqDTO itemStateTransReqDTO);

    @PostMapping("/kitchen_item/prd_print_again")
    void prdPrintAgain(@RequestBody KitchenItemDTO kitchenItemDTO);

    @PostMapping("/kitchen_item/dst_print_again")
    void dstPrintAgain(@RequestBody KitchenItemDTO kitchenItemDTO);

    @PostMapping("/kitchen_item/cancel_dst")
    void cancelDistribute(@RequestBody ItemCancelDstReqDTO itemCancelDstReqDTO);

    @PostMapping("/kitchen_item/query_by_order_item")
    @ApiOperation(value = "根据订单商品查询kds")
    List<KitchenItemRespDTO> queryByOrderItem(@RequestBody ItemBatchRefundReqDTO query);

    @PostMapping("/kitchen_item/query_by_order")
    @ApiOperation(value = "根据订单查询kds未出堂商品")
    List<PrdDstItemDTO> queryByOrder(@RequestBody PrdDstItemQueryDTO query);

    @ApiOperation(value = "根据sku查询出堂商品配置")
    @PostMapping("/distribute/query_distribute_item_by_sku")
    List<DistributeItemDTO> queryDistributeItemBySku(@RequestBody SingleDataDTO reqDTO);

    @Slf4j
    @Component
    class FallbackFactoryImpl implements FallbackFactory<KitchenItemRpcService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public KitchenItemRpcService create(Throwable throwable) {

            return new KitchenItemRpcService() {

                @Override
                public PrdDstRespDTO prdQuery(PrdItemStatusReqDTO prdItemStatusReqDTO) {
                    log.error(HYSTRIX_PATTERN, "prdQuery",
                            JacksonUtils.writeValueAsString(prdItemStatusReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public PrdDstRespDTO dstQuery(DstItemStatusReqDTO dstItemStatusReqDTO) {
                    log.error(HYSTRIX_PATTERN, "dstQuery",
                            JacksonUtils.writeValueAsString(dstItemStatusReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void cooking(ItemStateTransReqDTO itemStateTransReqDTO) {
                    log.error(HYSTRIX_PATTERN, "cooking",
                            JacksonUtils.writeValueAsString(itemStateTransReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void complete(ItemStateTransReqDTO itemStateTransReqDTO) {
                    log.error(HYSTRIX_PATTERN, "complete",
                            JacksonUtils.writeValueAsString(itemStateTransReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void cancelComplete(ItemStateTransReqDTO itemStateTransReqDTO) {
                    log.error(HYSTRIX_PATTERN, "cancelComplete",
                            JacksonUtils.writeValueAsString(itemStateTransReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PrdDstItemDTO> distribute(ItemStateTransReqDTO itemStateTransReqDTO) {
                    log.error(HYSTRIX_PATTERN, "distribute",
                            JacksonUtils.writeValueAsString(itemStateTransReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void batchDistribute(List<ItemStateTransReqDTO> reqDTOList) {
                    log.error(HYSTRIX_PATTERN, "batchDistribute",
                            JacksonUtils.writeValueAsString(reqDTOList),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<KitchenItemDTO> prdHistory(PrdDstItemHistoryReqDTO prdDstItemHistoryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "prdHistory",
                            JacksonUtils.writeValueAsString(prdDstItemHistoryReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public Page<KitchenItemDTO> dstHistory(PrdDstItemHistoryReqDTO itemStateTransReqDTO) {
                    log.error(HYSTRIX_PATTERN, "dstHistory",
                            JacksonUtils.writeValueAsString(itemStateTransReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void prdPrintAgain(KitchenItemDTO kitchenItemDTO) {
                    log.error(HYSTRIX_PATTERN, "prdPrintAgain",
                            JacksonUtils.writeValueAsString(kitchenItemDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void dstPrintAgain(KitchenItemDTO kitchenItemDTO) {
                    log.error(HYSTRIX_PATTERN, "dstPrintAgain",
                            JacksonUtils.writeValueAsString(kitchenItemDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public void cancelDistribute(ItemCancelDstReqDTO itemCancelDstReqDTO) {
                    log.error(HYSTRIX_PATTERN, "cancelDistribute",
                            JacksonUtils.writeValueAsString(itemCancelDstReqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<KitchenItemRespDTO> queryByOrderItem(ItemBatchRefundReqDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryByOrderItem",
                            JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<PrdDstItemDTO> queryByOrder(PrdDstItemQueryDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryByOrder",
                            JacksonUtils.writeValueAsString(query),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public List<DistributeItemDTO> queryDistributeItemBySku(SingleDataDTO reqDTO) {
                    log.error(HYSTRIX_PATTERN, "queryDistributeItemBySku",
                            JacksonUtils.writeValueAsString(reqDTO),
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }
            };
        }
    }
}
