package com.holderzone.holder.saas.aggregation.phoneapp.service.rpc;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.Page;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.resource.common.dto.user.RoleQuery;
import com.holderzone.saas.store.dto.common.SingleDataDTO;
import com.holderzone.saas.store.dto.user.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className UserFeignService
 * @date 2019/05/31 14:28
 * @description 用户feignService
 * @program holder-saas-store
 */
@Component
@FeignClient(name = "holder-saas-store-staff", fallbackFactory = UserFeignService.ServiceFallBack.class)
public interface UserFeignService {
    @PostMapping(value = "/user/new_account")
    String newAccount();

    @PostMapping(value = "/user/new_auth_code")
    String newAuthCode();

    @PostMapping(value = "/user/new_user_office")
    UserOfficeDTO newUserOffice(@RequestBody UserOfficeDTO userOfficeDTO);

    @PostMapping(value = "/user/list_user_office")
    List<UserOfficeDTO> listUserOffice();

    @PostMapping(value = "/user/create")
    String createUser(@RequestBody UserDTO userDTO);

    @PostMapping(value = "/user/update")
    String updateUser(@RequestBody UserDTO userDTO);

    @PostMapping(value = "/user/query")
    UserDTO queryUser(@RequestBody UserDTO userDTO);

    @PostMapping(value = "/user/enable_or_disable")
    void enableOrDisableUser(@RequestBody UserDTO userDTO);

    @PostMapping(value = "/user/delete")
    void deleteUser(@RequestBody UserDTO userDTO);

    @PostMapping(value = "/user/page_query")
    Page<UserDTO> pageQuery(@RequestBody UserQueryDTO userQueryDTO);

    @PostMapping(value = "/user/update_pwd")
    void updatePwd(@RequestBody UpdatePwdDTO updatePwdDTO);

    @PostMapping(value = "/user/self_update_pwd")
    void selfModifyPwd(@RequestBody UpdatePwdDTO updatePwdDTO);

    @PostMapping(value = "/user/reset_pwd")
    void resetPwd(@RequestBody(required = false) ResetPwdDTO resetPwdDTO);

    @PostMapping(value = "/user/role")
    void addUserRoleRelation(@RequestBody List<com.holderzone.resource.common.dto.user.UserDTO> userDTOList);

    @GetMapping(value = "/user/role/{userGuid}")
    List<com.holderzone.resource.common.dto.user.RoleDTO> findRoleByUserGuid(@PathVariable("userGuid") String userGuid);

    @PostMapping(value = "/permission/createRole")
    void createRole(@RequestBody GrantRoleDTO grantRoleDTO, @RequestParam(value = "requestUri", required = false) String requestUri);

    @DeleteMapping(value = "/role/{roleGUid}")
    void deleteRole(@PathVariable("roleGUid") String roleGUid);

    @PostMapping(value = "/role/enable")
    void enableRole(@RequestBody com.holderzone.resource.common.dto.user.RoleDTO roleDTO);

    @PostMapping(value = "/permission/updateRole")
    void updateRole(@RequestBody GrantRoleDTO grantRoleDTO, @RequestParam(value = "requestUri", required = false) String requestUri);

    @PostMapping(value = "/role/querybypage")
    Page<com.holderzone.resource.common.dto.user.RoleDTO> findRoleByPage(@RequestBody RoleQuery roleQuery);

    @PostMapping(value = "/role/query")
    List<com.holderzone.resource.common.dto.user.RoleDTO> findRoleList(@RequestBody RoleQuery roleQuery);

    @GetMapping(value = "/permission/findPermissionByRoleId/{roleGuid}")
    List<RolePermissionDTO> findPermissionById(@PathVariable("roleGuid") String roleGuid);

    @GetMapping(value = "/permission/findPermissionByUserId/{userGuid}")
    List<RolePermissionDTO> findPermissionByUserId(@PathVariable("userGuid") String userGuid);

    @PostMapping(value = "/permission/grantPermission")
    void grantPermission(@RequestBody GrantRoleDTO grantRoleDTO);

    @PostMapping(value = "/user_data/save")
    void saveUserData(@RequestBody UserDTO userDTO);

    @PostMapping(value = "/user_data/query")
    UserDTO queryUserData(@RequestBody UserDTO userDTO);

    @PostMapping(value = "/user_data/update")
    void updateUserData(@RequestBody UserDTO userDTO);

    @PostMapping(value = "/user_data/query_roles_distributable")
    UserDTO queryUserRolesDistributable();

    @PostMapping(value = "/user_data/query_store_spinner")
    UserSpinnerDTO queryStoreSpinner();

    @PostMapping(value = "/user_data/query_store_spinner_by_brand_guid")
    UserSpinnerDTO queryStoreSpinnerByBrandGuid(@RequestBody SingleDataDTO singleDataDTO);

    @PostMapping(value = "/user_data/query_org_spinner")
    UserSpinnerDTO queryOrgSpinner();

    @PostMapping(value = "/user_data/query_brand_spinner")
    UserSpinnerDTO queryBrandSpinner();

    @PostMapping(value = "/user_data/query_condition_spinner")
    UserSpinnerDTO queryConditionSpinner();

    @PostMapping(value = "/user_data/query_store_and_cond_spinner")
    UserSpinnerDTO queryStoreAndCondSpinner();

    @PostMapping(value = "/user_data/query_generic_org_spinner")
    UserSpinnerDTO queryGenericOrgSpinner();

    @PostMapping(value = "/user_data/query_all_store_guid")
    List<String> queryAllStoreGuid();

    @Slf4j
    @Component
    class ServiceFallBack implements FallbackFactory<UserFeignService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public UserFeignService create(Throwable cause) {
            return new UserFeignService() {

                @Override
                public String newAccount() {
                    log.error(HYSTRIX_PATTERN, "newAccount", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public String newAuthCode() {
                    log.error(HYSTRIX_PATTERN, "newAuthCode", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public UserOfficeDTO newUserOffice(UserOfficeDTO userOfficeDTO) {
                    log.error(HYSTRIX_PATTERN, "newUserOffice", JacksonUtils.writeValueAsString(userOfficeDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<UserOfficeDTO> listUserOffice() {
                    log.error(HYSTRIX_PATTERN, "listUserOffice", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public String createUser(UserDTO userDTO) {
                    log.error(HYSTRIX_PATTERN, "createUser", JacksonUtils.writeValueAsString(userDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException("创建员工失败");
                }

                @Override
                public String updateUser(UserDTO userDTO) {
                    log.error(HYSTRIX_PATTERN, "updateUser", JacksonUtils.writeValueAsString(userDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public UserDTO queryUser(UserDTO userDTO) {
                    log.error(HYSTRIX_PATTERN, "queryUser", JacksonUtils.writeValueAsString(userDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void enableOrDisableUser(UserDTO userDTO) {
                    log.error(HYSTRIX_PATTERN, "enableOrDisableUser", JacksonUtils.writeValueAsString(userDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void deleteUser(UserDTO userDTO) {
                    log.error(HYSTRIX_PATTERN, "deleteUser", JacksonUtils.writeValueAsString(userDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Page<UserDTO> pageQuery(UserQueryDTO userQueryDTO) {
                    log.error(HYSTRIX_PATTERN, "pageQuery", JacksonUtils.writeValueAsString(userQueryDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void updatePwd(UpdatePwdDTO updatePwdDTO) {
                    log.error(HYSTRIX_PATTERN, "updatePwd", JacksonUtils.writeValueAsString(updatePwdDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void selfModifyPwd(UpdatePwdDTO updatePwdDTO) {
                    log.error(HYSTRIX_PATTERN, "selfModifyPwd", JacksonUtils.writeValueAsString(updatePwdDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void resetPwd(ResetPwdDTO resetPwdDTO) {
                    log.error(HYSTRIX_PATTERN, "resetPwd", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void addUserRoleRelation(List<com.holderzone.resource.common.dto.user.UserDTO> userDTOList) {
                    log.error(HYSTRIX_PATTERN, "addUserRoleRelation", JacksonUtils.writeValueAsString(userDTOList),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<com.holderzone.resource.common.dto.user.RoleDTO> findRoleByUserGuid(String userGuid) {
                    log.error(HYSTRIX_PATTERN, "findRoleByUserGuid", "userGuid为：" + userGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void createRole(GrantRoleDTO grantRoleDTO, String requestUri) {
                    log.error(HYSTRIX_PATTERN, "createRole", JacksonUtils.writeValueAsString(grantRoleDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void deleteRole(String roleGuid) {
                    log.error(HYSTRIX_PATTERN, "deleteRole", "roleGuid为：" + roleGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void enableRole(com.holderzone.resource.common.dto.user.RoleDTO roleDTO) {
                    log.error(HYSTRIX_PATTERN, "enableRole", JacksonUtils.writeValueAsString(roleDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void updateRole(GrantRoleDTO grantRoleDTO, String requestUri) {
                    log.error(HYSTRIX_PATTERN, "updateRole", JacksonUtils.writeValueAsString(grantRoleDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public Page<com.holderzone.resource.common.dto.user.RoleDTO> findRoleByPage(RoleQuery roleQuery) {
                    log.error(HYSTRIX_PATTERN, "findRoleByPage", JacksonUtils.writeValueAsString(roleQuery),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<com.holderzone.resource.common.dto.user.RoleDTO> findRoleList(RoleQuery roleQuery) {
                    log.error(HYSTRIX_PATTERN, "findRoleList", JacksonUtils.writeValueAsString(roleQuery),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<RolePermissionDTO> findPermissionById(String roleGuid) {
                    log.error(HYSTRIX_PATTERN, "findPermissionById", "角色guid为：" + roleGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<RolePermissionDTO> findPermissionByUserId(String userGuid) {
                    log.error(HYSTRIX_PATTERN, "findPermissionById", "员工guid为：" + userGuid, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void grantPermission(GrantRoleDTO grantRoleDTO) {
                    log.error(HYSTRIX_PATTERN, "grantPermission", JacksonUtils.writeValueAsString(grantRoleDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void saveUserData(UserDTO userDTO) {
                    log.error(HYSTRIX_PATTERN, "save", JacksonUtils.writeValueAsString(userDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public UserDTO queryUserData(UserDTO userDTO) {
                    log.error(HYSTRIX_PATTERN, "query", JacksonUtils.writeValueAsString(userDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public void updateUserData(UserDTO userDTO) {
                    log.error(HYSTRIX_PATTERN, "update", JacksonUtils.writeValueAsString(userDTO),
                            ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public UserDTO queryUserRolesDistributable() {
                    log.error(HYSTRIX_PATTERN, "queryUserRolesDistributable", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public UserSpinnerDTO queryBrandSpinner() {
                    log.error(HYSTRIX_PATTERN, "queryBrandSpinner", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public UserSpinnerDTO queryConditionSpinner() {
                    log.error(HYSTRIX_PATTERN, "queryConditionSpinner", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public UserSpinnerDTO queryStoreAndCondSpinner() {
                    log.error(HYSTRIX_PATTERN, "queryStoreAndCondSpinner", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public UserSpinnerDTO queryStoreSpinner() {
                    log.error(HYSTRIX_PATTERN, "queryStoreSpinner", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public UserSpinnerDTO queryStoreSpinnerByBrandGuid(SingleDataDTO singleDataDTO) {
                    log.error(HYSTRIX_PATTERN, "queryStoreSpinnerByBrandGuid", singleDataDTO, ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public UserSpinnerDTO queryOrgSpinner() {
                    log.error(HYSTRIX_PATTERN, "queryOrgSpinner", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public UserSpinnerDTO queryGenericOrgSpinner() {
                    log.error(HYSTRIX_PATTERN, "queryGenericOrgSpinner", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }

                @Override
                public List<String> queryAllStoreGuid() {
                    log.error(HYSTRIX_PATTERN, "queryAllStoreGuid", "无", ThrowableUtils.asString(cause));
                    throw new ServerException();
                }
            };
        }
    }
}
