package com.holderzone.holder.saas.aggregation.merchant.service.impl;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.dynamic.datasource.starter.utils.EnterpriseIdentifier;
import com.holderzone.holder.saas.aggregation.merchant.service.OpenApiService;
import com.holderzone.holder.saas.aggregation.merchant.util.RedisCacheUtil;
import com.holderzone.saas.store.constant.OpenApiConstant;
import com.holderzone.saas.store.dto.report.openapi.TokenOpenRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;
import java.util.concurrent.TimeUnit;


@Slf4j
@Service
@RequiredArgsConstructor
public class OpenServiceImpl implements OpenApiService {

    private final RedisCacheUtil redisCacheUtil;

    @Override
    public TokenOpenRespDTO getAccessToken(String enterpriseGuid) {
        UserContextUtils.putErp(enterpriseGuid);
        EnterpriseIdentifier.setEnterpriseGuid(enterpriseGuid);
        String cacheKey = String.format(OpenApiConstant.ACCESS_TOKEN_CACHE_KEY, enterpriseGuid);
        String accessToken = redisCacheUtil.getOrSet(cacheKey,
                () -> {
                    String innerAccessToken = UUID.randomUUID().toString().replace("-", "");
                    log.info("openApi:{}, getAccessToken返回:{}", enterpriseGuid, innerAccessToken);
                    return innerAccessToken;
                },
                OpenApiConstant.ACCESS_TOKEN_EXPIRE_TIME,
                TimeUnit.MINUTES);
        return new TokenOpenRespDTO(accessToken);
    }
}
