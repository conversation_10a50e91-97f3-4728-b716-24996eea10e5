package com.holderzone.holder.saas.aggregation.merchant.service.rpc.business;

import com.google.common.collect.Lists;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.saas.store.dto.trade.*;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PaymentTypeClientService
 * @date 2018/09/12 19:24
 * @description
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = PaymentTypeClientService.PayTypeFallBack.class)
public interface PaymentTypeClientService {

    @PostMapping("/pay/type/add")
    String addPaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO);

    @PostMapping("/pay/type/update")
    String updatePaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO);

    @PostMapping("/pay/type/sort")
    String sorting(@RequestBody List<PaymentTypeDTO> paymentTypeDTOS);

    @PostMapping("/pay/type/delete")
    String deletePaymentType(@RequestBody PaymentTypeDTO paymentTypeDTO);

    @PostMapping("/pay/type/getAll")
    List<PaymentTypeDTO> getAll(@RequestBody PaymentTypeQueryDTO paymentTypeQueryDTO);

    @PostMapping("/pay/type/config")
    String config(@RequestBody JHConfigDTO jhConfigDTO);

    @PostMapping("/pay/type/detail")
    PaymentTypeDTO getOne(PaymentTypeDTO paymentTypeDTO);

    @PostMapping("/pay/type/unbind")
    String unbind(@RequestBody JHReqDTO jhReqDTO);

    @PostMapping("/pay/type/get_all_by_store_guid_list")
    List<PaymentTypeBatchDTO> getAllByStoreGuidList(@RequestBody PaymentTypeBatchQureyDTO paymentTypeBatchQureyDTO);

    @PostMapping("/pay/type/init_payment_type/{storeGuid}")
    Boolean initPaymentType(@PathVariable("storeGuid") String storeGuid);

    @PostMapping("/pay/type/get_payment_type_list")
    List<PaymentTypeEnumDTO> getPaymentTypeList();

    @PostMapping("/pay/type/edit_payment_type_mode")
    Boolean editPaymentTypeMode(@RequestBody PaymentTypeModeDTO modeDTO);

    @PostMapping("/pay/type/payType/{storeGuid}")
    List<PaymentTypeDTO> getAllTypeByStoreGuid(@PathVariable("storeGuid") String storeGuid);

    /**
     * 聚合支付账户设置
     *
     * @param request 入参
     * @return boolean
     */
    @ApiOperation(value = "聚合支付账户设置")
    @PostMapping("/pay/type/payment_account_save")
    Boolean paymentAccountSave(@RequestBody PaymentAccountReqDTO request);

    @Component
    class PayTypeFallBack implements FallbackFactory<PaymentTypeClientService> {

        private static final Logger logger = LoggerFactory.getLogger(PayTypeFallBack.class);

        @Override
        public PaymentTypeClientService create(Throwable throwable) {
            return new PaymentTypeClientService() {
                @Override
                public String addPaymentType(PaymentTypeDTO paymentTypeDTO) {
                    logger.error("新增失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e" + throwable.getMessage());
                }

                @Override
                public PaymentTypeDTO getOne(PaymentTypeDTO paymentTypeDTO) {
                    return null;
                }

                @Override
                public String updatePaymentType(PaymentTypeDTO paymentTypeDTO) {
                    logger.error("updatePaymentType失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e" + throwable.getMessage());
                }

                @Override
                public String sorting(List<PaymentTypeDTO> paymentTypeDTOS) {
                    logger.error("sorting失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e" + throwable.getMessage());
                }

                @Override
                public String deletePaymentType(PaymentTypeDTO paymentTypeDTO) {
                    logger.error("deletePaymentType失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e" + throwable.getMessage());
                }

                @Override
                public List<PaymentTypeDTO> getAll(PaymentTypeQueryDTO paymentTypeQueryDTO) {
                    logger.error("getAll失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e" + throwable.getMessage());
                }

                @Override
                public String config(JHConfigDTO jhConfigDTO) {
                    logger.error("config失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e" + throwable.getMessage());
                }

                @Override
                public String unbind(JHReqDTO jhReqDTO) {
                    logger.error("聚合支付解绑失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("聚合支付解绑 异常 e" + throwable.getMessage());
                }

                @Override
                public List<PaymentTypeBatchDTO> getAllByStoreGuidList(PaymentTypeBatchQureyDTO paymentTypeBatchQureyDTO) {
                    logger.error("getAllByStoreGuidList失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e" + throwable.getMessage());
                }

                @Override
                public Boolean initPaymentType(String storeGuid) {
                    logger.error("初始化默认支付方式失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e" + throwable.getMessage());
                }

                @Override
                public List<PaymentTypeEnumDTO> getPaymentTypeList() {
                    logger.error("查询系统默认支付方式失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    return Lists.newArrayList();
                }

                @Override
                public Boolean editPaymentTypeMode(PaymentTypeModeDTO modeDTO) {
                    logger.error("修改食堂卡/会员卡的支付模式失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    throw new BusinessException("fallBack 异常 e" + throwable.getMessage());
                }

                @Override
                public List<PaymentTypeDTO> getAllTypeByStoreGuid(String storeGuid) {
                    logger.error("根据门店guid查询支付方式列表失败，e={}", throwable.getMessage());
                    throwable.printStackTrace();
                    return Lists.newArrayList();
                }

                @Override
                public Boolean paymentAccountSave(PaymentAccountReqDTO request) {
                    logger.error("聚合支付账户设置失败，request={} e={}", JacksonUtils.writeValueAsString(request),
                            throwable.getMessage());
                    throwable.printStackTrace();
                    throw new ServerException();
                }
            };
        }
    }
}
