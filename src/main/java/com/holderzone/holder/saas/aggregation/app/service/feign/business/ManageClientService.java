package com.holderzone.holder.saas.aggregation.app.service.feign.business;

import com.holderzone.framework.exception.unchecked.ServerException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.saas.store.dto.business.manage.ScreenAppRespDTO;
import com.holderzone.saas.store.dto.business.manage.ScreenPicConfigReqDTO;
import com.holderzone.saas.store.dto.business.manage.ScreenPicRespDTO;
import com.holderzone.saas.store.dto.business.manage.ScreenPictureConfigDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className ManageClientService
 * @date 2018/11/19 15:57
 * @description
 * @program holder-saas-aggregation-app
 */
@Component
@FeignClient(name = "holder-saas-store-business", fallbackFactory = ManageClientService.ManageClientServiceFallBack.class)
public interface ManageClientService {

    @PostMapping("/pic/query/android/{storeGuid}")
    List<ScreenAppRespDTO> query(@PathVariable("storeGuid") String storeGuid);

    @ApiOperation(value = "查询门店附屏配置")
    @PostMapping("/pic/query_store_config")
    ScreenPictureConfigDTO queryStoreConfig(@RequestBody ScreenPicConfigReqDTO query);

    @Slf4j
    @Component
    class ManageClientServiceFallBack implements FallbackFactory<ManageClientService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public ManageClientService create(Throwable throwable) {
            return new ManageClientService() {

                @Override
                public List<ScreenAppRespDTO> query(String storeGuid) {
                    log.error(HYSTRIX_PATTERN, "query", storeGuid,
                            ThrowableUtils.asString(throwable));
                    throw new ServerException();
                }

                @Override
                public ScreenPictureConfigDTO queryStoreConfig(ScreenPicConfigReqDTO query) {
                    log.error(HYSTRIX_PATTERN, "queryStoreConfig", JacksonUtils.writeValueAsString(query),
                            throwable.getMessage());
                    throw new ServerException();
                }
            };
        }

    }

}
