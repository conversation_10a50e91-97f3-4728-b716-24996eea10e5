package com.holderzone.holder.saas.aggregation.merchant.service.rpc.member.baseresource;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.dto.baseresource.request.HsmProductReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.request.ProductQueryReqDTO;
import com.holderzone.holder.saas.member.dto.baseresource.response.ProductRespDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className HsmProductService
 * @date 2019/05/31 15:11
 * @description 菜品
 * @program holder-saas-aggregation-merchant
 */
@Component
@FeignClient(name = "holder-saas-member-account", fallbackFactory = HsmProductService.HsmProductServiceFallBack.class)
public interface HsmProductService {


    /**
     * 同步菜品
     *
     * @param productReqDTOList 列表
     */
    @ApiOperation("同步菜品")
    @PostMapping("/hsm/product/syc/list")
    List<HsmProductReqDTO> syc(@RequestBody List<HsmProductReqDTO> productReqDTOList);

    /**
     * 同步菜品
     *
     * @param productReqDTO 列表
     */
    @ApiOperation("同步菜品")
    @PostMapping("/hsm/product/syc")
    HsmProductReqDTO syc(@RequestBody HsmProductReqDTO productReqDTO);

    /**
     * 查询列表数据
     *
     * @param productQueryReqDTO 查询条件
     * @return 列表数据
     */
    @ApiOperation("查询列表数据")
    @PostMapping(value = "/hsm/product/query", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    List<ProductRespDTO> findPageByCondition(
            @RequestBody ProductQueryReqDTO productQueryReqDTO);

    /**
     * 删除菜品
     *
     * @param productKey
     * @param allianceid
     * @return
     */
    @ApiOperation(value = "根据外部关联的菜品ID和联盟ID删除菜品", notes = "根据外部关联的菜品ID和联盟ID删除菜品")
    @DeleteMapping("/{productKey}/{allianceid}")
    boolean removeHsmProduct(@ApiParam("productKey") @PathVariable("productKey") String productKey,
                             @ApiParam("allianceid") @PathVariable("allianceid") String allianceid);


    @ApiOperation("保存菜品")
    @PostMapping("/hsm/product/save")
    boolean save(@RequestBody @Validated HsmProductReqDTO productReqDTO);

    @ApiOperation("修改菜品")
    @PostMapping("/hsm/product/modify")
    boolean modify(@RequestBody @Validated HsmProductReqDTO productReqDTO);


    @Slf4j
    @Component
    class HsmProductServiceFallBack implements FallbackFactory<HsmProductService> {

        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public HsmProductService create(Throwable cause) {
            return new HsmProductService() {
                @Override
                public List<HsmProductReqDTO> syc(List<HsmProductReqDTO> productReqDTOList) {
                    log.error(HYSTRIX_PATTERN, "syc",
                            JSONObject.toJSON(productReqDTOList), ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("同步商品数据失败");
                }

                @Override
                public HsmProductReqDTO syc(HsmProductReqDTO productReqDTO) {
                    log.error(HYSTRIX_PATTERN, "syc",
                            JSONObject.toJSON(productReqDTO), ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("同步商品数据失败");
                }

                @Override
                public List<ProductRespDTO> findPageByCondition(
                        ProductQueryReqDTO productQueryReqDTO) {
                    log.error(HYSTRIX_PATTERN, "findPageByCondition",
                            JSONObject.toJSON(productQueryReqDTO), ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("查询商品数据失败");
                }

                @Override
                public boolean removeHsmProduct(String productKey, String allianceid) {
                    log.error(HYSTRIX_PATTERN, "removeHsmProduct",
                            JSONObject.toJSON(productKey + "," + allianceid), ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("删除商品数据失败");
                }

                @Override
                public boolean save(HsmProductReqDTO productReqDTO) {
                    log.error(HYSTRIX_PATTERN, "save",
                            JSONObject.toJSON(productReqDTO), ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("保存商品数据失败");
                }

                @Override
                public boolean modify(HsmProductReqDTO productReqDTO) {
                    log.error(HYSTRIX_PATTERN, "modify",
                            JSONObject.toJSON(productReqDTO), ThrowableUtils
                                    .asString(cause));
                    throw new BusinessException("修改商品数据失败");
                }
            };
        }
    }

}
