package com.holderzone.holder.saas.aggregation.merchant.service.rpc.report.ele;

import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2018/11/02 下午 14:50
 * @description
 */
@FeignClient(value = "holder-saas-store-report", fallbackFactory = EleService.EleServiceFallback.class)
public interface EleService {

    Logger log = LoggerFactory.getLogger(EleService.class);

    /**
     * 根据shopId查询enterpriseGuid
     *
     * @param shopId
     * @return
     */
    @GetMapping("/ele/getEnterpriseGuidByShopId/{shopId}")
    String getEnterpriseGuidByShopId(@PathVariable("shopId") Long shopId);

    @Component
    class EleServiceFallback implements FallbackFactory<EleService> {

        @Override
        public EleService create(Throwable throwable) {
            return new EleService() {
                @Override
                public String getEnterpriseGuidByShopId(Long shopId) {
                    log.error("根据shopId查询enterpriseGuid异常", throwable.getCause());
                    return null;
                }
            };
        }
    }
}
