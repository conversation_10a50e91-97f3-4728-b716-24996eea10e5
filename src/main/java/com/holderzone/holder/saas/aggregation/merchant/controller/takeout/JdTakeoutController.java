package com.holderzone.holder.saas.aggregation.merchant.controller.takeout;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.holder.saas.aggregation.merchant.service.rpc.takeout.TakeoutProducerService;
import com.holderzone.saas.store.dto.takeaway.jd.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @description 京东秒送回调接口
 */
@Slf4j
@RestController
@RequestMapping("/takeout/callback/jd")
@AllArgsConstructor
public class JdTakeoutController {

    private final TakeoutProducerService takeoutProducerService;

    private final ExecutorService executorTakeoutService;

    @PostMapping(consumes = "application/x-www-form-urlencoded")
    public JdTakeoutResponse<String> callback(@RequestParam("token") String token) {
        log.info("京东秒送回调：{}",token);
        //异步去处理业务
        CompletableFuture.runAsync(() -> takeoutProducerService.callback(JSON.parseObject(token, CallBackDTO.class)),executorTakeoutService);
        return JdTakeoutResponse.buildEmptySuccess();
    }

    /**
     * 创建新订单消息
     * @param orderDTO 新订单
     */
    @PostMapping("/djsw/newOrder")
    public JdTakeoutResponse<String> newOrder(@ModelAttribute BusinessOrderDTO orderDTO,@RequestParam("jd_param_json") String jdParamJson) {
        buildOrder(orderDTO,jdParamJson);
        log.info("京东秒送新订单回调：{}", JacksonUtils.writeValueAsString(orderDTO));
        orderDTO.setStatus(OrderStatusEnum.ACTIVATED);
        CompletableFuture.runAsync(() -> takeoutProducerService.jdOrderCallback(orderDTO),executorTakeoutService);
        return JdTakeoutResponse.buildEmptySuccess();
    }

    private void buildOrder(BusinessOrderDTO orderDTO, String jdParamJson) {
        BusinessOrderDTO param = JSON.parseObject(jdParamJson, BusinessOrderDTO.class);
        orderDTO.setBillId(param.getBillId());
        orderDTO.setStatusId(param.getStatusId());
        orderDTO.setTimestamp(param.getTimestamp());
        orderDTO.setOrderId(param.getOrderId() == null ? param.getBillId() : param.getOrderId());
        orderDTO.setBusinessTag(param.getBusinessTag());
        orderDTO.setRemark(param.getRemark());
    }

    /**
     * 接单
     * @param orderDTO 入参
     */
    @PostMapping("/djsw/orderWaitOutStore")
    public JdTakeoutResponse<String> orderWaitOutStore(@ModelAttribute BusinessOrderDTO orderDTO,@RequestParam("jd_param_json") String jdParamJson) {
        buildOrder(orderDTO,jdParamJson);
        log.info("京东秒送订单等待出库消息回调：{}", JacksonUtils.writeValueAsString(orderDTO));
        orderDTO.setStatus(OrderStatusEnum.CONFIRMED);
        CompletableFuture.runAsync(() -> takeoutProducerService.jdOrderCallback(orderDTO),executorTakeoutService);
        return JdTakeoutResponse.buildEmptySuccess();
    }

    /**
     * 开始配送
     * @param orderDTO 入参
     */
    @PostMapping("/djsw/deliveryOrder")
    public JdTakeoutResponse<String> deliveryOrder(@ModelAttribute BusinessOrderDTO orderDTO,@RequestParam("jd_param_json") String jdParamJson) {
        buildOrder(orderDTO,jdParamJson);
        log.info("京东秒送订单开始配送消息回调：{}", JacksonUtils.writeValueAsString(orderDTO));
        orderDTO.setStatus(OrderStatusEnum.SHIPPING);
        CompletableFuture.runAsync(() -> takeoutProducerService.jdOrderCallback(orderDTO),executorTakeoutService);
        return JdTakeoutResponse.buildEmptySuccess();
    }

    /**
     * 配送完成
     * @param orderDTO 入参
     */
    @PostMapping("/djsw/finishOrder")
    public JdTakeoutResponse<String> finishOrder(@ModelAttribute BusinessOrderDTO orderDTO,@RequestParam("jd_param_json") String jdParamJson) {
        buildOrder(orderDTO,jdParamJson);
        log.info("京东秒送订单妥投消息回调：{}", JacksonUtils.writeValueAsString(orderDTO));
        orderDTO.setStatus(OrderStatusEnum.FINISHED);
        CompletableFuture.runAsync(() -> takeoutProducerService.jdOrderCallback(orderDTO),executorTakeoutService);
        return JdTakeoutResponse.buildEmptySuccess();
    }

    /**
     * 订单完成
     * @param orderDTO 入参
     */
    @PostMapping("/djsw/orderFinish")
    public JdTakeoutResponse<String> orderFinish(@ModelAttribute BusinessOrderDTO orderDTO,@RequestParam("jd_param_json") String jdParamJson) {
        buildOrder(orderDTO,jdParamJson);
        log.info("京东秒送订单完成消息回调：{}", JacksonUtils.writeValueAsString(orderDTO));
        orderDTO.setStatus(OrderStatusEnum.SHIP_SUCCEED);
        CompletableFuture.runAsync(() -> takeoutProducerService.jdOrderCallback(orderDTO),executorTakeoutService);
        return JdTakeoutResponse.buildEmptySuccess();
    }

    /**
     * 用户取消 目前所有取消都选择直接取消方式
     * @param orderDTO 入参
     */
    @PostMapping("/djsw/userCancelOrder")
    public JdTakeoutResponse<String> userCancelOrder(@ModelAttribute BusinessOrderDTO orderDTO,@RequestParam("jd_param_json") String jdParamJson) {
        buildOrder(orderDTO,jdParamJson);
        log.info("京东秒送订单取消消息回调：{}", JacksonUtils.writeValueAsString(orderDTO));
        orderDTO.setStatus(OrderStatusEnum.CANCELED);
        CompletableFuture.runAsync(() -> takeoutProducerService.jdOrderCallback(orderDTO),executorTakeoutService);
        return JdTakeoutResponse.buildEmptySuccess();
    }

    /**
     * 用户申请取消
     * @param orderDTO 入参
     */
    @PostMapping("/djsw/applyCancelOrder")
    public JdTakeoutResponse<String> applyCancelOrder(@ModelAttribute BusinessOrderDTO orderDTO,@RequestParam("jd_param_json") String jdParamJson) {
        log.info("京东秒送用户申请取消消息回调：{}", JacksonUtils.writeValueAsString(orderDTO));
        buildOrder(orderDTO,jdParamJson);
        orderDTO.setStatus(OrderStatusEnum.CANCEL_REQ);
        CompletableFuture.runAsync(() -> takeoutProducerService.jdOrderCallback(orderDTO),executorTakeoutService);
        return JdTakeoutResponse.buildEmptySuccess();
    }

    /**
     * 用户申请取消后商家操作
     * @param orderDTO 入参
     */
    @PostMapping("/djsw/venderAuditApplyCancelOrder")
    public JdTakeoutResponse<String> venderAuditApplyCancelOrder(@ModelAttribute BusinessOrderDTO orderDTO,@RequestParam("jd_param_json") String jdParamJson) {
        log.info("京东秒送商家审核用户取消申请消息回调：{}", JacksonUtils.writeValueAsString(orderDTO));
        buildOrder(orderDTO,jdParamJson);
        orderDTO.initStatus();
        CompletableFuture.runAsync(() -> takeoutProducerService.jdOrderCallback(orderDTO),executorTakeoutService);
        return JdTakeoutResponse.buildEmptySuccess();
    }

    /**
     * 创建售后单申请消息
     * @param orderDTO 入参
     */
    @PostMapping("/djsw/newApplyAfterSaleBill")
    public JdTakeoutResponse<String> newApplyAfterSaleBill(@ModelAttribute BusinessOrderDTO orderDTO,@RequestParam("jd_param_json") String jdParamJson) {
        log.info("创建售后单申请消息回调：{}", JacksonUtils.writeValueAsString(orderDTO));
        buildOrder(orderDTO,jdParamJson);
        orderDTO.initStatus();
        CompletableFuture.runAsync(() -> takeoutProducerService.jdOrderCallback(orderDTO),executorTakeoutService);
        return JdTakeoutResponse.buildEmptySuccess();
    }

    /**
     * 用户申请取消后商家操作
     * @param orderDTO 入参
     */
    @PostMapping("/djsw/afterSaleBillStatus")
    public JdTakeoutResponse<String> afterSaleBillStatus(@ModelAttribute BusinessOrderDTO orderDTO,@RequestParam("jd_param_json") String jdParamJson) {
        log.info("京东秒送售后单状态消息回调：{}", JacksonUtils.writeValueAsString(orderDTO));
        buildOrder(orderDTO,jdParamJson);
        orderDTO.initStatus();
        CompletableFuture.runAsync(() -> takeoutProducerService.jdOrderCallback(orderDTO),executorTakeoutService);
        return JdTakeoutResponse.buildEmptySuccess();
    }
}
