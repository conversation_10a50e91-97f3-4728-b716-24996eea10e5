package com.holderzone.holder.saas.aggregation.app.service;

import com.holderzone.holder.saas.aggregation.app.entity.auth.*;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseConsumpStatis;
import com.holderzone.holder.saas.member.terminal.dto.statistics.ResponseRechargeStatis;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;
import com.holderzone.saas.store.dto.user.resp.UserBriefDTO;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className BusinessDailyService
 * @date 2019/02/15 17:45
 * @description
 * @program holder-saas-aggregation-app
 */
public interface BusinessDailyService {

    /**
     * 用餐类型统计
     */
    List<DiningTypeRespDTO> diningType(DailyReqDTO request);

    /**
     * 用餐类型统计
     * 受权限控制
     */
    List<DiningTypeSaleDTO> diningTypeSale(DailyReqDTO request);

    /**
     * 分类销售统计
     */
    List<ItemRespDTO> classify(DailyReqDTO request);

    /**
     * 分类销售统计
     */
    List<CategorySaleDTO> classifySale(DailyReqDTO request);

    /**
     * 商品销售统计
     */
    List<ItemRespDTO> goods(DailyReqDTO request);

    /**
     * 商品销售统计
     * 受权限控制
     */
    List<GoodsSaleDTO> goodsSale(DailyReqDTO request);

    /**
     * 属性销售统计
     */
    PropStatsDTO attr(DailyReqDTO request);

    /**
     * 属性销售统计
     */
    PropStatsSaleDTO attrSale(DailyReqDTO request);

    /**
     * 退菜统计
     */
    List<ItemRespDTO> returnVegetables(DailyReqDTO request);

    /**
     * 退菜统计
     */
    List<ReturnSaleDTO> returnVegetablesSale(DailyReqDTO request);

    /**
     * 赠菜统计
     */
    List<ItemRespDTO> dishGiving(DailyReqDTO request);

    /**
     * 赠菜统计
     */
    List<GiftSaleDTO> dishGivingSale(DailyReqDTO request);

    /**
     * 会员消费统计
     *
     * @param request
     * @return
     */
    MemberConsumeRespDTO memberConsume(DailyReqDTO request);

    /**
     * 收款统计
     */
    List<GatherRespDTO> gather(DailyReqDTO request);

    /**
     * 收款统计
     * 受权限控制
     */
    List<GatherSaleDTO> gatherSale(DailyReqDTO request);

    /**
     * 营业概况
     */
    OverviewRespDTO overview(DailyReqDTO request);

    /**
     * 营业概况
     * 受权限控制
     */
    OverviewSaleDTO overviewSale(DailyReqDTO request);

    /**
     * 营业日报会员消费统计
     */
    ResponseConsumpStatis memberConsumeDaily(DailyReqDTO reqDTO);

    /**
     * 营业日报会员消费统计
     * 受权限控制
     */
    MemberConsumeSaleDTO memberConsumeSale(DailyReqDTO reqDTO);

    /**
     * 营业日报会员充值统计
     */
    ResponseRechargeStatis memberRechargeDaily(DailyReqDTO reqDTO);

    /**
     * 营业日报会员充值统计
     * 受权限控制
     */
    MemberRechargeSaleDTO memberRechargeSale(DailyReqDTO reqDTO);

    /**
     * 营业报表查询当前有权限的员工信息
     *
     * @return 员工信息集合
     */
    List<UserBriefDTO> users();

    /**
     * 退款统计报表
     */
    RefundRespDTO refund(@Valid DailyReqDTO request);

    /**
     * 退款统计报表
     * 受权限控制
     */
    RefundSaleDTO refundSale(@Valid DailyReqDTO request);
}
