package com.holderzone.holder.saas.aggregation.app.service.feign.trade;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.saas.store.dto.order.request.daily.DailyReqDTO;
import com.holderzone.saas.store.dto.order.response.daily.*;
import feign.hystrix.FallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @className SnackOrderDailyService
 * @date 2019/02/21 14:49
 * @description 快餐（trading-center项目）营业日报接口
 * @program holder-saas-store-trade
 */
@Component
@FeignClient(name = "holder-saas-store-trading-center", fallbackFactory = SnackTradingCenterDailyClientService.SnackOrderFallBack.class)
public interface SnackTradingCenterDailyClientService {
    @PostMapping("/fast_food/daily/index/report")
    OverviewRespDTO overview(DailyReqDTO request);

    @PostMapping("/fast_food/daily/incoming/report")
    List<GatherRespDTO> gather(DailyReqDTO request);

    @PostMapping("/fast_food/daily/member/report")
    MemberConsumeRespDTO memberConsume(DailyReqDTO request);


    @Component
    class SnackOrderFallBack implements FallbackFactory<SnackTradingCenterDailyClientService> {
        private static final Logger logger = LoggerFactory.getLogger(SnackTradingCenterDailyClientService.SnackOrderFallBack.class);

        @Override
        public SnackTradingCenterDailyClientService create(Throwable throwable) {
            return new SnackTradingCenterDailyClientService() {

                @Override
                public OverviewRespDTO overview(DailyReqDTO request) {
                    logger.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public List<GatherRespDTO> gather(DailyReqDTO request) {
                    logger.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }

                @Override
                public MemberConsumeRespDTO memberConsume(DailyReqDTO request) {
                    logger.error("失败，throwable={}", throwable.getMessage());
                    throw new BusinessException("失败!!" + throwable.getMessage());
                }
            };
        }
    }
}
