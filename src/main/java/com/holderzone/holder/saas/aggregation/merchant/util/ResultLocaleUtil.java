package com.holderzone.holder.saas.aggregation.merchant.util;

import com.holderzone.holder.saas.aggregation.merchant.entity.enums.CommonLocaleEnum;
import com.holderzone.saas.store.enums.locale.LocaleMessageEnum;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * <AUTHOR>
 * @create 2023-07-19
 * @description
 */
public class ResultLocaleUtil {

    private ResultLocaleUtil() {
        throw new UnsupportedOperationException();
    }

    public static String replacePrefixLocale(String resultString,String prefix){
        //若本地是简体中文直接返回
        if(LocaleContextHolder.getLocale() == Locale.SIMPLIFIED_CHINESE){
            return resultString;
        }
        String suffix = resultString.replace(prefix, "");

        return CommonLocaleEnum.getLocale(prefix) + LocaleMessageEnum.getLocale(suffix);

    }

}
